<!DOCTYPE html>
<html>
<head>
    <title>SVG 处理测试</title>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-item {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .test-item h3 {
            margin-top: 0;
            color: #333;
        }
        .svg-preview {
            border: 1px solid #ccc;
            padding: 10px;
            background: white;
            text-align: center;
            margin: 10px 0;
            min-height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .svg-preview svg, .svg-preview img {
            max-width: 100%;
            max-height: 150px;
        }
        .info-box {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .log {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <h1>SVG 处理测试</h1>
    
    <div class="container">
        <h2>SVG 处理问题诊断</h2>
        <p>这个页面专门用于测试和诊断 SVG 文件的处理问题：</p>
        <ul>
            <li><strong>MIME 类型检查</strong>: 验证 SVG 的 MIME 类型是否正确</li>
            <li><strong>内容验证</strong>: 检查 SVG 内容格式</li>
            <li><strong>Blob URL 测试</strong>: 测试 SVG Blob URL 的有效性</li>
            <li><strong>渲染测试</strong>: 验证 SVG 是否能正确显示</li>
        </ul>
    </div>

    <div class="container">
        <h2>测试用例</h2>
        <div class="test-grid">
            <div class="test-item">
                <h3>简单 SVG 测试</h3>
                <button class="button" onclick="testSimpleSvg()">测试简单 SVG</button>
                <div class="svg-preview" id="simpleSvgPreview"></div>
                <div class="info-box" id="simpleSvgInfo"></div>
            </div>

            <div class="test-item">
                <h3>复杂 SVG 测试</h3>
                <button class="button" onclick="testComplexSvg()">测试复杂 SVG</button>
                <div class="svg-preview" id="complexSvgPreview"></div>
                <div class="info-box" id="complexSvgInfo"></div>
            </div>

            <div class="test-item">
                <h3>错误 MIME 类型测试</h3>
                <button class="button" onclick="testWrongMimeSvg()">测试错误 MIME</button>
                <div class="svg-preview" id="wrongMimeSvgPreview"></div>
                <div class="info-box" id="wrongMimeSvgInfo"></div>
            </div>

            <div class="test-item">
                <h3>模拟媒体资源 SVG</h3>
                <button class="button" onclick="testMediaResourceSvg()">测试媒体资源</button>
                <div class="svg-preview" id="mediaResourceSvgPreview"></div>
                <div class="info-box" id="mediaResourceSvgInfo"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>批量测试</h2>
        <button class="button" onclick="runAllTests()">运行所有测试</button>
        <button class="button" onclick="clearResults()">清空结果</button>
        <div id="batchStatus" class="status" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>日志输出</h2>
        <div id="logOutput" class="log"></div>
    </div>

    <script>
        // 测试用的 SVG 内容
        const simpleSvgContent = `<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
            <circle cx="50" cy="50" r="40" stroke="black" stroke-width="3" fill="red" />
        </svg>`;

        const complexSvgContent = `<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:rgb(255,255,0);stop-opacity:1" />
                    <stop offset="100%" style="stop-color:rgb(255,0,0);stop-opacity:1" />
                </linearGradient>
            </defs>
            <ellipse cx="100" cy="70" rx="85" ry="55" fill="url(#grad1)" />
            <text x="100" y="125" font-family="Verdana" font-size="60" text-anchor="middle" fill="blue">SVG</text>
        </svg>`;

        // 日志函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logOutput.textContent += logMessage;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(logMessage);
        }

        // 显示状态
        function showStatus(message, type = 'info') {
            const element = document.getElementById('batchStatus');
            element.textContent = message;
            element.className = `status ${type}`;
            element.style.display = 'block';
        }

        // 创建 SVG Blob URL
        function createSvgBlobUrl(svgContent, mimeType = 'image/svg+xml') {
            const blob = new Blob([svgContent], { type: mimeType });
            const url = URL.createObjectURL(blob);
            log(`创建 SVG Blob URL: ${url}, MIME: ${mimeType}, 大小: ${blob.size} bytes`);
            return { blob, url };
        }

        // 验证 SVG Blob
        async function validateSvgBlob(blob, url) {
            const info = {
                size: blob.size,
                type: blob.type,
                url: url,
                content: null,
                isValid: false,
                errors: []
            };

            try {
                // 读取内容
                info.content = await blob.text();
                
                // 检查 MIME 类型
                if (blob.type !== 'image/svg+xml') {
                    info.errors.push(`MIME 类型错误: ${blob.type}, 应该是 image/svg+xml`);
                }
                
                // 检查内容格式
                const trimmedContent = info.content.trim();
                if (!trimmedContent.startsWith('<svg') && !trimmedContent.startsWith('<?xml')) {
                    info.errors.push('SVG 内容格式错误: 不以 <svg 或 <?xml 开头');
                }
                
                // 检查是否包含基本 SVG 元素
                if (!trimmedContent.includes('<svg')) {
                    info.errors.push('SVG 内容错误: 缺少 <svg 标签');
                }
                
                info.isValid = info.errors.length === 0;
                
            } catch (error) {
                info.errors.push(`读取内容失败: ${error.message}`);
            }

            return info;
        }

        // 显示测试结果
        function displayTestResult(previewId, infoId, blob, url, info) {
            const preview = document.getElementById(previewId);
            const infoDiv = document.getElementById(infoId);
            
            // 显示预览
            preview.innerHTML = '';
            const img = document.createElement('img');
            img.src = url;
            img.onerror = function() {
                preview.innerHTML = '<span style="color: red;">SVG 渲染失败</span>';
            };
            img.onload = function() {
                log(`SVG 渲染成功: ${previewId}`);
            };
            preview.appendChild(img);
            
            // 显示信息
            infoDiv.innerHTML = `
                <strong>大小:</strong> ${info.size} bytes<br>
                <strong>MIME:</strong> ${info.type}<br>
                <strong>URL:</strong> ${url.substring(0, 50)}...<br>
                <strong>有效:</strong> ${info.isValid ? '✓' : '✗'}<br>
                ${info.errors.length > 0 ? '<strong>错误:</strong><br>' + info.errors.map(e => `• ${e}`).join('<br>') : ''}
            `;
        }

        // 测试简单 SVG
        async function testSimpleSvg() {
            log('开始测试简单 SVG...');
            const { blob, url } = createSvgBlobUrl(simpleSvgContent);
            const info = await validateSvgBlob(blob, url);
            displayTestResult('simpleSvgPreview', 'simpleSvgInfo', blob, url, info);
            log(`简单 SVG 测试完成: ${info.isValid ? '成功' : '失败'}`);
        }

        // 测试复杂 SVG
        async function testComplexSvg() {
            log('开始测试复杂 SVG...');
            const { blob, url } = createSvgBlobUrl(complexSvgContent);
            const info = await validateSvgBlob(blob, url);
            displayTestResult('complexSvgPreview', 'complexSvgInfo', blob, url, info);
            log(`复杂 SVG 测试完成: ${info.isValid ? '成功' : '失败'}`);
        }

        // 测试错误 MIME 类型的 SVG
        async function testWrongMimeSvg() {
            log('开始测试错误 MIME 类型的 SVG...');
            const { blob, url } = createSvgBlobUrl(simpleSvgContent, 'text/plain'); // 故意使用错误的 MIME 类型
            const info = await validateSvgBlob(blob, url);
            displayTestResult('wrongMimeSvgPreview', 'wrongMimeSvgInfo', blob, url, info);
            log(`错误 MIME SVG 测试完成: ${info.isValid ? '成功' : '失败'}`);
        }

        // 模拟媒体资源 SVG 测试
        async function testMediaResourceSvg() {
            log('开始测试媒体资源 SVG...');
            
            // 模拟媒体资源存储
            if (!window.mediaResources) window.mediaResources = new Map();
            if (!window.mediaBlobs) window.mediaBlobs = new Map();
            
            const { blob, url } = createSvgBlobUrl(complexSvgContent);
            const mediaPath = 'media/display2image1.svg';
            
            // 存储到模拟的媒体资源中
            window.mediaResources.set(mediaPath, url);
            window.mediaBlobs.set(mediaPath, blob);
            
            log(`模拟存储媒体资源: ${mediaPath} -> ${url}`);
            
            // 模拟 getImageURL 调用
            const requestPath = 'display2image1.svg';
            log(`模拟 getImageURL 请求: ${requestPath}`);
            
            // 查找匹配
            let foundUrl = null;
            for (let [storedPath, storedUrl] of window.mediaResources) {
                if (storedPath.includes(requestPath) || requestPath.includes(storedPath.replace('media/', ''))) {
                    foundUrl = storedUrl;
                    log(`找到匹配: ${requestPath} -> ${storedPath} -> ${storedUrl}`);
                    break;
                }
            }
            
            if (foundUrl) {
                const info = await validateSvgBlob(blob, foundUrl);
                displayTestResult('mediaResourceSvgPreview', 'mediaResourceSvgInfo', blob, foundUrl, info);
                log(`媒体资源 SVG 测试完成: ${info.isValid ? '成功' : '失败'}`);
            } else {
                log('媒体资源 SVG 测试失败: 未找到匹配', 'error');
            }
        }

        // 运行所有测试
        async function runAllTests() {
            showStatus('正在运行所有 SVG 测试...', 'info');
            log('开始批量 SVG 测试...');
            
            try {
                await testSimpleSvg();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testComplexSvg();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testWrongMimeSvg();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testMediaResourceSvg();
                
                showStatus('所有 SVG 测试完成！', 'success');
                log('批量 SVG 测试完成');
                
            } catch (error) {
                showStatus(`测试失败: ${error.message}`, 'error');
                log(`批量测试失败: ${error.message}`, 'error');
            }
        }

        // 清空结果
        function clearResults() {
            const previews = document.querySelectorAll('.svg-preview');
            const infos = document.querySelectorAll('.info-box');
            
            previews.forEach(preview => preview.innerHTML = '');
            infos.forEach(info => info.innerHTML = '');
            
            document.getElementById('logOutput').textContent = '';
            document.getElementById('batchStatus').style.display = 'none';
            
            log('已清空所有测试结果');
        }

        // 页面加载完成
        window.addEventListener('load', function() {
            log('SVG 处理测试页面加载完成');
            log('可以开始测试各种 SVG 处理场景');
        });
    </script>
</body>
</html>
