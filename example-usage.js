/**
 * OnlyOffice Editor with X2T Conversion - 使用示例
 * 
 * 这个文件展示了如何使用集成的 X2T 转换和媒体资源处理功能
 */

// 1. 初始化 X2T 转换器
async function initializeX2T() {
    try {
        console.log('正在初始化 X2T 转换器...');
        await window.initX2T();
        console.log('X2T 转换器初始化成功');
        return true;
    } catch (error) {
        console.error('X2T 初始化失败:', error);
        return false;
    }
}

// 2. 转换文档并提取媒体资源
async function convertDocumentWithMedia(file) {
    try {
        console.log(`开始转换文档: ${file.name}`);
        
        // 执行转换
        const result = await window.convertDocument(file);
        
        console.log('转换结果:', {
            fileName: result.fileName,
            type: result.type,
            binSize: result.bin.length,
            mediaCount: Object.keys(result.media).length
        });
        
        // 存储媒体资源到全局变量 (这在实际的 index.html 中会自动完成)
        if (!window.mediaResources) {
            window.mediaResources = new Map();
        }
        
        Object.keys(result.media).forEach(mediaPath => {
            window.mediaResources.set(mediaPath, result.media[mediaPath]);
            console.log(`存储媒体资源: ${mediaPath}`);
        });
        
        return result;
        
    } catch (error) {
        console.error('文档转换失败:', error);
        throw error;
    }
}

// 3. 获取媒体资源 URL (模拟 OnlyOffice 的 getImageURL)
function getImageURL(imagePath, callback) {
    console.log(`getImageURL 请求: ${imagePath}`);
    
    return new Promise((resolve, reject) => {
        // 检查媒体资源中是否有对应的图片
        if (window.mediaResources && window.mediaResources.has(imagePath)) {
            const imageUrl = window.mediaResources.get(imagePath);
            console.log(`找到媒体资源: ${imagePath} -> ${imageUrl}`);
            resolve(imageUrl);
            if (callback) callback(imageUrl);
        } else {
            // 尝试匹配部分路径
            let foundUrl = null;
            if (window.mediaResources) {
                for (let [storedPath, url] of window.mediaResources) {
                    if (storedPath.includes(imagePath) || imagePath.includes(storedPath.replace('media/', ''))) {
                        foundUrl = url;
                        console.log(`部分匹配媒体资源: ${imagePath} -> ${storedPath} -> ${url}`);
                        break;
                    }
                }
            }
            
            if (foundUrl) {
                resolve(foundUrl);
                if (callback) callback(foundUrl);
            } else {
                console.warn(`未找到媒体资源: ${imagePath}`);
                if (window.mediaResources) {
                    console.log('可用的媒体资源:', Array.from(window.mediaResources.keys()));
                }
                
                // 返回原始路径作为后备
                resolve(imagePath);
                if (callback) callback(imagePath);
            }
        }
    });
}

// 4. 完整的使用示例
async function completeExample() {
    // 步骤 1: 初始化
    const initialized = await initializeX2T();
    if (!initialized) {
        console.error('初始化失败，无法继续');
        return;
    }
    
    // 步骤 2: 模拟文件选择 (在实际使用中，这会是用户选择的文件)
    // const fileInput = document.getElementById('fileInput');
    // const file = fileInput.files[0];
    
    // 这里我们假设有一个文件
    console.log('等待用户选择文件...');
    console.log('请在页面中选择一个包含图片的 .docx 文件');
    
    // 步骤 3: 转换文档 (这个函数会在用户选择文件后调用)
    window.handleFileConversion = async function(file) {
        try {
            const result = await convertDocumentWithMedia(file);
            
            // 步骤 4: 测试媒体资源获取
            const mediaKeys = Object.keys(result.media);
            if (mediaKeys.length > 0) {
                console.log('测试媒体资源获取...');
                
                for (let mediaPath of mediaKeys) {
                    try {
                        const url = await getImageURL(mediaPath);
                        console.log(`✓ 成功获取: ${mediaPath} -> ${url}`);
                    } catch (error) {
                        console.error(`✗ 获取失败: ${mediaPath} -> ${error.message}`);
                    }
                }
                
                // 测试部分路径匹配
                const firstMedia = mediaKeys[0];
                const partialPath = firstMedia.replace('media/', '');
                try {
                    const url = await getImageURL(partialPath);
                    console.log(`✓ 部分路径匹配成功: ${partialPath} -> ${url}`);
                } catch (error) {
                    console.error(`✗ 部分路径匹配失败: ${partialPath} -> ${error.message}`);
                }
            } else {
                console.log('文档中没有媒体文件');
            }
            
        } catch (error) {
            console.error('处理失败:', error);
        }
    };
}

// 5. OnlyOffice 编辑器集成示例
function setupOnlyOfficeIntegration() {
    // 这是在 index.html 中实际使用的配置
    const docEditorConfig = {
        // ... 其他配置
        events: {
            onAppReady: async () => {
                // 初始化全局媒体资源存储
                window.mediaResources = new Map();
                
                // 初始化 X2T
                await initializeX2T();
                
                // 设置 XMLHttpRequest 拦截 (已在 index.html 中实现)
                // ...
            }
        }
    };
    
    // connectMockServer 配置
    const mockServerConfig = {
        getParticipants: () => ({}),
        onMessage: (message) => {
            console.log('收到消息:', message);
        },
        getImageURL: (imagePath, callback) => {
            // 返回 Promise 并支持回调
            return getImageURL(imagePath, callback);
        }
    };
    
    return { docEditorConfig, mockServerConfig };
}

// 6. 工具函数
const utils = {
    // 检查是否为图片文件
    isImageFile: (path) => {
        const imageExtensions = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg'];
        const extension = path.split('.').pop().toLowerCase();
        return imageExtensions.includes(extension);
    },
    
    // 获取文件扩展名
    getFileExtension: (path) => {
        return path.split('.').pop().toLowerCase();
    },
    
    // 清理媒体资源缓存
    clearMediaCache: () => {
        if (window.mediaResources) {
            // 释放 Blob URLs
            for (let url of window.mediaResources.values()) {
                if (url.startsWith('blob:')) {
                    URL.revokeObjectURL(url);
                }
            }
            window.mediaResources.clear();
            console.log('媒体资源缓存已清理');
        }
    },
    
    // 获取媒体资源统计信息
    getMediaStats: () => {
        if (!window.mediaResources) return null;
        
        const stats = {
            total: window.mediaResources.size,
            images: 0,
            others: 0,
            paths: Array.from(window.mediaResources.keys())
        };
        
        for (let path of stats.paths) {
            if (utils.isImageFile(path)) {
                stats.images++;
            } else {
                stats.others++;
            }
        }
        
        return stats;
    }
};

// 导出函数供全局使用
window.X2TExample = {
    initializeX2T,
    convertDocumentWithMedia,
    getImageURL,
    completeExample,
    setupOnlyOfficeIntegration,
    utils
};

// 自动运行示例
if (typeof window !== 'undefined') {
    console.log('X2T 使用示例已加载');
    console.log('可用函数:', Object.keys(window.X2TExample));
    console.log('运行 window.X2TExample.completeExample() 开始完整示例');
}
