<!DOCTYPE html>
<html>
<head>
    <title>getImageURL 调试面板测试</title>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-item {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .test-item h3 {
            margin-top: 0;
            color: #333;
        }
        .test-item .url-input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .test-item .result {
            margin-top: 10px;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border: 1px solid #eee;
        }
        .test-item .result img {
            max-width: 100%;
            max-height: 150px;
            object-fit: contain;
            display: block;
            margin: 10px auto;
        }
        .test-item .result .url-display {
            font-family: monospace;
            font-size: 11px;
            color: #666;
            word-break: break-all;
            background: #f5f5f5;
            padding: 5px;
            border-radius: 2px;
            margin: 5px 0;
        }
        .log {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* 模拟调试面板样式 */
        .debug-panel-demo {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 350px;
            max-height: 80vh;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            border-radius: 8px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            overflow-y: auto;
        }
        .debug-panel-demo .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #333;
        }
        .debug-panel-demo .image-item {
            margin-bottom: 10px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            border-left: 3px solid #007acc;
        }
        .debug-panel-demo .image-item .path {
            color: #ffd700;
            word-break: break-all;
            margin-bottom: 5px;
        }
        .debug-panel-demo .image-item .url {
            color: #90ee90;
            word-break: break-all;
            font-size: 10px;
            margin-bottom: 5px;
        }
        .debug-panel-demo .image-item .preview {
            text-align: center;
            margin-top: 5px;
        }
        .debug-panel-demo .image-item .preview img {
            max-width: 100%;
            max-height: 60px;
            object-fit: contain;
            border: 1px solid #333;
            border-radius: 2px;
        }
        .debug-panel-demo .image-item .info {
            color: #ccc;
            font-size: 10px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>getImageURL 调试面板测试</h1>
    
    <div class="container">
        <h2>功能说明</h2>
        <p>这个页面演示了 getImageURL 调试面板的功能，可以帮助您分析图片请求的处理过程：</p>
        <ul>
            <li><strong>实时监控</strong>: 显示所有 getImageURL 请求</li>
            <li><strong>图片预览</strong>: 直接显示图片内容</li>
            <li><strong>URL 分析</strong>: 显示原始路径和解析后的 URL</li>
            <li><strong>来源追踪</strong>: 标识图片来源（Data URL、媒体资源、缓存等）</li>
            <li><strong>时间戳</strong>: 记录请求时间</li>
        </ul>
    </div>

    <div class="container">
        <h2>测试不同类型的图片 URL</h2>
        <div class="test-grid">
            <div class="test-item">
                <h3>Data URL 测试</h3>
                <textarea class="url-input" id="dataUrlInput" placeholder="输入 Data URL...">data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==</textarea>
                <button class="button" onclick="testUrl('dataUrlInput', 'dataUrlResult')">测试 Data URL</button>
                <div class="result" id="dataUrlResult"></div>
            </div>

            <div class="test-item">
                <h3>HTTP URL 测试</h3>
                <textarea class="url-input" id="httpUrlInput" placeholder="输入 HTTP URL...">https://via.placeholder.com/150/0000FF/808080?text=Test</textarea>
                <button class="button" onclick="testUrl('httpUrlInput', 'httpUrlResult')">测试 HTTP URL</button>
                <div class="result" id="httpUrlResult"></div>
            </div>

            <div class="test-item">
                <h3>相对路径测试</h3>
                <textarea class="url-input" id="relativeUrlInput" placeholder="输入相对路径...">media/image1.png</textarea>
                <button class="button" onclick="testUrl('relativeUrlInput', 'relativeUrlResult')">测试相对路径</button>
                <div class="result" id="relativeUrlResult"></div>
            </div>

            <div class="test-item">
                <h3>自定义测试</h3>
                <textarea class="url-input" id="customUrlInput" placeholder="输入任意 URL..."></textarea>
                <button class="button" onclick="testUrl('customUrlInput', 'customUrlResult')">测试自定义 URL</button>
                <div class="result" id="customUrlResult"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>批量测试</h2>
        <button class="button" onclick="runBatchTest()">运行批量测试</button>
        <button class="button" onclick="clearResults()">清空结果</button>
        <div id="batchStatus" class="status info" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>调试日志</h2>
        <div id="logOutput" class="log"></div>
    </div>

    <!-- 模拟调试面板 -->
    <div class="debug-panel-demo">
        <div class="header">
            <span>图片请求调试 (模拟)</span>
        </div>
        <div id="debugContent"></div>
    </div>

    <script>
        // 模拟调试数据
        let mockImageRequestLog = [];

        // 日志函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logOutput.textContent += logMessage;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(logMessage);
        }

        // 显示状态
        function showStatus(message, type = 'info') {
            const element = document.getElementById('batchStatus');
            element.textContent = message;
            element.className = `status ${type}`;
            element.style.display = 'block';
        }

        // 获取 URL 类型
        function getUrlType(url) {
            if (!url) return 'empty';
            if (url.startsWith('data:')) return 'Data URL';
            if (url.startsWith('blob:')) return 'Blob URL';
            if (url.startsWith('http://') || url.startsWith('https://')) return 'HTTP URL';
            if (url.startsWith('/')) return 'Absolute Path';
            return 'Relative Path';
        }

        // 模拟 getImageURL 函数
        function mockGetImageURL(imagePath, callback) {
            log(`模拟 getImageURL 调用: ${imagePath.substring(0, 50)}...`);
            
            const requestInfo = {
                timestamp: new Date().toLocaleTimeString(),
                path: imagePath,
                type: getUrlType(imagePath),
                resolvedUrl: null,
                source: 'unknown'
            };

            return new Promise((resolve, reject) => {
                // 模拟处理延迟
                setTimeout(() => {
                    if (imagePath.startsWith('data:')) {
                        // Data URL 处理
                        requestInfo.source = 'Data URL';
                        fetch(imagePath)
                            .then(res => res.blob())
                            .then(blob => {
                                const blobUrl = URL.createObjectURL(blob);
                                requestInfo.resolvedUrl = blobUrl;
                                addToMockDebugLog(requestInfo);
                                resolve(blobUrl);
                                if (callback) callback(blobUrl);
                            })
                            .catch(error => {
                                requestInfo.resolvedUrl = imagePath;
                                requestInfo.source = 'Data URL (转换失败)';
                                addToMockDebugLog(requestInfo);
                                resolve(imagePath);
                                if (callback) callback(imagePath);
                            });
                    } else if (imagePath.startsWith('http') || imagePath.startsWith('blob:')) {
                        // 完整 URL
                        requestInfo.resolvedUrl = imagePath;
                        requestInfo.source = '完整 URL';
                        addToMockDebugLog(requestInfo);
                        resolve(imagePath);
                        if (callback) callback(imagePath);
                    } else {
                        // 相对路径或其他
                        requestInfo.resolvedUrl = imagePath;
                        requestInfo.source = '后备路径';
                        addToMockDebugLog(requestInfo);
                        resolve(imagePath);
                        if (callback) callback(imagePath);
                    }
                }, 100);
            });
        }

        // 添加到模拟调试日志
        function addToMockDebugLog(requestInfo) {
            mockImageRequestLog.push(requestInfo);
            if (mockImageRequestLog.length > 20) {
                mockImageRequestLog.shift();
            }
            updateMockDebugPanel();
        }

        // 更新模拟调试面板
        function updateMockDebugPanel() {
            const debugContent = document.getElementById('debugContent');
            debugContent.innerHTML = '';
            
            mockImageRequestLog.slice().reverse().forEach((item) => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'image-item';
                
                const pathDiv = document.createElement('div');
                pathDiv.className = 'path';
                pathDiv.textContent = `路径: ${item.path.length > 30 ? item.path.substring(0, 30) + '...' : item.path}`;
                
                const urlDiv = document.createElement('div');
                urlDiv.className = 'url';
                urlDiv.textContent = `解析: ${item.resolvedUrl ? (item.resolvedUrl.length > 40 ? item.resolvedUrl.substring(0, 40) + '...' : item.resolvedUrl) : '处理中...'}`;
                
                const infoDiv = document.createElement('div');
                infoDiv.className = 'info';
                infoDiv.textContent = `${item.timestamp} | ${item.type} | ${item.source}`;
                
                itemDiv.appendChild(pathDiv);
                itemDiv.appendChild(urlDiv);
                itemDiv.appendChild(infoDiv);
                
                // 图片预览
                if (item.resolvedUrl && (item.resolvedUrl.startsWith('blob:') || item.resolvedUrl.startsWith('data:') || item.resolvedUrl.startsWith('http'))) {
                    const previewDiv = document.createElement('div');
                    previewDiv.className = 'preview';
                    
                    const img = document.createElement('img');
                    img.src = item.resolvedUrl;
                    img.onerror = function() {
                        previewDiv.innerHTML = '<span style="color: #ff6b6b;">加载失败</span>';
                    };
                    
                    previewDiv.appendChild(img);
                    itemDiv.appendChild(previewDiv);
                }
                
                debugContent.appendChild(itemDiv);
            });
        }

        // 测试单个 URL
        async function testUrl(inputId, resultId) {
            const input = document.getElementById(inputId);
            const result = document.getElementById(resultId);
            const url = input.value.trim();
            
            if (!url) {
                result.innerHTML = '<span style="color: red;">请输入 URL</span>';
                return;
            }
            
            result.innerHTML = '<span style="color: blue;">处理中...</span>';
            
            try {
                log(`测试 URL: ${url}`);
                const resolvedUrl = await mockGetImageURL(url);
                
                result.innerHTML = `
                    <div class="url-display"><strong>原始:</strong> ${url}</div>
                    <div class="url-display"><strong>解析:</strong> ${resolvedUrl}</div>
                    <img src="${resolvedUrl}" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div style="display: none; color: red;">图片加载失败</div>
                `;
                
                log(`测试完成: ${url} -> ${resolvedUrl}`);
            } catch (error) {
                result.innerHTML = `<span style="color: red;">错误: ${error.message}</span>`;
                log(`测试失败: ${error.message}`, 'error');
            }
        }

        // 批量测试
        async function runBatchTest() {
            showStatus('正在运行批量测试...', 'info');
            
            const testUrls = [
                'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
                'https://via.placeholder.com/100/FF0000/FFFFFF?text=Red',
                'https://via.placeholder.com/100/00FF00/FFFFFF?text=Green',
                'media/image1.png',
                'media/image2.jpg',
                '/absolute/path/image.png',
                'relative/path/image.gif'
            ];
            
            log('开始批量测试...');
            
            for (let i = 0; i < testUrls.length; i++) {
                const url = testUrls[i];
                try {
                    await mockGetImageURL(url);
                    log(`批量测试 ${i + 1}/${testUrls.length}: ${url} - 成功`);
                } catch (error) {
                    log(`批量测试 ${i + 1}/${testUrls.length}: ${url} - 失败: ${error.message}`, 'error');
                }
                
                // 添加小延迟以便观察
                await new Promise(resolve => setTimeout(resolve, 200));
            }
            
            showStatus(`批量测试完成，共测试 ${testUrls.length} 个 URL`, 'success');
            log('批量测试完成');
        }

        // 清空结果
        function clearResults() {
            const results = document.querySelectorAll('.result');
            results.forEach(result => result.innerHTML = '');
            
            mockImageRequestLog = [];
            updateMockDebugPanel();
            
            document.getElementById('logOutput').textContent = '';
            document.getElementById('batchStatus').style.display = 'none';
            
            log('已清空所有结果');
        }

        // 页面加载完成
        window.addEventListener('load', function() {
            log('调试面板测试页面加载完成');
            log('右侧面板将显示所有 getImageURL 请求的详细信息');
        });
    </script>
</body>
</html>
