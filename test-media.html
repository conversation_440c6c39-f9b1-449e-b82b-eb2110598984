<!DOCTYPE html>
<html>
<head>
    <title>媒体资源测试</title>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .log {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .media-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .media-item {
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .media-item img {
            max-width: 100%;
            max-height: 150px;
            object-fit: contain;
        }
        .media-path {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
            word-break: break-all;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>媒体资源处理测试</h1>
    
    <div class="container">
        <h2>测试步骤</h2>
        <ol>
            <li>点击"初始化 X2T"按钮</li>
            <li>选择包含图片的文档文件</li>
            <li>点击"转换文档"按钮</li>
            <li>查看提取的媒体资源</li>
            <li>测试 getImageURL 功能</li>
        </ol>
    </div>

    <div class="container">
        <h2>操作</h2>
        <button id="initBtn" class="button">初始化 X2T</button>
        <input type="file" id="fileInput" accept=".docx,.doc,.xlsx,.pptx" style="margin: 10px;">
        <button id="convertBtn" class="button" disabled>转换文档</button>
        <button id="testImageBtn" class="button" disabled>测试 getImageURL</button>
        <button id="clearBtn" class="button">清空日志</button>
        <div id="status" class="status info" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>媒体资源预览</h2>
        <div id="mediaCount">媒体文件数量: 0</div>
        <div id="mediaGrid" class="media-grid"></div>
    </div>

    <div class="container">
        <h2>日志输出</h2>
        <div id="logOutput" class="log"></div>
    </div>

    <script src="./x2t.js"></script>
    <script>
        let isInitialized = false;
        let currentMediaResources = new Map();

        // 日志函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logOutput.textContent += logMessage;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(logMessage);
        }

        // 显示状态
        function showStatus(message, type = 'info') {
            const element = document.getElementById('status');
            element.textContent = message;
            element.className = `status ${type}`;
            element.style.display = 'block';
        }

        // 隐藏状态
        function hideStatus() {
            document.getElementById('status').style.display = 'none';
        }

        // 显示媒体资源
        function displayMediaResources(mediaResources) {
            const mediaGrid = document.getElementById('mediaGrid');
            const mediaCount = document.getElementById('mediaCount');
            
            mediaGrid.innerHTML = '';
            mediaCount.textContent = `媒体文件数量: ${Object.keys(mediaResources).length}`;
            
            Object.keys(mediaResources).forEach(mediaPath => {
                const mediaUrl = mediaResources[mediaPath];
                const mediaItem = document.createElement('div');
                mediaItem.className = 'media-item';
                
                // 判断文件类型
                const extension = mediaPath.split('.').pop().toLowerCase();
                
                if (['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg'].includes(extension)) {
                    // 图片文件
                    const img = document.createElement('img');
                    img.src = mediaUrl;
                    img.alt = mediaPath;
                    img.onerror = function() {
                        this.style.display = 'none';
                        const errorText = document.createElement('div');
                        errorText.textContent = '图片加载失败';
                        errorText.style.color = 'red';
                        mediaItem.appendChild(errorText);
                    };
                    mediaItem.appendChild(img);
                } else {
                    // 其他文件类型
                    const fileIcon = document.createElement('div');
                    fileIcon.textContent = '📄';
                    fileIcon.style.fontSize = '48px';
                    mediaItem.appendChild(fileIcon);
                }
                
                const pathDiv = document.createElement('div');
                pathDiv.className = 'media-path';
                pathDiv.textContent = mediaPath;
                mediaItem.appendChild(pathDiv);
                
                const urlDiv = document.createElement('div');
                urlDiv.className = 'media-path';
                urlDiv.textContent = `URL: ${mediaUrl.substring(0, 50)}...`;
                urlDiv.style.fontSize = '10px';
                mediaItem.appendChild(urlDiv);
                
                mediaGrid.appendChild(mediaItem);
            });
        }

        // 模拟 getImageURL 函数
        function mockGetImageURL(imagePath, callback) {
            log(`模拟 getImageURL 调用: ${imagePath}`);
            
            return new Promise((resolve, reject) => {
                // 检查媒体资源中是否有对应的图片
                if (currentMediaResources.has(imagePath)) {
                    const imageUrl = currentMediaResources.get(imagePath);
                    log(`找到媒体资源: ${imagePath} -> ${imageUrl}`);
                    resolve(imageUrl);
                    if (callback) callback(imageUrl);
                } else {
                    // 尝试匹配部分路径
                    let foundUrl = null;
                    for (let [storedPath, url] of currentMediaResources) {
                        if (storedPath.includes(imagePath) || imagePath.includes(storedPath.replace('media/', ''))) {
                            foundUrl = url;
                            log(`部分匹配媒体资源: ${imagePath} -> ${storedPath} -> ${url}`);
                            break;
                        }
                    }
                    
                    if (foundUrl) {
                        resolve(foundUrl);
                        if (callback) callback(foundUrl);
                    } else {
                        log(`未找到媒体资源: ${imagePath}`, 'warn');
                        log(`可用的媒体资源: ${Array.from(currentMediaResources.keys()).join(', ')}`);
                        
                        // 返回一个默认的空图片或者原始路径
                        const fallbackUrl = imagePath;
                        resolve(fallbackUrl);
                        if (callback) callback(fallbackUrl);
                    }
                }
            });
        }

        // 初始化 X2T
        document.getElementById('initBtn').addEventListener('click', async function() {
            const btn = this;
            btn.disabled = true;
            showStatus('正在初始化 X2T 转换器...', 'info');
            
            try {
                log('开始初始化 X2T 转换器...');
                
                if (typeof window.initX2T !== 'function') {
                    throw new Error('X2T 脚本未正确加载');
                }
                
                await window.initX2T();
                isInitialized = true;
                
                log('X2T 转换器初始化成功');
                showStatus('X2T 转换器初始化成功！', 'success');
                
                // 启用转换按钮
                document.getElementById('convertBtn').disabled = false;
                
            } catch (error) {
                log(`X2T 初始化失败: ${error.message}`, 'error');
                showStatus(`初始化失败: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
            }
        });

        // 文档转换
        document.getElementById('convertBtn').addEventListener('click', async function() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('请先选择一个文件');
                return;
            }
            
            if (!isInitialized) {
                alert('请先初始化 X2T 转换器');
                return;
            }
            
            const btn = this;
            btn.disabled = true;
            showStatus('正在转换文档...', 'info');
            
            try {
                log(`开始转换文件: ${file.name} (${file.size} bytes)`);
                
                const result = await window.convertDocument(file);
                
                log(`文档转换成功:`);
                log(`  - 文件名: ${result.fileName}`);
                log(`  - 类型: ${result.type}`);
                log(`  - BIN 数据大小: ${result.bin.length} bytes`);
                log(`  - 媒体文件数量: ${Object.keys(result.media).length}`);
                
                // 存储媒体资源
                currentMediaResources.clear();
                Object.keys(result.media).forEach(mediaPath => {
                    currentMediaResources.set(mediaPath, result.media[mediaPath]);
                    log(`存储媒体资源: ${mediaPath}`);
                });
                
                // 显示媒体资源
                displayMediaResources(result.media);
                
                showStatus(`转换成功！BIN 数据大小: ${result.bin.length} bytes，媒体文件: ${Object.keys(result.media).length} 个`, 'success');
                
                // 启用测试按钮
                document.getElementById('testImageBtn').disabled = false;
                
            } catch (error) {
                log(`文档转换失败: ${error.message}`, 'error');
                showStatus(`转换失败: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
            }
        });

        // 测试 getImageURL
        document.getElementById('testImageBtn').addEventListener('click', async function() {
            if (currentMediaResources.size === 0) {
                alert('请先转换包含媒体文件的文档');
                return;
            }
            
            log('开始测试 getImageURL 功能...');
            
            // 测试所有媒体资源
            for (let mediaPath of currentMediaResources.keys()) {
                try {
                    const url = await mockGetImageURL(mediaPath);
                    log(`getImageURL 测试成功: ${mediaPath} -> ${url}`);
                } catch (error) {
                    log(`getImageURL 测试失败: ${mediaPath} -> ${error.message}`, 'error');
                }
            }
            
            // 测试部分路径匹配
            const firstMediaPath = Array.from(currentMediaResources.keys())[0];
            if (firstMediaPath) {
                const partialPath = firstMediaPath.replace('media/', '');
                try {
                    const url = await mockGetImageURL(partialPath);
                    log(`部分路径测试成功: ${partialPath} -> ${url}`);
                } catch (error) {
                    log(`部分路径测试失败: ${partialPath} -> ${error.message}`, 'error');
                }
            }
        });

        // 清空日志
        document.getElementById('clearBtn').addEventListener('click', function() {
            document.getElementById('logOutput').textContent = '';
            hideStatus();
        });

        // 页面加载完成
        window.addEventListener('load', function() {
            log('页面加载完成');
            
            if (typeof window.X2TConverter !== 'undefined') {
                log('X2T 脚本加载成功');
            } else {
                log('X2T 脚本未加载', 'error');
            }
        });
    </script>
</body>
</html>
