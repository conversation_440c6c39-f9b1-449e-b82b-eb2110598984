# dictionary-eu-es

Basque (Spain) spelling dictionary in UTF-8.

Useful with [hunspell][hunspell] ([node bindings][nodehun]), Open Office,
LibreOffice, FireFox and Thunderbird, or [place them in
`~/Library/Spelling`][osx] on OS X.

Generated by [**wooorm/dictionaries**][dictionaries], from [this
dictionary][source].

## Installation

[npm][npm]:

```bash
npm install dictionary-eu-es
```

## Usage

```js
var euES = require('dictionary-eu-es');

euES(function (err, result) {
    if (err) throw err;

    console.log(result);
    /*
     * Yields:
     * { dic: <Buffer>,
     *   aff: <Buffer> }
     */
});
```

Where `dic` is a buffer for the dictionary file at `index.dic` (in UTF-8), and
`aff` is a buffer for the affix file at `index.aff` (in UTF-8).

Or directly load the files, using something like:

```js
var path = require('path');
var base = require.resolve('dictionary-eu-es');

// NEVER USE `readFileSync` IN PRODUCTION.
fs.readFileSync(path.join(base, 'index.dic'), 'utf-8');
fs.readFileSync(path.join(base, 'index.aff'), 'utf-8');
```

## License

Dictionary and affix file: GPL-2.0. Rest: MIT ©
[Titus Wormer][home].

[hunspell]: http://hunspell.sourceforge.net

[nodehun]: https://github.com/nathanjsweet/nodehun

[osx]: https://github.com/wooorm/dictionaries#os-x

[source]: http://extensions.openoffice.org/en/project/xuxen-basque-spell-checking-dictionary

[npm]: https://docs.npmjs.com/cli/install

[dictionaries]: https://github.com/wooorm/dictionaries

[home]: https://wooorm.com
