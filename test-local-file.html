<!DOCTYPE html>
<html>
<head>
    <title>本地文件加载测试</title>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .file-drop-zone {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            background-color: #f9f9f9;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .file-drop-zone:hover {
            border-color: #4CAF50;
            background-color: #f0f8f0;
        }
        .file-drop-zone.dragover {
            border-color: #4CAF50;
            background-color: #e8f5e8;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .file-info {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .log {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            font-size: 12px;
        }
        .media-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .media-item {
            border: 1px solid #ddd;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
            background: white;
        }
        .media-item img {
            max-width: 100%;
            max-height: 100px;
            object-fit: contain;
        }
        .media-path {
            font-size: 10px;
            color: #666;
            margin-top: 5px;
            word-break: break-all;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>本地文件加载和媒体资源测试</h1>
    
    <div class="container">
        <h2>文件选择</h2>
        <div class="file-drop-zone" id="dropZone">
            <p>📁 拖拽文件到这里或点击选择文件</p>
            <p style="font-size: 14px; color: #666;">支持: .docx, .doc, .xlsx, .xls, .pptx, .ppt, .odt, .ods, .odp, .rtf, .txt, .csv</p>
            <input type="file" id="fileInput" accept=".docx,.doc,.xlsx,.xls,.pptx,.ppt,.odt,.ods,.odp,.rtf,.txt,.csv" style="display: none;">
        </div>
        <div id="fileInfo" class="file-info" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>操作</h2>
        <button id="initBtn" class="button">初始化 X2T</button>
        <button id="convertBtn" class="button" disabled>转换文档</button>
        <button id="testMimeBtn" class="button" disabled>测试 MIME 类型</button>
        <button id="clearBtn" class="button">清空日志</button>
        <div id="status" class="status info" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>转换结果</h2>
        <div id="conversionResult" style="display: none;">
            <h3>文档信息</h3>
            <div id="docInfo"></div>
            <h3>媒体资源 (<span id="mediaCount">0</span>)</h3>
            <div id="mediaPreview" class="media-preview"></div>
        </div>
    </div>

    <div class="container">
        <h2>日志输出</h2>
        <div id="logOutput" class="log"></div>
    </div>

    <script src="./x2t.js"></script>
    <script>
        let isInitialized = false;
        let currentFile = null;
        let conversionResult = null;

        // 日志函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logOutput.textContent += logMessage;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(logMessage);
        }

        // 显示状态
        function showStatus(message, type = 'info') {
            const element = document.getElementById('status');
            element.textContent = message;
            element.className = `status ${type}`;
            element.style.display = 'block';
        }

        // 隐藏状态
        function hideStatus() {
            document.getElementById('status').style.display = 'none';
        }

        // 显示文件信息
        function showFileInfo(file) {
            const fileInfo = document.getElementById('fileInfo');
            const fileName = file.name;
            const fileSize = (file.size / 1024 / 1024).toFixed(2);
            const fileType = fileName.split('.').pop().toLowerCase();
            
            fileInfo.innerHTML = `
                <h3>📄 已选择文件</h3>
                <p><strong>文件名:</strong> ${fileName}</p>
                <p><strong>大小:</strong> ${fileSize} MB</p>
                <p><strong>类型:</strong> ${fileType.toUpperCase()}</p>
                <p><strong>MIME:</strong> <span id="mimeType">检测中...</span></p>
            `;
            fileInfo.style.display = 'block';
            
            // 检测 MIME 类型
            if (window.x2tConverter && window.x2tConverter.getMimeTypeFromExtension) {
                const mimeType = window.x2tConverter.getMimeTypeFromExtension(fileType);
                document.getElementById('mimeType').textContent = mimeType;
                log(`检测到 MIME 类型: ${fileType} -> ${mimeType}`);
            }
        }

        // 显示转换结果
        function showConversionResult(result) {
            const resultDiv = document.getElementById('conversionResult');
            const docInfo = document.getElementById('docInfo');
            const mediaCount = document.getElementById('mediaCount');
            const mediaPreview = document.getElementById('mediaPreview');
            
            // 文档信息
            docInfo.innerHTML = `
                <p><strong>文件名:</strong> ${result.fileName}</p>
                <p><strong>类型:</strong> ${result.type}</p>
                <p><strong>BIN 大小:</strong> ${result.bin.length} bytes</p>
            `;
            
            // 媒体资源
            const mediaKeys = Object.keys(result.media);
            mediaCount.textContent = mediaKeys.length;
            
            mediaPreview.innerHTML = '';
            mediaKeys.forEach(mediaPath => {
                const mediaUrl = result.media[mediaPath];
                const mediaItem = document.createElement('div');
                mediaItem.className = 'media-item';
                
                const extension = mediaPath.split('.').pop().toLowerCase();
                if (['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg'].includes(extension)) {
                    const img = document.createElement('img');
                    img.src = mediaUrl;
                    img.alt = mediaPath;
                    img.onerror = function() {
                        this.style.display = 'none';
                        const errorText = document.createElement('div');
                        errorText.textContent = '❌';
                        errorText.style.color = 'red';
                        mediaItem.appendChild(errorText);
                    };
                    mediaItem.appendChild(img);
                } else {
                    const fileIcon = document.createElement('div');
                    fileIcon.textContent = '📄';
                    fileIcon.style.fontSize = '32px';
                    mediaItem.appendChild(fileIcon);
                }
                
                const pathDiv = document.createElement('div');
                pathDiv.className = 'media-path';
                pathDiv.textContent = mediaPath;
                mediaItem.appendChild(pathDiv);
                
                mediaPreview.appendChild(mediaItem);
            });
            
            resultDiv.style.display = 'block';
        }

        // 文件处理
        function handleFile(file) {
            currentFile = file;
            showFileInfo(file);
            document.getElementById('convertBtn').disabled = !isInitialized;
            document.getElementById('testMimeBtn').disabled = false;
            log(`选择文件: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`);
        }

        // 文件拖拽处理
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');

        dropZone.addEventListener('click', () => fileInput.click());

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        // 初始化 X2T
        document.getElementById('initBtn').addEventListener('click', async function() {
            const btn = this;
            btn.disabled = true;
            showStatus('正在初始化 X2T 转换器...', 'info');
            
            try {
                log('开始初始化 X2T 转换器...');
                
                if (typeof window.initX2T !== 'function') {
                    throw new Error('X2T 脚本未正确加载');
                }
                
                await window.initX2T();
                isInitialized = true;
                
                log('X2T 转换器初始化成功');
                showStatus('X2T 转换器初始化成功！', 'success');
                
                // 启用相关按钮
                if (currentFile) {
                    document.getElementById('convertBtn').disabled = false;
                }
                
            } catch (error) {
                log(`X2T 初始化失败: ${error.message}`, 'error');
                showStatus(`初始化失败: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
            }
        });

        // 转换文档
        document.getElementById('convertBtn').addEventListener('click', async function() {
            if (!currentFile) {
                alert('请先选择一个文件');
                return;
            }
            
            if (!isInitialized) {
                alert('请先初始化 X2T 转换器');
                return;
            }
            
            const btn = this;
            btn.disabled = true;
            showStatus('正在转换文档...', 'info');
            
            try {
                log(`开始转换文件: ${currentFile.name}`);
                
                const result = await window.convertDocument(currentFile);
                conversionResult = result;
                
                log(`文档转换成功:`);
                log(`  - 文件名: ${result.fileName}`);
                log(`  - 类型: ${result.type}`);
                log(`  - BIN 数据大小: ${result.bin.length} bytes`);
                log(`  - 媒体文件数量: ${Object.keys(result.media).length}`);
                
                // 显示转换结果
                showConversionResult(result);
                
                showStatus(`转换成功！BIN 数据大小: ${result.bin.length} bytes，媒体文件: ${Object.keys(result.media).length} 个`, 'success');
                
            } catch (error) {
                log(`文档转换失败: ${error.message}`, 'error');
                showStatus(`转换失败: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
            }
        });

        // 测试 MIME 类型
        document.getElementById('testMimeBtn').addEventListener('click', function() {
            if (!currentFile) {
                alert('请先选择一个文件');
                return;
            }
            
            const fileName = currentFile.name;
            const fileExtension = fileName.split('.').pop().toLowerCase();
            
            log('测试 MIME 类型检测...');
            
            if (window.x2tConverter && window.x2tConverter.getMimeTypeFromExtension) {
                const mimeType = window.x2tConverter.getMimeTypeFromExtension(fileExtension);
                log(`文件扩展名: ${fileExtension}`);
                log(`检测到的 MIME 类型: ${mimeType}`);
                log(`浏览器检测的 MIME 类型: ${currentFile.type}`);
                
                showStatus(`MIME 类型: ${mimeType}`, 'info');
            } else {
                log('X2T 转换器未初始化或 getMimeTypeFromExtension 方法不可用', 'error');
            }
        });

        // 清空日志
        document.getElementById('clearBtn').addEventListener('click', function() {
            document.getElementById('logOutput').textContent = '';
            hideStatus();
        });

        // 页面加载完成
        window.addEventListener('load', function() {
            log('页面加载完成');
            
            if (typeof window.X2TConverter !== 'undefined') {
                log('X2T 脚本加载成功');
            } else {
                log('X2T 脚本未加载', 'error');
            }
        });
    </script>
</body>
</html>
