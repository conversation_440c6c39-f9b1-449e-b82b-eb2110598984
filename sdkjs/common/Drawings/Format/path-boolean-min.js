/*
 * Copyright (C) Ascensio System SIA 2012-2025. All rights reserved
 *
 * https://www.onlyoffice.com/
 *
 * Version: 0.0.0 (build:0)
 */

function fa(z){var x=0;return function(){return x<z.length?{done:!1,value:z[x++]}:{done:!0}}}function la(z){var x="undefined"!=typeof Symbol&&Symbol.iterator&&z[Symbol.iterator];if(x)return x.call(z);if("number"==typeof z.length)return{next:fa(z)};throw Error(String(z)+" is not an iterable or ArrayLike");}var na="function"==typeof Object.defineProperties?Object.defineProperty:function(z,x,t){if(z==Array.prototype||z==Object.prototype)return z;z[x]=t.value;return z};
function oa(z){z=["object"==typeof globalThis&&globalThis,z,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var x=0;x<z.length;++x){var t=z[x];if(t&&t.Math==Math)return t}throw Error("Cannot find global object");}var pa=oa(this);function V(z,x){if(x)a:{var t=pa;z=z.split(".");for(var u=0;u<z.length-1;u++){var C=z[u];if(!(C in t))break a;t=t[C]}z=z[z.length-1];u=t[z];x=x(u);x!=u&&null!=x&&na(t,z,{configurable:!0,writable:!0,value:x})}}
V("Object.is",function(z){return z?z:function(x,t){return x===t?0!==x||1/x===1/t:x!==x&&t!==t}});V("Array.prototype.includes",function(z){return z?z:function(x,t){var u=this;u instanceof String&&(u=String(u));var C=u.length;t=t||0;for(0>t&&(t=Math.max(t+C,0));t<C;t++){var r=u[t];if(r===x||Object.is(r,x))return!0}return!1}});
V("String.prototype.includes",function(z){return z?z:function(x,t){if(null==this)throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(x instanceof RegExp)throw new TypeError("First argument to String.prototype.includes must not be a regular expression");return-1!==(this+"").indexOf(x,t||0)}});
V("Symbol",function(z){function x(r){if(this instanceof x)throw new TypeError("Symbol is not a constructor");return new t(u+(r||"")+"_"+C++,r)}function t(r,D){this.pd=r;na(this,"description",{configurable:!0,writable:!0,value:D})}if(z)return z;t.prototype.toString=function(){return this.pd};var u="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",C=0;return x});
V("Symbol.iterator",function(z){if(z)return z;z=Symbol("Symbol.iterator");for(var x="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),t=0;t<x.length;t++){var u=pa[x[t]];"function"===typeof u&&"function"!=typeof u.prototype[z]&&na(u.prototype,z,{configurable:!0,writable:!0,value:function(){return qa(fa(this))}})}return z});function qa(z){z={next:z};z[Symbol.iterator]=function(){return this};return z}
function ra(z,x){z instanceof String&&(z+="");var t=0,u=!1,C={next:function(){if(!u&&t<z.length){var r=t++;return{value:x(r,z[r]),done:!1}}u=!0;return{done:!0,value:void 0}}};C[Symbol.iterator]=function(){return C};return C}V("Array.prototype.keys",function(z){return z?z:function(){return ra(this,function(x){return x})}});
var sa="function"==typeof Object.assign?Object.assign:function(z,x){for(var t=1;t<arguments.length;t++){var u=arguments[t];if(u)for(var C in u)Object.prototype.hasOwnProperty.call(u,C)&&(z[C]=u[C])}return z};V("Object.assign",function(z){return z||sa});V("Array.prototype.fill",function(z){return z?z:function(x,t,u){var C=this.length||0;0>t&&(t=Math.max(0,C+t));if(null==u||u>C)u=C;u=Number(u);0>u&&(u=Math.max(0,C+u));for(t=Number(t||0);t<u;t++)this[t]=x;return this}});
function ta(z){return z?z:Array.prototype.fill}V("Int8Array.prototype.fill",ta);V("Uint8Array.prototype.fill",ta);V("Uint8ClampedArray.prototype.fill",ta);V("Int16Array.prototype.fill",ta);V("Uint16Array.prototype.fill",ta);V("Int32Array.prototype.fill",ta);V("Uint32Array.prototype.fill",ta);V("Float32Array.prototype.fill",ta);V("Float64Array.prototype.fill",ta);
V("Array.prototype.flatMap",function(z){return z?z:function(x,t){var u=[];Array.prototype.forEach.call(this,function(C,r){C=x.call(t,C,r,this);Array.isArray(C)?u.push.apply(u,C):u.push(C)});return u}});
(function(z){function x(a){this.g=[];this.qc(a)||this.nb(Array.isArray(a)?a:arguments)}function t(a){this.F=!1;this.i=[];this.Za=0;var b=Array.isArray(a),c=b&&"object"===typeof a[0],d=a&&void 0===a.size&&(void 0!==a.x||void 0!==a.bd);(b=b?c?a:arguments:d?arguments:null)&&0<b.length?this.dc(b):this.O=void 0;this.qc(!b&&a)}function u(){}function C(a,b,c,d,f){if(.99999999<=b){var e=a.ba();e&&(b=0,a=e)}this.Fb(a);this.ka=b;this.j=c||a.Rc(b);this.va=d;this.ud=f;this.v=this.Ca=this.Ra=null}function r(a,
b,c,d,f,e,g,h){var l=arguments.length;if(3===l){this.h=a;var k=b;var p=c}else if(l)if(1===l)if(a.me)k=new D(a.me),p=new D(a.Ee);else if(a.ie){var m=a.ie;var q=a.ze;var n=a.Ae;var v=a.Ce}else Array.isArray(a)&&(m=[a[0],a[1]],v=[a[6],a[7]],q=[a[2]-a[0],a[3]-a[1]],n=[a[4]-a[6],a[5]-a[7]]);else 2===l?(k=new D(a),p=new D(b)):4===l?(m=a,q=b,n=c,v=d):8===l&&(m=[a,b],v=[g,h],q=[c-a,d-b],n=[f-g,e-h]);else k=new D,p=new D;this.s=k||new D(m,null,q);this.M=p||new D(v,n,null)}function D(a,b,c,d,f,e){for(var g=
0,h=arguments.length;g<h;g++){var l=arguments[g];l&&Object.assign(this,l)}g=arguments.length;if(0<g)if(null==a||"object"===typeof a)if(1===g&&a&&a.bd){var k=a.bd;var p=a.Uc;var m=a.Be}else k=a,p=b,m=c;else k=[a,b],p=void 0!==c?[c,d]:null,m=void 0!==f?[f,e]:null;this.j=new w(k,this);this.A=new w(p,this);this.H=new w(m,this)}function B(){}function N(a,b,c,d,f){if(4<=arguments.length){this.Sa=a;this.Ta=b;this.pa=c;this.qa=d;var e=f}else this.Sa=a.x,this.Ta=a.y,this.pa=b.x,this.qa=b.y,e=c;e||(this.pa-=
this.Sa,this.qa-=this.Ta)}function M(a,b){var c=arguments.length;6<=c?this.I.apply(this,arguments):1===c||2===c?a instanceof M?this.I(a.B,a.C,a.D,a.G,a.J,a.K,b):Array.isArray(a)&&this.I.apply(this,b?a.concat([b]):a):c||this.reset();return this}function F(a,b,c,d){var f=typeof a;if("number"===f){this.I(a,b,c,d);var e=4}else if("undefined"===f||null===a)this.I(0,0,0,0),e=null===a?1:0;this.Pa&&(this.Pa=e);return this}function w(a,b,c){var d=typeof a,f=this.Pa,e=0;"number"===d?(d="number"===typeof b,
this.I(a,d?b:a),f&&(e=d?2:1)):"undefined"===d||null===a?(this.I(0,0),f&&(e=null===a?1:0)):(e=1,Array.isArray(a)?this.I(+a[0],+(1<a.length?a[1]:a[0])):(b=a.x,"number"===typeof b&&!isNaN(b)&&isFinite(b)?this.I(a.x||0,a.y||0):(this.I(0,0),e=0)));f&&(this.Pa=e);c&&(this.oa=c);return this}function I(){}function ba(a,b){a.prototype=Object.create(b.prototype);a.prototype.constructor=a;Object.getOwnPropertyNames(b).forEach(function(c){["prototype","name","length"].includes(c)||Function.prototype.hasOwnProperty(c)||
Object.defineProperty(a,c,Object.getOwnPropertyDescriptor(b,c))});a.prototype.initialize=a}var ja={Ic:function(a,b,c){function d(e){for(var g=Array(e.length),h=0;h<e.length;h++){var l=e[h].W();g[h]=[l.Ua(),l.Va(),l.Wd(),l.Jd()]}return g}var f=d(a);a=b&&b!==a?d(b):f;return this.sb(f,a,c)},Hc:function(a,b,c,d){function f(g){for(var h=Array(g.length),l=0;l<g.length;l++){var k=g[l];h[l]=[Math.min(k[0],k[2],k[4],k[6]),Math.min(k[1],k[3],k[5],k[7]),Math.max(k[0],k[2],k[4],k[6]),Math.max(k[1],k[3],k[5],
k[7])]}return h}var e=f(a);a=b&&b!==a?f(b):e;if(d){d=this.sb(e,a,c||0,!1,!0);c=this.sb(e,a,c||0,!0,!0);e=[];a=0;for(b=d.length;a<b;a++)e[a]={Tb:d[a],fc:c[a]};return e}return this.sb(e,a,c||0)},sb:function(a,b,c,d,f){function e(W,ca,Y){for(var da=0,R=W.length;da<R;){var S=R+da>>>1;h[W[S]][ca]<Y?da=S+1:R=S}return da-1}var g=!b||a===b,h=g?a:a.concat(b),l=d?1:0,k=l+2,p=d?0:1,m=p+2,q=Array(h.length);for(d=0;d<h.length;d++)q[d]=d;q.sort(function(W,ca){return h[W][l]-h[ca][l]});var n=[];d=Array(a.length);
for(var v=0;v<h.length;v++){var y=q[v],A=h[y],E=g?y:y-a.length,G=y<a.length,P=g||!G,U=G?[]:null;if(n.length){var Q=e(n,k,A[l]-c)+1;n.splice(0,Q);if(g&&f)for(U=U.concat(n),P=0;P<n.length;P++)d[n[P]].push(E);else for(Q=0;Q<n.length;Q++){var J=n[Q],K=h[J],O=J<a.length,L=g||J>=a.length;L=G&&L;O=P&&O;K=A[m]>=K[p]-c&&A[p]<=K[m]+c;if(f||(L||O)&&K)L&&U.push(g?J:J-a.length),O&&d[J].push(E)}}G&&(a===b&&U.push(y),d[y]=U);n.length?(A=e(n,k,A[k]),n.splice(A+1,0,y)):n.push(y)}for(a=0;a<d.length;a++)(b=d[a])&&b.sort(function(W,
ca){return W-ca});return d}},H=new function(){function a(e,g,h){return e<g?g:e>h?h:e}function b(e,g,h){function l(q){var n=134217729*q;n=q-n+n;return[n,q-n]}var k=g*g-e*h;if(3*Math.abs(k)<g*g+e*h){k=l(e);var p=l(g),m=l(h);g*=g;e*=h;k=g-e+(p[0]*p[0]-g+2*p[0]*p[1]+p[1]*p[1]-(k[0]*m[0]-e+k[0]*m[1]+k[1]*m[0]+k[1]*m[1]))}return k}function c(){var e=Math.max.apply(Math,arguments);return e&&(1E-8>e||1E8<e)?Math.pow(2,-Math.round(Math.log(e)*Math.LOG2E)):0}var d=[[.5773502691896257],[0,.7745966692414834],
[.33998104358485626,.8611363115940526],[0,.5384693101056831,.906179845938664],[.2386191860831969,.6612093864662645,.932469514203152],[0,.4058451513773972,.7415311855993945,.9491079123427585],[.1834346424956498,.525532409916329,.7966664774136267,.9602898564975363],[0,.3242534234038089,.6133714327005904,.8360311073266358,.9681602395076261],[.14887433898163122,.4333953941292472,.6794095682990244,.8650633666889845,.9739065285171717],[0,.26954315595234496,.5190961292068118,.7301520055740494,.8870625997680953,
.978228658146057],[.1252334085114689,.3678314989981802,.5873179542866175,.7699026741943047,.9041172563704749,.9815606342467192],[0,.2304583159551348,.44849275103644687,.6423493394403402,.8015780907333099,.9175983992229779,.9841830547185881],[.10805494870734367,.31911236892788974,.5152486363581541,.6872929048116855,.827201315069765,.9284348836635735,.9862838086968123],[0,.20119409399743451,.3941513470775634,.5709721726085388,.7244177313601701,.8482065834104272,.937273392400706,.9879925180204854],[.09501250983763744,
.2816035507792589,.45801677765722737,.6178762444026438,.755404408355003,.8656312023878318,.9445750230732326,.9894009349916499]],f=[[1],[.8888888888888888,.5555555555555556],[.6521451548625461,.34785484513745385],[.5688888888888889,.47862867049936647,.23692688505618908],[.46791393457269104,.3607615730481386,.17132449237917036],[.4179591836734694,.3818300505051189,.27970539148927664,.1294849661688697],[.362683783378362,.31370664587788727,.22238103445337448,.10122853629037626],[.3302393550012598,.31234707704000286,
.26061069640293544,.1806481606948574,.08127438836157441],[.29552422471475287,.26926671930999635,.21908636251598204,.1494513491505806,.06667134430868814],[.2729250867779006,.26280454451024665,.23319376459199048,.18629021092773426,.1255803694649046,.05566856711617366],[.24914704581340277,.2334925365383548,.20316742672306592,.16007832854334622,.10693932599531843,.04717533638651183],[.2325515532308739,.22628318026289723,.2078160475368885,.17814598076194574,.13887351021978725,.09212149983772845,.04048400476531588],
[.2152638534631578,.2051984637212956,.18553839747793782,.15720316715819355,.12151857068790319,.08015808715976021,.03511946033175186],[.2025782419255613,.19843148532711158,.1861610000155622,.16626920581699392,.13957067792615432,.10715922046717194,.07036604748810812,.03075324199611727],[.1894506104550685,.18260341504492358,.16915651939500254,.14959598881657674,.12462897125553388,.09515851168249279,.062253523938647894,.027152459411754096]];return{EPSILON:1E-12,ue:1.12E-16,te:1E-8,na:1E-7,u:function(e){return-1E-12<=
e&&1E-12>=e},ee:function(e){return-1.12E-16<=e&&1.12E-16>=e},Dd:a,Xc:function(e,g,h,l){var k=d[l-2],p=f[l-2];h=.5*(h-g);g=h+g;var m=l+1>>1,q=0;for(l=l&1?p[q++]*e(g):0;q<m;){var n=h*k[q];l+=p[q++]*(e(g+n)+e(g-n))}return h*l},Hd:function(e,g,h,l,k,p,m){for(var q=0;q<p;q++){var n=e(h),v=n/g(h),y=h-v;if(Math.abs(v)<m){h=y;break}0<n?(k=h,h=y<=l?.5*(l+k):y):(l=h,h=y>=k?.5*(l+k):y)}return a(h,l,k)},ec:function(e,g,h,l,k,p){var m=Infinity;if(1E-12>Math.abs(e)){if(1E-12>Math.abs(g))return 1E-12>Math.abs(h)?
-1:0;var q=-h/g}else{g*=-.5;var n=b(e,g,h);if(n&&1.12E-16>Math.abs(n)){var v=c(Math.abs(e),Math.abs(g),Math.abs(h));v&&(e*=v,g*=v,h*=v,n=b(e,g,h))}-1.12E-16<=n&&(q=0>n?0:Math.sqrt(n),m=g+(0>g?-q:q),q=0===m?h/e:m/e,m=0===m?-q:h/m)}e=0;h=null==k;g=k-1E-12;n=p+1E-12;isFinite(q)&&(h||q>g&&q<n)&&(l[e++]=h?q:a(q,k,p));m!==q&&isFinite(m)&&(h||m>g&&m<n)&&(l[e++]=h?m:a(m,k,p));return e},Oa:function(e,g,h,l,k,p,m){function q(Q){G=Q;Q=e*G;A=Q+g;E=A*G+h;v=(Q+A)*G+E;y=E*G+l}var n=c(Math.abs(e),Math.abs(g),Math.abs(h),
Math.abs(l));n&&(e*=n,g*=n,h*=n,l*=n);var v,y;if(1E-12>Math.abs(e)){e=g;var A=h;var E=l;var G=Infinity}else if(1E-12>Math.abs(l))A=g,E=h,G=0;else{q(-(g/e)/3);var P=y/e;n=Math.pow(Math.abs(P),1/3);P=0>P?-1:1;var U=-v/e;n=G-P*(0<U?1.324717957244746*Math.max(n,Math.sqrt(U)):n);if(n!==G){do q(n),n=0===v?G:G-y/v/(1+1.12E-16);while(P*n>P*G);Math.abs(e)*G*G>Math.abs(l/G)&&(E=-l/G,A=(E-h)/G)}}n=H.ec(e,A,E,k,p,m);P=null==p;U=0===n||0<n&&G!==k[0]&&G!==k[1];isFinite(G)&&U&&(P||G>p-1E-12&&G<m+1E-12)&&(k[n++]=
P?G:a(G,p,m));return n}}},ua={id:1,cd:{},get:function(a){if(a){var b=this.cd[a];b||(b=this.cd[a]={T:1});return b.T++}return this.id++}};I.Gc=function(a,b,c){if(a){var d=Object.getOwnPropertyDescriptor(a,"length"),f=function(e,g){for(var h in this)this.hasOwnProperty(h)&&e.call(g,this[h],h,this)};(d&&"number"===typeof d.value?Array.prototype.forEach:f).call(a,b,c=c||a)}return c};I.$c=function(a){return(a=null!=a&&a.constructor)&&(a===Object||a===I||"Object"===a.name)};I.yb=function(a,b){return void 0!==
a?a:b};I.slice=function(a,b,c){return Array.prototype.slice.call(a,b,c)};I.L=function(a,b){if(a===b)return!0;if(a&&a.L)return a.L(b);if(b&&b.L)return b.L(a);if(a&&b&&"object"===typeof a&&"object"===typeof b){if(Array.isArray(a)&&Array.isArray(b)){var c=a.length;if(c!==b.length)return!1;for(;c--;)if(!I.L(a[c],b[c]))return!1}else{c=Object.keys(a);var d=c.length;if(d!==Object.keys(b).length)return!1;for(;d--;){var f=c[d];if(!b.hasOwnProperty(f)||!I.L(a[f],b[f]))return!1}}return!0}return!1};I.read=function(a,
b,c,d){if(this===I)return c=a[a.Xa=b||a.Xa||0],a.Xa++,c;var f=this===w||this===F;b=b||f&&a.Xa||0;var e=a[b];d=d||a.length-b;if(e instanceof this||c&&c.fd&&null==e&&1>=d)return f&&(a.Xa=b+1),e&&c&&c.clone?e.clone():e;e=Object.create(this.prototype);f&&(e.Pa=!0);e=e.initialize.apply(e,0<b||b+d<a.length?I.slice(a,b,b+d):a)||e;f&&(a.Xa=b+e.Pa,e.Pa=void 0);return e};I.$b=function(a,b,c,d){var f=[];b=b||0;for(d=d?b+d:a.length;b<d;b++){var e=a[b];f.push(Array.isArray(e)?this.read(e,0,c):this.read(a,b,c,
1))}return f};I.filter=function(a,b,c,d){function f(p){if(!(c&&p in c||k&&p in k)){var m=b[p];void 0!==m&&(a[p]=m)}}if(d){for(var e={},g=0,h=d.length;g<h;g++){var l=d[g];l in b&&(f(l),e[l]=!0)}var k=e}Object.keys(b).forEach(f);return a};I.fe=function(a){return I.$c(a)||Array.isArray(a)||void 0};I.splice=function(a,b,c,d){var f=b&&b.length,e=void 0===c;c=e?a.length:c;c>a.length&&(c=a.length);for(var g=0;g<f;g++)b[g].m=c+g;if(e)return a.push.apply(a,b),[];d=[c,d];b&&d.push.apply(d,b);b=a.splice.apply(a,
d);d=0;for(e=b.length;d<e;d++)b[d].m=void 0;c+=f;for(f=a.length;c<f;c++)a[c].m=c;return b};ba(w,I);w.prototype.set=w;w.prototype.I=function(a,b){this.x=a;this.y=b;this.oa&&this.oa.l(this);return this};w.prototype.Ja=function(){return this.x};w.prototype.Ka=function(){return this.y};w.prototype.L=function(a){return this===a||a&&(this.x===a.x&&this.y===a.y||Array.isArray(a)&&this.x===a[0]&&this.y===a[1])};w.prototype.clone=function(){return new w(this.x,this.y)};w.prototype.N=function(){return Math.sqrt(this.x*
this.x+this.y*this.y)};w.prototype.tb=function(){return 180*this.Id.apply(this,arguments)/Math.PI};w.prototype.Id=function(){if(arguments.length){var a=w.read(arguments),b=this.N()*a.N();if(H.u(b))return NaN;a=this.Fa(a)/b;return Math.acos(-1>a?-1:1<a?1:a)}return this.u()?this.Bb||0:this.Bb=Math.atan2(this.y,this.x)};w.prototype.V=function(){var a=w.read(arguments),b=a.x-this.x;a=a.y-this.y;b=b*b+a*a;return I.read(arguments)?b:Math.sqrt(b)};w.prototype.normalize=function(a){void 0===a&&(a=1);var b=
this.N();a=0!==b?a/b:0;b=new w(this.x*a,this.y*a);0<=a&&(b.Bb=this.Bb);return b};w.prototype.rotate=function(a,b){if(0===a)return this.clone();a=a*Math.PI/180;var c=b?this.R(b):this,d=Math.sin(a);a=Math.cos(a);c=new w(c.x*a-c.y*d,c.x*d+c.y*a);return b?c.add(b):c};w.prototype.transform=function(a){return a?a.Ya(this):this};w.prototype.add=function(){var a=w.read(arguments);return new w(this.x+a.x,this.y+a.y)};w.prototype.R=function(){var a=w.read(arguments);return new w(this.x-a.x,this.y-a.y)};w.prototype.multiply=
function(){var a=w.read(arguments);return new w(this.x*a.x,this.y*a.y)};w.prototype.ab=function(){var a=w.read(arguments);return new w(this.x/a.x,this.y/a.y)};w.prototype.Zb=function(){return new w(-this.x,-this.y)};w.prototype.Wb=function(){return F.read(arguments).contains(this)};w.prototype.da=function(){var a=w.read(arguments),b=I.read(arguments);return this.V(a)<=b};w.prototype.ea=function(){var a=w.read(arguments);return w.ea(this.x,this.y,a.x,a.y)};w.prototype.u=function(){return H.u(this.x)&&
H.u(this.y)};w.prototype.isNaN=function(){return isNaN(this.x)||isNaN(this.y)};w.prototype.Fa=function(){var a=w.read(arguments);return this.x*a.x+this.y*a.y};w.ea=function(a,b,c,d){return Math.abs(a*d-b*c)<=1E-8*Math.sqrt((a*a+b*b)*(c*c+d*d))};ba(F,I);F.prototype.I=function(a,b,c,d){this.x=a;this.y=b;this.width=c;this.height=d;return this};F.prototype.clone=function(){return new F(this.x,this.y,this.width,this.height)};F.prototype.L=function(a){var b=I.fe(a)?F.read(arguments):a;return b===this||
b&&this.x===b.x&&this.y===b.y&&this.width===b.width&&this.height===b.height};F.prototype.S=function(){return new w(this.x,this.y,this)};F.prototype.Ua=F.prototype.Ja=function(){return this.x};F.prototype.Va=F.prototype.Ka=function(){return this.y};F.prototype.Wd=function(){return this.x+this.width};F.prototype.Jd=function(){return this.y+this.height};F.prototype.Qb=function(){return this.width};F.prototype.Kb=function(){return this.height};F.prototype.Kd=function(){return this.Ua()+this.Qb()/2};F.prototype.Ld=
function(){return this.Va()+this.Kb()/2};F.prototype.Jc=function(){return new w(this.Kd(),this.Ld())};F.prototype.Zd=function(){return new w(this.Ua(),this.Va())};F.prototype.aa=function(){return this.width*this.height};F.prototype.ha=function(){return 0===this.width||0===this.height};F.prototype.contains=function(a){return a&&void 0!==a.width||4===(Array.isArray(a)?a:arguments).length?this.sd(F.read(arguments)):this.rd(w.read(arguments))};F.prototype.rd=function(a){var b=a.x;a=a.y;return b>=this.x&&
a>=this.y&&b<=this.x+this.width&&a<=this.y+this.height};F.prototype.sd=function(a){var b=a.x,c=a.y;return b>=this.x&&c>=this.y&&b+a.width<=this.x+this.width&&c+a.height<=this.y+this.height};F.prototype.de=function(){var a=F.read(arguments),b=I.read(arguments)||0;return a.x+a.width>this.x-b&&a.y+a.height>this.y-b&&a.x<this.x+this.width+b&&a.y<this.y+this.height+b};ba(M,I);M.prototype.set=M;M.prototype.I=function(a,b,c,d,f,e,g){this.B=a;this.C=b;this.D=c;this.G=d;this.J=f;this.K=e;g||this.l();return this};
M.prototype.l=function(){this.oa&&(this.oa.Qa?this.oa.transform(null,!0):this.oa.l(25))};M.prototype.clone=function(){return new M(this.B,this.C,this.D,this.G,this.J,this.K)};M.prototype.L=function(a){return a===this||a&&this.B===a.B&&this.C===a.C&&this.D===a.D&&this.G===a.G&&this.J===a.J&&this.K===a.K};M.prototype.reset=function(a){this.B=this.G=1;this.C=this.D=this.J=this.K=0;a||this.l();return this};M.prototype.apply=function(a,b){if(!this.oa)return!1;this.oa.transform(null,I.yb(a,!0),b);return this.isIdentity()};
M.prototype.translate=function(){var a=w.read(arguments);this.J+=a.x*this.B+a.y*this.D;this.K+=a.x*this.C+a.y*this.G;this.l();return this};M.prototype.scale=function(){var a=w.read(arguments),b=w.read(arguments,0,{fd:!0});b&&this.translate(b);this.B*=a.x;this.C*=a.x;this.D*=a.y;this.G*=a.y;b&&this.translate(b.Zb());this.l();return this};M.prototype.rotate=function(a){a*=Math.PI/180;var b=w.read(arguments,1),c=Math.cos(a),d=Math.sin(a),f=b.x-b.x*c+b.y*d;b=b.y-b.x*d-b.y*c;var e=this.B,g=this.C,h=this.D,
l=this.G;this.B=c*e+d*h;this.C=c*g+d*l;this.D=-d*e+c*h;this.G=-d*g+c*l;this.J+=f*e+b*h;this.K+=f*g+b*l;this.l();return this};M.prototype.append=function(a,b){if(a){var c=this.B,d=this.C,f=this.D,e=this.G,g=a.B,h=a.D,l=a.C,k=a.G,p=a.J;a=a.K;this.B=g*c+l*f;this.D=h*c+k*f;this.C=g*d+l*e;this.G=h*d+k*e;this.J+=p*c+a*f;this.K+=p*d+a*e;b||this.l()}return this};M.prototype.prepend=function(a,b){if(a){var c=this.B,d=this.C,f=this.D,e=this.G,g=a.B,h=a.D,l=a.C,k=a.G,p=this.J,m=this.K,q=a.J;a=a.K;this.B=g*c+
h*d;this.D=g*f+h*e;this.C=l*c+k*d;this.G=l*f+k*e;this.J=g*p+h*m+q;this.K=l*p+k*m+a;b||this.l()}return this};M.prototype.Cd=function(a){return this.clone().append(a)};M.prototype.kb=function(){return this.isIdentity()?null:this};M.prototype.isIdentity=function(){return 1===this.B&&0===this.C&&0===this.D&&1===this.G&&0===this.J&&0===this.K};M.prototype.Xb=function(){var a=this.B*this.G-this.D*this.C;return a&&!isNaN(a)&&isFinite(this.J)&&isFinite(this.K)};M.prototype.transform=function(a,b,c){return 3>
arguments.length?this.Ya(w.read(arguments)):this.la(a,b,c)};M.prototype.Ya=function(a,b,c){b||(b=new w);return b.I(a.x*this.B+a.y*this.D+this.J,a.x*this.C+a.y*this.G+this.K,c)};M.prototype.la=function(a,b,c){var d=0;for(c*=2;d<c;d+=2){var f=a[d],e=a[d+1];b[d]=f*this.B+e*this.D+this.J;b[d+1]=f*this.C+e*this.G+this.K}return b};M.prototype.zd=function(a){var b=a.x,c=a.y,d=b+a.width;a=c+a.height;b=[b,c,d,c,d,a,b,a];return this.la(b,b,4)};M.prototype.yd=function(a,b){a=this.zd(a);for(var c=a.slice(0,2),
d=c.slice(),f=2;8>f;f++){var e=a[f],g=f&1;e<c[g]?c[g]=e:e>d[g]&&(d[g]=e)}b||(b=new F);b.I(c[0],c[1],d[0]-c[0],d[1]-c[1],void 0)};M.prototype.xd=function(a){var b,c=this.B,d=this.C,f=this.D,e=this.G,g=this.J,h=this.K,l=c*e-d*f,k=null;l&&!isNaN(l)&&isFinite(g)&&isFinite(h)&&(g=a.x-this.J,a=a.y-this.K,b||(b=new w),k=b.I((g*e-a*f)/l,(a*c-g*d)/l,void 0));return k};M.prototype.Fd=function(){var a=this.B,b=this.C,c=this.D,d=this.G,f=a*d-b*c,e=180/Math.PI;if(0!==a||0!==b){var g=Math.sqrt(a*a+b*b);var h=Math.acos(a/
g)*(0<b?1:-1);f=[g,f/g];a=[Math.atan2(a*c+b*d,g*g),0]}else 0!==c||0!==d?(g=Math.sqrt(c*c+d*d),h=Math.asin(c/g)*(0<d?1:-1),f=[f/g,g],a=[0,Math.atan2(a*c+b*d,g*g)]):(h=0,a=f=[0,0]);return{Ge:this.$d(),rotation:h*e,De:new w(f),pe:new w(a[0]*e,a[1]*e)}};M.prototype.o=function(){return[this.B,this.C,this.D,this.G,this.J,this.K]};M.prototype.$d=function(){return new w(this.J,this.K)};ba(N,I);N.prototype.S=function(){return new w(this.Sa,this.Ta)};N.prototype.ae=function(){return new w(this.pa,this.qa)};
N.prototype.N=function(){return this.ae().N()};N.prototype.ga=function(a,b){return N.ga(this.Sa,this.Ta,this.pa,this.qa,a.Sa,a.Ta,a.pa,a.qa,!0,b)};N.prototype.V=function(a){return Math.abs(this.ma(a))};N.prototype.ma=function(a){return N.ma(this.Sa,this.Ta,this.pa,this.qa,a.x,a.y,!0)};N.prototype.ea=function(a){return w.ea(this.pa,this.qa,a.pa,a.qa)};N.ga=function(a,b,c,d,f,e,g,h,l,k){l||(c-=a,d-=b,g-=f,h-=e);l=c*h-d*g;if(!H.ee(l)&&(f=a-f,e=b-e,g=(g*e-h*f)/l,h=(c*e-d*f)/l,l=-H.EPSILON,e=1+H.EPSILON,
k||l<g&&g<e&&l<h&&h<e))return k||(g=0>=g?0:1<=g?1:g),new w(a+g*c,b+g*d)};N.ma=function(a,b,c,d,f,e,g){g||(c-=a,d-=b);return 0===c?0<d?f-a:a-f:0===d?0>c?e-b:b-e:((f-a)*d-(e-b)*c)/(d>c?d*Math.sqrt(1+c*c/(d*d)):c*Math.sqrt(1+d*d/(c*c)))};N.V=function(a,b,c,d,f,e,g){return Math.abs(N.ma(a,b,c,d,f,e,g))};ba(B,I);B.prototype.Qa=!0;B.prototype.ib=!0;B.prototype.xa=null;B.prototype.qc=function(a){var b=a&&I.$c(a);a=b&&!0===a.xb;var c=this.$=new M;this.T=a?null:ua.get();this.wa=this.m=null;this.Qa=this.ib&&
!0;c.oa=this;return b};B.prototype.l=function(a){a&8&&(this.Y=this.ya=void 0);this.wa&&a&72&&B.Db(this.wa);a&2&&B.Db(this)};B.prototype.Ob=function(){var a=this.ya||(this.ya=this.oc());return new w(a.x,a.y,this)};B.prototype.setPosition=function(){this.translate(w.read(arguments).R(this.Ob(!0)))};B.prototype.oc=function(a){return this.xa?this.$.Ya(this.xa):(a||this.W()).Jc()};B.prototype.oe=function(){this.xa=w.read(arguments,0,{clone:!0,fd:!0});this.ya=void 0};B.prototype.W=function(a){var b=Object.assign({},
a);b.Ac=this;b=this.mc(!1,b).rect;return arguments.length?b:new F(b.x,b.y,b.width,b.height)};B.prototype.jb=function(a,b){if(!this.g||!this.g.length)return new F;B.xc(this,b.Ac);return B.jb(this.g,a,b)};B.prototype.vd=function(a,b){return[a.stroke?1:0,a.handle?1:0,b?1:0].join("")};B.prototype.mc=function(a,b,c){a=a&&a.kb();c=b.xb&&!c;var d=b.Ac,f=c?null:this.$.kb(),e=d&&(!a||a.L(f))&&this.vd(b,c);B.xc(this.wa,d);if(e&&this.Y&&e in this.Y)return c=this.Y[e],{rect:c.rect.clone(),Na:c.Na};b=this.jb(a||
f,b);a=b.rect||b;b=b.Na;e&&(this.Y||(this.Y={}),this.Y[e]={rect:a.clone(),Na:b,xb:c});return{rect:a,Na:b}};B.prototype.ne=function(a){(this.Qa=this.ib&&!!a)&&this.transform(null,!0)};B.prototype.nc=B.prototype.getParent=function(){return this.wa};B.prototype.Jb=function(){return this.g};B.prototype.kd=function(a){this.ac();this.nb(a)};B.prototype.Pd=function(){return this.g&&this.g[0]||null};B.prototype.Nc=function(){return this.g&&this.g[this.g.length-1]||null};B.prototype.sa=function(){return this.m};
B.prototype.L=function(a){return a===this||a&&this.$.L(a.$)&&this.lc(a)};B.prototype.lc=function(a){return I.L(this.g,a.g)};B.prototype.clone=function(a){var b=new this.constructor({ca:!1});this.g&&b.$a(this);var c=I.yb(a?a.xe:void 0,!0);this.g&&!c||b.Ec(this);this.g||b.$a(this);I.yb(a?a.ca:void 0,void 0===a||!0===a)&&b.Ub(this);return b};B.prototype.Ec=function(a){for(var b=0,c=a.g&&a.g.length;b<c;b++)this.zc(a.g[b].clone(!1))};B.prototype.$a=function(a,b){b||this.$.set(a.$,!0);this.ne(a.Qa);this.oe(a.xa);
this.td=(a=a.td)?Object.assign(new a.constructor,a):null};B.prototype.contains=function(){return this.$.Xb()&&!!this.jc(this.$.xd(w.read(arguments)))};B.prototype.jc=function(a){if(this.g){for(var b=this.g.length-1;0<=b;b--)if(this.g[b].contains(a))return!0;return!1}return a.Wb(this.ye())};B.prototype.Wb=function(){return F.read(arguments).contains(this.W())};B.prototype.zc=function(a){this.ce(a)};B.prototype.ce=function(a){a&&this.gb(void 0,[a])};B.prototype.nb=function(a){this.gb(this.g.length,
a)};B.prototype.gb=function(a,b){if(this.g&&b&&0<b.length){b=I.slice(b);for(var c={},d=b.length-1;0<=d;d--){var f=b[d],e=f&&f.T;!f||c[e]?b.splice(d,1):(f.lb(!0),c[e]=!0)}I.splice(this.g,b,a,0);a=0;for(c=b.length;a<c;a++)b[a].wa=this;this.l(11)}else b=null;return b};B.prototype.rc=function(a,b){var c=a&&a.nc(),d=a!==this&&c?this:null;d&&(d.lb(!0),c.wd(a.m+b,d));return d};B.prototype.wd=function(a,b){b&&this.gb(a,[b])};B.prototype.Ub=function(a){this.rc(a,1)};B.prototype.be=function(a){return this.rc(a,
0)};B.prototype.reduce=function(a){return this.g&&1===this.g.length?(a=this.g[0].reduce(a),this.wa?(a.Ub(this),this.remove()):a.remove(),a):this};B.prototype.lb=function(a){var b=this.nc();return b?(null!=this.m&&I.splice(b.g,null,this.m,1),a&&b.l(11),this.wa=null,!0):!1};B.prototype.remove=function(){return this.lb(!0)};B.prototype.replaceWith=function(a){(a=a&&a.be(this))&&this.remove();return a};B.prototype.clear=B.prototype.ac=function(a,b){if(!this.g)return null;a=a||0;b=I.yb(b,this.g.length);
a=I.splice(this.g,null,a,b-a);for(b=a.length-1;0<=b;b--)a[b].lb(!1);0<a.length&&this.l(11);return a};B.prototype.ha=function(a){var b=this.g?this.g.length:0;if(a){for(var c=0;c<b;c++)if(!this.g[c].ha(a))return!1;return!0}return!b};B.prototype.translate=function(){var a=new M;return this.transform(a.translate.apply(a,arguments))};B.prototype.transform=function(a,b,c){var d=this.$,f=a&&!a.isIdentity(),e=c&&this.ib||this.Qa&&(f||!d.isIdentity()||b&&this.g);if(!f&&!e)return this;f&&(!a.Xb()&&d.Xb()&&
(d.ve=d.o()),d.prepend(a,!0));e&&(e=this.wc(d,b,c))&&(this.xa&&d.Ya(this.xa,this.xa,!0),d.reset(!0),c&&this.ib&&(this.Qa=!0));b=this.Y;c=this.ya;(f||e)&&this.l(25);if((d=f&&b&&a.Fd())&&d.pe.u()&&0===d.rotation%90){for(var g in b)f=b[g],f.Na?delete b[g]:(e||!f.xb)&&a.yd(f.rect,f.rect);this.Y=b;if(a=b["000"])this.ya=this.oc(a.rect)}else f&&c&&this.xa&&(this.ya=a.Ya(c,c));return this};B.prototype.wc=function(a,b,c){var d=this.g;if(d){for(var f=0,e=d.length;f<e;f++)d[f].transform(a,b,c);return!0}};B.xc=
function(a,b){if(a&&b){var c=b.T;a=a.hb=a.hb||{Wc:{},list:[]};a.Wc[c]||(a.list.push(b),a.Wc[c]=b)}};B.Db=function(a){var b=a.hb;if(b){a.Y=a.ya=a.hb=void 0;var c=0;b=b.list;for(var d=b.length;c<d;c++){var f=b[c];f!==a&&(f.Y=f.ya=void 0,f.hb&&B.Db(f))}}};B.jb=function(a,b,c){var d=Infinity,f=-d,e=d,g=f,h=!1;c=c||{};for(var l=0,k=a.length;l<k;l++){var p=a[l];p.ha(!0)||(p=p.mc(b&&b.Cd(p.$),c,!0),d=Math.min(p.rect.x,d),e=Math.min(p.rect.y,e),f=Math.max(p.rect.x+p.rect.width,f),g=Math.max(p.rect.y+p.rect.height,
g),p.Na&&(h=!0))}return{rect:isFinite(d)?new F(d,e,f-d,g-e):new F,Na:h}};ba(D,I);D.prototype.l=function(a){if(this.h){var b=this.h.O,c=this.m,d;b&&(a&&a!==this.j&&a!==this.A||!(d=0<c?b[c-1]:this.h.F?b[b.length-1]:null)||d.l(),a&&a!==this.j&&a!==this.H||!(d=b[c])||d.l());this.h.l(41)}};D.prototype.S=function(){return this.j};D.prototype.Rd=function(){return this.A};D.prototype.zb=function(){this.A.set(w.read(arguments))};D.prototype.Sd=function(){return this.H};D.prototype.cc=function(){var a=w.read(arguments);
this.H.set(a)};D.prototype.La=function(){return!this.A.u()||!this.H.u()};D.prototype.pb=function(){this.A.I(0,0);this.H.I(0,0)};D.prototype.sa=function(){return void 0!==this.m?this.m:null};D.prototype.Ha=function(){return this.h||null};D.prototype.P=function(){var a=this.h,b=this.m;return a?(0<b&&!a.F&&b===a.i.length-1&&b--,a.U()[b]||null):null};D.prototype.ba=function(){var a=this.h&&this.h.i;return a&&(a[this.m+1]||this.h.F&&a[0])||null};D.prototype.za=function(){var a=this.h&&this.h.i;return a&&
(a[this.m-1]||this.h.F&&a[a.length-1])||null};D.prototype.Vb=function(){return!this.m};D.prototype.Yb=function(){return this.h&&this.m===this.h.i.length-1||!1};D.prototype.reverse=function(){var a=this.A,b=this.H,c=a.clone();a.set(b);b.set(c)};D.prototype.remove=function(){return this.h?!!this.h.gd(this.m):!1};D.prototype.clone=function(){return new D(this.j,this.A,this.H)};D.prototype.L=function(a){return a===this||a&&this.j.L(a.j)&&this.A.L(a.A)&&this.H.L(a.H)||!1};D.prototype.transform=function(a){this.la(a,
Array(6),!0);this.l()};D.prototype.la=function(a,b,c){var d=c&&this.A.u()?null:this.A,f=c&&this.H.u()?null:this.H,e=this.j.Ja(),g=this.j.Ka(),h=2;b[0]=e;b[1]=g;d&&(b[h++]=d.Ja()+e,b[h++]=d.Ka()+g);f&&(b[h++]=f.Ja()+e,b[h++]=f.Ka()+g);a&&(a.la(b,b,h/2),e=b[0],g=b[1],c?(this.j.x=e,this.j.y=g,h=2,d&&(d.x=b[h++]-e,d.y=b[h++]-g),f&&(f.x=b[h++]-e,f.y=b[h++]-g)):(d||(b[h++]=e,b[h++]=g),f||(b[h++]=e,b[h++]=g)));return b};ba(r,I);r.prototype.l=function(){this.Ba=this.Y=void 0};r.prototype.clone=function(){return new r(this.s,
this.M)};r.prototype.ob=function(){return r.ob(this.o())};r.prototype.remove=function(){var a=!1;if(this.h){a=this.M;var b=a.H;(a=a.remove())&&this.s.H.set(b)}return a};r.prototype.Ha=function(){return this.h};r.prototype.sa=function(){return this.s.m};r.prototype.ba=function(){var a=this.h&&this.h.O;return a&&(a[this.s.m+1]||this.h.F&&a[0])||null};r.prototype.za=function(){var a=this.h&&this.h.O;return a&&(a[this.s.m-1]||this.h.F&&a[a.length-1])||null};r.prototype.Vb=function(){return!this.s.m};
r.prototype.Yb=function(){var a=this.h;return a&&this.s.m===a.O.length-1||!1};r.prototype.o=function(a){return r.o(this.s,this.M,a)};r.prototype.N=function(){null==this.Ba&&(this.Ba=r.N(this.o(),0,1));return this.Ba};r.prototype.aa=function(){return r.aa(this.o())};r.prototype.vb=function(){return new N(this.s.j,this.M.j)};r.prototype.eb=function(a,b){return new r(r.eb(this.o(),a,b))};r.prototype.Mb=function(a,b){return r.N(this.o(),a,b)};r.prototype.Gb=function(a,b){var c=null;if(1E-8<=a&&.99999999>=
a){c=r.ua(this.o(),a);a=c[0];c=c[1];var d=b||this.La();b=this.s;var f=this.M,e=this.h;d&&(b.H.I(a[2]-a[0],a[3]-a[1]),f.A.I(c[4]-c[6],c[5]-c[7]));var g=a[6],h=a[7];a=new D(new w(g,h),d&&new w(a[4]-g,a[5]-h),d&&new w(c[2]-g,c[3]-h));e?(e.ca(b.m+1,a),c=this.ba()):(this.M=a,this.l(),c=new r(a,f))}return c};r.prototype.ab=function(a,b){return this.Gb(void 0===a?.5:b?a:this.Ia(a))};r.prototype.pb=function(){this.s.H.I(0,0);this.M.A.I(0,0)};r.prototype.La=function(){return!this.s.H.u()||!this.M.A.u()};r.prototype.Vc=
function(a){return(!this.s.j.L(this.M.j)||this.La())&&this.N()>(a||0)};r.prototype.ea=function(a){return a&&this.ia()&&a.ia()&&this.vb().ea(a.vb())};r.prototype.ia=function(){a:{var a=this.s.j,b=this.s.H;var c=this.M.A;var d=this.M.j;if(b.u()&&c.u())c=!0;else{var f=d.R(a);if(!f.u()&&f.ea(b)&&f.ea(c)){var e=new N(a,d);if(1E-7>e.V(a.add(b))&&1E-7>e.V(d.add(c))){a=f.Fa(f);b=f.Fa(b)/a;c=f.Fa(c)/a;c=0<=b&&1>=b&&0>=c&&-1<=c;break a}}c=!1}}return c};r.prototype.wb=function(a){return this.Ud(this.Ia(a))};
r.prototype.Ud=function(a){return null!=a&&0<=a&&1>=a?new C(this,a):null};r.prototype.Ia=function(a,b){return r.Ia(this.o(),a,b)};r.prototype.ta=function(){return r.ta(this.o(),w.read(arguments))};r.prototype.fb=function(a){var b=this.o();return r.S(b,r.Ia(b,a))};r.prototype.Rc=function(a){return r.S(this.o(),a)};r.prototype.Tc=function(a,b){var c=this.o();return r.Aa(c,b?a:r.Ia(c,a))};r.prototype.Pb=function(a){return r.Aa(this.o(),a)};r.prototype.cb=function(a){var b=this.o(),c=a&&a!==this&&a.o();
return c?r.Lc(b,c,this,a,[]):r.Sc(b,this,[])};r.o=function(a,b,c,d){var f=a.j;a=a.H;var e=b.A,g=b.j;b=f.x;f=f.y;var h=g.x;g=g.y;d=d?[b,f,b,f,h,g,h,g]:[b,f,b+a.Ja(),f+a.Ka(),h+e.Ja(),g+e.Ka(),h,g];c&&c.la(d,d,4);return d};r.ua=function(a,b){void 0===b&&(b=.5);var c=a[0],d=a[1],f=a[2],e=a[3],g=a[4],h=a[5],l=a[6];a=a[7];var k=1-b,p=k*c+b*f,m=k*d+b*e,q=k*f+b*g,n=k*e+b*h;g=k*g+b*l;h=k*h+b*a;e=k*p+b*q;f=k*m+b*n;q=k*q+b*g;n=k*n+b*h;var v=k*e+b*q;b=k*f+b*n;return[[c,d,p,m,e,f,v,b],[v,b,q,n,g,h,l,a]]};r.Pc=
function(a,b){var c=[];b=b?0:1;var d=a[b+0],f=a[b+2],e=a[b+4],g=a[b+6];d>=f===f>=e&&f>=e===e>=g||r.ia(a)?c.push(a):(b=[],(d=H.ec(3*(f-e)-d+g,2*(d+e)-4*f,f-d,b,1E-8,.99999999))?(b.sort(),f=b[0],a=r.ua(a,f),c.push(a[0]),1<d&&(f=(b[1]-f)/(1-f),a=r.ua(a[1],f),c.push(a[0])),c.push(a[1])):c.push(a));return c};r.Oa=function(a,b,c,d,f,e){var g=a[b],h=a[b+2],l=a[b+4];a=a[b+6];b=0;g<c&&a<c&&h<c&&l<c||g>c&&a>c&&h>c&&l>c||(b=3*(h-g),h=3*(l-h)-b,b=H.Oa(a-g-b-h,h,b,g-c,d,f,e));return b};r.ta=function(a,b){var c=
new w(a[0],a[1]),d=new w(a[6],a[7]);if(null===(b.da(c,H.EPSILON)?0:b.da(d,H.EPSILON)?1:null))for(var f=[b.x,b.y],e=[],g=0;2>g;g++)for(var h=r.Oa(a,g,f[g],e,0,1),l=0;l<h;l++){var k=e[l];if(b.da(r.S(a,k),H.na))return k}return b.da(c,H.na)?0:b.da(d,H.na)?1:null};r.eb=function(a,b,c){var d=b>c;if(d){var f=b;b=c;c=f}0<b&&(a=r.ua(a,b)[1]);1>c&&(a=r.ua(a,(c-b)/(1-b))[0]);return d?[a[6],a[7],a[4],a[5],a[2],a[3],a[0],a[1]]:a};r.aa=function(a){var b=a[0],c=a[1],d=a[2],f=a[3],e=a[4],g=a[5],h=a[6];a=a[7];return 3*
((a-c)*(d+e)-(h-b)*(f+g)+f*(b-e)-d*(c-g)+a*(e+b/3)-h*(g+c/3))/20};r.W=function(a){for(var b=a.slice(0,2),c=b.slice(),d=[0,0],f=0;2>f;f++)r.hc(a[f],a[f+2],a[f+4],a[f+6],f,b,c,d);return new F(b[0],b[1],c[0]-b[0],c[1]-b[1])};r.hc=function(a,b,c,d,f,e,g,h){function l(v,y){var A=v-y;v+=y;A<e[f]&&(e[f]=A);v>g[f]&&(g[f]=v)}var k=0;var p=e[f]+k,m=g[f]-k;if(a<p||b<p||c<p||d<p||a>m||b>m||c>m||d>m)if(b<a!=b<d&&c<a!=c<d)l(a,0),l(d,0);else for(p=H.ec(3*(b-c)-a+d,2*(a+c)-4*b,b-a,h),l(d,0),m=0;m<p;m++){var q=h[m],
n=1-q;1E-8<=q&&.99999999>=q&&l(n*n*n*a+3*n*n*q*b+3*n*q*q*c+q*q*q*d,k)}};r.ia=function(a){var b=a[0],c=a[1],d=a[6],f=a[7];a:{var e=new w(b,c);b=new w(a[2]-b,a[3]-c);a=new w(a[4]-d,a[5]-f);f=new w(d,f);if(b.u()&&a.u())e=!0;else{d=f.R(e);if(!d.u()&&d.ea(b)&&d.ea(a)&&(c=new N(e,f),c.V(e.add(b))<H.na&&c.V(f.add(a))<H.na)){e=d.Fa(d);b=d.Fa(b)/e;e=d.Fa(a)/e;e=0<=b&&1>=b&&0>=e&&-1<=e;break a}e=!1}}return e};r.Oc=function(a){var b=a[0],c=a[1],d=a[2],f=a[3],e=a[4],g=a[5],h=9*(d-e)+3*(a[6]-b),l=6*(b+e)-12*d,
k=3*(d-b),p=9*(f-g)+3*(a[7]-c),m=6*(c+g)-12*f,q=3*(f-c);return function(n){var v=(h*n+l)*n+k;n=(p*n+m)*n+q;return Math.sqrt(v*v+n*n)}};r.Mc=function(a,b){return Math.max(2,Math.min(16,Math.ceil(32*Math.abs(b-a))))};r.evaluate=function(a,b,c,d){if(null==b||0>b||1<b)return null;var f=a[0],e=a[1],g=a[2],h=a[3],l=a[4],k=a[5],p=a[6],m=a[7];H.u(g-f)&&H.u(h-e)&&(g=f,h=e);H.u(l-p)&&H.u(k-m)&&(l=p,k=m);var q=3*(g-f);a=3*(l-g)-q;var n=p-f-q-a,v=3*(h-e),y=3*(k-h)-v,A=m-e-v-y;0===c?(f=0===b?f:1===b?p:((n*b+a)*
b+q)*b+f,e=0===b?e:1===b?m:((A*b+y)*b+v)*b+e):(1E-8>b?(f=q,e=v):.99999999<b?(f=3*(p-l),e=3*(m-k)):(f=(3*n*b+2*a)*b+q,e=(3*A*b+2*y)*b+v),d&&(0===f&&0===e&&(1E-8>b||.99999999<b)&&(f=l-g,e=k-h),d=Math.sqrt(f*f+e*e))&&(f/=d,e/=d),3===c&&(d=Math.pow(f*f+e*e,1.5),f=0!==d?(f*(6*A*b+2*y)-e*(6*n*b+2*a))/d:0,e=0));return 2===c?new w(e,-f):new w(f,e)};r.ob=function(a){function b(p,m,q){var n=void 0!==m,v=n&&0<m&&1>m,y=n&&0<q&&1>q;!n||(v||y)&&(1!==p||v&&y)||(p=2,v=y=!1);return{type:p,jd:v||y?v&&y?m<q?[m,q]:[q,
m]:[v?m:q]:null}}var c=a[0],d=a[1],f=a[2],e=a[3],g=a[4],h=a[5],l=a[6];a=a[7];var k=f*(d-a)+e*(l-c)+c*a-d*l;e=3*(g*(e-d)+h*(c-f)+f*d-e*c);f=e-k;c=f-k+(c*(a-h)+d*(g-l)+l*h-a*g);d=Math.sqrt(c*c+f*f+e*e);d=0!==d?1/d:0;c*=d;f*=d;e*=d;if(H.u(c))return H.u(f)?b(H.u(e)?3:4):b(0,e/(3*f));d=3*f*f-4*c*e;if(H.u(d))return b(5,f/(2*c));g=0<d?Math.sqrt(d/3):Math.sqrt(-d);c*=2;return b(0<d?0:1,(f+g)/c,(f-g)/c)};r.N=function(a,b,c,d){void 0===b&&(b=0);void 0===c&&(c=1);return r.ia(a)?(1>c&&(a=r.ua(a,c)[0],b/=c),0<
b&&(a=r.ua(a,b)[1]),b=a[6]-a[0],c=a[7]-a[1],Math.sqrt(b*b+c*c)):H.Xc(d||r.Oc(a),b,c,r.Mc(b,c))};r.Ia=function(a,b,c){void 0===c&&(c=0>b?1:0);if(0===b)return c;var d=0<b,f=d?c:0,e=d?1:c,g=r.Oc(a);a=r.N(a,f,e,g);var h=Math.abs(b)-a;if(Math.abs(h)<H.EPSILON)return d?e:f;if(h>H.EPSILON)return null;var l=0;return H.Hd(function(k){l+=H.Xc(g,c,k,r.Mc(c,k));c=k;return l-b},g,c+b/a,f,e,32,H.EPSILON)};r.S=function(a,b){return r.evaluate(a,b,0,!1)};r.Aa=function(a,b){return r.evaluate(a,b,1,!0)};r.Vd=function(a){var b=
a[0],c=a[1],d=a[2],f=a[3],e=a[4],g=a[5],h=-b+3*d-3*e+a[6];e=3*b-6*d+3*e;b=-3*b+3*d;a=-c+3*f-3*g+a[7];g=3*c-6*f+3*g;c=-3*c+3*f;f=[];H.Oa(9*(h*h+a*a),9*(h*e+g*a),2*(e*e+g*g)+3*(b*h+c*a),b*e+g*c,f,1E-8,.99999999);return f.sort()};r.Ea=function(a,b,c,d,f,e,g){var h=!g&&c.za()===f,l=!g&&c!==f&&c.ba()===f;null!==d&&d>=(h?1E-8:0)&&d<=(l?.99999999:1)&&null!==e&&e>=(l?1E-8:0)&&e<=(h?.99999999:1)&&(c=new C(c,d,null,g),f=new C(f,e,null,g),c.v=f,f.v=c,b&&!b(c)||C.ca(a,c,!0))};r.Da=function(a,b,c,d,f,e,g,h,l,
k,p,m,q){if(4096<=++l||40<=++h)return l;var n=b[0],v=b[1],y=b[6],A=b[7],E=N.ma(n,v,y,A,b[2],b[3]),G=N.ma(n,v,y,A,b[4],b[5]),P=0<E*G?.75:4/9,U=P*Math.min(0,E,G);P*=Math.max(0,E,G);var Q=N.ma(n,v,y,A,a[0],a[1]),J=N.ma(n,v,y,A,a[2],a[3]),K=N.ma(n,v,y,A,a[4],a[5]);n=N.ma(n,v,y,A,a[6],a[7]);y=r.Md(Q,J,K,n);v=y[0];y=y[1];var O,L;if(0===E&&0===G&&0===Q&&0===J&&0===K&&0===n||null==(O=r.Cc(v,y,U,P))||null==(L=r.Cc(v.reverse(),y.reverse(),U,P)))return l;E=k+(p-k)*O;k+=(p-k)*L;1E-9>Math.max(q-m,k-E)?(h=(E+k)/
2,m=(m+q)/2,r.Ea(f,e,g?d:c,g?m:h,g?c:d,g?h:m)):(a=r.eb(a,O,L),p=q-m,.8<L-O?k-E>p?(a=r.ua(a,.5),O=(E+k)/2,l=r.Da(b,a[0],d,c,f,e,!g,h,l,m,q,E,O),l=r.Da(b,a[1],d,c,f,e,!g,h,l,m,q,O,k)):(b=r.ua(b,.5),O=(m+q)/2,l=r.Da(b[0],a,d,c,f,e,!g,h,l,m,O,E,k),l=r.Da(b[1],a,d,c,f,e,!g,h,l,O,q,E,k)):l=0===p||1E-9<=p?r.Da(b,a,d,c,f,e,!g,h,l,m,q,E,k):r.Da(a,b,c,d,f,e,g,h,l,E,k,m,q));return l};r.Md=function(a,b,c,d){var f=[0,a],e=[1/3,b],g=[2/3,c],h=[1,d];b-=(2*a+d)/3;a=c-(a+2*d)/3;0>b*a?f=[[f,e,h],[f,g,h]]:(c=b/a,f=
[2<=c?[f,e,h]:.5>=c?[f,g,h]:[f,e,g,h],[f,h]]);return 0>(b||a)?f.reverse():f};r.Cc=function(a,b,c,d){return a[0][1]<c?r.Dc(a,!0,c):b[0][1]>d?r.Dc(b,!1,d):a[0][0]};r.Dc=function(a,b,c){for(var d=a[0][0],f=a[0][1],e=1,g=a.length;e<g;e++){var h=a[e][0],l=a[e][1];if(b?l>=c:l<=c)return l===c?h:d+(c-f)*(h-d)/(l-f);d=h;f=l}return null};r.Nd=function(a,b,c,d,f){if(H.u(d)&&H.u(f))return a=r.ta(a,new w(b,c)),null===a?[]:[a];f=Math.atan2(-f,d);d=Math.sin(f);f=Math.cos(f);for(var e=[],g=[],h=0;8>h;h+=2){var l=
a[h]-b,k=a[h+1]-c;e.push(l*f-k*d,l*d+k*f)}r.Oa(e,1,0,g,0,1);return g};r.Ad=function(a,b,c,d,f,e,g){var h=b[0],l=b[1];h=r.Nd(a,h,l,b[6]-h,b[7]-l);l=0;for(var k=h.length;l<k;l++){var p=h[l],m=r.S(a,p);m=r.ta(b,m);null!==m&&(g?r.Ea(f,e,d,m,c,p):r.Ea(f,e,c,p,d,m))}};r.Bd=function(a,b,c,d,f,e){var g=N.ga(a[0],a[1],a[6],a[7],b[0],b[1],b[6],b[7]);g&&r.Ea(f,e,c,r.ta(a,g),d,r.ta(b,g))};r.Lc=function(a,b,c,d,f,e){if(Math.max(a[0],a[2],a[4],a[6])+H.EPSILON>Math.min(b[0],b[2],b[4],b[6])&&Math.min(a[0],a[2],a[4],
a[6])-H.EPSILON<Math.max(b[0],b[2],b[4],b[6])&&Math.max(a[1],a[3],a[5],a[7])+H.EPSILON>Math.min(b[1],b[3],b[5],b[7])&&Math.min(a[1],a[3],a[5],a[7])-H.EPSILON<Math.max(b[1],b[3],b[5],b[7])){var g=r.Lb(a,b);if(g)for(a=0;2>a;a++)b=g[a],r.Ea(f,e,c,b[0],d,b[1],!0);else{var h=r.ia(a),l=r.ia(b);g=h&&l;var k=h&&!l,p=f.length;h=g?r.Bd:h||l?r.Ad:r.Da;k?h(b,a,d,c,f,e,k,0,0,0,1,0,1):h(a,b,c,d,f,e,k,0,0,0,1,0,1);if(!g||f.length===p)for(g=0;4>g;g++)k=g>>1,p=g&1,l=6*k,h=6*p,l=new w(a[l],a[l+1]),h=new w(b[h],b[h+
1]),l.da(h,H.EPSILON)&&r.Ea(f,e,c,k,d,p)}}return f};r.Sc=function(a,b,c,d){a=r.ob(a);1===a.type&&(a=a.jd,r.Ea(c,d,b,a[0],b,a[1]));return c};r.cb=function(a,b,c,d,f,e){var g=!b;g&&(b=a);for(var h=Array(a.length),l=g?h:Array(b.length),k=[],p=0;p<a.length;p++)h[p]=a[p].o(d);if(!g)for(d=0;d<b.length;d++)l[d]=b[d].o(f);f=ja.Hc(h,l,1E-7);for(d=0;d<a.length;d++){p=a[d];var m=h[d];g&&r.Sc(m,p,k,c);var q=f[d];if(q)for(var n=0;n<q.length;n++){if(e&&k.length)return k;var v=q[n];(!g||v>d)&&r.Lc(m,l[v],p,b[v],
k,c)}}return k};r.Lb=function(a,b){function c(q){var n=q[6]-q[0];q=q[7]-q[1];return n*n+q*q}var d=r.ia(a),f=r.ia(b),e=d&&f,g=c(a)<c(b),h=g?b:a;g=g?a:b;var l=h[0],k=h[1],p=h[6]-l,m=h[7]-k;if(1E-7>N.V(l,k,p,m,g[0],g[1],!0)&&1E-7>N.V(l,k,p,m,g[6],g[7],!0))!e&&1E-7>N.V(l,k,p,m,h[2],h[3],!0)&&1E-7>N.V(l,k,p,m,h[4],h[5],!0)&&1E-7>N.V(l,k,p,m,g[2],g[3],!0)&&1E-7>N.V(l,k,p,m,g[4],g[5],!0)&&(d=f=e=!0);else if(e)return null;if(d^f)return null;f=[a,b];d=[];for(h=0;4>h&&2>d.length&&(g=h&1,k=g^1,l=h>>1,k=r.ta(f[g],
new w(f[k][l?6:0],f[k][l?7:1])),null!=k&&(g=g?[l,k]:[k,l],(!d.length||1E-8<Math.abs(g[0]-d[0][0])&&1E-8<Math.abs(g[1]-d[0][1]))&&d.push(g)),!(2<h)||d.length);h++);2!==d.length?d=null:!e&&(a=r.eb(a,d[0][0],d[1][0]),b=r.eb(b,d[0][1],d[1][1]),1E-7<Math.abs(b[2]-a[2])||1E-7<Math.abs(b[3]-a[3])||1E-7<Math.abs(b[4]-a[4])||1E-7<Math.abs(b[5]-a[5]))&&(d=null);return d};ba(C,I);C.prototype.uc=function(a){this.Za=(this.h=a)?a.Za:0};C.prototype.Fb=function(a){this.uc(a.h);this.Z=a;this.ja=null;this.s=a.s;this.M=
a.M};C.prototype.vc=function(a){var b=a.P();b?this.Fb(b):(this.uc(a.h),this.s=a,this.M=null);this.ja=a;this.ka=a===this.s?0:1;this.j=a.j.clone()};C.prototype.Xd=function(){var a=this.ja;if(!a){var b=this.P(),c=this.getTime();0===c?a=b.s:1===c?a=b.M:null!=c&&(a=b.Mb(0,c)<b.Mb(c,1)?b.s:b.M);this.ja=a}return a};C.prototype.P=function(){function a(c){if((c=c&&c.P())&&null!=(b.ka=c.ta(b.j)))return b.Fb(c),c}this.h&&this.h.Za!==this.Za&&(this.ka=this.sc=this.kc=this.Z=null);var b=this;return this.Z||a(this.ja)||
a(this.s)||a(this.M.za())};C.prototype.Ha=function(){var a=this.P();return a&&a.h};C.prototype.sa=function(){var a=this.P();return a&&a.sa()};C.prototype.getTime=function(){var a=this.P();return a&&null==this.ka?this.ka=a.ta(this.j):this.ka};C.prototype.S=function(){return this.j};C.prototype.Qc=function(){var a=this.sc;if(null==a){a=0;var b=this.Ha(),c=this.sa();if(b&&null!=c){b=b.U();for(var d=0;d<c;d++)a+=b[d].N()}this.sc=a+=this.Od()}return a};C.prototype.Od=function(){var a=this.kc;if(null==
a){a=this.P();var b=this.getTime();this.kc=a=null!=b&&a&&a.Mb(0,b)}return a};C.prototype.V=function(){return this.ud};C.prototype.ab=function(){var a=this.P();(a=a&&a.Gb(this.getTime()))&&this.vc(a.s);return a};C.prototype.L=function(a,b){if(this===a)return!0;if(!(a instanceof C))return!1;var c=this.P(),d=a.P();if(c.h!==d.h)return!1;d=Math.abs(this.Qc()-a.Qc());c=d<H.na||c.h&&Math.abs(c.h.N()-d)<H.na;d=!b&&this.v;a=!b&&a.v;a=!d&&!a||d&&a&&d.L(a,!0);return c&&a};C.prototype.ge=function(){if(this.v&&
this.Aa().ea(this.v.Aa())){var a=this.P(),b=this.v.P();return!(a.ia()&&b.ia()&&a.vb().ga(b.vb()))}return!1};C.prototype.Zc=function(){function a(n,v){var y=n.o(),A=r.ob(y).jd||r.Vd(y);n=A.length;v=r.N(y,v&&n?A[n-1]:0,!v&&n?A[0]:1);p.push(n?v:v/32)}function b(n,v,y){return v<y?n>v&&n<y:n>v||n<y}if(!this.v)return!1;var c=this.getTime(),d=this.v.getTime(),f=1E-8<=c&&.99999999>=c,e=1E-8<=d&&.99999999>=d;if(f&&e)return!this.ge();var g=this.P(),h=g&&1E-8>c?g.za():g,l=this.v.P(),k=l&&1E-8>d?l.za():l;.99999999<
c&&(g=g.ba());.99999999<d&&(l=l.ba());if(!(h&&g&&k&&l))return!1;var p=[];f||(a(h,!0),a(g,!1));e||(a(k,!0),a(l,!1));var m=this.S(),q=Math.min.apply(Math,p);c=f?g.Pb(c):g.fb(q).R(m);h=f?c.Zb():h.fb(-q).R(m);d=e?l.Pb(d):l.fb(q).R(m);k=e?d.Zb():k.fb(-q).R(m);e=h.tb();m=c.tb();k=k.tb();q=d.tb();return!!(f?b(e,k,q)^b(m,k,q)&&b(e,q,k)^b(m,q,k):b(k,e,m)^b(q,e,m)&&b(k,m,e)^b(q,m,e))};C.prototype.Sb=function(){return!!this.va};C.prototype.Aa=function(){var a=this.P(),b=this.getTime();return null!=b&&a&&a.Tc(b,
!0)};C.ca=function(a,b,c){function d(m,q){for(m+=q;-1<=m&&m<=f;m+=q){var n=a[(m%f+f)%f];if(!b.S().da(n.S(),1E-7))break;if(b.L(n))return n}return null}for(var f=a.length,e=0,g=f-1;e<=g;){var h=e+g>>>1,l=a[h],k=void 0;if(c&&(k=b.L(l)?l:d(h,-1)||d(h,1)))return b.va&&(k.va=k.v.va=!0),k;k=b.Ha();var p=l.Ha();0>(k!==p?k.T-p.T:b.sa()+b.getTime()-(l.sa()+l.getTime()))?g=h-1:e=h+1}a.splice(e,0,b);return b};C.expand=function(a){for(var b=a.slice(),c=a.length-1;0<=c;c--)C.ca(b,a[c].v,!1);return b};ba(u,B);u.prototype.Ma=
function(){return 0<=this.aa()};u.prototype.ld=function(a){this.Ma()!=!!a&&this.reverse()};u.prototype.jc=function(a){a=a.Wb(this.W({xb:!0,handle:!0}))?this.pc(a):{};return a.he||!!a.fa};u.prototype.cb=function(a,b,c,d){var f=this===a||!a,e=this.$.kb();c=f?e:(c||a.$).kb();return f||this.W(e).de(a.W(c),1E-12)?r.cb(this.U(),!f&&a.U(),b,e,c,d):[]};u.prototype.compare=function(a){if(!a)return!1;var b=this.g||[this];a=a.g?a.g.slice():[a];var c=b.length,d=a.length,f=ja.Ic(b,a,H.na),e=Array(d).fill(!1),
g=0,h=!0;for(--c;0<=c&&h;c--){var l=b[c],k=f[c],p=!1;if(k)for(var m=k.length-1;0<=m&&!p;m--){var q=k[m];l.compare(a[q])&&(e[q]||(e[q]=!0,g++),p=!0)}p||(h=!1)}return h&&g===d};u.prototype.pc=function(a,b,c){return u.Rb(a,this.U(),b,c)};u.prototype.od=function(a){return u.Ab(this,a,1)};u.prototype.ga=function(a){return u.Ab(this,a,2)};u.prototype.R=function(a){return u.Ab(this,a,3)};u.prototype.Ib=function(a){return u.Ab(this,a,4)};u.prototype.ab=function(a){function b(k){var p=[],m=k.Jb().slice();
m.forEach(function(q){m.some(function(n){return n!==q&&!n.ga(q).ha()})||(p.push(q),q.remove())});k.Jb().length&&p.push(k);return p}if(!Array.isArray(a))return u.Fc([this.Ib(a),this.ga(a)],this);var c=function(k){var p=k.map(function(n){return n.W()});k=Math.min.apply(null,p.map(function(n){return n.Ua()}));var m=Math.min.apply(null,p.map(function(n){return n.Va()})),q=Math.max.apply(null,p.map(function(n){return n.Ua()+n.Qb()}));p=Math.max.apply(null,p.map(function(n){return n.Va()+n.Kb()}));return[k,
m,q,p]}(a);c=new t.qd(c[0]-1,c[1]-1,c[2]+1,c[3]+1);for(var d=[],f=1,e=Math.pow(2,a.length);f<e;f++){for(var g=c,h=0;h<a.length;h++){var l=a[h];g=0!==(f&1<<h)?g.ga(l):g.ga(c.R(l))}g.ha()||(g.we=f,d.push(g))}return d.flatMap(function(k){if(k instanceof t)return[k];if(k instanceof x)return b(k)})};u.prototype.ke=function(){function a(n,v){return(n=n&&n.v)&&n.va&&n.h===v}var b=this.g||[this],c=!1,d=!1,f=this.cb(null,function(n){return n.Sb()&&(c=!0)||n.Zc()&&(d=!0)}),e=c&&d?[]:null;f=C.expand(f);if(c)for(var g=
u.Hb(f,function(n){return n.Sb()},e),h=g.length-1;0<=h;h--){var l=g[h],k=l.h;l=l.ja;var p=l.za(),m=l.ba();a(p,k)&&a(m,k)&&(l.remove(),p.H.I(0,0),m.A.I(0,0),p===l||p.P().Vc()||(m.A.set(p.A),p.remove()))}d&&(u.Hb(f,c&&function(n){var v=n.P(),y=n.Xd(),A=n.v;n=A.Z;A=A.ja;if(v&&n&&v.h&&n.h)return!0;y&&(y.v=null);A&&(A.v=null)},e),e&&u.Bc(e),b=u.nd(I.Gc(b,function(n){this.push.apply(this,n.i)},[])));f=b.length;if(1<f&&this.g){b!==this.g&&this.kd(b);var q=this}else 1!==f||this.g||(b[0]!==this&&this.dc(b[0].bc()),
q=this);q||(q=new x({ca:!1}),q.nb(b),q=q.reduce(),q.$a(this),this.replaceWith(q));return q};u.prototype.je=function(){if(this.g&&this.g.length){var a=u.hd(this.ac(),function(b){return!!b},!0);this.kd(a)}else this.ld(!0);return this};u.prototype.Td=function(){var a=this.W().Jc();if(this.contains(a))return a;var b=this.U(),c=a.y,d=[],f=[];b.forEach(function(e){e=e.o();var g=la([e[1],e[3],e[5],e[7]]),h=g.next().value,l=g.next().value,k=g.next().value;g=g.next().value;c>=Math.min(h,l,k,g)&&c<=Math.max(h,
l,k,g)&&r.Pc(e).forEach(function(p){var m=p[1],q=p[7];m!==q&&c>=Math.min(m,q)&&c<=Math.max(m,q)&&(p=c===m?p[0]:c===q?p[6]:1===r.Oa(p,1,c,f,0,1)?r.S(p,f[0]).x:(p[0]+p[6])/2,d.push(p))})});1<d.length&&(d.sort(function(e,g){return e-g}),a.x=(d[0]+d[1])/2);return a};u.Nb=function(a){return a.g||[a]};u.dd=function(a){a=a.clone(!1).reduce({md:!0}).transform(null,!0,!0);for(var b=u.Nb(a),c=0,d=b.length;c<d;c++){var f=b[c];f.F||f.ha()||(f.closePath(H.EPSILON),f.ra().zb(0,0),f.Ga().cc(0,0))}return a.ke().je()};
u.Fc=function(a,b){var c=new x({ca:!1});c.nb(a);c=c.reduce({md:!0});c.$a(b,!0);return c};u.Gd=function(a){return a.Sb()||a.Zc()};u.Ab=function(a,b,c){function d(v){for(var y=0,A=v.length;y<A;y++){var E=v[y];k.push.apply(k,E.i);p.push.apply(p,E.U());E.tc=!0}}function f(v){for(var y=[],A=0,E=v&&v.length;A<E;A++)y.push(p[v[A]]);return y}var e={1:{1:!0,2:!0,unite:!0},2:{2:!0,intersect:!0},3:{1:!0,subtract:!0},4:{1:!0,"-1":!0,exclude:!0}}[c];c=u.dd(a);(b=b&&a!==b?u.dd(b):null)&&(e.subtract||e.exclude)^
b.Ma()^c.Ma()&&b.reverse();var g=u.Hb(C.expand(c.cb(b,u.Gd))),h=u.Nb(c),l=b?u.Nb(b):null,k=[],p=[];if(g.length){d(h);l&&d(l);h=Array(p.length);l=0;for(var m=p.length;l<m;l++)h[l]=p[l].o();l=ja.Hc(h,h,0,!0);h={};for(m=0;m<p.length;m++){var q=p[m],n=q.h.T;(h[n]=h[n]||{})[q.sa()]={Tb:f(l[m].Tb),fc:f(l[m].fc)}}l=0;for(m=g.length;l<m;l++)u.ed(g[l].ja,c,b,h,e);g=0;for(l=k.length;g<l;g++)m=k[g],q=m.v,m.yc||u.ed(m,c,b,h,e),q&&q.va||(m.h.tc=!1);c=u.nd(k,e)}else c=u.hd(l?h.concat(l):h.slice(),function(v){return!!e[v]});
return u.Fc(c,a)};u.ad=function(a,b){for(var c=a;c;){if(c===b)return;c=c.Ra}for(;a.Ca&&a.Ca!==b;)a=a.Ca;if(!a.Ca){for(;b.Ra;)b=b.Ra;a.Ca=b;b.Ra=a}};u.Bc=function(a){for(var b=a.length-1;0<=b;b--)a[b].pb()};u.hd=function(a,b,c){var d=a?a.length:0;if(!d)return a;var f=I.Gc(a,function(y,A){this[y.T]={qb:null,fa:y.Ma()?1:-1,index:A}},{}),e=a.slice().sort(function(y,A){return Math.abs(A.aa())-Math.abs(y.aa())}),g=e[0];null==c&&(c=g.Ma());g=ja.Ic(e,null,H.na);for(var h=0;h<d;h++){var l=e[h],k=f[l.T],p=
0,m=g[h];if(m)for(var q=null,n=m.length-1;0<=n;n--)if(m[n]<h){q=q||l.Td();var v=e[m[n]];if(v.contains(q)){m=f[v.T];p=m.fa;k.fa+=p;k.qb=m.exclude?m.qb:v;break}}b(k.fa)===b(p)?(k.exclude=!0,a[k.index]=null):l.ld(k.qb?!k.qb.Ma():c)}return a};u.Hb=function(a,b,c){for(var d=b&&[],f=c||[],e=c&&{},g=!1,h,l,k,p=(c&&c.length)-1;0<=p;p--){var m=c[p];m.h&&(e[m.h.T+"."+m.s.m]=!0)}for(p=a.length-1;0<=p;p--){m=a[p];var q=m.ka,n=m.ka;m.Z&&(m.Z!==l?(!(g=!m.Z.La())&&(g=e)&&(g=m.Z,g=e[g.h.T+"."+g.s.m]),h=[],k=null,
l=m.Z):1E-8<=k&&(n/=k));if(b&&!b(m))h&&h.push(m);else{b&&d.unshift(m);k=q;if(1E-8>n)q=m.Z.s;else if(.99999999<n)q=m.Z.M;else{q=m.Z.Gb(n,!0);g&&f.push(m.Z,q);q=q.s;for(var v=h.length-1;0<=v;v--){var y=h[v];y.ka=(y.ka-n)/(1-n)}}m.vc(q);n=q.v;m=m.v;if(n)for(u.ad(n,m),m=n;m;)u.ad(m.v,n),m=m.Ca;else q.v=m}}c||u.Bc(f);return d||a};u.Rb=function(a,b,c,d,f){function e(R){var S=R[l+0],ea=R[l+2],T=R[l+4],aa=R[l+6];if(m<=Math.max(S,ea,T,aa)&&m>=Math.min(S,ea,T,aa))for(S=R[h+0],ea=R[h+2],T=R[h+4],aa=R[h+6],R=
q>Math.max(S,ea,T,aa)||n<Math.min(S,ea,T,aa)?[R]:r.Pc(R,c),S=0,ea=R.length;S<ea;S++){a:{T=R[S];aa=T[l+0];var Z=T[l+6];if(!(m<Math.min(aa,Z)||m>Math.max(aa,Z))){var ia=T[h+0],ha=T[h+2],ka=T[h+4],X=T[h+6];if(aa===Z){if(ia<n&&X>q||X<n&&ia>q)E=!0}else{ha=m===aa?0:m===Z?1:q>Math.max(ia,ha,ka,X)||n<Math.min(ia,ha,ka,X)?1:0<r.Oa(T,l,m,U,0,1)?U[0]:1;X=0===ha?ia:1===ha?X:c?r.S(T,ha).y:r.S(T,ha).x;Z=aa>Z?1:-1;ka=Q[l]>Q[l+6]?1:-1;var ma=Q[h+6];m!==aa?(X<q?y+=Z:X>n?A+=Z:E=!0,X>p-1E-6&&X<p+1E-6&&(P/=2)):(Z!==
ka?ia<q?y+=Z:ia>n&&(A+=Z):ia!=ma&&(ma<n&&X>n?(A+=Z,E=!0):ma>q&&X<q&&(y+=Z,E=!0)),P/=4);Q=T;T=!f&&X>q&&X<n&&(c?0===r.Aa(T,ha).x:0===r.Aa(T,ha).y)&&u.Rb(a,b,!c,d,!0);break a}}T=void 0}if(T)return T}}for(var g=Array.isArray(b)?b:c?b.Tb:b.fc,h=c?1:0,l=h^1,k=[a.x,a.y],p=k[h],m=k[l],q=p-1E-9,n=p+1E-9,v=k=0,y=0,A=0,E=!1,G=!1,P=1,U=[],Q,J,K=0,O=g.length;K<O;K++){var L=g[K],W=L.h,ca=L.o();if(!K||g[K-1].h!==W)if(Q=null,W.F||(J=r.o(W.ub().M,L.s,null,!d),J[l]!==J[l+6]&&(Q=J)),!Q){Q=ca;for(var Y=W.ub();Y&&Y!==
L;){var da=Y.o();if(da[l]!==da[l+6]){Q=da;break}Y=Y.za()}}L=void 0;if(L=e(ca))return L;if(K+1===O||g[K+1].h!==W){if(J&&(L=e(J)))return L;!E||y||A||(y=A=W.Ma()^c?1:-1);k+=y;v+=A;y=A=0;E&&(G=!0,E=!1);J=null}}k=Math.abs(k);v=Math.abs(v);return{fa:Math.max(k,v),re:k,se:v,quality:P,he:G}};u.ed=function(a,b,c,d,f){var e=[],g=a,h=0,l={fa:0,quality:-1};do{var k=a.P();if(k){var p=k.N();e.push({le:a,curve:k,length:p});h+=p}a=a.ba()}while(a&&!a.v&&a!==g);a=[.5,.25,.75];for(g=0;g<a.length&&.5>l.quality;g++){k=
h*a[g];p=0;for(var m=e.length;p<m;p++){var q=e[p],n=q.length;if(k<=n){q=q.curve;n=q.h;var v=n.wa;v=v instanceof x?v:n;var y=H.Dd(q.Ia(k),.001,.999),A=q.Rc(y);y=Math.abs(q.Pb(y).y)<Math.SQRT1_2;var E=null;if(f.subtract&&c){var G=(v===b?c:b).pc(A,y,!0);if(v===b&&G.fa||v===c&&!G.fa)if(1>G.quality)continue;else E={fa:0,quality:1}}E=E||u.Rb(A,d[n.T][q.sa()],y,!0);E.quality>l.quality&&(l=E);break}k-=n}}for(b=e.length-1;0<=b;b--)e[b].le.yc=l};u.nd=function(a,b){function c(J){var K;return!(!J||J.mb||b&&(!b[(K=
J.yc||{}).fa]||b.unite&&2===K.fa&&K.re&&K.se))}function d(J){if(J)for(var K=0,O=h.length;K<O;K++)if(J===h[K])return!0;return!1}function f(J){for(var K=0,O=J.i.length;K<O;K++)J.i[K].mb=!0}function e(J,K){function O(Y,da){for(;Y&&Y!==da;){var R=Y.ja,S=R&&R.h;if(S){S=R.ba()||S.ra();var ea=S.v;S=d(R)||d(S)||S&&c(R)&&(c(S)||ea&&c(ea.ja));R!==J&&S&&ca.push(R);K&&h.push(R)}Y=Y.Ca}}var L=J.v,W=L,ca=[];K&&(h=[J]);if(L){for(O(L);L&&L.Ra;)L=L.Ra;O(L,W)}return ca}var g=[],h;a.sort(function(J,K){var O=J.v,L=K.v,
W=O?O.va:!1;return W^(L?L.va:0)?W?1:-1:!O^!L?O?1:-1:J.h!==K.h?J.h.T-K.h.T:J.m-K.m});for(var l=0;l<a.length;l++){var k=a[l],p=c(k);if(p&&k.h.tc){var m=k.h,q=k.v.ja.h;m.compare(q)&&(m.aa()&&g.push(m.clone(!1)),f(m),f(q),p=!1)}m=void 0;q=[];for(var n=null,v=!1,y=!0,A=void 0,E=void 0;p;){var G=!n,P=e(k,G),U=P.shift();v=!G&&(d(k)||d(U));var Q=!v&&U;G&&(n=new t({ca:!1}),A=null);if(v){if(k.Vb()||k.Yb())y=k.h.F;k.mb=!0;break}Q&&A&&(q.push(A),A=null);A||(Q&&P.push(k),A={start:n.i.length,Ed:P,qe:m=[],Uc:E});
Q&&(k=U);if(!c(k)){n.bc(A.start);m.forEach(function(J){J.mb=!1});m.length=0;do if(k=A&&A.Ed.shift(),!k||!k.h)if(k=null,A=q.pop())m=A.qe,E=A.Uc;while(A&&!c(k));if(!k)break}G=k.ba();n.add(new D(k.j,E,G&&k.H));k.mb=!0;m.push(k);k=G||k.h.ra();E=G&&G.A}v&&y&&(n.ra().zb(E),n.Wa(y));v&&0!==n.aa()&&g.push(n)}return g};ba(t,u);t.prototype.lc=function(a){return this.F===a.F&&I.L(this.i,a.i)};t.prototype.Ec=function(a){this.dc(a.i);this.F=a.F};t.prototype.l=function(a){B.prototype.l.call(this,a);if(a&8)if(this.Ba=
this.Cb=void 0,a&32)this.Za++;else{if(this.O){a=0;for(var b=this.O.length;a<b;a++)this.O[a].l()}}else a&64&&(this.Y=void 0)};t.prototype.Yd=function(){return this.i};t.prototype.dc=function(a){this.i.length=0;this.O=void 0;var b=a&&a.length;if(b){var c=a[b-1];"boolean"===typeof c&&(this.Wa(c),b--);this.X(D.$b(a,0,{},b))}};t.prototype.ra=function(){return this.i[0]};t.prototype.Ga=function(){return this.i[this.i.length-1]};t.prototype.U=function(){var a=this.O,b=this.i;if(!a){var c=this.Eb();a=this.O=
Array(c);for(var d=0;d<c;d++)a[d]=new r(this,b[d],b[d+1]||b[0])}return a};t.prototype.ub=function(){var a=this.U();return a[a.length-1]};t.prototype.Yc=function(){return this.F};t.prototype.Wa=function(a){if(this.F!=(a=!!a)){this.F=a;if(this.O){var b=this.O.length=this.Eb();a&&(this.O[b-1]=new r(this,this.i[b-1],this.i[0]))}this.l(41)}};t.prototype.ha=function(){return!this.i.length};t.prototype.wc=function(a){for(var b=this.i,c=Array(6),d=0,f=b.length;d<f;d++)b[d].la(a,c,!0);return!0};t.prototype.X=
function(a,b){var c=this.i,d=this.O,f=a.length,e=null==b;b=e?c.length:b;for(var g=0;g<f;g++){var h=a[g];h.h&&(h=a[g]=h.clone());h.h=this;h.m=b+g}if(e)c.push.apply(c,a);else for(c.splice.apply(c,[b,0].concat(a)),e=b+f,g=c.length;e<g;e++)c[e].m=e;if(d){c=this.Eb();e=b=0<b&&b+f-1===c?b-1:b;f=Math.min(b+f,c);a.O&&(d.splice.apply(d,[b,0].concat(a.O)),e+=a.O.length);for(c=e;c<f;c++)d.splice(c,0,new r(this,null,null));this.ic(b,f)}this.l(41);return a};t.prototype.ic=function(a,b){for(var c=this.i,d=this.O,
f,e=a;e<b;e++)f=d[e],f.h=this,f.s=c[e],f.M=c[e+1]||c[0],f.l();if(f=d[this.F&&!a?c.length-1:a-1])f.M=c[a]||c[0],f.l();if(f=d[b])f.s=c[b],f.l()};t.prototype.Eb=function(){var a=this.i.length;return!this.F&&0<a?a-1:a};t.prototype.add=function(a){return 1<arguments.length&&"number"!==typeof a?this.X(D.$b(arguments)):this.X([D.read(arguments)])[0]};t.prototype.ca=function(a,b){return 2<arguments.length&&"number"!==typeof b?this.X(D.$b(arguments,1),a):this.X([D.read(arguments,1)],a)[0]};t.prototype.gd=
function(a){return this.bc(a,a+1)[0]||null};t.prototype.bc=function(a,b){null==a&&(a=0);null==b&&(b=this.i.length);var c=this.i,d=this.O,f=c.length,e=c.splice(a,b-a),g=e.length;if(!g)return e;for(var h=0;h<g;h++)e[h].m=e[h].h=null;h=a;for(var l=c.length;h<l;h++)c[h].m=h;if(d){a=0<a&&b===f+(this.F?1:0)?a-1:a;for(d=d.splice(a,g).length-1;0<=d;d--);this.ic(a,a)}this.l(41);return e};t.prototype.La=function(){for(var a=0,b=this.i.length;a<b;a++)if(this.i[a].La())return!0;return!1};t.prototype.pb=function(){for(var a=
0,b=this.i.length;a<b;a++)this.i[a].pb()};t.prototype.N=function(){if(null==this.Ba){for(var a=this.U(),b=0,c=0,d=a.length;c<d;c++)b+=a[c].N();this.Ba=b}return this.Ba};t.prototype.aa=function(){if(null!=this.Cb)return this.Cb;for(var a=0,b=this.i.length,c=0;c<b;c++){var d=(c+1)%b,f=c===b-1;a+=r.aa(r.o(this.i[c],this.i[f?0:d],null,f&&!this.F))}return this.Cb=a};t.prototype.join=function(a,b){b=b||0;if(a&&a!==this){var c=this.Ga(),d=a.Ga();if(!d)return this;c&&c.j.da(d.j,b)&&a.reverse();d=a.ra();c&&
c.j.da(d.j,b)?(c.cc(d.H),this.X(a.i.slice(1))):((c=this.ra())&&c.j.da(d.j,b)&&a.reverse(),d=a.Ga(),c&&c.j.da(d.j,b)?(c.zb(d.A),this.X(a.i.slice(0,a.i.length-1),0)):this.X(a.i.slice()));a.F&&this.X([a.i[0]]);a.remove()}a=this.ra();c=this.Ga();a!==c&&a.j.da(c.j,b)&&(a.zb(c.A),c.remove(),this.Wa(!0));return this};t.prototype.reduce=function(a){for(var b=this.U(),c=(a=a&&a.md)?1E-7:0,d=b.length-1;0<=d;d--){var f=b[d];!f.La()&&(!f.Vc(c)||a&&f.ea(f.ba()))&&f.remove()}return this};t.prototype.reverse=function(){this.i.reverse();
for(var a=0,b=this.i.length;a<b;a++){var c=this.i[a],d=c.A;c.A=c.H;c.H=d;c.m=a}this.O=null;this.l(9)};t.prototype.compare=function(a){if(!a||a instanceof x)return u.prototype.compare.call(this,a);var b=this.U();a=a.U();if(!b.length||!a.length)return b.length==a.length;for(var c=b[0].o(),d=[],f=0,e,g=0,h,l=0;l<a.length;l++){var k=a[l].o();d.push(k);if(k=r.Lb(c,k)){e=!l&&0<k[0][0]?a.length-1:l;h=k[0][1];break}}l=d[e];for(var p;c&&l;){if((k=r.Lb(c,l))&&1E-8>Math.abs(k[0][0]-g)){g=k[1][0];1===g&&(c=++f<
b.length?b[f].o():null,g=0);var m=k[0][1];if(1E-8>Math.abs(m-h)){p||(p=[e,m]);h=k[1][1];1===h&&(++e>=a.length&&(e=0),l=d[e]||a[e].o(),h=0);if(!c)return p[0]===e&&p[1]===h;continue}}break}return!1};t.prototype.wb=function(a){if("number"===typeof a){for(var b=this.U(),c=0,d=0,f=b.length;d<f;d++){var e=c,g=b[d];c+=g.N();if(c>a)return g.wb(a-e)}if(0<b.length&&a<=this.N())return new C(b[b.length-1],1)}else if(a&&a.Ha&&a.Ha()===this)return a;return null};t.prototype.fb=function(a){return(a=this.wb(a))&&
a.S()};t.prototype.Tc=function(a){return(a=this.wb(a))&&a.Aa()};t.prototype.moveTo=function(){1===this.i.length&&this.gd(0);this.i.length||this.X([new D(w.read(arguments))])};t.prototype.lineTo=function(){this.X([new D(w.read(arguments))])};t.prototype.rb=function(){var a=w.read(arguments),b=w.read(arguments),c=w.read(arguments),d=t.Kc(this);d.cc(a.R(d.j));this.X([new D(c,b.R(c))])};t.prototype.quadraticCurveTo=function(){var a=w.read(arguments),b=w.read(arguments),c=t.Kc(this).j;this.rb(a.add(c.R(a).multiply(1/
3)),a.add(b.R(a).multiply(1/3)),b)};t.prototype.closePath=function(a){this.Wa(!0);this.join(this,a)};t.prototype.jb=function(a,b){return b.handle?t.Qd(this.i,a):t.W(this.i,this.F,this,a,b)};t.Kc=function(a){a=a.i;if(!a.length)throw Error("Use a moveTo() command first");return a[a.length-1]};t.W=function(a,b,c,d){function f(q){q.la(d,e);for(q=0;2>q;q++)r.hc(g[q],g[q+4],e[q+2],e[q],q,h,l,k);q=g;g=e;e=q}c=a[0];if(!c)return new F;for(var e=Array(6),g=c.la(d,Array(6)),h=g.slice(0,2),l=h.slice(),k=Array(2),
p=1,m=a.length;p<m;p++)f(a[p]);b&&f(c);return new F(h[0],h[1],l[0]-h[0],l[1]-h[1])};t.Qd=function(a,b){for(var c=Array(6),d=Infinity,f=-d,e=d,g=f,h=0,l=a.length;h<l;h++){a[h].la(b,c);for(var k=0;6>k;k+=2){var p=c[k],m=c[k+1];p<d&&(d=p);p>f&&(f=p);m<e&&(e=m);m>g&&(g=m)}}return new F(d,e,f-d,g-e)};t.qd=function(a,b,c,d){var f=new t;f.moveTo(a,b);f.lineTo(c,b);f.lineTo(c,d);f.lineTo(a,d);f.closePath();return f};ba(x,u);x.prototype.gb=function(a,b){var c=b,d=c[0];d&&"number"===typeof d[0]&&(c=[c]);for(d=
b.length-1;0<=d;d--){var f=c[d];c!==b||f instanceof t||(c=I.slice(c));Array.isArray(f)?c[d]=new t({Fe:f,ca:!1}):f instanceof x&&(c.splice.apply(c,[d,1].concat(f.ac())),f.remove())}return B.prototype.gb.call(this,a,c)};x.prototype.reduce=function(a){for(var b=this.g.length-1;0<=b;b--){var c=this.g[b].reduce(a);c.ha()&&c.remove()}return this.g.length?B.prototype.reduce.call(this):(a=new t({ca:!1}),a.$a(this),a.Ub(this),this.remove(),a)};x.prototype.Yc=function(){for(var a=0,b=this.g.length;a<b;a++)if(!this.g[a].F)return!1;
return!0};x.prototype.Wa=function(a){for(var b=0,c=this.g.length;b<c;b++)this.g[b].Wa(a)};x.prototype.ra=function(){var a=this.Pd();return a&&a.ra()};x.prototype.Ga=function(){var a=this.Nc();return a&&a.Ga()};x.prototype.U=function(){for(var a=[],b=0,c=this.g.length;b<c;b++)a.push.apply(a,this.g[b].U());return a};x.prototype.ub=function(){var a=this.Nc();return a&&a.ub()};x.prototype.aa=function(){for(var a=0,b=0,c=this.g.length;b<c;b++)a+=this.g[b].aa();return a};x.prototype.ha=function(){for(var a=
!0,b=0,c=this.g.length;b<c;b++)a*=this.g[b].ha();return a};x.prototype.N=function(){for(var a=0,b=0,c=this.g.length;b<c;b++)a+=this.g[b].N();return a};x.prototype.moveTo=function(){var a=x.bb(this),b=a&&a.ha()?a:new t({ca:!1});b!==a&&this.zc(b);b.moveTo.apply(b,arguments)};x.prototype.closePath=function(a){x.bb(this,!0).closePath(a)};x.prototype.lineTo=function(){var a=x.bb(this,!0);a.lineTo.apply(a,arguments)};x.prototype.rb=function(){var a=x.bb(this,!0);a.rb.apply(a,arguments)};x.prototype.quadraticCurveTo=
function(){var a=x.bb(this,!0);a.quadraticCurveTo.apply(a,arguments)};x.prototype.reverse=function(a){for(var b,c=0,d=this.g.length;c<d;c++)b=this.g[c].reverse(a)||b;return b};x.bb=function(a,b){if(b&&!a.g.length)throw Error("Use a moveTo() command first");return a.g[a.g.length-1]};z.AscCommon=z.AscCommon||{};z.AscCommon.PathBoolean={};z.AscCommon.PathBoolean.CompoundPath=x;x.prototype.divide=u.prototype.ab;x.prototype.unite=u.prototype.od;x.prototype.intersect=u.prototype.ga;x.prototype.subtract=
u.prototype.R;x.prototype.exclude=u.prototype.Ib;t.prototype.divide=u.prototype.ab;t.prototype.unite=u.prototype.od;t.prototype.intersect=u.prototype.ga;t.prototype.subtract=u.prototype.R;t.prototype.exclude=u.prototype.Ib;x.prototype.moveTo=x.prototype.moveTo;x.prototype.lineTo=x.prototype.lineTo;x.prototype.cubicCurveTo=x.prototype.rb;x.prototype.closePath=x.prototype.closePath;x.prototype.getChildren=B.prototype.Jb;x.prototype.getBounds=B.prototype.W;x.prototype.getPosition=B.prototype.Ob;x.prototype.setPosition=
B.prototype.setPosition;t.prototype.getSegments=t.prototype.Yd;t.prototype.isClosed=t.prototype.Yc;t.prototype.getBounds=B.prototype.W;t.prototype.getPosition=B.prototype.Ob;t.prototype.setPosition=B.prototype.setPosition;D.prototype.isFirst=D.prototype.Vb;D.prototype.isLast=D.prototype.Yb;D.prototype.getPrevious=D.prototype.za;D.prototype.getNext=D.prototype.ba;D.prototype.getPoint=D.prototype.S;D.prototype.getHandleOut=D.prototype.Sd;D.prototype.getHandleIn=D.prototype.Rd;F.prototype.getTopLeft=
F.prototype.Zd;F.prototype.getWidth=F.prototype.Qb;F.prototype.getHeight=F.prototype.Kb;F.prototype.getLeft=F.prototype.Ua;F.prototype.getTop=F.prototype.Va;w.prototype.subtract=w.prototype.R;w.prototype.getX=w.prototype.Ja;w.prototype.getY=w.prototype.Ka})(window);
