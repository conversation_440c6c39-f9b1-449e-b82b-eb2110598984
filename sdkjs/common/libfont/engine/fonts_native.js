/*
 * Copyright (C) Ascensio System SIA 2012-2025. All rights reserved
 *
 * https://www.onlyoffice.com/
 *
 * Version: 0.0.0 (build:0)
 */

(function(window,undefined){window["AscFonts"]=window["AscFonts"]||{};var AscFonts=window["AscFonts"];var g_native_engine=null;function CReturnObject(){this.error=0}CReturnObject.prototype.free=function(){};var g_return_obj=new CReturnObject;var g_return_obj_count=new CReturnObject;g_return_obj_count.count=0;AscFonts.CopyStreamToMemory=function(data,size){return data};AscFonts.GetUint8ArrayFromPointer=function(pointer,size){return pointer};function CShapeString(size){this.size=size;this.pointer=new Uint8Array(size)}
CShapeString.prototype.getBuffer=function(){return this.pointer};CShapeString.prototype.free=function(){};CShapeString.prototype.set=function(index,value){this.pointer[index]=value};AscFonts.AllocString=function(size){return new CShapeString(size)};AscFonts.FT_CreateLibrary=function(library){return g_native_engine["FT_Init"](library)};AscFonts.FT_Done_Library=function(face){if(face)g_native_engine.FT_Free(face)};AscFonts.FT_Set_TrueType_HintProp=function(library,tt_interpreter){return g_native_engine["FT_Set_TrueType_HintProp"](library,
tt_interpreter)};AscFonts.FT_Open_Face=function(library,memory,len,face_index){return g_native_engine["FT_Open_Face2"](library,memory,face_index)};AscFonts.FT_Done_Face=function(face){if(face)g_native_engine["FT_Free"](face)};AscFonts.FT_SetCMapForCharCode=function(face,unicode){return g_native_engine["FT_SetCMapForCharCode"](face,unicode)};AscFonts.FT_GetKerningX=function(face,gid1,gid2){return g_native_engine["FT_GetKerningX"](face,gid1,gid2)};AscFonts.FT_GetFaceMaxAdvanceX=function(face){return g_native_engine["FT_GetFaceMaxAdvanceX"](face)};
AscFonts.FT_Set_Transform=function(face,xx,yx,xy,yy){return g_native_engine["FT_Set_Transform"](face,xx,yx,xy,yy)};AscFonts.FT_Set_Char_Size=function(face,cw,ch,hres,vres){return g_native_engine["FT_Set_Char_Size"](face,cw,ch,hres,vres)};AscFonts.FT_GetFaceInfo=function(face,reader){var data=g_native_engine["FT_GetFaceInfo"](face);if(!data){g_return_obj.error=1;return g_return_obj}g_return_obj.error=0;reader.init(data,0,data.length);return g_return_obj};AscFonts.FT_Load_Glyph=function(face,gid,mode){return g_native_engine["FT_Load_Glyph"](face,
gid,mode)};AscFonts.FT_SetCMapForCharCode=function(face,unicode){return g_native_engine["FT_SetCMapForCharCode"](face,unicode)};AscFonts.FT_Get_Glyph_Measure_Params=function(face,vector_worker,reader){var data=g_native_engine["FT_Get_Glyph_Measure_Params"](face,vector_worker?true:false);if(!data){g_return_obj_count.error=1;return g_return_obj_count}reader.init(new Uint8Array(data,0,data.length));g_return_obj_count.count=reader.readInt();g_return_obj_count.error=0;return g_return_obj_count};AscFonts.FT_Get_Glyph_Render_Params=
function(face,render_mode,reader){var data=g_native_engine["FT_Get_Glyph_Render_Params"](face,render_mode);if(!data){g_return_obj.error=1;return g_return_obj}g_return_obj.error=0;reader.init(data,0,data.length);return g_return_obj};AscFonts.FT_Get_Glyph_Render_Buffer=function(face,size){return g_native_engine["FT_Get_Glyph_Render_Buffer"](face,size)};var hb_cache_languages={};AscFonts.HB_FontFree=function(font){if(font)g_native_engine["FT_Free"](font)};AscFonts.HB_ShapeText=function(fontFile,text,
features,script,direction,language,reader){if(!hb_cache_languages[language])hb_cache_languages[language]=g_native_engine["HB_LanguageFromString"]();if(!fontFile["GetHBFont"]())fontFile["SetHBFont"](g_native_engine["HB_FontMalloc"]());var data=g_native_engine["HB_ShapeText"](fontFile["GetFace"](),fontFile["GetHBFont"](),text.pointer,features,script,direction,hb_cache_languages[language]);if(!data){g_return_obj_count.error=1;return g_return_obj_count}reader.init(new Uint8Array(data,0,data.length));
var len=reader.readUInt();var fontPointer=reader.readPointer64();g_return_obj_count.count=(len-12)/26;g_return_obj_count.error=0;return g_return_obj_count};AscFonts.Hyphen_Init=function(){};AscFonts.Hyphen_Destroy=function(){};AscFonts.Hyphen_CheckDictionary=function(lang){return g_native_engine["Hyphen_IsDictionaryExist"](lang)};AscFonts.Hyphen_LoadDictionary=function(lang,data){return false};AscFonts.Hyphen_Word=function(lang,word){var ret=g_native_engine["Hyphen_Word"](lang,word);return ret?ret:
[]};AscFonts.onLoadModule();AscFonts.onLoadModule();window["InitNativeTextMeasurer"]=function(){g_native_engine=CreateEmbedObject("CTextMeasurerEmbed")}})(window,undefined);
