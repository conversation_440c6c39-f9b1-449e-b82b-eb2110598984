/*
 * Copyright (C) Ascensio System SIA 2012-2025. All rights reserved
 *
 * https://www.onlyoffice.com/
 *
 * Version: 0.0.0 (build:0)
 */

(function(window,undefined){var AscFonts=window["AscFonts"];if(window["NATIVE_EDITOR_ENJINE"]){var immediateArray=[];window.setImmediate=function(fn){if(immediateArray)immediateArray.push(fn);else fn()};window.immediateRun=function(){for(var i=0;i<immediateArray.length;i++)immediateArray[i]();immediateArray=undefined}}var setImmediate=window.setImmediate;var printErr=undefined;var print=undefined;var fetch="undefined"!==typeof window?window.fetch:"undefined"!==typeof self?self.fetch:null;var getBinaryPromise=
null;function internal_isLocal(){if(window.navigator&&window.navigator.userAgent.toLowerCase().indexOf("ascdesktopeditor")<0)return false;if(window.location&&window.location.protocol=="file:")return true;if(window.document&&window.document.currentScript&&0==window.document.currentScript.src.indexOf("file:///"))return true;return false}if(internal_isLocal()){fetch=undefined;getBinaryPromise=function(){var wasmPath="ascdesktop://fonts/"+wasmBinaryFile.substr(8);return new Promise(function(resolve,reject){var xhr=
new XMLHttpRequest;xhr.open("GET",wasmPath,true);xhr.responseType="arraybuffer";if(xhr.overrideMimeType)xhr.overrideMimeType("text/plain; charset=x-user-defined");else xhr.setRequestHeader("Accept-Charset","x-user-defined");xhr.onload=function(){if(this.status==200)resolve(new Uint8Array(this.response))};xhr.send(null)})}}else getBinaryPromise=function(){return getBinaryPromise2.apply(undefined,arguments)};(function(){if(undefined!==String.prototype.fromUtf8&&undefined!==String.prototype.toUtf8)return;
var STRING_UTF8_BUFFER_LENGTH=1024;var STRING_UTF8_BUFFER=new ArrayBuffer(STRING_UTF8_BUFFER_LENGTH);String.prototype.fromUtf8=function(buffer,start,len){if(undefined===start)start=0;if(undefined===len)len=buffer.length-start;var result="";var index=start;var end=start+len;while(index<end){var u0=buffer[index++];if(!(u0&128)){result+=String.fromCharCode(u0);continue}var u1=buffer[index++]&63;if((u0&224)==192){result+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=buffer[index++]&63;if((u0&240)==
224)u0=(u0&15)<<12|u1<<6|u2;else u0=(u0&7)<<18|u1<<12|u2<<6|buffer[index++]&63;if(u0<65536)result+=String.fromCharCode(u0);else{var ch=u0-65536;result+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return result};String.prototype.toUtf8=function(isNoEndNull,isUseBuffer){var inputLen=this.length;var testLen=6*inputLen+1;var tmpStrings=isUseBuffer&&testLen<STRING_UTF8_BUFFER_LENGTH?STRING_UTF8_BUFFER:new ArrayBuffer(testLen);var code=0;var index=0;var outputIndex=0;var outputDataTmp=new Uint8Array(tmpStrings);
var outputData=outputDataTmp;while(index<inputLen){code=this.charCodeAt(index++);if(code>=55296&&code<=57343&&index<inputLen)code=65536+((code&1023)<<10|1023&this.charCodeAt(index++));if(code<128)outputData[outputIndex++]=code;else if(code<2048){outputData[outputIndex++]=192|code>>6;outputData[outputIndex++]=128|code&63}else if(code<65536){outputData[outputIndex++]=224|code>>12;outputData[outputIndex++]=128|code>>6&63;outputData[outputIndex++]=128|code&63}else if(code<2097151){outputData[outputIndex++]=
240|code>>18;outputData[outputIndex++]=128|code>>12&63;outputData[outputIndex++]=128|code>>6&63;outputData[outputIndex++]=128|code&63}else if(code<67108863){outputData[outputIndex++]=248|code>>24;outputData[outputIndex++]=128|code>>18&63;outputData[outputIndex++]=128|code>>12&63;outputData[outputIndex++]=128|code>>6&63;outputData[outputIndex++]=128|code&63}else if(code<2147483647){outputData[outputIndex++]=252|code>>30;outputData[outputIndex++]=128|code>>24&63;outputData[outputIndex++]=128|code>>
18&63;outputData[outputIndex++]=128|code>>12&63;outputData[outputIndex++]=128|code>>6&63;outputData[outputIndex++]=128|code&63}}if(isNoEndNull!==true)outputData[outputIndex++]=0;return new Uint8Array(tmpStrings,0,outputIndex)};function StringPointer(pointer,len){this.ptr=pointer;this.length=len}StringPointer.prototype.free=function(){if(0!==this.ptr)Module["_free"](this.ptr)};String.prototype.toUtf8Pointer=function(isNoEndNull){var tmp=this.toUtf8(isNoEndNull,true);var pointer=Module["_malloc"](tmp.length);
if(0==pointer)return null;Module["HEAP8"].set(tmp,pointer);return new StringPointer(pointer,tmp.length)}})();var Module=typeof Module!="undefined"?Module:{};var moduleOverrides=Object.assign({},Module);var arguments_=[];var thisProgram="./this.program";var quit_=function(status,toThrow){throw toThrow;};var ENVIRONMENT_IS_WEB=true;var ENVIRONMENT_IS_WORKER=false;var scriptDirectory="";function locateFile(path){if(Module["locateFile"])return Module["locateFile"](path,scriptDirectory);return scriptDirectory+
path}var read_,readAsync,readBinary;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER)scriptDirectory=self.location.href;else if(typeof document!="undefined"&&document.currentScript)scriptDirectory=document.currentScript.src;if(scriptDirectory.indexOf("blob:")!==0)scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1);else scriptDirectory="";{read_=function(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};
if(ENVIRONMENT_IS_WORKER)readBinary=function(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)};readAsync=function(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=function(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}}else;var out=Module["print"]||console.log.bind(console);
var err=Module["printErr"]||console.error.bind(console);Object.assign(Module,moduleOverrides);moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=Module["thisProgram"];if(Module["quit"])quit_=Module["quit"];var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];if(typeof WebAssembly!="object")abort("no native wasm support detected");var wasmMemory;var ABORT=false;var EXITSTATUS;var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,
HEAPF32,HEAPF64;function updateMemoryViews(){var b=wasmMemory.buffer;Module["HEAP8"]=HEAP8=new Int8Array(b);Module["HEAP16"]=HEAP16=new Int16Array(b);Module["HEAPU8"]=HEAPU8=new Uint8Array(b);Module["HEAPU16"]=HEAPU16=new Uint16Array(b);Module["HEAP32"]=HEAP32=new Int32Array(b);Module["HEAPU32"]=HEAPU32=new Uint32Array(b);Module["HEAPF32"]=HEAPF32=new Float32Array(b);Module["HEAPF64"]=HEAPF64=new Float64Array(b)}var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[function(){window["AscFonts"].onLoadModule()}];
var runtimeInitialized=false;function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length)addOnPreRun(Module["preRun"].shift())}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length)addOnPostRun(Module["postRun"].shift())}callRuntimeCallbacks(__ATPOSTRUN__)}
function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"])Module["monitorRunDependencies"](runDependencies)}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"])Module["monitorRunDependencies"](runDependencies);
if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){if(Module["onAbort"])Module["onAbort"](what);what="Aborted("+what+")";err(what);ABORT=true;EXITSTATUS=1;what+=". Build with -sASSERTIONS for more info.";var e=new WebAssembly.RuntimeError(what);throw e;}var dataURIPrefix="data:application/octet-stream;base64,";var isDataURI=
function(filename){return filename.startsWith(dataURIPrefix)};var wasmBinaryFile;wasmBinaryFile="fonts.wasm";if(!isDataURI(wasmBinaryFile))wasmBinaryFile=locateFile(wasmBinaryFile);function getBinarySync(file){if(file==wasmBinaryFile&&wasmBinary)return new Uint8Array(wasmBinary);if(readBinary)return readBinary(file);throw"both async and sync fetching of the wasm failed";}function getBinaryPromise2(binaryFile){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER))if(typeof fetch=="function")return fetch(binaryFile,
{credentials:"same-origin"}).then(function(response){if(!response["ok"])throw"failed to load wasm binary file at '"+binaryFile+"'";return response["arrayBuffer"]()}).catch(function(){return getBinarySync(binaryFile)});return Promise.resolve().then(function(){return getBinarySync(binaryFile)})}function instantiateArrayBuffer(binaryFile,imports,receiver){return getBinaryPromise(binaryFile).then(function(binary){return WebAssembly.instantiate(binary,imports)}).then(function(instance){return instance}).then(receiver,
function(reason){err("failed to asynchronously prepare wasm: "+reason);abort(reason)})}function instantiateAsync(binary,binaryFile,imports,callback){if(!binary&&typeof WebAssembly.instantiateStreaming=="function"&&!isDataURI(binaryFile)&&typeof fetch=="function")return fetch(binaryFile,{credentials:"same-origin"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,imports);return result.then(callback,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");
return instantiateArrayBuffer(binaryFile,imports,callback)})});return instantiateArrayBuffer(binaryFile,imports,callback)}function createWasm(){var info={"a":wasmImports};function receiveInstance(instance,module){wasmExports=instance.exports;wasmMemory=wasmExports["Ca"];updateMemoryViews();wasmTable=wasmExports["Ea"];addOnInit(wasmExports["Da"]);removeRunDependency("wasm-instantiate");return wasmExports}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){receiveInstance(result["instance"])}
if(Module["instantiateWasm"])try{return Module["instantiateWasm"](info,receiveInstance)}catch(e){err("Module.instantiateWasm callback failed with error: "+e);return false}instantiateAsync(wasmBinary,wasmBinaryFile,info,receiveInstantiationResult);return{}}function ExitStatus(status){this.name="ExitStatus";this.message="Program terminated with exit("+status+")";this.status=status}var callRuntimeCallbacks=function(callbacks){while(callbacks.length>0)callbacks.shift()(Module)};var noExitRuntime=Module["noExitRuntime"]||
true;var exceptionCaught=[];var uncaughtExceptionCount=0;var ___cxa_begin_catch=function(ptr){var info=new ExceptionInfo(ptr);if(!info.get_caught()){info.set_caught(true);uncaughtExceptionCount--}info.set_rethrown(false);exceptionCaught.push(info);___cxa_increment_exception_refcount(info.excPtr);return info.get_exception_ptr()};var exceptionLast=0;var ___cxa_end_catch=function(){_setThrew(0,0);var info=exceptionCaught.pop();___cxa_decrement_exception_refcount(info.excPtr);exceptionLast=0};function ExceptionInfo(excPtr){this.excPtr=
excPtr;this.ptr=excPtr-24;this.set_type=function(type){HEAPU32[this.ptr+4>>2]=type};this.get_type=function(){return HEAPU32[this.ptr+4>>2]};this.set_destructor=function(destructor){HEAPU32[this.ptr+8>>2]=destructor};this.get_destructor=function(){return HEAPU32[this.ptr+8>>2]};this.set_caught=function(caught){caught=caught?1:0;HEAP8[this.ptr+12>>0]=caught};this.get_caught=function(){return HEAP8[this.ptr+12>>0]!=0};this.set_rethrown=function(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13>>0]=rethrown};
this.get_rethrown=function(){return HEAP8[this.ptr+13>>0]!=0};this.init=function(type,destructor){this.set_adjusted_ptr(0);this.set_type(type);this.set_destructor(destructor)};this.set_adjusted_ptr=function(adjustedPtr){HEAPU32[this.ptr+16>>2]=adjustedPtr};this.get_adjusted_ptr=function(){return HEAPU32[this.ptr+16>>2]};this.get_exception_ptr=function(){var isPointer=___cxa_is_pointer_type(this.get_type());if(isPointer)return HEAPU32[this.excPtr>>2];var adjusted=this.get_adjusted_ptr();if(adjusted!==
0)return adjusted;return this.excPtr}}var ___resumeException=function(ptr){if(!exceptionLast)exceptionLast=ptr;throw exceptionLast;};var findMatchingCatch=function(args){var thrown=exceptionLast;if(!thrown){setTempRet0(0);return 0}var info=new ExceptionInfo(thrown);info.set_adjusted_ptr(thrown);var thrownType=info.get_type();if(!thrownType){setTempRet0(0);return thrown}for(var arg in args){var caughtType=args[arg];if(caughtType===0||caughtType===thrownType)break;var adjusted_ptr_addr=info.ptr+16;
if(___cxa_can_catch(caughtType,thrownType,adjusted_ptr_addr)){setTempRet0(caughtType);return thrown}}setTempRet0(thrownType);return thrown};var ___cxa_find_matching_catch_2=function(){return findMatchingCatch([])};var ___cxa_find_matching_catch_3=function(arg0){return findMatchingCatch([arg0])};var ___cxa_rethrow=function(){var info=exceptionCaught.pop();if(!info)abort("no exception to throw");var ptr=info.excPtr;if(!info.get_rethrown()){exceptionCaught.push(info);info.set_rethrown(true);info.set_caught(false);
uncaughtExceptionCount++}exceptionLast=ptr;throw exceptionLast;};var ___cxa_throw=function(ptr,type,destructor){var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw exceptionLast;};var ___cxa_uncaught_exceptions=function(){return uncaughtExceptionCount};var UTF8Decoder=typeof TextDecoder!="undefined"?new TextDecoder("utf8"):undefined;var UTF8ArrayToString=function(heapOrArray,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&
!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder)return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr));var str="";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224)u0=(u0&15)<<12|u1<<6|u2;else u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63;if(u0<65536)str+=String.fromCharCode(u0);
else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str};var UTF8ToString=function(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):""};var SYSCALLS={varargs:undefined,get:function(){var ret=HEAP32[+SYSCALLS.varargs>>2];SYSCALLS.varargs+=4;return ret},getp:function(){return SYSCALLS.get()},getStr:function(ptr){var ret=UTF8ToString(ptr);return ret}};function ___syscall_fcntl64(fd,cmd,varargs){SYSCALLS.varargs=varargs;return 0}var ___syscall_fstat64=
function(fd,buf){};function ___syscall_ioctl(fd,op,varargs){SYSCALLS.varargs=varargs;return 0}var ___syscall_lstat64=function(path,buf){};var ___syscall_newfstatat=function(dirfd,path,buf,flags){};function ___syscall_openat(dirfd,path,flags,varargs){SYSCALLS.varargs=varargs}var ___syscall_rmdir=function(path){};var ___syscall_stat64=function(path,buf){};var ___syscall_unlinkat=function(dirfd,path,flags){};var nowIsMonotonic=true;var __emscripten_get_now_is_monotonic=function(){return nowIsMonotonic};
var __emscripten_throw_longjmp=function(){throw Infinity;};var _abort=function(){abort("")};var _emscripten_date_now=function(){return Date.now()};var _emscripten_get_now;_emscripten_get_now=function(){return performance.now()};var _emscripten_memcpy_js=function(dest,src,num){return HEAPU8.copyWithin(dest,src,src+num)};var getHeapMax=function(){return 2147483648};var growMemory=function(size){var b=wasmMemory.buffer;var pages=(size-b.byteLength+65535)/65536;try{wasmMemory.grow(pages);updateMemoryViews();
return 1}catch(e){}};var _emscripten_resize_heap=function(requestedSize){var oldSize=HEAPU8.length;requestedSize>>>=0;var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize)return false;var alignUp=function(x,multiple){return x+(multiple-x%multiple)%multiple};for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),
65536));var replacement=growMemory(newSize);if(replacement)return true}return false};var ENV={};var getExecutableName=function(){return thisProgram||"./this.program"};var getEnvStrings=function(){if(!getEnvStrings.strings){var lang=(typeof navigator=="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8";var env={"USER":"web_user","LOGNAME":"web_user","PATH":"/","PWD":"/","HOME":"/home/<USER>","LANG":lang,"_":getExecutableName()};for(var x in ENV)if(ENV[x]===undefined)delete env[x];
else env[x]=ENV[x];var strings=[];for(var x in env)strings.push(x+"="+env[x]);getEnvStrings.strings=strings}return getEnvStrings.strings};var stringToAscii=function(str,buffer){for(var i=0;i<str.length;++i)HEAP8[buffer++>>0]=str.charCodeAt(i);HEAP8[buffer>>0]=0};var _environ_get=function(__environ,environ_buf){var bufSize=0;getEnvStrings().forEach(function(string,i){var ptr=environ_buf+bufSize;HEAPU32[__environ+i*4>>2]=ptr;stringToAscii(string,ptr);bufSize+=string.length+1});return 0};var _environ_sizes_get=
function(penviron_count,penviron_buf_size){var strings=getEnvStrings();HEAPU32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(function(string){return bufSize+=string.length+1});HEAPU32[penviron_buf_size>>2]=bufSize;return 0};var runtimeKeepaliveCounter=0;var keepRuntimeAlive=function(){return noExitRuntime||runtimeKeepaliveCounter>0};var _proc_exit=function(code){EXITSTATUS=code;if(!keepRuntimeAlive()){if(Module["onExit"])Module["onExit"](code);ABORT=true}quit_(code,new ExitStatus(code))};
var exitJS=function(status,implicit){EXITSTATUS=status;_proc_exit(status)};var _exit=exitJS;var _fd_close=function(fd){return 52};var _fd_read=function(fd,iov,iovcnt,pnum){return 52};var convertI32PairToI53Checked=function(lo,hi){return hi+2097152>>>0<4194305-!!lo?(lo>>>0)+hi*4294967296:NaN};function _fd_seek(fd,offset_low,offset_high,whence,newOffset){var offset=convertI32PairToI53Checked(offset_low,offset_high);return 70}var printCharBuffers=[null,[],[]];var printChar=function(stream,curr){var buffer=
printCharBuffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer,0));buffer.length=0}else buffer.push(curr)};var _fd_write=function(fd,iov,iovcnt,pnum){var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;for(var j=0;j<len;j++)printChar(fd,HEAPU8[ptr+j]);num+=len}HEAPU32[pnum>>2]=num;return 0};var _llvm_eh_typeid_for=function(type){return type};var isLeapYear=function(year){return year%4===0&&(year%100!==0||year%400===0)};var arraySum=
function(array,index){var sum=0;for(var i=0;i<=index;sum+=array[i++]);return sum};var MONTH_DAYS_LEAP=[31,29,31,30,31,30,31,31,30,31,30,31];var MONTH_DAYS_REGULAR=[31,28,31,30,31,30,31,31,30,31,30,31];var addDays=function(date,days){var newDate=new Date(date.getTime());while(days>0){var leap=isLeapYear(newDate.getFullYear());var currentMonth=newDate.getMonth();var daysInCurrentMonth=(leap?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR)[currentMonth];if(days>daysInCurrentMonth-newDate.getDate()){days-=daysInCurrentMonth-
newDate.getDate()+1;newDate.setDate(1);if(currentMonth<11)newDate.setMonth(currentMonth+1);else{newDate.setMonth(0);newDate.setFullYear(newDate.getFullYear()+1)}}else{newDate.setDate(newDate.getDate()+days);return newDate}}return newDate};var lengthBytesUTF8=function(str){var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127)len++;else if(c<=2047)len+=2;else if(c>=55296&&c<=57343){len+=4;++i}else len+=3}return len};var stringToUTF8Array=function(str,heap,outIdx,maxBytesToWrite){if(!(maxBytesToWrite>
0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=
240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx};function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var writeArrayToMemory=function(array,buffer){HEAP8.set(array,buffer)};var _strftime=function(s,maxsize,format,
tm){var tm_zone=HEAPU32[tm+40>>2];var date={tm_sec:HEAP32[tm>>2],tm_min:HEAP32[tm+4>>2],tm_hour:HEAP32[tm+8>>2],tm_mday:HEAP32[tm+12>>2],tm_mon:HEAP32[tm+16>>2],tm_year:HEAP32[tm+20>>2],tm_wday:HEAP32[tm+24>>2],tm_yday:HEAP32[tm+28>>2],tm_isdst:HEAP32[tm+32>>2],tm_gmtoff:HEAP32[tm+36>>2],tm_zone:tm_zone?UTF8ToString(tm_zone):""};var pattern=UTF8ToString(format);var EXPANSION_RULES_1={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S",
"%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var rule in EXPANSION_RULES_1)pattern=pattern.replace(new RegExp(rule,"g"),EXPANSION_RULES_1[rule]);var WEEKDAYS=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];var MONTHS=["January","February","March","April","May","June",
"July","August","September","October","November","December"];function leadingSomething(value,digits,character){var str=typeof value=="number"?value.toString():value||"";while(str.length<digits)str=character[0]+str;return str}function leadingNulls(value,digits){return leadingSomething(value,digits,"0")}function compareByDay(date1,date2){function sgn(value){return value<0?-1:value>0?1:0}var compare;if((compare=sgn(date1.getFullYear()-date2.getFullYear()))===0)if((compare=sgn(date1.getMonth()-date2.getMonth()))===
0)compare=sgn(date1.getDate()-date2.getDate());return compare}function getFirstWeekStartDate(janFourth){switch(janFourth.getDay()){case 0:return new Date(janFourth.getFullYear()-1,11,29);case 1:return janFourth;case 2:return new Date(janFourth.getFullYear(),0,3);case 3:return new Date(janFourth.getFullYear(),0,2);case 4:return new Date(janFourth.getFullYear(),0,1);case 5:return new Date(janFourth.getFullYear()-1,11,31);case 6:return new Date(janFourth.getFullYear()-1,11,30)}}function getWeekBasedYear(date){var thisDate=
addDays(new Date(date.tm_year+1900,0,1),date.tm_yday);var janFourthThisYear=new Date(thisDate.getFullYear(),0,4);var janFourthNextYear=new Date(thisDate.getFullYear()+1,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);if(compareByDay(firstWeekStartThisYear,thisDate)<=0){if(compareByDay(firstWeekStartNextYear,thisDate)<=0)return thisDate.getFullYear()+1;return thisDate.getFullYear()}return thisDate.getFullYear()-
1}var EXPANSION_RULES_2={"%a":function(date){return WEEKDAYS[date.tm_wday].substring(0,3)},"%A":function(date){return WEEKDAYS[date.tm_wday]},"%b":function(date){return MONTHS[date.tm_mon].substring(0,3)},"%B":function(date){return MONTHS[date.tm_mon]},"%C":function(date){var year=date.tm_year+1900;return leadingNulls(year/100|0,2)},"%d":function(date){return leadingNulls(date.tm_mday,2)},"%e":function(date){return leadingSomething(date.tm_mday,2," ")},"%g":function(date){return getWeekBasedYear(date).toString().substring(2)},
"%G":function(date){return getWeekBasedYear(date)},"%H":function(date){return leadingNulls(date.tm_hour,2)},"%I":function(date){var twelveHour=date.tm_hour;if(twelveHour==0)twelveHour=12;else if(twelveHour>12)twelveHour-=12;return leadingNulls(twelveHour,2)},"%j":function(date){return leadingNulls(date.tm_mday+arraySum(isLeapYear(date.tm_year+1900)?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR,date.tm_mon-1),3)},"%m":function(date){return leadingNulls(date.tm_mon+1,2)},"%M":function(date){return leadingNulls(date.tm_min,
2)},"%n":function(){return"\n"},"%p":function(date){if(date.tm_hour>=0&&date.tm_hour<12)return"AM";return"PM"},"%S":function(date){return leadingNulls(date.tm_sec,2)},"%t":function(){return"\t"},"%u":function(date){return date.tm_wday||7},"%U":function(date){var days=date.tm_yday+7-date.tm_wday;return leadingNulls(Math.floor(days/7),2)},"%V":function(date){var val=Math.floor((date.tm_yday+7-(date.tm_wday+6)%7)/7);if((date.tm_wday+371-date.tm_yday-2)%7<=2)val++;if(!val){val=52;var dec31=(date.tm_wday+
7-date.tm_yday-1)%7;if(dec31==4||dec31==5&&isLeapYear(date.tm_year%400-1))val++}else if(val==53){var jan1=(date.tm_wday+371-date.tm_yday)%7;if(jan1!=4&&(jan1!=3||!isLeapYear(date.tm_year)))val=1}return leadingNulls(val,2)},"%w":function(date){return date.tm_wday},"%W":function(date){var days=date.tm_yday+7-(date.tm_wday+6)%7;return leadingNulls(Math.floor(days/7),2)},"%y":function(date){return(date.tm_year+1900).toString().substring(2)},"%Y":function(date){return date.tm_year+1900},"%z":function(date){var off=
date.tm_gmtoff;var ahead=off>=0;off=Math.abs(off)/60;off=off/60*100+off%60;return(ahead?"+":"-")+String("0000"+off).slice(-4)},"%Z":function(date){return date.tm_zone},"%%":function(){return"%"}};pattern=pattern.replace(/%%/g,"\x00\x00");for(var rule in EXPANSION_RULES_2)if(pattern.includes(rule))pattern=pattern.replace(new RegExp(rule,"g"),EXPANSION_RULES_2[rule](date));pattern=pattern.replace(/\0\0/g,"%");var bytes=intArrayFromString(pattern,false);if(bytes.length>maxsize)return 0;writeArrayToMemory(bytes,
s);return bytes.length-1};var _strftime_l=function(s,maxsize,format,tm,loc){return _strftime(s,maxsize,format,tm)};var wasmTableMirror=[];var wasmTable;var getWasmTableEntry=function(funcPtr){var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func};var wasmImports={v:___cxa_begin_catch,x:___cxa_end_catch,a:___cxa_find_matching_catch_2,k:___cxa_find_matching_catch_3,V:___cxa_rethrow,
B:___cxa_throw,ga:___cxa_uncaught_exceptions,d:___resumeException,T:___syscall_fcntl64,qa:___syscall_fstat64,sa:___syscall_ioctl,na:___syscall_lstat64,oa:___syscall_newfstatat,U:___syscall_openat,ja:___syscall_rmdir,pa:___syscall_stat64,ka:___syscall_unlinkat,ta:__emscripten_get_now_is_monotonic,ha:__emscripten_throw_longjmp,K:_abort,ua:_emscripten_date_now,va:_emscripten_memcpy_js,ia:_emscripten_resize_heap,la:_environ_get,ma:_environ_sizes_get,C:_exit,M:_fd_close,ra:_fd_read,ca:_fd_seek,S:_fd_write,
y:invoke_di,Q:invoke_diii,R:invoke_fiii,r:invoke_i,b:invoke_ii,ya:invoke_iid,o:invoke_iidd,J:invoke_iidddddd,f:invoke_iii,xa:invoke_iiidd,za:invoke_iiidddd,e:invoke_iiii,O:invoke_iiiidddd,j:invoke_iiiii,fa:invoke_iiiiid,n:invoke_iiiiii,_:invoke_iiiiiidd,s:invoke_iiiiiii,z:invoke_iiiiiiii,E:invoke_iiiiiiiii,I:invoke_iiiiiiiiiiii,P:invoke_iiiiiiiiiiiiiii,ba:invoke_jiiii,m:invoke_v,i:invoke_vi,wa:invoke_vid,u:invoke_vidd,Z:invoke_viddddiii,h:invoke_vidi,c:invoke_vii,da:invoke_viid,p:invoke_viidd,Y:invoke_viidi,
X:invoke_viidiii,Ba:invoke_viiffffffi,aa:invoke_viiffffi,g:invoke_viii,N:invoke_viiiddiidd,D:invoke_viiiffffffi,A:invoke_viiiffffi,w:invoke_viiiffi,l:invoke_viiii,q:invoke_viiiii,W:invoke_viiiiidd,G:invoke_viiiiii,t:invoke_viiiiiii,L:invoke_viiiiiiii,Aa:invoke_viiiiiiiii,F:invoke_viiiiiiiiii,H:invoke_viiiiiiiiiiiiiii,$:_llvm_eh_typeid_for,ea:_strftime_l};var wasmExports=createWasm();var ___wasm_call_ctors=function(){return(___wasm_call_ctors=wasmExports["Da"])()};var _malloc=Module["_malloc"]=function(a0){return(_malloc=
Module["_malloc"]=wasmExports["Fa"])(a0)};var _free=Module["_free"]=function(a0){return(_free=Module["_free"]=wasmExports["Ga"])(a0)};var ___errno_location=function(){return(___errno_location=wasmExports["__errno_location"])()};var _ASC_FT_Malloc=Module["_ASC_FT_Malloc"]=function(a0){return(_ASC_FT_Malloc=Module["_ASC_FT_Malloc"]=wasmExports["Ha"])(a0)};var _ASC_FT_Free=Module["_ASC_FT_Free"]=function(a0){return(_ASC_FT_Free=Module["_ASC_FT_Free"]=wasmExports["Ia"])(a0)};var _ASC_FT_Init=Module["_ASC_FT_Init"]=
function(){return(_ASC_FT_Init=Module["_ASC_FT_Init"]=wasmExports["Ja"])()};var _ASC_FT_Done_FreeType=Module["_ASC_FT_Done_FreeType"]=function(a0){return(_ASC_FT_Done_FreeType=Module["_ASC_FT_Done_FreeType"]=wasmExports["Ka"])(a0)};var _ASC_FT_Set_TrueType_HintProp=Module["_ASC_FT_Set_TrueType_HintProp"]=function(a0,a1){return(_ASC_FT_Set_TrueType_HintProp=Module["_ASC_FT_Set_TrueType_HintProp"]=wasmExports["La"])(a0,a1)};var _ASC_FT_Open_Face=Module["_ASC_FT_Open_Face"]=function(a0,a1,a2,a3){return(_ASC_FT_Open_Face=
Module["_ASC_FT_Open_Face"]=wasmExports["Ma"])(a0,a1,a2,a3)};var _ASC_FT_Done_Face=Module["_ASC_FT_Done_Face"]=function(a0){return(_ASC_FT_Done_Face=Module["_ASC_FT_Done_Face"]=wasmExports["Na"])(a0)};var _ASC_FT_SetCMapForCharCode=Module["_ASC_FT_SetCMapForCharCode"]=function(a0,a1){return(_ASC_FT_SetCMapForCharCode=Module["_ASC_FT_SetCMapForCharCode"]=wasmExports["Oa"])(a0,a1)};var _ASC_FT_GetFaceInfo=Module["_ASC_FT_GetFaceInfo"]=function(a0){return(_ASC_FT_GetFaceInfo=Module["_ASC_FT_GetFaceInfo"]=
wasmExports["Pa"])(a0)};var _ASC_FT_GetFaceMaxAdvanceX=Module["_ASC_FT_GetFaceMaxAdvanceX"]=function(a0){return(_ASC_FT_GetFaceMaxAdvanceX=Module["_ASC_FT_GetFaceMaxAdvanceX"]=wasmExports["Qa"])(a0)};var _ASC_FT_GetKerningX=Module["_ASC_FT_GetKerningX"]=function(a0,a1,a2){return(_ASC_FT_GetKerningX=Module["_ASC_FT_GetKerningX"]=wasmExports["Ra"])(a0,a1,a2)};var _ASC_FT_Set_Transform=Module["_ASC_FT_Set_Transform"]=function(a0,a1,a2,a3,a4){return(_ASC_FT_Set_Transform=Module["_ASC_FT_Set_Transform"]=
wasmExports["Sa"])(a0,a1,a2,a3,a4)};var _ASC_FT_Set_Char_Size=Module["_ASC_FT_Set_Char_Size"]=function(a0,a1,a2,a3,a4){return(_ASC_FT_Set_Char_Size=Module["_ASC_FT_Set_Char_Size"]=wasmExports["Ta"])(a0,a1,a2,a3,a4)};var _ASC_FT_Load_Glyph=Module["_ASC_FT_Load_Glyph"]=function(a0,a1,a2){return(_ASC_FT_Load_Glyph=Module["_ASC_FT_Load_Glyph"]=wasmExports["Ua"])(a0,a1,a2)};var _ASC_FT_Glyph_Get_CBox=Module["_ASC_FT_Glyph_Get_CBox"]=function(a0,a1){return(_ASC_FT_Glyph_Get_CBox=Module["_ASC_FT_Glyph_Get_CBox"]=
wasmExports["Va"])(a0,a1)};var _ASC_FT_Get_Glyph_Measure_Params=Module["_ASC_FT_Get_Glyph_Measure_Params"]=function(a0,a1){return(_ASC_FT_Get_Glyph_Measure_Params=Module["_ASC_FT_Get_Glyph_Measure_Params"]=wasmExports["Wa"])(a0,a1)};var _ASC_FT_Get_Glyph_Render_Params=Module["_ASC_FT_Get_Glyph_Render_Params"]=function(a0,a1){return(_ASC_FT_Get_Glyph_Render_Params=Module["_ASC_FT_Get_Glyph_Render_Params"]=wasmExports["Xa"])(a0,a1)};var _ASC_FT_Get_Glyph_Render_Buffer=Module["_ASC_FT_Get_Glyph_Render_Buffer"]=
function(a0){return(_ASC_FT_Get_Glyph_Render_Buffer=Module["_ASC_FT_Get_Glyph_Render_Buffer"]=wasmExports["Ya"])(a0)};var _ASC_HB_LanguageFromString=Module["_ASC_HB_LanguageFromString"]=function(a0){return(_ASC_HB_LanguageFromString=Module["_ASC_HB_LanguageFromString"]=wasmExports["Za"])(a0)};var _ASC_HB_ShapeText=Module["_ASC_HB_ShapeText"]=function(a0,a1,a2,a3,a4,a5,a6){return(_ASC_HB_ShapeText=Module["_ASC_HB_ShapeText"]=wasmExports["_a"])(a0,a1,a2,a3,a4,a5,a6)};var _ASC_HB_FontFree=Module["_ASC_HB_FontFree"]=
function(a0){return(_ASC_HB_FontFree=Module["_ASC_HB_FontFree"]=wasmExports["$a"])(a0)};var ___cxa_free_exception=function(a0){return(___cxa_free_exception=wasmExports["__cxa_free_exception"])(a0)};var _Zlib_Malloc=Module["_Zlib_Malloc"]=function(a0){return(_Zlib_Malloc=Module["_Zlib_Malloc"]=wasmExports["ab"])(a0)};var _Zlib_Free=Module["_Zlib_Free"]=function(a0){return(_Zlib_Free=Module["_Zlib_Free"]=wasmExports["bb"])(a0)};var _Zlib_Create=Module["_Zlib_Create"]=function(){return(_Zlib_Create=
Module["_Zlib_Create"]=wasmExports["cb"])()};var _Zlib_Open=Module["_Zlib_Open"]=function(a0,a1){return(_Zlib_Open=Module["_Zlib_Open"]=wasmExports["db"])(a0,a1)};var _Zlib_Close=Module["_Zlib_Close"]=function(a0){return(_Zlib_Close=Module["_Zlib_Close"]=wasmExports["eb"])(a0)};var _Zlib_AddFile=Module["_Zlib_AddFile"]=function(a0,a1,a2,a3){return(_Zlib_AddFile=Module["_Zlib_AddFile"]=wasmExports["fb"])(a0,a1,a2,a3)};var _Zlib_RemoveFile=Module["_Zlib_RemoveFile"]=function(a0,a1){return(_Zlib_RemoveFile=
Module["_Zlib_RemoveFile"]=wasmExports["gb"])(a0,a1)};var _Zlib_GetPaths=Module["_Zlib_GetPaths"]=function(a0){return(_Zlib_GetPaths=Module["_Zlib_GetPaths"]=wasmExports["hb"])(a0)};var _Zlib_GetFile=Module["_Zlib_GetFile"]=function(a0,a1){return(_Zlib_GetFile=Module["_Zlib_GetFile"]=wasmExports["ib"])(a0,a1)};var _Zlib_Save=Module["_Zlib_Save"]=function(a0){return(_Zlib_Save=Module["_Zlib_Save"]=wasmExports["jb"])(a0)};var _Raster_DecodeFile=Module["_Raster_DecodeFile"]=function(a0,a1,a2){return(_Raster_DecodeFile=
Module["_Raster_DecodeFile"]=wasmExports["kb"])(a0,a1,a2)};var _Raster_GetDecodedBuffer=Module["_Raster_GetDecodedBuffer"]=function(a0){return(_Raster_GetDecodedBuffer=Module["_Raster_GetDecodedBuffer"]=wasmExports["lb"])(a0)};var _Raster_GetWidth=Module["_Raster_GetWidth"]=function(a0){return(_Raster_GetWidth=Module["_Raster_GetWidth"]=wasmExports["mb"])(a0)};var _Raster_GetHeight=Module["_Raster_GetHeight"]=function(a0){return(_Raster_GetHeight=Module["_Raster_GetHeight"]=wasmExports["nb"])(a0)};
var _Raster_GetStride=Module["_Raster_GetStride"]=function(a0){return(_Raster_GetStride=Module["_Raster_GetStride"]=wasmExports["ob"])(a0)};var _Raster_Destroy=Module["_Raster_Destroy"]=function(a0){return(_Raster_Destroy=Module["_Raster_Destroy"]=wasmExports["pb"])(a0)};var _Raster_EncodeImageData=Module["_Raster_EncodeImageData"]=function(a0,a1,a2,a3,a4,a5){return(_Raster_EncodeImageData=Module["_Raster_EncodeImageData"]=wasmExports["qb"])(a0,a1,a2,a3,a4,a5)};var _Raster_Encode=Module["_Raster_Encode"]=
function(a0,a1,a2){return(_Raster_Encode=Module["_Raster_Encode"]=wasmExports["rb"])(a0,a1,a2)};var _Raster_GetEncodedSize=Module["_Raster_GetEncodedSize"]=function(a0){return(_Raster_GetEncodedSize=Module["_Raster_GetEncodedSize"]=wasmExports["sb"])(a0)};var _Raster_GetEncodedBuffer=Module["_Raster_GetEncodedBuffer"]=function(a0){return(_Raster_GetEncodedBuffer=Module["_Raster_GetEncodedBuffer"]=wasmExports["tb"])(a0)};var _Raster_DestroyEncodedData=Module["_Raster_DestroyEncodedData"]=function(a0){return(_Raster_DestroyEncodedData=
Module["_Raster_DestroyEncodedData"]=wasmExports["ub"])(a0)};var _Image_GetFormat=Module["_Image_GetFormat"]=function(a0,a1){return(_Image_GetFormat=Module["_Image_GetFormat"]=wasmExports["vb"])(a0,a1)};var setTempRet0=function(a0){return(setTempRet0=wasmExports["wb"])(a0)};var _hyphenCreateApplication=Module["_hyphenCreateApplication"]=function(){return(_hyphenCreateApplication=Module["_hyphenCreateApplication"]=wasmExports["xb"])()};var _hyphenDestroyApplication=Module["_hyphenDestroyApplication"]=
function(a0){return(_hyphenDestroyApplication=Module["_hyphenDestroyApplication"]=wasmExports["yb"])(a0)};var _hyphenLoadDictionary=Module["_hyphenLoadDictionary"]=function(a0,a1,a2,a3){return(_hyphenLoadDictionary=Module["_hyphenLoadDictionary"]=wasmExports["zb"])(a0,a1,a2,a3)};var _hyphenCheckDictionary=Module["_hyphenCheckDictionary"]=function(a0,a1){return(_hyphenCheckDictionary=Module["_hyphenCheckDictionary"]=wasmExports["Ab"])(a0,a1)};var _hyphenWord=Module["_hyphenWord"]=function(a0,a1,a2,
a3){return(_hyphenWord=Module["_hyphenWord"]=wasmExports["Bb"])(a0,a1,a2,a3)};var _setThrew=function(a0,a1){return(_setThrew=wasmExports["Cb"])(a0,a1)};var stackSave=function(){return(stackSave=wasmExports["Db"])()};var stackRestore=function(a0){return(stackRestore=wasmExports["Eb"])(a0)};var ___cxa_decrement_exception_refcount=function(a0){return(___cxa_decrement_exception_refcount=wasmExports["Fb"])(a0)};var ___cxa_increment_exception_refcount=function(a0){return(___cxa_increment_exception_refcount=
wasmExports["Gb"])(a0)};var ___cxa_can_catch=function(a0,a1,a2){return(___cxa_can_catch=wasmExports["Hb"])(a0,a1,a2)};var ___cxa_is_pointer_type=function(a0){return(___cxa_is_pointer_type=wasmExports["Ib"])(a0)};var dynCall_jiiii=Module["dynCall_jiiii"]=function(a0,a1,a2,a3,a4){return(dynCall_jiiii=Module["dynCall_jiiii"]=wasmExports["Jb"])(a0,a1,a2,a3,a4)};function invoke_v(index){var sp=stackSave();try{getWasmTableEntry(index)()}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiii(index,
a1,a2,a3){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vii(index,a1,a2){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iii(index,a1,a2){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_ii(index,a1){var sp=stackSave();try{return getWasmTableEntry(index)(a1)}catch(e){stackRestore(sp);
if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vi(index,a1){var sp=stackSave();try{getWasmTableEntry(index)(a1)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_i(index){var sp=stackSave();try{return getWasmTableEntry(index)()}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiii(index,a1,a2,a3,a4,a5){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiii(index,
a1,a2,a3,a4){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiii(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiii(index,a1,a2,a3,a4){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiiiiii(index,
a1,a2,a3,a4,a5,a6,a7,a8){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viii(index,a1,a2,a3){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiffi(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiiiiiiiiiiii(index,
a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiffffffi(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiffffi(index,a1,a2,a3,a4,a5,a6,a7,a8){var sp=stackSave();try{getWasmTableEntry(index)(a1,
a2,a3,a4,a5,a6,a7,a8)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiffffi(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiffffffi(index,a1,a2,a3,a4,a5,a6,a7,a8,a9){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiii(index,a1,a2,a3,
a4,a5){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiiii(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(e!==e+0)throw e;
_setThrew(1,0)}}function invoke_iiiiiiii(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiiii(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,
a3,a4,a5,a6,a7,a8)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiidddd(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiidddd(index,a1,
a2,a3,a4,a5,a6){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_di(index,a1){var sp=stackSave();try{return getWasmTableEntry(index)(a1)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iid(index,a1,a2){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vidi(index,a1,a2,a3){var sp=stackSave();
try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iidd(index,a1,a2,a3){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiiidd(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iidddddd(index,a1,a2,a3,a4,a5,a6,
a7){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viidd(index,a1,a2,a3,a4){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiidd(index,a1,a2,a3,a4){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiddiidd(index,
a1,a2,a3,a4,a5,a6,a7,a8,a9){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viddddiii(index,a1,a2,a3,a4,a5,a6,a7,a8){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vidd(index,a1,a2,a3){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,
0)}}function invoke_viidi(index,a1,a2,a3,a4){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viidiii(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiidd(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(e!==e+
0)throw e;_setThrew(1,0)}}function invoke_vid(index,a1,a2){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiid(index,a1,a2,a3,a4,a5){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_fiii(index,a1,a2,a3){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,
0)}}function invoke_diii(index,a1,a2,a3){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiiiiiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14,a15){var sp=stackSave();try{getWasmTableEntry(index)(a1,
a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14,a15)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viid(index,a1,a2,a3){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_jiiii(index,a1,a2,a3,a4){var sp=stackSave();try{return dynCall_jiiii(index,a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();
if(!calledRun)dependenciesFulfilled=runCaller};function run(){if(runDependencies>0)return;preRun();if(runDependencies>0)return;function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else doRun()}if(Module["preInit"]){if(typeof Module["preInit"]==
"function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0)Module["preInit"].pop()()}run();function CReturnObject(){this.error=0;this.freeObj=0}CReturnObject.prototype.free=function(){Module["_ASC_FT_Free"](this.freeObj)};var g_return_obj=new CReturnObject;var g_return_obj_count=new CReturnObject;g_return_obj_count.count=0;AscFonts.CopyStreamToMemory=function(data,size){var fontStreamPointer=Module["_ASC_FT_Malloc"](size);Module["HEAP8"].set(data,fontStreamPointer);return fontStreamPointer};
AscFonts.GetUint8ArrayFromPointer=function(pointer,size){return new Uint8Array(Module["HEAP8"].buffer,pointer,size)};function CShapeString(size){this.size=size;this.pointer=Module["_malloc"](size)}CShapeString.prototype.getBuffer=function(){return new Uint8Array(Module["HEAPU8"].buffer,this.pointer,this.size)};CShapeString.prototype.free=function(){Module["_free"](this.pointer)};CShapeString.prototype.set=function(index,value){Module["HEAPU8"][this.pointer+index]=value};AscFonts.AllocString=function(size){return new CShapeString(size)};
AscFonts.FT_CreateLibrary=Module["_ASC_FT_Init"];AscFonts.FT_Done_Library=Module["_ASC_FT_Done_FreeType"];AscFonts.FT_Set_TrueType_HintProp=Module["_ASC_FT_Set_TrueType_HintProp"];AscFonts.FT_Open_Face=Module["_ASC_FT_Open_Face"];AscFonts.FT_Done_Face=Module["_ASC_FT_Done_Face"];AscFonts.FT_SetCMapForCharCode=Module["_ASC_FT_SetCMapForCharCode"];AscFonts.FT_GetKerningX=Module["_ASC_FT_GetKerningX"];AscFonts.FT_GetFaceMaxAdvanceX=Module["_ASC_FT_GetFaceMaxAdvanceX"];AscFonts.FT_Set_Transform=Module["_ASC_FT_Set_Transform"];
AscFonts.FT_Set_Char_Size=Module["_ASC_FT_Set_Char_Size"];AscFonts.FT_GetFaceInfo=function(face,reader){var pointer=Module["_ASC_FT_GetFaceInfo"](face);if(!pointer){g_return_obj.error=1;return g_return_obj}var len_buffer=Math.min(Module["HEAP8"].length-pointer,1E3);reader.init(new Uint8Array(Module["HEAP8"].buffer,pointer,len_buffer));g_return_obj.freeObj=pointer;g_return_obj.error=0;return g_return_obj};AscFonts.FT_Load_Glyph=Module["_ASC_FT_Load_Glyph"];AscFonts.FT_SetCMapForCharCode=Module["_ASC_FT_SetCMapForCharCode"];
AscFonts.FT_Get_Glyph_Measure_Params=function(face,vector_worker,reader){var pointer=Module["_ASC_FT_Get_Glyph_Measure_Params"](face,vector_worker?1:0);if(!pointer){g_return_obj_count.error=1;return g_return_obj_count}var len=!vector_worker?15:Module["HEAP32"][pointer>>2];if(vector_worker)len=Module["HEAP32"][pointer>>2];reader.init(new Uint8Array(Module["HEAP8"].buffer,pointer+4,4*(len-1)));g_return_obj_count.freeObj=pointer;g_return_obj_count.count=len;g_return_obj_count.error=0;return g_return_obj_count};
AscFonts.FT_Get_Glyph_Render_Params=function(face,render_mode,reader){var pointer=Module["_ASC_FT_Get_Glyph_Render_Params"](face,render_mode);if(!pointer){g_return_obj_count.error=1;return g_return_obj_count}reader.init(new Uint8Array(Module["HEAP8"].buffer,pointer,4*6));g_return_obj.freeObj=pointer;g_return_obj.error=0;return g_return_obj};AscFonts.FT_Get_Glyph_Render_Buffer=function(face,size){var pointer=Module["_ASC_FT_Get_Glyph_Render_Buffer"](face);return new Uint8Array(Module["HEAP8"].buffer,
pointer,size)};var hb_cache_languages={};AscFonts.HB_FontFree=Module["ASC_HB_FontFree"];AscFonts.HB_ShapeText=function(fontFile,text,features,script,direction,language,reader){if(!hb_cache_languages[language]){var langBuffer=language.toUtf8();var langPointer=Module["_malloc"](langBuffer.length);Module["HEAP8"].set(langBuffer,langBuffer);hb_cache_languages[language]=Module["_ASC_HB_LanguageFromString"](langPointer);Module["_free"](langPointer)}var pointer=Module["_ASC_HB_ShapeText"](fontFile["GetFace"](),
fontFile["GetHBFont"](),text.pointer,features,script,direction,hb_cache_languages[language]);if(!pointer){g_return_obj_count.error=1;return g_return_obj_count}var buffer=Module["HEAP8"];var len=(buffer[pointer+3]&255)<<24|(buffer[pointer+2]&255)<<16|(buffer[pointer+1]&255)<<8|buffer[pointer]&255;reader.init(buffer,pointer+4,len-4);fontFile["SetHBFont"](reader.readPointer64());g_return_obj_count.freeObj=pointer;g_return_obj_count.count=(len-12)/26;g_return_obj_count.error=0;return g_return_obj_count};
function ZLib(){this.engine=0;this.files={}}ZLib.prototype.isModuleInit=false;ZLib.prototype.open=function(buf){if(!this.isModuleInit)return false;if(this.engine)this.close();if(!buf)return false;var arrayBuffer=undefined!==buf.byteLength?new Uint8Array(buf):buf;var FileRawDataSize=arrayBuffer.length;var FileRawData=Module["_Zlib_Malloc"](FileRawDataSize);if(0==FileRawData)return false;Module["HEAP8"].set(arrayBuffer,FileRawData);this.engine=Module["_Zlib_Open"](FileRawData,FileRawDataSize);if(0==
this.engine){Module["_Zlib_Free"](FileRawData);return false}var pointer=Module["_Zlib_GetPaths"](this.engine);if(0==pointer){Module["_Zlib_Close"](this.engine);Module["_Zlib_Free"](FileRawData);return false}var lenArray=new Int32Array(Module["HEAP8"].buffer,pointer,4);var len=lenArray[0];len-=4;var buffer=new Uint8Array(Module["HEAP8"].buffer,pointer+4,len);var index=0;while(index<len){var lenRec=buffer[index]|buffer[index+1]<<8|buffer[index+2]<<16|buffer[index+3]<<24;index+=4;var _path="".fromUtf8(buffer,
index,lenRec);this.files[_path]=null;index+=lenRec}Module["_Zlib_Free"](FileRawData);Module["_Zlib_Free"](pointer);return true};ZLib.prototype.create=function(){if(!this.isModuleInit)return false;if(this.engine)this.close();this.engine=Module["_Zlib_Create"]();return!!this.engine};ZLib.prototype.save=function(){if(!this.isModuleInit||!this.engine)return null;var pointerZip=Module["_Zlib_Save"](this.engine);if(0==pointerZip)return null;var _lenFile=new Int32Array(Module["HEAP8"].buffer,pointerZip,
4);var len=_lenFile[0];var zip=new Uint8Array(Module["HEAP8"].buffer,pointerZip+4,len);return zip};ZLib.prototype.getPaths=function(){var retFiles=[];if(!this.files)return retFiles;for(var path in this.files)if(this.files.hasOwnProperty(path))retFiles.push(path);return retFiles};ZLib.prototype.getFile=function(path){if(!this.isModuleInit||!this.engine)return null;if(undefined===this.files[path])return null;if(null!==this.files[path])if(this.files[path].l>0)return new Uint8Array(Module["HEAP8"].buffer,
this.files[path].p,this.files[path].l);else{var _lenFile=new Int32Array(Module["HEAP8"].buffer,this.files[path].p,4);var len=_lenFile[0];return new Uint8Array(Module["HEAP8"].buffer,this.files[path].p+4,len)}var tmp=path.toUtf8();var pointer=Module["_Zlib_Malloc"](tmp.length);if(0==pointer)return null;Module["HEAP8"].set(tmp,pointer);var pointerFile=Module["_Zlib_GetFile"](this.engine,pointer);if(0==pointerFile){Module["_Zlib_Free"](pointer);return null}var _lenFile=new Int32Array(Module["HEAP8"].buffer,
pointerFile,4);var len=_lenFile[0];Module["_Zlib_Free"](pointer);this.files[path]={p:pointerFile,l:0};return new Uint8Array(Module["HEAP8"].buffer,pointerFile+4,len)};ZLib.prototype.addFile=function(path,data){if(!this.isModuleInit||!this.engine)return false;if(!data)return false;if(undefined!==this.files[path])this.removeFile(path);var tmp=path.toUtf8();var pointer=Module["_Zlib_Malloc"](tmp.length);if(0==pointer)return false;Module["HEAP8"].set(tmp,pointer);var arrayBuffer=undefined!==data.byteLength?
new Uint8Array(data):data;var FileRawDataSize=arrayBuffer.length;var FileRawData=Module["_Zlib_Malloc"](FileRawDataSize);if(0==FileRawData){Module["_Zlib_Free"](pointer);return false}Module["HEAP8"].set(arrayBuffer,FileRawData);Module["_Zlib_AddFile"](this.engine,pointer,FileRawData,FileRawDataSize);this.files[path]={p:FileRawData,l:FileRawDataSize};Module["_Zlib_Free"](pointer);return true};ZLib.prototype.removeFile=function(path){if(!this.isModuleInit||!this.engine)return false;if(undefined===this.files[path])return false;
var tmp=path.toUtf8();var pointer=Module["_Zlib_Malloc"](tmp.length);if(0==pointer)return false;Module["HEAP8"].set(tmp,pointer);Module["_Zlib_RemoveFile"](this.engine,pointer);if(this.files[path]&&this.files[path].p){Module["_Zlib_Free"](this.files[path].p);delete this.files[path]}Module["_Zlib_Free"](pointer);return true};ZLib.prototype.close=function(){if(!this.isModuleInit||!this.engine)return;for(var i in this.files)if(this.files[i]&&this.files[i].p)Module["_Zlib_Free"](this.files[i].p);this.files=
{};if(this.engine)Module["_Zlib_Free"](this.engine);this.engine=0};ZLib.prototype.getImageType=function(path){var fileData=this.getFile(path);return Module["_Image_GetFormat"](this.files[path].p+4,fileData.length)};ZLib.prototype.getImageAsFormat=function(path,format){var fileData=this.getFile(path);var encodedData=Module["_Raster_Encode"](this.files[path].p+4,fileData.length,format);var encodedSize=Module["_Raster_GetEncodedSize"](encodedData);var encodedBuffer=Module["_Raster_GetEncodedBuffer"](encodedData);
var copyData=new Uint8Array(encodedSize);copyData.set(new Uint8Array(Module["HEAP8"].buffer,encodedBuffer,encodedSize));Module["_Raster_DestroyEncodedData"](encodedData);return copyData};ZLib.prototype.getImageAsSvg=function(path){var fileData=this.getFile(path);var encodedData=Module["_Raster_Encode"](this.files[path].p+4,fileData.length,24);var encodedSize=Module["_Raster_GetEncodedSize"](encodedData);var encodedBuffer=Module["_Raster_GetEncodedBuffer"](encodedData);var string=String.prototype.fromUtf8(new Uint8Array(Module["HEAP8"].buffer,
encodedBuffer,encodedSize));Module["_Raster_DestroyEncodedData"](encodedData);return string};ZLib.prototype.getImageBuffer=function(path){var result={type:0,data:null};result.type=this.getImageType(path);if(result.type===0)return null;var fileData=this.getFile(path);result.data=new Uint8Array(fileData.length);result.data.set(fileData);if(result.type!=10&&result.type!=21)return result;result.type=24;var encodedData=Module["_Raster_Encode"](this.files[path].p+4,fileData.length,24);var encodedSize=Module["_Raster_GetEncodedSize"](encodedData);
var encodedBuffer=Module["_Raster_GetEncodedBuffer"](encodedData);var fileDataEnc=new Uint8Array(Module["HEAP8"].buffer,encodedBuffer,encodedSize);result.dataBlob=new Uint8Array(fileDataEnc.length);result.dataBlob.set(fileDataEnc);Module["_Raster_DestroyEncodedData"](encodedData);return result};ZLib.prototype.getImageBlob=function(path){var imageType=this.getImageType(path);if(imageType!=10&&imageType!=21)return new Blob([this.getFile(path)],{type:AscCommon.openXml.GetMimeType(AscCommon.GetFileExtension(path))});
var fileData=this.getFile(path);var encodedData=Module["_Raster_Encode"](this.files[path].p+4,fileData.length,24);var encodedSize=Module["_Raster_GetEncodedSize"](encodedData);var encodedBuffer=Module["_Raster_GetEncodedBuffer"](encodedData);var blob=new Blob([new Uint8Array(Module["HEAP8"].buffer,encodedBuffer,encodedSize)],{type:AscCommon.openXml.GetMimeType("svg")});Module["_Raster_DestroyEncodedData"](encodedData);return blob};window.AscCommon=window.AscCommon||{};window.AscCommon.CZLibEngineJS=
ZLib;var hyphenApplication=0;AscFonts.Hyphen_Init=function(){hyphenApplication=Module["_hyphenCreateApplication"]()};AscFonts.Hyphen_Destroy=function(){Module["_hyphenDestroyApplication"](hyphenApplication)};AscFonts.Hyphen_CheckDictionary=function(lang){return false};AscFonts.Hyphen_LoadDictionary=function(lang,data){var dictSize=data.byteLength;var dictPointer=Module["_malloc"](dictSize);Module["HEAP8"].set(new Uint8ClampedArray(data),dictPointer);var result=Module["_hyphenLoadDictionary"](hyphenApplication,
lang,dictPointer,dictSize);Module["_free"](dictPointer);return result===0?true:false};AscFonts.Hyphen_Word=function(lang,word){var wordPointer=word.toUtf8Pointer(true);var wordLen=wordPointer.length;var hyphens=[];if(wordPointer){var ptr=Module._hyphenWord(hyphenApplication,lang,wordPointer.ptr,wordLen);var vector=new Uint8ClampedArray(Module["HEAP8"].buffer,ptr,wordLen+5);var pos=0;while(vector[pos]!=0){if(1===(vector[pos]&1))hyphens.push(pos+1);pos++}wordPointer.free()}return hyphens};if(window["NATIVE_EDITOR_ENJINE"])window.immediateRun();
AscFonts.onLoadModule()})(window,undefined);
