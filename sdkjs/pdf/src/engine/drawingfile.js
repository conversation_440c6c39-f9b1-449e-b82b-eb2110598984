/*
 * Copyright (C) Ascensio System SIA 2012-2025. All rights reserved
 *
 * https://www.onlyoffice.com/
 *
 * Version: 0.0.0 (build:0)
 */

(function(window,undefined){function getMemoryPathIE(name){if(self["AscViewer"]&&self["AscViewer"]["baseUrl"])return self["AscViewer"]["baseUrl"]+name;return name}var baseFontsPath="../../../../fonts/";var FS=undefined;var printErr=undefined;var print=undefined;var fetch="undefined"!==typeof window?window.fetch:"undefined"!==typeof self?self.fetch:null;var getBinaryPromise=null;function internal_isLocal(){if(window.navigator&&window.navigator.userAgent.toLowerCase().indexOf("ascdesktopeditor")<0)return false;
if(window.location&&window.location.protocol=="file:")return true;if(window.document&&window.document.currentScript&&0==window.document.currentScript.src.indexOf("file:///"))return true;return false}if(internal_isLocal()){fetch=undefined;getBinaryPromise=function(){var wasmPath="ascdesktop://fonts/"+wasmBinaryFile.substr(8);return new Promise(function(resolve,reject){var xhr=new XMLHttpRequest;xhr.open("GET",wasmPath,true);xhr.responseType="arraybuffer";if(xhr.overrideMimeType)xhr.overrideMimeType("text/plain; charset=x-user-defined");
else xhr.setRequestHeader("Accept-Charset","x-user-defined");xhr.onload=function(){if(this.status==200)resolve(new Uint8Array(this.response))};xhr.send(null)})}}else getBinaryPromise=function(){return getBinaryPromise2.apply(undefined,arguments)};(function(){if(undefined!==String.prototype.fromUtf8&&undefined!==String.prototype.toUtf8)return;var STRING_UTF8_BUFFER_LENGTH=1024;var STRING_UTF8_BUFFER=new ArrayBuffer(STRING_UTF8_BUFFER_LENGTH);String.prototype.fromUtf8=function(buffer,start,len){if(undefined===
start)start=0;if(undefined===len)len=buffer.length-start;var result="";var index=start;var end=start+len;while(index<end){var u0=buffer[index++];if(!(u0&128)){result+=String.fromCharCode(u0);continue}var u1=buffer[index++]&63;if((u0&224)==192){result+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=buffer[index++]&63;if((u0&240)==224)u0=(u0&15)<<12|u1<<6|u2;else u0=(u0&7)<<18|u1<<12|u2<<6|buffer[index++]&63;if(u0<65536)result+=String.fromCharCode(u0);else{var ch=u0-65536;result+=String.fromCharCode(55296|
ch>>10,56320|ch&1023)}}return result};String.prototype.toUtf8=function(isNoEndNull,isUseBuffer){var inputLen=this.length;var testLen=6*inputLen+1;var tmpStrings=isUseBuffer&&testLen<STRING_UTF8_BUFFER_LENGTH?STRING_UTF8_BUFFER:new ArrayBuffer(testLen);var code=0;var index=0;var outputIndex=0;var outputDataTmp=new Uint8Array(tmpStrings);var outputData=outputDataTmp;while(index<inputLen){code=this.charCodeAt(index++);if(code>=55296&&code<=57343&&index<inputLen)code=65536+((code&1023)<<10|1023&this.charCodeAt(index++));
if(code<128)outputData[outputIndex++]=code;else if(code<2048){outputData[outputIndex++]=192|code>>6;outputData[outputIndex++]=128|code&63}else if(code<65536){outputData[outputIndex++]=224|code>>12;outputData[outputIndex++]=128|code>>6&63;outputData[outputIndex++]=128|code&63}else if(code<2097151){outputData[outputIndex++]=240|code>>18;outputData[outputIndex++]=128|code>>12&63;outputData[outputIndex++]=128|code>>6&63;outputData[outputIndex++]=128|code&63}else if(code<67108863){outputData[outputIndex++]=
248|code>>24;outputData[outputIndex++]=128|code>>18&63;outputData[outputIndex++]=128|code>>12&63;outputData[outputIndex++]=128|code>>6&63;outputData[outputIndex++]=128|code&63}else if(code<2147483647){outputData[outputIndex++]=252|code>>30;outputData[outputIndex++]=128|code>>24&63;outputData[outputIndex++]=128|code>>18&63;outputData[outputIndex++]=128|code>>12&63;outputData[outputIndex++]=128|code>>6&63;outputData[outputIndex++]=128|code&63}}if(isNoEndNull!==true)outputData[outputIndex++]=0;return new Uint8Array(tmpStrings,
0,outputIndex)};function StringPointer(pointer,len){this.ptr=pointer;this.length=len}StringPointer.prototype.free=function(){if(0!==this.ptr)Module["_free"](this.ptr)};String.prototype.toUtf8Pointer=function(isNoEndNull){var tmp=this.toUtf8(isNoEndNull,true);var pointer=Module["_malloc"](tmp.length);if(0==pointer)return null;Module["HEAP8"].set(tmp,pointer);return new StringPointer(pointer,tmp.length)}})();var Module=typeof Module!="undefined"?Module:{};var moduleOverrides=Object.assign({},Module);
var arguments_=[];var thisProgram="./this.program";var quit_=function(status,toThrow){throw toThrow;};var ENVIRONMENT_IS_WEB=true;var ENVIRONMENT_IS_WORKER=false;var scriptDirectory="";function locateFile(path){if(Module["locateFile"])return Module["locateFile"](path,scriptDirectory);return scriptDirectory+path}var read_,readAsync,readBinary;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER)scriptDirectory=self.location.href;else if(typeof document!="undefined"&&document.currentScript)scriptDirectory=
document.currentScript.src;if(scriptDirectory.indexOf("blob:")!==0)scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1);else scriptDirectory="";{read_=function(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER)readBinary=function(url){var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)};readAsync=function(url,
onload,onerror){var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=function(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}}else;var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.error.bind(console);Object.assign(Module,moduleOverrides);moduleOverrides=null;if(Module["arguments"])arguments_=Module["arguments"];if(Module["thisProgram"])thisProgram=
Module["thisProgram"];if(Module["quit"])quit_=Module["quit"];var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];if(typeof WebAssembly!="object")abort("no native wasm support detected");var wasmMemory;var ABORT=false;var EXITSTATUS;var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateMemoryViews(){var b=wasmMemory.buffer;Module["HEAP8"]=HEAP8=new Int8Array(b);Module["HEAP16"]=HEAP16=new Int16Array(b);Module["HEAPU8"]=HEAPU8=new Uint8Array(b);Module["HEAPU16"]=
HEAPU16=new Uint16Array(b);Module["HEAP32"]=HEAP32=new Int32Array(b);Module["HEAPU32"]=HEAPU32=new Uint32Array(b);Module["HEAPF32"]=HEAPF32=new Float32Array(b);Module["HEAPF64"]=HEAPF64=new Float64Array(b)}var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[function(){window["AscViewer"]&&window["AscViewer"]["onLoadModule"]&&window["AscViewer"]["onLoadModule"]()}];var runtimeInitialized=false;function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];
while(Module["preRun"].length)addOnPreRun(Module["preRun"].shift())}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function postRun(){if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length)addOnPostRun(Module["postRun"].shift())}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}
function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module["monitorRunDependencies"])Module["monitorRunDependencies"](runDependencies)}function removeRunDependency(id){runDependencies--;if(Module["monitorRunDependencies"])Module["monitorRunDependencies"](runDependencies);if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=
null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){if(Module["onAbort"])Module["onAbort"](what);what="Aborted("+what+")";err(what);ABORT=true;EXITSTATUS=1;what+=". Build with -sASSERTIONS for more info.";var e=new WebAssembly.RuntimeError(what);throw e;}var dataURIPrefix="data:application/octet-stream;base64,";var isDataURI=function(filename){return filename.startsWith(dataURIPrefix)};var wasmBinaryFile;wasmBinaryFile="drawingfile.wasm";
if(!isDataURI(wasmBinaryFile))wasmBinaryFile=locateFile(wasmBinaryFile);function getBinarySync(file){if(file==wasmBinaryFile&&wasmBinary)return new Uint8Array(wasmBinary);if(readBinary)return readBinary(file);throw"both async and sync fetching of the wasm failed";}function getBinaryPromise2(binaryFile){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER))if(typeof fetch=="function")return fetch(binaryFile,{credentials:"same-origin"}).then(function(response){if(!response["ok"])throw"failed to load wasm binary file at '"+
binaryFile+"'";return response["arrayBuffer"]()}).catch(function(){return getBinarySync(binaryFile)});return Promise.resolve().then(function(){return getBinarySync(binaryFile)})}function instantiateArrayBuffer(binaryFile,imports,receiver){return getBinaryPromise(binaryFile).then(function(binary){return WebAssembly.instantiate(binary,imports)}).then(function(instance){return instance}).then(receiver,function(reason){err("failed to asynchronously prepare wasm: "+reason);abort(reason)})}function instantiateAsync(binary,
binaryFile,imports,callback){if(!binary&&typeof WebAssembly.instantiateStreaming=="function"&&!isDataURI(binaryFile)&&typeof fetch=="function")return fetch(binaryFile,{credentials:"same-origin"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,imports);return result.then(callback,function(reason){err("wasm streaming compile failed: "+reason);err("falling back to ArrayBuffer instantiation");return instantiateArrayBuffer(binaryFile,imports,callback)})});return instantiateArrayBuffer(binaryFile,
imports,callback)}function createWasm(){var info={"a":wasmImports};function receiveInstance(instance,module){wasmExports=instance.exports;wasmMemory=wasmExports["ib"];updateMemoryViews();wasmTable=wasmExports["kb"];addOnInit(wasmExports["jb"]);removeRunDependency("wasm-instantiate");return wasmExports}addRunDependency("wasm-instantiate");function receiveInstantiationResult(result){receiveInstance(result["instance"])}if(Module["instantiateWasm"])try{return Module["instantiateWasm"](info,receiveInstance)}catch(e){err("Module.instantiateWasm callback failed with error: "+
e);return false}instantiateAsync(wasmBinary,wasmBinaryFile,info,receiveInstantiationResult);return{}}var tempDouble;function js_get_stream_id(data,status){return self.AscViewer.CheckStreamId(data,status)}function js_free_id(data){self.AscViewer.Free(data);return 1}function ExitStatus(status){this.name="ExitStatus";this.message="Program terminated with exit("+status+")";this.status=status}var callRuntimeCallbacks=function(callbacks){while(callbacks.length>0)callbacks.shift()(Module)};var noExitRuntime=
Module["noExitRuntime"]||true;var UTF8Decoder=typeof TextDecoder!="undefined"?new TextDecoder("utf8"):undefined;var UTF8ArrayToString=function(heapOrArray,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder)return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr));var str="";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&
63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224)u0=(u0&15)<<12|u1<<6|u2;else u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63;if(u0<65536)str+=String.fromCharCode(u0);else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str};var UTF8ToString=function(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):""};var ___assert_fail=function(condition,filename,line,func){abort("Assertion failed: "+
UTF8ToString(condition)+", at: "+[filename?UTF8ToString(filename):"unknown filename",line,func?UTF8ToString(func):"unknown function"])};var exceptionCaught=[];var uncaughtExceptionCount=0;var ___cxa_begin_catch=function(ptr){var info=new ExceptionInfo(ptr);if(!info.get_caught()){info.set_caught(true);uncaughtExceptionCount--}info.set_rethrown(false);exceptionCaught.push(info);___cxa_increment_exception_refcount(info.excPtr);return info.get_exception_ptr()};var exceptionLast=0;var ___cxa_end_catch=
function(){_setThrew(0,0);var info=exceptionCaught.pop();___cxa_decrement_exception_refcount(info.excPtr);exceptionLast=0};function ExceptionInfo(excPtr){this.excPtr=excPtr;this.ptr=excPtr-24;this.set_type=function(type){HEAPU32[this.ptr+4>>2]=type};this.get_type=function(){return HEAPU32[this.ptr+4>>2]};this.set_destructor=function(destructor){HEAPU32[this.ptr+8>>2]=destructor};this.get_destructor=function(){return HEAPU32[this.ptr+8>>2]};this.set_caught=function(caught){caught=caught?1:0;HEAP8[this.ptr+
12>>0]=caught};this.get_caught=function(){return HEAP8[this.ptr+12>>0]!=0};this.set_rethrown=function(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13>>0]=rethrown};this.get_rethrown=function(){return HEAP8[this.ptr+13>>0]!=0};this.init=function(type,destructor){this.set_adjusted_ptr(0);this.set_type(type);this.set_destructor(destructor)};this.set_adjusted_ptr=function(adjustedPtr){HEAPU32[this.ptr+16>>2]=adjustedPtr};this.get_adjusted_ptr=function(){return HEAPU32[this.ptr+16>>2]};this.get_exception_ptr=
function(){var isPointer=___cxa_is_pointer_type(this.get_type());if(isPointer)return HEAPU32[this.excPtr>>2];var adjusted=this.get_adjusted_ptr();if(adjusted!==0)return adjusted;return this.excPtr}}var ___resumeException=function(ptr){if(!exceptionLast)exceptionLast=ptr;throw exceptionLast;};var findMatchingCatch=function(args){var thrown=exceptionLast;if(!thrown){setTempRet0(0);return 0}var info=new ExceptionInfo(thrown);info.set_adjusted_ptr(thrown);var thrownType=info.get_type();if(!thrownType){setTempRet0(0);
return thrown}for(var arg in args){var caughtType=args[arg];if(caughtType===0||caughtType===thrownType)break;var adjusted_ptr_addr=info.ptr+16;if(___cxa_can_catch(caughtType,thrownType,adjusted_ptr_addr)){setTempRet0(caughtType);return thrown}}setTempRet0(thrownType);return thrown};var ___cxa_find_matching_catch_2=function(){return findMatchingCatch([])};var ___cxa_find_matching_catch_3=function(arg0){return findMatchingCatch([arg0])};var ___cxa_rethrow=function(){var info=exceptionCaught.pop();if(!info)abort("no exception to throw");
var ptr=info.excPtr;if(!info.get_rethrown()){exceptionCaught.push(info);info.set_rethrown(true);info.set_caught(false);uncaughtExceptionCount++}exceptionLast=ptr;throw exceptionLast;};var ___cxa_throw=function(ptr,type,destructor){var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw exceptionLast;};var ___cxa_uncaught_exceptions=function(){return uncaughtExceptionCount};var SYSCALLS={varargs:undefined,get:function(){var ret=HEAP32[+SYSCALLS.varargs>>
2];SYSCALLS.varargs+=4;return ret},getp:function(){return SYSCALLS.get()},getStr:function(ptr){var ret=UTF8ToString(ptr);return ret}};function ___syscall_fcntl64(fd,cmd,varargs){SYSCALLS.varargs=varargs;return 0}var ___syscall_fstat64=function(fd,buf){};var lengthBytesUTF8=function(str){var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127)len++;else if(c<=2047)len+=2;else if(c>=55296&&c<=57343){len+=4;++i}else len+=3}return len};var stringToUTF8Array=function(str,heap,outIdx,
maxBytesToWrite){if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&
63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx};var stringToUTF8=function(str,outPtr,maxBytesToWrite){return stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite)};var ___syscall_getcwd=function(buf,size){};var ___syscall_getdents64=function(fd,dirp,count){};function ___syscall_ioctl(fd,op,varargs){SYSCALLS.varargs=varargs;return 0}var ___syscall_lstat64=function(path,buf){};
var ___syscall_mkdirat=function(dirfd,path,mode){};var ___syscall_newfstatat=function(dirfd,path,buf,flags){};function ___syscall_openat(dirfd,path,flags,varargs){SYSCALLS.varargs=varargs}var ___syscall_readlinkat=function(dirfd,path,buf,bufsize){};var ___syscall_rmdir=function(path){};var ___syscall_stat64=function(path,buf){};var ___syscall_unlinkat=function(dirfd,path,flags){};var ___syscall_utimensat=function(dirfd,path,times,flags){};var nowIsMonotonic=true;var __emscripten_get_now_is_monotonic=
function(){return nowIsMonotonic};var __emscripten_throw_longjmp=function(){throw Infinity;};var isLeapYear=function(year){return year%4===0&&(year%100!==0||year%400===0)};var MONTH_DAYS_LEAP_CUMULATIVE=[0,31,60,91,121,152,182,213,244,274,305,335];var MONTH_DAYS_REGULAR_CUMULATIVE=[0,31,59,90,120,151,181,212,243,273,304,334];var ydayFromDate=function(date){var leap=isLeapYear(date.getFullYear());var monthDaysCumulative=leap?MONTH_DAYS_LEAP_CUMULATIVE:MONTH_DAYS_REGULAR_CUMULATIVE;var yday=monthDaysCumulative[date.getMonth()]+
date.getDate()-1;return yday};var convertI32PairToI53Checked=function(lo,hi){return hi+2097152>>>0<4194305-!!lo?(lo>>>0)+hi*4294967296:NaN};var __mktime_js=function(tmPtr){var ret=function(){var date=new Date(HEAP32[tmPtr+20>>2]+1900,HEAP32[tmPtr+16>>2],HEAP32[tmPtr+12>>2],HEAP32[tmPtr+8>>2],HEAP32[tmPtr+4>>2],HEAP32[tmPtr>>2],0);var dst=HEAP32[tmPtr+32>>2];var guessedOffset=date.getTimezoneOffset();var start=new Date(date.getFullYear(),0,1);var summerOffset=(new Date(date.getFullYear(),6,1)).getTimezoneOffset();
var winterOffset=start.getTimezoneOffset();var dstOffset=Math.min(winterOffset,summerOffset);if(dst<0)HEAP32[tmPtr+32>>2]=Number(summerOffset!=winterOffset&&dstOffset==guessedOffset);else if(dst>0!=(dstOffset==guessedOffset)){var nonDstOffset=Math.max(winterOffset,summerOffset);var trueOffset=dst>0?dstOffset:nonDstOffset;date.setTime(date.getTime()+(trueOffset-guessedOffset)*6E4)}HEAP32[tmPtr+24>>2]=date.getDay();var yday=ydayFromDate(date)|0;HEAP32[tmPtr+28>>2]=yday;HEAP32[tmPtr>>2]=date.getSeconds();
HEAP32[tmPtr+4>>2]=date.getMinutes();HEAP32[tmPtr+8>>2]=date.getHours();HEAP32[tmPtr+12>>2]=date.getDate();HEAP32[tmPtr+16>>2]=date.getMonth();HEAP32[tmPtr+20>>2]=date.getYear();return date.getTime()/1E3}();return setTempRet0((tempDouble=ret,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)),ret>>>0};function __mmap_js(len,prot,flags,fd,offset_low,offset_high,allocated,addr){var offset=convertI32PairToI53Checked(offset_low,
offset_high);return-52}function __munmap_js(addr,len,prot,flags,fd,offset_low,offset_high){var offset=convertI32PairToI53Checked(offset_low,offset_high)}var stringToNewUTF8=function(str){var size=lengthBytesUTF8(str)+1;var ret=_malloc(size);if(ret)stringToUTF8(str,ret,size);return ret};var __tzset_js=function(timezone,daylight,tzname){var currentYear=(new Date).getFullYear();var winter=new Date(currentYear,0,1);var summer=new Date(currentYear,6,1);var winterOffset=winter.getTimezoneOffset();var summerOffset=
summer.getTimezoneOffset();var stdTimezoneOffset=Math.max(winterOffset,summerOffset);HEAPU32[timezone>>2]=stdTimezoneOffset*60;HEAP32[daylight>>2]=Number(winterOffset!=summerOffset);function extractZone(date){var match=date.toTimeString().match(/\(([A-Za-z ]+)\)$/);return match?match[1]:"GMT"}var winterName=extractZone(winter);var summerName=extractZone(summer);var winterNamePtr=stringToNewUTF8(winterName);var summerNamePtr=stringToNewUTF8(summerName);if(summerOffset<winterOffset){HEAPU32[tzname>>
2]=winterNamePtr;HEAPU32[tzname+4>>2]=summerNamePtr}else{HEAPU32[tzname>>2]=summerNamePtr;HEAPU32[tzname+4>>2]=winterNamePtr}};var _abort=function(){abort("")};var _emscripten_date_now=function(){return Date.now()};var _emscripten_get_now;_emscripten_get_now=function(){return performance.now()};var _emscripten_memcpy_js=function(dest,src,num){return HEAPU8.copyWithin(dest,src,src+num)};var getHeapMax=function(){return 2147483648};var growMemory=function(size){var b=wasmMemory.buffer;var pages=(size-
b.byteLength+65535)/65536;try{wasmMemory.grow(pages);updateMemoryViews();return 1}catch(e){}};var _emscripten_resize_heap=function(requestedSize){var oldSize=HEAPU8.length;requestedSize>>>=0;var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize)return false;var alignUp=function(x,multiple){return x+(multiple-x%multiple)%multiple};for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=
Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=growMemory(newSize);if(replacement)return true}return false};var ENV={};var getExecutableName=function(){return thisProgram||"./this.program"};var getEnvStrings=function(){if(!getEnvStrings.strings){var lang=(typeof navigator=="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8";var env={"USER":"web_user","LOGNAME":"web_user","PATH":"/","PWD":"/","HOME":"/home/<USER>",
"LANG":lang,"_":getExecutableName()};for(var x in ENV)if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x];var strings=[];for(var x in env)strings.push(x+"="+env[x]);getEnvStrings.strings=strings}return getEnvStrings.strings};var stringToAscii=function(str,buffer){for(var i=0;i<str.length;++i)HEAP8[buffer++>>0]=str.charCodeAt(i);HEAP8[buffer>>0]=0};var _environ_get=function(__environ,environ_buf){var bufSize=0;getEnvStrings().forEach(function(string,i){var ptr=environ_buf+bufSize;HEAPU32[__environ+
i*4>>2]=ptr;stringToAscii(string,ptr);bufSize+=string.length+1});return 0};var _environ_sizes_get=function(penviron_count,penviron_buf_size){var strings=getEnvStrings();HEAPU32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(function(string){return bufSize+=string.length+1});HEAPU32[penviron_buf_size>>2]=bufSize;return 0};var runtimeKeepaliveCounter=0;var keepRuntimeAlive=function(){return noExitRuntime||runtimeKeepaliveCounter>0};var _proc_exit=function(code){EXITSTATUS=code;if(!keepRuntimeAlive()){if(Module["onExit"])Module["onExit"](code);
ABORT=true}quit_(code,new ExitStatus(code))};var exitJS=function(status,implicit){EXITSTATUS=status;_proc_exit(status)};var _exit=exitJS;var _fd_close=function(fd){return 52};var _fd_read=function(fd,iov,iovcnt,pnum){return 52};function _fd_seek(fd,offset_low,offset_high,whence,newOffset){var offset=convertI32PairToI53Checked(offset_low,offset_high);return 70}var printCharBuffers=[null,[],[]];var printChar=function(stream,curr){var buffer=printCharBuffers[stream];if(curr===0||curr===10){(stream===
1?out:err)(UTF8ArrayToString(buffer,0));buffer.length=0}else buffer.push(curr)};var _fd_write=function(fd,iov,iovcnt,pnum){var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;for(var j=0;j<len;j++)printChar(fd,HEAPU8[ptr+j]);num+=len}HEAPU32[pnum>>2]=num;return 0};var _llvm_eh_typeid_for=function(type){return type};var arraySum=function(array,index){var sum=0;for(var i=0;i<=index;sum+=array[i++]);return sum};var MONTH_DAYS_LEAP=[31,29,31,30,31,30,31,31,30,
31,30,31];var MONTH_DAYS_REGULAR=[31,28,31,30,31,30,31,31,30,31,30,31];var addDays=function(date,days){var newDate=new Date(date.getTime());while(days>0){var leap=isLeapYear(newDate.getFullYear());var currentMonth=newDate.getMonth();var daysInCurrentMonth=(leap?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR)[currentMonth];if(days>daysInCurrentMonth-newDate.getDate()){days-=daysInCurrentMonth-newDate.getDate()+1;newDate.setDate(1);if(currentMonth<11)newDate.setMonth(currentMonth+1);else{newDate.setMonth(0);newDate.setFullYear(newDate.getFullYear()+
1)}}else{newDate.setDate(newDate.getDate()+days);return newDate}}return newDate};function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var writeArrayToMemory=function(array,buffer){HEAP8.set(array,buffer)};var _strftime=function(s,maxsize,format,tm){var tm_zone=HEAPU32[tm+40>>2];var date=
{tm_sec:HEAP32[tm>>2],tm_min:HEAP32[tm+4>>2],tm_hour:HEAP32[tm+8>>2],tm_mday:HEAP32[tm+12>>2],tm_mon:HEAP32[tm+16>>2],tm_year:HEAP32[tm+20>>2],tm_wday:HEAP32[tm+24>>2],tm_yday:HEAP32[tm+28>>2],tm_isdst:HEAP32[tm+32>>2],tm_gmtoff:HEAP32[tm+36>>2],tm_zone:tm_zone?UTF8ToString(tm_zone):""};var pattern=UTF8ToString(format);var EXPANSION_RULES_1={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c",
"%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var rule in EXPANSION_RULES_1)pattern=pattern.replace(new RegExp(rule,"g"),EXPANSION_RULES_1[rule]);var WEEKDAYS=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];var MONTHS=["January","February","March","April","May","June","July","August","September","October",
"November","December"];function leadingSomething(value,digits,character){var str=typeof value=="number"?value.toString():value||"";while(str.length<digits)str=character[0]+str;return str}function leadingNulls(value,digits){return leadingSomething(value,digits,"0")}function compareByDay(date1,date2){function sgn(value){return value<0?-1:value>0?1:0}var compare;if((compare=sgn(date1.getFullYear()-date2.getFullYear()))===0)if((compare=sgn(date1.getMonth()-date2.getMonth()))===0)compare=sgn(date1.getDate()-
date2.getDate());return compare}function getFirstWeekStartDate(janFourth){switch(janFourth.getDay()){case 0:return new Date(janFourth.getFullYear()-1,11,29);case 1:return janFourth;case 2:return new Date(janFourth.getFullYear(),0,3);case 3:return new Date(janFourth.getFullYear(),0,2);case 4:return new Date(janFourth.getFullYear(),0,1);case 5:return new Date(janFourth.getFullYear()-1,11,31);case 6:return new Date(janFourth.getFullYear()-1,11,30)}}function getWeekBasedYear(date){var thisDate=addDays(new Date(date.tm_year+
1900,0,1),date.tm_yday);var janFourthThisYear=new Date(thisDate.getFullYear(),0,4);var janFourthNextYear=new Date(thisDate.getFullYear()+1,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);if(compareByDay(firstWeekStartThisYear,thisDate)<=0){if(compareByDay(firstWeekStartNextYear,thisDate)<=0)return thisDate.getFullYear()+1;return thisDate.getFullYear()}return thisDate.getFullYear()-1}var EXPANSION_RULES_2=
{"%a":function(date){return WEEKDAYS[date.tm_wday].substring(0,3)},"%A":function(date){return WEEKDAYS[date.tm_wday]},"%b":function(date){return MONTHS[date.tm_mon].substring(0,3)},"%B":function(date){return MONTHS[date.tm_mon]},"%C":function(date){var year=date.tm_year+1900;return leadingNulls(year/100|0,2)},"%d":function(date){return leadingNulls(date.tm_mday,2)},"%e":function(date){return leadingSomething(date.tm_mday,2," ")},"%g":function(date){return getWeekBasedYear(date).toString().substring(2)},
"%G":function(date){return getWeekBasedYear(date)},"%H":function(date){return leadingNulls(date.tm_hour,2)},"%I":function(date){var twelveHour=date.tm_hour;if(twelveHour==0)twelveHour=12;else if(twelveHour>12)twelveHour-=12;return leadingNulls(twelveHour,2)},"%j":function(date){return leadingNulls(date.tm_mday+arraySum(isLeapYear(date.tm_year+1900)?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR,date.tm_mon-1),3)},"%m":function(date){return leadingNulls(date.tm_mon+1,2)},"%M":function(date){return leadingNulls(date.tm_min,
2)},"%n":function(){return"\n"},"%p":function(date){if(date.tm_hour>=0&&date.tm_hour<12)return"AM";return"PM"},"%S":function(date){return leadingNulls(date.tm_sec,2)},"%t":function(){return"\t"},"%u":function(date){return date.tm_wday||7},"%U":function(date){var days=date.tm_yday+7-date.tm_wday;return leadingNulls(Math.floor(days/7),2)},"%V":function(date){var val=Math.floor((date.tm_yday+7-(date.tm_wday+6)%7)/7);if((date.tm_wday+371-date.tm_yday-2)%7<=2)val++;if(!val){val=52;var dec31=(date.tm_wday+
7-date.tm_yday-1)%7;if(dec31==4||dec31==5&&isLeapYear(date.tm_year%400-1))val++}else if(val==53){var jan1=(date.tm_wday+371-date.tm_yday)%7;if(jan1!=4&&(jan1!=3||!isLeapYear(date.tm_year)))val=1}return leadingNulls(val,2)},"%w":function(date){return date.tm_wday},"%W":function(date){var days=date.tm_yday+7-(date.tm_wday+6)%7;return leadingNulls(Math.floor(days/7),2)},"%y":function(date){return(date.tm_year+1900).toString().substring(2)},"%Y":function(date){return date.tm_year+1900},"%z":function(date){var off=
date.tm_gmtoff;var ahead=off>=0;off=Math.abs(off)/60;off=off/60*100+off%60;return(ahead?"+":"-")+String("0000"+off).slice(-4)},"%Z":function(date){return date.tm_zone},"%%":function(){return"%"}};pattern=pattern.replace(/%%/g,"\x00\x00");for(var rule in EXPANSION_RULES_2)if(pattern.includes(rule))pattern=pattern.replace(new RegExp(rule,"g"),EXPANSION_RULES_2[rule](date));pattern=pattern.replace(/\0\0/g,"%");var bytes=intArrayFromString(pattern,false);if(bytes.length>maxsize)return 0;writeArrayToMemory(bytes,
s);return bytes.length-1};var _strftime_l=function(s,maxsize,format,tm,loc){return _strftime(s,maxsize,format,tm)};var wasmTableMirror=[];var wasmTable;var getWasmTableEntry=function(funcPtr){var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func};var wasmImports={i:___assert_fail,r:___cxa_begin_catch,w:___cxa_end_catch,a:___cxa_find_matching_catch_2,h:___cxa_find_matching_catch_3,
Q:___cxa_rethrow,A:___cxa_throw,db:___cxa_uncaught_exceptions,e:___resumeException,ca:___syscall_fcntl64,ya:___syscall_fstat64,ua:___syscall_getcwd,qa:___syscall_getdents64,Ba:___syscall_ioctl,va:___syscall_lstat64,ra:___syscall_mkdirat,wa:___syscall_newfstatat,_:___syscall_openat,Z:___syscall_readlinkat,pa:___syscall_rmdir,xa:___syscall_stat64,Y:___syscall_unlinkat,gb:___syscall_utimensat,za:__emscripten_get_now_is_monotonic,eb:__emscripten_throw_longjmp,$a:__mktime_js,Za:__mmap_js,_a:__munmap_js,
hb:__tzset_js,u:_abort,aa:_emscripten_date_now,$:_emscripten_get_now,Aa:_emscripten_memcpy_js,fb:_emscripten_resize_heap,sa:_environ_get,ta:_environ_sizes_get,B:_exit,P:_fd_close,ba:_fd_read,ab:_fd_seek,U:_fd_write,t:invoke_di,ma:invoke_didd,R:invoke_dii,G:invoke_diii,Ga:invoke_fif,oa:invoke_fiii,s:invoke_i,Ta:invoke_idddiii,b:invoke_ii,z:invoke_iidd,D:invoke_iidddddd,Ma:invoke_iiddiii,d:invoke_iii,ia:invoke_iiiddddd,Oa:invoke_iiiddiii,S:invoke_iiiff,Pa:invoke_iiiffff,j:invoke_iiii,k:invoke_iiiii,
ea:invoke_iiiiid,Va:invoke_iiiiifi,q:invoke_iiiiii,V:invoke_iiiiiiddiiiii,m:invoke_iiiiiii,y:invoke_iiiiiiii,C:invoke_iiiiiiiii,J:invoke_iiiiiiiiidddd,L:invoke_iiiiiiiiii,W:invoke_iiiiiiiiiii,I:invoke_iiiiiiiiiiii,ka:invoke_iiiiiiiiiiiiiiiiiiiiiiiiiii,bb:invoke_ji,Ya:invoke_jiiii,n:invoke_v,Ia:invoke_vdii,c:invoke_vi,O:invoke_vid,X:invoke_vidd,la:invoke_viddd,Ua:invoke_vidddddddd,Da:invoke_viddddiiiiiii,Wa:invoke_viddi,na:invoke_vidi,Qa:invoke_viffffi,g:invoke_vii,E:invoke_viid,La:invoke_viidddd,
Ka:invoke_viiddddddi,Ca:invoke_viiddiiiii,Ha:invoke_viif,f:invoke_viii,Ja:invoke_viiid,F:invoke_viiiddiii,Ea:invoke_viiiddiiiii,ha:invoke_viiiddiiiiii,ga:invoke_viiidi,Na:invoke_viiidiiiddddd,l:invoke_viiii,H:invoke_viiiid,da:invoke_viiiidii,p:invoke_viiiii,fa:invoke_viiiiid,ja:invoke_viiiiiff,o:invoke_viiiiii,x:invoke_viiiiiii,K:invoke_viiiiiiii,M:invoke_viiiiiiiii,N:invoke_viiiiiiiiii,Fa:invoke_viiiiiiiiiiii,Xa:invoke_viiiiiiiiiiiiii,T:invoke_viiiiiiiiiiiiiii,Ra:js_free_id,Sa:js_get_stream_id,v:_llvm_eh_typeid_for,
cb:_strftime_l};var wasmExports=createWasm();var ___wasm_call_ctors=function(){return(___wasm_call_ctors=wasmExports["jb"])()};var ___cxa_free_exception=function(a0){return(___cxa_free_exception=wasmExports["__cxa_free_exception"])(a0)};var _malloc=Module["_malloc"]=function(a0){return(_malloc=Module["_malloc"]=wasmExports["lb"])(a0)};var _free=Module["_free"]=function(a0){return(_free=Module["_free"]=wasmExports["mb"])(a0)};var setTempRet0=function(a0){return(setTempRet0=wasmExports["nb"])(a0)};
var ___errno_location=function(){return(___errno_location=wasmExports["ob"])()};var _InitializeFontsBin=Module["_InitializeFontsBin"]=function(a0,a1){return(_InitializeFontsBin=Module["_InitializeFontsBin"]=wasmExports["pb"])(a0,a1)};var _InitializeFontsBase64=Module["_InitializeFontsBase64"]=function(a0,a1){return(_InitializeFontsBase64=Module["_InitializeFontsBase64"]=wasmExports["qb"])(a0,a1)};var _InitializeFontsRanges=Module["_InitializeFontsRanges"]=function(a0){return(_InitializeFontsRanges=
Module["_InitializeFontsRanges"]=wasmExports["rb"])(a0)};var _SetFontBinary=Module["_SetFontBinary"]=function(a0,a1,a2){return(_SetFontBinary=Module["_SetFontBinary"]=wasmExports["sb"])(a0,a1,a2)};var _IsFontBinaryExist=Module["_IsFontBinaryExist"]=function(a0){return(_IsFontBinaryExist=Module["_IsFontBinaryExist"]=wasmExports["tb"])(a0)};var _GetType=Module["_GetType"]=function(a0,a1){return(_GetType=Module["_GetType"]=wasmExports["ub"])(a0,a1)};var _Open=Module["_Open"]=function(a0,a1,a2){return(_Open=
Module["_Open"]=wasmExports["vb"])(a0,a1,a2)};var _GetErrorCode=Module["_GetErrorCode"]=function(a0){return(_GetErrorCode=Module["_GetErrorCode"]=wasmExports["wb"])(a0)};var _Close=Module["_Close"]=function(a0){return(_Close=Module["_Close"]=wasmExports["xb"])(a0)};var _GetInfo=Module["_GetInfo"]=function(a0){return(_GetInfo=Module["_GetInfo"]=wasmExports["yb"])(a0)};var _GetPixmap=Module["_GetPixmap"]=function(a0,a1,a2,a3,a4){return(_GetPixmap=Module["_GetPixmap"]=wasmExports["zb"])(a0,a1,a2,a3,
a4)};var _GetGlyphs=Module["_GetGlyphs"]=function(a0,a1){return(_GetGlyphs=Module["_GetGlyphs"]=wasmExports["Ab"])(a0,a1)};var _GetLinks=Module["_GetLinks"]=function(a0,a1){return(_GetLinks=Module["_GetLinks"]=wasmExports["Bb"])(a0,a1)};var _GetStructure=Module["_GetStructure"]=function(a0){return(_GetStructure=Module["_GetStructure"]=wasmExports["Cb"])(a0)};var _GetInteractiveFormsInfo=Module["_GetInteractiveFormsInfo"]=function(a0){return(_GetInteractiveFormsInfo=Module["_GetInteractiveFormsInfo"]=
wasmExports["Db"])(a0)};var _GetInteractiveFormsFonts=Module["_GetInteractiveFormsFonts"]=function(a0,a1){return(_GetInteractiveFormsFonts=Module["_GetInteractiveFormsFonts"]=wasmExports["Eb"])(a0,a1)};var _GetInteractiveFormsAP=Module["_GetInteractiveFormsAP"]=function(a0,a1,a2,a3,a4,a5,a6,a7){return(_GetInteractiveFormsAP=Module["_GetInteractiveFormsAP"]=wasmExports["Fb"])(a0,a1,a2,a3,a4,a5,a6,a7)};var _GetButtonIcons=Module["_GetButtonIcons"]=function(a0,a1,a2,a3,a4,a5){return(_GetButtonIcons=
Module["_GetButtonIcons"]=wasmExports["Gb"])(a0,a1,a2,a3,a4,a5)};var _GetAnnotationsInfo=Module["_GetAnnotationsInfo"]=function(a0,a1){return(_GetAnnotationsInfo=Module["_GetAnnotationsInfo"]=wasmExports["Hb"])(a0,a1)};var _GetAnnotationsAP=Module["_GetAnnotationsAP"]=function(a0,a1,a2,a3,a4,a5,a6){return(_GetAnnotationsAP=Module["_GetAnnotationsAP"]=wasmExports["Ib"])(a0,a1,a2,a3,a4,a5,a6)};var _GetFontBinary=Module["_GetFontBinary"]=function(a0,a1){return(_GetFontBinary=Module["_GetFontBinary"]=
wasmExports["Jb"])(a0,a1)};var _DestroyTextInfo=Module["_DestroyTextInfo"]=function(a0){return(_DestroyTextInfo=Module["_DestroyTextInfo"]=wasmExports["Kb"])(a0)};var _IsNeedCMap=Module["_IsNeedCMap"]=function(a0){return(_IsNeedCMap=Module["_IsNeedCMap"]=wasmExports["Lb"])(a0)};var _SetCMapData=Module["_SetCMapData"]=function(a0,a1,a2){return(_SetCMapData=Module["_SetCMapData"]=wasmExports["Mb"])(a0,a1,a2)};var _ScanPage=Module["_ScanPage"]=function(a0,a1,a2){return(_ScanPage=Module["_ScanPage"]=
wasmExports["Nb"])(a0,a1,a2)};var _GetImageBase64=Module["_GetImageBase64"]=function(a0,a1){return(_GetImageBase64=Module["_GetImageBase64"]=wasmExports["Ob"])(a0,a1)};var _GetImageBase64Len=Module["_GetImageBase64Len"]=function(a0){return(_GetImageBase64Len=Module["_GetImageBase64Len"]=wasmExports["Pb"])(a0)};var _GetImageBase64Ptr=Module["_GetImageBase64Ptr"]=function(a0){return(_GetImageBase64Ptr=Module["_GetImageBase64Ptr"]=wasmExports["Qb"])(a0)};var _GetImageBase64Free=Module["_GetImageBase64Free"]=
function(a0){return(_GetImageBase64Free=Module["_GetImageBase64Free"]=wasmExports["Rb"])(a0)};var _setThrew=function(a0,a1){return(_setThrew=wasmExports["Sb"])(a0,a1)};var stackSave=function(){return(stackSave=wasmExports["Tb"])()};var stackRestore=function(a0){return(stackRestore=wasmExports["Ub"])(a0)};var ___cxa_decrement_exception_refcount=function(a0){return(___cxa_decrement_exception_refcount=wasmExports["Vb"])(a0)};var ___cxa_increment_exception_refcount=function(a0){return(___cxa_increment_exception_refcount=
wasmExports["Wb"])(a0)};var ___cxa_can_catch=function(a0,a1,a2){return(___cxa_can_catch=wasmExports["Xb"])(a0,a1,a2)};var ___cxa_is_pointer_type=function(a0){return(___cxa_is_pointer_type=wasmExports["Yb"])(a0)};var dynCall_ji=Module["dynCall_ji"]=function(a0,a1){return(dynCall_ji=Module["dynCall_ji"]=wasmExports["Zb"])(a0,a1)};var dynCall_jiiii=Module["dynCall_jiiii"]=function(a0,a1,a2,a3,a4){return(dynCall_jiiii=Module["dynCall_jiiii"]=wasmExports["_b"])(a0,a1,a2,a3,a4)};var ___start_em_js=Module["___start_em_js"]=
2763320;var ___stop_em_js=Module["___stop_em_js"]=2763489;function invoke_iii(index,a1,a2){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiii(index,a1,a2,a3,a4,a5){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiii(index,a1,a2,a3){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);
if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_ii(index,a1){var sp=stackSave();try{return getWasmTableEntry(index)(a1)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vii(index,a1,a2){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiiii(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(e!==e+0)throw e;
_setThrew(1,0)}}function invoke_iiiii(index,a1,a2,a3,a4){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viii(index,a1,a2,a3){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiiiii(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(e!==
e+0)throw e;_setThrew(1,0)}}function invoke_iiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vi(index,a1){var sp=stackSave();try{getWasmTableEntry(index)(a1)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiii(index,a1,a2,a3,a4,a5){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);
if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiii(index,a1,a2,a3,a4){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiii(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,
a7,a8,a9)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_v(index){var sp=stackSave();try{getWasmTableEntry(index)()}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiiiiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiiiiiii(index,a1,a2,a3,a4,a5,a6,
a7,a8,a9){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_i(index){var sp=stackSave();try{return getWasmTableEntry(index)()}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viddi(index,a1,a2,a3,a4){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vidi(index,a1,a2,a3){var sp=
stackSave();try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiifi(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiiii(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vid(index,
a1,a2){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vidd(index,a1,a2,a3){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iidddddd(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiiiii(index,
a1,a2,a3,a4,a5,a6,a7,a8){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iidd(index,a1,a2,a3){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vidddddddd(index,a1,a2,a3,a4,a5,a6,a7,a8,a9){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9)}catch(e){stackRestore(sp);if(e!==e+0)throw e;
_setThrew(1,0)}}function invoke_idddiii(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_didd(index,a1,a2,a3){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiddiii(index,a1,a2,a3,a4,a5,a6,a7,a8){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8)}catch(e){stackRestore(sp);
if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiiiiiidddd(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viddd(index,a1,a2,a3,a4){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiiiiiiiiiiiiiiiiiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,
a8,a9,a10,a11,a12,a13,a14,a15,a16,a17,a18,a19,a20,a21,a22,a23,a24,a25,a26){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14,a15,a16,a17,a18,a19,a20,a21,a22,a23,a24,a25,a26)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}
function invoke_iiiiiiddiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viffffi(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiiff(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{getWasmTableEntry(index)(a1,
a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiffff(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiff(index,a1,a2,a3,a4){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viid(index,a1,a2,a3){var sp=stackSave();try{getWasmTableEntry(index)(a1,
a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiddiii(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiidiiiddddd(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_dii(index,
a1,a2){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiddiii(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiddddd(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,
0)}}function invoke_viiiddiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_di(index,a1){var sp=stackSave();try{return getWasmTableEntry(index)(a1)}catch(e){stackRestore(sp);
if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viidddd(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiddddddi(index,a1,a2,a3,a4,a5,a6,a7,a8,a9){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiid(index,a1,a2,a3,a4,a5){var sp=stackSave();try{getWasmTableEntry(index)(a1,
a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiidi(index,a1,a2,a3,a4,a5){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiid(index,a1,a2,a3,a4){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_vdii(index,a1,a2,a3){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);
if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiid(index,a1,a2,a3,a4,a5,a6){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_iiiiid(index,a1,a2,a3,a4,a5){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3,a4,a5)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_diii(index,a1,a2,a3){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);
if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viif(index,a1,a2,a3){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_fif(index,a1,a2){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,
a11,a12)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiddiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viddddiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}
function invoke_viiiidii(index,a1,a2,a3,a4,a5,a6,a7){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiddiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_fiii(index,a1,a2,a3){var sp=stackSave();try{return getWasmTableEntry(index)(a1,a2,a3)}catch(e){stackRestore(sp);
if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_viiiiiiiiiiiiiii(index,a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14,a15){var sp=stackSave();try{getWasmTableEntry(index)(a1,a2,a3,a4,a5,a6,a7,a8,a9,a10,a11,a12,a13,a14,a15)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_ji(index,
a1){var sp=stackSave();try{return dynCall_ji(index,a1)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}function invoke_jiiii(index,a1,a2,a3,a4){var sp=stackSave();try{return dynCall_jiiii(index,a1,a2,a3,a4)}catch(e){stackRestore(sp);if(e!==e+0)throw e;_setThrew(1,0)}}var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(){if(runDependencies>0)return;preRun();if(runDependencies>0)return;function doRun(){if(calledRun)return;
calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else doRun()}if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0)Module["preInit"].pop()()}run();function CBinaryReader(data,start,
size){this.data=data;this.pos=start;this.limit=start+size}CBinaryReader.prototype.readByte=function(){var val=this.data[this.pos];this.pos+=1;return val};CBinaryReader.prototype.readInt=function(){var val=this.data[this.pos]|this.data[this.pos+1]<<8|this.data[this.pos+2]<<16|this.data[this.pos+3]<<24;this.pos+=4;return val};CBinaryReader.prototype.readDouble=function(){return this.readInt()/100};CBinaryReader.prototype.readDouble2=function(){return this.readInt()/1E4};CBinaryReader.prototype.readString=
function(){var len=this.readInt();var val=String.prototype.fromUtf8(this.data,this.pos,len);this.pos+=len;return val};CBinaryReader.prototype.readData=function(){var len=this.readInt();var val=this.data.slice(this.pos,this.pos+len);this.pos+=len;return val};CBinaryReader.prototype.isValid=function(){return this.pos<this.limit?true:false};CBinaryReader.prototype.Skip=function(nPos){this.pos+=nPos};function CBinaryWriter(){this.size=1E5;this.dataSize=0;this.buffer=new Uint8Array(this.size)}CBinaryWriter.prototype.checkAlloc=
function(addition){if(this.dataSize+addition<=this.size)return;var newSize=Math.max(this.size*2,this.size+addition);var newBuffer=new Uint8Array(newSize);newBuffer.set(this.buffer,0);this.size=newSize;this.buffer=newBuffer};CBinaryWriter.prototype.writeUint=function(value){this.checkAlloc(4);var val=value>2147483647?value-4294967296:value;this.buffer[this.dataSize++]=val&255;this.buffer[this.dataSize++]=val>>>8&255;this.buffer[this.dataSize++]=val>>>16&255;this.buffer[this.dataSize++]=val>>>24&255};
CBinaryWriter.prototype.writeString=function(value){var valueUtf8=value.toUtf8();this.checkAlloc(valueUtf8.length);this.buffer.set(valueUtf8,this.dataSize);this.dataSize+=valueUtf8.length};var UpdateFontsSource={Undefined:0,Page:1,Annotation:2,Forms:4};function CFile(){this.nativeFile=0;this.stream=-1;this.stream_size=0;this.type=-1;this.pages=[];this.info=null;this._isNeedPassword=false;this.fontPageIndex=-1;this.fontPageUpdateType=UpdateFontsSource.Undefined;this.fontStreams={};this.scannedImages=
{}}function CWasmPointer(){this.ptr=0}CWasmPointer.prototype.free=function(){Module["_free"](this.ptr);this.ptr=0};CWasmPointer.prototype.getReader=function(){if(!this.ptr)return null;var lenArr=new Int32Array(Module["HEAP8"].buffer,this.ptr,1);if(!lenArr){this.free();return null}var len=lenArr[0];if(len<=4){this.free();return null}len-=4;var buffer=new Uint8Array(Module["HEAP8"].buffer,this.ptr+4,len);return new CBinaryReader(buffer,0,len)};var g_module_pointer=new CWasmPointer;CFile.prototype._free=
function(ptr){Module["_free"](ptr)};CFile.prototype._getUint8Array=function(ptr,len){return new Uint8Array(Module["HEAP8"].buffer,ptr,len)};CFile.prototype._getUint8ClampedArray=function(ptr,len){return new Uint8ClampedArray(Module["HEAP8"].buffer,ptr,len)};CFile.prototype._openFile=function(buffer,password){if(buffer){var data=new Uint8Array(buffer);this.stream_size=data.length;this.stream=Module["_malloc"](this.stream_size);Module["HEAP8"].set(data,this.stream)}var passwordPtr=0;if(password){var passwordBuf=
password.toUtf8();passwordPtr=Module["_malloc"](passwordBuf.length);Module["HEAP8"].set(passwordBuf,passwordPtr)}this.nativeFile=Module["_Open"](this.stream,this.stream_size,passwordPtr);if(passwordPtr)Module["_free"](passwordPtr);return this.nativeFile>0?true:false};CFile.prototype._closeFile=function(){Module["_Close"](this.nativeFile)};CFile.prototype._getType=function(){return Module["_GetType"](this.stream,this.stream_size)};CFile.prototype._getError=function(){return Module["_GetErrorCode"](this.nativeFile)};
CFile.prototype._isNeedCMap=function(){var isNeed=Module["_IsNeedCMap"](this.nativeFile);return isNeed===1?true:false};CFile.prototype._setCMap=function(memoryBuffer){var pointer=Module["_malloc"](memoryBuffer.length);Module.HEAP8.set(memoryBuffer,pointer);Module["_SetCMapData"](this.nativeFile,pointer,memoryBuffer.length)};CFile.prototype._getFontByID=function(ID){if(ID===undefined)return null;var idBuffer=ID.toUtf8();var idPointer=Module["_malloc"](idBuffer.length);Module["HEAP8"].set(idBuffer,
idPointer);g_module_pointer.ptr=Module["_GetFontBinary"](this.nativeFile,idPointer);Module["_free"](idPointer);var reader=g_module_pointer.getReader();if(!reader)return null;var nFontLength=reader.readInt();var np1=reader.readInt();var np2=reader.readInt();var pFontPointer=np2<<32|np1;var res=new Uint8Array(Module["HEAP8"].buffer,pFontPointer,nFontLength);g_module_pointer.free();return res};CFile.prototype._getInteractiveFormsFonts=function(type){g_module_pointer.ptr=Module["_GetInteractiveFormsFonts"](this.nativeFile,
type);return g_module_pointer};CFile.prototype._getInfo=function(){g_module_pointer.ptr=Module["_GetInfo"](this.nativeFile);return g_module_pointer};CFile.prototype._getStructure=function(){g_module_pointer.ptr=Module["_GetStructure"](this.nativeFile);return g_module_pointer};CFile.prototype._getLinks=function(pageIndex){g_module_pointer.ptr=Module["_GetLinks"](this.nativeFile,pageIndex);return g_module_pointer};CFile.prototype._getInteractiveFormsInfo=function(){g_module_pointer.ptr=Module["_GetInteractiveFormsInfo"](this.nativeFile);
return g_module_pointer};CFile.prototype._getAnnotationsInfo=function(pageIndex){g_module_pointer.ptr=Module["_GetAnnotationsInfo"](this.nativeFile,pageIndex===undefined?-1:pageIndex);return g_module_pointer};CFile.prototype._getButtonIcons=function(backgroundColor,pageIndex,isBase64,nWidget,nView){g_module_pointer.ptr=Module["_GetButtonIcons"](this.nativeFile,backgroundColor===undefined?16777215:backgroundColor,pageIndex,isBase64?1:0,nWidget===undefined?-1:nWidget,nView);return g_module_pointer};
CFile.prototype._getAnnotationsAP=function(width,height,backgroundColor,pageIndex,nAnnot,nView){g_module_pointer.ptr=Module["_GetAnnotationsAP"](this.nativeFile,width,height,backgroundColor===undefined?16777215:backgroundColor,pageIndex,nAnnot===undefined?-1:nAnnot,nView);return g_module_pointer};CFile.prototype._getInteractiveFormsAP=function(width,height,backgroundColor,pageIndex,nWidget,nView,nButtonView){g_module_pointer.ptr=Module["_GetInteractiveFormsAP"](this.nativeFile,width,height,backgroundColor===
undefined?16777215:backgroundColor,pageIndex,nWidget===undefined?-1:nWidget,nView,nButtonView);return g_module_pointer};CFile.prototype._scanPage=function(page,mode){g_module_pointer.ptr=Module["_ScanPage"](this.nativeFile,page,mode===undefined?0:mode);return g_module_pointer};CFile.prototype._getImageBase64=function(rId){var strPtr=Module["_GetImageBase64"](this.nativeFile,rId);if(0==strPtr)return"error";var len=Module["_GetImageBase64Len"](strPtr);var ptr=Module["_GetImageBase64Ptr"](strPtr);var buffer=
new Uint8Array(Module["HEAP8"].buffer,ptr,len);var result=String.prototype.fromUtf8(buffer,0,len);Module["_GetImageBase64Free"](strPtr);return result};CFile.prototype._getGlyphs=function(pageIndex){var ptr=Module["_GetGlyphs"](this.nativeFile,pageIndex);if(!ptr)return null;var ptrArray=new Int32Array(Module["HEAP8"].buffer,ptr,5);var len=ptrArray[0];len-=20;var res={};res.info=[ptrArray[1],ptrArray[2],ptrArray[3],ptrArray[4]];if(len>0){var textCommandsSrc=new Uint8Array(Module["HEAP8"].buffer,ptr+
20,len);res.result=new Uint8Array(len);res.result.set(textCommandsSrc)}else res.result=[];return res};CFile.prototype._destroyTextInfo=function(){Module["_DestroyTextInfo"]()};CFile.prototype._getPixmap=function(pageIndex,width,height,backgroundColor){return Module["_GetPixmap"](this.nativeFile,pageIndex,width,height,backgroundColor===undefined?16777215:backgroundColor)};CFile.prototype._InitializeFonts=function(basePath){if(undefined!==basePath&&""!==basePath)baseFontsPath=basePath;if(!window["g_fonts_selection_bin"])return;
var memoryBuffer=window["g_fonts_selection_bin"].toUtf8();var pointer=Module["_malloc"](memoryBuffer.length);Module.HEAP8.set(memoryBuffer,pointer);Module["_InitializeFontsBase64"](pointer,memoryBuffer.length);Module["_free"](pointer);delete window["g_fonts_selection_bin"];var rangesBuffer=new CBinaryWriter;var ranges=AscFonts.getSymbolRanges();var rangesCount=ranges.length;rangesBuffer.writeUint(rangesCount);for(var i=0;i<rangesCount;i++){rangesBuffer.writeString(ranges[i].getName());rangesBuffer.writeUint(ranges[i].getStart());
rangesBuffer.writeUint(ranges[i].getEnd())}var rangesFinalLen=rangesBuffer.dataSize;var rangesFinal=new Uint8Array(rangesBuffer.buffer.buffer,0,rangesFinalLen);pointer=Module["_malloc"](rangesFinalLen);Module.HEAP8.set(rangesFinal,pointer);Module["_InitializeFontsRanges"](pointer,rangesFinalLen);Module["_free"](pointer)};CFile.prototype._CheckStreamId=function(data,status){var drawingFile=self.drawingFile;if(!drawingFile)return;var lenArray=new Int32Array(Module["HEAP8"].buffer,data,4);var len=lenArray[0];
len-=4;var buffer=new Uint8Array(Module["HEAP8"].buffer,data+4,len);var reader=new CBinaryReader(buffer,0,len);var name=reader.readString();var style=0;if(reader.readInt()!=0)style|=1;if(reader.readInt()!=0)style|=2;var file=AscFonts.pickFont(name,style);var fileId=file.GetID();var fileStatus=file.GetStatus();if(fileStatus===0)fontToMemory(file,true);else{drawingFile.fontStreams[fileId]=drawingFile.fontStreams[fileId]||{};drawingFile.fontStreams[fileId].pages=drawingFile.fontStreams[fileId].pages||
[];addToArrayAsDictionary(drawingFile.fontStreams[fileId].pages,drawingFile.fontPageIndex);addToArrayAsDictionary(drawingFile.pages[drawingFile.fontPageIndex].fonts,fileId);drawingFile.pages[drawingFile.fontPageIndex].fontsUpdateType|=drawingFile.fontPageUpdateType;if(undefined===file.externalCallback){var _t=file;file.externalCallback=function(){fontToMemory(_t,true);var pages=drawingFile.fontStreams[fileId].pages;delete drawingFile.fontStreams[fileId];var pagesRepaint_Page=[];var pagesRepaint_Annotation=
[];var pagesRepaint_Forms=[];for(var i=0,len$0=pages.length;i<len$0;i++){var pageNum=pages[i];var pageObj=drawingFile.pages[pageNum];var fonts=pageObj.fonts;for(var j=0,len_fonts=fonts.length;j<len_fonts;j++)if(fonts[j]==fileId){fonts.splice(j,1);break}if(0==fonts.length){if(pageObj.fontsUpdateType&UpdateFontsSource.Page)pagesRepaint_Page.push(pageNum);if(pageObj.fontsUpdateType&UpdateFontsSource.Annotation)pagesRepaint_Annotation.push(pageNum);if(pageObj.fontsUpdateType&UpdateFontsSource.Forms)pagesRepaint_Forms.push(pageNum);
pageObj.fontsUpdateType=UpdateFontsSource.Undefined}}if(pagesRepaint_Page.length>0&&drawingFile.onRepaintPages)drawingFile.onRepaintPages(pagesRepaint_Page);if(pagesRepaint_Annotation.length>0&&drawingFile.onRepaintAnnotations)drawingFile.onRepaintAnnotations(pagesRepaint_Annotation);if(pagesRepaint_Forms.length>0&&drawingFile.onRepaintForms)drawingFile.onRepaintForms(pagesRepaint_Forms);delete _t.externalCallback};if(2!==file.LoadFontAsync)file.LoadFontAsync(baseFontsPath,null)}}var memoryBuffer=
fileId.toUtf8();var pointer=Module["_malloc"](memoryBuffer.length);Module.HEAP8.set(memoryBuffer,pointer);Module["HEAP8"][status]=fileStatus==0?1:0;return pointer};CFile.prototype.lockPageNumForFontsLoader=function(pageIndex,type){this.fontPageIndex=pageIndex;this.fontPageUpdateType=type};CFile.prototype.unlockPageNumForFontsLoader=function(){this.fontPageIndex=-1;drawingFile.fontPageUpdateType=UpdateFontsSource.Undefined};CFile.prototype["getPages"]=function(){return this.pages};CFile.prototype["openForms"]=
function(){};CFile.prototype["getDocumentInfo"]=function(){return this.info};CFile.prototype["getStartID"]=function(){return this.StartID};CFile.prototype["loadFromData"]=function(arrayBuffer){var isSuccess=this._openFile(arrayBuffer);var error=this._getError();this.type=this._getType();self.drawingFile=this;if(!error)this.getInfo();this._isNeedPassword=4===error?true:false;return error};CFile.prototype["loadFromDataWithPassword"]=function(password){if(0!=this.nativeFile)this._closeFile();var isSuccess=
this._openFile(undefined,password);var error=this._getError();this.type=this._getType();self.drawingFile=this;if(!error)this.getInfo();this._isNeedPassword=4===error?true:false;return error};CFile.prototype["getType"]=function(){return this.type};CFile.prototype["close"]=function(){this._closeFile();this.nativeFile=0;this.pages=[];this.info=null;this.StartID=null;if(this.stream>0)this._free(this.stream);this.stream=-1;self.drawingFile=null};CFile.prototype["getFileBinary"]=function(){if(0>=this.stream)return"";
return new Uint8Array(Module["HEAP8"].buffer,this.stream,this.stream_size)};CFile.prototype["isNeedPassword"]=function(){return this._isNeedPassword};CFile.prototype.getInfo=function(){if(!this.nativeFile)return false;var ptr=this._getInfo();var reader=ptr.getReader();if(!reader)return false;this.StartID=reader.readInt();var _pages=reader.readInt();for(var i=0;i<_pages;i++){var rec={};rec["W"]=reader.readInt();rec["H"]=reader.readInt();rec["Dpi"]=reader.readInt();rec["Rotate"]=reader.readInt();rec["originIndex"]=
i;rec.fonts=[];rec.fontsUpdateType=UpdateFontsSource.Undefined;rec.text=null;this.pages.push(rec)}var json_info=reader.readString();try{this.info=JSON.parse(json_info)}catch(err$1){}ptr.free();return this.pages.length>0};CFile.prototype["getStructure"]=function(){var ptr=this._getStructure();var reader=ptr.getReader();if(!reader)return[];var res=[];while(reader.isValid()){var rec={};rec["page"]=reader.readInt();rec["level"]=reader.readInt();rec["y"]=reader.readDouble();rec["description"]=reader.readString();
res.push(rec)}ptr.free();return res};CFile.prototype["getLinks"]=function(pageIndex){var ptr=this._getLinks(pageIndex);var reader=ptr.getReader();if(!reader)return[];var res=[];while(reader.isValid()){var rec={};rec["link"]=reader.readString();rec["dest"]=reader.readDouble();rec["x"]=reader.readDouble();rec["y"]=reader.readDouble();rec["w"]=reader.readDouble();rec["h"]=reader.readDouble();res.push(rec)}ptr.free();return res};CFile.prototype["getGlyphs"]=function(pageIndex){var page=this.pages[pageIndex];
if(page.originIndex==undefined)return[];if(page.fonts.length>0)return null;this.lockPageNumForFontsLoader(pageIndex,UpdateFontsSource.Page);var res=this._getGlyphs(page.originIndex);this.unlockPageNumForFontsLoader();if(page.fonts.length>0){res=null;return null}if(res&&this.onUpdateStatistics)this.onUpdateStatistics(res.info[0],res.info[1],res.info[2],res.info[3]);return res.result||null};CFile.prototype["destroyTextInfo"]=function(){this._destroyTextInfo()};CFile.prototype.getWidgetFonts=function(type){var ptr=
this._getInteractiveFormsFonts(type);var reader=ptr.getReader();if(!reader)return[];var res=[];while(reader.isValid()){var n=reader.readInt();for(var i=0;i<n;++i)res.push(reader.readString())}ptr.free();return res};CFile.prototype["getInteractiveFormsEmbeddedFonts"]=function(){return this.getWidgetFonts(1)};CFile.prototype["getInteractiveFormsStandardFonts"]=function(){return this.getWidgetFonts(2)};CFile.prototype["getFontByID"]=function(ID){return this._getFontByID(ID)};CFile.prototype["setCMap"]=
function(memoryBuffer){if(!this.nativeFile)return;this._setCMap(memoryBuffer)};CFile.prototype["isNeedCMap"]=function(){return this._isNeedCMap()};function readAction(reader,rec){var SType=reader.readByte();rec["S"]=SType;if(SType==14)rec["JS"]=reader.readString();else if(SType==1){rec["page"]=reader.readInt();rec["kind"]=reader.readByte();switch(rec["kind"]){case 0:case 2:case 3:case 6:case 7:{var nFlag=reader.readByte();if(nFlag&1<<0)rec["left"]=reader.readDouble();if(nFlag&1<<1)rec["top"]=reader.readDouble();
if(nFlag&1<<2)rec["zoom"]=reader.readDouble();break}case 4:{rec["left"]=reader.readDouble();rec["bottom"]=reader.readDouble();rec["right"]=reader.readDouble();rec["top"]=reader.readDouble();break}case 1:case 5:default:break}}else if(SType==10)rec["N"]=reader.readString();else if(SType==6)rec["URI"]=reader.readString();else if(SType==9){rec["H"]=reader.readByte();var m=reader.readInt();rec["T"]=[];for(var j=0;j<m;++j)rec["T"].push(reader.readString())}else if(SType==12){rec["Flags"]=reader.readInt();
var m$2=reader.readInt();rec["Fields"]=[];for(var j$3=0;j$3<m$2;++j$3)rec["Fields"].push(reader.readString())}var NextAction=reader.readByte();if(NextAction){rec["Next"]={};readAction(reader,rec["Next"])}}function readAnnot(reader,rec){rec["AP"]={};rec["AP"]["i"]=reader.readInt();rec["annotflag"]=reader.readInt();var bHidden=rec["annotflag"]>>1&1;var bPrint=rec["annotflag"]>>2&1;rec["noZoom"]=rec["annotflag"]>>3&1;rec["noRotate"]=rec["annotflag"]>>4&1;var bNoView=rec["annotflag"]>>5&1;rec["locked"]=
rec["annotflag"]>>7&1;rec["ToggleNoView"]=rec["annotflag"]>>8&1;rec["lockedC"]=rec["annotflag"]>>9&1;rec["display"]=0;if(bHidden)rec["display"]=1;else if(bPrint)if(bNoView)rec["display"]=3;else rec["display"]=0;else if(bNoView)rec["display"]=0;else rec["display"]=2;rec["page"]=reader.readInt();rec["rect"]={};rec["rect"]["x1"]=reader.readDouble2();rec["rect"]["y1"]=reader.readDouble2();rec["rect"]["x2"]=reader.readDouble2();rec["rect"]["y2"]=reader.readDouble2();var flags=reader.readInt();if(flags&
1<<0)rec["UniqueName"]=reader.readString();if(flags&1<<1)rec["Contents"]=reader.readString();if(flags&1<<2){rec["BE"]={};rec["BE"]["S"]=reader.readByte();rec["BE"]["I"]=reader.readDouble()}if(flags&1<<3){var n=reader.readInt();rec["C"]=[];for(var i=0;i<n;++i)rec["C"].push(reader.readDouble2())}if(flags&1<<4){rec["border"]=reader.readByte();rec["borderWidth"]=reader.readDouble();if(rec["border"]==2){var n$4=reader.readInt();rec["dashed"]=[];for(var i$5=0;i$5<n$4;++i$5)rec["dashed"].push(reader.readDouble())}}if(flags&
1<<5)rec["LastModified"]=reader.readString();rec["AP"]["have"]=flags>>6&1;if(flags&1<<7)rec["OUserID"]=reader.readString()}function readAnnotAP(reader,AP){AP["i"]=reader.readInt();AP["x"]=reader.readDouble();AP["y"]=reader.readDouble();AP["w"]=reader.readInt();AP["h"]=reader.readInt();var n=reader.readInt();for(var i=0;i<n;++i){var APType=reader.readString();if(!AP[APType])AP[APType]={};var APi=AP[APType];var ASType=reader.readString();if(ASType){AP[APType][ASType]={};APi=AP[APType][ASType]}var np1=
reader.readInt();var np2=reader.readInt();APi["retValue"]=np2<<32|np1;APi["BlendMode"]=reader.readByte()}}CFile.prototype["getInteractiveFormsInfo"]=function(){var ptr=this._getInteractiveFormsInfo();var reader=ptr.getReader();if(!reader)return{};var res={};var k=reader.readInt();if(k>0)res["CO"]=[];for(var i=0;i<k;++i)res["CO"].push(reader.readInt());k=reader.readInt();if(k>0)res["Parents"]=[];for(var i$6=0;i$6<k;++i$6){var rec={};rec["i"]=reader.readInt();var flags=reader.readInt();if(flags&1<<
0)rec["name"]=reader.readString();if(flags&1<<1)rec["value"]=reader.readString();if(flags&1<<2)rec["defaultValue"]=reader.readString();if(flags&1<<3){var n=reader.readInt();rec["curIdxs"]=[];for(var i$7=0;i$7<n;++i$7)rec["curIdxs"].push(reader.readInt())}if(flags&1<<4)rec["Parent"]=reader.readInt();if(flags&1<<5){var n$8=reader.readInt();rec["value"]=[];for(var i$9=0;i$9<n$8;++i$9)rec["value"].push(reader.readString())}if(flags&1<<6){var n$10=reader.readInt();rec["Opt"]=[];for(var i$11=0;i$11<n$10;++i$11)rec["Opt"].push(reader.readString())}res["Parents"].push(rec)}res["Fields"]=
[];k=reader.readInt();for(var q=0;reader.isValid()&&q<k;++q){var rec$12={};rec$12["type"]=reader.readByte();readAnnot(reader,rec$12);rec$12["font"]={};rec$12["font"]["name"]=reader.readString();rec$12["font"]["size"]=reader.readDouble();rec$12["font"]["style"]=reader.readInt();var tc=reader.readInt();if(tc){rec$12["font"]["color"]=[];for(var i$13=0;i$13<tc;++i$13)rec$12["font"]["color"].push(reader.readDouble2())}rec$12["alignment"]=reader.readByte();rec$12["flag"]=reader.readInt();rec$12["readOnly"]=
rec$12["flag"]>>0&1;rec$12["required"]=rec$12["flag"]>>1&1;rec$12["noexport"]=rec$12["flag"]>>2&1;var flags$14=reader.readInt();if(flags$14&1<<0)rec$12["userName"]=reader.readString();if(flags$14&1<<1)rec$12["defaultStyle"]=reader.readString();if(flags$14&1<<2)rec$12["font"]["actual"]=reader.readString();if(flags$14&1<<3)rec$12["highlight"]=reader.readByte();if(flags$14&1<<4)rec$12["font"]["key"]=reader.readString();if(flags$14&1<<5){var n$15=reader.readInt();rec$12["BC"]=[];for(var i$16=0;i$16<n$15;++i$16)rec$12["BC"].push(reader.readDouble2())}if(flags$14&
1<<6)rec$12["rotate"]=reader.readInt();if(flags$14&1<<7){var n$17=reader.readInt();rec$12["BG"]=[];for(var i$18=0;i$18<n$17;++i$18)rec$12["BG"].push(reader.readDouble2())}if(flags$14&1<<8)rec$12["defaultValue"]=reader.readString();if(flags$14&1<<17)rec$12["Parent"]=reader.readInt();if(flags$14&1<<18)rec$12["name"]=reader.readString();if(flags$14&1<<19)rec$12["font"]["AP"]=reader.readString();var nAction=reader.readInt();if(nAction>0)rec$12["AA"]={};for(var i$19=0;i$19<nAction;++i$19){var AAType=reader.readString();
rec$12["AA"][AAType]={};readAction(reader,rec$12["AA"][AAType])}if(rec$12["type"]==27){if(flags$14&1<<9)rec$12["value"]=reader.readString();var IFflags=reader.readInt();if(flags$14&1<<10)rec$12["caption"]=reader.readString();if(flags$14&1<<11)rec$12["rolloverCaption"]=reader.readString();if(flags$14&1<<12)rec$12["alternateCaption"]=reader.readString();if(flags$14&1<<13)rec$12["position"]=reader.readByte();if(IFflags&1<<0){rec$12["IF"]={};if(IFflags&1<<1)rec$12["IF"]["SW"]=reader.readByte();if(IFflags&
1<<2)rec$12["IF"]["S"]=reader.readByte();if(IFflags&1<<3){rec$12["IF"]["A"]=[];rec$12["IF"]["A"].push(reader.readDouble());rec$12["IF"]["A"].push(reader.readDouble())}rec$12["IF"]["FB"]=IFflags>>4&1}}else if(rec$12["type"]==29||rec$12["type"]==28){if(flags$14&1<<9)rec$12["value"]=reader.readString();rec$12["style"]=reader.readByte();if(flags$14&1<<14)rec$12["ExportValue"]=reader.readString();rec$12["NoToggleToOff"]=rec$12["flag"]>>14&1;rec$12["radiosInUnison"]=rec$12["flag"]>>25&1}else if(rec$12["type"]==
30){if(flags$14&1<<9)rec$12["value"]=reader.readString();if(flags$14&1<<10)rec$12["maxLen"]=reader.readInt();if(rec$12["flag"]&1<<25)rec$12["richValue"]=reader.readString();rec$12["multiline"]=rec$12["flag"]>>12&1;rec$12["password"]=rec$12["flag"]>>13&1;rec$12["fileSelect"]=rec$12["flag"]>>20&1;rec$12["doNotSpellCheck"]=rec$12["flag"]>>22&1;rec$12["doNotScroll"]=rec$12["flag"]>>23&1;rec$12["comb"]=rec$12["flag"]>>24&1;rec$12["richText"]=rec$12["flag"]>>25&1}else if(rec$12["type"]==31||rec$12["type"]==
32){if(flags$14&1<<9)rec$12["value"]=reader.readString();if(flags$14&1<<10){var n$20=reader.readInt();rec$12["opt"]=[];for(var i$21=0;i$21<n$20;++i$21){var opt1=reader.readString();var opt2=reader.readString();if(opt1=="")rec$12["opt"].push(opt2);else rec$12["opt"].push([opt2,opt1])}}if(flags$14&1<<11)rec$12["TI"]=reader.readInt();if(flags$14&1<<12){var n$22=reader.readInt();rec$12["curIdxs"]=[];for(var i$23=0;i$23<n$22;++i$23)rec$12["curIdxs"].push(reader.readInt())}if(flags$14&1<<13){var n$24=reader.readInt();
rec$12["value"]=[];for(var i$25=0;i$25<n$24;++i$25)rec$12["value"].push(reader.readString())}rec$12["editable"]=rec$12["flag"]>>18&1;rec$12["multipleSelection"]=rec$12["flag"]>>21&1;rec$12["doNotSpellCheck"]=rec$12["flag"]>>22&1;rec$12["commitOnSelChange"]=rec$12["flag"]>>26&1}else if(rec$12["type"]==33)rec$12["Sig"]=flags$14>>9&1;res["Fields"].push(rec$12)}ptr.free();return res};CFile.prototype["getInteractiveFormsAP"]=function(pageIndex,width,height,backgroundColor,nWidget,sView,sButtonView){var nView=
-1;if(sView)if(sView=="N")nView=0;else if(sView=="D")nView=1;else if(sView=="R")nView=2;var nButtonView=-1;if(sButtonView)nButtonView=sButtonView=="Off"?0:1;this.lockPageNumForFontsLoader(pageIndex,UpdateFontsSource.Forms);var ptr=this._getInteractiveFormsAP(width,height,backgroundColor,pageIndex,nWidget,nView,nButtonView);var reader=ptr.getReader();this.unlockPageNumForFontsLoader();if(!reader)return[];var res=[];while(reader.isValid()){var AP={};readAnnotAP(reader,AP);res.push(AP)}ptr.free();return res};
CFile.prototype["getButtonIcons"]=function(pageIndex,width,height,backgroundColor,bBase64,nWidget,sIconView){var nView=-1;if(sIconView)if(sIconView=="I")nView=0;else if(sIconView=="RI")nView=1;else if(sIconView=="IX")nView=2;var ptr=this._getButtonIcons(backgroundColor,pageIndex,bBase64,nWidget,nView);var reader=ptr.getReader();if(!reader)return{};var res={};res["MK"]=[];res["View"]=[];while(reader.isValid()){var MK={};MK["i"]=reader.readInt();var n=reader.readInt();for(var i=0;i<n;++i){var MKType=
reader.readString();MK[MKType]=reader.readInt();var unique=reader.readByte();if(unique){var ViewMK={};ViewMK["j"]=MK[MKType];ViewMK["w"]=reader.readInt();ViewMK["h"]=reader.readInt();if(bBase64)ViewMK["retValue"]=reader.readString();else{var np1=reader.readInt();var np2=reader.readInt();ViewMK["retValue"]=np2<<32|np1}res["View"].push(ViewMK)}}res["MK"].push(MK)}ptr.free();return res};CFile.prototype["getAnnotationsInfo"]=function(pageIndex){if(!this.nativeFile)return[];var ptr=this._getAnnotationsInfo(pageIndex);
var reader=ptr.getReader();if(!reader)return[];var res=[];while(reader.isValid()){var rec={};rec["Type"]=reader.readByte();readAnnot(reader,rec);var flags=0;if(rec["Type"]<18&&rec["Type"]!=1&&rec["Type"]!=15||rec["Type"]==25){flags=reader.readInt();if(flags&1<<0)rec["Popup"]=reader.readInt();if(flags&1<<1)rec["User"]=reader.readString();if(flags&1<<2)rec["CA"]=reader.readDouble();if(flags&1<<3){var n=reader.readInt();rec["RC"]=[];for(var i=0;i<n;++i){var oFont={};oFont["alignment"]=reader.readByte();
var nFontFlag=reader.readInt();oFont["bold"]=nFontFlag>>0&1;oFont["italic"]=nFontFlag>>1&1;oFont["strikethrough"]=nFontFlag>>3&1;oFont["underlined"]=nFontFlag>>4&1;if(nFontFlag&1<<5)oFont["vertical"]=reader.readDouble();if(nFontFlag&1<<6)oFont["actual"]=reader.readString();oFont["size"]=reader.readDouble();oFont["color"]=[];oFont["color"].push(reader.readDouble2());oFont["color"].push(reader.readDouble2());oFont["color"].push(reader.readDouble2());oFont["name"]=reader.readString();oFont["text"]=reader.readString();
rec["RC"].push(oFont)}}if(flags&1<<4)rec["CreationDate"]=reader.readString();if(flags&1<<5)rec["RefTo"]=reader.readInt();if(flags&1<<6)rec["RefToReason"]=reader.readByte();if(flags&1<<7)rec["Subj"]=reader.readString()}if(rec["Type"]==0){if(rec["C"]){rec["IC"]=rec["C"];delete rec["C"]}rec["Open"]=flags>>15&1;if(flags&1<<16)rec["Icon"]=reader.readByte();if(flags&1<<17)rec["StateModel"]=reader.readByte();if(flags&1<<18)rec["State"]=reader.readByte()}else if(rec["Type"]==3){rec["L"]=[];for(var i$26=0;i$26<
4;++i$26)rec["L"].push(reader.readDouble());if(flags&1<<15){rec["LE"]=[];rec["LE"].push(reader.readByte());rec["LE"].push(reader.readByte())}if(flags&1<<16){var n$27=reader.readInt();rec["IC"]=[];for(var i$28=0;i$28<n$27;++i$28)rec["IC"].push(reader.readDouble2())}if(flags&1<<17)rec["LL"]=reader.readDouble();if(flags&1<<18)rec["LLE"]=reader.readDouble();rec["Cap"]=flags>>19&1;if(flags&1<<20)rec["IT"]=reader.readByte();if(flags&1<<21)rec["LLO"]=reader.readDouble();if(flags&1<<22)rec["CP"]=reader.readByte();
if(flags&1<<23){rec["CO"]=[];rec["CO"].push(reader.readDouble());rec["CO"].push(reader.readDouble())}}else if(rec["Type"]==14){var n$29=reader.readInt();rec["InkList"]=[];for(var i$30=0;i$30<n$29;++i$30){rec["InkList"][i$30]=[];var m=reader.readInt();for(var j=0;j<m;++j)rec["InkList"][i$30].push(reader.readDouble())}}else if(rec["Type"]>7&&rec["Type"]<12){var n$31=reader.readInt();rec["QuadPoints"]=[];for(var i$32=0;i$32<n$31;++i$32)rec["QuadPoints"].push(reader.readDouble())}else if(rec["Type"]==
4||rec["Type"]==5){if(flags&1<<15){rec["RD"]=[];for(var i$33=0;i$33<4;++i$33)rec["RD"].push(reader.readDouble())}if(flags&1<<16){var n$34=reader.readInt();rec["IC"]=[];for(var i$35=0;i$35<n$34;++i$35)rec["IC"].push(reader.readDouble2())}}else if(rec["Type"]==6||rec["Type"]==7){var nVertices=reader.readInt();rec["Vertices"]=[];for(var i$36=0;i$36<nVertices;++i$36)rec["Vertices"].push(reader.readDouble());if(flags&1<<15){rec["LE"]=[];rec["LE"].push(reader.readByte());rec["LE"].push(reader.readByte())}if(flags&
1<<16){var n$37=reader.readInt();rec["IC"]=[];for(var i$38=0;i$38<n$37;++i$38)rec["IC"].push(reader.readDouble2())}if(flags&1<<20)rec["IT"]=reader.readByte()}else if(rec["Type"]==2){if(rec["C"]){rec["IC"]=rec["C"];delete rec["C"]}rec["alignment"]=reader.readByte();rec["Rotate"]=reader.readInt();if(flags&1<<15){rec["RD"]=[];for(var i$39=0;i$39<4;++i$39)rec["RD"].push(reader.readDouble())}if(flags&1<<16){var n$40=reader.readInt();rec["CL"]=[];for(var i$41=0;i$41<n$40;++i$41)rec["CL"].push(reader.readDouble())}if(flags&
1<<17)rec["defaultStyle"]=reader.readString();if(flags&1<<18)rec["LE"]=reader.readByte();if(flags&1<<20)rec["IT"]=reader.readByte();if(flags&1<<21){var n$42=reader.readInt();rec["C"]=[];for(var i$43=0;i$43<n$42;++i$43)rec["C"].push(reader.readDouble2())}}else if(rec["Type"]==13){if(flags&1<<15){rec["RD"]=[];for(var i$44=0;i$44<4;++i$44)rec["RD"].push(reader.readDouble())}if(flags&1<<16)rec["Sy"]=reader.readByte()}else if(rec["Type"]==16){if(flags&1<<15)rec["Icon"]=reader.readString();if(flags&1<<
16)rec["FS"]=reader.readString();if(flags&1<<17){rec["F"]={};rec["F"]["FileName"]=reader.readString()}if(flags&1<<18){rec["UF"]={};rec["UF"]["FileName"]=reader.readString()}if(flags&1<<19){rec["DOS"]={};rec["DOS"]["FileName"]=reader.readString()}if(flags&1<<20){rec["Mac"]={};rec["Mac"]["FileName"]=reader.readString()}if(flags&1<<21){rec["Unix"]={};rec["Unix"]["FileName"]=reader.readString()}if(flags&1<<22){rec["ID"]=[];rec["ID"].push(reader.readString());rec["ID"].push(reader.readString())}rec["V"]=
flags&1<<23;if(flags&1<<24){var flag=reader.readInt();if(flag&1<<0){var n$45=reader.readInt();var np1=reader.readInt();var np2=reader.readInt();var pPoint=np2<<32|np1;rec["F"]["File"]=new Uint8Array(Module["HEAP8"].buffer,pPoint,n$45);Module["_free"](pPoint)}if(flag&1<<1){var n$46=reader.readInt();var np1$47=reader.readInt();var np2$48=reader.readInt();var pPoint$49=np2$48<<32|np1$47;rec["UF"]["File"]=new Uint8Array(Module["HEAP8"].buffer,pPoint$49,n$46);Module["_free"](pPoint$49)}if(flag&1<<2){var n$50=
reader.readInt();var np1$51=reader.readInt();var np2$52=reader.readInt();var pPoint$53=np2$52<<32|np1$51;rec["DOS"]["File"]=new Uint8Array(Module["HEAP8"].buffer,pPoint$53,n$50);Module["_free"](pPoint$53)}if(flag&1<<3){var n$54=reader.readInt();var np1$55=reader.readInt();var np2$56=reader.readInt();var pPoint$57=np2$56<<32|np1$55;rec["Mac"]["File"]=new Uint8Array(Module["HEAP8"].buffer,pPoint$57,n$54);Module["_free"](pPoint$57)}if(flag&1<<4){var n$58=reader.readInt();var np1$59=reader.readInt();
var np2$60=reader.readInt();var pPoint$61=np2$60<<32|np1$59;rec["Unix"]["File"]=new Uint8Array(Module["HEAP8"].buffer,pPoint$61,n$58);Module["_free"](pPoint$61)}}if(flags&1<<26)rec["Desc"]=reader.readString()}else if(rec["Type"]==12){rec["Icon"]=reader.readString();rec["Rotate"]=reader.readDouble2();rec["InRect"]=[];for(var i$62=0;i$62<8;++i$62)rec["InRect"].push(reader.readDouble2())}res.push(rec)}ptr.free();return res};CFile.prototype["getAnnotationsAP"]=function(pageIndex,width,height,backgroundColor,
nAnnot,sView){var nView=-1;if(sView)if(sView=="N")nView=0;else if(sView=="D")nView=1;else if(sView=="R")nView=2;this.lockPageNumForFontsLoader(pageIndex,UpdateFontsSource.Annotation);var ptr=this._getAnnotationsAP(width,height,backgroundColor,pageIndex,nAnnot,nView);var reader=ptr.getReader();this.unlockPageNumForFontsLoader();if(!reader)return[];var res=[];while(reader.isValid()){var AP={};readAnnotAP(reader,AP);res.push(AP)}ptr.free();return res};CFile.prototype["scanPage"]=function(page,mode){var ptr=
this._scanPage(page,mode);var reader=ptr.getReader();if(!reader)return[];var shapesCount=reader.readInt();var shapes=new Array(shapesCount);for(var i=0;i<shapesCount;i++)shapes[i]=reader.readString();ptr.free();return shapes};CFile.prototype["getImageBase64"]=function(rId){var strId=""+rId;if(this.scannedImages[strId])return this.scannedImages[strId];this.scannedImages[strId]=this._getImageBase64(rId);return this.scannedImages[strId]};CFile.prototype["changeImageUrl"]=function(baseUrl,resultUrl){for(var i in this.scannedImages)if(this.scannedImages[i]==
baseUrl)this.scannedImages[i]=resultUrl};CFile.prototype["getUint8Array"]=function(ptr,len){return this._getUint8Array(ptr,len)};CFile.prototype["getUint8ClampedArray"]=function(ptr,len){return this._getUint8ClampedArray(ptr,len)};CFile.prototype["free"]=function(pointer){this._free(pointer)};CFile.prototype["getPagePixmap"]=function(pageIndex,width,height,backgroundColor){var page=this.pages[pageIndex];if(page.originIndex==undefined)return null;if(page.fonts.length>0)return null;this.lockPageNumForFontsLoader(pageIndex,
UpdateFontsSource.Page);var ptr=this._getPixmap(page.originIndex,width,height,backgroundColor);this.unlockPageNumForFontsLoader();if(page.fonts.length>0){this._free(ptr);ptr=null}return ptr};function addToArrayAsDictionary(arr,value){var isFound=false;for(var i=0,len=arr.length;i<len;i++)if(arr[i]==value){isFound=true;break}if(!isFound)arr.push(value);return isFound}function fontToMemory(file,isCheck){var idBuffer=file.GetID().toUtf8();var idPointer=Module["_malloc"](idBuffer.length);Module["HEAP8"].set(idBuffer,
idPointer);if(isCheck){var nExist=Module["_IsFontBinaryExist"](idPointer);if(nExist!=0){Module["_free"](idPointer);return}}var stream_index=file.GetStreamIndex();var stream=AscFonts.getFontStream(stream_index);var streamPointer=Module["_malloc"](stream.size);Module["HEAP8"].set(stream.data,streamPointer);Module["_SetFontBinary"](idPointer,streamPointer,stream.size);Module["_free"](streamPointer);Module["_free"](idPointer)}CFile.prototype["addPage"]=function(pageIndex,pageObj){this.pages.splice(pageIndex,
0,pageObj);if(this.fontStreams)for(var i in this.fontStreams){var pages=this.fontStreams[i].pages;for(var j=0;j<pages.length;j++)if(pages[j]>=pageIndex)pages[j]+=1}};CFile.prototype["removePage"]=function(pageIndex){var result=this.pages.splice(pageIndex,1);if(this.fontStreams)for(var i in this.fontStreams){var pages=this.fontStreams[i].pages;for(var j=0;j<pages.length;j++)if(pages[j]>pageIndex)pages[j]-=1;else if(pages[j]==pageIndex)pages.splice(j,1)}return result};self["AscViewer"]["Free"]=function(pointer){CFile.prototype._free(pointer)};
self["AscViewer"]["InitializeFonts"]=function(basePath){return CFile.prototype._InitializeFonts(basePath)};self["AscViewer"]["CheckStreamId"]=function(data,status){return CFile.prototype._CheckStreamId(data,status)};self["AscViewer"]["CDrawingFile"]=CFile;self.drawingFile=null})(window,undefined);
