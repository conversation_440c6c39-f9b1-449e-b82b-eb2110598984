<!DOCTYPE html>
<html>
<head>
    <title>X2T Converter Test</title>
    <meta charset="UTF-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .log {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .file-input {
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>X2T Converter Test</h1>
    
    <div class="container">
        <h2>1. 初始化 X2T</h2>
        <button id="initBtn" class="button">初始化 X2T 转换器</button>
        <div id="initStatus" class="status info" style="display: none;">正在初始化...</div>
    </div>

    <div class="container">
        <h2>2. 文档转换测试</h2>
        <div class="file-input">
            <label for="fileInput">选择文档文件 (支持 .docx, .doc, .xlsx, .pptx 等):</label><br>
            <input type="file" id="fileInput" accept=".docx,.doc,.xlsx,.xls,.pptx,.ppt,.odt,.ods,.odp,.rtf,.txt,.csv">
        </div>
        <button id="convertBtn" class="button" disabled>转换为 BIN 格式</button>
        <div id="convertStatus" class="status info" style="display: none;">正在转换...</div>
    </div>

    <div class="container">
        <h2>3. 日志输出</h2>
        <button id="clearLogBtn" class="button">清空日志</button>
        <div id="logOutput" class="log"></div>
    </div>

    <script src="./x2t.js"></script>
    <script>
        let isInitialized = false;
        let convertedBinData = null;

        // 日志函数
        function log(message, type = 'info') {
            const logOutput = document.getElementById('logOutput');
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logOutput.textContent += logMessage;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(logMessage);
        }

        // 显示状态
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            element.style.display = 'block';
        }

        // 隐藏状态
        function hideStatus(elementId) {
            document.getElementById(elementId).style.display = 'none';
        }

        // 初始化 X2T
        document.getElementById('initBtn').addEventListener('click', async function() {
            const btn = this;
            btn.disabled = true;
            showStatus('initStatus', '正在初始化 X2T 转换器...', 'info');
            
            try {
                log('开始初始化 X2T 转换器...');
                
                // 检查 X2T 是否已加载
                if (typeof window.initX2T !== 'function') {
                    throw new Error('X2T 脚本未正确加载');
                }
                
                await window.initX2T();
                isInitialized = true;
                
                log('X2T 转换器初始化成功');
                showStatus('initStatus', 'X2T 转换器初始化成功！', 'success');
                
                // 启用转换按钮
                document.getElementById('convertBtn').disabled = false;
                
            } catch (error) {
                log(`X2T 初始化失败: ${error.message}`, 'error');
                showStatus('initStatus', `初始化失败: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
            }
        });

        // 文档转换
        document.getElementById('convertBtn').addEventListener('click', async function() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('请先选择一个文件');
                return;
            }
            
            if (!isInitialized) {
                alert('请先初始化 X2T 转换器');
                return;
            }
            
            const btn = this;
            btn.disabled = true;
            showStatus('convertStatus', '正在转换文档...', 'info');
            
            try {
                log(`开始转换文件: ${file.name} (${file.size} bytes)`);
                
                const result = await window.convertDocument(file);
                convertedBinData = result;
                
                log(`文档转换成功:`);
                log(`  - 文件名: ${result.fileName}`);
                log(`  - 类型: ${result.type}`);
                log(`  - BIN 数据大小: ${result.bin.length} bytes`);
                log(`  - 媒体文件数量: ${Object.keys(result.media).length}`);
                
                showStatus('convertStatus', `转换成功！BIN 数据大小: ${result.bin.length} bytes`, 'success');
                
            } catch (error) {
                log(`文档转换失败: ${error.message}`, 'error');
                showStatus('convertStatus', `转换失败: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
            }
        });

        // 清空日志
        document.getElementById('clearLogBtn').addEventListener('click', function() {
            document.getElementById('logOutput').textContent = '';
        });

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            log('页面加载完成');
            
            // 检查 X2T 脚本是否加载
            if (typeof window.X2TConverter !== 'undefined') {
                log('X2T 脚本加载成功');
            } else {
                log('X2T 脚本未加载', 'error');
            }
        });
    </script>
</body>
</html>
