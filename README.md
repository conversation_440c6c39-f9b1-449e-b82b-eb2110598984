# OnlyOffice Editor with X2T Conversion

这个项目将 OnlyOffice 编辑器与 X2T 文档转换功能集成，实现了在文档加载时自动将文档转换为 bin 格式。

## 主要修改

### 1. X2T TypeScript 转 JavaScript

- **原文件**: `x2t.ts` (TypeScript)
- **新文件**: `x2t.js` (JavaScript)
- **修改内容**:
  - 移除了所有 TypeScript 类型定义
  - 转换为纯 JavaScript 语法
  - 保留了完整的 X2T 转换功能
  - 添加了全局 API 暴露

### 2. XMLHttpRequest 拦截机制

在 `index.html` 中实现了 XMLHttpRequest 拦截功能：

- **拦截目标**: 多种文档格式和本地文件
  - 文档格式: `.docx`, `.doc`, `.xlsx`, `.xls`, `.pptx`, `.ppt`
  - 本地文件: `blob:` URL (通过文件选择器选择的本地文件)
- **处理流程**:
  1. 检测到文档请求时进行拦截
  2. 获取原始文档数据
  3. 动态检测文件类型并获取正确的 MIME 类型
  4. 使用 X2T 转换为 bin 格式
  5. 提取并存储媒体资源 (图片、嵌入对象等)
  6. 替换响应数据为转换后的 bin 数据
  7. 缓存转换结果以提高性能

### 3. 媒体资源处理

实现了完整的媒体资源管理系统：

- **媒体提取**: 从转换结果中提取所有媒体文件
- **全局存储**: 将媒体资源存储在 `window.mediaResources` Map 中
- **URL 映射**: 每个媒体文件都有对应的 Blob URL
- **智能匹配**: `getImageURL` 支持完整路径和部分路径匹配
- **Promise 支持**: `getImageURL` 返回 Promise 对象

### 4. 核心功能

#### X2T 转换器类 (X2TConverter)
```javascript
// 主要方法
- loadScript()              // 加载 X2T WASM 脚本
- initialize()              // 初始化 X2T 模块
- convertDocument(file)     // 将文档转换为 bin 格式
- convertBinToDocumentAndDownload() // 将 bin 转换回文档格式
```

#### 全局 API
```javascript
// 可在 window 对象上访问的 API
- window.initX2T()                    // 初始化 X2T
- window.convertDocument(file)        // 转换文档
- window.convertBinToDocumentAndDownload() // bin 转文档
- window.oAscFileType                 // 文件类型常量
- window.mediaResources               // 媒体资源 Map
```

#### getImageURL 方法
```javascript
// OnlyOffice 编辑器中的媒体资源获取
getImageURL: (imagePath, callback) => {
    return new Promise((resolve, reject) => {
        // 1. 精确匹配
        if (window.mediaResources.has(imagePath)) {
            resolve(window.mediaResources.get(imagePath));
        }
        // 2. 部分路径匹配
        else {
            // 智能匹配逻辑
        }
    });
}
```

## 文件结构

```
├── index.html              # 主页面，包含 XMLHttpRequest 拦截和媒体资源处理
├── x2t.js                  # X2T 转换器 (从 TypeScript 转换)
├── test-x2t.html          # X2T 功能测试页面
├── test-intercept.html    # XMLHttpRequest 拦截测试页面
├── test-media.html        # 媒体资源处理测试页面
├── start-server.sh        # 简单的 HTTP 服务器启动脚本
├── x2t/
│   ├── x2t.js             # X2T WASM 脚本
│   └── x2t.wasm           # X2T WASM 模块
└── web-apps/              # OnlyOffice 应用文件
```

## 使用方法

### 1. 启动服务器
```bash
chmod +x start-server.sh
./start-server.sh
```

### 2. 访问页面
- 主编辑器: `http://localhost:8080/index.html`
- X2T 测试: `http://localhost:8080/test-x2t.html`
- 拦截测试: `http://localhost:8080/test-intercept.html`
- 媒体资源测试: `http://localhost:8080/test-media.html`

### 3. 测试流程

#### 测试 X2T 转换功能
1. 访问 `test-x2t.html`
2. 点击"初始化 X2T 转换器"
3. 选择文档文件 (.docx, .doc, .xlsx 等)
4. 点击"转换为 BIN 格式"
5. 查看转换结果

#### 测试 XMLHttpRequest 拦截
1. 访问 `test-intercept.html`
2. 点击"初始化 X2T"
3. 点击"测试文档请求"
4. 查看日志，确认拦截和转换是否成功

#### 测试媒体资源处理
1. 访问 `test-media.html`
2. 点击"初始化 X2T"
3. 选择包含图片的文档文件 (.docx)
4. 点击"转换文档"
5. 查看提取的媒体资源预览
6. 点击"测试 getImageURL"验证媒体资源获取功能

## 技术细节

### XMLHttpRequest 拦截实现

使用了完全替换 XMLHttpRequest 构造函数的方法：

```javascript
// 保存原始构造函数
var OriginalXMLHttpRequest = iframeWindow.XMLHttpRequest;

// 创建新的构造函数
iframeWindow.XMLHttpRequest = function() {
    var xhr = new OriginalXMLHttpRequest();
    // 拦截 open 和 send 方法
    // ...
};
```

### 异步转换处理

为了解决异步转换的时序问题：

1. 在 `send` 方法中完全拦截文档请求
2. 创建新的 XMLHttpRequest 获取原始数据
3. 异步执行 X2T 转换
4. 手动设置响应属性和触发事件

### 缓存机制

实现了转换结果缓存：
- 使用 `Map` 存储已转换的文档
- 键为请求 URL，值为转换后的 bin 数据
- 避免重复转换提高性能

## 注意事项

1. **CORS 限制**: 需要在 HTTP 服务器环境下运行
2. **文件大小**: 大文件转换可能需要较长时间
3. **浏览器兼容性**: 需要支持 WebAssembly 的现代浏览器
4. **内存使用**: 转换过程会占用较多内存

## 故障排除

### 常见问题

1. **X2T 初始化失败**
   - 检查 `x2t/x2t.js` 和 `x2t/x2t.wasm` 文件是否存在
   - 确保在 HTTP 服务器环境下运行

2. **文档转换失败**
   - 检查文档格式是否支持
   - 查看浏览器控制台错误信息

3. **拦截不生效**
   - 确保 X2T 已正确初始化
   - 检查请求 URL 是否包含 `.docx` 或 `.doc`

### 调试方法

1. 打开浏览器开发者工具
2. 查看 Console 标签页的日志输出
3. 使用 Network 标签页监控网络请求
4. 在代码中添加 `debugger` 断点进行调试

## 扩展功能

可以进一步扩展的功能：

1. 支持更多文档格式 (.xlsx, .pptx 等)
2. 添加转换进度显示
3. 实现转换结果的持久化存储
4. 添加转换选项配置
5. 优化内存使用和性能
