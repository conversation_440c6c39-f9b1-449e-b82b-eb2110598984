#!/bin/bash

# 简单的 HTTP 服务器启动脚本
# 用于测试 OnlyOffice 编辑器和 X2T 转换功能

PORT=8080

echo "启动 HTTP 服务器..."
echo "端口: $PORT"
echo "访问地址: http://localhost:$PORT"
echo "测试页面: http://localhost:$PORT/test-x2t.html"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

# 检查 Python 版本并启动相应的服务器
if command -v python3 &> /dev/null; then
    echo "使用 Python 3 启动服务器..."
    python3 -m http.server $PORT
elif command -v python &> /dev/null; then
    echo "使用 Python 2 启动服务器..."
    python -m SimpleHTTPServer $PORT
else
    echo "错误: 未找到 Python，请安装 Python 后重试"
    exit 1
fi
