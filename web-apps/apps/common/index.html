<!DOCTYPE html>
<html lang="en">
  <head>
      <title>ONLYOFFICE Documents</title>
      <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=IE8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no">

      <style type="text/css">
          .loadmask {
              left: 0;
              top: 0;
              position: absolute;
              height: 100%;
              width: 100%;
              overflow: hidden;
              border: none;
              background: #e2e2e2;
              background: var(--canvas-background, #e2e2e2);
              z-index: 1002;
          }

          .theme-dark .loadmask, .theme-type-dark .loadmask {
              background: #555;
          }
      </style>
      <script>
          function getUrlParams() {
              var e,
                  a = /\+/g,  // Regex for replacing addition symbol with a space
                  r = /([^&=]+)=?([^&]*)/g,
                  d = function (s) { return decodeURIComponent(s.replace(a, " ")); },
                  q = window.location.search.substring(1),
                  urlParams = {};

              while (e = r.exec(q))
                  urlParams[d(e[1])] = d(e[2]);

              return urlParams;
          }

          var params = getUrlParams(),
              postfix = params["indexPostfix"] || '',
              embed = params["type"]==='embedded';
          params.skipScaling = true;
          window.frameEditorId = params["frameEditorId"];
          window.parentOrigin = params["parentOrigin"];
      </script>
      <script>
/*
 * (c) Copyright Ascensio System SIA 2010-2024
 *
 * This program is a free software product. You can redistribute it and/or
 * modify it under the terms of the GNU Affero General Public License (AGPL)
 * version 3 as published by the Free Software Foundation. In accordance with
 * Section 7(a) of the GNU AGPL its Section 15 shall be amended to the effect
 * that Ascensio System SIA expressly excludes the warranty of non-infringement
 * of any third-party rights.
 *
 * This program is distributed WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR  PURPOSE. For
 * details, see the GNU AGPL at: http://www.gnu.org/licenses/agpl-3.0.html
 *
 * You can contact Ascensio System SIA at 20A-6 Ernesta Birznieka-Upish
 * street, Riga, Latvia, EU, LV-1050.
 *
 * The  interactive user interfaces in modified source and object code versions
 * of the Program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU AGPL version 3.
 *
 * Pursuant to Section 7(b) of the License you must retain the original Product
 * logo when distributing the program. Pursuant to Section 7(e) we decline to
 * grant you any rights under trademark law for use of our trademarks.
 *
 * All the Product's GUI elements, including illustrations and icon sets, as
 * well as technical writing content are licensed under the terms of the
 * Creative Commons Attribution-ShareAlike 4.0 International. See the License
 * terms at http://creativecommons.org/licenses/by-sa/4.0/legalcode
 *
 */

+function init_themes() {
    let localstorage;
    const local_storage_available = +function () {
        try {
            return !!(localstorage = window.localStorage);
        } catch (e) {
            console.warn('localStorage is unavailable');
            localstorage = {
                getItem: function (key) {return null;},
            };
            return false;
        }
    }();

    !window.uitheme && (window.uitheme = {});

    window.uitheme.set_id = function (id) {
        if ( id == 'theme-system' )
            this.adapt_to_system_theme();
        else this.id = id;
    }

    window.uitheme.is_theme_system = function () {
        return this.id == 'theme-system';
    }

    window.uitheme.adapt_to_system_theme = function () {
        this.id = 'theme-system';
        this.type = this.is_system_theme_dark() ? 'dark' : 'light';
    }

    window.uitheme.relevant_theme_id = function () {
        if ( this.is_theme_system() )
            return this.is_system_theme_dark() ? 'theme-dark' : 'theme-classic-light';
        return this.id;
    }

    if ( !window.uitheme.is_system_theme_dark )
        window.uitheme.is_system_theme_dark = function () {
            return window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        }

    !window.uitheme.id && window.uitheme.set_id(localstorage.getItem("ui-theme-id"));
    window.uitheme.iscontentdark = localstorage.getItem("content-theme") == 'dark';

    function inject_style_tag(content) {
        const style = document.createElement('style');
        style.type = 'text/css';
        style.innerHTML = content;
        document.getElementsByTagName('head')[0].appendChild(style);
    }

    inject_style_tag(':root .theme-dark {' +
                                '--toolbar-header-document: #2a2a2a; --toolbar-header-spreadsheet: #2a2a2a;' +
                                '--toolbar-header-presentation: #2a2a2a; --toolbar-header-pdf: #2a2a2a; --toolbar-header-visio: #2a2a2a;}' +
                            ':root .theme-contrast-dark {' +
                                '--toolbar-header-document: #1e1e1e; --toolbar-header-spreadsheet: #1e1e1e;' +
                                '--toolbar-header-presentation: #1e1e1e; --toolbar-header-pdf: #1e1e1e; --toolbar-header-visio: #1e1e1e;}');

    let objtheme = window.uitheme.colors ? window.uitheme : localstorage.getItem("ui-theme");
    const header_tokens = ['toolbar-header-document', 'toolbar-header-spreadsheet', 'toolbar-header-presentation', 'toolbar-header-pdf', 'toolbar-header-visio'];
    if ( !!objtheme ) {
        if ( typeof(objtheme) == 'string' && objtheme.lastIndexOf("{", 0) === 0 &&
                objtheme.indexOf("}", objtheme.length - 1) !== -1 )
        {
            objtheme = JSON.parse(objtheme);
        }

        if ( objtheme ) {
            if ( window.uitheme.id && window.uitheme.id != objtheme.id ) {
                local_storage_available && localstorage.removeItem("ui-theme");
                !window.uitheme.type && /-dark/.test(window.uitheme.id) && (window.uitheme.type = 'dark');
            } else {
                window.uitheme.cache = objtheme;
                if ( !window.uitheme.type && objtheme.type ) {
                    window.uitheme.type = objtheme.type;
                }

                if ( objtheme.colors ) {
                    header_tokens.forEach(function (i) {
                            !!objtheme.colors[i] && document.documentElement.style.setProperty('--' + i, objtheme.colors[i]);
                        });

                    let colors = [];
                    for (let c in objtheme.colors) {
                        colors.push('--' + c + ':' + objtheme.colors[c]);
                    }

                    inject_style_tag('.' + objtheme.id + '{' + colors.join(';') + ';}');
                }
            }
        }
    } else {
        if ( window.uitheme.id && window.uitheme.id.lastIndexOf("theme-gray", 0) === 0 ) {
            header_tokens.forEach(function (i) {
                !!document.documentElement.style.setProperty('--' + i, "#f7f7f7");
            });
        }
    }
}();

</script>
  </head>
  <body>
      <script>
/*
 * (c) Copyright Ascensio System SIA 2010-2024
 *
 * This program is a free software product. You can redistribute it and/or
 * modify it under the terms of the GNU Affero General Public License (AGPL)
 * version 3 as published by the Free Software Foundation. In accordance with
 * Section 7(a) of the GNU AGPL its Section 15 shall be amended to the effect
 * that Ascensio System SIA expressly excludes the warranty of non-infringement
 * of any third-party rights.
 *
 * This program is distributed WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR  PURPOSE. For
 * details, see the GNU AGPL at: http://www.gnu.org/licenses/agpl-3.0.html
 *
 * You can contact Ascensio System SIA at 20A-6 Ernesta Birznieka-Upish
 * street, Riga, Latvia, EU, LV-1050.
 *
 * The  interactive user interfaces in modified source and object code versions
 * of the Program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU AGPL version 3.
 *
 * Pursuant to Section 7(b) of the License you must retain the original Product
 * logo when distributing the program. Pursuant to Section 7(e) we decline to
 * grant you any rights under trademark law for use of our trademarks.
 *
 * All the Product's GUI elements, including illustrations and icon sets, as
 * well as technical writing content are licensed under the terms of the
 * Creative Commons Attribution-ShareAlike 4.0 International. See the License
 * terms at http://creativecommons.org/licenses/by-sa/4.0/legalcode
 *
 */

var checkLocalStorage = (function () {
    try {
        var storage = window['localStorage'];
        return true;
    }
    catch(e) {
        return false;
    }
})();

if (!window.lang) {
    window.lang = (/(?:&|^)lang=([^&]+)&?/i).exec(window.location.search.substring(1));
    window.lang = window.lang ? window.lang[1] : '';
}
window.lang && (window.lang = window.lang.split(/[\-\_]/)[0].toLowerCase());

var isLangRtl = function (lang) {
    return lang.lastIndexOf('ar', 0) === 0 || lang.lastIndexOf('he', 0) === 0;
}

var ui_rtl = false;
if ( window.nativeprocvars && window.nativeprocvars.rtl !== undefined ) {
    ui_rtl = window.nativeprocvars.rtl;
} else {
    if ( isLangRtl(lang) )
        if ( checkLocalStorage && localStorage.getItem("settings-ui-rtl") !== null )
            ui_rtl = localStorage.getItem("settings-ui-rtl") === '1';
        else ui_rtl = true;
}

if ( ui_rtl && window.isIEBrowser !== true ) {
    document.body.setAttribute('dir', 'rtl');
    document.body.classList.add('rtl');
}
if ( isLangRtl(lang) ) {
    document.body.classList.add('rtl-font');
}
document.body.setAttribute('applang', lang);

function checkScaling() {
    var matches = {
        'pixel-ratio__1_25': "screen and (-webkit-min-device-pixel-ratio: 1.25) and (-webkit-max-device-pixel-ratio: 1.49), " +
                                "screen and (min-resolution: 1.25dppx) and (max-resolution: 1.49dppx)",
        'pixel-ratio__1_5': "screen and (-webkit-min-device-pixel-ratio: 1.5) and (-webkit-max-device-pixel-ratio: 1.74), " +
                                "screen and (min-resolution: 1.5dppx) and (max-resolution: 1.74dppx)",
        'pixel-ratio__1_75': "screen and (-webkit-min-device-pixel-ratio: 1.75) and (-webkit-max-device-pixel-ratio: 1.99), " +
                                "screen and (min-resolution: 1.75dppx) and (max-resolution: 1.99dppx)",
    };

    for (var c in matches) {
        if ( window.matchMedia(matches[c]).matches ) {
            document.body.classList.add(c);
            break;
        }
    }

    if ( window.isIEBrowser !== true ) {
        matches = {
            'pixel-ratio__2_5': 'screen and (-webkit-min-device-pixel-ratio: 2.25), screen and (min-resolution: 2.25dppx)',
        };
        for (let c in matches) {
            if ( window.matchMedia(matches[c]).matches ) {
                document.body.classList.add(c);
                Common.Utils.injectSvgIcons();
                break;
            }
        }
    }
}

let svg_icons = ['./resources/img/<EMAIL>',
    './resources/img/<EMAIL>', './resources/img/<EMAIL>'];

window.Common = {
    Utils: {
        injectSvgIcons: function () {
            if ( window.isIEBrowser === true ) return;

            let runonce;
            // const el = document.querySelector('div.inlined-svg');
            // if (!el || !el.innerHTML.firstChild) {
            if ( !runonce ) {
                runonce = true;
                function htmlToElements(html) {
                    var template = document.createElement('template');
                    template.innerHTML = html;
                    // return template.content.childNodes;
                    return template.content.firstChild;
                }

                svg_icons.map(function (url) {
                            fetch(url)
                                .then(function (r) {
                                    if (r.ok) return r.text();
                                    else {/* error */}
                                }).then(function (text) {
                                    const el = document.querySelector('div.inlined-svg')
                                    el.appendChild(htmlToElements(text));

                                    const i = svg_icons.findIndex(function (item) {return item == url});
                                    if ( !(i < 0) ) svg_icons.splice(i, 1)
                                }).catch(console.error.bind(console))
                        })
            }
        }
    }
}

!params.skipScaling && checkScaling();

if ( !window.uitheme.id && !!params.uitheme ) {
    if ( params.uitheme == 'default-dark' ) {
        window.uitheme.id = 'theme-dark';
        window.uitheme.type = 'dark';
    } else
    if ( params.uitheme == 'default-light' ) {
        window.uitheme.id = 'theme-classic-light';
        window.uitheme.type = 'light';
    } else
    if ( params.uitheme == 'theme-system' ) {
        window.uitheme.adapt_to_system_theme();
    } else {
        window.uitheme.id = params.uitheme;
    }
}

if ( !window.uitheme.id ) {
    window.uitheme.adapt_to_system_theme();
} else {
    !window.uitheme.type && params.uitheme && (window.uitheme.type = params.uithemetype);
}

document.body.classList.add(window.uitheme.relevant_theme_id());

if ( window.uitheme.type == 'dark' ) {
    document.body.classList.add("theme-type-dark");

    if ( checkLocalStorage && localStorage.getItem("content-theme") == 'dark' ) {
        document.body.classList.add("content-theme-dark");
    } else {
    // document.body.classList.add("theme-type-ligth");
    }
}

if ( !window.is_system_theme_dark )
    delete window.is_system_theme_dark;

</script>
      <script>
/*
 * (c) Copyright Ascensio System SIA 2010-2024
 *
 * This program is a free software product. You can redistribute it and/or
 * modify it under the terms of the GNU Affero General Public License (AGPL)
 * version 3 as published by the Free Software Foundation. In accordance with
 * Section 7(a) of the GNU AGPL its Section 15 shall be amended to the effect
 * that Ascensio System SIA expressly excludes the warranty of non-infringement
 * of any third-party rights.
 *
 * This program is distributed WITHOUT ANY WARRANTY; without even the implied
 * warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR  PURPOSE. For
 * details, see the GNU AGPL at: http://www.gnu.org/licenses/agpl-3.0.html
 *
 * You can contact Ascensio System SIA at 20A-6 Ernesta Birznieka-Upish
 * street, Riga, Latvia, EU, LV-1050.
 *
 * The  interactive user interfaces in modified source and object code versions
 * of the Program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU AGPL version 3.
 *
 * Pursuant to Section 7(b) of the License you must retain the original Product
 * logo when distributing the program. Pursuant to Section 7(e) we decline to
 * grant you any rights under trademark law for use of our trademarks.
 *
 * All the Product's GUI elements, including illustrations and icon sets, as
 * well as technical writing content are licensed under the terms of the
 * Creative Commons Attribution-ShareAlike 4.0 International. See the License
 * terms at http://creativecommons.org/licenses/by-sa/4.0/legalcode
 *
 */
function checkExtendedPDF(directUrl, key, url, token, callback) {
    //110 is not enough for the new PDF form    
    var limit = 300;
    if (directUrl) {
        downloadPartialy(directUrl, limit, null, function(text) {
            callback(isExtendedPDFFile(text))
        });
    } else {
        let postData = JSON.stringify({
            'url': url,
            "token": token
        });
        var handlerUrl = "../../../../downloadfile/"+encodeURIComponent(key);
        downloadPartialy(handlerUrl, limit, postData, function(text) {
            callback(isExtendedPDFFile(text))
        });
    }
}
function isExtendedPDFFile(text) {
    if (!text) {
        return false;
    }
    const indexFirst = text.indexOf('%\xCD\xCA\xD2\xA9\x0D');
    if (indexFirst === -1) {
        return false;
    }

    let pFirst = text.substring(indexFirst + 6);

    if (!(pFirst.lastIndexOf('1 0 obj\x0A<<\x0A', 0) === 0)) {
        return false;
    }

    pFirst = pFirst.substring(11);

    let signature = 'ONLYOFFICEFORM';
    const indexStream = pFirst.indexOf('stream\x0D\x0A');
    const indexMeta = pFirst.indexOf(signature);

    if (indexStream === -1 || indexMeta === -1 || indexStream < indexMeta) {
        return false;
    }

    let pMeta = pFirst.substring(indexMeta);
    pMeta = pMeta.substring(signature.length + 3);

    let indexMetaLast = pMeta.indexOf(' ');
    if (indexMetaLast === -1) {
        return false;
    }

    pMeta = pMeta.substring(indexMetaLast + 1);

    indexMetaLast = pMeta.indexOf(' ');
    if (indexMetaLast === -1) {
        return false;
    }

    return true;
}
function downloadPartialy(url, limit, postData, callback) {
    var callbackCalled = false;
    var xhr = new XMLHttpRequest();
    //value of responseText always has the current content received from the server, even if it's incomplete
    // xhr.responseType = "json"; it raises an IE error. bug 66160
    xhr.overrideMimeType('text/plain; charset=iso-8859-1');
    xhr.onreadystatechange = function () {
        if (callbackCalled) {
            return;
        }
        if (xhr.readyState === 4) {
            callbackCalled = true;
            callback(xhr.responseText);
        } else if (xhr.readyState === 3 && xhr.responseText.length >= limit) {
            callbackCalled = true;
            var res = xhr.responseText;
            xhr.abort();
            callback(res);
        }
    };
    let method = postData ? 'POST' : 'GET';
    xhr.open(method, url, true);
    xhr.setRequestHeader('Range', 'bytes=0-' + limit); // the bytes (incl.) you request
    xhr.send(postData);
}

var startCallback;
var eventFn = function(msg) {
    if (msg.origin !== window.parentOrigin && msg.origin !== window.location.origin && !(msg.origin==="null" && (window.parentOrigin==="file://" || window.location.origin==="file://"))) return;

    var data = msg.data;
    if (Object.prototype.toString.apply(data) !== '[object String]' || !window.JSON) {
        return;
    }
    try {
        data = window.JSON.parse(data)
    } catch(e) {
        data = '';
    }

    if (data && data.command==="checkParams") {
        data = data.data || {};
        checkExtendedPDF(data.directUrl, data.key, data.url, data.token, startCallback);
        _unbindWindowEvents();
    }
};

var _bindWindowEvents = function() {
    if (window.addEventListener) {
        window.addEventListener("message", eventFn, false)
    } else if (window.attachEvent) {
        window.attachEvent("onmessage", eventFn);
    }
};

var _unbindWindowEvents = function() {
    if (window.removeEventListener) {
        window.removeEventListener("message", eventFn)
    } else if (window.detachEvent) {
        window.detachEvent("onmessage", eventFn);
    }
};

function listenApiMsg(callback) {
    startCallback = callback;
    _bindWindowEvents();
}

</script>
      <div id="loading-mask" class="loadmask"></div>
      <script>
          listenApiMsg(function (isform) {
              var match = window.location.href.match(/(.*)common\/index.html/i);
              match && window.location.replace(match[1] + (isform || embed ? 'documenteditor' : 'pdfeditor') + '/' + (isform && embed ? 'forms' : embed ? 'embed' : 'main') + '/index' + (isform && embed ? '' : postfix) + '.html' +
                       window.location.search + ("&isForm=" + !!isform));
          });
      </script>
  </body>
</html>
