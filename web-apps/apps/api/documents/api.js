(()=>{"use strict";class e{handlers;counter=0;queue;debug;constructor(e){this.handlers=new Map,this.queue=[],this.debug=e}addHandler(e){const r=this.debug?t=>{console.log(this.debug,t),e(t)}:e,s=this.counter;if(this.counter++,this.handlers.set(s,r),this.queue.length>0){for(const e of this.queue)this.callHandlers(e);this.queue=[]}return new t(this.handlers,s)}fire(e){this.handlers.size>0?this.callHandlers(e):this.queue.push(structuredClone(e))}callHandlers(e){for(const t of this.handlers.values()){const r=structuredClone(e);setTimeout((()=>t(r)))}}}class t{handlers;id;constructor(e,t){this.handlers=e,this.id=t}remove(){this.handlers.delete(this.id)}}function r(e,t){const s=e,i=t;if("object"!=typeof s)return i;const n=Object.assign({},s);for(const e of Object.keys(i))n[e]=r(s[e],i[e]);return n}function s(){}function i(e=!1){const t=[];let r=!1;return{reg:function(s){e&&r?setTimeout(s):t.push(s)},unreg:function(e){-1!==t.indexOf(e)?t.splice(t.indexOf(e),1):console.log("event handler was already unregistered")},fire:function(...s){e&&r||(r=!0,t.forEach((e=>{e(...s)})))}}}let n;class o{waitForAppReady;origEditor;fromOOHandler=new e;toOOHandler=new e;placeholderId;server;fromOOHandle;constructor(e,t){this.placeholderId=e,this.init(t).catch((e=>{console.error(e)}))}async init(e){let t;await a,this.waitForAppReady=new Promise((e=>{t=e})),this.waitForAppReady.then(e?.events?.onAppReady??s).catch(s);const i=r(e,{events:{onAppReady:t,cryptPadSendMessageFromOO:e=>{this.fromOOHandler.fire(e.data.msg)}}});this.origEditor=new n(this.placeholderId,i),this.toOOHandler.addHandler((e=>this.origEditor.cryptPadMessageToOO(e)))}installLegacyChannel(){const e=i(),t=this.getIframe().contentWindow;window.addEventListener("message",(r=>{r.source===t&&e.fire(r)})),function(e,t,r){let s=!1;const n=[],o=i(!0);e.reg((function(r){if(s)return;const i=r.data;if("_READY"===i)return t("_READY"),s=!0,o.fire(),void n.forEach((function(t){e.fire(t)}));n.push(i)}));const a={},d={},c={},h=[],u={},g={query:function(e,r,s,i){const n=Math.random().toString(16).replace("0.","")+Math.random().toString(16).replace("0.",""),a=(i=i||{}).timeout||3e4;let h;a>0&&(h=setTimeout((function(){delete d[n],s("TIMEOUT")}),a)),c[n]=function(e){clearTimeout(h),delete c[n],e&&(delete d[n],s("UNHANDLED"))},d[n]=function(e,t){delete d[n],s(void 0,e.content,t)},o.reg((function(){const s={txid:n,content:r,q:e,raw:i.raw};t(i.raw?s:JSON.stringify(s))}))}},l=g.event=function(e,r,s){s=s||{},o.reg((function(){const i={content:r,q:e,raw:s.raw};t(s.raw?i:JSON.stringify(i))}))};g.on=function(e,r,s){const i=function(e,s,i){r(e.content,(function(r){const s={txid:e.txid,content:r};t(i?s:JSON.stringify(s))}),s)};return(a[e]=a[e]||[]).push(i),s||l("EV_REGISTER_HANDLER",e),{stop:function(){const t=a[e].indexOf(i);-1!==t&&a[e].splice(t,1)}}},g.whenReg=function(e,t,r){let s=r;h.indexOf(e)>-1?t():s=!0,s&&(u[e]=u[e]||[]).push(t)},g.onReg=function(e,t){g.whenReg(e,t,!0)},g.on("EV_REGISTER_HANDLER",(function(e){u[e]&&(u[e].forEach((function(e){e()})),delete u[e]),h.push(e)}));let f=!1;g.onReady=function(e){f?e():"function"==typeof e&&g.on("EV_RPC_READY",(function(){f=!0,e()}))},g.ready=function(){g.whenReg("EV_RPC_READY",(function(){g.event("EV_RPC_READY")}))},e.reg((function(e){if(!s)return;if(!e.data||"_READY"===e.data)return;let r;try{r="object"==typeof e.data?e.data:JSON.parse(e.data)}catch(e){return void console.warn(e)}void 0!==r.ack?c[r.txid]&&c[r.txid](!r.ack):"string"==typeof r.q?a[r.q]?(r.txid&&t(JSON.stringify({txid:r.txid,ack:!0})),a[r.q].forEach((function(t){t(r||JSON.parse(e.data),e,r&&r.raw),r=void 0}))):r.txid&&t(JSON.stringify({txid:r.txid,ack:!1})):void 0===r.q&&d[r.txid]&&d[r.txid](r,e)})),t("_READY"),r(g)}(e,(e=>{t.postMessage(e)}),(e=>{this.toOOHandler.addHandler((t=>{e.event("CMD",t)})),e.on("CMD",(e=>{this.fromOOHandler.fire(e)}))}))}destroyEditor(){this.fromOOHandle?.remove(),this.origEditor.destroyEditor()}getIframe(){return document.querySelector('iframe[name="frameEditor"]')}injectCSS(e){const t=this.getIframe().contentDocument.querySelector("head"),r=document.createElement("style");r.innerText=e,t.appendChild(r)}sendMessageToOO(e){this.toOOHandler.fire(e)}connectMockServer(e){this.server=e,window.APP.getImageURL=e.getImageURL?(t,r)=>{e.getImageURL(t).then(r).catch((e=>console.error(e)))}:(e,t)=>t(""),this.fromOOHandle=this.fromOOHandler.addHandler((e=>{"auth"==e.type&&this.handleAuth(e),this.server.onMessage(e)}))}handleAuth(e){const t=this.server.getInitialChanges?this.server.getInitialChanges():[],r=this.server.getParticipants();this.sendMessageToOO({type:"authChanges",changes:t}),this.sendMessageToOO({type:"auth",result:1,sessionId:"session-id",participants:r.list,locks:[],changes:t,changesIndex:0,indexUser:r.index,buildVersion:"5.2.6",buildNumber:2,licenseType:3}),this.sendMessageToOO({type:"documentOpen",data:{type:"open",status:"ok",data:{"Editor.bin":e.openCmd.url}}}),this.server.onAuth&&this.server.onAuth()}serviceCommand(e,t){this.origEditor.serviceCommand(e,t)}showMessage(...e){return this.origEditor.showMessage(...e)}processSaveResult(...e){return this.origEditor.processSaveResult(...e)}processRightsChange(...e){return this.origEditor.processRightsChange(...e)}denyEditingRights(...e){return this.origEditor.denyEditingRights(...e)}refreshHistory(...e){return this.origEditor.refreshHistory(...e)}setHistoryData(...e){return this.origEditor.setHistoryData(...e)}setEmailAddresses(...e){return this.origEditor.setEmailAddresses(...e)}setActionLink(...e){return this.origEditor.setActionLink(...e)}processMailMerge(...e){return this.origEditor.processMailMerge(...e)}downloadAs(...e){return this.origEditor.downloadAs(...e)}attachMouseEvents(...e){return this.origEditor.attachMouseEvents(...e)}detachMouseEvents(...e){return this.origEditor.detachMouseEvents(...e)}setUsers(...e){return this.origEditor.setUsers(...e)}showSharingSettings(...e){return this.origEditor.showSharingSettings(...e)}setSharingSettings(...e){return this.origEditor.setSharingSettings(...e)}insertImage(...e){return this.origEditor.insertImage(...e)}setMailMergeRecipients(...e){return this.origEditor.setMailMergeRecipients(...e)}setRevisedFile(...e){return this.origEditor.setRevisedFile(...e)}setFavorite(...e){return this.origEditor.setFavorite(...e)}requestClose(...e){return this.origEditor.requestClose(...e)}grabFocus(...e){return this.origEditor.grabFocus(...e)}blurFocus(...e){return this.origEditor.blurFocus(...e)}setReferenceData(...e){return this.origEditor.setReferenceData(...e)}}const a=async function(){let e,t;for(const r of document.getElementsByTagName("script"))try{if(new URL(r.src).pathname.endsWith("web-apps/apps/api/documents/api.js")){e=r.src,t=r;break}}catch(e){if(!(e instanceof TypeError))throw e}const r=document.createElement("script");r.setAttribute("type","text/javascript");const s=new URL("api-orig.js",e);s.search=new URL(e).search,r.setAttribute("src",s.href);const i=(a=r,new Promise((e=>{a.addEventListener("load",(()=>e()),{once:!0})})));var a;t.after(r);const d=window;d.DocsAPI=d.DocsAPI??{},d.DocsAPI.DocEditor=o,await i,n=d.DocsAPI.DocEditor,d.DocsAPI.DocEditor=o}()})();