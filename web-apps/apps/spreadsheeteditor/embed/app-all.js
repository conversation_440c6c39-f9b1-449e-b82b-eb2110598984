/*!
 * Copyright (C) Ascensio System SIA 2012-2025. All rights reserved
 *
 * https://www.onlyoffice.com/ 
 *
 * Version: 0.0.0 (build:0)
 */

if(void 0===Common)var Common={};if(Common.Locale=new function(){"use strict";var l10n=null,loadcallback,apply=!1,defLang="{{DEFAULT_LANG}}",currentLang=defLang,_4letterLangs=["pt-pt","zh-tw","sr-cyrl"],_applyLocalization=function(e){_clearRtl();try{if(e&&(loadcallback=e),l10n){for(var t in l10n){var o=t.split(".");if(o&&o.length>2){for(var n=window,i=0;i<o.length-1;++i)void 0===n[o[i]]&&(n[o[i]]=new Object),n=n[o[i]];n&&(n[o[o.length-1]]=l10n[t])}}loadcallback&&loadcallback()}else apply=!0}catch(e){}},_get=function(prop,scope){var res="";return l10n&&scope&&scope.name&&(res=l10n[scope.name+"."+prop],!res&&scope.default&&(res=scope.default)),res||(scope?eval(scope.name).prototype[prop]:"")},_getCurrentLanguage=function(){return currentLang},_getDefaultLanguage=function(){return defLang},_getLoadedLanguage=function(){return loadedLang},_getUrlParameterByName=function(e){e=e.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var t=new RegExp("[\\?&]"+e+"=([^&#]*)").exec(location.search);return null==t?"":decodeURIComponent(t[1].replace(/\+/g," "))},_requireLang=function(e){"string"!=typeof e&&(e=null);var t=(e||_getUrlParameterByName("lang")||defLang).toLowerCase().split(/[\-_]/);t=t[0]+(t.length>1?"-"+t[1]:"");var o=_4letterLangs.indexOf(t);t=o<0?t.split(/[\-]/)[0]:_4letterLangs[o],currentLang=t,fetch("locale/"+t+".json?"+window.CP_urlArgs).then((function(e){if(!e.ok){if(o>=0)throw new Error("4letters error");if(currentLang=defLang,t!=defLang)return fetch("locale/"+defLang+".json");throw new Error("server error")}return e.json()})).then((function(e){if(e.json){if(!e.ok)throw new Error("server error");return e.json()}throw l10n=e,new Error("loaded")})).then((function(e){l10n=e||{},apply&&_applyLocalization()})).catch((function(e){return/4letters/.test(e)?setTimeout((function(){_requireLang(t.split(/[\-_]/)[0])}),0):!/loaded/.test(e)&&currentLang!=defLang&&defLang&&defLang.length<3?setTimeout((function(){_requireLang(defLang)}),0):(l10n=l10n||{},apply&&_applyLocalization(),void("loaded"==e.message||(currentLang=null,console.log("fetch error: "+e))))}))},_clearRtl=function(){!_isCurrentRtl()&&document.body.classList.contains("rtl")&&(document.body.removeAttribute("dir"),document.body.classList.remove("rtl"),document.body.classList.remove("rtl-font"),document.body.setAttribute("applang",currentLang),window.isrtl=!1)};if(window.fetch)_requireLang();else{var polyfills=["../vendor/fetch/fetch.umd"];window.Promise?require(polyfills,_requireLang):require(["../vendor/es6-promise/es6-promise.auto.min"],(function(){require(polyfills,_requireLang)}))}const _isCurrentRtl=function(){return currentLang&&/^(ar|he)$/i.test(currentLang)};return{apply:_applyLocalization,get:_get,getCurrentLanguage:_getCurrentLanguage,isCurrentLanguageRtl:_isCurrentRtl,getDefaultLanguage:_getDefaultLanguage}},void 0===window.Common&&(window.Common={}),Common.Gateway=new function(){var e=this,t=$(e),o={init:function(e){t.trigger("init",e)},openDocument:function(e){t.trigger("opendocument",e)},openDocumentFromBinary:function(e){t.trigger("opendocumentfrombinary",e)},showMessage:function(e){t.trigger("showmessage",e)},applyEditRights:function(e){t.trigger("applyeditrights",e)},processSaveResult:function(e){t.trigger("processsaveresult",e)},processRightsChange:function(e){t.trigger("processrightschange",e)},refreshHistory:function(e){t.trigger("refreshhistory",e)},setHistoryData:function(e){t.trigger("sethistorydata",e)},setEmailAddresses:function(e){t.trigger("setemailaddresses",e)},setActionLink:function(e){t.trigger("setactionlink",e.url)},processMailMerge:function(e){t.trigger("processmailmerge",e)},downloadAs:function(e){t.trigger("downloadas",e)},processMouse:function(e){t.trigger("processmouse",e)},internalCommand:function(e){t.trigger("internalcommand",e)},resetFocus:function(e){t.trigger("resetfocus",e)},setUsers:function(e){t.trigger("setusers",e)},showSharingSettings:function(e){t.trigger("showsharingsettings",e)},setSharingSettings:function(e){t.trigger("setsharingsettings",e)},insertImage:function(e){t.trigger("insertimage",e)},setMailMergeRecipients:function(e){t.trigger("setmailmergerecipients",e)},setRevisedFile:function(e){t.trigger("setrevisedfile",e)},setFavorite:function(e){t.trigger("setfavorite",e)},requestClose:function(e){t.trigger("requestclose",e)},blurFocus:function(e){t.trigger("blurfocus",e)},grabFocus:function(e){t.trigger("grabfocus",e)},setReferenceData:function(e){t.trigger("setreferencedata",e)},refreshFile:function(e){t.trigger("refreshfile",e)},setRequestedDocument:function(e){t.trigger("setrequesteddocument",e)},setRequestedSpreadsheet:function(e){t.trigger("setrequestedspreadsheet",e)},setReferenceSource:function(e){t.trigger("setreferencesource",e)},startFilling:function(e){t.trigger("startfilling",e)},requestRoles:function(e){t.trigger("requestroles",e)},cryptPadMessageToOO:function(e){t.trigger("cryptPadMessageToOO",e)}},n=function(e,t){window.parent&&window.JSON&&(e.frameEditorId=window.frameEditorId,t?window.parent.postMessage(e,"*",[t]):window.parent.postMessage(window.JSON.stringify(e),"*"))},i=function(e){!function(e){if(e.origin===window.parentOrigin||e.origin===window.location.origin||"null"===e.origin&&("file://"===window.parentOrigin||"file://"===window.location.origin)){var t=e.data;if(t&&"openDocumentFromBinary"===t.command)(i=o[t.command])&&i.call(this,t.data);else if("[object String]"===Object.prototype.toString.apply(t)&&window.JSON){var n,i;try{n=window.JSON.parse(t)}catch(e){n=""}n&&(i=o[n.command])&&i.call(this,n.data)}}}(e)};return window.attachEvent?window.attachEvent("onmessage",i):window.addEventListener("message",i,!1),{appReady:function(){n({event:"onAppReady"})},requestEditRights:function(){n({event:"onRequestEditRights"})},requestHistory:function(){n({event:"onRequestHistory"})},requestHistoryData:function(e){n({event:"onRequestHistoryData",data:e})},requestRestore:function(e,t,o){n({event:"onRequestRestore",data:{version:e,url:t,fileType:o}})},requestEmailAddresses:function(){n({event:"onRequestEmailAddresses"})},requestStartMailMerge:function(){n({event:"onRequestStartMailMerge"})},requestHistoryClose:function(e){n({event:"onRequestHistoryClose"})},reportError:function(e,t){n({event:"onError",data:{errorCode:e,errorDescription:t}})},reportWarning:function(e,t){n({event:"onWarning",data:{warningCode:e,warningDescription:t}})},sendInfo:function(e){n({event:"onInfo",data:e})},setDocumentModified:function(e){n({event:"onDocumentStateChange",data:e})},internalMessage:function(e,t){n({event:"onInternalMessage",data:{type:e,data:t}})},updateVersion:function(){n({event:"onOutdatedVersion"})},downloadAs:function(e,t){n({event:"onDownloadAs",data:{url:e,fileType:t}})},requestSaveAs:function(e,t,o){n({event:"onRequestSaveAs",data:{url:e,title:t,fileType:o}})},collaborativeChanges:function(){n({event:"onCollaborativeChanges"})},requestRename:function(e){n({event:"onRequestRename",data:e})},metaChange:function(e){n({event:"onMetaChange",data:e})},documentReady:function(){n({event:"onDocumentReady"})},requestClose:function(){n({event:"onRequestClose"})},requestMakeActionLink:function(e){n({event:"onMakeActionLink",data:e})},requestUsers:function(e,t){n({event:"onRequestUsers",data:{c:e,id:t}})},requestSendNotify:function(e){n({event:"onRequestSendNotify",data:e})},requestInsertImage:function(e){n({event:"onRequestInsertImage",data:{c:e}})},requestMailMergeRecipients:function(){n({event:"onRequestMailMergeRecipients"})},requestCompareFile:function(){n({event:"onRequestCompareFile"})},requestSharingSettings:function(){n({event:"onRequestSharingSettings"})},requestCreateNew:function(){n({event:"onRequestCreateNew"})},requestReferenceData:function(e){n({event:"onRequestReferenceData",data:e})},requestOpen:function(e){n({event:"onRequestOpen",data:e})},requestSelectDocument:function(e){n({event:"onRequestSelectDocument",data:{c:e}})},requestSelectSpreadsheet:function(e){n({event:"onRequestSelectSpreadsheet",data:{c:e}})},requestReferenceSource:function(){n({event:"onRequestReferenceSource"})},requestStartFilling:function(e){n({event:"onRequestStartFilling",data:e})},startFilling:function(){n({event:"onStartFilling"})},requestFillingStatus:function(e){n({event:"onRequestFillingStatus",data:e})},switchEditorType:function(e,t){n({event:"onSwitchEditorType",data:{type:e,restart:t}})},pluginsReady:function(){n({event:"onPluginsReady"})},requestRefreshFile:function(){n({event:"onRequestRefreshFile"})},userActionRequired:function(){n({event:"onUserActionRequired"})},saveDocument:function(e){e&&n({event:"onSaveDocument",data:e.buffer},e.buffer)},submitForm:function(){n({event:"onSubmit"})},cryptPadSendMessageFromOO:function(e){n({event:"cryptPadSendMessageFromOO",data:{msg:e}})},on:function(o,n){t.on(o,(function(t,o){n.call(e,o)}))}}},void 0===window.Common&&(window.Common={}),Common.component=Common.component||{},Common.Analytics=Common.component.Analytics=new function(){var e;return{initialize:function(t,o){if(void 0===t)throw"Analytics: invalid id.";if(void 0===o||"[object String]"!==Object.prototype.toString.apply(o))throw"Analytics: invalid category type.";e=o,$("head").append('<script type="text/javascript">var _gaq = _gaq || [];_gaq.push(["_setAccount", "'+t+'"]);_gaq.push(["_trackPageview"]);(function() {var ga = document.createElement("script"); ga.type = "text/javascript"; ga.async = true;ga.src = ("https:" == document.location.protocol ? "https://ssl" : "http://www") + ".google-analytics.com/ga.js";var s = document.getElementsByTagName("script")[0]; s.parentNode.insertBefore(ga, s);})();<\/script>')},trackEvent:function(t,o,n){if(void 0!==t&&"[object String]"!==Object.prototype.toString.apply(t))throw"Analytics: invalid action type.";if(void 0!==o&&"[object String]"!==Object.prototype.toString.apply(o))throw"Analytics: invalid label type.";if(void 0!==n&&("[object Number]"!==Object.prototype.toString.apply(n)||!isFinite(n)))throw"Analytics: invalid value type.";if("undefined"!=typeof _gaq){if("undefined"===e)throw"Analytics is not initialized.";_gaq.push(["_trackEvent",e,t,o,n])}}}},function(e){"use strict";var t='[data-toggle="dropdown"]',o=function(t){e(t).on("click.bs.dropdown",this.toggle)};function n(t){var o=t.attr("data-target");o||(o=(o=t.attr("href"))&&/#[A-Za-z]/.test(o)&&o.replace(/.*(?=#[^\s]*$)/,""));var n="#"!==o?e(document).find(o):null;return n&&n.length?n:t.parent()}function i(o){o&&3===o.which||(e(".dropdown-backdrop").remove(),e(t).each((function(){var t=e(this),i=n(t),a={relatedTarget:this};i.hasClass("open")&&(o&&"click"==o.type&&/input|textarea/i.test(o.target.tagName)&&e.contains(i[0],o.target)||(i.trigger(o=e.Event("hide.bs.dropdown",a)),o.isDefaultPrevented()||(t.attr("aria-expanded","false"),i.removeClass("open").trigger(e.Event("hidden.bs.dropdown",a)))))})))}o.VERSION="3.4.1",o.prototype.toggle=function(t){var o=e(this);if(!o.is(".disabled, :disabled")){var a=n(o),r=a.hasClass("open");if(i(),!r){var s={relatedTarget:this};if(a.trigger(t=e.Event("show.bs.dropdown",s)),t.isDefaultPrevented())return;o.trigger("focus").attr("aria-expanded","true"),a.toggleClass("open").trigger(e.Event("shown.bs.dropdown",s))}return!1}},o.prototype.keydown=function(o){if(/(38|40|27|32)/.test(o.which)&&!/input|textarea/i.test(o.target.tagName)){var i=e(this);if(o.preventDefault(),o.stopPropagation(),!i.is(".disabled, :disabled")){var a=n(i),r=a.hasClass("open");if(!r&&27!=o.which||r&&27==o.which)return 27==o.which&&a.find(t).trigger("focus"),i.trigger("click");var s=a.find(".dropdown-menu li:not(.disabled):visible a");if(s.length){var l=s.index(o.target);38==o.which&&l>0&&l--,40==o.which&&l<s.length-1&&l++,~l||(l=0),s.eq(l).trigger("focus")}}}};var a=e.fn.dropdown;e.fn.dropdown=function(t){return this.each((function(){var n=e(this),i=n.data("bs.dropdown");i||n.data("bs.dropdown",i=new o(this)),"string"==typeof t&&i[t].call(n)}))},e.fn.dropdown.Constructor=o,e.fn.dropdown.noConflict=function(){return e.fn.dropdown=a,this},e(document).on("click.bs.dropdown.data-api",i).on("click.bs.dropdown.data-api",".dropdown form",(function(e){e.stopPropagation()})).on("click.bs.dropdown.data-api",t,o.prototype.toggle).on("keydown.bs.dropdown.data-api",t,o.prototype.keydown).on("keydown.bs.dropdown.data-api",".dropdown-menu",o.prototype.keydown)}(jQuery),function(e){"use strict";var t=function(t,o){this.options=o,this.$body=e(document.body),this.$element=e(t),this.$dialog=this.$element.find(".modal-dialog"),this.$backdrop=null,this.isShown=null,this.originalBodyPad=null,this.scrollbarWidth=0,this.ignoreBackdropClick=!1,this.fixedContent=".navbar-fixed-top, .navbar-fixed-bottom",this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,e.proxy((function(){this.$element.trigger("loaded.bs.modal")}),this))};function o(o,n){return this.each((function(){var i=e(this),a=i.data("bs.modal"),r=e.extend({},t.DEFAULTS,i.data(),"object"==typeof o&&o);a||i.data("bs.modal",a=new t(this,r)),"string"==typeof o?a[o](n):r.show&&a.show(n)}))}t.VERSION="3.4.1",t.TRANSITION_DURATION=300,t.BACKDROP_TRANSITION_DURATION=150,t.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},t.prototype.toggle=function(e){return this.isShown?this.hide():this.show(e)},t.prototype.show=function(o){var n=this,i=e.Event("show.bs.modal",{relatedTarget:o});this.$element.trigger(i),this.isShown||i.isDefaultPrevented()||(this.isShown=!0,this.checkScrollbar(),this.setScrollbar(),this.$body.addClass("modal-open"),this.escape(),this.resize(),this.$element.on("click.dismiss.bs.modal",'[data-dismiss="modal"]',e.proxy(this.hide,this)),this.$dialog.on("mousedown.dismiss.bs.modal",(function(){n.$element.one("mouseup.dismiss.bs.modal",(function(t){e(t.target).is(n.$element)&&(n.ignoreBackdropClick=!0)}))})),this.backdrop((function(){var i=e.support.transition&&n.$element.hasClass("fade");n.$element.parent().length||n.$element.appendTo(n.$body),n.$element.show().scrollTop(0),n.adjustDialog(),i&&n.$element[0].offsetWidth,n.$element.addClass("in"),n.enforceFocus();var a=e.Event("shown.bs.modal",{relatedTarget:o});i?n.$dialog.one("bsTransitionEnd",(function(){n.$element.trigger("focus").trigger(a)})).emulateTransitionEnd(t.TRANSITION_DURATION):n.$element.trigger("focus").trigger(a)})))},t.prototype.hide=function(o){o&&o.preventDefault(),o=e.Event("hide.bs.modal"),this.$element.trigger(o),this.isShown&&!o.isDefaultPrevented()&&(this.isShown=!1,this.escape(),this.resize(),e(document).off("focusin.bs.modal"),this.$element.removeClass("in").off("click.dismiss.bs.modal").off("mouseup.dismiss.bs.modal"),this.$dialog.off("mousedown.dismiss.bs.modal"),e.support.transition&&this.$element.hasClass("fade")?this.$element.one("bsTransitionEnd",e.proxy(this.hideModal,this)).emulateTransitionEnd(t.TRANSITION_DURATION):this.hideModal())},t.prototype.enforceFocus=function(){e(document).off("focusin.bs.modal").on("focusin.bs.modal",e.proxy((function(e){document===e.target||this.$element[0]===e.target||this.$element.has(e.target).length||this.$element.trigger("focus")}),this))},t.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keydown.dismiss.bs.modal",e.proxy((function(e){27==e.which&&this.hide()}),this)):this.isShown||this.$element.off("keydown.dismiss.bs.modal")},t.prototype.resize=function(){this.isShown?e(window).on("resize.bs.modal",e.proxy(this.handleUpdate,this)):e(window).off("resize.bs.modal")},t.prototype.hideModal=function(){var e=this;this.$element.hide(),this.backdrop((function(){e.$body.removeClass("modal-open"),e.resetAdjustments(),e.resetScrollbar(),e.$element.trigger("hidden.bs.modal")}))},t.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},t.prototype.backdrop=function(o){var n=this,i=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var a=e.support.transition&&i;if(this.$backdrop=e(document.createElement("div")).addClass("modal-backdrop "+i).appendTo(this.$body),this.$element.on("click.dismiss.bs.modal",e.proxy((function(e){this.ignoreBackdropClick?this.ignoreBackdropClick=!1:e.target===e.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus():this.hide())}),this)),a&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!o)return;a?this.$backdrop.one("bsTransitionEnd",o).emulateTransitionEnd(t.BACKDROP_TRANSITION_DURATION):o()}else if(!this.isShown&&this.$backdrop){this.$backdrop.removeClass("in");var r=function(){n.removeBackdrop(),o&&o()};e.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one("bsTransitionEnd",r).emulateTransitionEnd(t.BACKDROP_TRANSITION_DURATION):r()}else o&&o()},t.prototype.handleUpdate=function(){this.adjustDialog()},t.prototype.adjustDialog=function(){var e=this.$element[0].scrollHeight>document.documentElement.clientHeight;this.$element.css({paddingLeft:!this.bodyIsOverflowing&&e?this.scrollbarWidth:"",paddingRight:this.bodyIsOverflowing&&!e?this.scrollbarWidth:""})},t.prototype.resetAdjustments=function(){this.$element.css({paddingLeft:"",paddingRight:""})},t.prototype.checkScrollbar=function(){var e=window.innerWidth;if(!e){var t=document.documentElement.getBoundingClientRect();e=t.right-Math.abs(t.left)}this.bodyIsOverflowing=document.body.clientWidth<e,this.scrollbarWidth=this.measureScrollbar()},t.prototype.setScrollbar=function(){var t=parseInt(this.$body.css("padding-right")||0,10);this.originalBodyPad=document.body.style.paddingRight||"";var o=this.scrollbarWidth;this.bodyIsOverflowing&&(this.$body.css("padding-right",t+o),e(this.fixedContent).each((function(t,n){var i=n.style.paddingRight,a=e(n).css("padding-right");e(n).data("padding-right",i).css("padding-right",parseFloat(a)+o+"px")})))},t.prototype.resetScrollbar=function(){this.$body.css("padding-right",this.originalBodyPad),e(this.fixedContent).each((function(t,o){var n=e(o).data("padding-right");e(o).removeData("padding-right"),o.style.paddingRight=n||""}))},t.prototype.measureScrollbar=function(){var e=document.createElement("div");e.className="modal-scrollbar-measure",this.$body.append(e);var t=e.offsetWidth-e.clientWidth;return this.$body[0].removeChild(e),t};var n=e.fn.modal;e.fn.modal=o,e.fn.modal.Constructor=t,e.fn.modal.noConflict=function(){return e.fn.modal=n,this},e(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',(function(t){var n=e(this),i=n.attr("href"),a=n.attr("data-target")||i&&i.replace(/.*(?=#[^\s]+$)/,""),r=e(document).find(a),s=r.data("bs.modal")?"toggle":e.extend({remote:!/#/.test(i)&&i},r.data(),n.data());n.is("a")&&t.preventDefault(),r.one("show.bs.modal",(function(e){e.isDefaultPrevented()||r.one("hidden.bs.modal",(function(){n.is(":visible")&&n.trigger("focus")}))})),o.call(r,s,this)}))}(jQuery),function(e){"use strict";var t=["sanitize","whiteList","sanitizeFn"],o=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],n={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},i=/^(?:(?:https?|mailto|ftp|tel|file):|[^&:/?#]*(?:[/?#]|$))/gi,a=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+/]+=*$/i;function r(t,n){var r=t.nodeName.toLowerCase();if(-1!==e.inArray(r,n))return-1===e.inArray(r,o)||Boolean(t.nodeValue.match(i)||t.nodeValue.match(a));for(var s=e(n).filter((function(e,t){return t instanceof RegExp})),l=0,c=s.length;l<c;l++)if(r.match(s[l]))return!0;return!1}function s(t,o,n){if(0===t.length)return t;if(n&&"function"==typeof n)return n(t);if(!document.implementation||!document.implementation.createHTMLDocument)return t;var i=document.implementation.createHTMLDocument("sanitization");i.body.innerHTML=t;for(var a=e.map(o,(function(e,t){return t})),s=e(i.body).find("*"),l=0,c=s.length;l<c;l++){var d=s[l],u=d.nodeName.toLowerCase();if(-1!==e.inArray(u,a))for(var h=e.map(d.attributes,(function(e){return e})),p=[].concat(o["*"]||[],o[u]||[]),m=0,g=h.length;m<g;m++)r(h[m],p)||d.removeAttribute(h[m].nodeName);else d.parentNode.removeChild(d)}return i.body.innerHTML}var l=function(e,t){this.type=null,this.options=null,this.enabled=null,this.timeout=null,this.hoverState=null,this.$element=null,this.inState=null,this.init("tooltip",e,t)};l.VERSION="3.4.1",l.TRANSITION_DURATION=150,l.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1,viewport:{selector:"body",padding:0},sanitize:!0,sanitizeFn:null,whiteList:n},l.prototype.init=function(t,o,n){if(this.enabled=!0,this.type=t,this.$element=e(o),this.options=this.getOptions(n),this.$viewport=this.options.viewport&&e(document).find(e.isFunction(this.options.viewport)?this.options.viewport.call(this,this.$element):this.options.viewport.selector||this.options.viewport),this.inState={click:!1,hover:!1,focus:!1},this.$element[0]instanceof document.constructor&&!this.options.selector)throw new Error("`selector` option must be specified when initializing "+this.type+" on the window.document object!");for(var i=this.options.trigger.split(" "),a=i.length;a--;){var r=i[a];if("click"==r)this.$element.on("click."+this.type,this.options.selector,e.proxy(this.toggle,this));else if("manual"!=r){var s="hover"==r?"mouseenter":"focusin",l="hover"==r?"mouseleave":"focusout";this.$element.on(s+"."+this.type,this.options.selector,e.proxy(this.enter,this)),this.$element.on(l+"."+this.type,this.options.selector,e.proxy(this.leave,this))}}this.options.selector?this._options=e.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},l.prototype.getDefaults=function(){return l.DEFAULTS},l.prototype.getOptions=function(o){var n=this.$element.data();for(var i in n)n.hasOwnProperty(i)&&-1!==e.inArray(i,t)&&delete n[i];return(o=e.extend({},this.getDefaults(),n,o)).delay&&"number"==typeof o.delay&&(o.delay={show:o.delay,hide:o.delay}),o.sanitize&&(o.template=s(o.template,o.whiteList,o.sanitizeFn)),o},l.prototype.getDelegateOptions=function(){var t={},o=this.getDefaults();return this._options&&e.each(this._options,(function(e,n){o[e]!=n&&(t[e]=n)})),t},l.prototype.enter=function(t){var o=t instanceof this.constructor?t:e(t.currentTarget).data("bs."+this.type);if(o||(o=new this.constructor(t.currentTarget,this.getDelegateOptions()),e(t.currentTarget).data("bs."+this.type,o)),t instanceof e.Event&&(o.inState["focusin"==t.type?"focus":"hover"]=!0),o.tip().hasClass("in")||"in"==o.hoverState)o.hoverState="in";else{if(clearTimeout(o.timeout),o.hoverState="in",!o.options.delay||!o.options.delay.show)return o.show();o.timeout=setTimeout((function(){"in"==o.hoverState&&o.show()}),o.options.delay.show)}},l.prototype.isInStateTrue=function(){for(var e in this.inState)if(this.inState[e])return!0;return!1},l.prototype.leave=function(t){var o=t instanceof this.constructor?t:e(t.currentTarget).data("bs."+this.type);if(o||(o=new this.constructor(t.currentTarget,this.getDelegateOptions()),e(t.currentTarget).data("bs."+this.type,o)),t instanceof e.Event&&(o.inState["focusout"==t.type?"focus":"hover"]=!1),!o.isInStateTrue()){if(clearTimeout(o.timeout),o.hoverState="out",!o.options.delay||!o.options.delay.hide)return o.hide();o.timeout=setTimeout((function(){"out"==o.hoverState&&o.hide()}),o.options.delay.hide)}},l.prototype.show=function(){var t=e.Event("show.bs."+this.type);if(this.hasContent()&&this.enabled){this.$element.trigger(t);var o=e.contains(this.$element[0].ownerDocument.documentElement,this.$element[0]);if(t.isDefaultPrevented()||!o)return;var n=this,i=this.tip(),a=this.getUID(this.type);this.setContent(),i.attr("id",a),this.$element.attr("aria-describedby",a),this.options.animation&&i.addClass("fade");var r="function"==typeof this.options.placement?this.options.placement.call(this,i[0],this.$element[0]):this.options.placement,s=/\s?auto?\s?/i,c=s.test(r);c&&(r=r.replace(s,"")||"top"),i.detach().css({top:0,left:0,display:"block"}).addClass(r).data("bs."+this.type,this),this.options.container?i.appendTo(e(document).find(this.options.container)):i.insertAfter(this.$element),this.$element.trigger("inserted.bs."+this.type);var d=this.getPosition(),u=i[0].offsetWidth,h=i[0].offsetHeight;if(c){var p=r,m=this.getPosition(this.$viewport);r="bottom"==r&&d.bottom+h>m.bottom?"top":"top"==r&&d.top-h<m.top?"bottom":"right"==r&&d.right+u>m.width?"left":"left"==r&&d.left-u<m.left?"right":r,i.removeClass(p).addClass(r)}var g=this.getCalculatedOffset(r,d,u,h);this.applyPlacement(g,r);var f=function(){var e=n.hoverState;n.$element.trigger("shown.bs."+n.type),n.hoverState=null,"out"==e&&n.leave(n)};e.support.transition&&this.$tip.hasClass("fade")?i.one("bsTransitionEnd",f).emulateTransitionEnd(l.TRANSITION_DURATION):f()}},l.prototype.applyPlacement=function(e,t){var o=this.tip(),n=o[0].offsetWidth,i=o[0].offsetHeight,a=parseInt(o.css("margin-top"),10),r=parseInt(o.css("margin-left"),10);isNaN(a)&&(a=0),isNaN(r)&&(r=0),e.top+=a,e.left+=r,(Common.Utils||common.utils).setOffset(o,e),o.addClass("in");var s=o[0].offsetWidth,l=o[0].offsetHeight;"top"==t&&l!=i&&(e.top=e.top+i-l);var c=this.getViewportAdjustedDelta(t,e,s,l);c.left?e.left+=c.left:e.top+=c.top;var d=/top|bottom/.test(t),u=d?2*c.left-n+s:2*c.top-i+l,h=d?"offsetWidth":"offsetHeight";(Common.Utils||common.utils).setOffset(o,e),this.replaceArrow(u,o[0][h],d)},l.prototype.replaceArrow=function(e,t,o){this.arrow().css(o?"left":"top",50*(1-e/t)+"%").css(o?"top":"left","")},l.prototype.setContent=function(){var e=this.tip(),t=this.getTitle();this.options.html?(this.options.sanitize&&(t=s(t,this.options.whiteList,this.options.sanitizeFn)),e.find(".tooltip-inner").html(t)):e.find(".tooltip-inner").text(t),e.removeClass("fade in top bottom left right")},l.prototype.hide=function(t){var o=this,n=e(this.$tip),i=e.Event("hide.bs."+this.type);function a(){"in"!=o.hoverState&&n.detach(),o.$element&&o.$element.removeAttr("aria-describedby").trigger("hidden.bs."+o.type),t&&t()}if(this.$element.trigger(i),!i.isDefaultPrevented())return n.removeClass("in"),e.support.transition&&n.hasClass("fade")?n.one("bsTransitionEnd",a).emulateTransitionEnd(l.TRANSITION_DURATION):a(),this.hoverState=null,this},l.prototype.fixTitle=function(){var e=this.$element;(e.attr("title")||"string"!=typeof e.attr("data-original-title"))&&e.attr("data-original-title",e.attr("title")||"").attr("title","")},l.prototype.hasContent=function(){return this.getTitle()},l.prototype.getPosition=function(t){var o=(t=t||this.$element)[0],n="BODY"==o.tagName,i=(Common.Utils||common.utils).getBoundingClientRect(o);null==i.width&&(i=e.extend({},i,{width:i.right-i.left,height:i.bottom-i.top}));var a=window.SVGElement&&o instanceof window.SVGElement,r=n?{top:0,left:0}:a?null:(Common.Utils||common.utils).getOffset(t),s={scroll:n?document.documentElement.scrollTop||document.body.scrollTop:t.scrollTop()},l=n?{width:e(window).width(),height:e(window).height()}:null;return e.extend({},i,s,l,r)},l.prototype.getCalculatedOffset=function(e,t,o,n){return"bottom"==e?{top:t.top+t.height,left:t.left+t.width/2-o/2}:"top"==e?{top:t.top-n,left:t.left+t.width/2-o/2}:"left"==e?{top:t.top+t.height/2-n/2,left:t.left-o}:{top:t.top+t.height/2-n/2,left:t.left+t.width}},l.prototype.getViewportAdjustedDelta=function(e,t,o,n){var i={top:0,left:0};if(!this.$viewport)return i;var a=this.options.viewport&&this.options.viewport.padding||0,r=this.getPosition(this.$viewport);if(/right|left/.test(e)){var s=t.top-a-r.scroll,l=t.top+a-r.scroll+n;s<r.top?i.top=r.top-s:l>r.top+r.height&&(i.top=r.top+r.height-l)}else{var c=t.left-a,d=t.left+a+o;c<r.left?i.left=r.left-c:d>r.right&&(i.left=r.left+r.width-d)}return i},l.prototype.getTitle=function(){var e=this.$element,t=this.options;return e.attr("data-original-title")||("function"==typeof t.title?t.title.call(e[0]):t.title)},l.prototype.getUID=function(e){do{e+=~~(1e6*Math.random())}while(document.getElementById(e));return e},l.prototype.tip=function(){if(!this.$tip&&(this.$tip=e(this.options.template),1!=this.$tip.length))throw new Error(this.type+" `template` option must consist of exactly 1 top-level element!");return this.$tip},l.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},l.prototype.enable=function(){this.enabled=!0},l.prototype.disable=function(){this.enabled=!1},l.prototype.toggleEnabled=function(){this.enabled=!this.enabled},l.prototype.toggle=function(t){var o=this;t&&((o=e(t.currentTarget).data("bs."+this.type))||(o=new this.constructor(t.currentTarget,this.getDelegateOptions()),e(t.currentTarget).data("bs."+this.type,o))),t?(o.inState.click=!o.inState.click,o.isInStateTrue()?o.enter(o):o.leave(o)):o.tip().hasClass("in")?o.leave(o):o.enter(o)},l.prototype.destroy=function(){var e=this;clearTimeout(this.timeout),this.hide((function(){e.$element.off("."+e.type).removeData("bs."+e.type),e.$tip&&e.$tip.detach(),e.$tip=null,e.$arrow=null,e.$viewport=null,e.$element=null}))},l.prototype.sanitizeHtml=function(e){return s(e,this.options.whiteList,this.options.sanitizeFn)};var c=e.fn.tooltip;e.fn.tooltip=function(t){return this.each((function(){var o=e(this),n=o.data("bs.tooltip"),i="object"==typeof t&&t;!n&&/destroy|hide/.test(t)||(n||o.data("bs.tooltip",n=new l(this,i)),"string"==typeof t&&n[t]())}))},e.fn.tooltip.Constructor=l,e.fn.tooltip.noConflict=function(){return e.fn.tooltip=c,this}}(jQuery),void 0===window.Common&&(window.Common={}),Common.util=Common.util||{},Common.util.LanguageInfo=new function(){var e={54:["af","Afrikaans"],1078:["af-ZA","Afrikaans (Suid Afrika)","Afrikaans (South Africa)"],28:["sq","Shqipe"],1052:["sq-AL","Shqipe (Shqipëria)","Albanian (Albania)"],132:["gsw","Elsässisch"],1156:["gsw-FR","Elsässisch (Frànkrisch)","Alsatian (France)"],94:["am","አማርኛ"],1118:["am-ET","አማርኛ (ኢትዮጵያ)","Amharic (Ethiopia)"],1:["ar","العربية‏"],5121:["ar-DZ","العربية (الجزائر)‏","Arabic (Algeria)"],15361:["ar-BH","العربية (البحرين)‏","Arabic (Bahrain)"],3073:["ar-EG","العربية (مصر)‏","Arabic (Egypt)"],2049:["ar-IQ","العربية (العراق)‏","Arabic (Iraq)"],11265:["ar-JO","العربية (الأردن)‏","Arabic (Jordan)"],13313:["ar-KW","العربية (الكويت)‏","Arabic (Kuwait)"],12289:["ar-LB","العربية (لبنان)‏","Arabic (Lebanon)"],4097:["ar-LY","العربية (ليبيا)‏","Arabic (Libya)"],6145:["ar-MA","العربية (المملكة المغربية)‏","Arabic (Morocco)"],8193:["ar-OM","العربية (عمان)‏","Arabic (Oman)"],16385:["ar-QA","العربية (قطر)‏","Arabic (Qatar)"],1025:["ar-SA","العربية (المملكة العربية السعودية)‏","Arabic (Saudi Arabia)"],10241:["ar-SY","العربية (سوريا)‏","Arabic (Syria)"],7169:["ar-TN","العربية (تونس)‏","Arabic (Tunisia)"],14337:["ar-AE","العربية (الإمارات العربية المتحدة)‏","Arabic (U.A.E.)"],9217:["ar-YE","العربية (اليمن)‏","Arabic (Yemen)"],43:["hy","Հայերեն"],1067:["hy-AM","Հայերեն (Հայաստան)","Armenian (Armenia)"],77:["as","অসমীয়া"],1101:["as-IN","অসমীয়া (ভাৰত)","Assamese (India)"],44:["az","Azərbaycan­ılı"],29740:["az-Cyrl","Азәрбајҹан дили"],2092:["az-Cyrl-AZ","Азәрбајҹан (Азәрбајҹан)","Azeri (Cyrillic, Azerbaijan)"],30764:["az-Latn","Azərbaycan­ılı"],1068:["az-Latn-AZ","Azərbaycan­ılı (Azərbaycan)","Azeri (Latin, Azerbaijan)"],109:["ba","Башҡорт"],1133:["ba-RU","Башҡорт (Россия)","Bashkir (Russia)"],45:["eu","Euskara"],1069:["eu-ES","Euskara (Euskara)","Basque (Basque)"],35:["be","Беларуская"],1059:["be-BY","Беларуская (Беларусь)","Belarusian (Belarus)"],69:["bn","বাংলা"],2117:["bn-BD","বাংলা (বাংলাদেশ)","Bengali (Bangladesh)"],1093:["bn-IN","বাংলা (ভারত)","Bengali (India)"],30746:["bs","bosanski"],25626:["bs-Cyrl","Босански (Ћирилица)"],8218:["bs-Cyrl-BA","Босански (Босна и Херцеговина)","Bosnian (Cyrillic, Bosnia and Herzegovina)"],26650:["bs-Latn","Bosanski (Latinica)"],5146:["bs-Latn-BA","Bosanski (Bosna i Hercegovina)","Bosnian (Latin, Bosnia and Herzegovina)"],126:["br","Brezhoneg"],1150:["br-FR","Brezhoneg (Frañs)","Breton (France)"],2:["bg","Български"],1026:["bg-BG","Български (България)","Bulgarian (Bulgaria)"],3:["ca","Català"],1027:["ca-ES","Català (Català)","Catalan (Catalan)"],2051:["ca-ES-valencia","Català (Valencià)","Catalan (Valencia)"],30724:["zh","中文"],4:["zh-Hans","中文(简体)","Chinese (Simplified)"],2052:["zh-CN","中文(中华人民共和国)","Chinese (People's Republic of China)"],4100:["zh-SG","中文(新加坡)","Chinese (Simplified, Singapore)"],31748:["zh-Hant","中文(繁體)","Chinese (Traditional)"],3076:["zh-HK","中文(香港特別行政區)","Chinese (Traditional, Hong Kong S.A.R.)"],5124:["zh-MO","中文(澳門特別行政區)","Chinese (Traditional, Macao S.A.R.)"],1028:["zh-TW","中文(台灣)","Chinese (Traditional, Taiwan)"],131:["co","Corsu"],1155:["co-FR","Corsu (France)","Corsican (France)"],26:["hr","Hrvatski"],1050:["hr-HR","Hrvatski (Hrvatska)","Croatian (Croatia)"],4122:["hr-BA","Hrvatski (Bosna i Hercegovina)","Croatian (Bosnia and Herzegovina)"],5:["cs","Čeština"],1029:["cs-CZ","Čeština (Česká republika)","Czech (Czech Republic)"],6:["da","Dansk"],1030:["da-DK","Dansk (Danmark)","Danish (Denmark)"],140:["prs","درى‏"],1164:["prs-AF","درى (افغانستان)‏","Dari (Afghanistan)"],101:["dv","ދިވެހިބަސް‏"],1125:["dv-MV","ދިވެހިބަސް (ދިވެހި ރާއްޖެ)‏","Divehi (Maldives)"],19:["nl","Nederlands"],2067:["nl-BE","Nederlands (België)","Dutch (Belgium)"],1043:["nl-NL","Nederlands (Nederland)","Dutch (Netherlands)"],9:["en","English"],3081:["en-AU","English (Australia)","English (Australia)"],10249:["en-BZ","English (Belize)","English (Belize)"],4105:["en-CA","English (Canada)","English (Canada)"],9225:["en-029","English (Caribbean)","English (Caribbean)"],16393:["en-IN","English (India)","English (India)"],6153:["en-IE","English (Ireland)","English (Ireland)"],8201:["en-JM","English (Jamaica)","English (Jamaica)"],17417:["en-MY","English (Malaysia)","English (Malaysia)"],5129:["en-NZ","English (New Zealand)","English (New Zealand)"],13321:["en-PH","English (Philippines)","English (Philippines)"],18441:["en-SG","English (Singapore)","English (Singapore)"],7177:["en-ZA","English (South Africa)","English (South Africa)"],11273:["en-TT","English (Trinidad y Tobago)","English (Trinidad y Tobago)"],2057:["en-GB","English (United Kingdom)","English (United Kingdom)"],1033:["en-US","English (United States)","English (United States)"],12297:["en-ZW","English (Zimbabwe)","English (Zimbabwe)"],15369:["en-HK","English (Hong Kong)","English (Hong Kong)"],14345:["en-ID","English (Indonesia)","English (Indonesia)"],37:["et","Eesti"],1061:["et-EE","Eesti (Eesti)","Estonian (Estonia)"],56:["fo","Føroyskt"],1080:["fo-FO","Føroyskt (Føroyar)","Faroese (Faroe Islands)"],100:["fil","Filipino"],1124:["fil-PH","Filipino (Pilipinas)","Filipino (Philippines)"],11:["fi","Suomi"],1035:["fi-FI","Suomi (Suomi)","Finnish (Finland)"],12:["fr","Français"],2060:["fr-BE","Français (Belgique)","French (Belgium)"],3084:["fr-CA","Français (Canada)","French (Canada)"],1036:["fr-FR","Français (France)","French (France)"],5132:["fr-LU","Français (Luxembourg)","French (Luxembourg)"],6156:["fr-MC","Français (Principauté de Monaco)","French (Principality of Monaco)"],4108:["fr-CH","Français (Suisse)","French (Switzerland)"],15372:["fr-HT","Français (Haïti)","French (Haiti)"],9228:["fr-CG","Français (Congo-Brazzaville)","French (Congo)"],12300:["fr-CI","Français (Côte d’Ivoire)","French (Cote d'Ivoire)"],11276:["fr-CM","Français (Cameroun)","French (Cameroon)"],14348:["fr-MA","Français (Maroc)","French (Morocco)"],13324:["fr-ML","Français (Mali)","French (Mali)"],8204:["fr-RE","Français (La Réunion)","French (Reunion)"],10252:["fr-SN","Français (Sénégal)","French (Senegal)"],7180:["fr-West","French"],98:["fy","Frysk"],1122:["fy-NL","Frysk (Nederlân)","Frisian (Netherlands)"],86:["gl","Galego"],1110:["gl-ES","Galego (Galego)","Galician (Galician)"],55:["ka","ქართული"],1079:["ka-GE","ქართული (საქართველო)","Georgian (Georgia)"],7:["de","Deutsch"],3079:["de-AT","Deutsch (Österreich)","German (Austria)"],1031:["de-DE","Deutsch (Deutschland)","German (Germany)"],5127:["de-LI","Deutsch (Liechtenstein)","German (Liechtenstein)"],4103:["de-LU","Deutsch (Luxemburg)","German (Luxembourg)"],2055:["de-CH","Deutsch (Schweiz)","German (Switzerland)"],8:["el","Ελληνικά"],1032:["el-GR","Ελληνικά (Ελλάδα)","Greek (Greece)"],111:["kl","Kalaallisut"],1135:["kl-GL","Kalaallisut (Kalaallit Nunaat)","Greenlandic (Greenland)"],71:["gu","ગુજરાતી"],1095:["gu-IN","ગુજરાતી (ભારત)","Gujarati (India)"],104:["ha","Hausa"],31848:["ha-Latn","Hausa (Latin)"],1128:["ha-Latn-NG","Hausa (Nigeria)","Hausa (Latin, Nigeria)"],13:["he","עברית‏"],1037:["he-IL","עברית (ישראל)‏","Hebrew (Israel)"],57:["hi","हिंदी"],1081:["hi-IN","हिंदी (भारत)","Hindi (India)"],14:["hu","Magyar"],1038:["hu-HU","Magyar (Magyarország)","Hungarian (Hungary)"],15:["is","Íslenska"],1039:["is-IS","Íslenska (Ísland)","Icelandic (Iceland)"],112:["ig","Igbo"],1136:["ig-NG","Igbo (Nigeria)","Igbo (Nigeria)"],33:["id","Bahasa Indonesia"],1057:["id-ID","Bahasa Indonesia (Indonesia)","Indonesian (Indonesia)"],93:["iu","Inuktitut"],31837:["iu-Latn","Inuktitut (Qaliujaaqpait)"],2141:["iu-Latn-CA","Inuktitut (Kanatami, kanata)","Inuktitut (Latin, Canada)"],30813:["iu-Cans","ᐃᓄᒃᑎᑐᑦ (ᖃᓂᐅᔮᖅᐸᐃᑦ)"],1117:["iu-Cans-CA","ᐃᓄᒃᑎᑐᑦ (ᑲᓇᑕᒥ)","Inuktitut (Canada)"],60:["ga","Gaeilge"],2108:["ga-IE","Gaeilge (Éire)","Irish (Ireland)"],52:["xh","isiXhosa"],1076:["xh-ZA","isiXhosa (uMzantsi Afrika)","isiXhosa (South Africa)"],53:["zu","isiZulu"],1077:["zu-ZA","isiZulu (iNingizimu Afrika)","isiZulu (South Africa)"],16:["it","Italiano"],1040:["it-IT","Italiano (Italia)","Italian (Italy)"],2064:["it-CH","Italiano (Svizzera)","Italian (Switzerland)"],17:["ja","日本語"],1041:["ja-JP","日本語 (日本)","Japanese (Japan)"],75:["kn","ಕನ್ನಡ"],1099:["kn-IN","ಕನ್ನಡ (ಭಾರತ)","Kannada (India)"],63:["kk","Қазақ"],1087:["kk-KZ","Қазақ (Қазақстан)","Kazakh (Kazakhstan)"],83:["km","ខ្មែរ"],1107:["km-KH","ខ្មែរ (កម្ពុជា)","Khmer (Cambodia)"],134:["qut","K'iche"],1158:["qut-GT","K'iche (Guatemala)","K'iche (Guatemala)"],135:["rw","Kinyarwanda"],1159:["rw-RW","Kinyarwanda (Rwanda)","Kinyarwanda (Rwanda)"],65:["sw","Kiswahili"],1089:["sw-KE","Kiswahili (Kenya)","Kiswahili (Kenya)"],87:["kok","कोंकणी"],1111:["kok-IN","कोंकणी (भारत)","Konkani (India)"],18:["ko","한국어"],1042:["ko-KR","한국어 (대한민국)","Korean (Korea)"],64:["ky","Кыргыз"],1088:["ky-KG","Кыргыз (Кыргызстан)","Kyrgyz (Kyrgyzstan)"],84:["lo","ລາວ"],1108:["lo-LA","ລາວ (ສ.ປ.ປ. ລາວ)","Lao (Lao P.D.R.)"],38:["lv","Latviešu"],1062:["lv-LV","Latviešu (Latvija)","Latvian (Latvia)"],39:["lt","Lietuvių"],1063:["lt-LT","Lietuvių (Lietuva)","Lithuanian (Lithuania)"],31790:["dsb","Dolnoserbšćina"],2094:["dsb-DE","Dolnoserbšćina (Nimska)","Lower Sorbian (Germany)"],110:["lb","Lëtzebuergesch"],1134:["lb-LU","Lëtzebuergesch (Luxembourg)","Luxembourgish (Luxembourg)"],1071:["mk-MK","Македонски јазик (Македонија)","Macedonian (Macedonia)"],47:["mk","Македонски јазик"],62:["ms","Bahasa Melayu"],2110:["ms-BN","Bahasa Melayu (Brunei Darussalam)","Malay (Brunei Darussalam)"],1086:["ms-MY","Bahasa Melayu (Malaysia)","Malay (Malaysia)"],76:["ml","മലയാളം"],1100:["ml-IN","മലയാളം (ഭാരതം)","Malayalam (India)"],58:["mt","Malti"],1082:["mt-MT","Malti (Malta)","Maltese (Malta)"],129:["mi","Reo Māori"],1153:["mi-NZ","Reo Māori (Aotearoa)","Maori (New Zealand)"],122:["arn","Mapudungun"],1146:["arn-CL","Mapudungun (Chile)","Mapudungun (Chile)"],78:["mr","मराठी"],1102:["mr-IN","मराठी (भारत)","Marathi (India)"],124:["moh","Kanien'kéha"],1148:["moh-CA","Kanien'kéha (Canada)","Mohawk (Canada)"],80:["mn","Монгол хэл"],30800:["mn-Cyrl","Монгол хэл"],1104:["mn-MN","Монгол хэл (Монгол улс)","Mongolian (Cyrillic, Mongolia)"],31824:["mn-Mong","ᠮᠤᠨᠭᠭᠤᠯ ᠬᠡᠯᠡ"],2128:["mn-Mong-CN","ᠮᠤᠨᠭᠭᠤᠯ ᠬᠡᠯᠡ (ᠪᠦᠭᠦᠳᠡ ᠨᠠᠢᠷᠠᠮᠳᠠᠬᠤ ᠳᠤᠮᠳᠠᠳᠤ ᠠᠷᠠᠳ ᠣᠯᠣᠰ)","Mongolian (People's Republic of China)"],97:["ne","नेपाली"],1121:["ne-NP","नेपाली (नेपाल)","Nepali (Nepal)"],2145:["ne-IN","नेपाली (भारत)","Nepali (India)"],20:["no","Norsk"],31764:["nb","Norsk (bokmål)"],1044:["nb-NO","Norsk, bokmål (Norge)","Norwegian, Bokmål (Norway)"],30740:["nn","Norsk (Nynorsk)"],2068:["nn-NO","Norsk, nynorsk (Noreg)","Norwegian, Nynorsk (Norway)"],130:["oc","Occitan"],1154:["oc-FR","Occitan (França)","Occitan (France)"],72:["or","ଓଡ଼ିଆ"],1096:["or-IN","ଓଡ଼ିଆ (ଭାରତ)","Oriya (India)"],99:["ps","پښتو‏"],1123:["ps-AF","پښتو (افغانستان)‏","Pashto (Afghanistan)"],41:["fa","فارسى‏"],1065:["fa-IR","فارسى (ایران)‏","Persian (Iran)"],21:["pl","Polski"],1045:["pl-PL","Polski (Polska)","Polish (Poland)"],22:["pt","Português"],1046:["pt-BR","Português (Brasil)","Portuguese (Brazil)"],2070:["pt-PT","Português (Portugal)","Portuguese (Portugal)"],70:["pa","ਪੰਜਾਬੀ"],1094:["pa-IN","ਪੰਜਾਬੀ (ਭਾਰਤ)","Punjabi (India)"],107:["quz","Runasimi"],1131:["quz-BO","Runasimi (Qullasuyu)","Quechua (Bolivia)"],2155:["quz-EC","Runasimi (Ecuador)","Quechua (Ecuador)"],3179:["quz-PE","Runasimi (Piruw)","Quechua (Peru)"],24:["ro","Română"],1048:["ro-RO","Română (România)","Romanian (Romania)"],2072:["ro-MD","Română (Moldova)","Romanian (Republic of Moldova)"],23:["rm","Rumantsch"],1047:["rm-CH","Rumantsch (Svizra)","Romansh (Switzerland)"],25:["ru","Русский"],1049:["ru-RU","Русский (Россия)","Russian (Russia)"],2073:["ru-MD","Русский (Молдавия)","Russian (Republic of Moldova)"],28731:["smn","Sämikielâ"],9275:["smn-FI","Sämikielâ (Suomâ)","Sami (Inari, Finland)"],31803:["smj","Julevusámegiella"],4155:["smj-NO","Julevusámegiella (Vuodna)","Sami (Lule, Norway)"],5179:["smj-SE","Julevusámegiella (Svierik)","Sami (Lule, Sweden)"],59:["se","Davvisámegiella"],3131:["se-FI","Davvisámegiella (Suopma)","Sami (Northern, Finland)"],1083:["se-NO","Davvisámegiella (Norga)","Sami (Northern, Norway)"],2107:["se-SE","Davvisámegiella (Ruoŧŧa)","Sami (Northern, Sweden)"],29755:["sms","Sääm´ǩiõll"],8251:["sms-FI","Sääm´ǩiõll (Lää´ddjânnam)","Sami (Skolt, Finland)"],30779:["sma","åarjelsaemiengiele"],6203:["sma-NO","åarjelsaemiengiele (Nöörje)","Sami (Southern, Norway)"],7227:["sma-SE","åarjelsaemiengiele (Sveerje)","Sami (Southern, Sweden)"],79:["sa","संस्कृत"],1103:["sa-IN","संस्कृत (भारतम्)","Sanskrit (India)"],145:["gd","Gàidhlig"],1169:["gd-GB","Gàidhlig (An Rìoghachd Aonaichte)","Scottish Gaelic (United Kingdom)"],31770:["sr","Srpski"],27674:["sr-Cyrl","Српски (Ћирилица)"],7194:["sr-Cyrl-BA","Српски (Босна и Херцеговина)","Serbian (Cyrillic, Bosnia and Herzegovina)"],12314:["sr-Cyrl-ME","Српски (Црна Гора)","Serbian (Cyrillic, Montenegro)"],3098:["sr-Cyrl-CS","Српски (Србија и Црна Гора (Претходно))","Serbian (Cyrillic, Serbia and Montenegro (Former))"],10266:["sr-Cyrl-RS","Српски (Србија)","Serbian (Cyrillic, Serbia)"],28698:["sr-Latn","Srpski (Latinica)"],6170:["sr-Latn-BA","Srpski (Bosna i Hercegovina)","Serbian (Latin, Bosnia and Herzegovina)"],11290:["sr-Latn-ME","Srpski (Crna Gora)","Serbian (Latin, Montenegro)"],2074:["sr-Latn-CS","Srpski (Srbija i Crna Gora (Prethodno))","Serbian (Latin, Serbia and Montenegro (Former))"],9242:["sr-Latn-RS","Srpski (Srbija, Latinica)","Serbian (Latin, Serbia)"],108:["nso","Sesotho sa Leboa"],1132:["nso-ZA","Sesotho sa Leboa (Afrika Borwa)","Sesotho sa Leboa (South Africa)"],50:["tn","Setswana"],1074:["tn-ZA","Setswana (Aforika Borwa)","Setswana (South Africa)"],91:["si","සිංහ"],1115:["si-LK","සිංහල (ශ්‍රී ලංකාව)","Sinhala (Sri Lanka)"],27:["sk","Slovenčina"],1051:["sk-SK","Slovenčina (Slovenská republika)","Slovak (Slovakia)"],36:["sl","Slovenski"],1060:["sl-SI","Slovenski (Slovenija)","Slovenian (Slovenia)"],10:["es","Español"],11274:["es-AR","Español (Argentina)","Spanish (Argentina)"],16394:["es-BO","Español (Bolivia)","Spanish (Bolivia)"],13322:["es-CL","Español (Chile)","Spanish (Chile)"],9226:["es-CO","Español (Colombia)","Spanish (Colombia)"],5130:["es-CR","Español (Costa Rica)","Spanish (Costa Rica)"],7178:["es-DO","Español (República Dominicana)","Spanish (Dominican Republic)"],12298:["es-EC","Español (Ecuador)","Spanish (Ecuador)"],17418:["es-SV","Español (El Salvador)","Spanish (El Salvador)"],4106:["es-GT","Español (Guatemala)","Spanish (Guatemala)"],18442:["es-HN","Español (Honduras)","Spanish (Honduras)"],2058:["es-MX","Español (México)","Spanish (Mexico)"],19466:["es-NI","Español (Nicaragua)","Spanish (Nicaragua)"],6154:["es-PA","Español (Panamá)","Spanish (Panama)"],15370:["es-PY","Español (Paraguay)","Spanish (Paraguay)"],10250:["es-PE","Español (Perú)","Spanish (Peru)"],20490:["es-PR","Español (Puerto Rico)","Spanish (Puerto Rico)"],3082:["es-ES","Español (España, alfabetización internacional)","Spanish (Spain)"],21514:["es-US","Español (Estados Unidos)","Spanish (United States)"],14346:["es-UY","Español (Uruguay)","Spanish (Uruguay)"],8202:["es-VE","Español (Republica Bolivariana de Venezuela)","Spanish (Venezuela)"],1034:["es-ES_tradnl","Spanish"],22538:["es-419","Español (América Latina y el Caribe)","Spanish (Latin America and the Caribbean)"],23562:["es-CU","Español (Cuba)","Spanish (Cuba)"],29:["sv","Svenska"],2077:["sv-FI","Svenska (Finland)","Swedish (Finland)"],1053:["sv-SE","Svenska (Sverige)","Swedish (Sweden)"],90:["syr","ܣܘܪܝܝܐ‏"],1114:["syr-SY","ܣܘܪܝܝܐ (سوريا)‏","Syriac (Syria)"],40:["tg","Тоҷикӣ"],31784:["tg-Cyrl","Тоҷикӣ"],1064:["tg-Cyrl-TJ","Тоҷикӣ (Тоҷикистон)","Tajik (Cyrillic, Tajikistan)"],95:["tzm","Tamazight"],31839:["tzm-Latn","Tamazight (Latin)"],2143:["tzm-Latn-DZ","Tamazight (Djazaïr)","Tamazight (Latin, Algeria)"],73:["ta","தமிழ்"],1097:["ta-IN","தமிழ் (இந்தியா)","Tamil (India)"],68:["tt","Татар"],1092:["tt-RU","Татар (Россия)","Tatar (Russia)"],74:["te","తెలుగు"],1098:["te-IN","తెలుగు (భారత దేశం)","Telugu (India)"],30:["th","ไทย"],1054:["th-TH","ไทย (ไทย)","Thai (Thailand)"],81:["bo","བོད་ཡིག"],1105:["bo-CN","བོད་ཡིག (ཀྲུང་ཧྭ་མི་དམངས་སྤྱི་མཐུན་རྒྱལ་ཁབ།)","Tibetan (People's Republic of China)"],2129:["bo-BT","Tibetan (Bhutan)","Tibetan (Bhutan)"],31:["tr","Türkçe"],1055:["tr-TR","Türkçe (Türkiye)","Turkish (Turkey)"],66:["tk","Türkmençe"],1090:["tk-TM","Türkmençe (Türkmenistan)","Turkmen (Turkmenistan)"],34:["uk","Українська"],1058:["uk-UA","Українська (Україна)","Ukrainian (Ukraine)"],46:["hsb","Hornjoserbšćina"],1070:["hsb-DE","Hornjoserbšćina (Němska)","Upper Sorbian (Germany)"],32:["ur","اُردو‏"],1056:["ur-PK","اُردو (پاکستان)‏","Urdu (Islamic Republic of Pakistan)"],2080:["ur-IN","اُردو (بھارت)‏","Urdu (India)"],128:["ug","ئۇيغۇر يېزىقى‏"],1152:["ug-CN","ئۇيغۇر يېزىقى (جۇڭخۇا خەلق جۇمھۇرىيىتى)‏","Uighur (People's Republic of China)"],30787:["uz-Cyrl","Ўзбек"],2115:["uz-Cyrl-UZ","Ўзбек (Ўзбекистон)","Uzbek (Cyrillic, Uzbekistan)"],67:["uz","U'zbek"],31811:["uz-Latn","U'zbek"],1091:["uz-Latn-UZ","U'zbek (U'zbekiston Respublikasi)","Uzbek (Latin, Uzbekistan)"],42:["vi","Tiếng Việt"],1066:["vi-VN","Tiếng Việt (Việt Nam)","Vietnamese (Vietnam)"],82:["cy","Cymraeg"],1106:["cy-GB","Cymraeg (y Deyrnas Unedig)","Welsh (United Kingdom)"],136:["wo","Wolof"],1160:["wo-SN","Wolof (Sénégal)","Wolof (Senegal)"],133:["sah","Саха"],1157:["sah-RU","Саха (Россия)","Yakut (Russia)"],120:["ii","ꆈꌠꁱꂷ"],1144:["ii-CN","ꆈꌠꁱꂷ (ꍏꉸꏓꂱꇭꉼꇩ)","Yi (People's Republic of China)"],106:["yo","Yoruba"],1130:["yo-NG","Yoruba (Nigeria)","Yoruba (Nigeria)"],1126:["bin-NG","Bini (Nigeria)","Bini (Nigeria)"],1116:["chr-US","ᏣᎳᎩ (ᏌᏊ ᎢᏳᎾᎵᏍᏔᏅ ᏍᎦᏚᎩ)","Cherokee (United States)"],1127:["fuv-NG","Nigerian Fulfulde (Nigeria)","Nigerian Fulfulde (Nigeria)"],1138:["gaz-ET","West Central Oromo (Ethiopia)","West Central Oromo (Ethiopia)"],1140:["gn-PY","Guarani (Paraguay)","Guarani (Paraguay)"],1141:["haw-US","ʻŌlelo Hawaiʻi (ʻAmelika Hui Pū ʻIa)","Hawaiian (United States)"],1129:["ibb-NG","Ibibio (Nigeria)","Ibibio (Nigeria)"],1137:["kr-NG","Kanuri (Nigeria)","Kanuri (Nigeria)"],1112:["mni","Manipuri","Manipuri"],1109:["my-MM","Burmese (Myanmar)","Burmese (Myanmar)"],1145:["pap-AN","Papiamento, Netherlands Antilles","Papiamento, Netherlands Antilles"],2118:["pa-PK","Panjabi (Pakistan)","Panjabi (Pakistan)"],1165:["plt-MG","Plateau Malagasy (Madagascar)","Plateau Malagasy (Madagascar)"],1113:["sd-IN","Sindhi (India)","Sindhi (India)"],2137:["sd-PK","Sindhi (Pakistan)","Sindhi (Pakistan)"],1143:["so-SO","Soomaali (Soomaaliya)","Somali (Somalia)"],1072:["st-ZA","Southern Sotho (South Africa)","Southern Sotho (South Africa)"],1139:["ti-ER","ትግርኛ (ኤርትራ)","Tigrinya (Eritrea)"],2163:["ti-ET","ትግርኛ (ኢትዮጵያ)","Tigrinya (Ethiopia)"],1119:["tmz","Tamanaku"],3167:["tmz-MA","Tamaziɣt n laṭlaṣ (Meṛṛuk)","Tamanaku (Morocco)"],1073:["ts-ZA","Tsonga (South Africa)","Tsonga (South Africa)"],1075:["ven-ZA","South Africa","South Africa"]};return{getLocalLanguageName:function(t){return e[t]||["",t]},getLocalLanguageCode:function(t){if(t)for(var o in e)if(e[o][0].toLowerCase()===t.toLowerCase())return o;return null},getLocalLanguageDisplayName:function(t){var o=e[t];if(o){var n=o[1],i=o[2];function a(e){let t=e.replace("(","– "),o=t.lastIndexOf(")");return-1!==o&&(t=t.slice(0,o)+t.slice(o+1)),t}return i?{native:a(n),english:a(i)}:{native:n,english:""}}return null},getLanguages:function(){return e}}},!window.common&&(window.common={}),common.localStorage=new function(){var e,t,o={};Common.Gateway.on("internalcommand",(function(e){"localstorage"==e.type&&(o=e.keys)}));var n=function(e,t,n){if(a)try{localStorage.setItem(e,t)}catch(e){}else o[e]=t,!0===n&&Common.Gateway.internalMessage("localstorage",{cmd:"set",keys:{name:t}})},i=function(e){return a?localStorage.getItem(e):void 0===o[e]?null:o[e]};try{var a=!!window.localStorage}catch(e){a=!1}return{getId:function(){return e},setId:function(t){e=t},getItem:i,getBool:function(e,t){var o=i(e);return t=t||!1,null!==o?0!=parseInt(o):t},setBool:function(e,t,o){n(e,t?1:0,o)},setItem:n,removeItem:function(e){a?localStorage.removeItem(e):delete o[e]},setKeysFilter:function(e){t=e},getKeysFilter:function(){return t},itemExists:function(e){return null!==i(e)},sync:function(){a||Common.Gateway.internalMessage("localstorage",{cmd:"get",keys:t})},save:function(){a||Common.Gateway.internalMessage("localstorage",{cmd:"set",keys:o})}}},!window.common&&(window.common={}),!common.utils&&(common.utils={}),common.utils=new function(){var e,t=navigator.userAgent.toLowerCase(),o=function(e){return e.test(t)},n=!o(/opera/)&&(o(/msie/)||o(/trident/)||o(/edge/)),i=!n&&o(/\bchrome\b/),a=(e=/\bchrome\/(\d+\.\d+)/.exec(t))?parseFloat(e[1]):0,r=o(/macintosh|mac os x/),s=1,l=function(){var e={};window.AscCommon&&window.AscCommon.checkDeviceScale&&(e=window.AscCommon.checkDeviceScale(),AscCommon.correctApplicationScale(e),e.correct&&(s=e.zoom))},c=function(){return!!(i&&128<=a)&&1!==s},d=function(e){let t=e.offset();return c()?{left:t.left*s,top:t.top*s}:t},u=function(e){let t=e.position();return c()?{left:t.left*s,top:t.top*s}:t};return n||(l(),$(window).on("resize",l)),{openLink:function(e){e&&window.parent.APP.openURL(e)},dialogPrint:function(e,t){if($("#id-print-frame").remove(),e){var o=document.createElement("iframe");o.id="id-print-frame",o.style.display="none",o.style.visibility="hidden",o.style.position="fixed",o.style.right="0",o.style.bottom="0",document.body.appendChild(o),o.onload=function(){try{o.contentWindow.focus(),o.contentWindow.print(),o.contentWindow.blur(),window.focus()}catch(e){t.asc_DownloadAs(new Asc.asc_CDownloadOptions(Asc.c_oAscFileType.PDF))}},o.src=e}},htmlEncode:function(e){return $("<div/>").text(e).html()},fillUserInfo:function(e,t,o,n){var i=e||{};return i.anonymous=!i.id,!i.id&&(i.id=n),i.fullname=i.name?i.name:o,i.group&&(i.fullname=i.group.toString()+AscCommon.UserInfoParser.getSeparator()+i.fullname),i.guest=!i.name,i},fixedDigits:function(e,t,o){void 0===o&&(o="0");for(var n="",i=e.toString(),a=i.length;a<t;a++)n+=o;return n+i},getKeyByValue:function(e,t){for(var o in e)if(e.hasOwnProperty(o)&&e[o]===t)return o},getBoundingClientRect:function(e){let t=e.getBoundingClientRect();if(!c())return t;let o=s,n={};return void 0!==t.x&&(n.x=t.x*o),void 0!==t.y&&(n.y=t.y*o),void 0!==t.width&&(n.width=t.width*o),void 0!==t.height&&(n.height=t.height*o),void 0!==t.left&&(n.left=t.left*o),void 0!==t.top&&(n.top=t.top*o),void 0!==t.right&&(n.right=t.right*o),void 0!==t.bottom&&(n.bottom=t.bottom*o),n},getOffset:d,setOffset:function(e,t){var o,n,i,a,r,s,l=e.css("position"),c={};"static"===l&&(e[0].style.position="relative"),r=d(e),i=e.css("top"),s=e.css("left"),("absolute"===l||"fixed"===l)&&(i+s).indexOf("auto")>-1?(a=(o=u(e)).top,n=o.left):(a=parseFloat(i)||0,n=parseFloat(s)||0),null!=t.top&&(c.top=t.top-r.top+a),null!=t.left&&(c.left=t.left-r.left+n),e.css(c)},getPosition:u,isMac:r,isIE:n}},!window.common&&(window.common={}),!common.view&&(common.view={}),common.view.LoadMask=function(e){var t,o,n=e||$(document.body),i="",a=0,r=!1;return{show:function(){t&&o||(t=$('<div class="asc-loadmask-body" role="presentation" tabindex="-1"><i id="loadmask-spinner" class="asc-loadmask-image"></i><div class="asc-loadmask-title"></div></div>'),o=$('<div class="asc-loadmask"></div>')),$(".asc-loadmask-title",t).html(i),r||(r=!0,a=setTimeout((function(){n.append(o),n.append(t),t.css("min-width",$(".asc-loadmask-title",t).width()+108)}),500))},hide:function(){a&&(clearTimeout(a),a=0),o&&o.remove(),t&&t.remove(),o=t=null,r=!1},setTitle:function(e){if(i=e,n&&t){var o=$(".asc-loadmask-title",t);o.html(i),t.css("min-width",o.width()+108)}}}},!window.common&&(window.common={}),!common.view&&(common.view={}),common.view.modals=new function(){var e='<div class="modal fade" tabindex="-1" role="dialog" aria-labelledby="idm-title" aria-hidden="true"><div class="modal-dialog" role="document"><div class="modal-content"><div class="modal-header"><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button><h4 id="idm-title" class="modal-title">{title}</h4></div><div class="modal-body">{body}</div><div class="modal-footer">{footer}</div></div></div></div>',t='<div class="share-link"><input id="id-short-url" class="form-control" type="text" readonly/></div><div class="share-buttons"><span class="svg big-facebook" data-name="facebook"></span><span class="svg big-twitter" data-name="twitter"></span><span class="svg big-email" data-name="email"></span><div class="autotest" id="email" style="display: none"></div></div>';return{create:function(o,n){var i;if(!n&&(n="body"),"share"==o){if(window.config&&window.config.btnsShare){let e=[];const o=Object.keys(config.btnsShare);for(var a in o)e.push('<span class="svg big-'+o[a]+'" data-name="'+o[a]+'"></span>');if(e){let o=$(t);o.find(".autotest").prevAll().remove(),o.eq(1).prepend(e.join("")),t=$("<div>").append(o).html()}}i=$(e.replace(/\{title}/,this.txtShare).replace(/\{body}/,t).replace(/\{footer}/,'<button id="btn-copyshort" type="button" class="btn">'+this.txtCopy+"</button>")).appendTo(n).attr("id","dlg-share")}else"embed"==o?i=$(e.replace(/\{title}/,this.txtEmbed).replace(/\{body}/,'<div class="size-manual"><span class="caption">{width}:</span><input id="txt-embed-width" class="form-control input-xs" type="text" value="400px"><input id="txt-embed-height" class="form-control input-xs right" type="text" value="600px"><span class="right caption">{height}:</span></div><textarea id="txt-embed-url" rows="4" class="form-control" readonly></textarea>').replace(/\{width}/,this.txtWidth).replace(/\{height}/,this.txtHeight).replace(/\{footer}/,'<button id="btn-copyembed" type="button" class="btn">'+this.txtCopy+"</button>")).appendTo(n).attr("id","dlg-embed"):"password"==o&&((i=$(e.replace(/\{title}/,this.txtTitleProtected).replace(/\{body}/,'<div class="password-body"><label>{label}</label><input id="password-input" class="form-control" type="password"/><label id="password-label-error">{error}</label>{button}</div>').replace(/\{label}/,this.txtOpenFile).replace(/\{error}/,this.txtIncorrectPwd).replace(/\{button}/,'<button id="password-btn" type="button" class="btn">OK</button>')).appendTo(n).attr("id","dlg-password")).find("button.close").remove(),i.find(".modal-footer").remove());return i},txtWidth:"Width",txtHeight:"Height",txtShare:"Share Link",txtCopy:"Copy to clipboard",txtEmbed:"Embed",txtTitleProtected:"Protected file",txtOpenFile:"Enter a password to open the file",txtIncorrectPwd:"Password is incorrect"}},!window.common&&(window.common={}),!common.controller&&(common.controller={}),common.controller.modals=new function(){var e,t,o,n,i='<iframe allowtransparency="true" frameborder="0" scrolling="no" src="{embed-url}" width="{width}" height="{height}"></iframe>';function a(e,t){e.select(),document.execCommand("copy")||window.alert("Browser's error! Use keyboard shortcut [Ctrl] + [C]")}var r=function(){e=common.view.modals.create("share");var t=encodeURIComponent(n.shareUrl),o="mailto:?subject=I have shared a document with you: "+n.docTitle+"&body=I have shared a document with you: "+t;e.find("#btn-copyshort").on("click",a.bind(this,e.find("#id-short-url"))),e.find(".share-buttons > span").on("click",(function(e){if(window.config){const t=$(e.target).attr("data-name"),o=config.btnsShare[t];if(o&&o.getUrl)return void window.open(o.getUrl(n.shareUrl,n.docTitle),o.target||"",o.features||"menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=300,width=600")}var i;switch($(e.target).attr("data-name")){case"facebook":i="https://www.facebook.com/sharer/sharer.php?u="+n.shareUrl+"&t="+encodeURI(n.docTitle),window.open(i,"","menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=300,width=600");break;case"twitter":i="https://twitter.com/share?url="+t,n.docTitle&&(i+=encodeURIComponent("&text="+n.docTitle)),window.open(i,"","menubar=no,toolbar=no,resizable=yes,scrollbars=yes,height=300,width=600");break;case"email":window.open(o,"_self")}})),e.find("#id-short-url").val(n.shareUrl),e.find(".share-buttons > #email.autotest").attr("data-test",o)};function s(){var e=t.find("#txt-embed-width"),o=t.find("#txt-embed-height"),a=parseInt(e.val()),r=parseInt(o.val());a<400&&(a=400),r<600&&(r=600),t.find("#txt-embed-url").text(i.replace("{embed-url}",n.embedUrl).replace("{width}",a).replace("{height}",r)),e.val(a+"px"),o.val(r+"px")}return{init:function(e){n=e},attach:function(o){o.share&&n.shareUrl&&(e||r(),$(o.share).on("click",(function(t){e.modal("show")}))),o.embed&&n.embedUrl&&(t||function(){var e=(t=common.view.modals.create("embed")).find("#txt-embed-url");e.text(i.replace("{embed-url}",n.embedUrl).replace("{width}",400).replace("{height}",600)),t.find("#btn-copyembed").on("click",a.bind(this,e)),t.find("#txt-embed-width, #txt-embed-height").on({keypress:function(e){13==e.keyCode&&s()},focusout:function(e){s()}})}(),$(o.embed).on("click",(function(e){t.modal("show")})))},createDlgPassword:function(e){if(o)o.modal("show"),o.find("#password-input").attr("disabled",!1).addClass("error").val(""),o.find("#password-label-error").addClass("error"),o.find("#password-btn").attr("disabled",!1);else{var t=function(){e&&(o.modal("hide"),o.find("#password-input").attr("disabled",!0),o.find("#password-btn").attr("disabled",!0),setTimeout((function(){e(o.find("#password-input").val())}),350))};(o=common.view.modals.create("password")).modal({backdrop:"static",keyboard:!1}),o.modal("show"),o.find("#password-btn").on("click",(function(){t()})),o.find("#password-input").keyup((function(e){"Enter"==e.key&&t()}))}setTimeout((function(){o.find("#password-input").focus()}),500)}}},!window.common&&(window.common={}),!common.view&&(common.view={}),common.view.SearchBar=new function(){var e='<div class="asc-window search-window" style="display: none;"><div class="body">{body}</div></div>';return{create:function(t){return!t&&(t="body"),$(e.replace(/\{body}/,'<div class="search-input-group"><input type="text" id="search-bar-text" placeholder="{textFind}" autocomplete="off"><div id="search-bar-results">0/0</div></div><div class="tools"><button id="search-bar-back" class="svg-icon search-arrow-up"></button><button id="search-bar-next" class="svg-icon search-arrow-down"></button><button id="search-bar-close" class="svg-icon search-close"></button></div>').replace(/\{textFind}/,this.textFind)).appendTo(t).attr("id","dlg-search")},disableNavButtons:function(e,t){var o=""===$("#search-bar-text").val()||!t;$("#search-bar-back").attr({disabled:o}),$("#search-bar-next").attr({disabled:o})},updateResultsNumber:function(e,t){var o=$("#search-bar-results"),n=$("#search-bar-text");o.text(t&&""!==n.val()?e+1+"/"+t:"0/0")},textFind:"Find"}},!window.common&&(window.common={}),!common.controller&&(common.controller={}),common.controller.SearchBar=new function(){var e,t,o,n,i,a,r={searchText:""},s={ctrl:!1,f:!1,other:!1},l=function(){if(e||(e=common.view.SearchBar.create(),"bottom"===o.toolbarDocked?window.isrtl?e.css({left:"45px",bottom:"31px"}):e.css({right:"45px",bottom:"31px"}):window.isrtl?e.css({left:"45px",top:"31px"}):e.css({right:"45px",top:"31px"}),(t=e.find("#search-bar-text")).on("input",(function(e){common.view.SearchBar.disableNavButtons(),c(t.val())})).on("keydown",(function(e){u("keydown",t.val(),e)})),e.find("#search-bar-back").on("click",(function(e){u("back",t.val())})),e.find("#search-bar-next").on("click",(function(e){u("next",t.val())})),e.find("#search-bar-close").on("click",(function(t){p(!1),e.hide()})),common.view.SearchBar.disableNavButtons()),!e.is(":visible")){p(!0);var i=n&&n.asc_GetSelectedText()||r.searchText;t.val(i),i.length>0&&c(i),e.show(),setTimeout((function(){t.focus(),t.select()}),10)}},c=function(e){r.searchText!==e&&(r.newSearchText=e,i=new Date,void 0===a&&(a=setInterval((function(){new Date-i<400||(r.searchText=r.newSearchText,d(),clearInterval(a),a=void 0)}),10)))},d=function(e,t){var o=new Asc.asc_CFindOptions;return o.asc_setFindWhat(r.searchText),o.asc_setScanForward("back"!=e),o.asc_setIsMatchCase(!1),o.asc_setIsWholeCell(!1),o.asc_setScanOnOnlySheet(Asc.c_oAscSearchBy.Sheet),o.asc_setScanByRows(!0),o.asc_setLookIn(Asc.c_oAscFindLookIn.Formulas),o.asc_setNeedRecalc(t),o.asc_setNotSearchEmptyCells(!0),!!n.asc_findText(o)||(common.view.SearchBar.disableNavButtons(),common.view.SearchBar.updateResultsNumber(),!1)},u=function(e,t,o){("keydown"===e&&13===o.keyCode||"keydown"!==e)&&(r.searchText=t,d(e)&&a&&(clearInterval(a),a=void 0))},h=function(e,t){common.view.SearchBar.disableNavButtons(e,t),common.view.SearchBar.updateResultsNumber(e,t)},p=function(e){r.isHighlightedResults!==e&&(n.asc_selectSearchingResults(e),r.isHighlightedResults=e)},m=function(){d(void 0,!0)};return{init:function(t){o=t,$(document.body).on("keydown",(function(t){if(27===t.keyCode&&e&&e.is(":visible"))return p(!1),void e.hide();70===t.keyCode&&(s.f=!0),(t.ctrlKey||t.metaKey)&&(s.ctrl=!0),(t.altKey||t.shiftKey)&&(s.other=!0),s.f&&s.ctrl&&!s.other&&(t.preventDefault(),l())})).on("keyup",(function(){for(var e in s)s[e]=!1}))},setApi:function(e){(n=e)&&(n.asc_registerCallback("asc_onSetSearchCurrent",h),n.asc_registerCallback("asc_onActiveSheetChanged",m))},show:l}},void 0===SSE)var SSE={};SSE.ApplicationView=new function(){var e;return{create:function(){(e=$("#box-tools button")).addClass("dropdown-toggle").attr("data-toggle","dropdown").attr("aria-expanded","true"),e.parent().append('<ul class="dropdown-menu pull-right"><li><a id="idt-download"><span class="mi-icon svg-icon download"></span>'+this.txtDownload+'</a></li><li><a id="idt-print"><span class="mi-icon svg-icon print"></span>'+this.txtPrint+'</a></li><li class="divider"></li><li><a id="idt-search"><span class="mi-icon svg-icon search"></span>'+this.txtSearch+'</a></li><li class="divider"></li><li><a id="idt-share" data-toggle="modal"><span class="mi-icon svg-icon share"></span>'+this.txtShare+'</a></li><li><a id="idt-close" data-toggle="modal"><span class="mi-icon svg-icon go-to-location"></span><span class="caption">'+this.txtFileLocation+'</span></a></li><li class="divider"></li><li><a id="idt-embed" data-toggle="modal"><span class="mi-icon svg-icon embed"></span>'+this.txtEmbed+'</a></li><li><a id="idt-fullscreen"><span class="mi-icon svg-icon fullscr"></span>'+this.txtFullScreen+"</a></li></ul>")},tools:{get:function(t){return e.parent().find(t)}},txtDownload:"Download",txtPrint:"Print",txtShare:"Share",txtEmbed:"Embed",txtFullScreen:"Full Screen",txtFileLocation:"Open file location",txtSearch:"Search"}},SSE.ApplicationController=new function(){var e,t,o,n,i,a={},r={},s={},l={},c={},d=0,u=!1,h=!1,p=!0,m=[6,-15],g=-256;if("undefined"==typeof isBrowserSupported||isBrowserSupported())return common.localStorage.setId("text"),common.localStorage.setKeysFilter("sse-,asc.table"),common.localStorage.sync(),{create:function(){return u||(e=this,u=!0,$(window).resize((function(){t&&t.asc_Resize()})),window.onbeforeunload=M,(t=new Asc.spreadsheet_api({"id-view":"editor_sdk",embedded:!0,isRtlInterface:window.isrtl}))&&(t.asc_registerCallback("asc_onEndAction",I),t.asc_registerCallback("asc_onError",_),t.asc_registerCallback("asc_onDocumentContentReady",A),t.asc_registerCallback("asc_onOpenDocumentProgress",x),t.asc_registerCallback("asc_onAdvancedOptions",L),t.asc_registerCallback("asc_onSheetsChanged",w),t.asc_registerCallback("asc_onActiveSheetChanged",v),Common.Gateway.on("init",f),Common.Gateway.on("opendocument",b),Common.Gateway.on("showmessage",R),Common.Gateway.appReady(),common.controller.SearchBar.setApi(t))),e},errorDefaultMessage:"Error code: %1",unknownErrorText:"Unknown error.",convertationTimeoutText:"Conversion timeout exceeded.",convertationErrorText:"Conversion failed.",downloadErrorText:"Download failed.",criticalErrorTitle:"Error",notcriticalErrorTitle:"Warning",scriptLoadError:"The connection is too slow, some of the components could not be loaded. Please reload the page.",errorFilePassProtect:"The file is password protected and cannot be opened.",errorAccessDeny:"You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.",errorUserDrop:"The file cannot be accessed right now.",unsupportedBrowserErrorText:"Your browser is not supported.",textOf:"of",downloadTextText:"Downloading spreadsheet...",waitText:"Please, wait...",textLoadingDocument:"Loading spreadsheet",txtClose:"Close",errorFileSizeExceed:"The file size exceeds the limitation set for your server.<br>Please contact your Document Server administrator for details.",errorUpdateVersionOnDisconnect:"Internet connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.",textGuest:"Guest",textAnonymous:"Anonymous",errorForceSave:"An error occurred while saving the file. Please use the 'Download as' option to save the file to your computer hard drive or try again later.",errorLoadingFont:"Fonts are not loaded.<br>Please contact your Document Server administrator.",errorTokenExpire:"The document security token has expired.<br>Please contact your Document Server administrator.",openErrorText:"An error has occurred while opening the file",errorInconsistentExtDocx:"An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.",errorInconsistentExtXlsx:"An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.",errorInconsistentExtPptx:"An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.",errorInconsistentExtPdf:"An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.",errorInconsistentExt:"An error has occurred while opening the file.<br>The file content does not match the file extension.",titleLicenseExp:"License expired",titleLicenseNotActive:"License not active",warnLicenseBefore:"License not active. Please contact your administrator.",warnLicenseExp:"Your license has expired. Please update your license and refresh the page.",errorEditingDownloadas:"An error occurred during the work with the document.<br>Use the 'Download as...' option to save the file backup copy to your computer hard drive.",errorToken:"The document security token is not correctly formed.<br>Please contact your Document Server administrator.",txtPressLink:"Click the link to open it"};function f(e){a=$.extend(a,e.config),s=$.extend(s,e.config.embedded),common.controller.modals.init(s),common.controller.SearchBar.init(s),"bottom"===s.toolbarDocked?($("#toolbar").addClass("bottom"),$(".viewer").addClass("bottom"),$("#box-tools").removeClass("dropdown").addClass("dropup"),m[1]=-40):($("#toolbar").addClass("top"),$(".viewer").addClass("top")),a.mode="view",a.canCloseEditor=!1;var o=!1;"object"==typeof a.customization&&("object"==typeof a.customization.goback&&!1!==a.canBackToFolder&&(o=void 0===a.customization.close?a.customization.goback.url||a.customization.goback.requestClose&&a.canRequestClose:a.customization.goback.url&&!a.customization.goback.requestClose,a.customization.goback.requestClose&&console.log("Obsolete: The 'requestClose' parameter of the 'customization.goback' section is deprecated. Please use 'close' parameter in the 'customization' section instead.")),a.customization.close&&"object"==typeof a.customization.close&&(a.canCloseEditor=!1!==a.customization.close.visible&&a.canRequestClose&&!a.isDesktopApp)),a.canBackToFolder=!!o;var n="string"==typeof a.region?a.region.toLowerCase():a.region;n=null!==(n=Common.util.LanguageInfo.getLanguages().hasOwnProperty(n)?n:Common.util.LanguageInfo.getLocalLanguageCode(n))?parseInt(n):a.lang?parseInt(Common.util.LanguageInfo.getLocalLanguageCode(a.lang)):1033,t.asc_setLocale(n)}function b(o){if(r=o.doc){l=$.extend(l,r.permissions);var n=new Asc.asc_CDocInfo,c=new Asc.asc_CUserInfo,d=!("object"==typeof a.customization&&"object"==typeof a.customization.anonymous&&!1===a.customization.anonymous.request),u="object"==typeof a.customization&&"object"==typeof a.customization.anonymous&&"string"==typeof a.customization.anonymous.label&&""!==a.customization.anonymous.label.trim()?common.utils.htmlEncode(a.customization.anonymous.label):e.textGuest,h=d?common.localStorage.getItem("guest-username"):null,p=common.utils.fillUserInfo(a.user,a.lang,h?h+" ("+u+")":e.textAnonymous,common.localStorage.getItem("guest-id")||"uid-"+Date.now());p.anonymous&&common.localStorage.setItem("guest-id",p.id),c.put_Id(p.id),c.put_FullName(p.fullname),c.put_IsAnonymousUser(p.anonymous),n.put_Id(r.key),n.put_Url(r.url),n.put_DirectUrl(r.directUrl),n.put_Title(r.title),n.put_Format(r.fileType),n.put_VKey(r.vkey),n.put_UserInfo(c),n.put_CallbackUrl(a.callbackUrl),n.put_Token(r.token),n.put_Permissions(r.permissions),n.put_EncryptedInfo(a.encryptionKeys),n.put_Lang(a.lang),n.put_Mode(a.mode),n.put_Wopi(a.wopi),a.shardkey&&n.put_Shardkey(a.shardkey);var m=!a.customization||!1!==a.customization.macros;n.asc_putIsEnabledMacroses(!!m),m=!a.customization||!1!==a.customization.plugins,n.asc_putIsEnabledPlugins(!!m),a.customization&&(void 0!==a.customization.showVerticalScroll&&null!==a.customization.showVerticalScroll&&n.asc_putShowVerticalScroll(a.customization.showVerticalScroll),void 0!==a.customization.showHorizontalScroll&&null!==a.customization.showHorizontalScroll&&n.asc_putShowHorizontalScroll(a.customization.showHorizontalScroll)),t&&(t.asc_registerCallback("asc_onGetEditorPermissions",E),t.asc_registerCallback("asc_onRunAutostartMacroses",P),t.asc_setDocInfo(n),t.asc_getEditorPermissions(a.licenseUrl,a.customerId),t.asc_enableKeyEvents(!0),Common.Analytics.trackEvent("Load","Start")),s.docTitle=r.title,(i=$("#title-doc-name")).text(s.docTitle||"")}}function v(e){var o,n=$("#worksheets");n.find("> li").removeClass("active"),n.find("#worksheet"+e).addClass("active"),t.asc_showWorksheet(e),o=$("#worksheet-container"),h=!(!t||common.utils.isIE||!t.asc_getSheetViewSettings().asc_getRightToLeft()),o.toggleClass("rtl-sheet",h),o.attr({dir:h?"rtl":"ltr"})}function w(){d=t.asc_getWorksheetsCount();var e=function(e){var t=$(this).attr("id").match(/\d+$/);t.length>0&&(t=parseInt(t[0]))>-1&&t<d&&v(t)},o=$("#worksheets");o.find("li").off(),o.empty();for(var n=0;n<d;n++)if(!t.asc_isWorksheetHidden(n)){var i="",a=t.asc_getWorksheetTabColor(n);a&&(i='style="box-shadow: inset 0 4px 0 rgb({r}, {g}, {b})"'.replace(/\{r}/,a.get_r()).replace(/\{g}/,a.get_g()).replace(/\{b}/,a.get_b()));var r=t.asc_getWorksheetName(n).replace(/[&<>"']/g,(function(e){return{"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}[e]})),s='<li id="worksheet{index}" tabtitle="{tabtitle}" {style}>{title}</li>'.replace(/\{index}/,n).replace(/\{tabtitle}/,r).replace(/\{title}/,r).replace(/\{style}/,i);$(s).appendTo(o).on("click",e)}v(t.asc_getActiveWorksheetIndex())}function y(e,t){Common.Gateway.downloadAs(e,t)}function k(){!1!==l.print&&t.asc_Print(new Asc.asc_CDownloadOptions(null,$.browser.chrome||$.browser.safari||$.browser.opera||$.browser.mozilla&&$.browser.versionNumber>86))}function S(e){common.utils.dialogPrint(e,t)}function C(){$("#loading-mask").fadeOut("slow")}function A(){if(C(),I(Asc.c_oAscAsyncActionType.BlockInteraction,g),t){t.asc_Resize();var e=a.customization&&a.customization.zoom?parseInt(a.customization.zoom)/100:1;t.asc_setZoom(e>0?e:1)}var o,n=$("#box-tools .divider"),i=$("#box-tools a").length;if(!1===l.print&&($("#idt-print").hide(),i--),s.saveUrl&&!1!==l.download||($("#idt-download").hide(),i--),s.shareUrl||($("#idt-share").hide(),i--),a.canBackToFolder){var r=a.customization.goback.text;r&&"string"==typeof r&&$("#idt-close .caption").text(r)}else $("#idt-close").hide(),i--;a.canCloseEditor&&$("#id-btn-close-editor").removeClass("hidden"),i<7&&($(n[0]).hide(),$(n[1]).hide()),s.embedUrl||($("#idt-embed").hide(),i--),s.fullscreenUrl||($("#idt-fullscreen").hide(),i--),i<1?$("#box-tools").addClass("hidden"):s.embedUrl||s.fullscreenUrl||$(n[2]).hide(),common.controller.modals.attach({share:"#idt-share",embed:"#idt-embed"}),t.asc_registerCallback("asc_onMouseMove",F),t.asc_registerCallback("asc_onHyperlinkClick",common.utils.openLink),t.asc_registerCallback("asc_onDownloadUrl",y),t.asc_registerCallback("asc_onPrint",k),t.asc_registerCallback("asc_onPrintUrl",S),t.asc_registerCallback("asc_onStartAction",T),Common.Gateway.on("processmouse",D),Common.Gateway.on("downloadas",N),Common.Gateway.on("requestclose",z),SSE.ApplicationView.tools.get("#idt-fullscreen").on("click",(function(){common.utils.openLink(s.fullscreenUrl)})),SSE.ApplicationView.tools.get("#idt-download").on("click",(function(){s.saveUrl&&!1!==l.download&&common.utils.openLink(s.saveUrl),Common.Analytics.trackEvent("Save")})),SSE.ApplicationView.tools.get("#idt-print").on("click",(function(){t.asc_Print(new Asc.asc_CDownloadOptions(null,$.browser.chrome||$.browser.safari||$.browser.opera||$.browser.mozilla&&$.browser.versionNumber>86)),Common.Analytics.trackEvent("Print")})),SSE.ApplicationView.tools.get("#idt-close").on("click",(function(){a.customization&&a.customization.goback&&(a.customization.goback.requestClose&&a.canRequestClose?Common.Gateway.requestClose():a.customization.goback.url&&(!1!==a.customization.goback.blank?window.open(a.customization.goback.url,"_blank"):window.parent.location.href=a.customization.goback.url))})),$("#id-btn-close-editor").on("click",(function(){a.canRequestClose&&Common.Gateway.requestClose()})),SSE.ApplicationView.tools.get("#idt-search").on("click",(function(){common.controller.SearchBar.show()})),$("#id-btn-zoom-in").on("click",(function(){if(t){var e=Math.floor(10*t.asc_getZoom())/10;(e+=.1)>0&&!(e>5)&&t.asc_setZoom(e)}})),$("#id-btn-zoom-out").on("click",(function(){if(t){var e=Math.ceil(10*t.asc_getZoom())/10;!((e-=.1)<.1)&&t.asc_setZoom(e)}}));var c=!1;$(document).mousemove((function(e){$("#id-btn-zoom-in").fadeIn(),$("#id-btn-zoom-out").fadeIn(),c=!0,o||(o=setInterval((function(){c||($("#id-btn-zoom-in").fadeOut(),$("#id-btn-zoom-out").fadeOut(),clearInterval(o),o=void 0),c=!1}),2e3))}));var d=!1;$(document.body).on("show.bs.modal",".modal",(function(e){d=!0,t.asc_enableKeyEvents(!1)})).on("hidden.bs.modal",".modal",(function(e){d=!1,t.asc_enableKeyEvents(!0)})).on("hidden.bs.dropdown",".dropdown",(function(e){d||t.asc_enableKeyEvents(!0)})).on("blur","input, textarea",(function(e){d||/area_id/.test(e.target.id)||t.asc_enableKeyEvents(!0)})),$("#editor_sdk").on("click",(function(e){"canvas"==e.target.localName&&e.currentTarget.focus()})),$(document).on("mousewheel",(function(e){!e.ctrlKey&&!e.metaKey||e.altKey||(e.preventDefault(),e.stopPropagation())})),Common.Gateway.documentReady(),Common.Analytics.trackEvent("Load","Complete"),p=!1,w(),function(){var e=$("#worksheet-container"),t=$("#worksheet-list-button-prev"),o=$("#worksheet-list-button-next"),n=$("#worksheets"),i=function(){if(e[0].scrollWidth>e[0].clientWidth){var n=e.scrollLeft(),i=e[0].scrollWidth,a=e.innerWidth();h?Math.abs(n)+a>=i-1?(t.prop("disabled",!1),o.prop("disabled",!0)):n>=0?(t.prop("disabled",!0),o.prop("disabled",!1)):(t.prop("disabled",!1),o.prop("disabled",!1)):0===n?(t.prop("disabled",!0),o.prop("disabled",!1)):n+a>=i?(t.prop("disabled",!1),o.prop("disabled",!0)):(t.prop("disabled",!1),o.prop("disabled",!1))}else t.prop("disabled",!0),o.prop("disabled",!0)};e.on("scroll",i),$(window).on("resize",i),i();var a=$(".worksheet-list-buttons").outerWidth();t.on("click",(function(){if(h){var t=e.width();$(n.children().get().reverse()).each((function(){var o=$(this),n=common.utils.getPosition(o).left+o.outerWidth()+a;if(n>t)return e.scrollLeft(e.scrollLeft()+n-t+(e.width()>400?20:5)),!1}))}else $(n.children().get().reverse()).each((function(){var t=$(this),o=common.utils.getPosition(t).left-a;if(o<0)return e.scrollLeft(e.scrollLeft()+o-26),!1}))})),o.on("click",(function(){if(h)$(n.children()).each((function(){var t=$(this),o=common.utils.getPosition(t).left-a;if(o<0)return e.scrollLeft(e.scrollLeft()+o-26),!1}));else{var t=e.width();n.children().each((function(){var o=$(this),n=common.utils.getPosition(o).left+o.outerWidth();if(n>t)return e.scrollLeft(e.scrollLeft()+n-t+(e.width()>400?20:5)),!1}))}}))}()}function E(o){var n=o.asc_getLicenseType();if(Asc.c_oLicenseResult.Expired===n||Asc.c_oLicenseResult.Error===n||Asc.c_oLicenseResult.ExpiredTrial===n||Asc.c_oLicenseResult.NotBefore===n||Asc.c_oLicenseResult.ExpiredLimited===n)return $("#id-critical-error-title").text(Asc.c_oLicenseResult.NotBefore===n?e.titleLicenseNotActive:e.titleLicenseExp),$("#id-critical-error-message").html(Asc.c_oLicenseResult.NotBefore===n?e.warnLicenseBefore:e.warnLicenseExp),$("#id-critical-error-close").parent().remove(),void $("#id-critical-error-dialog").css("z-index",20002).modal({backdrop:"static",keyboard:!1,show:!0});c.canBranding=o.asc_getCustomization(),c.canBranding&&function(e){if(e&&e.logo){var t=$("#header-logo");if(!1===e.logo.visible)return void t.addClass("hidden");(e.logo.image||e.logo.imageEmbedded)&&(t.html('<img src="'+(e.logo.image||e.logo.imageEmbedded)+'" style="max-width:100px; max-height:20px;"/>'),t.css({"background-image":"none",width:"auto",height:"auto"}),e.logo.imageEmbedded&&console.log("Obsolete: The 'imageEmbedded' parameter of the 'customization.logo' section is deprecated. Please use 'image' parameter instead.")),e.logo.url?t.attr("href",e.logo.url):void 0!==e.logo.url&&(t.removeAttr("href"),t.removeAttr("target"))}}(a.customization);var r=i.parent(),s=common.utils.getPosition(r).left,l=r.next().outerWidth();s<l?r.css("padding-left",parseFloat(r.css("padding-left"))+l-s):r.css("padding-right",parseFloat(r.css("padding-right"))+s-l),T(Asc.c_oAscAsyncActionType.BlockInteraction,g),t.asc_setViewMode(!0),t.asc_LoadDocument()}function x(t){var o=(t.asc_getCurrentFont()+t.asc_getCurrentImage())/(t.asc_getFontsCount()+t.asc_getImagesCount());e.loadMask&&e.loadMask.setTitle(e.textLoadingDocument+": "+common.utils.fixedDigits(Math.min(Math.round(100*o),100),3,"  ")+"%")}function T(t,o){var n="";switch(o){case Asc.c_oAscAsyncAction.Print:n=e.downloadTextText;break;case g:n=e.textLoadingDocument+"           ";break;default:n=e.waitText}t==Asc.c_oAscAsyncActionType.BlockInteraction&&(e.loadMask||(e.loadMask=new common.view.LoadMask),e.loadMask.setTitle(n),e.loadMask.show())}function I(t,o){t===Asc.c_oAscAsyncActionType.BlockInteraction&&e.loadMask&&e.loadMask.hide()}function L(o,n,i,r){if(o==Asc.c_oAscAdvancedOptionsID.DRM){var s=!!a.customization.loaderName||!!a.customization.loaderLogo;common.controller.modals.createDlgPassword((function(o){t&&t.asc_setAdvancedOptions(Asc.c_oAscAdvancedOptionsID.DRM,new Asc.asc_CDRMAdvancedOptions(o)),e.loadMask&&e.loadMask.show(),s||$("#loading-mask").removeClass("none-animation")})),s?C():$("#loading-mask").addClass("none-animation"),I(Asc.c_oAscAsyncActionType.BlockInteraction)}else o==Asc.c_oAscAdvancedOptionsID.CSV&&(t&&t.asc_setAdvancedOptions(Asc.c_oAscAdvancedOptionsID.CSV,n.asc_getRecommendedSettings()||new Asc.asc_CTextOptions),I(Asc.c_oAscAsyncActionType.BlockInteraction));p&&(Common.Gateway.userActionRequired(),p=!1)}function _(t,o,n){if(t==Asc.c_oAscError.ID.LoadingScriptError)return $("#id-critical-error-title").text(e.criticalErrorTitle),$("#id-critical-error-message").text(e.scriptLoadError),$("#id-critical-error-close").text(e.txtClose).off().on("click",(function(){window.location.reload()})),void $("#id-critical-error-dialog").css("z-index",20002).modal("show");var i;switch(C(),I(Asc.c_oAscAsyncActionType.BlockInteraction),t){case Asc.c_oAscError.ID.Unknown:i=e.unknownErrorText;break;case Asc.c_oAscError.ID.ConvertationTimeout:i=e.convertationTimeoutText;break;case Asc.c_oAscError.ID.ConvertationError:i=e.convertationErrorText;break;case Asc.c_oAscError.ID.ConvertationOpenError:i=e.openErrorText;break;case Asc.c_oAscError.ID.DownloadError:i=e.downloadErrorText;break;case Asc.c_oAscError.ID.ConvertationPassword:i=e.errorFilePassProtect;break;case Asc.c_oAscError.ID.UserDrop:i=e.errorUserDrop;break;case Asc.c_oAscError.ID.ConvertationOpenLimitError:i=e.errorFileSizeExceed;break;case Asc.c_oAscError.ID.UpdateVersion:i=e.errorUpdateVersionOnDisconnect;break;case Asc.c_oAscError.ID.AccessDeny:i=e.errorAccessDeny;break;case Asc.c_oAscError.ID.ForceSaveButton:case Asc.c_oAscError.ID.ForceSaveTimeout:i=e.errorForceSave;break;case Asc.c_oAscError.ID.LoadingFontError:i=e.errorLoadingFont;break;case Asc.c_oAscError.ID.KeyExpire:i=e.errorTokenExpire;break;case Asc.c_oAscError.ID.VKeyEncrypt:i=e.errorToken;break;case Asc.c_oAscError.ID.ConvertationOpenFormat:i="pdf"===n?e.errorInconsistentExtPdf.replace("%1",r.fileType||""):"docx"===n?e.errorInconsistentExtDocx.replace("%1",r.fileType||""):"xlsx"===n?e.errorInconsistentExtXlsx.replace("%1",r.fileType||""):"pptx"===n?e.errorInconsistentExtPptx.replace("%1",r.fileType||""):e.errorInconsistentExt;break;case Asc.c_oAscError.ID.SessionToken:return;case Asc.c_oAscError.ID.EditingError:i=e.errorEditingDownloadas;break;default:return}o==Asc.c_oAscError.Level.Critical?(Common.Gateway.reportError(t,i),$("#id-critical-error-title").text(e.criticalErrorTitle),$("#id-critical-error-message").html(i),$("#id-critical-error-close").text(e.txtClose).off().on("click",(function(){window.location.reload()}))):(Common.Gateway.reportWarning(t,i),$("#id-critical-error-title").text(e.notcriticalErrorTitle),$("#id-critical-error-message").html(i),$("#id-critical-error-close").text(e.txtClose).off().on("click",(function(){$("#id-critical-error-dialog").modal("hide")}))),$("#id-critical-error-dialog").modal("show"),Common.Analytics.trackEvent("Internal Error",t.toString())}function R(t){t&&(C(),$("#id-error-mask-title").text(e.criticalErrorTitle),$("#id-error-mask-text").text(t.msg),$("#id-error-mask").css("display","block"),Common.Analytics.trackEvent("External Error"))}function D(e){if("mouseup"==e.type){var o=document.getElementById("editor_sdk");if(o){var n=common.utils.getBoundingClientRect(o),i=window.event||arguments.callee.caller.arguments[0];t.asc_onMouseUp(i,e.x-n.left,e.y-n.top)}}}function z(){Common.Gateway.requestClose()}function N(){if(!1!==l.download){if(t){var o=new Asc.asc_CDownloadOptions(Asc.c_oAscFileType.XLSX,!0);o.asc_setIsSaveAs(!0),t.asc_DownloadAs(o)}}else Common.Gateway.reportError(Asc.c_oAscError.ID.AccessDeny,e.errorAccessDeny)}function F(t){if(t.length){for(var i,a=t.length;a>0;a--)if(t[a-1].asc_getType()==Asc.c_oAscMouseMoveType.Hyperlink){i=t[a-1];break}i?(o||((o=$(".hyperlink-tooltip")).tooltip({container:"body",trigger:"manual"}),o.on("shown.bs.tooltip",(function(e){(n=o.data("bs.tooltip").tip()).css({left:o.ttpos[0]+m[0],top:o.ttpos[1]+m[1]}),n.find(".tooltip-arrow").css({left:10})})),o.data("bs.tooltip").options.title=e.txtPressLink),n?n.css({left:i.asc_getX()+m[0],top:i.asc_getY()+m[1]}):(o.ttpos=[i.asc_getX(),i.asc_getY()],o.tooltip("show"))):n&&(n.tooltip("hide"),n=!1)}}function P(){a.customization&&!1===a.customization.macros||t&&t.asc_runAutostartMacroses()}function M(){common.localStorage.save()}Common.Gateway.reportError(void 0,this.unsupportedBrowserErrorText)},(0,window.jQuery)((function(){Common.Locale.apply((function(){SSE.ApplicationView.create(),SSE.ApplicationController.create()}))}));