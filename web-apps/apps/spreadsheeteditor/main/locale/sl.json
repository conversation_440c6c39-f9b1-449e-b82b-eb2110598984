{"cancelButtonText": "Prekliči", "Common.Controllers.Chat.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.History.notcriticalErrorTitle": "Warning", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "Ploščinski grafikon", "Common.define.chartData.textAreaStacked": "Stacked area", "Common.define.chartData.textAreaStackedPer": "100% Stacked area", "Common.define.chartData.textBar": "Stolpični grafikon", "Common.define.chartData.textBarNormal": "Clustered column", "Common.define.chartData.textBarNormal3d": "3-D Clustered column", "Common.define.chartData.textBarNormal3dPerspective": "3-D column", "Common.define.chartData.textBarStacked": "Stacked column", "Common.define.chartData.textBarStacked3d": "3-<PERSON> Stacked column", "Common.define.chartData.textBarStackedPer": "100% Stacked column", "Common.define.chartData.textBarStackedPer3d": "3-D 100% Stacked column", "Common.define.chartData.textCharts": "<PERSON><PERSON>", "Common.define.chartData.textColumn": "Stolpični grafikon", "Common.define.chartData.textColumnSpark": "Stolpec", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Stacked area - clustered column", "Common.define.chartData.textComboBarLine": "Clustered column - line", "Common.define.chartData.textComboBarLineSecondary": "Clustered column - line on secondary axis", "Common.define.chartData.textComboCustom": "Custom combination", "Common.define.chartData.textDoughnut": "Doughnut", "Common.define.chartData.textHBarNormal": "Clustered bar", "Common.define.chartData.textHBarNormal3d": "3-D Clustered bar", "Common.define.chartData.textHBarStacked": "Stacked bar", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> Stacked bar", "Common.define.chartData.textHBarStackedPer": "100% Stacked bar", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% Stacked bar", "Common.define.chartData.textLine": "Vrstični grafikon", "Common.define.chartData.textLine3d": "3-D line", "Common.define.chartData.textLineMarker": "Line with markers", "Common.define.chartData.textLineSpark": "Line", "Common.define.chartData.textLineStacked": "Stacked line", "Common.define.chartData.textLineStackedMarker": "Stacked line with markers", "Common.define.chartData.textLineStackedPer": "100% Stacked line", "Common.define.chartData.textLineStackedPerMarker": "100% Stacked line with markers", "Common.define.chartData.textPie": "Tortni grafikon", "Common.define.chartData.textPie3d": "3-D pie", "Common.define.chartData.textPoint": "Točkovni grafikon", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Filled radar", "Common.define.chartData.textRadarMarker": "Radar with markers", "Common.define.chartData.textScatter": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "Scatter with straight lines", "Common.define.chartData.textScatterLineMarker": "Scatter with straight lines and markers", "Common.define.chartData.textScatterSmooth": "Scatter with smooth lines", "Common.define.chartData.textScatterSmoothMarker": "Scatter with smooth lines and markers", "Common.define.chartData.textSparks": "Sparklines", "Common.define.chartData.textStock": "Založni grafikon", "Common.define.chartData.textSurface": "Surface", "Common.define.chartData.textWinLossSpark": "Win/Loss", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "No format set", "Common.define.conditionalData.text1Above": "1 std dev above", "Common.define.conditionalData.text1Below": "1 std dev below", "Common.define.conditionalData.text2Above": "2 std dev above", "Common.define.conditionalData.text2Below": "2 std dev below", "Common.define.conditionalData.text3Above": "3 std dev above", "Common.define.conditionalData.text3Below": "3 std dev below", "Common.define.conditionalData.textAbove": "Nad", "Common.define.conditionalData.textAverage": "Povprečje", "Common.define.conditionalData.textBegins": "Se začne z", "Common.define.conditionalData.textBelow": "Pod", "Common.define.conditionalData.textBetween": "Med", "Common.define.conditionalData.textBlank": "Prazen", "Common.define.conditionalData.textBlanks": "Contains blanks", "Common.define.conditionalData.textBottom": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textContains": "Contains", "Common.define.conditionalData.textDataBar": "Data bar", "Common.define.conditionalData.textDate": "Datum", "Common.define.conditionalData.textDuplicate": "Duplicate", "Common.define.conditionalData.textEnds": "Ends with", "Common.define.conditionalData.textEqAbove": "Equal to or above", "Common.define.conditionalData.textEqBelow": "Equal to or below", "Common.define.conditionalData.textEqual": "Equal to", "Common.define.conditionalData.textError": "Napaka", "Common.define.conditionalData.textErrors": "Contains errors", "Common.define.conditionalData.textFormula": "Formula", "Common.define.conditionalData.textGreater": "Greater than", "Common.define.conditionalData.textGreaterEq": "Greater than or equal to", "Common.define.conditionalData.textIconSets": "Icon sets", "Common.define.conditionalData.textLast7days": "In the last 7 days", "Common.define.conditionalData.textLastMonth": "Last month", "Common.define.conditionalData.textLastWeek": "Last week", "Common.define.conditionalData.textLess": "Less than", "Common.define.conditionalData.textLessEq": "Less than or equal to", "Common.define.conditionalData.textNextMonth": "Next month", "Common.define.conditionalData.textNextWeek": "Next week", "Common.define.conditionalData.textNotBetween": "Not between", "Common.define.conditionalData.textNotBlanks": "Does not contain blanks", "Common.define.conditionalData.textNotContains": "Ne vsebuje", "Common.define.conditionalData.textNotEqual": "Not equal to", "Common.define.conditionalData.textNotErrors": "Does not contain errors", "Common.define.conditionalData.textText": "Text", "Common.define.conditionalData.textThisMonth": "This month", "Common.define.conditionalData.textThisWeek": "This week", "Common.define.conditionalData.textToday": "Today", "Common.define.conditionalData.textTomorrow": "Tomorrow", "Common.define.conditionalData.textTop": "Top", "Common.define.conditionalData.textUnique": "Unique", "Common.define.conditionalData.textValue": "Value is", "Common.define.conditionalData.textYesterday": "Yesterday", "Common.define.smartArt.textAccentedPicture": "Accented Picture", "Common.define.smartArt.textAccentProcess": "Accent Process", "Common.define.smartArt.textAlternatingFlow": "Alternating flow", "Common.define.smartArt.textAlternatingHexagons": "Alternating hexagons", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternating picture blocks", "Common.define.smartArt.textAlternatingPictureCircles": "Alternating picture circles", "Common.define.smartArt.textArchitectureLayout": "Architecture layout", "Common.define.smartArt.textArrowRibbon": "Arrow ribbon", "Common.define.smartArt.textAscendingPictureAccentProcess": "Ascending picture accent process", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Basic bending process", "Common.define.smartArt.textBasicBlockList": "Basic block list", "Common.define.smartArt.textBasicChevronProcess": "Basic chevron process", "Common.define.smartArt.textBasicCycle": "Basic cycle", "Common.define.smartArt.textBasicMatrix": "Basic matrix", "Common.define.smartArt.textBasicPie": "Basic Pie", "Common.define.smartArt.textBasicProcess": "Basic process", "Common.define.smartArt.textBasicPyramid": "Basic pyramid", "Common.define.smartArt.textBasicRadial": "Basic radial", "Common.define.smartArt.textBasicTarget": "Basic target", "Common.define.smartArt.textBasicTimeline": "Basic timeline", "Common.define.smartArt.textBasicVenn": "Basic Venn", "Common.define.smartArt.textBendingPictureAccentList": "Bending picture accent list", "Common.define.smartArt.textBendingPictureBlocks": "Bending picture blocks", "Common.define.smartArt.textBendingPictureCaption": "Bending picture caption", "Common.define.smartArt.textBendingPictureCaptionList": "Bending picture caption list", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Bending picture semi-transparent text", "Common.define.smartArt.textBlockCycle": "Block cycle", "Common.define.smartArt.textBubblePictureList": "Bubble picture list", "Common.define.smartArt.textCaptionedPictures": "Captioned pictures", "Common.define.smartArt.textChevronAccentProcess": "Chevron accent process", "Common.define.smartArt.textChevronList": "Chevron list", "Common.define.smartArt.textCircleAccentTimeline": "Circle accent timeline", "Common.define.smartArt.textCircleArrowProcess": "Circle arrow process", "Common.define.smartArt.textCirclePictureHierarchy": "Circle picture hierarchy", "Common.define.smartArt.textCircleProcess": "Circle process", "Common.define.smartArt.textCircleRelationship": "Circle relationship", "Common.define.smartArt.textCircularBendingProcess": "Circular bending process", "Common.define.smartArt.textCircularPictureCallout": "Circular picture callout", "Common.define.smartArt.textClosedChevronProcess": "Closed chevron process", "Common.define.smartArt.textContinuousArrowProcess": "Continuous arrow process", "Common.define.smartArt.textContinuousBlockProcess": "Continuous block process", "Common.define.smartArt.textContinuousCycle": "Continuous cycle", "Common.define.smartArt.textContinuousPictureList": "Continuous picture list", "Common.define.smartArt.textConvergingArrows": "Converging arrows", "Common.define.smartArt.textConvergingRadial": "Converging radial", "Common.define.smartArt.textConvergingText": "Converging text", "Common.define.smartArt.textCounterbalanceArrows": "Counterbalance arrows", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "Cycle matrix", "Common.define.smartArt.textDescendingBlockList": "Descending block list", "Common.define.smartArt.textDescendingProcess": "Descending process", "Common.define.smartArt.textDetailedProcess": "Detailed process", "Common.define.smartArt.textDivergingArrows": "Diverging arrows", "Common.define.smartArt.textDivergingRadial": "Diverging radial", "Common.define.smartArt.textEquation": "Equation", "Common.define.smartArt.textFramedTextPicture": "Framed text picture", "Common.define.smartArt.textFunnel": "Funnel", "Common.define.smartArt.textGear": "Gear", "Common.define.smartArt.textGridMatrix": "Grid matrix", "Common.define.smartArt.textGroupedList": "Grouped list", "Common.define.smartArt.textHalfCircleOrganizationChart": "Half circle organization chart", "Common.define.smartArt.textHexagonCluster": "Hexagon cluster", "Common.define.smartArt.textHexagonRadial": "Hexagon radial", "Common.define.smartArt.textHierarchy": "Hierarchy", "Common.define.smartArt.textHierarchyList": "Hierarchy list", "Common.define.smartArt.textHorizontalBulletList": "Horizontal bullet list", "Common.define.smartArt.textHorizontalHierarchy": "Horizontal hierarchy", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal labeled hierarchy", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontal multi-level hierarchy", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontal organization chart", "Common.define.smartArt.textHorizontalPictureList": "Horizontal picture list", "Common.define.smartArt.textIncreasingArrowProcess": "Increasing arrow process", "Common.define.smartArt.textIncreasingCircleProcess": "Increasing circle process", "Common.define.smartArt.textInterconnectedBlockProcess": "Interconnected block process", "Common.define.smartArt.textInterconnectedRings": "Interconnected rings", "Common.define.smartArt.textInvertedPyramid": "Inverted pyramid", "Common.define.smartArt.textLabeledHierarchy": "Labeled hierarchy", "Common.define.smartArt.textLinearVenn": "Linear Venn", "Common.define.smartArt.textLinedList": "Lined list", "Common.define.smartArt.textList": "List", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Multidirectional cycle", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Name and title organization chart", "Common.define.smartArt.textNestedTarget": "Nested target", "Common.define.smartArt.textNondirectionalCycle": "Nondirectional cycle", "Common.define.smartArt.textOpposingArrows": "Opposing arrows", "Common.define.smartArt.textOpposingIdeas": "Opposing ideas", "Common.define.smartArt.textOrganizationChart": "Organization chart", "Common.define.smartArt.textOther": "Other", "Common.define.smartArt.textPhasedProcess": "Phased process", "Common.define.smartArt.textPicture": "Picture", "Common.define.smartArt.textPictureAccentBlocks": "Picture accent blocks", "Common.define.smartArt.textPictureAccentList": "Picture accent list", "Common.define.smartArt.textPictureAccentProcess": "Picture accent process", "Common.define.smartArt.textPictureCaptionList": "Picture caption list", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "Picture grid", "Common.define.smartArt.textPictureLineup": "Picture lineup", "Common.define.smartArt.textPictureOrganizationChart": "Picture organization chart", "Common.define.smartArt.textPictureStrips": "Picture strips", "Common.define.smartArt.textPieProcess": "Pie process", "Common.define.smartArt.textPlusAndMinus": "Plus and minus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "Process arrows", "Common.define.smartArt.textProcessList": "Process list", "Common.define.smartArt.textPyramid": "Pyramid", "Common.define.smartArt.textPyramidList": "Pyramid list", "Common.define.smartArt.textRadialCluster": "Radial cluster", "Common.define.smartArt.textRadialCycle": "Radial cycle", "Common.define.smartArt.textRadialList": "Radial list", "Common.define.smartArt.textRadialPictureList": "Radial picture list", "Common.define.smartArt.textRadialVenn": "Radial Venn", "Common.define.smartArt.textRandomToResultProcess": "Random to result process", "Common.define.smartArt.textRelationship": "Relationship", "Common.define.smartArt.textRepeatingBendingProcess": "Repeating bending process", "Common.define.smartArt.textReverseList": "Reverse list", "Common.define.smartArt.textSegmentedCycle": "Segmented cycle", "Common.define.smartArt.textSegmentedProcess": "Segmented process", "Common.define.smartArt.textSegmentedPyramid": "Segmented pyramid", "Common.define.smartArt.textSnapshotPictureList": "Snapshot picture list", "Common.define.smartArt.textSpiralPicture": "Spiral picture", "Common.define.smartArt.textSquareAccentList": "Square accent list", "Common.define.smartArt.textStackedList": "Stacked list", "Common.define.smartArt.textStackedVenn": "Stacked <PERSON>n", "Common.define.smartArt.textStaggeredProcess": "Staggered process", "Common.define.smartArt.textStepDownProcess": "Step down process", "Common.define.smartArt.textStepUpProcess": "Step up process", "Common.define.smartArt.textSubStepProcess": "Sub-step process", "Common.define.smartArt.textTabbedArc": "Tabbed arc", "Common.define.smartArt.textTableHierarchy": "Table hierarchy", "Common.define.smartArt.textTableList": "Table list", "Common.define.smartArt.textTabList": "Tab List", "Common.define.smartArt.textTargetList": "Target list", "Common.define.smartArt.textTextCycle": "Text cycle", "Common.define.smartArt.textThemePictureAccent": "Theme picture accent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Theme picture alternating accent", "Common.define.smartArt.textThemePictureGrid": "Theme picture grid", "Common.define.smartArt.textTitledMatrix": "Titled matrix", "Common.define.smartArt.textTitledPictureAccentList": "Titled picture accent list", "Common.define.smartArt.textTitledPictureBlocks": "Titled picture blocks", "Common.define.smartArt.textTitlePictureLineup": "Title picture lineup", "Common.define.smartArt.textTrapezoidList": "Trapezoid list", "Common.define.smartArt.textUpwardArrow": "Upward arrow", "Common.define.smartArt.textVaryingWidthList": "Varying width list", "Common.define.smartArt.textVerticalAccentList": "Vertical accent list", "Common.define.smartArt.textVerticalArrowList": "Vertical arrow list", "Common.define.smartArt.textVerticalBendingProcess": "Vertical bending process", "Common.define.smartArt.textVerticalBlockList": "Vertical block list", "Common.define.smartArt.textVerticalBoxList": "Vertical box list", "Common.define.smartArt.textVerticalBracketList": "Vertical bracket list", "Common.define.smartArt.textVerticalBulletList": "Vertical bullet list", "Common.define.smartArt.textVerticalChevronList": "Vertical chevron list", "Common.define.smartArt.textVerticalCircleList": "Vertical circle list", "Common.define.smartArt.textVerticalCurvedList": "Vertical curved list", "Common.define.smartArt.textVerticalEquation": "Vertical equation", "Common.define.smartArt.textVerticalPictureAccentList": "Vertical picture accent list", "Common.define.smartArt.textVerticalPictureList": "Vertical picture list", "Common.define.smartArt.textVerticalProcess": "Vertical process", "Common.Translation.textMoreButton": "Več", "Common.Translation.tipFileLocked": "Document is locked for editing. You can make changes and save it as local copy later.", "Common.Translation.tipFileReadOnly": "The file is read-only. To keep your changes, save the file with a new name or in a different location.", "Common.Translation.warnFileLocked": "The file is being edited in another app. You can continue editing and save it as a copy.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON><PERSON><PERSON> kop<PERSON>jo", "Common.Translation.warnFileLockedBtnView": "Open for viewing", "Common.UI.ButtonColored.textAutoColor": "Automatic", "Common.UI.ButtonColored.textEyedropper": "Eyedropper", "Common.UI.ButtonColored.textNewColor": "Dodaj novo barvo po meri", "Common.UI.ComboBorderSize.txtNoBorders": "Ni mej", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Ni mej", "Common.UI.ComboDataView.emptyComboText": "<PERSON>", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "trenuten", "Common.UI.ExtendedColorDialog.textHexErr": "Vnesena vrednost je nepravilna.<br>Prosim vnesite vrednost med 000000 in FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Novo", "Common.UI.ExtendedColorDialog.textRGBErr": "Vnesena vrednost je nepravilna.<br>Prosim vnesite numerično vrednost med 0 in 255.", "Common.UI.HSBColorPicker.textNoColor": "Ni barve", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Hide password", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Show password", "Common.UI.SearchBar.textFind": "Find", "Common.UI.SearchBar.tipCloseSearch": "Close find", "Common.UI.SearchBar.tipNextResult": "Next result", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Open advanced settings", "Common.UI.SearchBar.tipPreviousResult": "Previous result", "Common.UI.SearchDialog.textHighlight": "Označi rezultate", "Common.UI.SearchDialog.textMatchCase": "Občutljiv na velike in male črke", "Common.UI.SearchDialog.textReplaceDef": "Vnesi nadomestno besedilo", "Common.UI.SearchDialog.textSearchStart": "<PERSON><PERSON><PERSON> besedilo vnesite tu", "Common.UI.SearchDialog.textTitle": "Najdi in zamenjaj", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "Le cele besede", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "Zamenjaj", "Common.UI.SearchDialog.txtBtnReplaceAll": "Zamenjaj vse", "Common.UI.SynchronizeTip.textDontShow": "Tega sporočila ne p<PERSON>ži več", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "Dokument je spremenil drug uporabnik.<br>Prosim pritisnite za shranjevanje svojih sprememb in osvežitev posodobitev.", "Common.UI.ThemeColorPalette.textRecentColors": "Recent colors", "Common.UI.ThemeColorPalette.textStandartColors": "Standard colors", "Common.UI.ThemeColorPalette.textThemeColors": "Theme colors", "Common.UI.Themes.txtThemeClassicLight": "Classic Light", "Common.UI.Themes.txtThemeContrastDark": "Contrast Dark", "Common.UI.Themes.txtThemeDark": "Temen", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "Light", "Common.UI.Themes.txtThemeSystem": "Same as system", "Common.UI.Window.cancelButtonText": "Prekliči", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Ne", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.textDontShow": "Tega sporočila ne p<PERSON>ži več", "Common.UI.Window.textError": "Napaka", "Common.UI.Window.textInformation": "Informacija", "Common.UI.Window.textWarning": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.yesButtonText": "Da", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Accent", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "Background", "Common.Utils.ThemeColor.txtBlack": "Black", "Common.Utils.ThemeColor.txtBlue": "Blue", "Common.Utils.ThemeColor.txtBrightGreen": "Bright green", "Common.Utils.ThemeColor.txtBrown": "<PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Dark blue", "Common.Utils.ThemeColor.txtDarker": "Darker", "Common.Utils.ThemeColor.txtDarkGray": "Dark gray", "Common.Utils.ThemeColor.txtDarkGreen": "Dark green", "Common.Utils.ThemeColor.txtDarkPurple": "Dark purple", "Common.Utils.ThemeColor.txtDarkRed": "Dark red", "Common.Utils.ThemeColor.txtDarkTeal": "Dark teal", "Common.Utils.ThemeColor.txtDarkYellow": "Dark yellow", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "<PERSON>", "Common.Utils.ThemeColor.txtGreen": "Green", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "Light blue", "Common.Utils.ThemeColor.txtLighter": "Lighter", "Common.Utils.ThemeColor.txtLightGray": "Light gray", "Common.Utils.ThemeColor.txtLightGreen": "Light green", "Common.Utils.ThemeColor.txtLightOrange": "Light orange", "Common.Utils.ThemeColor.txtLightYellow": "Light yellow", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "Pink", "Common.Utils.ThemeColor.txtPurple": "Purple", "Common.Utils.ThemeColor.txtRed": "Red", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Sky blue", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Text", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "White", "Common.Utils.ThemeColor.txtYellow": "Yellow", "Common.Views.About.txtAddress": "naslov:", "Common.Views.About.txtLicensee": "LICENCE", "Common.Views.About.txtLicensor": "DAJALEC LICENCE", "Common.Views.About.txtMail": "e-naslov:", "Common.Views.About.txtPoweredBy": "Poganja", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Različica", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Apply as you work", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Samodejno popravljanje", "Common.Views.AutoCorrectDialog.textAutoFormat": "AutoFormat as you type", "Common.Views.AutoCorrectDialog.textBy": "Od", "Common.Views.AutoCorrectDialog.textDelete": "Izbriši", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet and network paths with hyperlinks", "Common.Views.AutoCorrectDialog.textMathCorrect": "Math AutoCorrect", "Common.Views.AutoCorrectDialog.textNewRowCol": "Include new rows and columns in table", "Common.Views.AutoCorrectDialog.textRecognized": "Recognized functions", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "The following expressions are recognized math expressions. They will not be automatically italicized.", "Common.Views.AutoCorrectDialog.textReplace": "Replace", "Common.Views.AutoCorrectDialog.textReplaceText": "Replace as you type", "Common.Views.AutoCorrectDialog.textReplaceType": "Replace text as you type", "Common.Views.AutoCorrectDialog.textReset": "Reset", "Common.Views.AutoCorrectDialog.textResetAll": "Reset to default", "Common.Views.AutoCorrectDialog.textRestore": "Rest<PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Samodejno popravljanje", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Recognized functions must contain only the letters A through Z, uppercase or lowercase.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Any expression you added will be removed and the removed ones will be restored. Do you want to continue?", "Common.Views.AutoCorrectDialog.warnReplace": "The autocorrect entry for %1 already exists. Do you want to replace it?", "Common.Views.AutoCorrectDialog.warnReset": "Any autocorrect you added will be removed and the changed ones will be restored to their original values. Do you want to continue?", "Common.Views.AutoCorrectDialog.warnRestore": "The autocorrect entry for %1 will be reset to its original value. Do you want to continue?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "Pošlji", "Common.Views.Comments.mniAuthorAsc": "Author A to Z", "Common.Views.Comments.mniAuthorDesc": "Author Z to A", "Common.Views.Comments.mniDateAsc": "Oldest", "Common.Views.Comments.mniDateDesc": "Newest", "Common.Views.Comments.mniFilterGroups": "Filter by Group", "Common.Views.Comments.mniPositionAsc": "From top", "Common.Views.Comments.mniPositionDesc": "From bottom", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "K dokumentu dodaj komentar", "Common.Views.Comments.textAddReply": "Dodaj odgovor", "Common.Views.Comments.textAll": "All", "Common.Views.Comments.textAnonym": "Gost", "Common.Views.Comments.textCancel": "Prekliči", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "Close comments", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textEnterCommentHint": "Svoj komentar vnesite tu", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON> k<PERSON>", "Common.Views.Comments.textOpenAgain": "Ponovno odpri", "Common.Views.Comments.textReply": "Odgovori", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "Sort comments", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "You have no permission to reopen the comment", "Common.Views.Comments.txtEmpty": "There are no comments in the sheet.", "Common.Views.CopyWarningDialog.textDontShow": "Tega sporočila ne p<PERSON>ži več", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON> kop<PERSON>j, <PERSON><PERSON><PERSON><PERSON><PERSON> in prilepi izvedena z uporabo gumbov urejevalne orodne vrstice in kontekstnega menija bodo izvedena le v urejevalnem zavihku.<br><br>Za kopiranje in lepljenje v in iz aplikacij zunaj urejevalnega zavihka uporabite sledeče kombinacije na tipkovnici:", "Common.Views.CopyWarningDialog.textTitle": "Kopiraj, Izreži in Prilepi dejanja", "Common.Views.CopyWarningDialog.textToCopy": "za <PERSON><PERSON><PERSON>", "Common.Views.CopyWarningDialog.textToCut": "za Izrez", "Common.Views.CopyWarningDialog.textToPaste": "za Lepljenje", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "Nalaganje...", "Common.Views.DocumentAccessDialog.textTitle": "Nastavitve deljenja", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "Eraser", "Common.Views.Draw.hintSelect": "Select", "Common.Views.Draw.txtEraser": "Eraser", "Common.Views.Draw.txtHighlighter": "Highlighter", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Pen", "Common.Views.Draw.txtSelect": "Select", "Common.Views.Draw.txtSize": "Size", "Common.Views.EditNameDialog.textLabel": "Oznaka:", "Common.Views.EditNameDialog.textLabelError": "Oznaka ne more biti prazna", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "Users who are editing the file:", "Common.Views.Header.textAddFavorite": "<PERSON> as favorite", "Common.Views.Header.textAdvSettings": "Napredne nastavitve", "Common.Views.Header.textBack": "Pojdi v dokumente", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "<PERSON><PERSON>", "Common.Views.Header.textHideLines": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideStatusBar": "Combine sheet and status bars", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "Read only", "Common.Views.Header.textRemoveFavorite": "Remove from Favorites", "Common.Views.Header.textSaveBegin": "Saving...", "Common.Views.Header.textSaveChanged": "Modified", "Common.Views.Header.textSaveEnd": "Vse spremembe shranjene", "Common.Views.Header.textSaveExpander": "Vse spremembe shranjene", "Common.Views.Header.textShare": "Share", "Common.Views.Header.textZoom": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipAccessRights": "Manage document access rights", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipGoEdit": "Uredi trenutno da<PERSON>ko", "Common.Views.Header.tipPrint": "Print file", "Common.Views.Header.tipPrintQuick": "Quick print", "Common.Views.Header.tipRedo": "Redo", "Common.Views.Header.tipSave": "Save", "Common.Views.Header.tipSearch": "Find", "Common.Views.Header.tipUndo": "Undo", "Common.Views.Header.tipUndock": "Undock into separate window", "Common.Views.Header.tipUsers": "View users", "Common.Views.Header.tipViewSettings": "View settings", "Common.Views.Header.tipViewUsers": "View users and manage document access rights", "Common.Views.Header.txtAccessRights": "Spremeni pravice dostopa", "Common.Views.Header.txtRename": "<PERSON><PERSON>", "Common.Views.History.textCloseHistory": "Close history", "Common.Views.History.textHideAll": "Hide detailed changes", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "Rest<PERSON>", "Common.Views.History.textShowAll": "Show detailed changes", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "Prilepi URL slike:", "Common.Views.ImageFromUrlDialog.txtEmpty": "To polje je obvezno", "Common.Views.ImageFromUrlDialog.txtNotUrl": "To polje mora biti URL v \"http://www.example.com\" formatu", "Common.Views.ListSettingsDialog.textBulleted": "Bulleted", "Common.Views.ListSettingsDialog.textFromFile": "From file", "Common.Views.ListSettingsDialog.textFromStorage": "From storage", "Common.Views.ListSettingsDialog.textFromUrl": "From URL", "Common.Views.ListSettingsDialog.textNumbering": "Numbered", "Common.Views.ListSettingsDialog.textSelect": "Select from", "Common.Views.ListSettingsDialog.tipChange": "Change bullet", "Common.Views.ListSettingsDialog.txtBullet": "Bullet", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "Image", "Common.Views.ListSettingsDialog.txtImport": "Import", "Common.Views.ListSettingsDialog.txtNewBullet": "New bullet", "Common.Views.ListSettingsDialog.txtNewImage": "New image", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtOfText": "% od besedila", "Common.Views.ListSettingsDialog.txtSize": "Size", "Common.Views.ListSettingsDialog.txtStart": "Start at", "Common.Views.ListSettingsDialog.txtSymbol": "Symbol", "Common.Views.ListSettingsDialog.txtTitle": "List settings", "Common.Views.ListSettingsDialog.txtType": "Type", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.textInvalidRange": "Invalid cells range", "Common.Views.OpenDialog.textSelectData": "Select data", "Common.Views.OpenDialog.txtAdvanced": "Napredne nastavitve", "Common.Views.OpenDialog.txtColon": "Dvopičje", "Common.Views.OpenDialog.txtComma": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtDelimiter": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtDestData": "Choose where to put the data", "Common.Views.OpenDialog.txtEmpty": "This field is required", "Common.Views.OpenDialog.txtEncoding": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtIncorrectPwd": "Geslo je <PERSON>", "Common.Views.OpenDialog.txtOpenFile": "Vnesite geslo za odpiranje datoteke", "Common.Views.OpenDialog.txtOther": "Other", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "Preview", "Common.Views.OpenDialog.txtProtected": "Once you enter the password and open the file, the current password to the file will be reset.", "Common.Views.OpenDialog.txtSemicolon": "Semicolon", "Common.Views.OpenDialog.txtSpace": "Razmik", "Common.Views.OpenDialog.txtTab": "Zavihek", "Common.Views.OpenDialog.txtTitle": "Izberi %1 možnosti", "Common.Views.OpenDialog.txtTitleProtected": "Protected file", "Common.Views.PasswordDialog.txtDescription": "Set a password to protect this document", "Common.Views.PasswordDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON><PERSON> gesla se ne ujema", "Common.Views.PasswordDialog.txtPassword": "<PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "Repeat password", "Common.Views.PasswordDialog.txtTitle": "Set password", "Common.Views.PasswordDialog.txtWarning": "Pozor: <PERSON>e izgubite geslo ali ga pozabite, ga ne morete več obnoviti. Hranite ga na varnem mestu.", "Common.Views.PluginDlg.textLoading": "Loading", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "Razširitve", "Common.Views.Plugins.strPlugins": "Razširitve", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "Start", "Common.Views.Plugins.textStop": "Stop", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "Encrypt with password", "Common.Views.Protection.hintDelPwd": "Delete password", "Common.Views.Protection.hintPwd": "Spremeni ali odstrani geslo", "Common.Views.Protection.hintSignature": "Do<PERSON><PERSON> pod<PERSON> ali podpisno črto", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "Spremeni geslo", "Common.Views.Protection.txtDeletePwd": "Odstrani geslo", "Common.Views.Protection.txtEncrypt": "Encrypt", "Common.Views.Protection.txtInvisibleSignature": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtSignature": "Signature", "Common.Views.Protection.txtSignatureLine": "<PERSON><PERSON>j <PERSON>pisno črto", "Common.Views.RecentFiles.txtOpenRecent": "Open Recent", "Common.Views.RenameDialog.textName": "<PERSON><PERSON> da<PERSON>", "Common.Views.RenameDialog.txtInvalidName": "The file name cannot contain any of the following characters: ", "Common.Views.ReviewChanges.hintNext": "To next change", "Common.Views.ReviewChanges.hintPrev": "To previous change", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Real-time co-editing. All changes are saved automatically.", "Common.Views.ReviewChanges.strStrict": "Strict", "Common.Views.ReviewChanges.strStrictDesc": "Use the 'Save' button to sync the changes you and others make.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Accept current change", "Common.Views.ReviewChanges.tipCoAuthMode": "Set co-editing mode", "Common.Views.ReviewChanges.tipCommentRem": "Delete comments", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Delete current comments", "Common.Views.ReviewChanges.tipCommentResolve": "Resolve comments", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolve current comments", "Common.Views.ReviewChanges.tipHistory": "Show version history", "Common.Views.ReviewChanges.tipRejectCurrent": "Reject current change", "Common.Views.ReviewChanges.tipReview": "Track changes", "Common.Views.ReviewChanges.tipReviewView": "Select the mode you want the changes to be displayed", "Common.Views.ReviewChanges.tipSetDocLang": "Set document language", "Common.Views.ReviewChanges.tipSetSpelling": "Spell checking", "Common.Views.ReviewChanges.tipSharing": "Manage document access rights", "Common.Views.ReviewChanges.txtAccept": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptAll": "Sprej<PERSON> vse spremembe", "Common.Views.ReviewChanges.txtAcceptChanges": "<PERSON><PERSON><PERSON><PERSON><PERSON> sprem<PERSON>", "Common.Views.ReviewChanges.txtAcceptCurrent": "Accept current change", "Common.Views.ReviewChanges.txtChat": "Pogovor", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Co-editing Mode", "Common.Views.ReviewChanges.txtCommentRemAll": "Delete all comments", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Delete current comments", "Common.Views.ReviewChanges.txtCommentRemMy": "Delete my comments", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Delete my current comments", "Common.Views.ReviewChanges.txtCommentRemove": "Odstrani", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolve all comments", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolve current comments", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolve my comments", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolve My Current Comments", "Common.Views.ReviewChanges.txtDocLang": "Language", "Common.Views.ReviewChanges.txtFinal": "Vse spremembe so sprejete (Predogled)", "Common.Views.ReviewChanges.txtFinalCap": "Končno", "Common.Views.ReviewChanges.txtHistory": "Version History", "Common.Views.ReviewChanges.txtMarkup": "All changes (Editing)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "Next", "Common.Views.ReviewChanges.txtOriginal": "Vse spremembe zavrnjene (Predogled)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Previous", "Common.Views.ReviewChanges.txtReject": "Reject", "Common.Views.ReviewChanges.txtRejectAll": "Reject All Changes", "Common.Views.ReviewChanges.txtRejectChanges": "Reject changes", "Common.Views.ReviewChanges.txtRejectCurrent": "Reject Current Change", "Common.Views.ReviewChanges.txtSharing": "Sharing", "Common.Views.ReviewChanges.txtSpelling": "Spell checking", "Common.Views.ReviewChanges.txtTurnon": "Track Changes", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "Dodaj odgovor", "Common.Views.ReviewPopover.textCancel": "Prekliči", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Enter your comment here", "Common.Views.ReviewPopover.textMention": "+omemba bo dodelila uporabniku dostop do datoteke in poslano bo e-poštno sporočilo", "Common.Views.ReviewPopover.textMentionNotify": "+omemba bo obvestila uporabnika preko e-pošte", "Common.Views.ReviewPopover.textOpenAgain": "Open again", "Common.Views.ReviewPopover.textReply": "Reply", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "You have no permission to reopen the comment", "Common.Views.ReviewPopover.txtDeleteTip": "Delete", "Common.Views.ReviewPopover.txtEditTip": "Edit", "Common.Views.SaveAsDlg.textLoading": "Loading", "Common.Views.SaveAsDlg.textTitle": "Folder for save", "Common.Views.SearchPanel.textByColumns": "By columns", "Common.Views.SearchPanel.textByRows": "By rows", "Common.Views.SearchPanel.textCaseSensitive": "Case sensitive", "Common.Views.SearchPanel.textCell": "Cell", "Common.Views.SearchPanel.textCloseSearch": "Close find", "Common.Views.SearchPanel.textContentChanged": "Document changed.", "Common.Views.SearchPanel.textFind": "Find", "Common.Views.SearchPanel.textFindAndReplace": "Find and replace", "Common.Views.SearchPanel.textFormula": "Formula", "Common.Views.SearchPanel.textFormulas": "Formulas", "Common.Views.SearchPanel.textItemEntireCell": "Entire cell contents", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} items successfully replaced.", "Common.Views.SearchPanel.textLookIn": "Look in", "Common.Views.SearchPanel.textMatchUsingRegExp": "Match using regular expressions", "Common.Views.SearchPanel.textName": "Name", "Common.Views.SearchPanel.textNoMatches": "No matches", "Common.Views.SearchPanel.textNoSearchResults": "No search results", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} items replaced. Remaining {2} items are locked by other users.", "Common.Views.SearchPanel.textReplace": "Replace", "Common.Views.SearchPanel.textReplaceAll": "Replace All", "Common.Views.SearchPanel.textReplaceWith": "Replace with", "Common.Views.SearchPanel.textSearch": "Search", "Common.Views.SearchPanel.textSearchAgain": "{0}Perform new search{1} for accurate results.", "Common.Views.SearchPanel.textSearchHasStopped": "Search has stopped", "Common.Views.SearchPanel.textSearchOptions": "Search options", "Common.Views.SearchPanel.textSearchResults": "Search results: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textSelectDataRange": "Select Data range", "Common.Views.SearchPanel.textSheet": "Sheet", "Common.Views.SearchPanel.textSpecificRange": "Specific range", "Common.Views.SearchPanel.textTooManyResults": "There are too many results to show here", "Common.Views.SearchPanel.textValue": "Value", "Common.Views.SearchPanel.textValues": "Values", "Common.Views.SearchPanel.textWholeWords": "Whole words only", "Common.Views.SearchPanel.textWithin": "Within", "Common.Views.SearchPanel.textWorkbook": "Workbook", "Common.Views.SearchPanel.tipNextResult": "Next result", "Common.Views.SearchPanel.tipPreviousResult": "Previous result", "Common.Views.SelectFileDlg.textLoading": "Loading", "Common.Views.SelectFileDlg.textTitle": "Select data source", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "Spremeni", "Common.Views.SignDialog.textInputName": "Input signer name", "Common.Views.SignDialog.textItalic": "Ležeče", "Common.Views.SignDialog.textNameError": "Signer name must not be empty.", "Common.Views.SignDialog.textPurpose": "Purpose for signing this document", "Common.Views.SignDialog.textSelect": "Select", "Common.Views.SignDialog.textSelectImage": "Select image", "Common.Views.SignDialog.textSignature": "Signature looks as", "Common.Views.SignDialog.textTitle": "Sign document", "Common.Views.SignDialog.textUseImage": "or click 'Select Image' to use a picture as signature", "Common.Views.SignDialog.textValid": "Valid from %1 to %2", "Common.Views.SignDialog.tipFontName": "<PERSON><PERSON> pisave", "Common.Views.SignDialog.tipFontSize": "Velikost pisave", "Common.Views.SignSettingsDialog.textAllowComment": "Allow signer to add comment in the signature dialog", "Common.Views.SignSettingsDialog.textDefInstruction": "Before signing this document, verify that the content you are signing is correct.", "Common.Views.SignSettingsDialog.textInfoEmail": "Elektronski naslov", "Common.Views.SignSettingsDialog.textInfoName": "Ime", "Common.Views.SignSettingsDialog.textInfoTitle": "Suggested signer's title", "Common.Views.SignSettingsDialog.textInstructions": "Instructions for signer", "Common.Views.SignSettingsDialog.textShowDate": "Show sign date in signature line", "Common.Views.SignSettingsDialog.textTitle": "Signature setup", "Common.Views.SignSettingsDialog.txtEmpty": "This field is required", "Common.Views.SymbolTableDialog.textCharacter": "Znak", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX value", "Common.Views.SymbolTableDialog.textCopyright": "Copyright sign", "Common.Views.SymbolTableDialog.textDCQuote": "Closing double quote", "Common.Views.SymbolTableDialog.textDOQuote": "Opening double quote", "Common.Views.SymbolTableDialog.textEllipsis": "Horizontal ellipsis", "Common.Views.SymbolTableDialog.textEmDash": "Em dash", "Common.Views.SymbolTableDialog.textEmSpace": "Em space", "Common.Views.SymbolTableDialog.textEnDash": "En dash", "Common.Views.SymbolTableDialog.textEnSpace": "En space", "Common.Views.SymbolTableDialog.textFont": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBHyphen": "Non-breaking hyphen", "Common.Views.SymbolTableDialog.textNBSpace": "No-break space", "Common.Views.SymbolTableDialog.textPilcrow": "Pilcrow sign", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 prostora", "Common.Views.SymbolTableDialog.textRange": "Range", "Common.Views.SymbolTableDialog.textRecent": "Recently used symbols", "Common.Views.SymbolTableDialog.textRegistered": "Registered sign", "Common.Views.SymbolTableDialog.textSCQuote": "Closing single quote", "Common.Views.SymbolTableDialog.textSection": "Section sign", "Common.Views.SymbolTableDialog.textShortcut": "Shortcut key", "Common.Views.SymbolTableDialog.textSHyphen": "Soft hyphen", "Common.Views.SymbolTableDialog.textSOQuote": "Opening single quote", "Common.Views.SymbolTableDialog.textSpecial": "Special characters", "Common.Views.SymbolTableDialog.textSymbols": "Symbols", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Trademark symbol", "Common.Views.UserNameDialog.textDontShow": "Don't ask me again", "Common.Views.UserNameDialog.textLabel": "Label:", "Common.Views.UserNameDialog.textLabelError": "Label must not be empty.", "SSE.Controllers.DataTab.strSheet": "Sheet", "SSE.Controllers.DataTab.textAddExternalData": "The link to an external source has been added. You can update such links in the Data tab.", "SSE.Controllers.DataTab.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textContinue": "Continue", "SSE.Controllers.DataTab.textDontUpdate": "Don't Update", "SSE.Controllers.DataTab.textEmptyUrl": "You need to specify URL.", "SSE.Controllers.DataTab.textRows": "Rows", "SSE.Controllers.DataTab.textTurnOff": "Turn off AutoUpdate", "SSE.Controllers.DataTab.textUpdate": "Update", "SSE.Controllers.DataTab.textWizard": "Text to Columns", "SSE.Controllers.DataTab.txtDataValidation": "Data Validation", "SSE.Controllers.DataTab.txtErrorExternalLink": "Error: updating is failed", "SSE.Controllers.DataTab.txtExpand": "Razširi", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "The data next to the selection will not be removed. Do you want to expand the selection to include the adjacent data or continue with the currently selected cells only?", "SSE.Controllers.DataTab.txtExtendDataValidation": "The selection contains some cells without Data Validation settings.<br>Do you want to extend Data Validation to these cells?", "SSE.Controllers.DataTab.txtImportWizard": "Text Import Wizard", "SSE.Controllers.DataTab.txtRemDuplicates": "Remove Duplicates", "SSE.Controllers.DataTab.txtRemoveDataValidation": "The selection contains more than one type of validation.<br>Erase current settings and continue?", "SSE.Controllers.DataTab.txtRemSelected": "Remove in selected", "SSE.Controllers.DataTab.txtUrlTitle": "Paste a data URL", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "This workbook contains links to external sources which update automatically. This might be unsafe.<br><br>If you trust them, press Continue.", "SSE.Controllers.DataTab.warnUpdateExternalData": "This workbook contains links to one or more external sources that could be unsafe.<br>If you trust the links, update them to get the latest data.", "SSE.Controllers.DocumentHolder.alignmentText": "Alignment", "SSE.Controllers.DocumentHolder.centerText": "Na sredino", "SSE.Controllers.DocumentHolder.deleteColumnText": "Izbriši stolpec", "SSE.Controllers.DocumentHolder.deleteRowText": "Izbriši vrsto", "SSE.Controllers.DocumentHolder.deleteText": "Izbriši", "SSE.Controllers.DocumentHolder.errorInvalidLink": "The link reference does not exist. Please correct the link or delete it.", "SSE.Controllers.DocumentHolder.guestText": "Gost", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Stolpec levo", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Stolpec desno", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Row Above", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Row Below", "SSE.Controllers.DocumentHolder.insertText": "Vstavi", "SSE.Controllers.DocumentHolder.leftText": "Levo", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Warning", "SSE.Controllers.DocumentHolder.rightText": "Right", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "AutoCorrect options", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON><PERSON><PERSON> s<PERSON> {0} simbo<PERSON> ({1} p<PERSON><PERSON><PERSON>)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "<PERSON><PERSON><PERSON><PERSON> vrste {0} to<PERSON><PERSON> ({1} pik<PERSON>lov)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Pritisnite CTRL in pritisnite povezavo", "SSE.Controllers.DocumentHolder.textInsertLeft": "Insert Left", "SSE.Controllers.DocumentHolder.textInsertTop": "Insert Top", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Paste special", "SSE.Controllers.DocumentHolder.textStopExpand": "Stop automatically expanding tables", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Ta element ureja drug uporabnik.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Nad p<PERSON>", "SSE.Controllers.DocumentHolder.txtAddBottom": "Add bottom border", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Add fraction bar", "SSE.Controllers.DocumentHolder.txtAddHor": "Add horizontal line", "SSE.Controllers.DocumentHolder.txtAddLB": "Add left bottom line", "SSE.Controllers.DocumentHolder.txtAddLeft": "Add left border", "SSE.Controllers.DocumentHolder.txtAddLT": "Add left top line", "SSE.Controllers.DocumentHolder.txtAddRight": "Add right border", "SSE.Controllers.DocumentHolder.txtAddTop": "Add top border", "SSE.Controllers.DocumentHolder.txtAddVer": "Add vertical line", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Align to character", "SSE.Controllers.DocumentHolder.txtAll": "(Vse)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Returns the entire contents of the table or specified table columns including column headers, data and total rows", "SSE.Controllers.DocumentHolder.txtAnd": "in", "SSE.Controllers.DocumentHolder.txtBegins": "Se začne z", "SSE.Controllers.DocumentHolder.txtBelowAve": "Below average", "SSE.Controllers.DocumentHolder.txtBlanks": "(Prazno)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Border properties", "SSE.Controllers.DocumentHolder.txtBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtByField": "%1 of %2", "SSE.Controllers.DocumentHolder.txtColumn": "Stolpec", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Column alignment", "SSE.Controllers.DocumentHolder.txtContains": "Vsebuje", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Link copied to the clipboard", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Returns the data cells of the table or specified table columns", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Decrease argument size", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Delete argument", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Delete manual break", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Delete enclosing characters", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Delete enclosing characters and separators", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Izbriši enačbo", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "<PERSON>z<PERSON>š<PERSON> diagram", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Delete radical", "SSE.Controllers.DocumentHolder.txtEnds": "Se konča s/z", "SSE.Controllers.DocumentHolder.txtEquals": "Enak<PERSON>", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Equal to cell color", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Equal to font color", "SSE.Controllers.DocumentHolder.txtExpand": "Expand and sort", "SSE.Controllers.DocumentHolder.txtExpandSort": "The data next to the selection will not be sorted. Do you want to expand the selection to include the adjacent data or continue with sorting the currently selected cells only?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtFilterTop": "Top", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Change to linear fraction", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Change to skewed fraction", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Change to stacked fraction", "SSE.Controllers.DocumentHolder.txtGreater": "Greater than", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Greater than or equal to", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Char over text", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Char under text", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Returns the column headers for the table or specified table columns", "SSE.Controllers.DocumentHolder.txtHeight": "Višina", "SSE.Controllers.DocumentHolder.txtHideBottom": "Hide bottom border", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Hide bottom limit", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Hide closing bracket", "SSE.Controllers.DocumentHolder.txtHideDegree": "Hide degree", "SSE.Controllers.DocumentHolder.txtHideHor": "Hide horizontal line", "SSE.Controllers.DocumentHolder.txtHideLB": "<PERSON><PERSON> left bottom line", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON><PERSON> left border", "SSE.Controllers.DocumentHolder.txtHideLT": "<PERSON><PERSON> left top line", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Hide opening bracket", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Hide placeholder", "SSE.Controllers.DocumentHolder.txtHideRight": "Hide right border", "SSE.Controllers.DocumentHolder.txtHideTop": "Hide top border", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Hide top limit", "SSE.Controllers.DocumentHolder.txtHideVer": "Hide vertical line", "SSE.Controllers.DocumentHolder.txtImportWizard": "Text Import Wizard", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Increase argument size", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Insert argument after", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Insert argument before", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Insert manual break", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Insert equation after", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Insert equation before", "SSE.Controllers.DocumentHolder.txtItems": "items", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Keep text only", "SSE.Controllers.DocumentHolder.txtLess": "Less than", "SSE.Controllers.DocumentHolder.txtLessEquals": "Less than or equal to", "SSE.Controllers.DocumentHolder.txtLimitChange": "Change limits location", "SSE.Controllers.DocumentHolder.txtLimitOver": "Limit over text", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Limit under text", "SSE.Controllers.DocumentHolder.txtLockSort": "Data is found next to your selection, but you do not have sufficient permissions to change those cells.<br>Do you wish to continue with the current selection?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Match brackets to argument height", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Matrix alignment", "SSE.Controllers.DocumentHolder.txtNoChoices": "There are no choices for filling the cell.<br>Only text values from the column can be selected for replacement.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Se ne začne z", "SSE.Controllers.DocumentHolder.txtNotContains": "Ne vsebuje", "SSE.Controllers.DocumentHolder.txtNotEnds": "Does not end with", "SSE.Controllers.DocumentHolder.txtNotEquals": "Does not equal", "SSE.Controllers.DocumentHolder.txtOr": "ali", "SSE.Controllers.DocumentHolder.txtOverbar": "Bar over text", "SSE.Controllers.DocumentHolder.txtPaste": "P<PERSON>ep<PERSON>", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formula without borders", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formula + column width", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Destination formatting", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Prilepi le oblikovanje", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formula + number format", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Prilepi le formulo", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formula + all formatting", "SSE.Controllers.DocumentHolder.txtPasteLink": "Prilepi povezavo", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Linked picture", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Merge conditional formatting", "SSE.Controllers.DocumentHolder.txtPastePicture": "Slika", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Source formatting", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Transpose", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Value + all formatting", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Value + number format", "SSE.Controllers.DocumentHolder.txtPasteValues": "Prilepi le vrednost", "SSE.Controllers.DocumentHolder.txtPercent": "percent", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Redo table autoexpansion", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Remove fraction bar", "SSE.Controllers.DocumentHolder.txtRemLimit": "Remove limit", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Remove accent character", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Remove bar", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Do you want to remove this signature?<br>It can't be undone.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Remove scripts", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Remove subscript", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Remove superscript", "SSE.Controllers.DocumentHolder.txtRowHeight": "V<PERSON>š<PERSON> vrste", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Scripts after text", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Scripts before text", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Show bottom limit", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Show closing bracket", "SSE.Controllers.DocumentHolder.txtShowDegree": "Show degree", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Show opening bracket", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Show placeholder", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Show top limit", "SSE.Controllers.DocumentHolder.txtSorting": "Sorting", "SSE.Controllers.DocumentHolder.txtSortSelected": "Sort selected", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Stretch brackets", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Choose only this row of the specified column", "SSE.Controllers.DocumentHolder.txtTop": "Top", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Returns the total rows for the table or specified table columns", "SSE.Controllers.DocumentHolder.txtUnderbar": "Bar under text", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Undo table autoexpansion", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Use text import wizard", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Clicking this link can be harmful to your device and data.<br>Are you sure you want to continue?", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.warnFilterError": "You need at least one field in the Values area in order to apply a value filter.", "SSE.Controllers.FormulaDialog.sCategoryAll": "Vse", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Custom", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Database", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Datum in čas", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Inžinirstvo", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Finance", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Informacije", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 last used", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logical", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Lookup & Reference", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Math & Trig", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistical", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Besedilo in podatki", "SSE.Controllers.LeftMenu.newDocumentTitle": "Neimenovana razpredelnica", "SSE.Controllers.LeftMenu.textByColumns": "<PERSON> stol<PERSON>h", "SSE.Controllers.LeftMenu.textByRows": "Po vrsticah", "SSE.Controllers.LeftMenu.textFormulas": "Formule", "SSE.Controllers.LeftMenu.textItemEntireCell": "<PERSON><PERSON><PERSON> vs<PERSON> celic", "SSE.Controllers.LeftMenu.textLoadHistory": "Loading version history...", "SSE.Controllers.LeftMenu.textLookin": "Poglej v", "SSE.Controllers.LeftMenu.textNoTextFound": "<PERSON><PERSON><PERSON><PERSON>, ka<PERSON><PERSON>, ni bilo mogoče najti. Prosim nastavite svoje možnosti iskanja.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Nadomestek je bil izdelan. {0} dogodki so bili presko<PERSON>.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Iskan<PERSON> je bilo storjeno. Dogodki nadomeščeni: {0}", "SSE.Controllers.LeftMenu.textSave": "Save", "SSE.Controllers.LeftMenu.textSearch": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "SSE.Controllers.LeftMenu.textSheet": "Stran", "SSE.Controllers.LeftMenu.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWarning": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWithin": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWorkbook": "Delovni zvezek", "SSE.Controllers.LeftMenu.txtUntitled": "Untitled", "SSE.Controllers.LeftMenu.warnDownloadAs": "Če boste nadaljevali s shranjevanje v tem dormatu bodo vse funkcije razen besedila izgubljene.<br><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, da želite nadaljevati?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "The CSV format does not support saving a multi-sheet file.<br>To keep the selected format and save only the current sheet, press Save.<br>To save the current spreadsheet, click Cancel and save it in a different format.", "SSE.Controllers.Main.confirmAddCellWatches": "This action will add {0} cell watches.<br>Do you want to continue?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "This action will add only {0} cell watches by memory save reason.<br>Do you want to continue?", "SSE.Controllers.Main.confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "SSE.Controllers.Main.confirmMoveCellRange": "Območje ciljne celice lahko vsebuje podatke. Nadaljujem operacijo?", "SSE.Controllers.Main.confirmPutMergeRange": "The source data contained merged cells.<br>They had been unmerged before they were pasted into the table.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Formulas in the header row will be removed and converted to static text.<br>Do you want to continue?", "SSE.Controllers.Main.confirmReplaceHFPicture": "Only one picture can be inserted in each section of the header.<br>Press \"Replace\" to replace existing picture.<br>Press \"Keep\" to keep existing picture.", "SSE.Controllers.Main.convertationTimeoutText": "Pretvorbena prekinitev presežena.", "SSE.Controllers.Main.criticalErrorExtText": "Pritisnite \"OK\" za vrnitev na seznam dokumentov.", "SSE.Controllers.Main.criticalErrorTitle": "Napaka", "SSE.Controllers.Main.downloadErrorText": "Prenos ni uspel.", "SSE.Controllers.Main.downloadTextText": "Prenašanje razpredelnice...", "SSE.Controllers.Main.downloadTitleText": "Prenašanje razpredelnice", "SSE.Controllers.Main.errNoDuplicates": "No duplicate values found.", "SSE.Controllers.Main.errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.", "SSE.Controllers.Main.errorArgsRange": "Napaka v vneseni formuli.<br>Uporabljen je napačen razpon argumentov.", "SSE.Controllers.Main.errorAutoFilterChange": "Operacija ni dovoljena, saj posku<PERSON> premakniti celice v razpredelnici na vaš delovni list.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Dejanja ni bilo mogoče izvesti za izbrane celice, saj ne morete premakniti dela razpredelnice.<br>Izberite drugo obmo<PERSON> podatkov tako, da je premaknjena celotna razpredelnica in ponovno poskusite.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Dejanja ni bilo mogoče izbrati za izbrano območje celic.<br>Izberite uniformirano območje podatkov v ali izven razpredelnice in ponovno poskusite.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "The operation cannot be performed because the area contains filtered cells.<br>Please unhide the filtered elements and try again.", "SSE.Controllers.Main.errorBadImageUrl": "URL slike je nepravilen", "SSE.Controllers.Main.errorCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the spreadsheet.", "SSE.Controllers.Main.errorCannotUngroup": "Cannot ungroup. To start an outline, select the detail rows or columns and group them.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "You cannot use this command on a protected sheet. To use this command, unprotect the sheet.<br>You might be requested to enter a password.", "SSE.Controllers.Main.errorChangeArray": "You cannot change part of an array.", "SSE.Controllers.Main.errorChangeFilteredRange": "This will change a filtered range on your worksheet.<br>To complete this task, please remove AutoFilters.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "The cell or chart you are trying to change is on a protected sheet.<br>To make a change, unprotect the sheet. You might be requested to enter a password.", "SSE.Controllers.Main.errorCircularReference": "There are one or more circular references where a formula refers to its own cell either directly or indirectly.<br>Try removing or changing these references, or moving the formulas to different cells.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Povezava s strežnikom izgubljena. Dokument v tem trenutku ne more biti urejen.", "SSE.Controllers.Main.errorConnectToServer": "The document could not be saved. Please check connection settings or contact your administrator.<br>When you click the 'OK' button, you will be prompted to download the document.", "SSE.Controllers.Main.errorConvertXml": "The file has an unsupported format.<br>Only XML Spreadsheet 2003 format can be used.", "SSE.Controllers.Main.errorCopyMultiselectArea": "This command cannot be used with multiple selections.<br>Select a single range and try again.", "SSE.Controllers.Main.errorCountArg": "Napaka v vneseni formuli.<br>Uporabljena je nepravilna številka argumentov.", "SSE.Controllers.Main.errorCountArgExceed": "Napaka v vneseni formuli.<br>Število argumentov je preseženo.", "SSE.Controllers.Main.errorCreateDefName": "The existing named ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Controllers.Main.errorCreateRange": "The existing ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON><PERSON> napaka.<br>Napaka povezave baze podatkov. V primeru, da napaka ni od<PERSON>, prosim kontaktirajte ekipo za pomoč.", "SSE.Controllers.Main.errorDataEncrypted": "Encrypted changes have been received, they cannot be deciphered.", "SSE.Controllers.Main.errorDataRange": "Nepravilen obseg podatkov.", "SSE.Controllers.Main.errorDataValidate": "The value you entered is not valid.<br>A user has restricted values that can be entered into this cell.", "SSE.Controllers.Main.errorDefaultMessage": "Koda napake: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "You are trying to delete a column that contains a locked cell. Locked cells cannot be deleted while the worksheet is protected.<br>To delete a locked cell, unprotect the sheet. You might be requested to enter a password.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "You are trying to delete a row that contains a locked cell. Locked cells cannot be deleted while the worksheet is protected.<br>To delete a locked cell, unprotect the sheet. You might be requested to enter a password.", "SSE.Controllers.Main.errorDependentsNoFormulas": "The Trace Dependents command found no formulas that refer to the active cell.", "SSE.Controllers.Main.errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "SSE.Controllers.Main.errorEditingDownloadas": "An error occurred during the work with the document.<br>Use the 'Download as' option to save the file backup copy to a drive.", "SSE.Controllers.Main.errorEditingSaveas": "An error occurred during the work with the document.<br>Use the 'Save as...' option to save the file backup copy to a drive.", "SSE.Controllers.Main.errorEditView": "The existing sheet view cannot be edited and the new ones cannot be created at the moment as some of them are being edited.", "SSE.Controllers.Main.errorEmailClient": "No email client could be found.", "SSE.Controllers.Main.errorFilePassProtect": "Dokument je zaščiten z geslom in ga ni mogoče odpreti.", "SSE.Controllers.Main.errorFileRequest": "<PERSON><PERSON><PERSON> napaka.<br><PERSON><PERSON><PERSON> zahteve datoteke. Prosim kontaktirija<PERSON> pomoč, če napaka ni odpravljena.", "SSE.Controllers.Main.errorFileSizeExceed": "The file size exceeds the limitation set for your server.<br>Please contact your Document Server administrator for details.", "SSE.Controllers.Main.errorFileVKey": "<PERSON><PERSON><PERSON> napaka.<br>Nepravilen varnostni ključ. Prosim kontaktirajte pomoč, če napaka ni odpravljena.", "SSE.Controllers.Main.errorFillRange": "Ni bilo mogoče izponiti izbranega območja celiv.<br><PERSON><PERSON> združene celice morajo biti enake velikosti.", "SSE.Controllers.Main.errorForceSave": "An error occurred while saving the file. Please use the 'Download as' option to save the file to a drive or try again later.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Cannot enter a formula for an item or field name in a pivot table report.", "SSE.Controllers.Main.errorFormulaName": "Napaka v vneseni formuli.<br>Uporabljeno je nepravilno ime formule.", "SSE.Controllers.Main.errorFormulaParsing": "Notranja napaka pri razčlenjevanju formule.", "SSE.Controllers.Main.errorFrmlMaxLength": "The length of your formula exceeds the limit of 8192 characters.<br>Please edit it and try again.", "SSE.Controllers.Main.errorFrmlMaxReference": "You cannot enter this formula because it has too many values,<br>cell references, and/or names.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Text values in formulas are limited to 255 characters.<br>Use the CONCATENATE function or concatenation operator (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "The function refers to a sheet that does not exist.<br>Please check the data and try again.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Operation could not be completed for the selected cell range.<br>Select a range so that the first table row was on the same row<br>and the resulting table overlapped the current one.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Operation could not be completed for the selected cell range.<br>Select a range which does not include other tables.", "SSE.Controllers.Main.errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "SSE.Controllers.Main.errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInvalidRef": "Enter a correct name for the selection or a valid reference to go to.", "SSE.Controllers.Main.errorKeyEncrypt": "Neznan ključni deskriptor", "SSE.Controllers.Main.errorKeyExpire": "Ključni deskriptor je potekel", "SSE.Controllers.Main.errorLabledColumnsPivot": "To create a pivot table, use data that is organized as a list with labeled columns.", "SSE.Controllers.Main.errorLoadingFont": "Fonts are not loaded.<br>Please contact your Document Server administrator.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "The reference for the location or data range is not valid.", "SSE.Controllers.Main.errorLockedAll": "The operation could not be done as the sheet has been locked by another user.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "One of the cells involved in the goal seek process has been modified by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "You cannot change data inside a pivot table.", "SSE.Controllers.Main.errorLockedWorksheetRename": "The sheet cannot be renamed at the moment as it is being renamed by another user", "SSE.Controllers.Main.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Controllers.Main.errorMoveRange": "Dela združene celice ni mogoče spremeniti", "SSE.Controllers.Main.errorMoveSlicerError": "Table slicers cannot be copied from one workbook to another.<br>Try again by selecting the entire table and the slicers.", "SSE.Controllers.Main.errorMultiCellFormula": "Multi-cell array formulas are not allowed in tables.", "SSE.Controllers.Main.errorNoDataToParse": "No data was selected to parse.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.Main.errorOpenWarning": "The length of one of the formulas in the file exceeded<br>the allowed number of characters and it was removed.", "SSE.Controllers.Main.errorOperandExpected": "Operand pričakovan", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "The password you supplied is not correct.<br>Verify that the CAPS LOCK key is off and be sure to use the correct capitalization.", "SSE.Controllers.Main.errorPasteInPivot": "We can't make this change for the selected cells because it will affect a pivot table.<br>Use the field list to change the report.", "SSE.Controllers.Main.errorPasteMaxRange": "The copy and paste area does not match.<br>Please select an area with the same size or click the first cell in a row to paste the copied cells.", "SSE.Controllers.Main.errorPasteMultiSelect": "This action cannot be done on a multiple range selection.<br>Select a single range and try again.", "SSE.Controllers.Main.errorPasteSlicerError": "Table slicers cannot be copied from one workbook to another.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Pivot table field name already exists.", "SSE.Controllers.Main.errorPivotGroup": "<PERSON><PERSON> group that selection.", "SSE.Controllers.Main.errorPivotOverlap": "A pivot table report cannot overlap a table.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "The Pivot Table report was saved without the underlying data.<br>Use the 'Refresh' button to update the report.", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "The Trace Precedents command requires that the active cell contain a formula which includes a valid references.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Unfortunately, it is not possible to print more than 1500 pages at once in the current program version.<br>This restriction will be removed in the upcoming releases.", "SSE.Controllers.Main.errorProcessSaveResult": "Shranjevanje ni bilo us<PERSON>šno", "SSE.Controllers.Main.errorProtectedRange": "This range is not allowed for editing.", "SSE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "SSE.Controllers.Main.errorServerVersion": "The editor version has been updated. The page will be reloaded to apply the changes.", "SSE.Controllers.Main.errorSessionAbsolute": "The document editing session has expired. Please reload the page.", "SSE.Controllers.Main.errorSessionIdle": "The document has not been edited for quite a long time. Please reload the page.", "SSE.Controllers.Main.errorSessionToken": "The connection to the server has been interrupted. Please reload the page.", "SSE.Controllers.Main.errorSetPassword": "Password could not be set.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Location reference is not valid because the cells are not all in the same column or row.<br>Select cells that are all in a single column or row.", "SSE.Controllers.Main.errorStockChart": "Nepravilen vrstni red vrstic. Za izgradnjo razpredelnice delnic podatke na strani navedite v sledečem vrstnem redu:<br>ot<PERSON>itvena cena, ma<PERSON><PERSON><PERSON>a cena, <PERSON>na cena, zak<PERSON><PERSON><PERSON><PERSON> cena.", "SSE.Controllers.Main.errorToken": "The document security token is not correctly formed.<br>Please contact your Document Server administrator.", "SSE.Controllers.Main.errorTokenExpire": "The document security token has expired.<br>Please contact your Document Server administrator.", "SSE.Controllers.Main.errorUnexpectedGuid": "<PERSON><PERSON><PERSON> nap<PERSON>.<br>Nepričakovan GUID. Če težava ni odstranjena prosim kontaktirajte pomoč.", "SSE.Controllers.Main.errorUpdateVersion": "Različica datoteke je bila spremenjena. Stran bo osvežena.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.", "SSE.Controllers.Main.errorUserDrop": "Do datoteke v tem trenutku ni možno dostopati.", "SSE.Controllers.Main.errorUsersExceed": "<PERSON><PERSON><PERSON><PERSON>, ki ga dovoljuje cenovni načrt, je bilo preseženo", "SSE.Controllers.Main.errorViewerDisconnect": "Connection is lost. You can still view the document,<br>but will not be able to download or print it until the connection is restored and page is reloaded.", "SSE.Controllers.Main.errorWrongBracketsCount": "napaka v vneseni formuli.<br>Uporabljeno je napačno število oklepajev.", "SSE.Controllers.Main.errorWrongOperator": "Napaka v vneseni formuli.<br>Uporabljen je napačen operator.", "SSE.Controllers.Main.errorWrongPassword": "The password you supplied is not correct.", "SSE.Controllers.Main.errRemDuplicates": "Duplicate values found and deleted: {0}, unique values left: {1}.", "SSE.Controllers.Main.leavePageText": "V razpredelnici imate neshranjene spremembe. Pritisnite 'ostani na tej strani' nato 'shrani' da jih shranite. Pritisnite 'zapusti to stran', da zavržete vse neshranjene spremembe.", "SSE.Controllers.Main.leavePageTextOnClose": "All unsaved changes in this spreadsheet will be lost.<br> Click \"Cancel\" then \"Save\" to save them. Click \"OK\" to discard all the unsaved changes.", "SSE.Controllers.Main.loadFontsTextText": "<PERSON><PERSON><PERSON><PERSON> pod<PERSON>kov...", "SSE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadFontTextText": "<PERSON><PERSON><PERSON><PERSON> pod<PERSON>kov...", "SSE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadImagesTextText": "Nalaganje slik...", "SSE.Controllers.Main.loadImagesTitleText": "Nalaganje slik", "SSE.Controllers.Main.loadImageTextText": "Nalaganje slike...", "SSE.Controllers.Main.loadImageTitleText": "Nalaganje Slike", "SSE.Controllers.Main.loadingDocumentTitleText": "Nalaganje razpredelnice", "SSE.Controllers.Main.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.openErrorText": "An error has occurred while opening the file.", "SSE.Controllers.Main.openTextText": "Odpiranje razpredelnice...", "SSE.Controllers.Main.openTitleText": "Odpiranje razpredelnice", "SSE.Controllers.Main.pastInMergeAreaError": "Dela združene celice ni mogoče spremeniti", "SSE.Controllers.Main.printTextText": "Tiskanje razpredelnice...", "SSE.Controllers.Main.printTitleText": "Tiskanje razpredelnice", "SSE.Controllers.Main.reloadButtonText": "Osveži stran", "SSE.Controllers.Main.requestEditFailedMessageText": "Nekdo v tem trenutku ureja ta dokument. Prosim ponovno poskusite kasneje.", "SSE.Controllers.Main.requestEditFailedTitleText": "Dostop zavrnjen", "SSE.Controllers.Main.saveErrorText": "An error has occurred while saving the file.", "SSE.Controllers.Main.saveErrorTextDesktop": "This file cannot be saved or created.<br>Possible reasons are: <br>1. The file is read-only. <br>2. The file is being edited by other users. <br>3. The disk is full or corrupted.", "SSE.Controllers.Main.saveTextText": "Shranjevanje razpredelnice...", "SSE.Controllers.Main.saveTitleText": "Shranjevanje razpredelnice", "SSE.Controllers.Main.scriptLoadError": "The connection is too slow, some of the components could not be loaded. Please reload the page.", "SSE.Controllers.Main.textAnonymous": "Anonimno", "SSE.Controllers.Main.textApplyAll": "Apply to all equations", "SSE.Controllers.Main.textBuyNow": "Visit website", "SSE.Controllers.Main.textChangesSaved": "All changes saved", "SSE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textCloseTip": "Pritisni za zapiranje namiga", "SSE.Controllers.Main.textConfirm": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "SSE.Controllers.Main.textContactUs": "Kontaktirajte oddelek za prodajo", "SSE.Controllers.Main.textContinue": "Continue", "SSE.Controllers.Main.textConvertEquation": "This equation was created with an old version of the equation editor which is no longer supported. To edit it, convert the equation to the Office Math ML format.<br>Convert now?", "SSE.Controllers.Main.textCustomLoader": "Please note that according to the terms of the license you are not entitled to change the loader.<br>Please contact our Sales Department to get a quote.", "SSE.Controllers.Main.textDisconnect": "Connection is lost", "SSE.Controllers.Main.textFillOtherRows": "Fill other rows", "SSE.Controllers.Main.textFormulaFilledAllRows": "Formula filled {0} rows have data. Filling other empty rows may take a few minutes.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Formula filled first {0} rows. Filling other empty rows may take a few minutes.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Formula filled only first {0} rows have data by memory save reason. There are other {1} rows have data in this sheet. You can fill them manually.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Formula filled only first {0} rows by memory save reason. Other rows in this sheet don't have data.", "SSE.Controllers.Main.textGuest": "Guest", "SSE.Controllers.Main.textHasMacros": "The file contains automatic macros.<br>Do you want to run macros?", "SSE.Controllers.Main.textKeep": "Keep", "SSE.Controllers.Main.textLearnMore": "Learn more", "SSE.Controllers.Main.textLoadingDocument": "Nalaganje razpredelnice", "SSE.Controllers.Main.textLongName": "Enter a name that is less than 128 characters.", "SSE.Controllers.Main.textNeedSynchronize": "You have updates", "SSE.Controllers.Main.textNo": "Ne", "SSE.Controllers.Main.textNoLicenseTitle": "License limit reached", "SSE.Controllers.Main.textPaidFeature": "Paid feature", "SSE.Controllers.Main.textPleaseWait": "Dejanje lahko traja dlje od pričakovanja. Prosimo počakajte...", "SSE.Controllers.Main.textReconnect": "Connection is restored", "SSE.Controllers.Main.textRemember": "Remember my choice for all files", "SSE.Controllers.Main.textRememberMacros": "Remember my choice for all macros", "SSE.Controllers.Main.textRenameError": "User name must not be empty.", "SSE.Controllers.Main.textRenameLabel": "Enter a name to be used for collaboration", "SSE.Controllers.Main.textReplace": "Replace", "SSE.Controllers.Main.textRequestMacros": "A macro makes a request to URL. Do you want to allow the request to the %1?", "SSE.Controllers.Main.textShape": "Oblika", "SSE.Controllers.Main.textStrict": "Strict mode", "SSE.Controllers.Main.textText": "Tekst", "SSE.Controllers.Main.textTryQuickPrint": "You have selected Quick print: the entire document will be printed on the last selected or default printer.<br>Do you want to continue?", "SSE.Controllers.Main.textTryUndoRedo": "The Undo/Redo functions are disabled for the Fast co-editing mode.<br>Click the 'Strict mode' button to switch to the Strict co-editing mode to edit the file without other users interference and send your changes only after you save them. You can switch between the co-editing modes using the editor Advanced settings.", "SSE.Controllers.Main.textTryUndoRedoWarn": "The Undo/Redo functions are disabled for the Fast co-editing mode.", "SSE.Controllers.Main.textUndo": "Undo", "SSE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "SSE.Controllers.Main.textUpdating": "Updating", "SSE.Controllers.Main.textYes": "Da", "SSE.Controllers.Main.titleLicenseExp": "License expired", "SSE.Controllers.Main.titleLicenseNotActive": "License not active", "SSE.Controllers.Main.titleServerVersion": "Urednik je bil posodobljen", "SSE.Controllers.Main.titleUpdateVersion": "Version changed", "SSE.Controllers.Main.txtAccent": "Preg<PERSON>", "SSE.Controllers.Main.txtAll": "(Vse)", "SSE.Controllers.Main.txtArt": "Your text here", "SSE.Controllers.Main.txtBasicShapes": "Osnovne oblike", "SSE.Controllers.Main.txtBlank": "(prazno)", "SSE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtByField": "%1 od %2", "SSE.Controllers.Main.txtCallouts": "Oblački", "SSE.Controllers.Main.txtCharts": "<PERSON><PERSON>", "SSE.Controllers.Main.txtClearFilter": "Clear filter", "SSE.Controllers.Main.txtColLbls": "Column labels", "SSE.Controllers.Main.txtColumn": "Stolpec", "SSE.Controllers.Main.txtConfidential": "Confidential", "SSE.Controllers.Main.txtDate": "Datum", "SSE.Controllers.Main.txtDays": "Dnevi", "SSE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON> diagrama", "SSE.Controllers.Main.txtEditingMode": "Nastavi način u<PERSON>...", "SSE.Controllers.Main.txtErrorLoadHistory": "History loading failed", "SSE.Controllers.Main.txtFiguredArrows": "Figurirane puščice", "SSE.Controllers.Main.txtFile": "Datoteka", "SSE.Controllers.Main.txtGrandTotal": "Grand Total", "SSE.Controllers.Main.txtGroup": "Group", "SSE.Controllers.Main.txtHours": "Hours", "SSE.Controllers.Main.txtInfo": "Info", "SSE.Controllers.Main.txtLines": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMath": "Matematika", "SSE.Controllers.Main.txtMinutes": "Minutes", "SSE.Controllers.Main.txtMonths": "Months", "SSE.Controllers.Main.txtMultiSelect": "Multi-Select", "SSE.Controllers.Main.txtNone": "None", "SSE.Controllers.Main.txtOr": "%1 ali %2", "SSE.Controllers.Main.txtPage": "Page", "SSE.Controllers.Main.txtPageOf": "Page %1 of %2", "SSE.Controllers.Main.txtPages": "Pages", "SSE.Controllers.Main.txtPicture": "Picture", "SSE.Controllers.Main.txtPivotTable": "PivotTable", "SSE.Controllers.Main.txtPreparedBy": "Prepared by", "SSE.Controllers.Main.txtPrintArea": "Print_Area", "SSE.Controllers.Main.txtQuarter": "Qtr", "SSE.Controllers.Main.txtQuarters": "Quarters", "SSE.Controllers.Main.txtRectangles": "Pravokotniki", "SSE.Controllers.Main.txtRow": "Vrsta", "SSE.Controllers.Main.txtRowLbls": "Row Labels", "SSE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "SSE.Controllers.Main.txtScheme_Aspect": "Aspect", "SSE.Controllers.Main.txtScheme_Blue": "Blue", "SSE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "SSE.Controllers.Main.txtScheme_Blue_II": "Blue II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "SSE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "SSE.Controllers.Main.txtScheme_Green": "Green", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "SSE.Controllers.Main.txtScheme_Marquee": "Marquee", "SSE.Controllers.Main.txtScheme_Median": "Median", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Orange", "SSE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "SSE.Controllers.Main.txtScheme_Paper": "Paper", "SSE.Controllers.Main.txtScheme_Red": "Red", "SSE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "SSE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "SSE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "SSE.Controllers.Main.txtScheme_Violet": "Violet", "SSE.Controllers.Main.txtScheme_Violet_II": "Violet II", "SSE.Controllers.Main.txtScheme_Yellow": "Yellow", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "SSE.Controllers.Main.txtSeconds": "Seconds", "SSE.Controllers.Main.txtSeries": "Serije", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Line Callout 1 (Border and Accent Bar)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Line Callout 2 (Border and Accent Bar)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Line Callout 3 (Border and Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout1": "Line Callout 1 (Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout2": "Line Callout 2 (Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout3": "Line Callout 3 (Accent Bar)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Back or previous button", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Beginning button", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Prazen gumb", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Document Button", "SSE.Controllers.Main.txtShape_actionButtonEnd": "End button", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Forward or next button", "SSE.Controllers.Main.txtShape_actionButtonHelp": "G<PERSON>b za pomoč", "SSE.Controllers.Main.txtShape_actionButtonHome": "Home button", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Information button", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Movie Button", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Return Button", "SSE.Controllers.Main.txtShape_actionButtonSound": "Sound Button", "SSE.Controllers.Main.txtShape_arc": "<PERSON>", "SSE.Controllers.Main.txtShape_bentArrow": "Bent arrow", "SSE.Controllers.Main.txtShape_bentConnector5": "Elbow connector", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Elbow arrow connector", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Elbow double-arrow connector", "SSE.Controllers.Main.txtShape_bentUpArrow": "Bent up arrow", "SSE.Controllers.Main.txtShape_bevel": "Stožčasti", "SSE.Controllers.Main.txtShape_blockArc": "Block arc", "SSE.Controllers.Main.txtShape_borderCallout1": "Line Callout 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Line Callout 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Line Callout 3", "SSE.Controllers.Main.txtShape_bracePair": "Double brace", "SSE.Controllers.Main.txtShape_callout1": "Line Callout 1 (No Border)", "SSE.Controllers.Main.txtShape_callout2": "Line Callout 2 (No Border)", "SSE.Controllers.Main.txtShape_callout3": "Line Callout 3 (No Border)", "SSE.Controllers.Main.txtShape_can": "Can", "SSE.Controllers.Main.txtShape_chevron": "Chevron", "SSE.Controllers.Main.txtShape_chord": "Chord", "SSE.Controllers.Main.txtShape_circularArrow": "Circular arrow", "SSE.Controllers.Main.txtShape_cloud": "Oblak", "SSE.Controllers.Main.txtShape_cloudCallout": "Cloud callout", "SSE.Controllers.Main.txtShape_corner": "Corner", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Curved connector", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Curved arrow connector", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Curved double-arrow connector", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Curved down arrow", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Curved left arrow", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Curved right arrow", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Curved up arrow", "SSE.Controllers.Main.txtShape_decagon": "Decagon", "SSE.Controllers.Main.txtShape_diagStripe": "Diagonal stripe", "SSE.Controllers.Main.txtShape_diamond": "Diamond", "SSE.Controllers.Main.txtShape_dodecagon": "Dodecagon", "SSE.Controllers.Main.txtShape_donut": "Donut", "SSE.Controllers.Main.txtShape_doubleWave": "Double Wave", "SSE.Controllers.Main.txtShape_downArrow": "Down Arrow", "SSE.Controllers.Main.txtShape_downArrowCallout": "Down Arrow Callout", "SSE.Controllers.Main.txtShape_ellipse": "Ellipse", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Curved down ribbon", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Curved up ribbon", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Flowchart: Alternate process", "SSE.Controllers.Main.txtShape_flowChartCollate": "Flowchart: Collate", "SSE.Controllers.Main.txtShape_flowChartConnector": "Flowchart: Connector", "SSE.Controllers.Main.txtShape_flowChartDecision": "Flowchart: Decision", "SSE.Controllers.Main.txtShape_flowChartDelay": "Flowchart: Delay", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Flowchart: Display", "SSE.Controllers.Main.txtShape_flowChartDocument": "Flowchart: Document", "SSE.Controllers.Main.txtShape_flowChartExtract": "Flowchart: Extract", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Flowchart: Data", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Flowchart: Internal storage", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Flowchart: Magnetic disk", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Flowchart: Direct access storage", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Flowchart: Sequential access storage", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Flowchart: Manual input", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Flowchart: Manual operation", "SSE.Controllers.Main.txtShape_flowChartMerge": "Flowchart: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Flowchart: Multidocument ", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Flowchart: Off-page Connector", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Flowchart: Stored data", "SSE.Controllers.Main.txtShape_flowChartOr": "Flowchart: Or", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Flowchart: Predefined Process", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Flowchart: Preparation", "SSE.Controllers.Main.txtShape_flowChartProcess": "Flowchart: Process", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Flowchart: Card", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Flowchart: Punched tape", "SSE.Controllers.Main.txtShape_flowChartSort": "Flowchart: Sort", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Flowchart: Summing junction", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Flowchart: Terminator", "SSE.Controllers.Main.txtShape_foldedCorner": "Folded Corner", "SSE.Controllers.Main.txtShape_frame": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_halfFrame": "Half frame", "SSE.Controllers.Main.txtShape_heart": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heptagon": "Heptagon", "SSE.Controllers.Main.txtShape_hexagon": "Hexagon", "SSE.Controllers.Main.txtShape_homePlate": "Pentagon", "SSE.Controllers.Main.txtShape_horizontalScroll": "Horizontal scroll", "SSE.Controllers.Main.txtShape_irregularSeal1": "Explosion 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Explosion 2", "SSE.Controllers.Main.txtShape_leftArrow": "Left Arrow", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Left arrow callout", "SSE.Controllers.Main.txtShape_leftBrace": "Left brace", "SSE.Controllers.Main.txtShape_leftBracket": "Left bracket", "SSE.Controllers.Main.txtShape_leftRightArrow": "Left right arrow", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Left right arrow callout", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Left right up arrow", "SSE.Controllers.Main.txtShape_leftUpArrow": "Left up arrow", "SSE.Controllers.Main.txtShape_lightningBolt": "Lightning bolt", "SSE.Controllers.Main.txtShape_line": "Line", "SSE.Controllers.Main.txtShape_lineWithArrow": "Puščica", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Double arrow", "SSE.Controllers.Main.txtShape_mathDivide": "Division", "SSE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMinus": "Minus", "SSE.Controllers.Main.txtShape_mathMultiply": "Multiply", "SSE.Controllers.Main.txtShape_mathNotEqual": "Not Equal", "SSE.Controllers.Main.txtShape_mathPlus": "Plus", "SSE.Controllers.Main.txtShape_moon": "Moon", "SSE.Controllers.Main.txtShape_noSmoking": "\"Ni\" simbol", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Notched right arrow", "SSE.Controllers.Main.txtShape_octagon": "Octagon", "SSE.Controllers.Main.txtShape_parallelogram": "Parallelogram", "SSE.Controllers.Main.txtShape_pentagon": "Pentagon", "SSE.Controllers.Main.txtShape_pie": "Tortni grafikon", "SSE.Controllers.Main.txtShape_plaque": "Sign", "SSE.Controllers.Main.txtShape_plus": "Plus", "SSE.Controllers.Main.txtShape_polyline1": "Scribble", "SSE.Controllers.Main.txtShape_polyline2": "Freeform", "SSE.Controllers.Main.txtShape_quadArrow": "Quad arrow", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Quad arrow callout", "SSE.Controllers.Main.txtShape_rect": "Rectangle", "SSE.Controllers.Main.txtShape_ribbon": "Down Ribbon", "SSE.Controllers.Main.txtShape_ribbon2": "Up ribbon", "SSE.Controllers.Main.txtShape_rightArrow": "Right Arrow", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Right arrow callout", "SSE.Controllers.Main.txtShape_rightBrace": "Right Brace", "SSE.Controllers.Main.txtShape_rightBracket": "Right Bracket", "SSE.Controllers.Main.txtShape_round1Rect": "Round Single Corner Rectangle", "SSE.Controllers.Main.txtShape_round2DiagRect": "Round Diagonal Corner Rectangle", "SSE.Controllers.Main.txtShape_round2SameRect": "Round Same Side Corner Rectangle", "SSE.Controllers.Main.txtShape_roundRect": "Round Corner Rectangle", "SSE.Controllers.Main.txtShape_rtTriangle": "Right Triangle", "SSE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_snip1Rect": "Snip single corner rectangle", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Snip diagonal corner rectangle", "SSE.Controllers.Main.txtShape_snip2SameRect": "Snip same side corner rectangle", "SSE.Controllers.Main.txtShape_snipRoundRect": "Snip and Round Single Corner Rectangle", "SSE.Controllers.Main.txtShape_spline": "Curve", "SSE.Controllers.Main.txtShape_star10": "10-kraka zvezda", "SSE.Controllers.Main.txtShape_star12": "12-<PERSON>rak<PERSON> zvezda", "SSE.Controllers.Main.txtShape_star16": "16-<PERSON>rak<PERSON> zvezda", "SSE.Controllers.Main.txtShape_star24": "24-krak<PERSON> zvezda", "SSE.Controllers.Main.txtShape_star32": "32-Point Star", "SSE.Controllers.Main.txtShape_star4": "4-kraka zvezda", "SSE.Controllers.Main.txtShape_star5": "5-<PERSON>rak<PERSON> zvezda", "SSE.Controllers.Main.txtShape_star6": "6-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star7": "7-<PERSON>rak<PERSON> zvezda", "SSE.Controllers.Main.txtShape_star8": "8-krak<PERSON> zvezda", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Striped Right Arrow", "SSE.Controllers.Main.txtShape_sun": "Sun", "SSE.Controllers.Main.txtShape_teardrop": "Teardrop", "SSE.Controllers.Main.txtShape_textRect": "Text Box", "SSE.Controllers.Main.txtShape_trapezoid": "Trapezoid", "SSE.Controllers.Main.txtShape_triangle": "Triangle", "SSE.Controllers.Main.txtShape_upArrow": "Up Arrow", "SSE.Controllers.Main.txtShape_upArrowCallout": "Up arrow callout", "SSE.Controllers.Main.txtShape_upDownArrow": "Up down arrow", "SSE.Controllers.Main.txtShape_uturnArrow": "U-Turn Arrow", "SSE.Controllers.Main.txtShape_verticalScroll": "Vertical scroll", "SSE.Controllers.Main.txtShape_wave": "Wave", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Oval callout", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Rectangular Callout", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Rounded Rectangular Callout", "SSE.Controllers.Main.txtSheet": "Sheet", "SSE.Controllers.Main.txtSlicer": "<PERSON>licer", "SSE.Controllers.Main.txtStarsRibbons": "Zvezde & Trakovi", "SSE.Controllers.Main.txtStyle_Bad": "Slabo", "SSE.Controllers.Main.txtStyle_Calculation": "Calculation", "SSE.Controllers.Main.txtStyle_Check_Cell": "<PERSON>veri celico", "SSE.Controllers.Main.txtStyle_Comma": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "Valuta", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Explanatory Text", "SSE.Controllers.Main.txtStyle_Good": "Good", "SSE.Controllers.Main.txtStyle_Heading_1": "Naslov 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Naslov 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Naslov 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Naslov 4", "SSE.Controllers.Main.txtStyle_Input": "Vnos", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Linked Cell", "SSE.Controllers.Main.txtStyle_Neutral": "Neutral", "SSE.Controllers.Main.txtStyle_Normal": "Normalno", "SSE.Controllers.Main.txtStyle_Note": "Note", "SSE.Controllers.Main.txtStyle_Output": "Output", "SSE.Controllers.Main.txtStyle_Percent": "Percent", "SSE.Controllers.Main.txtStyle_Title": "Title", "SSE.Controllers.Main.txtStyle_Total": "Total", "SSE.Controllers.Main.txtStyle_Warning_Text": "Warning Text", "SSE.Controllers.Main.txtTab": "Tab", "SSE.Controllers.Main.txtTable": "Table", "SSE.Controllers.Main.txtTime": "Time", "SSE.Controllers.Main.txtUnlock": "Unlock", "SSE.Controllers.Main.txtUnlockRange": "Unlock range", "SSE.Controllers.Main.txtUnlockRangeDescription": "Enter the password to change this range:", "SSE.Controllers.Main.txtUnlockRangeWarning": "A range you are trying to change is password protected.", "SSE.Controllers.Main.txtValues": "Values", "SSE.Controllers.Main.txtView": "View", "SSE.Controllers.Main.txtXAxis": "X os", "SSE.Controllers.Main.txtYAxis": "Y os", "SSE.Controllers.Main.txtYears": "Years", "SSE.Controllers.Main.unknownErrorText": "<PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Vaš brskalnik ni podprt.", "SSE.Controllers.Main.uploadDocExtMessage": "Unknown document format.", "SSE.Controllers.Main.uploadDocFileCountMessage": "No documents uploaded.", "SSE.Controllers.Main.uploadDocSizeMessage": "Maximum document size limit exceeded.", "SSE.Controllers.Main.uploadImageExtMessage": "Neznan format slike.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Ni naloženih slik.", "SSE.Controllers.Main.uploadImageSizeMessage": "Maksimalni limit velikosti slike je presežen.", "SSE.Controllers.Main.uploadImageTextText": "Nalaganje slike...", "SSE.Controllers.Main.uploadImageTitleText": "Nalaganje slike", "SSE.Controllers.Main.waitText": "Please, wait...", "SSE.Controllers.Main.warnBrowserIE9": "Aplikacija ima nizke zmogljivosti na IE9. Uporabite IE10 ali več", "SSE.Controllers.Main.warnBrowserZoom": "Nastavitve povečave vašega trenutnega brskalnika niso popolnoma podprte. Prosim ponastavite na privzeto povečanje s pritiskom na Ctrl+0.", "SSE.Controllers.Main.warnLicenseAnonymous": "Access denied for anonymous users.<br>This document will be opened for viewing only.", "SSE.Controllers.Main.warnLicenseBefore": "License not active.<br>Please contact your administrator.", "SSE.Controllers.Main.warnLicenseExceeded": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only.<br>Contact your administrator to learn more.", "SSE.Controllers.Main.warnLicenseExp": "Your license has expired.<br>Please update your license and refresh the page.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "License expired.<br>You have no access to document editing functionality.<br>Please contact your administrator.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "License needs to be renewed.<br>You have a limited access to document editing functionality.<br>Please contact your administrator to get full access", "SSE.Controllers.Main.warnLicenseUsersExceeded": "You've reached the user limit for %1 editors. Contact your administrator to learn more.", "SSE.Controllers.Main.warnNoLicense": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only.<br>Contact %1 sales team for personal upgrade terms.", "SSE.Controllers.Main.warnNoLicenseUsers": "You've reached the user limit for %1 editors. Contact %1 sales team for personal upgrade terms.", "SSE.Controllers.Main.warnProcessRightsChange": "<PERSON><PERSON><PERSON><PERSON>, da urejate datoteko je bila zavrnjena.", "SSE.Controllers.PivotTable.strSheet": "Sheet", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "No actions with calculated items are allowed for this active cell.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Calculated items do not work with custom subtotals.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "An item name cannot be found. Check that you've typed name correctly and the item is present in the PivotTable report.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Averages, standard deviations, and variances are not supported when a PivotTable report has calculated items.", "SSE.Controllers.Print.strAllSheets": "Vsi listi", "SSE.Controllers.Print.textFirstCol": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Controllers.Print.textFirstRow": "Prva vrstica", "SSE.Controllers.Print.textFrozenCols": "Frozen columns", "SSE.Controllers.Print.textFrozenRows": "Frozen rows", "SSE.Controllers.Print.textInvalidRange": "ERROR! Invalid cells range", "SSE.Controllers.Print.textNoRepeat": "Don't repeat", "SSE.Controllers.Print.textRepeat": "Repeat...", "SSE.Controllers.Print.textSelectRange": "Select range", "SSE.Controllers.Print.txtCustom": "Po meri", "SSE.Controllers.Print.txtZoomToPage": "Zoom to page", "SSE.Controllers.Search.textInvalidRange": "ERROR! Invalid cells range", "SSE.Controllers.Search.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "SSE.Controllers.Search.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "SSE.Controllers.Search.textReplaceSuccess": "Search has been done. {0} occurrences have been replaced", "SSE.Controllers.Statusbar.errorLastSheet": "Delovni zvezek mora imeti vsaj eno vidno delovno stran.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Delovnega listna ni mogoče izbrisati.", "SSE.Controllers.Statusbar.strSheet": "Stran", "SSE.Controllers.Statusbar.textDisconnect": "<b>Connection is lost</b><br>Trying to connect. Please check connection settings.", "SSE.Controllers.Statusbar.textSheetViewTip": "You are in Sheet View mode. Filters and sorting are visible only to you and those who are still in this view.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "You are in Sheet View mode. Filters are visible only to you and those who are still in this view.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Delovni list morda vsebuje podatke. <PERSON>, da ž<PERSON><PERSON> na<PERSON>?", "SSE.Controllers.Statusbar.zoomText": "Povečava {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "The font you are going to save is not available on the current device.<br>The text style will be displayed using one of the device fonts, the saved font will be used when it is available.<br>Do you want to continue?", "SSE.Controllers.Toolbar.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Controllers.Toolbar.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "ERROR! The maximum number of data series per chart is 255", "SSE.Controllers.Toolbar.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "SSE.Controllers.Toolbar.helpCalcItems": "Work with calculated items in Pivot Tables.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Calculated items", "SSE.Controllers.Toolbar.helpFastUndo": "Easily undo changes while collaborating on sheets in Fast mode.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "\"Undo\" in real-time co-editing", "SSE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "SSE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textDirectional": "Directional", "SSE.Controllers.Toolbar.textFontSizeErr": "Vnesena vrednost je nepravilna.<br>Prosim vnesite numerično vrednost med 1 in 409", "SSE.Controllers.Toolbar.textFraction": "Frak<PERSON><PERSON>", "SSE.Controllers.Toolbar.textFunction": "Functions", "SSE.Controllers.Toolbar.textIndicator": "Indicators", "SSE.Controllers.Toolbar.textInsert": "Vstavi", "SSE.Controllers.Toolbar.textIntegral": "Integrali", "SSE.Controllers.Toolbar.textLargeOperator": "Large operators", "SSE.Controllers.Toolbar.textLimitAndLog": "Limits and logarithms", "SSE.Controllers.Toolbar.textLongOperation": "Long operation", "SSE.Controllers.Toolbar.textMatrix": "Matrices", "SSE.Controllers.Toolbar.textOperator": "Operators", "SSE.Controllers.Toolbar.textPivot": "Pivot Table", "SSE.Controllers.Toolbar.textRadical": "Radicals", "SSE.Controllers.Toolbar.textRating": "Ratings", "SSE.Controllers.Toolbar.textRecentlyUsed": "Recently used", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textShapes": "Oblike", "SSE.Controllers.Toolbar.textSymbols": "Symbols", "SSE.Controllers.Toolbar.textWarning": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Right-left arrow above", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Leftwards arrow above", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Rightwards arrow above", "SSE.Controllers.Toolbar.txtAccent_Bar": "Stolpični grafikon", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Underbar", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Overbar", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Boxed formula (with placeholder)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Boxed formula (example)", "SSE.Controllers.Toolbar.txtAccent_Check": "Obkljukaj", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Underbrace", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Overbrace", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC z nadvrstico", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y with overbar", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Triple dot", "SSE.Controllers.Toolbar.txtAccent_DDot": "Double Dot", "SSE.Controllers.Toolbar.txtAccent_Dot": "Pika", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Double overbar", "SSE.Controllers.Toolbar.txtAccent_Grave": "Grave", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Grouping character below", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Grouping character above", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Leftwards harpoon above", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Rightwards harpoon above", "SSE.Controllers.Toolbar.txtAccent_Hat": "Hat", "SSE.Controllers.Toolbar.txtAccent_Smile": "Breve", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Oklepaji z ločevalniki", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Oklepaji z ločevalniki", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Right angle bracket", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Left angle bracket", "SSE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Oklepaji z ločevalniki", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Right curly bracket", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Left curly bracket", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Cases (two conditions)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Cases (three conditions)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Stack object", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Stack object in parentheses", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Primer primerov", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Binoms<PERSON> koe<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Binoms<PERSON> koe<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Right vertical bar", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Left vertical bar", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Right double vertical bar", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Left double vertical bar", "SSE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Right floor", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Left floor", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Oklepaji z ločevalniki", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Right parenthesis", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Left parenthesis", "SSE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Right square bracket", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Left square bracket", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Right double square bracket", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Left double square bracket", "SSE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Right ceiling", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Left ceiling", "SSE.Controllers.Toolbar.txtDeleteCells": "Izbriši celice", "SSE.Controllers.Toolbar.txtExpand": "Expand and sort", "SSE.Controllers.Toolbar.txtExpandSort": "The data next to the selection will not be sorted. Do you want to expand the selection to include the adjacent data or continue with sorting the currently selected cells only?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Skewed fraction", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "dx over dy", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "cap delta y over cap delta x", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "partial y over partial x", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "delta y over delta x", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Linear fraction", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi čez 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Small fraction", "SSE.Controllers.Toolbar.txtFractionVertical": "Stacked fraction", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Inverse cosine function", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Hyperbolic inverse cosine function", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Inverse cotangent function", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Hyperbolic inverse cotangent function", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Inverse cosecant function", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Hyperbolic inverse cosecant function", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Inverse secant function", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Hyperbolic inverse secant function", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Inverse sine function", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Hyperbolic inverse sine function", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Inverse tangent function", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Hyperbolic inverse tangent function", "SSE.Controllers.Toolbar.txtFunction_Cos": "Cosine function", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Hyperbolic cosine function", "SSE.Controllers.Toolbar.txtFunction_Cot": "Cotangent function", "SSE.Controllers.Toolbar.txtFunction_Coth": "Hyperbolic cotangent function", "SSE.Controllers.Toolbar.txtFunction_Csc": "Cosecant function", "SSE.Controllers.Toolbar.txtFunction_Csch": "Hyperbolic cosecant function", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sine theta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Tangent formula", "SSE.Controllers.Toolbar.txtFunction_Sec": "Secant function", "SSE.Controllers.Toolbar.txtFunction_Sech": "Hyperbolic secant function", "SSE.Controllers.Toolbar.txtFunction_Sin": "Sine function", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Hyperbolic sine function", "SSE.Controllers.Toolbar.txtFunction_Tan": "Tangent function", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Hyperbolic tangent function", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Custom", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Data and model", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Good, bad and neutral", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "No name", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Number format", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Themed cell styles", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Titles and headings", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Custom", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Dark", "SSE.Controllers.Toolbar.txtGroupTable_Light": "Light", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Medium", "SSE.Controllers.Toolbar.txtInsertCells": "Insert cells", "SSE.Controllers.Toolbar.txtIntegral": "Integral", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Differential theta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Differential x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Differential y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral", "SSE.Controllers.Toolbar.txtIntegralDouble": "Double Integral", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Double integral with stacked limits", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Double integral with limits", "SSE.Controllers.Toolbar.txtIntegralOriented": "Contour integral", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Contour integral with stacked limits", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Surface integral", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Surface integral with stacked limits", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Surface integral with limits", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Contour integral with limits", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Volume integral", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Volume integral with stacked limits", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Volume integral with limits", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integral", "SSE.Controllers.Toolbar.txtIntegralTriple": "Triple integral", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Triple integral with stacked limits", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Triple integral with limits", "SSE.Controllers.Toolbar.txtInvalidRange": "ERROR! Invalid cell range", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Logical And", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Logical And with lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Logical And with limits", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Logical And with subscript lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Logical And with subscript/superscript limits", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "So<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "So<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "So<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "So<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "So<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summation over k of n choose k", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summation from i equal zero to n", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summation example using two indices", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Product example", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Union example", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Logical Or", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Logical Or with lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Logical Or with limits", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Logical Or with subscript lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Logical Or with subscript/superscript limits", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersection", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersection with lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersection with limits", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersection with subscript lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersection with subscript/superscript limits", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Product", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Product with lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Product with limits", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Product with subscript lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Product with subscript/superscript limits", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Summation", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summation with lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summation with limits", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summation with subscript lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summation with subscript/superscript limits", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Union with lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Union with limits", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Union with subscript lower limit", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Union with subscript/superscript limits", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Limit example", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Maximum example", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Limit", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Natural logarithm", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logarithm", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarithm", "SSE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimalno", "SSE.Controllers.Toolbar.txtLockSort": "Data is found next to your selection, but you do not have sufficient permissions to change those cells.<br>Do you wish to continue with the current selection?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 prazna matrica", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 empty matrix", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 prazna matrica", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 prazna matrica", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Empty 2 by 2 matrix in double vertical bars", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Empty 2 by 2 determinant", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Empty 2 by 2 matrix in parentheses", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Empty 2 by 2 matrix in brackets", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 empty matrix", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 empty matrix", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 empty matrix", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 empty matrix", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Baseline dots", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Midline dots", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diagonalne pike", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Vertical dots", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Sparse matrix in parentheses", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Sparse matrix in brackets", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 identity matrix with zeros", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "2x2 identity matrix with blank off-diagonal cells", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 identity matrix with zeros", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 identity matrix with blank off-diagonal cells", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Right-left arrow below", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Right-left arrow above", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Leftwards arrow below", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Leftwards arrow above", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Rightwards arrow below", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Rightwards arrow above", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Enak dvopičju", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Yields", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta yields", "SSE.Controllers.Toolbar.txtOperator_Definition": "Equal to by definition", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "<PERSON> je enaka", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Right-left double arrow below", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Right-left double arrow above", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Leftwards arrow below", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Leftwards arrow above", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Rightwards arrow below", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Rightwards arrow above", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Equal equal", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Minus equal", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus equal", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Measured by", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Right hand side of quadratic formula", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Square root of a squared plus b squared", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Square root with degree", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Kvadratni koren", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Radical with degree", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Square root", "SSE.Controllers.Toolbar.txtScriptCustom_1": "x subscript y squared", "SSE.Controllers.Toolbar.txtScriptCustom_2": "e to the minus i omega t", "SSE.Controllers.Toolbar.txtScriptCustom_3": "x squared", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Y left superscript n left subscript one", "SSE.Controllers.Toolbar.txtScriptSub": "Subscript", "SSE.Controllers.Toolbar.txtScriptSubSup": "Subscript-superscript", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Left subscript-superscript", "SSE.Controllers.Toolbar.txtScriptSup": "Superscript", "SSE.Controllers.Toolbar.txtSorting": "Sorting", "SSE.Controllers.Toolbar.txtSortSelected": "Sort selected", "SSE.Controllers.Toolbar.txtSymbol_about": "Približno", "SSE.Controllers.Toolbar.txtSymbol_additional": "Kompliment", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "Almost equal to", "SSE.Controllers.Toolbar.txtSymbol_ast": "Asterisk operator", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Bullet operator", "SSE.Controllers.Toolbar.txtSymbol_cap": "Intersection", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Midline horizontal ellipsis", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Približno enako", "SSE.Controllers.Toolbar.txtSymbol_cup": "Union", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Down right diagonal ellipsis", "SSE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Division sign", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Down arrow", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Empty set", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Identical to", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "There exist", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Factorial", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_forall": "<PERSON>a vse", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "Greater than or equal to", "SSE.Controllers.Toolbar.txtSymbol_gg": "Much greater than", "SSE.Controllers.Toolbar.txtSymbol_greater": "Greater than", "SSE.Controllers.Toolbar.txtSymbol_in": "Element of", "SSE.Controllers.Toolbar.txtSymbol_inc": "Increment", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Neskončnost", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Left arrow", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Left-right arrow", "SSE.Controllers.Toolbar.txtSymbol_leq": "Less than or equal to", "SSE.Controllers.Toolbar.txtSymbol_less": "Less than", "SSE.Controllers.Toolbar.txtSymbol_ll": "Much less than", "SSE.Controllers.Toolbar.txtSymbol_minus": "Minus", "SSE.Controllers.Toolbar.txtSymbol_mp": "Minus plus", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Not equal to", "SSE.Controllers.Toolbar.txtSymbol_ni": "Vsebuje kot član", "SSE.Controllers.Toolbar.txtSymbol_not": "Not sign", "SSE.Controllers.Toolbar.txtSymbol_notexists": "There does not exist", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Partial differential", "SSE.Controllers.Toolbar.txtSymbol_percent": "Percentage", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus minus", "SSE.Controllers.Toolbar.txtSymbol_propto": "Proportional to", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Fourth root", "SSE.Controllers.Toolbar.txtSymbol_qed": "End of proof", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Up right diagonal ellipsis", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Right arrow", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Radical sign", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Therefore", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "Multiplication sign", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Up arrow", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon variant", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Phi variant", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Pi varianta", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Rho variant", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma variant", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Theta variant", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Vertical ellipsis", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Table style Dark", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Table style Light", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Table style Medium", "SSE.Controllers.Toolbar.warnLongOperation": "The operation you are about to perform might take rather much time to complete.<br>Are you sure you want to continue?", "SSE.Controllers.Toolbar.warnMergeLostData": "Le podatki z zgornje leve celice bodo ostali v združeni celici. <br><PERSON><PERSON> <PERSON>p<PERSON>, da želite nadaljevati?", "SSE.Controllers.Toolbar.warnNoRecommended": "To create a chart, select the cells that contain the data you'd like to use.<br>If you have names for the rows and columns and you'd like use them as labels, include them in your selection.", "SSE.Controllers.Viewport.textFreezePanes": "Freeze panes", "SSE.Controllers.Viewport.textFreezePanesShadow": "Show Frozen Panes Shadow", "SSE.Controllers.Viewport.textHideFBar": "Hide Formula Bar", "SSE.Controllers.Viewport.textHideGridlines": "Hide Gridlines", "SSE.Controllers.Viewport.textHideHeadings": "<PERSON><PERSON> Headings", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Decimal separator", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Thousands separator", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Settings used to recognize numeric data", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Text qualifier", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Napredne nastavitve", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(brez)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Filter po meri", "SSE.Views.AutoFilterDialog.textAddSelection": "Add current selection to filter", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Blanks}", "SSE.Views.AutoFilterDialog.textSelectAll": "Izberi vse", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Select all search results", "SSE.Views.AutoFilterDialog.textWarning": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtAboveAve": "Nad p<PERSON>", "SSE.Views.AutoFilterDialog.txtAfter": "After...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "All dates in the period", "SSE.Views.AutoFilterDialog.txtApril": "April", "SSE.Views.AutoFilterDialog.txtAugust": "August", "SSE.Views.AutoFilterDialog.txtBefore": "Before...", "SSE.Views.AutoFilterDialog.txtBegins": "Se začne z ...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Below average", "SSE.Views.AutoFilterDialog.txtBetween": "med ...", "SSE.Views.AutoFilterDialog.txtClear": "Clear", "SSE.Views.AutoFilterDialog.txtContains": "Vsebuje ...", "SSE.Views.AutoFilterDialog.txtDateFilter": "Date filter", "SSE.Views.AutoFilterDialog.txtDecember": "December", "SSE.Views.AutoFilterDialog.txtEmpty": "Vnesi celični filter", "SSE.Views.AutoFilterDialog.txtEnds": "Se konča z/s ...", "SSE.Views.AutoFilterDialog.txtEquals": "Enako ...", "SSE.Views.AutoFilterDialog.txtFebruary": "February", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filter by cells color", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filter by font color", "SSE.Views.AutoFilterDialog.txtGreater": "Greater than...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Greater than or equal to...", "SSE.Views.AutoFilterDialog.txtJanuary": "January", "SSE.Views.AutoFilterDialog.txtJuly": "July", "SSE.Views.AutoFilterDialog.txtJune": "June", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Label filter", "SSE.Views.AutoFilterDialog.txtLastMonth": "Last month", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Last quarter", "SSE.Views.AutoFilterDialog.txtLastWeek": "Last week", "SSE.Views.AutoFilterDialog.txtLastYear": "Last year", "SSE.Views.AutoFilterDialog.txtLess": "Less than...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Less than or equal to...", "SSE.Views.AutoFilterDialog.txtMarch": "March", "SSE.Views.AutoFilterDialog.txtMay": "May", "SSE.Views.AutoFilterDialog.txtNextMonth": "Next month", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Next quarter", "SSE.Views.AutoFilterDialog.txtNextWeek": "Next week", "SSE.Views.AutoFilterDialog.txtNextYear": "Next year", "SSE.Views.AutoFilterDialog.txtNotBegins": "Se ne začne z ...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Not between...", "SSE.Views.AutoFilterDialog.txtNotContains": "Does not contain...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Does not end with...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Does not equal...", "SSE.Views.AutoFilterDialog.txtNovember": "November", "SSE.Views.AutoFilterDialog.txtNumFilter": "Number filter", "SSE.Views.AutoFilterDialog.txtOctober": "October", "SSE.Views.AutoFilterDialog.txtQuarter1": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Quarter 1", "SSE.Views.AutoFilterDialog.txtReapply": "Reapply", "SSE.Views.AutoFilterDialog.txtSeptember": "September", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Sort by cells color", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Sort by font color", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Sort highest to lowest", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Sort lowest to highest", "SSE.Views.AutoFilterDialog.txtSortOption": "More sort options...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Text filter", "SSE.Views.AutoFilterDialog.txtThisMonth": "This Month", "SSE.Views.AutoFilterDialog.txtThisQuarter": "This quarter", "SSE.Views.AutoFilterDialog.txtThisWeek": "This week", "SSE.Views.AutoFilterDialog.txtThisYear": "This Year", "SSE.Views.AutoFilterDialog.txtTitle": "Filter", "SSE.Views.AutoFilterDialog.txtToday": "Today", "SSE.Views.AutoFilterDialog.txtTomorrow": "Tomorrow", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Value filter", "SSE.Views.AutoFilterDialog.txtYearToDate": "Year to date", "SSE.Views.AutoFilterDialog.txtYesterday": "Yesterday", "SSE.Views.AutoFilterDialog.warnFilterError": "You need at least one field in the Values area in order to apply a value filter.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Izbrati morate vsaj eno vrednost", "SSE.Views.CellEditor.textManager": "Manager", "SSE.Views.CellEditor.tipFormula": "Vstavi funkcijo", "SSE.Views.CellRangeDialog.errorMaxRows": "NAPAKA! Maksimalno število podatkovnih serij na grafikon je 255", "SSE.Views.CellRangeDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "SSE.Views.CellRangeDialog.txtEmpty": "To polje je obvezno", "SSE.Views.CellRangeDialog.txtInvalidRange": "NAPAKA! Neveljaven razpon celic", "SSE.Views.CellRangeDialog.txtTitle": "Izberi območje podatkov", "SSE.Views.CellSettings.strShrink": "Shrink to fit", "SSE.Views.CellSettings.strWrap": "Wrap text", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBackground": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBorderColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "<PERSON><PERSON> obrob", "SSE.Views.CellSettings.textClearRule": "Clear Rules", "SSE.Views.CellSettings.textColor": "Color fill", "SSE.Views.CellSettings.textColorScales": "Color scales", "SSE.Views.CellSettings.textCondFormat": "Conditional formatting", "SSE.Views.CellSettings.textControl": "Text control", "SSE.Views.CellSettings.textDataBars": "Data bars", "SSE.Views.CellSettings.textDirection": "Direction", "SSE.Views.CellSettings.textFill": "Zapolni", "SSE.Views.CellSettings.textForeground": "<PERSON><PERSON> o<PERSON>ja", "SSE.Views.CellSettings.textGradient": "Gradient points", "SSE.Views.CellSettings.textGradientColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "Gradient fill", "SSE.Views.CellSettings.textIndent": "Indent", "SSE.Views.CellSettings.textItems": "Items", "SSE.Views.CellSettings.textLinear": "Linear", "SSE.Views.CellSettings.textManageRule": "Manage rules", "SSE.Views.CellSettings.textNewRule": "New rule", "SSE.Views.CellSettings.textNoFill": "No fill", "SSE.Views.CellSettings.textOrientation": "Text orientation", "SSE.Views.CellSettings.textPattern": "Pattern", "SSE.Views.CellSettings.textPatternFill": "Pattern", "SSE.Views.CellSettings.textPosition": "Position", "SSE.Views.CellSettings.textRadial": "Radial", "SSE.Views.CellSettings.textSelectBorders": "Select borders you want to change applying style chosen above", "SSE.Views.CellSettings.textSelection": "From current selection", "SSE.Views.CellSettings.textThisPivot": "From this pivot", "SSE.Views.CellSettings.textThisSheet": "From this worksheet", "SSE.Views.CellSettings.textThisTable": "From this table", "SSE.Views.CellSettings.tipAddGradientPoint": "Add gradient point", "SSE.Views.CellSettings.tipAll": "Set outer border and all inner lines", "SSE.Views.CellSettings.tipBottom": "Set outer bottom border only", "SSE.Views.CellSettings.tipDiagD": "Set diagonal down border", "SSE.Views.CellSettings.tipDiagU": "Set diagonal up border", "SSE.Views.CellSettings.tipInner": "Set inner lines only", "SSE.Views.CellSettings.tipInnerHor": "Set horizontal inner lines only", "SSE.Views.CellSettings.tipInnerVert": "Set vertical inner lines only", "SSE.Views.CellSettings.tipLeft": "Set outer left border only", "SSE.Views.CellSettings.tipNone": "Set no borders", "SSE.Views.CellSettings.tipOuter": "Set outer border only", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Remove gradient point", "SSE.Views.CellSettings.tipRight": "Set outer right border only", "SSE.Views.CellSettings.tipTop": "Set outer top border only", "SSE.Views.ChartDataDialog.errorInFormula": "There's an error in formula you entered.", "SSE.Views.ChartDataDialog.errorInvalidReference": "The reference is not valid. Reference must be to an open worksheet.", "SSE.Views.ChartDataDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "The reference is not valid. References for titles, values, sizes, or data labels must be a single cell, row, or column.", "SSE.Views.ChartDataDialog.errorNoValues": "To create a chart, the series must contain at least one value.", "SSE.Views.ChartDataDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "SSE.Views.ChartDataDialog.textAdd": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textCategory": "Horizontal (category) axis labels", "SSE.Views.ChartDataDialog.textData": "Chart data range", "SSE.Views.ChartDataDialog.textDelete": "Odstrani", "SSE.Views.ChartDataDialog.textDown": "Down", "SSE.Views.ChartDataDialog.textEdit": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ChartDataDialog.textSelectData": "Select data", "SSE.Views.ChartDataDialog.textSeries": "Legend entries (series)", "SSE.Views.ChartDataDialog.textSwitch": "Switch row/column", "SSE.Views.ChartDataDialog.textTitle": "Podat<PERSON> diagrama", "SSE.Views.ChartDataDialog.textUp": "Up", "SSE.Views.ChartDataRangeDialog.errorInFormula": "There's an error in formula you entered.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "The reference is not valid. Reference must be to an open worksheet.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "The reference is not valid. References for titles, values, sizes, or data labels must be a single cell, row, or column.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "To create a chart, the series must contain at least one value.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ChartDataRangeDialog.textSelectData": "Select data", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Axis label range", "SSE.Views.ChartDataRangeDialog.txtChoose": "Choose range", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Series name", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Axis labels", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Edit series", "SSE.Views.ChartDataRangeDialog.txtValues": "Values", "SSE.Views.ChartDataRangeDialog.txtXValues": "X values", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y values", "SSE.Views.ChartSettings.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartSettings.strLineWeight": "Line weight", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "Template", "SSE.Views.ChartSettings.text3dDepth": "Depth (% of base)", "SSE.Views.ChartSettings.text3dHeight": "Height (% of base)", "SSE.Views.ChartSettings.text3dRotation": "3D Rotation", "SSE.Views.ChartSettings.textAdvanced": "Prikaži napredne nastavitve", "SSE.Views.ChartSettings.textAutoscale": "Autoscale", "SSE.Views.ChartSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "Change type", "SSE.Views.ChartSettings.textChartType": "Spremeni vrsto razpredelnice", "SSE.Views.ChartSettings.textDefault": "Default Rotation", "SSE.Views.ChartSettings.textDown": "Down", "SSE.Views.ChartSettings.textEditData": "<PERSON><PERSON><PERSON> pod<PERSON>", "SSE.Views.ChartSettings.textFirstPoint": "Prva točka", "SSE.Views.ChartSettings.textHeight": "Višina", "SSE.Views.ChartSettings.textHighPoint": "High point", "SSE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textLastPoint": "Last point", "SSE.Views.ChartSettings.textLeft": "Left", "SSE.Views.ChartSettings.textLowPoint": "Low Point", "SSE.Views.ChartSettings.textMarkers": "Markers", "SSE.Views.ChartSettings.textNarrow": "Narrow field of view", "SSE.Views.ChartSettings.textNegativePoint": "Negative point", "SSE.Views.ChartSettings.textPerspective": "Perspective", "SSE.Views.ChartSettings.textRanges": "Data range", "SSE.Views.ChartSettings.textRight": "Right", "SSE.Views.ChartSettings.textRightAngle": "Right angle axes", "SSE.Views.ChartSettings.textSelectData": "Select data", "SSE.Views.ChartSettings.textShow": "Show", "SSE.Views.ChartSettings.textSize": "Velikost", "SSE.Views.ChartSettings.textStyle": "Slog", "SSE.Views.ChartSettings.textSwitch": "Switch Row/Column", "SSE.Views.ChartSettings.textType": "Type", "SSE.Views.ChartSettings.textUp": "Up", "SSE.Views.ChartSettings.textWiden": "Widen field of view", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textX": "X rotation", "SSE.Views.ChartSettings.textY": "Y rotation", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ERROR! The maximum number of points in series per chart is 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "NAPAKA! Maksimalno število podatkovnih serij na grafikon je 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Nepravilen vrstni red vrstic. Za izgradnjo razpredelnice delnic podatke na strani navedite v sledečem vrstnem redu:<br>ot<PERSON>itvena cena, ma<PERSON><PERSON><PERSON>a cena, <PERSON>na cena, zak<PERSON><PERSON><PERSON><PERSON> cena.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Don't move or size with cells", "SSE.Views.ChartSettingsDlg.textAlt": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "SSE.Views.ChartSettingsDlg.textAltDescription": "Opis", "SSE.Views.ChartSettingsDlg.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart, or table.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Title", "SSE.Views.ChartSettingsDlg.textAuto": "Samodejno", "SSE.Views.ChartSettingsDlg.textAutoEach": "Auto for each", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "K<PERSON>ži osi", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Možnosti osi", "SSE.Views.ChartSettingsDlg.textAxisPos": "Položaj osi", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Axis Settings", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Title", "SSE.Views.ChartSettingsDlg.textBase": "Base", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Med obklju<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBillions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCategoryName": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCenter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Elementi grafa &<br>Legenda grafa", "SSE.Views.ChartSettingsDlg.textChartTitle": "Naslov grafa", "SSE.Views.ChartSettingsDlg.textCross": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCustom": "Po meri", "SSE.Views.ChartSettingsDlg.textDataColumns": "v stolpcih", "SSE.Views.ChartSettingsDlg.textDataLabels": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDataRows": "v vrsticah", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Prikaži legendo", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Hidden and empty cells", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Connect data points with line", "SSE.Views.ChartSettingsDlg.textFit": "Fit to width", "SSE.Views.ChartSettingsDlg.textFixed": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textFormat": "Label format", "SSE.Views.ChartSettingsDlg.textGaps": "Gaps", "SSE.Views.ChartSettingsDlg.textGridLines": "Gridlines", "SSE.Views.ChartSettingsDlg.textGroup": "Group sparkline", "SSE.Views.ChartSettingsDlg.textHide": "<PERSON>de", "SSE.Views.ChartSettingsDlg.textHideAxis": "Hide axis", "SSE.Views.ChartSettingsDlg.textHigh": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Secondary horizontal axis", "SSE.Views.ChartSettingsDlg.textHorizontal": "Horizontalen", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Stotine", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "v", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Notranje dno", "SSE.Views.ChartSettingsDlg.textInnerTop": "Notranji vrh", "SSE.Views.ChartSettingsDlg.textInvalidRange": "NAPAKA! Neveljaven razpon celic", "SSE.Views.ChartSettingsDlg.textLabelDist": "Razdalja oznak osi", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Interval med ozna<PERSON>", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Možnosti oznake", "SSE.Views.ChartSettingsDlg.textLabelPos": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLayout": "Postavitev", "SSE.Views.ChartSettingsDlg.textLeft": "Levo", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Levo <PERSON>", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Dno", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Levo", "SSE.Views.ChartSettingsDlg.textLegendPos": "<PERSON>a", "SSE.Views.ChartSettingsDlg.textLegendRight": "Des<PERSON>", "SSE.Views.ChartSettingsDlg.textLegendTop": "Vrh", "SSE.Views.ChartSettingsDlg.textLines": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLocationRange": "Location range", "SSE.Views.ChartSettingsDlg.textLogScale": "Logarithmic scale", "SSE.Views.ChartSettingsDlg.textLow": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Velik in majhen", "SSE.Views.ChartSettingsDlg.textMajorType": "Velika vrsta", "SSE.Views.ChartSettingsDlg.textManual": "Ročno", "SSE.Views.ChartSettingsDlg.textMarkers": "Oznake", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Interval med znaki", "SSE.Views.ChartSettingsDlg.textMaxValue": "Maksimalna vrednost", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinorType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinValue": "Minimalna vrednost", "SSE.Views.ChartSettingsDlg.textNextToAxis": "<PERSON><PERSON> osi", "SSE.Views.ChartSettingsDlg.textNone": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Ni pre<PERSON>", "SSE.Views.ChartSettingsDlg.textOneCell": "Move but don't size with cells", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Na obkljukanih <PERSON>h", "SSE.Views.ChartSettingsDlg.textOut": "Ven", "SSE.Views.ChartSettingsDlg.textOuterTop": "Zunanji vrh", "SSE.Views.ChartSettingsDlg.textOverlay": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textReverse": "Vrednosti v obratnem vrstnem redu", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Reverse order", "SSE.Views.ChartSettingsDlg.textRight": "Right", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Desno prekrivan<PERSON>", "SSE.Views.ChartSettingsDlg.textRotated": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSameAll": "Same for all", "SSE.Views.ChartSettingsDlg.textSelectData": "Izberi podatke", "SSE.Views.ChartSettingsDlg.textSeparator": "Ločevalnik oznak podatkov", "SSE.Views.ChartSettingsDlg.textSeriesName": "<PERSON><PERSON> se<PERSON>je", "SSE.Views.ChartSettingsDlg.textShow": "Show", "SSE.Views.ChartSettingsDlg.textShowBorders": "Prikaži meje razpredelnice", "SSE.Views.ChartSettingsDlg.textShowData": "Show data in hidden rows and columns", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Show empty cells as", "SSE.Views.ChartSettingsDlg.textShowEquation": "Display equation on chart", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Show axis", "SSE.Views.ChartSettingsDlg.textShowValues": "Prikaži vrednosti grafikona", "SSE.Views.ChartSettingsDlg.textSingle": "Single sparkline", "SSE.Views.ChartSettingsDlg.textSmooth": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSnap": "Cell snapping", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Sparkline ranges", "SSE.Views.ChartSettingsDlg.textStraight": "Raven", "SSE.Views.ChartSettingsDlg.textStyle": "Slog", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTickOptions": "Obkljukaj možnosti", "SSE.Views.ChartSettingsDlg.textTitle": "<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Sparkline - Advanced settings", "SSE.Views.ChartSettingsDlg.textTop": "Top", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Trendline options", "SSE.Views.ChartSettingsDlg.textTrillions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTwoCell": "Move and size with cells", "SSE.Views.ChartSettingsDlg.textType": "Tip", "SSE.Views.ChartSettingsDlg.textTypeData": "Vrsta & Podatki", "SSE.Views.ChartSettingsDlg.textUnits": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textValue": "Vrednost", "SSE.Views.ChartSettingsDlg.textVertAxis": "Vertikalne osi", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Secondary vertical axis", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Naslov X osi", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Naslov Y osi", "SSE.Views.ChartSettingsDlg.textZero": "Zero", "SSE.Views.ChartSettingsDlg.txtEmpty": "To polje je obvezno", "SSE.Views.ChartTypeDialog.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartTypeDialog.textSecondary": "Secondary axis", "SSE.Views.ChartTypeDialog.textSeries": "Series", "SSE.Views.ChartTypeDialog.textStyle": "Style", "SSE.Views.ChartTypeDialog.textTitle": "Chart type", "SSE.Views.ChartTypeDialog.textType": "Type", "SSE.Views.ChartWizardDialog.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartWizardDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order: opening price, max price, min price, closing price.", "SSE.Views.ChartWizardDialog.textRecommended": "Recommended", "SSE.Views.ChartWizardDialog.textSecondary": "Secondary Axis", "SSE.Views.ChartWizardDialog.textSeries": "Series", "SSE.Views.ChartWizardDialog.textTitle": "Insert Chart", "SSE.Views.ChartWizardDialog.textTitleChange": "Change chart type", "SSE.Views.ChartWizardDialog.textType": "Type", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Choose the chart type and axis for your data series", "SSE.Views.CreatePivotDialog.textDataRange": "Source data range", "SSE.Views.CreatePivotDialog.textDestination": "Choose where to place the table", "SSE.Views.CreatePivotDialog.textExist": "Existing worksheet", "SSE.Views.CreatePivotDialog.textInvalidRange": "Invalid cells range", "SSE.Views.CreatePivotDialog.textNew": "New worksheet", "SSE.Views.CreatePivotDialog.textSelectData": "Select data", "SSE.Views.CreatePivotDialog.textTitle": "Create pivot table", "SSE.Views.CreatePivotDialog.txtEmpty": "This field is required", "SSE.Views.CreateSparklineDialog.textDataRange": "Source data range", "SSE.Views.CreateSparklineDialog.textDestination": "Choose, where to place the sparklines", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Invalid cells range", "SSE.Views.CreateSparklineDialog.textSelectData": "Select data", "SSE.Views.CreateSparklineDialog.textTitle": "Create sparklines", "SSE.Views.CreateSparklineDialog.txtEmpty": "This field is required", "SSE.Views.DataTab.capBtnGroup": "<PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextCustomSort": "Custom Sort", "SSE.Views.DataTab.capBtnTextDataValidation": "Data Validation", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Remove Duplicates", "SSE.Views.DataTab.capBtnTextToCol": "Text to Columns", "SSE.Views.DataTab.capBtnUngroup": "Ungroup", "SSE.Views.DataTab.capDataExternalLinks": "External Links", "SSE.Views.DataTab.capDataFromText": "Iz besedila/CSV", "SSE.Views.DataTab.capGoalSeek": "Goal Seek", "SSE.Views.DataTab.mniFromFile": "From Local TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "From TXT/CSV Web Address", "SSE.Views.DataTab.mniFromXMLFile": "From Local XML", "SSE.Views.DataTab.textBelow": "Summary rows below detail", "SSE.Views.DataTab.textClear": "Clear outline", "SSE.Views.DataTab.textColumns": "Ungroup columns", "SSE.Views.DataTab.textGroupColumns": "Group columns", "SSE.Views.DataTab.textGroupRows": "Group rows", "SSE.Views.DataTab.textRightOf": "Summary columns to right of detail", "SSE.Views.DataTab.textRows": "Ungroup rows", "SSE.Views.DataTab.tipCustomSort": "Custom sort", "SSE.Views.DataTab.tipDataFromText": "Get data from file", "SSE.Views.DataTab.tipDataValidation": "Data validation", "SSE.Views.DataTab.tipExternalLinks": "View other files this spreadsheet is linked to", "SSE.Views.DataTab.tipGoalSeek": "Find the right input for the value you want", "SSE.Views.DataTab.tipGroup": "Group range of cells", "SSE.Views.DataTab.tipRemDuplicates": "Remove duplicate rows from a sheet", "SSE.Views.DataTab.tipToColumns": "Separate cell text into columns", "SSE.Views.DataTab.tipUngroup": "Ungroup range of cells", "SSE.Views.DataValidationDialog.errorFormula": "The value currently evaluates to an error. Do you want to continue?", "SSE.Views.DataValidationDialog.errorInvalid": "The value you entered for the field \"{0}\" is invalid.", "SSE.Views.DataValidationDialog.errorInvalidDate": "The date you entered for the field \"{0}\" is invalid.", "SSE.Views.DataValidationDialog.errorInvalidList": "The list source must be a delimited list, or a reference to single row or column.", "SSE.Views.DataValidationDialog.errorInvalidTime": "The time you entered for the field \"{0}\" is invalid.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "The \"{1}\" field must be greater than or equal to the \"{0}\" field.", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "You must enter a value in both field \"{0}\" and field \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "You must enter a value in field \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "A named range you specified cannot be found.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Negative values cannot be used in conditions \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "The field \"{0}\" must be a numeric value, numeric expression, or refer to a cell containing a numeric value.", "SSE.Views.DataValidationDialog.strError": "Error alert", "SSE.Views.DataValidationDialog.strInput": "Input message", "SSE.Views.DataValidationDialog.strSettings": "Settings", "SSE.Views.DataValidationDialog.textAlert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAllow": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textApply": "Apply these changes to all other cells with the same settings", "SSE.Views.DataValidationDialog.textCellSelected": "When cell is selected, show this input message", "SSE.Views.DataValidationDialog.textCompare": "Compare to", "SSE.Views.DataValidationDialog.textData": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndDate": "Končni datum", "SSE.Views.DataValidationDialog.textEndTime": "Končni čas", "SSE.Views.DataValidationDialog.textError": "Error message", "SSE.Views.DataValidationDialog.textFormula": "Formula", "SSE.Views.DataValidationDialog.textIgnore": "Ignore blank", "SSE.Views.DataValidationDialog.textInput": "Input message", "SSE.Views.DataValidationDialog.textMax": "Maximum", "SSE.Views.DataValidationDialog.textMessage": "Message", "SSE.Views.DataValidationDialog.textMin": "Minimum", "SSE.Views.DataValidationDialog.textSelectData": "Select data", "SSE.Views.DataValidationDialog.textShowDropDown": "Show drop-down list in cell", "SSE.Views.DataValidationDialog.textShowError": "Show error alert after invalid data is entered", "SSE.Views.DataValidationDialog.textShowInput": "Show input message when cell is selected", "SSE.Views.DataValidationDialog.textSource": "Source", "SSE.Views.DataValidationDialog.textStartDate": "Start date", "SSE.Views.DataValidationDialog.textStartTime": "Start time", "SSE.Views.DataValidationDialog.textStop": "Stop", "SSE.Views.DataValidationDialog.textStyle": "Style", "SSE.Views.DataValidationDialog.textTitle": "Title", "SSE.Views.DataValidationDialog.textUserEnters": "When user enters invalid data, show this error alert", "SSE.Views.DataValidationDialog.txtAny": "Any value", "SSE.Views.DataValidationDialog.txtBetween": "med", "SSE.Views.DataValidationDialog.txtDate": "Datum", "SSE.Views.DataValidationDialog.txtDecimal": "Decimal", "SSE.Views.DataValidationDialog.txtElTime": "Elapsed time", "SSE.Views.DataValidationDialog.txtEndDate": "Končni datum", "SSE.Views.DataValidationDialog.txtEndTime": "Končni čas", "SSE.Views.DataValidationDialog.txtEqual": "equals", "SSE.Views.DataValidationDialog.txtGreaterThan": "greater than", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "greater than or equal to", "SSE.Views.DataValidationDialog.txtLength": "Length", "SSE.Views.DataValidationDialog.txtLessThan": "less than", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "less than or equal to", "SSE.Views.DataValidationDialog.txtList": "List", "SSE.Views.DataValidationDialog.txtNotBetween": "not between", "SSE.Views.DataValidationDialog.txtNotEqual": "does not equal", "SSE.Views.DataValidationDialog.txtOther": "Other", "SSE.Views.DataValidationDialog.txtStartDate": "Start date", "SSE.Views.DataValidationDialog.txtStartTime": "Start time", "SSE.Views.DataValidationDialog.txtTextLength": "Text length", "SSE.Views.DataValidationDialog.txtTime": "Time", "SSE.Views.DataValidationDialog.txtWhole": "Whole number", "SSE.Views.DigitalFilterDialog.capAnd": "In", "SSE.Views.DigitalFilterDialog.capCondition1": "enako", "SSE.Views.DigitalFilterDialog.capCondition10": "se ne konča z", "SSE.Views.DigitalFilterDialog.capCondition11": "vsebuje", "SSE.Views.DigitalFilterDialog.capCondition12": "ne vseb<PERSON>je", "SSE.Views.DigitalFilterDialog.capCondition2": "ni enako", "SSE.Views.DigitalFilterDialog.capCondition3": "je ve<PERSON>ji kot", "SSE.Views.DigitalFilterDialog.capCondition30": "is after", "SSE.Views.DigitalFilterDialog.capCondition4": "je ve<PERSON>ji ali enak", "SSE.Views.DigitalFilterDialog.capCondition40": "is after or equal to", "SSE.Views.DigitalFilterDialog.capCondition5": "je manj kot", "SSE.Views.DigitalFilterDialog.capCondition50": "is before", "SSE.Views.DigitalFilterDialog.capCondition6": "je manj ali enak", "SSE.Views.DigitalFilterDialog.capCondition60": "is before or equal to", "SSE.Views.DigitalFilterDialog.capCondition7": "se začne z", "SSE.Views.DigitalFilterDialog.capCondition8": "se ne začne z", "SSE.Views.DigitalFilterDialog.capCondition9": "se konča z", "SSE.Views.DigitalFilterDialog.capOr": "ali", "SSE.Views.DigitalFilterDialog.textNoFilter": "ni filtra", "SSE.Views.DigitalFilterDialog.textShowRows": "Pokaži vrste kjer", "SSE.Views.DigitalFilterDialog.textUse1": "Uporabi ? za predstavitev katerega koli samskega znaka", "SSE.Views.DigitalFilterDialog.textUse2": "Uporabi * za predstavitev katere koli serije znakov", "SSE.Views.DigitalFilterDialog.txtSelectDate": "Select date", "SSE.Views.DigitalFilterDialog.txtTitle": "Filter po meri", "SSE.Views.DocumentHolder.advancedEquationText": "Equation settings", "SSE.Views.DocumentHolder.advancedImgText": "<PERSON><PERSON><PERSON><PERSON> nastavitve slike", "SSE.Views.DocumentHolder.advancedShapeText": "Napredne nastavitve oblike", "SSE.Views.DocumentHolder.advancedSlicerText": "Slicer advanced settings", "SSE.Views.DocumentHolder.allLinearText": "All - Linear", "SSE.Views.DocumentHolder.allProfText": "All - Professional", "SSE.Views.DocumentHolder.bottomCellText": "Poravnaj dno", "SSE.Views.DocumentHolder.bulletsText": "Bullets and Numbering", "SSE.Views.DocumentHolder.centerCellText": "Poravnaj <PERSON>", "SSE.Views.DocumentHolder.chartDataText": "Select Chart Data", "SSE.Views.DocumentHolder.chartText": "Napredne nastavitve grafa", "SSE.Views.DocumentHolder.chartTypeText": "Change Chart Type", "SSE.Views.DocumentHolder.currLinearText": "Current - Linear", "SSE.Views.DocumentHolder.currProfText": "Current - Professional", "SSE.Views.DocumentHolder.deleteColumnText": "Stolpec", "SSE.Views.DocumentHolder.deleteRowText": "Vrsta", "SSE.Views.DocumentHolder.deleteTableText": "Table", "SSE.Views.DocumentHolder.direct270Text": "Rotate at 270°", "SSE.Views.DocumentHolder.direct90Text": "Rotate at 90°", "SSE.Views.DocumentHolder.directHText": "Horizontal", "SSE.Views.DocumentHolder.directionText": "Text Direction", "SSE.Views.DocumentHolder.editChartText": "<PERSON><PERSON><PERSON> pod<PERSON>", "SSE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "SSE.Views.DocumentHolder.insertColumnLeftText": "Stolpec levo", "SSE.Views.DocumentHolder.insertColumnRightText": "Stolpec desno", "SSE.Views.DocumentHolder.insertRowAboveText": "Row Above", "SSE.Views.DocumentHolder.insertRowBelowText": "Row Below", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "Dejanska velikost", "SSE.Views.DocumentHolder.removeHyperlinkText": "Odstrani hiperpovezavo", "SSE.Views.DocumentHolder.selectColumnText": "Cel stolpec", "SSE.Views.DocumentHolder.selectDataText": "Podatki stol<PERSON>", "SSE.Views.DocumentHolder.selectRowText": "Vrsta", "SSE.Views.DocumentHolder.selectTableText": "Table", "SSE.Views.DocumentHolder.showEqToolbar": "Show Equation Toolbar", "SSE.Views.DocumentHolder.strDelete": "Remove Signature", "SSE.Views.DocumentHolder.strDetails": "Signature Details", "SSE.Views.DocumentHolder.strSetup": "Signature Setup", "SSE.Views.DocumentHolder.strSign": "Sign", "SSE.Views.DocumentHolder.textAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "Pošlji k ozadju", "SSE.Views.DocumentHolder.textArrangeBackward": "Premakni nazaj", "SSE.Views.DocumentHolder.textArrangeForward": "Premakni naprej", "SSE.Views.DocumentHolder.textArrangeFront": "Premakni v ospredje", "SSE.Views.DocumentHolder.textAverage": "POVPREČNA", "SSE.Views.DocumentHolder.textBullets": "Bullets", "SSE.Views.DocumentHolder.textCopyCells": "Copy cells", "SSE.Views.DocumentHolder.textCount": "Štetje", "SSE.Views.DocumentHolder.textCrop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFill": "Zapolni", "SSE.Views.DocumentHolder.textCropFit": "Fit", "SSE.Views.DocumentHolder.textEditPoints": "Edit points", "SSE.Views.DocumentHolder.textEntriesList": "Select from drop-down list", "SSE.Views.DocumentHolder.textFillDays": "Fill days", "SSE.Views.DocumentHolder.textFillFormatOnly": "Fill formatting only", "SSE.Views.DocumentHolder.textFillMonths": "Fill months", "SSE.Views.DocumentHolder.textFillSeries": "Fill series", "SSE.Views.DocumentHolder.textFillWeekdays": "Fill weekdays", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Fill without formatting", "SSE.Views.DocumentHolder.textFillYears": "Fill years", "SSE.Views.DocumentHolder.textFlashFill": "Flash fill", "SSE.Views.DocumentHolder.textFlipH": "Zrcali horizontalno", "SSE.Views.DocumentHolder.textFlipV": "Zrcali vertikalno", "SSE.Views.DocumentHolder.textFreezePanes": "Zamrzni plošče", "SSE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON> da<PERSON><PERSON>ke", "SSE.Views.DocumentHolder.textFromStorage": "<PERSON><PERSON> shrambe", "SSE.Views.DocumentHolder.textFromUrl": "z URL naslova", "SSE.Views.DocumentHolder.textGrowthTrend": "Growth trend", "SSE.Views.DocumentHolder.textLinearTrend": "Linear trend", "SSE.Views.DocumentHolder.textListSettings": "List Settings", "SSE.Views.DocumentHolder.textMacro": "Assign <PERSON>", "SSE.Views.DocumentHolder.textMax": "Max", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textMoreFormats": "More formats", "SSE.Views.DocumentHolder.textNone": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textNumbering": "Numbering", "SSE.Views.DocumentHolder.textReplace": "Replace image", "SSE.Views.DocumentHolder.textResetCrop": "Reset crop", "SSE.Views.DocumentHolder.textRotate": "Rotate", "SSE.Views.DocumentHolder.textRotate270": "Rotate 90В° Counterclockwise", "SSE.Views.DocumentHolder.textRotate90": "Rotate 90В° Clockwise", "SSE.Views.DocumentHolder.textSaveAsPicture": "Save as picture", "SSE.Views.DocumentHolder.textSeries": "Series", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Poravnaj spodaj", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Poravnava na sredino", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Poravnaj levo", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Poravnaj na sredino", "SSE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textShapeAlignTop": "Poravnaj na vrh", "SSE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "Sum", "SSE.Views.DocumentHolder.textUndo": "Undo", "SSE.Views.DocumentHolder.textUnFreezePanes": "Unfreeze Panes", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "Arrow bullets", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Checkmark bullets", "SSE.Views.DocumentHolder.tipMarkersDash": "Dash bullets", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Filled rhombus bullets", "SSE.Views.DocumentHolder.tipMarkersFRound": "Filled round bullets", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Filled square bullets", "SSE.Views.DocumentHolder.tipMarkersHRound": "Hollow round bullets", "SSE.Views.DocumentHolder.tipMarkersStar": "Star bullets", "SSE.Views.DocumentHolder.topCellText": "Poravnaj vrh", "SSE.Views.DocumentHolder.txtAccounting": "Računovodstvo", "SSE.Views.DocumentHolder.txtAddComment": "<PERSON><PERSON><PERSON> k<PERSON>", "SSE.Views.DocumentHolder.txtAddNamedRange": "Define Name", "SSE.Views.DocumentHolder.txtArrange": "Razporedi", "SSE.Views.DocumentHolder.txtAscending": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Auto fit column width", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Auto fit row height", "SSE.Views.DocumentHolder.txtAverage": "Average", "SSE.Views.DocumentHolder.txtCellFormat": "Format cells", "SSE.Views.DocumentHolder.txtClear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearAll": "Vse", "SSE.Views.DocumentHolder.txtClearComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearFormat": "Format", "SSE.Views.DocumentHolder.txtClearHyper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearPivotField": "Clear filter from {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Clear selected sparkline groups", "SSE.Views.DocumentHolder.txtClearSparklines": "Clear selected sparklines", "SSE.Views.DocumentHolder.txtClearText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCollapse": "Collapse", "SSE.Views.DocumentHolder.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.DocumentHolder.txtColumn": "Cel stolpec", "SSE.Views.DocumentHolder.txtColumnWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCondFormat": "Conditional formatting", "SSE.Views.DocumentHolder.txtCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCount": "Count", "SSE.Views.DocumentHolder.txtCurrency": "Valuta", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Custom column width", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Custom row height", "SSE.Views.DocumentHolder.txtCustomSort": "Custom sort", "SSE.Views.DocumentHolder.txtCut": "<PERSON><PERSON><PERSON>ži", "SSE.Views.DocumentHolder.txtDateLong": "Long Date", "SSE.Views.DocumentHolder.txtDateShort": "Short Date", "SSE.Views.DocumentHolder.txtDelete": "Izbriši", "SSE.Views.DocumentHolder.txtDelField": "Remove", "SSE.Views.DocumentHolder.txtDescending": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDifference": "Difference from", "SSE.Views.DocumentHolder.txtDistribHor": "Distribute horizontally", "SSE.Views.DocumentHolder.txtDistribVert": "Distribute vertically", "SSE.Views.DocumentHolder.txtEditComment": "<PERSON><PERSON><PERSON> komenta<PERSON>", "SSE.Views.DocumentHolder.txtEditObject": "Edit object", "SSE.Views.DocumentHolder.txtExpand": "Expand", "SSE.Views.DocumentHolder.txtExpandCollapse": "Expand/Collapse", "SSE.Views.DocumentHolder.txtExpandEntire": "Expand Entire Field", "SSE.Views.DocumentHolder.txtFieldSettings": "Field settings", "SSE.Views.DocumentHolder.txtFilter": "Filter", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filter by cell's color", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filter by font color", "SSE.Views.DocumentHolder.txtFilterValue": "Filter by selected cell's value", "SSE.Views.DocumentHolder.txtFormula": "Vstavi funkcijo", "SSE.Views.DocumentHolder.txtFraction": "Frakcija", "SSE.Views.DocumentHolder.txtGeneral": "Splošno", "SSE.Views.DocumentHolder.txtGetLink": "Get link to this range", "SSE.Views.DocumentHolder.txtGrandTotal": "Grand total", "SSE.Views.DocumentHolder.txtGroup": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtHide": "<PERSON>k<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtIndex": "Index", "SSE.Views.DocumentHolder.txtInsert": "Vstavi", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hiperpovezava", "SSE.Views.DocumentHolder.txtInsImage": "Insert image from file", "SSE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "SSE.Views.DocumentHolder.txtLabelFilter": "Label filters", "SSE.Views.DocumentHolder.txtMax": "Max", "SSE.Views.DocumentHolder.txtMin": "Min", "SSE.Views.DocumentHolder.txtMoreOptions": "More options", "SSE.Views.DocumentHolder.txtNormal": "No calculation", "SSE.Views.DocumentHolder.txtNumber": "Števil<PERSON>", "SSE.Views.DocumentHolder.txtNumFormat": "Number Format", "SSE.Views.DocumentHolder.txtPaste": "P<PERSON>ep<PERSON>", "SSE.Views.DocumentHolder.txtPercent": "% of", "SSE.Views.DocumentHolder.txtPercentage": "Percentage", "SSE.Views.DocumentHolder.txtPercentDiff": "% difference from", "SSE.Views.DocumentHolder.txtPercentOfCol": "% of column total", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% of grand total", "SSE.Views.DocumentHolder.txtPercentOfParent": "% of parent total", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% of parent column total", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% of parent row total", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% running total in", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% of row total", "SSE.Views.DocumentHolder.txtPivotSettings": "Pivot Table settings", "SSE.Views.DocumentHolder.txtProduct": "Product", "SSE.Views.DocumentHolder.txtRankAscending": "Rank smallest to largest", "SSE.Views.DocumentHolder.txtRankDescending": "Rank largest to smallest", "SSE.Views.DocumentHolder.txtReapply": "Reapply", "SSE.Views.DocumentHolder.txtRefresh": "Refresh", "SSE.Views.DocumentHolder.txtRow": "Cela <PERSON>rst<PERSON>", "SSE.Views.DocumentHolder.txtRowHeight": "V<PERSON>š<PERSON> vrste", "SSE.Views.DocumentHolder.txtRunTotal": "Running total in", "SSE.Views.DocumentHolder.txtScientific": "Scientific", "SSE.Views.DocumentHolder.txtSelect": "Select", "SSE.Views.DocumentHolder.txtShiftDown": "<PERSON><PERSON> premakni navzdol", "SSE.Views.DocumentHolder.txtShiftLeft": "<PERSON><PERSON> poma<PERSON>ni levo", "SSE.Views.DocumentHolder.txtShiftRight": "<PERSON><PERSON> p<PERSON>", "SSE.Views.DocumentHolder.txtShiftUp": "<PERSON><PERSON> pomakni navzgor", "SSE.Views.DocumentHolder.txtShow": "Pokaži", "SSE.Views.DocumentHolder.txtShowAs": "Show values as", "SSE.Views.DocumentHolder.txtShowComment": "Show comment", "SSE.Views.DocumentHolder.txtShowDetails": "Show details", "SSE.Views.DocumentHolder.txtSort": "Razv<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSortCellColor": "Selected cell color on top", "SSE.Views.DocumentHolder.txtSortFontColor": "Selected font color on top", "SSE.Views.DocumentHolder.txtSortOption": "More sort options", "SSE.Views.DocumentHolder.txtSparklines": "Sparklines", "SSE.Views.DocumentHolder.txtSubtotalField": "Subtotal", "SSE.Views.DocumentHolder.txtSum": "Sum", "SSE.Views.DocumentHolder.txtSummarize": "Summarize values by", "SSE.Views.DocumentHolder.txtText": "Text", "SSE.Views.DocumentHolder.txtTextAdvanced": "Na<PERSON><PERSON>ne nastavitve besedila", "SSE.Views.DocumentHolder.txtTime": "Time", "SSE.Views.DocumentHolder.txtTop10": "Top 10", "SSE.Views.DocumentHolder.txtUngroup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtValueFieldSettings": "Value field settings", "SSE.Views.DocumentHolder.txtValueFilter": "Value filters", "SSE.Views.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "Vertikalna <PERSON>", "SSE.Views.ExternalLinksDlg.closeButtonText": "Close", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Automatically update data from the linked sources", "SSE.Views.ExternalLinksDlg.textChange": "Change source", "SSE.Views.ExternalLinksDlg.textDelete": "Break links", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Break all links", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textOpen": "Open source", "SSE.Views.ExternalLinksDlg.textSource": "Source", "SSE.Views.ExternalLinksDlg.textStatus": "Status", "SSE.Views.ExternalLinksDlg.textUnknown": "Unknown", "SSE.Views.ExternalLinksDlg.textUpdate": "Update values", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Update all", "SSE.Views.ExternalLinksDlg.textUpdating": "Updating...", "SSE.Views.ExternalLinksDlg.txtTitle": "External links", "SSE.Views.FieldSettingsDialog.strLayout": "Layout", "SSE.Views.FieldSettingsDialog.strSubtotals": "Subtotals", "SSE.Views.FieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.FieldSettingsDialog.textReport": "Report form", "SSE.Views.FieldSettingsDialog.textTitle": "Field settings", "SSE.Views.FieldSettingsDialog.txtAverage": "POVPREČNA", "SSE.Views.FieldSettingsDialog.txtBlank": "Insert blank rows after each item", "SSE.Views.FieldSettingsDialog.txtBottom": "Show at bottom of group", "SSE.Views.FieldSettingsDialog.txtCompact": "Kompakten", "SSE.Views.FieldSettingsDialog.txtCount": "Štetje", "SSE.Views.FieldSettingsDialog.txtCountNums": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtCustomName": "Ime po meri", "SSE.Views.FieldSettingsDialog.txtEmpty": "Show items with no data", "SSE.Views.FieldSettingsDialog.txtMax": "Max", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "Outline", "SSE.Views.FieldSettingsDialog.txtProduct": "Product", "SSE.Views.FieldSettingsDialog.txtRepeat": "Repeat items labels at each row", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Show subtotals", "SSE.Views.FieldSettingsDialog.txtSourceName": "Source name:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "Sum", "SSE.Views.FieldSettingsDialog.txtSummarize": "Functions for subtotals", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabular", "SSE.Views.FieldSettingsDialog.txtTop": "Show at top of group", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.ariaFileMenu": "File menu", "SSE.Views.FileMenu.btnBackCaption": "Pojdi v dokumente", "SSE.Views.FileMenu.btnCloseEditor": "Close File", "SSE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON> meni", "SSE.Views.FileMenu.btnCreateNewCaption": "Ustvari nov", "SSE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON> kot", "SSE.Views.FileMenu.btnExitCaption": "Close", "SSE.Views.FileMenu.btnExportToPDFCaption": "Export to PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "Open", "SSE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnHistoryCaption": "Version History", "SSE.Views.FileMenu.btnInfoCaption": "Informacije razpredelnice", "SSE.Views.FileMenu.btnPrintCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnProtectCaption": "Zaščiti", "SSE.Views.FileMenu.btnRecentFilesCaption": "Odpri nedav<PERSON>", "SSE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnReturnCaption": "Nazaj na preglednico", "SSE.Views.FileMenu.btnRightsCaption": "Access Rights", "SSE.Views.FileMenu.btnSaveAsCaption": "Save as", "SSE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Save Copy As", "SSE.Views.FileMenu.btnSettingsCaption": "Napredne nastavitve", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "SSE.Views.FileMenu.btnToEditCaption": "<PERSON><PERSON><PERSON> razpredel<PERSON>o", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Blank Spreadsheet", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Create New", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Uporabi", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Dodaj <PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplikacija", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "A<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Spremeni pravice dostopa", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Komentar", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Created", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Nazadnje spremenjenil/a", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Nazadnje spremenjeno", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Owner", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Lokacija", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON><PERSON>, ki imajo pravice", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Spreadsheet info", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Subject", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Na<PERSON>lov razpredelnice", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Uploaded", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Access Rights", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Spremeni pravice dostopa", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON><PERSON>, ki imajo pravice", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Uporabi", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Co-editing mode", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Use 1904 date system", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Decimal separator", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Dictionary language", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Enable iterative calculation", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Fast", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Namigovanje pisave", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Formula Language", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Example: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Show function tooltip", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Show horizontal scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Ignore words in UPPERCASE", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Ignore words with numbers", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Nastavitve ma<PERSON>rojev", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Maximum change", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Maximum iterations", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Show the paste options button when the content is pasted", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "R1C1 reference style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Regional Settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Example: ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTL interface", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Show comments in sheet", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Show changes from other users", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Show resolved comments", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Snapped to the grid while scrolling", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Strict", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Tab style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Interface theme", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Thousands separator", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "<PERSON><PERSON><PERSON> enota", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Use separators based on regional settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Show vertical scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Privzeta vrednost zooma", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Vsakih 10 minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Vsakih 30 minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Vsakih 5 minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "<PERSON><PERSON><PERSON> u<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Autorecover", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Samodejno s<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Onemogočeno", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "Fill", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Saving intermediate versions", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "Line", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "<PERSON><PERSON><PERSON> min<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Reference Style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "Advanced settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "Appearance", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "AutoCorrect options...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Belarusian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Bulgarian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Catalan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Default cache mode", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Calculating", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centimeter", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Collaboration", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Češka", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Customize quick access", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Danska", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "De<PERSON>ch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Editing and saving", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Greek", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "English", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Spanish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Real-time co-editing. All changes are saved automatically", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Finnish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Hungarian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "Armenian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonesian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Palec", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Italian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Japanese", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Korean", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Last used", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Lao", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Latvian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "kot OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Norwegian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Dutch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Polish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Proofing", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Točka", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Portuguese (Brazil)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "Portuguese (Portugal)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Show the Quick Print button in the editor header", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Region", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Romanian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Russian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Enable All", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Enable all macros without a notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Turn on screen reader support", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Default sheet direction", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "This setting will affect only the new sheets", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Left-to-right", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Right-to-left", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Slovak", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Slovenian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Disable all", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Disable all macros without a notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Use the \"Save\" button to sync the changes you and others make", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Swedish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Use toolbar color as tabs background", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Turkish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ukrainian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Use Alt key to navigate the user interface using the keyboard", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Use Option key to navigate the user interface using the keyboard", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vietnamese", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Show Notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Disable all macros with a notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "kot Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Workspace", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "Chinese", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Warning", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "With password", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Protect Spreadsheet", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "With signature", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Valid signatures have been added to the spreadsheet.<br>The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Ensure the integrity of the spreadsheet by adding an<br>invisible digital signature", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "<PERSON><PERSON><PERSON> razpredel<PERSON>o", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Editing will remove signatures from the spreadsheet.<br>Continue?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "This spreadsheet has been protected by password", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Encrypt this spreadsheet with a password", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "This spreadsheet needs to be signed.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Valid signatures have been added to the spreadsheet. The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Some of the digital signatures in spreadsheet are invalid or could not be verified. The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "View signatures", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Download as", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Save copy as", "SSE.Views.FillSeriesDialog.textAuto": "AutoFill", "SSE.Views.FillSeriesDialog.textCols": "Columns", "SSE.Views.FillSeriesDialog.textDate": "Date", "SSE.Views.FillSeriesDialog.textDateUnit": "Date unit", "SSE.Views.FillSeriesDialog.textDay": "Day", "SSE.Views.FillSeriesDialog.textGrowth": "Growth", "SSE.Views.FillSeriesDialog.textLinear": "Linear", "SSE.Views.FillSeriesDialog.textMonth": "Month", "SSE.Views.FillSeriesDialog.textRows": "Rows", "SSE.Views.FillSeriesDialog.textSeries": "Series in", "SSE.Views.FillSeriesDialog.textStep": "Step value", "SSE.Views.FillSeriesDialog.textStop": "Stop value", "SSE.Views.FillSeriesDialog.textTitle": "Series", "SSE.Views.FillSeriesDialog.textTrend": "Trend", "SSE.Views.FillSeriesDialog.textType": "Type", "SSE.Views.FillSeriesDialog.textWeek": "Weekday", "SSE.Views.FillSeriesDialog.textYear": "Year", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FormatRulesEditDlg.fillColor": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Warning", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 color scale", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 color scale", "SSE.Views.FormatRulesEditDlg.textAllBorders": "All borders", "SSE.Views.FormatRulesEditDlg.textAppearance": "Bar appearance", "SSE.Views.FormatRulesEditDlg.textApply": "Apply to range", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Samodejeno", "SSE.Views.FormatRulesEditDlg.textAxis": "Axis", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Bar direction", "SSE.Views.FormatRulesEditDlg.textBold": "Bold", "SSE.Views.FormatRulesEditDlg.textBorder": "Border", "SSE.Views.FormatRulesEditDlg.textBordersColor": "<PERSON><PERSON> obrob", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Slogi obrob", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Spodnje obrobe", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Cannot add the conditional formatting.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Cell midpoint", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Inside vertical borders", "SSE.Views.FormatRulesEditDlg.textClear": "Clear", "SSE.Views.FormatRulesEditDlg.textColor": "Text color", "SSE.Views.FormatRulesEditDlg.textContext": "Context", "SSE.Views.FormatRulesEditDlg.textCustom": "Po meri", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Diagonal down border", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Diagonal up border", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Enter a valid formula.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "The formula you entered does not evaluate to a number, date, time or string.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Vnesite vrednost.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "The value you entered is not a valid number, date, time or string.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "The value for the {0} must be greater than the value for the {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Enter a number between {0} and {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Zapolni", "SSE.Views.FormatRulesEditDlg.textFormat": "Format", "SSE.Views.FormatRulesEditDlg.textFormula": "Formula", "SSE.Views.FormatRulesEditDlg.textGradient": "Gradient", "SSE.Views.FormatRulesEditDlg.textIconLabel": "when {0} {1} and", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "when {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "when value is", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "One or more icon data ranges overlap.<br>Adjust icon data range values so that the ranges do not overlap.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Icon style", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Inside borders", "SSE.Views.FormatRulesEditDlg.textInvalid": "Invalid data range.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.FormatRulesEditDlg.textItalic": "Italic", "SSE.Views.FormatRulesEditDlg.textItem": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Left to right", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Left borders", "SSE.Views.FormatRulesEditDlg.textLongBar": "longest bar", "SSE.Views.FormatRulesEditDlg.textMaximum": "Maximum", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Maxpoint", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Inside horizontal borders", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Midpoint", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimum", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Minpoint", "SSE.Views.FormatRulesEditDlg.textNegative": "Negative", "SSE.Views.FormatRulesEditDlg.textNewColor": "Dodaj novo barvo po meri", "SSE.Views.FormatRulesEditDlg.textNoBorders": "No borders", "SSE.Views.FormatRulesEditDlg.textNone": "None", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "One or more of the specified values is not a valid percentage.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "The specified {0} value is not a valid percentage.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "One or more of the specified values is not a valid percentile.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "The specified {0} value is not a valid percentile.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Outside borders", "SSE.Views.FormatRulesEditDlg.textPercent": "Percent", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentile", "SSE.Views.FormatRulesEditDlg.textPosition": "Position", "SSE.Views.FormatRulesEditDlg.textPositive": "Positive", "SSE.Views.FormatRulesEditDlg.textPresets": "Presets", "SSE.Views.FormatRulesEditDlg.textPreview": "Preview", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "You cannot use relative references in conditional formatting criteria for color scales, data bars, and icon sets.", "SSE.Views.FormatRulesEditDlg.textReverse": "Reverse icons order", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Right to left", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Right borders", "SSE.Views.FormatRulesEditDlg.textRule": "Rule", "SSE.Views.FormatRulesEditDlg.textSameAs": "Same as positive", "SSE.Views.FormatRulesEditDlg.textSelectData": "Select data", "SSE.Views.FormatRulesEditDlg.textShortBar": "shortest bar", "SSE.Views.FormatRulesEditDlg.textShowBar": "Show bar only", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Show icon only", "SSE.Views.FormatRulesEditDlg.textSingleRef": "This type of reference cannot be used in a conditional formatting formula.<br>Change the reference to a single cell, or use the reference with a worksheet function, such as =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Solid", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Strikeout", "SSE.Views.FormatRulesEditDlg.textSubscript": "Subscript", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Superscript", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Top borders", "SSE.Views.FormatRulesEditDlg.textUnderline": "Underline", "SSE.Views.FormatRulesEditDlg.tipBorders": "Obrobe", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Number format", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Računovodstvo", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Valuta", "SSE.Views.FormatRulesEditDlg.txtDate": "Datum", "SSE.Views.FormatRulesEditDlg.txtDateLong": "Long date", "SSE.Views.FormatRulesEditDlg.txtDateShort": "Short date", "SSE.Views.FormatRulesEditDlg.txtEmpty": "This field is required", "SSE.Views.FormatRulesEditDlg.txtFraction": "Frakcija", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Splošno", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "No icon", "SSE.Views.FormatRulesEditDlg.txtNumber": "Number", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Percentage", "SSE.Views.FormatRulesEditDlg.txtScientific": "Scientific", "SSE.Views.FormatRulesEditDlg.txtText": "Text", "SSE.Views.FormatRulesEditDlg.txtTime": "Time", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Uredi pravila formata", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "New formatting rule", "SSE.Views.FormatRulesManagerDlg.guestText": "Guest", "SSE.Views.FormatRulesManagerDlg.lockText": "Locked", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 std dev above average", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 std dev below average", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 std dev above average", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 std dev below average", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 std dev above average", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 std dev below average", "SSE.Views.FormatRulesManagerDlg.textAbove": "Nad p<PERSON>", "SSE.Views.FormatRulesManagerDlg.textApply": "Apply to", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Cell value begins with", "SSE.Views.FormatRulesManagerDlg.textBelow": "Below average", "SSE.Views.FormatRulesManagerDlg.textBetween": "is between {0} and {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Cell value", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Graded color scale", "SSE.Views.FormatRulesManagerDlg.textContains": "Cell value contains", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Cell contains a blank value", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Cell contains an error", "SSE.Views.FormatRulesManagerDlg.textDelete": "Izbriši", "SSE.Views.FormatRulesManagerDlg.textDown": "Move rule down", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Duplicate values", "SSE.Views.FormatRulesManagerDlg.textEdit": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEnds": "Cell value ends with", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Equal to or above average", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Equal to or below average", "SSE.Views.FormatRulesManagerDlg.textFormat": "Format", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Icon set", "SSE.Views.FormatRulesManagerDlg.textNew": "New", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "is not between {0} and {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Cell value does not contain", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Cell does not contain a blank value", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Cell does not contain an error", "SSE.Views.FormatRulesManagerDlg.textRules": "Rules", "SSE.Views.FormatRulesManagerDlg.textScope": "Show formatting rules for", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Select data", "SSE.Views.FormatRulesManagerDlg.textSelection": "Current selection", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "This pivot", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "This worksheet", "SSE.Views.FormatRulesManagerDlg.textThisTable": "This table", "SSE.Views.FormatRulesManagerDlg.textUnique": "Unique values", "SSE.Views.FormatRulesManagerDlg.textUp": "Move rule up", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Conditional formatting", "SSE.Views.FormatSettingsDialog.textCategory": "Kategorija", "SSE.Views.FormatSettingsDialog.textDecimal": "Decimal", "SSE.Views.FormatSettingsDialog.textFormat": "Format", "SSE.Views.FormatSettingsDialog.textLinked": "Linked to source", "SSE.Views.FormatSettingsDialog.textSeparator": "Use 1000 separator", "SSE.Views.FormatSettingsDialog.textSymbols": "Symbols", "SSE.Views.FormatSettingsDialog.textTitle": "Number format", "SSE.Views.FormatSettingsDialog.txtAccounting": "Računovodstvo", "SSE.Views.FormatSettingsDialog.txtAs10": "As tenths (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "As hundredths (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "As sixteenths (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "As halves (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "As fourths (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "As eighths (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Valuta", "SSE.Views.FormatSettingsDialog.txtCustom": "Po meri", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Please enter the custom number format carefully. Spreadsheet Editor does not check custom formats for errors that may affect the xlsx file.", "SSE.Views.FormatSettingsDialog.txtDate": "Datum", "SSE.Views.FormatSettingsDialog.txtFraction": "Frakcija", "SSE.Views.FormatSettingsDialog.txtGeneral": "Splošno", "SSE.Views.FormatSettingsDialog.txtNone": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNumber": "Števil<PERSON>", "SSE.Views.FormatSettingsDialog.txtPercentage": "Percentage", "SSE.Views.FormatSettingsDialog.txtSample": "Sample:", "SSE.Views.FormatSettingsDialog.txtScientific": "Scientific", "SSE.Views.FormatSettingsDialog.txtText": "Text", "SSE.Views.FormatSettingsDialog.txtTime": "Time", "SSE.Views.FormatSettingsDialog.txtUpto1": "Up to one digit (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Up to two digits (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Up to three digits (131/135)", "SSE.Views.FormulaDialog.sDescription": "Opis", "SSE.Views.FormulaDialog.textGroupDescription": "Izberi skupino funk<PERSON>", "SSE.Views.FormulaDialog.textListDescription": "Izberi funkcijo", "SSE.Views.FormulaDialog.txtRecommended": "Recommended", "SSE.Views.FormulaDialog.txtSearch": "Search", "SSE.Views.FormulaDialog.txtTitle": "Vstavi funkcijo", "SSE.Views.FormulaTab.capBtnRemoveArr": "Remove Arrows", "SSE.Views.FormulaTab.capBtnTraceDep": "Trace Dependents", "SSE.Views.FormulaTab.capBtnTracePrec": "Trace Precedents", "SSE.Views.FormulaTab.textAutomatic": "Samodejeno", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Calculate current sheet", "SSE.Views.FormulaTab.textCalculateWorkbook": "Calculate workbook", "SSE.Views.FormulaTab.textManual": "Manual", "SSE.Views.FormulaTab.tipCalculate": "Izračunaj", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Calculate the entire workbook", "SSE.Views.FormulaTab.tipRemoveArr": "Remove the arrows drawn by Trace Precedents or Trace Dependents", "SSE.Views.FormulaTab.tipShowFormulas": "Display the formula in each cell instead of the resulting value", "SSE.Views.FormulaTab.tipTraceDep": "Show arrows that indicate which cells are affected by the value of the selected cell", "SSE.Views.FormulaTab.tipTracePrec": "Show arrows that indicate which cells affect the value of the selected cell", "SSE.Views.FormulaTab.tipWatch": "Add cells to the Watch Window list", "SSE.Views.FormulaTab.txtAdditional": "Dodatno", "SSE.Views.FormulaTab.txtAutosum": "Autosum", "SSE.Views.FormulaTab.txtAutosumTip": "Summation", "SSE.Views.FormulaTab.txtCalculation": "Calculation", "SSE.Views.FormulaTab.txtFormula": "Funkcija", "SSE.Views.FormulaTab.txtFormulaTip": "Insert function", "SSE.Views.FormulaTab.txtMore": "More functions", "SSE.Views.FormulaTab.txtRecent": "Recently used", "SSE.Views.FormulaTab.txtRemDep": "Remove Dependents Arrows", "SSE.Views.FormulaTab.txtRemPrec": "Remove Precedents Arrows", "SSE.Views.FormulaTab.txtShowFormulas": "Show Formulas", "SSE.Views.FormulaTab.txtWatch": "Watch Window", "SSE.Views.FormulaWizard.textAny": "any", "SSE.Views.FormulaWizard.textArgument": "Argument", "SSE.Views.FormulaWizard.textFunction": "Funkcija", "SSE.Views.FormulaWizard.textFunctionRes": "Function result", "SSE.Views.FormulaWizard.textHelp": "Help on this function", "SSE.Views.FormulaWizard.textLogical": "logical", "SSE.Views.FormulaWizard.textNoArgs": "This function has no arguments", "SSE.Views.FormulaWizard.textNoArgsDesc": "this argument has no description", "SSE.Views.FormulaWizard.textNumber": "števil<PERSON>", "SSE.Views.FormulaWizard.textReadMore": "Read more", "SSE.Views.FormulaWizard.textRef": "reference", "SSE.Views.FormulaWizard.textText": "text", "SSE.Views.FormulaWizard.textTitle": "Function arguments", "SSE.Views.FormulaWizard.textValue": "Formula result", "SSE.Views.GoalSeekDlg.textChangingCell": "By changing cell", "SSE.Views.GoalSeekDlg.textDataRangeError": "The formula is missing a range", "SSE.Views.GoalSeekDlg.textMustContainFormula": "The cell must contain a formula", "SSE.Views.GoalSeekDlg.textMustContainValue": "Cell must contain a value", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Formula in cell must result in a number", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Reference must be to a single cell", "SSE.Views.GoalSeekDlg.textSelectData": "Select data", "SSE.Views.GoalSeekDlg.textSetCell": "Set cell", "SSE.Views.GoalSeekDlg.textTitle": "Goal seek", "SSE.Views.GoalSeekDlg.textToValue": "To value", "SSE.Views.GoalSeekDlg.txtEmpty": "This field is required", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.GoalSeekStatusDlg.textContinue": "Continue", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Current value:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Goal seeking with cell {0} found a solution.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Goal seeking with cell {0} may not have found a solution.", "SSE.Views.GoalSeekStatusDlg.textPause": "Pause", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Goal seeking with cell {0} on iteration #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "Step", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Target value:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Goal seek status", "SSE.Views.HeaderFooterDialog.textAlign": "Align with page margins", "SSE.Views.HeaderFooterDialog.textAll": "<PERSON><PERSON> strani", "SSE.Views.HeaderFooterDialog.textBold": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textCenter": "Na sredino", "SSE.Views.HeaderFooterDialog.textColor": "Text color", "SSE.Views.HeaderFooterDialog.textDate": "Datum", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Različna prva stran", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Different odd and even pages", "SSE.Views.HeaderFooterDialog.textEven": "Even page", "SSE.Views.HeaderFooterDialog.textFileName": "<PERSON><PERSON> da<PERSON>", "SSE.Views.HeaderFooterDialog.textFirst": "Prva stran", "SSE.Views.HeaderFooterDialog.textFooter": "Noga", "SSE.Views.HeaderFooterDialog.textHeader": "Glava", "SSE.Views.HeaderFooterDialog.textImage": "Picture", "SSE.Views.HeaderFooterDialog.textInsert": "Vstavi", "SSE.Views.HeaderFooterDialog.textItalic": "Ležeče", "SSE.Views.HeaderFooterDialog.textLeft": "Levo", "SSE.Views.HeaderFooterDialog.textMaxError": "The text string you entered is too long. Reduce the number of characters used.", "SSE.Views.HeaderFooterDialog.textNewColor": "Dodaj novo barvo po meri", "SSE.Views.HeaderFooterDialog.textOdd": "Odd page", "SSE.Views.HeaderFooterDialog.textPageCount": "Page count", "SSE.Views.HeaderFooterDialog.textPageNum": "Page number", "SSE.Views.HeaderFooterDialog.textPresets": "Presets", "SSE.Views.HeaderFooterDialog.textRight": "Right", "SSE.Views.HeaderFooterDialog.textScale": "Scale with document", "SSE.Views.HeaderFooterDialog.textSheet": "Sheet name", "SSE.Views.HeaderFooterDialog.textStrikeout": "Strikethrough", "SSE.Views.HeaderFooterDialog.textSubscript": "Subscript", "SSE.Views.HeaderFooterDialog.textSuperscript": "Superscript", "SSE.Views.HeaderFooterDialog.textTime": "Time", "SSE.Views.HeaderFooterDialog.textTitle": "Nastavitve glave/noge", "SSE.Views.HeaderFooterDialog.textUnderline": "Underline", "SSE.Views.HeaderFooterDialog.tipFontName": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.tipFontSize": "Velikost pisave", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Povezava k", "SSE.Views.HyperlinkSettingsDialog.strRange": "Razdalja", "SSE.Views.HyperlinkSettingsDialog.strSheet": "Stran", "SSE.Views.HyperlinkSettingsDialog.textCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Izbrano območje", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Napis vnesite tu", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Povezavo vnesi tu", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "<PERSON><PERSON> vnesite tu", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Zunanja povezava", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Get link", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Notranje območ<PERSON> pod<PERSON>kov", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "NAPAKA! Neveljaven razpon celic", "SSE.Views.HyperlinkSettingsDialog.textNames": "Defined names", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Select data", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Sheets", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Besedila <PERSON>a", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Nastavitve <PERSON>", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "To polje je obvezno", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "To polje mora biti URL v \"http://www.example.com\" formatu", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "This field is limited to 2083 characters", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "SSE.Views.ImageSettings.strTransparency": "Opacity", "SSE.Views.ImageSettings.textAdvanced": "Show advanced settings", "SSE.Views.ImageSettings.textCrop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFill": "Zapolni", "SSE.Views.ImageSettings.textCropFit": "Fit", "SSE.Views.ImageSettings.textCropToShape": "Crop to shape", "SSE.Views.ImageSettings.textEdit": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textEditObject": "Edit object", "SSE.Views.ImageSettings.textFlip": "<PERSON>z<PERSON><PERSON>", "SSE.Views.ImageSettings.textFromFile": "z datoteke", "SSE.Views.ImageSettings.textFromStorage": "<PERSON><PERSON> shrambe", "SSE.Views.ImageSettings.textFromUrl": "z URL", "SSE.Views.ImageSettings.textHeight": "Višina", "SSE.Views.ImageSettings.textHint270": "Rotate 90В° Counterclockwise", "SSE.Views.ImageSettings.textHint90": "Rotate 90В° Clockwise", "SSE.Views.ImageSettings.textHintFlipH": "Zrcali horizontalno", "SSE.Views.ImageSettings.textHintFlipV": "Zrcali vertikalno", "SSE.Views.ImageSettings.textInsert": "Zamenjaj sliko", "SSE.Views.ImageSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textOriginalSize": "Privzeta velikost", "SSE.Views.ImageSettings.textRecentlyUsed": "Recently used", "SSE.Views.ImageSettings.textResetCrop": "Reset crop", "SSE.Views.ImageSettings.textRotate90": "Rotate 90В°", "SSE.Views.ImageSettings.textRotation": "Rotation", "SSE.Views.ImageSettings.textSize": "Velikost", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Don't move or size with cells", "SSE.Views.ImageSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Opis", "SSE.Views.ImageSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart, or table.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Title", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Prezrcaljeno", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontally", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Move but don't size with cells", "SSE.Views.ImageSettingsAdvanced.textRotation": "Rotation", "SSE.Views.ImageSettingsAdvanced.textSnap": "Cell snapping", "SSE.Views.ImageSettingsAdvanced.textTitle": "Slika - napredne nastavitve", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Move and size with cells", "SSE.Views.ImageSettingsAdvanced.textVertically": "Vertically", "SSE.Views.ImportFromXmlDialog.textDestination": "Choose, where to place the data", "SSE.Views.ImportFromXmlDialog.textExist": "Existing worksheet", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ImportFromXmlDialog.textNew": "New worksheet", "SSE.Views.ImportFromXmlDialog.textSelectData": "Select data", "SSE.Views.ImportFromXmlDialog.textTitle": "Import data", "SSE.Views.ImportFromXmlDialog.txtEmpty": "This field is required", "SSE.Views.LeftMenu.ariaLeftMenu": "Left menu", "SSE.Views.LeftMenu.tipAbout": "O programu", "SSE.Views.LeftMenu.tipChat": "Pogovor", "SSE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipFile": "Datoteka", "SSE.Views.LeftMenu.tipPlugins": "Razširitve", "SSE.Views.LeftMenu.tipSearch": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSpellcheck": "Spell checking", "SSE.Views.LeftMenu.tipSupport": "Povratne informacije & Pomoč", "SSE.Views.LeftMenu.txtDeveloper": "RAZVIJALSKI NAČIN", "SSE.Views.LeftMenu.txtEditor": "Spreadsheet Editor", "SSE.Views.LeftMenu.txtLimit": "Limit access", "SSE.Views.LeftMenu.txtTrial": "TRIAL MODE", "SSE.Views.LeftMenu.txtTrialDev": "Trial Developer Mode", "SSE.Views.MacroDialog.textMacro": "Macro name", "SSE.Views.MacroDialog.textTitle": "Assign macro", "SSE.Views.MainSettingsPrint.okButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strBottom": "Dno", "SSE.Views.MainSettingsPrint.strLandscape": "Krajina", "SSE.Views.MainSettingsPrint.strLeft": "Levo", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPrintTitles": "Print titles", "SSE.Views.MainSettingsPrint.strRight": "Des<PERSON>", "SSE.Views.MainSettingsPrint.strTop": "Vrh", "SSE.Views.MainSettingsPrint.textActualSize": "Dejanska velikost", "SSE.Views.MainSettingsPrint.textCustom": "Po meri", "SSE.Views.MainSettingsPrint.textCustomOptions": "Custom options", "SSE.Views.MainSettingsPrint.textFitCols": "Fit all columns on one page", "SSE.Views.MainSettingsPrint.textFitPage": "Fit sheet on one page", "SSE.Views.MainSettingsPrint.textFitRows": "Fit all rows on one page", "SSE.Views.MainSettingsPrint.textPageOrientation": "Orientacija strani", "SSE.Views.MainSettingsPrint.textPageScaling": "Sc<PERSON>", "SSE.Views.MainSettingsPrint.textPageSize": "Velikost strani", "SSE.Views.MainSettingsPrint.textPrintGrid": "Natisni mrežne č<PERSON>", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Natisni naslove vrstic in stolpcev", "SSE.Views.MainSettingsPrint.textRepeat": "Repeat...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Repeat columns at left", "SSE.Views.MainSettingsPrint.textRepeatTop": "Repeat rows at top", "SSE.Views.MainSettingsPrint.textSettings": "Nastavitve za", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "The existing named ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Defined name", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Warning", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Workbook", "SSE.Views.NamedRangeEditDlg.textDataRange": "Data Range", "SSE.Views.NamedRangeEditDlg.textExistName": "ERROR! Range with such a name already exists", "SSE.Views.NamedRangeEditDlg.textInvalidName": "ERROR! Invalid range name", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ERROR! Invalid cell range", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ERROR! This element is being edited by another user.", "SSE.Views.NamedRangeEditDlg.textName": "Name", "SSE.Views.NamedRangeEditDlg.textReservedName": "The name you are trying to use is already referenced in cell formulas. Please use some other name.", "SSE.Views.NamedRangeEditDlg.textScope": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textSelectData": "Select Data", "SSE.Views.NamedRangeEditDlg.txtEmpty": "This field is required", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Edit Name", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "New Name", "SSE.Views.NamedRangePasteDlg.textNames": "Named Ranges", "SSE.Views.NamedRangePasteDlg.txtTitle": "Paste Name", "SSE.Views.NameManagerDlg.closeButtonText": "Close", "SSE.Views.NameManagerDlg.guestText": "Guest", "SSE.Views.NameManagerDlg.lockText": "Locked", "SSE.Views.NameManagerDlg.textDataRange": "Data Range", "SSE.Views.NameManagerDlg.textDelete": "Delete", "SSE.Views.NameManagerDlg.textEdit": "Edit", "SSE.Views.NameManagerDlg.textEmpty": "No named ranges have been created yet.<br>Create at least one named range and it will appear in this field.", "SSE.Views.NameManagerDlg.textFilter": "Filter", "SSE.Views.NameManagerDlg.textFilterAll": "All", "SSE.Views.NameManagerDlg.textFilterDefNames": "Defined names", "SSE.Views.NameManagerDlg.textFilterSheet": "Names Scoped to Sheet", "SSE.Views.NameManagerDlg.textFilterTableNames": "Table names", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Names Scoped to Workbook", "SSE.Views.NameManagerDlg.textNew": "New", "SSE.Views.NameManagerDlg.textnoNames": "No named ranges matching your filter could be found.", "SSE.Views.NameManagerDlg.textRanges": "Named Ranges", "SSE.Views.NameManagerDlg.textScope": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textWorkbook": "Workbook", "SSE.Views.NameManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.NameManagerDlg.txtTitle": "Name Manager", "SSE.Views.NameManagerDlg.warnDelete": "Are you sure you want to delete the name {0}?", "SSE.Views.PageMarginsDialog.textBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textCenter": "Center on page", "SSE.Views.PageMarginsDialog.textHor": "Horizontally", "SSE.Views.PageMarginsDialog.textLeft": "Levo", "SSE.Views.PageMarginsDialog.textRight": "Right", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "Top", "SSE.Views.PageMarginsDialog.textVert": "Vertically", "SSE.Views.PageMarginsDialog.textWarning": "Warning", "SSE.Views.PageMarginsDialog.warnCheckMargings": "Margins are incorrect", "SSE.Views.ParagraphSettings.strLineHeight": "Razmik med vrsticami", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Razmik", "SSE.Views.ParagraphSettings.strSpacingAfter": "po", "SSE.Views.ParagraphSettings.strSpacingBefore": "Pred", "SSE.Views.ParagraphSettings.textAdvanced": "Prikaži napredne nastavitve", "SSE.Views.ParagraphSettings.textAt": "na", "SSE.Views.ParagraphSettings.textAtLeast": "Vsaj", "SSE.Views.ParagraphSettings.textAuto": "Večkratno", "SSE.Views.ParagraphSettings.textExact": "Točno", "SSE.Views.ParagraphSettings.txtAutoText": "Samodejno", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Določeni zavihki se bodo pojavili v tem polju", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Vse z veliko", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Dvojno pre<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Indents", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Levo", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Line spacing", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Des<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Po", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Pred", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Special", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Od", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Zamiki & Postavitev", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "mali p<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Spacing", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Prečrtano", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Zavihek", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Multiple", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Razmak znaka", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Prevzeti zavihek", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Učinki", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Exactly", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Prva vrstica", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Hanging", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Justified", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(nič)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Odstrani", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Odstrani vse", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Določite", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Center", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Levo", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Položaj zavihka", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Des<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Odstavek - Napredne nastavitve", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Samodejno", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Delete", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "Duplicate", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Edit", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Formula", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Items Name", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "New", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Calculated Items in", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "enako", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "does not end with", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "vsebuje", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "ne vseb<PERSON>je", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "med", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "not between", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "does not equal", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "is greater than", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "is greater than or equal to", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "is less than", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "is less than or equal to", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "se začne z", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "does not begin with", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "se konča z", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Show items for which the label:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Show items for which:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Use ? to present any single character", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Use * to present any series of character", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "in", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Label filter", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Value filter", "SSE.Views.PivotGroupDialog.textAuto": "Samodejno", "SSE.Views.PivotGroupDialog.textBy": "Od", "SSE.Views.PivotGroupDialog.textDays": "Dnevi", "SSE.Views.PivotGroupDialog.textEnd": "Ending at", "SSE.Views.PivotGroupDialog.textError": "This field must be a numeric value", "SSE.Views.PivotGroupDialog.textGreaterError": "The end number must be greater than the start number", "SSE.Views.PivotGroupDialog.textHour": "Hours", "SSE.Views.PivotGroupDialog.textMin": "Minutes", "SSE.Views.PivotGroupDialog.textMonth": "Months", "SSE.Views.PivotGroupDialog.textNumDays": "Number of days", "SSE.Views.PivotGroupDialog.textQuart": "Quarters", "SSE.Views.PivotGroupDialog.textSec": "Seconds", "SSE.Views.PivotGroupDialog.textStart": "Starting at", "SSE.Views.PivotGroupDialog.textYear": "Years", "SSE.Views.PivotGroupDialog.txtTitle": "Grouping", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "You can use Calculated Items for basic calculations between different items within a single field", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Insert into formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "<PERSON><PERSON>", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Item name", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Items", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Read more", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Insert Calculated Item in", "SSE.Views.PivotSettings.textAdvanced": "Show advanced settings", "SSE.Views.PivotSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textFields": "Select fields", "SSE.Views.PivotSettings.textFilters": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textRows": "Rows", "SSE.Views.PivotSettings.textValues": "Values", "SSE.Views.PivotSettings.txtAddColumn": "Add to columns", "SSE.Views.PivotSettings.txtAddFilter": "Dodaj v filtre", "SSE.Views.PivotSettings.txtAddRow": "Add to rows", "SSE.Views.PivotSettings.txtAddValues": "Add to values", "SSE.Views.PivotSettings.txtFieldSettings": "Field settings", "SSE.Views.PivotSettings.txtMoveBegin": "Move to beginning", "SSE.Views.PivotSettings.txtMoveColumn": "Move to columns", "SSE.Views.PivotSettings.txtMoveDown": "Move down", "SSE.Views.PivotSettings.txtMoveEnd": "Move to end", "SSE.Views.PivotSettings.txtMoveFilter": "Move to filters", "SSE.Views.PivotSettings.txtMoveRow": "Move to rows", "SSE.Views.PivotSettings.txtMoveUp": "Move up", "SSE.Views.PivotSettings.txtMoveValues": "Move to values", "SSE.Views.PivotSettings.txtRemove": "Remove field", "SSE.Views.PivotSettingsAdvanced.strLayout": "Name and layout", "SSE.Views.PivotSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Opis", "SSE.Views.PivotSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart or table.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Title", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Autofit column widths on update", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Data range", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Data source", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Display fields in report filter area", "SSE.Views.PivotSettingsAdvanced.textDown": "Down, then over", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Grand totals", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Field headers", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.PivotSettingsAdvanced.textOver": "Over, then down", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Select data", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Show for columns", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Show field headers for rows and columns", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Show for rows", "SSE.Views.PivotSettingsAdvanced.textTitle": "Pivot Table - Advanced settings", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Report filter fields per column", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Report filter fields per row", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "This field is required", "SSE.Views.PivotSettingsAdvanced.txtName": "Ime", "SSE.Views.PivotShowDetailDialog.textDescription": "Choose the field containing the detail you want to show:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Show Detail", "SSE.Views.PivotTable.capBlankRows": "Blank Rows", "SSE.Views.PivotTable.capGrandTotals": "Grand Totals", "SSE.Views.PivotTable.capLayout": "Report Layout", "SSE.Views.PivotTable.capSubtotals": "Subtotals", "SSE.Views.PivotTable.mniBottomSubtotals": "Show all subtotals at bottom of group", "SSE.Views.PivotTable.mniInsertBlankLine": "Insert blank line after each item", "SSE.Views.PivotTable.mniLayoutCompact": "Show in compact form", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Don't repeat all item labels", "SSE.Views.PivotTable.mniLayoutOutline": "Show in outline form", "SSE.Views.PivotTable.mniLayoutRepeat": "Repeat all item labels", "SSE.Views.PivotTable.mniLayoutTabular": "Show in tabular form", "SSE.Views.PivotTable.mniNoSubtotals": "Don't show subtotals", "SSE.Views.PivotTable.mniOffTotals": "Off for rows and columns", "SSE.Views.PivotTable.mniOnColumnsTotals": "On for columns only", "SSE.Views.PivotTable.mniOnRowsTotals": "On for rows only", "SSE.Views.PivotTable.mniOnTotals": "On for rows and columns", "SSE.Views.PivotTable.mniRemoveBlankLine": "Remove blank line after each item", "SSE.Views.PivotTable.mniTopSubtotals": "Show all subtotals at top of group", "SSE.Views.PivotTable.textColBanded": "Banded Columns", "SSE.Views.PivotTable.textColHeader": "Glava <PERSON>ca", "SSE.Views.PivotTable.textRowBanded": "Banded Rows", "SSE.Views.PivotTable.textRowHeader": "Row Headers", "SSE.Views.PivotTable.tipCalculatedItems": "Calculated items", "SSE.Views.PivotTable.tipCreatePivot": "Insert Pivot Table", "SSE.Views.PivotTable.tipGrandTotals": "Show or hide grand totals", "SSE.Views.PivotTable.tipRefresh": "Update the information from data source", "SSE.Views.PivotTable.tipRefreshCurrent": "Update the information from data source for the current table", "SSE.Views.PivotTable.tipSelect": "Select entire pivot table", "SSE.Views.PivotTable.tipSubtotals": "Show or hide subtotals", "SSE.Views.PivotTable.txtCalculatedItems": "Calculated Items", "SSE.Views.PivotTable.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.PivotTable.txtCreate": "<PERSON><PERSON><PERSON> tabelo", "SSE.Views.PivotTable.txtExpandEntire": "Expand Entire Field", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Custom", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Dark", "SSE.Views.PivotTable.txtGroupPivot_Light": "Light", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Medium", "SSE.Views.PivotTable.txtPivotTable": "Pivot Table", "SSE.Views.PivotTable.txtRefresh": "Refresh", "SSE.Views.PivotTable.txtRefreshAll": "Refresh all", "SSE.Views.PivotTable.txtSelect": "Select", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Pivot Table Style Dark", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Pivot Table Style Light", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Pivot Table Style Medium", "SSE.Views.PrintSettings.btnDownload": "Save & Download", "SSE.Views.PrintSettings.btnExport": "Save & Export", "SSE.Views.PrintSettings.btnPrint": "<PERSON><PERSON><PERSON> & Natisni", "SSE.Views.PrintSettings.strBottom": "Dno", "SSE.Views.PrintSettings.strLandscape": "Krajina", "SSE.Views.PrintSettings.strLeft": "Levo", "SSE.Views.PrintSettings.strMargins": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPrintTitles": "Print titles", "SSE.Views.PrintSettings.strRight": "Des<PERSON>", "SSE.Views.PrintSettings.strShow": "Show", "SSE.Views.PrintSettings.strTop": "Vrh", "SSE.Views.PrintSettings.textActiveSheets": "Active sheets", "SSE.Views.PrintSettings.textActualSize": "Dejanska velikost", "SSE.Views.PrintSettings.textAllSheets": "Vsi listi", "SSE.Views.PrintSettings.textCurrentSheet": "Trenuten list", "SSE.Views.PrintSettings.textCustom": "Po meri", "SSE.Views.PrintSettings.textCustomOptions": "Custom options", "SSE.Views.PrintSettings.textFitCols": "Fit all columns on one page", "SSE.Views.PrintSettings.textFitPage": "Fit sheet on one page", "SSE.Views.PrintSettings.textFitRows": "Fit all rows on one page", "SSE.Views.PrintSettings.textHideDetails": "Skrij podrobnosti", "SSE.Views.PrintSettings.textIgnore": "Ignore print area", "SSE.Views.PrintSettings.textLayout": "Postavitev", "SSE.Views.PrintSettings.textMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintSettings.textMarginsNormal": "Normal", "SSE.Views.PrintSettings.textMarginsWide": "Wide", "SSE.Views.PrintSettings.textPageOrientation": "Orientacija strani", "SSE.Views.PrintSettings.textPages": "Pages:", "SSE.Views.PrintSettings.textPageScaling": "Sc<PERSON>", "SSE.Views.PrintSettings.textPageSize": "Velikost strani", "SSE.Views.PrintSettings.textPrintGrid": "Natisni mrežne č<PERSON>", "SSE.Views.PrintSettings.textPrintHeadings": "Natisni naslove vrstic in stolpcev", "SSE.Views.PrintSettings.textPrintRange": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textRange": "Range", "SSE.Views.PrintSettings.textRepeat": "Repeat...", "SSE.Views.PrintSettings.textRepeatLeft": "Repeat columns at left", "SSE.Views.PrintSettings.textRepeatTop": "Repeat rows at top", "SSE.Views.PrintSettings.textSelection": "Izbira", "SSE.Views.PrintSettings.textSettings": "Sheet settings", "SSE.Views.PrintSettings.textShowDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textShowGrid": "Show gridlines", "SSE.Views.PrintSettings.textShowHeadings": "Show rows and columns headings", "SSE.Views.PrintSettings.textTitle": "Natisni nastavitve", "SSE.Views.PrintSettings.textTitlePDF": "Nastavitev PDF dokumenta", "SSE.Views.PrintSettings.textTo": "to", "SSE.Views.PrintSettings.txtMarginsLast": "Last Custom", "SSE.Views.PrintTitlesDialog.textFirstCol": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.PrintTitlesDialog.textFirstRow": "Prva vrstica", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Frozen columns", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Frozen rows", "SSE.Views.PrintTitlesDialog.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.PrintTitlesDialog.textLeft": "Repeat columns at left", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Don't repeat", "SSE.Views.PrintTitlesDialog.textRepeat": "Repeat...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Select range", "SSE.Views.PrintTitlesDialog.textTitle": "Print titles", "SSE.Views.PrintTitlesDialog.textTop": "Repeat rows at top", "SSE.Views.PrintWithPreview.txtActiveSheets": "Active sheets", "SSE.Views.PrintWithPreview.txtActualSize": "Actual size", "SSE.Views.PrintWithPreview.txtAllSheets": "All sheets", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Apply to all sheets", "SSE.Views.PrintWithPreview.txtBothSides": "Print on both sides", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "Flip pages on long edge", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "Flip pages on short edge", "SSE.Views.PrintWithPreview.txtBottom": "Bottom", "SSE.Views.PrintWithPreview.txtCopies": "Copies", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Current sheet", "SSE.Views.PrintWithPreview.txtCustom": "Custom", "SSE.Views.PrintWithPreview.txtCustomOptions": "Custom options", "SSE.Views.PrintWithPreview.txtEmptyTable": "There is nothing to print because the table is empty", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "First page number:", "SSE.Views.PrintWithPreview.txtFitCols": "Fit All Columns on One Page", "SSE.Views.PrintWithPreview.txtFitPage": "<PERSON>t Sheet on One Page", "SSE.Views.PrintWithPreview.txtFitRows": "Fit All Rows on One Page", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Gridlines and headings", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Header/footer settings", "SSE.Views.PrintWithPreview.txtIgnore": "Ignore print area", "SSE.Views.PrintWithPreview.txtLandscape": "Landscape", "SSE.Views.PrintWithPreview.txtLeft": "Left", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMarginsLast": "Last Custom", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Normal", "SSE.Views.PrintWithPreview.txtMarginsWide": "Wide", "SSE.Views.PrintWithPreview.txtOf": "of {0}", "SSE.Views.PrintWithPreview.txtOneSide": "Print one sided", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "SSE.Views.PrintWithPreview.txtPage": "Page", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Page number invalid", "SSE.Views.PrintWithPreview.txtPageOrientation": "Page orientation", "SSE.Views.PrintWithPreview.txtPages": "Pages:", "SSE.Views.PrintWithPreview.txtPageSize": "Page size", "SSE.Views.PrintWithPreview.txtPortrait": "Portrait", "SSE.Views.PrintWithPreview.txtPrint": "Print", "SSE.Views.PrintWithPreview.txtPrintGrid": "Print gridlines", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Print row and column headings", "SSE.Views.PrintWithPreview.txtPrintRange": "Print range", "SSE.Views.PrintWithPreview.txtPrintSides": "Print sides", "SSE.Views.PrintWithPreview.txtPrintTitles": "Print titles", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Print to PDF", "SSE.Views.PrintWithPreview.txtRepeat": "Repeat...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Repeat columns at left", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Repeat rows at top", "SSE.Views.PrintWithPreview.txtRight": "Right", "SSE.Views.PrintWithPreview.txtSave": "Save", "SSE.Views.PrintWithPreview.txtScaling": "Sc<PERSON>", "SSE.Views.PrintWithPreview.txtSelection": "Selection", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Settings of sheet", "SSE.Views.PrintWithPreview.txtSheet": "Sheet: {0}", "SSE.Views.PrintWithPreview.txtTo": "to", "SSE.Views.PrintWithPreview.txtTop": "Top", "SSE.Views.ProtectDialog.textExistName": "ERROR! Range with such a title already exists", "SSE.Views.ProtectDialog.textInvalidName": "The range title must begin with a letter and may only contain letters, numbers, and spaces.", "SSE.Views.ProtectDialog.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.ProtectDialog.textSelectData": "Select data", "SSE.Views.ProtectDialog.txtAllow": "Allow all users of this sheet to", "SSE.Views.ProtectDialog.txtAllowDescription": "You can unlock specific ranges for editing.", "SSE.Views.ProtectDialog.txtAllowRanges": "Allow edit ranges", "SSE.Views.ProtectDialog.txtAutofilter": "Use AutoFilter", "SSE.Views.ProtectDialog.txtDelCols": "Delete columns", "SSE.Views.ProtectDialog.txtDelRows": "Delete rows", "SSE.Views.ProtectDialog.txtEmpty": "This field is required", "SSE.Views.ProtectDialog.txtFormatCells": "Format cells", "SSE.Views.ProtectDialog.txtFormatCols": "Format columns", "SSE.Views.ProtectDialog.txtFormatRows": "Format rows", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Confirmation password is not identical", "SSE.Views.ProtectDialog.txtInsCols": "Insert columns", "SSE.Views.ProtectDialog.txtInsHyper": "Insert hyperlink", "SSE.Views.ProtectDialog.txtInsRows": "Insert rows", "SSE.Views.ProtectDialog.txtObjs": "Edit objects", "SSE.Views.ProtectDialog.txtOptional": "optional", "SSE.Views.ProtectDialog.txtPassword": "Password", "SSE.Views.ProtectDialog.txtPivot": "Use PivotTable and PivotChart", "SSE.Views.ProtectDialog.txtProtect": "Zaščiti", "SSE.Views.ProtectDialog.txtRange": "Range", "SSE.Views.ProtectDialog.txtRangeName": "Title", "SSE.Views.ProtectDialog.txtRepeat": "Repeat password", "SSE.Views.ProtectDialog.txtScen": "Edit scenarios", "SSE.Views.ProtectDialog.txtSelLocked": "Select locked cells", "SSE.Views.ProtectDialog.txtSelUnLocked": "Select unlocked cells", "SSE.Views.ProtectDialog.txtSheetDescription": "Prevent unwanted changes from others by limiting their ability to edit.", "SSE.Views.ProtectDialog.txtSheetTitle": "Protect sheet", "SSE.Views.ProtectDialog.txtSort": "Sort", "SSE.Views.ProtectDialog.txtWarning": "Warning: If you lose or forget the password, it cannot be recovered. Please keep it in a safe place.", "SSE.Views.ProtectDialog.txtWBDescription": "To prevent other users from viewing hidden worksheets, adding, moving, deleting, or hiding worksheets and renaming worksheets, you can protect the structure of your workbook with a password.", "SSE.Views.ProtectDialog.txtWBTitle": "Protect workbook structure", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "Anonymous", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Anyone", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Edit", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Denied", "SSE.Views.ProtectedRangesEditDlg.textCanView": "View", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "The range title must begin with a letter and may only contain letters, numbers, and spaces.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Remove", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "Select data", "SSE.Views.ProtectedRangesEditDlg.textYou": "you", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Access to range", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "This field is required", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "Protect", "SSE.Views.ProtectedRangesEditDlg.txtRange": "Range", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "Title", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Only you can edit this range", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Start typing name or email", "SSE.Views.ProtectedRangesManagerDlg.guestText": "Guest", "SSE.Views.ProtectedRangesManagerDlg.lockText": "Locked", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "Delete", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "Edit", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "No protected ranges have been created yet.<br>Create at least one protected range and it will appear in this field.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "Filter", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "All", "SSE.Views.ProtectedRangesManagerDlg.textNew": "New", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Protect sheet", "SSE.Views.ProtectedRangesManagerDlg.textRange": "Range", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "You can restrict editing or viewing ranges to selected people.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "Title", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Access", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Denied", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "Edit", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "Edit range", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "New range", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Protected ranges", "SSE.Views.ProtectedRangesManagerDlg.txtView": "View", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Are you sure you want to delete the protected range {0}?<br>Anyone who has edit access to the spreadsheet will be able to edit content in the range.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Are you sure you want to delete the protected ranges?<br>Anyone who has edit access to the spreadsheet will be able to edit content in those ranges.", "SSE.Views.ProtectRangesDlg.guestText": "Guest", "SSE.Views.ProtectRangesDlg.lockText": "Locked", "SSE.Views.ProtectRangesDlg.textDelete": "Delete", "SSE.Views.ProtectRangesDlg.textEdit": "Edit", "SSE.Views.ProtectRangesDlg.textEmpty": "No ranges allowed for edit.", "SSE.Views.ProtectRangesDlg.textNew": "New", "SSE.Views.ProtectRangesDlg.textProtect": "Protect sheet", "SSE.Views.ProtectRangesDlg.textPwd": "Password", "SSE.Views.ProtectRangesDlg.textRange": "Range", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Ranges unlocked by a password when sheet is protected (this works only for locked cells)", "SSE.Views.ProtectRangesDlg.textTitle": "Title", "SSE.Views.ProtectRangesDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Edit range", "SSE.Views.ProtectRangesDlg.txtNewRange": "New range", "SSE.Views.ProtectRangesDlg.txtNo": "No", "SSE.Views.ProtectRangesDlg.txtTitle": "Allow users to edit ranges", "SSE.Views.ProtectRangesDlg.txtYes": "Yes", "SSE.Views.ProtectRangesDlg.warnDelete": "Are you sure you want to delete the name {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RemoveDuplicatesDialog.textDescription": "To delete duplicate values, select one or more columns that contain duplicates.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "My data has headers", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Select all", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Remove duplicates", "SSE.Views.RightMenu.ariaRightMenu": "Right menu", "SSE.Views.RightMenu.txtCellSettings": "Nastavitve celice", "SSE.Views.RightMenu.txtChartSettings": "Nastavitve grafa", "SSE.Views.RightMenu.txtImageSettings": "Nastavitve slike", "SSE.Views.RightMenu.txtParagraphSettings": "Nastavitve besedila", "SSE.Views.RightMenu.txtPivotSettings": "Pivot Table settings", "SSE.Views.RightMenu.txtSettings": "Pogoste nastavitve", "SSE.Views.RightMenu.txtShapeSettings": "Nastavitve oblike", "SSE.Views.RightMenu.txtSignatureSettings": "Signature settings", "SSE.Views.RightMenu.txtSlicerSettings": "Slicer settings", "SSE.Views.RightMenu.txtSparklineSettings": "Sparkline settings", "SSE.Views.RightMenu.txtTableSettings": "Table settings", "SSE.Views.RightMenu.txtTextArtSettings": "Text Art Settings", "SSE.Views.ScaleDialog.textAuto": "Samodejno", "SSE.Views.ScaleDialog.textError": "The entered value is incorrect.", "SSE.Views.ScaleDialog.textFewPages": "pages", "SSE.Views.ScaleDialog.textFitTo": "Fit to", "SSE.Views.ScaleDialog.textHeight": "Višina", "SSE.Views.ScaleDialog.textManyPages": "pages", "SSE.Views.ScaleDialog.textOnePage": "page", "SSE.Views.ScaleDialog.textScaleTo": "Scale to", "SSE.Views.ScaleDialog.textTitle": "Scale settings", "SSE.Views.ScaleDialog.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "Maksimalna vrednost za to polje je {0}", "SSE.Views.SetValueDialog.txtMinText": "Minimalna vrednost za to polje je {0}", "SSE.Views.ShapeSettings.strBackground": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strChange": "Spremeni obliko", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strForeground": "<PERSON><PERSON> o<PERSON>ja", "SSE.Views.ShapeSettings.strPattern": "Vzorec", "SSE.Views.ShapeSettings.strShadow": "Show shadow", "SSE.Views.ShapeSettings.strSize": "Velikost", "SSE.Views.ShapeSettings.strStroke": "Črta", "SSE.Views.ShapeSettings.strTransparency": "Motnost", "SSE.Views.ShapeSettings.strType": "Type", "SSE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "SSE.Views.ShapeSettings.textAdvanced": "Prikaži napredne nastavitve", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "Vnesena vrednost je nepravilna.<br>Prosim vnesite vrednost med 0 pt in 1584 pt.", "SSE.Views.ShapeSettings.textColor": "Barvna izpolnitev", "SSE.Views.ShapeSettings.textDirection": "Smer", "SSE.Views.ShapeSettings.textEditPoints": "Edit points", "SSE.Views.ShapeSettings.textEditShape": "Edit shape", "SSE.Views.ShapeSettings.textEmptyPattern": "Ni vzorcev", "SSE.Views.ShapeSettings.textEyedropper": "Eyedropper", "SSE.Views.ShapeSettings.textFlip": "<PERSON>z<PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromFile": "z datoteke", "SSE.Views.ShapeSettings.textFromStorage": "<PERSON><PERSON> shrambe", "SSE.Views.ShapeSettings.textFromUrl": "z URL", "SSE.Views.ShapeSettings.textGradient": "Gradient", "SSE.Views.ShapeSettings.textGradientFill": "Polnjenje <PERSON>", "SSE.Views.ShapeSettings.textHint270": "Rotate 90В° Counterclockwise", "SSE.Views.ShapeSettings.textHint90": "Rotate 90В° Clockwise", "SSE.Views.ShapeSettings.textHintFlipH": "Zrcali horizontalno", "SSE.Views.ShapeSettings.textHintFlipV": "Zrcali vertikalno", "SSE.Views.ShapeSettings.textImageTexture": "Slika ali tekstura", "SSE.Views.ShapeSettings.textLinear": "Linearna", "SSE.Views.ShapeSettings.textMoreColors": "More colors", "SSE.Views.ShapeSettings.textNoFill": "Ni pol<PERSON>la", "SSE.Views.ShapeSettings.textNoShadow": "No Shadow", "SSE.Views.ShapeSettings.textOriginalSize": "Originalna velikost", "SSE.Views.ShapeSettings.textPatternFill": "Vzorec", "SSE.Views.ShapeSettings.textPosition": "Position", "SSE.Views.ShapeSettings.textRadial": "Radial", "SSE.Views.ShapeSettings.textRecentlyUsed": "Recently used", "SSE.Views.ShapeSettings.textRotate90": "Rotate 90В°", "SSE.Views.ShapeSettings.textRotation": "Rotation", "SSE.Views.ShapeSettings.textSelectImage": "Select picture", "SSE.Views.ShapeSettings.textSelectTexture": "Izberi", "SSE.Views.ShapeSettings.textShadow": "Shadow", "SSE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStyle": "Slog", "SSE.Views.ShapeSettings.textTexture": "S teksture", "SSE.Views.ShapeSettings.textTile": "Ploščica", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Add gradient point", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Remove gradient point", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> papir", "SSE.Views.ShapeSettings.txtCanvas": "Platno", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGrain": "Zrno", "SSE.Views.ShapeSettings.txtGranite": "Granit", "SSE.Views.ShapeSettings.txtGreyPaper": "Siv papir", "SSE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtNoBorders": "<PERSON>", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "Les", "SSE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Oblazinjenje besedila", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Don't move or size with cells", "SSE.Views.ShapeSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Opis", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart, or table.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Title", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Puščice", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "AutoFit", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Začetna velikost", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Za<PERSON><PERSON><PERSON> stil", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Stožčasti", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Dno", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Vrsta pokrovčka", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Number of columns", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Končna velikost", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Končni slog", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Ploščat", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Prezrcaljeno", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Višina", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontally", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Vrsta pridruževanja", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Levo", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Slog vrste", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Move but don't size with cells", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Allow text to overflow shape", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Resize shape to fit text", "SSE.Views.ShapeSettingsAdvanced.textRight": "Des<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Rotation", "SSE.Views.ShapeSettingsAdvanced.textRound": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSize": "Velikost", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Cell snapping", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Spacing between columns", "SSE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Text box", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Oblika - Napredne nastavitve", "SSE.Views.ShapeSettingsAdvanced.textTop": "Vrh", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Move and size with cells", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Vertically", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Uteži & puščice", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Warning", "SSE.Views.SignatureSettings.strDelete": "Remove Signature", "SSE.Views.SignatureSettings.strDetails": "Signature details", "SSE.Views.SignatureSettings.strInvalid": "Invalid signatures", "SSE.Views.SignatureSettings.strRequested": "Requested signatures", "SSE.Views.SignatureSettings.strSetup": "Signature setup", "SSE.Views.SignatureSettings.strSign": "Sign", "SSE.Views.SignatureSettings.strSignature": "Signature", "SSE.Views.SignatureSettings.strSigner": "Signer", "SSE.Views.SignatureSettings.strValid": "Valid signatures", "SSE.Views.SignatureSettings.txtContinueEditing": "V<PERSON>no uredi", "SSE.Views.SignatureSettings.txtEditWarning": "Editing will remove signatures from the spreadsheet.<br>Continue?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Do you want to remove this signature?<br>It can't be undone.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "This spreadsheet needs to be signed.", "SSE.Views.SignatureSettings.txtSigned": "Valid signatures have been added to the spreadsheet. The spreadsheet is protected from editing.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Some of the digital signatures in spreadsheet are invalid or could not be verified. The spreadsheet is protected from editing.", "SSE.Views.SlicerAddDialog.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerAddDialog.txtTitle": "Insert slicers", "SSE.Views.SlicerSettings.strHideNoData": "Hide items with no data", "SSE.Views.SlicerSettings.strIndNoData": "Visually indicate items with no data", "SSE.Views.SlicerSettings.strShowDel": "Show items deleted from the data source", "SSE.Views.SlicerSettings.strShowNoData": "Show items with no data last", "SSE.Views.SlicerSettings.strSorting": "Sorting and filtering", "SSE.Views.SlicerSettings.textAdvanced": "Show advanced settings", "SSE.Views.SlicerSettings.textAsc": "Naraščaj<PERSON>če", "SSE.Views.SlicerSettings.textAZ": "A do Ž", "SSE.Views.SlicerSettings.textButtons": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textHeight": "Višina", "SSE.Views.SlicerSettings.textHor": "Horizontal", "SSE.Views.SlicerSettings.textKeepRatio": "Constant Proportions", "SSE.Views.SlicerSettings.textLargeSmall": "largest to smallest", "SSE.Views.SlicerSettings.textLock": "Disable resizing or moving", "SSE.Views.SlicerSettings.textNewOld": "newest to oldest", "SSE.Views.SlicerSettings.textOldNew": "oldest to newest", "SSE.Views.SlicerSettings.textPosition": "Position", "SSE.Views.SlicerSettings.textSize": "Size", "SSE.Views.SlicerSettings.textSmallLarge": "smallest to largest", "SSE.Views.SlicerSettings.textStyle": "Style", "SSE.Views.SlicerSettings.textVert": "Vertical", "SSE.Views.SlicerSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "Z to A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Višina", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Hide items with no data", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Visually indicate items with no data", "SSE.Views.SlicerSettingsAdvanced.strReferences": "References", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Show items deleted from the data source", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Pokaži nogo", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Show items with no data last", "SSE.Views.SlicerSettingsAdvanced.strSize": "Size", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Sorting & Filtering", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Style", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Style & Size", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Don't move or size with cells", "SSE.Views.SlicerSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Opis", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart or table.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Title", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Naraščaj<PERSON>če", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A do Ž", "SSE.Views.SlicerSettingsAdvanced.textDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Name to use in formulas", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Glava", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Constant proportions", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "largest to smallest", "SSE.Views.SlicerSettingsAdvanced.textName": "Ime", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "newest to oldest", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "oldest to newest", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Move but don't size with cells", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "smallest to largest", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Cell snapping", "SSE.Views.SlicerSettingsAdvanced.textSort": "Sort", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Source name", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Slicer - Advanced settings", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Move and size with cells", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z to A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "This field is required", "SSE.Views.SortDialog.errorEmpty": "All sort criteria must have a column or row specified.", "SSE.Views.SortDialog.errorMoreOneCol": "More than one column is selected.", "SSE.Views.SortDialog.errorMoreOneRow": "More than one row is selected.", "SSE.Views.SortDialog.errorNotOriginalCol": "The column you selected is not in the original selected range.", "SSE.Views.SortDialog.errorNotOriginalRow": "The row you selected is not in the original selected range.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 is being sorted by the same color more than once.<br>Delete the duplicate sort criteria and try again.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 is being sorted by values more than once.<br>Delete the duplicate sort criteria and try again.", "SSE.Views.SortDialog.textAsc": "Naraščaj<PERSON>če", "SSE.Views.SortDialog.textAuto": "Samodejeno", "SSE.Views.SortDialog.textAZ": "A do Ž", "SSE.Views.SortDialog.textBelow": "Pod", "SSE.Views.SortDialog.textBtnCopy": "Copy", "SSE.Views.SortDialog.textBtnDelete": "Delete", "SSE.Views.SortDialog.textBtnNew": "New", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON> celice", "SSE.Views.SortDialog.textColumn": "Stolpec", "SSE.Views.SortDialog.textDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDown": "Move level down", "SSE.Views.SortDialog.textFontColor": "<PERSON><PERSON> pisave", "SSE.Views.SortDialog.textLeft": "Levo", "SSE.Views.SortDialog.textLevels": "Levels", "SSE.Views.SortDialog.textMoreCols": "(<PERSON><PERSON><PERSON> sto<PERSON>v ...)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON><PERSON> v<PERSON> ...)", "SSE.Views.SortDialog.textNone": "<PERSON><PERSON>", "SSE.Views.SortDialog.textOptions": "Options", "SSE.Views.SortDialog.textOrder": "Order", "SSE.Views.SortDialog.textRight": "Right", "SSE.Views.SortDialog.textRow": "Vrsta", "SSE.Views.SortDialog.textSort": "Sort on", "SSE.Views.SortDialog.textSortBy": "Sort by", "SSE.Views.SortDialog.textThenBy": "Then by", "SSE.Views.SortDialog.textTop": "Top", "SSE.Views.SortDialog.textUp": "Move level up", "SSE.Views.SortDialog.textValues": "Values", "SSE.Views.SortDialog.textZA": "Z to A", "SSE.Views.SortDialog.txtInvalidRange": "Invalid cells range.", "SSE.Views.SortDialog.txtTitle": "Sort", "SSE.Views.SortFilterDialog.textAsc": "Ascending (A to Z) by", "SSE.Views.SortFilterDialog.textDesc": "Descending (Z to A) by", "SSE.Views.SortFilterDialog.textNoSort": "No sort", "SSE.Views.SortFilterDialog.txtTitle": "Sort", "SSE.Views.SortFilterDialog.txtTitleValue": "Sort by value", "SSE.Views.SortOptionsDialog.textCase": "Case sensitive", "SSE.Views.SortOptionsDialog.textHeaders": "My data has headers", "SSE.Views.SortOptionsDialog.textLeftRight": "Sort left to right", "SSE.Views.SortOptionsDialog.textOrientation": "Orientation", "SSE.Views.SortOptionsDialog.textTitle": "Sort options", "SSE.Views.SortOptionsDialog.textTopBottom": "Sort top to bottom", "SSE.Views.SpecialPasteDialog.textAdd": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textAll": "Vse", "SSE.Views.SpecialPasteDialog.textBlanks": "Skip blanks", "SSE.Views.SpecialPasteDialog.textColWidth": "Column widths", "SSE.Views.SpecialPasteDialog.textComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textDiv": "Divide", "SSE.Views.SpecialPasteDialog.textFFormat": "Formulas & formatting", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formulas & number formats", "SSE.Views.SpecialPasteDialog.textFormats": "Formati", "SSE.Views.SpecialPasteDialog.textFormulas": "Formule", "SSE.Views.SpecialPasteDialog.textFWidth": "Formulas & column widths", "SSE.Views.SpecialPasteDialog.textMult": "Multiply", "SSE.Views.SpecialPasteDialog.textNone": "<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textOperation": "Operation", "SSE.Views.SpecialPasteDialog.textPaste": "P<PERSON>ep<PERSON>", "SSE.Views.SpecialPasteDialog.textSub": "Subtract", "SSE.Views.SpecialPasteDialog.textTitle": "Paste special", "SSE.Views.SpecialPasteDialog.textTranspose": "Transpose", "SSE.Views.SpecialPasteDialog.textValues": "Values", "SSE.Views.SpecialPasteDialog.textVFormat": "Values & Formatting", "SSE.Views.SpecialPasteDialog.textVNFormat": "Values & Number formats", "SSE.Views.SpecialPasteDialog.textWBorders": "All except borders", "SSE.Views.Spellcheck.noSuggestions": "No spelling suggestions", "SSE.Views.Spellcheck.textChange": "Spremeni", "SSE.Views.Spellcheck.textChangeAll": "Spremeni vse", "SSE.Views.Spellcheck.textIgnore": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textIgnoreAll": "<PERSON><PERSON><PERSON><PERSON> vse", "SSE.Views.Spellcheck.txtAddToDictionary": "Dodaj v slovar", "SSE.Views.Spellcheck.txtClosePanel": "Close spelling", "SSE.Views.Spellcheck.txtComplete": "Spellcheck has been completed", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Jezik slovarja", "SSE.Views.Spellcheck.txtNextTip": "Go to the next word", "SSE.Views.Spellcheck.txtSpelling": "Spelling", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Premakni na konec)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "Create a copy", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(Create new spreadsheet)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Premakni pred stran", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Spreadsheet", "SSE.Views.Statusbar.filteredRecordsText": "{0} od {1} <PERSON><PERSON><PERSON><PERSON> filt<PERSON>h", "SSE.Views.Statusbar.filteredText": "Filter mode", "SSE.Views.Statusbar.itemAverage": "POVPREČNA", "SSE.Views.Statusbar.itemCount": "Štetje", "SSE.Views.Statusbar.itemDelete": "Izbriši", "SSE.Views.Statusbar.itemHidden": "Skrito", "SSE.Views.Statusbar.itemHide": "<PERSON>k<PERSON><PERSON>", "SSE.Views.Statusbar.itemInsert": "Vstavi", "SSE.Views.Statusbar.itemMaximum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMinimum": "Minimalno", "SSE.Views.Statusbar.itemMoveOrCopy": "Move or copy", "SSE.Views.Statusbar.itemProtect": "Zaščiti", "SSE.Views.Statusbar.itemRename": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemStatus": "Saving status", "SSE.Views.Statusbar.itemSum": "Sum", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON> z<PERSON>", "SSE.Views.Statusbar.itemUnProtect": "Unprotect", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Delovni zvezek s tem imenom že obstaja.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Ime strani ne more vsebovati naslednjih znakov: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "<PERSON><PERSON> strani", "SSE.Views.Statusbar.selectAllSheets": "Select All Sheets", "SSE.Views.Statusbar.sheetIndexText": "Sheet {0} of {1}", "SSE.Views.Statusbar.textAverage": "POVPREČNA", "SSE.Views.Statusbar.textCount": "ŠTETJE", "SSE.Views.Statusbar.textMax": "Max", "SSE.Views.Statusbar.textMin": "Min", "SSE.Views.Statusbar.textNewColor": "Dodaj novo barvo po meri", "SSE.Views.Statusbar.textNoColor": "Ni barve", "SSE.Views.Statusbar.textSum": "Vsota", "SSE.Views.Statusbar.tipAddTab": "<PERSON><PERSON><PERSON> list", "SSE.Views.Statusbar.tipFirst": "Premakni do prve strani", "SSE.Views.Statusbar.tipLast": "Premakni do zadnje strani", "SSE.Views.Statusbar.tipListOfSheets": "List of sheets", "SSE.Views.Statusbar.tipNext": "Pomakni seznam strani desno", "SSE.Views.Statusbar.tipPrev": "Pomakni seznam strani levo", "SSE.Views.Statusbar.tipZoomFactor": "Povečava", "SSE.Views.Statusbar.tipZoomIn": "Približaj", "SSE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.ungroupSheets": "Ungroup sheets", "SSE.Views.Statusbar.zoomText": "Povečava {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Dejanja ni bilo mogoče izbrati za izbrano območje celic.<br>Izberite uniformirano območje podatkov v ali izven razpredelnice in ponovno poskusite.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Operation could not be completed for the selected cell range.<br>Select a range so that the first table row was on the same row<br>and the resulting table overlapped the current one.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Operation could not be completed for the selected cell range.<br>Select a range which does not include other tables.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Multi-cell array formulas are not allowed in tables.", "SSE.Views.TableOptionsDialog.txtEmpty": "To polje je obvezno", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON><PERSON><PERSON> tabelo", "SSE.Views.TableOptionsDialog.txtInvalidRange": "NAPAKA! Neveljaven razpon celic", "SSE.Views.TableOptionsDialog.txtNote": "The headers must remain in the same row, and the resulting table range must overlap the original table range.", "SSE.Views.TableOptionsDialog.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.deleteColumnText": "Izbriši stolpec", "SSE.Views.TableSettings.deleteRowText": "Izbriši vrsto", "SSE.Views.TableSettings.deleteTableText": "Izbriši tabelo", "SSE.Views.TableSettings.insertColumnLeftText": "Insert column left", "SSE.Views.TableSettings.insertColumnRightText": "Insert column right", "SSE.Views.TableSettings.insertRowAboveText": "Insert row above", "SSE.Views.TableSettings.insertRowBelowText": "Insert row below", "SSE.Views.TableSettings.notcriticalErrorTitle": "Warning", "SSE.Views.TableSettings.selectColumnText": "Select entire column", "SSE.Views.TableSettings.selectDataText": "Select column data", "SSE.Views.TableSettings.selectRowText": "Select row", "SSE.Views.TableSettings.selectTableText": "Select table", "SSE.Views.TableSettings.textActions": "Table actions", "SSE.Views.TableSettings.textAdvanced": "Show advanced settings", "SSE.Views.TableSettings.textBanded": "Banded", "SSE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textConvertRange": "Convert to range", "SSE.Views.TableSettings.textEdit": "Rows & Columns", "SSE.Views.TableSettings.textEmptyTemplate": "No templates", "SSE.Views.TableSettings.textExistName": "ERROR! A range with such a name already exists", "SSE.Views.TableSettings.textFilter": "Filter button", "SSE.Views.TableSettings.textFirst": "Prvi", "SSE.Views.TableSettings.textHeader": "Glava", "SSE.Views.TableSettings.textInvalidName": "ERROR! Invalid table name", "SSE.Views.TableSettings.textIsLocked": "This element is being edited by another user.", "SSE.Views.TableSettings.textLast": "Last", "SSE.Views.TableSettings.textLongOperation": "Long operation", "SSE.Views.TableSettings.textPivot": "Insert pivot table", "SSE.Views.TableSettings.textRemDuplicates": "Remove duplicates", "SSE.Views.TableSettings.textReservedName": "The name you are trying to use is already referenced in cell formulas. Please use some other name.", "SSE.Views.TableSettings.textResize": "Resize table", "SSE.Views.TableSettings.textRows": "Rows", "SSE.Views.TableSettings.textSelectData": "Select data", "SSE.Views.TableSettings.textSlicer": "Insert slicer", "SSE.Views.TableSettings.textTableName": "Table name", "SSE.Views.TableSettings.textTemplate": "Select from template", "SSE.Views.TableSettings.textTotal": "Total", "SSE.Views.TableSettings.txtGroupTable_Custom": "Custom", "SSE.Views.TableSettings.txtGroupTable_Dark": "Dark", "SSE.Views.TableSettings.txtGroupTable_Light": "Light", "SSE.Views.TableSettings.txtGroupTable_Medium": "Medium", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Table style dark", "SSE.Views.TableSettings.txtTable_TableStyleLight": "Table style light", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "Table style medium", "SSE.Views.TableSettings.warnLongOperation": "The operation you are about to perform might take rather much time to complete.<br>Are you sure you want to continue?", "SSE.Views.TableSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Opis", "SSE.Views.TableSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart or table.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Title", "SSE.Views.TableSettingsAdvanced.textTitle": "Table - Advanced settings", "SSE.Views.TextArtSettings.strBackground": "Background color", "SSE.Views.TextArtSettings.strColor": "Color", "SSE.Views.TextArtSettings.strFill": "Fill", "SSE.Views.TextArtSettings.strForeground": "Foreground color", "SSE.Views.TextArtSettings.strPattern": "Pattern", "SSE.Views.TextArtSettings.strSize": "Size", "SSE.Views.TextArtSettings.strStroke": "Stroke", "SSE.Views.TextArtSettings.strTransparency": "Opacity", "SSE.Views.TextArtSettings.strType": "Type", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "The entered value is incorrect.<br>Please enter a value between 0 pt and 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Color Fill", "SSE.Views.TextArtSettings.textDirection": "Direction", "SSE.Views.TextArtSettings.textEmptyPattern": "No Pattern", "SSE.Views.TextArtSettings.textFromFile": "From File", "SSE.Views.TextArtSettings.textFromUrl": "From URL", "SSE.Views.TextArtSettings.textGradient": "Gradient", "SSE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON><PERSON>ll", "SSE.Views.TextArtSettings.textImageTexture": "Picture or Texture", "SSE.Views.TextArtSettings.textLinear": "Linear", "SSE.Views.TextArtSettings.textNoFill": "No Fill", "SSE.Views.TextArtSettings.textPatternFill": "Pattern", "SSE.Views.TextArtSettings.textPosition": "Position", "SSE.Views.TextArtSettings.textRadial": "Radial", "SSE.Views.TextArtSettings.textSelectTexture": "Select", "SSE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStyle": "Style", "SSE.Views.TextArtSettings.textTemplate": "Template", "SSE.Views.TextArtSettings.textTexture": "From Texture", "SSE.Views.TextArtSettings.textTile": "Tile", "SSE.Views.TextArtSettings.textTransform": "Transform", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Add gradient point", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Remove gradient point", "SSE.Views.TextArtSettings.txtBrownPaper": "Brown Paper", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "<PERSON> Fab<PERSON>", "SSE.Views.TextArtSettings.txtGrain": "Grain", "SSE.Views.TextArtSettings.txtGranite": "Granite", "SSE.Views.TextArtSettings.txtGreyPaper": "Gray Paper", "SSE.Views.TextArtSettings.txtKnit": "K<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "Leather", "SSE.Views.TextArtSettings.txtNoBorders": "No Line", "SSE.Views.TextArtSettings.txtPapyrus": "Papyrus", "SSE.Views.TextArtSettings.txtWood": "<PERSON>", "SSE.Views.Toolbar.capBtnAddComment": "<PERSON><PERSON><PERSON> k<PERSON>", "SSE.Views.Toolbar.capBtnColorSchemas": "Colors", "SSE.Views.Toolbar.capBtnComment": "Komentar", "SSE.Views.Toolbar.capBtnInsHeader": "Glava/noga", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON>licer", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Symbol", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageBreak": "Breaks", "SSE.Views.Toolbar.capBtnPageOrient": "Orientation", "SSE.Views.Toolbar.capBtnPageSize": "Size", "SSE.Views.Toolbar.capBtnPrintArea": "Print Area", "SSE.Views.Toolbar.capBtnPrintTitles": "Print Titles", "SSE.Views.Toolbar.capBtnScale": "Scale To Fit", "SSE.Views.Toolbar.capImgAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capImgBackward": "Send Backward", "SSE.Views.Toolbar.capImgForward": "Premakni naprej", "SSE.Views.Toolbar.capImgGroup": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertChart": "<PERSON>", "SSE.Views.Toolbar.capInsertChartRecommend": "Recommended Chart", "SSE.Views.Toolbar.capInsertEquation": "Enačba", "SSE.Views.Toolbar.capInsertHyperlink": "Hyperlink", "SSE.Views.Toolbar.capInsertImage": "Slika", "SSE.Views.Toolbar.capInsertShape": "Oblika", "SSE.Views.Toolbar.capInsertSpark": "Sparkline", "SSE.Views.Toolbar.capInsertTable": "Table", "SSE.Views.Toolbar.capInsertText": "Text Box", "SSE.Views.Toolbar.capInsertTextart": "Text Art", "SSE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniCapitalizeWords": "Capitalize Each Word", "SSE.Views.Toolbar.mniImageFromFile": "Slika z datoteke", "SSE.Views.Toolbar.mniImageFromStorage": "Slika iz oblaka", "SSE.Views.Toolbar.mniImageFromUrl": "Slika z URL", "SSE.Views.Toolbar.mniLowerCase": "lowercase", "SSE.Views.Toolbar.mniSentenceCase": "Sentence case.", "SSE.Views.Toolbar.mniToggleCase": "tOGGLE cASE", "SSE.Views.Toolbar.mniUpperCase": "UPPERCASE", "SSE.Views.Toolbar.textAddPrintArea": "Add to print area", "SSE.Views.Toolbar.textAlignBottom": "Poravnaj dno", "SSE.Views.Toolbar.textAlignCenter": "Poravnaj <PERSON>", "SSE.Views.Toolbar.textAlignJust": "Upravičena", "SSE.Views.Toolbar.textAlignLeft": "Poravnaj levo", "SSE.Views.Toolbar.textAlignMiddle": "Poravnaj sredino", "SSE.Views.Toolbar.textAlignRight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignTop": "Poravnaj vrh", "SSE.Views.Toolbar.textAllBorders": "Vse meje", "SSE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "SSE.Views.Toolbar.textAuto": "Samodejno", "SSE.Views.Toolbar.textAutoColor": "Samodejeno", "SSE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "SSE.Views.Toolbar.textBlackHeart": "Black Heart Suit", "SSE.Views.Toolbar.textBold": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON> o<PERSON>", "SSE.Views.Toolbar.textBordersStyle": "Slogi obrob", "SSE.Views.Toolbar.textBottom": "Bottom: ", "SSE.Views.Toolbar.textBottomBorders": "Meje na dnu", "SSE.Views.Toolbar.textBullet": "Bullet", "SSE.Views.Toolbar.textCellAlign": "Format cell alignment", "SSE.Views.Toolbar.textCenterBorders": "Notranje vertikalne meje", "SSE.Views.Toolbar.textClearPrintArea": "Clear print area", "SSE.Views.Toolbar.textClearRule": "Clear rules", "SSE.Views.Toolbar.textClockwise": "Kot v smeri urinega kazalca", "SSE.Views.Toolbar.textColorScales": "Color scales", "SSE.Views.Toolbar.textCopyright": "Copyright Sign", "SSE.Views.Toolbar.textCounterCw": "Kot v nasprotni smeri urinega kazalca", "SSE.Views.Toolbar.textCustom": "Custom", "SSE.Views.Toolbar.textDataBars": "Data Bars", "SSE.Views.Toolbar.textDegree": "Degree Sign", "SSE.Views.Toolbar.textDelLeft": "<PERSON><PERSON> poma<PERSON>ni levo", "SSE.Views.Toolbar.textDelPageBreak": "Remove page break", "SSE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "SSE.Views.Toolbar.textDelUp": "<PERSON><PERSON> pomakni navzgor", "SSE.Views.Toolbar.textDiagDownBorder": "Diagonalna spodnja meja", "SSE.Views.Toolbar.textDiagUpBorder": "Diagonalna zgornja meja", "SSE.Views.Toolbar.textDivision": "Division Sign", "SSE.Views.Toolbar.textDollar": "Dollar Sign", "SSE.Views.Toolbar.textDone": "Done", "SSE.Views.Toolbar.textDown": "Down", "SSE.Views.Toolbar.textEditVA": "Edit Visible Area", "SSE.Views.Toolbar.textEntireCol": "Cel stolpec", "SSE.Views.Toolbar.textEntireRow": "Cela <PERSON>rst<PERSON>", "SSE.Views.Toolbar.textEuro": "Euro Sign", "SSE.Views.Toolbar.textFewPages": "pages", "SSE.Views.Toolbar.textFillLeft": "Left", "SSE.Views.Toolbar.textFillRight": "Right", "SSE.Views.Toolbar.textFormatCellFill": "Format cell fill", "SSE.Views.Toolbar.textGreaterEqual": "Greater Than Or Equal To", "SSE.Views.Toolbar.textHeight": "Višina", "SSE.Views.Toolbar.textHideVA": "Hide Visible Area", "SSE.Views.Toolbar.textHorizontal": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textInfinity": "Infinity", "SSE.Views.Toolbar.textInsDown": "<PERSON><PERSON> premakni navzdol", "SSE.Views.Toolbar.textInsideBorders": "Vstavi meje", "SSE.Views.Toolbar.textInsPageBreak": "Insert page break", "SSE.Views.Toolbar.textInsRight": "<PERSON><PERSON> p<PERSON>", "SSE.Views.Toolbar.textItalic": "<PERSON><PERSON>evno", "SSE.Views.Toolbar.textItems": "Items", "SSE.Views.Toolbar.textLandscape": "Landscape", "SSE.Views.Toolbar.textLeft": "Left: ", "SSE.Views.Toolbar.textLeftBorders": "Leve meje", "SSE.Views.Toolbar.textLessEqual": "Less Than Or Equal To", "SSE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "SSE.Views.Toolbar.textManageRule": "Manage rules", "SSE.Views.Toolbar.textManyPages": "pages", "SSE.Views.Toolbar.textMarginsLast": "Last Custom", "SSE.Views.Toolbar.textMarginsNarrow": "<PERSON>rrow", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "Wide", "SSE.Views.Toolbar.textMiddleBorders": "Not<PERSON>je <PERSON> meje", "SSE.Views.Toolbar.textMoreBorders": "More borders", "SSE.Views.Toolbar.textMoreFormats": "More formats", "SSE.Views.Toolbar.textMorePages": "More pages", "SSE.Views.Toolbar.textMoreSymbols": "More symbols", "SSE.Views.Toolbar.textNewColor": "Dodaj novo barvo po meri", "SSE.Views.Toolbar.textNewRule": "New rule", "SSE.Views.Toolbar.textNoBorders": "Ni mej", "SSE.Views.Toolbar.textNotEqualTo": "Not Equal To", "SSE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "SSE.Views.Toolbar.textOnePage": "page", "SSE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "SSE.Views.Toolbar.textOutBorders": "Zunanje meje", "SSE.Views.Toolbar.textPageMarginsCustom": "Robovi po meri", "SSE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "SSE.Views.Toolbar.textPortrait": "Portrait", "SSE.Views.Toolbar.textPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPrintGridlines": "Print Gridlines", "SSE.Views.Toolbar.textPrintHeadings": "Print Headings", "SSE.Views.Toolbar.textPrintOptions": "Natisni nastavitve", "SSE.Views.Toolbar.textRegistered": "Registered Sign", "SSE.Views.Toolbar.textResetPageBreak": "Reset all page breaks", "SSE.Views.Toolbar.textRight": "Right: ", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON> meje", "SSE.Views.Toolbar.textRotateDown": "<PERSON><PERSON><PERSON> dol", "SSE.Views.Toolbar.textRotateUp": "<PERSON><PERSON><PERSON> gor", "SSE.Views.Toolbar.textRtlSheet": "Sheet Right-to-left", "SSE.Views.Toolbar.textScale": "Scale", "SSE.Views.Toolbar.textScaleCustom": "Po meri", "SSE.Views.Toolbar.textSection": "Section Sign", "SSE.Views.Toolbar.textSelection": "From current selection", "SSE.Views.Toolbar.textSeries": "Series", "SSE.Views.Toolbar.textSetPrintArea": "Set print area", "SSE.Views.Toolbar.textShapesCombine": "Combine", "SSE.Views.Toolbar.textShapesFragment": "Fragment", "SSE.Views.Toolbar.textShapesIntersect": "Intersect", "SSE.Views.Toolbar.textShapesSubstract": "Subtract", "SSE.Views.Toolbar.textShapesUnion": "Union", "SSE.Views.Toolbar.textShowVA": "Show Visible Area", "SSE.Views.Toolbar.textSmile": "White Smiling Face", "SSE.Views.Toolbar.textSquareRoot": "Square Root", "SSE.Views.Toolbar.textStrikeout": "Strikethrough", "SSE.Views.Toolbar.textSubscript": "Subscript", "SSE.Views.Toolbar.textSubSuperscript": "Subscript/Superscript", "SSE.Views.Toolbar.textSuperscript": "Superscript", "SSE.Views.Toolbar.textTabCollaboration": "Skupinsko delo", "SSE.Views.Toolbar.textTabData": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabDraw": "Draw", "SSE.Views.Toolbar.textTabFile": "Datoteka", "SSE.Views.Toolbar.textTabFormula": "Formula", "SSE.Views.Toolbar.textTabHome": "Home", "SSE.Views.Toolbar.textTabInsert": "Vstavi", "SSE.Views.Toolbar.textTabLayout": "Layout", "SSE.Views.Toolbar.textTabProtect": "Protection", "SSE.Views.Toolbar.textTabView": "Pogled", "SSE.Views.Toolbar.textThisPivot": "From this pivot", "SSE.Views.Toolbar.textThisSheet": "From this worksheet", "SSE.Views.Toolbar.textThisTable": "From this table", "SSE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTop": "Top: ", "SSE.Views.Toolbar.textTopBorders": "Vrhnje meje", "SSE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "SSE.Views.Toolbar.textUnderline": "Podčrtaj", "SSE.Views.Toolbar.textUp": "Up", "SSE.Views.Toolbar.textVertical": "Vertical text", "SSE.Views.Toolbar.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textYen": "Yen Sign", "SSE.Views.Toolbar.textZoom": "Povečava", "SSE.Views.Toolbar.tipAlignBottom": "Poravnaj dno", "SSE.Views.Toolbar.tipAlignCenter": "Poravnaj <PERSON>", "SSE.Views.Toolbar.tipAlignJust": "Upravičena", "SSE.Views.Toolbar.tipAlignLeft": "Poravnaj levo", "SSE.Views.Toolbar.tipAlignMiddle": "Poravnaj sredino", "SSE.Views.Toolbar.tipAlignRight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignTop": "Poravnaj vrh", "SSE.Views.Toolbar.tipAutofilter": "Razvrsti in filtriraj", "SSE.Views.Toolbar.tipBack": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipCellStyle": "Slog celice", "SSE.Views.Toolbar.tipChangeCase": "Change case", "SSE.Views.Toolbar.tipChangeChart": "Change chart type", "SSE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipColorSchemas": "Spremeni barv<PERSON> shemo", "SSE.Views.Toolbar.tipCondFormat": "Conditional formatting", "SSE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON> slog", "SSE.Views.Toolbar.tipCut": "Cut", "SSE.Views.Toolbar.tipDecDecimal": "Zmanjšaj decimal", "SSE.Views.Toolbar.tipDecFont": "Zmanjšaj velikost pisave", "SSE.Views.Toolbar.tipDeleteOpt": "Izbriši celice", "SSE.Views.Toolbar.tipDigStyleAccounting": "Slog računovodstva", "SSE.Views.Toolbar.tipDigStyleComma": "Comma style", "SSE.Views.Toolbar.tipDigStyleCurrency": "Slog valute", "SSE.Views.Toolbar.tipDigStylePercent": "Slog odstotkov", "SSE.Views.Toolbar.tipEditChart": "<PERSON>redi gra<PERSON>", "SSE.Views.Toolbar.tipEditChartData": "Select data", "SSE.Views.Toolbar.tipEditChartType": "Change chart type", "SSE.Views.Toolbar.tipEditHeader": "Uredi glavo ali nogo", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON> pisave", "SSE.Views.Toolbar.tipFontName": "<PERSON><PERSON> pisave", "SSE.Views.Toolbar.tipFontSize": "Velikost pisave", "SSE.Views.Toolbar.tipHAlighOle": "Horizontal align", "SSE.Views.Toolbar.tipImgAlign": "Poravnaj predmete", "SSE.Views.Toolbar.tipImgGroup": "Group objects", "SSE.Views.Toolbar.tipIncDecimal": "Po<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipIncFont": "Prirastek velikosti pisave", "SSE.Views.Toolbar.tipInsertChart": "Vstavi grafikon", "SSE.Views.Toolbar.tipInsertChartRecommend": "Insert recommended chart", "SSE.Views.Toolbar.tipInsertChartSpark": "Insert chart", "SSE.Views.Toolbar.tipInsertEquation": "Insert equation", "SSE.Views.Toolbar.tipInsertHorizontalText": "Insert horizontal text box", "SSE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertImage": "Vstavi sliko", "SSE.Views.Toolbar.tipInsertOpt": "<PERSON><PERSON><PERSON> celice", "SSE.Views.Toolbar.tipInsertShape": "Vstavi obliko", "SSE.Views.Toolbar.tipInsertSlicer": "Insert slicer", "SSE.Views.Toolbar.tipInsertSmartArt": "Insert SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Insert sparkline", "SSE.Views.Toolbar.tipInsertSymbol": "Insert symbol", "SSE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON> tabelo", "SSE.Views.Toolbar.tipInsertText": "<PERSON><PERSON><PERSON> be<PERSON>", "SSE.Views.Toolbar.tipInsertTextart": "Insert Text Art", "SSE.Views.Toolbar.tipInsertVerticalText": "Insert vertical text box", "SSE.Views.Toolbar.tipMerge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipNone": "None", "SSE.Views.Toolbar.tipNumFormat": "Številčni format", "SSE.Views.Toolbar.tipPageBreak": "Add a break where you want the next page to begin in the printed copy", "SSE.Views.Toolbar.tipPageMargins": "Page margins", "SSE.Views.Toolbar.tipPageOrient": "Page orientation", "SSE.Views.Toolbar.tipPageSize": "Page size", "SSE.Views.Toolbar.tipPaste": "P<PERSON>ep<PERSON>", "SSE.Views.Toolbar.tipPrColor": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrintArea": "Print area", "SSE.Views.Toolbar.tipPrintQuick": "Quick print", "SSE.Views.Toolbar.tipPrintTitles": "Print titles", "SSE.Views.Toolbar.tipRedo": "Ponovite", "SSE.Views.Toolbar.tipReplace": "Replace", "SSE.Views.Toolbar.tipRtlSheet": "Switch the sheet direction so that the first column is on the right side", "SSE.Views.Toolbar.tipSave": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSaveCoauth": "<PERSON><PERSON>ni<PERSON> svoje sprem<PERSON>, da jih drugi uporabniki lahko vidijo.", "SSE.Views.Toolbar.tipScale": "Scale to fit", "SSE.Views.Toolbar.tipSelectAll": "Select all", "SSE.Views.Toolbar.tipSendBackward": "Send backward", "SSE.Views.Toolbar.tipSendForward": "Premakni naprej", "SSE.Views.Toolbar.tipShapesMerge": "Merge shapes", "SSE.Views.Toolbar.tipSynchronize": "Dokument je spremenil drug uporabnik. Prosim pritisnite za shranjevanje svojih sprememb in osvežitev posodobitev.", "SSE.Views.Toolbar.tipTextFormatting": "More text formatting tools", "SSE.Views.Toolbar.tipTextOrientation": "Orientacija", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipVAlighOle": "Vertical align", "SSE.Views.Toolbar.tipVisibleArea": "Visible area", "SSE.Views.Toolbar.tipWrap": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "SSE.Views.Toolbar.txtAccounting": "Računovodstvo", "SSE.Views.Toolbar.txtAdditional": "Do<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAscending": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAutosumTip": "Summation", "SSE.Views.Toolbar.txtCellStyle": "Cell Style", "SSE.Views.Toolbar.txtClearAll": "Vse", "SSE.Views.Toolbar.txtClearComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFilter": "Počisti filter", "SSE.Views.Toolbar.txtClearFormat": "Format", "SSE.Views.Toolbar.txtClearFormula": "Funkcija", "SSE.Views.Toolbar.txtClearHyper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearText": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCurrency": "Valuta", "SSE.Views.Toolbar.txtCustom": "Po meri", "SSE.Views.Toolbar.txtDate": "Datum", "SSE.Views.Toolbar.txtDateLong": "Long Date", "SSE.Views.Toolbar.txtDateShort": "Short Date", "SSE.Views.Toolbar.txtDateTime": "Datum & Ura", "SSE.Views.Toolbar.txtDescending": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDollar": "$ Dolar", "SSE.Views.Toolbar.txtEuro": "€ Evro", "SSE.Views.Toolbar.txtExp": "Eksponencialna", "SSE.Views.Toolbar.txtFillNum": "Fill", "SSE.Views.Toolbar.txtFilter": "Filter", "SSE.Views.Toolbar.txtFormula": "Vstavi funkcijo", "SSE.Views.Toolbar.txtFraction": "Frakcija", "SSE.Views.Toolbar.txtFranc": "CHF Švicarski frank", "SSE.Views.Toolbar.txtGeneral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtInteger": "Ce<PERSON>številč<PERSON>", "SSE.Views.Toolbar.txtManageRange": "Name manager", "SSE.Views.Toolbar.txtMergeAcross": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtMergeCells": "<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.Toolbar.txtMergeCenter": "Združi & Centriraj", "SSE.Views.Toolbar.txtNamedRange": "Named Ranges", "SSE.Views.Toolbar.txtNewRange": "Define Name", "SSE.Views.Toolbar.txtNoBorders": "Ni mej", "SSE.Views.Toolbar.txtNumber": "Števil<PERSON>", "SSE.Views.Toolbar.txtPasteRange": "Paste name", "SSE.Views.Toolbar.txtPercentage": "Odstotek", "SSE.Views.Toolbar.txtPound": "£ funt", "SSE.Views.Toolbar.txtRouble": "₽ <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScientific": "Znanstveni", "SSE.Views.Toolbar.txtSearch": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSort": "Razv<PERSON><PERSON>", "SSE.Views.Toolbar.txtSortAZ": "Razvrsti od najnižjega do najvišjega", "SSE.Views.Toolbar.txtSortZA": "Razvrsti od najvišjega do najnižjega", "SSE.Views.Toolbar.txtSpecial": "Poseben", "SSE.Views.Toolbar.txtTableTemplate": "Formatiraj kot predloga tabele", "SSE.Views.Toolbar.txtText": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtTime": "Čas", "SSE.Views.Toolbar.txtUnmerge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> celi<PERSON>", "SSE.Views.Toolbar.txtYen": "¥ Jen", "SSE.Views.Top10FilterDialog.textType": "Show", "SSE.Views.Top10FilterDialog.txtBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBy": "od", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtPercent": "Percent", "SSE.Views.Top10FilterDialog.txtSum": "Sum", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 AutoFilter", "SSE.Views.Top10FilterDialog.txtTop": "Top", "SSE.Views.Top10FilterDialog.txtValueTitle": "Top 10 filter", "SSE.Views.ValueFieldSettingsDialog.textNext": "(next)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(previous)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Value field settings", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "POVPREČNA", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Base field", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Base item", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 od %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Štetje", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Ime po meri", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Difference from", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Max", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "No calculation", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "% of", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "% difference from", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "% of column", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% of grand total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% of parent total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% of parent column total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% of parent row total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% running total in", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "% of row", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Product", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Rank smallest to largest", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Rank largest to smallest", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Running total in", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Show values as", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Source name:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Sum", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Summarize value field by", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.guestText": "Guest", "SSE.Views.ViewManagerDlg.lockText": "Locked", "SSE.Views.ViewManagerDlg.textDelete": "Izbriši", "SSE.Views.ViewManagerDlg.textDuplicate": "Podvoji", "SSE.Views.ViewManagerDlg.textEmpty": "No views have been created yet.", "SSE.Views.ViewManagerDlg.textGoTo": "Go to view", "SSE.Views.ViewManagerDlg.textLongName": "<PERSON><PERSON><PERSON> ime, ki ima manj kot 128 znakov", "SSE.Views.ViewManagerDlg.textNew": "Novo", "SSE.Views.ViewManagerDlg.textRename": "<PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textRenameError": "View name must not be empty.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Rename view", "SSE.Views.ViewManagerDlg.textViews": "Sheet views", "SSE.Views.ViewManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.ViewManagerDlg.txtTitle": "Sheet view manager", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Are you sure you want to delete this sheet view?", "SSE.Views.ViewManagerDlg.warnDeleteView": "You are trying to delete the currently enabled view '%1'.<br>Close this view and delete it?", "SSE.Views.ViewTab.capBtnFreeze": "Freeze Panes", "SSE.Views.ViewTab.capBtnSheetView": "Sheet View", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Always Show Toolbar", "SSE.Views.ViewTab.textClose": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Combine Sheet and Status Bars", "SSE.Views.ViewTab.textCreate": "Novo", "SSE.Views.ViewTab.textDefault": "Privzeto", "SSE.Views.ViewTab.textFill": "Fill", "SSE.Views.ViewTab.textFormula": "Formula Bar", "SSE.Views.ViewTab.textFreezeCol": "Freeze first column", "SSE.Views.ViewTab.textFreezeRow": "Freeze top row", "SSE.Views.ViewTab.textGridlines": "Gridlines", "SSE.Views.ViewTab.textHeadings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textInterfaceTheme": "Interface Theme", "SSE.Views.ViewTab.textLeftMenu": "Left Panel", "SSE.Views.ViewTab.textLine": "Line", "SSE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "View manager", "SSE.Views.ViewTab.textRightMenu": "Right Panel", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Show frozen panes shadow", "SSE.Views.ViewTab.textTabStyle": "Tab style", "SSE.Views.ViewTab.textUnFreeze": "Unfreeze panes", "SSE.Views.ViewTab.textZeros": "Show Zeros", "SSE.Views.ViewTab.textZoom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.tipClose": "Close sheet view", "SSE.Views.ViewTab.tipCreate": "Create sheet view", "SSE.Views.ViewTab.tipFreeze": "Freeze panes", "SSE.Views.ViewTab.tipInterfaceTheme": "Interface theme", "SSE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipSheetView": "Sheet view", "SSE.Views.ViewTab.tipViewNormal": "See your document in Normal view", "SSE.Views.ViewTab.tipViewPageBreak": "See where the page breaks will appear when your document is printed", "SSE.Views.ViewTab.txtViewNormal": "Normal", "SSE.Views.ViewTab.txtViewPageBreak": "Page Break Preview", "SSE.Views.WatchDialog.closeButtonText": "Close", "SSE.Views.WatchDialog.textAdd": "Add watch", "SSE.Views.WatchDialog.textBook": "Book", "SSE.Views.WatchDialog.textCell": "Cell", "SSE.Views.WatchDialog.textDelete": "Delete watch", "SSE.Views.WatchDialog.textDeleteAll": "Delete all", "SSE.Views.WatchDialog.textFormula": "Formula", "SSE.Views.WatchDialog.textName": "Name", "SSE.Views.WatchDialog.textSheet": "Sheet", "SSE.Views.WatchDialog.textValue": "Value", "SSE.Views.WatchDialog.txtTitle": "Watch window", "SSE.Views.WBProtection.hintAllowRanges": "Allow edit ranges", "SSE.Views.WBProtection.hintProtectRange": "Protect range", "SSE.Views.WBProtection.hintProtectSheet": "Protect sheet", "SSE.Views.WBProtection.hintProtectWB": "Protect workbook", "SSE.Views.WBProtection.txtAllowRanges": "Allow edit ranges", "SSE.Views.WBProtection.txtHiddenFormula": "Hidden Formulas", "SSE.Views.WBProtection.txtLockedCell": "Locked Cell", "SSE.Views.WBProtection.txtLockedShape": "<PERSON><PERSON><PERSON> Locked", "SSE.Views.WBProtection.txtLockedText": "Lock Text", "SSE.Views.WBProtection.txtProtectRange": "Protect Range", "SSE.Views.WBProtection.txtProtectSheet": "Protect Sheet", "SSE.Views.WBProtection.txtProtectWB": "Protect workbook", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Enter a password to unprotect sheet", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Unprotect sheet", "SSE.Views.WBProtection.txtWBUnlockDescription": "Enter a password to unprotect workbook", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}