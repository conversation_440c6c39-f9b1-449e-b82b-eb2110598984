{"cancelButtonText": "Lə<PERSON>v edin", "Common.Controllers.Chat.notcriticalErrorTitle": "Xəbərdarlıq", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.History.notcriticalErrorTitle": "Xəbərdarlıq", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "Sahə", "Common.define.chartData.textAreaStacked": "<PERSON>s<PERSON><PERSON><PERSON>i sahə", "Common.define.chartData.textAreaStackedPer": "100% Qruplaşmış Sahə", "Common.define.chartData.textBar": "Zolaq", "Common.define.chartData.textBarNormal": "Qruplaşdırılmış sütun", "Common.define.chartData.textBarNormal3d": "3-<PERSON> Qruplaşmış sütun", "Common.define.chartData.textBarNormal3dPerspective": "3-<PERSON>", "Common.define.chartData.textBarStacked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>tun", "Common.define.chartData.textBarStacked3d": "3-<PERSON> Nisbətli Sütun", "Common.define.chartData.textBarStackedPer": "100% Nisbətli sütun", "Common.define.chartData.textBarStackedPer3d": "3-D 100% Nisbətli s<PERSON>tun", "Common.define.chartData.textCharts": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textColumnSpark": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Nisbətli sahə-qruplaşmış sütun", "Common.define.chartData.textComboBarLine": "Qruplaşdırılm<PERSON>ş sütun - sətir", "Common.define.chartData.textComboBarLineSecondary": "İkinci oxda qruplaşmış sütun - sətir", "Common.define.chartData.textComboCustom": "Fərdi kombinasiya", "Common.define.chartData.textDoughnut": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "Qruplaşmış Zolaq", "Common.define.chartData.textHBarNormal3d": "3D Qruplaşmış Zolaq", "Common.define.chartData.textHBarStacked": "Nisbətli zolaq", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> Nisbətli Zolaq", "Common.define.chartData.textHBarStackedPer": "100% Nisbətli zolaq", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% Nisbətli zolaq", "Common.define.chartData.textLine": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textLine3d": "3-<PERSON> xətt", "Common.define.chartData.textLineMarker": "<PERSON><PERSON><PERSON><PERSON><PERSON> ilə Qrafik xətt", "Common.define.chartData.textLineSpark": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textLineStacked": "Nisbətli xətt", "Common.define.chartData.textLineStackedMarker": "<PERSON><PERSON><PERSON><PERSON>r ilə nisbətli xətt", "Common.define.chartData.textLineStackedPer": "100% Nisbətli xətt", "Common.define.chartData.textLineStackedPerMarker": "Markerlərlə 100% Nisbətli xətt", "Common.define.chartData.textPie": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textPie3d": "3-<PERSON> diaqramı", "Common.define.chartData.textPoint": "XY (Nöqtəvi)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Filled radar", "Common.define.chartData.textRadarMarker": "Radar with markers", "Common.define.chartData.textScatter": "Nöq<PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "<PERSON><PERSON><PERSON> xətlər ilə <PERSON>öq<PERSON>ə<PERSON>", "Common.define.chartData.textScatterLineMarker": "<PERSON><PERSON><PERSON> xətlər və markerlər ilə Nöqtəvi", "Common.define.chartData.textScatterSmooth": "<PERSON><PERSON> xətlər ilə <PERSON>öq<PERSON>ə<PERSON>", "Common.define.chartData.textScatterSmoothMarker": "<PERSON>ar xətlər və markerlər ilə Nöqtəvi", "Common.define.chartData.textSparks": "Sparklaynlar", "Common.define.chartData.textStock": "<PERSON>ond", "Common.define.chartData.textSurface": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textWinLossSpark": "Qələbə/məğlubiyyət", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Format təyin edil<PERSON>yib", "Common.define.conditionalData.text1Above": "1 standart sapma yuxarı", "Common.define.conditionalData.text1Below": "1 standart sapma aşağı", "Common.define.conditionalData.text2Above": "2 standart sapma yuxarı", "Common.define.conditionalData.text2Below": "2 standart sapma a<PERSON>ı", "Common.define.conditionalData.text3Above": "3 standart sapma yuxarı", "Common.define.conditionalData.text3Below": "3 standart sapma aşağı", "Common.define.conditionalData.textAbove": "Yuxarıda", "Common.define.conditionalData.textAverage": "Orta", "Common.define.conditionalData.textBegins": "<PERSON><PERSON>ə başlayır", "Common.define.conditionalData.textBelow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textBetween": "Arasında", "Common.define.conditionalData.textBlank": "Boş ", "Common.define.conditionalData.textBlanks": "Blankları ehtiva edir", "Common.define.conditionalData.textBottom": "Aşağı", "Common.define.conditionalData.textContains": "İbarətdir", "Common.define.conditionalData.textDataBar": "Məlumat zolağı", "Common.define.conditionalData.textDate": "<PERSON><PERSON>", "Common.define.conditionalData.textDuplicate": "Dublikat", "Common.define.conditionalData.textEnds": "<PERSON>lə başa çatır", "Common.define.conditionalData.textEqAbove": "<PERSON><PERSON><PERSON><PERSON>r və ya yuxarı", "Common.define.conditionalData.textEqBelow": "<PERSON><PERSON><PERSON><PERSON>r və ya aşağı", "Common.define.conditionalData.textEqual": "Bərabərdir", "Common.define.conditionalData.textError": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textErrors": "Xətaları ehtiva edir", "Common.define.conditionalData.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textGreater": "-də<PERSON>", "Common.define.conditionalData.textGreaterEq": "-dən bö<PERSON><PERSON>k və ya ona bərabər", "Common.define.conditionalData.textIconSets": "Piktoqram yı<PERSON>ı", "Common.define.conditionalData.textLast7days": "Son 7 gündə", "Common.define.conditionalData.textLastMonth": "Keçən ay", "Common.define.conditionalData.textLastWeek": "<PERSON><PERSON><PERSON>n həftə", "Common.define.conditionalData.textLess": "-dən az", "Common.define.conditionalData.textLessEq": "-dən az və ya bərabərdir", "Common.define.conditionalData.textNextMonth": "Növbəti ay", "Common.define.conditionalData.textNextWeek": "Növb<PERSON>ti həftə", "Common.define.conditionalData.textNotBetween": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textNotBlanks": "Boşluqları ehtiva etmir", "Common.define.conditionalData.textNotContains": "Ehtiva etmir", "Common.define.conditionalData.textNotEqual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textNotErrors": "Xətaları ehtiva etmir", "Common.define.conditionalData.textText": "Mətn", "Common.define.conditionalData.textThisMonth": "<PERSON>u ay", "Common.define.conditionalData.textThisWeek": "Bu həftə", "Common.define.conditionalData.textToday": "<PERSON><PERSON>n", "Common.define.conditionalData.textTomorrow": "Sabah", "Common.define.conditionalData.textTop": "Yuxarı", "Common.define.conditionalData.textUnique": "Unikal", "Common.define.conditionalData.textValue": "<PERSON><PERSON><PERSON><PERSON><PERSON> budur", "Common.define.conditionalData.textYesterday": "Dü<PERSON>ə<PERSON>", "Common.define.smartArt.textAccentedPicture": "Accented Picture", "Common.define.smartArt.textAccentProcess": "Accent Process", "Common.define.smartArt.textAlternatingFlow": "Alternating flow", "Common.define.smartArt.textAlternatingHexagons": "Alternating hexagons", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternating picture blocks", "Common.define.smartArt.textAlternatingPictureCircles": "Alternating picture circles", "Common.define.smartArt.textArchitectureLayout": "Architecture layout", "Common.define.smartArt.textArrowRibbon": "Arrow ribbon", "Common.define.smartArt.textAscendingPictureAccentProcess": "Ascending picture accent process", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Basic bending process", "Common.define.smartArt.textBasicBlockList": "Basic block list", "Common.define.smartArt.textBasicChevronProcess": "Basic chevron process", "Common.define.smartArt.textBasicCycle": "Basic cycle", "Common.define.smartArt.textBasicMatrix": "Basic matrix", "Common.define.smartArt.textBasicPie": "Basic Pie", "Common.define.smartArt.textBasicProcess": "Basic process", "Common.define.smartArt.textBasicPyramid": "Basic pyramid", "Common.define.smartArt.textBasicRadial": "Basic radial", "Common.define.smartArt.textBasicTarget": "Basic target", "Common.define.smartArt.textBasicTimeline": "Basic timeline", "Common.define.smartArt.textBasicVenn": "Basic Venn", "Common.define.smartArt.textBendingPictureAccentList": "Bending picture accent list", "Common.define.smartArt.textBendingPictureBlocks": "Bending picture blocks", "Common.define.smartArt.textBendingPictureCaption": "Bending picture caption", "Common.define.smartArt.textBendingPictureCaptionList": "Bending picture caption list", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Bending picture semi-transparent text", "Common.define.smartArt.textBlockCycle": "Block cycle", "Common.define.smartArt.textBubblePictureList": "Bubble picture list", "Common.define.smartArt.textCaptionedPictures": "Captioned pictures", "Common.define.smartArt.textChevronAccentProcess": "Chevron accent process", "Common.define.smartArt.textChevronList": "Chevron list", "Common.define.smartArt.textCircleAccentTimeline": "Circle accent timeline", "Common.define.smartArt.textCircleArrowProcess": "Circle arrow process", "Common.define.smartArt.textCirclePictureHierarchy": "Circle picture hierarchy", "Common.define.smartArt.textCircleProcess": "Circle process", "Common.define.smartArt.textCircleRelationship": "Circle relationship", "Common.define.smartArt.textCircularBendingProcess": "Circular bending process", "Common.define.smartArt.textCircularPictureCallout": "Circular picture callout", "Common.define.smartArt.textClosedChevronProcess": "Closed chevron process", "Common.define.smartArt.textContinuousArrowProcess": "Continuous arrow process", "Common.define.smartArt.textContinuousBlockProcess": "Continuous block process", "Common.define.smartArt.textContinuousCycle": "Continuous cycle", "Common.define.smartArt.textContinuousPictureList": "Continuous picture list", "Common.define.smartArt.textConvergingArrows": "Converging arrows", "Common.define.smartArt.textConvergingRadial": "Converging radial", "Common.define.smartArt.textConvergingText": "Converging text", "Common.define.smartArt.textCounterbalanceArrows": "Counterbalance arrows", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "Cycle matrix", "Common.define.smartArt.textDescendingBlockList": "Descending block list", "Common.define.smartArt.textDescendingProcess": "Descending process", "Common.define.smartArt.textDetailedProcess": "Detailed process", "Common.define.smartArt.textDivergingArrows": "Diverging arrows", "Common.define.smartArt.textDivergingRadial": "Diverging radial", "Common.define.smartArt.textEquation": "Equation", "Common.define.smartArt.textFramedTextPicture": "Framed text picture", "Common.define.smartArt.textFunnel": "Funnel", "Common.define.smartArt.textGear": "Gear", "Common.define.smartArt.textGridMatrix": "Grid matrix", "Common.define.smartArt.textGroupedList": "Grouped list", "Common.define.smartArt.textHalfCircleOrganizationChart": "Half circle organization chart", "Common.define.smartArt.textHexagonCluster": "Hexagon cluster", "Common.define.smartArt.textHexagonRadial": "Hexagon radial", "Common.define.smartArt.textHierarchy": "Hierarchy", "Common.define.smartArt.textHierarchyList": "Hierarchy list", "Common.define.smartArt.textHorizontalBulletList": "Horizontal bullet list", "Common.define.smartArt.textHorizontalHierarchy": "Horizontal hierarchy", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal labeled hierarchy", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontal multi-level hierarchy", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontal organization chart", "Common.define.smartArt.textHorizontalPictureList": "Horizontal picture list", "Common.define.smartArt.textIncreasingArrowProcess": "Increasing arrow process", "Common.define.smartArt.textIncreasingCircleProcess": "Increasing circle process", "Common.define.smartArt.textInterconnectedBlockProcess": "Interconnected block process", "Common.define.smartArt.textInterconnectedRings": "Interconnected rings", "Common.define.smartArt.textInvertedPyramid": "Inverted pyramid", "Common.define.smartArt.textLabeledHierarchy": "Labeled hierarchy", "Common.define.smartArt.textLinearVenn": "Linear Venn", "Common.define.smartArt.textLinedList": "Lined list", "Common.define.smartArt.textList": "List", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Multidirectional cycle", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Name and title organization chart", "Common.define.smartArt.textNestedTarget": "Nested target", "Common.define.smartArt.textNondirectionalCycle": "Nondirectional cycle", "Common.define.smartArt.textOpposingArrows": "Opposing arrows", "Common.define.smartArt.textOpposingIdeas": "Opposing ideas", "Common.define.smartArt.textOrganizationChart": "Organization chart", "Common.define.smartArt.textOther": "Other", "Common.define.smartArt.textPhasedProcess": "Phased process", "Common.define.smartArt.textPicture": "Picture", "Common.define.smartArt.textPictureAccentBlocks": "Picture accent blocks", "Common.define.smartArt.textPictureAccentList": "Picture accent list", "Common.define.smartArt.textPictureAccentProcess": "Picture accent process", "Common.define.smartArt.textPictureCaptionList": "Picture caption list", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "Picture grid", "Common.define.smartArt.textPictureLineup": "Picture lineup", "Common.define.smartArt.textPictureOrganizationChart": "Picture organization chart", "Common.define.smartArt.textPictureStrips": "Picture strips", "Common.define.smartArt.textPieProcess": "Pie process", "Common.define.smartArt.textPlusAndMinus": "Plus and minus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "Process arrows", "Common.define.smartArt.textProcessList": "Process list", "Common.define.smartArt.textPyramid": "Pyramid", "Common.define.smartArt.textPyramidList": "Pyramid list", "Common.define.smartArt.textRadialCluster": "Radial cluster", "Common.define.smartArt.textRadialCycle": "Radial cycle", "Common.define.smartArt.textRadialList": "Radial list", "Common.define.smartArt.textRadialPictureList": "Radial picture list", "Common.define.smartArt.textRadialVenn": "Radial Venn", "Common.define.smartArt.textRandomToResultProcess": "Random to result process", "Common.define.smartArt.textRelationship": "Relationship", "Common.define.smartArt.textRepeatingBendingProcess": "Repeating bending process", "Common.define.smartArt.textReverseList": "Reverse list", "Common.define.smartArt.textSegmentedCycle": "Segmented cycle", "Common.define.smartArt.textSegmentedProcess": "Segmented process", "Common.define.smartArt.textSegmentedPyramid": "Segmented pyramid", "Common.define.smartArt.textSnapshotPictureList": "Snapshot picture list", "Common.define.smartArt.textSpiralPicture": "Spiral picture", "Common.define.smartArt.textSquareAccentList": "Square accent list", "Common.define.smartArt.textStackedList": "Stacked list", "Common.define.smartArt.textStackedVenn": "Stacked <PERSON>n", "Common.define.smartArt.textStaggeredProcess": "Staggered process", "Common.define.smartArt.textStepDownProcess": "Step down process", "Common.define.smartArt.textStepUpProcess": "Step up process", "Common.define.smartArt.textSubStepProcess": "Sub-step process", "Common.define.smartArt.textTabbedArc": "Tabbed arc", "Common.define.smartArt.textTableHierarchy": "Table hierarchy", "Common.define.smartArt.textTableList": "Table list", "Common.define.smartArt.textTabList": "Tab List", "Common.define.smartArt.textTargetList": "Target list", "Common.define.smartArt.textTextCycle": "Text cycle", "Common.define.smartArt.textThemePictureAccent": "Theme picture accent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Theme picture alternating accent", "Common.define.smartArt.textThemePictureGrid": "Theme picture grid", "Common.define.smartArt.textTitledMatrix": "Titled matrix", "Common.define.smartArt.textTitledPictureAccentList": "Titled picture accent list", "Common.define.smartArt.textTitledPictureBlocks": "Titled picture blocks", "Common.define.smartArt.textTitlePictureLineup": "Title picture lineup", "Common.define.smartArt.textTrapezoidList": "Trapezoid list", "Common.define.smartArt.textUpwardArrow": "Upward arrow", "Common.define.smartArt.textVaryingWidthList": "Varying width list", "Common.define.smartArt.textVerticalAccentList": "Vertical accent list", "Common.define.smartArt.textVerticalArrowList": "Vertical arrow list", "Common.define.smartArt.textVerticalBendingProcess": "Vertical bending process", "Common.define.smartArt.textVerticalBlockList": "Vertical block list", "Common.define.smartArt.textVerticalBoxList": "Vertical box list", "Common.define.smartArt.textVerticalBracketList": "Vertical bracket list", "Common.define.smartArt.textVerticalBulletList": "Vertical bullet list", "Common.define.smartArt.textVerticalChevronList": "Vertical chevron list", "Common.define.smartArt.textVerticalCircleList": "Vertical circle list", "Common.define.smartArt.textVerticalCurvedList": "Vertical curved list", "Common.define.smartArt.textVerticalEquation": "Vertical equation", "Common.define.smartArt.textVerticalPictureAccentList": "Vertical picture accent list", "Common.define.smartArt.textVerticalPictureList": "Vertical picture list", "Common.define.smartArt.textVerticalProcess": "Vertical process", "Common.Translation.textMoreButton": "<PERSON><PERSON>", "Common.Translation.tipFileLocked": "Document is locked for editing. You can make changes and save it as local copy later.", "Common.Translation.tipFileReadOnly": "The file is read-only. To keep your changes, save the file with a new name or in a different location.", "Common.Translation.warnFileLocked": "<PERSON><PERSON> başqa proqramda redaktə olunur. Siz redaktə etməyə davam edə və onu surət kimi saxlaya bilərsiniz.", "Common.Translation.warnFileLockedBtnEdit": "Kopyasını yaradın", "Common.Translation.warnFileLockedBtnView": "Baxmaq üçün açın", "Common.UI.ButtonColored.textAutoColor": "Avtomatik", "Common.UI.ButtonColored.textEyedropper": "Eyedropper", "Common.UI.ButtonColored.textNewColor": "Yeni Fərdi Rəng Əlavə Edin", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur", "Common.UI.ComboDataView.emptyComboText": "Üslub yoxdur", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON> edin", "Common.UI.ExtendedColorDialog.textCurrent": "Hazırki", "Common.UI.ExtendedColorDialog.textHexErr": "Daxil edilmiş dəyər yanlışdır.<br>000000 və FFFFFF arasında dəyər daxil edin.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "Daxil edilmiş dəyər yanlışdır.<br>0 və 255 arasında rəqəmsal dəyər daxil edin.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON><PERSON> yoxdur", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Hide password", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Show password", "Common.UI.SearchBar.textFind": "Find", "Common.UI.SearchBar.tipCloseSearch": "Close find", "Common.UI.SearchBar.tipNextResult": "Next result", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Open advanced settings", "Common.UI.SearchBar.tipPreviousResult": "Previous result", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textMatchCase": "Böyük/Kiçik Hərfə <PERSON>s", "Common.UI.SearchDialog.textReplaceDef": "<PERSON><PERSON><PERSON><PERSON> edilmiş mətni daxil edin", "Common.UI.SearchDialog.textSearchStart": "Mətninizi buraya daxil edin", "Common.UI.SearchDialog.textTitle": "Tapın və Əvəz edin", "Common.UI.SearchDialog.textTitle2": "Tapın", "Common.UI.SearchDialog.textWholeWords": "<PERSON><PERSON><PERSON>z tam sözlər", "Common.UI.SearchDialog.txtBtnHideReplace": "Əv<PERSON>zet<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON>vəz edin", "Common.UI.SearchDialog.txtBtnReplaceAll": "Hamısını Əvəz edin", "Common.UI.SynchronizeTip.textDontShow": "Bu mesajı bir daha gö<PERSON>ə<PERSON>əyin", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "<PERSON>ənəd başqa istifadəçi tərəfindən dəyişdirilib.<br>Dəyişikliklərinizi yadda saxlamaq və yeniləmələri yenidən yükləmək üçün klikləyin.", "Common.UI.ThemeColorPalette.textRecentColors": "Recent colors", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Klassik İşıq", "Common.UI.Themes.txtThemeContrastDark": "Contrast Dark", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "Açıq", "Common.UI.Themes.txtThemeSystem": "Same as system", "Common.UI.Window.cancelButtonText": "Lə<PERSON>v edin", "Common.UI.Window.closeButtonText": "Bağlayın", "Common.UI.Window.noButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Təsdiq", "Common.UI.Window.textDontShow": "Bu mesajı bir daha gö<PERSON>ə<PERSON>əyin", "Common.UI.Window.textError": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textInformation": "İnformasiya", "Common.UI.Window.textWarning": "Xəbərdarlıq", "Common.UI.Window.yesButtonText": "<PERSON><PERSON><PERSON>", "Common.Utils.Metric.txtCm": "sm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Accent", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "Background", "Common.Utils.ThemeColor.txtBlack": "Black", "Common.Utils.ThemeColor.txtBlue": "Blue", "Common.Utils.ThemeColor.txtBrightGreen": "Bright green", "Common.Utils.ThemeColor.txtBrown": "<PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Dark blue", "Common.Utils.ThemeColor.txtDarker": "Darker", "Common.Utils.ThemeColor.txtDarkGray": "Dark gray", "Common.Utils.ThemeColor.txtDarkGreen": "Dark green", "Common.Utils.ThemeColor.txtDarkPurple": "Dark purple", "Common.Utils.ThemeColor.txtDarkRed": "Dark red", "Common.Utils.ThemeColor.txtDarkTeal": "Dark teal", "Common.Utils.ThemeColor.txtDarkYellow": "Dark yellow", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "<PERSON>", "Common.Utils.ThemeColor.txtGreen": "Green", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "Light blue", "Common.Utils.ThemeColor.txtLighter": "Lighter", "Common.Utils.ThemeColor.txtLightGray": "Light gray", "Common.Utils.ThemeColor.txtLightGreen": "Light green", "Common.Utils.ThemeColor.txtLightOrange": "Light orange", "Common.Utils.ThemeColor.txtLightYellow": "Light yellow", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "Pink", "Common.Utils.ThemeColor.txtPurple": "Purple", "Common.Utils.ThemeColor.txtRed": "Red", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Sky blue", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Text", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "White", "Common.Utils.ThemeColor.txtYellow": "Yellow", "Common.Views.About.txtAddress": "ünvan:", "Common.Views.About.txtLicensee": "LİSENZİYA", "Common.Views.About.txtLicensor": "LİSENZİYAÇI", "Common.Views.About.txtMail": "e-poçt:", "Common.Views.About.txtPoweredBy": "ilə enerji ilə təmin olunur", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Vers<PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON> edin", "Common.Views.AutoCorrectDialog.textApplyAsWork": "İşlədiyiniz kimi müraciət edin", "Common.Views.AutoCorrectDialog.textAutoCorrect": "\nAvtoDüzəliş", "Common.Views.AutoCorrectDialog.textAutoFormat": "Yazdıqca AvtomaFormat edin", "Common.Views.AutoCorrectDialog.textBy": "vasitəsi ilə", "Common.Views.AutoCorrectDialog.textDelete": "Silin", "Common.Views.AutoCorrectDialog.textHyperlink": "Hiperlinklərlə İnternet və şəbəkə yolları", "Common.Views.AutoCorrectDialog.textMathCorrect": "R<PERSON>zi Avto<PERSON>", "Common.Views.AutoCorrectDialog.textNewRowCol": "Cədvəldəki yeni sətir və sütunları əhatə edir", "Common.Views.AutoCorrectDialog.textRecognized": "Tanınmış Funksiyalar", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Aşağıdakı ifadələr tanınan riyazi ifadələrdir. Onlar avtomatik kursivləşdirilməyəcək.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON>vəz edin", "Common.Views.AutoCorrectDialog.textReplaceText": "Yazdıqca Əvəz edin", "Common.Views.AutoCorrectDialog.textReplaceType": "Yaz<PERSON><PERSON>qca mətni əvəz edin", "Common.Views.AutoCorrectDialog.textReset": "Sıfırla", "Common.Views.AutoCorrectDialog.textResetAll": "Defolt-a Sıfırla", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON>ərpa edin", "Common.Views.AutoCorrectDialog.textTitle": "AvtoDüzəliş", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Tanınmış funksiyalar yalnız A-dan Z, böyük və ya kiçik hərflərdən ibarət olmalıdır.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "<PERSON><PERSON>ə etdiyiniz hər hansı ifadə silinəcək və sildiyiniz ifadə bərpa olunacaq. Davam etmək istəyirsiniz?", "Common.Views.AutoCorrectDialog.warnReplace": "1% üçün avtomatik düzəliş girişi artıq mövcuddur. Onu əvəz etmək istəyirsiniz?", "Common.Views.AutoCorrectDialog.warnReset": "<PERSON>lavə etdiyiniz hər hansı avtodüzəlişlər silinəcək və dəyişdirdiyiniz avtomatik düzəlişlər orijinal dəyərlərinə sıfırlanacaq. Davam etmək istəyirsiniz?", "Common.Views.AutoCorrectDialog.warnRestore": "1% üçün avtomatik düzəliş girişi orijinal dəyərinə sıfırlanacaq. Davam etmək istəyirsiniz?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "<PERSON><PERSON><PERSON><PERSON><PERSON> A-dan Z-ə", "Common.Views.Comments.mniAuthorDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON>ya", "Common.Views.Comments.mniDateAsc": "Ən köhnə", "Common.Views.Comments.mniDateDesc": "<PERSON>n yeni", "Common.Views.Comments.mniFilterGroups": "Filter by Group", "Common.Views.Comments.mniPositionAsc": "Yu<PERSON>r<PERSON>dan", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON> edin", "Common.Views.Comments.textAddComment": "Şərh əlavə edin", "Common.Views.Comments.textAddCommentToDoc": "Sənədə Şərh Əlavə edin", "Common.Views.Comments.textAddReply": "<PERSON><PERSON>b <PERSON> edin", "Common.Views.Comments.textAll": "All", "Common.Views.Comments.textAnonym": "Qonaq", "Common.Views.Comments.textCancel": "Lə<PERSON>v edin", "Common.Views.Comments.textClose": "Bağlayın", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON>", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "Şə<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Şərhinizi buraya daxil edin", "Common.Views.Comments.textHintAddComment": "Şərh əlavə edin", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON> verin", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON> edin", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "You have no permission to reopen the comment", "Common.Views.Comments.txtEmpty": "There are no comments in the sheet.", "Common.Views.CopyWarningDialog.textDontShow": "Bu mesajı bir daha gö<PERSON>ə<PERSON>əyin", "Common.Views.CopyWarningDialog.textMsg": "Alətlər paneli düymələrindən istifadə edərək və yalnız bu tabdakı kontekst menyusundan istifadə edərək kopyalama, kəsmə və yapışdırma əməliyyatlarını yerinə yetirə bilərsiniz. <br> <br> Redaktor tabından kənar proqramları köçürmək və ya yapışdırmaq üçün aşağıdakı düymə kombinasiyalarından istifadə edin:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>, Kəsm<PERSON> və Yapışdırma Əməliyyatları", "Common.Views.CopyWarningDialog.textToCopy": "Kopyalama üçün", "Common.Views.CopyWarningDialog.textToCut": "Kəsmək üçün", "Common.Views.CopyWarningDialog.textToPaste": "Yerləşdirmək üçün", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "Yüklənir...", "Common.Views.DocumentAccessDialog.textTitle": "Paylaşma <PERSON>rlə<PERSON>", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "Eraser", "Common.Views.Draw.hintSelect": "Select", "Common.Views.Draw.txtEraser": "Eraser", "Common.Views.Draw.txtHighlighter": "Highlighter", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Pen", "Common.Views.Draw.txtSelect": "Select", "Common.Views.Draw.txtSize": "Size", "Common.Views.EditNameDialog.textLabel": "Nişan:", "Common.Views.EditNameDialog.textLabelError": "<PERSON><PERSON><PERSON> bo<PERSON>ıdı<PERSON>.", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "Faylı redaktə edən istifadəçilər:", "Common.Views.Header.textAddFavorite": "<PERSON>avorit kimi i<PERSON>ələ", "Common.Views.Header.textAdvSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON> parametrlər", "Common.Views.Header.textBack": "<PERSON><PERSON> a<PERSON>", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "<PERSON><PERSON><PERSON> gizlədin", "Common.Views.Header.textHideLines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "Common.Views.Header.textHideStatusBar": "Vərəq və status panellərini birləşdirin", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "Read only", "Common.Views.Header.textRemoveFavorite": "Favoritl<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveBegin": "<PERSON><PERSON><PERSON>...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "<PERSON><PERSON><PERSON><PERSON><PERSON> də<PERSON>ər yadda sa<PERSON>lıb", "Common.Views.Header.textSaveExpander": "<PERSON><PERSON><PERSON><PERSON><PERSON> də<PERSON>ər yadda sa<PERSON>lıb", "Common.Views.Header.textShare": "Share", "Common.Views.Header.textZoom": "<PERSON><PERSON><PERSON><PERSON> də<PERSON>", "Common.Views.Header.tipAccessRights": "Sənə<PERSON>ə giriş hüquqlarını idarə edin", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "Faylı endirin", "Common.Views.Header.tipGoEdit": "Cari faylı redaktə edin", "Common.Views.Header.tipPrint": "Faylı çap edin", "Common.Views.Header.tipPrintQuick": "Quick print", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON><PERSON> edin", "Common.Views.Header.tipSave": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSearch": "Find", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Ayrı pəncərəyə çıxarın", "Common.Views.Header.tipUsers": "View users", "Common.Views.Header.tipViewSettings": "Görünüş parametrləri", "Common.Views.Header.tipViewUsers": "İstifadəçilərə baxın və sənədə giriş hüquqlarını idarə edin", "Common.Views.Header.txtAccessRights": "<PERSON><PERSON><PERSON>ü<PERSON>ını dəyiş", "Common.Views.Header.txtRename": "Adın<PERSON> də<PERSON>", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON>ın", "Common.Views.History.textHideAll": "Ətraflı dəyişik<PERSON><PERSON><PERSON><PERSON> giz<PERSON>ədin", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "<PERSON>ərpa edin", "Common.Views.History.textShowAll": "Detallı dəyişik<PERSON>l<PERSON><PERSON>", "Common.Views.History.textVer": "versiya", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "Təsvir URL-ni yerləşdirin:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Bu sahə tələb olunur", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Bu sahə \"http://www.example.com\" formatında URL olmalıdır", "Common.Views.ListSettingsDialog.textBulleted": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.textFromFile": "From file", "Common.Views.ListSettingsDialog.textFromStorage": "From storage", "Common.Views.ListSettingsDialog.textFromUrl": "From URL", "Common.Views.ListSettingsDialog.textNumbering": "Nömrələnmiş", "Common.Views.ListSettingsDialog.textSelect": "Select from", "Common.Views.ListSettingsDialog.tipChange": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "Image", "Common.Views.ListSettingsDialog.txtImport": "Import", "Common.Views.ListSettingsDialog.txtNewBullet": "Yeni marker", "Common.Views.ListSettingsDialog.txtNewImage": "New image", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtOfText": "mətnin %-i", "Common.Views.ListSettingsDialog.txtSize": "Ölçü", "Common.Views.ListSettingsDialog.txtStart": "Başlayın", "Common.Views.ListSettingsDialog.txtSymbol": "Simvol", "Common.Views.ListSettingsDialog.txtTitle": "Siyahı Parametrləri", "Common.Views.ListSettingsDialog.txtType": "Növ", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "Faylı Bağlayın", "Common.Views.OpenDialog.textInvalidRange": "Yanlış xana di<PERSON>", "Common.Views.OpenDialog.textSelectData": "Məlumatı seçin", "Common.Views.OpenDialog.txtAdvanced": "Təkmilləşdirilmiş", "Common.Views.OpenDialog.txtColon": "Qoşa nöqtə", "Common.Views.OpenDialog.txtComma": "Verg<PERSON><PERSON>", "Common.Views.OpenDialog.txtDelimiter": "Ayırıcı", "Common.Views.OpenDialog.txtDestData": "Verilə<PERSON><PERSON><PERSON><PERSON> hara yerləşdir<PERSON>əcəyini seçin", "Common.Views.OpenDialog.txtEmpty": "Bu sahə tələb olunur", "Common.Views.OpenDialog.txtEncoding": "Kodlaşdırma", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON> s<PERSON>.", "Common.Views.OpenDialog.txtOpenFile": "Faylı açmaq üçün parol daxil edin", "Common.Views.OpenDialog.txtOther": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "Önbaxış", "Common.Views.OpenDialog.txtProtected": "Şifrəni daxil edib faylı açdıqdan sonra faylın cari parolu sıfırlanacaq.", "Common.Views.OpenDialog.txtSemicolon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtSpace": "Boşluq", "Common.Views.OpenDialog.txtTab": "Tab", "Common.Views.OpenDialog.txtTitle": "Seçimlərin %1-ni seçin", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtDescription": "Bu sənədi qorumaq üçün parol təyin edin", "Common.Views.PasswordDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON> parolu eyni deyil", "Common.Views.PasswordDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON> təyin edin", "Common.Views.PasswordDialog.txtWarning": "Xəbərdarlıq: Əgər parolu itirsəniz və ya unutsanız, onu bərpa etmək mümkün olmayacaq. Zə<PERSON>ət olmasa onu təhlükəsiz yerdə saxlayın.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "Qoşmalar", "Common.Views.Plugins.strPlugins": "Qoşmalar", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "Başla", "Common.Views.Plugins.textStop": "Dayandır", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "<PERSON><PERSON> ilə ş<PERSON>ə<PERSON>əyin", "Common.Views.Protection.hintDelPwd": "Delete password", "Common.Views.Protection.hintPwd": "<PERSON><PERSON><PERSON> də<PERSON> və ya sil", "Common.Views.Protection.hintSignature": "<PERSON>ə<PERSON>ə<PERSON>al imza və ya imza sətri əlavə edin", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON> ə<PERSON>ə edin", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtEncrypt": "Şifrələ", "Common.Views.Protection.txtInvisibleSignature": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON> imza əlavə edin", "Common.Views.Protection.txtSignature": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtSignatureLine": "<PERSON><PERSON>za sətri əlavə edin", "Common.Views.RecentFiles.txtOpenRecent": "Open Recent", "Common.Views.RenameDialog.textName": "<PERSON><PERSON> adı", "Common.Views.RenameDialog.txtInvalidName": "<PERSON><PERSON> adında a<PERSON>ğıdakı simvollardan heç biri ola bilməz:", "Common.Views.ReviewChanges.hintNext": "Növbəti də<PERSON>ş<PERSON>yə", "Common.Views.ReviewChanges.hintPrev": "Əvvəlki dəyişikliyə", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Real vaxtda birgə redaktə. Bütün dəyişikliklər avtomatik olaraq yadda saxlanılır.", "Common.Views.ReviewChanges.strStrict": "Mə<PERSON><PERSON>dlaşdır", "Common.Views.ReviewChanges.strStrictDesc": "Sizin və başqalarının etdiyi dəyişiklikləri sinxronlaşdırmaq üçün \"Saxla\" düyməsindən istifadə edin.", "Common.Views.ReviewChanges.tipAcceptCurrent": "<PERSON><PERSON> də<PERSON>yi qəbul edin", "Common.Views.ReviewChanges.tipCoAuthMode": "Birgə redaktə rejimini təyin edin", "Common.Views.ReviewChanges.tipCommentRem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipCommentRemCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.tipCommentResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> həll edin", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "<PERSON><PERSON> şə<PERSON><PERSON><PERSON>ri həll edin", "Common.Views.ReviewChanges.tipHistory": "<PERSON>ersiya tarixçəsini <PERSON>", "Common.Views.ReviewChanges.tipRejectCurrent": "<PERSON><PERSON> də<PERSON>yi rədd edin", "Common.Views.ReviewChanges.tipReview": "Dəyişik<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipReviewView": "Dəyişik<PERSON><PERSON><PERSON><PERSON> göstəril<PERSON><PERSON><PERSON>i istədiyiniz rejimi seçin", "Common.Views.ReviewChanges.tipSetDocLang": "<PERSON>ə<PERSON><PERSON>d dilini təyin edin", "Common.Views.ReviewChanges.tipSetSpelling": "Orfoqrafiyanın <PERSON>", "Common.Views.ReviewChanges.tipSharing": "Sənə<PERSON>ə giriş hüquqlarını idarə edin", "Common.Views.ReviewChanges.txtAccept": "<PERSON>əbul edin", "Common.Views.ReviewChanges.txtAcceptAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> də<PERSON> qəbul edin", "Common.Views.ReviewChanges.txtAcceptChanges": "<PERSON><PERSON><PERSON>ş<PERSON><PERSON><PERSON><PERSON><PERSON> qəbul edin", "Common.Views.ReviewChanges.txtAcceptCurrent": "<PERSON><PERSON>yi Qəbul edin", "Common.Views.ReviewChanges.txtChat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtClose": "Bağlayın", "Common.Views.ReviewChanges.txtCoAuthMode": "Birgə redaktə Rejimi", "Common.Views.ReviewChanges.txtCommentRemAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemMy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "<PERSON>i <PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemove": "Silin", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON> edin", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "<PERSON><PERSON>in", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Həll edin", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "<PERSON>i Şərhlə<PERSON> Həll edin", "Common.Views.ReviewChanges.txtDocLang": "Dil", "Common.Views.ReviewChanges.txtFinal": "<PERSON><PERSON><PERSON><PERSON><PERSON> də<PERSON> qəbul edildi (Ön baxış)", "Common.Views.ReviewChanges.txtFinalCap": "Son", "Common.Views.ReviewChanges.txtHistory": "Versiya <PERSON>ə<PERSON>", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON><PERSON><PERSON><PERSON> də<PERSON> (Redaktə)", "Common.Views.ReviewChanges.txtMarkupCap": "Düzəlişlər", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON><PERSON><PERSON><PERSON> də<PERSON> rədd <PERSON>il<PERSON> (Ön baxış)", "Common.Views.ReviewChanges.txtOriginalCap": "Orijinal", "Common.Views.ReviewChanges.txtPrev": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON> edin", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>in", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rədd edin", "Common.Views.ReviewChanges.txtRejectCurrent": "<PERSON><PERSON>yi Rədd <PERSON>in", "Common.Views.ReviewChanges.txtSharing": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtSpelling": "Orfoqrafiyanın <PERSON>", "Common.Views.ReviewChanges.txtTurnon": "Dəyişik<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON><PERSON> rejimi", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON> edin", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON>b <PERSON> edin", "Common.Views.ReviewPopover.textCancel": "Lə<PERSON>v edin", "Common.Views.ReviewPopover.textClose": "Bağlayın", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Enter your comment here", "Common.Views.ReviewPopover.textMention": "+ qeyd sənədə girişi təmin edəcək və e-poçt göndərəcək", "Common.Views.ReviewPopover.textMentionNotify": "+ qeyd e-mail vasitəsilə istifadəçiyə bildiriş verəcək", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON> verin", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON> edin", "Common.Views.ReviewPopover.textViewResolved": "You have no permission to reopen the comment", "Common.Views.ReviewPopover.txtDeleteTip": "Silin", "Common.Views.ReviewPopover.txtEditTip": "Redaktə edin", "Common.Views.SaveAsDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textTitle": "Yadda sax<PERSON>a <PERSON><PERSON> qovluq", "Common.Views.SearchPanel.textByColumns": "By columns", "Common.Views.SearchPanel.textByRows": "By rows", "Common.Views.SearchPanel.textCaseSensitive": "Case sensitive", "Common.Views.SearchPanel.textCell": "Cell", "Common.Views.SearchPanel.textCloseSearch": "Close find", "Common.Views.SearchPanel.textContentChanged": "Document changed.", "Common.Views.SearchPanel.textFind": "Find", "Common.Views.SearchPanel.textFindAndReplace": "Find and replace", "Common.Views.SearchPanel.textFormula": "Formula", "Common.Views.SearchPanel.textFormulas": "Formulas", "Common.Views.SearchPanel.textItemEntireCell": "Entire cell contents", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} items successfully replaced.", "Common.Views.SearchPanel.textLookIn": "Look in", "Common.Views.SearchPanel.textMatchUsingRegExp": "Match using regular expressions", "Common.Views.SearchPanel.textName": "Name", "Common.Views.SearchPanel.textNoMatches": "No matches", "Common.Views.SearchPanel.textNoSearchResults": "No search results", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} items replaced. Remaining {2} items are locked by other users.", "Common.Views.SearchPanel.textReplace": "Replace", "Common.Views.SearchPanel.textReplaceAll": "Replace All", "Common.Views.SearchPanel.textReplaceWith": "Replace with", "Common.Views.SearchPanel.textSearch": "Search", "Common.Views.SearchPanel.textSearchAgain": "{0}Perform new search{1} for accurate results.", "Common.Views.SearchPanel.textSearchHasStopped": "Search has stopped", "Common.Views.SearchPanel.textSearchOptions": "Search options", "Common.Views.SearchPanel.textSearchResults": "Search results: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textSelectDataRange": "Select Data range", "Common.Views.SearchPanel.textSheet": "Sheet", "Common.Views.SearchPanel.textSpecificRange": "Specific range", "Common.Views.SearchPanel.textTooManyResults": "There are too many results to show here", "Common.Views.SearchPanel.textValue": "Value", "Common.Views.SearchPanel.textValues": "Values", "Common.Views.SearchPanel.textWholeWords": "Whole words only", "Common.Views.SearchPanel.textWithin": "Within", "Common.Views.SearchPanel.textWorkbook": "Workbook", "Common.Views.SearchPanel.tipNextResult": "Next result", "Common.Views.SearchPanel.tipPreviousResult": "Previous result", "Common.Views.SelectFileDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SelectFileDlg.textTitle": "Veri<PERSON>ən<PERSON>ər mənbəyini seçin", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "Qalın", "Common.Views.SignDialog.textCertificate": "Ser<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textInputName": "İmzalayanın adını daxil edin", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "İmzalayanın adı boş o<PERSON>malıdır", "Common.Views.SignDialog.textPurpose": "<PERSON>u sənədin im<PERSON>ının məqsədi", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSignature": "<PERSON><PERSON><PERSON> kimi <PERSON>", "Common.Views.SignDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textUseImage": "və ya şəkili imza kimi istifadə etmək üçün \"Şəkil Seçin\" hissəsinin üzərinə klikləyin", "Common.Views.SignDialog.textValid": "%1 ilə %2 arasında etibarlıdır", "Common.Views.SignDialog.tipFontName": "Şrift Adı", "Common.Views.SignDialog.tipFontSize": "Şrift Ölçüsü", "Common.Views.SignSettingsDialog.textAllowComment": "İmzalayana imza dialoq qutusuna şərh əlavə etmək icazəsi verin", "Common.Views.SignSettingsDialog.textDefInstruction": "Before signing this document, verify that the content you are signing is correct.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-poçt", "Common.Views.SignSettingsDialog.textInfoName": "Ad", "Common.Views.SignSettingsDialog.textInfoTitle": "İmzalayın <PERSON>", "Common.Views.SignSettingsDialog.textInstructions": "İmzalayan üçün tə<PERSON>", "Common.Views.SignSettingsDialog.textShowDate": "<PERSON><PERSON>za sətirində imza ta<PERSON> g<PERSON>ərin", "Common.Views.SignSettingsDialog.textTitle": "<PERSON><PERSON><PERSON>", "Common.Views.SignSettingsDialog.txtEmpty": "Bu sahə tələb olunur", "Common.Views.SymbolTableDialog.textCharacter": "Simvol", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX dəyəri", "Common.Views.SymbolTableDialog.textCopyright": "<PERSON><PERSON><PERSON><PERSON><PERSON> hü<PERSON>", "Common.Views.SymbolTableDialog.textDCQuote": "Cüt Dırnaq Bağlanır", "Common.Views.SymbolTableDialog.textDOQuote": "Qoşa Dırnaq Açılır", "Common.Views.SymbolTableDialog.textEllipsis": "Üfüqi Ellips", "Common.Views.SymbolTableDialog.textEmDash": "Uzun Tire", "Common.Views.SymbolTableDialog.textEmSpace": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEnDash": "Tire", "Common.Views.SymbolTableDialog.textEnSpace": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textFont": "Şrift", "Common.Views.SymbolTableDialog.textNBHyphen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBSpace": "Kə<PERSON>lmənin olmadığı Boşluq", "Common.Views.SymbolTableDialog.textPilcrow": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 M Boşluğu", "Common.Views.SymbolTableDialog.textRange": "Diapazon", "Common.Views.SymbolTableDialog.textRecent": "Son zamanlarda istifadə olunmuş simvollar", "Common.Views.SymbolTableDialog.textRegistered": "Qeydiyyatdan keçmə işarəsi", "Common.Views.SymbolTableDialog.textSCQuote": "Tək Dırnaq Bağlanır", "Common.Views.SymbolTableDialog.textSection": "Bölmə İşarəsi", "Common.Views.SymbolTableDialog.textShortcut": "Qısayol dü<PERSON>", "Common.Views.SymbolTableDialog.textSHyphen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSOQuote": "Tək Dırnaq Açılır", "Common.Views.SymbolTableDialog.textSpecial": "<PERSON><PERSON><PERSON><PERSON> simvo<PERSON>", "Common.Views.SymbolTableDialog.textSymbols": "Simvollar", "Common.Views.SymbolTableDialog.textTitle": "Simvol", "Common.Views.SymbolTableDialog.textTradeMark": "Ticarət nişanı simvolu", "Common.Views.UserNameDialog.textDontShow": "Məndən bir də soruşmayın", "Common.Views.UserNameDialog.textLabel": "Nişan:", "Common.Views.UserNameDialog.textLabelError": "<PERSON><PERSON><PERSON> bo<PERSON>ıdı<PERSON>.", "SSE.Controllers.DataTab.strSheet": "Sheet", "SSE.Controllers.DataTab.textAddExternalData": "The link to an external source has been added. You can update such links in the Data tab.", "SSE.Controllers.DataTab.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textContinue": "Continue", "SSE.Controllers.DataTab.textDontUpdate": "Don't Update", "SSE.Controllers.DataTab.textEmptyUrl": "URL təyin etməlisiniz.", "SSE.Controllers.DataTab.textRows": "Sə<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textTurnOff": "Turn off AutoUpdate", "SSE.Controllers.DataTab.textUpdate": "Update", "SSE.Controllers.DataTab.textWizard": "Sütunlar üçün Mətn", "SSE.Controllers.DataTab.txtDataValidation": "Verilə<PERSON><PERSON><PERSON><PERSON> yox<PERSON>ı", "SSE.Controllers.DataTab.txtErrorExternalLink": "Error: updating is failed", "SSE.Controllers.DataTab.txtExpand": "Geniş<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Seç<PERSON>l<PERSON>rin yanındakı məlumatlar silinməyəcək. Bitişik məlumatları daxil etmək üçün seçimi genişləndirmək və ya yalnız hazırda seçilmiş xanalarla davam etmək istəyirsiniz?", "SSE.Controllers.DataTab.txtExtendDataValidation": "Seçimdə Verilənlərin Yoxlanılması parametrləri olmayan bəzi xanalar var.<br>Verilənlərin Yoxlanılmasını bu xanalara genişləndirmək istəyirsiniz?", "SSE.Controllers.DataTab.txtImportWizard": "Mətnin İdxalı Ustadı", "SSE.Controllers.DataTab.txtRemDuplicates": "Dublikatları Silin", "SSE.Controllers.DataTab.txtRemoveDataValidation": "Seç<PERSON>də birdən çox yoxlama növü var.<br><PERSON>i parametrlər silinsin və davam edilsin?", "SSE.Controllers.DataTab.txtRemSelected": "Seçilmişdə silin", "SSE.Controllers.DataTab.txtUrlTitle": "Məlumat URL-ni yerləşdirin", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "This workbook contains links to external sources which update automatically. This might be unsafe.<br><br>If you trust them, press Continue.", "SSE.Controllers.DataTab.warnUpdateExternalData": "This workbook contains links to one or more external sources that could be unsafe.<br>If you trust the links, update them to get the latest data.", "SSE.Controllers.DocumentHolder.alignmentText": "Düzləndirmə", "SSE.Controllers.DocumentHolder.centerText": "Mərkəz", "SSE.Controllers.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>", "SSE.Controllers.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.deleteText": "Silin", "SSE.Controllers.DocumentHolder.errorInvalidLink": "<PERSON><PERSON><PERSON> istinadı mövcud deyil. <PERSON><PERSON><PERSON><PERSON><PERSON> olmasa keçidi düzəldin və ya silin.", "SSE.Controllers.DocumentHolder.guestText": "Qonaq", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "<PERSON>", "SSE.Controllers.DocumentHolder.insertColumnRightText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Yuxarı Sətir", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Aşağı Sətir", "SSE.Controllers.DocumentHolder.insertText": "Daxil edin", "SSE.Controllers.DocumentHolder.leftText": "Sol", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Xəbərdarlıq", "SSE.Controllers.DocumentHolder.rightText": "Sağ", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "AvtoDüz<PERSON><PERSON><PERSON> seçimləri", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON><PERSON><PERSON> {0} simvol ({1} piksel)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "<PERSON><PERSON><PERSON><PERSON> {0} nöqtə ({1} piksel)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Açmaq üçün keçidi vurun və ya xananı seçmək üçün siçan düyməsini basıb saxlayın.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Sola əlavə edin", "SSE.Controllers.DocumentHolder.textInsertTop": "Yuxarı daxil edin", "SSE.Controllers.DocumentHolder.textPasteSpecial": "<PERSON><PERSON><PERSON><PERSON> əlavəetmə", "SSE.Controllers.DocumentHolder.textStopExpand": "Cədvəllərin avtomatik genişləndirilməsini dayandırın", "SSE.Controllers.DocumentHolder.textSym": "simvolik", "SSE.Controllers.DocumentHolder.tipIsLocked": "Bu element başqa istifadəçi tərəfindən redaktə olunur.", "SSE.Controllers.DocumentHolder.txtAboveAve": "<PERSON><PERSON> Qiymətdən Yuxarı", "SSE.Controllers.DocumentHolder.txtAddBottom": "Aşağı sərhəd əlavə edin", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "<PERSON>ə<PERSON>r xətti əlavə edin", "SSE.Controllers.DocumentHolder.txtAddHor": "Üfüqi xətt əlavə edin", "SSE.Controllers.DocumentHolder.txtAddLB": "Sol sərhəd xətti əlavə edin", "SSE.Controllers.DocumentHolder.txtAddLeft": "Sol sərhəd əlavə edin", "SSE.Controllers.DocumentHolder.txtAddLT": "Sol yuxarı sətir əlavə edin", "SSE.Controllers.DocumentHolder.txtAddRight": "<PERSON><PERSON> sərhəd əlavə edin", "SSE.Controllers.DocumentHolder.txtAddTop": "Yuxarı sərhəd əlavə edin", "SSE.Controllers.DocumentHolder.txtAddVer": "<PERSON><PERSON><PERSON> sətir əlavə edin", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Sim<PERSON>la uyğun düzləndir", "SSE.Controllers.DocumentHolder.txtAll": "(Hamısı)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Returns the entire contents of the table or specified table columns including column headers, data and total rows", "SSE.Controllers.DocumentHolder.txtAnd": "və", "SSE.Controllers.DocumentHolder.txtBegins": "<PERSON><PERSON>ə başlayır", "SSE.Controllers.DocumentHolder.txtBelowAve": "<PERSON>ta qiymətdən aşağı", "SSE.Controllers.DocumentHolder.txtBlanks": "(<PERSON><PERSON><PERSON><PERSON><PERSON>)", "SSE.Controllers.DocumentHolder.txtBorderProps": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtBottom": "Aşağı", "SSE.Controllers.DocumentHolder.txtByField": "%1 of %2", "SSE.Controllers.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtColumnAlign": "<PERSON><PERSON><PERSON> dü<PERSON>", "SSE.Controllers.DocumentHolder.txtContains": "İbarətdir", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Link copied to the clipboard", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Returns the data cells of the table or specified table columns", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Arqument ölçüsünü azaldın", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Arqumenti silin", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Əl ilə kəsilməni silin", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Ətrafdakı simvolları silin", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Ətrafdakı simvolları və ayırıcıları silin", "SSE.Controllers.DocumentHolder.txtDeleteEq": "<PERSON>ə<PERSON><PERSON><PERSON> silin", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Simvolu silin", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Radikalı silin", "SSE.Controllers.DocumentHolder.txtEnds": "<PERSON>lə başa çatır", "SSE.Controllers.DocumentHolder.txtEquals": "Bərabərdir", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "<PERSON><PERSON> rənginə bərabərdir", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Şrift rənginə bərabərdir", "SSE.Controllers.DocumentHolder.txtExpand": "Genişləndir və sırala", "SSE.Controllers.DocumentHolder.txtExpandSort": "Seçimin yanındakı məlumatlar çeşidlənməyəcək. Bitişik məlumatları daxil etmək üçün seçimi genişləndirmək və ya yalnız hazırda seçilmiş xanaları çeşidləməyə davam etmək istəyirsiniz?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Aşağı", "SSE.Controllers.DocumentHolder.txtFilterTop": "Yuxarı", "SSE.Controllers.DocumentHolder.txtFractionLinear": "<PERSON><PERSON><PERSON> frak<PERSON>", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Diaqonal fraksiyaya <PERSON>", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Şaquli sadə fraksiyaya də<PERSON>", "SSE.Controllers.DocumentHolder.txtGreater": "-də<PERSON>", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "-dən bö<PERSON><PERSON>k və ya ona bərabər", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "<PERSON><PERSON><PERSON>nin yuxarısında işarə", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Mə<PERSON>nin aşağısında işarə", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Returns the column headers for the table or specified table columns", "SSE.Controllers.DocumentHolder.txtHeight": "Hündürlük", "SSE.Controllers.DocumentHolder.txtHideBottom": "Aşa<PERSON><PERSON> sərhədi g<PERSON>din", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Aşağı limiti gizlədin", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Bağlı mötərizəni gizlədin", "SSE.Controllers.DocumentHolder.txtHideDegree": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>ədin", "SSE.Controllers.DocumentHolder.txtHideHor": "Üfüqi sətri giz<PERSON>ədin", "SSE.Controllers.DocumentHolder.txtHideLB": "Sol aşağı sətri gizlədin", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON> sər<PERSON>ə<PERSON> g<PERSON>ədin", "SSE.Controllers.DocumentHolder.txtHideLT": "Sol yuxarı sətri gizlədin", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "<PERSON><PERSON><PERSON><PERSON> mö<PERSON>ə<PERSON>ə<PERSON> giz<PERSON>ədin", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "<PERSON>r tutucunu g<PERSON>ədin", "SSE.Controllers.DocumentHolder.txtHideRight": "<PERSON><PERSON> sər<PERSON>ə<PERSON> g<PERSON>", "SSE.Controllers.DocumentHolder.txtHideTop": "Yu<PERSON>r<PERSON> sər<PERSON>ədi g<PERSON>ədin", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Yuxarı limiti giz<PERSON>ədin", "SSE.Controllers.DocumentHolder.txtHideVer": "<PERSON><PERSON><PERSON> s<PERSON> g<PERSON>", "SSE.Controllers.DocumentHolder.txtImportWizard": "Mətnin İdxalı Ustadı", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Arqument ölçüsünü artırın", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Arqumenti sonra daxil edin", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Arqumenti əvvəl daxil edin", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Əl ilə kəsilməni əlavə edin", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "<PERSON>ra tənlik daxil edin", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Əvvəl tənlik daxil edin", "SSE.Controllers.DocumentHolder.txtItems": "Element<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON><PERSON>z mətni saxlayın", "SSE.Controllers.DocumentHolder.txtLess": "-dən az", "SSE.Controllers.DocumentHolder.txtLessEquals": "-dən az və ya bərabərdir", "SSE.Controllers.DocumentHolder.txtLimitChange": "<PERSON><PERSON><PERSON><PERSON><PERSON> mö<PERSON> də<PERSON>", "SSE.Controllers.DocumentHolder.txtLimitOver": "Mətn üzərində limit", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Mətn altında limit", "SSE.Controllers.DocumentHolder.txtLockSort": "Seçiminizin yanında məlumat tapılıb, lakin sizin həmin xanaları dəyişmək üçün kifayət qədər icazəniz yoxdur.<br>Cari seçimlə davam etmək istəyirsiniz?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Mötə<PERSON><PERSON><PERSON><PERSON><PERSON> arqumentin h<PERSON>ü<PERSON>ə uyğunlaşdırın", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtNoChoices": "Xananı doldurmaq üçün heç bir seçim yoxdur.<br>Əvəz etmək üçün yalnız sütundakı mətn dəyərləri seçilə bilər.", "SSE.Controllers.DocumentHolder.txtNotBegins": "<PERSON><PERSON>ə başlamır", "SSE.Controllers.DocumentHolder.txtNotContains": "Ehtiva etmir", "SSE.Controllers.DocumentHolder.txtNotEnds": "İlə başa çatmır", "SSE.Controllers.DocumentHolder.txtNotEquals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtOr": "Və ya", "SSE.Controllers.DocumentHolder.txtOverbar": "Mə<PERSON>nin üstündə xətt", "SSE.Controllers.DocumentHolder.txtPaste": "Yapışdır", "SSE.Controllers.DocumentHolder.txtPasteBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON> dü<PERSON>", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Düstur + sütun eni", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Təyinat formatı", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Yaln<PERSON>z formatı yapışdırın", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Düstur + rəqəm formatı", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Yalnız düsturu yapışdırın", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Düstur + bütün <PERSON>ırma", "SSE.Controllers.DocumentHolder.txtPasteLink": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Şərti formatlaşdırmanı birləşdirin", "SSE.Controllers.DocumentHolder.txtPastePicture": "Şəkil", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Mən<PERSON>ə formatı", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Dəyər + bütün <PERSON>ırma", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Dəyər + rəqəm formatı", "SSE.Controllers.DocumentHolder.txtPasteValues": "<PERSON><PERSON>ı<PERSON> dəyəri ya<PERSON>", "SSE.Controllers.DocumentHolder.txtPercent": "faiz", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Cədvəlin AvtoGenişlətməsini Təkrarla", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "<PERSON><PERSON><PERSON><PERSON> xəttini silin", "SSE.Controllers.DocumentHolder.txtRemLimit": "<PERSON><PERSON> si<PERSON>", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Diakritik Simvolu Sil", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Zolağı Sil", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Bu imzanı silmək istəyirsiniz?<br>Bunu geri qaytarmaq mümkün deyil.", "SSE.Controllers.DocumentHolder.txtRemScripts": "İndeksi Silin", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Sətiraltı işarəni silin", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Sətiraltı işarəni silin", "SSE.Controllers.DocumentHolder.txtRowHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "<PERSON><PERSON><PERSON><PERSON><PERSON>n sonra skriptlər", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "<PERSON>ə<PERSON><PERSON>ən əvvəl skriptlər", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> limit<PERSON> g<PERSON>", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Bağlama mötərizə<PERSON>i g<PERSON>", "SSE.Controllers.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>ş mötərizəsini gö<PERSON>ərin", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Üst <PERSON>i g<PERSON>ərin", "SSE.Controllers.DocumentHolder.txtSorting": "Sıralama", "SSE.Controllers.DocumentHolder.txtSortSelected": "Seçilmiş sıralama", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Mötə<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Choose only this row of the specified column", "SSE.Controllers.DocumentHolder.txtTop": "Yuxarı", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Returns the total rows for the table or specified table columns", "SSE.Controllers.DocumentHolder.txtUnderbar": "Mə<PERSON>nin altında xətt", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Cədvəlin avtomatik genişləndirilməsini qaytar", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Mətnin idxalı ustadından istifadə edin", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Bu keçidə klikləmək cihazınız və məlumatlarınız üçün təhlükəli ola bilər. <br> Davam etmək istədiyinizdən əminsiniz?", "SSE.Controllers.DocumentHolder.txtWidth": "En", "SSE.Controllers.DocumentHolder.warnFilterError": "You need at least one field in the Values area in order to apply a value filter.", "SSE.Controllers.FormulaDialog.sCategoryAll": "Hamısı", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Custom", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "V<PERSON>l<PERSON><PERSON><PERSON><PERSON><PERSON> bazası", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Tarix və saat", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Mühəndislik", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Maliyyə", "SSE.Controllers.FormulaDialog.sCategoryInformation": "İnformasiya", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 sonuncu dəfə istifadə olunub", "SSE.Controllers.FormulaDialog.sCategoryLogical": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Axtarış və istinad", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Riyaziyyat və triqonometriya", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistik", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Mətn və verilənlər", "SSE.Controllers.LeftMenu.newDocumentTitle": "Adsız elektron cədvəl", "SSE.Controllers.LeftMenu.textByColumns": "Sütunlar üzrə", "SSE.Controllers.LeftMenu.textByRows": "Sə<PERSON>rlər üzrə", "SSE.Controllers.LeftMenu.textFormulas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textItemEntireCell": "<PERSON><PERSON><PERSON><PERSON><PERSON> xana mə<PERSON>nu", "SSE.Controllers.LeftMenu.textLoadHistory": "Versiya tarixçəsi yüklənir...", "SSE.Controllers.LeftMenu.textLookin": "Nəzərdən keçir:", "SSE.Controllers.LeftMenu.textNoTextFound": "Axtardığınız verilən tapılmadı. Axtarış seçimlərinizi tənzimləyin.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "<PERSON>vəz edilir. {0} hal nəzər<PERSON> alınmadı.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: {0}", "SSE.Controllers.LeftMenu.textSave": "Save", "SSE.Controllers.LeftMenu.textSearch": "Axtarış", "SSE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "SSE.Controllers.LeftMenu.textSheet": "Və<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textValues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWarning": "Xəbərdarlıq", "SSE.Controllers.LeftMenu.textWithin": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWorkbook": "İş kitabı", "SSE.Controllers.LeftMenu.txtUntitled": "Başlıqsız", "SSE.Controllers.LeftMenu.warnDownloadAs": "Bu formatda yadda saxlamağa davam etsəniz, mətndən başqa bütün funksiyalar itiriləcək.<br>Davam etmək istədiyinizdən əminsiniz?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "The CSV format does not support saving a multi-sheet file.<br>To keep the selected format and save only the current sheet, press Save.<br>To save the current spreadsheet, click Cancel and save it in a different format.", "SSE.Controllers.Main.confirmAddCellWatches": "This action will add {0} cell watches.<br>Do you want to continue?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "This action will add only {0} cell watches by memory save reason.<br>Do you want to continue?", "SSE.Controllers.Main.confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "SSE.Controllers.Main.confirmMoveCellRange": "Təyinat xanası diapazonunda məlumat ola bilər. Əməliyyata davam edirsiniz?", "SSE.Controllers.Main.confirmPutMergeRange": "Mənbə veriləni birləşdirilən xanaları ehtiva edir.<br>Cədvələ yapışdırılmamışdan əvvəl onlar birləşdirilib.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Başlıq cərgəsindəki düsturlar silinəcək və statik mətnə çevriləcək.<br>Davam etmək istəyirsiniz?", "SSE.Controllers.Main.confirmReplaceHFPicture": "Only one picture can be inserted in each section of the header.<br>Press \"Replace\" to replace existing picture.<br>Press \"Keep\" to keep existing picture.", "SSE.Controllers.Main.convertationTimeoutText": "Çevrilmə vaxtı maksimumu keçdi.", "SSE.Controllers.Main.criticalErrorExtText": "<PERSON>ə<PERSON><PERSON><PERSON> siyahı<PERSON>ına qayıtmaq üçün \"OK\" dü<PERSON><PERSON><PERSON><PERSON> basın.", "SSE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.downloadErrorText": "Endirmə prosesi uğ<PERSON>uz oldu.", "SSE.Controllers.Main.downloadTextText": "<PERSON>əd<PERSON>əl endiri<PERSON>r...", "SSE.Controllers.Main.downloadTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.errNoDuplicates": "<PERSON>ç bir dublikat dəyər tapılmadı.", "SSE.Controllers.Main.errorAccessDeny": "Hüquqlarınız olmayan əməliyyatı yerinə yetirməyə çalışırsınız.<br>Sənəd Serveri inzibatçınızla əlaqə saxlayın.", "SSE.Controllers.Main.errorArgsRange": "Daxil edilmiş düsturda xəta var.<br><PERSON><PERSON><PERSON>ş arqument diapazonundan istifadə edilib.", "SSE.Controllers.Main.errorAutoFilterChange": "Əməliyyata icazə veril<PERSON>, ç<PERSON>nk<PERSON> o, iş vərəqinizdəki cədvəldəki xanaları dəyişməyə çalışır.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Cədvəlin bir hissəsini köçürə bilməyəcəyiniz üçün seçilmiş xanalar üçün əməliyyat həyata keçirilə bilmədi.<br>Bütün cədvəlin yerdəyişməsi üçün başqa məlumat diapazonunu seçin və yenidən cəhd edin.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Seçilmiş xanalar diapazonu üçün əməliyyat həyata keçirilə bilmədi.<br>Mövcuddan fərqli vahid verilənlər diapazonu seçin və yenidən cəhd edin.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Sahədə filtrlənmiş xanalar olduğu üçün əməliyyat həyata keçirilə bilməz.<br>Süzülmüş elementləri göstərin və yenidən cəhd edin.", "SSE.Controllers.Main.errorBadImageUrl": "Təsvir URL-i yanlışdır", "SSE.Controllers.Main.errorCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the spreadsheet.", "SSE.Controllers.Main.errorCannotUngroup": "Qruplaşdırmanı ləğv etmək mümkün deyil. Struktur qurmaq üçün sətir və ya sütunları seçin və onları qruplaşdırın.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "You cannot use this command on a protected sheet. To use this command, unprotect the sheet.<br>You might be requested to enter a password.", "SSE.Controllers.Main.errorChangeArray": "<PERSON><PERSON> massivin bir hissəsini dəyişə bilməzsiniz.", "SSE.Controllers.Main.errorChangeFilteredRange": "Bu, iş vərəqinizdə süzülmüş diapazonu dəyişəcək.<br>Bu tapşırığı tamamlamaq üçün Avtomatik Süzgəcləri silin.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "Dəyişdirməyə çalışdığınız xana və ya diaqram qorunan vərəqdədir.<br>Dəyişiklik etmək üçün vərəqin qorumasını ləğv edin. Sizdən parol daxil etməyiniz tələb oluna bilər.", "SSE.Controllers.Main.errorCircularReference": "There are one or more circular references where a formula refers to its own cell either directly or indirectly.<br>Try removing or changing these references, or moving the formulas to different cells.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Server ba<PERSON><PERSON><PERSON><PERSON><PERSON> itdi. Sənədi hazırda redaktə etmək mümkün deyil.", "SSE.Controllers.Main.errorConnectToServer": "Sənədi saxlamaq mümkün olmadı. Lü<PERSON><PERSON>ə<PERSON>, əlaqə parametrlərini yoxlayın və ya inzibatçınızla əlaqə saxlayın.<br>'OK' düyməsini kliklədiyiniz zaman sizdən sənədi endirmək təklif olunacaq.", "SSE.Controllers.Main.errorConvertXml": "The file has an unsupported format.<br>Only XML Spreadsheet 2003 format can be used.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Bu əmr birdən çox seçimlə istifadə edilə bilməz.<br>Tək diapazon seçin və yenidən cəhd edin.", "SSE.Controllers.Main.errorCountArg": "Daxil edilmiş düsturda xəta var.<br><PERSON><PERSON><PERSON><PERSON> sayda arqument istifadə olunub.", "SSE.Controllers.Main.errorCountArgExceed": "Daxil edilmiş düsturda xəta var.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sayı keçilib.", "SSE.Controllers.Main.errorCreateDefName": "Mövcud adlandırılmış diapazonlar redaktə edilə bilməz və <br>hazırda onlardan bəziləri redaktə olunduğu üçün yeniləri yaradıla bilməz.", "SSE.Controllers.Main.errorCreateRange": "The existing ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON><PERSON> xəta.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bazasına qoşulma xətası. Xəta davam edərsə, dəstək xidməti ilə əlaqə saxlayın.", "SSE.Controllers.Main.errorDataEncrypted": "Şif<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dəyişik<PERSON>lər qəbul edildi, onları deşifrə etmək mümkün deyil.", "SSE.Controllers.Main.errorDataRange": "Yanlış məlumat diapazonu.", "SSE.Controllers.Main.errorDataValidate": "Daxil etdiyiniz dəyər etibarlı deyil.<br>İstifadəçinin bu xanaya daxil edilə bilən məhdudlaşdırılmış dəyərləri var.", "SSE.Controllers.Main.errorDefaultMessage": "<PERSON><PERSON><PERSON> kodu: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "<PERSON>z kilidlənmiş xana olan sütunu silməyə çalışırsınız. İş vərəqi qorunarkən kilidlənmiş xanalar silinə bilməz.<br>Kilidlənmiş xananı silmək üçün vərəqin qorumasını ləğv edin. Sizdən parol daxil etməyiniz tələb oluna bilər.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "<PERSON>z kilidlənmiş xana olan sətiri silməyə çalışırsınız. İş vərəqi qorunarkən kilidlənmiş xanalar silinə bilməz.<br>Kilidlənmiş xananı silmək üçün vərəqin qorumasını ləğv edin. Sizdən parol daxil etməyiniz tələb oluna bilər.", "SSE.Controllers.Main.errorDependentsNoFormulas": "The Trace Dependents command found no formulas that refer to the active cell.", "SSE.Controllers.Main.errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "SSE.Controllers.Main.errorEditingDownloadas": "Sən<PERSON><PERSON>ə işləyərkən xəta baş verdi. <br> <PERSON><PERSON><PERSON><PERSON> ehtiyat nüsxəsini kompüterinizin sərtt diskində saxlamaq üçün \"Kimi Endirin\" seçimindən istifadə edin.", "SSE.Controllers.Main.errorEditingSaveas": "Sənədlə işləyərkən xəta baş verdi. <br> Yed<PERSON><PERSON> nüsxəsini kompüterinizin sərt diskində saxlamaq üçün \"... kimi yadda saxla\" seçimindən istifadə edin.", "SSE.Controllers.Main.errorEditView": "Mövcud vərəq görünüşü redaktə edilə bilməz və bəziləri redaktə olunduğundan yeniləri hazırda yaradıla bilməz.", "SSE.Controllers.Main.errorEmailClient": "<PERSON>ç bir e-poçt müştərisi tapılmadı.", "SSE.Controllers.Main.errorFilePassProtect": "<PERSON>l parolla qorunur və onu açmaq mümkün de<PERSON>l.", "SSE.Controllers.Main.errorFileRequest": "<PERSON><PERSON><PERSON> xəta.<br><PERSON><PERSON><PERSON><PERSON><PERSON> xətası. Xəta davam edərsə, dəstək xidməti ilə əlaqə saxlayın.", "SSE.Controllers.Main.errorFileSizeExceed": "Faylın ölçüsü serveriniz üçün təyin edilmiş məhdudiyyəti keçir.<br>Təfərrüatlar üçün Sənəd Serveri inzibatçınızla əlaqə saxlayın.", "SSE.Controllers.Main.errorFileVKey": "Xarici xəta.<br><PERSON><PERSON><PERSON><PERSON> təhlükəsizlik açarı. Xəta davam edərsə, dəstək xidməti ilə əlaqə saxlayın.", "SSE.Controllers.Main.errorFillRange": "Seçilmiş xanalar diapazonunu doldurmaq mümkün olmadı.<br><PERSON><PERSON><PERSON>ün birləşdirilən xanalar eyni ölçüdə olmalıdır. ", "SSE.Controllers.Main.errorForceSave": "Faylı saxlayarkən xəta baş verdi. <PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>, faylı kompüterinizin sərtt diskində saxlamaq üçün “... kimi endir” seçimindən istifadə edin və ya sonra yenidən cəhd edin.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Cannot enter a formula for an item or field name in a pivot table report.", "SSE.Controllers.Main.errorFormulaName": "Daxil edilmiş düsturda xəta var.<br><PERSON><PERSON><PERSON>ş düstur adı istifadə olunub.", "SSE.Controllers.Main.errorFormulaParsing": "<PERSON><PERSON><PERSON><PERSON><PERSON> təhlili zamanı daxili xəta.", "SSE.Controllers.Main.errorFrmlMaxLength": "Düsturunuzun uzunluğu 8192 simvol limitini keçir.<br>Redaktə edin və yenidən cəhd edin.", "SSE.Controllers.Main.errorFrmlMaxReference": "<PERSON>z bu düsturu daxil edə bilmə<PERSON>, çü<PERSON><PERSON> onun həddən çox dəyəri,<br>xana istinadları və/yaxud adları var.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Düsturlardakı mətn dəyərləri 255 simvolla məhdudlaşır.<br>BİRLƏŞDİRMƏ funksiyasından və ya birləşdirici operatordan (&) istifadə edin.", "SSE.Controllers.Main.errorFrmlWrongReferences": "Funksiya mövcud olmayan cədvələ istinad edir.<br>Veriləni yoxlayın və yenidən cəhd edin.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Seçilmiş xana diapazonu üçün əməliyyat tamamlana bilmədi.<br> Elə diapazon seçin ki, birinci cədvəl sətri eyni sətirdə olsun<br>və nəticədə yaranan cədvəl cari cədvəl ilə üst-üstə düşsün.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Seçilmiş xana diapazonu üçün əməliyyat tamamlana bilmədi.<br><PERSON><PERSON>ə<PERSON> cədvəll<PERSON><PERSON> daxil olmadığı sətri seçin.", "SSE.Controllers.Main.errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "SSE.Controllers.Main.errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInvalidRef": "<PERSON><PERSON><PERSON> üçün düzgün ad və ya keçid üçün etibarlı istinad daxil edin.", "SSE.Controllers.Main.errorKeyEncrypt": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.errorKeyExpire": "<PERSON><PERSON><PERSON> vaxtı keçib", "SSE.Controllers.Main.errorLabledColumnsPivot": "Pivot cədvəli yaratmaq üçün etiketli sütunları olan siyahı kimi təşkil edilmiş verilənlərdən istifadə edin.", "SSE.Controllers.Main.errorLoadingFont": "Ş<PERSON><PERSON><PERSON>r yüklənməyib.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Sənəd Serveri inzibatçınızla əlaqə saxlayın.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Məkan və ya məlumat diapazonu üçün istinad etibarlı deyil.", "SSE.Controllers.Main.errorLockedAll": "<PERSON>ə<PERSON><PERSON><PERSON> başqa istifadəçi tərəfindən kilidləndiyi üçün əməliyyatı yerinə yetirmək mümkün olmadı.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "One of the cells involved in the goal seek process has been modified by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "Siz pivot cədvəl daxilində məlumatları dəyişə bilməzsiniz.", "SSE.Controllers.Main.errorLockedWorksheetRename": "Vərəqin adını hazırda dəyişdirmək mümkün deyil, çünki onun adı başqa istifadəçi tərəfindən dəyişdirilir", "SSE.Controllers.Main.errorMaxPoints": "Qrafik üzrə seriyalardakı nöqtələrin maksimum sayı 4096-dır.", "SSE.Controllers.Main.errorMoveRange": "Birləşdirilmiş xananın bir hissəsini dəyişdirmək mümkün deyil", "SSE.Controllers.Main.errorMoveSlicerError": "Cədvəl kəsimləri bir iş kitabından digərinə kopyalana bilməz.<br><PERSON><PERSON><PERSON>ün cədvəli və kəsimləri seçməklə yenidən cəhd edin.", "SSE.Controllers.Main.errorMultiCellFormula": "Cədvə<PERSON>ərdə çox xanalı massiv düsturlarına icazə verilmir.", "SSE.Controllers.Main.errorNoDataToParse": "<PERSON><PERSON><PERSON> etmək üçün heç bir məlumat seçilməyib.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.Main.errorOpenWarning": "<PERSON><PERSON> düsturlarından biri 8192 simvol limitini keçir.<br><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.errorOperandExpected": "Daxil edilmiş funksiya sintaksisi düzgün deyil. <PERSON><PERSON><PERSON>, mötərizələrdən birinin olun-olmadığını - '(' və ya ')' yox<PERSON><PERSON>n.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "Təqdim etdiyiniz parol düzgün deyil.<br>CAPS LOCK düyməsinin söndürüldüyünü yoxlayın və düzgün böyük hərfdən istifadə etdiyinizdən əmin olun.", "SSE.Controllers.Main.errorPasteInPivot": "We can't make this change for the selected cells because it will affect a pivot table.<br>Use the field list to change the report.", "SSE.Controllers.Main.errorPasteMaxRange": "Kopyalama və yapışdırma sahəsi uyğun gəlmir.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eyni <PERSON>ü<PERSON>ü sahə seçin və ya kopyalanmış xanaları yapışdırmaq üçün ard-arda birinci xanaya klikləyin.", "SSE.Controllers.Main.errorPasteMultiSelect": "Bu əməliyyat çoxsaylı diapazon seçimində həyata keçirilə bilməz.<br>Tək diapazon seçin və yenidən cəhd edin.", "SSE.Controllers.Main.errorPasteSlicerError": "<PERSON>əd<PERSON>əl kəsimləri bir iş kitabından digərinə kopyalana bilməz.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Pivot table field name already exists.", "SSE.Controllers.Main.errorPivotGroup": "<PERSON>u seç<PERSON>i q<PERSON>ş<PERSON><PERSON><PERSON><PERSON> mü<PERSON>ü<PERSON>.", "SSE.Controllers.Main.errorPivotOverlap": "Pivot cədvəl hesabatı cədvəllə üst-üstə düşə bilməz.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Pivot Cədvəl hesabatı əsas məlumat olmadan yadda saxlanıldı.<br>Hesabatı yeniləmək üçün \"Yenilə\" düyməsini istifadə edin.", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "The Trace Precedents command requires that the active cell contain a formula which includes a valid references.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON> ki, proqramın cari versiyasında eyni vaxtda 1500-dən çox səhifə çap etmək mümkün deyil.<br>Bu məhdudiyyət gələcək buraxılışlarda aradan qaldırılacaq.", "SSE.Controllers.Main.errorProcessSaveResult": "Saxlama prosesi uğ<PERSON> oldu", "SSE.Controllers.Main.errorProtectedRange": "This range is not allowed for editing.", "SSE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "SSE.Controllers.Main.errorServerVersion": "Redaktor versiyası yeniləndi. Dəyişiklikləri tətbiq etmək üçün səhifə yenidən yüklənəcək.", "SSE.Controllers.Main.errorSessionAbsolute": "Sən<PERSON>din redaktə sessiyasının vaxtı bitdi. Səhifəni yenidən yükləyin.", "SSE.Controllers.Main.errorSessionIdle": "Sənəd uzun müddətdir ki, redaktə olunmayıb. Səhifəni yenidən yükləyin.", "SSE.Controllers.Main.errorSessionToken": "Server ilə əlaqə kəsildi. Səhifəni yenidən yükləyin.", "SSE.Controllers.Main.errorSetPassword": "<PERSON><PERSON> təyin edilə bilmədi.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Xanaların hamısı eyni sütun və ya cərgədə olmadığı üçün yer istinadı etibarlı deyil.<br>Ham<PERSON>sı bir sütun və ya cərgədə olan xanaları seçin.", "SSE.Controllers.Main.errorStockChart": "Yanlış sıra ardıcıllığı. Səhm qrafikini yaratmaq üçün məlumatları vərəqdə aşağıdakı ardıcıllıqla yerləşdirin:<br> a<PERSON><PERSON><PERSON><PERSON><PERSON> qiyməti, maks<PERSON>um qiymət, minimum qiymət, ba<PERSON><PERSON><PERSON><PERSON> qiyməti.", "SSE.Controllers.Main.errorToken": "<PERSON><PERSON><PERSON><PERSON><PERSON> təhlükəsizlik nişanı düzgün formalaşmayıb.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Sənəd Server inzibatçısı ilə əlaqə saxlayın.", "SSE.Controllers.Main.errorTokenExpire": "<PERSON><PERSON><PERSON><PERSON><PERSON> təhlükəsizlik nişanının vaxtı bitib.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Sənəd Server inzibatçısı ilə əlaqə saxlayın.", "SSE.Controllers.Main.errorUnexpectedGuid": "Xarici xəta.<br>Gözlənilməz GUID. Xəta davam edərsə, dəstək xidməti ilə əlaqə saxlayın.", "SSE.Controllers.Main.errorUpdateVersion": "Fayl versiyası dəyişdirilib. Səhifə yenidən yüklənəcək.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "İnternet bağlantısı bərpa edildi və fayl versiyası dəyişdirildi.<br>İşə davam etməzdən əvvəl heç nəyin itirilmədiyindən əmin olmaq üçün faylı endirməli və ya onun məzmununu kopyalamalı, sonra bu səhifəni yenidən yükləməlisiniz.", "SSE.Controllers.Main.errorUserDrop": "<PERSON><PERSON> ha<PERSON> daxil olmaq mü<PERSON>.", "SSE.Controllers.Main.errorUsersExceed": "Qiymət planı ilə icazə verilən istifadəçilərin sayı maksimumu keçdi", "SSE.Controllers.Main.errorViewerDisconnect": "Bağlantı kəsildi. Siz hələ də sənədə baxa bilərs<PERSON>z, lakin əlaqə yenidən qurulana və səhifə təzələnənə qədər onu endirə və ya çap edə bilməzsiniz.", "SSE.Controllers.Main.errorWrongBracketsCount": "Daxil edilmiş düsturda xəta var.<br><PERSON><PERSON><PERSON><PERSON> sayda mötərizə istifadə olunub.", "SSE.Controllers.Main.errorWrongOperator": "Daxil edilmiş düsturda xəta. Yanlış operator istifadə olunub.<br>Xahiş edirəm xətanı düzəldin.", "SSE.Controllers.Main.errorWrongPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON> et<PERSON>yi<PERSON>z parol düz<PERSON>ün <PERSON>.", "SSE.Controllers.Main.errRemDuplicates": "Dublikat dəyərlər tapıldı və silindi: {0}, qalan unikal dəyərlər: {1}.", "SSE.Controllers.Main.leavePageText": "Bu cədvəldə yadda saxlanmamış dəyişiklikləriniz var. Onları saxlamaq üçün \"Bu Səhifədə Qalın\" və sonra \"Saxla\" hissəsinin üzərinə klikləyin. Bütün saxlanmamış dəyişiklikləri ləğv etmək üçün \"Bu səhifədən çıx\" hissəsinin üzərinə klikləyin.", "SSE.Controllers.Main.leavePageTextOnClose": "Bu cədvəldə yadda saxlanılmamış bütün dəyişikliklər silinəcək.<br> Onları saxlamaq üçün \"Ləğv et\" və \"Yadda saxla\" üzərinə klikləyin. Bütün saxlanmamış dəyişiklikləri ləğv etmək üçün \"OK\" düyməsini basın.", "SSE.Controllers.Main.loadFontsTextText": "Məlumat yüklənir...", "SSE.Controllers.Main.loadFontsTitleText": "Məlumat Yüklənir", "SSE.Controllers.Main.loadFontTextText": "Məlumat yüklənir...", "SSE.Controllers.Main.loadFontTitleText": "Məlumat Yüklənir", "SSE.Controllers.Main.loadImagesTextText": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON><PERSON> yüklə<PERSON>...", "SSE.Controllers.Main.loadImagesTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadImageTextText": "<PERSON>ə<PERSON><PERSON> yük<PERSON>ə<PERSON>...", "SSE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadingDocumentTitleText": "Elektron cədvəl yüklənir", "SSE.Controllers.Main.notcriticalErrorTitle": "Xəbərdarlıq", "SSE.Controllers.Main.openErrorText": "Faylı açan zaman xəta baş verdi.", "SSE.Controllers.Main.openTextText": "İş vərəqi açılır...", "SSE.Controllers.Main.openTitleText": "İş vərəqi Açılır", "SSE.Controllers.Main.pastInMergeAreaError": "Birləşdirilmiş xananın bir hissəsini dəyişdirmək mümkün deyil", "SSE.Controllers.Main.printTextText": "Elektron cədvəl çap edilir...", "SSE.Controllers.Main.printTitleText": "Elektron cədvəl Çap edilir", "SSE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n", "SSE.Controllers.Main.requestEditFailedMessageText": "<PERSON><PERSON><PERSON><PERSON> kimsə bu sənədi redaktə edir. Zəhmət olmasa bir az sonra yenə cəhd edin.", "SSE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON> rədd edildi", "SSE.Controllers.Main.saveErrorText": "<PERSON><PERSON><PERSON> yadda saxlayarkən xəta baş verdi.", "SSE.Controllers.Main.saveErrorTextDesktop": "Bu faylı saxlamaq və ya yaratmaq mümkün deyil.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> səbəblər: <br>1. <PERSON><PERSON> yalnız oxumaq üçündür. <br>2. <PERSON><PERSON> digər istifadəçilər tərəfindən redaktə olunur. <br>3. Disk doludur və ya xarabdır.", "SSE.Controllers.Main.saveTextText": "Elektron cədvəl saxlanır...", "SSE.Controllers.Main.saveTitleText": "Elektron cədvəlin saxlanması", "SSE.Controllers.Main.scriptLoadError": "Bağlantı çox yavaşdır, bə<PERSON> komponent<PERSON><PERSON><PERSON> yükləmək mümkün deyil. Səhifəni yenidən yükləyin.", "SSE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textApplyAll": "<PERSON><PERSON><PERSON><PERSON>n tənliklərə tətbiq edin", "SSE.Controllers.Main.textBuyNow": "<PERSON>eb <PERSON>ta daxil olun", "SSE.Controllers.Main.textChangesSaved": "<PERSON><PERSON><PERSON><PERSON><PERSON> də<PERSON>ər yadda sa<PERSON>lıb", "SSE.Controllers.Main.textClose": "Bağlayın", "SSE.Controllers.Main.textCloseTip": "İpucunu bağlamaq üçün k<PERSON>ləyin", "SSE.Controllers.Main.textConfirm": "Təsdiq", "SSE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "SSE.Controllers.Main.textContactUs": "Satışlarla əlaqə", "SSE.Controllers.Main.textContinue": "Continue", "SSE.Controllers.Main.textConvertEquation": "Bu tənlik tənlik redaktorunun artıq mövcud olmayan köhnə versiyası ilə yaradılmışdır. Bu tənliyi dəyişmək üçün onu Office Math ML formatına çevirin. <br> İndi çevrilsin?", "SSE.Controllers.Main.textCustomLoader": "Nə<PERSON><PERSON>rə alın ki, lisenziyanın şərtlərinə görə sizin yükləyicini dəyişmək hüququnuz yoxdur.<br>Qiymət almaq üçün Satış Departamentimizlə əlaqə saxlayın.", "SSE.Controllers.Main.textDisconnect": "Bağlantı itib", "SSE.Controllers.Main.textFillOtherRows": "Fill other rows", "SSE.Controllers.Main.textFormulaFilledAllRows": "Formula filled {0} rows have data. Filling other empty rows may take a few minutes.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Formula filled first {0} rows. Filling other empty rows may take a few minutes.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Formula filled only first {0} rows have data by memory save reason. There are other {1} rows have data in this sheet. You can fill them manually.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Formula filled only first {0} rows by memory save reason. Other rows in this sheet don't have data.", "SSE.Controllers.Main.textGuest": "Qonaq", "SSE.Controllers.Main.textHasMacros": "Faylda avtomatik makrolar var.<br><PERSON><PERSON><PERSON><PERSON><PERSON> işə salmaq istəyirsiniz?", "SSE.Controllers.Main.textKeep": "Keep", "SSE.Controllers.Main.textLearnMore": "Ətraflı məlumat", "SSE.Controllers.Main.textLoadingDocument": "Elektron cədvəl yüklənir", "SSE.Controllers.Main.textLongName": "128 simvoldan az olan ad daxil edin.", "SSE.Controllers.Main.textNeedSynchronize": "Ye<PERSON>l<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> var", "SSE.Controllers.Main.textNo": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textNoLicenseTitle": "Lisenziya limitinə çatdı", "SSE.Controllers.Main.textPaidFeature": "Ödənişli xü<PERSON>iyyət", "SSE.Controllers.Main.textPleaseWait": "Əməliyyat gözlənildiyindən daha çox vaxt apara bilər. <PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>, gözləyin...", "SSE.Controllers.Main.textReconnect": "Connection is restored", "SSE.Controllers.Main.textRemember": "<PERSON><PERSON><PERSON><PERSON><PERSON> fayllar üçün seçimimi yadda saxla", "SSE.Controllers.Main.textRememberMacros": "Remember my choice for all macros", "SSE.Controllers.Main.textRenameError": "İstifadəçi adı boş olmamalıdır.", "SSE.Controllers.Main.textRenameLabel": "Əməkdaşlıq üçün istifadə ediləcək ad daxil edin", "SSE.Controllers.Main.textReplace": "Replace", "SSE.Controllers.Main.textRequestMacros": "A macro makes a request to URL. Do you want to allow the request to the %1?", "SSE.Controllers.Main.textShape": "Forma", "SSE.Controllers.Main.textStrict": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textText": "Mətn", "SSE.Controllers.Main.textTryQuickPrint": "You have selected Quick print: the entire document will be printed on the last selected or default printer.<br>Do you want to continue?", "SSE.Controllers.Main.textTryUndoRedo": "Qaytar / Təkrarla funksiyaları sürətli birgə redaktə rejimi üçün qeyri-aktiv edilib. <br> <PERSON>g<PERSON>r istifadəçilərin müdaxiləsi olmadan faylı redaktə etmək və fayllarınızı göndərmək üçün ciddi birgə redaktə rejiminə keçmək üçün \"Ciddi rejim\" düyməsini klikləyin. dəyişikliklər yalnız siz onları saxladıqdan sonra baş verir. Qabaqcıl redaktor parametrlərindən istifadə edərək birgə redaktə rejimləri arasında keçid edə bilərsiniz.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Qaytar/Təkrarla funksiyaları Sürətli birgə redaktə rejimi üçün qeyri-aktiv edilib.", "SSE.Controllers.Main.textUndo": "Undo", "SSE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "SSE.Controllers.Main.textUpdating": "Updating", "SSE.Controllers.Main.textYes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.titleLicenseExp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> müddə<PERSON>di", "SSE.Controllers.Main.titleLicenseNotActive": "License not active", "SSE.Controllers.Main.titleServerVersion": "Redaktor <PERSON>", "SSE.Controllers.Main.titleUpdateVersion": "Version changed", "SSE.Controllers.Main.txtAccent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtAll": "(Hamısı)", "SSE.Controllers.Main.txtArt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtBasicShapes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtBlank": "(boş)", "SSE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtByField": "2%-in 1%-i", "SSE.Controllers.Main.txtCallouts": "İzahatlar", "SSE.Controllers.Main.txtCharts": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtClearFilter": "S<PERSON><PERSON>g<PERSON>ci Təmizlə", "SSE.Controllers.Main.txtColLbls": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtConfidential": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtDate": "<PERSON><PERSON>", "SSE.Controllers.Main.txtDays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtEditingMode": "Redaktə rejimini təyin edin...", "SSE.Controllers.Main.txtErrorLoadHistory": "Tarixçəni yükləmək prosesi uğursuz oldu", "SSE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtFile": "<PERSON><PERSON>", "SSE.Controllers.Main.txtGrandTotal": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtGroup": "Qrup", "SSE.Controllers.Main.txtHours": "Saatlar", "SSE.Controllers.Main.txtInfo": "Info", "SSE.Controllers.Main.txtLines": "Sə<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMath": "Riyaziyyat", "SSE.Controllers.Main.txtMinutes": "Dəqi<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMonths": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMultiSelect": "Çoxlu Seçim", "SSE.Controllers.Main.txtNone": "None", "SSE.Controllers.Main.txtOr": "1% və ya 2%", "SSE.Controllers.Main.txtPage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPageOf": "Səhifə 2%-in 1%-i", "SSE.Controllers.Main.txtPages": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPicture": "Picture", "SSE.Controllers.Main.txtPivotTable": "PivotTable", "SSE.Controllers.Main.txtPreparedBy": "tərə<PERSON>dən hazı<PERSON>ır", "SSE.Controllers.Main.txtPrintArea": "<PERSON><PERSON>_<PERSON>", "SSE.Controllers.Main.txtQuarter": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtQuarters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRectangles": "Düzbucaqlılar", "SSE.Controllers.Main.txtRow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRowLbls": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "SSE.Controllers.Main.txtScheme_Aspect": "Aspect", "SSE.Controllers.Main.txtScheme_Blue": "Blue", "SSE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "SSE.Controllers.Main.txtScheme_Blue_II": "Blue II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "SSE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "SSE.Controllers.Main.txtScheme_Green": "Green", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "SSE.Controllers.Main.txtScheme_Marquee": "Marquee", "SSE.Controllers.Main.txtScheme_Median": "Median", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Orange", "SSE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "SSE.Controllers.Main.txtScheme_Paper": "Paper", "SSE.Controllers.Main.txtScheme_Red": "Red", "SSE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "SSE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "SSE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "SSE.Controllers.Main.txtScheme_Violet": "Violet", "SSE.Controllers.Main.txtScheme_Violet_II": "Violet II", "SSE.Controllers.Main.txtScheme_Yellow": "Yellow", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "SSE.Controllers.Main.txtSeconds": "San<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSeries": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Xətt Çıxarışı 1 (Sərhəd və Vurğu Zolağı)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Xətt Çıxarışı 2 (Sərhəd və Vurğu Zolağı)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Xətt Çıxarışı 3 (Sərhəd və Vurğu Zolağı)", "SSE.Controllers.Main.txtShape_accentCallout1": "Xətt Çıxarışı 1 (Vurğu Zolağı)", "SSE.Controllers.Main.txtShape_accentCallout2": "Xətt Çıxarışı 2 (Vurğu Zolağı)", "SSE.Controllers.Main.txtShape_accentCallout3": "Xətt Çıxarışı 3 (Vurğu Zolağı)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Geri və ya Əvvəlki Düyməsi", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Başlama <PERSON>ü<PERSON>ə<PERSON>", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Boş Düymə", "SSE.Controllers.Main.txtShape_actionButtonDocument": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Bitmə Düyməsi", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "<PERSON>rəli və ya Növbəti Düyməsi", "SSE.Controllers.Main.txtShape_actionButtonHelp": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonInformation": "İnformasiya <PERSON>", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Film Düyməsi", "SSE.Controllers.Main.txtShape_actionButtonReturn": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonSound": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_arc": "<PERSON>ö<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentArrow": "Əyilmiş ox", "SSE.Controllers.Main.txtShape_bentConnector5": "Dirsəkvari Birləşdirici", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Dirsəkvari Oxlu Birləşdirici", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Dirsəkvari İkili Ox Birləşdiricisi", "SSE.Controllers.Main.txtShape_bentUpArrow": "Yuxarı Əyilmiş Ox", "SSE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_blockArc": "Qövs Bloku", "SSE.Controllers.Main.txtShape_borderCallout1": "Xətt Çıxarışı 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Xətt Çıxarışı 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Xətt Çıxarışı 3", "SSE.Controllers.Main.txtShape_bracePair": "İkiqat Fiqurlu Mötərizə", "SSE.Controllers.Main.txtShape_callout1": "Xətt Çıxarışı 1 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur)", "SSE.Controllers.Main.txtShape_callout2": "Xətt Çıxarışı 2 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur)", "SSE.Controllers.Main.txtShape_callout3": "Xətt Çıxarışı 3 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur)", "SSE.Controllers.Main.txtShape_can": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_chevron": "Chevron", "SSE.Controllers.Main.txtShape_chord": "Dairə", "SSE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cloud": "Bulud", "SSE.Controllers.Main.txtShape_cloudCallout": "Bulud Formalı Çıxarış", "SSE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Əyri Birləşdirici", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Əyri Ox birləşdiricisi", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "İki oxlu Əyri Ox Birləşdiricisi", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Aşağı Əyilmiş Ox", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Sola Əyilmiş Ox", "SSE.Controllers.Main.txtShape_curvedRightArrow": "<PERSON>ğa Ə<PERSON>lmiş Ox", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Yuxarı Əyilmiş Ox", "SSE.Controllers.Main.txtShape_decagon": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_diagStripe": "Diaqonal Zolağı", "SSE.Controllers.Main.txtShape_diamond": "Romb", "SSE.Controllers.Main.txtShape_dodecagon": "Onikibucaqlı", "SSE.Controllers.Main.txtShape_donut": "Donut", "SSE.Controllers.Main.txtShape_doubleWave": "İkiqat Dalğa", "SSE.Controllers.Main.txtShape_downArrow": "Aşağı Ox", "SSE.Controllers.Main.txtShape_downArrowCallout": "Aşağı Oxlu Çıxarış", "SSE.Controllers.Main.txtShape_ellipse": "Ellips", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Aşağı Əyilmiş Lent", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Yuxarı Əyilmiş Lent", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Axın diaqramı: Alternativ Proses", "SSE.Controllers.Main.txtShape_flowChartCollate": "Axın diaqramı: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartConnector": "Axın diaqramı: Birləşdirici", "SSE.Controllers.Main.txtShape_flowChartDecision": "Axın diaqramı: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDelay": "Axın diaqramı: Gecikmə", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Axın diaqramı: Ekran", "SSE.Controllers.Main.txtShape_flowChartDocument": "Axın diaqramı: Sənəd", "SSE.Controllers.Main.txtShape_flowChartExtract": "Axın diaqramı: <PERSON><PERSON><PERSON>rtma", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Axın diaqramı: Verilənlər", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Axın diaqramı: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Axın diaqramı: Maqnit Disk", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Axın diaqramı: Birbaşa Giriş Yaddaşı", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Axın diaqramı: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Giriş Yaddaşı", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Axın diaqramı: Əl ilə Daxiletmə", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Axın diaqramı: Əl ilə <PERSON>ə<PERSON>yyat", "SSE.Controllers.Main.txtShape_flowChartMerge": "Axın diaqramı: B<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Axın diaqramı: Çoxlu Sənəd", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Axın diaqramı: Səhi<PERSON>ə Xaricində Birləşdirici", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Axın diaqramı: Saxlanılan Verilənlər", "SSE.Controllers.Main.txtShape_flowChartOr": "Axın diaqramı: Və ya", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Axın diaqramı: Əvvəlcədən Müəyyən Edilmiş Proses", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Axın diaqramı: <PERSON><PERSON><PERSON>rl<PERSON>q", "SSE.Controllers.Main.txtShape_flowChartProcess": "Axın diaqramı: Proses", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Axın diaqramı: Kart", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Axın diaqramı: Perfolent", "SSE.Controllers.Main.txtShape_flowChartSort": "Axın diaqrmı: Çeşidlə", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Axın diaqramı: <PERSON>əm<PERSON>əmə qovşağı", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Axın diaqramı: Sonlandırıcı", "SSE.Controllers.Main.txtShape_foldedCorner": "Qatlanmış Künc", "SSE.Controllers.Main.txtShape_frame": "Ç<PERSON><PERSON><PERSON><PERSON>ə", "SSE.Controllers.Main.txtShape_halfFrame": "Çərçivənin Yarısı", "SSE.Controllers.Main.txtShape_heart": "Ürək", "SSE.Controllers.Main.txtShape_heptagon": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_hexagon": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_homePlate": "Beşbucaqlı", "SSE.Controllers.Main.txtShape_horizontalScroll": "Üfüqi Sürüşdürmə", "SSE.Controllers.Main.txtShape_irregularSeal1": "Partlayış 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Partlayış 2", "SSE.Controllers.Main.txtShape_leftArrow": "Sol Ox", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Sola Ox Çıxarışı", "SSE.Controllers.Main.txtShape_leftBrace": "Sol Fiqurlu Mötərizə", "SSE.Controllers.Main.txtShape_leftBracket": "<PERSON>", "SSE.Controllers.Main.txtShape_leftRightArrow": "Sol sağ ox", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Soldan Sağa Ox Çıxarışı", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Soldan Sağa Yuxarı Ox", "SSE.Controllers.Main.txtShape_leftUpArrow": "Sol Yuxarı Ox", "SSE.Controllers.Main.txtShape_lightningBolt": "İldırım vurması", "SSE.Controllers.Main.txtShape_line": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "Ox", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "İkitərəfli Ox", "SSE.Controllers.Main.txtShape_mathDivide": "<PERSON><PERSON><PERSON>ə", "SSE.Controllers.Main.txtShape_mathEqual": "B<PERSON><PERSON>ə<PERSON>", "SSE.Controllers.Main.txtShape_mathMinus": "Minus", "SSE.Controllers.Main.txtShape_mathMultiply": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathNotEqual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathPlus": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_moon": "Ay", "SSE.Controllers.Main.txtShape_noSmoking": "\"No\" simvolu", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Kəsikli Sağa Ox", "SSE.Controllers.Main.txtShape_octagon": "Səkkizbucaqlı", "SSE.Controllers.Main.txtShape_parallelogram": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_pentagon": "Beşbucaqlı", "SSE.Controllers.Main.txtShape_pie": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_plaque": "İşarə", "SSE.Controllers.Main.txtShape_plus": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_polyline1": "Cızma-qara", "SSE.Controllers.Main.txtShape_polyline2": "Sərbəst forma", "SSE.Controllers.Main.txtShape_quadArrow": "<PERSON><PERSON><PERSON> ox", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Dördoxlu Çıxarış", "SSE.Controllers.Main.txtShape_rect": "Düzbucaqlı", "SSE.Controllers.Main.txtShape_ribbon": "Aşağı Lent", "SSE.Controllers.Main.txtShape_ribbon2": "Üzü Yuxarı Lent", "SSE.Controllers.Main.txtShape_rightArrow": "<PERSON>ğ <PERSON>", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Sağ Oxlu Çıxarış", "SSE.Controllers.Main.txtShape_rightBrace": "Sağ fiqurlu mötərizə", "SSE.Controllers.Main.txtShape_rightBracket": "<PERSON>ğ <PERSON>", "SSE.Controllers.Main.txtShape_round1Rect": "Dairəvi Tək Künc Düzbucaqlı", "SSE.Controllers.Main.txtShape_round2DiagRect": "Dairəvi Diaqonal Künc Düzbucaqlı", "SSE.Controllers.Main.txtShape_round2SameRect": "Dairəvi Eyni Yan Künc Düzbucaqlı", "SSE.Controllers.Main.txtShape_roundRect": "Dai<PERSON>əvi <PERSON>nc <PERSON>ü<PERSON>bu<PERSON>qlı", "SSE.Controllers.Main.txtShape_rtTriangle": "Düzbucaqlı üçbucaq", "SSE.Controllers.Main.txtShape_smileyFace": "Gülən Üz", "SSE.Controllers.Main.txtShape_snip1Rect": "Bir Küncü Kəsik Düzbucaqlı", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Diaqonal Küncləri Kəsik Düzbucaqlı", "SSE.Controllers.Main.txtShape_snip2SameRect": "<PERSON><PERSON><PERSON> Tərəfdə Küncü Kəsik Düzbucaqlı", "SSE.Controllers.Main.txtShape_snipRoundRect": "Diaqonal Küncləri olan K<PERSON> və Dəyirmi Düzbucaqlı", "SSE.Controllers.Main.txtShape_spline": "Əyr<PERSON>", "SSE.Controllers.Main.txtShape_star10": "10 Guşəli Ulduz", "SSE.Controllers.Main.txtShape_star12": "12 Guşəli Ulduz", "SSE.Controllers.Main.txtShape_star16": "16 Guşəli Ulduz", "SSE.Controllers.Main.txtShape_star24": "24 <PERSON><PERSON><PERSON><PERSON><PERSON>lduz", "SSE.Controllers.Main.txtShape_star32": "32 Guşəli Ulduz", "SSE.Controllers.Main.txtShape_star4": "4 gu<PERSON>əli Ulduz", "SSE.Controllers.Main.txtShape_star5": "5-<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star6": "6 G<PERSON>ş<PERSON>li <PERSON>", "SSE.Controllers.Main.txtShape_star7": "7 <PERSON><PERSON>ş<PERSON>li ulduz", "SSE.Controllers.Main.txtShape_star8": "8 Guşəli Ulduz", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Ştrixli Sağa Ox", "SSE.Controllers.Main.txtShape_sun": "Gün<PERSON>ş", "SSE.Controllers.Main.txtShape_teardrop": "Gözyaşı damcısı", "SSE.Controllers.Main.txtShape_textRect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_trapezoid": "Trapesiya", "SSE.Controllers.Main.txtShape_triangle": "Üçbucaq", "SSE.Controllers.Main.txtShape_upArrow": "Yuxarı Ox", "SSE.Controllers.Main.txtShape_upArrowCallout": "Yuxarı Oxlu Çıxarış", "SSE.Controllers.Main.txtShape_upDownArrow": "Yuxarı Aşağı Ox", "SSE.Controllers.Main.txtShape_uturnArrow": "U-Dönmə Oxu", "SSE.Controllers.Main.txtShape_verticalScroll": "Şaquli Sürüşdürmə", "SSE.Controllers.Main.txtShape_wave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Oval Çıxarış", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Düzbucaqlı Çıxarış", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Dairəvi Düzbucaqlı Çıxarış", "SSE.Controllers.Main.txtSheet": "Sheet", "SSE.Controllers.Main.txtSlicer": "<PERSON>licer", "SSE.Controllers.Main.txtStarsRibbons": "Ulduzlar və Lentlər", "SSE.Controllers.Main.txtStyle_Bad": "Pis", "SSE.Controllers.Main.txtStyle_Calculation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Check_Cell": "İşarələmə Xanası", "SSE.Controllers.Main.txtStyle_Comma": "Verg<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Aydınlaşdırıcı Mətn", "SSE.Controllers.Main.txtStyle_Good": "Yaxşı", "SSE.Controllers.Main.txtStyle_Heading_1": "Başlıq 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Başlıq 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Başlıq 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Başlıq 4", "SSE.Controllers.Main.txtStyle_Input": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Linked_Cell": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Neutral": "Neytral", "SSE.Controllers.Main.txtStyle_Normal": "Normal", "SSE.Controllers.Main.txtStyle_Note": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Output": "Çıxış", "SSE.Controllers.Main.txtStyle_Percent": "Faiz", "SSE.Controllers.Main.txtStyle_Title": "Başlıq", "SSE.Controllers.Main.txtStyle_Total": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Warning_Text": "Xəbərda<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtTab": "Tab", "SSE.Controllers.Main.txtTable": "<PERSON><PERSON><PERSON><PERSON><PERSON>l", "SSE.Controllers.Main.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtUnlock": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtUnlockRange": "Diapazonun <PERSON>ini aç", "SSE.Controllers.Main.txtUnlockRangeDescription": "Bu diapazonu dəyişmək üçün parol daxil edin:", "SSE.Controllers.Main.txtUnlockRangeWarning": "Dəyişdirməyə çalışdığınız diapazon parolla qorunur.", "SSE.Controllers.Main.txtValues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtView": "View", "SSE.Controllers.Main.txtXAxis": "X Oxu", "SSE.Controllers.Main.txtYAxis": "Y Oxu", "SSE.Controllers.Main.txtYears": "İllər", "SSE.Controllers.Main.unknownErrorText": "<PERSON><PERSON><PERSON> xəta.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "B<PERSON><PERSON><PERSON>z dəstəklənmir.", "SSE.Controllers.Main.uploadDocExtMessage": "<PERSON><PERSON><PERSON> sənəd formatı.", "SSE.Controllers.Main.uploadDocFileCountMessage": "<PERSON><PERSON> bir sənəd yü<PERSON>ə<PERSON>.", "SSE.Controllers.Main.uploadDocSizeMessage": "<PERSON><PERSON><PERSON><PERSON> sənəd ölçüsü limiti keçildi.", "SSE.Controllers.Main.uploadImageExtMessage": "<PERSON><PERSON><PERSON> təsvir formatı.", "SSE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON> bir təsvir yü<PERSON>.", "SSE.Controllers.Main.uploadImageSizeMessage": "<PERSON>ə<PERSON><PERSON> çox böyükdür. Maksimum ölçü 25 MB-dır.", "SSE.Controllers.Main.uploadImageTextText": "<PERSON>ə<PERSON><PERSON> yük<PERSON>ə<PERSON>...", "SSE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.waitText": "L<PERSON><PERSON><PERSON><PERSON>n, gözləyin...", "SSE.Controllers.Main.warnBrowserIE9": "Tətbiqin IE9-da aşağı imkanları var. IE10 və ya daha yüksək olandan istifadə edin", "SSE.Controllers.Main.warnBrowserZoom": "Brauzerinizin cari böyütmə parametri tam dəstəklənmir. Ctrl+0 d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> basaraq defolt böyütməyə sıfırlayın.", "SSE.Controllers.Main.warnLicenseAnonymous": "Access denied for anonymous users.<br>This document will be opened for viewing only.", "SSE.Controllers.Main.warnLicenseBefore": "License not active.<br>Please contact your administrator.", "SSE.Controllers.Main.warnLicenseExceeded": "Siz %1 redaktorlarına eyni vaxtda qoşulma limitinə çatdınız. Bu sənəd yalnız baxmaq üçün açılacaq.<br>Ətraflı məlumat üçün inzibatçınızla əlaqə saxlayın.", "SSE.Controllers.Main.warnLicenseExp": "Lisenziyanızın vaxtı bitib.<br>Lisenziyanızı yeniləyin və səhifəni yeniləyin.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Lisenziyanın müddəti bitdi.<br><PERSON>ə<PERSON><PERSON>d redaktə funksiyasına giriş<PERSON>z yoxdur.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, inzibatçınızla əlaqə saxlayın.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Lisenziya yenilənməlidir.<br><PERSON>ənəd redaktə funksiyasına məhdud giriş<PERSON> var.<br><PERSON> giriş əldə etmək üçün inzibatçınızla əlaqə saxlayın.", "SSE.Controllers.Main.warnLicenseUsersExceeded": "%1 redaktor üçün istifadəçi limitinə çatdınız. Ətraflı öyrənmək üçün inzibatçınızla əlaqə saxlayın.", "SSE.Controllers.Main.warnNoLicense": "Siz %1 redaktorlarına eyni vaxtda qoşulma limitinə çatdınız. Bu sənəd yalnız baxmaq üçün açılacaq.<br>Şəxsi təkmilləşdirmə şərtləri üçün %1 satış komandası ilə əlaqə saxlayın.", "SSE.Controllers.Main.warnNoLicenseUsers": "%1 redaktor üçün istifadəçi limitinə çatdınız. Şəxsi təkmilləşdirmə şərtləri üçün %1 satış komandası ilə əlaqə saxlayın.", "SSE.Controllers.Main.warnProcessRightsChange": "Siz faylı redaktə etmək hüququnuzdan məhrum oldunuz.", "SSE.Controllers.PivotTable.strSheet": "Sheet", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "No actions with calculated items are allowed for this active cell.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Calculated items do not work with custom subtotals.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "An item name cannot be found. Check that you've typed name correctly and the item is present in the PivotTable report.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Averages, standard deviations, and variances are not supported when a PivotTable report has calculated items.", "SSE.Controllers.Print.strAllSheets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textFirstCol": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textFirstRow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textFrozenCols": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textFrozenRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sətirlər", "SSE.Controllers.Print.textInvalidRange": "XƏTA! Yanlış xana diapazonu", "SSE.Controllers.Print.textNoRepeat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textRepeat": "Təkrarlayın...", "SSE.Controllers.Print.textSelectRange": "Diapazon<PERSON>", "SSE.Controllers.Print.txtCustom": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.txtZoomToPage": "Zoom to page", "SSE.Controllers.Search.textInvalidRange": "ERROR! Invalid cells range", "SSE.Controllers.Search.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "SSE.Controllers.Search.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "SSE.Controllers.Search.textReplaceSuccess": "Search has been done. {0} occurrences have been replaced", "SSE.Controllers.Statusbar.errorLastSheet": "İş kitabında ən azı bir görünən iş vərəqi olmalıdır.", "SSE.Controllers.Statusbar.errorRemoveSheet": "İş vərəqini silmək mümkün deyil.", "SSE.Controllers.Statusbar.strSheet": "Və<PERSON><PERSON><PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>Connection is lost</b><br>Trying to connect. Please check connection settings.", "SSE.Controllers.Statusbar.textSheetViewTip": "Siz Vərəq Görünüşü rejimindəsiniz. Süzgəclər və çeşidləmə yalnız sizə və hələ də bu görünüşdə olanlara görünür.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Siz Vərəq Görünüşü rejimindəsiniz. Süzgəclər yalnız sizə və hələ də bu görünüşdə olanlara görünür.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Seçilmiş iş vərəqlərində məlumatlar ola bilər. Davam etmək istədiyinizdən əminsiniz?", "SSE.Controllers.Statusbar.zoomText": "<PERSON><PERSON><PERSON><PERSON> dəyi<PERSON> {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Yadda saxlayacağınız şrift cari cihazda mövcud deyil.<br>Mətn üslubu sistem şriftlərindən biri ilə göstəriləcək, yadda saxlanılan şrift mövcud olduqda istifadə olunacaq.<br>Davam etmək istəyirsiniz? ?", "SSE.Controllers.Toolbar.errorComboSeries": "Birləşdirilmiş diaqram yaratmaq üçün ən azı iki məlumat seriyası seçin.", "SSE.Controllers.Toolbar.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "XƏTA! Qrafik üzrə verilənlər seriyasının maksimum sayı 255-dir", "SSE.Controllers.Toolbar.errorStockChart": "Yanlış sıra ardıcıllığı. Səhm qrafikini yaratmaq üçün məlumatları vərəqdə aşağıdakı ardıcıllıqla yerləşdirin:<br> a<PERSON><PERSON><PERSON><PERSON><PERSON> qiyməti, maks<PERSON>um qiymət, minimum qiymət, ba<PERSON><PERSON><PERSON><PERSON> qiyməti.", "SSE.Controllers.Toolbar.helpCalcItems": "Work with calculated items in Pivot Tables.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Calculated items", "SSE.Controllers.Toolbar.helpFastUndo": "Easily undo changes while collaborating on sheets in Fast mode.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "\"Undo\" in real-time co-editing", "SSE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "SSE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textBracket": "Mötə<PERSON><PERSON>lə<PERSON>", "SSE.Controllers.Toolbar.textDirectional": "İstiqamətli", "SSE.Controllers.Toolbar.textFontSizeErr": "Daxil edilmiş dəyər yanlışdır.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 1 ilə 409 arasında rəqəmsal dəyər daxil edin", "SSE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textIndicator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textInsert": "Daxil edin", "SSE.Controllers.Toolbar.textIntegral": "İnteqrallar", "SSE.Controllers.Toolbar.textLargeOperator": "Böyük Operatorlar", "SSE.Controllers.Toolbar.textLimitAndLog": "Limitlər və Loqarifmlər", "SSE.Controllers.Toolbar.textLongOperation": "<PERSON><PERSON><PERSON> ə<PERSON>ə<PERSON>t", "SSE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "SSE.Controllers.Toolbar.textOperator": "Operatorlar", "SSE.Controllers.Toolbar.textPivot": "Pivot Cədvəli", "SSE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRating": "Qi<PERSON>ətlə<PERSON>lə<PERSON>", "SSE.Controllers.Toolbar.textRecentlyUsed": "Recently used", "SSE.Controllers.Toolbar.textScript": "Skriptlər", "SSE.Controllers.Toolbar.textShapes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textSymbols": "Simvollar", "SSE.Controllers.Toolbar.textWarning": "Xəbərdarlıq", "SSE.Controllers.Toolbar.txtAccent_Accent": "Dəqiq", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Yuxarıda sağ-sol ox", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Sola Yuxarı Ox", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Sağa yuxarı ox", "SSE.Controllers.Toolbar.txtAccent_Bar": "Zolaq", "SSE.Controllers.Toolbar.txtAccent_BarBot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> xətt", "SSE.Controllers.Toolbar.txtAccent_BarTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> xətt", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> düstur (yer tutucu ilə)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> düstur (nümunə)", "SSE.Controllers.Toolbar.txtAccent_Check": "İşarələ", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Alt fiqurlu mötərizə", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Üst fiqurlu mötərizə", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vektor A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "Üstündə xətt olan <PERSON>", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "Yuxarıdan xətt ilə x XOR y", "SSE.Controllers.Toolbar.txtAccent_DDDot": "<PERSON>ç <PERSON>", "SSE.Controllers.Toolbar.txtAccent_DDot": "İki Nöqtə", "SSE.Controllers.Toolbar.txtAccent_Dot": "Nöqtə", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Yu<PERSON>rıdan ikiqat xətt", "SSE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Aşağıdakı simvolu qruplaşdır", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Yuxarıdakı simvolu qruplaşdır", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Sola Yuxarı Harpun", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "<PERSON>ğa yuxarı <PERSON>un", "SSE.Controllers.Toolbar.txtAccent_Hat": "Qapaq", "SSE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON> i<PERSON>", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Tilda", "SSE.Controllers.Toolbar.txtBracket_Angle": "Mötə<PERSON><PERSON>lə<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mö<PERSON>ə<PERSON>ələr", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mö<PERSON>ə<PERSON>ələr", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_Curve": "Mötə<PERSON><PERSON>lə<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mö<PERSON>ə<PERSON>ələr", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Böyük/kiçik hərflər (iki şərt)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Böyük/kiçik hərflər (üç şərt)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Obyek<PERSON><PERSON>r yığı<PERSON>ı", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Obyek<PERSON><PERSON>r yığı<PERSON>ı", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Böyük/kiçik hərf nümunəsi", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Binom əmsalı", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Binom əmsalı", "SSE.Controllers.Toolbar.txtBracket_Line": "Mötə<PERSON><PERSON>lə<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Mötə<PERSON><PERSON>lə<PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Mötə<PERSON><PERSON>lə<PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_Round": "Mötə<PERSON><PERSON>lə<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mö<PERSON>ə<PERSON>ələr", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_Square": "Mötə<PERSON><PERSON>lə<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Mötə<PERSON><PERSON>lə<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Mötə<PERSON><PERSON>lə<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Mötə<PERSON><PERSON>lə<PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Mötə<PERSON><PERSON>lə<PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Mötə<PERSON><PERSON>lə<PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Tək mötərizə", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Tək mötərizə", "SSE.Controllers.Toolbar.txtDeleteCells": "Xanaları silin", "SSE.Controllers.Toolbar.txtExpand": "Genişləndir və sırala", "SSE.Controllers.Toolbar.txtExpandSort": "Seçimin yanındakı məlumatlar çeşidlənməyəcək. Bitişik məlumatları daxil etmək üçün seçimi genişləndirmək və ya yalnız hazırda seçilmiş xanaları çeşidləməyə davam etmək istəyirsiniz?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Əyri fraksiya", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Diferensial", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Diferensial", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Diferensial", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Diferensial", "SSE.Controllers.Toolbar.txtFractionHorizontal": "<PERSON><PERSON><PERSON> frak<PERSON>", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi böl 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Kiçik fraksiya", "SSE.Controllers.Toolbar.txtFractionVertical": "Şaquli adi kəsr", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "<PERSON><PERSON><PERSON> kos<PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Hiperbolik tərs kosinus <PERSON>ı", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Tərs kotangent funksiyası", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Hiperbolik tərs kotangent funksiyası", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "<PERSON>ə<PERSON> kosekant <PERSON>ı", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Hiperbolik tərs kosekant funksiyası", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "<PERSON><PERSON><PERSON> sekant <PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Hiperbolik tərs sekant <PERSON>ı", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Tərs sinus funksiyası", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Hiperbolik tərs sinus funksiyası", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Tərs tangens funksiyası", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Hiperbolik tərs tangens funksiyası", "SSE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Hiperbolik kosinus <PERSON>", "SSE.Controllers.Toolbar.txtFunction_Cot": "Kotangent funksiyası", "SSE.Controllers.Toolbar.txtFunction_Coth": "Hiperbolik kotangent funksiyası", "SSE.Controllers.Toolbar.txtFunction_Csc": "Kosekant funksiyası", "SSE.Controllers.Toolbar.txtFunction_Csch": "Hiperbolik kosekant funksiyası", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sin teta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON> dü<PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sech": "Hiperbolik sekant funksiyası", "SSE.Controllers.Toolbar.txtFunction_Sin": "Sinus funksiyası", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Hiperbolik sinus funksiyası", "SSE.Controllers.Toolbar.txtFunction_Tan": "Tangens funksiyası", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Hiperbolik tangens funksiyası", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Custom", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Data and model", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Good, bad and neutral", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "No name", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Number format", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Themed cell styles", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Titles and headings", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Custom", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Light": "Açıq", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Medium", "SSE.Controllers.Toolbar.txtInsertCells": "Xanaları daxil edin", "SSE.Controllers.Toolbar.txtIntegral": "İnteqral", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Diferensial teta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Differensial x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Differensial y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "İnteqral", "SSE.Controllers.Toolbar.txtIntegralDouble": "İkiqat inteqral", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "İkiqat inteqral", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "İkiqat inteqral", "SSE.Controllers.Toolbar.txtIntegralOriented": "Kontur inteqralı", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Kontur inteqralı", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "<PERSON>əth inteqralı", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "<PERSON>əth inteqralı", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "<PERSON>əth inteqralı", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Kontur inteqralı", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Həcm inteqralı", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Həcm inteqralı", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Həcm inteqralı", "SSE.Controllers.Toolbar.txtIntegralSubSup": "İnteqral", "SSE.Controllers.Toolbar.txtIntegralTriple": "Üçqat inteqral", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Üçqat inteqral", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Üçqat inteqral", "SSE.Controllers.Toolbar.txtInvalidRange": "XƏTA! Yanlış xana diapazonu", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON>ır", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON>ır", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON>ır", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON>ır", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON>ır", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON>ə məhsul", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "<PERSON><PERSON><PERSON>ə məhsul", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "<PERSON><PERSON><PERSON>ə məhsul", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "<PERSON><PERSON><PERSON>ə məhsul", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "<PERSON><PERSON><PERSON>ə məhsul", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Birləşmə", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "K<PERSON>sişmə", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "K<PERSON>sişmə", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "K<PERSON>sişmə", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "K<PERSON>sişmə", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "K<PERSON>sişmə", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Birləşmə", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Birləşmə", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Birləşmə", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Birləşmə", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Birləşmə", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON><PERSON> nümunə", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Limit", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON><PERSON><PERSON><PERSON> loqari<PERSON>m", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Loqarifm", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Loqarifm", "SSE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "SSE.Controllers.Toolbar.txtLockSort": "Seçiminizin yanında məlumat tapılıb, lakin sizin həmin xanaları dəyişmək üçün kifayət qədər icazəniz yoxdur.<br>Cari seçimlə davam etmək istəyirsiniz?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 boş matris", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 boş matris", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 boş matris", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 boş matris", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Mötərizədə boş matris", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Mötərizədə boş matris", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Mötərizədə boş matris", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Mötərizədə boş matris", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 boş matris", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 boş matris", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 boş matris", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 boş matris", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON><PERSON> xətt nö<PERSON>ə<PERSON>ə<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Ortaxətt nöqtələri", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diaqonal nöqtələri", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Şaquli <PERSON>ələ<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Mötərizədə Seyrək Matris", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Mötərizədə Seyrək Matris", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 şəxsiyyət matrisi", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 şəxsiyyət matrisi", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 şəxsiyyət matrisi", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 şəxsiyyət matrisi", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "<PERSON>şağ<PERSON>da sağ-sol ox", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Yuxarıda sağ-sol ox", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Sola Yuxarı Ox", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Sola Yuxarı Ox", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Sağa aşağı ox", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Sağa yuxarı ox", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "İki nöqtə bərabər", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Axınlar", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta Axınlar", "SSE.Controllers.Toolbar.txtOperator_Definition": "Tə<PERSON><PERSON>ə görə bərabərdir", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta bərabərdir", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "<PERSON>şağ<PERSON>da sağ-sol ox", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Yuxarıda sağ-sol ox", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Sola Yuxarı Ox", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Sola Yuxarı Ox", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Sağa aşağı ox", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Sağa yuxarı ox", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON><PERSON><PERSON><PERSON> bərabər", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON>ü<PERSON>", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Radikal", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Radikal", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Dərəcə ilə kvadrat kök", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Dərə<PERSON>ə ilə radikal", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Kvadrat kök", "SSE.Controllers.Toolbar.txtScriptCustom_1": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "S<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "Aşağı İndeks", "SSE.Controllers.Toolbar.txtScriptSubSup": "Aşağı İndeks-Yuxarı İndeks", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Sol aşağı indeks-yuxarı indeks", "SSE.Controllers.Toolbar.txtScriptSup": "Yuxarı İndeks", "SSE.Controllers.Toolbar.txtSorting": "Sıralama", "SSE.Controllers.Toolbar.txtSortSelected": "Seçilmiş sıralama", "SSE.Controllers.Toolbar.txtSymbol_about": "<PERSON>ə<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_additional": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON><PERSON><PERSON> olar ki bərabərdir:", "SSE.Controllers.Toolbar.txtSymbol_ast": "Asterisk Operatoru", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "<PERSON>er operatoru", "SSE.Controllers.Toolbar.txtSymbol_cap": "K<PERSON>sişmə", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cdots": "<PERSON><PERSON> xətt ü<PERSON>üqi ellips", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Təx<PERSON>ən bərabə<PERSON>r:", "SSE.Controllers.Toolbar.txtSymbol_cup": "Birləşmə", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Aşağıya sağa diaqonal üç nöqtə", "SSE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON>lmə işarəsi", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Aşağı ox", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Boş çoxluq", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "B<PERSON><PERSON>ə<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Oxşardır", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "Mövcuddur", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Faktorial", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Fahrenheit Dərəcəsi", "SSE.Controllers.Toolbar.txtSymbol_forall": "Hamı ü<PERSON>ün", "SSE.Controllers.Toolbar.txtSymbol_gamma": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_geq": "-dən bö<PERSON><PERSON>k və ya ona bərabər", "SSE.Controllers.Toolbar.txtSymbol_gg": "-dən çox böyükdür", "SSE.Controllers.Toolbar.txtSymbol_greater": "-də<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_in": "Elementi", "SSE.Controllers.Toolbar.txtSymbol_inc": "Artım", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Sonsuzluq", "SSE.Controllers.Toolbar.txtSymbol_iota": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Sol ox", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Sol-sağ ox", "SSE.Controllers.Toolbar.txtSymbol_leq": "-dən az və ya bərabərdir", "SSE.Controllers.Toolbar.txtSymbol_less": "-dən az", "SSE.Controllers.Toolbar.txtSymbol_ll": "-dən çox kiçikdir", "SSE.Controllers.Toolbar.txtSymbol_minus": "Minus", "SSE.Controllers.Toolbar.txtSymbol_mp": "Minus və Plyus", "SSE.Controllers.Toolbar.txtSymbol_mu": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ni": "Üzv kimi da<PERSON>ir", "SSE.Controllers.Toolbar.txtSymbol_not": "İşarə yoxdur", "SSE.Controllers.Toolbar.txtSymbol_notexists": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_o": "Omikron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omeqa", "SSE.Controllers.Toolbar.txtSymbol_partial": "Qismən diferensial", "SSE.Controllers.Toolbar.txtSymbol_percent": "Faiz", "SSE.Controllers.Toolbar.txtSymbol_phi": "Fi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_pm": "<PERSON><PERSON><PERSON> minus", "SSE.Controllers.Toolbar.txtSymbol_propto": "ilə mütənasibdir", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kök", "SSE.Controllers.Toolbar.txtSymbol_qed": "İsbatın <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Yuxarıya Sağa Diaqonal Üç Nöqtə", "SSE.Controllers.Toolbar.txtSymbol_rho": "Ro", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Sağ ox", "SSE.Controllers.Toolbar.txtSymbol_sigma": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Radikal işarə", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON>una görə də", "SSE.Controllers.Toolbar.txtSymbol_theta": "Teta", "SSE.Controllers.Toolbar.txtSymbol_times": "Çoxalma i<PERSON>arə<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Yuxarı ox", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "İpsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon variantı", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Fi variantı", "SSE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON>ı", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Ro variantı", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Siqma variantı", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Şaquli ellips", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Cədvəl Üslubu Tünd", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Cədvəl Üslubu Açıq", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Cədvəl Üslubu Orta", "SSE.Controllers.Toolbar.warnLongOperation": "<PERSON><PERSON><PERSON> yetirəcəyiniz əməliyyatın tamamlanması xeyli vaxt apara bilər.<br>Davam etmək istədiyinizdən əminsiniz?", "SSE.Controllers.Toolbar.warnMergeLostData": "Birləşdirilmiş xanada yalnız yuxarı sol xanadakı məlumatlar qalacaq. <br>Davam etmək istədiyinizdən əminsiniz?", "SSE.Controllers.Toolbar.warnNoRecommended": "To create a chart, select the cells that contain the data you'd like to use.<br>If you have names for the rows and columns and you'd like use them as labels, include them in your selection.", "SSE.Controllers.Viewport.textFreezePanes": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Viewport.textFreezePanesShadow": "Dondurulmuş Panellərin Kölgəsini gö<PERSON>ərin", "SSE.Controllers.Viewport.textHideFBar": "Düstur Zolağını Gizlədin", "SSE.Controllers.Viewport.textHideGridlines": "<PERSON> xə<PERSON><PERSON><PERSON>", "SSE.Controllers.Viewport.textHideHeadings": "Başlıqları Gizlədin", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Onluq a<PERSON>ı<PERSON>ı<PERSON>ı<PERSON>ı", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Minlərlə ayırıcı", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Rəqəmsal məlumatları tanımaq üçün istifadə edilən parametrlər", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Text qualifier", "SSE.Views.AdvancedSeparatorDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> parametrlər", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(none)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.textAddSelection": "Filtr etmək üçün cari seçimi əlavə edin", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Boş<PERSON>qlar}", "SSE.Views.AutoFilterDialog.textSelectAll": "Hamısını Seçin", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Bütün Axtarış Nəticələrini Seçin", "SSE.Views.AutoFilterDialog.textWarning": "Xəbərdarlıq", "SSE.Views.AutoFilterDialog.txtAboveAve": "<PERSON><PERSON> Qiymətdən Yuxarı", "SSE.Views.AutoFilterDialog.txtAfter": "After...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "All dates in the period", "SSE.Views.AutoFilterDialog.txtApril": "April", "SSE.Views.AutoFilterDialog.txtAugust": "August", "SSE.Views.AutoFilterDialog.txtBefore": "Before...", "SSE.Views.AutoFilterDialog.txtBegins": "İlə başlayır...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Orta Qiymətdən Aşağı", "SSE.Views.AutoFilterDialog.txtBetween": "Arasında...", "SSE.Views.AutoFilterDialog.txtClear": "Təmizlə", "SSE.Views.AutoFilterDialog.txtContains": "Ehtiva edir...", "SSE.Views.AutoFilterDialog.txtDateFilter": "Date filter", "SSE.Views.AutoFilterDialog.txtDecember": "December", "SSE.Views.AutoFilterDialog.txtEmpty": "<PERSON><PERSON> daxil edin", "SSE.Views.AutoFilterDialog.txtEnds": "İlə başa çatır...", "SSE.Views.AutoFilterDialog.txtEquals": "Bərabərdir...", "SSE.Views.AutoFilterDialog.txtFebruary": "February", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "<PERSON>ana rənginə görə süzgəc", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Şrift Rənginə görə Süzgəc", "SSE.Views.AutoFilterDialog.txtGreater": "-dən b<PERSON> ", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "-dən bö<PERSON><PERSON>k və ya ona bərabər", "SSE.Views.AutoFilterDialog.txtJanuary": "January", "SSE.Views.AutoFilterDialog.txtJuly": "July", "SSE.Views.AutoFilterDialog.txtJune": "June", "SSE.Views.AutoFilterDialog.txtLabelFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtLastMonth": "Last month", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Last quarter", "SSE.Views.AutoFilterDialog.txtLastWeek": "Last week", "SSE.Views.AutoFilterDialog.txtLastYear": "Last year", "SSE.Views.AutoFilterDialog.txtLess": "-dən azdır...", "SSE.Views.AutoFilterDialog.txtLessEquals": "-dən az və ya bərabərdir...", "SSE.Views.AutoFilterDialog.txtMarch": "March", "SSE.Views.AutoFilterDialog.txtMay": "May", "SSE.Views.AutoFilterDialog.txtNextMonth": "Next month", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Next quarter", "SSE.Views.AutoFilterDialog.txtNextWeek": "Next week", "SSE.Views.AutoFilterDialog.txtNextYear": "Next year", "SSE.Views.AutoFilterDialog.txtNotBegins": "İlə başlamır...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Arasında deyil...", "SSE.Views.AutoFilterDialog.txtNotContains": "Ehtiva etmir...", "SSE.Views.AutoFilterDialog.txtNotEnds": "İlə başa çatmır...", "SSE.Views.AutoFilterDialog.txtNotEquals": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "SSE.Views.AutoFilterDialog.txtNovember": "November", "SSE.Views.AutoFilterDialog.txtNumFilter": "Nömrə süzgəci", "SSE.Views.AutoFilterDialog.txtOctober": "October", "SSE.Views.AutoFilterDialog.txtQuarter1": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Quarter 1", "SSE.Views.AutoFilterDialog.txtReapply": "<PERSON>ə<PERSON><PERSON> tətbiq edin", "SSE.Views.AutoFilterDialog.txtSeptember": "September", "SSE.Views.AutoFilterDialog.txtSortCellColor": "<PERSON>ana rənginə görə sırala", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Şrift rənginə görə sırala", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Ən Böyükdən ən Kiçiyə doğru Sırala", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Ən Kiçikdən ən Böyüyə doğru sırala", "SSE.Views.AutoFilterDialog.txtSortOption": "Daha çox çeşidləmə seçimləri...", "SSE.Views.AutoFilterDialog.txtTextFilter": "<PERSON>ə<PERSON>n <PERSON>g<PERSON>", "SSE.Views.AutoFilterDialog.txtThisMonth": "This Month", "SSE.Views.AutoFilterDialog.txtThisQuarter": "This quarter", "SSE.Views.AutoFilterDialog.txtThisWeek": "This week", "SSE.Views.AutoFilterDialog.txtThisYear": "This Year", "SSE.Views.AutoFilterDialog.txtTitle": "Süzgəc", "SSE.Views.AutoFilterDialog.txtToday": "Today", "SSE.Views.AutoFilterDialog.txtTomorrow": "Tomorrow", "SSE.Views.AutoFilterDialog.txtTop10": "İlk 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtYearToDate": "Year to date", "SSE.Views.AutoFilterDialog.txtYesterday": "Yesterday", "SSE.Views.AutoFilterDialog.warnFilterError": "<PERSON>ə<PERSON>ər süzgəcini tətbiq etmək üçün Dəyərlər sahəsində ən azı bir sahəyə ehtiyacınız var.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Ən azı bir dəyər seçmə<PERSON>z", "SSE.Views.CellEditor.textManager": "<PERSON>", "SSE.Views.CellEditor.tipFormula": "Funksiya daxil edin", "SSE.Views.CellRangeDialog.errorMaxRows": "XƏTA! Qrafik üzrə verilənlər seriyasının maksimum sayı 255-dir", "SSE.Views.CellRangeDialog.errorStockChart": "Yanlış sıra ardıcıllığı. Səhm qrafikini yaratmaq üçün məlumatları vərəqdə aşağıdakı ardıcıllıqla yerləşdirin:<br> a<PERSON><PERSON><PERSON><PERSON><PERSON> qiyməti, maks<PERSON>um qiymət, minimum qiymət, ba<PERSON><PERSON><PERSON><PERSON> qiyməti.", "SSE.Views.CellRangeDialog.txtEmpty": "Bu sahə tələb olunur", "SSE.Views.CellRangeDialog.txtInvalidRange": "XƏTA! Yanlış xana diapazonu", "SSE.Views.CellRangeDialog.txtTitle": "Məlumatı Diapazonunu Seçin", "SSE.Views.CellSettings.strShrink": "Sığışdırmaq üçün sıxın", "SSE.Views.CellSettings.strWrap": "Mətni sətirdən-sətrə keçirin", "SSE.Views.CellSettings.textAngle": "Bucaq", "SSE.Views.CellSettings.textBackColor": "<PERSON><PERSON><PERSON> fon rəngi", "SSE.Views.CellSettings.textBackground": "<PERSON><PERSON><PERSON> fon rəngi", "SSE.Views.CellSettings.textBorderColor": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textClearRule": "Qaydaları Təmizləyin", "SSE.Views.CellSettings.textColor": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textColorScales": "Rəng <PERSON>ı", "SSE.Views.CellSettings.textCondFormat": "Şərti Formatlaşdırma", "SSE.Views.CellSettings.textControl": "Mətn İdarə elementi", "SSE.Views.CellSettings.textDataBars": "Məlumat Zolaqları", "SSE.Views.CellSettings.textDirection": "İstiqamət", "SSE.Views.CellSettings.textFill": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textForeground": "Ön plan rəngi", "SSE.Views.CellSettings.textGradient": "Qradiyent nöqtələri", "SSE.Views.CellSettings.textGradientColor": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textIndent": "Boşluq", "SSE.Views.CellSettings.textItems": "Element<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textLinear": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textManageRule": "Qaydaları İdarə edin", "SSE.Views.CellSettings.textNewRule": "<PERSON><PERSON>", "SSE.Views.CellSettings.textNoFill": "Doldurulmasın", "SSE.Views.CellSettings.textOrientation": "Mə<PERSON>n İstiq<PERSON>ə<PERSON>", "SSE.Views.CellSettings.textPattern": "Naxış", "SSE.Views.CellSettings.textPatternFill": "Naxış", "SSE.Views.CellSettings.textPosition": "Mövq<PERSON>", "SSE.Views.CellSettings.textRadial": "Radial", "SSE.Views.CellSettings.textSelectBorders": "Yu<PERSON>r<PERSON>da seçilmiş tətbiq üslubunu dəyişdirmək istədiyiniz sərhədləri seçin", "SSE.Views.CellSettings.textSelection": "<PERSON><PERSON> se<PERSON>n", "SSE.Views.CellSettings.textThisPivot": "<PERSON><PERSON>n sahədən", "SSE.Views.CellSettings.textThisSheet": "Bu iş vərəqindən", "SSE.Views.CellSettings.textThisTable": "Bu cədvəldən", "SSE.Views.CellSettings.tipAddGradientPoint": "Qradiyent nöqtəsi əlavə edin", "SSE.Views.CellSettings.tipAll": "Xarici sərhədi və bütün daxili xətləri təyin edin", "SSE.Views.CellSettings.tipBottom": "<PERSON><PERSON><PERSON><PERSON> xarici alt sərhədi təyin edin", "SSE.Views.CellSettings.tipDiagD": "Diaqonal Aşağı Sərhədi təyin edin", "SSE.Views.CellSettings.tipDiagU": "Diaqonal Yuxarı Sərhədi təyin edin", "SSE.Views.CellSettings.tipInner": "<PERSON><PERSON><PERSON>z daxili xətləri təyin edin", "SSE.Views.CellSettings.tipInnerHor": "<PERSON><PERSON><PERSON>z üfüqi daxili xətləri təyin edin", "SSE.Views.CellSettings.tipInnerVert": "<PERSON><PERSON><PERSON>z şaquli daxili xətləri təyin edin", "SSE.Views.CellSettings.tipLeft": "<PERSON><PERSON><PERSON><PERSON> xarici sol sərhədi təyin edin", "SSE.Views.CellSettings.tipNone": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> olmamasını təyin edin", "SSE.Views.CellSettings.tipOuter": "<PERSON><PERSON><PERSON><PERSON> xarici sərhədi təyin edin", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Qradiyent nöqtəni silin", "SSE.Views.CellSettings.tipRight": "<PERSON><PERSON><PERSON>z xarici sağ sərhədi təyin edin", "SSE.Views.CellSettings.tipTop": "<PERSON><PERSON><PERSON><PERSON> xarici üst sərhədi təyin edin", "SSE.Views.ChartDataDialog.errorInFormula": "Da<PERSON>l etdiyiniz düstur<PERSON> xəta var.", "SSE.Views.ChartDataDialog.errorInvalidReference": "İstinad etibarlı deyil. İstinad açıq iş vərəqinə olmalıdır.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Qrafik üzrə seriyalardakı nöqtələrin maksimum sayı 4096-dır.", "SSE.Views.ChartDataDialog.errorMaxRows": "Qrafik üzrə verilənlər seriyasının maksimum sayı 255-dir.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "İstinad etibarlı deyil. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, də<PERSON><PERSON><PERSON><PERSON><PERSON>, öl<PERSON><PERSON>l<PERSON>r və ya məlumat etiketləri üçün istinadlar tək xana, sıra və ya sütun olmalıdır.", "SSE.Views.ChartDataDialog.errorNoValues": "Qrafik yaratmaq üçün seriyada ən azı bir dəyər olmalıdır.", "SSE.Views.ChartDataDialog.errorStockChart": "Yanlış sıra ardıcıllığı. Səhm qrafikini yaratmaq üçün məlumatları vərəqdə aşağıdakı ardıcıllıqla yerləşdirin:<br> a<PERSON><PERSON><PERSON><PERSON><PERSON> qiyməti, maks<PERSON>um qiymət, minimum qiymət, ba<PERSON><PERSON><PERSON><PERSON> qiyməti.", "SSE.Views.ChartDataDialog.textAdd": "<PERSON><PERSON><PERSON> edin", "SSE.Views.ChartDataDialog.textCategory": "Üfüqi(Kateqoriya) Ox Etiketləri", "SSE.Views.ChartDataDialog.textData": "<PERSON><PERSON><PERSON> verilən di<PERSON>u", "SSE.Views.ChartDataDialog.textDelete": "Silin", "SSE.Views.ChartDataDialog.textDown": "Aşağı", "SSE.Views.ChartDataDialog.textEdit": "Redaktə edin", "SSE.Views.ChartDataDialog.textInvalidRange": "Yanlış xana di<PERSON>", "SSE.Views.ChartDataDialog.textSelectData": "Məlumatı seçin", "SSE.Views.ChartDataDialog.textSeries": "<PERSON><PERSON> (Sıralar)", "SSE.Views.ChartDataDialog.textSwitch": "Sətri/Sütunu <PERSON>", "SSE.Views.ChartDataDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textUp": "Yuxarıya", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Da<PERSON>l etdiyiniz düstur<PERSON> xəta var.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "İstinad etibarlı deyil. İstinad açıq iş vərəqinə olmalıdır.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Qrafik üzrə seriyalardakı nöqtələrin maksimum sayı 4096-dır.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Qrafik üzrə verilənlər seriyasının maksimum sayı 255-dir.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "İstinad etibarlı deyil. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, də<PERSON><PERSON><PERSON><PERSON><PERSON>, öl<PERSON><PERSON>l<PERSON>r və ya məlumat etiketləri üçün istinadlar tək xana, sıra və ya sütun olmalıdır.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Qrafik yaratmaq üçün seriyada ən azı bir dəyər olmalıdır.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Yanlış sıra ardıcıllığı. Səhm qrafikini yaratmaq üçün məlumatları vərəqdə aşağıdakı ardıcıllıqla yerləşdirin:<br> a<PERSON><PERSON><PERSON><PERSON><PERSON> qiyməti, maks<PERSON>um qiymət, minimum qiymət, ba<PERSON><PERSON><PERSON><PERSON> qiyməti.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Yanlış xana di<PERSON>", "SSE.Views.ChartDataRangeDialog.textSelectData": "Məlumatı seçin", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "<PERSON><PERSON> etike<PERSON> di<PERSON>", "SSE.Views.ChartDataRangeDialog.txtChoose": "Diapazon<PERSON>", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "<PERSON><PERSON><PERSON> adı", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "<PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Sıranı redaktə edin", "SSE.Views.ChartDataRangeDialog.txtValues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtXValues": "X Dəyərləri", "SSE.Views.ChartDataRangeDialog.txtYValues": "<PERSON>", "SSE.Views.ChartSettings.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartSettings.strLineWeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "Şablon", "SSE.Views.ChartSettings.text3dDepth": "Depth (% of base)", "SSE.Views.ChartSettings.text3dHeight": "Height (% of base)", "SSE.Views.ChartSettings.text3dRotation": "3D Rotation", "SSE.Views.ChartSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textAutoscale": "Autoscale", "SSE.Views.ChartSettings.textBorderSizeErr": "Daxil edilmiş dəyər yanlışdır.<br>0 pt ilə 1584 pt arasında dəyər daxil edin.", "SSE.Views.ChartSettings.textChangeType": "<PERSON><PERSON><PERSON><PERSON> də<PERSON>", "SSE.Views.ChartSettings.textChartType": "Diaq<PERSON>ın Növünü <PERSON>", "SSE.Views.ChartSettings.textDefault": "Default Rotation", "SSE.Views.ChartSettings.textDown": "Down", "SSE.Views.ChartSettings.textEditData": "Veriləni və Məkanı Redaktə edin", "SSE.Views.ChartSettings.textFirstPoint": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textHeight": "Hündürlük", "SSE.Views.ChartSettings.textHighPoint": "Yüksək Nöqtə", "SSE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textLastPoint": "Son <PERSON>ö<PERSON>ə", "SSE.Views.ChartSettings.textLeft": "Left", "SSE.Views.ChartSettings.textLowPoint": "Aşağı Nöqtə", "SSE.Views.ChartSettings.textMarkers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textNarrow": "Narrow field of view", "SSE.Views.ChartSettings.textNegativePoint": "Neqativ Nöqtə", "SSE.Views.ChartSettings.textPerspective": "Perspective", "SSE.Views.ChartSettings.textRanges": "Məlumat Diapazonu", "SSE.Views.ChartSettings.textRight": "Right", "SSE.Views.ChartSettings.textRightAngle": "Right angle axes", "SSE.Views.ChartSettings.textSelectData": "Məlumatı Seçin", "SSE.Views.ChartSettings.textShow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSize": "Ölçü", "SSE.Views.ChartSettings.textStyle": "Üslub", "SSE.Views.ChartSettings.textSwitch": "Switch Row/Column", "SSE.Views.ChartSettings.textType": "Növ", "SSE.Views.ChartSettings.textUp": "Up", "SSE.Views.ChartSettings.textWiden": "Widen field of view", "SSE.Views.ChartSettings.textWidth": "En", "SSE.Views.ChartSettings.textX": "X rotation", "SSE.Views.ChartSettings.textY": "Y rotation", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "XƏTA! Qrafik üzrə seriyalardakı punktların maksimum sayı 4096-dır.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "XƏTA! Qrafik üzrə verilənlər seriyasının maksimum sayı 255-dir", "SSE.Views.ChartSettingsDlg.errorStockChart": "Yanlış sıra ardıcıllığı. Səhm qrafikini yaratmaq üçün məlumatları vərəqdə aşağıdakı ardıcıllıqla yerləşdirin:<br> a<PERSON><PERSON><PERSON><PERSON><PERSON> qiyməti, maks<PERSON>um qiymət, minimum qiymət, ba<PERSON><PERSON><PERSON><PERSON> qiyməti.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Xanalarla hərəkət etməyin və ya ölçü götürməyin", "SSE.Views.ChartSettingsDlg.textAlt": "Alternativ Mətn", "SSE.Views.ChartSettingsDlg.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAltTip": "Şəkildə, avtoformada, diaqramda və ya cədvəldə hansı məlumatın olduğunu daha yaxşı anlamağa kömək etmək üçün görmə və ya idrak qüsurları olan insanlara oxunacaq vizual obyekt məlumatının alternativ mətn əsaslı təqdimatı.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Başlıq", "SSE.Views.ChartSettingsDlg.textAuto": "Avtomatik", "SSE.Views.ChartSettingsDlg.textAutoEach": "<PERSON><PERSON><PERSON> <PERSON><PERSON> üçün avtomatik", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisOptions": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisPos": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisSettings": "<PERSON>x <PERSON>", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Başlıq", "SSE.Views.ChartSettingsDlg.textBase": "Base", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBillions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBottom": "Aşağı", "SSE.Views.ChartSettingsDlg.textCategoryName": "Kateqoriya Adı", "SSE.Views.ChartSettingsDlg.textCenter": "Mərkəz", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "<PERSON><PERSON><PERSON> və<br><PERSON><PERSON><PERSON><PERSON><PERSON>i", "SSE.Views.ChartSettingsDlg.textChartTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCross": "K<PERSON>sişmə", "SSE.Views.ChartSettingsDlg.textCustom": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDataColumns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDataLabels": "V<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDataRows": "sətirlərdə", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Gizli və Boş xanalar", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Məlumat nöqtələrini xətt ilə birləşdirin", "SSE.Views.ChartSettingsDlg.textFit": "Enə uyğun tənzimlə", "SSE.Views.ChartSettingsDlg.textFixed": "Sabit", "SSE.Views.ChartSettingsDlg.textFormat": "Nişan formatı", "SSE.Views.ChartSettingsDlg.textGaps": "Boşluqlar", "SSE.Views.ChartSettingsDlg.textGridLines": "<PERSON>ə<PERSON>", "SSE.Views.ChartSettingsDlg.textGroup": "Sparklaynı qruplaşdır", "SSE.Views.ChartSettingsDlg.textHide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHideAxis": "<PERSON><PERSON><PERSON> ox", "SSE.Views.ChartSettingsDlg.textHigh": "Yüksək", "SSE.Views.ChartSettingsDlg.textHorAxis": "Üfüqi Ox", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "İkinci dərə<PERSON>ə<PERSON>", "SSE.Views.ChartSettingsDlg.textHorizontal": "Üfüqi", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Yü<PERSON>l<PERSON>rlə", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "içində", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Da<PERSON>li Alt", "SSE.Views.ChartSettingsDlg.textInnerTop": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textInvalidRange": "XƏTA! Yanlış xana diapazonu", "SSE.Views.ChartSettingsDlg.textLabelDist": "Ox Etiket <PERSON>", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Etiket<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLabelOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLabelPos": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLayout": "Düzüm", "SSE.Views.ChartSettingsDlg.textLeft": "Sol", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "<PERSON>", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Aşağı", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Sol", "SSE.Views.ChartSettingsDlg.textLegendPos": "Legend", "SSE.Views.ChartSettingsDlg.textLegendRight": "Sağ", "SSE.Views.ChartSettingsDlg.textLegendTop": "Yuxarı", "SSE.Views.ChartSettingsDlg.textLines": "Sə<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLocationRange": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLogScale": "Logarithmic scale", "SSE.Views.ChartSettingsDlg.textLow": "Aşağı", "SSE.Views.ChartSettingsDlg.textMajor": "Əsas", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Əsas və Köməkçi", "SSE.Views.ChartSettingsDlg.textMajorType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textManual": "Əl ilə", "SSE.Views.ChartSettingsDlg.textMarkers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarksInterval": "İşarə<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "Kiçik", "SSE.Views.ChartSettingsDlg.textMinorType": "Kiçik Növ", "SSE.Views.ChartSettingsDlg.textMinValue": "Minimum Dəyər", "SSE.Views.ChartSettingsDlg.textNextToAxis": "<PERSON><PERSON><PERSON> yanında", "SSE.Views.ChartSettingsDlg.textNone": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNoOverlay": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textOneCell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lakin xana<PERSON>", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Bölgü Nişanları üzərində", "SSE.Views.ChartSettingsDlg.textOut": "Kənar", "SSE.Views.ChartSettingsDlg.textOuterTop": "<PERSON>arici <PERSON>ə", "SSE.Views.ChartSettingsDlg.textOverlay": "Overley", "SSE.Views.ChartSettingsDlg.textReverse": "Əks Ardıcıllı<PERSON>", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Əks sıralama", "SSE.Views.ChartSettingsDlg.textRight": "Sağ", "SSE.Views.ChartSettingsDlg.textRightOverlay": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRotated": "Döndə<PERSON>miş", "SSE.Views.ChartSettingsDlg.textSameAll": "Hamısı üçün Eyni", "SSE.Views.ChartSettingsDlg.textSelectData": "Məlumatı Seçin", "SSE.Views.ChartSettingsDlg.textSeparator": "Verilən Nişanları Ayırıcısı", "SSE.Views.ChartSettingsDlg.textSeriesName": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowBorders": "<PERSON><PERSON><PERSON> g<PERSON>", "SSE.Views.ChartSettingsDlg.textShowData": "G<PERSON><PERSON> sətir və sütunlarda məlumatları göstərin", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Boş xanaları kimi g<PERSON>", "SSE.Views.ChartSettingsDlg.textShowEquation": "Display equation on chart", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowValues": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSingle": "Tək Sparklayn", "SSE.Views.ChartSettingsDlg.textSmooth": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSnap": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Sparklayn Diapazonları", "SSE.Views.ChartSettingsDlg.textStraight": "Düz", "SSE.Views.ChartSettingsDlg.textStyle": "Üslub", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Min<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTickOptions": "Bölgü Seçimləri", "SSE.Views.ChartSettingsDlg.textTitle": "Diaqram - <PERSON><PERSON><PERSON><PERSON>l <PERSON>", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Sparklayn-T<PERSON>k<PERSON>l <PERSON>rlər", "SSE.Views.ChartSettingsDlg.textTop": "Yuxarı", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Trendline options", "SSE.Views.ChartSettingsDlg.textTrillions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTwoCell": "Xanalarla köçürün və ölçün", "SSE.Views.ChartSettingsDlg.textType": "Növ", "SSE.Views.ChartSettingsDlg.textTypeData": "Növ və Verilən", "SSE.Views.ChartSettingsDlg.textUnits": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "İkinci dərə<PERSON><PERSON><PERSON> Şaquli O<PERSON>", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X Oxu Başlığı", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y Oxu Başlığı", "SSE.Views.ChartSettingsDlg.textZero": "Sıf<PERSON>r", "SSE.Views.ChartSettingsDlg.txtEmpty": "Bu sahə tələb olunur", "SSE.Views.ChartTypeDialog.errorComboSeries": "Birləşdirilmiş diaqram yaratmaq üçün ən azı iki məlumat seriyası seçin.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Seçilmiş diaqram növü mövcud diaqramın istifadə etdiyi ikinci dərəcəli oxu tələb edir. Başqa bir qrafik növü seçin.", "SSE.Views.ChartTypeDialog.textSecondary": "<PERSON><PERSON><PERSON> dərəcəli ox", "SSE.Views.ChartTypeDialog.textSeries": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textStyle": "Üslub", "SSE.Views.ChartTypeDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textType": "Növ", "SSE.Views.ChartWizardDialog.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartWizardDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order: opening price, max price, min price, closing price.", "SSE.Views.ChartWizardDialog.textRecommended": "Recommended", "SSE.Views.ChartWizardDialog.textSecondary": "Secondary Axis", "SSE.Views.ChartWizardDialog.textSeries": "Series", "SSE.Views.ChartWizardDialog.textTitle": "Insert Chart", "SSE.Views.ChartWizardDialog.textTitleChange": "Change chart type", "SSE.Views.ChartWizardDialog.textType": "Type", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Choose the chart type and axis for your data series", "SSE.Views.CreatePivotDialog.textDataRange": "Mənbə məlumat diapazonu", "SSE.Views.CreatePivotDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON> harada yerləşdiriləcəyini seçin", "SSE.Views.CreatePivotDialog.textExist": "Möv<PERSON>d iş vərəqi", "SSE.Views.CreatePivotDialog.textInvalidRange": "Yanlış xana di<PERSON>", "SSE.Views.CreatePivotDialog.textNew": "Yeni iş vərəqi", "SSE.Views.CreatePivotDialog.textSelectData": "Məlumatı seçin", "SSE.Views.CreatePivotDialog.textTitle": "Pivot Cədvəli <PERSON>ın", "SSE.Views.CreatePivotDialog.txtEmpty": "Bu sahə tələb olunur", "SSE.Views.CreateSparklineDialog.textDataRange": "Mənbə məlumat diapazonu", "SSE.Views.CreateSparklineDialog.textDestination": "Sparklaynların harada yerləşdiriləcəyini seçin", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Yanlış xana di<PERSON>", "SSE.Views.CreateSparklineDialog.textSelectData": "Məlumatı seçin", "SSE.Views.CreateSparklineDialog.textTitle": "Sparklaynlar Yaradın", "SSE.Views.CreateSparklineDialog.txtEmpty": "Bu sahə tələb olunur", "SSE.Views.DataTab.capBtnGroup": "Qrup", "SSE.Views.DataTab.capBtnTextCustomSort": "<PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextDataValidation": "Verilə<PERSON><PERSON><PERSON><PERSON> yox<PERSON>ı", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Dublikatları Silin", "SSE.Views.DataTab.capBtnTextToCol": "Sütunlar üçün Mətn", "SSE.Views.DataTab.capBtnUngroup": "Qruplaşdırmanı ləğv et", "SSE.Views.DataTab.capDataExternalLinks": "External Links", "SSE.Views.DataTab.capDataFromText": "Məlumat əldə edin", "SSE.Views.DataTab.capGoalSeek": "Goal Seek", "SSE.Views.DataTab.mniFromFile": "Lokal TXT/CSV-dən", "SSE.Views.DataTab.mniFromUrl": "TXT/CSV veb ünvanından", "SSE.Views.DataTab.mniFromXMLFile": "From Local XML", "SSE.Views.DataTab.textBelow": "Detalların altında sətirlərdə yekunlar", "SSE.Views.DataTab.textClear": "<PERSON>ruk<PERSON><PERSON> təmizləyin", "SSE.Views.DataTab.textColumns": "Sütunların qruplaşmasını ləğv et", "SSE.Views.DataTab.textGroupColumns": "Sütunları qruplaşdır", "SSE.Views.DataTab.textGroupRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataTab.textRightOf": "Detalların sağında sütunlarda yekunlar", "SSE.Views.DataTab.textRows": "Sə<PERSON>rl<PERSON>rin qruplaşmasını ləğv et", "SSE.Views.DataTab.tipCustomSort": "<PERSON><PERSON><PERSON>", "SSE.Views.DataTab.tipDataFromText": "Mətn/CSV faylından məlumat alın", "SSE.Views.DataTab.tipDataValidation": "Verilə<PERSON><PERSON><PERSON><PERSON> yox<PERSON>ı", "SSE.Views.DataTab.tipExternalLinks": "View other files this spreadsheet is linked to", "SSE.Views.DataTab.tipGoalSeek": "Find the right input for the value you want", "SSE.Views.DataTab.tipGroup": "Xanaların qrup diapazonu", "SSE.Views.DataTab.tipRemDuplicates": "Və<PERSON>əqdən dublikat sətirləri silin", "SSE.Views.DataTab.tipToColumns": "<PERSON><PERSON> mə<PERSON><PERSON>i s<PERSON>a a<PERSON>ırın", "SSE.Views.DataTab.tipUngroup": "Xanalar diapazonunun qruplaşmasını ləğv et", "SSE.Views.DataValidationDialog.errorFormula": "Də<PERSON>ər hazırda xəta olaraq qiymətləndirilir. Davam etmək istəyirsiniz?", "SSE.Views.DataValidationDialog.errorInvalid": "\"{0}\" sahəsi üçün daxil etdiyiniz dəyər yanlışdır.", "SSE.Views.DataValidationDialog.errorInvalidDate": "\"{0}\" sahə<PERSON>ün daxil etdiyiniz tarix yanlışdır.", "SSE.Views.DataValidationDialog.errorInvalidList": "Siyahı mənbəyi ayrılmış siyahı və ya tək sətir və ya sütuna istinad olmalıdır.", "SSE.Views.DataValidationDialog.errorInvalidTime": "\"{0}\" sahə<PERSON> ü<PERSON>ün daxil etdiyiniz vaxt yanlışdır.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "\"{1}\" sahəsi \"{0}\" sahəsindən böyük və ya ona bərabər olmalıdır.", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Siz həm \"{0}\" sahəsinə, həm də \"{1}\" sahəsinə dəyər daxil etməlis<PERSON>z.", "SSE.Views.DataValidationDialog.errorMustEnterValue": "<PERSON>z \"{0}\" sahəsinə dəyər daxil etməlis<PERSON>z.", "SSE.Views.DataValidationDialog.errorNamedRange": "Göstərdiyiniz adlandırılmış diapazonu tapmaq mümkün deyil.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "<PERSON>ə<PERSON><PERSON> dəyərlər \"{0}\" şərtlərində istifadə edilə bilməz.", "SSE.Views.DataValidationDialog.errorNotNumeric": "\"{0}\" sahəsi rəqəmli dəyər, rəqəmli ifadə və ya ədədi dəyəri olan xanaya istinad olmalıdır.", "SSE.Views.DataValidationDialog.strError": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.strInput": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.strSettings": "Parametrlər", "SSE.Views.DataValidationDialog.textAlert": "Xəbərdarlıq", "SSE.Views.DataValidationDialog.textAllow": "İcazə verin", "SSE.Views.DataValidationDialog.textApply": "Bu dəyişiklikləri eyni parametrlərlə bütün digər xanalara tətbiq edin", "SSE.Views.DataValidationDialog.textCellSelected": "<PERSON>ana seçildikdə, bu daxiletmə mesajını göstərin", "SSE.Views.DataValidationDialog.textCompare": "Müqayisə et", "SSE.Views.DataValidationDialog.textData": "Verilənlər", "SSE.Views.DataValidationDialog.textEndDate": "Bitmə Tarixi", "SSE.Views.DataValidationDialog.textEndTime": "<PERSON>", "SSE.Views.DataValidationDialog.textError": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textIgnore": "Boşluğa əhəmiyyət vermə", "SSE.Views.DataValidationDialog.textInput": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMax": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMessage": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMin": "Minimum", "SSE.Views.DataValidationDialog.textSelectData": "Məlumatı seçin", "SSE.Views.DataValidationDialog.textShowDropDown": "Xanada açılan siyahını göstərin", "SSE.Views.DataValidationDialog.textShowError": "Yanlış məlumat daxil edildikdən sonra xəta xəbərdarlığını göstərin", "SSE.Views.DataValidationDialog.textShowInput": "Xana seçildikdə daxiletmə mesajını göstərin", "SSE.Views.DataValidationDialog.textSource": "Mənbə", "SSE.Views.DataValidationDialog.textStartDate": "Başlama <PERSON>i", "SSE.Views.DataValidationDialog.textStartTime": "Başlama Vaxtı", "SSE.Views.DataValidationDialog.textStop": "Dayandır", "SSE.Views.DataValidationDialog.textStyle": "Üslub", "SSE.Views.DataValidationDialog.textTitle": "Başlıq", "SSE.Views.DataValidationDialog.textUserEnters": "İstifadəçi yanlış məlumat daxil etdikdə, bu xəta xəbərdarlığını göstərin", "SSE.Views.DataValidationDialog.txtAny": "İstə<PERSON><PERSON><PERSON>n dəyər", "SSE.Views.DataValidationDialog.txtBetween": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtDate": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtDecimal": "Onluq", "SSE.Views.DataValidationDialog.txtElTime": "<PERSON><PERSON><PERSON><PERSON> vaxt", "SSE.Views.DataValidationDialog.txtEndDate": "Bitmə Tarixi", "SSE.Views.DataValidationDialog.txtEndTime": "<PERSON> vaxt", "SSE.Views.DataValidationDialog.txtEqual": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtGreaterThan": "-dən b<PERSON>", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "-dən bö<PERSON><PERSON>k və ya ona bərabər", "SSE.Views.DataValidationDialog.txtLength": "Uzunluq", "SSE.Views.DataValidationDialog.txtLessThan": "-dən az", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "-dən az və ya bərabərdir", "SSE.Views.DataValidationDialog.txtList": "Siyahı", "SSE.Views.DataValidationDialog.txtNotBetween": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtNotEqual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtOther": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtStartDate": "Başlama <PERSON>i", "SSE.Views.DataValidationDialog.txtStartTime": "Başlama vaxtı", "SSE.Views.DataValidationDialog.txtTextLength": "Mətn uzunluğu", "SSE.Views.DataValidationDialog.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtWhole": "<PERSON>əd", "SSE.Views.DigitalFilterDialog.capAnd": "Və", "SSE.Views.DigitalFilterDialog.capCondition1": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition10": "ilə başa çatmır", "SSE.Views.DigitalFilterDialog.capCondition11": "ibarətdir:", "SSE.Views.DigitalFilterDialog.capCondition12": "eh<PERSON>va etmir", "SSE.Views.DigitalFilterDialog.capCondition2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition3": "-dən b<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition30": "is after", "SSE.Views.DigitalFilterDialog.capCondition4": "-dən bö<PERSON><PERSON>k və ya bərabərdir", "SSE.Views.DigitalFilterDialog.capCondition40": "is after or equal to", "SSE.Views.DigitalFilterDialog.capCondition5": "-dən azdır", "SSE.Views.DigitalFilterDialog.capCondition50": "is before", "SSE.Views.DigitalFilterDialog.capCondition6": "-dən az və ya bərabərdir", "SSE.Views.DigitalFilterDialog.capCondition60": "is before or equal to", "SSE.Views.DigitalFilterDialog.capCondition7": "ilə baş<PERSON>ır", "SSE.Views.DigitalFilterDialog.capCondition8": "ilə başlamır", "SSE.Views.DigitalFilterDialog.capCondition9": "ilə başa çatır", "SSE.Views.DigitalFilterDialog.capOr": "Və ya", "SSE.Views.DigitalFilterDialog.textNoFilter": "doldurulmasın", "SSE.Views.DigitalFilterDialog.textShowRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ha<PERSON> g<PERSON>", "SSE.Views.DigitalFilterDialog.textUse1": "Hər hansı simvolu təqdim etmək üçün ? istifadə edilir", "SSE.Views.DigitalFilterDialog.textUse2": "İstənilən xarakter seriyasını təqdim etmək üçün * istifadə edin", "SSE.Views.DigitalFilterDialog.txtSelectDate": "Select date", "SSE.Views.DigitalFilterDialog.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.advancedEquationText": "Equation settings", "SSE.Views.DocumentHolder.advancedImgText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.advancedShapeText": "Forma Təkmil Parametrlər", "SSE.Views.DocumentHolder.advancedSlicerText": "Məhdudlaşdırıcı Təkmil Parametrlər", "SSE.Views.DocumentHolder.allLinearText": "All - Linear", "SSE.Views.DocumentHolder.allProfText": "All - Professional", "SSE.Views.DocumentHolder.bottomCellText": "Aşağı Düzləndir", "SSE.Views.DocumentHolder.bulletsText": "Marker və Nömrələr", "SSE.Views.DocumentHolder.centerCellText": "Or<PERSON>ya <PERSON>", "SSE.Views.DocumentHolder.chartDataText": "Select Chart Data", "SSE.Views.DocumentHolder.chartText": "Diaq<PERSON><PERSON>n T<PERSON>kmil Parametrləri", "SSE.Views.DocumentHolder.chartTypeText": "Change Chart Type", "SSE.Views.DocumentHolder.currLinearText": "Current - Linear", "SSE.Views.DocumentHolder.currProfText": "Current - Professional", "SSE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON><PERSON><PERSON>l", "SSE.Views.DocumentHolder.direct270Text": "Mətni Yuxarı Döndər", "SSE.Views.DocumentHolder.direct90Text": "Mətni Aşağı Döndər", "SSE.Views.DocumentHolder.directHText": "Üfüqi", "SSE.Views.DocumentHolder.directionText": "Mə<PERSON>n İstiq<PERSON>ə<PERSON>", "SSE.Views.DocumentHolder.editChartText": "Veriləni Redaktə edin", "SSE.Views.DocumentHolder.editHyperlinkText": "Hiperlinki redaktə edin", "SSE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "SSE.Views.DocumentHolder.insertColumnLeftText": "<PERSON>", "SSE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.insertRowAboveText": "Yuxarı Sətir", "SSE.Views.DocumentHolder.insertRowBelowText": "Aşağı Sətir", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "Faktiki Ölçü", "SSE.Views.DocumentHolder.removeHyperlinkText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectDataText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON><PERSON><PERSON><PERSON>l", "SSE.Views.DocumentHolder.showEqToolbar": "Show Equation Toolbar", "SSE.Views.DocumentHolder.strDelete": "İmzanı Silin", "SSE.Views.DocumentHolder.strDetails": "<PERSON><PERSON>za Detalları", "SSE.Views.DocumentHolder.strSetup": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.strSign": "İşarə", "SSE.Views.DocumentHolder.textAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrange": "Ni<PERSON>layın", "SSE.Views.DocumentHolder.textArrangeBack": "Fona göndərin", "SSE.Views.DocumentHolder.textArrangeBackward": "Geriyə Göndərin", "SSE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "SSE.Views.DocumentHolder.textArrangeFront": "Ön plana çıxarın", "SSE.Views.DocumentHolder.textAverage": "Orta", "SSE.Views.DocumentHolder.textBullets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCopyCells": "Copy cells", "SSE.Views.DocumentHolder.textCount": "Say", "SSE.Views.DocumentHolder.textCrop": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFit": "<PERSON>ə<PERSON><PERSON><PERSON>ə", "SSE.Views.DocumentHolder.textEditPoints": "Edit points", "SSE.Views.DocumentHolder.textEntriesList": "<PERSON><PERSON><PERSON><PERSON> si<PERSON> seç<PERSON>", "SSE.Views.DocumentHolder.textFillDays": "Fill days", "SSE.Views.DocumentHolder.textFillFormatOnly": "Fill formatting only", "SSE.Views.DocumentHolder.textFillMonths": "Fill months", "SSE.Views.DocumentHolder.textFillSeries": "Fill series", "SSE.Views.DocumentHolder.textFillWeekdays": "Fill weekdays", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Fill without formatting", "SSE.Views.DocumentHolder.textFillYears": "Fill years", "SSE.Views.DocumentHolder.textFlashFill": "Flash fill", "SSE.Views.DocumentHolder.textFlipH": "Üfüqi olaraq çevir", "SSE.Views.DocumentHolder.textFlipV": "<PERSON><PERSON><PERSON> çevir", "SSE.Views.DocumentHolder.textFreezePanes": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textFromStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textFromUrl": "URL-dən", "SSE.Views.DocumentHolder.textGrowthTrend": "Growth trend", "SSE.Views.DocumentHolder.textLinearTrend": "Linear trend", "SSE.Views.DocumentHolder.textListSettings": "Siyahı Parametrləri", "SSE.Views.DocumentHolder.textMacro": "<PERSON><PERSON><PERSON> edin", "SSE.Views.DocumentHolder.textMax": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "<PERSON>ha <PERSON> funksiya", "SSE.Views.DocumentHolder.textMoreFormats": "Daha çox format", "SSE.Views.DocumentHolder.textNone": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textNumbering": "Nömr<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textReplace": "Təsviri əvəz edin", "SSE.Views.DocumentHolder.textResetCrop": "Reset crop", "SSE.Views.DocumentHolder.textRotate": "D<PERSON>nd<PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate270": "90° Saat əqrəbinin əksi istiqamətində Döndər", "SSE.Views.DocumentHolder.textRotate90": "90° saat əqrəbi istiqamətində döndər", "SSE.Views.DocumentHolder.textSaveAsPicture": "Save as picture", "SSE.Views.DocumentHolder.textSeries": "Series", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Aşağı Düzləndir", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Mərkəzə Düzləndir", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Sola Düzləndir", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Or<PERSON>ya <PERSON>", "SSE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textShapeAlignTop": "Yuxarı Düzləndir", "SSE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "SSE.Views.DocumentHolder.textStdDev": "StdSapma", "SSE.Views.DocumentHolder.textSum": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "<PERSON><PERSON>ə<PERSON><PERSON><PERSON>ö<PERSON>ü<PERSON>", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "Arrow bullets", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Checkmark bullets", "SSE.Views.DocumentHolder.tipMarkersDash": "Dash bullets", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Filled rhombus bullets", "SSE.Views.DocumentHolder.tipMarkersFRound": "Filled round bullets", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Filled square bullets", "SSE.Views.DocumentHolder.tipMarkersHRound": "Hollow round bullets", "SSE.Views.DocumentHolder.tipMarkersStar": "Star bullets", "SSE.Views.DocumentHolder.topCellText": "Yuxarı Düzləndir", "SSE.Views.DocumentHolder.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAddComment": "Şərh əlavə edin", "SSE.Views.DocumentHolder.txtAddNamedRange": "<PERSON> edin", "SSE.Views.DocumentHolder.txtArrange": "Ni<PERSON>layın", "SSE.Views.DocumentHolder.txtAscending": "Artan ü<PERSON>rə", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Sütun Enini Avtomatik Sığışdır", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Sətirlərin Hündürlüyünü AvtoSığışdır", "SSE.Views.DocumentHolder.txtAverage": "Average", "SSE.Views.DocumentHolder.txtCellFormat": "Format cells", "SSE.Views.DocumentHolder.txtClear": "Təmizlə", "SSE.Views.DocumentHolder.txtClearAll": "Hamısı", "SSE.Views.DocumentHolder.txtClearComments": "Şə<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearFormat": "Format", "SSE.Views.DocumentHolder.txtClearHyper": "Hiperlinkl<PERSON>r", "SSE.Views.DocumentHolder.txtClearPivotField": "Clear filter from {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Seçilmiş Sparklalyn Qruplarını Təmizləyin", "SSE.Views.DocumentHolder.txtClearSparklines": "Seçilmiş Sparklaynları Təmizləyin", "SSE.Views.DocumentHolder.txtClearText": "Mətn", "SSE.Views.DocumentHolder.txtCollapse": "Collapse", "SSE.Views.DocumentHolder.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtColumnWidth": "<PERSON><PERSON><PERSON> təyin edin", "SSE.Views.DocumentHolder.txtCondFormat": "Şərti Formatlaşdırma", "SSE.Views.DocumentHolder.txtCopy": "Kopyalayın", "SSE.Views.DocumentHolder.txtCount": "Count", "SSE.Views.DocumentHolder.txtCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomRowHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomSort": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCut": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDateLong": "Long Date", "SSE.Views.DocumentHolder.txtDateShort": "Short Date", "SSE.Views.DocumentHolder.txtDelete": "Silin", "SSE.Views.DocumentHolder.txtDelField": "Remove", "SSE.Views.DocumentHolder.txtDescending": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDifference": "Difference from", "SSE.Views.DocumentHolder.txtDistribHor": "Üfüqi olaraq Bölüşdürün", "SSE.Views.DocumentHolder.txtDistribVert": "Şaquli o<PERSON>aq Bölüşdürün", "SSE.Views.DocumentHolder.txtEditComment": "Ş<PERSON>rhi Redaktə edin", "SSE.Views.DocumentHolder.txtEditObject": "Edit object", "SSE.Views.DocumentHolder.txtExpand": "Expand", "SSE.Views.DocumentHolder.txtExpandCollapse": "Expand/Collapse", "SSE.Views.DocumentHolder.txtExpandEntire": "Expand Entire Field", "SSE.Views.DocumentHolder.txtFieldSettings": "Field settings", "SSE.Views.DocumentHolder.txtFilter": "Süzgəc", "SSE.Views.DocumentHolder.txtFilterCellColor": "<PERSON>ana rənginə görə süzgəc", "SSE.Views.DocumentHolder.txtFilterFontColor": "Şrift Rənginə görə Süzgəc", "SSE.Views.DocumentHolder.txtFilterValue": "Seçilmiş xananın dəyərinə görə süzgəc", "SSE.Views.DocumentHolder.txtFormula": "Funksiya Daxil edin", "SSE.Views.DocumentHolder.txtFraction": "Kəsr", "SSE.Views.DocumentHolder.txtGeneral": "Ümumi", "SSE.Views.DocumentHolder.txtGetLink": "Get link to this range", "SSE.Views.DocumentHolder.txtGrandTotal": "Grand total", "SSE.Views.DocumentHolder.txtGroup": "Qrup", "SSE.Views.DocumentHolder.txtHide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtIndex": "Index", "SSE.Views.DocumentHolder.txtInsert": "Daxil edin", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hiperlink", "SSE.Views.DocumentHolder.txtInsImage": "Insert image from file", "SSE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "SSE.Views.DocumentHolder.txtLabelFilter": "Label filters", "SSE.Views.DocumentHolder.txtMax": "Max", "SSE.Views.DocumentHolder.txtMin": "Min", "SSE.Views.DocumentHolder.txtMoreOptions": "More options", "SSE.Views.DocumentHolder.txtNormal": "No calculation", "SSE.Views.DocumentHolder.txtNumber": "Nömrə", "SSE.Views.DocumentHolder.txtNumFormat": "Nömrə Formatı", "SSE.Views.DocumentHolder.txtPaste": "Yapışdır", "SSE.Views.DocumentHolder.txtPercent": "% of", "SSE.Views.DocumentHolder.txtPercentage": "Faiz", "SSE.Views.DocumentHolder.txtPercentDiff": "% difference from", "SSE.Views.DocumentHolder.txtPercentOfCol": "% of column total", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% of grand total", "SSE.Views.DocumentHolder.txtPercentOfParent": "% of parent total", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% of parent column total", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% of parent row total", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% running total in", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% of row total", "SSE.Views.DocumentHolder.txtPivotSettings": "Pivot Table settings", "SSE.Views.DocumentHolder.txtProduct": "Product", "SSE.Views.DocumentHolder.txtRankAscending": "Rank smallest to largest", "SSE.Views.DocumentHolder.txtRankDescending": "Rank largest to smallest", "SSE.Views.DocumentHolder.txtReapply": "<PERSON>ə<PERSON><PERSON> tətbiq edin", "SSE.Views.DocumentHolder.txtRefresh": "Refresh", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON><PERSON><PERSON><PERSON> sıra", "SSE.Views.DocumentHolder.txtRowHeight": "<PERSON><PERSON><PERSON><PERSON>əyin edin", "SSE.Views.DocumentHolder.txtRunTotal": "Running total in", "SSE.Views.DocumentHolder.txtScientific": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSelect": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftDown": "Xanaları aşağı sürüşdürün", "SSE.Views.DocumentHolder.txtShiftLeft": "Xanaları sola sürüşdürün", "SSE.Views.DocumentHolder.txtShiftRight": "Xanaları sağa sürüşdürün", "SSE.Views.DocumentHolder.txtShiftUp": "Xanaları yuxarı sürüşdürün", "SSE.Views.DocumentHolder.txtShow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShowAs": "Show values as", "SSE.Views.DocumentHolder.txtShowComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShowDetails": "Show details", "SSE.Views.DocumentHolder.txtSort": "S<PERSON>rala", "SSE.Views.DocumentHolder.txtSortCellColor": "Üstdə Seçilmiş Xana Rəngi", "SSE.Views.DocumentHolder.txtSortFontColor": "Üstdə Seçilmiş Şrift Rəngi", "SSE.Views.DocumentHolder.txtSortOption": "More sort options", "SSE.Views.DocumentHolder.txtSparklines": "Sparklaynlar", "SSE.Views.DocumentHolder.txtSubtotalField": "Subtotal", "SSE.Views.DocumentHolder.txtSum": "Sum", "SSE.Views.DocumentHolder.txtSummarize": "Summarize values by", "SSE.Views.DocumentHolder.txtText": "Mətn", "SSE.Views.DocumentHolder.txtTextAdvanced": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtTop10": "Top 10", "SSE.Views.DocumentHolder.txtUngroup": "Qruplaşdırmanı ləğv et", "SSE.Views.DocumentHolder.txtValueFieldSettings": "Value field settings", "SSE.Views.DocumentHolder.txtValueFilter": "Value filters", "SSE.Views.DocumentHolder.txtWidth": "En", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "Şaquli <PERSON>", "SSE.Views.ExternalLinksDlg.closeButtonText": "Close", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Automatically update data from the linked sources", "SSE.Views.ExternalLinksDlg.textChange": "Change source", "SSE.Views.ExternalLinksDlg.textDelete": "Break links", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Break all links", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textOpen": "Open source", "SSE.Views.ExternalLinksDlg.textSource": "Source", "SSE.Views.ExternalLinksDlg.textStatus": "Status", "SSE.Views.ExternalLinksDlg.textUnknown": "Unknown", "SSE.Views.ExternalLinksDlg.textUpdate": "Update values", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Update all", "SSE.Views.ExternalLinksDlg.textUpdating": "Updating...", "SSE.Views.ExternalLinksDlg.txtTitle": "External links", "SSE.Views.FieldSettingsDialog.strLayout": "Düzüm", "SSE.Views.FieldSettingsDialog.strSubtotals": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.FieldSettingsDialog.textReport": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.textTitle": "<PERSON><PERSON><PERSON> Parametrlə<PERSON>", "SSE.Views.FieldSettingsDialog.txtAverage": "Orta", "SSE.Views.FieldSettingsDialog.txtBlank": "Hər bir elementdən sonra boş sətirlər daxil edin", "SSE.Views.FieldSettingsDialog.txtBottom": "Qrupun aşağı hissəsində göstərin", "SSE.Views.FieldSettingsDialog.txtCompact": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtCount": "Say", "SSE.Views.FieldSettingsDialog.txtCountNums": "<PERSON>ö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtCustomName": "Fərdi ad", "SSE.Views.FieldSettingsDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "SSE.Views.FieldSettingsDialog.txtMax": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "Struktur", "SSE.Views.FieldSettingsDialog.txtProduct": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtRepeat": "Hər sətirdə element etiketlərini təkrarlayın", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "<PERSON> cəmləri g<PERSON>", "SSE.Views.FieldSettingsDialog.txtSourceName": "Mənbə adı:", "SSE.Views.FieldSettingsDialog.txtStdDev": "StdSapma", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdSapmasız", "SSE.Views.FieldSettingsDialog.txtSum": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtSummarize": "<PERSON> cəmi <PERSON>", "SSE.Views.FieldSettingsDialog.txtTabular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kimi", "SSE.Views.FieldSettingsDialog.txtTop": "Qrupun yuxarı hissəsində göstərin", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.ariaFileMenu": "File menu", "SSE.Views.FileMenu.btnBackCaption": "<PERSON><PERSON> a<PERSON>", "SSE.Views.FileMenu.btnCloseEditor": "Close File", "SSE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON><PERSON>n", "SSE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnExitCaption": "Çı<PERSON>ın", "SSE.Views.FileMenu.btnExportToPDFCaption": "Export to PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnHelpCaption": "Yardım", "SSE.Views.FileMenu.btnHistoryCaption": "Versiya <PERSON>ə<PERSON>", "SSE.Views.FileMenu.btnInfoCaption": "Elektron Cədvəl Məlumatı", "SSE.Views.FileMenu.btnPrintCaption": "Çap edin", "SSE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnRenameCaption": "Adını Dəyişdirin", "SSE.Views.FileMenu.btnReturnCaption": "Elektron cədvəl səhifəsinə qayıt", "SSE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveAsCaption": "kimi <PERSON>a saxla", "SSE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "<PERSON><PERSON><PERSON> kimi yadda saxla", "SSE.Views.FileMenu.btnSettingsCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON> parametrlər", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "SSE.Views.FileMenu.btnToEditCaption": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON> Redaktə edin", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Boş Elektron cədvəl", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "<PERSON>ə<PERSON><PERSON><PERSON> edin", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Mətn Əlavə edin", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "<PERSON><PERSON><PERSON>ü<PERSON>ını dəyiş", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Şərh", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Yaradıldı", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "tərə<PERSON>dən Son Dəyişiklik ", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "<PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Sahib", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Yer", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Hüq<PERSON><PERSON><PERSON>ı olan <PERSON>ə<PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Spreadsheet info", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Başlıq", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Yükləndi", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Access Rights", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "<PERSON><PERSON><PERSON>ü<PERSON>ını dəyiş", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Hüq<PERSON><PERSON><PERSON>ı olan <PERSON>ə<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "<PERSON>ə<PERSON><PERSON><PERSON> edin", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Birgə redaktə Rejimi", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Use 1904 date system", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Onluq a<PERSON>ı<PERSON>ı<PERSON>ı<PERSON>ı", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Dictionary language", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Enable iterative calculation", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Şrift Hamarlaşdırma", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Nümunə: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Show function tooltip", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Show horizontal scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Ignore words in UPPERCASE", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Ignore words with numbers", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Maximum change", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Maximum iterations", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> yapış<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>da Yapış<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON> g<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "R1C1 reference style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Regional Parametrlər", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Nümunə:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTL interface", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Show comments in sheet", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Show changes from other users", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Show resolved comments", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Snapped to the grid while scrolling", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Mə<PERSON><PERSON>dlaşdır", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Tab style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "İnterfeys mövzusu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Minlərlə ayırıcı", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Ölçü Vahidi", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Regional parametrlər əsasında ayırıcılardan istifadə edin", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Show vertical scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Hər 10 dəqiqə", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Hər 30 dəqiqədən bir", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Hər 5 Dəqiqədən bir", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "<PERSON>v<PERSON><PERSON><PERSON> bərpa", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Avtomatik yadda saxlama", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Deaktiv", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Ara versiyaların saxlan<PERSON>ı", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "İstinad Üslubu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "Advanced settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "G<PERSON>rünüş", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "AutoCorrect options...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Belarus", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Katalon", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Defolt keş rejimi", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Calculating", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Collaboration", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Çex", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Customize quick access", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Danimark<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Alman ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Editing and saving", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "<PERSON>ng<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "İspan dili", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Real-time co-editing. All changes are saved automatically", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Fin", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "Fr<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "<PERSON>ar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "Armenian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "İndoneziya", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Last used", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Laos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Latviya", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "OS X kimi", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Holland", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Polyak", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Proofing", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Portuqal (Braziliya)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "Portuqal (Portuqaliya)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Show the Quick Print button in the editor header", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Region", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "<PERSON>us", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Hamısını Aktivləşdirin", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Bütün makroları bildiriş olmadan aktivləşdirin", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Turn on screen reader support", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Default sheet direction", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "This setting will affect only the new sheets", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Left-to-right", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Right-to-left", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Slovak", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Sloven", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Hamısını Deaktiv edin", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Bütün makroları bildiriş olmadan deaktiv edin", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Use the \"Save\" button to sync the changes you and others make", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "İsveçli", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Use toolbar color as tabs background", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Türk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ukraynalı", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Use Alt key to navigate the user interface using the keyboard", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Use Option key to navigate the user interface using the keyboard", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vyetnamlı", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Bütün makroları bildirişlə deaktiv edin", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "Windows kimi", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Workspace", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Xəbərdarlıq", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON><PERSON> il<PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Elektron cədvə<PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON><PERSON><PERSON> ilə", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Valid signatures have been added to the spreadsheet.<br>The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Ensure the integrity of the spreadsheet by adding an<br>invisible digital signature", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Cəd<PERSON>əli redaktə edin", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Redaktə elektron cədvəldən imzaları siləcək.<br><PERSON><PERSON><PERSON> edilsin?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Bu elektron cədvəl parolla qorunub", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Encrypt this spreadsheet with a password", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Bu elektron cədvəl imzalanmalıdır.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Etibarlı imzalar elektron cədvələ əlavə edildi. Elektron cədvəl redaktədən qorunur.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Cədvəldəki bəzi rəqəmsal imzalar etibarsızdır və ya təsdiq edilə bilməz. Cədvəl redaktədən qorunur.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "İmzalara baxın", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Download as", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Save copy as", "SSE.Views.FillSeriesDialog.textAuto": "AutoFill", "SSE.Views.FillSeriesDialog.textCols": "Columns", "SSE.Views.FillSeriesDialog.textDate": "Date", "SSE.Views.FillSeriesDialog.textDateUnit": "Date unit", "SSE.Views.FillSeriesDialog.textDay": "Day", "SSE.Views.FillSeriesDialog.textGrowth": "Growth", "SSE.Views.FillSeriesDialog.textLinear": "<PERSON><PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textMonth": "Month", "SSE.Views.FillSeriesDialog.textRows": "Rows", "SSE.Views.FillSeriesDialog.textSeries": "Series in", "SSE.Views.FillSeriesDialog.textStep": "Step value", "SSE.Views.FillSeriesDialog.textStop": "Stop value", "SSE.Views.FillSeriesDialog.textTitle": "Series", "SSE.Views.FillSeriesDialog.textTrend": "Trend", "SSE.Views.FillSeriesDialog.textType": "Type", "SSE.Views.FillSeriesDialog.textWeek": "Weekday", "SSE.Views.FillSeriesDialog.textYear": "Year", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FormatRulesEditDlg.fillColor": "<PERSON><PERSON><PERSON><PERSON> do<PERSON>n", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Xəbərdarlıq", "SSE.Views.FormatRulesEditDlg.text2Scales": "2-Rə<PERSON><PERSON> Şkala", "SSE.Views.FormatRulesEditDlg.text3Scales": "3-Rə<PERSON><PERSON> Şkala", "SSE.Views.FormatRulesEditDlg.textAllBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textAppearance": "Zolaq Görünüşü:", "SSE.Views.FormatRulesEditDlg.textApply": "Diapazona tətbiq edin", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Avtomatik", "SSE.Views.FormatRulesEditDlg.textAxis": "Ox", "SSE.Views.FormatRulesEditDlg.textBarDirection": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBold": "Qalın", "SSE.Views.FormatRulesEditDlg.textBorder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersColor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Aşağı Sərhədlər", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Şərti formatlaşdırma əlavə etmək mümkün deyil.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Xananın mərkəz nöqtəsi", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textClear": "Təmizlə", "SSE.Views.FormatRulesEditDlg.textColor": "Mətn rəngi", "SSE.Views.FormatRulesEditDlg.textContext": "Kontekst", "SSE.Views.FormatRulesEditDlg.textCustom": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Aşağı Diaqonal Sərhəd", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Yuxarı Diaqonal Sərhəd", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Etibarlı düstur daxil edin.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "<PERSON><PERSON>l etdiyiniz düstur nömrə, ta<PERSON>, vaxt və ya sətirlə qiymətləndirilmir.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "<PERSON><PERSON><PERSON><PERSON><PERSON> daxil edin.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "Da<PERSON>l etdiyiniz dəyər etibarlı nömrə, ta<PERSON>, vaxt və ya sətir deyil.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "{0} ü<PERSON>ün dəyər {1} dəyərindən böyük olmalıdır.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "{0} və {1} arasında nömrə daxil edin.", "SSE.Views.FormatRulesEditDlg.textFill": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormat": "Format", "SSE.Views.FormatRulesEditDlg.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textGradient": "<PERSON>rad<PERSON><PERSON>nt", "SSE.Views.FormatRulesEditDlg.textIconLabel": "{0} {1} <PERSON><PERSON><PERSON><PERSON> və", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "{0} {1} <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "dəyər olduqda", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Bir və ya daha çox piktoqram məlumat diapazonu üst-üstə düşür.<br>Piktoqramın məlumat diapazonu dəyərlərini elə tənzimləyin ki, diapazonlar üst-üstə düşməsin.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Piktoqram <PERSON>lub<PERSON>", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textInvalid": "Yanlış məlumat diapazonu.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "XƏTA! Yanlış xana diapazonu", "SSE.Views.FormatRulesEditDlg.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textItem": "Element", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Soldan Sağa", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Sol <PERSON>", "SSE.Views.FormatRulesEditDlg.textLongBar": "ən uzun zolaq", "SSE.Views.FormatRulesEditDlg.textMaximum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Maxpoint", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMidpoint": "<PERSON>ta nöqtə", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimum", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Orta", "SSE.Views.FormatRulesEditDlg.textNegative": "Neqativ", "SSE.Views.FormatRulesEditDlg.textNewColor": "Yeni Fərdi Rəng Əlavə Edin", "SSE.Views.FormatRulesEditDlg.textNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur", "SSE.Views.FormatRulesEditDlg.textNone": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "G<PERSON><PERSON><PERSON><PERSON><PERSON>n dəyərlərdən biri və ya bir neçəsi etibarlı faiz deyil.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Gö<PERSON><PERSON><PERSON><PERSON>n {0} də<PERSON>əri etibarlı faiz deyil.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n dəyərlərdən biri və ya daha çoxu etibarlı faiz deyil.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Gö<PERSON><PERSON><PERSON><PERSON>n {0} də<PERSON>əri etibarlı faiz deyil.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPercent": "Faiz", "SSE.Views.FormatRulesEditDlg.textPercentile": "Faiz", "SSE.Views.FormatRulesEditDlg.textPosition": "Mövq<PERSON>", "SSE.Views.FormatRulesEditDlg.textPositive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPresets": "Öncəqurmalar", "SSE.Views.FormatRulesEditDlg.textPreview": "Önbaxış", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "<PERSON>ə<PERSON>, məlumat zolaqları və piktoqram dəstləri üçün şərti formatlaşdırma meyarlarında nisbi istinadlardan istifadə edə bilməzsiniz.", "SSE.Views.FormatRulesEditDlg.textReverse": "Əks Piktoqramlar Ardıcıllığı", "SSE.Views.FormatRulesEditDlg.textRight2Left": "<PERSON><PERSON><PERSON>la", "SSE.Views.FormatRulesEditDlg.textRightBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRule": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSameAs": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSelectData": "Məlumatı Seçin", "SSE.Views.FormatRulesEditDlg.textShortBar": "ən qısa zolaq", "SSE.Views.FormatRulesEditDlg.textShowBar": "<PERSON><PERSON><PERSON>z zolağı gö<PERSON>ərin", "SSE.Views.FormatRulesEditDlg.textShowIcon": "<PERSON>ln<PERSON>z piktoqramı gö<PERSON>ərin", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Bu növ istinad şərti formatlaşdırma düsturunda istifadə edilə bilməz.<br>İstinadı tək xanaya dəyişin və ya istinadı =SUM(A1:B5) kimi iş vərəqi funksiyası ilə istifadə edin.", "SSE.Views.FormatRulesEditDlg.textSolid": "<PERSON><PERSON><PERSON>ö<PERSON>", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Üstündən Xətt Çəkilmiş", "SSE.Views.FormatRulesEditDlg.textSubscript": "Aşağı İndeks", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Yuxarı İndeks", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Yuxarı Sərhədlər", "SSE.Views.FormatRulesEditDlg.textUnderline": "Altından xətt çə<PERSON>lmiş", "SSE.Views.FormatRulesEditDlg.tipBorders": "Sə<PERSON><PERSON><PERSON><PERSON><PERSON>r", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Nömrə Formatı", "SSE.Views.FormatRulesEditDlg.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDate": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDateLong": "Long date", "SSE.Views.FormatRulesEditDlg.txtDateShort": "Short date", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Bu sahə tələb olunur", "SSE.Views.FormatRulesEditDlg.txtFraction": "Kəsr", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Ümumi", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtNumber": "Nömrə", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Faiz", "SSE.Views.FormatRulesEditDlg.txtScientific": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtText": "Mətn", "SSE.Views.FormatRulesEditDlg.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Formatlaşdırma <PERSON>ını Redaktə edin", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Yeni <PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.guestText": "Qonaq", "SSE.Views.FormatRulesManagerDlg.lockText": "Locked", "SSE.Views.FormatRulesManagerDlg.text1Above": "Ortadan yuxarı 1 standart sapma", "SSE.Views.FormatRulesManagerDlg.text1Below": "Ortadan aşağı 1 standart sapma", "SSE.Views.FormatRulesManagerDlg.text2Above": "Ortadan yuxarı 2 standart sapma", "SSE.Views.FormatRulesManagerDlg.text2Below": "Ortadan aşağı 2 standart sapma", "SSE.Views.FormatRulesManagerDlg.text3Above": "Ortadan yuxarı 3 standart sapma", "SSE.Views.FormatRulesManagerDlg.text3Below": "Ortadan aşağı 3 standart sapma", "SSE.Views.FormatRulesManagerDlg.textAbove": "<PERSON><PERSON> Qiymətdən Yuxarı", "SSE.Views.FormatRulesManagerDlg.textApply": "<PERSON>ə<PERSON><PERSON><PERSON> edin:", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "<PERSON><PERSON> dəyəri ilə başlayır", "SSE.Views.FormatRulesManagerDlg.textBelow": "<PERSON>ta qiymətdən aşağı", "SSE.Views.FormatRulesManagerDlg.textBetween": "{0} və {1} arasındadır", "SSE.Views.FormatRulesManagerDlg.textCellValue": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textColorScale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textContains": "<PERSON><PERSON> dəyəri ehtiva edir", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "<PERSON><PERSON> boş dəyərdən ibarətdir", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Xanada xəta var", "SSE.Views.FormatRulesManagerDlg.textDelete": "Silin", "SSE.Views.FormatRulesManagerDlg.textDown": "Qaydanı aşağı apar", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Dublikat Qiymətlər", "SSE.Views.FormatRulesManagerDlg.textEdit": "Redaktə edin", "SSE.Views.FormatRulesManagerDlg.textEnds": "<PERSON>ana dəyəri ilə başa çatır", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "<PERSON>ə<PERSON><PERSON>r və ya Orta Qiymətdən Yuxarı", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "<PERSON><PERSON><PERSON><PERSON>r və ya Orta Qiymətdən Aşağı", "SSE.Views.FormatRulesManagerDlg.textFormat": "Format", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Piktoqram yı<PERSON>ı<PERSON>ı", "SSE.Views.FormatRulesManagerDlg.textNew": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "{0} və {1} a<PERSON><PERSON><PERSON> deyil", "SSE.Views.FormatRulesManagerDlg.textNotContains": "<PERSON><PERSON> də<PERSON>ə<PERSON> etmir", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "<PERSON><PERSON><PERSON> boş dəyər yoxdur", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Xanada xəta yoxdur", "SSE.Views.FormatRulesManagerDlg.textRules": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textScope": "üçün <PERSON>ş<PERSON><PERSON>rma qay<PERSON>ını göstərin", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Məlumatı seçin", "SSE.Views.FormatRulesManagerDlg.textSelection": "Hazırki seçim", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "<PERSON>u yekun", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Bu iş vərəqi", "SSE.Views.FormatRulesManagerDlg.textThisTable": "Bu cədvəl", "SSE.Views.FormatRulesManagerDlg.textUnique": "Unikal dəyərlər", "SSE.Views.FormatRulesManagerDlg.textUp": "Qaydanı yuxarı apar", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Bu element başqa istifadəçi tərəfindən redaktə olunur.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Şərti Formatlaşdırma", "SSE.Views.FormatSettingsDialog.textCategory": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textDecimal": "Onluq", "SSE.Views.FormatSettingsDialog.textFormat": "Format", "SSE.Views.FormatSettingsDialog.textLinked": "Mən<PERSON>ə ilə əlaqəli", "SSE.Views.FormatSettingsDialog.textSeparator": "1000 ayırıcıdan istifadə edin", "SSE.Views.FormatSettingsDialog.textSymbols": "Simvollar", "SSE.Views.FormatSettingsDialog.textTitle": "Nömrə Formatı", "SSE.Views.FormatSettingsDialog.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtAs10": "Onda birlərlə (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "<PERSON><PERSON><PERSON><PERSON>ə birlərlə (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "On altıda birlərlə (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "İkidə birlərlə (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "<PERSON><PERSON><PERSON><PERSON><PERSON> birlərlə (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Səkkiz<PERSON>ə birlərlə(4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustom": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Fərdi nömrə formatını diqqətlə daxil edin. Cədvəl Redaktoru xlsx faylına təsir edə biləcək xətalar üçün xüsusi formatları yoxlamır.", "SSE.Views.FormatSettingsDialog.txtDate": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtFraction": "Kəsr", "SSE.Views.FormatSettingsDialog.txtGeneral": "Ümumi", "SSE.Views.FormatSettingsDialog.txtNone": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNumber": "Nömrə", "SSE.Views.FormatSettingsDialog.txtPercentage": "Faiz", "SSE.Views.FormatSettingsDialog.txtSample": "Nümunə:", "SSE.Views.FormatSettingsDialog.txtScientific": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtText": "Mətn", "SSE.Views.FormatSettingsDialog.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtUpto1": "Bir rəqəmə qədər (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "<PERSON><PERSON> rəqəmə qədər (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "<PERSON><PERSON> rəqəmə qədər (131/135)", "SSE.Views.FormulaDialog.sDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.textGroupDescription": "Funksiya <PERSON>", "SSE.Views.FormulaDialog.textListDescription": "Funksiyanı Seçin", "SSE.Views.FormulaDialog.txtRecommended": "Tövsi<PERSON>ə olunanlar", "SSE.Views.FormulaDialog.txtSearch": "Axtarış", "SSE.Views.FormulaDialog.txtTitle": "Funksiya Daxil edin", "SSE.Views.FormulaTab.capBtnRemoveArr": "Remove Arrows", "SSE.Views.FormulaTab.capBtnTraceDep": "Trace Dependents", "SSE.Views.FormulaTab.capBtnTracePrec": "Trace Precedents", "SSE.Views.FormulaTab.textAutomatic": "Avtomatik", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "<PERSON>i vərəqi hesa<PERSON>yın", "SSE.Views.FormulaTab.textCalculateWorkbook": "İş kitabını hesablayın", "SSE.Views.FormulaTab.textManual": "Əl ilə", "SSE.Views.FormulaTab.tipCalculate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Bütün iş kitabını hesablayın", "SSE.Views.FormulaTab.tipRemoveArr": "Remove the arrows drawn by Trace Precedents or Trace Dependents", "SSE.Views.FormulaTab.tipShowFormulas": "Display the formula in each cell instead of the resulting value", "SSE.Views.FormulaTab.tipTraceDep": "Show arrows that indicate which cells are affected by the value of the selected cell", "SSE.Views.FormulaTab.tipTracePrec": "Show arrows that indicate which cells affect the value of the selected cell", "SSE.Views.FormulaTab.tipWatch": "Add cells to the Watch Window list", "SSE.Views.FormulaTab.txtAdditional": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtAutosum": "Avtomatik cəm", "SSE.Views.FormulaTab.txtAutosumTip": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtCalculation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormula": "Funksiya", "SSE.Views.FormulaTab.txtFormulaTip": "Funksiya daxil edin", "SSE.Views.FormulaTab.txtMore": "<PERSON>ha <PERSON> funksiya", "SSE.Views.FormulaTab.txtRecent": "<PERSON> z<PERSON><PERSON><PERSON> olu<PERSON>", "SSE.Views.FormulaTab.txtRemDep": "Remove Dependents Arrows", "SSE.Views.FormulaTab.txtRemPrec": "Remove Precedents Arrows", "SSE.Views.FormulaTab.txtShowFormulas": "Show Formulas", "SSE.Views.FormulaTab.txtWatch": "Watch Window", "SSE.Views.FormulaWizard.textAny": "istənilən", "SSE.Views.FormulaWizard.textArgument": "Arqument", "SSE.Views.FormulaWizard.textFunction": "Funksiya", "SSE.Views.FormulaWizard.textFunctionRes": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textHelp": "Bu funksiyaya kömək edin", "SSE.Views.FormulaWizard.textLogical": "mə<PERSON><PERSON>", "SSE.Views.FormulaWizard.textNoArgs": "<PERSON>u <PERSON><PERSON>ın heç bir arqumenti yoxdur", "SSE.Views.FormulaWizard.textNoArgsDesc": "this argument has no description", "SSE.Views.FormulaWizard.textNumber": "nömrə", "SSE.Views.FormulaWizard.textReadMore": "Read more", "SSE.Views.FormulaWizard.textRef": "istinad", "SSE.Views.FormulaWizard.textText": "mətn", "SSE.Views.FormulaWizard.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textValue": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.GoalSeekDlg.textChangingCell": "By changing cell", "SSE.Views.GoalSeekDlg.textDataRangeError": "The formula is missing a range", "SSE.Views.GoalSeekDlg.textMustContainFormula": "The cell must contain a formula", "SSE.Views.GoalSeekDlg.textMustContainValue": "Cell must contain a value", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Formula in cell must result in a number", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Reference must be to a single cell", "SSE.Views.GoalSeekDlg.textSelectData": "Select data", "SSE.Views.GoalSeekDlg.textSetCell": "Set cell", "SSE.Views.GoalSeekDlg.textTitle": "Goal seek", "SSE.Views.GoalSeekDlg.textToValue": "To value", "SSE.Views.GoalSeekDlg.txtEmpty": "This field is required", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.GoalSeekStatusDlg.textContinue": "Continue", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Current value:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Goal seeking with cell {0} found a solution.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Goal seeking with cell {0} may not have found a solution.", "SSE.Views.GoalSeekStatusDlg.textPause": "Pause", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Goal seeking with cell {0} on iteration #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "Step", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Target value:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Goal seek status", "SSE.Views.HeaderFooterDialog.textAlign": "<PERSON>ə<PERSON><PERSON>ə kənar bo<PERSON>lu<PERSON><PERSON>ı ilə düzləndir", "SSE.Views.HeaderFooterDialog.textAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> səhi<PERSON>ələr", "SSE.Views.HeaderFooterDialog.textBold": "Qalın", "SSE.Views.HeaderFooterDialog.textCenter": "Mərkəz", "SSE.Views.HeaderFooterDialog.textColor": "Mətn rəngi", "SSE.Views.HeaderFooterDialog.textDate": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Fərqli ilk səhifə", "SSE.Views.HeaderFooterDialog.textDiffOdd": "<PERSON>ər<PERSON>li tək və cüt səhifələr", "SSE.Views.HeaderFooterDialog.textEven": "<PERSON><PERSON><PERSON> nömrəli səhifə", "SSE.Views.HeaderFooterDialog.textFileName": "<PERSON><PERSON> adı", "SSE.Views.HeaderFooterDialog.textFirst": "<PERSON><PERSON><PERSON><PERSON> səhifə", "SSE.Views.HeaderFooterDialog.textFooter": "Aşağı sərlövhə", "SSE.Views.HeaderFooterDialog.textHeader": "Başlıq", "SSE.Views.HeaderFooterDialog.textImage": "Picture", "SSE.Views.HeaderFooterDialog.textInsert": "Daxil edin", "SSE.Views.HeaderFooterDialog.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textLeft": "Sol", "SSE.Views.HeaderFooterDialog.textMaxError": "Daxil etdiyiniz mətn sətri çox uzundur. İstifadə olunan simvolların sayını azaldın.", "SSE.Views.HeaderFooterDialog.textNewColor": "Yeni Fərdi Rəng Əlavə Edin", "SSE.Views.HeaderFooterDialog.textOdd": "<PERSON>ə<PERSON>ə", "SSE.Views.HeaderFooterDialog.textPageCount": "<PERSON><PERSON><PERSON><PERSON><PERSON> sayı", "SSE.Views.HeaderFooterDialog.textPageNum": "<PERSON>ə<PERSON><PERSON>ə nömrəsi", "SSE.Views.HeaderFooterDialog.textPresets": "Öncəqurmalar", "SSE.Views.HeaderFooterDialog.textRight": "Sağ", "SSE.Views.HeaderFooterDialog.textScale": "<PERSON>ənəd ilə <PERSON>", "SSE.Views.HeaderFooterDialog.textSheet": "<PERSON>ə<PERSON><PERSON><PERSON> adı", "SSE.Views.HeaderFooterDialog.textStrikeout": "Üstüxətli", "SSE.Views.HeaderFooterDialog.textSubscript": "Aşağı İndeks", "SSE.Views.HeaderFooterDialog.textSuperscript": "Yuxarı İndeks", "SSE.Views.HeaderFooterDialog.textTime": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textTitle": "Yuxarı Sərlövhə/Aşağı Sərlövhə Parametrləri", "SSE.Views.HeaderFooterDialog.textUnderline": "Altından xətt çə<PERSON>lmiş", "SSE.Views.HeaderFooterDialog.tipFontName": "Şrift", "SSE.Views.HeaderFooterDialog.tipFontSize": "Şrift Ölçüsü", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "<PERSON><PERSON>qə", "SSE.Views.HyperlinkSettingsDialog.strRange": "Diapazon", "SSE.Views.HyperlinkSettingsDialog.strSheet": "Və<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Kopyalayın", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Seçilmiş diapazon", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Başlığı bura daxil edin", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Buraya keçid daxil edin", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Alətİzahını bura daxil edin", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "<PERSON><PERSON><PERSON>i əldə edin", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "<PERSON><PERSON><PERSON>apazonu", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "XƏTA! Yanlış xana diapazonu", "SSE.Views.HyperlinkSettingsDialog.textNames": "<PERSON><PERSON><PERSON><PERSON> ed<PERSON>", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Məlumatı seçin", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Və<PERSON>əqlər", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Ekran İzahı Mətn", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Hiperlink Parametrləri", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Bu sahə tələb olunur", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Bu sahə \"http://www.example.com\" formatında URL olmalıdır", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Bu sahə 2083 simvolla məhdudlaşır", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "SSE.Views.ImageSettings.strTransparency": "Opacity", "SSE.Views.ImageSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCrop": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFit": "<PERSON>ə<PERSON><PERSON><PERSON>ə", "SSE.Views.ImageSettings.textCropToShape": "Crop to shape", "SSE.Views.ImageSettings.textEdit": "Redaktə edin", "SSE.Views.ImageSettings.textEditObject": "Obyekti redaktə edin", "SSE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textFromFile": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textFromStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textFromUrl": "URL-dən", "SSE.Views.ImageSettings.textHeight": "Hündürlük", "SSE.Views.ImageSettings.textHint270": "90° Saat əqrəbinin əksi istiqamətində Döndər", "SSE.Views.ImageSettings.textHint90": "90° saat əqrəbi istiqamətində döndər", "SSE.Views.ImageSettings.textHintFlipH": "Üfüqi olaraq çevir", "SSE.Views.ImageSettings.textHintFlipV": "<PERSON><PERSON><PERSON> çevir", "SSE.Views.ImageSettings.textInsert": "Təsviri Əvəz edin", "SSE.Views.ImageSettings.textKeepRatio": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textOriginalSize": "Faktiki Ölçü", "SSE.Views.ImageSettings.textRecentlyUsed": "Recently used", "SSE.Views.ImageSettings.textResetCrop": "Reset crop", "SSE.Views.ImageSettings.textRotate90": " 90° Döndər", "SSE.Views.ImageSettings.textRotation": "Döndərmə", "SSE.Views.ImageSettings.textSize": "Ölçü", "SSE.Views.ImageSettings.textWidth": "En", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Xanalarla hərəkət etməyin və ya ölçü götürməyin", "SSE.Views.ImageSettingsAdvanced.textAlt": "Alternativ Mətn", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Şəkildə, avtoformada, diaqramda və ya cədvəldə hansı məlumatın olduğunu daha yaxşı anlamağa kömək etmək üçün görmə və ya idrak qüsurları olan insanlara oxunacaq vizual obyekt məlumatının alternativ mətn əsaslı təqdimatı.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Başlıq", "SSE.Views.ImageSettingsAdvanced.textAngle": "Bucaq", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Döndə<PERSON>miş", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Üfüqi o<PERSON>aq", "SSE.Views.ImageSettingsAdvanced.textOneCell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lakin xana<PERSON>", "SSE.Views.ImageSettingsAdvanced.textRotation": "Döndərmə", "SSE.Views.ImageSettingsAdvanced.textSnap": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textTitle": "Təsvir-Tə<PERSON><PERSON>l <PERSON>rlər", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Xanalarla köçürün və ölçün", "SSE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "SSE.Views.ImportFromXmlDialog.textDestination": "Choose, where to place the data", "SSE.Views.ImportFromXmlDialog.textExist": "Existing worksheet", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ImportFromXmlDialog.textNew": "New worksheet", "SSE.Views.ImportFromXmlDialog.textSelectData": "Select data", "SSE.Views.ImportFromXmlDialog.textTitle": "Import data", "SSE.Views.ImportFromXmlDialog.txtEmpty": "This field is required", "SSE.Views.LeftMenu.ariaLeftMenu": "Left menu", "SSE.Views.LeftMenu.tipAbout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipChat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipComments": "Şə<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipFile": "<PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "Qoşmalar", "SSE.Views.LeftMenu.tipSearch": "Axtarış", "SSE.Views.LeftMenu.tipSpellcheck": "Orfoqrafiyanın <PERSON>", "SSE.Views.LeftMenu.tipSupport": "Əks-əlaqə & Dəstək", "SSE.Views.LeftMenu.txtDeveloper": "DEVELOPER REJİMİ", "SSE.Views.LeftMenu.txtEditor": "Spreadsheet Editor", "SSE.Views.LeftMenu.txtLimit": "<PERSON><PERSON><PERSON><PERSON>ı<PERSON>", "SSE.Views.LeftMenu.txtTrial": "SINAQ REJİMİ", "SSE.Views.LeftMenu.txtTrialDev": "Sınaq Tərtibatçı Rejimi", "SSE.Views.MacroDialog.textMacro": "Makro ad", "SSE.Views.MacroDialog.textTitle": "<PERSON><PERSON><PERSON> edin", "SSE.Views.MainSettingsPrint.okButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strBottom": "Aşağı", "SSE.Views.MainSettingsPrint.strLandscape": "Albom", "SSE.Views.MainSettingsPrint.strLeft": "Sol", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPrint": "Çap edin", "SSE.Views.MainSettingsPrint.strPrintTitles": "Başlıqları Çap edin", "SSE.Views.MainSettingsPrint.strRight": "Sağ", "SSE.Views.MainSettingsPrint.strTop": "Yuxarı", "SSE.Views.MainSettingsPrint.textActualSize": "Faktiki Ölçü", "SSE.Views.MainSettingsPrint.textCustom": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textCustomOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textFitCols": "<PERSON><PERSON><PERSON><PERSON><PERSON> sütunları bir səhifəyə yerləşdirin", "SSE.Views.MainSettingsPrint.textFitPage": "Vərəqi Bir Səhifəyə Yerləşdirin", "SSE.Views.MainSettingsPrint.textFitRows": "Bü<PERSON>ün S<PERSON>tirləri Bir <PERSON>əhifəyə yerləşdirin", "SSE.Views.MainSettingsPrint.textPageOrientation": "<PERSON><PERSON><PERSON><PERSON><PERSON> İstiqaməti", "SSE.Views.MainSettingsPrint.textPageScaling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPageSize": "Səhifə Ölçüsü", "SSE.Views.MainSettingsPrint.textPrintGrid": "<PERSON> xə<PERSON>əri <PERSON> edin", "SSE.Views.MainSettingsPrint.textPrintHeadings": "<PERSON>ə<PERSON>r və Sütun Başlıqlarını Çap edin", "SSE.Views.MainSettingsPrint.textRepeat": "Təkrarlayın...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Soldakı sütunları təkrarlayın", "SSE.Views.MainSettingsPrint.textRepeatTop": "Yuxarıdakı sətirləri təkrarl<PERSON>ın", "SSE.Views.MainSettingsPrint.textSettings": "<PERSON><PERSON><PERSON>n <PERSON>", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Mövcud adlandırılmış diapazonlar redaktə edilə bilməz və <br>hazırda onlardan bəziləri redaktə olunduğu üçün yeniləri yaradıla bilməz.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "<PERSON>əyi<PERSON> edil<PERSON> ad", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Xəbərdarlıq", "SSE.Views.NamedRangeEditDlg.strWorkbook": "İş kitabı", "SSE.Views.NamedRangeEditDlg.textDataRange": "Məlumat Diapazonu", "SSE.Views.NamedRangeEditDlg.textExistName": "XƏTA! Belə bir ada malik diapazon artıq mövcuddur", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Ad hərf və ya alt xətt ilə başlamalı və etibarsız simvollardan ibarət olmamalıdır.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "XƏTA! Yanlış xana diapazonu", "SSE.Views.NamedRangeEditDlg.textIsLocked": "XƏTA! Bu element başqa istifadəçi tərəfindən redaktə olunur.", "SSE.Views.NamedRangeEditDlg.textName": "Ad", "SSE.Views.NamedRangeEditDlg.textReservedName": "İstifadə etməyə çalışdığınız ada artıq xana düsturlarında istinad edilib. Zə<PERSON>ət olmasa başqa ad istifadə edin.", "SSE.Views.NamedRangeEditDlg.textScope": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textSelectData": "Məlumatı Seçin", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Bu sahə tələb olunur", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Adı redaktə edin", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "<PERSON><PERSON>", "SSE.Views.NamedRangePasteDlg.textNames": "Adlandırılmış Diapazonlar", "SSE.Views.NamedRangePasteDlg.txtTitle": "<PERSON>ı <PERSON>pışdırın", "SSE.Views.NameManagerDlg.closeButtonText": "Bağlayın", "SSE.Views.NameManagerDlg.guestText": "Qonaq", "SSE.Views.NameManagerDlg.lockText": "Locked", "SSE.Views.NameManagerDlg.textDataRange": "Məlumat Diapazonu", "SSE.Views.NameManagerDlg.textDelete": "Silin", "SSE.Views.NameManagerDlg.textEdit": "Redaktə edin", "SSE.Views.NameManagerDlg.textEmpty": "Adlandırılmış diapazon hələ yaradılmayıb.<br>Ən azı bir adlandırılmış diapazon yaradın və o, bu sahədə görünəcək.", "SSE.Views.NameManagerDlg.textFilter": "Süzgəc", "SSE.Views.NameManagerDlg.textFilterAll": "Hamısı", "SSE.Views.NameManagerDlg.textFilterDefNames": "<PERSON><PERSON><PERSON><PERSON> ed<PERSON>", "SSE.Views.NameManagerDlg.textFilterSheet": "Və<PERSON><PERSON><PERSON>ə Daxil edilən Adlar", "SSE.Views.NameManagerDlg.textFilterTableNames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> adları", "SSE.Views.NameManagerDlg.textFilterWorkbook": "İş kitabına Daxil edilən <PERSON>", "SSE.Views.NameManagerDlg.textNew": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textnoNames": "Filtrinizə uyğun heç bir adlandırılmış diapazon tapılmadı.", "SSE.Views.NameManagerDlg.textRanges": "Adlandırılmış Diapazonlar", "SSE.Views.NameManagerDlg.textScope": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textWorkbook": "İş kitabı", "SSE.Views.NameManagerDlg.tipIsLocked": "Bu element başqa istifadəçi tərəfindən redaktə olunur.", "SSE.Views.NameManagerDlg.txtTitle": "<PERSON>", "SSE.Views.NameManagerDlg.warnDelete": "{0} adını silmək istədiyinizdən əminsiniz?", "SSE.Views.PageMarginsDialog.textBottom": "Aşağı", "SSE.Views.PageMarginsDialog.textCenter": "Center on page", "SSE.Views.PageMarginsDialog.textHor": "Horizontally", "SSE.Views.PageMarginsDialog.textLeft": "Sol", "SSE.Views.PageMarginsDialog.textRight": "Sağ", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "Yuxarı", "SSE.Views.PageMarginsDialog.textVert": "Vertically", "SSE.Views.PageMarginsDialog.textWarning": "Warning", "SSE.Views.PageMarginsDialog.warnCheckMargings": "Margins are incorrect", "SSE.Views.ParagraphSettings.strLineHeight": "Sətirarası İnterval", "SSE.Views.ParagraphSettings.strParagraphSpacing": "<PERSON><PERSON><PERSON> İntervalı", "SSE.Views.ParagraphSettings.strSpacingAfter": "Sonra", "SSE.Views.ParagraphSettings.strSpacingBefore": "Əvvəl", "SSE.Views.ParagraphSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAt": "Burada:", "SSE.Views.ParagraphSettings.textAtLeast": "Ən azı", "SSE.Views.ParagraphSettings.textAuto": "Çoxsaylı", "SSE.Views.ParagraphSettings.textExact": "<PERSON>", "SSE.Views.ParagraphSettings.txtAutoText": "Avtomatik", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "G<PERSON><PERSON><PERSON><PERSON>ən nişanlar bu sahədə göstərilir", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Hamısı böyük hərflə", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "İkiqat Üstüxətli", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Boşluqlar", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Sol", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Sətirarası İnterval", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Sağ", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Sonra", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Əvvəl", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "vasitəsi ilə", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Şrift", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Boşluq və İnterval", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON> baş hərflər", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "İnterval", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Üstüxətli", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Aşağı İndeks", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Yuxarı İndeks", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Tablar", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Düzləndirmə", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Çoxsaylı", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Simvol İntervalı", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textExact": "<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Çıxıntı", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Kənarlara düzləndirilmiş", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(yoxdur)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Silin", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Hamısını Sil", "SSE.Views.ParagraphSettingsAdvanced.textSet": "<PERSON>üə<PERSON>yən edin", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Mərkəz", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Sol", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Sağ", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Paraqraf - <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Avtomatik", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Delete", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "Duplicate", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Edit", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Formula", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Items Name", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "New", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Calculated Items in", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "ilə başa çatmır", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "ibarətdir:", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "eh<PERSON>va etmir", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "-dən b<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "-dən bö<PERSON><PERSON>k və ya bərabərdir", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "-dən azdır", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "-dən az və ya bərabərdir", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "ilə baş<PERSON>ır", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "ilə başlamır", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "ilə başa çatır", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "<PERSON><PERSON> <PERSON><PERSON><PERSON>:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Hər hansı simvolu təqdim etmək üçün ? istifadə edilir", "SSE.Views.PivotDigitalFilterDialog.textUse2": "İstənilən xarakter seriyasını təqdim etmək üçün * istifadə edin", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "və", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textAuto": "Avtomatik", "SSE.Views.PivotGroupDialog.textBy": "vasitəsi ilə", "SSE.Views.PivotGroupDialog.textDays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textEnd": "Başa çatır", "SSE.Views.PivotGroupDialog.textError": "Bu sahə rəqəmli dəyər olmalıdır", "SSE.Views.PivotGroupDialog.textGreaterError": "Son nömrə baş<PERSON><PERSON>ıc nömrədən bö<PERSON><PERSON>k olmalıdır", "SSE.Views.PivotGroupDialog.textHour": "Saatlar", "SSE.Views.PivotGroupDialog.textMin": "Dəqi<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMonth": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textNumDays": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sayı", "SSE.Views.PivotGroupDialog.textQuart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textSec": "San<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textStart": "Başlayır", "SSE.Views.PivotGroupDialog.textYear": "İllər", "SSE.Views.PivotGroupDialog.txtTitle": "Qruplaşdırma", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "You can use Calculated Items for basic calculations between different items within a single field", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Insert into formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "<PERSON><PERSON>", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Item name", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Items", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Read more", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Insert Calculated Item in", "SSE.Views.PivotSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textFields": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textFilters": "Süzgəclər", "SSE.Views.PivotSettings.textRows": "Sə<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddColumn": "<PERSON><PERSON><PERSON>lara Əlavə edin", "SSE.Views.PivotSettings.txtAddFilter": "Filtrlərə Əlavə edin", "SSE.Views.PivotSettings.txtAddRow": "Sətirlərə Əlavə edin", "SSE.Views.PivotSettings.txtAddValues": "D<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ə Əlavə edin", "SSE.Views.PivotSettings.txtFieldSettings": "<PERSON><PERSON><PERSON> Parametrlə<PERSON>", "SSE.Views.PivotSettings.txtMoveBegin": "Əvvələ Apar", "SSE.Views.PivotSettings.txtMoveColumn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtMoveDown": "Aşağı Apar", "SSE.Views.PivotSettings.txtMoveEnd": "<PERSON><PERSON>", "SSE.Views.PivotSettings.txtMoveFilter": "Süzgəclə<PERSON>ə Keçin", "SSE.Views.PivotSettings.txtMoveRow": "Sə<PERSON><PERSON><PERSON><PERSON><PERSON> Keçin", "SSE.Views.PivotSettings.txtMoveUp": "Yuxarı apar", "SSE.Views.PivotSettings.txtMoveValues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtRemove": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.strLayout": "Ad və Düzüm", "SSE.Views.PivotSettingsAdvanced.textAlt": "Alternativ Mətn", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Təsvirdə, avtoformada, diaqramda və ya cədvəldə hansı məlumatın olduğunu daha yaxşı anlamağa kömək etmək üçün görmə və ya koqnitiv qüsurları olan insanlara oxunacaq vizual obyekt məlumatının alternativ mətn əsaslı təqdimatı.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Başlıq", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Autofit column widths on update", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Məlumat Diapazonu", "SSE.Views.PivotSettingsAdvanced.textDataSource": "<PERSON><PERSON><PERSON>at M<PERSON>ə<PERSON>", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "<PERSON><PERSON><PERSON> filtri sahəsində sahələri gö<PERSON>ərin", "SSE.Views.PivotSettingsAdvanced.textDown": "<PERSON><PERSON><PERSON><PERSON><PERSON>, sonra yuxarı", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Sahə Başlıqları", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "XƏTA! Yanlış xana diapazonu", "SSE.Views.PivotSettingsAdvanced.textOver": "<PERSON><PERSON>, sonra a<PERSON>ağı", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Məlumatı seçin", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Sütunlar üçün <PERSON>", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "<PERSON><PERSON><PERSON>r və sütunlar üçün sahə başlıqlarını göstərin", "SSE.Views.PivotSettingsAdvanced.textShowRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textTitle": "Pivot Cədvəli - Təkmil Parametrlər", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Bir sütunda süzgəc sahələrini bildirin", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Bir sütunda süzgəc sahələrini bildirin", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Bu sahə tələb olunur", "SSE.Views.PivotSettingsAdvanced.txtName": "Ad", "SSE.Views.PivotShowDetailDialog.textDescription": "Choose the field containing the detail you want to show:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Show Detail", "SSE.Views.PivotTable.capBlankRows": "<PERSON>ş Sətirlər", "SSE.Views.PivotTable.capGrandTotals": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.capLayout": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.capSubtotals": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.mniBottomSubtotals": "Qrupun Aşağı hissəsində bütün Ara Cəmləri gö<PERSON>ərin", "SSE.Views.PivotTable.mniInsertBlankLine": "Hər bir El<PERSON>ən sonra <PERSON>ətir daxil edin", "SSE.Views.PivotTable.mniLayoutCompact": "Kompakt Formada göstərin", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Bütün Element Etiketlərini Təkrarlamayın", "SSE.Views.PivotTable.mniLayoutOutline": "Struktur Forması<PERSON> g<PERSON>", "SSE.Views.PivotTable.mniLayoutRepeat": "Bütün Element Etiketlərini təkrarlayın", "SSE.Views.PivotTable.mniLayoutTabular": "<PERSON>ə<PERSON><PERSON><PERSON><PERSON> şə<PERSON>lində g<PERSON>ə<PERSON>", "SSE.Views.PivotTable.mniNoSubtotals": "Ara yekunları göstərməyin", "SSE.Views.PivotTable.mniOffTotals": "Sə<PERSON>rlər və Sütunlar üçün deaktivdir", "SSE.Views.PivotTable.mniOnColumnsTotals": "Yalnız Sütunlar üçün Aktiv", "SSE.Views.PivotTable.mniOnRowsTotals": "Yaln<PERSON>z Sətirlər üçün Aktiv", "SSE.Views.PivotTable.mniOnTotals": "Sətirlər və Sütunlar üçün Aktiv", "SSE.Views.PivotTable.mniRemoveBlankLine": "Hər bir <PERSON>dən sonra boş sətri silin", "SSE.Views.PivotTable.mniTopSubtotals": "Qrupun Yuxarı hissəsində bütün Ara Cəmləri gö<PERSON>ərin", "SSE.Views.PivotTable.textColBanded": "Zolaqlı Sütunlar", "SSE.Views.PivotTable.textColHeader": "S<PERSON>tun <PERSON>ı<PERSON>ları", "SSE.Views.PivotTable.textRowBanded": "Zolaqlı Sətirlər", "SSE.Views.PivotTable.textRowHeader": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.tipCalculatedItems": "Calculated items", "SSE.Views.PivotTable.tipCreatePivot": "Pivot Cədvəli əlavə edin", "SSE.Views.PivotTable.tipGrandTotals": "Ümumi yekunları göstərin və ya gizlədin", "SSE.Views.PivotTable.tipRefresh": "Məlumat mənbəyindən məlumatları yeniləyin", "SSE.Views.PivotTable.tipRefreshCurrent": "Update the information from data source for the current table", "SSE.Views.PivotTable.tipSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON> pivot cədvəli seçin", "SSE.Views.PivotTable.tipSubtotals": "Ara cəmləri gö<PERSON>ərin və ya gizləyin", "SSE.Views.PivotTable.txtCalculatedItems": "Calculated Items", "SSE.Views.PivotTable.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.PivotTable.txtCreate": "Cədvəl əlavə edin", "SSE.Views.PivotTable.txtExpandEntire": "Expand Entire Field", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Custom", "SSE.Views.PivotTable.txtGroupPivot_Dark": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Light": "Açıq", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Medium", "SSE.Views.PivotTable.txtPivotTable": "Pivot Cədvəli", "SSE.Views.PivotTable.txtRefresh": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtRefreshAll": "Refresh all", "SSE.Views.PivotTable.txtSelect": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Pivot Table Style Dark", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Pivot Table Style Light", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Pivot Table Style Medium", "SSE.Views.PrintSettings.btnDownload": "Yadda saxla və Endir", "SSE.Views.PrintSettings.btnExport": "Save & Export", "SSE.Views.PrintSettings.btnPrint": "Yadda saxla və Çap et", "SSE.Views.PrintSettings.strBottom": "Aşağı", "SSE.Views.PrintSettings.strLandscape": "Albom", "SSE.Views.PrintSettings.strLeft": "Sol", "SSE.Views.PrintSettings.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strPrint": "Çap edin", "SSE.Views.PrintSettings.strPrintTitles": "Başlıqları Çap edin", "SSE.Views.PrintSettings.strRight": "Sağ", "SSE.Views.PrintSettings.strShow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strTop": "Yuxarı", "SSE.Views.PrintSettings.textActiveSheets": "Active sheets", "SSE.Views.PrintSettings.textActualSize": "Faktiki Ölçü", "SSE.Views.PrintSettings.textAllSheets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textCurrentSheet": "<PERSON>i <PERSON>", "SSE.Views.PrintSettings.textCustom": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textCustomOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textFitCols": "<PERSON><PERSON><PERSON><PERSON><PERSON> sütunları bir səhifəyə yerləşdirin", "SSE.Views.PrintSettings.textFitPage": "Vərəqi Bir Səhifəyə Yerləşdirin", "SSE.Views.PrintSettings.textFitRows": "Bü<PERSON>ün S<PERSON>tirləri Bir <PERSON>əhifəyə yerləşdirin", "SSE.Views.PrintSettings.textHideDetails": "Detalları Gizlədin", "SSE.Views.PrintSettings.textIgnore": "Çap Sahəsinə Əhəmiyyət vermə", "SSE.Views.PrintSettings.textLayout": "Düzüm", "SSE.Views.PrintSettings.textMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintSettings.textMarginsNormal": "Normal", "SSE.Views.PrintSettings.textMarginsWide": "Wide", "SSE.Views.PrintSettings.textPageOrientation": "<PERSON><PERSON><PERSON><PERSON><PERSON> İstiqaməti", "SSE.Views.PrintSettings.textPages": "Pages:", "SSE.Views.PrintSettings.textPageScaling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPageSize": "Səhifə Ölçüsü", "SSE.Views.PrintSettings.textPrintGrid": "<PERSON> xə<PERSON>əri <PERSON> edin", "SSE.Views.PrintSettings.textPrintHeadings": "<PERSON>ə<PERSON>r və Sütun Başlıqlarını Çap edin", "SSE.Views.PrintSettings.textPrintRange": "Diapazonu Çap edin", "SSE.Views.PrintSettings.textRange": "Diapazon", "SSE.Views.PrintSettings.textRepeat": "Təkrarlayın...", "SSE.Views.PrintSettings.textRepeatLeft": "Soldakı sütunları təkrarlayın", "SSE.Views.PrintSettings.textRepeatTop": "Yuxarıdakı sətirləri təkrarl<PERSON>ın", "SSE.Views.PrintSettings.textSelection": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textShowDetails": "Detalları Göstərin", "SSE.Views.PrintSettings.textShowGrid": "<PERSON>ə<PERSON><PERSON><PERSON> g<PERSON>", "SSE.Views.PrintSettings.textShowHeadings": "<PERSON><PERSON><PERSON>r və Sütun Başlıqlarını göstərin", "SSE.Views.PrintSettings.textTitle": "Parametrləri Çap edin", "SSE.Views.PrintSettings.textTitlePDF": "PDF Parametrləri", "SSE.Views.PrintSettings.textTo": "to", "SSE.Views.PrintSettings.txtMarginsLast": "Last Custom", "SSE.Views.PrintTitlesDialog.textFirstCol": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textFirstRow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textFrozenCols": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textFrozenRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sətirlər", "SSE.Views.PrintTitlesDialog.textInvalidRange": "XƏTA! Yanlış xana diapazonu", "SSE.Views.PrintTitlesDialog.textLeft": "Soldakı sütunları təkrarlayın", "SSE.Views.PrintTitlesDialog.textNoRepeat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textRepeat": "Təkrarlayın...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Diapazon<PERSON>", "SSE.Views.PrintTitlesDialog.textTitle": "Başlıqları Çap edin", "SSE.Views.PrintTitlesDialog.textTop": "Yuxarıdakı sətirləri təkrarl<PERSON>ın", "SSE.Views.PrintWithPreview.txtActiveSheets": "Active sheets", "SSE.Views.PrintWithPreview.txtActualSize": "Actual size", "SSE.Views.PrintWithPreview.txtAllSheets": "All sheets", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Apply to all sheets", "SSE.Views.PrintWithPreview.txtBothSides": "Print on both sides", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "Flip pages on long edge", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "Flip pages on short edge", "SSE.Views.PrintWithPreview.txtBottom": "Bottom", "SSE.Views.PrintWithPreview.txtCopies": "Copies", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Current sheet", "SSE.Views.PrintWithPreview.txtCustom": "Custom", "SSE.Views.PrintWithPreview.txtCustomOptions": "Custom options", "SSE.Views.PrintWithPreview.txtEmptyTable": "There is nothing to print because the table is empty", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "First page number:", "SSE.Views.PrintWithPreview.txtFitCols": "Fit All Columns on One Page", "SSE.Views.PrintWithPreview.txtFitPage": "<PERSON>t Sheet on One Page", "SSE.Views.PrintWithPreview.txtFitRows": "Fit All Rows on One Page", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Gridlines and headings", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Header/footer settings", "SSE.Views.PrintWithPreview.txtIgnore": "Ignore print area", "SSE.Views.PrintWithPreview.txtLandscape": "Landscape", "SSE.Views.PrintWithPreview.txtLeft": "Left", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMarginsLast": "Last Custom", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Normal", "SSE.Views.PrintWithPreview.txtMarginsWide": "Wide", "SSE.Views.PrintWithPreview.txtOf": "of {0}", "SSE.Views.PrintWithPreview.txtOneSide": "Print one sided", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "SSE.Views.PrintWithPreview.txtPage": "Page", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Page number invalid", "SSE.Views.PrintWithPreview.txtPageOrientation": "Page orientation", "SSE.Views.PrintWithPreview.txtPages": "Pages:", "SSE.Views.PrintWithPreview.txtPageSize": "Page size", "SSE.Views.PrintWithPreview.txtPortrait": "Portrait", "SSE.Views.PrintWithPreview.txtPrint": "Print", "SSE.Views.PrintWithPreview.txtPrintGrid": "Print gridlines", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Print row and column headings", "SSE.Views.PrintWithPreview.txtPrintRange": "Print range", "SSE.Views.PrintWithPreview.txtPrintSides": "Print sides", "SSE.Views.PrintWithPreview.txtPrintTitles": "Print titles", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Print to PDF", "SSE.Views.PrintWithPreview.txtRepeat": "Repeat...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Repeat columns at left", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Repeat rows at top", "SSE.Views.PrintWithPreview.txtRight": "Right", "SSE.Views.PrintWithPreview.txtSave": "Save", "SSE.Views.PrintWithPreview.txtScaling": "Sc<PERSON>", "SSE.Views.PrintWithPreview.txtSelection": "Selection", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Settings of sheet", "SSE.Views.PrintWithPreview.txtSheet": "Sheet: {0}", "SSE.Views.PrintWithPreview.txtTo": "to", "SSE.Views.PrintWithPreview.txtTop": "Top", "SSE.Views.ProtectDialog.textExistName": "XƏTA! Belə başlığa malik diapazon artıq mövcuddur", "SSE.Views.ProtectDialog.textInvalidName": "Diapazon başlığı hərflə başlamalıdır və yalnız hərflər, rəqəmlər və boşluqlardan ibarət ola bilər.", "SSE.Views.ProtectDialog.textInvalidRange": "XƏTA! Yanlış xana diapazonu", "SSE.Views.ProtectDialog.textSelectData": "Məlumatı Seçin", "SSE.Views.ProtectDialog.txtAllow": "Bu vərəqin bütün istifadəçilərinə icazə verin", "SSE.Views.ProtectDialog.txtAllowDescription": "You can unlock specific ranges for editing.", "SSE.Views.ProtectDialog.txtAllowRanges": "Allow edit ranges", "SSE.Views.ProtectDialog.txtAutofilter": "AvtoSüzgəcdən İstifadə edin", "SSE.Views.ProtectDialog.txtDelCols": "Sütunları silin", "SSE.Views.ProtectDialog.txtDelRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> silin", "SSE.Views.ProtectDialog.txtEmpty": "Bu sahə tələb olunur", "SSE.Views.ProtectDialog.txtFormatCells": "Xanaları formatlaşdır", "SSE.Views.ProtectDialog.txtFormatCols": "Sütunları formatlaşdır", "SSE.Views.ProtectDialog.txtFormatRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON> parolu eyni deyil", "SSE.Views.ProtectDialog.txtInsCols": "<PERSON>ü<PERSON><PERSON> daxil edin", "SSE.Views.ProtectDialog.txtInsHyper": "Hiperlink daxil edin", "SSE.Views.ProtectDialog.txtInsRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> əlavə edin", "SSE.Views.ProtectDialog.txtObjs": "Obyek<PERSON><PERSON>ri redaktə edin", "SSE.Views.ProtectDialog.txtOptional": "i<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPassword": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPivot": "PivotCədvəl və PivotDiaqramdan istifadə edin", "SSE.Views.ProtectDialog.txtProtect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRange": "Diapazon", "SSE.Views.ProtectDialog.txtRangeName": "Başlıq", "SSE.Views.ProtectDialog.txtRepeat": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtScen": "Ssenariləri redaktə edin", "SSE.Views.ProtectDialog.txtSelLocked": "Kilidlənmiş xanaları seçin", "SSE.Views.ProtectDialog.txtSelUnLocked": "Kilidi açılmış xanaları seçin", "SSE.Views.ProtectDialog.txtSheetDescription": "Başqalarının redaktə etmək imkanlarını məhdudlaşdırmaqla arzuolunmaz dəyişikliklərin qarşısını alın.", "SSE.Views.ProtectDialog.txtSheetTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtSort": "S<PERSON>rala", "SSE.Views.ProtectDialog.txtWarning": "Xəbərdarlıq: Əgər parolu itirsəniz və ya unutsanız, onu bərpa etmək mümkün olmayacaq. Zə<PERSON>ət olmasa onu təhlükəsiz yerdə saxlayın.", "SSE.Views.ProtectDialog.txtWBDescription": "<PERSON><PERSON><PERSON>r istifadəçilərin gizli iş vərəqlərinə baxmaqdan, iş vərəqlərini əlavə etməkdən, kö<PERSON><PERSON>rməkdən, silməkdən və ya gizlətməkdən və iş vərəqlərinin adını dəyişməkdən qorumaq üçün iş kitabınızın strukturunu parolla qoruya bilərsiniz.", "SSE.Views.ProtectDialog.txtWBTitle": "İş kitabı strukturunu Qoruyun", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "Anonymous", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Anyone", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Edit", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Denied", "SSE.Views.ProtectedRangesEditDlg.textCanView": "View", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "The range title must begin with a letter and may only contain letters, numbers, and spaces.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Remove", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "Select data", "SSE.Views.ProtectedRangesEditDlg.textYou": "you", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Access to range", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "This field is required", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "Protect", "SSE.Views.ProtectedRangesEditDlg.txtRange": "Range", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "Title", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Only you can edit this range", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Start typing name or email", "SSE.Views.ProtectedRangesManagerDlg.guestText": "Guest", "SSE.Views.ProtectedRangesManagerDlg.lockText": "Locked", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "Delete", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "Edit", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "No protected ranges have been created yet.<br>Create at least one protected range and it will appear in this field.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "Filter", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "All", "SSE.Views.ProtectedRangesManagerDlg.textNew": "New", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Protect sheet", "SSE.Views.ProtectedRangesManagerDlg.textRange": "Range", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "You can restrict editing or viewing ranges to selected people.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "Title", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Access", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Denied", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "Edit", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "Edit range", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "New range", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Protected ranges", "SSE.Views.ProtectedRangesManagerDlg.txtView": "View", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Are you sure you want to delete the protected range {0}?<br>Anyone who has edit access to the spreadsheet will be able to edit content in the range.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Are you sure you want to delete the protected ranges?<br>Anyone who has edit access to the spreadsheet will be able to edit content in those ranges.", "SSE.Views.ProtectRangesDlg.guestText": "Qonaq", "SSE.Views.ProtectRangesDlg.lockText": "Locked", "SSE.Views.ProtectRangesDlg.textDelete": "Silin", "SSE.Views.ProtectRangesDlg.textEdit": "Redaktə edin", "SSE.Views.ProtectRangesDlg.textEmpty": "Redaktə üçün heç bir diapazona icazə verilmir.", "SSE.Views.ProtectRangesDlg.textNew": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textProtect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textPwd": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRange": "Diapazon", "SSE.Views.ProtectRangesDlg.textRangesDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON> qorunarkən diapazonlar parolla kiliddən çıxarılır (bu, yalnız kilidlənmiş xanalar üçün işləyir)", "SSE.Views.ProtectRangesDlg.textTitle": "Başlıq", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Bu element başqa istifadəçi tərəfindən redaktə olunur.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Diapazonu Redaktə edin", "SSE.Views.ProtectRangesDlg.txtNewRange": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.txtNo": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.txtTitle": "İstifadəçilərə Diapazonları Redaktə Etməyə İcazə verin", "SSE.Views.ProtectRangesDlg.txtYes": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.warnDelete": "{0} adını silmək istədiyinizdən əminsiniz?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Dublikat dəyərləri silmək üçün dublikatları ehtiva edən bir və ya bir neçə sütun seçin.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Məlumatımda ba<PERSON><PERSON><PERSON><PERSON> var", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Hamısını Seçin", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Dublikatları Silin", "SSE.Views.RightMenu.ariaRightMenu": "Right menu", "SSE.Views.RightMenu.txtCellSettings": "<PERSON><PERSON>", "SSE.Views.RightMenu.txtChartSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtImageSettings": "<PERSON><PERSON><PERSON><PERSON> para<PERSON>", "SSE.Views.RightMenu.txtParagraphSettings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtPivotSettings": "Pivot Cədvəli parametrləri", "SSE.Views.RightMenu.txtSettings": "Ümumi Parametrlər", "SSE.Views.RightMenu.txtShapeSettings": "Forma parametrləri", "SSE.Views.RightMenu.txtSignatureSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtSlicerSettings": "Məhdudlaşdırıcı parametrləri", "SSE.Views.RightMenu.txtSparklineSettings": "Sparklayn Parametrləri", "SSE.Views.RightMenu.txtTableSettings": "<PERSON>ə<PERSON><PERSON><PERSON>l parametrləri", "SSE.Views.RightMenu.txtTextArtSettings": "Mətn Şəkli parametrləri", "SSE.Views.ScaleDialog.textAuto": "Avtomatik", "SSE.Views.ScaleDialog.textError": "<PERSON><PERSON><PERSON> edil<PERSON>ş dəyər səhvdir.", "SSE.Views.ScaleDialog.textFewPages": "səhi<PERSON>ələr", "SSE.Views.ScaleDialog.textFitTo": "Uyğundur", "SSE.Views.ScaleDialog.textHeight": "Hündürlük", "SSE.Views.ScaleDialog.textManyPages": "səhi<PERSON>ələr", "SSE.Views.ScaleDialog.textOnePage": "səhifə", "SSE.Views.ScaleDialog.textScaleTo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textWidth": "En", "SSE.Views.SetValueDialog.txtMaxText": "Bu sahə üçün maksimum dəyər: {0}", "SSE.Views.SetValueDialog.txtMinText": "Bu sahə üçün minimum dəyər: {0}", "SSE.Views.ShapeSettings.strBackground": "<PERSON><PERSON><PERSON> fon rəngi", "SSE.Views.ShapeSettings.strChange": "Formanı dəyiş", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strForeground": "Ön plan rəngi", "SSE.Views.ShapeSettings.strPattern": "Naxış", "SSE.Views.ShapeSettings.strShadow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strSize": "Ölçü", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strType": "Növ", "SSE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "SSE.Views.ShapeSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textAngle": "Bucaq", "SSE.Views.ShapeSettings.textBorderSizeErr": "Daxil edilmiş dəyər yanlışdır.<br>0 pt ilə 1584 pt arasında dəyər daxil edin.", "SSE.Views.ShapeSettings.textColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textDirection": "İstiqamət", "SSE.Views.ShapeSettings.textEditPoints": "Edit points", "SSE.Views.ShapeSettings.textEditShape": "Edit shape", "SSE.Views.ShapeSettings.textEmptyPattern": "<PERSON>ümu<PERSON><PERSON> yoxdur", "SSE.Views.ShapeSettings.textEyedropper": "Eyedropper", "SSE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromUrl": "URL-dən", "SSE.Views.ShapeSettings.textGradient": "Qradiyent nöqtələri", "SSE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textHint270": "90° Saat əqrəbinin əksi istiqamətində Döndər", "SSE.Views.ShapeSettings.textHint90": "90° saat əqrəbi istiqamətində döndər", "SSE.Views.ShapeSettings.textHintFlipH": "Üfüqi olaraq çevir", "SSE.Views.ShapeSettings.textHintFlipV": "<PERSON><PERSON><PERSON> çevir", "SSE.Views.ShapeSettings.textImageTexture": "Şəkil və ya Tekstura", "SSE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textMoreColors": "More colors", "SSE.Views.ShapeSettings.textNoFill": "Doldurulmasın", "SSE.Views.ShapeSettings.textNoShadow": "No Shadow", "SSE.Views.ShapeSettings.textOriginalSize": "Orijinal Ölçü", "SSE.Views.ShapeSettings.textPatternFill": "Naxış", "SSE.Views.ShapeSettings.textPosition": "Mövq<PERSON>", "SSE.Views.ShapeSettings.textRadial": "Radial", "SSE.Views.ShapeSettings.textRecentlyUsed": "Recently used", "SSE.Views.ShapeSettings.textRotate90": " 90° Döndər", "SSE.Views.ShapeSettings.textRotation": "Döndərmə", "SSE.Views.ShapeSettings.textSelectImage": "Şək<PERSON>", "SSE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textShadow": "Shadow", "SSE.Views.ShapeSettings.textStretch": "<PERSON><PERSON>ır", "SSE.Views.ShapeSettings.textStyle": "Üslub", "SSE.Views.ShapeSettings.textTexture": "Teksturdan", "SSE.Views.ShapeSettings.textTile": "Lövhəcik", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Qradiyent nöqtəsi əlavə edin", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Qradiyent nöqtəni silin", "SSE.Views.ShapeSettings.txtBrownPaper": "Qəhvəyi Kağız", "SSE.Views.ShapeSettings.txtCanvas": "Lövhə", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGrain": "Dən", "SSE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON>z <PERSON>", "SSE.Views.ShapeSettings.txtKnit": "Bərkitmək", "SSE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Mətn <PERSON>ı", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Xanalarla hərəkət etməyin və ya ölçü götürməyin", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Alternativ Mətn", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Şəkildə, avtoformada, diaqramda və ya cədvəldə hansı məlumatın olduğunu daha yaxşı anlamağa kömək etmək üçün görmə və ya idrak qüsurları olan insanlara oxunacaq vizual obyekt məlumatının alternativ mətn əsaslı təqdimatı.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Başlıq", "SSE.Views.ShapeSettingsAdvanced.textAngle": "Bucaq", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "AutoFit", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Başlanğıc Ölçü", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Başlanğıc Üslub", "SSE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Aşağı", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Başlıq Növü", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON>ü<PERSON><PERSON><PERSON><PERSON> sayı", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Son Üslub", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Düz", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Döndə<PERSON>miş", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Hündürlük", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Üfüqi o<PERSON>aq", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Qoşulma Növü", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Sol", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Şaquli kəsişmə nöqtəsi", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lakin xana<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Mətnə fiquru keçməyə icazə ver", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Mətnə uyğunlaşdırmaq üçün formanın ölçüsünü dəyişdirin", "SSE.Views.ShapeSettingsAdvanced.textRight": "Sağ", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Döndərmə", "SSE.Views.ShapeSettingsAdvanced.textRound": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSize": "Ölçü", "SSE.Views.ShapeSettingsAdvanced.textSnap": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Forma-<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTop": "Yuxarı", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Xanalarla köçürün və ölçün", "SSE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Çəkilər və Oxlar", "SSE.Views.ShapeSettingsAdvanced.textWidth": "En", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Xəbərdarlıq", "SSE.Views.SignatureSettings.strDelete": "İmzanı Silin", "SSE.Views.SignatureSettings.strDetails": "<PERSON><PERSON>za Detalları", "SSE.Views.SignatureSettings.strInvalid": "Etibarsız <PERSON>", "SSE.Views.SignatureSettings.strRequested": "<PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>", "SSE.Views.SignatureSettings.strSetup": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSign": "İşarə", "SSE.Views.SignatureSettings.strSignature": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSigner": "İmzalayan", "SSE.Views.SignatureSettings.strValid": "Etibarlı imzalar", "SSE.Views.SignatureSettings.txtContinueEditing": "İstə<PERSON><PERSON>ən halda redaktə edin", "SSE.Views.SignatureSettings.txtEditWarning": "Redaktə elektron cədvəldən imzaları siləcək.<br><PERSON><PERSON><PERSON> edilsin?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Bu imzanı silmək istəyirsiniz?<br>Bunu geri qaytarmaq mümkün deyil.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Bu elektron cədvəl imzalanmalıdır.", "SSE.Views.SignatureSettings.txtSigned": "Etibarlı imzalar elektron cədvələ əlavə edildi. Elektron cədvəl redaktədən qorunur.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Cədvəldəki bəzi rəqəmsal imzalar etibarsızdır və ya təsdiq edilə bilməz. Cədvəl redaktədən qorunur.", "SSE.Views.SlicerAddDialog.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerAddDialog.txtTitle": "Məhdudlaşdırıcılar Əlavə edin", "SSE.Views.SlicerSettings.strHideNoData": "<PERSON><PERSON><PERSON>at o<PERSON>yan <PERSON> g<PERSON>", "SSE.Views.SlicerSettings.strIndNoData": "<PERSON><PERSON> bir məlumat olmayan elementləri vizual olaraq göstərin", "SSE.Views.SlicerSettings.strShowDel": "Məlumat mənbəyindən silinmiş element<PERSON>əri gö<PERSON>ə<PERSON>", "SSE.Views.SlicerSettings.strShowNoData": "<PERSON><PERSON><PERSON> veriləni o<PERSON>", "SSE.Views.SlicerSettings.strSorting": "Sıralama və süzgəcdən keçirmə", "SSE.Views.SlicerSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> paramet<PERSON>ə<PERSON>", "SSE.Views.SlicerSettings.textAsc": "Artan ü<PERSON>rə", "SSE.Views.SlicerSettings.textAZ": "A-dan Z-yə", "SSE.Views.SlicerSettings.textButtons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textDesc": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textHeight": "Hündürlük", "SSE.Views.SlicerSettings.textHor": "Üfüqi", "SSE.Views.SlicerSettings.textKeepRatio": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textLargeSmall": "ən böyükdən kiçiyə", "SSE.Views.SlicerSettings.textLock": "Ölçü dəyişdirmə və ya köçürməni deaktiv edin", "SSE.Views.SlicerSettings.textNewOld": "ən yenidən köhnəyə", "SSE.Views.SlicerSettings.textOldNew": "ən köhnədən ən yeniyə", "SSE.Views.SlicerSettings.textPosition": "Mövq<PERSON>", "SSE.Views.SlicerSettings.textSize": "Ölçü", "SSE.Views.SlicerSettings.textSmallLarge": "ən kiçikdən ən böyüyə", "SSE.Views.SlicerSettings.textStyle": "Üslub", "SSE.Views.SlicerSettings.textVert": "Şaquli", "SSE.Views.SlicerSettings.textWidth": "En", "SSE.Views.SlicerSettings.textZA": "Z-dən A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Hündürlük", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "<PERSON><PERSON><PERSON>at o<PERSON>yan <PERSON> g<PERSON>", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "<PERSON><PERSON> bir məlumat olmayan elementləri vizual olaraq göstərin", "SSE.Views.SlicerSettingsAdvanced.strReferences": "İstinadlar", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Məlumat mənbəyindən silinmiş element<PERSON>əri gö<PERSON>ə<PERSON>", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Başlığı gö<PERSON>ərin", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "<PERSON><PERSON><PERSON> veriləni o<PERSON>", "SSE.Views.SlicerSettingsAdvanced.strSize": "Ölçü", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Sıralama və Süzgəcdən keçirmə", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Üslub", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Üslub və Ölçü", "SSE.Views.SlicerSettingsAdvanced.strWidth": "En", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Xanalarla hərəkət etməyin və ya ölçü götürməyin", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Alternativ Mətn", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Təsvirdə, avtoformada, diaqramda və ya cədvəldə hansı məlumatın olduğunu daha yaxşı anlamağa kömək etmək üçün görmə və ya koqnitiv qüsurları olan insanlara oxunacaq vizual obyekt məlumatının alternativ mətn əsaslı təqdimatı.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Başlıq", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Artan ü<PERSON>rə", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A-dan Z-yə", "SSE.Views.SlicerSettingsAdvanced.textDesc": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Düst<PERSON>larda istifadə ediləcək ad", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Başlıq", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "ən böyükdən kiçiyə", "SSE.Views.SlicerSettingsAdvanced.textName": "Ad", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "ən yenidən köhnəyə", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "ən köhnədən ən yeniyə", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lakin xana<PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "ən kiçikdən ən böyüyə", "SSE.Views.SlicerSettingsAdvanced.textSnap": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSort": "S<PERSON>rala", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "<PERSON>ən<PERSON>ə adı", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Məhdudlaşdırıcı-Təkmil Parametrlər", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Xanalarla köçürün və ölçün", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z-dən A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Bu sahə tələb olunur", "SSE.Views.SortDialog.errorEmpty": "Bütün çeşidləmə meyarlarında müəyyən edilmiş sütun və ya sıra olmalıdır.", "SSE.Views.SortDialog.errorMoreOneCol": "<PERSON>ən çox sütun seç<PERSON>.", "SSE.Views.SortDialog.errorMoreOneRow": "<PERSON><PERSON>n çox sətir se<PERSON>.", "SSE.Views.SortDialog.errorNotOriginalCol": "Seçdiyiniz sütun orijinal seçilmiş diapazonda deyil.", "SSE.Views.SortDialog.errorNotOriginalRow": "Seçdiyiniz sıra orijinal seçilmiş diapazonda deyil.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 eyni rəngə görə bir dəfədən çox çeşidlənir.<br>Dublikat çeşidləmə kriteriyalarını silin və yenidən cəhd edin.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 eyni rəngə görə bir dəfədən çox çeşidlənir.<br>Dublikat çeşidləmə kriteriyalarını silin və yenidən cəhd edin.", "SSE.Views.SortDialog.textAsc": "Artan ü<PERSON>rə", "SSE.Views.SortDialog.textAuto": "Avtomatik", "SSE.Views.SortDialog.textAZ": "A-dan Z-yə", "SSE.Views.SortDialog.textBelow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textBtnCopy": "Copy", "SSE.Views.SortDialog.textBtnDelete": "Delete", "SSE.Views.SortDialog.textBtnNew": "New", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON>", "SSE.Views.SortDialog.textColumn": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDesc": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDown": "Səviyyəni aşağı apar", "SSE.Views.SortDialog.textFontColor": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textLeft": "Sol", "SSE.Views.SortDialog.textLevels": "Levels", "SSE.Views.SortDialog.textMoreCols": "(<PERSON><PERSON><PERSON><PERSON>...)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON>ə<PERSON> sətirlər...)", "SSE.Views.SortDialog.textNone": "<PERSON><PERSON>", "SSE.Views.SortDialog.textOptions": "Se<PERSON><PERSON>l<PERSON><PERSON>", "SSE.Views.SortDialog.textOrder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textRight": "Sağ", "SSE.Views.SortDialog.textRow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textSort": "Sıralama", "SSE.Views.SortDialog.textSortBy": "Bu qayda ilə sırala", "SSE.Views.SortDialog.textThenBy": "<PERSON>ra ilə", "SSE.Views.SortDialog.textTop": "Yuxarı", "SSE.Views.SortDialog.textUp": "Səviyyəni aşağı köçürün", "SSE.Views.SortDialog.textValues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textZA": "Z-dən A", "SSE.Views.SortDialog.txtInvalidRange": "Yanlış xana di<PERSON>.", "SSE.Views.SortDialog.txtTitle": "S<PERSON>rala", "SSE.Views.SortFilterDialog.textAsc": "<PERSON><PERSON> (A-dan Z-yə) üzrə.", "SSE.Views.SortFilterDialog.textDesc": "<PERSON><PERSON><PERSON> (Z-dən A) ilə", "SSE.Views.SortFilterDialog.textNoSort": "No sort", "SSE.Views.SortFilterDialog.txtTitle": "S<PERSON>rala", "SSE.Views.SortFilterDialog.txtTitleValue": "Sort by value", "SSE.Views.SortOptionsDialog.textCase": "Böyük/Kiçik Hərfə <PERSON>s", "SSE.Views.SortOptionsDialog.textHeaders": "Məlumatımda ba<PERSON><PERSON><PERSON><PERSON> var", "SSE.Views.SortOptionsDialog.textLeftRight": "Soldan sağa sırala", "SSE.Views.SortOptionsDialog.textOrientation": "İstiqamət", "SSE.Views.SortOptionsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortOptionsDialog.textTopBottom": "Yuxarıdan a<PERSON>ğı<PERSON> sırala", "SSE.Views.SpecialPasteDialog.textAdd": "<PERSON><PERSON><PERSON> edin", "SSE.Views.SpecialPasteDialog.textAll": "Hamısı", "SSE.Views.SpecialPasteDialog.textBlanks": "Boşluqları keçin", "SSE.Views.SpecialPasteDialog.textColWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textComments": "Şə<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textDiv": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFFormat": "Düsturlar və formatlaşdırma", "SSE.Views.SpecialPasteDialog.textFNFormat": "Düsturlar və rəqəm formatları", "SSE.Views.SpecialPasteDialog.textFormats": "Formatlar", "SSE.Views.SpecialPasteDialog.textFormulas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFWidth": "Düsturlar və sütun enləri", "SSE.Views.SpecialPasteDialog.textMult": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textNone": "<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textOperation": "Əməliyyat", "SSE.Views.SpecialPasteDialog.textPaste": "Yapışdır", "SSE.Views.SpecialPasteDialog.textSub": "Çıxma", "SSE.Views.SpecialPasteDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> əlavəetmə", "SSE.Views.SpecialPasteDialog.textTranspose": "<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textValues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textVFormat": "<PERSON>ə<PERSON><PERSON><PERSON>ər və formatlaşdırma", "SSE.Views.SpecialPasteDialog.textVNFormat": "Də<PERSON><PERSON>rlər və nömrə formatları", "SSE.Views.SpecialPasteDialog.textWBorders": "Sə<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ən başqa hamısı", "SSE.Views.Spellcheck.noSuggestions": "Or<PERSON>q<PERSON><PERSON><PERSON> tək<PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur", "SSE.Views.Spellcheck.textChange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textChangeAll": "Hamısını Dəyişin", "SSE.Views.Spellcheck.textIgnore": "Ə<PERSON>ə<PERSON><PERSON><PERSON>ət vermə", "SSE.Views.Spellcheck.textIgnoreAll": "<PERSON><PERSON> birinə Əhəmiyyət vermə", "SSE.Views.Spellcheck.txtAddToDictionary": "Lüğətə Əlavə edin", "SSE.Views.Spellcheck.txtClosePanel": "Close spelling", "SSE.Views.Spellcheck.txtComplete": "Orfoqrafiya yoxlanışı tamamlandı", "SSE.Views.Spellcheck.txtDictionaryLanguage": "<PERSON><PERSON><PERSON><PERSON><PERSON> dili", "SSE.Views.Spellcheck.txtNextTip": "Növbəti sözə keçin", "SSE.Views.Spellcheck.txtSpelling": "Orfoqrafiya", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(<PERSON><PERSON> qədər <PERSON>)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "Create a copy", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(Create new spreadsheet)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Vərəqdən əvvələ köçür", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Spreadsheet", "SSE.Views.Statusbar.filteredRecordsText": "{1} qeyddən {0} filtrləndi", "SSE.Views.Statusbar.filteredText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rejimi", "SSE.Views.Statusbar.itemAverage": "Orta", "SSE.Views.Statusbar.itemCount": "Say", "SSE.Views.Statusbar.itemDelete": "Silin", "SSE.Views.Statusbar.itemHidden": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemHide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemInsert": "Daxil edin", "SSE.Views.Statusbar.itemMaximum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMinimum": "Minimum", "SSE.Views.Statusbar.itemMoveOrCopy": "Move or copy", "SSE.Views.Statusbar.itemProtect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemRename": "Adın<PERSON> də<PERSON>", "SSE.Views.Statusbar.itemStatus": "Status yadda saxlanır", "SSE.Views.Statusbar.itemSum": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemUnProtect": "Qorumanı ləğv et", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Belə adda iş vərəqi artıq mövcuddur.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "<PERSON>ə<PERSON><PERSON><PERSON> adında aşa<PERSON>dakı simvollar ola bilməz: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>ı", "SSE.Views.Statusbar.selectAllSheets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.sheetIndexText": "{1} vərəqdən {0}", "SSE.Views.Statusbar.textAverage": "Orta", "SSE.Views.Statusbar.textCount": "Say", "SSE.Views.Statusbar.textMax": "<PERSON><PERSON>", "SSE.Views.Statusbar.textMin": "Min", "SSE.Views.Statusbar.textNewColor": "Yeni Fərdi Rəng Əlavə Edin", "SSE.Views.Statusbar.textNoColor": "<PERSON><PERSON><PERSON> yoxdur", "SSE.Views.Statusbar.textSum": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipAddTab": "İş vərəqi əlavə edin", "SSE.Views.Statusbar.tipFirst": "<PERSON><PERSON><PERSON><PERSON> vərəqə sürüşdür<PERSON>n", "SSE.Views.Statusbar.tipLast": "<PERSON><PERSON><PERSON> vərəqə sürüşd<PERSON>r<PERSON>n", "SSE.Views.Statusbar.tipListOfSheets": "List of sheets", "SSE.Views.Statusbar.tipNext": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>ı<PERSON>ını sağa sürüşdürün", "SSE.Views.Statusbar.tipPrev": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>ı<PERSON>ını sola sürüşdürün", "SSE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON><PERSON><PERSON> də<PERSON>", "SSE.Views.Statusbar.tipZoomIn": "B<PERSON><PERSON>üdün", "SSE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.ungroupSheets": "Vərəq<PERSON><PERSON>rin Qruplaşmasını ləğv et", "SSE.Views.Statusbar.zoomText": "<PERSON><PERSON><PERSON><PERSON> dəyi<PERSON> {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Seçilmiş xanalar diapazonu üçün əməliyyat həyata keçirilə bilmədi.<br>Mövcuddan fərqli vahid veriənlər diapazonu seçin və yenidən cəhd edin.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Seçilmiş xana diapazonu üçün əməliyyat tamamlana bilmədi.<br> Elə diapazon seçin ki, birinci cədvəl sətri eyni sətirdə olsun<br>və nəticədə yaranan cədvəl cari cədvəl ilə üst-üstə düşsün.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Seçilmiş xana diapazonu üçün əməliyyat tamamlana bilmədi.<br><PERSON><PERSON>ə<PERSON> cədvəll<PERSON><PERSON> daxil olmadığı sətri seçin.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Cədvə<PERSON>ərdə çox xanalı massiv düsturlarına icazə verilmir.", "SSE.Views.TableOptionsDialog.txtEmpty": "Bu sahə tələb olunur", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableOptionsDialog.txtInvalidRange": "XƏTA! Yanlış xana diapazonu", "SSE.Views.TableOptionsDialog.txtNote": "Başlıqlar eyni cərgədə qalmalı və nəticədə ortaya çıxan cədvəl diapazonu orijinal cədvəl diapazonu ilə üst-üstə düşməlidir.", "SSE.Views.TableOptionsDialog.txtTitle": "Başlıq", "SSE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.insertColumnLeftText": "<PERSON> sütun daxil edin", "SSE.Views.TableSettings.insertColumnRightText": "<PERSON><PERSON> daxil edin", "SSE.Views.TableSettings.insertRowAboveText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.insertRowBelowText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.notcriticalErrorTitle": "Xəbərdarlıq", "SSE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectDataText": "<PERSON><PERSON><PERSON> Məlumatını Seçin", "SSE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textActions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>əri", "SSE.Views.TableSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textBanded": "Zolaqlı", "SSE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textConvertRange": "Diapazona çevirin", "SSE.Views.TableSettings.textEdit": "Sə<PERSON>rl<PERSON>r və Sütunlar", "SSE.Views.TableSettings.textEmptyTemplate": "Şablon yoxdur", "SSE.Views.TableSettings.textExistName": "XƏTA! Belə ada malik diapazon artıq mövcuddur", "SSE.Views.TableSettings.textFilter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textFirst": "İlk", "SSE.Views.TableSettings.textHeader": "Başlıq", "SSE.Views.TableSettings.textInvalidName": "XƏTA! Yanlış cədvəl adı", "SSE.Views.TableSettings.textIsLocked": "Bu element başqa istifadəçi tərəfindən redaktə olunur.", "SSE.Views.TableSettings.textLast": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textLongOperation": "<PERSON><PERSON><PERSON> ə<PERSON>ə<PERSON>t", "SSE.Views.TableSettings.textPivot": "Pivot cədvəli əlavə edin", "SSE.Views.TableSettings.textRemDuplicates": "Dublikatları silin", "SSE.Views.TableSettings.textReservedName": "İstifadə etməyə çalışdığınız ada artıq xana düsturlarında istinad edilib. Zə<PERSON>ət olmasa başqa ad istifadə edin.", "SSE.Views.TableSettings.textResize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ölçü<PERSON>ü<PERSON>ü də<PERSON>in", "SSE.Views.TableSettings.textRows": "Sə<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "Məlumatı Seçin", "SSE.Views.TableSettings.textSlicer": "Məhdudlaşdırıcı əlavə edin", "SSE.Views.TableSettings.textTableName": "Cəd<PERSON><PERSON>l Adı", "SSE.Views.TableSettings.textTemplate": "Şablondan Seçin", "SSE.Views.TableSettings.textTotal": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.txtGroupTable_Custom": "Custom", "SSE.Views.TableSettings.txtGroupTable_Dark": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.txtGroupTable_Light": "Açıq", "SSE.Views.TableSettings.txtGroupTable_Medium": "Medium", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Table style dark", "SSE.Views.TableSettings.txtTable_TableStyleLight": "Table style light", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "Table style medium", "SSE.Views.TableSettings.warnLongOperation": "<PERSON><PERSON><PERSON> yetirəcəyiniz əməliyyatın tamamlanması xeyli vaxt apara bilər.<br>Davam etmək istədiyinizdən əminsiniz?", "SSE.Views.TableSettingsAdvanced.textAlt": "Alternativ Mətn", "SSE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textAltTip": "Təsvirdə, avtoformada, diaqramda və ya cədvəldə hansı məlumatın olduğunu daha yaxşı anlamağa kömək etmək üçün görmə və ya koqnitiv qüsurları olan insanlara oxunacaq vizual obyekt məlumatının alternativ mətn əsaslı təqdimatı.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Başlıq", "SSE.Views.TableSettingsAdvanced.textTitle": "Cədvəl-Tək<PERSON>l Parametrlər", "SSE.Views.TextArtSettings.strBackground": "<PERSON><PERSON><PERSON> fon rəngi", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strForeground": "Ön plan rəngi", "SSE.Views.TextArtSettings.strPattern": "Naxış", "SSE.Views.TextArtSettings.strSize": "Ölçü", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strType": "Növ", "SSE.Views.TextArtSettings.textAngle": "Bucaq", "SSE.Views.TextArtSettings.textBorderSizeErr": "Daxil edilmiş dəyər yanlışdır.<br>0 pt ilə 1584 pt arasında dəyər daxil edin.", "SSE.Views.TextArtSettings.textColor": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textDirection": "İstiqamət", "SSE.Views.TextArtSettings.textEmptyPattern": "<PERSON>ümu<PERSON><PERSON> yoxdur", "SSE.Views.TextArtSettings.textFromFile": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textFromUrl": "URL-dən", "SSE.Views.TextArtSettings.textGradient": "Qradiyent nöqtələri", "SSE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textImageTexture": "Şəkil və ya Tekstura", "SSE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textNoFill": "Doldurulmasın", "SSE.Views.TextArtSettings.textPatternFill": "Naxış", "SSE.Views.TextArtSettings.textPosition": "Mövq<PERSON>", "SSE.Views.TextArtSettings.textRadial": "Radial", "SSE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStretch": "<PERSON><PERSON>ır", "SSE.Views.TextArtSettings.textStyle": "Üslub", "SSE.Views.TextArtSettings.textTemplate": "Şablon", "SSE.Views.TextArtSettings.textTexture": "Teksturdan", "SSE.Views.TextArtSettings.textTile": "Lövhəcik", "SSE.Views.TextArtSettings.textTransform": "Çev<PERSON><PERSON>", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Qradiyent nöqtəsi əlavə edin", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Qradiyent nöqtəni silin", "SSE.Views.TextArtSettings.txtBrownPaper": "Qəhvəyi Kağız", "SSE.Views.TextArtSettings.txtCanvas": "Lövhə", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGrain": "Dən", "SSE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON>z <PERSON>", "SSE.Views.TextArtSettings.txtKnit": "Bərkitmək", "SSE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtWood": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnAddComment": "Şərh əlavə edin", "SSE.Views.Toolbar.capBtnColorSchemas": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnComment": "Şərh", "SSE.Views.Toolbar.capBtnInsHeader": "Yuxarı sərlövhə/Aşağı sərlövhə", "SSE.Views.Toolbar.capBtnInsSlicer": "Məhdudlaşdırıcı", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Simvol", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageBreak": "Breaks", "SSE.Views.Toolbar.capBtnPageOrient": "İstiqamət", "SSE.Views.Toolbar.capBtnPageSize": "Ölçü", "SSE.Views.Toolbar.capBtnPrintArea": "Çap Sahəsi", "SSE.Views.Toolbar.capBtnPrintTitles": "Başlıqları Çap edin", "SSE.Views.Toolbar.capBtnScale": "Sığışdırmaq üçün Miq<PERSON>la", "SSE.Views.Toolbar.capImgAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capImgBackward": "Geriyə Göndərin", "SSE.Views.Toolbar.capImgForward": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "SSE.Views.Toolbar.capImgGroup": "Qrup", "SSE.Views.Toolbar.capInsertChart": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertChartRecommend": "Recommended Chart", "SSE.Views.Toolbar.capInsertEquation": "Tənlik", "SSE.Views.Toolbar.capInsertHyperlink": "Hiperlink", "SSE.Views.Toolbar.capInsertImage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertShape": "Forma", "SSE.Views.Toolbar.capInsertSpark": "Sparklayn", "SSE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON><PERSON><PERSON>l", "SSE.Views.Toolbar.capInsertText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertTextart": "Text Art", "SSE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniCapitalizeWords": "Capitalize Each Word", "SSE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.mniImageFromStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.mniImageFromUrl": "URL-dən <PERSON>", "SSE.Views.Toolbar.mniLowerCase": "lowercase", "SSE.Views.Toolbar.mniSentenceCase": "Sentence case.", "SSE.Views.Toolbar.mniToggleCase": "tOGGLE cASE", "SSE.Views.Toolbar.mniUpperCase": "UPPERCASE", "SSE.Views.Toolbar.textAddPrintArea": "Çap Sahəsinə Əlavə edin", "SSE.Views.Toolbar.textAlignBottom": "Aşağı Düzləndir", "SSE.Views.Toolbar.textAlignCenter": "Mərkəzə Düzləndir", "SSE.Views.Toolbar.textAlignJust": "Kənarlara düzləndirilmiş", "SSE.Views.Toolbar.textAlignLeft": "Sola Düzləndir", "SSE.Views.Toolbar.textAlignMiddle": "Or<PERSON>ya <PERSON>", "SSE.Views.Toolbar.textAlignRight": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignTop": "Yuxarı Düzləndir", "SSE.Views.Toolbar.textAllBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "SSE.Views.Toolbar.textAuto": "Avtomatik", "SSE.Views.Toolbar.textAutoColor": "Avtomatik", "SSE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "SSE.Views.Toolbar.textBlackHeart": "Black Heart Suit", "SSE.Views.Toolbar.textBold": "Qalın", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBordersStyle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBottom": "Aşağı: ", "SSE.Views.Toolbar.textBottomBorders": "Aşağı Sərhədlər", "SSE.Views.Toolbar.textBullet": "Bullet", "SSE.Views.Toolbar.textCellAlign": "Format cell alignment", "SSE.Views.Toolbar.textCenterBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textClearPrintArea": "Çap Sahəsini təmizləyin", "SSE.Views.Toolbar.textClearRule": "Qaydaları Təmizləyin", "SSE.Views.Toolbar.textClockwise": "Saat əqrəbi istiqa<PERSON>ətində Bucaq", "SSE.Views.Toolbar.textColorScales": "Rəng <PERSON>ı", "SSE.Views.Toolbar.textCopyright": "Copyright Sign", "SSE.Views.Toolbar.textCounterCw": "Saat əqrəbinin əksi istiqamətində Bucaq", "SSE.Views.Toolbar.textCustom": "Custom", "SSE.Views.Toolbar.textDataBars": "Məlumat Zolaqları", "SSE.Views.Toolbar.textDegree": "Degree Sign", "SSE.Views.Toolbar.textDelLeft": "Xanaları Sola Sürüşdürün", "SSE.Views.Toolbar.textDelPageBreak": "Remove page break", "SSE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "SSE.Views.Toolbar.textDelUp": "Xanaları Yuxarı Sürüşdürün", "SSE.Views.Toolbar.textDiagDownBorder": "Aşağı Diaqonal Sərhəd", "SSE.Views.Toolbar.textDiagUpBorder": "Yuxarı Diaqonal Sərhəd", "SSE.Views.Toolbar.textDivision": "Division Sign", "SSE.Views.Toolbar.textDollar": "Dollar Sign", "SSE.Views.Toolbar.textDone": "Done", "SSE.Views.Toolbar.textDown": "Down", "SSE.Views.Toolbar.textEditVA": "Edit Visible Area", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textEntireRow": "Bü<PERSON>ün <PERSON>", "SSE.Views.Toolbar.textEuro": "Euro Sign", "SSE.Views.Toolbar.textFewPages": "səhi<PERSON>ələr", "SSE.Views.Toolbar.textFillLeft": "Left", "SSE.Views.Toolbar.textFillRight": "Right", "SSE.Views.Toolbar.textFormatCellFill": "Format cell fill", "SSE.Views.Toolbar.textGreaterEqual": "Greater Than Or Equal To", "SSE.Views.Toolbar.textHeight": "Hündürlük", "SSE.Views.Toolbar.textHideVA": "Hide Visible Area", "SSE.Views.Toolbar.textHorizontal": "Üfüqi Mətn", "SSE.Views.Toolbar.textInfinity": "Infinity", "SSE.Views.Toolbar.textInsDown": "Xanaları Aşağı Sürüşdürün", "SSE.Views.Toolbar.textInsideBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textInsPageBreak": "Insert page break", "SSE.Views.Toolbar.textInsRight": "Xanaları Sağa Sürüşdürün", "SSE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textItems": "Element<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textLandscape": "Albom", "SSE.Views.Toolbar.textLeft": "Sol: ", "SSE.Views.Toolbar.textLeftBorders": "Sol <PERSON>", "SSE.Views.Toolbar.textLessEqual": "Less Than Or Equal To", "SSE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "SSE.Views.Toolbar.textManageRule": "Qaydaları İdarə edin", "SSE.Views.Toolbar.textManyPages": "səhi<PERSON>ələr", "SSE.Views.Toolbar.textMarginsLast": "<PERSON>", "SSE.Views.Toolbar.textMarginsNarrow": "Dar", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "Geniş", "SSE.Views.Toolbar.textMiddleBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMoreBorders": "More borders", "SSE.Views.Toolbar.textMoreFormats": "Daha çox format", "SSE.Views.Toolbar.textMorePages": "<PERSON>ha çox səhifə", "SSE.Views.Toolbar.textMoreSymbols": "More symbols", "SSE.Views.Toolbar.textNewColor": "Yeni Fərdi Rəng Əlavə Edin", "SSE.Views.Toolbar.textNewRule": "<PERSON><PERSON>", "SSE.Views.Toolbar.textNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur", "SSE.Views.Toolbar.textNotEqualTo": "Not Equal To", "SSE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "SSE.Views.Toolbar.textOnePage": "səhifə", "SSE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "SSE.Views.Toolbar.textOutBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPageMarginsCustom": "<PERSON><PERSON><PERSON> kənar b<PERSON>", "SSE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "SSE.Views.Toolbar.textPortrait": "<PERSON><PERSON>", "SSE.Views.Toolbar.textPrint": "Çap edin", "SSE.Views.Toolbar.textPrintGridlines": "Print Gridlines", "SSE.Views.Toolbar.textPrintHeadings": "Print Headings", "SSE.Views.Toolbar.textPrintOptions": "Parametrləri Çap edin", "SSE.Views.Toolbar.textRegistered": "Registered Sign", "SSE.Views.Toolbar.textResetPageBreak": "Reset all page breaks", "SSE.Views.Toolbar.textRight": "Sağ: ", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textRotateDown": "Mətni Aşağı Döndər", "SSE.Views.Toolbar.textRotateUp": "Mətni Yuxarı Döndər", "SSE.Views.Toolbar.textRtlSheet": "Sheet Right-to-left", "SSE.Views.Toolbar.textScale": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textScaleCustom": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSection": "Section Sign", "SSE.Views.Toolbar.textSelection": "<PERSON><PERSON> se<PERSON>n", "SSE.Views.Toolbar.textSeries": "Series", "SSE.Views.Toolbar.textSetPrintArea": "Çap Sahəsini Təyin edin", "SSE.Views.Toolbar.textShapesCombine": "Combine", "SSE.Views.Toolbar.textShapesFragment": "Fragment", "SSE.Views.Toolbar.textShapesIntersect": "Intersect", "SSE.Views.Toolbar.textShapesSubstract": "Subtract", "SSE.Views.Toolbar.textShapesUnion": "Union", "SSE.Views.Toolbar.textShowVA": "Show Visible Area", "SSE.Views.Toolbar.textSmile": "White Smiling Face", "SSE.Views.Toolbar.textSquareRoot": "Square Root", "SSE.Views.Toolbar.textStrikeout": "Üstüxətli", "SSE.Views.Toolbar.textSubscript": "Aşağı İndeks", "SSE.Views.Toolbar.textSubSuperscript": "Aşağı İndeks/Yuxarı İndeks", "SSE.Views.Toolbar.textSuperscript": "Yuxarı İndeks", "SSE.Views.Toolbar.textTabCollaboration": "Əməkdaşlıq", "SSE.Views.Toolbar.textTabData": "Verilənlər", "SSE.Views.Toolbar.textTabDraw": "Draw", "SSE.Views.Toolbar.textTabFile": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTabFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabHome": "<PERSON><PERSON><PERSON> səhi<PERSON>ə", "SSE.Views.Toolbar.textTabInsert": "Daxil edin", "SSE.Views.Toolbar.textTabLayout": "Düzüm", "SSE.Views.Toolbar.textTabProtect": "<PERSON>orum<PERSON>", "SSE.Views.Toolbar.textTabView": "Baxış", "SSE.Views.Toolbar.textThisPivot": "<PERSON><PERSON>n sahədən", "SSE.Views.Toolbar.textThisSheet": "Bu iş vərəqindən", "SSE.Views.Toolbar.textThisTable": "Bu cədvəldən", "SSE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTop": "Yuxarı: ", "SSE.Views.Toolbar.textTopBorders": "Yuxarı Sərhədlər", "SSE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "SSE.Views.Toolbar.textUnderline": "Altından xətt çə<PERSON>lmiş", "SSE.Views.Toolbar.textUp": "Up", "SSE.Views.Toolbar.textVertical": "Şaquli <PERSON>", "SSE.Views.Toolbar.textWidth": "En", "SSE.Views.Toolbar.textYen": "Yen Sign", "SSE.Views.Toolbar.textZoom": "<PERSON><PERSON><PERSON><PERSON> də<PERSON>", "SSE.Views.Toolbar.tipAlignBottom": "Aşağı düzləndir", "SSE.Views.Toolbar.tipAlignCenter": "Mərkəzə Düzləndir", "SSE.Views.Toolbar.tipAlignJust": "Kənarlara düzləndirilmiş", "SSE.Views.Toolbar.tipAlignLeft": "Sola düzləndir", "SSE.Views.Toolbar.tipAlignMiddle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignRight": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignTop": "Yuxarı Düzləndir", "SSE.Views.Toolbar.tipAutofilter": "Sıralama və Süzgəc", "SSE.Views.Toolbar.tipBack": "G<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipBorders": "Sə<PERSON><PERSON><PERSON><PERSON><PERSON>r", "SSE.Views.Toolbar.tipCellStyle": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipChangeCase": "Change case", "SSE.Views.Toolbar.tipChangeChart": "<PERSON><PERSON><PERSON><PERSON><PERSON> nö<PERSON>ü<PERSON> də<PERSON>in", "SSE.Views.Toolbar.tipClearStyle": "Təmizlə", "SSE.Views.Toolbar.tipColorSchemas": "<PERSON><PERSON><PERSON> sxemini də<PERSON>", "SSE.Views.Toolbar.tipCondFormat": "Şərti Formatlaşdırma", "SSE.Views.Toolbar.tipCopy": "Kopyalayın", "SSE.Views.Toolbar.tipCopyStyle": "Üslubu kopyalayın", "SSE.Views.Toolbar.tipCut": "Cut", "SSE.Views.Toolbar.tipDecDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDecFont": "Şriftin ölçüsünü azaldın", "SSE.Views.Toolbar.tipDeleteOpt": "Xanaları silin", "SSE.Views.Toolbar.tipDigStyleAccounting": "Mühasibat üslubu", "SSE.Views.Toolbar.tipDigStyleComma": "Comma style", "SSE.Views.Toolbar.tipDigStyleCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDigStylePercent": "Faiz üslubu", "SSE.Views.Toolbar.tipEditChart": "Diaqramı redaktə edin", "SSE.Views.Toolbar.tipEditChartData": "Məlumatı Seçin", "SSE.Views.Toolbar.tipEditChartType": "Diaq<PERSON>ın Növünü <PERSON>", "SSE.Views.Toolbar.tipEditHeader": "Yuxarı və aşağı sərlövhəni redaktə edin", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipFontName": "Şrift", "SSE.Views.Toolbar.tipFontSize": "Şrift Ölçüsü", "SSE.Views.Toolbar.tipHAlighOle": "Horizontal align", "SSE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipImgGroup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipIncDecimal": "<PERSON><PERSON><PERSON>u artırın", "SSE.Views.Toolbar.tipIncFont": "Şriftin ölçüsünü artırın", "SSE.Views.Toolbar.tipInsertChart": "<PERSON><PERSON><PERSON> daxil edin", "SSE.Views.Toolbar.tipInsertChartRecommend": "Insert recommended chart", "SSE.Views.Toolbar.tipInsertChartSpark": "<PERSON><PERSON><PERSON> daxil edin", "SSE.Views.Toolbar.tipInsertEquation": "Tənlik daxil edin", "SSE.Views.Toolbar.tipInsertHorizontalText": "Insert horizontal text box", "SSE.Views.Toolbar.tipInsertHyperlink": "Hiperlink əlavə edin", "SSE.Views.Toolbar.tipInsertImage": "<PERSON><PERSON><PERSON><PERSON>i Daxil edin", "SSE.Views.Toolbar.tipInsertOpt": "Xanaları daxil edin", "SSE.Views.Toolbar.tipInsertShape": "<PERSON><PERSON><PERSON> daxil edin", "SSE.Views.Toolbar.tipInsertSlicer": "Məhdudlaşdırıcı əlavə edin", "SSE.Views.Toolbar.tipInsertSmartArt": "Insert SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "<PERSON><PERSON><PERSON>n əlavə edin", "SSE.Views.Toolbar.tipInsertSymbol": "Simvol əlavə edin", "SSE.Views.Toolbar.tipInsertTable": "Cədvəl əlavə edin", "SSE.Views.Toolbar.tipInsertText": "Mətn qutusu əlavə edin", "SSE.Views.Toolbar.tipInsertTextart": "Mətn Şəkli Əlavə edin", "SSE.Views.Toolbar.tipInsertVerticalText": "Insert vertical text box", "SSE.Views.Toolbar.tipMerge": "B<PERSON>ləşdir və mərkəzə düzləndir", "SSE.Views.Toolbar.tipNone": "None", "SSE.Views.Toolbar.tipNumFormat": "Nömrə formatı", "SSE.Views.Toolbar.tipPageBreak": "Add a break where you want the next page to begin in the printed copy", "SSE.Views.Toolbar.tipPageMargins": "<PERSON>ə<PERSON><PERSON>ə kənar bo<PERSON>q<PERSON>ı", "SSE.Views.Toolbar.tipPageOrient": "<PERSON><PERSON><PERSON><PERSON><PERSON> istiqaməti", "SSE.Views.Toolbar.tipPageSize": "Səhifə ölçüsü", "SSE.Views.Toolbar.tipPaste": "Yapışdır", "SSE.Views.Toolbar.tipPrColor": "<PERSON><PERSON><PERSON><PERSON> do<PERSON>n", "SSE.Views.Toolbar.tipPrint": "Çap edin", "SSE.Views.Toolbar.tipPrintArea": "Çap sahəsi", "SSE.Views.Toolbar.tipPrintQuick": "Quick print", "SSE.Views.Toolbar.tipPrintTitles": "Başlıqları çap edin", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON><PERSON> edin", "SSE.Views.Toolbar.tipReplace": "Replace", "SSE.Views.Toolbar.tipRtlSheet": "Switch the sheet direction so that the first column is on the right side", "SSE.Views.Toolbar.tipSave": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSaveCoauth": "Dəyişikliklərinizi digər istifadəçilərin görməsi üçün yadda saxlayın.", "SSE.Views.Toolbar.tipScale": "Sığışdırmaq üçün Miq<PERSON>la", "SSE.Views.Toolbar.tipSelectAll": "Select all", "SSE.Views.Toolbar.tipSendBackward": "G<PERSON><PERSON>ə göndərin", "SSE.Views.Toolbar.tipSendForward": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "SSE.Views.Toolbar.tipShapesMerge": "Merge shapes", "SSE.Views.Toolbar.tipSynchronize": "Sənəd başqa istifadəçi tərəfindən dəyişdirilib. Dəyişikliklərinizi saxlamaq və yeniləmələri yenidən yükləmək üçün klikləyin.", "SSE.Views.Toolbar.tipTextFormatting": "More text formatting tools", "SSE.Views.Toolbar.tipTextOrientation": "İstiqamət", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipVAlighOle": "Vertical align", "SSE.Views.Toolbar.tipVisibleArea": "Visible area", "SSE.Views.Toolbar.tipWrap": "Mətni sətirdən-sətrə keçirin", "SSE.Views.Toolbar.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAdditional": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAscending": "Artan ü<PERSON>rə", "SSE.Views.Toolbar.txtAutosumTip": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCellStyle": "Cell Style", "SSE.Views.Toolbar.txtClearAll": "Hamısı", "SSE.Views.Toolbar.txtClearComments": "Şə<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFilter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> təmizləyin", "SSE.Views.Toolbar.txtClearFormat": "Format", "SSE.Views.Toolbar.txtClearFormula": "Funksiya", "SSE.Views.Toolbar.txtClearHyper": "Hiperlinkl<PERSON>r", "SSE.Views.Toolbar.txtClearText": "Mətn", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCustom": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDate": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtDateLong": "Long Date", "SSE.Views.Toolbar.txtDateShort": "Short Date", "SSE.Views.Toolbar.txtDateTime": "Tarix və Vaxt", "SSE.Views.Toolbar.txtDescending": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDollar": "$ Dollar", "SSE.Views.Toolbar.txtEuro": "€ Avro", "SSE.Views.Toolbar.txtExp": "Eksponensial", "SSE.Views.Toolbar.txtFillNum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtFilter": "Süzgəc", "SSE.Views.Toolbar.txtFormula": "Funksiya daxil edin", "SSE.Views.Toolbar.txtFraction": "Kəsr", "SSE.Views.Toolbar.txtFranc": "CHF İsveçrə frankı", "SSE.Views.Toolbar.txtGeneral": "Ümumi", "SSE.Views.Toolbar.txtInteger": "Tam <PERSON>", "SSE.Views.Toolbar.txtManageRange": "<PERSON>", "SSE.Views.Toolbar.txtMergeAcross": "Sətirlər Üzrə Birləşdirin", "SSE.Views.Toolbar.txtMergeCells": "Xanaları Birləşdir", "SSE.Views.Toolbar.txtMergeCenter": "Birləşdir & Mərkəzə düzləndir", "SSE.Views.Toolbar.txtNamedRange": "Adlandırılmış diapazonlar", "SSE.Views.Toolbar.txtNewRange": "<PERSON> edin", "SSE.Views.Toolbar.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yoxdur", "SSE.Views.Toolbar.txtNumber": "Nömrə", "SSE.Views.Toolbar.txtPasteRange": "<PERSON>ı <PERSON>pışdırın", "SSE.Views.Toolbar.txtPercentage": "Faiz", "SSE.Views.Toolbar.txtPound": "£ Funt", "SSE.Views.Toolbar.txtRouble": "₽ Rubl", "SSE.Views.Toolbar.txtScientific": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtSearch": "Axtarış", "SSE.Views.Toolbar.txtSort": "S<PERSON>rala", "SSE.Views.Toolbar.txtSortAZ": "Artma üzrə Sırala", "SSE.Views.Toolbar.txtSortZA": "Azalma üzrə Sırala", "SSE.Views.Toolbar.txtSpecial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtTableTemplate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ş<PERSON>u kimi <PERSON>ın", "SSE.Views.Toolbar.txtText": "Mətn", "SSE.Views.Toolbar.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtUnmerge": "Xanaların Birləşdirilməsini Ləğv Edin", "SSE.Views.Toolbar.txtYen": "¥ Yen", "SSE.Views.Top10FilterDialog.textType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBottom": "Aşağı", "SSE.Views.Top10FilterDialog.txtBy": "vasitəsi ilə", "SSE.Views.Top10FilterDialog.txtItems": "Element", "SSE.Views.Top10FilterDialog.txtPercent": "Faiz", "SSE.Views.Top10FilterDialog.txtSum": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtTitle": "İlk 10 AvtoSüzgəc", "SSE.Views.Top10FilterDialog.txtTop": "Yuxarı", "SSE.Views.Top10FilterDialog.txtValueTitle": "İlk 10 Süzgəc", "SSE.Views.ValueFieldSettingsDialog.textNext": "(next)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(previous)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Orta", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "<PERSON><PERSON><PERSON>ə", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Əsas element", "SSE.Views.ValueFieldSettingsDialog.txtByField": "2%-in 1%-i", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Say", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "<PERSON>ö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Fərdi ad", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "-dən fərq<PERSON>ir", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "İndeks", "SSE.Views.ValueFieldSettingsDialog.txtMax": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "<PERSON><PERSON><PERSON><PERSON> yoxdur", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "-nin faizi", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "<PERSON><PERSON><PERSON> fərqi", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% of grand total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% of parent total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% of parent column total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% of parent row total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% running total in", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Rank smallest to largest", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Rank largest to smallest", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Ümum<PERSON> Daxildir", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "kimi də<PERSON>ə<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Mənbə adı:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "StdSapma", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdSapmasız", "SSE.Views.ValueFieldSettingsDialog.txtSum": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "<PERSON><PERSON><PERSON><PERSON> sahə<PERSON>in cəmlənməsi", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "Bağlayın", "SSE.Views.ViewManagerDlg.guestText": "Qonaq", "SSE.Views.ViewManagerDlg.lockText": "Locked", "SSE.Views.ViewManagerDlg.textDelete": "Silin", "SSE.Views.ViewManagerDlg.textDuplicate": "Dublikat", "SSE.Views.ViewManagerDlg.textEmpty": "Hələ heç bir baxış yaradılmayıb.", "SSE.Views.ViewManagerDlg.textGoTo": "Baxmağa keçin", "SSE.Views.ViewManagerDlg.textLongName": "128 simvoldan az olan ad daxil edin.", "SSE.Views.ViewManagerDlg.textNew": "<PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textRename": "Adın<PERSON> də<PERSON>", "SSE.Views.ViewManagerDlg.textRenameError": "Görünüş adı boş o<PERSON>malıdır.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Görünüşün <PERSON>", "SSE.Views.ViewManagerDlg.textViews": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.tipIsLocked": "Bu element başqa istifadəçi tərəfindən redaktə olunur.", "SSE.Views.ViewManagerDlg.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> Meneceri", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Are you sure you want to delete this sheet view?", "SSE.Views.ViewManagerDlg.warnDeleteView": "Hazırda aktivləşdirilmiş '%1' görünüşünü silməyə çalışırsınız.<br>Bu görün<PERSON>ş bağlansın və silinsin?", "SSE.Views.ViewTab.capBtnFreeze": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.capBtnSheetView": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Always Show Toolbar", "SSE.Views.ViewTab.textClose": "Bağlayın", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Combine Sheet and Status Bars", "SSE.Views.ViewTab.textCreate": "<PERSON><PERSON>", "SSE.Views.ViewTab.textDefault": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textFill": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textFreezeCol": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textFreezeRow": "<PERSON>st <PERSON><PERSON>", "SSE.Views.ViewTab.textGridlines": "Gridlines", "SSE.Views.ViewTab.textHeadings": "Başlıqlar", "SSE.Views.ViewTab.textInterfaceTheme": "İnterfeys mövzusu", "SSE.Views.ViewTab.textLeftMenu": "Left Panel", "SSE.Views.ViewTab.textLine": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textRightMenu": "Right Panel", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Show frozen panes shadow", "SSE.Views.ViewTab.textTabStyle": "Tab style", "SSE.Views.ViewTab.textUnFreeze": "<PERSON><PERSON>ə<PERSON><PERSON><PERSON>ö<PERSON>ü<PERSON>", "SSE.Views.ViewTab.textZeros": "Sıfırları göstərin", "SSE.Views.ViewTab.textZoom": "<PERSON><PERSON><PERSON><PERSON> də<PERSON>", "SSE.Views.ViewTab.tipClose": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> ba<PERSON>n", "SSE.Views.ViewTab.tipCreate": "<PERSON><PERSON><PERSON><PERSON><PERSON> gö<PERSON><PERSON><PERSON>ın", "SSE.Views.ViewTab.tipFreeze": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.tipInterfaceTheme": "İnterfeys mövzusu", "SSE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipSheetView": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.tipViewNormal": "See your document in Normal view", "SSE.Views.ViewTab.tipViewPageBreak": "See where the page breaks will appear when your document is printed", "SSE.Views.ViewTab.txtViewNormal": "Normal", "SSE.Views.ViewTab.txtViewPageBreak": "Page Break Preview", "SSE.Views.WatchDialog.closeButtonText": "Close", "SSE.Views.WatchDialog.textAdd": "Add watch", "SSE.Views.WatchDialog.textBook": "Book", "SSE.Views.WatchDialog.textCell": "Cell", "SSE.Views.WatchDialog.textDelete": "Delete watch", "SSE.Views.WatchDialog.textDeleteAll": "Delete all", "SSE.Views.WatchDialog.textFormula": "Formula", "SSE.Views.WatchDialog.textName": "Name", "SSE.Views.WatchDialog.textSheet": "Sheet", "SSE.Views.WatchDialog.textValue": "Value", "SSE.Views.WatchDialog.txtTitle": "Watch window", "SSE.Views.WBProtection.hintAllowRanges": "Diapazonları redaktə etməyə icazə verin", "SSE.Views.WBProtection.hintProtectRange": "Protect range", "SSE.Views.WBProtection.hintProtectSheet": "<PERSON><PERSON><PERSON><PERSON><PERSON> qoruyun", "SSE.Views.WBProtection.hintProtectWB": "İş kitabını qoruyun", "SSE.Views.WBProtection.txtAllowRanges": "Diapazonları redaktə etməyə icazə verin", "SSE.Views.WBProtection.txtHiddenFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedCell": "Kilidlənmiş Xana", "SSE.Views.WBProtection.txtLockedShape": "<PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedText": "<PERSON>ə<PERSON>ni blokla", "SSE.Views.WBProtection.txtProtectRange": "Protect Range", "SSE.Views.WBProtection.txtProtectSheet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtProtectWB": "İş kitabını Qoruyun", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Və<PERSON><PERSON><PERSON>n müha<PERSON>zəsini ləğv etmək üçün parol daxil edin", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Və<PERSON><PERSON><PERSON>n qorumasını ləğv et", "SSE.Views.WBProtection.txtWBUnlockDescription": "İş kitabının mühafizəsini ləğv etmək üçün parol daxil edin", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}