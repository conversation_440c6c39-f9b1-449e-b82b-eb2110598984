{"cancelButtonText": "M<PERSON>gs<PERSON>", "Common.Controllers.Chat.notcriticalErrorTitle": "Figyelmeztetés", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.History.notcriticalErrorTitle": "Figyelmeztetés", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textAreaStacked": "Halmozott terület", "Common.define.chartData.textAreaStackedPer": "100% halmozott terület", "Common.define.chartData.textBar": "Sáv", "Common.define.chartData.textBarNormal": "Csoportosított <PERSON>", "Common.define.chartData.textBarNormal3d": "Csoportosított 3D-oszlop", "Common.define.chartData.textBarNormal3dPerspective": "3D oszlop", "Common.define.chartData.textBarStacked": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textBarStacked3d": "3D csoportosított <PERSON>", "Common.define.chartData.textBarStackedPer": "100%-ban halmozott oszlopdiagram", "Common.define.chartData.textBarStackedPer3d": "Háromdimenziós 100%-os halmozott oszlop", "Common.define.chartData.textCharts": "Diagramok", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textColumnSpark": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "Legörd<PERSON><PERSON><PERSON>", "Common.define.chartData.textComboAreaBar": "Halmozott terület - csoportosított oszlop", "Common.define.chartData.textComboBarLine": "Csoportosí<PERSON><PERSON>", "Common.define.chartData.textComboBarLineSecondary": "Csoportosított <PERSON> - vonal a másodlagos tengelyen", "Common.define.chartData.textComboCustom": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textDoughnut": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "Csoportosított sáv", "Common.define.chartData.textHBarNormal3d": "Csoportosított 3D sávok", "Common.define.chartData.textHBarStacked": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>v", "Common.define.chartData.textHBarStacked3d": "3D halmozott sáv", "Common.define.chartData.textHBarStackedPer": "100%-ban halmozott oszlopdiagram", "Common.define.chartData.textHBarStackedPer3d": "Háromdi<PERSON><PERSON><PERSON> s<PERSON>zalékos oszlopdiagram", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "<PERSON> vonal", "Common.define.chartData.textLineMarker": "<PERSON><PERSON>", "Common.define.chartData.textLineSpark": "<PERSON><PERSON>", "Common.define.chartData.textLineStacked": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textLineStackedMarker": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textLineStackedPer": "100%-ig ha<PERSON><PERSON><PERSON>", "Common.define.chartData.textLineStackedPerMarker": "100%-ig halm<PERSON><PERSON> j<PERSON>", "Common.define.chartData.textPie": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textPie3d": "3-D torta", "Common.define.chartData.textPoint": "<PERSON>", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Filled radar", "Common.define.chartData.textRadarMarker": "Radar with markers", "Common.define.chartData.textScatter": "Szétszór", "Common.define.chartData.textScatterLine": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON><PERSON>", "Common.define.chartData.textScatterLineMarker": "Sz<PERSON><PERSON><PERSON> egy<PERSON> és jelölőkkel", "Common.define.chartData.textScatterSmooth": "<PERSON><PERSON><PERSON><PERSON><PERSON> si<PERSON>", "Common.define.chartData.textScatterSmoothMarker": "Sz<PERSON>r<PERSON> sima <PERSON> é<PERSON> jelölőkkel", "Common.define.chartData.textSparks": "Értékgörbék", "Common.define.chartData.textStock": "Részvény", "Common.define.chartData.textSurface": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textWinLossSpark": "Nyereség/veszteség", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "<PERSON>ncs megadva formátum", "Common.define.conditionalData.text1Above": "1 standard eltéréssel nagyobb", "Common.define.conditionalData.text1Below": "1 standard eltérés az átlag alatt", "Common.define.conditionalData.text2Above": "2 standard el<PERSON><PERSON><PERSON>", "Common.define.conditionalData.text2Below": "2 standard el<PERSON><PERSON><PERSON> alatt", "Common.define.conditionalData.text3Above": "3 std eltérés ha több mint:", "Common.define.conditionalData.text3Below": "3 standard eltérés az átlag alatt", "Common.define.conditionalData.textAbove": "<PERSON><PERSON>", "Common.define.conditionalData.textAverage": "Átlag", "Common.define.conditionalData.textBegins": "Kezdődik", "Common.define.conditionalData.textBelow": "<PERSON><PERSON>", "Common.define.conditionalData.textBetween": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textBlank": "Üres", "Common.define.conditionalData.textBlanks": "Tartalmaz üres karaktereket", "Common.define.conditionalData.textBottom": "Alul", "Common.define.conditionalData.textContains": "tartalmaz", "Common.define.conditionalData.textDataBar": "<PERSON><PERSON>áv", "Common.define.conditionalData.textDate": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textDuplicate": "Kettőzés", "Common.define.conditionalData.textEnds": "Ezzel záródik:", "Common.define.conditionalData.textEqAbove": "Egyenl<PERSON> vagy nagy<PERSON>b", "Common.define.conditionalData.textEqBelow": "Egyenlő vagy k<PERSON>", "Common.define.conditionalData.textEqual": "Egyenlő", "Common.define.conditionalData.textError": "Hiba", "Common.define.conditionalData.textErrors": "Tartalmaz hibák<PERSON>", "Common.define.conditionalData.textFormula": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textGreater": "Nagyobb mint", "Common.define.conditionalData.textGreaterEq": "<PERSON><PERSON><PERSON><PERSON> vagy e<PERSON>", "Common.define.conditionalData.textIconSets": "Ikon <PERSON>k", "Common.define.conditionalData.textLast7days": "Az elmúlt 7 napban", "Common.define.conditionalData.textLastMonth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textLastWeek": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textLess": "Kevesebb mint", "Common.define.conditionalData.textLessEq": "Kisebb vagy egyenlő mint", "Common.define.conditionalData.textNextMonth": "Következő hónap", "Common.define.conditionalData.textNextWeek": "Következő hét", "Common.define.conditionalData.textNotBetween": "<PERSON><PERSON>", "Common.define.conditionalData.textNotBlanks": "Nem tartalmaz üres karaktereket", "Common.define.conditionalData.textNotContains": "<PERSON><PERSON>", "Common.define.conditionalData.textNotEqual": "<PERSON><PERSON>", "Common.define.conditionalData.textNotErrors": "<PERSON><PERSON> ta<PERSON> hib<PERSON>", "Common.define.conditionalData.textText": "Szöveg", "Common.define.conditionalData.textThisMonth": "Ez a hónap", "Common.define.conditionalData.textThisWeek": "<PERSON>z a hét", "Common.define.conditionalData.textToday": "Ma", "Common.define.conditionalData.textTomorrow": "Holnap", "Common.define.conditionalData.textTop": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textUnique": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textValue": "<PERSON><PERSON>", "Common.define.conditionalData.textYesterday": "Tegnap", "Common.define.smartArt.textAccentedPicture": "Accented Picture", "Common.define.smartArt.textAccentProcess": "Accent Process", "Common.define.smartArt.textAlternatingFlow": "Alternating flow", "Common.define.smartArt.textAlternatingHexagons": "Alternating hexagons", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternating picture blocks", "Common.define.smartArt.textAlternatingPictureCircles": "Alternating picture circles", "Common.define.smartArt.textArchitectureLayout": "Építészeti Élrendezés", "Common.define.smartArt.textArrowRibbon": "Nyilvessző", "Common.define.smartArt.textAscendingPictureAccentProcess": "Ascending picture accent process", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Basic bending process", "Common.define.smartArt.textBasicBlockList": "Basic block list", "Common.define.smartArt.textBasicChevronProcess": "Basic chevron process", "Common.define.smartArt.textBasicCycle": "Basic cycle", "Common.define.smartArt.textBasicMatrix": "Basic matrix", "Common.define.smartArt.textBasicPie": "Basic Pie", "Common.define.smartArt.textBasicProcess": "Basic process", "Common.define.smartArt.textBasicPyramid": "Basic pyramid", "Common.define.smartArt.textBasicRadial": "Basic radial", "Common.define.smartArt.textBasicTarget": "Basic target", "Common.define.smartArt.textBasicTimeline": "Egyszerű idővonal", "Common.define.smartArt.textBasicVenn": "Basic Venn", "Common.define.smartArt.textBendingPictureAccentList": "Bending picture accent list", "Common.define.smartArt.textBendingPictureBlocks": "Bending picture blocks", "Common.define.smartArt.textBendingPictureCaption": "Bending picture caption", "Common.define.smartArt.textBendingPictureCaptionList": "Bending picture caption list", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Bending picture semi-transparent text", "Common.define.smartArt.textBlockCycle": "Block cycle", "Common.define.smartArt.textBubblePictureList": "Bubble picture list", "Common.define.smartArt.textCaptionedPictures": "Captioned pictures", "Common.define.smartArt.textChevronAccentProcess": "Chevron accent process", "Common.define.smartArt.textChevronList": "Chevron list", "Common.define.smartArt.textCircleAccentTimeline": "Kiemelt tervezési sáv körökkel", "Common.define.smartArt.textCircleArrowProcess": "Circle arrow process", "Common.define.smartArt.textCirclePictureHierarchy": "Circle picture hierarchy", "Common.define.smartArt.textCircleProcess": "Circle process", "Common.define.smartArt.textCircleRelationship": "Circle relationship", "Common.define.smartArt.textCircularBendingProcess": "Circular bending process", "Common.define.smartArt.textCircularPictureCallout": "Circular picture callout", "Common.define.smartArt.textClosedChevronProcess": "Closed chevron process", "Common.define.smartArt.textContinuousArrowProcess": "Continuous arrow process", "Common.define.smartArt.textContinuousBlockProcess": "Continuous block process", "Common.define.smartArt.textContinuousCycle": "Continuous cycle", "Common.define.smartArt.textContinuousPictureList": "Continuous picture list", "Common.define.smartArt.textConvergingArrows": "Converging arrows", "Common.define.smartArt.textConvergingRadial": "Converging radial", "Common.define.smartArt.textConvergingText": "Converging text", "Common.define.smartArt.textCounterbalanceArrows": "Counterbalance arrows", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "Cycle matrix", "Common.define.smartArt.textDescendingBlockList": "Descending block list", "Common.define.smartArt.textDescendingProcess": "Descending process", "Common.define.smartArt.textDetailedProcess": "Detailed process", "Common.define.smartArt.textDivergingArrows": "Diverging arrows", "Common.define.smartArt.textDivergingRadial": "Diverging radial", "Common.define.smartArt.textEquation": "Equation", "Common.define.smartArt.textFramedTextPicture": "Framed text picture", "Common.define.smartArt.textFunnel": "Funnel", "Common.define.smartArt.textGear": "Gear", "Common.define.smartArt.textGridMatrix": "Grid matrix", "Common.define.smartArt.textGroupedList": "Grouped list", "Common.define.smartArt.textHalfCircleOrganizationChart": "Half circle organization chart", "Common.define.smartArt.textHexagonCluster": "Hexagon cluster", "Common.define.smartArt.textHexagonRadial": "Hexagon radial", "Common.define.smartArt.textHierarchy": "Hierarchy", "Common.define.smartArt.textHierarchyList": "Hierarchy list", "Common.define.smartArt.textHorizontalBulletList": "Horizontal bullet list", "Common.define.smartArt.textHorizontalHierarchy": "Horizontal hierarchy", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal labeled hierarchy", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontal multi-level hierarchy", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontal organization chart", "Common.define.smartArt.textHorizontalPictureList": "Horizontal picture list", "Common.define.smartArt.textIncreasingArrowProcess": "Increasing arrow process", "Common.define.smartArt.textIncreasingCircleProcess": "Increasing circle process", "Common.define.smartArt.textInterconnectedBlockProcess": "Interconnected block process", "Common.define.smartArt.textInterconnectedRings": "Interconnected rings", "Common.define.smartArt.textInvertedPyramid": "Inverted pyramid", "Common.define.smartArt.textLabeledHierarchy": "Labeled hierarchy", "Common.define.smartArt.textLinearVenn": "Linear Venn", "Common.define.smartArt.textLinedList": "Lined list", "Common.define.smartArt.textList": "List", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Multidirectional cycle", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Name and title organization chart", "Common.define.smartArt.textNestedTarget": "Nested target", "Common.define.smartArt.textNondirectionalCycle": "Nondirectional cycle", "Common.define.smartArt.textOpposingArrows": "Opposing arrows", "Common.define.smartArt.textOpposingIdeas": "Opposing ideas", "Common.define.smartArt.textOrganizationChart": "Organization chart", "Common.define.smartArt.textOther": "Other", "Common.define.smartArt.textPhasedProcess": "Phased process", "Common.define.smartArt.textPicture": "Picture", "Common.define.smartArt.textPictureAccentBlocks": "Picture accent blocks", "Common.define.smartArt.textPictureAccentList": "Picture accent list", "Common.define.smartArt.textPictureAccentProcess": "Picture accent process", "Common.define.smartArt.textPictureCaptionList": "Picture caption list", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "Picture grid", "Common.define.smartArt.textPictureLineup": "Picture lineup", "Common.define.smartArt.textPictureOrganizationChart": "Picture organization chart", "Common.define.smartArt.textPictureStrips": "Picture strips", "Common.define.smartArt.textPieProcess": "Pie process", "Common.define.smartArt.textPlusAndMinus": "Plus and minus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "Process arrows", "Common.define.smartArt.textProcessList": "Process list", "Common.define.smartArt.textPyramid": "Pyramid", "Common.define.smartArt.textPyramidList": "Pyramid list", "Common.define.smartArt.textRadialCluster": "Radial cluster", "Common.define.smartArt.textRadialCycle": "Radial cycle", "Common.define.smartArt.textRadialList": "Radial list", "Common.define.smartArt.textRadialPictureList": "Radial picture list", "Common.define.smartArt.textRadialVenn": "Radial Venn", "Common.define.smartArt.textRandomToResultProcess": "Random to result process", "Common.define.smartArt.textRelationship": "Relationship", "Common.define.smartArt.textRepeatingBendingProcess": "Repeating bending process", "Common.define.smartArt.textReverseList": "Reverse list", "Common.define.smartArt.textSegmentedCycle": "Segmented cycle", "Common.define.smartArt.textSegmentedProcess": "Segmented process", "Common.define.smartArt.textSegmentedPyramid": "Segmented pyramid", "Common.define.smartArt.textSnapshotPictureList": "Snapshot picture list", "Common.define.smartArt.textSpiralPicture": "Spiral picture", "Common.define.smartArt.textSquareAccentList": "Square accent list", "Common.define.smartArt.textStackedList": "Stacked list", "Common.define.smartArt.textStackedVenn": "Stacked <PERSON>n", "Common.define.smartArt.textStaggeredProcess": "Staggered process", "Common.define.smartArt.textStepDownProcess": "Step down process", "Common.define.smartArt.textStepUpProcess": "Step up process", "Common.define.smartArt.textSubStepProcess": "Sub-step process", "Common.define.smartArt.textTabbedArc": "Tabbed arc", "Common.define.smartArt.textTableHierarchy": "Table hierarchy", "Common.define.smartArt.textTableList": "Table list", "Common.define.smartArt.textTabList": "Tab List", "Common.define.smartArt.textTargetList": "Target list", "Common.define.smartArt.textTextCycle": "Text cycle", "Common.define.smartArt.textThemePictureAccent": "Theme picture accent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Theme picture alternating accent", "Common.define.smartArt.textThemePictureGrid": "Theme picture grid", "Common.define.smartArt.textTitledMatrix": "Titled matrix", "Common.define.smartArt.textTitledPictureAccentList": "Titled picture accent list", "Common.define.smartArt.textTitledPictureBlocks": "Titled picture blocks", "Common.define.smartArt.textTitlePictureLineup": "Title picture lineup", "Common.define.smartArt.textTrapezoidList": "Trapezoid list", "Common.define.smartArt.textUpwardArrow": "Upward arrow", "Common.define.smartArt.textVaryingWidthList": "Varying width list", "Common.define.smartArt.textVerticalAccentList": "Vertical accent list", "Common.define.smartArt.textVerticalArrowList": "Vertical arrow list", "Common.define.smartArt.textVerticalBendingProcess": "Vertical bending process", "Common.define.smartArt.textVerticalBlockList": "Vertical block list", "Common.define.smartArt.textVerticalBoxList": "Vertical box list", "Common.define.smartArt.textVerticalBracketList": "Vertical bracket list", "Common.define.smartArt.textVerticalBulletList": "Vertical bullet list", "Common.define.smartArt.textVerticalChevronList": "Vertical chevron list", "Common.define.smartArt.textVerticalCircleList": "Vertical circle list", "Common.define.smartArt.textVerticalCurvedList": "Vertical curved list", "Common.define.smartArt.textVerticalEquation": "Vertical equation", "Common.define.smartArt.textVerticalPictureAccentList": "Vertical picture accent list", "Common.define.smartArt.textVerticalPictureList": "Vertical picture list", "Common.define.smartArt.textVerticalProcess": "Vertical process", "Common.Translation.textMoreButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Translation.tipFileLocked": "Document is locked for editing. You can make changes and save it as local copy later.", "Common.Translation.tipFileReadOnly": "The file is read-only. To keep your changes, save the file with a new name or in a different location.", "Common.Translation.warnFileLocked": "A fájlt egy másik alkalmazásban szerkesztik. Folytathatja a szerkesztést és elmentheti másolatként.", "Common.Translation.warnFileLockedBtnEdit": "Másolat létrehozása", "Common.Translation.warnFileLockedBtnView": "Megnyitás megtekintéshez", "Common.UI.ButtonColored.textAutoColor": "Automatikus", "Common.UI.ButtonColored.textEyedropper": "Eyedropper", "Common.UI.ButtonColored.textNewColor": "Új egyéni sz<PERSON> hozz<PERSON>adása", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON><PERSON>ek stílusok", "Common.UI.ExtendedColorDialog.addButtonText": "Hozzáad", "Common.UI.ExtendedColorDialog.textCurrent": "a<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textHexErr": "A megadott érték helytelen.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy értéket 000000 és FFFFFF között", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "A megadott érték helytelen.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy számértéket 0 és 255 között.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON><PERSON>", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "<PERSON><PERSON><PERSON><PERSON> me<PERSON>", "Common.UI.SearchBar.textFind": "<PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "Ke<PERSON><PERSON>", "Common.UI.SearchBar.tipNextResult": "Következő eredmény", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Speciális beállítások megnyitása", "Common.UI.SearchBar.tipPreviousResult": "Előző eredmény", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>e", "Common.UI.SearchDialog.textMatchCase": "Kis-nagybetű érzékeny", "Common.UI.SearchDialog.textReplaceDef": "<PERSON><PERSON><PERSON> be a helyettesítő szöveget", "Common.UI.SearchDialog.textSearchStart": "<PERSON><PERSON><PERSON> ide a szöveget", "Common.UI.SearchDialog.textTitle": "<PERSON><PERSON> és cserél", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "Csak teljes s<PERSON>k", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON><PERSON>", "Common.UI.SynchronizeTip.textDontShow": "Ne mutassa újra ezt az üzenetet", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "A dokumentumot egy másik felhasználó módosította.<br><PERSON><PERSON><PERSON><PERSON> a gombra a módosítások mentéséhez és a frissítések újratöltéséhez.", "Common.UI.ThemeColorPalette.textRecentColors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Klasszikus Világítás", "Common.UI.Themes.txtThemeContrastDark": "Sötét kontraszt", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "<PERSON>il<PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "A rendszerrel megegyező", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Nem", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.textDontShow": "Ne mutassa újra ezt az üzenetet", "Common.UI.Window.textError": "Hiba", "Common.UI.Window.textInformation": "Információ", "Common.UI.Window.textWarning": "Figyelmeztetés", "Common.UI.Window.yesButtonText": "Igen", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Váltás", "Common.Utils.ThemeColor.txtaccent": "Accent", "Common.Utils.ThemeColor.txtAqua": "Víz", "Common.Utils.ThemeColor.txtbackground": "Background", "Common.Utils.ThemeColor.txtBlack": "Black", "Common.Utils.ThemeColor.txtBlue": "Blue", "Common.Utils.ThemeColor.txtBrightGreen": "Bright green", "Common.Utils.ThemeColor.txtBrown": "<PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Sötétkék", "Common.Utils.ThemeColor.txtDarker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkGray": "Sötétszürke", "Common.Utils.ThemeColor.txtDarkGreen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkPurple": "Sötétlila", "Common.Utils.ThemeColor.txtDarkRed": "Sötétvörös", "Common.Utils.ThemeColor.txtDarkTeal": "Sötét kékeszöld", "Common.Utils.ThemeColor.txtDarkYellow": "<PERSON><PERSON><PERSON><PERSON>á<PERSON>", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "<PERSON>", "Common.Utils.ThemeColor.txtGreen": "Green", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "Világoskék", "Common.Utils.ThemeColor.txtLighter": "Világosabb", "Common.Utils.ThemeColor.txtLightGray": "Világosszürke", "Common.Utils.ThemeColor.txtLightGreen": "Világoszöld", "Common.Utils.ThemeColor.txtLightOrange": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtLightYellow": "Világossárga", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "Pink", "Common.Utils.ThemeColor.txtPurple": "Purple", "Common.Utils.ThemeColor.txtRed": "Red", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Sky blue", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Text", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "White", "Common.Utils.ThemeColor.txtYellow": "Yellow", "Common.Views.About.txtAddress": "cím:", "Common.Views.About.txtLicensee": "LICENC VÁSÁRLÓ", "Common.Views.About.txtLicensor": "LICENCTULAJDONOS", "Common.Views.About.txtMail": "e-mail:", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "Hozzáadás", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Munka közben alkalmazza", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Automatikus <PERSON>", "Common.Views.AutoCorrectDialog.textAutoFormat": "Automatikus formázás gépelés közben", "Common.Views.AutoCorrectDialog.textBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textDelete": "Törlés", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet és hálózati utak hiperhivatkozásokkal", "Common.Views.AutoCorrectDialog.textMathCorrect": "Matematikai automatikus javí<PERSON>ás", "Common.Views.AutoCorrectDialog.textNewRowCol": "Új sorok és oszlopok felvétele a táblázatba", "Common.Views.AutoCorrectDialog.textRecognized": "Felismert függvények", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "A következő kifejezések felismert matematikai kifejezések. Nem lesznek automatikusan dőlt betűkkel.", "Common.Views.AutoCorrectDialog.textReplace": "Cser<PERSON><PERSON><PERSON> ki", "Common.Views.AutoCorrectDialog.textReplaceText": "Cserélje gépelés közben", "Common.Views.AutoCorrectDialog.textReplaceType": "Szöveg cseréje gépelés közben", "Common.Views.AutoCorrectDialog.textReset": "Visszaállítás", "Common.Views.AutoCorrectDialog.textResetAll": "Alapbeállítások visszaállítása", "Common.Views.AutoCorrectDialog.textRestore": "Visszaállítás", "Common.Views.AutoCorrectDialog.textTitle": "Automatikus <PERSON>", "Common.Views.AutoCorrectDialog.textWarnAddRec": "A felismert függvények kizárólag az A–Z betűket tartalmazhatják kis- vagy nagybetűvel.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "A hozzáadott kifejezések eltávolításra kerülnek, és az eredetiek visszaállnak. Szeretné folytatni?", "Common.Views.AutoCorrectDialog.warnReplace": "%1 automatikus javítása már lé<PERSON>. Kicseréljük?", "Common.Views.AutoCorrectDialog.warnReset": "<PERSON>z Ön <PERSON>l hozzáadott automatikus helyesbítések eltávolításra kerülnek, a megváltoztatottak pedig visszaállítják az eredetire. Szeretné folytatni?", "Common.Views.AutoCorrectDialog.warnRestore": "%1 automatikus javítása visszaáll az eredeti értékére. Folytassuk?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "Szerző (A-Z)", "Common.Views.Comments.mniAuthorDesc": "Szerző (Z-A)", "Common.Views.Comments.mniDateAsc": "Legöregebb először", "Common.Views.Comments.mniDateDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniFilterGroups": "Szűrés csoport szerint", "Common.Views.Comments.mniPositionAsc": "Fel<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniPositionDesc": "Alulról", "Common.Views.Comments.textAdd": "Hozzáad", "Common.Views.Comments.textAddComment": "Megjegyzés hozzáadása", "Common.Views.Comments.textAddCommentToDoc": "Megjegyzés hozzáadása a dokumentumhoz", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON>z <PERSON>", "Common.Views.Comments.textAll": "Minden", "Common.Views.Comments.textAnonym": "Vendég", "Common.Views.Comments.textCancel": "M<PERSON>gs<PERSON>", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "Megjegyzések bezárása", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "Megjegyzések", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Megjegyzés beírása itt", "Common.Views.Comments.textHintAddComment": "Megjegyzés hozzáadása", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textReply": "Ismétel", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "Megjegyzések rendezése", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "<PERSON><PERSON><PERSON> engedé<PERSON>e a megjegyzés újranyitásához", "Common.Views.Comments.txtEmpty": "A lapon nincsenek megjegyzések.", "Common.Views.CopyWarningDialog.textDontShow": "Ne mutassa újra ezt az üzenetet", "Common.Views.CopyWarningDialog.textMsg": "A szerkesztés eszköztár gombjaival és a helyi menüvel végzett másolás, kivágás és beillesztés műveletek csak a szerkesztő oldalon használhatóak.<br><br>A szerkesztő lapon kívüli alkalmazásokba való másoláshoz vagy beillesztéshez az alábbi billentyűzetkombinációkat használja:", "Common.Views.CopyWarningDialog.textTitle": "Másolás, kivágás és beillesztés", "Common.Views.CopyWarningDialog.textToCopy": "Másolásra", "Common.Views.CopyWarningDialog.textToCut": "Kivágásra", "Common.Views.CopyWarningDialog.textToPaste": "Beillesztésre", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "Betöltés...", "Common.Views.DocumentAccessDialog.textTitle": "Megosztási beállítások", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "Eraser", "Common.Views.Draw.hintSelect": "Select", "Common.Views.Draw.txtEraser": "Eraser", "Common.Views.Draw.txtHighlighter": "Highlighter", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Pen", "Common.Views.Draw.txtSelect": "Select", "Common.Views.Draw.txtSize": "Size", "Common.Views.EditNameDialog.textLabel": "Címke:", "Common.Views.EditNameDialog.textLabelError": "A címke nem lehet üres.", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "A fájlt szerkesztő felhasználók:", "Common.Views.Header.textAddFavorite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textAdvSettings": "<PERSON><PERSON><PERSON>", "Common.Views.Header.textBack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "Esz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideLines": "Vonalzók <PERSON>", "Common.Views.Header.textHideStatusBar": "A munkalap és az állapotsorok kombinálása", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "Read only", "Common.Views.Header.textRemoveFavorite": "Eltávolítás a kedvencekből", "Common.Views.Header.textSaveBegin": "Mentés...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "Minden módosítás elmentve", "Common.Views.Header.textSaveExpander": "Minden módosítás elmentve", "Common.Views.Header.textShare": "Megosztás", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "Dokumentum hozzáférési jogok kezelése", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "Fájl letöltése", "Common.Views.Header.tipGoEdit": "Az aktuális fájl szerkesztése", "Common.Views.Header.tipPrint": "Fájl nyomtatása", "Common.Views.Header.tipPrintQuick": "Quick print", "Common.Views.Header.tipRedo": "Újra", "Common.Views.Header.tipSave": "Men<PERSON>s", "Common.Views.Header.tipSearch": "Keresés", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Leválasztás külön ablakba", "Common.Views.Header.tipUsers": "Felhasználók megjelenítése", "Common.Views.Header.tipViewSettings": "Beállítások mutatása", "Common.Views.Header.tipViewUsers": "A felhasználók megtekintése és a dokumentumokhoz való hozzáférési jogok kezelése", "Common.Views.Header.txtAccessRights": "Hozzáférési jogok módosítása", "Common.Views.Header.txtRename": "Név változtatása", "Common.Views.History.textCloseHistory": "Előzmények bezárása", "Common.Views.History.textHideAll": "Részletes módosítások elrejtése", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "Visszaállítás", "Common.Views.History.textShowAll": "Módosítások részletes megjelenítése", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "Illesszen be egy kép <PERSON>:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Ez egy szükséges mező", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Ennek a mezőnek hivatkozásnak kell lennie a \"http://www.example.com\" formátumban", "Common.Views.ListSettingsDialog.textBulleted": "Felsorolásos", "Common.Views.ListSettingsDialog.textFromFile": "Fájlból", "Common.Views.ListSettingsDialog.textFromStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.textFromUrl": "URL-ből", "Common.Views.ListSettingsDialog.textNumbering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.tipChange": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "Szín", "Common.Views.ListSettingsDialog.txtImage": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImport": "Importálás", "Common.Views.ListSettingsDialog.txtNewBullet": "<PERSON>j pont", "Common.Views.ListSettingsDialog.txtNewImage": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON> sem", "Common.Views.ListSettingsDialog.txtOfText": "%-a a szövegnek", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "Kezdet", "Common.Views.ListSettingsDialog.txtSymbol": "Szimbólum", "Common.Views.ListSettingsDialog.txtTitle": "Lista beállí<PERSON>ok", "Common.Views.ListSettingsDialog.txtType": "<PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.textInvalidRange": "Érvénytelen cellatartomány", "Common.Views.OpenDialog.textSelectData": "Adatok kiválasztása", "Common.Views.OpenDialog.txtAdvanced": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtColon": "Kettőspont", "Common.Views.OpenDialog.txtComma": "Vess<PERSON><PERSON>", "Common.Views.OpenDialog.txtDelimiter": "Elválasztó", "Common.Views.OpenDialog.txtDestData": "Válassza ki, hová helyezze az adatokat", "Common.Views.OpenDialog.txtEmpty": "Ez egy szükséges mező", "Common.Views.OpenDialog.txtEncoding": "K<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON> j<PERSON>.", "Common.Views.OpenDialog.txtOpenFile": "Írja be a megnyitáshoz szükséges jelszót", "Common.Views.OpenDialog.txtOther": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtPassword": "Je<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "Előnézet", "Common.Views.OpenDialog.txtProtected": "<PERSON><PERSON><PERSON> mega<PERSON>a a jelszót és megnyitotta a fájlt, annak jelenlegi j<PERSON>zava v<PERSON>zaállítódik.", "Common.Views.OpenDialog.txtSemicolon": "Pontosvessző", "Common.Views.OpenDialog.txtSpace": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtTab": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtTitle": "%1 opció választása", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> be j<PERSON>zót a dokumentum védelmére", "Common.Views.PasswordDialog.txtIncorrectPwd": "A jelszavak nem azonosak", "Common.Views.PasswordDialog.txtPassword": "Je<PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtWarning": "Figyelem: ha <PERSON><PERSON><PERSON> vagy el<PERSON>lej<PERSON> a j<PERSON>, annak v<PERSON>llítására nincs mód. Tárolja biztonsá<PERSON> he<PERSON>.", "Common.Views.PluginDlg.textLoading": "Betöltés", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "Kiegészítők", "Common.Views.Plugins.strPlugins": "Kiegészítők", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "Kezdés", "Common.Views.Plugins.textStop": "Stop", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "Common.Views.Protection.hintDelPwd": "Delete password", "Common.Views.Protection.hintPwd": "Jelszó módosítása vagy törlése", "Common.Views.Protection.hintSignature": "Digit<PERSON><PERSON> aláí<PERSON> vagy aláírási sor hozzáadása", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "Jelszó módosítása", "Common.Views.Protection.txtDeletePwd": "Jelszó törlése", "Common.Views.Protection.txtEncrypt": "Titkosít", "Common.Views.Protection.txtInvisibleSignature": "<PERSON><PERSON><PERSON><PERSON> ho<PERSON>", "Common.Views.Protection.txtSignature": "Aláírás", "Common.Views.Protection.txtSignatureLine": "Aláírás sor hozzáadása", "Common.Views.RecentFiles.txtOpenRecent": "Open Recent", "Common.Views.RenameDialog.textName": "Fájl név", "Common.Views.RenameDialog.txtInvalidName": "A fájlnév nem tartalmazhatja a következő karaktereket:", "Common.Views.ReviewChanges.hintNext": "A következő változáshoz", "Common.Views.ReviewChanges.hintPrev": "A korábbi változáshoz", "Common.Views.ReviewChanges.strFast": "Gyors", "Common.Views.ReviewChanges.strFastDesc": "Valós idejű együttes szerkesztés. Minden módosítás automatikusan mentésre kerül.", "Common.Views.ReviewChanges.strStrict": "Biztonságos", "Common.Views.ReviewChanges.strStrictDesc": "A „Mentés” gomb seg<PERSON><PERSON><PERSON><PERSON><PERSON> szinkronizálhatja az Ön és mások által végrehajtott módosításokat.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Változás elfogadása", "Common.Views.ReviewChanges.tipCoAuthMode": "Együttes szerkesztés beállítása", "Common.Views.ReviewChanges.tipCommentRem": "Megjegyzések eltávolítása", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Jelenlegi megjegyzések eltávolítása", "Common.Views.ReviewChanges.tipCommentResolve": "Megjegyzések <PERSON>", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Aktuális megjegyzések megoldása", "Common.Views.ReviewChanges.tipHistory": "Verziótörténet mutatása", "Common.Views.ReviewChanges.tipRejectCurrent": "Az aktuális változás elutasítása", "Common.Views.ReviewChanges.tipReview": "Módosítások követése", "Common.Views.ReviewChanges.tipReviewView": "Válassza ki a módot, ahogyan a módosítások megjelenjenek", "Common.Views.ReviewChanges.tipSetDocLang": "Dokumentumnyelv beállítása", "Common.Views.ReviewChanges.tipSetSpelling": "Helyesírás-ellenőrzés", "Common.Views.ReviewChanges.tipSharing": "Dokumentum hozzáférési jogok kezelése", "Common.Views.ReviewChanges.txtAccept": "Elfogad", "Common.Views.ReviewChanges.txtAcceptAll": "Minden módosítás elfogadása", "Common.Views.ReviewChanges.txtAcceptChanges": "Módosítások elfogadása", "Common.Views.ReviewChanges.txtAcceptCurrent": "Változás elfogadása", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Együttes szerkesztési mód", "Common.Views.ReviewChanges.txtCommentRemAll": "Minden megjegyzés eltávolítása", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Jelenlegi megjegyzések eltávolítása", "Common.Views.ReviewChanges.txtCommentRemMy": "Megjegyzéseim eltávolítása", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Jelenlegi megjegyzéseim eltávolítása", "Common.Views.ReviewChanges.txtCommentRemove": "Eltávolítás", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "Összes megjegyzés megoldása", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "<PERSON>ktu<PERSON><PERSON>ek <PERSON>", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Aktuális <PERSON>éseim Megoldása", "Common.Views.ReviewChanges.txtDocLang": "Nyelv", "Common.Views.ReviewChanges.txtFinal": "Minden módosítás elfogadva (Előnézet)", "Common.Views.ReviewChanges.txtFinalCap": "Végső", "Common.Views.ReviewChanges.txtHistory": "Verziótörténet", "Common.Views.ReviewChanges.txtMarkup": "Minden módosítás (szerkesztés)", "Common.Views.ReviewChanges.txtMarkupCap": "Haszonkulcs", "Common.Views.ReviewChanges.txtNext": "Következő", "Common.Views.ReviewChanges.txtOriginal": "Minden módosítás visszautasítva (Előnézet)", "Common.Views.ReviewChanges.txtOriginalCap": "Eredeti", "Common.Views.ReviewChanges.txtPrev": "Előző", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Minden módosítást elvetése", "Common.Views.ReviewChanges.txtRejectChanges": "Elutasítja a módosításokat", "Common.Views.ReviewChanges.txtRejectCurrent": "Az aktuális változás elutasítása", "Common.Views.ReviewChanges.txtSharing": "Megosztás", "Common.Views.ReviewChanges.txtSpelling": "Helyesírás-ellenőrzés", "Common.Views.ReviewChanges.txtTurnon": "Módosítások követése", "Common.Views.ReviewChanges.txtView": "Megjelení<PERSON><PERSON> mó<PERSON>", "Common.Views.ReviewPopover.textAdd": "Hozzáad", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON>z <PERSON>", "Common.Views.ReviewPopover.textCancel": "M<PERSON>gs<PERSON>", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Enter your comment here", "Common.Views.ReviewPopover.textMention": "+megemlítés hozzáférést ad a dokumentumhoz, és e-mailt küld", "Common.Views.ReviewPopover.textMentionNotify": "+megemlítés értesíti a felhasználót e-mailben", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textReply": "Ismétel", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "<PERSON><PERSON><PERSON> engedé<PERSON>e a megjegyzés újranyitásához", "Common.Views.ReviewPopover.txtDeleteTip": "Törlés", "Common.Views.ReviewPopover.txtEditTip": "Szerkesztés", "Common.Views.SaveAsDlg.textLoading": "Betöltés", "Common.Views.SaveAsDlg.textTitle": "<PERSON><PERSON><PERSON> mappa", "Common.Views.SearchPanel.textByColumns": "Oszlopok szerint", "Common.Views.SearchPanel.textByRows": "<PERSON><PERSON> szer<PERSON>", "Common.Views.SearchPanel.textCaseSensitive": "Nagy- és kisbetű érzékeny", "Common.Views.SearchPanel.textCell": "Cella", "Common.Views.SearchPanel.textCloseSearch": "Ke<PERSON><PERSON>", "Common.Views.SearchPanel.textContentChanged": "Megváltozott a dokumentum.", "Common.Views.SearchPanel.textFind": "Keresés", "Common.Views.SearchPanel.textFindAndReplace": "Keresés és csere", "Common.Views.SearchPanel.textFormula": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFormulas": "Függvények", "Common.Views.SearchPanel.textItemEntireCell": "Teljes cellatartalom", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} elem si<PERSON><PERSON>n k<PERSON>.", "Common.Views.SearchPanel.textLookIn": "Betekintés", "Common.Views.SearchPanel.textMatchUsingRegExp": "Szabályos kifejezésekkel való egyezés", "Common.Views.SearchPanel.textName": "Név", "Common.Views.SearchPanel.textNoMatches": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textNoSearchResults": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} items replaced. Remaining {2} items are locked by other users.", "Common.Views.SearchPanel.textReplace": "Csere", "Common.Views.SearchPanel.textReplaceAll": "<PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceWith": "Cserélje a következővel", "Common.Views.SearchPanel.textSearch": "Keresés", "Common.Views.SearchPanel.textSearchAgain": "{0}<PERSON><PERSON> indítás<PERSON>{1} a pontosabb eredményekért.", "Common.Views.SearchPanel.textSearchHasStopped": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSearchOptions": "Keresési opciók", "Common.Views.SearchPanel.textSearchResults": "Keresés eredményei:", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textSelectDataRange": "Adattartomány kiválasztása", "Common.Views.SearchPanel.textSheet": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSpecificRange": "Különleges tartomány", "Common.Views.SearchPanel.textTooManyResults": "<PERSON><PERSON> sok ered<PERSON>ny van, hogy bemutassuk őket", "Common.Views.SearchPanel.textValue": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textValues": "<PERSON>rté<PERSON><PERSON>", "Common.Views.SearchPanel.textWholeWords": "Csak egész s<PERSON>k", "Common.Views.SearchPanel.textWithin": "Belül", "Common.Views.SearchPanel.textWorkbook": "Munkafüzet", "Common.Views.SearchPanel.tipNextResult": "Következő eredmény", "Common.Views.SearchPanel.tipPreviousResult": "Előző eredmény", "Common.Views.SelectFileDlg.textLoading": "Betöltés", "Common.Views.SelectFileDlg.textTitle": "Adatforrás kiválasztása", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "F<PERSON>lkövér", "Common.Views.SignDialog.textCertificate": "Tan<PERSON>ít<PERSON>", "Common.Views.SignDialog.textChange": "Módosítás", "Common.Views.SignDialog.textInputName": "Aláí<PERSON><PERSON> nev<PERSON>ek megadás<PERSON>", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "<PERSON>z aláíró neve nem lehet üres.", "Common.Views.SignDialog.textPurpose": "A dokumentum aláírásának célja", "Common.Views.SignDialog.textSelect": "Kiválaszt", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON>ztása", "Common.Views.SignDialog.textSignature": "Aláírás kinézet mint", "Common.Views.SignDialog.textTitle": "Dokumentum aláírása", "Common.Views.SignDialog.textUseImage": "vagy ka<PERSON> a „Kép kiválasztása” gombra a kép aláírásként használatához", "Common.Views.SignDialog.textValid": "Érvényes %1 és %2 között", "Common.Views.SignDialog.tipFontName": "Betűtípus neve", "Common.Views.SignDialog.tipFontSize": "Bet<PERSON><PERSON><PERSON>", "Common.Views.SignSettingsDialog.textAllowComment": "Engedélyezi az aláírónak megjegyzés hozzáadását az aláírási párbeszédablakban", "Common.Views.SignSettingsDialog.textDefInstruction": "Before signing this document, verify that the content you are signing is correct.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail", "Common.Views.SignSettingsDialog.textInfoName": "Név", "Common.Views.SignSettingsDialog.textInfoTitle": "Aláíró titulus", "Common.Views.SignSettingsDialog.textInstructions": "Utasítások az aláírónak", "Common.Views.SignSettingsDialog.textShowDate": "Aláírási sorban az aláírás dátumának mutatása", "Common.Views.SignSettingsDialog.textTitle": "Aláí<PERSON><PERSON>", "Common.Views.SignSettingsDialog.txtEmpty": "Ez egy szükséges mező", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Hexadecimális unikód <PERSON>", "Common.Views.SymbolTableDialog.textCopyright": "Szerzői jogi aláí<PERSON>ás", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON><PERSON><PERSON> du<PERSON>", "Common.Views.SymbolTableDialog.textDOQuote": "Nyitó dupla idéző<PERSON>l", "Common.Views.SymbolTableDialog.textEllipsis": "Vízszintes ellipszis", "Common.Views.SymbolTableDialog.textEmDash": "em gondolatjel", "Common.Views.SymbolTableDialog.textEmSpace": "em köz", "Common.Views.SymbolTableDialog.textEnDash": "em gondolatjel", "Common.Views.SymbolTableDialog.textEnSpace": "em köz", "Common.Views.SymbolTableDialog.textFont": "Betűtípus", "Common.Views.SymbolTableDialog.textNBHyphen": "<PERSON><PERSON> k<PERSON>", "Common.Views.SymbolTableDialog.textNBSpace": "<PERSON>em tö<PERSON> szóköz", "Common.Views.SymbolTableDialog.textPilcrow": "Bekezdésjel", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Hosszú térköz", "Common.Views.SymbolTableDialog.textRange": "Tartomány", "Common.Views.SymbolTableDialog.textRecent": "Legutóbb használt szimbólumok", "Common.Views.SymbolTableDialog.textRegistered": "Bejegy<PERSON><PERSON> jel", "Common.Views.SymbolTableDialog.textSCQuote": "Z<PERSON><PERSON>ó egyszeres idézőjel", "Common.Views.SymbolTableDialog.textSection": "Szakaszjel", "Common.Views.SymbolTableDialog.textShortcut": "Gyorsbillentyű", "Common.Views.SymbolTableDialog.textSHyphen": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSOQuote": "Nyitó egyszeres idézőjel", "Common.Views.SymbolTableDialog.textSpecial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSymbols": "Szimbólumok", "Common.Views.SymbolTableDialog.textTitle": "Szimbólum", "Common.Views.SymbolTableDialog.textTradeMark": "Védjegy szimbólum", "Common.Views.UserNameDialog.textDontShow": "Ne kérdezze újra", "Common.Views.UserNameDialog.textLabel": "Címke:", "Common.Views.UserNameDialog.textLabelError": "A címke nem lehet üres.", "SSE.Controllers.DataTab.strSheet": "Sheet", "SSE.Controllers.DataTab.textAddExternalData": "The link to an external source has been added. You can update such links in the Data tab.", "SSE.Controllers.DataTab.textColumns": "Oszlopok", "SSE.Controllers.DataTab.textContinue": "Continue", "SSE.Controllers.DataTab.textDontUpdate": "Don't Update", "SSE.Controllers.DataTab.textEmptyUrl": "<PERSON> kell adni az URL-t.", "SSE.Controllers.DataTab.textRows": "So<PERSON>", "SSE.Controllers.DataTab.textTurnOff": "Turn off AutoUpdate", "SSE.Controllers.DataTab.textUpdate": "Update", "SSE.Controllers.DataTab.textWizard": "Szöveg oszlopokká", "SSE.Controllers.DataTab.txtDataValidation": "Adatellenőrzés", "SSE.Controllers.DataTab.txtErrorExternalLink": "Error: updating is failed", "SSE.Controllers.DataTab.txtExpand": "Kibont", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "A kiválasztás melletti adatokat nem távolítjuk el. Kibővíti a kijelölést a szomszédos adatokra, vagy csak az éppen kijelölt cellákkal folytatja?", "SSE.Controllers.DataTab.txtExtendDataValidation": "A kiválasztás tartalmaz n<PERSON>, am<PERSON><PERSON> nem tartalmazzák az adatellenőrzés beállításait.<br>Kiterjesszük az adatellenőrzést ezekre a cellákra is?", "SSE.Controllers.DataTab.txtImportWizard": "Szöveg importá<PERSON>ás <PERSON>ló", "SSE.Controllers.DataTab.txtRemDuplicates": "Ismétlődések eltávolítása", "SSE.Controllers.DataTab.txtRemoveDataValidation": "A kiválasztás többféle ellenőrzést tartalmaz.<br>Töröljük az aktuális beállításokat és folytassuk?", "SSE.Controllers.DataTab.txtRemSelected": "Eltávolítás a kiválasztottból", "SSE.Controllers.DataTab.txtUrlTitle": "Illesszen be egy adat URL-t", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "This workbook contains links to external sources which update automatically. This might be unsafe.<br><br>If you trust them, press Continue.", "SSE.Controllers.DataTab.warnUpdateExternalData": "This workbook contains links to one or more external sources that could be unsafe.<br>If you trust the links, update them to get the latest data.", "SSE.Controllers.DocumentHolder.alignmentText": "Elrendezés", "SSE.Controllers.DocumentHolder.centerText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.deleteColumnText": "Oszlop törlése", "SSE.Controllers.DocumentHolder.deleteRowText": "<PERSON><PERSON> t<PERSON>", "SSE.Controllers.DocumentHolder.deleteText": "T<PERSON>r<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.errorInvalidLink": "A hivatkozás nem létezik. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jav<PERSON>tsa ki a hivat<PERSON><PERSON>, vagy tö<PERSON><PERSON><PERSON> a<PERSON>t.", "SSE.Controllers.DocumentHolder.guestText": "Vendég", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertColumnRightText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON>r", "SSE.Controllers.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.leftText": "<PERSON>l", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Figyelmeztetés", "SSE.Controllers.DocumentHolder.rightText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Automatikus jav<PERSON>", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Oszlopszélesség {0} szimbólum ({1} képpont)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Sor magass<PERSON>g {0} pont ({1} pixel)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Kat<PERSON>tson a hivatkozásra annak megny<PERSON>z, vag<PERSON> katti<PERSON> és tartsa lenyomva az egérgombot a cella kiválasztásához.", "SSE.Controllers.DocumentHolder.textInsertLeft": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textInsertTop": "Besz<PERSON><PERSON><PERSON> fentre", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Spec<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textStopExpand": "Állítsa le a táblák automatikus bővítését", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Ezt az elemet egy másik felhasználó szerkeszti.", "SSE.Controllers.DocumentHolder.txtAboveAve": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON> ho<PERSON>", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Törtek összeadása", "SSE.Controllers.DocumentHolder.txtAddHor": "Vízszintes vonal hozzáadása", "SSE.Controllers.DocumentHolder.txtAddLB": "<PERSON><PERSON> <PERSON><PERSON><PERSON> ho<PERSON>", "SSE.Controllers.DocumentHolder.txtAddLeft": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddLT": "<PERSON><PERSON> <PERSON><PERSON><PERSON> ho<PERSON>", "SSE.Controllers.DocumentHolder.txtAddRight": "<PERSON><PERSON> <PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddTop": "Fels<PERSON> szegély ho<PERSON>a", "SSE.Controllers.DocumentHolder.txtAddVer": "Függőleg<PERSON> vonal hozz<PERSON>adása", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Karakterhez igazít", "SSE.Controllers.DocumentHolder.txtAll": "(Mind)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "A táblázat teljes tartalmát vagy a megadott táblázat oszlopait adja vissza, beleértve az oszlopfejléceket, az adatokat és az összes sort", "SSE.Controllers.DocumentHolder.txtAnd": "és", "SSE.Controllers.DocumentHolder.txtBegins": "Kezdődik", "SSE.Controllers.DocumentHolder.txtBelowAve": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtBlanks": "(Üresek)", "SSE.Controllers.DocumentHolder.txtBorderProps": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtBottom": "Alsó", "SSE.Controllers.DocumentHolder.txtByField": "%1 of %2", "SSE.Controllers.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Oszlop <PERSON>dez<PERSON>", "SSE.Controllers.DocumentHolder.txtContains": "tartalmaz", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Link másolása a vágólapra", "SSE.Controllers.DocumentHolder.txtDataTableHint": "A táblázat adatcelláit vagy a megadott táblázatoszlopokat adja vissza", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Az argumentum csökkentése", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Argumentum törlése", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Oldaltörés törlése", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Mellékelt karakterek törlése", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Mellékelt karakterek és elválasztók törlése", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Egyenlet törlése", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Karakter törlése", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Gyök törlése", "SSE.Controllers.DocumentHolder.txtEnds": "z<PERSON>r<PERSON><PERSON>k", "SSE.Controllers.DocumentHolder.txtEquals": "Egyenlők", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Cella színével egyenlő", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Azonos karakter sz<PERSON>", "SSE.Controllers.DocumentHolder.txtExpand": "Kibont és rendez", "SSE.Controllers.DocumentHolder.txtExpandSort": "A kijelölt adatok mellett található adatok nem lesznek rendezve. Szeretné kibővíteni a kijelölést a szomszédos adatok felvételével, vagy csak a jelenleg kiválasztott cellákat rendezi?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Alsó", "SSE.Controllers.DocumentHolder.txtFilterTop": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtFractionLinear": "<PERSON><PERSON><PERSON> v<PERSON>", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Torzított törtre váltás", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Torzított törtre váltás", "SSE.Controllers.DocumentHolder.txtGreater": "Nagyobb mint", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "<PERSON><PERSON><PERSON><PERSON> vagy e<PERSON>", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Szöveg feletti szi<PERSON>ólum", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Szöveg utáni szimbólum", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Visszaadja a táblázat vagy a megadott táblázatoszlopok oszlopfejléceit", "SSE.Controllers.DocumentHolder.txtHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideBottom": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Alsó limit elrejtése", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideDegree": "Fokok el<PERSON>", "SSE.Controllers.DocumentHolder.txtHideHor": "Vízszintes vonal elrejtése", "SSE.Controllers.DocumentHolder.txtHideLB": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON><PERSON> <PERSON> s<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideLT": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Pozicioná<PERSON>ó <PERSON>", "SSE.Controllers.DocumentHolder.txtHideRight": "<PERSON><PERSON> s<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideTop": "<PERSON><PERSON><PERSON> sze<PERSON>ly <PERSON>", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Felső limit elrejtése", "SSE.Controllers.DocumentHolder.txtHideVer": "Függőleg<PERSON> vonal elrejtése", "SSE.Controllers.DocumentHolder.txtImportWizard": "Szöveg importá<PERSON>ás <PERSON>ló", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Argumentum méret növelése", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "<PERSON>jon meg az argumentumot utána", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "<PERSON>jon meg az argumentumot el<PERSON>", "SSE.Controllers.DocumentHolder.txtInsertBreak": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "<PERSON><PERSON><PERSON><PERSON> beszúrása utánna", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>ú<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtItems": "t<PERSON>telek", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Csak szöveg megtartása", "SSE.Controllers.DocumentHolder.txtLess": "Kevesebb mint", "SSE.Controllers.DocumentHolder.txtLessEquals": "Kisebb vagy egyenlő mint", "SSE.Controllers.DocumentHolder.txtLimitChange": "Határértékek helyének megváltoztatása", "SSE.Controllers.DocumentHolder.txtLimitOver": "Szöveg fölötti limit", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Szöveg alatti limit", "SSE.Controllers.DocumentHolder.txtLockSort": "Adatok találhatók a kijel<PERSON><PERSON>, de nincs elege<PERSON> engedélye a cellák módosításához.<br>Szeretné folytatni a jelenlegi kijelöléssel?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Zárójelek és argumentum egyenlő magasságú", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtNoChoices": "<PERSON><PERSON><PERSON> a cellának kitöltésére.<br>Csak az oszlopból származó szövegértékek választhatók ki cserére.", "SSE.Controllers.DocumentHolder.txtNotBegins": "nem azzal k<PERSON>", "SSE.Controllers.DocumentHolder.txtNotContains": "nem tartalmaz", "SSE.Controllers.DocumentHolder.txtNotEnds": "nem <PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtNotEquals": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtOr": "vagy", "SSE.Controllers.DocumentHolder.txtOverbar": "Sáv a szöveg fölött", "SSE.Controllers.DocumentHolder.txtPaste": "Beilleszt", "SSE.Controllers.DocumentHolder.txtPasteBorders": "<PERSON>ügg<PERSON><PERSON><PERSON> szegélyek <PERSON>l", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Függvény + oszlop szélesség", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Csak formázás be<PERSON>", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Függvény + számformátum", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Csak függvény beillesztése", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Függvény + minden formázás", "SSE.Controllers.DocumentHolder.txtPasteLink": "<PERSON>", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Hivatkozott kép", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Feltételes formázás egyesítése", "SSE.Controllers.DocumentHolder.txtPastePicture": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Érték + minden formá<PERSON>ás", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Érték + számformátum", "SSE.Controllers.DocumentHolder.txtPasteValues": "Csak érték beillesztése", "SSE.Controllers.DocumentHolder.txtPercent": "Százalék", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Táblázat auto-kiterjesztés vissza", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "T<PERSON><PERSON><PERSON>l eltávolítása", "SSE.Controllers.DocumentHolder.txtRemLimit": "<PERSON>it <PERSON>", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Ékezet eltávolítása", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Sáv eltávolítása", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "El szeretné távolítani ezt az aláírást?<br><PERSON><PERSON> le<PERSON> v<PERSON>.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Szkriptek eltávolítása", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Alsó index eltávolítása", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Felső index eltávolítása", "SSE.Controllers.DocumentHolder.txtRowHeight": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Scripts a szöveg után", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Scripts a szöveg előtt", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Alsó limit megjelenítése", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtShowDegree": "Fokok megjelenítése", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Zárójel nyitás megjelenítése", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Pozicionáló mutatása", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Felső limit megjelenítése", "SSE.Controllers.DocumentHolder.txtSorting": "Rendez<PERSON>", "SSE.Controllers.DocumentHolder.txtSortSelected": "Kiválasztottak renezése", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Zárójelek nyújtása", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Csak ezt a sort válassza ki a megadott oszlopból", "SSE.Controllers.DocumentHolder.txtTop": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "A táblázat vagy a megadott táblázatoszlopok összes sorát adja vissza", "SSE.Controllers.DocumentHolder.txtUnderbar": "Sáv a szöveg alatt", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Táblázat automatikus bővítésének visszaállítása", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Használja a szöveg importálás varázslót", "SSE.Controllers.DocumentHolder.txtWarnUrl": "A link megnyitása káros lehet az eszközére és adataira.<br>Biztosan folytatja?", "SSE.Controllers.DocumentHolder.txtWidth": "Szélesség", "SSE.Controllers.DocumentHolder.warnFilterError": "You need at least one field in the Values area in order to apply a value filter.", "SSE.Controllers.FormulaDialog.sCategoryAll": "Minden", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Custom", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Adatb<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Dátum <PERSON>", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Pénzügyi", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Információ", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 legut<PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logikai", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Keresés és hivatkozás", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Matematika és trigonometria", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statisztikai", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Szöveg és adat", "SSE.Controllers.LeftMenu.newDocumentTitle": "Névtelen munkafüzet", "SSE.Controllers.LeftMenu.textByColumns": "Oszlopoktól", "SSE.Controllers.LeftMenu.textByRows": "Soroktól", "SSE.Controllers.LeftMenu.textFormulas": "Függvények", "SSE.Controllers.LeftMenu.textItemEntireCell": "Teljes cellatartalom", "SSE.Controllers.LeftMenu.textLoadHistory": "Verzióelőzmények betöltése...", "SSE.Controllers.LeftMenu.textLookin": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textNoTextFound": "A keresett adatok nem találhatók. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> a keresési beállításokon.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "A csere megtörtént. {0} események kihagyásra kerültek.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "A keresés megtörtént. Helyettesített események: {0}", "SSE.Controllers.LeftMenu.textSave": "Save", "SSE.Controllers.LeftMenu.textSearch": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "SSE.Controllers.LeftMenu.textSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textValues": "<PERSON>rté<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWarning": "Figyelmeztetés", "SSE.Controllers.LeftMenu.textWithin": "Belül", "SSE.Controllers.LeftMenu.textWorkbook": "Munkafüzet", "SSE.Controllers.LeftMenu.txtUntitled": "Névtelen", "SSE.Controllers.LeftMenu.warnDownloadAs": "Ha ebbe a form<PERSON><PERSON><PERSON> ment, a nyers szövegen kívül minden elveszik.<br><PERSON><PERSON><PERSON> ben<PERSON>, hogy folytatni akarja?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "The CSV format does not support saving a multi-sheet file.<br>To keep the selected format and save only the current sheet, press Save.<br>To save the current spreadsheet, click Cancel and save it in a different format.", "SSE.Controllers.Main.confirmAddCellWatches": "This action will add {0} cell watches.<br>Do you want to continue?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "This action will add only {0} cell watches by memory save reason.<br>Do you want to continue?", "SSE.Controllers.Main.confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "SSE.Controllers.Main.confirmMoveCellRange": "A cél cella tartomány tartalmazhat adatokat. Folytassa a műveletet?", "SSE.Controllers.Main.confirmPutMergeRange": "A forrásadatok összevont cellákat tartalmaztak.<br>A cellák szétválasztásra kerültek a beillesztés előtt.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "A fejlécsorban lévő függvények el lesznek távolítva, majd statikus szöveggé alakítva.<br>Folytatja?", "SSE.Controllers.Main.confirmReplaceHFPicture": "Only one picture can be inserted in each section of the header.<br>Press \"Replace\" to replace existing picture.<br>Press \"Keep\" to keep existing picture.", "SSE.Controllers.Main.convertationTimeoutText": "Időtúllépés az átalakítás során.", "SSE.Controllers.Main.criticalErrorExtText": "Nyomja meg az \"OK\" gombot a dokumentumlistához való visszatéréshez.", "SSE.Controllers.Main.criticalErrorTitle": "Hiba", "SSE.Controllers.Main.downloadErrorText": "Sikertelen letöltés.", "SSE.Controllers.Main.downloadTextText": "Munkafüzet letöltése...", "SSE.Controllers.Main.downloadTitleText": "Munkafüzet letöltése", "SSE.Controllers.Main.errNoDuplicates": "<PERSON><PERSON> ismétlődő érték.", "SSE.Controllers.Main.errorAccessDeny": "<PERSON><PERSON><PERSON> pr<PERSON><PERSON><PERSON><PERSON>, me<PERSON>re ninc<PERSON> j<PERSON>.<br>Vegye fel a kapcsolatot a Document Server adminisztrátor<PERSON>val.", "SSE.Controllers.Main.errorArgsRange": "Hiba a megadott függvényben.<br>Helytelen argumentumtartomány került alkalmazásra.", "SSE.Controllers.Main.errorAutoFilterChange": "A művelet nem megen<PERSON>, mivel megpróbálja a cellákat a munkalapon lévő táblázatban eltolni.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "A műveletet nem lehetett elvégezni a kiválasztott cellák esetében, mivel nem tudunk mozgatni a táblázat egy részét.<br>Válasszon másik adattartományt, hogy az egész asztal el<PERSON>ódjon, és próbálja újra.", "SSE.Controllers.Main.errorAutoFilterDataRange": "A műveletet nem lehetett elvégezni a kiválasztott cellatartományban.<br>Válasszon egy egységes adattartományt, amely el<PERSON> a meglévő cellától, és próbálja újra.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "A műveletet nem lehet v<PERSON>, mert a terület szűrt cellákat tartalmaz.<br>Szüntesse meg a szűrt elemeket, és próbálja újra.", "SSE.Controllers.Main.errorBadImageUrl": "Hibás kép URL", "SSE.Controllers.Main.errorCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the spreadsheet.", "SSE.Controllers.Main.errorCannotUngroup": "Nem le<PERSON>t feloldani a csoportosítást. A körvonalazás elindításához jelölje ki a részletező sorokat vagy oszlopokat, és csoportosítsa őket.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Ez a parancs nem használható védett munkalapon. A parancs használatához szüntesse meg a munkalap védelmét.<br><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy j<PERSON>zót kell megadnia.", "SSE.Controllers.Main.errorChangeArray": "<PERSON><PERSON> a tömb egy részét.", "SSE.Controllers.Main.errorChangeFilteredRange": "Ez megváltoztat a munkalapon egy szűrt tartományt.<br>A feladat végrehajtásához távolítsa el az automatikus szűrőket.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "A módosítani kívánt cella vagy diagram egy védett munkalapon található.<br>Módosítás<PERSON>z szüntesse meg a munkalap védelmét. Előfordulhat, hogy ehhez j<PERSON>zót kell megadnia.", "SSE.Controllers.Main.errorCircularReference": "There are one or more circular references where a formula refers to its own cell either directly or indirectly.<br>Try removing or changing these references, or moving the formulas to different cells.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Elveszett a kapcsolat a szerverrel. A dokumentum jelenleg nem szerkeszthető.", "SSE.Controllers.Main.errorConnectToServer": "A dokumentum mentése nem lehetséges. Kérjük ellenőrizze a kapcsolódási beállításokat vagy lépjen kapcsolatba a rendszer adminisztrátorral.<br>Ha az 'OK'-ra kattint letöltheti a dokumentumot.", "SSE.Controllers.Main.errorConvertXml": "The file has an unsupported format.<br>Only XML Spreadsheet 2003 format can be used.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Ez a parancs nem használhat<PERSON> többes kiválasztással.<br>Válasszon ki egy tartományt és próbálja újra.", "SSE.Controllers.Main.errorCountArg": "Hiba a megadott függvényben.<br><PERSON><PERSON> meg<PERSON> számú argumentum használata.", "SSE.Controllers.Main.errorCountArgExceed": "Hiba a megadott függvényben.<br>Az argumentumok száma túllépve.", "SSE.Controllers.Main.errorCreateDefName": "A meglévő tartományok nem szerkeszthetők, és az újakat nem lehet létrehozni<br> j<PERSON><PERSON><PERSON> némely szerkesztés alatt <PERSON>ll.", "SSE.Controllers.Main.errorCreateRange": "The existing ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Controllers.Main.errorDatabaseConnection": "K<PERSON><PERSON>ő hiba.<br>Adatb<PERSON><PERSON>s-kapcsolati hiba. Ha a hiba továbbra is fen<PERSON><PERSON>, lép<PERSON>n kapcsolatba a rendszer támogatással.", "SSE.Controllers.Main.errorDataEncrypted": "Titkosított változások érkeztek, me<PERSON><PERSON> felold<PERSON> si<PERSON>.", "SSE.Controllers.Main.errorDataRange": "<PERSON><PERSON><PERSON> ad<PERSON>.", "SSE.Controllers.Main.errorDataValidate": "A megadott érték nem érvényes.<br>A felhasználónak korlátozott értékei van<PERSON>, amelyeket be lehet írni a cellába.", "SSE.Controllers.Main.errorDefaultMessage": "Hibakód: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "<PERSON><PERSON><PERSON> pr<PERSON><PERSON><PERSON><PERSON>, amely z<PERSON><PERSON>t cellát tartalmaz. A zárolt cellák nem töröl<PERSON>tők, amíg a munkalap védett.<br><PERSON><PERSON><PERSON><PERSON> cella törléséhez szüntesse meg a munkalap védelmét. Előfordulhat, hogy ehhez jelszót kell megadnia.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "<PERSON><PERSON><PERSON> sort pr<PERSON><PERSON><PERSON><PERSON>, amely z<PERSON><PERSON>t cellát tartalmaz. A zárolt cellák nem törö<PERSON>ők, amíg a munkalap védett.<br><PERSON><PERSON><PERSON><PERSON> cella törléséhez szüntesse meg a munkalap védelmét. Előfordulhat, hogy ehhez j<PERSON>zót kell megadnia.", "SSE.Controllers.Main.errorDependentsNoFormulas": "The Trace Dependents command found no formulas that refer to the active cell.", "SSE.Controllers.Main.errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "SSE.Controllers.Main.errorEditingDownloadas": "A dokumentummal való munka során hiba történt.<br>Haszná<PERSON>ja a 'Letöltés másként' opciót a fájl biztonsági másolatának mentéséhez meghajtóra.", "SSE.Controllers.Main.errorEditingSaveas": "Hiba történt a dokumentummal végzett munka során.<br><PERSON><PERSON><PERSON><PERSON><PERSON> a 'Mentés másként...' op<PERSON><PERSON><PERSON>, hogy mentse a fájl biztonsági másolatát a számítógép merevlemezére.", "SSE.Controllers.Main.errorEditView": "A meglévő lapnézet nem szerkeszthető, és újak nem hozhatók létre j<PERSON>nleg, mivel néhányukat szerkesztik.", "SSE.Controllers.Main.errorEmailClient": "<PERSON><PERSON> e-mail kliens.", "SSE.Controllers.Main.errorFilePassProtect": "A dokumentum jels<PERSON>ó<PERSON> v<PERSON>, és nem nyitható meg.", "SSE.Controllers.Main.errorFileRequest": "<PERSON><PERSON><PERSON><PERSON> hiba.<br><PERSON><PERSON><PERSON><PERSON> kéré<PERSON> hiba. Ha a hiba továbbra is fen<PERSON><PERSON>, lép<PERSON>n kap<PERSON>olatba a támogatással.", "SSE.Controllers.Main.errorFileSizeExceed": "A fájlméret meghaladja a szerverre beállított korlátozást.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, forduljon a Document Server rendszergazdájához a részletekért.", "SSE.Controllers.Main.errorFileVKey": "<PERSON><PERSON><PERSON><PERSON> hiba.<br>Helytelen biztonsági kulcs. Ha a hiba továbbra is fen<PERSON><PERSON>, lép<PERSON>n kap<PERSON>olatba a támogatással.", "SSE.Controllers.Main.errorFillRange": "<PERSON><PERSON> a kiválasztott cellatartományt.<br>Minden egyesített cellának azonos méretűnek kell lennie.", "SSE.Controllers.Main.errorForceSave": "Hiba történt a fájl mentése közben. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, használja a 'Letöltés másként' opciót a fájl meghajtóra mentéséhez, vagy próbálja meg később újra.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Cannot enter a formula for an item or field name in a pivot table report.", "SSE.Controllers.Main.errorFormulaName": "Hiba a bevitt függvényben.<br>He<PERSON>tel<PERSON> függvénynév.", "SSE.Controllers.Main.errorFormulaParsing": "Belső hiba a függvény elemzése közben.", "SSE.Controllers.Main.errorFrmlMaxLength": "A függvény hossza meghaladja a 8192 karakteres korlátot.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, módosítsa és próbálja újra.", "SSE.Controllers.Main.errorFrmlMaxReference": "<PERSON>em adhatja meg ezt a függ<PERSON><PERSON><PERSON>, mert túl sok értéke,<br>cellahivatkozása és/vagy neve van.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "A függvényben a szövegértékek legfeljebb 255 karakterre korlátozódhatnak.<br>Használja a CONCATENATE funkciót vagy az összefűző operátort (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "A függvény nem létező munkalapra vonatkozik.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> az adatokat és próbálja újra.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "A műveletet nem sikerült befejezni a kiválasztott cellatartományban.<br>Válasszon ki egy tartományt, hogy az első táblázat sora ugyanabban a sorban legyen, és az eredményül kapott táblázat átfedje az aktuális sort.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "A műveletet nem si<PERSON>ült befejezni a kiválasztott cellatartományban.<br>Válasszon ki egy olyan tartom<PERSON>, amely nem tartalmaz más táblákat.", "SSE.Controllers.Main.errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "SSE.Controllers.Main.errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInvalidRef": "Adjon meg egy helyes nevet a kijelöléshez, vagy érvényes hivatkozást.", "SSE.Controllers.Main.errorKeyEncrypt": "Ismeretlen kulcsleíró", "SSE.Controllers.Main.errorKeyExpire": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.errorLabledColumnsPivot": "Kimutatás tábla létrehozásához használjon listaként rendezett adatokat címkézett oszlopokkal.", "SSE.Controllers.Main.errorLoadingFont": "A betűtípusok nincsenek betöltve.<br>Kérjük forduljon a dokumentumszerver rendszergazdájához.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "A hely vagy adattartomány hivatkozása nem érvényes.", "SSE.Controllers.Main.errorLockedAll": "A műveletet nem lehetett v<PERSON>, mivel a munkalapot egy másik felhaszná<PERSON>ó <PERSON>.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "One of the cells involved in the goal seek process has been modified by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "<PERSON>em m<PERSON>thatja az adatokat a pivot táblában.", "SSE.Controllers.Main.errorLockedWorksheetRename": "A lapot nem lehet <PERSON>, egy másik fel<PERSON>z<PERSON><PERSON><PERSON> átnevezte azt", "SSE.Controllers.Main.errorMaxPoints": "A soronkénti maximális pontszám diagramonként 4096.", "SSE.Controllers.Main.errorMoveRange": "<PERSON>em lehet megváltoztatni egy egyesített cella egy részét", "SSE.Controllers.Main.errorMoveSlicerError": "A táblázat elválasztói nem másolhatók át egyik munkafüzetből a másikba.<br>Próbálja meg újra a teljes táblázat és az elválasztók kiválasztásával.", "SSE.Controllers.Main.errorMultiCellFormula": "A táblázatokban nem használhatók többcellás tömbfüggvények.", "SSE.Controllers.Main.errorNoDataToParse": "Nem volt feldolgozásra kiválasztott adat.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.Main.errorOpenWarning": "Az egyik fájlfüggvény meghaladja a 8192 karakteres korlátot.<br>A függvényt eltávolítottuk.", "SSE.Controllers.Main.errorOperandExpected": "A bevitt függvényszintaxis nem megfelelő. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, hogy hiányzik-e az egyik zárójel - '(' vagy ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "A megadott jels<PERSON>ó helytelen.<br><PERSON><PERSON><PERSON>z<PERSON>djön meg arr<PERSON>, hogy a CAPS LOCK billentyű ki van ka<PERSON>olva, <PERSON>s ügyeljen arra, hogy a megfelelő nagybetűket használja.", "SSE.Controllers.Main.errorPasteInPivot": "We can't make this change for the selected cells because it will affect a pivot table.<br>Use the field list to change the report.", "SSE.Controllers.Main.errorPasteMaxRange": "A másolási és beillesztési terület nem egyezik.<br>Válasszon ki egy azonos méretű területet, vagy katti<PERSON> a sorban lévő első cellára a másolt cellák beillesztéséhez.", "SSE.Controllers.Main.errorPasteMultiSelect": "Ez a művelet nem hajtható végre több tartomány kiválasztása esetén.<br>Válasszon ki egy tartományt, és próbálja újra.", "SSE.Controllers.Main.errorPasteSlicerError": "A táblázat elválasztói nem másolhatók át egyik munkafüzetből a másikba.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Pivot table field name already exists.", "SSE.Controllers.Main.errorPivotGroup": "A kijelölés nem csoportosítható.", "SSE.Controllers.Main.errorPivotOverlap": "<PERSON>z összefoglaló táblázatos jelentések és a táblázatok között eltérés van.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "A pivot táblázat jelentést a mögöttes adatok nélkül mentette.<br>A jelentés frissítéséhez használja a \"Frissítés\" gombot.", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "The Trace Precedents command requires that the active cell contain a formula which includes a valid references.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Sajnos nem lehet több mint 1500 oldalt egyszerre kinyomtatni az aktuális programverzióban.<br>Ez a korlátozás eltávolításra kerül a következő kiadásokban.", "SSE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON><PERSON><PERSON> men<PERSON>", "SSE.Controllers.Main.errorProtectedRange": "This range is not allowed for editing.", "SSE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "SSE.Controllers.Main.errorServerVersion": "A szerkesztő verziója frissült. Az oldal újratöltésre kerül a módosítások alkalmazásához.", "SSE.Controllers.Main.errorSessionAbsolute": "A dokumentumszerkesztési munkamenet lejárt. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, töltse be újra az oldalt.", "SSE.Controllers.Main.errorSessionIdle": "A dokumentumot sokáig nem szerkesztették. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, töltse be újra az oldalt.", "SSE.Controllers.Main.errorSessionToken": "A szerverrel való kapcsolat megszakadt. Töltse be újra az oldalt.", "SSE.Controllers.Main.errorSetPassword": "A j<PERSON>ót nem lehet be<PERSON>.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "A helyhivatkozás nem érvényes, mert a cellák nem mindegyike ugyanabban az oszlopban vagy sorban található.<br>Válassza ki azokat a cellákat, amelyek mindegyike egyetlen oszlopban vagy sorban található.", "SSE.Controllers.Main.errorStockChart": "Helytelen sor sorrend. Tőzsdei diagram létrehozásához az adatokat az alábbi sorrendben vigye fel:<br>nyi<PERSON><PERSON> ár, maximum ár, minimum ár, z<PERSON><PERSON><PERSON> ár.", "SSE.Controllers.Main.errorToken": "A dokumentum biztonsági tokenje nem megfelelő.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lé<PERSON><PERSON>n kap<PERSON>olatba a Dokumentumszerver rendszergazdájával.", "SSE.Controllers.Main.errorTokenExpire": "A dokumentum biztonsági tokenje lejárt.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lé<PERSON><PERSON><PERSON> kap<PERSON>olatba a dokumentumszerver rendszergazdájával.", "SSE.Controllers.Main.errorUnexpectedGuid": "K<PERSON>lső hiba.<br>Váratlan GUID. Ha a hiba továbbra is fen<PERSON><PERSON>, lépjen kap<PERSON>olatba a támogatással.", "SSE.Controllers.Main.errorUpdateVersion": "A dokumentum verziója megváltozott. Az oldal újratöltődik.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Az internetkapcsolat helyreállt és a fájl verziója megváltozott.<br><PERSON><PERSON><PERSON><PERSON> folytatná a munk<PERSON>, töltse le a fájlt vagy másolja át annak tartalmát megbizonyosodva arról, hogy semmi sem veszett el, végül töltse újra ezt az oldalt.", "SSE.Controllers.Main.errorUserDrop": "A dokumentum jelenleg nem elérhető", "SSE.Controllers.Main.errorUsersExceed": "Túllépte a csomagja által engedélyezett felhasználók számát", "SSE.Controllers.Main.errorViewerDisconnect": "A kapcsolat megszakadt. Továbbra is megtekinthető a dokumentum,<br>de a kapcsolat helyreálltáig és az oldal újratöltéséig nem lehet letölteni.", "SSE.Controllers.Main.errorWrongBracketsCount": "Hiba a bevitt függvényben.<br>A zárójelek száma hibás.", "SSE.Controllers.Main.errorWrongOperator": "Hiba a megadott függvényben. Hibás operátor has<PERSON>.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jav<PERSON><PERSON><PERSON> ki a hib<PERSON><PERSON>.", "SSE.Controllers.Main.errorWrongPassword": "A megadott jelszó nem megfelelő.", "SSE.Controllers.Main.errRemDuplicates": "Ismétlődő értékek voltak és törlésre kerültek: {0}, egyedi értékek maradtak: {1}.", "SSE.Controllers.Main.leavePageText": "El nem mentett változások vannak a munkafüzetben. A mentéshez kattintson a \"Maradjon ezen az oldalon\", majd a \"Mentés\" gombra. Az elmentett módosítások elvetéséhez kattintson az \"Oldal elhagyása\" gombra.", "SSE.Controllers.Main.leavePageTextOnClose": "A táblázatban lévő összes nem mentett módosítás elveszik.<br> <PERSON><PERSON><PERSON><PERSON> a „Mégse”, majd a „Mentés” gombra a mentésükhöz. Kattintson az „OK” gombra az összes nem mentett módosítás elvetéséhez.", "SSE.Controllers.Main.loadFontsTextText": "Adatok betöltése...", "SSE.Controllers.Main.loadFontsTitleText": "Adatok betöltése", "SSE.Controllers.Main.loadFontTextText": "Adatok betöltése...", "SSE.Controllers.Main.loadFontTitleText": "Adatok betöltése", "SSE.Controllers.Main.loadImagesTextText": "Képek betöltése...", "SSE.Controllers.Main.loadImagesTitleText": "Képek betöltése", "SSE.Controllers.Main.loadImageTextText": "<PERSON><PERSON>p betöltése...", "SSE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadingDocumentTitleText": "Munkafüzet betöltése", "SSE.Controllers.Main.notcriticalErrorTitle": "Figyelmeztetés", "SSE.Controllers.Main.openErrorText": "Hiba történt a fájl megnyitásakor", "SSE.Controllers.Main.openTextText": "Munkafüzet megnyitása..", "SSE.Controllers.Main.openTitleText": "Munkafüzet megnyitása", "SSE.Controllers.Main.pastInMergeAreaError": "<PERSON>em lehet megváltoztatni egy egyesített cella egy részét", "SSE.Controllers.Main.printTextText": "Munkafüzet nyomtatása...", "SSE.Controllers.Main.printTitleText": "Munkafüzet nyomtatása", "SSE.Controllers.Main.reloadButtonText": "Oldal újratöltése", "SSE.Controllers.Main.requestEditFailedMessageText": "Jelenleg valaki más szerkeszti ezt a dokumentumot. Próbálja újra később.", "SSE.Controllers.Main.requestEditFailedTitleText": "Hozzáfé<PERSON><PERSON>", "SSE.Controllers.Main.saveErrorText": "Hiba történt a fájl mentése során.", "SSE.Controllers.Main.saveErrorTextDesktop": "Ezt a fájlt nem lehet menteni vagy létrehozni.<br>Lehetséges okok:<br>1. A fájl csak olvasható.<br>2. A fájlt más felhasználók szerkesztik.<br>3. A lemez megtelt vagy s<PERSON>ült.", "SSE.Controllers.Main.saveTextText": "Munkafüzet mentése...", "SSE.Controllers.Main.saveTitleText": "Munkafüzet mentése", "SSE.Controllers.Main.scriptLoadError": "A kapcsolat túl las<PERSON>, néhány komponens nem töltődött be. Frissítse az oldalt.", "SSE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textApplyAll": "Minden egyenletre alkalmazza", "SSE.Controllers.Main.textBuyNow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textChangesSaved": "Minden módosítás elmentve", "SSE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textCloseTip": "<PERSON><PERSON><PERSON><PERSON>, a tippek bezárásához", "SSE.Controllers.Main.textConfirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "SSE.Controllers.Main.textContactUs": "Értékesítés elérhetősége", "SSE.Controllers.Main.textContinue": "Continue", "SSE.Controllers.Main.textConvertEquation": "Ez az egyenlet az egyenletszerkesztő régi verz<PERSON> lett lé<PERSON>hozva, amely már nem támo<PERSON>ott. A szerkesztéshez alakítsa az egyenletet Office Math ML formátumra.<br>Konvertáljuk most?", "SSE.Controllers.Main.textCustomLoader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ve<PERSON><PERSON>, hogy a licence feltételei szerint nem jogosult a betöltő cseréjére.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, forduljon értékesítési osztály<PERSON>z, hogy árajánlatot kapjon.", "SSE.Controllers.Main.textDisconnect": "A kapcsolat megszakadt", "SSE.Controllers.Main.textFillOtherRows": "T<PERSON>bbi sor kitöltése", "SSE.Controllers.Main.textFormulaFilledAllRows": "Függvénnyel kitöltött {0} sor tartalmaz adatokat. A többi üres sor kitöltése eltarthat néhány percig.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "A függvény kitöltötte az első {0} sort. A többi üres sor kitöltése eltarthat néhány percig.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "A függvény csak az első {0} sort töltötte ki memóriamentési okokból. A munkalapon a további {1} sor is tartalmaz adatokat. Ezeket manuá<PERSON> is kitöltheti.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "A függvény csak az első {0} sort töltötte ki memóriamentési okokból. A munkalap többi sora nem tartalmaz adatokat.", "SSE.Controllers.Main.textGuest": "Vendég", "SSE.Controllers.Main.textHasMacros": "A fájl automatikus makrókat tartalmaz.<br><PERSON><PERSON><PERSON><PERSON> makrókat futtatni?", "SSE.Controllers.Main.textKeep": "Keep", "SSE.Controllers.Main.textLearnMore": "<PERSON><PERSON><PERSON> meg többet", "SSE.Controllers.Main.textLoadingDocument": "Munkafüzet betöltése", "SSE.Controllers.Main.textLongName": "<PERSON><PERSON><PERSON> be egy 128 karak<PERSON><PERSON><PERSON> nevet.", "SSE.Controllers.Main.textNeedSynchronize": "Frissítések érhetőek el", "SSE.Controllers.Main.textNo": "Nem", "SSE.Controllers.Main.textNoLicenseTitle": "Elérte a licenckorlátot", "SSE.Controllers.Main.textPaidFeature": "<PERSON><PERSON><PERSON>tt <PERSON>", "SSE.Controllers.Main.textPleaseWait": "A művelet a vártnál több időt vehet igénybe. Kérjük várjon...", "SSE.Controllers.Main.textReconnect": "A ka<PERSON>t he<PERSON>", "SSE.Controllers.Main.textRemember": "Emlékezzen a választásomra minden fájlhoz", "SSE.Controllers.Main.textRememberMacros": "Emlékezzen a választásomra minden makró esetében", "SSE.Controllers.Main.textRenameError": "A felhasználónév nem lehet üres.", "SSE.Controllers.Main.textRenameLabel": "Adjon meg egy nevet az együttműködéshez", "SSE.Controllers.Main.textReplace": "Replace", "SSE.Controllers.Main.textRequestMacros": "A makró URL-kérést indít. Engedélyezi a %1 lekérdezését?", "SSE.Controllers.Main.textShape": "Alakzat", "SSE.Controllers.Main.textStrict": "Biztonsá<PERSON> mód", "SSE.Controllers.Main.textText": "Szöveg", "SSE.Controllers.Main.textTryQuickPrint": "You have selected Quick print: the entire document will be printed on the last selected or default printer.<br>Do you want to continue?", "SSE.Controllers.Main.textTryUndoRedo": "A Visszavonás / Újra funkciók le vannak tiltva a Gyors együttes szerkesztés módban.<br>A \"Biztonságos mód\" gombra kattintva válthat a Biztonságos együttes szerkesztés módra, hogy a dokumentumot más felhasználókkal való interferencia nélkül tudja szerkeszteni, mentés után küldve el a módosításokat. A szerkesztési módok között a Speciális beállítások segítségével válthat.", "SSE.Controllers.Main.textTryUndoRedoWarn": "A Visszavonás/Újra funkciók le vannak tiltva a Gyors társszerkesztés módban.", "SSE.Controllers.Main.textUndo": "Undo", "SSE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "SSE.Controllers.Main.textUpdating": "Updating", "SSE.Controllers.Main.textYes": "Igen", "SSE.Controllers.Main.titleLicenseExp": "<PERSON><PERSON><PERSON><PERSON> licenc", "SSE.Controllers.Main.titleLicenseNotActive": "License not active", "SSE.Controllers.Main.titleServerVersion": "Szerkesztő frissítve", "SSE.Controllers.Main.titleUpdateVersion": "Version changed", "SSE.Controllers.Main.txtAccent": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtAll": "(Mind)", "SSE.Controllers.Main.txtArt": "Írja a szöveget ide", "SSE.Controllers.Main.txtBasicShapes": "Egyszerű alakzatok", "SSE.Controllers.Main.txtBlank": "(üres)", "SSE.Controllers.Main.txtButtons": "Gombok", "SSE.Controllers.Main.txtByField": "%1 a %2-ből", "SSE.Controllers.Main.txtCallouts": "Felhívások", "SSE.Controllers.Main.txtCharts": "Diagramok", "SSE.Controllers.Main.txtClearFilter": "Szűrő ürítése", "SSE.Controllers.Main.txtColLbls": "Oszlopcímkék", "SSE.Controllers.Main.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtConfidential": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtDays": "Napok", "SSE.Controllers.Main.txtDiagramTitle": "Diagram neve", "SSE.Controllers.Main.txtEditingMode": "Szerkesztési mód beállítása...", "SSE.Controllers.Main.txtErrorLoadHistory": "Az előzmények betöltése nem sikerült", "SSE.Controllers.Main.txtFiguredArrows": "Nyíl formák", "SSE.Controllers.Main.txtFile": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtGrandTotal": "Teljes összeg", "SSE.Controllers.Main.txtGroup": "Csoport", "SSE.Controllers.Main.txtHours": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtInfo": "Info", "SSE.Controllers.Main.txtLines": "<PERSON>ala<PERSON>", "SSE.Controllers.Main.txtMath": "Matematika", "SSE.Controllers.Main.txtMinutes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMonths": "Hónapok", "SSE.Controllers.Main.txtMultiSelect": "Többszörö<PERSON> k<PERSON>", "SSE.Controllers.Main.txtNone": "None", "SSE.Controllers.Main.txtOr": "%1 vagy %2", "SSE.Controllers.Main.txtPage": "<PERSON><PERSON>", "SSE.Controllers.Main.txtPageOf": "%1. oldal (Össz: %2)", "SSE.Controllers.Main.txtPages": "Oldalak", "SSE.Controllers.Main.txtPicture": "Picture", "SSE.Controllers.Main.txtPivotTable": "PivotTable", "SSE.Controllers.Main.txtPreparedBy": "Előkészítette", "SSE.Controllers.Main.txtPrintArea": "Nyomtatási terület", "SSE.Controllers.Main.txtQuarter": "Qtr", "SSE.Controllers.Main.txtQuarters": "Negyedévek", "SSE.Controllers.Main.txtRectangles": "Négyszögek", "SSE.Controllers.Main.txtRow": "Sor", "SSE.Controllers.Main.txtRowLbls": "Sorcímkék", "SSE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "SSE.Controllers.Main.txtScheme_Aspect": "Aspect", "SSE.Controllers.Main.txtScheme_Blue": "Blue", "SSE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "SSE.Controllers.Main.txtScheme_Blue_II": "Blue II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "SSE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "SSE.Controllers.Main.txtScheme_Green": "Green", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "SSE.Controllers.Main.txtScheme_Marquee": "Marquee", "SSE.Controllers.Main.txtScheme_Median": "Median", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Orange", "SSE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "SSE.Controllers.Main.txtScheme_Paper": "Paper", "SSE.Controllers.Main.txtScheme_Red": "Red", "SSE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "SSE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "SSE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "SSE.Controllers.Main.txtScheme_Violet": "Violet", "SSE.Controllers.Main.txtScheme_Violet_II": "Violet II", "SSE.Controllers.Main.txtScheme_Yellow": "Yellow", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "SSE.Controllers.Main.txtSeconds": "Másodpercek", "SSE.Controllers.Main.txtSeries": "Sorozatok", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "1. szövegbuborék (kerettel és vonallal)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "2. szövegbuborék (kerettel és vonallal)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "3. szövegbuborék (kerettel és vonallal)", "SSE.Controllers.Main.txtShape_accentCallout1": "1. szö<PERSON>g<PERSON><PERSON><PERSON><PERSON> (vonallal)", "SSE.Controllers.Main.txtShape_accentCallout2": "2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (vonallal)", "SSE.Controllers.Main.txtShape_accentCallout3": "3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (vonallal)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Vissza vagy el<PERSON>", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "<PERSON>z<PERSON><PERSON> gomb", "SSE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON><PERSON> gomb", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Dokumentum gomb", "SSE.Controllers.Main.txtShape_actionButtonEnd": "<PERSON><PERSON><PERSON> gomb", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "<PERSON><PERSON><PERSON>, vagy következő gomb", "SSE.Controllers.Main.txtShape_actionButtonHelp": "<PERSON><PERSON><PERSON><PERSON> gomb", "SSE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON><PERSON><PERSON><PERSON> gomb", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Inform<PERSON><PERSON><PERSON> gomb", "SSE.Controllers.Main.txtShape_actionButtonMovie": "<PERSON><PERSON><PERSON> gomb", "SSE.Controllers.Main.txtShape_actionButtonReturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gomb", "SSE.Controllers.Main.txtShape_actionButtonSound": "Hang gomb", "SSE.Controllers.Main.txtShape_arc": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentArrow": "Hajlított nyíl", "SSE.Controllers.Main.txtShape_bentConnector5": "Könyökcsatlakozó", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "T<PERSON>rt összekötő nyíl", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "<PERSON><PERSON><PERSON> dupla összekötő nyíl", "SSE.Controllers.Main.txtShape_bentUpArrow": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>", "SSE.Controllers.Main.txtShape_bevel": "Ferde", "SSE.Controllers.Main.txtShape_blockArc": "Körív blokk", "SSE.Controllers.Main.txtShape_borderCallout1": "1. <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_borderCallout2": "2. szövegbuborék", "SSE.Controllers.Main.txtShape_borderCallout3": "3. szövegbuborék", "SSE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_callout1": "1. szövegbuborék (keret nélkül)", "SSE.Controllers.Main.txtShape_callout2": "2. szövegbuborék (keret nélkül)", "SSE.Controllers.Main.txtShape_callout3": "3. szövegbuborék (keret nélkül)", "SSE.Controllers.Main.txtShape_can": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_chevron": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_chord": "Akkord", "SSE.Controllers.Main.txtShape_circularArrow": "Körkörös <PERSON>", "SSE.Controllers.Main.txtShape_cloud": "Fel<PERSON>ő", "SSE.Controllers.Main.txtShape_cloudCallout": "Lekérdez a felhőből", "SSE.Controllers.Main.txtShape_corner": "Sarok", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "G<PERSON>rbe összekötő", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Görbe összekötő nyíl", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "G<PERSON>rbe összekötő dupla nyíl", "SSE.Controllers.Main.txtShape_curvedDownArrow": "<PERSON><PERSON><PERSON> n<PERSON>íl", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "<PERSON><PERSON>ra ívelt nyíl", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Jobbra ívelt nyíl", "SSE.Controllers.Main.txtShape_curvedUpArrow": "<PERSON><PERSON><PERSON>í<PERSON>", "SSE.Controllers.Main.txtShape_decagon": "T<PERSON>zszög", "SSE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_dodecagon": "Tizenkétszög", "SSE.Controllers.Main.txtShape_donut": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON><PERSON> n<PERSON>l", "SSE.Controllers.Main.txtShape_downArrowCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ellipse": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ellipseRibbon": "<PERSON><PERSON><PERSON> szalag", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Folyamatábra: alternat<PERSON>v folyamat", "SSE.Controllers.Main.txtShape_flowChartCollate": "Folyamatábra: összehasonlít", "SSE.Controllers.Main.txtShape_flowChartConnector": "Folyamatábra: c<PERSON><PERSON>oz<PERSON>", "SSE.Controllers.Main.txtShape_flowChartDecision": "Folyamatábra: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDelay": "Folyamatábra: várakozás", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Folyamatábra: megjelení<PERSON>s", "SSE.Controllers.Main.txtShape_flowChartDocument": "Folyamatábra: dokumentum", "SSE.Controllers.Main.txtShape_flowChartExtract": "Folyamatábra: kivon", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Folyamatábra: adat", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Folyamatábra: be<PERSON><PERSON> tár", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Folyamatábra: mágneslemez", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Folyamatábra: k<PERSON><PERSON><PERSON><PERSON> eléré<PERSON>", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Folyamatábra: szekvenciális elérésű tároló", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Folyamatábra: k<PERSON><PERSON> bevitel", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Folyamatábra: k<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMerge": "Folyamatábra: összevon", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Folyamatábra: multi dokumentum", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Folyamatábra: <PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Folyamatábra: t<PERSON><PERSON><PERSON> adat", "SSE.Controllers.Main.txtShape_flowChartOr": "Folyamatábra: vagy", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Folyamatábra: <PERSON><PERSON><PERSON> defini<PERSON> folyamat", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Folyamatábra: előkészület", "SSE.Controllers.Main.txtShape_flowChartProcess": "Folyamatábra: folyamat", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Folyamatábra: k<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Folyamatábra: lyukasz<PERSON>tt szalag", "SSE.Controllers.Main.txtShape_flowChartSort": "Folyamatábra: rendez", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Folyamatábra: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Folyamatábra: megszakító", "SSE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON><PERSON><PERSON><PERSON> sarok", "SSE.Controllers.Main.txtShape_frame": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON><PERSON> keret", "SSE.Controllers.Main.txtShape_heart": "Szív", "SSE.Controllers.Main.txtShape_heptagon": "<PERSON><PERSON>tszög", "SSE.Controllers.Main.txtShape_hexagon": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_homePlate": "Ötszög", "SSE.Controllers.Main.txtShape_horizontalScroll": "Vízszintes görgetés", "SSE.Controllers.Main.txtShape_irregularSeal1": "Kitörés 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Kitörés 2", "SSE.Controllers.Main.txtShape_leftArrow": "Balra nyíl", "SSE.Controllers.Main.txtShape_leftArrowCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> balra", "SSE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON> z<PERSON>", "SSE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON> z<PERSON>", "SSE.Controllers.Main.txtShape_leftRightArrow": "Jobbra-balra nyíl", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-balra", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Jobbra-balra-f<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftUpArrow": "<PERSON><PERSON><PERSON> f<PERSON>", "SSE.Controllers.Main.txtShape_lightningBolt": "Villámlás", "SSE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "Nyíl", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Dupla nyíl", "SSE.Controllers.Main.txtShape_mathDivide": "Osztás", "SSE.Controllers.Main.txtShape_mathEqual": "Egyenlő", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Többszörözés", "SSE.Controllers.Main.txtShape_mathNotEqual": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathPlus": "Plusz", "SSE.Controllers.Main.txtShape_moon": "Hold", "SSE.Controllers.Main.txtShape_noSmoking": "\"Nem\" szimb<PERSON>lum", "SSE.Controllers.Main.txtShape_notchedRightArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON> jobb nyíl", "SSE.Controllers.Main.txtShape_octagon": "Nyolcszög", "SSE.Controllers.Main.txtShape_parallelogram": "Paralelogram", "SSE.Controllers.Main.txtShape_pentagon": "Ötszög", "SSE.Controllers.Main.txtShape_pie": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_plaque": "Aláír", "SSE.Controllers.Main.txtShape_plus": "Plusz", "SSE.Controllers.Main.txtShape_polyline1": "Firkálás", "SSE.Controllers.Main.txtShape_polyline2": "Szabad forma", "SSE.Controllers.Main.txtShape_quadArrow": "Négyes n<PERSON>", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Négy nyilas szövegbuborék", "SSE.Controllers.Main.txtShape_rect": "Téglalap", "SSE.Controllers.Main.txtShape_ribbon": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Controllers.Main.txtShape_ribbon2": "Felső szalag", "SSE.Controllers.Main.txtShape_rightArrow": "Jobbra nyíl", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Jobb nyíl szövegbuborék", "SSE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_round1Rect": "<PERSON><PERSON> sa<PERSON>ban lekerekített téglalap", "SSE.Controllers.Main.txtShape_round2DiagRect": "<PERSON><PERSON><PERSON><PERSON> le<PERSON>ekített sarkú téglalap", "SSE.Controllers.Main.txtShape_round2SameRect": "<PERSON><PERSON> oldalon lekerekített sarkú téglalap", "SSE.Controllers.Main.txtShape_roundRect": "Lekerekített sarkú téglalap", "SSE.Controllers.Main.txtShape_rtTriangle": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_smileyFace": "Mosolyka", "SSE.Controllers.Main.txtShape_snip1Rect": "<PERSON><PERSON> sarokban lemetszett téglalap", "SSE.Controllers.Main.txtShape_snip2DiagRect": "<PERSON><PERSON><PERSON><PERSON> le<PERSON> sarkú téglalap", "SSE.Controllers.Main.txtShape_snip2SameRect": "<PERSON><PERSON> oldalon le<PERSON> sarkú téglalap", "SSE.Controllers.Main.txtShape_snipRoundRect": "Lemetszett és egy oldalon lekerekített téglalap", "SSE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star10": "10-<PERSON><PERSON><PERSON> c<PERSON>g", "SSE.Controllers.Main.txtShape_star12": "12 pontos csillag", "SSE.Controllers.Main.txtShape_star16": "16-<PERSON><PERSON><PERSON> c<PERSON>g", "SSE.Controllers.Main.txtShape_star24": "24-<PERSON><PERSON><PERSON> c<PERSON>g", "SSE.Controllers.Main.txtShape_star32": "32-<PERSON><PERSON><PERSON> c<PERSON>g", "SSE.Controllers.Main.txtShape_star4": "4-<PERSON><PERSON><PERSON> c<PERSON>g", "SSE.Controllers.Main.txtShape_star5": "5-<PERSON><PERSON><PERSON> c<PERSON>g", "SSE.Controllers.Main.txtShape_star6": "6-<PERSON><PERSON><PERSON> c<PERSON>g", "SSE.Controllers.Main.txtShape_star7": "7-<PERSON><PERSON><PERSON> c<PERSON>g", "SSE.Controllers.Main.txtShape_star8": "8 pontos csillag", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Csíkos jobb nyíl", "SSE.Controllers.Main.txtShape_sun": "Vas", "SSE.Controllers.Main.txtShape_teardrop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_textRect": "Szövegdoboz", "SSE.Controllers.Main.txtShape_trapezoid": "Trapéz", "SSE.Controllers.Main.txtShape_triangle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_upArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_upArrowCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> f<PERSON>", "SSE.Controllers.Main.txtShape_upDownArrow": "Fel-le nyíl", "SSE.Controllers.Main.txtShape_uturnArrow": "Visszaforduló nyíl", "SSE.Controllers.Main.txtShape_verticalScroll": "Függőleges görgetés", "SSE.Controllers.Main.txtShape_wave": "Hullám", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "<PERSON><PERSON><PERSON><PERSON> szövegbuborék", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Négyzetes szövegbuborék", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Lekerekített téglalap alakú szövegbuborék", "SSE.Controllers.Main.txtSheet": "Sheet", "SSE.Controllers.Main.txtSlicer": "<PERSON>licer", "SSE.Controllers.Main.txtStarsRibbons": "Csillagok és szalagok", "SSE.Controllers.Main.txtStyle_Bad": "Hibás", "SSE.Controllers.Main.txtStyle_Calculation": "Feldolgozás", "SSE.Controllers.Main.txtStyle_Check_Cell": "Cellák ellenőrzése", "SSE.Controllers.Main.txtStyle_Comma": "Vess<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "Pénznem", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Magyarázó s<PERSON>öve<PERSON>", "SSE.Controllers.Main.txtStyle_Good": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Heading_1": "Címsor 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Címsor 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Címsor 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Címsor 4", "SSE.Controllers.Main.txtStyle_Input": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Ka<PERSON><PERSON>olt cella", "SSE.Controllers.Main.txtStyle_Neutral": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Normal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Note": "Jegyzet", "SSE.Controllers.Main.txtStyle_Output": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Percent": "Százalék", "SSE.Controllers.Main.txtStyle_Title": "Cím", "SSE.Controllers.Main.txtStyle_Total": "Összesen", "SSE.Controllers.Main.txtStyle_Warning_Text": "Figyelmeztető szöveg", "SSE.Controllers.Main.txtTab": "<PERSON><PERSON>", "SSE.Controllers.Main.txtTable": "T<PERSON>b<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtUnlock": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtUnlockRange": "Tart<PERSON><PERSON><PERSON> felo<PERSON>", "SSE.Controllers.Main.txtUnlockRangeDescription": "Adja meg a jelszót a tartomány módosításához:", "SSE.Controllers.Main.txtUnlockRangeWarning": "A módosítani kívánt tartomány jelszóval védett.", "SSE.Controllers.Main.txtValues": "<PERSON>rté<PERSON><PERSON>", "SSE.Controllers.Main.txtView": "View", "SSE.Controllers.Main.txtXAxis": "X tengely", "SSE.Controllers.Main.txtYAxis": "Y tengely", "SSE.Controllers.Main.txtYears": "Évek", "SSE.Controllers.Main.unknownErrorText": "Ismeretlen hiba.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "A böngészője nem támogatott.", "SSE.Controllers.Main.uploadDocExtMessage": "Ismeretlen dokumentum formátum.", "SSE.Controllers.Main.uploadDocFileCountMessage": "<PERSON><PERSON><PERSON> dokumentum.", "SSE.Controllers.Main.uploadDocSizeMessage": "A maximális dokumentum méret elérve.", "SSE.Controllers.Main.uploadImageExtMessage": "Ismeretlen képformátum.", "SSE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.uploadImageSizeMessage": "A kép túl nagy. A maximális méret 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "<PERSON><PERSON><PERSON>...", "SSE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.waitText": "K<PERSON><PERSON><PERSON><PERSON>k, várjon...", "SSE.Controllers.Main.warnBrowserIE9": "Az alkalmazás alacsony képességekkel rendelkezik az IE9-en. Használjon IE10 vagy <PERSON> verzi<PERSON>t", "SSE.Controllers.Main.warnBrowserZoom": "A böngészője jelenlegi nagyítása nem teljesen támogatott. Kérem <PERSON>llítsa vissza alapértelmezett értékre a Ctrl+0 megnyomásával.", "SSE.Controllers.Main.warnLicenseAnonymous": "Access denied for anonymous users.<br>This document will be opened for viewing only.", "SSE.Controllers.Main.warnLicenseBefore": "License not active.<br>Please contact your administrator.", "SSE.Controllers.Main.warnLicenseExceeded": "Elérte a(z) %1 szerkesztőhöz tartozó egyidejű csatlakozás korlátját. Ez a dokumentum csak megtekintésre nyílik meg.<br>További információért forduljon rendszergazdájához.", "SSE.Controllers.Main.warnLicenseExp": "A licence lejárt.<br><PERSON><PERSON><PERSON> fris<PERSON> a licencét, majd az oldalt.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "A licenc lejárt.<br><PERSON><PERSON><PERSON> a dokumentumszerkesztő funkciókhoz.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, l<PERSON><PERSON><PERSON><PERSON> ka<PERSON> a rendszergazdával.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "A licencet meg kell ú<PERSON>.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hozzáférése van a dokumentumszerkesztési funkciókhoz.<br>A teljes hozzáférésért forduljon rendszergazdájához", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Elérte a(z) %1 szerkesztőhöz tartozó felhasználói korlátját. További információért forduljon rendszergazdájához.", "SSE.Controllers.Main.warnNoLicense": "Elérte a(z) %1 szerkesztőhöz tartozó egyidejű csatlakozás korlátját. Ez a dokumentum csak megtekintésre nyílik meg.<br>Vegye fel a kapcsolatot a(z) %1 értékesítési csapattal a személyes frissítési feltételekért.", "SSE.Controllers.Main.warnNoLicenseUsers": "Elérte a(z) %1 szerkesztőhöz tartozó felhasználói korlátját. Vegye fel a kapcsolatot a(z) %1 értékesítési csapattal a személyes frissítési feltételekért.", "SSE.Controllers.Main.warnProcessRightsChange": "<PERSON>ncs joga szerkeszteni a fájl-t.", "SSE.Controllers.PivotTable.strSheet": "Sheet", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "No actions with calculated items are allowed for this active cell.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Calculated items do not work with custom subtotals.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "An item name cannot be found. Check that you've typed name correctly and the item is present in the PivotTable report.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Averages, standard deviations, and variances are not supported when a PivotTable report has calculated items.", "SSE.Controllers.Print.strAllSheets": "<PERSON><PERSON> lap", "SSE.Controllers.Print.textFirstCol": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textFirstRow": "<PERSON><PERSON>ő sor", "SSE.Controllers.Print.textFrozenCols": "Rögzített oszlopok", "SSE.Controllers.Print.textFrozenRows": "R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sorok", "SSE.Controllers.Print.textInvalidRange": "HIBA! Érvénytelen cellatartomány", "SSE.Controllers.Print.textNoRepeat": "Ne ismételje meg", "SSE.Controllers.Print.textRepeat": "Ismétlés...", "SSE.Controllers.Print.textSelectRange": "Tartomány kiválasztása", "SSE.Controllers.Print.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.txtZoomToPage": "Zoom to page", "SSE.Controllers.Search.textInvalidRange": "HIBA! Érvénytelen cellatartomány", "SSE.Controllers.Search.textNoTextFound": "A keresett adatot nem sikerült megtalálni. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, módosítsa a keresési beállításokat.", "SSE.Controllers.Search.textReplaceSkipped": "A csere megtörtént. {0} események kihagyásra kerültek.", "SSE.Controllers.Search.textReplaceSuccess": "A keresés megtörtént. {0} eredmények lettek kicserélve", "SSE.Controllers.Statusbar.errorLastSheet": "A munkafüzetnek legalább egy látható munkalapot kell tartalmaznia.", "SSE.Controllers.Statusbar.errorRemoveSheet": "<PERSON><PERSON> le<PERSON>t tö<PERSON> a munkalapot.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>A kapcsolat megs<PERSON>adt</b><br>Újrakapcsolódás folyamatban. Ellenőrizze a csatlakozási beállításokat.", "SSE.Controllers.Statusbar.textSheetViewTip": "Lapnézet módban van. A szűrőket és a rendezést csak Ön és azok láthatják, akik még mindig ebben a nézetben vannak.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Lapnézet mód<PERSON> van. A szűrőket csak Ön és azok láthatják, akik tov<PERSON>bb<PERSON> is ebben a nézetben vannak.", "SSE.Controllers.Statusbar.warnDeleteSheet": "A kiválasztott munkalapon lehetnek adatok. Biztosan folytatja a műveletet?", "SSE.Controllers.Statusbar.zoomText": "Zoom {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "A menteni kívánt betűkészlet nem érhető el az aktuális eszközön.<br>A szövegstílus a rendszer egyik betűkészletével jelenik meg, a mentett betűtípust akkor használja, ha elérhető.<br>Folytatni szeretné?", "SSE.Controllers.Toolbar.errorComboSeries": "Kombinált diagram létrehozásához válasszon legalább két adatsort.", "SSE.Controllers.Toolbar.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "HIBA! Az adatsorok maximális száma diagramonként 255", "SSE.Controllers.Toolbar.errorStockChart": "Helytelen sor sorrend. Tőzsdei diagram létrehozásához az adatokat az alábbi sorrendben vigye fel:<br>nyi<PERSON><PERSON> ár, maximum ár, minimum ár, z<PERSON><PERSON><PERSON> ár.", "SSE.Controllers.Toolbar.helpCalcItems": "Work with calculated items in Pivot Tables.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Calculated items", "SSE.Controllers.Toolbar.helpFastUndo": "Easily undo changes while collaborating on sheets in Fast mode.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "\"Undo\" in real-time co-editing", "SSE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "SSE.Controllers.Toolbar.textAccent": "Ak<PERSON>usok", "SSE.Controllers.Toolbar.textBracket": "Z<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textDirectional": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textFontSizeErr": "A megadott érték helytelen.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy számértéket 1 és 409 között", "SSE.Controllers.Toolbar.textFraction": "Törtek", "SSE.Controllers.Toolbar.textFunction": "Szögfüggvények", "SSE.Controllers.Toolbar.textIndicator": "Indikátorok", "SSE.Controllers.Toolbar.textInsert": "Beszúrás", "SSE.Controllers.Toolbar.textIntegral": "Integrálok", "SSE.Controllers.Toolbar.textLargeOperator": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.textLimitAndLog": "Határérték és logaritmus", "SSE.Controllers.Toolbar.textLongOperation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textOperator": "Operátorok", "SSE.Controllers.Toolbar.textPivot": "Pivot tábla", "SSE.Controllers.Toolbar.textRadical": "Gyökvonás", "SSE.Controllers.Toolbar.textRating": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRecentlyUsed": "<PERSON>an<PERSON><PERSON>", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textShapes": "Alakzatok", "SSE.Controllers.Toolbar.textSymbols": "Szimbólumok", "SSE.Controllers.Toolbar.textWarning": "Figyelmeztetés", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Jobbra-balra nyíl fent", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Balra mutató nyíl fent", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Jobbra nyíl fent", "SSE.Controllers.Toolbar.txtAccent_Bar": "Sáv", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Aláhúzás", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Felső sáv", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Dobozos függv<PERSON>y (pozicionálóval)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Dobozos <PERSON> (példa)", "SSE.Controllers.Toolbar.txtAccent_Check": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Felső összekötő", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "A vektor", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC felső csíkkal", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y felülhúzással", "SSE.Controllers.Toolbar.txtAccent_DDDot": "<PERSON><PERSON><PERSON> pont", "SSE.Controllers.Toolbar.txtAccent_DDot": "Kettőspont", "SSE.Controllers.Toolbar.txtAccent_Dot": "<PERSON>", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Grave": "Grave", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Alsó ka<PERSON>ek csoportosítása", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "<PERSON><PERSON> csoportosítása", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Leftwards harpoon above", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Rightwards harpoon above", "SSE.Controllers.Toolbar.txtAccent_Hat": "Tető", "SSE.Controllers.Toolbar.txtAccent_Smile": "B<PERSON>ve", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "Z<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Zárójelek elválasztójelekkel", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Zárójelek elválasztójelekkel", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_Curve": "Z<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Zárójelek elválasztójelekkel", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Esetek (két felt<PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON> (három <PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Verem objektum", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Verem objektum", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Binomiális <PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Binomiális <PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line": "Z<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Z<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Z<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_Round": "Z<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Zárójelek elválasztójelekkel", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_Square": "Z<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Z<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Z<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Z<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Z<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Z<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Egyszerű zárójel", "SSE.Controllers.Toolbar.txtDeleteCells": "Cellák törlése", "SSE.Controllers.Toolbar.txtExpand": "Kibont és rendez", "SSE.Controllers.Toolbar.txtExpandSort": "A kijelölt adatok mellett található adatok nem lesznek rendezve. Szeretné kibővíteni a kijelölést a szomszédos adatok felvételével, vagy csak a jelenleg kiválasztott cellákat rendezi?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "<PERSON><PERSON> tört", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionHorizontal": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi osztva kettővel", "SSE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON> t<PERSON>", "SSE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Inverz koszinusz függvény", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Hiperbolikus inverz koszinusz függvény", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Inverz kotangens függvény", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Hiperbolikus inverz kotangens függvény", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Inverz koszekáns függv<PERSON>y", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Hiperbolikus inverz koszekáns függvény", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Inverz szekáns f<PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Hiperbolikus inverz szekáns függvény", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "<PERSON><PERSON>z s<PERSON>z <PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Hiperbolikus inverz szinusz függvény", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Inverz tangens függvény", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Hiperbolikus inverz tangens függvény", "SSE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Hiperbolikus k<PERSON><PERSON>z <PERSON>", "SSE.Controllers.Toolbar.txtFunction_Cot": "Kotangens függvény", "SSE.Controllers.Toolbar.txtFunction_Coth": "Hiperbolikus kotangens függvény", "SSE.Controllers.Toolbar.txtFunction_Csc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Csch": "Hiperbolikus k<PERSON>ze<PERSON> f<PERSON>", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON> függvény", "SSE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sech": "Hiperbolikus <PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Hiperbolikus s<PERSON>", "SSE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON> függvény", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Hiperbolikus tangens függvény", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Custom", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Data and model", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Good, bad and neutral", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "No name", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Number format", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Themed cell styles", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Titles and headings", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Custom", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Light": "<PERSON>il<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Medium", "SSE.Controllers.Toolbar.txtInsertCells": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegral": "Integrál", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Differenci<PERSON><PERSON> theta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Differenciál x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Differen<PERSON><PERSON><PERSON> y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integrál", "SSE.Controllers.Toolbar.txtIntegralDouble": "Du<PERSON><PERSON> integr<PERSON>", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Du<PERSON><PERSON> integr<PERSON>", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Du<PERSON><PERSON> integr<PERSON>", "SSE.Controllers.Toolbar.txtIntegralOriented": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Felületi integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Felületi integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Felületi integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Mennyiség integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Mennyiség integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Mennyiség integrál", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integrál", "SSE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtInvalidRange": "HIBA! Érvénytelen cellatartomány", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Összegzés", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Összegzés", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Összegzés", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Termék", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Unió", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "V-alakú", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "V-alakú", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "V-alakú", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "V-alakú", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "V-alakú", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Metszet", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Metszet", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Metszet", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Metszet", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Metszet", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Termék", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Termék", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Termék", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Termék", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Termék", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Összegzés", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Összegzés", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Összegzés", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Összegzés", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Összegzés", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Unió", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Unió", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Unió", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Unió", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Unió", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON>it p<PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Maximum példa", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Limit", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Természetes logaritmus", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmus", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmus", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Maximum", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "SSE.Controllers.Toolbar.txtLockSort": "Adatok találhatók a kijel<PERSON><PERSON>, de nincs elege<PERSON> engedélye a cellák módosításához.<br>Szeretné folytatni a jelenlegi kijelöléssel?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 üres mátrix", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 üres mátrix", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 üres mátrix", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 üres mátrix", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON> m<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON><PERSON> m<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON><PERSON> m<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON> m<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 üres mátrix", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 üres mátrix", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 üres mátrix", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 üres mátrix", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Alapvonali pontok", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Midline dots", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Diagonal dots", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Vertical dots", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 identitás mátrix", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 identitásmátrix", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 identitásmátrix", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 identitásmátrix", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Jobbra-balra nyíl lent", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Jobbra-balra nyíl fent", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Balra mutató nyíl lent", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Balra mutató nyíl fent", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Jobbra nyíl lent", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Jobbra nyíl fent", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Kettőspont egyenlő", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta hozamok", "SSE.Controllers.Toolbar.txtOperator_Definition": "Definíció szerint egyenlő", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta egyenlő", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Jobbra-balra nyíl lent", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Jobbra-balra nyíl fent", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Balra mutató nyíl lent", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Balra mutató nyíl fent", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Jobbra nyíl lent", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Jobbra nyíl fent", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Egyenlő egyenlő", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plusz egyenlő", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Measured by", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Gyök", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Gyök", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Négyzetgyök fokkal", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Köbgyök", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Gyök fokai", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Négyzetgyök", "SSE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "Alsó index", "SSE.Controllers.Toolbar.txtScriptSubSup": "Alsó-felső index", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Bal alsó-felső index", "SSE.Controllers.Toolbar.txtScriptSup": "Felső index", "SSE.Controllers.Toolbar.txtSorting": "Rendez<PERSON>", "SSE.Controllers.Toolbar.txtSortSelected": "Kiválasztottak renezése", "SSE.Controllers.Toolbar.txtSymbol_about": "Megközelítőleg", "SSE.Controllers.Toolbar.txtSymbol_additional": "Kiegészítés", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ast": "<PERSON><PERSON><PERSON><PERSON> jel", "SSE.Controllers.Toolbar.txtSymbol_beta": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cap": "Metszet", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Köbgyök", "SSE.Controllers.Toolbar.txtSymbol_cdots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON>ius fok", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Megközelítőleg megegyezik", "SSE.Controllers.Toolbar.txtSymbol_cup": "Unió", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Le és jobbra á<PERSON> el<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_degree": "Fok", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON><PERSON><PERSON> jel", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON><PERSON> n<PERSON>l", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON> k<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epszilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "Egyenlő", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Megegyező", "SSE.Controllers.Toolbar.txtSymbol_eta": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_exists": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_factorial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Fahrenheit fok", "SSE.Controllers.Toolbar.txtSymbol_forall": "Mindenkinek", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON><PERSON><PERSON> vagy e<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_gg": "Sokkal nagyobb mint", "SSE.Controllers.Toolbar.txtSymbol_greater": "Nagyobb mint", "SSE.Controllers.Toolbar.txtSymbol_in": "Eleme", "SSE.Controllers.Toolbar.txtSymbol_inc": "Növekmény", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Végtelenség", "SSE.Controllers.Toolbar.txtSymbol_iota": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Balra nyíl", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Jobbra-balra nyíl", "SSE.Controllers.Toolbar.txtSymbol_leq": "Kisebb vagy egyenlő mint", "SSE.Controllers.Toolbar.txtSymbol_less": "Kevesebb mint", "SSE.Controllers.Toolbar.txtSymbol_ll": "Sokkal kisebb mint", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mű", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ni": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_not": "<PERSON><PERSON><PERSON> jel", "SSE.Controllers.Toolbar.txtSymbol_notexists": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_nu": "Nű", "SSE.Controllers.Toolbar.txtSymbol_o": "Omikron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Ómega", "SSE.Controllers.Toolbar.txtSymbol_partial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_percent": "Százalék", "SSE.Controllers.Toolbar.txtSymbol_phi": "Fí", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pí", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plusz", "SSE.Controllers.Toolbar.txtSymbol_pm": "<PERSON><PERSON> m<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_propto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_psi": "Pszí", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Negyedik gyök", "SSE.Controllers.Toolbar.txtSymbol_qed": "A bizonyítás vége", "SSE.Controllers.Toolbar.txtSymbol_rddots": "<PERSON><PERSON><PERSON><PERSON> diagon<PERSON><PERSON> el<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_rho": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Jobbra nyíl", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Szig<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Gyökjel", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_theta": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_times": "Többszörözés jele", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Üpszilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epszilon változat", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Fí változat", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Pí változat", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Ró v<PERSON>ltozat", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Szigma változat", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Théta változat", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Függőleges ellipszis", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Kszí", "SSE.Controllers.Toolbar.txtSymbol_zeta": "D<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Sö<PERSON>t táblázat stílus", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Világos táblá<PERSON>t stílus", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Közepes táblázat stílus", "SSE.Controllers.Toolbar.warnLongOperation": "A végrehajtani kívánt művelet meglehetősen sok időt vehet igénybe.<br>Biztosan folytatni kívánja?", "SSE.Controllers.Toolbar.warnMergeLostData": "Csak a bal felső cellából származó adatok maradnak az egyesített cellában.<br>Biztosan folytatni szeretné?", "SSE.Controllers.Toolbar.warnNoRecommended": "To create a chart, select the cells that contain the data you'd like to use.<br>If you have names for the rows and columns and you'd like use them as labels, include them in your selection.", "SSE.Controllers.Viewport.textFreezePanes": "Panelek rögzítése", "SSE.Controllers.Viewport.textFreezePanesShadow": "Mutassa a rögzített panelek árnyékát", "SSE.Controllers.Viewport.textHideFBar": "Függvény sáv elrej<PERSON>", "SSE.Controllers.Viewport.textHideGridlines": "Rácsvonalak el<PERSON>", "SSE.Controllers.Viewport.textHideHeadings": "<PERSON><PERSON><PERSON><PERSON>k elre<PERSON>", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "<PERSON><PERSON><PERSON>ó", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Numerikus adatok felismerésére használt beállítások", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Szöveg minősítő", "SSE.Views.AdvancedSeparatorDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(nincs)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Egy<PERSON>i <PERSON>", "SSE.Views.AutoFilterDialog.textAddSelection": "Az aktuális kijelölés hozzáadása a szűrőhöz", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Üresek}", "SSE.Views.AutoFilterDialog.textSelectAll": "Összes kiválasztása", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Minden keresési eredmény kiválasztása", "SSE.Views.AutoFilterDialog.textWarning": "Figyelmeztetés", "SSE.Views.AutoFilterDialog.txtAboveAve": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtAfter": "After...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "Minden dátum az időszakban", "SSE.Views.AutoFilterDialog.txtApril": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtAugust": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtBefore": "Before...", "SSE.Views.AutoFilterDialog.txtBegins": "Kezdődik...", "SSE.Views.AutoFilterDialog.txtBelowAve": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtBetween": "K<PERSON>z<PERSON>tt...", "SSE.Views.AutoFilterDialog.txtClear": "T<PERSON>r<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtContains": "Tartalmaz...", "SSE.Views.AutoFilterDialog.txtDateFilter": "Date filter", "SSE.Views.AutoFilterDialog.txtDecember": "December", "SSE.Views.AutoFilterDialog.txtEmpty": "Adja meg a cellaszűrőt", "SSE.Views.AutoFilterDialog.txtEnds": "záródik...", "SSE.Views.AutoFilterDialog.txtEquals": "Egyenlők...", "SSE.Views.AutoFilterDialog.txtFebruary": "February", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Cellaszín szerinti szűrés", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Karakterszín szerinti szűrés", "SSE.Views.AutoFilterDialog.txtGreater": "Nagyobb mint...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Nagyobb vagy egy<PERSON>l<PERSON>...", "SSE.Views.AutoFilterDialog.txtJanuary": "January", "SSE.Views.AutoFilterDialog.txtJuly": "July", "SSE.Views.AutoFilterDialog.txtJune": "June", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Címkeszűrő", "SSE.Views.AutoFilterDialog.txtLastMonth": "Last month", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Last quarter", "SSE.Views.AutoFilterDialog.txtLastWeek": "Last week", "SSE.Views.AutoFilterDialog.txtLastYear": "Last year", "SSE.Views.AutoFilterDialog.txtLess": "Kevesebb mint...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Kisebb vagy egyenlő mint...", "SSE.Views.AutoFilterDialog.txtMarch": "March", "SSE.Views.AutoFilterDialog.txtMay": "May", "SSE.Views.AutoFilterDialog.txtNextMonth": "Next month", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Next quarter", "SSE.Views.AutoFilterDialog.txtNextWeek": "Next week", "SSE.Views.AutoFilterDialog.txtNextYear": "Next year", "SSE.Views.AutoFilterDialog.txtNotBegins": "<PERSON><PERSON> k<PERSON>dődik...", "SSE.Views.AutoFilterDialog.txtNotBetween": "<PERSON><PERSON>...", "SSE.Views.AutoFilterDialog.txtNotContains": "nem tartalmaz...", "SSE.Views.AutoFilterDialog.txtNotEnds": "nem z<PERSON>l...", "SSE.Views.AutoFilterDialog.txtNotEquals": "<PERSON><PERSON>...", "SSE.Views.AutoFilterDialog.txtNovember": "November", "SSE.Views.AutoFilterDialog.txtNumFilter": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtOctober": "October", "SSE.Views.AutoFilterDialog.txtQuarter1": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Quarter 1", "SSE.Views.AutoFilterDialog.txtReapply": "Újra alkalmaz", "SSE.Views.AutoFilterDialog.txtSeptember": "September", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Cella színe szerint rendez", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Karakterszín szerint rendez", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "A legnagyobbtól a legkisebbig rendez", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Legkisebbtől legnagyobbig rendez", "SSE.Views.AutoFilterDialog.txtSortOption": "További rendezési lehetőségek...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Szöveg szűrő", "SSE.Views.AutoFilterDialog.txtThisMonth": "This Month", "SSE.Views.AutoFilterDialog.txtThisQuarter": "This quarter", "SSE.Views.AutoFilterDialog.txtThisWeek": "This week", "SSE.Views.AutoFilterDialog.txtThisYear": "This Year", "SSE.Views.AutoFilterDialog.txtTitle": "Szűrő", "SSE.Views.AutoFilterDialog.txtToday": "Today", "SSE.Views.AutoFilterDialog.txtTomorrow": "Tomorrow", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Értékszűrő", "SSE.Views.AutoFilterDialog.txtYearToDate": "Year to date", "SSE.Views.AutoFilterDialog.txtYesterday": "Yesterday", "SSE.Views.AutoFilterDialog.warnFilterError": "Legalább egy mezőre van szükség az értékterületen az értékszűrő alkalmazásához.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Legalább egy értéket ki kell választania", "SSE.Views.CellEditor.textManager": "Név kezelő", "SSE.Views.CellEditor.tipFormula": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellRangeDialog.errorMaxRows": "HIBA! Az adatsorok maximális száma diagramonként 255", "SSE.Views.CellRangeDialog.errorStockChart": "Helytelen sor sorrend. Tőzsdei diagram létrehozásához az adatokat az alábbi sorrendben vigye fel:<br>nyi<PERSON><PERSON> ár, maximum ár, minimum ár, z<PERSON><PERSON><PERSON> ár.", "SSE.Views.CellRangeDialog.txtEmpty": "Ez egy szükséges mező", "SSE.Views.CellRangeDialog.txtInvalidRange": "HIBA! Érvénytelen cellatartomány", "SSE.Views.CellRangeDialog.txtTitle": "Adattartomány kiválasztása", "SSE.Views.CellSettings.strShrink": "<PERSON><PERSON><PERSON>od<PERSON>, hogy elférjen", "SSE.Views.CellSettings.strWrap": "Szövegtördelés", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "Háttérsz<PERSON>", "SSE.Views.CellSettings.textBackground": "Háttérsz<PERSON>", "SSE.Views.CellSettings.textBorderColor": "Szín", "SSE.Views.CellSettings.textBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textClearRule": "Szabályok törlése", "SSE.Views.CellSettings.textColor": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textColorScales": "Színskálák", "SSE.Views.CellSettings.textCondFormat": "Feltételes formázás", "SSE.Views.CellSettings.textControl": "Szövegvezérlő", "SSE.Views.CellSettings.textDataBars": "Adatsávok", "SSE.Views.CellSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textFill": "Kitöltés", "SSE.Views.CellSettings.textForeground": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textGradient": "Színátmeneti pontok", "SSE.Views.CellSettings.textGradientColor": "Szín", "SSE.Views.CellSettings.textGradientFill": "Színátmenetes kitöltés", "SSE.Views.CellSettings.textIndent": "Behúzás", "SSE.Views.CellSettings.textItems": "Tételek", "SSE.Views.CellSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textManageRule": "Szabályok kezelése", "SSE.Views.CellSettings.textNewRule": "<PERSON><PERSON>", "SSE.Views.CellSettings.textNoFill": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textOrientation": "Szövegirány", "SSE.Views.CellSettings.textPattern": "Minta", "SSE.Views.CellSettings.textPatternFill": "Minta", "SSE.Views.CellSettings.textPosition": "Pozíció", "SSE.Views.CellSettings.textRadial": "Sugárirányú", "SSE.Views.CellSettings.textSelectBorders": "Válassza ki a s<PERSON><PERSON><PERSON><PERSON><PERSON>, amelyeket módosí<PERSON> szeret<PERSON>, a fenti stílus k<PERSON>lasztásával", "SSE.Views.CellSettings.textSelection": "Jelenlegi kijelölésből", "SSE.Views.CellSettings.textThisPivot": "Ebből a Pivot táblázatból", "SSE.Views.CellSettings.textThisSheet": "Ebből a munkalapból", "SSE.Views.CellSettings.textThisTable": "Ebből a táblázatból", "SSE.Views.CellSettings.tipAddGradientPoint": "Színátmeneti pont hozzáadása", "SSE.Views.CellSettings.tipAll": "<PERSON><PERSON><PERSON><PERSON> szegély és minden belső vonal beállítása", "SSE.Views.CellSettings.tipBottom": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.CellSettings.tipDiagD": "<PERSON><PERSON><PERSON> le<PERSON> s<PERSON>", "SSE.Views.CellSettings.tipDiagU": "<PERSON><PERSON><PERSON> f<PERSON> s<PERSON>", "SSE.Views.CellSettings.tipInner": "Csak belső vonalak beállítása", "SSE.Views.CellSettings.tipInnerHor": "Csak vízszintes belső vonalak beállítása", "SSE.Views.CellSettings.tipInnerVert": "Csak függőleges belső vonalak beállítsa", "SSE.Views.CellSettings.tipLeft": "<PERSON><PERSON><PERSON>, bal <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.tipNone": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.tipOuter": "Csak külső szegély be<PERSON>llítása", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Színátmenet pont eltávolítása", "SSE.Views.CellSettings.tipRight": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.tipTop": "Csak <PERSON>, felső szegély beállítása", "SSE.Views.ChartDataDialog.errorInFormula": "<PERSON><PERSON> van a megadott függvényben.", "SSE.Views.ChartDataDialog.errorInvalidReference": "A hivatkozás érvénytelen. Egy nyitott munkalapra kell hivatkozni.", "SSE.Views.ChartDataDialog.errorMaxPoints": "A soronkénti maximális pontszám diagramonként 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Diagramonként az adatsorok maximális száma 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "A hivatkozás érvénytelen. A címekre, <PERSON>rt<PERSON><PERSON>k<PERSON>, méretekre vagy adatcímkék<PERSON> vonat<PERSON><PERSON>ó hivatkozásoknak egyetlen cellának, sornak vagy oszlopnak kell lenniük.", "SSE.Views.ChartDataDialog.errorNoValues": "Diagram létrehozásához a sorozatnak tartalmaznia kell legalább egy értéket.", "SSE.Views.ChartDataDialog.errorStockChart": "Helytelen sor sorrend. Részvénydiagram létrehozásához az adatokat az alábbi sorrendben vigye fel:<br>nyi<PERSON><PERSON> ár, maximum ár, minimum ár, z<PERSON><PERSON><PERSON> ár.", "SSE.Views.ChartDataDialog.textAdd": "Hozzáadás", "SSE.Views.ChartDataDialog.textCategory": "Vízszintes (kategória) tengelycímkék", "SSE.Views.ChartDataDialog.textData": "<PERSON><PERSON><PERSON> ad<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textDelete": "Eltávolítás", "SSE.Views.ChartDataDialog.textDown": "Le", "SSE.Views.ChartDataDialog.textEdit": "Szerkesztés", "SSE.Views.ChartDataDialog.textInvalidRange": "Érvénytelen cellatartomány", "SSE.Views.ChartDataDialog.textSelectData": "Adatok kiválasztása", "SSE.Views.ChartDataDialog.textSeries": "Jelma<PERSON>ar<PERSON><PERSON><PERSON> (sorozat)", "SSE.Views.ChartDataDialog.textSwitch": "Váltsa a sort/oszlopot", "SSE.Views.ChartDataDialog.textTitle": "<PERSON><PERSON><PERSON> ad<PERSON>k", "SSE.Views.ChartDataDialog.textUp": "<PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.errorInFormula": "<PERSON><PERSON> van a megadott függvényben.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "A hivatkozás érvénytelen. Egy nyitott munkalapra kell hivatkozni.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "A soronkénti maximális pontszám diagramonként 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Diagramonként az adatsorok maximális száma 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "A hivatkozás érvénytelen. A címekre, <PERSON>rt<PERSON><PERSON>k<PERSON>, méretekre vagy adatcímkék<PERSON> vonat<PERSON><PERSON>ó hivatkozásoknak egyetlen cellának, sornak vagy oszlopnak kell lenniük.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Diagram létrehozásához a sorozatnak tartalmaznia kell legalább egy értéket.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Helytelen sor sorrend. Részvénydiagram létrehozásához az adatokat az alábbi sorrendben vigye fel:<br>nyi<PERSON><PERSON> ár, maximum ár, minimum ár, z<PERSON><PERSON><PERSON> ár.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Érvénytelen cellatartomány", "SSE.Views.ChartDataRangeDialog.textSelectData": "Adatok kiválasztása", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "<PERSON><PERSON><PERSON> c<PERSON> tart<PERSON>", "SSE.Views.ChartDataRangeDialog.txtChoose": "Válasszon tartományt", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "So<PERSON><PERSON><PERSON>név", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "So<PERSON>zat szerkesztése", "SSE.Views.ChartDataRangeDialog.txtValues": "<PERSON>rté<PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtXValues": "X értékek", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y értékek", "SSE.Views.ChartSettings.errorMaxRows": "Az adatsorok maximális száma diagramonként 255.", "SSE.Views.ChartSettings.strLineWeight": "<PERSON><PERSON>", "SSE.Views.ChartSettings.strSparkColor": "Szín", "SSE.Views.ChartSettings.strTemplate": "Sablon", "SSE.Views.ChartSettings.text3dDepth": "Depth (% of base)", "SSE.Views.ChartSettings.text3dHeight": "Height (% of base)", "SSE.Views.ChartSettings.text3dRotation": "3D Forgatás", "SSE.Views.ChartSettings.textAdvanced": "Speciális beállítások megjelenítése", "SSE.Views.ChartSettings.textAutoscale": "Autoscale", "SSE.Views.ChartSettings.textBorderSizeErr": "A megadott érték helytelen.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy számértéket 0 és 1584 között", "SSE.Views.ChartSettings.textChangeType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textChartType": "Diagramtípus m<PERSON>í<PERSON>", "SSE.Views.ChartSettings.textDefault": "Default Rotation", "SSE.Views.ChartSettings.textDown": "Down", "SSE.Views.ChartSettings.textEditData": "Adatok és he<PERSON>", "SSE.Views.ChartSettings.textFirstPoint": "Első pont", "SSE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textHighPoint": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textLastPoint": "Utolsó pont", "SSE.Views.ChartSettings.textLeft": "Left", "SSE.Views.ChartSettings.textLowPoint": "Alacsony pont", "SSE.Views.ChartSettings.textMarkers": "Jel<PERSON>lők", "SSE.Views.ChartSettings.textNarrow": "Narrow field of view", "SSE.Views.ChartSettings.textNegativePoint": "Negatív pont", "SSE.Views.ChartSettings.textPerspective": "Perspective", "SSE.Views.ChartSettings.textRanges": "Adattartomány", "SSE.Views.ChartSettings.textRight": "Right", "SSE.Views.ChartSettings.textRightAngle": "Right angle axes", "SSE.Views.ChartSettings.textSelectData": "Adatok kiválasztása", "SSE.Views.ChartSettings.textShow": "Mutat", "SSE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSwitch": "Sor/oszlop v<PERSON>", "SSE.Views.ChartSettings.textType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textUp": "Up", "SSE.Views.ChartSettings.textWiden": "Widen field of view", "SSE.Views.ChartSettings.textWidth": "Szélesség", "SSE.Views.ChartSettings.textX": "X rotation", "SSE.Views.ChartSettings.textY": "Y rotation", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "HIBA! A soronkénti maximális pontszám a diagramon 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "HIBA! Az adatsorok maximális száma diagramonként 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Helytelen sor sorrend. Részvénydiagram létrehozásához az adatokat az alábbi sorrendben vigye fel:<br>nyi<PERSON><PERSON> ár, maximum ár, minimum ár, z<PERSON><PERSON><PERSON> ár.", "SSE.Views.ChartSettingsDlg.textAbsolute": "<PERSON><PERSON>s moz<PERSON> vagy méretezés cellákkal", "SSE.Views.ChartSettingsDlg.textAlt": "Alternatív szöveg", "SSE.Views.ChartSettingsDlg.textAltDescription": "Le<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAltTip": "A vizuális objektumok alternatív szövegalapú ábrázolása, amely a látás vagy kognitív károsodottak számára is o<PERSON><PERSON><PERSON><PERSON>, hogy segítsen nekik job<PERSON> me<PERSON>, hogy milyen <PERSON>, al<PERSON><PERSON><PERSON>, diagram vagy táblázat látható.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Cím", "SSE.Views.ChartSettingsDlg.textAuto": "Automatikus", "SSE.Views.ChartSettingsDlg.textAutoEach": "Automatikusan mindegyikhez", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Tengelykeresztek", "SSE.Views.ChartSettingsDlg.textAxisOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisPos": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Cím", "SSE.Views.ChartSettingsDlg.textBase": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Jelölések között", "SSE.Views.ChartSettingsDlg.textBillions": "Milliárdok", "SSE.Views.ChartSettingsDlg.textBottom": "Alsó", "SSE.Views.ChartSettingsDlg.textCategoryName": "Kategória név", "SSE.Views.ChartSettingsDlg.textCenter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Diagramelemek és<br>Diagrammagyar<PERSON><PERSON>t", "SSE.Views.ChartSettingsDlg.textChartTitle": "Diagram neve", "SSE.Views.ChartSettingsDlg.textCross": "Kereszt", "SSE.Views.ChartSettingsDlg.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDataColumns": "oszlopokban", "SSE.Views.ChartSettingsDlg.textDataLabels": "Adatcímkék", "SSE.Views.ChartSettingsDlg.textDataRows": "so<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Jelmagyar<PERSON><PERSON><PERSON> me<PERSON>", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Rejtett és üres cellák", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Adatpontok összekötése vonalakkal", "SSE.Views.ChartSettingsDlg.textFit": "Sz<PERSON>lességhez <PERSON>", "SSE.Views.ChartSettingsDlg.textFixed": "R<PERSON><PERSON><PERSON><PERSON><PERSON>tt", "SSE.Views.ChartSettingsDlg.textFormat": "Címkeformátum", "SSE.Views.ChartSettingsDlg.textGaps": "Kihagyások", "SSE.Views.ChartSettingsDlg.textGridLines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGroup": "Értékgörbék csoportosítása", "SSE.Views.ChartSettingsDlg.textHide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHideAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHigh": "Magas", "SSE.Views.ChartSettingsDlg.textHorAxis": "Vízszintes tengely", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Másodlagos vízszintes tengely", "SSE.Views.ChartSettingsDlg.textHorizontal": "Vízszintes", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Száz", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "-ben", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Alul belül", "SSE.Views.ChartSettingsDlg.textInnerTop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textInvalidRange": "HIBA! Érvénytelen cellatartomány", "SSE.Views.ChartSettingsDlg.textLabelDist": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLabelInterval": "A címkék közötti intervallum", "SSE.Views.ChartSettingsDlg.textLabelOptions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLabelPos": "Címke <PERSON>", "SSE.Views.ChartSettingsDlg.textLayout": "Elrendezés", "SSE.Views.ChartSettingsDlg.textLeft": "<PERSON>l", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Bal á<PERSON>", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Alsó", "SSE.Views.ChartSettingsDlg.textLegendLeft": "<PERSON>l", "SSE.Views.ChartSettingsDlg.textLegendPos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendRight": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendTop": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLines": "<PERSON>ala<PERSON>", "SSE.Views.ChartSettingsDlg.textLocationRange": "Pozíció tartomány", "SSE.Views.ChartSettingsDlg.textLogScale": "Logaritmikus skála", "SSE.Views.ChartSettingsDlg.textLow": "Alacsony", "SSE.Views.ChartSettingsDlg.textMajor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorMinor": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> kisebb", "SSE.Views.ChartSettingsDlg.textMajorType": "<PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.ChartSettingsDlg.textManual": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarkers": "Jel<PERSON>lők", "SSE.Views.ChartSettingsDlg.textMarksInterval": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMaxValue": "Maximum érték", "SSE.Views.ChartSettingsDlg.textMillions": "Milliók", "SSE.Views.ChartSettingsDlg.textMinor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinorType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinValue": "Minimum érték", "SSE.Views.ChartSettingsDlg.textNextToAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNoOverlay": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textOneCell": "Mozgatás cellákkal méretezés nélkül", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textOut": "<PERSON>", "SSE.Views.ChartSettingsDlg.textOuterTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> fent", "SSE.Views.ChartSettingsDlg.textOverlay": "Átfedés", "SSE.Views.ChartSettingsDlg.textReverse": "Értékek fordított sorrendben", "SSE.Views.ChartSettingsDlg.textReverseOrder": "<PERSON><PERSON><PERSON><PERSON> sorrend", "SSE.Views.ChartSettingsDlg.textRight": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRightOverlay": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRotated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSameAll": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSelectData": "Adatok kiválasztása", "SSE.Views.ChartSettingsDlg.textSeparator": "Elválasztó adatcí<PERSON>ez", "SSE.Views.ChartSettingsDlg.textSeriesName": "Sorozat név", "SSE.Views.ChartSettingsDlg.textShow": "Mutat", "SSE.Views.ChartSettingsDlg.textShowBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowData": "Adatok megjelenítése a rejtett sorokban és oszlopokban", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Az üres cellák megjelenítése", "SSE.Views.ChartSettingsDlg.textShowEquation": "Display equation on chart", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowValues": "Grafikonérték megjelenítése", "SSE.Views.ChartSettingsDlg.textSingle": "Egyetlen értékgörbe", "SSE.Views.ChartSettingsDlg.textSmooth": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSnap": "Csatlakozás a cellához", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Értékgörbe tartományok", "SSE.Views.ChartSettingsDlg.textStraight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTickOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTitle": "Diagram - Spec<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Értékgörbe - <PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTop": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Trendline options", "SSE.Views.ChartSettingsDlg.textTrillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTwoCell": "Mozgatás és méretezés cellákkal", "SSE.Views.ChartSettingsDlg.textType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTypeData": "Típus és adat", "SSE.Views.ChartSettingsDlg.textUnits": "Egységek megjelenítése", "SSE.Views.ChartSettingsDlg.textValue": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxis": "Függőleges tengely", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Másodlagos függőleges tengely", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "<PERSON> tengely címe", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "<PERSON> tengely címe", "SSE.Views.ChartSettingsDlg.textZero": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.txtEmpty": "Ez egy szükséges mező", "SSE.Views.ChartTypeDialog.errorComboSeries": "Kombinált diagram létrehozásához válasszon legalább két adatsort.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "A kiválasztott diagramtípus megköveteli azt a másodlagos tengelyt, amelyet egy meglévő diagram használ. Válasszon másik diagramtípust.", "SSE.Views.ChartTypeDialog.textSecondary": "Másod<PERSON><PERSON> tengely", "SSE.Views.ChartTypeDialog.textSeries": "Sorozatok", "SSE.Views.ChartTypeDialog.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textTitle": "Diagram t<PERSON><PERSON>a", "SSE.Views.ChartTypeDialog.textType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartWizardDialog.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartWizardDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order: opening price, max price, min price, closing price.", "SSE.Views.ChartWizardDialog.textRecommended": "Recommended", "SSE.Views.ChartWizardDialog.textSecondary": "Secondary Axis", "SSE.Views.ChartWizardDialog.textSeries": "Series", "SSE.Views.ChartWizardDialog.textTitle": "Insert Chart", "SSE.Views.ChartWizardDialog.textTitleChange": "Change chart type", "SSE.Views.ChartWizardDialog.textType": "Type", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Choose the chart type and axis for your data series", "SSE.Views.CreatePivotDialog.textDataRange": "Forrásadatok tartománya", "SSE.Views.CreatePivotDialog.textDestination": "Válassza ki a tá<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CreatePivotDialog.textExist": "<PERSON><PERSON><PERSON><PERSON> munkalap", "SSE.Views.CreatePivotDialog.textInvalidRange": "Érvénytelen cellatartomány", "SSE.Views.CreatePivotDialog.textNew": "<PERSON><PERSON>", "SSE.Views.CreatePivotDialog.textSelectData": "Adatok kiválasztása", "SSE.Views.CreatePivotDialog.textTitle": "Kimutatás tábla létrehozása", "SSE.Views.CreatePivotDialog.txtEmpty": "Ez egy szükséges mező", "SSE.Views.CreateSparklineDialog.textDataRange": "Forrásadatok tartománya", "SSE.Views.CreateSparklineDialog.textDestination": "Válassza ki, hol használja az értékgörbéket", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Érvénytelen cellatartomány", "SSE.Views.CreateSparklineDialog.textSelectData": "Adatok kiválasztása", "SSE.Views.CreateSparklineDialog.textTitle": "Értékgörbék létrehozása", "SSE.Views.CreateSparklineDialog.txtEmpty": "Ez egy szükséges mező", "SSE.Views.DataTab.capBtnGroup": "Csoport", "SSE.Views.DataTab.capBtnTextCustomSort": "Egyéni rendezés", "SSE.Views.DataTab.capBtnTextDataValidation": "Adatellenőrzés", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Ismétlődések eltávolítása", "SSE.Views.DataTab.capBtnTextToCol": "Szöveg oszlopokká", "SSE.Views.DataTab.capBtnUngroup": "Csoport szétválasztása", "SSE.Views.DataTab.capDataExternalLinks": "External Links", "SSE.Views.DataTab.capDataFromText": "Adatok beszerzése", "SSE.Views.DataTab.capGoalSeek": "Goal Seek", "SSE.Views.DataTab.mniFromFile": "Helyi TXT/CVS fájlból", "SSE.Views.DataTab.mniFromUrl": "Ebből a TXT/CVS webcímből", "SSE.Views.DataTab.mniFromXMLFile": "From Local XML", "SSE.Views.DataTab.textBelow": "Összefoglaló oszlopok a részletek alatt", "SSE.Views.DataTab.textClear": "Körvonal törlése", "SSE.Views.DataTab.textColumns": "Oszlopok szétválasztása", "SSE.Views.DataTab.textGroupColumns": "Oszlopok csoportosítása", "SSE.Views.DataTab.textGroupRows": "Sorok csoportosítása", "SSE.Views.DataTab.textRightOf": "Összefoglaló oszlopok a részletek jobb oldalán", "SSE.Views.DataTab.textRows": "<PERSON><PERSON>választása", "SSE.Views.DataTab.tipCustomSort": "Egyéni rendezés", "SSE.Views.DataTab.tipDataFromText": "Adatok beszerzése TXT/CVS fájlból", "SSE.Views.DataTab.tipDataValidation": "Adatellenőrzés", "SSE.Views.DataTab.tipExternalLinks": "View other files this spreadsheet is linked to", "SSE.Views.DataTab.tipGoalSeek": "Find the right input for the value you want", "SSE.Views.DataTab.tipGroup": "Cellatartomány csoportosítása", "SSE.Views.DataTab.tipRemDuplicates": "Ismétlődő sorok eltávolítása egy munkalapról", "SSE.Views.DataTab.tipToColumns": "Cella szöveg szétválasztása oszlopokra", "SSE.Views.DataTab.tipUngroup": "Cellatartomány szétválasztása", "SSE.Views.DataValidationDialog.errorFormula": "Az érték jelenleg hibára értékelődik ki. Folytassuk?", "SSE.Views.DataValidationDialog.errorInvalid": "A(z) \"{0}\" mezőbe írt érték érvénytelen.", "SSE.Views.DataValidationDialog.errorInvalidDate": "A(z) \"{0}\" mezőbe írt dátum érvénytelen.", "SSE.Views.DataValidationDialog.errorInvalidList": "A listaforrásnak elhatárolt listának vagy egy sorra vagy oszlopra való hivatkozásnak kell lennie.", "SSE.Views.DataValidationDialog.errorInvalidTime": "A(z) \"{0}\" mezőbe írt idő érvénytelen.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "A(z) \"{1}\" mezőnek nagyobbnak vagy egyenlőnek kell lennie a(z) \"{0}\" mezővel.", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "<PERSON> kell adnia egy értéket mind a(z) \"{0}\", mind a(z) \"{1}\" mez<PERSON>ben.", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Értéket kell megadnia a(z) \"{0}\" mezőben.", "SSE.Views.DataValidationDialog.errorNamedRange": "A megadott nevű tartomány nem található.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Negatív értékeket nem lehet használni \"{0}\" feltételekben.", "SSE.Views.DataValidationDialog.errorNotNumeric": "A(z) \"{0}\" mezőnek numerikus értéknek, numerikus kifejezésnek kell lennie, vagy egy numerikus értéket tartalmazó cellára kell hivatkoznia.", "SSE.Views.DataValidationDialog.strError": "Hiba figyelmeztetés", "SSE.Views.DataValidationDialog.strInput": "Bemeneti üzenet", "SSE.Views.DataValidationDialog.strSettings": "Beállítások", "SSE.Views.DataValidationDialog.textAlert": "Figyelmeztetés", "SSE.Views.DataValidationDialog.textAllow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textApply": "A módosításokat alkalmazza az összes többi cellára ugyanazokkal a beállításokkal.", "SSE.Views.DataValidationDialog.textCellSelected": "Amikor a cella ki van választva, j<PERSON><PERSON><PERSON><PERSON>e meg ezt a bemeneti üzenetet", "SSE.Views.DataValidationDialog.textCompare": "Összehas<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textData": "Ada<PERSON>", "SSE.Views.DataValidationDialog.textEndDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndTime": "Befejezés ideje", "SSE.Views.DataValidationDialog.textError": "Hibaüzenet", "SSE.Views.DataValidationDialog.textFormula": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textIgnore": "Üresek figyelmen kívül hagyása", "SSE.Views.DataValidationDialog.textInput": "Bemeneti üzenet", "SSE.Views.DataValidationDialog.textMax": "Maximum", "SSE.Views.DataValidationDialog.textMessage": "Üzenet", "SSE.Views.DataValidationDialog.textMin": "Minimum", "SSE.Views.DataValidationDialog.textSelectData": "Adatok kiválasztása", "SSE.Views.DataValidationDialog.textShowDropDown": "Legördülő lista megjelenítése a cellában", "SSE.Views.DataValidationDialog.textShowError": "Hiba figyelmeztetés megjelenítése érvénytelen adatok megadása után", "SSE.Views.DataValidationDialog.textShowInput": "Bemeneti üzenet megjelenítése, amikor a cella ki van j<PERSON>ölve", "SSE.Views.DataValidationDialog.textSource": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStartDate": "Kezdés <PERSON>", "SSE.Views.DataValidationDialog.textStartTime": "Kezdés ideje", "SSE.Views.DataValidationDialog.textStop": "Stop", "SSE.Views.DataValidationDialog.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textTitle": "Cím", "SSE.Views.DataValidationDialog.textUserEnters": "Amikor a felhasználó érvénytelen adatot ad meg, jelenítse meg ezt a figyelmeztetést", "SSE.Views.DataValidationDialog.txtAny": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtBetween": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtElTime": "Eltelt idő", "SSE.Views.DataValidationDialog.txtEndDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtEndTime": "Befejezés ideje", "SSE.Views.DataValidationDialog.txtEqual": "egyenlő", "SSE.Views.DataValidationDialog.txtGreaterThan": "nagyobb mint", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "nagy<PERSON><PERSON> vagy e<PERSON>", "SSE.Views.DataValidationDialog.txtLength": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtLessThan": "kevesebb mint", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "kisebb vagy egyenlő mint", "SSE.Views.DataValidationDialog.txtList": "Lista", "SSE.Views.DataValidationDialog.txtNotBetween": "nem k<PERSON>", "SSE.Views.DataValidationDialog.txtNotEqual": "nem e<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtOther": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtStartDate": "Kezdés <PERSON>", "SSE.Views.DataValidationDialog.txtStartTime": "Kezdés ideje", "SSE.Views.DataValidationDialog.txtTextLength": "Szöveg hossza", "SSE.Views.DataValidationDialog.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtWhole": "<PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capAnd": "és", "SSE.Views.DigitalFilterDialog.capCondition1": "egyenlők", "SSE.Views.DigitalFilterDialog.capCondition10": "nem <PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition11": "tartalmaz", "SSE.Views.DigitalFilterDialog.capCondition12": "nem tartalmaz", "SSE.Views.DigitalFilterDialog.capCondition2": "<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition3": "nagyobb mint", "SSE.Views.DigitalFilterDialog.capCondition30": "is after", "SSE.Views.DigitalFilterDialog.capCondition4": "nagy<PERSON><PERSON> vagy e<PERSON>", "SSE.Views.DigitalFilterDialog.capCondition40": "is after or equal to", "SSE.Views.DigitalFilterDialog.capCondition5": "kisebb mint", "SSE.Views.DigitalFilterDialog.capCondition50": "is before", "SSE.Views.DigitalFilterDialog.capCondition6": "kisebb vagy egyenlő mint", "SSE.Views.DigitalFilterDialog.capCondition60": "is before or equal to", "SSE.Views.DigitalFilterDialog.capCondition7": "kezdődik", "SSE.Views.DigitalFilterDialog.capCondition8": "nem azzal k<PERSON>", "SSE.Views.DigitalFilterDialog.capCondition9": "z<PERSON>r<PERSON><PERSON>k", "SSE.Views.DigitalFilterDialog.capOr": "vagy", "SSE.Views.DigitalFilterDialog.textNoFilter": "ninc<PERSON>", "SSE.Views.DigitalFilterDialog.textShowRows": "<PERSON><PERSON> me<PERSON> ahol", "SSE.Views.DigitalFilterDialog.textUse1": "Használjon kérdőjelet egyetlen karakter bemutatásához", "SSE.Views.DigitalFilterDialog.textUse2": "Használja a * -ot a karakterek sorozatának megjelenítéséhez", "SSE.Views.DigitalFilterDialog.txtSelectDate": "Select date", "SSE.Views.DigitalFilterDialog.txtTitle": "Egy<PERSON>i <PERSON>", "SSE.Views.DocumentHolder.advancedEquationText": "Equation settings", "SSE.Views.DocumentHolder.advancedImgText": "<PERSON><PERSON><PERSON> k<PERSON>állí<PERSON>ok", "SSE.Views.DocumentHolder.advancedShapeText": "<PERSON><PERSON><PERSON>llí<PERSON>ás<PERSON>", "SSE.Views.DocumentHolder.advancedSlicerText": "Elválasztó speciális <PERSON>ll<PERSON>", "SSE.Views.DocumentHolder.allLinearText": "Mind - <PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.allProfText": "Minden - Professzionális", "SSE.Views.DocumentHolder.bottomCellText": "Alsóhoz igazít", "SSE.Views.DocumentHolder.bulletsText": "Felsorolás és számozás", "SSE.Views.DocumentHolder.centerCellText": "Középhez igazít", "SSE.Views.DocumentHolder.chartDataText": "Grafikonadatok kiválasztása", "SSE.Views.DocumentHolder.chartText": "Speciális diagram beállítások", "SSE.Views.DocumentHolder.chartTypeText": "Diagram tí<PERSON> módosít<PERSON>", "SSE.Views.DocumentHolder.currLinearText": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.currProfText": "Current - Professional", "SSE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteRowText": "Sor", "SSE.Views.DocumentHolder.deleteTableText": "T<PERSON>b<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.direct270Text": "Szöveg elforgatása felfelé", "SSE.Views.DocumentHolder.direct90Text": "Szöveg elforgatás<PERSON>", "SSE.Views.DocumentHolder.directHText": "Vízszintes", "SSE.Views.DocumentHolder.directionText": "Szövegirány", "SSE.Views.DocumentHolder.editChartText": "Adat szerkesztése", "SSE.Views.DocumentHolder.editHyperlinkText": "Hivatkozás szerkesztése", "SSE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "SSE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON>r", "SSE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.removeHyperlinkText": "Hivatkozás eltávolítása", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectDataText": "Oszlop adat", "SSE.Views.DocumentHolder.selectRowText": "Sor", "SSE.Views.DocumentHolder.selectTableText": "T<PERSON>b<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.showEqToolbar": "Show Equation Toolbar", "SSE.Views.DocumentHolder.strDelete": "Aláírás eltávolítása", "SSE.Views.DocumentHolder.strDetails": "Aláírás r<PERSON>zletek", "SSE.Views.DocumentHolder.strSetup": "Aláí<PERSON><PERSON>", "SSE.Views.DocumentHolder.strSign": "Aláír", "SSE.Views.DocumentHolder.textAlign": "Igazít", "SSE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON><PERSON>z", "SSE.Views.DocumentHolder.textArrangeFront": "Előtérbe hoz", "SSE.Views.DocumentHolder.textAverage": "Átlag", "SSE.Views.DocumentHolder.textBullets": "Pontok", "SSE.Views.DocumentHolder.textCopyCells": "Copy cells", "SSE.Views.DocumentHolder.textCount": "Dar<PERSON>", "SSE.Views.DocumentHolder.textCrop": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFill": "Kitöltés", "SSE.Views.DocumentHolder.textCropFit": "Illesztés", "SSE.Views.DocumentHolder.textEditPoints": "Pontok Szerkesztése", "SSE.Views.DocumentHolder.textEntriesList": "Lenyíló listából választás", "SSE.Views.DocumentHolder.textFillDays": "Fill days", "SSE.Views.DocumentHolder.textFillFormatOnly": "Fill formatting only", "SSE.Views.DocumentHolder.textFillMonths": "Fill months", "SSE.Views.DocumentHolder.textFillSeries": "Fill series", "SSE.Views.DocumentHolder.textFillWeekdays": "Fill weekdays", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Fill without formatting", "SSE.Views.DocumentHolder.textFillYears": "Fill years", "SSE.Views.DocumentHolder.textFlashFill": "Flash fill", "SSE.Views.DocumentHolder.textFlipH": "Vízszintesen tükröz", "SSE.Views.DocumentHolder.textFlipV": "Függőlegesen tükröz", "SSE.Views.DocumentHolder.textFreezePanes": "Panelek rögzítése", "SSE.Views.DocumentHolder.textFromFile": "Fájlból", "SSE.Views.DocumentHolder.textFromStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textFromUrl": "URL-ből", "SSE.Views.DocumentHolder.textGrowthTrend": "Growth trend", "SSE.Views.DocumentHolder.textLinearTrend": "Linear trend", "SSE.Views.DocumentHolder.textListSettings": "Lista beállí<PERSON>ok", "SSE.Views.DocumentHolder.textMacro": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textMax": "Maximum", "SSE.Views.DocumentHolder.textMin": "Minimum", "SSE.Views.DocumentHolder.textMore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textMoreFormats": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textNone": "nincs", "SSE.Views.DocumentHolder.textNumbering": "Számozás", "SSE.Views.DocumentHolder.textReplace": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textResetCrop": "Reset crop", "SSE.Views.DocumentHolder.textRotate": "Forgatás", "SSE.Views.DocumentHolder.textRotate270": "Elforgat balra 90 fokkal", "SSE.Views.DocumentHolder.textRotate90": "Elforgat jobbra 90 fokkal", "SSE.Views.DocumentHolder.textSaveAsPicture": "Save as picture", "SSE.Views.DocumentHolder.textSeries": "Series", "SSE.Views.DocumentHolder.textShapeAlignBottom": "<PERSON><PERSON>ra rendez", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Középre igazít", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Balra rendez", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Középhez igazít", "SSE.Views.DocumentHolder.textShapeAlignRight": "Jobbra igazít", "SSE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON><PERSON><PERSON>re rendez ", "SSE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "Összeg", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "Rögzítés eltávolítása", "SSE.Views.DocumentHolder.textVar": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersArrow": "Nyílazás", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersDash": "Köt<PERSON><PERSON>l f<PERSON>lek", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersFRound": "Tömör kör <PERSON>", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Tömör szögletes felsorolásjelek", "SSE.Views.DocumentHolder.tipMarkersHRound": "Üreges kör fels<PERSON>lek", "SSE.Views.DocumentHolder.tipMarkersStar": "Csillag felsorolásjelek", "SSE.Views.DocumentHolder.topCellText": "<PERSON><PERSON><PERSON><PERSON>re rendez ", "SSE.Views.DocumentHolder.txtAccounting": "Könyvelés", "SSE.Views.DocumentHolder.txtAddComment": "Megjegyzés hozzáadása", "SSE.Views.DocumentHolder.txtAddNamedRange": "Név megadása", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "Növekvő", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Automatikus oszlopszélesség illesztése", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Automatikus sormagasság illesztése", "SSE.Views.DocumentHolder.txtAverage": "Average", "SSE.Views.DocumentHolder.txtCellFormat": "Format cells", "SSE.Views.DocumentHolder.txtClear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearAll": "Összes", "SSE.Views.DocumentHolder.txtClearComments": "Megjegyzések", "SSE.Views.DocumentHolder.txtClearFormat": "Formátum", "SSE.Views.DocumentHolder.txtClearHyper": "Hivatkozások", "SSE.Views.DocumentHolder.txtClearPivotField": "Clear filter from {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Törölje a kijelölt értékgörbe csoportokat", "SSE.Views.DocumentHolder.txtClearSparklines": "Törölje a kijelölt értékgörbéket", "SSE.Views.DocumentHolder.txtClearText": "Szöveg", "SSE.Views.DocumentHolder.txtCollapse": "Collapse", "SSE.Views.DocumentHolder.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtColumnWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCondFormat": "Feltételes formázás", "SSE.Views.DocumentHolder.txtCopy": "Másol", "SSE.Views.DocumentHolder.txtCount": "Count", "SSE.Views.DocumentHolder.txtCurrency": "Pénznem", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomRowHeight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomSort": "Egyéni rendezés", "SSE.Views.DocumentHolder.txtCut": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDateLong": "Long Date", "SSE.Views.DocumentHolder.txtDateShort": "Short Date", "SSE.Views.DocumentHolder.txtDelete": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDelField": "Remove", "SSE.Views.DocumentHolder.txtDescending": "Csökkenő", "SSE.Views.DocumentHolder.txtDifference": "Difference from", "SSE.Views.DocumentHolder.txtDistribHor": "Vízszintes igazítás", "SSE.Views.DocumentHolder.txtDistribVert": "Függőleges igazítás", "SSE.Views.DocumentHolder.txtEditComment": "Megjegyzés szerkesztése", "SSE.Views.DocumentHolder.txtEditObject": "Edit object", "SSE.Views.DocumentHolder.txtExpand": "Expand", "SSE.Views.DocumentHolder.txtExpandCollapse": "Expand/Collapse", "SSE.Views.DocumentHolder.txtExpandEntire": "Expand Entire Field", "SSE.Views.DocumentHolder.txtFieldSettings": "Field settings", "SSE.Views.DocumentHolder.txtFilter": "Szűrő", "SSE.Views.DocumentHolder.txtFilterCellColor": "Cellaszín szerinti szűrés", "SSE.Views.DocumentHolder.txtFilterFontColor": "Karakterszín szerinti szűrés", "SSE.Views.DocumentHolder.txtFilterValue": "Cella értéke szerinti szűrés", "SSE.Views.DocumentHolder.txtFormula": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtFraction": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtGeneral": "<PERSON><PERSON>lán<PERSON>", "SSE.Views.DocumentHolder.txtGetLink": "Link a területhez", "SSE.Views.DocumentHolder.txtGrandTotal": "Grand total", "SSE.Views.DocumentHolder.txtGroup": "Csoport", "SSE.Views.DocumentHolder.txtHide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtIndex": "Index", "SSE.Views.DocumentHolder.txtInsert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hivatkozás", "SSE.Views.DocumentHolder.txtInsImage": "Insert image from file", "SSE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "SSE.Views.DocumentHolder.txtLabelFilter": "Label filters", "SSE.Views.DocumentHolder.txtMax": "Max", "SSE.Views.DocumentHolder.txtMin": "Min", "SSE.Views.DocumentHolder.txtMoreOptions": "More options", "SSE.Views.DocumentHolder.txtNormal": "No calculation", "SSE.Views.DocumentHolder.txtNumber": "Szám", "SSE.Views.DocumentHolder.txtNumFormat": "Számformátum", "SSE.Views.DocumentHolder.txtPaste": "Beilleszt", "SSE.Views.DocumentHolder.txtPercent": "% of", "SSE.Views.DocumentHolder.txtPercentage": "Százalék", "SSE.Views.DocumentHolder.txtPercentDiff": "% difference from", "SSE.Views.DocumentHolder.txtPercentOfCol": "% of column total", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% of grand total", "SSE.Views.DocumentHolder.txtPercentOfParent": "% of parent total", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% of parent column total", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% of parent row total", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% running total in", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% of row total", "SSE.Views.DocumentHolder.txtPivotSettings": "Pivot Table settings", "SSE.Views.DocumentHolder.txtProduct": "Product", "SSE.Views.DocumentHolder.txtRankAscending": "Rank smallest to largest", "SSE.Views.DocumentHolder.txtRankDescending": "Rank largest to smallest", "SSE.Views.DocumentHolder.txtReapply": "Újra alkalmaz", "SSE.Views.DocumentHolder.txtRefresh": "Refresh", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON><PERSON> sor", "SSE.Views.DocumentHolder.txtRowHeight": "Sormagass<PERSON>g <PERSON>", "SSE.Views.DocumentHolder.txtRunTotal": "Running total in", "SSE.Views.DocumentHolder.txtScientific": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSelect": "Kiválaszt", "SSE.Views.DocumentHolder.txtShiftDown": "Cella helyettesí<PERSON> f<PERSON>", "SSE.Views.DocumentHolder.txtShiftLeft": "<PERSON><PERSON> he<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftRight": "<PERSON>a he<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.DocumentHolder.txtShiftUp": "Cella he<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShow": "Mutat", "SSE.Views.DocumentHolder.txtShowAs": "Show values as", "SSE.Views.DocumentHolder.txtShowComment": "Megjegyzés megjelenítése", "SSE.Views.DocumentHolder.txtShowDetails": "Show details", "SSE.Views.DocumentHolder.txtSort": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSortCellColor": "Kiválasztott cellaszín a tetején", "SSE.Views.DocumentHolder.txtSortFontColor": "Kiválasztott karakterszín a tetején", "SSE.Views.DocumentHolder.txtSortOption": "More sort options", "SSE.Views.DocumentHolder.txtSparklines": "Értékgörbék", "SSE.Views.DocumentHolder.txtSubtotalField": "Subtotal", "SSE.Views.DocumentHolder.txtSum": "Sum", "SSE.Views.DocumentHolder.txtSummarize": "Summarize values by", "SSE.Views.DocumentHolder.txtText": "Szöveg", "SSE.Views.DocumentHolder.txtTextAdvanced": "<PERSON>adó bekezdés beállítások", "SSE.Views.DocumentHolder.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtTop10": "Top 10", "SSE.Views.DocumentHolder.txtUngroup": "Csoport szétválasztása", "SSE.Views.DocumentHolder.txtValueFieldSettings": "Value field settings", "SSE.Views.DocumentHolder.txtValueFilter": "Value filters", "SSE.Views.DocumentHolder.txtWidth": "Szélesség", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "Függőleges rendezés", "SSE.Views.ExternalLinksDlg.closeButtonText": "Close", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Automatically update data from the linked sources", "SSE.Views.ExternalLinksDlg.textChange": "Change source", "SSE.Views.ExternalLinksDlg.textDelete": "Break links", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Break all links", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textOpen": "Open source", "SSE.Views.ExternalLinksDlg.textSource": "Source", "SSE.Views.ExternalLinksDlg.textStatus": "Status", "SSE.Views.ExternalLinksDlg.textUnknown": "Unknown", "SSE.Views.ExternalLinksDlg.textUpdate": "Update values", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Update all", "SSE.Views.ExternalLinksDlg.textUpdating": "Updating...", "SSE.Views.ExternalLinksDlg.txtTitle": "External links", "SSE.Views.FieldSettingsDialog.strLayout": "Elrendezés", "SSE.Views.FieldSettingsDialog.strSubtotals": "Részösszegek", "SSE.Views.FieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.FieldSettingsDialog.textReport": "Jelent<PERSON>", "SSE.Views.FieldSettingsDialog.textTitle": "Mezőbeállítások", "SSE.Views.FieldSettingsDialog.txtAverage": "Átlag", "SSE.Views.FieldSettingsDialog.txtBlank": "He<PERSON>ezzen be üres sorokat minden elem után", "SSE.Views.FieldSettingsDialog.txtBottom": "Megjelenítés a csoport alján", "SSE.Views.FieldSettingsDialog.txtCompact": "Kompakt", "SSE.Views.FieldSettingsDialog.txtCount": "Dar<PERSON>", "SSE.Views.FieldSettingsDialog.txtCountNums": "Számolja meg a számokat", "SSE.Views.FieldSettingsDialog.txtCustomName": "Egyéni név", "SSE.Views.FieldSettingsDialog.txtEmpty": "Adatok nélküli elemek megjelenítése", "SSE.Views.FieldSettingsDialog.txtMax": "Maximum", "SSE.Views.FieldSettingsDialog.txtMin": "Minimum", "SSE.Views.FieldSettingsDialog.txtOutline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtProduct": "Termék", "SSE.Views.FieldSettingsDialog.txtRepeat": "Elemek címkéinek ismétlése minden sorban", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Részösszegek megjelenítése", "SSE.Views.FieldSettingsDialog.txtSourceName": "Forr<PERSON> neve:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "<PERSON><PERSON><PERSON><PERSON> populáci<PERSON>", "SSE.Views.FieldSettingsDialog.txtSum": "Összeg", "SSE.Views.FieldSettingsDialog.txtSummarize": "Függvények és részösszegek", "SSE.Views.FieldSettingsDialog.txtTabular": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtTop": "Megjelenítés a csoport tetején", "SSE.Views.FieldSettingsDialog.txtVar": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.ariaFileMenu": "File menu", "SSE.Views.FileMenu.btnBackCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnCloseEditor": "Close File", "SSE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnCreateNewCaption": "Új létrehozása", "SSE.Views.FileMenu.btnDownloadCaption": "Letöltés másként", "SSE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnExportToPDFCaption": "Export to PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "Megnyitás", "SSE.Views.FileMenu.btnHelpCaption": "S<PERSON>gó", "SSE.Views.FileMenu.btnHistoryCaption": "Verziótörténet", "SSE.Views.FileMenu.btnInfoCaption": "Munkafüzet infó", "SSE.Views.FileMenu.btnPrintCaption": "Nyomtatás", "SSE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnRenameCaption": "Átnevezés", "SSE.Views.FileMenu.btnReturnCaption": "Vissza a munkafüzethez", "SSE.Views.FileMenu.btnRightsCaption": "Hozzáférési j<PERSON>sultságok", "SSE.Views.FileMenu.btnSaveAsCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveCaption": "Men<PERSON>s", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Másolat mentése mint", "SSE.Views.FileMenu.btnSettingsCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "SSE.Views.FileMenu.btnToEditCaption": "Munkafüzet szerkesztése", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Üres munkafüzet", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Új létrehozása", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Alkalmaz", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Szerző hozzáadása", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Szöveg hozzáadása", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Applikáció", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Szerző", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Hozzáférési jogok módosítása", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Megjegyzés", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Létrehozva", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Spreadsheet info", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Cím", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Feltöltve", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Access Rights", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Hozzáférési jogok módosítása", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Alkalmaz", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Együttes szerkesztési mód", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "1904-es dátumrendszer használata", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Szót<PERSON><PERSON> n<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Enable iterative calculation", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Gyors", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Betűtípus a<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Függvény nyelve", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Példa: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Show function tooltip", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Show horizontal scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "A NAGYBETŰS szavak figyelmen kívül hagyása", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "A számokat tartalmazó szavak figyelmen kívül hagyása", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Maximum change", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Maximum iterations", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "A tartalom beillesztésekor jelenítse meg a beillesztési beállítások gombot", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "R1C1 referenciastílus", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Területi beállí<PERSON>ások", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "<PERSON><PERSON><PERSON>:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTL interface", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Megjegyzés megjelenítése a lapon", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Más felhasználók módosításainak megjelenítése", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Meg<PERSON>ott megjegyzések megjelenítése", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Snapped to the grid while scrolling", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Biztonságos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Tab style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Felhasználói felület témája", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "<PERSON><PERSON><PERSON>ó", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Mérési egység", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Használjon elválasztókat a regionális beállítások alapján", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Show vertical scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Alapértelmezett zoom érték", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "10 percenk<PERSON>t", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "30 perce<PERSON><PERSON>t", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "5 perce<PERSON><PERSON>t", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "<PERSON><PERSON> óra", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Automatikus <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Köztes verziók mentése", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Minden perc", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Referen<PERSON>st<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "Advanced settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Automatikus javí<PERSON> be<PERSON>llí<PERSON>ai...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Katalán", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Alapértelmezett cache mód", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Számítás", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Együttműködés", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Cseh", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Customize quick access", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Dán", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Német", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Szerkesztés és mentés", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "Angol", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Spanyol", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Valós idejű társszerkesztés. Minden módosítás automatikusan elmentésre kerül", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "Francia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonéz", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Hüvelyk", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Japán", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Koreai", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Last used", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "<PERSON>t", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "OS X-ként", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Natív", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Norvég", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Holland", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Igazolás", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "<PERSON><PERSON><PERSON><PERSON> (Brazil)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "Portugál (Portugál)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Show the Quick Print button in the editor header", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Román", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Orosz", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Összes engedélyezése", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Minden értesítés nélküli makró engedélyezése", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Turn on screen reader support", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Default sheet direction", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "This setting will affect only the new sheets", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Left-to-right", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Right-to-left", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Szlov<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Szlovén", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Összes letiltása", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Minden értesítés nélküli ma<PERSON>r<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "A Mentés gomb segítsé<PERSON>vel szinkronizálhatja az Ön és mások által végzett módosításokat.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Svéd", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Use toolbar color as tabs background", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Török", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ukrán", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Az Alt billentyűvel navigálhat a felhasználói felületen a billentyűzet segítségével.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Az Opciós billentyűvel navigálhat a felhasználói felületen a billentyűzet segítségével.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vietnámi", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Értesítés me<PERSON>jelení<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Minden értesítéssel rendelkező makró let<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "Windows-ként", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Munkaterület", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Figyelmeztetés", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Munkafüzet védelme", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Aláírással", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Valid signatures have been added to the spreadsheet.<br>The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Ensure the integrity of the spreadsheet by adding an<br>invisible digital signature", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Munkafüzet szerkesztése", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "A szerkesztés eltávolítja az aláírásokat a munkafüzetről.<br>Folytassuk?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Ezt a munkafüzetet jelszó védi", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Encrypt this spreadsheet with a password", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Ezt a munkafüzetet alá kell írni.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Érvényes aláírásokat adtak hozzá a munkafüzethez. A táblázatkezelő védve van a szerkesztéstől.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "A munkafüzetben található digitális aláírások némelyike érvénytelen vagy nem ellenőrizhető. A táblázatkezelő védve van a szerkesztéstől.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Aláírások megtekintése", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Download as", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Save copy as", "SSE.Views.FillSeriesDialog.textAuto": "AutoFill", "SSE.Views.FillSeriesDialog.textCols": "Columns", "SSE.Views.FillSeriesDialog.textDate": "Date", "SSE.Views.FillSeriesDialog.textDateUnit": "Date unit", "SSE.Views.FillSeriesDialog.textDay": "Day", "SSE.Views.FillSeriesDialog.textGrowth": "Growth", "SSE.Views.FillSeriesDialog.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textMonth": "Month", "SSE.Views.FillSeriesDialog.textRows": "Rows", "SSE.Views.FillSeriesDialog.textSeries": "Series in", "SSE.Views.FillSeriesDialog.textStep": "Step value", "SSE.Views.FillSeriesDialog.textStop": "Stop value", "SSE.Views.FillSeriesDialog.textTitle": "Series", "SSE.Views.FillSeriesDialog.textTrend": "Trend", "SSE.Views.FillSeriesDialog.textType": "Type", "SSE.Views.FillSeriesDialog.textWeek": "Weekday", "SSE.Views.FillSeriesDialog.textYear": "Year", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FormatRulesEditDlg.fillColor": "Kitöltőszín", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Figyelmeztetés", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 Színskála", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 Színskála", "SSE.Views.FormatRulesEditDlg.textAllBorders": "<PERSON><PERSON> s<PERSON>", "SSE.Views.FormatRulesEditDlg.textAppearance": "Sáv megjelenése", "SSE.Views.FormatRulesEditDlg.textApply": "A tartományra való alkalmazás", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automatikus", "SSE.Views.FormatRulesEditDlg.textAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Sáv iránya", "SSE.Views.FormatRulesEditDlg.textBold": "F<PERSON>lkövér", "SSE.Views.FormatRulesEditDlg.textBorder": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Cannot add the conditional formatting.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "A cella középpontja", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Belső függőleges szegélyek", "SSE.Views.FormatRulesEditDlg.textClear": "T<PERSON>r<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textColor": "Szöveg szín", "SSE.Views.FormatRulesEditDlg.textContext": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Adjon meg egy érvényes függvényt.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "A megadott függvény nem v<PERSON>ltható <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ra vagy <PERSON>.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Adjon meg egy értéket.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "A megadott érték nem érvényes s<PERSON>m, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> v<PERSON>.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "A {0} értékének nagyobbnak kell lennie, mint a {1} értékének.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "<PERSON><PERSON><PERSON> be egy <PERSON> {0} és {1} között.", "SSE.Views.FormatRulesEditDlg.textFill": "Kitöltés", "SSE.Views.FormatRulesEditDlg.textFormat": "Formázás", "SSE.Views.FormatRulesEditDlg.textFormula": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textGradient": "Színátmenet", "SSE.Views.FormatRulesEditDlg.textIconLabel": "ha {0} {1} és", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "ha {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "amikor az érték", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Egy vagy több ikonadat-tartomány átfedi egymást.<br>Módosítsa az ikonadat-tartomány értékeit úgy, hogy a tartományok ne fedjék egymást.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Bel<PERSON><PERSON> szegélyek", "SSE.Views.FormatRulesEditDlg.textInvalid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON>", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "HIBA! Érvénytelen cellatartomány", "SSE.Views.FormatRulesEditDlg.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textItem": "Tétel", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "<PERSON><PERSON>r<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "<PERSON><PERSON> <PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLongBar": "Leghosszabb sáv", "SSE.Views.FormatRulesEditDlg.textMaximum": "Maximum", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Belső vízszintes szegélyek", "SSE.Views.FormatRulesEditDlg.textMidpoint": "K<PERSON>zé<PERSON><PERSON>rt<PERSON>k", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimum", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Minimum érték", "SSE.Views.FormatRulesEditDlg.textNegative": "Negatív", "SSE.Views.FormatRulesEditDlg.textNewColor": "Új egyéni sz<PERSON> hozz<PERSON>adása", "SSE.Views.FormatRulesEditDlg.textNoBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNone": "<PERSON><PERSON><PERSON> sem", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "A megadott értékek közül egy vagy több nem érvényes százalékos érték.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "A megadott {0} érték nem érvényes százalék.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "A megadott értékek közül egy vagy több nem érvényes százalékos érték.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "A megadott {0} érték nem érvényes százalékos érték.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "K<PERSON><PERSON><PERSON> szegélyek", "SSE.Views.FormatRulesEditDlg.textPercent": "Százalék", "SSE.Views.FormatRulesEditDlg.textPercentile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPosition": "Pozíció", "SSE.Views.FormatRulesEditDlg.textPositive": "Pozitív", "SSE.Views.FormatRulesEditDlg.textPresets": "Előbeállítások", "SSE.Views.FormatRulesEditDlg.textPreview": "Előnézet", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "<PERSON>em <PERSON> relatív hivatkozásokat a színskálák, adatsorok és ikonkészletek feltételes formázási kritériumában.", "SSE.Views.FormatRulesEditDlg.textReverse": "Ikonok sorrend megfordítás<PERSON>", "SSE.Views.FormatRulesEditDlg.textRight2Left": "<PERSON><PERSON><PERSON><PERSON><PERSON> balra", "SSE.Views.FormatRulesEditDlg.textRightBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRule": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSameAs": "Ugyanaz, mint a pozitív", "SSE.Views.FormatRulesEditDlg.textSelectData": "Adatok kiválasztása", "SSE.Views.FormatRulesEditDlg.textShortBar": "<PERSON><PERSON><PERSON>videbb sáv", "SSE.Views.FormatRulesEditDlg.textShowBar": "Csak sáv megjelenítése", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Csak ikon megjelenítése", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Ez a típusú hivatkozás nem használható feltételes formázási függvényekben.<br>Módosítsa a hivatkozást egyetlen cellára, vagy hasz<PERSON> a hivatkozást egy munkalapfüggvénnyel, például =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Egyszínű", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Áthúzás", "SSE.Views.FormatRulesEditDlg.textSubscript": "Alsó index", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Felső index", "SSE.Views.FormatRulesEditDlg.textTopBorders": "<PERSON><PERSON><PERSON> szegélyek", "SSE.Views.FormatRulesEditDlg.textUnderline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Számformátum", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Könyvelés", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Pénznem", "SSE.Views.FormatRulesEditDlg.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDateLong": "Long date", "SSE.Views.FormatRulesEditDlg.txtDateShort": "Short date", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Ez egy szükséges mező", "SSE.Views.FormatRulesEditDlg.txtFraction": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtGeneral": "<PERSON><PERSON>lán<PERSON>", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "<PERSON><PERSON><PERSON> ikon", "SSE.Views.FormatRulesEditDlg.txtNumber": "Szám", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Százalék", "SSE.Views.FormatRulesEditDlg.txtScientific": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtText": "Szöveg", "SSE.Views.FormatRulesEditDlg.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Formázás<PERSON> s<PERSON><PERSON><PERSON>keszté<PERSON>", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "<PERSON>j formázási <PERSON>", "SSE.Views.FormatRulesManagerDlg.guestText": "Vendég", "SSE.Views.FormatRulesManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 standard eltérés az átlag felett", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 standard eltérés az átlag alatt", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 standard eltérés az átlag felett", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 standard eltérés az átlag alatt", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 standard eltérés az átlag felett", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 standard eltérés az átlag alatt", "SSE.Views.FormatRulesManagerDlg.textAbove": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textApply": "Alkalmazza a következőre", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "A cella kezdő értéke", "SSE.Views.FormatRulesManagerDlg.textBelow": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textBetween": "{0} és {1} <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textCellValue": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Osztályozott <PERSON>", "SSE.Views.FormatRulesManagerDlg.textContains": "A cella értékének tartalma", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "A cella értéke üres", "SSE.Views.FormatRulesManagerDlg.textContainsError": "A cella egy hibát tartalmaz", "SSE.Views.FormatRulesManagerDlg.textDelete": "Törlés", "SSE.Views.FormatRulesManagerDlg.textDown": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Értékek kettőzése", "SSE.Views.FormatRulesManagerDlg.textEdit": "Szerkesztés", "SSE.Views.FormatRulesManagerDlg.textEnds": "A cella érték vége", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Egyenlő vagy nagyobb az átlagnál", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Egyenlő vagy k<PERSON>ebb az átlagnál", "SSE.Views.FormatRulesManagerDlg.textFormat": "Formázás", "SSE.Views.FormatRulesManagerDlg.textIconSet": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textNew": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "nem {0} <PERSON><PERSON> {1} <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textNotContains": "A cella értéke nem tartalmaz", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Az cellában lévő érték nem üres", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "A cella nem tartalmaz hibát", "SSE.Views.FormatRulesManagerDlg.textRules": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textScope": "Formázási szabályok megjelenítése a következőhöz:", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Adatok kiválasztása", "SSE.Views.FormatRulesManagerDlg.textSelection": "Pénznem kiválasztása", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "Ez a pivot táblázat", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Ez a munkalap", "SSE.Views.FormatRulesManagerDlg.textThisTable": "Ez a táblázat", "SSE.Views.FormatRulesManagerDlg.textUnique": "Egyedi <PERSON>", "SSE.Views.FormatRulesManagerDlg.textUp": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON> felfel<PERSON>", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Ezt az elemet egy másik felhasználó szerkeszti.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Feltételes formázás", "SSE.Views.FormatSettingsDialog.textCategory": "Kategória", "SSE.Views.FormatSettingsDialog.textDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textFormat": "Formátum", "SSE.Views.FormatSettingsDialog.textLinked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textSeparator": "<PERSON><PERSON><PERSON>las<PERSON><PERSON><PERSON> has<PERSON>", "SSE.Views.FormatSettingsDialog.textSymbols": "Szimbólumok", "SSE.Views.FormatSettingsDialog.textTitle": "Számformátum", "SSE.Views.FormatSettingsDialog.txtAccounting": "Könyvelés", "SSE.Views.FormatSettingsDialog.txtAs10": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Nyolcadok<PERSON><PERSON><PERSON> (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Pénznem", "SSE.Views.FormatSettingsDialog.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, go<PERSON><PERSON> adja meg az egyéni számformátumot. A táblázatszerkesztő nem ellenőrzi, hogy az egyéni formátumokban vannak-e hibák, am<PERSON><PERSON> hat<PERSON> lehetnek az xlsx fájlra.", "SSE.Views.FormatSettingsDialog.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtFraction": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtGeneral": "<PERSON><PERSON>lán<PERSON>", "SSE.Views.FormatSettingsDialog.txtNone": "nincs", "SSE.Views.FormatSettingsDialog.txtNumber": "Szám", "SSE.Views.FormatSettingsDialog.txtPercentage": "Százalék", "SSE.Views.FormatSettingsDialog.txtSample": "Minta:", "SSE.Views.FormatSettingsDialog.txtScientific": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtText": "Szöveg", "SSE.Views.FormatSettingsDialog.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtUpto1": "<PERSON><PERSON> (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "<PERSON><PERSON><PERSON> (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "<PERSON><PERSON><PERSON> (131/135)", "SSE.Views.FormulaDialog.sDescription": "Le<PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.textGroupDescription": "Függvénycsoport választása", "SSE.Views.FormulaDialog.textListDescription": "Függvény választása", "SSE.Views.FormulaDialog.txtRecommended": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtSearch": "Keresés", "SSE.Views.FormulaDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.capBtnRemoveArr": "Remove Arrows", "SSE.Views.FormulaTab.capBtnTraceDep": "Trace Dependents", "SSE.Views.FormulaTab.capBtnTracePrec": "Trace Precedents", "SSE.Views.FormulaTab.textAutomatic": "Automatikus", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Számítás a jelenlegi munkalapra", "SSE.Views.FormulaTab.textCalculateWorkbook": "Munkafüzet számítás", "SSE.Views.FormulaTab.textManual": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculate": "Számítás", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Számítás az egész munkafüzetre", "SSE.Views.FormulaTab.tipRemoveArr": "Remove the arrows drawn by Trace Precedents or Trace Dependents", "SSE.Views.FormulaTab.tipShowFormulas": "Display the formula in each cell instead of the resulting value", "SSE.Views.FormulaTab.tipTraceDep": "Show arrows that indicate which cells are affected by the value of the selected cell", "SSE.Views.FormulaTab.tipTracePrec": "Show arrows that indicate which cells affect the value of the selected cell", "SSE.Views.FormulaTab.tipWatch": "Add cells to the Watch Window list", "SSE.Views.FormulaTab.txtAdditional": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtAutosum": "Automatikus összegzés", "SSE.Views.FormulaTab.txtAutosumTip": "Összegzés", "SSE.Views.FormulaTab.txtCalculation": "Számítás", "SSE.Views.FormulaTab.txtFormula": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormulaTip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtMore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtRecent": "<PERSON>an<PERSON><PERSON>", "SSE.Views.FormulaTab.txtRemDep": "Remove Dependents Arrows", "SSE.Views.FormulaTab.txtRemPrec": "Remove Precedents Arrows", "SSE.Views.FormulaTab.txtShowFormulas": "Show Formulas", "SSE.Views.FormulaTab.txtWatch": "Watch Window", "SSE.Views.FormulaWizard.textAny": "b<PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textArgument": "Argumentum", "SSE.Views.FormulaWizard.textFunction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textFunctionRes": "Függv<PERSON><PERSON>", "SSE.Views.FormulaWizard.textHelp": "Segítség ehhez a függvényhez", "SSE.Views.FormulaWizard.textLogical": "logikai", "SSE.Views.FormulaWizard.textNoArgs": "Ennek a függvénynek nincsenek paraméterei", "SSE.Views.FormulaWizard.textNoArgsDesc": "this argument has no description", "SSE.Views.FormulaWizard.textNumber": "szám", "SSE.Views.FormulaWizard.textReadMore": "Read more", "SSE.Views.FormulaWizard.textRef": "referencia", "SSE.Views.FormulaWizard.textText": "szöveg", "SSE.Views.FormulaWizard.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textValue": "Függv<PERSON><PERSON>", "SSE.Views.GoalSeekDlg.textChangingCell": "By changing cell", "SSE.Views.GoalSeekDlg.textDataRangeError": "The formula is missing a range", "SSE.Views.GoalSeekDlg.textMustContainFormula": "The cell must contain a formula", "SSE.Views.GoalSeekDlg.textMustContainValue": "Cell must contain a value", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Formula in cell must result in a number", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Reference must be to a single cell", "SSE.Views.GoalSeekDlg.textSelectData": "Select data", "SSE.Views.GoalSeekDlg.textSetCell": "Set cell", "SSE.Views.GoalSeekDlg.textTitle": "Goal seek", "SSE.Views.GoalSeekDlg.textToValue": "To value", "SSE.Views.GoalSeekDlg.txtEmpty": "This field is required", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.GoalSeekStatusDlg.textContinue": "Continue", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Current value:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Goal seeking with cell {0} found a solution.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Goal seeking with cell {0} may not have found a solution.", "SSE.Views.GoalSeekStatusDlg.textPause": "Pause", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Goal seeking with cell {0} on iteration #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "Step", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Target value:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Goal seek status", "SSE.Views.HeaderFooterDialog.textAlign": "Igazítás az oldal margóihoz", "SSE.Views.HeaderFooterDialog.textAll": "<PERSON>en oldal", "SSE.Views.HeaderFooterDialog.textBold": "F<PERSON>lkövér", "SSE.Views.HeaderFooterDialog.textCenter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textColor": "Szöveg színe", "SSE.Views.HeaderFooterDialog.textDate": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textDiffFirst": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Páros és páratlan oldalak eltérőek", "SSE.Views.HeaderFooterDialog.textEven": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textFileName": "Fájlnév", "SSE.Views.HeaderFooterDialog.textFirst": "<PERSON><PERSON><PERSON> oldal", "SSE.Views.HeaderFooterDialog.textFooter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textHeader": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textImage": "Picture", "SSE.Views.HeaderFooterDialog.textInsert": "Beszúrás", "SSE.Views.HeaderFooterDialog.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textLeft": "<PERSON>l", "SSE.Views.HeaderFooterDialog.textMaxError": "A beírt szöveg túl hosszú. Csökkentse a használt karakterek számát.", "SSE.Views.HeaderFooterDialog.textNewColor": "Új egyéni sz<PERSON> hozz<PERSON>adása", "SSE.Views.HeaderFooterDialog.textOdd": "<PERSON><PERSON><PERSON><PERSON> oldal", "SSE.Views.HeaderFooterDialog.textPageCount": "Oldalak száma", "SSE.Views.HeaderFooterDialog.textPageNum": "Oldalszám", "SSE.Views.HeaderFooterDialog.textPresets": "Mentett <PERSON>", "SSE.Views.HeaderFooterDialog.textRight": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textScale": "Méretezés a dokumentummal", "SSE.Views.HeaderFooterDialog.textSheet": "<PERSON><PERSON><PERSON> neve", "SSE.Views.HeaderFooterDialog.textStrikeout": "Áthúzás", "SSE.Views.HeaderFooterDialog.textSubscript": "Alsó index", "SSE.Views.HeaderFooterDialog.textSuperscript": "Felső index", "SSE.Views.HeaderFooterDialog.textTime": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textTitle": "Fejléc/lábléc beállítások", "SSE.Views.HeaderFooterDialog.textUnderline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.tipFontName": "Betűtípus", "SSE.Views.HeaderFooterDialog.tipFontSize": "Bet<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Hivatkozás erre", "SSE.Views.HyperlinkSettingsDialog.strRange": "Tartomány", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Másol", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Kiválasztott tartomány", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Itt adja meg a feliratot", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Itt adja meg a hivat<PERSON>ást", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Itt adja meg a Gyorsinfót", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Külső hivatkozás", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Hivatkozás kérése", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "<PERSON><PERSON><PERSON> adattartomány", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "HIBA! Érvénytelen cellatartomány", "SSE.Views.HyperlinkSettingsDialog.textNames": "Megadott nevek", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Adatok kiválasztása", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Lapok", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Gyorstipp szöveg", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Hivatkozás beállítások", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Ez egy szükséges mező", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Ennek a mezőnek hivatkozásnak kell lennie a \"http://www.example.com\" formátumban", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Ez a mező legfeljebb 2083 karakterből állhat", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "SSE.Views.ImageSettings.strTransparency": "Opacity", "SSE.Views.ImageSettings.textAdvanced": "Speciális beállítások megjelenítése", "SSE.Views.ImageSettings.textCrop": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFill": "Kitöltés", "SSE.Views.ImageSettings.textCropFit": "Illesztés", "SSE.Views.ImageSettings.textCropToShape": "Formára vágás", "SSE.Views.ImageSettings.textEdit": "Szerkeszt", "SSE.Views.ImageSettings.textEditObject": "Objektum szerkesztése", "SSE.Views.ImageSettings.textFlip": "Tükröz", "SSE.Views.ImageSettings.textFromFile": "Fájlból", "SSE.Views.ImageSettings.textFromStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textFromUrl": "URL-ből", "SSE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textHint270": "Elforgat balra 90 fokkal", "SSE.Views.ImageSettings.textHint90": "Elforgat jobbra 90 fokkal", "SSE.Views.ImageSettings.textHintFlipH": "Vízszintesen tükröz", "SSE.Views.ImageSettings.textHintFlipV": "Függőlegesen tükröz", "SSE.Views.ImageSettings.textInsert": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textKeepRatio": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textOriginalSize": "Tényleges méret", "SSE.Views.ImageSettings.textRecentlyUsed": "<PERSON>an<PERSON><PERSON>", "SSE.Views.ImageSettings.textResetCrop": "Reset crop", "SSE.Views.ImageSettings.textRotate90": "Elforgat 90 fokkal", "SSE.Views.ImageSettings.textRotation": "Forgatás", "SSE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textWidth": "Szélesség", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "<PERSON><PERSON>s moz<PERSON> vagy méretezés cellákkal", "SSE.Views.ImageSettingsAdvanced.textAlt": "Alternatív szöveg", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Le<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAltTip": "A vizuális objektumok alternatív szövegalapú ábrázolása, amely a látás vagy kognitív károsodottak számára is o<PERSON><PERSON><PERSON><PERSON>, hogy segítsen nekik job<PERSON> me<PERSON>, hogy milyen <PERSON>, al<PERSON><PERSON><PERSON>, diagram vagy táblázat látható.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Cím", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Tükrözött", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Vízszintesen", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Mozgatás cellákkal méretezés nélkül", "SSE.Views.ImageSettingsAdvanced.textRotation": "Forgatás", "SSE.Views.ImageSettingsAdvanced.textSnap": "Cella igazítás", "SSE.Views.ImageSettingsAdvanced.textTitle": "Kép - <PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Mozgatás és méretezés cellákkal", "SSE.Views.ImageSettingsAdvanced.textVertically": "Függőlegesen", "SSE.Views.ImportFromXmlDialog.textDestination": "Choose, where to place the data", "SSE.Views.ImportFromXmlDialog.textExist": "Existing worksheet", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ImportFromXmlDialog.textNew": "New worksheet", "SSE.Views.ImportFromXmlDialog.textSelectData": "Select data", "SSE.Views.ImportFromXmlDialog.textTitle": "Import data", "SSE.Views.ImportFromXmlDialog.txtEmpty": "This field is required", "SSE.Views.LeftMenu.ariaLeftMenu": "Left menu", "SSE.Views.LeftMenu.tipAbout": "Névjegy", "SSE.Views.LeftMenu.tipChat": "Cha<PERSON>", "SSE.Views.LeftMenu.tipComments": "Megjegyzések", "SSE.Views.LeftMenu.tipFile": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "Kiegészítők", "SSE.Views.LeftMenu.tipSearch": "<PERSON><PERSON>", "SSE.Views.LeftMenu.tipSpellcheck": "Helyesírás-ellenőrzés", "SSE.Views.LeftMenu.tipSupport": "Visszajelzés és támogatás", "SSE.Views.LeftMenu.txtDeveloper": "FEJLESZTŐI MÓD", "SSE.Views.LeftMenu.txtEditor": "Táblázat szerkesztő", "SSE.Views.LeftMenu.txtLimit": "Ko<PERSON><PERSON><PERSON>zza a hozzáférést", "SSE.Views.LeftMenu.txtTrial": "PRÓBA MÓD", "SSE.Views.LeftMenu.txtTrialDev": "Próba fejlesztői mód", "SSE.Views.MacroDialog.textMacro": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MacroDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.okButtonText": "Men<PERSON>s", "SSE.Views.MainSettingsPrint.strBottom": "Alsó", "SSE.Views.MainSettingsPrint.strLandscape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLeft": "<PERSON>l", "SSE.Views.MainSettingsPrint.strMargins": "Margók", "SSE.Views.MainSettingsPrint.strPortrait": "Portré", "SSE.Views.MainSettingsPrint.strPrint": "Nyomtat", "SSE.Views.MainSettingsPrint.strPrintTitles": "Címek nyomtatása", "SSE.Views.MainSettingsPrint.strRight": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strTop": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textActualSize": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>", "SSE.Views.MainSettingsPrint.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textCustomOptions": "Egy<PERSON>i <PERSON>é<PERSON>k", "SSE.Views.MainSettingsPrint.textFitCols": "Illessze az összes oszlopot egy oldalon", "SSE.Views.MainSettingsPrint.textFitPage": "Illessze a munkalapot egy oldalra", "SSE.Views.MainSettingsPrint.textFitRows": "Illessze az összes sort egy oldalon", "SSE.Views.MainSettingsPrint.textPageOrientation": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPageScaling": "Skálázás", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON><PERSON> m<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPrintGrid": "Rácsvonalak nyomtatása", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Sor- és oszlopfejlécek nyomtatása", "SSE.Views.MainSettingsPrint.textRepeat": "Ismétlés...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Oszlopok ismétlése a bal oldalon", "SSE.Views.MainSettingsPrint.textRepeatTop": "Oszlopok ismétlése felül", "SSE.Views.MainSettingsPrint.textSettings": "Beállítások", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "A meglévő tartományok nem szerkeszthetők, és az újakat nem lehet létrehozni<br> j<PERSON><PERSON><PERSON> némely szerkesztés alatt <PERSON>ll.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Megadott név", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Figyelmeztetés", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Munkafüzet", "SSE.Views.NamedRangeEditDlg.textDataRange": "Adattartomány", "SSE.Views.NamedRangeEditDlg.textExistName": "HIBA! Tartomány már létezik ilyen névvel", "SSE.Views.NamedRangeEditDlg.textInvalidName": "A névnek betűvel vagy aláhúzással kell kezdődnie, és nem tartalmazhat érvénytelen karaktereket.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "HIBA! Érvénytelen cellatartomány", "SSE.Views.NamedRangeEditDlg.textIsLocked": "HIBA! Ezt az elemet jelenleg egy másik felhasználó szerkeszti.", "SSE.Views.NamedRangeEditDlg.textName": "Név", "SSE.Views.NamedRangeEditDlg.textReservedName": "A használni kívánt név már hivatkozásra került egyes függvényekben. K<PERSON>r<PERSON>ük, használjon más nevet.", "SSE.Views.NamedRangeEditDlg.textScope": "Hatókör", "SSE.Views.NamedRangeEditDlg.textSelectData": "Adatok kiválasztása", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Ez egy szükséges mező", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Név szerkesztése", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "<PERSON>j név", "SSE.Views.NamedRangePasteDlg.textNames": "Elnevezett tartományok", "SSE.Views.NamedRangePasteDlg.txtTitle": "Név beillesztése", "SSE.Views.NameManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.guestText": "Vendég", "SSE.Views.NameManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textDataRange": "Adattartomány", "SSE.Views.NameManagerDlg.textDelete": "T<PERSON>r<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEdit": "Szerkeszt", "SSE.Views.NameManagerDlg.textEmpty": "Még nem hoztak létre elnevezett tartományokat.<br><PERSON><PERSON><PERSON> létre legalább egy elnevezett tartományt, és megjelenik a mezőben.", "SSE.Views.NameManagerDlg.textFilter": "Szűrő", "SSE.Views.NameManagerDlg.textFilterAll": "Minden", "SSE.Views.NameManagerDlg.textFilterDefNames": "Megadott nevek", "SSE.Views.NameManagerDlg.textFilterSheet": "A laphoz tartozó nevek", "SSE.Views.NameManagerDlg.textFilterTableNames": "Táblázatnevek", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Munkafüzethez tartozó nevek", "SSE.Views.NameManagerDlg.textNew": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textnoNames": "Nem <PERSON>ó<PERSON> a szűrőhöz tartozó elnevezett tartományok.", "SSE.Views.NameManagerDlg.textRanges": "Elnevezett tartományok", "SSE.Views.NameManagerDlg.textScope": "Hatókör", "SSE.Views.NameManagerDlg.textWorkbook": "Munkafüzet", "SSE.Views.NameManagerDlg.tipIsLocked": "Ezt az elemet egy másik felhasználó szerkeszti.", "SSE.Views.NameManagerDlg.txtTitle": "Név kezelő", "SSE.Views.NameManagerDlg.warnDelete": "<PERSON><PERSON><PERSON>, hogy törölni akarja a {0} nevet?", "SSE.Views.PageMarginsDialog.textBottom": "Alsó", "SSE.Views.PageMarginsDialog.textCenter": "Center on page", "SSE.Views.PageMarginsDialog.textHor": "Horizontally", "SSE.Views.PageMarginsDialog.textLeft": "<PERSON>l", "SSE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTitle": "Margók", "SSE.Views.PageMarginsDialog.textTop": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textVert": "Vertically", "SSE.Views.PageMarginsDialog.textWarning": "Warning", "SSE.Views.PageMarginsDialog.warnCheckMargings": "Margins are incorrect", "SSE.Views.ParagraphSettings.strLineHeight": "Sortávolság", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Bekezdés térköz", "SSE.Views.ParagraphSettings.strSpacingAfter": "után", "SSE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAdvanced": "Speciális beállítások megjelenítése", "SSE.Views.ParagraphSettings.textAt": "n<PERSON>l", "SSE.Views.ParagraphSettings.textAtLeast": "legalább", "SSE.Views.ParagraphSettings.textAuto": "Többszörös", "SSE.Views.ParagraphSettings.textExact": "Pontosan", "SSE.Views.ParagraphSettings.txtAutoText": "Automatikus", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "A megadott lapok ezen a területen jelennek meg.", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Minden nagybetű", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Behúzások", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON>l", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Sortávolság", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Betűtípus", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Behúzások és térközök", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Kisbetűk", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Térköz", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Áthúzás", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Alsó index", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Felső index", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Lapok", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Elrendezés", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Többszörös", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lap", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Effektek", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Pontosan", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON>ő sor", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Függő", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Sorkizárt", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(nincs)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Eltávolít", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Minden eltávolítása", "SSE.Views.ParagraphSettingsAdvanced.textSet": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON>l", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Bekezdés - <PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Automatikus", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Delete", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "Duplicate", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Edit", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Formula", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Items Name", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "New", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Calculated Items in", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "egyenlő", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "nem zá<PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "tartalmaz", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "nem tartalmaz", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "nem k<PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "nem e<PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "nagyobb mint", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "nagy<PERSON><PERSON> vagy e<PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "kisebb mint", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "kisebb vagy egyenlő mint", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "kezdődik", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "nem azzal k<PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "z<PERSON>r<PERSON><PERSON>k", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "<PERSON><PERSON>sa a<PERSON> az elemeket, amelyek<PERSON><PERSON><PERSON> a címke:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Mutassa azokat az elemeket, amelyekhez:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Használjon kérdőjelet egyetlen karakter bemutatásához", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Használja a * -ot a karakterek sorozatának megjelenítéséhez", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "és", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Címkeszűrő", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Értékszűrő", "SSE.Views.PivotGroupDialog.textAuto": "Auto", "SSE.Views.PivotGroupDialog.textBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textDays": "Napok", "SSE.Views.PivotGroupDialog.textEnd": "Vége", "SSE.Views.PivotGroupDialog.textError": "Ennek a mezőnek numerikus értéknek kell lennie", "SSE.Views.PivotGroupDialog.textGreaterError": "A végső számnak nagyobbnak kell lennie, mint a kezdő szám", "SSE.Views.PivotGroupDialog.textHour": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMonth": "Hónapok", "SSE.Views.PivotGroupDialog.textNumDays": "Napok száma", "SSE.Views.PivotGroupDialog.textQuart": "Negyedévek", "SSE.Views.PivotGroupDialog.textSec": "Másodpercek", "SSE.Views.PivotGroupDialog.textStart": "Kezdés", "SSE.Views.PivotGroupDialog.textYear": "Évek", "SSE.Views.PivotGroupDialog.txtTitle": "Csoportosítás", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "You can use Calculated Items for basic calculations between different items within a single field", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Insert into formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "<PERSON><PERSON>", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Item name", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Items", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Read more", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Insert Calculated Item in", "SSE.Views.PivotSettings.textAdvanced": "Speciális beállítások megjelenítése", "SSE.Views.PivotSettings.textColumns": "Oszlopok", "SSE.Views.PivotSettings.textFields": "Mezők kiválasztása", "SSE.Views.PivotSettings.textFilters": "Szűrők", "SSE.Views.PivotSettings.textRows": "So<PERSON>", "SSE.Views.PivotSettings.textValues": "<PERSON>rté<PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddColumn": "Oszlopokhoz hozz<PERSON>ad", "SSE.Views.PivotSettings.txtAddFilter": "Szűrőkh<PERSON>z <PERSON>", "SSE.Views.PivotSettings.txtAddRow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddValues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtFieldSettings": "Mezőbeállítások", "SSE.Views.PivotSettings.txtMoveBegin": "<PERSON><PERSON><PERSON><PERSON> az elejére", "SSE.Views.PivotSettings.txtMoveColumn": "Move to Columns", "SSE.Views.PivotSettings.txtMoveDown": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtMoveEnd": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "SSE.Views.PivotSettings.txtMoveFilter": "Move to Filters", "SSE.Views.PivotSettings.txtMoveRow": "Move to Rows", "SSE.Views.PivotSettings.txtMoveUp": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "SSE.Views.PivotSettings.txtMoveValues": "Move to Values", "SSE.Views.PivotSettings.txtRemove": "<PERSON><PERSON><PERSON> eltávolítsa", "SSE.Views.PivotSettingsAdvanced.strLayout": "Név és elrendezés", "SSE.Views.PivotSettingsAdvanced.textAlt": "Alternatív szöveg", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Le<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAltTip": "A vizuális objektumok alternatív szövegalapú ábrázolása, amely a látás vagy kognitív károsodottak számára is o<PERSON><PERSON><PERSON><PERSON>, hogy segítsen nekik job<PERSON> me<PERSON>, hogy milyen <PERSON>, al<PERSON><PERSON><PERSON>, diagram vagy táblázat látható.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Cím", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "<PERSON>z oszlopszélesség automatikus illesztése frissítéskor", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Adattartomány", "SSE.Views.PivotSettingsAdvanced.textDataSource": "<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Mezők megjelenítése a jelentés szűrő területén", "SSE.Views.PivotSettingsAdvanced.textDown": "Le, az<PERSON><PERSON>t", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Összesített eredmények", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Mezőfejlécek", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "HIBA! Érvénytelen cellatartomány", "SSE.Views.PivotSettingsAdvanced.textOver": "Á<PERSON>, aztán le", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Adatok kiválasztása", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Megjelenítés oszlopokhoz", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Sorok és oszlopok mezőfejléceinek megjelenítése", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Megjelen<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textTitle": "Kimutatás tábla - Speci<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Jelölje a szűrőmezőket oszloponként", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Jelölje a szűrőmezőket soronként", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Ez egy szükséges mező", "SSE.Views.PivotSettingsAdvanced.txtName": "Név", "SSE.Views.PivotShowDetailDialog.textDescription": "Choose the field containing the detail you want to show:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Show Detail", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON> sorok", "SSE.Views.PivotTable.capGrandTotals": "Összesített eredmények", "SSE.Views.PivotTable.capLayout": "Jelentés elrendezése", "SSE.Views.PivotTable.capSubtotals": "Részösszegek", "SSE.Views.PivotTable.mniBottomSubtotals": "Az összes részösszeg megjelenítése a csoport alján", "SSE.Views.PivotTable.mniInsertBlankLine": "Minden elem után helyezzen be üres sort", "SSE.Views.PivotTable.mniLayoutCompact": "Megjelenítés kompakt formátumban", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Ne ismételje meg az összes elemcímkét", "SSE.Views.PivotTable.mniLayoutOutline": "Megjelenítés vázlat formátum<PERSON>", "SSE.Views.PivotTable.mniLayoutRepeat": "Az összes elemcímke megismétlése", "SSE.Views.PivotTable.mniLayoutTabular": "Megjelenítés táblázatos formátum<PERSON>", "SSE.Views.PivotTable.mniNoSubtotals": "Ne mutasson részösszegeket", "SSE.Views.PivotTable.mniOffTotals": "Kikapcsolás a sorokhoz és oszlopokhoz", "SSE.Views.PivotTable.mniOnColumnsTotals": "Csak oszlopok esetén", "SSE.Views.PivotTable.mniOnRowsTotals": "Csak soroknál bekapcsol", "SSE.Views.PivotTable.mniOnTotals": "Bekapcsol soroknál és oszlopoknál", "SSE.Views.PivotTable.mniRemoveBlankLine": "Minden elem után távolítsa el az üres sort", "SSE.Views.PivotTable.mniTopSubtotals": "Az összes részösszeg megjelenítése a Csoport tetején", "SSE.Views.PivotTable.textColBanded": "Sávos oszlopok", "SSE.Views.PivotTable.textColHeader": "Oszlopfejlécek", "SSE.Views.PivotTable.textRowBanded": "<PERSON><PERSON><PERSON> so<PERSON>", "SSE.Views.PivotTable.textRowHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.tipCalculatedItems": "Calculated items", "SSE.Views.PivotTable.tipCreatePivot": "Pivot tábla beszúrása", "SSE.Views.PivotTable.tipGrandTotals": "Összesített eredmények <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.tipRefresh": "Frissítse az adatokat az adatforrásból", "SSE.Views.PivotTable.tipRefreshCurrent": "Update the information from data source for the current table", "SSE.Views.PivotTable.tipSelect": "Teljes pivot tábla kiválasztása", "SSE.Views.PivotTable.tipSubtotals": "Részösszegek megjelenítése, elrejtése", "SSE.Views.PivotTable.txtCalculatedItems": "Calculated Items", "SSE.Views.PivotTable.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.PivotTable.txtCreate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtExpandEntire": "Expand Entire Field", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Custom", "SSE.Views.PivotTable.txtGroupPivot_Dark": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Light": "<PERSON>il<PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Medium", "SSE.Views.PivotTable.txtPivotTable": "Kimutatás tábla", "SSE.Views.PivotTable.txtRefresh": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtRefreshAll": "Refresh all", "SSE.Views.PivotTable.txtSelect": "Kiválaszt", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Pivot Table Style Dark", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Pivot Table Style Light", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Pivot Table Style Medium", "SSE.Views.PrintSettings.btnDownload": "Ment és letölt", "SSE.Views.PrintSettings.btnExport": "Save & Export", "SSE.Views.PrintSettings.btnPrint": "Ment és nyomtat", "SSE.Views.PrintSettings.strBottom": "Alsó", "SSE.Views.PrintSettings.strLandscape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strLeft": "<PERSON>l", "SSE.Views.PrintSettings.strMargins": "Margók", "SSE.Views.PrintSettings.strPortrait": "Portré", "SSE.Views.PrintSettings.strPrint": "Nyomtat", "SSE.Views.PrintSettings.strPrintTitles": "Címek nyomtatása", "SSE.Views.PrintSettings.strRight": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strShow": "Mutat", "SSE.Views.PrintSettings.strTop": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textActiveSheets": "Active sheets", "SSE.Views.PrintSettings.textActualSize": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>", "SSE.Views.PrintSettings.textAllSheets": "<PERSON><PERSON> lap", "SSE.Views.PrintSettings.textCurrentSheet": "<PERSON><PERSON><PERSON><PERSON><PERSON> munk<PERSON>", "SSE.Views.PrintSettings.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textCustomOptions": "Egy<PERSON>i <PERSON>é<PERSON>k", "SSE.Views.PrintSettings.textFitCols": "Illessze az összes oszlopot egy oldalon", "SSE.Views.PrintSettings.textFitPage": "Illessze a munkalapot egy oldalra", "SSE.Views.PrintSettings.textFitRows": "Illessze az összes sort egy oldalon", "SSE.Views.PrintSettings.textHideDetails": "Részletek elrejtése", "SSE.Views.PrintSettings.textIgnore": "Nyomtatási terület mellőzése", "SSE.Views.PrintSettings.textLayout": "Elrendezés", "SSE.Views.PrintSettings.textMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintSettings.textMarginsNormal": "Normal", "SSE.Views.PrintSettings.textMarginsWide": "Wide", "SSE.Views.PrintSettings.textPageOrientation": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textPages": "Pages:", "SSE.Views.PrintSettings.textPageScaling": "Skálázás", "SSE.Views.PrintSettings.textPageSize": "<PERSON><PERSON> m<PERSON><PERSON>", "SSE.Views.PrintSettings.textPrintGrid": "Rácsvonalak nyomtatása", "SSE.Views.PrintSettings.textPrintHeadings": "Sor- és oszlopfejlécek nyomtatása", "SSE.Views.PrintSettings.textPrintRange": "Nyomtatási tartomány", "SSE.Views.PrintSettings.textRange": "Tartomány", "SSE.Views.PrintSettings.textRepeat": "Ismétlés...", "SSE.Views.PrintSettings.textRepeatLeft": "Oszlopok ismétlése a bal oldalon", "SSE.Views.PrintSettings.textRepeatTop": "Oszlopok ismétlése felül", "SSE.Views.PrintSettings.textSelection": "Választás", "SSE.Views.PrintSettings.textSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textShowDetails": "Részletek", "SSE.Views.PrintSettings.textShowGrid": "Rácsvonalak megjelenítése", "SSE.Views.PrintSettings.textShowHeadings": "Sorok és oszlopok címsorainak megjelenítése", "SSE.Views.PrintSettings.textTitle": "Nyomtatási beállí<PERSON>ok", "SSE.Views.PrintSettings.textTitlePDF": "PDF beállítások", "SSE.Views.PrintSettings.textTo": "to", "SSE.Views.PrintSettings.txtMarginsLast": "Last Custom", "SSE.Views.PrintTitlesDialog.textFirstCol": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textFirstRow": "<PERSON><PERSON>ő sor", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Rögzített oszlopok", "SSE.Views.PrintTitlesDialog.textFrozenRows": "R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sorok", "SSE.Views.PrintTitlesDialog.textInvalidRange": "HIBA! Érvénytelen cellatartomány", "SSE.Views.PrintTitlesDialog.textLeft": "Oszlopok ismétlése a bal oldalon", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Ne ismételje meg", "SSE.Views.PrintTitlesDialog.textRepeat": "Ismétlés...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Tartomány kiválasztása", "SSE.Views.PrintTitlesDialog.textTitle": "Címek nyomtatása", "SSE.Views.PrintTitlesDialog.textTop": "Oszlopok ismétlése felül", "SSE.Views.PrintWithPreview.txtActiveSheets": "Active sheets", "SSE.Views.PrintWithPreview.txtActualSize": "Tényleges méret", "SSE.Views.PrintWithPreview.txtAllSheets": "<PERSON><PERSON> munk<PERSON>p", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Minden lapra alkalmazandó", "SSE.Views.PrintWithPreview.txtBothSides": "Print on both sides", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "Flip pages on long edge", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "Flip pages on short edge", "SSE.Views.PrintWithPreview.txtBottom": "Alul", "SSE.Views.PrintWithPreview.txtCopies": "Copies", "SSE.Views.PrintWithPreview.txtCurrentSheet": "<PERSON><PERSON><PERSON><PERSON><PERSON> munk<PERSON>", "SSE.Views.PrintWithPreview.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCustomOptions": "Egy<PERSON>i <PERSON>é<PERSON>k", "SSE.Views.PrintWithPreview.txtEmptyTable": "<PERSON><PERSON><PERSON> n<PERSON> tarta<PERSON>, mert a táblázat üres", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "First page number:", "SSE.Views.PrintWithPreview.txtFitCols": "Illessze az összes oszlopot egy oldalon", "SSE.Views.PrintWithPreview.txtFitPage": "Illessze a munkalapot egy oldalra", "SSE.Views.PrintWithPreview.txtFitRows": "Illessze az összes sort egy oldalon", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Rácsvonalak és címsorok", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Fejléc/lábléc beállítások", "SSE.Views.PrintWithPreview.txtIgnore": "Nyomtatási terület mellőzése", "SSE.Views.PrintWithPreview.txtLandscape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtLeft": "<PERSON>l", "SSE.Views.PrintWithPreview.txtMargins": "Margók", "SSE.Views.PrintWithPreview.txtMarginsLast": "Last Custom", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Normal", "SSE.Views.PrintWithPreview.txtMarginsWide": "Wide", "SSE.Views.PrintWithPreview.txtOf": "-ból/ből {0}", "SSE.Views.PrintWithPreview.txtOneSide": "Print one sided", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "SSE.Views.PrintWithPreview.txtPage": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Hibás oldalszám", "SSE.Views.PrintWithPreview.txtPageOrientation": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPages": "Pages:", "SSE.Views.PrintWithPreview.txtPageSize": "<PERSON><PERSON> m<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPortrait": "Portré", "SSE.Views.PrintWithPreview.txtPrint": "Nyomtatás", "SSE.Views.PrintWithPreview.txtPrintGrid": "Rácsvonalak nyomtatása", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Sor- és oszlopfejlécek nyomtatása", "SSE.Views.PrintWithPreview.txtPrintRange": "Nyomtatási tartomány", "SSE.Views.PrintWithPreview.txtPrintSides": "Print sides", "SSE.Views.PrintWithPreview.txtPrintTitles": "Címek nyomtatása", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Print to PDF", "SSE.Views.PrintWithPreview.txtRepeat": "Ismétlés...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Oszlopok ismétlése a bal oldalon", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Oszlopok ismétlése felül", "SSE.Views.PrintWithPreview.txtRight": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSave": "Men<PERSON>s", "SSE.Views.PrintWithPreview.txtScaling": "Skálázás", "SSE.Views.PrintWithPreview.txtSelection": "Választás", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSheet": "Munkalap: {0}", "SSE.Views.PrintWithPreview.txtTo": "to", "SSE.Views.PrintWithPreview.txtTop": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.textExistName": "HIBA! Már létezik ilyen című tartomány", "SSE.Views.ProtectDialog.textInvalidName": "A tartomány címének betűvel kell kezdődnie, és csak betűket, számokat és szóközöket tartalmazhat.", "SSE.Views.ProtectDialog.textInvalidRange": "HIBA! Érvénytelen cellatartomány", "SSE.Views.ProtectDialog.textSelectData": "Adatok kiválasztása", "SSE.Views.ProtectDialog.txtAllow": "Engedélyezze a munkalap minden felhasználójának, hogy", "SSE.Views.ProtectDialog.txtAllowDescription": "You can unlock specific ranges for editing.", "SSE.Views.ProtectDialog.txtAllowRanges": "Tartományok szerkesztésének engedélyezése", "SSE.Views.ProtectDialog.txtAutofilter": "Automatikus szűrő használata", "SSE.Views.ProtectDialog.txtDelCols": "Oszlopok törlése", "SSE.Views.ProtectDialog.txtDelRows": "<PERSON>rok t<PERSON>", "SSE.Views.ProtectDialog.txtEmpty": "Ez egy szükséges mező", "SSE.Views.ProtectDialog.txtFormatCells": "Cellák formáz<PERSON>", "SSE.Views.ProtectDialog.txtFormatCols": "Oszlopok formázása", "SSE.Views.ProtectDialog.txtFormatRows": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtIncorrectPwd": "A megerősítő jelszó nem azonos", "SSE.Views.ProtectDialog.txtInsCols": "Oszlopok beszúrása", "SSE.Views.ProtectDialog.txtInsHyper": "Hiperhivat<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtInsRows": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtObjs": "Objektum szerkesztése", "SSE.Views.ProtectDialog.txtOptional": "opcionális", "SSE.Views.ProtectDialog.txtPassword": "Je<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPivot": "Pivot táblázat és pivot diagram használata", "SSE.Views.ProtectDialog.txtProtect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRange": "Tartomány", "SSE.Views.ProtectDialog.txtRangeName": "Cím", "SSE.Views.ProtectDialog.txtRepeat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtScen": "Forgatókönyvek szerkesztése", "SSE.Views.ProtectDialog.txtSelLocked": "Zárolt cellák kiválasztása", "SSE.Views.ProtectDialog.txtSelUnLocked": "Feloldott cellákat kiválasztása", "SSE.Views.ProtectDialog.txtSheetDescription": "Akadályozza meg a mások nem kívánt változtatásait azáltal, hogy korlátozza ezen személyek szerkesztési képességét.", "SSE.Views.ProtectDialog.txtSheetTitle": "<PERSON><PERSON><PERSON> védelme", "SSE.Views.ProtectDialog.txtSort": "Rendez<PERSON>", "SSE.Views.ProtectDialog.txtWarning": "Figyelem: ha <PERSON><PERSON><PERSON> vagy el<PERSON>lej<PERSON> a j<PERSON>, annak v<PERSON>llítására nincs mód. Tárolja biztonsá<PERSON> he<PERSON>.", "SSE.Views.ProtectDialog.txtWBDescription": "<PERSON> meg szeret<PERSON><PERSON>, hogy más felhasz<PERSON><PERSON><PERSON>k megtekintsék a rejtett munkalapokat, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, illet<PERSON>ek munkalapokat, j<PERSON><PERSON><PERSON><PERSON> védheti a munkafüzet szerkezetét.", "SSE.Views.ProtectDialog.txtWBTitle": "Munkafüzet struktúrájának védelme", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "Anonymous", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Anyone", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Edit", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Denied", "SSE.Views.ProtectedRangesEditDlg.textCanView": "View", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "The range title must begin with a letter and may only contain letters, numbers, and spaces.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Remove", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "Select data", "SSE.Views.ProtectedRangesEditDlg.textYou": "you", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Access to range", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "This field is required", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "Protect", "SSE.Views.ProtectedRangesEditDlg.txtRange": "Range", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "Title", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Only you can edit this range", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Start typing name or email", "SSE.Views.ProtectedRangesManagerDlg.guestText": "Guest", "SSE.Views.ProtectedRangesManagerDlg.lockText": "Locked", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "Delete", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "Edit", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "No protected ranges have been created yet.<br>Create at least one protected range and it will appear in this field.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "Filter", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "Minden", "SSE.Views.ProtectedRangesManagerDlg.textNew": "New", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Protect sheet", "SSE.Views.ProtectedRangesManagerDlg.textRange": "Range", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "You can restrict editing or viewing ranges to selected people.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "Title", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Access", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Denied", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "Edit", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "Edit range", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "New range", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Protected ranges", "SSE.Views.ProtectedRangesManagerDlg.txtView": "View", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Biztosan szeretné törölni a védett tartományt {0}<br><PERSON><PERSON><PERSON><PERSON> van táblázatkezelői jogosultsága szerkesztheti a tartalmat a tartományon belül.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Are you sure you want to delete the protected ranges?<br>Anyone who has edit access to the spreadsheet will be able to edit content in those ranges.", "SSE.Views.ProtectRangesDlg.guestText": "Vendég", "SSE.Views.ProtectRangesDlg.lockText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textDelete": "Törlés", "SSE.Views.ProtectRangesDlg.textEdit": "Szerkesztés", "SSE.Views.ProtectRangesDlg.textEmpty": "Nincsenek szerkeszthető tartományok.", "SSE.Views.ProtectRangesDlg.textNew": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textProtect": "<PERSON><PERSON><PERSON> védelme", "SSE.Views.ProtectRangesDlg.textPwd": "Je<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRange": "Tartomány", "SSE.Views.ProtectRangesDlg.textRangesDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON> feloldott tartományok, ha a munkalap védett (ez csak zárolt cellák esetén működik)", "SSE.Views.ProtectRangesDlg.textTitle": "Cím", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Ezt az elemet egy másik felhasználó szerkeszti.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Tartomány szerkesztése", "SSE.Views.ProtectRangesDlg.txtNewRange": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.txtNo": "Nem", "SSE.Views.ProtectRangesDlg.txtTitle": "Tartományok szerkesztésének engedélyezése a felhasználók számára", "SSE.Views.ProtectRangesDlg.txtYes": "Igen", "SSE.Views.ProtectRangesDlg.warnDelete": "<PERSON><PERSON><PERSON>, hogy törölni akarja a {0} nevet?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Oszlopok", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Ismétlődő értékek törléséhez jelöljön ki egy vagy több osz<PERSON>ot, am<PERSON>ek duplikátumokat tartalmaznak.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "<PERSON><PERSON> adataim fejléccel rendelkeznek", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Összes kiválasztása", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Ismétlődések eltávolítása", "SSE.Views.RightMenu.ariaRightMenu": "Right menu", "SSE.Views.RightMenu.txtCellSettings": "<PERSON><PERSON>", "SSE.Views.RightMenu.txtChartSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtImageSettings": "Képbeállítások", "SSE.Views.RightMenu.txtParagraphSettings": "Bekezdés beállítások", "SSE.Views.RightMenu.txtPivotSettings": "Pivot Tábla beállítások", "SSE.Views.RightMenu.txtSettings": "Általános be<PERSON>", "SSE.Views.RightMenu.txtShapeSettings": "Alakzat beállítások", "SSE.Views.RightMenu.txtSignatureSettings": "Aláí<PERSON><PERSON>", "SSE.Views.RightMenu.txtSlicerSettings": "Elválasztó be<PERSON>llí<PERSON>ok", "SSE.Views.RightMenu.txtSparklineSettings": "Értékgörbe be<PERSON>ll<PERSON>ok", "SSE.Views.RightMenu.txtTableSettings": "Tábl<PERSON><PERSON><PERSON> be<PERSON>llí<PERSON>ok", "SSE.Views.RightMenu.txtTextArtSettings": "TextArt beáll<PERSON>", "SSE.Views.ScaleDialog.textAuto": "Automatikus", "SSE.Views.ScaleDialog.textError": "A megadott érték helytelen.", "SSE.Views.ScaleDialog.textFewPages": "Oldalak", "SSE.Views.ScaleDialog.textFitTo": "Illesztés", "SSE.Views.ScaleDialog.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textManyPages": "Oldalak", "SSE.Views.ScaleDialog.textOnePage": "<PERSON><PERSON>", "SSE.Views.ScaleDialog.textScaleTo": "Méretezés", "SSE.Views.ScaleDialog.textTitle": "Mérték beállítások", "SSE.Views.ScaleDialog.textWidth": "Szélesség", "SSE.Views.SetValueDialog.txtMaxText": "A mező maximális értéke {0}", "SSE.Views.SetValueDialog.txtMinText": "A mező minimális értéke {0}", "SSE.Views.ShapeSettings.strBackground": "Háttérsz<PERSON>", "SSE.Views.ShapeSettings.strChange": "Automatikus forma váltás", "SSE.Views.ShapeSettings.strColor": "Szín", "SSE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strForeground": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strPattern": "Minta", "SSE.Views.ShapeSettings.strShadow": "Árnyék mutatása", "SSE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "Á<PERSON><PERSON><PERSON>z<PERSON>", "SSE.Views.ShapeSettings.strType": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "SSE.Views.ShapeSettings.textAdvanced": "Speciális beállítások megjelenítése", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "A megadott érték helytelen.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy számértéket 0 és 1584 között", "SSE.Views.ShapeSettings.textColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textEditPoints": "Edit points", "SSE.Views.ShapeSettings.textEditShape": "Edit shape", "SSE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON>s minta", "SSE.Views.ShapeSettings.textEyedropper": "Eyedropper", "SSE.Views.ShapeSettings.textFlip": "Tükröz", "SSE.Views.ShapeSettings.textFromFile": "Fájlból", "SSE.Views.ShapeSettings.textFromStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromUrl": "URL-ből", "SSE.Views.ShapeSettings.textGradient": "Színátmeneti pontok", "SSE.Views.ShapeSettings.textGradientFill": "Színátmenetes kitöltés", "SSE.Views.ShapeSettings.textHint270": "Elforgat balra 90 fokkal", "SSE.Views.ShapeSettings.textHint90": "Elforgat jobbra 90 fokkal", "SSE.Views.ShapeSettings.textHintFlipH": "Vízszintesen tükröz", "SSE.Views.ShapeSettings.textHintFlipV": "Függőlegesen tükröz", "SSE.Views.ShapeSettings.textImageTexture": "<PERSON><PERSON><PERSON> vag<PERSON>", "SSE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textMoreColors": "More colors", "SSE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textNoShadow": "No Shadow", "SSE.Views.ShapeSettings.textOriginalSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textPatternFill": "Minta", "SSE.Views.ShapeSettings.textPosition": "Pozíció", "SSE.Views.ShapeSettings.textRadial": "Sugárirányú", "SSE.Views.ShapeSettings.textRecentlyUsed": "<PERSON>an<PERSON><PERSON>", "SSE.Views.ShapeSettings.textRotate90": "Elforgat 90 fokkal", "SSE.Views.ShapeSettings.textRotation": "Forgatás", "SSE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON>ztása", "SSE.Views.ShapeSettings.textSelectTexture": "Kiválaszt", "SSE.Views.ShapeSettings.textShadow": "Shadow", "SSE.Views.ShapeSettings.textStretch": "Nyújt", "SSE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textTexture": "Textúrából", "SSE.Views.ShapeSettings.textTile": "Csempe", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Színátmenet pont hozzáadása", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Színátmenet pont eltávolítása", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCanvas": "Vászon", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "Sötét s<PERSON>", "SSE.Views.ShapeSettings.txtGrain": "Szemcse", "SSE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "Szürke papír", "SSE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtLeather": "Bőr", "SSE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "Fa", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Oszlopok", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Szöveg távolság", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "<PERSON><PERSON>s moz<PERSON> vagy méretezés cellákkal", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Alternatív szöveg", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Le<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "A vizuális objektumok alternatív szövegalapú ábrázolása, amely a látás vagy kognitív károsodottak számára is o<PERSON><PERSON><PERSON><PERSON>, hogy segítsen nekik job<PERSON> me<PERSON>, hogy milyen <PERSON>, al<PERSON><PERSON><PERSON>, diagram vagy táblázat látható.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Cím", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Automatikusan illeszkedő", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON>z<PERSON><PERSON> méret", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Kezdő stílus", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Ferde", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Alsó", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Kisbetű - Nagybetű", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Oszlopok száma", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Végs<PERSON> méret", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Végső stílus", "SSE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Tükrözött", "SSE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Vízszintesen", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Csatlakozás típusa", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON>l", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON> s<PERSON>í<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Mozgatás cellákkal méretezés nélkül", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Túlcsordulhasson a szöveg az alakzaton", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Alakzat átméretezése a szöveghez", "SSE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Forgatás", "SSE.Views.ShapeSettingsAdvanced.textRound": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Cella igazítás", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Hasábtávolság", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Szögletes", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Szövegdoboz", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Alakzat - Speci<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTop": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Mozgatás és méretezés cellákkal", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Függőlegesen", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Súlyok és nyilak", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Szélesség", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Figyelmeztetés", "SSE.Views.SignatureSettings.strDelete": "Aláírás eltávolítása", "SSE.Views.SignatureSettings.strDetails": "Aláírás r<PERSON>zletek", "SSE.Views.SignatureSettings.strInvalid": "Érvénytelen al<PERSON>í<PERSON>", "SSE.Views.SignatureSettings.strRequested": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSetup": "Aláí<PERSON><PERSON>", "SSE.Views.SignatureSettings.strSign": "Aláír", "SSE.Views.SignatureSettings.strSignature": "Aláírás", "SSE.Views.SignatureSettings.strSigner": "Aláíró", "SSE.Views.SignatureSettings.strValid": "Érvényes aláírások", "SSE.Views.SignatureSettings.txtContinueEditing": "Mindenképp szerkeszt", "SSE.Views.SignatureSettings.txtEditWarning": "A szerkesztés eltávolítja az aláírásokat a munkafüzetről.<br>Folytassuk?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Eltávolítsuk ezt az aláírást?<br><PERSON><PERSON> v<PERSON>.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Ezt a munkafüzetet alá kell írni.", "SSE.Views.SignatureSettings.txtSigned": "Érvényes aláírásokat adtak hozzá a munkafüzethez. A táblázatkezelő védve van a szerkesztéstől.", "SSE.Views.SignatureSettings.txtSignedInvalid": "A munkafüzetben található digitális aláírások némelyike érvénytelen vagy nem ellenőrizhető. A táblázatkezelő védve van a szerkesztéstől.", "SSE.Views.SlicerAddDialog.textColumns": "Oszlopok", "SSE.Views.SlicerAddDialog.txtTitle": "Elválasztók be<PERSON>ú<PERSON>", "SSE.Views.SlicerSettings.strHideNoData": "<PERSON><PERSON> nélküli elemek elrejté<PERSON>", "SSE.Views.SlicerSettings.strIndNoData": "Vizuálisan jelezze az adatokat nem tartalmazó elemeket", "SSE.Views.SlicerSettings.strShowDel": "Adatforrásból törölt elemek megjelenítése", "SSE.Views.SlicerSettings.strShowNoData": "Utolsó adatok nélküli elemek megjelenítése", "SSE.Views.SlicerSettings.strSorting": "Rendezés és szűrés", "SSE.Views.SlicerSettings.textAdvanced": "Speciális beállítások megjelenítése", "SSE.Views.SlicerSettings.textAsc": "Növekvő", "SSE.Views.SlicerSettings.textAZ": "A-tól Z-ig", "SSE.Views.SlicerSettings.textButtons": "Gombok", "SSE.Views.SlicerSettings.textColumns": "Oszlopok", "SSE.Views.SlicerSettings.textDesc": "Csökkenő", "SSE.Views.SlicerSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textHor": "Vízszintes", "SSE.Views.SlicerSettings.textKeepRatio": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textLargeSmall": "legnagyobbtól a legkisebbig", "SSE.Views.SlicerSettings.textLock": "Átméretezés vagy <PERSON>the<PERSON> letilt<PERSON>", "SSE.Views.SlicerSettings.textNewOld": "legújabbtól a legrégebbiig", "SSE.Views.SlicerSettings.textOldNew": "legrégibbtől a legújabbig", "SSE.Views.SlicerSettings.textPosition": "Pozíció", "SSE.Views.SlicerSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textSmallLarge": "legkisebbtől a legnagyobbig", "SSE.Views.SlicerSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textVert": "Függőleges", "SSE.Views.SlicerSettings.textWidth": "Szélesség", "SSE.Views.SlicerSettings.textZA": "Z-től A-ig", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Gombok", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Oszlopok", "SSE.Views.SlicerSettingsAdvanced.strHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "<PERSON><PERSON> nélküli elemek elrejté<PERSON>", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Vizuálisan jelezze az adatokat nem tartalmazó elemeket", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Re<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Adatforrásból törölt elemek megjelenítése", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Fejléc me<PERSON>jelení<PERSON>", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Utolsó adatok nélküli elemek megjelenítése", "SSE.Views.SlicerSettingsAdvanced.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Rendezés és szűrés", "SSE.Views.SlicerSettingsAdvanced.strStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strWidth": "Szélesség", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "<PERSON><PERSON>s moz<PERSON> vagy méretezés cellákkal", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Alternatív szöveg", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Le<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "A vizuális objektumok alternatív szövegalapú ábrázolása, amely a látás vagy kognitív károsodottak számára is o<PERSON><PERSON><PERSON><PERSON>, hogy segítsen nekik job<PERSON> me<PERSON>, hogy milyen <PERSON>, al<PERSON><PERSON><PERSON>, diagram vagy táblázat látható.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Cím", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Növekvő", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A-tól Z-ig", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Csökkenő", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "A függvényekben használt név", "SSE.Views.SlicerSettingsAdvanced.textHeader": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "legnagyobbtól a legkisebbig", "SSE.Views.SlicerSettingsAdvanced.textName": "Név", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "legújabbtól a legrégebbiig", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "legrégibbtől a legújabbig", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Mozgatás cellákkal méretezés nélkül", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "legkisebbtől a legnagyobbig", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Cella igazítás", "SSE.Views.SlicerSettingsAdvanced.textSort": "Rendez<PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "<PERSON><PERSON><PERSON> neve", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Elválasztó - Speci<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Mozgatás és méretezés cellákkal", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z-től A-ig", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Ez egy szükséges mező", "SSE.Views.SortDialog.errorEmpty": "Minden rendezési feltételhez oszlopot vagy sort kell megadni.", "SSE.Views.SortDialog.errorMoreOneCol": "<PERSON><PERSON><PERSON>, mint egy osz<PERSON> van kiválasztva.", "SSE.Views.SortDialog.errorMoreOneRow": "<PERSON><PERSON><PERSON>, mint egy sor van kiválasztva.", "SSE.Views.SortDialog.errorNotOriginalCol": "A kiválasztott oszlop nem az eredeti választott tartományban van.", "SSE.Views.SortDialog.errorNotOriginalRow": "A kiválasztott sor nem az eredeti választott tartományban van.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 többször is ugyanazzal a színnel van szortírozva.<br>Tör<PERSON>lje a duplikált szortírozási feltételeket, és próbálja újra.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 sor vagy o<PERSON><PERSON> többször van érték szerint rendezve.<br>Távolítsa el a duplikált rendezési feltételeket, és próbálja újra.", "SSE.Views.SortDialog.textAsc": "Növekvő", "SSE.Views.SortDialog.textAuto": "Automatikus", "SSE.Views.SortDialog.textAZ": "A-tól Z-ig", "SSE.Views.SortDialog.textBelow": "<PERSON><PERSON>", "SSE.Views.SortDialog.textBtnCopy": "Copy", "SSE.Views.SortDialog.textBtnDelete": "Delete", "SSE.Views.SortDialog.textBtnNew": "New", "SSE.Views.SortDialog.textCellColor": "Cellaszín", "SSE.Views.SortDialog.textColumn": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDesc": "Csökkenő", "SSE.Views.SortDialog.textDown": "<PERSON><PERSON><PERSON> lentebb mozgatása", "SSE.Views.SortDialog.textFontColor": "Betűszín", "SSE.Views.SortDialog.textLeft": "<PERSON>l", "SSE.Views.SortDialog.textLevels": "Levels", "SSE.Views.SortDialog.textMoreCols": "(<PERSON><PERSON><PERSON> o<PERSON>...)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON><PERSON> sor...)", "SSE.Views.SortDialog.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textOptions": "Beállítások", "SSE.Views.SortDialog.textOrder": "Rendez<PERSON>", "SSE.Views.SortDialog.textRight": "<PERSON><PERSON>", "SSE.Views.SortDialog.textRow": "Sor", "SSE.Views.SortDialog.textSort": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textSortBy": "Rendez<PERSON>", "SSE.Views.SortDialog.textThenBy": "Aztán", "SSE.Views.SortDialog.textTop": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textUp": "Szint feljebb mozgatása", "SSE.Views.SortDialog.textValues": "<PERSON>rté<PERSON><PERSON>", "SSE.Views.SortDialog.textZA": "Z-től A-ig", "SSE.Views.SortDialog.txtInvalidRange": "Valótlan cellatartomány.", "SSE.Views.SortDialog.txtTitle": "<PERSON><PERSON>", "SSE.Views.SortFilterDialog.textAsc": "Növekvő (A-től Z-ig)", "SSE.Views.SortFilterDialog.textDesc": "Csökkenő (Z-től A-ig)", "SSE.Views.SortFilterDialog.textNoSort": "No sort", "SSE.Views.SortFilterDialog.txtTitle": "Rendez<PERSON>", "SSE.Views.SortFilterDialog.txtTitleValue": "Sort by value", "SSE.Views.SortOptionsDialog.textCase": "Nagy- és kisbetű érzékeny", "SSE.Views.SortOptionsDialog.textHeaders": "<PERSON><PERSON> adataim fejléccel rendelkeznek", "SSE.Views.SortOptionsDialog.textLeftRight": "Balról jobbra rendezés", "SSE.Views.SortOptionsDialog.textOrientation": "Tájolás", "SSE.Views.SortOptionsDialog.textTitle": "Rendezési lehetőségek", "SSE.Views.SortOptionsDialog.textTopBottom": "Rendezés fent<PERSON><PERSON> lentre", "SSE.Views.SpecialPasteDialog.textAdd": "Hozzáadás", "SSE.Views.SpecialPasteDialog.textAll": "Minden", "SSE.Views.SpecialPasteDialog.textBlanks": "Üresek kihagyása", "SSE.Views.SpecialPasteDialog.textColWidth": "Oszlopszélességek", "SSE.Views.SpecialPasteDialog.textComments": "Megjegyzések", "SSE.Views.SpecialPasteDialog.textDiv": "Felosztás", "SSE.Views.SpecialPasteDialog.textFFormat": "Függvények és formázás", "SSE.Views.SpecialPasteDialog.textFNFormat": "Függvények és számformátumok", "SSE.Views.SpecialPasteDialog.textFormats": "Formátumok", "SSE.Views.SpecialPasteDialog.textFormulas": "Függvények", "SSE.Views.SpecialPasteDialog.textFWidth": "Függvények és oszlopszélességek", "SSE.Views.SpecialPasteDialog.textMult": "Többszörözés", "SSE.Views.SpecialPasteDialog.textNone": "<PERSON><PERSON><PERSON> sem", "SSE.Views.SpecialPasteDialog.textOperation": "Művelet", "SSE.Views.SpecialPasteDialog.textPaste": "Beillesztés", "SSE.Views.SpecialPasteDialog.textSub": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textTitle": "Spec<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textTranspose": "Transzponálás", "SSE.Views.SpecialPasteDialog.textValues": "<PERSON>rté<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textVFormat": "Értékek és formázás", "SSE.Views.SpecialPasteDialog.textVNFormat": "Értékek és számformátumok", "SSE.Views.SpecialPasteDialog.textWBorders": "Mind a szegélyek kivételével", "SSE.Views.Spellcheck.noSuggestions": "<PERSON><PERSON><PERSON>s<PERSON>gi j<PERSON>", "SSE.Views.Spellcheck.textChange": "Módosítás", "SSE.Views.Spellcheck.textChangeAll": "<PERSON><PERSON> m<PERSON>", "SSE.Views.Spellcheck.textIgnore": "Mellőzés", "SSE.Views.Spellcheck.textIgnoreAll": "<PERSON><PERSON>", "SSE.Views.Spellcheck.txtAddToDictionary": "Hozzáadás a szótárhoz", "SSE.Views.Spellcheck.txtClosePanel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.txtComplete": "A helyesírás-ellenőrzés kész", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Szót<PERSON><PERSON> n<PERSON>", "SSE.Views.Spellcheck.txtNextTip": "Következő szóra lépés", "SSE.Views.Spellcheck.txtSpelling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(A végére lépni)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "Create a copy", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(Create new spreadsheet)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "<PERSON><PERSON><PERSON>ez a munkalap el<PERSON>tt", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Spreadsheet", "SSE.Views.Statusbar.filteredRecordsText": "{0} az {1} bejegyzésekből kiszűrve", "SSE.Views.Statusbar.filteredText": "Szű<PERSON><PERSON> mód", "SSE.Views.Statusbar.itemAverage": "Átlag", "SSE.Views.Statusbar.itemCount": "Dar<PERSON>", "SSE.Views.Statusbar.itemDelete": "T<PERSON>r<PERSON><PERSON>", "SSE.Views.Statusbar.itemHidden": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemHide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemInsert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMaximum": "Maximum", "SSE.Views.Statusbar.itemMinimum": "Minimum", "SSE.Views.Statusbar.itemMoveOrCopy": "Move or copy", "SSE.Views.Statusbar.itemProtect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemRename": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemStatus": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemSum": "Összeg", "SSE.Views.Statusbar.itemTabColor": "Lap színe", "SSE.Views.Statusbar.itemUnProtect": "Vé<PERSON>em me<PERSON>zünteté<PERSON>", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Az ilyen névvel ellátott munkafüzet már l<PERSON>.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "A munkalap neve nem tartalmazhat /\\*?[]: karaktereket.", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Munkalap név", "SSE.Views.Statusbar.selectAllSheets": "Minden munkalap kiválasztása", "SSE.Views.Statusbar.sheetIndexText": "<PERSON><PERSON>lap {0}/{1}", "SSE.Views.Statusbar.textAverage": "Átlag", "SSE.Views.Statusbar.textCount": "Dar<PERSON>", "SSE.Views.Statusbar.textMax": "Maximum", "SSE.Views.Statusbar.textMin": "Minimum", "SSE.Views.Statusbar.textNewColor": "Új egyedi szín hozzáadása", "SSE.Views.Statusbar.textNoColor": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textSum": "Összeg", "SSE.Views.Statusbar.tipAddTab": "<PERSON><PERSON><PERSON> ho<PERSON>", "SSE.Views.Statusbar.tipFirst": "Első laphoz gö<PERSON>", "SSE.Views.Statusbar.tipLast": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipListOfSheets": "Munkalapok listája", "SSE.Views.Statusbar.tipNext": "Laplista jobbra gö<PERSON>", "SSE.Views.Statusbar.tipPrev": "La<PERSON><PERSON><PERSON> balra <PERSON>", "SSE.Views.Statusbar.tipZoomFactor": "Zoom", "SSE.Views.Statusbar.tipZoomIn": "Nagyítás", "SSE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.ungroupSheets": "Munkalapok szétválasztása", "SSE.Views.Statusbar.zoomText": "Zoom {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "A műveletet nem lehetett elvégezni a kiválasztott cellatartományban.<br>Válasszon egy egységes adattartományt, amely el<PERSON> a meglévő cellától, és próbálja újra.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "A műveletet nem sikerült befejezni a kiválasztott cellatartományban.<br>Válasszon ki egy tartományt, hogy az első táblázat sora ugyanabban a sorban legyen, és az eredményül kapott táblázat átfedje az aktuális sort.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "A műveletet nem si<PERSON>ült befejezni a kiválasztott cellatartományban.<br>Válasszon ki egy olyan tartom<PERSON>, amely nem tartalmaz más táblákat.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "A táblázatokban nem használhatók többcellás tömbfüggvények.", "SSE.Views.TableOptionsDialog.txtEmpty": "Ez egy szükséges mező", "SSE.Views.TableOptionsDialog.txtFormat": "Táblázat létrehozása", "SSE.Views.TableOptionsDialog.txtInvalidRange": "HIBA! Érvénytelen cellatartomány", "SSE.Views.TableOptionsDialog.txtNote": "A fejléceknek ugyanabban a sorban kell maradniuk, és a kapott táblázattartománynak át kell fednie az eredeti táblázattartományt.", "SSE.Views.TableOptionsDialog.txtTitle": "Cím", "SSE.Views.TableSettings.deleteColumnText": "Oszlop törlése", "SSE.Views.TableSettings.deleteRowText": "<PERSON><PERSON> t<PERSON>", "SSE.Views.TableSettings.deleteTableText": "Táblázat törlése", "SSE.Views.TableSettings.insertColumnLeftText": "<PERSON><PERSON><PERSON> balra", "SSE.Views.TableSettings.insertColumnRightText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.insertRowAboveText": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> fel", "SSE.Views.TableSettings.insertRowBelowText": "<PERSON><PERSON> <PERSON>", "SSE.Views.TableSettings.notcriticalErrorTitle": "Figyelmeztetés", "SSE.Views.TableSettings.selectColumnText": "Teljes o<PERSON>lop k<PERSON>lasztása", "SSE.Views.TableSettings.selectDataText": "Válassza az oszlopadatokat", "SSE.Views.TableSettings.selectRowText": "<PERSON><PERSON>las<PERSON>tás<PERSON>", "SSE.Views.TableSettings.selectTableText": "Táblázat kiválasztása", "SSE.Views.TableSettings.textActions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textAdvanced": "Speciális beállítások megjelenítése", "SSE.Views.TableSettings.textBanded": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textColumns": "Oszlopok", "SSE.Views.TableSettings.textConvertRange": "Átalakítás cellatartományra", "SSE.Views.TableSettings.textEdit": "Sorok és oszlopok", "SSE.Views.TableSettings.textEmptyTemplate": "<PERSON>nc<PERSON> sablon", "SSE.Views.TableSettings.textExistName": "HIBA! Tartomány már létezik ilyen névvel", "SSE.Views.TableSettings.textFilter": "Szűré<PERSON> gomb", "SSE.Views.TableSettings.textFirst": "Első", "SSE.Views.TableSettings.textHeader": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textInvalidName": "HIBA! Érvénytelen táblázatnév", "SSE.Views.TableSettings.textIsLocked": "Ezt az elemet egy másik felhasználó szerkeszti.", "SSE.Views.TableSettings.textLast": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textLongOperation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textPivot": "Kimutatás tábla beszúrása", "SSE.Views.TableSettings.textRemDuplicates": "Ismétlődések eltávolítása", "SSE.Views.TableSettings.textReservedName": "A használni kívánt név már hivatkozásra került cella függvényekben. Kérjük, használjon más nevet.", "SSE.Views.TableSettings.textResize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mé<PERSON>", "SSE.Views.TableSettings.textRows": "So<PERSON>", "SSE.Views.TableSettings.textSelectData": "Adatok kiválasztása", "SSE.Views.TableSettings.textSlicer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textTableName": "Táblázat név", "SSE.Views.TableSettings.textTemplate": "Választás a sablonokból", "SSE.Views.TableSettings.textTotal": "Összesen", "SSE.Views.TableSettings.txtGroupTable_Custom": "Custom", "SSE.Views.TableSettings.txtGroupTable_Dark": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.txtGroupTable_Light": "<PERSON>il<PERSON><PERSON>", "SSE.Views.TableSettings.txtGroupTable_Medium": "Medium", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Sö<PERSON>t táblázat stílus", "SSE.Views.TableSettings.txtTable_TableStyleLight": "Table style light", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "Table style medium", "SSE.Views.TableSettings.warnLongOperation": "A végrehajtani kívánt művelet meglehetősen sok időt vehet igénybe.<br>Biztosan folytatni kívánja?", "SSE.Views.TableSettingsAdvanced.textAlt": "Alternatív szöveg", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Le<PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textAltTip": "A vizuális objektumok alternatív szövegalapú ábrázolása, amely a látás vagy kognitív károsodottak számára is o<PERSON><PERSON><PERSON><PERSON>, hogy segítsen nekik job<PERSON> me<PERSON>, hogy milyen <PERSON>, al<PERSON><PERSON><PERSON>, diagram vagy táblázat látható.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Cím", "SSE.Views.TableSettingsAdvanced.textTitle": "Táblázat - <PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strBackground": "Háttérsz<PERSON>", "SSE.Views.TextArtSettings.strColor": "Szín", "SSE.Views.TextArtSettings.strFill": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strForeground": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strPattern": "Minta", "SSE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "Á<PERSON><PERSON><PERSON>z<PERSON>", "SSE.Views.TextArtSettings.strType": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "A megadott érték helytelen.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy számértéket 0 és 1584 között", "SSE.Views.TextArtSettings.textColor": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON>s minta", "SSE.Views.TextArtSettings.textFromFile": "Fájlból", "SSE.Views.TextArtSettings.textFromUrl": "URL-ből", "SSE.Views.TextArtSettings.textGradient": "Színátmeneti pontok", "SSE.Views.TextArtSettings.textGradientFill": "Színátmenetes kitöltés", "SSE.Views.TextArtSettings.textImageTexture": "<PERSON><PERSON><PERSON> vag<PERSON>", "SSE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textPatternFill": "Minta", "SSE.Views.TextArtSettings.textPosition": "Pozíció", "SSE.Views.TextArtSettings.textRadial": "Sugárirányú", "SSE.Views.TextArtSettings.textSelectTexture": "Kiválaszt", "SSE.Views.TextArtSettings.textStretch": "Nyújt", "SSE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textTemplate": "Sablon", "SSE.Views.TextArtSettings.textTexture": "Textúrából", "SSE.Views.TextArtSettings.textTile": "Csempe", "SSE.Views.TextArtSettings.textTransform": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Színátmeneti pont hozzáadása", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Színátmenet pont eltávolítása", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCanvas": "Vászon", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "Sötét s<PERSON>", "SSE.Views.TextArtSettings.txtGrain": "Szemcse", "SSE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "Szürke papír", "SSE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "Bőr", "SSE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtWood": "Fa", "SSE.Views.Toolbar.capBtnAddComment": "Megjegyzés hozzáadása", "SSE.Views.Toolbar.capBtnColorSchemas": "Színséma", "SSE.Views.Toolbar.capBtnComment": "Megjegyzés", "SSE.Views.Toolbar.capBtnInsHeader": "Fejléc & Lábléc", "SSE.Views.Toolbar.capBtnInsSlicer": "Elválasztó", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Szimbólum", "SSE.Views.Toolbar.capBtnMargins": "Margók", "SSE.Views.Toolbar.capBtnPageBreak": "Breaks", "SSE.Views.Toolbar.capBtnPageOrient": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageSize": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintArea": "Nyomtatási terület", "SSE.Views.Toolbar.capBtnPrintTitles": "Címek nyomtatása", "SSE.Views.Toolbar.capBtnScale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capImgAlign": "Igazít", "SSE.Views.Toolbar.capImgBackward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capImgForward": "<PERSON><PERSON><PERSON>z", "SSE.Views.Toolbar.capImgGroup": "Csoport", "SSE.Views.Toolbar.capInsertChart": "Diagram", "SSE.Views.Toolbar.capInsertChartRecommend": "Recommended Chart", "SSE.Views.Toolbar.capInsertEquation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertHyperlink": "Hivatkozás", "SSE.Views.Toolbar.capInsertImage": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertShape": "Alakzat", "SSE.Views.Toolbar.capInsertSpark": "Vonaldiagram", "SSE.Views.Toolbar.capInsertTable": "T<PERSON>b<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertText": "Szövegdoboz", "SSE.Views.Toolbar.capInsertTextart": "Text Art", "SSE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniCapitalizeWords": "Capitalize Each Word", "SSE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.mniImageFromStorage": "Kép a tárolóból", "SSE.Views.Toolbar.mniImageFromUrl": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.mniLowerCase": "lowercase", "SSE.Views.Toolbar.mniSentenceCase": "Sentence case.", "SSE.Views.Toolbar.mniToggleCase": "tOGGLE cASE", "SSE.Views.Toolbar.mniUpperCase": "UPPERCASE", "SSE.Views.Toolbar.textAddPrintArea": "Nyomtatási területhez hozzáad", "SSE.Views.Toolbar.textAlignBottom": "Alsóhoz igazít", "SSE.Views.Toolbar.textAlignCenter": "Középre igazít", "SSE.Views.Toolbar.textAlignJust": "Sorkizárt", "SSE.Views.Toolbar.textAlignLeft": "Balra igazít", "SSE.Views.Toolbar.textAlignMiddle": "Középhez igazít", "SSE.Views.Toolbar.textAlignRight": "Jobbra igazít", "SSE.Views.Toolbar.textAlignTop": "<PERSON><PERSON><PERSON><PERSON>re rendez ", "SSE.Views.Toolbar.textAllBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "SSE.Views.Toolbar.textAuto": "Automatikus", "SSE.Views.Toolbar.textAutoColor": "Automatikus", "SSE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "SSE.Views.Toolbar.textBlackHeart": "Black Heart Suit", "SSE.Views.Toolbar.textBold": "F<PERSON>lkövér", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBordersStyle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBottom": "Alsó: ", "SSE.Views.Toolbar.textBottomBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBullet": "Bullet", "SSE.Views.Toolbar.textCellAlign": "Format cell alignment", "SSE.Views.Toolbar.textCenterBorders": "Belső függőleges szegélyek", "SSE.Views.Toolbar.textClearPrintArea": "Nyomási terület törlése", "SSE.Views.Toolbar.textClearRule": "Szabályok törlése", "SSE.Views.Toolbar.textClockwise": "Az óramutató járásával megegyező szög", "SSE.Views.Toolbar.textColorScales": "Színskálák", "SSE.Views.Toolbar.textCopyright": "Copyright Sign", "SSE.Views.Toolbar.textCounterCw": "Az óramutató j<PERSON><PERSON><PERSON><PERSON> el<PERSON>tes szög", "SSE.Views.Toolbar.textCustom": "Custom", "SSE.Views.Toolbar.textDataBars": "Adatsávok", "SSE.Views.Toolbar.textDegree": "Degree Sign", "SSE.Views.Toolbar.textDelLeft": "<PERSON><PERSON> he<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textDelPageBreak": "Remove page break", "SSE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "SSE.Views.Toolbar.textDelUp": "Cella he<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textDiagDownBorder": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textDiagUpBorder": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textDivision": "Division Sign", "SSE.Views.Toolbar.textDollar": "Dollar Sign", "SSE.Views.Toolbar.textDone": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textDown": "Down", "SSE.Views.Toolbar.textEditVA": "Látható terület szerkesztése", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textEntireRow": "<PERSON><PERSON><PERSON> sor", "SSE.Views.Toolbar.textEuro": "Euro Sign", "SSE.Views.Toolbar.textFewPages": "Oldalak", "SSE.Views.Toolbar.textFillLeft": "Left", "SSE.Views.Toolbar.textFillRight": "Right", "SSE.Views.Toolbar.textFormatCellFill": "Format cell fill", "SSE.Views.Toolbar.textGreaterEqual": "Greater Than Or Equal To", "SSE.Views.Toolbar.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textHideVA": "<PERSON><PERSON><PERSON><PERSON><PERSON> ter<PERSON>", "SSE.Views.Toolbar.textHorizontal": "Vízszintes szöveg", "SSE.Views.Toolbar.textInfinity": "Infinity", "SSE.Views.Toolbar.textInsDown": "Cella helyettesí<PERSON> f<PERSON>", "SSE.Views.Toolbar.textInsideBorders": "Bel<PERSON><PERSON> szegélyek", "SSE.Views.Toolbar.textInsPageBreak": "Insert page break", "SSE.Views.Toolbar.textInsRight": "<PERSON>a he<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textItems": "Tételek", "SSE.Views.Toolbar.textLandscape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textLeft": "Bal: ", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON> <PERSON><PERSON>", "SSE.Views.Toolbar.textLessEqual": "Less Than Or Equal To", "SSE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "SSE.Views.Toolbar.textManageRule": "Szabályok kezelése", "SSE.Views.Toolbar.textManyPages": "Oldalak", "SSE.Views.Toolbar.textMarginsLast": "Előző egyéni beállítások", "SSE.Views.Toolbar.textMarginsNarrow": "Keskeny", "SSE.Views.Toolbar.textMarginsNormal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsWide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMiddleBorders": "Belső vízszintes szegélyek", "SSE.Views.Toolbar.textMoreBorders": "More borders", "SSE.Views.Toolbar.textMoreFormats": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMorePages": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMoreSymbols": "More symbols", "SSE.Views.Toolbar.textNewColor": "Új egyedi szín hozzáadása", "SSE.Views.Toolbar.textNewRule": "<PERSON><PERSON>", "SSE.Views.Toolbar.textNoBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textNotEqualTo": "Not Equal To", "SSE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "SSE.Views.Toolbar.textOnePage": "<PERSON><PERSON>", "SSE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "SSE.Views.Toolbar.textOutBorders": "K<PERSON><PERSON><PERSON> szegélyek", "SSE.Views.Toolbar.textPageMarginsCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "SSE.Views.Toolbar.textPortrait": "Portré", "SSE.Views.Toolbar.textPrint": "Nyomtat", "SSE.Views.Toolbar.textPrintGridlines": "Rácsvonalak nyomtatása", "SSE.Views.Toolbar.textPrintHeadings": "Nyomtatási címsorok", "SSE.Views.Toolbar.textPrintOptions": "Nyomtatási beállí<PERSON>ok", "SSE.Views.Toolbar.textRegistered": "Registered Sign", "SSE.Views.Toolbar.textResetPageBreak": "Reset all page breaks", "SSE.Views.Toolbar.textRight": "Jobb: ", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textRotateDown": "Szöveg elforgatás<PERSON>", "SSE.Views.Toolbar.textRotateUp": "Szöveg elforgatása felfelé", "SSE.Views.Toolbar.textRtlSheet": "Sheet Right-to-left", "SSE.Views.Toolbar.textScale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textScaleCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSection": "Section Sign", "SSE.Views.Toolbar.textSelection": "Jelenlegi kijelölésből", "SSE.Views.Toolbar.textSeries": "Series", "SSE.Views.Toolbar.textSetPrintArea": "Nyomtatási terület beállítása", "SSE.Views.Toolbar.textShapesCombine": "Combine", "SSE.Views.Toolbar.textShapesFragment": "Fragment", "SSE.Views.Toolbar.textShapesIntersect": "Intersect", "SSE.Views.Toolbar.textShapesSubstract": "Subtract", "SSE.Views.Toolbar.textShapesUnion": "Union", "SSE.Views.Toolbar.textShowVA": "Látható terület megjeleníté<PERSON>", "SSE.Views.Toolbar.textSmile": "White Smiling Face", "SSE.Views.Toolbar.textSquareRoot": "Square Root", "SSE.Views.Toolbar.textStrikeout": "áthúz<PERSON>", "SSE.Views.Toolbar.textSubscript": "Alsó index", "SSE.Views.Toolbar.textSubSuperscript": "Alsó/felső index", "SSE.Views.Toolbar.textSuperscript": "Felső index", "SSE.Views.Toolbar.textTabCollaboration": "Együttműködés", "SSE.Views.Toolbar.textTabData": "Ada<PERSON>", "SSE.Views.Toolbar.textTabDraw": "Draw", "SSE.Views.Toolbar.textTabFile": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFormula": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabHome": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabInsert": "Beszúrás", "SSE.Views.Toolbar.textTabLayout": "Elrendezés", "SSE.Views.Toolbar.textTabProtect": "V<PERSON>delem", "SSE.Views.Toolbar.textTabView": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textThisPivot": "Ebből a Pivot táblázatból", "SSE.Views.Toolbar.textThisSheet": "Ebből a munkalapból", "SSE.Views.Toolbar.textThisTable": "Ebből a táblázatból", "SSE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTop": "Felső: ", "SSE.Views.Toolbar.textTopBorders": "<PERSON><PERSON><PERSON> szegélyek", "SSE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "SSE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textUp": "Up", "SSE.Views.Toolbar.textVertical": "Függőleges szöveg", "SSE.Views.Toolbar.textWidth": "Szélesség", "SSE.Views.Toolbar.textYen": "Yen Sign", "SSE.Views.Toolbar.textZoom": "Zoom", "SSE.Views.Toolbar.tipAlignBottom": "Alsóhoz igazít", "SSE.Views.Toolbar.tipAlignCenter": "Középre igazít", "SSE.Views.Toolbar.tipAlignJust": "Sorkizárt", "SSE.Views.Toolbar.tipAlignLeft": "Balra igazít", "SSE.Views.Toolbar.tipAlignMiddle": "Középhez igazít", "SSE.Views.Toolbar.tipAlignRight": "Jobbra igazít", "SSE.Views.Toolbar.tipAlignTop": "<PERSON><PERSON><PERSON><PERSON>re rendez ", "SSE.Views.Toolbar.tipAutofilter": "Rendezés és szűrés", "SSE.Views.Toolbar.tipBack": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCellStyle": "Cell<PERSON> stílus", "SSE.Views.Toolbar.tipChangeCase": "Change case", "SSE.Views.Toolbar.tipChangeChart": "Diagramtípus m<PERSON>í<PERSON>", "SSE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipColorSchemas": "Színséma módosítása", "SSE.Views.Toolbar.tipCondFormat": "Feltételes formázás", "SSE.Views.Toolbar.tipCopy": "Másol", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCut": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDecDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDecFont": "Betűméret csökkentése", "SSE.Views.Toolbar.tipDeleteOpt": "Cellák törlése", "SSE.Views.Toolbar.tipDigStyleAccounting": "Könyvelési stílus", "SSE.Views.Toolbar.tipDigStyleComma": "Comma style", "SSE.Views.Toolbar.tipDigStyleCurrency": "Pénznem", "SSE.Views.Toolbar.tipDigStylePercent": "Százalék", "SSE.Views.Toolbar.tipEditChart": "Grafikon szerkesztése", "SSE.Views.Toolbar.tipEditChartData": "Adatok kiválasztása", "SSE.Views.Toolbar.tipEditChartType": "Diagramtípus m<PERSON>í<PERSON>", "SSE.Views.Toolbar.tipEditHeader": "<PERSON>j<PERSON>c vag<PERSON> l<PERSON>c szerkesztése", "SSE.Views.Toolbar.tipFontColor": "Betűszín", "SSE.Views.Toolbar.tipFontName": "Betűtípus", "SSE.Views.Toolbar.tipFontSize": "Bet<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipHAlighOle": "Vízszintes rendezés", "SSE.Views.Toolbar.tipImgAlign": "Objektumok igazítása", "SSE.Views.Toolbar.tipImgGroup": "Objektum csoportok", "SSE.Views.Toolbar.tipIncDecimal": "<PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.Toolbar.tipIncFont": "Betű<PERSON>ret növelése", "SSE.Views.Toolbar.tipInsertChart": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertChartRecommend": "Insert recommended chart", "SSE.Views.Toolbar.tipInsertChartSpark": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertEquation": "<PERSON><PERSON><PERSON><PERSON>ú<PERSON>", "SSE.Views.Toolbar.tipInsertHorizontalText": "Insert horizontal text box", "SSE.Views.Toolbar.tipInsertHyperlink": "Hivatkozás hozzáadása", "SSE.Views.Toolbar.tipInsertImage": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertOpt": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertShape": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertSlicer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertSmartArt": "Insert SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Értékgörb<PERSON>", "SSE.Views.Toolbar.tipInsertSymbol": "Szimb<PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertText": "Szövegdoboz beszú<PERSON>ása", "SSE.Views.Toolbar.tipInsertTextart": "TextArt beszú<PERSON>a", "SSE.Views.Toolbar.tipInsertVerticalText": "Insert vertical text box", "SSE.Views.Toolbar.tipMerge": "Összevonás és középre", "SSE.Views.Toolbar.tipNone": "<PERSON><PERSON><PERSON> sem", "SSE.Views.Toolbar.tipNumFormat": "Számformátum", "SSE.Views.Toolbar.tipPageBreak": "Add a break where you want the next page to begin in the printed copy", "SSE.Views.Toolbar.tipPageMargins": "Oldal margók", "SSE.Views.Toolbar.tipPageOrient": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipPageSize": "<PERSON><PERSON> m<PERSON><PERSON>", "SSE.Views.Toolbar.tipPaste": "Beilleszt", "SSE.Views.Toolbar.tipPrColor": "Kitöltőszín", "SSE.Views.Toolbar.tipPrint": "Nyomtat", "SSE.Views.Toolbar.tipPrintArea": "Nyomtatási terület", "SSE.Views.Toolbar.tipPrintQuick": "Quick print", "SSE.Views.Toolbar.tipPrintTitles": "Címek nyomtatása", "SSE.Views.Toolbar.tipRedo": "Újra", "SSE.Views.Toolbar.tipReplace": "Replace", "SSE.Views.Toolbar.tipRtlSheet": "Switch the sheet direction so that the first column is on the right side", "SSE.Views.Toolbar.tipSave": "Ment", "SSE.Views.Toolbar.tipSaveCoauth": "M<PERSON>dos<PERSON><PERSON><PERSON><PERSON> mentése, hogy más felhasználók lá<PERSON>", "SSE.Views.Toolbar.tipScale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSelectAll": "Összes kiválasztása", "SSE.Views.Toolbar.tipSendBackward": "Visszafe<PERSON>ld", "SSE.Views.Toolbar.tipSendForward": "<PERSON><PERSON><PERSON>z", "SSE.Views.Toolbar.tipShapesMerge": "Merge shapes", "SSE.Views.Toolbar.tipSynchronize": "A dokumentumot egy másik felhasználó módosította. Kattintson a gombra a módosítások mentéséhez és a frissítések újratöltéséhez.", "SSE.Views.Toolbar.tipTextFormatting": "További szövegformázási eszközök", "SSE.Views.Toolbar.tipTextOrientation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipVAlighOle": "Függőlegesen rendez", "SSE.Views.Toolbar.tipVisibleArea": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipWrap": "Több sorba rendezés", "SSE.Views.Toolbar.txtAccounting": "Könyvelés", "SSE.Views.Toolbar.txtAdditional": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAscending": "Növekvő", "SSE.Views.Toolbar.txtAutosumTip": "Összegzés", "SSE.Views.Toolbar.txtCellStyle": "Cell Style", "SSE.Views.Toolbar.txtClearAll": "Minden", "SSE.Views.Toolbar.txtClearComments": "Megjegyzések", "SSE.Views.Toolbar.txtClearFilter": "A szűrő ürítése", "SSE.Views.Toolbar.txtClearFormat": "Formátum", "SSE.Views.Toolbar.txtClearFormula": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearHyper": "Hivatkozások", "SSE.Views.Toolbar.txtClearText": "Szöveg", "SSE.Views.Toolbar.txtCurrency": "Pénznem", "SSE.Views.Toolbar.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDateLong": "Long Date", "SSE.Views.Toolbar.txtDateShort": "Short Date", "SSE.Views.Toolbar.txtDateTime": "Dátum <PERSON>", "SSE.Views.Toolbar.txtDescending": "Csökkenő", "SSE.Views.Toolbar.txtDollar": "$ Dollár", "SSE.Views.Toolbar.txtEuro": "€ Euró", "SSE.Views.Toolbar.txtExp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtFillNum": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtFilter": "Szűrő", "SSE.Views.Toolbar.txtFormula": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtFraction": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtFranc": "CHF Svájci frank", "SSE.Views.Toolbar.txtGeneral": "<PERSON><PERSON>lán<PERSON>", "SSE.Views.Toolbar.txtInteger": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtManageRange": "Név kezelő", "SSE.Views.Toolbar.txtMergeAcross": "Vízszintesen mindent összevon", "SSE.Views.Toolbar.txtMergeCells": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtMergeCenter": "Összevon és központoz", "SSE.Views.Toolbar.txtNamedRange": "Elnevezett tartományok", "SSE.Views.Toolbar.txtNewRange": "Név megadása", "SSE.Views.Toolbar.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtNumber": "Szám", "SSE.Views.Toolbar.txtPasteRange": "Név beillesztése", "SSE.Views.Toolbar.txtPercentage": "Százalék", "SSE.Views.Toolbar.txtPound": "£ Font", "SSE.Views.Toolbar.txtRouble": "₽ Rubel", "SSE.Views.Toolbar.txtScientific": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSearch": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtSort": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtSortAZ": "Rendezés növekvően", "SSE.Views.Toolbar.txtSortZA": "Rendezés c<PERSON>ökkenően", "SSE.Views.Toolbar.txtSpecial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtTableTemplate": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtText": "Szöveg", "SSE.Views.Toolbar.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtUnmerge": "Cellák szétválasztása", "SSE.Views.Toolbar.txtYen": "¥ Jen", "SSE.Views.Top10FilterDialog.textType": "Mutat", "SSE.Views.Top10FilterDialog.txtBottom": "Alsó", "SSE.Views.Top10FilterDialog.txtBy": "<PERSON>ltal", "SSE.Views.Top10FilterDialog.txtItems": "tétel", "SSE.Views.Top10FilterDialog.txtPercent": "Százalék", "SSE.Views.Top10FilterDialog.txtSum": "Összeg", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 autoszűrő", "SSE.Views.Top10FilterDialog.txtTop": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtValueTitle": "Top 10 szűrő", "SSE.Views.ValueFieldSettingsDialog.textNext": "(következő)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(előző)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Érték<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Átlag", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Alapelem", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 a %2-ből", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Dar<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Számolja meg a számokat", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Egyéni név", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "A különbség", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Sorszá<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Maximum", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Minimum", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "százaléka", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Százalékos különbség", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% of grand total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% of parent total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% of parent column total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% of parent row total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% running total in", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Termék", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Rank smallest to largest", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Rank largest to smallest", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Összesen fut", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Az értékek megjelenítése", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Forr<PERSON> neve:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "<PERSON><PERSON><PERSON><PERSON> populáci<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Összeg", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Összegezze az érték mezőt", "SSE.Views.ValueFieldSettingsDialog.txtVar": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "Bezárás", "SSE.Views.ViewManagerDlg.guestText": "Vendég", "SSE.Views.ViewManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textDelete": "Törlés", "SSE.Views.ViewManagerDlg.textDuplicate": "Kettőzés", "SSE.Views.ViewManagerDlg.textEmpty": "Még nem hoztak létre nézeteket.", "SSE.Views.ViewManagerDlg.textGoTo": "Ugrás a nézetre", "SSE.Views.ViewManagerDlg.textLongName": "<PERSON><PERSON><PERSON> be egy 128 karak<PERSON><PERSON><PERSON> nevet.", "SSE.Views.ViewManagerDlg.textNew": "<PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textRename": "Átnevezés", "SSE.Views.ViewManagerDlg.textRenameError": "A nézet neve nem lehet üres.", "SSE.Views.ViewManagerDlg.textRenameLabel": "N<PERSON>zet <PERSON>", "SSE.Views.ViewManagerDlg.textViews": "Lapnézetek", "SSE.Views.ViewManagerDlg.tipIsLocked": "Ezt az elemet egy másik felhasználó szerkeszti.", "SSE.Views.ViewManagerDlg.txtTitle": "Lapné<PERSON>t kezel<PERSON>", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Are you sure you want to delete this sheet view?", "SSE.Views.ViewManagerDlg.warnDeleteView": "Megpróbálja törölni a jelenleg engedélyezett '%1' nézetet.<br><PERSON><PERSON><PERSON><PERSON><PERSON> ezt a nézetet, és törli?", "SSE.Views.ViewTab.capBtnFreeze": "Panelek rögzítése", "SSE.Views.ViewTab.capBtnSheetView": "Lapn<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Mindig mutasd az eszköztárat", "SSE.Views.ViewTab.textClose": "Bezárás", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "A munkalap és az állapotsorok kombinálása", "SSE.Views.ViewTab.textCreate": "<PERSON><PERSON>", "SSE.Views.ViewTab.textDefault": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textFormula": "Függvény s<PERSON>v", "SSE.Views.ViewTab.textFreezeCol": "<PERSON><PERSON> első oszlop rögzítése", "SSE.Views.ViewTab.textFreezeRow": "Legfelső sor rögzítése", "SSE.Views.ViewTab.textGridlines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textHeadings": "Címsorok", "SSE.Views.ViewTab.textInterfaceTheme": "Felhasználói felület témája", "SSE.Views.ViewTab.textLeftMenu": "Left Panel", "SSE.Views.ViewTab.textLine": "<PERSON><PERSON>", "SSE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "Megtekintés kelező", "SSE.Views.ViewTab.textRightMenu": "Right Panel", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Mutassa a rögzített panelek árnyékát", "SSE.Views.ViewTab.textTabStyle": "Tab style", "SSE.Views.ViewTab.textUnFreeze": "Rögzítés eltávolítása", "SSE.Views.ViewTab.textZeros": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textZoom": "Zoom", "SSE.Views.ViewTab.tipClose": "Lapn<PERSON><PERSON><PERSON> be<PERSON>", "SSE.Views.ViewTab.tipCreate": "Lapnézet létrehozása", "SSE.Views.ViewTab.tipFreeze": "Panelek rögzítése", "SSE.Views.ViewTab.tipInterfaceTheme": "Interfész témája", "SSE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipSheetView": "Lapn<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.tipViewNormal": "See your document in Normal view", "SSE.Views.ViewTab.tipViewPageBreak": "See where the page breaks will appear when your document is printed", "SSE.Views.ViewTab.txtViewNormal": "Normal", "SSE.Views.ViewTab.txtViewPageBreak": "Page Break Preview", "SSE.Views.WatchDialog.closeButtonText": "Close", "SSE.Views.WatchDialog.textAdd": "Add watch", "SSE.Views.WatchDialog.textBook": "Book", "SSE.Views.WatchDialog.textCell": "Cell", "SSE.Views.WatchDialog.textDelete": "Delete watch", "SSE.Views.WatchDialog.textDeleteAll": "Delete all", "SSE.Views.WatchDialog.textFormula": "Formula", "SSE.Views.WatchDialog.textName": "Name", "SSE.Views.WatchDialog.textSheet": "Sheet", "SSE.Views.WatchDialog.textValue": "Value", "SSE.Views.WatchDialog.txtTitle": "Watch window", "SSE.Views.WBProtection.hintAllowRanges": "Tartományok szerkesztésének engedélyezése", "SSE.Views.WBProtection.hintProtectRange": "Protect range", "SSE.Views.WBProtection.hintProtectSheet": "<PERSON><PERSON><PERSON> védelme", "SSE.Views.WBProtection.hintProtectWB": "Munkafüzet védelme", "SSE.Views.WBProtection.txtAllowRanges": "Tartományok szerkesztésének engedélyezése", "SSE.Views.WBProtection.txtHiddenFormula": "<PERSON><PERSON><PERSON><PERSON> f<PERSON>", "SSE.Views.WBProtection.txtLockedCell": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedShape": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedText": "Szöveg zárolása", "SSE.Views.WBProtection.txtProtectRange": "Protect Range", "SSE.Views.WBProtection.txtProtectSheet": "<PERSON><PERSON><PERSON> védelme", "SSE.Views.WBProtection.txtProtectWB": "Munkafüzet védelme", "SSE.Views.WBProtection.txtSheetUnlockDescription": "<PERSON>r<PERSON> be egy j<PERSON>t a munkalap védelmének megszüntetéséhez", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Munkalap védelmének megszüntetése", "SSE.Views.WBProtection.txtWBUnlockDescription": "Írjon be egy j<PERSON>t a munkafüzet védelmének megszüntetéséhez", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}