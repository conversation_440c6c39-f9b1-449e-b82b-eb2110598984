{"cancelButtonText": "Batalkan", "Common.Controllers.Chat.notcriticalErrorTitle": "<PERSON><PERSON>", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.History.notcriticalErrorTitle": "<PERSON><PERSON>", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textAreaStackedPer": "<PERSON><PERSON><PERSON> 100%", "Common.define.chartData.textBar": "Bar", "Common.define.chartData.textBarNormal": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textBarNormal3d": "Lajur bergugus 3-D", "Common.define.chartData.textBarNormal3dPerspective": "Lajur 3-D", "Common.define.chartData.textBarStacked": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textBarStacked3d": "Lajur bertindan 3-D", "Common.define.chartData.textBarStackedPer": "<PERSON><PERSON><PERSON> 100%", "Common.define.chartData.textBarStackedPer3d": "<PERSON><PERSON><PERSON> 100% 3-D", "Common.define.chartData.textCharts": "Carta", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textColumnSpark": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "Kombo", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON><PERSON> be<PERSON> – lajur bergugus", "Common.define.chartData.textComboBarLine": "<PERSON><PERSON><PERSON> - garis", "Common.define.chartData.textComboBarLineSecondary": "<PERSON><PERSON><PERSON> - garis pada paksi kedua", "Common.define.chartData.textComboCustom": "Kombinasi tersuai", "Common.define.chartData.textDoughnut": "Donut", "Common.define.chartData.textHBarNormal": "Bar bergugus", "Common.define.chartData.textHBarNormal3d": "Bar bergugus 3-D", "Common.define.chartData.textHBarStacked": "Bar bertindan", "Common.define.chartData.textHBarStacked3d": "Bar bertindan 3-D", "Common.define.chartData.textHBarStackedPer": "Bar bertindan 100%", "Common.define.chartData.textHBarStackedPer3d": "Bar bertindan 100% 3-D", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "Baris 3-D", "Common.define.chartData.textLineMarker": "<PERSON><PERSON> dengan penanda", "Common.define.chartData.textLineSpark": "<PERSON><PERSON>", "Common.define.chartData.textLineStacked": "<PERSON><PERSON> be<PERSON>", "Common.define.chartData.textLineStackedMarker": "<PERSON><PERSON> bertindan dengan penanda", "Common.define.chartData.textLineStackedPer": "<PERSON><PERSON> 100%", "Common.define.chartData.textLineStackedPerMarker": "<PERSON><PERSON> be<PERSON> 100% dengan penanda", "Common.define.chartData.textPie": "<PERSON><PERSON>", "Common.define.chartData.textPie3d": "Pai 3-D", "Common.define.chartData.textPoint": "XY (Serak)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Filled radar", "Common.define.chartData.textRadarMarker": "Radar with markers", "Common.define.chartData.textScatter": "Serak", "Common.define.chartData.textScatterLine": "<PERSON><PERSON> dengan garis rata", "Common.define.chartData.textScatterLineMarker": "<PERSON><PERSON> dengan garis rata dan penanda", "Common.define.chartData.textScatterSmooth": "<PERSON><PERSON> dengan garis rata", "Common.define.chartData.textScatterSmoothMarker": "<PERSON><PERSON> dengan garis rata dan penanda", "Common.define.chartData.textSparks": "Sparkline", "Common.define.chartData.textStock": "Stok", "Common.define.chartData.textSurface": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textWinLossSpark": "Menang/Kalah", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Tiada format ditetapkan", "Common.define.conditionalData.text1Above": "1 std dev melebihi", "Common.define.conditionalData.text1Below": "1 std dev kurang", "Common.define.conditionalData.text2Above": "2 std dev melebihi", "Common.define.conditionalData.text2Below": "2 std dev kurang", "Common.define.conditionalData.text3Above": "3 std dev melebihi", "Common.define.conditionalData.text3Below": "3 std dev kurang", "Common.define.conditionalData.textAbove": "Di atas", "Common.define.conditionalData.textAverage": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textBegins": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textBelow": "<PERSON>", "Common.define.conditionalData.textBetween": "Di <PERSON>ara", "Common.define.conditionalData.textBlank": "Kosong", "Common.define.conditionalData.textBlanks": "Mengandungi kosong", "Common.define.conditionalData.textBottom": "<PERSON>wa<PERSON>", "Common.define.conditionalData.textContains": "Mengandungi", "Common.define.conditionalData.textDataBar": "Bar Data", "Common.define.conditionalData.textDate": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textDuplicate": "Pendua", "Common.define.conditionalData.textEnds": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textEqAbove": "<PERSON>a dengan atau melebihi", "Common.define.conditionalData.textEqBelow": "<PERSON>a dengan atau di bawah", "Common.define.conditionalData.textEqual": "<PERSON><PERSON> den<PERSON>", "Common.define.conditionalData.textError": "<PERSON><PERSON>", "Common.define.conditionalData.textErrors": "Mengan<PERSON>ng<PERSON> ralat", "Common.define.conditionalData.textFormula": "Formula", "Common.define.conditionalData.textGreater": "<PERSON><PERSON><PERSON> besar dari", "Common.define.conditionalData.textGreaterEq": "<PERSON><PERSON><PERSON> besar dari atau sama dengan", "Common.define.conditionalData.textIconSets": "Set ikon", "Common.define.conditionalData.textLast7days": "Dalam 7 hari yang lalu", "Common.define.conditionalData.textLastMonth": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textLastWeek": "<PERSON><PERSON> lepas", "Common.define.conditionalData.textLess": "<PERSON><PERSON>", "Common.define.conditionalData.textLessEq": "<PERSON><PERSON> da<PERSON>ada atau sama dengan", "Common.define.conditionalData.textNextMonth": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textNextWeek": "<PERSON><PERSON>", "Common.define.conditionalData.textNotBetween": "Bukan di antara", "Common.define.conditionalData.textNotBlanks": "Tidak mengandungi kosong", "Common.define.conditionalData.textNotContains": "Tidak mengandungi", "Common.define.conditionalData.textNotEqual": "Tidak sama kepada", "Common.define.conditionalData.textNotErrors": "Tidak mengandungi ralat", "Common.define.conditionalData.textText": "Teks", "Common.define.conditionalData.textThisMonth": "<PERSON><PERSON><PERSON> ini", "Common.define.conditionalData.textThisWeek": "<PERSON><PERSON> ini", "Common.define.conditionalData.textToday": "<PERSON> ini", "Common.define.conditionalData.textTomorrow": "Esok", "Common.define.conditionalData.textTop": "Atas", "Common.define.conditionalData.textUnique": "Unik", "Common.define.conditionalData.textValue": "<PERSON><PERSON>", "Common.define.conditionalData.textYesterday": "Semalam", "Common.define.smartArt.textAccentedPicture": "Accented Picture", "Common.define.smartArt.textAccentProcess": "Accent Process", "Common.define.smartArt.textAlternatingFlow": "Alternating flow", "Common.define.smartArt.textAlternatingHexagons": "Alternating hexagons", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternating picture blocks", "Common.define.smartArt.textAlternatingPictureCircles": "Alternating picture circles", "Common.define.smartArt.textArchitectureLayout": "Architecture layout", "Common.define.smartArt.textArrowRibbon": "Arrow ribbon", "Common.define.smartArt.textAscendingPictureAccentProcess": "Ascending picture accent process", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Basic bending process", "Common.define.smartArt.textBasicBlockList": "Basic block list", "Common.define.smartArt.textBasicChevronProcess": "Basic chevron process", "Common.define.smartArt.textBasicCycle": "Basic cycle", "Common.define.smartArt.textBasicMatrix": "Basic matrix", "Common.define.smartArt.textBasicPie": "Basic Pie", "Common.define.smartArt.textBasicProcess": "Basic process", "Common.define.smartArt.textBasicPyramid": "Basic pyramid", "Common.define.smartArt.textBasicRadial": "Basic radial", "Common.define.smartArt.textBasicTarget": "Basic target", "Common.define.smartArt.textBasicTimeline": "Basic timeline", "Common.define.smartArt.textBasicVenn": "Basic Venn", "Common.define.smartArt.textBendingPictureAccentList": "Bending picture accent list", "Common.define.smartArt.textBendingPictureBlocks": "Bending picture blocks", "Common.define.smartArt.textBendingPictureCaption": "Bending picture caption", "Common.define.smartArt.textBendingPictureCaptionList": "Bending picture caption list", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Bending picture semi-transparent text", "Common.define.smartArt.textBlockCycle": "Block cycle", "Common.define.smartArt.textBubblePictureList": "Bubble picture list", "Common.define.smartArt.textCaptionedPictures": "Captioned pictures", "Common.define.smartArt.textChevronAccentProcess": "Chevron accent process", "Common.define.smartArt.textChevronList": "Chevron list", "Common.define.smartArt.textCircleAccentTimeline": "Circle accent timeline", "Common.define.smartArt.textCircleArrowProcess": "Circle arrow process", "Common.define.smartArt.textCirclePictureHierarchy": "Circle picture hierarchy", "Common.define.smartArt.textCircleProcess": "Circle process", "Common.define.smartArt.textCircleRelationship": "Circle relationship", "Common.define.smartArt.textCircularBendingProcess": "Circular bending process", "Common.define.smartArt.textCircularPictureCallout": "Circular picture callout", "Common.define.smartArt.textClosedChevronProcess": "Closed chevron process", "Common.define.smartArt.textContinuousArrowProcess": "Continuous arrow process", "Common.define.smartArt.textContinuousBlockProcess": "Continuous block process", "Common.define.smartArt.textContinuousCycle": "Continuous cycle", "Common.define.smartArt.textContinuousPictureList": "Continuous picture list", "Common.define.smartArt.textConvergingArrows": "Converging arrows", "Common.define.smartArt.textConvergingRadial": "Converging radial", "Common.define.smartArt.textConvergingText": "Converging text", "Common.define.smartArt.textCounterbalanceArrows": "Counterbalance arrows", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "Cycle matrix", "Common.define.smartArt.textDescendingBlockList": "Descending block list", "Common.define.smartArt.textDescendingProcess": "Descending process", "Common.define.smartArt.textDetailedProcess": "Detailed process", "Common.define.smartArt.textDivergingArrows": "Diverging arrows", "Common.define.smartArt.textDivergingRadial": "Diverging radial", "Common.define.smartArt.textEquation": "Equation", "Common.define.smartArt.textFramedTextPicture": "Framed text picture", "Common.define.smartArt.textFunnel": "Funnel", "Common.define.smartArt.textGear": "Gear", "Common.define.smartArt.textGridMatrix": "Grid matrix", "Common.define.smartArt.textGroupedList": "Grouped list", "Common.define.smartArt.textHalfCircleOrganizationChart": "Half circle organization chart", "Common.define.smartArt.textHexagonCluster": "Hexagon cluster", "Common.define.smartArt.textHexagonRadial": "Hexagon radial", "Common.define.smartArt.textHierarchy": "Hierarchy", "Common.define.smartArt.textHierarchyList": "Hierarchy list", "Common.define.smartArt.textHorizontalBulletList": "Horizontal bullet list", "Common.define.smartArt.textHorizontalHierarchy": "Horizontal hierarchy", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal labeled hierarchy", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontal multi-level hierarchy", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontal organization chart", "Common.define.smartArt.textHorizontalPictureList": "Horizontal picture list", "Common.define.smartArt.textIncreasingArrowProcess": "Increasing arrow process", "Common.define.smartArt.textIncreasingCircleProcess": "Increasing circle process", "Common.define.smartArt.textInterconnectedBlockProcess": "Interconnected block process", "Common.define.smartArt.textInterconnectedRings": "Interconnected rings", "Common.define.smartArt.textInvertedPyramid": "Inverted pyramid", "Common.define.smartArt.textLabeledHierarchy": "Labeled hierarchy", "Common.define.smartArt.textLinearVenn": "Linear Venn", "Common.define.smartArt.textLinedList": "Lined list", "Common.define.smartArt.textList": "List", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Multidirectional cycle", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Name and title organization chart", "Common.define.smartArt.textNestedTarget": "Nested target", "Common.define.smartArt.textNondirectionalCycle": "Nondirectional cycle", "Common.define.smartArt.textOpposingArrows": "Opposing arrows", "Common.define.smartArt.textOpposingIdeas": "Opposing ideas", "Common.define.smartArt.textOrganizationChart": "Organization chart", "Common.define.smartArt.textOther": "Other", "Common.define.smartArt.textPhasedProcess": "Phased process", "Common.define.smartArt.textPicture": "Picture", "Common.define.smartArt.textPictureAccentBlocks": "Picture accent blocks", "Common.define.smartArt.textPictureAccentList": "Picture accent list", "Common.define.smartArt.textPictureAccentProcess": "Picture accent process", "Common.define.smartArt.textPictureCaptionList": "Picture caption list", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "Picture grid", "Common.define.smartArt.textPictureLineup": "Picture lineup", "Common.define.smartArt.textPictureOrganizationChart": "Picture organization chart", "Common.define.smartArt.textPictureStrips": "Picture strips", "Common.define.smartArt.textPieProcess": "Pie process", "Common.define.smartArt.textPlusAndMinus": "Plus and minus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "Process arrows", "Common.define.smartArt.textProcessList": "Process list", "Common.define.smartArt.textPyramid": "Pyramid", "Common.define.smartArt.textPyramidList": "Pyramid list", "Common.define.smartArt.textRadialCluster": "Radial cluster", "Common.define.smartArt.textRadialCycle": "Radial cycle", "Common.define.smartArt.textRadialList": "Radial list", "Common.define.smartArt.textRadialPictureList": "Radial picture list", "Common.define.smartArt.textRadialVenn": "Radial Venn", "Common.define.smartArt.textRandomToResultProcess": "Random to result process", "Common.define.smartArt.textRelationship": "Relationship", "Common.define.smartArt.textRepeatingBendingProcess": "Repeating bending process", "Common.define.smartArt.textReverseList": "Reverse list", "Common.define.smartArt.textSegmentedCycle": "Segmented cycle", "Common.define.smartArt.textSegmentedProcess": "Segmented process", "Common.define.smartArt.textSegmentedPyramid": "Segmented pyramid", "Common.define.smartArt.textSnapshotPictureList": "Snapshot picture list", "Common.define.smartArt.textSpiralPicture": "Spiral picture", "Common.define.smartArt.textSquareAccentList": "Square accent list", "Common.define.smartArt.textStackedList": "Stacked list", "Common.define.smartArt.textStackedVenn": "Stacked <PERSON>n", "Common.define.smartArt.textStaggeredProcess": "Staggered process", "Common.define.smartArt.textStepDownProcess": "Step down process", "Common.define.smartArt.textStepUpProcess": "Step up process", "Common.define.smartArt.textSubStepProcess": "Sub-step process", "Common.define.smartArt.textTabbedArc": "Tabbed arc", "Common.define.smartArt.textTableHierarchy": "Table hierarchy", "Common.define.smartArt.textTableList": "Table list", "Common.define.smartArt.textTabList": "Tab List", "Common.define.smartArt.textTargetList": "Target list", "Common.define.smartArt.textTextCycle": "Text cycle", "Common.define.smartArt.textThemePictureAccent": "Theme picture accent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Theme picture alternating accent", "Common.define.smartArt.textThemePictureGrid": "Theme picture grid", "Common.define.smartArt.textTitledMatrix": "Titled matrix", "Common.define.smartArt.textTitledPictureAccentList": "Titled picture accent list", "Common.define.smartArt.textTitledPictureBlocks": "Titled picture blocks", "Common.define.smartArt.textTitlePictureLineup": "Title picture lineup", "Common.define.smartArt.textTrapezoidList": "Trapezoid list", "Common.define.smartArt.textUpwardArrow": "Upward arrow", "Common.define.smartArt.textVaryingWidthList": "Varying width list", "Common.define.smartArt.textVerticalAccentList": "Vertical accent list", "Common.define.smartArt.textVerticalArrowList": "Vertical arrow list", "Common.define.smartArt.textVerticalBendingProcess": "Vertical bending process", "Common.define.smartArt.textVerticalBlockList": "Vertical block list", "Common.define.smartArt.textVerticalBoxList": "Vertical box list", "Common.define.smartArt.textVerticalBracketList": "Vertical bracket list", "Common.define.smartArt.textVerticalBulletList": "Vertical bullet list", "Common.define.smartArt.textVerticalChevronList": "Vertical chevron list", "Common.define.smartArt.textVerticalCircleList": "Vertical circle list", "Common.define.smartArt.textVerticalCurvedList": "Vertical curved list", "Common.define.smartArt.textVerticalEquation": "Vertical equation", "Common.define.smartArt.textVerticalPictureAccentList": "Vertical picture accent list", "Common.define.smartArt.textVerticalPictureList": "Vertical picture list", "Common.define.smartArt.textVerticalProcess": "Vertical process", "Common.Translation.textMoreButton": "Selanjutnya", "Common.Translation.tipFileLocked": "Document is locked for editing. You can make changes and save it as local copy later.", "Common.Translation.tipFileReadOnly": "The file is read-only. To keep your changes, save the file with a new name or in a different location.", "Common.Translation.warnFileLocked": "<PERSON><PERSON> sedang diedit dalam apl yang lain. <PERSON>a boleh terus mengedit dan simpan ia sebagai Salinan.", "Common.Translation.warnFileLockedBtnEdit": "Cipta <PERSON>", "Common.Translation.warnFileLockedBtnView": "Buka untuk paparan", "Common.UI.ButtonColored.textAutoColor": "Automatik", "Common.UI.ButtonColored.textEyedropper": "Eyedropper", "Common.UI.ButtonColored.textNewColor": "Tambah Warna Tersuai Baharu", "Common.UI.ComboBorderSize.txtNoBorders": "T<PERSON>da sempanan", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "T<PERSON>da sempanan", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON><PERSON> gaya", "Common.UI.ExtendedColorDialog.addButtonText": "Tambah", "Common.UI.ExtendedColorDialog.textCurrent": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textHexErr": "<PERSON><PERSON> yang dimasukkan adalah tidak betul.<br><PERSON><PERSON> masukkan nilai di antara 000000 dan FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "<PERSON><PERSON> yang dimasukkan adalah tidak betul.<br><PERSON><PERSON> masukkan nilai be<PERSON>ka di antara 0 dan 255.", "Common.UI.HSBColorPicker.textNoColor": "Tiada Warna", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Sembunyikan kata laluan", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Tunjuk kata laluan", "Common.UI.SearchBar.textFind": "<PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipNextResult": "Keputusan seterusnya", "Common.UI.SearchBar.tipOpenAdvancedSettings": "B<PERSON> tetapan lan<PERSON>", "Common.UI.SearchBar.tipPreviousResult": "Keputusan sebelum", "Common.UI.SearchDialog.textHighlight": "Keputusan Sorotan Penting", "Common.UI.SearchDialog.textMatchCase": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "Common.UI.SearchDialog.textReplaceDef": "Masukkan teks gantian", "Common.UI.SearchDialog.textSearchStart": "<PERSON><PERSON><PERSON><PERSON> teks anda di sini", "Common.UI.SearchDialog.textTitle": "<PERSON><PERSON> dan <PERSON>", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>an sahaja", "Common.UI.SearchDialog.txtBtnHideReplace": "Sembunyikan Ganti", "Common.UI.SearchDialog.txtBtnReplace": "Gantikan", "Common.UI.SearchDialog.txtBtnReplaceAll": "Gantikan Semua", "Common.UI.SynchronizeTip.textDontShow": "<PERSON><PERSON> tunjukkan mesej ini semula", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "Dokumen telah ditukar dengan pengguna yang lain.<br><PERSON>la klik untuk simpan perubahan anda dan muat semula kemas kini.", "Common.UI.ThemeColorPalette.textRecentColors": "<PERSON><PERSON>", "Common.UI.ThemeColorPalette.textStandartColors": "Warna Standard", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Klasik <PERSON>", "Common.UI.Themes.txtThemeContrastDark": "Kontras Gelap", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "Sama seperti Sistem", "Common.UI.Window.cancelButtonText": "Batalkan", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Tidak", "Common.UI.Window.okButtonText": "<PERSON><PERSON>", "Common.UI.Window.textConfirmation": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textDontShow": "<PERSON><PERSON> tunjukkan mesej ini semula", "Common.UI.Window.textError": "<PERSON><PERSON>", "Common.UI.Window.textInformation": "Informasi", "Common.UI.Window.textWarning": "<PERSON><PERSON>", "Common.UI.Window.yesButtonText": "Ya", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtaccent": "Accent", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "Background", "Common.Utils.ThemeColor.txtBlack": "Black", "Common.Utils.ThemeColor.txtBlue": "Blue", "Common.Utils.ThemeColor.txtBrightGreen": "Bright green", "Common.Utils.ThemeColor.txtBrown": "<PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Dark blue", "Common.Utils.ThemeColor.txtDarker": "Darker", "Common.Utils.ThemeColor.txtDarkGray": "Dark gray", "Common.Utils.ThemeColor.txtDarkGreen": "Dark green", "Common.Utils.ThemeColor.txtDarkPurple": "Dark purple", "Common.Utils.ThemeColor.txtDarkRed": "Dark red", "Common.Utils.ThemeColor.txtDarkTeal": "Dark teal", "Common.Utils.ThemeColor.txtDarkYellow": "Dark yellow", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "<PERSON>", "Common.Utils.ThemeColor.txtGreen": "Green", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "Light blue", "Common.Utils.ThemeColor.txtLighter": "Lighter", "Common.Utils.ThemeColor.txtLightGray": "Light gray", "Common.Utils.ThemeColor.txtLightGreen": "Light green", "Common.Utils.ThemeColor.txtLightOrange": "Light orange", "Common.Utils.ThemeColor.txtLightYellow": "Light yellow", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "Pink", "Common.Utils.ThemeColor.txtPurple": "Purple", "Common.Utils.ThemeColor.txtRed": "Red", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Sky blue", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Text", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "White", "Common.Utils.ThemeColor.txtYellow": "Yellow", "Common.Views.About.txtAddress": "<PERSON><PERSON><PERSON> ", "Common.Views.About.txtLicensee": "LESENKAN", "Common.Views.About.txtLicensor": "PEMBERI LESEN", "Common.Views.About.txtMail": "e-mel: ", "Common.Views.About.txtPoweredBy": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>h", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "<PERSON><PERSON><PERSON> ", "Common.Views.AutoCorrectDialog.textAdd": "Tambah", "Common.Views.AutoCorrectDialog.textApplyAsWork": "<PERSON><PERSON> semasa anda bekerja", "Common.Views.AutoCorrectDialog.textAutoCorrect": "AutoBaiki", "Common.Views.AutoCorrectDialog.textAutoFormat": "AutoFormat Semasa <PERSON>", "Common.Views.AutoCorrectDialog.textBy": "Mengikut", "Common.Views.AutoCorrectDialog.textDelete": "Padam", "Common.Views.AutoCorrectDialog.textHyperlink": "<PERSON><PERSON> dan rang<PERSON>an dengan hiperpautan", "Common.Views.AutoCorrectDialog.textMathCorrect": "Matematik", "Common.Views.AutoCorrectDialog.textNewRowCol": "<PERSON><PERSON><PERSON><PERSON> baris dan lajur baharu dalam jadual", "Common.Views.AutoCorrectDialog.textRecognized": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Ungkapan berikut adalah ungkapan kira-kira yang dicam. <PERSON>ya akan dipaparkan secara automatik dalam huruf italik.", "Common.Views.AutoCorrectDialog.textReplace": "Gantikan", "Common.Views.AutoCorrectDialog.textReplaceText": "Gantikan Semasa <PERSON>", "Common.Views.AutoCorrectDialog.textReplaceType": "Gantikan teks semasa anda taip", "Common.Views.AutoCorrectDialog.textReset": "<PERSON>", "Common.Views.AutoCorrectDialog.textResetAll": "<PERSON><PERSON> ke lalai", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "AutoBaiki", "Common.Views.AutoCorrectDialog.textWarnAddRec": "<PERSON>gsi dikenalpasti mestilah mengandungi hanya huruf A hingga Z, huruf besar atau huruf kecil.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Mana-mana eks<PERSON><PERSON>i yang anda tambah akan dikeluarkan dan mana telah dikeluarkan akan dipulihkan semula. <PERSON><PERSON><PERSON> anda mahu men<PERSON>kan?", "Common.Views.AutoCorrectDialog.warnReplace": "Entri autobetul untuk %1 telah wujud. <PERSON><PERSON><PERSON> anda mahu gantikannya?", "Common.Views.AutoCorrectDialog.warnReset": "Mana-mana pembetulan auto yang anda tambah akan dibuang dan mana telah berubah akan dipulihkan kepada nilai asal. <PERSON><PERSON><PERSON> anda mahu men<PERSON>kan?", "Common.Views.AutoCorrectDialog.warnRestore": "Entri autobetul untuk %1 akan diset semula ke nilai aslinya. <PERSON><PERSON><PERSON> anda mahu gantikannya?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "Hantar", "Common.Views.Comments.mniAuthorAsc": "Pengarang A ke Z", "Common.Views.Comments.mniAuthorDesc": "Pengarang Z ke A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON>", "Common.Views.Comments.mniDateDesc": "Terbaharu", "Common.Views.Comments.mniFilterGroups": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniPositionAsc": "<PERSON><PERSON> atas", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON> bawah", "Common.Views.Comments.textAdd": "Tambah", "Common.Views.Comments.textAddComment": "Tambah Komen", "Common.Views.Comments.textAddCommentToDoc": "Tambah Komen pada Dokumen", "Common.Views.Comments.textAddReply": "Tambah Balasan", "Common.Views.Comments.textAll": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "Batalkan", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "Tutup komen", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "Komen", "Common.Views.Comments.textEdit": "<PERSON><PERSON>", "Common.Views.Comments.textEnterCommentHint": "<PERSON><PERSON><PERSON><PERSON> komen anda di sini", "Common.Views.Comments.textHintAddComment": "Tambah komen", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON>", "Common.Views.Comments.textReply": "<PERSON><PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "Diselesaikan", "Common.Views.Comments.textSort": "<PERSON><PERSON> komen", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "Anda tidak mempunyai keizinan untuk membuka semula komen", "Common.Views.Comments.txtEmpty": "Tidak ada komen di dalam he<PERSON>an.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON><PERSON> tunjukkan mesej ini semula", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON> salin, potong dan tampal menggunakan bar alatan editor dan konteks tindakan menu akan dilakukan dalam tab editor ini sahaja.<br><br>Untuk menyalin atau menampal keapda atau daripada aplikasi di luar tab editor guna kombinasi papan kekunci yang berikut:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> dan <PERSON>", "Common.Views.CopyWarningDialog.textToCopy": "untuk Salinan", "Common.Views.CopyWarningDialog.textToCut": "untuk Potong", "Common.Views.CopyWarningDialog.textToPaste": "untuk Tampal", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "Memuatkan", "Common.Views.DocumentAccessDialog.textTitle": "<PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "Eraser", "Common.Views.Draw.hintSelect": "Select", "Common.Views.Draw.txtEraser": "Eraser", "Common.Views.Draw.txtHighlighter": "Highlighter", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Pen", "Common.Views.Draw.txtSelect": "Select", "Common.Views.Draw.txtSize": "Size", "Common.Views.EditNameDialog.textLabel": "Label:", "Common.Views.EditNameDialog.textLabelError": "Label tidak boleh kosong.", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "<PERSON><PERSON><PERSON> yang mengedit fail:", "Common.Views.Header.textAddFavorite": "Tanda sebagai kegemaran", "Common.Views.Header.textAdvSettings": "Seting lanjutan", "Common.Views.Header.textBack": "Buka lokasi fail", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "Sembunyikan Bar Alat", "Common.Views.Header.textHideLines": "Sembunyikan Pembaris", "Common.Views.Header.textHideStatusBar": "<PERSON><PERSON><PERSON> he<PERSON>an dan bar status", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "Read only", "Common.Views.Header.textRemoveFavorite": "<PERSON><PERSON> k<PERSON>", "Common.Views.Header.textSaveBegin": "Sedang menyimpan…", "Common.Views.Header.textSaveChanged": "Diubahsuai", "Common.Views.Header.textSaveEnd": "<PERSON><PERSON><PERSON> disimpan", "Common.Views.Header.textSaveExpander": "<PERSON><PERSON><PERSON> disimpan", "Common.Views.Header.textShare": "Kong<PERSON>", "Common.Views.Header.textZoom": "<PERSON><PERSON>", "Common.Views.Header.tipAccessRights": "Uruskan hak akses dokumen", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "Muat turun fail", "Common.Views.Header.tipGoEdit": "Edit fail semasa", "Common.Views.Header.tipPrint": "<PERSON>ail cetakan", "Common.Views.Header.tipPrintQuick": "Quick print", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Simpan", "Common.Views.Header.tipSearch": "<PERSON><PERSON>", "Common.Views.Header.tipUndo": "<PERSON><PERSON>t semula", "Common.Views.Header.tipUndock": "<PERSON><PERSON>ar dok ke tetingkap be<PERSON>ingan", "Common.Views.Header.tipUsers": "<PERSON><PERSON>", "Common.Views.Header.tipViewSettings": "<PERSON><PERSON> seting", "Common.Views.Header.tipViewUsers": "Lihat pengguna dan uruskan hak akses dokumen", "Common.Views.Header.txtAccessRights": "Ubah hak akses", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON>", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON>", "Common.Views.History.textShowAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> per<PERSON> terperi<PERSON>i", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "Tampal URL imej:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Medan ini diperlukan", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Medan ini perlu sebagai URL dalam format \"http://www.example.com\"", "Common.Views.ListSettingsDialog.textBulleted": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.textFromFile": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.textFromStorage": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.textFromUrl": "Dari URL", "Common.Views.ListSettingsDialog.textNumbering": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.textSelect": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.tipChange": "<PERSON><PERSON> bulet", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImport": "Import", "Common.Views.ListSettingsDialog.txtNewBullet": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNewImage": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNone": "Tiada", "Common.Views.ListSettingsDialog.txtOfText": "Teks %", "Common.Views.ListSettingsDialog.txtSize": "Saiz", "Common.Views.ListSettingsDialog.txtStart": "<PERSON><PERSON><PERSON> pada", "Common.Views.ListSettingsDialog.txtSymbol": "Simbol", "Common.Views.ListSettingsDialog.txtTitle": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtType": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.textInvalidRange": "Julat sel tidak sah", "Common.Views.OpenDialog.textSelectData": "Pilih data", "Common.Views.OpenDialog.txtAdvanced": "Lanjutan", "Common.Views.OpenDialog.txtColon": "Titik bertindih", "Common.Views.OpenDialog.txtComma": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtDelimiter": "Pembatas", "Common.Views.OpenDialog.txtDestData": "<PERSON><PERSON><PERSON> di mana untuk letak data", "Common.Views.OpenDialog.txtEmpty": "Medan ini diperlukan", "Common.Views.OpenDialog.txtEncoding": "Pengekodan ", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON>a laluan tidak betul.", "Common.Views.OpenDialog.txtOpenFile": "<PERSON><PERSON><PERSON><PERSON> kata laluan untuk membuka fail", "Common.Views.OpenDialog.txtOther": "<PERSON>n", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtProtected": "<PERSON><PERSON><PERSON> anda masukkan kata laluan dan buka fail, kata laluan semasa kepada fail akan diset semula.", "Common.Views.OpenDialog.txtSemicolon": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtSpace": "Jarak", "Common.Views.OpenDialog.txtTab": "Tab", "Common.Views.OpenDialog.txtTitle": "Pilih pilihan %1", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON><PERSON>", "Common.Views.PasswordDialog.txtDescription": "Tetapkan kata laluan untuk lindungi dokumen", "Common.Views.PasswordDialog.txtIncorrectPwd": "Pengesahan kata laluan tidak identical", "Common.Views.PasswordDialog.txtPassword": "<PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "Ulang kata laluan", "Common.Views.PasswordDialog.txtTitle": "Tetapkan Kata <PERSON>", "Common.Views.PasswordDialog.txtWarning": "Amaran: <PERSON><PERSON> anda hilang atau lupa kata laluan, ia tidak dapat dipulihkan. Sila simpan ia dalam tempat selamat.", "Common.Views.PluginDlg.textLoading": "Memuatkan", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "Plug masuk", "Common.Views.Plugins.strPlugins": "Plug masuk", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStop": "<PERSON><PERSON>", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "Sulitkan dengan kata laluan", "Common.Views.Protection.hintDelPwd": "Delete password", "Common.Views.Protection.hintPwd": "<PERSON><PERSON> atau padam kata laluan", "Common.Views.Protection.hintSignature": "Tambah tandatangan digital atau garis tandatangan", "Common.Views.Protection.txtAddPwd": "Tambah kata laluan", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON> kata laluan", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON> kata laluan", "Common.Views.Protection.txtEncrypt": "Sulitkan", "Common.Views.Protection.txtInvisibleSignature": "Tambah tandatangan digital", "Common.Views.Protection.txtSignature": "Tandatangan", "Common.Views.Protection.txtSignatureLine": "Tam<PERSON> garis tandatangan", "Common.Views.RecentFiles.txtOpenRecent": "Open Recent", "Common.Views.RenameDialog.textName": "<PERSON><PERSON>", "Common.Views.RenameDialog.txtInvalidName": "Nama fail tidak boleh mengandungi sebarang aksara yang berikut: ", "Common.Views.ReviewChanges.hintNext": "<PERSON>", "Common.Views.ReviewChanges.hintPrev": "<PERSON>han sebelumnya", "Common.Views.ReviewChanges.strFast": "Pantas", "Common.Views.ReviewChanges.strFastDesc": "Editing-be<PERSON><PERSON> masa-se<PERSON><PERSON>. <PERSON><PERSON><PERSON> per<PERSON>han telah disimpan secara automatic.", "Common.Views.ReviewChanges.strStrict": "Tegas", "Common.Views.ReviewChanges.strStrictDesc": "<PERSON><PERSON> butang ‘Save’ untuk menyegerakkan perubahan yang anda dan yang lain lakukan.", "Common.Views.ReviewChanges.tipAcceptCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.tipCoAuthMode": "Tetapkan mod pengeditan-bersama", "Common.Views.ReviewChanges.tipCommentRem": "<PERSON><PERSON> keluar komen", "Common.Views.ReviewChanges.tipCommentRemCurrent": "<PERSON><PERSON> keluar komen semasa", "Common.Views.ReviewChanges.tipCommentResolve": "Selesaikan komen", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Selesaikan komen semasa", "Common.Views.ReviewChanges.tipHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON> sejarah versi", "Common.Views.ReviewChanges.tipRejectCurrent": "<PERSON><PERSON> semasa", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON>", "Common.Views.ReviewChanges.tipReviewView": "<PERSON><PERSON><PERSON> mod yang anda mahu perubahan untuk dipaparkan", "Common.Views.ReviewChanges.tipSetDocLang": "Tetapkan Bahasa dokumen", "Common.Views.ReviewChanges.tipSetSpelling": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.tipSharing": "Uruskan hak akses dokumen", "Common.Views.ReviewChanges.txtAccept": "Terima", "Common.Views.ReviewChanges.txtAcceptAll": "<PERSON><PERSON> semua per<PERSON>han", "Common.Views.ReviewChanges.txtAcceptChanges": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtChat": "Sembang", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemAll": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemCurrent": "<PERSON><PERSON> keluar komen semasa", "Common.Views.ReviewChanges.txtCommentRemMy": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemove": "<PERSON><PERSON> k<PERSON>", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON><PERSON>ai<PERSON>", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtDocLang": "Bahasa", "Common.Views.ReviewChanges.txtFinal": "<PERSON><PERSON><PERSON> (Pratonton)", "Common.Views.ReviewChanges.txtFinalCap": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtHistory": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON><PERSON> (Pengeditan)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "Seterusnya", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON><PERSON> (Pratonton)", "Common.Views.ReviewChanges.txtOriginalCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtPrev": "Sebelumnya", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtSharing": "<PERSON>kon<PERSON><PERSON>", "Common.Views.ReviewChanges.txtSpelling": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textAdd": "Tambah", "Common.Views.ReviewPopover.textAddReply": "Tambah Balasan", "Common.Views.ReviewPopover.textCancel": "Batalkan", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textEnterComment": "Enter your comment here", "Common.Views.ReviewPopover.textMention": "+sebutan akan memberikan akses terhadap dokumen dan menghantar e-mel", "Common.Views.ReviewPopover.textMentionNotify": "+sebutan akan memaklumkan pengguna melalui e-mel", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Anda tidak mempunyai keizinan untuk membuka semula komen", "Common.Views.ReviewPopover.txtDeleteTip": "Padam", "Common.Views.ReviewPopover.txtEditTip": "Edit", "Common.Views.SaveAsDlg.textLoading": "Memuatkan", "Common.Views.SaveAsDlg.textTitle": "Folder untuk simpan", "Common.Views.SearchPanel.textByColumns": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textByRows": "Mengikut baris", "Common.Views.SearchPanel.textCaseSensitive": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "Common.Views.SearchPanel.textCell": "<PERSON>l", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textContentChanged": "Document changed.", "Common.Views.SearchPanel.textFind": "<PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "<PERSON><PERSON> dan <PERSON>", "Common.Views.SearchPanel.textFormula": "Formula", "Common.Views.SearchPanel.textFormulas": "Formula", "Common.Views.SearchPanel.textItemEntireCell": "<PERSON><PERSON><PERSON><PERSON> kandungan sel", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} items successfully replaced.", "Common.Views.SearchPanel.textLookIn": "<PERSON><PERSON>", "Common.Views.SearchPanel.textMatchUsingRegExp": "Sepadan menggunakan ung<PERSON>pan biasa", "Common.Views.SearchPanel.textName": "<PERSON><PERSON>", "Common.Views.SearchPanel.textNoMatches": "<PERSON><PERSON><PERSON> padanan", "Common.Views.SearchPanel.textNoSearchResults": "Tiada carian kep<PERSON>n", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} items replaced. Remaining {2} items are locked by other users.", "Common.Views.SearchPanel.textReplace": "Gantikan", "Common.Views.SearchPanel.textReplaceAll": "Gantikan Semua", "Common.Views.SearchPanel.textReplaceWith": "Gantikan dengan", "Common.Views.SearchPanel.textSearch": "<PERSON><PERSON>", "Common.Views.SearchPanel.textSearchAgain": "{0}Perform new search{1} for accurate results.", "Common.Views.SearchPanel.textSearchHasStopped": "<PERSON><PERSON> telah <PERSON>", "Common.Views.SearchPanel.textSearchOptions": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSearchResults": "Keputusan carian: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textSelectDataRange": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSheet": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSpecificRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textTooManyResults": "Terlalu banyak keputusan untuk dipaparkan di sini", "Common.Views.SearchPanel.textValue": "<PERSON><PERSON>", "Common.Views.SearchPanel.textValues": "<PERSON><PERSON>", "Common.Views.SearchPanel.textWholeWords": "<PERSON><PERSON><PERSON><PERSON> per<PERSON>an sahaja", "Common.Views.SearchPanel.textWithin": "<PERSON>", "Common.Views.SearchPanel.textWorkbook": "<PERSON><PERSON> kerja", "Common.Views.SearchPanel.tipNextResult": "Keputusan seterusnya", "Common.Views.SearchPanel.tipPreviousResult": "Keputusan sebelum", "Common.Views.SelectFileDlg.textLoading": "Memuatkan", "Common.Views.SelectFileDlg.textTitle": "<PERSON><PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "<PERSON><PERSON>", "Common.Views.SignDialog.textCertificate": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "Ubah", "Common.Views.SignDialog.textInputName": "<PERSON><PERSON> pen<PERSON> input", "Common.Views.SignDialog.textItalic": "Italik", "Common.Views.SignDialog.textNameError": "<PERSON>a penanda<PERSON>gan tidak boleh kosong.", "Common.Views.SignDialog.textPurpose": "Tu<PERSON>an menandatangani dokumen ini", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSignature": "Tandatangan kelihatan se<PERSON>i", "Common.Views.SignDialog.textTitle": "<PERSON><PERSON><PERSON> tan<PERSON>", "Common.Views.SignDialog.textUseImage": "Atau klik ‘select Image’ untuk guna gambar sebagai tandatangan", "Common.Views.SignDialog.textValid": "Sah dari %1 to %2", "Common.Views.SignDialog.tipFontName": "<PERSON><PERSON>", "Common.Views.SignDialog.tipFontSize": "<PERSON>z F<PERSON>", "Common.Views.SignSettingsDialog.textAllowComment": "Benarkan penandatangan untuk menambah komen dalam dialok tandatangan", "Common.Views.SignSettingsDialog.textDefInstruction": "Before signing this document, verify that the content you are signing is correct.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mel", "Common.Views.SignSettingsDialog.textInfoName": "<PERSON><PERSON>", "Common.Views.SignSettingsDialog.textInfoTitle": "Tajuk <PERSON>", "Common.Views.SignSettingsDialog.textInstructions": "<PERSON><PERSON> un<PERSON>", "Common.Views.SignSettingsDialog.textShowDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> tarikh tanda dalam baris tandatangan", "Common.Views.SignSettingsDialog.textTitle": "Menetapkan Tandatangan", "Common.Views.SignSettingsDialog.txtEmpty": "Medan ini diperlukan", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "<PERSON><PERSON> unikod HEX", "Common.Views.SymbolTableDialog.textCopyright": "Tanda Hak Milik", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textDOQuote": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEllipsis": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEmDash": "Tanda sengkang Em", "Common.Views.SymbolTableDialog.textEmSpace": "<PERSON><PERSON> E<PERSON>", "Common.Views.SymbolTableDialog.textEnDash": "Tanda sengkang En", "Common.Views.SymbolTableDialog.textEnSpace": "<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textFont": "Fon", "Common.Views.SymbolTableDialog.textNBHyphen": "Tiada Tanda Sempang tidak-berpecah", "Common.Views.SymbolTableDialog.textNBSpace": "<PERSON>iada-<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textPilcrow": "Tanda Pilcrow", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 <PERSON>", "Common.Views.SymbolTableDialog.textRange": "Julat", "Common.Views.SymbolTableDialog.textRecent": "Simbol digunakan baru-baru ini", "Common.Views.SymbolTableDialog.textRegistered": "Tandatangan Berdaftar", "Common.Views.SymbolTableDialog.textSCQuote": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSection": "Bahagian Tanda", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSHyphen": "Tanda sempang lembut", "Common.Views.SymbolTableDialog.textSOQuote": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSpecial": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSymbols": "Simbol", "Common.Views.SymbolTableDialog.textTitle": "Simbol", "Common.Views.SymbolTableDialog.textTradeMark": "Simbol Cap Dagang", "Common.Views.UserNameDialog.textDontShow": "<PERSON>an tanya saya lagi", "Common.Views.UserNameDialog.textLabel": "Label:", "Common.Views.UserNameDialog.textLabelError": "Label tidak boleh kosong.", "SSE.Controllers.DataTab.strSheet": "Sheet", "SSE.Controllers.DataTab.textAddExternalData": "The link to an external source has been added. You can update such links in the Data tab.", "SSE.Controllers.DataTab.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textContinue": "Continue", "SSE.Controllers.DataTab.textDontUpdate": "Don't Update", "SSE.Controllers.DataTab.textEmptyUrl": "<PERSON>a perlu menentukan URL.", "SSE.Controllers.DataTab.textRows": "<PERSON><PERSON>", "SSE.Controllers.DataTab.textTurnOff": "Turn off AutoUpdate", "SSE.Controllers.DataTab.textUpdate": "Update", "SSE.Controllers.DataTab.textWizard": "Teks ke Lajur", "SSE.Controllers.DataTab.txtDataValidation": "Pengesahan data", "SSE.Controllers.DataTab.txtErrorExternalLink": "Error: updating is failed", "SSE.Controllers.DataTab.txtExpand": "Kembangkan", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Data di sebelah pilihan tidak akan dialih keluar. <PERSON><PERSON><PERSON> anda mahu meluaskan pilihan untuk sertakan data bertentangan atau teruskan sel pilihan sahaja?", "SSE.Controllers.DataTab.txtExtendDataValidation": "<PERSON><PERSON>han ini mengandungi beberapa sel tanpa seting Pengesahan Data.<br> <PERSON><PERSON><PERSON> anda mahu lanju<PERSON>kan Pengesahan Data bagi sel tersebut?", "SSE.Controllers.DataTab.txtImportWizard": "Import Teks Bestari", "SSE.Controllers.DataTab.txtRemDuplicates": "<PERSON><PERSON>", "SSE.Controllers.DataTab.txtRemoveDataValidation": "<PERSON><PERSON>han ini mengandungi lebih daripada satu jenis pengesahan.<br><PERSON><PERSON><PERSON> seting semasa dan teruskan?", "SSE.Controllers.DataTab.txtRemSelected": "<PERSON><PERSON> keluar dalam dipilih", "SSE.Controllers.DataTab.txtUrlTitle": "Tampal URL data", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "This workbook contains links to external sources which update automatically. This might be unsafe.<br><br>If you trust them, press Continue.", "SSE.Controllers.DataTab.warnUpdateExternalData": "This workbook contains links to one or more external sources that could be unsafe.<br>If you trust the links, update them to get the latest data.", "SSE.Controllers.DocumentHolder.alignmentText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.centerText": "Pusat", "SSE.Controllers.DocumentHolder.deleteColumnText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.deleteRowText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.deleteText": "Padam", "SSE.Controllers.DocumentHolder.errorInvalidLink": "<PERSON><PERSON><PERSON><PERSON> pautan tidak wujud. <PERSON>la betulkan pautan atau padam ia.", "SSE.Controllers.DocumentHolder.guestText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertColumnRightText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Di Atas Baris", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Di Bawah Baris", "SSE.Controllers.DocumentHolder.insertText": "<PERSON>sip<PERSON>", "SSE.Controllers.DocumentHolder.leftText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.rightText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Pilihan AutoBaiki", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON><PERSON> {0} simbol ({1} piksel)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Ketinggian Baris mata {0} (p<PERSON><PERSON> {1})", "SSE.Controllers.DocumentHolder.textCtrlClick": "<PERSON>lik pautan untuk buka dan klik dan tahan butang tetikus untuk pilih sel.", "SSE.Controllers.DocumentHolder.textInsertLeft": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textInsertTop": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textPasteSpecial": "<PERSON><PERSON> is<PERSON>wa", "SSE.Controllers.DocumentHolder.textStopExpand": "<PERSON><PERSON><PERSON><PERSON> mengembangkan jadual secara automatik", "SSE.Controllers.DocumentHolder.textSym": "sim", "SSE.Controllers.DocumentHolder.tipIsLocked": "Elemen ini sedang diedit oleh pengguna lain.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Atas purata", "SSE.Controllers.DocumentHolder.txtAddBottom": "Tambah sempadan bawah", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Tambah bar pecahan", "SSE.Controllers.DocumentHolder.txtAddHor": "Tam<PERSON> garis melintang", "SSE.Controllers.DocumentHolder.txtAddLB": "Tambah sempadan bawah kiri", "SSE.Controllers.DocumentHolder.txtAddLeft": "Tambah sempadan kiri", "SSE.Controllers.DocumentHolder.txtAddLT": "Tambah garis atas kiri", "SSE.Controllers.DocumentHolder.txtAddRight": "Tambah sempadan kanan", "SSE.Controllers.DocumentHolder.txtAddTop": "Tambah sempadan atas", "SSE.Controllers.DocumentHolder.txtAddVer": "<PERSON><PERSON> garis menegak", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Jajarkan ke aksara", "SSE.Controllers.DocumentHolder.txtAll": "(<PERSON><PERSON><PERSON>)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "<PERSON><PERSON><PERSON><PERSON> keselu<PERSON>han kandungan jadual atau lajur jadual tertentu termasuk pengepala lajur, data dan jumlah baris", "SSE.Controllers.DocumentHolder.txtAnd": "dan", "SSE.Controllers.DocumentHolder.txtBegins": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtBelowAve": "<PERSON> b<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtBlanks": "(Kosong)", "SSE.Controllers.DocumentHolder.txtBorderProps": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtBottom": "<PERSON>wa<PERSON>", "SSE.Controllers.DocumentHolder.txtByField": "%1 of %2", "SSE.Controllers.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtColumnAlign": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtContains": "Mengandungi", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Pautan disalin ke papan klip", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Kembalikan data sel bagi jadual atau lajur jadual tertentu", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON> saiz argument", "SSE.Controllers.DocumentHolder.txtDeleteArg": "<PERSON><PERSON> argument", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Padam pemisah manual", "SSE.Controllers.DocumentHolder.txtDeleteChars": "<PERSON><PERSON> aksara <PERSON>n", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "<PERSON><PERSON> aksara lampiran dan pemisah", "SSE.Controllers.DocumentHolder.txtDeleteEq": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON> char", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Padam radikal", "SSE.Controllers.DocumentHolder.txtEnds": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtEquals": "<PERSON><PERSON> den<PERSON>", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "<PERSON>a dengan warna sel", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "<PERSON>a dengan warna tulisan", "SSE.Controllers.DocumentHolder.txtExpand": "Kembangkan dan sisih", "SSE.Controllers.DocumentHolder.txtExpandSort": "Data di sebelah pilihan tidak akan diisih. <PERSON><PERSON><PERSON> anda mahu meluaskan pilihan untuk sertakan data bertentangan atau teruskan sel pilihan sahaja?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "<PERSON>wa<PERSON>", "SSE.Controllers.DocumentHolder.txtFilterTop": "Atas", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Ubah ke pecahan linear", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON> kepada pecahan terpencong", "SSE.Controllers.DocumentHolder.txtFractionStacked": "<PERSON><PERSON> ke pecahan bertindan", "SSE.Controllers.DocumentHolder.txtGreater": "<PERSON><PERSON><PERSON> besar dari", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "<PERSON><PERSON><PERSON> besar dari atau sama dengan", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "<PERSON>ksara melebihi teks", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Aksara di bawah teks", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "<PERSON><PERSON><PERSON> pengepala lajur untuk jadual atau lajur jadual tertentu", "SSE.Controllers.DocumentHolder.txtHeight": "Ketinggian", "SSE.Controllers.DocumentHolder.txtHideBottom": "Sembunyikan sempadan bawah", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> had bawah", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "<PERSON><PERSON><PERSON> tanda kurung tersemb<PERSON>yi", "SSE.Controllers.DocumentHolder.txtHideDegree": "Sembunyikan darjah", "SSE.Controllers.DocumentHolder.txtHideHor": "Sembunyikan garis melintang", "SSE.Controllers.DocumentHolder.txtHideLB": "Sembunyikan garis kiri bawah", "SSE.Controllers.DocumentHolder.txtHideLeft": "Sembunyikan sempadan kiri", "SSE.Controllers.DocumentHolder.txtHideLT": "Sembunyikan garis kiri atas", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Sembunyikan bukaan tanda kurung", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Sembunyikan pemegang tempat", "SSE.Controllers.DocumentHolder.txtHideRight": "Sembunyikan sempadan kanan", "SSE.Controllers.DocumentHolder.txtHideTop": "Sembunyikan sempadan atas", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> had atas", "SSE.Controllers.DocumentHolder.txtHideVer": "Sembunyikan garis menegak", "SSE.Controllers.DocumentHolder.txtImportWizard": "Import Teks Bestari", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "<PERSON><PERSON><PERSON><PERSON> saiz argument", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Sisipkan argumen selepas", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Sisipkan argumen sebelum", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Sisipkan pemisah manual", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "<PERSON>si<PERSON><PERSON> persa<PERSON>an se<PERSON>pas", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "<PERSON>sip<PERSON> persamaan sebelum", "SSE.Controllers.DocumentHolder.txtItems": "item", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Kekalkan teks sahaja", "SSE.Controllers.DocumentHolder.txtLess": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtLessEquals": "<PERSON><PERSON> da<PERSON>ada atau sama dengan", "SSE.Controllers.DocumentHolder.txtLimitChange": "Ubah lokasi had", "SSE.Controllers.DocumentHolder.txtLimitOver": "<PERSON><PERSON><PERSON> had teks", "SSE.Controllers.DocumentHolder.txtLimitUnder": "<PERSON><PERSON><PERSON><PERSON> had teks", "SSE.Controllers.DocumentHolder.txtLockSort": "Data dijumpai selepas pilihan anda, tetapi anda tidak mempunyai keizinan yang mencukupi untuk mengubah sel tersebut.<br><PERSON><PERSON><PERSON> anda mahu teruskan dengan pilihan semasa?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Sepadankan tanda kurung kepada ketinggian argument.", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON><PERSON><PERSON> matriks", "SSE.Controllers.DocumentHolder.txtNoChoices": "Tidak ada pilihan untuk mengisi sel.<br><PERSON><PERSON> nilai teks dari lajur yang boleh dipilih untuk penggantian.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Tidak bermula dengan", "SSE.Controllers.DocumentHolder.txtNotContains": "Tidak mengandungi", "SSE.Controllers.DocumentHolder.txtNotEnds": "Tidak be<PERSON>", "SSE.Controllers.DocumentHolder.txtNotEquals": "Tidak sama", "SSE.Controllers.DocumentHolder.txtOr": "atau", "SSE.Controllers.DocumentHolder.txtOverbar": "Bar melepasi teks", "SSE.Controllers.DocumentHolder.txtPaste": "<PERSON>l", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formula tanpa sempadan", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formula + lebar lajur", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Pemformatan <PERSON>", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Tampal hanya pemformatan", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formula + format nombor", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Tampal hanya formula", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formula + semua pemformatan", "SSE.Controllers.DocumentHolder.txtPasteLink": "<PERSON><PERSON> p<PERSON>an", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Gambar <PERSON>", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Gabungkan pemformatan bersyarat", "SSE.Controllers.DocumentHolder.txtPastePicture": "Gambar", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Sumber pemformatan", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Silang Ganti", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Nilai + semua pemformatan", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Nilai + format nombor", "SSE.Controllers.DocumentHolder.txtPasteValues": "<PERSON>l hanya nilai", "SSE.Controllers.DocumentHolder.txtPercent": "peratus", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Buat semula jadual autopengembangan", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "<PERSON><PERSON> keluar bar pecahan", "SSE.Controllers.DocumentHolder.txtRemLimit": "<PERSON><PERSON> k<PERSON> had", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "<PERSON><PERSON> keluar aksara aksen", "SSE.Controllers.DocumentHolder.txtRemoveBar": "<PERSON><PERSON> keluar bar", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "<PERSON><PERSON><PERSON> anda mahu alih keluar tandatangan ini?<br>Ia tidak boleh dibuat asal.", "SSE.Controllers.DocumentHolder.txtRemScripts": "<PERSON><PERSON> keluar skrip", "SSE.Controllers.DocumentHolder.txtRemSubscript": "<PERSON><PERSON> keluar subskrip", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "<PERSON><PERSON> keluar superskrip", "SSE.Controllers.DocumentHolder.txtRowHeight": "Ketinggian Baris", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Skrip selepas teks", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Skrip sebelum teks", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> had bawah", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON> tanda kurung penutup", "SSE.Controllers.DocumentHolder.txtShowDegree": "Tunjukkan darjah", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON><PERSON><PERSON> tanda kurung bukaan", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Tunjuk pemegang tempat", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> had atas", "SSE.Controllers.DocumentHolder.txtSorting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtSortSelected": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Regang<PERSON> tanda <PERSON>", "SSE.Controllers.DocumentHolder.txtThisRowHint": "<PERSON>ya pilih baris ini bagi lajur yang ditentukan", "SSE.Controllers.DocumentHolder.txtTop": "Atas", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Ke<PERSON>lik<PERSON> jumlah baris bagi jadual atau lajur jadual te<PERSON>tu", "SSE.Controllers.DocumentHolder.txtUnderbar": "Bar di bawah teks", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Buat asal autopengembangan jadual", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Gunakan bestari import teks", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Mengklik pautan ini boleh membahayakan peranti dan data anda.<br><PERSON><PERSON><PERSON> anda mahu men<PERSON>?", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.warnFilterError": "You need at least one field in the Values area in order to apply a value filter.", "SSE.Controllers.FormulaDialog.sCategoryAll": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Custom", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "<PERSON>as", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "<PERSON><PERSON><PERSON> dan masa", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Informasi", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 terakhir digunakan", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logik", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Cari ke atas dan rujukan", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Matematik", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistikal", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Teks dan data", "SSE.Controllers.LeftMenu.newDocumentTitle": "<PERSON><PERSON><PERSON> tanpa nama", "SSE.Controllers.LeftMenu.textByColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textByRows": "Mengikut baris", "SSE.Controllers.LeftMenu.textFormulas": "Formula", "SSE.Controllers.LeftMenu.textItemEntireCell": "<PERSON><PERSON><PERSON><PERSON> kandungan sel", "SSE.Controllers.LeftMenu.textLoadHistory": "<PERSON><PERSON> sejarah dimua<PERSON>…", "SSE.Controllers.LeftMenu.textLookin": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textNoTextFound": "Data yang andaa sedang cari tidak dijumpai. Sila laras pilihan carian.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "<PERSON><PERSON>an telah dilakukan. {0} kejadian telah dilangkau.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "<PERSON><PERSON> telah <PERSON>. Kejadian digantikan: {0}", "SSE.Controllers.LeftMenu.textSave": "Save", "SSE.Controllers.LeftMenu.textSearch": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "SSE.Controllers.LeftMenu.textSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textValues": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWarning": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWithin": "<PERSON>", "SSE.Controllers.LeftMenu.textWorkbook": "<PERSON><PERSON> kerja", "SSE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.warnDownloadAs": "<PERSON>ka anda terus menyimpan dalam format ini semua ciri kecuali teks akan hilang.<br><PERSON><PERSON><PERSON> anda pasti mahu teruskan?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "The CSV format does not support saving a multi-sheet file.<br>To keep the selected format and save only the current sheet, press Save.<br>To save the current spreadsheet, click Cancel and save it in a different format.", "SSE.Controllers.Main.confirmAddCellWatches": "This action will add {0} cell watches.<br>Do you want to continue?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "This action will add only {0} cell watches by memory save reason.<br>Do you want to continue?", "SSE.Controllers.Main.confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "SSE.Controllers.Main.confirmMoveCellRange": "Julat sel destinasi boleh mengandungi data. Teruskan operasi?", "SSE.Controllers.Main.confirmPutMergeRange": "Sumber data mengandungi sel bercantum.<br>Ia perlu dinyahcantum sebelum ditampal ke dalam jadual", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Formula dalam baris pengepala akan dialih keluar dan ditukarkan kepada teks statik.<br><PERSON><PERSON><PERSON> anda mahu teruskan?", "SSE.Controllers.Main.confirmReplaceHFPicture": "Only one picture can be inserted in each section of the header.<br>Press \"Replace\" to replace existing picture.<br>Press \"Keep\" to keep existing picture.", "SSE.Controllers.Main.convertationTimeoutText": "<PERSON><PERSON><PERSON><PERSON> masa tamat <PERSON>.", "SSE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON> \"OK\" untuk kembali ke senarai dokumen.", "SSE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON>", "SSE.Controllers.Main.downloadErrorText": "<PERSON>at turun telah gagal.", "SSE.Controllers.Main.downloadTextText": "<PERSON><PERSON><PERSON> dimuat turun…", "SSE.Controllers.Main.downloadTitleText": "<PERSON><PERSON><PERSON> dimuat turun", "SSE.Controllers.Main.errNoDuplicates": "<PERSON>iada nilai pendua dijumpai.", "SSE.Controllers.Main.errorAccessDeny": "Anda sedang cuba untuk melakukan tindakan yang anda tidak mempunyai hak untuk.<br><PERSON><PERSON>, hubungi pentadbir Pelayan Dokumen anda.", "SSE.Controllers.Main.errorArgsRange": "Terdapat ralat dalam formula yang dimasukkan.<br>Julat argumen yang tidak betul digunakan.", "SSE.Controllers.Main.errorAutoFilterChange": "Operasi ini tidak di<PERSON>, kerana cubaan ia untuk menganjak sel dalam jadual pada lembaran kerja anda.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Operasi tidak dapat dilakukan bagi sel yang dipilih kerana anda tidak dapat mengalih sebahagian daripada jadual.<br><PERSON><PERSON><PERSON> julat data yang lain supaya keseluruhan jadual telah teranjak dan cuba semula.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Operasi tidak dapat dilakukan bagi julat sel yang dipilih.<br><PERSON><PERSON><PERSON> julat data seragam yang berbeza dari sedia ada dan cuba semula.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Operasi tidak boleh dilakukan kerana kawasan mengandungi sel bertapis.<br><PERSON><PERSON>, nyah<PERSON>mb<PERSON>yi elemen ditapis dan cuba semula.", "SSE.Controllers.Main.errorBadImageUrl": "Imej URL tidak betul", "SSE.Controllers.Main.errorCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the spreadsheet.", "SSE.Controllers.Main.errorCannotUngroup": "Tidak dapat leraikan. Untuk memu<PERSON> rang<PERSON>, pilih baris dan lajur butiran dan kumpulkan semuanya.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Anda tidak boleh menggunakan arahan ini pada helaian terlindung. Untuk menggunakan arahan ini, nyah<PERSON>ung helaian.<br>Anda mungkin diminta untuk memasukkan kata laluan.", "SSE.Controllers.Main.errorChangeArray": "<PERSON>a tidak boleh mengubah bahagian tatas<PERSON>n.", "SSE.Controllers.Main.errorChangeFilteredRange": "Ini akan mengubah julat tapisan pada lembaran kerja anda.<br>Untuk melengkapkan tugas ini, sila alih keluar AutoPengisian.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "Sel atau carta yang anda cuba ubah ialah helaian dilindung.<br><PERSON>tu<PERSON> membuat per<PERSON>, nyah<PERSON><PERSON> helaian. Anda mungkin diminta untuk memasukkan kata laluan.", "SSE.Controllers.Main.errorCircularReference": "There are one or more circular references where a formula refers to its own cell either directly or indirectly.<br>Try removing or changing these references, or moving the formulas to different cells.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Pelayan sambungan hilang. Dokumen tidak dapat diedit buat masa sekarang.", "SSE.Controllers.Main.errorConnectToServer": "Dokumen tidak dapat disimpan. <PERSON><PERSON> semak seting sambungan atau hubungi pentadbir anda.<br><PERSON><PERSON><PERSON><PERSON> anda klik butang ‘OK’, anda akan diminta untuk muat turun dokumen.", "SSE.Controllers.Main.errorConvertXml": "The file has an unsupported format.<br>Only XML Spreadsheet 2003 format can be used.", "SSE.Controllers.Main.errorCopyMultiselectArea": "<PERSON><PERSON><PERSON> ini tidak boleh digunakan dengan berbilang pilihan.<br> <PERSON><PERSON><PERSON> julat tunggal dan cuba lagi.", "SSE.Controllers.Main.errorCountArg": "Terdapat ralat dalam formula yang dimasukkan.<br>Nombor argumen yang tidak betul digunakan.", "SSE.Controllers.Main.errorCountArgExceed": "Terdapat ralat dalam formula yang dimasukkan.<br>Bilangan argumen telah melebihi.", "SSE.Controllers.Main.errorCreateDefName": "Julat nama sedia ada tidak boleh diedit dan yang baharu tidak boleh dicipta<br>pada masa sekarang kerana beberapa daripadanya sedang diedit.", "SSE.Controllers.Main.errorCreateRange": "The existing ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON> luaran.<br><PERSON><PERSON> sambungan data asas. <PERSON>la hubungi sokongan sekiranya ralat masih berlaku.", "SSE.Controllers.Main.errorDataEncrypted": "<PERSON><PERSON><PERSON> per<PERSON>han yang telah di<PERSON>, mereka tidak dapat dita<PERSON>.", "SSE.Controllers.Main.errorDataRange": "Julat data tidak betul.", "SSE.Controllers.Main.errorDataValidate": "<PERSON><PERSON> yang anda masukkan adalah tidak sah.<br>Pengguna telah membataskan nilai yang boleh dimasukkan ke dalam sel ini.", "SSE.Controllers.Main.errorDefaultMessage": "Kod ralat: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Anda sedang cuba untuk memadam lajur yang mengandungi sel terkunci. Sel terkunci tidak dapat dipadam semasa lembaran kerja terlindung.<br>Untuk memadam sel terkunci, n<PERSON><PERSON><PERSON> he<PERSON>. Anda mungkin diminta untuk memasukkan kata laluan.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Anda sedang cuba untuk memadam lajur yang mengandungi sel terkunci. Sel terkunci tidak dapat dipadam semasa lembaran kerja terlindung.<br>Untuk memadam sel terkunci, n<PERSON><PERSON><PERSON> he<PERSON>. Anda mungkin diminta untuk memasukkan kata laluan.", "SSE.Controllers.Main.errorDependentsNoFormulas": "The Trace Dependents command found no formulas that refer to the active cell.", "SSE.Controllers.Main.errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "SSE.Controllers.Main.errorEditingDownloadas": "Terdapat ralat yang berlaku semasa bekerja dengan dokumen.<br><PERSON><PERSON><PERSON> pilihan 'Download as' untuk menyimpan salinan fail sandaran ke pemacu keras komputer anda.", "SSE.Controllers.Main.errorEditingSaveas": "Terdapat ralat yang berlaku semasa bekerja dengan dokumen.<br><PERSON><PERSON><PERSON> pilihan 'Save as...' untuk menyimpan salinan fail sandaran ke pemacu keras komputer anda.", "SSE.Controllers.Main.errorEditView": "<PERSON><PERSON> he<PERSON>an sedia ada tidak boleh diedit dan yang baharu tidak boleh dicipta pada masa sekarang kerana beberapa daripadanya sedang diedit.", "SSE.Controllers.Main.errorEmailClient": "Tiada e-mel klien yang dijumpai.", "SSE.Controllers.Main.errorFilePassProtect": "<PERSON><PERSON> dilindungi kata laluan dan tidak boleh dibuka.", "SSE.Controllers.Main.errorFileRequest": "<PERSON><PERSON> luaran.<br><PERSON><PERSON> permin<PERSON> fail. <PERSON><PERSON> hubungi sokongan sekiranya ralat masih berlaku.", "SSE.Controllers.Main.errorFileSizeExceed": "Saiz fail me<PERSON><PERSON><PERSON> had ditetapkan untuk pelayan anda.<br><PERSON><PERSON>, hubungi pentadbir <PERSON> anda untuk butiran.", "SSE.Controllers.Main.errorFileVKey": "<PERSON><PERSON> luaran.<br><PERSON><PERSON><PERSON> keselamatan tidak Betul. <PERSON>la hubungi sokongan sekiranya ralat masih berlaku.", "SSE.Controllers.Main.errorFillRange": "Tidak dapat mengisi julat sel yang dipilih.<br><PERSON><PERSON><PERSON> sel tercantum perlulah sama saiz.", "SSE.Controllers.Main.errorForceSave": "Terdapat ralat yang berlaku semasa menyimpan fail. <PERSON><PERSON> gunakan pilihan 'Download as' untuk menyimpan salinan fail sandaran ke pemacu keras komputer anda dan cuba semula nanti.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Cannot enter a formula for an item or field name in a pivot table report.", "SSE.Controllers.Main.errorFormulaName": "Terdapat ralat dalam formula yang dimasukkan.<br>Nama formula yang tidak betul digunakan.", "SSE.Controllers.Main.errorFormulaParsing": "<PERSON><PERSON> da<PERSON>an semasa pengh<PERSON>an formula.", "SSE.Controllers.Main.errorFrmlMaxLength": "Panjang formula anda mele<PERSON><PERSON> had 8192 aksara.<br><PERSON><PERSON> edit ia dan cuba semula.", "SSE.Controllers.Main.errorFrmlMaxReference": "Anda tidak boleh masukkan formula ini kerana ia mempunyai banyak nilai,<br>rujukan sel dan/atau nama.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "<PERSON>lai teks dalam formula telah dihadkan kepada 255 aksara. <PERSON><PERSON>n fungsi CONCATENATE atau operator perangkaian (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Fungsi merujuk kepada helain yang tidak wujud.<br><PERSON><PERSON>, semak data dan cuba lagi.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Operasi tidak dapat dilengkapkan bagi julat sel yang dipilih.<br><PERSON><PERSON>h satu julat supaya baris pertama jadual berada pada baris yang sama<br>dan men<PERSON><PERSON><PERSON><PERSON> jadual bertindih pada yang semasa.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Operasi tidak dapat dilengkapkan bagi julat sel yang dipilih.<br><PERSON><PERSON><PERSON> julat yang mana tidak beserta jadual lain.", "SSE.Controllers.Main.errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "SSE.Controllers.Main.errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInvalidRef": "<PERSON><PERSON><PERSON>n nama betul bagi pilihan atau rujukan sah untuk pergi kepada.", "SSE.Controllers.Main.errorKeyEncrypt": "Pemerihal kunci tidak diketahui.", "SSE.Controllers.Main.errorKeyExpire": "<PERSON><PERSON><PERSON><PERSON> kunci tamat tempoh", "SSE.Controllers.Main.errorLabledColumnsPivot": "Untuk mencipta jadual pivot, gunakan data yang diaturkan sebagai senarai dengan lajur berlabel.", "SSE.Controllers.Main.errorLoadingFont": "Fon tidak dimuatkan.<br><PERSON><PERSON> hubungi pentadbir Pelayan <PERSON> anda.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Rujukan bagi julat lokasi atau data adalah tidak sah.", "SSE.Controllers.Main.errorLockedAll": "Operasi tidak boleh dilakukan kerana helaian telah dikunci oleh pengguna yang lain.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "One of the cells involved in the goal seek process has been modified by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "<PERSON>a tidak boleh mengubah data dalam jadual pangsi.", "SSE.Controllers.Main.errorLockedWorksheetRename": "<PERSON><PERSON><PERSON> tidak boleh dinamaka semula pada masa ini kerana ia sedang dinamakan semula oleh pengguna lain", "SSE.Controllers.Main.errorMaxPoints": "Bilangan maksimum mata dalam siri per carta ialah 4096.", "SSE.Controllers.Main.errorMoveRange": "Tidak boleh mengubah bahagian daripada sel tercantum", "SSE.Controllers.Main.errorMoveSlicerError": "Penghiris Jadual tidak boleh disalin dari satu buku kerja ke yang lain.<br>Cuba semula dengan memilih kese<PERSON><PERSON>han jadual dan penghiris.", "SSE.Controllers.Main.errorMultiCellFormula": "Formula tatasusunan multisel tidak diizinkan dalam jadual.", "SSE.Controllers.Main.errorNoDataToParse": "Tiada data yang dipilih untuk penguraian.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.Main.errorOpenWarning": "Salah satu dari formula fail me<PERSON><PERSON><PERSON> had 8192 aksara.<br>Formula telah dialih keluar.", "SSE.Controllers.Main.errorOperandExpected": "Fungsi sintaks yang dimasukkan adalah tidak betul. <PERSON>la semak jika anda sedang kehilangan satu daripada tanda kurung - '(' or ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "<PERSON>a laluan yang anda bekalkan tidak betul.<br><PERSON><PERSON><PERSON> bahawa kunci CAPS LOCK dimatikan dan pastikan guna penghurufbesaran yang betul.", "SSE.Controllers.Main.errorPasteInPivot": "We can't make this change for the selected cells because it will affect a pivot table.<br>Use the field list to change the report.", "SSE.Controllers.Main.errorPasteMaxRange": "<PERSON>in dan tampal Ka<PERSON>an tidak sepadan.<br><PERSON><PERSON> pilih <PERSON>an dengan saiz sama atau klik sel pertama dalam baris untuk menampal sel disalin.", "SSE.Controllers.Main.errorPasteMultiSelect": "Tindakan ini tidak dapat dilakukan pada pelbagai julat pilihan .<br>", "SSE.Controllers.Main.errorPasteSlicerError": "<PERSON><PERSON><PERSON><PERSON><PERSON> tidak boleh disalin dari satu buku kerja ke yang lain.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Pivot table field name already exists.", "SSE.Controllers.Main.errorPivotGroup": "Tidak dapat mengumpulkan pemilihan tersebut.", "SSE.Controllers.Main.errorPivotOverlap": "Laporan jadual pivot tidak boleh menindih jadual.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Laporan Jadual Pivot telah disimpan tanpa data dasar.<br><PERSON><PERSON> but<PERSON> '<PERSON>fresh' untuk kemas kini laporan.", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "The Trace Precedents command requires that the active cell contain a formula which includes a valid references.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "<PERSON><PERSON><PERSON>, i<PERSON>a tidak mungkin untuk mencetak lebih dari daripada 1500 halaman sekaligus dalam versi program semasa.<br>Pembatasan ini akan dialih keluar dalam keluaran akan datang.", "SSE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON> disimpan", "SSE.Controllers.Main.errorProtectedRange": "This range is not allowed for editing.", "SSE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "SSE.Controllers.Main.errorServerVersion": "Versi editor te<PERSON> di<PERSON>. <PERSON><PERSON> akan dimuat semula untuk menggunakan perubahan.", "SSE.Controllers.Main.errorSessionAbsolute": "Sesi pengeditan dokumen telah tamat tempoh. Sila muat semula halaman.", "SSE.Controllers.Main.errorSessionIdle": "<PERSON><PERSON><PERSON> tidak diedit buat masa yang lama. <PERSON>la muat semula halaman.", "SSE.Controllers.Main.errorSessionToken": "Sambungan kepada pelayan telah terganggu. <PERSON>la muat semula halaman.", "SSE.Controllers.Main.errorSetPassword": "Kata laluan tidak dapat ditetapkan.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Rujukan lokasi adalah tidak sah kerana sel bukan semuanya berada dalam lajur atau baris yang sama.<br><PERSON><PERSON><PERSON> sel yang semuanya berada dalam lajur atau baris tunggal.", "SSE.Controllers.Main.errorStockChart": "Urutan baris tidak betul. Untuk membina carta stok, tempatkan data pada helaian mengikut urutan:<br> harga bukaan, harga mak, harga min, harga penutup.", "SSE.Controllers.Main.errorToken": "Dokumen token keselamatan kini tidak dibentuk.<br><PERSON><PERSON> hubungi pentadbir Pelayan Dokumen.", "SSE.Controllers.Main.errorTokenExpire": "Dokumen token keselamatan telah tamat tempoh.<br><PERSON><PERSON> hubungi pentadbir Pelayan Dokumen.", "SSE.Controllers.Main.errorUnexpectedGuid": "<PERSON><PERSON> lua<PERSON>.<br>GUID tidak dijangka. Sila hubungi sokongan sekiranya ralat masih berlaku.", "SSE.Controllers.Main.errorUpdateVersion": "<PERSON><PERSON><PERSON> fail telah berubah. <PERSON><PERSON> akan dimuatkan semula.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Sambungan telah dipulihkan, dan versi fail telah berubah.<br><PERSON><PERSON><PERSON> anda boleh terus bekerja, anda perlu memuat turun fail atau menyalin kandungannya untuk memastikan tiada ada yang hilang, dan kemudian muat semula halaman ini.", "SSE.Controllers.Main.errorUserDrop": "<PERSON>ail tidak boleh diakses sekarang.", "SSE.Controllers.Main.errorUsersExceed": "Bilangan pengguna yang didizinkan mengikut pelan pembayaran telah lebih", "SSE.Controllers.Main.errorViewerDisconnect": "Sambungan telah hilang. <PERSON>a masih boleh melihat dokumen,<br>tetapi tidak dapat muat turun atau cetaknya sehingga sambungan dipulihkan dan halaman dimuat semua.", "SSE.Controllers.Main.errorWrongBracketsCount": "Terdapat ralat dalam formula yang dimasukkan.<br>Bilangan tanda kurung yang silap digunakan.", "SSE.Controllers.Main.errorWrongOperator": "Terdapat ralat dalam formula yang dimasukkan. Operator yang salah digunakan.<br><PERSON><PERSON> betulkan ralat.", "SSE.Controllers.Main.errorWrongPassword": "<PERSON>a laluan yang anda bekalkan tidak betul.", "SSE.Controllers.Main.errRemDuplicates": "<PERSON><PERSON> pendua dijumpai dan dipadam: {0}, baki nilai unik: {1}.", "SSE.Controllers.Main.leavePageText": "Anda mempunyai perubahan yang tidak disimpan dalam hamparan ini. <PERSON><PERSON> \"Stay on this Page\" kemudian \"Save\" untuk menyimpannya. <PERSON><PERSON> 'Leave this Page' untuk membuang semua perubahan yang tidak disimpan.", "SSE.Controllers.Main.leavePageTextOnClose": "<PERSON><PERSON><PERSON> perubahan yang tidak disimpan dalam hamparan ini akan hilang.<br> <PERSON><PERSON> \"<PERSON><PERSON>\" kem<PERSON>an \"Save\" untuk menyimpannya. <PERSON><PERSON> \"OK\" untuk membuang semua perubahan yang tidak disimpan.", "SSE.Controllers.Main.loadFontsTextText": "Data dimuatkan…", "SSE.Controllers.Main.loadFontsTitleText": "Data Dimuatkan", "SSE.Controllers.Main.loadFontTextText": "Data dimuatkan…", "SSE.Controllers.Main.loadFontTitleText": "Data Dimuatkan", "SSE.Controllers.Main.loadImagesTextText": "<PERSON><PERSON><PERSON>…", "SSE.Controllers.Main.loadImagesTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadImageTextText": "<PERSON><PERSON><PERSON>…", "SSE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadingDocumentTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.notcriticalErrorTitle": "<PERSON><PERSON>", "SSE.Controllers.Main.openErrorText": "<PERSON><PERSON> telah berlaku semasa membuka fail.", "SSE.Controllers.Main.openTextText": "<PERSON><PERSON><PERSON>…", "SSE.Controllers.Main.openTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.pastInMergeAreaError": "Tidak boleh mengubah bahagian daripada sel tercantum", "SSE.Controllers.Main.printTextText": "<PERSON><PERSON><PERSON>…", "SSE.Controllers.Main.printTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.reloadButtonText": "<PERSON><PERSON>", "SSE.Controllers.Main.requestEditFailedMessageText": "Seseorang sedang mengedit dokumen ini sekarang ini. Sila cuba semula nanti.", "SSE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.saveErrorText": "<PERSON><PERSON> telah berlaku semasa men<PERSON> fail.", "SSE.Controllers.Main.saveErrorTextDesktop": "<PERSON>ail ini tidak boleh disimpan atau dicipta.<br><PERSON><PERSON><PERSON><PERSON><PERSON> alasannya adalah: <br>1. <PERSON>ail ini ialah untuk dibaca-sahaja. <br>2. <PERSON><PERSON> sedang diedit oleh pengguna lain. <br>3. <PERSON><PERSON><PERSON> telah penuh atau rosak.", "SSE.Controllers.Main.saveTextText": "<PERSON><PERSON><PERSON> disimpan…", "SSE.Controllers.Main.saveTitleText": "<PERSON><PERSON><PERSON> disimpan", "SSE.Controllers.Main.scriptLoadError": "Sambungan terlalu per<PERSON>, beberapa komponen tidak dapat dimuatkan. Sila muat semula halaman.", "SSE.Controllers.Main.textAnonymous": "<PERSON><PERSON>", "SSE.Controllers.Main.textApplyAll": "<PERSON>a kepada semua", "SSE.Controllers.Main.textBuyNow": "<PERSON><PERSON> web", "SSE.Controllers.Main.textChangesSaved": "<PERSON><PERSON><PERSON> disimpan", "SSE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textCloseTip": "Klik untuk tutup petua", "SSE.Controllers.Main.textConfirm": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "SSE.Controllers.Main.textContactUs": "<PERSON><PERSON><PERSON><PERSON> jualan", "SSE.Controllers.Main.textContinue": "Continue", "SSE.Controllers.Main.textConvertEquation": "Per<PERSON><PERSON><PERSON> telah dicipta dengan versi lama editor per<PERSON><PERSON><PERSON> yang tidal lagi disokong. <PERSON><PERSON><PERSON> men<PERSON>, tukar persamaan kepada format Office Math ML.<br><PERSON><PERSON>?", "SSE.Controllers.Main.textCustomLoader": "Sila ambil maklum bahawa menurut kepada terma lesen yang anda tidak berhak untuk ubah pemuat.<br><PERSON>la hubungi Jabatan Jualan kami untuk mendapatkan sebut harga.", "SSE.Controllers.Main.textDisconnect": "Sambungan telah hilang", "SSE.Controllers.Main.textFillOtherRows": "<PERSON><PERSON> baris lain", "SSE.Controllers.Main.textFormulaFilledAllRows": "Baris {0} diisi formula mempunyai data. Mengisi baris kosong lain mungkin mengambil sedikit masa.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Baris {0} pertama diisi formula. Mengisi baris kosong lain mungkin mengambil sedikit masa.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "<PERSON>ya baris {0} pertama diisi formula mempunyai data dengan sebab simpan memori. Terdapat {1} baris kosong lain mempunyai data dalam helaian ini. Anda boleh mengisinya secara manual.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Baris {0} pertama diisi formula dengan sebab simpan memori. Baris kosong lain dalam helaian ini tidak mempunyai data.", "SSE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textHasMacros": "<PERSON>ail mengandungi makro automatik.<br><PERSON><PERSON><PERSON> anda mahu jalankan makro?", "SSE.Controllers.Main.textKeep": "Keep", "SSE.Controllers.Main.textLearnMore": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textLoadingDocument": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textLongName": "<PERSON><PERSON><PERSON><PERSON> nama yang kurang daripada 128 aksara.", "SSE.Controllers.Main.textNeedSynchronize": "Anda mempunyai kemas kini", "SSE.Controllers.Main.textNo": "Tidak", "SSE.Controllers.Main.textNoLicenseTitle": "Had lesen telah di<PERSON>ai", "SSE.Controllers.Main.textPaidFeature": "<PERSON><PERSON>", "SSE.Controllers.Main.textPleaseWait": "Operasi ini mungkin mengambil lebih masa berbanding jangkaan. <PERSON><PERSON>, tunggu…", "SSE.Controllers.Main.textReconnect": "Sambungan dipulihkan", "SSE.Controllers.Main.textRemember": "Ingat pilihan saya untuk semua fail", "SSE.Controllers.Main.textRememberMacros": "<PERSON>gati pilihan saya untuk semua makro", "SSE.Controllers.Main.textRenameError": "<PERSON>a pengguna tidak boleh kosong.", "SSE.Controllers.Main.textRenameLabel": "<PERSON><PERSON><PERSON>n nama untuk digunakan bagi kerjasama", "SSE.Controllers.Main.textReplace": "Replace", "SSE.Controllers.Main.textRequestMacros": "Makro membuat permintaan ke URL. Adakah anda mahu membenarkan permintaan kepada %1?", "SSE.Controllers.Main.textShape": "Bentuk", "SSE.Controllers.Main.textStrict": "Mod tegas", "SSE.Controllers.Main.textText": "Teks", "SSE.Controllers.Main.textTryQuickPrint": "You have selected Quick print: the entire document will be printed on the last selected or default printer.<br>Do you want to continue?", "SSE.Controllers.Main.textTryUndoRedo": "Fungsi Buat asal/Buat semula dinyahdayakan bagi mod pengeditan bersama.<br><PERSON><PERSON> butang ‘Strict mode’ untuk menukar kepada mod pengeditan Bersama Tegas untuk mengedit fail tanpa gangguan pengguna lain dan menghantar perubahan anda hanya selepas anda menyimp<PERSON>ya. <PERSON>a boleh bertukar di antara mod-mod pengeditan Bersama mennguna<PERSON> seting Lanjutan editor.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Fungsi Buat asal/Buat semula dinyahdayakan bagi mod pengeditan Bersama <PERSON>.", "SSE.Controllers.Main.textUndo": "Undo", "SSE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "SSE.Controllers.Main.textUpdating": "Updating", "SSE.Controllers.Main.textYes": "Ya", "SSE.Controllers.Main.titleLicenseExp": "<PERSON><PERSON> tamat tempoh", "SSE.Controllers.Main.titleLicenseNotActive": "License not active", "SSE.Controllers.Main.titleServerVersion": "Editor di<PERSON><PERSON>i", "SSE.Controllers.Main.titleUpdateVersion": "Version changed", "SSE.Controllers.Main.txtAccent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtAll": "(<PERSON><PERSON><PERSON>)", "SSE.Controllers.Main.txtArt": "<PERSON><PERSON> anda di sini", "SSE.Controllers.Main.txtBasicShapes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtBlank": "(kosong)", "SSE.Controllers.Main.txtButtons": "<PERSON><PERSON>", "SSE.Controllers.Main.txtByField": "%1 dari %2", "SSE.Controllers.Main.txtCallouts": "<PERSON><PERSON>", "SSE.Controllers.Main.txtCharts": "Carta", "SSE.Controllers.Main.txtClearFilter": "<PERSON><PERSON>", "SSE.Controllers.Main.txtColLbls": "Label Lajur", "SSE.Controllers.Main.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtConfidential": "Sulit", "SSE.Controllers.Main.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtDays": "<PERSON>", "SSE.Controllers.Main.txtDiagramTitle": "Tajuk <PERSON>", "SSE.Controllers.Main.txtEditingMode": "Tetapkan mod pengeditan…", "SSE.Controllers.Main.txtErrorLoadHistory": "<PERSON><PERSON><PERSON><PERSON> sejarah gagal", "SSE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON>", "SSE.Controllers.Main.txtFile": "Fail", "SSE.Controllers.Main.txtGrandTotal": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtGroup": "Ku<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtHours": "Jam", "SSE.Controllers.Main.txtInfo": "Info", "SSE.Controllers.Main.txtLines": "<PERSON><PERSON>", "SSE.Controllers.Main.txtMath": "Matematik", "SSE.Controllers.Main.txtMinutes": "<PERSON><PERSON>", "SSE.Controllers.Main.txtMonths": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMultiSelect": "Berbilang-Pilih", "SSE.Controllers.Main.txtNone": "None", "SSE.Controllers.Main.txtOr": "%1 atau %2", "SSE.Controllers.Main.txtPage": "<PERSON><PERSON>", "SSE.Controllers.Main.txtPageOf": "Halaman %1 of %2", "SSE.Controllers.Main.txtPages": "<PERSON><PERSON>", "SSE.Controllers.Main.txtPicture": "Picture", "SSE.Controllers.Main.txtPivotTable": "PivotTable", "SSE.Controllers.Main.txtPreparedBy": "Disediakan o<PERSON>h", "SSE.Controllers.Main.txtPrintArea": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtQuarter": "<PERSON><PERSON>", "SSE.Controllers.Main.txtQuarters": "<PERSON><PERSON>", "SSE.Controllers.Main.txtRectangles": "Segi Empat Tepat", "SSE.Controllers.Main.txtRow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtRowLbls": "Label Baris", "SSE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "SSE.Controllers.Main.txtScheme_Aspect": "Aspect", "SSE.Controllers.Main.txtScheme_Blue": "Blue", "SSE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "SSE.Controllers.Main.txtScheme_Blue_II": "Blue II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "SSE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "SSE.Controllers.Main.txtScheme_Green": "Green", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "SSE.Controllers.Main.txtScheme_Marquee": "Marquee", "SSE.Controllers.Main.txtScheme_Median": "Median", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Orange", "SSE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "SSE.Controllers.Main.txtScheme_Paper": "Paper", "SSE.Controllers.Main.txtScheme_Red": "Red", "SSE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "SSE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "SSE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "SSE.Controllers.Main.txtScheme_Violet": "Violet", "SSE.Controllers.Main.txtScheme_Violet_II": "Violet II", "SSE.Controllers.Main.txtScheme_Yellow": "Yellow", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "SSE.Controllers.Main.txtSeconds": "Saat", "SSE.Controllers.Main.txtSeries": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Petak Bual Garis 1 (<PERSON><PERSON>)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Petak Bual Garis 2 (<PERSON><PERSON>)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Petak Bual Garis 3 (<PERSON><PERSON>)", "SSE.Controllers.Main.txtShape_accentCallout1": "Petak Bual Garis 1 (<PERSON>)", "SSE.Controllers.Main.txtShape_accentCallout2": "Petak Bual Garis 2 (<PERSON>)", "SSE.Controllers.Main.txtShape_accentCallout3": "Petak Bual Garis 3 (<PERSON>)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Kembali atau Butang Sebelumnya", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Butang Dokumen", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Butang Tam<PERSON>", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "<PERSON><PERSON><PERSON> atau <PERSON> Seterusnya", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Butang Bantu", "SSE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Butang Informasi", "SSE.Controllers.Main.txtShape_actionButtonMovie": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Butang <PERSON>", "SSE.Controllers.Main.txtShape_actionButtonSound": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_arc": "<PERSON>g<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentArrow": "Anak <PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5": "Penyambung Siku", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Penyambung Anak Panah Siku", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Penyambung Anak <PERSON>-<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentUpArrow": "Anak Panah Lengkung Ke Atas", "SSE.Controllers.Main.txtShape_bevel": "Serong", "SSE.Controllers.Main.txtShape_blockArc": "Lengkung Blok", "SSE.Controllers.Main.txtShape_borderCallout1": "Petak Bual Garis 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Petak Bual Garis 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Petak Bual Garis 3", "SSE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_callout1": "Petak Bual Garis 1 (Tiada Sempadan)", "SSE.Controllers.Main.txtShape_callout2": "Petak Bual Garis 2 (Tiada Sempadan)", "SSE.Controllers.Main.txtShape_callout3": "Petak Bual Garis 3 (Tiada Sempadan)", "SSE.Controllers.Main.txtShape_can": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_chevron": "Chevron", "SSE.Controllers.Main.txtShape_chord": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_circularArrow": "Anak <PERSON>", "SSE.Controllers.Main.txtShape_cloud": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cloudCallout": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Penyambung Anak Panah <PERSON>kung", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Penyambung Anak <PERSON>-Be<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Anak <PERSON> Melengkung Ke Bawah", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Anak <PERSON>", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Anak <PERSON> Kanan", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Anak <PERSON>ng Ke Atas", "SSE.Controllers.Main.txtShape_decagon": "Dekagon", "SSE.Controllers.Main.txtShape_diagStripe": "Belang Pepenjuru", "SSE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_dodecagon": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_donut": "Donat", "SSE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_downArrow": "<PERSON>k panah ke bawah", "SSE.Controllers.Main.txtShape_downArrowCallout": "Petak Bual Anak Panah Ke Bawah", "SSE.Controllers.Main.txtShape_ellipse": "Elips", "SSE.Controllers.Main.txtShape_ellipseRibbon": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Carta Aliran: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartCollate": "Carta Aliran: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartConnector": "Carta Aliran: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDecision": "Carta Aliran: Keputusan", "SSE.Controllers.Main.txtShape_flowChartDelay": "Carta Aliran: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Carta Aliran: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDocument": "Carta Aliran: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartExtract": "Carta Aliran: Ekstrak", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Carta Aliran: Data", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Carta Aliran: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Carta Aliran: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Carta Aliran: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Carta Aliran: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Carta Aliran: Input Manual", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Carta Aliran: Operasi Manual", "SSE.Controllers.Main.txtShape_flowChartMerge": "Carta Aliran: Cantum", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Carta Aliran: Multidokumen ", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Carta Aliran: Penyambung Luar-halaman", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Carta Aliran: Data Disimpan", "SSE.Controllers.Main.txtShape_flowChartOr": "Carta Aliran: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Carta Aliran: <PERSON><PERSON>rata<PERSON>", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Carta Aliran: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartProcess": "Carta Aliran: Proses", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Carta Aliran: Kad", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Carta Aliran: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartSort": "Carta Aliran: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Carta Aliran: Simpang Penambahan", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Carta Aliran: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_frame": "Rangka", "SSE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heart": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heptagon": "Heptagon", "SSE.Controllers.Main.txtShape_hexagon": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_homePlate": "Pentagon", "SSE.Controllers.Main.txtShape_horizontalScroll": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_irregularSeal1": "Ledakan 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Ledakan 2", "SSE.Controllers.Main.txtShape_leftArrow": "Anak panak kiri", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Petak Bual Anak <PERSON>", "SSE.Controllers.Main.txtShape_leftBrace": "Tanda Kurung Dakap <PERSON>", "SSE.Controllers.Main.txtShape_leftBracket": "Tanda Sempang Kiri", "SSE.Controllers.Main.txtShape_leftRightArrow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Petak Bual Anak <PERSON>", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftUpArrow": "Anak Panak ke Kiri At<PERSON>", "SSE.Controllers.Main.txtShape_lightningBolt": "Panahan Petir", "SSE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON>ah", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathDivide": "Bahagian", "SSE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Dar<PERSON>", "SSE.Controllers.Main.txtShape_mathNotEqual": "Tidak Sama", "SSE.Controllers.Main.txtShape_mathPlus": "Tambah", "SSE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_noSmoking": "Simbol \"Tiada\"", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Anak <PERSON> ke Kanan <PERSON>", "SSE.Controllers.Main.txtShape_octagon": "Oktagon", "SSE.Controllers.Main.txtShape_parallelogram": "Parallelogram", "SSE.Controllers.Main.txtShape_pentagon": "Pentagon", "SSE.Controllers.Main.txtShape_pie": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_plus": "Tambah", "SSE.Controllers.Main.txtShape_polyline1": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_polyline2": "Bentuk <PERSON>", "SSE.Controllers.Main.txtShape_quadArrow": "Anak <PERSON> Ganda Empat", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Petak Bual Anak Panah Ganda Empat", "SSE.Controllers.Main.txtShape_rect": "Segi Empat Tepat", "SSE.Controllers.Main.txtShape_ribbon": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ribbon2": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Petak Bual Anak <PERSON>", "SSE.Controllers.Main.txtShape_rightBrace": "Tanda Kurung <PERSON>", "SSE.Controllers.Main.txtShape_rightBracket": "Tanda Kurung Kanan", "SSE.Controllers.Main.txtShape_round1Rect": "Segi Empat Tepat Sudut Tunggal Bundar", "SSE.Controllers.Main.txtShape_round2DiagRect": "Segi Empat Tepat Sudut Pepenjuru Bundar", "SSE.Controllers.Main.txtShape_round2SameRect": "Segi Empat Tepat Sudut Sisi Sama Bundar", "SSE.Controllers.Main.txtShape_roundRect": "Segi Empat Tepat Sudut Bundar", "SSE.Controllers.Main.txtShape_rtTriangle": "Segi Tiga Kanan", "SSE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON> se<PERSON>um", "SSE.Controllers.Main.txtShape_snip1Rect": "Segi Empat Tepat Sudut Tunggal Digunting", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Segi Empat Tepat Sudut Pepenjuru Digunting", "SSE.Controllers.Main.txtShape_snip2SameRect": "Segi Empat Tepat Sudut Sisi Sama Digunting", "SSE.Controllers.Main.txtShape_snipRoundRect": "Segi Empat Tepat Sudut Tunggal Digunting dan <PERSON>", "SSE.Controllers.Main.txtShape_spline": "Melengkung", "SSE.Controllers.Main.txtShape_star10": "Bintang 10-<PERSON>", "SSE.Controllers.Main.txtShape_star12": "Bintang 12-<PERSON>", "SSE.Controllers.Main.txtShape_star16": "Bintang 16-<PERSON>", "SSE.Controllers.Main.txtShape_star24": "Bintang 24-<PERSON>", "SSE.Controllers.Main.txtShape_star32": "Bintang 32-<PERSON>", "SSE.Controllers.Main.txtShape_star4": "Bintang 4-<PERSON>", "SSE.Controllers.Main.txtShape_star5": "Bintang 5-<PERSON>", "SSE.Controllers.Main.txtShape_star6": "Bintang 6-<PERSON>", "SSE.Controllers.Main.txtShape_star7": "Bintang 7-<PERSON>", "SSE.Controllers.Main.txtShape_star8": "Bintang 8-<PERSON>", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Anak <PERSON> ke Kanan <PERSON>", "SSE.Controllers.Main.txtShape_sun": "Matahari", "SSE.Controllers.Main.txtShape_teardrop": "Titisan air mata", "SSE.Controllers.Main.txtShape_textRect": "Kotak Teks", "SSE.Controllers.Main.txtShape_trapezoid": "Trapezoid", "SSE.Controllers.Main.txtShape_triangle": "Segi tiga", "SSE.Controllers.Main.txtShape_upArrow": "Anak panah ke atas", "SSE.Controllers.Main.txtShape_upArrowCallout": "Petak Bual Anak Panah <PERSON> Atas", "SSE.Controllers.Main.txtShape_upDownArrow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_uturnArrow": "Anak <PERSON> Pusingan U", "SSE.Controllers.Main.txtShape_verticalScroll": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wave": "Gelombang", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Petak Bual Segi Empat Tepat", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Petak Bual Segi Empat Tepat Bulat", "SSE.Controllers.Main.txtSheet": "Sheet", "SSE.Controllers.Main.txtSlicer": "<PERSON>licer", "SSE.Controllers.Main.txtStarsRibbons": "Bintang & Reben", "SSE.Controllers.Main.txtStyle_Bad": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Calculation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Check_Cell": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Comma": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON> wang", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Good": "Bagus", "SSE.Controllers.Main.txtStyle_Heading_1": "Pengepala 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Pengepala 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Pengepala 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Pengepala 4", "SSE.Controllers.Main.txtStyle_Input": "Input", "SSE.Controllers.Main.txtStyle_Linked_Cell": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Neutral": "Neutral", "SSE.Controllers.Main.txtStyle_Normal": "Normal", "SSE.Controllers.Main.txtStyle_Note": "<PERSON>a", "SSE.Controllers.Main.txtStyle_Output": "Output", "SSE.Controllers.Main.txtStyle_Percent": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Title": "Tajuk", "SSE.Controllers.Main.txtStyle_Total": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Warning_Text": "<PERSON><PERSON>", "SSE.Controllers.Main.txtTab": "Tab", "SSE.Controllers.Main.txtTable": "Jadual", "SSE.Controllers.Main.txtTime": "<PERSON><PERSON>", "SSE.Controllers.Main.txtUnlock": "<PERSON><PERSON> kunci", "SSE.Controllers.Main.txtUnlockRange": "<PERSON><PERSON>", "SSE.Controllers.Main.txtUnlockRangeDescription": "<PERSON><PERSON>kkan kata laluan untuk mengubah julat ini:", "SSE.Controllers.Main.txtUnlockRangeWarning": "<PERSON>at yang anda sedang cuba untuk tukar adalah kata laluan yang dilindungi.", "SSE.Controllers.Main.txtValues": "<PERSON><PERSON>", "SSE.Controllers.Main.txtView": "View", "SSE.Controllers.Main.txtXAxis": "Paksi X", "SSE.Controllers.Main.txtYAxis": "Paksi X", "SSE.Controllers.Main.txtYears": "<PERSON><PERSON>", "SSE.Controllers.Main.unknownErrorText": "<PERSON><PERSON> t<PERSON>.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON><PERSON><PERSON> anda tidak disokong.", "SSE.Controllers.Main.uploadDocExtMessage": "Format dokumen tidak diketahui.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Tiada dokumen dimuat naik.", "SSE.Controllers.Main.uploadDocSizeMessage": "<PERSON>z Dokumen maksimum adalah terhad.", "SSE.Controllers.Main.uploadImageExtMessage": "Format imej yang tidak diketahui.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Tiada Imej dimuat naik.", "SSE.Controllers.Main.uploadImageSizeMessage": "<PERSON><PERSON><PERSON> terlalu besar. <PERSON><PERSON> maksimum adalah 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "<PERSON><PERSON><PERSON> dimuat naik…", "SSE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON><PERSON> dimuat naik", "SSE.Controllers.Main.waitText": "Sila, tunggu…", "SSE.Controllers.Main.warnBrowserIE9": "Aplikasi memiliki Ke<PERSON>an yang rendah pada IE9. Guna IE10 atau lebih tinggi", "SSE.Controllers.Main.warnBrowserZoom": "Seting zum semasa pelayar anda tidak disokong sepenuhnya. <PERSON><PERSON> set semula kepada zum lalai dengan menekan Ctrl+0.", "SSE.Controllers.Main.warnLicenseAnonymous": "Access denied for anonymous users.<br>This document will be opened for viewing only.", "SSE.Controllers.Main.warnLicenseBefore": "License not active.<br>Please contact your administrator.", "SSE.Controllers.Main.warnLicenseExceeded": "<PERSON><PERSON> <PERSON> had pengguna untuk sambungan serentak kepada editor %1. Do<PERSON>men ini akan dibuka untuk dilihat sahaja.<br>Hubung<PERSON> pentadbir anda untuk ketahui selanjutnya.", "SSE.Controllers.Main.warnLicenseExp": "Lesen anda telah tamat tempoh.<br><PERSON><PERSON> kemas kini lesen anda dan segarkan halaman.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "<PERSON>en tamat tempoh.<br><PERSON>a tidak mempunyai akses terhadap fungsi pengeditan dokumen.<br><PERSON><PERSON> hubungi pentadbir anda.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Lesen perlu untuk diperbaharui.<br>Anda mempunyai akses terhad kepada fungi pengeditan dokumen.<br><PERSON>la hubungi pentadbir anda untuk mendapatkan akses penuh", "SSE.Controllers.Main.warnLicenseUsersExceeded": "<PERSON><PERSON> te<PERSON> had pengguna untuk editor %1. <PERSON><PERSON><PERSON><PERSON> pentadbir anda untuk ketahui selanjutnya.", "SSE.Controllers.Main.warnNoLicense": "<PERSON><PERSON> had pengguna untuk sambungan serentak kepada editor %1. Dokumen ini akan dibuka untuk dilihat sahaja.<br>Hubungi pasukan jualan %1 untuk naik taraf terma peribadi.", "SSE.Controllers.Main.warnNoLicenseUsers": "<PERSON><PERSON> had pengguna untuk editor %1. Hubungi pasukan jualan %1 untuk naik taraf terma peribadi.", "SSE.Controllers.Main.warnProcessRightsChange": "<PERSON>a telah di<PERSON>lak hak untuk edit fail.", "SSE.Controllers.PivotTable.strSheet": "Sheet", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "No actions with calculated items are allowed for this active cell.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Calculated items do not work with custom subtotals.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "An item name cannot be found. Check that you've typed name correctly and the item is present in the PivotTable report.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Averages, standard deviations, and variances are not supported when a PivotTable report has calculated items.", "SSE.Controllers.Print.strAllSheets": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textFirstCol": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textFirstRow": "<PERSON><PERSON> pertama", "SSE.Controllers.Print.textFrozenCols": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textFrozenRows": "<PERSON><PERSON>", "SSE.Controllers.Print.textInvalidRange": "RALAT! Julat sel tidak sah", "SSE.Controllers.Print.textNoRepeat": "<PERSON><PERSON>", "SSE.Controllers.Print.textRepeat": "<PERSON><PERSON>…", "SSE.Controllers.Print.textSelectRange": "<PERSON><PERSON><PERSON> julat", "SSE.Controllers.Print.txtCustom": "Tersuai", "SSE.Controllers.Print.txtZoomToPage": "Zoom to page", "SSE.Controllers.Search.textInvalidRange": "RALAT! Julat sel tidak sah", "SSE.Controllers.Search.textNoTextFound": "Data yang andaa sedang cari tidak dijumpai. Sila laras pilihan carian.", "SSE.Controllers.Search.textReplaceSkipped": "<PERSON><PERSON>an telah dilakukan. {0} kejadian telah dilangkau.", "SSE.Controllers.Search.textReplaceSuccess": "<PERSON><PERSON> telah dilakukan. Kejadian {0} telah digantikan", "SSE.Controllers.Statusbar.errorLastSheet": "<PERSON><PERSON> kerja mestilah mempunyai sekurang-kurangnya satu lembaran kerja yang kelihatan.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Tidak boleh memadam lembaran kerja.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>Sambungan telah hilang</b><br>Sedang cuba untuk menyambung. Sila semak seting sambungan.", "SSE.Controllers.Statusbar.textSheetViewTip": "Anda berada dalam mod <PERSON><PERSON>. <PERSON><PERSON><PERSON> dan pengisihan hanya kelihatan kepada anda dan sesiapa yang masih berada dalam paparan ini.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Anda berada dalam mod <PERSON><PERSON>. <PERSON><PERSON><PERSON> hanya kelihatan kepada anda dan sesiapa yang masih berada dalam paparan ini.", "SSE.Controllers.Statusbar.warnDeleteSheet": "<PERSON><PERSON><PERSON> kerja yang dipilih mungkin mengandungi data. <PERSON><PERSON><PERSON> anda pasti mahu men<PERSON>kan?", "SSE.Controllers.Statusbar.zoomText": "Zum {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Fon yang anda akan simpan tidak tersedia pada peranti semasa.<br>Gaya teks akan dipaparkan menggunakan satu daripada fon system, fon yang disimpan akan digunakan apabila ia tersedia.<br><PERSON><PERSON><PERSON> anda mahu teruskan?", "SSE.Controllers.Toolbar.errorComboSeries": "Untuk mencipta kombinasi carta, pilih sekurang-kurangnya dua siri data.", "SSE.Controllers.Toolbar.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "RALAT! Bilangan maksimum siri data per carta ialah 255", "SSE.Controllers.Toolbar.errorStockChart": "Urutan baris tidak betul. Untuk membina carta stok, tempatkan data pada helaian mengikut urutan:<br> harga bukaan, harga mak, harga min, harga penutup.", "SSE.Controllers.Toolbar.helpCalcItems": "Work with calculated items in Pivot Tables.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Calculated items", "SSE.Controllers.Toolbar.helpFastUndo": "Easily undo changes while collaborating on sheets in Fast mode.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "\"Undo\" in real-time co-editing", "SSE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "SSE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textBracket": "<PERSON>da kurung", "SSE.Controllers.Toolbar.textDirectional": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textFontSizeErr": "<PERSON><PERSON> yang dimasukkan adalah tidak betul.<br><PERSON><PERSON> masukkan nilai berangka di antara 1 dan 409", "SSE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textIndicator": "Penunjuk", "SSE.Controllers.Toolbar.textInsert": "<PERSON>sip<PERSON>", "SSE.Controllers.Toolbar.textIntegral": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textLargeOperator": "Operator <PERSON>", "SSE.Controllers.Toolbar.textLimitAndLog": "Had dan logaritma", "SSE.Controllers.Toolbar.textLongOperation": "Operasi panjang", "SSE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.textOperator": "Operator", "SSE.Controllers.Toolbar.textPivot": "Jadual Pivot", "SSE.Controllers.Toolbar.textRadical": "Radikal", "SSE.Controllers.Toolbar.textRating": "Rating", "SSE.Controllers.Toolbar.textRecentlyUsed": "<PERSON><PERSON><PERSON><PERSON>-baru ini", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textShapes": "Bentuk", "SSE.Controllers.Toolbar.textSymbols": "Simbol", "SSE.Controllers.Toolbar.textWarning": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Anak panah kiri-kanan atas", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Anak panah arah kiri atas", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Anak panah arah kanan atas", "SSE.Controllers.Toolbar.txtAccent_Bar": "Bar", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Bar bawah", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Melepasi bar", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Formula berkotak (dengan ruang letak)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Formula berkotak (contoh)", "SSE.Controllers.Toolbar.txtAccent_Check": "Semak", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "<PERSON><PERSON><PERSON> bawah", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Pendakap atas", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vektor A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC Dengan Bar Atas", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y dengan bar atas", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Bintik Ganda Tiga", "SSE.Controllers.Toolbar.txtAccent_DDot": "Titik berganda", "SSE.Controllers.Toolbar.txtAccent_Dot": "Titik", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Bar atas berkembar", "SSE.Controllers.Toolbar.txtAccent_Grave": "Grava", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Kumpulkan aksara di bawah", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Kumpulkan aksara di atas", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Tempuling arah kiri atas", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Tempulin<PERSON> <PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Hat": "<PERSON>i", "SSE.Controllers.Toolbar.txtAccent_Smile": "Breve", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON>da kurung", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "<PERSON>da kurung dengan pemisah", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "<PERSON>da kurung dengan pemisah", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_Curve": "<PERSON>da kurung", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "<PERSON>da kurung dengan pemisah", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON> (dua keadaan)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON> (tiga keadaan)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON><PERSON> kes", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "<PERSON><PERSON>li Binomial", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "<PERSON><PERSON>li Binomial", "SSE.Controllers.Toolbar.txtBracket_Line": "<PERSON>da kurung", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON>da kurung", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON>da kurung", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON>da kurung", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "<PERSON>da kurung dengan pemisah", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_Square": "<PERSON>da kurung", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON>da kurung", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON>da kurung", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON>da kurung", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON>da kurung", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON>da kurung", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Tanda kurung tunggal", "SSE.Controllers.Toolbar.txtDeleteCells": "Padam Sel", "SSE.Controllers.Toolbar.txtExpand": "Kembangkan dan sisih", "SSE.Controllers.Toolbar.txtExpandSort": "Data di sebelah pilihan tidak akan diisih. <PERSON><PERSON><PERSON> anda mahu meluaskan pilihan untuk sertakan data bertentangan atau teruskan sel pilihan sahaja?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Pembezaan", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Pembezaan", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Pembezaan", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Pembezaan", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Pecahan Linear", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi melebihi 2", "SSE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON>han kecil", "SSE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "<PERSON>gsi kosinus <PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Fungsi kosinus hiperbola songsang", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "<PERSON>gsi kotangen songsang", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Fungsi kotangen hiperbola songsang", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "<PERSON><PERSON>i kosekan songsang", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Fungsi kosekan hiperbola songsang", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "<PERSON><PERSON><PERSON> sekan <PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Fungsi sekan hiperbola songsang", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Fungsi sinus songsang", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Fungsi sinus hiperbola songsang", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "<PERSON><PERSON>i tangen songsang", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Fungsi tangen hiperbola songsang", "SSE.Controllers.Toolbar.txtFunction_Cos": "Fungsi kosinus", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Fungsi kosinus hiperbola", "SSE.Controllers.Toolbar.txtFunction_Cot": "<PERSON><PERSON>i kotangen", "SSE.Controllers.Toolbar.txtFunction_Coth": "Fungsi kotangen hiperbola", "SSE.Controllers.Toolbar.txtFunction_Csc": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Csch": "Fungsi kosekan hiperbola", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Theta sin", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Kos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Formula Tangen", "SSE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON> sekan", "SSE.Controllers.Toolbar.txtFunction_Sech": "Fungsi sekan hiperbola", "SSE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Fungsi sinus hiperbola", "SSE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON>i tangen", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Fungsi tangen hiperbola", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Custom", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Data and model", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Good, bad and neutral", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "No name", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Number format", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Themed cell styles", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Titles and headings", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Custom", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Dark", "SSE.Controllers.Toolbar.txtGroupTable_Light": "Light", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Medium", "SSE.Controllers.Toolbar.txtInsertCells": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegral": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Pembezaan theta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Pembezaan x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Pembezaan y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralDouble": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOriented": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralTriple": "Ka<PERSON>an Ganda tiga", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Ka<PERSON>an Ganda tiga", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Ka<PERSON>an Ganda tiga", "SSE.Controllers.Toolbar.txtInvalidRange": "RALAT! Julat sel tidak sah", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Produk-sampingan", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Produk-sampingan", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Produk-sampingan", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Produk-sampingan", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Produk-sampingan", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produk", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Kesatuan", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Produk", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produk", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produk", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produk", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produk", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Kesatuan", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Kesatuan", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Kesatuan", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Kesatuan", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Kesatuan", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON><PERSON><PERSON> had", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Had", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logarit<PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarit<PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "SSE.Controllers.Toolbar.txtLockSort": "Data dijumpai selepas pilihan anda, tetapi anda tidak mempunyai keizinan yang mencukupi untuk mengubah sel tersebut.<br><PERSON><PERSON><PERSON> anda mahu teruskan dengan pilihan semasa?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 matriks kosong", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 matriks kosong", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 matriks kosong", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 matriks kosong", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON><PERSON> kosong dengan tanda kurung", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON><PERSON><PERSON> kosong dengan tanda kurung", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON><PERSON><PERSON> kosong dengan tanda kurung", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON><PERSON> kosong dengan tanda kurung", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 matriks kosong", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 matriks kosong", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 matriks kosong", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 matriks kosong", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON><PERSON> garis asas", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Titik pepenjuru", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Bintik menegak", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 matriks identiti", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 matriks identiti", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 matriks identiti", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 matriks identiti", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "<PERSON>k panah kiri-kanan bawah", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Anak panah kiri-kanan atas", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Anak panah arah kiri bawah", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Anak panah arah kiri atas", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Anak panah arah kanan atas", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Sama titik bertindih", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Yield", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta yield", "SSE.Controllers.Toolbar.txtOperator_Definition": "<PERSON><PERSON> dengan mengikut definisi", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta sama dengan", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "<PERSON>k panah kiri-kanan bawah", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Anak panah kiri-kanan atas", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Anak panah arah kiri bawah", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Anak panah arah kiri atas", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Anak panah arah kanan atas", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON>a sama", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON> sama", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Tambah sama", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Radikal", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Radikal", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON> kuasa dua dengan darjah", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON>asa dua", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Radikal dengan darjah", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON> kuasa dua", "SSE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "Subskrip", "SSE.Controllers.Toolbar.txtScriptSubSup": "Subskrip-superskrip", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "<PERSON><PERSON>-Superskrip", "SSE.Controllers.Toolbar.txtScriptSup": "Superskrip", "SSE.Controllers.Toolbar.txtSorting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSortSelected": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_about": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_additional": "Komplimen", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON><PERSON> sama kepada", "SSE.Controllers.Toolbar.txtSymbol_ast": "Operator asterik", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Bulet operator", "SSE.Controllers.Toolbar.txtSymbol_cap": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Punca Kuasa Tiga", "SSE.Controllers.Toolbar.txtSymbol_cdots": "<PERSON><PERSON> mendatar perten<PERSON> baris", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "Ci", "SSE.Controllers.Toolbar.txtSymbol_cong": "Anggaran sama kepada", "SSE.Controllers.Toolbar.txtSymbol_cup": "Kesatuan", "SSE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_degree": "Darjah", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON> bah<PERSON>an", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON>k panah ke bawah", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Set kosong", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_equiv": "<PERSON><PERSON><PERSON> kepada", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "Di sana wujud", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Pemfaktoran", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Darjah Fahrenheit", "SSE.Controllers.Toolbar.txtSymbol_forall": "Untuk semua", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON><PERSON> besar dari atau sama dengan", "SSE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON><PERSON> besar da<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON> besar dari", "SSE.Controllers.Toolbar.txtSymbol_in": "Elemen bagi", "SSE.Controllers.Toolbar.txtSymbol_inc": "Kenaikan", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Infiniti", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Anak panak kiri", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Anak panah ke kiri-kanan", "SSE.Controllers.Toolbar.txtSymbol_leq": "<PERSON><PERSON> da<PERSON>ada atau sama dengan", "SSE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON><PERSON> kurang da<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "Tolak Tambah", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Tidak sama kepada", "SSE.Controllers.Toolbar.txtSymbol_ni": "Dikandung sebagai ahli", "SSE.Controllers.Toolbar.txtSymbol_not": "<PERSON><PERSON><PERSON> petanda", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Di sana tidak wujud", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omikron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Perbezaan separa", "SSE.Controllers.Toolbar.txtSymbol_percent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "Tambah", "SSE.Controllers.Toolbar.txtSymbol_pm": "Tambah Tolak", "SSE.Controllers.Toolbar.txtSymbol_propto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Punca kuasa empat", "SSE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON> bukti", "SSE.Controllers.Toolbar.txtSymbol_rddots": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_rho": "Ro", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Tanda Radikal", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON> itu", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Anak panah ke atas", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Varian Epsilon", "SSE.Controllers.Toolbar.txtSymbol_varphi": "V<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Varian <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Varian theta", "SSE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.warnLongOperation": "<PERSON>si yang anda akan lakukan mungkin mengambil lebih masa untuk lengkap.<br><PERSON><PERSON><PERSON> anda pasti mahu teruskan?", "SSE.Controllers.Toolbar.warnMergeLostData": "Hanya data dari sel kiri-atas akan kekal dalam sel dicantum. <br><PERSON><PERSON><PERSON> anda pasti mahu men<PERSON>?", "SSE.Controllers.Toolbar.warnNoRecommended": "To create a chart, select the cells that contain the data you'd like to use.<br>If you have names for the rows and columns and you'd like use them as labels, include them in your selection.", "SSE.Controllers.Viewport.textFreezePanes": "Bekukan Anak <PERSON>", "SSE.Controllers.Viewport.textFreezePanesShadow": "Tunjuk Anak Tetingkap Bayang Dibekukan", "SSE.Controllers.Viewport.textHideFBar": "Sembunyikan Bar Formula", "SSE.Controllers.Viewport.textHideGridlines": "Sembunyikan Garis Grid", "SSE.Controllers.Viewport.textHideHeadings": "Sembunyikan Pengepala", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Seting digunakan untuk mengenalpasti data berangka", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Pelayak teks", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Seting <PERSON>", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(tiada)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Penap<PERSON>", "SSE.Views.AutoFilterDialog.textAddSelection": "Tambah pilihan semasa pada tapisan", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Kosong}", "SSE.Views.AutoFilterDialog.textSelectAll": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.textSelectAllResults": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.textWarning": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtAboveAve": "Atas purata", "SSE.Views.AutoFilterDialog.txtAfter": "After...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "All dates in the period", "SSE.Views.AutoFilterDialog.txtApril": "April", "SSE.Views.AutoFilterDialog.txtAugust": "August", "SSE.Views.AutoFilterDialog.txtBefore": "Before...", "SSE.Views.AutoFilterDialog.txtBegins": "<PERSON><PERSON><PERSON> den<PERSON>…", "SSE.Views.AutoFilterDialog.txtBelowAve": "<PERSON> b<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtBetween": "Di antara…", "SSE.Views.AutoFilterDialog.txtClear": "Kosongkan", "SSE.Views.AutoFilterDialog.txtContains": "Mengandung<PERSON>…", "SSE.Views.AutoFilterDialog.txtDateFilter": "Date filter", "SSE.Views.AutoFilterDialog.txtDecember": "December", "SSE.Views.AutoFilterDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON> penapis sel", "SSE.Views.AutoFilterDialog.txtEnds": "<PERSON><PERSON><PERSON>…", "SSE.Views.AutoFilterDialog.txtEquals": "<PERSON><PERSON> den<PERSON>…", "SSE.Views.AutoFilterDialog.txtFebruary": "February", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Pen<PERSON><PERSON> mengikut warna sel", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Penapis mengikut warna fon", "SSE.Views.AutoFilterDialog.txtGreater": "<PERSON><PERSON><PERSON> besar dari…", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "<PERSON><PERSON>h besar dari atau sama dengan…", "SSE.Views.AutoFilterDialog.txtJanuary": "January", "SSE.Views.AutoFilterDialog.txtJuly": "July", "SSE.Views.AutoFilterDialog.txtJune": "June", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Tapisan label", "SSE.Views.AutoFilterDialog.txtLastMonth": "Last month", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Last quarter", "SSE.Views.AutoFilterDialog.txtLastWeek": "Last week", "SSE.Views.AutoFilterDialog.txtLastYear": "Last year", "SSE.Views.AutoFilterDialog.txtLess": "<PERSON><PERSON>…", "SSE.Views.AutoFilterDialog.txtLessEquals": "<PERSON><PERSON> da<PERSON>ada atau sama dengan…", "SSE.Views.AutoFilterDialog.txtMarch": "March", "SSE.Views.AutoFilterDialog.txtMay": "May", "SSE.Views.AutoFilterDialog.txtNextMonth": "Next month", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Next quarter", "SSE.Views.AutoFilterDialog.txtNextWeek": "Next week", "SSE.Views.AutoFilterDialog.txtNextYear": "Next year", "SSE.Views.AutoFilterDialog.txtNotBegins": "Tidak bermula dengan…", "SSE.Views.AutoFilterDialog.txtNotBetween": "<PERSON><PERSON><PERSON> di antara…", "SSE.Views.AutoFilterDialog.txtNotContains": "Tidak mengandungi…", "SSE.Views.AutoFilterDialog.txtNotEnds": "<PERSON><PERSON>k be<PERSON> dengan…", "SSE.Views.AutoFilterDialog.txtNotEquals": "Tidak sama…", "SSE.Views.AutoFilterDialog.txtNovember": "November", "SSE.Views.AutoFilterDialog.txtNumFilter": "Tapisan nombor", "SSE.Views.AutoFilterDialog.txtOctober": "October", "SSE.Views.AutoFilterDialog.txtQuarter1": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Quarter 1", "SSE.Views.AutoFilterDialog.txtReapply": "<PERSON><PERSON> semula", "SSE.Views.AutoFilterDialog.txtSeptember": "September", "SSE.Views.AutoFilterDialog.txtSortCellColor": "<PERSON><PERSON> mengikut warna sel", "SSE.Views.AutoFilterDialog.txtSortFontColor": "<PERSON><PERSON> mengikut warna fon", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "<PERSON><PERSON>rendah", "SSE.Views.AutoFilterDialog.txtSortLow2High": "<PERSON><PERSON>.", "SSE.Views.AutoFilterDialog.txtSortOption": "<PERSON><PERSON><PERSON> isih <PERSON>", "SSE.Views.AutoFilterDialog.txtTextFilter": "Penapis <PERSON>", "SSE.Views.AutoFilterDialog.txtThisMonth": "This Month", "SSE.Views.AutoFilterDialog.txtThisQuarter": "This quarter", "SSE.Views.AutoFilterDialog.txtThisWeek": "This week", "SSE.Views.AutoFilterDialog.txtThisYear": "This Year", "SSE.Views.AutoFilterDialog.txtTitle": "Penap<PERSON>", "SSE.Views.AutoFilterDialog.txtToday": "Today", "SSE.Views.AutoFilterDialog.txtTomorrow": "Tomorrow", "SSE.Views.AutoFilterDialog.txtTop10": "10 Teratas", "SSE.Views.AutoFilterDialog.txtValueFilter": "<PERSON><PERSON><PERSON> ni<PERSON>", "SSE.Views.AutoFilterDialog.txtYearToDate": "Year to date", "SSE.Views.AutoFilterDialog.txtYesterday": "Yesterday", "SSE.Views.AutoFilterDialog.warnFilterError": "Anda perlu sekurang-kurangnya satu medan dalam Kawasan Ni<PERSON> untuk anda menggunakan tapisan nilai.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Anda perlu memilih sekurang-kurangnya satu nilai", "SSE.Views.CellEditor.textManager": "<PERSON><PERSON>", "SSE.Views.CellEditor.tipFormula": "<PERSON>si<PERSON><PERSON> fungsi", "SSE.Views.CellRangeDialog.errorMaxRows": "RALAT! Bilangan maksimum siri data per carta ialah 255", "SSE.Views.CellRangeDialog.errorStockChart": "Urutan baris tidak betul. Untuk membina carta stok, tempatkan data pada helaian mengikut urutan:<br> harga bukaan, harga mak, harga min, harga penutup.", "SSE.Views.CellRangeDialog.txtEmpty": "Medan ini diperlukan", "SSE.Views.CellRangeDialog.txtInvalidRange": "RALAT! Julat sel tidak sah", "SSE.Views.CellRangeDialog.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.strShrink": "<PERSON><PERSON><PERSON><PERSON> untuk muat", "SSE.Views.CellSettings.strWrap": "Balut teks", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "<PERSON><PERSON> latar belakang", "SSE.Views.CellSettings.textBackground": "<PERSON><PERSON> latar belakang", "SSE.Views.CellSettings.textBorderColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "<PERSON><PERSON>", "SSE.Views.CellSettings.textClearRule": "<PERSON><PERSON>", "SSE.Views.CellSettings.textColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textColorScales": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textCondFormat": "Pemformatan bersyarat", "SSE.Views.CellSettings.textControl": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textDataBars": "Bar Data", "SSE.Views.CellSettings.textDirection": "<PERSON><PERSON>", "SSE.Views.CellSettings.textFill": "<PERSON><PERSON>", "SSE.Views.CellSettings.textForeground": "<PERSON><PERSON> la<PERSON> depan", "SSE.Views.CellSettings.textGradient": "<PERSON> gradien", "SSE.Views.CellSettings.textGradientColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "<PERSON><PERSON>", "SSE.Views.CellSettings.textIndent": "Inden", "SSE.Views.CellSettings.textItems": "<PERSON><PERSON>", "SSE.Views.CellSettings.textLinear": "Linear", "SSE.Views.CellSettings.textManageRule": "Uruskan Peraturan", "SSE.Views.CellSettings.textNewRule": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textNoFill": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textOrientation": "Orientasi Teks", "SSE.Views.CellSettings.textPattern": "Pola", "SSE.Views.CellSettings.textPatternFill": "Pola", "SSE.Views.CellSettings.textPosition": "Kedudukan", "SSE.Views.CellSettings.textRadial": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textSelectBorders": "<PERSON><PERSON><PERSON> sempadan yang anda mahu tukar menggunakan gaya yang dipilih di bawah", "SSE.Views.CellSettings.textSelection": "<PERSON><PERSON> p<PERSON>han se<PERSON>a", "SSE.Views.CellSettings.textThisPivot": "Dari pivot ini", "SSE.Views.CellSettings.textThisSheet": "<PERSON><PERSON> lembaran kerja ini", "SSE.Views.CellSettings.textThisTable": "<PERSON><PERSON> jadual ini", "SSE.Views.CellSettings.tipAddGradientPoint": "Tambah mata gradien", "SSE.Views.CellSettings.tipAll": "Tetapkan sempadan luaran dan semua sel dalaman", "SSE.Views.CellSettings.tipBottom": "Tetapkan sempadan luaran", "SSE.Views.CellSettings.tipDiagD": "Tetapkan Sempadan Pepenjuru Bawah", "SSE.Views.CellSettings.tipDiagU": "Tetapkan Sempadan Pepenjuru Atas", "SSE.Views.CellSettings.tipInner": "Tetapkan garis dalaman sahaja", "SSE.Views.CellSettings.tipInnerHor": "Tetapkan garis dalaman melintang sahaja", "SSE.Views.CellSettings.tipInnerVert": "Tetapkan garis menegak dan melintang sahaja", "SSE.Views.CellSettings.tipLeft": "Tetapkan sempadan luaran kiri sahaja", "SSE.Views.CellSettings.tipNone": "Tetapkan tiada sempadan", "SSE.Views.CellSettings.tipOuter": "Tetapkan sempadan luaran sahaja", "SSE.Views.CellSettings.tipRemoveGradientPoint": "<PERSON><PERSON> keluar mata gradien", "SSE.Views.CellSettings.tipRight": "Tetapkan sempadan luaran kanan sahaja", "SSE.Views.CellSettings.tipTop": "Tetapkan sempadan luaran atas sahaja", "SSE.Views.ChartDataDialog.errorInFormula": "Terdapat ralat dalam formula yang anda masukkan.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Rujukan tidak sah. Rujukan mestilah lembaran kerja terbuka.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Bilangan maksimum mata dalam siri per carta ialah 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Bilangan maksimum siri data per carta ialah 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Rujukan tidak sah. Rujukan bagi tajuk, nilai, saiz atau label data mestilah sel, baris atau lajur tunggal.", "SSE.Views.ChartDataDialog.errorNoValues": "<PERSON><PERSON>k mencipta carta, siri mestilah mengandungi sekurang-kurangnya satu nilai.", "SSE.Views.ChartDataDialog.errorStockChart": "Urutan baris tidak betul. Untuk membina carta stok, tempatkan data pada helaian mengikut urutan:<br> harga bukaan, harga mak, harga min, harga penutup.", "SSE.Views.ChartDataDialog.textAdd": "Tambah", "SSE.Views.ChartDataDialog.textCategory": "Label <PERSON><PERSON> (Kategori)", "SSE.Views.ChartDataDialog.textData": "Julat data carta", "SSE.Views.ChartDataDialog.textDelete": "<PERSON><PERSON> k<PERSON>", "SSE.Views.ChartDataDialog.textDown": "<PERSON>", "SSE.Views.ChartDataDialog.textEdit": "Edit", "SSE.Views.ChartDataDialog.textInvalidRange": "Julat sel tidak sah", "SSE.Views.ChartDataDialog.textSelectData": "Pilih data", "SSE.Views.ChartDataDialog.textSeries": "<PERSON><PERSON> (Siri)", "SSE.Views.ChartDataDialog.textSwitch": "<PERSON><PERSON>/Lajur", "SSE.Views.ChartDataDialog.textTitle": "Data Carta", "SSE.Views.ChartDataDialog.textUp": "<PERSON>", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Terdapat ralat dalam formula yang anda masukkan.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Rujukan tidak sah. Rujukan mestilah lembaran kerja terbuka.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Bilangan maksimum mata dalam siri per carta ialah 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Bilangan maksimum siri data per carta ialah 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Rujukan tidak sah. Rujukan bagi tajuk, nilai, saiz atau label data mestilah sel, baris atau lajur tunggal.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "<PERSON><PERSON>k mencipta carta, siri mestilah mengandungi sekurang-kurangnya satu nilai.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Urutan baris tidak betul. Untuk membina carta stok, tempatkan data pada helaian mengikut urutan:<br> harga bukaan, harga mak, harga min, harga penutup.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Julat sel tidak sah", "SSE.Views.ChartDataRangeDialog.textSelectData": "Pilih data", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Julat label paksi", "SSE.Views.ChartDataRangeDialog.txtChoose": "<PERSON><PERSON><PERSON> julat", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "<PERSON>a siri", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Label Paksi", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "<PERSON> <PERSON>", "SSE.Views.ChartDataRangeDialog.txtValues": "<PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtXValues": "<PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtYValues": "<PERSON><PERSON>", "SSE.Views.ChartSettings.errorMaxRows": "Bilangan maksimum siri data per carta ialah 255.", "SSE.Views.ChartSettings.strLineWeight": "<PERSON><PERSON>", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "Templat", "SSE.Views.ChartSettings.text3dDepth": "Depth (% of base)", "SSE.Views.ChartSettings.text3dHeight": "Height (% of base)", "SSE.Views.ChartSettings.text3dRotation": "3D Rotation", "SSE.Views.ChartSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> seting lanju<PERSON>", "SSE.Views.ChartSettings.textAutoscale": "Autoscale", "SSE.Views.ChartSettings.textBorderSizeErr": "<PERSON><PERSON> yang dimasukkan adalah tidak betul.<br><PERSON><PERSON> masukkan nilai di antara 0 pt dan 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "<PERSON><PERSON> jenis", "SSE.Views.ChartSettings.textChartType": "U<PERSON>", "SSE.Views.ChartSettings.textDefault": "Default Rotation", "SSE.Views.ChartSettings.textDown": "Down", "SSE.Views.ChartSettings.textEditData": "Edit Data dan <PERSON>", "SSE.Views.ChartSettings.textFirstPoint": "<PERSON>", "SSE.Views.ChartSettings.textHeight": "Ketinggian", "SSE.Views.ChartSettings.textHighPoint": "<PERSON>", "SSE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON> pemalar", "SSE.Views.ChartSettings.textLastPoint": "<PERSON>", "SSE.Views.ChartSettings.textLeft": "Left", "SSE.Views.ChartSettings.textLowPoint": "Mata <PERSON>", "SSE.Views.ChartSettings.textMarkers": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textNarrow": "Narrow field of view", "SSE.Views.ChartSettings.textNegativePoint": "<PERSON>", "SSE.Views.ChartSettings.textPerspective": "Perspective", "SSE.Views.ChartSettings.textRanges": "Julat Tarikh", "SSE.Views.ChartSettings.textRight": "Right", "SSE.Views.ChartSettings.textRightAngle": "Right angle axes", "SSE.Views.ChartSettings.textSelectData": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textShow": "Tunjukkan", "SSE.Views.ChartSettings.textSize": "Saiz", "SSE.Views.ChartSettings.textStyle": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textSwitch": "<PERSON><PERSON>/Lajur", "SSE.Views.ChartSettings.textType": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textUp": "Up", "SSE.Views.ChartSettings.textWiden": "Widen field of view", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textX": "X rotation", "SSE.Views.ChartSettings.textY": "Y rotation", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "RALAT! Bilangan maksimum mata dalam siri per carta ialah 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "RALAT! Bilangan maksimum siri data per carta ialah 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Urutan baris tidak betul. Untuk membina carta stok, tempatkan data pada helaian mengikut urutan:<br> harga bukaan, harga mak, harga min, harga penutup.", "SSE.Views.ChartSettingsDlg.textAbsolute": "<PERSON><PERSON> alih atau saiz dengan sel", "SSE.Views.ChartSettingsDlg.textAlt": "Teks Berselang", "SSE.Views.ChartSettingsDlg.textAltDescription": "Perihalan", "SSE.Views.ChartSettingsDlg.textAltTip": "Perwakilan berasaskan teks alternatif bagi maklumat objek visual, yang akan dibacakan kepada orang yang mengalami masalah penglihatan atau kognitif untuk membantu mereka memahami dengan lebih baik maklumat yang terdapat dalam imej, bentuk auto, carta atau jadual.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Tajuk", "SSE.Views.ChartSettingsDlg.textAuto": "Auto", "SSE.Views.ChartSettingsDlg.textAutoEach": "Auto untuk Setiap", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisPos": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisSettings": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Tajuk", "SSE.Views.ChartSettingsDlg.textBase": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "<PERSON>ara <PERSON>", "SSE.Views.ChartSettingsDlg.textBillions": "Bilion", "SSE.Views.ChartSettingsDlg.textBottom": "<PERSON>wa<PERSON>", "SSE.Views.ChartSettingsDlg.textCategoryName": "<PERSON><PERSON> ka<PERSON>i", "SSE.Views.ChartSettingsDlg.textCenter": "Pusat", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Element Carta &<br>Pet<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textChartTitle": "Tajuk <PERSON>", "SSE.Views.ChartSettingsDlg.textCross": "Silang", "SSE.Views.ChartSettingsDlg.textCustom": "Tersuai", "SSE.Views.ChartSettingsDlg.textDataColumns": "da<PERSON> la<PERSON>r", "SSE.Views.ChartSettingsDlg.textDataLabels": "Label Data", "SSE.Views.ChartSettingsDlg.textDataRows": "dalam baris", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Petun<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textEmptyCells": "<PERSON><PERSON> dan <PERSON>", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Sambung mata data dengan garis", "SSE.Views.ChartSettingsDlg.textFit": "Muat kepada <PERSON>", "SSE.Views.ChartSettingsDlg.textFixed": "Tetapkan", "SSE.Views.ChartSettingsDlg.textFormat": "Format label", "SSE.Views.ChartSettingsDlg.textGaps": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGridLines": "Garis grid", "SSE.Views.ChartSettingsDlg.textGroup": "Kumpulan Sparkline", "SSE.Views.ChartSettingsDlg.textHide": "Sembunyikan", "SSE.Views.ChartSettingsDlg.textHideAxis": "Sembunyikan paksi", "SSE.Views.ChartSettingsDlg.textHigh": "Tingg<PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxis": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorizontal": "Melintang", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Ratusan", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textInnerBottom": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textInnerTop": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textInvalidRange": "RALAT! Julat sel tidak sah", "SSE.Views.ChartSettingsDlg.textLabelDist": "Julat Label Paksi", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Selang antara Label ", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Pilihan Label", "SSE.Views.ChartSettingsDlg.textLabelPos": "Kedudukan Label", "SSE.Views.ChartSettingsDlg.textLayout": "Talaletak", "SSE.Views.ChartSettingsDlg.textLeft": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendBottom": "<PERSON>wa<PERSON>", "SSE.Views.ChartSettingsDlg.textLegendLeft": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendPos": "Petunjuk", "SSE.Views.ChartSettingsDlg.textLegendRight": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendTop": "Atas", "SSE.Views.ChartSettingsDlg.textLines": "<PERSON><PERSON> ", "SSE.Views.ChartSettingsDlg.textLocationRange": "Julat Lokasi", "SSE.Views.ChartSettingsDlg.textLogScale": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLow": "Rendah", "SSE.Views.ChartSettingsDlg.textMajor": "Major", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Major dan <PERSON>", "SSE.Views.ChartSettingsDlg.textMajorType": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textManual": "Manual", "SSE.Views.ChartSettingsDlg.textMarkers": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Selang antara Tanda", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "Minor", "SSE.Views.ChartSettingsDlg.textMinorType": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinValue": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNextToAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNone": "Tiada", "SSE.Views.ChartSettingsDlg.textNoOverlay": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textOneCell": "<PERSON><PERSON> tetapi jangan saizkan dengan sel", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "<PERSON><PERSON> Tan<PERSON> Pem<PERSON>", "SSE.Views.ChartSettingsDlg.textOut": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textOuterTop": "Atas Luar", "SSE.Views.ChartSettingsDlg.textOverlay": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textReverse": "<PERSON><PERSON> dalam urutan <PERSON>", "SSE.Views.ChartSettingsDlg.textReverseOrder": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRight": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRightOverlay": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRotated": "Diputarkan", "SSE.Views.ChartSettingsDlg.textSameAll": "<PERSON>a untuk Semu<PERSON>", "SSE.Views.ChartSettingsDlg.textSelectData": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSeparator": "Pemisah Label Data", "SSE.Views.ChartSettingsDlg.textSeriesName": "<PERSON>a siri", "SSE.Views.ChartSettingsDlg.textShow": "Tunjukkan", "SSE.Views.ChartSettingsDlg.textShowBorders": "Sempadan carta dipaparkan", "SSE.Views.ChartSettingsDlg.textShowData": "Tunjukkan data dalam baris dan lajur tersembunyi", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Tunjukkan sel kosong sebagai", "SSE.Views.ChartSettingsDlg.textShowEquation": "Display equation on chart", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowValues": "<PERSON>lai carta dipaparkan", "SSE.Views.ChartSettingsDlg.textSingle": "Sparkline Tunggal", "SSE.Views.ChartSettingsDlg.textSmooth": "Licin", "SSE.Views.ChartSettingsDlg.textSnap": "Menyentap <PERSON>", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Julat Sparkline", "SSE.Views.ChartSettingsDlg.textStraight": "Te<PERSON>", "SSE.Views.ChartSettingsDlg.textStyle": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Ribu", "SSE.Views.ChartSettingsDlg.textTickOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTitle": "Carta – Seting Lanjutan", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Sparkline – Seting <PERSON>", "SSE.Views.ChartSettingsDlg.textTop": "Atas", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Trendline options", "SSE.Views.ChartSettingsDlg.textTrillions": "Trilion", "SSE.Views.ChartSettingsDlg.textTwoCell": "<PERSON><PERSON> dan saizkan dengan sel", "SSE.Views.ChartSettingsDlg.textType": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTypeData": "Jenis & Data", "SSE.Views.ChartSettingsDlg.textUnits": "Paparkan Unit", "SSE.Views.ChartSettingsDlg.textValue": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxis": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Tajuk Paksi X", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Tajuk <PERSON>", "SSE.Views.ChartSettingsDlg.textZero": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.txtEmpty": "Medan ini diperlukan", "SSE.Views.ChartTypeDialog.errorComboSeries": "Untuk mencipta kombinasi carta, pilih sekurang-kurangnya dua siri data.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Jenis carta yang dipilih memerlukan paksi sekunder yang carta sedia ada sedang gunakan. Pilih jenis carta yang lain.", "SSE.Views.ChartTypeDialog.textSecondary": "<PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textSeries": "<PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textStyle": "<PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textTitle": "<PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textType": "<PERSON><PERSON>", "SSE.Views.ChartWizardDialog.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartWizardDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order: opening price, max price, min price, closing price.", "SSE.Views.ChartWizardDialog.textRecommended": "Recommended", "SSE.Views.ChartWizardDialog.textSecondary": "Secondary Axis", "SSE.Views.ChartWizardDialog.textSeries": "Series", "SSE.Views.ChartWizardDialog.textTitle": "Insert Chart", "SSE.Views.ChartWizardDialog.textTitleChange": "Change chart type", "SSE.Views.ChartWizardDialog.textType": "Type", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Choose the chart type and axis for your data series", "SSE.Views.CreatePivotDialog.textDataRange": "Julat sumber data", "SSE.Views.CreatePivotDialog.textDestination": "<PERSON><PERSON><PERSON> di mana untuk letak jadual", "SSE.Views.CreatePivotDialog.textExist": "<PERSON><PERSON><PERSON> kerja sedia ada", "SSE.Views.CreatePivotDialog.textInvalidRange": "Julat sel tidak sah", "SSE.Views.CreatePivotDialog.textNew": "<PERSON><PERSON><PERSON> kerja baharu", "SSE.Views.CreatePivotDialog.textSelectData": "Pilih data", "SSE.Views.CreatePivotDialog.textTitle": "Cipta Jadual Pivot", "SSE.Views.CreatePivotDialog.txtEmpty": "Medan ini diperlukan", "SSE.Views.CreateSparklineDialog.textDataRange": "Julat sumber data", "SSE.Views.CreateSparklineDialog.textDestination": "<PERSON><PERSON><PERSON>, di mana untuk tempatkan sparkline", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Julat sel tidak sah", "SSE.Views.CreateSparklineDialog.textSelectData": "Pilih data", "SSE.Views.CreateSparklineDialog.textTitle": "Cipta Sparkline", "SSE.Views.CreateSparklineDialog.txtEmpty": "Medan ini diperlukan", "SSE.Views.DataTab.capBtnGroup": "Ku<PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextCustomSort": "<PERSON><PERSON><PERSON> te<PERSON>", "SSE.Views.DataTab.capBtnTextDataValidation": "Pengesahan data", "SSE.Views.DataTab.capBtnTextRemDuplicates": "<PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextToCol": "Teks ke Lajur", "SSE.Views.DataTab.capBtnUngroup": "Leraikan", "SSE.Views.DataTab.capDataExternalLinks": "External Links", "SSE.Views.DataTab.capDataFromText": "Dapatkan data", "SSE.Views.DataTab.capGoalSeek": "Goal Seek", "SSE.Views.DataTab.mniFromFile": "Dari TXT/CSV setempat", "SSE.Views.DataTab.mniFromUrl": "<PERSON><PERSON> al<PERSON>t web TXT/CSV", "SSE.Views.DataTab.mniFromXMLFile": "From Local XML", "SSE.Views.DataTab.textBelow": "<PERSON><PERSON> tambah baris bawah butiran", "SSE.Views.DataTab.textClear": "<PERSON><PERSON>", "SSE.Views.DataTab.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.DataTab.textGroupColumns": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "SSE.Views.DataTab.textGroupRows": "Kumpulan baris", "SSE.Views.DataTab.textRightOf": "<PERSON><PERSON> tambah lajur ke kanan butiran", "SSE.Views.DataTab.textRows": "Lerai<PERSON> baris", "SSE.Views.DataTab.tipCustomSort": "<PERSON><PERSON><PERSON> te<PERSON>", "SSE.Views.DataTab.tipDataFromText": "Dapatkan data dari fail Teks/CSV", "SSE.Views.DataTab.tipDataValidation": "Pengesahan data", "SSE.Views.DataTab.tipExternalLinks": "View other files this spreadsheet is linked to", "SSE.Views.DataTab.tipGoalSeek": "Find the right input for the value you want", "SSE.Views.DataTab.tipGroup": "<PERSON><PERSON><PERSON><PERSON> julat sel", "SSE.Views.DataTab.tipRemDuplicates": "<PERSON><PERSON> keluar baris pendua daripada he<PERSON>an", "SSE.Views.DataTab.tipToColumns": "Pisahkan teks sel ke dalam lajur", "SSE.Views.DataTab.tipUngroup": "Lerai<PERSON> julat sel", "SSE.Views.DataValidationDialog.errorFormula": "<PERSON><PERSON> semasa din<PERSON>ikan kepada ralat. <PERSON><PERSON><PERSON> anda mahu teruskan?", "SSE.Views.DataValidationDialog.errorInvalid": "<PERSON><PERSON> yang anda masukkan bagi medan \"{0}\" adalah tidak sah.", "SSE.Views.DataValidationDialog.errorInvalidDate": "<PERSON><PERSON><PERSON> yang anda masukkan bagi medan \"{0}\" adalah tidak sah.", "SSE.Views.DataValidationDialog.errorInvalidList": "Sumber senarai mestilah senarai terbatas, atau rujukan kepada baris atau lajur tunggal.", "SSE.Views.DataValidationDialog.errorInvalidTime": "<PERSON>sa untuk anda masukkan bagi medan \"{0}\" adalah tidak sah.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "Medan \"{1}\" mestilah lebih besar atau sama dengan medan \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "<PERSON>a perlu masukkan nilai dalam kedua-dua medan \"{0}\" dan medan \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "<PERSON>a perlu masukkan nilai dalam medan \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "<PERSON>at nama yang anda tentukan tidak dapat dijumpai.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "<PERSON>lai negative tidak boleh digunakan dalam syarat \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "Medan \"{0}\" mest<PERSON>h nilai berang<PERSON>, ung<PERSON><PERSON> angka atau merujuk kepada sel yang mengandungi nilai berangka.", "SSE.Views.DataValidationDialog.strError": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.strInput": "Mesej <PERSON>", "SSE.Views.DataValidationDialog.strSettings": "Seting", "SSE.Views.DataValidationDialog.textAlert": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAllow": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textApply": "<PERSON><PERSON> per<PERSON>han ini kepada semua sel dengan seting sama", "SSE.Views.DataValidationDialog.textCellSelected": "<PERSON><PERSON><PERSON><PERSON> sel <PERSON>ili<PERSON>, tun<PERSON><PERSON><PERSON> mesej input", "SSE.Views.DataValidationDialog.textCompare": "Bandingkan kepada", "SSE.Views.DataValidationDialog.textData": "Data", "SSE.Views.DataValidationDialog.textEndDate": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndTime": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textError": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textFormula": "Formula", "SSE.Views.DataValidationDialog.textIgnore": "Abaikan kosong", "SSE.Views.DataValidationDialog.textInput": "Mesej <PERSON>", "SSE.Views.DataValidationDialog.textMax": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMessage": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMin": "Minimum", "SSE.Views.DataValidationDialog.textSelectData": "Pilih data", "SSE.Views.DataValidationDialog.textShowDropDown": "<PERSON><PERSON><PERSON><PERSON> senarai juntai-bawah dalam sel", "SSE.Views.DataValidationDialog.textShowError": "Tunjukkan amaran ralat selepas data yang tidak sah dimasukkan", "SSE.Views.DataValidationDialog.textShowInput": "Tunjuk mesej input apabila sel dipilih", "SSE.Views.DataValidationDialog.textSource": "Sumber", "SSE.Views.DataValidationDialog.textStartDate": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStartTime": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStop": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStyle": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textTitle": "Tajuk", "SSE.Views.DataValidationDialog.textUserEnters": "Apabila pengguna memasukkan data tidak sah, tunjukkan amaran ralat ini", "SSE.Views.DataValidationDialog.txtAny": "<PERSON><PERSON><PERSON> ni<PERSON>", "SSE.Views.DataValidationDialog.txtBetween": "di antara", "SSE.Views.DataValidationDialog.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtElTime": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtEndDate": "<PERSON><PERSON><PERSON> tamat", "SSE.Views.DataValidationDialog.txtEndTime": "<PERSON><PERSON> tamat", "SSE.Views.DataValidationDialog.txtEqual": "sama dengan", "SSE.Views.DataValidationDialog.txtGreaterThan": "adalah lebih besar dari", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "lebih besar dari atau sama dengan", "SSE.Views.DataValidationDialog.txtLength": "Panjang", "SSE.Views.DataValidationDialog.txtLessThan": "kurang da<PERSON>", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "lurang daripada atau sama dengan", "SSE.Views.DataValidationDialog.txtList": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtNotBetween": "bukan di antara", "SSE.Views.DataValidationDialog.txtNotEqual": "tidak sama", "SSE.Views.DataValidationDialog.txtOther": "<PERSON>n", "SSE.Views.DataValidationDialog.txtStartDate": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtStartTime": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtTextLength": "Panjang teks", "SSE.Views.DataValidationDialog.txtTime": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtWhole": "Nombor bulat", "SSE.Views.DigitalFilterDialog.capAnd": "<PERSON>", "SSE.Views.DigitalFilterDialog.capCondition1": "sama dengan", "SSE.Views.DigitalFilterDialog.capCondition10": "didak be<PERSON><PERSON> dengan", "SSE.Views.DigitalFilterDialog.capCondition11": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition12": "tidak mengandungi", "SSE.Views.DigitalFilterDialog.capCondition2": "tidak sama", "SSE.Views.DigitalFilterDialog.capCondition3": "adalah lebih besar dari", "SSE.Views.DigitalFilterDialog.capCondition30": "is after", "SSE.Views.DigitalFilterDialog.capCondition4": "adalah lebih besar dari atau sama kepada", "SSE.Views.DigitalFilterDialog.capCondition40": "is after or equal to", "SSE.Views.DigitalFilterDialog.capCondition5": "adalah kurang dari", "SSE.Views.DigitalFilterDialog.capCondition50": "is before", "SSE.Views.DigitalFilterDialog.capCondition6": "adalah kurang dari atau sama kepada", "SSE.Views.DigitalFilterDialog.capCondition60": "is before or equal to", "SSE.Views.DigitalFilterDialog.capCondition7": "bermula den<PERSON>", "SSE.Views.DigitalFilterDialog.capCondition8": "tidak bermula dengan", "SSE.Views.DigitalFilterDialog.capCondition9": "<PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capOr": "<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.textNoFilter": "tiada tapisan", "SSE.Views.DigitalFilterDialog.textShowRows": "Tunjukkan baris di mana", "SSE.Views.DigitalFilterDialog.textUse1": "Guna ? untuk persembahkan sebarang aksara tunggal", "SSE.Views.DigitalFilterDialog.textUse2": "Guna * untuk persembahkan sebarang siri aksara", "SSE.Views.DigitalFilterDialog.txtSelectDate": "Select date", "SSE.Views.DigitalFilterDialog.txtTitle": "Penap<PERSON>", "SSE.Views.DocumentHolder.advancedEquationText": "Equation settings", "SSE.Views.DocumentHolder.advancedImgText": "Seting <PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.advancedShapeText": "Bentuk Seting Lanjutan", "SSE.Views.DocumentHolder.advancedSlicerText": "Seting <PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.allLinearText": "All - Linear", "SSE.Views.DocumentHolder.allProfText": "All - Professional", "SSE.Views.DocumentHolder.bottomCellText": "Jajarka<PERSON>", "SSE.Views.DocumentHolder.bulletsText": "Bullet dan <PERSON>,", "SSE.Views.DocumentHolder.centerCellText": "Jajarkan <PERSON>", "SSE.Views.DocumentHolder.chartDataText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.chartText": "Seting <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.chartTypeText": "U<PERSON>", "SSE.Views.DocumentHolder.currLinearText": "Current - Linear", "SSE.Views.DocumentHolder.currProfText": "Current - Professional", "SSE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteTableText": "Jadual", "SSE.Views.DocumentHolder.direct270Text": "Putar Teks Ke Atas", "SSE.Views.DocumentHolder.direct90Text": "Putar Teks Ke Bawah", "SSE.Views.DocumentHolder.directHText": "Melintang", "SSE.Views.DocumentHolder.directionText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.editChartText": "Edit Data", "SSE.Views.DocumentHolder.editHyperlinkText": "<PERSON>", "SSE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "SSE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.insertRowAboveText": "Di Atas Baris", "SSE.Views.DocumentHolder.insertRowBelowText": "Di Bawah Baris", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.removeHyperlinkText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectDataText": "<PERSON>", "SSE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.selectTableText": "Jadual", "SSE.Views.DocumentHolder.showEqToolbar": "Show Equation Toolbar", "SSE.Views.DocumentHolder.strDelete": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.strDetails": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.strSetup": "Menetapkan Tandatangan", "SSE.Views.DocumentHolder.strSign": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textAlign": "Jajar", "SSE.Views.DocumentHolder.textArrange": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "Hantar ke Latar Belakang", "SSE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON> ke belakang", "SSE.Views.DocumentHolder.textArrangeForward": "Bawa Ke <PERSON>", "SSE.Views.DocumentHolder.textArrangeFront": "Bawa ke Tanah", "SSE.Views.DocumentHolder.textAverage": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textBullets": "Bullet", "SSE.Views.DocumentHolder.textCopyCells": "Copy cells", "SSE.Views.DocumentHolder.textCount": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textCrop": "Potong", "SSE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFit": "Muat", "SSE.Views.DocumentHolder.textEditPoints": "<PERSON>", "SSE.Views.DocumentHolder.textEntriesList": "<PERSON><PERSON><PERSON> da<PERSON>ada senarai", "SSE.Views.DocumentHolder.textFillDays": "Fill days", "SSE.Views.DocumentHolder.textFillFormatOnly": "Fill formatting only", "SSE.Views.DocumentHolder.textFillMonths": "Fill months", "SSE.Views.DocumentHolder.textFillSeries": "Fill series", "SSE.Views.DocumentHolder.textFillWeekdays": "Fill weekdays", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Fill without formatting", "SSE.Views.DocumentHolder.textFillYears": "Fill years", "SSE.Views.DocumentHolder.textFlashFill": "Flash fill", "SSE.Views.DocumentHolder.textFlipH": "Balikka<PERSON>", "SSE.Views.DocumentHolder.textFlipV": "Balikkan Secar<PERSON>", "SSE.Views.DocumentHolder.textFreezePanes": "Bekukan Anak <PERSON>", "SSE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textFromStorage": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textFromUrl": "Dari URL", "SSE.Views.DocumentHolder.textGrowthTrend": "Growth trend", "SSE.Views.DocumentHolder.textLinearTrend": "Linear trend", "SSE.Views.DocumentHolder.textListSettings": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textMacro": "Tugaskan Ma<PERSON>ro", "SSE.Views.DocumentHolder.textMax": "Mak", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "<PERSON><PERSON>i <PERSON>", "SSE.Views.DocumentHolder.textMoreFormats": "Format selanjutnya", "SSE.Views.DocumentHolder.textNone": "Tiada", "SSE.Views.DocumentHolder.textNumbering": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textReplace": "Gantikan imej", "SSE.Views.DocumentHolder.textResetCrop": "Reset crop", "SSE.Views.DocumentHolder.textRotate": "Putar", "SSE.Views.DocumentHolder.textRotate270": "Putar 90° Lawan Arah Jam", "SSE.Views.DocumentHolder.textRotate90": "Putar 90° Ikut Arah Jam", "SSE.Views.DocumentHolder.textSaveAsPicture": "Save as picture", "SSE.Views.DocumentHolder.textSeries": "Series", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Jajarka<PERSON>", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Jajarkan <PERSON>", "SSE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Jajarkan <PERSON>", "SSE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textShapeAlignTop": "Jajarkan <PERSON>", "SSE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON>t semula", "SSE.Views.DocumentHolder.textUnFreezePanes": "Bebaskan Anak <PERSON>", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "<PERSON><PERSON> anak panah", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Bulet tanda semak", "SSE.Views.DocumentHolder.tipMarkersDash": "Bulet tanda sengkang", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "<PERSON><PERSON> rombus diisi", "SSE.Views.DocumentHolder.tipMarkersFRound": "<PERSON><PERSON> bulat diisi", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Bulet segi empat diisi", "SSE.Views.DocumentHolder.tipMarkersHRound": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersStar": "<PERSON><PERSON> bintang", "SSE.Views.DocumentHolder.topCellText": "Jajarkan <PERSON>", "SSE.Views.DocumentHolder.txtAccounting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAddComment": "Tambah Komen", "SSE.Views.DocumentHolder.txtAddNamedRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "Meningkat", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Auto Muat Kelebaran <PERSON>", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Auto Muat Ketinggian Baris", "SSE.Views.DocumentHolder.txtAverage": "Average", "SSE.Views.DocumentHolder.txtCellFormat": "Format cells", "SSE.Views.DocumentHolder.txtClear": "Kosongkan", "SSE.Views.DocumentHolder.txtClearAll": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearComments": "Komen", "SSE.Views.DocumentHolder.txtClearFormat": "Format", "SSE.Views.DocumentHolder.txtClearHyper": "Hiperpautan", "SSE.Views.DocumentHolder.txtClearPivotField": "Clear filter from {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Buang Kumpulan Sparkline Dipilih", "SSE.Views.DocumentHolder.txtClearSparklines": "Buang Sparkline Dipilih", "SSE.Views.DocumentHolder.txtClearText": "Teks", "SSE.Views.DocumentHolder.txtCollapse": "Collapse", "SSE.Views.DocumentHolder.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtColumnWidth": "Tetapkan Le<PERSON>", "SSE.Views.DocumentHolder.txtCondFormat": "Pemformatan Bersyarat", "SSE.Views.DocumentHolder.txtCopy": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCount": "Count", "SSE.Views.DocumentHolder.txtCurrency": "<PERSON> wang", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Ketinggian Baris Tersuai", "SSE.Views.DocumentHolder.txtCustomSort": "<PERSON><PERSON><PERSON> te<PERSON>", "SSE.Views.DocumentHolder.txtCut": "Potong", "SSE.Views.DocumentHolder.txtDateLong": "Long Date", "SSE.Views.DocumentHolder.txtDateShort": "Short Date", "SSE.Views.DocumentHolder.txtDelete": "Padam", "SSE.Views.DocumentHolder.txtDelField": "Remove", "SSE.Views.DocumentHolder.txtDescending": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDifference": "Difference from", "SSE.Views.DocumentHolder.txtDistribHor": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDistribVert": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtEditComment": "<PERSON>", "SSE.Views.DocumentHolder.txtEditObject": "Edit object", "SSE.Views.DocumentHolder.txtExpand": "Expand", "SSE.Views.DocumentHolder.txtExpandCollapse": "Expand/Collapse", "SSE.Views.DocumentHolder.txtExpandEntire": "Expand Entire Field", "SSE.Views.DocumentHolder.txtFieldSettings": "Field settings", "SSE.Views.DocumentHolder.txtFilter": "Penap<PERSON>", "SSE.Views.DocumentHolder.txtFilterCellColor": "Pen<PERSON><PERSON> mengikut warna sel", "SSE.Views.DocumentHolder.txtFilterFontColor": "Penapis mengikut warna fon", "SSE.Views.DocumentHolder.txtFilterValue": "<PERSON><PERSON><PERSON> mengikut nilai sel <PERSON>", "SSE.Views.DocumentHolder.txtFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtFraction": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtGeneral": "Am", "SSE.Views.DocumentHolder.txtGetLink": "Dapatkan pautan kepada julat ini", "SSE.Views.DocumentHolder.txtGrandTotal": "Grand total", "SSE.Views.DocumentHolder.txtGroup": "Ku<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtHide": "Sembunyikan", "SSE.Views.DocumentHolder.txtIndex": "Index", "SSE.Views.DocumentHolder.txtInsert": "<PERSON>sip<PERSON>", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hiperpautan", "SSE.Views.DocumentHolder.txtInsImage": "Insert image from file", "SSE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "SSE.Views.DocumentHolder.txtLabelFilter": "Label filters", "SSE.Views.DocumentHolder.txtMax": "Max", "SSE.Views.DocumentHolder.txtMin": "Min", "SSE.Views.DocumentHolder.txtMoreOptions": "More options", "SSE.Views.DocumentHolder.txtNormal": "No calculation", "SSE.Views.DocumentHolder.txtNumber": "Nombor", "SSE.Views.DocumentHolder.txtNumFormat": "Format Nombor", "SSE.Views.DocumentHolder.txtPaste": "<PERSON>l", "SSE.Views.DocumentHolder.txtPercent": "% of", "SSE.Views.DocumentHolder.txtPercentage": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtPercentDiff": "% difference from", "SSE.Views.DocumentHolder.txtPercentOfCol": "% of column total", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% of grand total", "SSE.Views.DocumentHolder.txtPercentOfParent": "% of parent total", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% of parent column total", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% of parent row total", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% running total in", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% of row total", "SSE.Views.DocumentHolder.txtPivotSettings": "Pivot Table settings", "SSE.Views.DocumentHolder.txtProduct": "Product", "SSE.Views.DocumentHolder.txtRankAscending": "Rank smallest to largest", "SSE.Views.DocumentHolder.txtRankDescending": "Rank largest to smallest", "SSE.Views.DocumentHolder.txtReapply": "<PERSON><PERSON> semula", "SSE.Views.DocumentHolder.txtRefresh": "Refresh", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON><PERSON><PERSON> baris", "SSE.Views.DocumentHolder.txtRowHeight": "Tetapkan Ketinggian Baris", "SSE.Views.DocumentHolder.txtRunTotal": "Running total in", "SSE.Views.DocumentHolder.txtScientific": "Saintifik", "SSE.Views.DocumentHolder.txtSelect": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftDown": "<PERSON><PERSON><PERSON> sel ke bawah", "SSE.Views.DocumentHolder.txtShiftLeft": "Anjakkan sel ke kiri", "SSE.Views.DocumentHolder.txtShiftRight": "Anjak<PERSON> sel ke kanan", "SSE.Views.DocumentHolder.txtShiftUp": "Anjakkan sel ke atas", "SSE.Views.DocumentHolder.txtShow": "Tunjukkan", "SSE.Views.DocumentHolder.txtShowAs": "Show values as", "SSE.Views.DocumentHolder.txtShowComment": "Tunjuk Komen", "SSE.Views.DocumentHolder.txtShowDetails": "Show details", "SSE.Views.DocumentHolder.txtSort": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSortCellColor": "<PERSON><PERSON> Sel Terpili<PERSON> di atas", "SSE.Views.DocumentHolder.txtSortFontColor": "Warna fon Terpilih di atas", "SSE.Views.DocumentHolder.txtSortOption": "More sort options", "SSE.Views.DocumentHolder.txtSparklines": "Sparkline", "SSE.Views.DocumentHolder.txtSubtotalField": "Subtotal", "SSE.Views.DocumentHolder.txtSum": "Sum", "SSE.Views.DocumentHolder.txtSummarize": "Summarize values by", "SSE.Views.DocumentHolder.txtText": "Teks", "SSE.Views.DocumentHolder.txtTextAdvanced": "<PERSON>enggan Seting Lanjutan", "SSE.Views.DocumentHolder.txtTime": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtTop10": "Top 10", "SSE.Views.DocumentHolder.txtUngroup": "Leraikan", "SSE.Views.DocumentHolder.txtValueFieldSettings": "Value field settings", "SSE.Views.DocumentHolder.txtValueFilter": "Value filters", "SSE.Views.DocumentHolder.txtWidth": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.closeButtonText": "Close", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Automatically update data from the linked sources", "SSE.Views.ExternalLinksDlg.textChange": "Change source", "SSE.Views.ExternalLinksDlg.textDelete": "Break links", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Break all links", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textOpen": "Open source", "SSE.Views.ExternalLinksDlg.textSource": "Source", "SSE.Views.ExternalLinksDlg.textStatus": "Status", "SSE.Views.ExternalLinksDlg.textUnknown": "Unknown", "SSE.Views.ExternalLinksDlg.textUpdate": "Update values", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Update all", "SSE.Views.ExternalLinksDlg.textUpdating": "Updating...", "SSE.Views.ExternalLinksDlg.txtTitle": "External links", "SSE.Views.FieldSettingsDialog.strLayout": "Talaletak", "SSE.Views.FieldSettingsDialog.strSubtotals": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.FieldSettingsDialog.textReport": "Laporkan <PERSON>", "SSE.Views.FieldSettingsDialog.textTitle": "Seting <PERSON>", "SSE.Views.FieldSettingsDialog.txtAverage": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtBlank": "Sisipkan baris kosong selepas setiap item", "SSE.Views.FieldSettingsDialog.txtBottom": "<PERSON><PERSON><PERSON><PERSON><PERSON> pada bawah kumpulan", "SSE.Views.FieldSettingsDialog.txtCompact": "Sarat", "SSE.Views.FieldSettingsDialog.txtCount": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtCountNums": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtCustomName": "<PERSON><PERSON> te<PERSON>i", "SSE.Views.FieldSettingsDialog.txtEmpty": "Tunjuk item dengan tiada data", "SSE.Views.FieldSettingsDialog.txtMax": "Mak", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "Rangka", "SSE.Views.FieldSettingsDialog.txtProduct": "Produk", "SSE.Views.FieldSettingsDialog.txtRepeat": "Ulang label item pada setiap baris", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Tunjukkan subjumlah", "SSE.Views.FieldSettingsDialog.txtSourceName": "Nama sumber:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtSummarize": "Fungsi untuk Subjumlah", "SSE.Views.FieldSettingsDialog.txtTabular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtTop": "<PERSON>n<PERSON><PERSON>n pada atas kumpulan", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.ariaFileMenu": "File menu", "SSE.Views.FileMenu.btnBackCaption": "Buka lokasi fail", "SSE.Views.FileMenu.btnCloseEditor": "Close File", "SSE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnDownloadCaption": "Muat turun sebagai", "SSE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnExportToPDFCaption": "Export to PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnHelpCaption": "Bantu", "SSE.Views.FileMenu.btnHistoryCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnInfoCaption": "Ma<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnPrintCaption": "Cetak", "SSE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnReturnCaption": "<PERSON><PERSON><PERSON> k<PERSON>", "SSE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveAsCaption": "Simpan sebagai", "SSE.Views.FileMenu.btnSaveCaption": "Simpan", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Simpan SALINAN sebagai", "SSE.Views.FileMenu.btnSettingsCaption": "Seting <PERSON>", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "SSE.Views.FileMenu.btnToEditCaption": "<PERSON>", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Tambah Pengarang", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Tambah Teks", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Pengarang", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Ubah hak akses", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Komen", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Diciptakan", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Di<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Pemilik", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Orang yang mempunyai hak", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Spreadsheet info", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Subjek", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Tajuk", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "<PERSON><PERSON><PERSON> naik", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Access Rights", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Ubah hak akses", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Orang yang mempunyai hak", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Guna system data 1904", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Bahasa kamus", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Enable iterative calculation", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Pantas", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Pembayang Fon", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Bahasa Formula", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Contoh: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Show function tooltip", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Show horizontal scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "<PERSON><PERSON><PERSON>n per<PERSON>an dalam HURUF BESAR", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Abaikan perkataan dengan nombor", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Maximum change", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Maximum iterations", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> butang <PERSON> di mana kandungan ditampalkan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Gaya Rujukan R1C1", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Seting wilayah", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Contoh: ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTL interface", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "<PERSON>n<PERSON><PERSON>n komen dalam he<PERSON>an", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "<PERSON><PERSON><PERSON><PERSON><PERSON> per<PERSON>han daripada pengguna lain", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Tunjukkan komen yang diselesaikan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Snapped to the grid while scrolling", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Tegas", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Tab style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "<PERSON>ma antara muka", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Unit Pengukuran", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "<PERSON><PERSON> pem<PERSON>h be<PERSON> seting wilayah", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Show vertical scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Setiap 10 Minit", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Setiap 30 Minit", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Setiap 5 Minit", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "Setiap Jam", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Autopulih", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Autosimpan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "Fill", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "<PERSON><PERSON><PERSON><PERSON> versi per<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "Line", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Setiap <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "Advanced settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "Appearance", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "<PERSON><PERSON>han AutoBaiki…", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Bahasa Belarus", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Bulgarian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Catalonia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Sedang Mengira", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Sentimeter", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Czech", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Customize quick access", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Danish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Pen<PERSON><PERSON>n dan men<PERSON>an", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Greek", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "Bahasa <PERSON>gger<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Bahasa Sepanyol", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "<PERSON><PERSON><PERSON><PERSON> bersama masa n<PERSON>, <PERSON><PERSON><PERSON> disimpan secara automatik.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Finland", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Hungary", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "Armenian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonesia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Inci", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Itali", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Je<PERSON>n", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Korean", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Last used", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Laos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Latvia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "sebagai OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Norwegian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "<PERSON><PERSON><PERSON> (Brazil)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "<PERSON><PERSON><PERSON> (Portugal)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Show the Quick Print button in the editor header", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Rantau", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Roman", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Rusia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Mendayakan semua makro tanpa pemberitahuan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Turn on screen reader support", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Default sheet direction", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "This setting will affect only the new sheets", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Left-to-right", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Right-to-left", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Slovakia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Bahasa Slovenia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Nyahdayakan semua makro tanpa pemberitahuan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "<PERSON><PERSON> butang “Simpan” untuk segerakkan perubahan yang anda dan orang lain lakukan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Sweden", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Use toolbar color as tabs background", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ukraine", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Guna kunci Alt untuk navigasi pengguna", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Guna kunci Pilihan untuk navigasi antara muka pengguna menggunakan papan kekunci", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vietnam", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Tunjuk Pemberitahuan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Nyahdayakan semua makro dengan pemberitahuan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "sebagai Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "<PERSON><PERSON> kerja", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "Cina", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON>gan kata laluan", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON><PERSON> tan<PERSON>n", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Valid signatures have been added to the spreadsheet.<br>The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Ensure the integrity of the spreadsheet by adding an<br>invisible digital signature", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "<PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Pengeditan akan membuang tandatangan daripada hamparan.<br><PERSON><PERSON><PERSON>?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "<PERSON><PERSON><PERSON> ini telah dilindungi oleh kata laluan.", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Encrypt this spreadsheet with a password", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "<PERSON><PERSON><PERSON> ini perlu untuk ditandatangani.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Tandatangan yang sah telah ditambah kepada hamparan. <PERSON><PERSON><PERSON> editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Beberapa tandatangan digital dalam hamparan adalah tidak sah atau tidak dapat disahkan. <PERSON><PERSON><PERSON> di<PERSON> da<PERSON>ada editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Download as", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Save copy as", "SSE.Views.FillSeriesDialog.textAuto": "AutoFill", "SSE.Views.FillSeriesDialog.textCols": "Columns", "SSE.Views.FillSeriesDialog.textDate": "Date", "SSE.Views.FillSeriesDialog.textDateUnit": "Date unit", "SSE.Views.FillSeriesDialog.textDay": "Day", "SSE.Views.FillSeriesDialog.textGrowth": "Growth", "SSE.Views.FillSeriesDialog.textLinear": "Linear", "SSE.Views.FillSeriesDialog.textMonth": "Month", "SSE.Views.FillSeriesDialog.textRows": "Rows", "SSE.Views.FillSeriesDialog.textSeries": "Series in", "SSE.Views.FillSeriesDialog.textStep": "Step value", "SSE.Views.FillSeriesDialog.textStop": "Stop value", "SSE.Views.FillSeriesDialog.textTitle": "Series", "SSE.Views.FillSeriesDialog.textTrend": "Trend", "SSE.Views.FillSeriesDialog.textType": "Type", "SSE.Views.FillSeriesDialog.textWeek": "Weekday", "SSE.Views.FillSeriesDialog.textYear": "Year", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FormatRulesEditDlg.fillColor": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 skala Warna", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 skala Warna", "SSE.Views.FormatRulesEditDlg.textAllBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textAppearance": "Bar Penampilan", "SSE.Views.FormatRulesEditDlg.textApply": "<PERSON><PERSON> kepada Jul<PERSON>", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automatik", "SSE.Views.FormatRulesEditDlg.textAxis": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Bar Arah", "SSE.Views.FormatRulesEditDlg.textBold": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBorder": "Sempadan", "SSE.Views.FormatRulesEditDlg.textBordersColor": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Sempadan <PERSON>", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Tidak boleh tambah pemformatan bersyarat.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Titik tengah Sel", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textClear": "Kosongkan", "SSE.Views.FormatRulesEditDlg.textColor": "Warna teks", "SSE.Views.FormatRulesEditDlg.textContext": "Kandungan", "SSE.Views.FormatRulesEditDlg.textCustom": "Tersuai", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Sempadan Ba<PERSON>enju<PERSON>", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Sempadan Atas Pepenjuru", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Masukkan formula yang sah.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "Formula yang anda masukkan tidak nilaikan nombor, data, masa atau tali.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "<PERSON><PERSON><PERSON><PERSON> ni<PERSON>.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "<PERSON><PERSON> yang anda masukkan bukan nombor, tarikh. masa atau tali yang sah.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "<PERSON>lai bagi {0} mess<PERSON><PERSON> lebih besar daripada nilai bagi {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "<PERSON><PERSON>kka<PERSON> kata laluan di antara {0} and {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormat": "Format", "SSE.Views.FormatRulesEditDlg.textFormula": "Formula", "SSE.Views.FormatRulesEditDlg.textGradient": "Gardien", "SSE.Views.FormatRulesEditDlg.textIconLabel": "apabila {0} {1} dan", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "<PERSON><PERSON><PERSON><PERSON> when {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "<PERSON><PERSON><PERSON><PERSON> nilai adalah", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "<PERSON>tu atau lebih julat data ikon bertindih.<br>Laraskan nilau julat data ikon supaya julat tidak bertindih.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "<PERSON><PERSON> <PERSON>", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textInvalid": "Julat data tidak sah.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "RALAT! Julat sel tidak sah", "SSE.Views.FormatRulesEditDlg.textItalic": "Italik", "SSE.Views.FormatRulesEditDlg.textItem": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "<PERSON><PERSON> ke kanan", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLongBar": "bar terpanjang", "SSE.Views.FormatRulesEditDlg.textMaximum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Matamaks", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Titik tengah", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimum", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Titikmin", "SSE.Views.FormatRulesEditDlg.textNegative": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNewColor": "Tambah Warna Tersuai Baharu", "SSE.Views.FormatRulesEditDlg.textNoBorders": "T<PERSON>da sempanan", "SSE.Views.FormatRulesEditDlg.textNone": "Tiada", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "<PERSON>tu atau lebih nilai tertentu adalah bukan peratus yang sah.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "<PERSON><PERSON> {0} te<PERSON><PERSON> adalah bukan peratusan yang sah.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "<PERSON>tu atau lebih nilai tertentu adalah bukan persentil yang sah.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "<PERSON><PERSON> {0} te<PERSON><PERSON> adalah bukan persentil yang sah.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Sempadan Luar", "SSE.Views.FormatRulesEditDlg.textPercent": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPercentile": "Persentil", "SSE.Views.FormatRulesEditDlg.textPosition": "Kedudukan", "SSE.Views.FormatRulesEditDlg.textPositive": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPresets": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPreview": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "<PERSON>a tidak boleh menggunakan rujukan relatif dalam kriteria pemformatan bersyarat bagi skala warna, bar data dan set ikon.", "SSE.Views.FormatRulesEditDlg.textReverse": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRight2Left": "<PERSON><PERSON> ke <PERSON>ri", "SSE.Views.FormatRulesEditDlg.textRightBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRule": "Peraturan", "SSE.Views.FormatRulesEditDlg.textSameAs": "Sama seperti positif", "SSE.Views.FormatRulesEditDlg.textSelectData": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textShortBar": "Bar Terpendek", "SSE.Views.FormatRulesEditDlg.textShowBar": "Tunjukkan bar sahaja", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Tunjuk ikon sahaja", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Jenis rujukan ini tidak boleg digunakan dalam formula pemformatan bersyarat.<br>Ubah rujukan kepada sel tunggal, atau guna rujukan dengan fungsi lembaran kerja, seperti =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textStrikeout": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSubscript": "Subskrip", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Superskrip", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Sempadan Atas", "SSE.Views.FormatRulesEditDlg.textUnderline": "<PERSON><PERSON> bawah", "SSE.Views.FormatRulesEditDlg.tipBorders": "Sempadan", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Format Nombor", "SSE.Views.FormatRulesEditDlg.txtAccounting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtCurrency": "<PERSON> wang", "SSE.Views.FormatRulesEditDlg.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDateLong": "Long date", "SSE.Views.FormatRulesEditDlg.txtDateShort": "Short date", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Medan ini diperlukan", "SSE.Views.FormatRulesEditDlg.txtFraction": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Am", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "Tiada Ikon", "SSE.Views.FormatRulesEditDlg.txtNumber": "Nombor", "SSE.Views.FormatRulesEditDlg.txtPercentage": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtScientific": "Saintifik", "SSE.Views.FormatRulesEditDlg.txtText": "Teks", "SSE.Views.FormatRulesEditDlg.txtTime": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Edit Peraturan Pemformatan", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Peraturan Pemformatan Baharu", "SSE.Views.FormatRulesManagerDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 std dev melebihi purata", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 std dev kurang purata", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 std dev mele<PERSON>hi purata", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 std dev kurang purata", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 std dev mele<PERSON>hi purata", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 std dev kurang purata", "SSE.Views.FormatRulesManagerDlg.textAbove": "Atas purata", "SSE.Views.FormatRulesManagerDlg.textApply": "<PERSON><PERSON> kepada", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "<PERSON><PERSON> sel bermula dengan", "SSE.Views.FormatRulesManagerDlg.textBelow": "<PERSON> b<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textBetween": "adalah di antara {0} dan {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "<PERSON><PERSON> sel", "SSE.Views.FormatRulesManagerDlg.textColorScale": "<PERSON><PERSON><PERSON> pengredan warna", "SSE.Views.FormatRulesManagerDlg.textContains": "<PERSON><PERSON> sel men<PERSON>i", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Sel mengandungi nilai kosong", "SSE.Views.FormatRulesManagerDlg.textContainsError": "<PERSON><PERSON> men<PERSON>du<PERSON>i ralat", "SSE.Views.FormatRulesManagerDlg.textDelete": "Padam", "SSE.Views.FormatRulesManagerDlg.textDown": "<PERSON><PERSON> peraturan ke bawah", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEdit": "Edit", "SSE.Views.FormatRulesManagerDlg.textEnds": "<PERSON><PERSON> sel tamat dengan", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "<PERSON>a dengan atau melebihi purata", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "<PERSON>a dengan atau di bawah purata", "SSE.Views.FormatRulesManagerDlg.textFormat": "Format", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Set ikon", "SSE.Views.FormatRulesManagerDlg.textNew": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "bukan di antara {0} dan {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "<PERSON><PERSON> sel tidak mengandu<PERSON>i", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Sel tidak mengandungi nilai kosong", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Sel tidak mengandungi ralat", "SSE.Views.FormatRulesManagerDlg.textRules": "Peraturan", "SSE.Views.FormatRulesManagerDlg.textScope": "Tunjuk pemformatan pembaris bagi", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Pilih data", "SSE.Views.FormatRulesManagerDlg.textSelection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "Pangsi ini", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Le<PERSON>ran kerja ini", "SSE.Views.FormatRulesManagerDlg.textThisTable": "<PERSON><PERSON><PERSON> ini", "SSE.Views.FormatRulesManagerDlg.textUnique": "Unik", "SSE.Views.FormatRulesManagerDlg.textUp": "<PERSON><PERSON> peraturan ke atas", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Elemen ini sedang diedit oleh pengguna lain.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Pemformatan Bersyarat", "SSE.Views.FormatSettingsDialog.textCategory": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textFormat": "Format", "SSE.Views.FormatSettingsDialog.textLinked": "Dipautkan ke sumber", "SSE.Views.FormatSettingsDialog.textSeparator": "Guna 1000 pemisah", "SSE.Views.FormatSettingsDialog.textSymbols": "Simbol", "SSE.Views.FormatSettingsDialog.textTitle": "Format Nombor", "SSE.Views.FormatSettingsDialog.txtAccounting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtAs10": "sebagai per sepuluh (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "Sebagai per serratus (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "Sebagai per enam belas (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Sebagai separuh (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "Sebagai per empat (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Sebagai per lapan (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON> wang", "SSE.Views.FormatSettingsDialog.txtCustom": "Tersuai", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Sila masukkan format nombor tersuai dengan berhati-hati. Editor <PERSON><PERSON><PERSON> tidak memerika format tersuai bagi ralat yang mungkin menjejaskan fail xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtFraction": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtGeneral": "Am", "SSE.Views.FormatSettingsDialog.txtNone": "Tiada", "SSE.Views.FormatSettingsDialog.txtNumber": "Nombor", "SSE.Views.FormatSettingsDialog.txtPercentage": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtSample": "Sampel:", "SSE.Views.FormatSettingsDialog.txtScientific": "Saintifik", "SSE.Views.FormatSettingsDialog.txtText": "Teks", "SSE.Views.FormatSettingsDialog.txtTime": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtUpto1": "Sehingga satu digit (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Sehingga dua digit (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Sehingga tiga digit (131/135)", "SSE.Views.FormulaDialog.sDescription": "Perihalan", "SSE.Views.FormulaDialog.textGroupDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.textListDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtRecommended": "Disyorkan", "SSE.Views.FormulaDialog.txtSearch": "<PERSON><PERSON>", "SSE.Views.FormulaDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.capBtnRemoveArr": "Remove Arrows", "SSE.Views.FormulaTab.capBtnTraceDep": "Trace Dependents", "SSE.Views.FormulaTab.capBtnTracePrec": "Trace Precedents", "SSE.Views.FormulaTab.textAutomatic": "Automatik", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "<PERSON>a", "SSE.Views.FormulaTab.textCalculateWorkbook": "<PERSON> buku kerja", "SSE.Views.FormulaTab.textManual": "Manual", "SSE.Views.FormulaTab.tipCalculate": "<PERSON>", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "<PERSON> k<PERSON> buku kerja", "SSE.Views.FormulaTab.tipRemoveArr": "Remove the arrows drawn by Trace Precedents or Trace Dependents", "SSE.Views.FormulaTab.tipShowFormulas": "Display the formula in each cell instead of the resulting value", "SSE.Views.FormulaTab.tipTraceDep": "Show arrows that indicate which cells are affected by the value of the selected cell", "SSE.Views.FormulaTab.tipTracePrec": "Show arrows that indicate which cells affect the value of the selected cell", "SSE.Views.FormulaTab.tipWatch": "Add cells to the Watch Window list", "SSE.Views.FormulaTab.txtAdditional": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtAutosum": "<PERSON>ju<PERSON><PERSON>", "SSE.Views.FormulaTab.txtAutosumTip": "<PERSON><PERSON>", "SSE.Views.FormulaTab.txtCalculation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormulaTip": "<PERSON>si<PERSON><PERSON> fungsi", "SSE.Views.FormulaTab.txtMore": "<PERSON><PERSON>i <PERSON>", "SSE.Views.FormulaTab.txtRecent": "<PERSON><PERSON><PERSON><PERSON>-baru ini", "SSE.Views.FormulaTab.txtRemDep": "Remove Dependents Arrows", "SSE.Views.FormulaTab.txtRemPrec": "Remove Precedents Arrows", "SSE.Views.FormulaTab.txtShowFormulas": "Show Formulas", "SSE.Views.FormulaTab.txtWatch": "Watch Window", "SSE.Views.FormulaWizard.textAny": "mana-mana", "SSE.Views.FormulaWizard.textArgument": "Argumen", "SSE.Views.FormulaWizard.textFunction": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textFunctionRes": "Keputusan fungsi", "SSE.Views.FormulaWizard.textHelp": "Bantu fungsi ini", "SSE.Views.FormulaWizard.textLogical": "logik", "SSE.Views.FormulaWizard.textNoArgs": "Fungsi ini tidak mempunyai argumen", "SSE.Views.FormulaWizard.textNoArgsDesc": "this argument has no description", "SSE.Views.FormulaWizard.textNumber": "nombor", "SSE.Views.FormulaWizard.textReadMore": "Read more", "SSE.Views.FormulaWizard.textRef": "rujukan", "SSE.Views.FormulaWizard.textText": "teks", "SSE.Views.FormulaWizard.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textValue": "Keputusan formula", "SSE.Views.GoalSeekDlg.textChangingCell": "By changing cell", "SSE.Views.GoalSeekDlg.textDataRangeError": "The formula is missing a range", "SSE.Views.GoalSeekDlg.textMustContainFormula": "The cell must contain a formula", "SSE.Views.GoalSeekDlg.textMustContainValue": "Cell must contain a value", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Formula in cell must result in a number", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Reference must be to a single cell", "SSE.Views.GoalSeekDlg.textSelectData": "Select data", "SSE.Views.GoalSeekDlg.textSetCell": "Set cell", "SSE.Views.GoalSeekDlg.textTitle": "Goal seek", "SSE.Views.GoalSeekDlg.textToValue": "To value", "SSE.Views.GoalSeekDlg.txtEmpty": "This field is required", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.GoalSeekStatusDlg.textContinue": "Continue", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Current value:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Goal seeking with cell {0} found a solution.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Goal seeking with cell {0} may not have found a solution.", "SSE.Views.GoalSeekStatusDlg.textPause": "Pause", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Goal seeking with cell {0} on iteration #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "Step", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Target value:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Goal seek status", "SSE.Views.HeaderFooterDialog.textAlign": "<PERSON><PERSON><PERSON><PERSON> dengan jidar halaman", "SSE.Views.HeaderFooterDialog.textAll": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textBold": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textCenter": "Pusat", "SSE.Views.HeaderFooterDialog.textColor": "Warna teks", "SSE.Views.HeaderFooterDialog.textDate": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Halaman pertama berbeza", "SSE.Views.HeaderFooterDialog.textDiffOdd": "<PERSON><PERSON> genap dan ganjil berbeza", "SSE.Views.HeaderFooterDialog.textEven": "Halaman Genap", "SSE.Views.HeaderFooterDialog.textFileName": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textFirst": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textFooter": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textHeader": "Pen<PERSON>pal<PERSON>", "SSE.Views.HeaderFooterDialog.textImage": "Picture", "SSE.Views.HeaderFooterDialog.textInsert": "<PERSON>sip<PERSON>", "SSE.Views.HeaderFooterDialog.textItalic": "Italik", "SSE.Views.HeaderFooterDialog.textLeft": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textMaxError": "<PERSON><PERSON> set<PERSON>nya yang anda masukkan terlalu <PERSON>. Kurangkan bilangan aksara yang digunakan.", "SSE.Views.HeaderFooterDialog.textNewColor": "Tambah Warna Tersuai Baharu", "SSE.Views.HeaderFooterDialog.textOdd": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textPageCount": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textPageNum": "Nombor halaman", "SSE.Views.HeaderFooterDialog.textPresets": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textRight": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textScale": "Skala dengan dokumen", "SSE.Views.HeaderFooterDialog.textSheet": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textStrikeout": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textSubscript": "Subskrip", "SSE.Views.HeaderFooterDialog.textSuperscript": "Superskrip", "SSE.Views.HeaderFooterDialog.textTime": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textTitle": "Seting Pengepala/Pengaki", "SSE.Views.HeaderFooterDialog.textUnderline": "<PERSON><PERSON> bawah", "SSE.Views.HeaderFooterDialog.tipFontName": "Fon", "SSE.Views.HeaderFooterDialog.tipFontSize": "Saiz fon", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "<PERSON><PERSON> kepada", "SSE.Views.HyperlinkSettingsDialog.strRange": "Julat", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Julat Terpilih", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>en di sini", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "<PERSON><PERSON><PERSON><PERSON> pautan di sini", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Ma<PERSON>kkan petua alatan di sini", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Dapatkan Pautan", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Julat Data Dalaman", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "RALAT! Julat sel tidak sah", "SSE.Views.HyperlinkSettingsDialog.textNames": "<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Pilih data", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "SSE.Views.HyperlinkSettingsDialog.textSheets": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textTipText": "<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Seting hiperpautan", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Medan ini diperlukan", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Medan ini perlu sebagai URL dalam format \"http://www.example.com\"", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Medan ini terhad kepada 2083 aksara", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "SSE.Views.ImageSettings.strTransparency": "Opacity", "SSE.Views.ImageSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> seting lanju<PERSON>", "SSE.Views.ImageSettings.textCrop": "Potong", "SSE.Views.ImageSettings.textCropFill": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFit": "Muat", "SSE.Views.ImageSettings.textCropToShape": "Potong ke bentuk", "SSE.Views.ImageSettings.textEdit": "Edit", "SSE.Views.ImageSettings.textEditObject": "<PERSON>", "SSE.Views.ImageSettings.textFlip": "Balikkan", "SSE.Views.ImageSettings.textFromFile": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textFromStorage": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textFromUrl": "Dari URL", "SSE.Views.ImageSettings.textHeight": "Ketinggian", "SSE.Views.ImageSettings.textHint270": "Putar 90° Lawan Arah Jam", "SSE.Views.ImageSettings.textHint90": "Putar 90° Ikut Arah Jam", "SSE.Views.ImageSettings.textHintFlipH": "Balikka<PERSON>", "SSE.Views.ImageSettings.textHintFlipV": "Balikkan Secar<PERSON>", "SSE.Views.ImageSettings.textInsert": "Gantikan Imej", "SSE.Views.ImageSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON> pemalar", "SSE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textRecentlyUsed": "<PERSON><PERSON><PERSON><PERSON>-baru ini", "SSE.Views.ImageSettings.textResetCrop": "Reset crop", "SSE.Views.ImageSettings.textRotate90": "Putar 90°", "SSE.Views.ImageSettings.textRotation": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textSize": "Saiz", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "<PERSON><PERSON> alih atau saiz dengan sel", "SSE.Views.ImageSettingsAdvanced.textAlt": "Teks Berselang", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Perihalan", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Perwakilan berasaskan teks alternatif bagi maklumat objek visual, yang akan dibacakan kepada orang yang mengalami masalah penglihatan atau kognitif untuk membantu mereka memahami dengan lebih baik maklumat yang terdapat dalam imej, bentuk auto, carta atau jadual.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Tajuk", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Dibalikkan", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Melintang", "SSE.Views.ImageSettingsAdvanced.textOneCell": "<PERSON><PERSON> tetapi jangan saizkan dengan sel", "SSE.Views.ImageSettingsAdvanced.textRotation": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textSnap": "Menyentap <PERSON>", "SSE.Views.ImageSettingsAdvanced.textTitle": "Imej – Seting <PERSON>", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "<PERSON><PERSON> dan saizkan dengan sel", "SSE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "SSE.Views.ImportFromXmlDialog.textDestination": "Choose, where to place the data", "SSE.Views.ImportFromXmlDialog.textExist": "Existing worksheet", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ImportFromXmlDialog.textNew": "New worksheet", "SSE.Views.ImportFromXmlDialog.textSelectData": "Select data", "SSE.Views.ImportFromXmlDialog.textTitle": "Import data", "SSE.Views.ImportFromXmlDialog.txtEmpty": "This field is required", "SSE.Views.LeftMenu.ariaLeftMenu": "Left menu", "SSE.Views.LeftMenu.tipAbout": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipChat": "Sembang", "SSE.Views.LeftMenu.tipComments": "Komen", "SSE.Views.LeftMenu.tipFile": "Fail", "SSE.Views.LeftMenu.tipPlugins": "Plug masuk", "SSE.Views.LeftMenu.tipSearch": "<PERSON><PERSON>", "SSE.Views.LeftMenu.tipSpellcheck": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSupport": "Maklum Balas & Sokongan", "SSE.Views.LeftMenu.txtDeveloper": "MOD PEMAJU", "SSE.Views.LeftMenu.txtEditor": "Editor <PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.txtLimit": "<PERSON>", "SSE.Views.LeftMenu.txtTrial": "MOD PERCUBAAN", "SSE.Views.LeftMenu.txtTrialDev": "<PERSON><PERSON>", "SSE.Views.MacroDialog.textMacro": "<PERSON><PERSON> ma<PERSON>ro", "SSE.Views.MacroDialog.textTitle": "Tugaskan Ma<PERSON>ro", "SSE.Views.MainSettingsPrint.okButtonText": "Simpan", "SSE.Views.MainSettingsPrint.strBottom": "<PERSON>wa<PERSON>", "SSE.Views.MainSettingsPrint.strLandscape": "Lanskap", "SSE.Views.MainSettingsPrint.strLeft": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "Potret", "SSE.Views.MainSettingsPrint.strPrint": "Cetak", "SSE.Views.MainSettingsPrint.strPrintTitles": "Tajuk Cetakan", "SSE.Views.MainSettingsPrint.strRight": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strTop": "Atas", "SSE.Views.MainSettingsPrint.textActualSize": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textCustom": "Tersuai", "SSE.Views.MainSettingsPrint.textCustomOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textFitCols": "<PERSON><PERSON> pada <PERSON>", "SSE.Views.MainSettingsPrint.textFitPage": "<PERSON><PERSON> pada <PERSON>", "SSE.Views.MainSettingsPrint.textFitRows": "<PERSON><PERSON> pada <PERSON>", "SSE.Views.MainSettingsPrint.textPageOrientation": "Orientasi ha<PERSON>", "SSE.Views.MainSettingsPrint.textPageScaling": "Penskalaan", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPrintGrid": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Pengepala Baris dan <PERSON>", "SSE.Views.MainSettingsPrint.textRepeat": "<PERSON><PERSON>…", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Ulang lajur di kiri", "SSE.Views.MainSettingsPrint.textRepeatTop": "Ulang baris di atas", "SSE.Views.MainSettingsPrint.textSettings": "Seting untuk", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Julat nama sedia ada tidak boleh diedit dan yang baharu tidak boleh dicipta<br>pada masa sekarang kerana beberapa daripadanya sedang diedit.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.strWorkbook": "<PERSON><PERSON> kerja", "SSE.Views.NamedRangeEditDlg.textDataRange": "Julat Tarikh", "SSE.Views.NamedRangeEditDlg.textExistName": "RALAT! Julat dengan nama tersebut telah wujud.", "SSE.Views.NamedRangeEditDlg.textInvalidName": "<PERSON>a mestilah bermula dengan huruf atau garis bawah dan mestilah tidak mengandungi aksara tidak sah.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "RALAT! Julat sel tidak sah", "SSE.Views.NamedRangeEditDlg.textIsLocked": "RALAT! Elemen ini sedang diedit oleh pengguna lain.", "SSE.Views.NamedRangeEditDlg.textName": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textReservedName": "<PERSON>a yang anda sedang cuba gunakan telah merujuk dalam formula sel. <PERSON>la guna beberapa nama lain.", "SSE.Views.NamedRangeEditDlg.textScope": "Skop", "SSE.Views.NamedRangeEditDlg.textSelectData": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Medan ini diperlukan", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "<PERSON>", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "<PERSON><PERSON>", "SSE.Views.NamedRangePasteDlg.textNames": "<PERSON><PERSON>", "SSE.Views.NamedRangePasteDlg.txtTitle": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textDataRange": "Julat Tarikh", "SSE.Views.NameManagerDlg.textDelete": "Padam", "SSE.Views.NameManagerDlg.textEdit": "Edit", "SSE.Views.NameManagerDlg.textEmpty": "<PERSON><PERSON>da julat nama yang telah dicipta.<br><PERSON><PERSON><PERSON> se<PERSON>-kurangnya satu julat nama dan ia akan muncul dalam medan ini.", "SSE.Views.NameManagerDlg.textFilter": "Penap<PERSON>", "SSE.Views.NameManagerDlg.textFilterAll": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterDefNames": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterSheet": "<PERSON><PERSON><PERSON> k<PERSON>", "SSE.Views.NameManagerDlg.textFilterTableNames": "<PERSON><PERSON><PERSON>ma", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Namakan skop kepada Buku Kerja", "SSE.Views.NameManagerDlg.textNew": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textnoNames": "<PERSON><PERSON>da julat nama yang sepadan tapisan anda yang boleh dijumpai.", "SSE.Views.NameManagerDlg.textRanges": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textScope": "Skop", "SSE.Views.NameManagerDlg.textWorkbook": "<PERSON><PERSON> kerja", "SSE.Views.NameManagerDlg.tipIsLocked": "Elemen ini sedang diedit oleh pengguna lain.", "SSE.Views.NameManagerDlg.txtTitle": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.warnDelete": "<PERSON><PERSON>h anda pasti anda mahu padam nama {0}?", "SSE.Views.PageMarginsDialog.textBottom": "<PERSON>wa<PERSON>", "SSE.Views.PageMarginsDialog.textCenter": "Center on page", "SSE.Views.PageMarginsDialog.textHor": "Horizontally", "SSE.Views.PageMarginsDialog.textLeft": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "Atas", "SSE.Views.PageMarginsDialog.textVert": "Vertically", "SSE.Views.PageMarginsDialog.textWarning": "Warning", "SSE.Views.PageMarginsDialog.warnCheckMargings": "Margins are incorrect", "SSE.Views.ParagraphSettings.strLineHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.strParagraphSpacing": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingBefore": "Sebelum", "SSE.Views.ParagraphSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> seting lanju<PERSON>", "SSE.Views.ParagraphSettings.textAt": "Pada", "SSE.Views.ParagraphSettings.textAtLeast": "Sekurang-kurangnya", "SSE.Views.ParagraphSettings.textAuto": "Pelbagai", "SSE.Views.ParagraphSettings.textExact": "Tepat sekali", "SSE.Views.ParagraphSettings.txtAutoText": "Auto", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Tab yang tertentu akan muncul dalam medan ini", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Inden", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Sebelum", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Khas", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Mengikut", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Fon", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Inden & Penjarakan", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Subskrip", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Superskrip", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Tab", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Pelbagai", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "J<PERSON>k <PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Tepat sekali", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON> pertama", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(tiada)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "<PERSON><PERSON> k<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Tentukan", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Pusat", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Perenggan – Seting Lanjutan", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Delete", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "Duplicate", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Edit", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Formula", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Items Name", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "New", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Calculated Items in", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "sama dengan", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "didak be<PERSON><PERSON> dengan", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "tidak mengandungi", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "di antara", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "bukan di antara", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "tidak sama", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "adalah lebih besar dari", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "adalah lebih besar dari atau sama kepada", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "adalah kurang dari", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "adalah kurang dari atau sama kepada", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "bermula den<PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "tidak bermula dengan", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Tunjuk item untuk yang mana label:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Tunjuk item untuk yang mana:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Guna ? untuk persembahkan sebarang aksara tunggal", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Guna * untuk persembahkan sebarang siri aksara", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "dan", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Tapisan label", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "<PERSON><PERSON><PERSON> ni<PERSON>", "SSE.Views.PivotGroupDialog.textAuto": "Auto", "SSE.Views.PivotGroupDialog.textBy": "Mengikut", "SSE.Views.PivotGroupDialog.textDays": "<PERSON>", "SSE.Views.PivotGroupDialog.textEnd": "<PERSON><PERSON><PERSON> pada", "SSE.Views.PivotGroupDialog.textError": "Medan ini mestilah nilai be<PERSON>ka", "SSE.Views.PivotGroupDialog.textGreaterError": "Nombor hujung mestilah lebih besar berbanding nombor permulaan", "SSE.Views.PivotGroupDialog.textHour": "Jam", "SSE.Views.PivotGroupDialog.textMin": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMonth": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textNumDays": "Bilangan hari", "SSE.Views.PivotGroupDialog.textQuart": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textSec": "Saat", "SSE.Views.PivotGroupDialog.textStart": "<PERSON><PERSON><PERSON><PERSON> pada", "SSE.Views.PivotGroupDialog.textYear": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.txtTitle": "Dikumpulkan", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "You can use Calculated Items for basic calculations between different items within a single field", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Insert into formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "<PERSON><PERSON>", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Item name", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Items", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Read more", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Insert Calculated Item in", "SSE.Views.PivotSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> seting lanju<PERSON>", "SSE.Views.PivotSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textFields": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textFilters": "Penap<PERSON>", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "<PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddColumn": "Tambah kepada Lajur", "SSE.Views.PivotSettings.txtAddFilter": "Tambah kepada <PERSON>", "SSE.Views.PivotSettings.txtAddRow": "Tambah kepada Baris", "SSE.Views.PivotSettings.txtAddValues": "Tambah kepada Nilai", "SSE.Views.PivotSettings.txtFieldSettings": "Seting <PERSON>", "SSE.Views.PivotSettings.txtMoveBegin": "<PERSON><PERSON> ke Permu<PERSON>an", "SSE.Views.PivotSettings.txtMoveColumn": "<PERSON><PERSON> ke Laju<PERSON>", "SSE.Views.PivotSettings.txtMoveDown": "<PERSON><PERSON>", "SSE.Views.PivotSettings.txtMoveEnd": "<PERSON><PERSON> ke Hu<PERSON>g", "SSE.Views.PivotSettings.txtMoveFilter": "<PERSON><PERSON> ke Penapis", "SSE.Views.PivotSettings.txtMoveRow": "<PERSON><PERSON> ke <PERSON>", "SSE.Views.PivotSettings.txtMoveUp": "<PERSON><PERSON>", "SSE.Views.PivotSettings.txtMoveValues": "<PERSON><PERSON> ke <PERSON>", "SSE.Views.PivotSettings.txtRemove": "<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.strLayout": "<PERSON><PERSON> dan <PERSON>", "SSE.Views.PivotSettingsAdvanced.textAlt": "Teks Berselang", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Perihalan", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Perwakilan berasaskan teks alternatif bagi maklumat objek visual, yang akan dibacakan kepada orang yang mengalami masalah penglihatan atau kognitif untuk membantu mereka lebih memahami maklumat yang terdapat dalam imej, bentuk auto, carta atau jadual.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Tajuk", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Automuat lebar lajur semasa kemas kini", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Julat Tarikh", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Sumber Data", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Medan dalam laporan tapisan kawasan dipaparkan", "SSE.Views.PivotSettingsAdvanced.textDown": "Bawa<PERSON>, kemudian atas", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textHeaders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "RALAT! Julat sel tidak sah", "SSE.Views.PivotSettingsAdvanced.textOver": "Atas, Kemudian Bawah", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Pilih data", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Tunjuk untuk lajur", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Tunjukkan medan pengepala untuk baris dan lajur", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Tunjuk untuk baris", "SSE.Views.PivotSettingsAdvanced.textTitle": "Jadual <PERSON> – Seting Lanjutan", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Laporkan medan tapisan per lajur", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Laporkan medan tapisan per baris", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Medan ini diperlukan", "SSE.Views.PivotSettingsAdvanced.txtName": "<PERSON><PERSON>", "SSE.Views.PivotShowDetailDialog.textDescription": "Choose the field containing the detail you want to show:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Show Detail", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON>", "SSE.Views.PivotTable.capGrandTotals": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.capLayout": "Laporkan Talaletak", "SSE.Views.PivotTable.capSubtotals": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.mniBottomSubtotals": "<PERSON><PERSON><PERSON><PERSON><PERSON> semua <PERSON> pada <PERSON>", "SSE.Views.PivotTable.mniInsertBlankLine": "Sisip<PERSON> Garis Kosong selepas <PERSON>em", "SSE.Views.PivotTable.mniLayoutCompact": "Tunjuk dalam Bentuk Kompak", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Jangan <PERSON> Semua Label Item", "SSE.Views.PivotTable.mniLayoutOutline": "Tunjuk dalam Bentuk Rangka", "SSE.Views.PivotTable.mniLayoutRepeat": "Ulang Semua Label Item", "SSE.Views.PivotTable.mniLayoutTabular": "Tunjuk dalam Bentuk Jadual", "SSE.Views.PivotTable.mniNoSubtotals": "<PERSON><PERSON>", "SSE.Views.PivotTable.mniOffTotals": "Tutup untuk <PERSON> dan <PERSON>", "SSE.Views.PivotTable.mniOnColumnsTotals": "<PERSON><PERSON>", "SSE.Views.PivotTable.mniOnRowsTotals": "Hidupkan untuk <PERSON>", "SSE.Views.PivotTable.mniOnTotals": "Hidupkan untuk Baris dan <PERSON>", "SSE.Views.PivotTable.mniRemoveBlankLine": "<PERSON><PERSON> k<PERSON> Kosong se<PERSON>pas <PERSON>", "SSE.Views.PivotTable.mniTopSubtotals": "<PERSON><PERSON><PERSON><PERSON><PERSON> semua <PERSON> pada At<PERSON> Ku<PERSON>", "SSE.Views.PivotTable.textColBanded": "<PERSON><PERSON>", "SSE.Views.PivotTable.textColHeader": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.textRowBanded": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.textRowHeader": "Pengepala Baris", "SSE.Views.PivotTable.tipCalculatedItems": "Calculated items", "SSE.Views.PivotTable.tipCreatePivot": "Sisipkan Jadual Pivot", "SSE.Views.PivotTable.tipGrandTotals": "<PERSON><PERSON><PERSON><PERSON> atau sembunyi jumlah k<PERSON>", "SSE.Views.PivotTable.tipRefresh": "Kemas kini maklumat daripada sumber data", "SSE.Views.PivotTable.tipRefreshCurrent": "Update the information from data source for the current table", "SSE.Views.PivotTable.tipSelect": "<PERSON><PERSON><PERSON> k<PERSON> jadual pivot", "SSE.Views.PivotTable.tipSubtotals": "Tunjuk atau sembunyi subjumlah", "SSE.Views.PivotTable.txtCalculatedItems": "Calculated Items", "SSE.Views.PivotTable.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.PivotTable.txtCreate": "<PERSON><PERSON><PERSON> j<PERSON>", "SSE.Views.PivotTable.txtExpandEntire": "Expand Entire Field", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Custom", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Dark", "SSE.Views.PivotTable.txtGroupPivot_Light": "Light", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Medium", "SSE.Views.PivotTable.txtPivotTable": "Jadual Pivot", "SSE.Views.PivotTable.txtRefresh": "<PERSON><PERSON> semula", "SSE.Views.PivotTable.txtRefreshAll": "Refresh all", "SSE.Views.PivotTable.txtSelect": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Pivot Table Style Dark", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Pivot Table Style Light", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Pivot Table Style Medium", "SSE.Views.PrintSettings.btnDownload": "Simpan & Muat Turun", "SSE.Views.PrintSettings.btnExport": "Save & Export", "SSE.Views.PrintSettings.btnPrint": "Simpan & Cetak", "SSE.Views.PrintSettings.strBottom": "<PERSON>wa<PERSON>", "SSE.Views.PrintSettings.strLandscape": "Lanskap", "SSE.Views.PrintSettings.strLeft": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strMargins": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "Potret", "SSE.Views.PrintSettings.strPrint": "Cetak", "SSE.Views.PrintSettings.strPrintTitles": "Tajuk Cetakan", "SSE.Views.PrintSettings.strRight": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strShow": "Tunjukkan", "SSE.Views.PrintSettings.strTop": "Atas", "SSE.Views.PrintSettings.textActiveSheets": "Active sheets", "SSE.Views.PrintSettings.textActualSize": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textAllSheets": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textCurrentSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textCustom": "Tersuai", "SSE.Views.PrintSettings.textCustomOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textFitCols": "<PERSON><PERSON> pada <PERSON>", "SSE.Views.PrintSettings.textFitPage": "<PERSON><PERSON> pada <PERSON>", "SSE.Views.PrintSettings.textFitRows": "<PERSON><PERSON> pada <PERSON>", "SSE.Views.PrintSettings.textHideDetails": "Semb<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textIgnore": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textLayout": "Talaletak", "SSE.Views.PrintSettings.textMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintSettings.textMarginsNormal": "Normal", "SSE.Views.PrintSettings.textMarginsWide": "Wide", "SSE.Views.PrintSettings.textPageOrientation": "Orientasi ha<PERSON>", "SSE.Views.PrintSettings.textPages": "Pages:", "SSE.Views.PrintSettings.textPageScaling": "Penskalaan", "SSE.Views.PrintSettings.textPageSize": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textPrintGrid": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textPrintHeadings": "Pengepala Baris dan <PERSON>", "SSE.Views.PrintSettings.textPrintRange": "Julat Cetakan", "SSE.Views.PrintSettings.textRange": "Julat", "SSE.Views.PrintSettings.textRepeat": "<PERSON><PERSON>…", "SSE.Views.PrintSettings.textRepeatLeft": "Ulang lajur di kiri", "SSE.Views.PrintSettings.textRepeatTop": "Ulang baris di atas", "SSE.Views.PrintSettings.textSelection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textShowDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textShowGrid": "<PERSON>n<PERSON><PERSON>", "SSE.Views.PrintSettings.textShowHeadings": "<PERSON><PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "SSE.Views.PrintSettings.textTitle": "Seting Ce<PERSON>kan", "SSE.Views.PrintSettings.textTitlePDF": "Seting PDF", "SSE.Views.PrintSettings.textTo": "to", "SSE.Views.PrintSettings.txtMarginsLast": "Last Custom", "SSE.Views.PrintTitlesDialog.textFirstCol": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textFirstRow": "<PERSON><PERSON> pertama", "SSE.Views.PrintTitlesDialog.textFrozenCols": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textFrozenRows": "<PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textInvalidRange": "RALAT! Julat sel tidak sah", "SSE.Views.PrintTitlesDialog.textLeft": "Ulang lajur di kiri", "SSE.Views.PrintTitlesDialog.textNoRepeat": "<PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textRepeat": "<PERSON><PERSON>…", "SSE.Views.PrintTitlesDialog.textSelectRange": "<PERSON><PERSON><PERSON> julat", "SSE.Views.PrintTitlesDialog.textTitle": "Tajuk Cetakan", "SSE.Views.PrintTitlesDialog.textTop": "Ulang baris di atas", "SSE.Views.PrintWithPreview.txtActiveSheets": "Active sheets", "SSE.Views.PrintWithPreview.txtActualSize": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtAllSheets": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "<PERSON>a kepada semua he<PERSON>an", "SSE.Views.PrintWithPreview.txtBothSides": "Print on both sides", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "Flip pages on long edge", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "Flip pages on short edge", "SSE.Views.PrintWithPreview.txtBottom": "<PERSON>wa<PERSON>", "SSE.Views.PrintWithPreview.txtCopies": "Copies", "SSE.Views.PrintWithPreview.txtCurrentSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCustom": "Tersuai", "SSE.Views.PrintWithPreview.txtCustomOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtEmptyTable": "Tiada apa untuk dicetak kerana jadual kosong", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "First page number:", "SSE.Views.PrintWithPreview.txtFitCols": "<PERSON><PERSON> pada <PERSON>", "SSE.Views.PrintWithPreview.txtFitPage": "<PERSON><PERSON> pada <PERSON>", "SSE.Views.PrintWithPreview.txtFitRows": "<PERSON><PERSON> pada <PERSON>", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Garis drid dan pengepala", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Seting pengepala/pengaki", "SSE.Views.PrintWithPreview.txtIgnore": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtLandscape": "Lanskap", "SSE.Views.PrintWithPreview.txtLeft": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMarginsLast": "Last Custom", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Normal", "SSE.Views.PrintWithPreview.txtMarginsWide": "Wide", "SSE.Views.PrintWithPreview.txtOf": "{0}", "SSE.Views.PrintWithPreview.txtOneSide": "Print one sided", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "SSE.Views.PrintWithPreview.txtPage": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Nombor halaman tidak sah", "SSE.Views.PrintWithPreview.txtPageOrientation": "Orientasi ha<PERSON>", "SSE.Views.PrintWithPreview.txtPages": "Pages:", "SSE.Views.PrintWithPreview.txtPageSize": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPortrait": "Potret", "SSE.Views.PrintWithPreview.txtPrint": "Cetak", "SSE.Views.PrintWithPreview.txtPrintGrid": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Pengepala Baris dan <PERSON>", "SSE.Views.PrintWithPreview.txtPrintRange": "Julat Cetakan", "SSE.Views.PrintWithPreview.txtPrintSides": "Print sides", "SSE.Views.PrintWithPreview.txtPrintTitles": "Tajuk Cetakan", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Print to PDF", "SSE.Views.PrintWithPreview.txtRepeat": "<PERSON><PERSON>…", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Ulang lajur di kiri", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Ulang baris di atas", "SSE.Views.PrintWithPreview.txtRight": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSave": "Simpan", "SSE.Views.PrintWithPreview.txtScaling": "Penskalaan", "SSE.Views.PrintWithPreview.txtSelection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Seting bagi helaian", "SSE.Views.PrintWithPreview.txtSheet": "<PERSON><PERSON><PERSON>: {0}", "SSE.Views.PrintWithPreview.txtTo": "to", "SSE.Views.PrintWithPreview.txtTop": "Atas", "SSE.Views.ProtectDialog.textExistName": "RALAT! Julat dengan tajuk tersebut telah wujud.", "SSE.Views.ProtectDialog.textInvalidName": "Julat tajuk mestilah bermula dengan huruf dan hanya mengandungi huruf, angka dan jarak.", "SSE.Views.ProtectDialog.textInvalidRange": "RALAT! Julat sel tidak sah", "SSE.Views.ProtectDialog.textSelectData": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtAllow": "<PERSON><PERSON><PERSON> semua pengguna helaian ini untuk", "SSE.Views.ProtectDialog.txtAllowDescription": "You can unlock specific ranges for editing.", "SSE.Views.ProtectDialog.txtAllowRanges": "Allow edit ranges", "SSE.Views.ProtectDialog.txtAutofilter": "Guna AutoPengisi", "SSE.Views.ProtectDialog.txtDelCols": "<PERSON><PERSON> la<PERSON>r", "SSE.Views.ProtectDialog.txtDelRows": "Padam baris", "SSE.Views.ProtectDialog.txtEmpty": "Medan ini diperlukan", "SSE.Views.ProtectDialog.txtFormatCells": "Format sel", "SSE.Views.ProtectDialog.txtFormatCols": "Format lajur", "SSE.Views.ProtectDialog.txtFormatRows": "Format baris", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Pengesahan kata laluan tidak identical", "SSE.Views.ProtectDialog.txtInsCols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtInsHyper": "<PERSON>sip<PERSON>n", "SSE.Views.ProtectDialog.txtInsRows": "Sisipkan Baris", "SSE.Views.ProtectDialog.txtObjs": "<PERSON>", "SSE.Views.ProtectDialog.txtOptional": "pilihan", "SSE.Views.ProtectDialog.txtPassword": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPivot": "<PERSON><PERSON> JadualPivot dan <PERSON>", "SSE.Views.ProtectDialog.txtProtect": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRange": "Julat", "SSE.Views.ProtectDialog.txtRangeName": "Tajuk", "SSE.Views.ProtectDialog.txtRepeat": "Ulang kata laluan", "SSE.Views.ProtectDialog.txtScen": "Edit scenario", "SSE.Views.ProtectDialog.txtSelLocked": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtSelUnLocked": "<PERSON><PERSON><PERSON> sel terkunci", "SSE.Views.ProtectDialog.txtSheetDescription": "Elakkan perubahan yang tidak diingini daripada orang lain dengan mengehadkan kebolehan untuk edit.", "SSE.Views.ProtectDialog.txtSheetTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtSort": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtWarning": "Amaran: <PERSON><PERSON> anda hilang atau lupa kata laluan, ia tidak dapat dipulihkan. Sila simpan ia dalam tempat selamat.", "SSE.Views.ProtectDialog.txtWBDescription": "Untuk mengelakkan pengguna lain dari melihat lembaran kerja tersembunyi, men<PERSON><PERSON>, men<PERSON><PERSON><PERSON>, memadam atau menyembunyikan lembaran kerja dan menamakan semula lembaran kerja, anda boleh lindungi struktur buku kerja anda dengan kata laluan.", "SSE.Views.ProtectDialog.txtWBTitle": "Lindung struktur Buku Kerja", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "Anonymous", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Anyone", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Edit", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Denied", "SSE.Views.ProtectedRangesEditDlg.textCanView": "View", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "The range title must begin with a letter and may only contain letters, numbers, and spaces.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Remove", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "Select data", "SSE.Views.ProtectedRangesEditDlg.textYou": "you", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Access to range", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "This field is required", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "Protect", "SSE.Views.ProtectedRangesEditDlg.txtRange": "Range", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "Title", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Only you can edit this range", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Start typing name or email", "SSE.Views.ProtectedRangesManagerDlg.guestText": "Guest", "SSE.Views.ProtectedRangesManagerDlg.lockText": "Locked", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "Delete", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "Edit", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "No protected ranges have been created yet.<br>Create at least one protected range and it will appear in this field.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "Filter", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "All", "SSE.Views.ProtectedRangesManagerDlg.textNew": "New", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Protect sheet", "SSE.Views.ProtectedRangesManagerDlg.textRange": "Range", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "You can restrict editing or viewing ranges to selected people.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "Title", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Access", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Denied", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "Edit", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "Edit range", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "New range", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Protected ranges", "SSE.Views.ProtectedRangesManagerDlg.txtView": "View", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Are you sure you want to delete the protected range {0}?<br>Anyone who has edit access to the spreadsheet will be able to edit content in the range.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Are you sure you want to delete the protected ranges?<br>Anyone who has edit access to the spreadsheet will be able to edit content in those ranges.", "SSE.Views.ProtectRangesDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.lockText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textDelete": "Padam", "SSE.Views.ProtectRangesDlg.textEdit": "Edit", "SSE.Views.ProtectRangesDlg.textEmpty": "T<PERSON>da julat diben<PERSON>an untuk pengeditan.", "SSE.Views.ProtectRangesDlg.textNew": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textProtect": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textPwd": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRange": "Julat", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Julat dinyahsekat oleh kata laluan apabila he<PERSON>an di<PERSON> (ini hanya berkesan untuk sel disekat)", "SSE.Views.ProtectRangesDlg.textTitle": "Tajuk", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Elemen ini sedang diedit oleh pengguna lain.", "SSE.Views.ProtectRangesDlg.txtEditRange": "<PERSON>", "SSE.Views.ProtectRangesDlg.txtNewRange": "Julat Baharu", "SSE.Views.ProtectRangesDlg.txtNo": "Tidak", "SSE.Views.ProtectRangesDlg.txtTitle": "Benarkan Pengguna untuk Edit Julat", "SSE.Views.ProtectRangesDlg.txtYes": "Ya", "SSE.Views.ProtectRangesDlg.warnDelete": "<PERSON><PERSON>h anda pasti anda mahu padam nama {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.RemoveDuplicatesDialog.textDescription": "<PERSON><PERSON><PERSON> memadam nilai pendua, pilih satu atau lebih lajur yang mengandungi pendua.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Data saya mempunyai pengepala", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "<PERSON><PERSON><PERSON>", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "<PERSON><PERSON>", "SSE.Views.RightMenu.ariaRightMenu": "Right menu", "SSE.Views.RightMenu.txtCellSettings": "Seting <PERSON>", "SSE.Views.RightMenu.txtChartSettings": "<PERSON><PERSON>", "SSE.Views.RightMenu.txtImageSettings": "<PERSON><PERSON> <PERSON>", "SSE.Views.RightMenu.txtParagraphSettings": "Seting per<PERSON>", "SSE.Views.RightMenu.txtPivotSettings": "Seting <PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtSettings": "Seting biasa", "SSE.Views.RightMenu.txtShapeSettings": "<PERSON>ing bentuk", "SSE.Views.RightMenu.txtSignatureSettings": "<PERSON><PERSON>pan <PERSON>", "SSE.Views.RightMenu.txtSlicerSettings": "<PERSON><PERSON>", "SSE.Views.RightMenu.txtSparklineSettings": "Seting sparkline", "SSE.Views.RightMenu.txtTableSettings": "Seting <PERSON>", "SSE.Views.RightMenu.txtTextArtSettings": "Seting <PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textAuto": "Auto", "SSE.Views.ScaleDialog.textError": "<PERSON><PERSON> yang dimasukkan adalah tidak betul.", "SSE.Views.ScaleDialog.textFewPages": "halaman", "SSE.Views.ScaleDialog.textFitTo": "<PERSON><PERSON>", "SSE.Views.ScaleDialog.textHeight": "Ketinggian", "SSE.Views.ScaleDialog.textManyPages": "halaman", "SSE.Views.ScaleDialog.textOnePage": "halaman", "SSE.Views.ScaleDialog.textScaleTo": "<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textTitle": "<PERSON>ing <PERSON>", "SSE.Views.ScaleDialog.textWidth": "<PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "<PERSON><PERSON> maksimum bagi medan ini ialah {0}", "SSE.Views.SetValueDialog.txtMinText": "<PERSON>lai minimum bagi medan ini ialah {0}", "SSE.Views.ShapeSettings.strBackground": "<PERSON><PERSON> latar belakang", "SSE.Views.ShapeSettings.strChange": "Ubah Autobentuk", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strForeground": "<PERSON><PERSON> la<PERSON> depan", "SSE.Views.ShapeSettings.strPattern": "Pola", "SSE.Views.ShapeSettings.strShadow": "Tunjukkan bayang", "SSE.Views.ShapeSettings.strSize": "Saiz", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "Kelegapan", "SSE.Views.ShapeSettings.strType": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "SSE.Views.ShapeSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> seting lanju<PERSON>", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "<PERSON><PERSON> yang dimasukkan adalah tidak betul.<br><PERSON><PERSON> masukkan nilai di antara 0 pt dan 1584 pt.", "SSE.Views.ShapeSettings.textColor": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textDirection": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textEditPoints": "Edit points", "SSE.Views.ShapeSettings.textEditShape": "Edit shape", "SSE.Views.ShapeSettings.textEmptyPattern": "Tiada Pola", "SSE.Views.ShapeSettings.textEyedropper": "Eyedropper", "SSE.Views.ShapeSettings.textFlip": "Balikkan", "SSE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromStorage": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromUrl": "Dari URL", "SSE.Views.ShapeSettings.textGradient": "<PERSON> gradien", "SSE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textHint270": "Putar 90° Lawan Arah Jam", "SSE.Views.ShapeSettings.textHint90": "Putar 90° Ikut Arah Jam", "SSE.Views.ShapeSettings.textHintFlipH": "Balikka<PERSON>", "SSE.Views.ShapeSettings.textHintFlipV": "Balikkan Secar<PERSON>", "SSE.Views.ShapeSettings.textImageTexture": "Gambar atau Tekstur", "SSE.Views.ShapeSettings.textLinear": "Linear", "SSE.Views.ShapeSettings.textMoreColors": "More colors", "SSE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textNoShadow": "No Shadow", "SSE.Views.ShapeSettings.textOriginalSize": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textPatternFill": "Pola", "SSE.Views.ShapeSettings.textPosition": "Kedudukan", "SSE.Views.ShapeSettings.textRadial": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textRecentlyUsed": "<PERSON><PERSON><PERSON><PERSON>-baru ini", "SSE.Views.ShapeSettings.textRotate90": "Putar 90°", "SSE.Views.ShapeSettings.textRotation": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textShadow": "Shadow", "SSE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStyle": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textTexture": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Tambah mata gradien", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "<PERSON><PERSON> keluar mata gradien", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGranite": "Granit", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Pelapik Teks", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "<PERSON><PERSON> alih atau saiz dengan sel", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Teks Berselang", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Perihalan", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Perwakilan berasaskan teks alternatif bagi maklumat objek visual, yang akan dibacakan kepada orang yang mengalami masalah penglihatan atau kognitif untuk membantu mereka memahami dengan lebih baik maklumat yang terdapat dalam imej, bentuk auto, carta atau jadual.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Tajuk", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON>ah", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "AutoMuat", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Serong", "SSE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON>wa<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON> h<PERSON> be<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Bilangan <PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Saiz <PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Dibalikkan", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Ketinggian", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Melintang", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON> pemalar", "SSE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "<PERSON><PERSON> tetapi jangan saizkan dengan sel", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Benarkan teks untuk melimpahi bentuk", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "<PERSON><PERSON> ke muatkan teks", "SSE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRotation": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRound": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSize": "Saiz", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Menyentap <PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "<PERSON>jar<PERSON><PERSON> antara lajur", "SSE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON>asa dua", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Kotak Teks", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Bentuk – Seting Lanjutan", "SSE.Views.ShapeSettingsAdvanced.textTop": "Atas", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "<PERSON><PERSON> dan saizkan dengan sel", "SSE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Berat & Anak <PERSON>", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "<PERSON><PERSON>", "SSE.Views.SignatureSettings.strDelete": "<PERSON><PERSON>", "SSE.Views.SignatureSettings.strDetails": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strInvalid": "Tandatangan tidak sah", "SSE.Views.SignatureSettings.strRequested": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSetup": "Menetapkan Tandatangan", "SSE.Views.SignatureSettings.strSign": "<PERSON><PERSON>", "SSE.Views.SignatureSettings.strSignature": "Tandatangan", "SSE.Views.SignatureSettings.strSigner": "Penandatangan", "SSE.Views.SignatureSettings.strValid": "Tandatangan sah", "SSE.Views.SignatureSettings.txtContinueEditing": "Edit juga", "SSE.Views.SignatureSettings.txtEditWarning": "Pengeditan akan membuang tandatangan daripada hamparan.<br><PERSON><PERSON><PERSON>?", "SSE.Views.SignatureSettings.txtRemoveWarning": "<PERSON><PERSON><PERSON> anda mahu alih keluar tandatangan ini?<br>Ia tidak boleh dibuat asal.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "<PERSON><PERSON><PERSON> ini perlu untuk ditandatangani.", "SSE.Views.SignatureSettings.txtSigned": "Tandatangan yang sah telah ditambah kepada hamparan. <PERSON><PERSON><PERSON> editing.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Beberapa tandatangan digital dalam hamparan adalah tidak sah atau tidak dapat disahkan. <PERSON><PERSON><PERSON> di<PERSON> da<PERSON>ada editing.", "SSE.Views.SlicerAddDialog.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerAddDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.strHideNoData": "Sembunyikan item dengan tiada data", "SSE.Views.SlicerSettings.strIndNoData": "Secara visual menunjukkan item yang tanpa data", "SSE.Views.SlicerSettings.strShowDel": "Tunjuk item dipadam dariapda sumber data", "SSE.Views.SlicerSettings.strShowNoData": "Tunjuk item dengan tiada data terakhir", "SSE.Views.SlicerSettings.strSorting": "pen<PERSON><PERSON>an dan penapisan", "SSE.Views.SlicerSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> seting lanju<PERSON>", "SSE.Views.SlicerSettings.textAsc": "Meningkat", "SSE.Views.SlicerSettings.textAZ": "A ke Z", "SSE.Views.SlicerSettings.textButtons": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textDesc": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textHeight": "Ketinggian", "SSE.Views.SlicerSettings.textHor": "Melintang", "SSE.Views.SlicerSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textLargeSmall": "terbesar ke terkecil", "SSE.Views.SlicerSettings.textLock": "<PERSON><PERSON><PERSON><PERSON><PERSON> pensaizan atau peralihan", "SSE.Views.SlicerSettings.textNewOld": "terbaharu kepada tertua", "SSE.Views.SlicerSettings.textOldNew": "Paling tua ke paling baharu", "SSE.Views.SlicerSettings.textPosition": "Kedudukan", "SSE.Views.SlicerSettings.textSize": "Saiz", "SSE.Views.SlicerSettings.textSmallLarge": "paling kecil ke paling besar", "SSE.Views.SlicerSettings.textStyle": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textVert": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textWidth": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "Z ke A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Ketinggian", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Sembunyikan item dengan tiada data", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Secara visual menunjukkan item yang tanpa data", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Rujukan", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Tunjuk item dipadam dariapda sumber data", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "<PERSON><PERSON><PERSON><PERSON> paparan", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Tunjuk item dengan tiada data terakhir", "SSE.Views.SlicerSettingsAdvanced.strSize": "Saiz", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Pengisihan & Penapisan", "SSE.Views.SlicerSettingsAdvanced.strStyle": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Gaya & Saiz", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "<PERSON><PERSON> alih atau saiz dengan sel", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Teks Berselang", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Perihalan", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Perwakilan berasaskan teks alternatif bagi maklumat objek visual, yang akan dibacakan kepada orang yang mengalami masalah penglihatan atau kognitif untuk membantu mereka lebih memahami maklumat yang terdapat dalam imej, bentuk auto, carta atau jadual.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Tajuk", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Meningkat", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A ke Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Nama untuk digunakan dalam formula", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Pen<PERSON>pal<PERSON>", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "terbesar ke terkecil", "SSE.Views.SlicerSettingsAdvanced.textName": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "terbaharu kepada tertua", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "Paling tua ke paling baharu", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "<PERSON><PERSON> tetapi jangan saizkan dengan sel", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "paling kecil ke paling besar", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Menyentap <PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSort": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "<PERSON>a sumber", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Pengh<PERSON>s – Seting Lanjutan", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "<PERSON><PERSON> dan saizkan dengan sel", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z ke A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Medan ini diperlukan", "SSE.Views.SortDialog.errorEmpty": "<PERSON><PERSON><PERSON> jenis kriteria mestilah mempunyai lajur atau baris tertentu.", "SSE.Views.SortDialog.errorMoreOneCol": "Le<PERSON>h dari satu lajur dipilih.", "SSE.Views.SortDialog.errorMoreOneRow": "Lebih dari satu baris dipilih.", "SSE.Views.SortDialog.errorNotOriginalCol": "<PERSON><PERSON><PERSON> yang anda pilih tidak berada dalam julat pilihan asli.", "SSE.Views.SortDialog.errorNotOriginalRow": "<PERSON><PERSON> yang anda pilih tidak berada dalam julat pilihan asli.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 sedang disusun mengikut warna yang sama lebih dari sekali.<br><PERSON><PERSON> susunan kriteria pendua dan cuba lagi.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 sedang disusun mengikut nilai lebih dari sekali.<br><PERSON><PERSON> susunan kriteria pendua dan cuba lagi.", "SSE.Views.SortDialog.textAsc": "Meningkat", "SSE.Views.SortDialog.textAuto": "Automatik", "SSE.Views.SortDialog.textAZ": "A ke Z", "SSE.Views.SortDialog.textBelow": "<PERSON>", "SSE.Views.SortDialog.textBtnCopy": "Copy", "SSE.Views.SortDialog.textBtnDelete": "Delete", "SSE.Views.SortDialog.textBtnNew": "New", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON> sel", "SSE.Views.SortDialog.textColumn": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDesc": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDown": "<PERSON>h ke turun tahap", "SSE.Views.SortDialog.textFontColor": "<PERSON><PERSON> fon", "SSE.Views.SortDialog.textLeft": "<PERSON><PERSON>", "SSE.Views.SortDialog.textLevels": "Levels", "SSE.Views.SortDialog.textMoreCols": "(<PERSON><PERSON><PERSON>)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON>)", "SSE.Views.SortDialog.textNone": "Tiada", "SSE.Views.SortDialog.textOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textOrder": "Urutan", "SSE.Views.SortDialog.textRight": "<PERSON><PERSON>", "SSE.Views.SortDialog.textRow": "<PERSON><PERSON>", "SSE.Views.SortDialog.textSort": "<PERSON><PERSON> pada", "SSE.Views.SortDialog.textSortBy": "<PERSON><PERSON>", "SSE.Views.SortDialog.textThenBy": "Ke<PERSON><PERSON> mengikut", "SSE.Views.SortDialog.textTop": "Atas", "SSE.Views.SortDialog.textUp": "<PERSON>h ke naik tahap", "SSE.Views.SortDialog.textValues": "<PERSON><PERSON>", "SSE.Views.SortDialog.textZA": "Z ke A", "SSE.Views.SortDialog.txtInvalidRange": "Julat sel tidak sah.", "SSE.Views.SortDialog.txtTitle": "<PERSON><PERSON>", "SSE.Views.SortFilterDialog.textAsc": "Meningkat (A ke Z) mengikut", "SSE.Views.SortFilterDialog.textDesc": "<PERSON><PERSON><PERSON> (Z to A) mengikut", "SSE.Views.SortFilterDialog.textNoSort": "No sort", "SSE.Views.SortFilterDialog.txtTitle": "<PERSON><PERSON>", "SSE.Views.SortFilterDialog.txtTitleValue": "Sort by value", "SSE.Views.SortOptionsDialog.textCase": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>", "SSE.Views.SortOptionsDialog.textHeaders": "Data saya mempunyai pengepala", "SSE.Views.SortOptionsDialog.textLeftRight": "<PERSON>ih kiri ke kanan", "SSE.Views.SortOptionsDialog.textOrientation": "Orientasi", "SSE.Views.SortOptionsDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.SortOptionsDialog.textTopBottom": "<PERSON><PERSON> atas ke bawah", "SSE.Views.SpecialPasteDialog.textAdd": "Tambah", "SSE.Views.SpecialPasteDialog.textAll": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textBlanks": "Langkau kosong", "SSE.Views.SpecialPasteDialog.textColWidth": "<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textComments": "Komen", "SSE.Views.SpecialPasteDialog.textDiv": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFFormat": "Formula & pemformatan", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formula & format nombor", "SSE.Views.SpecialPasteDialog.textFormats": "Format", "SSE.Views.SpecialPasteDialog.textFormulas": "Formula", "SSE.Views.SpecialPasteDialog.textFWidth": "Formula & lebar lajur", "SSE.Views.SpecialPasteDialog.textMult": "Dar<PERSON>", "SSE.Views.SpecialPasteDialog.textNone": "Tiada", "SSE.Views.SpecialPasteDialog.textOperation": "Operasi", "SSE.Views.SpecialPasteDialog.textPaste": "<PERSON>l", "SSE.Views.SpecialPasteDialog.textSub": "<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textTitle": "<PERSON><PERSON> is<PERSON>wa", "SSE.Views.SpecialPasteDialog.textTranspose": "Silang Ganti", "SSE.Views.SpecialPasteDialog.textValues": "<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textVFormat": "Nilai & Pemformatan", "SSE.Views.SpecialPasteDialog.textVNFormat": "<PERSON><PERSON> dalam format nombor", "SSE.Views.SpecialPasteDialog.textWBorders": "<PERSON><PERSON><PERSON> kecuali sempadan", "SSE.Views.Spellcheck.noSuggestions": "Tiada cadangan ejaan", "SSE.Views.Spellcheck.textChange": "Ubah", "SSE.Views.Spellcheck.textChangeAll": "Ubah Semua", "SSE.Views.Spellcheck.textIgnore": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textIgnoreAll": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.txtAddToDictionary": "Tam<PERSON>", "SSE.Views.Spellcheck.txtClosePanel": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.txtComplete": "Se<PERSON>k ejaan telah dilengkapkan", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Bahasa kamus", "SSE.Views.Spellcheck.txtNextTip": "Pergi ke perkataan seterusnya", "SSE.Views.Spellcheck.txtSpelling": "Mengeja", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(<PERSON><PERSON> ke hujung)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "Create a copy", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(Create new spreadsheet)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "<PERSON><PERSON> sebelum helaian", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Spreadsheet", "SSE.Views.Statusbar.filteredRecordsText": "{0} dari {1} rekod telah ditapis", "SSE.Views.Statusbar.filteredText": "<PERSON><PERSON> penapis", "SSE.Views.Statusbar.itemAverage": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemCount": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemDelete": "Padam", "SSE.Views.Statusbar.itemHidden": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemHide": "Sembunyikan", "SSE.Views.Statusbar.itemInsert": "<PERSON>sip<PERSON>", "SSE.Views.Statusbar.itemMaximum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMinimum": "Minimum", "SSE.Views.Statusbar.itemMoveOrCopy": "Move or copy", "SSE.Views.Statusbar.itemProtect": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemRename": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemStatus": "Status disimpan", "SSE.Views.Statusbar.itemSum": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemUnProtect": "Nyahlindung", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Le<PERSON><PERSON> kerja dengan nama tersebut telah wujud.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "<PERSON>a helaian tidak boleh mengandungi aksara yang berikut: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "<PERSON><PERSON>", "SSE.Views.Statusbar.selectAllSheets": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.sheetIndexText": "<PERSON><PERSON><PERSON> {0} <PERSON><PERSON> {1}", "SSE.Views.Statusbar.textAverage": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textCount": "<PERSON><PERSON>", "SSE.Views.Statusbar.textMax": "Mak", "SSE.Views.Statusbar.textMin": "Min", "SSE.Views.Statusbar.textNewColor": "Tambah Warna Tersuai Baharu", "SSE.Views.Statusbar.textNoColor": "Tiada Warna", "SSE.Views.Statusbar.textSum": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipAddTab": "Tambah lembaran kerja", "SSE.Views.Statusbar.tipFirst": "<PERSON><PERSON><PERSON> ke helaian pertama", "SSE.Views.Statusbar.tipLast": "<PERSON><PERSON><PERSON> ke helaian te<PERSON>hir", "SSE.Views.Statusbar.tipListOfSheets": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipNext": "<PERSON><PERSON><PERSON>an skrol kanan", "SSE.Views.Statusbar.tipPrev": "<PERSON><PERSON><PERSON> he<PERSON>an skrol kiri", "SSE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomIn": "<PERSON><PERSON> masuk", "SSE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON> keluar", "SSE.Views.Statusbar.ungroupSheets": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.zoomText": "Zum {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Operasi tidak dapat dilakukan bagi julat sel yang dipilih.<br><PERSON><PERSON><PERSON> julat data seragam yang berbeza dari sedia ada dan cuba semula.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Operasi tidak dapat dilengkapkan bagi julat sel yang dipilih.<br><PERSON><PERSON>h satu julat supaya baris pertama jadual berada pada baris yang sama<br>dan men<PERSON><PERSON><PERSON><PERSON> jadual bertindih pada yang semasa.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Operasi tidak dapat dilengkapkan bagi julat sel yang dipilih.<br><PERSON><PERSON><PERSON> julat yang mana tidak beserta jadual lain.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Formula tatasusunan multisel tidak diizinkan dalam jadual.", "SSE.Views.TableOptionsDialog.txtEmpty": "Medan ini diperlukan", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.TableOptionsDialog.txtInvalidRange": "RALAT! Julat sel tidak sah", "SSE.Views.TableOptionsDialog.txtNote": "<PERSON><PERSON><PERSON><PERSON> perlu kekal dalam baris yang sama, dan hasil julat jadual mestilah bertindih julat jadual asli.", "SSE.Views.TableOptionsDialog.txtTitle": "Tajuk", "SSE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON>", "SSE.Views.TableSettings.deleteRowText": "<PERSON><PERSON>", "SSE.Views.TableSettings.deleteTableText": "Padam Jadual", "SSE.Views.TableSettings.insertColumnLeftText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.insertColumnRightText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.insertRowAboveText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.insertRowBelowText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.notcriticalErrorTitle": "<PERSON><PERSON>", "SSE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectDataText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textActions": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> seting lanju<PERSON>", "SSE.Views.TableSettings.textBanded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textConvertRange": "<PERSON><PERSON><PERSON> ke julat", "SSE.Views.TableSettings.textEdit": "Baris & Lajur", "SSE.Views.TableSettings.textEmptyTemplate": "Tiada templat", "SSE.Views.TableSettings.textExistName": "RALAT! Julat dengan nama tersebut telah wujud.", "SSE.Views.TableSettings.textFilter": "Butang penapis", "SSE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textHeader": "Pen<PERSON>pal<PERSON>", "SSE.Views.TableSettings.textInvalidName": "RALAT! Nama jadual tidak sah", "SSE.Views.TableSettings.textIsLocked": "Elemen ini sedang diedit oleh pengguna lain.", "SSE.Views.TableSettings.textLast": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textLongOperation": "Operasi panjang", "SSE.Views.TableSettings.textPivot": "Sisipkan jadual pivot", "SSE.Views.TableSettings.textRemDuplicates": "<PERSON><PERSON>", "SSE.Views.TableSettings.textReservedName": "<PERSON>a yang anda sedang cuba gunakan telah merujuk dalam formula sel. <PERSON>la guna beberapa nama lain.", "SSE.Views.TableSettings.textResize": "<PERSON><PERSON>", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSlicer": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textTableName": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textTemplate": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textTotal": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.txtGroupTable_Custom": "Custom", "SSE.Views.TableSettings.txtGroupTable_Dark": "Dark", "SSE.Views.TableSettings.txtGroupTable_Light": "Light", "SSE.Views.TableSettings.txtGroupTable_Medium": "Medium", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Table style dark", "SSE.Views.TableSettings.txtTable_TableStyleLight": "Table style light", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "Table style medium", "SSE.Views.TableSettings.warnLongOperation": "<PERSON>si yang anda akan lakukan mungkin mengambil lebih masa untuk lengkap.<br><PERSON><PERSON><PERSON> anda pasti mahu teruskan?", "SSE.Views.TableSettingsAdvanced.textAlt": "Teks Berselang", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Perihalan", "SSE.Views.TableSettingsAdvanced.textAltTip": "Perwakilan berasaskan teks alternatif bagi maklumat objek visual, yang akan dibacakan kepada orang yang mengalami masalah penglihatan atau kognitif untuk membantu mereka lebih memahami maklumat yang terdapat dalam imej, bentuk auto, carta atau jadual.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Tajuk", "SSE.Views.TableSettingsAdvanced.textTitle": "Jadual – Seting <PERSON>", "SSE.Views.TextArtSettings.strBackground": "<PERSON><PERSON> latar belakang", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strForeground": "<PERSON><PERSON> la<PERSON> depan", "SSE.Views.TextArtSettings.strPattern": "Pola", "SSE.Views.TextArtSettings.strSize": "Saiz", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "Kelegapan", "SSE.Views.TextArtSettings.strType": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "<PERSON><PERSON> yang dimasukkan adalah tidak betul.<br><PERSON><PERSON> masukkan nilai di antara 0 pt dan 1584 pt.", "SSE.Views.TextArtSettings.textColor": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textDirection": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textEmptyPattern": "Tiada Pola", "SSE.Views.TextArtSettings.textFromFile": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textFromUrl": "Dari URL", "SSE.Views.TextArtSettings.textGradient": "<PERSON> gradien", "SSE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textImageTexture": "Gambar atau Tekstur", "SSE.Views.TextArtSettings.textLinear": "Linear", "SSE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textPatternFill": "Pola", "SSE.Views.TextArtSettings.textPosition": "Kedudukan", "SSE.Views.TextArtSettings.textRadial": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStyle": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textTemplate": "Templat", "SSE.Views.TextArtSettings.textTexture": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textTile": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textTransform": "Transformasi", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Tambah mata gradien", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "<PERSON><PERSON> keluar mata gradien", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGrain": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGranite": "Granit", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtWood": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnAddComment": "Tambah Komen", "SSE.Views.Toolbar.capBtnColorSchemas": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnComment": "Komen", "SSE.Views.Toolbar.capBtnInsHeader": "Pengepala & Pengaki", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Simbol", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageBreak": "Breaks", "SSE.Views.Toolbar.capBtnPageOrient": "Orientasi", "SSE.Views.Toolbar.capBtnPageSize": "Saiz", "SSE.Views.Toolbar.capBtnPrintArea": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintTitles": "Tajuk Cetakan", "SSE.Views.Toolbar.capBtnScale": "Skala untuk Muat", "SSE.Views.Toolbar.capImgAlign": "Jajar", "SSE.Views.Toolbar.capImgBackward": "<PERSON><PERSON> ke belakang", "SSE.Views.Toolbar.capImgForward": "Bawa Ke <PERSON>", "SSE.Views.Toolbar.capImgGroup": "Ku<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertChart": "Carta", "SSE.Views.Toolbar.capInsertChartRecommend": "Recommended Chart", "SSE.Views.Toolbar.capInsertEquation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertHyperlink": "Hiperpautan", "SSE.Views.Toolbar.capInsertImage": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertShape": "Bentuk", "SSE.Views.Toolbar.capInsertSpark": "Sparkline", "SSE.Views.Toolbar.capInsertTable": "Jadual", "SSE.Views.Toolbar.capInsertText": "Kotak Teks", "SSE.Views.Toolbar.capInsertTextart": "Lukisan Teks", "SSE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniCapitalizeWords": "Capitalize Each Word", "SSE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.mniImageFromStorage": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.mniImageFromUrl": "<PERSON><PERSON>j daripada URL", "SSE.Views.Toolbar.mniLowerCase": "lowercase", "SSE.Views.Toolbar.mniSentenceCase": "Sentence case.", "SSE.Views.Toolbar.mniToggleCase": "tOGGLE cASE", "SSE.Views.Toolbar.mniUpperCase": "UPPERCASE", "SSE.Views.Toolbar.textAddPrintArea": "Tambah kepada <PERSON>", "SSE.Views.Toolbar.textAlignBottom": "Jajarka<PERSON>", "SSE.Views.Toolbar.textAlignCenter": "Jajarkan <PERSON>", "SSE.Views.Toolbar.textAlignJust": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignMiddle": "Jajarkan <PERSON>", "SSE.Views.Toolbar.textAlignRight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignTop": "Jajarkan <PERSON>", "SSE.Views.Toolbar.textAllBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "SSE.Views.Toolbar.textAuto": "Auto", "SSE.Views.Toolbar.textAutoColor": "Automatik", "SSE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "SSE.Views.Toolbar.textBlackHeart": "Black Heart Suit", "SSE.Views.Toolbar.textBold": "<PERSON><PERSON>", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON>", "SSE.Views.Toolbar.textBordersStyle": "<PERSON><PERSON>", "SSE.Views.Toolbar.textBottom": "Bawah: ", "SSE.Views.Toolbar.textBottomBorders": "Sempadan <PERSON>", "SSE.Views.Toolbar.textBullet": "Bullet", "SSE.Views.Toolbar.textCellAlign": "Format cell alignment", "SSE.Views.Toolbar.textCenterBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textClearPrintArea": "Kosong<PERSON>", "SSE.Views.Toolbar.textClearRule": "<PERSON><PERSON>", "SSE.Views.Toolbar.textClockwise": "Sudut Ikut Arah Jam", "SSE.Views.Toolbar.textColorScales": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textCopyright": "Copyright Sign", "SSE.Views.Toolbar.textCounterCw": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textCustom": "Custom", "SSE.Views.Toolbar.textDataBars": "Bar Data", "SSE.Views.Toolbar.textDegree": "Degree Sign", "SSE.Views.Toolbar.textDelLeft": "Anjakkan sel ke kiri", "SSE.Views.Toolbar.textDelPageBreak": "Remove page break", "SSE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "SSE.Views.Toolbar.textDelUp": "Anjakkan sel ke atas", "SSE.Views.Toolbar.textDiagDownBorder": "Sempadan Ba<PERSON>enju<PERSON>", "SSE.Views.Toolbar.textDiagUpBorder": "Sempadan Atas Pepenjuru", "SSE.Views.Toolbar.textDivision": "Division Sign", "SSE.Views.Toolbar.textDollar": "Dollar Sign", "SSE.Views.Toolbar.textDone": "Siap", "SSE.Views.Toolbar.textDown": "Down", "SSE.Views.Toolbar.textEditVA": "<PERSON>", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textEntireRow": "<PERSON><PERSON><PERSON><PERSON> baris", "SSE.Views.Toolbar.textEuro": "Euro Sign", "SSE.Views.Toolbar.textFewPages": "halaman", "SSE.Views.Toolbar.textFillLeft": "Left", "SSE.Views.Toolbar.textFillRight": "Right", "SSE.Views.Toolbar.textFormatCellFill": "Format cell fill", "SSE.Views.Toolbar.textGreaterEqual": "Greater Than Or Equal To", "SSE.Views.Toolbar.textHeight": "Ketinggian", "SSE.Views.Toolbar.textHideVA": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textHorizontal": "Teks Mel<PERSON>", "SSE.Views.Toolbar.textInfinity": "Infinity", "SSE.Views.Toolbar.textInsDown": "<PERSON><PERSON><PERSON> sel ke bawah", "SSE.Views.Toolbar.textInsideBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textInsPageBreak": "Insert page break", "SSE.Views.Toolbar.textInsRight": "Anjak<PERSON> sel ke kanan", "SSE.Views.Toolbar.textItalic": "Italik", "SSE.Views.Toolbar.textItems": "<PERSON><PERSON>", "SSE.Views.Toolbar.textLandscape": "Lanskap", "SSE.Views.Toolbar.textLeft": "<PERSON><PERSON>: ", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textLessEqual": "Less Than Or Equal To", "SSE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "SSE.Views.Toolbar.textManageRule": "Uruskan Peraturan", "SSE.Views.Toolbar.textManyPages": "halaman", "SSE.Views.Toolbar.textMarginsLast": "Ters<PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsNarrow": "Sempit", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "<PERSON><PERSON>", "SSE.Views.Toolbar.textMiddleBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textMoreBorders": "More borders", "SSE.Views.Toolbar.textMoreFormats": "Format selanjutnya", "SSE.Views.Toolbar.textMorePages": "Halaman <PERSON>", "SSE.Views.Toolbar.textMoreSymbols": "More symbols", "SSE.Views.Toolbar.textNewColor": "Tambah Warna Tersuai Baharu", "SSE.Views.Toolbar.textNewRule": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textNoBorders": "T<PERSON>da sempanan", "SSE.Views.Toolbar.textNotEqualTo": "Not Equal To", "SSE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "SSE.Views.Toolbar.textOnePage": "halaman", "SSE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "SSE.Views.Toolbar.textOutBorders": "Sempadan Luar", "SSE.Views.Toolbar.textPageMarginsCustom": "<PERSON><PERSON>", "SSE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "SSE.Views.Toolbar.textPortrait": "Potret", "SSE.Views.Toolbar.textPrint": "Cetak", "SSE.Views.Toolbar.textPrintGridlines": "<PERSON><PERSON>", "SSE.Views.Toolbar.textPrintHeadings": "Pengepala Cetakan", "SSE.Views.Toolbar.textPrintOptions": "Seting Ce<PERSON>kan", "SSE.Views.Toolbar.textRegistered": "Registered Sign", "SSE.Views.Toolbar.textResetPageBreak": "Reset all page breaks", "SSE.Views.Toolbar.textRight": "Kanan: ", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textRotateDown": "Putar Teks Ke Bawah", "SSE.Views.Toolbar.textRotateUp": "Putar Teks Ke Atas", "SSE.Views.Toolbar.textRtlSheet": "Sheet Right-to-left", "SSE.Views.Toolbar.textScale": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textScaleCustom": "Tersuai", "SSE.Views.Toolbar.textSection": "Section Sign", "SSE.Views.Toolbar.textSelection": "<PERSON><PERSON> p<PERSON>han se<PERSON>a", "SSE.Views.Toolbar.textSeries": "Series", "SSE.Views.Toolbar.textSetPrintArea": "Tetapkan <PERSON>", "SSE.Views.Toolbar.textShapesCombine": "Combine", "SSE.Views.Toolbar.textShapesFragment": "Fragment", "SSE.Views.Toolbar.textShapesIntersect": "Intersect", "SSE.Views.Toolbar.textShapesSubstract": "Subtract", "SSE.Views.Toolbar.textShapesUnion": "Union", "SSE.Views.Toolbar.textShowVA": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSmile": "White Smiling Face", "SSE.Views.Toolbar.textSquareRoot": "Square Root", "SSE.Views.Toolbar.textStrikeout": "<PERSON><PERSON>", "SSE.Views.Toolbar.textSubscript": "Subskrip", "SSE.Views.Toolbar.textSubSuperscript": "Subskrip/superskrip", "SSE.Views.Toolbar.textSuperscript": "Superskrip", "SSE.Views.Toolbar.textTabCollaboration": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabData": "Data", "SSE.Views.Toolbar.textTabDraw": "Draw", "SSE.Views.Toolbar.textTabFile": "Fail", "SSE.Views.Toolbar.textTabFormula": "Formula", "SSE.Views.Toolbar.textTabHome": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTabInsert": "<PERSON>sip<PERSON>", "SSE.Views.Toolbar.textTabLayout": "Talaletak", "SSE.Views.Toolbar.textTabProtect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabView": "Lihat", "SSE.Views.Toolbar.textThisPivot": "Dari pivot ini", "SSE.Views.Toolbar.textThisSheet": "<PERSON><PERSON> lembaran kerja ini", "SSE.Views.Toolbar.textThisTable": "<PERSON><PERSON> jadual ini", "SSE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTop": "Atas: ", "SSE.Views.Toolbar.textTopBorders": "Sempadan Atas", "SSE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "SSE.Views.Toolbar.textUnderline": "<PERSON><PERSON> bawah", "SSE.Views.Toolbar.textUp": "Up", "SSE.Views.Toolbar.textVertical": "<PERSON><PERSON>", "SSE.Views.Toolbar.textWidth": "<PERSON><PERSON>", "SSE.Views.Toolbar.textYen": "Yen Sign", "SSE.Views.Toolbar.textZoom": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignBottom": "<PERSON><PERSON><PERSON><PERSON> bawah", "SSE.Views.Toolbar.tipAlignCenter": "<PERSON><PERSON><PERSON><PERSON> pusat", "SSE.Views.Toolbar.tipAlignJust": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignLeft": "Jajarkan kiri", "SSE.Views.Toolbar.tipAlignMiddle": "Jajarkan tengah", "SSE.Views.Toolbar.tipAlignRight": "<PERSON><PERSON><PERSON><PERSON> kanan", "SSE.Views.Toolbar.tipAlignTop": "Jajarkan atas", "SSE.Views.Toolbar.tipAutofilter": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipBack": "Kembali", "SSE.Views.Toolbar.tipBorders": "Sempadan", "SSE.Views.Toolbar.tipCellStyle": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipChangeCase": "Change case", "SSE.Views.Toolbar.tipChangeChart": "U<PERSON>", "SSE.Views.Toolbar.tipClearStyle": "Kosongkan", "SSE.Views.Toolbar.tipColorSchemas": "<PERSON><PERSON> skema warna", "SSE.Views.Toolbar.tipCondFormat": "Pemformatan bersyarat", "SSE.Views.Toolbar.tipCopy": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipCut": "Potong", "SSE.Views.Toolbar.tipDecDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDecFont": "Pengurangan saiz fon", "SSE.Views.Toolbar.tipDeleteOpt": "Padam Sel", "SSE.Views.Toolbar.tipDigStyleAccounting": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipDigStyleComma": "Comma style", "SSE.Views.Toolbar.tipDigStyleCurrency": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipDigStylePercent": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipEditChart": "<PERSON>", "SSE.Views.Toolbar.tipEditChartData": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipEditChartType": "U<PERSON>", "SSE.Views.Toolbar.tipEditHeader": "<PERSON> pengepala atau pengaki", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON> fon", "SSE.Views.Toolbar.tipFontName": "Fon", "SSE.Views.Toolbar.tipFontSize": "Saiz fon", "SSE.Views.Toolbar.tipHAlighOle": "<PERSON><PERSON><PERSON> melintang", "SSE.Views.Toolbar.tipImgAlign": "Jajarkan objek", "SSE.Views.Toolbar.tipImgGroup": "Kump<PERSON>n objek", "SSE.Views.Toolbar.tipIncDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipIncFont": "Kenaikan saiz font", "SSE.Views.Toolbar.tipInsertChart": "Sisipkan carta", "SSE.Views.Toolbar.tipInsertChartRecommend": "Insert recommended chart", "SSE.Views.Toolbar.tipInsertChartSpark": "Sisipkan carta", "SSE.Views.Toolbar.tipInsertEquation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertHorizontalText": "Insert horizontal text box", "SSE.Views.Toolbar.tipInsertHyperlink": "Tambah hiperpautan", "SSE.Views.Toolbar.tipInsertImage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertOpt": "<PERSON><PERSON><PERSON><PERSON> sel", "SSE.Views.Toolbar.tipInsertShape": "Sisipkan autobentuk", "SSE.Views.Toolbar.tipInsertSlicer": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertSmartArt": "Insert SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Sisip sparkline", "SSE.Views.Toolbar.tipInsertSymbol": "<PERSON><PERSON><PERSON> simbol", "SSE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON> j<PERSON>", "SSE.Views.Toolbar.tipInsertText": "Sisip kotak teks", "SSE.Views.Toolbar.tipInsertTextart": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertVerticalText": "Insert vertical text box", "SSE.Views.Toolbar.tipMerge": "Cantum dan pusat", "SSE.Views.Toolbar.tipNone": "Tiada", "SSE.Views.Toolbar.tipNumFormat": "Format nombor", "SSE.Views.Toolbar.tipPageBreak": "Add a break where you want the next page to begin in the printed copy", "SSE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipPageOrient": "Orientasi ha<PERSON>", "SSE.Views.Toolbar.tipPageSize": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipPaste": "<PERSON>l", "SSE.Views.Toolbar.tipPrColor": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipPrint": "Cetak", "SSE.Views.Toolbar.tipPrintArea": "<PERSON><PERSON><PERSON> ceta<PERSON>", "SSE.Views.Toolbar.tipPrintQuick": "Quick print", "SSE.Views.Toolbar.tipPrintTitles": "Tajuk Cetakan", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipReplace": "Replace", "SSE.Views.Toolbar.tipRtlSheet": "Switch the sheet direction so that the first column is on the right side", "SSE.Views.Toolbar.tipSave": "Simpan", "SSE.Views.Toolbar.tipSaveCoauth": "<PERSON><PERSON><PERSON> per<PERSON> anda untuk pengguna lain melihatnya.", "SSE.Views.Toolbar.tipScale": "Skala untuk Muat", "SSE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSendBackward": "<PERSON><PERSON> ke belakang", "SSE.Views.Toolbar.tipSendForward": "Bawa Ke <PERSON>", "SSE.Views.Toolbar.tipShapesMerge": "Merge shapes", "SSE.Views.Toolbar.tipSynchronize": "Dokumen telah ditukar dengan pengguna yang lain. Sila klik untuk simpan perubahan anda dan muat semula kemas kini.", "SSE.Views.Toolbar.tipTextFormatting": "Lebih teks alat pemformatan", "SSE.Views.Toolbar.tipTextOrientation": "Orientasi", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON>t semula", "SSE.Views.Toolbar.tipVAlighOle": "Jajarka<PERSON>", "SSE.Views.Toolbar.tipVisibleArea": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipWrap": "Balut teks", "SSE.Views.Toolbar.txtAccounting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAdditional": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAscending": "Meningkat", "SSE.Views.Toolbar.txtAutosumTip": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtCellStyle": "Cell Style", "SSE.Views.Toolbar.txtClearAll": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearComments": "Komen", "SSE.Views.Toolbar.txtClearFilter": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFormat": "Format", "SSE.Views.Toolbar.txtClearFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearHyper": "Hiperpautan", "SSE.Views.Toolbar.txtClearText": "Teks", "SSE.Views.Toolbar.txtCurrency": "<PERSON> wang", "SSE.Views.Toolbar.txtCustom": "Tersuai", "SSE.Views.Toolbar.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDateLong": "Long Date", "SSE.Views.Toolbar.txtDateShort": "Short Date", "SSE.Views.Toolbar.txtDateTime": "Tarikh & Masa", "SSE.Views.Toolbar.txtDescending": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDollar": "Dolar $", "SSE.Views.Toolbar.txtEuro": "Euro €", "SSE.Views.Toolbar.txtExp": "Eksponen", "SSE.Views.Toolbar.txtFillNum": "Fill", "SSE.Views.Toolbar.txtFilter": "Penap<PERSON>", "SSE.Views.Toolbar.txtFormula": "<PERSON>si<PERSON><PERSON> fungsi", "SSE.Views.Toolbar.txtFraction": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtFranc": "CHF Swiss franc", "SSE.Views.Toolbar.txtGeneral": "Am", "SSE.Views.Toolbar.txtInteger": "Integer", "SSE.Views.Toolbar.txtManageRange": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtMergeAcross": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtMergeCells": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtMergeCenter": "Cantum & Pusat", "SSE.Views.Toolbar.txtNamedRange": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtNewRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtNoBorders": "T<PERSON>da sempanan", "SSE.Views.Toolbar.txtNumber": "Nombor", "SSE.Views.Toolbar.txtPasteRange": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtPercentage": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtPound": "Paun £", "SSE.Views.Toolbar.txtRouble": "Rouble ₽", "SSE.Views.Toolbar.txtScientific": "Saintifik", "SSE.Views.Toolbar.txtSearch": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtSort": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtSortAZ": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtSortZA": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtSpecial": "Khas", "SSE.Views.Toolbar.txtTableTemplate": "Format sebagai templat jadual", "SSE.Views.Toolbar.txtText": "Teks", "SSE.Views.Toolbar.txtTime": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtUnmerge": "Nyahcantum <PERSON>", "SSE.Views.Toolbar.txtYen": "Yen ¥", "SSE.Views.Top10FilterDialog.textType": "Tunjukkan", "SSE.Views.Top10FilterDialog.txtBottom": "<PERSON>wa<PERSON>", "SSE.Views.Top10FilterDialog.txtBy": "mengikut", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtPercent": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtSum": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtTitle": "Auto Pengisi 10 Teratas", "SSE.Views.Top10FilterDialog.txtTop": "Atas", "SSE.Views.Top10FilterDialog.txtValueTitle": "Pengisi 10 Teratas", "SSE.Views.ValueFieldSettingsDialog.textNext": "(next)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(previous)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Seting <PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "<PERSON><PERSON>as", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "<PERSON>em asas", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 dari %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "<PERSON><PERSON> te<PERSON>i", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Mak", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "<PERSON><PERSON> bagi", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% of grand total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% of parent total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% of parent column total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% of parent row total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% running total in", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Produk", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Rank smallest to largest", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Rank largest to smallest", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "<PERSON><PERSON><PERSON><PERSON><PERSON> nilai se<PERSON>ai", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Nama sumber:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Ju<PERSON>lahkan nilai medan mengikut", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textDelete": "Padam", "SSE.Views.ViewManagerDlg.textDuplicate": "Pendua", "SSE.Views.ViewManagerDlg.textEmpty": "<PERSON><PERSON><PERSON> paparan yang telah dicipta.", "SSE.Views.ViewManagerDlg.textGoTo": "<PERSON><PERSON> ke paparan", "SSE.Views.ViewManagerDlg.textLongName": "<PERSON><PERSON><PERSON><PERSON> nama yang kurang daripada 128 aksara.", "SSE.Views.ViewManagerDlg.textNew": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textRename": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textRenameError": "Paparan nama tidak boleh kosong.", "SSE.Views.ViewManagerDlg.textRenameLabel": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textViews": "<PERSON><PERSON>", "SSE.Views.ViewManagerDlg.tipIsLocked": "Elemen ini sedang diedit oleh pengguna lain.", "SSE.Views.ViewManagerDlg.txtTitle": "<PERSON><PERSON>", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Are you sure you want to delete this sheet view?", "SSE.Views.ViewManagerDlg.warnDeleteView": "Anda sedang cuba untuk memadam '%1' yang kini yang didayakan paparan.<br><PERSON>tu<PERSON> paparan ini dan padam ia?", "SSE.Views.ViewTab.capBtnFreeze": "Bekukan Anak <PERSON>", "SSE.Views.ViewTab.capBtnSheetView": "<PERSON><PERSON>", "SSE.Views.ViewTab.textAlwaysShowToolbar": "<PERSON><PERSON><PERSON> tun<PERSON>n bar alatan", "SSE.Views.ViewTab.textClose": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "<PERSON><PERSON><PERSON> he<PERSON>an dan bar status", "SSE.Views.ViewTab.textCreate": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textDefault": "<PERSON><PERSON>", "SSE.Views.ViewTab.textFill": "Fill", "SSE.Views.ViewTab.textFormula": "Bar formula", "SSE.Views.ViewTab.textFreezeCol": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textFreezeRow": "Bekukan Baris Atas", "SSE.Views.ViewTab.textGridlines": "Garis grid", "SSE.Views.ViewTab.textHeadings": "Pen<PERSON>pal<PERSON>", "SSE.Views.ViewTab.textInterfaceTheme": "<PERSON>ma antara muka", "SSE.Views.ViewTab.textLeftMenu": "Left Panel", "SSE.Views.ViewTab.textLine": "Line", "SSE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "<PERSON><PERSON>", "SSE.Views.ViewTab.textRightMenu": "Right Panel", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Tunjuk Anak Tetingkap Bayang Dibekukan", "SSE.Views.ViewTab.textTabStyle": "Tab style", "SSE.Views.ViewTab.textUnFreeze": "Bebaskan Anak <PERSON>", "SSE.Views.ViewTab.textZeros": "<PERSON><PERSON><PERSON><PERSON><PERSON> sifar", "SSE.Views.ViewTab.textZoom": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipClose": "<PERSON><PERSON>p pandangan he<PERSON>an", "SSE.Views.ViewTab.tipCreate": "<PERSON><PERSON><PERSON> paparan <PERSON>an", "SSE.Views.ViewTab.tipFreeze": "Bekukan Anak <PERSON>", "SSE.Views.ViewTab.tipInterfaceTheme": "<PERSON>ma antara muka", "SSE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipSheetView": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipViewNormal": "See your document in Normal view", "SSE.Views.ViewTab.tipViewPageBreak": "See where the page breaks will appear when your document is printed", "SSE.Views.ViewTab.txtViewNormal": "Normal", "SSE.Views.ViewTab.txtViewPageBreak": "Page Break Preview", "SSE.Views.WatchDialog.closeButtonText": "Close", "SSE.Views.WatchDialog.textAdd": "Add watch", "SSE.Views.WatchDialog.textBook": "Book", "SSE.Views.WatchDialog.textCell": "Cell", "SSE.Views.WatchDialog.textDelete": "Delete watch", "SSE.Views.WatchDialog.textDeleteAll": "Delete all", "SSE.Views.WatchDialog.textFormula": "Formula", "SSE.Views.WatchDialog.textName": "Name", "SSE.Views.WatchDialog.textSheet": "Sheet", "SSE.Views.WatchDialog.textValue": "Value", "SSE.Views.WatchDialog.txtTitle": "Watch window", "SSE.Views.WBProtection.hintAllowRanges": "<PERSON><PERSON><PERSON> edit julat", "SSE.Views.WBProtection.hintProtectRange": "Protect range", "SSE.Views.WBProtection.hintProtectSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.hintProtectWB": "<PERSON><PERSON><PERSON> buku kerja", "SSE.Views.WBProtection.txtAllowRanges": "Benarkan Edit <PERSON>", "SSE.Views.WBProtection.txtHiddenFormula": "Formula Tersembunyi", "SSE.Views.WBProtection.txtLockedCell": "<PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedShape": "Bentuk Dikunci", "SSE.Views.WBProtection.txtLockedText": "Teks Di<PERSON>nci", "SSE.Views.WBProtection.txtProtectRange": "Protect Range", "SSE.Views.WBProtection.txtProtectSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtProtectWB": "<PERSON><PERSON><PERSON> buku kerja", "SSE.Views.WBProtection.txtSheetUnlockDescription": "<PERSON><PERSON><PERSON><PERSON> kata laluan untuk melindungi helaian", "SSE.Views.WBProtection.txtSheetUnlockTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtWBUnlockDescription": "<PERSON><PERSON><PERSON><PERSON> kata laluan untuk melindungi buku kerja", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}