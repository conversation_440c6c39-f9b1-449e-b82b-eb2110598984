{"cancelButtonText": "Скасаваць", "Common.Controllers.Chat.notcriticalErrorTitle": "Увага", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.History.notcriticalErrorTitle": "Увага", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "Вобласць", "Common.define.chartData.textAreaStacked": "Дыяграма з абласцямі і накапленнем", "Common.define.chartData.textAreaStackedPer": "З абласцямі і поўным накапленнем", "Common.define.chartData.textBar": "Лінейчастая", "Common.define.chartData.textBarNormal": "Слупкі з групаваннем", "Common.define.chartData.textBarNormal3d": "Трохвымерная гістаграма з групаваннем", "Common.define.chartData.textBarNormal3dPerspective": "Трохвымерная гістаграма", "Common.define.chartData.textBarStacked": "Гістаграма з накапленнем", "Common.define.chartData.textBarStacked3d": "Трохвымерная састаўная гістаграма", "Common.define.chartData.textBarStackedPer": "Поўная гістаграма з накапленнем", "Common.define.chartData.textBarStackedPer3d": "Трохвымерная састаўная гістаграма з поўным накапленнем", "Common.define.chartData.textCharts": "Дыяграмы", "Common.define.chartData.textColumn": "Гістаграма", "Common.define.chartData.textColumnSpark": "Гістаграма", "Common.define.chartData.textCombo": "Камбінаваныя", "Common.define.chartData.textComboAreaBar": "Вобласці з накапленнем і слупкі з групаваннем", "Common.define.chartData.textComboBarLine": "Слупкі з групаваннем і лініі", "Common.define.chartData.textComboBarLineSecondary": "Слупкі з групаваннем і лініі на другаснай восі", "Common.define.chartData.textComboCustom": "Адвольная камбінацыя", "Common.define.chartData.textDoughnut": "Дыяграма ў выглядзе кола", "Common.define.chartData.textHBarNormal": "Лінейчастая з групаваннем", "Common.define.chartData.textHBarNormal3d": "Трохвымерная лінейная з групаваннем", "Common.define.chartData.textHBarStacked": "Лінейчастая з накапленнем", "Common.define.chartData.textHBarStacked3d": "Трохвымерная лінейчастая з накапленнем", "Common.define.chartData.textHBarStackedPer": "Поўная лінейчастая з накапленнем", "Common.define.chartData.textHBarStackedPer3d": "Трохвымерная лінейчастая з поўным накапленнем", "Common.define.chartData.textLine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textLine3d": "Трохвымерны графік", "Common.define.chartData.textLineMarker": "Гра<PERSON><PERSON><PERSON> з адзнакамі", "Common.define.chartData.textLineSpark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textLineStacked": "Графік з накапленнем", "Common.define.chartData.textLineStackedMarker": "Графік з накапленнем і адзнакамі", "Common.define.chartData.textLineStackedPer": "Графік з поўным накапленнем", "Common.define.chartData.textLineStackedPerMarker": "Графік з адзнакамі і поўным накапленнем", "Common.define.chartData.textPie": "Кругавая", "Common.define.chartData.textPie3d": "Трохвымерная кругавая", "Common.define.chartData.textPoint": "XY (рассеяная)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Filled radar", "Common.define.chartData.textRadarMarker": "Radar with markers", "Common.define.chartData.textScatter": "Кропкавая дыяграма", "Common.define.chartData.textScatterLine": "Кропкавая дыяграма з прамымі лініямі", "Common.define.chartData.textScatterLineMarker": "Кропкавая дыяграма з прамымі лініямі і адзнакамі", "Common.define.chartData.textScatterSmooth": "Кропкавая дыяграма з плаўнымі лініямі", "Common.define.chartData.textScatterSmoothMarker": "Кропкавая дыяграма з плаўнымі лініямі і адзнакамі", "Common.define.chartData.textSparks": "Спарклайны", "Common.define.chartData.textStock": "Біржа", "Common.define.chartData.textSurface": "Паверхня", "Common.define.chartData.textWinLossSpark": "Выйгрыш/параза", "Common.define.conditionalData.exampleText": "AaBbБбЯя", "Common.define.conditionalData.noFormatText": "Фармат не вызначаны", "Common.define.conditionalData.text1Above": "На адно стандартнае адхіленне вышэй", "Common.define.conditionalData.text1Below": "На адно стандартнае адхіленне ніжэй", "Common.define.conditionalData.text2Above": "На два стандартных адхілення вышэй", "Common.define.conditionalData.text2Below": "На два стандартных адхілення ніжэй", "Common.define.conditionalData.text3Above": "На тры стандартных адхілення вышэй", "Common.define.conditionalData.text3Below": "На тры стандартных адхілення ніжэй", "Common.define.conditionalData.textAbove": "вышэй", "Common.define.conditionalData.textAverage": "Сярэдняе", "Common.define.conditionalData.textBegins": "Пачынаецца з", "Common.define.conditionalData.textBelow": "ніж<PERSON>й", "Common.define.conditionalData.textBetween": "паміж", "Common.define.conditionalData.textBlank": "Пусты", "Common.define.conditionalData.textBlanks": "Змяшчае пустыя ячэйкі", "Common.define.conditionalData.textBottom": "Ніжняе", "Common.define.conditionalData.textContains": "Змяшчае", "Common.define.conditionalData.textDataBar": "Гістаграма", "Common.define.conditionalData.textDate": "Дата", "Common.define.conditionalData.textDuplicate": "Дублююцца", "Common.define.conditionalData.textEnds": "Заканчваецца на", "Common.define.conditionalData.textEqAbove": "Роўна або больш", "Common.define.conditionalData.textEqBelow": "Роўна або менш", "Common.define.conditionalData.textEqual": "Роўна", "Common.define.conditionalData.textError": "Памылка", "Common.define.conditionalData.textErrors": "Змяшчае памылкі", "Common.define.conditionalData.textFormula": "Формула", "Common.define.conditionalData.textGreater": "<PERSON>о<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textGreaterEq": "Больш альбо роўна", "Common.define.conditionalData.textIconSets": "Наборы значкоў", "Common.define.conditionalData.textLast7days": "За апошнія 7 дзён", "Common.define.conditionalData.textLastMonth": "Мінулы месяц", "Common.define.conditionalData.textLastWeek": "Мінулы тыдзень", "Common.define.conditionalData.textLess": "Ме<PERSON><PERSON> за", "Common.define.conditionalData.textLessEq": "Мен<PERSON> альбо роўна", "Common.define.conditionalData.textNextMonth": "Наступны месяц", "Common.define.conditionalData.textNextWeek": "Наступны тыдзень", "Common.define.conditionalData.textNotBetween": "Не паміж", "Common.define.conditionalData.textNotBlanks": "Не змяшчае пустыя ячэйкі", "Common.define.conditionalData.textNotContains": "Не змяшчае", "Common.define.conditionalData.textNotEqual": "Не роўна", "Common.define.conditionalData.textNotErrors": "Не змяшчае памылкі", "Common.define.conditionalData.textText": "Тэкст", "Common.define.conditionalData.textThisMonth": "Гэты месяц", "Common.define.conditionalData.textThisWeek": "Гэты тыдзень", "Common.define.conditionalData.textToday": "Сёння", "Common.define.conditionalData.textTomorrow": "Заўтра", "Common.define.conditionalData.textTop": "Найбольшыя", "Common.define.conditionalData.textUnique": "Унікальнае", "Common.define.conditionalData.textValue": "Значэнне -", "Common.define.conditionalData.textYesterday": "Учора", "Common.define.smartArt.textAccentedPicture": "Акцэнтаваная выява", "Common.define.smartArt.textAccentProcess": "Акцэнтаваны працэс", "Common.define.smartArt.textAlternatingFlow": "Пераменная плынь", "Common.define.smartArt.textAlternatingHexagons": "Чаргаванне шасцівугольнікаў", "Common.define.smartArt.textAlternatingPictureBlocks": "Чаргаванне блокаў выяў", "Common.define.smartArt.textAlternatingPictureCircles": "Чаргаванне колаў выяў", "Common.define.smartArt.textArchitectureLayout": "Архітэктурны макет", "Common.define.smartArt.textArrowRibbon": "Стужка са стрэлкамі", "Common.define.smartArt.textAscendingPictureAccentProcess": "Акцэнтаваны працэс з малюнкамі па павелічэнні", "Common.define.smartArt.textBalance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicBendingProcess": "Базавы нераўнамерны працэс", "Common.define.smartArt.textBasicBlockList": "Базавы блокавы спіс", "Common.define.smartArt.textBasicChevronProcess": "Базавы працэс вуглавых дужак", "Common.define.smartArt.textBasicCycle": "Базавы цыкл", "Common.define.smartArt.textBasicMatrix": "Базавая матрыца", "Common.define.smartArt.textBasicPie": "Базавая кругавая", "Common.define.smartArt.textBasicProcess": "Базавы працэс", "Common.define.smartArt.textBasicPyramid": "Базавая піраміда", "Common.define.smartArt.textBasicRadial": "Базавая радыяльная", "Common.define.smartArt.textBasicTarget": "Базавая мэтавая", "Common.define.smartArt.textBasicTimeline": "Базавая шкала часу", "Common.define.smartArt.textBasicVenn": "Базавая дыяграма Вэна", "Common.define.smartArt.textBendingPictureAccentList": "Нераўнамерны спіс з акцэнтаванымі малюнкамі", "Common.define.smartArt.textBendingPictureBlocks": "Нераўнамерныя малюнкі з блокамі", "Common.define.smartArt.textBendingPictureCaption": "Нераўнамерныя подпісы малюнкаў", "Common.define.smartArt.textBendingPictureCaptionList": "Нераўнамерны спіс малюнкаў з подпісамі", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Нераўнамерны спіс малюнкаў з паўпразрыстым тэкстам", "Common.define.smartArt.textBlockCycle": "Блокавы цыкл", "Common.define.smartArt.textBubblePictureList": "Спіс малюнкаў са зноскамі", "Common.define.smartArt.textCaptionedPictures": "Падпісаныя малюнкі", "Common.define.smartArt.textChevronAccentProcess": "Акцэнтаваны працэс вуглавых дужак", "Common.define.smartArt.textChevronList": "Спіс з вуглавымі дужкамі", "Common.define.smartArt.textCircleAccentTimeline": "Круглая шкала часу", "Common.define.smartArt.textCircleArrowProcess": "Кругавы працэс са стрэлкамі", "Common.define.smartArt.textCirclePictureHierarchy": "Іерархія з круглымі малюнкамі", "Common.define.smartArt.textCircleProcess": "Працэс з кругамі", "Common.define.smartArt.textCircleRelationship": "Кола сувязі", "Common.define.smartArt.textCircularBendingProcess": "Круглы нераўнамерны працэс", "Common.define.smartArt.textCircularPictureCallout": "Зноска з круглымі малюнкамі", "Common.define.smartArt.textClosedChevronProcess": "Працэс з закрывальнымі вуглавымі дужкамі", "Common.define.smartArt.textContinuousArrowProcess": "Бесперапынны працэс са стрэлкамі", "Common.define.smartArt.textContinuousBlockProcess": "Бесперапынны блокавы працэс", "Common.define.smartArt.textContinuousCycle": "Бесперапынны цыкл", "Common.define.smartArt.textContinuousPictureList": "Бесперапынны спіс з малюнкамі", "Common.define.smartArt.textConvergingArrows": "Збежныя стрэлкі", "Common.define.smartArt.textConvergingRadial": "Збежныя радыяльныя", "Common.define.smartArt.textConvergingText": "Збежны тэкст", "Common.define.smartArt.textCounterbalanceArrows": "Раўнаважныя стрэлкі", "Common.define.smartArt.textCycle": "Цыкл", "Common.define.smartArt.textCycleMatrix": "Цыклічная матрыца", "Common.define.smartArt.textDescendingBlockList": "Сыходны блокавы спіс", "Common.define.smartArt.textDescendingProcess": "Сыходны працэс", "Common.define.smartArt.textDetailedProcess": "Падрабязны працэс", "Common.define.smartArt.textDivergingArrows": "Разбежныя стрэлкі", "Common.define.smartArt.textDivergingRadial": "Разбежныя радыяльныя", "Common.define.smartArt.textEquation": "Раўнанне", "Common.define.smartArt.textFramedTextPicture": "Малюнак з тэкстам у рамцы", "Common.define.smartArt.textFunnel": "Варонка", "Common.define.smartArt.textGear": "Шасцярня", "Common.define.smartArt.textGridMatrix": "Сеткаватая матрыца", "Common.define.smartArt.textGroupedList": "Згрупаваны спіс", "Common.define.smartArt.textHalfCircleOrganizationChart": "Паўкруглая арганізацыйная дыяграма", "Common.define.smartArt.textHexagonCluster": "Шасцівугольны кластар", "Common.define.smartArt.textHexagonRadial": "Радыяльны шасцівугольнік", "Common.define.smartArt.textHierarchy": "Іерархія", "Common.define.smartArt.textHierarchyList": "Іерархічны спіс", "Common.define.smartArt.textHorizontalBulletList": "Гарызантальны спіс з адзнакамі", "Common.define.smartArt.textHorizontalHierarchy": "Гарызантальная іерархія", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Гарызантальная іерархія з адмецінамі", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Гарызантальная шматузроўневая іерархія", "Common.define.smartArt.textHorizontalOrganizationChart": "Гарызантальная арганізацыйная дыяграма", "Common.define.smartArt.textHorizontalPictureList": "Гарызантальны спіс малюнкаў", "Common.define.smartArt.textIncreasingArrowProcess": "Стрэлка працэсу павелічэння", "Common.define.smartArt.textIncreasingCircleProcess": "Кола працэсу павелічэння", "Common.define.smartArt.textInterconnectedBlockProcess": "Працэс з узаемазвязанымі блокамі", "Common.define.smartArt.textInterconnectedRings": "Узаемазвязаныя колы", "Common.define.smartArt.textInvertedPyramid": "Інвертаваная піраміда", "Common.define.smartArt.textLabeledHierarchy": "Іерархія з подпісамі", "Common.define.smartArt.textLinearVenn": "Линейная дыяграма Вэна", "Common.define.smartArt.textLinedList": "Спіс з лініямі", "Common.define.smartArt.textList": "Сп<PERSON>с", "Common.define.smartArt.textMatrix": "Матрыца", "Common.define.smartArt.textMultidirectionalCycle": "Цыкл з некалькімі кірункамі", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Арганізацыйная дыяграма з імёнамі і пасадамі", "Common.define.smartArt.textNestedTarget": "Укладзеная мэта", "Common.define.smartArt.textNondirectionalCycle": "Цыкл без кірунку", "Common.define.smartArt.textOpposingArrows": "Супрацьлеглыя стрэлкі", "Common.define.smartArt.textOpposingIdeas": "Супрацьлеглыя ідэі", "Common.define.smartArt.textOrganizationChart": "Арганізацыйная дыяграма", "Common.define.smartArt.textOther": "Іншае", "Common.define.smartArt.textPhasedProcess": "Паэтапны працэс", "Common.define.smartArt.textPicture": "Малюнак", "Common.define.smartArt.textPictureAccentBlocks": "Блокі з акцэнтаванымі малюнкамі", "Common.define.smartArt.textPictureAccentList": "Спіс з акцэнтаванымі малюнкамі", "Common.define.smartArt.textPictureAccentProcess": "Працэс з акцэнтаванымі малюнкамі", "Common.define.smartArt.textPictureCaptionList": "Спіс подпісаў да малюнкаў", "Common.define.smartArt.textPictureFrame": "Фотарамка", "Common.define.smartArt.textPictureGrid": "Сетка малюнкаў", "Common.define.smartArt.textPictureLineup": "Лінія малюнкаў", "Common.define.smartArt.textPictureOrganizationChart": "Арганізацыйная дыяграма з малюнкамі", "Common.define.smartArt.textPictureStrips": "Палоскі малюнкаў", "Common.define.smartArt.textPieProcess": "Працэс з кругавой дыяграмай", "Common.define.smartArt.textPlusAndMinus": "Плюс і мінус", "Common.define.smartArt.textProcess": "Працэс", "Common.define.smartArt.textProcessArrows": "Стрэлкі працэсу", "Common.define.smartArt.textProcessList": "Спіс працэсаў", "Common.define.smartArt.textPyramid": "Піраміда", "Common.define.smartArt.textPyramidList": "Пірамідальны спіс", "Common.define.smartArt.textRadialCluster": "Радыяльны кластар", "Common.define.smartArt.textRadialCycle": "Радыяльны цыкл", "Common.define.smartArt.textRadialList": "Радыяльны спіс", "Common.define.smartArt.textRadialPictureList": "Радыяльны спіс малюнкаў", "Common.define.smartArt.textRadialVenn": "Радыяльная дыяграма Вэна", "Common.define.smartArt.textRandomToResultProcess": "Працэс ад выпадковага да выніку", "Common.define.smartArt.textRelationship": "Сувязь", "Common.define.smartArt.textRepeatingBendingProcess": "Цыклічны нераўнамерны працэс", "Common.define.smartArt.textReverseList": "Адваротны спіс", "Common.define.smartArt.textSegmentedCycle": "Сегментаваны цыкл", "Common.define.smartArt.textSegmentedProcess": "Сегментаваны працэс", "Common.define.smartArt.textSegmentedPyramid": "Сегментаваная піраміда", "Common.define.smartArt.textSnapshotPictureList": "Спіс са здымкамі", "Common.define.smartArt.textSpiralPicture": "Спіраль малюнкаў", "Common.define.smartArt.textSquareAccentList": "Спіс з квадратамі", "Common.define.smartArt.textStackedList": "Складаны спіс", "Common.define.smartArt.textStackedVenn": "Складаная дыяграма Вэна", "Common.define.smartArt.textStaggeredProcess": "Паэтапны працэс", "Common.define.smartArt.textStepDownProcess": "Сыходны працэс", "Common.define.smartArt.textStepUpProcess": "Узыходны працэс", "Common.define.smartArt.textSubStepProcess": "Пакрокавы працэс", "Common.define.smartArt.textTabbedArc": "Дуга з укладкамі", "Common.define.smartArt.textTableHierarchy": "Таблічная іерархія", "Common.define.smartArt.textTableList": "Таблічны спіс", "Common.define.smartArt.textTabList": "Спіс укладак", "Common.define.smartArt.textTargetList": "Мэтавы спіс", "Common.define.smartArt.textTextCycle": "Тэкставы цыкл", "Common.define.smartArt.textThemePictureAccent": "Акцэнтаваныя малюнкі тэмы", "Common.define.smartArt.textThemePictureAlternatingAccent": "Акцэнтаваныя малюнкі тэмы, якія чаргуюцца", "Common.define.smartArt.textThemePictureGrid": "Сетка малюнкаў тэмы", "Common.define.smartArt.textTitledMatrix": "Матрыца з загалоўкамі", "Common.define.smartArt.textTitledPictureAccentList": "Спіс з акцэнтаванымі малюнкамі і загалоўкамі", "Common.define.smartArt.textTitledPictureBlocks": "Блокі малюнкаў з назвамі", "Common.define.smartArt.textTitlePictureLineup": "Лінія малюнкаў з назвамі", "Common.define.smartArt.textTrapezoidList": "Спіс у выглядзе трапецыі", "Common.define.smartArt.textUpwardArrow": "Узыходная стрэлка", "Common.define.smartArt.textVaryingWidthList": "Спіс са зменнай шырынёй", "Common.define.smartArt.textVerticalAccentList": "Вертыкальны акцэнтаваны спіс", "Common.define.smartArt.textVerticalArrowList": "Вертыкальны спіс са стрэлкай", "Common.define.smartArt.textVerticalBendingProcess": "Вертыкальны нераўнамерны працэс", "Common.define.smartArt.textVerticalBlockList": "Вертыкальны блокавы спіс", "Common.define.smartArt.textVerticalBoxList": "Вертыкальны спіс", "Common.define.smartArt.textVerticalBracketList": "Вертыкальны спіс з дужкамі", "Common.define.smartArt.textVerticalBulletList": "Вертыкальны спіс з адзнакамі", "Common.define.smartArt.textVerticalChevronList": "Вертыкальны спіс з вуглавымі дужкамі", "Common.define.smartArt.textVerticalCircleList": "Вертыкальны спіс з кругамі", "Common.define.smartArt.textVerticalCurvedList": "Вертыкальны крывы спіс", "Common.define.smartArt.textVerticalEquation": "Вертыкальнае раўнанне", "Common.define.smartArt.textVerticalPictureAccentList": "Вертыкальны спіс з акцэнтаванымі малюнкамі", "Common.define.smartArt.textVerticalPictureList": "Вертыкальны спіс малюнкаў", "Common.define.smartArt.textVerticalProcess": "Вертыкальны працэс", "Common.Translation.textMoreButton": "Яшчэ", "Common.Translation.tipFileLocked": "Дакумент заблакаваны для рэдагавання. Вы можаце змяніць яго і захаваць як лакальную копію.", "Common.Translation.tipFileReadOnly": "Файл даступны толькі для чытання. Каб захаваць змены, захавайце файл пад новай назвай або ў іншым месцы.", "Common.Translation.warnFileLocked": "Дакумент выкарыстоўваецца іншай праграмай. Вы можаце працягнуць рэдагаванне і захаваць яго як копію.", "Common.Translation.warnFileLockedBtnEdit": "Стварыць копію", "Common.Translation.warnFileLockedBtnView": "Адкрыць для прагляду", "Common.UI.ButtonColored.textAutoColor": "Аўтаматычна", "Common.UI.ButtonColored.textEyedropper": "Eyedropper", "Common.UI.ButtonColored.textNewColor": "Яшчэ колеры", "Common.UI.ComboBorderSize.txtNoBorders": "Без межаў", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Без межаў", "Common.UI.ComboDataView.emptyComboText": "Без стыляў", "Common.UI.ExtendedColorDialog.addButtonText": "Дада<PERSON>ь", "Common.UI.ExtendedColorDialog.textCurrent": "Бягучы", "Common.UI.ExtendedColorDialog.textHexErr": "Уведзена хібнае значэнне.<br>Кал<PERSON> ласка, ўвядзіце значэнне ад 000000 да FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Новы", "Common.UI.ExtendedColorDialog.textRGBErr": "Уведзена хібнае значэнне.<br>Кал<PERSON> ласка, ўвядзіце лік ад 0 да 255.", "Common.UI.HSBColorPicker.textNoColor": "Без колеру", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Схаваць пароль", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Паказваць пароль", "Common.UI.SearchBar.textFind": "По<PERSON><PERSON>к", "Common.UI.SearchBar.tipCloseSearch": "Закрыць пошук", "Common.UI.SearchBar.tipNextResult": "Наступны вынік", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Адкрыць дадатковыя налады", "Common.UI.SearchBar.tipPreviousResult": "Папярэдні вынік", "Common.UI.SearchDialog.textHighlight": "Падсвятліць вынікі", "Common.UI.SearchDialog.textMatchCase": "Улічваць рэгістр", "Common.UI.SearchDialog.textReplaceDef": "Увядзіце тэкст для замены", "Common.UI.SearchDialog.textSearchStart": "Увядзіце сюды тэкст", "Common.UI.SearchDialog.textTitle": "Пошук і замена", "Common.UI.SearchDialog.textTitle2": "По<PERSON><PERSON>к", "Common.UI.SearchDialog.textWholeWords": "Толькі слова цалкам", "Common.UI.SearchDialog.txtBtnHideReplace": "Схаваць замену", "Common.UI.SearchDialog.txtBtnReplace": "Замяніць", "Common.UI.SearchDialog.txtBtnReplaceAll": "Замяніць усе", "Common.UI.SynchronizeTip.textDontShow": "Больш не паказваць гэтае паведамленне", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "Дакумент быў зменены іншым карыстальнікам.<br>Націсніце, каб захаваць свае змены і загрузіць абнаўленні.", "Common.UI.ThemeColorPalette.textRecentColors": " Нядаўнія колеры", "Common.UI.ThemeColorPalette.textStandartColors": "Стандартныя колеры", "Common.UI.ThemeColorPalette.textThemeColors": "Колеры тэмы", "Common.UI.Themes.txtThemeClassicLight": "Класічная светлая", "Common.UI.Themes.txtThemeContrastDark": "Кантрасная цёмная", "Common.UI.Themes.txtThemeDark": "Цёмная", "Common.UI.Themes.txtThemeGray": "Серая", "Common.UI.Themes.txtThemeLight": "Светлая", "Common.UI.Themes.txtThemeSystem": "Сістэмная", "Common.UI.Window.cancelButtonText": "Скасаваць", "Common.UI.Window.closeButtonText": "Закрыць", "Common.UI.Window.noButtonText": "Не", "Common.UI.Window.okButtonText": "Добра", "Common.UI.Window.textConfirmation": "Пацвярджэнне", "Common.UI.Window.textDontShow": "Больш не паказваць гэтае паведамленне", "Common.UI.Window.textError": "Памылка", "Common.UI.Window.textInformation": "Інфармацыя", "Common.UI.Window.textWarning": "Увага", "Common.UI.Window.yesButtonText": "Так", "Common.Utils.Metric.txtCm": "см", "Common.Utils.Metric.txtPt": "пт", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Акцэнт", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "Фон", "Common.Utils.ThemeColor.txtBlack": "Black", "Common.Utils.ThemeColor.txtBlue": "Blue", "Common.Utils.ThemeColor.txtBrightGreen": "Bright green", "Common.Utils.ThemeColor.txtBrown": "<PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Dark blue", "Common.Utils.ThemeColor.txtDarker": "Darker", "Common.Utils.ThemeColor.txtDarkGray": "Dark gray", "Common.Utils.ThemeColor.txtDarkGreen": "Dark green", "Common.Utils.ThemeColor.txtDarkPurple": "Dark purple", "Common.Utils.ThemeColor.txtDarkRed": "Dark red", "Common.Utils.ThemeColor.txtDarkTeal": "Dark teal", "Common.Utils.ThemeColor.txtDarkYellow": "Dark yellow", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "Серая", "Common.Utils.ThemeColor.txtGreen": "Green", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "Light blue", "Common.Utils.ThemeColor.txtLighter": "Lighter", "Common.Utils.ThemeColor.txtLightGray": "Light gray", "Common.Utils.ThemeColor.txtLightGreen": "Light green", "Common.Utils.ThemeColor.txtLightOrange": "Light orange", "Common.Utils.ThemeColor.txtLightYellow": "Light yellow", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "Pink", "Common.Utils.ThemeColor.txtPurple": "Purple", "Common.Utils.ThemeColor.txtRed": "Red", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Sky blue", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Тэкст", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "White", "Common.Utils.ThemeColor.txtYellow": "Yellow", "Common.Views.About.txtAddress": "адрас:", "Common.Views.About.txtLicensee": "ЛІЦЭНЗІЯТ", "Common.Views.About.txtLicensor": "ЛІЦЭНЗІЯР", "Common.Views.About.txtMail": "адрас электроннай пошты:", "Common.Views.About.txtPoweredBy": "Распрацавана", "Common.Views.About.txtTel": "тэл.:", "Common.Views.About.txtVersion": "Версія", "Common.Views.AutoCorrectDialog.textAdd": "Дада<PERSON>ь", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Ужываць падчас працы", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Аўтазамена", "Common.Views.AutoCorrectDialog.textAutoFormat": "Аўтафарматаванне падчас уводу", "Common.Views.AutoCorrectDialog.textBy": "На", "Common.Views.AutoCorrectDialog.textDelete": "Выдаліць", "Common.Views.AutoCorrectDialog.textHyperlink": "Сеціўныя і сеткавыя шляхі з гіперспасылкамі", "Common.Views.AutoCorrectDialog.textMathCorrect": "Аўтазамена матэматычнымі сімваламі", "Common.Views.AutoCorrectDialog.textNewRowCol": "Уключаць у табліцу новыя радкі і слупкі", "Common.Views.AutoCorrectDialog.textRecognized": "Распазнаныя формулы", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Гэтыя выразы распазнаныя як матэматычныя. Яны не будуць пазначацца курсівам.", "Common.Views.AutoCorrectDialog.textReplace": "Замяніць", "Common.Views.AutoCorrectDialog.textReplaceText": "Замяняць падчас уводу", "Common.Views.AutoCorrectDialog.textReplaceType": "Замяніць тэкст падчас уводу", "Common.Views.AutoCorrectDialog.textReset": "Скінуць", "Common.Views.AutoCorrectDialog.textResetAll": "Скінуць да прадвызначанага", "Common.Views.AutoCorrectDialog.textRestore": "Аднавіць", "Common.Views.AutoCorrectDialog.textTitle": "Аўтазамена", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Распазнаныя формулы могуць змяшчаць толькі вялікія і малыя літары ад А да Я.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Любы дададзены вамі выраз будзе выдалены, а значэнні вернуцца да прадвызначаных. Хочаце працягнуць?", "Common.Views.AutoCorrectDialog.warnReplace": "Аўтазамена для %1 ужо існуе. Хочаце замяніць яе?", "Common.Views.AutoCorrectDialog.warnReset": "Любая дададзеная вамі аўтазамена будзе выдаленая, а значэнні вернуцца да прадвызначаных. Хочаце працягнуць?", "Common.Views.AutoCorrectDialog.warnRestore": "Аўтазамена для %1 скінутая да прадвызначанага значэння. Хочаце працягнуць?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "Адправіць", "Common.Views.Comments.mniAuthorAsc": "Аўтары ад А да Я", "Common.Views.Comments.mniAuthorDesc": "Аўтары ад Я да А", "Common.Views.Comments.mniDateAsc": "Спачатку старыя", "Common.Views.Comments.mniDateDesc": "Спачатку новыя", "Common.Views.Comments.mniFilterGroups": "Фільтраваць па групе", "Common.Views.Comments.mniPositionAsc": "Зверху ўніз", "Common.Views.Comments.mniPositionDesc": "Знізу ўверх", "Common.Views.Comments.textAdd": "Дада<PERSON>ь", "Common.Views.Comments.textAddComment": "Да<PERSON><PERSON><PERSON>ь каментар", "Common.Views.Comments.textAddCommentToDoc": "Дада<PERSON>ь каментар да дакумента", "Common.Views.Comments.textAddReply": "Дада<PERSON>ь адказ", "Common.Views.Comments.textAll": "Усе", "Common.Views.Comments.textAnonym": "Госць", "Common.Views.Comments.textCancel": "Скасаваць", "Common.Views.Comments.textClose": "Закрыць", "Common.Views.Comments.textClosePanel": "Закрыць каментары", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "Каментары", "Common.Views.Comments.textEdit": "Добра", "Common.Views.Comments.textEnterCommentHint": "Увядзіце сюды свой каментар", "Common.Views.Comments.textHintAddComment": "Да<PERSON><PERSON><PERSON>ь каментар", "Common.Views.Comments.textOpenAgain": "Адкрыць зноў", "Common.Views.Comments.textReply": "Адказаць", "Common.Views.Comments.textResolve": "Вырашыць", "Common.Views.Comments.textResolved": "Вырашана", "Common.Views.Comments.textSort": "Сартаваць каментары", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "У вас няма правоў на паўторнае адкрыццё каментара", "Common.Views.Comments.txtEmpty": "У аркушы няма каментароў.", "Common.Views.CopyWarningDialog.textDontShow": "Больш не паказваць гэтае паведамленне", "Common.Views.CopyWarningDialog.textMsg": "Аперацыі капіявання, выразання і ўстаўляння можна выканаць пры дапамозе кнопак на панэлі інструментаў і загадаў кантэкстнага меню толькі ў гэтай укладцы рэдактара.<br><br>Для ўзаемадзеяння з іншымі праграмамі выкарыстоўвайце наступныя спалучэнні клавіш:", "Common.Views.CopyWarningDialog.textTitle": "Дзеянні капіявання, выразання, устаўляння", "Common.Views.CopyWarningDialog.textToCopy": "для капіявання", "Common.Views.CopyWarningDialog.textToCut": "для выразання", "Common.Views.CopyWarningDialog.textToPaste": "для ўстаўляння", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "Загрузка…", "Common.Views.DocumentAccessDialog.textTitle": "Налады супольнага доступу", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "Eraser", "Common.Views.Draw.hintSelect": "Абраць", "Common.Views.Draw.txtEraser": "Eraser", "Common.Views.Draw.txtHighlighter": "Highlighter", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Pen", "Common.Views.Draw.txtSelect": "Абраць", "Common.Views.Draw.txtSize": "<PERSON>а<PERSON><PERSON><PERSON>", "Common.Views.EditNameDialog.textLabel": "Адмеціна:", "Common.Views.EditNameDialog.textLabelError": "Адмеціна не можа быць пустой", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "Дакумент рэдагуецца карыстальнікамі:", "Common.Views.Header.textAddFavorite": "Дадаць ва ўлюбёнае", "Common.Views.Header.textAdvSettings": "Дадатковыя налады", "Common.Views.Header.textBack": "Перайсці да дакументаў", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "Схаваць панэль прылад", "Common.Views.Header.textHideLines": "Схаваць лінейкі", "Common.Views.Header.textHideStatusBar": "Аб'яднаць панэлі радкоў і стану", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "Толькі чытанне", "Common.Views.Header.textRemoveFavorite": "Выдаліць з улюбёнага", "Common.Views.Header.textSaveBegin": "Захаванне…", "Common.Views.Header.textSaveChanged": "Зменена", "Common.Views.Header.textSaveEnd": "Усе змены захаваныя", "Common.Views.Header.textSaveExpander": "Усе змены захаваныя", "Common.Views.Header.textShare": "Падзяліцца", "Common.Views.Header.textZoom": "Ма<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipAccessRights": "Кіраванне правамі на доступ да дакумента", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "Спампаваць файл", "Common.Views.Header.tipGoEdit": "Рэдагаваць бягучы файл", "Common.Views.Header.tipPrint": "Друкаваць файл", "Common.Views.Header.tipPrintQuick": "Хуткае друкаванне", "Common.Views.Header.tipRedo": "Паўтарыць", "Common.Views.Header.tipSave": "Захаваць", "Common.Views.Header.tipSearch": "По<PERSON><PERSON>к", "Common.Views.Header.tipUndo": "Адрабіць", "Common.Views.Header.tipUndock": "Адмацаваць у асобнае акно", "Common.Views.Header.tipUsers": "Праглядзець карыстальнікаў", "Common.Views.Header.tipViewSettings": "Налады прагляду", "Common.Views.Header.tipViewUsers": "Прагляд карыстальнікаў і кіраванне правамі на доступ да дакумента", "Common.Views.Header.txtAccessRights": "Змяніць правы на доступ", "Common.Views.Header.txtRename": "Змяніць назву", "Common.Views.History.textCloseHistory": "Закрыць гісторыю", "Common.Views.History.textHideAll": "Схаваць падрабязныя змены", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "Аднавіць", "Common.Views.History.textShowAll": "Паказаць падрабязныя змены", "Common.Views.History.textVer": "вер.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "Устаўце URL выявы:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Гэтае поле неабходна запоўніць", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Гэтае поле павінна быць URL-адрасам фармату \"http://www.example.com\"", "Common.Views.ListSettingsDialog.textBulleted": "З адзнакамі", "Common.Views.ListSettingsDialog.textFromFile": "З файла", "Common.Views.ListSettingsDialog.textFromStorage": "Са сховішча", "Common.Views.ListSettingsDialog.textFromUrl": "Па URL", "Common.Views.ListSettingsDialog.textNumbering": "Нумараваны", "Common.Views.ListSettingsDialog.textSelect": "Абраць з", "Common.Views.ListSettingsDialog.tipChange": "Змяніць адзнаку", "Common.Views.ListSettingsDialog.txtBullet": "Адзнака", "Common.Views.ListSettingsDialog.txtColor": "<PERSON>о<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "Выява", "Common.Views.ListSettingsDialog.txtImport": "Імпартаванне", "Common.Views.ListSettingsDialog.txtNewBullet": "Новая адзнака", "Common.Views.ListSettingsDialog.txtNewImage": "Новая выява", "Common.Views.ListSettingsDialog.txtNone": "Няма", "Common.Views.ListSettingsDialog.txtOfText": "% тэксту", "Common.Views.ListSettingsDialog.txtSize": "<PERSON>а<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "Пачаць з", "Common.Views.ListSettingsDialog.txtSymbol": "Сімвал", "Common.Views.ListSettingsDialog.txtTitle": "Налады спіса", "Common.Views.ListSettingsDialog.txtType": "Тып", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "Закрыць файл", "Common.Views.OpenDialog.textInvalidRange": "Хібны дыяпазон ячэек", "Common.Views.OpenDialog.textSelectData": "Абраць даныя", "Common.Views.OpenDialog.txtAdvanced": "Дадаткова", "Common.Views.OpenDialog.txtColon": "Двукроп’е", "Common.Views.OpenDialog.txtComma": "Коска", "Common.Views.OpenDialog.txtDelimiter": "Падзяляльнік", "Common.Views.OpenDialog.txtDestData": "Абярыце месца ўстаўляння даных", "Common.Views.OpenDialog.txtEmpty": "Гэтае поле неабходна запоўніць", "Common.Views.OpenDialog.txtEncoding": "Кадаванне", "Common.Views.OpenDialog.txtIncorrectPwd": "Уведзены хібны пароль.", "Common.Views.OpenDialog.txtOpenFile": "Каб адкрыць файл, увядзіце пароль", "Common.Views.OpenDialog.txtOther": "Іншае", "Common.Views.OpenDialog.txtPassword": "Пароль", "Common.Views.OpenDialog.txtPreview": "Прагляд", "Common.Views.OpenDialog.txtProtected": "Калі вы ўвядзеце пароль і адкрыеце файл бягучы пароль да файла скінецца.", "Common.Views.OpenDialog.txtSemicolon": "Коска з кропкай", "Common.Views.OpenDialog.txtSpace": "Прагал", "Common.Views.OpenDialog.txtTab": "Табуляцыя", "Common.Views.OpenDialog.txtTitle": "Абраць параметры %1", "Common.Views.OpenDialog.txtTitleProtected": "Абаронены файл", "Common.Views.PasswordDialog.txtDescription": "Прызначце пароль, каб абараніць гэты дакумент", "Common.Views.PasswordDialog.txtIncorrectPwd": "Паролі адрозніваюцца", "Common.Views.PasswordDialog.txtPassword": "Пароль", "Common.Views.PasswordDialog.txtRepeat": "Паўтарыць пароль", "Common.Views.PasswordDialog.txtTitle": "Прызначыць пароль", "Common.Views.PasswordDialog.txtWarning": "Увага: Страчаны або забыты пароль аднавіць немагчыма. Захоўвайце яго ў надзейным месцы.", "Common.Views.PluginDlg.textLoading": "Загрузка", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "Убудовы", "Common.Views.Plugins.strPlugins": "Убудовы", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "Запуск", "Common.Views.Plugins.textStop": "Спыніць", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "Зашыфраваць пры дапамозе пароля", "Common.Views.Protection.hintDelPwd": "Выдаліць пароль", "Common.Views.Protection.hintPwd": "Змяніць альбо выдаліць пароль", "Common.Views.Protection.hintSignature": "Дадаць лічбавы подпіс альбо радок подпісу", "Common.Views.Protection.txtAddPwd": "Дадаць пароль", "Common.Views.Protection.txtChangePwd": "Змяніць пароль", "Common.Views.Protection.txtDeletePwd": "Выдаліць пароль", "Common.Views.Protection.txtEncrypt": "Шыфраванне", "Common.Views.Protection.txtInvisibleSignature": "Дадаць лічбавы подпіс", "Common.Views.Protection.txtSignature": "Под<PERSON><PERSON>с", "Common.Views.Protection.txtSignatureLine": "Дадаць радок подпісу", "Common.Views.RecentFiles.txtOpenRecent": "Адкрыць нядаўнія", "Common.Views.RenameDialog.textName": "Назва файла", "Common.Views.RenameDialog.txtInvalidName": "Назва файла не павінна змяшчаць наступныя сімвалы:", "Common.Views.ReviewChanges.hintNext": "Да наступнай змены", "Common.Views.ReviewChanges.hintPrev": "Да папярэдняй змены", "Common.Views.ReviewChanges.strFast": "Хутк<PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Сумеснае рэдагаванне ў рэжыме рэальнага часу. Ўсе змены захоўваюцца аўтаматычна.", "Common.Views.ReviewChanges.strStrict": "Строгі", "Common.Views.ReviewChanges.strStrictDesc": "Выкарыстоўвайце кнопку \"Захаваць\" для сінхранізацыі вашых змен і змен іншых карыстальнікаў.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Ухваліць бягучыя змены", "Common.Views.ReviewChanges.tipCoAuthMode": "Уключыць рэжым сумеснага рэдагавання", "Common.Views.ReviewChanges.tipCommentRem": "Выдаліць каментары", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Выдаліць бягучыя каментары", "Common.Views.ReviewChanges.tipCommentResolve": "Развязаць каментары", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Развязаць бягучыя каментары", "Common.Views.ReviewChanges.tipHistory": "Паказваць гісторыю версій", "Common.Views.ReviewChanges.tipRejectCurrent": "Адхіліць бягучыя змены", "Common.Views.ReviewChanges.tipReview": "Адсочваць змены", "Common.Views.ReviewChanges.tipReviewView": "Абярыце рэжым, у якім неабходна адлюстроўваць змены", "Common.Views.ReviewChanges.tipSetDocLang": "Вызначыць мову дакумента ", "Common.Views.ReviewChanges.tipSetSpelling": "Праверка правапісу", "Common.Views.ReviewChanges.tipSharing": "Кіраванне правамі на доступ да дакумента", "Common.Views.ReviewChanges.txtAccept": "Ухваліць", "Common.Views.ReviewChanges.txtAcceptAll": "Ухваліць усе змены", "Common.Views.ReviewChanges.txtAcceptChanges": "Ухваліць змены", "Common.Views.ReviewChanges.txtAcceptCurrent": "Ухваліць бягучыя змены", "Common.Views.ReviewChanges.txtChat": "Размова", "Common.Views.ReviewChanges.txtClose": "Закрыць", "Common.Views.ReviewChanges.txtCoAuthMode": "Рэжым сумеснага рэдагавання", "Common.Views.ReviewChanges.txtCommentRemAll": "Выдаліць усе каментары", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Выдаліць бягучыя каментары", "Common.Views.ReviewChanges.txtCommentRemMy": "Выдаліць мае каментары", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Выдаліць мае бягучыя каментары", "Common.Views.ReviewChanges.txtCommentRemove": "Выдаліць", "Common.Views.ReviewChanges.txtCommentResolve": "Вырашыць", "Common.Views.ReviewChanges.txtCommentResolveAll": "Развязаць усе каментары", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Развязаць бягучыя каментары", "Common.Views.ReviewChanges.txtCommentResolveMy": "Развязаць мае каментары", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Развязаць мае бягучыя каментары", "Common.Views.ReviewChanges.txtDocLang": "Мова", "Common.Views.ReviewChanges.txtFinal": "Усе змены ўхваленыя (папярэдні прагляд)", "Common.Views.ReviewChanges.txtFinalCap": "Выніковы дакумент", "Common.Views.ReviewChanges.txtHistory": "Гісторыя версій", "Common.Views.ReviewChanges.txtMarkup": "Усе змены (рэдагаванне)", "Common.Views.ReviewChanges.txtMarkupCap": "Змены", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Усе змены адкінутыя (папярэдні прагляд)", "Common.Views.ReviewChanges.txtOriginalCap": "Зыходны дакумент", "Common.Views.ReviewChanges.txtPrev": "Папярэдняе", "Common.Views.ReviewChanges.txtReject": "Адх<PERSON><PERSON><PERSON>ць", "Common.Views.ReviewChanges.txtRejectAll": "Адхіліць усе змены", "Common.Views.ReviewChanges.txtRejectChanges": "Адх<PERSON><PERSON><PERSON><PERSON>ь змены", "Common.Views.ReviewChanges.txtRejectCurrent": "Адхіліць бягучыя змены", "Common.Views.ReviewChanges.txtSharing": "Супольны доступ", "Common.Views.ReviewChanges.txtSpelling": "Праверка правапісу", "Common.Views.ReviewChanges.txtTurnon": "Адсочванне змен", "Common.Views.ReviewChanges.txtView": "Адлюстраванне", "Common.Views.ReviewPopover.textAdd": "Дада<PERSON>ь", "Common.Views.ReviewPopover.textAddReply": "Дада<PERSON>ь адказ", "Common.Views.ReviewPopover.textCancel": "Скасаваць", "Common.Views.ReviewPopover.textClose": "Закрыць", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "Добра", "Common.Views.ReviewPopover.textEnterComment": "Увядзіце сюды свой каментар", "Common.Views.ReviewPopover.textMention": "+апавяшчэнне дасць доступ да дакумента і адправіць апавяшчэнне па электроннай пошце", "Common.Views.ReviewPopover.textMentionNotify": "+апавяшчэнне адправіць карыстальніку апавяшчэнне па электроннай пошце", "Common.Views.ReviewPopover.textOpenAgain": "Адкрыць зноў", "Common.Views.ReviewPopover.textReply": "Адказаць", "Common.Views.ReviewPopover.textResolve": "Вырашыць", "Common.Views.ReviewPopover.textViewResolved": "У вас няма правоў на паўторнае адкрыццё каментара", "Common.Views.ReviewPopover.txtDeleteTip": "Выдаліць", "Common.Views.ReviewPopover.txtEditTip": "Рэдагаваць", "Common.Views.SaveAsDlg.textLoading": "Загрузка", "Common.Views.SaveAsDlg.textTitle": "Каталог для захавання", "Common.Views.SearchPanel.textByColumns": "Па слупках", "Common.Views.SearchPanel.textByRows": "Па радках", "Common.Views.SearchPanel.textCaseSensitive": "Зважаць на рэгістр", "Common.Views.SearchPanel.textCell": "Ячэйка", "Common.Views.SearchPanel.textCloseSearch": "Закрыць пошук", "Common.Views.SearchPanel.textContentChanged": "Дакумент зменены.", "Common.Views.SearchPanel.textFind": "По<PERSON><PERSON>к", "Common.Views.SearchPanel.textFindAndReplace": "Пошук і замена", "Common.Views.SearchPanel.textFormula": "Формула", "Common.Views.SearchPanel.textFormulas": "Формулы", "Common.Views.SearchPanel.textItemEntireCell": "Усё змесціва ячэек", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} элемент(аў) паспяхова заменена.", "Common.Views.SearchPanel.textLookIn": "Шукаць у", "Common.Views.SearchPanel.textMatchUsingRegExp": "Супастаўленне з дапамогай рэгулярных выразаў", "Common.Views.SearchPanel.textName": "Назва", "Common.Views.SearchPanel.textNoMatches": "Няма супадзенняў", "Common.Views.SearchPanel.textNoSearchResults": "Нічога не знойдзена", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} элемент(аў) паспяхова заменена. Астатнія {2} элемент(аў) заблакаваныя іншымі карыстальнікамі.", "Common.Views.SearchPanel.textReplace": "Замяніць", "Common.Views.SearchPanel.textReplaceAll": "Замяніць усе", "Common.Views.SearchPanel.textReplaceWith": "Замяніць на", "Common.Views.SearchPanel.textSearch": "По<PERSON><PERSON>к", "Common.Views.SearchPanel.textSearchAgain": "{0}Выканаць новы пошук{1}, каб атрымаць дакладныя вынікі.", "Common.Views.SearchPanel.textSearchHasStopped": "Пошук спынены", "Common.Views.SearchPanel.textSearchOptions": "Параметры пошуку", "Common.Views.SearchPanel.textSearchResults": "Вынікі пошуку: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textSelectDataRange": "Абраць дыяпазон даных", "Common.Views.SearchPanel.textSheet": "<PERSON>р<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSpecificRange": "Вызначаны дыяпазон", "Common.Views.SearchPanel.textTooManyResults": "Занадта шмат вынікаў, каб паказаць іх тут", "Common.Views.SearchPanel.textValue": "Значэнне", "Common.Views.SearchPanel.textValues": "Значэнні", "Common.Views.SearchPanel.textWholeWords": "Толькі слова цалкам", "Common.Views.SearchPanel.textWithin": "Шукаць", "Common.Views.SearchPanel.textWorkbook": "Кніга", "Common.Views.SearchPanel.tipNextResult": "Наступны вынік", "Common.Views.SearchPanel.tipPreviousResult": "Папярэдні вынік", "Common.Views.SelectFileDlg.textLoading": "Загрузка", "Common.Views.SelectFileDlg.textTitle": "Абраць крыніцу даных", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "Тоўсты", "Common.Views.SignDialog.textCertificate": "Сертыфікат", "Common.Views.SignDialog.textChange": "Змяніць", "Common.Views.SignDialog.textInputName": "Увядзіце імя падпісальніка", "Common.Views.SignDialog.textItalic": "Курсіў", "Common.Views.SignDialog.textNameError": "Імя падпісанта не павінна быць пустым.", "Common.Views.SignDialog.textPurpose": "Мэта падпісання дакумента", "Common.Views.SignDialog.textSelect": "Абраць", "Common.Views.SignDialog.textSelectImage": "Абраць выяву", "Common.Views.SignDialog.textSignature": "Выгляд подпісу:", "Common.Views.SignDialog.textTitle": "Падпісаць дакумент", "Common.Views.SignDialog.textUseImage": "альбо націсніце \"Абраць выяву\", каб выкарыстаць выяву як подпіс", "Common.Views.SignDialog.textValid": "Сапраўдны з %1 па %2", "Common.Views.SignDialog.tipFontName": "Назва шрыфту", "Common.Views.SignDialog.tipFontSize": "Памер шрыфту", "Common.Views.SignSettingsDialog.textAllowComment": "Дазволіць падпісальніку дадаваць каментары ў дыялогу подпісу", "Common.Views.SignSettingsDialog.textDefInstruction": "Перш чым падпісваць гэты дакумент, пераканайцеся, што змесціва, якое вы падпісваеце, правільнае.", "Common.Views.SignSettingsDialog.textInfoEmail": "Адрас электроннай пошты прапанаванага падпісанта", "Common.Views.SignSettingsDialog.textInfoName": "Прапанаваны падпісант", "Common.Views.SignSettingsDialog.textInfoTitle": "Пасада прапанаванага падпісанта", "Common.Views.SignSettingsDialog.textInstructions": "Інструкцыі для падпісанта", "Common.Views.SignSettingsDialog.textShowDate": "Паказваць дату подпісу ў радку подпісу", "Common.Views.SignSettingsDialog.textTitle": "Наладжванне подпісу", "Common.Views.SignSettingsDialog.txtEmpty": "Гэтае поле неабходна запоўніць", "Common.Views.SymbolTableDialog.textCharacter": "Знак", "Common.Views.SymbolTableDialog.textCode": "Шаснаццатковы код Юнікод", "Common.Views.SymbolTableDialog.textCopyright": "Знак аўтарскіх правоў", "Common.Views.SymbolTableDialog.textDCQuote": "Закрывальнае двукоссе", "Common.Views.SymbolTableDialog.textDOQuote": "Адкрывальнае двукоссе", "Common.Views.SymbolTableDialog.textEllipsis": "Гарызантальнае шматкроп’е", "Common.Views.SymbolTableDialog.textEmDash": "Доўгі злучок", "Common.Views.SymbolTableDialog.textEmSpace": "Доўгі прагал", "Common.Views.SymbolTableDialog.textEnDash": "Кароткі злучок", "Common.Views.SymbolTableDialog.textEnSpace": "Кароткі прагал", "Common.Views.SymbolTableDialog.textFont": "Шры<PERSON>т", "Common.Views.SymbolTableDialog.textNBHyphen": "Неразрыўны праця<PERSON>нік", "Common.Views.SymbolTableDialog.textNBSpace": "Неразрыўны прагал", "Common.Views.SymbolTableDialog.textPilcrow": "Знак абзаца", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 прагала", "Common.Views.SymbolTableDialog.textRange": "Дыяпазон", "Common.Views.SymbolTableDialog.textRecent": "Раней выкарыстаныя сімвалы", "Common.Views.SymbolTableDialog.textRegistered": "Зарэгістраваны таварны знак", "Common.Views.SymbolTableDialog.textSCQuote": "Закрывальная коска", "Common.Views.SymbolTableDialog.textSection": "Знак раздзела", "Common.Views.SymbolTableDialog.textShortcut": "Спалучэнне клавіш", "Common.Views.SymbolTableDialog.textSHyphen": "Мяккі працяжнік", "Common.Views.SymbolTableDialog.textSOQuote": "Адкрывальная коска", "Common.Views.SymbolTableDialog.textSpecial": "Адмысловыя сімвалы", "Common.Views.SymbolTableDialog.textSymbols": "Сімвалы", "Common.Views.SymbolTableDialog.textTitle": "Сімвал", "Common.Views.SymbolTableDialog.textTradeMark": "Сімвал таварнага знака", "Common.Views.UserNameDialog.textDontShow": "Больш не пытацца", "Common.Views.UserNameDialog.textLabel": "Адмеціна:", "Common.Views.UserNameDialog.textLabelError": "Адмеціна не можа быць пустой", "SSE.Controllers.DataTab.strSheet": "<PERSON>р<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textAddExternalData": "Дададзена спасылка на вонкавую крыніцу. Такія спасылкі можна абнавіць ва ўкладцы \"Даныя\".", "SSE.Controllers.DataTab.textColumns": "Слупкі", "SSE.Controllers.DataTab.textContinue": "Continue", "SSE.Controllers.DataTab.textDontUpdate": "Не абнаўляць", "SSE.Controllers.DataTab.textEmptyUrl": "Трэба пазначыць URL-адрас.", "SSE.Controllers.DataTab.textRows": "Радкі", "SSE.Controllers.DataTab.textTurnOff": "Turn off AutoUpdate", "SSE.Controllers.DataTab.textUpdate": "Абнавіць", "SSE.Controllers.DataTab.textWizard": "Тэкст па слупках", "SSE.Controllers.DataTab.txtDataValidation": "Правяранне даных", "SSE.Controllers.DataTab.txtErrorExternalLink": "Памылка: не ўдалося абнавіць", "SSE.Controllers.DataTab.txtExpand": "Пашырыць", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Даныя побач з абраным дыяпазонам не выдаляцца. Хочаце пашырыць абраны дыяпазон, каб уключыць даныя з вакольных ячэек, альбо працягнуць толькі з абраным дыяпазонам? ", "SSE.Controllers.DataTab.txtExtendDataValidation": "Абраная вобласць змяшчае ячэйкі без налад правярання даных.<br>Хочаце пашырыць налады на гэтыя ячэйкі?", "SSE.Controllers.DataTab.txtImportWizard": "Памагаты па імпартаванні тэксту", "SSE.Controllers.DataTab.txtRemDuplicates": "Выдаліць паўторы", "SSE.Controllers.DataTab.txtRemoveDataValidation": "Абраная вобласць змяшчае больш за адзін тып праверкі.<br>Выдаліць бягучыя налады і працягнуць?", "SSE.Controllers.DataTab.txtRemSelected": "Выдаліць у абраным", "SSE.Controllers.DataTab.txtUrlTitle": "Уставіць URL-адрас даных", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "This workbook contains links to external sources which update automatically. This might be unsafe.<br><br>If you trust them, press Continue.", "SSE.Controllers.DataTab.warnUpdateExternalData": "Кніга змяшчае спасылкі на вонкавыя крыніцы даных, якія могуць быць небяспечнымі.<br>Калі вы давяраеце гэтым спасылкам, абнавіце іх, каб атрымаць апошнія даныя.", "SSE.Controllers.DocumentHolder.alignmentText": "Выраўноўванне", "SSE.Controllers.DocumentHolder.centerText": "Па цэнтры", "SSE.Controllers.DocumentHolder.deleteColumnText": "Выдаліць слупок", "SSE.Controllers.DocumentHolder.deleteRowText": "Выдаліць радок", "SSE.Controllers.DocumentHolder.deleteText": "Выдаліць", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Спасылка паказвае на ячэйку, якая не існуе. Выпраўце ці выдаліце спасылку.", "SSE.Controllers.DocumentHolder.guestText": "Госць", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Слупок злева", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Слупок справа", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Радок вышэй", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Радок ніжэй", "SSE.Controllers.DocumentHolder.insertText": "Уставіць", "SSE.Controllers.DocumentHolder.leftText": "Па леваму краю", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Увага", "SSE.Controllers.DocumentHolder.rightText": "Па праваму краю", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Параметры аўтазамены", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Шырыня слупка {0} сімвалаў ({1} пікселяў)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Вышыня радка {0} кропак ({1} пікселяў)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Пстрыкніце па спасылцы, каб яе адкрыць, альбо пстрыкніце і ўтрымлівайце, каб абраць ячэйку.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Уставіць слупок злева", "SSE.Controllers.DocumentHolder.textInsertTop": "Уставіць радок вышэй", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Адмысловая ўстаўка", "SSE.Controllers.DocumentHolder.textStopExpand": "Спыніць аўтаматычнае разгортванне табліц", "SSE.Controllers.DocumentHolder.textSym": "сімв", "SSE.Controllers.DocumentHolder.tipIsLocked": "Гэты элемент рэдагуецца іншым карыстальнікам.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Вышэй за сярэдняе", "SSE.Controllers.DocumentHolder.txtAddBottom": "Дадаць ніжнюю мяжу", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Дадаць рыску дробу", "SSE.Controllers.DocumentHolder.txtAddHor": "Дада<PERSON>ь гарызантальную лінію", "SSE.Controllers.DocumentHolder.txtAddLB": "Дада<PERSON>ь лінію з левага ніжняга кута", "SSE.Controllers.DocumentHolder.txtAddLeft": "Дада<PERSON>ь левую мяжу", "SSE.Controllers.DocumentHolder.txtAddLT": "Дада<PERSON>ь лінію з левага верхняга кута", "SSE.Controllers.DocumentHolder.txtAddRight": "Дадаць правую мяжу", "SSE.Controllers.DocumentHolder.txtAddTop": "Дадаць верхнюю мяжу", "SSE.Controllers.DocumentHolder.txtAddVer": "Дадаць вертыкальную лінію", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Выраўноўванне па сімвале", "SSE.Controllers.DocumentHolder.txtAll": "(Усе)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Вяртае ўсё змесціва табліцы або вызначаныя слупкі табліцы, уключаючы загалоўкі слупкоў, даныя і радкі вынікаў", "SSE.Controllers.DocumentHolder.txtAnd": "і", "SSE.Controllers.DocumentHolder.txtBegins": "Пачынаецца з", "SSE.Controllers.DocumentHolder.txtBelowAve": "Ніжэй за сярэдняе", "SSE.Controllers.DocumentHolder.txtBlanks": "(Пустыя)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Уласцівасці межаў", "SSE.Controllers.DocumentHolder.txtBottom": "Па ніжняму краю", "SSE.Controllers.DocumentHolder.txtByField": "%1 з %2", "SSE.Controllers.DocumentHolder.txtColumn": "Слупок", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Выраўноўванне слупка", "SSE.Controllers.DocumentHolder.txtContains": "Змяшчае", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Спасылка скапіяваная ў буфер абмену", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Вяртае ячэйкі даных з табліцы або вызначаных слупкоў табліцы", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Паменшыць памер аргумента", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Выдаліць аргумент", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Выдаліць уласнаручны разрыў", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Выдаліць укладзеныя сімвалы", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Выдаліць укладзеныя сімвалы і падзяляльнікі", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Выдаліць раўнанне", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Выдаліць сімвал", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Выдаліць радыкал", "SSE.Controllers.DocumentHolder.txtEnds": "Заканчваецца на", "SSE.Controllers.DocumentHolder.txtEquals": "Роўна", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Адпавядае колеру ячэйкі", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Адпавядае колеру шрыфту", "SSE.Controllers.DocumentHolder.txtExpand": "Пашырыць і ўпарадкаваць", "SSE.Controllers.DocumentHolder.txtExpandSort": "Даныя побач з абраным дыяпазонам не будуць сартавацца. Хочаце пашырыць абраны дыяпазон, каб уключыць даныя з супольных ячэек, альбо працягнуць сартаванне толькі абранага дыяпазону?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Знізу", "SSE.Controllers.DocumentHolder.txtFilterTop": "Найбольшыя", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Змяніць на лінейны дроб", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Змяніць на дыяганальны дроб", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Змяніць на вертыкальны дроб", "SSE.Controllers.DocumentHolder.txtGreater": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Больш альбо роўна", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Сімвал па-над тэкстам", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Сімвал пад тэкстам", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Вяртае загалоўкі слупкоў з табліцы або вызначаных слупкоў табліцы", "SSE.Controllers.DocumentHolder.txtHeight": "Вышыня", "SSE.Controllers.DocumentHolder.txtHideBottom": "Схаваць ніжнюю мяжу", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Схаваць ніжні ліміт", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Схаваць закрывальную дужку", "SSE.Controllers.DocumentHolder.txtHideDegree": "Схаваць ступень", "SSE.Controllers.DocumentHolder.txtHideHor": "Схаваць гарызантальную лінію", "SSE.Controllers.DocumentHolder.txtHideLB": "Схаваць лінію з левага ніжняга кута ", "SSE.Controllers.DocumentHolder.txtHideLeft": "Схаваць левую мяжу", "SSE.Controllers.DocumentHolder.txtHideLT": "Схаваць лінію з верхняга левага кут", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Схаваць адкрывальную дужку", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Схаваць палі для запаўнення", "SSE.Controllers.DocumentHolder.txtHideRight": "Схаваць правую мяжу", "SSE.Controllers.DocumentHolder.txtHideTop": "Схаваць верхнюю мяжу", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Схаваць верхні ліміт", "SSE.Controllers.DocumentHolder.txtHideVer": "Схаваць вертыкальную лінію", "SSE.Controllers.DocumentHolder.txtImportWizard": "Памагаты па імпартаванні тэксту", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Павялічыць памер аргумента", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Уставіць аргумент пасля", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Уставіць аргумент перад", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Уставіць уласнаручны разрыў", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Уставіць раўнанне пасля", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Уставіць раўнанне перад", "SSE.Controllers.DocumentHolder.txtItems": "элементаў", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Пакінуць толькі тэкст", "SSE.Controllers.DocumentHolder.txtLess": "Ме<PERSON><PERSON> за", "SSE.Controllers.DocumentHolder.txtLessEquals": "Мен<PERSON> альбо роўна", "SSE.Controllers.DocumentHolder.txtLimitChange": "Змяніць размяшчэнне лімітаў", "SSE.Controllers.DocumentHolder.txtLimitOver": "Ліміт па-над тэкстам", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Ліміт пад тэкстам", "SSE.Controllers.DocumentHolder.txtLockSort": "Знойдзены даныя, якія знаходзяцца далей за абраны дыяпазон, але вы не маеце правоў на змену гэтых ячэек<br>Хочаце працягнуць працу з абраным дыяпазонам?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Змяніць памер дужак адпаведна вышыні аргумента", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Выраўноўванне матрыц", "SSE.Controllers.DocumentHolder.txtNoChoices": "Няма варыянтаў для запаўнення ячэйкі.<br>Для замены можна абраць толькі тэкставыя значэнні са слупка.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Не пачынаецца з", "SSE.Controllers.DocumentHolder.txtNotContains": "Не змяшчае", "SSE.Controllers.DocumentHolder.txtNotEnds": "Не заканчваецца на", "SSE.Controllers.DocumentHolder.txtNotEquals": "Не роўна", "SSE.Controllers.DocumentHolder.txtOr": "альбо", "SSE.Controllers.DocumentHolder.txtOverbar": "Лінія па-над тэкстам", "SSE.Controllers.DocumentHolder.txtPaste": "Уставіць", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Формула без межаў", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Формула + шырыня слупка", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Фарматаванне канцавых ячэек", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Уставіць толькі фарматаванне", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Формула + фармат лічбаў", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Уставіць толькі формулу", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Формула + усё фарматаванне", "SSE.Controllers.DocumentHolder.txtPasteLink": "Уставіць спасылку", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Звязаны малюнак", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Аб’яднаць умоўнае фарматаванне", "SSE.Controllers.DocumentHolder.txtPastePicture": "Малюнак", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Зыходнае фарматаванне", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Пераправіць", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Значэнне + усё фарматаванне", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Значэнне + фармат лікаў", "SSE.Controllers.DocumentHolder.txtPasteValues": "Уставіць толькі значэнне", "SSE.Controllers.DocumentHolder.txtPercent": "адсоткаў", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Паўтарыць аўтаразгортванне табліцы", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Выдаліць рыску дробу", "SSE.Controllers.DocumentHolder.txtRemLimit": "Выдаліць ліміт", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Выдаліць дыякрытычны знак", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Выдаліць лінію", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Хочаце выдаліць гэты подпіс?<br>Гэтае дзеянне нельга скасаваць.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Выдаліць індэксы", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Выдаліць ніжні індэкс", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Выдаліць верхні індэкс", "SSE.Controllers.DocumentHolder.txtRowHeight": "Вышыня радка", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Індэксы пасля тэксту", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Індэксы перад тэкстам", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Паказаць ніжні ліміт", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Паказаць закрывальную дужку", "SSE.Controllers.DocumentHolder.txtShowDegree": "Паказаць ступень", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Паказаць адкрывальную дужку", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Паказаць поле для запаўнення", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Паказаць верхні ліміт", "SSE.Controllers.DocumentHolder.txtSorting": "Парадкаванне", "SSE.Controllers.DocumentHolder.txtSortSelected": "Парадкаваць абранае", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Расцягнуць дужкі", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Абраць толькі гэты радок вызначанага слупка", "SSE.Controllers.DocumentHolder.txtTop": "Па верхняму краю", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Вяртае радкі вынікаў з табліцы або вызначаных слупкоў табліцы", "SSE.Controllers.DocumentHolder.txtUnderbar": "Лінія пад тэкстам", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Скасаваць аўтаразгортванне табліцы", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Выкарыстоўваць памагатага па імпартаванні тэксту", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Пераход па гэтай спасылцы можа прывесці да пашкоджання прылады і даных.<br>Сапраўды хочаце працягнуць?", "SSE.Controllers.DocumentHolder.txtWidth": "Шырыня", "SSE.Controllers.DocumentHolder.warnFilterError": "Каб узяць фільтр значэння, вобласць значэнняў мусіць змяшчаць хоць адно поле.", "SSE.Controllers.FormulaDialog.sCategoryAll": "Усе", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Custom", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Базы даных", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Дата і час", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Інжынерныя", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Фінансавыя", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Інфармацыйныя", "SSE.Controllers.FormulaDialog.sCategoryLast10": "Апошнія 10 выкарыстаных", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Лагічныя", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Агляд і спасылкі", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Матэматычныя", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Статыстычныя", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Тэкст і даныя", "SSE.Controllers.LeftMenu.newDocumentTitle": "Табліца без назвы", "SSE.Controllers.LeftMenu.textByColumns": "Па слупках", "SSE.Controllers.LeftMenu.textByRows": "Па радках", "SSE.Controllers.LeftMenu.textFormulas": "Формулы", "SSE.Controllers.LeftMenu.textItemEntireCell": "Усё змесціва ячэек", "SSE.Controllers.LeftMenu.textLoadHistory": "Загрузка гісторыі версій…", "SSE.Controllers.LeftMenu.textLookin": "Шукаць у", "SSE.Controllers.LeftMenu.textNoTextFound": "Даныя не знойдзеныя. Калі ласка, змяніце параметры пошуку.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Заменена. Прапушчана ўваходжанняў: {0}.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Пошук завершаны. Заменена ўваходжанняў: {0}", "SSE.Controllers.LeftMenu.textSave": "Захаваць", "SSE.Controllers.LeftMenu.textSearch": "По<PERSON><PERSON>к", "SSE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "SSE.Controllers.LeftMenu.textSheet": "<PERSON>р<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textValues": "Значэнні", "SSE.Controllers.LeftMenu.textWarning": "Увага", "SSE.Controllers.LeftMenu.textWithin": "Шукаць", "SSE.Controllers.LeftMenu.textWorkbook": "Кніга", "SSE.Controllers.LeftMenu.txtUntitled": "Без назвы", "SSE.Controllers.LeftMenu.warnDownloadAs": "Калі вы працягнеце захаванне ў гэты фармат, усе функцыі, апроч тэксту страцяцца.<br>Сапраўды хочаце працягнуць?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "The CSV format does not support saving a multi-sheet file.<br>To keep the selected format and save only the current sheet, press Save.<br>To save the current spreadsheet, click Cancel and save it in a different format.", "SSE.Controllers.Main.confirmAddCellWatches": "Гэтае дзеянне дадасць {0} ячэек назірання.<br>Хочаце працягнуць?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "Каб зэканоміць памяць, гэтае дзеянне дадасць толькі {0} ячэек назірання.<br>Хочаце працягнуць?", "SSE.Controllers.Main.confirmMaxChangesSize": "Памер зменаў перасягае абмежаванне вашага сервера.<br>Націсніце \"Адрабіць\", каб адрабіць апошняе дзеянне або націсніце \"Працягнуць\", каб захаваць дзеянне лакальна (трэба будзе спампаваць файл або скапіяваць яго змесціва, каб нічога не страцілася).", "SSE.Controllers.Main.confirmMoveCellRange": "Канцавы дыяпазон ячэек можа змяшчаць даныя. Працягнуць аперацыю?", "SSE.Controllers.Main.confirmPutMergeRange": "Зыходныя даныя змяшчалі аб’яднаныя ячэйкі .<br>Перад устаўляннем ячэек у табліцу аб’яднанне было скасаванае.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Формулы ў радку загалоўка выдаляцца і ператворацца ў статычны тэкст.<br>Хочаце працягнуць?", "SSE.Controllers.Main.confirmReplaceHFPicture": "Only one picture can be inserted in each section of the header.<br>Press \"Replace\" to replace existing picture.<br>Press \"Keep\" to keep existing picture.", "SSE.Controllers.Main.convertationTimeoutText": "Час чакання ператварэння скончыўся.", "SSE.Controllers.Main.criticalErrorExtText": "Націсніце \"Добра\", каб вярнуцца да спіса дакументаў.", "SSE.Controllers.Main.criticalErrorTitle": "Памылка", "SSE.Controllers.Main.downloadErrorText": "Не ўдалося спампаваць.", "SSE.Controllers.Main.downloadTextText": "Спампоўванне табліцы…", "SSE.Controllers.Main.downloadTitleText": "Спампоўванне табліцы", "SSE.Controllers.Main.errNoDuplicates": "Паўторных значэнняў не знойдзена.", "SSE.Controllers.Main.errorAccessDeny": "Вы спрабуеце выканаць дзеянне, на якое не маеце правоў.<br>Калі ласка, звярніцеся да адміністратара сервера дакументаў.", "SSE.Controllers.Main.errorArgsRange": "Ва ўведзенай формуле ёсць памылка.<br>Выкарыстаны хібны дыяпазон аргументаў.", "SSE.Controllers.Main.errorAutoFilterChange": "Аперацыя не дазволеная, бо адбываецца спроба ссунуць ячэйкі табліцы на аркушы.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Гэтую аперацыю немагчыма выканаць для абраных ячэек, бо нельга перамясціць частку табліцы.<br>Абярыце іншы дыяпазон даных, каб перамяшчалася ўся табліца, і паўтарыце зноў.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Гэтую аперацыю немагчыма выканаць для абранага дыяпазону ячэек.<br>Абярыце іншы дыяпазон даных, адрозны ад існага, і паўтарыце зноў.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Аперацыя не можа быць выкананая, бо ў вобласці змяшчаюцца адфільтраваныя ячэйкі.<br>Выведзіце на экран схаваныя элементы і паспрабуйце зноў.", "SSE.Controllers.Main.errorBadImageUrl": "Хібны URL-адрас выявы", "SSE.Controllers.Main.errorCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.Main.errorCannotPasteImg": "Не ўдалося ўставіць гэтую выяву з буфера абмену, але вы можаце захаваць яе на прыладзе і ўставіць адтуль. Таксама вы можаце скапіяваць выяву без тэксту і ўставіць яе ў табліцу.", "SSE.Controllers.Main.errorCannotUngroup": "Немагчыма разгрупаваць. Каб стварыць структуру дакумента, абярыце слупкі альбо радкі і згрупуйце іх.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Гэты загад нельга выкарыстоўваць у абароненым аркушы. Спачатку трэба зняць абарону аркуша.<br>Можа спатрэбіцца ўвесці пароль. ", "SSE.Controllers.Main.errorChangeArray": "Немагчыма змяніць частку масіву.", "SSE.Controllers.Main.errorChangeFilteredRange": "Гэта зменіць адфільтраваны дыяпазон аркуша.<br>Каб завяршыць гэтую задачу, трэба выдаліць усе аўтаматычныя фільтры.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "Ячэйка або дыяграма, якую вы спрабуеце змяніць, знаходзіцца на абароненым аркушы.<br>Каб унесці змены, здыміце абарону аркуша. Можа спатрэбіцца ўвесці пароль.", "SSE.Controllers.Main.errorCircularReference": "There are one or more circular references where a formula refers to its own cell either directly or indirectly.<br>Try removing or changing these references, or moving the formulas to different cells.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Злучэнне з серверам страчана. На дадзены момант рэдагаваць дакумент немагчыма.", "SSE.Controllers.Main.errorConnectToServer": "Не ўдалося захаваць дакумент. Калі ласка, праверце налады злучэння і звяжыцеся з адміністратарам.<br>Калі вы націснеце кнопку \"Добра\", вам прапануецца спампаваць дакумент.", "SSE.Controllers.Main.errorConvertXml": "Фармат файла не падтрымліваецца.<br>Можна выкарыстоўваць толькі фармат XML Spreadsheet 2003.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Гэты загад немагчыма ўжыць да незвязаных дыяпазонаў.<br>Абярыце адзін дыяпазон і паўтарыце спробу.", "SSE.Controllers.Main.errorCountArg": "Ва ўведзенай формуле ёсць памылка.<br>Выкарыстана хібная колькасць аргументаў.", "SSE.Controllers.Main.errorCountArgExceed": "Ва ўведзенай формуле ёсць памылка.<br>Перасягнута колькасць аргументаў.", "SSE.Controllers.Main.errorCreateDefName": "На дадзены момант немагчыма рэдагаваць існыя названыя дыяпазоны і ствараць новыя,<br>бо некаторыя з іх рэдагуюцца.", "SSE.Controllers.Main.errorCreateRange": "Наяўныя дыяпазоны нельга рэдагаваць, а новыя зараз немагчыма стварыць<br>, бо некаторыя з іх рэдагуюцца.", "SSE.Controllers.Main.errorDatabaseConnection": "Вонкавая памылка.<br>Памылка злучэння з базай даных. Калі памылка паўтараецца, калі ласка, звярніцеся ў службу падтрымкі.", "SSE.Controllers.Main.errorDataEncrypted": "Атрыманы зашыфраваныя змены, іх немагчыма расшыфраваць.", "SSE.Controllers.Main.errorDataRange": "Хібны дыяпазон даных.", "SSE.Controllers.Main.errorDataValidate": "Уведзена непрыдатнае значэнне.<br>З<PERSON><PERSON><PERSON>нні, якія можна ўвесці ў гэтую ячэйку, абмежаваныя.", "SSE.Controllers.Main.errorDefaultMessage": "Код памылкі: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Вы спрабуеце выдаліць слупок з заблакаванай ячэйкай. Заблакаваныя ячэйкі нельга выдаліць, калі аркуш абаронены.<br>Каб выдаліць заблакаваную ячэйку, здыміце абарону аркуша. Можа спатрэбіцца ўвесці пароль.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Вы спрабуеце выдаліць радок з заблакаванай ячэйкай. Заблакаваныя ячэйкі нельга выдаліць, калі аркуш абаронены.<br>Каб выдаліць заблакаваную ячэйку, здыміце абарону аркуша. Можа спатрэбіцца ўвесці пароль.", "SSE.Controllers.Main.errorDependentsNoFormulas": "The Trace Dependents command found no formulas that refer to the active cell.", "SSE.Controllers.Main.errorDirectUrl": "Калі ласка, праверце спасылку на дакумент.<br>Гэтая спасылка мусіць быць непасрэднай спасылкай для спампоўвання файла.", "SSE.Controllers.Main.errorEditingDownloadas": "Падчас працы з дакументам адбылася памылка.<br>Выкарыстайце параметр \"Спампаваць як\", каб захаваць рэзервовую копію файла на цвёрды дыск камп’ютара.", "SSE.Controllers.Main.errorEditingSaveas": "Падчас працы з дакументам адбылася памылка.<br>Выкарыстайце параметр \"Захаваць як…\", каб захаваць рэзервовую копію файла на цвёрды дыск камп’ютара.", "SSE.Controllers.Main.errorEditView": "Немагчыма рэдагаваць існыя мініяцюры аркушаў і ствараць новыя, бо некаторыя з іх ужо рэдагуюцца.", "SSE.Controllers.Main.errorEmailClient": "Не знойдзена паштовага кліента.", "SSE.Controllers.Main.errorFilePassProtect": "Файл абаронены паролем, яго немагчыма адкрыць.", "SSE.Controllers.Main.errorFileRequest": "Вонкавая памылка.<br>Памылка запыту файла. Калі памылка паўтараецца, калі ласка, звярніцеся ў службу падтрымкі.", "SSE.Controllers.Main.errorFileSizeExceed": "Памер файла перавышае ліміт, вызначаны для вашага сервера.<br>Звярніцеся да адміністратара сервера дакументаў за дадатковымі звесткамі.", "SSE.Controllers.Main.errorFileVKey": "Вонкавая памылка.<br><PERSON><PERSON><PERSON><PERSON><PERSON> ключ бяспекі. Калі памылка паўтараецца, калі ласка, звярніцеся ў службу падтрымкі.", "SSE.Controllers.Main.errorFillRange": "Немагчыма запоўніць абраны дыяпазон ячэек.<br>Усе аб’яднаныя ячэйкі мусяць быць аднаго памеру.", "SSE.Controllers.Main.errorForceSave": "Падчас захавання файла адбылася памылка. Калі ласка, выкарыстайце параметр \"Спампаваць як\", каб захаваць файл на цвёрдым дыску камп’ютара, альбо паспрабуйце яшчэ раз пазней.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Cannot enter a formula for an item or field name in a pivot table report.", "SSE.Controllers.Main.errorFormulaName": "Ва ўведзенай формуле ёсць памылка.<br>Выкарыстана хібная назва формулы.", "SSE.Controllers.Main.errorFormulaParsing": "Падчас разбору формулы адбылася ўнутраная памылка.", "SSE.Controllers.Main.errorFrmlMaxLength": "Даўжыня формулы перавышае абмежаванне ў 8192 сімвалы.<br>Адрэдагуйце яе і паўтарыце спробу.", "SSE.Controllers.Main.errorFrmlMaxReference": "Немагчыма увесці гэтую формулу, бо яна змяшчае занадта шмат значэнняў,<br>спасылак на ячэйкі альбо назваў.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Даўжыня тэкставых значэнняў у формулах не можа перавышаць 255 сімвалаў.<br>Выкарыстайце функцыю \"ЗЛУЧЫЦЬ\" альбо аператар злучэння (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Функцыя спасылаецца на аркуш, які не існуе.<br>Калі ласка, праверце даныя і паўтарыце спробу.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Не ўдалося выканаць аперацыю для абранага дыяпазону ячэек.<br>Абярыце дыяпазон так, каб першы радок знаходзіўся на тым жа радку,<br>а выніковая табліца перакрывала бягучую.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Не ўдалося выканаць аперацыю для абранага дыяпазону ячэек.<br> Абярыце дыяпазон, які не змяшчае іншых табліц.", "SSE.Controllers.Main.errorInconsistentExt": "Падчас адкрыцця файла адбылася памылка.<br>Змесціва файла не адпавядае пашырэнню файла.", "SSE.Controllers.Main.errorInconsistentExtDocx": "Падчас адкрыцця файла адбылася памылка.<br>Змесціва файла адпавядае прэзентацыі (напрыклад, docx), але файл мае несумяшчальнае пашырэнне: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "Падчас адкрыцця файла адбылася памылка.<br>Змесціва файла адпавядае аднаму з наступных фарматаў: pdf/djvu/xps/oxps, але файл мае несумяшчальнае пашырэнне: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "Падчас адкрыцця файла адбылася памылка.<br>Змесціва файла адпавядае прэзентацыі (напрыклад, pptx), але файл мае несумяшчальнае пашырэнне: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "Падчас адкрыцця файла адбылася памылка.<br>Змесціва файла адпавядае табліцы (напрыклад, xlsx), але файл мае несумяшчальнае пашырэнне: %1.", "SSE.Controllers.Main.errorInvalidRef": "Увядзіце прыдатную назву для дыяпазону альбо прыдатную спасылку для пераходу.", "SSE.Controllers.Main.errorKeyEncrypt": "Невядомы дэскрыптар ключа", "SSE.Controllers.Main.errorKeyExpire": "Тэрмін дзеяння ключа дэскрыптара сышоў", "SSE.Controllers.Main.errorLabledColumnsPivot": "Каб стварыць зводную табліцу, выкарыстайце даныя, пададзеныя спісам з загалоўкамі слупкоў.", "SSE.Controllers.Main.errorLoadingFont": "Шрыфты не загрузіліся.<br>Звярніцеся да адміністратара сервера дакументаў.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Спасылка на размяшчэнне або дыяпазон даных несапраўдная.", "SSE.Controllers.Main.errorLockedAll": "Аперацыя не можа быць выкананая, бо аркуш заблакаваны іншым карыстальнікам.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "One of the cells involved in the goal seek process has been modified by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "Нельга змяніць даныя ў зводнай табліцы.", "SSE.Controllers.Main.errorLockedWorksheetRename": "На дадзены момант немагчыма змяніць назву аркуша, бо яе змяняе іншы  карыстальнік", "SSE.Controllers.Main.errorMaxPoints": "Максімальная колькасць кропак у шэрагу для дыяграмы - 4096.", "SSE.Controllers.Main.errorMoveRange": "Немагчыма змяніць частку аб’яднанай ячэйкі", "SSE.Controllers.Main.errorMoveSlicerError": "Зводкі табліц немагчыма капіяваць з адной працоўнай кнігі ў іншую.<br>Паспрабуйце яшчэ раз, абраўшы ўсю табліцу і зводкі.", "SSE.Controllers.Main.errorMultiCellFormula": "У табліцах забаронена выкарыстанне формул масіву з некалькімі ячэйкамі.", "SSE.Controllers.Main.errorNoDataToParse": "Даныя для разбору не абраныя.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.Main.errorOpenWarning": "Даўжыня адной з формул у файле перасягнула 8192 сімвалы.<br>Формула была выдаленая.", "SSE.Controllers.Main.errorOperandExpected": "Сінтаксіс уведзенай функцыі хібны. Праверце, ці не прапушчана адна з дужак.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "Вы ўвялі няправільны пароль.<br>Перак<PERSON>найцеся, што клавіша CAPS LOCK адключаная і літары ўводзяцца ў патрэбным рэгістры.", "SSE.Controllers.Main.errorPasteInPivot": "We can't make this change for the selected cells because it will affect a pivot table.<br>Use the field list to change the report.", "SSE.Controllers.Main.errorPasteMaxRange": "Вобласці капіявання і ўстаўляння адрозніваюцца.<br>Каб уставіць скапіяваныя ячэйкі, абярыце вобласць таго ж памеру альбо пстрыкніце па першай ячэйцы ў радку.", "SSE.Controllers.Main.errorPasteMultiSelect": "Гэтае значэнне немагчыма ўжыць да некалькіх абраных дыяпазонаў.<br>Абярыце адзін дыяпазон і паўтарыце спробу.", "SSE.Controllers.Main.errorPasteSlicerError": "Зводкі табліц немагчыма капіяваць з адной працоўнай кнігі ў іншую.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Pivot table field name already exists.", "SSE.Controllers.Main.errorPivotGroup": "Немагчыма згрупаваць абраныя элементы.", "SSE.Controllers.Main.errorPivotOverlap": "Забараняецца перакрыцце справаздачы зводнай табліцы і табліцы.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Справаздача зводнай табліцы была захаваная без даных.<br>Каб абнавіць справаздачу, скарыстайцеся кнопкай \"Абнавіць\".", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "The Trace Precedents command requires that the active cell contain a formula which includes a valid references.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "На жаль, у бягучай версіі праграмы немагчыма надрукаваць больш за 1500 старонак запар.<br>Гэтае абмежаванне будзе прыбрана ў наступных версіях.", "SSE.Controllers.Main.errorProcessSaveResult": "Не ўдалося захаваць", "SSE.Controllers.Main.errorProtectedRange": "Гэты дыяпазон нельга рэдагаваць.", "SSE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "SSE.Controllers.Main.errorServerVersion": "Рэдактар быў абноўлены. Старонка перазагрузіцца, каб ужыць змены.", "SSE.Controllers.Main.errorSessionAbsolute": "Час сеанса рэдагавання дакумента сышоў. Калі ласка, абнавіце старонку.", "SSE.Controllers.Main.errorSessionIdle": "Дакумент працяглы час не рэдагаваўся. Калі ласка, абнавіце старонку.", "SSE.Controllers.Main.errorSessionToken": "Злучэнне з серверам перарванае. Калі ласка, абнавіце старонку.", "SSE.Controllers.Main.errorSetPassword": "Не ўдалося прызначыць пароль.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Спасылка на размяшчэнне несапраўдная, бо ячэйкі знаходзяцца ў розных слупках або радках.<br>Абярыце ячэйкі, якія знаходзяцца ў адным слупку або радку.", "SSE.Controllers.Main.errorStockChart": "Хібны парадак радкоў. Каб стварыць біржавую дыяграму размясціце даныя ў наступным парадку:<br>кошт адкрыцця, максімальны кошт, мінімальны кошт, кошт закрыцця.", "SSE.Controllers.Main.errorToken": "Токен бяспекі дакумента мае хібны фармат.<br>Калі ласка, звярніцеся да адміністатара сервера дакументаў.", "SSE.Controllers.Main.errorTokenExpire": "Тэрмін дзеяння токена бяспекі дакумента сышоў.<br>Калі ласка, звярніцеся да адміністратара сервера дакументаў.", "SSE.Controllers.Main.errorUnexpectedGuid": "Вонкавая памылка.<br>Нечаканы ідэнтыфікатар GUID. Калі памылка паўтараецца, калі ласка, звярніцеся ў службу падтрымкі.", "SSE.Controllers.Main.errorUpdateVersion": "Версія файла была змененая. Старонка будзе перазагружаная.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Злучэнне з інтэрнэтам было адноўлена, і версія файла змянілася.<br>Перш чым працягнуць працу, неабходна спампаваць файл альбо скапіяваць яго змесціва, каб захаваць даныя, а пасля перазагрузіць старонку.", "SSE.Controllers.Main.errorUserDrop": "На дадзены момант файл недаступны.", "SSE.Controllers.Main.errorUsersExceed": "Перасягнута колькасць карыстальнікаў, дазволеных згодна тарыфу", "SSE.Controllers.Main.errorViewerDisconnect": "Злучэнне страчана. Вы зможаце праглядаць дакумент,<br>але не зможаце спампаваць альбо надрукаваць яго да аднаўлення злучэння і перазагрузкі старонкі.", "SSE.Controllers.Main.errorWrongBracketsCount": "Ва ўведзенай формуле ёсць памылка.<br>Выкарыстана хібная колькасць дужак.", "SSE.Controllers.Main.errorWrongOperator": "Ва ўведзенай формуле ёсць памылка. Выкарыстаны хібны аператар.<br>Калі ласка, выпраўце памылку.", "SSE.Controllers.Main.errorWrongPassword": "Няправільны пароль.", "SSE.Controllers.Main.errRemDuplicates": "Знойдзена і выдалена паўторных значэнняў: {0}, засталося непаўторных значэнняў: {1}.", "SSE.Controllers.Main.leavePageText": "У электроннай табліцы ёсць незахаваныя змены. Каб захаваць іх, націсніце \"Застацца на гэтай старонцы\", пасля \"Захаваць\". Націсніце \"Пакінуць старонку\", каб скінуць усе незахаваныя змены.", "SSE.Controllers.Main.leavePageTextOnClose": "Усе незахаваныя змены ў гэтай табліцы страцяцца.<br> Націсніце кнопку \"Скасаваць\", пасля націсніце \"Захаваць\", каб захаваць змены. Націсніце \"Добра\", каб адкінуць змены.", "SSE.Controllers.Main.loadFontsTextText": "Загрузка даных…", "SSE.Controllers.Main.loadFontsTitleText": "Загрузка даных", "SSE.Controllers.Main.loadFontTextText": "Загрузка даных…", "SSE.Controllers.Main.loadFontTitleText": "Загрузка даных", "SSE.Controllers.Main.loadImagesTextText": "Загрузка выяў…", "SSE.Controllers.Main.loadImagesTitleText": "Загрузка выяў", "SSE.Controllers.Main.loadImageTextText": "Загрузка выявы…", "SSE.Controllers.Main.loadImageTitleText": "Загрузка выявы", "SSE.Controllers.Main.loadingDocumentTitleText": "Загрузка табліцы", "SSE.Controllers.Main.notcriticalErrorTitle": "Увага", "SSE.Controllers.Main.openErrorText": "Падчас адкрыцця файла адбылася памылка.", "SSE.Controllers.Main.openTextText": "Адкрыццё табліцы…", "SSE.Controllers.Main.openTitleText": "Адкрыццё табліцы", "SSE.Controllers.Main.pastInMergeAreaError": "Немагчыма змяніць частку аб’яднанай ячэйкі", "SSE.Controllers.Main.printTextText": "Друкаванне табліцы…", "SSE.Controllers.Main.printTitleText": "Друкаванне табліцы", "SSE.Controllers.Main.reloadButtonText": "Абнав<PERSON>ць старонку", "SSE.Controllers.Main.requestEditFailedMessageText": "Дакумент зараз рэдагуецца. Калі ласка, паспрабуйце пазней.", "SSE.Controllers.Main.requestEditFailedTitleText": "Доступ забаронены", "SSE.Controllers.Main.saveErrorText": "Падчас захавання файла адбылася памылка.", "SSE.Controllers.Main.saveErrorTextDesktop": "Немагчыма захаваць або стварыць гэты файл.<br>Магчымыя прычыны: <br>1. Файл даступны толькі для чытання. <br>2. Файл рэдагуецца іншымі карыстальнікамі. <br>3. Дыск запоўнены або пашкоджаны.", "SSE.Controllers.Main.saveTextText": "Захаванне табліцы…", "SSE.Controllers.Main.saveTitleText": "Захаванне табліцы", "SSE.Controllers.Main.scriptLoadError": "Занадта павольнае злучэнне, не ўсе кампаненты ўдалося загрузіць. Калі ласка, абнавіце старонку.", "SSE.Controllers.Main.textAnonymous": "Ананімны карыстальнік", "SSE.Controllers.Main.textApplyAll": "Ужыць да ўсіх раўнанняў", "SSE.Controllers.Main.textBuyNow": "Наведаць сайт", "SSE.Controllers.Main.textChangesSaved": "Усе змены захаваныя", "SSE.Controllers.Main.textClose": "Закрыць", "SSE.Controllers.Main.textCloseTip": "Пстрыкніце, каб закрыць гэтую падказку", "SSE.Controllers.Main.textConfirm": "Пацвярджэнне", "SSE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "SSE.Controllers.Main.textContactUs": "Звязацца з аддзелам продажу", "SSE.Controllers.Main.textContinue": "Працягнуць", "SSE.Controllers.Main.textConvertEquation": "Гэтае раўнанне створана ў старой версіі рэдактара раўнанняў, якая больш не падтрымліваецца. Каб змяніць раўнанне, яго неабходна ператварыць у фармат Math ML.<br>Ператварыць?", "SSE.Controllers.Main.textCustomLoader": "Звярніце ўвагу, што па ўмовах ліцэнзіі вы не можаце змяняць экран загрузкі.<br>Калі ласка, звярніцеся ў аддзел продажу.", "SSE.Controllers.Main.textDisconnect": "Злучэнне страчана", "SSE.Controllers.Main.textFillOtherRows": "Запоўніць іншыя радкі", "SSE.Controllers.Main.textFormulaFilledAllRows": "Формула запоўніла {0} радкоў з данымі. Запаўненне іншых пустых радкоў можа заняць некалькі хвілін.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Формула запоўніла першыя {0} радкі(оў). Запаўненне іншых пустых радкоў можа заняць некалькі хвілін.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "З прычыны эканоміі памяці формула запоўніла толькі першыя {0} радкі(оў) з данымі. На гэтым аркушы ёсць яшчэ {1} радкі(оў) з данымі. Вы можаце запоўніць іх уласнаручна.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "З прычыны эканоміі памяці формула запоўніла толькі першыя {0} радкі(оў). Астатнія радкі гэтага аркуша не змяшчаюць даных.", "SSE.Controllers.Main.textGuest": "Госць", "SSE.Controllers.Main.textHasMacros": "Файл змяшчае макрасы з аўтазапускам.<br>Хочаце запусціць макрасы?", "SSE.Controllers.Main.textKeep": "Keep", "SSE.Controllers.Main.textLearnMore": "Падрабязней", "SSE.Controllers.Main.textLoadingDocument": "Загрузка табліцы", "SSE.Controllers.Main.textLongName": "Увядзіце назву, якая карацей 128 знакаў.", "SSE.Controllers.Main.textNeedSynchronize": "Ёсць абнаўленні", "SSE.Controllers.Main.textNo": "Не", "SSE.Controllers.Main.textNoLicenseTitle": "Ліцэнзійнае абмежаванне", "SSE.Controllers.Main.textPaidFeature": "Платная функцыя", "SSE.Controllers.Main.textPleaseWait": "Аперацыя можа заняць больш часу. Калі ласка, пачакайце… ", "SSE.Controllers.Main.textReconnect": "Злучэнне адноўлена", "SSE.Controllers.Main.textRemember": "Запомніць мой выбар для ўсіх файлаў", "SSE.Controllers.Main.textRememberMacros": "Запомніць выбар для ўсіх макрасаў", "SSE.Controllers.Main.textRenameError": "Імя карыстальніка не павінна быць пустым.", "SSE.Controllers.Main.textRenameLabel": "Увядзіце назву, якая будзе выкарыстоўвацца для сумеснай працы.", "SSE.Controllers.Main.textReplace": "Замяніць", "SSE.Controllers.Main.textRequestMacros": "Макрас робіць запыт да URL. Хочаце дазволіць запыт да %1?", "SSE.Controllers.Main.textShape": "Фігура", "SSE.Controllers.Main.textStrict": "Строгі рэжым", "SSE.Controllers.Main.textText": "Тэкст", "SSE.Controllers.Main.textTryQuickPrint": "Вы абралі хуткае друкаванне: увесь дакумент будзе надрукаваны на апошнім абраным прынтары або прадвызначаным прынтары.<br>Хочаце ппацягнууь?", "SSE.Controllers.Main.textTryUndoRedo": "Функцыі скасавання і паўтору дзеянняў выключаныя ў хуткім рэжыме сумеснага рэдагавання.<br>Націсніце кнопку \"Строгі рэжым\", каб пераключыцца ў строгі рэжым сумеснага рэдагавання, каб рэдагаваць файл без іншых карыстальнікаў і адпраўляць змены толькі пасля іх захавання. Пераключацца паміж рэжымамі сумеснага рэдагавання можна з дапамогай дадатковых налад рэдактара.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Функцыі скасавання і паўтору дзеянняў выключаныя ў хуткім рэжыме сумеснага рэдагавання.", "SSE.Controllers.Main.textUndo": "Адрабіць", "SSE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "SSE.Controllers.Main.textUpdating": "Updating", "SSE.Controllers.Main.textYes": "Так", "SSE.Controllers.Main.titleLicenseExp": "Тэрмін дзеяння ліцэнзіі сышоў", "SSE.Controllers.Main.titleLicenseNotActive": "Ліцэнзія не дзейнічае", "SSE.Controllers.Main.titleServerVersion": "Рэдактар абноўлены", "SSE.Controllers.Main.titleUpdateVersion": "Version changed", "SSE.Controllers.Main.txtAccent": "Акцэнт", "SSE.Controllers.Main.txtAll": "(Усе)", "SSE.Controllers.Main.txtArt": "Увядзіце ваш тэкст", "SSE.Controllers.Main.txtBasicShapes": "Асноўныя фігуры", "SSE.Controllers.Main.txtBlank": "(пуста)", "SSE.Controllers.Main.txtButtons": "Кнопкі", "SSE.Controllers.Main.txtByField": "%1 з %2", "SSE.Controllers.Main.txtCallouts": "Удакладненні", "SSE.Controllers.Main.txtCharts": "Схемы", "SSE.Controllers.Main.txtClearFilter": "Ачысціць фільтр", "SSE.Controllers.Main.txtColLbls": "Назвы слупкоў", "SSE.Controllers.Main.txtColumn": "Слупок", "SSE.Controllers.Main.txtConfidential": "Канфідэнцыйна", "SSE.Controllers.Main.txtDate": "Дата", "SSE.Controllers.Main.txtDays": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtDiagramTitle": "Загаловак дыяграмы", "SSE.Controllers.Main.txtEditingMode": "Актывацыя рэжыму рэдагавання…", "SSE.Controllers.Main.txtErrorLoadHistory": "Не ўдалося загрузіць гісторыю", "SSE.Controllers.Main.txtFiguredArrows": "Фігурныя стрэлкі", "SSE.Controllers.Main.txtFile": "<PERSON>а<PERSON><PERSON>", "SSE.Controllers.Main.txtGrandTotal": "Агульны вынік", "SSE.Controllers.Main.txtGroup": "Згрупаваць", "SSE.Controllers.Main.txtHours": "Гад<PERSON><PERSON>ны", "SSE.Controllers.Main.txtInfo": "Інфармацыя", "SSE.Controllers.Main.txtLines": "Лініі", "SSE.Controllers.Main.txtMath": "Матэматычныя знакі", "SSE.Controllers.Main.txtMinutes": "Х<PERSON><PERSON><PERSON><PERSON><PERSON>ы", "SSE.Controllers.Main.txtMonths": "Месяцы", "SSE.Controllers.Main.txtMultiSelect": "Масавы выбар", "SSE.Controllers.Main.txtNone": "Няма", "SSE.Controllers.Main.txtOr": "%1 або %2", "SSE.Controllers.Main.txtPage": "Старонка", "SSE.Controllers.Main.txtPageOf": "Старонка %1 з %2", "SSE.Controllers.Main.txtPages": "Старонкі", "SSE.Controllers.Main.txtPicture": "Малюнак", "SSE.Controllers.Main.txtPivotTable": "PivotTable", "SSE.Controllers.Main.txtPreparedBy": "Падрыхтаваў", "SSE.Controllers.Main.txtPrintArea": "Вобласць_друкавання", "SSE.Controllers.Main.txtQuarter": "Кв", "SSE.Controllers.Main.txtQuarters": "Кварталы", "SSE.Controllers.Main.txtRectangles": "Прамавугольнікі", "SSE.Controllers.Main.txtRow": "Радок", "SSE.Controllers.Main.txtRowLbls": "Адмеціны радкоў", "SSE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "SSE.Controllers.Main.txtScheme_Aspect": "Aspect", "SSE.Controllers.Main.txtScheme_Blue": "Blue", "SSE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "SSE.Controllers.Main.txtScheme_Blue_II": "Blue II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "SSE.Controllers.Main.txtScheme_Grayscale": "Адценні шэрага", "SSE.Controllers.Main.txtScheme_Green": "Green", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "SSE.Controllers.Main.txtScheme_Marquee": "Marquee", "SSE.Controllers.Main.txtScheme_Median": "Median", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Orange", "SSE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "SSE.Controllers.Main.txtScheme_Paper": "Paper", "SSE.Controllers.Main.txtScheme_Red": "Red", "SSE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "SSE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "SSE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "SSE.Controllers.Main.txtScheme_Violet": "Violet", "SSE.Controllers.Main.txtScheme_Violet_II": "Violet II", "SSE.Controllers.Main.txtScheme_Yellow": "Yellow", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "SSE.Controllers.Main.txtSeconds": "Секунды", "SSE.Controllers.Main.txtSeries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Зноска 1 (мяжа і лінія)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Зноска 2 (мяжа і лінія)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Зноска 3 (мяжа і лінія)", "SSE.Controllers.Main.txtShape_accentCallout1": "Зноска 2 (лінія)", "SSE.Controllers.Main.txtShape_accentCallout2": "Зноска 2 (лінія)", "SSE.Controllers.Main.txtShape_accentCallout3": "Зноска 3 (лінія)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Кнопка \"Назад\"", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Кнопка \"У пачатак\"", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Пустая кнопка", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Кнопка дакумента", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Кнопка \"У канец\"", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Кнопка \"Уперад\"", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Кнопка \"Даведка\"", "SSE.Controllers.Main.txtShape_actionButtonHome": "Кнопка \"На хатнюю\"", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Кнопка \"Інфармацыя\"", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Кнопка відэа", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Кнопка вяртання", "SSE.Controllers.Main.txtShape_actionButtonSound": "Кнопка гуку", "SSE.Controllers.Main.txtShape_arc": "Дуга", "SSE.Controllers.Main.txtShape_bentArrow": "Загнутая стрэлка", "SSE.Controllers.Main.txtShape_bentConnector5": "Злучальнік водступам", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Водступ са стрэлкай", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Водступ з дзвюма стрэлкамі", "SSE.Controllers.Main.txtShape_bentUpArrow": "Загнутая стрэлка ўверх", "SSE.Controllers.Main.txtShape_bevel": "Нахілены", "SSE.Controllers.Main.txtShape_blockArc": "Арка", "SSE.Controllers.Main.txtShape_borderCallout1": "Зноска 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Зноска 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Зноска 3", "SSE.Controllers.Main.txtShape_bracePair": "Падвойныя фігурныя дужкі", "SSE.Controllers.Main.txtShape_callout1": "Зноска 1 (без мяжы)", "SSE.Controllers.Main.txtShape_callout2": "Зноска 2 (без мяжы)", "SSE.Controllers.Main.txtShape_callout3": "Зноска 3 (без мяжы)", "SSE.Controllers.Main.txtShape_can": "Цыліндр", "SSE.Controllers.Main.txtShape_chevron": "Сімвал", "SSE.Controllers.Main.txtShape_chord": "Хорда", "SSE.Controllers.Main.txtShape_circularArrow": "Кругавая стрэлка", "SSE.Controllers.Main.txtShape_cloud": "Воблака", "SSE.Controllers.Main.txtShape_cloudCallout": "Зноска-воблака", "SSE.Controllers.Main.txtShape_corner": "<PERSON><PERSON>т", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Крывая злучальная лінія", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Крывая злучальная стрэлка", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Крывая лінія з дзвюма стрэлкамі", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Загнутая ўверх стрэлка", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Загнутая ўправа стрэлка", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Загнутая ўлева стрэлка", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Загнутая ўніз стрэлка", "SSE.Controllers.Main.txtShape_decagon": "Дзесяцівугольнік", "SSE.Controllers.Main.txtShape_diagStripe": "Дыяганальная рыска", "SSE.Controllers.Main.txtShape_diamond": "Ромб", "SSE.Controllers.Main.txtShape_dodecagon": "Дванаццацівугольнік", "SSE.Controllers.Main.txtShape_donut": "Кола", "SSE.Controllers.Main.txtShape_doubleWave": "Падвойная хваля", "SSE.Controllers.Main.txtShape_downArrow": "Стрэлка ўніз", "SSE.Controllers.Main.txtShape_downArrowCallout": "Зноска са стрэлкай уніз", "SSE.Controllers.Main.txtShape_ellipse": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Загнутая ўніз стужка", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Закручаная ўверх стужка", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Блок-схема: альтэрнатыўны працэс", "SSE.Controllers.Main.txtShape_flowChartCollate": "Блок-схема: супастаўленне", "SSE.Controllers.Main.txtShape_flowChartConnector": "Блок-схема: злучальн<PERSON>к", "SSE.Controllers.Main.txtShape_flowChartDecision": "Блок-схема: рашэнне", "SSE.Controllers.Main.txtShape_flowChartDelay": "Блок-схема: затрымка", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Блок-схема: дысплэй", "SSE.Controllers.Main.txtShape_flowChartDocument": "Блок-схема: дакумент", "SSE.Controllers.Main.txtShape_flowChartExtract": "Блок-схема: выманне", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Блок-схема: даныя", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Блок-схема: унутраная памяць", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Блок-схема: магнітны дыск", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Блок-схема: памяць з непасрэдным доступам", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Блок-схема: сховішча з паслядоўным доступам", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Блок-схема: уласнаручны ўвод", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Блок-схема: уласнаручнае кіраванне", "SSE.Controllers.Main.txtShape_flowChartMerge": "Блок-схема: аб’яднанне", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Блок-схема: множны дакумент", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Блок-схема: спасылка на іншую старонку", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Блок-схема: захаваныя даныя", "SSE.Controllers.Main.txtShape_flowChartOr": "Блок-схема:альбо", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Блок-схема: прадвызначаны працэс", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Блок-схема: падрыхтоўка", "SSE.Controllers.Main.txtShape_flowChartProcess": "Блок-схема: працэс", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Блок-схема: картка", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Блок-схема: перфараваная стужка", "SSE.Controllers.Main.txtShape_flowChartSort": "Блок-схема: сартаванне", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Блок-схема: вузел сумы", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Блок-схема: знак завяршэння", "SSE.Controllers.Main.txtShape_foldedCorner": "Сагнуты кут", "SSE.Controllers.Main.txtShape_frame": "Рамка", "SSE.Controllers.Main.txtShape_halfFrame": "Палова рамкі", "SSE.Controllers.Main.txtShape_heart": "Сэрца", "SSE.Controllers.Main.txtShape_heptagon": "Сямівугольнік", "SSE.Controllers.Main.txtShape_hexagon": "Шасцівугольнік", "SSE.Controllers.Main.txtShape_homePlate": "Пяцівугольнік", "SSE.Controllers.Main.txtShape_horizontalScroll": "Гарызантальны скрутак", "SSE.Controllers.Main.txtShape_irregularSeal1": "Выбліск 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Выбліск 2", "SSE.Controllers.Main.txtShape_leftArrow": "Стрэлка ўлева", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Зноска са стрэлкай улева", "SSE.Controllers.Main.txtShape_leftBrace": "Левая фігурная дужка", "SSE.Controllers.Main.txtShape_leftBracket": "Левая дужка", "SSE.Controllers.Main.txtShape_leftRightArrow": "Стрэлкі ўлева-ўправа", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Зноска са стрэлкамі ўлева-ўправа", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Стрэлкі ўлева-ўправа-ўверх", "SSE.Controllers.Main.txtShape_leftUpArrow": "Стрэлкі ўлева-ўверх", "SSE.Controllers.Main.txtShape_lightningBolt": "Маланка", "SSE.Controllers.Main.txtShape_line": "Лінія", "SSE.Controllers.Main.txtShape_lineWithArrow": "Стрэлка", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Падвойная стрэлка", "SSE.Controllers.Main.txtShape_mathDivide": "Дзяленне", "SSE.Controllers.Main.txtShape_mathEqual": "Роўна", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Множанне", "SSE.Controllers.Main.txtShape_mathNotEqual": "Не роўна", "SSE.Controllers.Main.txtShape_mathPlus": "Плюс", "SSE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_noSmoking": "Забаронена", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Стрэлка ўправа з выразам", "SSE.Controllers.Main.txtShape_octagon": "Васьмівугольнік", "SSE.Controllers.Main.txtShape_parallelogram": "Парал<PERSON><PERSON><PERSON><PERSON><PERSON>ам", "SSE.Controllers.Main.txtShape_pentagon": "Пяцівугольнік", "SSE.Controllers.Main.txtShape_pie": "Кругавая", "SSE.Controllers.Main.txtShape_plaque": "Под<PERSON><PERSON>с", "SSE.Controllers.Main.txtShape_plus": "Плюс", "SSE.Controllers.Main.txtShape_polyline1": "Крывая", "SSE.Controllers.Main.txtShape_polyline2": "Адвольная форма", "SSE.Controllers.Main.txtShape_quadArrow": "Чацвярная стрэлка", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Зноска з чацвярной стрэлкай", "SSE.Controllers.Main.txtShape_rect": "Прамавугольнік", "SSE.Controllers.Main.txtShape_ribbon": "Стужка ўніз", "SSE.Controllers.Main.txtShape_ribbon2": "Стужка ўверх", "SSE.Controllers.Main.txtShape_rightArrow": "Стрэлка ўправа", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Зноска са стрэлкай управа", "SSE.Controllers.Main.txtShape_rightBrace": "Правая фігурная дужка", "SSE.Controllers.Main.txtShape_rightBracket": "Правая дужка", "SSE.Controllers.Main.txtShape_round1Rect": "Прамавугольнік з адным скругленым вуглом", "SSE.Controllers.Main.txtShape_round2DiagRect": "Прамавугольнік з двума скругленымі супрацьлеглымі вугламі", "SSE.Controllers.Main.txtShape_round2SameRect": "Прамавугольнік з двума скругленымі суседнімі вугламі", "SSE.Controllers.Main.txtShape_roundRect": "Прамавугольнік са скругленымі вугламі", "SSE.Controllers.Main.txtShape_rtTriangle": "Прамавугольны трохвугольнік", "SSE.Controllers.Main.txtShape_smileyFace": "Твар з усмешкай", "SSE.Controllers.Main.txtShape_snip1Rect": "Прамавугольнік з адным абрэзаным вуглом", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Прамавугольнік з двума абрэзанымі супрацьлеглымі вугламі", "SSE.Controllers.Main.txtShape_snip2SameRect": "Прамавугольнік з двума абрэзанымі суседнімі вугламі", "SSE.Controllers.Main.txtShape_snipRoundRect": "Прамавугольнік з абрэзаным скругленым вуглом", "SSE.Controllers.Main.txtShape_spline": "Крывая", "SSE.Controllers.Main.txtShape_star10": "10-канцовая зорка", "SSE.Controllers.Main.txtShape_star12": "12-канцовая зорка", "SSE.Controllers.Main.txtShape_star16": "16-канцовая зорка", "SSE.Controllers.Main.txtShape_star24": "24-канцовая зорка", "SSE.Controllers.Main.txtShape_star32": "32-канцовая зорка", "SSE.Controllers.Main.txtShape_star4": "4-канцовая зорка", "SSE.Controllers.Main.txtShape_star5": "5-канцовая зорка", "SSE.Controllers.Main.txtShape_star6": "6-канцовая зорка", "SSE.Controllers.Main.txtShape_star7": "7-канцовая зорка", "SSE.Controllers.Main.txtShape_star8": "8-канцовая зорка", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Рыскавая стрэлка ўправа", "SSE.Controllers.Main.txtShape_sun": "Сонца", "SSE.Controllers.Main.txtShape_teardrop": "Кропля", "SSE.Controllers.Main.txtShape_textRect": "Над<PERSON><PERSON>с", "SSE.Controllers.Main.txtShape_trapezoid": "Трапецыя", "SSE.Controllers.Main.txtShape_triangle": "Трохвугольнік", "SSE.Controllers.Main.txtShape_upArrow": "Стрэлка ўверх", "SSE.Controllers.Main.txtShape_upArrowCallout": "Зноска са стрэлкай уверх", "SSE.Controllers.Main.txtShape_upDownArrow": "Стрэлкі ўверх-уніз", "SSE.Controllers.Main.txtShape_uturnArrow": "Разгорнутая стрэлка", "SSE.Controllers.Main.txtShape_verticalScroll": "Вертыкальны скрутак", "SSE.Controllers.Main.txtShape_wave": "Х<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Авальная зноска", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Прамавугольная зноска", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Скругленая прамавугольная зноска", "SSE.Controllers.Main.txtSheet": "<PERSON>р<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSlicer": "Зводка", "SSE.Controllers.Main.txtStarsRibbons": "Зоркі і стужкі", "SSE.Controllers.Main.txtStyle_Bad": "Дрэнны", "SSE.Controllers.Main.txtStyle_Calculation": "Вылічэнне", "SSE.Controllers.Main.txtStyle_Check_Cell": "Праверачная ячэйка", "SSE.Controllers.Main.txtStyle_Comma": "Фінансавы", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Удакладненне", "SSE.Controllers.Main.txtStyle_Good": "До<PERSON><PERSON>ы", "SSE.Controllers.Main.txtStyle_Heading_1": "Загаловак 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Загаловак 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Загаловак 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Загаловак 4", "SSE.Controllers.Main.txtStyle_Input": "Увод", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Звязаная ячэйка", "SSE.Controllers.Main.txtStyle_Neutral": "Нейтральны", "SSE.Controllers.Main.txtStyle_Normal": "Звычайны", "SSE.Controllers.Main.txtStyle_Note": "Нататка", "SSE.Controllers.Main.txtStyle_Output": "Вывад", "SSE.Controllers.Main.txtStyle_Percent": "Адсотак", "SSE.Controllers.Main.txtStyle_Title": "Назва", "SSE.Controllers.Main.txtStyle_Total": "<PERSON>г<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Warning_Text": "Тэкст папярэджання", "SSE.Controllers.Main.txtTab": "Укладка", "SSE.Controllers.Main.txtTable": "Табліца", "SSE.Controllers.Main.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtUnlock": "Разблакаваць", "SSE.Controllers.Main.txtUnlockRange": "Разблакаваць дыяпазон", "SSE.Controllers.Main.txtUnlockRangeDescription": "Каб змяніць гэты дыяпазон, увядзіце пароль:", "SSE.Controllers.Main.txtUnlockRangeWarning": "Дыяпазон, які вы спрабуеце змяніць, абаронены паролем.", "SSE.Controllers.Main.txtValues": "Значэнні", "SSE.Controllers.Main.txtView": "View", "SSE.Controllers.Main.txtXAxis": "Вось Х", "SSE.Controllers.Main.txtYAxis": "Вось Y", "SSE.Controllers.Main.txtYears": "Гады", "SSE.Controllers.Main.unknownErrorText": "Невядомая памылка.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Ваш браўзер не падтрымліваецца.", "SSE.Controllers.Main.uploadDocExtMessage": "Невядомы фармат дакумента.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Ніводнага дакумента не загружана. ", "SSE.Controllers.Main.uploadDocSizeMessage": "Перасягнуты максімальны памер дакумента.", "SSE.Controllers.Main.uploadImageExtMessage": "Невядомы фармат выявы.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Выяў не запампавана.", "SSE.Controllers.Main.uploadImageSizeMessage": "Занадта вялікая выява. Максімальны памер - 25 МБ.", "SSE.Controllers.Main.uploadImageTextText": "Запампоўванне выявы…", "SSE.Controllers.Main.uploadImageTitleText": "Запампоўванне выявы", "SSE.Controllers.Main.waitText": "Калі ласка, пачакайце...", "SSE.Controllers.Main.warnBrowserIE9": "У IE9 праграма працуе вельмі павольна. Выкарыстоўвайце IE10 альбо пазнейшыя версіі.", "SSE.Controllers.Main.warnBrowserZoom": "Бягучыя налады маштабу старонкі падтрымліваюцца браўзерам толькі часткова. Калі ласка, скінце да прадвызначанага значэння націснуўшы Ctrl+О.", "SSE.Controllers.Main.warnLicenseAnonymous": "Access denied for anonymous users.<br>This document will be opened for viewing only.", "SSE.Controllers.Main.warnLicenseBefore": "Ліцэнзія не дзейнічае.<br>Звярніцеся да адміністратара.", "SSE.Controllers.Main.warnLicenseExceeded": "Вы дасягнулі абмежавання па адначасовай колькасці падлучэнняў да рэдактараў %1.Гэты дакумент будзе адкрыты для прагляду.<br>Звяжыцеся з адміністратарам, каб даведацца больш.", "SSE.Controllers.Main.warnLicenseExp": "Тэрмін дзеяння ліцэнзіі сышоў.<br>Калі ласка, абнавіце ліцэнзію, пасля абнавіце старонку.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Тэрмін дзеяння ліцэнзіі сышоў.<br>У вас няма доступу да функцый рэдагавання дакументаў.<br>Калі ласка, звярніцеся да адміністратара.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Патрэбна абнавіць ліцэнзію.<br>У вас абмежаваны доступ да функцый рэдагавання дакументаў.<br>Каб атрымаць поўны доступ, звярніцеся да адміністратара", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Вы дасягнулі абмежавання па адначасовай колькасці падлучэнняў да рэдактараў %1.<br>Звяжыцеся з адміністратарам, каб даведацца больш.", "SSE.Controllers.Main.warnNoLicense": "Вы дасягнулі абмежавання па адначасовай колькасці падлучэнняў да рэдактараў %1.Гэты дакумент будзе адкрыты для прагляду.<br>Напішыце ў аддзел продажу %1,каб абмеркаваць асабістыя ўмовы ліцэнзіявання.", "SSE.Controllers.Main.warnNoLicenseUsers": "Вы дасягнулі абмежавання па адначасовай колькасці падлучэнняў да рэдактараў %1.<br>Напішыце ў аддзел продажу %1,каб абмеркаваць асабістыя ўмовы ліцэнзіявання.", "SSE.Controllers.Main.warnProcessRightsChange": "Вам адмоўлена ў правах на рэдагаванне гэтага файла.", "SSE.Controllers.PivotTable.strSheet": "<PERSON>р<PERSON><PERSON><PERSON>", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "No actions with calculated items are allowed for this active cell.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Calculated items do not work with custom subtotals.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "An item name cannot be found. Check that you've typed name correctly and the item is present in the PivotTable report.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Averages, standard deviations, and variances are not supported when a PivotTable report has calculated items.", "SSE.Controllers.Print.strAllSheets": "Усе аркушы", "SSE.Controllers.Print.textFirstCol": "Першы слупок", "SSE.Controllers.Print.textFirstRow": "Першы радок", "SSE.Controllers.Print.textFrozenCols": "Замацаваныя слупкі", "SSE.Controllers.Print.textFrozenRows": "Замацаваныя радкі", "SSE.Controllers.Print.textInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Controllers.Print.textNoRepeat": "Не паўтараць", "SSE.Controllers.Print.textRepeat": "Паўтараць…", "SSE.Controllers.Print.textSelectRange": "Абраць дыяпазон", "SSE.Controllers.Print.txtCustom": "Адвольны", "SSE.Controllers.Print.txtZoomToPage": "Zoom to page", "SSE.Controllers.Search.textInvalidRange": "ПАМЫЛКА! Хібны дыяпазон ячэек", "SSE.Controllers.Search.textNoTextFound": "Даныя не знойдзеныя. Калі ласка, змяніце параметры пошуку.", "SSE.Controllers.Search.textReplaceSkipped": "Заменена. Прапушчана ўваходжанняў: {0}.", "SSE.Controllers.Search.textReplaceSuccess": "Пошук завершаны. Зам<PERSON><PERSON><PERSON><PERSON> {0} супадзенне(яў)", "SSE.Controllers.Statusbar.errorLastSheet": "Працоўная кніга мусіць утрымліваць не менш за адзін бачны аркуш.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Не ўдалося выдаліць аркуш.", "SSE.Controllers.Statusbar.strSheet": "<PERSON>р<PERSON><PERSON><PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>Злучэнне страчана</b><br>Выконваецца спроба падлучэння. Праверце налады.", "SSE.Controllers.Statusbar.textSheetViewTip": "Вы знаходзіцеся ў рэжыме мініяцюры аркуша. Фільтры і сартаванне бачныя толькі вам і тым, хто працуе ў гэтым рэжыме.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Зараз вы ў рэжыме прагляду аркуша. Фільтры бачыце толькі вы і тыя, хто знаходзіцца ў гэтым рэжыме прагляду.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Аркуш можа змяшчаць даныя. Працягнуць аперацыю?", "SSE.Controllers.Statusbar.zoomText": "Ма<PERSON><PERSON><PERSON>б {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Шрыфт, які вы хочаце захаваць, недаступны на гэтай прыладзе.<br>Стыль тэксту будзе паказвацца пры дапамозе аднаго з сістэмных шрыфтоў. Захаваны шрыфт будзе выкарыстоўвацца, калі ён стане даступным.<br>Працягнуць?", "SSE.Controllers.Toolbar.errorComboSeries": "Каб стварыць камбінаваную дыяграму, абярыце прынамсі два рады даных.", "SSE.Controllers.Toolbar.errorMaxPoints": "Максімальная колькасць кропак у шэрагу для дыяграмы - 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "ПАМЫЛКА! максімальная колькасць шэрагаў даных у адной дыяграме - 255", "SSE.Controllers.Toolbar.errorStockChart": "Хібны парадак радкоў. Каб стварыць біржавую дыяграму размясціце даныя ў наступным парадку:<br>кошт адкрыцця, максімальны кошт, мінімальны кошт, кошт закрыцця.", "SSE.Controllers.Toolbar.helpCalcItems": "Work with calculated items in Pivot Tables.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Calculated items", "SSE.Controllers.Toolbar.helpFastUndo": "Easily undo changes while collaborating on sheets in Fast mode.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "\"Undo\" in real-time co-editing", "SSE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "SSE.Controllers.Toolbar.textAccent": "Дыякрытычныя знакі", "SSE.Controllers.Toolbar.textBracket": "Дужкі", "SSE.Controllers.Toolbar.textDirectional": "Кірункі", "SSE.Controllers.Toolbar.textFontSizeErr": "Уведзена хібнае значэнне.<br>Увядзіце лік ад 1 да 409", "SSE.Controllers.Toolbar.textFraction": "Дробы", "SSE.Controllers.Toolbar.textFunction": "Функцыі", "SSE.Controllers.Toolbar.textIndicator": "Індыкатары", "SSE.Controllers.Toolbar.textInsert": "Уставіць", "SSE.Controllers.Toolbar.textIntegral": "Інтэгралы", "SSE.Controllers.Toolbar.textLargeOperator": "Буйныя аператары", "SSE.Controllers.Toolbar.textLimitAndLog": "Ліміты і лагарыфмы", "SSE.Controllers.Toolbar.textLongOperation": "Працяглая аперацыя", "SSE.Controllers.Toolbar.textMatrix": "Матрыцы", "SSE.Controllers.Toolbar.textOperator": "Аперата<PERSON>ы", "SSE.Controllers.Toolbar.textPivot": "Зводная табліца", "SSE.Controllers.Toolbar.textRadical": "Радыкалы", "SSE.Controllers.Toolbar.textRating": "Рэйтынг", "SSE.Controllers.Toolbar.textRecentlyUsed": "Апошнія выкарыстаныя", "SSE.Controllers.Toolbar.textScript": "Індэксы", "SSE.Controllers.Toolbar.textShapes": "Фігуры", "SSE.Controllers.Toolbar.textSymbols": "Сімвалы", "SSE.Controllers.Toolbar.textWarning": "Увага", "SSE.Controllers.Toolbar.txtAccent_Accent": "Націск", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Стрэлка ўправа-ўлева зверху", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Стрэлка ўлева зверху", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Стрэлка ўправа зверху", "SSE.Controllers.Toolbar.txtAccent_Bar": "Лінія", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Лінія знізу", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Рыска зверху", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Формула ў рамцы (з запаўняльнікам)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Формула ў рамцы (прыклад)", "SSE.Controllers.Toolbar.txtAccent_Check": "Сцяг", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Фігурная дужка знізу", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Фігурная дужка зверху", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Вектар А", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC з радком уверсе", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y з лініяй зверху", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Тры кропкі", "SSE.Controllers.Toolbar.txtAccent_DDot": "Дзве кропкі", "SSE.Controllers.Toolbar.txtAccent_Dot": "Кропка", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Падвойная рыска зверху", "SSE.Controllers.Toolbar.txtAccent_Grave": "Націск", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Сімвал групавання знізу", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Сімвал групавання зверху", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Гар<PERSON>ун улева зверху", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Гарпун управа зверху", "SSE.Controllers.Toolbar.txtAccent_Hat": "Вечка", "SSE.Controllers.Toolbar.txtAccent_Smile": "Скарочанасць", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Тыльда", "SSE.Controllers.Toolbar.txtBracket_Angle": "Вуглавыя дужкі", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Вуглавыя дужкі з падзяляльнікам", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Вуглавыя дужкі з двума падзяляльнікамі", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Правая вуглавая дужка", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Левая вуглавая дужка", "SSE.Controllers.Toolbar.txtBracket_Curve": "Фігурныя дужкі", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Фігурныя дужкі з падзяляльнікам", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Правая фігурная дужка", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Левая фігурная дужка", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Наборы (дзве ўмовы)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Наборы (тры ўмовы)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Набор аб’ектаў", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Набор аб’ектаў у круглых дужках", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Наборы (прыклад)", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Бінамінальны каэфіцыент", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Бінамінальны каэфіцыент у вуглавых дужках", "SSE.Controllers.Toolbar.txtBracket_Line": "Вертыкальныя лініі", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Правая вертыкальная лінія", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Левая вертыкальная лінія", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Падвойныя вертыкальныя лініі", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Правая падвойная вертыкальная лінія", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Левыя падвойныя вертыкальныя лініі", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Закрытыя дужкі", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Правы ніжні ліміт", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Левая ніжняя мяжа", "SSE.Controllers.Toolbar.txtBracket_Round": "Круглыя дужкі", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Круглыя дужкі з падзяляльнікам", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Правая круглая дужка", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Левая круглая дужка", "SSE.Controllers.Toolbar.txtBracket_Square": "Квадратныя дужкі", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Запаўняльнік паміж дзвюма правымі квадратнымі дужкамі", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Адваротныя квадратныя дужкі", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Правая квадратная дужка", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Левая квадратная дужка", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Запаўняльнік паміж дзвюма левымі квадратнымі дужкамі", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Падвойныя квадратныя дужкі", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Правая падвойная квадратная дужка", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Левая падвойная квадратная дужка", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Дужкі", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Правы верхні ліміт", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Левая верхняя мяжа", "SSE.Controllers.Toolbar.txtDeleteCells": "Выдаліць ячэйкі", "SSE.Controllers.Toolbar.txtExpand": "Пашырыць і ўпарадкаваць", "SSE.Controllers.Toolbar.txtExpandSort": "Даныя побач з абраным дыяпазонам не будуць сартавацца. Хочаце пашырыць абраны дыяпазон, каб уключыць даныя з супольных ячэек, альбо працягнуць сартаванне толькі абранага дыяпазону?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Дыяганальны дроб", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "dy па-над dx", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "верхняя дэльта y над верхняй дэльтай x", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "частковая y па частковай x", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "дэльта y праз дэльта x", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Лінейны дроб", "SSE.Controllers.Toolbar.txtFractionPi_2": "Пі падзяліць на два", "SSE.Controllers.Toolbar.txtFractionSmall": "Маленькі дроб", "SSE.Controllers.Toolbar.txtFractionVertical": "Вертыкальны дроб", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Арккосінус", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Гіпербалічны арккосінус", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Арккат<PERSON><PERSON><PERSON><PERSON>нс", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Гіпербалічны арккатангенс", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Арккасеканс", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Гіпербалічны арккасеканс", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Арксеканс", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Гіпербалічны арксеканс", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Аркс<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Гіпербалічны арксінус", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Арк<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Гіпербалічны арктангенс", "SSE.Controllers.Toolbar.txtFunction_Cos": "Ко<PERSON><PERSON><PERSON><PERSON>с", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Гіпербалічны косінус", "SSE.Controllers.Toolbar.txtFunction_Cot": "Ка<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Coth": "Гіпербалічны катангенс", "SSE.Controllers.Toolbar.txtFunction_Csc": "Касеканс", "SSE.Controllers.Toolbar.txtFunction_Csch": "Гіпербалічны касеканс", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sin θ", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Формула тангенса", "SSE.Controllers.Toolbar.txtFunction_Sec": "Секанс", "SSE.Controllers.Toolbar.txtFunction_Sech": "Гіпербалічны секанс", "SSE.Controllers.Toolbar.txtFunction_Sin": "Сінус", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Гіпербалічны сінус", "SSE.Controllers.Toolbar.txtFunction_Tan": "Та<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Гіпербалічны тангенс", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Адвольна", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Даныя і мадэль", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Добры, дрэнны і нейтральны", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "Няма назвы", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Лічбавы фармат", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Стыль ячэек з тэмай", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Назвы і загалоўкі", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Адвольна", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Цёмныя", "SSE.Controllers.Toolbar.txtGroupTable_Light": "Светлыя", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Сярэднія", "SSE.Controllers.Toolbar.txtInsertCells": "Уставіць ячэйкі", "SSE.Controllers.Toolbar.txtIntegral": "Інтэграл", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Дыферэнцыял dθ", "SSE.Controllers.Toolbar.txtIntegral_dx": "Дыферэнцыял dx", "SSE.Controllers.Toolbar.txtIntegral_dy": "Дыферэнцыял dy", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Інтэграл з лімітамі і накапленнем", "SSE.Controllers.Toolbar.txtIntegralDouble": "Падвойны інтэграл", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Падвойны інтэграл з лімітамі з накапленнем", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Падвойны інтэграл з лімітамі", "SSE.Controllers.Toolbar.txtIntegralOriented": "Контурны інтэграл", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Контурны інтэграл з лімітамі і накапленнем", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Паверхневы інтэграл", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Паверхневы інтэграл з лімітамі з накапленнем", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Паверхневы інтэграл з лімітамі", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Контурны інтэграл з лімітамі", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Аб’ёмны інтэграл", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Аб’ёмны інтэграл з лімітамі з накапленнем", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Аб’ёмны інтэграл з лімітамі", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Інтэграл з лімітамі", "SSE.Controllers.Toolbar.txtIntegralTriple": "Патройны інтэграл", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Патройны інтэграл з лімітамі з накапленнем", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Патройны інтэграл з лімітамі", "SSE.Controllers.Toolbar.txtInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Лагічнае \"І\"", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Ла<PERSON><PERSON><PERSON><PERSON><PERSON> \"І\" з ніжнім лімітам", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Лаг<PERSON><PERSON><PERSON><PERSON> \"І\" з лімітамі", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Лаг<PERSON><PERSON><PERSON><PERSON> \"І\" з ніжнім падрадковым лімітам", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Лагічна<PERSON> \"І\" з надрадковым/падрадковым лімітам", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Супольны здабытак", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Супольны здабытак з ніжнім лімітам", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Супольны здабытак з лімітамі", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Супольны здабытак з ніжнім лімітам", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Супольны здабытак з верхнім і ніжнім лімітамі", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Сумаванне па k ад n з выбарам k", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Сумаванне ад і роўна нуль да n", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Сумаванне на прыкладзе двух індэксаў", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Прыклад здабытку", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Прыклад аб'яднання", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Лагічнае \"Або\"", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Лаг<PERSON><PERSON><PERSON><PERSON> \"Або\" з ніжнім лімітам", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Лаг<PERSON>ч<PERSON><PERSON> \"Або\" з лімітамі", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Лагічна<PERSON> \"Або\" з ніжнім падрадковым лімітам", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Лагічнае \"Або\" з падрадковым/надрадковым лімітам", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Перакрыжаванне", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Перакрыжаванне з ніжнім лімітам", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Перакрыжаванне з лімітамі", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Перакрыжаванне з ніжнім лімітам", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Перакрыжаванне з падрадковымі/надрадковымі лімітамі", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Здабытак", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Здабытак з ніжнім лімітам", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Здабытак з лімітамі", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Здабытак з ніжнім падрадковым лімітам", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Здабытак з падрадковым/надрадковым лімітам", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Сума", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Сумаванне з ніжнім лімітам", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Сумаванне з лімітамі", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Сумаванне з індэксам ніжняга ліміту", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Сумаванне з падрадковым/надрадковым лімітамі", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Аб’яднанне", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Аб’яднанне з ніжнім лімітам", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Аб’яднанне з лімітамі", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Аб’яднанне з ніжнім падрадковым лімітам", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Аб’яднанне з падрадковым/надрадковым лімітамі", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Прыклад ліміту", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Прыклад максімуму", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Натуральны лагарыфм", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Лагарыфм", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Лагарыфм", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Максімум", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Мін<PERSON><PERSON>ум", "SSE.Controllers.Toolbar.txtLockSort": "Знойдзены даныя, якія знаходзяцца далей за абраны дыяпазон, але вы не маеце правоў на змену гэтых ячэек<br>Хочаце працягнуць працу з абраным дыяпазонам?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "Пустая матрыца 1х2", "SSE.Controllers.Toolbar.txtMatrix_1_3": "Пустая матрыца 1х3", "SSE.Controllers.Toolbar.txtMatrix_2_1": "Пустая матрыца 2х1", "SSE.Controllers.Toolbar.txtMatrix_2_2": "Пустая матрыца 2х2", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Пустая матрыца 2 х 2 у падвойных вертыкальных лініях", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Пусты вызначальнік 2 x 2", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Пустая матрыца 2 х 2 у круглых дужках", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Пустая матрыца 2 x 2 у дужках", "SSE.Controllers.Toolbar.txtMatrix_2_3": "Пустая матрыца 2х3", "SSE.Controllers.Toolbar.txtMatrix_3_1": "Пустая матрыца 3х1", "SSE.Controllers.Toolbar.txtMatrix_3_2": "Пустая матрыца 3х2", "SSE.Controllers.Toolbar.txtMatrix_3_3": "Пустая матрыца 3х3", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Кропкі базавай лініі", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Кропкі пасярэдзіне", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Дыяганальныя кропкі", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Вертыкальныя кропкі", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Разрэджаная матрыца ў круглых дужках", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Разрэджаная матрыца ў квадратных дужках", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "Адзінкавая матрыца 2х2 з нулямі", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Адзінкавая матрыца 3х3 з пустымі ячэйкамі не па дыяганалі", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "Адзінкавая матрыца 3х3 з нулямі", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Адзінкавая матрыца 3х3 з пустымі недыяганальнымі ячэйкамі", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Стрэлка ўправа-ўлева знізу", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Стрэлка ўправа-ўлева зверху", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Стрэлка ўлева знізу", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Стрэлка ўлева зверху", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Стрэлка ўправа знізу", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Стрэлка ўправа зверху", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Двукроп’е роўна", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Выхад", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Дэльта выхаду", "SSE.Controllers.Toolbar.txtOperator_Definition": "Роўна па вызначэнні", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Дэльта роўная", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Падвойная стрэлка ўправа-ўлева знізу", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Падвойная стрэлка ўправа-ўлева зверху", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Стрэлка ўлева знізу", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Стрэлка ўлева зверху", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Стрэлка ўправа знізу", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Стрэлка ўправа зверху", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Роўна роўна", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Мінус роўна", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Плюс роўна", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Адзінкі вымярэння", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Правая частка квадратнага раўнання", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Квадратны корань з а у квадраце плюс b у квадраце", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Квадратны корань са ступенню", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Кубічны корань", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Радыкал са ступенню", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Квадратны корань", "SSE.Controllers.Toolbar.txtScriptCustom_1": "x з індэксам y у квадраце", "SSE.Controllers.Toolbar.txtScriptCustom_2": "e у -iωt", "SSE.Controllers.Toolbar.txtScriptCustom_3": "квадрат x", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Y, надрадковы індэкс n злева, падрадковы індэкс 1 справа", "SSE.Controllers.Toolbar.txtScriptSub": "Ніжні індэкс", "SSE.Controllers.Toolbar.txtScriptSubSup": "Ніжні і верхні індэксы", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Ніжні і верхні індэксы злева", "SSE.Controllers.Toolbar.txtScriptSup": "Верхні індэкс", "SSE.Controllers.Toolbar.txtSorting": "Парадкаванне", "SSE.Controllers.Toolbar.txtSortSelected": "Парадкаваць абранае", "SSE.Controllers.Toolbar.txtSymbol_about": "Прыблізна", "SSE.Controllers.Toolbar.txtSymbol_additional": "Дапаўненне", "SSE.Controllers.Toolbar.txtSymbol_aleph": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Альфа", "SSE.Controllers.Toolbar.txtSymbol_approx": "Амаль роўна", "SSE.Controllers.Toolbar.txtSymbol_ast": "Аператар \"зорачка\"", "SSE.Controllers.Toolbar.txtSymbol_beta": "Бэта", "SSE.Controllers.Toolbar.txtSymbol_beth": "Бэт", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Аператар \"адзнака\"", "SSE.Controllers.Toolbar.txtSymbol_cap": "Перакрыжаванне", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Кубічны корань", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Гарызантальнае шматкроп’е пасярэдзіне", "SSE.Controllers.Toolbar.txtSymbol_celsius": "Градусы Цэльсія", "SSE.Controllers.Toolbar.txtSymbol_chi": "Хі", "SSE.Controllers.Toolbar.txtSymbol_cong": "Прыблізна роўна", "SSE.Controllers.Toolbar.txtSymbol_cup": "Аб’яднанне", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Дыяганальнае шматкроп’е ўніз управа", "SSE.Controllers.Toolbar.txtSymbol_degree": "<PERSON>ра<PERSON><PERSON><PERSON>ы", "SSE.Controllers.Toolbar.txtSymbol_delta": "Дэльта", "SSE.Controllers.Toolbar.txtSymbol_div": "Знак дзялення", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Стрэлка ўніз", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Пусты набор", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Эп<PERSON><PERSON>лон", "SSE.Controllers.Toolbar.txtSymbol_equals": "Роўна", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Адпавядае", "SSE.Controllers.Toolbar.txtSymbol_eta": "Эта", "SSE.Controllers.Toolbar.txtSymbol_exists": "Існуе", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Фактарыял", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Градусы Фарэнгейта", "SSE.Controllers.Toolbar.txtSymbol_forall": "Для ўсіх", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Гама", "SSE.Controllers.Toolbar.txtSymbol_geq": "Больш альбо роўна", "SSE.Controllers.Toolbar.txtSymbol_gg": "Значна больш", "SSE.Controllers.Toolbar.txtSymbol_greater": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_in": "З’яўляецца элементам", "SSE.Controllers.Toolbar.txtSymbol_inc": "Інкрэмент", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Бясконцасць", "SSE.Controllers.Toolbar.txtSymbol_iota": "Іота", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Капа", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Лямбда", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Стрэлка ўлева", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Стрэлкі ўлева і ўправа", "SSE.Controllers.Toolbar.txtSymbol_leq": "Мен<PERSON> альбо роўна", "SSE.Controllers.Toolbar.txtSymbol_less": "Ме<PERSON><PERSON> за", "SSE.Controllers.Toolbar.txtSymbol_ll": "<PERSON>начна менш", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "Мін<PERSON>с плюс", "SSE.Controllers.Toolbar.txtSymbol_mu": "Мю", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Набла", "SSE.Controllers.Toolbar.txtSymbol_neq": "Не роўна", "SSE.Controllers.Toolbar.txtSymbol_ni": "Утрымлівае як член", "SSE.Controllers.Toolbar.txtSymbol_not": "Знак адмаўлення", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Не існуе", "SSE.Controllers.Toolbar.txtSymbol_nu": "Ню", "SSE.Controllers.Toolbar.txtSymbol_o": "Амік<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_omega": "Амега", "SSE.Controllers.Toolbar.txtSymbol_partial": "Часны дыферэнцыял", "SSE.Controllers.Toolbar.txtSymbol_percent": "У адсотках", "SSE.Controllers.Toolbar.txtSymbol_phi": "Фі", "SSE.Controllers.Toolbar.txtSymbol_pi": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_plus": "Плюс", "SSE.Controllers.Toolbar.txtSymbol_pm": "Плюс мінус", "SSE.Controllers.Toolbar.txtSymbol_propto": "Прапар<PERSON>ыйна", "SSE.Controllers.Toolbar.txtSymbol_psi": "Пс<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Корань чацвёртай ступені", "SSE.Controllers.Toolbar.txtSymbol_qed": "Канец доказу", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Дыяганальнае шматкроп’е ўверх управа", "SSE.Controllers.Toolbar.txtSymbol_rho": "Ро", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Стрэлка ўправа", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Сігма", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Знак радыкала", "SSE.Controllers.Toolbar.txtSymbol_tau": "Тау", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Такім чынам", "SSE.Controllers.Toolbar.txtSymbol_theta": "Тэта", "SSE.Controllers.Toolbar.txtSymbol_times": "Знак множання", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Стрэлка ўверх", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Іп<PERSON><PERSON>лон", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Эпсілон (варыянт)", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Фі (варыянт)", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Пі (варыянт)", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Ро (варыянт) ", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Сігма (варыянт)", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Тэта (варыянт)", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Вертыкальнае шматкроп’е", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Ксі", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Дзэта", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Цёмны стыль табліцы", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Светлы стыль табліцы", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Сярэдні стыль табліцы", "SSE.Controllers.Toolbar.warnLongOperation": "Для завяршэння аперацыі, якую вы хочаце выканаць, можа спатрэбіцца шмат часу.<br>Сапраўды хочаце працягнуць?", "SSE.Controllers.Toolbar.warnMergeLostData": "У аб’яднанай ячэйцы застануцца толькі даныя з левай верхняй ячэйкі.<br>Сапраўды хочаце працягнуць?", "SSE.Controllers.Toolbar.warnNoRecommended": "To create a chart, select the cells that contain the data you'd like to use.<br>If you have names for the rows and columns and you'd like use them as labels, include them in your selection.", "SSE.Controllers.Viewport.textFreezePanes": "Замацаваць вобласці", "SSE.Controllers.Viewport.textFreezePanesShadow": "Паказваць цень для замацаваных панэляў", "SSE.Controllers.Viewport.textHideFBar": "Схаваць панэль формул", "SSE.Controllers.Viewport.textHideGridlines": "Схаваць лініі сеткі", "SSE.Controllers.Viewport.textHideHeadings": "Схаваць загалоўкі", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Дзесятковы падзяляльнік", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Падзяляльнік разрадаў тысяч", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Налады вызначэння лічбавых даных", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Кла<PERSON><PERSON>фіка<PERSON>ар тэксту", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Дадатковыя налады", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(няма)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Адвольны фільтр", "SSE.Views.AutoFilterDialog.textAddSelection": "Дад<PERSON><PERSON>ь абранае ў фільтр", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Пустыя}", "SSE.Views.AutoFilterDialog.textSelectAll": "Аб<PERSON><PERSON><PERSON><PERSON> усё", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Абраць усе вынікі пошуку", "SSE.Views.AutoFilterDialog.textWarning": "Увага", "SSE.Views.AutoFilterDialog.txtAboveAve": "Вышэй за сярэдняе", "SSE.Views.AutoFilterDialog.txtAfter": "After...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "All dates in the period", "SSE.Views.AutoFilterDialog.txtApril": "кра<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtAugust": "жнівень", "SSE.Views.AutoFilterDialog.txtBefore": "Before...", "SSE.Views.AutoFilterDialog.txtBegins": "Пачынаецца з…", "SSE.Views.AutoFilterDialog.txtBelowAve": "Ніжэй за сярэдняе", "SSE.Views.AutoFilterDialog.txtBetween": "<PERSON>а<PERSON><PERSON><PERSON>…", "SSE.Views.AutoFilterDialog.txtClear": "Ачысціць", "SSE.Views.AutoFilterDialog.txtContains": "Змяшчае…", "SSE.Views.AutoFilterDialog.txtDateFilter": "Date filter", "SSE.Views.AutoFilterDialog.txtDecember": "Снежань", "SSE.Views.AutoFilterDialog.txtEmpty": "Увядзіце значэнне для фільтрацыі", "SSE.Views.AutoFilterDialog.txtEnds": "Заканчваецца на…", "SSE.Views.AutoFilterDialog.txtEquals": "Роўна…", "SSE.Views.AutoFilterDialog.txtFebruary": "Люты", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Фільтр па колеру ячэек", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Фільтр па колеру шрыфту ", "SSE.Views.AutoFilterDialog.txtGreater": "Больш за…", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Больш альбо роўна…", "SSE.Views.AutoFilterDialog.txtJanuary": "Студзень", "SSE.Views.AutoFilterDialog.txtJuly": "Ліпень", "SSE.Views.AutoFilterDialog.txtJune": "Чэрвень", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Фільтр адмецін", "SSE.Views.AutoFilterDialog.txtLastMonth": "Мінулы месяц", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Last quarter", "SSE.Views.AutoFilterDialog.txtLastWeek": "Мінулы тыдзень", "SSE.Views.AutoFilterDialog.txtLastYear": "Last year", "SSE.Views.AutoFilterDialog.txtLess": "Ме<PERSON><PERSON> за…", "SSE.Views.AutoFilterDialog.txtLessEquals": "Мен<PERSON> альбо роўна…", "SSE.Views.AutoFilterDialog.txtMarch": "Сакавік", "SSE.Views.AutoFilterDialog.txtMay": "Травень", "SSE.Views.AutoFilterDialog.txtNextMonth": "Наступны месяц", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Next quarter", "SSE.Views.AutoFilterDialog.txtNextWeek": "Наступны тыдзень", "SSE.Views.AutoFilterDialog.txtNextYear": "Next year", "SSE.Views.AutoFilterDialog.txtNotBegins": "Не пачынаецца з…", "SSE.Views.AutoFilterDialog.txtNotBetween": "Не паміж…", "SSE.Views.AutoFilterDialog.txtNotContains": "Не змяшчае…", "SSE.Views.AutoFilterDialog.txtNotEnds": "Не заканчваецца на…", "SSE.Views.AutoFilterDialog.txtNotEquals": "Не роўна…", "SSE.Views.AutoFilterDialog.txtNovember": "Лістапад", "SSE.Views.AutoFilterDialog.txtNumFilter": "Лічбавы фільтр", "SSE.Views.AutoFilterDialog.txtOctober": "Кастрычнік", "SSE.Views.AutoFilterDialog.txtQuarter1": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Quarter 1", "SSE.Views.AutoFilterDialog.txtReapply": "Ужыць паўторна", "SSE.Views.AutoFilterDialog.txtSeptember": "Верасень", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Сартаванне па колеры ячэек", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Сартаванне па колеры шрыфту", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Сартаванне па памяншэнні", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Сартаванне па павелічэнні", "SSE.Views.AutoFilterDialog.txtSortOption": "Дадатковыя параметры сартавання… ", "SSE.Views.AutoFilterDialog.txtTextFilter": "Тэкставы фільтр", "SSE.Views.AutoFilterDialog.txtThisMonth": "Гэты месяц", "SSE.Views.AutoFilterDialog.txtThisQuarter": "This quarter", "SSE.Views.AutoFilterDialog.txtThisWeek": "Гэты тыдзень", "SSE.Views.AutoFilterDialog.txtThisYear": "Гэты год", "SSE.Views.AutoFilterDialog.txtTitle": "Фільтр", "SSE.Views.AutoFilterDialog.txtToday": "Сёння", "SSE.Views.AutoFilterDialog.txtTomorrow": "Заўтра", "SSE.Views.AutoFilterDialog.txtTop10": "Першыя 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Фільтр значэнняў", "SSE.Views.AutoFilterDialog.txtYearToDate": "Year to date", "SSE.Views.AutoFilterDialog.txtYesterday": "Учора", "SSE.Views.AutoFilterDialog.warnFilterError": "Каб узяць фільтр значэння, вобласць значэнняў мусіць змяшчаць хоць адно поле.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Неабходна абраць прынамсі адно значэнне", "SSE.Views.CellEditor.textManager": "Кіраўнік назваў", "SSE.Views.CellEditor.tipFormula": "Уставіць функцыю", "SSE.Views.CellRangeDialog.errorMaxRows": "ПАМЫЛКА! максімальная колькасць шэрагаў даных у адной дыяграме - 255", "SSE.Views.CellRangeDialog.errorStockChart": "Хібны парадак радкоў. Каб стварыць біржавую дыяграму размясціце даныя ў наступным парадку:<br>кошт адкрыцця, максімальны кошт, мінімальны кошт, кошт закрыцця.", "SSE.Views.CellRangeDialog.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.CellRangeDialog.txtInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.CellRangeDialog.txtTitle": "Абраць дыяпазон даных", "SSE.Views.CellSettings.strShrink": "Запаўняць па шырыні", "SSE.Views.CellSettings.strWrap": "Перанос тэксту", "SSE.Views.CellSettings.textAngle": "Вугал", "SSE.Views.CellSettings.textBackColor": "Колер фону", "SSE.Views.CellSettings.textBackground": "Колер фону", "SSE.Views.CellSettings.textBorderColor": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "Стыль межаў", "SSE.Views.CellSettings.textClearRule": "Выдаліць правілы", "SSE.Views.CellSettings.textColor": "Заліўка колерам", "SSE.Views.CellSettings.textColorScales": "Каляровыя шкалы", "SSE.Views.CellSettings.textCondFormat": "Умоўнае фарматаванне", "SSE.Views.CellSettings.textControl": "Кіраванне тэкстам", "SSE.Views.CellSettings.textDataBars": "Гістагра<PERSON>ы", "SSE.Views.CellSettings.textDirection": "Напрамак", "SSE.Views.CellSettings.textFill": "Заліўка", "SSE.Views.CellSettings.textForeground": "Колер пярэдняга плану", "SSE.Views.CellSettings.textGradient": "Град<PERSON><PERSON><PERSON>т", "SSE.Views.CellSettings.textGradientColor": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "Градыентная заліўка", "SSE.Views.CellSettings.textIndent": "Водступ", "SSE.Views.CellSettings.textItems": "элементаў", "SSE.Views.CellSettings.textLinear": "Лінейны", "SSE.Views.CellSettings.textManageRule": "Кіраванне правіламі", "SSE.Views.CellSettings.textNewRule": "Новае правіла", "SSE.Views.CellSettings.textNoFill": "Без заліўкі", "SSE.Views.CellSettings.textOrientation": "Арыентацыя тэксту", "SSE.Views.CellSettings.textPattern": "Узор", "SSE.Views.CellSettings.textPatternFill": "Узор", "SSE.Views.CellSettings.textPosition": "Пасада", "SSE.Views.CellSettings.textRadial": "Радыяльны", "SSE.Views.CellSettings.textSelectBorders": "Абярыце межы, да якіх патрэбна ўжыць абраны стыль", "SSE.Views.CellSettings.textSelection": "З бягучага абранага", "SSE.Views.CellSettings.textThisPivot": "З гэтай зводнай табліцы", "SSE.Views.CellSettings.textThisSheet": "З гэтага аркуша", "SSE.Views.CellSettings.textThisTable": "З гэтай табліцы", "SSE.Views.CellSettings.tipAddGradientPoint": "Дадаць кропку градыента", "SSE.Views.CellSettings.tipAll": "Вызначыць вонкавую мяжу і ўсе ўнутраныя лініі", "SSE.Views.CellSettings.tipBottom": "Вызначыць толькі вонкавую ніжнюю мяжу", "SSE.Views.CellSettings.tipDiagD": "Прызначыць дыяганальную мяжу зверху ўніз", "SSE.Views.CellSettings.tipDiagU": "Прызначыць дыяганальную мяжу знізу ўверх", "SSE.Views.CellSettings.tipInner": "Вызначыць толькі ўнутраныя лініі", "SSE.Views.CellSettings.tipInnerHor": "Вызначыць толькі гарызантальныя ўнутраныя лініі", "SSE.Views.CellSettings.tipInnerVert": "Вызначыць толькі вертыкальныя ўнутраныя лініі", "SSE.Views.CellSettings.tipLeft": "Вызначыць толькі вонкавую левую мяжу", "SSE.Views.CellSettings.tipNone": "Не вызначаць межаў", "SSE.Views.CellSettings.tipOuter": "Вызначыць толькі вонкавую мяжу", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Выдаліць кропку градыента", "SSE.Views.CellSettings.tipRight": "Вызначыць толькі вонкавую правую мяжу", "SSE.Views.CellSettings.tipTop": "Вызначыць толькі вонкавую верхнюю мяжу", "SSE.Views.ChartDataDialog.errorInFormula": "Ва ўведзенай формуле ёсць памылка.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Непрыдатная спасылка. Спасылка мусіць паказваць на адкрыты аркуш.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Максімальная колькасць кропак у шэрагу для дыяграмы - 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Максімальная колькасць шэрагаў даных дыяграмы - 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Непрыдатная спасылка. Спасылка для загалоўкаў, значэнняў памераў альбо адмецін мусіць паказваць на адну ячэйку, радок ці слупок.", "SSE.Views.ChartDataDialog.errorNoValues": "Для стварэння дыяграмы неабходна, каб шэраг змяшчаў хоць адно значэнне.", "SSE.Views.ChartDataDialog.errorStockChart": "Хібны парадак радкоў. Каб стварыць біржавую дыяграму размясціце даныя ў наступным парадку:<br>кошт адкрыцця, максімальны кошт, мінімальны кошт, кошт закрыцця.", "SSE.Views.ChartDataDialog.textAdd": "Дада<PERSON>ь", "SSE.Views.ChartDataDialog.textCategory": "Адмеціны гарызантальнай восі (катэгорыі)", "SSE.Views.ChartDataDialog.textData": "Дыяпазон даных для дыяграмы", "SSE.Views.ChartDataDialog.textDelete": "Выдаліць", "SSE.Views.ChartDataDialog.textDown": "Уніз", "SSE.Views.ChartDataDialog.textEdit": "Рэдагаваць", "SSE.Views.ChartDataDialog.textInvalidRange": "Хібны дыяпазон ячэек", "SSE.Views.ChartDataDialog.textSelectData": "Абраць даныя", "SSE.Views.ChartDataDialog.textSeries": "Элементы легенды (шэрагі)", "SSE.Views.ChartDataDialog.textSwitch": "Пераключыць радок / слупок", "SSE.Views.ChartDataDialog.textTitle": "Даныя дыяграмы", "SSE.Views.ChartDataDialog.textUp": "Уверх", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Ва ўведзенай формуле ёсць памылка.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Непрыдатная спасылка. Спасылка мусіць паказваць на адкрыты аркуш.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Максімальная колькасць кропак у шэрагу для дыяграмы - 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Максімальная колькасць шэрагаў даных дыяграмы - 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Непрыдатная спасылка. Спасылка для загалоўкаў, значэнняў памераў альбо адмецін мусіць паказваць на адну ячэйку, радок ці слупок.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Для стварэння дыяграмы неабходна, каб шэраг змяшчаў хоць адно значэнне.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Хібны парадак радкоў. Каб стварыць біржавую дыяграму размясціце даныя ў наступным парадку:<br>кошт адкрыцця, максімальны кошт, мінімальны кошт, кошт закрыцця.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Хібны дыяпазон ячэек", "SSE.Views.ChartDataRangeDialog.textSelectData": "Абраць даныя", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Дыяпазон адмецін восяў", "SSE.Views.ChartDataRangeDialog.txtChoose": "Абярыце дыяпазон", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Назва шэрагу", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Адмеціны восяў", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Змян<PERSON>ць шэраг", "SSE.Views.ChartDataRangeDialog.txtValues": "Значэнні", "SSE.Views.ChartDataRangeDialog.txtXValues": "Значэнні Х", "SSE.Views.ChartDataRangeDialog.txtYValues": "Значэнні Y", "SSE.Views.ChartSettings.errorMaxRows": "Максімальная колькасць шэрагаў даных дыяграмы - 255.", "SSE.Views.ChartSettings.strLineWeight": "Таўшчыня лініі", "SSE.Views.ChartSettings.strSparkColor": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "Шабл<PERSON>н", "SSE.Views.ChartSettings.text3dDepth": "Глыбіня (% ад базавай)", "SSE.Views.ChartSettings.text3dHeight": "Вышыня (% ад базавай)", "SSE.Views.ChartSettings.text3dRotation": "Трохвымернае паварочванне", "SSE.Views.ChartSettings.textAdvanced": "Дадатковыя налады", "SSE.Views.ChartSettings.textAutoscale": "Аўтаматычнае маштабаванне", "SSE.Views.ChartSettings.textBorderSizeErr": "Уведзена хібнае значэнне.<br>Кал<PERSON> ласка, ўвядзіце значэнне ад 0 да 1584 пунктаў.", "SSE.Views.ChartSettings.textChangeType": "Змяніць тып", "SSE.Views.ChartSettings.textChartType": "Змяніць тып дыяграмы", "SSE.Views.ChartSettings.textDefault": "Прадвызначанае паварочванне", "SSE.Views.ChartSettings.textDown": "Уніз", "SSE.Views.ChartSettings.textEditData": "Рэдагаваць даныя і месца", "SSE.Views.ChartSettings.textFirstPoint": "Першая кропка", "SSE.Views.ChartSettings.textHeight": "Вышыня", "SSE.Views.ChartSettings.textHighPoint": "Максімальная кропка", "SSE.Views.ChartSettings.textKeepRatio": "Захаваць прапорцыі", "SSE.Views.ChartSettings.textLastPoint": "Апошняя кропка", "SSE.Views.ChartSettings.textLeft": "Улева", "SSE.Views.ChartSettings.textLowPoint": "Мінімальная кропка", "SSE.Views.ChartSettings.textMarkers": "Адзнакі", "SSE.Views.ChartSettings.textNarrow": "Вузкае поле зроку", "SSE.Views.ChartSettings.textNegativePoint": "Адмоўная кропка", "SSE.Views.ChartSettings.textPerspective": "Перспектыва", "SSE.Views.ChartSettings.textRanges": "Дыяпазон даных", "SSE.Views.ChartSettings.textRight": "Управа", "SSE.Views.ChartSettings.textRightAngle": "Прамавугольныя восі", "SSE.Views.ChartSettings.textSelectData": "Абраць даныя", "SSE.Views.ChartSettings.textShow": "Паказаць", "SSE.Views.ChartSettings.textSize": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textStyle": "Стыль", "SSE.Views.ChartSettings.textSwitch": "Пераключыць радок / слупок", "SSE.Views.ChartSettings.textType": "Тып", "SSE.Views.ChartSettings.textUp": "Уверх", "SSE.Views.ChartSettings.textWiden": "Пашырыць поле зроку", "SSE.Views.ChartSettings.textWidth": "Шырыня", "SSE.Views.ChartSettings.textX": "Паварочванне па X", "SSE.Views.ChartSettings.textY": "Паварочванне па Y", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ПАМЫЛКА! Максімальная колькасць кропак у шэрагу дыяграмы - 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "ПАМЫЛКА! максімальная колькасць шэрагаў даных у адной дыяграме - 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Хібны парадак радкоў. Каб стварыць біржавую дыяграму размясціце даныя ў наступным парадку:<br>кошт адкрыцця, максімальны кошт, мінімальны кошт, кошт закрыцця.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Не перамяшчаць і не змяняць памеры разам з ячэйкамі", "SSE.Views.ChartSettingsDlg.textAlt": "Альтэрнатыўны тэкст", "SSE.Views.ChartSettingsDlg.textAltDescription": "Апісанне", "SSE.Views.ChartSettingsDlg.textAltTip": "Альтэрнатыўная тэкставая падача інфармацыі пра візуальны аб’ект, якая будзе агучвацца для слабавідушчых людзей ці людзей з кагнітыўнымі парушэннямі, каб дапамагчы ім зразумець інфармацыю, якую змяшчае выява, аўтафігура, дыяграма ці табліца.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Загаловак", "SSE.Views.ChartSettingsDlg.textAuto": "Аўта", "SSE.Views.ChartSettingsDlg.textAutoEach": "Аўтаматычна для кожнага", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Перасячэнне з воссю", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Параметры восі", "SSE.Views.ChartSettingsDlg.textAxisPos": "Пазіцыя восі", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Налады восі", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Загаловак", "SSE.Views.ChartSettingsDlg.textBase": "Базавы", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Паміж падзеламі", "SSE.Views.ChartSettingsDlg.textBillions": "Мільярды", "SSE.Views.ChartSettingsDlg.textBottom": "Знізу", "SSE.Views.ChartSettingsDlg.textCategoryName": "Назва катэгорыі", "SSE.Views.ChartSettingsDlg.textCenter": "Па цэнтры", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Элементы дыяграмы і<br>легенда дыяграмы", "SSE.Views.ChartSettingsDlg.textChartTitle": "Загаловак дыяграмы", "SSE.Views.ChartSettingsDlg.textCross": "На скрыжаванні", "SSE.Views.ChartSettingsDlg.textCustom": "Адвольны", "SSE.Views.ChartSettingsDlg.textDataColumns": "у слупках", "SSE.Views.ChartSettingsDlg.textDataLabels": "Адмеціны даных", "SSE.Views.ChartSettingsDlg.textDataRows": "у радках", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Паказваць легенду", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Схаваныя і пустыя ячэйкі", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Злучаць кропкі даных лініямі", "SSE.Views.ChartSettingsDlg.textFit": "Па шырыні", "SSE.Views.ChartSettingsDlg.textFixed": "Фіксаванае", "SSE.Views.ChartSettingsDlg.textFormat": "Фармат адмеціны", "SSE.Views.ChartSettingsDlg.textGaps": "Пуста", "SSE.Views.ChartSettingsDlg.textGridLines": "Лініі сеткі", "SSE.Views.ChartSettingsDlg.textGroup": "Група спарклайнаў", "SSE.Views.ChartSettingsDlg.textHide": "Схаваць", "SSE.Views.ChartSettingsDlg.textHideAxis": "Схаваць вось", "SSE.Views.ChartSettingsDlg.textHigh": "Выш<PERSON>й", "SSE.Views.ChartSettingsDlg.textHorAxis": "Гарызантальная вось", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Другасная гарызантальная вось", "SSE.Views.ChartSettingsDlg.textHorizontal": "Гарызантальна", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Сотні", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "Унутры", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Унутры ўнізе", "SSE.Views.ChartSettingsDlg.textInnerTop": "Унутры зверху", "SSE.Views.ChartSettingsDlg.textInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.ChartSettingsDlg.textLabelDist": "Адлегласць да адмеціны", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Прамежак паміж адмецінамі", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Параметры адмеціны", "SSE.Views.ChartSettingsDlg.textLabelPos": "Пазіцыя адмеціны", "SSE.Views.ChartSettingsDlg.textLayout": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeft": "Злева", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Накладанне злева", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Знізу", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Злева", "SSE.Views.ChartSettingsDlg.textLegendPos": "Легенда", "SSE.Views.ChartSettingsDlg.textLegendRight": "Справа", "SSE.Views.ChartSettingsDlg.textLegendTop": "Уверсе", "SSE.Views.ChartSettingsDlg.textLines": "Лініі", "SSE.Views.ChartSettingsDlg.textLocationRange": "Дыяпазон размяшчэння:", "SSE.Views.ChartSettingsDlg.textLogScale": "Лагарыфмічная шкала", "SSE.Views.ChartSettingsDlg.textLow": "Ніжэ<PERSON>", "SSE.Views.ChartSettingsDlg.textMajor": "Асноўныя", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Асноўныя і дадатковыя", "SSE.Views.ChartSettingsDlg.textMajorType": "Асноўны тып", "SSE.Views.ChartSettingsDlg.textManual": "Уласнаручна", "SSE.Views.ChartSettingsDlg.textMarkers": "Адзнакі", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Прамежак паміж падзеламі", "SSE.Views.ChartSettingsDlg.textMaxValue": "Максімальнае значэнне", "SSE.Views.ChartSettingsDlg.textMillions": "Мільёны", "SSE.Views.ChartSettingsDlg.textMinor": "Дадатковыя", "SSE.Views.ChartSettingsDlg.textMinorType": "Дадатковы тып", "SSE.Views.ChartSettingsDlg.textMinValue": "Мінімальнае значэнне", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Побач з воссю", "SSE.Views.ChartSettingsDlg.textNone": "Няма", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Без накладання", "SSE.Views.ChartSettingsDlg.textOneCell": "Перам<PERSON><PERSON><PERSON><PERSON><PERSON>ь, але не змяняць памеры разам з ячэйкамі.", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Падзелы", "SSE.Views.ChartSettingsDlg.textOut": "Звонку", "SSE.Views.ChartSettingsDlg.textOuterTop": "Звонку зверху", "SSE.Views.ChartSettingsDlg.textOverlay": "Накладанне", "SSE.Views.ChartSettingsDlg.textReverse": " Значэнні ў адваротным парадку", "SSE.Views.ChartSettingsDlg.textReverseOrder": "У адваротным парадку", "SSE.Views.ChartSettingsDlg.textRight": "Справа", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Накладанне справа", "SSE.Views.ChartSettingsDlg.textRotated": "Павернута", "SSE.Views.ChartSettingsDlg.textSameAll": "Аднолькава для ўсіх", "SSE.Views.ChartSettingsDlg.textSelectData": "Абраць даныя", "SSE.Views.ChartSettingsDlg.textSeparator": "Падзяляльнік адмецін", "SSE.Views.ChartSettingsDlg.textSeriesName": "Назва шэрагу", "SSE.Views.ChartSettingsDlg.textShow": "Паказаць", "SSE.Views.ChartSettingsDlg.textShowBorders": "Паказваць межы дыяграмы", "SSE.Views.ChartSettingsDlg.textShowData": "Паказваць даныя ў схаваных радках і слупках", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Паказваць пустыя ячэйкі як", "SSE.Views.ChartSettingsDlg.textShowEquation": "Display equation on chart", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Паказваць вось", "SSE.Views.ChartSettingsDlg.textShowValues": "Паказваць значэнні дыяграмы", "SSE.Views.ChartSettingsDlg.textSingle": "Асобны спарклайн", "SSE.Views.ChartSettingsDlg.textSmooth": "Згладжаныя", "SSE.Views.ChartSettingsDlg.textSnap": "Далучэнне да ячэйкі", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Дыяпазоны спарклайнаў", "SSE.Views.ChartSettingsDlg.textStraight": "Непасрэдныя", "SSE.Views.ChartSettingsDlg.textStyle": "Стыль", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Тысячы", "SSE.Views.ChartSettingsDlg.textTickOptions": "Параметры падзелаў", "SSE.Views.ChartSettingsDlg.textTitle": "Дыяграма - дадатковыя налады", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Спарклайн - дадатковыя налады", "SSE.Views.ChartSettingsDlg.textTop": "Уверсе", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Trendline options", "SSE.Views.ChartSettingsDlg.textTrillions": "Трыльёны", "SSE.Views.ChartSettingsDlg.textTwoCell": "Перамяшчаць і змяняць памеры разам з ячэйкамі", "SSE.Views.ChartSettingsDlg.textType": "Тып", "SSE.Views.ChartSettingsDlg.textTypeData": "Тып і даныя", "SSE.Views.ChartSettingsDlg.textUnits": "Адзінкі адлюстравання", "SSE.Views.ChartSettingsDlg.textValue": "Значэнне", "SSE.Views.ChartSettingsDlg.textVertAxis": "Вертыкальная вось", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Другасная вертыкальная вось", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Назва восі Х", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Назва восі Y", "SSE.Views.ChartSettingsDlg.textZero": "Нуль", "SSE.Views.ChartSettingsDlg.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.ChartTypeDialog.errorComboSeries": "Каб стварыць камбінаваную дыяграму, абярыце прынамсі два рады даных.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Для абранага тыпу дыяграмы патрэбная другасная вось, якая выкарыстоўваецца ў наяўнай дыяграме. Абярыце іншы тып дыяграмы.", "SSE.Views.ChartTypeDialog.textSecondary": "Другасная вось", "SSE.Views.ChartTypeDialog.textSeries": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textStyle": "Стыль", "SSE.Views.ChartTypeDialog.textTitle": "Тып дыяграмы", "SSE.Views.ChartTypeDialog.textType": "Тып", "SSE.Views.ChartWizardDialog.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartWizardDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order: opening price, max price, min price, closing price.", "SSE.Views.ChartWizardDialog.textRecommended": "Recommended", "SSE.Views.ChartWizardDialog.textSecondary": "Secondary Axis", "SSE.Views.ChartWizardDialog.textSeries": "Series", "SSE.Views.ChartWizardDialog.textTitle": "Insert Chart", "SSE.Views.ChartWizardDialog.textTitleChange": "Change chart type", "SSE.Views.ChartWizardDialog.textType": "Type", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Choose the chart type and axis for your data series", "SSE.Views.CreatePivotDialog.textDataRange": "Дыяпазон зыходных даных", "SSE.Views.CreatePivotDialog.textDestination": "Абраць месца размяшчэння табліцы", "SSE.Views.CreatePivotDialog.textExist": "Існы аркуш", "SSE.Views.CreatePivotDialog.textInvalidRange": "Хібны дыяпазон ячэек", "SSE.Views.CreatePivotDialog.textNew": "Новы аркуш", "SSE.Views.CreatePivotDialog.textSelectData": "Абраць даныя", "SSE.Views.CreatePivotDialog.textTitle": "Стварыць зводную табліцу", "SSE.Views.CreatePivotDialog.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.CreateSparklineDialog.textDataRange": "Дыяпазон зыходных даных", "SSE.Views.CreateSparklineDialog.textDestination": "Абярыце месца размяшчэння спарклайнаў", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Хібны дыяпазон ячэек", "SSE.Views.CreateSparklineDialog.textSelectData": "Абраць даныя", "SSE.Views.CreateSparklineDialog.textTitle": "Стварыць спарклайны", "SSE.Views.CreateSparklineDialog.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.DataTab.capBtnGroup": "Згрупаваць", "SSE.Views.DataTab.capBtnTextCustomSort": "Адвольнае сартаванне", "SSE.Views.DataTab.capBtnTextDataValidation": "Правяранне даных", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Выдаліць паўторы", "SSE.Views.DataTab.capBtnTextToCol": "Тэкст па слупках", "SSE.Views.DataTab.capBtnUngroup": "Разгрупаваць", "SSE.Views.DataTab.capDataExternalLinks": "Вонкавыя спасылкі", "SSE.Views.DataTab.capDataFromText": "Атрымаць даныя", "SSE.Views.DataTab.capGoalSeek": "Goal Seek", "SSE.Views.DataTab.mniFromFile": "З лакальнага файла TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "З файла TXT/CSV у сеціве", "SSE.Views.DataTab.mniFromXMLFile": "З лакальнага файла XML", "SSE.Views.DataTab.textBelow": "Вынікі ў радках пад данымі", "SSE.Views.DataTab.textClear": "Выдаліць структуру", "SSE.Views.DataTab.textColumns": "Разгрупаваць слупкі", "SSE.Views.DataTab.textGroupColumns": "Згрупаваць слупкі", "SSE.Views.DataTab.textGroupRows": "Згрупаваць радкі", "SSE.Views.DataTab.textRightOf": "Вынікі ў слупках справа ад даных", "SSE.Views.DataTab.textRows": "Разгрупаваць радкі", "SSE.Views.DataTab.tipCustomSort": "Адвольнае сартаванне", "SSE.Views.DataTab.tipDataFromText": "Атрымаць даныя з файла", "SSE.Views.DataTab.tipDataValidation": "Правяранне даных", "SSE.Views.DataTab.tipExternalLinks": "Праглядзець іншыя файлы, з якімі звязаная гэтая табліца", "SSE.Views.DataTab.tipGoalSeek": "Find the right input for the value you want", "SSE.Views.DataTab.tipGroup": "Згрупаваць дыяпазон ячэек", "SSE.Views.DataTab.tipRemDuplicates": "Выдаліць з аркуша паўторныя радкі", "SSE.Views.DataTab.tipToColumns": "Падзяліць тэкст ячэйкі па слупках", "SSE.Views.DataTab.tipUngroup": "Разгрупаваць дыяпазон ячэек", "SSE.Views.DataValidationDialog.errorFormula": "Значэнне ўважаецца за памылку. Хочаце працягнуць?", "SSE.Views.DataValidationDialog.errorInvalid": "Вы ўвялі ў поле {0}\" хібнае значэнне.", "SSE.Views.DataValidationDialog.errorInvalidDate": "У поле \"{0}\" уведзена хібная дата.", "SSE.Views.DataValidationDialog.errorInvalidList": "Крыніца мусіць быць спісам з падзяляльнікамі або спасылкай на адзін радок (слупок).", "SSE.Views.DataValidationDialog.errorInvalidTime": "Вы ўвялі ў поле {0}\" хібны час.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "Значэнне поля \"{1}\" мусіць быць большым або роўным значэнню поля \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Трэба ўвесці значэнне і ў поле \"{0}\", і ў поле \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Трэба ўвесці значэнне ў поле \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "Вызначаны названы дыяпазон не знойдзены.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": " У ўмовах \"{0}\" нельга выкарыстоўваць адмоўныя значэнні.", "SSE.Views.DataValidationDialog.errorNotNumeric": "Поле \"{0}\" мусіць змяшчаць лікавае значэнне, лікавы выраз або спасылку на ячэйку з лікавым значэннем.", "SSE.Views.DataValidationDialog.strError": "Абвестка пра памылку", "SSE.Views.DataValidationDialog.strInput": "Паведамленне ўводу", "SSE.Views.DataValidationDialog.strSettings": "Налады", "SSE.Views.DataValidationDialog.textAlert": "Абвестка", "SSE.Views.DataValidationDialog.textAllow": "Дазволіць", "SSE.Views.DataValidationDialog.textApply": "Распаўсюдзіць змены на ўсе іншыя ячэйкі з той жа наладай", "SSE.Views.DataValidationDialog.textCellSelected": "Калі ячэйка абраная, паказваць гэтае паведамленне ўводу", "SSE.Views.DataValidationDialog.textCompare": "Параўнаць з", "SSE.Views.DataValidationDialog.textData": "Даныя", "SSE.Views.DataValidationDialog.textEndDate": "Дата завяршэння", "SSE.Views.DataValidationDialog.textEndTime": "<PERSON>ас завяршэння", "SSE.Views.DataValidationDialog.textError": "Паведамленне пра памылку", "SSE.Views.DataValidationDialog.textFormula": "Формула", "SSE.Views.DataValidationDialog.textIgnore": "Не зважаць на пустыя ячэйкі", "SSE.Views.DataValidationDialog.textInput": "Паведамленне ўводу", "SSE.Views.DataValidationDialog.textMax": "Максімум", "SSE.Views.DataValidationDialog.textMessage": "Паведамленне", "SSE.Views.DataValidationDialog.textMin": "Мін<PERSON><PERSON>ум", "SSE.Views.DataValidationDialog.textSelectData": "Абраць даныя", "SSE.Views.DataValidationDialog.textShowDropDown": "Паказваць выплыўны спіс у ячэйцы", "SSE.Views.DataValidationDialog.textShowError": "Пасля ўводу хібных даных паказваць абвестку пра памылку", "SSE.Views.DataValidationDialog.textShowInput": "Паказваць паведамленне ўводу, калі ячэйка абраная", "SSE.Views.DataValidationDialog.textSource": "Крыніца", "SSE.Views.DataValidationDialog.textStartDate": "Дата пачатку", "SSE.Views.DataValidationDialog.textStartTime": "Час пачатку", "SSE.Views.DataValidationDialog.textStop": "Спыніць", "SSE.Views.DataValidationDialog.textStyle": "Стыль", "SSE.Views.DataValidationDialog.textTitle": "Загаловак", "SSE.Views.DataValidationDialog.textUserEnters": "Калі карыстальнікі ўводзяць хібныя даныя, паказваць гэтую абвестку", "SSE.Views.DataValidationDialog.txtAny": "Любое значэнне", "SSE.Views.DataValidationDialog.txtBetween": "паміж", "SSE.Views.DataValidationDialog.txtDate": "Дата", "SSE.Views.DataValidationDialog.txtDecimal": "Дзесятковыя знакі", "SSE.Views.DataValidationDialog.txtElTime": "Мін<PERSON><PERSON>а часу", "SSE.Views.DataValidationDialog.txtEndDate": "Дата завяршэння", "SSE.Views.DataValidationDialog.txtEndTime": "<PERSON>ас завяршэння", "SSE.Views.DataValidationDialog.txtEqual": "Роўна", "SSE.Views.DataValidationDialog.txtGreaterThan": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "Больш альбо роўна", "SSE.Views.DataValidationDialog.txtLength": "Даўжыня", "SSE.Views.DataValidationDialog.txtLessThan": "Ме<PERSON><PERSON> за", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "Мен<PERSON> альбо роўна", "SSE.Views.DataValidationDialog.txtList": "Сп<PERSON>с", "SSE.Views.DataValidationDialog.txtNotBetween": "не паміж", "SSE.Views.DataValidationDialog.txtNotEqual": "Не роўна", "SSE.Views.DataValidationDialog.txtOther": "Іншае", "SSE.Views.DataValidationDialog.txtStartDate": "Дата пачатку", "SSE.Views.DataValidationDialog.txtStartTime": "Час пачатку", "SSE.Views.DataValidationDialog.txtTextLength": "Даўжыня тэксту", "SSE.Views.DataValidationDialog.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtWhole": "Цэлы лік", "SSE.Views.DigitalFilterDialog.capAnd": "і", "SSE.Views.DigitalFilterDialog.capCondition1": "роўна", "SSE.Views.DigitalFilterDialog.capCondition10": "не заканчваецца на", "SSE.Views.DigitalFilterDialog.capCondition11": "змяшчае", "SSE.Views.DigitalFilterDialog.capCondition12": "не змяшчае", "SSE.Views.DigitalFilterDialog.capCondition2": "не роўна", "SSE.Views.DigitalFilterDialog.capCondition3": "больш за", "SSE.Views.DigitalFilterDialog.capCondition30": "is after", "SSE.Views.DigitalFilterDialog.capCondition4": "больш альбо роўна", "SSE.Views.DigitalFilterDialog.capCondition40": "is after or equal to", "SSE.Views.DigitalFilterDialog.capCondition5": "менш за", "SSE.Views.DigitalFilterDialog.capCondition50": "is before", "SSE.Views.DigitalFilterDialog.capCondition6": "менш альбо роўна", "SSE.Views.DigitalFilterDialog.capCondition60": "is before or equal to", "SSE.Views.DigitalFilterDialog.capCondition7": "пачынаецца з", "SSE.Views.DigitalFilterDialog.capCondition8": "не пачынаецца з", "SSE.Views.DigitalFilterDialog.capCondition9": "заканчваецца на", "SSE.Views.DigitalFilterDialog.capOr": "Альбо", "SSE.Views.DigitalFilterDialog.textNoFilter": "без фільтра", "SSE.Views.DigitalFilterDialog.textShowRows": "Паказаць радкі, у якіх", "SSE.Views.DigitalFilterDialog.textUse1": "Выкарыстоўвайце знак ? замест любога асобнага сімвала", "SSE.Views.DigitalFilterDialog.textUse2": "Выкарыстоўвайце знак * замест любой паслядоўнасці сімвалаў", "SSE.Views.DigitalFilterDialog.txtSelectDate": "Select date", "SSE.Views.DigitalFilterDialog.txtTitle": "Адвольны фільтр", "SSE.Views.DocumentHolder.advancedEquationText": "Налады раўнанняў", "SSE.Views.DocumentHolder.advancedImgText": "Дадатковыя налады выявы", "SSE.Views.DocumentHolder.advancedShapeText": "Дадатковыя налады фігуры", "SSE.Views.DocumentHolder.advancedSlicerText": "Дадатковыя параметры зводкі", "SSE.Views.DocumentHolder.allLinearText": "Усе - лінейны", "SSE.Views.DocumentHolder.allProfText": "Усе - прафесійны", "SSE.Views.DocumentHolder.bottomCellText": "Выраўнаваць па ніжняму краю", "SSE.Views.DocumentHolder.bulletsText": "Адзнакі і нумарацыя", "SSE.Views.DocumentHolder.centerCellText": "Выраўнаваць па сярэдзіне", "SSE.Views.DocumentHolder.chartDataText": "Абраць даныя для дыяграмы", "SSE.Views.DocumentHolder.chartText": "Дадатковыя налады дыяграмы", "SSE.Views.DocumentHolder.chartTypeText": "Змяніць тып дыяграмы", "SSE.Views.DocumentHolder.currLinearText": "Бягучы - лінейны", "SSE.Views.DocumentHolder.currProfText": "Бягучы - прафесійны", "SSE.Views.DocumentHolder.deleteColumnText": "Слупок", "SSE.Views.DocumentHolder.deleteRowText": "Радок", "SSE.Views.DocumentHolder.deleteTableText": "Табліца", "SSE.Views.DocumentHolder.direct270Text": "Павярнуць тэкст уверх", "SSE.Views.DocumentHolder.direct90Text": "Павярнуць тэкст уніз", "SSE.Views.DocumentHolder.directHText": "Гарызантальна", "SSE.Views.DocumentHolder.directionText": "Напрамак тэксту", "SSE.Views.DocumentHolder.editChartText": "Рэдагаваць даныя", "SSE.Views.DocumentHolder.editHyperlinkText": "Рэдагаваць гіперспасылку", "SSE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "SSE.Views.DocumentHolder.insertColumnLeftText": "Слупок злева", "SSE.Views.DocumentHolder.insertColumnRightText": "Слупок справа", "SSE.Views.DocumentHolder.insertRowAboveText": "Радок вышэй", "SSE.Views.DocumentHolder.insertRowBelowText": "Радок ніжэй", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "Актуальны памер", "SSE.Views.DocumentHolder.removeHyperlinkText": "Выдаліць гіперспасылку", "SSE.Views.DocumentHolder.selectColumnText": "Слупок", "SSE.Views.DocumentHolder.selectDataText": "Даныя слупкоў", "SSE.Views.DocumentHolder.selectRowText": "Радок", "SSE.Views.DocumentHolder.selectTableText": "Табліца", "SSE.Views.DocumentHolder.showEqToolbar": "Show Equation Toolbar", "SSE.Views.DocumentHolder.strDelete": "Выдаліць подпіс", "SSE.Views.DocumentHolder.strDetails": "Падрабязнасці подпісу", "SSE.Views.DocumentHolder.strSetup": "Наладжванне подпісу", "SSE.Views.DocumentHolder.strSign": "Падпісаць", "SSE.Views.DocumentHolder.textAlign": "Выраўноўванне", "SSE.Views.DocumentHolder.textArrange": "Пар<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "Перамясціць у фон", "SSE.Views.DocumentHolder.textArrangeBackward": "Адправіць назад", "SSE.Views.DocumentHolder.textArrangeForward": "Перамясціць уперад", "SSE.Views.DocumentHolder.textArrangeFront": "Перанесці на пярэдні план", "SSE.Views.DocumentHolder.textAverage": "Сярэдняе", "SSE.Views.DocumentHolder.textBullets": "Адзнакі", "SSE.Views.DocumentHolder.textCopyCells": "Copy cells", "SSE.Views.DocumentHolder.textCount": "Колькасць", "SSE.Views.DocumentHolder.textCrop": "Абрэзаць", "SSE.Views.DocumentHolder.textCropFill": "Заліўка", "SSE.Views.DocumentHolder.textCropFit": "Умясціць", "SSE.Views.DocumentHolder.textEditPoints": "Рэдагаваць кропкі", "SSE.Views.DocumentHolder.textEntriesList": "Абраць са спіса", "SSE.Views.DocumentHolder.textFillDays": "Fill days", "SSE.Views.DocumentHolder.textFillFormatOnly": "Fill formatting only", "SSE.Views.DocumentHolder.textFillMonths": "Fill months", "SSE.Views.DocumentHolder.textFillSeries": "Fill series", "SSE.Views.DocumentHolder.textFillWeekdays": "Fill weekdays", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Fill without formatting", "SSE.Views.DocumentHolder.textFillYears": "Fill years", "SSE.Views.DocumentHolder.textFlashFill": "Flash fill", "SSE.Views.DocumentHolder.textFlipH": "Адлюстраваць па гарызанталі", "SSE.Views.DocumentHolder.textFlipV": "Адлюстраваць па вертыкалі", "SSE.Views.DocumentHolder.textFreezePanes": "Замацаваць вобласці", "SSE.Views.DocumentHolder.textFromFile": "З файла", "SSE.Views.DocumentHolder.textFromStorage": "Са сховішча", "SSE.Views.DocumentHolder.textFromUrl": "Па URL", "SSE.Views.DocumentHolder.textGrowthTrend": "Growth trend", "SSE.Views.DocumentHolder.textLinearTrend": "Linear trend", "SSE.Views.DocumentHolder.textListSettings": "Налады спіса", "SSE.Views.DocumentHolder.textMacro": "Прызначыць макрас", "SSE.Views.DocumentHolder.textMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.DocumentHolder.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textMore": "Іншыя функцыі", "SSE.Views.DocumentHolder.textMoreFormats": "Іншыя фарматы", "SSE.Views.DocumentHolder.textNone": "Няма", "SSE.Views.DocumentHolder.textNumbering": "Нумарацыя", "SSE.Views.DocumentHolder.textReplace": "Замяніць выяву", "SSE.Views.DocumentHolder.textResetCrop": "Reset crop", "SSE.Views.DocumentHolder.textRotate": "Паварочванне", "SSE.Views.DocumentHolder.textRotate270": "Павярнуць улева на 90°", "SSE.Views.DocumentHolder.textRotate90": "Павярнуць управа на 90°", "SSE.Views.DocumentHolder.textSaveAsPicture": "Захаваць як выяву", "SSE.Views.DocumentHolder.textSeries": "Series", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Выраўнаваць па ніжняму краю", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Выраўнаваць па цэнтры", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Выраўнаваць па леваму краю", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Выраўнаваць па сярэдзіне", "SSE.Views.DocumentHolder.textShapeAlignRight": "Выраўнаваць па праваму краю", "SSE.Views.DocumentHolder.textShapeAlignTop": "Выраўнаваць па верхняму краю", "SSE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "Сума", "SSE.Views.DocumentHolder.textUndo": "Адрабіць", "SSE.Views.DocumentHolder.textUnFreezePanes": "Адмацаваць вобласці", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "Маркеры-стрэлкі", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Адзнакі", "SSE.Views.DocumentHolder.tipMarkersDash": "Адзнакі-працяжнікі", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Адзнакі ў выглядзе запоўненых ромбаў", "SSE.Views.DocumentHolder.tipMarkersFRound": "Адзнакі ў выглядзе запоўненых колаў", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Адзнакі ў выглядзе запоўненых квадратаў", "SSE.Views.DocumentHolder.tipMarkersHRound": "Пустыя круглыя адзнакі", "SSE.Views.DocumentHolder.tipMarkersStar": "Адзнакі-зорачкі", "SSE.Views.DocumentHolder.topCellText": "Выраўнаваць па верхняму краю", "SSE.Views.DocumentHolder.txtAccounting": "Фінансавы", "SSE.Views.DocumentHolder.txtAddComment": "Да<PERSON><PERSON><PERSON>ь каментар", "SSE.Views.DocumentHolder.txtAddNamedRange": "Прызначыць назву", "SSE.Views.DocumentHolder.txtArrange": "Пар<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "Па ўзрастанні", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Аўтавызначэнне шырыні слупка", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Аўтавызначэнне шырыні радка", "SSE.Views.DocumentHolder.txtAverage": "Сярэдняе", "SSE.Views.DocumentHolder.txtCellFormat": "Format cells", "SSE.Views.DocumentHolder.txtClear": "Ачысціць", "SSE.Views.DocumentHolder.txtClearAll": "Усе", "SSE.Views.DocumentHolder.txtClearComments": "Каментары", "SSE.Views.DocumentHolder.txtClearFormat": "Фарматаванне", "SSE.Views.DocumentHolder.txtClearHyper": "Гіперспасылкі", "SSE.Views.DocumentHolder.txtClearPivotField": "Прыбраць фільтр са слупка {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Ачысціць абраныя групы спарклайнаў", "SSE.Views.DocumentHolder.txtClearSparklines": "Ачысціць абраныя спарклайны", "SSE.Views.DocumentHolder.txtClearText": "Тэкст", "SSE.Views.DocumentHolder.txtCollapse": "Collapse", "SSE.Views.DocumentHolder.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.DocumentHolder.txtColumn": "Слупок", "SSE.Views.DocumentHolder.txtColumnWidth": "Прызначыць шырыню слупка", "SSE.Views.DocumentHolder.txtCondFormat": "Умоўнае фарматаванне", "SSE.Views.DocumentHolder.txtCopy": "Капіяваць", "SSE.Views.DocumentHolder.txtCount": "Колькасць", "SSE.Views.DocumentHolder.txtCurrency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Адвольная шырыня слупка", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Адвольная вышыня радка", "SSE.Views.DocumentHolder.txtCustomSort": "Адвольнае сартаванне", "SSE.Views.DocumentHolder.txtCut": "Выразаць", "SSE.Views.DocumentHolder.txtDateLong": "Доўгі фармат даты", "SSE.Views.DocumentHolder.txtDateShort": "Кароткі фармат даты", "SSE.Views.DocumentHolder.txtDelete": "Выдаліць", "SSE.Views.DocumentHolder.txtDelField": "Выдаліць", "SSE.Views.DocumentHolder.txtDescending": "Па памяншэнні", "SSE.Views.DocumentHolder.txtDifference": "Адрозненне", "SSE.Views.DocumentHolder.txtDistribHor": "Размеркаваць па гарызанталі", "SSE.Views.DocumentHolder.txtDistribVert": "Размеркаваць па вертыкалі", "SSE.Views.DocumentHolder.txtEditComment": "Рэдагава<PERSON>ь каментар", "SSE.Views.DocumentHolder.txtEditObject": "Edit object", "SSE.Views.DocumentHolder.txtExpand": "Expand", "SSE.Views.DocumentHolder.txtExpandCollapse": "Expand/Collapse", "SSE.Views.DocumentHolder.txtExpandEntire": "Expand Entire Field", "SSE.Views.DocumentHolder.txtFieldSettings": "Налады палёў", "SSE.Views.DocumentHolder.txtFilter": "Фільтр", "SSE.Views.DocumentHolder.txtFilterCellColor": "Фільтр па колеру ячэек", "SSE.Views.DocumentHolder.txtFilterFontColor": "Фільтр па колеру шрыфту ", "SSE.Views.DocumentHolder.txtFilterValue": "Фільтр па значэнні абранай ячэйкі", "SSE.Views.DocumentHolder.txtFormula": "Уставіць функцыю", "SSE.Views.DocumentHolder.txtFraction": "Дроб", "SSE.Views.DocumentHolder.txtGeneral": "Агульны", "SSE.Views.DocumentHolder.txtGetLink": "Атрымаць спасылку на гэты дыяпазон", "SSE.Views.DocumentHolder.txtGrandTotal": "Агульны вынік", "SSE.Views.DocumentHolder.txtGroup": "Згрупаваць", "SSE.Views.DocumentHolder.txtHide": "Схаваць", "SSE.Views.DocumentHolder.txtIndex": "Індэкс", "SSE.Views.DocumentHolder.txtInsert": "Уставіць", "SSE.Views.DocumentHolder.txtInsHyperlink": "Гіперспасылка", "SSE.Views.DocumentHolder.txtInsImage": "Insert image from file", "SSE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "SSE.Views.DocumentHolder.txtLabelFilter": "Фільтры адмецін", "SSE.Views.DocumentHolder.txtMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtMin": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtMoreOptions": "Дадатковыя параметры", "SSE.Views.DocumentHolder.txtNormal": "Без падліку", "SSE.Views.DocumentHolder.txtNumber": "Лічбавы", "SSE.Views.DocumentHolder.txtNumFormat": "Фармат нумара", "SSE.Views.DocumentHolder.txtPaste": "Уставіць", "SSE.Views.DocumentHolder.txtPercent": "Адсотак", "SSE.Views.DocumentHolder.txtPercentage": "Адсотак", "SSE.Views.DocumentHolder.txtPercentDiff": "Адрозненне ў адсотках", "SSE.Views.DocumentHolder.txtPercentOfCol": "% ад сумы ў слупку", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% ад агульнага выніку", "SSE.Views.DocumentHolder.txtPercentOfParent": "% ад бацькоўскай сумы", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% ад суму бацькоўскага слупка", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% ад сумы бацькоўскага радка", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% ад сумы з нарастальным вынікам ", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% ад сумы радка", "SSE.Views.DocumentHolder.txtPivotSettings": "Налады зводнай табліцы", "SSE.Views.DocumentHolder.txtProduct": "Здабытак", "SSE.Views.DocumentHolder.txtRankAscending": "Дыяпазон ад найменшага да найбольшага", "SSE.Views.DocumentHolder.txtRankDescending": "Дыяпазон ад найбольшага да найменшага", "SSE.Views.DocumentHolder.txtReapply": "Ужыць паўторна", "SSE.Views.DocumentHolder.txtRefresh": "Абнавіць", "SSE.Views.DocumentHolder.txtRow": "Радок", "SSE.Views.DocumentHolder.txtRowHeight": "Вызначыць вышыню радка", "SSE.Views.DocumentHolder.txtRunTotal": "З вынікам", "SSE.Views.DocumentHolder.txtScientific": "Навуковы", "SSE.Views.DocumentHolder.txtSelect": "Абраць", "SSE.Views.DocumentHolder.txtShiftDown": "Ячэйкі са зрухам уніз", "SSE.Views.DocumentHolder.txtShiftLeft": "Ячэйкі са зрухам улева", "SSE.Views.DocumentHolder.txtShiftRight": "Ячэйкі са зрухам управа", "SSE.Views.DocumentHolder.txtShiftUp": "Ячэйкі са зрухам уверх", "SSE.Views.DocumentHolder.txtShow": "Паказаць", "SSE.Views.DocumentHolder.txtShowAs": "Паказваць значэнні як", "SSE.Views.DocumentHolder.txtShowComment": "Паказаць каментар", "SSE.Views.DocumentHolder.txtShowDetails": "Паказаць падрабязнасці", "SSE.Views.DocumentHolder.txtSort": "Сартаванне", "SSE.Views.DocumentHolder.txtSortCellColor": "Спачатку ячэйкі з абраным колерам", "SSE.Views.DocumentHolder.txtSortFontColor": "Спачатку ячэйкі з абраным шрыфтам", "SSE.Views.DocumentHolder.txtSortOption": "Дадатковыя параметры сартавання", "SSE.Views.DocumentHolder.txtSparklines": "Спарклайны", "SSE.Views.DocumentHolder.txtSubtotalField": "Прамежкавы вынік", "SSE.Views.DocumentHolder.txtSum": "Сума", "SSE.Views.DocumentHolder.txtSummarize": "Выніковыя значэнні па", "SSE.Views.DocumentHolder.txtText": "Тэкст", "SSE.Views.DocumentHolder.txtTextAdvanced": "Дадатковыя налады абзаца", "SSE.Views.DocumentHolder.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtTop10": "Першыя 10", "SSE.Views.DocumentHolder.txtUngroup": "Разгрупаваць", "SSE.Views.DocumentHolder.txtValueFieldSettings": "Налады поля значэнняў", "SSE.Views.DocumentHolder.txtValueFilter": "Фільтры значэнняў", "SSE.Views.DocumentHolder.txtWidth": "Шырыня", "SSE.Views.DocumentHolder.unicodeText": "Юнік<PERSON>д", "SSE.Views.DocumentHolder.vertAlignText": "Вертыкальнае выраўноўванне", "SSE.Views.ExternalLinksDlg.closeButtonText": "Закрыць", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Automatically update data from the linked sources", "SSE.Views.ExternalLinksDlg.textChange": "Change source", "SSE.Views.ExternalLinksDlg.textDelete": "Разарваць спасылкі", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Разарваць усе спасылкі", "SSE.Views.ExternalLinksDlg.textOk": "Добра", "SSE.Views.ExternalLinksDlg.textOpen": "Open source", "SSE.Views.ExternalLinksDlg.textSource": "Крыніца", "SSE.Views.ExternalLinksDlg.textStatus": "Статус", "SSE.Views.ExternalLinksDlg.textUnknown": "Невядома", "SSE.Views.ExternalLinksDlg.textUpdate": "Абнавіць значэнні", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Абнав<PERSON>ць усе", "SSE.Views.ExternalLinksDlg.textUpdating": "Абнаўленне…", "SSE.Views.ExternalLinksDlg.txtTitle": "Вонкавыя спасылкі", "SSE.Views.FieldSettingsDialog.strLayout": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.strSubtotals": "Прамежкавыя вынікі", "SSE.Views.FieldSettingsDialog.textNumFormat": "Лічбавы фармат", "SSE.Views.FieldSettingsDialog.textReport": "Форма справаздачы", "SSE.Views.FieldSettingsDialog.textTitle": "Налады палёў", "SSE.Views.FieldSettingsDialog.txtAverage": "Сярэдняе", "SSE.Views.FieldSettingsDialog.txtBlank": "Устаўляць пусты радок пасля кожнага запісу", "SSE.Views.FieldSettingsDialog.txtBottom": "Паказваць у ніжняй частцы групы", "SSE.Views.FieldSettingsDialog.txtCompact": "Кампактная", "SSE.Views.FieldSettingsDialog.txtCount": "Колькасць", "SSE.Views.FieldSettingsDialog.txtCountNums": "Колькасць лікаў", "SSE.Views.FieldSettingsDialog.txtCustomName": "Адвольная назва", "SSE.Views.FieldSettingsDialog.txtEmpty": "Паказваць элементы без даных", "SSE.Views.FieldSettingsDialog.txtMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtMin": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtOutline": "Структура", "SSE.Views.FieldSettingsDialog.txtProduct": "Здабытак", "SSE.Views.FieldSettingsDialog.txtRepeat": "Паўтараць адмеціны элементаў у кожным радку", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Паказваць прамежкавыя вынікі", "SSE.Views.FieldSettingsDialog.txtSourceName": "Назва крыніцы:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "Сума", "SSE.Views.FieldSettingsDialog.txtSummarize": "Функцыі для прамежкавых вынікаў", "SSE.Views.FieldSettingsDialog.txtTabular": "Як табліца", "SSE.Views.FieldSettingsDialog.txtTop": "Паказваць у загалоўку групы", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.ariaFileMenu": "File menu", "SSE.Views.FileMenu.btnBackCaption": "Перайсці да дакументаў", "SSE.Views.FileMenu.btnCloseEditor": "Close File", "SSE.Views.FileMenu.btnCloseMenuCaption": "Закрыць меню", "SSE.Views.FileMenu.btnCreateNewCaption": "Стварыць новую", "SSE.Views.FileMenu.btnDownloadCaption": "Спампаваць як", "SSE.Views.FileMenu.btnExitCaption": "Закрыць", "SSE.Views.FileMenu.btnExportToPDFCaption": "Export to PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "Адкрыць", "SSE.Views.FileMenu.btnHelpCaption": "Даведка", "SSE.Views.FileMenu.btnHistoryCaption": "Гісторыя версій", "SSE.Views.FileMenu.btnInfoCaption": "Інфармацыя аб табліцы", "SSE.Views.FileMenu.btnPrintCaption": "Друкаванне", "SSE.Views.FileMenu.btnProtectCaption": "Аб<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ь", "SSE.Views.FileMenu.btnRecentFilesCaption": "Адкрыць апошнія", "SSE.Views.FileMenu.btnRenameCaption": "Змяніць назву", "SSE.Views.FileMenu.btnReturnCaption": "Назад да табліцы", "SSE.Views.FileMenu.btnRightsCaption": "Правы на доступ", "SSE.Views.FileMenu.btnSaveAsCaption": "Захаваць як", "SSE.Views.FileMenu.btnSaveCaption": "Захаваць", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Захаваць копію як", "SSE.Views.FileMenu.btnSettingsCaption": "Дадатковыя налады", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "SSE.Views.FileMenu.btnToEditCaption": "Рэдагаваць табліцу", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Пустая табліца", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Стварыць", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Ужыць", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Дадаць аўтара", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Дадаць тэкст", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Аўтар", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Змяніць правы на доступ", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Ка<PERSON><PERSON>н<PERSON>ар", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Створана", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Аўтар апошняй змены", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Апошняя змена", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Уладальнік", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Размяшчэнне", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Асобы, што маюць правы", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Інфармацыя пра табліцу", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Тэма", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Тэгі", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Назва", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Запампавана", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Правы на доступ", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Змяніць правы на доступ", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Асобы, што маюць правы", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Ужыць", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Рэжым сумеснага рэдагавання", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Выкарыстоўваць сістэму датаў 1904", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Дзесятковы падзяляльнік", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Мова слоўніка", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Enable iterative calculation", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Хутк<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Хінтынг шрыфтоў", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Мова формул", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Прыклад: СУМ; МІН; МАКС; ПАДЛІК", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Show function tooltip", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Show horizontal scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Прапускаць словы ВЯЛІКІМІ ЛІТАРАМІ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Прапускаць словы з лічбамі", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Налады макрасаў", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Maximum change", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Maximum iterations", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Паказваць кнопку параметраў устаўляння падчас устаўляння", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Стыль спасылак R1C1", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Рэгіянальныя налады", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Прыклад:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTL interface", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Паказваць каментары ў аркушы", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Паказваць змены ад іншых карыстальнікаў", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Паказваць развязаныя каментары", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Snapped to the grid while scrolling", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Строгі", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Tab style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Тэма інтэрфейсу", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Падзяляльнік разрадаў тысяч", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Адзінкі вымярэння", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Выкарыстоўваць падзяляльнікі на базе рэгіянальных налад", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Show vertical scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Прадвызначанае значэнне маштабу", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Кожныя 10 хвілін", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Кожныя 30 хвілін", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Кожныя 5 хвілін", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "<PERSON>т<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Аўтаматычнае аднаўленне", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Аўтазахаванне", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Выключана", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "Заліўка", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Захаванне прамежкавых версій", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "Лінія", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Кожн<PERSON>ю хвіліну", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Стыль спасылак", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "Дадатковыя налады", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "Выгляд", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Параметры аўтазамены…", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Беларуская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Балгарская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Каталанская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Прадвызначаны рэжым кэшу", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Вылічэнне", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Сантыметр", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Сумесная праца", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Чэшская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Customize quick access", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Дацкая", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Нямецкая", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Рэдагаванне і захаванне", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Грэчаская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "Англійская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Іспанская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Сумеснае рэдагаванне ў рэжыме рэальнага часу. Усе змены захоўваюцца аўтаматычна", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Фінская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "Французская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Венгерская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "Армянская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Інданезійская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Цаля", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Італьянская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Японская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Карэйская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Last used", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Лаоская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Латышская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "як OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Уласны", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Нарвежская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Галандская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Польская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Права<PERSON><PERSON>с", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "<PERSON>у<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Партугальская (Бразілія)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "Партугальская (Партугалія)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Паказваць кнопку хуткага друкавання ўверсе рэдактара", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "Дакумент будзе надрукаваны на прынтары, які вы абралі апошнім разам, або на прадвызначаным прынтары", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Рэ<PERSON><PERSON><PERSON>н", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Румынская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Расійская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Уключыць усе", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Уключыць усе макрасы без апавяшчэння", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Turn on screen reader support", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Default sheet direction", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "This setting will affect only the new sheets", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Left-to-right", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Right-to-left", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Славацкая", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Славенская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Адключыць усе", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Адключыць усе макрасы без апавяшчэння", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Для сінхранізацыі зменаў, унесеных іншымі карыстальнікамі, выкарыстоўвайце кнопку \"Захаваць\"", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Шведская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Use toolbar color as tabs background", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Турэцкая", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Украінская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Выкарыстоўваць клавішу Alt для навігацыі з дапамогай клавіятуры", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Выкарыстоўваць клавішу Option для навігацыі з дапамогай клавіятуры", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "В'етнамская", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Паказваць апавяшчэнне", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Адключыць усе макрасы з апавяшчэннем", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "як Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Працоўная прастора", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "Кітайская", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Увага", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Пры дапамозе пароля", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Аб<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> табліцу", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Пры дапамозе подпісу", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "У табліцу дададзены сапраўдныя подпісы.<br>Табліца абароненая ад рэдагавання.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Гарантаваць цэласнасць электроннай табліцы, дадаўшы <br>нябачны лічбавы подпіс", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Рэдагаваць табліцу", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Пры рэдагаванні табліцы выдаляцца подпісы.<br>Працягнуць?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Гэтая электронная табліца абароненая паролем", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Зашыфраваць гэтую табліцу з дапамогай пароля", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Гэтую табліцу неабходна падпісаць.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "У табліцу былі дададзеныя дзейныя подпісы. Табліца абароненая ад рэдагавання.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Некаторыя з лічбавых подпісаў электроннай табліцы хібныя альбо іх немагчыма праверыць. Табліца абароненая ад рэдагавання.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Прагляд подпісаў", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Спампаваць як", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Захаваць копію як", "SSE.Views.FillSeriesDialog.textAuto": "AutoFill", "SSE.Views.FillSeriesDialog.textCols": "Columns", "SSE.Views.FillSeriesDialog.textDate": "Date", "SSE.Views.FillSeriesDialog.textDateUnit": "Date unit", "SSE.Views.FillSeriesDialog.textDay": "Day", "SSE.Views.FillSeriesDialog.textGrowth": "Growth", "SSE.Views.FillSeriesDialog.textLinear": "Лінейны", "SSE.Views.FillSeriesDialog.textMonth": "Month", "SSE.Views.FillSeriesDialog.textRows": "Rows", "SSE.Views.FillSeriesDialog.textSeries": "Series in", "SSE.Views.FillSeriesDialog.textStep": "Step value", "SSE.Views.FillSeriesDialog.textStop": "Stop value", "SSE.Views.FillSeriesDialog.textTitle": "Series", "SSE.Views.FillSeriesDialog.textTrend": "Trend", "SSE.Views.FillSeriesDialog.textType": "Type", "SSE.Views.FillSeriesDialog.textWeek": "Weekday", "SSE.Views.FillSeriesDialog.textYear": "Year", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FormatRulesEditDlg.fillColor": "Колер запаўнення", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Увага", "SSE.Views.FormatRulesEditDlg.text2Scales": "Двухкаляровая шкала", "SSE.Views.FormatRulesEditDlg.text3Scales": "Трохкаляровая шкала", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Усе межы", "SSE.Views.FormatRulesEditDlg.textAppearance": "Выгляд панэлі", "SSE.Views.FormatRulesEditDlg.textApply": "Ужыць да дыяпазону", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Аўтаматычна", "SSE.Views.FormatRulesEditDlg.textAxis": "Восі", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Кірунак панэлі", "SSE.Views.FormatRulesEditDlg.textBold": "Тоўсты", "SSE.Views.FormatRulesEditDlg.textBorder": "Мяжа", "SSE.Views.FormatRulesEditDlg.textBordersColor": "Колер межаў", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Стыль межаў", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Ніжнія межы", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Не ўдалося дадаць умоўнае фарматаванне.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Сярэдзіна ячэйкі", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Унутраныя вертыкальныя межы", "SSE.Views.FormatRulesEditDlg.textClear": "Ачысціць", "SSE.Views.FormatRulesEditDlg.textColor": "Колер тэксту", "SSE.Views.FormatRulesEditDlg.textContext": "Кантэкст", "SSE.Views.FormatRulesEditDlg.textCustom": "Адвольны", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Дыяганальная мяжа зверху ўніз", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Дыяганальная мяжа знізу ўверх", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Увядзіце сапраўдную формулу.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "Уведзеная вамі формула не вяртае ліку, даты, часу або радка.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Увядзіце значэнне.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "Уведзенае вамі значэнне не ёсць сапраўдным лікам, датай, часам або радком.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "Значэнне для {0} мусіць быць большым за значэнне для {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Увядзіце лік ад {0} да {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Заліўка", "SSE.Views.FormatRulesEditDlg.textFormat": "Фарматаванне", "SSE.Views.FormatRulesEditDlg.textFormula": "Формула", "SSE.Views.FormatRulesEditDlg.textGradient": "Град<PERSON><PERSON><PERSON>т", "SSE.Views.FormatRulesEditDlg.textIconLabel": "калі {0} {1} і", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "калі {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "калі значэнне роўна", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Адзін або некалькі дыяпазонаў даных значкоў перакрываюць адзін аднаго.<br>Падладзьце значэнні дыяпазонаў, каб гэтага не адбывалася.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Стыль значка", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Унутраныя межы", "SSE.Views.FormatRulesEditDlg.textInvalid": "Хібны дыяпазон даных.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.FormatRulesEditDlg.textItalic": "Курсіў", "SSE.Views.FormatRulesEditDlg.textItem": "Элемент", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Злева ўправа", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Левыя межы", "SSE.Views.FormatRulesEditDlg.textLongBar": "Самая доўгая панэль", "SSE.Views.FormatRulesEditDlg.textMaximum": "Максімум", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Максімальны пункт", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Унутраныя гарызантальныя межы", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Сярэдні пункт", "SSE.Views.FormatRulesEditDlg.textMinimum": "Мін<PERSON><PERSON>ум", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Мінімальны пункт", "SSE.Views.FormatRulesEditDlg.textNegative": "Адмоўнае", "SSE.Views.FormatRulesEditDlg.textNewColor": "Яшчэ колеры", "SSE.Views.FormatRulesEditDlg.textNoBorders": "Без межаў", "SSE.Views.FormatRulesEditDlg.textNone": "Няма", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Прынамсі адно з вызначаных значэнняў не ёсць сапраўдным адсоткам.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Вызначанае значэнне {0} не ёсць сапраўдным адсоткам.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Прынамсі адно з вызначаных значэнняў не ёсць сапраўдным працэнтылем.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Вызначанае значэнне {0} не ёсць сапраўдным працэнтылем.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Вонкавыя межы", "SSE.Views.FormatRulesEditDlg.textPercent": "У адсотках", "SSE.Views.FormatRulesEditDlg.textPercentile": "Працэнтыль", "SSE.Views.FormatRulesEditDlg.textPosition": "Пазіцыя", "SSE.Views.FormatRulesEditDlg.textPositive": "Дада<PERSON>ны", "SSE.Views.FormatRulesEditDlg.textPresets": "Перадналады", "SSE.Views.FormatRulesEditDlg.textPreview": "Прагляд", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Пры ўмоўным фарматаванні нельга выкарыстоўваць адносныя спасылкі для каляровых шкал, гістаграм і набораў значкоў.", "SSE.Views.FormatRulesEditDlg.textReverse": "Адваротны парадак значкоў", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Справа ўлева", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Правыя межы", "SSE.Views.FormatRulesEditDlg.textRule": "Правіла", "SSE.Views.FormatRulesEditDlg.textSameAs": "Як дадатны", "SSE.Views.FormatRulesEditDlg.textSelectData": "Абраць даныя", "SSE.Views.FormatRulesEditDlg.textShortBar": "самая кароткая панэль", "SSE.Views.FormatRulesEditDlg.textShowBar": "Паказваць толькі панэль", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Паказваць толькі значок", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Гэты тып спасылкі нельга выкарыстоўваць у формуле ўмоўнага фарматавання.<br>Змяніце спасылку такім чынам, каб яна спасылалася на адну ячэйку або змясціце яе ў функцыю. Напрыклад: =СУМ(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Суцэльны", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Закрэслены", "SSE.Views.FormatRulesEditDlg.textSubscript": "Падрадковыя", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Надрадковыя", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Верхнія межы", "SSE.Views.FormatRulesEditDlg.textUnderline": "Падкрэслены", "SSE.Views.FormatRulesEditDlg.tipBorders": "Межы", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Лічбавы фармат", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Фінансавы", "SSE.Views.FormatRulesEditDlg.txtCurrency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDate": "Дата", "SSE.Views.FormatRulesEditDlg.txtDateLong": "Доўгі фармат даты", "SSE.Views.FormatRulesEditDlg.txtDateShort": "Кароткі фармат даты", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.FormatRulesEditDlg.txtFraction": "Дроб", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Агульны", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "Няма значка", "SSE.Views.FormatRulesEditDlg.txtNumber": "Лічбавы", "SSE.Views.FormatRulesEditDlg.txtPercentage": "У адсотках", "SSE.Views.FormatRulesEditDlg.txtScientific": "Навуковы", "SSE.Views.FormatRulesEditDlg.txtText": "Тэкст", "SSE.Views.FormatRulesEditDlg.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Рэдагаваць правіла фарматавання", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Новае правіла фарматавання", "SSE.Views.FormatRulesManagerDlg.guestText": "Госць", "SSE.Views.FormatRulesManagerDlg.lockText": "Заблакавана", "SSE.Views.FormatRulesManagerDlg.text1Above": "На адно стандартнае адхіленне вышэй за сярэдняе", "SSE.Views.FormatRulesManagerDlg.text1Below": "На адно стандартнае адхіленне ніжэй за сярэдняе", "SSE.Views.FormatRulesManagerDlg.text2Above": "На два стандартных адхілення вышэй за сярэдняе", "SSE.Views.FormatRulesManagerDlg.text2Below": "На два стандартных адхілення ніжэй за сярэдняе", "SSE.Views.FormatRulesManagerDlg.text3Above": "На тры стандартных адхілення вышэй за сярэдняе", "SSE.Views.FormatRulesManagerDlg.text3Below": "На тры стандартных адхілення ніжэй за сярэдняе", "SSE.Views.FormatRulesManagerDlg.textAbove": "Вышэй за сярэдняе", "SSE.Views.FormatRulesManagerDlg.textApply": "Ужыць да", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Значэнне ячэйкі пачынаецца з", "SSE.Views.FormatRulesManagerDlg.textBelow": "Ніжэй за сярэдняе", "SSE.Views.FormatRulesManagerDlg.textBetween": "знаходзіцца паміж {0} і {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Значэнне ячэйкі", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Шкала градацыі колераў", "SSE.Views.FormatRulesManagerDlg.textContains": "Значэнне ячэйкі змяшчае", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Ячэйка змяшчае пустое значэнне", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Ячэйка змяшчае памылку", "SSE.Views.FormatRulesManagerDlg.textDelete": "Выдаліць", "SSE.Views.FormatRulesManagerDlg.textDown": "Перамясціць правіла ніжэй", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Значэнні дублююцца", "SSE.Views.FormatRulesManagerDlg.textEdit": "Рэдагаваць", "SSE.Views.FormatRulesManagerDlg.textEnds": "Значэнне ячэйкі сканчаецца на", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Роўна або вышэй сярэдняга", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Роўна аб ніжэй сярэдняга", "SSE.Views.FormatRulesManagerDlg.textFormat": "Фарматаванне", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Набор значкоў", "SSE.Views.FormatRulesManagerDlg.textNew": "Новы", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "не знаходзіцца паміж {0} і {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Значэнне ячэйкі не змяшчае", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Ячэйка не змяшчае пустое значэнне", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Ячэйка не змяшчае памылку", "SSE.Views.FormatRulesManagerDlg.textRules": "Правілы", "SSE.Views.FormatRulesManagerDlg.textScope": "Паказваць правілы фарматавання для", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Абраць даныя", "SSE.Views.FormatRulesManagerDlg.textSelection": "Бягучае абранае", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "Гэтая зводная табліца", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Гэты аркуш", "SSE.Views.FormatRulesManagerDlg.textThisTable": "Гэтая табліца", "SSE.Views.FormatRulesManagerDlg.textUnique": "Унікальныя значэнні", "SSE.Views.FormatRulesManagerDlg.textUp": "Перамясціць правіла вышэй", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Гэты элемент рэдагуецца іншым карыстальнікам.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Умоўнае фарматаванне", "SSE.Views.FormatSettingsDialog.textCategory": "Катэгорыя", "SSE.Views.FormatSettingsDialog.textDecimal": "Дзесятковыя знакі", "SSE.Views.FormatSettingsDialog.textFormat": "Фармат", "SSE.Views.FormatSettingsDialog.textLinked": "Звязана з крыніцай", "SSE.Views.FormatSettingsDialog.textSeparator": "Выкарыстоўваць падзяляльнік разрадаў", "SSE.Views.FormatSettingsDialog.textSymbols": "Сімвалы", "SSE.Views.FormatSettingsDialog.textTitle": "Фармат нумара", "SSE.Views.FormatSettingsDialog.txtAccounting": "Фінансавы", "SSE.Views.FormatSettingsDialog.txtAs10": "Дзясятыя часткі (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "Сотыя часткі (50/100) ", "SSE.Views.FormatSettingsDialog.txtAs16": "Шаснаццатыя часткі (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Паловы (1/2) ", "SSE.Views.FormatSettingsDialog.txtAs4": "Чацвёртыя часткі (2/4) ", "SSE.Views.FormatSettingsDialog.txtAs8": "Восьмыя часткі (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustom": "Адвольны", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Уводзьце адмысловы лічбавы фармат уважліва. Рэдактар электронных табліц не правярае адвольныя фарматы на наяўнасць памылак, таму памылкі могуць паўплываць на файл xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "Дата", "SSE.Views.FormatSettingsDialog.txtFraction": "Дроб", "SSE.Views.FormatSettingsDialog.txtGeneral": "Агульны", "SSE.Views.FormatSettingsDialog.txtNone": "Няма", "SSE.Views.FormatSettingsDialog.txtNumber": "Лічбавы", "SSE.Views.FormatSettingsDialog.txtPercentage": "У адсотках", "SSE.Views.FormatSettingsDialog.txtSample": "Прыклад:", "SSE.Views.FormatSettingsDialog.txtScientific": "Навуковы", "SSE.Views.FormatSettingsDialog.txtText": "Тэкст", "SSE.Views.FormatSettingsDialog.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtUpto1": "Да адной лічбы (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Да двух лічбаў (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Да трох лічбаў (131/135)", "SSE.Views.FormulaDialog.sDescription": "Апісанне", "SSE.Views.FormulaDialog.textGroupDescription": "Абраць групу функцый", "SSE.Views.FormulaDialog.textListDescription": "Абраць функцыю", "SSE.Views.FormulaDialog.txtRecommended": "Рэкамендаваныя", "SSE.Views.FormulaDialog.txtSearch": "По<PERSON><PERSON>к", "SSE.Views.FormulaDialog.txtTitle": "Уставіць функцыю", "SSE.Views.FormulaTab.capBtnRemoveArr": "Remove Arrows", "SSE.Views.FormulaTab.capBtnTraceDep": "Trace Dependents", "SSE.Views.FormulaTab.capBtnTracePrec": "Trace Precedents", "SSE.Views.FormulaTab.textAutomatic": "Аўтаматычна", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Падлік бягучага аркуша", "SSE.Views.FormulaTab.textCalculateWorkbook": "Пад<PERSON><PERSON><PERSON> кнігі", "SSE.Views.FormulaTab.textManual": "Уласнаручна", "SSE.Views.FormulaTab.tipCalculate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Падлік усёй кнігі", "SSE.Views.FormulaTab.tipRemoveArr": "Remove the arrows drawn by Trace Precedents or Trace Dependents", "SSE.Views.FormulaTab.tipShowFormulas": "Display the formula in each cell instead of the resulting value", "SSE.Views.FormulaTab.tipTraceDep": "Show arrows that indicate which cells are affected by the value of the selected cell", "SSE.Views.FormulaTab.tipTracePrec": "Show arrows that indicate which cells affect the value of the selected cell", "SSE.Views.FormulaTab.tipWatch": "Дада<PERSON>ь ячэйкі ў спіс акна назірання", "SSE.Views.FormulaTab.txtAdditional": "Дадаткова", "SSE.Views.FormulaTab.txtAutosum": "Аўтасума", "SSE.Views.FormulaTab.txtAutosumTip": "Сума", "SSE.Views.FormulaTab.txtCalculation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormula": "Функцыя", "SSE.Views.FormulaTab.txtFormulaTip": "Уставіць функцыю", "SSE.Views.FormulaTab.txtMore": "Іншыя функцыі", "SSE.Views.FormulaTab.txtRecent": "Апошнія выкарыстаныя", "SSE.Views.FormulaTab.txtRemDep": "Remove Dependents Arrows", "SSE.Views.FormulaTab.txtRemPrec": "Remove Precedents Arrows", "SSE.Views.FormulaTab.txtShowFormulas": "Show Formulas", "SSE.Views.FormulaTab.txtWatch": "Акно назірання", "SSE.Views.FormulaWizard.textAny": "любы", "SSE.Views.FormulaWizard.textArgument": "Аргумент", "SSE.Views.FormulaWizard.textFunction": "Функцыя", "SSE.Views.FormulaWizard.textFunctionRes": "Вынік функцыі", "SSE.Views.FormulaWizard.textHelp": "Даведка па гэтай функцыі", "SSE.Views.FormulaWizard.textLogical": "лагічныя", "SSE.Views.FormulaWizard.textNoArgs": "У гэтай функцыі няма аргументаў", "SSE.Views.FormulaWizard.textNoArgsDesc": "this argument has no description", "SSE.Views.FormulaWizard.textNumber": "лічбавы", "SSE.Views.FormulaWizard.textReadMore": "Read more", "SSE.Views.FormulaWizard.textRef": "спасылка", "SSE.Views.FormulaWizard.textText": "Тэкст", "SSE.Views.FormulaWizard.textTitle": "Аргументы функцыі", "SSE.Views.FormulaWizard.textValue": "Вынік формулы", "SSE.Views.GoalSeekDlg.textChangingCell": "By changing cell", "SSE.Views.GoalSeekDlg.textDataRangeError": "The formula is missing a range", "SSE.Views.GoalSeekDlg.textMustContainFormula": "The cell must contain a formula", "SSE.Views.GoalSeekDlg.textMustContainValue": "Cell must contain a value", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Formula in cell must result in a number", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Reference must be to a single cell", "SSE.Views.GoalSeekDlg.textSelectData": "Select data", "SSE.Views.GoalSeekDlg.textSetCell": "Set cell", "SSE.Views.GoalSeekDlg.textTitle": "Goal seek", "SSE.Views.GoalSeekDlg.textToValue": "To value", "SSE.Views.GoalSeekDlg.txtEmpty": "This field is required", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.GoalSeekStatusDlg.textContinue": "Continue", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Current value:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Goal seeking with cell {0} found a solution.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Goal seeking with cell {0} may not have found a solution.", "SSE.Views.GoalSeekStatusDlg.textPause": "Pause", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Goal seeking with cell {0} on iteration #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "Step", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Target value:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Goal seek status", "SSE.Views.HeaderFooterDialog.textAlign": "Выраўнаваць адносна палёў старонкі", "SSE.Views.HeaderFooterDialog.textAll": "Усе старонкі", "SSE.Views.HeaderFooterDialog.textBold": "Тоўсты", "SSE.Views.HeaderFooterDialog.textCenter": "Па цэнтры", "SSE.Views.HeaderFooterDialog.textColor": "Колер тэксту", "SSE.Views.HeaderFooterDialog.textDate": "Дата", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Асобны для першай старонкі", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Асобныя для цотных і няцотных", "SSE.Views.HeaderFooterDialog.textEven": "Цотная старонка", "SSE.Views.HeaderFooterDialog.textFileName": "Назва файла", "SSE.Views.HeaderFooterDialog.textFirst": "Першая старонка", "SSE.Views.HeaderFooterDialog.textFooter": "Ніжні калонтытул", "SSE.Views.HeaderFooterDialog.textHeader": "Верхні калонтытул", "SSE.Views.HeaderFooterDialog.textImage": "Малюнак", "SSE.Views.HeaderFooterDialog.textInsert": "Уставіць", "SSE.Views.HeaderFooterDialog.textItalic": "Курсіў", "SSE.Views.HeaderFooterDialog.textLeft": "Злева", "SSE.Views.HeaderFooterDialog.textMaxError": "Уведзены занадта доўгі тэкставы радок. Паменшыце колькасць знакаў.", "SSE.Views.HeaderFooterDialog.textNewColor": "Яшчэ колеры", "SSE.Views.HeaderFooterDialog.textOdd": "Няцотная старонка", "SSE.Views.HeaderFooterDialog.textPageCount": "Колькасць старонак", "SSE.Views.HeaderFooterDialog.textPageNum": "Нумар старон<PERSON>і", "SSE.Views.HeaderFooterDialog.textPresets": "Перадналады", "SSE.Views.HeaderFooterDialog.textRight": "Справа", "SSE.Views.HeaderFooterDialog.textScale": "Змяняць маштаб разам з дакументам", "SSE.Views.HeaderFooterDialog.textSheet": "Назва аркуша", "SSE.Views.HeaderFooterDialog.textStrikeout": "Закрэслены", "SSE.Views.HeaderFooterDialog.textSubscript": "Падрадковыя", "SSE.Views.HeaderFooterDialog.textSuperscript": "Надрадковыя", "SSE.Views.HeaderFooterDialog.textTime": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textTitle": "Налады калонтытулаў", "SSE.Views.HeaderFooterDialog.textUnderline": "Падкрэслены", "SSE.Views.HeaderFooterDialog.tipFontName": "Шры<PERSON>т", "SSE.Views.HeaderFooterDialog.tipFontSize": "Памер шрыфту", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Паказваць", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Звязаць з", "SSE.Views.HyperlinkSettingsDialog.strRange": "Дыяпазон", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON>р<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Капіяваць", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Абраны дыяпазон", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Увядзіце сюды подпіс", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Увядзіце сюды спасылку", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Увядзіце сюды падказку", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Вонкавая спасылка", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Атрымаць спасылку", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Унутраны дыяпазон даных", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.HyperlinkSettingsDialog.textNames": "Пэўныя назвы", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Абраць даныя", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Арк<PERSON><PERSON>ы", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Тэкст падказкі", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Налады гіперспасылкі", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Гэтае поле павінна быць URL-адрасам фармату \"http://www.example.com\"", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Гэтае поле можа змяшчаць не больш за 2083 сімвалы", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "SSE.Views.ImageSettings.strTransparency": "Opacity", "SSE.Views.ImageSettings.textAdvanced": "Дадатковыя налады", "SSE.Views.ImageSettings.textCrop": "Абрэзаць", "SSE.Views.ImageSettings.textCropFill": "Заліўка", "SSE.Views.ImageSettings.textCropFit": "Умясціць", "SSE.Views.ImageSettings.textCropToShape": "Абрэзаць па фігуры", "SSE.Views.ImageSettings.textEdit": "Рэдагаваць", "SSE.Views.ImageSettings.textEditObject": "Рэдагаваць аб’ект", "SSE.Views.ImageSettings.textFlip": "Пераварочванне", "SSE.Views.ImageSettings.textFromFile": "З файла", "SSE.Views.ImageSettings.textFromStorage": "Са сховішча", "SSE.Views.ImageSettings.textFromUrl": "Па URL", "SSE.Views.ImageSettings.textHeight": "Вышыня", "SSE.Views.ImageSettings.textHint270": "Павярнуць улева на 90°", "SSE.Views.ImageSettings.textHint90": "Павярнуць управа на 90°", "SSE.Views.ImageSettings.textHintFlipH": "Адлюстраваць па гарызанталі", "SSE.Views.ImageSettings.textHintFlipV": "Адлюстраваць па вертыкалі", "SSE.Views.ImageSettings.textInsert": "Замяніць выяву", "SSE.Views.ImageSettings.textKeepRatio": "Захаваць прапорцыі", "SSE.Views.ImageSettings.textOriginalSize": "Актуальны памер", "SSE.Views.ImageSettings.textRecentlyUsed": "Апошнія выкарыстаныя", "SSE.Views.ImageSettings.textResetCrop": "Reset crop", "SSE.Views.ImageSettings.textRotate90": "Павярнуць на 90°", "SSE.Views.ImageSettings.textRotation": "Паварочванне", "SSE.Views.ImageSettings.textSize": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textWidth": "Шырыня", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Не перамяшчаць і не змяняць памеры разам з ячэйкамі", "SSE.Views.ImageSettingsAdvanced.textAlt": "Альтэрнатыўны тэкст", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Апісанне", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Альтэрнатыўная тэкставая падача інфармацыі пра візуальны аб’ект, якая будзе агучвацца для слабавідушчых людзей ці людзей з кагнітыўнымі парушэннямі, каб дапамагчы ім зразумець інфармацыю, якую змяшчае выява, аўтафігура, дыяграма ці табліца.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Загаловак", "SSE.Views.ImageSettingsAdvanced.textAngle": "Вугал", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Перавернута", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Па гарызанталі", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Перам<PERSON><PERSON><PERSON><PERSON><PERSON>ь, але не змяняць памеры разам з ячэйкамі.", "SSE.Views.ImageSettingsAdvanced.textRotation": "Паварочванне", "SSE.Views.ImageSettingsAdvanced.textSnap": "Далучэнне да ячэйкі", "SSE.Views.ImageSettingsAdvanced.textTitle": "Выява - дадатковыя налады", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Перамяшчаць і змяняць памеры разам з ячэйкамі", "SSE.Views.ImageSettingsAdvanced.textVertically": "Па вертыкалі", "SSE.Views.ImportFromXmlDialog.textDestination": "Абярыце месца размяшчэння даных", "SSE.Views.ImportFromXmlDialog.textExist": "Існы аркуш", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Хібны дыяпазон ячэек", "SSE.Views.ImportFromXmlDialog.textNew": "Новы аркуш", "SSE.Views.ImportFromXmlDialog.textSelectData": "Абраць даныя", "SSE.Views.ImportFromXmlDialog.textTitle": "Імпартаванне даных", "SSE.Views.ImportFromXmlDialog.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.LeftMenu.ariaLeftMenu": "Left menu", "SSE.Views.LeftMenu.tipAbout": "Пра праграму", "SSE.Views.LeftMenu.tipChat": "Размова", "SSE.Views.LeftMenu.tipComments": "Каментары", "SSE.Views.LeftMenu.tipFile": "<PERSON>а<PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "Убудовы", "SSE.Views.LeftMenu.tipSearch": "По<PERSON><PERSON>к", "SSE.Views.LeftMenu.tipSpellcheck": "Праверка правапісу", "SSE.Views.LeftMenu.tipSupport": "Зваротная сувязь і падтрымка", "SSE.Views.LeftMenu.txtDeveloper": "РЭЖЫМ РАСПРАЦОЎНІКА", "SSE.Views.LeftMenu.txtEditor": "Рэдакт<PERSON><PERSON> табліц", "SSE.Views.LeftMenu.txtLimit": "Абмежаваны доступ", "SSE.Views.LeftMenu.txtTrial": "ПРОБНЫ РЭЖЫМ", "SSE.Views.LeftMenu.txtTrialDev": "Пробны рэжым распрацоўніка", "SSE.Views.MacroDialog.textMacro": "Назва макраса", "SSE.Views.MacroDialog.textTitle": "Прызначыць макрас", "SSE.Views.MainSettingsPrint.okButtonText": "Захаваць", "SSE.Views.MainSettingsPrint.strBottom": "Знізу", "SSE.Views.MainSettingsPrint.strLandscape": "Альбомная", "SSE.Views.MainSettingsPrint.strLeft": "Злева", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON>а<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "Кніжная", "SSE.Views.MainSettingsPrint.strPrint": "Друкаванне", "SSE.Views.MainSettingsPrint.strPrintTitles": "Друкаваць загалоўкі", "SSE.Views.MainSettingsPrint.strRight": "Справа", "SSE.Views.MainSettingsPrint.strTop": "Уверсе", "SSE.Views.MainSettingsPrint.textActualSize": "Актуальны памер", "SSE.Views.MainSettingsPrint.textCustom": "Адвольны", "SSE.Views.MainSettingsPrint.textCustomOptions": "Адвольныя параметры", "SSE.Views.MainSettingsPrint.textFitCols": "Умясціць усе слупкі на адной старонцы", "SSE.Views.MainSettingsPrint.textFitPage": "Умясціць аркуш на адной старонцы", "SSE.Views.MainSettingsPrint.textFitRows": "Умясціць усе радкі на адной старонцы", "SSE.Views.MainSettingsPrint.textPageOrientation": "Арыентацыя старонкі", "SSE.Views.MainSettingsPrint.textPageScaling": "Ма<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPageSize": "Памер старон<PERSON>і", "SSE.Views.MainSettingsPrint.textPrintGrid": "Друкаваць сетку", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Друкаваць загалоўкі радкоў і слупкоў", "SSE.Views.MainSettingsPrint.textRepeat": "Паўтараць…", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Паўтараць слупкі злева", "SSE.Views.MainSettingsPrint.textRepeatTop": "Паўтараць радкі зверху", "SSE.Views.MainSettingsPrint.textSettings": "Налады для", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "На дадзены момант немагчыма рэдагаваць існыя названыя дыяпазоны і ствараць новыя,<br>бо некаторыя з іх рэдагуюцца.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Пэўная назва", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Увага", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Кніга", "SSE.Views.NamedRangeEditDlg.textDataRange": "Дыяпазон даных", "SSE.Views.NamedRangeEditDlg.textExistName": "ПАМЫЛКА! Дыяпазон з такой назвай ужо існуе", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Назва мусіць пачынацца з літары альбо ніжняга падкрэслівання і не павінна змяшчаць непрыдатных сімвалаў.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ПАМЫЛКА! Гэты элемент рэдагуецца іншым карыстальнікам.", "SSE.Views.NamedRangeEditDlg.textName": "Назва", "SSE.Views.NamedRangeEditDlg.textReservedName": "Формулы ў ячэйках ужо змяшчаюць назву, якую вы спрабуеце выкарыстоўваць. Выкарыстайце іншую назву.", "SSE.Views.NamedRangeEditDlg.textScope": "Вобласць", "SSE.Views.NamedRangeEditDlg.textSelectData": "Абраць даныя", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Рэдагаваць назву", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Новая назва", "SSE.Views.NamedRangePasteDlg.textNames": "Названыя дыяпазоны", "SSE.Views.NamedRangePasteDlg.txtTitle": "Уставіць назву", "SSE.Views.NameManagerDlg.closeButtonText": "Закрыць", "SSE.Views.NameManagerDlg.guestText": "Госць", "SSE.Views.NameManagerDlg.lockText": "Заблакавана", "SSE.Views.NameManagerDlg.textDataRange": "Дыяпазон даных", "SSE.Views.NameManagerDlg.textDelete": "Выдаліць", "SSE.Views.NameManagerDlg.textEdit": "Рэдагаваць", "SSE.Views.NameManagerDlg.textEmpty": "Яшчэ не створана ніводнага названага дыяпазону.<br>Стварыце хоць адзін названы дыяпазон, і ён з’явіцца ў гэтым полі.", "SSE.Views.NameManagerDlg.textFilter": "Фільтр", "SSE.Views.NameManagerDlg.textFilterAll": "Усе", "SSE.Views.NameManagerDlg.textFilterDefNames": "Пэўныя назвы", "SSE.Views.NameManagerDlg.textFilterSheet": "Назвы на аркушы", "SSE.Views.NameManagerDlg.textFilterTableNames": "Назвы табліц", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Назвы ў кнізе", "SSE.Views.NameManagerDlg.textNew": "Новы", "SSE.Views.NameManagerDlg.textnoNames": "Не знойдзена названых дыяпазонаў, якія адпавядаюць фільтру. ", "SSE.Views.NameManagerDlg.textRanges": "Названыя дыяпазоны", "SSE.Views.NameManagerDlg.textScope": "Вобласць", "SSE.Views.NameManagerDlg.textWorkbook": "Кніга", "SSE.Views.NameManagerDlg.tipIsLocked": "Гэты элемент рэдагуецца іншым карыстальнікам.", "SSE.Views.NameManagerDlg.txtTitle": "Кіраўнік назваў", "SSE.Views.NameManagerDlg.warnDelete": "Сапраўды хочаце выдаліць назву {0}?", "SSE.Views.PageMarginsDialog.textBottom": "Ніжняе", "SSE.Views.PageMarginsDialog.textCenter": "Center on page", "SSE.Views.PageMarginsDialog.textHor": "Horizontally", "SSE.Views.PageMarginsDialog.textLeft": "Левае", "SSE.Views.PageMarginsDialog.textRight": "Правае", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON>а<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "Верхняе", "SSE.Views.PageMarginsDialog.textVert": "Vertically", "SSE.Views.PageMarginsDialog.textWarning": "Увага", "SSE.Views.PageMarginsDialog.warnCheckMargings": "Хібныя палі", "SSE.Views.ParagraphSettings.strLineHeight": "Прамежак паміж радкамі", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Прамежак паміж абзацамі", "SSE.Views.ParagraphSettings.strSpacingAfter": "Пасля", "SSE.Views.ParagraphSettings.strSpacingBefore": "Пер<PERSON>д", "SSE.Views.ParagraphSettings.textAdvanced": "Дадатковыя налады", "SSE.Views.ParagraphSettings.textAt": "Значэнне", "SSE.Views.ParagraphSettings.textAtLeast": "Мін<PERSON><PERSON>ум", "SSE.Views.ParagraphSettings.textAuto": "Множнік", "SSE.Views.ParagraphSettings.textExact": "Дак<PERSON><PERSON>дна", "SSE.Views.ParagraphSettings.txtAutoText": "Аўта", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Вызначаныя табуляцыі з’явяцца ў гэтым полі", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Усе ў верхнім рэгістры", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Падвойнае закрэсліванне", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Водступы", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Злева", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Прамежак паміж радкамі", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Справа", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Пасля", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Пер<PERSON>д", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Адмысловы", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "На", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Шры<PERSON>т", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Водступы і інтэрвалы", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Малыя прапісныя", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Прамежак", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Закрэсліванне", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Падрадковыя", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Надрадковыя", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Табуляцыя", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Выраўноўванне", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Множнік", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Прамежак паміж знакамі", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Прадвызначана", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Эфекты", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Дак<PERSON><PERSON>дна", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Першы радок", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Выступ", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Па шырыні", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(няма)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Выдаліць", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Выдаліць усе", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Вызначыць", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Па цэнтры", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Па леваму краю", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Пазіцыя", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Па праваму краю", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Абзац - дадатковыя налады", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Аўта", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Delete", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "Duplicate", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Edit", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Formula", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Items Name", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "New", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Calculated Items in", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "роўна", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "не заканчваецца на", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "Змяшчае", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "не змяшчае", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "паміж", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "не паміж", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "не роўна", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "больш за", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "больш альбо роўна", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "менш за", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "менш альбо роўна", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "Пачынаецца з", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "не пачынаецца з", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "заканчваецца на", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Паказваць элементы з адмецінамі:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Паказваць элементы, у якіх:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Выкарыстоўвайце знак ? замест любога асобнага сімвала", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Выкарыстоўвайце знак * замест любой паслядоўнасці сімвалаў", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "і", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Фільтр адмецін", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Фільтр значэнняў", "SSE.Views.PivotGroupDialog.textAuto": "Аўта", "SSE.Views.PivotGroupDialog.textBy": "На", "SSE.Views.PivotGroupDialog.textDays": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textEnd": "Сканчаецца ў", "SSE.Views.PivotGroupDialog.textError": "Гэтае поле мусіць змяшчаць лікавае значэнне", "SSE.Views.PivotGroupDialog.textGreaterError": "Канцавы лік мусіць быць большы за пачатковы.", "SSE.Views.PivotGroupDialog.textHour": "Гад<PERSON><PERSON>ны", "SSE.Views.PivotGroupDialog.textMin": "Х<PERSON><PERSON><PERSON><PERSON><PERSON>ы", "SSE.Views.PivotGroupDialog.textMonth": "Месяцы", "SSE.Views.PivotGroupDialog.textNumDays": "Колькасць дзён", "SSE.Views.PivotGroupDialog.textQuart": "Кварталы", "SSE.Views.PivotGroupDialog.textSec": "Секунды", "SSE.Views.PivotGroupDialog.textStart": "Пачынаючы з:", "SSE.Views.PivotGroupDialog.textYear": "Гады", "SSE.Views.PivotGroupDialog.txtTitle": "Групаванне", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "You can use Calculated Items for basic calculations between different items within a single field", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Insert into formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "<PERSON><PERSON>", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Item name", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Items", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Read more", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Insert Calculated Item in", "SSE.Views.PivotSettings.textAdvanced": "Дадатковыя налады", "SSE.Views.PivotSettings.textColumns": "Слупкі", "SSE.Views.PivotSettings.textFields": "Абра<PERSON>ь палі", "SSE.Views.PivotSettings.textFilters": "Фільтры", "SSE.Views.PivotSettings.textRows": "Радкі", "SSE.Views.PivotSettings.textValues": "Значэнні", "SSE.Views.PivotSettings.txtAddColumn": "Дада<PERSON>ь у слупкі", "SSE.Views.PivotSettings.txtAddFilter": "Дадаць у фільтры", "SSE.Views.PivotSettings.txtAddRow": "Дада<PERSON>ь у радкі", "SSE.Views.PivotSettings.txtAddValues": "Дада<PERSON>ь у значэнні", "SSE.Views.PivotSettings.txtFieldSettings": "Налады палёў", "SSE.Views.PivotSettings.txtMoveBegin": "Перамясціць у пачатак", "SSE.Views.PivotSettings.txtMoveColumn": "Перамясціць у слупкі", "SSE.Views.PivotSettings.txtMoveDown": "Перамясціць уніз", "SSE.Views.PivotSettings.txtMoveEnd": "Перамясціць у канец", "SSE.Views.PivotSettings.txtMoveFilter": "Перамясціць у фільтры", "SSE.Views.PivotSettings.txtMoveRow": "Перамясціць у радкі", "SSE.Views.PivotSettings.txtMoveUp": "Перамясціць уверх", "SSE.Views.PivotSettings.txtMoveValues": "Перамясціць у значэнні", "SSE.Views.PivotSettings.txtRemove": "Выдаліць поле", "SSE.Views.PivotSettingsAdvanced.strLayout": "Назва і макет", "SSE.Views.PivotSettingsAdvanced.textAlt": "Альтэрнатыўны тэкст", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Апісанне", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Альтэрнатыўная тэкставая падача інфармацыі пра візуальны аб’ект, якая будзе агучвацца для слабавідушчых людзей ці людзей з кагнітыўнымі парушэннямі, каб дапамагчы ім зразумець інфармацыю, якую змяшчае выява, фігура, дыяграма ці табліца.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Загаловак", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Пры абнаўленні аўтаматычна змяняць шырыню слупкоў", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Дыяпазон даных", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Крыніца даных", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Паказваць палі ў вобласці фільтра справаздачы", "SSE.Views.PivotSettingsAdvanced.textDown": "Уніз, пасля ўправа", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Агульныя вынікі", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Загалоўкі палёў", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.PivotSettingsAdvanced.textOver": "Управа, пасля ўніз", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Абраць даныя", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Паказваць для слупкоў", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Паказваць загалоўкі палёў для радкоў і слупкоў", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Паказваць для радкоў", "SSE.Views.PivotSettingsAdvanced.textTitle": "Зводная табліца - дадатковыя налады", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Колькасць палёў фільтра справаздачы ў слупку", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Колькасць палёў фільтра справаздачы ў радку", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.PivotSettingsAdvanced.txtName": "Назва", "SSE.Views.PivotShowDetailDialog.textDescription": "Choose the field containing the detail you want to show:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Show Detail", "SSE.Views.PivotTable.capBlankRows": "Пустыя радкі", "SSE.Views.PivotTable.capGrandTotals": "Агульныя вынікі", "SSE.Views.PivotTable.capLayout": "Макет справаздачы", "SSE.Views.PivotTable.capSubtotals": "Прамежкавыя вынікі", "SSE.Views.PivotTable.mniBottomSubtotals": "Паказваць усе прамежкавыя вынікі ўнізе групы", "SSE.Views.PivotTable.mniInsertBlankLine": "Устаўляць пусты радок пасля кожнага элемента", "SSE.Views.PivotTable.mniLayoutCompact": "Паказваць у сціслай форме", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Не паўтараць усе адмеціны элементаў", "SSE.Views.PivotTable.mniLayoutOutline": "Паказваць у форме структуры", "SSE.Views.PivotTable.mniLayoutRepeat": "Паўтараць усе адмеціны элементаў", "SSE.Views.PivotTable.mniLayoutTabular": "Паказваць у форме табліцы", "SSE.Views.PivotTable.mniNoSubtotals": "Не паказваць прамежкавыя вынікі", "SSE.Views.PivotTable.mniOffTotals": "Адключыць для радкоў і слупкоў", "SSE.Views.PivotTable.mniOnColumnsTotals": "Уключыць толькі для слупкоў", "SSE.Views.PivotTable.mniOnRowsTotals": "Уключыць толькі для радкоў", "SSE.Views.PivotTable.mniOnTotals": "Уключыць для слупкоў і радкоў", "SSE.Views.PivotTable.mniRemoveBlankLine": "Выдаліць пусты радок пасля кожнага элемента", "SSE.Views.PivotTable.mniTopSubtotals": "Паказваць усе прамежкавыя вынікі ўверсе групы", "SSE.Views.PivotTable.textColBanded": "Чаргаваць слупкі", "SSE.Views.PivotTable.textColHeader": "Загалоўкі слупкоў", "SSE.Views.PivotTable.textRowBanded": "Чаргаваць радкі", "SSE.Views.PivotTable.textRowHeader": "Загалоўкі радкоў", "SSE.Views.PivotTable.tipCalculatedItems": "Calculated items", "SSE.Views.PivotTable.tipCreatePivot": "Уставіць зводную табліцу", "SSE.Views.PivotTable.tipGrandTotals": "Паказаць альбо схаваць агульныя вынікі", "SSE.Views.PivotTable.tipRefresh": "Абнавіць інфармацыю з крыніцы даных", "SSE.Views.PivotTable.tipRefreshCurrent": "Абнавіць інфармацыю з крыніцы даных для бягучай табліцы", "SSE.Views.PivotTable.tipSelect": "Абраць усю зводную табліцу", "SSE.Views.PivotTable.tipSubtotals": "Паказаць альбо схаваць прамежкавыя вынікі", "SSE.Views.PivotTable.txtCalculatedItems": "Calculated Items", "SSE.Views.PivotTable.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.PivotTable.txtCreate": "Уставіць табліцу", "SSE.Views.PivotTable.txtExpandEntire": "Expand Entire Field", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Адвольна", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Цёмныя", "SSE.Views.PivotTable.txtGroupPivot_Light": "Светлыя", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Сярэднія", "SSE.Views.PivotTable.txtPivotTable": "Зводная табліца", "SSE.Views.PivotTable.txtRefresh": "Абнавіць", "SSE.Views.PivotTable.txtRefreshAll": "Абнав<PERSON>ць усе", "SSE.Views.PivotTable.txtSelect": "Абраць", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Цёмны стыль зводнай табліцы", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Светлы стыль зводнай табліцы", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Сярэдні стыль зводнай табліцы", "SSE.Views.PrintSettings.btnDownload": "Захаваць і спампаваць", "SSE.Views.PrintSettings.btnExport": "Save & Export", "SSE.Views.PrintSettings.btnPrint": "Захаваць і друкаваць", "SSE.Views.PrintSettings.strBottom": "Знізу", "SSE.Views.PrintSettings.strLandscape": "Альбомная", "SSE.Views.PrintSettings.strLeft": "Злева", "SSE.Views.PrintSettings.strMargins": "<PERSON>а<PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "Кніжная", "SSE.Views.PrintSettings.strPrint": "Друкаванне", "SSE.Views.PrintSettings.strPrintTitles": "Друкаваць загалоўкі", "SSE.Views.PrintSettings.strRight": "Справа", "SSE.Views.PrintSettings.strShow": "Паказаць", "SSE.Views.PrintSettings.strTop": "Уверсе", "SSE.Views.PrintSettings.textActiveSheets": "Актыўныя аркушы", "SSE.Views.PrintSettings.textActualSize": "Актуальны памер", "SSE.Views.PrintSettings.textAllSheets": "Усе аркушы", "SSE.Views.PrintSettings.textCurrentSheet": "Бягучы аркуш", "SSE.Views.PrintSettings.textCustom": "Адвольны", "SSE.Views.PrintSettings.textCustomOptions": "Адвольныя параметры", "SSE.Views.PrintSettings.textFitCols": "Умясціць усе слупкі на адной старонцы", "SSE.Views.PrintSettings.textFitPage": "Умясціць аркуш на адной старонцы", "SSE.Views.PrintSettings.textFitRows": "Умясціць усе радкі на адной старонцы", "SSE.Views.PrintSettings.textHideDetails": "Схаваць падрабязнасці", "SSE.Views.PrintSettings.textIgnore": "Ігнараваць вобласць друкавання", "SSE.Views.PrintSettings.textLayout": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textMarginsNarrow": "Вузкія", "SSE.Views.PrintSettings.textMarginsNormal": "Звычайны", "SSE.Views.PrintSettings.textMarginsWide": "Шырокія", "SSE.Views.PrintSettings.textPageOrientation": "Арыентацыя старонкі", "SSE.Views.PrintSettings.textPages": "Старонкі:", "SSE.Views.PrintSettings.textPageScaling": "Ма<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPageSize": "Памер старон<PERSON>і", "SSE.Views.PrintSettings.textPrintGrid": "Друкаваць сетку", "SSE.Views.PrintSettings.textPrintHeadings": "Друкаваць загалоўкі радкоў і слупкоў", "SSE.Views.PrintSettings.textPrintRange": "Дыяпазон друкавання", "SSE.Views.PrintSettings.textRange": "Дыяпазон", "SSE.Views.PrintSettings.textRepeat": "Паўтараць…", "SSE.Views.PrintSettings.textRepeatLeft": "Паўтараць слупкі злева", "SSE.Views.PrintSettings.textRepeatTop": "Паўтараць радкі зверху", "SSE.Views.PrintSettings.textSelection": "Абранае", "SSE.Views.PrintSettings.textSettings": "Налады аркуша", "SSE.Views.PrintSettings.textShowDetails": "Паказаць падрабязнасці", "SSE.Views.PrintSettings.textShowGrid": "Паказваць лініі сеткі", "SSE.Views.PrintSettings.textShowHeadings": "Паказаць загалоўкі радкоў і слупкоў", "SSE.Views.PrintSettings.textTitle": "Налады друку", "SSE.Views.PrintSettings.textTitlePDF": "Налады PDF", "SSE.Views.PrintSettings.textTo": "па", "SSE.Views.PrintSettings.txtMarginsLast": "Апошнія адвольныя", "SSE.Views.PrintTitlesDialog.textFirstCol": "Першы слупок", "SSE.Views.PrintTitlesDialog.textFirstRow": "Першы радок", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Замацаваныя слупкі", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Замацаваныя радкі", "SSE.Views.PrintTitlesDialog.textInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.PrintTitlesDialog.textLeft": "Паўтараць слупкі злева", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Не паўтараць", "SSE.Views.PrintTitlesDialog.textRepeat": "Паўтараць…", "SSE.Views.PrintTitlesDialog.textSelectRange": "Абраць дыяпазон", "SSE.Views.PrintTitlesDialog.textTitle": "Друкаваць загалоўкі", "SSE.Views.PrintTitlesDialog.textTop": "Паўтараць радкі зверху", "SSE.Views.PrintWithPreview.txtActiveSheets": "Актыўныя аркушы", "SSE.Views.PrintWithPreview.txtActualSize": "Актуальны памер", "SSE.Views.PrintWithPreview.txtAllSheets": "Усе аркушы", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Ужыць да ўсіх аркушаў", "SSE.Views.PrintWithPreview.txtBothSides": "Print on both sides", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "Flip pages on long edge", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "Flip pages on short edge", "SSE.Views.PrintWithPreview.txtBottom": "Ніжняе", "SSE.Views.PrintWithPreview.txtCopies": "Copies", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Бягучы аркуш", "SSE.Views.PrintWithPreview.txtCustom": "Адвольны", "SSE.Views.PrintWithPreview.txtCustomOptions": "Адвольныя параметры", "SSE.Views.PrintWithPreview.txtEmptyTable": "Няма чаго друкаваць, бо табліца пустая", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "Нумар пер<PERSON>ай старонкі:", "SSE.Views.PrintWithPreview.txtFitCols": "Умясціць усе слупкі на адной старонцы", "SSE.Views.PrintWithPreview.txtFitPage": "Умясціць аркуш на адной старонцы", "SSE.Views.PrintWithPreview.txtFitRows": "Умясціць усе радкі на адной старонцы", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Лініі сеткі і загалоўкі", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Налады калонтытулаў", "SSE.Views.PrintWithPreview.txtIgnore": "Ігнараваць вобласць друкавання", "SSE.Views.PrintWithPreview.txtLandscape": "Альбомная", "SSE.Views.PrintWithPreview.txtLeft": "Злева", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON>а<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMarginsLast": "Апошнія адвольныя", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "Вузкія", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Звычайны", "SSE.Views.PrintWithPreview.txtMarginsWide": "Шырокія", "SSE.Views.PrintWithPreview.txtOf": "з {0}", "SSE.Views.PrintWithPreview.txtOneSide": "Print one sided", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "SSE.Views.PrintWithPreview.txtPage": "Старонка", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Хібны нумар старонкі", "SSE.Views.PrintWithPreview.txtPageOrientation": "Арыентацыя старонкі", "SSE.Views.PrintWithPreview.txtPages": "Старонкі:", "SSE.Views.PrintWithPreview.txtPageSize": "Памер старон<PERSON>і", "SSE.Views.PrintWithPreview.txtPortrait": "Кніжная", "SSE.Views.PrintWithPreview.txtPrint": "Друкаванне", "SSE.Views.PrintWithPreview.txtPrintGrid": "Друкаваць сетку", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Друкаваць загалоўкі радкоў і слупкоў", "SSE.Views.PrintWithPreview.txtPrintRange": "Дыяпазон друкавання", "SSE.Views.PrintWithPreview.txtPrintSides": "Print sides", "SSE.Views.PrintWithPreview.txtPrintTitles": "Друкаваць загалоўкі", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Друкаванне ў PDF", "SSE.Views.PrintWithPreview.txtRepeat": "Паўтараць…", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Паўтараць слупкі злева", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Паўтараць радкі зверху", "SSE.Views.PrintWithPreview.txtRight": "Правае", "SSE.Views.PrintWithPreview.txtSave": "Захаваць", "SSE.Views.PrintWithPreview.txtScaling": "Ма<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSelection": "Абранае", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Налады аркуша", "SSE.Views.PrintWithPreview.txtSheet": "Аркуш: {0}", "SSE.Views.PrintWithPreview.txtTo": "па", "SSE.Views.PrintWithPreview.txtTop": "Верхняе", "SSE.Views.ProtectDialog.textExistName": "ПАМЫЛКА! Дыяпазон з такой назвай ужо існуе", "SSE.Views.ProtectDialog.textInvalidName": "Назва дыяпазону мусіць пачынацца з літары і можа змяшчаць толькі літары, лічбы і прагалы.", "SSE.Views.ProtectDialog.textInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.ProtectDialog.textSelectData": "Абраць даныя", "SSE.Views.ProtectDialog.txtAllow": "Дазволіць усім карыстальнікам аркуша", "SSE.Views.ProtectDialog.txtAllowDescription": "Вы можаце разблакаваць для рэдагавання вызначаныя дыяпазоны.", "SSE.Views.ProtectDialog.txtAllowRanges": "Дазволіць рэдагаванне дыяпазонаў", "SSE.Views.ProtectDialog.txtAutofilter": "Выкарыстоўваць аўтаматычны фільтр", "SSE.Views.ProtectDialog.txtDelCols": "Выдаліць слупкі", "SSE.Views.ProtectDialog.txtDelRows": "Выдаліць радкі", "SSE.Views.ProtectDialog.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.ProtectDialog.txtFormatCells": "Фарматаваць ячэйкі", "SSE.Views.ProtectDialog.txtFormatCols": "Фарматаваць слупкі", "SSE.Views.ProtectDialog.txtFormatRows": "Фарматаваць радкі", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Паролі адрозніваюцца", "SSE.Views.ProtectDialog.txtInsCols": "Уставіць слупкі", "SSE.Views.ProtectDialog.txtInsHyper": "Уставіць гіперспасылку", "SSE.Views.ProtectDialog.txtInsRows": "Уставіць радкі", "SSE.Views.ProtectDialog.txtObjs": "Рэдагаваць аб'екты", "SSE.Views.ProtectDialog.txtOptional": "неабавязкова", "SSE.Views.ProtectDialog.txtPassword": "Пароль", "SSE.Views.ProtectDialog.txtPivot": "Выкарыстоўваць зводную табліцу і зводную дыяграму", "SSE.Views.ProtectDialog.txtProtect": "Аб<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ь", "SSE.Views.ProtectDialog.txtRange": "Дыяпазон", "SSE.Views.ProtectDialog.txtRangeName": "Назва", "SSE.Views.ProtectDialog.txtRepeat": "Паўтарыць пароль", "SSE.Views.ProtectDialog.txtScen": "Рэдагаваць сцэнарыі", "SSE.Views.ProtectDialog.txtSelLocked": "Абраць заблакаваныя ячэйкі", "SSE.Views.ProtectDialog.txtSelUnLocked": "Абраць разблакаваныя ячэйкі", "SSE.Views.ProtectDialog.txtSheetDescription": "Каб прадухіліць непажаданыя змены ад іншых карыстальнікаў, можна абмежаваць правы на рэдагаванне.", "SSE.Views.ProtectDialog.txtSheetTitle": "Аб<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> аркуш", "SSE.Views.ProtectDialog.txtSort": "Сартаванне", "SSE.Views.ProtectDialog.txtWarning": "Увага: страчаны або забыты пароль аднавіць немагчыма. Захоўвайце яго ў надзейным месцы.", "SSE.Views.ProtectDialog.txtWBDescription": "Вы можаце абараніць структуру кнігі паролем, каб забараніць іншым карыстальнікам прагляд схаваных аркушаў, дад<PERSON>в<PERSON><PERSON><PERSON><PERSON>, перамя<PERSON><PERSON><PERSON>нне, выдаленне, хаванне, змену назвы аркушаў.", "SSE.Views.ProtectDialog.txtWBTitle": "Аб<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> структуру працоўнай кнігі", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "Ананімны карыстальнік", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Anyone", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Edit", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Denied", "SSE.Views.ProtectedRangesEditDlg.textCanView": "View", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "Назва дыяпазону мусіць пачынацца з літары і можа змяшчаць толькі літары, лічбы і прагалы.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "ПАМЫЛКА! Хібны дыяпазон ячэек", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Remove", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "Абраць даныя", "SSE.Views.ProtectedRangesEditDlg.textYou": "вы", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Access to range", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "Аб<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ь", "SSE.Views.ProtectedRangesEditDlg.txtRange": "Дыяпазон", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "Назва", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Толькі вы можаце рэдагаваць гэты дыяпазон", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Пачніце ўводзіць імя або адрас электроннай пошты", "SSE.Views.ProtectedRangesManagerDlg.guestText": "Госць", "SSE.Views.ProtectedRangesManagerDlg.lockText": "Заблакавана", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "Выдаліць", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "Рэдагаваць", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "Пакуль што абароненыя дыяпазоны не ствараліся.<br>Стварыце прынамсі адзін абаронены дыяпазон, і ён з'явіцца ў гэтым полі.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "Фільтр", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "Усе", "SSE.Views.ProtectedRangesManagerDlg.textNew": "Стварыць", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Аб<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> аркуш", "SSE.Views.ProtectedRangesManagerDlg.textRange": "Дыяпазон", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "Вы можаце абмежаваць абраным людзям магчымасць рэдагавання дыяпазонаў.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "Назва", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "Гэты элемент рэдагуецца іншым карыстальнікам.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Access", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Denied", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "Рэдагаваць", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "Рэдагаваць дыяпазон", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "Новы дыяпазон", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Абароненыя дыяпазоны", "SSE.Views.ProtectedRangesManagerDlg.txtView": "Прагляд", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Сапраўды хочаце выдаліць абаронены дыяпазон {0}?<br>Любы карыстальнік з правамі на рэдагаванне табліцы зможа рэдагаваць змесціва дыяпазону.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Сапраўды хочаце выдаліць абароненыя дыяпазоны {0}?<br>Любы карыстальнік з правамі на рэдагаванне табліцы зможа рэдагаваць змесціва дыяпазонаў.", "SSE.Views.ProtectRangesDlg.guestText": "Госць", "SSE.Views.ProtectRangesDlg.lockText": "Заблакавана", "SSE.Views.ProtectRangesDlg.textDelete": "Выдаліць", "SSE.Views.ProtectRangesDlg.textEdit": "Рэдагаваць", "SSE.Views.ProtectRangesDlg.textEmpty": "Няма дыяпазонаў, якія дазволена рэдагаваць.", "SSE.Views.ProtectRangesDlg.textNew": "Новы", "SSE.Views.ProtectRangesDlg.textProtect": "Аб<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> аркуш", "SSE.Views.ProtectRangesDlg.textPwd": "Пароль", "SSE.Views.ProtectRangesDlg.textRange": "Дыяпазон", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Дыяпазоны абароненага аркуша, разблакаваныя паролем (толькі для заблакаваных ячэек)", "SSE.Views.ProtectRangesDlg.textTitle": "Назва", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Гэты элемент рэдагуецца іншым карыстальнікам.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Рэдагаваць дыяпазон", "SSE.Views.ProtectRangesDlg.txtNewRange": "Новы дыяпазон", "SSE.Views.ProtectRangesDlg.txtNo": "Не", "SSE.Views.ProtectRangesDlg.txtTitle": "Дазволіць карыстальнікам рэдагаваць дыяпазоны", "SSE.Views.ProtectRangesDlg.txtYes": "Так", "SSE.Views.ProtectRangesDlg.warnDelete": "Сапраўды хочаце выдаліць назву {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Слупкі", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Каб выдаліць паўторныя значэнні, абярыце адзін альбо некалькі слупкоў, якія іх змяшчаюць.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Мае даныя змяшчаюць загалоўкі", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Аб<PERSON><PERSON><PERSON><PERSON> усё", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Выдаліць паўторы", "SSE.Views.RightMenu.ariaRightMenu": "Right menu", "SSE.Views.RightMenu.txtCellSettings": "Налады ячэйкі", "SSE.Views.RightMenu.txtChartSettings": "Налады дыяграмы", "SSE.Views.RightMenu.txtImageSettings": "Налады выявы", "SSE.Views.RightMenu.txtParagraphSettings": "Налады абзаца", "SSE.Views.RightMenu.txtPivotSettings": "Налады зводнай табліцы", "SSE.Views.RightMenu.txtSettings": "Асноўныя налады", "SSE.Views.RightMenu.txtShapeSettings": "Налады фігуры", "SSE.Views.RightMenu.txtSignatureSettings": "Налады подпісу", "SSE.Views.RightMenu.txtSlicerSettings": "Налады зводкі", "SSE.Views.RightMenu.txtSparklineSettings": "Налады спарклайна", "SSE.Views.RightMenu.txtTableSettings": "Налады табліцы", "SSE.Views.RightMenu.txtTextArtSettings": "Налады Text Art", "SSE.Views.ScaleDialog.textAuto": "Аўта", "SSE.Views.ScaleDialog.textError": "Уведзена няправільнае значэнне.", "SSE.Views.ScaleDialog.textFewPages": "Старонкі", "SSE.Views.ScaleDialog.textFitTo": "Запоўніць не больш чым", "SSE.Views.ScaleDialog.textHeight": "Вышыня", "SSE.Views.ScaleDialog.textManyPages": "старонкі", "SSE.Views.ScaleDialog.textOnePage": "Старонка", "SSE.Views.ScaleDialog.textScaleTo": "Вызначыць", "SSE.Views.ScaleDialog.textTitle": "Налады маштабу", "SSE.Views.ScaleDialog.textWidth": "Шырыня", "SSE.Views.SetValueDialog.txtMaxText": "Максімальнае значэнне для гэтага поля - {0}", "SSE.Views.SetValueDialog.txtMinText": "Мінімальнае значэнне для гэтага поля - {0}", "SSE.Views.ShapeSettings.strBackground": "Колер фону", "SSE.Views.ShapeSettings.strChange": "Змяніць фігуру", "SSE.Views.ShapeSettings.strColor": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "Заліўка", "SSE.Views.ShapeSettings.strForeground": "Колер пярэдняга плану", "SSE.Views.ShapeSettings.strPattern": "Узор", "SSE.Views.ShapeSettings.strShadow": "Паказваць цень", "SSE.Views.ShapeSettings.strSize": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strStroke": "Абвядзенне", "SSE.Views.ShapeSettings.strTransparency": "Непразрыстасць", "SSE.Views.ShapeSettings.strType": "Тып", "SSE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "SSE.Views.ShapeSettings.textAdvanced": "Дадатковыя налады", "SSE.Views.ShapeSettings.textAngle": "Вугал", "SSE.Views.ShapeSettings.textBorderSizeErr": "Уведзена хібнае значэнне.<br>Кал<PERSON> ласка, ўвядзіце значэнне ад 0 да 1584 пунктаў.", "SSE.Views.ShapeSettings.textColor": "Заліўка колерам", "SSE.Views.ShapeSettings.textDirection": "Напрамак", "SSE.Views.ShapeSettings.textEditPoints": "Рэдагаваць кропкі", "SSE.Views.ShapeSettings.textEditShape": "Edit shape", "SSE.Views.ShapeSettings.textEmptyPattern": "Без узору", "SSE.Views.ShapeSettings.textEyedropper": "Eyedropper", "SSE.Views.ShapeSettings.textFlip": "Пераварочванне", "SSE.Views.ShapeSettings.textFromFile": "З файла", "SSE.Views.ShapeSettings.textFromStorage": "Са сховішча", "SSE.Views.ShapeSettings.textFromUrl": "Па URL", "SSE.Views.ShapeSettings.textGradient": "Град<PERSON><PERSON><PERSON>т", "SSE.Views.ShapeSettings.textGradientFill": "Градыентная заліўка", "SSE.Views.ShapeSettings.textHint270": "Павярнуць улева на 90°", "SSE.Views.ShapeSettings.textHint90": "Павярнуць управа на 90°", "SSE.Views.ShapeSettings.textHintFlipH": "Адлюстраваць па гарызанталі", "SSE.Views.ShapeSettings.textHintFlipV": "Адлюстраваць па вертыкалі", "SSE.Views.ShapeSettings.textImageTexture": "Малюнак альбо тэкстура", "SSE.Views.ShapeSettings.textLinear": "Лінейны", "SSE.Views.ShapeSettings.textMoreColors": "More colors", "SSE.Views.ShapeSettings.textNoFill": "Без заліўкі", "SSE.Views.ShapeSettings.textNoShadow": "No Shadow", "SSE.Views.ShapeSettings.textOriginalSize": "Зыходны памер", "SSE.Views.ShapeSettings.textPatternFill": "Узор", "SSE.Views.ShapeSettings.textPosition": "Пасада", "SSE.Views.ShapeSettings.textRadial": "Радыяльны", "SSE.Views.ShapeSettings.textRecentlyUsed": "Апошнія выкарыстаныя", "SSE.Views.ShapeSettings.textRotate90": "Павярнуць на 90°", "SSE.Views.ShapeSettings.textRotation": "Паварочванне", "SSE.Views.ShapeSettings.textSelectImage": "Абраць выяву", "SSE.Views.ShapeSettings.textSelectTexture": "Абраць", "SSE.Views.ShapeSettings.textShadow": "Shadow", "SSE.Views.ShapeSettings.textStretch": "Расцягванне", "SSE.Views.ShapeSettings.textStyle": "Стыль", "SSE.Views.ShapeSettings.textTexture": "З тэкстуры", "SSE.Views.ShapeSettings.textTile": "Плітка", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Дадаць кропку градыента", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Выдаліць кропку градыента", "SSE.Views.ShapeSettings.txtBrownPaper": "Карычневая папера", "SSE.Views.ShapeSettings.txtCanvas": "Палатно", "SSE.Views.ShapeSettings.txtCarton": "Карт<PERSON>н", "SSE.Views.ShapeSettings.txtDarkFabric": "Цёмная тканіна", "SSE.Views.ShapeSettings.txtGrain": "Пясок", "SSE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "Шэрая папера", "SSE.Views.ShapeSettings.txtKnit": "Вязанне", "SSE.Views.ShapeSettings.txtLeather": "Скура", "SSE.Views.ShapeSettings.txtNoBorders": "Без абвядзення", "SSE.Views.ShapeSettings.txtPapyrus": "Па<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "Дрэва", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Слупкі", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Водступ вакол тэксту", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Не перамяшчаць і не змяняць памеры разам з ячэйкамі", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Альтэрнатыўны тэкст", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Апісанне", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Альтэрнатыўная тэкставая падача інфармацыі пра візуальны аб’ект, якая будзе агучвацца для слабавідушчых людзей ці людзей з кагнітыўнымі парушэннямі, каб дапамагчы ім зразумець інфармацыю, якую змяшчае выява, аўтафігура, дыяграма ці табліца.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Загаловак", "SSE.Views.ShapeSettingsAdvanced.textAngle": "Вугал", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Стрэлкі", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Аўтазапаўненне", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Першапачатковы памер", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Першапачатковы стыль", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Нахілены", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Знізу", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Тып канчатка", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Колькасць слупкоў", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Канцавы памер", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Канцавы стыль", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Плоскі", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Перавернута", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Вышыня", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Па гарызанталі", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Тып аб’яднання", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Захаваць прапорцыі", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Злева", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Стыль лініі", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Непасрэдны", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Перам<PERSON><PERSON><PERSON><PERSON><PERSON>ь, але не змяняць памеры разам з ячэйкамі.", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Дазволіць перапаўненне фігуры тэкстам", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Падладжваць памер фігуры пад тэкст", "SSE.Views.ShapeSettingsAdvanced.textRight": "Справа", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Паварочванне", "SSE.Views.ShapeSettingsAdvanced.textRound": "Скруглены", "SSE.Views.ShapeSettingsAdvanced.textSize": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Далучэнне да ячэйкі", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Прамежак паміж слупкамі", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Квадра<PERSON>ны", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Тэкставае поле", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Фігура - дадатковыя налады", "SSE.Views.ShapeSettingsAdvanced.textTop": "Уверсе", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Перамяшчаць і змяняць памеры разам з ячэйкамі", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Па вертыкалі", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Лініі і стрэлкі", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Шырыня", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Увага", "SSE.Views.SignatureSettings.strDelete": "Выдаліць подпіс", "SSE.Views.SignatureSettings.strDetails": "Падрабязнасці подпісу", "SSE.Views.SignatureSettings.strInvalid": "Хібныя подпісы", "SSE.Views.SignatureSettings.strRequested": "Запытаныя подпісы", "SSE.Views.SignatureSettings.strSetup": "Наладжванне подпісу", "SSE.Views.SignatureSettings.strSign": "Падпісаць", "SSE.Views.SignatureSettings.strSignature": "Под<PERSON><PERSON>с", "SSE.Views.SignatureSettings.strSigner": "Пад<PERSON><PERSON><PERSON><PERSON><PERSON>т", "SSE.Views.SignatureSettings.strValid": "Дзейныя подпісы", "SSE.Views.SignatureSettings.txtContinueEditing": "Рэдагаваць усё роўна", "SSE.Views.SignatureSettings.txtEditWarning": "Пры рэдагаванні табліцы выдаляцца подпісы.<br>Працягнуць?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Хочаце выдаліць гэты подпіс?<br>Гэтае дзеянне нельга скасаваць.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Гэтую табліцу неабходна падпісаць.", "SSE.Views.SignatureSettings.txtSigned": "У табліцу былі дададзеныя дзейныя подпісы. Табліца абароненая ад рэдагавання.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Некаторыя з лічбавых подпісаў электроннай табліцы хібныя альбо іх немагчыма праверыць. Табліца абароненая ад рэдагавання.", "SSE.Views.SlicerAddDialog.textColumns": "Слупкі", "SSE.Views.SlicerAddDialog.txtTitle": "Устаўлянне зводак", "SSE.Views.SlicerSettings.strHideNoData": "Схаваць элементы без даных", "SSE.Views.SlicerSettings.strIndNoData": "Адзначаць пустыя элементы", "SSE.Views.SlicerSettings.strShowDel": "Паказваць элементы, якія былі выдаленыя з крыніцы даных", "SSE.Views.SlicerSettings.strShowNoData": "Паказваць пустыя элементы апошнімі", "SSE.Views.SlicerSettings.strSorting": "Сартаванне і фільтрацыя", "SSE.Views.SlicerSettings.textAdvanced": "Дадатковыя налады", "SSE.Views.SlicerSettings.textAsc": "Па ўзрастанні", "SSE.Views.SlicerSettings.textAZ": "Ад А да Я", "SSE.Views.SlicerSettings.textButtons": "Кнопкі", "SSE.Views.SlicerSettings.textColumns": "Слупкі", "SSE.Views.SlicerSettings.textDesc": "Па памяншэнні", "SSE.Views.SlicerSettings.textHeight": "Вышыня", "SSE.Views.SlicerSettings.textHor": "Гарызантальна", "SSE.Views.SlicerSettings.textKeepRatio": "Захаваць прапорцыі", "SSE.Views.SlicerSettings.textLargeSmall": "ад большага да меншага", "SSE.Views.SlicerSettings.textLock": "Адключыць змену памеру альбо перамяшчэнне", "SSE.Views.SlicerSettings.textNewOld": "ад новых да старых", "SSE.Views.SlicerSettings.textOldNew": "ад старых да новых", "SSE.Views.SlicerSettings.textPosition": "Пасада", "SSE.Views.SlicerSettings.textSize": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textSmallLarge": "ад малога да вялікага", "SSE.Views.SlicerSettings.textStyle": "Стыль", "SSE.Views.SlicerSettings.textVert": "Вертыкальна", "SSE.Views.SlicerSettings.textWidth": "Шырыня", "SSE.Views.SlicerSettings.textZA": "Ад Я да А", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Кнопкі", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Слупкі", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Вышыня", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Схаваць элементы без даных", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Адзначаць пустыя элементы", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Спасылкі", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Паказваць элементы, якія былі выдаленыя з крыніцы даных", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Паказваць загаловак", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Паказваць пустыя элементы апошнімі", "SSE.Views.SlicerSettingsAdvanced.strSize": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Сартаванне і фільтрацыя", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Стыль", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Стыль і памер", "SSE.Views.SlicerSettingsAdvanced.strWidth": "Шырыня", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Не перамяшчаць і не змяняць памеры разам з ячэйкамі", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Альтэрнатыўны тэкст", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Апісанне", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Альтэрнатыўная тэкставая падача інфармацыі пра візуальны аб’ект, якая будзе агучвацца для слабавідушчых людзей ці людзей з кагнітыўнымі парушэннямі, каб дапамагчы ім зразумець інфармацыю, якую змяшчае выява, фігура, дыяграма ці табліца.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Назва", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Па ўзрастанні", "SSE.Views.SlicerSettingsAdvanced.textAZ": "Ад А да Я", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Па памяншэнні", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Назва для ўжывання ў формулах", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Загаловак", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Захаваць прапорцыі", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "ад большага да меншага", "SSE.Views.SlicerSettingsAdvanced.textName": "Назва", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "ад новых да старых", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "ад старых да новых", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Перам<PERSON><PERSON><PERSON><PERSON><PERSON>ь, але не змяняць памеры разам з ячэйкамі.", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "ад малога да вялікага", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Далучэнне да ячэйкі", "SSE.Views.SlicerSettingsAdvanced.textSort": "Сартаванне", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Назва крыніцы", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Зводка - дадатковыя параметры", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Перамяшчаць і змяняць памеры разам з ячэйкамі", "SSE.Views.SlicerSettingsAdvanced.textZA": "Ад Я да А", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.SortDialog.errorEmpty": "Для кожнай умовы сартавання мусіць быць вызначаны слупок і радок.", "SSE.Views.SortDialog.errorMoreOneCol": "Абрана некалькі слупкоў.", "SSE.Views.SortDialog.errorMoreOneRow": "Абрана некалькі радкоў.", "SSE.Views.SortDialog.errorNotOriginalCol": "Абраны слупок не ўваходзіць у папярэдне абраны дыяпазон.", "SSE.Views.SortDialog.errorNotOriginalRow": "Абраны радок не належыць папярэдне абранаму дыяпазону.", "SSE.Views.SortDialog.errorSameColumnColor": "Сартаванне радка альбо слупка %1 па аднаму і таму ж колеру выконваецца больш за адзін раз.<br>Выдаліце паўторныя ўмовы сартавання і паўтарыце спробу.", "SSE.Views.SortDialog.errorSameColumnValue": "Сартаванне радка альбо слупка %1 па значэнням выконваецца больш за адзін раз.<br>Выдаліце паўторныя ўмовы сартавання і паўтарыце спробу.", "SSE.Views.SortDialog.textAsc": "Па ўзрастанні", "SSE.Views.SortDialog.textAuto": "Аўтаматычна", "SSE.Views.SortDialog.textAZ": "Ад А да Я", "SSE.Views.SortDialog.textBelow": "Унізе", "SSE.Views.SortDialog.textBtnCopy": "Капіяваць", "SSE.Views.SortDialog.textBtnDelete": "Выдаліць", "SSE.Views.SortDialog.textBtnNew": "Новы", "SSE.Views.SortDialog.textCellColor": "Ко<PERSON>ер яч<PERSON><PERSON>кі", "SSE.Views.SortDialog.textColumn": "Слупок", "SSE.Views.SortDialog.textDesc": "Па памяншэнні", "SSE.Views.SortDialog.textDown": "Перамясціць узровень ніжэй", "SSE.Views.SortDialog.textFontColor": "Колер шрыфту", "SSE.Views.SortDialog.textLeft": "Злева", "SSE.Views.SortDialog.textLevels": "Узроўні", "SSE.Views.SortDialog.textMoreCols": "(Яшчэ слупкі…)", "SSE.Views.SortDialog.textMoreRows": "(Яшчэ радкі…)", "SSE.Views.SortDialog.textNone": "Няма", "SSE.Views.SortDialog.textOptions": "Параметры", "SSE.Views.SortDialog.textOrder": "Пар<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textRight": "Справа", "SSE.Views.SortDialog.textRow": "Радок", "SSE.Views.SortDialog.textSort": "Сартаванне", "SSE.Views.SortDialog.textSortBy": "Парадкаваць па", "SSE.Views.SortDialog.textThenBy": "Пасля па", "SSE.Views.SortDialog.textTop": "Уверсе", "SSE.Views.SortDialog.textUp": "Перамясціць узровень вышэй", "SSE.Views.SortDialog.textValues": "Значэнні", "SSE.Views.SortDialog.textZA": "Ад Я да А", "SSE.Views.SortDialog.txtInvalidRange": "Хібны дыяпазон ячэек.", "SSE.Views.SortDialog.txtTitle": "Сартаванне", "SSE.Views.SortFilterDialog.textAsc": "Па ўзрастанні (ад А да Я)", "SSE.Views.SortFilterDialog.textDesc": "Па ўбыванні (ад Я да А)", "SSE.Views.SortFilterDialog.textNoSort": "No sort", "SSE.Views.SortFilterDialog.txtTitle": "Сартаванне", "SSE.Views.SortFilterDialog.txtTitleValue": "Сартаванне па значэнні", "SSE.Views.SortOptionsDialog.textCase": "Улічваць рэгістр", "SSE.Views.SortOptionsDialog.textHeaders": "Мае даныя змяшчаюць загалоўкі", "SSE.Views.SortOptionsDialog.textLeftRight": "Сартаванне злева направа", "SSE.Views.SortOptionsDialog.textOrientation": "Арыентацыя", "SSE.Views.SortOptionsDialog.textTitle": "Параметры сартавання", "SSE.Views.SortOptionsDialog.textTopBottom": "Сартаванне зверху ўніз", "SSE.Views.SpecialPasteDialog.textAdd": "Дада<PERSON>ь", "SSE.Views.SpecialPasteDialog.textAll": "Усе", "SSE.Views.SpecialPasteDialog.textBlanks": "Мінаць пустыя ячэйкі", "SSE.Views.SpecialPasteDialog.textColWidth": "Шырыня слупкоў", "SSE.Views.SpecialPasteDialog.textComments": "Каментары", "SSE.Views.SpecialPasteDialog.textDiv": "Дзяленне", "SSE.Views.SpecialPasteDialog.textFFormat": "Формулы і фарматаванне", "SSE.Views.SpecialPasteDialog.textFNFormat": "Формулы і фарматы лікаў", "SSE.Views.SpecialPasteDialog.textFormats": "Фарматы", "SSE.Views.SpecialPasteDialog.textFormulas": "Формулы", "SSE.Views.SpecialPasteDialog.textFWidth": "Формулы і шырыня слупкоў", "SSE.Views.SpecialPasteDialog.textMult": "Множанне", "SSE.Views.SpecialPasteDialog.textNone": "Няма", "SSE.Views.SpecialPasteDialog.textOperation": "Аперацыя", "SSE.Views.SpecialPasteDialog.textPaste": "Уставіць", "SSE.Views.SpecialPasteDialog.textSub": "Адніманне", "SSE.Views.SpecialPasteDialog.textTitle": "Адмысловая ўстаўка", "SSE.Views.SpecialPasteDialog.textTranspose": "Пераправіць", "SSE.Views.SpecialPasteDialog.textValues": "Значэнні", "SSE.Views.SpecialPasteDialog.textVFormat": "Значэнні і фарматаванне", "SSE.Views.SpecialPasteDialog.textVNFormat": "Значэнні і фарматы лікаў", "SSE.Views.SpecialPasteDialog.textWBorders": "Без рамкі", "SSE.Views.Spellcheck.noSuggestions": "Варыянтаў не знойдзена", "SSE.Views.Spellcheck.textChange": "Змяніць", "SSE.Views.Spellcheck.textChangeAll": "Змяніць усе", "SSE.Views.Spellcheck.textIgnore": "Мінуць", "SSE.Views.Spellcheck.textIgnoreAll": "Мінуць усе", "SSE.Views.Spellcheck.txtAddToDictionary": "Дадаць у слоўнік", "SSE.Views.Spellcheck.txtClosePanel": "Закрыць праверку правапісу", "SSE.Views.Spellcheck.txtComplete": "Правапіс правераны", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Мова слоўніка", "SSE.Views.Spellcheck.txtNextTip": "Перайсці да наступнага слова", "SSE.Views.Spellcheck.txtSpelling": "Права<PERSON><PERSON>с", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Перамясціць у канец)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "Create a copy", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(Create new spreadsheet)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Перамясціць перад аркушам", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Spreadsheet", "SSE.Views.Statusbar.filteredRecordsText": "Адфільтра<PERSON><PERSON><PERSON> {0} з {1} запісаў", "SSE.Views.Statusbar.filteredText": "Рэжым фільтрацыі", "SSE.Views.Statusbar.itemAverage": "Сярэдняе", "SSE.Views.Statusbar.itemCount": "Колькасць", "SSE.Views.Statusbar.itemDelete": "Выдаліць", "SSE.Views.Statusbar.itemHidden": "Схавана", "SSE.Views.Statusbar.itemHide": "Схаваць", "SSE.Views.Statusbar.itemInsert": "Уставіць", "SSE.Views.Statusbar.itemMaximum": "Максімум", "SSE.Views.Statusbar.itemMinimum": "Мін<PERSON><PERSON>ум", "SSE.Views.Statusbar.itemMoveOrCopy": "Move or copy", "SSE.Views.Statusbar.itemProtect": "Аб<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ь", "SSE.Views.Statusbar.itemRename": "Змяніць назву", "SSE.Views.Statusbar.itemStatus": "Статус захавання", "SSE.Views.Statusbar.itemSum": "Сума", "SSE.Views.Statusbar.itemTabColor": "Колер укладкі", "SSE.Views.Statusbar.itemUnProtect": "Зняць абарону", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Аркуш з такой назвай ужо існуе", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Назва аркуша не можа змяшчаць наступныя сімвалы: \\/*?[]: або ' у пачатку або канцы", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Назва аркуша", "SSE.Views.Statusbar.selectAllSheets": "Абраць усе аркушы", "SSE.Views.Statusbar.sheetIndexText": "Аркуш {0} з {1}", "SSE.Views.Statusbar.textAverage": "Сярэдняе", "SSE.Views.Statusbar.textCount": "Колькасць", "SSE.Views.Statusbar.textMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.Statusbar.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textNewColor": "Яшчэ колеры", "SSE.Views.Statusbar.textNoColor": "Без колеру", "SSE.Views.Statusbar.textSum": "Сума", "SSE.Views.Statusbar.tipAddTab": "Да<PERSON><PERSON><PERSON>ь аркуш", "SSE.Views.Statusbar.tipFirst": "Прагарнуць да першага аркуша", "SSE.Views.Statusbar.tipLast": "Прагарнуць да апошняга аркуша", "SSE.Views.Statusbar.tipListOfSheets": "Спіс аркушаў", "SSE.Views.Statusbar.tipNext": "Прагарнуць спіс аркушаў управа", "SSE.Views.Statusbar.tipPrev": "Прагарну<PERSON>ь спіс аркушаў улева", "SSE.Views.Statusbar.tipZoomFactor": "Ма<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomIn": "Павелічэнне", "SSE.Views.Statusbar.tipZoomOut": "Памяншэнне", "SSE.Views.Statusbar.ungroupSheets": "Разгрупаваць аркушы", "SSE.Views.Statusbar.zoomText": "Ма<PERSON><PERSON><PERSON>б {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Гэтую аперацыю немагчыма выканаць для абранага дыяпазону ячэек.<br>Абярыце іншы дыяпазон даных, адрозны ад існага, і паўтарыце зноў.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Не ўдалося выканаць аперацыю для абранага дыяпазону ячэек.<br>Абярыце дыяпазон так, каб першы радок знаходзіўся на тым жа радку,<br>а выніковая табліца перакрывала бягучую.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Не ўдалося выканаць аперацыю для абранага дыяпазону ячэек.<br> Абярыце дыяпазон, які не змяшчае іншых табліц.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "У табліцах забаронена выкарыстанне формул масіву з некалькімі ячэйкамі.", "SSE.Views.TableOptionsDialog.txtEmpty": "Гэтае поле неабходна запоўніць", "SSE.Views.TableOptionsDialog.txtFormat": "Стварыць табліцу", "SSE.Views.TableOptionsDialog.txtInvalidRange": "ПАМЫЛКА! хібны дыяпазон ячэек", "SSE.Views.TableOptionsDialog.txtNote": "Загалоўкі мусяць заставацца ў тым самым радку, а выніковы дыяпазон табліцы часткова супадаць з зыходным дыяпазонам.", "SSE.Views.TableOptionsDialog.txtTitle": "Загаловак", "SSE.Views.TableSettings.deleteColumnText": "Выдаліць слупок", "SSE.Views.TableSettings.deleteRowText": "Выдаліць радок", "SSE.Views.TableSettings.deleteTableText": "Выдаліць табліцу", "SSE.Views.TableSettings.insertColumnLeftText": "Уставіць слупок злева", "SSE.Views.TableSettings.insertColumnRightText": "Уставіць слупок справа", "SSE.Views.TableSettings.insertRowAboveText": "Уставіць радок вышэй", "SSE.Views.TableSettings.insertRowBelowText": "Уставіць радок ніжэй", "SSE.Views.TableSettings.notcriticalErrorTitle": "Увага", "SSE.Views.TableSettings.selectColumnText": "Абраць увесь слупок", "SSE.Views.TableSettings.selectDataText": "Абраць даныя слупкоў", "SSE.Views.TableSettings.selectRowText": "Абраць радок", "SSE.Views.TableSettings.selectTableText": "Абра<PERSON>ь табліцу", "SSE.Views.TableSettings.textActions": "Дзеянні з табліцамі", "SSE.Views.TableSettings.textAdvanced": "Дадатковыя налады", "SSE.Views.TableSettings.textBanded": "Чаргаваць", "SSE.Views.TableSettings.textColumns": "Слупкі", "SSE.Views.TableSettings.textConvertRange": "Ператварыць у дыяпазон", "SSE.Views.TableSettings.textEdit": "Радкі і слупкі", "SSE.Views.TableSettings.textEmptyTemplate": "Без шаблонаў", "SSE.Views.TableSettings.textExistName": "ПАМЫЛКА! Дыяпазон з такой назвай ужо існуе", "SSE.Views.TableSettings.textFilter": "Кнопка фільтра", "SSE.Views.TableSettings.textFirst": "Пер<PERSON>ы", "SSE.Views.TableSettings.textHeader": "Загаловак", "SSE.Views.TableSettings.textInvalidName": "ПАМЫЛКА! Хібная назва табліцы", "SSE.Views.TableSettings.textIsLocked": "Гэты элемент рэдагуецца іншым карыстальнікам.", "SSE.Views.TableSettings.textLast": "Апошні", "SSE.Views.TableSettings.textLongOperation": "Працяглая аперацыя", "SSE.Views.TableSettings.textPivot": "Уставіць зводную табліцу", "SSE.Views.TableSettings.textRemDuplicates": "Выдаліць паўторы", "SSE.Views.TableSettings.textReservedName": "Формулы ў ячэйках ужо змяшчаюць назву, якую вы спрабуеце выкарыстоўваць. Выкарыстайце іншую назву.", "SSE.Views.TableSettings.textResize": "Змяніць памер табліцы", "SSE.Views.TableSettings.textRows": "Радкі", "SSE.Views.TableSettings.textSelectData": "Абраць даныя", "SSE.Views.TableSettings.textSlicer": "Уставіць зводку", "SSE.Views.TableSettings.textTableName": "Назва табліцы", "SSE.Views.TableSettings.textTemplate": "Абраць шаблон", "SSE.Views.TableSettings.textTotal": "<PERSON>г<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.txtGroupTable_Custom": "Адвольна", "SSE.Views.TableSettings.txtGroupTable_Dark": "Цёмныя", "SSE.Views.TableSettings.txtGroupTable_Light": "Светлыя", "SSE.Views.TableSettings.txtGroupTable_Medium": "Сярэднія", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Цёмны стыль табліцы", "SSE.Views.TableSettings.txtTable_TableStyleLight": "Светлы стыль табліцы", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "Сярэдні стыль табліцы", "SSE.Views.TableSettings.warnLongOperation": "Для завяршэння аперацыі, якую вы хочаце выканаць, можа спатрэбіцца шмат часу.<br>Сапраўды хочаце працягнуць?", "SSE.Views.TableSettingsAdvanced.textAlt": "Альтэрнатыўны тэкст", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Апісанне", "SSE.Views.TableSettingsAdvanced.textAltTip": "Альтэрнатыўная тэкставая падача інфармацыі пра візуальны аб’ект, якая будзе агучвацца для слабавідушчых людзей ці людзей з кагнітыўнымі парушэннямі, каб дапамагчы ім зразумець інфармацыю, якую змяшчае выява, фігура, дыяграма ці табліца.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Загаловак", "SSE.Views.TableSettingsAdvanced.textTitle": "Табліца - дадатковыя налады", "SSE.Views.TextArtSettings.strBackground": "Колер фону", "SSE.Views.TextArtSettings.strColor": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "Заліўка", "SSE.Views.TextArtSettings.strForeground": "Колер пярэдняга плану", "SSE.Views.TextArtSettings.strPattern": "Узор", "SSE.Views.TextArtSettings.strSize": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strStroke": "Абвядзенне", "SSE.Views.TextArtSettings.strTransparency": "Непразрыстасць", "SSE.Views.TextArtSettings.strType": "Тып", "SSE.Views.TextArtSettings.textAngle": "Вугал", "SSE.Views.TextArtSettings.textBorderSizeErr": "Уведзена хібнае значэнне.<br>Кал<PERSON> ласка, ўвядзіце значэнне ад 0 да 1584 пунктаў.", "SSE.Views.TextArtSettings.textColor": "Заліўка колерам", "SSE.Views.TextArtSettings.textDirection": "Напрамак", "SSE.Views.TextArtSettings.textEmptyPattern": "Без узору", "SSE.Views.TextArtSettings.textFromFile": "З файла", "SSE.Views.TextArtSettings.textFromUrl": "Па URL", "SSE.Views.TextArtSettings.textGradient": "Град<PERSON><PERSON><PERSON>т", "SSE.Views.TextArtSettings.textGradientFill": "Градыентная заліўка", "SSE.Views.TextArtSettings.textImageTexture": "Малюнак альбо тэкстура", "SSE.Views.TextArtSettings.textLinear": "Лінейны", "SSE.Views.TextArtSettings.textNoFill": "Без заліўкі", "SSE.Views.TextArtSettings.textPatternFill": "Узор", "SSE.Views.TextArtSettings.textPosition": "Пасада", "SSE.Views.TextArtSettings.textRadial": "Радыяльны", "SSE.Views.TextArtSettings.textSelectTexture": "Абраць", "SSE.Views.TextArtSettings.textStretch": "Расцягванне", "SSE.Views.TextArtSettings.textStyle": "Стыль", "SSE.Views.TextArtSettings.textTemplate": "Шабл<PERSON>н", "SSE.Views.TextArtSettings.textTexture": "З тэкстуры", "SSE.Views.TextArtSettings.textTile": "Плітка", "SSE.Views.TextArtSettings.textTransform": "Трансфармацыя", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Дадаць кропку градыента", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Выдаліць кропку градыента", "SSE.Views.TextArtSettings.txtBrownPaper": "Карычневая папера", "SSE.Views.TextArtSettings.txtCanvas": "Палатно", "SSE.Views.TextArtSettings.txtCarton": "Карт<PERSON>н", "SSE.Views.TextArtSettings.txtDarkFabric": "Цёмная тканіна", "SSE.Views.TextArtSettings.txtGrain": "Пясок", "SSE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "Шэрая папера", "SSE.Views.TextArtSettings.txtKnit": "Вязанне", "SSE.Views.TextArtSettings.txtLeather": "Скура", "SSE.Views.TextArtSettings.txtNoBorders": "Без абвядзення", "SSE.Views.TextArtSettings.txtPapyrus": "Па<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtWood": "Дрэва", "SSE.Views.Toolbar.capBtnAddComment": "Да<PERSON><PERSON><PERSON>ь каментар", "SSE.Views.Toolbar.capBtnColorSchemas": "Каляровая схема", "SSE.Views.Toolbar.capBtnComment": "Ка<PERSON><PERSON>н<PERSON>ар", "SSE.Views.Toolbar.capBtnInsHeader": "Калонтытулы", "SSE.Views.Toolbar.capBtnInsSlicer": "Зводка", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Сімвал", "SSE.Views.Toolbar.capBtnMargins": "<PERSON>а<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageBreak": "Разрывы", "SSE.Views.Toolbar.capBtnPageOrient": "Арыентацыя", "SSE.Views.Toolbar.capBtnPageSize": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintArea": "Вобласць друкавання", "SSE.Views.Toolbar.capBtnPrintTitles": "Друкаваць загалоўкі", "SSE.Views.Toolbar.capBtnScale": "Умясціць", "SSE.Views.Toolbar.capImgAlign": "Выраўноўванне", "SSE.Views.Toolbar.capImgBackward": "Адправіць назад", "SSE.Views.Toolbar.capImgForward": "Перамясціць уперад", "SSE.Views.Toolbar.capImgGroup": "Групаванне", "SSE.Views.Toolbar.capInsertChart": "Дыяграма", "SSE.Views.Toolbar.capInsertChartRecommend": "Recommended Chart", "SSE.Views.Toolbar.capInsertEquation": "Раўнанне", "SSE.Views.Toolbar.capInsertHyperlink": "Гіперспасылка", "SSE.Views.Toolbar.capInsertImage": "Выява", "SSE.Views.Toolbar.capInsertShape": "Фігура", "SSE.Views.Toolbar.capInsertSpark": "Спарклайн", "SSE.Views.Toolbar.capInsertTable": "Табліца", "SSE.Views.Toolbar.capInsertText": "Над<PERSON><PERSON>с", "SSE.Views.Toolbar.capInsertTextart": "Text Art", "SSE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniCapitalizeWords": "Кожнае слова з вялікай літары", "SSE.Views.Toolbar.mniImageFromFile": "Выява з файла", "SSE.Views.Toolbar.mniImageFromStorage": "Выява са сховішча", "SSE.Views.Toolbar.mniImageFromUrl": "Выява па URL", "SSE.Views.Toolbar.mniLowerCase": "ніжні рэгістр", "SSE.Views.Toolbar.mniSentenceCase": "Як у сказах", "SSE.Views.Toolbar.mniToggleCase": "зМЯНІЦЬ рЭГІСТР", "SSE.Views.Toolbar.mniUpperCase": "ВЕРХНІ РЭГІСТР", "SSE.Views.Toolbar.textAddPrintArea": "Дадаць у вобласць друкавання", "SSE.Views.Toolbar.textAlignBottom": "Выраўнаваць па ніжняму краю", "SSE.Views.Toolbar.textAlignCenter": "Выраўнаваць па цэнтры", "SSE.Views.Toolbar.textAlignJust": "Па шырыні", "SSE.Views.Toolbar.textAlignLeft": "Выраўнаваць па леваму краю", "SSE.Views.Toolbar.textAlignMiddle": "Выраўнаваць па сярэдзіне", "SSE.Views.Toolbar.textAlignRight": "Выраўнаваць па праваму краю", "SSE.Views.Toolbar.textAlignTop": "Выраўнаваць па верхняму краю", "SSE.Views.Toolbar.textAllBorders": "Усе межы", "SSE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "SSE.Views.Toolbar.textAuto": "Аўта", "SSE.Views.Toolbar.textAutoColor": "Аўтаматычна", "SSE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "SSE.Views.Toolbar.textBlackHeart": "Black Heart Suit", "SSE.Views.Toolbar.textBold": "Тоўсты", "SSE.Views.Toolbar.textBordersColor": "Колер межаў", "SSE.Views.Toolbar.textBordersStyle": "Стыль межаў", "SSE.Views.Toolbar.textBottom": "Ніжняе: ", "SSE.Views.Toolbar.textBottomBorders": "Ніжнія межы", "SSE.Views.Toolbar.textBullet": "Адзнака", "SSE.Views.Toolbar.textCellAlign": "Format cell alignment", "SSE.Views.Toolbar.textCenterBorders": "Унутраныя вертыкальныя межы", "SSE.Views.Toolbar.textClearPrintArea": "Ачысціць вобласць друкавання", "SSE.Views.Toolbar.textClearRule": "Выдаліць правілы", "SSE.Views.Toolbar.textClockwise": "Тэкст па гадзіннікавай стрэлцы", "SSE.Views.Toolbar.textColorScales": "Каляровыя шкалы", "SSE.Views.Toolbar.textCopyright": "Знак аўтарскіх правоў", "SSE.Views.Toolbar.textCounterCw": "Тэкст супраць гадзіннікавай стрэлкі", "SSE.Views.Toolbar.textCustom": "Адвольна", "SSE.Views.Toolbar.textDataBars": "Гістагра<PERSON>ы", "SSE.Views.Toolbar.textDegree": "Degree Sign", "SSE.Views.Toolbar.textDelLeft": "Ячэйкі са зрухам улева", "SSE.Views.Toolbar.textDelPageBreak": "Remove page break", "SSE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "SSE.Views.Toolbar.textDelUp": "Ячэйкі са зрухам уверх", "SSE.Views.Toolbar.textDiagDownBorder": "Дыяганальная мяжа зверху ўніз", "SSE.Views.Toolbar.textDiagUpBorder": "Дыяганальная мяжа знізу ўверх", "SSE.Views.Toolbar.textDivision": "Знак дзялення", "SSE.Views.Toolbar.textDollar": "Dollar Sign", "SSE.Views.Toolbar.textDone": "Завершана", "SSE.Views.Toolbar.textDown": "Down", "SSE.Views.Toolbar.textEditVA": "Рэдагаваць бачную вобласць", "SSE.Views.Toolbar.textEntireCol": "Слупок", "SSE.Views.Toolbar.textEntireRow": "Радок", "SSE.Views.Toolbar.textEuro": "Euro Sign", "SSE.Views.Toolbar.textFewPages": "Старонак", "SSE.Views.Toolbar.textFillLeft": "Left", "SSE.Views.Toolbar.textFillRight": "Right", "SSE.Views.Toolbar.textFormatCellFill": "Format cell fill", "SSE.Views.Toolbar.textGreaterEqual": "Больш альбо роўна", "SSE.Views.Toolbar.textHeight": "Вышыня", "SSE.Views.Toolbar.textHideVA": "Схаваць бачную вобласць", "SSE.Views.Toolbar.textHorizontal": "Гарызантальны тэкст", "SSE.Views.Toolbar.textInfinity": "Бясконцасць", "SSE.Views.Toolbar.textInsDown": "Ячэйкі са зрухам уніз", "SSE.Views.Toolbar.textInsideBorders": "Унутраныя межы", "SSE.Views.Toolbar.textInsPageBreak": "Уставіць разрыў старонкі", "SSE.Views.Toolbar.textInsRight": "Ячэйкі са зрухам управа", "SSE.Views.Toolbar.textItalic": "Курсіў", "SSE.Views.Toolbar.textItems": "элементаў", "SSE.Views.Toolbar.textLandscape": "Альбомная", "SSE.Views.Toolbar.textLeft": "Левае: ", "SSE.Views.Toolbar.textLeftBorders": "Левыя межы", "SSE.Views.Toolbar.textLessEqual": "Мен<PERSON> альбо роўна", "SSE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "SSE.Views.Toolbar.textManageRule": "Кіраванне правіламі", "SSE.Views.Toolbar.textManyPages": "стар<PERSON><PERSON>к", "SSE.Views.Toolbar.textMarginsLast": "Апошнія адвольныя", "SSE.Views.Toolbar.textMarginsNarrow": "Вузкія", "SSE.Views.Toolbar.textMarginsNormal": "Звычайныя", "SSE.Views.Toolbar.textMarginsWide": "Шырокія", "SSE.Views.Toolbar.textMiddleBorders": "Унутраныя гарызантальныя межы", "SSE.Views.Toolbar.textMoreBorders": "More borders", "SSE.Views.Toolbar.textMoreFormats": "Іншыя фарматы", "SSE.Views.Toolbar.textMorePages": "Іншыя старонкі", "SSE.Views.Toolbar.textMoreSymbols": "More symbols", "SSE.Views.Toolbar.textNewColor": "Яшчэ колеры", "SSE.Views.Toolbar.textNewRule": "Новае правіла", "SSE.Views.Toolbar.textNoBorders": "Без межаў", "SSE.Views.Toolbar.textNotEqualTo": "Не роўна", "SSE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "SSE.Views.Toolbar.textOnePage": "Старонка", "SSE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "SSE.Views.Toolbar.textOutBorders": "Вонкавыя межы", "SSE.Views.Toolbar.textPageMarginsCustom": "Адвольныя палі", "SSE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "SSE.Views.Toolbar.textPortrait": "Кніжная", "SSE.Views.Toolbar.textPrint": "Друкаванне", "SSE.Views.Toolbar.textPrintGridlines": "Друкаваць сетку", "SSE.Views.Toolbar.textPrintHeadings": "Друкаванне загалоўкаў", "SSE.Views.Toolbar.textPrintOptions": "Налады друку", "SSE.Views.Toolbar.textRegistered": "Зарэгістраваны таварны знак", "SSE.Views.Toolbar.textResetPageBreak": "Reset all page breaks", "SSE.Views.Toolbar.textRight": "Правае: ", "SSE.Views.Toolbar.textRightBorders": "Правыя межы", "SSE.Views.Toolbar.textRotateDown": "Павярнуць тэкст уніз", "SSE.Views.Toolbar.textRotateUp": "Павярнуць тэкст уверх", "SSE.Views.Toolbar.textRtlSheet": "Sheet Right-to-left", "SSE.Views.Toolbar.textScale": "Ма<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textScaleCustom": "Адвольны", "SSE.Views.Toolbar.textSection": "Знак раздзела", "SSE.Views.Toolbar.textSelection": "З бягучага абранага", "SSE.Views.Toolbar.textSeries": "Series", "SSE.Views.Toolbar.textSetPrintArea": "Вызначыць вобласць друкавання", "SSE.Views.Toolbar.textShapesCombine": "Combine", "SSE.Views.Toolbar.textShapesFragment": "Fragment", "SSE.Views.Toolbar.textShapesIntersect": "Intersect", "SSE.Views.Toolbar.textShapesSubstract": "Subtract", "SSE.Views.Toolbar.textShapesUnion": "Union", "SSE.Views.Toolbar.textShowVA": "Паказваць бачную вобласць", "SSE.Views.Toolbar.textSmile": "White Smiling Face", "SSE.Views.Toolbar.textSquareRoot": "Квадратны корань", "SSE.Views.Toolbar.textStrikeout": "Закрэслены", "SSE.Views.Toolbar.textSubscript": "Падрадковыя", "SSE.Views.Toolbar.textSubSuperscript": "Падрадковыя / Надрадковыя", "SSE.Views.Toolbar.textSuperscript": "Надрадковыя", "SSE.Views.Toolbar.textTabCollaboration": "Сумесная праца", "SSE.Views.Toolbar.textTabData": "Даныя", "SSE.Views.Toolbar.textTabDraw": "Draw", "SSE.Views.Toolbar.textTabFile": "<PERSON>а<PERSON><PERSON>", "SSE.Views.Toolbar.textTabFormula": "Формула", "SSE.Views.Toolbar.textTabHome": "Асноўныя функцыі", "SSE.Views.Toolbar.textTabInsert": "Устаўка", "SSE.Views.Toolbar.textTabLayout": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabProtect": "Абарона", "SSE.Views.Toolbar.textTabView": "Мініяцюра", "SSE.Views.Toolbar.textThisPivot": "З гэтай зводнай табліцы", "SSE.Views.Toolbar.textThisSheet": "З гэтага аркуша", "SSE.Views.Toolbar.textThisTable": "З гэтай табліцы", "SSE.Views.Toolbar.textTilde": "Тыльда", "SSE.Views.Toolbar.textTop": "Верх: ", "SSE.Views.Toolbar.textTopBorders": "Верхнія межы", "SSE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "SSE.Views.Toolbar.textUnderline": "Падкрэслены", "SSE.Views.Toolbar.textUp": "Up", "SSE.Views.Toolbar.textVertical": "Вертыкальны тэкст ", "SSE.Views.Toolbar.textWidth": "Шырыня", "SSE.Views.Toolbar.textYen": "Yen Sign", "SSE.Views.Toolbar.textZoom": "Ма<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignBottom": "Выраўнаваць па ніжняму краю", "SSE.Views.Toolbar.tipAlignCenter": "Выраўнаваць па цэнтры", "SSE.Views.Toolbar.tipAlignJust": "Па шырыні", "SSE.Views.Toolbar.tipAlignLeft": "Выраўнаваць па леваму краю", "SSE.Views.Toolbar.tipAlignMiddle": "Выраўнаваць па сярэдзіне", "SSE.Views.Toolbar.tipAlignRight": "Выраўнаваць па праваму краю", "SSE.Views.Toolbar.tipAlignTop": "Выраўнаваць па верхняму краю", "SSE.Views.Toolbar.tipAutofilter": "Парадкаванне і фільтрацыя", "SSE.Views.Toolbar.tipBack": "Назад", "SSE.Views.Toolbar.tipBorders": "Межы", "SSE.Views.Toolbar.tipCellStyle": "Стыль ячэйкі", "SSE.Views.Toolbar.tipChangeCase": "Змяніць рэгістр", "SSE.Views.Toolbar.tipChangeChart": "Змяніць тып дыяграмы", "SSE.Views.Toolbar.tipClearStyle": "Ачысціць", "SSE.Views.Toolbar.tipColorSchemas": "Змяніць каляровую схему", "SSE.Views.Toolbar.tipCondFormat": "Умоўнае фарматаванне", "SSE.Views.Toolbar.tipCopy": "Капіяваць", "SSE.Views.Toolbar.tipCopyStyle": "Скапіяваць стыль", "SSE.Views.Toolbar.tipCut": "Выразаць", "SSE.Views.Toolbar.tipDecDecimal": "Паменшыць разрад", "SSE.Views.Toolbar.tipDecFont": "Паменшыць памер шрыфту", "SSE.Views.Toolbar.tipDeleteOpt": "Выдаліць ячэйкі", "SSE.Views.Toolbar.tipDigStyleAccounting": "Фінансавы стыль", "SSE.Views.Toolbar.tipDigStyleComma": "Comma style", "SSE.Views.Toolbar.tipDigStyleCurrency": "Грашовы фармат", "SSE.Views.Toolbar.tipDigStylePercent": "Стыль адсоткаў", "SSE.Views.Toolbar.tipEditChart": "Рэдагаваць дыяграму", "SSE.Views.Toolbar.tipEditChartData": "Абраць даныя", "SSE.Views.Toolbar.tipEditChartType": "Змяніць тып дыяграмы", "SSE.Views.Toolbar.tipEditHeader": "Рэдагаваць калонтытулы", "SSE.Views.Toolbar.tipFontColor": "Колер шрыфту", "SSE.Views.Toolbar.tipFontName": "Шры<PERSON>т", "SSE.Views.Toolbar.tipFontSize": "Памер шрыфту", "SSE.Views.Toolbar.tipHAlighOle": "Гарызантальнае выраўноўванне", "SSE.Views.Toolbar.tipImgAlign": "Выраўнаваць аб’екты", "SSE.Views.Toolbar.tipImgGroup": "Згрупаваць аб’екты", "SSE.Views.Toolbar.tipIncDecimal": "Павялічыць разрад", "SSE.Views.Toolbar.tipIncFont": "Павялічыць памер шрыфту", "SSE.Views.Toolbar.tipInsertChart": "Уставіць дыяграму", "SSE.Views.Toolbar.tipInsertChartRecommend": "Insert recommended chart", "SSE.Views.Toolbar.tipInsertChartSpark": "Уставіць дыяграму", "SSE.Views.Toolbar.tipInsertEquation": "Уставіць раўнанне", "SSE.Views.Toolbar.tipInsertHorizontalText": "Уставіць гарызантальнае тэкставае поле", "SSE.Views.Toolbar.tipInsertHyperlink": "Дада<PERSON>ь гіперспасылку", "SSE.Views.Toolbar.tipInsertImage": "Уставіць выяву", "SSE.Views.Toolbar.tipInsertOpt": "Уставіць ячэйкі", "SSE.Views.Toolbar.tipInsertShape": "Уставіць фігуру", "SSE.Views.Toolbar.tipInsertSlicer": "Уставіць зводку", "SSE.Views.Toolbar.tipInsertSmartArt": "Уставіць SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Уставіць спарклайн", "SSE.Views.Toolbar.tipInsertSymbol": "Уставіць сімвал", "SSE.Views.Toolbar.tipInsertTable": "Уставіць табліцу", "SSE.Views.Toolbar.tipInsertText": "Уставіць надпіс", "SSE.Views.Toolbar.tipInsertTextart": "Уставіць Text Art", "SSE.Views.Toolbar.tipInsertVerticalText": "Уставіць вертыкальнае тэкставае поле", "SSE.Views.Toolbar.tipMerge": "Аб’яднаць і памясціць у цэнтры", "SSE.Views.Toolbar.tipNone": "Няма", "SSE.Views.Toolbar.tipNumFormat": "Фармат нумара", "SSE.Views.Toolbar.tipPageBreak": "Add a break where you want the next page to begin in the printed copy", "SSE.Views.Toolbar.tipPageMargins": "Пал<PERSON> старонак", "SSE.Views.Toolbar.tipPageOrient": "Арыентацыя старонкі", "SSE.Views.Toolbar.tipPageSize": "Памер старон<PERSON>і", "SSE.Views.Toolbar.tipPaste": "Уставіць", "SSE.Views.Toolbar.tipPrColor": "Колер запаўнення", "SSE.Views.Toolbar.tipPrint": "Друкаванне", "SSE.Views.Toolbar.tipPrintArea": "Вобласць друкавання", "SSE.Views.Toolbar.tipPrintQuick": "Хуткае друкаванне", "SSE.Views.Toolbar.tipPrintTitles": "Друкаваць загалоўкі", "SSE.Views.Toolbar.tipRedo": "Паўтарыць", "SSE.Views.Toolbar.tipReplace": "Replace", "SSE.Views.Toolbar.tipRtlSheet": "Switch the sheet direction so that the first column is on the right side", "SSE.Views.Toolbar.tipSave": "Захаваць", "SSE.Views.Toolbar.tipSaveCoauth": "Захаваць свае змены, каб іншыя карыстальнікі іх убачылі.", "SSE.Views.Toolbar.tipScale": "Умясціць", "SSE.Views.Toolbar.tipSelectAll": "Аб<PERSON><PERSON><PERSON><PERSON> усё", "SSE.Views.Toolbar.tipSendBackward": "Адправіць назад", "SSE.Views.Toolbar.tipSendForward": "Перамясціць уперад", "SSE.Views.Toolbar.tipShapesMerge": "Merge shapes", "SSE.Views.Toolbar.tipSynchronize": "Дакумент быў зменены іншым карыстальнікам. Націсніце, каб захаваць свае змены і загрузіць абнаўленні.", "SSE.Views.Toolbar.tipTextFormatting": "Яшчэ інструменты для фарматавання тэксту", "SSE.Views.Toolbar.tipTextOrientation": "Арыентацыя", "SSE.Views.Toolbar.tipUndo": "Адрабіць", "SSE.Views.Toolbar.tipVAlighOle": "Вертыкальнае выраўноўванне", "SSE.Views.Toolbar.tipVisibleArea": "Бачная вобласць", "SSE.Views.Toolbar.tipWrap": "Перанос тэксту", "SSE.Views.Toolbar.txtAccounting": "Фінансавы", "SSE.Views.Toolbar.txtAdditional": "Дадаткова", "SSE.Views.Toolbar.txtAscending": "Па ўзрастанні", "SSE.Views.Toolbar.txtAutosumTip": "Сума", "SSE.Views.Toolbar.txtCellStyle": "Стыль ячэйкі", "SSE.Views.Toolbar.txtClearAll": "Усе", "SSE.Views.Toolbar.txtClearComments": "Каментары", "SSE.Views.Toolbar.txtClearFilter": "Ачысціць фільтр", "SSE.Views.Toolbar.txtClearFormat": "Фарматаванне", "SSE.Views.Toolbar.txtClearFormula": "Функцыя", "SSE.Views.Toolbar.txtClearHyper": "Гіперспасылкі", "SSE.Views.Toolbar.txtClearText": "Тэкст", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCustom": "Адвольны", "SSE.Views.Toolbar.txtDate": "Дата", "SSE.Views.Toolbar.txtDateLong": "Доўгі фармат даты", "SSE.Views.Toolbar.txtDateShort": "Кароткі фармат даты", "SSE.Views.Toolbar.txtDateTime": "Дата і час", "SSE.Views.Toolbar.txtDescending": "Па памяншэнні", "SSE.Views.Toolbar.txtDollar": "<PERSON><PERSON><PERSON><PERSON><PERSON> ЗША", "SSE.Views.Toolbar.txtEuro": "Еўра", "SSE.Views.Toolbar.txtExp": "Экспаненцыйны", "SSE.Views.Toolbar.txtFillNum": "Заліўка", "SSE.Views.Toolbar.txtFilter": "Фільтр", "SSE.Views.Toolbar.txtFormula": "Уставіць функцыю", "SSE.Views.Toolbar.txtFraction": "Дроб", "SSE.Views.Toolbar.txtFranc": "CHF Франк", "SSE.Views.Toolbar.txtGeneral": "Агульны", "SSE.Views.Toolbar.txtInteger": "Цэлы лік", "SSE.Views.Toolbar.txtManageRange": "Кіраўнік назваў", "SSE.Views.Toolbar.txtMergeAcross": "Аб’яднаць па радках", "SSE.Views.Toolbar.txtMergeCells": "Аб’яднаць ячэйкі", "SSE.Views.Toolbar.txtMergeCenter": "Аб’яднаць і размясціць у цэнтры", "SSE.Views.Toolbar.txtNamedRange": "Названыя дыяпазоны", "SSE.Views.Toolbar.txtNewRange": "Прызначыць назву", "SSE.Views.Toolbar.txtNoBorders": "Без межаў", "SSE.Views.Toolbar.txtNumber": "Лічбавы", "SSE.Views.Toolbar.txtPasteRange": "Уставіць назву", "SSE.Views.Toolbar.txtPercentage": "Адсотак", "SSE.Views.Toolbar.txtPound": "Фунт", "SSE.Views.Toolbar.txtRouble": "Расійскі рубель", "SSE.Views.Toolbar.txtScientific": "Навуковы", "SSE.Views.Toolbar.txtSearch": "По<PERSON><PERSON>к", "SSE.Views.Toolbar.txtSort": "Сартаванне", "SSE.Views.Toolbar.txtSortAZ": "Сартаванне па ўзрастанні", "SSE.Views.Toolbar.txtSortZA": "Сартаванне па ўбыванні", "SSE.Views.Toolbar.txtSpecial": "Адмысловы", "SSE.Views.Toolbar.txtTableTemplate": "Фарматаваць як шаблон табліцы", "SSE.Views.Toolbar.txtText": "Тэкст", "SSE.Views.Toolbar.txtTime": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtUnmerge": "Скасаваць аб’яднанне ячэек", "SSE.Views.Toolbar.txtYen": "Ена", "SSE.Views.Top10FilterDialog.textType": "Паказаць", "SSE.Views.Top10FilterDialog.txtBottom": "Знізу", "SSE.Views.Top10FilterDialog.txtBy": "па", "SSE.Views.Top10FilterDialog.txtItems": "Элемент", "SSE.Views.Top10FilterDialog.txtPercent": "Адсотак", "SSE.Views.Top10FilterDialog.txtSum": "Сума", "SSE.Views.Top10FilterDialog.txtTitle": "Аўтафільтр першых 10", "SSE.Views.Top10FilterDialog.txtTop": "Найбольшыя", "SSE.Views.Top10FilterDialog.txtValueTitle": "Фільтр першых 10", "SSE.Views.ValueFieldSettingsDialog.textNext": "(наступнае)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "Лічбавы фармат", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(папярэдняе)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Налады поля значэнняў", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Сярэдняе", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Базавае поле", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Базавы элемент", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 з %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Колькасць", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Колькасць лікаў", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Адвольная назва", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Адрозненне", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Індэкс", "SSE.Views.ValueFieldSettingsDialog.txtMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMin": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "Без падліку", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "Адсотак", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Адрозненне ў адсотках", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "Адсотак ад слупка", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% ад агульнага выніку", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% ад бацькоўскай сумы", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% ад суму бацькоўскага слупка", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% ад сумы бацькоўскага радка", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% ад сумы з нарастальным вынікам ", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "Адсотак ад радка", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Здабытак", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Дыяпазон ад найменшага да найбольшага", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Дыяпазон ад найбольшага да найменшага", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "З вынікам", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Паказваць значэнні як", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Назва крыніцы:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Сума", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Аперацыя", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "Закрыць", "SSE.Views.ViewManagerDlg.guestText": "Госць", "SSE.Views.ViewManagerDlg.lockText": "Заблакавана", "SSE.Views.ViewManagerDlg.textDelete": "Выдаліць", "SSE.Views.ViewManagerDlg.textDuplicate": "Дубляваць", "SSE.Views.ViewManagerDlg.textEmpty": "Мініяцюры яшчэ не створаныя.", "SSE.Views.ViewManagerDlg.textGoTo": "Перайсці да мініяцюры", "SSE.Views.ViewManagerDlg.textLongName": "Увядзіце назву, якая карацей 128 знакаў.", "SSE.Views.ViewManagerDlg.textNew": "Новы", "SSE.Views.ViewManagerDlg.textRename": "Змяніць назву", "SSE.Views.ViewManagerDlg.textRenameError": "Назва выгляду не можа быць пустой.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Змяніць назву мініяцюры", "SSE.Views.ViewManagerDlg.textViews": "Мініяцюры аркушаў", "SSE.Views.ViewManagerDlg.tipIsLocked": "Гэты элемент рэдагуецца іншым карыстальнікам.", "SSE.Views.ViewManagerDlg.txtTitle": "Кіраўнік мініяцюр аркуша", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Are you sure you want to delete this sheet view?", "SSE.Views.ViewManagerDlg.warnDeleteView": "Вы спрабуеце выдаліць актыўную мініяцюру \"%1\".<br>Закрыць і выдаліць?", "SSE.Views.ViewTab.capBtnFreeze": "Замацаваць вобласці", "SSE.Views.ViewTab.capBtnSheetView": "Мініяцюра аркуша", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Заўсёды паказваць панэль інструментаў", "SSE.Views.ViewTab.textClose": "Закрыць", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Аб'яднаць панэлі радкоў і стану", "SSE.Views.ViewTab.textCreate": "Новы", "SSE.Views.ViewTab.textDefault": "Прадвызначана", "SSE.Views.ViewTab.textFill": "Заліўка", "SSE.Views.ViewTab.textFormula": "Панэль формул", "SSE.Views.ViewTab.textFreezeCol": "Замарозіць першы слупок", "SSE.Views.ViewTab.textFreezeRow": "Замарозіць верхні радок", "SSE.Views.ViewTab.textGridlines": "Лініі сеткі", "SSE.Views.ViewTab.textHeadings": "Загалоўкі", "SSE.Views.ViewTab.textInterfaceTheme": "Тэма інтэрфейсу", "SSE.Views.ViewTab.textLeftMenu": "Левая панэль", "SSE.Views.ViewTab.textLine": "Лінія", "SSE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "Кіраўнік мініяцюр", "SSE.Views.ViewTab.textRightMenu": "Правая панэль", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Паказваць цень для замацаваных панэляў", "SSE.Views.ViewTab.textTabStyle": "Tab style", "SSE.Views.ViewTab.textUnFreeze": "Адмацаваць вобласці", "SSE.Views.ViewTab.textZeros": "Паказваць нулі", "SSE.Views.ViewTab.textZoom": "Ма<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.tipClose": "Закрыць мініяцюру аркуша", "SSE.Views.ViewTab.tipCreate": "Стварыць мініяцюру аркуша", "SSE.Views.ViewTab.tipFreeze": "Замацаваць вобласці", "SSE.Views.ViewTab.tipInterfaceTheme": "Тэма інтэрфейсу", "SSE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipSheetView": "Мініяцюра аркуша", "SSE.Views.ViewTab.tipViewNormal": "See your document in Normal view", "SSE.Views.ViewTab.tipViewPageBreak": "See where the page breaks will appear when your document is printed", "SSE.Views.ViewTab.txtViewNormal": "Звычайны", "SSE.Views.ViewTab.txtViewPageBreak": "Page Break Preview", "SSE.Views.WatchDialog.closeButtonText": "Закрыць", "SSE.Views.WatchDialog.textAdd": "Дада<PERSON>ь значэнне для назірання", "SSE.Views.WatchDialog.textBook": "Кніга", "SSE.Views.WatchDialog.textCell": "Ячэйка", "SSE.Views.WatchDialog.textDelete": "Выдаліць назіранне", "SSE.Views.WatchDialog.textDeleteAll": "Выдаліць усе", "SSE.Views.WatchDialog.textFormula": "Формула", "SSE.Views.WatchDialog.textName": "Назва", "SSE.Views.WatchDialog.textSheet": "<PERSON>р<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textValue": "Значэнне", "SSE.Views.WatchDialog.txtTitle": "Акно назірання", "SSE.Views.WBProtection.hintAllowRanges": "Дазволіць рэдагаванне дыяпазонаў", "SSE.Views.WBProtection.hintProtectRange": "Абараніць дыяпазон", "SSE.Views.WBProtection.hintProtectSheet": "Аб<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> аркуш", "SSE.Views.WBProtection.hintProtectWB": "Аба<PERSON><PERSON>н<PERSON>ць працоўную кнігу", "SSE.Views.WBProtection.txtAllowRanges": "Дазволіць рэдагаванне дыяпазонаў", "SSE.Views.WBProtection.txtHiddenFormula": "Схаваныя формулы", "SSE.Views.WBProtection.txtLockedCell": "Заблакаваная ячэйка", "SSE.Views.WBProtection.txtLockedShape": "Заблакаваная фігура", "SSE.Views.WBProtection.txtLockedText": "Заблакаваць тэкст", "SSE.Views.WBProtection.txtProtectRange": "Абараніць дыяпазон", "SSE.Views.WBProtection.txtProtectSheet": "Аб<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> аркуш", "SSE.Views.WBProtection.txtProtectWB": "Аба<PERSON><PERSON>н<PERSON>ць працоўную кнігу", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Каб зняць абарону аркуша, увядзіце пароль", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Зняць абарону аркуша", "SSE.Views.WBProtection.txtWBUnlockDescription": "Каб зняць абарону кнігі, увядзіце пароль", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}