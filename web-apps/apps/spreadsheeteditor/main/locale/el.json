{"cancelButtonText": "Ακύρωση", "Common.Controllers.Chat.notcriticalErrorTitle": "Προειδοποίηση", "Common.Controllers.Desktop.hintBtnHome": "Εμφάνιση κύριου παραθύρου", "Common.Controllers.Desktop.itemCreateFromTemplate": "Δημιουργ<PERSON>α από πρότυπο", "Common.Controllers.History.notcriticalErrorTitle": "Προειδοποίηση", "Common.Controllers.History.txtErrorLoadHistory": "Η φόρτωση ιστορικού απέτυχε", "Common.Controllers.Plugins.helpUseMacros": "Βρείτε το κουμπί Μακροεντολές εδώ", "Common.Controllers.Plugins.helpUseMacrosHeader": "Ενημερωμένη πρόσβαση σε μακροεντολές", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Τα πρόσθετα εγκαταστάθηκαν με επιτυχία. Μπορείτε να αποκτήσετε πρόσβαση σε όλα τα πρόσθετα φόντου εδώ.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> εγκ<PERSON><PERSON><PERSON><PERSON>τάθηκε με επιτυχία. Μπορείτε να έχετε πρόσβαση σε όλα τα πρόσθετα υποβάθρου εδώ.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Εκτελέστε εγκατεστημένα πρόσθετα", "Common.Controllers.Plugins.textRunPlugin": "Εκτέλεση προόσθετου", "Common.define.chartData.textArea": "Περιοχή", "Common.define.chartData.textAreaStacked": "Σωρευμένη περιοχή", "Common.define.chartData.textAreaStackedPer": "100% Σωρευμένη περιοχή", "Common.define.chartData.textBar": "Μπάρα", "Common.define.chartData.textBarNormal": "Ομαδοποιημένη στήλη", "Common.define.chartData.textBarNormal3d": "3-<PERSON> Ομαδοποιημένη στήλη", "Common.define.chartData.textBarNormal3dPerspective": "3-<PERSON> στήλη", "Common.define.chartData.textBarStacked": "Σωρευμένη στήλη", "Common.define.chartData.textBarStacked3d": "3-<PERSON> Σωρευμένη στήλη", "Common.define.chartData.textBarStackedPer": "100% Σωρευμένη στήλη", "Common.define.chartData.textBarStackedPer3d": "3-D 100% Σωρευμένη στήλη", "Common.define.chartData.textCharts": "Γραφήματα", "Common.define.chartData.textColumn": "Στήλη", "Common.define.chartData.textColumnSpark": "Στήλη", "Common.define.chartData.textCombo": "Συνδυασμ<PERSON>ς", "Common.define.chartData.textComboAreaBar": "Σωρευμένη στήλη ομαδοποιημένη ανά περιοχή", "Common.define.chartData.textComboBarLine": "Ομαδοποιημένη γραμμή - στήλης", "Common.define.chartData.textComboBarLineSecondary": "Ομαδοποιημένη γραμμή - στήλη σε δευτερεύοντα άξονα", "Common.define.chartData.textComboCustom": "Προσαρμοσμένος συνδυασμός", "Common.define.chartData.textDoughnut": "Ντ<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "Ομαδοποιημένη μπάρα", "Common.define.chartData.textHBarNormal3d": "3-<PERSON> Ομαδοποιημένη μπάρα", "Common.define.chartData.textHBarStacked": "Σωρευμένη μπάρα", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> Σωρευμένη μπάρα", "Common.define.chartData.textHBarStackedPer": "100% Σωρευμένη μπάρα", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% Σωρευμένη μπάρα", "Common.define.chartData.textLine": "Γραμμή", "Common.define.chartData.textLine3d": "3-D γραμμή", "Common.define.chartData.textLineMarker": "Γραμμή με δείκτες", "Common.define.chartData.textLineSpark": "Γραμμή", "Common.define.chartData.textLineStacked": "Σωρευμένη γραμμή", "Common.define.chartData.textLineStackedMarker": "Σωρευμένη γραμμή με δείκτες", "Common.define.chartData.textLineStackedPer": "100% Σωρευμένη γραμμή", "Common.define.chartData.textLineStackedPerMarker": "100% Σωρευμένη γραμμή με δείκτες", "Common.define.chartData.textPie": "Πίτα", "Common.define.chartData.textPie3d": "3-<PERSON> πίτα", "Common.define.chartData.textPoint": "ΧΥ (Διασπορά)", "Common.define.chartData.textRadar": "Ραντάρ", "Common.define.chartData.textRadarFilled": "Γε<PERSON>ισμ<PERSON><PERSON><PERSON>", "Common.define.chartData.textRadarMarker": "Ραντάρ με δείκτες", "Common.define.chartData.textScatter": "Διασπορά", "Common.define.chartData.textScatterLine": "Διασπορά με ευθείες γραμμές", "Common.define.chartData.textScatterLineMarker": "Διασπορά με ευθείες γραμμές και δείκτες", "Common.define.chartData.textScatterSmooth": "Διασπορά με ομαλές γραμμές", "Common.define.chartData.textScatterSmoothMarker": "Διασπορά με ομαλές γραμμές και δείκτες", "Common.define.chartData.textSparks": "Μικρογραφήματα", "Common.define.chartData.textStock": "Μετοχή", "Common.define.chartData.textSurface": "Επιφάνεια", "Common.define.chartData.textWinLossSpark": "Νίκες/Ήττες", "Common.define.conditionalData.exampleText": "ΑαΒβΓγΨψΩω", "Common.define.conditionalData.noFormatText": "Δεν ορίστηκε μορφή", "Common.define.conditionalData.text1Above": "1 τυπική απόκλιση πάνω", "Common.define.conditionalData.text1Below": "1 τυπική απόκλιση κάτω", "Common.define.conditionalData.text2Above": "2 τυπική απόκλιση πάνω", "Common.define.conditionalData.text2Below": "2 τυπική απόκλιση κάτω", "Common.define.conditionalData.text3Above": "3 τυπική απόκλιση πάνω", "Common.define.conditionalData.text3Below": "3 τυπική απόκλιση κάτω", "Common.define.conditionalData.textAbove": "Πάνω από", "Common.define.conditionalData.textAverage": "Μέ<PERSON><PERSON> Όρος", "Common.define.conditionalData.textBegins": "Αρχίζει με", "Common.define.conditionalData.textBelow": "Κάτω από", "Common.define.conditionalData.textBetween": "Μεταξύ", "Common.define.conditionalData.textBlank": "Κενό", "Common.define.conditionalData.textBlanks": "Περιέχει κενά", "Common.define.conditionalData.textBottom": "Κάτω", "Common.define.conditionalData.textContains": "Περιέχει", "Common.define.conditionalData.textDataBar": "Μπάρα δεδομένων", "Common.define.conditionalData.textDate": "Ημερομηνία", "Common.define.conditionalData.textDuplicate": "Δημιουργ<PERSON>α διπλότυπου", "Common.define.conditionalData.textEnds": "Τελειώνει με", "Common.define.conditionalData.textEqAbove": "Ίσο με ή μεγαλύτερο", "Common.define.conditionalData.textEqBelow": "Ίσο με ή μικρότερο", "Common.define.conditionalData.textEqual": "Ίσο με", "Common.define.conditionalData.textError": "Σφάλμα", "Common.define.conditionalData.textErrors": "Περιέχει σφάλματα", "Common.define.conditionalData.textFormula": "Τύπος", "Common.define.conditionalData.textGreater": "Μεγαλύτερο από", "Common.define.conditionalData.textGreaterEq": "Μεγαλύτερο από ή ίσο με", "Common.define.conditionalData.textIconSets": "Σύνολα εικονιδίων", "Common.define.conditionalData.textLast7days": "Τις τελευταίες 7 ημέρες", "Common.define.conditionalData.textLastMonth": "Τον περασμένο μήνα", "Common.define.conditionalData.textLastWeek": "Την περασμένη εβδομάδα", "Common.define.conditionalData.textLess": "Μικρότερο από", "Common.define.conditionalData.textLessEq": "Μικρότερο από ή ίσο με", "Common.define.conditionalData.textNextMonth": "Τον επόμενο μήνα", "Common.define.conditionalData.textNextWeek": "Την επόμενη εβδομάδα", "Common.define.conditionalData.textNotBetween": "Όχι ανάμεσα", "Common.define.conditionalData.textNotBlanks": "Δεν περιέχει κενά", "Common.define.conditionalData.textNotContains": "Δεν περιέχει", "Common.define.conditionalData.textNotEqual": "Διάφορο από", "Common.define.conditionalData.textNotErrors": "Δεν περιέχει σφάλματα", "Common.define.conditionalData.textText": "Κείμενο", "Common.define.conditionalData.textThisMonth": "Αυτό το μήνα", "Common.define.conditionalData.textThisWeek": "Αυτή την εβδομάδα", "Common.define.conditionalData.textToday": "Σήμερα", "Common.define.conditionalData.textTomorrow": "Αύριο", "Common.define.conditionalData.textTop": "Επάνω", "Common.define.conditionalData.textUnique": "Μοναδικό", "Common.define.conditionalData.textValue": "Η τιμή είναι ", "Common.define.conditionalData.textYesterday": "Χθ<PERSON><PERSON>", "Common.define.smartArt.textAccentedPicture": "Τονισμένη εικόνα", "Common.define.smartArt.textAccentProcess": "Διεργασία Τονισμού", "Common.define.smartArt.textAlternatingFlow": "Εναλλασσόμενη ροή", "Common.define.smartArt.textAlternatingHexagons": "Εναλλασσόμενα εξάγωνα", "Common.define.smartArt.textAlternatingPictureBlocks": "Εναλλασσόμενα μπλοκ εικόνων", "Common.define.smartArt.textAlternatingPictureCircles": "Εναλλασσόμενοι κύκλοι εικόνων", "Common.define.smartArt.textArchitectureLayout": "Αρχιτεκτονικό σχέδιο", "Common.define.smartArt.textArrowRibbon": "Κορδέλα με βέλος", "Common.define.smartArt.textAscendingPictureAccentProcess": "Αύξουσα έμφαση εικόνας", "Common.define.smartArt.textBalance": "Ισορροπία", "Common.define.smartArt.textBasicBendingProcess": "Βασική διαδικασία κάμψης", "Common.define.smartArt.textBasicBlockList": "Βασική λίστα αποκλεισμού", "Common.define.smartArt.textBasicChevronProcess": "Βασική διαδικασία βημάτων", "Common.define.smartArt.textBasicCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> κύκλος", "Common.define.smartArt.textBasicMatrix": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πίνακας", "Common.define.smartArt.textBasicPie": "Βασική πίτα", "Common.define.smartArt.textBasicProcess": "Βασική διεργασία", "Common.define.smartArt.textBasicPyramid": "Βασική πυραμίδα", "Common.define.smartArt.textBasicRadial": "Βασικό ακτινικό ", "Common.define.smartArt.textBasicTarget": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> στόχος", "Common.define.smartArt.textBasicTimeline": "Βασική χρονική ακολουθία", "Common.define.smartArt.textBasicVenn": "Βασικό Venn", "Common.define.smartArt.textBendingPictureAccentList": "<PERSON><PERSON>σ<PERSON><PERSON> έμφα<PERSON>ης κυρτών εικόνων ", "Common.define.smartArt.textBendingPictureBlocks": "<PERSON>υρτ<PERSON> μπλοκ εικόνων", "Common.define.smartArt.textBendingPictureCaption": "Κυρτή λεζάντα εικόνας", "Common.define.smartArt.textBendingPictureCaptionList": "Κυρτή λίστα λεζάντων εικόνας", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Ημιδια<PERSON><PERSON><PERSON><PERSON><PERSON> κείμενο κυρτής εικόνας", "Common.define.smartArt.textBlockCycle": "Κυκλικό μπλοκ", "Common.define.smartArt.textBubblePictureList": "Λίστα εικόνων φυσαλίδων", "Common.define.smartArt.textCaptionedPictures": "Εικόνες με λεζάντα", "Common.define.smartArt.textChevronAccentProcess": "Διαδικα<PERSON><PERSON><PERSON> έμφασης βημάτων", "Common.define.smartArt.textChevronList": "Λίστα βημάτων", "Common.define.smartArt.textCircleAccentTimeline": "Λωρίδα χρόνου έμφασης κύκλου", "Common.define.smartArt.textCircleArrowProcess": "Διαδικασία κυκλικού βέλους ", "Common.define.smartArt.textCirclePictureHierarchy": "Ιεραρχ<PERSON>α κυκλικών εικόνων ", "Common.define.smartArt.textCircleProcess": "Κυκλική διαδικασία", "Common.define.smartArt.textCircleRelationship": "Σχέση κύκλου", "Common.define.smartArt.textCircularBendingProcess": "Διαδικασία κυκλικής κάμψης", "Common.define.smartArt.textCircularPictureCallout": "Επεξήγηση κυκλικής εικόνας", "Common.define.smartArt.textClosedChevronProcess": "Κλειστή διαδικασία βημάτων", "Common.define.smartArt.textContinuousArrowProcess": "Συνεχής διαδικασία βέλους", "Common.define.smartArt.textContinuousBlockProcess": "Συνεχής διαδικασία μπλοκ", "Common.define.smartArt.textContinuousCycle": "Συνε<PERSON><PERSON>ς κύκλος", "Common.define.smartArt.textContinuousPictureList": "Συνεχής λίστα εικόνων", "Common.define.smartArt.textConvergingArrows": "Συμβαλλόμενα βέλη", "Common.define.smartArt.textConvergingRadial": "Συγκλίνουσα ακτινική", "Common.define.smartArt.textConvergingText": "Συγκλίνον κείμενο", "Common.define.smartArt.textCounterbalanceArrows": "Βέλη αντιστάθμισης", "Common.define.smartArt.textCycle": "<PERSON><PERSON><PERSON><PERSON>ος", "Common.define.smartArt.textCycleMatrix": "Πί<PERSON><PERSON><PERSON><PERSON><PERSON> κύκλου", "Common.define.smartArt.textDescendingBlockList": "Φθίνουσα λίστα μπλοκ", "Common.define.smartArt.textDescendingProcess": "Φθίνουσα διαδικασία", "Common.define.smartArt.textDetailedProcess": "Λεπτομερής διαδικασία", "Common.define.smartArt.textDivergingArrows": "Αποκλίνοντα βέλη", "Common.define.smartArt.textDivergingRadial": "Αποκλίνουσα ακτινική", "Common.define.smartArt.textEquation": "Εξίσωση", "Common.define.smartArt.textFramedTextPicture": "Εικόνα κειμένου με πλαίσιο", "Common.define.smartArt.textFunnel": "Χωνί", "Common.define.smartArt.textGear": "Γρανάζι", "Common.define.smartArt.textGridMatrix": "Πίνα<PERSON><PERSON>ς πλέγματος", "Common.define.smartArt.textGroupedList": "Ομαδοποιημένη λίστα", "Common.define.smartArt.textHalfCircleOrganizationChart": "Οργανόγραμμα μισού κύκλου", "Common.define.smartArt.textHexagonCluster": "Εξάγωνο σύμπλεγμα", "Common.define.smartArt.textHexagonRadial": "Εξάγωνο ακτινωτό", "Common.define.smartArt.textHierarchy": "Ιεραρχία", "Common.define.smartArt.textHierarchyList": "Λίστα ιεραρχίας", "Common.define.smartArt.textHorizontalBulletList": "Οριζόντια λίστα κουκκίδων", "Common.define.smartArt.textHorizontalHierarchy": "Οριζόντια ιεραρχία", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Οριζόντια ιεραρχία με ετικέτες", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Οριζόντια πολυεπίπεδη ιεραρχία", "Common.define.smartArt.textHorizontalOrganizationChart": "Οριζόντιο οργανόγραμμα", "Common.define.smartArt.textHorizontalPictureList": "Οριζόντια λίστα εικόνων", "Common.define.smartArt.textIncreasingArrowProcess": "Αύξουσα διαδικασία βέλους", "Common.define.smartArt.textIncreasingCircleProcess": "Αύξουσα διαδικασία κύκλου", "Common.define.smartArt.textInterconnectedBlockProcess": "Διασυνδεδεμένη διαδικασία μπλοκ", "Common.define.smartArt.textInterconnectedRings": "Διασυνδεδεμένοι δακτύλιοι", "Common.define.smartArt.textInvertedPyramid": "Ανεστραμμένη πυραμίδα", "Common.define.smartArt.textLabeledHierarchy": "Ιεραρχία με ετικέτα", "Common.define.smartArt.textLinearVenn": "Γραμμικό Venn", "Common.define.smartArt.textLinedList": "Λίστα με γραμμές", "Common.define.smartArt.textList": "Λίστα", "Common.define.smartArt.textMatrix": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "Common.define.smartArt.textMultidirectionalCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON> πολλα<PERSON>λών κατευθύνσεων", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Όνομα και τίτλος οργανογράμματος", "Common.define.smartArt.textNestedTarget": "Ένθετος προορισμός", "Common.define.smartArt.textNondirectionalCycle": "Μη κατευθυντικ<PERSON>ς κύκλος", "Common.define.smartArt.textOpposingArrows": "Αντίθετα βέλη", "Common.define.smartArt.textOpposingIdeas": "Αντίθετες ιδέες", "Common.define.smartArt.textOrganizationChart": "Διάγραμμα οργάνωσης", "Common.define.smartArt.textOther": "Άλλο", "Common.define.smartArt.textPhasedProcess": "Σταδιακή διαδικασία", "Common.define.smartArt.textPicture": "Εικόνα", "Common.define.smartArt.textPictureAccentBlocks": "Μπλ<PERSON><PERSON> έμφασης εικόνας", "Common.define.smartArt.textPictureAccentList": "<PERSON>ίσ<PERSON><PERSON> έμφασης εικόνας", "Common.define.smartArt.textPictureAccentProcess": "Διαδικα<PERSON><PERSON><PERSON> έμφασης εικόνας", "Common.define.smartArt.textPictureCaptionList": "Λίστ<PERSON> λεζάντων εικόνων", "Common.define.smartArt.textPictureFrame": "Κορνίζα", "Common.define.smartArt.textPictureGrid": "Πλέγμα εικόνας", "Common.define.smartArt.textPictureLineup": "Σύνθεση εικόνων", "Common.define.smartArt.textPictureOrganizationChart": "Οργανόγραμμα εικόνας", "Common.define.smartArt.textPictureStrips": "Ταινίες εικόνων", "Common.define.smartArt.textPieProcess": "Διεργασία πίτας", "Common.define.smartArt.textPlusAndMinus": "Συν και πλην", "Common.define.smartArt.textProcess": "Διεργασία", "Common.define.smartArt.textProcessArrows": "Βέλη διεργασίας", "Common.define.smartArt.textProcessList": "Λίστα διεργα<PERSON>ιών", "Common.define.smartArt.textPyramid": "Πυραμίδα", "Common.define.smartArt.textPyramidList": "Λίστα πυραμίδας", "Common.define.smartArt.textRadialCluster": "Ακτινικό σύμπλεγμα", "Common.define.smartArt.textRadialCycle": "Ακτιν<PERSON><PERSON><PERSON><PERSON> κύκλος", "Common.define.smartArt.textRadialList": "Ακτινική λίστα", "Common.define.smartArt.textRadialPictureList": "Λίστα ακτινικών εικόνων", "Common.define.smartArt.textRadialVenn": "Ακτινικό Venn", "Common.define.smartArt.textRandomToResultProcess": "Τυχαία διαδικασία στο αποτέλεσμα", "Common.define.smartArt.textRelationship": "Σχέση", "Common.define.smartArt.textRepeatingBendingProcess": "Επαναλαμβανόμενη διαδικασία κάμψης", "Common.define.smartArt.textReverseList": "Αντίστροφη λίστα", "Common.define.smartArt.textSegmentedCycle": "Τμηματι<PERSON><PERSON><PERSON> κύκλος", "Common.define.smartArt.textSegmentedProcess": "Τμηματοποιημένη διαδικασία", "Common.define.smartArt.textSegmentedPyramid": "Κατακερματισμένη πυραμίδα", "Common.define.smartArt.textSnapshotPictureList": "Στιγμιότυπ<PERSON> λίστας εικόνων", "Common.define.smartArt.textSpiralPicture": "Σπειροειδής εικόνα", "Common.define.smartArt.textSquareAccentList": "Λίστα τετράγωνης έμφασης", "Common.define.smartArt.textStackedList": "Σωρευμένη λίστα", "Common.define.smartArt.textStackedVenn": "Στοιβαγμένο Venn", "Common.define.smartArt.textStaggeredProcess": "Κλιμακωτή διαδικασία", "Common.define.smartArt.textStepDownProcess": "Διαδικασία επιβράδυνσης", "Common.define.smartArt.textStepUpProcess": "Διαδικα<PERSON><PERSON>α επιτάχυνσης", "Common.define.smartArt.textSubStepProcess": "Διαδικασία υπο-βημάτων", "Common.define.smartArt.textTabbedArc": "Τόξο με καρτέλες", "Common.define.smartArt.textTableHierarchy": "Ιεραρχία πινάκων", "Common.define.smartArt.textTableList": "Λίστα πινάκων", "Common.define.smartArt.textTabList": "Λίστα καρτελών", "Common.define.smartArt.textTargetList": "Λίστα στόχων", "Common.define.smartArt.textTextCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON> κειμένου", "Common.define.smartArt.textThemePictureAccent": "Έμφαση εικόνας θέματος", "Common.define.smartArt.textThemePictureAlternatingAccent": "Εναλ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ος τόνος εικόνας θέματος", "Common.define.smartArt.textThemePictureGrid": "Πλέγμα εικόνας θέματος", "Common.define.smartArt.textTitledMatrix": "Πίν<PERSON><PERSON><PERSON>ς με τίτλους", "Common.define.smartArt.textTitledPictureAccentList": "<PERSON><PERSON><PERSON><PERSON><PERSON> έμφασης εικόνων με τίτλο", "Common.define.smartArt.textTitledPictureBlocks": "Μπλο<PERSON> εικόνων με τίτλο", "Common.define.smartArt.textTitlePictureLineup": "Σύνθεση εικόνων τίτλου", "Common.define.smartArt.textTrapezoidList": "Τραπεζοειδής λίστα", "Common.define.smartArt.textUpwardArrow": "<PERSON><PERSON><PERSON><PERSON> προς τα επάνω", "Common.define.smartArt.textVaryingWidthList": "Λίστα μεταβλητού πλάτους", "Common.define.smartArt.textVerticalAccentList": "Κατακόρυφη λίστα έμφασης", "Common.define.smartArt.textVerticalArrowList": "Κατακόρυφη λίστα βέλους", "Common.define.smartArt.textVerticalBendingProcess": "Διαδικα<PERSON><PERSON><PERSON> κάθετης κάμψης", "Common.define.smartArt.textVerticalBlockList": "Κατακόρυφη λίστα μπλοκ", "Common.define.smartArt.textVerticalBoxList": "Κατακόρυφη λίστα πλαισίων", "Common.define.smartArt.textVerticalBracketList": "Κατακόρυφη λίστα αγκύλης", "Common.define.smartArt.textVerticalBulletList": "Κατακόρυφη λίστα με κουκκίδες", "Common.define.smartArt.textVerticalChevronList": "Κάθετη λίστα βημάτων", "Common.define.smartArt.textVerticalCircleList": "Κατακόρυφη λίστα κύκλων", "Common.define.smartArt.textVerticalCurvedList": "Κατακόρυφη κυρτή λίστα", "Common.define.smartArt.textVerticalEquation": "Κατακόρυφη εξίσωση", "Common.define.smartArt.textVerticalPictureAccentList": "Λίστ<PERSON> έμφασης κατακόρυφης εικόνας", "Common.define.smartArt.textVerticalPictureList": "Κατακόρυφη λίστα εικόνων", "Common.define.smartArt.textVerticalProcess": "Κάθετη διαδικασία", "Common.Translation.textMoreButton": "Περισσότερα", "Common.Translation.tipFileLocked": "Το έγγραφο είναι κλειδωμένο για επεξεργασία. Μπορείτε να κάνετε αλλαγές και να τις αποθηκεύσετε αργότερα ως τοπικό αντίγραφο.", "Common.Translation.tipFileReadOnly": "Το αρχείο είναι μόνο για ανάγνωση. Για να διατηρήσετε τις αλλαγές σας, αποθηκεύστε το αρχείο με νέο όνομα ή σε διαφορετική θέση.", "Common.Translation.warnFileLocked": "Το αρχείο τελεί υπό επεξεργασία σε άλλη εφαρμογή. Μπορείτε να συνεχίσετε την επεξεργασία και να το αποθηκεύσετε ως αντίγραφo.", "Common.Translation.warnFileLockedBtnEdit": "Δημιουρ<PERSON><PERSON><PERSON> αντιγράφου", "Common.Translation.warnFileLockedBtnView": "Άνοιγμα για προβολή", "Common.UI.ButtonColored.textAutoColor": "Αυτόματα", "Common.UI.ButtonColored.textEyedropper": "Σταγονόμετρο", "Common.UI.ButtonColored.textNewColor": "Περισσότερα χρώματα", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> περιγράμματα", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> περιγράμματα", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON><PERSON><PERSON><PERSON> τεχνοτροπίες", "Common.UI.ExtendedColorDialog.addButtonText": "Προσθήκη", "Common.UI.ExtendedColorDialog.textCurrent": "Τρέχουσα", "Common.UI.ExtendedColorDialog.textHexErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια τιμή μεταξύ 000000 και FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Νέο", "Common.UI.ExtendedColorDialog.textRGBErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια αριθμητική τιμή μεταξύ 0 και 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.InputField.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Απόκρυψη συνθηματικού", "Common.UI.InputFieldBtnPassword.textHintHold": "Πατήστε παρατεταμένα για να εμφανιστεί ο κωδικός πρόσβασης", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Εμφάνιση συνθηματικού", "Common.UI.SearchBar.textFind": "Εύρεση", "Common.UI.SearchBar.tipCloseSearch": "Κλείσι<PERSON>ο αναζήτησης", "Common.UI.SearchBar.tipNextResult": "Επόμενο αποτέλεσμα", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Άνοιγμα σύνθετων ρυθμίσεων", "Common.UI.SearchBar.tipPreviousResult": "Προηγούμενο αποτέλεσμα", "Common.UI.SearchDialog.textHighlight": "Επισήμανση αποτελεσμάτων", "Common.UI.SearchDialog.textMatchCase": "Με διάκριση πεζών - κε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ν γραμμάτων", "Common.UI.SearchDialog.textReplaceDef": "Εισάγετε το κείμενο αντικατάστασης", "Common.UI.SearchDialog.textSearchStart": "Εισάγετε το κείμενό σας εδώ", "Common.UI.SearchDialog.textTitle": "Εύρεση και αντικατάσταση", "Common.UI.SearchDialog.textTitle2": "Εύρεση", "Common.UI.SearchDialog.textWholeWords": "Ολόκληρες λέξεις μόνο", "Common.UI.SearchDialog.txtBtnHideReplace": "Απόκρυψη Αντικατάστασης", "Common.UI.SearchDialog.txtBtnReplace": "Αντικατάσταση", "Common.UI.SearchDialog.txtBtnReplaceAll": "Αντικα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>η όλων", "Common.UI.SynchronizeTip.textDontShow": "Να μην εμφανίζεται αυτό το μήνυμα ξανά", "Common.UI.SynchronizeTip.textGotIt": "Εντάξει", "Common.UI.SynchronizeTip.textSynchronize": "Το έγγραφο έχει αλλάξει από άλλο χρήστη.<br>Πα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>με κάντε κλικ για να αποθηκεύσετε τις αλλαγές σας και να φορτώσετε ξανά τις ενημερώσεις.", "Common.UI.ThemeColorPalette.textRecentColors": "Πρόσφατα Χρώματα", "Common.UI.ThemeColorPalette.textStandartColors": "Τυπικά χρώματα", "Common.UI.ThemeColorPalette.textThemeColors": "Χρώματα θέματος", "Common.UI.Themes.txtThemeClassicLight": "Κλα<PERSON><PERSON><PERSON><PERSON><PERSON> ανοιχτόχρωμο", "Common.UI.Themes.txtThemeContrastDark": "Αντίθεση σκουρόχρωμο", "Common.UI.Themes.txtThemeDark": "Σκουρόχρωμο", "Common.UI.Themes.txtThemeGray": "Γκρι", "Common.UI.Themes.txtThemeLight": "Ανοιχτόχρωμο", "Common.UI.Themes.txtThemeSystem": "Ίδιο με το σύστημα", "Common.UI.Window.cancelButtonText": "Ακύρωση", "Common.UI.Window.closeButtonText": "Κλείσιμο", "Common.UI.Window.noButtonText": "Όχι", "Common.UI.Window.okButtonText": "Εντάξει", "Common.UI.Window.textConfirmation": "Επιβεβαίωση", "Common.UI.Window.textDontShow": "Να μην εμφανίζεται αυτό το μήνυμα ξανά", "Common.UI.Window.textError": "Σφάλμα", "Common.UI.Window.textInformation": "Πληροφορία", "Common.UI.Window.textWarning": "Προειδοποίηση", "Common.UI.Window.yesButtonText": "Ναι", "Common.Utils.Metric.txtCm": "εκ", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Τόνος", "Common.Utils.ThemeColor.txtAqua": "Άκουα", "Common.Utils.ThemeColor.txtbackground": "Παρασκήνιο", "Common.Utils.ThemeColor.txtBlack": "Μαύρ<PERSON>", "Common.Utils.ThemeColor.txtBlue": "Μπλε", "Common.Utils.ThemeColor.txtBrightGreen": "Ανοιχτό πράσινο", "Common.Utils.ThemeColor.txtBrown": "Καφέ", "Common.Utils.ThemeColor.txtDarkBlue": "Σκούρο μπλε", "Common.Utils.ThemeColor.txtDarker": "Σκοτεινότερο", "Common.Utils.ThemeColor.txtDarkGray": "Σκούρο γκρι", "Common.Utils.ThemeColor.txtDarkGreen": "Σκούρο πράσινο", "Common.Utils.ThemeColor.txtDarkPurple": "Σκούρο μωβ", "Common.Utils.ThemeColor.txtDarkRed": "Σκο<PERSON><PERSON><PERSON> κόκκινο", "Common.Utils.ThemeColor.txtDarkTeal": "Σκο<PERSON><PERSON><PERSON> γαλαζ<PERSON><PERSON>ρ<PERSON><PERSON>ινο", "Common.Utils.ThemeColor.txtDarkYellow": "Σκο<PERSON><PERSON><PERSON> κίτρινο", "Common.Utils.ThemeColor.txtGold": "Χρυσ<PERSON>", "Common.Utils.ThemeColor.txtGray": "Γκρι", "Common.Utils.ThemeColor.txtGreen": "Πράσινο", "Common.Utils.ThemeColor.txtIndigo": "Λουλακί", "Common.Utils.ThemeColor.txtLavender": "Λεβάντα", "Common.Utils.ThemeColor.txtLightBlue": "Ανοιχτό μπλε", "Common.Utils.ThemeColor.txtLighter": "Ανοιχτότερο", "Common.Utils.ThemeColor.txtLightGray": "Ανοιχτό γκρι", "Common.Utils.ThemeColor.txtLightGreen": "Ανοιχτό πράσινο", "Common.Utils.ThemeColor.txtLightOrange": "Ανοιχτό πορτοκαλί", "Common.Utils.ThemeColor.txtLightYellow": "Ανοιχτό κίτρινο", "Common.Utils.ThemeColor.txtOrange": "Πορτοκ<PERSON>λ<PERSON>", "Common.Utils.ThemeColor.txtPink": "Ροζ", "Common.Utils.ThemeColor.txtPurple": "Μοβ", "Common.Utils.ThemeColor.txtRed": "Κόκκινο", "Common.Utils.ThemeColor.txtRose": "Ρόδο", "Common.Utils.ThemeColor.txtSkyBlue": "Μπλε ουρανού", "Common.Utils.ThemeColor.txtTeal": "Γ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>νο", "Common.Utils.ThemeColor.txttext": "Κείμενο", "Common.Utils.ThemeColor.txtTurquosie": "<PERSON>υ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtViolet": "Βιολετί", "Common.Utils.ThemeColor.txtWhite": "Λευκό", "Common.Utils.ThemeColor.txtYellow": "Κίτρινο", "Common.Views.About.txtAddress": "Διεύθυνση: ", "Common.Views.About.txtLicensee": "ΑΔΕΙΟΔΕΚΤΗΣ", "Common.Views.About.txtLicensor": "ΑΔΕΙΟΔΟΤΗΣ", "Common.Views.About.txtMail": "email: ", "Common.Views.About.txtPoweredBy": "Υποστηρίζεται από", "Common.Views.About.txtTel": "Tηλ.: ", "Common.Views.About.txtVersion": "Έκδοση ", "Common.Views.AutoCorrectDialog.textAdd": "Προσθήκη", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Εφαρμογή κατά την εργασία", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Αυτόματη διόρθωση", "Common.Views.AutoCorrectDialog.textAutoFormat": "Αυτόματη μορφοποίηση κατά την πληκτρολόγηση", "Common.Views.AutoCorrectDialog.textBy": "Από", "Common.Views.AutoCorrectDialog.textDelete": "Διαγραφή", "Common.Views.AutoCorrectDialog.textHyperlink": "Μονοπάτια δικτύου και διαδικτύου με υπερσυνδέσμους", "Common.Views.AutoCorrectDialog.textMathCorrect": "Αυτόματη διόρθωση μαθηματικών", "Common.Views.AutoCorrectDialog.textNewRowCol": "Συμπερίληψη νέων γραμμών και στηλών στον πίνακα", "Common.Views.AutoCorrectDialog.textRecognized": "Αναγνωρισμένες συναρτήσεις", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Οι ακόλουθες εκφράσεις αναγνωρίζονται ως μαθηματικές. Δεν θα μορφοποιηθούν αυτόματα με πλάγια γράμματα.", "Common.Views.AutoCorrectDialog.textReplace": "Αντικατάσταση", "Common.Views.AutoCorrectDialog.textReplaceText": "Αντικα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>η κατά την πληκτρολόγηση", "Common.Views.AutoCorrectDialog.textReplaceType": "Αντικα<PERSON><PERSON><PERSON>τα<PERSON>η κειμένου κατά την πληκτρολόγηση", "Common.Views.AutoCorrectDialog.textReset": "Αρχικοποίηση", "Common.Views.AutoCorrectDialog.textResetAll": "Αρχικοποίηση στην προεπιλογή", "Common.Views.AutoCorrectDialog.textRestore": "Επαναφορά", "Common.Views.AutoCorrectDialog.textTitle": "Αυτόματη διόρθωση", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Οι αναγνωρισμένες συναρτήσεις πρέπει να περιέχουν μόνο τα γράμματα A <PERSON>ω<PERSON> Z, κεφαλα<PERSON>α ή μικρά.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Κάθε έκφραση που προσθέσατε θα αφαιρεθεί και ό,τι αφαιρέθηκε θα αποκατασταθεί. Θέλετε να συνεχίσετε;", "Common.Views.AutoCorrectDialog.warnReplace": "Η καταχώρηση αυτόματης διόρθωσης για %1 υπάρχει ήδη. Θέλετε να την αντικαταστήσετε;", "Common.Views.AutoCorrectDialog.warnReset": "Κάθε αυτόματη διόρθωση που προσθέσατε θα αφαιρεθεί και ό,τι τροποποιήθηκε θα αποκατασταθεί στην αρχική του τιμή. Θέλετε να συνεχίσετε;", "Common.Views.AutoCorrectDialog.warnRestore": "Η καταχώρηση αυτόματης διόρθωσης για %1 θα τεθεί στην αρχική τιμή της. Θέλετε να συνεχίσετε;", "Common.Views.Chat.textChat": "Συνομιλία", "Common.Views.Chat.textClosePanel": "Κλείσιμο συνομιλίας", "Common.Views.Chat.textEnterMessage": "Εισάγετε το μήνυμά σας εδώ", "Common.Views.Chat.textSend": "Αποστολή", "Common.Views.Comments.mniAuthorAsc": "Συγγραφ<PERSON>ας Α έως Ω", "Common.Views.Comments.mniAuthorDesc": "Συγγραφ<PERSON>ας Ω έως Α", "Common.Views.Comments.mniDateAsc": "Παλαιότερο", "Common.Views.Comments.mniDateDesc": "Νεότερο", "Common.Views.Comments.mniFilterGroups": "Φιλτράρισμα κατά Ομάδα", "Common.Views.Comments.mniPositionAsc": "Από πάνω", "Common.Views.Comments.mniPositionDesc": "Από κάτω", "Common.Views.Comments.textAdd": "Προσθήκη", "Common.Views.Comments.textAddComment": "Προσθήκη σχολίου", "Common.Views.Comments.textAddCommentToDoc": "Προσθήκη σχολίου στο έγγραφο", "Common.Views.Comments.textAddReply": "Προσθήκη απάντησης", "Common.Views.Comments.textAll": "Όλα", "Common.Views.Comments.textAnonym": "Επισκέπτης", "Common.Views.Comments.textCancel": "Ακύρωση", "Common.Views.Comments.textClose": "Κλείσιμο", "Common.Views.Comments.textClosePanel": "Κλείσιμο σχολίων", "Common.Views.Comments.textComment": "Σχόλιο", "Common.Views.Comments.textComments": "Σχόλια", "Common.Views.Comments.textEdit": "Εντάξει", "Common.Views.Comments.textEnterCommentHint": "Εισάγετε το σχόλιό σας εδώ", "Common.Views.Comments.textHintAddComment": "Προσθήκη σχολίου", "Common.Views.Comments.textOpenAgain": "Άνοιγμα ξανά", "Common.Views.Comments.textReply": "Απάντηση", "Common.Views.Comments.textResolve": "Επίλυση", "Common.Views.Comments.textResolved": "Επιλύθηκε", "Common.Views.Comments.textSort": "Ταξινόμηση σχολίων", "Common.Views.Comments.textSortFilter": "Ταξινόμηση και φιλτράρισμα σχολίων", "Common.Views.Comments.textSortFilterMore": "Ταξινόμηση, φιλτράρισμα και άλλα", "Common.Views.Comments.textSortMore": "Ταξινόμηση και άλλα", "Common.Views.Comments.textViewResolved": "Δεν έχετε άδεια να ανοίξετε ξανά το σχόλιο", "Common.Views.Comments.txtEmpty": "Δεν υπάρχουν σχόλια στο φύλλο.", "Common.Views.CopyWarningDialog.textDontShow": "Να μην εμφανίζεται αυτό το μήνυμα ξανά", "Common.Views.CopyWarningDialog.textMsg": "Η αντιγραφή, η αποκοπή και η επικόλληση μέσω των κουμπιών της εργαλειοθήκης του συντάκτη καθώς και οι ενέργειες του μενού συμφραζομένων εφαρμόζονται μόνο εντός αυτής της καρτέλας.<br><br>Γ<PERSON>α αντιγραφή ή επικόλληση από ή προς εφαρμογές εκτός της καρτέλας χρησιμοποιήστε τους ακόλουθους συνδυασμούς πλήκτρων:", "Common.Views.CopyWarningDialog.textTitle": "Ενέργειες αντιγραφής, αποκ<PERSON><PERSON><PERSON><PERSON> και επικόλλησης", "Common.Views.CopyWarningDialog.textToCopy": "για Αντιγραφή", "Common.Views.CopyWarningDialog.textToCut": "για Αποκοπή", "Common.Views.CopyWarningDialog.textToPaste": "για Επικόλληση", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Λή<PERSON>η", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Ελέγξτε τις εντολές που θα εμφανίζονται στη γραμμή εργαλείων γρήγορης πρόσβασης", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Εκτύπωση", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Γρήγορη εκτύπωση", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Επανάληψη", "Common.Views.CustomizeQuickAccessDialog.textSave": "Αποθήκευση", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Προσαρμογή γρήγορης πρόσβασης", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Αναίρεση", "Common.Views.DocumentAccessDialog.textLoading": "Φόρτωση...", "Common.Views.DocumentAccessDialog.textTitle": "Ρυθμίσεις δικαιωμάτων", "Common.Views.DocumentPropertyDialog.errorDate": "Μπορείτε να επιλέξετε μια τιμή από το ημερολόγιο για να αποθηκεύσετε την τιμή ως Ημερομηνία. <br><PERSON><PERSON><PERSON> εισαγάγετε μια τιμή με μη αυτόματο τρόπο, θα αποθηκευτεί ως Κείμενο.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "Όχι", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Ναι", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Η ιδιότητα πρέπει να έχει τίτλο", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Τίτλος", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Ναι\" ή \"όχι\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Ημερομηνία", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Τύπος", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Αριθμός", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Κατα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> έναν έγκυρο αριθμό", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Κείμενο", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Η ιδιότητα πρέπει να έχει τιμή", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Τιμή", "Common.Views.DocumentPropertyDialog.txtTitle": "Νέα ιδιότητα εγγράφου", "Common.Views.Draw.hintEraser": "Γόμα", "Common.Views.Draw.hintSelect": "Επιλογή", "Common.Views.Draw.txtEraser": "Γόμα", "Common.Views.Draw.txtHighlighter": "Επισήμανση", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Πένα", "Common.Views.Draw.txtSelect": "Επιλογή", "Common.Views.Draw.txtSize": "Μέγεθος", "Common.Views.EditNameDialog.textLabel": "Ετικέτα:", "Common.Views.EditNameDialog.textLabelError": "Η ετικέτα δεν μπορεί να είναι κενή.", "Common.Views.Header.ariaQuickAccessToolbar": "Γραμμή εργαλείων γρήγορης πρόσβασης", "Common.Views.Header.labelCoUsersDescr": "Οι χρήστες που επεξεργ<PERSON><PERSON>ονται το αρχείο:", "Common.Views.Header.textAddFavorite": "Σημείωση ως αγαπημένο", "Common.Views.Header.textAdvSettings": "Προηγμένες ρυθμίσεις", "Common.Views.Header.textBack": "Άνοιγμα θέσης αρχείου", "Common.Views.Header.textClose": "Κλείσιμο αρχείου", "Common.Views.Header.textCompactView": "Απόκρυψη Γραμμής Εργαλείων", "Common.Views.Header.textHideLines": "Απόκρυψη Χαράκων", "Common.Views.Header.textHideStatusBar": "Φύλλα στη γραμμή κατάστασης", "Common.Views.Header.textPrint": "Εκτύπωση", "Common.Views.Header.textReadOnly": "Μόνο για ανάγνωση", "Common.Views.Header.textRemoveFavorite": "Αφαίρεση από τα Αγαπημένα", "Common.Views.Header.textSaveBegin": "Αποθήκευση...", "Common.Views.Header.textSaveChanged": "Τροποποιημένο", "Common.Views.Header.textSaveEnd": "Όλες οι αλλαγές αποθηκεύτηκαν", "Common.Views.Header.textSaveExpander": "Όλες οι αλλαγές αποθηκεύτηκαν", "Common.Views.Header.textShare": "Διαμοιρασμός", "Common.Views.Header.textZoom": "Εστίαση", "Common.Views.Header.tipAccessRights": "Διαχείριση δικαιωμάτων πρόσβασης εγγράφου", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Προσαρμογή γραμμής εργαλείων γρήγορης πρόσβασης", "Common.Views.Header.tipDownload": "Λήψη αρχείου", "Common.Views.Header.tipGoEdit": "Επεξεργασία τρέχοντος αρχείου", "Common.Views.Header.tipPrint": "Εκτύπωση αρχείου", "Common.Views.Header.tipPrintQuick": "Γρήγορη εκτύπωση", "Common.Views.Header.tipRedo": "Επανάληψη", "Common.Views.Header.tipSave": "Αποθήκευση", "Common.Views.Header.tipSearch": "Εύρεση", "Common.Views.Header.tipUndo": "Αναίρεση", "Common.Views.Header.tipUndock": "Απαγ<PERSON>ίστρωση σε ξεχωριστό παράθυρο", "Common.Views.Header.tipUsers": "Προβολή χρηστών", "Common.Views.Header.tipViewSettings": "Προβολή ρυθμίσεων", "Common.Views.Header.tipViewUsers": "Προβολή χρηστών και διαχείριση δικαιωμάτων πρόσβασης σε έγγραφα", "Common.Views.Header.txtAccessRights": "Αλλαγή δικαιωμάτων πρόσβασης", "Common.Views.Header.txtRename": "Μετονομασία", "Common.Views.History.textCloseHistory": "Κλείσι<PERSON><PERSON> Ιστορικού", "Common.Views.History.textHideAll": "Απόκρυψη λεπτομερών αλλαγών", "Common.Views.History.textHighlightDeleted": "Η επισήμανση διαγράφηκε", "Common.Views.History.textMore": "Περισσότερα", "Common.Views.History.textRestore": "Επαναφορά", "Common.Views.History.textShowAll": "Εμφάνιση λεπτομερών αλλαγών", "Common.Views.History.textVer": "εκδ.", "Common.Views.History.textVersionHistory": "Ιστορικ<PERSON> εκδόσεων", "Common.Views.ImageFromUrlDialog.textUrl": "Επικόλληση URL εικόνας:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Αυτό το πεδίο πρέπει να είναι διεύθυνση URL με τη μορφή «http://www.example.com»", "Common.Views.ListSettingsDialog.textBulleted": "Με κουκίδες", "Common.Views.ListSettingsDialog.textFromFile": "Από αρχείο", "Common.Views.ListSettingsDialog.textFromStorage": "Από αποθηκευτικό χώρο", "Common.Views.ListSettingsDialog.textFromUrl": "Από διεύθυνση URL", "Common.Views.ListSettingsDialog.textNumbering": "Αριθμημένο", "Common.Views.ListSettingsDialog.textSelect": "Επιλογή από", "Common.Views.ListSettingsDialog.tipChange": "Αλλαγ<PERSON> κουκίδων", "Common.Views.ListSettingsDialog.txtBullet": "Κουκκίδα", "Common.Views.ListSettingsDialog.txtColor": "Χρώμα", "Common.Views.ListSettingsDialog.txtImage": "Εικόνα", "Common.Views.ListSettingsDialog.txtImport": "Εισαγωγή", "Common.Views.ListSettingsDialog.txtNewBullet": "Νέα κουκίδα", "Common.Views.ListSettingsDialog.txtNewImage": "Νέα εικόνα", "Common.Views.ListSettingsDialog.txtNone": "Κανένα", "Common.Views.ListSettingsDialog.txtOfText": "% του κειμένου", "Common.Views.ListSettingsDialog.txtSize": "Μέγεθος", "Common.Views.ListSettingsDialog.txtStart": "Έναρξη από", "Common.Views.ListSettingsDialog.txtSymbol": "Σύμβολο", "Common.Views.ListSettingsDialog.txtTitle": "Ρυθμίσεις λίστας", "Common.Views.ListSettingsDialog.txtType": "Τύπος", "Common.Views.MacrosDialog.textCopy": "Αντιγραφή", "Common.Views.MacrosDialog.textCustomFunction": "Προσαρμοσμένη λειτουργία", "Common.Views.MacrosDialog.textDelete": "Διαγραφή", "Common.Views.MacrosDialog.textLoading": "Φόρτωση...", "Common.Views.MacrosDialog.textMacros": "Μακροεντολές", "Common.Views.MacrosDialog.textMakeAutostart": "Κάντε αυτόματη εκκίνηση", "Common.Views.MacrosDialog.textRename": "Μετονομασία", "Common.Views.MacrosDialog.textRun": "Εκτέλεση", "Common.Views.MacrosDialog.textSave": "Αποθήκευση", "Common.Views.MacrosDialog.textTitle": "Μακροεντολές", "Common.Views.MacrosDialog.textUnMakeAutostart": "Κατάργηση αυτόματης εκκίνησης", "Common.Views.MacrosDialog.tipFunctionAdd": "Προσθήκη προσαρμοσμένης λειτουργίας", "Common.Views.MacrosDialog.tipMacrosAdd": "Προσθήκη μακροεντολών", "Common.Views.MacrosDialog.tipMacrosRun": "Εκτέλεση", "Common.Views.OpenDialog.closeButtonText": "Κλείσιμο αρχείου", "Common.Views.OpenDialog.textInvalidRange": "Μη έγκυρο εύρος κελιών", "Common.Views.OpenDialog.textSelectData": "Επιλογή δεδομένων", "Common.Views.OpenDialog.txtAdvanced": "Για προχωρημένους", "Common.Views.OpenDialog.txtColon": "Άνω κάτω τελεία", "Common.Views.OpenDialog.txtComma": "Κόμμα", "Common.Views.OpenDialog.txtDelimiter": "Διαχωριστικό", "Common.Views.OpenDialog.txtDestData": "Επιλογή θέσης για τα δεδομένα", "Common.Views.OpenDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "Common.Views.OpenDialog.txtEncoding": "Κωδικοποίηση", "Common.Views.OpenDialog.txtIncorrectPwd": "Το συνθηματικό είναι εσφαλμένο.", "Common.Views.OpenDialog.txtOpenFile": "Εισάγετε συνθηματικό για να ανοίξετε το αρχείο", "Common.Views.OpenDialog.txtOther": "Άλλο", "Common.Views.OpenDialog.txtPassword": "Συνθηματικό", "Common.Views.OpenDialog.txtPreview": "Προεπισκόπηση", "Common.Views.OpenDialog.txtProtected": "Μό<PERSON><PERSON>ς βάλετε το συνθηματι<PERSON><PERSON> και ανοίξετε το αρχείο, θα γίνει επαναφορά του τρέχοντος συνθηματικού.", "Common.Views.OpenDialog.txtSemicolon": "Άνω τελεία", "Common.Views.OpenDialog.txtSpace": "Κενό διάστημα", "Common.Views.OpenDialog.txtTab": "Καρτ<PERSON><PERSON>α", "Common.Views.OpenDialog.txtTitle": "Διαλέξτε %1 επιλογές", "Common.Views.OpenDialog.txtTitleProtected": "Προστατευμένο αρχείο", "Common.Views.PasswordDialog.txtDescription": "Ορίστε ένα συνθηματικό για την προστασία αυτού του εγγράφου", "Common.Views.PasswordDialog.txtIncorrectPwd": "Το συνθηματικ<PERSON> επιβεβαίωσης δεν είναι πανομοιότυπο", "Common.Views.PasswordDialog.txtPassword": "Συνθηματικό", "Common.Views.PasswordDialog.txtRepeat": "Επανάληψη συνθηματικού", "Common.Views.PasswordDialog.txtTitle": "Ορισμός συνθηματικού", "Common.Views.PasswordDialog.txtWarning": "Προσοχή: <PERSON><PERSON><PERSON> χ<PERSON>τε ή ξεχάσετε το συνθηματικό, δεν είναι δυνατή η ανάκτησή του. Παρακαλούμε διατηρήστε το σε ασφαλές μέρος.", "Common.Views.PluginDlg.textLoading": "Γίνεται φόρτωση", "Common.Views.PluginPanel.textClosePanel": "Κλείσιμο πρόσθετου", "Common.Views.PluginPanel.textLoading": "Φόρτωση", "Common.Views.Plugins.groupCaption": "Πρόσθετα", "Common.Views.Plugins.strPlugins": "Πρόσθετα", "Common.Views.Plugins.textBackgroundPlugins": "Πρόσθετα φόντου", "Common.Views.Plugins.textSettings": "Ρυθμίσεις", "Common.Views.Plugins.textStart": "Εκκίνηση", "Common.Views.Plugins.textStop": "Διακοπή", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "Η λίστα των προσθηκών φόντου", "Common.Views.Plugins.tipMore": "Περισσότερα", "Common.Views.Protection.hintAddPwd": "Κρυπτογράφηση με συνθηματικό", "Common.Views.Protection.hintDelPwd": "Διαγραφή συνθηματικού", "Common.Views.Protection.hintPwd": "Αλλα<PERSON><PERSON> <PERSON> διαγραφή συνθηματικού", "Common.Views.Protection.hintSignature": "Προσθήκη ψηφιακής υπογραφής ή γραμμής υπογραφής", "Common.Views.Protection.txtAddPwd": "Προσθήκη συνθηματικού", "Common.Views.Protection.txtChangePwd": "Αλλαγή συνθηματικού", "Common.Views.Protection.txtDeletePwd": "Διαγραφή συνθηματικού", "Common.Views.Protection.txtEncrypt": "Κρυπτογράφηση", "Common.Views.Protection.txtInvisibleSignature": "Προσθήκη ψηφιακής υπογραφής", "Common.Views.Protection.txtSignature": "Υπογραφή", "Common.Views.Protection.txtSignatureLine": "Προσθήκη γραμμής υπογραφής", "Common.Views.RecentFiles.txtOpenRecent": "Άνοιγμα πρόσφατου", "Common.Views.RenameDialog.textName": "Όνομα αρχείου", "Common.Views.RenameDialog.txtInvalidName": "Το όνομα αρχείου δεν μπορεί να περιέχει κανέναν από τους ακόλουθους χαρακτήρες:", "Common.Views.ReviewChanges.hintNext": "Στην επόμενη αλλαγή", "Common.Views.ReviewChanges.hintPrev": "Στην προηγούμενη αλλαγή", "Common.Views.ReviewChanges.strFast": "Γρήγ<PERSON><PERSON>η", "Common.Views.ReviewChanges.strFastDesc": "Συν-επεξεργασία πραγματικού χρόνου. Όλες οι αλλαγές αποθηκεύονται αυτόματα.", "Common.Views.ReviewChanges.strStrict": "Αυστηρή", "Common.Views.ReviewChanges.strStrictDesc": "Χρησιμοποιήστε το κουμπί 'Αποθήκευση' για να συγχρονίσετε τις αλλαγές που κάνετε εσείς και οι άλλοι.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Αποδοχή τρέχουσας αλλαγής", "Common.Views.ReviewChanges.tipCoAuthMode": "Ορισμ<PERSON><PERSON> κατάστασης συν-επεξεργασίας", "Common.Views.ReviewChanges.tipCommentRem": "Διαγρα<PERSON><PERSON> σχολίων", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Διαγρα<PERSON><PERSON> υφιστάμενων σχολίων", "Common.Views.ReviewChanges.tipCommentResolve": "Επίλυση σχολίων", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Επίλυση των τρεχόντων σχολίων", "Common.Views.ReviewChanges.tipHistory": "Εμφάνιση ιστορικού εκδόσεων", "Common.Views.ReviewChanges.tipRejectCurrent": "Απόρριψη τρέχουσας αλλαγής", "Common.Views.ReviewChanges.tipReview": "Παρα<PERSON><PERSON>λούθηση αλλαγών", "Common.Views.ReviewChanges.tipReviewView": "Επιλέξτε τον τρόπο προβολής των αλλαγών", "Common.Views.ReviewChanges.tipSetDocLang": "Ορισμ<PERSON><PERSON> γλώσσας εγγράφου", "Common.Views.ReviewChanges.tipSetSpelling": "Έλεγχος ορθογραφίας", "Common.Views.ReviewChanges.tipSharing": "Διαχείριση δικαιωμάτων πρόσβασης εγγράφου", "Common.Views.ReviewChanges.txtAccept": "Αποδοχή", "Common.Views.ReviewChanges.txtAcceptAll": "Αποδο<PERSON><PERSON> όλων των αλλαγών", "Common.Views.ReviewChanges.txtAcceptChanges": "Αποδοχ<PERSON> αλλαγών", "Common.Views.ReviewChanges.txtAcceptCurrent": "Αποδοχή τρέχουσας αλλαγής", "Common.Views.ReviewChanges.txtChat": "Συνομιλία", "Common.Views.ReviewChanges.txtClose": "Κλείσιμο", "Common.Views.ReviewChanges.txtCoAuthMode": "Κατάσταση Συν-επεξεργασίας", "Common.Views.ReviewChanges.txtCommentRemAll": "Διαγρα<PERSON><PERSON> όλων των σχολίων", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Διαγρα<PERSON><PERSON> υφιστάμενων σχολίων", "Common.Views.ReviewChanges.txtCommentRemMy": "Διαγραφή των σχολίων μου", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Διαγραφ<PERSON> πρόσφατων σχολίων μου", "Common.Views.ReviewChanges.txtCommentRemove": "Διαγραφή", "Common.Views.ReviewChanges.txtCommentResolve": "Επίλυση", "Common.Views.ReviewChanges.txtCommentResolveAll": "Επίλυση όλων των σχολίων", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Επίλυση των τρεχόντων σχολίων", "Common.Views.ReviewChanges.txtCommentResolveMy": "Επίλυση των σχολίων μου", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Επίλυση των Τρεχόντων Σχολίων Μου", "Common.Views.ReviewChanges.txtDocLang": "Γλώσσα", "Common.Views.ReviewChanges.txtFinal": "Όλες οι αλλαγές έγιναν αποδεκτές (Προεπισκόπηση)", "Common.Views.ReviewChanges.txtFinalCap": "Τελικ<PERSON>ς", "Common.Views.ReviewChanges.txtHistory": "Ιστορικ<PERSON> Εκδόσεων", "Common.Views.ReviewChanges.txtMarkup": "Όλες οι αλλαγές (Επεξεργασία)", "Common.Views.ReviewChanges.txtMarkupCap": "Σήμανση", "Common.Views.ReviewChanges.txtNext": "Επόμενο", "Common.Views.ReviewChanges.txtOriginal": "Όλες οι αλλαγές απορρίφθηκαν (Προεπισκόπηση)", "Common.Views.ReviewChanges.txtOriginalCap": "Πρωτότυπο", "Common.Views.ReviewChanges.txtPrev": "Προηγούμενο", "Common.Views.ReviewChanges.txtReject": "Απόρριψη", "Common.Views.ReviewChanges.txtRejectAll": "Απόρριψη όλων των αλλαγών", "Common.Views.ReviewChanges.txtRejectChanges": "Απόρριψη αλλαγών", "Common.Views.ReviewChanges.txtRejectCurrent": "Απόρριψη τρέχουσας αλλαγής", "Common.Views.ReviewChanges.txtSharing": "Διαμοιρασμός", "Common.Views.ReviewChanges.txtSpelling": "Έλεγχος ορθογραφίας", "Common.Views.ReviewChanges.txtTurnon": "Παρα<PERSON><PERSON>λούθηση αλλαγών", "Common.Views.ReviewChanges.txtView": "Κατάσταση Προβολής", "Common.Views.ReviewPopover.textAdd": "Προσθήκη", "Common.Views.ReviewPopover.textAddReply": "Προσθήκη απάντησης", "Common.Views.ReviewPopover.textCancel": "Ακύρωση", "Common.Views.ReviewPopover.textClose": "Κλείσιμο", "Common.Views.ReviewPopover.textComment": "Σχόλιο", "Common.Views.ReviewPopover.textEdit": "Εντάξει", "Common.Views.ReviewPopover.textEnterComment": "Εισάγετε το σχόλιό σας εδώ", "Common.Views.ReviewPopover.textMention": "+mention θα δώσει πρόσβαση στο αρχείο και θα στείλει email", "Common.Views.ReviewPopover.textMentionNotify": "+mention θα ενημερώσει τον χρήστη με email", "Common.Views.ReviewPopover.textOpenAgain": "Άνοιγμα ξανά", "Common.Views.ReviewPopover.textReply": "Απάντηση", "Common.Views.ReviewPopover.textResolve": "Επίλυση", "Common.Views.ReviewPopover.textViewResolved": "Δεν έχετε άδεια να ανοίξετε ξανά το σχόλιο", "Common.Views.ReviewPopover.txtDeleteTip": "Διαγραφή", "Common.Views.ReviewPopover.txtEditTip": "Επεξεργασία", "Common.Views.SaveAsDlg.textLoading": "Γίνεται φόρτωση", "Common.Views.SaveAsDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> για αποθήκευση", "Common.Views.SearchPanel.textByColumns": "Κατά στήλες", "Common.Views.SearchPanel.textByRows": "Κατά γραμμές", "Common.Views.SearchPanel.textCaseSensitive": "Διάκριση Πεζών-Κεφαλαίων", "Common.Views.SearchPanel.textCell": "Κελί", "Common.Views.SearchPanel.textCloseSearch": "Κλείσι<PERSON>ο αναζήτησης", "Common.Views.SearchPanel.textContentChanged": "Το έγγραφο τροποποιήθηκε.", "Common.Views.SearchPanel.textFind": "Εύρεση", "Common.Views.SearchPanel.textFindAndReplace": "Εύρεση και αντικατάσταση", "Common.Views.SearchPanel.textFormula": "Τύπος", "Common.Views.SearchPanel.textFormulas": "Τύποι", "Common.Views.SearchPanel.textItemEntireCell": "Ολόκληρο το περιεχόμενου κελιού", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} αντικ<PERSON>ίμ<PERSON><PERSON>α αντικατα<PERSON>τάθηκαν με επιτυχία.", "Common.Views.SearchPanel.textLookIn": "Αναζήτηση σε", "Common.Views.SearchPanel.textMatchUsingRegExp": "Ταίριασμα με χρήση κανονικ<PERSON>ν εκφράσεων", "Common.Views.SearchPanel.textName": "Όνομα", "Common.Views.SearchPanel.textNoMatches": "Χ<PERSON><PERSON><PERSON>ς ταίριασμα", "Common.Views.SearchPanel.textNoSearchResults": "Δεν υπάρχουν αποτελέσματα αναζήτησης", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "Αντικα<PERSON><PERSON><PERSON><PERSON><PERSON>θηκαν {0}/{1} στοιχεία. Τα υπόλοιπα {2} στοιχεία είναι κλειδωμένα από άλλους χρήστες.", "Common.Views.SearchPanel.textReplace": "Αντικατάσταση", "Common.Views.SearchPanel.textReplaceAll": "Αντικα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>η όλων", "Common.Views.SearchPanel.textReplaceWith": "Αντικατάσταση με", "Common.Views.SearchPanel.textSearch": "Αναζήτηση", "Common.Views.SearchPanel.textSearchAgain": "{0}Διενέργεια νέας αναζήτησης{1} για ακριβή αποτελέσματα.", "Common.Views.SearchPanel.textSearchHasStopped": "Η αναζήτηση έχει σταματήσει", "Common.Views.SearchPanel.textSearchOptions": "Ε<PERSON>ι<PERSON><PERSON><PERSON><PERSON><PERSON> αναζήτησης", "Common.Views.SearchPanel.textSearchResults": "Αποτελέσματα αναζήτησης: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Αποτελέσματα αναζήτησης", "Common.Views.SearchPanel.textSelectDataRange": "Επιλογή εύρους δεδομένων", "Common.Views.SearchPanel.textSheet": "Φύλλο", "Common.Views.SearchPanel.textSpecificRange": "Συγκεκριμένο εύρος", "Common.Views.SearchPanel.textTooManyResults": "Υπάρχουν πάρα πολλά αποτελέσματα για εμφάνιση εδώ", "Common.Views.SearchPanel.textValue": "Τιμή", "Common.Views.SearchPanel.textValues": "Τιμές", "Common.Views.SearchPanel.textWholeWords": "Ολόκληρες λέξεις μόνο", "Common.Views.SearchPanel.textWithin": "Εντ<PERSON>ς", "Common.Views.SearchPanel.textWorkbook": "Βιβλίο Εργασίας", "Common.Views.SearchPanel.tipNextResult": "Επόμενο αποτέλεσμα", "Common.Views.SearchPanel.tipPreviousResult": "Προηγούμενο αποτέλεσμα", "Common.Views.SelectFileDlg.textLoading": "Γίνεται φόρτωση", "Common.Views.SelectFileDlg.textTitle": "Επιλογή πηγής δεδομένων", "Common.Views.ShapeShadowDialog.txtAngle": "Γωνία", "Common.Views.ShapeShadowDialog.txtDistance": "Aπόσταση", "Common.Views.ShapeShadowDialog.txtSize": "Μέγεθος", "Common.Views.ShapeShadowDialog.txtTitle": "Ρύθμιση σκιάς", "Common.Views.ShapeShadowDialog.txtTransparency": "Διαφάνεια", "Common.Views.SignDialog.textBold": "Έντονα", "Common.Views.SignDialog.textCertificate": "Πιστοποιητικό", "Common.Views.SignDialog.textChange": "Αλλαγή", "Common.Views.SignDialog.textInputName": "Εισαγω<PERSON><PERSON> ονόμα<PERSON>ος υπογράφοντος", "Common.Views.SignDialog.textItalic": "Πλάγια", "Common.Views.SignDialog.textNameError": "Το όνομα υπογράφοντα δεν μπορεί να είναι κενό.", "Common.Views.SignDialog.textPurpose": "Ο σκοπός για υπογραφή αυτού του εγγράφου", "Common.Views.SignDialog.textSelect": "Επιλογή", "Common.Views.SignDialog.textSelectImage": "Επιλογή εικόνας", "Common.Views.SignDialog.textSignature": "Η υπογραφή μοιάζει με", "Common.Views.SignDialog.textTitle": "Υπογραφή εγγράφου", "Common.Views.SignDialog.textUseImage": "ή κάντε κλικ στο 'Επιλογή εικόνας' για να χρησιμοποιήσετε μια εικόνα ως υπογραφή", "Common.Views.SignDialog.textValid": "Έγκυρο από %1 έως %2", "Common.Views.SignDialog.tipFontName": "Όνομα γραμματοσειράς", "Common.Views.SignDialog.tipFontSize": "Μέγ<PERSON><PERSON>ος γραμματοσειράς", "Common.Views.SignSettingsDialog.textAllowComment": "Να επιτρέπεται στον υπογράφοντα να προσθέτει σχόλιο στο διάλογο υπογραφής", "Common.Views.SignSettingsDialog.textDefInstruction": "Πριν υπογράψετε το έγγραφο, βεβαιωθείτε για την ορθότητα των περιεχομένων.", "Common.Views.SignSettingsDialog.textInfoEmail": "e-mail προτεινόμενου υπογράφοντος", "Common.Views.SignSettingsDialog.textInfoName": "Προτεινόμενος υπογράφων", "Common.Views.SignSettingsDialog.textInfoTitle": "Τ<PERSON><PERSON><PERSON><PERSON> προτεινόμενου υπογράφοντος", "Common.Views.SignSettingsDialog.textInstructions": "Οδηγ<PERSON><PERSON>ς προς υπογράφοντα", "Common.Views.SignSettingsDialog.textShowDate": "Εμφάνιση ημερομηνίας υπογραφής στη γραμμή υπογράφοντος", "Common.Views.SignSettingsDialog.textTitle": "Ρύθμιση υπογραφής", "Common.Views.SignSettingsDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Δεκαεξαδική τιμή Unicode", "Common.Views.SymbolTableDialog.textCopyright": "Σήμα πνευματικών δικαιωμάτων", "Common.Views.SymbolTableDialog.textDCQuote": "Κλείσιμο διπλών εισαγωγικών", "Common.Views.SymbolTableDialog.textDOQuote": "Άνοιγμα διπλών εισαγωγικών", "Common.Views.SymbolTableDialog.textEllipsis": "Οριζόντια έλλειψη", "Common.Views.SymbolTableDialog.textEmDash": "Πλατιά παύλα Em", "Common.Views.SymbolTableDialog.textEmSpace": "Διάστημα em", "Common.Views.SymbolTableDialog.textEnDash": "Πλατιά παύλα En", "Common.Views.SymbolTableDialog.textEnSpace": "Διάστημα en", "Common.Views.SymbolTableDialog.textFont": "Γραμματοσειρά", "Common.Views.SymbolTableDialog.textNBHyphen": "Προστατευμένη παύλα", "Common.Views.SymbolTableDialog.textNBSpace": "Προστατευμένο διάστημα", "Common.Views.SymbolTableDialog.textPilcrow": "Σύμβολο παραγράφου", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em διάστημα", "Common.Views.SymbolTableDialog.textRange": "Εύρ<PERSON>", "Common.Views.SymbolTableDialog.textRecent": "Πρόσφατα χρησιμοποιημένα σύμβολα", "Common.Views.SymbolTableDialog.textRegistered": "Σύμβολο καταχωρημένου", "Common.Views.SymbolTableDialog.textSCQuote": "Κλείσιμο απλών εισαγωγικών", "Common.Views.SymbolTableDialog.textSection": "Σύμβολο τμήματος", "Common.Views.SymbolTableDialog.textShortcut": "Πλήκτρο συντόμευσης", "Common.Views.SymbolTableDialog.textSHyphen": "Απαλή παύλα", "Common.Views.SymbolTableDialog.textSOQuote": "Άνοιγμα απλών εισαγωγικών", "Common.Views.SymbolTableDialog.textSpecial": "Ειδικοί χαρακτήρες", "Common.Views.SymbolTableDialog.textSymbols": "Σύμβολα", "Common.Views.SymbolTableDialog.textTitle": "Σύμβολο", "Common.Views.SymbolTableDialog.textTradeMark": "Σύμβολο εμπορικού σήματος", "Common.Views.UserNameDialog.textDontShow": "Να μην ερωτηθώ ξανά", "Common.Views.UserNameDialog.textLabel": "Ετικέτα:", "Common.Views.UserNameDialog.textLabelError": "Η ετικέτα δεν μπορεί να είναι κενή.", "SSE.Controllers.DataTab.strSheet": "Φύλλο", "SSE.Controllers.DataTab.textAddExternalData": "Ο σύνδεσμος προς μια εξωτερική πηγή έχει προστεθεί. Μπορείτε να ενημερώσετε τέτοιους συνδέσμους στην καρτέλα Δεδομένα.", "SSE.Controllers.DataTab.textColumns": "Στήλες", "SSE.Controllers.DataTab.textContinue": "Συνέχεια", "SSE.Controllers.DataTab.textDontUpdate": "Μη γίνεται ενημέρωση", "SSE.Controllers.DataTab.textEmptyUrl": "Πρέπει να ορίσετε μια διεύθυνση URL.", "SSE.Controllers.DataTab.textRows": "Γραμμές", "SSE.Controllers.DataTab.textTurnOff": "Απενεργοποίηση αυτόματης ενημέρωσης", "SSE.Controllers.DataTab.textUpdate": "Ενημέρωση", "SSE.Controllers.DataTab.textWizard": "Κείμενο σε στήλες", "SSE.Controllers.DataTab.txtDataValidation": "Επικύρωση δεδομένων", "SSE.Controllers.DataTab.txtErrorExternalLink": "Σφάλμα: η ενημέρωση απέτυχε", "SSE.Controllers.DataTab.txtExpand": "Επέκταση", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Τα δεδομένα δίπλα στην επιλογή δεν θα διαγραφούν. Θέλετε να επεκτείνετε την επιλογή ώστε να συμπεριλάβει τα παρακείμενα δεδομένα ή να συνεχίσετε μόνο με τα τρέχοντα επιλεγμένα κελιά;", "SSE.Controllers.DataTab.txtExtendDataValidation": "Η επιλογή περιέχει μερικά κελιά χωρίς ρυθμίσεις Επικύρωσης Δεδομένων.<br>Θέλετε να επεκτείνετε την Επικύρωση Δεδομένων και σε αυτά τα κελιά;", "SSE.Controllers.DataTab.txtImportWizard": "Οδηγ<PERSON><PERSON> εισαγωγής κειμένου", "SSE.Controllers.DataTab.txtRemDuplicates": "Αφαίρεση διπλότυπων", "SSE.Controllers.DataTab.txtRemoveDataValidation": "Η επιλογή περιέχει περισσότερους από έναν τύπους επικύρωσης.<br>Διαγραφή τρεχουσών ρυθμίσεων και συνέχεια;", "SSE.Controllers.DataTab.txtRemSelected": "Αφαίρεση στα επιλεγμένα", "SSE.Controllers.DataTab.txtUrlTitle": "Επικόλληση URL δεδομένων", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "Αυτό το βιβλίο εργασίας περιέχει συνδέσεις σε εξωτερικές πηγές που ενημερώνονται αυτόματα. Αυτό μπορεί να μην είναι ασφαλές.<br><br>Εάν τους εμπιστεύεστε, πατήστε Συνέχεια.", "SSE.Controllers.DataTab.warnUpdateExternalData": "Αυτό το βιβλίο εργασίας περιέχει συνδέσεις σε μία ή περισσότερες εξωτερικές προελεύσεις που μπορεί να μην είναι ασφαλείς. <br><PERSON><PERSON><PERSON> εμπιστεύεστε τους συνδέσμους, ενημερώστε τους για να λάβετε τα πιο πρόσφατα δεδομένα.", "SSE.Controllers.DocumentHolder.alignmentText": "Στοίχιση", "SSE.Controllers.DocumentHolder.centerText": "Κέντρο", "SSE.Controllers.DocumentHolder.deleteColumnText": "Διαγρα<PERSON><PERSON> στήλης", "SSE.Controllers.DocumentHolder.deleteRowText": "Διαγραφή Γραμμής", "SSE.Controllers.DocumentHolder.deleteText": "Διαγραφή", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Η παραπομπή του συνδέσμου δεν υπάρχει. Παρακαλούμε διορθώστε τον σύνδεσμο ή διαγράψτε τον.", "SSE.Controllers.DocumentHolder.guestText": "Επισκέπτης", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Στήλη αριστερά", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Στήλη δεξιά", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Γραμμή Από Πάνω", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Γραμμή Από <PERSON>ά<PERSON>ω", "SSE.Controllers.DocumentHolder.insertText": "Εισαγωγή", "SSE.Controllers.DocumentHolder.leftText": "Αριστερά", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Προειδοποίηση", "SSE.Controllers.DocumentHolder.rightText": "Δεξιά", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Επιλ<PERSON><PERSON><PERSON><PERSON> αυτόματης διόρθωσης", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON> στήλης {0} σύμβολα ({1} εικονοστοιχεία)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Ύψος Γραμμής {0} σημεία ({1} εικονοστοιχεία)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Κάντε κλικ στον σύνδεσμο για να ανοίξει ή κάντε κλικ και κρατήστε πατημένο το κουμπί του ποντικιού για να επιλέξετε το κελί.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Εισαγω<PERSON><PERSON> στήλης αριστερά", "SSE.Controllers.DocumentHolder.textInsertTop": "Εισαγωγή γραμμής από πάνω", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Ειδική επικόλληση", "SSE.Controllers.DocumentHolder.textStopExpand": "Διακο<PERSON>ή αυτόματης επέκτασης πινάκων", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Αυτό το στοιχείο τελεί υπό επεξεργασία από άλλο χρήστη.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Πάνω από τον μέσο όρο", "SSE.Controllers.DocumentHolder.txtAddBottom": "Προσθήκη κάτω περιγράμματος", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Προσθήκη γραμμής κλάσματος", "SSE.Controllers.DocumentHolder.txtAddHor": "Προσθήκη οριζόντιας γραμμής", "SSE.Controllers.DocumentHolder.txtAddLB": "Προσθήκη αριστερής κάτω γραμμής", "SSE.Controllers.DocumentHolder.txtAddLeft": "Προσθήκη αριστερού περιγράμματος", "SSE.Controllers.DocumentHolder.txtAddLT": "Προσθήκη αριστερής πάνω γραμμής", "SSE.Controllers.DocumentHolder.txtAddRight": "Προσθήκη δεξιού περιγράμματος", "SSE.Controllers.DocumentHolder.txtAddTop": "Προσθήκη επάνω περιγράμματος", "SSE.Controllers.DocumentHolder.txtAddVer": "Προσθήκη κατακόρυφης γραμμής", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Στοίχιση σε χαρακτήρα", "SSE.Controllers.DocumentHolder.txtAll": "(Όλα)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Επιστρέφει όλα τα περιεχόμενα από τον πίνακα ή από τις καθορισμένες στήλες του πίνακα συμπεριλαμβανομένων των επικεφαλίδων των στηλών, τα δεδομένα και τις συνολικές σειρές", "SSE.Controllers.DocumentHolder.txtAnd": "και", "SSE.Controllers.DocumentHolder.txtBegins": "Αρχίζει με", "SSE.Controllers.DocumentHolder.txtBelowAve": "Κάτω από τον μέσο όρο", "SSE.Controllers.DocumentHolder.txtBlanks": "(Κενά)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Ιδιότητες περιγράμματος", "SSE.Controllers.DocumentHolder.txtBottom": "Κάτω", "SSE.Controllers.DocumentHolder.txtByField": "%1 από %2", "SSE.Controllers.DocumentHolder.txtColumn": "Στήλη", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Στοίχιση στήλης", "SSE.Controllers.DocumentHolder.txtContains": "Περιέχει", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Ο σύνδεσμος αντιγρά<PERSON>ηκε στο πρόχειρο", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Επιστρέφει τα κελιά δεδομένων από τον πίνακα ή τις καθορισμένες στήλες του πίνακα", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Μείωση μεγέθους ορίσματος", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Διαγραφή ορίσματος", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Διαγρα<PERSON>ή χειροκίνητης αλλαγής", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Διαγρα<PERSON>ή χαρακτήρων εγκλεισμού", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Διαγρα<PERSON>ή χαρακτήρων εγκλεισμού και διαχωριστών", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Διαγραφή εξίσωσης", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Διαγρα<PERSON><PERSON> χαρακτήρα", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Διαγραφή ρίζας", "SSE.Controllers.DocumentHolder.txtEnds": "Τελειώνει με", "SSE.Controllers.DocumentHolder.txtEquals": "Ισούται", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Ίσο με το χρώμα κελιού", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Ίσο με το χρώμα γραμματοσειράς", "SSE.Controllers.DocumentHolder.txtExpand": "Επέκταση και ταξινόμηση", "SSE.Controllers.DocumentHolder.txtExpandSort": "Τα δεδομένα δίπλα στην επιλογή δεν θα ταξινομηθούν. Θέλετε να επεκτείνετε την επιλογή ώστε να συμπεριλάβει τα παρακείμενα δεδομένα ή να συνεχίσετε με την ταξινόμηση μόνο των επιλεγμένων κελιών;", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Κάτω", "SSE.Controllers.DocumentHolder.txtFilterTop": "Επάνω", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Αλλαγή σε γραμμικό κλάσμα", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Αλλαγή σε πλάγιο κλάσμα", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Αλλαγή σε σύνθετο κλάσμα", "SSE.Controllers.DocumentHolder.txtGreater": "Μεγαλύτερο από", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Μεγαλύτερο ή ίσο με", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> πάνω από το κείμενο", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> κάτω από το κείμενο", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Επιστρέφει τις επικεφαλίδες των στηλών για τον πίνακα ή τις καθορισμένες στήλες του πίνακα", "SSE.Controllers.DocumentHolder.txtHeight": "Ύψος", "SSE.Controllers.DocumentHolder.txtHideBottom": "Απόκρυψη κάτω περιγράμματος", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Απόκρυψη κάτω ορίου", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Απόκρυψη παρένθεσης που κλείνει", "SSE.Controllers.DocumentHolder.txtHideDegree": "Απόκρυψη πτυχίου", "SSE.Controllers.DocumentHolder.txtHideHor": "Απόκρυψη οριζόντιας γραμμής", "SSE.Controllers.DocumentHolder.txtHideLB": "Απόκρυψη αριστερής κάτω γραμμής", "SSE.Controllers.DocumentHolder.txtHideLeft": "Απόκρυψη αριστερού περιγράμματος", "SSE.Controllers.DocumentHolder.txtHideLT": "Απόκρυψη αριστερής πάνω γραμμής", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Απόκρυψη παρένθεσης που ανοίγει", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Απόκρυψη δέσμευσης θέσης", "SSE.Controllers.DocumentHolder.txtHideRight": "Απόκρυψη δεξιού περιγράμματος", "SSE.Controllers.DocumentHolder.txtHideTop": "Απόκρυψη πάνω περιγράμματος", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Απόκρυψη άνω ορίου", "SSE.Controllers.DocumentHolder.txtHideVer": "Απόκρυψη κατακόρυφης γραμμής", "SSE.Controllers.DocumentHolder.txtImportWizard": "Οδηγ<PERSON><PERSON> εισαγωγής κειμένου", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Αύξηση μεγέθους ορίσματος", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Εισαγωγή ορίσματος μετά", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Εισαγωγή ορίσματος πριν", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Εισαγωγή χειροκίνητης αλλαγής", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Εισαγωγή εξίσωσης μετά", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Εισαγωγή εξίσωσης πριν", "SSE.Controllers.DocumentHolder.txtItems": "αντικείμενα", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Διατήρηση κειμένου μόνο", "SSE.Controllers.DocumentHolder.txtLess": "Μικρότερο από", "SSE.Controllers.DocumentHolder.txtLessEquals": "Μικρότερο από ή ίσο με", "SSE.Controllers.DocumentHolder.txtLimitChange": "Αλλαγή θέσης ορίων", "SSE.Controllers.DocumentHolder.txtLimitOver": "Όριο πάνω από το κείμενο", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Όριο κάτω από το κείμενο", "SSE.Controllers.DocumentHolder.txtLockSort": "Υπάρχουν δεδομένα δίπλα στην επιλογή σας, αλλ<PERSON> δεν έχετε επαρκή δικαιώματα τροποποίησης αυτών των κελιών.<br>Θέλετε να συνεχίσετε με την τρέχουσα επιλογή;", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Προσαρμογή παρενθέσεων στο ύψος των ορισμάτων", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Στοίχιση πίνακα", "SSE.Controllers.DocumentHolder.txtNoChoices": "Δεν υπάρχουν επιλογές για το γέμισμα του κελιού.<br><PERSON><PERSON><PERSON><PERSON> τιμές κειμένου από την στήλη μπορούν να επιλεγούν για αντικατάσταση.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Δεν ξεκινά με", "SSE.Controllers.DocumentHolder.txtNotContains": "Δεν περιέχει", "SSE.Controllers.DocumentHolder.txtNotEnds": "Δεν τελειώνει με", "SSE.Controllers.DocumentHolder.txtNotEquals": "Δεν είναι ίσο με", "SSE.Controllers.DocumentHolder.txtOr": "ή", "SSE.Controllers.DocumentHolder.txtOverbar": "Μπάρα πάνω από κείμενο", "SSE.Controllers.DocumentHolder.txtPaste": "Επικόλληση", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Μαθημα<PERSON>ι<PERSON><PERSON>ς τύπος χωρίς περιγράμματα", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Τύπος + πλάτος στήλης", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Μορφοποίηση προορισμού", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Επικόλληση μορφοποίησης μόνο", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Τύπος + μορφή αριθμού", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Επικόλληση τύπου μόνο", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Τύπος + όλες οι μορφοποιήσεις", "SSE.Controllers.DocumentHolder.txtPasteLink": "Επικόλληση συνδέσμου", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Συνδεδεμένη εικόνα", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Συγχώνευση μορφοποίησης υπό όρους", "SSE.Controllers.DocumentHolder.txtPastePicture": "Εικόνα", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Μορφοποίηση πηγής", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Μετατόπιση", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Τιμή + όλες οι μορφοποιήσεις", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Τιμή + μορφή αριθμού", "SSE.Controllers.DocumentHolder.txtPasteValues": "Επικόλληση τιμής μόνο", "SSE.Controllers.DocumentHolder.txtPercent": "ποσοστό", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Επανάληψη αυτόματης επέκτασης πίνακα", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Αφαίρεση γραμμής κλάσματος", "SSE.Controllers.DocumentHolder.txtRemLimit": "Αφαίρεση ορίου", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Αφαίρεση τονισμένου χαρακτήρα", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Αφαίρεση μπάρας", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Θέλετε να αφαιρέσετε αυτή την υπογραφή;<br>Δεν μπορεί να αναιρεθεί.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Αφαίρεση δεσμών ενεργειών", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Αφαίρεση δείκτη", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Αφαίρεση εκθέτη", "SSE.Controllers.DocumentHolder.txtRowHeight": "Ύψος γραμμής", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Δέσμες ενεργειών μετά το κείμενο", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Δέσμες ενεργειών πριν το κείμενο", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Εμφάνιση κάτω ορίου", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Εμφάνιση δεξιάς παρένθεσης", "SSE.Controllers.DocumentHolder.txtShowDegree": "Εμφάνιση πτυχίου", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Εμφάνιση αριστερής παρένθεσης", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Εμφάνιση δεσμευμένης θέσης", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Εμφάνιση πάνω ορίου", "SSE.Controllers.DocumentHolder.txtSorting": "Ταξινόμηση", "SSE.Controllers.DocumentHolder.txtSortSelected": "Ταξινόμηση επιλεγμένων", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Έκταση παρενθέσεων", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Επίλεξε μόνο αυτή τη σειρά της καθορισμένης στήλης", "SSE.Controllers.DocumentHolder.txtTop": "Επάνω", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Επιστρέφει τις συνολικές σειρές για τον πίνακα ή για τις καθορισμένες στήλες του πίνακα", "SSE.Controllers.DocumentHolder.txtUnderbar": "Μπά<PERSON><PERSON> κάτω από κείμενο", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Αναίρεση αυτόματης έκτασης πίνακα", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Χρήση οδηγού εισαγωγής κειμένου", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Η συσκευή και τα δεδομένα σας μπορεί να κινδυνεύσουν αν κάνετε κλικ σε αυτόν τον σύνδεσμο.<br>Θέλετε σίγουρα να συνεχίσετε;", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.warnFilterError": "Απαιτείτ<PERSON><PERSON> τουλάχιστον ένα πεδίο στην περιοχή τιμών για να εφαρμοστεί ένα φίλτρο τιμών.", "SSE.Controllers.FormulaDialog.sCategoryAll": "Όλα", "SSE.Controllers.FormulaDialog.sCategoryCube": "Κύβος", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Προσαρμογή", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Βάση δεδομένων", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Ημερομηνία και ώρα", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Μηχανική", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Χρηματοοικονομικά", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Πληροφορία", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 τελευταία χρησιμοποιημένες", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Λογική", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Αναζήτηση και παραπομπή", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Μαθηματι<PERSON><PERSON> και τριγωνομετρία", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Στατιστική", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Κείμενο και δεδομένα", "SSE.Controllers.LeftMenu.newDocumentTitle": "Λογιστικ<PERSON> φύλλο χωρίς όνομα", "SSE.Controllers.LeftMenu.textByColumns": "Κατά στήλες", "SSE.Controllers.LeftMenu.textByRows": "Κατά γραμμές", "SSE.Controllers.LeftMenu.textFormulas": "Τύποι", "SSE.Controllers.LeftMenu.textItemEntireCell": "Ολόκληρο το περιεχόμενου κελιού", "SSE.Controllers.LeftMenu.textLoadHistory": "Φόρτωση ιστορικού εκδόσεων...", "SSE.Controllers.LeftMenu.textLookin": "Αναζήτηση σε", "SSE.Controllers.LeftMenu.textNoTextFound": "Τα δεδομένα που αναζητάτε δεν βρέθηκαν. Παρακαλούμε προσαρμόστε τις επιλογές αναζήτησης.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Η αντικατάσταση έγινε. {0} εμφανίσεις προσπεράστηκαν.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Η αναζήτηση ολοκληρώθηκε. Εμφανίσεις που αντικαταστάθηκαν: {0}", "SSE.Controllers.LeftMenu.textSave": "Αποθήκευση", "SSE.Controllers.LeftMenu.textSearch": "Αναζήτηση", "SSE.Controllers.LeftMenu.textSelectPath": "Εισάγετε ένα νέο όνομα για την αποθήκευση του αντιγράφου αρχείου", "SSE.Controllers.LeftMenu.textSheet": "Φύλλο", "SSE.Controllers.LeftMenu.textValues": "Τιμές", "SSE.Controllers.LeftMenu.textWarning": "Προειδοποίηση", "SSE.Controllers.LeftMenu.textWithin": "Εντ<PERSON>ς", "SSE.Controllers.LeftMenu.textWorkbook": "Βιβλίο Εργασίας", "SSE.Controllers.LeftMenu.txtUntitled": "Άτιτλο", "SSE.Controllers.LeftMenu.warnDownloadAs": "Αν προχωρήσετε με την αποθήκευση σε αυτή τη μορφή, όλα τα χαρακτηριστικά πλην του κειμένου θα χαθούν.<br>Θέλετε σίγουρα να συνεχίσετε;", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "Η μορφή CSV δεν υποστηρίζει την αποθήκευση ενός αρχείου πολλών φύλλων.<br>Για να διατηρήσετε την επιλεγμένη μορφή και να αποθηκεύσετε μόνο το τρέχον φύλλο, πατήστε Αποθήκευση.<br>Για να αποθηκεύσετε το τρέχον υπολογιστικό φύλλο, κάντε κλικ στην Ακύρωση και αποθηκεύστε το σε διαφορετική μορφή.", "SSE.Controllers.Main.confirmAddCellWatches": "Αυτή η ενέργεια θα προσθέσει {0} παρακολουθήσεις κελιών. <br>Θέλετε να συνεχίσετε;", "SSE.Controllers.Main.confirmAddCellWatchesMax": "Αυτή η ενέργεια θα προσθέσει μόνο {0} παρακολουθήσεις κελιών λόγω εξοικονόμησης μνήμης. <br>Θέλετε να συνεχίσετε;", "SSE.Controllers.Main.confirmMaxChangesSize": "Το μέγεθος των ενεργειών υπερβαίνει τον περιορισμό που έχει οριστεί για τον διακομιστή σας.<br>Πατ<PERSON><PERSON><PERSON><PERSON> \"Αναίρεση\" για να ακυρώσετε την τελευταία σας ενέργεια ή πατήστε \"Συνέχεια\" για να διατηρήσετε την ενέργεια τοπικά (πρέπει να κατεβάσετε το αρχείο ή να αντιγράψετε το περιεχόμενό του για να βεβαιωθείτε ότι δεν θα χαθεί τίποτα).", "SSE.Controllers.Main.confirmMoveCellRange": "Το εύρος κελιών προορισμού περιέχει δεδομένα. Να συνεχιστεί η λειτουργία;", "SSE.Controllers.Main.confirmPutMergeRange": "Τα δεδομένα προέλευσης περιείχαν συγχωνευμένα κελιά.<br>Αυτά διαιρέθηκαν πριν την επικόλλησή τους στον πίνακα.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Οι τύποι στη γραμμή κεφαλίδας θα αφαιρεθούν και θα μετατραπούν σε στατικό κείμενο.<br>Θέλετε να συνεχίσετε;", "SSE.Controllers.Main.confirmReplaceHFPicture": "Μόνο μία εικόνα μπορεί να εισαχθεί σε κάθε ενότητα της κεφαλίδας. <br>Πατήστε \"Αντικατάσταση\" για να αντικαταστήσετε την υπάρχουσα εικόνα. <br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> \"Διατήρηση\" για να διατηρήσετε την υπάρχουσα εικόνα.", "SSE.Controllers.Main.convertationTimeoutText": "Υπέρβαση χρονικού ορίου μετατροπής.", "SSE.Controllers.Main.criticalErrorExtText": "Πατήστ<PERSON> \"Εντάξει\" για να επιστρέψετε στη λίστα εγγράφων.", "SSE.Controllers.Main.criticalErrorTitle": "Σφάλμα", "SSE.Controllers.Main.downloadErrorText": "Αποτυχία λήψης.", "SSE.Controllers.Main.downloadTextText": "Γίνεται λήψη υπολογιστικού φύλλου...", "SSE.Controllers.Main.downloadTitleText": "Λήψη υπολογιστικού φύλλου", "SSE.Controllers.Main.errNoDuplicates": "Δεν βρέθηκαν διπλότυπες τιμές.", "SSE.Controllers.Main.errorAccessDeny": "Προσπαθείτε να εκτελέσετε μια ενέργεια για την οποία δεν έχετε δικαιώματα.<br>Παρακαλούμε να επικοινωνήστε με τον διαχειριστή του διακομιστή εγγράφων.", "SSE.Controllers.Main.errorArgsRange": "Υπάρχει σφάλμα στον καταχωρημένο τύπο.<br>Χρησιμοποιείται εσφαλμένο εύρος ορίσματος.", "SSE.Controllers.Main.errorAutoFilterChange": "Η λειτουργία δεν επιτρέπεται, καθ<PERSON><PERSON> επιχειρεί να μετατοπίσει κελιά σε έναν πίνακα στο φύλλο εργασίας σας.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Δεν ήταν δυνατή η πραγματοποίηση της λειτουργίας για τα επιλεγμένα κελιά, καθώς δεν μπορείτε να μετακινήσετε ένα μέρος του πίνακα.<br>Επιλέξτε μια άλλη περιοχή δεδομένων, ώστε ολόκληρος ο πίνακας να μετατοπιστεί και να δοκιμάσετε ξανά.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Δεν ήταν δυνατή η εκτέλεση της επιλεγμένης περιοχής κελιών.<br>Επιλέξτε ένα ομοιόμορφο εύρος δεδομένων διαφορετικό από το υπάρχον και δοκιμάστε ξανά.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Δεν είναι δυνατή η εκτέλεση της λειτουργίας, επειδή η περιοχή περιέχει φιλτραρισμένα κελιά.<br>Παρακαλούμε εμφανίστε τα φιλτραρισμένα στοιχεία και δοκιμάστε ξανά.", "SSE.Controllers.Main.errorBadImageUrl": "Εσφαλμένη διεύθυνση URL εικόνας", "SSE.Controllers.Main.errorCalculatedItemInPageField": "Το στοιχείο δεν μπορεί να προστεθεί ή να τροποποιηθεί. Η αναφορά Συγκεντρωτικού Πίνακα έχει αυτό το πεδίο στα Φίλτρα.", "SSE.Controllers.Main.errorCannotPasteImg": "Δεν μπορούμε να επικολλήσουμε αυτήν την εικόνα από το Πρόχειρο, αλλά μπορείτε να την αποθηκεύσετε στη συσκευή σας και να το εισάγετε από εκεί ή μπορείτε να αντιγράψετε την εικόνα χωρίς κείμενο και να την επικολλήσετε στο υπολογιστικό φύλλο.", "SSE.Controllers.Main.errorCannotUngroup": "Δεν είναι δυνατή η αφαίρεση ομαδοποίησης. Για να ξεκινήσετε μια ομάδα, επιλέξτε τις γραμμές ή στήλες λεπτομερειών και ομαδοποιήστε τες. ", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Δεν μπορείτε να χρησιμοποιήσετε αυτή την εντολή σε προστατευμένο φύλλο. Για να χρησιμοποιήσετε αυτή την εντολή, αφαιρέστε την προστασία.<br>Εν<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> να σας ζητηθεί να εισάγετε συνθηματικό.", "SSE.Controllers.Main.errorChangeArray": "Δεν μπορείτε να αλλάξετε μέρος ενός πίνακα.", "SSE.Controllers.Main.errorChangeFilteredRange": "Αυτό θα αλλάξει ένα φιλτραρισμένο εύρος στο φύλλο εργασίας.<br>Γ<PERSON>α να ολοκληρώσετε αυτή την εργασία, παρακαλούμε αφαιρέστε τα Αυτόματα Φίλτρα.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "Το κελί ή γράφημα που προσπαθείτε να αλλάξετε βρίσκεται σε προστατευμένο φύλλο.<br>Γ<PERSON><PERSON> να κάνετε αλλαγή, αφαιρέστε την προστασία. Ίσως σας ζητηθεί συνθηματικό.", "SSE.Controllers.Main.errorCircularReference": "Υπάρχουν μία ή περισσότερες κυκλικές αναφορές όπου ένας τύπος αναφέρεται στο δικό του κελί είτε άμεσα είτε έμμεσα.<br> Προσπαθήστε να αφαιρέσετε ή να αλλάξετε αυτές τις αναφορές ή να μετακινήσετε τους τύπους σε διαφορετικά κελιά.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Η σύνδεση διακομιστή χάθηκε. Δεν είναι δυνατή η επεξεργασία του εγγράφου αυτήν τη στιγμή.", "SSE.Controllers.Main.errorConnectToServer": "Δεν ήταν δυνατή η αποθήκευση του εγγράφου. Παρακαλούμε ελέγξτε τις ρυθμίσεις σύνδεσης ή επικοινωνήστε με τον διαχειριστή σας.<br>Όταν κάνετε κλικ στο κουμπί «OK», θα σας ζητηθεί να πραγματοποιήσετε λήψη του εγγράφου.", "SSE.Controllers.Main.errorConvertXml": "Το αρχείο έχει μορφή που δεν υποστηρίζεται. <br>Μπ<PERSON>ρεί να χρησιμοποιηθεί μόνο η μορφή υπολογιστικού φύλλου XML 2003.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Αυτή η εντολή δεν μπορεί να χρησιμοποιηθεί με πολλές επιλογές.<br>Επιλέξτε ένα εύρος και δοκιμάστε ξανά.", "SSE.Controllers.Main.errorCountArg": "Υπάρχει σφάλμα στον καταχωρημένο τύπο.<br>Χρησιμοποιείται εσφαλμένος αριθμός ορισμάτων.", "SSE.Controllers.Main.errorCountArgExceed": "Υπάρχει σφάλμα καταχωρημένο τύπο.<br>Έγινε υπέρβαση του αριθμού των ορισμάτων.", "SSE.Controllers.Main.errorCreateDefName": "Δεν είναι δυνατή η επεξεργασία των υφιστάμενων επώνυμων ευρών και δεν είναι δυνατή η δημιουργία νέων<br>αυτ<PERSON><PERSON> τη στιγμή, καθ<PERSON><PERSON> ορισμένα από αυτά τελούν υπό επεξεργασία.", "SSE.Controllers.Main.errorCreateRange": "Δεν είναι δυνατή η επεξεργασία των υπαρχόντων περιοχών και δεν είναι δυνατή η δημιουργία νέων προς<br>το παρόν, καθώς ορισμένες από αυτές επεξεργάζονται.", "SSE.Controllers.Main.errorDatabaseConnection": "Εξωτερι<PERSON><PERSON> σφάλμα.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> σύνδεσης βάσης δεδομένων. Παρακαλούμε επικοινωνήστε με την υποστήριξη σε περίπτωση που το σφάλμα παραμένει.", "SSE.Controllers.Main.errorDataEncrypted": "Οι κρυπτογραφημένες αλλαγ<PERSON>ς έχουν ληφθεί, δεν μπορούν να αποκρυπτογραφηθούν.", "SSE.Controllers.Main.errorDataRange": "Εσφαλμένο εύρος δεδομένων.", "SSE.Controllers.Main.errorDataValidate": "Η τιμή που εισαγάγατε δεν είναι έγκυρη.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> χρήστης έχει περιορίσει τις δυνατές τιμές σε αυτό το κελί.", "SSE.Controllers.Main.errorDefaultMessage": "Κωδικός σφάλματος: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Προσπαθείτε να διαγράψετε μια στήλη που περιέχει κλειδωμένο κελί. Τα κλειδωμένα κελιά δεν μπορούν να διαγραφούν όσο το φύλλο εργασίας προστατεύεται.<br>Για να διαγράψετε ένα κλειδωμένο κελί, αφαιρέστε την προστασία φύλλου. Ίσως σας ζητηθεί συνθηματικό.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Προσπαθείτε να διαγράψετε μια γραμμή που περιέχει κλειδωμένο κελί. Τα κλειδωμένα κελιά δεν μπορούν να διαγραφούν όσο το φύλλο εργασίας προστατεύεται.<br>Για να διαγράψετε ένα κλειδωμένο κελί, αφαιρέστε την προστασία φύλλου. Ίσως σας ζητηθεί συνθηματικό.", "SSE.Controllers.Main.errorDependentsNoFormulas": "Η εντολή \"Ανίχνευση εξαρτημένων\" δεν εντόπισε τύπους που να αναφέρονται στο ενεργό κελί.", "SSE.Controllers.Main.errorDirectUrl": "Παρακαλούμε επιβεβαιώστε τον σύνδεσμο προς το έγγραφο.<br>Αυτ<PERSON><PERSON> ο σύνδεσμος πρέπει να είναι άμεσος σύνδεσμος προς το αρχείο για κατέβασμα.", "SSE.Controllers.Main.errorEditingDownloadas": "Παρουσ<PERSON>ά<PERSON>τηκε σφάλμα κατά την εργασία με το έγγραφο.<br>Χρησιμοποιήστε την επιλογή «Λήψη ως» για να αποθηκεύσετε το αντίγραφο ασφαλείας στον σκληρό δίσκο του υπολογιστή σας.", "SSE.Controllers.Main.errorEditingSaveas": "Παρουσιάστηκε σφάλμα κατά την εργασία με το έγγραφο.<br>Χρησιμοποιήστε την επιλογή «Αποθήκευση ως...» για να αποθηκεύσετε το αντίγραφο ασφαλείας στον σκληρό δίσκο του υπολογιστή σας.", "SSE.Controllers.Main.errorEditView": "Η υφιστάμενη όψη φύλλου δεν μπορεί να τροποποιηθεί και οι νέες δεν μπορούν να δημιουργηθούν αυτή τη στιγμή καθώς κάποιες από αυτές τελούν υπό επεξεργασία.", "SSE.Controllers.Main.errorEmailClient": "Δε βρέθηκε καμιά εφαρμογή ηλεκτρονικού ταχυδρομείου.", "SSE.Controllers.Main.errorFilePassProtect": "Το αρχεί<PERSON> προστατεύεται με συνθηματικό και δεν μπορεί να ανοίξει.", "SSE.Controllers.Main.errorFileRequest": "Εξωτερι<PERSON><PERSON> σφάλμα.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αιτήματος αρχείου. Επικοινωνήστε με την υποστήριξη σε περίπτωση που το σφάλμα παραμένει.", "SSE.Controllers.Main.errorFileSizeExceed": "Το μέγεθος του αρχείου υπερβαίνει το όριο που έχει οριστεί για τον διακομιστή σας.<br>Παρακαλούμε επικοινωνήστε με τον διαχειριστή του Εξυπηρετητή Εγγράφων για λεπτομέρειες.", "SSE.Controllers.Main.errorFileVKey": "Εξωτερικ<PERSON> σφάλμα.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>νο κλειδί ασφαλείας. Παρακαλούμε επικοινωνήστε με την υποστήριξη σε περίπτωση που το σφάλμα παραμένει.", "SSE.Controllers.Main.errorFillRange": "Δεν ήταν δυνατή η συμπλήρωση του επιλεγμένου εύρους κελιών.<br>Όλα τα συγχωνευμένα κελιά πρέπει να έχουν το ίδιο μέγεθος.", "SSE.Controllers.Main.errorForceSave": "Παρουσ<PERSON><PERSON><PERSON><PERSON>ηκε σφάλμα κατά την αποθήκευση του αρχείου. Χρησιμοποιήστε την επιλογή «Λήψη ως» για να αποθηκεύσετε το αρχείο στον σκληρό δίσκο του υπολογιστή σας ή δοκιμάστε ξανά αργότερα.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Δεν είναι δυνατή η εισαγωγή τύπου για όνομα στοιχείου ή πεδίου σε μια αναφορά συγκεντρωτικού πίνακα.", "SSE.Controllers.Main.errorFormulaName": "Υπάρχει σφάλμα στον καταχωρημένο τύπο.<br>Χρησιμοποιείται εσφαλμένο όνομα τύπου.", "SSE.Controllers.Main.errorFormulaParsing": "Εσωτερι<PERSON><PERSON> σφάλμα κατά την ανάλυση του τύπου.", "SSE.Controllers.Main.errorFrmlMaxLength": "Ο μαθηματικός τύπος ξεπερνά το όριο των 8192 χαρακτήρων.<br>Παρακαλούμε επεξεργαστείτε τον και δοκιμάστε ξανά.", "SSE.Controllers.Main.errorFrmlMaxReference": "Δεν μπορείτε να εισαγάγετε αυτόν τον τύπο επειδή έχει πάρα πολλές τιμές,<br>αναφορές κελιού ή/και ονόματα.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Οι τιμές κειμένου στους τύπους περιορίζονται σε 255 χαρακτήρες.<br>Χρησιμοποιήστε τη συνάρτηση CONCATENATE ή τον τελεστή συνένωσης (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Η συνάρτηση αναφέρεται σε ένα φύλλο που δεν υπάρχει.<br>Παρακαλούμε ελέγξτε τα δεδομένα και δοκιμάστε ξανά.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Δεν ήταν δυνατή η ολοκλήρωση της λειτουργίας για το επιλεγμένο εύρος κελιών.<br>Ε<PERSON><PERSON>λ<PERSON>ξτε ένα εύρος ώστε η πρώτη γραμμή του πίνακα να είναι στην ίδια γραμμή<br>και ο παραγόμενος πίνακας να επικαλύπτει τον τρέχοντα.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Δεν ήταν δυνατή η ολοκλήρωση της λειτουργίας για το επιλεγμένο εύρος κελιών.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τε ένα εύρος που δεν περιλαμβάνει άλλους πίνακες.", "SSE.Controllers.Main.errorInconsistentExt": "Προέκυψε σφάλμα κατά το άνοιγμα του αρχείου.<br>Τα περιεχόμενα του αρχείου δεν αντιστοιχούν στην κατάληξή του ονόματός του.", "SSE.Controllers.Main.errorInconsistentExtDocx": "Προέκυψε σφάλμα κατά το άνοιγμα του αρχείου.<br>Τα περιεχόμενα του αρχείου αντιστοιχούν σε αρχεία κειμένου (π.χ. docx), αλ<PERSON><PERSON> το αρχείο έχει ασύμφωνη κατάληξη: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "Προέκυψε σφάλμα κατά το άνοιγμα του αρχείου.<br>Τα περιεχόμενα του αρχείου αντιστοιχούν σε μια από τις ακόλουθες μορφές: pdf/djvu/xps/oxps, αλλά το αρχείο έχει ασύμφωνη κατάληξη: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "Προέκυψε σφάλμα κατά το άνοιγμα του αρχείου.<br>Τα περιεχόμενα του αρχείου αντιστοιχούν σε παρουσιά<PERSON>εις (π.χ. pptx), αλλά το αρχείο έχει ασύμφωνη κατάληξη: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "Προέκυψε σφάλμα κατά το άνοιγμα του αρχείου.<br>Τα περιεχόμενα του αρχείου αντιστοιχούν σε υπολογιστ<PERSON><PERSON><PERSON> φύλλα (π.χ. xlsx), αλλ<PERSON> το αρχείο έχει ασύμφωνη κατάληξη: %1.", "SSE.Controllers.Main.errorInvalidRef": "Εισάγετε ένα σωστό όνομα για την επιλογή ή μια έγκυρη αναφορά για να μεταβείτε.", "SSE.Controllers.Main.errorKeyEncrypt": "Άγνωστος περιγραφέας κλειδιού", "SSE.Controllers.Main.errorKeyExpire": "Ο περιγραφέας κλειδιού έχει λήξει", "SSE.Controllers.Main.errorLabledColumnsPivot": "Για να δημιουργηθεί συγκεντρωτικ<PERSON>ς πίνακας, χρησιμοποιήστε δεδομένα οργανωμένα ως λίστα στηλών με ετικέτες.", "SSE.Controllers.Main.errorLoadingFont": "Οι γραμματοσειρές δεν έχουν φορτωθεί.<br>Παρα<PERSON><PERSON>λ<PERSON>ύμε επικοινωνήστε με τον διαχειριστή του Εξυπηρετητή Εγγράφων σας.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Η αναφορά προορισμού ή εύρους δεδομένων δεν είναι έγκυρη.", "SSE.Controllers.Main.errorLockedAll": "Η λειτουργία δεν μπόρεσε να γίνει καθώς το φύλλο έχει κλειδωθεί από άλλο χρήστη.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "Ένα από τα κελιά που εμπλέκονται στη διαδικασία αναζήτησης στόχου έχει τροποποιηθεί από άλλο χρήστη.", "SSE.Controllers.Main.errorLockedCellPivot": "Δεν είναι δυνατή η τροποποίηση δεδομένων εντός ενός συγκεντρωτικού πίνακα", "SSE.Controllers.Main.errorLockedWorksheetRename": "Το φύλλο δεν μπορεί να μετονομαστεί προς το παρόν καθώς μετονομάζεται από άλλο χρήστη", "SSE.Controllers.Main.errorMaxPoints": "Ο μέγιστος αριθμός σημείων σε σειρά ανά γράφημα είναι 4096.", "SSE.Controllers.Main.errorMoveRange": "Αδυναμία αλλαγής μέρους ενός συγχωνευμένου κελιού", "SSE.Controllers.Main.errorMoveSlicerError": "Οι αναλυτές πίνακα δεν μπορούν να αντιγραφούν από ένα βιβλίο εργασίας σε άλλο.<br>Προσπαθήστε ξανά επιλέγοντας ολόκληρο τον πίνακα και τους αναλυτές.", "SSE.Controllers.Main.errorMultiCellFormula": "Οι τύποι συστοιχιών πολλών κελιών δεν επιτρέπονται στους πίνακες.", "SSE.Controllers.Main.errorNoDataToParse": "Δεν επιλέχτηκαν δεδομένα για επεξεργασία.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "Εάν ένας ή περισσότεροι Συγκεντρωτικοί Πίνακες έχουν υπολογιζόμενα στοιχεία, καν<PERSON><PERSON><PERSON> πεδίο δεν μπορεί να χρησιμοποιηθεί στην περιοχή δεδομένων δύο ή περισσότερες φορές ή στην περιοχή δεδομένων και σε άλλη περιοχή ταυτόχρονα.", "SSE.Controllers.Main.errorOpenWarning": "Το μήκος ενός από τους τύπους στο αρχείο ξεπέρασε<br>τον επιτρεπόμενο αριθμό 8192 χαρακτήρων και αφαιρέθηκε.", "SSE.Controllers.Main.errorOperandExpected": "Η σύνταξη της εισαγόμενης συνάρτησης δεν είναι σωστή. Παρακαλούμε ελέγξτε αν λείπει μία από τις παρενθέσεις - «(» ή «)».", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "Το συνθηματικό που βάλατε δεν είναι σωστό.<br>Βεβαιωθείτε ότι το πλήκτρο CAPS LOCK είναι ανενεργό και ότι βάζετε κεφαλαία όπου χρειάζεται.", "SSE.Controllers.Main.errorPasteInPivot": "Δεν μπορούμε να κάνουμε αυτήν την αλλαγή για τα επιλεγμένα κελιά, επειδή θα επηρεάσει έναν συγκεντρωτικ<PERSON> πίνακα.<br>Χρησιμοποιήστε τη λίστα πεδίων για να αλλάξετε την αναφορά.", "SSE.Controllers.Main.errorPasteMaxRange": "Η περιοχή αντιγραφής και επικόλλησης δεν ταιριάζει.<br>Παρακαλούμε επιλέξτε μια περιοχή με το ίδιο μέγεθος ή κάντε κλικ στο πρώτο κελί της σειράς για να επικολλήσετε τα αντιγραμμένα κελιά.", "SSE.Controllers.Main.errorPasteMultiSelect": "Αυτή η ενέργεια δεν μπορεί να γίνει σε μια επιλογή πολλαπλών εύρους.<br>Επιλέξτε ένα μόνο εύρος και δοκιμάστε ξανά.", "SSE.Controllers.Main.errorPasteSlicerError": "Οι αναλυτές πίνακα δεν μπορούν να αντιγραφούν από ένα βιβλίο εργασίας σε άλλο.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Το όνομα του πεδίου συγκεντρωτικού πίνακα υπάρχει ήδη.", "SSE.Controllers.Main.errorPivotGroup": "Δεν είναι δυνατή η ομαδοποίηση αυτής της επιλογής.", "SSE.Controllers.Main.errorPivotOverlap": "Μια αναφορ<PERSON> συγκεντρωτικού πίνακα δεν μπορεί να επικαλύπτει έναν πίνακα.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Η αναφορά Συγκεντρωτικού Πίνακα αποθηκεύτηκε χωρίς τα υποκείμενα δεδομένα.<br>Χρησιμοποιήστε το κουμπί \"Ανανέωση\" για να ενημερώσετε την αναφορά.", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "Η εντολή \"Ανίχνευση προηγουμένων\" απαιτεί το ενεργό κελί να περιέχει έναν τύπο που περιλαμβάνει έγκυρες αναφορές.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Δυστυχ<PERSON><PERSON>, δεν είναι δυνατή η εκτύπωση περισσότερων από 1500 σελίδων ταυτόχρονα στην τρέχουσα έκδοση του προγράμματος.<br><PERSON><PERSON><PERSON><PERSON><PERSON> ο περιορισμός θα καταργηθεί στις επερχόμενες εκδόσεις.", "SSE.Controllers.Main.errorProcessSaveResult": "Αποτυχία αποθήκευσης", "SSE.Controllers.Main.errorProtectedRange": "Αυτό το εύρος δεν επιτρέπεται για επεξεργασία.", "SSE.Controllers.Main.errorSaveWatermark": "Αυτό το αρχείο περιέχει μια εικόνα υδατογραφήματος συνδεδεμένη με άλλο τομέα.<br>Για να γίνει ορατή σε PDF, ενημερώστε την εικόνα υδατογραφήματος ώστε να συνδέεται από τον ίδιο τομέα με το έγγραφό σας ή ανεβάστε την από τον υπολογιστή σας.", "SSE.Controllers.Main.errorServerVersion": "Αναβαθμίστηκε η έκδοση του συντάκτη. Η σελίδα θα φορτωθεί ξανά για να εφαρμοστούν οι αλλαγές.", "SSE.Controllers.Main.errorSessionAbsolute": "Η περίοδος επεξεργασίας εγγράφων έχει λήξει. Παρακαλούμε φορτώστε ξανά τη σελίδα.", "SSE.Controllers.Main.errorSessionIdle": "Το έγγραφο δεν έχει επεξεργα<PERSON>τε<PERSON> εδώ και πολύ ώρα. Παρακαλούμε φορτώστε ξανά τη σελίδα.", "SSE.Controllers.Main.errorSessionToken": "Η σύνδεση με το διακομιστή έχει διακοπεί. Παρακαλούμε φορτώστε ξανά τη σελίδα.", "SSE.Controllers.Main.errorSetPassword": "Δεν ήταν δυνα<PERSON><PERSON>ς ο ορισμός του συνθηματικού.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Η αναφορά θέσης δεν είναι έγκυρη επειδή τα κελιά δε βρίσκονται όλα στην ίδια γραμμή ή στήλη.<br>Επιλέξτε κελιά που βρίσκονται όλα σε μια γραμμή ή στήλη.", "SSE.Controllers.Main.errorStockChart": "Λανθασμένη διάταξη γραμμών. Για να δημιουργήσετε ένα γράφημα μετοχών τοποθετήστε τα δεδομένα στο φύλλο με την ακόλουθη σειρά:<br>τιμή ανοίγματος, μέγιστη τιμή, ελάχιστη τιμή, τιμή κλεισίματος.", "SSE.Controllers.Main.errorToken": "Το κλειδί ασφαλείας του εγγράφου δεν είναι σωστά σχηματισμένο.<br>Παρακαλούμε επικοινωνήστε με τον διαχειριστή του Εξυπηρετητή Εγγράφων.", "SSE.Controllers.Main.errorTokenExpire": "Το κλειδί ασφαλείας του εγγράφου έληξε.<br>Παρακαλούμε επικοινωνήστε με τον διαχειριστή του Εξυπηρετητή Εγγράφων.", "SSE.Controllers.Main.errorUnexpectedGuid": "Εξωτερικ<PERSON> σφάλμα.<br>Μη αναμενόμενο GUID. Παρακαλούμε επικοινωνήστε με την υποστήριξη σε περίπτωση που το σφάλμα παραμένει.", "SSE.Controllers.Main.errorUpdateVersion": "Η έκδοση του αρχείου έχει αλλάξει. Η σελίδα θα φορτωθεί ξανά.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Η σύνδεση στο Διαδίκτυο έχει αποκατασταθεί και η έκδοση του αρχείου έχει αλλάξει.<br>Προτού συνεχίσετε να εργάζεστε, πρέπει να κατεβάσετε το αρχείο ή να αντιγράψετε το περιεχόμενό του για να βεβαιωθείτε ότι δεν έχει χαθεί τίποτα. Στη συνέχεια, φορτώστε ξανά αυτή τη σελίδα.", "SSE.Controllers.Main.errorUserDrop": "Δεν είναι δυνατή η πρόσβαση στο αρχείο αυτή τη στιγμή.", "SSE.Controllers.Main.errorUsersExceed": "Υπέρβαση του αριθμού των χρηστών που επιτρέπονται από το πρόγραμμα τιμολόγησης", "SSE.Controllers.Main.errorViewerDisconnect": "Η σύνδεση χάθηκε. Μπορείτε να συνεχίσετε να βλέπετε το έγγραφο,<br>αλλά δεν θα μπορείτε να το λάβετε ή να το εκτυπώσετε έως ότου αποκατασταθεί η σύνδεση και ανανεωθεί η σελίδα.", "SSE.Controllers.Main.errorWrongBracketsCount": "Σφάλμα στον καταχωρημένο τύπο.<br>Χρησιμοποιείται λανθασμένος αριθμός αγκυλών.", "SSE.Controllers.Main.errorWrongOperator": "Υπάρχει σφάλμα στον καταχωρημένο τύπο. Χρησιμοποιείται λανθασμένος τελεστής.<br>Παρακαλούμε διορθώστε το σφάλμα.", "SSE.Controllers.Main.errorWrongPassword": "Το συνθηματικό που βάλατε δεν είναι σωστό.", "SSE.Controllers.Main.errRemDuplicates": "Διπλότυπες τιμές που βρέθηκαν και διαγράφηκαν: {0}, μοναδικές τιμές που απέμειναν: {1}.", "SSE.Controllers.Main.leavePageText": "Έχετε μη αποθηκευμένες αλλαγές στο λογιστικό φύλλο. Πατήστε 'Παραμονή στη Σελίδα' και μετά 'Αποθήκευση' για να τις αποθηκεύσετε. Πατήστε 'Έξοδος από τη Σελίδα' για να απορρίψετε όλες τις μη αποθηκευμένες αλλαγές.", "SSE.Controllers.Main.leavePageTextOnClose": "Όλες οι μη αποθηκευμένες αλλαγές στο λογιστικό φύλλο θα χαθούν.<br>Πατή<PERSON>τ<PERSON> \"Ακύρωση\" και μετά \"Αποθήκευση\" για να τις αποθηκεύσετε. Πατήστε \"Εντάξει\" για να τις απορρίψετε.", "SSE.Controllers.Main.loadFontsTextText": "Γίνεται φόρτωση δεδομένων...", "SSE.Controllers.Main.loadFontsTitleText": "Γίνεται φόρτωση δεδομένων", "SSE.Controllers.Main.loadFontTextText": "Γίνεται φόρτωση δεδομένων...", "SSE.Controllers.Main.loadFontTitleText": "Γίνεται φόρτωση δεδομένων", "SSE.Controllers.Main.loadImagesTextText": "Γίνεται φόρτωση εικόνων...", "SSE.Controllers.Main.loadImagesTitleText": "Γίνεται φόρτωση εικόνων", "SSE.Controllers.Main.loadImageTextText": "Γίνεται φόρτωση εικόνας...", "SSE.Controllers.Main.loadImageTitleText": "Γίνεται φόρτωση εικόνας", "SSE.Controllers.Main.loadingDocumentTitleText": "Γίνετα<PERSON> φόρτωση υπολογιστικού φύλλου", "SSE.Controllers.Main.notcriticalErrorTitle": "Προειδοποίηση", "SSE.Controllers.Main.openErrorText": "Παρου<PERSON><PERSON><PERSON><PERSON>τηκε σφάλμα κατά το άνοιγμα του αρχείου.", "SSE.Controllers.Main.openTextText": "Άνοιγμα υπολογιστικού φύλλου...", "SSE.Controllers.Main.openTitleText": "Άνοιγμα υπολογιστικού φύλλου", "SSE.Controllers.Main.pastInMergeAreaError": "Αδυναμία αλλαγής μέρους ενός συγχωνευμένου κελιού", "SSE.Controllers.Main.printTextText": "Εκτύπωση υπολογιστικού φύλλου...", "SSE.Controllers.Main.printTitleText": "Εκτύπωση υπολογιστικού φύλλου", "SSE.Controllers.Main.reloadButtonText": "Επαναφόρτωση σελίδας", "SSE.Controllers.Main.requestEditFailedMessageText": "<PERSON><PERSON><PERSON><PERSON><PERSON>ος επεξ<PERSON><PERSON>γ<PERSON>ζ<PERSON><PERSON>α<PERSON> αυτό το έγγραφο αυτήν τη στιγμή. Παρακαλούμε δοκιμάστε ξανά αργότερα.", "SSE.Controllers.Main.requestEditFailedTitleText": "Δεν επιτρέπεται η πρόσβαση", "SSE.Controllers.Main.saveErrorText": "Παρου<PERSON><PERSON><PERSON><PERSON><PERSON>η<PERSON>ε σφάλμα κατά την αποθήκευση του αρχείου.", "SSE.Controllers.Main.saveErrorTextDesktop": "Δεν είναι δυνατή η αποθήκευση ή η δημιουργία αυτού του αρχείου.<br>Πιθανοί λόγοι είναι:<br>1. Το αρχείο είναι μόνο για ανάγνωση.<br>2. Το αρχείο τελεί υπό επεξεργασία από άλλους χρήστες.<br>3. Ο δίσκος είναι γεμάτος ή κατεστραμμένος.", "SSE.Controllers.Main.saveTextText": "Γίνεται αποθήκευση υπολογιστικού φύλλου...", "SSE.Controllers.Main.saveTitleText": "Αποθήκευση υπολογιστικού φύλλου", "SSE.Controllers.Main.scriptLoadError": "Η σύνδεση είναι πολύ αργή, δεν ήταν δυνατή η φόρτωση ορισμένων στοιχείων. Παρακαλούμε φορτώστε ξανά τη σελίδα.", "SSE.Controllers.Main.textAnonymous": "Ανώνυμος", "SSE.Controllers.Main.textApplyAll": "Εφαρμογή σε όλες τις εξισώσεις", "SSE.Controllers.Main.textBuyNow": "Επισκεφθείτε τον ιστότοπο", "SSE.Controllers.Main.textChangesSaved": "Όλες οι αλλαγές αποθηκεύτηκαν", "SSE.Controllers.Main.textClose": "Κλείσιμο", "SSE.Controllers.Main.textCloseTip": "Κάντε κλικ για να κλείσει η υπόδειξη", "SSE.Controllers.Main.textConfirm": "Επιβεβαίωση", "SSE.Controllers.Main.textConnectionLost": "Προσπάθεια σύνδεσης. Ελέγξτε τις ρυθμίσεις σύνδεσης.", "SSE.Controllers.Main.textContactUs": "Επικοινωνήστε με το τμήμα πωλήσεων", "SSE.Controllers.Main.textContinue": "Συνέχεια", "SSE.Controllers.Main.textConvertEquation": "Η εξίσωση αυτή δημιουργήθηκε με παλαιότερη έκδοση του συντάκτη εξισώσεων που δεν υποστηρίζεται πια. Για να την επεξεργαστείτε, μετατρέψτε την σε μορφή Office Math ML.<br>Να μετατραπεί τώρα;", "SSE.Controllers.Main.textCustomLoader": "Παρακαλούμε λάβετε υπόψη ότι σύμφωνα με τους όρους της άδειας δεν δικαιούστε αλλαγή του φορτωτή.<br>Π<PERSON><PERSON><PERSON><PERSON><PERSON>λούμε επικοινωνήστε με το Τμήμα Πωλήσεων για να λάβετε μια προσφορά.", "SSE.Controllers.Main.textDisconnect": "Η σύνδεση χάθηκε", "SSE.Controllers.Main.textFillOtherRows": "Γέμισμα υπόλοιπων γραμμών", "SSE.Controllers.Main.textFormulaFilledAllRows": "Ο μαθηματικός τύπος γέμισε {0} γραμμές με δεδομένα. Το γέμισμα των υπόλοιπων γραμμών ίσως χρειαστεί μερικά λεπτά.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Ο μαθηματικός τύπος γέμισε τις {0} πρώτες γραμμές. Το γέμισμα των υπόλοιπων άδειων γραμμών ίσως χρειαστεί μερικά λεπτά.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Ο μαθηματικός τύπος γέμισε μόνο τις {0} πρώτες γραμμές με δεδομένα για λόγους εξοικονόμησης μνήμης. Υπάρχουν άλλες {1} γραμμές με δεδομένα. Μπορείτε να τις γεμίσετε χειρονακτικά.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Ο μαθηματικός τύπος γέμισε μόνο τις {0} πρώτες γραμμές για λόγους εξοικονόμησης μνήμης. Οι υπόλοιπες γραμμές αυτού του φύλλου δεν περιέχουν δεδομένα.", "SSE.Controllers.Main.textGuest": "Επισκέπτης", "SSE.Controllers.Main.textHasMacros": "Το αρχείο περιέχει αυτόματες μακροεντολές.<br>Θέλετε να εκτελέσετε μακροεντολές;", "SSE.Controllers.Main.textKeep": "Διατήρη<PERSON>η", "SSE.Controllers.Main.textLearnMore": "Μάθετε περισσότερα", "SSE.Controllers.Main.textLoadingDocument": "Γίνετα<PERSON> φόρτωση υπολογιστικού φύλλου", "SSE.Controllers.Main.textLongName": "Εισάγετε ένα όνομα μικρότερο από 128 χαρακτήρες.", "SSE.Controllers.Main.textNeedSynchronize": "Έχετε ενημερώσεις", "SSE.Controllers.Main.textNo": "Όχι", "SSE.Controllers.Main.textNoLicenseTitle": "Το όριο της άδειας χρήσης έχει εξαντληθεί.", "SSE.Controllers.Main.textPaidFeature": "Δυνατότητα επί πληρωμή", "SSE.Controllers.Main.textPleaseWait": "Η λειτουργία ίσως χρειαστεί περισσότερο χρόνο από τον αναμενόμενο. Παρακαλούμε περιμένετε...", "SSE.Controllers.Main.textReconnect": "Η σύνδεση αποκαταστάθηκε", "SSE.Controllers.Main.textRemember": "Να θυμάσαι την επιλογή μου για όλα τα αρχεία", "SSE.Controllers.Main.textRememberMacros": "Απομνημόνευση της επιλογής μου για όλες τις μακροεντολές", "SSE.Controllers.Main.textRenameError": "Το όνομα χρήστη δεν μπορεί να είναι κενό.", "SSE.Controllers.Main.textRenameLabel": "Εισάγετε ένα όνομα για συνεργατική χρήση", "SSE.Controllers.Main.textReplace": "Αντικατάσταση", "SSE.Controllers.Main.textRequestMacros": "Μια μακροεντολή κάνει ένα αίτημα σε διεύθυνση URL. Θέλετε να επιτρέψετε το αίτημα στο %1;", "SSE.Controllers.Main.textShape": "Σχήμα", "SSE.Controllers.Main.textStrict": "Αυστηρή κατάσταση", "SSE.Controllers.Main.textText": "Κείμενο", "SSE.Controllers.Main.textTryQuickPrint": "Έχετε επιλέξει Γρήγορη εκτύπωση: ολόκληρο το έγγραφο θα εκτυπωθεί στον τελευταίο επιλεγμένο ή προεπιλεγμένο εκτυπωτή. <br>Θέλετε να συνεχίσετε;", "SSE.Controllers.Main.textTryUndoRedo": "Οι λειτουργίες Αναίρεση/Επανάληψη είναι απενεργοποιημένες στην κατάσταση Γρήγορης συν-επεξεργασίας.<br>Κάντε κλικ στο κουμπί 'Αυστηρή κατάσταση' για να μεταβείτε στην Αυστηρή κατάσταση συν-επεξεργασίας όπου επεξεργάζεστε το αρχείο χωρίς παρέμβαση άλλων χρηστών και στέλνετε τις αλλαγές σας αφού τις αποθηκεύσετε. Η μετάβαση μεταξύ των δύο καταστάσεων γίνεται μέσω των Προηγμένων Ρυθμίσεων.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Οι λειτουργ<PERSON>ες Αναίρεση/Επανάληψη είναι απενεργοποιημένες στην κατάσταση Γρήγορης συν-επεξεργασίας.", "SSE.Controllers.Main.textUndo": "Αναίρεση", "SSE.Controllers.Main.textUpdateVersion": "Δεν είναι δυνατή η επεξεργασία του εγγράφου αυτήν τη στιγμή.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ενημέρωση του αρχείου, παρα<PERSON><PERSON><PERSON><PERSON> περιμένετε...", "SSE.Controllers.Main.textUpdating": "Ενημέρωση", "SSE.Controllers.Main.textYes": "Ναι", "SSE.Controllers.Main.titleLicenseExp": "Η άδεια έληξε", "SSE.Controllers.Main.titleLicenseNotActive": "Η άδεια δεν είναι ενεργή", "SSE.Controllers.Main.titleServerVersion": "Ο επεξεργαστής ενημερώθηκε", "SSE.Controllers.Main.titleUpdateVersion": "Η έκδοση άλλαξε", "SSE.Controllers.Main.txtAccent": "Τόνος", "SSE.Controllers.Main.txtAll": "(Όλα)", "SSE.Controllers.Main.txtArt": "Το κείμενό σας εδώ", "SSE.Controllers.Main.txtBasicShapes": "Βασικ<PERSON> σχήματα", "SSE.Controllers.Main.txtBlank": "(κενό)", "SSE.Controllers.Main.txtButtons": "Κουμπιά", "SSE.Controllers.Main.txtByField": "%1 από %2", "SSE.Controllers.Main.txtCallouts": "Επεξηγήσεις", "SSE.Controllers.Main.txtCharts": "Γραφήματα", "SSE.Controllers.Main.txtClearFilter": "Εκκαθάριση φίλτρου", "SSE.Controllers.Main.txtColLbls": "Ετικέτες στηλών", "SSE.Controllers.Main.txtColumn": "Στήλη", "SSE.Controllers.Main.txtConfidential": "Εμπιστευτικό", "SSE.Controllers.Main.txtDate": "Ημερομηνία", "SSE.Controllers.Main.txtDays": "Ημέρες", "SSE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>τος", "SSE.Controllers.Main.txtEditingMode": "Ορισμός λειτουργίας επεξεργασίας...", "SSE.Controllers.Main.txtErrorLoadHistory": "Η φόρτωση ιστορικού απέτυχε", "SSE.Controllers.Main.txtFiguredArrows": "Σχηματικ<PERSON> βέλη", "SSE.Controllers.Main.txtFile": "Αρχείο", "SSE.Controllers.Main.txtGrandTotal": "Τελικό Σύνολο", "SSE.Controllers.Main.txtGroup": "Ομάδα", "SSE.Controllers.Main.txtHours": "Ώρες", "SSE.Controllers.Main.txtInfo": "Πληροφορίες", "SSE.Controllers.Main.txtLines": "Γραμμές", "SSE.Controllers.Main.txtMath": "Μαθηματικά", "SSE.Controllers.Main.txtMinutes": "Λεπτά", "SSE.Controllers.Main.txtMonths": "Μήνες", "SSE.Controllers.Main.txtMultiSelect": "Πολλαπλή Επιλογή", "SSE.Controllers.Main.txtNone": "Κανένα", "SSE.Controllers.Main.txtOr": "%1 ή %2", "SSE.Controllers.Main.txtPage": "Σελίδα", "SSE.Controllers.Main.txtPageOf": "Σελίδα %1 από %2", "SSE.Controllers.Main.txtPages": "Σελίδες", "SSE.Controllers.Main.txtPicture": "Εικόνα", "SSE.Controllers.Main.txtPivotTable": "Συγκεντρωτικ<PERSON>ς πίνακας", "SSE.Controllers.Main.txtPreparedBy": "Ετοιμάστηκε από", "SSE.Controllers.Main.txtPrintArea": "Εκτυπώσιμη_Περιοχή", "SSE.Controllers.Main.txtQuarter": "Τέταρτο", "SSE.Controllers.Main.txtQuarters": "Τετράμηνα", "SSE.Controllers.Main.txtRectangles": "Ορθογώνια", "SSE.Controllers.Main.txtRow": "Γραμμή", "SSE.Controllers.Main.txtRowLbls": "Ετικέτες <PERSON>μ<PERSON>ν", "SSE.Controllers.Main.txtSaveCopyAsComplete": "Το αντίγραφο του αρχείου αποθηκεύτηκε με επιτυχία", "SSE.Controllers.Main.txtScheme_Aspect": "Όψη", "SSE.Controllers.Main.txtScheme_Blue": "Μπλε", "SSE.Controllers.Main.txtScheme_Blue_Green": "Μπλε Πράσινο", "SSE.Controllers.Main.txtScheme_Blue_II": "Μπλε II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "Μπλε ζεστό", "SSE.Controllers.Main.txtScheme_Grayscale": "Αποχρώσεις του γκρι", "SSE.Controllers.Main.txtScheme_Green": "Πράσινο", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Πρά<PERSON>ι<PERSON><PERSON> κίτρινο", "SSE.Controllers.Main.txtScheme_Marquee": "Τέντα", "SSE.Controllers.Main.txtScheme_Median": "Διάμεσο", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Πορτοκ<PERSON>λ<PERSON>", "SSE.Controllers.Main.txtScheme_Orange_Red": "Πορτοκ<PERSON><PERSON><PERSON>όκκινο", "SSE.Controllers.Main.txtScheme_Paper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Red": "Κόκκινο", "SSE.Controllers.Main.txtScheme_Red_Orange": "Κόκκινο πορτοκαλί", "SSE.Controllers.Main.txtScheme_Red_Violet": "Κόκκινο βιολετί", "SSE.Controllers.Main.txtScheme_Slipstream": "Ανατρο<PERSON>ασμός", "SSE.Controllers.Main.txtScheme_Violet": "Βιολετί", "SSE.Controllers.Main.txtScheme_Violet_II": "Βιολετί ΙΙ", "SSE.Controllers.Main.txtScheme_Yellow": "Κίτρινο", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "Κίτριν<PERSON> πορτοκαλ<PERSON>", "SSE.Controllers.Main.txtSeconds": "Δευτερόλεπτα", "SSE.Controllers.Main.txtSeries": "Σειρά", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Επεξήγηση γραμμής 1 (Περίγραμμα και Μπάρα)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Επεξήγηση γραμμής 2 (Περίγραμμα και Μπάρα)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Επεξήγηση γραμμής 3 (Περίγραμμα και Μπάρα)", "SSE.Controllers.Main.txtShape_accentCallout1": "Επεξήγηση γραμμής 1 (Μπάρ<PERSON>)", "SSE.Controllers.Main.txtShape_accentCallout2": "Επεξήγηση γραμμής 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "SSE.Controllers.Main.txtShape_accentCallout3": "Επεξήγηση γραμμής 3 (Μπ<PERSON><PERSON><PERSON>)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Κουμπ<PERSON> πίσω ή προηγούμενο", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Κουμπί αρχής", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Κενό κουμπί", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Κουμπί εγγράφου", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Κουμπί τέλους", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Κουμπί μπροστά ή επόμενο", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Κουμπί βοήθειας", "SSE.Controllers.Main.txtShape_actionButtonHome": "Κουμπί αρχικής", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Κουμπί πληροφοριών", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Κουμπί Ταινίας", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Κουμπί Επιστροφής", "SSE.Controllers.Main.txtShape_actionButtonSound": "Κουμπί Ήχου", "SSE.Controllers.Main.txtShape_arc": "Κυκλικό Τόξο", "SSE.Controllers.Main.txtShape_bentArrow": "Λυγισμέν<PERSON> βέλος", "SSE.Controllers.Main.txtShape_bentConnector5": "Αρθρωτός σύνδεσμος", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Σύνδεσμος βέλους αγκώνα", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Σύνδεσμος διπλού βέλους αγκώνα", "SSE.Controllers.Main.txtShape_bentUpArrow": "Λυγισμένο επάνω βέλος", "SSE.Controllers.Main.txtShape_bevel": "Πλάγια Τομή", "SSE.Controllers.Main.txtShape_blockArc": "Πλα<PERSON><PERSON>ι<PERSON> τόξου", "SSE.Controllers.Main.txtShape_borderCallout1": "Επεξήγηση γραμμής 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Επεξήγηση γραμμής 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Επεξήγηση γραμμής 3", "SSE.Controllers.Main.txtShape_bracePair": "Διπ<PERSON><PERSON>γκιστρο", "SSE.Controllers.Main.txtShape_callout1": "Επεξήγηση γραμμής 1 (χωρ<PERSON>ς περίγραμμα)", "SSE.Controllers.Main.txtShape_callout2": "Επεξήγηση γραμμής 2 (χωρ<PERSON>ς περίγραμμα)", "SSE.Controllers.Main.txtShape_callout3": "Επεξήγηση γραμμής 3 (χωρ<PERSON>ς περίγραμμα)", "SSE.Controllers.Main.txtShape_can": "Κύλινδρος", "SSE.Controllers.Main.txtShape_chevron": "Σιρίτι", "SSE.Controllers.Main.txtShape_chord": "Χορ<PERSON><PERSON> Κύκλου", "SSE.Controllers.Main.txtShape_circularArrow": "Κυκλικό βέλος", "SSE.Controllers.Main.txtShape_cloud": "Cloud", "SSE.Controllers.Main.txtShape_cloudCallout": "Πρόσκληση Cloud", "SSE.Controllers.Main.txtShape_corner": "Γωνία", "SSE.Controllers.Main.txtShape_cube": "Κύβος", "SSE.Controllers.Main.txtShape_curvedConnector3": "Καμπυλω<PERSON><PERSON>ς σύνδεσμος", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Καμπυλω<PERSON><PERSON>ς σύνδεσμος με βέλος", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Καμπυλω<PERSON><PERSON>ς σύνδεσμος με διπλό βέλος", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Καμπυλωτ<PERSON> κάτω βέλος", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Καμπυλωτό αριστερό βέλος", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Καμπυλωτό δεξί βέλος", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Καμπυλωτό επάνω βέλος", "SSE.Controllers.Main.txtShape_decagon": "Δεκάγωνο", "SSE.Controllers.Main.txtShape_diagStripe": "Διαγώνια λωρίδα", "SSE.Controllers.Main.txtShape_diamond": "Ρόμβος", "SSE.Controllers.Main.txtShape_dodecagon": "Δωδεκάγωνο", "SSE.Controllers.Main.txtShape_donut": "Ντ<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_doubleWave": "Διπλό Κύμα", "SSE.Controllers.Main.txtShape_downArrow": "Κάτω βέλος", "SSE.Controllers.Main.txtShape_downArrowCallout": "Επεξήγηση με κάτω βέλος", "SSE.Controllers.Main.txtShape_ellipse": "Έλλειψη", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Καμπυλωτή κορδέλα κάτω", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Καμπυλωτή κορδέλα επάνω", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Διάγραμμα ροής: Εναλλακτική διεργασία", "SSE.Controllers.Main.txtShape_flowChartCollate": "Διάγραμμα ροής: Τοποθέτηση σε σειρά", "SSE.Controllers.Main.txtShape_flowChartConnector": "Διάγραμμα ροής: Σύνδεσμος", "SSE.Controllers.Main.txtShape_flowChartDecision": "Διάγραμμα ροής: Απόφαση", "SSE.Controllers.Main.txtShape_flowChartDelay": "Διάγραμμα ροής: Καθυστέρηση", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Διάγραμμα ροής: Προβολή", "SSE.Controllers.Main.txtShape_flowChartDocument": "Διάγραμμα ροής: Έγγραφο", "SSE.Controllers.Main.txtShape_flowChartExtract": "Διάγραμμα ροής: Εξαγωγή", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Διάγραμμα ροής: Δεδομένα", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Διάγραμμα ροής: Ε<PERSON>ω<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> αποθηκευτικ<PERSON>ς χώρος", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Διάγραμμα ροής: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ι<PERSON><PERSON><PERSON> δίσκος", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Διάγραμμα ροής: Αποθηκ<PERSON>υτι<PERSON><PERSON> μέσο άμεσης πρόσβασης", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Διάγραμμα ροής: Αποθηκευτικ<PERSON> μέσο σειριακής πρόσβασης", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Διάγραμμα ροής: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>νητη εισαγωγή", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Διάγραμμα ροής: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>νητη λειτουργία", "SSE.Controllers.Main.txtShape_flowChartMerge": "Διάγραμμα ροής: Συγχώνευση", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Διάγραμμα ροής: Πολλα<PERSON><PERSON><PERSON> έγγραφο", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Διάγραμμα ροής: Σύν<PERSON><PERSON>σ<PERSON>ος εκτός σελίδας", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Διάγραμμα ροής: Αποθηκευμένα δεδομένα", "SSE.Controllers.Main.txtShape_flowChartOr": "Διάγραμμα ροής: Ή", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Διάγραμμα ροής: Προκαθορισμένη διεργασία", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Διάγραμμα ροής: Προετοιμασία", "SSE.Controllers.Main.txtShape_flowChartProcess": "Διάγραμμα ροής: Διεργασία", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Διάγραμμα ροής: <PERSON><PERSON><PERSON><PERSON>α", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Διάγραμμα ροής: Διάτρητη ταινία", "SSE.Controllers.Main.txtShape_flowChartSort": "Διάγραμμα ροής: Ταξινόμηση", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Διάγραμμα ροής: Συμβολή", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Διάγραμμα ροής: Τερματισμ<PERSON>ς", "SSE.Controllers.Main.txtShape_foldedCorner": "Διπλωμένη Γωνία", "SSE.Controllers.Main.txtShape_frame": "Πλαίσιο", "SSE.Controllers.Main.txtShape_halfFrame": "Μισό πλαίσιο", "SSE.Controllers.Main.txtShape_heart": "Καρδιά", "SSE.Controllers.Main.txtShape_heptagon": "Επτάγωνο", "SSE.Controllers.Main.txtShape_hexagon": "Εξάγωνο", "SSE.Controllers.Main.txtShape_homePlate": "Πεντάγωνο", "SSE.Controllers.Main.txtShape_horizontalScroll": "Οριζόντια κύλιση", "SSE.Controllers.Main.txtShape_irregularSeal1": "Έκρηξη 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Έκρηξη 2", "SSE.Controllers.Main.txtShape_leftArrow": "Αριστερ<PERSON> βέλος", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Επεξήγηση με αριστερό βέλος", "SSE.Controllers.Main.txtShape_leftBrace": "Αριστερ<PERSON> άγκιστρο", "SSE.Controllers.Main.txtShape_leftBracket": "Αριστερή παρένθεση", "SSE.Controllers.Main.txtShape_leftRightArrow": "Αριστερό δεξιό βέλος", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Επεξήγηση με αριστερό δεξί βέλος", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Αριστερό δεξιό επάνω βέλος", "SSE.Controllers.Main.txtShape_leftUpArrow": "Αριστερό επάνω βέλος", "SSE.Controllers.Main.txtShape_lightningBolt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ς", "SSE.Controllers.Main.txtShape_line": "Γραμμή", "SSE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Διπλ<PERSON> βέλος", "SSE.Controllers.Main.txtShape_mathDivide": "Δια", "SSE.Controllers.Main.txtShape_mathEqual": "Ίσον", "SSE.Controllers.Main.txtShape_mathMinus": "Πλην", "SSE.Controllers.Main.txtShape_mathMultiply": "Επί", "SSE.Controllers.Main.txtShape_mathNotEqual": "Διάφορο", "SSE.Controllers.Main.txtShape_mathPlus": "Συν", "SSE.Controllers.Main.txtShape_moon": "Φεγγάρι", "SSE.Controllers.Main.txtShape_noSmoking": "Σύμβολο \"Όχι\"", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Δεξ<PERSON> βέλος με εγκοπή", "SSE.Controllers.Main.txtShape_octagon": "Οκτάγωνο", "SSE.Controllers.Main.txtShape_parallelogram": "Παραλληλόγραμμο", "SSE.Controllers.Main.txtShape_pentagon": "Πεντάγωνο", "SSE.Controllers.Main.txtShape_pie": "Πίτα", "SSE.Controllers.Main.txtShape_plaque": "Σύμβολο", "SSE.Controllers.Main.txtShape_plus": "Συν", "SSE.Controllers.Main.txtShape_polyline1": "Μουντζού<PERSON>α", "SSE.Controllers.Main.txtShape_polyline2": "Ελεύθερο Σχέδιο", "SSE.Controllers.Main.txtShape_quadArrow": "Τετραπλό βέλος", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Επεξήγηση τετραπλού βέλους", "SSE.Controllers.Main.txtShape_rect": "Ορθογώνιο ", "SSE.Controllers.Main.txtShape_ribbon": "Κάτ<PERSON>λα", "SSE.Controllers.Main.txtShape_ribbon2": "Πάν<PERSON> κορδέλα", "SSE.Controllers.Main.txtShape_rightArrow": "Δεξ<PERSON> βέλος", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Επεξήγηση με δεξί βέλος", "SSE.Controllers.Main.txtShape_rightBrace": "Δεξιό Άγκιστρο", "SSE.Controllers.Main.txtShape_rightBracket": "Δεξιά παρένθεση", "SSE.Controllers.Main.txtShape_round1Rect": "Με Στρογγυλεμένη Γωνία", "SSE.Controllers.Main.txtShape_round2DiagRect": "Με Διαγώνιες Στρογγυλεμένες Γω<PERSON>ς", "SSE.Controllers.Main.txtShape_round2SameRect": "Με Στρογγυλεμένες Γωνίες στην Ίδια Πλευρά", "SSE.Controllers.Main.txtShape_roundRect": "Με Στρογγυλεμένες Γωνίες", "SSE.Controllers.Main.txtShape_rtTriangle": "Ορθογώνιο Τρίγωνο", "SSE.Controllers.Main.txtShape_smileyFace": "Χαμογ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Φατσούλα", "SSE.Controllers.Main.txtShape_snip1Rect": "Με ψαλιδισμένη γωνία", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Με διαγώνιες ψαλιδισμένες γωνίες", "SSE.Controllers.Main.txtShape_snip2SameRect": "Με ψαλιδισμένες γωνίες στην ίδια πλευρά", "SSE.Controllers.Main.txtShape_snipRoundRect": "Με Στρογγυλεμένη και Ψαλιδισμένη Γωνία", "SSE.Controllers.Main.txtShape_spline": "Καμπύλη", "SSE.Controllers.Main.txtShape_star10": "Αστέρι 10 Σημείων", "SSE.Controllers.Main.txtShape_star12": "Αστέρι 12 Σημείων", "SSE.Controllers.Main.txtShape_star16": "Αστέρι 16 Σημείων", "SSE.Controllers.Main.txtShape_star24": "Αστέρι 24 Σημείων", "SSE.Controllers.Main.txtShape_star32": "Αστέρι 32 Σημείων", "SSE.Controllers.Main.txtShape_star4": "Αστέρι 4 Σημείων", "SSE.Controllers.Main.txtShape_star5": "Αστέρι 5 Σημείων", "SSE.Controllers.Main.txtShape_star6": "Αστέρι 6 Σημείων", "SSE.Controllers.Main.txtShape_star7": "Αστέρι 7 Σημείων", "SSE.Controllers.Main.txtShape_star8": "Αστέρι 8 Σημείων", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Ριγέ δεξί βέλος", "SSE.Controllers.Main.txtShape_sun": "Ήλιος", "SSE.Controllers.Main.txtShape_teardrop": "Δά<PERSON>ρυ", "SSE.Controllers.Main.txtShape_textRect": "Πλα<PERSON><PERSON><PERSON><PERSON> κειμένου", "SSE.Controllers.Main.txtShape_trapezoid": "Τραπέζιο", "SSE.Controllers.Main.txtShape_triangle": "Τρίγωνο", "SSE.Controllers.Main.txtShape_upArrow": "Επάνω βέλος", "SSE.Controllers.Main.txtShape_upArrowCallout": "Επεξήγηση με επάνω βέλος", "SSE.Controllers.Main.txtShape_upDownArrow": "Επάνω κάτω βέλος", "SSE.Controllers.Main.txtShape_uturnArrow": "<PERSON><PERSON><PERSON><PERSON> αναστροφής", "SSE.Controllers.Main.txtShape_verticalScroll": "Κατακόρυφη κύλιση", "SSE.Controllers.Main.txtShape_wave": "Κύμα", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Ο<PERSON><PERSON><PERSON> επεξήγηση", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Ορθογώνια Επεξήγηση", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Στρογγυλεμένη ορθογώνια επεξήγηση", "SSE.Controllers.Main.txtSheet": "Φύλλο", "SSE.Controllers.Main.txtSlicer": "Αναλυτής", "SSE.Controllers.Main.txtStarsRibbons": "Αστέρια & Κορδέλες", "SSE.Controllers.Main.txtStyle_Bad": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Calculation": "Υπολογισμός", "SSE.Controllers.Main.txtStyle_Check_Cell": "Έλεγχος Κελιού", "SSE.Controllers.Main.txtStyle_Comma": "Κόμμα", "SSE.Controllers.Main.txtStyle_Currency": "Νόμισμα", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Επεξηγηματικ<PERSON> κείμενο", "SSE.Controllers.Main.txtStyle_Good": "Σωστό", "SSE.Controllers.Main.txtStyle_Heading_1": "Επικεφαλίδα 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Επικεφαλίδα 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Επικεφαλίδα 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Επικεφαλίδα 4", "SSE.Controllers.Main.txtStyle_Input": "Εισαγωγή", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Συνδεδεμένο κελί", "SSE.Controllers.Main.txtStyle_Neutral": "Ουδέτερο", "SSE.Controllers.Main.txtStyle_Normal": "Κανονική", "SSE.Controllers.Main.txtStyle_Note": "Σημείωση", "SSE.Controllers.Main.txtStyle_Output": "Αποτέλεσμα", "SSE.Controllers.Main.txtStyle_Percent": "Ποσοστό", "SSE.Controllers.Main.txtStyle_Title": "Τίτλος", "SSE.Controllers.Main.txtStyle_Total": "Σύνολο", "SSE.Controllers.Main.txtStyle_Warning_Text": "Κείμενο Προειδοποίησης", "SSE.Controllers.Main.txtTab": "Καρτ<PERSON><PERSON>α", "SSE.Controllers.Main.txtTable": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "SSE.Controllers.Main.txtTime": "Ώρα", "SSE.Controllers.Main.txtUnlock": "Ξεκλείδωμα", "SSE.Controllers.Main.txtUnlockRange": "Ξεκλείδωμα εύρους", "SSE.Controllers.Main.txtUnlockRangeDescription": "Εισάγετε το συνθηματικό τροποποίησης αυτού του εύρους:", "SSE.Controllers.Main.txtUnlockRangeWarning": "Ένα εύρος που προσπαθείτε να τροποποιήσετε προστατεύεται με συνθηματικό.", "SSE.Controllers.Main.txtValues": "Τιμές", "SSE.Controllers.Main.txtView": "Προβολή", "SSE.Controllers.Main.txtXAxis": "Άξονας Χ", "SSE.Controllers.Main.txtYAxis": "Άξονας Υ", "SSE.Controllers.Main.txtYears": "Έτη", "SSE.Controllers.Main.unknownErrorText": "Άγνωστο σφάλμα.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Ο περιηγητής σας δεν υποστηρίζεται.", "SSE.Controllers.Main.uploadDocExtMessage": "Άγνωστη μορφή εγγράφου.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Δεν μεταφορτώθηκαν έγγραφα.", "SSE.Controllers.Main.uploadDocSizeMessage": "Ξεπεράστηκε το μέγιστο μέγεθος εγγράφου.", "SSE.Controllers.Main.uploadImageExtMessage": "Άγνωστη μορφή εικόνας.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Δεν μεταφορτώθηκαν εικόνες.", "SSE.Controllers.Main.uploadImageSizeMessage": "Η εικόνα είναι πολύ μεγάλη. Το μέγιστο μέγεθος είναι 25MB.", "SSE.Controllers.Main.uploadImageTextText": "Γίνεται μεταφόρτωση εικόνας...", "SSE.Controllers.Main.uploadImageTitleText": "Μεταφόρτωση εικόνας", "SSE.Controllers.Main.waitText": "Παρακαλούμε, περιμένετε...", "SSE.Controllers.Main.warnBrowserIE9": "Η εφαρμογή έχει περιορισμένες δυνατότητες στον IE9. Δοκιμάστε IE10 ή νεώτερο.", "SSE.Controllers.Main.warnBrowserZoom": "Η τρέχουσα ρύθμιση εστίασης του περιηγητή σας δεν υποστηρίζεται πλήρως. Παρακαλούμε επιστρέψτε στην προεπιλεγμένη εστίαση πατώντας Ctrl+0.", "SSE.Controllers.Main.warnLicenseAnonymous": "Δεν επιτρέπεται η πρόσβαση για ανώνυμους χρήστες.<br>Αυτό το έγγραφο θα ανοίξει μόνο για προβολή.", "SSE.Controllers.Main.warnLicenseBefore": "Η άδεια δεν είναι ενεργή.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>λ<PERSON>ύμε επικοινωνήστε με τον διαχειριστή σας.", "SSE.Controllers.Main.warnLicenseExceeded": "Έχετε υπερβεί τον αριθμό των ταυτόχρονων συνδέσεων με τον διακομιστή εγγράφων και το έγγραφο θα ανοίξει μόνο για προβολή.<br>Πα<PERSON><PERSON><PERSON><PERSON>λούμε επικοινωνήστε με τον διαχειριστή σας για περισσότερες πληροφορίες.", "SSE.Controllers.Main.warnLicenseExp": "Η άδειά σας έχει λήξει.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>με ενημερώστε την άδεια χρήσης σας και ανανεώστε τη σελίδα.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Η άδεια έληξε.<br>Δεν έχετε πρόσβαση στη δυνατότητα επεξεργασίας εγγράφων.<br>Ε<PERSON><PERSON><PERSON><PERSON><PERSON>νωνήστε με τον διαχειριστή σας.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Η άδεια πρέπει να ανανεωθεί.<br>Έχετε περιορισμένη πρόσβαση στη λειτουργία επεξεργασίας εγγράφων.<br>Ε<PERSON><PERSON><PERSON><PERSON><PERSON>νωνήστε με τον διαχειριστή σας για πλήρη πρόσβαση", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Έχετε υπερβεί τον αριθμό των ταυτόχρονων χρηστών και το έγγραφο θα ανοίξει μόνο για προβολή.<br>Παρακαλούμε επικοινωνήστε με τον διαχειριστή σας για περισσότερες πληροφορίες.", "SSE.Controllers.Main.warnNoLicense": "Αυτή η έκδοση των επεξεργαστών %1 έχει ορισμένους περιορισμούς για ταυτόχρονες συνδέσεις με το διακομιστή εγγράφων.<br><PERSON><PERSON><PERSON> χρειάζεστε περισσότερες, παρακαλούμε σκεφτείτε να αγοράσετε μια εμπορική άδεια.", "SSE.Controllers.Main.warnNoLicenseUsers": "Αυτή η έκδοση των επεξεργαστών %1 έχει ορισμένους περιορισμούς για τους ταυτόχρονους χρήστες.<br><PERSON><PERSON><PERSON> χρειάζεστε περισσότερους, παρακαλούμε σκεφτείτε να αγοράσετε μια εμπορική άδεια.", "SSE.Controllers.Main.warnProcessRightsChange": "Σας έχει απαγορευτεί το δικαίωμα επεξεργασίας του αρχείου.", "SSE.Controllers.PivotTable.strSheet": "Φύλλο", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "Το στοιχείο δεν μπορεί να προστεθεί ή να τροποποιηθεί. Η αναφορά Συγκεντρωτικού Πίνακα έχει αυτό το πεδίο στα Φίλτρα.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "Δεν επιτρέπονται ενέργειες με υπολογιζόμενα στοιχεία για αυτό το ενεργό κελί.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "Εάν ένας ή περισσότεροι Συγκεντρωτικοί Πίνακες έχουν υπολογιζόμενα στοιχεία, καν<PERSON><PERSON><PERSON> πεδίο δεν μπορεί να χρησιμοποιηθεί στην περιοχή δεδομένων δύο ή περισσότερες φορές ή στην περιοχή δεδομένων και σε άλλη περιοχή ταυτόχρονα.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Τα υπολογιζόμενα στοιχεία δεν λειτουργούν με προσαρμοσμένα μερικά αθροίσματα.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "Δεν είναι δυνατή η εύρεση ενός ονόματος στοιχείου. Βεβαιωθείτε ότι έχετε πληκτρολογήσει σωστά το όνομα και ότι το στοιχείο υπάρχει στην αναφορά Συγκεντρωτικού Πίνακα.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Οι μέσοι όροι, οι τυπικές αποκλίσεις και οι διακυμάνσεις δεν υποστηρίζονται όταν μια αναφορά Συγκεντρωτικού Πίνακα έχει υπολογιζόμενα στοιχεία.", "SSE.Controllers.Print.strAllSheets": "Όλα τα Φύλλα", "SSE.Controllers.Print.textFirstCol": "Πρώτη στήλη", "SSE.Controllers.Print.textFirstRow": "Πρώτη γραμμή", "SSE.Controllers.Print.textFrozenCols": "Σταθεροποιημένες στήλες", "SSE.Controllers.Print.textFrozenRows": "Σταθεροποιημένες γραμμές", "SSE.Controllers.Print.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Controllers.Print.textNoRepeat": "Να μην επαναλαμβάνεται", "SSE.Controllers.Print.textRepeat": "Επανάληψη...", "SSE.Controllers.Print.textSelectRange": "Επιλογή εύρους", "SSE.Controllers.Print.txtCustom": "Προσαρμογή", "SSE.Controllers.Print.txtZoomToPage": "Ζουμ στη σελίδα", "SSE.Controllers.Search.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Controllers.Search.textNoTextFound": "Τα δεδομένα που αναζητάτε δεν βρέθηκαν. Παρακαλούμε προσαρμόστε τις επιλογές αναζήτησης.", "SSE.Controllers.Search.textReplaceSkipped": "Η αντικατάσταση ολοκληρώθηκε. {0} εμφανίσεις προσπεράστηκαν.", "SSE.Controllers.Search.textReplaceSuccess": "Η αναζήτηση ολοκληρώθηκε. Αντικαταστάθηκαν {0} εμφανίσεις", "SSE.Controllers.Statusbar.errorLastSheet": "Το βιβλίο εργασίας πρέπει να έχει τουλάχιστον ένα ορατό φύλλο εργασίας.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Δεν είναι δυνατή η διαγραφή του φύλλου εργασίας.", "SSE.Controllers.Statusbar.strSheet": "Φύλλο", "SSE.Controllers.Statusbar.textDisconnect": "<b>Η σύνδεση χάθηκε</b><br>Απ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> επανασύνδεσης. Παρακαλούμε ελέγξτε τις ρυθμίσεις σύνδεσης.", "SSE.Controllers.Statusbar.textSheetViewTip": "Βρίσκεστε σε κατάσταση προβολής φύλλου. Φίλτρα και ταξινομήσεις είναι ορατά μόνο σε εσάς και όσους είναι ακόμα σε αυτή την προβολή.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Βρίσκεστε ακόμα σε κατάσταση προβολής φύλλου. Φίλτρα είναι ορατά μόνο σε εσάς και όσους βρίσκονται ακόμα σε αυτή την προβολή.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Τα επιλεγμένα φύλλα εργασίας ενδέχεται να περιέχουν δεδομένα. Είστε βέβαιοι ότι θέλετε να συνεχίσετε;", "SSE.Controllers.Statusbar.zoomText": "Εστίαση {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Η γραμματοσειρά που επιχειρείτε να αποθηκεύσετε δεν είναι διαθέσιμη στην τρέχουσα συσκευή.<br>Η τεχνοτροπία κειμένου θα εμφανιστεί με μια από τις γραμματοσειρές συστήματος, η αποθηκευμένη γραμματοσειρά θα χρησιμοποιηθεί όταν γίνει διαθέσιμη.<br>Θέλετε να συνεχίσετε;", "SSE.Controllers.Toolbar.errorComboSeries": "Για να δημιουργήσετε συνδυαστικ<PERSON> γράφημα, επιλέξτε τουλάχιστον δύο σειρές δεδομένων.", "SSE.Controllers.Toolbar.errorMaxPoints": "Ο μέγιστος αριθμός σημείων σε σειρά ανά γράφημα είναι 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "ΣΦΑΛΜΑ! Ο μέγιστος αριθμός σειρών δεδομένων ανά γράφημα είναι 255", "SSE.Controllers.Toolbar.errorStockChart": "Λανθασμένη διάταξη γραμμών. Για να δημιουργήσετε ένα γράφημα μετοχών τοποθετήστε τα δεδομένα στο φύλλο με την ακόλουθη σειρά:<br>τιμή ανοίγματος, μέγιστη τιμή, ελάχιστη τιμή, τιμή κλεισίματος.", "SSE.Controllers.Toolbar.helpCalcItems": "Εργαστείτε με υπολογισμένα στοιχεία σε Συγκεντρωτικούς Πίνακες.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Υπολογιζόμενα στοιχεία", "SSE.Controllers.Toolbar.helpFastUndo": "Αναιρέστε εύκολα τις αλλ<PERSON><PERSON><PERSON><PERSON> ενώ συνεργάζεστε σε φύλλα στη Γρήγορη λειτουργία.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "\"Αναίρεση\" σε συν-επεξεργασία σε πραγματικό χρόνο", "SSE.Controllers.Toolbar.helpMergeShapes": "Συνδ<PERSON><PERSON><PERSON><PERSON><PERSON>, κα<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, δι<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τ<PERSON>, αφαιρέστε σχήματα σε δευτερόλεπτα για να δημιουργήσετε προσαρμοσμένα γραφικά.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Συγχώνευση σχημάτων", "SSE.Controllers.Toolbar.textAccent": "Τόνοι/Πνεύματα", "SSE.Controllers.Toolbar.textBracket": "Αγκ<PERSON>λες", "SSE.Controllers.Toolbar.textDirectional": "Κατευθυντικό", "SSE.Controllers.Toolbar.textFontSizeErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια αριθμητική τιμή μεταξύ 1 και 409", "SSE.Controllers.Toolbar.textFraction": "Κλάσματα", "SSE.Controllers.Toolbar.textFunction": "Συναρτή<PERSON><PERSON>ις", "SSE.Controllers.Toolbar.textIndicator": "Δείκτες", "SSE.Controllers.Toolbar.textInsert": "Εισαγωγή", "SSE.Controllers.Toolbar.textIntegral": "Ολοκληρώματα", "SSE.Controllers.Toolbar.textLargeOperator": "Μεγάλοι τελεστές", "SSE.Controllers.Toolbar.textLimitAndLog": "Όρια και λογάριθμοι", "SSE.Controllers.Toolbar.textLongOperation": "Η λειτουργία είναι χρονοβόρα", "SSE.Controllers.Toolbar.textMatrix": "Πίνακ<PERSON>ς", "SSE.Controllers.Toolbar.textOperator": "Τελεστές", "SSE.Controllers.Toolbar.textPivot": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "SSE.Controllers.Toolbar.textRadical": "Ρίζες", "SSE.Controllers.Toolbar.textRating": "Αξιολογήσεις", "SSE.Controllers.Toolbar.textRecentlyUsed": "Πρόσφατα χρησιμοποιημένα", "SSE.Controllers.Toolbar.textScript": "Δέσμες ενεργειών", "SSE.Controllers.Toolbar.textShapes": "Σχήματα", "SSE.Controllers.Toolbar.textSymbols": "Σύμβολα", "SSE.Controllers.Toolbar.textWarning": "Προειδοποίηση", "SSE.Controllers.Toolbar.txtAccent_Accent": "Οξεία", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Από πάνω βέλος δεξιά-αριστερά", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Από πάνω βέλος προς αριστερά", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Από πάνω βέλος προς τα δεξιά", "SSE.Controllers.Toolbar.txtAccent_Bar": "Μπάρα", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Κάτω οριζόντια γραμμή", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Επάνω οριζόντια γραμμή", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Μαθηματική σχέση εντός πλαισίου (με δέσμευση θέσης)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Μαθηματική σχέση εντός πλαισίου (παράδειγμα)", "SSE.Controllers.Toolbar.txtAccent_Check": "Έλεγχος", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Κάτω αγκύλη έκτασης", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Πάνω αγκύλη έκτασης", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Διάνυσμα A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC με οριζόντια γραμμή από πάνω", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y με επάνω οριζόντια γραμμή", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Τριπλή τελεία", "SSE.Controllers.Toolbar.txtAccent_DDot": "Διπλή τελεία", "SSE.Controllers.Toolbar.txtAccent_Dot": "Τελεία", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Διπλή μπάρα από πάνω", "SSE.Controllers.Toolbar.txtAccent_Grave": "Βαρεία", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Ομαδοποίηση χαρακτήρα από κάτω", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Ομαδοποίηση χαρακτήρα από πάνω", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Από πάνω καμάκι προς τα αριστερά", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Από πάνω καμάκι προς τα δεξιά", "SSE.Controllers.Toolbar.txtAccent_Hat": "Σύμβολο (^)", "SSE.Controllers.Toolbar.txtAccent_Smile": "Σημεί<PERSON> βραχέος", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Περισπωμένη", "SSE.Controllers.Toolbar.txtBracket_Angle": "Αγκ<PERSON>λες", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Αγκύλες με διαχωριστικά", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Αγκύλες με δύο διαχωριστικά", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Δεξιά αγκύλη", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Αριστερή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_Curve": "Άγκιστρα", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Άγκιστρα με διαχωριστικό", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Δεξι<PERSON> άγκιστρο", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Αριστερ<PERSON> άγκιστρο", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Περιπ<PERSON><PERSON><PERSON><PERSON>ι<PERSON> (δύο συνθήκες)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Περιπτώσεις (τρεις συνθήκες)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Αντικείμενο στοίβας", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Στοίβα αντικειμένου σε παρένθεση", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Παράδειγμα περιπτώσεων", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Διωνυ<PERSON><PERSON><PERSON><PERSON>ς συντελεστής", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Διωνυ<PERSON><PERSON><PERSON><PERSON>ς συντελεστής σε αγκύλες", "SSE.Controllers.Toolbar.txtBracket_Line": "Κάθετες μπάρες", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Δεξιά κατακόρυφη γραμμή", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Αριστερή κατακόρυφη γραμμή", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Δι<PERSON><PERSON><PERSON><PERSON> κάθετες αγκύλες", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Δεξιά διπλή κάθετη γραμμή", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Αριστερή διπλή κάθετη γραμμή", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Δάπεδο", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Δεξιό δάπεδο", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Αριστερ<PERSON> δάπεδο", "SSE.Controllers.Toolbar.txtBracket_Round": "Παρένθεση", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Παρένθεση με διαχωριστικό", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Δεξιά παρένθεση", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Αριστερή παρένθεση", "SSE.Controllers.Toolbar.txtBracket_Square": "Αγκ<PERSON>λες", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Σύμβολο κράτησης θέσης μεταξ<PERSON> δύο δεξιών αγκυλών", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Ανεστραμμένες αγκύλες", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Δεξιά αγκύλη", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Αριστερή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Σύμβολο κράτησης θέσης ανάμεσα σε δύο αριστερές αγκύλες", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Δι<PERSON><PERSON><PERSON>ς αγκύλες", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Δεξιά διπλή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Αριστερή διπλή αγκύλη", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Ταβάνι", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Δεξιά οροφή", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Αριστερή οροφή", "SSE.Controllers.Toolbar.txtDeleteCells": "Διαγρα<PERSON><PERSON> κελιών", "SSE.Controllers.Toolbar.txtExpand": "Επέκταση και ταξινόμηση", "SSE.Controllers.Toolbar.txtExpandSort": "Τα δεδομένα δίπλα στην επιλογή δεν θα ταξινομηθούν. Θέλετε να επεκτείνετε την επιλογή ώστε να συμπεριλάβει τα παρακείμενα δεδομένα ή να συνεχίσετε με την ταξινόμηση μόνο των επιλεγμένων κελιών;", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Κεκλιμένο κλάσμα", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "dx πάνω από dy", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "cap delta y πάνω από cap delta x", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "μερικό y έναντι μερικού x", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "δέλτα Y πάνω από δέλτα X", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Γραμμικό κλάσμα", "SSE.Controllers.Toolbar.txtFractionPi_2": "π/2", "SSE.Controllers.Toolbar.txtFractionSmall": "Μικρό κλάσμα", "SSE.Controllers.Toolbar.txtFractionVertical": "Σύνθετο κλάσμα", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Συνάρτηση αντίστροφου συνημιτόνου", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Συνάρτηση αντίστροφου υπερβολικού συνημιτόνου", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Συνάρτηση αντίστροφης συνεφαπτομένης", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Συνάρτηση αντίστροφης υπερβολικής συνεφαπτομένης", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Συνάρτηση αντίστροφης συντέμνουσας", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Συνάρτηση αντίστροφης υπερβολικής συντέμνουσας", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Συνάρτηση αντίστροφης τέμνουσας", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Συνάρτηση αντίστροφης υπερβολικής τέμνουσας", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Συνάρτηση αντίστροφου ημίτονου", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Συνάρτηση αντίστροφου υπερβολικού ημίτονου", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Συνάρτηση αντίστροφης εφαπτομένης", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Συνάρτηση αντίστροφης υπερβολικής εφαπτομένης", "SSE.Controllers.Toolbar.txtFunction_Cos": "Συνάρτηση συνημίτονου", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Συνάρτηση υπερβολικού συνημιτόνου", "SSE.Controllers.Toolbar.txtFunction_Cot": "Συνάρτηση συνεφαπτομένης", "SSE.Controllers.Toolbar.txtFunction_Coth": "Συνάρτηση υπερβολικής συνεφαπτομένης", "SSE.Controllers.Toolbar.txtFunction_Csc": "Συνάρτηση συντέμνουσας", "SSE.Controllers.Toolbar.txtFunction_Csch": "Συνάρτηση υπερβολικής συντέμνουσας", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Ημίτονο γωνίας θήτα", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Συνημίτονο 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Τύ<PERSON><PERSON> εφαπτομένης", "SSE.Controllers.Toolbar.txtFunction_Sec": "Συνάρτηση τέμνουσας", "SSE.Controllers.Toolbar.txtFunction_Sech": "Συνάρτηση υπερβολικής τέμνουσας", "SSE.Controllers.Toolbar.txtFunction_Sin": "Συνάρτηση ημίτονου", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Συνάρτηση υπερβολικού ημίτονου", "SSE.Controllers.Toolbar.txtFunction_Tan": "Συνάρτηση εφαπτομένης", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Συνάρτηση υπερβολικής εφαπτομένης", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Προσαρμογή", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Δεδομένα και μοντέλο", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "<PERSON><PERSON><PERSON><PERSON>, κακό και ουδέτερο", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "<PERSON><PERSON><PERSON><PERSON><PERSON>νο<PERSON>α", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Μορφή αριθμού", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Θεματι<PERSON><PERSON> στυλ κελιών", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Τίτλοι και επικεφαλίδες", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Προσαρμογή", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Σκουρόχρωμο", "SSE.Controllers.Toolbar.txtGroupTable_Light": "Ανοιχτόχρωμο", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Μεσαίο", "SSE.Controllers.Toolbar.txtInsertCells": "Εισαγωγ<PERSON> κελιών", "SSE.Controllers.Toolbar.txtIntegral": "Ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Διαφορικ<PERSON> θήτα", "SSE.Controllers.Toolbar.txtIntegral_dx": "Διαφορικό x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Διαφορικό y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Ολοκλήρωμα με στοιβαγμένα όρια", "SSE.Controllers.Toolbar.txtIntegralDouble": "Διπλ<PERSON> ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Διπλ<PERSON> ολοκλήρωμα με στοιβαγμένα όρια", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Διπλ<PERSON> ολοκλήρωμα με όρια", "SSE.Controllers.Toolbar.txtIntegralOriented": "Επικαμ<PERSON>ύ<PERSON>ι<PERSON> ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Επικαμ<PERSON><PERSON><PERSON>ι<PERSON> ολοκλήρωμα με στοιβαγμένα όρια", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Επιφανεια<PERSON><PERSON> ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Επιφανεια<PERSON><PERSON> ολοκλήρωμα με σωρευμένα όρια", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Επιφανεια<PERSON><PERSON> ολοκλήρωμα με όρια", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Επικαμ<PERSON><PERSON><PERSON>ι<PERSON> ολοκλήρωμα με όρια", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Ολοκλήρω<PERSON>α όγκου", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Ολοκλήρω<PERSON>α όγκου", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Ολοκλήρω<PERSON>α όγκου", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Ολοκλήρωμα με όρια", "SSE.Controllers.Toolbar.txtIntegralTriple": "Τριπλό ολοκλήρωμα", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Τριπλό ολοκλήρωμα με στοιβαγμένα όρια", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Τριπλό ολοκλήρωμα με όρια", "SSE.Controllers.Toolbar.txtInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Λογική Και", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Σύζευξη", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Λογική Και με όρια", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Λογική Και με κατώτερο όριο δείκτη", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Λογική Και με όρια δείκτη/εκθέτη", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Συν-γινόμενο", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Συν-γινόμενο με κατώτερο όριο", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Συν-γινόμενο με όρια", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Συν-γινόμενο με κατώτερο όριο δείκτη", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Συν-γινόμενο με όρια δείκτη/εκθέτη", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Άθροιση πάνω από k του n επιλέξτε k", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Άθροιση από i ίσο με μηδέν έως n", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Παράδειγμα άθροισης με χρήση δύο δεικτών", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Παράδειγμα προϊόντος", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Παράδειγμα ένωσης", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Λογική Ή", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Λογική Ή με κατώτερο όριο", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Λογική Ή με όρια", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Λογική Ή με κατώτερο όριο δείκτη", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Λογική Ή με όρια δείκτη/εκθέτη", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Τομή", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Διασταύρωση με κατώτατο όριο", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Διασταύρωση με όρια", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Τομή με κατώτερο όριο δείκτη", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Τομή με όρια δείκτη/εκθέτη", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Γινόμενο", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Γινόμενο με χαμηλότερο όριο", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Γινόμενο με όρια", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Γινόμενο με κατώτατο όριο δείκτη", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Γινόμενο με όρια δείκτη/εκθέτη", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Άθροιση", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Άθροιση με κατώτατο όριο", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Άθροιση", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Άθροιση με κατώτατο όριο δείκτη", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Άθροιση με όρια δείκτη/εκθέτη", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Ένωση", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Ένωση με κατώτατο όριο", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Ένωση με όρια", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Ένωση με κατώτατο όριο δείκτη", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Ένωση με όρια δείκτη/εκθέτη", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Παράδειγμα ορίου", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Παράδειγμα μέγιστου", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Όριο", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON>υ<PERSON>ι<PERSON><PERSON><PERSON> λογάριθμος", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Λογάριθμος", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Λογάριθμος", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Μέγιστο", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Ελάχιστο", "SSE.Controllers.Toolbar.txtLockSort": "Υπάρχουν δεδομένα δίπλα στην επιλογή σας, αλλ<PERSON> δεν έχετε επαρκή δικαιώματα τροποποίησης αυτών των κελιών.<br>Θέλετε να συνεχίσετε με την τρέχουσα επιλογή;", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 κενός πίνακας", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 κενός πίνακας", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 κενός πίνακας", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 κενός πίνακας", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON>ν<PERSON><PERSON> πίνακας 2x2 σε διπλές κάθετες γραμμές", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Κεν<PERSON>ς πίνακας 2x2 καθοριστικός", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON>εν<PERSON><PERSON> πίνακας 2x2 σε παρενθέσεις", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON>ν<PERSON><PERSON> πίνακας 2x2 σε αγκύλες", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 κενός πίνακας", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 κενός πίνακας", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 κενός πίνακας", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 κενός πίνακας", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Τελείες στο κάτω μέρος της γραμμής", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Τελείες στο μέσον της γραμμής", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Διαγώνιες τελείες", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Κατακόρυφες τελείες", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Αραι<PERSON>ς πίνακας σε παρενθέσεις", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Αραι<PERSON>ς πίνακας σε αγκύλες", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "Πίνακας ταυτότητας 2x2 με μηδενικά", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "2x2 μοναδι<PERSON><PERSON>ος πίνακας", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "Πίνακας ταυτότητας 3x3 με μηδενικά", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 μονα<PERSON><PERSON><PERSON><PERSON><PERSON> πίνακας", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Από κάτω βέλος δεξιά-αριστερά", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Από πάνω βέλος δεξιά-αριστερά", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Από κάτω βέλος προς τα αριστερά", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Από πάνω βέλος προς αριστερά", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Από κάτω βέλος προς τα δεξιά", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Από πάνω βέλος προς τα δεξιά", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Άνω κάτω τελεία ίσον", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Δίνει", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Δέλτα δίνει", "SSE.Controllers.Toolbar.txtOperator_Definition": "Ίσον με εξ ορισμού", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Δέλτα ίσο με", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Από κάτω βέλος δεξιά-αριστερά", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Από πάνω βέλος δεξιά-αριστερά", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Από κάτω βέλος προς τα αριστερά", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Από πάνω βέλος προς αριστερά", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Από κάτω βέλος προς τα δεξιά", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Από πάνω βέλος προς τα δεξιά", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Ίσον ίσον", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Πλην ίσον", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Συν ίσον", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Μέτρηση με", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Δεξιά πλευρά τετραγωνικού τύπου", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Τετραγωνική ρίζα α τετράγωνο συν β τετράγωνο", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Τετραγωνική ρίζα με βαθμό", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Κυβική ρίζα", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Ρίζα με βαθμό", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Τετραγωνική ρίζα", "SSE.Controllers.Toolbar.txtScriptCustom_1": "x δείκτης y τετράγωνο", "SSE.Controllers.Toolbar.txtScriptCustom_2": "e στο μείον i ωμέγα", "SSE.Controllers.Toolbar.txtScriptCustom_3": "x τετράγωνο", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Y αριστερός εκθέτης n αριστερός δείκτης ένα", "SSE.Controllers.Toolbar.txtScriptSub": "Δείκτης", "SSE.Controllers.Toolbar.txtScriptSubSup": "Δείκτης-εκθέτης", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Αριστερ<PERSON>ς δείκτης-εκθέτης", "SSE.Controllers.Toolbar.txtScriptSup": "Εκθέτης", "SSE.Controllers.Toolbar.txtSorting": "Ταξινόμηση", "SSE.Controllers.Toolbar.txtSortSelected": "Ταξινόμηση επιλεγμένων", "SSE.Controllers.Toolbar.txtSymbol_about": "Περίπου", "SSE.Controllers.Toolbar.txtSymbol_additional": "Συμπλήρωμα", "SSE.Controllers.Toolbar.txtSymbol_aleph": "'Aλεφ", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Άλφα", "SSE.Controllers.Toolbar.txtSymbol_approx": "Σχεδόν ίσο με", "SSE.Controllers.Toolbar.txtSymbol_ast": "Τελεστής αστερίσκος", "SSE.Controllers.Toolbar.txtSymbol_beta": "Βήτα", "SSE.Controllers.Toolbar.txtSymbol_beth": "Στοίχημα", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Τελεστής κουκκίδα", "SSE.Controllers.Toolbar.txtSymbol_cap": "Τομή", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Κυβική ρίζα", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Οριζόντια έλλειψη στο μέσον της γραμμής", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON>αθ<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "Χι", "SSE.Controllers.Toolbar.txtSymbol_cong": "Περίπου ίσο με", "SSE.Controllers.Toolbar.txtSymbol_cup": "Ένωση", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Διαγώνια έλλειψη κάτω δεξιά", "SSE.Controllers.Toolbar.txtSymbol_degree": "Βαθμοί", "SSE.Controllers.Toolbar.txtSymbol_delta": "Δέλτα", "SSE.Controllers.Toolbar.txtSymbol_div": "Σύμβολο διαίρεσης", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Κάτω βέλος", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Κενό σύνολο", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Έψιλον", "SSE.Controllers.Toolbar.txtSymbol_equals": "Ίσον", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Πανομοιότυπο με", "SSE.Controllers.Toolbar.txtSymbol_eta": "Ήτα", "SSE.Controllers.Toolbar.txtSymbol_exists": "Υπάρχει", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Παραγοντικό", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Βαθμοί Fahrenheit", "SSE.Controllers.Toolbar.txtSymbol_forall": "<PERSON><PERSON>α όλα", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Γάμμα", "SSE.Controllers.Toolbar.txtSymbol_geq": "Μεγαλύτερο ή ίσο με", "SSE.Controllers.Toolbar.txtSymbol_gg": "Πολύ μεγαλύτερο από", "SSE.Controllers.Toolbar.txtSymbol_greater": "Μεγαλύτερο από", "SSE.Controllers.Toolbar.txtSymbol_in": "Στοιχείο του", "SSE.Controllers.Toolbar.txtSymbol_inc": "Αύξηση τιμής", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Άπειρο", "SSE.Controllers.Toolbar.txtSymbol_iota": "Γιώτα", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Κάπα", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Λάμδα", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Αριστερ<PERSON> βέλος", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Αριστερό-δεξιό βέλος", "SSE.Controllers.Toolbar.txtSymbol_leq": "Μικρότερο από ή ίσο με", "SSE.Controllers.Toolbar.txtSymbol_less": "Μικρότερο από", "SSE.Controllers.Toolbar.txtSymbol_ll": "Πολ<PERSON> μικρότερο από", "SSE.Controllers.Toolbar.txtSymbol_minus": "Πλην", "SSE.Controllers.Toolbar.txtSymbol_mp": "Πλην συν", "SSE.Controllers.Toolbar.txtSymbol_mu": "Μι", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Ανάδελτα", "SSE.Controllers.Toolbar.txtSymbol_neq": "Διάφορο από", "SSE.Controllers.Toolbar.txtSymbol_ni": "Περιέχει ως μέλος", "SSE.Controllers.Toolbar.txtSymbol_not": "Σύμβολο άρνησης", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Δεν υπάρχει", "SSE.Controllers.Toolbar.txtSymbol_nu": "Νι", "SSE.Controllers.Toolbar.txtSymbol_o": "Όμικρον", "SSE.Controllers.Toolbar.txtSymbol_omega": "Ωμέγα", "SSE.Controllers.Toolbar.txtSymbol_partial": "Μερικό διαφορικό", "SSE.Controllers.Toolbar.txtSymbol_percent": "Ποσοστό", "SSE.Controllers.Toolbar.txtSymbol_phi": "Φι", "SSE.Controllers.Toolbar.txtSymbol_pi": "Πι", "SSE.Controllers.Toolbar.txtSymbol_plus": "Συν", "SSE.Controllers.Toolbar.txtSymbol_pm": "Συν πλην", "SSE.Controllers.Toolbar.txtSymbol_propto": "Σε αναλογία με", "SSE.Controllers.Toolbar.txtSymbol_psi": "Ψι", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Τέταρτη ρίζα", "SSE.Controllers.Toolbar.txtSymbol_qed": "Τ<PERSON><PERSON><PERSON> απόδειξης", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Επάνω δεξιά διαγώνια έλλειψη", "SSE.Controllers.Toolbar.txtSymbol_rho": "Ρο", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Δεξ<PERSON> βέλος", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Σίγμα", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Σύμβολο ρίζας", "SSE.Controllers.Toolbar.txtSymbol_tau": "Ταυ", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Επομένως", "SSE.Controllers.Toolbar.txtSymbol_theta": "Θήτα", "SSE.Controllers.Toolbar.txtSymbol_times": "Σύμβολο πολλαπλασιασμού", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Επάνω βέλος", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Ύψιλον", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Παραλλαγή του έψιλον", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Παραλλαγή του φι", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Παραλλαγή του πι", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Παραλλαγή του ρο", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Τελικό σίγμα", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Παραλλαγή του θήτα", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Κατακόρυφη έλλειψη", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Ξι", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Ζήτα", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Σκούρα τεχνοτροπία πίνακα", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Φωτεινή τεχνοτροπία πίνακα", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Ενδιάμεση τεχνοτροπία πίνακα", "SSE.Controllers.Toolbar.warnLongOperation": "Η λειτουργία που πρόκειται να εκτελέσετε ίσως χρειαστεί πολύ χρόνο για να ολοκληρωθεί.<br>Θέλετε σίγουρα να συνεχίσετε;", "SSE.Controllers.Toolbar.warnMergeLostData": "Μόνο τα δεδομένα από το επάνω αριστερό κελί θα παραμείνουν στο συγχωνευμένο κελί.<br><PERSON><PERSON><PERSON><PERSON><PERSON> σίγουροι ότι θέλετε να συνεχίσετε;", "SSE.Controllers.Toolbar.warnNoRecommended": "Για να δημιουργήσετε ένα γράφημα, επιλέξτε τα κελιά που περιέχουν τα δεδομένα που θέλετε να χρησιμοποιήσετε.<br>Εάν έχετε ονόματα για τις σειρές και τις στήλες και θέλετε να τα χρησιμοποιήσετε ως ετικέτες, συμπεριλάβετέ τα στην επιλογή σας.", "SSE.Controllers.Viewport.textFreezePanes": "Σταθεροποίηση πλαισίων", "SSE.Controllers.Viewport.textFreezePanesShadow": "Εμφάνιση σκιάς σταθεροποιημένων πλαισίων", "SSE.Controllers.Viewport.textHideFBar": "Απόκρυψη Μπάρας Τύπων", "SSE.Controllers.Viewport.textHideGridlines": "Απόκρυψη γραμμών πλέγματος", "SSE.Controllers.Viewport.textHideHeadings": "Απόκρυψη Επικεφαλίδων", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Διαχωριστικ<PERSON> δεκαδικού", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Διαχωριστικ<PERSON> χιλιάδων", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Ρυθμίσεις αναγνώρισης αριθμητικών δεδομένων", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Προσδιοριστής κειμένου", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Προηγμένες ρυθμίσεις", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(κανένα)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Προσαρμοσμένο φίλτρο", "SSE.Views.AutoFilterDialog.textAddSelection": "Προσθήκη τρέχουσας επιλογής στο φίλτρο", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Κενά}", "SSE.Views.AutoFilterDialog.textSelectAll": "Επιλογ<PERSON> όλων ", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Επιλογή όλων των αποτελεσμάτων αναζήτησης", "SSE.Views.AutoFilterDialog.textWarning": "Προειδοποίηση", "SSE.Views.AutoFilterDialog.txtAboveAve": "Πάνω από τον μέσο όρο", "SSE.Views.AutoFilterDialog.txtAfter": "Μετά...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "Όλες οι ημερομηνίες της περιόδου", "SSE.Views.AutoFilterDialog.txtApril": "Απρίλιος", "SSE.Views.AutoFilterDialog.txtAugust": "Άύγουστος", "SSE.Views.AutoFilterDialog.txtBefore": "Πριν...", "SSE.Views.AutoFilterDialog.txtBegins": "Αρχίζει με...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Κάτω από τον μέσο όρο", "SSE.Views.AutoFilterDialog.txtBetween": "Μεταξύ...", "SSE.Views.AutoFilterDialog.txtClear": "Εκκαθάριση", "SSE.Views.AutoFilterDialog.txtContains": "Περιέχει...", "SSE.Views.AutoFilterDialog.txtDateFilter": "Φίλτρο ημερομηνίας", "SSE.Views.AutoFilterDialog.txtDecember": "Δεκέμβριος", "SSE.Views.AutoFilterDialog.txtEmpty": "Εισάγετε φίλτρο κελιού", "SSE.Views.AutoFilterDialog.txtEnds": "Τελειώνει με...", "SSE.Views.AutoFilterDialog.txtEquals": "Ισούται...", "SSE.Views.AutoFilterDialog.txtFebruary": "Φεβρουά<PERSON>ιος", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Φιλτράρισμα με χρώμα κελιών", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Φιλτράρισμα με χρώμα γραμματοσειράς", "SSE.Views.AutoFilterDialog.txtGreater": "Μεγαλύτερο από...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Μεγαλύτερο από ή ίσο με...", "SSE.Views.AutoFilterDialog.txtJanuary": "Ιανου<PERSON><PERSON>ιος", "SSE.Views.AutoFilterDialog.txtJuly": "<PERSON>ού<PERSON>ιος", "SSE.Views.AutoFilterDialog.txtJune": "Ιούνιος", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Φίλτρο ετικέτας", "SSE.Views.AutoFilterDialog.txtLastMonth": "Τον περασμένο μήνα", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Τελευταίο τρίμηνο", "SSE.Views.AutoFilterDialog.txtLastWeek": "Προηγούμενη εβδομάδα", "SSE.Views.AutoFilterDialog.txtLastYear": "Προηγούμενο έτος", "SSE.Views.AutoFilterDialog.txtLess": "Μικρότερο από...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Μικρότερο από ή ίσο με...", "SSE.Views.AutoFilterDialog.txtMarch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtMay": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtNextMonth": "Επόμενος μήνας", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Επόμενο τρίμηνο", "SSE.Views.AutoFilterDialog.txtNextWeek": "Την επόμενη εβδομάδα", "SSE.Views.AutoFilterDialog.txtNextYear": "Επόμενο έτος", "SSE.Views.AutoFilterDialog.txtNotBegins": "Δεν ξεκινά με...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Όχι ανάμεσα...", "SSE.Views.AutoFilterDialog.txtNotContains": "Δεν περιέχει...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Δεν τελειώνει με...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Δεν είναι ίσο με...", "SSE.Views.AutoFilterDialog.txtNovember": "Νοέμβριος", "SSE.Views.AutoFilterDialog.txtNumFilter": "Φίλτρο αριθμού", "SSE.Views.AutoFilterDialog.txtOctober": "Οκτώβριος", "SSE.Views.AutoFilterDialog.txtQuarter1": "Τρίμηνο 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Τρίμηνο 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Τρίμηνο 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Τρίμηνο 1", "SSE.Views.AutoFilterDialog.txtReapply": "Εφαρμογή Ξανά", "SSE.Views.AutoFilterDialog.txtSeptember": "Σεπτέμβριος", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Ταξινόμηση κατά το χρώμα κελιών", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Ταξινόμηση κατά το χρώμα γραμματοσειράς", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Ταξινόμηση από το υψηλότερο στο χαμηλότερο", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Ταξινόμηση από το χαμηλότερο στο υψηλότερο", "SSE.Views.AutoFilterDialog.txtSortOption": "Περισσότερες ρυθμίσεις ταξινόμησης...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Φίλτρ<PERSON> κειμένου", "SSE.Views.AutoFilterDialog.txtThisMonth": "Αυτό το μήνα", "SSE.Views.AutoFilterDialog.txtThisQuarter": "Αυτό το τρίμηνο", "SSE.Views.AutoFilterDialog.txtThisWeek": "Αυτή την εβδομάδα", "SSE.Views.AutoFilterDialog.txtThisYear": "Αυτό το έτος", "SSE.Views.AutoFilterDialog.txtTitle": "Φίλτρο", "SSE.Views.AutoFilterDialog.txtToday": "Σήμερα", "SSE.Views.AutoFilterDialog.txtTomorrow": "Αύριο", "SSE.Views.AutoFilterDialog.txtTop10": "Κορυφαία 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Φίλ<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtYearToDate": "Έτος σε ημερομηνία", "SSE.Views.AutoFilterDialog.txtYesterday": "Χθ<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.warnFilterError": "Απαιτείτ<PERSON>ι τουλάχιστον ένα πεδίο στην περιοχή Τιμών για να εφαρμοστεί ένα φίλτρο τιμών.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Πρέπει να επιλέξετε τουλάχιστον μία τιμή", "SSE.Views.CellEditor.textManager": "Διαχειριστής Ονομάτων", "SSE.Views.CellEditor.tipFormula": "Εισαγωγ<PERSON> συνάρτησης", "SSE.Views.CellRangeDialog.errorMaxRows": "ΣΦΑΛΜΑ! Ο μέγιστος αριθμός σειρών δεδομένων ανά γράφημα είναι 255", "SSE.Views.CellRangeDialog.errorStockChart": "Λανθασμένη διάταξη γραμμών. Για να δημιουργήσετε ένα γράφημα μετοχών τοποθετήστε τα δεδομένα στο φύλλο με την ακόλουθη σειρά:<br>τιμή ανοίγματος, μέγιστη τιμή, ελάχιστη τιμή, τιμή κλεισίματος.", "SSE.Views.CellRangeDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.CellRangeDialog.txtInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.CellRangeDialog.txtTitle": "Επιλογή εύρους δεδομένων", "SSE.Views.CellSettings.strShrink": "Σμίκρυνση για ταίριασμα", "SSE.Views.CellSettings.strWrap": "Αναδίπλωση κειμένου", "SSE.Views.CellSettings.textAngle": "Γωνία", "SSE.Views.CellSettings.textBackColor": "Χρώμα παρασκηνίου", "SSE.Views.CellSettings.textBackground": "Χρώμα παρασκηνίου", "SSE.Views.CellSettings.textBorderColor": "Χρώμα", "SSE.Views.CellSettings.textBorders": "Τεχνοτροπία περιγραμμάτων", "SSE.Views.CellSettings.textClearRule": "Εκκαθάριση Κανόνων", "SSE.Views.CellSettings.textColor": "Γέμισμα με χρώμα", "SSE.Views.CellSettings.textColorScales": "Κλίμακες χρωμάτων", "SSE.Views.CellSettings.textCondFormat": "Μορφοποίηση υπό όρους", "SSE.Views.CellSettings.textControl": "Έλεγχος κειμένου", "SSE.Views.CellSettings.textDataBars": "Μπάρες δεδομένων", "SSE.Views.CellSettings.textDirection": "Κατεύθυνση", "SSE.Views.CellSettings.textFill": "Γέμισμα", "SSE.Views.CellSettings.textForeground": "Χρώμα προσκηνίου", "SSE.Views.CellSettings.textGradient": "Σημεία διαβάθμισης", "SSE.Views.CellSettings.textGradientColor": "Χρώμα", "SSE.Views.CellSettings.textGradientFill": "Βαθμωτό γέμισμα", "SSE.Views.CellSettings.textIndent": "Εσοχή", "SSE.Views.CellSettings.textItems": "Αντικείμενα", "SSE.Views.CellSettings.textLinear": "Γραμμικός", "SSE.Views.CellSettings.textManageRule": "Διαχείριση κανόνων", "SSE.Views.CellSettings.textNewRule": "<PERSON><PERSON><PERSON> κανόνας", "SSE.Views.CellSettings.textNoFill": "<PERSON><PERSON><PERSON><PERSON><PERSON> γέμισμα", "SSE.Views.CellSettings.textOrientation": "Προσανατ<PERSON><PERSON>ισμ<PERSON>ς κειμένου", "SSE.Views.CellSettings.textPattern": "Μοτίβο", "SSE.Views.CellSettings.textPatternFill": "Μοτίβο", "SSE.Views.CellSettings.textPosition": "Θέση", "SSE.Views.CellSettings.textRadial": "Ακτινικ<PERSON>ς", "SSE.Views.CellSettings.textSelectBorders": "Επιλέξτε τα περιγράμματα που θέλετε να αλλάξετε εφαρμόζοντας την ανωτέρω επιλεγμένη τεχνοτροπία", "SSE.Views.CellSettings.textSelection": "Από την τρέχουσα επιλογή", "SSE.Views.CellSettings.textThisPivot": "Από αυτόν τον συγκεντρωτικό πίνακα", "SSE.Views.CellSettings.textThisSheet": "Από αυτό το φύλλο εργασίας", "SSE.Views.CellSettings.textThisTable": "Από αυτόν τον πίνακα", "SSE.Views.CellSettings.tipAddGradientPoint": "Προσθήκη σημείου διαβάθμισης", "SSE.Views.CellSettings.tipAll": "Ορισμ<PERSON>ς εξωτερικού περιγράμματος και όλων των εσωτερικών γραμμών", "SSE.Views.CellSettings.tipBottom": "Ορισμός μόνο εξωτερικού κάτω περιγράμματος", "SSE.Views.CellSettings.tipDiagD": "Ορισμός διαγώνιου κάτω περιγράμματος", "SSE.Views.CellSettings.tipDiagU": "Ορισμός διαγώνιου επάνω περιγράμματος", "SSE.Views.CellSettings.tipInner": "Ορισμός μόνο των εσωτερικών γραμμών", "SSE.Views.CellSettings.tipInnerHor": "Ορισμός μόνο των οριζόντιων εσωτερικών γραμμών", "SSE.Views.CellSettings.tipInnerVert": "Ορισμός μόνο των κατακόρυφων εσωτερικών γραμμών", "SSE.Views.CellSettings.tipLeft": "Ορισμός μόνο του εξωτερικού αριστερού περιγράμματος", "SSE.Views.CellSettings.tipNone": "<PERSON><PERSON><PERSON><PERSON><PERSON> κανένα περίγραμμα", "SSE.Views.CellSettings.tipOuter": "Ορισμός μόνο του εξωτερικού περιγράμματος", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Αφαίρεση σημείου διαβάθμισης", "SSE.Views.CellSettings.tipRight": "Ορισμός μόνο του εξωτερικού δεξιού περιγράμματος", "SSE.Views.CellSettings.tipTop": "Ορισμός μόνο του εξωτερικού πάνω περιγράμματος", "SSE.Views.ChartDataDialog.errorInFormula": "Υπάρχει κάποιο σφάλμα στον τύπο που εισαγάγατε.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Η αναφορά δεν είναι έγκυρη. Η αναφορά πρέπει να δείχνει σε ένα ανοικτό φύλλο εργασιών.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Ο μέγιστος αριθμός σημείων σε σειρά ανά γράφημα είναι 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Ο μέγιστος αριθμός σειρών δεδομένων ανά γράφημα είναι 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Η αναφορά δεν είναι έγκυρη. Οι αναφορές σε τίτλους, τιμ<PERSON>ς, μεγέθη ή ετικέτες δεδομένων πρέπει να είναι ένα μονα<PERSON>ικ<PERSON> κελί, γρα<PERSON><PERSON><PERSON> <PERSON> στήλη.", "SSE.Views.ChartDataDialog.errorNoValues": "Για να δημιουργηθεί γράφημα, πρέπει η σειρά να περιέχει τουλάχιστον μία τιμή.", "SSE.Views.ChartDataDialog.errorStockChart": "Λανθασμένη διάταξη γραμμών. Για να δημιουργήσετε ένα γράφημα μετοχών τοποθετήστε τα δεδομένα στο φύλλο με την ακόλουθη σειρά:<br>τιμή ανοίγματος, μέγιστη τιμή, ελάχιστη τιμή, τιμή κλεισίματος.", "SSE.Views.ChartDataDialog.textAdd": "Προσθήκη", "SSE.Views.ChartDataDialog.textCategory": "Ετικέτες οριζόντιου άξονα (κατηγορία)", "SSE.Views.ChartDataDialog.textData": "Εύρ<PERSON> δεδομένων γραφήματος", "SSE.Views.ChartDataDialog.textDelete": "Αφαίρεση", "SSE.Views.ChartDataDialog.textDown": "Κάτω", "SSE.Views.ChartDataDialog.textEdit": "Επεξεργασία", "SSE.Views.ChartDataDialog.textInvalidRange": "Μη έγκυρο εύρος κελιών", "SSE.Views.ChartDataDialog.textSelectData": "Επιλογή δεδομένων", "SSE.Views.ChartDataDialog.textSeries": "Καταχω<PERSON><PERSON><PERSON><PERSON><PERSON>ς υπομνήματ<PERSON> (σειρές)", "SSE.Views.ChartDataDialog.textSwitch": "Εναλλαγή γραμμής/στήλης", "SSE.Views.ChartDataDialog.textTitle": "Δεδομένα γραφήματος", "SSE.Views.ChartDataDialog.textUp": "Πάνω", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Υπάρχει κάποιο σφάλμα στον τύπο που εισαγάγατε.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Η αναφορά δεν είναι έγκυρη. Η αναφορά πρέπει να δείχνει σε ένα ανοικτό φύλλο εργασιών.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Ο μέγιστος αριθμός σημείων σε σειρά ανά γράφημα είναι 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Ο μέγιστος αριθμός σειρών δεδομένων ανά γράφημα είναι 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Η αναφορά δεν είναι έγκυρη. Οι αναφορές σε τίτλους, τιμ<PERSON>ς, μεγέθη ή ετικέτες δεδομένων πρέπει να είναι ένα μονα<PERSON>ικ<PERSON> κελί, γρα<PERSON><PERSON><PERSON> <PERSON> στήλη.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Για να δημιουργηθεί γράφημα, πρέπει η σειρά να περιέχει τουλάχιστον μία τιμή.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Λανθασμένη διάταξη γραμμών. Για να δημιουργήσετε ένα γράφημα μετοχών τοποθετήστε τα δεδομένα στο φύλλο με την ακόλουθη σειρά:<br>τιμή ανοίγματος, μέγιστη τιμή, ελάχιστη τιμή, τιμή κλεισίματος.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Μη έγκυρο εύρος κελιών", "SSE.Views.ChartDataRangeDialog.textSelectData": "Επιλογή δεδομένων", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Ε<PERSON><PERSON><PERSON> ετικέτας άξονα", "SSE.Views.ChartDataRangeDialog.txtChoose": "Επιλέξτε εύρος", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Όνομα σειράς", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Ετικέτες άξονα", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Επεξεργασία σειράς", "SSE.Views.ChartDataRangeDialog.txtValues": "Τιμές", "SSE.Views.ChartDataRangeDialog.txtXValues": "<PERSON>ι<PERSON><PERSON><PERSON> Χ", "SSE.Views.ChartDataRangeDialog.txtYValues": "Τιμές Υ", "SSE.Views.ChartSettings.errorMaxRows": "Ο μέγιστος αριθμός σειρών δεδομένων ανά γράφημα είναι 255.", "SSE.Views.ChartSettings.strLineWeight": "<PERSON><PERSON><PERSON><PERSON><PERSON> γραμμής", "SSE.Views.ChartSettings.strSparkColor": "Χρώμα", "SSE.Views.ChartSettings.strTemplate": "Πρότυπο", "SSE.Views.ChartSettings.text3dDepth": "Βάθος (% της βάσης)", "SSE.Views.ChartSettings.text3dHeight": "Ύψος (% βάσης)", "SSE.Views.ChartSettings.text3dRotation": "Περιστροφή 3Δ", "SSE.Views.ChartSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "SSE.Views.ChartSettings.textAutoscale": "Αυτόματη κλιμάκωση", "SSE.Views.ChartSettings.textBorderSizeErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια αριθμητική τιμή μεταξύ 0 pt και 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "Αλλαγή τύπου", "SSE.Views.ChartSettings.textChartType": "Αλλαγή τύπου γραφήματος", "SSE.Views.ChartSettings.textDefault": "Προεπιλεγμένη περιστροφή", "SSE.Views.ChartSettings.textDown": "Κάτω", "SSE.Views.ChartSettings.textEditData": "Επεξεργασία δεδομένων και τοποθεσίας", "SSE.Views.ChartSettings.textFirstPoint": "Πρώτο σημείο", "SSE.Views.ChartSettings.textHeight": "Ύψος", "SSE.Views.ChartSettings.textHighPoint": "Μέγιστο σημείο", "SSE.Views.ChartSettings.textKeepRatio": "Σταθερές αναλογίες", "SSE.Views.ChartSettings.textLastPoint": "Τελευτα<PERSON><PERSON> σημείο", "SSE.Views.ChartSettings.textLeft": "Αριστερά", "SSE.Views.ChartSettings.textLowPoint": "Χαμηλ<PERSON>ημείο", "SSE.Views.ChartSettings.textMarkers": "Δείκτες", "SSE.Views.ChartSettings.textNarrow": "Στενό πεδίο προβολής", "SSE.Views.ChartSettings.textNegativePoint": "Αρνητικ<PERSON> σημείο", "SSE.Views.ChartSettings.textPerspective": "Προοπτική", "SSE.Views.ChartSettings.textRanges": "Εύρ<PERSON> δεδομένων", "SSE.Views.ChartSettings.textRight": "Δεξιά", "SSE.Views.ChartSettings.textRightAngle": "Άξονες δεξιάς γωνίας", "SSE.Views.ChartSettings.textSelectData": "Επιλογή δεδομένων", "SSE.Views.ChartSettings.textShow": "Εμφάνιση", "SSE.Views.ChartSettings.textSize": "Μέγεθος", "SSE.Views.ChartSettings.textStyle": "Τεχνοτροπία", "SSE.Views.ChartSettings.textSwitch": "Εναλλαγή γραμμής/στήλης", "SSE.Views.ChartSettings.textType": "Τύπος", "SSE.Views.ChartSettings.textUp": "Επάνω", "SSE.Views.ChartSettings.textWiden": "Διεύρυνση του πεδίου προβολής", "SSE.Views.ChartSettings.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textX": "Περιστροφή X", "SSE.Views.ChartSettings.textY": "Περιστροφή Υ", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ΣΦΑΛΜΑ! Ο μέγιστος αριθμός σημείων σε σειρά ανά γράφημα είναι 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "ΣΦΑΛΜΑ! Ο μέγιστος αριθμός σειρών δεδομένων ανά γράφημα είναι 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Λανθασμένη διάταξη γραμμών. Για να δημιουργήσετε ένα γράφημα μετοχών τοποθετήστε τα δεδομένα στο φύλλο με την ακόλουθη σειρά:<br>τιμή ανοίγματος, μέγιστη τιμή, ελάχιστη τιμή, τιμή κλεισίματος.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Να μην αλλάζει θέση ή μέγεθος με τα κελιά", "SSE.Views.ChartSettingsDlg.textAlt": "Εναλλακτικό κείμενο", "SSE.Views.ChartSettingsDlg.textAltDescription": "Περιγραφή", "SSE.Views.ChartSettingsDlg.textAltTip": "Η εναλλακτική, κειμενική αναπαράσταση των πληροφοριών του οπτικού αντικειμένου, που θα αναγνωστεί σε ανθρώπους με προβλήματα όρασης ή γνωστικές αδυναμίες, για να κατανοήσουν καλύτερα τις πληροφορίες που περιέχονται στην εικόνα, σχήμα, γρά<PERSON>η<PERSON><PERSON> ή πίνακα.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Τίτλος", "SSE.Views.ChartSettingsDlg.textAuto": "Αυτόματα", "SSE.Views.ChartSettingsDlg.textAutoEach": "Αυτόματο για κάθε", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Διασ<PERSON><PERSON><PERSON><PERSON><PERSON>ς άξονα", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Επιλογ<PERSON><PERSON> αξόνων", "SSE.Views.ChartSettingsDlg.textAxisPos": "Θέση αξόνων", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Ρυθμίσεις άξονα", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Τίτλος", "SSE.Views.ChartSettingsDlg.textBase": "Βάση", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Μεταξύ διαβαθμίσεων", "SSE.Views.ChartSettingsDlg.textBillions": "Δισεκατομμύρια", "SSE.Views.ChartSettingsDlg.textBottom": "Κάτω", "SSE.Views.ChartSettingsDlg.textCategoryName": "Όνομα κατηγορίας", "SSE.Views.ChartSettingsDlg.textCenter": "Κέντρο", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Στοιχεία γραφήματος &<br>υπόμνημα γραφήματος", "SSE.Views.ChartSettingsDlg.textChartTitle": "Τίτλος διαγράμματος", "SSE.Views.ChartSettingsDlg.textCross": "Διασταύρωση", "SSE.Views.ChartSettingsDlg.textCustom": "Προσαρμογή", "SSE.Views.ChartSettingsDlg.textDataColumns": "σε στήλες", "SSE.Views.ChartSettingsDlg.textDataLabels": "Ετικέτες δεδομένων", "SSE.Views.ChartSettingsDlg.textDataRows": "σε γραμμές", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Εμφάνιση υπομνήματος", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Κρυφά και άδεια κελιά", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Σύνδεση σημείων δεδομένων με γραμμή", "SSE.Views.ChartSettingsDlg.textFit": "Προσαρμογή στο πλάτος", "SSE.Views.ChartSettingsDlg.textFixed": "Σταθερό", "SSE.Views.ChartSettingsDlg.textFormat": "Μορφή ετικέτας", "SSE.Views.ChartSettingsDlg.textGaps": "Κενά", "SSE.Views.ChartSettingsDlg.textGridLines": "Γραμ<PERSON><PERSON>ς πλέγματος", "SSE.Views.ChartSettingsDlg.textGroup": "Ομαδοποίηση μικρογραφήματος", "SSE.Views.ChartSettingsDlg.textHide": "Απόκρυψη", "SSE.Views.ChartSettingsDlg.textHideAxis": "Απόκρυψη άξονα", "SSE.Views.ChartSettingsDlg.textHigh": "Υψηλό", "SSE.Views.ChartSettingsDlg.textHorAxis": "Οριζ<PERSON><PERSON><PERSON><PERSON><PERSON> ά<PERSON>ο<PERSON>ας", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Δευτερε<PERSON>ων οριζ<PERSON>ντιος άξονας", "SSE.Views.ChartSettingsDlg.textHorizontal": "Οριζόντια", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Εκατοντάδες", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "Σε", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Εσωτερικ<PERSON> κάτω μέρος", "SSE.Views.ChartSettingsDlg.textInnerTop": "Εσωτερικ<PERSON> επάνω μέρος", "SSE.Views.ChartSettingsDlg.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.ChartSettingsDlg.textLabelDist": "Απόσταση ετικέτας άξονα", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Διάστημα μεταξύ ετικετών", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Επιλογ<PERSON>ς ετικέτας", "SSE.Views.ChartSettingsDlg.textLabelPos": "Θέση ετικέτας", "SSE.Views.ChartSettingsDlg.textLayout": "Διάταξη", "SSE.Views.ChartSettingsDlg.textLeft": "Αριστερά", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Αριστερή επικάλυψη", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Κάτω", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Αριστερά", "SSE.Views.ChartSettingsDlg.textLegendPos": "Υπόμνημα", "SSE.Views.ChartSettingsDlg.textLegendRight": "Δεξιά", "SSE.Views.ChartSettingsDlg.textLegendTop": "Επάνω", "SSE.Views.ChartSettingsDlg.textLines": "Γραμμές", "SSE.Views.ChartSettingsDlg.textLocationRange": "Εύρος τοποθεσίας", "SSE.Views.ChartSettingsDlg.textLogScale": "Λογαριθμική κλίμακα", "SSE.Views.ChartSettingsDlg.textLow": "Χαμηλό", "SSE.Views.ChartSettingsDlg.textMajor": "Βα<PERSON>ι<PERSON><PERSON>ς", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Βα<PERSON><PERSON><PERSON><PERSON>ς και δευτερεύουσες", "SSE.Views.ChartSettingsDlg.textMajorType": "<PERSON><PERSON><PERSON><PERSON>ος τύπος", "SSE.Views.ChartSettingsDlg.textManual": "Χε<PERSON>ρ<PERSON>κ<PERSON>νητα", "SSE.Views.ChartSettingsDlg.textMarkers": "Δείκτες", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Διάστημα μεταξύ σημαδιών", "SSE.Views.ChartSettingsDlg.textMaxValue": "Μέγιστη τιμή", "SSE.Views.ChartSettingsDlg.textMillions": "Εκατομμύρια", "SSE.Views.ChartSettingsDlg.textMinor": "Ελάσσον", "SSE.Views.ChartSettingsDlg.textMinorType": "Δευτερεύων τύπος", "SSE.Views.ChartSettingsDlg.textMinValue": "Ελάχιστη τιμή", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Δί<PERSON><PERSON>α στον άξονα", "SSE.Views.ChartSettingsDlg.textNone": "Κανένα", "SSE.Views.ChartSettingsDlg.textNoOverlay": "<PERSON><PERSON><PERSON><PERSON><PERSON> επικάλυψη", "SSE.Views.ChartSettingsDlg.textOneCell": "Μετακίνηση αλλά όχι αλλαγή μεγέθους με τα κελιά", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Επί των διαβαθμίσεων", "SSE.Views.ChartSettingsDlg.textOut": "Έξω", "SSE.Views.ChartSettingsDlg.textOuterTop": "Εξωτερικ<PERSON> επάνω", "SSE.Views.ChartSettingsDlg.textOverlay": "Επικάλυψη", "SSE.Views.ChartSettingsDlg.textReverse": "Τιμές σε αντίστροφη σειρά", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Αντίστροφη σειρά", "SSE.Views.ChartSettingsDlg.textRight": "Δεξιά", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Δεξιά επικάλυψη", "SSE.Views.ChartSettingsDlg.textRotated": "Περιστραμμένος", "SSE.Views.ChartSettingsDlg.textSameAll": "Ίδιο για όλα", "SSE.Views.ChartSettingsDlg.textSelectData": "Επιλογή δεδομένων", "SSE.Views.ChartSettingsDlg.textSeparator": "Διαχωριστής ετικετών δεδομένων", "SSE.Views.ChartSettingsDlg.textSeriesName": "Όνομα σειράς", "SSE.Views.ChartSettingsDlg.textShow": "Εμφάνιση", "SSE.Views.ChartSettingsDlg.textShowBorders": "Εμφάνιση περιγραμμάτων γραφήματος", "SSE.Views.ChartSettingsDlg.textShowData": "Εμφάνιση δεδομένων σε κρυμμένες γραμμές και στήλες", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Εμφάνιση κενών κελιών ως", "SSE.Views.ChartSettingsDlg.textShowEquation": "Εμφάνιση εξίσωσης στο γράφημα", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Εμφάνιση άξονα", "SSE.Views.ChartSettingsDlg.textShowValues": "Εμφάνιση τιμών γραφήματος", "SSE.Views.ChartSettingsDlg.textSingle": "Μονό μικρογράφημα", "SSE.Views.ChartSettingsDlg.textSmooth": "Ομαλ<PERSON>ς", "SSE.Views.ChartSettingsDlg.textSnap": "Ευθυγράμμιση σε κελί", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Εύρη μικρογραφήματος", "SSE.Views.ChartSettingsDlg.textStraight": "Ίσιες", "SSE.Views.ChartSettingsDlg.textStyle": "Τεχνοτροπία", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Χιλιάδες", "SSE.Views.ChartSettingsDlg.textTickOptions": "Επιλογές διαβαθμίσεων", "SSE.Views.ChartSettingsDlg.textTitle": "Γράφημα - σύνθετες ρυθμίσεις", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Μικρογράφημα - Προηγμένες ρυθμίσεις", "SSE.Views.ChartSettingsDlg.textTop": "Επάνω", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Επιλογ<PERSON>ς γραμμής τάσης", "SSE.Views.ChartSettingsDlg.textTrillions": "Τρισεκατομμύρια", "SSE.Views.ChartSettingsDlg.textTwoCell": "Μετακίνηση και αλλαγή μεγέθους με τα κελιά", "SSE.Views.ChartSettingsDlg.textType": "Τύπος", "SSE.Views.ChartSettingsDlg.textTypeData": "Τύπος & Δεδομένα", "SSE.Views.ChartSettingsDlg.textUnits": "Μον<PERSON><PERSON><PERSON>ς εμφάνισης", "SSE.Views.ChartSettingsDlg.textValue": "Τιμή", "SSE.Views.ChartSettingsDlg.textVertAxis": "Κατακ<PERSON><PERSON><PERSON><PERSON><PERSON> άξονας", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Δευτε<PERSON><PERSON><PERSON><PERSON><PERSON> κατακ<PERSON><PERSON><PERSON><PERSON>ος άξονας", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Τίτλος Άξονα Χ", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Τίτλος άξονα Υ", "SSE.Views.ChartSettingsDlg.textZero": "Μηδέν", "SSE.Views.ChartSettingsDlg.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.ChartTypeDialog.errorComboSeries": "Για να δημιουργήσετε συνδυαστικ<PERSON> γράφημα, επιλέξτε τουλάχιστον δύο σειρές δεδομένων.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Ο επιλεγμένος τύπος γραφήματος απαιτεί τον δευτερεύοντα άξονα που χρησιμοποιείται ήδη από υφιστάμενο γράφημα. Επιλέξτε άλλο τύπο γραφήματος.", "SSE.Views.ChartTypeDialog.textSecondary": "Δευτερεύων άξονας", "SSE.Views.ChartTypeDialog.textSeries": "Σειρά", "SSE.Views.ChartTypeDialog.textStyle": "Τεχνοτροπία", "SSE.Views.ChartTypeDialog.textTitle": "Τύ<PERSON><PERSON> γραφήματος", "SSE.Views.ChartTypeDialog.textType": "Τύπος", "SSE.Views.ChartWizardDialog.errorComboSeries": "Για να δημιουργήσετε συνδυαστικ<PERSON> γράφημα, επιλέξτε τουλάχιστον δύο σειρές δεδομένων.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "Ο μέγιστος αριθμός σημείων σε σειρά ανά γράφημα είναι 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "Ο μέγιστος αριθμός σειρών δεδομένων ανά γράφημα είναι 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "Ο επιλεγμένος τύπος γραφήματος απαιτεί τον δευτερεύοντα άξονα που χρησιμοποιείται ήδη από υφιστάμενο γράφημα. Επιλέξτε άλλο τύπο γραφήματος.", "SSE.Views.ChartWizardDialog.errorStockChart": "Εσφαλμένη σειρά γραμμών. Για να δημιουργήσετε ένα γράφημα μετοχών, τοποθετήστε τα δεδομένα στο φύλλο με την ακόλουθη σειρά: τιμή ανοίγματος, μέγιστη τιμή, ελάχιστη τιμή, τιμή κλεισίματος.", "SSE.Views.ChartWizardDialog.textRecommended": "Προτεινόμενα", "SSE.Views.ChartWizardDialog.textSecondary": "Δευτερεύων άξονας", "SSE.Views.ChartWizardDialog.textSeries": "Σειρά", "SSE.Views.ChartWizardDialog.textTitle": "Εισαγωγή γραφήματος", "SSE.Views.ChartWizardDialog.textTitleChange": "Αλλαγή τύπου γραφήματος", "SSE.Views.ChartWizardDialog.textType": "Τύπος", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Επιλέξτε τον τύπο γραφήματος και τον άξονα για τη σειρά δεδομένων σας", "SSE.Views.CreatePivotDialog.textDataRange": "Εύ<PERSON><PERSON> δεδομένων πηγής", "SSE.Views.CreatePivotDialog.textDestination": "Επιλέξτε θέση πίνακα", "SSE.Views.CreatePivotDialog.textExist": "Υφιστάμενο φύλλο εργασίας", "SSE.Views.CreatePivotDialog.textInvalidRange": "Μη έγκυρο εύρος κελιών", "SSE.Views.CreatePivotDialog.textNew": "Νέο φύλλο εργασίας", "SSE.Views.CreatePivotDialog.textSelectData": "Επιλογή δεδομένων", "SSE.Views.CreatePivotDialog.textTitle": "Δημιουργ<PERSON>α συγκεντρωτικού πίνακα", "SSE.Views.CreatePivotDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.CreateSparklineDialog.textDataRange": "Εύ<PERSON><PERSON> δεδομένων πηγής", "SSE.Views.CreateSparklineDialog.textDestination": "Επιλογή θέσης για τα μικρογραφήματα", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Μη έγκυρο εύρος κελιών", "SSE.Views.CreateSparklineDialog.textSelectData": "Επιλογή δεδομένων", "SSE.Views.CreateSparklineDialog.textTitle": "Δημιουργ<PERSON>α μικρογραφημάτων", "SSE.Views.CreateSparklineDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.DataTab.capBtnGroup": "Ομάδα", "SSE.Views.DataTab.capBtnTextCustomSort": "Προσαρμοσμένη ταξινόμηση", "SSE.Views.DataTab.capBtnTextDataValidation": "Επικύρωση δεδομένων", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Αφαίρεση διπλότυπων", "SSE.Views.DataTab.capBtnTextToCol": "Κείμενο σε στήλες", "SSE.Views.DataTab.capBtnUngroup": "Κατάργηση ομαδοποίησης", "SSE.Views.DataTab.capDataExternalLinks": "Εξωτερικοί σύνδεσμοι", "SSE.Views.DataTab.capDataFromText": "Λήψη δεδομένων", "SSE.Views.DataTab.capGoalSeek": "Αναζήτη<PERSON>η Στόχου", "SSE.Views.DataTab.mniFromFile": "Από τοπικό αρχείο TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "Από διεύθυνση ιστού TXT/CSV", "SSE.Views.DataTab.mniFromXMLFile": "Από τοπικό XML", "SSE.Views.DataTab.textBelow": "Περίληψη γραμμών κάτω από τις γραμμές λεπτομέρειας", "SSE.Views.DataTab.textClear": "Εκκαθάριση περιγράμματος", "SSE.Views.DataTab.textColumns": "Κατάργηση ομαδοποίησης στηλών", "SSE.Views.DataTab.textGroupColumns": "Ομαδοποίη<PERSON>η στηλών", "SSE.Views.DataTab.textGroupRows": "Ομαδοποίηση γραμμών", "SSE.Views.DataTab.textRightOf": "Περίληψη στηλών στα δεξιά των στηλών λεπτομερειών", "SSE.Views.DataTab.textRows": "Αναίρεση ομαδοποίησης γραμμών", "SSE.Views.DataTab.tipCustomSort": "Προσαρμοσμένη ταξινόμηση", "SSE.Views.DataTab.tipDataFromText": "Λήψ<PERSON> δεδομένων από αρχείο", "SSE.Views.DataTab.tipDataValidation": "Επικύρωση δεδομένων", "SSE.Views.DataTab.tipExternalLinks": "Προβολ<PERSON> άλλων αρχείων με τα οποία είναι συνδεδεμένο αυτό το υπολογιστικό φύλλο", "SSE.Views.DataTab.tipGoalSeek": "Ανεύρευση της σωστής εισόδου για την τιμή που επιθυμείτε", "SSE.Views.DataTab.tipGroup": "Ομαδοποίηση εύρους κελιών", "SSE.Views.DataTab.tipRemDuplicates": "Αφαίρεση διπλότυπων γραμμών από ένα φύλλο", "SSE.Views.DataTab.tipToColumns": "Διαχω<PERSON>ισμ<PERSON>ς κειμένου κελιού σε στήλες", "SSE.Views.DataTab.tipUngroup": "Κατάργηση ομαδοποίησης εύρους κελιών", "SSE.Views.DataValidationDialog.errorFormula": "Η τιμή οδηγεί σε σφάλμα με τον τρέχοντα υπολογισμό. Θέλετε να συνεχίσετε;", "SSE.Views.DataValidationDialog.errorInvalid": "Η τιμή που εισαγάγατε στο πεδίο \"{0}\" δεν είναι έγκυρη.", "SSE.Views.DataValidationDialog.errorInvalidDate": "Η ημερομηνία για το πεδίο \"{0}\" δεν είναι έγκυρη.", "SSE.Views.DataValidationDialog.errorInvalidList": "Η πηγή της λίστας πρέπει να είναι μια λίστα στοιχείων χωρισμένων με ειδικό χαρακτήρα ή μια αναφορά σε μια γραμμή ή στήλη.", "SSE.Views.DataValidationDialog.errorInvalidTime": "Ο χρόνος που εισαγάγατε στο πεδίο \"{0}\" δεν είναι έγκυρος.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "Το πεδίο \"{1}\" πρέπει να είναι μεγαλύτερο από ή ίσο με το πεδίο \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Πρέπει να βάλετε μια τιμή και στο πεδίο \"{0}\" και στο πεδίο \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Πρέπει να βάλετε μια τιμή στο πεδίο \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "Ένα επώνυμο εύρος που ορίσατε δεν μπορεί να βρεθεί.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Αρνητικές τιμές δεν μπορούν να χρησιμοποιηθούν σε συνθήκες \"{0}\". ", "SSE.Views.DataValidationDialog.errorNotNumeric": "Το πεδίο \"{0}\" πρέπει να έχει αριθμητική τιμή, αριθμητική έκφραση ή να αναφέρεται σε κελί με αριθμητική τιμή.", "SSE.Views.DataValidationDialog.strError": "Ειδοποίηση σφάλματος", "SSE.Views.DataValidationDialog.strInput": "Εισαγωγή μηνύματος", "SSE.Views.DataValidationDialog.strSettings": "Ρυθμίσεις", "SSE.Views.DataValidationDialog.textAlert": "Συναγ<PERSON><PERSON><PERSON><PERSON>ς", "SSE.Views.DataValidationDialog.textAllow": "Επιτρέπεται", "SSE.Views.DataValidationDialog.textApply": "Εφαρμογ<PERSON> αυτών των αλλα<PERSON>ών σε όλα τα κελιά με τις ίδιες ρυθμίσεις", "SSE.Views.DataValidationDialog.textCellSelected": "Όταν επιλέγεται κελί να εμφανίζεται αυτό το μήνυμα εισόδου", "SSE.Views.DataValidationDialog.textCompare": "Σύγκριση με", "SSE.Views.DataValidationDialog.textData": "Δεδομένα", "SSE.Views.DataValidationDialog.textEndDate": "Ημερομηνία τέλους", "SSE.Views.DataValidationDialog.textEndTime": "Ώρα Τέλους", "SSE.Views.DataValidationDialog.textError": "Μήνυμα λάθους", "SSE.Views.DataValidationDialog.textFormula": "Τύπος", "SSE.Views.DataValidationDialog.textIgnore": "Αγνόηση κενών", "SSE.Views.DataValidationDialog.textInput": "Εισαγωγή μηνύματος", "SSE.Views.DataValidationDialog.textMax": "Μέγιστο", "SSE.Views.DataValidationDialog.textMessage": "Μήνυμα", "SSE.Views.DataValidationDialog.textMin": "Ελάχιστο", "SSE.Views.DataValidationDialog.textSelectData": "Επιλογή δεδομένων", "SSE.Views.DataValidationDialog.textShowDropDown": "Εμφάνιση αναδυόμενης λίστας σε κελί", "SSE.Views.DataValidationDialog.textShowError": "Εμφάνιση προειδοποίησης σφάλματος μετά την εισαγωγή μη έγκυρων δεδομένων", "SSE.Views.DataValidationDialog.textShowInput": "Εμφάνιση μηνύ<PERSON>α<PERSON>ος εισόδου κατά την επιλογή κελιού", "SSE.Views.DataValidationDialog.textSource": "Πηγή", "SSE.Views.DataValidationDialog.textStartDate": "Ημερομηνία έναρξης", "SSE.Views.DataValidationDialog.textStartTime": "Ώρα Έναρξης", "SSE.Views.DataValidationDialog.textStop": "Διακοπή", "SSE.Views.DataValidationDialog.textStyle": "Τεχνοτροπία", "SSE.Views.DataValidationDialog.textTitle": "Τίτλος", "SSE.Views.DataValidationDialog.textUserEnters": "Όταν ο χρήστης εισάγει μη έγκυρα δεδομένα να εμφανίζεται αυτή η προειδοποίηση σφάλματος", "SSE.Views.DataValidationDialog.txtAny": "Οποιαδήποτε τιμή", "SSE.Views.DataValidationDialog.txtBetween": "μεταξύ", "SSE.Views.DataValidationDialog.txtDate": "Ημερομηνία", "SSE.Views.DataValidationDialog.txtDecimal": "Δεκαδικ<PERSON>ς", "SSE.Views.DataValidationDialog.txtElTime": "<PERSON>ρ<PERSON><PERSON><PERSON> που πέρασε", "SSE.Views.DataValidationDialog.txtEndDate": "Ημερομηνία τέλους", "SSE.Views.DataValidationDialog.txtEndTime": "Ώρα τέλους", "SSE.Views.DataValidationDialog.txtEqual": "ισούται", "SSE.Views.DataValidationDialog.txtGreaterThan": "μεγαλύτερο από", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "μεγαλύτερο από ή ίσο με", "SSE.Views.DataValidationDialog.txtLength": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtLessThan": "μικρότερο από", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "μικρότερο από ή ίσο με", "SSE.Views.DataValidationDialog.txtList": "Λίστα", "SSE.Views.DataValidationDialog.txtNotBetween": "όχι ανάμεσα", "SSE.Views.DataValidationDialog.txtNotEqual": "δεν είναι ίσο με", "SSE.Views.DataValidationDialog.txtOther": "Άλλο", "SSE.Views.DataValidationDialog.txtStartDate": "Ημερομηνία έναρξης", "SSE.Views.DataValidationDialog.txtStartTime": "Ώρα έναρξης", "SSE.Views.DataValidationDialog.txtTextLength": "<PERSON><PERSON><PERSON><PERSON> κειμένου", "SSE.Views.DataValidationDialog.txtTime": "Ώρα", "SSE.Views.DataValidationDialog.txtWhole": "Ολόκληρος αριθμός", "SSE.Views.DigitalFilterDialog.capAnd": "Και", "SSE.Views.DigitalFilterDialog.capCondition1": "ισούται", "SSE.Views.DigitalFilterDialog.capCondition10": "δεν τελειώνει με", "SSE.Views.DigitalFilterDialog.capCondition11": "περιέχει", "SSE.Views.DigitalFilterDialog.capCondition12": "δεν περιέχει", "SSE.Views.DigitalFilterDialog.capCondition2": "δεν είναι ίσο με", "SSE.Views.DigitalFilterDialog.capCondition3": "είναι μεγαλύτερο από", "SSE.Views.DigitalFilterDialog.capCondition30": "είναι μετά", "SSE.Views.DigitalFilterDialog.capCondition4": "είναι μεγαλύτερο από ή ίσο με", "SSE.Views.DigitalFilterDialog.capCondition40": "είναι μετά ή ίσο με", "SSE.Views.DigitalFilterDialog.capCondition5": "είναι μικρότερο από", "SSE.Views.DigitalFilterDialog.capCondition50": "είναι πριν", "SSE.Views.DigitalFilterDialog.capCondition6": "είναι μικρότερο από ή ίσο με", "SSE.Views.DigitalFilterDialog.capCondition60": "είναι πριν ή ίσο με", "SSE.Views.DigitalFilterDialog.capCondition7": "αρχίζει με", "SSE.Views.DigitalFilterDialog.capCondition8": "δεν ξεκινά με", "SSE.Views.DigitalFilterDialog.capCondition9": "τελειώνει με", "SSE.Views.DigitalFilterDialog.capOr": "Ή", "SSE.Views.DigitalFilterDialog.textNoFilter": "χωρ<PERSON>ς φίλτρο", "SSE.Views.DigitalFilterDialog.textShowRows": "Εμφάνιση γραμμών όπου", "SSE.Views.DigitalFilterDialog.textUse1": "Χρήση του ? για αναπαράσταση οποιουδήποτε χαρακτήρα", "SSE.Views.DigitalFilterDialog.textUse2": "Χρήση του * για αναπαράσταση οποιασδήποτε συμβολοσειράς", "SSE.Views.DigitalFilterDialog.txtSelectDate": "Επιλογή ημερομηνίας", "SSE.Views.DigitalFilterDialog.txtTitle": "Προσαρμοσμένο φίλτρο", "SSE.Views.DocumentHolder.advancedEquationText": "Ρυθμίσεις εξίσωσης", "SSE.Views.DocumentHolder.advancedImgText": "Προηγμένες ρυθμίσεις εικόνας", "SSE.Views.DocumentHolder.advancedShapeText": "Προηγμένες ρυθμίσεις σχήματος", "SSE.Views.DocumentHolder.advancedSlicerText": "Προηγμένες ρυθμίσεις αναλυτή", "SSE.Views.DocumentHolder.allLinearText": "Όλα - Γραμμικό", "SSE.Views.DocumentHolder.allProfText": "Όλα - Επαγγελματικό", "SSE.Views.DocumentHolder.bottomCellText": "Στοίχιση Κάτω", "SSE.Views.DocumentHolder.bulletsText": "Κουκκίδες και Αρίθμηση", "SSE.Views.DocumentHolder.centerCellText": "Στοίχιση στη Μέση", "SSE.Views.DocumentHolder.chartDataText": "Επιλογή δεδομένων γραφήματος", "SSE.Views.DocumentHolder.chartText": "Προηγμένες ρυθμίσεις γραφήματος", "SSE.Views.DocumentHolder.chartTypeText": "Αλλαγή Τύπου Γραφήματος", "SSE.Views.DocumentHolder.currLinearText": "Τρέχον - Γραμμικό", "SSE.Views.DocumentHolder.currProfText": "Τρέχον - Επαγγελματικό", "SSE.Views.DocumentHolder.deleteColumnText": "Στήλη", "SSE.Views.DocumentHolder.deleteRowText": "Γραμμή", "SSE.Views.DocumentHolder.deleteTableText": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "SSE.Views.DocumentHolder.direct270Text": "Περιστροφή κειμένου επάνω", "SSE.Views.DocumentHolder.direct90Text": "Περιστροφή κειμένου κάτω", "SSE.Views.DocumentHolder.directHText": "Οριζόντια", "SSE.Views.DocumentHolder.directionText": "Κατεύθυνση κειμένου", "SSE.Views.DocumentHolder.editChartText": "Επεξεργασία δεδομένων", "SSE.Views.DocumentHolder.editHyperlinkText": "Επεξεργασία Υπερσυνδέσμου", "SSE.Views.DocumentHolder.hideEqToolbar": "Απόκρυψη γραμμής εργαλείων εξίσωσης", "SSE.Views.DocumentHolder.insertColumnLeftText": "Στήλη αριστερά", "SSE.Views.DocumentHolder.insertColumnRightText": "Στήλη δεξιά", "SSE.Views.DocumentHolder.insertRowAboveText": "Γραμμή Από Πάνω", "SSE.Views.DocumentHolder.insertRowBelowText": "Γραμμή Από <PERSON>ά<PERSON>ω", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "Πραγματικ<PERSON> Μέγεθος", "SSE.Views.DocumentHolder.removeHyperlinkText": "Αφαίρεση Υπερσυνδέσμου", "SSE.Views.DocumentHolder.selectColumnText": "Ολόκληρη Στήλη", "SSE.Views.DocumentHolder.selectDataText": "Δεδομένα στήλης", "SSE.Views.DocumentHolder.selectRowText": "Γραμμή", "SSE.Views.DocumentHolder.selectTableText": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "SSE.Views.DocumentHolder.showEqToolbar": "Εμφάνιση γραμμής εργαλείων εξίσωσης", "SSE.Views.DocumentHolder.strDelete": "Αφαίρεση Υπογραφής", "SSE.Views.DocumentHolder.strDetails": "Λεπτομέρειες Υπογραφής", "SSE.Views.DocumentHolder.strSetup": "Ρύθμιση Υπογραφής", "SSE.Views.DocumentHolder.strSign": "Σύμβολο", "SSE.Views.DocumentHolder.textAlign": "Στοίχιση", "SSE.Views.DocumentHolder.textArrange": "Τακτοποίηση", "SSE.Views.DocumentHolder.textArrangeBack": "Μεταφ<PERSON><PERSON><PERSON> στο Παρασκήνιο", "SSE.Views.DocumentHolder.textArrangeBackward": "Μεταφορ<PERSON> πίσω", "SSE.Views.DocumentHolder.textArrangeForward": "Μεταφορ<PERSON> εμπρός", "SSE.Views.DocumentHolder.textArrangeFront": "Μετα<PERSON><PERSON><PERSON><PERSON> στο προσκήνιο", "SSE.Views.DocumentHolder.textAverage": "Μέ<PERSON><PERSON> Όρος", "SSE.Views.DocumentHolder.textBullets": "Κουκκίδες", "SSE.Views.DocumentHolder.textCopyCells": "Αντιγρα<PERSON><PERSON> κελιών", "SSE.Views.DocumentHolder.textCount": "Μέτρηση", "SSE.Views.DocumentHolder.textCrop": "Περικοπή", "SSE.Views.DocumentHolder.textCropFill": "Γέμισμα", "SSE.Views.DocumentHolder.textCropFit": "Προσαρμογή", "SSE.Views.DocumentHolder.textEditPoints": "Επεξεργασία σημείων", "SSE.Views.DocumentHolder.textEntriesList": "Επιλογή από αναδυόμενη λίστα", "SSE.Views.DocumentHolder.textFillDays": "Γεμίστε ημέρες", "SSE.Views.DocumentHolder.textFillFormatOnly": "Συμπλήρωση μορφοποίησης μόνο", "SSE.Views.DocumentHolder.textFillMonths": "Συμπληρώστε μήνες", "SSE.Views.DocumentHolder.textFillSeries": "Γεμίστε τη σειρά", "SSE.Views.DocumentHolder.textFillWeekdays": "Γεμίστε τις καθημερινές", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Γέμισμα χωρίς μορφοποίηση", "SSE.Views.DocumentHolder.textFillYears": "Γεμίστε χρόνια", "SSE.Views.DocumentHolder.textFlashFill": "Γρήγορο γέμισμα", "SSE.Views.DocumentHolder.textFlipH": "Οριζόντια Περιστροφή", "SSE.Views.DocumentHolder.textFlipV": "Κατακόρυφη Περιστροφή", "SSE.Views.DocumentHolder.textFreezePanes": "Σταθεροποίηση πλαισίων", "SSE.Views.DocumentHolder.textFromFile": "Από αρχείο", "SSE.Views.DocumentHolder.textFromStorage": "Από αποθηκευτικό χώρο", "SSE.Views.DocumentHolder.textFromUrl": "Από διεύθυνση URL", "SSE.Views.DocumentHolder.textGrowthTrend": "Τάση ανάπτυξης", "SSE.Views.DocumentHolder.textLinearTrend": "Γραμμική τάση", "SSE.Views.DocumentHolder.textListSettings": "Ρυθμίσεις λίστας", "SSE.Views.DocumentHolder.textMacro": "Ανάθεση μακροεντολής", "SSE.Views.DocumentHolder.textMax": "Μέγιστο", "SSE.Views.DocumentHolder.textMin": "Ελάχιστο", "SSE.Views.DocumentHolder.textMore": "Περισσότερες συναρτήσεις", "SSE.Views.DocumentHolder.textMoreFormats": "Περισσότερες μορφές", "SSE.Views.DocumentHolder.textNone": "Κανένα", "SSE.Views.DocumentHolder.textNumbering": "Αρίθμηση", "SSE.Views.DocumentHolder.textReplace": "Αντικατάσταση εικόνας", "SSE.Views.DocumentHolder.textResetCrop": "Επαναφορ<PERSON> περικοπής", "SSE.Views.DocumentHolder.textRotate": "Περιστροφή", "SSE.Views.DocumentHolder.textRotate270": "Περιστροφή 90° Αριστερόστροφα", "SSE.Views.DocumentHolder.textRotate90": "Περιστροφή 90° Δεξιόστροφα", "SSE.Views.DocumentHolder.textSaveAsPicture": "Αποθήκευση ως εικόνα", "SSE.Views.DocumentHolder.textSeries": "Σειρά", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Στοίχιση κάτω", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Στοίχιση στο κέντρο", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Στοίχιση αριστερά", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Σοίχιση στη μέση", "SSE.Views.DocumentHolder.textShapeAlignRight": "Στοίχιση δεξιά", "SSE.Views.DocumentHolder.textShapeAlignTop": "Στοίχιση επάνω", "SSE.Views.DocumentHolder.textShapesMerge": "Συγχώνευση σχημάτων", "SSE.Views.DocumentHolder.textStdDev": "Τυπική Απόκλιση", "SSE.Views.DocumentHolder.textSum": "Άθροισμα", "SSE.Views.DocumentHolder.textUndo": "Αναίρεση", "SSE.Views.DocumentHolder.textUnFreezePanes": "Απελευθέρωση παραθύρων", "SSE.Views.DocumentHolder.textVar": "Διαφορά", "SSE.Views.DocumentHolder.tipMarkersArrow": "Κου<PERSON><PERSON><PERSON><PERSON>ς βέλη", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Κουκίδες τσεκαρίσματος", "SSE.Views.DocumentHolder.tipMarkersDash": "Κουκ<PERSON>δες παύλας", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Κου<PERSON><PERSON>δες πλήρους ρόμβου", "SSE.Views.DocumentHolder.tipMarkersFRound": "Κουκίδες πλήρεις στρογγυλές", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Κουκίδες πλήρεις τετράγωνες", "SSE.Views.DocumentHolder.tipMarkersHRound": "Κουκ<PERSON>δες κούφιες στρογγυλές", "SSE.Views.DocumentHolder.tipMarkersStar": "Κουκ<PERSON>δες αστέρια", "SSE.Views.DocumentHolder.topCellText": "Στοίχιση επάνω", "SSE.Views.DocumentHolder.txtAccounting": "Λογιστική", "SSE.Views.DocumentHolder.txtAddComment": "Προσθήκη σχολίου", "SSE.Views.DocumentHolder.txtAddNamedRange": "Προσδιορισμ<PERSON>ς ονόματος", "SSE.Views.DocumentHolder.txtArrange": "Τακτοποίηση", "SSE.Views.DocumentHolder.txtAscending": "Αύξουσα", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Αυτόματη προσαρμογή πλάτους στήλης", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Αυτόματη προσαρμογή ύψους γραμμής", "SSE.Views.DocumentHolder.txtAverage": "<PERSON><PERSON><PERSON><PERSON> όρ<PERSON>", "SSE.Views.DocumentHolder.txtCellFormat": "Μορφοποίηση κελιών", "SSE.Views.DocumentHolder.txtClear": "Εκκαθάριση", "SSE.Views.DocumentHolder.txtClearAll": "Όλα", "SSE.Views.DocumentHolder.txtClearComments": "Σχόλια", "SSE.Views.DocumentHolder.txtClearFormat": "Μορφή", "SSE.Views.DocumentHolder.txtClearHyper": "Υπερσύνδεσμοι", "SSE.Views.DocumentHolder.txtClearPivotField": "Απαλοι<PERSON><PERSON> φίλτρου από {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Εκκαθάριση επιλεγμένων ομάδων μικρογραφημάτων (sparklines)", "SSE.Views.DocumentHolder.txtClearSparklines": "Εκκαθάριση επιλεγμένων μικρογραφημάτων (sparklines)", "SSE.Views.DocumentHolder.txtClearText": "Κείμενο", "SSE.Views.DocumentHolder.txtCollapse": "Κλείσιμο", "SSE.Views.DocumentHolder.txtCollapseEntire": "Σύμπτυξη ολόκληρου του πεδίου", "SSE.Views.DocumentHolder.txtColumn": "Ολόκληρη στήλη", "SSE.Views.DocumentHolder.txtColumnWidth": "Ορισμός πλάτους στήλης", "SSE.Views.DocumentHolder.txtCondFormat": "Μορφοποίηση υπό όρους", "SSE.Views.DocumentHolder.txtCopy": "Αντιγραφή", "SSE.Views.DocumentHolder.txtCount": "Μέτρηση", "SSE.Views.DocumentHolder.txtCurrency": "Νόμισμα", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Προσαρ<PERSON>οσμ<PERSON>ν<PERSON> πλάτος στήλης", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Προσαρμοσμέν<PERSON> ύ<PERSON>ος γραμμής", "SSE.Views.DocumentHolder.txtCustomSort": "Προσαρμοσμένη ταξινόμηση", "SSE.Views.DocumentHolder.txtCut": "Αποκοπή", "SSE.Views.DocumentHolder.txtDateLong": "Πλήρης ημερομηνία", "SSE.Views.DocumentHolder.txtDateShort": "Σύντομη ημερομηνία", "SSE.Views.DocumentHolder.txtDelete": "Διαγραφή", "SSE.Views.DocumentHolder.txtDelField": "Αφαίρεση", "SSE.Views.DocumentHolder.txtDescending": "Φθίνουσα", "SSE.Views.DocumentHolder.txtDifference": "Διαφορά από", "SSE.Views.DocumentHolder.txtDistribHor": "Διανομή οριζόντια", "SSE.Views.DocumentHolder.txtDistribVert": "Διανο<PERSON>ή κάθετα", "SSE.Views.DocumentHolder.txtEditComment": "Επεξεργασ<PERSON>α σχολίου", "SSE.Views.DocumentHolder.txtEditObject": "Επεξεργα<PERSON><PERSON>α αντικειμένου", "SSE.Views.DocumentHolder.txtExpand": "Επέκταση", "SSE.Views.DocumentHolder.txtExpandCollapse": "Ανάπτυξη/Σύμπτυξη", "SSE.Views.DocumentHolder.txtExpandEntire": "Ανάπτυξη ολόκληρου του πεδίου", "SSE.Views.DocumentHolder.txtFieldSettings": "Ρυθμίσεις πεδίων", "SSE.Views.DocumentHolder.txtFilter": "Φίλτρο", "SSE.Views.DocumentHolder.txtFilterCellColor": "Φιλτράρισμα με χρώμα κελιού", "SSE.Views.DocumentHolder.txtFilterFontColor": "Φιλτράρισμα με χρώμα γραμματοσειράς", "SSE.Views.DocumentHolder.txtFilterValue": "Φιλτράρισμα με την τιμή του επιλεγμένου κελιού", "SSE.Views.DocumentHolder.txtFormula": "Εισαγωγ<PERSON> συνάρτησης", "SSE.Views.DocumentHolder.txtFraction": "Κλάσμα", "SSE.Views.DocumentHolder.txtGeneral": "Γενικά", "SSE.Views.DocumentHolder.txtGetLink": "Λή<PERSON>η συνδέσμου για αυτό το εύρος", "SSE.Views.DocumentHolder.txtGrandTotal": "Τελικό σύνολο", "SSE.Views.DocumentHolder.txtGroup": "Ομάδα", "SSE.Views.DocumentHolder.txtHide": "Απόκρυψη", "SSE.Views.DocumentHolder.txtIndex": "Ευρετήριο", "SSE.Views.DocumentHolder.txtInsert": "Εισαγωγή", "SSE.Views.DocumentHolder.txtInsHyperlink": "Υπερσύνδεσμος", "SSE.Views.DocumentHolder.txtInsImage": "Εισαγωγ<PERSON> εικόνας από αρχείο", "SSE.Views.DocumentHolder.txtInsImageUrl": "Εισαγωγ<PERSON> εικόνας από διεύθυνση URL", "SSE.Views.DocumentHolder.txtLabelFilter": "Φίλτρα ετικετών", "SSE.Views.DocumentHolder.txtMax": "Μέγιστο", "SSE.Views.DocumentHolder.txtMin": "Ελάχιστο", "SSE.Views.DocumentHolder.txtMoreOptions": "Περισσότερες επιλογές", "SSE.Views.DocumentHolder.txtNormal": "<PERSON><PERSON><PERSON><PERSON><PERSON> υπολογισμό", "SSE.Views.DocumentHolder.txtNumber": "Αριθμός", "SSE.Views.DocumentHolder.txtNumFormat": "Μορφή Αριθμού", "SSE.Views.DocumentHolder.txtPaste": "Επικόλληση", "SSE.Views.DocumentHolder.txtPercent": "% του", "SSE.Views.DocumentHolder.txtPercentage": "Ποσοστό", "SSE.Views.DocumentHolder.txtPercentDiff": "% διαφορά από", "SSE.Views.DocumentHolder.txtPercentOfCol": "% του συνόλου της στήλης", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% του γενικού συνόλου", "SSE.Views.DocumentHolder.txtPercentOfParent": "% του συνόλου των μητρικών", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% συνόλου γονικής στήλης", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% του συνόλου της γονικής γραμμής", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% τρέχοντος συνόλου σε", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% του συνόλου της γραμμής", "SSE.Views.DocumentHolder.txtPivotSettings": "Ρυθμίσεις συγκεντρωτικού πίνακα", "SSE.Views.DocumentHolder.txtProduct": "Γινόμενο", "SSE.Views.DocumentHolder.txtRankAscending": "Κατάταξη από το μικρότερο στο μεγαλύτερο", "SSE.Views.DocumentHolder.txtRankDescending": "Κατάταξη από το μεγαλύτερο στο μικρότερο", "SSE.Views.DocumentHolder.txtReapply": "Εφαρμογή Ξανά", "SSE.Views.DocumentHolder.txtRefresh": "Ανανέωση", "SSE.Views.DocumentHolder.txtRow": "Ολόκληρη γραμμή", "SSE.Views.DocumentHolder.txtRowHeight": "Ορισμός ύψους γραμμής", "SSE.Views.DocumentHolder.txtRunTotal": "Τρέχον σύνολο σε", "SSE.Views.DocumentHolder.txtScientific": "Επιστημονική", "SSE.Views.DocumentHolder.txtSelect": "Επιλογή", "SSE.Views.DocumentHolder.txtShiftDown": "Ολίσθηση κελιών κάτω", "SSE.Views.DocumentHolder.txtShiftLeft": "Ολίσθηση κελιών αριστερά", "SSE.Views.DocumentHolder.txtShiftRight": "Ολίσθηση κελιών δεξιά", "SSE.Views.DocumentHolder.txtShiftUp": "Ολίσθηση κελιών πάνω", "SSE.Views.DocumentHolder.txtShow": "Εμφάνιση", "SSE.Views.DocumentHolder.txtShowAs": "Εμφάνιση τιμών ως", "SSE.Views.DocumentHolder.txtShowComment": "Εμφάνιση σχολίου", "SSE.Views.DocumentHolder.txtShowDetails": "Εμφάνιση λεπτομερειών", "SSE.Views.DocumentHolder.txtSort": "Ταξινόμηση", "SSE.Views.DocumentHolder.txtSortCellColor": "Επιλεγμένο χρώμα κελιού στην κορυφή", "SSE.Views.DocumentHolder.txtSortFontColor": "Επιλεγμένο χρώμα γραμματοσειράς στην κορυφή", "SSE.Views.DocumentHolder.txtSortOption": "Περισσότερες επιλογές ταξινόμησης", "SSE.Views.DocumentHolder.txtSparklines": "Μικρογραφήματα", "SSE.Views.DocumentHolder.txtSubtotalField": "Μερικό άθροισμα", "SSE.Views.DocumentHolder.txtSum": "Άθροισμα", "SSE.Views.DocumentHolder.txtSummarize": "Σύνοψη τιμών κατά", "SSE.Views.DocumentHolder.txtText": "Κείμενο", "SSE.Views.DocumentHolder.txtTextAdvanced": "Προηγμένες ρυθμίσεις παραγράφου", "SSE.Views.DocumentHolder.txtTime": "Ώρα", "SSE.Views.DocumentHolder.txtTop10": "Κορυφαία 10", "SSE.Views.DocumentHolder.txtUngroup": "Κατάργηση ομαδοποίησης", "SSE.Views.DocumentHolder.txtValueFieldSettings": "Ρυθμίσεις πεδίου τιμών", "SSE.Views.DocumentHolder.txtValueFilter": "Φίλτρα τιμών", "SSE.Views.DocumentHolder.txtWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "Κατακόρυφη Στοίχιση", "SSE.Views.ExternalLinksDlg.closeButtonText": "Κλείσιμο", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Αυτόματη ενημέρωση δεδομένων από τις συνδεδεμένες προελεύσεις", "SSE.Views.ExternalLinksDlg.textChange": "Αλλαγή πηγής", "SSE.Views.ExternalLinksDlg.textDelete": "Σπάσιμο συνδέσμων", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Σπάσιμο όλων των συνδέσμων", "SSE.Views.ExternalLinksDlg.textOk": "Εντάξει", "SSE.Views.ExternalLinksDlg.textOpen": "Open source", "SSE.Views.ExternalLinksDlg.textSource": "Πηγή", "SSE.Views.ExternalLinksDlg.textStatus": "Κατάσταση", "SSE.Views.ExternalLinksDlg.textUnknown": "Άγνωστο", "SSE.Views.ExternalLinksDlg.textUpdate": "Ενημέρωση τιμών", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Ενημέρωση όλων", "SSE.Views.ExternalLinksDlg.textUpdating": "Ενημέρωση...", "SSE.Views.ExternalLinksDlg.txtTitle": "Εξωτερικοί σύνδεσμοι", "SSE.Views.FieldSettingsDialog.strLayout": "Διάταξη", "SSE.Views.FieldSettingsDialog.strSubtotals": "Μερικά σύνολα", "SSE.Views.FieldSettingsDialog.textNumFormat": "Μορφή αριθμού", "SSE.Views.FieldSettingsDialog.textReport": "Φόρμα αναφοράς", "SSE.Views.FieldSettingsDialog.textTitle": "Ρυθμίσεις πεδίων", "SSE.Views.FieldSettingsDialog.txtAverage": "Μέ<PERSON><PERSON> Όρος", "SSE.Views.FieldSettingsDialog.txtBlank": "Εισαγω<PERSON><PERSON> κενών γραμμών μετά από κάθε στοιχείο", "SSE.Views.FieldSettingsDialog.txtBottom": "Εμφάνισης στο κάτω μέρος της ομάδας", "SSE.Views.FieldSettingsDialog.txtCompact": "Συμπαγές", "SSE.Views.FieldSettingsDialog.txtCount": "Μέτρηση", "SSE.Views.FieldSettingsDialog.txtCountNums": "Μέτρηση αριθμών", "SSE.Views.FieldSettingsDialog.txtCustomName": "Προσαρμοσμένο όνομα", "SSE.Views.FieldSettingsDialog.txtEmpty": "Εμφάνιση στοιχείων χωρίς καθόλου δεδομένα", "SSE.Views.FieldSettingsDialog.txtMax": "Μέγιστο", "SSE.Views.FieldSettingsDialog.txtMin": "Ελάχιστο", "SSE.Views.FieldSettingsDialog.txtOutline": "Περίγραμμα", "SSE.Views.FieldSettingsDialog.txtProduct": "Γινόμενο", "SSE.Views.FieldSettingsDialog.txtRepeat": "Επανάληψη ετικετών στοιχείων σε κάθε γραμμή", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Εμφάνιση μερικών συνόλων", "SSE.Views.FieldSettingsDialog.txtSourceName": "Όνομα πηγής:", "SSE.Views.FieldSettingsDialog.txtStdDev": "Τυπική Απόκλιση", "SSE.Views.FieldSettingsDialog.txtStdDevp": "Τυπική απόκλιση πληθυσμού", "SSE.Views.FieldSettingsDialog.txtSum": "Άθροισμα", "SSE.Views.FieldSettingsDialog.txtSummarize": "Συναρτήσεις για μερικά σύνολα", "SSE.Views.FieldSettingsDialog.txtTabular": "Σε μορφή πίνακα", "SSE.Views.FieldSettingsDialog.txtTop": "Εμφάνιση στο πάνω μέρος της ομάδας", "SSE.Views.FieldSettingsDialog.txtVar": "Διαφορά", "SSE.Views.FieldSettingsDialog.txtVarp": "Διακύμανση πληθυσμού", "SSE.Views.FileMenu.ariaFileMenu": "Μενού αρχείου", "SSE.Views.FileMenu.btnBackCaption": "Άνοιγμα θέσης αρχείου", "SSE.Views.FileMenu.btnCloseEditor": "Κλείσιμο αρχείου", "SSE.Views.FileMenu.btnCloseMenuCaption": "Πίσω", "SSE.Views.FileMenu.btnCreateNewCaption": "Δημιουργ<PERSON>α νέου", "SSE.Views.FileMenu.btnDownloadCaption": "Λήψη ως", "SSE.Views.FileMenu.btnExitCaption": "Κλείσιμο", "SSE.Views.FileMenu.btnExportToPDFCaption": "Εξαγωγή σε PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "Άνοιγμα", "SSE.Views.FileMenu.btnHelpCaption": "Βοήθεια", "SSE.Views.FileMenu.btnHistoryCaption": "Ιστορικ<PERSON> Εκδόσεων", "SSE.Views.FileMenu.btnInfoCaption": "Πληροφορίες", "SSE.Views.FileMenu.btnPrintCaption": "Εκτύπωση", "SSE.Views.FileMenu.btnProtectCaption": "Προστασία", "SSE.Views.FileMenu.btnRecentFilesCaption": "Άνοιγμα πρόσφατου", "SSE.Views.FileMenu.btnRenameCaption": "Μετονομασία", "SSE.Views.FileMenu.btnReturnCaption": "Πίσ<PERSON> στο Λογιστικό Φύλλο", "SSE.Views.FileMenu.btnRightsCaption": "Δικαιώματα Πρόσβασης", "SSE.Views.FileMenu.btnSaveAsCaption": "Αποθήκευση ως", "SSE.Views.FileMenu.btnSaveCaption": "Αποθήκευση", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Αποθήκευση αντιγράφου ως", "SSE.Views.FileMenu.btnSettingsCaption": "Προηγμένες ρυθμίσεις", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Μετάβαση σε Κινητό", "SSE.Views.FileMenu.btnToEditCaption": "Επεξεργασία υπολογιστικού Φύλλου", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Κενό Φύλλο Εργασίας", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Δημιουργ<PERSON>α νέου", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Εφαρμογή", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Προσθήκη Συγγραφέα", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Προσθήκη ιδιότητας", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Προσθήκη κειμένου", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Εφαρμογή", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Συγγραφέας", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Αλλαγή δικαιωμάτων πρόσβασης", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Σχόλιο", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Κοινό", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Δημιουργήθηκε", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Ιδιότητα εγγράφου", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Τελευταία Τροποποίηση Από", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Τελευταία Τροποποίηση", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "Όχι", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Ιδιοκτήτης", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Τοποθεσία", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Ιδιότητες", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Υπάρχει ήδη ιδιότητα με αυτή την τιμή", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Άτομα που έχουν δικαιώματα", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Πληροφορίες υπολογιστικού φύλλου", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Θέμα", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Ετικέτες", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Τίτλος", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Μεταφορτώθηκε", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "Ναι", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Δικαιώματα πρόσβασης", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Αλλαγή δικαιωμάτων πρόσβασης", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Άτομα που έχουν δικαιώματα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Εφαρμογή", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Κατάσταση συν-επεξεργασίας", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Χρησιμοποιήστε το σύστημα ημερομηνίας 1904", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Διαχωριστικ<PERSON> δεκαδικού", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Γλώσσ<PERSON> λεξικού", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Ενεργοποίηση επαναληπτικού υπολογισμού", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Γρήγ<PERSON><PERSON>η", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Υποδείξεις γραμματοσειρών", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Γλώσσα τύπου", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Παράδειγμα: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Εμφάνιση επεξήγησης εργαλείου λειτουργίας", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Εμφάνιση οριζόντιας γραμμής κύλισης", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Αγνόηση λέξεων με ΚΕΦΑΛΑΙΑ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Αγνόηση λέξεων με αριθμούς", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Ρυθμίσεις μακροεντολών", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Μέγιστη αλλαγή", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Μέγιστες επαναλήψεις", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Εμφάνιση επιλογών επικόλλησης κατά την επικόλληση περιεχομένου", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Τεχνοτροπία Παραπομπών R1C1", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Τοπικές ρυθμίσεις", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Παράδειγμα:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "Διεπαφή RTL", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Εμφάνιση σχολίων στο φύλλο", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Εμφάνιση αλλαγ<PERSON><PERSON> από άλλους χρήστες", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Εμφάνιση επιλυμένων σχολίων", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Προσκόλληση στο πλέγμα κατά την κύλιση", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Αυστηρή", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Στυλ καρτέλας", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Θέμα διεπαφής", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Διαχωριστικ<PERSON> χιλιάδων", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Μονάδα μέτρησης", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Χρήση διαχω<PERSON><PERSON><PERSON><PERSON>ικών από τις τοπικές ρυθμίσεις", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Εμφάνιση κατακόρυφης γραμμής κύλισης", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Προεπιλεγμένη τιμή εστίασης", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Κάθε 10 Λεπτά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Κάθε 30 Λεπτά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Κάθε 5 Λεπτά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "κάθε ώρα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Αυτόματη ανάκτηση", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Αυτόματη αποθήκευση", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Απενεργοποιημένο", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "Γέμισμα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Αποθήκευση ενδιάμεσων εκδόσεων", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "Γραμμή", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "κάθε λεπτό", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Τεχνοτροπία Παραπομπών", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "Προηγμένες ρυθμίσεις", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "Εμφάνιση", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Επιλογ<PERSON>ς αυτόματης διόρθωσης...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Λευκ<PERSON>ρωσικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Βουλγάρικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Καταλανικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Προεπιλεγμένη κατάσταση λανθάνουσας μνήμης", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Υπολογισμός", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Εκατοστό", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Συνεργασία", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Τσέχικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Προσαρμογή γρήγορης πρόσβασης", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Δανέζικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Γερμανικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Επεξεργασία και αποθήκευση", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Ελληνικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "Αγγλικ<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Η εγγραφή σας δεν μπορεί να χρησιμοποιηθεί. Ενδέχεται να απαιτείται ακέραιος ή δεκαδικός αριθμός.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Ισπανικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Συν-επεξεργασία σε πραγματικό χρόνο. Όλες οι αλλαγές αποθηκεύονται αυτόματα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Φινλανδικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "Γαλλικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Ουγγρικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "Αρμενικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Ινδονησιακά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Ίντσα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Ιταλικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Ιαπωνικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Κορεάτικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Τελευταία χρήση", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Λαοϊκά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Λετονικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "Ως OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Εγγεν<PERSON>ς", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Νορβηγικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Ολλανδικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Πολωνικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Διόρθωση κειμένου", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Σημείο", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Πορ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Βραζιλίας)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "Πορτ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Πορτογαλίας)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Εμφάνιση του κουμπιού γρήγορης εκτύπωσης στην κεφαλίδα του προγράμματος επεξεργασίας", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "Το έγγραφο θα εκτυπωθεί στον τελευταίο επιλεγμένο ή προεπιλεγμένο εκτυπωτή", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Περιοχή", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Ρουμάνικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Ρώσικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Ενεργοποίηση όλων", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Ενεργοποίηση όλων των μακροεντολών χωρίς ειδοποίηση", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Ενεργοποίηση υποστήριξης προγράμματος ανάγνωσης οθόνης", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Προεπιλεγμένη κατεύθυνση φύλλου", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "Αυτή η ρύθμιση θα επηρεάσει μόνο τα νέα φύλλα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Από αριστερά προς τα δεξιά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Από δεξιά προς τα αριστερά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Σλοβάκικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Σλοβένικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Απενεργοποίηση όλων", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Απενεργοποίηση όλων των μακροεντολών χωρίς ειδοποίηση", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Χρησιμοποιήστε το κουμπί \"Αποθήκευση\" για να συγχρονίσετε τις αλλαγές που κάνετε εσείς και άλλοι", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Σουηδικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Χρήση χρώματος γραμμής εργαλείων ως φόντο καρτελών", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Τουρκικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ουκρανικά", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Χρησιμοποιήστε το πλήκτρο Alt για πλοήγηση στη διεπαφή χρήστη χρησιμοποιώντας το πληκτρολόγιο", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Χρησιμοποιήστε το πλήκτρο επιλογής για πλοήγηση στη διεπαφή χρήστη χρησιμοποιώντας το πληκτρολόγιο", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Βιετναμέζικα", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Εμφάνιση ειδοποίησης", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Απενεργοποίηση όλων των μακροεντολών με μια ειδοποίηση", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "Ως Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "<PERSON><PERSON><PERSON><PERSON> εργασ<PERSON>ας", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "Κινέζικα", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Προειδοποίηση", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Με συνθηματικό", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Προστα<PERSON><PERSON><PERSON> υπολογιστικού φύλλου", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Με υπογραφή", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Έχουν προστεθεί έγκυρες υπογραφές στο υπολογιστικό φύλλο. <br>Το υπολογιστικό φύλλο προστατεύεται από επεξεργασία.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Διασφαλίστε την ακεραιότητα του υπολογιστικού φύλλου<br>προσθέτοντας μια αόρατη ψηφιακή υπογραφή", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Επεξεργασία υπολογιστικού φύλλου", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Η επεξεργασία θα αφαιρέσει τις υπογραφές από το υπολογιστικό φύλλο.<br>Θέλετε σίγουρα να συνεχίσετε;", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Αυτό το υπολογιστικό φύλλο προστατεύτηκε με συνθηματικό", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Κρυπτογράφηση αυτού του υπολογιστικού φύλλου με κωδικό", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Αυτό το υπολογιστικό φύλλο πρέπει να υπογραφεί.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Προστέθηκαν έγκυρες υπογραφές στο λογιστικό φύλλο. Το φύλλο προστατεύεται από επεξεργασία.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Κάποιες από τις ψηφιακές υπογραφές στο υπολογιστικό φύλλο δεν είναι έγκυρες ή δεν ήταν δυνατό να επιβεβαιωθούν. Το υπολογιστικό φύλλο προστατεύεται από επεξεργασία.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Προβολή υπογραφών", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Λήψη ως", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Αποθήκευση αντιγράφου ως", "SSE.Views.FillSeriesDialog.textAuto": "Αυτόματη συμπλήρωση", "SSE.Views.FillSeriesDialog.textCols": "Στήλες", "SSE.Views.FillSeriesDialog.textDate": "Ημερομηνία", "SSE.Views.FillSeriesDialog.textDateUnit": "Μονάδα ημερομηνίας", "SSE.Views.FillSeriesDialog.textDay": "Ημέρα", "SSE.Views.FillSeriesDialog.textGrowth": "Ανάπτυξη", "SSE.Views.FillSeriesDialog.textLinear": "Γραμμική", "SSE.Views.FillSeriesDialog.textMonth": "Μήνας", "SSE.Views.FillSeriesDialog.textRows": "Γραμμές", "SSE.Views.FillSeriesDialog.textSeries": "Σειρά σε", "SSE.Views.FillSeriesDialog.textStep": "Αξία βήματος", "SSE.Views.FillSeriesDialog.textStop": "Αξία διακοπής", "SSE.Views.FillSeriesDialog.textTitle": "Σειρά", "SSE.Views.FillSeriesDialog.textTrend": "Τάση", "SSE.Views.FillSeriesDialog.textType": "Τύπος", "SSE.Views.FillSeriesDialog.textWeek": "Καθημερινή", "SSE.Views.FillSeriesDialog.textYear": "Έτος", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Η εγγραφή σας δεν μπορεί να χρησιμοποιηθεί. Ενδέχεται να απαιτείται ακέραιος ή δεκαδικός αριθμός.", "SSE.Views.FormatRulesEditDlg.fillColor": "Χρώμα γεμίσματος", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Προειδοποίηση", "SSE.Views.FormatRulesEditDlg.text2Scales": "Δίχρωμη κλίμακα", "SSE.Views.FormatRulesEditDlg.text3Scales": "Τρίχρωμη κλίμακα", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Όλα τα περιγράμματα", "SSE.Views.FormatRulesEditDlg.textAppearance": "Μορφή μπάρας", "SSE.Views.FormatRulesEditDlg.textApply": "Εφαρμογή σε εύρος", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Αυτόματα", "SSE.Views.FormatRulesEditDlg.textAxis": "Άξονας", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Κατεύθυνση μπάρας", "SSE.Views.FormatRulesEditDlg.textBold": "Έντονα", "SSE.Views.FormatRulesEditDlg.textBorder": "Περίγραμμα", "SSE.Views.FormatRulesEditDlg.textBordersColor": "Χρώμα περιγραμμάτων", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Τεχνοτροπία περιγράμματος", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Κάτω περιγράμματα", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Δεν είναι δυνατή η προσθήκη της μορφοποίησης υπό όρους.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Μέσον κελιού", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Εσωτερι<PERSON><PERSON> κατακόρυφα περιγράμματα", "SSE.Views.FormatRulesEditDlg.textClear": "Εκκαθάριση", "SSE.Views.FormatRulesEditDlg.textColor": "Χρώμα κειμένου", "SSE.Views.FormatRulesEditDlg.textContext": "Συμφραζόμενα", "SSE.Views.FormatRulesEditDlg.textCustom": "Προσαρμογή", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Διαγ<PERSON><PERSON><PERSON><PERSON> κάτω όριο", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Διαγών<PERSON><PERSON> επάνω όριο", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Εισάγετε έναν έγκυρο τύπο.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "Ο τύπος που εισάγατε δεν έχει τιμή αριθμού, ημερομην<PERSON>ας, ώρας ή συμβολοσειράς.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Εισάγετε μια τιμή.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "Η τιμή που βάλατε δεν είναι έγκυρος αριθμός, ημερομην<PERSON><PERSON>, ώρα ή συμβολοσειρά.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "Η τιμή για το {0} πρέπει να είναι μεγαλύτερη της τιμής για το {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Εισάγετε έναν αριθμό μεταξύ {0} και {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Γέμισμα", "SSE.Views.FormatRulesEditDlg.textFormat": "Μορφή", "SSE.Views.FormatRulesEditDlg.textFormula": "Τύπος", "SSE.Views.FormatRulesEditDlg.textGradient": "Βαθμωτό", "SSE.Views.FormatRulesEditDlg.textIconLabel": "όταν {0} {1} και", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "όταν {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "όταν η τιμή είναι", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Ένα ή περισσότερα εύρη δεδομένων εικονιδίων επικαλύπτονται.<br>Προ<PERSON><PERSON>ρμόστε τις τιμές των ευρών δεδομένων εικονιδίων ώστε να μην επικαλύπτονται.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Τεχνοτροπία εικονιδίου", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Εσωτερικό όριο", "SSE.Views.FormatRulesEditDlg.textInvalid": "Μη έγκυρο εύρος δεδομένων.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.FormatRulesEditDlg.textItalic": "Πλάγια", "SSE.Views.FormatRulesEditDlg.textItem": "Αντικείμενο", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Αριστερ<PERSON> προς δεξιά", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Αριστερά περιγράμματα", "SSE.Views.FormatRulesEditDlg.textLongBar": "μακρύτερη μπάρα", "SSE.Views.FormatRulesEditDlg.textMaximum": "Μέγιστο", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Μέγιστο σημείο", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Εσωτερικά οριζόντια περιγράμματα", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Με<PERSON><PERSON><PERSON><PERSON> σημείο", "SSE.Views.FormatRulesEditDlg.textMinimum": "Ελάχιστο", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Ελάχιστο σημείο", "SSE.Views.FormatRulesEditDlg.textNegative": "Αρνητική", "SSE.Views.FormatRulesEditDlg.textNewColor": "Περισσότερα χρώματα", "SSE.Views.FormatRulesEditDlg.textNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> περιγράμματα", "SSE.Views.FormatRulesEditDlg.textNone": "Κανένα", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Μία ή περισσότερες από τις καθορισμένες τιμές δεν είναι έγκυρο ποσοστό.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Η καθορισμένη {0} τιμή δεν είναι έγκυρο ποσοστό επί τοις εκατό.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Μία ή περισσότερες από τις καθορισμένες τιμές δεν είναι έγκυρη εκατοστιαία τιμή.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Η καθορισμένη {0} τιμή δεν είναι έγκυρη εκατοστιαία τιμή.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Εξωτερικ<PERSON> περιγράμματα", "SSE.Views.FormatRulesEditDlg.textPercent": "Επί τοις εκατό", "SSE.Views.FormatRulesEditDlg.textPercentile": "Εκατοστιαία τιμή", "SSE.Views.FormatRulesEditDlg.textPosition": "Θέση", "SSE.Views.FormatRulesEditDlg.textPositive": "Θετικ<PERSON>ς", "SSE.Views.FormatRulesEditDlg.textPresets": "Προεπιλογές", "SSE.Views.FormatRulesEditDlg.textPreview": "Προεπισκόπηση", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Δεν μπορείτε να χρησιμοποιήσετε σχετικές αναφορές στα κριτήρια μορφοποίησης υπό όρους για χρωματικές κλίμακες, μπάρες δεδομένων και σύνολα εικονιδίων.", "SSE.Views.FormatRulesEditDlg.textReverse": "Αντιστροφή διάταξης εικονιδίων", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Δεξιά προς αριστερά", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Δεξιά περιγράμματα", "SSE.Views.FormatRulesEditDlg.textRule": "Κανόνας", "SSE.Views.FormatRulesEditDlg.textSameAs": "Ίδιο με το θετικό", "SSE.Views.FormatRulesEditDlg.textSelectData": "Επιλογή δεδομένων", "SSE.Views.FormatRulesEditDlg.textShortBar": "κοντύτερη μπάρα", "SSE.Views.FormatRulesEditDlg.textShowBar": "Εμφάνιση μπάρας μόνο", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Εμφάνιση εικονιδίου μόνο", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Αυτός ο τύπος αναφοράς δεν μπορεί να χρησιμοποιηθεί σε τύπο προσαρμοσμένης μορφοποίησης.<br>Αλλάξτε την αναφορά σε μεμονωμένο κελί, ή χρησιμοποιήστε την αναφορά σε μια συνάρτηση, όπως =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Συμπαγές", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Διαγραφή", "SSE.Views.FormatRulesEditDlg.textSubscript": "Δείκτης", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Εκθέτης", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Επάνω περιγράμματα", "SSE.Views.FormatRulesEditDlg.textUnderline": "Υπογράμμιση", "SSE.Views.FormatRulesEditDlg.tipBorders": "Περιγράμματα", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Μορφή Αριθμού", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Λογιστική", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Νόμισμα", "SSE.Views.FormatRulesEditDlg.txtDate": "Ημερομηνία", "SSE.Views.FormatRulesEditDlg.txtDateLong": "Πλήρης ημερομηνία", "SSE.Views.FormatRulesEditDlg.txtDateShort": "Σύντομη ημερομηνία", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.FormatRulesEditDlg.txtFraction": "Κλάσμα", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Γενικά", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "<PERSON><PERSON><PERSON><PERSON><PERSON> εικονίδιο", "SSE.Views.FormatRulesEditDlg.txtNumber": "Αριθμός", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Ποσοστό επί τοις εκατό", "SSE.Views.FormatRulesEditDlg.txtScientific": "Επιστημονική", "SSE.Views.FormatRulesEditDlg.txtText": "Κείμενο", "SSE.Views.FormatRulesEditDlg.txtTime": "Ώρα", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Επεξεργα<PERSON><PERSON>α κανόνα μορφοποίησης", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "<PERSON><PERSON><PERSON> κανόνας μορφοποίησης", "SSE.Views.FormatRulesManagerDlg.guestText": "Επισκέπτης", "SSE.Views.FormatRulesManagerDlg.lockText": "Κλειδωμένο", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 τυπική απόκλιση πάνω από το μέσο όρο", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 τυπική απόκλιση κάτω από το μέσο όρο", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 τυπική απόκλιση πάνω από το μέσο όρο", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 τυπική απόκλιση κάτω από το μέσο όρο", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 τυπική απόκλιση πάνω από το μέσο όρο", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 τυπική απόκλιση κάτω από το μέσο όρο", "SSE.Views.FormatRulesManagerDlg.textAbove": "Πάνω από τον μέσο όρο", "SSE.Views.FormatRulesManagerDlg.textApply": "Εφαρμογή σε", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Η τιμή του κελιού ξεκινά με", "SSE.Views.FormatRulesManagerDlg.textBelow": "Κάτω από τον μέσο όρο", "SSE.Views.FormatRulesManagerDlg.textBetween": "είναι μεταξύ {0] και {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Τιμή κελιού", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Βαθμωτή χρωματική κλίμακα", "SSE.Views.FormatRulesManagerDlg.textContains": "Η τιμή του κελιού περιέχει", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Το κελί περιέχει μια κενή τιμή", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Το κελί περιέχει σφάλμα", "SSE.Views.FormatRulesManagerDlg.textDelete": "Διαγραφή", "SSE.Views.FormatRulesManagerDlg.textDown": "Μετακίνηση κανόνα κάτω", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Διπλότυπες τιμές", "SSE.Views.FormatRulesManagerDlg.textEdit": "Επεξεργασία", "SSE.Views.FormatRulesManagerDlg.textEnds": "Η τιμή του κελιού τελειώνει με", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Ίσο με ή μεγαλύτερο του μέσου όρου", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Ίσο με ή μικρότερο του μέσου όρου", "SSE.Views.FormatRulesManagerDlg.textFormat": "Μορφή", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Σύνολο εικονιδίων", "SSE.Views.FormatRulesManagerDlg.textNew": "Νέο", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "δεν είναι μεταξύ {0} και {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Η τιμή του κελιού δεν περιέχει", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Το κελί δεν περιέχει μια κενή τιμή", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Το κελί δεν περιέχει σφάλμα", "SSE.Views.FormatRulesManagerDlg.textRules": "Κανόνες", "SSE.Views.FormatRulesManagerDlg.textScope": "Εμφάνιση κανόνων μορφοποίησης για ", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Επιλογή δεδομένων", "SSE.Views.FormatRulesManagerDlg.textSelection": "Τρέχουσα επιλογή", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "Αυτ<PERSON><PERSON> ο συγκεντρωτικ<PERSON>ς πίνακας", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Αυτό το φύλλο εργασίας", "SSE.Views.FormatRulesManagerDlg.textThisTable": "Αυτ<PERSON><PERSON> ο πίνακας", "SSE.Views.FormatRulesManagerDlg.textUnique": "Μοναδικές τιμές", "SSE.Views.FormatRulesManagerDlg.textUp": "Μετακίνηση κανόνα πάνω", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Αυτό το στοιχείο τελεί υπό επεξεργασία από άλλο χρήστη.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Μορφοποίηση υπό όρους", "SSE.Views.FormatSettingsDialog.textCategory": "Κατηγορία", "SSE.Views.FormatSettingsDialog.textDecimal": "Δεκαδικ<PERSON>ς", "SSE.Views.FormatSettingsDialog.textFormat": "Μορφή", "SSE.Views.FormatSettingsDialog.textLinked": "Συνδεδεμένο με την προέλευση", "SSE.Views.FormatSettingsDialog.textSeparator": "Χρήση διαχωριστή 1000", "SSE.Views.FormatSettingsDialog.textSymbols": "Σύμβολα", "SSE.Views.FormatSettingsDialog.textTitle": "Μορφή αριθμού", "SSE.Views.FormatSettingsDialog.txtAccounting": "Λογιστική", "SSE.Views.FormatSettingsDialog.txtAs10": "Ως δέκατα (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "Ως εκατοστά (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "Ως δέκατα έκτα (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Ως δεύτερα (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "Ως τέταρτα (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Ως όγδοα (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Νόμισμα", "SSE.Views.FormatSettingsDialog.txtCustom": "Προσαρμογή", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Παρακαλούμε εισάγετε προσεκτικά την προσαρμοσμένη μορφή αριθμού. Ο συντάκτης υπολογιστικού φύλλου δεν ελέγχει τις προκαθορισμένες μορφές για λάθη που μπορεί να επηρεάσουν το αρχείο xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "Ημερομηνία", "SSE.Views.FormatSettingsDialog.txtFraction": "Κλάσμα", "SSE.Views.FormatSettingsDialog.txtGeneral": "Γενικά", "SSE.Views.FormatSettingsDialog.txtNone": "Κανένα", "SSE.Views.FormatSettingsDialog.txtNumber": "Αριθμός", "SSE.Views.FormatSettingsDialog.txtPercentage": "Ποσοστό", "SSE.Views.FormatSettingsDialog.txtSample": "Δείγμα:", "SSE.Views.FormatSettingsDialog.txtScientific": "Επιστημονική", "SSE.Views.FormatSettingsDialog.txtText": "Κείμενο", "SSE.Views.FormatSettingsDialog.txtTime": "Ώρα", "SSE.Views.FormatSettingsDialog.txtUpto1": "Έως ένα ψηφίο (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Έως δύο ψηφία (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Έως τρία ψηφία (131/135)", "SSE.Views.FormulaDialog.sDescription": "Περιγραφή", "SSE.Views.FormulaDialog.textGroupDescription": "Επιλογή ομάδας συναρτήσεων", "SSE.Views.FormulaDialog.textListDescription": "Επιλογή συνάρτησης", "SSE.Views.FormulaDialog.txtRecommended": "Προτεινόμενα", "SSE.Views.FormulaDialog.txtSearch": "Αναζήτηση", "SSE.Views.FormulaDialog.txtTitle": "Εισαγωγ<PERSON> συνάρτησης", "SSE.Views.FormulaTab.capBtnRemoveArr": "Κατάργη<PERSON>η βελών", "SSE.Views.FormulaTab.capBtnTraceDep": "Εξαρτώμενα ιχνηλατημένα άτομα", "SSE.Views.FormulaTab.capBtnTracePrec": "Εξαρτώμενα ιχνηλατημένα άτομα", "SSE.Views.FormulaTab.textAutomatic": "Αυτόματα", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Υπολογισμός τρέχοντος φύλλου εργασίας", "SSE.Views.FormulaTab.textCalculateWorkbook": "Υπολογισμός βιβλίου εργασίας", "SSE.Views.FormulaTab.textManual": "Χε<PERSON>ρ<PERSON>κ<PERSON>νητα", "SSE.Views.FormulaTab.tipCalculate": "Υπολογισμός", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Υπολογισμός ολόκληρου του βιβλίου εργασίας", "SSE.Views.FormulaTab.tipRemoveArr": "Καταργήστε τα βέλη που έχουν σχεδιαστεί από προηγούμενα ίχνη ή εξαρτώμενα ίχνη", "SSE.Views.FormulaTab.tipShowFormulas": "Εμφάνιση του τύπου σε κάθε κελί αντί για την τιμή που προκύπτει", "SSE.Views.FormulaTab.tipTraceDep": "Εμφάνιση βελών που υποδεικνύουν ποια κελιά επηρεάζονται από την τιμή του επιλεγμένου κελιού", "SSE.Views.FormulaTab.tipTracePrec": "Εμφάνιση βελών που υποδεικνύουν ποια κελιά επηρεάζουν την τιμή του επιλεγμένου κελιού", "SSE.Views.FormulaTab.tipWatch": "Προσθήκη κελιών στη λίστα \"Παράθυρο παρακολούθησης\"", "SSE.Views.FormulaTab.txtAdditional": "Επιπρόσθετα", "SSE.Views.FormulaTab.txtAutosum": "Αυτόματο άθροισμα", "SSE.Views.FormulaTab.txtAutosumTip": "Άθροιση", "SSE.Views.FormulaTab.txtCalculation": "Υπολογισμός", "SSE.Views.FormulaTab.txtFormula": "Συνάρτηση", "SSE.Views.FormulaTab.txtFormulaTip": "Εισαγωγ<PERSON> συνάρτησης", "SSE.Views.FormulaTab.txtMore": "Περισσότερες συναρτήσεις", "SSE.Views.FormulaTab.txtRecent": "Πρόσφατα χρησιμοποιημένα", "SSE.Views.FormulaTab.txtRemDep": "Κατάργηση εξαρτημένων βελών", "SSE.Views.FormulaTab.txtRemPrec": "Κατάργη<PERSON>η βελών προηγούμενων", "SSE.Views.FormulaTab.txtShowFormulas": "Εμφάνιση τύπων", "SSE.Views.FormulaTab.txtWatch": "Παράθυρο παρακολούθησης", "SSE.Views.FormulaWizard.textAny": "οποιοδήποτε", "SSE.Views.FormulaWizard.textArgument": "Όρισμα", "SSE.Views.FormulaWizard.textFunction": "Συνάρτηση", "SSE.Views.FormulaWizard.textFunctionRes": "Αποτέλεσμα συνάρτησης", "SSE.Views.FormulaWizard.textHelp": "Βοήθεια για αυτή τη συνάρτηση", "SSE.Views.FormulaWizard.textLogical": "λογικό", "SSE.Views.FormulaWizard.textNoArgs": "Η συνάρτηση δεν έχει ορίσματα", "SSE.Views.FormulaWizard.textNoArgsDesc": "αυτό το επιχείρημα δεν έχει περιγραφή", "SSE.Views.FormulaWizard.textNumber": "αριθμός", "SSE.Views.FormulaWizard.textReadMore": "Διαβάστε περισσότερα", "SSE.Views.FormulaWizard.textRef": "παραπομπή", "SSE.Views.FormulaWizard.textText": "κείμενο", "SSE.Views.FormulaWizard.textTitle": "Ορίσματα συνάρτησης", "SSE.Views.FormulaWizard.textValue": "Αποτέλεσμα μαθηματικού τύπου", "SSE.Views.GoalSeekDlg.textChangingCell": "Αλλ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> κελί", "SSE.Views.GoalSeekDlg.textDataRangeError": "Η παράσταση στερείται εύρους", "SSE.Views.GoalSeekDlg.textMustContainFormula": "Το κελί πρέπει να περιέχει έναν τύπο", "SSE.Views.GoalSeekDlg.textMustContainValue": "Το κελί πρέπει να περιέχει μια τιμή", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Από την παράσταση στο κελί οφείλει να προκύπτει αριθμός", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Η αναφορά πρέπει να γίνεται σε ένα μόνο κελί", "SSE.Views.GoalSeekDlg.textSelectData": "Επιλογή δεδομένων", "SSE.Views.GoalSeekDlg.textSetCell": "Καθορισμός κελιού", "SSE.Views.GoalSeekDlg.textTitle": "Αναζήτηση στόχου", "SSE.Views.GoalSeekDlg.textToValue": "Έως την αξία", "SSE.Views.GoalSeekDlg.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Η εγγραφή σας δεν μπορεί να χρησιμοποιηθεί. Ενδέχεται να απαιτείται ακέραιος ή δεκαδικός αριθμός.", "SSE.Views.GoalSeekStatusDlg.textContinue": "Συνεχίστε", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Τρέχουσα τιμή:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Η αναζήτηση στόχου με κελί {0} βρήκε λύση.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Η αναζήτηση στόχου με κελί {0} ίσως να μη βρήκε λύση.", "SSE.Views.GoalSeekStatusDlg.textPause": "Παύση", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Αναζήτηση στόχου με κελί {0} στην επανάληψη #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "Βήμα", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Αξία-στόχος:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Κατάστα<PERSON>η αναζήτη<PERSON>ης στόχου", "SSE.Views.HeaderFooterDialog.textAlign": "Στοίχιση με τα περιθώρια σελίδας", "SSE.Views.HeaderFooterDialog.textAll": "Όλες οι σελίδες", "SSE.Views.HeaderFooterDialog.textBold": "Έντονα", "SSE.Views.HeaderFooterDialog.textCenter": "Κέντρο", "SSE.Views.HeaderFooterDialog.textColor": "Χρώμα κειμένου", "SSE.Views.HeaderFooterDialog.textDate": "Ημερομηνία", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Διαφορετική πρώτη σελίδα", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Διαφορετικές μονές και ζυγές σελίδες", "SSE.Views.HeaderFooterDialog.textEven": "Ζυγή σελίδα", "SSE.Views.HeaderFooterDialog.textFileName": "Όνομα αρχείου", "SSE.Views.HeaderFooterDialog.textFirst": "Πρώτη σελίδα", "SSE.Views.HeaderFooterDialog.textFooter": "Υποσέλιδο", "SSE.Views.HeaderFooterDialog.textHeader": "Κεφαλίδα", "SSE.Views.HeaderFooterDialog.textImage": "Εικόνα", "SSE.Views.HeaderFooterDialog.textInsert": "Εισαγωγή", "SSE.Views.HeaderFooterDialog.textItalic": "Πλάγια", "SSE.Views.HeaderFooterDialog.textLeft": "Αριστερά", "SSE.Views.HeaderFooterDialog.textMaxError": "Η συμβολοσειρά που εισαγάγατε είναι πολύ μεγάλη. Μειώστε τον αριθμό των χαρακτήρων.", "SSE.Views.HeaderFooterDialog.textNewColor": "Περισσότερα χρώματα", "SSE.Views.HeaderFooterDialog.textOdd": "Μονή σελίδα", "SSE.Views.HeaderFooterDialog.textPageCount": "Αρίθμηση σελίδων", "SSE.Views.HeaderFooterDialog.textPageNum": "Αριθμός σελίδας", "SSE.Views.HeaderFooterDialog.textPresets": "Προεπιλογές", "SSE.Views.HeaderFooterDialog.textRight": "Δεξιά", "SSE.Views.HeaderFooterDialog.textScale": "Κλιμάκωση με το έγγραφο", "SSE.Views.HeaderFooterDialog.textSheet": "Όνομα φύλλου", "SSE.Views.HeaderFooterDialog.textStrikeout": "Διαγραφή", "SSE.Views.HeaderFooterDialog.textSubscript": "Δείκτης", "SSE.Views.HeaderFooterDialog.textSuperscript": "Εκθέτης", "SSE.Views.HeaderFooterDialog.textTime": "Ώρα", "SSE.Views.HeaderFooterDialog.textTitle": "Ρυθμίσεις κεφαλίδας/υποσέλιδου", "SSE.Views.HeaderFooterDialog.textUnderline": "Υπογράμμιση", "SSE.Views.HeaderFooterDialog.tipFontName": "Γραμματοσειρά", "SSE.Views.HeaderFooterDialog.tipFontSize": "Μέγ<PERSON><PERSON>ος γραμματοσειράς", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Προβολή", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Σύνδεσμος σε", "SSE.Views.HyperlinkSettingsDialog.strRange": "Εύρ<PERSON>", "SSE.Views.HyperlinkSettingsDialog.strSheet": "Φύλλο", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Αντιγραφή", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Επιλεγμένο εύρος", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Εισάγετε λεζάντα εδώ", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Εισάγετε σύνδεσμο εδώ", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Εισάγετε συμβουλή εδώ", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Εξωτερικ<PERSON>ς σύνδεσμος", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "<PERSON>ή<PERSON>η συνδέσμου", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Εσωτερικ<PERSON> εύρος δεδομένων", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.HyperlinkSettingsDialog.textNames": "Προσδιορισμένα ονόματα", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Επιλογή δεδομένων", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Επιλογή αρχείου", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Φύλλα", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Κείμενο υπόδειξης", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Ρυθμίσεις υπερσυνδέσμου", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Αυτό το πεδίο πρέπει να είναι διεύθυνση URL με τη μορφή «http://www.example.com»", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Το πεδίο αυτό χωράει 2083 χαρακτήρες", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Εισαγάγετε τη διεύθυνση ιστού ή επιλέξτε ένα αρχείο", "SSE.Views.ImageSettings.strTransparency": "Αδιαφάνεια", "SSE.Views.ImageSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "SSE.Views.ImageSettings.textCrop": "Περικοπή", "SSE.Views.ImageSettings.textCropFill": "Γέμισμα", "SSE.Views.ImageSettings.textCropFit": "Προσαρμογή", "SSE.Views.ImageSettings.textCropToShape": "Περικο<PERSON>ή στο σχήμα", "SSE.Views.ImageSettings.textEdit": "Επεξεργασία", "SSE.Views.ImageSettings.textEditObject": "Επεξεργα<PERSON><PERSON>α αντικειμένου", "SSE.Views.ImageSettings.textFlip": "Περιστροφή", "SSE.Views.ImageSettings.textFromFile": "Από αρχείο", "SSE.Views.ImageSettings.textFromStorage": "Από αποθηκευτικό χώρο", "SSE.Views.ImageSettings.textFromUrl": "Από διεύθυνση URL", "SSE.Views.ImageSettings.textHeight": "Ύψος", "SSE.Views.ImageSettings.textHint270": "Περιστροφή 90° Αριστερόστροφα", "SSE.Views.ImageSettings.textHint90": "Περιστροφή 90° Δεξιόστροφα", "SSE.Views.ImageSettings.textHintFlipH": "Οριζόντια περιστροφή", "SSE.Views.ImageSettings.textHintFlipV": "Κατακόρυφη περιστροφή", "SSE.Views.ImageSettings.textInsert": "Αντικατάσταση εικόνας", "SSE.Views.ImageSettings.textKeepRatio": "Σταθερές αναλογίες", "SSE.Views.ImageSettings.textOriginalSize": "Πλήρες μέγεθος", "SSE.Views.ImageSettings.textRecentlyUsed": "Πρόσφατα χρησιμοποιημένα", "SSE.Views.ImageSettings.textResetCrop": "Επαναφορ<PERSON> περικοπής", "SSE.Views.ImageSettings.textRotate90": "Περιστροφή 90°", "SSE.Views.ImageSettings.textRotation": "Περιστροφή", "SSE.Views.ImageSettings.textSize": "Μέγεθος", "SSE.Views.ImageSettings.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Να μην αλλάζει θέση ή μέγεθος με τα κελιά", "SSE.Views.ImageSettingsAdvanced.textAlt": "Εναλλακτικό κείμενο", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Περιγραφή", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Η εναλλακτική, κειμενική αναπαράσταση των πληροφοριών του οπτικού αντικειμένου, που θα αναγνωστεί σε ανθρώπους με προβλήματα όρασης ή γνωστικές αδυναμίες, για να κατανοήσουν καλύτερα τις πληροφορίες που περιέχονται στην εικόνα, σχήμα, γρά<PERSON>η<PERSON><PERSON> ή πίνακα.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Τίτλος", "SSE.Views.ImageSettingsAdvanced.textAngle": "Γωνία", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Περιεστρεμμένο  ", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Οριζόντια", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Μετακίνηση αλλά όχι αλλαγή μεγέθους με τα κελιά", "SSE.Views.ImageSettingsAdvanced.textRotation": "Περιστροφή", "SSE.Views.ImageSettingsAdvanced.textSnap": "Ευθυγράμμιση σε κελί", "SSE.Views.ImageSettingsAdvanced.textTitle": "Εικόνα - Προηγμένες ρυθμίσεις", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Μετακίνηση και αλλαγή μεγέθους με τα κελιά", "SSE.Views.ImageSettingsAdvanced.textVertically": "Κατακόρυφα", "SSE.Views.ImportFromXmlDialog.textDestination": "Επιλέξτε πού θα τοποθετήσετε τα δεδομένα", "SSE.Views.ImportFromXmlDialog.textExist": "Υφιστάμενο φύλλο εργασίας", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Μη έγκυρο εύρος κελιών", "SSE.Views.ImportFromXmlDialog.textNew": "Νέο φύλλο εργασίας", "SSE.Views.ImportFromXmlDialog.textSelectData": "Επιλογή δεδομένων", "SSE.Views.ImportFromXmlDialog.textTitle": "Εισαγωγή δεδομένων", "SSE.Views.ImportFromXmlDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.LeftMenu.ariaLeftMenu": "Αριστερό μενού", "SSE.Views.LeftMenu.tipAbout": "Περί", "SSE.Views.LeftMenu.tipChat": "Συνομιλία", "SSE.Views.LeftMenu.tipComments": "Σχόλια", "SSE.Views.LeftMenu.tipFile": "Αρχείο", "SSE.Views.LeftMenu.tipPlugins": "Πρόσθετα", "SSE.Views.LeftMenu.tipSearch": "Εύρεση", "SSE.Views.LeftMenu.tipSpellcheck": "Έλεγχος ορθογραφίας", "SSE.Views.LeftMenu.tipSupport": "Ανατροφοδότηση & Υποστήριξη", "SSE.Views.LeftMenu.txtDeveloper": "ΛΕΙΤΟΥΡΓΙΑ ΓΙΑ ΠΡΟΓΡΑΜΜΑΤΙΣΤΕΣ", "SSE.Views.LeftMenu.txtEditor": "Συντάκτης υπολογιστικού φύλλου", "SSE.Views.LeftMenu.txtLimit": "Περιορισμ<PERSON>ς πρόσβασης", "SSE.Views.LeftMenu.txtTrial": "ΚΑΤΑΣΤΑΣΗ ΔΟΚΙΜΑΣΤΙΚΗΣ ΛΕΙΤΟΥΡΓΙΑΣ", "SSE.Views.LeftMenu.txtTrialDev": "Δοκιμαστική Λειτουργία για Προγραμματιστές", "SSE.Views.MacroDialog.textMacro": "Όνομα μακροεντολής", "SSE.Views.MacroDialog.textTitle": "Ανάθεση μακροεντολής", "SSE.Views.MainSettingsPrint.okButtonText": "Αποθήκευση", "SSE.Views.MainSettingsPrint.strBottom": "Κάτω", "SSE.Views.MainSettingsPrint.strLandscape": "Οριζόντιος", "SSE.Views.MainSettingsPrint.strLeft": "Αριστερά", "SSE.Views.MainSettingsPrint.strMargins": "Περιθώρια", "SSE.Views.MainSettingsPrint.strPortrait": "Κατακόρυφος", "SSE.Views.MainSettingsPrint.strPrint": "Εκτύπωση", "SSE.Views.MainSettingsPrint.strPrintTitles": "Εκτύπωση τίτλων", "SSE.Views.MainSettingsPrint.strRight": "Δεξιά", "SSE.Views.MainSettingsPrint.strTop": "Επάνω", "SSE.Views.MainSettingsPrint.textActualSize": "Πλήρες μέγεθος", "SSE.Views.MainSettingsPrint.textCustom": "Προσαρμογή", "SSE.Views.MainSettingsPrint.textCustomOptions": "Προσαρμοσμένες επιλογές", "SSE.Views.MainSettingsPrint.textFitCols": "Προσα<PERSON><PERSON><PERSON><PERSON><PERSON> όλων των στηλών σε μια σελίδα", "SSE.Views.MainSettingsPrint.textFitPage": "Προσαρμογή φύλλου σε μια σελίδα", "SSE.Views.MainSettingsPrint.textFitRows": "Προσα<PERSON><PERSON><PERSON><PERSON>ή όλων των σειρών σε μια σελίδα", "SSE.Views.MainSettingsPrint.textPageOrientation": "Προσανα<PERSON><PERSON><PERSON>ισμ<PERSON>ς σελίδας", "SSE.Views.MainSettingsPrint.textPageScaling": "Κλίμακα", "SSE.Views.MainSettingsPrint.textPageSize": "Μέγ<PERSON><PERSON>ος σελίδας", "SSE.Views.MainSettingsPrint.textPrintGrid": "Εκτύπωση γραμμών πλέγματος", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Εκτύπωση επικεφαλίδων σειρών και στηλών", "SSE.Views.MainSettingsPrint.textRepeat": "Επανάληψη...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Επανάληψη στηλών στα αριστερά", "SSE.Views.MainSettingsPrint.textRepeatTop": "Επανάληψη γραμμών στην κορυφή", "SSE.Views.MainSettingsPrint.textSettings": "Ρυθμίσεις για", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Δεν είναι δυνατή η επεξεργασία των υφιστάμενων επώνυμων ευρών και δεν είναι δυνατή η δημιουργία νέων<br>αυτ<PERSON><PERSON> τη στιγμή, καθ<PERSON><PERSON> ορισμένα από αυτά τελούν υπό επεξεργασία.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Προσδιορισμένο όνομα", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Προειδοποίηση", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Βιβλίο Εργασίας", "SSE.Views.NamedRangeEditDlg.textDataRange": "Εύρ<PERSON> δεδομένων", "SSE.Views.NamedRangeEditDlg.textExistName": "ΣΦΑΛΜΑ! Υπάρχει ήδη εύρος με αυτό το όνομα", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Το όνομα πρέπει να ξεκινάει με γράμμα ή κάτω παύλα και δεν πρέπει να περιέχει μη έγκυρους χαρακτήρες.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ΣΦΑΛΜΑ! Το στοιχείο αυτό τελεί υπό επεξεργασία από άλλο χρήστη.", "SSE.Views.NamedRangeEditDlg.textName": "Όνομα", "SSE.Views.NamedRangeEditDlg.textReservedName": "Το όνομα που προσπαθείτε να χρησιμοποιήσετε αναφέρεται ήδη σε τύπους κελιών. Παρακαλούμε χρησιμοποιήστε κάποιο άλλο όνομα.", "SSE.Views.NamedRangeEditDlg.textScope": "Εμβέλεια", "SSE.Views.NamedRangeEditDlg.textSelectData": "Επιλογή δεδομένων", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Επεξεργασ<PERSON>α ονόματος", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Νέο όνομα", "SSE.Views.NamedRangePasteDlg.textNames": "Επώνυμα εύρη ", "SSE.Views.NamedRangePasteDlg.txtTitle": "Επικόλληση ονόματος", "SSE.Views.NameManagerDlg.closeButtonText": "Κλείσιμο", "SSE.Views.NameManagerDlg.guestText": "Επισκέπτης", "SSE.Views.NameManagerDlg.lockText": "Κλειδωμένο", "SSE.Views.NameManagerDlg.textDataRange": "Εύρ<PERSON> δεδομένων", "SSE.Views.NameManagerDlg.textDelete": "Διαγραφή", "SSE.Views.NameManagerDlg.textEdit": "Επεξεργασία", "SSE.Views.NameManagerDlg.textEmpty": "Δεν δημιουργήθηκαν ακόμα επώνυμα εύρη.<br>Δημιουργήστε τουλάχιστον ένα επώνυμο εύρος και θα εμφανιστεί σε αυτό το πεδίο.", "SSE.Views.NameManagerDlg.textFilter": "Φίλτρο", "SSE.Views.NameManagerDlg.textFilterAll": "Όλα", "SSE.Views.NameManagerDlg.textFilterDefNames": "Προσδιορισμένα ονόματα", "SSE.Views.NameManagerDlg.textFilterSheet": "Ονόματα εμβέλειας εντός φύλλου", "SSE.Views.NameManagerDlg.textFilterTableNames": "Ονόματα πίνακα", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Ονόματα με εύρος στο βιβλίο εργασίας", "SSE.Views.NameManagerDlg.textNew": "Νέο", "SSE.Views.NameManagerDlg.textnoNames": "Δεν βρέθηκαν επώνυμα εύρη που να ταιριάζουν στο φίλτρο σας.", "SSE.Views.NameManagerDlg.textRanges": "Επώνυμα εύρη ", "SSE.Views.NameManagerDlg.textScope": "Εμβέλεια", "SSE.Views.NameManagerDlg.textWorkbook": "Βιβλίο Εργασίας", "SSE.Views.NameManagerDlg.tipIsLocked": "Αυτό το στοιχείο τελεί υπό επεξεργασία από άλλο χρήστη.", "SSE.Views.NameManagerDlg.txtTitle": "Διαχειριστής ονομάτων", "SSE.Views.NameManagerDlg.warnDelete": "Θέλετε σίγουρα να διαγράψετε το όνομα {0};", "SSE.Views.PageMarginsDialog.textBottom": "Κάτω", "SSE.Views.PageMarginsDialog.textCenter": "Κεντράρισμα στη σελίδα", "SSE.Views.PageMarginsDialog.textHor": "Οριζόντια", "SSE.Views.PageMarginsDialog.textLeft": "Αριστερά", "SSE.Views.PageMarginsDialog.textRight": "Δεξιά", "SSE.Views.PageMarginsDialog.textTitle": "Περιθώρια", "SSE.Views.PageMarginsDialog.textTop": "Επάνω", "SSE.Views.PageMarginsDialog.textVert": "Κατακόρυφα", "SSE.Views.PageMarginsDialog.textWarning": "Προειδοποίηση", "SSE.Views.PageMarginsDialog.warnCheckMargings": "Τα περιθώρια είναι εσφαλμένα", "SSE.Views.ParagraphSettings.strLineHeight": "Διάστιχο", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Απόσταση παραγράφων", "SSE.Views.ParagraphSettings.strSpacingAfter": "Μετά", "SSE.Views.ParagraphSettings.strSpacingBefore": "Πριν", "SSE.Views.ParagraphSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "SSE.Views.ParagraphSettings.textAt": "Στο", "SSE.Views.ParagraphSettings.textAtLeast": "Τουλάχιστον", "SSE.Views.ParagraphSettings.textAuto": "Πολλαπλό", "SSE.Views.ParagraphSettings.textExact": "Α<PERSON><PERSON>ιβ<PERSON>ς", "SSE.Views.ParagraphSettings.txtAutoText": "Αυτόματα", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Οι καθορισμένοι στηλοθέτες θα εμφανίζονται σε αυτό το πεδίο", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Όλα κεφαλαία", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Διπλή διαγραφή", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Εσοχ<PERSON>ς", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Αριστερά", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Διάστιχο", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Δεξιά", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Μετά", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Πριν", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Ειδική", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Από", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Γραμματοσειρά", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Εσο<PERSON><PERSON>ς & Αποστάσεις", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Μικρά κεφαλαία", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Απόσταση", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Διακριτική διαγραφή", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Δείκτης", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Εκθέτης", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Στηλοθέτες", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Στοίχιση", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Πολλαπλό", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Απόσταση χαρακτήρων", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Προεπιλεγ<PERSON><PERSON><PERSON>ος στηλοθέτης", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Εφέ", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Α<PERSON><PERSON>ιβ<PERSON>ς", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Πρώτη γραμμή", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Αρνητική", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Πλήρης στοίχιση", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(κανένα)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Αφαίρεση", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Αφαίρε<PERSON>η όλων", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Προσδιορισμός", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Κέντρο", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Αριστερά", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Θέση στηλοθέτη", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Δεξιά", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Παράγρα<PERSON>ος - Προηγμένες ρυθμίσεις", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Αυτόματα", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Διαγραφή", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "Αντίγραφο", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Επεξεργασία", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Τύπος", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Όνομα στοιχείων", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "Νέο", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Υπολογιζόμενα στοιχεία στο", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "ισούται", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "δεν τελειώνει με", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "περιέχει", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "δεν περιέχει", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "μεταξύ", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "όχι ανάμεσα", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "δεν είναι ίσο με", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "είναι μεγαλύτερο από", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "είναι μεγαλύτερο από ή ίσο με", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "είναι μικρότερο από", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "είναι μικρότερο από ή ίσο με", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "αρχίζει με", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "δεν ξεκινά με", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "τελειώνει με", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Εμφάνιση στοιχείων για τα οποία η ετικέτα:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Εμφάνιση στοιχείων για τα οποία:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Χρήση του ? για αναπαράσταση οποιουδήποτε χαρακτήρα", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Χρήση του * για αναπαράσταση οποιασδήποτε συμβολοσειράς", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "και", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Φίλτρο ετικέτας", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Φίλτρο τιμών", "SSE.Views.PivotGroupDialog.textAuto": "Αυτόματα", "SSE.Views.PivotGroupDialog.textBy": "Κατά", "SSE.Views.PivotGroupDialog.textDays": "Ημέρες", "SSE.Views.PivotGroupDialog.textEnd": "Τελειώνει στο", "SSE.Views.PivotGroupDialog.textError": "Το πεδίο αυτό πρέπει να έχει αριθμητική τιμή", "SSE.Views.PivotGroupDialog.textGreaterError": "Ο αριθμός τέλους πρέπει να είναι μεγαλύτερος από τον αριθμό αρχής", "SSE.Views.PivotGroupDialog.textHour": "Ώρες", "SSE.Views.PivotGroupDialog.textMin": "Λεπτά", "SSE.Views.PivotGroupDialog.textMonth": "Μήνες", "SSE.Views.PivotGroupDialog.textNumDays": "Αριθμ<PERSON>ς ημερών", "SSE.Views.PivotGroupDialog.textQuart": "Τετράμηνα", "SSE.Views.PivotGroupDialog.textSec": "Δευτερόλεπτα", "SSE.Views.PivotGroupDialog.textStart": "Αρχίζει στο", "SSE.Views.PivotGroupDialog.textYear": "Έτη", "SSE.Views.PivotGroupDialog.txtTitle": "Ομαδοποίηση", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "Μπορείτε να χρησιμοποιήσετε τα υπολογιζόμενα στοιχεία για βασικούς υπολογισμούς μεταξύ διαφορετικών στοιχείων μέσα σε ένα πεδίο", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Τύπος", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Εισαγωγή σε τύπο", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "Αντικείμενο", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Όνομα αντικειμένου", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Αντικείμενα", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Διαβάστε περισσότερα", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Εισαγωγή υπολογιζόμενου στοιχείου στο", "SSE.Views.PivotSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "SSE.Views.PivotSettings.textColumns": "Στήλες", "SSE.Views.PivotSettings.textFields": "Επιλογή πεδίων", "SSE.Views.PivotSettings.textFilters": "Φίλτρα", "SSE.Views.PivotSettings.textRows": "Γραμμές", "SSE.Views.PivotSettings.textValues": "Τιμές", "SSE.Views.PivotSettings.txtAddColumn": "Προσθήκη στις στήλες", "SSE.Views.PivotSettings.txtAddFilter": "Προσθήκη στα φίλτρα", "SSE.Views.PivotSettings.txtAddRow": "Προσθήκη στις γραμμές", "SSE.Views.PivotSettings.txtAddValues": "Προσθήκη στις Τιμές", "SSE.Views.PivotSettings.txtFieldSettings": "Ρυθμίσεις πεδίων", "SSE.Views.PivotSettings.txtMoveBegin": "Μετακίνηση στην αρχή", "SSE.Views.PivotSettings.txtMoveColumn": "Μετακίνηση στις στήλες", "SSE.Views.PivotSettings.txtMoveDown": "Μετακίνηση κάτω", "SSE.Views.PivotSettings.txtMoveEnd": "Μετακίνηση στο τέλος", "SSE.Views.PivotSettings.txtMoveFilter": "Μετακίνηση στα φίλτρα", "SSE.Views.PivotSettings.txtMoveRow": "Μετακίνηση στις γραμμές", "SSE.Views.PivotSettings.txtMoveUp": "Μετακίνηση επάνω", "SSE.Views.PivotSettings.txtMoveValues": "Μετακίνηση στις τιμές", "SSE.Views.PivotSettings.txtRemove": "Αφαίρεση πεδίου", "SSE.Views.PivotSettingsAdvanced.strLayout": "Όνομα και διάταξη", "SSE.Views.PivotSettingsAdvanced.textAlt": "Εναλλακτικό κείμενο", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Περιγραφή", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Η εναλλακτική, κειμενική αναπαράσταση των πληροφοριών του οπτικού αντικειμένου, που θα αναγνωστεί σε ανθρώπους με προβλήματα όρασης ή γνωστικές αδυναμίες, για να κατανοήσουν καλύτερα τις πληροφορίες που περιέχονται στην εικόνα, σχήμα, γρά<PERSON>η<PERSON><PERSON> ή πίνακα.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Τίτλος", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Αυτόματη προσαρμογή πλάτους στηλών κατά την ενημέρωση", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Εύρ<PERSON> δεδομένων", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Πηγή δεδομένων", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Εμφάνιση πεδίων στην περιοχή φίλτρων της αναφοράς", "SSE.Views.PivotSettingsAdvanced.textDown": "Κ<PERSON>τ<PERSON>, μετά πάνω από", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Γενικά σύνολα", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Κεφαλίδες πεδίων", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.PivotSettingsAdvanced.textOver": "<PERSON><PERSON><PERSON><PERSON>, μετά κάτω", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Επιλογή δεδομένων", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Εμφάνιση για στήλες", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Εμφάνιση κεφαλίδων πεδίων για γραμμές και στήλες", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Εμφάνιση για γραμμές", "SSE.Views.PivotSettingsAdvanced.textTitle": "Συγκεντρω<PERSON>ι<PERSON><PERSON>ς πίνακας - Προηγμένες ρυθμίσεις", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Πεδία φίλτρων ανα<PERSON><PERSON><PERSON><PERSON><PERSON> ανά στήλη", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Πεδία φίλτρων αναφ<PERSON><PERSON><PERSON>ς ανά γραμμή", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.PivotSettingsAdvanced.txtName": "Όνομα", "SSE.Views.PivotShowDetailDialog.textDescription": "Επιλέξτε το πεδίο που περιέχει τις λεπτομέρειες που θέλετε να εμφανίσετε:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Εμφάνιση λεπτομερειών", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON><PERSON><PERSON><PERSON> γραμμές", "SSE.Views.PivotTable.capGrandTotals": "Γενικά σύνολα", "SSE.Views.PivotTable.capLayout": "Διάταξη αναφοράς", "SSE.Views.PivotTable.capSubtotals": "Μερικά σύνολα", "SSE.Views.PivotTable.mniBottomSubtotals": "Εμφάνιση όλων των μερικών συνόλων στο κάτω μέρος της ομάδας", "SSE.Views.PivotTable.mniInsertBlankLine": "Εισαγωγή κενής γραμμής μετά από κάθε στοιχείο", "SSE.Views.PivotTable.mniLayoutCompact": "Εμφάνιση σε συμπαγή μορφή", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Να μην επανα<PERSON>α<PERSON>βάνεται καμία ετικέτα στοιχείων", "SSE.Views.PivotTable.mniLayoutOutline": "Προβολή ως φόρμα διάρθρωσης", "SSE.Views.PivotTable.mniLayoutRepeat": "Επανάληψη όλων των ετικετών στοιχείων", "SSE.Views.PivotTable.mniLayoutTabular": "Εμφάνιση σε μορφή πίνακα", "SSE.Views.PivotTable.mniNoSubtotals": "Να μην εμφανίζονται μερικά σύνολα", "SSE.Views.PivotTable.mniOffTotals": "Απενεργοποίηση για γραμμές και στήλες", "SSE.Views.PivotTable.mniOnColumnsTotals": "Ενεργοποίηση μόνο για στήλες", "SSE.Views.PivotTable.mniOnRowsTotals": "Ενεργοποίηση μόνο για γραμμές", "SSE.Views.PivotTable.mniOnTotals": "Ενεργοποίηση για γραμμές και στήλες", "SSE.Views.PivotTable.mniRemoveBlankLine": "Αφαίρεση κενής γραμμής μετά από κάθε στοιχείο", "SSE.Views.PivotTable.mniTopSubtotals": "Εμφάνιση όλων των μερικών συνόλων στο επάνω μέρος της ομάδας", "SSE.Views.PivotTable.textColBanded": "Στήλες με εναλλαγή σκίασης", "SSE.Views.PivotTable.textColHeader": "Κε<PERSON>αλίδες στήλης", "SSE.Views.PivotTable.textRowBanded": "Γραμ<PERSON>ές με εναλλαγή σκίασης", "SSE.Views.PivotTable.textRowHeader": "Κεφαλίδες γραμμών", "SSE.Views.PivotTable.tipCalculatedItems": "Υπολογιζόμενα στοιχεία", "SSE.Views.PivotTable.tipCreatePivot": "Εισαγωγ<PERSON> συγκεντρωτικού πίνακα", "SSE.Views.PivotTable.tipGrandTotals": "Εμφάνιση ή απόκρυψη τελικών συνόλων", "SSE.Views.PivotTable.tipRefresh": "Ενημέρωση πληροφοριών από πηγή δεδομένων", "SSE.Views.PivotTable.tipRefreshCurrent": "Ενημέρωση των πληροφοριών από την προέλευση δεδομένων για τον τρέχοντα πίνακα", "SSE.Views.PivotTable.tipSelect": "Επιλογή ολόκληρου συγκεντρωτικού πίνακα", "SSE.Views.PivotTable.tipSubtotals": "Εμφάνιση ή απόκρυψη μερικών συνόλων", "SSE.Views.PivotTable.txtCalculatedItems": "Υπολογιζόμενα στοιχεία", "SSE.Views.PivotTable.txtCollapseEntire": "Σύμπτυξη ολόκληρου του πεδίου", "SSE.Views.PivotTable.txtCreate": "Εισαγωγή πίνακα", "SSE.Views.PivotTable.txtExpandEntire": "Ανάπτυξη ολόκληρου του πεδίου", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Προσαρμογή", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Σκουρόχρωμο", "SSE.Views.PivotTable.txtGroupPivot_Light": "Ανοιχτόχρωμο", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Μεσαίο", "SSE.Views.PivotTable.txtPivotTable": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "SSE.Views.PivotTable.txtRefresh": "Ανανέωση", "SSE.Views.PivotTable.txtRefreshAll": "Ανανέω<PERSON>η όλων", "SSE.Views.PivotTable.txtSelect": "Επιλογή", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Στυλ συγκεντρωτικού πίνακα σκούρο", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Στυλ συγκεντρωτικού πίνακα ανοιχτόχρωμο", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Στυλ συγκεντρωτικού πίνακα ενδιάμεσο", "SSE.Views.PrintSettings.btnDownload": "Αποθήκευση & Λήψη", "SSE.Views.PrintSettings.btnExport": "Αποθήκευση και Εξαγωγή", "SSE.Views.PrintSettings.btnPrint": "Αποθήκευση & Εκτύπωση", "SSE.Views.PrintSettings.strBottom": "Κάτω", "SSE.Views.PrintSettings.strLandscape": "Οριζόντιος", "SSE.Views.PrintSettings.strLeft": "Αριστερά", "SSE.Views.PrintSettings.strMargins": "Περιθώρια", "SSE.Views.PrintSettings.strPortrait": "Κατακόρυφος", "SSE.Views.PrintSettings.strPrint": "Εκτύπωση", "SSE.Views.PrintSettings.strPrintTitles": "Εκτύπωση τίτλων", "SSE.Views.PrintSettings.strRight": "Δεξιά", "SSE.Views.PrintSettings.strShow": "Εμφάνιση", "SSE.Views.PrintSettings.strTop": "Επάνω", "SSE.Views.PrintSettings.textActiveSheets": "Ενεργά φύλλα", "SSE.Views.PrintSettings.textActualSize": "Πλήρες μέγεθος", "SSE.Views.PrintSettings.textAllSheets": "Όλα τα φύλλα", "SSE.Views.PrintSettings.textCurrentSheet": "Τρέχον φύλλο", "SSE.Views.PrintSettings.textCustom": "Προσαρμογή", "SSE.Views.PrintSettings.textCustomOptions": "Προσαρμοσμένες Επιλογές", "SSE.Views.PrintSettings.textFitCols": "Προσα<PERSON><PERSON><PERSON><PERSON><PERSON> όλων των στηλών σε μια σελίδα", "SSE.Views.PrintSettings.textFitPage": "Προσαρμογή φύλλου σε μια σελίδα", "SSE.Views.PrintSettings.textFitRows": "Προσα<PERSON><PERSON><PERSON><PERSON>ή όλων των σειρών σε μια σελίδα", "SSE.Views.PrintSettings.textHideDetails": "Απόκρυψη λεπτομερειών", "SSE.Views.PrintSettings.textIgnore": "Αγνόηση περιοχής εκτύπωσης", "SSE.Views.PrintSettings.textLayout": "Διάταξη", "SSE.Views.PrintSettings.textMarginsNarrow": "Στενό", "SSE.Views.PrintSettings.textMarginsNormal": "Κανονικό", "SSE.Views.PrintSettings.textMarginsWide": "<PERSON>λα<PERSON><PERSON>", "SSE.Views.PrintSettings.textPageOrientation": "Προσανα<PERSON><PERSON><PERSON>ισμ<PERSON>ς σελίδας", "SSE.Views.PrintSettings.textPages": "Σελίδες:", "SSE.Views.PrintSettings.textPageScaling": "Κλίμακα", "SSE.Views.PrintSettings.textPageSize": "Μέγ<PERSON><PERSON>ος σελίδας", "SSE.Views.PrintSettings.textPrintGrid": "Εκτύπωση γραμμών πλέγματος", "SSE.Views.PrintSettings.textPrintHeadings": "Εκτύπωση επικεφαλίδων σειρών και στηλών", "SSE.Views.PrintSettings.textPrintRange": "Ε<PERSON><PERSON><PERSON> εκτύπωσης", "SSE.Views.PrintSettings.textRange": "Εύρ<PERSON>", "SSE.Views.PrintSettings.textRepeat": "Επανάληψη...", "SSE.Views.PrintSettings.textRepeatLeft": "Επανάληψη στηλών στα αριστερά", "SSE.Views.PrintSettings.textRepeatTop": "Επανάληψη γραμμών στην κορυφή", "SSE.Views.PrintSettings.textSelection": "Επιλογή", "SSE.Views.PrintSettings.textSettings": "Ρυθμίσεις φύλλου", "SSE.Views.PrintSettings.textShowDetails": "Εμφάνιση λεπτομερειών", "SSE.Views.PrintSettings.textShowGrid": "Εμφάνιση γραμμών πλέγματος", "SSE.Views.PrintSettings.textShowHeadings": "Εμφάνιση επικεφαλίδων γραμμών και στηλών", "SSE.Views.PrintSettings.textTitle": "Ρυθμίσεις εκτύπωσης", "SSE.Views.PrintSettings.textTitlePDF": "Ρυθμίσεις PDF", "SSE.Views.PrintSettings.textTo": "προς", "SSE.Views.PrintSettings.txtMarginsLast": "Τελευτ<PERSON><PERSON><PERSON> προσαρμοσμένο", "SSE.Views.PrintTitlesDialog.textFirstCol": "Πρώτη στήλη", "SSE.Views.PrintTitlesDialog.textFirstRow": "Πρώτη γραμμή", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Σταθεροποιημένες στήλες", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Σταθεροποιημένες γραμμές", "SSE.Views.PrintTitlesDialog.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.PrintTitlesDialog.textLeft": "Επανάληψη στηλών στα αριστερά", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Να μην επαναλαμβάνεται", "SSE.Views.PrintTitlesDialog.textRepeat": "Επανάληψη...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Επιλογή εύρους", "SSE.Views.PrintTitlesDialog.textTitle": "Εκτύπωση τίτλων", "SSE.Views.PrintTitlesDialog.textTop": "Επανάληψη γραμμών στην κορυφή", "SSE.Views.PrintWithPreview.txtActiveSheets": "Ενεργά φύλλα", "SSE.Views.PrintWithPreview.txtActualSize": "Πλήρες μέγεθος", "SSE.Views.PrintWithPreview.txtAllSheets": "Όλα τα Φύλλα", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Εφαρμογή σε όλα τα φύλλα", "SSE.Views.PrintWithPreview.txtBothSides": "Εκτύπωση και στις δύο πλευρές", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "Αναστροφή σελίδων στη μεγάλη άκρη", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "Αναστροφή σελίδων στη μικρή άκρη", "SSE.Views.PrintWithPreview.txtBottom": "Κάτω μέρος", "SSE.Views.PrintWithPreview.txtCopies": "Αντίγραφα", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Τρέχον φύλλο", "SSE.Views.PrintWithPreview.txtCustom": "Προσαρμογή", "SSE.Views.PrintWithPreview.txtCustomOptions": "Προσαρμοσμένες επιλογές", "SSE.Views.PrintWithPreview.txtEmptyTable": "Δεν υπάρχει κάτι για εκτύπωση επειδή ο πίνακας είναι άδειος", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "Αριθμός πρώτης σελίδας:", "SSE.Views.PrintWithPreview.txtFitCols": "Προσα<PERSON><PERSON><PERSON><PERSON><PERSON> όλων των στηλών σε μια σελίδα", "SSE.Views.PrintWithPreview.txtFitPage": "Προσαρμογή φύλλου σε μια σελίδα", "SSE.Views.PrintWithPreview.txtFitRows": "Προσα<PERSON><PERSON><PERSON><PERSON>ή όλων των σειρών σε μια σελίδα", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Γραμ<PERSON><PERSON>ς πλέγματος και επικεφαλίδες", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Ρυθμίσεις κεφαλίδας/υποσέλιδου", "SSE.Views.PrintWithPreview.txtIgnore": "Αγνόηση περιοχής εκτύπωσης", "SSE.Views.PrintWithPreview.txtLandscape": "Οριζόντιος", "SSE.Views.PrintWithPreview.txtLeft": "Αριστερά", "SSE.Views.PrintWithPreview.txtMargins": "Περιθώρια", "SSE.Views.PrintWithPreview.txtMarginsLast": "Τελευτ<PERSON><PERSON><PERSON> προσαρμοσμένο", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "Στενό", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Κανονικό", "SSE.Views.PrintWithPreview.txtMarginsWide": "<PERSON>λα<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtOf": "του {0}", "SSE.Views.PrintWithPreview.txtOneSide": "Εκτύπωση στη μια πλευρά", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Εκτύπωση μόνο στη μία πλευρά της σελίδας", "SSE.Views.PrintWithPreview.txtPage": "Σελίδα", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Μη έγκυρος αριθμός σελίδας", "SSE.Views.PrintWithPreview.txtPageOrientation": "Προσανα<PERSON><PERSON><PERSON>ισμ<PERSON>ς σελίδας", "SSE.Views.PrintWithPreview.txtPages": "Σελίδες:", "SSE.Views.PrintWithPreview.txtPageSize": "Μέγ<PERSON><PERSON>ος σελίδας", "SSE.Views.PrintWithPreview.txtPortrait": "Κατακόρυφος", "SSE.Views.PrintWithPreview.txtPrint": "Εκτύπωση", "SSE.Views.PrintWithPreview.txtPrintGrid": "Εκτύπωση γραμμών πλέγματος", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Εκτύπωση επικεφαλίδων σειρών και στηλών", "SSE.Views.PrintWithPreview.txtPrintRange": "Εκτύπωση εύρους", "SSE.Views.PrintWithPreview.txtPrintSides": "Εκτύπωση πλευρών", "SSE.Views.PrintWithPreview.txtPrintTitles": "Εκτύπωση τίτλων", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Εκτύπωση σε PDF", "SSE.Views.PrintWithPreview.txtRepeat": "Επανάληψη...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Επανάληψη στηλών στα αριστερά", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Επανάληψη σειρών στην κορυφή", "SSE.Views.PrintWithPreview.txtRight": "Δεξιά", "SSE.Views.PrintWithPreview.txtSave": "Αποθήκευση", "SSE.Views.PrintWithPreview.txtScaling": "Κλίμακα", "SSE.Views.PrintWithPreview.txtSelection": "Επιλογή", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Ρυθμίσεις φύλλου", "SSE.Views.PrintWithPreview.txtSheet": "Φύλλο: {0}", "SSE.Views.PrintWithPreview.txtTo": "προς", "SSE.Views.PrintWithPreview.txtTop": "Κορυφή", "SSE.Views.ProtectDialog.textExistName": "ΣΦΑΛΜΑ! Υπάρχει ήδη εύρος με αυτόν τον τίτλο", "SSE.Views.ProtectDialog.textInvalidName": "Ο τίτλος εύρους πρέπει να ξεκινά με γράμμα και να περιέχει μόνο γράμματα, αριθμούς και διαστήματα.", "SSE.Views.ProtectDialog.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.ProtectDialog.textSelectData": "Επιλογή δεδομένων", "SSE.Views.ProtectDialog.txtAllow": "Επιτρέπεται σε όλους τους χρήστες του φύλλου να", "SSE.Views.ProtectDialog.txtAllowDescription": "Μπορείτε να ξεκλειδώσετε συγκεκριμένα εύρη για επεξεργασία.", "SSE.Views.ProtectDialog.txtAllowRanges": "Επιτρέπεται η επεξεργασία ευρών", "SSE.Views.ProtectDialog.txtAutofilter": "Χρήση Αυτόματου Φίλτρου", "SSE.Views.ProtectDialog.txtDelCols": "Διαγρα<PERSON><PERSON> στηλών", "SSE.Views.ProtectDialog.txtDelRows": "Διαγραφή γραμμών", "SSE.Views.ProtectDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.ProtectDialog.txtFormatCells": "Μορφοποίηση κελιών", "SSE.Views.ProtectDialog.txtFormatCols": "Μορφοποίη<PERSON>η στηλών", "SSE.Views.ProtectDialog.txtFormatRows": "Μορφοποίηση γραμμών", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Το συνθηματικ<PERSON> επιβεβαίωσης δεν είναι πανομοιότυπο", "SSE.Views.ProtectDialog.txtInsCols": "Εισαγω<PERSON><PERSON> στηλών", "SSE.Views.ProtectDialog.txtInsHyper": "Εισαγωγή υπερυνδέσμου", "SSE.Views.ProtectDialog.txtInsRows": "Εισαγωγή γραμμών", "SSE.Views.ProtectDialog.txtObjs": "Επεξεργα<PERSON><PERSON>α αντικειμένων", "SSE.Views.ProtectDialog.txtOptional": "προαιρετικό", "SSE.Views.ProtectDialog.txtPassword": "Συνθηματικό", "SSE.Views.ProtectDialog.txtPivot": "Χρήση συγκεντρωτικού πίνακα και συγκεντρωτικού γραφήματος", "SSE.Views.ProtectDialog.txtProtect": "Προστασία", "SSE.Views.ProtectDialog.txtRange": "Εύρ<PERSON>", "SSE.Views.ProtectDialog.txtRangeName": "Τίτλος", "SSE.Views.ProtectDialog.txtRepeat": "Επανάληψη συνθηματικού", "SSE.Views.ProtectDialog.txtScen": "Επεξεργα<PERSON><PERSON>α σεναρίων", "SSE.Views.ProtectDialog.txtSelLocked": "Επιλογή κλειδωμένων κελιών", "SSE.Views.ProtectDialog.txtSelUnLocked": "Επιλογή ξεκλείδωτων κελιών", "SSE.Views.ProtectDialog.txtSheetDescription": "Εμποδίστε ανεπιθύμητες αλλαγές από τρίτους περιορίζοντας τη δυνατότητα επεξεργασίας.", "SSE.Views.ProtectDialog.txtSheetTitle": "Προστα<PERSON><PERSON><PERSON> φύλλου", "SSE.Views.ProtectDialog.txtSort": "Ταξινόμηση", "SSE.Views.ProtectDialog.txtWarning": "Προειδοποίηση: <PERSON><PERSON><PERSON> χάσετε ή ξεχάσετε το συνθηματικ<PERSON>, δεν είναι δυνατή η ανάκτησή του. Παρακαλούμε διατηρήστε το σε ασφαλές μέρος.", "SSE.Views.ProtectDialog.txtWBDescription": "Για να αποτρέψετε άλλους χρήστες από το να βλέπουν κρυφά φύλλα εργασίας, να προσθέτουν, μ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, δι<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, κρύβ<PERSON>υν και μετονομάζουν φύλλα εργασίας, μπορείτε να προστατέψετε τη δομή του βιβλίου εργασίας σας με ένα συνθηματικό.", "SSE.Views.ProtectDialog.txtWBTitle": "Προστασί<PERSON> δομής βιβλίου εργασίας", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "Ανώνυμος", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Οποιοσδήποτε", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Επεξεργασία", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Απορρίφθηκε", "SSE.Views.ProtectedRangesEditDlg.textCanView": "Προβολή", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "Ο τίτλος εύρους πρέπει να ξεκινά με γράμμα και να περιέχει μόνο γράμματα, αριθμούς και διαστήματα.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Κατάργηση", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "Επιλογή δεδομένων", "SSE.Views.ProtectedRangesEditDlg.textYou": "εσείς", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Πρόσβαση στην εμβέλεια", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "Προστασία", "SSE.Views.ProtectedRangesEditDlg.txtRange": "Εύρ<PERSON>", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "Τίτλος", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Μόν<PERSON> εσείς μπορείτε να επεξεργαστείτε αυτό το εύρος", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Αρχίστε να πληκτρολογείτε όνομα ή email", "SSE.Views.ProtectedRangesManagerDlg.guestText": "Επισκέπτης", "SSE.Views.ProtectedRangesManagerDlg.lockText": "Κλειδωμένο", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "Διαγραφή", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "Επεξεργασία", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "Δεν έχουν δημιουργηθεί ακόμα προστατευμένες περιοχές. <br>Δημιουργήστε τουλάχιστον μία προστατευμένη περιοχή και θα εμφανιστεί σε αυτό το πεδίο.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "Φίλτρο", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "Όλα", "SSE.Views.ProtectedRangesManagerDlg.textNew": "Νέο", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Προστα<PERSON><PERSON><PERSON> φύλλου", "SSE.Views.ProtectedRangesManagerDlg.textRange": "Εύρ<PERSON>", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "Μπορείτε να περιορίσετε το εύρος επεξεργασίας σε επιλεγμένα άτομα.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "Τίτλος", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "Αυτό το στοιχείο τελεί υπό επεξεργασία από άλλο χρήστη.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Πρόσβαση", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Απορρίφθηκε", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "Επεξεργασία", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "Επεξεργασία εύρους", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "<PERSON>έ<PERSON> εύρος", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Προστατευμένα εύρη", "SSE.Views.ProtectedRangesManagerDlg.txtView": "Προβολή", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Είστε βέβαιοι ότι θέλετε να διαγράψετε το προστατευμένο εύρος {0}; <br>Οποιοσδήποτε έχει πρόσβαση επεξεργα<PERSON>ίας στο υπολογιστικό φύλλο θα μπορεί να επεξεργαστεί περιεχόμενο εντός του εύρους.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Είστε βέβαιοι ότι θέλετε να διαγράψετε τις προστατευόμενες περιοχές; <br>Οποιοσδήποτε έχει πρόσβαση επεξεργασίας στο υπολογιστικό φύλλο θα μπορεί να επεξεργαστεί περιεχόμενο σε αυτά τα εύρη.", "SSE.Views.ProtectRangesDlg.guestText": "Επισκέπτης", "SSE.Views.ProtectRangesDlg.lockText": "Κλειδωμένο", "SSE.Views.ProtectRangesDlg.textDelete": "Διαγραφή", "SSE.Views.ProtectRangesDlg.textEdit": "Επεξεργασία", "SSE.Views.ProtectRangesDlg.textEmpty": "Δεν επιτρέπεται η επεξεργασία κανενός εύρους.", "SSE.Views.ProtectRangesDlg.textNew": "Νέο", "SSE.Views.ProtectRangesDlg.textProtect": "Προστα<PERSON><PERSON><PERSON> φύλλου", "SSE.Views.ProtectRangesDlg.textPwd": "Συνθηματικό", "SSE.Views.ProtectRangesDlg.textRange": "Εύρ<PERSON>", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Εύρη που ξεκλειδώνουν με συνθηματικ<PERSON> όταν το φύλλο είναι προστατευμένο (δουλεύει μόνο για κλειδωμένα κελιά)", "SSE.Views.ProtectRangesDlg.textTitle": "Τίτλος", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Αυτό το στοιχείο τελεί υπό επεξεργασία από άλλο χρήστη.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Επεξεργασία εύρους", "SSE.Views.ProtectRangesDlg.txtNewRange": "<PERSON>έ<PERSON> εύρος", "SSE.Views.ProtectRangesDlg.txtNo": "Όχι", "SSE.Views.ProtectRangesDlg.txtTitle": "Επιτρέπεται στους χρήστες η επεξεργασία ευρών", "SSE.Views.ProtectRangesDlg.txtYes": "Ναι", "SSE.Views.ProtectRangesDlg.warnDelete": "Θέλετε σίγουρα να διαγράψετε το όνομα {0};", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Στήλες", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Για να διαγράψετε διπλότυπες τιμές, επιλέξτε μία ή περισσότερες στήλες που περιέχουν διπλότυπα.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Τα δεδομένα μου έχουν κεφαλίδες", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Επιλογ<PERSON> όλων ", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Αφαίρεση διπλότυπων", "SSE.Views.RightMenu.ariaRightMenu": "Δεξί μενού", "SSE.Views.RightMenu.txtCellSettings": "Ρυθμίσεις κελιού", "SSE.Views.RightMenu.txtChartSettings": "Ρυθμίσεις γραφήματος", "SSE.Views.RightMenu.txtImageSettings": "Ρυθμίσεις εικόνας", "SSE.Views.RightMenu.txtParagraphSettings": "Ρυθμίσεις παραγράφου", "SSE.Views.RightMenu.txtPivotSettings": "Ρυθμίσεις συγκεντρωτικού πίνακα", "SSE.Views.RightMenu.txtSettings": "Κοινές ρυθμίσεις", "SSE.Views.RightMenu.txtShapeSettings": "Ρυθμίσεις σχήματος", "SSE.Views.RightMenu.txtSignatureSettings": "Ρυθμίσεις υπογραφής", "SSE.Views.RightMenu.txtSlicerSettings": "Ρυθμίσεις αναλυτή", "SSE.Views.RightMenu.txtSparklineSettings": "Ρυθμίσεις μικρογραφήματος", "SSE.Views.RightMenu.txtTableSettings": "Ρυθμίσεις πίνακα", "SSE.Views.RightMenu.txtTextArtSettings": "Ρυθμίσεις καλλιτεχνικού κειμένου", "SSE.Views.ScaleDialog.textAuto": "Αυτόματα", "SSE.Views.ScaleDialog.textError": "Η εισηγμένη τιμή είναι εσφαλμένη.", "SSE.Views.ScaleDialog.textFewPages": "σελίδες", "SSE.Views.ScaleDialog.textFitTo": "Προσαρμογή σε", "SSE.Views.ScaleDialog.textHeight": "Ύψος", "SSE.Views.ScaleDialog.textManyPages": "σελίδες", "SSE.Views.ScaleDialog.textOnePage": "σελίδα", "SSE.Views.ScaleDialog.textScaleTo": "Κλιμάκωση σε", "SSE.Views.ScaleDialog.textTitle": "Ρυθμίσεις κλίμακας", "SSE.Views.ScaleDialog.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "Η μέγιστη τιμή για αυτό το πεδίο είναι {0}", "SSE.Views.SetValueDialog.txtMinText": "Η ελάχιστη τιμή για αυτό το πεδίο είναι {0}", "SSE.Views.ShapeSettings.strBackground": "Χρώμα παρασκηνίου", "SSE.Views.ShapeSettings.strChange": "Αλλαγ<PERSON>", "SSE.Views.ShapeSettings.strColor": "Χρώμα", "SSE.Views.ShapeSettings.strFill": "Γέμισμα", "SSE.Views.ShapeSettings.strForeground": "Χρώμα προσκηνίου", "SSE.Views.ShapeSettings.strPattern": "Μοτίβο", "SSE.Views.ShapeSettings.strShadow": "Εμφάνιση σκιάς", "SSE.Views.ShapeSettings.strSize": "Μέγεθος", "SSE.Views.ShapeSettings.strStroke": "Πινελιά", "SSE.Views.ShapeSettings.strTransparency": "Αδιαφάνεια", "SSE.Views.ShapeSettings.strType": "Τύπος", "SSE.Views.ShapeSettings.textAdjustShadow": "Ρύθμιση σκιάς", "SSE.Views.ShapeSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "SSE.Views.ShapeSettings.textAngle": "Γωνία", "SSE.Views.ShapeSettings.textBorderSizeErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια αριθμητική τιμή μεταξύ 0 pt και 1584 pt.", "SSE.Views.ShapeSettings.textColor": "Γέμισμα με χρώμα", "SSE.Views.ShapeSettings.textDirection": "Κατεύθυνση", "SSE.Views.ShapeSettings.textEditPoints": "Επεξεργασία σημείων", "SSE.Views.ShapeSettings.textEditShape": "Επεξεργα<PERSON><PERSON>α σχήματος", "SSE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textEyedropper": "Σταγονόμετρο", "SSE.Views.ShapeSettings.textFlip": "Περιστροφή", "SSE.Views.ShapeSettings.textFromFile": "Από αρχείο", "SSE.Views.ShapeSettings.textFromStorage": "Από αποθηκευτικό χώρο", "SSE.Views.ShapeSettings.textFromUrl": "Από διεύθυνση URL", "SSE.Views.ShapeSettings.textGradient": "Σημεία διαβάθμισης", "SSE.Views.ShapeSettings.textGradientFill": "Βαθμωτό γέμισμα", "SSE.Views.ShapeSettings.textHint270": "Περιστροφή 90° Αριστερόστροφα", "SSE.Views.ShapeSettings.textHint90": "Περιστροφή 90° Δεξιόστροφα", "SSE.Views.ShapeSettings.textHintFlipH": "Οριζόντια περιστροφή", "SSE.Views.ShapeSettings.textHintFlipV": "Κατακόρυφη περιστροφή", "SSE.Views.ShapeSettings.textImageTexture": "Εικόνα ή υφή", "SSE.Views.ShapeSettings.textLinear": "Γραμμικός", "SSE.Views.ShapeSettings.textMoreColors": "Περισσότερα χρώματα", "SSE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON><PERSON><PERSON><PERSON> γέμισμα", "SSE.Views.ShapeSettings.textNoShadow": "<PERSON><PERSON><PERSON><PERSON><PERSON> σκιά", "SSE.Views.ShapeSettings.textOriginalSize": "Αρχικό μέγεθος", "SSE.Views.ShapeSettings.textPatternFill": "Μοτίβο", "SSE.Views.ShapeSettings.textPosition": "Θέση", "SSE.Views.ShapeSettings.textRadial": "Ακτινικ<PERSON>ς", "SSE.Views.ShapeSettings.textRecentlyUsed": "Πρόσφατα χρησιμοποιημένα", "SSE.Views.ShapeSettings.textRotate90": "Περιστροφή 90°", "SSE.Views.ShapeSettings.textRotation": "Περιστροφή", "SSE.Views.ShapeSettings.textSelectImage": "Επιλογή εικόνας", "SSE.Views.ShapeSettings.textSelectTexture": "Επιλογή", "SSE.Views.ShapeSettings.textShadow": "Σκιά", "SSE.Views.ShapeSettings.textStretch": "Έκταση", "SSE.Views.ShapeSettings.textStyle": "Τεχνοτροπία", "SSE.Views.ShapeSettings.textTexture": "Από υφή", "SSE.Views.ShapeSettings.textTile": "Πλακίδιο", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Προσθήκη σημείου διαβάθμισης", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Αφαίρεση σημείου διαβάθμισης", "SSE.Views.ShapeSettings.txtBrownPaper": "Καφέ χαρτί", "SSE.Views.ShapeSettings.txtCanvas": "Καμβάς", "SSE.Views.ShapeSettings.txtCarton": "Χ<PERSON>ρτόνι", "SSE.Views.ShapeSettings.txtDarkFabric": "Σκούρο ύφασμα", "SSE.Views.ShapeSettings.txtGrain": "Κόκκος", "SSE.Views.ShapeSettings.txtGranite": "Γραν<PERSON>της", "SSE.Views.ShapeSettings.txtGreyPaper": "Γκρι χαρτί", "SSE.Views.ShapeSettings.txtKnit": "Πλέκω", "SSE.Views.ShapeSettings.txtLeather": "Δέρμα", "SSE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "Ξύλο", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Στήλες", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Απόσταση κειμένου", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Να μην αλλάζει θέση ή μέγεθος με τα κελιά", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Εναλλακτικό κείμενο", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Περιγραφή", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Η εναλλακτική με βάση κείμενο αναπαράσταση των πληροφοριών οπτικού αντικειμένου, η οποία θα αναγνωσθε<PERSON> στα άτομα με προβλήματα όρασης ή γνωστικών προβλημάτων για να τους βοηθήσουν να κατανοήσουν καλύτερα ποιες πληροφορίες υπάρχουν στην εικόνα, σε σχήμα, στο διάγραμμα ή στον πίνακα.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Τίτλος", "SSE.Views.ShapeSettingsAdvanced.textAngle": "Γωνία", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Βέλη", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Αυτόματο Ταίριασμα", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Μέγ<PERSON><PERSON>ος εκκίνησης", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Τεχνοτροπία εκκίνησης", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Πλάγια Τομή", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Κάτω", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Τύπος Cap ", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Αριθμ<PERSON><PERSON> στηλών", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Μέγ<PERSON><PERSON>ος τέλους", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Τεχνοτροπία τέλους", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Επίπεδο", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Περιεστρεμμένο", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Ύψος", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Οριζόντια", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Τύ<PERSON>ος ένωσης", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Σταθερές αναλογίες", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Αριστερά", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Τεχνοτροπία γραμμής", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Μίτρα", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Μετακίνηση αλλά όχι αλλαγή μεγέθους με τα κελιά", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Να επιτρέπεται η επέκταση κειμένου εκτός σχήματος", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Προσαρμογή σχήματος για ταίριασμα με το κείμενο", "SSE.Views.ShapeSettingsAdvanced.textRight": "Δεξιά", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Περιστροφή", "SSE.Views.ShapeSettingsAdvanced.textRound": "Στρογγυλεμένο", "SSE.Views.ShapeSettingsAdvanced.textSize": "Μέγεθος", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Ευθυγράμμιση σε κελί", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Απόσταση μεταξύ στηλών", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Τετράγωνο", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Πλα<PERSON><PERSON><PERSON><PERSON> κειμένου", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Σχήμα - Προηγμένες ρυθμίσεις", "SSE.Views.ShapeSettingsAdvanced.textTop": "Επάνω", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Μετακίνηση και αλλαγή μεγέθους με τα κελιά", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Κατακόρυφα", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Πλάτη & Βέλη", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Προειδοποίηση", "SSE.Views.SignatureSettings.strDelete": "Αφαίρεση Υπογραφής", "SSE.Views.SignatureSettings.strDetails": "Λεπτομέρειες υπογραφής", "SSE.Views.SignatureSettings.strInvalid": "Μη έγκυρες υπογραφές", "SSE.Views.SignatureSettings.strRequested": "Αιτηθείσες υπογραφές", "SSE.Views.SignatureSettings.strSetup": "Ρύθμιση υπογραφής", "SSE.Views.SignatureSettings.strSign": "Σύμβολο", "SSE.Views.SignatureSettings.strSignature": "Υπογραφή", "SSE.Views.SignatureSettings.strSigner": "Υπογράφων", "SSE.Views.SignatureSettings.strValid": "Έγκυρες υπογραφές", "SSE.Views.SignatureSettings.txtContinueEditing": "Επεξεργασ<PERSON>α ούτως ή άλλως", "SSE.Views.SignatureSettings.txtEditWarning": "Η επεξεργασία θα αφαιρέσει τις υπογραφές από το υπολογιστικό φύλλο.<br>Θέλετε σίγουρα να συνεχίσετε;", "SSE.Views.SignatureSettings.txtRemoveWarning": "Θέλετε να αφαιρέσετε αυτή την υπογραφή;<br>Δεν μπορεί να αναιρεθεί.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Αυτό το υπολογιστικό φύλλο πρέπει να υπογραφεί.", "SSE.Views.SignatureSettings.txtSigned": "Προστέθηκαν έγκυρες υπογραφές στο λογιστικό φύλλο. Το φύλλο προστατεύεται από επεξεργασία.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Κάποιες από τις ψηφιακές υπογραφές στο υπολογιστικό φύλλο δεν είναι έγκυρες ή δεν ήταν δυνατό να επιβεβαιωθούν. Το υπολογιστικό φύλλο προστατεύεται από επεξεργασία.", "SSE.Views.SlicerAddDialog.textColumns": "Στήλες", "SSE.Views.SlicerAddDialog.txtTitle": "Εισαγω<PERSON><PERSON> αναλυτών", "SSE.Views.SlicerSettings.strHideNoData": "Απόκρυψη στοιχείων χωρίς δεδομένα", "SSE.Views.SlicerSettings.strIndNoData": "Οπτική κατάδειξη στοιχείων χωρίς δεδομένα", "SSE.Views.SlicerSettings.strShowDel": "Εμφάνιση διαγραμμένων στοιχείων από την πηγή δεδομένων", "SSE.Views.SlicerSettings.strShowNoData": "Εμφάνιση στοιχείων χωρίς καθόλου δεδομένα τελευταία", "SSE.Views.SlicerSettings.strSorting": "Ταξινόμηση και φιλτράρισμα", "SSE.Views.SlicerSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "SSE.Views.SlicerSettings.textAsc": "Αύξουσα", "SSE.Views.SlicerSettings.textAZ": "A έως Ω", "SSE.Views.SlicerSettings.textButtons": "Κουμπιά", "SSE.Views.SlicerSettings.textColumns": "Στήλες", "SSE.Views.SlicerSettings.textDesc": "Φθίνουσα", "SSE.Views.SlicerSettings.textHeight": "Ύψος", "SSE.Views.SlicerSettings.textHor": "Οριζόντια", "SSE.Views.SlicerSettings.textKeepRatio": "Σταθερές Αναλογίες", "SSE.Views.SlicerSettings.textLargeSmall": "μεγαλύτερο προς μικρότερο", "SSE.Views.SlicerSettings.textLock": "Απενεργοποίηση αλλαγής μεγέθους και μετακίνησης", "SSE.Views.SlicerSettings.textNewOld": "από το νεότερο στο παλαιότερο", "SSE.Views.SlicerSettings.textOldNew": "από το παλαιότερο στο νεότερο", "SSE.Views.SlicerSettings.textPosition": "Θέση", "SSE.Views.SlicerSettings.textSize": "Μέγεθος", "SSE.Views.SlicerSettings.textSmallLarge": "από το μικρότερο στο μεγαλύτερο", "SSE.Views.SlicerSettings.textStyle": "Τεχνοτροπία", "SSE.Views.SlicerSettings.textVert": "Κατακόρυφος", "SSE.Views.SlicerSettings.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "Ω έως Α", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Κουμπιά", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Στήλες", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Ύψος", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Απόκρυψη στοιχείων χωρίς δεδομένα", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Οπτική κατάδειξη στοιχείων χωρίς δεδομένα", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Παραπομπ<PERSON>ς", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Εμφάνιση διαγραμμένων στοιχείων από την πηγή δεδομένων", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Εμφάνισης κεφαλίδας", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Εμφάνιση στοιχείων χωρίς καθόλου δεδομένα τελευταία", "SSE.Views.SlicerSettingsAdvanced.strSize": "Μέγεθος", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Ταξινόμηση & Φιλτράρισμα", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Τεχνοτροπία", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Τεχνοτροπία & Μέγεθος", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Να μην αλλάζει θέση ή μέγεθος με τα κελιά", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Εναλλακτικό κείμενο", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Περιγραφή", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Η εναλλακτική, κειμενική αναπαράσταση των πληροφοριών του οπτικού αντικειμένου, που θα αναγνωστεί σε ανθρώπους με προβλήματα όρασης ή γνωστικές αδυναμίες, για να κατανοήσουν καλύτερα τις πληροφορίες που περιέχονται στην εικόνα, σχήμα, γρά<PERSON>η<PERSON><PERSON> ή πίνακα.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Τίτλος", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Αύξουσα", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A έως Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Φθίνουσα", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Όνομα που θα χρησιμοποιείται στους τύπους", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Κεφαλίδα", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Σταθερές αναλογίες", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "μεγαλύτερο προς μικρότερο", "SSE.Views.SlicerSettingsAdvanced.textName": "Όνομα", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "από το νεότερο στο παλαιότερο", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "από το παλαιότερο στο νεότερο", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Μετακίνηση αλλά όχι αλλαγή μεγέθους με τα κελιά", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "από το μικρότερο στο μεγαλύτερο", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Ευθυγράμμιση σε κελί", "SSE.Views.SlicerSettingsAdvanced.textSort": "Ταξινόμηση", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Όνομα πηγής", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Αναλυτής- Προηγμένες ρυθμίσεις", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Μετακίνηση και αλλαγή μεγέθους με τα κελιά", "SSE.Views.SlicerSettingsAdvanced.textZA": "Ω έως Α", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.SortDialog.errorEmpty": "Όλα τα κριτήρια ταξινόμησης πρέπει να ορίζουν μια στήλη ή γραμμή", "SSE.Views.SortDialog.errorMoreOneCol": "Περισσότερες από μία στήλες είναι επιλεγμένες.", "SSE.Views.SortDialog.errorMoreOneRow": "Περισσότερες από μία γραμμή είναι επιλεγμένες", "SSE.Views.SortDialog.errorNotOriginalCol": "Η στήλη που επιλέξατε δεν περιλαμβάνεται στο αρχικά επιλεγμένο εύρος.", "SSE.Views.SortDialog.errorNotOriginalRow": "Η γραμμή που επιλέξατε δεν είναι στο αρχικό επιλεγμένο εύρος.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 ταξινομείται με το ίδιο χρώμα περισσότερες από μία φορές.<br>Διαγράψτε τα διπλότυπα κριτήρια ταξινόμησης και προσπαθήστε ξανά.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 ταξινομείται ως προς τις τιμές περισσότερες από μία φορές.<br>Διαγράψτε τα διπλότυπα κριτήρια ταξινόμησης και προσπαθήστε ξανά.", "SSE.Views.SortDialog.textAsc": "Αύξουσα", "SSE.Views.SortDialog.textAuto": "Αυτόματα", "SSE.Views.SortDialog.textAZ": "A έως Ω", "SSE.Views.SortDialog.textBelow": "Από κάτω", "SSE.Views.SortDialog.textBtnCopy": "Αντιγραφή", "SSE.Views.SortDialog.textBtnDelete": "Διαγραφή", "SSE.Views.SortDialog.textBtnNew": "Νέο", "SSE.Views.SortDialog.textCellColor": "Χρώμα κελιού", "SSE.Views.SortDialog.textColumn": "Στήλη", "SSE.Views.SortDialog.textDesc": "Φθίνουσα", "SSE.Views.SortDialog.textDown": "Μετακίνηση επιπέδου κάτω", "SSE.Views.SortDialog.textFontColor": "Χρώμα γραμματοσειράς", "SSE.Views.SortDialog.textLeft": "Αριστερά", "SSE.Views.SortDialog.textLevels": "Επίπεδα", "SSE.Views.SortDialog.textMoreCols": "(Περισσότερες στήλες...)", "SSE.Views.SortDialog.textMoreRows": "(Περισσότερες γραμμές...)", "SSE.Views.SortDialog.textNone": "Κανένα", "SSE.Views.SortDialog.textOptions": "Επιλογές", "SSE.Views.SortDialog.textOrder": "Σειρά", "SSE.Views.SortDialog.textRight": "Δεξιά", "SSE.Views.SortDialog.textRow": "Γραμμή", "SSE.Views.SortDialog.textSort": "Ταξινόμηση σε", "SSE.Views.SortDialog.textSortBy": "Ταξινόμηση κατά", "SSE.Views.SortDialog.textThenBy": "Τότε από", "SSE.Views.SortDialog.textTop": "Επάνω", "SSE.Views.SortDialog.textUp": "Μετακίνηση επιπέδου πάνω", "SSE.Views.SortDialog.textValues": "Τιμές", "SSE.Views.SortDialog.textZA": "Ω έως Α", "SSE.Views.SortDialog.txtInvalidRange": "Μη έγκυρο εύρος κελιών.", "SSE.Views.SortDialog.txtTitle": "Ταξινόμηση", "SSE.Views.SortFilterDialog.textAsc": "Αύξουσα (A έως Ω) κατά", "SSE.Views.SortFilterDialog.textDesc": "Φθίνουσα (Ω έως Α) κατά", "SSE.Views.SortFilterDialog.textNoSort": "<PERSON><PERSON><PERSON><PERSON><PERSON> ταξινόμιση", "SSE.Views.SortFilterDialog.txtTitle": "Ταξινόμηση", "SSE.Views.SortFilterDialog.txtTitleValue": "Ταξινόμηση κατά τιμή", "SSE.Views.SortOptionsDialog.textCase": "Με διάκριση πεζών - κε<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ν γραμμάτων", "SSE.Views.SortOptionsDialog.textHeaders": "Τα δεδομένα μου έχουν κεφαλίδες", "SSE.Views.SortOptionsDialog.textLeftRight": "Ταξινόμηση από αριστερά προς τα δεξιά", "SSE.Views.SortOptionsDialog.textOrientation": "Προσανατολισμός", "SSE.Views.SortOptionsDialog.textTitle": "Επιλογ<PERSON>ς ταξινόμησης", "SSE.Views.SortOptionsDialog.textTopBottom": "Ταξινόμηση από πάνω προς τα κάτω", "SSE.Views.SpecialPasteDialog.textAdd": "Προσθήκη", "SSE.Views.SpecialPasteDialog.textAll": "Όλα", "SSE.Views.SpecialPasteDialog.textBlanks": "Προσπέρα<PERSON>η κενών", "SSE.Views.SpecialPasteDialog.textColWidth": "Πλάτη στηλών", "SSE.Views.SpecialPasteDialog.textComments": "Σχόλια", "SSE.Views.SpecialPasteDialog.textDiv": "Διαίρεση", "SSE.Views.SpecialPasteDialog.textFFormat": "Τύποι & μορφοποίηση", "SSE.Views.SpecialPasteDialog.textFNFormat": "Τύποι & μορφές αριθμών", "SSE.Views.SpecialPasteDialog.textFormats": "Μορφοποιήσεις", "SSE.Views.SpecialPasteDialog.textFormulas": "Τύποι", "SSE.Views.SpecialPasteDialog.textFWidth": "Πλάτη μαθηματικών τύπων & στηλών", "SSE.Views.SpecialPasteDialog.textMult": "Επί", "SSE.Views.SpecialPasteDialog.textNone": "Κανένα", "SSE.Views.SpecialPasteDialog.textOperation": "Λειτουργία", "SSE.Views.SpecialPasteDialog.textPaste": "Επικόλληση", "SSE.Views.SpecialPasteDialog.textSub": "Αφαίρεση", "SSE.Views.SpecialPasteDialog.textTitle": "Ειδική επικόλληση", "SSE.Views.SpecialPasteDialog.textTranspose": "Μετατόπιση", "SSE.Views.SpecialPasteDialog.textValues": "Τιμές", "SSE.Views.SpecialPasteDialog.textVFormat": "Τιμές & μορφοποίηση", "SSE.Views.SpecialPasteDialog.textVNFormat": "Τιμές & μορφές αριθμών", "SSE.Views.SpecialPasteDialog.textWBorders": "Όλα εκτός από τα περιγράμματα", "SSE.Views.Spellcheck.noSuggestions": "Δεν υπάρχουν προτάσεις ορθογραφίας", "SSE.Views.Spellcheck.textChange": "Αλλαγή", "SSE.Views.Spellcheck.textChangeAll": "Αλλα<PERSON><PERSON>λων", "SSE.Views.Spellcheck.textIgnore": "Αγνόηση", "SSE.Views.Spellcheck.textIgnoreAll": "Αγνόηση όλων", "SSE.Views.Spellcheck.txtAddToDictionary": "Προσθήκη στο λεξικό", "SSE.Views.Spellcheck.txtClosePanel": "Κλείσιμο συλλαβισμού", "SSE.Views.Spellcheck.txtComplete": "Ο έλεγχος ορθογρα<PERSON><PERSON>ας ολοκληρώθηκε", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Γλώσσ<PERSON> λεξικού", "SSE.Views.Spellcheck.txtNextTip": "Μετάβαση στην επόμενη λέξη", "SSE.Views.Spellcheck.txtSpelling": "Ορθογραφία", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Με<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>η στο τέλος)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "Δημιουρ<PERSON><PERSON><PERSON> αντιγράφου", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(Δημιουργ<PERSON>α νέου υπολογιστικού φύλλου)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Μετακίνηση πριν το φύλλο", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Λογιστικ<PERSON> φύλλο", "SSE.Views.Statusbar.filteredRecordsText": "{0} από {1} εγγραφ<PERSON>ς φιλτραρίστηκαν", "SSE.Views.Statusbar.filteredText": "Κατάσταση φιλτραρίσματος", "SSE.Views.Statusbar.itemAverage": "Μέ<PERSON><PERSON> Όρος", "SSE.Views.Statusbar.itemCount": "Μέτρηση", "SSE.Views.Statusbar.itemDelete": "Διαγραφή", "SSE.Views.Statusbar.itemHidden": "Κρυφό", "SSE.Views.Statusbar.itemHide": "Απόκρυψη", "SSE.Views.Statusbar.itemInsert": "Εισαγωγή", "SSE.Views.Statusbar.itemMaximum": "Μέγιστο", "SSE.Views.Statusbar.itemMinimum": "Ελάχιστο", "SSE.Views.Statusbar.itemMoveOrCopy": "Μετακ<PERSON><PERSON>η<PERSON>η ή αντιγραφή", "SSE.Views.Statusbar.itemProtect": "Προστασία", "SSE.Views.Statusbar.itemRename": "Μετονομασία", "SSE.Views.Statusbar.itemStatus": "Κατάσταση αποθήκευσης", "SSE.Views.Statusbar.itemSum": "Άθροισμα", "SSE.Views.Statusbar.itemTabColor": "Χρώ<PERSON>α στηλοθέτη", "SSE.Views.Statusbar.itemUnProtect": "Άρση Προστασίας", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Υπάρχει ήδη ένα φύλλο εργασίας με αυτό το όνομα", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Ένα όνομα φύλλου δεν μπορεί να περιέχει τους ακόλουθους χαρακτήρες: /*? []: ή ο χαρακτήρας ' ως πρώτος ή τελευταίος χαρακτήρας", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Όνομα φύλλου", "SSE.Views.Statusbar.selectAllSheets": "Επιλογή όλων των φύλλων", "SSE.Views.Statusbar.sheetIndexText": "Φύλλο {0} από {1}", "SSE.Views.Statusbar.textAverage": "Μέ<PERSON><PERSON> Όρος", "SSE.Views.Statusbar.textCount": "Μέτρηση", "SSE.Views.Statusbar.textMax": "Μέγιστο", "SSE.Views.Statusbar.textMin": "Ελάχιστο", "SSE.Views.Statusbar.textNewColor": "Περισσότερα χρώματα", "SSE.Views.Statusbar.textNoColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textSum": "Άθροισμα", "SSE.Views.Statusbar.tipAddTab": "Προσθήκη φύλλου εργασίας", "SSE.Views.Statusbar.tipFirst": "Κύλιση στο πρώτο φύλλο", "SSE.Views.Statusbar.tipLast": "Κύλιση στο τελευτα<PERSON><PERSON> φύλλο", "SSE.Views.Statusbar.tipListOfSheets": "Λίστα φύλλων", "SSE.Views.Statusbar.tipNext": "Κύλιση λίστας φύλλων δεξιά", "SSE.Views.Statusbar.tipPrev": "Κύλιση λίστας φύλλων αριστερά", "SSE.Views.Statusbar.tipZoomFactor": "Εστίαση", "SSE.Views.Statusbar.tipZoomIn": "Μεγέθυνση", "SSE.Views.Statusbar.tipZoomOut": "Σμίκρυνση", "SSE.Views.Statusbar.ungroupSheets": "Κατάργηση ομαδοποίησης φύλλων", "SSE.Views.Statusbar.zoomText": "Εστίαση {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Δεν ήταν δυνατή η εκτέλεση της επιλεγμένης περιοχής κελιών.<br>Επιλέξτε ένα ομοιόμορφο εύρος δεδομένων διαφορετικό από το υπάρχον και δοκιμάστε ξανά.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Δεν ήταν δυνατή η ολοκλήρωση της λειτουργίας για το επιλεγμένο εύρος κελιών.<br>Ε<PERSON><PERSON>λ<PERSON>ξτε ένα εύρος ώστε η πρώτη γραμμή του πίνακα να είναι στην ίδια γραμμή<br>και ο παραγόμενος πίνακας να επικαλύπτει τον τρέχοντα.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Δεν ήταν δυνατή η ολοκλήρωση της λειτουργίας για το επιλεγμένο εύρος κελιών.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>τε ένα εύρος που δεν περιλαμβάνει άλλους πίνακες.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Οι τύποι συστοιχιών πολλών κελιών δεν επιτρέπονται στους πίνακες.", "SSE.Views.TableOptionsDialog.txtEmpty": "Αυτό το πεδίο είναι υποχρεωτικό", "SSE.Views.TableOptionsDialog.txtFormat": "Δημιουργ<PERSON>α πίνακα", "SSE.Views.TableOptionsDialog.txtInvalidRange": "ΣΦΑΛΜΑ! Μη έγκυρο εύρος κελιών", "SSE.Views.TableOptionsDialog.txtNote": "Οι κεφαλίδες πρέπει να παραμείνουν στην ίδια γραμμή και το παραγόμενο εύρος πίνακα να επικαλύπτει το αρχικό εύρος πίνακα.", "SSE.Views.TableOptionsDialog.txtTitle": "Τίτλος", "SSE.Views.TableSettings.deleteColumnText": "Διαγρα<PERSON><PERSON> στήλης", "SSE.Views.TableSettings.deleteRowText": "Διαγραφή γραμμής", "SSE.Views.TableSettings.deleteTableText": "Διαγραφή πίνακα", "SSE.Views.TableSettings.insertColumnLeftText": "Εισαγω<PERSON><PERSON> στήλης αριστερά", "SSE.Views.TableSettings.insertColumnRightText": "Εισαγωγή στήλης δεξιά", "SSE.Views.TableSettings.insertRowAboveText": "Εισαγωγή γραμμής από επάνω", "SSE.Views.TableSettings.insertRowBelowText": "Εισαγωγή γραμμής από κάτω", "SSE.Views.TableSettings.notcriticalErrorTitle": "Προειδοποίηση", "SSE.Views.TableSettings.selectColumnText": "Επιλογή ολόκληρης στήλης", "SSE.Views.TableSettings.selectDataText": "Επιλογή δεδομένων στήλης", "SSE.Views.TableSettings.selectRowText": "Επιλογή γραμμής", "SSE.Views.TableSettings.selectTableText": "Επιλογή πίνακα", "SSE.Views.TableSettings.textActions": "Ενέργειες πίνακα", "SSE.Views.TableSettings.textAdvanced": "Εμφάνιση προηγμένων ρυθμίσεων", "SSE.Views.TableSettings.textBanded": "Με εναλλαγή σκίασης", "SSE.Views.TableSettings.textColumns": "Στήλες", "SSE.Views.TableSettings.textConvertRange": "Μετατροπή σε εύρος", "SSE.Views.TableSettings.textEdit": "Γραμμές & Στήλες", "SSE.Views.TableSettings.textEmptyTemplate": "Κανένα πρότυπο", "SSE.Views.TableSettings.textExistName": "ΣΦΑΛΜΑ! Υπάρχει ήδη εύρος με αυτό το όνομα", "SSE.Views.TableSettings.textFilter": "Κουμπί φίλτρου", "SSE.Views.TableSettings.textFirst": "Πρώτη", "SSE.Views.TableSettings.textHeader": "Κεφαλίδα", "SSE.Views.TableSettings.textInvalidName": "ΣΦΑΛΜΑ! Μη έγκυρο όνομα πίνακα", "SSE.Views.TableSettings.textIsLocked": "Αυτό το στοιχείο τελεί υπό επεξεργασία από άλλο χρήστη.", "SSE.Views.TableSettings.textLast": "Τελευταία", "SSE.Views.TableSettings.textLongOperation": "Η λειτουργία είναι χρονοβόρα", "SSE.Views.TableSettings.textPivot": "Εισαγωγ<PERSON> συγκεντρωτικού πίνακα", "SSE.Views.TableSettings.textRemDuplicates": "Αφαίρεση διπλότυπων", "SSE.Views.TableSettings.textReservedName": "Το όνομα που προσπαθείτε να χρησιμοποιήσετε αναφέρεται ήδη σε τύπους κελιών. Παρακαλούμε χρησιμοποιήστε κάποιο άλλο όνομα.", "SSE.Views.TableSettings.textResize": "Αλλαγή μεγέθους πίνακα", "SSE.Views.TableSettings.textRows": "Γραμμές", "SSE.Views.TableSettings.textSelectData": "Επιλογή δεδομένων", "SSE.Views.TableSettings.textSlicer": "Εισαγωγ<PERSON> αναλυτή", "SSE.Views.TableSettings.textTableName": "Όνομα πίνακα", "SSE.Views.TableSettings.textTemplate": "Επιλογή από πρότυπο", "SSE.Views.TableSettings.textTotal": "Σύνολο", "SSE.Views.TableSettings.txtGroupTable_Custom": "Προσαρμογή", "SSE.Views.TableSettings.txtGroupTable_Dark": "Σκουρόχρωμο", "SSE.Views.TableSettings.txtGroupTable_Light": "Ανοιχτόχρωμο", "SSE.Views.TableSettings.txtGroupTable_Medium": "Μεσαίο", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Σκούρα τεχνοτροπία πίνακα", "SSE.Views.TableSettings.txtTable_TableStyleLight": "Φωτεινή τεχνοτροπία πίνακα", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "Ενδιάμεση τεχνοτροπία πίνακα", "SSE.Views.TableSettings.warnLongOperation": "Η λειτουργία που πρόκειται να εκτελέσετε ίσως χρειαστεί πολύ χρόνο για να ολοκληρωθεί.<br>Θέλετε σίγουρα να συνεχίσετε;", "SSE.Views.TableSettingsAdvanced.textAlt": "Εναλλακτικό κείμενο", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Περιγραφή", "SSE.Views.TableSettingsAdvanced.textAltTip": "Η εναλλακτική, κειμενική αναπαράσταση των πληροφοριών του οπτικού αντικειμένου, που θα αναγνωστεί σε ανθρώπους με προβλήματα όρασης ή γνωστικές αδυναμίες, για να κατανοήσουν καλύτερα τις πληροφορίες που περιέχονται στην εικόνα, σχήμα, γρά<PERSON>η<PERSON><PERSON> ή πίνακα.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Τίτλος", "SSE.Views.TableSettingsAdvanced.textTitle": "Πίνακας - Προηγμένες ρυθμίσεις", "SSE.Views.TextArtSettings.strBackground": "Χρώμα παρασκηνίου", "SSE.Views.TextArtSettings.strColor": "Χρώμα", "SSE.Views.TextArtSettings.strFill": "Γέμισμα", "SSE.Views.TextArtSettings.strForeground": "Χρώμα προσκηνίου", "SSE.Views.TextArtSettings.strPattern": "Μοτίβο", "SSE.Views.TextArtSettings.strSize": "Μέγεθος", "SSE.Views.TextArtSettings.strStroke": "Πινελιά", "SSE.Views.TextArtSettings.strTransparency": "Αδιαφάνεια", "SSE.Views.TextArtSettings.strType": "Τύπος", "SSE.Views.TextArtSettings.textAngle": "Γωνία", "SSE.Views.TextArtSettings.textBorderSizeErr": "Η τιμή που βάλατε δεν είναι αποδεκτή.<br>Παρακαλούμε βάλτε μια αριθμητική τιμή μεταξύ 0 pt και 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Γέμισμα με χρώμα", "SSE.Views.TextArtSettings.textDirection": "Κατεύθυνση", "SSE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textFromFile": "Από αρχείο", "SSE.Views.TextArtSettings.textFromUrl": "Από διεύθυνση URL", "SSE.Views.TextArtSettings.textGradient": "Σημεία διαβάθμισης", "SSE.Views.TextArtSettings.textGradientFill": "Βαθμωτό γέμισμα", "SSE.Views.TextArtSettings.textImageTexture": "Εικόνα ή υφή", "SSE.Views.TextArtSettings.textLinear": "Γραμμικός", "SSE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON><PERSON><PERSON><PERSON> γέμισμα", "SSE.Views.TextArtSettings.textPatternFill": "Μοτίβο", "SSE.Views.TextArtSettings.textPosition": "Θέση", "SSE.Views.TextArtSettings.textRadial": "Ακτινικ<PERSON>ς", "SSE.Views.TextArtSettings.textSelectTexture": "Επιλογή", "SSE.Views.TextArtSettings.textStretch": "Έκταση", "SSE.Views.TextArtSettings.textStyle": "Τεχνοτροπία", "SSE.Views.TextArtSettings.textTemplate": "Πρότυπο", "SSE.Views.TextArtSettings.textTexture": "Από υφή", "SSE.Views.TextArtSettings.textTile": "Πλακίδιο", "SSE.Views.TextArtSettings.textTransform": "Μετασχηματισμός", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Προσθήκη σημείου διαβάθμισης", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Αφαίρεση σημείου διαβάθμισης", "SSE.Views.TextArtSettings.txtBrownPaper": "Καφέ χαρτί", "SSE.Views.TextArtSettings.txtCanvas": "Καμβάς", "SSE.Views.TextArtSettings.txtCarton": "Χ<PERSON>ρτόνι", "SSE.Views.TextArtSettings.txtDarkFabric": "Σκούρο ύφασμα", "SSE.Views.TextArtSettings.txtGrain": "Κόκκος", "SSE.Views.TextArtSettings.txtGranite": "Γραν<PERSON>της", "SSE.Views.TextArtSettings.txtGreyPaper": "Γκρι χαρτί", "SSE.Views.TextArtSettings.txtKnit": "Πλέκω", "SSE.Views.TextArtSettings.txtLeather": "Δέρμα", "SSE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtWood": "Ξύλο", "SSE.Views.Toolbar.capBtnAddComment": "Προσθήκη σχολίου", "SSE.Views.Toolbar.capBtnColorSchemas": "Χρώματα", "SSE.Views.Toolbar.capBtnComment": "Σχόλιο", "SSE.Views.Toolbar.capBtnInsHeader": "Κεφαλίδα & υποσέλιδο", "SSE.Views.Toolbar.capBtnInsSlicer": "Αναλυτής", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Σύμβολο", "SSE.Views.Toolbar.capBtnMargins": "Περιθώρια", "SSE.Views.Toolbar.capBtnPageBreak": "Αλ<PERSON><PERSON><PERSON><PERSON>ς", "SSE.Views.Toolbar.capBtnPageOrient": "Προσανατολισμός", "SSE.Views.Toolbar.capBtnPageSize": "Μέγεθος", "SSE.Views.Toolbar.capBtnPrintArea": "Περιοχή εκτύπωσης", "SSE.Views.Toolbar.capBtnPrintTitles": "Εκτύπωση τίτλων", "SSE.Views.Toolbar.capBtnScale": "Κλιμάκωση για ταίριασμα", "SSE.Views.Toolbar.capImgAlign": "Στοίχιση", "SSE.Views.Toolbar.capImgBackward": "Μεταφορ<PERSON> πίσω", "SSE.Views.Toolbar.capImgForward": "Μεταφορ<PERSON> εμπρός", "SSE.Views.Toolbar.capImgGroup": "Ομάδα", "SSE.Views.Toolbar.capInsertChart": "Γράφημα", "SSE.Views.Toolbar.capInsertChartRecommend": "Προτεινόμενο γράφημα", "SSE.Views.Toolbar.capInsertEquation": "Εξίσωση", "SSE.Views.Toolbar.capInsertHyperlink": "Υπερσύνδεσμος", "SSE.Views.Toolbar.capInsertImage": "Εικόνα", "SSE.Views.Toolbar.capInsertShape": "Σχήμα", "SSE.Views.Toolbar.capInsertSpark": "Μικρογράφημα", "SSE.Views.Toolbar.capInsertTable": "Π<PERSON>ν<PERSON><PERSON><PERSON>ς", "SSE.Views.Toolbar.capInsertText": "Πλα<PERSON><PERSON><PERSON><PERSON> κειμένου", "SSE.Views.Toolbar.capInsertTextart": "Καλλιτεχνικό κείμενο", "SSE.Views.Toolbar.capShapesMerge": "Συγχώνευση σχημάτων", "SSE.Views.Toolbar.mniCapitalizeWords": "Κεφαλαία Πρώτα Γράμματα", "SSE.Views.Toolbar.mniImageFromFile": "Εικόνα από αρχείο", "SSE.Views.Toolbar.mniImageFromStorage": "Εικόνα από αποθηκευτικό χώρο", "SSE.Views.Toolbar.mniImageFromUrl": "Εικόνα από διεύθυνση URL", "SSE.Views.Toolbar.mniLowerCase": "πεζά", "SSE.Views.Toolbar.mniSentenceCase": "Πεζά-κεφα<PERSON><PERSON><PERSON><PERSON> πρότασης.", "SSE.Views.Toolbar.mniToggleCase": "εΝΑΛΛΑΓΗ πΕΖΩΝ-κΕΦΑΛΑΙΩΝ", "SSE.Views.Toolbar.mniUpperCase": "ΚΕΦΑΛΑΙΑ", "SSE.Views.Toolbar.textAddPrintArea": "Προσθήκη στην περιοχή εκτύπωσης", "SSE.Views.Toolbar.textAlignBottom": "Στοίχιση Κάτω", "SSE.Views.Toolbar.textAlignCenter": "Στοίχιση στο Κέντρο", "SSE.Views.Toolbar.textAlignJust": "Πλήρης στοίχιση", "SSE.Views.Toolbar.textAlignLeft": "Στοίχιση Αριστερά", "SSE.Views.Toolbar.textAlignMiddle": "Στοίχιση στη Μέση", "SSE.Views.Toolbar.textAlignRight": "Στοίχιση δεξιά", "SSE.Views.Toolbar.textAlignTop": "Στοίχιση Πάνω", "SSE.Views.Toolbar.textAllBorders": "Όλα τα περιγράμματα", "SSE.Views.Toolbar.textAlpha": "Ελληνικό μικρό γράμμα άλφα", "SSE.Views.Toolbar.textAuto": "Αυτόματα", "SSE.Views.Toolbar.textAutoColor": "Αυτόματα", "SSE.Views.Toolbar.textBetta": "Ελληνικό μικρό γράμμα βήτα", "SSE.Views.Toolbar.textBlackHeart": "Συλλογή <PERSON>δ<PERSON>ς", "SSE.Views.Toolbar.textBold": "Έντονα", "SSE.Views.Toolbar.textBordersColor": "Χρώμα περιγράμματος", "SSE.Views.Toolbar.textBordersStyle": "Τεχνοτροπία περιγράμματος", "SSE.Views.Toolbar.textBottom": "Κάτω μέρος: ", "SSE.Views.Toolbar.textBottomBorders": "Κάτω περιγράμματα", "SSE.Views.Toolbar.textBullet": "Κουκκίδα", "SSE.Views.Toolbar.textCellAlign": "Μορφοποίηση στοίχισης κελιών", "SSE.Views.Toolbar.textCenterBorders": "Εσωτερι<PERSON><PERSON> κατακόρυφα περιγράμματα", "SSE.Views.Toolbar.textClearPrintArea": "Εκκαθάριση περιοχής εκτύπωσης", "SSE.Views.Toolbar.textClearRule": "Εκκαθάριση κανόνων", "SSE.Views.Toolbar.textClockwise": "Γωνία δεξιόστροφα", "SSE.Views.Toolbar.textColorScales": "Κλίμακες χρωμάτων", "SSE.Views.Toolbar.textCopyright": "Σήμα πνευματικών δικαιωμάτων", "SSE.Views.Toolbar.textCounterCw": "Γωνία αριστερόστροφα", "SSE.Views.Toolbar.textCustom": "Προσαρμογή", "SSE.Views.Toolbar.textDataBars": "Μπάρες δεδομένων", "SSE.Views.Toolbar.textDegree": "Σημάδι βαθμού", "SSE.Views.Toolbar.textDelLeft": "Ολίσθηση κελιών αριστερά", "SSE.Views.Toolbar.textDelPageBreak": "Κατάργηση αλλαγής σελίδας", "SSE.Views.Toolbar.textDelta": "Ελληνικό μικρό γράμμα δέλτα", "SSE.Views.Toolbar.textDelUp": "Ολίσθηση κελιών επάνω", "SSE.Views.Toolbar.textDiagDownBorder": "Διαγ<PERSON><PERSON><PERSON><PERSON> κάτω όριο", "SSE.Views.Toolbar.textDiagUpBorder": "Διαγών<PERSON><PERSON> επάνω όριο", "SSE.Views.Toolbar.textDivision": "Σύμβολο διαίρεσης", "SSE.Views.Toolbar.textDollar": "Σύμβολο δολαρίου", "SSE.Views.Toolbar.textDone": "Ολοκληρώθηκε", "SSE.Views.Toolbar.textDown": "Κάτω", "SSE.Views.Toolbar.textEditVA": "Επεξεργασία Ορατής Περιοχής", "SSE.Views.Toolbar.textEntireCol": "Ολόκληρη στήλη", "SSE.Views.Toolbar.textEntireRow": "Ολόκληρη γραμμή", "SSE.Views.Toolbar.textEuro": "Σύμβολο του ευρώ", "SSE.Views.Toolbar.textFewPages": "σελίδες", "SSE.Views.Toolbar.textFillLeft": "Αριστερά", "SSE.Views.Toolbar.textFillRight": "Δεξιά", "SSE.Views.Toolbar.textFormatCellFill": "Μορφοποίηση γεμίσματος κελιού", "SSE.Views.Toolbar.textGreaterEqual": "Μεγαλύτερο από ή ίσο με", "SSE.Views.Toolbar.textHeight": "Ύψος", "SSE.Views.Toolbar.textHideVA": "Απόκρυψη Ορατής Περιοχής", "SSE.Views.Toolbar.textHorizontal": "Οριζόντιο κείμενο", "SSE.Views.Toolbar.textInfinity": "Άπειρο", "SSE.Views.Toolbar.textInsDown": "Ολίσθηση κελιών κάτω", "SSE.Views.Toolbar.textInsideBorders": "Εσωτερικό όριο", "SSE.Views.Toolbar.textInsPageBreak": "Εισαγωγή αλλαγής σελίδας", "SSE.Views.Toolbar.textInsRight": "Ολίσθηση κελιών δεξιά", "SSE.Views.Toolbar.textItalic": "Πλάγια", "SSE.Views.Toolbar.textItems": "Αντικείμενα", "SSE.Views.Toolbar.textLandscape": "Οριζόντιος", "SSE.Views.Toolbar.textLeft": "Αριστερά: ", "SSE.Views.Toolbar.textLeftBorders": "Αριστερά περιγράμματα", "SSE.Views.Toolbar.textLessEqual": "Μικρότερο από ή ίσο με", "SSE.Views.Toolbar.textLetterPi": "Ελληνικό μικρό γράμμα πι", "SSE.Views.Toolbar.textManageRule": "Διαχείριση κανόνων", "SSE.Views.Toolbar.textManyPages": "σελίδες", "SSE.Views.Toolbar.textMarginsLast": "Τελευτ<PERSON><PERSON><PERSON> Προσαρμοσμένο", "SSE.Views.Toolbar.textMarginsNarrow": "Στενό", "SSE.Views.Toolbar.textMarginsNormal": "Κανονική", "SSE.Views.Toolbar.textMarginsWide": "<PERSON>λα<PERSON><PERSON>", "SSE.Views.Toolbar.textMiddleBorders": "Εσωτερικά οριζόντια περιγράμματα", "SSE.Views.Toolbar.textMoreBorders": "Περισσότερα περιθώρια", "SSE.Views.Toolbar.textMoreFormats": "Περισσότερες μορφές", "SSE.Views.Toolbar.textMorePages": "Περισσότερες σελίδες", "SSE.Views.Toolbar.textMoreSymbols": "Περισσότερα σύμβολα", "SSE.Views.Toolbar.textNewColor": "Περισσότερα χρώματα", "SSE.Views.Toolbar.textNewRule": "<PERSON><PERSON><PERSON> κανόνας", "SSE.Views.Toolbar.textNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> περιγράμματα", "SSE.Views.Toolbar.textNotEqualTo": "Δεν είναι ίσο με", "SSE.Views.Toolbar.textOneHalf": "Κλάσμα μισού", "SSE.Views.Toolbar.textOnePage": "σελίδα", "SSE.Views.Toolbar.textOneQuarter": "Κλάσμα τετάρτου", "SSE.Views.Toolbar.textOutBorders": "Εξωτερικ<PERSON> περιγράμματα", "SSE.Views.Toolbar.textPageMarginsCustom": "Προσαρμοσμένα περιθώρια", "SSE.Views.Toolbar.textPlusMinus": "Σύμβολο <PERSON>ν-Πλην", "SSE.Views.Toolbar.textPortrait": "Κατακόρυφος", "SSE.Views.Toolbar.textPrint": "Εκτύπωση", "SSE.Views.Toolbar.textPrintGridlines": "Εκτύπωση γραμμών πλέγματος", "SSE.Views.Toolbar.textPrintHeadings": "Εκτύπωση επικεφαλίδων", "SSE.Views.Toolbar.textPrintOptions": "Ρυθμίσεις εκτύπωσης", "SSE.Views.Toolbar.textRegistered": "Σύμβολο καταχωρημένου", "SSE.Views.Toolbar.textResetPageBreak": "Επανα<PERSON><PERSON><PERSON><PERSON> όλων των αλλαγών σελίδας", "SSE.Views.Toolbar.textRight": "Δεξιά: ", "SSE.Views.Toolbar.textRightBorders": "Δεξιά περιγράμματα", "SSE.Views.Toolbar.textRotateDown": "Περιστροφή κειμένου κάτω", "SSE.Views.Toolbar.textRotateUp": "Περιστροφή κειμένου επάνω", "SSE.Views.Toolbar.textRtlSheet": "Φύλλο από δεξιά προς τα αριστερά", "SSE.Views.Toolbar.textScale": "Κλίμακα", "SSE.Views.Toolbar.textScaleCustom": "Προσαρμογή", "SSE.Views.Toolbar.textSection": "Σύμβολο τμήματος", "SSE.Views.Toolbar.textSelection": "Από την τρέχουσα επιλογή", "SSE.Views.Toolbar.textSeries": "Σειρά", "SSE.Views.Toolbar.textSetPrintArea": "Ορισμ<PERSON>ς εκτυπώσιμης περιοχής", "SSE.Views.Toolbar.textShapesCombine": "Συνδυασμ<PERSON>ς", "SSE.Views.Toolbar.textShapesFragment": "Τεμάχιο", "SSE.Views.Toolbar.textShapesIntersect": "Διατομή", "SSE.Views.Toolbar.textShapesSubstract": "Αφαίρεση", "SSE.Views.Toolbar.textShapesUnion": "Ένωση", "SSE.Views.Toolbar.textShowVA": "Εμφάνιση ορατής περιοχής", "SSE.Views.Toolbar.textSmile": "Λευκό χαμογελαστό πρόσωπο", "SSE.Views.Toolbar.textSquareRoot": "Τετραγωνική ρίζα", "SSE.Views.Toolbar.textStrikeout": "Διαγραφή", "SSE.Views.Toolbar.textSubscript": "Δείκτης", "SSE.Views.Toolbar.textSubSuperscript": "Δείκτης/Εκθέτης", "SSE.Views.Toolbar.textSuperscript": "Εκθέτης", "SSE.Views.Toolbar.textTabCollaboration": "Συνεργασία", "SSE.Views.Toolbar.textTabData": "Δεδομένα", "SSE.Views.Toolbar.textTabDraw": "Σχεδίαση", "SSE.Views.Toolbar.textTabFile": "Αρχείο", "SSE.Views.Toolbar.textTabFormula": "Τύπος", "SSE.Views.Toolbar.textTabHome": "Αρχική", "SSE.Views.Toolbar.textTabInsert": "Εισαγωγή", "SSE.Views.Toolbar.textTabLayout": "Διάταξη", "SSE.Views.Toolbar.textTabProtect": "Προστασία", "SSE.Views.Toolbar.textTabView": "Προβολή", "SSE.Views.Toolbar.textThisPivot": "Από αυτόν τον συγκεντρωτικό πίνακα", "SSE.Views.Toolbar.textThisSheet": "Από αυτό το φύλλο εργασίας", "SSE.Views.Toolbar.textThisTable": "Από αυτόν τον πίνακα", "SSE.Views.Toolbar.textTilde": "Περισπωμένη", "SSE.Views.Toolbar.textTop": "Πάνω: ", "SSE.Views.Toolbar.textTopBorders": "Επάνω περιγράμματα", "SSE.Views.Toolbar.textTradeMark": "Σήμα κατατεθέν", "SSE.Views.Toolbar.textUnderline": "Υπογράμμιση", "SSE.Views.Toolbar.textUp": "Επάνω", "SSE.Views.Toolbar.textVertical": "Κατακόρυ<PERSON><PERSON> κείμενο", "SSE.Views.Toolbar.textWidth": "<PERSON>λ<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textYen": "Σύμβολο <PERSON>ν", "SSE.Views.Toolbar.textZoom": "Εστίαση", "SSE.Views.Toolbar.tipAlignBottom": "Στοίχιση κάτω", "SSE.Views.Toolbar.tipAlignCenter": "Στοίχιση στο κέντρο", "SSE.Views.Toolbar.tipAlignJust": "Πλήρης στοίχιση", "SSE.Views.Toolbar.tipAlignLeft": "Στοίχιση αριστερά", "SSE.Views.Toolbar.tipAlignMiddle": "Στοίχιση στη μέση", "SSE.Views.Toolbar.tipAlignRight": "Στοίχιση δεξιά", "SSE.Views.Toolbar.tipAlignTop": "Στοίχιση πάνω", "SSE.Views.Toolbar.tipAutofilter": "Ταξινόμηση και φίλτρο", "SSE.Views.Toolbar.tipBack": "Πίσω", "SSE.Views.Toolbar.tipBorders": "Περιγράμματα", "SSE.Views.Toolbar.tipCellStyle": "Τεχνοτροπία κελιού", "SSE.Views.Toolbar.tipChangeCase": "Αλλαγ<PERSON> πεζών-κεφαλαίων", "SSE.Views.Toolbar.tipChangeChart": "Αλλαγή τύπου γραφήματος", "SSE.Views.Toolbar.tipClearStyle": "Εκκαθάριση", "SSE.Views.Toolbar.tipColorSchemas": "Αλλαγή χρωματικού σχεδίου", "SSE.Views.Toolbar.tipCondFormat": "Μορφοποίηση υπό όρους", "SSE.Views.Toolbar.tipCopy": "Αντιγραφή", "SSE.Views.Toolbar.tipCopyStyle": "Αντιγραφή τεχνοτροπίας", "SSE.Views.Toolbar.tipCut": "Αποκοπή", "SSE.Views.Toolbar.tipDecDecimal": "Μείωση δεκαδικού", "SSE.Views.Toolbar.tipDecFont": "Μείωση μεγέθους γραμματοσειράς", "SSE.Views.Toolbar.tipDeleteOpt": "Διαγρα<PERSON><PERSON> κελιών", "SSE.Views.Toolbar.tipDigStyleAccounting": "Τεχνοτροπία λογιστικής", "SSE.Views.Toolbar.tipDigStyleComma": "Στυλ κόμμα", "SSE.Views.Toolbar.tipDigStyleCurrency": "Νομισματική τεχνοτροπία", "SSE.Views.Toolbar.tipDigStylePercent": "Τεχνοτροπία ποσοστού", "SSE.Views.Toolbar.tipEditChart": "Επεξεργασία γραφήματος", "SSE.Views.Toolbar.tipEditChartData": "Επιλογή δεδομένων", "SSE.Views.Toolbar.tipEditChartType": "Αλλαγή τύπου γραφήματος", "SSE.Views.Toolbar.tipEditHeader": "Επεξεργασ<PERSON><PERSON> κεφαλίδας ή υποσέλιδου", "SSE.Views.Toolbar.tipFontColor": "Χρώμα γραμματοσειράς", "SSE.Views.Toolbar.tipFontName": "Γραμματοσειρά", "SSE.Views.Toolbar.tipFontSize": "Μέγ<PERSON><PERSON>ος γραμματοσειράς", "SSE.Views.Toolbar.tipHAlighOle": "Οριζόντια στοίχιση", "SSE.Views.Toolbar.tipImgAlign": "Στοίχιση αντικειμένων", "SSE.Views.Toolbar.tipImgGroup": "Ομαδοποίηση αντικειμένων", "SSE.Views.Toolbar.tipIncDecimal": "Αύξηση δεκαδικού", "SSE.Views.Toolbar.tipIncFont": "Αύξηση μεγέθους γραμματοσειράς", "SSE.Views.Toolbar.tipInsertChart": "Εισαγωγή γραφήματος", "SSE.Views.Toolbar.tipInsertChartRecommend": "Εισαγωγή προτεινόμενου γραφήματος", "SSE.Views.Toolbar.tipInsertChartSpark": "Εισαγωγή γραφήματος", "SSE.Views.Toolbar.tipInsertEquation": "Εισαγωγή εξίσωσης", "SSE.Views.Toolbar.tipInsertHorizontalText": "Εισαγωγή οριζόντιου πλαισίου κειμένου", "SSE.Views.Toolbar.tipInsertHyperlink": "Προσθήκη υπερσυνδέσμου", "SSE.Views.Toolbar.tipInsertImage": "Εισαγωγή εικόνας", "SSE.Views.Toolbar.tipInsertOpt": "Εισαγωγ<PERSON> κελιών", "SSE.Views.Toolbar.tipInsertShape": "Εισαγωγή σχήματος", "SSE.Views.Toolbar.tipInsertSlicer": "Εισαγωγ<PERSON> αναλυτή", "SSE.Views.Toolbar.tipInsertSmartArt": "Εισαγωγή SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Εισαγωγή μικρογραφήματος", "SSE.Views.Toolbar.tipInsertSymbol": "Εισαγωγή συμβόλου", "SSE.Views.Toolbar.tipInsertTable": "Εισαγωγή πίνακα", "SSE.Views.Toolbar.tipInsertText": "Εισαγωγή πλαισίου κειμένου", "SSE.Views.Toolbar.tipInsertTextart": "Εισαγωγή Τεχνοκειμένου", "SSE.Views.Toolbar.tipInsertVerticalText": "Εισαγω<PERSON><PERSON> κάθετου πλαισίου κειμένου", "SSE.Views.Toolbar.tipMerge": "Συγχώνευση και κεντράρισμα", "SSE.Views.Toolbar.tipNone": "Κανένα", "SSE.Views.Toolbar.tipNumFormat": "Μορφή αριθμού", "SSE.Views.Toolbar.tipPageBreak": "Προσθέστε μια αλλαγή στο σημείο όπου θέλετε να ξεκινά η επόμενη σελίδα στο εκτυπωμένο αντίγραφο", "SSE.Views.Toolbar.tipPageMargins": "Περιθώρια σελίδας", "SSE.Views.Toolbar.tipPageOrient": "Προσανα<PERSON><PERSON><PERSON>ισμ<PERSON>ς σελίδας", "SSE.Views.Toolbar.tipPageSize": "Μέγ<PERSON><PERSON>ος σελίδας", "SSE.Views.Toolbar.tipPaste": "Επικόλληση", "SSE.Views.Toolbar.tipPrColor": "Χρώμα γεμίσματος", "SSE.Views.Toolbar.tipPrint": "Εκτύπωση", "SSE.Views.Toolbar.tipPrintArea": "Περιοχή εκτύπωσης", "SSE.Views.Toolbar.tipPrintQuick": "Γρήγορη εκτύπωση", "SSE.Views.Toolbar.tipPrintTitles": "Εκτύπωση τίτλων", "SSE.Views.Toolbar.tipRedo": "Επανάληψη", "SSE.Views.Toolbar.tipReplace": "Αντικατάσταση", "SSE.Views.Toolbar.tipRtlSheet": "Αλλάξτε την κατεύθυνση του φύλλου έτσι ώστε η πρώτη στήλη να βρίσκεται στη δεξιά πλευρά", "SSE.Views.Toolbar.tipSave": "Αποθήκευση", "SSE.Views.Toolbar.tipSaveCoauth": "Αποθηκεύστε τις αλλαγές σας για να τις δουν οι άλλοι χρήστες.", "SSE.Views.Toolbar.tipScale": "Κλιμάκωση για ταίριασμα", "SSE.Views.Toolbar.tipSelectAll": "Επιλογ<PERSON> όλων ", "SSE.Views.Toolbar.tipSendBackward": "Μεταφορ<PERSON> πίσω", "SSE.Views.Toolbar.tipSendForward": "Μεταφορ<PERSON> εμπρός", "SSE.Views.Toolbar.tipShapesMerge": "Συγχώνευση σχημάτων", "SSE.Views.Toolbar.tipSynchronize": "Το έγγραφο τροποποιήθηκε από άλλο χρήστη. Κάντε κλικ για να αποθηκεύσετε τις αλλαγές σας και να επαναφορτώσετε τις ενημερώσεις.", "SSE.Views.Toolbar.tipTextFormatting": "Περισσότερα εργαλεία μορφοποίησης κειμένου ", "SSE.Views.Toolbar.tipTextOrientation": "Προσανατολισμός", "SSE.Views.Toolbar.tipUndo": "Αναίρεση", "SSE.Views.Toolbar.tipVAlighOle": "Κατακόρυφη στοίχιση", "SSE.Views.Toolbar.tipVisibleArea": "Ορατή περιοχή", "SSE.Views.Toolbar.tipWrap": "Αναδίπλωση κειμένου", "SSE.Views.Toolbar.txtAccounting": "Λογιστική", "SSE.Views.Toolbar.txtAdditional": "Επιπρόσθετα", "SSE.Views.Toolbar.txtAscending": "Αύξουσα", "SSE.Views.Toolbar.txtAutosumTip": "Άθροιση", "SSE.Views.Toolbar.txtCellStyle": "Τεχνοτροπία κελιού", "SSE.Views.Toolbar.txtClearAll": "Όλα", "SSE.Views.Toolbar.txtClearComments": "Σχόλια", "SSE.Views.Toolbar.txtClearFilter": "Εκκαθάριση φίλτρου", "SSE.Views.Toolbar.txtClearFormat": "Μορφή", "SSE.Views.Toolbar.txtClearFormula": "Συνάρτηση", "SSE.Views.Toolbar.txtClearHyper": "Υπερσύνδεσμοι", "SSE.Views.Toolbar.txtClearText": "Κείμενο", "SSE.Views.Toolbar.txtCurrency": "Νόμισμα", "SSE.Views.Toolbar.txtCustom": "Προσαρμογή", "SSE.Views.Toolbar.txtDate": "Ημερομηνία", "SSE.Views.Toolbar.txtDateLong": "Πλήρης ημερομηνία", "SSE.Views.Toolbar.txtDateShort": "Σύντομη ημερομηνία", "SSE.Views.Toolbar.txtDateTime": "Ημερομηνία & Ώρα", "SSE.Views.Toolbar.txtDescending": "Φθίνουσα", "SSE.Views.Toolbar.txtDollar": "$ Δολάριο", "SSE.Views.Toolbar.txtEuro": "€ Ευρώ", "SSE.Views.Toolbar.txtExp": "Εκθετικό", "SSE.Views.Toolbar.txtFillNum": "Γέμισμα", "SSE.Views.Toolbar.txtFilter": "Φίλτρο", "SSE.Views.Toolbar.txtFormula": "Εισαγωγ<PERSON> συνάρτησης", "SSE.Views.Toolbar.txtFraction": "Κλάσμα", "SSE.Views.Toolbar.txtFranc": "CHF Ελβετικά φράγκα", "SSE.Views.Toolbar.txtGeneral": "Γενικά", "SSE.Views.Toolbar.txtInteger": "Ακ<PERSON><PERSON><PERSON><PERSON><PERSON> αριθμός", "SSE.Views.Toolbar.txtManageRange": "Διαχειριστής ονομάτων", "SSE.Views.Toolbar.txtMergeAcross": "Συγχώνευση κατά", "SSE.Views.Toolbar.txtMergeCells": "Συγχώνευση κελιών", "SSE.Views.Toolbar.txtMergeCenter": "Συγχώνευση & Κεντράρισμα", "SSE.Views.Toolbar.txtNamedRange": "Επώνυμα εύρη ", "SSE.Views.Toolbar.txtNewRange": "Προσδιορισμ<PERSON>ς ονόματος", "SSE.Views.Toolbar.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON> περιγράμματα", "SSE.Views.Toolbar.txtNumber": "Αριθμός", "SSE.Views.Toolbar.txtPasteRange": "Επικόλληση ονόματος", "SSE.Views.Toolbar.txtPercentage": "Ποσοστό", "SSE.Views.Toolbar.txtPound": "£ Λίρα", "SSE.Views.Toolbar.txtRouble": "₽ Ρούβλι", "SSE.Views.Toolbar.txtScientific": "Επιστημονική", "SSE.Views.Toolbar.txtSearch": "Αναζήτηση", "SSE.Views.Toolbar.txtSort": "Ταξινόμηση", "SSE.Views.Toolbar.txtSortAZ": "Ταξινόμηση με αύξουσα σειρά", "SSE.Views.Toolbar.txtSortZA": "Ταξινόμηση σε φθίνουσα σειρά", "SSE.Views.Toolbar.txtSpecial": "Ειδική", "SSE.Views.Toolbar.txtTableTemplate": "Μορφοποίηση σύμφωνα με το πρότυπο πίνακα", "SSE.Views.Toolbar.txtText": "Κείμενο", "SSE.Views.Toolbar.txtTime": "Ώρα", "SSE.Views.Toolbar.txtUnmerge": "Κατάργηση συγχώνευσης κελιών", "SSE.Views.Toolbar.txtYen": "¥ Γιέν", "SSE.Views.Top10FilterDialog.textType": "Εμφάνιση", "SSE.Views.Top10FilterDialog.txtBottom": "Κάτω", "SSE.Views.Top10FilterDialog.txtBy": "από", "SSE.Views.Top10FilterDialog.txtItems": "Αντικείμενο", "SSE.Views.Top10FilterDialog.txtPercent": "Ποσοστό", "SSE.Views.Top10FilterDialog.txtSum": "Άθροισμα", "SSE.Views.Top10FilterDialog.txtTitle": "Κορυφαία 10 Αυτόματα Φίλτρα", "SSE.Views.Top10FilterDialog.txtTop": "Επάνω", "SSE.Views.Top10FilterDialog.txtValueTitle": "Κορυφαία 10 Φίλτρα", "SSE.Views.ValueFieldSettingsDialog.textNext": "(επόμενο)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "Μορφή αριθμού", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(προηγούμενο)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Ρυθμίσεις πεδίου τιμών", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Μέ<PERSON><PERSON> Όρος", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Βασικ<PERSON> πεδίο", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Βασικ<PERSON> πεδίο", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 από %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Μέτρηση", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Μέτρηση αριθμών", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Προσαρμοσμένο όνομα", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Διαφορά από", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Ευρετήριο", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Μέγιστο", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Ελάχιστο", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "<PERSON><PERSON><PERSON><PERSON><PERSON> υπολογισμό", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "% του", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "% διαφορά από", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "% της στήλης", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% του γενικού συνόλου", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% του συνόλου των μητρικών", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% συνόλου γονικής στήλης", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% του συνόλου της γονικής γραμμής", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% τρέχοντος συνόλου σε", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "& της γραμμής", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Γινόμενο", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Κατάταξη από το μικρότερο στο μεγαλύτερο", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Κατάταξη από το μεγαλύτερο στο μικρότερο", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Τρέχον σύνολο σε", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Εμφάνιση τιμών ως", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Όνομα πηγής:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "Τυπική Απόκλιση", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "Τυπική απόκλιση πληθυσμού", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Άθροισμα", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "\nΣυνοψίστε το πεδίο τιμών κατά", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Διαφορά", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "Διακύμανση πληθυσμού", "SSE.Views.ViewManagerDlg.closeButtonText": "Κλείσιμο", "SSE.Views.ViewManagerDlg.guestText": "Επισκέπτης", "SSE.Views.ViewManagerDlg.lockText": "Κλειδωμένο", "SSE.Views.ViewManagerDlg.textDelete": "Διαγραφή", "SSE.Views.ViewManagerDlg.textDuplicate": "Αντίγραφο", "SSE.Views.ViewManagerDlg.textEmpty": "Δεν δημιουργήθηκαν ακόμα όψεις.", "SSE.Views.ViewManagerDlg.textGoTo": "Μετάβαση σε προβολή", "SSE.Views.ViewManagerDlg.textLongName": "Εισάγετε ένα όνομα μικρότερο από 128 χαρακτήρες.", "SSE.Views.ViewManagerDlg.textNew": "Νέο", "SSE.Views.ViewManagerDlg.textRename": "Μετονομασία", "SSE.Views.ViewManagerDlg.textRenameError": "Το όνομα όψης δεν μπορεί να είναι κενό.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Μετονομασία όψης", "SSE.Views.ViewManagerDlg.textViews": "Όψεις φύλλων", "SSE.Views.ViewManagerDlg.tipIsLocked": "Αυτό το στοιχείο τελεί υπό επεξεργασία από άλλο χρήστη.", "SSE.Views.ViewManagerDlg.txtTitle": "Διαχ<PERSON><PERSON><PERSON>ιστής όψης φύλλου", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Είστε βέβαιοι ότι θέλετε να διαγράψετε αυτήν την προβολή φύλλου;", "SSE.Views.ViewManagerDlg.warnDeleteView": "Προσπαθείτε να διαγράψετε την τρέχουσα επιλεγμένη όψη '%1'.<br>Κλ<PERSON><PERSON><PERSON><PERSON>μο αυτής της όψης και διαγραφή της;", "SSE.Views.ViewTab.capBtnFreeze": "Σταθεροποίηση πλαισίων", "SSE.Views.ViewTab.capBtnSheetView": "Όψη φύλλου", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Καρφίτσωμα κορδέλας", "SSE.Views.ViewTab.textClose": "Κλείσιμο", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Ενιαία γρ. φύλλων και κατάστασης", "SSE.Views.ViewTab.textCreate": "Νέο", "SSE.Views.ViewTab.textDefault": "Προεπιλογή", "SSE.Views.ViewTab.textFill": "Γέμισμα", "SSE.Views.ViewTab.textFormula": "Γραμμή τύπων", "SSE.Views.ViewTab.textFreezeCol": "Σταθεροποίηση πρώτης στήλης", "SSE.Views.ViewTab.textFreezeRow": "Σταθεροποίηση επάνω γραμμής", "SSE.Views.ViewTab.textGridlines": "Γραμ<PERSON><PERSON>ς πλέγματος", "SSE.Views.ViewTab.textHeadings": "Επικεφαλίδες", "SSE.Views.ViewTab.textInterfaceTheme": "Θέμα διεπαφής", "SSE.Views.ViewTab.textLeftMenu": "Αριστερό πλαίσιο", "SSE.Views.ViewTab.textLine": "Γραμμή", "SSE.Views.ViewTab.textMacros": "Μακροεντολές", "SSE.Views.ViewTab.textManager": "Διαχειριστής προβολής", "SSE.Views.ViewTab.textRightMenu": "Δεξιό πλαίσιο", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Εμφάνιση σκιάς σταθεροποιημένων πλαισίων", "SSE.Views.ViewTab.textTabStyle": "Στυλ καρτέλας", "SSE.Views.ViewTab.textUnFreeze": "Απελευθέρωση παραθύρων", "SSE.Views.ViewTab.textZeros": "Εμφάνιση μηδενικών", "SSE.Views.ViewTab.textZoom": "Εστίαση", "SSE.Views.ViewTab.tipClose": "Κλείσιμο προβολής φύλλου εργασίας", "SSE.Views.ViewTab.tipCreate": "Δημιουρ<PERSON><PERSON><PERSON> όψης φύλλου", "SSE.Views.ViewTab.tipFreeze": "Σταθεροποίηση πλαισίων", "SSE.Views.ViewTab.tipInterfaceTheme": "Θέμα διεπαφής", "SSE.Views.ViewTab.tipMacros": "Μακροεντολές", "SSE.Views.ViewTab.tipSheetView": "Όψη φύλλου", "SSE.Views.ViewTab.tipViewNormal": "Προβολή του εγγράφου σας σε κανονική προβολή", "SSE.Views.ViewTab.tipViewPageBreak": "Δείτε πού θα εμφανίζονται οι αλλαγές σελίδας όταν εκτυπωθεί το έγγραφό σας", "SSE.Views.ViewTab.txtViewNormal": "Κανονικό", "SSE.Views.ViewTab.txtViewPageBreak": "Προεπισκόπηση αλλαγής σελίδας", "SSE.Views.WatchDialog.closeButtonText": "Κλείσιμο", "SSE.Views.WatchDialog.textAdd": "Προσθήκη παρακολούθησης", "SSE.Views.WatchDialog.textBook": "Βιβλίο", "SSE.Views.WatchDialog.textCell": "Κελί", "SSE.Views.WatchDialog.textDelete": "Διαγρα<PERSON><PERSON> παρακολούθησης", "SSE.Views.WatchDialog.textDeleteAll": "Διαγρα<PERSON><PERSON> όλων", "SSE.Views.WatchDialog.textFormula": "Τύπος", "SSE.Views.WatchDialog.textName": "Όνομα", "SSE.Views.WatchDialog.textSheet": "Φύλλο", "SSE.Views.WatchDialog.textValue": "Τιμή", "SSE.Views.WatchDialog.txtTitle": "Παράθυρο παρακολούθησης", "SSE.Views.WBProtection.hintAllowRanges": "Επιτρέπεται η επεξεργασία ευρών", "SSE.Views.WBProtection.hintProtectRange": "Εύρ<PERSON> προστασίας", "SSE.Views.WBProtection.hintProtectSheet": "Προστα<PERSON><PERSON><PERSON> φύλλου", "SSE.Views.WBProtection.hintProtectWB": "Προστασία βιβλίου εργασίας", "SSE.Views.WBProtection.txtAllowRanges": "Επιτρέπεται η επεξεργασία ευρών", "SSE.Views.WBProtection.txtHiddenFormula": "Κρυφοί τύποι", "SSE.Views.WBProtection.txtLockedCell": "Κλειδωμένο κελί", "SSE.Views.WBProtection.txtLockedShape": "Κλειδωμένο σχήμα ", "SSE.Views.WBProtection.txtLockedText": "Κλείδωμα κειμένου", "SSE.Views.WBProtection.txtProtectRange": "Εύρ<PERSON> προστασίας", "SSE.Views.WBProtection.txtProtectSheet": "Προστα<PERSON><PERSON><PERSON> φύλλου", "SSE.Views.WBProtection.txtProtectWB": "Προστασία βιβλίου εργασίας", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Εισάγετε συνθηματικό για άρση προστασίας φύλλου", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Άρση προστασίας φύλλου", "SSE.Views.WBProtection.txtWBUnlockDescription": "Εισάγετε συνθηματικό για άρση προστασίας βιβλίου εργασίας", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}