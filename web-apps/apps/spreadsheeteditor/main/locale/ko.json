{"cancelButtonText": "취소", "Common.Controllers.Chat.notcriticalErrorTitle": "경고", "Common.Controllers.Desktop.hintBtnHome": "메인 창 표시", "Common.Controllers.Desktop.itemCreateFromTemplate": "템플릿에서 만들기", "Common.Controllers.History.notcriticalErrorTitle": "경고", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "영역", "Common.define.chartData.textAreaStacked": "누적 영역형", "Common.define.chartData.textAreaStackedPer": "100% 누적 영역형", "Common.define.chartData.textBar": "막대", "Common.define.chartData.textBarNormal": "묶은 세로 막대형", "Common.define.chartData.textBarNormal3d": "3차원 묶은 세로 막대", "Common.define.chartData.textBarNormal3dPerspective": "3차원 세로 막대", "Common.define.chartData.textBarStacked": "누적 세로 막대형", "Common.define.chartData.textBarStacked3d": "3차원 누적 세로 막대형", "Common.define.chartData.textBarStackedPer": "100% 누적 세로 막대형", "Common.define.chartData.textBarStackedPer3d": "3차원 100 % 누적 세로 막 대형", "Common.define.chartData.textCharts": "차트", "Common.define.chartData.textColumn": "열", "Common.define.chartData.textColumnSpark": "열", "Common.define.chartData.textCombo": "콤보", "Common.define.chartData.textComboAreaBar": "누적 영역형 - 묶은 세로 막대형", "Common.define.chartData.textComboBarLine": "묶은 세로 막대형 - 꺾은선형", "Common.define.chartData.textComboBarLineSecondary": "묶은 세로 막대형 - 꺾은선형,보조 축", "Common.define.chartData.textComboCustom": "맞춤 조합", "Common.define.chartData.textDoughnut": "도넛", "Common.define.chartData.textHBarNormal": "묶은 가로 막대형", "Common.define.chartData.textHBarNormal3d": "3차원 집합 막대", "Common.define.chartData.textHBarStacked": "누적 가로 막대형", "Common.define.chartData.textHBarStacked3d": "3차원 누적 가로 막대형", "Common.define.chartData.textHBarStackedPer": "100％ 누적 막대형", "Common.define.chartData.textHBarStackedPer3d": "3차원 100 % 기준 누적 가로 막 대형", "Common.define.chartData.textLine": "선", "Common.define.chartData.textLine3d": "3차원 꺾은 선형", "Common.define.chartData.textLineMarker": "마커 라인", "Common.define.chartData.textLineSpark": "선", "Common.define.chartData.textLineStacked": "누적 꺾은 선형", "Common.define.chartData.textLineStackedMarker": "표식이 있는 누적 꺾은 선형", "Common.define.chartData.textLineStackedPer": "100 % 기준 누적 꺾은 선형", "Common.define.chartData.textLineStackedPerMarker": "표식이 있는 100 % 기준 누적 꺾은 선형", "Common.define.chartData.textPie": "부분 원형", "Common.define.chartData.textPie3d": "3차원 원형", "Common.define.chartData.textPoint": "XY (분산형)", "Common.define.chartData.textRadar": "레이더", "Common.define.chartData.textRadarFilled": "채워진 레이더", "Common.define.chartData.textRadarMarker": "마커가 있는 레이더", "Common.define.chartData.textScatter": "분산형", "Common.define.chartData.textScatterLine": "직선이 있는 분산형", "Common.define.chartData.textScatterLineMarker": "직선 및 표식이 있는 분산형", "Common.define.chartData.textScatterSmooth": "곡선이 있는 분산형", "Common.define.chartData.textScatterSmoothMarker": "곡선 및 표식이 있는 분산형", "Common.define.chartData.textSparks": "스파크라인", "Common.define.chartData.textStock": "주식형", "Common.define.chartData.textSurface": "표면", "Common.define.chartData.textWinLossSpark": "승리/패배", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "형식 없음", "Common.define.conditionalData.text1Above": "1 이상의 표준편차", "Common.define.conditionalData.text1Below": "표준편차 1이하", "Common.define.conditionalData.text2Above": "표준편차 2이상", "Common.define.conditionalData.text2Below": "표준편차 2이하", "Common.define.conditionalData.text3Above": "표준편차 3이상", "Common.define.conditionalData.text3Below": "표준편차 3이하", "Common.define.conditionalData.textAbove": "이상", "Common.define.conditionalData.textAverage": "평균", "Common.define.conditionalData.textBegins": "시작", "Common.define.conditionalData.textBelow": "이하", "Common.define.conditionalData.textBetween": "해당 범위", "Common.define.conditionalData.textBlank": "공백", "Common.define.conditionalData.textBlanks": "공백 포함", "Common.define.conditionalData.textBottom": "하단", "Common.define.conditionalData.textContains": "포함", "Common.define.conditionalData.textDataBar": "데이터 막대", "Common.define.conditionalData.textDate": "날짜", "Common.define.conditionalData.textDuplicate": "중복", "Common.define.conditionalData.textEnds": "종료", "Common.define.conditionalData.textEqAbove": "다음의 값과 동일한 또는 이상", "Common.define.conditionalData.textEqBelow": "다음의 값과 동일한 이하", "Common.define.conditionalData.textEqual": "동일한", "Common.define.conditionalData.textError": "오류", "Common.define.conditionalData.textErrors": "오류 포함", "Common.define.conditionalData.textFormula": "수식", "Common.define.conditionalData.textGreater": "보다 큼", "Common.define.conditionalData.textGreaterEq": "크거나 같음", "Common.define.conditionalData.textIconSets": "아이콘 셋", "Common.define.conditionalData.textLast7days": "지난 7일 동안", "Common.define.conditionalData.textLastMonth": "지난 달", "Common.define.conditionalData.textLastWeek": "지난 주", "Common.define.conditionalData.textLess": "보다 작음", "Common.define.conditionalData.textLessEq": "작거나 같음", "Common.define.conditionalData.textNextMonth": "다음 달", "Common.define.conditionalData.textNextWeek": "다음 주", "Common.define.conditionalData.textNotBetween": "제외 범위", "Common.define.conditionalData.textNotBlanks": "공백을 포함하지 않음", "Common.define.conditionalData.textNotContains": "포함하지 않음", "Common.define.conditionalData.textNotEqual": "같지 않음", "Common.define.conditionalData.textNotErrors": "오류를 포함하지 않음", "Common.define.conditionalData.textText": "텍스트", "Common.define.conditionalData.textThisMonth": "이번 달", "Common.define.conditionalData.textThisWeek": "이번 주", "Common.define.conditionalData.textToday": "오늘", "Common.define.conditionalData.textTomorrow": "내일", "Common.define.conditionalData.textTop": "위", "Common.define.conditionalData.textUnique": "고유값", "Common.define.conditionalData.textValue": "값이", "Common.define.conditionalData.textYesterday": "어제", "Common.define.smartArt.textAccentedPicture": "강조 이미지", "Common.define.smartArt.textAccentProcess": "강조 프로세스", "Common.define.smartArt.textAlternatingFlow": "번갈아 가는 흐름", "Common.define.smartArt.textAlternatingHexagons": "번갈아 가는 육각형", "Common.define.smartArt.textAlternatingPictureBlocks": "번갈아 가며 그림 블록 만들기", "Common.define.smartArt.textAlternatingPictureCircles": "번갈아 가는 그림 원", "Common.define.smartArt.textArchitectureLayout": "아키텍처 레이아웃", "Common.define.smartArt.textArrowRibbon": "화살표 리본", "Common.define.smartArt.textAscendingPictureAccentProcess": "오름차순 그림 강조 프로세스", "Common.define.smartArt.textBalance": "균형", "Common.define.smartArt.textBasicBendingProcess": "기본 절곡 프로세스", "Common.define.smartArt.textBasicBlockList": "기본 차단 리스트", "Common.define.smartArt.textBasicChevronProcess": "기본 쉐브론 프로세스", "Common.define.smartArt.textBasicCycle": "기본 주기", "Common.define.smartArt.textBasicMatrix": "기본 행렬", "Common.define.smartArt.textBasicPie": "기본 파이", "Common.define.smartArt.textBasicProcess": "기본 프로세스", "Common.define.smartArt.textBasicPyramid": "기본 피라미드", "Common.define.smartArt.textBasicRadial": "기본 원형", "Common.define.smartArt.textBasicTarget": "기본 대상", "Common.define.smartArt.textBasicTimeline": "기본 타임라인", "Common.define.smartArt.textBasicVenn": "기본 벤 다이어그램", "Common.define.smartArt.textBendingPictureAccentList": "휜 이미지 강조 목록", "Common.define.smartArt.textBendingPictureBlocks": "휜 이미지 블록", "Common.define.smartArt.textBendingPictureCaption": "휜 이미지 캡션", "Common.define.smartArt.textBendingPictureCaptionList": "휜 이미지 캡션 목록", "Common.define.smartArt.textBendingPictureSemiTranparentText": "휜 이미지 반투명 텍스트", "Common.define.smartArt.textBlockCycle": "블록 주기", "Common.define.smartArt.textBubblePictureList": "거품 이미지 목록", "Common.define.smartArt.textCaptionedPictures": "캡션이 있는 사진", "Common.define.smartArt.textChevronAccentProcess": "쉐브론 액센트 프로세스", "Common.define.smartArt.textChevronList": "쉐브론 목록", "Common.define.smartArt.textCircleAccentTimeline": "원형 강조 타임라인", "Common.define.smartArt.textCircleArrowProcess": "원형 화살표 프로세스", "Common.define.smartArt.textCirclePictureHierarchy": "원형 이미지 계층 구조", "Common.define.smartArt.textCircleProcess": "원형 프로세스", "Common.define.smartArt.textCircleRelationship": "원형 관계", "Common.define.smartArt.textCircularBendingProcess": "원형 절곡 공정", "Common.define.smartArt.textCircularPictureCallout": "원형 이미지 주석", "Common.define.smartArt.textClosedChevronProcess": "닫힌 형태의 쉐브론 프로세스", "Common.define.smartArt.textContinuousArrowProcess": "연속된 화살표 프로세스", "Common.define.smartArt.textContinuousBlockProcess": "연속된 블록 프로세스", "Common.define.smartArt.textContinuousCycle": "연속적인 주기", "Common.define.smartArt.textContinuousPictureList": "연속된 그림 목록", "Common.define.smartArt.textConvergingArrows": "수렴 화살표", "Common.define.smartArt.textConvergingRadial": "한 지점으로 모이는 방사형", "Common.define.smartArt.textConvergingText": "한 지점으로 모이는 텍스트", "Common.define.smartArt.textCounterbalanceArrows": "평형 화살", "Common.define.smartArt.textCycle": "주기", "Common.define.smartArt.textCycleMatrix": "주기 행렬", "Common.define.smartArt.textDescendingBlockList": "내림차순으로 정렬한 목록", "Common.define.smartArt.textDescendingProcess": "내림차순 프로세스", "Common.define.smartArt.textDetailedProcess": "상세한 프로세스", "Common.define.smartArt.textDivergingArrows": "분기 화살표", "Common.define.smartArt.textDivergingRadial": "분기하는 방사형", "Common.define.smartArt.textEquation": "방정식", "Common.define.smartArt.textFramedTextPicture": "테두리가 있는 텍스트 이미지", "Common.define.smartArt.textFunnel": "깔때기", "Common.define.smartArt.textGear": "대비", "Common.define.smartArt.textGridMatrix": "격자 행렬", "Common.define.smartArt.textGroupedList": "그룹화 된 목록", "Common.define.smartArt.textHalfCircleOrganizationChart": "반원 형태 조직도", "Common.define.smartArt.textHexagonCluster": "육각형 클러스터", "Common.define.smartArt.textHexagonRadial": "육각형 방사형", "Common.define.smartArt.textHierarchy": "계층", "Common.define.smartArt.textHierarchyList": "계층 목록", "Common.define.smartArt.textHorizontalBulletList": "가로 방향 불릿 목록", "Common.define.smartArt.textHorizontalHierarchy": "수평적 계층", "Common.define.smartArt.textHorizontalLabeledHierarchy": "가로로 라벨링된 계층 구조", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "가로로 다중 수준 계층", "Common.define.smartArt.textHorizontalOrganizationChart": "가로 방향 조직도", "Common.define.smartArt.textHorizontalPictureList": "가로로 나열된 그림 목록", "Common.define.smartArt.textIncreasingArrowProcess": "증가 화살표 프로세스", "Common.define.smartArt.textIncreasingCircleProcess": "증가하는 원 프로세스", "Common.define.smartArt.textInterconnectedBlockProcess": "상호 연결된 블록 프로세스", "Common.define.smartArt.textInterconnectedRings": "상호 연결된 링", "Common.define.smartArt.textInvertedPyramid": "역 피라미드", "Common.define.smartArt.textLabeledHierarchy": "레이블이 있는 계층 구조", "Common.define.smartArt.textLinearVenn": "선형 벤 다이어그램", "Common.define.smartArt.textLinedList": "선으로 구분된 목록", "Common.define.smartArt.textList": "목록", "Common.define.smartArt.textMatrix": "행렬", "Common.define.smartArt.textMultidirectionalCycle": "다방향 사이클", "Common.define.smartArt.textNameAndTitleOrganizationChart": "이름 및 직위 조직도", "Common.define.smartArt.textNestedTarget": "중첩 대상", "Common.define.smartArt.textNondirectionalCycle": "비방향 사이클", "Common.define.smartArt.textOpposingArrows": "반대 화살표", "Common.define.smartArt.textOpposingIdeas": "상반된 개념", "Common.define.smartArt.textOrganizationChart": "조직도", "Common.define.smartArt.textOther": "기타", "Common.define.smartArt.textPhasedProcess": "단계별 프로세스", "Common.define.smartArt.textPicture": "그림", "Common.define.smartArt.textPictureAccentBlocks": "그림 강조 블럭", "Common.define.smartArt.textPictureAccentList": "그림 강조 목록", "Common.define.smartArt.textPictureAccentProcess": "그림 강조 프로세스", "Common.define.smartArt.textPictureCaptionList": "그림 캡션 목록", "Common.define.smartArt.textPictureFrame": "사진 프레임", "Common.define.smartArt.textPictureGrid": "그림 격자", "Common.define.smartArt.textPictureLineup": "사진 라인업", "Common.define.smartArt.textPictureOrganizationChart": "그림 조직도", "Common.define.smartArt.textPictureStrips": "그림 스트립", "Common.define.smartArt.textPieProcess": "파이 프로세스", "Common.define.smartArt.textPlusAndMinus": "플러스와 마이너스", "Common.define.smartArt.textProcess": "프로세스", "Common.define.smartArt.textProcessArrows": "프로세스 화살표", "Common.define.smartArt.textProcessList": "프로세스 목록", "Common.define.smartArt.textPyramid": "피라미드", "Common.define.smartArt.textPyramidList": "피라미드 목록", "Common.define.smartArt.textRadialCluster": "방사형 클러스터", "Common.define.smartArt.textRadialCycle": "방사형주기", "Common.define.smartArt.textRadialList": "방사형 목록", "Common.define.smartArt.textRadialPictureList": "방사형 그림 목록", "Common.define.smartArt.textRadialVenn": "원형 벤 다이어그램", "Common.define.smartArt.textRandomToResultProcess": "무작위 랜덤 프로세스", "Common.define.smartArt.textRelationship": "관계", "Common.define.smartArt.textRepeatingBendingProcess": "반복되는 접힘 과정", "Common.define.smartArt.textReverseList": "역방향 목록", "Common.define.smartArt.textSegmentedCycle": "분할된 주기", "Common.define.smartArt.textSegmentedProcess": "세분화된 프로세스", "Common.define.smartArt.textSegmentedPyramid": "분할된 피라미드", "Common.define.smartArt.textSnapshotPictureList": "스냅샷 사진 목록", "Common.define.smartArt.textSpiralPicture": "나선형 그림", "Common.define.smartArt.textSquareAccentList": "사각형 강조 목록", "Common.define.smartArt.textStackedList": "스택 리스트", "Common.define.smartArt.textStackedVenn": "쌓인 벤 다이어그램", "Common.define.smartArt.textStaggeredProcess": "단계별 프로세스", "Common.define.smartArt.textStepDownProcess": "단계적 프로세스", "Common.define.smartArt.textStepUpProcess": "단계별 프로세스", "Common.define.smartArt.textSubStepProcess": "하위 단계 프로세스", "Common.define.smartArt.textTabbedArc": "원호형 탭", "Common.define.smartArt.textTableHierarchy": "테이블 계층", "Common.define.smartArt.textTableList": "테이블 목록", "Common.define.smartArt.textTabList": "탭 목록", "Common.define.smartArt.textTargetList": "대상 목록", "Common.define.smartArt.textTextCycle": "텍스트 사이클", "Common.define.smartArt.textThemePictureAccent": "테마 이미지 강조", "Common.define.smartArt.textThemePictureAlternatingAccent": "테마 이미지 교체 강조", "Common.define.smartArt.textThemePictureGrid": "테마 이미지 격자", "Common.define.smartArt.textTitledMatrix": "제목 행렬", "Common.define.smartArt.textTitledPictureAccentList": "제목이 있는 이미지 강조 목록", "Common.define.smartArt.textTitledPictureBlocks": "제목이 있는 그림 블록", "Common.define.smartArt.textTitlePictureLineup": "타이틀 이미지 라인업", "Common.define.smartArt.textTrapezoidList": "사다리꼴 목록", "Common.define.smartArt.textUpwardArrow": "위쪽 화살표", "Common.define.smartArt.textVaryingWidthList": "너비가 다른 목록", "Common.define.smartArt.textVerticalAccentList": "수직 강조 목록", "Common.define.smartArt.textVerticalArrowList": "수직 화살표 목록", "Common.define.smartArt.textVerticalBendingProcess": "수직 절곡 프로세스", "Common.define.smartArt.textVerticalBlockList": "수직 블록 목록", "Common.define.smartArt.textVerticalBoxList": "수직 상자 목록", "Common.define.smartArt.textVerticalBracketList": "수직 괄호 목록", "Common.define.smartArt.textVerticalBulletList": "수직 글머리 기호 목록", "Common.define.smartArt.textVerticalChevronList": "수직 쉐브론 목록", "Common.define.smartArt.textVerticalCircleList": "수직 원 목록", "Common.define.smartArt.textVerticalCurvedList": "수직 곡선 목록", "Common.define.smartArt.textVerticalEquation": "수직 방정식", "Common.define.smartArt.textVerticalPictureAccentList": "수직 방향 그림 강조 목록", "Common.define.smartArt.textVerticalPictureList": "수직 이미지 목록", "Common.define.smartArt.textVerticalProcess": "수직 프로세스", "Common.Translation.textMoreButton": "더", "Common.Translation.tipFileLocked": "문서가 편집 잠금 상태입니다.변경한 후 로컬 복사본으로 저장할 수 있습니다.", "Common.Translation.tipFileReadOnly": "파일이 읽기 전용입니다. 변경 사항을 유지하려면 파일을 새 이름으로 저장하거나 다른 위치에 저장하세요.", "Common.Translation.warnFileLocked": "파일이 다른 응용 프로그램에서 편집 중입니다. 편집을 계속하고 사본으로 저장할 수 있습니다.", "Common.Translation.warnFileLockedBtnEdit": "복사본 만들기", "Common.Translation.warnFileLockedBtnView": "미리보기", "Common.UI.ButtonColored.textAutoColor": "자동", "Common.UI.ButtonColored.textEyedropper": "스포이드", "Common.UI.ButtonColored.textNewColor": "새로운 사용자 정의 색 추가", "Common.UI.ComboBorderSize.txtNoBorders": "테두리 없음", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "테두리 없음", "Common.UI.ComboDataView.emptyComboText": "스타일 없음", "Common.UI.ExtendedColorDialog.addButtonText": "Add", "Common.UI.ExtendedColorDialog.textCurrent": "현재", "Common.UI.ExtendedColorDialog.textHexErr": "입력 한 값이 잘못되었습니다. <br> 000000에서 FFFFFF 사이의 값을 입력하십시오.", "Common.UI.ExtendedColorDialog.textNew": "New", "Common.UI.ExtendedColorDialog.textRGBErr": "입력 한 값이 잘못되었습니다. <br> 0에서 255 사이의 숫자 값을 입력하십시오.", "Common.UI.HSBColorPicker.textNoColor": "색상 없음", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "비밀번호 숨기기", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "비밀번호 표시", "Common.UI.SearchBar.textFind": "찾기", "Common.UI.SearchBar.tipCloseSearch": "검색 닫기", "Common.UI.SearchBar.tipNextResult": "다음결과", "Common.UI.SearchBar.tipOpenAdvancedSettings": "고급 설정 열기", "Common.UI.SearchBar.tipPreviousResult": "이전 결과", "Common.UI.SearchDialog.textHighlight": "결과 강조 표시", "Common.UI.SearchDialog.textMatchCase": "대소 문자를 구분합니다", "Common.UI.SearchDialog.textReplaceDef": "대체 텍스트 입력", "Common.UI.SearchDialog.textSearchStart": "여기에 텍스트를 입력하십시오", "Common.UI.SearchDialog.textTitle": "찾기 및 바꾸기", "Common.UI.SearchDialog.textTitle2": "찾기", "Common.UI.SearchDialog.textWholeWords": "전체 단어 만", "Common.UI.SearchDialog.txtBtnHideReplace": "바꾸기 숨기기", "Common.UI.SearchDialog.txtBtnReplace": "바꾸기", "Common.UI.SearchDialog.txtBtnReplaceAll": "모두 바꾸기", "Common.UI.SynchronizeTip.textDontShow": "이 메시지를 다시 표시하지 않음", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "다른 사용자가 문서를 변경했습니다. <br> 클릭하여 변경 사항을 저장하고 업데이트를 다시로드하십시오.", "Common.UI.ThemeColorPalette.textRecentColors": "최근 색상", "Common.UI.ThemeColorPalette.textStandartColors": "표준 색상", "Common.UI.ThemeColorPalette.textThemeColors": "테마 색", "Common.UI.Themes.txtThemeClassicLight": "전통적인 밝은 색상", "Common.UI.Themes.txtThemeContrastDark": "어두운 대비", "Common.UI.Themes.txtThemeDark": "어두운", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "밝은", "Common.UI.Themes.txtThemeSystem": "시스템과 동일", "Common.UI.Window.cancelButtonText": "취소", "Common.UI.Window.closeButtonText": "닫기", "Common.UI.Window.noButtonText": "No", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "확인", "Common.UI.Window.textDontShow": "이 메시지를 다시 표시하지 않음", "Common.UI.Window.textError": "오류", "Common.UI.Window.textInformation": "정보", "Common.UI.Window.textWarning": "경고", "Common.UI.Window.yesButtonText": "예", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt 키", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl 키", "Common.Utils.String.textShift": "Shift 키", "Common.Utils.ThemeColor.txtaccent": "강조", "Common.Utils.ThemeColor.txtAqua": "아쿠아", "Common.Utils.ThemeColor.txtbackground": "배경", "Common.Utils.ThemeColor.txtBlack": "검정", "Common.Utils.ThemeColor.txtBlue": "파랑", "Common.Utils.ThemeColor.txtBrightGreen": "밝은 녹색", "Common.Utils.ThemeColor.txtBrown": "갈색", "Common.Utils.ThemeColor.txtDarkBlue": "어두운 파랑색", "Common.Utils.ThemeColor.txtDarker": "더 어둡게", "Common.Utils.ThemeColor.txtDarkGray": "어두운 회색", "Common.Utils.ThemeColor.txtDarkGreen": "어두운 초록색", "Common.Utils.ThemeColor.txtDarkPurple": "진한 보라색", "Common.Utils.ThemeColor.txtDarkRed": "어두운 빨간색", "Common.Utils.ThemeColor.txtDarkTeal": "어두운 암청색", "Common.Utils.ThemeColor.txtDarkYellow": "어두운 노란색", "Common.Utils.ThemeColor.txtGold": "금색", "Common.Utils.ThemeColor.txtGray": "회색", "Common.Utils.ThemeColor.txtGreen": "녹색", "Common.Utils.ThemeColor.txtIndigo": "남색", "Common.Utils.ThemeColor.txtLavender": "라벤더", "Common.Utils.ThemeColor.txtLightBlue": "밝은 파랑", "Common.Utils.ThemeColor.txtLighter": "더 밝은", "Common.Utils.ThemeColor.txtLightGray": "밝은 회색", "Common.Utils.ThemeColor.txtLightGreen": "밝은 초록", "Common.Utils.ThemeColor.txtLightOrange": "밝은 주황", "Common.Utils.ThemeColor.txtLightYellow": "밝은 노랑", "Common.Utils.ThemeColor.txtOrange": "주황", "Common.Utils.ThemeColor.txtPink": "분홍", "Common.Utils.ThemeColor.txtPurple": "보라", "Common.Utils.ThemeColor.txtRed": "빨강", "Common.Utils.ThemeColor.txtRose": "장미", "Common.Utils.ThemeColor.txtSkyBlue": "하늘색", "Common.Utils.ThemeColor.txtTeal": "암청색", "Common.Utils.ThemeColor.txttext": "본문", "Common.Utils.ThemeColor.txtTurquosie": "터키옥색", "Common.Utils.ThemeColor.txtViolet": "바이올렛", "Common.Utils.ThemeColor.txtWhite": "흰색", "Common.Utils.ThemeColor.txtYellow": "노랑", "Common.Views.About.txtAddress": "주소 :", "Common.Views.About.txtLicensee": "LICENSEE", "Common.Views.About.txtLicensor": "LICENSOR", "Common.Views.About.txtMail": "email:", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "tel .:", "Common.Views.About.txtVersion": "버전", "Common.Views.AutoCorrectDialog.textAdd": "추가", "Common.Views.AutoCorrectDialog.textApplyAsWork": "작업하는 동안 적용", "Common.Views.AutoCorrectDialog.textAutoCorrect": "자동 고침", "Common.Views.AutoCorrectDialog.textAutoFormat": "입력 할 때 자동 서식", "Common.Views.AutoCorrectDialog.textBy": "~로", "Common.Views.AutoCorrectDialog.textDelete": "삭제", "Common.Views.AutoCorrectDialog.textHyperlink": "인터넷과 네트워크 경로를 하이퍼 링크로 설정", "Common.Views.AutoCorrectDialog.textMathCorrect": "수식 자동 고침", "Common.Views.AutoCorrectDialog.textNewRowCol": "테이블에 새 행과 열을 포함", "Common.Views.AutoCorrectDialog.textRecognized": "인식된 함수", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "다음 표현식은 인식 된 수식입니다. 자동으로 이탤릭체로 될 수는 없습니다.", "Common.Views.AutoCorrectDialog.textReplace": "바꾸기", "Common.Views.AutoCorrectDialog.textReplaceText": "입력시 바꿈", "Common.Views.AutoCorrectDialog.textReplaceType": "입력시 텍스트 바꿈", "Common.Views.AutoCorrectDialog.textReset": "재설정", "Common.Views.AutoCorrectDialog.textResetAll": "기본값으로 재설정", "Common.Views.AutoCorrectDialog.textRestore": "복구", "Common.Views.AutoCorrectDialog.textTitle": "자동 고침", "Common.Views.AutoCorrectDialog.textWarnAddRec": "인식되는 함수는 대소 A ~ Z까지의 문자만을 포함해야합니다.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "추가한 모든 표현식이 삭제되고 삭제된 표현식이 복원됩니다. 계속하시겠습니까?", "Common.Views.AutoCorrectDialog.warnReplace": "%1에 대한 자동 고침 항목이 이미 있습니다. 교체하시겠습니까?", "Common.Views.AutoCorrectDialog.warnReset": "추가한 모든 자동 고침이 삭제되고 변경된 자동 수정이 원래 값으로 복원됩니다. 계속하시겠습니까?", "Common.Views.AutoCorrectDialog.warnRestore": "%1의 자동 고침 항목이 원래 값으로 재설정됩니다. 계속하시겠습니까?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "보내기", "Common.Views.Comments.mniAuthorAsc": "작성자 A > Z", "Common.Views.Comments.mniAuthorDesc": "작성자 Z > A", "Common.Views.Comments.mniDateAsc": "가장 오래된", "Common.Views.Comments.mniDateDesc": "최신", "Common.Views.Comments.mniFilterGroups": "그룹별 필터링", "Common.Views.Comments.mniPositionAsc": "위에서 부터", "Common.Views.Comments.mniPositionDesc": "아래로 부터", "Common.Views.Comments.textAdd": "추가", "Common.Views.Comments.textAddComment": "코멘트 추가", "Common.Views.Comments.textAddCommentToDoc": "문서에 설명 추가", "Common.Views.Comments.textAddReply": "답장 추가", "Common.Views.Comments.textAll": "모두", "Common.Views.Comments.textAnonym": "손님", "Common.Views.Comments.textCancel": "취소", "Common.Views.Comments.textClose": "닫기", "Common.Views.Comments.textClosePanel": "코멘트 닫기", "Common.Views.Comments.textComment": "코멘트", "Common.Views.Comments.textComments": "코멘트", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "여기에 의견을 입력하십시오", "Common.Views.Comments.textHintAddComment": "코멘트 추가", "Common.Views.Comments.textOpenAgain": "다시 열기", "Common.Views.Comments.textReply": "Reply", "Common.Views.Comments.textResolve": "해결", "Common.Views.Comments.textResolved": "해결됨", "Common.Views.Comments.textSort": "코멘트 분류", "Common.Views.Comments.textSortFilter": "코멘트", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "코멘트를 다시 열 수 있는 권한이 없습니다", "Common.Views.Comments.txtEmpty": "시트에 코멘트가 없습니다", "Common.Views.CopyWarningDialog.textDontShow": "이 메시지를 다시 표시하지 않음", "Common.Views.CopyWarningDialog.textMsg": "편집기 도구 모음 단추 및 컨텍스트 메뉴 작업을 사용하여 복사, 잘라 내기 및 붙여 넣기 작업은이 편집기 탭 내에서만 수행됩니다. <br> <br> 외부 응용 프로그램으로 복사하거나 붙여 넣으려면 편집기 탭은 다음과 같은 키보드 조합을 사용합니다 : ", "Common.Views.CopyWarningDialog.textTitle": "작업 복사, 잘라 내기 및 붙여 넣기", "Common.Views.CopyWarningDialog.textToCopy": "복사", "Common.Views.CopyWarningDialog.textToCut": "잘라 내기", "Common.Views.CopyWarningDialog.textToPaste": "붙여 넣기", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "인쇄", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "빠른 인쇄", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "로드 중 ...", "Common.Views.DocumentAccessDialog.textTitle": "공유 설정", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "지우개", "Common.Views.Draw.hintSelect": "선택", "Common.Views.Draw.txtEraser": "지우개", "Common.Views.Draw.txtHighlighter": "하이라이터", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "펜:", "Common.Views.Draw.txtSelect": "선택", "Common.Views.Draw.txtSize": "크기", "Common.Views.EditNameDialog.textLabel": "라벨:", "Common.Views.EditNameDialog.textLabelError": "라벨은 비워 둘 수 없습니다.", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "파일을 편집 중인 사용자:", "Common.Views.Header.textAddFavorite": "즐겨찾기에 추가", "Common.Views.Header.textAdvSettings": "고급 설정", "Common.Views.Header.textBack": "파일 위치 열기", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "보기 컴팩트 도구 모음", "Common.Views.Header.textHideLines": "눈금자 숨기기", "Common.Views.Header.textHideStatusBar": "상태 표시 줄 숨기기", "Common.Views.Header.textPrint": "인쇄", "Common.Views.Header.textReadOnly": "읽기 전용", "Common.Views.Header.textRemoveFavorite": "즐겨찾기에서 제거", "Common.Views.Header.textSaveBegin": "저장 중 ...", "Common.Views.Header.textSaveChanged": "수정된", "Common.Views.Header.textSaveEnd": "모든 변경 사항이 저장되었습니다", "Common.Views.Header.textSaveExpander": "모든 변경 사항이 저장되었습니다", "Common.Views.Header.textShare": "공유", "Common.Views.Header.textZoom": "확대/축소", "Common.Views.Header.tipAccessRights": "문서 액세스 권한 관리", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "파일을 다운로드", "Common.Views.Header.tipGoEdit": "현재 파일 편집", "Common.Views.Header.tipPrint": "파일 출력", "Common.Views.Header.tipPrintQuick": "빠른 인쇄", "Common.Views.Header.tipRedo": "다시 실행", "Common.Views.Header.tipSave": "저장", "Common.Views.Header.tipSearch": "검색", "Common.Views.Header.tipUndo": "실행 취소", "Common.Views.Header.tipUndock": "별도 창으로 이동", "Common.Views.Header.tipUsers": "사용자 보기", "Common.Views.Header.tipViewSettings": "보기 설정", "Common.Views.Header.tipViewUsers": "사용자보기 및 문서 액세스 권한 관리", "Common.Views.Header.txtAccessRights": "액세스 권한 변경", "Common.Views.Header.txtRename": "이름 바꾸기", "Common.Views.History.textCloseHistory": "버전 기록 닫기", "Common.Views.History.textHideAll": "자세한 변경 사항 숨기기", "Common.Views.History.textHighlightDeleted": "결과 강조 삭제", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "복구", "Common.Views.History.textShowAll": "자세한 변경 사항 표시", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "이미지 URL 붙여 넣기 :", "Common.Views.ImageFromUrlDialog.txtEmpty": "이 입력란은 필수 항목", "Common.Views.ImageFromUrlDialog.txtNotUrl": "이 필드는 \"http://www.example.com\"형식의 URL이어야합니다.", "Common.Views.ListSettingsDialog.textBulleted": "단추", "Common.Views.ListSettingsDialog.textFromFile": "파일에서", "Common.Views.ListSettingsDialog.textFromStorage": "스토리지로 부터", "Common.Views.ListSettingsDialog.textFromUrl": "URL로부터", "Common.Views.ListSettingsDialog.textNumbering": "번호 매기기", "Common.Views.ListSettingsDialog.textSelect": "선택해서 가져오다", "Common.Views.ListSettingsDialog.tipChange": "글 머리 기호 변경", "Common.Views.ListSettingsDialog.txtBullet": "단추", "Common.Views.ListSettingsDialog.txtColor": "색상", "Common.Views.ListSettingsDialog.txtImage": "이미지", "Common.Views.ListSettingsDialog.txtImport": "가져오기", "Common.Views.ListSettingsDialog.txtNewBullet": "새로운 글머리 기호", "Common.Views.ListSettingsDialog.txtNewImage": "새로운 이미지", "Common.Views.ListSettingsDialog.txtNone": "없음", "Common.Views.ListSettingsDialog.txtOfText": "전체의 %", "Common.Views.ListSettingsDialog.txtSize": "크기", "Common.Views.ListSettingsDialog.txtStart": "시작", "Common.Views.ListSettingsDialog.txtSymbol": "기호", "Common.Views.ListSettingsDialog.txtTitle": "목록 설정", "Common.Views.ListSettingsDialog.txtType": "형식", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "파일 닫기", "Common.Views.OpenDialog.textInvalidRange": "유효하지 않은 셀 범위", "Common.Views.OpenDialog.textSelectData": "데이터 선택", "Common.Views.OpenDialog.txtAdvanced": "고급", "Common.Views.OpenDialog.txtColon": "콜론", "Common.Views.OpenDialog.txtComma": "쉼표", "Common.Views.OpenDialog.txtDelimiter": "구분 기호", "Common.Views.OpenDialog.txtDestData": "데이터 배치 선택", "Common.Views.OpenDialog.txtEmpty": "이 입력란은 필수 항목입니다.", "Common.Views.OpenDialog.txtEncoding": "인코딩", "Common.Views.OpenDialog.txtIncorrectPwd": "비밀번호가 맞지 않음", "Common.Views.OpenDialog.txtOpenFile": "파일을 열려면 암호를 입력하십시오.", "Common.Views.OpenDialog.txtOther": "기타", "Common.Views.OpenDialog.txtPassword": "비밀번호", "Common.Views.OpenDialog.txtPreview": "미리보기", "Common.Views.OpenDialog.txtProtected": "암호를 입력하고 파일을 열면 파일의 현재 암호가 재설정됩니다.", "Common.Views.OpenDialog.txtSemicolon": "세미콜론", "Common.Views.OpenDialog.txtSpace": "공간", "Common.Views.OpenDialog.txtTab": "탭", "Common.Views.OpenDialog.txtTitle": "% 1 옵션 선택", "Common.Views.OpenDialog.txtTitleProtected": "보호 된 파일", "Common.Views.PasswordDialog.txtDescription": "문서 보호용 비밀번호를 세팅하세요", "Common.Views.PasswordDialog.txtIncorrectPwd": "확인 비밀번호가 같지 않음", "Common.Views.PasswordDialog.txtPassword": "암호", "Common.Views.PasswordDialog.txtRepeat": "비밀번호 반복", "Common.Views.PasswordDialog.txtTitle": "비밀번호 설정", "Common.Views.PasswordDialog.txtWarning": "주의: 암호를 잊으면 복구할 수 없습니다. 암호는 대/소문자를 구분합니다. 이 코드를 안전한 곳에 보관하세요.", "Common.Views.PluginDlg.textLoading": "로드 중", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "플러그인", "Common.Views.Plugins.strPlugins": "플러그인", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "시작", "Common.Views.Plugins.textStop": "정지", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "비밀번호로 암호화", "Common.Views.Protection.hintDelPwd": "비밀번호 삭제", "Common.Views.Protection.hintPwd": "비밀번호 변경 또는 삭제", "Common.Views.Protection.hintSignature": "디지털 서명 또는 서명 라인을 추가 ", "Common.Views.Protection.txtAddPwd": "비밀번호 추가", "Common.Views.Protection.txtChangePwd": "비밀번호를 변경", "Common.Views.Protection.txtDeletePwd": "비밀번호 삭제", "Common.Views.Protection.txtEncrypt": "암호화", "Common.Views.Protection.txtInvisibleSignature": "디지털 서명을 추가", "Common.Views.Protection.txtSignature": "서명", "Common.Views.Protection.txtSignatureLine": "서명란 추가", "Common.Views.RecentFiles.txtOpenRecent": "최근 열기", "Common.Views.RenameDialog.textName": "파일 이름", "Common.Views.RenameDialog.txtInvalidName": "파일 이름에 다음 문자를 포함 할 수 없습니다 :", "Common.Views.ReviewChanges.hintNext": "다음 변경 사항", "Common.Views.ReviewChanges.hintPrev": "이전 변경으로", "Common.Views.ReviewChanges.strFast": "빠르게", "Common.Views.ReviewChanges.strFastDesc": "실시간 협력 편집. 모든 변경사항들은 자동적으로 저장됨.", "Common.Views.ReviewChanges.strStrict": "엄격한", "Common.Views.ReviewChanges.strStrictDesc": "\"저장\" 버튼을 사용하여 귀하와 다른 사람들이 변경한 사항을 동기화하십시오.", "Common.Views.ReviewChanges.tipAcceptCurrent": "현재 변경 내용 적용", "Common.Views.ReviewChanges.tipCoAuthMode": "협력 편집 모드 세팅", "Common.Views.ReviewChanges.tipCommentRem": "코멘트 삭제", "Common.Views.ReviewChanges.tipCommentRemCurrent": "현재 코멘트 삭제", "Common.Views.ReviewChanges.tipCommentResolve": "코멘트를 해결된 것으로 표시", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "현 코멘트를 해결된 것으로 표시", "Common.Views.ReviewChanges.tipHistory": "버전 표시", "Common.Views.ReviewChanges.tipRejectCurrent": "현재 변경 거부", "Common.Views.ReviewChanges.tipReview": "변경 내역 추적", "Common.Views.ReviewChanges.tipReviewView": "변경사항이 표시될 모드 선택", "Common.Views.ReviewChanges.tipSetDocLang": "문서 언어 설정", "Common.Views.ReviewChanges.tipSetSpelling": "맞춤법 검사", "Common.Views.ReviewChanges.tipSharing": "문서 액세스 권한 관리", "Common.Views.ReviewChanges.txtAccept": "수락", "Common.Views.ReviewChanges.txtAcceptAll": "모든 변경 내용 적용", "Common.Views.ReviewChanges.txtAcceptChanges": "변경 접수", "Common.Views.ReviewChanges.txtAcceptCurrent": "현재 변경 내용 적용", "Common.Views.ReviewChanges.txtChat": "채팅", "Common.Views.ReviewChanges.txtClose": "완료", "Common.Views.ReviewChanges.txtCoAuthMode": "공동 편집 모드", "Common.Views.ReviewChanges.txtCommentRemAll": "모든 코멘트 삭제", "Common.Views.ReviewChanges.txtCommentRemCurrent": "현재 코멘트 삭제", "Common.Views.ReviewChanges.txtCommentRemMy": "내 코멘트 삭제", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "내 현재 코멘트 삭제", "Common.Views.ReviewChanges.txtCommentRemove": "삭제", "Common.Views.ReviewChanges.txtCommentResolve": "해결", "Common.Views.ReviewChanges.txtCommentResolveAll": "모든 코멘트를 해결된 것으로 표시", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "현 코멘트를 해결된 것으로 표시", "Common.Views.ReviewChanges.txtCommentResolveMy": "내 코멘트를 해결된 것을 표시", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "내 코멘트를 해결된 것으로 표시", "Common.Views.ReviewChanges.txtDocLang": "언어", "Common.Views.ReviewChanges.txtFinal": "모든 변경 접수됨 (미리보기)", "Common.Views.ReviewChanges.txtFinalCap": "최종", "Common.Views.ReviewChanges.txtHistory": "버전 기록", "Common.Views.ReviewChanges.txtMarkup": "모든 변경 (편집)", "Common.Views.ReviewChanges.txtMarkupCap": "마크업", "Common.Views.ReviewChanges.txtNext": "다음", "Common.Views.ReviewChanges.txtOriginal": "모든 변경 거부됨 (미리보기)", "Common.Views.ReviewChanges.txtOriginalCap": "오리지널", "Common.Views.ReviewChanges.txtPrev": "이전", "Common.Views.ReviewChanges.txtReject": "거부", "Common.Views.ReviewChanges.txtRejectAll": "모든 변경 사항 거부", "Common.Views.ReviewChanges.txtRejectChanges": "변경 거부", "Common.Views.ReviewChanges.txtRejectCurrent": "현재 변경 거부", "Common.Views.ReviewChanges.txtSharing": "공유", "Common.Views.ReviewChanges.txtSpelling": "맞춤법 검사", "Common.Views.ReviewChanges.txtTurnon": "변경 내역 추적", "Common.Views.ReviewChanges.txtView": "디스플레이 모드", "Common.Views.ReviewPopover.textAdd": "추가", "Common.Views.ReviewPopover.textAddReply": "답장 추가", "Common.Views.ReviewPopover.textCancel": "취소", "Common.Views.ReviewPopover.textClose": "닫기", "Common.Views.ReviewPopover.textComment": "코멘트", "Common.Views.ReviewPopover.textEdit": "확인", "Common.Views.ReviewPopover.textEnterComment": "여기에 의견을 입력하십시오", "Common.Views.ReviewPopover.textMention": "+이 내용은 이 문서에 접근할 시 이메일을 통해 전해 질 것입니다.", "Common.Views.ReviewPopover.textMentionNotify": "+이 내용은 이메일을 통해 사용자에게 알려 줄 것 입니다.", "Common.Views.ReviewPopover.textOpenAgain": "다시 열기", "Common.Views.ReviewPopover.textReply": "답변", "Common.Views.ReviewPopover.textResolve": "해결", "Common.Views.ReviewPopover.textViewResolved": "코멘트를 다시 열 수 있는 권한이 없습니다", "Common.Views.ReviewPopover.txtDeleteTip": "삭제", "Common.Views.ReviewPopover.txtEditTip": "편집", "Common.Views.SaveAsDlg.textLoading": "로드 중", "Common.Views.SaveAsDlg.textTitle": "저장 폴더", "Common.Views.SearchPanel.textByColumns": "열 기준", "Common.Views.SearchPanel.textByRows": "행 기준", "Common.Views.SearchPanel.textCaseSensitive": "대소 문자를 구분합니다", "Common.Views.SearchPanel.textCell": "셀", "Common.Views.SearchPanel.textCloseSearch": "검색 닫기", "Common.Views.SearchPanel.textContentChanged": "문서가 변경되었습니다.", "Common.Views.SearchPanel.textFind": "찾기", "Common.Views.SearchPanel.textFindAndReplace": "찾기 및 바꾸기", "Common.Views.SearchPanel.textFormula": "수식", "Common.Views.SearchPanel.textFormulas": "수식", "Common.Views.SearchPanel.textItemEntireCell": "전체 셀 내용", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} 항목이 성공적으로 대체되었습니다.", "Common.Views.SearchPanel.textLookIn": "검색 범위", "Common.Views.SearchPanel.textMatchUsingRegExp": "정규 표현식을 사용하여 일치하는 것을 찾기", "Common.Views.SearchPanel.textName": "이름", "Common.Views.SearchPanel.textNoMatches": "일치 하는 항목 없음", "Common.Views.SearchPanel.textNoSearchResults": "검색결과 없음", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} 항목이 대체되었습니다. 남은 {2} 항목은 다른 사용자에 의해 잠겨 있습니다.", "Common.Views.SearchPanel.textReplace": "바꾸기", "Common.Views.SearchPanel.textReplaceAll": "모두 바꾸기", "Common.Views.SearchPanel.textReplaceWith": "다음으로 교체", "Common.Views.SearchPanel.textSearch": "검색", "Common.Views.SearchPanel.textSearchAgain": "{0}정확한 결과를 보려면 새 검색 {1}을(를) 수행하십시오.", "Common.Views.SearchPanel.textSearchHasStopped": "검색이 중지되었습니다", "Common.Views.SearchPanel.textSearchOptions": "검색 옵션", "Common.Views.SearchPanel.textSearchResults": "검색결과: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textSelectDataRange": "데이터 범위 선택", "Common.Views.SearchPanel.textSheet": "시트", "Common.Views.SearchPanel.textSpecificRange": "특정 범위", "Common.Views.SearchPanel.textTooManyResults": "표시할 결과가 너무 많습니다.", "Common.Views.SearchPanel.textValue": "값", "Common.Views.SearchPanel.textValues": "값", "Common.Views.SearchPanel.textWholeWords": "전체 단어 만", "Common.Views.SearchPanel.textWithin": "내부에", "Common.Views.SearchPanel.textWorkbook": "통합 문서", "Common.Views.SearchPanel.tipNextResult": "다음결과", "Common.Views.SearchPanel.tipPreviousResult": "이전 결과", "Common.Views.SelectFileDlg.textLoading": "로드 중", "Common.Views.SelectFileDlg.textTitle": "데이터 소스 선택", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "볼드체", "Common.Views.SignDialog.textCertificate": "인증", "Common.Views.SignDialog.textChange": "변경", "Common.Views.SignDialog.textInputName": "서명자 성함을 입력하세요", "Common.Views.SignDialog.textItalic": "이탤릭", "Common.Views.SignDialog.textNameError": "서명자의 이름은 비워둘 수 없습니다.", "Common.Views.SignDialog.textPurpose": "이 문서에 서명하는 목적", "Common.Views.SignDialog.textSelect": "선택", "Common.Views.SignDialog.textSelectImage": "이미지 선택", "Common.Views.SignDialog.textSignature": "서명은 처럼 보임", "Common.Views.SignDialog.textTitle": "서명문서", "Common.Views.SignDialog.textUseImage": "또는 서명으로 그림을 사용하려면 '이미지 선택'을 클릭", "Common.Views.SignDialog.textValid": "%1에서 %2까지 유효", "Common.Views.SignDialog.tipFontName": "폰트명", "Common.Views.SignDialog.tipFontSize": "글꼴 크기", "Common.Views.SignSettingsDialog.textAllowComment": "서명 대화창에 서명자의 코멘트 추가 허용", "Common.Views.SignSettingsDialog.textDefInstruction": "이 문서에 서명하기 전에, 서명하는 내용이 정확한지 확인하세요.", "Common.Views.SignSettingsDialog.textInfoEmail": "이메일", "Common.Views.SignSettingsDialog.textInfoName": "이름", "Common.Views.SignSettingsDialog.textInfoTitle": "서명자 타이틀", "Common.Views.SignSettingsDialog.textInstructions": "서명자용 지침", "Common.Views.SignSettingsDialog.textShowDate": "서명라인에 서명 날짜를 보여주세요", "Common.Views.SignSettingsDialog.textTitle": "서명 셋업", "Common.Views.SignSettingsDialog.txtEmpty": "이 입력란은 필수 항목", "Common.Views.SymbolTableDialog.textCharacter": "문자", "Common.Views.SymbolTableDialog.textCode": "유니코드 HEX 값", "Common.Views.SymbolTableDialog.textCopyright": "저작권 표시", "Common.Views.SymbolTableDialog.textDCQuote": "큰 따옴표 닫기", "Common.Views.SymbolTableDialog.textDOQuote": "큰 따옴표 (왼쪽)", "Common.Views.SymbolTableDialog.textEllipsis": "말줄임표", "Common.Views.SymbolTableDialog.textEmDash": "Em 대시", "Common.Views.SymbolTableDialog.textEmSpace": "Em 공백", "Common.Views.SymbolTableDialog.textEnDash": "En 대시", "Common.Views.SymbolTableDialog.textEnSpace": "En 공백", "Common.Views.SymbolTableDialog.textFont": "글꼴", "Common.Views.SymbolTableDialog.textNBHyphen": "줄 바꿈없는 하이픈", "Common.Views.SymbolTableDialog.textNBSpace": "줄 바꿈 없는 공백", "Common.Views.SymbolTableDialog.textPilcrow": "단락기호", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 칸", "Common.Views.SymbolTableDialog.textRange": "범위", "Common.Views.SymbolTableDialog.textRecent": "최근 사용한 기호", "Common.Views.SymbolTableDialog.textRegistered": "등록된 서명", "Common.Views.SymbolTableDialog.textSCQuote": "작은 따옴표 닫기", "Common.Views.SymbolTableDialog.textSection": "섹션 기호", "Common.Views.SymbolTableDialog.textShortcut": "단축키", "Common.Views.SymbolTableDialog.textSHyphen": "소프트 하이픈", "Common.Views.SymbolTableDialog.textSOQuote": "작은 따옴표 (왼쪽)", "Common.Views.SymbolTableDialog.textSpecial": "특수 문자", "Common.Views.SymbolTableDialog.textSymbols": "기호", "Common.Views.SymbolTableDialog.textTitle": "기호", "Common.Views.SymbolTableDialog.textTradeMark": "로고기호", "Common.Views.UserNameDialog.textDontShow": "다시 표시하지 않음", "Common.Views.UserNameDialog.textLabel": "라벨:", "Common.Views.UserNameDialog.textLabelError": "라벨은 비워 둘 수 없습니다.", "SSE.Controllers.DataTab.strSheet": "시트", "SSE.Controllers.DataTab.textAddExternalData": "외부 소스로의 링크가 추가되었습니다. 데이터 탭에서 이러한 링크를 업데이트할 수 있습니다.", "SSE.Controllers.DataTab.textColumns": "열", "SSE.Controllers.DataTab.textContinue": "Continue", "SSE.Controllers.DataTab.textDontUpdate": "업데이트하지 않음", "SSE.Controllers.DataTab.textEmptyUrl": "URL을 지정해야 합니다.", "SSE.Controllers.DataTab.textRows": "행", "SSE.Controllers.DataTab.textTurnOff": "Turn off AutoUpdate", "SSE.Controllers.DataTab.textUpdate": "업데이트", "SSE.Controllers.DataTab.textWizard": "텍스트 나누기", "SSE.Controllers.DataTab.txtDataValidation": "데이터 유효성", "SSE.Controllers.DataTab.txtErrorExternalLink": "오류: 업데이트에 실패했습니다.", "SSE.Controllers.DataTab.txtExpand": "확장", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "선택한 콘텐츠 옆의 데이터는 삭제되지 않습니다. 인접 데이터를 포함하도록 선택 영역을 확장하시겠습니까, 아니면 현재 선택한 셀을 계속 사용하시겠습니까?", "SSE.Controllers.DataTab.txtExtendDataValidation": "이 선택에는 데이터 확인 설정이 없는 데이터가 포함되어 있습니다. <br>데이터 유효성 체크를 이 장치로 하시겠습니까?", "SSE.Controllers.DataTab.txtImportWizard": "텍스트 가져오기 마법사", "SSE.Controllers.DataTab.txtRemDuplicates": "중복된 항목 제거", "SSE.Controllers.DataTab.txtRemoveDataValidation": "이 선택에는 여러 확인 유형이 포함됩니다. <br>현재 설정을 지우고 계속하시겠습니까?", "SSE.Controllers.DataTab.txtRemSelected": "선택한 위치에서 삭제", "SSE.Controllers.DataTab.txtUrlTitle": "데이터 URL 붙여넣기", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "This workbook contains links to external sources which update automatically. This might be unsafe.<br><br>If you trust them, press Continue.", "SSE.Controllers.DataTab.warnUpdateExternalData": "이 통합 문서에는 하나 이상의 안전하지 않을 수 있는 외부 소스로의 링크가 포함되어 있습니다.<br>만약 이 링크를 신뢰한다면 최신 데이터를 얻기 위해 업데이트하세요.", "SSE.Controllers.DocumentHolder.alignmentText": "정렬", "SSE.Controllers.DocumentHolder.centerText": "Center", "SSE.Controllers.DocumentHolder.deleteColumnText": "열 삭제", "SSE.Controllers.DocumentHolder.deleteRowText": "행 삭제", "SSE.Controllers.DocumentHolder.deleteText": "Delete", "SSE.Controllers.DocumentHolder.errorInvalidLink": "링크 참조가 존재하지 않습니다. 링크를 수정하거나 삭제하십시오.", "SSE.Controllers.DocumentHolder.guestText": "Guest", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "왼쪽 열", "SSE.Controllers.DocumentHolder.insertColumnRightText": "오른쪽 열", "SSE.Controllers.DocumentHolder.insertRowAboveText": "위의 행", "SSE.Controllers.DocumentHolder.insertRowBelowText": "행 아래", "SSE.Controllers.DocumentHolder.insertText": "Insert", "SSE.Controllers.DocumentHolder.leftText": "Left", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "경고", "SSE.Controllers.DocumentHolder.rightText": "Right", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "자동 고침 옵션", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "열 너비 {0} 기호 ({1} 픽셀)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "행 높이 {0} 점 ({1} 픽셀)", "SSE.Controllers.DocumentHolder.textCtrlClick": "CTRL 키를 누른 상태에서 링크 클릭", "SSE.Controllers.DocumentHolder.textInsertLeft": "왼쪽에 삽입", "SSE.Controllers.DocumentHolder.textInsertTop": "위의 행 삽입", "SSE.Controllers.DocumentHolder.textPasteSpecial": "특수기호 붙이기", "SSE.Controllers.DocumentHolder.textStopExpand": "자동으로 표 확장 끔", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "이 요소는 다른 사용자가 편집하고 있습니다.", "SSE.Controllers.DocumentHolder.txtAboveAve": "평균 이상", "SSE.Controllers.DocumentHolder.txtAddBottom": "아래쪽 테두리 추가", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "분수 막대 추가", "SSE.Controllers.DocumentHolder.txtAddHor": "가로선 추가", "SSE.Controllers.DocumentHolder.txtAddLB": "왼쪽 하단 추가", "SSE.Controllers.DocumentHolder.txtAddLeft": "왼쪽 테두리 추가", "SSE.Controllers.DocumentHolder.txtAddLT": "왼쪽 상단 줄 추가", "SSE.Controllers.DocumentHolder.txtAddRight": "오른쪽 테두리 추가", "SSE.Controllers.DocumentHolder.txtAddTop": "위쪽 테두리 추가", "SSE.Controllers.DocumentHolder.txtAddVer": "세로선 추가", "SSE.Controllers.DocumentHolder.txtAlignToChar": "문자에 정렬", "SSE.Controllers.DocumentHolder.txtAll": "(전체)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "열 머리글, 데이터 및 총 행을 포함하여 테이블 또는 지정된 테이블 열의 전체 내용을 반환합니다", "SSE.Controllers.DocumentHolder.txtAnd": "그리고", "SSE.Controllers.DocumentHolder.txtBegins": "~와 함께 시작하다.\n~로 시작하다", "SSE.Controllers.DocumentHolder.txtBelowAve": "평균 이하", "SSE.Controllers.DocumentHolder.txtBlanks": "(빈칸들)", "SSE.Controllers.DocumentHolder.txtBorderProps": "테두리 속성", "SSE.Controllers.DocumentHolder.txtBottom": "Bottom", "SSE.Controllers.DocumentHolder.txtByField": "%2의 %1", "SSE.Controllers.DocumentHolder.txtColumn": "열", "SSE.Controllers.DocumentHolder.txtColumnAlign": "열 정렬", "SSE.Controllers.DocumentHolder.txtContains": "포함", "SSE.Controllers.DocumentHolder.txtCopySuccess": "클립보드로 링크 복사됨", "SSE.Controllers.DocumentHolder.txtDataTableHint": "테이블 또는 지정된 테이블 열의 데이터 셀을 반환합니다", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "인수 크기 감소", "SSE.Controllers.DocumentHolder.txtDeleteArg": "인수 삭제", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "수동 브레이크 삭제", "SSE.Controllers.DocumentHolder.txtDeleteChars": "둘러싸는 문자 삭제", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "둘러싸는 문자 및 구분 기호 삭제", "SSE.Controllers.DocumentHolder.txtDeleteEq": "수식 삭제", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "문자 삭제", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "래디 칼 삭제", "SSE.Controllers.DocumentHolder.txtEnds": "종료", "SSE.Controllers.DocumentHolder.txtEquals": "같음", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "셀의 색상에 등호", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "글꼴 색상 등호", "SSE.Controllers.DocumentHolder.txtExpand": "확장 및 정렬", "SSE.Controllers.DocumentHolder.txtExpandSort": "선택 영역 옆의 데이터는 정렬되지 않습니다. 인접한 데이터를 포함하도록 선택 영역을 확장 하시겠습니까, 아니면 현재 선택된 셀만 정렬할까요?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "바닥", "SSE.Controllers.DocumentHolder.txtFilterTop": "위", "SSE.Controllers.DocumentHolder.txtFractionLinear": "선형 분수로 변경", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "기울어 진 분수로 변경", "SSE.Controllers.DocumentHolder.txtFractionStacked": "누적 분율로 변경", "SSE.Controllers.DocumentHolder.txtGreater": "보다 큼", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "크거나 같음", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "텍스트를 덮는 문자", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "문자 아래의 문자", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "테이블 또는 지정된 테이블 열에 대한 열 머리글을 반환합니다", "SSE.Controllers.DocumentHolder.txtHeight": "높이", "SSE.Controllers.DocumentHolder.txtHideBottom": "아래쪽 테두리 숨기기", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "하단 제한 숨기기", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "닫는 대괄호 숨기기", "SSE.Controllers.DocumentHolder.txtHideDegree": "학위 숨기기", "SSE.Controllers.DocumentHolder.txtHideHor": "가로 선 숨기기", "SSE.Controllers.DocumentHolder.txtHideLB": "왼쪽 하단 줄 숨기기", "SSE.Controllers.DocumentHolder.txtHideLeft": "왼쪽 테두리 숨기기", "SSE.Controllers.DocumentHolder.txtHideLT": "왼쪽 상단 줄 숨기기", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "여는 대괄호 숨기기", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "자리 표시 자 숨기기", "SSE.Controllers.DocumentHolder.txtHideRight": "오른쪽 테두리 숨기기", "SSE.Controllers.DocumentHolder.txtHideTop": "위쪽 테두리 숨기기", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "상한값 숨기기", "SSE.Controllers.DocumentHolder.txtHideVer": "수직선 숨기기", "SSE.Controllers.DocumentHolder.txtImportWizard": "텍스트 가져오기 마법사", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "인수 크기 늘리기", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "뒤에 인수를 삽입하십시오.", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "앞에 인수를 삽입하십시오.", "SSE.Controllers.DocumentHolder.txtInsertBreak": "수동 중단 삽입", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "이후 수식 삽입", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "이전에 수식 삽입", "SSE.Controllers.DocumentHolder.txtItems": "아이템", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "텍스트 만 유지", "SSE.Controllers.DocumentHolder.txtLess": "보다 작음", "SSE.Controllers.DocumentHolder.txtLessEquals": "작거나 같음", "SSE.Controllers.DocumentHolder.txtLimitChange": "제한 위치 변경", "SSE.Controllers.DocumentHolder.txtLimitOver": "텍스트 제한", "SSE.Controllers.DocumentHolder.txtLimitUnder": "텍스트에서 제한", "SSE.Controllers.DocumentHolder.txtLockSort": "선택의 범위 근처에 데이터가 존재 하지만이 셀을 변경하려면 충분한 권한이 없습니다. <br> 선택의 범위를 계속 하시겠습니까?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "인수 높이에 대괄호 일치", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "매트릭스 정렬", "SSE.Controllers.DocumentHolder.txtNoChoices": "셀을 채울 선택이 없습니다. <br> 열의 텍스트 값만 대체 할 수 있습니다.", "SSE.Controllers.DocumentHolder.txtNotBegins": "다음 문자에서 시작하기", "SSE.Controllers.DocumentHolder.txtNotContains": "포함하지 않음", "SSE.Controllers.DocumentHolder.txtNotEnds": "다음 문자열로 끝나지 않음", "SSE.Controllers.DocumentHolder.txtNotEquals": "같지 않음", "SSE.Controllers.DocumentHolder.txtOr": "또는", "SSE.Controllers.DocumentHolder.txtOverbar": "텍스트 위에 바", "SSE.Controllers.DocumentHolder.txtPaste": "붙여 넣기", "SSE.Controllers.DocumentHolder.txtPasteBorders": "테두리없는 수식", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "수식 + 열 너비", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "대상 서식 지정", "SSE.Controllers.DocumentHolder.txtPasteFormat": "서식 붙이기 만 붙여 넣기", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "수식 + 숫자 형식", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "수식 만 붙여 넣기", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "수식 + 모든 서식 지정", "SSE.Controllers.DocumentHolder.txtPasteLink": "붙여 넣기 링크", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "연결된 그림", "SSE.Controllers.DocumentHolder.txtPasteMerge": "조건부 서식 병합", "SSE.Controllers.DocumentHolder.txtPastePicture": "그림", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "소스 서식 지정", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Transpose", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "값 + 모든 서식 지정", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "값 + 숫자 형식", "SSE.Controllers.DocumentHolder.txtPasteValues": "값만 붙여 넣기", "SSE.Controllers.DocumentHolder.txtPercent": "백분율", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "리두 테이블 자동확장", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "분수 막대 제거", "SSE.Controllers.DocumentHolder.txtRemLimit": "제한 제거", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "액센트 문자 제거", "SSE.Controllers.DocumentHolder.txtRemoveBar": "막대 제거", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "이 서명을 삭제하시겠습니까?<br>이 작업은 취소할 수 없습니다.", "SSE.Controllers.DocumentHolder.txtRemScripts": "스크립트 제거", "SSE.Controllers.DocumentHolder.txtRemSubscript": "아래 첨자 제거", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "위 첨자 제거", "SSE.Controllers.DocumentHolder.txtRowHeight": "행 높이", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "텍스트 뒤의 스크립트", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "텍스트 앞의 스크립트", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "아래쪽 한계 표시", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "닫는 괄호 표시", "SSE.Controllers.DocumentHolder.txtShowDegree": "학위 표시", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "여는 대괄호 표시", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Show placeholder", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "상한 표시", "SSE.Controllers.DocumentHolder.txtSorting": "정렬", "SSE.Controllers.DocumentHolder.txtSortSelected": "정렬 선택", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "스트레치 괄호", "SSE.Controllers.DocumentHolder.txtThisRowHint": "지정된 열의 이 행만 선택", "SSE.Controllers.DocumentHolder.txtTop": "Top", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "테이블 또는 지정된 테이블 열의 총 행을 반환합니다", "SSE.Controllers.DocumentHolder.txtUnderbar": "텍스트 아래에 바", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "테이블 자동확장 하지 않기", "SSE.Controllers.DocumentHolder.txtUseTextImport": "텍스트 마법사를 사용", "SSE.Controllers.DocumentHolder.txtWarnUrl": "이 링크는 장치와 데이터에 손상을 줄 수 있습니다. <br> 계속하시겠습니까?", "SSE.Controllers.DocumentHolder.txtWidth": "너비", "SSE.Controllers.DocumentHolder.warnFilterError": "값 필터를 적용하려면 \"값\" 영역에 하나 이상의 필드가 있어야 합니다.", "SSE.Controllers.FormulaDialog.sCategoryAll": "모든", "SSE.Controllers.FormulaDialog.sCategoryCube": "정육면체", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Custom", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "데이터베이스", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "날짜 및 시간", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "엔지니어링", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "재무", "SSE.Controllers.FormulaDialog.sCategoryInformation": "정보", "SSE.Controllers.FormulaDialog.sCategoryLast10": "지난 10가지 되살리기 목록", "SSE.Controllers.FormulaDialog.sCategoryLogical": "논리적", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "조회 및 참조", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "수학 및 삼각법", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "통계", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "텍스트 및 데이터", "SSE.Controllers.LeftMenu.newDocumentTitle": "이름없는 스프레드시트", "SSE.Controllers.LeftMenu.textByColumns": "열 기준", "SSE.Controllers.LeftMenu.textByRows": "행 기준", "SSE.Controllers.LeftMenu.textFormulas": "수식", "SSE.Controllers.LeftMenu.textItemEntireCell": "전체 셀 내용", "SSE.Controllers.LeftMenu.textLoadHistory": "버전 기록 로드 중...", "SSE.Controllers.LeftMenu.textLookin": "Look in", "SSE.Controllers.LeftMenu.textNoTextFound": "검색 한 데이터를 찾을 수 없습니다. 검색 옵션을 조정하십시오.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "대체가 이루어졌습니다. {0} 건은 건너 뛰었습니다.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "검색이 완료되었습니다. 발생 횟수가 대체되었습니다 : {0}", "SSE.Controllers.LeftMenu.textSave": "저장", "SSE.Controllers.LeftMenu.textSearch": "Search", "SSE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "SSE.Controllers.LeftMenu.textSheet": "시트", "SSE.Controllers.LeftMenu.textValues": "값", "SSE.Controllers.LeftMenu.textWarning": "경고", "SSE.Controllers.LeftMenu.textWithin": "within", "SSE.Controllers.LeftMenu.textWorkbook": "통합 문서", "SSE.Controllers.LeftMenu.txtUntitled": "제목없음", "SSE.Controllers.LeftMenu.warnDownloadAs": "이 형식으로 저장을 계속하면 텍스트를 제외한 모든 기능이 손실됩니다. 계속 하시겠습니까?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "CSV 형식은 다중 시트 파일을 저장하지 않습니다.<br>선택한 형식을 유지하고 현재 시트만 저장하려면 저장을 누르세요.<br>현재 스프레드시트를 저장하려면 취소를 누르고 다른 형식으로 저장하세요.", "SSE.Controllers.Main.confirmAddCellWatches": "이 작업으로 {0}개의 셀 모니터링이 추가됩니다.<br>계속하시겠습니까?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "이 작업은 메모리 저장 이유로 {0}개의 셀 모니터링만 추가합니다.<br>계속하시겠습니까?", "SSE.Controllers.Main.confirmMaxChangesSize": "작업의 크기가 서버에 설정된 제한을 초과합니다.<br>마지막 작업을 취소하려면 '실행 취소'를 누르고 작업을 로컬로 유지하려면 '계속'을 누르세요 (파일을 다운로드하거나 내용을 복사하여 데이터 손실이 없도록 하십시오).", "SSE.Controllers.Main.confirmMoveCellRange": "대상 셀 범위에 데이터가 포함될 수 있습니다. 작업을 계속 하시겠습니까?", "SSE.Controllers.Main.confirmPutMergeRange": "원본 데이터에 병합 된 셀이 있습니다. <br> 테이블에 붙여 넣기 전에 병합되지 않았습니다.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "머리글 행의 수식이 삭제되고 정적 텍스트로 변환됩니다. <br>계속하시겠습니까?", "SSE.Controllers.Main.confirmReplaceHFPicture": "헤더 각 섹션에는 하나의 그림만 삽입할 수 있습니다.<br>기존 그림을 대체하려면 '대체'를 누르세요.<br>기존 그림을 유지하려면 '유지'를 누르세요.", "SSE.Controllers.Main.convertationTimeoutText": "전환 시간 초과를 초과했습니다.", "SSE.Controllers.Main.criticalErrorExtText": "문서 목록으로 돌아가려면 \"OK\"를 누르십시오.", "SSE.Controllers.Main.criticalErrorTitle": "오류", "SSE.Controllers.Main.downloadErrorText": "다운로드하지 못했습니다.", "SSE.Controllers.Main.downloadTextText": "스프레드시트 다운로드 중 ...", "SSE.Controllers.Main.downloadTitleText": "스프레드시트 다운로드 중", "SSE.Controllers.Main.errNoDuplicates": "중복 값이 ​​없습니다.", "SSE.Controllers.Main.errorAccessDeny": "권한이 없는 작업을 수행하려고 합니다.<br>관리자에게 문의하십시오.", "SSE.Controllers.Main.errorArgsRange": "입력 된 수식에 오류가 있습니다. <br> 잘못된 인수 범위가 사용되었습니다.", "SSE.Controllers.Main.errorAutoFilterChange": "이 작업은 워크시트의 테이블에 있는 셀을 이동하려고 하기 때문에 허용되지 않습니다.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "테이블의 일부를 이동할 수 없으므로 선택한 셀에 대해 작업을 수행 할 수 없습니다. <br> 전체 데이터를 이동하여 다시 시도하도록 다른 데이터 범위를 선택하십시오.", "SSE.Controllers.Main.errorAutoFilterDataRange": "선택한 셀 범위에서 작업을 수행 할 수 없습니다. <br> 기존 데이터 범위와 다른 데이터 범위를 선택하고 다시 시도하십시오.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "영역에 필터링 된 셀이 포함되어있어 작업을 수행 할 수 없습니다. <br> 필터링 된 요소를 숨김 해제하고 다시 시도하십시오.", "SSE.Controllers.Main.errorBadImageUrl": "이미지 URL이 잘못되었습니다.", "SSE.Controllers.Main.errorCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.Main.errorCannotPasteImg": "클립보드에서 이 이미지를 붙여넣을 수 없지만, 장치에 저장한 후 \n삽입하거나 텍스트 없이 이미지를 복사하여 스프레드시트에 붙여넣을 수 있습니다.", "SSE.Controllers.Main.errorCannotUngroup": "그룹을 해제할 수 없습니다. 윤곽선을 시작하려면 세부 행 또는 열을 선택하고 그룹화하십시오.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "보호된 시트에서는 이 명령을 사용할 수 없습니다. 이 명령을 사용하려면 시트 보호를 해제하세요.<br>비밀번호를 입력하라는 메시지가 표시될 수 있습니다.", "SSE.Controllers.Main.errorChangeArray": "배열의 일부를 변경할 수 없습니다.", "SSE.Controllers.Main.errorChangeFilteredRange": "이렇게 하면 워크시트의 필터 범위가 변경됩니다. <br>이 작업을 완료하려면 \"자동 필터\"를 삭제하십시오.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "변경하려는 셀 또는 차트가 보호된 워크시트에 있습니다. <br>변경하려면 워크시트의 잠금을 해제하세요. 입력한 비밀번호를 수정해야 할 수도 있습니다.", "SSE.Controllers.Main.errorCircularReference": "There are one or more circular references where a formula refers to its own cell either directly or indirectly.<br>Try removing or changing these references, or moving the formulas to different cells.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "서버 연결이 끊어졌습니다. 문서를 지금 편집 할 수 없습니다.", "SSE.Controllers.Main.errorConnectToServer": "문서를 저장할 수 없습니다. 연결 설정을 확인하거나 관리자에게 문의하십시오.<br>'확인'버튼을 클릭하면 문서를 다운로드하라는 메시지가 나타납니다.", "SSE.Controllers.Main.errorConvertXml": "지원되지 않는 형식의 파일입니다.<br>XML 스프레드시트 2003 형식만 사용할 수 있습니다.", "SSE.Controllers.Main.errorCopyMultiselectArea": "이 명령은 여러 선택 항목과 함께 사용할 수 없습니다. <br> 단일 범위를 선택하고 다시 시도하십시오.", "SSE.Controllers.Main.errorCountArg": "입력 된 수식에 오류가 있습니다. <br> 잘못된 수의 인수가 사용되었습니다.", "SSE.Controllers.Main.errorCountArgExceed": "입력 된 수식에 오류가 있습니다. <br> 인수 수가 초과되었습니다.", "SSE.Controllers.Main.errorCreateDefName": "기존 명명 된 범위를 편집 할 수 없으며 일부는 편집 중임에 따라 현재 명명 된 범위를 만들 수 없습니다.", "SSE.Controllers.Main.errorCreateRange": "현재 일부 범위가 편집 중이어서 기존 범위를 편집할 수 없으며 새로운 범위를 생성할 수 없습니다.", "SSE.Controllers.Main.errorDatabaseConnection": "외부 오류. <br> 데이터베이스 연결 오류입니다. 오류가 계속 발생하면 지원 담당자에게 문의하십시오.", "SSE.Controllers.Main.errorDataEncrypted": "암호화 변경 사항이 수신되었으며 해독할 수 없습니다.", "SSE.Controllers.Main.errorDataRange": "잘못된 데이터 범위입니다.", "SSE.Controllers.Main.errorDataValidate": "입력한 값이 잘못되었습니다. <br>사용자는 이 셀에 입력할 수 있는 제한 값이 있습니다.", "SSE.Controllers.Main.errorDefaultMessage": "오류 코드 : % 1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "잠긴 셀이 포함된 열을 삭제하려고 합니다. 워크시트가 보호된 경우 잠긴 셀은 삭제할 수 없습니다. <br>잠긴 셀을 삭제하려면 워크시트의 잠금을 해제하세요. 입력한 비밀번호를 수정해야 할 수도 있습니다.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "잠긴 셀이 포함된 행을 삭제하려고 합니다. 워크시트가 보호된 경우 잠긴 셀은 삭제할 수 없습니다. <br>잠긴 셀을 삭제하려면 워크시트의 잠금을 해제하세요. 입력한 비밀번호를 수정해야 할 수도 있습니다.", "SSE.Controllers.Main.errorDependentsNoFormulas": "종속 항목 추적 명령에서는 활성 셀을 참조하는 수식을 찾지 못했습니다.", "SSE.Controllers.Main.errorDirectUrl": "문서에 대한 링크를 확인하십시오. <br>이 링크는 다운로드할 파일에 대한 직접 링크여야 합니다.", "SSE.Controllers.Main.errorEditingDownloadas": "  문서 작업 중에 알수 없는 장애가 발생했습니다.<br>  \"다른 이름으로 다운로드\"를 선택하여 파일을 현재 사용 중인 컴퓨터 하드 디스크에 저장하시기 바랍니다.", "SSE.Controllers.Main.errorEditingSaveas": "문서를 사용하는 동안 오류가 발생했습니다. <br>파일의 백업 사본을 컴퓨터의 하드 드라이브에 저장하려면 \"다른 이름으로 저장...\" 옵션을 사용하십시오.", "SSE.Controllers.Main.errorEditView": "그 중 일부가 편집 중이기 때문에 현재 기존 도면 뷰를 편집하거나 새 도면 뷰를 생성할 수 없습니다.", "SSE.Controllers.Main.errorEmailClient": "이메일 클라이언트를 찾을 수 없습니다.", "SSE.Controllers.Main.errorFilePassProtect": "문서가 암호로 보호되어 있습니다.", "SSE.Controllers.Main.errorFileRequest": "외부 오류. <br> 파일 요청 오류입니다. 오류가 지속될 경우 지원 담당자에게 문의하십시오.", "SSE.Controllers.Main.errorFileSizeExceed": "이 파일은 이 호스트의 크기 제한을 초과합니다.<br> 자세한 내용은 파일 서비스 호스트의 관리자에게 문의하십시오.", "SSE.Controllers.Main.errorFileVKey": "외부 오류. <br> 잘못된 보안 키입니다. 오류가 계속 발생하면 지원 부서에 문의하십시오.", "SSE.Controllers.Main.errorFillRange": "선택한 셀 범위를 채울 수 없습니다. <br> 병합 된 모든 셀이 같은 크기 여야합니다.", "SSE.Controllers.Main.errorForceSave": "파일 저장중 문제 발생됨. 컴퓨터 하드 드라이브에 파일을 저장하려면 '로 다운로드' 옵션을 사용 또는 나중에 다시 시도하세요.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Cannot enter a formula for an item or field name in a pivot table report.", "SSE.Controllers.Main.errorFormulaName": "입력 한 수식에 오류가 있습니다. <br> 잘못된 수식 이름이 사용되었습니다.", "SSE.Controllers.Main.errorFormulaParsing": "수식을 분석하는 동안 내부 오류가 발생했습니다.", "SSE.Controllers.Main.errorFrmlMaxLength": "수식의 길이가 8192자 제한을 초과합니다. <br>수정하고 다시 시도하십시오.", "SSE.Controllers.Main.errorFrmlMaxReference": "값, 셀 참조 및/또는 이름이 너무 많기 때문에 이 수식을 입력할 수 없습니다.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "수식의 텍스트 값은 255자로 제한됩니다. <br>CONCATENATE 함수 또는 연결 연산자(&)를 사용합니다.", "SSE.Controllers.Main.errorFrmlWrongReferences": "이 함수는 존재하지 않는 시트를 참조합니다. <br> 데이터를 확인한 후 다시 시도하십시오.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "선택한 셀 범위에 대해 작업을 완료 할 수 없습니다. <br> 첫 번째 테이블 행이 같은 행에 있고 결과 테이블이 현재 테이블과 겹치도록 범위를 선택하십시오. . ", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "선택한 셀 범위에 대해 작업을 완료 할 수 없습니다. <br> 다른 테이블을 포함하지 않는 범위를 선택하십시오.", "SSE.Controllers.Main.errorInconsistentExt": "파일을 여는 중 오류가 발생했습니다.<br>파일 내용이 파일 확장명과 일치하지 않습니다.", "SSE.Controllers.Main.errorInconsistentExtDocx": "파일을 여는 동안 오류가 발생했습니다.<br>파일 내용이 텍스트 문서(예: docx)에 해당하지만 파일의 확장자가 일치하지 않습니다:1%.", "SSE.Controllers.Main.errorInconsistentExtPdf": "파일을 여는 동안 오류가 발생했습니다.<br>파일의 내용은 pdf/djvu/xps/oxps 형식 중 하나와 일치하지만, 파일의 확장자가 일치하지 않습니다.:1%.", "SSE.Controllers.Main.errorInconsistentExtPptx": "파일을 여는 동안 오류가 발생했습니다.<br>파일 내용이 프리젠테이션(예: pptx)에 해당하지만 파일의 확장자가 일치하지 않습니다:1%.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "파일을 여는 동안 오류가 발생했습니다.<br>파일 내용은 스프레드시트(예: xlsx)에 해당하지만 파일의 확장자가 일치하지 않습니다:1%.", "SSE.Controllers.Main.errorInvalidRef": "선택 항목의 정확한 이름을 입력하거나 이동할 참조를 입력하십시오.", "SSE.Controllers.Main.errorKeyEncrypt": "알 수없는 키 설명자", "SSE.Controllers.Main.errorKeyExpire": "키 설명자가 만료되었습니다", "SSE.Controllers.Main.errorLabledColumnsPivot": "피벗 테이블을 만들려면 레이블이 지정된 열이 있는 목록으로 구성된 데이터를 사용합니다.", "SSE.Controllers.Main.errorLoadingFont": "글꼴이 로드되지 않았습니다. <br>문서 관리 관리자에게 문의하십시오.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "위치 또는 데이터 범위에 대한 참조가 잘못되었습니다.", "SSE.Controllers.Main.errorLockedAll": "다른 사용자가 시트를 잠근 상태에서 작업을 수행 할 수 없습니다.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "One of the cells involved in the goal seek process has been modified by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "피벗 테이블에서 데이터를 변경할 수 없습니다.", "SSE.Controllers.Main.errorLockedWorksheetRename": "시트의 이름을 다른 사용자가 바꾸면 이름을 바꿀 수 없습니다.", "SSE.Controllers.Main.errorMaxPoints": "차트당 시리즈내 포인트의 최대값은 4096임", "SSE.Controllers.Main.errorMoveRange": "병합 된 셀의 일부를 변경할 수 없습니다", "SSE.Controllers.Main.errorMoveSlicerError": "한 통합 문서에서 다른 통합 문서로 테이블 슬라이서를 복사할 수 없습니다. <br>전체 테이블과 슬라이서를 선택하여 다시 시도하세요.", "SSE.Controllers.Main.errorMultiCellFormula": "다중 셀 배열 수식은 테이블에서 허용되지 않습니다.", "SSE.Controllers.Main.errorNoDataToParse": "선택한 행에는 구문 분석을 위한 데이터가 없습니다.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.Main.errorOpenWarning": "파일 수식 중 하나가 8192자 제한을 초과합니다. <br>공식이 삭제되었습니다.", "SSE.Controllers.Main.errorOperandExpected": "입력 한 함수 구문이 올바르지 않습니다. 괄호 중 하나가 누락되어 있는지 확인하십시오 ( '('또는 ')').", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "잘못된 비밀번호. <br>캡 잠금 버튼이 꺼져 있는지 확인하고 올바른 대문자를 사용해야 합니다.", "SSE.Controllers.Main.errorPasteInPivot": "We can't make this change for the selected cells because it will affect a pivot table.<br>Use the field list to change the report.", "SSE.Controllers.Main.errorPasteMaxRange": "복사 및 붙여넣기 영역이 일치하지 않습니다. <br>같은 크기의 영역을 선택하거나 행의 첫 번째 셀을 클릭하여 복사한 셀을 붙여넣으세요.", "SSE.Controllers.Main.errorPasteMultiSelect": "이 작업은 여러 범위를 선택한 경우에는 사용할 수 없습니다. <br> 단일 범위를 선택하고 다시 시도하십시오.", "SSE.Controllers.Main.errorPasteSlicerError": "테이블 슬라이서는 한 통합 문서에서 다른 통합 문서로 복사할 수 없습니다.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Pivot table field name already exists.", "SSE.Controllers.Main.errorPivotGroup": "그룹화할 수 없음", "SSE.Controllers.Main.errorPivotOverlap": "피벗 보고서가 정해진 범위를 벗어났습니다.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "피벗 테이블은 기본 데이터와 함께 저장되지 않습니다. <br>보고서를 업데이트하려면 \"업데이트\" 버튼을 사용하십시오.", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "선행 항목 추적 명령을 사용하려면 활성 셀에 유효한 참조를 포함하는 수식이 있어야 합니다.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "유감스럽게도 현재 프로그램 버전에서 한 번에 1500 페이지 이상을 인쇄 할 수 없습니다. <br>이 제한 사항은 다음 릴리스에서 제거 될 예정입니다.", "SSE.Controllers.Main.errorProcessSaveResult": "저장 실패", "SSE.Controllers.Main.errorProtectedRange": "이 범위는 편집이 허용되지 않습니다.", "SSE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "SSE.Controllers.Main.errorServerVersion": "편집기 버전이 업데이트되었습니다. 페이지가 다시로드되어 변경 사항이 적용됩니다.", "SSE.Controllers.Main.errorSessionAbsolute": "문서 편집 세션이 만료되었습니다. 페이지를 새로 고침하십시오.", "SSE.Controllers.Main.errorSessionIdle": "문서가 오랫동안 편집되지 않았습니다. 페이지를 새로고침 하십시오.", "SSE.Controllers.Main.errorSessionToken": "서버 연결이 중단되었습니다. 페이지를 새로 고침하십시오.", "SSE.Controllers.Main.errorSetPassword": "비밀번호를 재설정할 수 없습니다.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "셀이 모두 같은 열이나 행에 있지 않기 때문에 위치 참조가 잘못되었습니다. <br>같은 열이나 행에서 셀을 선택하십시오.", "SSE.Controllers.Main.errorStockChart": "잘못된 행 순서. 주식형 차트를 작성하려면 시트의 데이터를 다음과 같은 순서로 배치하십시오 : <br> 개시 가격, 최대 가격, 최소 가격, 마감 가격.", "SSE.Controllers.Main.errorToken": "문서 보안 토큰이 올바르게 구성되지 않았습니다. <br> Document Server 관리자에게 문의하십시오.", "SSE.Controllers.Main.errorTokenExpire": "문서 보안 토큰이 만료되었습니다. <br> Document Server 관리자에게 문의하십시오.", "SSE.Controllers.Main.errorUnexpectedGuid": "외부 오류입니다. <br> 예기치 않은 GUID 오류가 계속 발생하면 지원 담당자에게 문의하십시오.", "SSE.Controllers.Main.errorUpdateVersion": "파일 버전이 변경되었습니다. 페이지가 다시 로드됩니다.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "네트워크 연결이 복원되었으며 파일 버전이 변경되었습니다. <br>계속 작업하기 전에 데이터 손실을 방지하기 위해 파일을 다운로드하거나 내용을 복사한 다음 이 페이지를 새로 고쳐야 합니다.", "SSE.Controllers.Main.errorUserDrop": "파일에 지금 액세스 할 수 없습니다.", "SSE.Controllers.Main.errorUsersExceed": "요금제에서 허용하는 사용자 수 초과", "SSE.Controllers.Main.errorViewerDisconnect": "연결이 끊어졌습니다. 문서를 계속해서 볼 수 있지만,<br>연결이 복원되고 페이지가 다시 로드 될 때까지 다운로드하거나 인쇄할 수 없습니다.", "SSE.Controllers.Main.errorWrongBracketsCount": "입력 된 수식에 오류가 있습니다. <br> 괄호가 잘못 사용되었습니다.", "SSE.Controllers.Main.errorWrongOperator": "입력 한 수식에 오류가 있습니다. 잘못된 연산자가 사용되었습니다. <br> 오류를 수정하십시오.", "SSE.Controllers.Main.errorWrongPassword": "잘못된 비밀번호", "SSE.Controllers.Main.errRemDuplicates": "중복 값이 ​​발견 및 삭제됨: {0}, 남은 고유 값: {1}.", "SSE.Controllers.Main.leavePageText": "이 스프레드 시트에 변경 사항을 저장하지 않았습니다.'이 페이지에 머물기\"를 누르고 '저장'으로 저장하십시오. 저장하지 않은 모든 변경 사항을 무시하려면 '이 페이지 벗어나기'를 클릭하십시오.", "SSE.Controllers.Main.leavePageTextOnClose": "이 문서에 저장되지 않은 모든 변경 사항이 손실됩니다. <br>\"취소\"를 클릭한 다음 \"저장\"을 클릭하여 저장하십시오. 저장되지 않은 모든 변경 사항을 취소하려면 \"확인\"을 클릭하십시오.", "SSE.Controllers.Main.loadFontsTextText": "데이터로드 중 ...", "SSE.Controllers.Main.loadFontsTitleText": "데이터로드 중", "SSE.Controllers.Main.loadFontTextText": "데이터로드 중 ...", "SSE.Controllers.Main.loadFontTitleText": "데이터로드 중", "SSE.Controllers.Main.loadImagesTextText": "이미지로드 중 ...", "SSE.Controllers.Main.loadImagesTitleText": "이미지로드 중", "SSE.Controllers.Main.loadImageTextText": "이미지로드 중 ...", "SSE.Controllers.Main.loadImageTitleText": "이미지로드 중", "SSE.Controllers.Main.loadingDocumentTitleText": "스프레드시트 로드 중", "SSE.Controllers.Main.notcriticalErrorTitle": "경고", "SSE.Controllers.Main.openErrorText": "파일을 여는 동안 오류가 발생했습니다", "SSE.Controllers.Main.openTextText": "스프레드시트 열기 중...", "SSE.Controllers.Main.openTitleText": "스프레드시트 열기", "SSE.Controllers.Main.pastInMergeAreaError": "병합 된 셀의 일부를 변경할 수 없습니다", "SSE.Controllers.Main.printTextText": "스프레드시트 인쇄 중...", "SSE.Controllers.Main.printTitleText": "스프레드시트 인쇄", "SSE.Controllers.Main.reloadButtonText": "Reload Page", "SSE.Controllers.Main.requestEditFailedMessageText": "누군가이 문서를 지금 편집하고 있습니다. 나중에 다시 시도하십시오.", "SSE.Controllers.Main.requestEditFailedTitleText": "액세스가 거부되었습니다", "SSE.Controllers.Main.saveErrorText": "파일을 저장하는 동안 오류가 발생했습니다.", "SSE.Controllers.Main.saveErrorTextDesktop": "이 파일을 저장하거나 생성할 수 없습니다. <br>가능한 이유는 다음과 같습니다. <br> 1. 파일이 읽기 전용입니다. <br> 2. 다른 사용자가 파일을 편집 중입니다. <br> 3. 디스크가 가득 찼거나 손상되었습니다.", "SSE.Controllers.Main.saveTextText": "스프레드시트 저장 중...", "SSE.Controllers.Main.saveTitleText": "스프레드시트 저장 중", "SSE.Controllers.Main.scriptLoadError": "연결 속도가 느려, 일부 요소들이 로드되지 않았습니다. 페이지를 다시 새로 고침해주세요.", "SSE.Controllers.Main.textAnonymous": "익명", "SSE.Controllers.Main.textApplyAll": "모든 방정식에 적용", "SSE.Controllers.Main.textBuyNow": "웹 사이트 방문", "SSE.Controllers.Main.textChangesSaved": "모든 변경 사항이 저장되었습니다", "SSE.Controllers.Main.textClose": "닫기", "SSE.Controllers.Main.textCloseTip": "도움말을 닫으려면 클릭하십시오", "SSE.Controllers.Main.textConfirm": "확인", "SSE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "SSE.Controllers.Main.textContactUs": "영업 담당자에게 문의", "SSE.Controllers.Main.textContinue": "계속", "SSE.Controllers.Main.textConvertEquation": "방정식은 더 이상 지원되지 않는 이전 버전의 방정식 편집기를 사용하여 생성되었습니다. 편집하려면 수식을 Office Math ML 형식으로 변환하세요. <br>지금 변환하시겠습니까?", "SSE.Controllers.Main.textCustomLoader": "라이센스 조건에 따라 교체할 권한이 없습니다. <br>견적은 당사 영업부에 문의해 주십시오.", "SSE.Controllers.Main.textDisconnect": "네트워크 연결 끊김", "SSE.Controllers.Main.textFillOtherRows": "다른 행 채우기", "SSE.Controllers.Main.textFormulaFilledAllRows": "수식이 채워진 {0} 행에 데이터가 있습니다. 다른 빈 행을 채우는 데 몇 분 정도 걸릴 수 있습니다.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "수식이 첫 {0}행을 채웠습니다. 다른 빈 행을 채우는 데 몇 분 정도 걸릴 수 있습니다.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "메모리 절약 이유로 수식은 첫 {0}행만 채웠습니다. 이 시트의 다른 행에는 데이터가 없습니다.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "메모리 절약 이유로 수식은 첫 {0}행만 채웠습니다. 이 시트의 다른 행에는 데이터가 없습니다.", "SSE.Controllers.Main.textGuest": "게스트", "SSE.Controllers.Main.textHasMacros": "파일에 자동 매크로가 포함되어 있습니다. <br> 매크로를 실행 하시겠습니까?", "SSE.Controllers.Main.textKeep": "유지", "SSE.Controllers.Main.textLearnMore": "자세히", "SSE.Controllers.Main.textLoadingDocument": "스프레드시트 로드 중", "SSE.Controllers.Main.textLongName": "128자 미만의 이름을 입력하세요.", "SSE.Controllers.Main.textNeedSynchronize": "업데이트가 있습니다.", "SSE.Controllers.Main.textNo": "No", "SSE.Controllers.Main.textNoLicenseTitle": "라이센스 수를 제한했습니다.", "SSE.Controllers.Main.textPaidFeature": "유료기능", "SSE.Controllers.Main.textPleaseWait": "작업이 예상보다 많은 시간이 걸릴 수 있습니다. 잠시 기다려주십시오 ...", "SSE.Controllers.Main.textReconnect": "연결이 복원되었습니다", "SSE.Controllers.Main.textRemember": "모든 파일에 대한 선택 사항을 기억하기", "SSE.Controllers.Main.textRememberMacros": "모든 매크로에 대한 내 선택 기억", "SSE.Controllers.Main.textRenameError": "사용자 이름은 비워둘 수 없습니다.", "SSE.Controllers.Main.textRenameLabel": "협업에 사용할 이름을 입력합니다", "SSE.Controllers.Main.textReplace": "바꾸기", "SSE.Controllers.Main.textRequestMacros": "매크로에서 URL로 요청합니다. %1에게 요청을 허용하시겠습니까?", "SSE.Controllers.Main.textShape": "도형", "SSE.Controllers.Main.textStrict": "엄격 모드", "SSE.Controllers.Main.textText": "본문", "SSE.Controllers.Main.textTryQuickPrint": "빠른 인쇄를 선택했습니다. 전체 문서가 마지막으로 선택한 프린터 또는 기본 프린터에서 인쇄됩니다.<br>계속하시겠습니까?", "SSE.Controllers.Main.textTryUndoRedo": "빠른 공동 편집 모드에서는 실행 취소 / 다시 실행 기능이 비활성화됩니다. <br>\"엄격 모드 \"버튼을 클릭하면 엄격한 공동 편집 모드로 전환되어 파일을 편집 할 수 있습니다. 다른 사용자가 방해를해서 저장 한 후에 만 ​​변경 사항을 보내면됩니다. 편집자 고급 설정을 사용하여 공동 편집 모드간에 전환 할 수 있습니다. ", "SSE.Controllers.Main.textTryUndoRedoWarn": "빠른 공동 편집 모드에서 실행 취소 / 다시 실행 기능을 사용할 수 없습니다.", "SSE.Controllers.Main.textUndo": "실행 취소", "SSE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "SSE.Controllers.Main.textUpdating": "Updating", "SSE.Controllers.Main.textYes": "예", "SSE.Controllers.Main.titleLicenseExp": "라이센스 만료", "SSE.Controllers.Main.titleLicenseNotActive": "라이선스가 활성화되지 않음", "SSE.Controllers.Main.titleServerVersion": "편집기가 업데이트되었습니다.", "SSE.Controllers.Main.titleUpdateVersion": "Version changed", "SSE.Controllers.Main.txtAccent": "Accent", "SSE.Controllers.Main.txtAll": "(전체)", "SSE.Controllers.Main.txtArt": "여기에 귀하의 텍스트를 입력하여 주십시오", "SSE.Controllers.Main.txtBasicShapes": "기본 도형", "SSE.Controllers.Main.txtBlank": "(빈칸)", "SSE.Controllers.Main.txtButtons": "버튼", "SSE.Controllers.Main.txtByField": "%2의 %1", "SSE.Controllers.Main.txtCallouts": "설명선", "SSE.Controllers.Main.txtCharts": "차트", "SSE.Controllers.Main.txtClearFilter": "필터 지우기", "SSE.Controllers.Main.txtColLbls": "열 라벨", "SSE.Controllers.Main.txtColumn": "열", "SSE.Controllers.Main.txtConfidential": "비밀", "SSE.Controllers.Main.txtDate": "날짜", "SSE.Controllers.Main.txtDays": "일", "SSE.Controllers.Main.txtDiagramTitle": "차트 제목", "SSE.Controllers.Main.txtEditingMode": "편집 모드 설정 ...", "SSE.Controllers.Main.txtErrorLoadHistory": "이력을 로드하지 못했습니다.", "SSE.Controllers.Main.txtFiguredArrows": "그림 화살표", "SSE.Controllers.Main.txtFile": "파일", "SSE.Controllers.Main.txtGrandTotal": "총합계", "SSE.Controllers.Main.txtGroup": "그룹", "SSE.Controllers.Main.txtHours": "시간", "SSE.Controllers.Main.txtInfo": "정보", "SSE.Controllers.Main.txtLines": "선", "SSE.Controllers.Main.txtMath": "수학", "SSE.Controllers.Main.txtMinutes": "분", "SSE.Controllers.Main.txtMonths": "월", "SSE.Controllers.Main.txtMultiSelect": "다중 선택", "SSE.Controllers.Main.txtNone": "없음", "SSE.Controllers.Main.txtOr": "1% 또는 2%", "SSE.Controllers.Main.txtPage": "페이지", "SSE.Controllers.Main.txtPageOf": "전체 %2 중 %1", "SSE.Controllers.Main.txtPages": "페이지", "SSE.Controllers.Main.txtPicture": "그림", "SSE.Controllers.Main.txtPivotTable": "PivotTable", "SSE.Controllers.Main.txtPreparedBy": "편집자", "SSE.Controllers.Main.txtPrintArea": "인쇄 영역", "SSE.Controllers.Main.txtQuarter": "분기", "SSE.Controllers.Main.txtQuarters": "분기", "SSE.Controllers.Main.txtRectangles": "직사각형", "SSE.Controllers.Main.txtRow": "행", "SSE.Controllers.Main.txtRowLbls": "행 레이블", "SSE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "SSE.Controllers.Main.txtScheme_Aspect": "Aspect", "SSE.Controllers.Main.txtScheme_Blue": "Blue", "SSE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "SSE.Controllers.Main.txtScheme_Blue_II": "Blue II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "SSE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "SSE.Controllers.Main.txtScheme_Green": "Green", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "SSE.Controllers.Main.txtScheme_Marquee": "Marquee", "SSE.Controllers.Main.txtScheme_Median": "Median", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Orange", "SSE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "SSE.Controllers.Main.txtScheme_Paper": "Paper", "SSE.Controllers.Main.txtScheme_Red": "Red", "SSE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "SSE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "SSE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "SSE.Controllers.Main.txtScheme_Violet": "Violet", "SSE.Controllers.Main.txtScheme_Violet_II": "Violet II", "SSE.Controllers.Main.txtScheme_Yellow": "Yellow", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "SSE.Controllers.Main.txtSeconds": "초", "SSE.Controllers.Main.txtSeries": "Series", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "설명선 1 (테두리 강조)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "설명선 2 (테두리 강조)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "설명선 3 (테두리 강조)", "SSE.Controllers.Main.txtShape_accentCallout1": "설명선 1 (강조선)", "SSE.Controllers.Main.txtShape_accentCallout2": "설명선 2 (강조선)", "SSE.Controllers.Main.txtShape_accentCallout3": "설명선 3 (강조선)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "되돌리기 또는 이전 버튼", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "시작 버튼", "SSE.Controllers.Main.txtShape_actionButtonBlank": "공백 버튼", "SSE.Controllers.Main.txtShape_actionButtonDocument": "문서 버튼", "SSE.Controllers.Main.txtShape_actionButtonEnd": "종료 버튼", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "다음 버튼", "SSE.Controllers.Main.txtShape_actionButtonHelp": "도움말 버튼", "SSE.Controllers.Main.txtShape_actionButtonHome": "홈 버튼", "SSE.Controllers.Main.txtShape_actionButtonInformation": "상세정보 버튼", "SSE.Controllers.Main.txtShape_actionButtonMovie": "동영상 버튼", "SSE.Controllers.Main.txtShape_actionButtonReturn": "뒤로가기 버튼", "SSE.Controllers.Main.txtShape_actionButtonSound": "소리 버튼", "SSE.Controllers.Main.txtShape_arc": "호", "SSE.Controllers.Main.txtShape_bentArrow": "구부러진 화살", "SSE.Controllers.Main.txtShape_bentConnector5": "연결선: 꺾임", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "연결선: 꺾인 화살표", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "연결선: 꺾인 양쪽 화살표", "SSE.Controllers.Main.txtShape_bentUpArrow": "위로 구부러진 화살", "SSE.Controllers.Main.txtShape_bevel": "사선", "SSE.Controllers.Main.txtShape_blockArc": "닫힌 호", "SSE.Controllers.Main.txtShape_borderCallout1": "설명선 1", "SSE.Controllers.Main.txtShape_borderCallout2": "설명선 2", "SSE.Controllers.Main.txtShape_borderCallout3": "설명선 3", "SSE.Controllers.Main.txtShape_bracePair": "양쪽 중괄호", "SSE.Controllers.Main.txtShape_callout1": "설명선 1 (테두리없음)", "SSE.Controllers.Main.txtShape_callout2": "설명선 2 (테두리없음)", "SSE.Controllers.Main.txtShape_callout3": "설명선 3 (테두리없음)", "SSE.Controllers.Main.txtShape_can": "원통형", "SSE.Controllers.Main.txtShape_chevron": "쉐브론", "SSE.Controllers.Main.txtShape_chord": "현", "SSE.Controllers.Main.txtShape_circularArrow": "화살표: 원형", "SSE.Controllers.Main.txtShape_cloud": "클라우드", "SSE.Controllers.Main.txtShape_cloudCallout": "생각풍선: 구름 모양", "SSE.Controllers.Main.txtShape_corner": "L도형", "SSE.Controllers.Main.txtShape_cube": "정육면체", "SSE.Controllers.Main.txtShape_curvedConnector3": "연결선: 구부러짐", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "연결선: 구부러진 화살표", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "연결선: 구부러진 양쪽 화살표", "SSE.Controllers.Main.txtShape_curvedDownArrow": "화살표: 아래로 구불어 짐", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "화살표: 왼쪽으로 구불어 짐", "SSE.Controllers.Main.txtShape_curvedRightArrow": "화살표: 오른쪽으로 구불어 짐", "SSE.Controllers.Main.txtShape_curvedUpArrow": "화살표: 위로 구불어 짐", "SSE.Controllers.Main.txtShape_decagon": "십각형", "SSE.Controllers.Main.txtShape_diagStripe": "대각선 줄무늬", "SSE.Controllers.Main.txtShape_diamond": "다이아몬드", "SSE.Controllers.Main.txtShape_dodecagon": "십이각형", "SSE.Controllers.Main.txtShape_donut": "도넛", "SSE.Controllers.Main.txtShape_doubleWave": "이중 물결", "SSE.Controllers.Main.txtShape_downArrow": "화살표: 아래쪽", "SSE.Controllers.Main.txtShape_downArrowCallout": "설명선: 아래쪽 화살표", "SSE.Controllers.Main.txtShape_ellipse": "타원형", "SSE.Controllers.Main.txtShape_ellipseRibbon": "리본: 아래로 구불어지고 기울어짐 ", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "리본: 위로 구불어지고 기울어짐 ", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "순서도: 대체 프로세스", "SSE.Controllers.Main.txtShape_flowChartCollate": "순서도: 일치", "SSE.Controllers.Main.txtShape_flowChartConnector": "순서도: 연결 연산자", "SSE.Controllers.Main.txtShape_flowChartDecision": "순서도: 결정", "SSE.Controllers.Main.txtShape_flowChartDelay": "순서도: 지연", "SSE.Controllers.Main.txtShape_flowChartDisplay": "순서도: 표시", "SSE.Controllers.Main.txtShape_flowChartDocument": "순서도: 문서", "SSE.Controllers.Main.txtShape_flowChartExtract": "순서도: 추출", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "순서도: 데이터", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "순서도: 내부 스토리지", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "순서도: 디스크", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "순서도: 스토리지에 직접 접근", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "순서도: 순차 접근 스토리지", "SSE.Controllers.Main.txtShape_flowChartManualInput": "순서도: 수동 입력", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "순서도: 수동조작", "SSE.Controllers.Main.txtShape_flowChartMerge": "순서도: 병합", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "순서도: 다중문서", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "순서도: 페이지 외부 커넥터", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "순서도: 저장된 데이터", "SSE.Controllers.Main.txtShape_flowChartOr": "순서도: 또는", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "순서도: 미리 정의된 흐름", "SSE.Controllers.Main.txtShape_flowChartPreparation": "순서도: 준비", "SSE.Controllers.Main.txtShape_flowChartProcess": "순서도: 프로세스", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "순서도: 카드", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "순서도: 천공된 종이 테이프", "SSE.Controllers.Main.txtShape_flowChartSort": "순서도: 정렬", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "순서도: 합계 노드", "SSE.Controllers.Main.txtShape_flowChartTerminator": "순서도: 종료", "SSE.Controllers.Main.txtShape_foldedCorner": "접힌 모서리", "SSE.Controllers.Main.txtShape_frame": "프레임", "SSE.Controllers.Main.txtShape_halfFrame": "1/2 액자", "SSE.Controllers.Main.txtShape_heart": "하트모양", "SSE.Controllers.Main.txtShape_heptagon": "칠각형", "SSE.Controllers.Main.txtShape_hexagon": "육각형", "SSE.Controllers.Main.txtShape_homePlate": "오각형", "SSE.Controllers.Main.txtShape_horizontalScroll": "두루마리 모양: 가로로 말림", "SSE.Controllers.Main.txtShape_irregularSeal1": "폭발: 8pt", "SSE.Controllers.Main.txtShape_irregularSeal2": "폭발: 14pt", "SSE.Controllers.Main.txtShape_leftArrow": "화살표: 왼쪽", "SSE.Controllers.Main.txtShape_leftArrowCallout": "설명선: 왼쪽 화살표", "SSE.Controllers.Main.txtShape_leftBrace": "왼쪽 중괄호", "SSE.Controllers.Main.txtShape_leftBracket": "왼쪽 대괄호", "SSE.Controllers.Main.txtShape_leftRightArrow": "선 화살표 : 양방향", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "설명선: 왼쪽 및 오른쪽 화살표", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "화살표: 왼쪽/위쪽", "SSE.Controllers.Main.txtShape_leftUpArrow": "화살표: 왼쪽", "SSE.Controllers.Main.txtShape_lightningBolt": "번개", "SSE.Controllers.Main.txtShape_line": "선", "SSE.Controllers.Main.txtShape_lineWithArrow": "화살표", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "선 화살표: 양방향", "SSE.Controllers.Main.txtShape_mathDivide": "배분", "SSE.Controllers.Main.txtShape_mathEqual": "등호", "SSE.Controllers.Main.txtShape_mathMinus": "뺄셈", "SSE.Controllers.Main.txtShape_mathMultiply": "곱셈", "SSE.Controllers.Main.txtShape_mathNotEqual": "부등호", "SSE.Controllers.Main.txtShape_mathPlus": "덧셈", "SSE.Controllers.Main.txtShape_moon": "달모양", "SSE.Controllers.Main.txtShape_noSmoking": "\"없음\" 기호", "SSE.Controllers.Main.txtShape_notchedRightArrow": "화살표: 오른쪽 톱니 모양", "SSE.Controllers.Main.txtShape_octagon": "팔각형", "SSE.Controllers.Main.txtShape_parallelogram": "평행 사변형", "SSE.Controllers.Main.txtShape_pentagon": "오각형", "SSE.Controllers.Main.txtShape_pie": "부분 원형", "SSE.Controllers.Main.txtShape_plaque": "배지", "SSE.Controllers.Main.txtShape_plus": "덧셈", "SSE.Controllers.Main.txtShape_polyline1": "자유형: 자유 곡선", "SSE.Controllers.Main.txtShape_polyline2": "자유형: 도형", "SSE.Controllers.Main.txtShape_quadArrow": "화살표: 왼쪽/오른쪽/위쪽/아래쪽", "SSE.Controllers.Main.txtShape_quadArrowCallout": "설명선: 왼쪽/오른쪽/위쪽/아래쪽", "SSE.Controllers.Main.txtShape_rect": "사각형", "SSE.Controllers.Main.txtShape_ribbon": "리본: 아래로 기울어짐", "SSE.Controllers.Main.txtShape_ribbon2": "리본: 위로 구불어짐", "SSE.Controllers.Main.txtShape_rightArrow": "화살표: 오른쪽", "SSE.Controllers.Main.txtShape_rightArrowCallout": "설명선: 오른쪽 화살표", "SSE.Controllers.Main.txtShape_rightBrace": "오른쪽 중괄호", "SSE.Controllers.Main.txtShape_rightBracket": "오른쪽 대괄호", "SSE.Controllers.Main.txtShape_round1Rect": "사각형: 둥근 한쪽 모서리", "SSE.Controllers.Main.txtShape_round2DiagRect": "사각형: 둥근 대각선 방향 모서리", "SSE.Controllers.Main.txtShape_round2SameRect": "사각형: 둥근 위쪽 모서리", "SSE.Controllers.Main.txtShape_roundRect": "사각형: 둥근 모서리", "SSE.Controllers.Main.txtShape_rtTriangle": "직각 삼각형", "SSE.Controllers.Main.txtShape_smileyFace": "웃는 얼굴", "SSE.Controllers.Main.txtShape_snip1Rect": "사각형: 잘린 한쪽 모서리", "SSE.Controllers.Main.txtShape_snip2DiagRect": "사각형: 잘린 대각선 방향 모서리", "SSE.Controllers.Main.txtShape_snip2SameRect": "사각형: 잘린 양쪽 모서리", "SSE.Controllers.Main.txtShape_snipRoundRect": "사각형: 한쪽은 둥글고 한쪽은 짤린 모서리", "SSE.Controllers.Main.txtShape_spline": "곡선", "SSE.Controllers.Main.txtShape_star10": "별: 꼭짓점 10개", "SSE.Controllers.Main.txtShape_star12": "별: 꼭짓점 12개", "SSE.Controllers.Main.txtShape_star16": "별: 꼭짓점 16개", "SSE.Controllers.Main.txtShape_star24": "별: 꼭짓점 24개", "SSE.Controllers.Main.txtShape_star32": "별: 꼭짓점 32개", "SSE.Controllers.Main.txtShape_star4": "별: 꼭짓점 4개", "SSE.Controllers.Main.txtShape_star5": "별: 꼭짓점 5개", "SSE.Controllers.Main.txtShape_star6": "별: 꼭짓점 6개", "SSE.Controllers.Main.txtShape_star7": "별: 꼭짓점 7개", "SSE.Controllers.Main.txtShape_star8": "8-포인트 크기 별", "SSE.Controllers.Main.txtShape_stripedRightArrow": "줄무늬 오른쪽 화살표", "SSE.Controllers.Main.txtShape_sun": "해모양", "SSE.Controllers.Main.txtShape_teardrop": "눈물 방울", "SSE.Controllers.Main.txtShape_textRect": "텍스트 상자", "SSE.Controllers.Main.txtShape_trapezoid": "사다리꼴", "SSE.Controllers.Main.txtShape_triangle": "삼각형", "SSE.Controllers.Main.txtShape_upArrow": "화살표: 위쪽", "SSE.Controllers.Main.txtShape_upArrowCallout": "설명선: 위쪽 화살표", "SSE.Controllers.Main.txtShape_upDownArrow": "화살표: 위쪽/아래쪽", "SSE.Controllers.Main.txtShape_uturnArrow": "화살표: U자형", "SSE.Controllers.Main.txtShape_verticalScroll": "두루마리 모양: 세로로 말림", "SSE.Controllers.Main.txtShape_wave": "물결", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "말풍선: 타원형", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "말풍선: 사각형", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "말풍선: 모서리가 둥근 사각형", "SSE.Controllers.Main.txtSheet": "시트", "SSE.Controllers.Main.txtSlicer": "슬라이서", "SSE.Controllers.Main.txtStarsRibbons": "별 및 현수막", "SSE.Controllers.Main.txtStyle_Bad": "Bad", "SSE.Controllers.Main.txtStyle_Calculation": "계산", "SSE.Controllers.Main.txtStyle_Check_Cell": "셀 검사", "SSE.Controllers.Main.txtStyle_Comma": "쉼표", "SSE.Controllers.Main.txtStyle_Currency": "통화", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "설명 텍스트", "SSE.Controllers.Main.txtStyle_Good": "Good", "SSE.Controllers.Main.txtStyle_Heading_1": "제목 1", "SSE.Controllers.Main.txtStyle_Heading_2": "제목 2", "SSE.Controllers.Main.txtStyle_Heading_3": "제목 3", "SSE.Controllers.Main.txtStyle_Heading_4": "제목 4", "SSE.Controllers.Main.txtStyle_Input": "입력", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Linked Cell", "SSE.Controllers.Main.txtStyle_Neutral": "Neutral", "SSE.Controllers.Main.txtStyle_Normal": "일반", "SSE.Controllers.Main.txtStyle_Note": "참고", "SSE.Controllers.Main.txtStyle_Output": "출력", "SSE.Controllers.Main.txtStyle_Percent": "Percent", "SSE.Controllers.Main.txtStyle_Title": "제목", "SSE.Controllers.Main.txtStyle_Total": "합계", "SSE.Controllers.Main.txtStyle_Warning_Text": "경고문", "SSE.Controllers.Main.txtTab": "탭", "SSE.Controllers.Main.txtTable": "표", "SSE.Controllers.Main.txtTime": "시간", "SSE.Controllers.Main.txtUnlock": "잠금해제", "SSE.Controllers.Main.txtUnlockRange": "범위해제", "SSE.Controllers.Main.txtUnlockRangeDescription": "범위를 변경하려면 비밀번호를 입력하세요 :", "SSE.Controllers.Main.txtUnlockRangeWarning": "변경하려는 범위는 암호로 보호됩니다.", "SSE.Controllers.Main.txtValues": "값", "SSE.Controllers.Main.txtView": "보기", "SSE.Controllers.Main.txtXAxis": "X 축", "SSE.Controllers.Main.txtYAxis": "Y 축", "SSE.Controllers.Main.txtYears": "년", "SSE.Controllers.Main.unknownErrorText": "알 수없는 오류.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "사용 중인 브라우저가 지원되지 않습니다.", "SSE.Controllers.Main.uploadDocExtMessage": "알 수 없는 파일 형식입니다.", "SSE.Controllers.Main.uploadDocFileCountMessage": "업로드 된 문서가 없습니다.", "SSE.Controllers.Main.uploadDocSizeMessage": "최대 문서 크기 제한을 초과했습니다.", "SSE.Controllers.Main.uploadImageExtMessage": "알 수없는 이미지 형식입니다.", "SSE.Controllers.Main.uploadImageFileCountMessage": "이미지가 업로드되지 않았습니다.", "SSE.Controllers.Main.uploadImageSizeMessage": "이미지 크기 제한을 초과했습니다.", "SSE.Controllers.Main.uploadImageTextText": "이미지 업로드 중 ...", "SSE.Controllers.Main.uploadImageTitleText": "이미지 업로드 중", "SSE.Controllers.Main.waitText": "잠시만 기다려주세요...", "SSE.Controllers.Main.warnBrowserIE9": "응용 프로그램의 기능이 IE9에서 부족합니다. IE10 이상을 사용하십시오.", "SSE.Controllers.Main.warnBrowserZoom": "브라우저의 현재 확대/축소 설정이 완전히 지원되지 않습니다. Ctrl + 0을 눌러 기본 확대 / 축소로 재설정하십시오.", "SSE.Controllers.Main.warnLicenseAnonymous": "익명 사용자에 대한 접근이 거부되었습니다.<br>이 문서는 보기 전용으로 열립니다.", "SSE.Controllers.Main.warnLicenseBefore": "라이센스가 활성화되지 않았습니다.<br>관리자에게 문의하세요.", "SSE.Controllers.Main.warnLicenseExceeded": "귀하의 시스템은 동시에 연결을 편집하는 %1명의 편집자에게 도달했습니다. 이 문서는 보기 모드에서만 열 수 있습니다. <br> 자세한 내용은 관리자에게 문의하십시오.", "SSE.Controllers.Main.warnLicenseExp": "귀하의 라이센스가 만료되었습니다. <br> 라이센스를 갱신하고 페이지를 새로 고침하십시오.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "라이센스가 만료되었습니다.<br>더 이상 파일을 수정할 수 있는 권한이 없습니다.<br> 관리자에게 문의하세요.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "라이센스를 갱신해야합니다. <br> 문서 편집 기능에 대한 액세스가 제한되어 있습니다. <br> 전체 액세스 권한을 얻으려면 관리자에게 문의하십시오", "SSE.Controllers.Main.warnLicenseUsersExceeded": "편집자 사용자 한도인 %1명에 도달했습니다. 자세한 내용은 관리자에게 문의하십시오.", "SSE.Controllers.Main.warnNoLicense": "이 버전의 %1 편집자는 문서 서버에 대한 동시 연결에 특정 제한 사항이 있습니다. <br> 더 많은 정보가 필요하면 현재 라이센스를 업그레이드하거나 상업용 라이센스를 구입하십시오.", "SSE.Controllers.Main.warnNoLicenseUsers": "이 버전의 %1 편집자에게는 동시 사용자에게 특정 제한 사항이 있습니다. <br> 더 필요한 것이 있으면 현재 라이센스를 업그레이드하거나 상업용 라이센스를 구입하십시오.", "SSE.Controllers.Main.warnProcessRightsChange": "파일 편집 권한이 거부되었습니다.", "SSE.Controllers.PivotTable.strSheet": "시트", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "No actions with calculated items are allowed for this active cell.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Calculated items do not work with custom subtotals.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "An item name cannot be found. Check that you've typed name correctly and the item is present in the PivotTable report.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Averages, standard deviations, and variances are not supported when a PivotTable report has calculated items.", "SSE.Controllers.Print.strAllSheets": "모든 시트", "SSE.Controllers.Print.textFirstCol": "첫째 열", "SSE.Controllers.Print.textFirstRow": "머리글 행", "SSE.Controllers.Print.textFrozenCols": "고정 된 열", "SSE.Controllers.Print.textFrozenRows": "고정된 행", "SSE.Controllers.Print.textInvalidRange": "오류! 셀 범위가 잘못되었습니다.", "SSE.Controllers.Print.textNoRepeat": "반복 없음", "SSE.Controllers.Print.textRepeat": "반복...", "SSE.Controllers.Print.textSelectRange": "범위 선택", "SSE.Controllers.Print.txtCustom": "사용자 정의", "SSE.Controllers.Print.txtZoomToPage": "Zoom to page", "SSE.Controllers.Search.textInvalidRange": "오류! 셀 범위가 잘못되었습니다.", "SSE.Controllers.Search.textNoTextFound": "검색 한 데이터를 찾을 수 없습니다. 검색 옵션을 조정하십시오.", "SSE.Controllers.Search.textReplaceSkipped": "대체가 이루어졌습니다. {0} 건은 건너 뛰었습니다.", "SSE.Controllers.Search.textReplaceSuccess": "검색이 완료되었습니다. {0}번의 항목이 대체되었습니다.", "SSE.Controllers.Statusbar.errorLastSheet": "통합 문서에는 최소한 하나의 보이는 워크 시트가 있어야합니다.", "SSE.Controllers.Statusbar.errorRemoveSheet": "워크 시트를 삭제할 수 없습니다.", "SSE.Controllers.Statusbar.strSheet": "시트", "SSE.Controllers.Statusbar.textDisconnect": "<b>연결이 끊어졌습니다</b><br>연결을 시도하는 중입니다.", "SSE.Controllers.Statusbar.textSheetViewTip": "시트보기 모드입니다. 필터 및 정렬은 나와 이 보기에 있는 사용자만 볼 수 있습니다.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "시트 보기 모드에 있습니다. 필터는 나와 이 보기에 있는 사람들만 볼 수 있습니다.", "SSE.Controllers.Statusbar.warnDeleteSheet": "워크 시트에 데이터가 있을 수 있습니다. 작업을 계속 하시겠습니까?", "SSE.Controllers.Statusbar.zoomText": "확대/축소 {0} %", "SSE.Controllers.Toolbar.confirmAddFontName": "저장하려는 글꼴을 현재 장치에서 사용할 수 없습니다. <br> 시스템 글꼴 중 하나를 사용하여 텍스트 스타일이 표시되고 저장된 글꼴은 사용할 수 있습니다. <br> 계속 하시겠습니까? ", "SSE.Controllers.Toolbar.errorComboSeries": "혼합형 차트를 만들려면 최소 2 개의 데이터를 선택합니다.", "SSE.Controllers.Toolbar.errorMaxPoints": "차트당 시리즈내 포인트의 최대값은 4096임.", "SSE.Controllers.Toolbar.errorMaxRows": "오류! 차트 당 최대 데이터 시리즈 수는 255입니다.", "SSE.Controllers.Toolbar.errorStockChart": "잘못된 행 순서. 주식형 차트를 작성하려면 시트에 데이터를 다음과 같은 순서로 배치하십시오 : <br> 개시 가격, 최대 가격, 최소 가격, 마감 가격.", "SSE.Controllers.Toolbar.helpCalcItems": "Work with calculated items in Pivot Tables.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Calculated items", "SSE.Controllers.Toolbar.helpFastUndo": "Easily undo changes while collaborating on sheets in Fast mode.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "\"Undo\" in real-time co-editing", "SSE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "SSE.Controllers.Toolbar.textAccent": "악센트", "SSE.Controllers.Toolbar.textBracket": "대괄호", "SSE.Controllers.Toolbar.textDirectional": "방향", "SSE.Controllers.Toolbar.textFontSizeErr": "입력 한 값이 잘못되었습니다. <br> 1 ~ 409 사이의 숫자 값을 입력하십시오.", "SSE.Controllers.Toolbar.textFraction": "분수", "SSE.Controllers.Toolbar.textFunction": "함수", "SSE.Controllers.Toolbar.textIndicator": "지표", "SSE.Controllers.Toolbar.textInsert": "삽입", "SSE.Controllers.Toolbar.textIntegral": "적분", "SSE.Controllers.Toolbar.textLargeOperator": "대형 연산자", "SSE.Controllers.Toolbar.textLimitAndLog": "극한 및 로그", "SSE.Controllers.Toolbar.textLongOperation": "긴 작업", "SSE.Controllers.Toolbar.textMatrix": "행렬", "SSE.Controllers.Toolbar.textOperator": "연산자", "SSE.Controllers.Toolbar.textPivot": "피벗 테이블", "SSE.Controllers.Toolbar.textRadical": "근호", "SSE.Controllers.Toolbar.textRating": "평가", "SSE.Controllers.Toolbar.textRecentlyUsed": "최근 사용된", "SSE.Controllers.Toolbar.textScript": "첨자", "SSE.Controllers.Toolbar.textShapes": "도형", "SSE.Controllers.Toolbar.textSymbols": "기호", "SSE.Controllers.Toolbar.textWarning": "경고", "SSE.Controllers.Toolbar.txtAccent_Accent": "급성", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "오른쪽 위 왼쪽 화살표 위", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "왼쪽 위 화살표", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "오른쪽 위 화살표 위", "SSE.Controllers.Toolbar.txtAccent_Bar": "Bar", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Underbar", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Overbar", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "박스형 수식 (자리 표시 자 포함)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "상자화 된 수식 (예)", "SSE.Controllers.Toolbar.txtAccent_Check": "확인", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "아래쪽 중괄호", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "위쪽 중괄호", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "벡터 A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC with Overbar", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y Overbar", "SSE.Controllers.Toolbar.txtAccent_DDDot": "트리플 도트", "SSE.Controllers.Toolbar.txtAccent_DDot": "Double Dot", "SSE.Controllers.Toolbar.txtAccent_Dot": "Dot", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Double Overbar", "SSE.Controllers.Toolbar.txtAccent_Grave": "Grave", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "아래의 문자 그룹화", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "위의 문자 그룹화", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Leftwards Harpoon Above", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Rightwards Harpoon Above", "SSE.Controllers.Toolbar.txtAccent_Hat": "Hat", "SSE.Controllers.Toolbar.txtAccent_Smile": "Breve", "SSE.Controllers.Toolbar.txtAccent_Tilde": "물결표", "SSE.Controllers.Toolbar.txtBracket_Angle": "대괄호", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "구분 기호가있는 대괄호", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "구분 기호가있는 대괄호", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "단일 브라켓", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "단일 브래킷", "SSE.Controllers.Toolbar.txtBracket_Curve": "대괄호", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "구분 기호가있는 대괄호", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "단일 대괄호", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "단일 대괄호", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "사례 (두 조건)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "사례 (세 조건)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Stack Object", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Stack Object", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "사례 사례", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Binomial Coefficient", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Binomial Coefficient", "SSE.Controllers.Toolbar.txtBracket_Line": "대괄호", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "단일 대괄호", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "단일 브래킷", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "대괄호", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "단일 브래킷", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "단일 대괄호", "SSE.Controllers.Toolbar.txtBracket_LowLim": "대괄호", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "단일 대괄호", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "단일 브래킷", "SSE.Controllers.Toolbar.txtBracket_Round": "대괄호", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "구분 기호가있는 대괄호", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "단일 대괄호", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "단일 대괄호", "SSE.Controllers.Toolbar.txtBracket_Square": "대괄호", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "대괄호", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "대괄호", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "단일 대괄호", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "단일 대괄호", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "대괄호", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "대괄호", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "단일 대괄호", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "단일 대괄호", "SSE.Controllers.Toolbar.txtBracket_UppLim": "대괄호", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "단일 대괄호", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "단일 대괄호", "SSE.Controllers.Toolbar.txtDeleteCells": "셀 삭제", "SSE.Controllers.Toolbar.txtExpand": "확장 및 정렬", "SSE.Controllers.Toolbar.txtExpandSort": "선택 영역 옆의 데이터는 정렬되지 않습니다. 인접한 데이터를 포함하도록 선택 영역을 확장 하시겠습니까? 아니면 현재 선택된 셀만 정렬할까요?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Skewed Fraction", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Differential", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Differential", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Differential", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Differential", "SSE.Controllers.Toolbar.txtFractionHorizontal": "선형 분수", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi Over 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Small Fraction", "SSE.Controllers.Toolbar.txtFractionVertical": "누적분수", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "역 코사인 함수", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "쌍곡선 역 코사인 함수", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "역 코탄젠트 함수", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "쌍곡선 역방향 코탄젠트 함수", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Inverse Cosecant Function", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "쌍곡선 반전 보조 함수", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "역 분개 함수", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "쌍곡선 역 차감 함수", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "역 사인 함수", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "쌍곡선 역 사인 함수", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "역 탄젠트 함수", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "쌍곡선 역 탄젠트 함수", "SSE.Controllers.Toolbar.txtFunction_Cos": "코사인 함수", "SSE.Controllers.Toolbar.txtFunction_Cosh": "쌍곡선 코사인 함수", "SSE.Controllers.Toolbar.txtFunction_Cot": "코탄 센트 함수", "SSE.Controllers.Toolbar.txtFunction_Coth": "쌍곡선 코탄 센트 함수", "SSE.Controllers.Toolbar.txtFunction_Csc": "Cosecant 함수", "SSE.Controllers.Toolbar.txtFunction_Csch": "쌍곡선 보조 함수", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sine theta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Tangent formula", "SSE.Controllers.Toolbar.txtFunction_Sec": "Secant 함수", "SSE.Controllers.Toolbar.txtFunction_Sech": "쌍곡선 시컨트 함수", "SSE.Controllers.Toolbar.txtFunction_Sin": "사인 함수", "SSE.Controllers.Toolbar.txtFunction_Sinh": "쌍곡선 사인 함수", "SSE.Controllers.Toolbar.txtFunction_Tan": "Tangent Function", "SSE.Controllers.Toolbar.txtFunction_Tanh": "쌍곡선 탄젠트 함수", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "사용자 정의", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "데이터와 모델", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "좋음, 나쁨, 중립", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "이름 없음", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "숫자 형식", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "테마 기반 셀 스타일", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "제목 및 표제", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "사용자 정의", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "어두운", "SSE.Controllers.Toolbar.txtGroupTable_Light": "밝은", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "중", "SSE.Controllers.Toolbar.txtInsertCells": "셀 삽입", "SSE.Controllers.Toolbar.txtIntegral": "Integral", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Differential theta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Differential x", "SSE.Controllers.Toolbar.txtIntegral_dy": "차등 y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral", "SSE.Controllers.Toolbar.txtIntegralDouble": "Double Integral", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Double Integral", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Double Integral", "SSE.Controllers.Toolbar.txtIntegralOriented": "윤곽선 적분", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "윤곽선 적분", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Surface Integral", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Surface Integral", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "표면 적분", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "윤곽선 적분", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "볼륨 정수", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "볼륨 정수", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "볼륨 정수", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integral", "SSE.Controllers.Toolbar.txtIntegralTriple": "Triple Integral", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Triple Integral", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Triple Integral", "SSE.Controllers.Toolbar.txtInvalidRange": "오류! 셀 범위가 잘못되었습니다.", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "쇄기꼴", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "쇄기꼴", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "쇄기꼴", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "쇄기꼴", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "쇄기꼴", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Co-Product", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Co-Product", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Co-Product", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Co-Product", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Co-Product", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summation", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summation", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Summation", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Product", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "교차점", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "교차점", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "교차점", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "교차점", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "교차점", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Product", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Product", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Product", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Product", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Product", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Summation", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Summation", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summation", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summation", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summation", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Union", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Limit Example", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "최대 예", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "제한", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "자연 로그", "SSE.Controllers.Toolbar.txtLimitLog_Log": "로그", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "로그", "SSE.Controllers.Toolbar.txtLimitLog_Max": "최대값", "SSE.Controllers.Toolbar.txtLimitLog_Min": "최소값", "SSE.Controllers.Toolbar.txtLockSort": "선택의 범위 근처에 데이터가 존재 하지만이 셀을 변경하려면 충분한 권한이 없습니다. <br> 선택의 범위를 계속 하시겠습니까?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 빈 행렬", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 빈 행렬", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 빈 행렬", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 빈 행렬", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "대괄호가있는 빈 행렬", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "대괄호가있는 빈 행렬", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "대괄호가있는 빈 행렬", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "괄호로 빈 행렬", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 빈 행렬", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 빈 행렬", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 빈 행렬", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 빈 행렬", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "기준점", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Midline Dots", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "대각선 점", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "수직 점", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "스파 스 매트릭스", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Sparse Matrix", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Identity Matrix", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 Identity Matrix", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 Identity Matrix", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 Identity Matrix", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "오른쪽 아래 화살표", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "오른쪽 위 왼쪽 화살표", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "왼쪽 아래쪽 화살표", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "왼쪽 위 화살표", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "오른쪽 아래 화살표", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "오른쪽 위 화살표 위", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "콜론 균등", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "수익률", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta Yields", "SSE.Controllers.Toolbar.txtOperator_Definition": "정의에 의한 동일", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta Equal To", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "오른쪽 아래 화살표", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "오른쪽 위 왼쪽 화살표", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "왼쪽 아래쪽 화살표", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "왼쪽 위 화살표", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "오른쪽 아래 화살표", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "오른쪽 위 화살표 위", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Equal Equal", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Equal", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "측정 기준", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Radical", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Radical", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "학위가있는 제곱근", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Cubic Root", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Degree와 함께 급진적", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Square Root", "SSE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "Subscript", "SSE.Controllers.Toolbar.txtScriptSubSup": "Subscript-Superscript", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "LeftSubscript-Superscript", "SSE.Controllers.Toolbar.txtScriptSup": "Superscript", "SSE.Controllers.Toolbar.txtSorting": "정렬", "SSE.Controllers.Toolbar.txtSortSelected": "정렬 선택", "SSE.Controllers.Toolbar.txtSymbol_about": "대략", "SSE.Controllers.Toolbar.txtSymbol_additional": "Complement", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "SSE.Controllers.Toolbar.txtSymbol_approx": "거의 동일", "SSE.Controllers.Toolbar.txtSymbol_ast": "별표 연산자", "SSE.Controllers.Toolbar.txtSymbol_beta": "베타", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "글 머리 기호 연산자", "SSE.Controllers.Toolbar.txtSymbol_cap": "교차점", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "큐브 루트", "SSE.Controllers.Toolbar.txtSymbol_cdots": "중간 말줄임표", "SSE.Controllers.Toolbar.txtSymbol_celsius": "Degrees Celsius", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "대략 같음", "SSE.Controllers.Toolbar.txtSymbol_cup": "Union", "SSE.Controllers.Toolbar.txtSymbol_ddots": "오른쪽 아래 대각선 줄임표", "SSE.Controllers.Toolbar.txtSymbol_degree": "도", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Division Sign", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "화살표: 아래쪽", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "빈 세트", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "등호", "SSE.Controllers.Toolbar.txtSymbol_equiv": "동일 함", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "존재 함", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Factorial", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "화씨", "SSE.Controllers.Toolbar.txtSymbol_forall": "모두에게", "SSE.Controllers.Toolbar.txtSymbol_gamma": "감마", "SSE.Controllers.Toolbar.txtSymbol_geq": "크거나 같음", "SSE.Controllers.Toolbar.txtSymbol_gg": "훨씬 더 큼", "SSE.Controllers.Toolbar.txtSymbol_greater": "보다 큼", "SSE.Controllers.Toolbar.txtSymbol_in": "Element Of", "SSE.Controllers.Toolbar.txtSymbol_inc": "Increment", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Infinity", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "화살표: 왼쪽", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "화살표: 왼쪽/오른쪽", "SSE.Controllers.Toolbar.txtSymbol_leq": "작거나 같음", "SSE.Controllers.Toolbar.txtSymbol_less": "보다 작음", "SSE.Controllers.Toolbar.txtSymbol_ll": "훨씬 적습니다", "SSE.Controllers.Toolbar.txtSymbol_minus": "Minus", "SSE.Controllers.Toolbar.txtSymbol_mp": "마이너스 플러스", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "같지 않음", "SSE.Controllers.Toolbar.txtSymbol_ni": "구성원으로 포함", "SSE.Controllers.Toolbar.txtSymbol_not": "부호 없음", "SSE.Controllers.Toolbar.txtSymbol_notexists": "존재하지 않습니다", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "부분 미분", "SSE.Controllers.Toolbar.txtSymbol_percent": "백분율", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus Minus", "SSE.Controllers.Toolbar.txtSymbol_propto": "Proportional To", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "네 번째 루트", "SSE.Controllers.Toolbar.txtSymbol_qed": "End of Proof", "SSE.Controllers.Toolbar.txtSymbol_rddots": "오른쪽 위 대각선 줄임표", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "화살표: 오른쪽", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Radical Sign", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "그러므로", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "곱셈 기호", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "화살표: 위쪽", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon Variant", "SSE.Controllers.Toolbar.txtSymbol_varphi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma Variant", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_vdots": "수직 줄임표", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "어두운 표 스타일", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "밝은 표 스타일", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "중간 표 스타일", "SSE.Controllers.Toolbar.warnLongOperation": "수행하려는 작업이 완료하는 데 시간이 오래 걸릴 수 있습니다. 계속 하시겠습니까?", "SSE.Controllers.Toolbar.warnMergeLostData": "왼쪽 위 셀의 데이터 만 병합 된 셀에 남아 있습니다. <br> 계속 하시겠습니까?", "SSE.Controllers.Toolbar.warnNoRecommended": "To create a chart, select the cells that contain the data you'd like to use.<br>If you have names for the rows and columns and you'd like use them as labels, include them in your selection.", "SSE.Controllers.Viewport.textFreezePanes": "창 고정", "SSE.Controllers.Viewport.textFreezePanesShadow": "틀 고정 음영 표시", "SSE.Controllers.Viewport.textHideFBar": "수식 입력줄 감추기", "SSE.Controllers.Viewport.textHideGridlines": "눈금 선 숨기기", "SSE.Controllers.Viewport.textHideHeadings": "제목 숨기기", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "소수점 구분 기호", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "천 단위 구분자", "SSE.Views.AdvancedSeparatorDialog.textLabel": "디지털 데이터 식별을 위한 설정", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "텍스트 퀄리파이어", "SSE.Views.AdvancedSeparatorDialog.textTitle": "고급 설정", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(없음)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "사용자 정의 필터", "SSE.Views.AutoFilterDialog.textAddSelection": "현재 선택을 필터에 추가", "SSE.Views.AutoFilterDialog.textEmptyItem": "{공백}", "SSE.Views.AutoFilterDialog.textSelectAll": "모두 선택", "SSE.Views.AutoFilterDialog.textSelectAllResults": "모든 검색 결과 선택", "SSE.Views.AutoFilterDialog.textWarning": "경고", "SSE.Views.AutoFilterDialog.txtAboveAve": "평균 이상", "SSE.Views.AutoFilterDialog.txtAfter": "이후...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "기간 내의 모든 날짜", "SSE.Views.AutoFilterDialog.txtApril": "4월", "SSE.Views.AutoFilterDialog.txtAugust": "8월", "SSE.Views.AutoFilterDialog.txtBefore": "이전...", "SSE.Views.AutoFilterDialog.txtBegins": "다음으로 시작 ...", "SSE.Views.AutoFilterDialog.txtBelowAve": "평균 이하", "SSE.Views.AutoFilterDialog.txtBetween": "해당 범위...", "SSE.Views.AutoFilterDialog.txtClear": "지우기", "SSE.Views.AutoFilterDialog.txtContains": "포함 ...", "SSE.Views.AutoFilterDialog.txtDateFilter": "날짜 필터", "SSE.Views.AutoFilterDialog.txtDecember": "12월", "SSE.Views.AutoFilterDialog.txtEmpty": "검색", "SSE.Views.AutoFilterDialog.txtEnds": "끝내기 ...", "SSE.Views.AutoFilterDialog.txtEquals": "같음 ...", "SSE.Views.AutoFilterDialog.txtFebruary": "2월", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "셀 색상별로 필터링", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "글꼴 색으로 필터링", "SSE.Views.AutoFilterDialog.txtGreater": "보다 큼 ...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "크거나 같음 ...", "SSE.Views.AutoFilterDialog.txtJanuary": "1월", "SSE.Views.AutoFilterDialog.txtJuly": "7월", "SSE.Views.AutoFilterDialog.txtJune": "6월", "SSE.Views.AutoFilterDialog.txtLabelFilter": "라벨 필터", "SSE.Views.AutoFilterDialog.txtLastMonth": "지난 달", "SSE.Views.AutoFilterDialog.txtLastQuarter": "지난 분기", "SSE.Views.AutoFilterDialog.txtLastWeek": "지난 주", "SSE.Views.AutoFilterDialog.txtLastYear": "작년", "SSE.Views.AutoFilterDialog.txtLess": "작음 ...", "SSE.Views.AutoFilterDialog.txtLessEquals": "작거나 같음 ...", "SSE.Views.AutoFilterDialog.txtMarch": "3월", "SSE.Views.AutoFilterDialog.txtMay": "5월", "SSE.Views.AutoFilterDialog.txtNextMonth": "다음 달", "SSE.Views.AutoFilterDialog.txtNextQuarter": "다음 분기", "SSE.Views.AutoFilterDialog.txtNextWeek": "다음 주", "SSE.Views.AutoFilterDialog.txtNextYear": "다음 해", "SSE.Views.AutoFilterDialog.txtNotBegins": "...로 시작하지 않습니다 ...", "SSE.Views.AutoFilterDialog.txtNotBetween": "제외 범위...", "SSE.Views.AutoFilterDialog.txtNotContains": "포함하지 않음 ...", "SSE.Views.AutoFilterDialog.txtNotEnds": "끝내지 않습니다 ...", "SSE.Views.AutoFilterDialog.txtNotEquals": "같지 않습니다 ...", "SSE.Views.AutoFilterDialog.txtNovember": "11월", "SSE.Views.AutoFilterDialog.txtNumFilter": "숫자 필터", "SSE.Views.AutoFilterDialog.txtOctober": "10월", "SSE.Views.AutoFilterDialog.txtQuarter1": "1분기", "SSE.Views.AutoFilterDialog.txtQuarter2": "1분기", "SSE.Views.AutoFilterDialog.txtQuarter3": "1분기", "SSE.Views.AutoFilterDialog.txtQuarter4": "1분기", "SSE.Views.AutoFilterDialog.txtReapply": "재적용", "SSE.Views.AutoFilterDialog.txtSeptember": "9월", "SSE.Views.AutoFilterDialog.txtSortCellColor": "셀 색", "SSE.Views.AutoFilterDialog.txtSortFontColor": "글꼴 색", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "내림차순 정렬", "SSE.Views.AutoFilterDialog.txtSortLow2High": "최저에서 최고까지 정렬", "SSE.Views.AutoFilterDialog.txtSortOption": "더 많은 옵션...", "SSE.Views.AutoFilterDialog.txtTextFilter": "텍스트 필터", "SSE.Views.AutoFilterDialog.txtThisMonth": "이번 달", "SSE.Views.AutoFilterDialog.txtThisQuarter": "이번 분기", "SSE.Views.AutoFilterDialog.txtThisWeek": "이번 주", "SSE.Views.AutoFilterDialog.txtThisYear": "올해", "SSE.Views.AutoFilterDialog.txtTitle": "필터", "SSE.Views.AutoFilterDialog.txtToday": "오늘", "SSE.Views.AutoFilterDialog.txtTomorrow": "내일", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "값 필터", "SSE.Views.AutoFilterDialog.txtYearToDate": "년간 누적", "SSE.Views.AutoFilterDialog.txtYesterday": "어제", "SSE.Views.AutoFilterDialog.warnFilterError": "값 필터를 적용하려면 \"값\" 영역에 하나 이상의 필드가 있어야 합니다.", "SSE.Views.AutoFilterDialog.warnNoSelected": "하나 이상의 값을 선택해야합니다.", "SSE.Views.CellEditor.textManager": "이름 관리자", "SSE.Views.CellEditor.tipFormula": "함수 삽입", "SSE.Views.CellRangeDialog.errorMaxRows": "오류! 차트 당 최대 데이터 시리즈 수는 255입니다.", "SSE.Views.CellRangeDialog.errorStockChart": "잘못된 행 순서. 주식형 차트를 작성하려면 시트에 데이터를 다음과 같은 순서로 배치하십시오 : <br> 개시 가격, 최대 가격, 최소 가격, 마감 가격.", "SSE.Views.CellRangeDialog.txtEmpty": "이 입력란은 필수 항목", "SSE.Views.CellRangeDialog.txtInvalidRange": "오류! 셀 범위가 잘못되었습니다.", "SSE.Views.CellRangeDialog.txtTitle": "데이터 범위 선택", "SSE.Views.CellSettings.strShrink": "크기에 맞게 축소", "SSE.Views.CellSettings.strWrap": "텍스트 줄 바꾸기", "SSE.Views.CellSettings.textAngle": "각도", "SSE.Views.CellSettings.textBackColor": "배경색", "SSE.Views.CellSettings.textBackground": "배경색", "SSE.Views.CellSettings.textBorderColor": "색상", "SSE.Views.CellSettings.textBorders": "테두리 스타일", "SSE.Views.CellSettings.textClearRule": "규칙 제거", "SSE.Views.CellSettings.textColor": "색상 채우기", "SSE.Views.CellSettings.textColorScales": "색상 코드", "SSE.Views.CellSettings.textCondFormat": "조건부 서식", "SSE.Views.CellSettings.textControl": "텍스트 제어", "SSE.Views.CellSettings.textDataBars": "데이터 막대", "SSE.Views.CellSettings.textDirection": "방향", "SSE.Views.CellSettings.textFill": "채우기", "SSE.Views.CellSettings.textForeground": "전경색", "SSE.Views.CellSettings.textGradient": "그라데이션 포인트", "SSE.Views.CellSettings.textGradientColor": "색상", "SSE.Views.CellSettings.textGradientFill": "그라데이션 채우기", "SSE.Views.CellSettings.textIndent": "톱니 모양", "SSE.Views.CellSettings.textItems": "아이템", "SSE.Views.CellSettings.textLinear": "선형", "SSE.Views.CellSettings.textManageRule": "관리 규칙", "SSE.Views.CellSettings.textNewRule": "새로운 규칙", "SSE.Views.CellSettings.textNoFill": "채우기 없음", "SSE.Views.CellSettings.textOrientation": "텍스트 방향", "SSE.Views.CellSettings.textPattern": "패턴", "SSE.Views.CellSettings.textPatternFill": "패턴", "SSE.Views.CellSettings.textPosition": "위치", "SSE.Views.CellSettings.textRadial": "방사형", "SSE.Views.CellSettings.textSelectBorders": "위에서 선택한 스타일 적용을 변경하려는 테두리 선택", "SSE.Views.CellSettings.textSelection": "현재 선택 고정", "SSE.Views.CellSettings.textThisPivot": "피벗 테이블로 부터", "SSE.Views.CellSettings.textThisSheet": "워크시트로 부터", "SSE.Views.CellSettings.textThisTable": "표로 부터", "SSE.Views.CellSettings.tipAddGradientPoint": "그라데이션 포인트 추가", "SSE.Views.CellSettings.tipAll": "바깥쪽 테두리 및 안쪽 테두리", "SSE.Views.CellSettings.tipBottom": "바깥 아래쪽 테두리", "SSE.Views.CellSettings.tipDiagD": "대각선 테두리 (오른쪽 아래)을 설정", "SSE.Views.CellSettings.tipDiagU": "대각선 테두리 (오른쪽 위쪽)을 설정", "SSE.Views.CellSettings.tipInner": "내부 라인 만 설정", "SSE.Views.CellSettings.tipInnerHor": "안쪽 가로 테두리", "SSE.Views.CellSettings.tipInnerVert": "세로 내부 선만 설정", "SSE.Views.CellSettings.tipLeft": "바깥 왼쪽 테두리", "SSE.Views.CellSettings.tipNone": "테두리 없음 설정", "SSE.Views.CellSettings.tipOuter": "바깥쪽 테두리", "SSE.Views.CellSettings.tipRemoveGradientPoint": "그라데이션 포인트 제거", "SSE.Views.CellSettings.tipRight": "바깥 오른쪽 테두리", "SSE.Views.CellSettings.tipTop": "바깥 위쪽 테두리", "SSE.Views.ChartDataDialog.errorInFormula": "입력한 공식이 잘못되었습니다.", "SSE.Views.ChartDataDialog.errorInvalidReference": "참조가 잘못되었습니다. 참조는 열려 있는 워크시트여야 합니다.", "SSE.Views.ChartDataDialog.errorMaxPoints": "차트당 시리즈내 포인트의 최대값은 4096임", "SSE.Views.ChartDataDialog.errorMaxRows": "각 차트의 최대 데이터 계열 수는 255개입니다.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "참조가 잘못되었습니다. 제목, 값, 크기 또는 데이터 레이블에 대한 참조는 단일 셀, 행 또는 열이어야 합니다.", "SSE.Views.ChartDataDialog.errorNoValues": "차트를 만들려면 계열에 값이 하나 이상 있어야 합니다.", "SSE.Views.ChartDataDialog.errorStockChart": "잘못된 행 순서. 주식형 차트를 작성하려면 다음 순서로 시트에 데이터를 배치하십시오 : <br> 개시 가격, 최대 가격, 최소 가격, 마감 가격.", "SSE.Views.ChartDataDialog.textAdd": "추가", "SSE.Views.ChartDataDialog.textCategory": "가로 (범주) 축 레이블", "SSE.Views.ChartDataDialog.textData": "차트 참조 대상", "SSE.Views.ChartDataDialog.textDelete": "삭제", "SSE.Views.ChartDataDialog.textDown": "아래로", "SSE.Views.ChartDataDialog.textEdit": "편집", "SSE.Views.ChartDataDialog.textInvalidRange": "유효하지 않은 셀 범위", "SSE.Views.ChartDataDialog.textSelectData": "데이터 선택", "SSE.Views.ChartDataDialog.textSeries": "범례 항목(시리즈)", "SSE.Views.ChartDataDialog.textSwitch": "행 / 열 전환", "SSE.Views.ChartDataDialog.textTitle": "차트 데이터", "SSE.Views.ChartDataDialog.textUp": "위", "SSE.Views.ChartDataRangeDialog.errorInFormula": "입력한 공식이 잘못되었습니다.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "참조가 잘못되었습니다. 참조는 열려 있는 워크시트여야 합니다.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "차트당 시리즈내 포인트의 최대값은 4096임", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "각 차트의 최대 데이터 계열 수는 255개입니다.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "참조가 잘못되었습니다. 제목, 값, 크기 또는 데이터 레이블에 대한 참조는 단일 셀, 행 또는 열이어야 합니다.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "차트를 만들려면 계열에 값이 하나 이상 있어야 합니다.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "잘못된 행 순서. 주식형 차트를 작성하려면 다음 순서로 시트에 데이터를 배치하십시오 : <br> 개시 가격, 최대 가격, 최소 가격, 마감 가격.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "유효하지 않은 셀 범위", "SSE.Views.ChartDataRangeDialog.textSelectData": "데이터 선택", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "축 표준 범위", "SSE.Views.ChartDataRangeDialog.txtChoose": "범위 선택", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "시리즈 이름", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "축 라벨", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "시리즈 편집", "SSE.Views.ChartDataRangeDialog.txtValues": "값", "SSE.Views.ChartDataRangeDialog.txtXValues": "X값", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y값", "SSE.Views.ChartSettings.errorMaxRows": "각 차트의 최대 데이터 계열 수는 255개입니다.", "SSE.Views.ChartSettings.strLineWeight": "선 두께", "SSE.Views.ChartSettings.strSparkColor": "색상", "SSE.Views.ChartSettings.strTemplate": "템플릿", "SSE.Views.ChartSettings.text3dDepth": "깊이(%)", "SSE.Views.ChartSettings.text3dHeight": "높이(%)", "SSE.Views.ChartSettings.text3dRotation": "3D 회전", "SSE.Views.ChartSettings.textAdvanced": "고급 설정", "SSE.Views.ChartSettings.textAutoscale": "자동 크기 조정", "SSE.Views.ChartSettings.textBorderSizeErr": "입력 한 값이 잘못되었습니다. <br> 0 ~ 1584pt 사이의 값을 입력하십시오.", "SSE.Views.ChartSettings.textChangeType": "유형 변경", "SSE.Views.ChartSettings.textChartType": "차트 유형 변경", "SSE.Views.ChartSettings.textDefault": "기본 로테이션", "SSE.Views.ChartSettings.textDown": "아래로", "SSE.Views.ChartSettings.textEditData": "데이터 및 위치 편집", "SSE.Views.ChartSettings.textFirstPoint": "첫 번째 지점", "SSE.Views.ChartSettings.textHeight": "높이", "SSE.Views.ChartSettings.textHighPoint": "높은 점수", "SSE.Views.ChartSettings.textKeepRatio": "상수 비율", "SSE.Views.ChartSettings.textLastPoint": "마지막 지점", "SSE.Views.ChartSettings.textLeft": "왼쪽", "SSE.Views.ChartSettings.textLowPoint": "Low Point", "SSE.Views.ChartSettings.textMarkers": "마커", "SSE.Views.ChartSettings.textNarrow": "좁은 시야각", "SSE.Views.ChartSettings.textNegativePoint": "Negative Point", "SSE.Views.ChartSettings.textPerspective": "관점", "SSE.Views.ChartSettings.textRanges": "참조 대상", "SSE.Views.ChartSettings.textRight": "오른쪽", "SSE.Views.ChartSettings.textRightAngle": "직각 축", "SSE.Views.ChartSettings.textSelectData": "데이터 선택", "SSE.Views.ChartSettings.textShow": "보기", "SSE.Views.ChartSettings.textSize": "크기", "SSE.Views.ChartSettings.textStyle": "스타일", "SSE.Views.ChartSettings.textSwitch": "행 / 열 전환", "SSE.Views.ChartSettings.textType": "유형", "SSE.Views.ChartSettings.textUp": "위", "SSE.Views.ChartSettings.textWiden": "시야 확장", "SSE.Views.ChartSettings.textWidth": "너비", "SSE.Views.ChartSettings.textX": "X 회전", "SSE.Views.ChartSettings.textY": "Y 회전", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "오류! 차트당 시리즈내 포인트의 최대값은 4096임", "SSE.Views.ChartSettingsDlg.errorMaxRows": "오류! 차트 당 최대 데이터 시리즈 수는 255입니다.", "SSE.Views.ChartSettingsDlg.errorStockChart": "잘못된 행 순서. 주식형 차트를 작성하려면 시트의 데이터를 다음 순서로 배치하십시오 : <br> 개시 가격, 최대 가격, 최소 가격, 마감 가격.", "SSE.Views.ChartSettingsDlg.textAbsolute": "셀을 이동하거나 크기를 조정하지 마십시오.", "SSE.Views.ChartSettingsDlg.textAlt": "대체 텍스트", "SSE.Views.ChartSettingsDlg.textAltDescription": "설명", "SSE.Views.ChartSettingsDlg.textAltTip": "시각적 개체 정보의 교체는 텍스트 표현을 기반으로 하며 시각 또는 인지 장애가 있는 사람들이 이미지, 자동 모양, 차트 또는 표에 포함된 정보를 더 잘 이해할 수 있도록 읽어줍니다.", "SSE.Views.ChartSettingsDlg.textAltTitle": "제목", "SSE.Views.ChartSettingsDlg.textAuto": "Auto", "SSE.Views.ChartSettingsDlg.textAutoEach": "각자 자동", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "교차축", "SSE.Views.ChartSettingsDlg.textAxisOptions": "축 옵션", "SSE.Views.ChartSettingsDlg.textAxisPos": "축 위치", "SSE.Views.ChartSettingsDlg.textAxisSettings": "축 설정", "SSE.Views.ChartSettingsDlg.textAxisTitle": "제목", "SSE.Views.ChartSettingsDlg.textBase": "기본", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "눈금 사이", "SSE.Views.ChartSettingsDlg.textBillions": "10 억", "SSE.Views.ChartSettingsDlg.textBottom": "Bottom", "SSE.Views.ChartSettingsDlg.textCategoryName": "카테고리 이름", "SSE.Views.ChartSettingsDlg.textCenter": "Center", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "차트 요소 &<br>차트 범례", "SSE.Views.ChartSettingsDlg.textChartTitle": "차트 제목", "SSE.Views.ChartSettingsDlg.textCross": "Cross", "SSE.Views.ChartSettingsDlg.textCustom": "사용자 지정", "SSE.Views.ChartSettingsDlg.textDataColumns": "열 단위로", "SSE.Views.ChartSettingsDlg.textDataLabels": "데이터 레이블", "SSE.Views.ChartSettingsDlg.textDataRows": "행 단위로", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "범례 표시", "SSE.Views.ChartSettingsDlg.textEmptyCells": "숨겨진 빈 셀", "SSE.Views.ChartSettingsDlg.textEmptyLine": "데이터 요소를 선으로 연결", "SSE.Views.ChartSettingsDlg.textFit": "너비에 맞춤", "SSE.Views.ChartSettingsDlg.textFixed": "고정", "SSE.Views.ChartSettingsDlg.textFormat": "라벨 형식", "SSE.Views.ChartSettingsDlg.textGaps": "Gaps", "SSE.Views.ChartSettingsDlg.textGridLines": "눈금 선", "SSE.Views.ChartSettingsDlg.textGroup": "스파크라인 그룹", "SSE.Views.ChartSettingsDlg.textHide": "숨기기", "SSE.Views.ChartSettingsDlg.textHideAxis": "축 감추기", "SSE.Views.ChartSettingsDlg.textHigh": "높음", "SSE.Views.ChartSettingsDlg.textHorAxis": "가로 축", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "수평 보조축", "SSE.Views.ChartSettingsDlg.textHorizontal": "Horizontal", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Hundreds", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "In", "SSE.Views.ChartSettingsDlg.textInnerBottom": "내부 하단", "SSE.Views.ChartSettingsDlg.textInnerTop": "내부 상단", "SSE.Views.ChartSettingsDlg.textInvalidRange": "오류! 셀 범위가 잘못되었습니다.", "SSE.Views.ChartSettingsDlg.textLabelDist": "축 레이블 거리", "SSE.Views.ChartSettingsDlg.textLabelInterval": "레이블 간격", "SSE.Views.ChartSettingsDlg.textLabelOptions": "레이블 옵션", "SSE.Views.ChartSettingsDlg.textLabelPos": "레이블 위치", "SSE.Views.ChartSettingsDlg.textLayout": "레이아웃", "SSE.Views.ChartSettingsDlg.textLeft": "왼쪽", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "왼쪽 오버레이", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Bottom", "SSE.Views.ChartSettingsDlg.textLegendLeft": "왼쪽", "SSE.Views.ChartSettingsDlg.textLegendPos": "범례", "SSE.Views.ChartSettingsDlg.textLegendRight": "오른쪽", "SSE.Views.ChartSettingsDlg.textLegendTop": "Top", "SSE.Views.ChartSettingsDlg.textLines": "선", "SSE.Views.ChartSettingsDlg.textLocationRange": "위치 범위", "SSE.Views.ChartSettingsDlg.textLogScale": "로그 스케일", "SSE.Views.ChartSettingsDlg.textLow": "낮음", "SSE.Views.ChartSettingsDlg.textMajor": "Major", "SSE.Views.ChartSettingsDlg.textMajorMinor": "주니어 및 마이너", "SSE.Views.ChartSettingsDlg.textMajorType": "주요 유형", "SSE.Views.ChartSettingsDlg.textManual": "수동", "SSE.Views.ChartSettingsDlg.textMarkers": "마커", "SSE.Views.ChartSettingsDlg.textMarksInterval": "마크 간 간격", "SSE.Views.ChartSettingsDlg.textMaxValue": "최대값", "SSE.Views.ChartSettingsDlg.textMillions": "Millions", "SSE.Views.ChartSettingsDlg.textMinor": "Minor", "SSE.Views.ChartSettingsDlg.textMinorType": "보조 유형", "SSE.Views.ChartSettingsDlg.textMinValue": "최소값", "SSE.Views.ChartSettingsDlg.textNextToAxis": "축 옆", "SSE.Views.ChartSettingsDlg.textNone": "없음", "SSE.Views.ChartSettingsDlg.textNoOverlay": "오버레이 없음", "SSE.Views.ChartSettingsDlg.textOneCell": "이동하지만 셀별로 크기 조정되지 않음", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "눈금 표시", "SSE.Views.ChartSettingsDlg.textOut": "Out", "SSE.Views.ChartSettingsDlg.textOuterTop": "외부 상단", "SSE.Views.ChartSettingsDlg.textOverlay": "오버레이", "SSE.Views.ChartSettingsDlg.textReverse": "역순으로 값", "SSE.Views.ChartSettingsDlg.textReverseOrder": "역순", "SSE.Views.ChartSettingsDlg.textRight": "오른쪽", "SSE.Views.ChartSettingsDlg.textRightOverlay": "오른쪽 오버레이", "SSE.Views.ChartSettingsDlg.textRotated": "Rotated", "SSE.Views.ChartSettingsDlg.textSameAll": "모두 동일 함", "SSE.Views.ChartSettingsDlg.textSelectData": "데이터 선택", "SSE.Views.ChartSettingsDlg.textSeparator": "데이터 레이블 구분 기호", "SSE.Views.ChartSettingsDlg.textSeriesName": "시리즈 이름", "SSE.Views.ChartSettingsDlg.textShow": "보기", "SSE.Views.ChartSettingsDlg.textShowBorders": "차트 테두리 표시", "SSE.Views.ChartSettingsDlg.textShowData": "숨겨진 행과 열에 데이터 표시", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "빈 셀을 다음으로 표시", "SSE.Views.ChartSettingsDlg.textShowEquation": "Display equation on chart", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "축 표시", "SSE.Views.ChartSettingsDlg.textShowValues": "차트 값 표시", "SSE.Views.ChartSettingsDlg.textSingle": "단일 스파크라인", "SSE.Views.ChartSettingsDlg.textSmooth": "부드럽게", "SSE.Views.ChartSettingsDlg.textSnap": "셀 잠그기", "SSE.Views.ChartSettingsDlg.textSparkRanges": "스파크라인 범위", "SSE.Views.ChartSettingsDlg.textStraight": "직선", "SSE.Views.ChartSettingsDlg.textStyle": "스타일", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "수천", "SSE.Views.ChartSettingsDlg.textTickOptions": "눈금 옵션", "SSE.Views.ChartSettingsDlg.textTitle": "차트 - 고급 설정", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "스파크라인 - 고급 설정", "SSE.Views.ChartSettingsDlg.textTop": "Top", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Trendline options", "SSE.Views.ChartSettingsDlg.textTrillions": "수조", "SSE.Views.ChartSettingsDlg.textTwoCell": "셀 이동 및 크기 조정", "SSE.Views.ChartSettingsDlg.textType": "유형", "SSE.Views.ChartSettingsDlg.textTypeData": "유형 및 데이터", "SSE.Views.ChartSettingsDlg.textUnits": "표시 단위", "SSE.Views.ChartSettingsDlg.textValue": "값", "SSE.Views.ChartSettingsDlg.textVertAxis": "세로 축", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "수직 보조축", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X 축 제목", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y 축 제목", "SSE.Views.ChartSettingsDlg.textZero": "Zero", "SSE.Views.ChartSettingsDlg.txtEmpty": "이 입력란은 필수 항목", "SSE.Views.ChartTypeDialog.errorComboSeries": "혼합형 차트를 만들려면 최소 2 개의 데이터를 선택합니다.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "선택한 차트 유형에는 기존 차트에서 사용하는 보조 축이 필요합니다. 다른 차트 유형을 선택하세요.", "SSE.Views.ChartTypeDialog.textSecondary": "보조축", "SSE.Views.ChartTypeDialog.textSeries": "시리즈", "SSE.Views.ChartTypeDialog.textStyle": "스타일", "SSE.Views.ChartTypeDialog.textTitle": "차트 유형", "SSE.Views.ChartTypeDialog.textType": "형식", "SSE.Views.ChartWizardDialog.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "차트당 시리즈의 최대 포인트 수는 4096입니다.", "SSE.Views.ChartWizardDialog.errorMaxRows": "차트당 최대 데이터 시리즈 수는 255개입니다.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartWizardDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order: opening price, max price, min price, closing price.", "SSE.Views.ChartWizardDialog.textRecommended": "Recommended", "SSE.Views.ChartWizardDialog.textSecondary": "Secondary Axis", "SSE.Views.ChartWizardDialog.textSeries": "Series", "SSE.Views.ChartWizardDialog.textTitle": "Insert Chart", "SSE.Views.ChartWizardDialog.textTitleChange": "Change chart type", "SSE.Views.ChartWizardDialog.textType": "Type", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Choose the chart type and axis for your data series", "SSE.Views.CreatePivotDialog.textDataRange": "소스 데이터 범위", "SSE.Views.CreatePivotDialog.textDestination": "표를 놓을 위치 선택", "SSE.Views.CreatePivotDialog.textExist": "존재하는 워크시트", "SSE.Views.CreatePivotDialog.textInvalidRange": "유효하지 않은 셀 범위", "SSE.Views.CreatePivotDialog.textNew": "신규 워크시트", "SSE.Views.CreatePivotDialog.textSelectData": "데이터 선택", "SSE.Views.CreatePivotDialog.textTitle": "피벗 테이블 만들기", "SSE.Views.CreatePivotDialog.txtEmpty": "이 입력란은 필수 항목입니다.", "SSE.Views.CreateSparklineDialog.textDataRange": "소스 데이터 범위", "SSE.Views.CreateSparklineDialog.textDestination": "스파크라인의 넣을 위치를 선택하십시오", "SSE.Views.CreateSparklineDialog.textInvalidRange": "유효하지 않은 셀 범위", "SSE.Views.CreateSparklineDialog.textSelectData": "데이터 선택", "SSE.Views.CreateSparklineDialog.textTitle": "스파크라인 만들기", "SSE.Views.CreateSparklineDialog.txtEmpty": "이 입력란은 필수 항목입니다.", "SSE.Views.DataTab.capBtnGroup": "그룹", "SSE.Views.DataTab.capBtnTextCustomSort": "정렬", "SSE.Views.DataTab.capBtnTextDataValidation": "데이터 유효성", "SSE.Views.DataTab.capBtnTextRemDuplicates": "중복된 항목 제거", "SSE.Views.DataTab.capBtnTextToCol": "텍스트 나누기", "SSE.Views.DataTab.capBtnUngroup": "그룹 해제", "SSE.Views.DataTab.capDataExternalLinks": "외부 링크", "SSE.Views.DataTab.capDataFromText": "데이터 검색", "SSE.Views.DataTab.capGoalSeek": "Goal Seek", "SSE.Views.DataTab.mniFromFile": "로컬 시스템의 TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "Web 주소에서 TXT/CSV", "SSE.Views.DataTab.mniFromXMLFile": "로컬 XML에서", "SSE.Views.DataTab.textBelow": "요약 행 아래에 설명 위치", "SSE.Views.DataTab.textClear": "윤곽 지우기", "SSE.Views.DataTab.textColumns": "열 그룹 해제", "SSE.Views.DataTab.textGroupColumns": "열 그룹", "SSE.Views.DataTab.textGroupRows": "행 그룹", "SSE.Views.DataTab.textRightOf": "요약 열 오른쪽에 설명 위치", "SSE.Views.DataTab.textRows": "행 그룹 해제", "SSE.Views.DataTab.tipCustomSort": "정렬", "SSE.Views.DataTab.tipDataFromText": "TXT/CSV 파일에서 데이터 가져오기", "SSE.Views.DataTab.tipDataValidation": "데이터 유효성", "SSE.Views.DataTab.tipExternalLinks": "이 스프레드시트와 연결된 다른 파일 보기", "SSE.Views.DataTab.tipGoalSeek": "Find the right input for the value you want", "SSE.Views.DataTab.tipGroup": "셀 범위를 그룹화", "SSE.Views.DataTab.tipRemDuplicates": "시트에서 중복된 행을 삭제합니다", "SSE.Views.DataTab.tipToColumns": "텍스트가 있는 한 열을 여러 열로 나눕니다", "SSE.Views.DataTab.tipUngroup": "셀 범위 그룹 해제", "SSE.Views.DataValidationDialog.errorFormula": "이 값은 현재 오류로 평가됩니다. 계속하시겠습니까?", "SSE.Views.DataValidationDialog.errorInvalid": "\"{0}\" 필드에 입력한 값이 잘못되었습니다.", "SSE.Views.DataValidationDialog.errorInvalidDate": "\"{0}\" 필드에 입력한 날짜가 잘못되었습니다.", "SSE.Views.DataValidationDialog.errorInvalidList": "목록 소스는 구분된 목록이거나 단일 행 또는 단일 열에 대한 참조여야 합니다.", "SSE.Views.DataValidationDialog.errorInvalidTime": "\"{0}\" 필드에 입력한 시간이 잘못되었습니다.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "\"{1}\" 필드는 \"{0}\" 필드보다 크거나 같아야 합니다.", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "\"{0}\" 필드와 \"{1}\" 필드 모두에 값을 입력해야 합니다.", "SSE.Views.DataValidationDialog.errorMustEnterValue": "\"{0}\" 필드에 값을 입력해야 합니다.", "SSE.Views.DataValidationDialog.errorNamedRange": "지정한 명명된 범위를 찾을 수 없습니다.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "\"{0}\" 조건에서는 음수 값을 사용할 수 없습니다.", "SSE.Views.DataValidationDialog.errorNotNumeric": "필드 \"{0}\"은 숫자 또는 수식인지, 숫자가 포함 된 셀을 참조해야합니다.", "SSE.Views.DataValidationDialog.strError": "오류 메시지", "SSE.Views.DataValidationDialog.strInput": "설명 메시지", "SSE.Views.DataValidationDialog.strSettings": "설정", "SSE.Views.DataValidationDialog.textAlert": "경고", "SSE.Views.DataValidationDialog.textAllow": "제한 대상", "SSE.Views.DataValidationDialog.textApply": "변경 내용을 설정이 같은 모든 셀에 적용", "SSE.Views.DataValidationDialog.textCellSelected": "셀을 선택하면 이 설명 메시지가 표시됩니다.", "SSE.Views.DataValidationDialog.textCompare": "비교", "SSE.Views.DataValidationDialog.textData": "제한 방법", "SSE.Views.DataValidationDialog.textEndDate": "종료 일", "SSE.Views.DataValidationDialog.textEndTime": "종료 시간", "SSE.Views.DataValidationDialog.textError": "오류 메시지", "SSE.Views.DataValidationDialog.textFormula": "수식", "SSE.Views.DataValidationDialog.textIgnore": "공백 무시", "SSE.Views.DataValidationDialog.textInput": "설명 메시지", "SSE.Views.DataValidationDialog.textMax": "최대값", "SSE.Views.DataValidationDialog.textMessage": "메시지", "SSE.Views.DataValidationDialog.textMin": "최소값", "SSE.Views.DataValidationDialog.textSelectData": "데이터 선택", "SSE.Views.DataValidationDialog.textShowDropDown": "셀에 드롭 다운 목록 표시", "SSE.Views.DataValidationDialog.textShowError": "잘못된 데이터 입력 시 오류 메시지 표시", "SSE.Views.DataValidationDialog.textShowInput": "셀 선택 시 설명 메시지 표시", "SSE.Views.DataValidationDialog.textSource": "출처", "SSE.Views.DataValidationDialog.textStartDate": "시작일", "SSE.Views.DataValidationDialog.textStartTime": "시작 시간", "SSE.Views.DataValidationDialog.textStop": "정지", "SSE.Views.DataValidationDialog.textStyle": "스타일", "SSE.Views.DataValidationDialog.textTitle": "제목", "SSE.Views.DataValidationDialog.textUserEnters": "사용자가 잘못된 데이터를 입력하면 이 오류 메시지가 표시됩니다.", "SSE.Views.DataValidationDialog.txtAny": "모든 값", "SSE.Views.DataValidationDialog.txtBetween": "해당 범위", "SSE.Views.DataValidationDialog.txtDate": "날짜", "SSE.Views.DataValidationDialog.txtDecimal": "소수 자릿수", "SSE.Views.DataValidationDialog.txtElTime": "경과 시간", "SSE.Views.DataValidationDialog.txtEndDate": "종료 일", "SSE.Views.DataValidationDialog.txtEndTime": "종료 시간", "SSE.Views.DataValidationDialog.txtEqual": "=", "SSE.Views.DataValidationDialog.txtGreaterThan": ">", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "> =", "SSE.Views.DataValidationDialog.txtLength": "길이", "SSE.Views.DataValidationDialog.txtLessThan": "<", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "< =", "SSE.Views.DataValidationDialog.txtList": "목록", "SSE.Views.DataValidationDialog.txtNotBetween": "제외 범위", "SSE.Views.DataValidationDialog.txtNotEqual": "< >", "SSE.Views.DataValidationDialog.txtOther": "사용자 지정", "SSE.Views.DataValidationDialog.txtStartDate": "시작일", "SSE.Views.DataValidationDialog.txtStartTime": "시작 시간", "SSE.Views.DataValidationDialog.txtTextLength": "텍스트 길이", "SSE.Views.DataValidationDialog.txtTime": "시간", "SSE.Views.DataValidationDialog.txtWhole": "정수", "SSE.Views.DigitalFilterDialog.capAnd": "그리고", "SSE.Views.DigitalFilterDialog.capCondition1": "=", "SSE.Views.DigitalFilterDialog.capCondition10": "끝나지 않습니다", "SSE.Views.DigitalFilterDialog.capCondition11": "포함", "SSE.Views.DigitalFilterDialog.capCondition12": "포함하지 않음", "SSE.Views.DigitalFilterDialog.capCondition2": "< >", "SSE.Views.DigitalFilterDialog.capCondition3": "보다 큼", "SSE.Views.DigitalFilterDialog.capCondition30": "이후다", "SSE.Views.DigitalFilterDialog.capCondition4": "크거나 같음", "SSE.Views.DigitalFilterDialog.capCondition40": "이후 또는 같음", "SSE.Views.DigitalFilterDialog.capCondition5": "미만", "SSE.Views.DigitalFilterDialog.capCondition50": "이전", "SSE.Views.DigitalFilterDialog.capCondition6": "작거나 같음", "SSE.Views.DigitalFilterDialog.capCondition60": "이전 또는 같음", "SSE.Views.DigitalFilterDialog.capCondition7": "시작 문자", "SSE.Views.DigitalFilterDialog.capCondition8": "로 시작하지 않습니다", "SSE.Views.DigitalFilterDialog.capCondition9": "로 끝남", "SSE.Views.DigitalFilterDialog.capOr": "또는", "SSE.Views.DigitalFilterDialog.textNoFilter": "필터 없음", "SSE.Views.DigitalFilterDialog.textShowRows": "다음 조건을 만족하는 행 표시", "SSE.Views.DigitalFilterDialog.textUse1": "한 문자 만 표시하려면?", "SSE.Views.DigitalFilterDialog.textUse2": "모든 문자를 표시하려면 *를 사용하십시오.", "SSE.Views.DigitalFilterDialog.txtSelectDate": "날짜선택", "SSE.Views.DigitalFilterDialog.txtTitle": "사용자 정의 필터", "SSE.Views.DocumentHolder.advancedEquationText": "방정식 설정", "SSE.Views.DocumentHolder.advancedImgText": "이미지 고급 설정", "SSE.Views.DocumentHolder.advancedShapeText": "모양 고급 설정", "SSE.Views.DocumentHolder.advancedSlicerText": "슬라이서 고급 설정", "SSE.Views.DocumentHolder.allLinearText": "모두 - 선형", "SSE.Views.DocumentHolder.allProfText": "전체 - 프로페셔널", "SSE.Views.DocumentHolder.bottomCellText": "아래쪽 정렬", "SSE.Views.DocumentHolder.bulletsText": "글 머리 기호 및 번호 매기기", "SSE.Views.DocumentHolder.centerCellText": "가운데 맞춤", "SSE.Views.DocumentHolder.chartDataText": "차트 데이터 선택", "SSE.Views.DocumentHolder.chartText": "차트 고급 설정", "SSE.Views.DocumentHolder.chartTypeText": "차트 유형 변경", "SSE.Views.DocumentHolder.currLinearText": "전류 - 선형", "SSE.Views.DocumentHolder.currProfText": "현재-직업", "SSE.Views.DocumentHolder.deleteColumnText": "열", "SSE.Views.DocumentHolder.deleteRowText": "행", "SSE.Views.DocumentHolder.deleteTableText": "테이블", "SSE.Views.DocumentHolder.direct270Text": "텍스트 회전", "SSE.Views.DocumentHolder.direct90Text": "텍스트 아래로 회전", "SSE.Views.DocumentHolder.directHText": "Horizontal", "SSE.Views.DocumentHolder.directionText": "텍스트 방향", "SSE.Views.DocumentHolder.editChartText": "데이터 편집", "SSE.Views.DocumentHolder.editHyperlinkText": "하이퍼 링크 편집", "SSE.Views.DocumentHolder.hideEqToolbar": "수식 도구 모음 숨기기", "SSE.Views.DocumentHolder.insertColumnLeftText": "왼쪽 열", "SSE.Views.DocumentHolder.insertColumnRightText": "오른쪽 열", "SSE.Views.DocumentHolder.insertRowAboveText": "위의 행", "SSE.Views.DocumentHolder.insertRowBelowText": "아래 행", "SSE.Views.DocumentHolder.latexText": "라텍", "SSE.Views.DocumentHolder.originalSizeText": "실제 크기", "SSE.Views.DocumentHolder.removeHyperlinkText": "하이퍼 링크 제거", "SSE.Views.DocumentHolder.selectColumnText": "전체 열", "SSE.Views.DocumentHolder.selectDataText": "열 데이터", "SSE.Views.DocumentHolder.selectRowText": "행", "SSE.Views.DocumentHolder.selectTableText": "테이블", "SSE.Views.DocumentHolder.showEqToolbar": "수식 도구 모음 표시", "SSE.Views.DocumentHolder.strDelete": "서명 삭제", "SSE.Views.DocumentHolder.strDetails": "서명 상세", "SSE.Views.DocumentHolder.strSetup": "서명 셋업", "SSE.Views.DocumentHolder.strSign": "서명", "SSE.Views.DocumentHolder.textAlign": "정렬", "SSE.Views.DocumentHolder.textArrange": "순서", "SSE.Views.DocumentHolder.textArrangeBack": "맨 뒤로 보내기", "SSE.Views.DocumentHolder.textArrangeBackward": "뒤로 보내기", "SSE.Views.DocumentHolder.textArrangeForward": "앞으로 보내기", "SSE.Views.DocumentHolder.textArrangeFront": "맨 앞으로 보내기", "SSE.Views.DocumentHolder.textAverage": "평균", "SSE.Views.DocumentHolder.textBullets": "글 머리 기호", "SSE.Views.DocumentHolder.textCopyCells": "Copy cells", "SSE.Views.DocumentHolder.textCount": "계산", "SSE.Views.DocumentHolder.textCrop": "자르기", "SSE.Views.DocumentHolder.textCropFill": "채우기", "SSE.Views.DocumentHolder.textCropFit": "맞춤", "SSE.Views.DocumentHolder.textEditPoints": "꼭지점 수정", "SSE.Views.DocumentHolder.textEntriesList": "드롭 다운 목록에서 선택", "SSE.Views.DocumentHolder.textFillDays": "Fill days", "SSE.Views.DocumentHolder.textFillFormatOnly": "Fill formatting only", "SSE.Views.DocumentHolder.textFillMonths": "Fill months", "SSE.Views.DocumentHolder.textFillSeries": "Fill series", "SSE.Views.DocumentHolder.textFillWeekdays": "Fill weekdays", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Fill without formatting", "SSE.Views.DocumentHolder.textFillYears": "Fill years", "SSE.Views.DocumentHolder.textFlashFill": "Flash fill", "SSE.Views.DocumentHolder.textFlipH": "좌우대칭", "SSE.Views.DocumentHolder.textFlipV": "상하대칭", "SSE.Views.DocumentHolder.textFreezePanes": "창 고정", "SSE.Views.DocumentHolder.textFromFile": "파일로부터", "SSE.Views.DocumentHolder.textFromStorage": "스토리지로 부터", "SSE.Views.DocumentHolder.textFromUrl": "URL로부터", "SSE.Views.DocumentHolder.textGrowthTrend": "Growth trend", "SSE.Views.DocumentHolder.textLinearTrend": "Linear trend", "SSE.Views.DocumentHolder.textListSettings": "목록 설정", "SSE.Views.DocumentHolder.textMacro": "매크로 지정", "SSE.Views.DocumentHolder.textMax": "최대", "SSE.Views.DocumentHolder.textMin": "최소", "SSE.Views.DocumentHolder.textMore": "더 많은 기능", "SSE.Views.DocumentHolder.textMoreFormats": "기타 형식", "SSE.Views.DocumentHolder.textNone": "없음", "SSE.Views.DocumentHolder.textNumbering": "번호 매기기", "SSE.Views.DocumentHolder.textReplace": "이미지 바꾸기", "SSE.Views.DocumentHolder.textResetCrop": "Reset crop", "SSE.Views.DocumentHolder.textRotate": "회전", "SSE.Views.DocumentHolder.textRotate270": "왼쪽으로 90도 회전", "SSE.Views.DocumentHolder.textRotate90": "오른쪽으로 90도 회전", "SSE.Views.DocumentHolder.textSaveAsPicture": "그림으로 저장", "SSE.Views.DocumentHolder.textSeries": "Series", "SSE.Views.DocumentHolder.textShapeAlignBottom": "아래쪽 정렬", "SSE.Views.DocumentHolder.textShapeAlignCenter": "가운데 정렬", "SSE.Views.DocumentHolder.textShapeAlignLeft": "왼쪽 정렬", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "중간 정렬", "SSE.Views.DocumentHolder.textShapeAlignRight": "오른쪽 정렬", "SSE.Views.DocumentHolder.textShapeAlignTop": "상단 정렬", "SSE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "SSE.Views.DocumentHolder.textStdDev": "표준편차", "SSE.Views.DocumentHolder.textSum": "합계", "SSE.Views.DocumentHolder.textUndo": "실행 취소", "SSE.Views.DocumentHolder.textUnFreezePanes": "창 고정 취소", "SSE.Views.DocumentHolder.textVar": "표본분산", "SSE.Views.DocumentHolder.tipMarkersArrow": "화살 글머리 기호", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "체크 표시 글머리 기호", "SSE.Views.DocumentHolder.tipMarkersDash": "대시 글머리 기호", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "채워진 마름모 글머리 기호", "SSE.Views.DocumentHolder.tipMarkersFRound": "채워진 원형 글머리 기호", "SSE.Views.DocumentHolder.tipMarkersFSquare": "채워진 사각형 글머리 기호", "SSE.Views.DocumentHolder.tipMarkersHRound": "빈 원형 글머리 기호", "SSE.Views.DocumentHolder.tipMarkersStar": "별 글머리 기호", "SSE.Views.DocumentHolder.topCellText": "정렬 위쪽", "SSE.Views.DocumentHolder.txtAccounting": "회계", "SSE.Views.DocumentHolder.txtAddComment": "주석 추가", "SSE.Views.DocumentHolder.txtAddNamedRange": "이름 정의", "SSE.Views.DocumentHolder.txtArrange": "순서", "SSE.Views.DocumentHolder.txtAscending": "오름차순", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "자동 맞춤 열 너비", "SSE.Views.DocumentHolder.txtAutoRowHeight": "행 높이 자동 맞춤", "SSE.Views.DocumentHolder.txtAverage": "평균", "SSE.Views.DocumentHolder.txtCellFormat": "Format cells", "SSE.Views.DocumentHolder.txtClear": "지우기", "SSE.Views.DocumentHolder.txtClearAll": "모두", "SSE.Views.DocumentHolder.txtClearComments": "코멘트", "SSE.Views.DocumentHolder.txtClearFormat": "형식", "SSE.Views.DocumentHolder.txtClearHyper": "하이퍼 링크", "SSE.Views.DocumentHolder.txtClearPivotField": "{0}에서 필터 지우기", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "선택한 스파크라인 그룹 지우기", "SSE.Views.DocumentHolder.txtClearSparklines": "선택한 스파크라인 지우기", "SSE.Views.DocumentHolder.txtClearText": "텍스트", "SSE.Views.DocumentHolder.txtCollapse": "Collapse", "SSE.Views.DocumentHolder.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.DocumentHolder.txtColumn": "전체 열", "SSE.Views.DocumentHolder.txtColumnWidth": "열 너비 설정", "SSE.Views.DocumentHolder.txtCondFormat": "조건부 서식", "SSE.Views.DocumentHolder.txtCopy": "복사", "SSE.Views.DocumentHolder.txtCount": "계산", "SSE.Views.DocumentHolder.txtCurrency": "통화", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "사용자 정의 열 너비", "SSE.Views.DocumentHolder.txtCustomRowHeight": "사용자 정의 행 높이", "SSE.Views.DocumentHolder.txtCustomSort": "정렬", "SSE.Views.DocumentHolder.txtCut": "잘라 내기", "SSE.Views.DocumentHolder.txtDateLong": "확장된 날짜 형식", "SSE.Views.DocumentHolder.txtDateShort": "간단한 날짜 형식", "SSE.Views.DocumentHolder.txtDelete": "삭제", "SSE.Views.DocumentHolder.txtDelField": "삭제", "SSE.Views.DocumentHolder.txtDescending": "내림차순", "SSE.Views.DocumentHolder.txtDifference": "차이", "SSE.Views.DocumentHolder.txtDistribHor": "수평 분포", "SSE.Views.DocumentHolder.txtDistribVert": "수직 분포", "SSE.Views.DocumentHolder.txtEditComment": "주석 편집", "SSE.Views.DocumentHolder.txtEditObject": "Edit object", "SSE.Views.DocumentHolder.txtExpand": "Expand", "SSE.Views.DocumentHolder.txtExpandCollapse": "Expand/Collapse", "SSE.Views.DocumentHolder.txtExpandEntire": "Expand Entire Field", "SSE.Views.DocumentHolder.txtFieldSettings": "필드 세팅", "SSE.Views.DocumentHolder.txtFilter": "필터", "SSE.Views.DocumentHolder.txtFilterCellColor": "셀의 색상별로 필터링", "SSE.Views.DocumentHolder.txtFilterFontColor": "글꼴 색으로 필터링", "SSE.Views.DocumentHolder.txtFilterValue": "선택한 셀의 값으로 필터링", "SSE.Views.DocumentHolder.txtFormula": "함수 삽입", "SSE.Views.DocumentHolder.txtFraction": "분수", "SSE.Views.DocumentHolder.txtGeneral": "일반", "SSE.Views.DocumentHolder.txtGetLink": "이 범위의 링크 가져오기", "SSE.Views.DocumentHolder.txtGrandTotal": "총합계", "SSE.Views.DocumentHolder.txtGroup": "그룹", "SSE.Views.DocumentHolder.txtHide": "숨기기", "SSE.Views.DocumentHolder.txtIndex": "색인", "SSE.Views.DocumentHolder.txtInsert": "삽입", "SSE.Views.DocumentHolder.txtInsHyperlink": "하이퍼 링크", "SSE.Views.DocumentHolder.txtInsImage": "파일에서 이미지 삽입", "SSE.Views.DocumentHolder.txtInsImageUrl": "URL에서 이미지 삽입", "SSE.Views.DocumentHolder.txtLabelFilter": "라벨 필터", "SSE.Views.DocumentHolder.txtMax": "최대", "SSE.Views.DocumentHolder.txtMin": "최소", "SSE.Views.DocumentHolder.txtMoreOptions": "더 많은 옵션", "SSE.Views.DocumentHolder.txtNormal": "계산되지 않음", "SSE.Views.DocumentHolder.txtNumber": "숫자", "SSE.Views.DocumentHolder.txtNumFormat": "숫자 형식", "SSE.Views.DocumentHolder.txtPaste": "붙여 넣기", "SSE.Views.DocumentHolder.txtPercent": "백분율", "SSE.Views.DocumentHolder.txtPercentage": "백분율", "SSE.Views.DocumentHolder.txtPercentDiff": "%의 차이", "SSE.Views.DocumentHolder.txtPercentOfCol": "열 전체의 %", "SSE.Views.DocumentHolder.txtPercentOfGrand": "총계의 %", "SSE.Views.DocumentHolder.txtPercentOfParent": "상위 합계의 %", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "상위 열 합계의 %", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "상위 행 합계의 %", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "누계 합계 %", "SSE.Views.DocumentHolder.txtPercentOfTotal": "행 전체의 %", "SSE.Views.DocumentHolder.txtPivotSettings": "피벗 테이블 설정", "SSE.Views.DocumentHolder.txtProduct": "제품", "SSE.Views.DocumentHolder.txtRankAscending": "오름차순으로 순위 매기기", "SSE.Views.DocumentHolder.txtRankDescending": "내림차순으로 순위 매기기", "SSE.Views.DocumentHolder.txtReapply": "재적용", "SSE.Views.DocumentHolder.txtRefresh": "새로고침", "SSE.Views.DocumentHolder.txtRow": "전체 행", "SSE.Views.DocumentHolder.txtRowHeight": "행 높이 설정", "SSE.Views.DocumentHolder.txtRunTotal": "총실행", "SSE.Views.DocumentHolder.txtScientific": "지수", "SSE.Views.DocumentHolder.txtSelect": "선택", "SSE.Views.DocumentHolder.txtShiftDown": "셀을 아래로 이동", "SSE.Views.DocumentHolder.txtShiftLeft": "셀을 왼쪽으로 시프트", "SSE.Views.DocumentHolder.txtShiftRight": "셀을 오른쪽으로 이동", "SSE.Views.DocumentHolder.txtShiftUp": "셀을 위로 이동", "SSE.Views.DocumentHolder.txtShow": "숨기기 취소", "SSE.Views.DocumentHolder.txtShowAs": "표시된 값은", "SSE.Views.DocumentHolder.txtShowComment": "설명 표시", "SSE.Views.DocumentHolder.txtShowDetails": "세부 정보 표시", "SSE.Views.DocumentHolder.txtSort": "정렬", "SSE.Views.DocumentHolder.txtSortCellColor": "위에 셀 색상 선택", "SSE.Views.DocumentHolder.txtSortFontColor": "선택한 글꼴 색을 맨 위에 표시", "SSE.Views.DocumentHolder.txtSortOption": "더 많은 정렬 옵션", "SSE.Views.DocumentHolder.txtSparklines": "스파크라인", "SSE.Views.DocumentHolder.txtSubtotalField": "소계", "SSE.Views.DocumentHolder.txtSum": "합계", "SSE.Views.DocumentHolder.txtSummarize": "값을 요약하는 기준으로", "SSE.Views.DocumentHolder.txtText": "텍스트", "SSE.Views.DocumentHolder.txtTextAdvanced": "단락 고급 설정", "SSE.Views.DocumentHolder.txtTime": "시간", "SSE.Views.DocumentHolder.txtTop10": "상위 10", "SSE.Views.DocumentHolder.txtUngroup": "그룹 해제", "SSE.Views.DocumentHolder.txtValueFieldSettings": "값 필드 설정", "SSE.Views.DocumentHolder.txtValueFilter": "값 필터", "SSE.Views.DocumentHolder.txtWidth": "폭", "SSE.Views.DocumentHolder.unicodeText": "유니코드", "SSE.Views.DocumentHolder.vertAlignText": "Vertical Alignment", "SSE.Views.ExternalLinksDlg.closeButtonText": "닫기", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Automatically update data from the linked sources", "SSE.Views.ExternalLinksDlg.textChange": "소스 변경", "SSE.Views.ExternalLinksDlg.textDelete": "링크 해제", "SSE.Views.ExternalLinksDlg.textDeleteAll": "모든 링크 해제", "SSE.Views.ExternalLinksDlg.textOk": "확정", "SSE.Views.ExternalLinksDlg.textOpen": "오픈 소스", "SSE.Views.ExternalLinksDlg.textSource": "출처", "SSE.Views.ExternalLinksDlg.textStatus": "상태", "SSE.Views.ExternalLinksDlg.textUnknown": "알 수 없음", "SSE.Views.ExternalLinksDlg.textUpdate": "값 업데이트", "SSE.Views.ExternalLinksDlg.textUpdateAll": "모두 업데이트", "SSE.Views.ExternalLinksDlg.textUpdating": "갱신중...", "SSE.Views.ExternalLinksDlg.txtTitle": "외부 링크", "SSE.Views.FieldSettingsDialog.strLayout": "레이아웃", "SSE.Views.FieldSettingsDialog.strSubtotals": "소계", "SSE.Views.FieldSettingsDialog.textNumFormat": "숫자 형식", "SSE.Views.FieldSettingsDialog.textReport": "보고서 폼", "SSE.Views.FieldSettingsDialog.textTitle": "필드 세팅", "SSE.Views.FieldSettingsDialog.txtAverage": "평균", "SSE.Views.FieldSettingsDialog.txtBlank": "각 항목 다음에 빈 줄을 삽입", "SSE.Views.FieldSettingsDialog.txtBottom": "그룹 하단에 표시", "SSE.Views.FieldSettingsDialog.txtCompact": "요약", "SSE.Views.FieldSettingsDialog.txtCount": "계산", "SSE.Views.FieldSettingsDialog.txtCountNums": "수를 집계", "SSE.Views.FieldSettingsDialog.txtCustomName": "사용자 정의 이름", "SSE.Views.FieldSettingsDialog.txtEmpty": "데이터가 없는 항목 표시", "SSE.Views.FieldSettingsDialog.txtMax": "최대", "SSE.Views.FieldSettingsDialog.txtMin": "최소", "SSE.Views.FieldSettingsDialog.txtOutline": "개요", "SSE.Views.FieldSettingsDialog.txtProduct": "제품", "SSE.Views.FieldSettingsDialog.txtRepeat": "각 행에서 항목 레이블을 반복", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "소계 표시", "SSE.Views.FieldSettingsDialog.txtSourceName": "소스 이름:", "SSE.Views.FieldSettingsDialog.txtStdDev": "표준편차", "SSE.Views.FieldSettingsDialog.txtStdDevp": "표준편차", "SSE.Views.FieldSettingsDialog.txtSum": "합계", "SSE.Views.FieldSettingsDialog.txtSummarize": "부분합 함수", "SSE.Views.FieldSettingsDialog.txtTabular": "표 형식", "SSE.Views.FieldSettingsDialog.txtTop": "그룹 상단에 표시", "SSE.Views.FieldSettingsDialog.txtVar": "표본분산", "SSE.Views.FieldSettingsDialog.txtVarp": "분산", "SSE.Views.FileMenu.ariaFileMenu": "File menu", "SSE.Views.FileMenu.btnBackCaption": "파일 위치 열기", "SSE.Views.FileMenu.btnCloseEditor": "Close File", "SSE.Views.FileMenu.btnCloseMenuCaption": "메뉴 닫기", "SSE.Views.FileMenu.btnCreateNewCaption": "새로 만들기", "SSE.Views.FileMenu.btnDownloadCaption": "다운로드 방법", "SSE.Views.FileMenu.btnExitCaption": "완료", "SSE.Views.FileMenu.btnExportToPDFCaption": "PDF로 내보내기", "SSE.Views.FileMenu.btnFileOpenCaption": "열기", "SSE.Views.FileMenu.btnHelpCaption": "Help", "SSE.Views.FileMenu.btnHistoryCaption": "버전 기록", "SSE.Views.FileMenu.btnInfoCaption": "스프레드 시트 정보", "SSE.Views.FileMenu.btnPrintCaption": "인쇄", "SSE.Views.FileMenu.btnProtectCaption": "보호", "SSE.Views.FileMenu.btnRecentFilesCaption": "최근 열기", "SSE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnReturnCaption": "스프레드시트로 돌아 가기", "SSE.Views.FileMenu.btnRightsCaption": "액세스 권한", "SSE.Views.FileMenu.btnSaveAsCaption": "다른 이름으로 저장", "SSE.Views.FileMenu.btnSaveCaption": "저장", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "다른 이름으로 저장", "SSE.Views.FileMenu.btnSettingsCaption": "고급 설정", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "SSE.Views.FileMenu.btnToEditCaption": "스프레드시트 편집", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "빈 스프레드시트", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "새로 만들기", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "적용", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "저자 추가", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "텍스트추가", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "애플리케이션", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "작성자", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "액세스 권한 변경", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "코멘트", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "생성됨", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "최종 편집자", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "최종 편집", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "소유자", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "위치", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "권한이있는 사람", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "스프레드시트 정보", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "제목", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "태그 추가", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "스프레드 시트 제목", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "업로드 되었습니다", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "접근 권한", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "액세스 권한 변경", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "권한이있는 사람", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "적용", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "공동 편집 모드", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "1904 날짜 시스템 사용", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "소수점 구분 기호", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "사전 언어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Enable iterative calculation", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Fast", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "글꼴 힌트", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "수식 언어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "예 : SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Show function tooltip", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Show horizontal scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "대문자 무시", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "숫자가 있는 단어 무시", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "매크로 설정", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Maximum change", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Maximum iterations", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "내용을 붙여넣을 때 \"붙여넣기 옵션\" 표시", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "R1C1 참조 양식", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "국가 별 설정", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "예 :", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTL interface", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "시트에서 코멘트 표시", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "다른 사용자의 변경사항 표시", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "해결된 코멘트 표시", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Snapped to the grid while scrolling", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Strict", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Tab style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "인터페이스 테마", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "천 단위 구분자", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "측정 단위", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "지역 설정에 따라 구분 기호 사용", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Show vertical scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "기본 확대/축소 값", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "매 10 분마다", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "30 분마다", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "매 5 분마다", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "매시간", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "자동 복구", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "자동 저장", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "사용 안 함", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "Fill", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "모든 기록 버전을 서버에 저장", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "Line", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Every Minute", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "참조 스타일", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "고급 설정", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "Appearance", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "자동 고침 옵션...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "벨라루스어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "불가리아어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "캐나다어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "사전 설정 캐시 모드", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "계산 중", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "센티미터", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "협업", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "체코어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Customize quick access", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "덴마크어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "De<PERSON>ch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "편집 및 저장", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "그리스어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "영어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "입력하신 항목을 사용할 수 없습니다. 정수 또는 소수가 필요할 수 있습니다.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "스페인어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "실시간 공동 편집. 모든 변경사항은 자동으로 저장됩니다.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "끝", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "프랑스 국민", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "헝가리어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "아르메니아어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "인도네시아어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "인치", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "이탈리아어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "일본어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "한국어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "최종 사용", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "라오스어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "라트비아어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "as OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Native", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "노르웨이어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "네델란드어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "폴란드어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "보정", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Point", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "브라질어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "포르투갈어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "편집기 헤더에 빠른 인쇄 버튼 표시", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "문서는 마지막으로 선택한 프린터 또는 기본 프린터에서 인쇄됩니다.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "지역", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "루마니아어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "러시아어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "모두 활성화", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "알림 없이 모든 매크로 활성화", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Turn on screen reader support", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Default sheet direction", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "This setting will affect only the new sheets", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Left-to-right", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Right-to-left", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "슬로바키아어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "슬로베이나어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "모두 비활성화", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "알림없이 모든 매크로를 비활성화", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "변경 사항을 동기화하기 위해 '저장' 버튼을 사용하세요", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "스웨덴어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Use toolbar color as tabs background", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "터키어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "우크라이나어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "키보드를 사용하여 사용자 인터페이스를 탐색하려면 Alt 키를 사용하세요.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "키보드를 사용하여 사용자 인터페이스를 탐색하려면 Option 키를 사용하세요.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "베트남어", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "알림 표시", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "모든 매크로를 비활성화로 알림", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "Windows로", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "워크스페이스", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "중국어", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "경고", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "비밀번호로", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "스프레드시트 보호", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "서명으로", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "스프레드시트에 유효한 서명이 추가되었습니다.<br>스프레드시트는 편집으로부터 보호됩니다.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "눈에 보이지 않는 디지털 서명을 추가하여<br>스프레드시트의 무결성을 보장하세요.", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "스프레드시트 편집", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "편집은 스프레드시트에서 서명을 삭제할 것입니다.<br> 계속하시겠습니까?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "이 스프레드시트는 비밀번호로 보호되어있습니다.", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "해당 스프레드시트를 비밀번호로 암호화하세요.", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "이 스프레드시트는 서명되어야 합니다.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "유효한 서명이 스프레드시트에 추가되었습니다. 이 스프레드시트는 편집할 수 없도록 보호되었습니다.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "스프레드시트에 몇 가지 디지털 서명이 유효하지 않거나 확인되지 않음. 스프레드시트는 편집할 수 없도록 보호됨.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "서명 보기", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "다운로드 방법", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "다른 이름으로 저장", "SSE.Views.FillSeriesDialog.textAuto": "AutoFill", "SSE.Views.FillSeriesDialog.textCols": "Columns", "SSE.Views.FillSeriesDialog.textDate": "Date", "SSE.Views.FillSeriesDialog.textDateUnit": "Date unit", "SSE.Views.FillSeriesDialog.textDay": "Day", "SSE.Views.FillSeriesDialog.textGrowth": "Growth", "SSE.Views.FillSeriesDialog.textLinear": "Linear", "SSE.Views.FillSeriesDialog.textMonth": "Month", "SSE.Views.FillSeriesDialog.textRows": "행", "SSE.Views.FillSeriesDialog.textSeries": "Series in", "SSE.Views.FillSeriesDialog.textStep": "Step value", "SSE.Views.FillSeriesDialog.textStop": "Stop value", "SSE.Views.FillSeriesDialog.textTitle": "Series", "SSE.Views.FillSeriesDialog.textTrend": "Trend", "SSE.Views.FillSeriesDialog.textType": "Type", "SSE.Views.FillSeriesDialog.textWeek": "Weekday", "SSE.Views.FillSeriesDialog.textYear": "Year", "SSE.Views.FillSeriesDialog.txtErrorNumber": "입력하신 항목을 사용할 수 없습니다. 정수 또는 소수가 필요할 수 있습니다.", "SSE.Views.FormatRulesEditDlg.fillColor": "채우기 색", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "경고", "SSE.Views.FormatRulesEditDlg.text2Scales": "2색 눈금", "SSE.Views.FormatRulesEditDlg.text3Scales": "3색 눈금", "SSE.Views.FormatRulesEditDlg.textAllBorders": "모든 테두리", "SSE.Views.FormatRulesEditDlg.textAppearance": "막대 모양", "SSE.Views.FormatRulesEditDlg.textApply": "범위에 적용", "SSE.Views.FormatRulesEditDlg.textAutomatic": "자동", "SSE.Views.FormatRulesEditDlg.textAxis": "축", "SSE.Views.FormatRulesEditDlg.textBarDirection": "막대 방향", "SSE.Views.FormatRulesEditDlg.textBold": "굵게", "SSE.Views.FormatRulesEditDlg.textBorder": "테두리", "SSE.Views.FormatRulesEditDlg.textBordersColor": "테두리 색상", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "테두리 스타일", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "아래쪽 테두리", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "조건부 형식을 설정할 수 없습니다.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "셀 중간점", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "내부 세로 테두리", "SSE.Views.FormatRulesEditDlg.textClear": "지우기", "SSE.Views.FormatRulesEditDlg.textColor": "글꼴색", "SSE.Views.FormatRulesEditDlg.textContext": "문맥", "SSE.Views.FormatRulesEditDlg.textCustom": "사용자 정의", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "대각선 아래쪽 테두리", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "대각선 위쪽 테두리", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "유효한 공식을 입력하세요.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "입력한 수식은 숫자, 날짜, 시간 또는 문자열로 계산되지 않습니다.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "고정 값을 입력합니다.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "입력 값이 숫자, 날짜, 시간 또는 문자열이 아닙니다.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "{0} 고정 값은 {1} 고정 값보다 커야 합니다.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "{0}에서 {1} 사이의 숫자를 입력합니다.", "SSE.Views.FormatRulesEditDlg.textFill": "채우기", "SSE.Views.FormatRulesEditDlg.textFormat": "서식", "SSE.Views.FormatRulesEditDlg.textFormula": "수식", "SSE.Views.FormatRulesEditDlg.textGradient": "그라디언트", "SSE.Views.FormatRulesEditDlg.textIconLabel": "{0}{1}시 &", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "{0}{1}시", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "고정값일때", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "하나 이상의 아이콘 범위가 겹칩니다. <br>범위가 겹치지 않도록 아이콘 참조 대상 값을 조정합니다.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "아이콘 스타일", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "테두리 안쪽", "SSE.Views.FormatRulesEditDlg.textInvalid": "유효하지 않은 참조 대상", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "오류! 셀 범위가 잘못되었습니다.", "SSE.Views.FormatRulesEditDlg.textItalic": "기울림꼴", "SSE.Views.FormatRulesEditDlg.textItem": "아이템", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "왼쪽에서 오른쪽으로", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "왼쪽 테두리", "SSE.Views.FormatRulesEditDlg.textLongBar": "가장 긴 막대", "SSE.Views.FormatRulesEditDlg.textMaximum": "최대값", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "최고점", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "내부 수평 테두리", "SSE.Views.FormatRulesEditDlg.textMidpoint": "중간점", "SSE.Views.FormatRulesEditDlg.textMinimum": "최소값", "SSE.Views.FormatRulesEditDlg.textMinpoint": "최저점", "SSE.Views.FormatRulesEditDlg.textNegative": "음수", "SSE.Views.FormatRulesEditDlg.textNewColor": "새로운 사용자 정의 색 추가", "SSE.Views.FormatRulesEditDlg.textNoBorders": "테두리 없음", "SSE.Views.FormatRulesEditDlg.textNone": "없음", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "하나 이상의 지정된 고정 값이 유효한 백분율이 아닙니다.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "{0} 지정된 값은 유효한 백분율이 아닙니다.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "하나 이상의 지정된 고정 값이 유효한 백분위수가 아닙니다.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "{0} 지정된 값은 유효한 백분위수가 아닙니다.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "바깥쪽 테두리", "SSE.Views.FormatRulesEditDlg.textPercent": "백분율", "SSE.Views.FormatRulesEditDlg.textPercentile": "백분위수", "SSE.Views.FormatRulesEditDlg.textPosition": "위치", "SSE.Views.FormatRulesEditDlg.textPositive": "정수", "SSE.Views.FormatRulesEditDlg.textPresets": "기본값", "SSE.Views.FormatRulesEditDlg.textPreview": "미리보기", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "색상 레이블, 데이터 열 및 아이콘 집합에 대한 조건부 서식 표준을 설정하기 위해 상대 참조를 사용할 수 없습니다.", "SSE.Views.FormatRulesEditDlg.textReverse": "아이콘 순서 반전", "SSE.Views.FormatRulesEditDlg.textRight2Left": "오른쪽에서 왼쪽으로", "SSE.Views.FormatRulesEditDlg.textRightBorders": "오른쪽 테두리", "SSE.Views.FormatRulesEditDlg.textRule": "규칙", "SSE.Views.FormatRulesEditDlg.textSameAs": "양수와 같음", "SSE.Views.FormatRulesEditDlg.textSelectData": "데이터 선택", "SSE.Views.FormatRulesEditDlg.textShortBar": "가장 잛은 열", "SSE.Views.FormatRulesEditDlg.textShowBar": "막대만 표시", "SSE.Views.FormatRulesEditDlg.textShowIcon": "아이콘만 표시", "SSE.Views.FormatRulesEditDlg.textSingleRef": "이 유형의 참조는 조건부 형식 수식에서 사용할 수 없습니다. <br>참조를 단일 셀로 변경하거나 =SUM(A1:B5)와 같은 워크시트 수식으로 설정하십시오.", "SSE.Views.FormatRulesEditDlg.textSolid": "실선", "SSE.Views.FormatRulesEditDlg.textStrikeout": "취소선", "SSE.Views.FormatRulesEditDlg.textSubscript": "아래 첨자", "SSE.Views.FormatRulesEditDlg.textSuperscript": "위첨자", "SSE.Views.FormatRulesEditDlg.textTopBorders": "위쪽 테두리", "SSE.Views.FormatRulesEditDlg.textUnderline": "밑줄", "SSE.Views.FormatRulesEditDlg.tipBorders": "테두리", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "숫자 형식", "SSE.Views.FormatRulesEditDlg.txtAccounting": "회계", "SSE.Views.FormatRulesEditDlg.txtCurrency": "통화", "SSE.Views.FormatRulesEditDlg.txtDate": "날짜", "SSE.Views.FormatRulesEditDlg.txtDateLong": "확장된 날짜 형식", "SSE.Views.FormatRulesEditDlg.txtDateShort": "간단한 날짜 형식", "SSE.Views.FormatRulesEditDlg.txtEmpty": "이 입력란은 필수 항목입니다.", "SSE.Views.FormatRulesEditDlg.txtFraction": "분수", "SSE.Views.FormatRulesEditDlg.txtGeneral": "일반", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "아이콘 없음", "SSE.Views.FormatRulesEditDlg.txtNumber": "숫자", "SSE.Views.FormatRulesEditDlg.txtPercentage": "백분율", "SSE.Views.FormatRulesEditDlg.txtScientific": "지수", "SSE.Views.FormatRulesEditDlg.txtText": "텍스트", "SSE.Views.FormatRulesEditDlg.txtTime": "시간", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "포맷 규칙을 편집", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "새로운 형식 규칙", "SSE.Views.FormatRulesManagerDlg.guestText": "게스트", "SSE.Views.FormatRulesManagerDlg.lockText": "잠김", "SSE.Views.FormatRulesManagerDlg.text1Above": "표준편차 1이상 평균", "SSE.Views.FormatRulesManagerDlg.text1Below": "표준편차 1이하 평균", "SSE.Views.FormatRulesManagerDlg.text2Above": "표준편차 2이상 평균", "SSE.Views.FormatRulesManagerDlg.text2Below": "표준편차 2이하 평균", "SSE.Views.FormatRulesManagerDlg.text3Above": "표준편차 3이상 평균", "SSE.Views.FormatRulesManagerDlg.text3Below": "표준편차 3이하 평균", "SSE.Views.FormatRulesManagerDlg.textAbove": "평균 이상", "SSE.Views.FormatRulesManagerDlg.textApply": "적용", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "셀 시작 값", "SSE.Views.FormatRulesManagerDlg.textBelow": "평균 이하", "SSE.Views.FormatRulesManagerDlg.textBetween": "{0} 과 {1} 사이", "SSE.Views.FormatRulesManagerDlg.textCellValue": "셀 값", "SSE.Views.FormatRulesManagerDlg.textColorScale": "그라데이션 색상 스케일", "SSE.Views.FormatRulesManagerDlg.textContains": "셀 설정 값 포함", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "셀 값이 비어 있습니다", "SSE.Views.FormatRulesManagerDlg.textContainsError": "셀에 오류가 있습니다", "SSE.Views.FormatRulesManagerDlg.textDelete": "삭제", "SSE.Views.FormatRulesManagerDlg.textDown": "규칙을 아래로 이동", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "중복 값", "SSE.Views.FormatRulesManagerDlg.textEdit": "편집", "SSE.Views.FormatRulesManagerDlg.textEnds": "셀 설정은 다음으로 끝납니다.", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "다음의 값과 동일한 또는 평균 이상", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "평균 이하", "SSE.Views.FormatRulesManagerDlg.textFormat": "서식", "SSE.Views.FormatRulesManagerDlg.textIconSet": "아이콘 셋", "SSE.Views.FormatRulesManagerDlg.textNew": "새로만들기", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "{0} 과 {1} 사이를 제외", "SSE.Views.FormatRulesManagerDlg.textNotContains": "셀 설정 값에 포함되지 않음", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "셀 값이 비어 있지 않습니다", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "셀에 오류가 없습니다", "SSE.Views.FormatRulesManagerDlg.textRules": "규칙", "SSE.Views.FormatRulesManagerDlg.textScope": "규칙 형식 표시", "SSE.Views.FormatRulesManagerDlg.textSelectData": "데이터 선택", "SSE.Views.FormatRulesManagerDlg.textSelection": "현재 섹션", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "이 피벗", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "이 워크시트", "SSE.Views.FormatRulesManagerDlg.textThisTable": "이 표", "SSE.Views.FormatRulesManagerDlg.textUnique": "고유값", "SSE.Views.FormatRulesManagerDlg.textUp": "규칙을 위로 이동", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "이 요소는 다른 사용자가 편집하고 있습니다.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "조건부 서식", "SSE.Views.FormatSettingsDialog.textCategory": "카테고리", "SSE.Views.FormatSettingsDialog.textDecimal": "소수 자릿수", "SSE.Views.FormatSettingsDialog.textFormat": "형식", "SSE.Views.FormatSettingsDialog.textLinked": "소스 링크", "SSE.Views.FormatSettingsDialog.textSeparator": "1000 단위 구분 기호 사용", "SSE.Views.FormatSettingsDialog.textSymbols": "심볼", "SSE.Views.FormatSettingsDialog.textTitle": "숫자 형식", "SSE.Views.FormatSettingsDialog.txtAccounting": "회계", "SSE.Views.FormatSettingsDialog.txtAs10": "분모를 10으로 (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "분모를 100으로 (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "분모를 16으로 (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "분모를 2로 (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "분모를 4로 (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "분모를 8로 (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "통화", "SSE.Views.FormatSettingsDialog.txtCustom": "사용자 지정", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "사용자 지정 숫자 형식을 주의해서 입력하십시오. 스프레드시트 편집기는 xlsx 파일에 영향을 줄 수 있는 사용자 정의 형식의 오류를 확인하지 않습니다.", "SSE.Views.FormatSettingsDialog.txtDate": "날짜", "SSE.Views.FormatSettingsDialog.txtFraction": "분수", "SSE.Views.FormatSettingsDialog.txtGeneral": "일반", "SSE.Views.FormatSettingsDialog.txtNone": "없음", "SSE.Views.FormatSettingsDialog.txtNumber": "숫자", "SSE.Views.FormatSettingsDialog.txtPercentage": "백분율", "SSE.Views.FormatSettingsDialog.txtSample": "보기 :", "SSE.Views.FormatSettingsDialog.txtScientific": "지수", "SSE.Views.FormatSettingsDialog.txtText": "텍스트", "SSE.Views.FormatSettingsDialog.txtTime": "시간", "SSE.Views.FormatSettingsDialog.txtUpto1": "한 자릿수 분모 (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "두 자릿수 분모 (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "세 자릿수 분모 (131/135)", "SSE.Views.FormulaDialog.sDescription": "설명", "SSE.Views.FormulaDialog.textGroupDescription": "기능 그룹 선택", "SSE.Views.FormulaDialog.textListDescription": "함수 선택", "SSE.Views.FormulaDialog.txtRecommended": "추천", "SSE.Views.FormulaDialog.txtSearch": "검색", "SSE.Views.FormulaDialog.txtTitle": "함수 삽입", "SSE.Views.FormulaTab.capBtnRemoveArr": "화살표 제거", "SSE.Views.FormulaTab.capBtnTraceDep": "종속 항목 추적", "SSE.Views.FormulaTab.capBtnTracePrec": "선행 항목 추적", "SSE.Views.FormulaTab.textAutomatic": "자동", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "현재 시트 계산", "SSE.Views.FormulaTab.textCalculateWorkbook": "통합 문서 계산", "SSE.Views.FormulaTab.textManual": "수동", "SSE.Views.FormulaTab.tipCalculate": "계산하다", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "전체 통합 문서 계산", "SSE.Views.FormulaTab.tipRemoveArr": "선행 추적 또는 종속 추적으로 그려진 화살표 제거", "SSE.Views.FormulaTab.tipShowFormulas": "결과 값 대신 각 셀의 수식 표시", "SSE.Views.FormulaTab.tipTraceDep": "선택한 셀의 값에 영향을 받는 셀을 나타내는 화살표 표시", "SSE.Views.FormulaTab.tipTracePrec": "선택한 셀의 값에 영향을 미치는 셀을 나타내는 화살표 표시", "SSE.Views.FormulaTab.tipWatch": "Watch 창 목록에 셀을 추가하세요.", "SSE.Views.FormulaTab.txtAdditional": "추가", "SSE.Views.FormulaTab.txtAutosum": "자동 합계", "SSE.Views.FormulaTab.txtAutosumTip": "합계", "SSE.Views.FormulaTab.txtCalculation": "계산", "SSE.Views.FormulaTab.txtFormula": "함수", "SSE.Views.FormulaTab.txtFormulaTip": "함수 삽입", "SSE.Views.FormulaTab.txtMore": "더 많은 기능", "SSE.Views.FormulaTab.txtRecent": "최근 사용된", "SSE.Views.FormulaTab.txtRemDep": "종속 화살표 제거", "SSE.Views.FormulaTab.txtRemPrec": "선행 화살표 제거", "SSE.Views.FormulaTab.txtShowFormulas": "수식 표시", "SSE.Views.FormulaTab.txtWatch": "모니터링 창", "SSE.Views.FormulaWizard.textAny": "어떤 것", "SSE.Views.FormulaWizard.textArgument": "인수", "SSE.Views.FormulaWizard.textFunction": "함수", "SSE.Views.FormulaWizard.textFunctionRes": "함수의 결과", "SSE.Views.FormulaWizard.textHelp": "이 기능에 대한 도움말", "SSE.Views.FormulaWizard.textLogical": "\n논리적", "SSE.Views.FormulaWizard.textNoArgs": "함수에 매개변수가 없습니다.", "SSE.Views.FormulaWizard.textNoArgsDesc": "this argument has no description", "SSE.Views.FormulaWizard.textNumber": "숫자", "SSE.Views.FormulaWizard.textReadMore": "Read more", "SSE.Views.FormulaWizard.textRef": "참조", "SSE.Views.FormulaWizard.textText": "텍스트", "SSE.Views.FormulaWizard.textTitle": "함수의 인수", "SSE.Views.FormulaWizard.textValue": "수식의 결과", "SSE.Views.GoalSeekDlg.textChangingCell": "By changing cell", "SSE.Views.GoalSeekDlg.textDataRangeError": "The formula is missing a range", "SSE.Views.GoalSeekDlg.textMustContainFormula": "The cell must contain a formula", "SSE.Views.GoalSeekDlg.textMustContainValue": "Cell must contain a value", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "셀의 공식 결과는 숫자여야 합니다.", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Reference must be to a single cell", "SSE.Views.GoalSeekDlg.textSelectData": "Select data", "SSE.Views.GoalSeekDlg.textSetCell": "Set cell", "SSE.Views.GoalSeekDlg.textTitle": "Goal seek", "SSE.Views.GoalSeekDlg.textToValue": "To value", "SSE.Views.GoalSeekDlg.txtEmpty": "This field is required", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.GoalSeekStatusDlg.textContinue": "Continue", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Current value:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Goal seeking with cell {0} found a solution.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Goal seeking with cell {0} may not have found a solution.", "SSE.Views.GoalSeekStatusDlg.textPause": "Pause", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Goal seeking with cell {0} on iteration #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "Step", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Target value:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Goal seek status", "SSE.Views.HeaderFooterDialog.textAlign": "페이지 본문 영역에 정렬", "SSE.Views.HeaderFooterDialog.textAll": "전체 페이지", "SSE.Views.HeaderFooterDialog.textBold": "굵은체", "SSE.Views.HeaderFooterDialog.textCenter": "중앙", "SSE.Views.HeaderFooterDialog.textColor": "글꼴색", "SSE.Views.HeaderFooterDialog.textDate": "날짜", "SSE.Views.HeaderFooterDialog.textDiffFirst": "첫 페이지를 다르게 지정", "SSE.Views.HeaderFooterDialog.textDiffOdd": "홀수 및 짝수 페이지 다르게 지정", "SSE.Views.HeaderFooterDialog.textEven": "짝수 페이지", "SSE.Views.HeaderFooterDialog.textFileName": "파일 이름", "SSE.Views.HeaderFooterDialog.textFirst": "첫 페이지", "SSE.Views.HeaderFooterDialog.textFooter": "꼬리말", "SSE.Views.HeaderFooterDialog.textHeader": "머리글", "SSE.Views.HeaderFooterDialog.textImage": "그림", "SSE.Views.HeaderFooterDialog.textInsert": "삽입", "SSE.Views.HeaderFooterDialog.textItalic": "기울림꼴", "SSE.Views.HeaderFooterDialog.textLeft": "왼쪽", "SSE.Views.HeaderFooterDialog.textMaxError": "입력한 텍스트 문자열이 너무 깁니다. 사용되는 문자 수를 줄이십시오.", "SSE.Views.HeaderFooterDialog.textNewColor": "새 맞춤 색상 추가", "SSE.Views.HeaderFooterDialog.textOdd": "홀수 페이지", "SSE.Views.HeaderFooterDialog.textPageCount": "페이지 수", "SSE.Views.HeaderFooterDialog.textPageNum": "페이지 번호", "SSE.Views.HeaderFooterDialog.textPresets": "기본값", "SSE.Views.HeaderFooterDialog.textRight": "오른쪽", "SSE.Views.HeaderFooterDialog.textScale": "문서에 맞게 조정", "SSE.Views.HeaderFooterDialog.textSheet": "시트 이름", "SSE.Views.HeaderFooterDialog.textStrikeout": "취소선", "SSE.Views.HeaderFooterDialog.textSubscript": "아래 첨자", "SSE.Views.HeaderFooterDialog.textSuperscript": "위첨자", "SSE.Views.HeaderFooterDialog.textTime": "시간", "SSE.Views.HeaderFooterDialog.textTitle": "머리글/바닥글 설정", "SSE.Views.HeaderFooterDialog.textUnderline": "밑줄", "SSE.Views.HeaderFooterDialog.tipFontName": "글꼴", "SSE.Views.HeaderFooterDialog.tipFontSize": "글꼴 크기", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "표시", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "링크 대상", "SSE.Views.HyperlinkSettingsDialog.strRange": "Range", "SSE.Views.HyperlinkSettingsDialog.strSheet": "시트", "SSE.Views.HyperlinkSettingsDialog.textCopy": "복사", "SSE.Views.HyperlinkSettingsDialog.textDefault": "선택한 범위", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "여기에 캡션 입력", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "여기에 링크 입력", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "여기에 툴팁 입력", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "외부 링크", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "링크 가져오기", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "내부 데이터 범위", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "오류! 셀 범위가 잘못되었습니다.", "SSE.Views.HyperlinkSettingsDialog.textNames": "정의 된 이름", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "데이터 선택", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "SSE.Views.HyperlinkSettingsDialog.textSheets": "시트", "SSE.Views.HyperlinkSettingsDialog.textTipText": "스크린 팁 텍스트", "SSE.Views.HyperlinkSettingsDialog.textTitle": "하이퍼 링크 설정", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "이 입력란은 필수 항목", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "이 필드는 \"http://www.example.com\"형식의 URL이어야합니다.", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "이 필드는 2083 자로 제한되어 있습니다", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "SSE.Views.ImageSettings.strTransparency": "Opacity", "SSE.Views.ImageSettings.textAdvanced": "고급 설정", "SSE.Views.ImageSettings.textCrop": "자르기", "SSE.Views.ImageSettings.textCropFill": "채우기", "SSE.Views.ImageSettings.textCropFit": "맞춤", "SSE.Views.ImageSettings.textCropToShape": "도형에 맞게 자르기", "SSE.Views.ImageSettings.textEdit": "편집", "SSE.Views.ImageSettings.textEditObject": "개체 편집", "SSE.Views.ImageSettings.textFlip": "대칭", "SSE.Views.ImageSettings.textFromFile": "파일로부터", "SSE.Views.ImageSettings.textFromStorage": "스토리지로 부터", "SSE.Views.ImageSettings.textFromUrl": "URL로부터", "SSE.Views.ImageSettings.textHeight": "높이", "SSE.Views.ImageSettings.textHint270": "왼쪽으로 90도 회전", "SSE.Views.ImageSettings.textHint90": "오른쪽으로 90도 회전", "SSE.Views.ImageSettings.textHintFlipH": "좌우대칭", "SSE.Views.ImageSettings.textHintFlipV": "상하대칭", "SSE.Views.ImageSettings.textInsert": "이미지 바꾸기", "SSE.Views.ImageSettings.textKeepRatio": "상수 비율", "SSE.Views.ImageSettings.textOriginalSize": "실제 크기", "SSE.Views.ImageSettings.textRecentlyUsed": "최근 사용된", "SSE.Views.ImageSettings.textResetCrop": "Reset crop", "SSE.Views.ImageSettings.textRotate90": "90도 회전", "SSE.Views.ImageSettings.textRotation": "회전", "SSE.Views.ImageSettings.textSize": "크기", "SSE.Views.ImageSettings.textWidth": "너비", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "셀을 이동하거나 크기를 조정하지 마십시오.", "SSE.Views.ImageSettingsAdvanced.textAlt": "대체 텍스트", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "설명", "SSE.Views.ImageSettingsAdvanced.textAltTip": "시각적 개체 정보의 교체는 텍스트 표현을 기반으로 하며 시각 또는 인지 장애가 있는 사람들이 이미지, 자동 모양, 차트 또는 표에 포함된 정보를 더 잘 이해할 수 있도록 읽어줍니다.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "제목", "SSE.Views.ImageSettingsAdvanced.textAngle": "각도", "SSE.Views.ImageSettingsAdvanced.textFlipped": "대칭됨", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "수평", "SSE.Views.ImageSettingsAdvanced.textOneCell": "이동하지만 셀별로 크기 조정되지 않음", "SSE.Views.ImageSettingsAdvanced.textRotation": "회전", "SSE.Views.ImageSettingsAdvanced.textSnap": "셀 잠그기", "SSE.Views.ImageSettingsAdvanced.textTitle": "이미지 - 고급 설정", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "셀 이동 및 크기 조정", "SSE.Views.ImageSettingsAdvanced.textVertically": "세로", "SSE.Views.ImportFromXmlDialog.textDestination": "데이터를 저장할 위치를 선택하세요.", "SSE.Views.ImportFromXmlDialog.textExist": "존재하는 워크시트", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "유효하지 않은 셀 범위", "SSE.Views.ImportFromXmlDialog.textNew": "신규 워크시트", "SSE.Views.ImportFromXmlDialog.textSelectData": "데이터 선택", "SSE.Views.ImportFromXmlDialog.textTitle": "데이터 가져오기", "SSE.Views.ImportFromXmlDialog.txtEmpty": "이 입력란은 필수 항목", "SSE.Views.LeftMenu.ariaLeftMenu": "Left menu", "SSE.Views.LeftMenu.tipAbout": "정보", "SSE.Views.LeftMenu.tipChat": "채팅", "SSE.Views.LeftMenu.tipComments": "코멘트", "SSE.Views.LeftMenu.tipFile": "파일", "SSE.Views.LeftMenu.tipPlugins": "플러그인", "SSE.Views.LeftMenu.tipSearch": "Search", "SSE.Views.LeftMenu.tipSpellcheck": "맞춤법 검사", "SSE.Views.LeftMenu.tipSupport": "피드백 및 지원", "SSE.Views.LeftMenu.txtDeveloper": "개발자 모드", "SSE.Views.LeftMenu.txtEditor": "스프레드시트 편집기", "SSE.Views.LeftMenu.txtLimit": "접근 제한", "SSE.Views.LeftMenu.txtTrial": "시험 모드", "SSE.Views.LeftMenu.txtTrialDev": "개발자 모드 시도", "SSE.Views.MacroDialog.textMacro": "매크로 이름", "SSE.Views.MacroDialog.textTitle": "매크로 지정", "SSE.Views.MainSettingsPrint.okButtonText": "저장", "SSE.Views.MainSettingsPrint.strBottom": "Bottom", "SSE.Views.MainSettingsPrint.strLandscape": "Landscape", "SSE.Views.MainSettingsPrint.strLeft": "Left", "SSE.Views.MainSettingsPrint.strMargins": "여백", "SSE.Views.MainSettingsPrint.strPortrait": "Portrait", "SSE.Views.MainSettingsPrint.strPrint": "인쇄", "SSE.Views.MainSettingsPrint.strPrintTitles": "제목 인쇄", "SSE.Views.MainSettingsPrint.strRight": "Right", "SSE.Views.MainSettingsPrint.strTop": "Top", "SSE.Views.MainSettingsPrint.textActualSize": "실제 크기", "SSE.Views.MainSettingsPrint.textCustom": "사용자 정의", "SSE.Views.MainSettingsPrint.textCustomOptions": "사용자 정의 옵션", "SSE.Views.MainSettingsPrint.textFitCols": "한 페이지에 모든 열 맞추기", "SSE.Views.MainSettingsPrint.textFitPage": "한 페이지에 시트 맞추기", "SSE.Views.MainSettingsPrint.textFitRows": "한 페이지에 모든 행 맞추기", "SSE.Views.MainSettingsPrint.textPageOrientation": "페이지 방향", "SSE.Views.MainSettingsPrint.textPageScaling": "Sc<PERSON>", "SSE.Views.MainSettingsPrint.textPageSize": "페이지 크기", "SSE.Views.MainSettingsPrint.textPrintGrid": "눈금 선 인쇄", "SSE.Views.MainSettingsPrint.textPrintHeadings": "행 및 열 머리글 인쇄", "SSE.Views.MainSettingsPrint.textRepeat": "반복...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "왼쪽 열을 반복", "SSE.Views.MainSettingsPrint.textRepeatTop": "위의 행을 반복", "SSE.Views.MainSettingsPrint.textSettings": "설정", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "기존 명명 된 범위를 편집 할 수 없으며 일부는 편집 중임에 따라 현재 명명 된 범위를 만들 수 없습니다.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "정의 된 이름", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "경고", "SSE.Views.NamedRangeEditDlg.strWorkbook": "통합 문서", "SSE.Views.NamedRangeEditDlg.textDataRange": "데이터 범위", "SSE.Views.NamedRangeEditDlg.textExistName": "오류! 같은 이름의 범위가 이미 있습니다", "SSE.Views.NamedRangeEditDlg.textInvalidName": "이름은 문자 또는 밑줄로 시작해야하며 잘못된 문자를 포함해서는 안됩니다.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "오류! 잘못된 셀 범위", "SSE.Views.NamedRangeEditDlg.textIsLocked": "오류!이 요소는 다른 사용자가 편집하고 있습니다.", "SSE.Views.NamedRangeEditDlg.textName": "Name", "SSE.Views.NamedRangeEditDlg.textReservedName": "사용하려는 이름이 이미 셀 수식에서 참조되어 있습니다. 다른 이름을 사용하십시오.", "SSE.Views.NamedRangeEditDlg.textScope": "범위", "SSE.Views.NamedRangeEditDlg.textSelectData": "데이터 선택", "SSE.Views.NamedRangeEditDlg.txtEmpty": "이 입력란은 필수 항목", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "이름 편집", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "새 이름", "SSE.Views.NamedRangePasteDlg.textNames": "Named Ranges", "SSE.Views.NamedRangePasteDlg.txtTitle": "붙여 넣기 이름", "SSE.Views.NameManagerDlg.closeButtonText": "닫기", "SSE.Views.NameManagerDlg.guestText": "Guest", "SSE.Views.NameManagerDlg.lockText": "잠김", "SSE.Views.NameManagerDlg.textDataRange": "데이터 범위", "SSE.Views.NameManagerDlg.textDelete": "삭제", "SSE.Views.NameManagerDlg.textEdit": "편집", "SSE.Views.NameManagerDlg.textEmpty": "명명 된 범위가 아직 생성되지 않았습니다. <br> 명명 된 하나 이상의 범위를 만들고이 필드에 나타납니다.", "SSE.Views.NameManagerDlg.textFilter": "필터", "SSE.Views.NameManagerDlg.textFilterAll": "모두", "SSE.Views.NameManagerDlg.textFilterDefNames": "정의 된 이름", "SSE.Views.NameManagerDlg.textFilterSheet": "이름이 시트로 범위 지정됨", "SSE.Views.NameManagerDlg.textFilterTableNames": "테이블 이름", "SSE.Views.NameManagerDlg.textFilterWorkbook": "이름이 통합 문서로 범위 지정됨", "SSE.Views.NameManagerDlg.textNew": "New", "SSE.Views.NameManagerDlg.textnoNames": "필터와 일치하는 명명 된 범위를 찾을 수 없습니다.", "SSE.Views.NameManagerDlg.textRanges": "Named Ranges", "SSE.Views.NameManagerDlg.textScope": "범위", "SSE.Views.NameManagerDlg.textWorkbook": "통합 문서", "SSE.Views.NameManagerDlg.tipIsLocked": "이 요소는 다른 사용자가 편집하고 있습니다.", "SSE.Views.NameManagerDlg.txtTitle": "이름 관리자", "SSE.Views.NameManagerDlg.warnDelete": "이름 {0}을 삭제 하시겠습니까?", "SSE.Views.PageMarginsDialog.textBottom": "바닥", "SSE.Views.PageMarginsDialog.textCenter": "Center on page", "SSE.Views.PageMarginsDialog.textHor": "Horizontally", "SSE.Views.PageMarginsDialog.textLeft": "왼쪽", "SSE.Views.PageMarginsDialog.textRight": "오른쪽", "SSE.Views.PageMarginsDialog.textTitle": "여백", "SSE.Views.PageMarginsDialog.textTop": "위", "SSE.Views.PageMarginsDialog.textVert": "Vertically", "SSE.Views.PageMarginsDialog.textWarning": "경고", "SSE.Views.PageMarginsDialog.warnCheckMargings": "여백이 잘못되었습니다", "SSE.Views.ParagraphSettings.strLineHeight": "줄 간격", "SSE.Views.ParagraphSettings.strParagraphSpacing": "단락 간격", "SSE.Views.ParagraphSettings.strSpacingAfter": "이후", "SSE.Views.ParagraphSettings.strSpacingBefore": "이전", "SSE.Views.ParagraphSettings.textAdvanced": "고급 설정", "SSE.Views.ParagraphSettings.textAt": "At", "SSE.Views.ParagraphSettings.textAtLeast": "적어도", "SSE.Views.ParagraphSettings.textAuto": "배수", "SSE.Views.ParagraphSettings.textExact": "정확히", "SSE.Views.ParagraphSettings.txtAutoText": "Auto", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "지정한 탭이이 필드에 나타납니다", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "모든 대문자", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "이중 취소선", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "들여쓰기", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Left", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "줄 간격", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Right", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "이후", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "이전", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "첫줄", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "~로", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "글꼴", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "들여쓰기 및 간격", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "작은 대문자", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "간격", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "취소선", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscript", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Superscript", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "탭", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "정렬", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "배수", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "문자 간격", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "기본 탭", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "효과", "SSE.Views.ParagraphSettingsAdvanced.textExact": "고정", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "머리글 행", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "둘째 줄 이하", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "균등분할", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(없음)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "제거", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "모두 제거", "SSE.Views.ParagraphSettingsAdvanced.textSet": "지정", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Center", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "왼쪽", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "탭 위치", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Right", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "단락 - 고급 설정", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "자동", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Delete", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "Duplicate", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Edit", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Formula", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Items Name", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "New", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Calculated Items in", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "같음", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "다음 문자열로 끝나지 않음", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "포함", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "포함하지 않음", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "해당 범위", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "제외 범위", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "같지 않음", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "보다 큼", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "크거나 같음", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "미만", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "작거나 같음", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "~와 함께 시작하다.\n~로 시작하다", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "다음 문자에서 시작하기", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "종료", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "레이블이 지정된 항목을 표시:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "다음의 항목이 표시：", "SSE.Views.PivotDigitalFilterDialog.textUse1": "? 를 사용하여 단일 문자를 나타낼 수 있습니다.", "SSE.Views.PivotDigitalFilterDialog.textUse2": "모든 문자를 표시하려면 *를 사용하십시오.", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "그리고", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "라벨 필터", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "값 필터", "SSE.Views.PivotGroupDialog.textAuto": "자동", "SSE.Views.PivotGroupDialog.textBy": "작성", "SSE.Views.PivotGroupDialog.textDays": "일", "SSE.Views.PivotGroupDialog.textEnd": "종료", "SSE.Views.PivotGroupDialog.textError": "이 필드는 숫자 여야합니다", "SSE.Views.PivotGroupDialog.textGreaterError": "끝 번호는 시작 번호보다 커야 합니다.", "SSE.Views.PivotGroupDialog.textHour": "시간", "SSE.Views.PivotGroupDialog.textMin": "분", "SSE.Views.PivotGroupDialog.textMonth": "월", "SSE.Views.PivotGroupDialog.textNumDays": "일자", "SSE.Views.PivotGroupDialog.textQuart": "분기", "SSE.Views.PivotGroupDialog.textSec": "초", "SSE.Views.PivotGroupDialog.textStart": "시작 시간", "SSE.Views.PivotGroupDialog.textYear": "년", "SSE.Views.PivotGroupDialog.txtTitle": "그룹핑", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "You can use Calculated Items for basic calculations between different items within a single field", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Insert into formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "<PERSON><PERSON>", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Item name", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Items", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Read more", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Insert Calculated Item in", "SSE.Views.PivotSettings.textAdvanced": "고급 설정", "SSE.Views.PivotSettings.textColumns": "열", "SSE.Views.PivotSettings.textFields": "필드 선택", "SSE.Views.PivotSettings.textFilters": "필터", "SSE.Views.PivotSettings.textRows": "행", "SSE.Views.PivotSettings.textValues": "값", "SSE.Views.PivotSettings.txtAddColumn": "열에 추가", "SSE.Views.PivotSettings.txtAddFilter": "필터에 추가", "SSE.Views.PivotSettings.txtAddRow": "행에 추가", "SSE.Views.PivotSettings.txtAddValues": "값에 추가", "SSE.Views.PivotSettings.txtFieldSettings": "필드 세팅", "SSE.Views.PivotSettings.txtMoveBegin": "시작점으로 이동", "SSE.Views.PivotSettings.txtMoveColumn": "열로 이동", "SSE.Views.PivotSettings.txtMoveDown": "아래로 이동", "SSE.Views.PivotSettings.txtMoveEnd": "끝으로 이동", "SSE.Views.PivotSettings.txtMoveFilter": "필터로 이동", "SSE.Views.PivotSettings.txtMoveRow": "행으로 이동", "SSE.Views.PivotSettings.txtMoveUp": "위로 이동", "SSE.Views.PivotSettings.txtMoveValues": "값으로 이동", "SSE.Views.PivotSettings.txtRemove": "필드 삭제", "SSE.Views.PivotSettingsAdvanced.strLayout": "이름 및 레이아웃", "SSE.Views.PivotSettingsAdvanced.textAlt": "대체 문장", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "세부 설명", "SSE.Views.PivotSettingsAdvanced.textAltTip": "시력이나인지 장애가있는 사람들에게 읽을 수있는 시각적 객체 정보의 대체 텍스트 기반 표현으로 이미지에있는 정보를 더 잘 이해할 수 있도록 도와줍니다. 차트 또는 표. ", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "제목", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "업데이트 시에 열 너비 자동 맞춤", "SSE.Views.PivotSettingsAdvanced.textDataRange": "데이터 범위", "SSE.Views.PivotSettingsAdvanced.textDataSource": "데이터 소스", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "보고서 필터 영역에 필드 표시", "SSE.Views.PivotSettingsAdvanced.textDown": "위에서 아래로", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "종합 합계", "SSE.Views.PivotSettingsAdvanced.textHeaders": "필드 제목", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "오류! 셀 범위가 잘못되었습니다.", "SSE.Views.PivotSettingsAdvanced.textOver": "종료 후 아래로", "SSE.Views.PivotSettingsAdvanced.textSelectData": "데이터 선택", "SSE.Views.PivotSettingsAdvanced.textShowCols": "열에 표시", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "행과 열의 필드 헤더 표시", "SSE.Views.PivotSettingsAdvanced.textShowRows": "행에 표시", "SSE.Views.PivotSettingsAdvanced.textTitle": "피벗테이블-고급설정", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "열별 필터 필드 보고서", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "행별 필터 필드 보고서", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "이 입력란은 필수 항목입니다.", "SSE.Views.PivotSettingsAdvanced.txtName": "이름", "SSE.Views.PivotShowDetailDialog.textDescription": "Choose the field containing the detail you want to show:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Show Detail", "SSE.Views.PivotTable.capBlankRows": "빈 행", "SSE.Views.PivotTable.capGrandTotals": "종합 합계", "SSE.Views.PivotTable.capLayout": "레이아웃 리포트", "SSE.Views.PivotTable.capSubtotals": "서브 합계", "SSE.Views.PivotTable.mniBottomSubtotals": "그룹 하단에 모든 서브 합계를 보여주기", "SSE.Views.PivotTable.mniInsertBlankLine": "각 아이템 다음에 빈 라인을 추가하기", "SSE.Views.PivotTable.mniLayoutCompact": "컴팩트 폼으로 보여주기", "SSE.Views.PivotTable.mniLayoutNoRepeat": "모든 아이템 레이블을 반복하지 말 것", "SSE.Views.PivotTable.mniLayoutOutline": "아웃라인 폼으로 보여주기", "SSE.Views.PivotTable.mniLayoutRepeat": "모든 아이템 레이블 반복", "SSE.Views.PivotTable.mniLayoutTabular": "태블러 폼으로 보여주기", "SSE.Views.PivotTable.mniNoSubtotals": "서브 합계를 보여주지 말 것", "SSE.Views.PivotTable.mniOffTotals": "행과 열에 적용", "SSE.Views.PivotTable.mniOnColumnsTotals": "열에만 적용", "SSE.Views.PivotTable.mniOnRowsTotals": "행에만 적용", "SSE.Views.PivotTable.mniOnTotals": "행과 열에 적용", "SSE.Views.PivotTable.mniRemoveBlankLine": "각 아이템 다음에 빈 라인 삭제", "SSE.Views.PivotTable.mniTopSubtotals": "그룹 상단에 모든 서브 합계 보여주기", "SSE.Views.PivotTable.textColBanded": "줄무늬 열", "SSE.Views.PivotTable.textColHeader": "열 머리글", "SSE.Views.PivotTable.textRowBanded": "줄무늬 행", "SSE.Views.PivotTable.textRowHeader": "열 헤더", "SSE.Views.PivotTable.tipCalculatedItems": "Calculated items", "SSE.Views.PivotTable.tipCreatePivot": "피벗 테이블 삽입", "SSE.Views.PivotTable.tipGrandTotals": "종합 합계 보이기 또는 숨기기", "SSE.Views.PivotTable.tipRefresh": "데이터 소스에서 정보를 업데이트 하기", "SSE.Views.PivotTable.tipRefreshCurrent": "현재 표의 데이터 소스로부터 정보 업데이트", "SSE.Views.PivotTable.tipSelect": "전체 피벗 테이블 선택", "SSE.Views.PivotTable.tipSubtotals": "서브 합계 보이기 또는 숨기기", "SSE.Views.PivotTable.txtCalculatedItems": "Calculated Items", "SSE.Views.PivotTable.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.PivotTable.txtCreate": "표 삽입", "SSE.Views.PivotTable.txtExpandEntire": "Expand Entire Field", "SSE.Views.PivotTable.txtGroupPivot_Custom": "사용자 정의", "SSE.Views.PivotTable.txtGroupPivot_Dark": "어두운", "SSE.Views.PivotTable.txtGroupPivot_Light": "밝은", "SSE.Views.PivotTable.txtGroupPivot_Medium": "중", "SSE.Views.PivotTable.txtPivotTable": "피벗 테이블", "SSE.Views.PivotTable.txtRefresh": "새로고침", "SSE.Views.PivotTable.txtRefreshAll": "모두 새로 고침", "SSE.Views.PivotTable.txtSelect": "선택", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "피벗 테이블 어두운 스타일", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "피벗 테이블 밝은 스타일", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "피벗 테이블 중간 스타일", "SSE.Views.PrintSettings.btnDownload": "저장 및 다운로드", "SSE.Views.PrintSettings.btnExport": "저장 및 내보내기", "SSE.Views.PrintSettings.btnPrint": "저장 및 인쇄", "SSE.Views.PrintSettings.strBottom": "Bottom", "SSE.Views.PrintSettings.strLandscape": "Landscape", "SSE.Views.PrintSettings.strLeft": "왼쪽", "SSE.Views.PrintSettings.strMargins": "여백", "SSE.Views.PrintSettings.strPortrait": "Portrait", "SSE.Views.PrintSettings.strPrint": "인쇄", "SSE.Views.PrintSettings.strPrintTitles": "제목 인쇄", "SSE.Views.PrintSettings.strRight": "오른쪽", "SSE.Views.PrintSettings.strShow": "표시", "SSE.Views.PrintSettings.strTop": "Top", "SSE.Views.PrintSettings.textActiveSheets": "활성 시트", "SSE.Views.PrintSettings.textActualSize": "실제 크기", "SSE.Views.PrintSettings.textAllSheets": "모든 시트", "SSE.Views.PrintSettings.textCurrentSheet": "현재 시트", "SSE.Views.PrintSettings.textCustom": "사용자 정의", "SSE.Views.PrintSettings.textCustomOptions": "사용자 정의 옵션", "SSE.Views.PrintSettings.textFitCols": "한 페이지에 모든 열 맞추기", "SSE.Views.PrintSettings.textFitPage": "한 페이지에 시트 맞추기", "SSE.Views.PrintSettings.textFitRows": "한 페이지에 모든 행 맞추기", "SSE.Views.PrintSettings.textHideDetails": "세부 정보 숨기기", "SSE.Views.PrintSettings.textIgnore": "인쇄 영역 무시", "SSE.Views.PrintSettings.textLayout": "레이아웃", "SSE.Views.PrintSettings.textMarginsNarrow": "좁게", "SSE.Views.PrintSettings.textMarginsNormal": "표준", "SSE.Views.PrintSettings.textMarginsWide": "넓게", "SSE.Views.PrintSettings.textPageOrientation": "페이지 방향", "SSE.Views.PrintSettings.textPages": "페이지:", "SSE.Views.PrintSettings.textPageScaling": "Sc<PERSON>", "SSE.Views.PrintSettings.textPageSize": "페이지 크기", "SSE.Views.PrintSettings.textPrintGrid": "눈금 선 인쇄", "SSE.Views.PrintSettings.textPrintHeadings": "행 및 열 머리글 인쇄", "SSE.Views.PrintSettings.textPrintRange": "인쇄 범위", "SSE.Views.PrintSettings.textRange": "범위", "SSE.Views.PrintSettings.textRepeat": "반복...", "SSE.Views.PrintSettings.textRepeatLeft": "왼쪽 열을 반복", "SSE.Views.PrintSettings.textRepeatTop": "위의 행을 반복", "SSE.Views.PrintSettings.textSelection": "선택", "SSE.Views.PrintSettings.textSettings": "시트 설정", "SSE.Views.PrintSettings.textShowDetails": "세부 정보 표시", "SSE.Views.PrintSettings.textShowGrid": "눈금선 표시", "SSE.Views.PrintSettings.textShowHeadings": "행 및 열 머리글을 표시", "SSE.Views.PrintSettings.textTitle": "인쇄 설정", "SSE.Views.PrintSettings.textTitlePDF": "PDF 설정", "SSE.Views.PrintSettings.textTo": "받는 사람", "SSE.Views.PrintSettings.txtMarginsLast": "마지막 사용자 정의", "SSE.Views.PrintTitlesDialog.textFirstCol": "첫째 열", "SSE.Views.PrintTitlesDialog.textFirstRow": "머리글 행", "SSE.Views.PrintTitlesDialog.textFrozenCols": "고정 된 열", "SSE.Views.PrintTitlesDialog.textFrozenRows": "고정된 행", "SSE.Views.PrintTitlesDialog.textInvalidRange": "오류! 셀 범위가 잘못되었습니다.", "SSE.Views.PrintTitlesDialog.textLeft": "왼쪽 열을 반복", "SSE.Views.PrintTitlesDialog.textNoRepeat": "반복 없음", "SSE.Views.PrintTitlesDialog.textRepeat": "반복...", "SSE.Views.PrintTitlesDialog.textSelectRange": "범위 선택", "SSE.Views.PrintTitlesDialog.textTitle": "제목 인쇄", "SSE.Views.PrintTitlesDialog.textTop": "위의 행을 반복", "SSE.Views.PrintWithPreview.txtActiveSheets": "활성 시트", "SSE.Views.PrintWithPreview.txtActualSize": "실제 크기", "SSE.Views.PrintWithPreview.txtAllSheets": "모든 시트", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "모든 시트에 적용", "SSE.Views.PrintWithPreview.txtBothSides": "양면에 인쇄", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "긴 변을 중심으로 페이지를 뒤집다", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "짧은 변을 중심으로 페이지를 뒤집다", "SSE.Views.PrintWithPreview.txtBottom": "바닥", "SSE.Views.PrintWithPreview.txtCopies": "사본", "SSE.Views.PrintWithPreview.txtCurrentSheet": "현재 시트", "SSE.Views.PrintWithPreview.txtCustom": "사용자 정의", "SSE.Views.PrintWithPreview.txtCustomOptions": "사용자 정의 옵션", "SSE.Views.PrintWithPreview.txtEmptyTable": "표가 비어 있어 인쇄할 내용이 없습니다", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "첫 페이지 수:", "SSE.Views.PrintWithPreview.txtFitCols": "한 페이지에 모든 열 맞추기", "SSE.Views.PrintWithPreview.txtFitPage": "한 페이지에 시트 맞추기", "SSE.Views.PrintWithPreview.txtFitRows": "한 페이지에 모든 행 맞추기", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "눈금선 및 글머리", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "머리글/바닥글 설정", "SSE.Views.PrintWithPreview.txtIgnore": "인쇄 영역 무시", "SSE.Views.PrintWithPreview.txtLandscape": "가로 모드", "SSE.Views.PrintWithPreview.txtLeft": "왼쪽", "SSE.Views.PrintWithPreview.txtMargins": "여백", "SSE.Views.PrintWithPreview.txtMarginsLast": "마지막 사용자 정의", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "좁게", "SSE.Views.PrintWithPreview.txtMarginsNormal": "표준", "SSE.Views.PrintWithPreview.txtMarginsWide": "넓게", "SSE.Views.PrintWithPreview.txtOf": "/ {0}", "SSE.Views.PrintWithPreview.txtOneSide": "단면 인쇄", "SSE.Views.PrintWithPreview.txtOneSideDesc": "페이지의 한쪽에만 인쇄", "SSE.Views.PrintWithPreview.txtPage": "페이지", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "페이지 번호가 잘못되었습니다.", "SSE.Views.PrintWithPreview.txtPageOrientation": "페이지 방향", "SSE.Views.PrintWithPreview.txtPages": "페이지:", "SSE.Views.PrintWithPreview.txtPageSize": "페이지 크기", "SSE.Views.PrintWithPreview.txtPortrait": "세로", "SSE.Views.PrintWithPreview.txtPrint": "인쇄", "SSE.Views.PrintWithPreview.txtPrintGrid": "눈금 선 인쇄", "SSE.Views.PrintWithPreview.txtPrintHeadings": "행 및 열 머리글 인쇄", "SSE.Views.PrintWithPreview.txtPrintRange": "인쇄 범위", "SSE.Views.PrintWithPreview.txtPrintSides": "인쇄면", "SSE.Views.PrintWithPreview.txtPrintTitles": "제목 인쇄", "SSE.Views.PrintWithPreview.txtPrintToPDF": "PDF로 인쇄", "SSE.Views.PrintWithPreview.txtRepeat": "반복...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "왼쪽 열을 반복", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "위의 행을 반복", "SSE.Views.PrintWithPreview.txtRight": "오른쪽", "SSE.Views.PrintWithPreview.txtSave": "저장", "SSE.Views.PrintWithPreview.txtScaling": "스케일링", "SSE.Views.PrintWithPreview.txtSelection": "선택", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "시트 설정", "SSE.Views.PrintWithPreview.txtSheet": "시트: {0}", "SSE.Views.PrintWithPreview.txtTo": "받는 사람", "SSE.Views.PrintWithPreview.txtTop": "맨 위", "SSE.Views.ProtectDialog.textExistName": "오류! 제목이 지정된 범위가 이미 있습니다.", "SSE.Views.ProtectDialog.textInvalidName": "범위 표준은 문자로 시작해야 하며 숫자, 문자 및 공백만 포함할 수 있습니다.", "SSE.Views.ProtectDialog.textInvalidRange": "오류! 셀 범위가 잘못되었습니다.", "SSE.Views.ProtectDialog.textSelectData": "데이터 선택", "SSE.Views.ProtectDialog.txtAllow": "모든 사용자 허용:", "SSE.Views.ProtectDialog.txtAllowDescription": "편집을 위해 특정 범위를 잠금 해제할 수 있습니다.", "SSE.Views.ProtectDialog.txtAllowRanges": "허용 범위 편집", "SSE.Views.ProtectDialog.txtAutofilter": "자동 필터 사용", "SSE.Views.ProtectDialog.txtDelCols": "열 삭제", "SSE.Views.ProtectDialog.txtDelRows": "행 삭제", "SSE.Views.ProtectDialog.txtEmpty": "이 입력란은 필수 항목입니다.", "SSE.Views.ProtectDialog.txtFormatCells": "셀서식", "SSE.Views.ProtectDialog.txtFormatCols": "열서식", "SSE.Views.ProtectDialog.txtFormatRows": "행 서식", "SSE.Views.ProtectDialog.txtIncorrectPwd": "비밀번호가 같지 않은지 확인", "SSE.Views.ProtectDialog.txtInsCols": "열 삽입", "SSE.Views.ProtectDialog.txtInsHyper": "하이퍼링크 삽입", "SSE.Views.ProtectDialog.txtInsRows": "행 삽입", "SSE.Views.ProtectDialog.txtObjs": "객체 편집", "SSE.Views.ProtectDialog.txtOptional": "선택", "SSE.Views.ProtectDialog.txtPassword": "비밀번호", "SSE.Views.ProtectDialog.txtPivot": "피벗 테이블과 피벗 차트를 사용", "SSE.Views.ProtectDialog.txtProtect": "보호", "SSE.Views.ProtectDialog.txtRange": "범위", "SSE.Views.ProtectDialog.txtRangeName": "제목", "SSE.Views.ProtectDialog.txtRepeat": "비밀번호 확인", "SSE.Views.ProtectDialog.txtScen": "시나리오 편집", "SSE.Views.ProtectDialog.txtSelLocked": "잠긴 셀 선택", "SSE.Views.ProtectDialog.txtSelUnLocked": "잠겨지지 않은 셀 선택", "SSE.Views.ProtectDialog.txtSheetDescription": "다른 사용자의 편집을 금지하고 다른 사용자의 편집 권한을 제한합니다.", "SSE.Views.ProtectDialog.txtSheetTitle": "시트 보호", "SSE.Views.ProtectDialog.txtSort": "정렬", "SSE.Views.ProtectDialog.txtWarning": "주의: 암호를 잊으면 복구할 수 없습니다. 암호는 대/소문자를 구분합니다. 이 코드를 안전한 곳에 보관하세요.", "SSE.Views.ProtectDialog.txtWBDescription": "다른 사용자가 숨겨진 워크시트를 보고, 워크시트를 추가, 이동, 삭제 또는 숨기고 워크시트 이름을 바꾸는 것을 방지하기 위해 암호를 설정하여 워크시트 구조를 보호할 수 있습니다.", "SSE.Views.ProtectDialog.txtWBTitle": "통합 문서 구조 보호", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "익명사용자", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Anyone", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Edit", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Denied", "SSE.Views.ProtectedRangesEditDlg.textCanView": "보기", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "범위 표준은 문자로 시작해야 하며 숫자, 문자 및 공백만 포함할 수 있습니다.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "오류! 셀 범위가 잘못되었습니다.", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Remove", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "데이터 선택", "SSE.Views.ProtectedRangesEditDlg.textYou": "당신", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Access to range", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "이 입력란은 필수 항목", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "보호", "SSE.Views.ProtectedRangesEditDlg.txtRange": "범위", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "제목", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "이 범위는 당신 만이 편집할 수 있습니다.", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "이름 또는 이메일 주소 입력 시작", "SSE.Views.ProtectedRangesManagerDlg.guestText": "게스트", "SSE.Views.ProtectedRangesManagerDlg.lockText": "잠김", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "삭제", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "편집", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "아직 보호된 범위가 생성되지 않았습니다.<br>최소한 하나의 보호된 범위를 생성하면 이 필드에 나타납니다.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "필터", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "모든", "SSE.Views.ProtectedRangesManagerDlg.textNew": "신규", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "시트 보호", "SSE.Views.ProtectedRangesManagerDlg.textRange": "범위", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "선택한 사람들에게 편집 범위를 제한할 수 있습니다.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "제목", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "이 요소는 다른 사용자가 편집하고 있습니다.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Access", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Denied", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "편집", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "범위 편집", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "새로운 범위", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "보호된 범위", "SSE.Views.ProtectedRangesManagerDlg.txtView": "보기", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "보호된 범위 {0}를 삭제하시겠습니까?<br>스프레드시트의 수정 권한을 가진 모든 사용자가 해당 범위의 내용을 편집할 수 있게 됩니다.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "보호된 범위를 삭제하시겠습니까?<br>스프레드시트의 수정 권한을 가진 모든 사용자가 해당 범위의 내용을 편집할 수 있게 됩니다.", "SSE.Views.ProtectRangesDlg.guestText": "게스트", "SSE.Views.ProtectRangesDlg.lockText": "잠김", "SSE.Views.ProtectRangesDlg.textDelete": "삭제", "SSE.Views.ProtectRangesDlg.textEdit": "편집", "SSE.Views.ProtectRangesDlg.textEmpty": "수정할 범위가 없습니다.", "SSE.Views.ProtectRangesDlg.textNew": "새로만들기", "SSE.Views.ProtectRangesDlg.textProtect": "시트 보호", "SSE.Views.ProtectRangesDlg.textPwd": "비밀번호", "SSE.Views.ProtectRangesDlg.textRange": "범위", "SSE.Views.ProtectRangesDlg.textRangesDesc": "워크시트가 보호되면 암호로 범위가 잠금 해제됩니다.", "SSE.Views.ProtectRangesDlg.textTitle": "제목", "SSE.Views.ProtectRangesDlg.tipIsLocked": "이 요소는 다른 사용자가 편집하고 있습니다.", "SSE.Views.ProtectRangesDlg.txtEditRange": "범위 편집", "SSE.Views.ProtectRangesDlg.txtNewRange": "새로운 범위", "SSE.Views.ProtectRangesDlg.txtNo": "아니오", "SSE.Views.ProtectRangesDlg.txtTitle": "사용자 범위 편집 허용", "SSE.Views.ProtectRangesDlg.txtYes": "확인", "SSE.Views.ProtectRangesDlg.warnDelete": "이름 {0}을 삭제 하시겠습니까?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "열", "SSE.Views.RemoveDuplicatesDialog.textDescription": "중복 값을 제거하려면 중복이 포함된 열을 하나 이상 선택하십시오.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "내 데이터에 제목이 있습니다.", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "모두 선택", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "중복된 항목 제거", "SSE.Views.RightMenu.ariaRightMenu": "Right menu", "SSE.Views.RightMenu.txtCellSettings": "셀 설정", "SSE.Views.RightMenu.txtChartSettings": "차트 설정", "SSE.Views.RightMenu.txtImageSettings": "이미지 설정", "SSE.Views.RightMenu.txtParagraphSettings": "단락 설정", "SSE.Views.RightMenu.txtPivotSettings": "피벗 테이블 설정", "SSE.Views.RightMenu.txtSettings": "공통 설정", "SSE.Views.RightMenu.txtShapeSettings": "도형 설정", "SSE.Views.RightMenu.txtSignatureSettings": "서명 세팅", "SSE.Views.RightMenu.txtSlicerSettings": "슬라이서 설정", "SSE.Views.RightMenu.txtSparklineSettings": "스파크라인 설정", "SSE.Views.RightMenu.txtTableSettings": "표 설정", "SSE.Views.RightMenu.txtTextArtSettings": "텍스트 아트 설정", "SSE.Views.ScaleDialog.textAuto": "자동", "SSE.Views.ScaleDialog.textError": "입력한 값이 잘못되었습니다.", "SSE.Views.ScaleDialog.textFewPages": "페이지", "SSE.Views.ScaleDialog.textFitTo": "맞춤", "SSE.Views.ScaleDialog.textHeight": "높이", "SSE.Views.ScaleDialog.textManyPages": "페이지", "SSE.Views.ScaleDialog.textOnePage": "페이지", "SSE.Views.ScaleDialog.textScaleTo": "확대/축소", "SSE.Views.ScaleDialog.textTitle": "확대/축소 설정", "SSE.Views.ScaleDialog.textWidth": "너비", "SSE.Views.SetValueDialog.txtMaxText": "이 필드의 최대 값은 {0} 입니다.", "SSE.Views.SetValueDialog.txtMinText": "이 필드의 최소값은 {0} 입니다.", "SSE.Views.ShapeSettings.strBackground": "배경색", "SSE.Views.ShapeSettings.strChange": "도형 변경", "SSE.Views.ShapeSettings.strColor": "색상", "SSE.Views.ShapeSettings.strFill": "채우기", "SSE.Views.ShapeSettings.strForeground": "전경색", "SSE.Views.ShapeSettings.strPattern": "패턴", "SSE.Views.ShapeSettings.strShadow": "음영 표시", "SSE.Views.ShapeSettings.strSize": "크기", "SSE.Views.ShapeSettings.strStroke": "선", "SSE.Views.ShapeSettings.strTransparency": "투명도", "SSE.Views.ShapeSettings.strType": "Type", "SSE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "SSE.Views.ShapeSettings.textAdvanced": "고급 설정", "SSE.Views.ShapeSettings.textAngle": "각도", "SSE.Views.ShapeSettings.textBorderSizeErr": "입력 한 값이 잘못되었습니다. <br> 0 ~ 1584 포인트 사이의 값을 입력하십시오.", "SSE.Views.ShapeSettings.textColor": "색상 채우기", "SSE.Views.ShapeSettings.textDirection": "Direction", "SSE.Views.ShapeSettings.textEditPoints": "꼭지점 수정", "SSE.Views.ShapeSettings.textEditShape": "도형 편집", "SSE.Views.ShapeSettings.textEmptyPattern": "패턴 없음", "SSE.Views.ShapeSettings.textEyedropper": "Eyedropper", "SSE.Views.ShapeSettings.textFlip": "대칭", "SSE.Views.ShapeSettings.textFromFile": "파일로부터", "SSE.Views.ShapeSettings.textFromStorage": "스토리지로 부터", "SSE.Views.ShapeSettings.textFromUrl": "URL로부터", "SSE.Views.ShapeSettings.textGradient": "그라데이션 포인트", "SSE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON><PERSON>ll", "SSE.Views.ShapeSettings.textHint270": "왼쪽으로 90도 회전", "SSE.Views.ShapeSettings.textHint90": "오른쪽으로 90도 회전", "SSE.Views.ShapeSettings.textHintFlipH": "좌우대칭", "SSE.Views.ShapeSettings.textHintFlipV": "상하대칭", "SSE.Views.ShapeSettings.textImageTexture": "그림 또는 질감", "SSE.Views.ShapeSettings.textLinear": "선형", "SSE.Views.ShapeSettings.textMoreColors": "More colors", "SSE.Views.ShapeSettings.textNoFill": "채우기 없음", "SSE.Views.ShapeSettings.textNoShadow": "No Shadow", "SSE.Views.ShapeSettings.textOriginalSize": "원본 크기", "SSE.Views.ShapeSettings.textPatternFill": "패턴", "SSE.Views.ShapeSettings.textPosition": "위치", "SSE.Views.ShapeSettings.textRadial": "방사형", "SSE.Views.ShapeSettings.textRecentlyUsed": "최근 사용된", "SSE.Views.ShapeSettings.textRotate90": "90도 회전", "SSE.Views.ShapeSettings.textRotation": "회전", "SSE.Views.ShapeSettings.textSelectImage": "그림선택", "SSE.Views.ShapeSettings.textSelectTexture": "선택", "SSE.Views.ShapeSettings.textShadow": "Shadow", "SSE.Views.ShapeSettings.textStretch": "늘이기", "SSE.Views.ShapeSettings.textStyle": "스타일", "SSE.Views.ShapeSettings.textTexture": "텍스처에서", "SSE.Views.ShapeSettings.textTile": "타일", "SSE.Views.ShapeSettings.tipAddGradientPoint": "그라데이션 포인트 추가", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "그라데이션 포인트 제거", "SSE.Views.ShapeSettings.txtBrownPaper": "갈색 종이", "SSE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "어두운 직물", "SSE.Views.ShapeSettings.txtGrain": "Grain", "SSE.Views.ShapeSettings.txtGranite": "Granite", "SSE.Views.ShapeSettings.txtGreyPaper": "회색 용지", "SSE.Views.ShapeSettings.txtKnit": "K<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtLeather": "가죽", "SSE.Views.ShapeSettings.txtNoBorders": "선 없음", "SSE.Views.ShapeSettings.txtPapyrus": "파피루스", "SSE.Views.ShapeSettings.txtWood": "목재", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Columns", "SSE.Views.ShapeSettingsAdvanced.strMargins": "텍스트 채우기", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "셀을 이동하거나 크기를 조정하지 마십시오.", "SSE.Views.ShapeSettingsAdvanced.textAlt": "대체 텍스트", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "설명", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "시각적 개체 정보의 교체는 텍스트 표현을 기반으로 하며 시각 또는 인지 장애가 있는 사람들이 이미지, 자동 모양, 차트 또는 표에 포함된 정보를 더 잘 이해할 수 있도록 읽어줍니다.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "제목", "SSE.Views.ShapeSettingsAdvanced.textAngle": "각도", "SSE.Views.ShapeSettingsAdvanced.textArrows": "화살표", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "자동 맞춤", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "크기 시작", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "스타일 시작", "SSE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Bottom", "SSE.Views.ShapeSettingsAdvanced.textCapType": "모자 유형", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "열 수", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "최종 크기", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "끝 스타일", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Flat", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "대칭됨", "SSE.Views.ShapeSettingsAdvanced.textHeight": "높이", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "수평", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "조인 유형", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "일정 비율", "SSE.Views.ShapeSettingsAdvanced.textLeft": "왼쪽", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "선 스타일", "SSE.Views.ShapeSettingsAdvanced.textMiter": "연귀", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "이동하지만 셀별로 크기 조정되지 않음", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "도형 위에 텍스트 겹치기", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "텍스트에 맞게 모양 조정", "SSE.Views.ShapeSettingsAdvanced.textRight": "오른쪽", "SSE.Views.ShapeSettingsAdvanced.textRotation": "회전", "SSE.Views.ShapeSettingsAdvanced.textRound": "Round", "SSE.Views.ShapeSettingsAdvanced.textSize": "크기", "SSE.Views.ShapeSettingsAdvanced.textSnap": "셀 잠그기", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "열 사이의 간격", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Square", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "텍스트 상자", "SSE.Views.ShapeSettingsAdvanced.textTitle": "도형 - 고급 설정", "SSE.Views.ShapeSettingsAdvanced.textTop": "Top", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "셀 이동 및 크기 조정", "SSE.Views.ShapeSettingsAdvanced.textVertically": "세로", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "가중치 및 화살표", "SSE.Views.ShapeSettingsAdvanced.textWidth": "폭", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "경고", "SSE.Views.SignatureSettings.strDelete": "서명 삭제", "SSE.Views.SignatureSettings.strDetails": "서명 상세", "SSE.Views.SignatureSettings.strInvalid": "잘못된 서명", "SSE.Views.SignatureSettings.strRequested": "요청 서명", "SSE.Views.SignatureSettings.strSetup": "서명 셋업", "SSE.Views.SignatureSettings.strSign": "서명", "SSE.Views.SignatureSettings.strSignature": "서명", "SSE.Views.SignatureSettings.strSigner": "서명자", "SSE.Views.SignatureSettings.strValid": "유효 서명", "SSE.Views.SignatureSettings.txtContinueEditing": "무조건 편집", "SSE.Views.SignatureSettings.txtEditWarning": "편집은 스프레드시트에서 서명을 삭제할 것입니다.<br> 계속하시겠습니까?", "SSE.Views.SignatureSettings.txtRemoveWarning": "이 서명을 삭제하시겠습니까?<br>이 작업은 취소할 수 없습니다.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "이 스프레드시트는 서명되어야 합니다.", "SSE.Views.SignatureSettings.txtSigned": "유효한 서명이 스프레드시트에 추가되었습니다. 이 스프레드시트는 편집할 수 없도록 보호되었습니다.", "SSE.Views.SignatureSettings.txtSignedInvalid": "스프레드시트에 몇 가지 디지털 서명이 유효하지 않거나 확인되지 않음. 스프레드시트는 편집할 수 없도록 보호됨.", "SSE.Views.SlicerAddDialog.textColumns": "열", "SSE.Views.SlicerAddDialog.txtTitle": "슬라이서 추가", "SSE.Views.SlicerSettings.strHideNoData": "데이터가 없는 항목 숨기기", "SSE.Views.SlicerSettings.strIndNoData": "데이터가 없는 항목을 시각적으로 표시", "SSE.Views.SlicerSettings.strShowDel": "데이터 소스에서 삭제된 항목 표시", "SSE.Views.SlicerSettings.strShowNoData": "마지막에 데이터가 없는 항목 표시", "SSE.Views.SlicerSettings.strSorting": "정렬 및 필터", "SSE.Views.SlicerSettings.textAdvanced": "고급 설정", "SSE.Views.SlicerSettings.textAsc": "오름차순", "SSE.Views.SlicerSettings.textAZ": "오름차순 A > Z", "SSE.Views.SlicerSettings.textButtons": "버튼", "SSE.Views.SlicerSettings.textColumns": "열", "SSE.Views.SlicerSettings.textDesc": "내림차순", "SSE.Views.SlicerSettings.textHeight": "높이", "SSE.Views.SlicerSettings.textHor": "수평", "SSE.Views.SlicerSettings.textKeepRatio": "일정 비율", "SSE.Views.SlicerSettings.textLargeSmall": "최대에서 최소로", "SSE.Views.SlicerSettings.textLock": "크기 조정/이동 비활성화", "SSE.Views.SlicerSettings.textNewOld": "최신에서 가장 오래된 것", "SSE.Views.SlicerSettings.textOldNew": "오래된 것에서 최신 순으로", "SSE.Views.SlicerSettings.textPosition": "위치", "SSE.Views.SlicerSettings.textSize": "크기", "SSE.Views.SlicerSettings.textSmallLarge": "가장 작은 것에서 가장 큰 것", "SSE.Views.SlicerSettings.textStyle": "스타일", "SSE.Views.SlicerSettings.textVert": "세로", "SSE.Views.SlicerSettings.textWidth": "너비", "SSE.Views.SlicerSettings.textZA": "내림차순 Z > A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "버튼", "SSE.Views.SlicerSettingsAdvanced.strColumns": "열", "SSE.Views.SlicerSettingsAdvanced.strHeight": "높이", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "데이터가 없는 항목 숨기기", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "데이터가 없는 항목을 시각적으로 표시", "SSE.Views.SlicerSettingsAdvanced.strReferences": "참조", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "데이터 소스에서 삭제된 항목 표시", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "제목 표시", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "마지막에 데이터가 없는 항목 표시", "SSE.Views.SlicerSettingsAdvanced.strSize": "크기", "SSE.Views.SlicerSettingsAdvanced.strSorting": "정렬 & 필터", "SSE.Views.SlicerSettingsAdvanced.strStyle": "스타일", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "스타일 및 크기", "SSE.Views.SlicerSettingsAdvanced.strWidth": "너비", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "셀을 이동하거나 크기를 조정하지 마십시오.", "SSE.Views.SlicerSettingsAdvanced.textAlt": "대체 텍스트", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "세부 설명", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "시력이나인지 장애가있는 사람들에게 읽을 수있는 시각적 객체 정보의 대체 텍스트 기반 표현으로 이미지에있는 정보를 더 잘 이해할 수 있도록 도와줍니다. 차트 또는 표. ", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "제목", "SSE.Views.SlicerSettingsAdvanced.textAsc": "오름차순", "SSE.Views.SlicerSettingsAdvanced.textAZ": "오름차순 A > Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "내림차순", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "수식에 사용된 이름", "SSE.Views.SlicerSettingsAdvanced.textHeader": "머리글", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "상수 비율", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "최대에서 최소로", "SSE.Views.SlicerSettingsAdvanced.textName": "이름", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "최신에서 가장 오래된 것", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "오래된 것에서 최신 순으로", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "이동하지만 셀별로 크기 조정되지 않음", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "가장 작은 것에서 가장 큰 것", "SSE.Views.SlicerSettingsAdvanced.textSnap": "셀 잠그기", "SSE.Views.SlicerSettingsAdvanced.textSort": "정렬", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "소스 이름", "SSE.Views.SlicerSettingsAdvanced.textTitle": "슬라이서-고급설정", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "셀 이동 및 크기 조정", "SSE.Views.SlicerSettingsAdvanced.textZA": "내림차순 Z > A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "이 입력란은 필수 항목입니다.", "SSE.Views.SortDialog.errorEmpty": "분류 기준에는 반드시 특정 행과 열을 지정해야 합니다.", "SSE.Views.SortDialog.errorMoreOneCol": "여러 열이 선택되었습니다.", "SSE.Views.SortDialog.errorMoreOneRow": "여러 행이 선택되었습니다.", "SSE.Views.SortDialog.errorNotOriginalCol": "선택한 열이 원래의 선택 범위에 없습니다.", "SSE.Views.SortDialog.errorNotOriginalRow": "선택한 행이 원래 선택한 범위에 없습니다.", "SSE.Views.SortDialog.errorSameColumnColor": "  %1이 같은 색상으로 최소한 한 번 이상 분류되었습니다.<br>  중복된 분류 기준을 삭제하시고 다시 시도하여 주시기 바랍니다.", "SSE.Views.SortDialog.errorSameColumnValue": "%1이 중복된 값으로 한 번 이상 분류되었습니다.<br>  중복된 분류 기준을 삭제하시고 다시 한 번 더 시도하여 주시기 바랍니다.", "SSE.Views.SortDialog.textAsc": "오름차순", "SSE.Views.SortDialog.textAuto": "자동", "SSE.Views.SortDialog.textAZ": "오름차순 A > Z", "SSE.Views.SortDialog.textBelow": "아래", "SSE.Views.SortDialog.textBtnCopy": "복사", "SSE.Views.SortDialog.textBtnDelete": "삭제", "SSE.Views.SortDialog.textBtnNew": "신규", "SSE.Views.SortDialog.textCellColor": "셀 색상", "SSE.Views.SortDialog.textColumn": "열", "SSE.Views.SortDialog.textDesc": "내림차순", "SSE.Views.SortDialog.textDown": "레벨 아래로 이동", "SSE.Views.SortDialog.textFontColor": "글꼴 색", "SSE.Views.SortDialog.textLeft": "왼쪽", "SSE.Views.SortDialog.textLevels": "레벨들", "SSE.Views.SortDialog.textMoreCols": "(다른 행들...)", "SSE.Views.SortDialog.textMoreRows": "행 더 보기...", "SSE.Views.SortDialog.textNone": "없음", "SSE.Views.SortDialog.textOptions": "옵션...", "SSE.Views.SortDialog.textOrder": "순서", "SSE.Views.SortDialog.textRight": "오른쪽", "SSE.Views.SortDialog.textRow": "행", "SSE.Views.SortDialog.textSort": "정렬", "SSE.Views.SortDialog.textSortBy": "정렬 기준", "SSE.Views.SortDialog.textThenBy": "다음 우선되는 키", "SSE.Views.SortDialog.textTop": "위", "SSE.Views.SortDialog.textUp": "레벨 위로 이동", "SSE.Views.SortDialog.textValues": "값", "SSE.Views.SortDialog.textZA": "내림차순 Z > A", "SSE.Views.SortDialog.txtInvalidRange": "유효하지 않은 셀 범위.", "SSE.Views.SortDialog.txtTitle": "정렬", "SSE.Views.SortFilterDialog.textAsc": "오름차순 (A > Z) ", "SSE.Views.SortFilterDialog.textDesc": "내림차순 (Z > A)", "SSE.Views.SortFilterDialog.textNoSort": "No sort", "SSE.Views.SortFilterDialog.txtTitle": "정렬", "SSE.Views.SortFilterDialog.txtTitleValue": "값에 따라 정렬", "SSE.Views.SortOptionsDialog.textCase": "대소문자 구별", "SSE.Views.SortOptionsDialog.textHeaders": "내 데이터에 제목이 있습니다.", "SSE.Views.SortOptionsDialog.textLeftRight": "왼쪽에서 오른쪽으로 정렬", "SSE.Views.SortOptionsDialog.textOrientation": "방향", "SSE.Views.SortOptionsDialog.textTitle": "정렬 옵션", "SSE.Views.SortOptionsDialog.textTopBottom": "위에서 아래로 정렬", "SSE.Views.SpecialPasteDialog.textAdd": "추가", "SSE.Views.SpecialPasteDialog.textAll": "모든", "SSE.Views.SpecialPasteDialog.textBlanks": "공백 건너뛰기", "SSE.Views.SpecialPasteDialog.textColWidth": "열 너비", "SSE.Views.SpecialPasteDialog.textComments": "코멘트", "SSE.Views.SpecialPasteDialog.textDiv": "배분", "SSE.Views.SpecialPasteDialog.textFFormat": "수식 및 서식", "SSE.Views.SpecialPasteDialog.textFNFormat": "수식 및 숫자 형식", "SSE.Views.SpecialPasteDialog.textFormats": "서식", "SSE.Views.SpecialPasteDialog.textFormulas": "수식", "SSE.Views.SpecialPasteDialog.textFWidth": "수식 및 열 너비", "SSE.Views.SpecialPasteDialog.textMult": "곱셈", "SSE.Views.SpecialPasteDialog.textNone": "없음", "SSE.Views.SpecialPasteDialog.textOperation": "동작", "SSE.Views.SpecialPasteDialog.textPaste": "붙여 넣기", "SSE.Views.SpecialPasteDialog.textSub": "뺄셈", "SSE.Views.SpecialPasteDialog.textTitle": "특수기호 붙이기", "SSE.Views.SpecialPasteDialog.textTranspose": "교체", "SSE.Views.SpecialPasteDialog.textValues": "값", "SSE.Views.SpecialPasteDialog.textVFormat": "값 및 형식", "SSE.Views.SpecialPasteDialog.textVNFormat": "값 및 숫자 형식", "SSE.Views.SpecialPasteDialog.textWBorders": "외곽선만", "SSE.Views.Spellcheck.noSuggestions": "맞춤법 제안 없음", "SSE.Views.Spellcheck.textChange": "변경", "SSE.Views.Spellcheck.textChangeAll": "전체 변경", "SSE.Views.Spellcheck.textIgnore": "무시", "SSE.Views.Spellcheck.textIgnoreAll": "모두 무시", "SSE.Views.Spellcheck.txtAddToDictionary": "사용자 정의 사전에 추가", "SSE.Views.Spellcheck.txtClosePanel": "맞춤법 검사 닫기", "SSE.Views.Spellcheck.txtComplete": "맞춤법 검사 완료", "SSE.Views.Spellcheck.txtDictionaryLanguage": "사전 언어", "SSE.Views.Spellcheck.txtNextTip": "다음 단어로 이동", "SSE.Views.Spellcheck.txtSpelling": "스펠링", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(끝으로 이동)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "Create a copy", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "새 스프레드시트 만들기", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "시트 이전으로 이동", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "스프레드시트", "SSE.Views.Statusbar.filteredRecordsText": "{1} 개의 필터링 된 레코드 중 {0}", "SSE.Views.Statusbar.filteredText": "필터 모드", "SSE.Views.Statusbar.itemAverage": "평균", "SSE.Views.Statusbar.itemCount": "계산", "SSE.Views.Statusbar.itemDelete": "삭제", "SSE.Views.Statusbar.itemHidden": "숨김", "SSE.Views.Statusbar.itemHide": "숨기기", "SSE.Views.Statusbar.itemInsert": "삽입", "SSE.Views.Statusbar.itemMaximum": "최대값", "SSE.Views.Statusbar.itemMinimum": "최소값", "SSE.Views.Statusbar.itemMoveOrCopy": "Move or copy", "SSE.Views.Statusbar.itemProtect": "보호", "SSE.Views.Statusbar.itemRename": "이름 바꾸기", "SSE.Views.Statusbar.itemStatus": "저장 상태", "SSE.Views.Statusbar.itemSum": "합계", "SSE.Views.Statusbar.itemTabColor": "탭 색상", "SSE.Views.Statusbar.itemUnProtect": "보호해제", "SSE.Views.Statusbar.RenameDialog.errNameExists": "같은 이름의 워크 시트가 이미 있습니다.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "시트 이름에는 \\/ *? [] :", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "시트 이름", "SSE.Views.Statusbar.selectAllSheets": "모든 워크시트 선택", "SSE.Views.Statusbar.sheetIndexText": "전체 {1} 시트중 {0}", "SSE.Views.Statusbar.textAverage": "평균", "SSE.Views.Statusbar.textCount": "계산", "SSE.Views.Statusbar.textMax": "최대", "SSE.Views.Statusbar.textMin": "최소", "SSE.Views.Statusbar.textNewColor": "새 사용자 정의 색상 추가", "SSE.Views.Statusbar.textNoColor": "색상 없음", "SSE.Views.Statusbar.textSum": "합계", "SSE.Views.Statusbar.tipAddTab": "워크 시트 추가", "SSE.Views.Statusbar.tipFirst": "첫 번째 시트로 스크롤", "SSE.Views.Statusbar.tipLast": "마지막 시트로 스크롤", "SSE.Views.Statusbar.tipListOfSheets": "시트 목록", "SSE.Views.Statusbar.tipNext": "오른쪽 스크롤 목록", "SSE.Views.Statusbar.tipPrev": "왼쪽으로 스크롤 목록", "SSE.Views.Statusbar.tipZoomFactor": "확대/축소", "SSE.Views.Statusbar.tipZoomIn": "확대", "SSE.Views.Statusbar.tipZoomOut": "축소", "SSE.Views.Statusbar.ungroupSheets": "시트 그룹 해제", "SSE.Views.Statusbar.zoomText": "확대/축소 {0} %", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "선택한 셀 범위에서 작업을 수행 할 수 없습니다. <br> 기존 데이터 범위와 다른 데이터 범위를 선택하고 다시 시도하십시오.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "선택한 셀 범위에 대해 작업을 완료 할 수 없습니다. <br> 첫 번째 테이블 행이 같은 행에 있고 결과 테이블이 현재 테이블과 겹치도록 범위를 선택하십시오. . ", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "선택한 셀 범위에 대해 작업을 완료 할 수 없습니다. <br> 다른 테이블을 포함하지 않는 범위를 선택하십시오.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "다중 셀 배열 수식은 테이블에서 허용되지 않습니다.", "SSE.Views.TableOptionsDialog.txtEmpty": "이 입력란은 필수 항목", "SSE.Views.TableOptionsDialog.txtFormat": "표 만들기", "SSE.Views.TableOptionsDialog.txtInvalidRange": "오류! 셀 범위가 잘못되었습니다.", "SSE.Views.TableOptionsDialog.txtNote": "헤더는 동일한 행에 있어야 하며 결과 테이블 범위는 원래 테이블 범위와 겹쳐야 합니다.", "SSE.Views.TableOptionsDialog.txtTitle": "제목", "SSE.Views.TableSettings.deleteColumnText": "열 삭제", "SSE.Views.TableSettings.deleteRowText": "행 삭제", "SSE.Views.TableSettings.deleteTableText": "테이블 삭제", "SSE.Views.TableSettings.insertColumnLeftText": "왼쪽 열 삽입", "SSE.Views.TableSettings.insertColumnRightText": "오른쪽 열 삽입", "SSE.Views.TableSettings.insertRowAboveText": "위의 행 삽입", "SSE.Views.TableSettings.insertRowBelowText": "아래에 행 삽입", "SSE.Views.TableSettings.notcriticalErrorTitle": "경고", "SSE.Views.TableSettings.selectColumnText": "전체 열 선택", "SSE.Views.TableSettings.selectDataText": "열 데이터 선택", "SSE.Views.TableSettings.selectRowText": "행 선택", "SSE.Views.TableSettings.selectTableText": "표 선택", "SSE.Views.TableSettings.textActions": "표 작업", "SSE.Views.TableSettings.textAdvanced": "고급 설정", "SSE.Views.TableSettings.textBanded": "줄무늬", "SSE.Views.TableSettings.textColumns": "열", "SSE.Views.TableSettings.textConvertRange": "범위로 변환", "SSE.Views.TableSettings.textEdit": "행 및 열", "SSE.Views.TableSettings.textEmptyTemplate": "템플릿 없음", "SSE.Views.TableSettings.textExistName": "오류! 같은 이름의 범위가 이미 있습니다", "SSE.Views.TableSettings.textFilter": "필터 버튼", "SSE.Views.TableSettings.textFirst": "처음", "SSE.Views.TableSettings.textHeader": "머리글", "SSE.Views.TableSettings.textInvalidName": "오류! 잘못된 테이블 이름", "SSE.Views.TableSettings.textIsLocked": "이 요소는 다른 사용자가 편집하고 있습니다.", "SSE.Views.TableSettings.textLast": "마지막", "SSE.Views.TableSettings.textLongOperation": "긴 작업", "SSE.Views.TableSettings.textPivot": "피벗 테이블 삽입", "SSE.Views.TableSettings.textRemDuplicates": "중복된 항목 제거", "SSE.Views.TableSettings.textReservedName": "사용하려는 이름이 이미 셀 수식에서 참조되어 있습니다. 다른 이름을 사용하십시오.", "SSE.Views.TableSettings.textResize": "크기 조정 테이블", "SSE.Views.TableSettings.textRows": "행", "SSE.Views.TableSettings.textSelectData": "데이터 선택", "SSE.Views.TableSettings.textSlicer": "슬라이서추가", "SSE.Views.TableSettings.textTableName": "테이블 이름", "SSE.Views.TableSettings.textTemplate": "템플릿에서 선택", "SSE.Views.TableSettings.textTotal": "요약 행", "SSE.Views.TableSettings.txtGroupTable_Custom": "사용자 정의", "SSE.Views.TableSettings.txtGroupTable_Dark": "어두운", "SSE.Views.TableSettings.txtGroupTable_Light": "밝은", "SSE.Views.TableSettings.txtGroupTable_Medium": "중", "SSE.Views.TableSettings.txtTable_TableStyleDark": "어두운 표 스타일", "SSE.Views.TableSettings.txtTable_TableStyleLight": "밝은 표 스타일", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "중간 표 스타일", "SSE.Views.TableSettings.warnLongOperation": "수행하려는 작업이 완료하는 데 시간이 오래 걸릴 수 있습니다. 계속 하시겠습니까?", "SSE.Views.TableSettingsAdvanced.textAlt": "대체 텍스트", "SSE.Views.TableSettingsAdvanced.textAltDescription": "설명", "SSE.Views.TableSettingsAdvanced.textAltTip": "시각적 객체 정보의 대체 텍스트 기반 표현으로 시력이나인지 장애가있는 사람들에게 읽혀 이미지에있는 정보를 더 잘 이해할 수 있도록 도와줍니다. 차트 또는 표. ", "SSE.Views.TableSettingsAdvanced.textAltTitle": "제목", "SSE.Views.TableSettingsAdvanced.textTitle": "표 - 고급 설정", "SSE.Views.TextArtSettings.strBackground": "배경색", "SSE.Views.TextArtSettings.strColor": "색상", "SSE.Views.TextArtSettings.strFill": "채우기", "SSE.Views.TextArtSettings.strForeground": "전경색", "SSE.Views.TextArtSettings.strPattern": "패턴", "SSE.Views.TextArtSettings.strSize": "크기", "SSE.Views.TextArtSettings.strStroke": "선", "SSE.Views.TextArtSettings.strTransparency": "투명도", "SSE.Views.TextArtSettings.strType": "유형", "SSE.Views.TextArtSettings.textAngle": "각도", "SSE.Views.TextArtSettings.textBorderSizeErr": "입력 한 값이 잘못되었습니다. <br> 0 ~ 1584pt 사이의 값을 입력하십시오.", "SSE.Views.TextArtSettings.textColor": "색상 채우기", "SSE.Views.TextArtSettings.textDirection": "Direction", "SSE.Views.TextArtSettings.textEmptyPattern": "패턴 없음", "SSE.Views.TextArtSettings.textFromFile": "파일로부터", "SSE.Views.TextArtSettings.textFromUrl": "URL로부터", "SSE.Views.TextArtSettings.textGradient": "그라데이션 포인트", "SSE.Views.TextArtSettings.textGradientFill": "그라데이션 채우기", "SSE.Views.TextArtSettings.textImageTexture": "그림 또는 질감", "SSE.Views.TextArtSettings.textLinear": "선형", "SSE.Views.TextArtSettings.textNoFill": "채우기 없음", "SSE.Views.TextArtSettings.textPatternFill": "패턴", "SSE.Views.TextArtSettings.textPosition": "위치", "SSE.Views.TextArtSettings.textRadial": "방사형", "SSE.Views.TextArtSettings.textSelectTexture": "선택", "SSE.Views.TextArtSettings.textStretch": "늘이기", "SSE.Views.TextArtSettings.textStyle": "스타일", "SSE.Views.TextArtSettings.textTemplate": "템플릿", "SSE.Views.TextArtSettings.textTexture": "텍스처에서", "SSE.Views.TextArtSettings.textTile": "타일", "SSE.Views.TextArtSettings.textTransform": "변형", "SSE.Views.TextArtSettings.tipAddGradientPoint": "그라데이션 포인트 추가", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "그라데이션 포인트 제거", "SSE.Views.TextArtSettings.txtBrownPaper": "갈색 종이", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "어두운 직물", "SSE.Views.TextArtSettings.txtGrain": "Grain", "SSE.Views.TextArtSettings.txtGranite": "Granite", "SSE.Views.TextArtSettings.txtGreyPaper": "회색 용지", "SSE.Views.TextArtSettings.txtKnit": "K<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "가죽", "SSE.Views.TextArtSettings.txtNoBorders": "선 없음", "SSE.Views.TextArtSettings.txtPapyrus": "Papyrus", "SSE.Views.TextArtSettings.txtWood": "나무", "SSE.Views.Toolbar.capBtnAddComment": "코멘트 추가", "SSE.Views.Toolbar.capBtnColorSchemas": "색상 코드", "SSE.Views.Toolbar.capBtnComment": "코멘트", "SSE.Views.Toolbar.capBtnInsHeader": "머리말/꼬리말", "SSE.Views.Toolbar.capBtnInsSlicer": "슬라이서", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "기호", "SSE.Views.Toolbar.capBtnMargins": "여백", "SSE.Views.Toolbar.capBtnPageBreak": "나누기", "SSE.Views.Toolbar.capBtnPageOrient": "방향", "SSE.Views.Toolbar.capBtnPageSize": "크기", "SSE.Views.Toolbar.capBtnPrintArea": "인쇄 영역", "SSE.Views.Toolbar.capBtnPrintTitles": "제목 인쇄", "SSE.Views.Toolbar.capBtnScale": "크기에 맞게 확대/축소", "SSE.Views.Toolbar.capImgAlign": "정렬", "SSE.Views.Toolbar.capImgBackward": "뒤로 보내기", "SSE.Views.Toolbar.capImgForward": "앞으로 보내기", "SSE.Views.Toolbar.capImgGroup": "그룹", "SSE.Views.Toolbar.capInsertChart": "차트", "SSE.Views.Toolbar.capInsertChartRecommend": "Recommended Chart", "SSE.Views.Toolbar.capInsertEquation": "수식", "SSE.Views.Toolbar.capInsertHyperlink": "하이퍼 링크", "SSE.Views.Toolbar.capInsertImage": "그림", "SSE.Views.Toolbar.capInsertShape": "도형", "SSE.Views.Toolbar.capInsertSpark": "스파크라인", "SSE.Views.Toolbar.capInsertTable": "테이블", "SSE.Views.Toolbar.capInsertText": "텍스트 상자", "SSE.Views.Toolbar.capInsertTextart": "텍스트 아트", "SSE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniCapitalizeWords": "각 단어의 첫글자를 대문자로", "SSE.Views.Toolbar.mniImageFromFile": "파일에서 그림", "SSE.Views.Toolbar.mniImageFromStorage": "스토리지에서 불러오기", "SSE.Views.Toolbar.mniImageFromUrl": "URL에서 그림", "SSE.Views.Toolbar.mniLowerCase": "소문자", "SSE.Views.Toolbar.mniSentenceCase": "문장의 첫 글자를 대문자로", "SSE.Views.Toolbar.mniToggleCase": "대/소문자 전환", "SSE.Views.Toolbar.mniUpperCase": "대문자", "SSE.Views.Toolbar.textAddPrintArea": "인쇄 영역에 추가", "SSE.Views.Toolbar.textAlignBottom": "아래쪽 정렬", "SSE.Views.Toolbar.textAlignCenter": "가운데 정렬", "SSE.Views.Toolbar.textAlignJust": "Justified", "SSE.Views.Toolbar.textAlignLeft": "왼쪽 정렬", "SSE.Views.Toolbar.textAlignMiddle": "중간 정렬", "SSE.Views.Toolbar.textAlignRight": "오른쪽 정렬", "SSE.Views.Toolbar.textAlignTop": "Align Top", "SSE.Views.Toolbar.textAllBorders": "모든 테두리", "SSE.Views.Toolbar.textAlpha": "소문자 알파", "SSE.Views.Toolbar.textAuto": "자동", "SSE.Views.Toolbar.textAutoColor": "자동", "SSE.Views.Toolbar.textBetta": "소문자 베타", "SSE.Views.Toolbar.textBlackHeart": "검정 하트", "SSE.Views.Toolbar.textBold": "Bold", "SSE.Views.Toolbar.textBordersColor": "테두리 색상", "SSE.Views.Toolbar.textBordersStyle": "테두리 스타일", "SSE.Views.Toolbar.textBottom": "바닥: ", "SSE.Views.Toolbar.textBottomBorders": "아래쪽 테두리", "SSE.Views.Toolbar.textBullet": "글머리 기호", "SSE.Views.Toolbar.textCellAlign": "Format cell alignment", "SSE.Views.Toolbar.textCenterBorders": "내부 세로 테두리", "SSE.Views.Toolbar.textClearPrintArea": "인쇄 영역 해제", "SSE.Views.Toolbar.textClearRule": "규칙 제거", "SSE.Views.Toolbar.textClockwise": "시계 방향으로 각도", "SSE.Views.Toolbar.textColorScales": "색상 코드", "SSE.Views.Toolbar.textCopyright": "저작권 표시", "SSE.Views.Toolbar.textCounterCw": "시계 반대 방향 각도", "SSE.Views.Toolbar.textCustom": "사용자 정의", "SSE.Views.Toolbar.textDataBars": "데이터 막대", "SSE.Views.Toolbar.textDegree": "도수 기호", "SSE.Views.Toolbar.textDelLeft": "셀 왼쪽으로 시프트", "SSE.Views.Toolbar.textDelPageBreak": "페이지 구분 제거", "SSE.Views.Toolbar.textDelta": "소문자 델타", "SSE.Views.Toolbar.textDelUp": "셀을 위로 이동", "SSE.Views.Toolbar.textDiagDownBorder": "대각선 아래쪽 테두리", "SSE.Views.Toolbar.textDiagUpBorder": "대각선 위쪽 테두리", "SSE.Views.Toolbar.textDivision": "나누기 기호", "SSE.Views.Toolbar.textDollar": "달러 기호", "SSE.Views.Toolbar.textDone": "완료", "SSE.Views.Toolbar.textDown": "Down", "SSE.Views.Toolbar.textEditVA": "표시 영역 편집", "SSE.Views.Toolbar.textEntireCol": "전체 열", "SSE.Views.Toolbar.textEntireRow": "전체 행", "SSE.Views.Toolbar.textEuro": "유로화", "SSE.Views.Toolbar.textFewPages": "페이지", "SSE.Views.Toolbar.textFillLeft": "Left", "SSE.Views.Toolbar.textFillRight": "Right", "SSE.Views.Toolbar.textFormatCellFill": "Format cell fill", "SSE.Views.Toolbar.textGreaterEqual": "크거나 같음", "SSE.Views.Toolbar.textHeight": "높이", "SSE.Views.Toolbar.textHideVA": "표시 영역 숨기기", "SSE.Views.Toolbar.textHorizontal": "가로 텍스트", "SSE.Views.Toolbar.textInfinity": "무한대", "SSE.Views.Toolbar.textInsDown": "셀을 아래로 이동", "SSE.Views.Toolbar.textInsideBorders": "테두리 안에", "SSE.Views.Toolbar.textInsPageBreak": "페이지 나누기 삽입", "SSE.Views.Toolbar.textInsRight": "셀 오른쪽으로 이동", "SSE.Views.Toolbar.textItalic": "Italic", "SSE.Views.Toolbar.textItems": "아이템", "SSE.Views.Toolbar.textLandscape": "수평", "SSE.Views.Toolbar.textLeft": "왼쪽 : ", "SSE.Views.Toolbar.textLeftBorders": "왼쪽 테두리", "SSE.Views.Toolbar.textLessEqual": "보다 작거나 같음", "SSE.Views.Toolbar.textLetterPi": "소문자 파이", "SSE.Views.Toolbar.textManageRule": "관리 규칙", "SSE.Views.Toolbar.textManyPages": "페이지", "SSE.Views.Toolbar.textMarginsLast": "마지막 사용자 정의", "SSE.Views.Toolbar.textMarginsNarrow": "좁게", "SSE.Views.Toolbar.textMarginsNormal": "일반", "SSE.Views.Toolbar.textMarginsWide": "넓게", "SSE.Views.Toolbar.textMiddleBorders": "내부 수평 테두리", "SSE.Views.Toolbar.textMoreBorders": "More borders", "SSE.Views.Toolbar.textMoreFormats": "기타 형식", "SSE.Views.Toolbar.textMorePages": "더 많은 페이지", "SSE.Views.Toolbar.textMoreSymbols": "더 많은 기호", "SSE.Views.Toolbar.textNewColor": "새 사용자 지정 색 추가", "SSE.Views.Toolbar.textNewRule": "새로운 규칙", "SSE.Views.Toolbar.textNoBorders": "테두리 없음", "SSE.Views.Toolbar.textNotEqualTo": "같지 않음", "SSE.Views.Toolbar.textOneHalf": "2분의 1", "SSE.Views.Toolbar.textOnePage": "페이지", "SSE.Views.Toolbar.textOneQuarter": "4분의 1", "SSE.Views.Toolbar.textOutBorders": "바깥쪽 테두리", "SSE.Views.Toolbar.textPageMarginsCustom": "사용자 정의 여백", "SSE.Views.Toolbar.textPlusMinus": "플러스 마이너스 기호", "SSE.Views.Toolbar.textPortrait": "세로", "SSE.Views.Toolbar.textPrint": "인쇄", "SSE.Views.Toolbar.textPrintGridlines": "눈금 선 인쇄", "SSE.Views.Toolbar.textPrintHeadings": "글머리 인쇄", "SSE.Views.Toolbar.textPrintOptions": "인쇄 설정", "SSE.Views.Toolbar.textRegistered": "등록된 서명", "SSE.Views.Toolbar.textResetPageBreak": "모든 페이지 구분 재설정", "SSE.Views.Toolbar.textRight": "오른쪽 : ", "SSE.Views.Toolbar.textRightBorders": "오른쪽 테두리", "SSE.Views.Toolbar.textRotateDown": "텍스트 아래로 회전", "SSE.Views.Toolbar.textRotateUp": "텍스트 회전", "SSE.Views.Toolbar.textRtlSheet": "Sheet Right-to-left", "SSE.Views.Toolbar.textScale": "크기", "SSE.Views.Toolbar.textScaleCustom": "사용자 정의", "SSE.Views.Toolbar.textSection": "섹션 기호", "SSE.Views.Toolbar.textSelection": "현재 선택 고정", "SSE.Views.Toolbar.textSeries": "Series", "SSE.Views.Toolbar.textSetPrintArea": "인쇄영역 설정", "SSE.Views.Toolbar.textShapesCombine": "Combine", "SSE.Views.Toolbar.textShapesFragment": "Fragment", "SSE.Views.Toolbar.textShapesIntersect": "Intersect", "SSE.Views.Toolbar.textShapesSubstract": "Subtract", "SSE.Views.Toolbar.textShapesUnion": "Union", "SSE.Views.Toolbar.textShowVA": "가시 영역 표시", "SSE.Views.Toolbar.textSmile": "흰 웃는 얼굴 이모티콘", "SSE.Views.Toolbar.textSquareRoot": "제곱근", "SSE.Views.Toolbar.textStrikeout": "취소선", "SSE.Views.Toolbar.textSubscript": "첨자", "SSE.Views.Toolbar.textSubSuperscript": "첨자/위에 쓴", "SSE.Views.Toolbar.textSuperscript": "위에 쓴", "SSE.Views.Toolbar.textTabCollaboration": "협업", "SSE.Views.Toolbar.textTabData": "데이터", "SSE.Views.Toolbar.textTabDraw": "그리기", "SSE.Views.Toolbar.textTabFile": "파일", "SSE.Views.Toolbar.textTabFormula": "수식", "SSE.Views.Toolbar.textTabHome": "홈", "SSE.Views.Toolbar.textTabInsert": "삽입", "SSE.Views.Toolbar.textTabLayout": "레이아웃", "SSE.Views.Toolbar.textTabProtect": "보호", "SSE.Views.Toolbar.textTabView": "보기", "SSE.Views.Toolbar.textThisPivot": "피벗 테이블로 부터", "SSE.Views.Toolbar.textThisSheet": "워크시트로 부터", "SSE.Views.Toolbar.textThisTable": "표로 부터", "SSE.Views.Toolbar.textTilde": "물결표", "SSE.Views.Toolbar.textTop": "상위 : ", "SSE.Views.Toolbar.textTopBorders": "위쪽 테두리", "SSE.Views.Toolbar.textTradeMark": "상표 표시", "SSE.Views.Toolbar.textUnderline": "밑줄", "SSE.Views.Toolbar.textUp": "Up", "SSE.Views.Toolbar.textVertical": "세로 텍스트", "SSE.Views.Toolbar.textWidth": "너비", "SSE.Views.Toolbar.textYen": "엔화", "SSE.Views.Toolbar.textZoom": "확대/축소", "SSE.Views.Toolbar.tipAlignBottom": "아래쪽 정렬", "SSE.Views.Toolbar.tipAlignCenter": "가운데 정렬", "SSE.Views.Toolbar.tipAlignJust": "Justified", "SSE.Views.Toolbar.tipAlignLeft": "왼쪽 정렬", "SSE.Views.Toolbar.tipAlignMiddle": "중간 정렬", "SSE.Views.Toolbar.tipAlignRight": "오른쪽 정렬", "SSE.Views.Toolbar.tipAlignTop": "정렬", "SSE.Views.Toolbar.tipAutofilter": "정렬 및 필터링", "SSE.Views.Toolbar.tipBack": "뒤로", "SSE.Views.Toolbar.tipBorders": "테두리", "SSE.Views.Toolbar.tipCellStyle": "셀 스타일", "SSE.Views.Toolbar.tipChangeCase": "대소문자 변경", "SSE.Views.Toolbar.tipChangeChart": "차트 유형 변경", "SSE.Views.Toolbar.tipClearStyle": "지우기", "SSE.Views.Toolbar.tipColorSchemas": "색상 구성 변경", "SSE.Views.Toolbar.tipCondFormat": "조건부 서식", "SSE.Views.Toolbar.tipCopy": "복사", "SSE.Views.Toolbar.tipCopyStyle": "스타일 복사", "SSE.Views.Toolbar.tipCut": "잘라 내기", "SSE.Views.Toolbar.tipDecDecimal": "자리수 줄임", "SSE.Views.Toolbar.tipDecFont": "글꼴 크기 감소", "SSE.Views.Toolbar.tipDeleteOpt": "셀 삭제", "SSE.Views.Toolbar.tipDigStyleAccounting": "회계 표시 형식", "SSE.Views.Toolbar.tipDigStyleComma": "Comma style", "SSE.Views.Toolbar.tipDigStyleCurrency": "통화 스타일", "SSE.Views.Toolbar.tipDigStylePercent": "백분율 스타일", "SSE.Views.Toolbar.tipEditChart": "차트 편집", "SSE.Views.Toolbar.tipEditChartData": "데이터 선택", "SSE.Views.Toolbar.tipEditChartType": "차트 유형 변경", "SSE.Views.Toolbar.tipEditHeader": "머리글 또는 바닥글 편집", "SSE.Views.Toolbar.tipFontColor": "글꼴 색", "SSE.Views.Toolbar.tipFontName": "글꼴", "SSE.Views.Toolbar.tipFontSize": "글꼴 크기", "SSE.Views.Toolbar.tipHAlighOle": "수평 정렬", "SSE.Views.Toolbar.tipImgAlign": "오브젝트 정렬", "SSE.Views.Toolbar.tipImgGroup": "개체를 그룹화", "SSE.Views.Toolbar.tipIncDecimal": "자리수 늘림", "SSE.Views.Toolbar.tipIncFont": "글꼴 크기 증가", "SSE.Views.Toolbar.tipInsertChart": "차트 삽입", "SSE.Views.Toolbar.tipInsertChartRecommend": "Insert recommended chart", "SSE.Views.Toolbar.tipInsertChartSpark": "차트 또는 스파크 라인 삽입", "SSE.Views.Toolbar.tipInsertEquation": "수식 삽입", "SSE.Views.Toolbar.tipInsertHorizontalText": "가로 텍스트 상자 삽입", "SSE.Views.Toolbar.tipInsertHyperlink": "하이퍼 링크 추가", "SSE.Views.Toolbar.tipInsertImage": "그림 삽입", "SSE.Views.Toolbar.tipInsertOpt": "셀 삽입", "SSE.Views.Toolbar.tipInsertShape": "도형 삽입", "SSE.Views.Toolbar.tipInsertSlicer": "슬라이서추가", "SSE.Views.Toolbar.tipInsertSmartArt": "SmartArt 삽입", "SSE.Views.Toolbar.tipInsertSpark": "스파크라인 삽입", "SSE.Views.Toolbar.tipInsertSymbol": "기호 삽입", "SSE.Views.Toolbar.tipInsertTable": "표 삽입", "SSE.Views.Toolbar.tipInsertText": "텍스트 상자 삽입", "SSE.Views.Toolbar.tipInsertTextart": "텍스트 아트 삽입", "SSE.Views.Toolbar.tipInsertVerticalText": "세로 텍스트 상자 삽입", "SSE.Views.Toolbar.tipMerge": "병합하고 가운데 맞춤", "SSE.Views.Toolbar.tipNone": "없음", "SSE.Views.Toolbar.tipNumFormat": "숫자 형식", "SSE.Views.Toolbar.tipPageBreak": "인쇄된 사본에서 다음 페이지가 시작되길 원하는 위치에 줄바꿈을 추가하세요.", "SSE.Views.Toolbar.tipPageMargins": "페이지 여백", "SSE.Views.Toolbar.tipPageOrient": "페이지 방향", "SSE.Views.Toolbar.tipPageSize": "페이지 크기", "SSE.Views.Toolbar.tipPaste": "붙여 넣기", "SSE.Views.Toolbar.tipPrColor": "색상 채우기", "SSE.Views.Toolbar.tipPrint": "인쇄", "SSE.Views.Toolbar.tipPrintArea": "인쇄 영역", "SSE.Views.Toolbar.tipPrintQuick": "빠른 인쇄", "SSE.Views.Toolbar.tipPrintTitles": "제목 인쇄", "SSE.Views.Toolbar.tipRedo": "Redo", "SSE.Views.Toolbar.tipReplace": "바꾸기", "SSE.Views.Toolbar.tipRtlSheet": "Switch the sheet direction so that the first column is on the right side", "SSE.Views.Toolbar.tipSave": "저장", "SSE.Views.Toolbar.tipSaveCoauth": "다른 사용자가 볼 수 있도록 변경 사항을 저장하십시오.", "SSE.Views.Toolbar.tipScale": "크기에 맞게 확대/축소", "SSE.Views.Toolbar.tipSelectAll": "모두 선택", "SSE.Views.Toolbar.tipSendBackward": "뒤로 보내기", "SSE.Views.Toolbar.tipSendForward": "앞으로 보내기", "SSE.Views.Toolbar.tipShapesMerge": "Merge shapes", "SSE.Views.Toolbar.tipSynchronize": "다른 사용자가 문서를 변경했습니다. 변경 사항을 저장하고 업데이트를 다시로드하려면 클릭하십시오.", "SSE.Views.Toolbar.tipTextFormatting": "더 많은 텍스트 서식 지정 도구", "SSE.Views.Toolbar.tipTextOrientation": "Orientation", "SSE.Views.Toolbar.tipUndo": "실행 취소", "SSE.Views.Toolbar.tipVAlighOle": "수직 정렬", "SSE.Views.Toolbar.tipVisibleArea": "가시 영역", "SSE.Views.Toolbar.tipWrap": "텍스트 줄 바꾸기", "SSE.Views.Toolbar.txtAccounting": "회계", "SSE.Views.Toolbar.txtAdditional": "Additional", "SSE.Views.Toolbar.txtAscending": "오름차순", "SSE.Views.Toolbar.txtAutosumTip": "합계", "SSE.Views.Toolbar.txtCellStyle": "셀 스타일", "SSE.Views.Toolbar.txtClearAll": "모두", "SSE.Views.Toolbar.txtClearComments": "코멘트", "SSE.Views.Toolbar.txtClearFilter": "필터 지우기", "SSE.Views.Toolbar.txtClearFormat": "형식", "SSE.Views.Toolbar.txtClearFormula": "함수", "SSE.Views.Toolbar.txtClearHyper": "하이퍼 링크", "SSE.Views.Toolbar.txtClearText": "텍스트", "SSE.Views.Toolbar.txtCurrency": "통화", "SSE.Views.Toolbar.txtCustom": "Custom", "SSE.Views.Toolbar.txtDate": "날짜", "SSE.Views.Toolbar.txtDateLong": "확장된 날짜 형식", "SSE.Views.Toolbar.txtDateShort": "간단한 날짜 형식", "SSE.Views.Toolbar.txtDateTime": "날짜 및 시간", "SSE.Views.Toolbar.txtDescending": "내림차순", "SSE.Views.Toolbar.txtDollar": "$ 영어 (미국)", "SSE.Views.Toolbar.txtEuro": "€ 유로 (€123)", "SSE.Views.Toolbar.txtExp": "지수", "SSE.Views.Toolbar.txtFillNum": "Fill", "SSE.Views.Toolbar.txtFilter": "필터", "SSE.Views.Toolbar.txtFormula": "함수 삽입", "SSE.Views.Toolbar.txtFraction": "분수", "SSE.Views.Toolbar.txtFranc": "CHF Swiss franc", "SSE.Views.Toolbar.txtGeneral": "일반", "SSE.Views.Toolbar.txtInteger": "정수", "SSE.Views.Toolbar.txtManageRange": "이름 관리자", "SSE.Views.Toolbar.txtMergeAcross": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtMergeCells": "셀 병합", "SSE.Views.Toolbar.txtMergeCenter": "병합 및 센터", "SSE.Views.Toolbar.txtNamedRange": "Named Ranges", "SSE.Views.Toolbar.txtNewRange": "이름 정의", "SSE.Views.Toolbar.txtNoBorders": "테두리 없음", "SSE.Views.Toolbar.txtNumber": "숫자", "SSE.Views.Toolbar.txtPasteRange": "붙여 넣기 이름", "SSE.Views.Toolbar.txtPercentage": "백분율", "SSE.Views.Toolbar.txtPound": "£ 영어 (영국)", "SSE.Views.Toolbar.txtRouble": "₽ 러시아어", "SSE.Views.Toolbar.txtScientific": "지수", "SSE.Views.Toolbar.txtSearch": "검색", "SSE.Views.Toolbar.txtSort": "정렬", "SSE.Views.Toolbar.txtSortAZ": "텍스트 오름차순 정렬", "SSE.Views.Toolbar.txtSortZA": "텍스트 내림차순 정렬", "SSE.Views.Toolbar.txtSpecial": "Special", "SSE.Views.Toolbar.txtTableTemplate": "표 템플릿으로 서식 지정", "SSE.Views.Toolbar.txtText": "텍스트", "SSE.Views.Toolbar.txtTime": "시간", "SSE.Views.Toolbar.txtUnmerge": "셀 병합 해제", "SSE.Views.Toolbar.txtYen": "¥ 일본어", "SSE.Views.Top10FilterDialog.textType": "표시", "SSE.Views.Top10FilterDialog.txtBottom": "Bottom", "SSE.Views.Top10FilterDialog.txtBy": "~로", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtPercent": "백분율", "SSE.Views.Top10FilterDialog.txtSum": "합계", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 AutoFilter", "SSE.Views.Top10FilterDialog.txtTop": "Top", "SSE.Views.Top10FilterDialog.txtValueTitle": "상위 10", "SSE.Views.ValueFieldSettingsDialog.textNext": "(다음)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "숫자 형식", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(이전)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "값 필드 설정", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "평균", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "기본 필드", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "기본 항목", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%2의 %1", "SSE.Views.ValueFieldSettingsDialog.txtCount": "계산", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "수를 집계", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "사용자 정의 이름", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "차이", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "색인", "SSE.Views.ValueFieldSettingsDialog.txtMax": "최대", "SSE.Views.ValueFieldSettingsDialog.txtMin": "최소", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "계산되지 않음", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "백분율", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "%의 차이", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "열 백분율", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "총계의 %", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "상위 합계의 %", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "상위 열 합계의 %", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "상위 행 합계의 %", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "누계 합계 %", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "행 백분율", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "제품", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "오름차순으로 순위 매기기", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "내림차순으로 순위 매기기", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "총실행", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "표시된 값은", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "소스 이름:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "표준편차", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "표준편차", "SSE.Views.ValueFieldSettingsDialog.txtSum": "합계", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "값 필드를 다음과 같이 요약:", "SSE.Views.ValueFieldSettingsDialog.txtVar": "표본분산", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "분산", "SSE.Views.ViewManagerDlg.closeButtonText": "닫기", "SSE.Views.ViewManagerDlg.guestText": "게스트", "SSE.Views.ViewManagerDlg.lockText": "잠김", "SSE.Views.ViewManagerDlg.textDelete": "삭제", "SSE.Views.ViewManagerDlg.textDuplicate": "중복", "SSE.Views.ViewManagerDlg.textEmpty": "아직 생성된 보기가 없습니다.", "SSE.Views.ViewManagerDlg.textGoTo": "보기로 이동", "SSE.Views.ViewManagerDlg.textLongName": "128자 미만의 이름을 입력하세요.", "SSE.Views.ViewManagerDlg.textNew": "새로만들기", "SSE.Views.ViewManagerDlg.textRename": "이름 바꾸기", "SSE.Views.ViewManagerDlg.textRenameError": "보기 이름은 비워둘 수 없습니다.", "SSE.Views.ViewManagerDlg.textRenameLabel": "보기 이름 바꾸기", "SSE.Views.ViewManagerDlg.textViews": "시트 표시", "SSE.Views.ViewManagerDlg.tipIsLocked": "이 요소는 다른 사용자가 편집하고 있습니다.", "SSE.Views.ViewManagerDlg.txtTitle": "시트 표시 관리자", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Are you sure you want to delete this sheet view?", "SSE.Views.ViewManagerDlg.warnDeleteView": "현재 활성화된 보기 '%1'을(를) 삭제하려고 합니다. <br>이 보기를 닫고 삭제하시겠습니까?", "SSE.Views.ViewTab.capBtnFreeze": "창 고정", "SSE.Views.ViewTab.capBtnSheetView": "시트보기", "SSE.Views.ViewTab.textAlwaysShowToolbar": "항상 도구 모음 표시", "SSE.Views.ViewTab.textClose": "닫기", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "상태 표시 줄 숨기기", "SSE.Views.ViewTab.textCreate": "새로만들기", "SSE.Views.ViewTab.textDefault": "기본", "SSE.Views.ViewTab.textFill": "Fill", "SSE.Views.ViewTab.textFormula": "수식 입력줄", "SSE.Views.ViewTab.textFreezeCol": "첫 번째 열 고정", "SSE.Views.ViewTab.textFreezeRow": "첫 번째 행 고정", "SSE.Views.ViewTab.textGridlines": "눈금선", "SSE.Views.ViewTab.textHeadings": "제목", "SSE.Views.ViewTab.textInterfaceTheme": "인터페이스 테마", "SSE.Views.ViewTab.textLeftMenu": "왼쪽 패널", "SSE.Views.ViewTab.textLine": "Line", "SSE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "보기 관리자", "SSE.Views.ViewTab.textRightMenu": "오른쪽 패널", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "틀 고정 음영 표시", "SSE.Views.ViewTab.textTabStyle": "Tab style", "SSE.Views.ViewTab.textUnFreeze": "창 고정 취소", "SSE.Views.ViewTab.textZeros": "０표시", "SSE.Views.ViewTab.textZoom": "확대/축소", "SSE.Views.ViewTab.tipClose": "워크 시트 닫기", "SSE.Views.ViewTab.tipCreate": "시트보기를 만들기", "SSE.Views.ViewTab.tipFreeze": "창 고정", "SSE.Views.ViewTab.tipInterfaceTheme": "인터페이스 테마", "SSE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipSheetView": "시트보기", "SSE.Views.ViewTab.tipViewNormal": "문서를 일반 보기로 표시하세요.", "SSE.Views.ViewTab.tipViewPageBreak": "문서가 인쇄될 때 페이지 구분이 어디에 나타날지 확인하세요.", "SSE.Views.ViewTab.txtViewNormal": "표준", "SSE.Views.ViewTab.txtViewPageBreak": "페이지 구분 미리보기", "SSE.Views.WatchDialog.closeButtonText": "닫기", "SSE.Views.WatchDialog.textAdd": "모니터링 설정 추가", "SSE.Views.WatchDialog.textBook": "문서", "SSE.Views.WatchDialog.textCell": "셀", "SSE.Views.WatchDialog.textDelete": "모니터링 삭제", "SSE.Views.WatchDialog.textDeleteAll": "모두 삭제", "SSE.Views.WatchDialog.textFormula": "수식", "SSE.Views.WatchDialog.textName": "이름", "SSE.Views.WatchDialog.textSheet": "시트", "SSE.Views.WatchDialog.textValue": "값", "SSE.Views.WatchDialog.txtTitle": "모니터링 창", "SSE.Views.WBProtection.hintAllowRanges": "허용 범위 편집", "SSE.Views.WBProtection.hintProtectRange": "보호 범위", "SSE.Views.WBProtection.hintProtectSheet": "시트 보호", "SSE.Views.WBProtection.hintProtectWB": "통합 문서 보호", "SSE.Views.WBProtection.txtAllowRanges": "허용 범위 편집", "SSE.Views.WBProtection.txtHiddenFormula": "수식 숨기기", "SSE.Views.WBProtection.txtLockedCell": "셀 잠금", "SSE.Views.WBProtection.txtLockedShape": "잠긴 도형", "SSE.Views.WBProtection.txtLockedText": "텍스트 잠금", "SSE.Views.WBProtection.txtProtectRange": "보호 범위", "SSE.Views.WBProtection.txtProtectSheet": "시트 보호", "SSE.Views.WBProtection.txtProtectWB": "통합 문서 보호", "SSE.Views.WBProtection.txtSheetUnlockDescription": "양식 보호를 해제하려면 비밀번호를 입력하세요.", "SSE.Views.WBProtection.txtSheetUnlockTitle": "시트 보호해제", "SSE.Views.WBProtection.txtWBUnlockDescription": "통합 문서 보호를 해제하려면 비밀번호를 입력하세요.", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}