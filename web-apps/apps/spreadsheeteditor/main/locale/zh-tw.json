{"cancelButtonText": "取消", "Common.Controllers.Chat.notcriticalErrorTitle": "警告", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.History.notcriticalErrorTitle": "警告", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "區域圖", "Common.define.chartData.textAreaStacked": "堆叠面積", "Common.define.chartData.textAreaStackedPer": "100% 堆疊區域圖", "Common.define.chartData.textBar": "長條圖", "Common.define.chartData.textBarNormal": "叢集柱狀圖", "Common.define.chartData.textBarNormal3d": "3D 分組直式長條圖", "Common.define.chartData.textBarNormal3dPerspective": "3D 直式長條圖", "Common.define.chartData.textBarStacked": "堆疊柱狀圖", "Common.define.chartData.textBarStacked3d": "3D 堆疊直式長條圖", "Common.define.chartData.textBarStackedPer": "100% 堆疊直式長條圖", "Common.define.chartData.textBarStackedPer3d": "3D 100% 堆疊直式長條圖", "Common.define.chartData.textCharts": "流程圖", "Common.define.chartData.textColumn": "欄", "Common.define.chartData.textColumnSpark": "欄", "Common.define.chartData.textCombo": "組合圖", "Common.define.chartData.textComboAreaBar": "堆疊面積-叢集柱狀圖", "Common.define.chartData.textComboBarLine": "叢集柱狀圖 - 折線圖", "Common.define.chartData.textComboBarLineSecondary": "叢集柱狀圖 - 折線圖（次要軸）", "Common.define.chartData.textComboCustom": "自訂組合", "Common.define.chartData.textDoughnut": "環狀圖", "Common.define.chartData.textHBarNormal": "叢集長條圖", "Common.define.chartData.textHBarNormal3d": "3D 分組長條圖", "Common.define.chartData.textHBarStacked": "堆疊長條圖", "Common.define.chartData.textHBarStacked3d": "3D 堆疊長條圖", "Common.define.chartData.textHBarStackedPer": "100% 堆疊長條圖", "Common.define.chartData.textHBarStackedPer3d": "3D 100% 堆疊長條圖", "Common.define.chartData.textLine": "折線圖", "Common.define.chartData.textLine3d": "3D 折線圖", "Common.define.chartData.textLineMarker": "帶標記的線條", "Common.define.chartData.textLineSpark": "折線圖", "Common.define.chartData.textLineStacked": "堆疊折線圖", "Common.define.chartData.textLineStackedMarker": "帶標記的堆疊折線圖", "Common.define.chartData.textLineStackedPer": "100% 堆疊折線圖", "Common.define.chartData.textLineStackedPerMarker": "帶標記的 100% 堆疊折線", "Common.define.chartData.textPie": "圓餅圖", "Common.define.chartData.textPie3d": "3D 圓餅圖", "Common.define.chartData.textPoint": "XY散佈圖", "Common.define.chartData.textRadar": "雷達圖", "Common.define.chartData.textRadarFilled": "填充雷達圖", "Common.define.chartData.textRadarMarker": "含標記的雷達圖", "Common.define.chartData.textScatter": "散佈圖", "Common.define.chartData.textScatterLine": "直線散佈圖", "Common.define.chartData.textScatterLineMarker": "直線和標記的散佈圖", "Common.define.chartData.textScatterSmooth": "平滑線條散佈圖", "Common.define.chartData.textScatterSmoothMarker": "平滑線條和標記的散佈圖", "Common.define.chartData.textSparks": "迷你圖", "Common.define.chartData.textStock": "股票圖", "Common.define.chartData.textSurface": "表面", "Common.define.chartData.textWinLossSpark": "贏/輸", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "無設定格式", "Common.define.conditionalData.text1Above": "1 個標準差以上", "Common.define.conditionalData.text1Below": "1 個標準差以下", "Common.define.conditionalData.text2Above": "2 個標準差以上", "Common.define.conditionalData.text2Below": "2 個標準差以下", "Common.define.conditionalData.text3Above": "3 個標準差以上", "Common.define.conditionalData.text3Below": "3 個標準差以下", "Common.define.conditionalData.textAbove": "上方", "Common.define.conditionalData.textAverage": " 平均", "Common.define.conditionalData.textBegins": "開頭為", "Common.define.conditionalData.textBelow": "下方", "Common.define.conditionalData.textBetween": "介於之間", "Common.define.conditionalData.textBlank": "空白", "Common.define.conditionalData.textBlanks": "包含空白", "Common.define.conditionalData.textBottom": "底部", "Common.define.conditionalData.textContains": "包含", "Common.define.conditionalData.textDataBar": "資料條", "Common.define.conditionalData.textDate": "日期", "Common.define.conditionalData.textDuplicate": "重複", "Common.define.conditionalData.textEnds": "結尾為", "Common.define.conditionalData.textEqAbove": "等於或大於", "Common.define.conditionalData.textEqBelow": "等於或小於", "Common.define.conditionalData.textEqual": "等於", "Common.define.conditionalData.textError": "錯誤", "Common.define.conditionalData.textErrors": "包含錯誤", "Common.define.conditionalData.textFormula": "公式", "Common.define.conditionalData.textGreater": "大於", "Common.define.conditionalData.textGreaterEq": "大於或等於", "Common.define.conditionalData.textIconSets": "圖示集", "Common.define.conditionalData.textLast7days": "在過去的7天內", "Common.define.conditionalData.textLastMonth": "上個月", "Common.define.conditionalData.textLastWeek": "上週", "Common.define.conditionalData.textLess": "小於", "Common.define.conditionalData.textLessEq": "小於或等於", "Common.define.conditionalData.textNextMonth": "下個月", "Common.define.conditionalData.textNextWeek": "下週", "Common.define.conditionalData.textNotBetween": "不在之間", "Common.define.conditionalData.textNotBlanks": "不含空白", "Common.define.conditionalData.textNotContains": "不含", "Common.define.conditionalData.textNotEqual": "不等於", "Common.define.conditionalData.textNotErrors": "不含錯誤", "Common.define.conditionalData.textText": "文字", "Common.define.conditionalData.textThisMonth": "這個月", "Common.define.conditionalData.textThisWeek": "本週", "Common.define.conditionalData.textToday": "今天", "Common.define.conditionalData.textTomorrow": "明天", "Common.define.conditionalData.textTop": "頂部", "Common.define.conditionalData.textUnique": "唯一的", "Common.define.conditionalData.textValue": "值為", "Common.define.conditionalData.textYesterday": "昨天", "Common.define.smartArt.textAccentedPicture": "強調圖片", "Common.define.smartArt.textAccentProcess": "強調流程", "Common.define.smartArt.textAlternatingFlow": "交替流程", "Common.define.smartArt.textAlternatingHexagons": "交替六邊形", "Common.define.smartArt.textAlternatingPictureBlocks": "交替圖片區塊", "Common.define.smartArt.textAlternatingPictureCircles": "交替圖片圓形", "Common.define.smartArt.textArchitectureLayout": "建築佈局", "Common.define.smartArt.textArrowRibbon": "箭頭功能表", "Common.define.smartArt.textAscendingPictureAccentProcess": "遞增圖片裝飾處理", "Common.define.smartArt.textBalance": "平衡", "Common.define.smartArt.textBasicBendingProcess": "基本彎曲程序", "Common.define.smartArt.textBasicBlockList": "基本區塊清單", "Common.define.smartArt.textBasicChevronProcess": "基本箭頭程序", "Common.define.smartArt.textBasicCycle": "基本循環", "Common.define.smartArt.textBasicMatrix": "基本矩陣", "Common.define.smartArt.textBasicPie": "基本圓餅圖", "Common.define.smartArt.textBasicProcess": "基本程序", "Common.define.smartArt.textBasicPyramid": "基本金字塔", "Common.define.smartArt.textBasicRadial": "基本放射狀", "Common.define.smartArt.textBasicTarget": "基本目標", "Common.define.smartArt.textBasicTimeline": "基本時間軸", "Common.define.smartArt.textBasicVenn": "基本文氏圖", "Common.define.smartArt.textBendingPictureAccentList": "彎曲圖片重點清單", "Common.define.smartArt.textBendingPictureBlocks": "彎曲圖片區塊", "Common.define.smartArt.textBendingPictureCaption": "彎曲圖片標題", "Common.define.smartArt.textBendingPictureCaptionList": "彎曲圖片標題清單", "Common.define.smartArt.textBendingPictureSemiTranparentText": "彎曲圖片半透明文字", "Common.define.smartArt.textBlockCycle": "區塊循環", "Common.define.smartArt.textBubblePictureList": "泡泡圖片清單", "Common.define.smartArt.textCaptionedPictures": "帶標題的圖片", "Common.define.smartArt.textChevronAccentProcess": "箭頭強調程序", "Common.define.smartArt.textChevronList": "箭頭清單", "Common.define.smartArt.textCircleAccentTimeline": "圓形強調時間軸", "Common.define.smartArt.textCircleArrowProcess": "圓形箭頭程序", "Common.define.smartArt.textCirclePictureHierarchy": "圓形圖片階層", "Common.define.smartArt.textCircleProcess": "圓形程序", "Common.define.smartArt.textCircleRelationship": "圓形關聯", "Common.define.smartArt.textCircularBendingProcess": "圓形彎曲程序", "Common.define.smartArt.textCircularPictureCallout": "圓形圖片註解", "Common.define.smartArt.textClosedChevronProcess": "閉合式強調程序", "Common.define.smartArt.textContinuousArrowProcess": "連續箭頭流程", "Common.define.smartArt.textContinuousBlockProcess": "連續塊流程", "Common.define.smartArt.textContinuousCycle": "連續循環", "Common.define.smartArt.textContinuousPictureList": "連續圖片列表", "Common.define.smartArt.textConvergingArrows": "匯聚箭頭", "Common.define.smartArt.textConvergingRadial": "匯聚徑向", "Common.define.smartArt.textConvergingText": "匯聚文件", "Common.define.smartArt.textCounterbalanceArrows": "對稱平衡箭頭", "Common.define.smartArt.textCycle": "循環", "Common.define.smartArt.textCycleMatrix": "循環矩陣", "Common.define.smartArt.textDescendingBlockList": "遞減區塊清單", "Common.define.smartArt.textDescendingProcess": "遞減處理", "Common.define.smartArt.textDetailedProcess": "詳細處理", "Common.define.smartArt.textDivergingArrows": "分歧箭頭", "Common.define.smartArt.textDivergingRadial": "分歧徑向", "Common.define.smartArt.textEquation": "方程式", "Common.define.smartArt.textFramedTextPicture": "框線文字圖片", "Common.define.smartArt.textFunnel": "漏斗", "Common.define.smartArt.textGear": "齒輪", "Common.define.smartArt.textGridMatrix": "格狀矩陣", "Common.define.smartArt.textGroupedList": "分組清單", "Common.define.smartArt.textHalfCircleOrganizationChart": "半圓形組織圖", "Common.define.smartArt.textHexagonCluster": "六邊形集群", "Common.define.smartArt.textHexagonRadial": "六邊形放射狀", "Common.define.smartArt.textHierarchy": "層級結構", "Common.define.smartArt.textHierarchyList": "層級結構清單", "Common.define.smartArt.textHorizontalBulletList": "水平項目清單", "Common.define.smartArt.textHorizontalHierarchy": "水平層級結構", "Common.define.smartArt.textHorizontalLabeledHierarchy": "水平標籤層級結構", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "水平多層次層級結構", "Common.define.smartArt.textHorizontalOrganizationChart": "水平組織圖", "Common.define.smartArt.textHorizontalPictureList": "水平圖片清單", "Common.define.smartArt.textIncreasingArrowProcess": "遞增箭頭流程", "Common.define.smartArt.textIncreasingCircleProcess": "遞增圓圈流程", "Common.define.smartArt.textInterconnectedBlockProcess": "互連塊狀流程", "Common.define.smartArt.textInterconnectedRings": "互連環圖", "Common.define.smartArt.textInvertedPyramid": "倒置金字塔", "Common.define.smartArt.textLabeledHierarchy": "標記層次結構", "Common.define.smartArt.textLinearVenn": "線性文氏圖", "Common.define.smartArt.textLinedList": "有格線的清單", "Common.define.smartArt.textList": "清單", "Common.define.smartArt.textMatrix": "矩陣", "Common.define.smartArt.textMultidirectionalCycle": "多方向循環", "Common.define.smartArt.textNameAndTitleOrganizationChart": "名稱與職稱組織圖", "Common.define.smartArt.textNestedTarget": "巢狀目標", "Common.define.smartArt.textNondirectionalCycle": "非定向循環", "Common.define.smartArt.textOpposingArrows": "相對箭頭", "Common.define.smartArt.textOpposingIdeas": "相對觀點", "Common.define.smartArt.textOrganizationChart": "組織圖", "Common.define.smartArt.textOther": "其它", "Common.define.smartArt.textPhasedProcess": "分階段處理", "Common.define.smartArt.textPicture": "圖片", "Common.define.smartArt.textPictureAccentBlocks": "圖片強調區塊", "Common.define.smartArt.textPictureAccentList": "圖片強調清單", "Common.define.smartArt.textPictureAccentProcess": "圖片強調流程", "Common.define.smartArt.textPictureCaptionList": "圖片標題清單", "Common.define.smartArt.textPictureFrame": "圖片框架", "Common.define.smartArt.textPictureGrid": "圖片網格", "Common.define.smartArt.textPictureLineup": "圖片對齊", "Common.define.smartArt.textPictureOrganizationChart": "圖片組織圖", "Common.define.smartArt.textPictureStrips": "圖片條帶", "Common.define.smartArt.textPieProcess": "圓餅圖流程", "Common.define.smartArt.textPlusAndMinus": "加號和減號", "Common.define.smartArt.textProcess": "流程", "Common.define.smartArt.textProcessArrows": "流程箭頭", "Common.define.smartArt.textProcessList": "流程清單", "Common.define.smartArt.textPyramid": "金字塔", "Common.define.smartArt.textPyramidList": "金字塔清單", "Common.define.smartArt.textRadialCluster": "放射狀群集", "Common.define.smartArt.textRadialCycle": "徑向循環", "Common.define.smartArt.textRadialList": "放射狀清單", "Common.define.smartArt.textRadialPictureList": "放射狀圖片清單", "Common.define.smartArt.textRadialVenn": "放射狀文氏圖", "Common.define.smartArt.textRandomToResultProcess": "隨機到結果的流程", "Common.define.smartArt.textRelationship": "關聯性", "Common.define.smartArt.textRepeatingBendingProcess": "重複彎曲流程", "Common.define.smartArt.textReverseList": "反向清單", "Common.define.smartArt.textSegmentedCycle": "分段循環", "Common.define.smartArt.textSegmentedProcess": "分段流程", "Common.define.smartArt.textSegmentedPyramid": "分段金字塔", "Common.define.smartArt.textSnapshotPictureList": "快照圖片清單", "Common.define.smartArt.textSpiralPicture": "螺旋圖片", "Common.define.smartArt.textSquareAccentList": "方形強調清單", "Common.define.smartArt.textStackedList": "堆疊清單", "Common.define.smartArt.textStackedVenn": "堆疊文氏圖", "Common.define.smartArt.textStaggeredProcess": "交錯式流程", "Common.define.smartArt.textStepDownProcess": "向下階梯式流程", "Common.define.smartArt.textStepUpProcess": "向上階梯式流程", "Common.define.smartArt.textSubStepProcess": "子步驟流程", "Common.define.smartArt.textTabbedArc": "帶有制表符的弧形", "Common.define.smartArt.textTableHierarchy": "表格階層", "Common.define.smartArt.textTableList": "表格清單", "Common.define.smartArt.textTabList": "定位清單", "Common.define.smartArt.textTargetList": "目標清單", "Common.define.smartArt.textTextCycle": "文字循環", "Common.define.smartArt.textThemePictureAccent": "主題圖片強調", "Common.define.smartArt.textThemePictureAlternatingAccent": "主題圖片交替強調", "Common.define.smartArt.textThemePictureGrid": "主題圖片網格", "Common.define.smartArt.textTitledMatrix": "標題矩陣", "Common.define.smartArt.textTitledPictureAccentList": "標題圖片重點清單", "Common.define.smartArt.textTitledPictureBlocks": "標題圖片區塊", "Common.define.smartArt.textTitlePictureLineup": "標題圖片對齊", "Common.define.smartArt.textTrapezoidList": "梯形列表", "Common.define.smartArt.textUpwardArrow": "向上箭頭", "Common.define.smartArt.textVaryingWidthList": "不同寬度清單", "Common.define.smartArt.textVerticalAccentList": "垂直強調清單", "Common.define.smartArt.textVerticalArrowList": "垂直箭頭清單", "Common.define.smartArt.textVerticalBendingProcess": "垂直彎曲流程", "Common.define.smartArt.textVerticalBlockList": "垂直區塊清單", "Common.define.smartArt.textVerticalBoxList": "垂直方框清單", "Common.define.smartArt.textVerticalBracketList": "垂直括號清單", "Common.define.smartArt.textVerticalBulletList": "垂直項目清單", "Common.define.smartArt.textVerticalChevronList": "垂直雙箭頭清單", "Common.define.smartArt.textVerticalCircleList": "垂直圓圈清單", "Common.define.smartArt.textVerticalCurvedList": "垂直曲線清單", "Common.define.smartArt.textVerticalEquation": "垂直方程式", "Common.define.smartArt.textVerticalPictureAccentList": "垂直圖片重音清單", "Common.define.smartArt.textVerticalPictureList": "垂直圖片清單", "Common.define.smartArt.textVerticalProcess": "垂直流程", "Common.Translation.textMoreButton": "更多", "Common.Translation.tipFileLocked": "文件正在被鎖定，您可以進行更改並稍後保存為本地副本。", "Common.Translation.tipFileReadOnly": "文件為只讀。要保存更改，請使用新名稱或不同位置保存文件。", "Common.Translation.warnFileLocked": "該文件正在其他應用程序中進行編輯。您可以繼續編輯並將其另存為副本。", "Common.Translation.warnFileLockedBtnEdit": "創建副本", "Common.Translation.warnFileLockedBtnView": "僅供查看", "Common.UI.ButtonColored.textAutoColor": "自動", "Common.UI.ButtonColored.textEyedropper": "吸管", "Common.UI.ButtonColored.textNewColor": "更多顏色", "Common.UI.ComboBorderSize.txtNoBorders": "無邊框", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "無邊框", "Common.UI.ComboDataView.emptyComboText": "無樣式", "Common.UI.ExtendedColorDialog.addButtonText": "新增", "Common.UI.ExtendedColorDialog.textCurrent": "目前", "Common.UI.ExtendedColorDialog.textHexErr": "輸入的值不正確。<br>請輸入一個介於000000和FFFFFF之間的值。", "Common.UI.ExtendedColorDialog.textNew": "新增", "Common.UI.ExtendedColorDialog.textRGBErr": "輸入的值不正確。&lt;br&gt;請輸入介於0和255之間的數值。", "Common.UI.HSBColorPicker.textNoColor": "沒有顏色", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "隱藏密碼", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "顯示密碼", "Common.UI.SearchBar.textFind": "尋找", "Common.UI.SearchBar.tipCloseSearch": "關閉搜尋", "Common.UI.SearchBar.tipNextResult": "下一個結果", "Common.UI.SearchBar.tipOpenAdvancedSettings": "開啟進階設定", "Common.UI.SearchBar.tipPreviousResult": "前一個結果", "Common.UI.SearchDialog.textHighlight": "突顯結果", "Common.UI.SearchDialog.textMatchCase": "區分大小寫", "Common.UI.SearchDialog.textReplaceDef": "請輸入替換文字", "Common.UI.SearchDialog.textSearchStart": "在此輸入您的文字", "Common.UI.SearchDialog.textTitle": "尋找和取代", "Common.UI.SearchDialog.textTitle2": "尋找", "Common.UI.SearchDialog.textWholeWords": "僅完整單詞", "Common.UI.SearchDialog.txtBtnHideReplace": "隱藏取代", "Common.UI.SearchDialog.txtBtnReplace": "取代", "Common.UI.SearchDialog.txtBtnReplaceAll": "全部取代", "Common.UI.SynchronizeTip.textDontShow": "不再顯示此訊息", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "文件已被其他使用者更改。<br>請按一下以保存您的更改並重新載入更新。", "Common.UI.ThemeColorPalette.textRecentColors": "最近使用的顏色", "Common.UI.ThemeColorPalette.textStandartColors": "標準顏色", "Common.UI.ThemeColorPalette.textThemeColors": "主題色彩", "Common.UI.Themes.txtThemeClassicLight": "經典亮色", "Common.UI.Themes.txtThemeContrastDark": "對比度深", "Common.UI.Themes.txtThemeDark": "深色", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "淺色", "Common.UI.Themes.txtThemeSystem": "與系統相同", "Common.UI.Window.cancelButtonText": "取消", "Common.UI.Window.closeButtonText": "關閉", "Common.UI.Window.noButtonText": "否", "Common.UI.Window.okButtonText": "確定", "Common.UI.Window.textConfirmation": "確認", "Common.UI.Window.textDontShow": "不再顯示此消息", "Common.UI.Window.textError": "錯誤", "Common.UI.Window.textInformation": "資訊", "Common.UI.Window.textWarning": "警告", "Common.UI.Window.yesButtonText": "是", "Common.Utils.Metric.txtCm": "公分", "Common.Utils.Metric.txtPt": "點（排版單位）", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "移動", "Common.Utils.ThemeColor.txtaccent": "強調", "Common.Utils.ThemeColor.txtAqua": "水藍色", "Common.Utils.ThemeColor.txtbackground": "背景", "Common.Utils.ThemeColor.txtBlack": "黑色", "Common.Utils.ThemeColor.txtBlue": "藍色", "Common.Utils.ThemeColor.txtBrightGreen": "明亮綠色", "Common.Utils.ThemeColor.txtBrown": "棕色", "Common.Utils.ThemeColor.txtDarkBlue": "深藍色", "Common.Utils.ThemeColor.txtDarker": "更暗", "Common.Utils.ThemeColor.txtDarkGray": "深灰色", "Common.Utils.ThemeColor.txtDarkGreen": "深綠色", "Common.Utils.ThemeColor.txtDarkPurple": "深紫色", "Common.Utils.ThemeColor.txtDarkRed": "深紅色", "Common.Utils.ThemeColor.txtDarkTeal": "深青色", "Common.Utils.ThemeColor.txtDarkYellow": "深黃色", "Common.Utils.ThemeColor.txtGold": "金色", "Common.Utils.ThemeColor.txtGray": "灰色", "Common.Utils.ThemeColor.txtGreen": "綠色", "Common.Utils.ThemeColor.txtIndigo": "靛藍色", "Common.Utils.ThemeColor.txtLavender": "薰衣草色", "Common.Utils.ThemeColor.txtLightBlue": "淺藍色", "Common.Utils.ThemeColor.txtLighter": "較淺的", "Common.Utils.ThemeColor.txtLightGray": "淺灰色", "Common.Utils.ThemeColor.txtLightGreen": "淺綠色", "Common.Utils.ThemeColor.txtLightOrange": "淺橙色", "Common.Utils.ThemeColor.txtLightYellow": "淺黃色", "Common.Utils.ThemeColor.txtOrange": "橙色", "Common.Utils.ThemeColor.txtPink": "粉紅色", "Common.Utils.ThemeColor.txtPurple": "紫色", "Common.Utils.ThemeColor.txtRed": "紅色", "Common.Utils.ThemeColor.txtRose": "玫瑰色", "Common.Utils.ThemeColor.txtSkyBlue": "天藍色", "Common.Utils.ThemeColor.txtTeal": "藍綠色", "Common.Utils.ThemeColor.txttext": "文字", "Common.Utils.ThemeColor.txtTurquosie": "藍綠色", "Common.Utils.ThemeColor.txtViolet": "紫羅蘭色", "Common.Utils.ThemeColor.txtWhite": "白色", "Common.Utils.ThemeColor.txtYellow": "黃色", "Common.Views.About.txtAddress": "地址:", "Common.Views.About.txtLicensee": "被授權方", "Common.Views.About.txtLicensor": "授權方", "Common.Views.About.txtMail": "電子郵件：", "Common.Views.About.txtPoweredBy": "由...提供", "Common.Views.About.txtTel": "電話: ", "Common.Views.About.txtVersion": "版本", "Common.Views.AutoCorrectDialog.textAdd": "新增", "Common.Views.AutoCorrectDialog.textApplyAsWork": "隨著工作進行應用", "Common.Views.AutoCorrectDialog.textAutoCorrect": "自動校正", "Common.Views.AutoCorrectDialog.textAutoFormat": "輸入時自動套用格式", "Common.Views.AutoCorrectDialog.textBy": "依照", "Common.Views.AutoCorrectDialog.textDelete": "刪除", "Common.Views.AutoCorrectDialog.textHyperlink": "以超連結取代網際網路和網路路徑", "Common.Views.AutoCorrectDialog.textMathCorrect": "數學自動校正", "Common.Views.AutoCorrectDialog.textNewRowCol": "包含新的行和列到表格中", "Common.Views.AutoCorrectDialog.textRecognized": "可辨識的數學函數", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "下面的運算式是可辨識的數學運算式，將不會自動以斜體表示", "Common.Views.AutoCorrectDialog.textReplace": "取代", "Common.Views.AutoCorrectDialog.textReplaceText": "輸入時替換文字", "Common.Views.AutoCorrectDialog.textReplaceType": "輸入時替換文字", "Common.Views.AutoCorrectDialog.textReset": "重置", "Common.Views.AutoCorrectDialog.textResetAll": "重設為預設值", "Common.Views.AutoCorrectDialog.textRestore": "恢復", "Common.Views.AutoCorrectDialog.textTitle": "自動校正", "Common.Views.AutoCorrectDialog.textWarnAddRec": "識別的函數只能包含字母A到Z，大寫或小寫。", "Common.Views.AutoCorrectDialog.textWarnResetRec": "您新增的任何運算式將被刪除，已刪除的運算式將被恢復。是否要繼續？", "Common.Views.AutoCorrectDialog.warnReplace": "%1的自動更正項已經存在。是否要更換？", "Common.Views.AutoCorrectDialog.warnReset": "您新增的任何自動更正將被刪除，已更改的自動更正將恢復為其原始值。是否要繼續？", "Common.Views.AutoCorrectDialog.warnRestore": "%1的自動更正項目將被重置為其原始值。是否要繼續？", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "發送", "Common.Views.Comments.mniAuthorAsc": "作者 A 到 Z", "Common.Views.Comments.mniAuthorDesc": "作者 Z 到 A", "Common.Views.Comments.mniDateAsc": "最舊的", "Common.Views.Comments.mniDateDesc": "最新的", "Common.Views.Comments.mniFilterGroups": "依群組篩選", "Common.Views.Comments.mniPositionAsc": "從頂部", "Common.Views.Comments.mniPositionDesc": "來自底部", "Common.Views.Comments.textAdd": "新增", "Common.Views.Comments.textAddComment": "新增註解", "Common.Views.Comments.textAddCommentToDoc": "在文檔中添加評論", "Common.Views.Comments.textAddReply": "新增回覆", "Common.Views.Comments.textAll": "全部", "Common.Views.Comments.textAnonym": "訪客", "Common.Views.Comments.textCancel": "取消", "Common.Views.Comments.textClose": "關閉", "Common.Views.Comments.textClosePanel": "關閉評論", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "評論", "Common.Views.Comments.textEdit": "確定", "Common.Views.Comments.textEnterCommentHint": "在此輸入您的評論", "Common.Views.Comments.textHintAddComment": "新增註解", "Common.Views.Comments.textOpenAgain": "重新開啟", "Common.Views.Comments.textReply": "回覆", "Common.Views.Comments.textResolve": "解決", "Common.Views.Comments.textResolved": "已解決", "Common.Views.Comments.textSort": "排序註解", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "您沒有重新開啟評論的權限", "Common.Views.Comments.txtEmpty": "工作表中沒有備註。", "Common.Views.CopyWarningDialog.textDontShow": "不再顯示此消息", "Common.Views.CopyWarningDialog.textMsg": "使用編輯器工具列按鈕和內容選單操作進行的複製、剪下和貼上動作僅在此編輯器標籤中執行。&lt;br&gt;&lt;br&gt;若要在編輯器標籤以外的應用程式中複製或貼上，請使用以下鍵盤組合：", "Common.Views.CopyWarningDialog.textTitle": "複製、剪下和貼上動作", "Common.Views.CopyWarningDialog.textToCopy": "適用於複製", "Common.Views.CopyWarningDialog.textToCut": "適用於剪下", "Common.Views.CopyWarningDialog.textToPaste": "適用於貼上", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "載入中...", "Common.Views.DocumentAccessDialog.textTitle": "共用設定", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "橡皮擦", "Common.Views.Draw.hintSelect": "選擇", "Common.Views.Draw.txtEraser": "橡皮擦", "Common.Views.Draw.txtHighlighter": "螢光筆", "Common.Views.Draw.txtMM": "毫米", "Common.Views.Draw.txtPen": "筆", "Common.Views.Draw.txtSelect": "選擇", "Common.Views.Draw.txtSize": "大小", "Common.Views.EditNameDialog.textLabel": "標籤：", "Common.Views.EditNameDialog.textLabelError": "標籤不能為空。", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "正在編輯文件的使用者：", "Common.Views.Header.textAddFavorite": "標記為常用項目", "Common.Views.Header.textAdvSettings": "進階設定", "Common.Views.Header.textBack": "打開檔案位置", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "隱藏工具列", "Common.Views.Header.textHideLines": "隱藏標尺", "Common.Views.Header.textHideStatusBar": "合併工作表和狀態列", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "唯讀", "Common.Views.Header.textRemoveFavorite": "\n從最愛收藏夾中刪除", "Common.Views.Header.textSaveBegin": "保存中...", "Common.Views.Header.textSaveChanged": "已更改", "Common.Views.Header.textSaveEnd": "所有更動已儲存", "Common.Views.Header.textSaveExpander": "所有更動已儲存", "Common.Views.Header.textShare": "分享", "Common.Views.Header.textZoom": "縮放", "Common.Views.Header.tipAccessRights": "管理文檔存取權限", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "下載文件", "Common.Views.Header.tipGoEdit": "編輯當前檔案", "Common.Views.Header.tipPrint": "列印檔案", "Common.Views.Header.tipPrintQuick": "快速列印", "Common.Views.Header.tipRedo": "重做", "Common.Views.Header.tipSave": "儲存", "Common.Views.Header.tipSearch": "搜尋", "Common.Views.Header.tipUndo": "復原", "Common.Views.Header.tipUndock": "拆分成獨立視窗", "Common.Views.Header.tipUsers": "檢視使用者", "Common.Views.Header.tipViewSettings": "檢視設定", "Common.Views.Header.tipViewUsers": "檢視使用者並管理文件存取權限", "Common.Views.Header.txtAccessRights": "變更存取權限", "Common.Views.Header.txtRename": "重新命名", "Common.Views.History.textCloseHistory": "關閉歷史紀錄", "Common.Views.History.textHideAll": "隱藏詳細更改", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "恢復", "Common.Views.History.textShowAll": "顯示詳細變更", "Common.Views.History.textVer": "版本", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "貼上圖片URL：", "Common.Views.ImageFromUrlDialog.txtEmpty": "此欄位為必填欄位", "Common.Views.ImageFromUrlDialog.txtNotUrl": "此欄位應為符合「http://www.example.com」格式的網址。", "Common.Views.ListSettingsDialog.textBulleted": "項目符號", "Common.Views.ListSettingsDialog.textFromFile": "從檔案插入", "Common.Views.ListSettingsDialog.textFromStorage": "從儲存位置插入", "Common.Views.ListSettingsDialog.textFromUrl": "從網址", "Common.Views.ListSettingsDialog.textNumbering": "編號", "Common.Views.ListSettingsDialog.textSelect": "從...選取", "Common.Views.ListSettingsDialog.tipChange": "變更項目符號", "Common.Views.ListSettingsDialog.txtBullet": "項目符號", "Common.Views.ListSettingsDialog.txtColor": "顏色", "Common.Views.ListSettingsDialog.txtImage": "圖片", "Common.Views.ListSettingsDialog.txtImport": "匯入", "Common.Views.ListSettingsDialog.txtNewBullet": "新增項目符號", "Common.Views.ListSettingsDialog.txtNewImage": "新圖像", "Common.Views.ListSettingsDialog.txtNone": "無", "Common.Views.ListSettingsDialog.txtOfText": "文字百分比", "Common.Views.ListSettingsDialog.txtSize": "大小", "Common.Views.ListSettingsDialog.txtStart": "從...開始", "Common.Views.ListSettingsDialog.txtSymbol": "符號", "Common.Views.ListSettingsDialog.txtTitle": "清單設定", "Common.Views.ListSettingsDialog.txtType": "類型", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "關閉檔案", "Common.Views.OpenDialog.textInvalidRange": "無效的儲存格範圍", "Common.Views.OpenDialog.textSelectData": "選取資料", "Common.Views.OpenDialog.txtAdvanced": "進階", "Common.Views.OpenDialog.txtColon": "冒號", "Common.Views.OpenDialog.txtComma": "逗號", "Common.Views.OpenDialog.txtDelimiter": "分隔符", "Common.Views.OpenDialog.txtDestData": "選擇要放置資料的位置", "Common.Views.OpenDialog.txtEmpty": "此欄位為必填欄位", "Common.Views.OpenDialog.txtEncoding": "編碼", "Common.Views.OpenDialog.txtIncorrectPwd": "密碼不正確。", "Common.Views.OpenDialog.txtOpenFile": "輸入檔案密碼", "Common.Views.OpenDialog.txtOther": "其它", "Common.Views.OpenDialog.txtPassword": "密碼", "Common.Views.OpenDialog.txtPreview": "預覽", "Common.Views.OpenDialog.txtProtected": "當您輸入密碼並打開文件後，該文件的當前密碼將被重置。", "Common.Views.OpenDialog.txtSemicolon": "分號", "Common.Views.OpenDialog.txtSpace": "空格", "Common.Views.OpenDialog.txtTab": "標籤", "Common.Views.OpenDialog.txtTitle": "選擇%1個選項", "Common.Views.OpenDialog.txtTitleProtected": "受保護的檔案", "Common.Views.PasswordDialog.txtDescription": "設定密碼以保護此文件。", "Common.Views.PasswordDialog.txtIncorrectPwd": "確認密碼不相同", "Common.Views.PasswordDialog.txtPassword": "密碼", "Common.Views.PasswordDialog.txtRepeat": "重複輸入密碼", "Common.Views.PasswordDialog.txtTitle": "設定密碼", "Common.Views.PasswordDialog.txtWarning": "警告：如果您遺失或忘記密碼，將無法恢復。請將密碼保存在安全的地方。", "Common.Views.PluginDlg.textLoading": "載入中", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "外掛程式", "Common.Views.Plugins.strPlugins": "外掛程式", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "開始", "Common.Views.Plugins.textStop": "停止", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "使用密碼進行加密", "Common.Views.Protection.hintDelPwd": "刪除密碼", "Common.Views.Protection.hintPwd": "變更或刪除密碼", "Common.Views.Protection.hintSignature": "新增數位簽章或簽名行", "Common.Views.Protection.txtAddPwd": "新增密碼", "Common.Views.Protection.txtChangePwd": "變更密碼", "Common.Views.Protection.txtDeletePwd": "刪除密碼", "Common.Views.Protection.txtEncrypt": "加密", "Common.Views.Protection.txtInvisibleSignature": "新增數位簽章", "Common.Views.Protection.txtSignature": "簽名", "Common.Views.Protection.txtSignatureLine": "新增簽名行", "Common.Views.RecentFiles.txtOpenRecent": "Open Recent", "Common.Views.RenameDialog.textName": "檔案名稱", "Common.Views.RenameDialog.txtInvalidName": "文件名不能包含以下任何字符：", "Common.Views.ReviewChanges.hintNext": "到下一個變更處", "Common.Views.ReviewChanges.hintPrev": "到前一個變更處", "Common.Views.ReviewChanges.strFast": "快速", "Common.Views.ReviewChanges.strFastDesc": "即時共同編輯。所有更改都會自動儲存。", "Common.Views.ReviewChanges.strStrict": "嚴格", "Common.Views.ReviewChanges.strStrictDesc": "使用「儲存」按鈕同步您和其他人所做的更改", "Common.Views.ReviewChanges.tipAcceptCurrent": "同意當前更改", "Common.Views.ReviewChanges.tipCoAuthMode": "設定共同編輯模式", "Common.Views.ReviewChanges.tipCommentRem": "刪除評論", "Common.Views.ReviewChanges.tipCommentRemCurrent": "刪除當前評論", "Common.Views.ReviewChanges.tipCommentResolve": "標記註解為已解決", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "將所有的註解標記為已解決", "Common.Views.ReviewChanges.tipHistory": "顯示版本歷史", "Common.Views.ReviewChanges.tipRejectCurrent": "拒絕當前變更", "Common.Views.ReviewChanges.tipReview": "追蹤變更", "Common.Views.ReviewChanges.tipReviewView": "選擇要顯示變更的模式", "Common.Views.ReviewChanges.tipSetDocLang": "設定文件語言", "Common.Views.ReviewChanges.tipSetSpelling": "拼字檢查", "Common.Views.ReviewChanges.tipSharing": "管理文檔存取權限", "Common.Views.ReviewChanges.txtAccept": "同意", "Common.Views.ReviewChanges.txtAcceptAll": "接受所有變更", "Common.Views.ReviewChanges.txtAcceptChanges": "同意更改", "Common.Views.ReviewChanges.txtAcceptCurrent": "接受目前的變更", "Common.Views.ReviewChanges.txtChat": "聊天", "Common.Views.ReviewChanges.txtClose": "關閉", "Common.Views.ReviewChanges.txtCoAuthMode": "共同編輯模式", "Common.Views.ReviewChanges.txtCommentRemAll": "刪除所有評論", "Common.Views.ReviewChanges.txtCommentRemCurrent": "刪除當前評論", "Common.Views.ReviewChanges.txtCommentRemMy": "刪除我的評論", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "刪除我當前的評論", "Common.Views.ReviewChanges.txtCommentRemove": "刪除", "Common.Views.ReviewChanges.txtCommentResolve": "解決", "Common.Views.ReviewChanges.txtCommentResolveAll": "標記註解為已解決", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "解決目前註解", "Common.Views.ReviewChanges.txtCommentResolveMy": "解決我的註解", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "將自己的註解標記為已解決", "Common.Views.ReviewChanges.txtDocLang": "語言", "Common.Views.ReviewChanges.txtFinal": "接受所有變更（預覽）", "Common.Views.ReviewChanges.txtFinalCap": "最終的", "Common.Views.ReviewChanges.txtHistory": "版本歷史", "Common.Views.ReviewChanges.txtMarkup": "所有變更(編輯中)", "Common.Views.ReviewChanges.txtMarkupCap": "標記", "Common.Views.ReviewChanges.txtNext": "下一個", "Common.Views.ReviewChanges.txtOriginal": "拒絕所有變更（預覽）", "Common.Views.ReviewChanges.txtOriginalCap": "原始的", "Common.Views.ReviewChanges.txtPrev": "前一個", "Common.Views.ReviewChanges.txtReject": "拒絕", "Common.Views.ReviewChanges.txtRejectAll": "拒絕所有更改", "Common.Views.ReviewChanges.txtRejectChanges": "拒絕變更", "Common.Views.ReviewChanges.txtRejectCurrent": "拒絕當前變更", "Common.Views.ReviewChanges.txtSharing": "分享", "Common.Views.ReviewChanges.txtSpelling": "拼字檢查", "Common.Views.ReviewChanges.txtTurnon": "追蹤變更", "Common.Views.ReviewChanges.txtView": "顯示模式", "Common.Views.ReviewPopover.textAdd": "新增", "Common.Views.ReviewPopover.textAddReply": "新增回覆", "Common.Views.ReviewPopover.textCancel": "取消", "Common.Views.ReviewPopover.textClose": "關閉", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "確定", "Common.Views.ReviewPopover.textEnterComment": "在此輸入您的評論", "Common.Views.ReviewPopover.textMention": "\"+ 提及將提供對文件的訪問權限並發送電子郵件\"", "Common.Views.ReviewPopover.textMentionNotify": "\"+提及將通過電子郵件通知使用者\"", "Common.Views.ReviewPopover.textOpenAgain": "重新開啟", "Common.Views.ReviewPopover.textReply": "回覆", "Common.Views.ReviewPopover.textResolve": "解決", "Common.Views.ReviewPopover.textViewResolved": "您沒有重新開啟評論的權限", "Common.Views.ReviewPopover.txtDeleteTip": "刪除", "Common.Views.ReviewPopover.txtEditTip": "編輯", "Common.Views.SaveAsDlg.textLoading": "載入中", "Common.Views.SaveAsDlg.textTitle": "保存文件夾", "Common.Views.SearchPanel.textByColumns": "按欄", "Common.Views.SearchPanel.textByRows": "按列", "Common.Views.SearchPanel.textCaseSensitive": "區分大小寫", "Common.Views.SearchPanel.textCell": "儲存格", "Common.Views.SearchPanel.textCloseSearch": "關閉搜尋", "Common.Views.SearchPanel.textContentChanged": "文件已更改。", "Common.Views.SearchPanel.textFind": "尋找", "Common.Views.SearchPanel.textFindAndReplace": "尋找和取代", "Common.Views.SearchPanel.textFormula": "公式", "Common.Views.SearchPanel.textFormulas": "公式", "Common.Views.SearchPanel.textItemEntireCell": "整個儲存格內容", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "已成功取代 {0} 個項目。", "Common.Views.SearchPanel.textLookIn": "查詢", "Common.Views.SearchPanel.textMatchUsingRegExp": "使用正規表達式比對", "Common.Views.SearchPanel.textName": "名稱", "Common.Views.SearchPanel.textNoMatches": "沒有符合的結果", "Common.Views.SearchPanel.textNoSearchResults": "無搜索结果", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "已取代 {0}/{1} 個項目。剩餘 {2} 個項目被其他使用者鎖定。", "Common.Views.SearchPanel.textReplace": "取代", "Common.Views.SearchPanel.textReplaceAll": "全部取代", "Common.Views.SearchPanel.textReplaceWith": "取代為", "Common.Views.SearchPanel.textSearch": "搜尋", "Common.Views.SearchPanel.textSearchAgain": "進行新的搜尋以獲得準確的結果。", "Common.Views.SearchPanel.textSearchHasStopped": "已停止搜尋", "Common.Views.SearchPanel.textSearchOptions": "搜索選項", "Common.Views.SearchPanel.textSearchResults": "搜索结果：{0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textSelectDataRange": "選取資料範圍", "Common.Views.SearchPanel.textSheet": "工作表", "Common.Views.SearchPanel.textSpecificRange": "特定範圍", "Common.Views.SearchPanel.textTooManyResults": "因數量過多而無法顯示部分結果", "Common.Views.SearchPanel.textValue": "值", "Common.Views.SearchPanel.textValues": "值", "Common.Views.SearchPanel.textWholeWords": "僅完整單詞", "Common.Views.SearchPanel.textWithin": "介於", "Common.Views.SearchPanel.textWorkbook": "工作簿", "Common.Views.SearchPanel.tipNextResult": "下一個結果", "Common.Views.SearchPanel.tipPreviousResult": "前一個結果", "Common.Views.SelectFileDlg.textLoading": "載入中", "Common.Views.SelectFileDlg.textTitle": "選擇資料來源", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "粗體", "Common.Views.SignDialog.textCertificate": "證書", "Common.Views.SignDialog.textChange": "變更", "Common.Views.SignDialog.textInputName": "輸入簽署者名稱", "Common.Views.SignDialog.textItalic": "斜體", "Common.Views.SignDialog.textNameError": "簽署者名稱不得為空白。", "Common.Views.SignDialog.textPurpose": "簽署本文件的目的", "Common.Views.SignDialog.textSelect": "選擇", "Common.Views.SignDialog.textSelectImage": "選擇圖片", "Common.Views.SignDialog.textSignature": "簽名外觀如下", "Common.Views.SignDialog.textTitle": "簽署文件", "Common.Views.SignDialog.textUseImage": "或點選「選取圖片」以使用圖片作為簽名", "Common.Views.SignDialog.textValid": "有效期從 %1 到 %2", "Common.Views.SignDialog.tipFontName": "字型名稱", "Common.Views.SignDialog.tipFontSize": "字型大小", "Common.Views.SignSettingsDialog.textAllowComment": "允許簽名者在簽名對話方塊中添加註解", "Common.Views.SignSettingsDialog.textDefInstruction": "在簽署此文件前，請確認您簽署的內容正確無誤。", "Common.Views.SignSettingsDialog.textInfoEmail": "建議簽署人的電子郵件", "Common.Views.SignSettingsDialog.textInfoName": "建議簽署人", "Common.Views.SignSettingsDialog.textInfoTitle": "建議簽署人稱謂", "Common.Views.SignSettingsDialog.textInstructions": "簽署者說明", "Common.Views.SignSettingsDialog.textShowDate": "在簽名行中顯示簽署日期", "Common.Views.SignSettingsDialog.textTitle": "簽名設定", "Common.Views.SignSettingsDialog.txtEmpty": "此欄位為必填欄位", "Common.Views.SymbolTableDialog.textCharacter": "字元", "Common.Views.SymbolTableDialog.textCode": "Unicode十六進制值", "Common.Views.SymbolTableDialog.textCopyright": "版權符號", "Common.Views.SymbolTableDialog.textDCQuote": "結束的雙引號", "Common.Views.SymbolTableDialog.textDOQuote": "開始的雙引號", "Common.Views.SymbolTableDialog.textEllipsis": "水平省略號", "Common.Views.SymbolTableDialog.textEmDash": "破折號", "Common.Views.SymbolTableDialog.textEmSpace": "字寬空白", "Common.Views.SymbolTableDialog.textEnDash": "短橫線", "Common.Views.SymbolTableDialog.textEnSpace": "字元寬空白", "Common.Views.SymbolTableDialog.textFont": "字型", "Common.Views.SymbolTableDialog.textNBHyphen": "非斷行連字號", "Common.Views.SymbolTableDialog.textNBSpace": "不中斷空格", "Common.Views.SymbolTableDialog.textPilcrow": "段落符號", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 字寬空白", "Common.Views.SymbolTableDialog.textRange": "範圍", "Common.Views.SymbolTableDialog.textRecent": "最近使用的符號", "Common.Views.SymbolTableDialog.textRegistered": "註冊標誌", "Common.Views.SymbolTableDialog.textSCQuote": "結束的單引號", "Common.Views.SymbolTableDialog.textSection": "章節標誌", "Common.Views.SymbolTableDialog.textShortcut": "快捷鍵", "Common.Views.SymbolTableDialog.textSHyphen": "軟連字號", "Common.Views.SymbolTableDialog.textSOQuote": "開始的單引號", "Common.Views.SymbolTableDialog.textSpecial": "特殊字符", "Common.Views.SymbolTableDialog.textSymbols": "符號", "Common.Views.SymbolTableDialog.textTitle": "符號", "Common.Views.SymbolTableDialog.textTradeMark": "商標符號", "Common.Views.UserNameDialog.textDontShow": "不要再次詢問我", "Common.Views.UserNameDialog.textLabel": "標籤：", "Common.Views.UserNameDialog.textLabelError": "標籤不能為空。", "SSE.Controllers.DataTab.strSheet": "工作表", "SSE.Controllers.DataTab.textAddExternalData": "已添加對外部源的鏈接。您可以在“數據”選項卡中更新此類鏈接。", "SSE.Controllers.DataTab.textColumns": "欄", "SSE.Controllers.DataTab.textContinue": "Continue", "SSE.Controllers.DataTab.textDontUpdate": "不要更新", "SSE.Controllers.DataTab.textEmptyUrl": "您需要指定的網址。", "SSE.Controllers.DataTab.textRows": "行列", "SSE.Controllers.DataTab.textTurnOff": "Turn off AutoUpdate", "SSE.Controllers.DataTab.textUpdate": "更新", "SSE.Controllers.DataTab.textWizard": "文字轉欄", "SSE.Controllers.DataTab.txtDataValidation": "資料驗證", "SSE.Controllers.DataTab.txtErrorExternalLink": "錯誤：更新失敗", "SSE.Controllers.DataTab.txtExpand": "展開", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "選擇範圍旁邊的數據將不會被刪除。您要擴展選擇範圍以包括相鄰數據還是僅繼續使用當前選定的單元格？", "SSE.Controllers.DataTab.txtExtendDataValidation": "所選範圍中包含一些沒有數據驗證設置的單元格。&lt;br&gt;是否將數據驗證擴展到這些單元格？", "SSE.Controllers.DataTab.txtImportWizard": "文字彙入精靈", "SSE.Controllers.DataTab.txtRemDuplicates": "移除重複項目", "SSE.Controllers.DataTab.txtRemoveDataValidation": "所選範圍包含多種類型的驗證。&lt;br&gt;清除當前設置並繼續嗎？", "SSE.Controllers.DataTab.txtRemSelected": "在所選範圍中刪除", "SSE.Controllers.DataTab.txtUrlTitle": "貼上資料URL", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "This workbook contains links to external sources which update automatically. This might be unsafe.<br><br>If you trust them, press Continue.", "SSE.Controllers.DataTab.warnUpdateExternalData": "此工作簿包含指向一個或多個可能不安全的外部來源的連結。如果您信任這些連結，請更新它們以獲取最新的資料。", "SSE.Controllers.DocumentHolder.alignmentText": "對齊方式", "SSE.Controllers.DocumentHolder.centerText": "置中", "SSE.Controllers.DocumentHolder.deleteColumnText": "刪除欄", "SSE.Controllers.DocumentHolder.deleteRowText": "刪除列", "SSE.Controllers.DocumentHolder.deleteText": "刪除", "SSE.Controllers.DocumentHolder.errorInvalidLink": "引用的連結不存在。請更正連結或將其刪除。", "SSE.Controllers.DocumentHolder.guestText": "訪客", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "左欄", "SSE.Controllers.DocumentHolder.insertColumnRightText": "右欄", "SSE.Controllers.DocumentHolder.insertRowAboveText": "上方列", "SSE.Controllers.DocumentHolder.insertRowBelowText": "下方列", "SSE.Controllers.DocumentHolder.insertText": "插入", "SSE.Controllers.DocumentHolder.leftText": "左", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "警告", "SSE.Controllers.DocumentHolder.rightText": "右", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "自動更正選項", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "欄寬 {0} 個符號 ({1} 像素)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "列高 {0} 點 ({1} 像素)", "SSE.Controllers.DocumentHolder.textCtrlClick": "點擊連結以開啟它，或按住滑鼠按鍵以選取儲存格。", "SSE.Controllers.DocumentHolder.textInsertLeft": "插入左側欄位", "SSE.Controllers.DocumentHolder.textInsertTop": "在上方插入列", "SSE.Controllers.DocumentHolder.textPasteSpecial": "貼上特殊", "SSE.Controllers.DocumentHolder.textStopExpand": "停止自動展開表格", "SSE.Controllers.DocumentHolder.textSym": "符號", "SSE.Controllers.DocumentHolder.tipIsLocked": "該元素正在被其他使用者編輯。", "SSE.Controllers.DocumentHolder.txtAboveAve": "高於平均值", "SSE.Controllers.DocumentHolder.txtAddBottom": "新增底部邊框", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "添加分數欄", "SSE.Controllers.DocumentHolder.txtAddHor": "添加水平線", "SSE.Controllers.DocumentHolder.txtAddLB": "新增左下線", "SSE.Controllers.DocumentHolder.txtAddLeft": "新增左側框線", "SSE.Controllers.DocumentHolder.txtAddLT": "新增左上線", "SSE.Controllers.DocumentHolder.txtAddRight": "新增右側框線", "SSE.Controllers.DocumentHolder.txtAddTop": "新增頂部框線", "SSE.Controllers.DocumentHolder.txtAddVer": "新增垂直線", "SSE.Controllers.DocumentHolder.txtAlignToChar": "對齊至字元", "SSE.Controllers.DocumentHolder.txtAll": "(全部)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "返回包括列標題、資料和總計列在內的整個表格或指定表格列的內容", "SSE.Controllers.DocumentHolder.txtAnd": "和", "SSE.Controllers.DocumentHolder.txtBegins": "開頭為", "SSE.Controllers.DocumentHolder.txtBelowAve": "低於平均值", "SSE.Controllers.DocumentHolder.txtBlanks": "（空白）", "SSE.Controllers.DocumentHolder.txtBorderProps": "邊框屬性", "SSE.Controllers.DocumentHolder.txtBottom": "底部", "SSE.Controllers.DocumentHolder.txtByField": "%1 的 %2", "SSE.Controllers.DocumentHolder.txtColumn": "欄", "SSE.Controllers.DocumentHolder.txtColumnAlign": "欄對齊", "SSE.Controllers.DocumentHolder.txtContains": "包含", "SSE.Controllers.DocumentHolder.txtCopySuccess": "連結已複製到剪貼板", "SSE.Controllers.DocumentHolder.txtDataTableHint": "返回表格或指定表格列的資料儲存格", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "減少參數大小", "SSE.Controllers.DocumentHolder.txtDeleteArg": "刪除參數", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "刪除手動分頁", "SSE.Controllers.DocumentHolder.txtDeleteChars": "刪除封閉字元", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "刪除封閉字元和分隔符號", "SSE.Controllers.DocumentHolder.txtDeleteEq": "刪除方程式", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "刪除字元", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "刪除根號", "SSE.Controllers.DocumentHolder.txtEnds": "結尾為", "SSE.Controllers.DocumentHolder.txtEquals": "等於", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "等於儲存格顏色", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "等於字型顏色", "SSE.Controllers.DocumentHolder.txtExpand": "展開並排序", "SSE.Controllers.DocumentHolder.txtExpandSort": "選擇範圍旁邊的數據將不會被刪除。您要擴展選擇範圍以包括相鄰數據還是僅繼續使用當前選定的單元格？", "SSE.Controllers.DocumentHolder.txtFilterBottom": "底部", "SSE.Controllers.DocumentHolder.txtFilterTop": "頂部", "SSE.Controllers.DocumentHolder.txtFractionLinear": "變更為線性分數", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "變更為斜角分數", "SSE.Controllers.DocumentHolder.txtFractionStacked": "變更為堆疊分數", "SSE.Controllers.DocumentHolder.txtGreater": "大於", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "大於或等於", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "字元上方", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "字元下方", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "返回表格或指定表格列的列標題", "SSE.Controllers.DocumentHolder.txtHeight": "\n高度", "SSE.Controllers.DocumentHolder.txtHideBottom": "隱藏底部邊框", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "隱藏底部限制", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "隱藏閉括號", "SSE.Controllers.DocumentHolder.txtHideDegree": "隱藏度數", "SSE.Controllers.DocumentHolder.txtHideHor": "隱藏水平線", "SSE.Controllers.DocumentHolder.txtHideLB": "隱藏左下線", "SSE.Controllers.DocumentHolder.txtHideLeft": "隱藏左邊框", "SSE.Controllers.DocumentHolder.txtHideLT": "隱藏左上線", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "隱藏開括號", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "隱藏佔位符", "SSE.Controllers.DocumentHolder.txtHideRight": "隱藏右邊框", "SSE.Controllers.DocumentHolder.txtHideTop": "隱藏頂部邊框", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "隱藏頂部限制", "SSE.Controllers.DocumentHolder.txtHideVer": "隱藏垂直線", "SSE.Controllers.DocumentHolder.txtImportWizard": "文字彙入精靈", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "增加參數大小", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "在後面插入參數", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "在前面插入參數", "SSE.Controllers.DocumentHolder.txtInsertBreak": "插入手動分頁", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "在之後插入方程式", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "在之前插入方程式", "SSE.Controllers.DocumentHolder.txtItems": "項目", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "僅保留文字", "SSE.Controllers.DocumentHolder.txtLess": "小於", "SSE.Controllers.DocumentHolder.txtLessEquals": "小於或等於", "SSE.Controllers.DocumentHolder.txtLimitChange": "變更限制位置", "SSE.Controllers.DocumentHolder.txtLimitOver": "文字限制", "SSE.Controllers.DocumentHolder.txtLimitUnder": "文字下的限制", "SSE.Controllers.DocumentHolder.txtLockSort": "在您的選取旁找到資料，但您沒有足夠的權限更改這些儲存格。&lt;br&gt;您是否要繼續使用目前的選取？", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "括號與其內容的高度對齊", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "矩陣對齊", "SSE.Controllers.DocumentHolder.txtNoChoices": "在填充單元格的選擇中沒有項目。<br>只能從列中選擇文字進行替換。", "SSE.Controllers.DocumentHolder.txtNotBegins": "不以...開頭", "SSE.Controllers.DocumentHolder.txtNotContains": "不含", "SSE.Controllers.DocumentHolder.txtNotEnds": "不以...結尾", "SSE.Controllers.DocumentHolder.txtNotEquals": "不等於", "SSE.Controllers.DocumentHolder.txtOr": "或", "SSE.Controllers.DocumentHolder.txtOverbar": "文字上方橫線", "SSE.Controllers.DocumentHolder.txtPaste": "貼上", "SSE.Controllers.DocumentHolder.txtPasteBorders": "沒有邊框的公式", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "公式+欄寬", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "目的地格式", "SSE.Controllers.DocumentHolder.txtPasteFormat": "僅貼上格式", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "公式+數字格式", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "僅粘貼公式", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "公式+所有格式", "SSE.Controllers.DocumentHolder.txtPasteLink": "貼上連結", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "鏈接圖片", "SSE.Controllers.DocumentHolder.txtPasteMerge": "合併條件格式", "SSE.Controllers.DocumentHolder.txtPastePicture": "圖片", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "來源格式設定", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "轉置", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "值+所有格式", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "值+數字格式", "SSE.Controllers.DocumentHolder.txtPasteValues": "僅粘貼值", "SSE.Controllers.DocumentHolder.txtPercent": "百分比", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "重做表格自動擴展", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "刪除分數欄", "SSE.Controllers.DocumentHolder.txtRemLimit": "移除限制", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "刪除強調字符", "SSE.Controllers.DocumentHolder.txtRemoveBar": "移除欄", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "您確定要移除此簽名嗎？&lt;br&gt;此操作無法復原。", "SSE.Controllers.DocumentHolder.txtRemScripts": "移除腳本", "SSE.Controllers.DocumentHolder.txtRemSubscript": "移除下標", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "移除上標", "SSE.Controllers.DocumentHolder.txtRowHeight": "列高", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "文字後的腳本", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "文字前的腳本", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "顯示底限", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "顯示結束括號", "SSE.Controllers.DocumentHolder.txtShowDegree": "顯示度量單位", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "顯示開始括號", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "顯示佔位符", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "顯示頂部限制", "SSE.Controllers.DocumentHolder.txtSorting": "排序", "SSE.Controllers.DocumentHolder.txtSortSelected": "排序選定的範圍", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "延伸括號", "SSE.Controllers.DocumentHolder.txtThisRowHint": "僅選擇指定列的此行", "SSE.Controllers.DocumentHolder.txtTop": "頂部", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "返回表格或指定表格欄的總計列", "SSE.Controllers.DocumentHolder.txtUnderbar": "文字下方橫線", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "撤消表自動擴展", "SSE.Controllers.DocumentHolder.txtUseTextImport": "使用文字匯入精靈", "SSE.Controllers.DocumentHolder.txtWarnUrl": "點擊此連結可能對您的設備和資料造成危害。&lt;br&gt;您確定要繼續嗎？", "SSE.Controllers.DocumentHolder.txtWidth": "寬度", "SSE.Controllers.DocumentHolder.warnFilterError": "為了應用值篩選，您需要在數值區域中至少有一個字段。", "SSE.Controllers.FormulaDialog.sCategoryAll": "全部", "SSE.Controllers.FormulaDialog.sCategoryCube": "立方體", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Custom", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "資料庫", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "日期和時間", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "工程學", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "金融", "SSE.Controllers.FormulaDialog.sCategoryInformation": "資訊", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 最後使用", "SSE.Controllers.FormulaDialog.sCategoryLogical": "邏輯", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "查找和引用", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "數學和三角學", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "統計", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "文字和數據", "SSE.Controllers.LeftMenu.newDocumentTitle": "無名試算表", "SSE.Controllers.LeftMenu.textByColumns": "按欄", "SSE.Controllers.LeftMenu.textByRows": "按列", "SSE.Controllers.LeftMenu.textFormulas": "公式", "SSE.Controllers.LeftMenu.textItemEntireCell": "整個儲存格內容", "SSE.Controllers.LeftMenu.textLoadHistory": "載入版本記錄中...", "SSE.Controllers.LeftMenu.textLookin": "查詢", "SSE.Controllers.LeftMenu.textNoTextFound": "找不到您正在搜尋的資料。請調整您的搜尋選項。", "SSE.Controllers.LeftMenu.textReplaceSkipped": "替換已完成。有 {0} 個項目被跳過。", "SSE.Controllers.LeftMenu.textReplaceSuccess": "已完成搜尋。已替換的次數：{0}。", "SSE.Controllers.LeftMenu.textSave": "儲存", "SSE.Controllers.LeftMenu.textSearch": "搜尋", "SSE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "SSE.Controllers.LeftMenu.textSheet": "工作表", "SSE.Controllers.LeftMenu.textValues": "值", "SSE.Controllers.LeftMenu.textWarning": "警告", "SSE.Controllers.LeftMenu.textWithin": "介於", "SSE.Controllers.LeftMenu.textWorkbook": "工作簿", "SSE.Controllers.LeftMenu.txtUntitled": "未命名", "SSE.Controllers.LeftMenu.warnDownloadAs": "如果您繼續以此格式保存，除文字外的所有功能將會丟失。確定要繼續嗎？", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "CSV格式不支持保存多工作表文件。&lt;br&gt;要保留所選格式並僅保存當前工作表，請按“保存”。&lt;br&gt;要保存當前電子表格，請單擊“取消”並以不同的格式保存。", "SSE.Controllers.Main.confirmAddCellWatches": "此操作將新增 {0} 個儲存格監視。您是否要繼續？", "SSE.Controllers.Main.confirmAddCellWatchesMax": "出於記憶體節省的原因，此操作僅會新增 {0} 個儲存格監視。您是否要繼續？", "SSE.Controllers.Main.confirmMaxChangesSize": "操作的大小超過了您的服務器設置的限制。<br>按一下「復原」取消上一個動作，或按「繼續」在本地保留動作（您需要下載文件或複製其內容以確保內容不會遺失）。", "SSE.Controllers.Main.confirmMoveCellRange": "目標單元格範圍可能包含數據。是否繼續操作？", "SSE.Controllers.Main.confirmPutMergeRange": "源數據包含合併的單元格。在將其粘貼到表格之前，這些單元格已被拆分。", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "標題列中的公式將被刪除並轉換為靜態文字。是否要繼續？", "SSE.Controllers.Main.confirmReplaceHFPicture": "Only one picture can be inserted in each section of the header.<br>Press \"Replace\" to replace existing picture.<br>Press \"Keep\" to keep existing picture.", "SSE.Controllers.Main.convertationTimeoutText": "轉換逾時", "SSE.Controllers.Main.criticalErrorExtText": "按下「確定」返回文件清單。", "SSE.Controllers.Main.criticalErrorTitle": "錯誤", "SSE.Controllers.Main.downloadErrorText": "下載失敗", "SSE.Controllers.Main.downloadTextText": "正在下載試算表...", "SSE.Controllers.Main.downloadTitleText": "正在下載試算表", "SSE.Controllers.Main.errNoDuplicates": "未找到重複的值。", "SSE.Controllers.Main.errorAccessDeny": "您正試圖執行您無權限的操作。<br>請聯繫您的文件伺服器管理員。", "SSE.Controllers.Main.errorArgsRange": "輸入公式錯誤。使用了不正確的參數範圍。", "SSE.Controllers.Main.errorAutoFilterChange": "該操作不允許在工作表上移動單元格，因為它嘗試移動表格中的單元格。", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "由於選定的單元格屬於表格的一部分，因此無法執行操作。&lt;br&gt;選擇另一個數據範圍，使整個表格移動，然後重試。", "SSE.Controllers.Main.errorAutoFilterDataRange": "由於選定的單元格範圍無法執行操作。&lt;br&gt;選擇一個與現有範圍不同的統一數據範圍，然後重試。", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "無法執行操作，因為該區域包含過濾的單元格。&lt;br&gt;請取消隱藏過濾元素，然後重試。", "SSE.Controllers.Main.errorBadImageUrl": "圖片網址不正確", "SSE.Controllers.Main.errorCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.Main.errorCannotPasteImg": "我們無法從剪貼簿貼上此圖片，但您可以將其儲存到您的裝置，然後再從那裡插入；或者您可以複製不帶文字的圖片並將其貼入試算表。", "SSE.Controllers.Main.errorCannotUngroup": "無法取消分組。要開始大綱，請選擇詳細信息行或列並將其分組。", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "您無法在受保護的工作表上使用此命令。要使用此命令，請取消保護工作表。&lt;br&gt;您可能需要輸入密碼。", "SSE.Controllers.Main.errorChangeArray": "您無法更改數組的一部分。", "SSE.Controllers.Main.errorChangeFilteredRange": "這將更改您工作表上的篩選範圍。請先移除自動篩選以完成此任務。", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "您嘗試更改的單元格或圖表位於受保護的工作表上。&lt;br&gt;要進行更改，請取消保護工作表。您可能需要輸入密碼。", "SSE.Controllers.Main.errorCircularReference": "There are one or more circular references where a formula refers to its own cell either directly or indirectly.<br>Try removing or changing these references, or moving the formulas to different cells.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "伺服器連線中斷，目前無法編輯文件。", "SSE.Controllers.Main.errorConnectToServer": "無法保存文件。請檢查連接設置或聯繫您的管理員。<br>單擊“確定”按鈕後，您將被提示下載文件。", "SSE.Controllers.Main.errorConvertXml": "文件具有不受支持的格式。&lt;br&gt;只能使用XML Spreadsheet 2003格式。", "SSE.Controllers.Main.errorCopyMultiselectArea": "此命令無法與多個選擇一起使用。請選擇單一範圍並重試。", "SSE.Controllers.Main.errorCountArg": "輸入公式錯誤。使用了不正確的參數數目。", "SSE.Controllers.Main.errorCountArgExceed": "輸入公式錯誤。參數數目超出限制。", "SSE.Controllers.Main.errorCreateDefName": "現有的命名範圍無法編輯，並且無法創建新的&lt;br&gt;因為其中一些正在進行編輯。", "SSE.Controllers.Main.errorCreateRange": "現有的範圍無法編輯，並且無法創建新的&lt;br&gt;因為其中一些正在進行編輯。", "SSE.Controllers.Main.errorDatabaseConnection": "外部錯誤。&lt;br&gt;資料庫連接錯誤。如錯誤持續發生，請聯繫支援人員。", "SSE.Controllers.Main.errorDataEncrypted": "已接收到加密的更改，無法解密。", "SSE.Controllers.Main.errorDataRange": "資料範圍不正確。", "SSE.Controllers.Main.errorDataValidate": "您輸入的值無效。&lt;br&gt;用戶對可在此單元格中輸入的值進行了限制。", "SSE.Controllers.Main.errorDefaultMessage": "錯誤碼：%1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "您正試圖刪除包含鎖定儲存格的欄位。在工作表受到保護的情況下，無法刪除鎖定的儲存格。&lt;br&gt;要刪除鎖定的儲存格，請取消保護工作表。您可能需要輸入密碼。", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "您正試圖刪除包含鎖定儲存格的列。在工作表受到保護的情況下，無法刪除鎖定的儲存格。&lt;br&gt;要刪除鎖定的儲存格，請取消保護工作表。您可能需要輸入密碼。", "SSE.Controllers.Main.errorDependentsNoFormulas": "The Trace Dependents command found no formulas that refer to the active cell.", "SSE.Controllers.Main.errorDirectUrl": "請驗證文件的連結。<br>此連結必須是直接下載文件的連結。", "SSE.Controllers.Main.errorEditingDownloadas": "在處理文件時發生錯誤。使用「另存為」選項將文件備份到驅動器中。", "SSE.Controllers.Main.errorEditingSaveas": "在處理文件時發生錯誤。使用「另存為」選項將文件備份到驅動器中。", "SSE.Controllers.Main.errorEditView": "現有的工作表視圖無法編輯，並且無法創建新的視圖，因為其中一些正在進行編輯。", "SSE.Controllers.Main.errorEmailClient": "找不到電子郵件客戶端。", "SSE.Controllers.Main.errorFilePassProtect": "該文件已被密碼保護，無法打開。", "SSE.Controllers.Main.errorFileRequest": "外部錯誤。&lt;br&gt;文件請求錯誤。如錯誤持續發生，請聯繫支援人員。", "SSE.Controllers.Main.errorFileSizeExceed": "文件大小超出了伺服器設置的限制。<br>詳細請聯繫管理員。", "SSE.Controllers.Main.errorFileVKey": "外部錯誤。&lt;br&gt;安全金鑰不正確。如錯誤持續發生，請聯繫支援人員。", "SSE.Controllers.Main.errorFillRange": "無法填滿所選的儲存格範圍。&lt;br&gt;所有合併的儲存格都需要相同的大小。", "SSE.Controllers.Main.errorForceSave": "儲存檔案時發生錯誤。請使用「另存為」選項將檔案儲存到磁碟上，或稍後再試。", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Cannot enter a formula for an item or field name in a pivot table report.", "SSE.Controllers.Main.errorFormulaName": "輸入公式錯誤。使用了不正確的公式名稱。", "SSE.Controllers.Main.errorFormulaParsing": "解析公式時發生內部錯誤。", "SSE.Controllers.Main.errorFrmlMaxLength": "公式的長度超過了8192個字符的限制。&lt;br&gt;請進行編輯後重試。", "SSE.Controllers.Main.errorFrmlMaxReference": "您無法輸入此公式，因為它包含太多值、儲存格引用和/或名稱。", "SSE.Controllers.Main.errorFrmlMaxTextLength": "在公式中的文本值限制為255個字符。&lt;br&gt;請使用CONCATENATE函數或連接運算符（&amp;）。", "SSE.Controllers.Main.errorFrmlWrongReferences": "函數引用不存在的工作表。&lt;br&gt;請檢查數據並重試。", "SSE.Controllers.Main.errorFTChangeTableRangeError": "無法完成所選單元格範圍的操作。&lt;br&gt;選擇一個範圍，使第一個表格行與當前行重疊&lt;br&gt;並且結果表格與當前表格重疊。", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "無法完成所選單元格範圍的操作。&lt;br&gt;請選擇一個不包含其他表格的範圍。", "SSE.Controllers.Main.errorInconsistentExt": "開啟檔案時發生錯誤。<br>檔案內容與副檔名不一致。", "SSE.Controllers.Main.errorInconsistentExtDocx": "開啟檔案時發生錯誤。<br>檔案內容對應到文字檔（例如 docx），但檔案副檔名不一致：%1。", "SSE.Controllers.Main.errorInconsistentExtPdf": "在打開檔案時發生錯誤。檔案內容對應以下格式之一：pdf/djvu/xps/oxps，但檔案的副檔名矛盾：%1。", "SSE.Controllers.Main.errorInconsistentExtPptx": "開啟檔案時發生錯誤。<br>檔案內容對應到簡報檔（例如 pptx），但檔案副檔名不一致：%1。", "SSE.Controllers.Main.errorInconsistentExtXlsx": "開啟檔案時發生錯誤。<br>檔案內容對應到試算表檔案（例如 xlsx），但檔案副檔名不一致：%1。", "SSE.Controllers.Main.errorInvalidRef": "輸入正確的選取名稱或有效的參照以進入。", "SSE.Controllers.Main.errorKeyEncrypt": "未知的按鍵快捷功能", "SSE.Controllers.Main.errorKeyExpire": "密鑰描述符已過期", "SSE.Controllers.Main.errorLabledColumnsPivot": "要建立樞紐分析表，請使用以標籤欄位為組織的清單資料。", "SSE.Controllers.Main.errorLoadingFont": "字型未載入。請聯絡您的文件伺服器管理員。", "SSE.Controllers.Main.errorLocationOrDataRangeError": "位置或數據範圍的引用無效。", "SSE.Controllers.Main.errorLockedAll": "由於其他用戶鎖定了工作表，因此無法執行操作。", "SSE.Controllers.Main.errorLockedCellGoalSeek": "One of the cells involved in the goal seek process has been modified by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "您無法更改樞紐分析表中的數據。", "SSE.Controllers.Main.errorLockedWorksheetRename": "目前無法對工作表進行重新命名，因為其正由其他用戶重新命名。", "SSE.Controllers.Main.errorMaxPoints": "每個圖表系列中的最大點數量為4096。", "SSE.Controllers.Main.errorMoveRange": "無法變更合併儲存格的一部分。", "SSE.Controllers.Main.errorMoveSlicerError": "表格切片器無法從一個活頁簿複製到另一個活頁簿。&lt;br&gt;請嘗試選擇整個表格和切片器再次嘗試。", "SSE.Controllers.Main.errorMultiCellFormula": "表格不允許使用多儲存格陣列公式。", "SSE.Controllers.Main.errorNoDataToParse": "未選取任何資料進行解析。", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.Main.errorOpenWarning": "其中一個公式超過了8192個字符的限制。&lt;br&gt;該公式已被刪除。", "SSE.Controllers.Main.errorOperandExpected": "輸入的函數語法不正確。請檢查是否缺少括號 - （或）", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "您輸入的密碼不正確。請確認大寫鎖定鍵已關閉，並確保使用正確的大小寫。", "SSE.Controllers.Main.errorPasteInPivot": "We can't make this change for the selected cells because it will affect a pivot table.<br>Use the field list to change the report.", "SSE.Controllers.Main.errorPasteMaxRange": "複製和粘貼區域不匹配。&lt;br&gt;請選擇大小相同的區域，或單擊行中的第一個單元格以粘貼複製的單元格。", "SSE.Controllers.Main.errorPasteMultiSelect": "無法在多個範圍選擇上執行此操作。請選擇單一範圍並重試。", "SSE.Controllers.Main.errorPasteSlicerError": "表格切片器無法從一個活頁簿複製到另一個活頁簿。", "SSE.Controllers.Main.errorPivotFieldNameExists": "Pivot table field name already exists.", "SSE.Controllers.Main.errorPivotGroup": "無法將該選取範圍分組。", "SSE.Controllers.Main.errorPivotOverlap": "樞紐分析表報告不得與表格重疊。", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "樞紐表報告已保存，但未包含底層數據。使用「刷新」按鈕更新報告。", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "The Trace Precedents command requires that the active cell contain a formula which includes a valid references.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "很抱歉，目前的程式版本一次無法列印超過 1500 頁。此限制將在未來版本中移除。", "SSE.Controllers.Main.errorProcessSaveResult": "保存失敗", "SSE.Controllers.Main.errorProtectedRange": "不允許編輯此範圍。", "SSE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "SSE.Controllers.Main.errorServerVersion": "編輯器版本已更新。將重新載入頁面以更新改動。", "SSE.Controllers.Main.errorSessionAbsolute": "文件編輯會話已過期。請重新加載頁面。", "SSE.Controllers.Main.errorSessionIdle": "此文件已經在編輯狀態有很長時間, 請重新載入此頁面。", "SSE.Controllers.Main.errorSessionToken": "與伺服器的連線已中斷。請重新載入頁面。", "SSE.Controllers.Main.errorSetPassword": "無法設定密碼。", "SSE.Controllers.Main.errorSingleColumnOrRowError": "位置參照無效，因為儲存格不都位於同一列或同一行。請選擇全部位於單個列或行中的儲存格。", "SSE.Controllers.Main.errorStockChart": "行順序不正確。建立股票圖表時，請按照以下順序在工作表上放置數據：開盤價、最高價、最低價、收盤價。", "SSE.Controllers.Main.errorToken": "文件安全令牌格式不正確。<br>請聯繫您的相關管理員。", "SSE.Controllers.Main.errorTokenExpire": "文件安全令牌已過期。<br> 請聯繫相關管理員。", "SSE.Controllers.Main.errorUnexpectedGuid": "外部錯誤。<br>意外的GUID。如果錯誤仍然存在，請聯繫支持。", "SSE.Controllers.Main.errorUpdateVersion": "文件版本已更改。將重新載入頁面。", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "連線已恢復，且檔案版本已更改。&lt;br&gt;在您繼續工作之前，您需要下載該檔案或複製其內容以確保不會遺失任何內容，然後重新載入此頁面。", "SSE.Controllers.Main.errorUserDrop": "目前無法存取該檔案。", "SSE.Controllers.Main.errorUsersExceed": "已超出價格方案允許的使用者數量。", "SSE.Controllers.Main.errorViewerDisconnect": "連線已中斷。您仍然可以檢視文件，&lt;br&gt;但在連線恢復並重新載入頁面之前，將無法下載或列印文件。", "SSE.Controllers.Main.errorWrongBracketsCount": "輸入公式錯誤。使用了錯誤的括號數目。", "SSE.Controllers.Main.errorWrongOperator": "輸入公式錯誤。使用了錯誤的運算子。請修正錯誤。", "SSE.Controllers.Main.errorWrongPassword": "您輸入的密碼不正確。", "SSE.Controllers.Main.errRemDuplicates": "找到並刪除重複的值：{0}，剩下的唯一值：{1}。", "SSE.Controllers.Main.leavePageText": "您在此試算表中有未保存的更改。點擊「停留在此頁面」然後「保存」以保存這些更改。點擊「離開此頁面」以放棄所有未保存的更改。", "SSE.Controllers.Main.leavePageTextOnClose": "在此試算表中的所有未儲存變更都將遺失。點擊「取消」然後「儲存」以儲存變更。點擊「確定」以捨棄所有未儲存變更。", "SSE.Controllers.Main.loadFontsTextText": "載入資料中...", "SSE.Controllers.Main.loadFontsTitleText": "載入資料", "SSE.Controllers.Main.loadFontTextText": "載入資料中...", "SSE.Controllers.Main.loadFontTitleText": "載入資料", "SSE.Controllers.Main.loadImagesTextText": "載入圖片中...", "SSE.Controllers.Main.loadImagesTitleText": "載入圖片中", "SSE.Controllers.Main.loadImageTextText": "載入圖片中...", "SSE.Controllers.Main.loadImageTitleText": "載入圖片中", "SSE.Controllers.Main.loadingDocumentTitleText": "正在載入試算表", "SSE.Controllers.Main.notcriticalErrorTitle": "警告", "SSE.Controllers.Main.openErrorText": "打開檔案時發生錯誤。", "SSE.Controllers.Main.openTextText": "正在打開試算表...", "SSE.Controllers.Main.openTitleText": "正在打開試算表", "SSE.Controllers.Main.pastInMergeAreaError": "無法變更合併儲存格的一部分。", "SSE.Controllers.Main.printTextText": "正在列印試算表...", "SSE.Controllers.Main.printTitleText": "正在列印試算表", "SSE.Controllers.Main.reloadButtonText": "重新載入頁面", "SSE.Controllers.Main.requestEditFailedMessageText": "有人正在編輯此文件。請稍後再試。", "SSE.Controllers.Main.requestEditFailedTitleText": "存取權被拒絕", "SSE.Controllers.Main.saveErrorText": "儲存檔案時發生錯誤", "SSE.Controllers.Main.saveErrorTextDesktop": "無法保存或創建此檔案。可能的原因包括：1. 該檔案為唯讀。2. 其他使用者正在編輯該檔案。3. 磁碟已滿或損壞。", "SSE.Controllers.Main.saveTextText": "正在保存試算表...", "SSE.Controllers.Main.saveTitleText": "正在保存試算表", "SSE.Controllers.Main.scriptLoadError": "連接速度太慢，某些組件無法加載。請重新加載頁面。", "SSE.Controllers.Main.textAnonymous": "匿名", "SSE.Controllers.Main.textApplyAll": "套用於所有方程式", "SSE.Controllers.Main.textBuyNow": "瀏覽網站", "SSE.Controllers.Main.textChangesSaved": "所有更動已儲存", "SSE.Controllers.Main.textClose": "關閉", "SSE.Controllers.Main.textCloseTip": "點擊以關閉提示", "SSE.Controllers.Main.textConfirm": "確認", "SSE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "SSE.Controllers.Main.textContactUs": "聯繫銷售部門", "SSE.Controllers.Main.textContinue": "繼續", "SSE.Controllers.Main.textConvertEquation": "此方程式是使用舊版方程式編輯器創建的，不再受支援。要進行編輯，請將方程式轉換為 Office Math ML 格式。是否現在轉換？", "SSE.Controllers.Main.textCustomLoader": "請注意，根據許可證的條款，您無權更改載入器。<br>請聯繫我們的銷售部門以獲取報價。", "SSE.Controllers.Main.textDisconnect": "連線已中斷", "SSE.Controllers.Main.textFillOtherRows": "填充其他行", "SSE.Controllers.Main.textFormulaFilledAllRows": "填充 {0} 行的公式已有資料。填充其他空行可能需要幾分鐘的時間。", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "只填充前 {0} 行的公式。填充其他空行可能需要幾分鐘的時間。", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "只填充前 {0} 行的公式已有資料，原因是節省記憶體。本工作表中還有其他 {1} 行已有資料。您可以手動填充它們。", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "只填充前 {0} 行的公式，原因是節省記憶體。本工作表中的其他行沒有資料。", "SSE.Controllers.Main.textGuest": "訪客", "SSE.Controllers.Main.textHasMacros": "此檔案包含自動巨集程式。<br>是否要執行這些巨集？", "SSE.Controllers.Main.textKeep": "Keep", "SSE.Controllers.Main.textLearnMore": "了解更多", "SSE.Controllers.Main.textLoadingDocument": "正在載入試算表", "SSE.Controllers.Main.textLongName": "請輸入少於128個字元的名稱。", "SSE.Controllers.Main.textNeedSynchronize": "您有更新", "SSE.Controllers.Main.textNo": "否", "SSE.Controllers.Main.textNoLicenseTitle": "已達到授權人數限制", "SSE.Controllers.Main.textPaidFeature": "付費功能", "SSE.Controllers.Main.textPleaseWait": "該操作可能需要比預期更長的時間。請等待...", "SSE.Controllers.Main.textReconnect": "連線已恢復", "SSE.Controllers.Main.textRemember": "記住我對所有檔案的選擇", "SSE.Controllers.Main.textRememberMacros": "記住我對所有巨集的選擇", "SSE.Controllers.Main.textRenameError": "使用者名稱不能為空", "SSE.Controllers.Main.textRenameLabel": "請輸入用於協作的名稱", "SSE.Controllers.Main.textReplace": "Replace", "SSE.Controllers.Main.textRequestMacros": "巨集對URL發出請求。您是否要允許對 %1 的請求？", "SSE.Controllers.Main.textShape": "形狀", "SSE.Controllers.Main.textStrict": "嚴格模式", "SSE.Controllers.Main.textText": "文字", "SSE.Controllers.Main.textTryQuickPrint": "您選擇了快速列印：整份文件將被列印到最後選擇的或預設的印表機上。&lt;br&gt;您要繼續嗎？", "SSE.Controllers.Main.textTryUndoRedo": "您的撤消/重做功能在快速共同編輯模式下被禁用。&lt;br&gt;點擊「嚴格模式」按鈕切換到嚴格共同編輯模式，在該模式下編輯文件時不會受到其他用戶的干擾，並且只有在保存後才會發送您的更改。您可以使用編輯器的高級設置在共同編輯模式之間切換。", "SSE.Controllers.Main.textTryUndoRedoWarn": "在快速共同編輯模式下，復原/重做功能被禁用。", "SSE.Controllers.Main.textUndo": "復原", "SSE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "SSE.Controllers.Main.textUpdating": "Updating", "SSE.Controllers.Main.textYes": "是", "SSE.Controllers.Main.titleLicenseExp": "授權證書已過期", "SSE.Controllers.Main.titleLicenseNotActive": "授權證書未啟用", "SSE.Controllers.Main.titleServerVersion": "編輯器已更新", "SSE.Controllers.Main.titleUpdateVersion": "Version changed", "SSE.Controllers.Main.txtAccent": "強調", "SSE.Controllers.Main.txtAll": "(全部)", "SSE.Controllers.Main.txtArt": "在這輸入文字", "SSE.Controllers.Main.txtBasicShapes": "基本圖案", "SSE.Controllers.Main.txtBlank": "（空白）", "SSE.Controllers.Main.txtButtons": "按鈕", "SSE.Controllers.Main.txtByField": "%1 的 %2", "SSE.Controllers.Main.txtCallouts": "圖說文字", "SSE.Controllers.Main.txtCharts": "流程圖", "SSE.Controllers.Main.txtClearFilter": "清除篩選", "SSE.Controllers.Main.txtColLbls": "欄標籤", "SSE.Controllers.Main.txtColumn": "欄", "SSE.Controllers.Main.txtConfidential": "機密", "SSE.Controllers.Main.txtDate": "日期", "SSE.Controllers.Main.txtDays": "天", "SSE.Controllers.Main.txtDiagramTitle": "圖表標題", "SSE.Controllers.Main.txtEditingMode": "設定編輯模式...", "SSE.Controllers.Main.txtErrorLoadHistory": "歷史載入失敗", "SSE.Controllers.Main.txtFiguredArrows": "箭號圖案", "SSE.Controllers.Main.txtFile": "檔案", "SSE.Controllers.Main.txtGrandTotal": "總計", "SSE.Controllers.Main.txtGroup": "分組", "SSE.Controllers.Main.txtHours": "小時", "SSE.Controllers.Main.txtInfo": "Info", "SSE.Controllers.Main.txtLines": "線數", "SSE.Controllers.Main.txtMath": "方程式圖案", "SSE.Controllers.Main.txtMinutes": "分鐘", "SSE.Controllers.Main.txtMonths": "月份", "SSE.Controllers.Main.txtMultiSelect": "多選", "SSE.Controllers.Main.txtNone": "None", "SSE.Controllers.Main.txtOr": "%1 或 %2", "SSE.Controllers.Main.txtPage": "頁面", "SSE.Controllers.Main.txtPageOf": "第％1頁，共％2頁", "SSE.Controllers.Main.txtPages": "頁數", "SSE.Controllers.Main.txtPicture": "Picture", "SSE.Controllers.Main.txtPivotTable": "PivotTable", "SSE.Controllers.Main.txtPreparedBy": "由...準備", "SSE.Controllers.Main.txtPrintArea": "列印區域", "SSE.Controllers.Main.txtQuarter": "季度", "SSE.Controllers.Main.txtQuarters": "季度", "SSE.Controllers.Main.txtRectangles": "長方形", "SSE.Controllers.Main.txtRow": "列", "SSE.Controllers.Main.txtRowLbls": "列標籤", "SSE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "SSE.Controllers.Main.txtScheme_Aspect": "Aspect", "SSE.Controllers.Main.txtScheme_Blue": "Blue", "SSE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "SSE.Controllers.Main.txtScheme_Blue_II": "Blue II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "SSE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "SSE.Controllers.Main.txtScheme_Green": "Green", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "SSE.Controllers.Main.txtScheme_Marquee": "Marquee", "SSE.Controllers.Main.txtScheme_Median": "Median", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Orange", "SSE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "SSE.Controllers.Main.txtScheme_Paper": "Paper", "SSE.Controllers.Main.txtScheme_Red": "Red", "SSE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "SSE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "SSE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "SSE.Controllers.Main.txtScheme_Violet": "Violet", "SSE.Controllers.Main.txtScheme_Violet_II": "Violet II", "SSE.Controllers.Main.txtScheme_Yellow": "Yellow", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "SSE.Controllers.Main.txtSeconds": "秒數", "SSE.Controllers.Main.txtSeries": "系列", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "線條標注 1（有邊框和重音線）", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "線條標注 2（有邊框和重音線）", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "線條標注 3（有邊框和重音線）", "SSE.Controllers.Main.txtShape_accentCallout1": "線條標注 1（帶重音線）", "SSE.Controllers.Main.txtShape_accentCallout2": "線條標注 2（帶重音線）", "SSE.Controllers.Main.txtShape_accentCallout3": "線條標注 3（帶重音線）", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "返回或上一步按鈕", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "開始按鈕", "SSE.Controllers.Main.txtShape_actionButtonBlank": "空白按鈕", "SSE.Controllers.Main.txtShape_actionButtonDocument": "文件按鈕", "SSE.Controllers.Main.txtShape_actionButtonEnd": "結束按鈕", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "前往下一步按鈕", "SSE.Controllers.Main.txtShape_actionButtonHelp": "說明按鈕", "SSE.Controllers.Main.txtShape_actionButtonHome": "首頁按鈕", "SSE.Controllers.Main.txtShape_actionButtonInformation": "資訊按鈕", "SSE.Controllers.Main.txtShape_actionButtonMovie": "電影按鈕", "SSE.Controllers.Main.txtShape_actionButtonReturn": "返回按鈕", "SSE.Controllers.Main.txtShape_actionButtonSound": "音效按鈕", "SSE.Controllers.Main.txtShape_arc": "弧", "SSE.Controllers.Main.txtShape_bentArrow": "彎曲箭頭", "SSE.Controllers.Main.txtShape_bentConnector5": "彎曲連接器", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "彎曲箭頭連接器", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "彎曲雙箭頭連接器", "SSE.Controllers.Main.txtShape_bentUpArrow": "向上彎曲箭頭", "SSE.Controllers.Main.txtShape_bevel": "立體斜角", "SSE.Controllers.Main.txtShape_blockArc": "區塊弧形", "SSE.Controllers.Main.txtShape_borderCallout1": "線條標注 1", "SSE.Controllers.Main.txtShape_borderCallout2": "線條標注 2", "SSE.Controllers.Main.txtShape_borderCallout3": "線條標注 3", "SSE.Controllers.Main.txtShape_bracePair": "雙大括號", "SSE.Controllers.Main.txtShape_callout1": "線路標註1（無邊框）", "SSE.Controllers.Main.txtShape_callout2": "線條標注 2（無邊框）", "SSE.Controllers.Main.txtShape_callout3": "線條標注 3（無邊框）", "SSE.Controllers.Main.txtShape_can": "罐狀", "SSE.Controllers.Main.txtShape_chevron": "箭頭", "SSE.Controllers.Main.txtShape_chord": "和弦", "SSE.Controllers.Main.txtShape_circularArrow": "圓形箭頭", "SSE.Controllers.Main.txtShape_cloud": "雲", "SSE.Controllers.Main.txtShape_cloudCallout": "雲形註解", "SSE.Controllers.Main.txtShape_corner": "角", "SSE.Controllers.Main.txtShape_cube": "立方體", "SSE.Controllers.Main.txtShape_curvedConnector3": "彎曲連接器", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "彎曲箭頭連接器", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "彎曲雙箭頭連接器", "SSE.Controllers.Main.txtShape_curvedDownArrow": "彎曲的向下箭頭", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "彎曲向左箭頭", "SSE.Controllers.Main.txtShape_curvedRightArrow": "彎曲向右箭頭", "SSE.Controllers.Main.txtShape_curvedUpArrow": "彎曲向上箭頭", "SSE.Controllers.Main.txtShape_decagon": "十邊形", "SSE.Controllers.Main.txtShape_diagStripe": "對角線條紋", "SSE.Controllers.Main.txtShape_diamond": "菱形", "SSE.Controllers.Main.txtShape_dodecagon": "十二邊形", "SSE.Controllers.Main.txtShape_donut": "圓環圖", "SSE.Controllers.Main.txtShape_doubleWave": "雙波浪線", "SSE.Controllers.Main.txtShape_downArrow": "向下箭頭", "SSE.Controllers.Main.txtShape_downArrowCallout": "下箭頭標註", "SSE.Controllers.Main.txtShape_ellipse": "橢圓", "SSE.Controllers.Main.txtShape_ellipseRibbon": "彎曲向下緞帶", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "彎曲向上緞帶", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "流程圖：交替處理", "SSE.Controllers.Main.txtShape_flowChartCollate": "流程圖：整理", "SSE.Controllers.Main.txtShape_flowChartConnector": "流程圖：連接器", "SSE.Controllers.Main.txtShape_flowChartDecision": "流程圖：決策", "SSE.Controllers.Main.txtShape_flowChartDelay": "流程圖：延遲", "SSE.Controllers.Main.txtShape_flowChartDisplay": "流程圖：顯示", "SSE.Controllers.Main.txtShape_flowChartDocument": "流程圖：文件", "SSE.Controllers.Main.txtShape_flowChartExtract": "流程圖：提取", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "流程圖：資料", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "流程圖：內部存儲", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "流程圖：磁碟", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "流程圖：直接存取儲存", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "流程圖：順序存取儲存", "SSE.Controllers.Main.txtShape_flowChartManualInput": "流程圖：手動輸入", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "流程圖：手動操作", "SSE.Controllers.Main.txtShape_flowChartMerge": "流程圖：合併", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "流程圖：多文件", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "流程圖：頁面連接器", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "流程圖：儲存的資料", "SSE.Controllers.Main.txtShape_flowChartOr": "流程圖：或", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "流程圖：預定義流程", "SSE.Controllers.Main.txtShape_flowChartPreparation": "流程圖：準備", "SSE.Controllers.Main.txtShape_flowChartProcess": "流程圖：流程", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "流程圖：卡片", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "流程圖：打孔紙帶", "SSE.Controllers.Main.txtShape_flowChartSort": "流程圖：排序", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "流程圖：加總連結點", "SSE.Controllers.Main.txtShape_flowChartTerminator": "流程圖：終止符號", "SSE.Controllers.Main.txtShape_foldedCorner": "摺疊角落", "SSE.Controllers.Main.txtShape_frame": "框線", "SSE.Controllers.Main.txtShape_halfFrame": "半框架", "SSE.Controllers.Main.txtShape_heart": "心形", "SSE.Controllers.Main.txtShape_heptagon": "七邊形", "SSE.Controllers.Main.txtShape_hexagon": "六邊形", "SSE.Controllers.Main.txtShape_homePlate": "五邊形", "SSE.Controllers.Main.txtShape_horizontalScroll": "水平捲動", "SSE.Controllers.Main.txtShape_irregularSeal1": "爆炸效果1", "SSE.Controllers.Main.txtShape_irregularSeal2": "爆炸效果2", "SSE.Controllers.Main.txtShape_leftArrow": "向左箭頭", "SSE.Controllers.Main.txtShape_leftArrowCallout": "向左箭頭標註", "SSE.Controllers.Main.txtShape_leftBrace": "左大括號", "SSE.Controllers.Main.txtShape_leftBracket": "左方括號", "SSE.Controllers.Main.txtShape_leftRightArrow": "左右箭頭", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "左右箭頭標註", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "左右上箭頭", "SSE.Controllers.Main.txtShape_leftUpArrow": "左上箭頭", "SSE.Controllers.Main.txtShape_lightningBolt": "閃電符號", "SSE.Controllers.Main.txtShape_line": "折線圖", "SSE.Controllers.Main.txtShape_lineWithArrow": "箭頭", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "雙向箭頭", "SSE.Controllers.Main.txtShape_mathDivide": "除法", "SSE.Controllers.Main.txtShape_mathEqual": "等於", "SSE.Controllers.Main.txtShape_mathMinus": "減號", "SSE.Controllers.Main.txtShape_mathMultiply": "乘", "SSE.Controllers.Main.txtShape_mathNotEqual": "不等於", "SSE.Controllers.Main.txtShape_mathPlus": "加號", "SSE.Controllers.Main.txtShape_moon": "月亮", "SSE.Controllers.Main.txtShape_noSmoking": "\"否\"符號", "SSE.Controllers.Main.txtShape_notchedRightArrow": "凹右箭頭", "SSE.Controllers.Main.txtShape_octagon": "八邊形", "SSE.Controllers.Main.txtShape_parallelogram": "平行四邊形", "SSE.Controllers.Main.txtShape_pentagon": "五邊形", "SSE.Controllers.Main.txtShape_pie": "圓餅圖", "SSE.Controllers.Main.txtShape_plaque": "簽名", "SSE.Controllers.Main.txtShape_plus": "加號", "SSE.Controllers.Main.txtShape_polyline1": "塗鴉", "SSE.Controllers.Main.txtShape_polyline2": "自由形式", "SSE.Controllers.Main.txtShape_quadArrow": "四向箭頭", "SSE.Controllers.Main.txtShape_quadArrowCallout": "四向箭頭圖說", "SSE.Controllers.Main.txtShape_rect": "長方形", "SSE.Controllers.Main.txtShape_ribbon": "向下緞帶", "SSE.Controllers.Main.txtShape_ribbon2": "上方功能區", "SSE.Controllers.Main.txtShape_rightArrow": "右箭頭", "SSE.Controllers.Main.txtShape_rightArrowCallout": "右箭頭註解", "SSE.Controllers.Main.txtShape_rightBrace": "右大括號", "SSE.Controllers.Main.txtShape_rightBracket": "右方括弧", "SSE.Controllers.Main.txtShape_round1Rect": "圓角單邊矩形", "SSE.Controllers.Main.txtShape_round2DiagRect": "圓斜角矩形", "SSE.Controllers.Main.txtShape_round2SameRect": "圓角同邊矩形", "SSE.Controllers.Main.txtShape_roundRect": "圓角矩形", "SSE.Controllers.Main.txtShape_rtTriangle": "直角三角形", "SSE.Controllers.Main.txtShape_smileyFace": "笑臉符號", "SSE.Controllers.Main.txtShape_snip1Rect": "剪下圓角單邊矩形", "SSE.Controllers.Main.txtShape_snip2DiagRect": "剪下圓斜角矩形", "SSE.Controllers.Main.txtShape_snip2SameRect": "剪下圓角同邊矩形", "SSE.Controllers.Main.txtShape_snipRoundRect": "剪下圓角單邊矩形", "SSE.Controllers.Main.txtShape_spline": "曲線", "SSE.Controllers.Main.txtShape_star10": "10 點星形", "SSE.Controllers.Main.txtShape_star12": "12 點星形", "SSE.Controllers.Main.txtShape_star16": "16 點星形", "SSE.Controllers.Main.txtShape_star24": "24 點星形", "SSE.Controllers.Main.txtShape_star32": "32 點星形", "SSE.Controllers.Main.txtShape_star4": "4 點星形", "SSE.Controllers.Main.txtShape_star5": "5 點星形", "SSE.Controllers.Main.txtShape_star6": "6 點星形", "SSE.Controllers.Main.txtShape_star7": "7 點星形", "SSE.Controllers.Main.txtShape_star8": "8 點星形", "SSE.Controllers.Main.txtShape_stripedRightArrow": "條紋右箭頭", "SSE.Controllers.Main.txtShape_sun": "太陽", "SSE.Controllers.Main.txtShape_teardrop": "淚滴形狀", "SSE.Controllers.Main.txtShape_textRect": "文字方塊", "SSE.Controllers.Main.txtShape_trapezoid": "梯形", "SSE.Controllers.Main.txtShape_triangle": "三角形", "SSE.Controllers.Main.txtShape_upArrow": "向上箭頭", "SSE.Controllers.Main.txtShape_upArrowCallout": "向上箭頭註解", "SSE.Controllers.Main.txtShape_upDownArrow": "上下箭頭", "SSE.Controllers.Main.txtShape_uturnArrow": "U 形箭頭", "SSE.Controllers.Main.txtShape_verticalScroll": "垂直捲動", "SSE.Controllers.Main.txtShape_wave": "波浪", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "橢圓形標註", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "矩形圖說", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "圓角矩形標註", "SSE.Controllers.Main.txtSheet": "工作表", "SSE.Controllers.Main.txtSlicer": "<PERSON>licer", "SSE.Controllers.Main.txtStarsRibbons": "星星和緞帶", "SSE.Controllers.Main.txtStyle_Bad": "差劣", "SSE.Controllers.Main.txtStyle_Calculation": "計算", "SSE.Controllers.Main.txtStyle_Check_Cell": "檢查儲存格", "SSE.Controllers.Main.txtStyle_Comma": "逗號", "SSE.Controllers.Main.txtStyle_Currency": "貨幣", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "解釋性文字", "SSE.Controllers.Main.txtStyle_Good": "良好", "SSE.Controllers.Main.txtStyle_Heading_1": "標題 1", "SSE.Controllers.Main.txtStyle_Heading_2": "標題 2", "SSE.Controllers.Main.txtStyle_Heading_3": "標題 3", "SSE.Controllers.Main.txtStyle_Heading_4": "標題 4", "SSE.Controllers.Main.txtStyle_Input": "輸入", "SSE.Controllers.Main.txtStyle_Linked_Cell": "鏈接儲存格", "SSE.Controllers.Main.txtStyle_Neutral": "中立", "SSE.Controllers.Main.txtStyle_Normal": "一般", "SSE.Controllers.Main.txtStyle_Note": "備註", "SSE.Controllers.Main.txtStyle_Output": "輸出", "SSE.Controllers.Main.txtStyle_Percent": "百分比", "SSE.Controllers.Main.txtStyle_Title": "標題", "SSE.Controllers.Main.txtStyle_Total": "總計", "SSE.Controllers.Main.txtStyle_Warning_Text": "警告文字", "SSE.Controllers.Main.txtTab": "標籤", "SSE.Controllers.Main.txtTable": "表格", "SSE.Controllers.Main.txtTime": "時間", "SSE.Controllers.Main.txtUnlock": "解鎖", "SSE.Controllers.Main.txtUnlockRange": "解除鎖定範圍", "SSE.Controllers.Main.txtUnlockRangeDescription": "輸入更改此範圍的密碼：", "SSE.Controllers.Main.txtUnlockRangeWarning": "您嘗試更改的範圍受到密碼保護。", "SSE.Controllers.Main.txtValues": "值", "SSE.Controllers.Main.txtView": "View", "SSE.Controllers.Main.txtXAxis": "X軸", "SSE.Controllers.Main.txtYAxis": "Y軸", "SSE.Controllers.Main.txtYears": "年份", "SSE.Controllers.Main.unknownErrorText": "未知錯誤。", "SSE.Controllers.Main.unsupportedBrowserErrorText": "不支援您的瀏覽器", "SSE.Controllers.Main.uploadDocExtMessage": "未知的文件格式。", "SSE.Controllers.Main.uploadDocFileCountMessage": "沒有上傳文件。", "SSE.Controllers.Main.uploadDocSizeMessage": "超出最大文件大小限制。", "SSE.Controllers.Main.uploadImageExtMessage": "未知圖片格式。", "SSE.Controllers.Main.uploadImageFileCountMessage": "沒有上傳圖片。", "SSE.Controllers.Main.uploadImageSizeMessage": "圖片超出最大大小限制。最大大小為25MB。", "SSE.Controllers.Main.uploadImageTextText": "正在上傳圖片...", "SSE.Controllers.Main.uploadImageTitleText": "正在上傳圖片", "SSE.Controllers.Main.waitText": "請稍候...", "SSE.Controllers.Main.warnBrowserIE9": "該應用程式在IE9上的功能有限。請使用IE10或更高版本", "SSE.Controllers.Main.warnBrowserZoom": "您的瀏覽器目前的縮放設定不完全支援。請按Ctrl+0重設為預設縮放。", "SSE.Controllers.Main.warnLicenseAnonymous": "拒絕匿名使用者存取。此文件只能以檢視模式開啟。", "SSE.Controllers.Main.warnLicenseBefore": "授權證書未啟用。請聯繫您的管理員。", "SSE.Controllers.Main.warnLicenseExceeded": "您已達到同時連接 %1 編輯器的限制。此文件將僅以檢視模式開啟。&lt;br&gt;請聯繫您的管理員以了解詳情。", "SSE.Controllers.Main.warnLicenseExp": "您的授權證已過期.<br>請更新您的授權證並重新整理頁面。", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "許可證已過期。您無法訪問文件編輯功能。請聯繫您的管理員。", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "許可證需要更新。您只能有限制的訪問文件編輯功能。請聯繫您的管理員以獲得完全訪問權限。", "SSE.Controllers.Main.warnLicenseUsersExceeded": "您已達到 %1 編輯器的使用者限制。請聯繫您的管理員以了解詳情。", "SSE.Controllers.Main.warnNoLicense": "您已達到同時連接 %1 編輯器的限制。此文件將僅以檢視模式開啟。&lt;br&gt;請聯繫 %1 的銷售團隊了解個人升級條款。", "SSE.Controllers.Main.warnNoLicenseUsers": "您已達到 %1 編輯器的使用者限制。請聯繫 %1 的銷售團隊了解個人升級條款。", "SSE.Controllers.Main.warnProcessRightsChange": "您被拒絕編輯該文件的權限。", "SSE.Controllers.PivotTable.strSheet": "工作表", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "No actions with calculated items are allowed for this active cell.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Calculated items do not work with custom subtotals.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "An item name cannot be found. Check that you've typed name correctly and the item is present in the PivotTable report.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Averages, standard deviations, and variances are not supported when a PivotTable report has calculated items.", "SSE.Controllers.Print.strAllSheets": "所有工作表", "SSE.Controllers.Print.textFirstCol": "第一欄", "SSE.Controllers.Print.textFirstRow": "第一列", "SSE.Controllers.Print.textFrozenCols": "凍結的欄", "SSE.Controllers.Print.textFrozenRows": "凍結的列", "SSE.Controllers.Print.textInvalidRange": "錯誤！無效的儲存格範圍", "SSE.Controllers.Print.textNoRepeat": "不要重複", "SSE.Controllers.Print.textRepeat": "重複...", "SSE.Controllers.Print.textSelectRange": "選擇範圍", "SSE.Controllers.Print.txtCustom": "自訂", "SSE.Controllers.Print.txtZoomToPage": "Zoom to page", "SSE.Controllers.Search.textInvalidRange": "錯誤！無效的儲存格範圍", "SSE.Controllers.Search.textNoTextFound": "找不到您正在搜尋的資料。請調整您的搜尋選項。", "SSE.Controllers.Search.textReplaceSkipped": "替換已完成。有 {0} 個項目被跳過。", "SSE.Controllers.Search.textReplaceSuccess": "搜尋完成。 {0}個符合結果已被取代", "SSE.Controllers.Statusbar.errorLastSheet": "工作簿必須至少具有一個可見的工作表。", "SSE.Controllers.Statusbar.errorRemoveSheet": "無法刪除工作表。", "SSE.Controllers.Statusbar.strSheet": "工作表", "SSE.Controllers.Statusbar.textDisconnect": "&lt;b&gt;連線已斷開&lt;/b&gt;&lt;br&gt;正在嘗試連線。請檢查連線設定。", "SSE.Controllers.Statusbar.textSheetViewTip": "您正在使用工作表檢視模式。篩選和排序僅對您和仍然處於此檢視中的人可見。", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "您正在使用工作表檢視模式。篩選僅對您和仍然處於此檢視中的人可見。", "SSE.Controllers.Statusbar.warnDeleteSheet": "所選工作表可能包含數據。確定要繼續嗎？", "SSE.Controllers.Statusbar.zoomText": "縮放至{0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "要保存的字體在當前設備上不可用。&lt;br&gt;文本樣式將使用系統字體之一顯示，當可用時將使用保存的字體。&lt;br&gt;您要繼續嗎？", "SSE.Controllers.Toolbar.errorComboSeries": "要建立組合圖表，請選擇至少兩個資料系列。", "SSE.Controllers.Toolbar.errorMaxPoints": "每個圖表系列中的最大點數量為4096。", "SSE.Controllers.Toolbar.errorMaxRows": "錯誤！每個圖表的數據系列數量上限為255", "SSE.Controllers.Toolbar.errorStockChart": "行順序不正確。建立股票圖表時，請按照以下順序在工作表上放置數據：開盤價、最高價、最低價、收盤價。", "SSE.Controllers.Toolbar.helpCalcItems": "Work with calculated items in Pivot Tables.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Calculated items", "SSE.Controllers.Toolbar.helpFastUndo": "Easily undo changes while collaborating on sheets in Fast mode.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "\"Undo\" in real-time co-editing", "SSE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "SSE.Controllers.Toolbar.textAccent": "重音符號", "SSE.Controllers.Toolbar.textBracket": "括號", "SSE.Controllers.Toolbar.textDirectional": "方向性的", "SSE.Controllers.Toolbar.textFontSizeErr": "輸入的值不正確。&lt;br&gt;請輸入介於1和409之間的數值。", "SSE.Controllers.Toolbar.textFraction": "分數", "SSE.Controllers.Toolbar.textFunction": "函數", "SSE.Controllers.Toolbar.textIndicator": "指標", "SSE.Controllers.Toolbar.textInsert": "插入", "SSE.Controllers.Toolbar.textIntegral": "積分符號", "SSE.Controllers.Toolbar.textLargeOperator": "大型運營商", "SSE.Controllers.Toolbar.textLimitAndLog": "極限和對數", "SSE.Controllers.Toolbar.textLongOperation": "運行時間長", "SSE.Controllers.Toolbar.textMatrix": "矩陣", "SSE.Controllers.Toolbar.textOperator": "運算符", "SSE.Controllers.Toolbar.textPivot": "樞紐分析表", "SSE.Controllers.Toolbar.textRadical": "根式", "SSE.Controllers.Toolbar.textRating": "評分", "SSE.Controllers.Toolbar.textRecentlyUsed": "最近使用", "SSE.Controllers.Toolbar.textScript": "腳本", "SSE.Controllers.Toolbar.textShapes": "形狀", "SSE.Controllers.Toolbar.textSymbols": "符號", "SSE.Controllers.Toolbar.textWarning": "警告", "SSE.Controllers.Toolbar.txtAccent_Accent": "尖音符", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "上方左右箭頭", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "上方左箭頭", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "上方向右箭頭", "SSE.Controllers.Toolbar.txtAccent_Bar": "長條圖", "SSE.Controllers.Toolbar.txtAccent_BarBot": "底線", "SSE.Controllers.Toolbar.txtAccent_BarTop": "橫槓", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "有方框的公式（包含佔位符）", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "帶框公式（範例）", "SSE.Controllers.Toolbar.txtAccent_Check": "勾選", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "底括號", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "大括號", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "向量A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "帶有長槓的ABC", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y帶有上橫線", "SSE.Controllers.Toolbar.txtAccent_DDDot": "三點", "SSE.Controllers.Toolbar.txtAccent_DDot": "雙點", "SSE.Controllers.Toolbar.txtAccent_Dot": "點", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "雙上橫槓", "SSE.Controllers.Toolbar.txtAccent_Grave": "重音符號", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "下方分組字元", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "上方分組字元", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "上方左矢", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "上方右箭頭", "SSE.Controllers.Toolbar.txtAccent_Hat": "帽子", "SSE.Controllers.Toolbar.txtAccent_Smile": "短音符", "SSE.Controllers.Toolbar.txtAccent_Tilde": "波浪號", "SSE.Controllers.Toolbar.txtBracket_Angle": "角括號", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "帶分隔符的角括號", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "帶兩個分隔符的角括號", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "右尖括號", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "左尖括號", "SSE.Controllers.Toolbar.txtBracket_Curve": "大括號", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "帶分隔符的大括號", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "右大括號", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "左大括號", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "情況（兩個條件）", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "情況（三個條件）", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "堆疊物件", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "括號內的堆疊物件", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "情況範例", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "二項式係數", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "帶有角括號的二項式係數", "SSE.Controllers.Toolbar.txtBracket_Line": "豎線", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "右豎線", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "左豎線", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "雙豎線", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "右雙豎線", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "左雙豎線", "SSE.Controllers.Toolbar.txtBracket_LowLim": "底部整數", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "右地板", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "左地板", "SSE.Controllers.Toolbar.txtBracket_Round": "括號", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "帶分隔符的括號", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "右括號", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "左括號", "SSE.Controllers.Toolbar.txtBracket_Square": "方括號", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "兩個右方括號之間的佔位符", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "反向方括號", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "右方括號", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "左方括號", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "兩個左方括號之間的佔位符", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "雙方括號", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "右雙方括號", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "左雙方括號", "SSE.Controllers.Toolbar.txtBracket_UppLim": "天花板", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "右天花板", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "左天花板", "SSE.Controllers.Toolbar.txtDeleteCells": "刪除儲存格", "SSE.Controllers.Toolbar.txtExpand": "展開並排序", "SSE.Controllers.Toolbar.txtExpandSort": "選擇範圍旁邊的數據將不會被排序。您要擴展選擇範圍以包括相鄰數據還是僅繼續對當前選定的單元格進行排序？", "SSE.Controllers.Toolbar.txtFractionDiagonal": "斜向分數", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "dx 除以 dy", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Δy 除以 Δx 的大寫Δ", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "偏微分 y 對偏微分 x", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Δx 除以 Δy", "SSE.Controllers.Toolbar.txtFractionHorizontal": "線性分數", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi除以2", "SSE.Controllers.Toolbar.txtFractionSmall": "小分數", "SSE.Controllers.Toolbar.txtFractionVertical": "堆積分數", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "反餘弦函數", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "雙曲反餘弦函數", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "反餘切函數", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "雙曲反餘切函數", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "反餘割函數", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "雙曲反餘割函數", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "反正割函數", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "雙曲反正割函數", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "反正弦函數", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "雙曲反正弦函數", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "反正切函數", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "雙曲反正切函數", "SSE.Controllers.Toolbar.txtFunction_Cos": "餘弦函數", "SSE.Controllers.Toolbar.txtFunction_Cosh": "雙曲餘弦函數", "SSE.Controllers.Toolbar.txtFunction_Cot": "餘切函數", "SSE.Controllers.Toolbar.txtFunction_Coth": "雙曲餘切函數", "SSE.Controllers.Toolbar.txtFunction_Csc": "余割函數", "SSE.Controllers.Toolbar.txtFunction_Csch": "雙曲餘割函數", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "正弦θ", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "cos2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "正切公式", "SSE.Controllers.Toolbar.txtFunction_Sec": "正割函數", "SSE.Controllers.Toolbar.txtFunction_Sech": "雙曲正割函數", "SSE.Controllers.Toolbar.txtFunction_Sin": "正弦函數", "SSE.Controllers.Toolbar.txtFunction_Sinh": "雙曲正弦函數", "SSE.Controllers.Toolbar.txtFunction_Tan": "正切函數", "SSE.Controllers.Toolbar.txtFunction_Tanh": "雙曲正切函數", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "自訂", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "資料和模式", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "好、壞和中立", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "沒有名稱", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "數字格式", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "主題儲存格樣式", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "標題和標題行", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "自訂", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "深色", "SSE.Controllers.Toolbar.txtGroupTable_Light": "淺色", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "中等", "SSE.Controllers.Toolbar.txtInsertCells": "插入儲存格", "SSE.Controllers.Toolbar.txtIntegral": "積分", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "微分θ", "SSE.Controllers.Toolbar.txtIntegral_dx": "微分 x", "SSE.Controllers.Toolbar.txtIntegral_dy": "微分 y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "帶堆疊限制的積分", "SSE.Controllers.Toolbar.txtIntegralDouble": "雙重積分", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "帶堆疊限制的雙重積分", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "帶限制的雙重積分", "SSE.Controllers.Toolbar.txtIntegralOriented": "等高線積分", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "帶堆疊限制的等高積分", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "曲面積分", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "帶堆疊限制的曲面積分", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "帶限制的曲面積分", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "帶限制的等高積分", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "體積積分", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "帶堆疊限制的體積積分", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "帶限制的體積積分", "SSE.Controllers.Toolbar.txtIntegralSubSup": "帶限制的積分", "SSE.Controllers.Toolbar.txtIntegralTriple": "三重積分", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "帶堆疊限制的三重積分", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "帶限制的三重積分", "SSE.Controllers.Toolbar.txtInvalidRange": "錯誤！無效的儲存格範圍", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "邏輯且", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "帶下限的邏輯且", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "帶限制的邏輯且", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "帶下標下限的邏輯且", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "帶上下標限制的邏輯且", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "共積", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "帶下限的共積", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "帶限制的共積", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "帶有下標下限的共積", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "帶有上下標限制的共積", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "k 等於從 0 到 n 的選取 n 個的總和", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "從 i 等於零到 n 的總和", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "總和範例使用兩個索引", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "乘積範例", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "聯集範例", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "邏輯或", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "帶下限的邏輯或", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "帶限制的邏輯或", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "帶下標下限的邏輯或", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "帶上下標限制的邏輯或", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "交集", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "帶下標下限的交集", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "帶限制的交集", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "帶下標下限的交集", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "帶上下標限制的交集", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "乘積", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "帶下限的乘積", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "帶限制的乘積", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "帶下標下限的乘積", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "帶上下標限制的乘積", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "總和", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "帶下限的總和", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "帶限制的總和", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "帶下標下限的總和", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "帶上下標限制的總和", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "聯集", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "帶下限的聯集", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "帶限制的聯集", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "帶下標下限的聯集", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "帶上下標限制的聯集", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "限制例子", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "最大範例", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "限制", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "自然對數", "SSE.Controllers.Toolbar.txtLimitLog_Log": "對數", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "對數", "SSE.Controllers.Toolbar.txtLimitLog_Max": "最大值", "SSE.Controllers.Toolbar.txtLimitLog_Min": "最小值", "SSE.Controllers.Toolbar.txtLockSort": "在您的選取旁找到資料，但您沒有足夠的權限更改這些儲存格。&lt;br&gt;您是否要繼續使用目前的選取？", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "以雙豎線表示的空的 2x2 矩陣", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "空的 2x2 行列式", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "以括弧表示的空的 2x2 矩陣", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "以括號表示的空的 2x2 矩陣", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "基線點", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "中線點", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "對角點", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "垂直點", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "以括弧表示的稀疏矩陣", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "以括號表示的稀疏矩陣", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 除了對角線以外都是零的單位矩陣", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "2x2 除了對角線以外都是空白的單位矩陣", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 除了對角線以外都是零的單位矩陣", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 除了對角線以外都是空白的單位矩陣", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "下方左右箭頭", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "上方左右箭頭", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "下方左箭頭", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "上方左箭頭", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "下方向右箭頭", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "上方向右箭頭", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "冒號等號", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "產生", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta 收益", "SSE.Controllers.Toolbar.txtOperator_Definition": "按照定義相等", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta 等於", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "上下左右的雙箭頭(下方)", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "上下左右的雙箭頭(上方)", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "下方左箭頭", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "上方左箭頭", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "下方向右箭頭", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "上方向右箭頭", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "等於 等於", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "負等於", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "加等於", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "由...測量", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "二次方程式的右邊", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "平方根的平方和", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "帶次數的平方根", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "立方根", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "帶有次數的根號符號", "SSE.Controllers.Toolbar.txtRadicalSqrt": "平方根", "SSE.Controllers.Toolbar.txtScriptCustom_1": "x 的下標 y 的平方", "SSE.Controllers.Toolbar.txtScriptCustom_2": "e 的負 i omega t 次方", "SSE.Controllers.Toolbar.txtScriptCustom_3": "x 的平方", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Y 的左上標 n 的左下標 1", "SSE.Controllers.Toolbar.txtScriptSub": "下標", "SSE.Controllers.Toolbar.txtScriptSubSup": "下標-上標", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "左下標-上標", "SSE.Controllers.Toolbar.txtScriptSup": "上標", "SSE.Controllers.Toolbar.txtSorting": "排序", "SSE.Controllers.Toolbar.txtSortSelected": "排序選定的範圍", "SSE.Controllers.Toolbar.txtSymbol_about": "大約", "SSE.Controllers.Toolbar.txtSymbol_additional": "補集", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_approx": "幾乎等於", "SSE.Controllers.Toolbar.txtSymbol_ast": "星號運算子", "SSE.Controllers.Toolbar.txtSymbol_beta": "測試版", "SSE.Controllers.Toolbar.txtSymbol_beth": "賭注", "SSE.Controllers.Toolbar.txtSymbol_bullet": "點運算子", "SSE.Controllers.Toolbar.txtSymbol_cap": "交集", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "立方根", "SSE.Controllers.Toolbar.txtSymbol_cdots": "中線水平省略號", "SSE.Controllers.Toolbar.txtSymbol_celsius": "攝氏度", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "大約等於", "SSE.Controllers.Toolbar.txtSymbol_cup": "聯集", "SSE.Controllers.Toolbar.txtSymbol_ddots": "向右下對角省略號", "SSE.Controllers.Toolbar.txtSymbol_degree": "度", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta 等於", "SSE.Controllers.Toolbar.txtSymbol_div": "除號", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "向下箭頭", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "空集合", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "等於", "SSE.Controllers.Toolbar.txtSymbol_equiv": "相同於", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "存在", "SSE.Controllers.Toolbar.txtSymbol_factorial": "階乘", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "華氏度", "SSE.Controllers.Toolbar.txtSymbol_forall": "適用於全部", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "大於或等於", "SSE.Controllers.Toolbar.txtSymbol_gg": "遠大於", "SSE.Controllers.Toolbar.txtSymbol_greater": "大於", "SSE.Controllers.Toolbar.txtSymbol_in": "元素", "SSE.Controllers.Toolbar.txtSymbol_inc": "增量", "SSE.Controllers.Toolbar.txtSymbol_infinity": "無窮大", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "向左箭頭", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "左右箭頭", "SSE.Controllers.Toolbar.txtSymbol_leq": "小於或等於", "SSE.Controllers.Toolbar.txtSymbol_less": "小於", "SSE.Controllers.Toolbar.txtSymbol_ll": "遠小於", "SSE.Controllers.Toolbar.txtSymbol_minus": "減號", "SSE.Controllers.Toolbar.txtSymbol_mp": "減加號", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "不等於", "SSE.Controllers.Toolbar.txtSymbol_ni": "包含為成員", "SSE.Controllers.Toolbar.txtSymbol_not": "沒有簽名", "SSE.Controllers.Toolbar.txtSymbol_notexists": "不存在", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Ο (歐米克戎)", "SSE.Controllers.Toolbar.txtSymbol_omega": "Ω (歐米茄)", "SSE.Controllers.Toolbar.txtSymbol_partial": "偏微分", "SSE.Controllers.Toolbar.txtSymbol_percent": "百分比", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "加號", "SSE.Controllers.Toolbar.txtSymbol_pm": "加減號", "SSE.Controllers.Toolbar.txtSymbol_propto": "比例縮放為", "SSE.Controllers.Toolbar.txtSymbol_psi": "Ψ（希臘字母普西）", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "四次根", "SSE.Controllers.Toolbar.txtSymbol_qed": "證明結束", "SSE.Controllers.Toolbar.txtSymbol_rddots": "右上對角省略符號", "SSE.Controllers.Toolbar.txtSymbol_rho": "希臘字母\"ρ\"", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "右箭頭", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "根號符號", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "因此", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "乘法符號", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "向上箭頭", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon 變體", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Phi 變體", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Pi變體", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Rho變體", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma 變體", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Θ 變體", "SSE.Controllers.Toolbar.txtSymbol_vdots": "垂直省略號", "SSE.Controllers.Toolbar.txtSymbol_xsi": "希臘字母\"Ξ\"", "SSE.Controllers.Toolbar.txtSymbol_zeta": "ζ", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "深色表格樣式", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "亮色表格樣式", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "中等表格樣式", "SSE.Controllers.Toolbar.warnLongOperation": "您即將執行的操作可能需要較長時間才能完成。確定要繼續嗎？", "SSE.Controllers.Toolbar.warnMergeLostData": "僅保留合併單元格中左上角單元格的數據。&lt;br&gt;您確定要繼續嗎？", "SSE.Controllers.Toolbar.warnNoRecommended": "To create a chart, select the cells that contain the data you'd like to use.<br>If you have names for the rows and columns and you'd like use them as labels, include them in your selection.", "SSE.Controllers.Viewport.textFreezePanes": "凍結窗格", "SSE.Controllers.Viewport.textFreezePanesShadow": "顯示固定窗格陰影", "SSE.Controllers.Viewport.textHideFBar": "隱藏公式列", "SSE.Controllers.Viewport.textHideGridlines": "隱藏網格線", "SSE.Controllers.Viewport.textHideHeadings": "隱藏標題", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "小數點分隔符", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "千位數分隔符", "SSE.Views.AdvancedSeparatorDialog.textLabel": "用於識別數字數據的設置", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "文件定界符", "SSE.Views.AdvancedSeparatorDialog.textTitle": "進階設定", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(無)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "自訂過濾器", "SSE.Views.AutoFilterDialog.textAddSelection": "將目前選取範圍新增至篩選器", "SSE.Views.AutoFilterDialog.textEmptyItem": "{空白}", "SSE.Views.AutoFilterDialog.textSelectAll": "全選", "SSE.Views.AutoFilterDialog.textSelectAllResults": "選擇所有搜索結果", "SSE.Views.AutoFilterDialog.textWarning": "警告", "SSE.Views.AutoFilterDialog.txtAboveAve": "高於平均值", "SSE.Views.AutoFilterDialog.txtAfter": "After...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "All dates in the period", "SSE.Views.AutoFilterDialog.txtApril": "April", "SSE.Views.AutoFilterDialog.txtAugust": "August", "SSE.Views.AutoFilterDialog.txtBefore": "Before...", "SSE.Views.AutoFilterDialog.txtBegins": "開頭為...", "SSE.Views.AutoFilterDialog.txtBelowAve": "低於平均值", "SSE.Views.AutoFilterDialog.txtBetween": "介於...", "SSE.Views.AutoFilterDialog.txtClear": "清除", "SSE.Views.AutoFilterDialog.txtContains": "包含...", "SSE.Views.AutoFilterDialog.txtDateFilter": "Date filter", "SSE.Views.AutoFilterDialog.txtDecember": "December", "SSE.Views.AutoFilterDialog.txtEmpty": "輸入儲存格篩選", "SSE.Views.AutoFilterDialog.txtEnds": "結尾為...", "SSE.Views.AutoFilterDialog.txtEquals": "等於...", "SSE.Views.AutoFilterDialog.txtFebruary": "February", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "按儲存格顏色篩選", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "按字型顏色篩選", "SSE.Views.AutoFilterDialog.txtGreater": "大於...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "大於或等於...", "SSE.Views.AutoFilterDialog.txtJanuary": "January", "SSE.Views.AutoFilterDialog.txtJuly": "July", "SSE.Views.AutoFilterDialog.txtJune": "June", "SSE.Views.AutoFilterDialog.txtLabelFilter": "標籤篩選", "SSE.Views.AutoFilterDialog.txtLastMonth": "Last month", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Last quarter", "SSE.Views.AutoFilterDialog.txtLastWeek": "Last week", "SSE.Views.AutoFilterDialog.txtLastYear": "Last year", "SSE.Views.AutoFilterDialog.txtLess": "小於...", "SSE.Views.AutoFilterDialog.txtLessEquals": "小於或等於...", "SSE.Views.AutoFilterDialog.txtMarch": "March", "SSE.Views.AutoFilterDialog.txtMay": "May", "SSE.Views.AutoFilterDialog.txtNextMonth": "Next month", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Next quarter", "SSE.Views.AutoFilterDialog.txtNextWeek": "Next week", "SSE.Views.AutoFilterDialog.txtNextYear": "Next year", "SSE.Views.AutoFilterDialog.txtNotBegins": "不以...開頭", "SSE.Views.AutoFilterDialog.txtNotBetween": "不在之間...", "SSE.Views.AutoFilterDialog.txtNotContains": "不含...", "SSE.Views.AutoFilterDialog.txtNotEnds": "不以...結尾", "SSE.Views.AutoFilterDialog.txtNotEquals": "不等於...", "SSE.Views.AutoFilterDialog.txtNovember": "November", "SSE.Views.AutoFilterDialog.txtNumFilter": "數字篩選", "SSE.Views.AutoFilterDialog.txtOctober": "October", "SSE.Views.AutoFilterDialog.txtQuarter1": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Quarter 1", "SSE.Views.AutoFilterDialog.txtReapply": "重新應用", "SSE.Views.AutoFilterDialog.txtSeptember": "September", "SSE.Views.AutoFilterDialog.txtSortCellColor": "按儲存格顏色排序", "SSE.Views.AutoFilterDialog.txtSortFontColor": "按字體顏色排序", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "從高到低排序", "SSE.Views.AutoFilterDialog.txtSortLow2High": "從最低到最高排序", "SSE.Views.AutoFilterDialog.txtSortOption": "更多排序選項...", "SSE.Views.AutoFilterDialog.txtTextFilter": "文字過濾器", "SSE.Views.AutoFilterDialog.txtThisMonth": "This Month", "SSE.Views.AutoFilterDialog.txtThisQuarter": "This quarter", "SSE.Views.AutoFilterDialog.txtThisWeek": "This week", "SSE.Views.AutoFilterDialog.txtThisYear": "This Year", "SSE.Views.AutoFilterDialog.txtTitle": "篩選", "SSE.Views.AutoFilterDialog.txtToday": "Today", "SSE.Views.AutoFilterDialog.txtTomorrow": "Tomorrow", "SSE.Views.AutoFilterDialog.txtTop10": "前10名", "SSE.Views.AutoFilterDialog.txtValueFilter": "數值篩選", "SSE.Views.AutoFilterDialog.txtYearToDate": "Year to date", "SSE.Views.AutoFilterDialog.txtYesterday": "Yesterday", "SSE.Views.AutoFilterDialog.warnFilterError": "為了應用值篩選，您需要在數值區域中至少有一個字段。", "SSE.Views.AutoFilterDialog.warnNoSelected": "您必須至少選擇一個值", "SSE.Views.CellEditor.textManager": "名稱管理員", "SSE.Views.CellEditor.tipFormula": "插入函數", "SSE.Views.CellRangeDialog.errorMaxRows": "錯誤！每個圖表的數據系列數量上限為255", "SSE.Views.CellRangeDialog.errorStockChart": "行順序不正確。建立股票圖表時，請按照以下順序在工作表上放置數據：開盤價、最高價、最低價、收盤價。", "SSE.Views.CellRangeDialog.txtEmpty": "此欄位為必填欄位", "SSE.Views.CellRangeDialog.txtInvalidRange": "錯誤！無效的儲存格範圍", "SSE.Views.CellRangeDialog.txtTitle": "選取資料範圍", "SSE.Views.CellSettings.strShrink": "縮小以適合", "SSE.Views.CellSettings.strWrap": "換行文字", "SSE.Views.CellSettings.textAngle": "角度", "SSE.Views.CellSettings.textBackColor": "背景顏色", "SSE.Views.CellSettings.textBackground": "背景顏色", "SSE.Views.CellSettings.textBorderColor": "顏色", "SSE.Views.CellSettings.textBorders": "邊框樣式", "SSE.Views.CellSettings.textClearRule": "清除規則", "SSE.Views.CellSettings.textColor": "填充顏色", "SSE.Views.CellSettings.textColorScales": "色階", "SSE.Views.CellSettings.textCondFormat": "條件格式設定", "SSE.Views.CellSettings.textControl": "文字控制", "SSE.Views.CellSettings.textDataBars": "資料條", "SSE.Views.CellSettings.textDirection": "方向", "SSE.Views.CellSettings.textFill": "填入", "SSE.Views.CellSettings.textForeground": "前景顏色", "SSE.Views.CellSettings.textGradient": "漸層點", "SSE.Views.CellSettings.textGradientColor": "顏色", "SSE.Views.CellSettings.textGradientFill": "漸層填充", "SSE.Views.CellSettings.textIndent": "縮排", "SSE.Views.CellSettings.textItems": "項目", "SSE.Views.CellSettings.textLinear": "線性的", "SSE.Views.CellSettings.textManageRule": "管理規則", "SSE.Views.CellSettings.textNewRule": "新增規則", "SSE.Views.CellSettings.textNoFill": "無填滿", "SSE.Views.CellSettings.textOrientation": "文字方向", "SSE.Views.CellSettings.textPattern": "圖案", "SSE.Views.CellSettings.textPatternFill": "圖案", "SSE.Views.CellSettings.textPosition": "位置", "SSE.Views.CellSettings.textRadial": "徑向的", "SSE.Views.CellSettings.textSelectBorders": "選擇要套用上方選定樣式的邊框", "SSE.Views.CellSettings.textSelection": "從目前的選取範圍", "SSE.Views.CellSettings.textThisPivot": "從這個樞紐分析表", "SSE.Views.CellSettings.textThisSheet": "從這個工作表", "SSE.Views.CellSettings.textThisTable": "從這個表格", "SSE.Views.CellSettings.tipAddGradientPoint": "新增漸變點", "SSE.Views.CellSettings.tipAll": "設定外框和所有內部線", "SSE.Views.CellSettings.tipBottom": "僅設定外部底部邊框", "SSE.Views.CellSettings.tipDiagD": "設定對角線向下邊框", "SSE.Views.CellSettings.tipDiagU": "設置對角向上邊框", "SSE.Views.CellSettings.tipInner": "僅設定內部線", "SSE.Views.CellSettings.tipInnerHor": "僅設定水平內部線", "SSE.Views.CellSettings.tipInnerVert": "僅設定內部垂直線", "SSE.Views.CellSettings.tipLeft": "僅設定外部左邊框", "SSE.Views.CellSettings.tipNone": "設定無邊框", "SSE.Views.CellSettings.tipOuter": "僅設定外框", "SSE.Views.CellSettings.tipRemoveGradientPoint": "移除漸層點", "SSE.Views.CellSettings.tipRight": "僅設定外部右邊框", "SSE.Views.CellSettings.tipTop": "僅設定外部上邊框", "SSE.Views.ChartDataDialog.errorInFormula": "您輸入的公式中存在錯誤。", "SSE.Views.ChartDataDialog.errorInvalidReference": "引用無效。標題、值、大小或數據標籤的引用必須是單個單元格、行或列。", "SSE.Views.ChartDataDialog.errorMaxPoints": "每個圖表系列中的最大點數量為4096。", "SSE.Views.ChartDataDialog.errorMaxRows": "每個圖表的最大數據系列數為255。", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "該引用無效。標題，值，大小或數據標籤的引用必須是單個儲存格，行或列。", "SSE.Views.ChartDataDialog.errorNoValues": "要建立圖表，系列必須至少包含一個值。", "SSE.Views.ChartDataDialog.errorStockChart": "行順序不正確。建立股票圖表時，請按照以下順序在工作表上放置數據：開盤價、最高價、最低價、收盤價。", "SSE.Views.ChartDataDialog.textAdd": "新增", "SSE.Views.ChartDataDialog.textCategory": "水平（類別）軸標籤", "SSE.Views.ChartDataDialog.textData": "圖表資料範圍", "SSE.Views.ChartDataDialog.textDelete": "移除", "SSE.Views.ChartDataDialog.textDown": "下", "SSE.Views.ChartDataDialog.textEdit": "編輯", "SSE.Views.ChartDataDialog.textInvalidRange": "無效的儲存格範圍", "SSE.Views.ChartDataDialog.textSelectData": "選取資料", "SSE.Views.ChartDataDialog.textSeries": "圖例項目（數列）", "SSE.Views.ChartDataDialog.textSwitch": "切換列/欄", "SSE.Views.ChartDataDialog.textTitle": "圖表資料", "SSE.Views.ChartDataDialog.textUp": "向上", "SSE.Views.ChartDataRangeDialog.errorInFormula": "您輸入的公式中存在錯誤。", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "引用無效。標題、值、大小或數據標籤的引用必須是單個單元格、行或列。", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "每個圖表系列中的最大點數量為4096。", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "每個圖表的最大數據系列數為255。", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "該引用無效。標題，值，大小或數據標籤的引用必須是單個儲存格，行或列。", "SSE.Views.ChartDataRangeDialog.errorNoValues": "要建立圖表，系列必須至少包含一個值。", "SSE.Views.ChartDataRangeDialog.errorStockChart": "行順序不正確。建立股票圖表時，請按照以下順序在工作表上放置數據：開盤價、最高價、最低價、收盤價。", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "無效的儲存格範圍", "SSE.Views.ChartDataRangeDialog.textSelectData": "選取資料", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "座標軸標籤範圍", "SSE.Views.ChartDataRangeDialog.txtChoose": "選擇範圍", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "系列名稱", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "軸標籤", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "編輯數列", "SSE.Views.ChartDataRangeDialog.txtValues": "值", "SSE.Views.ChartDataRangeDialog.txtXValues": "X值", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y值", "SSE.Views.ChartSettings.errorMaxRows": "每個圖表的最大數據系列數為255。", "SSE.Views.ChartSettings.strLineWeight": "線條粗細", "SSE.Views.ChartSettings.strSparkColor": "顏色", "SSE.Views.ChartSettings.strTemplate": "範本", "SSE.Views.ChartSettings.text3dDepth": "深度（相對於基底的百分比）", "SSE.Views.ChartSettings.text3dHeight": "高度（基準的百分比）", "SSE.Views.ChartSettings.text3dRotation": "3D 旋轉", "SSE.Views.ChartSettings.textAdvanced": "顯示進階設定", "SSE.Views.ChartSettings.textAutoscale": "自動調整比例", "SSE.Views.ChartSettings.textBorderSizeErr": "輸入的值不正確。&lt;br&gt;請輸入介於0pt和1584pt之間的值。", "SSE.Views.ChartSettings.textChangeType": "變更類型", "SSE.Views.ChartSettings.textChartType": "變更圖表類型", "SSE.Views.ChartSettings.textDefault": "預設旋轉", "SSE.Views.ChartSettings.textDown": "下", "SSE.Views.ChartSettings.textEditData": "編輯資料和位置", "SSE.Views.ChartSettings.textFirstPoint": "第一個資料點", "SSE.Views.ChartSettings.textHeight": "高度", "SSE.Views.ChartSettings.textHighPoint": "最高點", "SSE.Views.ChartSettings.textKeepRatio": "比例恆定", "SSE.Views.ChartSettings.textLastPoint": "最後一個數據點", "SSE.Views.ChartSettings.textLeft": "左", "SSE.Views.ChartSettings.textLowPoint": "最低點", "SSE.Views.ChartSettings.textMarkers": "標記點", "SSE.Views.ChartSettings.textNarrow": "視野狹窄", "SSE.Views.ChartSettings.textNegativePoint": "負數點", "SSE.Views.ChartSettings.textPerspective": "透視", "SSE.Views.ChartSettings.textRanges": "資料範圍", "SSE.Views.ChartSettings.textRight": "右", "SSE.Views.ChartSettings.textRightAngle": "直角座標軸", "SSE.Views.ChartSettings.textSelectData": "選取資料", "SSE.Views.ChartSettings.textShow": "顯示", "SSE.Views.ChartSettings.textSize": "大小", "SSE.Views.ChartSettings.textStyle": "樣式", "SSE.Views.ChartSettings.textSwitch": "切換列/欄", "SSE.Views.ChartSettings.textType": "類型", "SSE.Views.ChartSettings.textUp": "向上", "SSE.Views.ChartSettings.textWiden": "擴大視野", "SSE.Views.ChartSettings.textWidth": "寬度", "SSE.Views.ChartSettings.textX": "X旋轉", "SSE.Views.ChartSettings.textY": "Y旋轉", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "錯誤！每個圖表的數據點數量上限為4096", "SSE.Views.ChartSettingsDlg.errorMaxRows": "錯誤！每個圖表的數據系列數量上限為255", "SSE.Views.ChartSettingsDlg.errorStockChart": "行順序不正確。建立股票圖表時，請按照以下順序在工作表上放置數據：開盤價、最高價、最低價、收盤價。", "SSE.Views.ChartSettingsDlg.textAbsolute": "不隨儲存格移動或調整大小", "SSE.Views.ChartSettingsDlg.textAlt": "替代文字", "SSE.Views.ChartSettingsDlg.textAltDescription": "描述", "SSE.Views.ChartSettingsDlg.textAltTip": "視覺物件的替代文字表示，將被朗讀給視力或認知障礙的人，以幫助他們更好地理解圖片、形狀、圖表或表格中的資訊。", "SSE.Views.ChartSettingsDlg.textAltTitle": "標題", "SSE.Views.ChartSettingsDlg.textAuto": "自動", "SSE.Views.ChartSettingsDlg.textAutoEach": "自動適用於每個", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "軸交叉點", "SSE.Views.ChartSettingsDlg.textAxisOptions": "軸選項", "SSE.Views.ChartSettingsDlg.textAxisPos": "軸位置", "SSE.Views.ChartSettingsDlg.textAxisSettings": "軸設定", "SSE.Views.ChartSettingsDlg.textAxisTitle": "標題", "SSE.Views.ChartSettingsDlg.textBase": "基礎", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "在刻度之間", "SSE.Views.ChartSettingsDlg.textBillions": "十億", "SSE.Views.ChartSettingsDlg.textBottom": "底部", "SSE.Views.ChartSettingsDlg.textCategoryName": "類別名稱", "SSE.Views.ChartSettingsDlg.textCenter": "置中", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "圖表元素和圖例", "SSE.Views.ChartSettingsDlg.textChartTitle": "圖表標題", "SSE.Views.ChartSettingsDlg.textCross": "交叉", "SSE.Views.ChartSettingsDlg.textCustom": "自訂", "SSE.Views.ChartSettingsDlg.textDataColumns": "在列中", "SSE.Views.ChartSettingsDlg.textDataLabels": "資料標籤", "SSE.Views.ChartSettingsDlg.textDataRows": "在行中", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "顯示圖例", "SSE.Views.ChartSettingsDlg.textEmptyCells": "隱藏和空白儲存格", "SSE.Views.ChartSettingsDlg.textEmptyLine": "連接資料點與線段", "SSE.Views.ChartSettingsDlg.textFit": "調整至寬度大小", "SSE.Views.ChartSettingsDlg.textFixed": "固定", "SSE.Views.ChartSettingsDlg.textFormat": "標記格式", "SSE.Views.ChartSettingsDlg.textGaps": "間隔", "SSE.Views.ChartSettingsDlg.textGridLines": "網格線", "SSE.Views.ChartSettingsDlg.textGroup": "分組火花圖", "SSE.Views.ChartSettingsDlg.textHide": "隱藏", "SSE.Views.ChartSettingsDlg.textHideAxis": "隱藏軸", "SSE.Views.ChartSettingsDlg.textHigh": "高", "SSE.Views.ChartSettingsDlg.textHorAxis": "水平軸", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "次要水平軸", "SSE.Views.ChartSettingsDlg.textHorizontal": "水平", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "百", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "在", "SSE.Views.ChartSettingsDlg.textInnerBottom": "內部底端", "SSE.Views.ChartSettingsDlg.textInnerTop": "內部頂端", "SSE.Views.ChartSettingsDlg.textInvalidRange": "錯誤！無效的儲存格範圍", "SSE.Views.ChartSettingsDlg.textLabelDist": "軸標距離", "SSE.Views.ChartSettingsDlg.textLabelInterval": "標籤間隔", "SSE.Views.ChartSettingsDlg.textLabelOptions": "標籤選項", "SSE.Views.ChartSettingsDlg.textLabelPos": "標籤位置", "SSE.Views.ChartSettingsDlg.textLayout": "版面配置", "SSE.Views.ChartSettingsDlg.textLeft": "左", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "左側覆蓋", "SSE.Views.ChartSettingsDlg.textLegendBottom": "底部", "SSE.Views.ChartSettingsDlg.textLegendLeft": "左", "SSE.Views.ChartSettingsDlg.textLegendPos": "圖例", "SSE.Views.ChartSettingsDlg.textLegendRight": "右", "SSE.Views.ChartSettingsDlg.textLegendTop": "頂部", "SSE.Views.ChartSettingsDlg.textLines": "線數", "SSE.Views.ChartSettingsDlg.textLocationRange": "位置範圍", "SSE.Views.ChartSettingsDlg.textLogScale": "對數刻度", "SSE.Views.ChartSettingsDlg.textLow": "低", "SSE.Views.ChartSettingsDlg.textMajor": "主要的", "SSE.Views.ChartSettingsDlg.textMajorMinor": "主要和次要", "SSE.Views.ChartSettingsDlg.textMajorType": "主要類型", "SSE.Views.ChartSettingsDlg.textManual": "手動", "SSE.Views.ChartSettingsDlg.textMarkers": "標記點", "SSE.Views.ChartSettingsDlg.textMarksInterval": "標記間隔", "SSE.Views.ChartSettingsDlg.textMaxValue": "最大值", "SSE.Views.ChartSettingsDlg.textMillions": "百萬", "SSE.Views.ChartSettingsDlg.textMinor": "次要", "SSE.Views.ChartSettingsDlg.textMinorType": "次要類型", "SSE.Views.ChartSettingsDlg.textMinValue": "最小值", "SSE.Views.ChartSettingsDlg.textNextToAxis": "靠近軸", "SSE.Views.ChartSettingsDlg.textNone": "無", "SSE.Views.ChartSettingsDlg.textNoOverlay": "無覆蓋", "SSE.Views.ChartSettingsDlg.textOneCell": "移動但不調整儲存格的大小", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "刻度線上", "SSE.Views.ChartSettingsDlg.textOut": "Out", "SSE.Views.ChartSettingsDlg.textOuterTop": "外部頂端", "SSE.Views.ChartSettingsDlg.textOverlay": "覆蓋", "SSE.Views.ChartSettingsDlg.textReverse": "值倒序", "SSE.Views.ChartSettingsDlg.textReverseOrder": "倒序", "SSE.Views.ChartSettingsDlg.textRight": "右", "SSE.Views.ChartSettingsDlg.textRightOverlay": "右側覆蓋", "SSE.Views.ChartSettingsDlg.textRotated": "已旋轉", "SSE.Views.ChartSettingsDlg.textSameAll": "全部相同", "SSE.Views.ChartSettingsDlg.textSelectData": "選取資料", "SSE.Views.ChartSettingsDlg.textSeparator": "資料標籤分隔符", "SSE.Views.ChartSettingsDlg.textSeriesName": "系列名稱", "SSE.Views.ChartSettingsDlg.textShow": "顯示", "SSE.Views.ChartSettingsDlg.textShowBorders": "顯示圖表邊框", "SSE.Views.ChartSettingsDlg.textShowData": "顯示隱藏行和列中的資料", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "顯示空白儲存格為", "SSE.Views.ChartSettingsDlg.textShowEquation": "Display equation on chart", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "顯示軸", "SSE.Views.ChartSettingsDlg.textShowValues": "顯示圖表數值", "SSE.Views.ChartSettingsDlg.textSingle": "單一資料列", "SSE.Views.ChartSettingsDlg.textSmooth": "平滑", "SSE.Views.ChartSettingsDlg.textSnap": "儲存格對齊", "SSE.Views.ChartSettingsDlg.textSparkRanges": "資料趨勢圖範圍", "SSE.Views.ChartSettingsDlg.textStraight": "直線", "SSE.Views.ChartSettingsDlg.textStyle": "樣式", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "千位數", "SSE.Views.ChartSettingsDlg.textTickOptions": "刻度選項", "SSE.Views.ChartSettingsDlg.textTitle": "圖表-進階設定", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "資料趨勢圖 - 進階設定", "SSE.Views.ChartSettingsDlg.textTop": "頂部", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Trendline options", "SSE.Views.ChartSettingsDlg.textTrillions": "兆", "SSE.Views.ChartSettingsDlg.textTwoCell": "隨儲存格一起移動和調整大小", "SSE.Views.ChartSettingsDlg.textType": "類型", "SSE.Views.ChartSettingsDlg.textTypeData": "類型與資料", "SSE.Views.ChartSettingsDlg.textUnits": "顯示單位", "SSE.Views.ChartSettingsDlg.textValue": "值", "SSE.Views.ChartSettingsDlg.textVertAxis": "垂直軸", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "次要垂直軸", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X軸標題", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y軸標題", "SSE.Views.ChartSettingsDlg.textZero": "零", "SSE.Views.ChartSettingsDlg.txtEmpty": "此欄位為必填欄位", "SSE.Views.ChartTypeDialog.errorComboSeries": "要建立組合圖表，請選擇至少兩個資料系列。", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "所選圖表類型需要現有圖表使用的次要軸。請選擇其他圖表類型。", "SSE.Views.ChartTypeDialog.textSecondary": "次要軸", "SSE.Views.ChartTypeDialog.textSeries": "系列", "SSE.Views.ChartTypeDialog.textStyle": "樣式", "SSE.Views.ChartTypeDialog.textTitle": "圖表類型", "SSE.Views.ChartTypeDialog.textType": "類型", "SSE.Views.ChartWizardDialog.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartWizardDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order: opening price, max price, min price, closing price.", "SSE.Views.ChartWizardDialog.textRecommended": "Recommended", "SSE.Views.ChartWizardDialog.textSecondary": "Secondary Axis", "SSE.Views.ChartWizardDialog.textSeries": "Series", "SSE.Views.ChartWizardDialog.textTitle": "Insert Chart", "SSE.Views.ChartWizardDialog.textTitleChange": "Change chart type", "SSE.Views.ChartWizardDialog.textType": "Type", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Choose the chart type and axis for your data series", "SSE.Views.CreatePivotDialog.textDataRange": "來源數據範圍", "SSE.Views.CreatePivotDialog.textDestination": "選擇要放置表格的位置", "SSE.Views.CreatePivotDialog.textExist": "現有工作表", "SSE.Views.CreatePivotDialog.textInvalidRange": "無效的儲存格範圍", "SSE.Views.CreatePivotDialog.textNew": "新工作表", "SSE.Views.CreatePivotDialog.textSelectData": "選取資料", "SSE.Views.CreatePivotDialog.textTitle": "建立樞紐分析表", "SSE.Views.CreatePivotDialog.txtEmpty": "此欄位為必填欄位", "SSE.Views.CreateSparklineDialog.textDataRange": "來源數據範圍", "SSE.Views.CreateSparklineDialog.textDestination": "要放置資料的位置", "SSE.Views.CreateSparklineDialog.textInvalidRange": "無效的儲存格範圍", "SSE.Views.CreateSparklineDialog.textSelectData": "選取資料", "SSE.Views.CreateSparklineDialog.textTitle": "建立火花圖", "SSE.Views.CreateSparklineDialog.txtEmpty": "此欄位為必填欄位", "SSE.Views.DataTab.capBtnGroup": "分組", "SSE.Views.DataTab.capBtnTextCustomSort": "自訂排序", "SSE.Views.DataTab.capBtnTextDataValidation": "資料驗證", "SSE.Views.DataTab.capBtnTextRemDuplicates": "移除重複項目", "SSE.Views.DataTab.capBtnTextToCol": "文字轉欄", "SSE.Views.DataTab.capBtnUngroup": "取消分組", "SSE.Views.DataTab.capDataExternalLinks": "外部連結", "SSE.Views.DataTab.capDataFromText": "取得資料", "SSE.Views.DataTab.capGoalSeek": "Goal Seek", "SSE.Views.DataTab.mniFromFile": "從本地TXT/CSV匯入", "SSE.Views.DataTab.mniFromUrl": "從本地TXT/CSV匯入", "SSE.Views.DataTab.mniFromXMLFile": "從本地XML", "SSE.Views.DataTab.textBelow": "摘要列放於詳細列下方", "SSE.Views.DataTab.textClear": "清除大綱", "SSE.Views.DataTab.textColumns": "取消群組欄", "SSE.Views.DataTab.textGroupColumns": "分組欄位", "SSE.Views.DataTab.textGroupRows": "分組列", "SSE.Views.DataTab.textRightOf": "摘要欄位放於詳細欄位右側", "SSE.Views.DataTab.textRows": "取消群組列", "SSE.Views.DataTab.tipCustomSort": "自訂排序", "SSE.Views.DataTab.tipDataFromText": "從檔案取得資料", "SSE.Views.DataTab.tipDataValidation": "資料驗證", "SSE.Views.DataTab.tipExternalLinks": "查看此試算表所連結的其他文件", "SSE.Views.DataTab.tipGoalSeek": "Find the right input for the value you want", "SSE.Views.DataTab.tipGroup": "群組範圍的儲存格", "SSE.Views.DataTab.tipRemDuplicates": "從工作表中刪除重複的行", "SSE.Views.DataTab.tipToColumns": "將儲存格文字分割為多個欄位", "SSE.Views.DataTab.tipUngroup": "取消合併儲存格", "SSE.Views.DataValidationDialog.errorFormula": "目前評估的值為錯誤。是否繼續？", "SSE.Views.DataValidationDialog.errorInvalid": "您為字段“{0}”輸入的值無效。", "SSE.Views.DataValidationDialog.errorInvalidDate": "您為字段\"{0}\"輸入的日期無效。", "SSE.Views.DataValidationDialog.errorInvalidList": "列表源必須是一個分隔的列表，或者是對單行或單列的引用。", "SSE.Views.DataValidationDialog.errorInvalidTime": "您為字段“{0}”輸入的時間無效。", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "{1}字段必須大於或等於\"{0}\"字段。", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "您必須在字段「{0}」和字段「{1}」中輸入值。", "SSE.Views.DataValidationDialog.errorMustEnterValue": "您必須在字段「{0}」中輸入值。", "SSE.Views.DataValidationDialog.errorNamedRange": "您指定的命名範圍找不到。", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "無法在條件\"{0}\"中使用負數值。", "SSE.Views.DataValidationDialog.errorNotNumeric": "字段\"{0}\"必須是數值、數值表達式或引用包含數值的單元格。", "SSE.Views.DataValidationDialog.strError": "錯誤警示", "SSE.Views.DataValidationDialog.strInput": "輸入訊息", "SSE.Views.DataValidationDialog.strSettings": "設定", "SSE.Views.DataValidationDialog.textAlert": "警示", "SSE.Views.DataValidationDialog.textAllow": "允許", "SSE.Views.DataValidationDialog.textApply": "將這些變更套用至所有其他具有相同設定的儲存格", "SSE.Views.DataValidationDialog.textCellSelected": "當選取儲存格時，顯示此輸入訊息", "SSE.Views.DataValidationDialog.textCompare": "與...相比", "SSE.Views.DataValidationDialog.textData": "資料", "SSE.Views.DataValidationDialog.textEndDate": "結束日期", "SSE.Views.DataValidationDialog.textEndTime": "結束時間", "SSE.Views.DataValidationDialog.textError": "錯誤訊息", "SSE.Views.DataValidationDialog.textFormula": "配方", "SSE.Views.DataValidationDialog.textIgnore": "忽略空白", "SSE.Views.DataValidationDialog.textInput": "輸入訊息", "SSE.Views.DataValidationDialog.textMax": "最大值", "SSE.Views.DataValidationDialog.textMessage": "訊息", "SSE.Views.DataValidationDialog.textMin": "最小值", "SSE.Views.DataValidationDialog.textSelectData": "選取資料", "SSE.Views.DataValidationDialog.textShowDropDown": "在儲存格中顯示下拉清單", "SSE.Views.DataValidationDialog.textShowError": "輸入無效資料後顯示錯誤警示", "SSE.Views.DataValidationDialog.textShowInput": "當選擇儲存格時顯示輸入訊息", "SSE.Views.DataValidationDialog.textSource": "來源", "SSE.Views.DataValidationDialog.textStartDate": "開始日期", "SSE.Views.DataValidationDialog.textStartTime": "開始時間", "SSE.Views.DataValidationDialog.textStop": "停止", "SSE.Views.DataValidationDialog.textStyle": "樣式", "SSE.Views.DataValidationDialog.textTitle": "標題", "SSE.Views.DataValidationDialog.textUserEnters": "當使用者輸入無效資料時，顯示此錯誤警示", "SSE.Views.DataValidationDialog.txtAny": "任何值", "SSE.Views.DataValidationDialog.txtBetween": "介於之間", "SSE.Views.DataValidationDialog.txtDate": "日期", "SSE.Views.DataValidationDialog.txtDecimal": "小數點", "SSE.Views.DataValidationDialog.txtElTime": "以經過時間", "SSE.Views.DataValidationDialog.txtEndDate": "結束日期", "SSE.Views.DataValidationDialog.txtEndTime": "結束時間", "SSE.Views.DataValidationDialog.txtEqual": "等於", "SSE.Views.DataValidationDialog.txtGreaterThan": "大於", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "大於或等於", "SSE.Views.DataValidationDialog.txtLength": "長度", "SSE.Views.DataValidationDialog.txtLessThan": "小於", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "小於或等於", "SSE.Views.DataValidationDialog.txtList": "清單", "SSE.Views.DataValidationDialog.txtNotBetween": "不在之間", "SSE.Views.DataValidationDialog.txtNotEqual": "不等於", "SSE.Views.DataValidationDialog.txtOther": "其它", "SSE.Views.DataValidationDialog.txtStartDate": "開始日期", "SSE.Views.DataValidationDialog.txtStartTime": "開始時間", "SSE.Views.DataValidationDialog.txtTextLength": "文字長度", "SSE.Views.DataValidationDialog.txtTime": "時間", "SSE.Views.DataValidationDialog.txtWhole": "整數", "SSE.Views.DigitalFilterDialog.capAnd": "和", "SSE.Views.DigitalFilterDialog.capCondition1": "等於", "SSE.Views.DigitalFilterDialog.capCondition10": "不以...結尾", "SSE.Views.DigitalFilterDialog.capCondition11": "包含", "SSE.Views.DigitalFilterDialog.capCondition12": "不含", "SSE.Views.DigitalFilterDialog.capCondition2": "不等於", "SSE.Views.DigitalFilterDialog.capCondition3": "大於", "SSE.Views.DigitalFilterDialog.capCondition30": "is after", "SSE.Views.DigitalFilterDialog.capCondition4": "大於或等於", "SSE.Views.DigitalFilterDialog.capCondition40": "is after or equal to", "SSE.Views.DigitalFilterDialog.capCondition5": "小於", "SSE.Views.DigitalFilterDialog.capCondition50": "is before", "SSE.Views.DigitalFilterDialog.capCondition6": "小於或等於", "SSE.Views.DigitalFilterDialog.capCondition60": "is before or equal to", "SSE.Views.DigitalFilterDialog.capCondition7": "開頭為", "SSE.Views.DigitalFilterDialog.capCondition8": "不以...開頭", "SSE.Views.DigitalFilterDialog.capCondition9": "結尾為", "SSE.Views.DigitalFilterDialog.capOr": "或", "SSE.Views.DigitalFilterDialog.textNoFilter": "沒有過濾器", "SSE.Views.DigitalFilterDialog.textShowRows": "顯示符合以下條件的列：", "SSE.Views.DigitalFilterDialog.textUse1": "使用 ? 代表任意單一字元", "SSE.Views.DigitalFilterDialog.textUse2": "使用 * 代表任意序列的字元", "SSE.Views.DigitalFilterDialog.txtSelectDate": "Select date", "SSE.Views.DigitalFilterDialog.txtTitle": "自訂過濾器", "SSE.Views.DocumentHolder.advancedEquationText": "方程式設定", "SSE.Views.DocumentHolder.advancedImgText": "圖片進階設定", "SSE.Views.DocumentHolder.advancedShapeText": "形狀進階設定", "SSE.Views.DocumentHolder.advancedSlicerText": "樞紐分析表篩選器 - 進階設定", "SSE.Views.DocumentHolder.allLinearText": "全部 - 線性", "SSE.Views.DocumentHolder.allProfText": "全部 - 專業", "SSE.Views.DocumentHolder.bottomCellText": "對齊底部", "SSE.Views.DocumentHolder.bulletsText": "項目符號與編號", "SSE.Views.DocumentHolder.centerCellText": "置於中央對齊", "SSE.Views.DocumentHolder.chartDataText": "選擇圖表資料", "SSE.Views.DocumentHolder.chartText": "圖表進階設定", "SSE.Views.DocumentHolder.chartTypeText": "變更圖表類型", "SSE.Views.DocumentHolder.currLinearText": "目前 - 線性", "SSE.Views.DocumentHolder.currProfText": "目前 - 專業", "SSE.Views.DocumentHolder.deleteColumnText": "欄", "SSE.Views.DocumentHolder.deleteRowText": "列", "SSE.Views.DocumentHolder.deleteTableText": "表格", "SSE.Views.DocumentHolder.direct270Text": "向上旋轉文字", "SSE.Views.DocumentHolder.direct90Text": "向下旋轉文字", "SSE.Views.DocumentHolder.directHText": "水平", "SSE.Views.DocumentHolder.directionText": "文字方向", "SSE.Views.DocumentHolder.editChartText": "編輯資料", "SSE.Views.DocumentHolder.editHyperlinkText": "編輯超連結", "SSE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "SSE.Views.DocumentHolder.insertColumnLeftText": "左欄", "SSE.Views.DocumentHolder.insertColumnRightText": "右欄", "SSE.Views.DocumentHolder.insertRowAboveText": "上方列", "SSE.Views.DocumentHolder.insertRowBelowText": "下方列", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "實際大小", "SSE.Views.DocumentHolder.removeHyperlinkText": "移除超連結", "SSE.Views.DocumentHolder.selectColumnText": "整個欄位", "SSE.Views.DocumentHolder.selectDataText": "欄資料", "SSE.Views.DocumentHolder.selectRowText": "列", "SSE.Views.DocumentHolder.selectTableText": "表格", "SSE.Views.DocumentHolder.showEqToolbar": "Show Equation Toolbar", "SSE.Views.DocumentHolder.strDelete": "移除簽名", "SSE.Views.DocumentHolder.strDetails": "簽名詳細資訊", "SSE.Views.DocumentHolder.strSetup": "簽名設定", "SSE.Views.DocumentHolder.strSign": "簽名", "SSE.Views.DocumentHolder.textAlign": "對齊", "SSE.Views.DocumentHolder.textArrange": "排列", "SSE.Views.DocumentHolder.textArrangeBack": "傳送到背景", "SSE.Views.DocumentHolder.textArrangeBackward": "向後發送", "SSE.Views.DocumentHolder.textArrangeForward": "向前移動", "SSE.Views.DocumentHolder.textArrangeFront": "置於前景", "SSE.Views.DocumentHolder.textAverage": "平均值", "SSE.Views.DocumentHolder.textBullets": "項目符號", "SSE.Views.DocumentHolder.textCopyCells": "Copy cells", "SSE.Views.DocumentHolder.textCount": "計數", "SSE.Views.DocumentHolder.textCrop": "裁剪", "SSE.Views.DocumentHolder.textCropFill": "填入", "SSE.Views.DocumentHolder.textCropFit": "適應大小", "SSE.Views.DocumentHolder.textEditPoints": "編輯點", "SSE.Views.DocumentHolder.textEntriesList": "從下拉清單中選擇", "SSE.Views.DocumentHolder.textFillDays": "Fill days", "SSE.Views.DocumentHolder.textFillFormatOnly": "Fill formatting only", "SSE.Views.DocumentHolder.textFillMonths": "Fill months", "SSE.Views.DocumentHolder.textFillSeries": "Fill series", "SSE.Views.DocumentHolder.textFillWeekdays": "Fill weekdays", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Fill without formatting", "SSE.Views.DocumentHolder.textFillYears": "Fill years", "SSE.Views.DocumentHolder.textFlashFill": "Flash fill", "SSE.Views.DocumentHolder.textFlipH": "水平翻轉", "SSE.Views.DocumentHolder.textFlipV": "垂直翻轉", "SSE.Views.DocumentHolder.textFreezePanes": "凍結窗格", "SSE.Views.DocumentHolder.textFromFile": "從檔案插入", "SSE.Views.DocumentHolder.textFromStorage": "從儲存位置插入", "SSE.Views.DocumentHolder.textFromUrl": "從網址", "SSE.Views.DocumentHolder.textGrowthTrend": "Growth trend", "SSE.Views.DocumentHolder.textLinearTrend": "Linear trend", "SSE.Views.DocumentHolder.textListSettings": "清單設定", "SSE.Views.DocumentHolder.textMacro": "指定巨集", "SSE.Views.DocumentHolder.textMax": "最大值", "SSE.Views.DocumentHolder.textMin": "最小值", "SSE.Views.DocumentHolder.textMore": "更多函數", "SSE.Views.DocumentHolder.textMoreFormats": "更多格式", "SSE.Views.DocumentHolder.textNone": "無", "SSE.Views.DocumentHolder.textNumbering": "編號", "SSE.Views.DocumentHolder.textReplace": "取代圖片", "SSE.Views.DocumentHolder.textResetCrop": "Reset crop", "SSE.Views.DocumentHolder.textRotate": "旋轉", "SSE.Views.DocumentHolder.textRotate270": "逆時針旋轉90°", "SSE.Views.DocumentHolder.textRotate90": "順時針旋轉90°", "SSE.Views.DocumentHolder.textSaveAsPicture": "另存為圖片", "SSE.Views.DocumentHolder.textSeries": "Series", "SSE.Views.DocumentHolder.textShapeAlignBottom": "對齊底部", "SSE.Views.DocumentHolder.textShapeAlignCenter": "置中對齊", "SSE.Views.DocumentHolder.textShapeAlignLeft": "靠左對齊", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "置於中央對齊", "SSE.Views.DocumentHolder.textShapeAlignRight": "靠右對齊", "SSE.Views.DocumentHolder.textShapeAlignTop": "靠上對齊", "SSE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "SSE.Views.DocumentHolder.textStdDev": "標準差", "SSE.Views.DocumentHolder.textSum": "求和", "SSE.Views.DocumentHolder.textUndo": "復原", "SSE.Views.DocumentHolder.textUnFreezePanes": "取消凍結窗格", "SSE.Views.DocumentHolder.textVar": "變異數", "SSE.Views.DocumentHolder.tipMarkersArrow": "箭頭符號", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "勾號符號清單", "SSE.Views.DocumentHolder.tipMarkersDash": "虛線符號", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "填充菱形符號", "SSE.Views.DocumentHolder.tipMarkersFRound": "填充圓形符號", "SSE.Views.DocumentHolder.tipMarkersFSquare": "填充方形符號", "SSE.Views.DocumentHolder.tipMarkersHRound": "空心圓點符號", "SSE.Views.DocumentHolder.tipMarkersStar": "星形符號", "SSE.Views.DocumentHolder.topCellText": "靠上對齊", "SSE.Views.DocumentHolder.txtAccounting": "會計", "SSE.Views.DocumentHolder.txtAddComment": "新增註解", "SSE.Views.DocumentHolder.txtAddNamedRange": "已定義名稱", "SSE.Views.DocumentHolder.txtArrange": "排列", "SSE.Views.DocumentHolder.txtAscending": "遞增排序", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "自動調整欄寬", "SSE.Views.DocumentHolder.txtAutoRowHeight": "自動調整行高", "SSE.Views.DocumentHolder.txtAverage": " 平均", "SSE.Views.DocumentHolder.txtCellFormat": "Format cells", "SSE.Views.DocumentHolder.txtClear": "清除", "SSE.Views.DocumentHolder.txtClearAll": "全部", "SSE.Views.DocumentHolder.txtClearComments": "評論", "SSE.Views.DocumentHolder.txtClearFormat": "格式", "SSE.Views.DocumentHolder.txtClearHyper": "超連結", "SSE.Views.DocumentHolder.txtClearPivotField": "從 {0} 清除篩選", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "清除選取的火花圖群組", "SSE.Views.DocumentHolder.txtClearSparklines": "清除選取的火花圖", "SSE.Views.DocumentHolder.txtClearText": "文字", "SSE.Views.DocumentHolder.txtCollapse": "折疊", "SSE.Views.DocumentHolder.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.DocumentHolder.txtColumn": "整個欄位", "SSE.Views.DocumentHolder.txtColumnWidth": "設定欄寬", "SSE.Views.DocumentHolder.txtCondFormat": "條件格式設定", "SSE.Views.DocumentHolder.txtCopy": "複製", "SSE.Views.DocumentHolder.txtCount": "計數", "SSE.Views.DocumentHolder.txtCurrency": "貨幣", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "自訂欄寬", "SSE.Views.DocumentHolder.txtCustomRowHeight": "自訂列高", "SSE.Views.DocumentHolder.txtCustomSort": "自訂排序", "SSE.Views.DocumentHolder.txtCut": "剪下", "SSE.Views.DocumentHolder.txtDateLong": "長日期", "SSE.Views.DocumentHolder.txtDateShort": "短日期", "SSE.Views.DocumentHolder.txtDelete": "刪除", "SSE.Views.DocumentHolder.txtDelField": "移除", "SSE.Views.DocumentHolder.txtDescending": "遞減排序", "SSE.Views.DocumentHolder.txtDifference": "與...的區別", "SSE.Views.DocumentHolder.txtDistribHor": "水平分散對齊", "SSE.Views.DocumentHolder.txtDistribVert": "垂直分散對齊", "SSE.Views.DocumentHolder.txtEditComment": "編輯註解", "SSE.Views.DocumentHolder.txtEditObject": "Edit object", "SSE.Views.DocumentHolder.txtExpand": "Expand", "SSE.Views.DocumentHolder.txtExpandCollapse": "Expand/Collapse", "SSE.Views.DocumentHolder.txtExpandEntire": "Expand Entire Field", "SSE.Views.DocumentHolder.txtFieldSettings": "欄位設定", "SSE.Views.DocumentHolder.txtFilter": "篩選", "SSE.Views.DocumentHolder.txtFilterCellColor": "按儲存格顏色篩選", "SSE.Views.DocumentHolder.txtFilterFontColor": "按字型顏色篩選", "SSE.Views.DocumentHolder.txtFilterValue": "按選取儲存格值篩選", "SSE.Views.DocumentHolder.txtFormula": "插入函數", "SSE.Views.DocumentHolder.txtFraction": "分數", "SSE.Views.DocumentHolder.txtGeneral": "一般", "SSE.Views.DocumentHolder.txtGetLink": "取得此範圍的連結", "SSE.Views.DocumentHolder.txtGrandTotal": "總計", "SSE.Views.DocumentHolder.txtGroup": "分組", "SSE.Views.DocumentHolder.txtHide": "隱藏", "SSE.Views.DocumentHolder.txtIndex": "索引", "SSE.Views.DocumentHolder.txtInsert": "插入", "SSE.Views.DocumentHolder.txtInsHyperlink": "超連結", "SSE.Views.DocumentHolder.txtInsImage": "Insert image from file", "SSE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "SSE.Views.DocumentHolder.txtLabelFilter": "標籤篩選", "SSE.Views.DocumentHolder.txtMax": "最大值", "SSE.Views.DocumentHolder.txtMin": "最小值", "SSE.Views.DocumentHolder.txtMoreOptions": "更多選項", "SSE.Views.DocumentHolder.txtNormal": "不計算", "SSE.Views.DocumentHolder.txtNumber": "數字", "SSE.Views.DocumentHolder.txtNumFormat": "數字格式", "SSE.Views.DocumentHolder.txtPaste": "貼上", "SSE.Views.DocumentHolder.txtPercent": "% 的", "SSE.Views.DocumentHolder.txtPercentage": "百分比", "SSE.Views.DocumentHolder.txtPercentDiff": "% 差異", "SSE.Views.DocumentHolder.txtPercentOfCol": "列總計的百分比", "SSE.Views.DocumentHolder.txtPercentOfGrand": "總計的百分比", "SSE.Views.DocumentHolder.txtPercentOfParent": "父總計的百分比", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "父欄位總計的百分比", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "父列總計的百分比", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "累計在 %1 內", "SSE.Views.DocumentHolder.txtPercentOfTotal": "列總計的百分比", "SSE.Views.DocumentHolder.txtPivotSettings": "樞紐分析表設定", "SSE.Views.DocumentHolder.txtProduct": "乘積", "SSE.Views.DocumentHolder.txtRankAscending": "將最小值排名至最大值", "SSE.Views.DocumentHolder.txtRankDescending": "將最大值排名至最小值", "SSE.Views.DocumentHolder.txtReapply": "重新應用", "SSE.Views.DocumentHolder.txtRefresh": "刷新", "SSE.Views.DocumentHolder.txtRow": "整個列", "SSE.Views.DocumentHolder.txtRowHeight": "設定列高", "SSE.Views.DocumentHolder.txtRunTotal": "累計總計", "SSE.Views.DocumentHolder.txtScientific": "科學計數法", "SSE.Views.DocumentHolder.txtSelect": "選擇", "SSE.Views.DocumentHolder.txtShiftDown": "向下移動儲存格", "SSE.Views.DocumentHolder.txtShiftLeft": "向左移動儲存格", "SSE.Views.DocumentHolder.txtShiftRight": "向右移動儲存格", "SSE.Views.DocumentHolder.txtShiftUp": "向上移動儲存格", "SSE.Views.DocumentHolder.txtShow": "顯示", "SSE.Views.DocumentHolder.txtShowAs": "顯示值為", "SSE.Views.DocumentHolder.txtShowComment": "顯示註解", "SSE.Views.DocumentHolder.txtShowDetails": "Show details", "SSE.Views.DocumentHolder.txtSort": "排序", "SSE.Views.DocumentHolder.txtSortCellColor": "選取的儲存格顏色在上方", "SSE.Views.DocumentHolder.txtSortFontColor": "選取的字型顏色在上方", "SSE.Views.DocumentHolder.txtSortOption": "更多排序選項", "SSE.Views.DocumentHolder.txtSparklines": "迷你圖", "SSE.Views.DocumentHolder.txtSubtotalField": "小計", "SSE.Views.DocumentHolder.txtSum": "求和", "SSE.Views.DocumentHolder.txtSummarize": "根據值進行摘要", "SSE.Views.DocumentHolder.txtText": "文字", "SSE.Views.DocumentHolder.txtTextAdvanced": "文字進階設定", "SSE.Views.DocumentHolder.txtTime": "時間", "SSE.Views.DocumentHolder.txtTop10": "前10名", "SSE.Views.DocumentHolder.txtUngroup": "取消分組", "SSE.Views.DocumentHolder.txtValueFieldSettings": "值欄位設定", "SSE.Views.DocumentHolder.txtValueFilter": "數值篩選", "SSE.Views.DocumentHolder.txtWidth": "寬度", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "垂直對齊", "SSE.Views.ExternalLinksDlg.closeButtonText": "關閉", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Automatically update data from the linked sources", "SSE.Views.ExternalLinksDlg.textChange": "Change source", "SSE.Views.ExternalLinksDlg.textDelete": "中斷連結", "SSE.Views.ExternalLinksDlg.textDeleteAll": "中斷所有連結", "SSE.Views.ExternalLinksDlg.textOk": "確定", "SSE.Views.ExternalLinksDlg.textOpen": "Open source", "SSE.Views.ExternalLinksDlg.textSource": "來源", "SSE.Views.ExternalLinksDlg.textStatus": "狀態", "SSE.Views.ExternalLinksDlg.textUnknown": "未知", "SSE.Views.ExternalLinksDlg.textUpdate": "更新數值", "SSE.Views.ExternalLinksDlg.textUpdateAll": "全部更新", "SSE.Views.ExternalLinksDlg.textUpdating": "正在更新中...", "SSE.Views.ExternalLinksDlg.txtTitle": "外部連結", "SSE.Views.FieldSettingsDialog.strLayout": "版面配置", "SSE.Views.FieldSettingsDialog.strSubtotals": "小計", "SSE.Views.FieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.FieldSettingsDialog.textReport": "報表表單", "SSE.Views.FieldSettingsDialog.textTitle": "欄位設定", "SSE.Views.FieldSettingsDialog.txtAverage": "平均值", "SSE.Views.FieldSettingsDialog.txtBlank": "在每個項目之後插入空白行", "SSE.Views.FieldSettingsDialog.txtBottom": "顯示在群組底部", "SSE.Views.FieldSettingsDialog.txtCompact": "緊湊", "SSE.Views.FieldSettingsDialog.txtCount": "計數", "SSE.Views.FieldSettingsDialog.txtCountNums": "計算數字", "SSE.Views.FieldSettingsDialog.txtCustomName": "自訂名稱", "SSE.Views.FieldSettingsDialog.txtEmpty": "顯示沒有資料的項目", "SSE.Views.FieldSettingsDialog.txtMax": "最大值", "SSE.Views.FieldSettingsDialog.txtMin": "最小值", "SSE.Views.FieldSettingsDialog.txtOutline": "大綱", "SSE.Views.FieldSettingsDialog.txtProduct": "乘積", "SSE.Views.FieldSettingsDialog.txtRepeat": "在每行重複項目標籤", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "顯示小計", "SSE.Views.FieldSettingsDialog.txtSourceName": "來源名稱:", "SSE.Views.FieldSettingsDialog.txtStdDev": "標準差", "SSE.Views.FieldSettingsDialog.txtStdDevp": "標準差p", "SSE.Views.FieldSettingsDialog.txtSum": "求和", "SSE.Views.FieldSettingsDialog.txtSummarize": "小計的函數", "SSE.Views.FieldSettingsDialog.txtTabular": "表格形式", "SSE.Views.FieldSettingsDialog.txtTop": "顯示在群組頂部", "SSE.Views.FieldSettingsDialog.txtVar": "變異數", "SSE.Views.FieldSettingsDialog.txtVarp": "總體變異數", "SSE.Views.FileMenu.ariaFileMenu": "File menu", "SSE.Views.FileMenu.btnBackCaption": "打開文件所在位置", "SSE.Views.FileMenu.btnCloseEditor": "Close File", "SSE.Views.FileMenu.btnCloseMenuCaption": "關閉選單", "SSE.Views.FileMenu.btnCreateNewCaption": "新增", "SSE.Views.FileMenu.btnDownloadCaption": "下載為", "SSE.Views.FileMenu.btnExitCaption": "關閉", "SSE.Views.FileMenu.btnExportToPDFCaption": "Export to PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "開啟", "SSE.Views.FileMenu.btnHelpCaption": "說明", "SSE.Views.FileMenu.btnHistoryCaption": "版本歷史", "SSE.Views.FileMenu.btnInfoCaption": "試算表資訊", "SSE.Views.FileMenu.btnPrintCaption": "列印", "SSE.Views.FileMenu.btnProtectCaption": "保護", "SSE.Views.FileMenu.btnRecentFilesCaption": "最近打開的", "SSE.Views.FileMenu.btnRenameCaption": "重新命名", "SSE.Views.FileMenu.btnReturnCaption": "返回至試算表", "SSE.Views.FileMenu.btnRightsCaption": "存取權限", "SSE.Views.FileMenu.btnSaveAsCaption": "另存為", "SSE.Views.FileMenu.btnSaveCaption": "儲存", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "複製為", "SSE.Views.FileMenu.btnSettingsCaption": "進階設定", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "SSE.Views.FileMenu.btnToEditCaption": "編輯試算表", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "空白試算表", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "新增", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "套用", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "添加作者", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "新增文字", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "應用程式", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "作者", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "變更存取權限", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "評論", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "已創建", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "上次修改者", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "上次修改時間", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "擁有者", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "位置", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "擁有權限的人", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "試算表資訊", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "主旨", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "標籤", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "標題", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "已上傳", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "存取權限", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "變更存取權限", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "擁有權限的人", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "套用", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "共同編輯模式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "使用1904年日期系統", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "小數點分隔符", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "字典語言", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Enable iterative calculation", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "快速", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "字型微調", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "公式語言", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "舉例：SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Show function tooltip", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Show horizontal scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "忽略大寫字詞", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "忽略包含數字的文字", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "巨集設定", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Maximum change", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Maximum iterations", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "貼上內容時顯示\"貼上選項\"按鈕", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "R1C1參考樣式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "區域設置", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "舉例：", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTL interface", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "在工作表中顯示註解", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "顯示其他使用者的變更", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "顯示已解決的註解", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Snapped to the grid while scrolling", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "嚴格", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Tab style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "介面主題", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "千位數分隔符", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "測量單位", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "根據地區設定使用分隔符號", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Show vertical scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "預設縮放值", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "每10分鐘", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "每30分鐘", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "每5分鐘", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "每小時", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "自動恢復", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "自動儲存", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "已停用", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "Fill", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "儲存所有歷史版本到伺服器", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "Line", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "每分鐘", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "參考樣式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "進階設定", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "Appearance", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "自動更正選項...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "白俄羅斯文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "保加利亞文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "加泰隆尼亞文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "預設暫存模式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "計算中", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "公分", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "共同編輯", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "捷克文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Customize quick access", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "丹麥文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "德語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "編輯並儲存", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "希臘文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "英語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "西班牙文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "即時共同編輯。所有更改都會自動儲存", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "芬蘭語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "法文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "匈牙利語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "亞美尼亞文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "印尼文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "英寸", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "義大利文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "日文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "韓文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Last used", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "寮語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "拉脫維亞語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "參照 OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "參照本機", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "挪威語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "荷蘭語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "波蘭語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "校對", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "點", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "葡萄牙語（巴西）", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "葡萄牙語（葡萄牙）", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "在編輯器標頭中顯示快速列印按鈕", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "文件將被列印在上次選擇的或預設的印表機上。", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "地區", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "羅馬尼亞語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "俄語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "全部啟用", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "啟用巨集時不通知", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Turn on screen reader support", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Default sheet direction", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "This setting will affect only the new sheets", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Left-to-right", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Right-to-left", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "斯洛伐克語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "斯洛文尼亞語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "停用全部", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "停用巨集時不通知", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "使用「儲存」按鈕同步您和其他人所做的更改", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "瑞典語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Use toolbar color as tabs background", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "土耳其文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "烏克蘭文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "使用 Alt 鍵使用鍵盤瀏覽使用者介面", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "使用 Option 鍵使用鍵盤瀏覽使用者介面", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "越南語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "顯示通知", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "停用巨集時通知", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "參照 Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "工作區", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "中文", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "警告", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "帶密碼", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "保護試算表", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "帶簽名", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "有效的簽名已添加到試算表中。試算表受到保護，無法進行編輯。", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "通過添加不可見的數位簽章來確保試算表的完整性", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "編輯試算表", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "編輯將會從試算表中移除簽名。&lt;br&gt;是否繼續？", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "此試算表已受密碼保護。", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "使用密碼加密試算表", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "此試算表需要簽名。", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "有效的簽名已添加到試算表中。試算表受到保護，無法進行編輯。", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "該試算表中的某些數位簽章無效或無法驗證。該試算表受到編輯保護。", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "檢視簽署", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "另存為", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "另存副本", "SSE.Views.FillSeriesDialog.textAuto": "AutoFill", "SSE.Views.FillSeriesDialog.textCols": "Columns", "SSE.Views.FillSeriesDialog.textDate": "Date", "SSE.Views.FillSeriesDialog.textDateUnit": "Date unit", "SSE.Views.FillSeriesDialog.textDay": "Day", "SSE.Views.FillSeriesDialog.textGrowth": "Growth", "SSE.Views.FillSeriesDialog.textLinear": "Linear", "SSE.Views.FillSeriesDialog.textMonth": "Month", "SSE.Views.FillSeriesDialog.textRows": "Rows", "SSE.Views.FillSeriesDialog.textSeries": "Series in", "SSE.Views.FillSeriesDialog.textStep": "Step value", "SSE.Views.FillSeriesDialog.textStop": "Stop value", "SSE.Views.FillSeriesDialog.textTitle": "Series", "SSE.Views.FillSeriesDialog.textTrend": "Trend", "SSE.Views.FillSeriesDialog.textType": "Type", "SSE.Views.FillSeriesDialog.textWeek": "Weekday", "SSE.Views.FillSeriesDialog.textYear": "Year", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FormatRulesEditDlg.fillColor": "填充顏色", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "警告", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 色比例", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 色比例", "SSE.Views.FormatRulesEditDlg.textAllBorders": "所有邊框", "SSE.Views.FormatRulesEditDlg.textAppearance": "條形外觀", "SSE.Views.FormatRulesEditDlg.textApply": "套用至範圍", "SSE.Views.FormatRulesEditDlg.textAutomatic": "自動", "SSE.Views.FormatRulesEditDlg.textAxis": "座標軸", "SSE.Views.FormatRulesEditDlg.textBarDirection": "線條方向", "SSE.Views.FormatRulesEditDlg.textBold": "粗體", "SSE.Views.FormatRulesEditDlg.textBorder": "邊框", "SSE.Views.FormatRulesEditDlg.textBordersColor": "邊框顔色", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "邊框樣式", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "底部邊框", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "無法新增條件格式設定。", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "儲存格中點", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "內部垂直邊框", "SSE.Views.FormatRulesEditDlg.textClear": "清除", "SSE.Views.FormatRulesEditDlg.textColor": "文字顏色", "SSE.Views.FormatRulesEditDlg.textContext": "上下文", "SSE.Views.FormatRulesEditDlg.textCustom": "自訂", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "向下對角邊框", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "對角向上邊框", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "輸入有效公式。", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "您輸入的公式無法求值為數字、日期、時間或字符串。", "SSE.Views.FormatRulesEditDlg.textEmptyText": "輸入數值。", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "您輸入的值不是有效的數字、日期、時間或字符串。", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "{0}的值必須大於{1}的值。", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "輸入{0}于{1}之間的數字。", "SSE.Views.FormatRulesEditDlg.textFill": "填入", "SSE.Views.FormatRulesEditDlg.textFormat": "格式", "SSE.Views.FormatRulesEditDlg.textFormula": "公式", "SSE.Views.FormatRulesEditDlg.textGradient": "漸層", "SSE.Views.FormatRulesEditDlg.textIconLabel": "當 {0} {1} 和", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "當 {0} {1}時", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "當值為", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "一個或多個圖標數據範圍重疊。&lt;br&gt;調整圖標數據範圍的值，使範圍不重疊。", "SSE.Views.FormatRulesEditDlg.textIconStyle": "圖示樣式", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "內部邊框", "SSE.Views.FormatRulesEditDlg.textInvalid": "無效的資料範圍。", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "錯誤！無效的儲存格範圍", "SSE.Views.FormatRulesEditDlg.textItalic": "斜體", "SSE.Views.FormatRulesEditDlg.textItem": "項目", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "從左到右", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "左邊框", "SSE.Views.FormatRulesEditDlg.textLongBar": "最長的條形", "SSE.Views.FormatRulesEditDlg.textMaximum": "最大值", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "最大點", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "內部水平邊框", "SSE.Views.FormatRulesEditDlg.textMidpoint": "中點", "SSE.Views.FormatRulesEditDlg.textMinimum": "最小值", "SSE.Views.FormatRulesEditDlg.textMinpoint": "最小點", "SSE.Views.FormatRulesEditDlg.textNegative": "負數", "SSE.Views.FormatRulesEditDlg.textNewColor": "更多顏色", "SSE.Views.FormatRulesEditDlg.textNoBorders": "無邊框", "SSE.Views.FormatRulesEditDlg.textNone": "無", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "一個或多個指定的值不是有效的百分比。", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "指定的{0}值不是有效的百分比。", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "一個或多個指定的值不是有效的百分比。", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "指定的{0}值不是有效的百分位數。", "SSE.Views.FormatRulesEditDlg.textOutBorders": "外部邊框", "SSE.Views.FormatRulesEditDlg.textPercent": "百分比", "SSE.Views.FormatRulesEditDlg.textPercentile": "百分位數", "SSE.Views.FormatRulesEditDlg.textPosition": "位置", "SSE.Views.FormatRulesEditDlg.textPositive": "正面", "SSE.Views.FormatRulesEditDlg.textPresets": "預設值", "SSE.Views.FormatRulesEditDlg.textPreview": "預覽", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "您無法在色彩比例尺、資料條和圖示集的條件格式設定中使用相對引用。", "SSE.Views.FormatRulesEditDlg.textReverse": "反轉圖示順序", "SSE.Views.FormatRulesEditDlg.textRight2Left": "從右到左", "SSE.Views.FormatRulesEditDlg.textRightBorders": "右邊框", "SSE.Views.FormatRulesEditDlg.textRule": "規則", "SSE.Views.FormatRulesEditDlg.textSameAs": "與正面相同", "SSE.Views.FormatRulesEditDlg.textSelectData": "選取資料", "SSE.Views.FormatRulesEditDlg.textShortBar": "最短的橫條", "SSE.Views.FormatRulesEditDlg.textShowBar": "僅顯示橫條", "SSE.Views.FormatRulesEditDlg.textShowIcon": "僅顯示圖示", "SSE.Views.FormatRulesEditDlg.textSingleRef": "條件格式化公式中無法使用此類參考。請將參考更改為單一儲存格，或者與工作表函數一起使用參考，例如 =SUM(A1:B5)。", "SSE.Views.FormatRulesEditDlg.textSolid": "實心", "SSE.Views.FormatRulesEditDlg.textStrikeout": "刪除線", "SSE.Views.FormatRulesEditDlg.textSubscript": "下標", "SSE.Views.FormatRulesEditDlg.textSuperscript": "上標", "SSE.Views.FormatRulesEditDlg.textTopBorders": "上框線", "SSE.Views.FormatRulesEditDlg.textUnderline": "下劃線", "SSE.Views.FormatRulesEditDlg.tipBorders": "邊框", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "數字格式", "SSE.Views.FormatRulesEditDlg.txtAccounting": "會計", "SSE.Views.FormatRulesEditDlg.txtCurrency": "貨幣", "SSE.Views.FormatRulesEditDlg.txtDate": "日期", "SSE.Views.FormatRulesEditDlg.txtDateLong": "長日期", "SSE.Views.FormatRulesEditDlg.txtDateShort": "短日期", "SSE.Views.FormatRulesEditDlg.txtEmpty": "此欄位為必填欄位", "SSE.Views.FormatRulesEditDlg.txtFraction": "分數", "SSE.Views.FormatRulesEditDlg.txtGeneral": "一般", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "無圖示", "SSE.Views.FormatRulesEditDlg.txtNumber": "數字", "SSE.Views.FormatRulesEditDlg.txtPercentage": "百分比", "SSE.Views.FormatRulesEditDlg.txtScientific": "科學計數法", "SSE.Views.FormatRulesEditDlg.txtText": "文字", "SSE.Views.FormatRulesEditDlg.txtTime": "時間", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "編輯格式規則", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "新增格式規則", "SSE.Views.FormatRulesManagerDlg.guestText": "訪客", "SSE.Views.FormatRulesManagerDlg.lockText": "已鎖定", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 個標準差高於平均值", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 個標準差低於平均值", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 個標準差以上的平均值", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 個標準差以下的平均值", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 個標準差以上的平均值", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 個標準差以下的平均值", "SSE.Views.FormatRulesManagerDlg.textAbove": "高於平均值", "SSE.Views.FormatRulesManagerDlg.textApply": "套用至", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "儲存格數值開頭為", "SSE.Views.FormatRulesManagerDlg.textBelow": "低於平均值", "SSE.Views.FormatRulesManagerDlg.textBetween": "介於 {0} 和 {1} 之間", "SSE.Views.FormatRulesManagerDlg.textCellValue": "儲存格數值", "SSE.Views.FormatRulesManagerDlg.textColorScale": "分級色階", "SSE.Views.FormatRulesManagerDlg.textContains": "儲存格數值包含", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "儲存格包含空白值", "SSE.Views.FormatRulesManagerDlg.textContainsError": "儲存格包含錯誤", "SSE.Views.FormatRulesManagerDlg.textDelete": "刪除", "SSE.Views.FormatRulesManagerDlg.textDown": "向下移動規則", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "重複值", "SSE.Views.FormatRulesManagerDlg.textEdit": "編輯", "SSE.Views.FormatRulesManagerDlg.textEnds": "儲存格數值結尾為", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "等於或高於平均值", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "等於或低於平均值", "SSE.Views.FormatRulesManagerDlg.textFormat": "格式", "SSE.Views.FormatRulesManagerDlg.textIconSet": "圖示集", "SSE.Views.FormatRulesManagerDlg.textNew": "新增", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "不在{0}與{1}之間", "SSE.Views.FormatRulesManagerDlg.textNotContains": "儲存格數值不包含", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "儲存格不包含空白值", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "儲存格不包含錯誤", "SSE.Views.FormatRulesManagerDlg.textRules": "規則", "SSE.Views.FormatRulesManagerDlg.textScope": "顯示格式設定為", "SSE.Views.FormatRulesManagerDlg.textSelectData": "選取資料", "SSE.Views.FormatRulesManagerDlg.textSelection": "目前的選取範圍", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "此樞紐表", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "此工作表", "SSE.Views.FormatRulesManagerDlg.textThisTable": "此表格", "SSE.Views.FormatRulesManagerDlg.textUnique": "唯一值", "SSE.Views.FormatRulesManagerDlg.textUp": "向上移動規則", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "該元素正在被其他使用者編輯。", "SSE.Views.FormatRulesManagerDlg.txtTitle": "條件格式設定", "SSE.Views.FormatSettingsDialog.textCategory": "類別", "SSE.Views.FormatSettingsDialog.textDecimal": "小數點", "SSE.Views.FormatSettingsDialog.textFormat": "格式", "SSE.Views.FormatSettingsDialog.textLinked": "鏈接到來源", "SSE.Views.FormatSettingsDialog.textSeparator": "使用1000個分隔符", "SSE.Views.FormatSettingsDialog.textSymbols": "符號", "SSE.Views.FormatSettingsDialog.textTitle": "數字格式", "SSE.Views.FormatSettingsDialog.txtAccounting": "計數", "SSE.Views.FormatSettingsDialog.txtAs10": "作為十分之五 (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "作為百分之五十 (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "作為十六分之八 (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "作為一半 (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "作為四分之二 (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "作為八分之四 (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "貨幣", "SSE.Views.FormatSettingsDialog.txtCustom": "自訂", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "請小心輸入自定數字格式。試算表編輯器不會檢查自定格式是否存在可能影響xlsx文件的錯誤。", "SSE.Views.FormatSettingsDialog.txtDate": "日期", "SSE.Views.FormatSettingsDialog.txtFraction": "分數", "SSE.Views.FormatSettingsDialog.txtGeneral": "一般", "SSE.Views.FormatSettingsDialog.txtNone": "無", "SSE.Views.FormatSettingsDialog.txtNumber": "數字", "SSE.Views.FormatSettingsDialog.txtPercentage": "百分比", "SSE.Views.FormatSettingsDialog.txtSample": "示例：", "SSE.Views.FormatSettingsDialog.txtScientific": "科學計數法", "SSE.Views.FormatSettingsDialog.txtText": "文字", "SSE.Views.FormatSettingsDialog.txtTime": "時間", "SSE.Views.FormatSettingsDialog.txtUpto1": "最多一位（1/3）", "SSE.Views.FormatSettingsDialog.txtUpto2": "最多兩位數（12/25）", "SSE.Views.FormatSettingsDialog.txtUpto3": "最多三位數（131/135）", "SSE.Views.FormulaDialog.sDescription": "描述", "SSE.Views.FormulaDialog.textGroupDescription": "選取函數群組", "SSE.Views.FormulaDialog.textListDescription": "選取函數", "SSE.Views.FormulaDialog.txtRecommended": "推薦", "SSE.Views.FormulaDialog.txtSearch": "搜尋", "SSE.Views.FormulaDialog.txtTitle": "插入函數", "SSE.Views.FormulaTab.capBtnRemoveArr": "Remove Arrows", "SSE.Views.FormulaTab.capBtnTraceDep": "Trace Dependents", "SSE.Views.FormulaTab.capBtnTracePrec": "Trace Precedents", "SSE.Views.FormulaTab.textAutomatic": "自動", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "計算當前工作表", "SSE.Views.FormulaTab.textCalculateWorkbook": "計算活頁簿", "SSE.Views.FormulaTab.textManual": "手動", "SSE.Views.FormulaTab.tipCalculate": "計算", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "計算整個活頁簿", "SSE.Views.FormulaTab.tipRemoveArr": "Remove the arrows drawn by Trace Precedents or Trace Dependents", "SSE.Views.FormulaTab.tipShowFormulas": "Display the formula in each cell instead of the resulting value", "SSE.Views.FormulaTab.tipTraceDep": "Show arrows that indicate which cells are affected by the value of the selected cell", "SSE.Views.FormulaTab.tipTracePrec": "Show arrows that indicate which cells affect the value of the selected cell", "SSE.Views.FormulaTab.tipWatch": "將儲存格新增至監視視窗清單", "SSE.Views.FormulaTab.txtAdditional": "額外", "SSE.Views.FormulaTab.txtAutosum": "自動加總", "SSE.Views.FormulaTab.txtAutosumTip": "總和", "SSE.Views.FormulaTab.txtCalculation": "計算", "SSE.Views.FormulaTab.txtFormula": "函數", "SSE.Views.FormulaTab.txtFormulaTip": "插入函數", "SSE.Views.FormulaTab.txtMore": "更多函數", "SSE.Views.FormulaTab.txtRecent": "最近使用", "SSE.Views.FormulaTab.txtRemDep": "Remove Dependents Arrows", "SSE.Views.FormulaTab.txtRemPrec": "Remove Precedents Arrows", "SSE.Views.FormulaTab.txtShowFormulas": "Show Formulas", "SSE.Views.FormulaTab.txtWatch": "監視視窗", "SSE.Views.FormulaWizard.textAny": "任何", "SSE.Views.FormulaWizard.textArgument": "參數", "SSE.Views.FormulaWizard.textFunction": "函數", "SSE.Views.FormulaWizard.textFunctionRes": "函數結果", "SSE.Views.FormulaWizard.textHelp": "此函數的說明", "SSE.Views.FormulaWizard.textLogical": "邏輯", "SSE.Views.FormulaWizard.textNoArgs": "此函數無參數。", "SSE.Views.FormulaWizard.textNoArgsDesc": "this argument has no description", "SSE.Views.FormulaWizard.textNumber": "數字", "SSE.Views.FormulaWizard.textReadMore": "Read more", "SSE.Views.FormulaWizard.textRef": "參考", "SSE.Views.FormulaWizard.textText": "文字", "SSE.Views.FormulaWizard.textTitle": "功能參數", "SSE.Views.FormulaWizard.textValue": "公式結果", "SSE.Views.GoalSeekDlg.textChangingCell": "By changing cell", "SSE.Views.GoalSeekDlg.textDataRangeError": "The formula is missing a range", "SSE.Views.GoalSeekDlg.textMustContainFormula": "The cell must contain a formula", "SSE.Views.GoalSeekDlg.textMustContainValue": "Cell must contain a value", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Formula in cell must result in a number", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Reference must be to a single cell", "SSE.Views.GoalSeekDlg.textSelectData": "Select data", "SSE.Views.GoalSeekDlg.textSetCell": "Set cell", "SSE.Views.GoalSeekDlg.textTitle": "Goal seek", "SSE.Views.GoalSeekDlg.textToValue": "To value", "SSE.Views.GoalSeekDlg.txtEmpty": "This field is required", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.GoalSeekStatusDlg.textContinue": "Continue", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Current value:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Goal seeking with cell {0} found a solution.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Goal seeking with cell {0} may not have found a solution.", "SSE.Views.GoalSeekStatusDlg.textPause": "Pause", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Goal seeking with cell {0} on iteration #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "Step", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Target value:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Goal seek status", "SSE.Views.HeaderFooterDialog.textAlign": "與頁邊對齊", "SSE.Views.HeaderFooterDialog.textAll": "所有頁面", "SSE.Views.HeaderFooterDialog.textBold": "粗體", "SSE.Views.HeaderFooterDialog.textCenter": "置中", "SSE.Views.HeaderFooterDialog.textColor": "文字顏色", "SSE.Views.HeaderFooterDialog.textDate": "日期", "SSE.Views.HeaderFooterDialog.textDiffFirst": "首頁不同", "SSE.Views.HeaderFooterDialog.textDiffOdd": "不同的奇數和偶數頁", "SSE.Views.HeaderFooterDialog.textEven": "偶數頁", "SSE.Views.HeaderFooterDialog.textFileName": "檔案名稱", "SSE.Views.HeaderFooterDialog.textFirst": "首頁", "SSE.Views.HeaderFooterDialog.textFooter": "頁尾", "SSE.Views.HeaderFooterDialog.textHeader": "頁首", "SSE.Views.HeaderFooterDialog.textImage": "Picture", "SSE.Views.HeaderFooterDialog.textInsert": "插入", "SSE.Views.HeaderFooterDialog.textItalic": "斜體", "SSE.Views.HeaderFooterDialog.textLeft": "左", "SSE.Views.HeaderFooterDialog.textMaxError": "您輸入的文本字符串太長。減少使用的字符數。", "SSE.Views.HeaderFooterDialog.textNewColor": "更多顏色", "SSE.Views.HeaderFooterDialog.textOdd": "奇數頁", "SSE.Views.HeaderFooterDialog.textPageCount": "頁數", "SSE.Views.HeaderFooterDialog.textPageNum": "頁碼", "SSE.Views.HeaderFooterDialog.textPresets": "預設值", "SSE.Views.HeaderFooterDialog.textRight": "右", "SSE.Views.HeaderFooterDialog.textScale": "與文件一起縮放", "SSE.Views.HeaderFooterDialog.textSheet": "工作表名稱", "SSE.Views.HeaderFooterDialog.textStrikeout": "刪除線", "SSE.Views.HeaderFooterDialog.textSubscript": "下標", "SSE.Views.HeaderFooterDialog.textSuperscript": "上標", "SSE.Views.HeaderFooterDialog.textTime": "時間", "SSE.Views.HeaderFooterDialog.textTitle": "頁頭/頁尾設定", "SSE.Views.HeaderFooterDialog.textUnderline": "下劃線", "SSE.Views.HeaderFooterDialog.tipFontName": "字型", "SSE.Views.HeaderFooterDialog.tipFontSize": "字型大小", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "顯示", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "連結到", "SSE.Views.HyperlinkSettingsDialog.strRange": "範圍", "SSE.Views.HyperlinkSettingsDialog.strSheet": "工作表", "SSE.Views.HyperlinkSettingsDialog.textCopy": "複製", "SSE.Views.HyperlinkSettingsDialog.textDefault": "選擇範圍", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "在此輸入標題", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "在此輸入連結", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "在此輸入工具提示", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "外部連結", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "取得連結", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "內部資料範圍", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "錯誤！無效的儲存格範圍", "SSE.Views.HyperlinkSettingsDialog.textNames": "已定義名稱", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "選取資料", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "SSE.Views.HyperlinkSettingsDialog.textSheets": "工作表", "SSE.Views.HyperlinkSettingsDialog.textTipText": "工具提示文字", "SSE.Views.HyperlinkSettingsDialog.textTitle": "超連結設定", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "此欄位為必填欄位", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "此欄位應為符合「http://www.example.com」格式的網址。", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "此欄位的限制為 2083 個字元。", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "SSE.Views.ImageSettings.strTransparency": "Opacity", "SSE.Views.ImageSettings.textAdvanced": "顯示進階設定", "SSE.Views.ImageSettings.textCrop": "裁剪", "SSE.Views.ImageSettings.textCropFill": "填入", "SSE.Views.ImageSettings.textCropFit": "適應大小", "SSE.Views.ImageSettings.textCropToShape": "裁剪為形狀", "SSE.Views.ImageSettings.textEdit": "編輯", "SSE.Views.ImageSettings.textEditObject": "編輯物件", "SSE.Views.ImageSettings.textFlip": "翻轉", "SSE.Views.ImageSettings.textFromFile": "從檔案插入", "SSE.Views.ImageSettings.textFromStorage": "從儲存位置插入", "SSE.Views.ImageSettings.textFromUrl": "從網址", "SSE.Views.ImageSettings.textHeight": "高度", "SSE.Views.ImageSettings.textHint270": "逆時針旋轉90°", "SSE.Views.ImageSettings.textHint90": "順時針旋轉90°", "SSE.Views.ImageSettings.textHintFlipH": "水平翻轉", "SSE.Views.ImageSettings.textHintFlipV": "垂直翻轉", "SSE.Views.ImageSettings.textInsert": "取代圖片", "SSE.Views.ImageSettings.textKeepRatio": "比例恆定", "SSE.Views.ImageSettings.textOriginalSize": "實際大小", "SSE.Views.ImageSettings.textRecentlyUsed": "最近使用", "SSE.Views.ImageSettings.textResetCrop": "Reset crop", "SSE.Views.ImageSettings.textRotate90": "旋轉90°", "SSE.Views.ImageSettings.textRotation": "旋轉", "SSE.Views.ImageSettings.textSize": "大小", "SSE.Views.ImageSettings.textWidth": "寬度", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "不隨儲存格移動或調整大小", "SSE.Views.ImageSettingsAdvanced.textAlt": "替代文字", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "描述", "SSE.Views.ImageSettingsAdvanced.textAltTip": "視覺物件的替代文字表示，將被朗讀給視力或認知障礙的人，以幫助他們更好地理解圖片、形狀、圖表或表格中的資訊。", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "標題", "SSE.Views.ImageSettingsAdvanced.textAngle": "角度", "SSE.Views.ImageSettingsAdvanced.textFlipped": "翻轉的", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "水平地", "SSE.Views.ImageSettingsAdvanced.textOneCell": "移動但不調整儲存格的大小", "SSE.Views.ImageSettingsAdvanced.textRotation": "旋轉", "SSE.Views.ImageSettingsAdvanced.textSnap": "儲存格對齊", "SSE.Views.ImageSettingsAdvanced.textTitle": "圖片-進階設定", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "隨儲存格一起移動和調整大小", "SSE.Views.ImageSettingsAdvanced.textVertically": "垂直地", "SSE.Views.ImportFromXmlDialog.textDestination": "選擇要放置資料的位置", "SSE.Views.ImportFromXmlDialog.textExist": "現有工作表", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "無效的儲存格範圍", "SSE.Views.ImportFromXmlDialog.textNew": "新工作表", "SSE.Views.ImportFromXmlDialog.textSelectData": "選取資料", "SSE.Views.ImportFromXmlDialog.textTitle": "匯入資料", "SSE.Views.ImportFromXmlDialog.txtEmpty": "此欄位為必填欄位", "SSE.Views.LeftMenu.ariaLeftMenu": "Left menu", "SSE.Views.LeftMenu.tipAbout": "關於", "SSE.Views.LeftMenu.tipChat": "聊天", "SSE.Views.LeftMenu.tipComments": "評論", "SSE.Views.LeftMenu.tipFile": "檔案", "SSE.Views.LeftMenu.tipPlugins": "外掛程式", "SSE.Views.LeftMenu.tipSearch": "搜尋", "SSE.Views.LeftMenu.tipSpellcheck": "拼字檢查", "SSE.Views.LeftMenu.tipSupport": "意見回饋與支援", "SSE.Views.LeftMenu.txtDeveloper": "開發人員模式", "SSE.Views.LeftMenu.txtEditor": "試算表編輯器", "SSE.Views.LeftMenu.txtLimit": "限制存取", "SSE.Views.LeftMenu.txtTrial": "試用模式", "SSE.Views.LeftMenu.txtTrialDev": "試用開發者模式", "SSE.Views.MacroDialog.textMacro": "巨集名稱", "SSE.Views.MacroDialog.textTitle": "指定巨集", "SSE.Views.MainSettingsPrint.okButtonText": "儲存", "SSE.Views.MainSettingsPrint.strBottom": "底部", "SSE.Views.MainSettingsPrint.strLandscape": "橫向方向", "SSE.Views.MainSettingsPrint.strLeft": "左", "SSE.Views.MainSettingsPrint.strMargins": "邊框", "SSE.Views.MainSettingsPrint.strPortrait": "直向方向", "SSE.Views.MainSettingsPrint.strPrint": "列印", "SSE.Views.MainSettingsPrint.strPrintTitles": "列印標題", "SSE.Views.MainSettingsPrint.strRight": "右", "SSE.Views.MainSettingsPrint.strTop": "頂部", "SSE.Views.MainSettingsPrint.textActualSize": "實際大小", "SSE.Views.MainSettingsPrint.textCustom": "自訂", "SSE.Views.MainSettingsPrint.textCustomOptions": "自訂選項", "SSE.Views.MainSettingsPrint.textFitCols": "將所有欄位調整為一頁", "SSE.Views.MainSettingsPrint.textFitPage": "將工作表調整為一頁", "SSE.Views.MainSettingsPrint.textFitRows": "將所有列調整為一頁", "SSE.Views.MainSettingsPrint.textPageOrientation": "頁面方向", "SSE.Views.MainSettingsPrint.textPageScaling": "縮放", "SSE.Views.MainSettingsPrint.textPageSize": "頁面大小", "SSE.Views.MainSettingsPrint.textPrintGrid": "列印網格線", "SSE.Views.MainSettingsPrint.textPrintHeadings": "列印列和欄標題", "SSE.Views.MainSettingsPrint.textRepeat": "重複...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "在左側重複列", "SSE.Views.MainSettingsPrint.textRepeatTop": "在頂部重複行", "SSE.Views.MainSettingsPrint.textSettings": "設定為", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "現有的命名範圍無法編輯，並且無法創建新的&lt;br&gt;因為其中一些正在進行編輯。", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "定義名稱", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "警告", "SSE.Views.NamedRangeEditDlg.strWorkbook": "工作簿", "SSE.Views.NamedRangeEditDlg.textDataRange": "資料範圍", "SSE.Views.NamedRangeEditDlg.textExistName": "錯誤！已存在具有該名稱的範圍", "SSE.Views.NamedRangeEditDlg.textInvalidName": "名稱必須以字母或底線開頭，並且不得包含無效字符。", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "錯誤！無效的儲存格範圍", "SSE.Views.NamedRangeEditDlg.textIsLocked": "錯誤！此元素正在被另一個使用者編輯", "SSE.Views.NamedRangeEditDlg.textName": "名稱", "SSE.Views.NamedRangeEditDlg.textReservedName": "您嘗試使用的名稱已在單元格公式中引用。請使用其他名稱。", "SSE.Views.NamedRangeEditDlg.textScope": "範圍", "SSE.Views.NamedRangeEditDlg.textSelectData": "選取資料", "SSE.Views.NamedRangeEditDlg.txtEmpty": "此欄位為必填欄位", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "編輯名稱", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "新增名稱", "SSE.Views.NamedRangePasteDlg.textNames": "已命名範圍", "SSE.Views.NamedRangePasteDlg.txtTitle": "貼上名稱", "SSE.Views.NameManagerDlg.closeButtonText": "關閉", "SSE.Views.NameManagerDlg.guestText": "訪客", "SSE.Views.NameManagerDlg.lockText": "已鎖定", "SSE.Views.NameManagerDlg.textDataRange": "資料範圍", "SSE.Views.NameManagerDlg.textDelete": "刪除", "SSE.Views.NameManagerDlg.textEdit": "編輯", "SSE.Views.NameManagerDlg.textEmpty": "尚未建立任何命名範圍。&lt;br&gt;建立至少一個命名範圍，它將顯示在此欄位中。", "SSE.Views.NameManagerDlg.textFilter": "篩選", "SSE.Views.NameManagerDlg.textFilterAll": "全部", "SSE.Views.NameManagerDlg.textFilterDefNames": "已定義名稱", "SSE.Views.NameManagerDlg.textFilterSheet": "工作表範圍的名稱", "SSE.Views.NameManagerDlg.textFilterTableNames": "表格名稱", "SSE.Views.NameManagerDlg.textFilterWorkbook": "工作簿範圍的名稱", "SSE.Views.NameManagerDlg.textNew": "新增", "SSE.Views.NameManagerDlg.textnoNames": "找不到與篩選條件匹配的命名範圍。", "SSE.Views.NameManagerDlg.textRanges": "已命名範圍", "SSE.Views.NameManagerDlg.textScope": "範圍", "SSE.Views.NameManagerDlg.textWorkbook": "工作簿", "SSE.Views.NameManagerDlg.tipIsLocked": "該元素正在被其他使用者編輯。", "SSE.Views.NameManagerDlg.txtTitle": "名稱管理員", "SSE.Views.NameManagerDlg.warnDelete": "您確定要刪除名稱 {0} 嗎？", "SSE.Views.PageMarginsDialog.textBottom": "底部", "SSE.Views.PageMarginsDialog.textCenter": "Center on page", "SSE.Views.PageMarginsDialog.textHor": "Horizontally", "SSE.Views.PageMarginsDialog.textLeft": "左", "SSE.Views.PageMarginsDialog.textRight": "右", "SSE.Views.PageMarginsDialog.textTitle": "邊框", "SSE.Views.PageMarginsDialog.textTop": "頂部", "SSE.Views.PageMarginsDialog.textVert": "Vertically", "SSE.Views.PageMarginsDialog.textWarning": "Warning", "SSE.Views.PageMarginsDialog.warnCheckMargings": "Margins are incorrect", "SSE.Views.ParagraphSettings.strLineHeight": "行高", "SSE.Views.ParagraphSettings.strParagraphSpacing": "段落間距", "SSE.Views.ParagraphSettings.strSpacingAfter": "之後", "SSE.Views.ParagraphSettings.strSpacingBefore": "之前", "SSE.Views.ParagraphSettings.textAdvanced": "顯示進階設定", "SSE.Views.ParagraphSettings.textAt": "在", "SSE.Views.ParagraphSettings.textAtLeast": "至少", "SSE.Views.ParagraphSettings.textAuto": "多個", "SSE.Views.ParagraphSettings.textExact": "確切地", "SSE.Views.ParagraphSettings.txtAutoText": "自動", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "指定的標籤將出現在這個欄位中。", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "全部大寫", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "雙刪除線", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "縮排", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "左", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "行距", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "右", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "之後", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "之前", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "特殊", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "依照", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "字型", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "縮排與間距", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "小型大寫", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "間距", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "刪除線", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "下標", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "上標", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "標籤", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "對齊方式", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "多個", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "字元間距", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "預設定位點", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "效果", "SSE.Views.ParagraphSettingsAdvanced.textExact": "確切地", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "首行縮排", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "懸掛", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "兩端對齊", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(無)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "移除", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "移除所有", "SSE.Views.ParagraphSettingsAdvanced.textSet": "指定", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "置中", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "左", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "制表位位置", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "右", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "段落-進階設定", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "自動", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Delete", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "Duplicate", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Edit", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Formula", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Items Name", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "New", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Calculated Items in", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "等於", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "不以...結尾", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "包含", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "不含", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "介於之間", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "不在之間", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "不等於", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "大於", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "大於或等於", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "小於", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "小於或等於", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "開頭為", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "不以...開頭", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "結尾為", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "顯示標籤為以下內容的項目：", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "顯示以下內容的項目：", "SSE.Views.PivotDigitalFilterDialog.textUse1": "使用 ? 代表任意單一字元", "SSE.Views.PivotDigitalFilterDialog.textUse2": "使用 * 代表任意序列的字元", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "和", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "標籤篩選", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "數值篩選", "SSE.Views.PivotGroupDialog.textAuto": "自動", "SSE.Views.PivotGroupDialog.textBy": "依照", "SSE.Views.PivotGroupDialog.textDays": "天", "SSE.Views.PivotGroupDialog.textEnd": "結束於", "SSE.Views.PivotGroupDialog.textError": "此欄位必須是數值。", "SSE.Views.PivotGroupDialog.textGreaterError": "結束數字必須大於開始數字", "SSE.Views.PivotGroupDialog.textHour": "小時", "SSE.Views.PivotGroupDialog.textMin": "分鐘", "SSE.Views.PivotGroupDialog.textMonth": "月份", "SSE.Views.PivotGroupDialog.textNumDays": "天數", "SSE.Views.PivotGroupDialog.textQuart": "季度", "SSE.Views.PivotGroupDialog.textSec": "秒數", "SSE.Views.PivotGroupDialog.textStart": "開始於", "SSE.Views.PivotGroupDialog.textYear": "年份", "SSE.Views.PivotGroupDialog.txtTitle": "分組", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "You can use Calculated Items for basic calculations between different items within a single field", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Insert into formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "<PERSON><PERSON>", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Item name", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Items", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Read more", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Insert Calculated Item in", "SSE.Views.PivotSettings.textAdvanced": "顯示進階設定", "SSE.Views.PivotSettings.textColumns": "欄", "SSE.Views.PivotSettings.textFields": "選取欄位", "SSE.Views.PivotSettings.textFilters": "過濾器", "SSE.Views.PivotSettings.textRows": "列", "SSE.Views.PivotSettings.textValues": "值", "SSE.Views.PivotSettings.txtAddColumn": "添加到欄", "SSE.Views.PivotSettings.txtAddFilter": "新增至篩選器", "SSE.Views.PivotSettings.txtAddRow": "新增至列", "SSE.Views.PivotSettings.txtAddValues": "新增至值", "SSE.Views.PivotSettings.txtFieldSettings": "欄位設定", "SSE.Views.PivotSettings.txtMoveBegin": "移至開頭", "SSE.Views.PivotSettings.txtMoveColumn": "移至欄位", "SSE.Views.PivotSettings.txtMoveDown": "向下移動", "SSE.Views.PivotSettings.txtMoveEnd": "移至結尾", "SSE.Views.PivotSettings.txtMoveFilter": "移至篩選", "SSE.Views.PivotSettings.txtMoveRow": "移至列", "SSE.Views.PivotSettings.txtMoveUp": "向上移動", "SSE.Views.PivotSettings.txtMoveValues": "移至數值", "SSE.Views.PivotSettings.txtRemove": "移除欄位", "SSE.Views.PivotSettingsAdvanced.strLayout": "名稱和版面配置", "SSE.Views.PivotSettingsAdvanced.textAlt": "替代文字", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "描述", "SSE.Views.PivotSettingsAdvanced.textAltTip": "視覺物件的替代文字表示，將向視力或認知障礙的人讀取，以幫助他們更好地理解圖片、形狀、圖表或表格中的資訊。", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "標題", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "更新時自動調整欄寬", "SSE.Views.PivotSettingsAdvanced.textDataRange": "資料範圍", "SSE.Views.PivotSettingsAdvanced.textDataSource": "資料來源", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "在報表篩選區域顯示欄位", "SSE.Views.PivotSettingsAdvanced.textDown": "向下，然後結束", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "總計", "SSE.Views.PivotSettingsAdvanced.textHeaders": "欄位標題", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "錯誤！無效的儲存格範圍", "SSE.Views.PivotSettingsAdvanced.textOver": "先橫後縱", "SSE.Views.PivotSettingsAdvanced.textSelectData": "選取資料", "SSE.Views.PivotSettingsAdvanced.textShowCols": "顯示欄", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "顯示列和欄的欄位標題", "SSE.Views.PivotSettingsAdvanced.textShowRows": "顯示列", "SSE.Views.PivotSettingsAdvanced.textTitle": "樞紐分析表 - 進階設定", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "按列的報表篩選欄位", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "按行的報表篩選欄位", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "此欄位為必填欄位", "SSE.Views.PivotSettingsAdvanced.txtName": "名稱", "SSE.Views.PivotShowDetailDialog.textDescription": "Choose the field containing the detail you want to show:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Show Detail", "SSE.Views.PivotTable.capBlankRows": "空白行", "SSE.Views.PivotTable.capGrandTotals": "總計", "SSE.Views.PivotTable.capLayout": "報表版面配置", "SSE.Views.PivotTable.capSubtotals": "小計", "SSE.Views.PivotTable.mniBottomSubtotals": "在群組底部顯示所有小計", "SSE.Views.PivotTable.mniInsertBlankLine": "在每個項目後插入空行", "SSE.Views.PivotTable.mniLayoutCompact": "以緊湊形式顯示", "SSE.Views.PivotTable.mniLayoutNoRepeat": "不重複所有項目標籤", "SSE.Views.PivotTable.mniLayoutOutline": "以大綱形式顯示", "SSE.Views.PivotTable.mniLayoutRepeat": "重複所有項目標籤", "SSE.Views.PivotTable.mniLayoutTabular": "以表格形式顯示", "SSE.Views.PivotTable.mniNoSubtotals": "不顯示小計", "SSE.Views.PivotTable.mniOffTotals": "關閉列和欄", "SSE.Views.PivotTable.mniOnColumnsTotals": "僅開啟列", "SSE.Views.PivotTable.mniOnRowsTotals": "僅開啟列", "SSE.Views.PivotTable.mniOnTotals": "開啟列和欄", "SSE.Views.PivotTable.mniRemoveBlankLine": "移除每個項目後的空白行", "SSE.Views.PivotTable.mniTopSubtotals": "在群組頂部顯示所有小計", "SSE.Views.PivotTable.textColBanded": "交替列", "SSE.Views.PivotTable.textColHeader": "欄標題", "SSE.Views.PivotTable.textRowBanded": "交替行", "SSE.Views.PivotTable.textRowHeader": "列標題", "SSE.Views.PivotTable.tipCalculatedItems": "Calculated items", "SSE.Views.PivotTable.tipCreatePivot": "插入樞紐分析表", "SSE.Views.PivotTable.tipGrandTotals": "顯示或隱藏總計", "SSE.Views.PivotTable.tipRefresh": "從資料來源更新資訊", "SSE.Views.PivotTable.tipRefreshCurrent": "從資料來源更新目前表格的資訊", "SSE.Views.PivotTable.tipSelect": "選擇整個樞紐分析表", "SSE.Views.PivotTable.tipSubtotals": "顯示或隱藏小計", "SSE.Views.PivotTable.txtCalculatedItems": "Calculated Items", "SSE.Views.PivotTable.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.PivotTable.txtCreate": "插入表格", "SSE.Views.PivotTable.txtExpandEntire": "Expand Entire Field", "SSE.Views.PivotTable.txtGroupPivot_Custom": "自訂", "SSE.Views.PivotTable.txtGroupPivot_Dark": "深色", "SSE.Views.PivotTable.txtGroupPivot_Light": "淺色", "SSE.Views.PivotTable.txtGroupPivot_Medium": "中等", "SSE.Views.PivotTable.txtPivotTable": "樞紐分析表", "SSE.Views.PivotTable.txtRefresh": "刷新", "SSE.Views.PivotTable.txtRefreshAll": "全部刷新", "SSE.Views.PivotTable.txtSelect": "選擇", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "樞紐分析表樣式 (深色)", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "樞紐分析表樣式 (亮色)", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "樞紐分析表樣式 (中等)", "SSE.Views.PrintSettings.btnDownload": "保存並下載", "SSE.Views.PrintSettings.btnExport": "Save & Export", "SSE.Views.PrintSettings.btnPrint": "保存並列印", "SSE.Views.PrintSettings.strBottom": "底部", "SSE.Views.PrintSettings.strLandscape": "橫向", "SSE.Views.PrintSettings.strLeft": "左", "SSE.Views.PrintSettings.strMargins": "邊框", "SSE.Views.PrintSettings.strPortrait": "直向方向", "SSE.Views.PrintSettings.strPrint": "列印", "SSE.Views.PrintSettings.strPrintTitles": "列印標題", "SSE.Views.PrintSettings.strRight": "右", "SSE.Views.PrintSettings.strShow": "顯示", "SSE.Views.PrintSettings.strTop": "頂部", "SSE.Views.PrintSettings.textActiveSheets": "使用中的工作表", "SSE.Views.PrintSettings.textActualSize": "實際大小", "SSE.Views.PrintSettings.textAllSheets": "所有工作表", "SSE.Views.PrintSettings.textCurrentSheet": "目前工作表", "SSE.Views.PrintSettings.textCustom": "自訂", "SSE.Views.PrintSettings.textCustomOptions": "自訂選項", "SSE.Views.PrintSettings.textFitCols": "將所有欄位調整為一頁", "SSE.Views.PrintSettings.textFitPage": "將工作表調整為一頁", "SSE.Views.PrintSettings.textFitRows": "將所有列調整為一頁", "SSE.Views.PrintSettings.textHideDetails": "隱藏詳細資料", "SSE.Views.PrintSettings.textIgnore": "忽略列印​​區域", "SSE.Views.PrintSettings.textLayout": "版面配置", "SSE.Views.PrintSettings.textMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintSettings.textMarginsNormal": "Normal", "SSE.Views.PrintSettings.textMarginsWide": "Wide", "SSE.Views.PrintSettings.textPageOrientation": "頁面方向", "SSE.Views.PrintSettings.textPages": "頁數：", "SSE.Views.PrintSettings.textPageScaling": "縮放", "SSE.Views.PrintSettings.textPageSize": "頁面大小", "SSE.Views.PrintSettings.textPrintGrid": "列印格線", "SSE.Views.PrintSettings.textPrintHeadings": "列印列和欄標題", "SSE.Views.PrintSettings.textPrintRange": "列印範圍", "SSE.Views.PrintSettings.textRange": "範圍", "SSE.Views.PrintSettings.textRepeat": "重複...", "SSE.Views.PrintSettings.textRepeatLeft": "在左側重複欄", "SSE.Views.PrintSettings.textRepeatTop": "在頂部重複行", "SSE.Views.PrintSettings.textSelection": "選擇", "SSE.Views.PrintSettings.textSettings": "工作表設定", "SSE.Views.PrintSettings.textShowDetails": "顯示詳細資料", "SSE.Views.PrintSettings.textShowGrid": "顯示格線", "SSE.Views.PrintSettings.textShowHeadings": "顯示列和欄標題", "SSE.Views.PrintSettings.textTitle": "列印面", "SSE.Views.PrintSettings.textTitlePDF": "PDF 設定", "SSE.Views.PrintSettings.textTo": "至", "SSE.Views.PrintSettings.txtMarginsLast": "Last Custom", "SSE.Views.PrintTitlesDialog.textFirstCol": "第一欄", "SSE.Views.PrintTitlesDialog.textFirstRow": "第一列", "SSE.Views.PrintTitlesDialog.textFrozenCols": "凍結的欄", "SSE.Views.PrintTitlesDialog.textFrozenRows": "凍結的列", "SSE.Views.PrintTitlesDialog.textInvalidRange": "錯誤！無效的儲存格範圍", "SSE.Views.PrintTitlesDialog.textLeft": "在左側重複欄", "SSE.Views.PrintTitlesDialog.textNoRepeat": "不要重複", "SSE.Views.PrintTitlesDialog.textRepeat": "重複...", "SSE.Views.PrintTitlesDialog.textSelectRange": "選擇範圍", "SSE.Views.PrintTitlesDialog.textTitle": "列印標題", "SSE.Views.PrintTitlesDialog.textTop": "在頂部重複行", "SSE.Views.PrintWithPreview.txtActiveSheets": "使用中的工作表", "SSE.Views.PrintWithPreview.txtActualSize": "實際大小", "SSE.Views.PrintWithPreview.txtAllSheets": "所有工作表", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "套用至所有工作表", "SSE.Views.PrintWithPreview.txtBothSides": "雙面列印", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "沿長邊翻轉頁面", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "沿短邊翻轉頁面", "SSE.Views.PrintWithPreview.txtBottom": "底部", "SSE.Views.PrintWithPreview.txtCopies": "副本", "SSE.Views.PrintWithPreview.txtCurrentSheet": "目前工作表", "SSE.Views.PrintWithPreview.txtCustom": "自訂", "SSE.Views.PrintWithPreview.txtCustomOptions": "自訂選項", "SSE.Views.PrintWithPreview.txtEmptyTable": "由於表格是空的，因此沒有內容可供打印", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "第一頁編號：", "SSE.Views.PrintWithPreview.txtFitCols": "將所有欄位調整為一頁", "SSE.Views.PrintWithPreview.txtFitPage": "將工作表調整為一頁", "SSE.Views.PrintWithPreview.txtFitRows": "將所有列調整為一頁", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "網格線和標題", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "頁頭/頁尾設定", "SSE.Views.PrintWithPreview.txtIgnore": "忽略列印範圍", "SSE.Views.PrintWithPreview.txtLandscape": "橫向", "SSE.Views.PrintWithPreview.txtLeft": "左", "SSE.Views.PrintWithPreview.txtMargins": "邊框", "SSE.Views.PrintWithPreview.txtMarginsLast": "Last Custom", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Normal", "SSE.Views.PrintWithPreview.txtMarginsWide": "Wide", "SSE.Views.PrintWithPreview.txtOf": "共 {0} 頁", "SSE.Views.PrintWithPreview.txtOneSide": "單面列印", "SSE.Views.PrintWithPreview.txtOneSideDesc": "僅在頁面的一側進行列印", "SSE.Views.PrintWithPreview.txtPage": "頁面", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "頁碼無效", "SSE.Views.PrintWithPreview.txtPageOrientation": "頁面方向", "SSE.Views.PrintWithPreview.txtPages": "頁數：", "SSE.Views.PrintWithPreview.txtPageSize": "頁面大小", "SSE.Views.PrintWithPreview.txtPortrait": "直向方向", "SSE.Views.PrintWithPreview.txtPrint": "列印", "SSE.Views.PrintWithPreview.txtPrintGrid": "列印格線", "SSE.Views.PrintWithPreview.txtPrintHeadings": "列印列和欄標題", "SSE.Views.PrintWithPreview.txtPrintRange": "列印範圍", "SSE.Views.PrintWithPreview.txtPrintSides": "列印面", "SSE.Views.PrintWithPreview.txtPrintTitles": "列印標題", "SSE.Views.PrintWithPreview.txtPrintToPDF": "列印為 PDF", "SSE.Views.PrintWithPreview.txtRepeat": "重複...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "在左側重複列", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "在頂部重複行", "SSE.Views.PrintWithPreview.txtRight": "右", "SSE.Views.PrintWithPreview.txtSave": "存檔", "SSE.Views.PrintWithPreview.txtScaling": "縮放", "SSE.Views.PrintWithPreview.txtSelection": "選擇", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "工作表設定", "SSE.Views.PrintWithPreview.txtSheet": "工作表：{0}", "SSE.Views.PrintWithPreview.txtTo": "至", "SSE.Views.PrintWithPreview.txtTop": "頂部", "SSE.Views.ProtectDialog.textExistName": "錯誤！已存在具有該名稱的範圍", "SSE.Views.ProtectDialog.textInvalidName": "範圍標題必須以字母開頭，只能包含字母、數字和空格。", "SSE.Views.ProtectDialog.textInvalidRange": "錯誤！無效的儲存格範圍", "SSE.Views.ProtectDialog.textSelectData": "選取資料", "SSE.Views.ProtectDialog.txtAllow": "允許此工作表的所有使用者", "SSE.Views.ProtectDialog.txtAllowDescription": "您可以解鎖特定範圍以進行編輯。", "SSE.Views.ProtectDialog.txtAllowRanges": "允許編輯範圍", "SSE.Views.ProtectDialog.txtAutofilter": "使用自動篩選", "SSE.Views.ProtectDialog.txtDelCols": "刪除欄", "SSE.Views.ProtectDialog.txtDelRows": "刪除列", "SSE.Views.ProtectDialog.txtEmpty": "此欄位為必填欄位", "SSE.Views.ProtectDialog.txtFormatCells": "格式化儲存格", "SSE.Views.ProtectDialog.txtFormatCols": "格式化欄", "SSE.Views.ProtectDialog.txtFormatRows": "格式化列", "SSE.Views.ProtectDialog.txtIncorrectPwd": "確認密碼不相同", "SSE.Views.ProtectDialog.txtInsCols": "插入欄位", "SSE.Views.ProtectDialog.txtInsHyper": "插入超連結", "SSE.Views.ProtectDialog.txtInsRows": "插入列", "SSE.Views.ProtectDialog.txtObjs": "編輯物件", "SSE.Views.ProtectDialog.txtOptional": "可選的", "SSE.Views.ProtectDialog.txtPassword": "密碼", "SSE.Views.ProtectDialog.txtPivot": "使用樞紐分析表和樞紐圖", "SSE.Views.ProtectDialog.txtProtect": "保護", "SSE.Views.ProtectDialog.txtRange": "範圍", "SSE.Views.ProtectDialog.txtRangeName": "標題", "SSE.Views.ProtectDialog.txtRepeat": "重複輸入密碼", "SSE.Views.ProtectDialog.txtScen": "編輯方案", "SSE.Views.ProtectDialog.txtSelLocked": "選取鎖定儲存格", "SSE.Views.ProtectDialog.txtSelUnLocked": "選擇未鎖定儲存格", "SSE.Views.ProtectDialog.txtSheetDescription": "通過限制他人的編輯權限，防止他人進行不必要的更改。", "SSE.Views.ProtectDialog.txtSheetTitle": "保護工作表", "SSE.Views.ProtectDialog.txtSort": "排序", "SSE.Views.ProtectDialog.txtWarning": "警告：如果您遺失或忘記密碼，將無法恢復。請將密碼保存在安全的地方。", "SSE.Views.ProtectDialog.txtWBDescription": "要防止其他使用者查看隱藏的工作表、添加、移動、刪除或隱藏工作表以及重新命名工作表，您可以使用密碼保護工作簿的結構。", "SSE.Views.ProtectDialog.txtWBTitle": "保護工作簿結構", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "匿名", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Anyone", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Edit", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Denied", "SSE.Views.ProtectedRangesEditDlg.textCanView": "View", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "範圍標題必須以字母開頭，只能包含字母、數字和空格。", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "錯誤！無效的儲存格範圍", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Remove", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "選取資料", "SSE.Views.ProtectedRangesEditDlg.textYou": "您", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Access to range", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "此欄位為必填欄位", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "保護", "SSE.Views.ProtectedRangesEditDlg.txtRange": "範圍", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "標題", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "只有您可以編輯此範圍", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "開始鍵入姓名或電子郵件", "SSE.Views.ProtectedRangesManagerDlg.guestText": "訪客", "SSE.Views.ProtectedRangesManagerDlg.lockText": "已鎖定", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "刪除", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "編輯", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "尚未建立任何受保護範圍。&lt;br&gt;建立至少一個受保護範圍，它將顯示在此欄位中。", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "篩選", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "全部", "SSE.Views.ProtectedRangesManagerDlg.textNew": "新增", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "保護工作表", "SSE.Views.ProtectedRangesManagerDlg.textRange": "範圍", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "您可以限制特定人員對編輯範圍的編輯權限。", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "標題", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "該元素正在被其他使用者編輯。", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Access", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Denied", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "編輯", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "編輯範圍", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "新增範圍", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "受保護範圍", "SSE.Views.ProtectedRangesManagerDlg.txtView": "檢視", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "您確定要刪除受保護的範圍 {0} 嗎？&lt;br&gt;對試算表具有編輯權限的任何人都可以編輯範圍內的內容。", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "您確定要刪除受保護的範圍嗎？&lt;br&gt;對試算表具有編輯權限的任何人都可以編輯這些範圍的內容。", "SSE.Views.ProtectRangesDlg.guestText": "訪客", "SSE.Views.ProtectRangesDlg.lockText": "已鎖定", "SSE.Views.ProtectRangesDlg.textDelete": "刪除", "SSE.Views.ProtectRangesDlg.textEdit": "編輯", "SSE.Views.ProtectRangesDlg.textEmpty": "不允許編輯範圍。", "SSE.Views.ProtectRangesDlg.textNew": "新增", "SSE.Views.ProtectRangesDlg.textProtect": "保護工作表", "SSE.Views.ProtectRangesDlg.textPwd": "密碼", "SSE.Views.ProtectRangesDlg.textRange": "範圍", "SSE.Views.ProtectRangesDlg.textRangesDesc": "當工作表受到保護時，通過密碼解鎖的範圍（僅適用於鎖定的儲存格）", "SSE.Views.ProtectRangesDlg.textTitle": "標題", "SSE.Views.ProtectRangesDlg.tipIsLocked": "該元素正在被其他使用者編輯。", "SSE.Views.ProtectRangesDlg.txtEditRange": "編輯範圍", "SSE.Views.ProtectRangesDlg.txtNewRange": "新增範圍", "SSE.Views.ProtectRangesDlg.txtNo": "否", "SSE.Views.ProtectRangesDlg.txtTitle": "允許使用者編輯範圍", "SSE.Views.ProtectRangesDlg.txtYes": "是", "SSE.Views.ProtectRangesDlg.warnDelete": "您確定要刪除名稱 {0} 嗎？", "SSE.Views.RemoveDuplicatesDialog.textColumns": "欄", "SSE.Views.RemoveDuplicatesDialog.textDescription": "要刪除重複的值，請選擇包含重複值的一個或多個欄位。", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "我的資料有標題", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "全選", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "移除重複項目", "SSE.Views.RightMenu.ariaRightMenu": "Right menu", "SSE.Views.RightMenu.txtCellSettings": "儲存格設定", "SSE.Views.RightMenu.txtChartSettings": "圖表設定", "SSE.Views.RightMenu.txtImageSettings": "圖片設定", "SSE.Views.RightMenu.txtParagraphSettings": "段落設定", "SSE.Views.RightMenu.txtPivotSettings": "樞紐分析表設定", "SSE.Views.RightMenu.txtSettings": "常用設定", "SSE.Views.RightMenu.txtShapeSettings": "形狀設定", "SSE.Views.RightMenu.txtSignatureSettings": "簽名設定", "SSE.Views.RightMenu.txtSlicerSettings": "切片器設置", "SSE.Views.RightMenu.txtSparklineSettings": "迷你圖設定", "SSE.Views.RightMenu.txtTableSettings": "表格設定", "SSE.Views.RightMenu.txtTextArtSettings": "文字藝術設定", "SSE.Views.ScaleDialog.textAuto": "自動", "SSE.Views.ScaleDialog.textError": "輸入的值不正確。", "SSE.Views.ScaleDialog.textFewPages": "頁數", "SSE.Views.ScaleDialog.textFitTo": "調整至", "SSE.Views.ScaleDialog.textHeight": "高度", "SSE.Views.ScaleDialog.textManyPages": "頁數", "SSE.Views.ScaleDialog.textOnePage": "頁面", "SSE.Views.ScaleDialog.textScaleTo": "縮放到", "SSE.Views.ScaleDialog.textTitle": "縮放設定", "SSE.Views.ScaleDialog.textWidth": "寬度", "SSE.Views.SetValueDialog.txtMaxText": "此字段的最大值為{0}", "SSE.Views.SetValueDialog.txtMinText": "此字段的最小值為{0}", "SSE.Views.ShapeSettings.strBackground": "背景顏色", "SSE.Views.ShapeSettings.strChange": "變更形狀", "SSE.Views.ShapeSettings.strColor": "顏色", "SSE.Views.ShapeSettings.strFill": "填入", "SSE.Views.ShapeSettings.strForeground": "前景顏色", "SSE.Views.ShapeSettings.strPattern": "圖案", "SSE.Views.ShapeSettings.strShadow": "顯示陰影", "SSE.Views.ShapeSettings.strSize": "大小", "SSE.Views.ShapeSettings.strStroke": "折線圖", "SSE.Views.ShapeSettings.strTransparency": "透明度", "SSE.Views.ShapeSettings.strType": "類型", "SSE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "SSE.Views.ShapeSettings.textAdvanced": "顯示進階設定", "SSE.Views.ShapeSettings.textAngle": "角度", "SSE.Views.ShapeSettings.textBorderSizeErr": "輸入的值不正確。&lt;br&gt;請輸入介於0pt和1584pt之間的值。", "SSE.Views.ShapeSettings.textColor": "填充顏色", "SSE.Views.ShapeSettings.textDirection": "方向", "SSE.Views.ShapeSettings.textEditPoints": "Edit points", "SSE.Views.ShapeSettings.textEditShape": "Edit shape", "SSE.Views.ShapeSettings.textEmptyPattern": "無圖案", "SSE.Views.ShapeSettings.textEyedropper": "Eyedropper", "SSE.Views.ShapeSettings.textFlip": "翻轉", "SSE.Views.ShapeSettings.textFromFile": "從檔案插入", "SSE.Views.ShapeSettings.textFromStorage": "從儲存位置插入", "SSE.Views.ShapeSettings.textFromUrl": "從網址", "SSE.Views.ShapeSettings.textGradient": "漸層點", "SSE.Views.ShapeSettings.textGradientFill": "漸層填充", "SSE.Views.ShapeSettings.textHint270": "逆時針旋轉90°", "SSE.Views.ShapeSettings.textHint90": "順時針旋轉90°", "SSE.Views.ShapeSettings.textHintFlipH": "水平翻轉", "SSE.Views.ShapeSettings.textHintFlipV": "垂直翻轉", "SSE.Views.ShapeSettings.textImageTexture": "圖片或紋理", "SSE.Views.ShapeSettings.textLinear": "線性的", "SSE.Views.ShapeSettings.textMoreColors": "More colors", "SSE.Views.ShapeSettings.textNoFill": "無填滿", "SSE.Views.ShapeSettings.textNoShadow": "No Shadow", "SSE.Views.ShapeSettings.textOriginalSize": "原始大小", "SSE.Views.ShapeSettings.textPatternFill": "圖案", "SSE.Views.ShapeSettings.textPosition": "位置", "SSE.Views.ShapeSettings.textRadial": "放射狀", "SSE.Views.ShapeSettings.textRecentlyUsed": "最近使用", "SSE.Views.ShapeSettings.textRotate90": "旋轉90°", "SSE.Views.ShapeSettings.textRotation": "旋轉", "SSE.Views.ShapeSettings.textSelectImage": "選擇圖片", "SSE.Views.ShapeSettings.textSelectTexture": "選擇", "SSE.Views.ShapeSettings.textShadow": "Shadow", "SSE.Views.ShapeSettings.textStretch": "延伸", "SSE.Views.ShapeSettings.textStyle": "樣式", "SSE.Views.ShapeSettings.textTexture": "從紋理", "SSE.Views.ShapeSettings.textTile": "磚瓦", "SSE.Views.ShapeSettings.tipAddGradientPoint": "添加漸變點", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "移除漸層點", "SSE.Views.ShapeSettings.txtBrownPaper": "棕色紙張", "SSE.Views.ShapeSettings.txtCanvas": "畫布", "SSE.Views.ShapeSettings.txtCarton": "紙盒", "SSE.Views.ShapeSettings.txtDarkFabric": "深色布料", "SSE.Views.ShapeSettings.txtGrain": "紋理", "SSE.Views.ShapeSettings.txtGranite": "花崗岩", "SSE.Views.ShapeSettings.txtGreyPaper": "灰色紙張", "SSE.Views.ShapeSettings.txtKnit": "編織", "SSE.Views.ShapeSettings.txtLeather": "皮革", "SSE.Views.ShapeSettings.txtNoBorders": "無線條", "SSE.Views.ShapeSettings.txtPapyrus": "帛書字體", "SSE.Views.ShapeSettings.txtWood": "木頭", "SSE.Views.ShapeSettingsAdvanced.strColumns": "欄", "SSE.Views.ShapeSettingsAdvanced.strMargins": "文字內邊距", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "不隨儲存格移動或調整大小", "SSE.Views.ShapeSettingsAdvanced.textAlt": "替代文字", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "描述", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "視覺物件的替代文字表示，將被朗讀給視力或認知障礙的人，以幫助他們更好地理解圖片、形狀、圖表或表格中的資訊。", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "標題", "SSE.Views.ShapeSettingsAdvanced.textAngle": "角度", "SSE.Views.ShapeSettingsAdvanced.textArrows": "箭頭", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "自動調整大小", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "起始大小", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "起始樣式", "SSE.Views.ShapeSettingsAdvanced.textBevel": "立體斜角", "SSE.Views.ShapeSettingsAdvanced.textBottom": "底部", "SSE.Views.ShapeSettingsAdvanced.textCapType": "大寫字元樣式", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "欄數", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "結束大小", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "結束樣式", "SSE.Views.ShapeSettingsAdvanced.textFlat": "平坦", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "翻轉的", "SSE.Views.ShapeSettingsAdvanced.textHeight": "高度", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "水平地", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "連接類型", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "比例恆定", "SSE.Views.ShapeSettingsAdvanced.textLeft": "左", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "行線樣式", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "移動但不調整儲存格的大小", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "允許文字超出形狀範圍", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "調整形狀以符合文字大小", "SSE.Views.ShapeSettingsAdvanced.textRight": "右", "SSE.Views.ShapeSettingsAdvanced.textRotation": "旋轉", "SSE.Views.ShapeSettingsAdvanced.textRound": "圓形", "SSE.Views.ShapeSettingsAdvanced.textSize": "大小", "SSE.Views.ShapeSettingsAdvanced.textSnap": "儲存格對齊", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "列間距", "SSE.Views.ShapeSettingsAdvanced.textSquare": "方形", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "文字方塊", "SSE.Views.ShapeSettingsAdvanced.textTitle": "形狀 - 進階設定", "SSE.Views.ShapeSettingsAdvanced.textTop": "頂部", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "隨儲存格一起移動和調整大小", "SSE.Views.ShapeSettingsAdvanced.textVertically": "垂直地", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "權重與箭頭", "SSE.Views.ShapeSettingsAdvanced.textWidth": "寬度", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "警告", "SSE.Views.SignatureSettings.strDelete": "移除簽名", "SSE.Views.SignatureSettings.strDetails": "簽名詳細資訊", "SSE.Views.SignatureSettings.strInvalid": "無效的簽名", "SSE.Views.SignatureSettings.strRequested": "要求的簽名", "SSE.Views.SignatureSettings.strSetup": "簽名設定", "SSE.Views.SignatureSettings.strSign": "簽名", "SSE.Views.SignatureSettings.strSignature": "簽名", "SSE.Views.SignatureSettings.strSigner": "簽署者", "SSE.Views.SignatureSettings.strValid": "有效簽名", "SSE.Views.SignatureSettings.txtContinueEditing": "無論如何編輯", "SSE.Views.SignatureSettings.txtEditWarning": "編輯將會從試算表中移除簽名。&lt;br&gt;是否繼續？", "SSE.Views.SignatureSettings.txtRemoveWarning": "您確定要移除此簽名嗎？&lt;br&gt;此操作無法復原。", "SSE.Views.SignatureSettings.txtRequestedSignatures": "此試算表需要簽名。", "SSE.Views.SignatureSettings.txtSigned": "有效的簽名已添加到試算表中。試算表受到保護，無法進行編輯。", "SSE.Views.SignatureSettings.txtSignedInvalid": "該試算表中的某些數位簽章無效或無法驗證。該試算表受到編輯保護。", "SSE.Views.SlicerAddDialog.textColumns": "欄", "SSE.Views.SlicerAddDialog.txtTitle": "插入切片器", "SSE.Views.SlicerSettings.strHideNoData": "隱藏無數據的項目", "SSE.Views.SlicerSettings.strIndNoData": "視覺上顯示沒有資料的項目", "SSE.Views.SlicerSettings.strShowDel": "顯示已從資料來源刪除的項目", "SSE.Views.SlicerSettings.strShowNoData": "顯示最後的沒有資料的項目", "SSE.Views.SlicerSettings.strSorting": "排序與篩選", "SSE.Views.SlicerSettings.textAdvanced": "顯示進階設定", "SSE.Views.SlicerSettings.textAsc": "遞增排序", "SSE.Views.SlicerSettings.textAZ": "A 到 Z", "SSE.Views.SlicerSettings.textButtons": "按鈕", "SSE.Views.SlicerSettings.textColumns": "欄", "SSE.Views.SlicerSettings.textDesc": "遞減排序", "SSE.Views.SlicerSettings.textHeight": "高度", "SSE.Views.SlicerSettings.textHor": "水平", "SSE.Views.SlicerSettings.textKeepRatio": "比例恆定", "SSE.Views.SlicerSettings.textLargeSmall": "由大到小排序", "SSE.Views.SlicerSettings.textLock": "禁用調整大小或移動", "SSE.Views.SlicerSettings.textNewOld": "最新到最舊", "SSE.Views.SlicerSettings.textOldNew": "最舊到最新", "SSE.Views.SlicerSettings.textPosition": "位置", "SSE.Views.SlicerSettings.textSize": "大小", "SSE.Views.SlicerSettings.textSmallLarge": "由小到大排序", "SSE.Views.SlicerSettings.textStyle": "樣式", "SSE.Views.SlicerSettings.textVert": "垂直", "SSE.Views.SlicerSettings.textWidth": "寬度", "SSE.Views.SlicerSettings.textZA": "從Z到A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "按鈕", "SSE.Views.SlicerSettingsAdvanced.strColumns": "欄", "SSE.Views.SlicerSettingsAdvanced.strHeight": "高度", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "隱藏無數據的項目", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "視覺上顯示沒有資料的項目", "SSE.Views.SlicerSettingsAdvanced.strReferences": "參考文獻", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "顯示已從資料來源刪除的項目", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "顯示標題", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "顯示最後的沒有資料的項目", "SSE.Views.SlicerSettingsAdvanced.strSize": "大小", "SSE.Views.SlicerSettingsAdvanced.strSorting": "排序與篩選", "SSE.Views.SlicerSettingsAdvanced.strStyle": "樣式", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "樣式和大小", "SSE.Views.SlicerSettingsAdvanced.strWidth": "寬度", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "不隨儲存格移動或調整大小", "SSE.Views.SlicerSettingsAdvanced.textAlt": "替代文字", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "描述", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "視覺物件的替代文字表示，將向視力或認知障礙的人讀取，以幫助他們更好地理解圖片、形狀、圖表或表格中的資訊。", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "標題", "SSE.Views.SlicerSettingsAdvanced.textAsc": "遞增排序", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A 到 Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "遞減排序", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "公式中使用的名稱", "SSE.Views.SlicerSettingsAdvanced.textHeader": "頁首", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "比例恆定", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "由大到小排序", "SSE.Views.SlicerSettingsAdvanced.textName": "名稱", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "最新到最舊", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "最舊到最新", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "移動但不調整儲存格的大小", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "由小到大排序", "SSE.Views.SlicerSettingsAdvanced.textSnap": "儲存格對齊", "SSE.Views.SlicerSettingsAdvanced.textSort": "排序", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "來源名稱", "SSE.Views.SlicerSettingsAdvanced.textTitle": "樞紐分析表篩選器 - 進階設定", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "隨儲存格一起移動和調整大小", "SSE.Views.SlicerSettingsAdvanced.textZA": "從Z到A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "此欄位為必填欄位", "SSE.Views.SortDialog.errorEmpty": "所有排序準則必須指定列或行。", "SSE.Views.SortDialog.errorMoreOneCol": "選擇多列。", "SSE.Views.SortDialog.errorMoreOneRow": "選擇多行。", "SSE.Views.SortDialog.errorNotOriginalCol": "您選擇的列不在原始選定範圍內。", "SSE.Views.SortDialog.errorNotOriginalRow": "您選擇的行不在原始選定範圍內。", "SSE.Views.SortDialog.errorSameColumnColor": "%1 正以相同的顏色進行多次排序。&lt;br&gt;刪除重複的排序條件，然後再試一次。", "SSE.Views.SortDialog.errorSameColumnValue": "%1 正以相同的顏色進行多次排序。&lt;br&gt;刪除重複的排序條件，然後再試一次。", "SSE.Views.SortDialog.textAsc": "升序", "SSE.Views.SortDialog.textAuto": "自動", "SSE.Views.SortDialog.textAZ": "A 到 Z", "SSE.Views.SortDialog.textBelow": "下方", "SSE.Views.SortDialog.textBtnCopy": "複製", "SSE.Views.SortDialog.textBtnDelete": "刪除", "SSE.Views.SortDialog.textBtnNew": "新增", "SSE.Views.SortDialog.textCellColor": "儲存格顏色", "SSE.Views.SortDialog.textColumn": "欄", "SSE.Views.SortDialog.textDesc": "遞減排序", "SSE.Views.SortDialog.textDown": "向下移動層級", "SSE.Views.SortDialog.textFontColor": "字型顏色", "SSE.Views.SortDialog.textLeft": "左", "SSE.Views.SortDialog.textLevels": "層級", "SSE.Views.SortDialog.textMoreCols": "(更多欄位...)", "SSE.Views.SortDialog.textMoreRows": "（更多列...）", "SSE.Views.SortDialog.textNone": "無", "SSE.Views.SortDialog.textOptions": "選項", "SSE.Views.SortDialog.textOrder": "排序", "SSE.Views.SortDialog.textRight": "右", "SSE.Views.SortDialog.textRow": "列", "SSE.Views.SortDialog.textSort": "排序", "SSE.Views.SortDialog.textSortBy": "排序方式", "SSE.Views.SortDialog.textThenBy": "然後按", "SSE.Views.SortDialog.textTop": "頂部", "SSE.Views.SortDialog.textUp": "向上移動層級", "SSE.Views.SortDialog.textValues": "值", "SSE.Views.SortDialog.textZA": "從Z到A", "SSE.Views.SortDialog.txtInvalidRange": "無效的儲存格範圍。", "SSE.Views.SortDialog.txtTitle": "排序", "SSE.Views.SortFilterDialog.textAsc": "按遞增排序 (A 到 Z) 依照", "SSE.Views.SortFilterDialog.textDesc": "遞減排序 (Z 到 A) 依照", "SSE.Views.SortFilterDialog.textNoSort": "No sort", "SSE.Views.SortFilterDialog.txtTitle": "排序", "SSE.Views.SortFilterDialog.txtTitleValue": "按值排序", "SSE.Views.SortOptionsDialog.textCase": "區分大小寫", "SSE.Views.SortOptionsDialog.textHeaders": "我的資料有標題", "SSE.Views.SortOptionsDialog.textLeftRight": "從左到右排序", "SSE.Views.SortOptionsDialog.textOrientation": "方向", "SSE.Views.SortOptionsDialog.textTitle": "排序選項", "SSE.Views.SortOptionsDialog.textTopBottom": "從上到下排序", "SSE.Views.SpecialPasteDialog.textAdd": "新增", "SSE.Views.SpecialPasteDialog.textAll": "全部", "SSE.Views.SpecialPasteDialog.textBlanks": "跳過空白", "SSE.Views.SpecialPasteDialog.textColWidth": "欄寬", "SSE.Views.SpecialPasteDialog.textComments": "評論", "SSE.Views.SpecialPasteDialog.textDiv": "除以", "SSE.Views.SpecialPasteDialog.textFFormat": "公式與格式", "SSE.Views.SpecialPasteDialog.textFNFormat": "公式與數字格式", "SSE.Views.SpecialPasteDialog.textFormats": "格式", "SSE.Views.SpecialPasteDialog.textFormulas": "公式", "SSE.Views.SpecialPasteDialog.textFWidth": "公式與欄寬", "SSE.Views.SpecialPasteDialog.textMult": "乘", "SSE.Views.SpecialPasteDialog.textNone": "無", "SSE.Views.SpecialPasteDialog.textOperation": "操作", "SSE.Views.SpecialPasteDialog.textPaste": "貼上", "SSE.Views.SpecialPasteDialog.textSub": "減去", "SSE.Views.SpecialPasteDialog.textTitle": "貼上特殊", "SSE.Views.SpecialPasteDialog.textTranspose": "轉置", "SSE.Views.SpecialPasteDialog.textValues": "值", "SSE.Views.SpecialPasteDialog.textVFormat": "值和格式", "SSE.Views.SpecialPasteDialog.textVNFormat": "值和數字格式", "SSE.Views.SpecialPasteDialog.textWBorders": "除了框線外的所有設定", "SSE.Views.Spellcheck.noSuggestions": "無拼寫建議", "SSE.Views.Spellcheck.textChange": "變更", "SSE.Views.Spellcheck.textChangeAll": "全部變更", "SSE.Views.Spellcheck.textIgnore": "忽略", "SSE.Views.Spellcheck.textIgnoreAll": "全部忽略", "SSE.Views.Spellcheck.txtAddToDictionary": "新增到字典", "SSE.Views.Spellcheck.txtClosePanel": "關閉拼字檢查", "SSE.Views.Spellcheck.txtComplete": "拼寫檢查已完成", "SSE.Views.Spellcheck.txtDictionaryLanguage": "字典語言", "SSE.Views.Spellcheck.txtNextTip": "前往下一個單字", "SSE.Views.Spellcheck.txtSpelling": "拼寫", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(移至最後)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "Create a copy", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(Create new spreadsheet)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "在工作表前移動", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Spreadsheet", "SSE.Views.Statusbar.filteredRecordsText": "已篩選 %1 個記錄中的 {0} 個", "SSE.Views.Statusbar.filteredText": "篩選模式", "SSE.Views.Statusbar.itemAverage": "平均", "SSE.Views.Statusbar.itemCount": "計數", "SSE.Views.Statusbar.itemDelete": "刪除", "SSE.Views.Statusbar.itemHidden": "隱藏", "SSE.Views.Statusbar.itemHide": "隱藏", "SSE.Views.Statusbar.itemInsert": "插入", "SSE.Views.Statusbar.itemMaximum": "最大值", "SSE.Views.Statusbar.itemMinimum": "最小值", "SSE.Views.Statusbar.itemMoveOrCopy": "Move or copy", "SSE.Views.Statusbar.itemProtect": "保護", "SSE.Views.Statusbar.itemRename": "重新命名", "SSE.Views.Statusbar.itemStatus": "保存狀態", "SSE.Views.Statusbar.itemSum": "求和", "SSE.Views.Statusbar.itemTabColor": "標籤顏色", "SSE.Views.Statusbar.itemUnProtect": "解除保護", "SSE.Views.Statusbar.RenameDialog.errNameExists": "具有相同名稱的工作表已存在。", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "工作表名稱不能包含以下字元：\\\\/*?[]: 或作為第一個或最後一個字元", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "工作表名稱", "SSE.Views.Statusbar.selectAllSheets": "選擇所有工作表", "SSE.Views.Statusbar.sheetIndexText": "第 {0} 頁，共 {1} 頁", "SSE.Views.Statusbar.textAverage": "平均值", "SSE.Views.Statusbar.textCount": "計數", "SSE.Views.Statusbar.textMax": "最大值", "SSE.Views.Statusbar.textMin": "最小值", "SSE.Views.Statusbar.textNewColor": "更多顏色", "SSE.Views.Statusbar.textNoColor": "沒有顏色", "SSE.Views.Statusbar.textSum": "求和", "SSE.Views.Statusbar.tipAddTab": "新增工作表", "SSE.Views.Statusbar.tipFirst": "捲動到第一個工作表", "SSE.Views.Statusbar.tipLast": "捲動到最後一個工作表", "SSE.Views.Statusbar.tipListOfSheets": "工作表列表", "SSE.Views.Statusbar.tipNext": "向右滾動工作表列表", "SSE.Views.Statusbar.tipPrev": "向左滾動工作表列表", "SSE.Views.Statusbar.tipZoomFactor": "縮放", "SSE.Views.Statusbar.tipZoomIn": "放大", "SSE.Views.Statusbar.tipZoomOut": "縮小", "SSE.Views.Statusbar.ungroupSheets": "取消合併工作表", "SSE.Views.Statusbar.zoomText": "縮放至{0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "由於選定的單元格範圍無法執行操作。&lt;br&gt;選擇一個與現有範圍不同的統一數據範圍，然後重試。", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "無法完成所選單元格範圍的操作。&lt;br&gt;選擇一個範圍，使第一個表格行與當前行重疊&lt;br&gt;並且結果表格與當前表格重疊。", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "無法完成所選單元格範圍的操作。&lt;br&gt;請選擇一個不包含其他表格的範圍。", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "表格不允許使用多儲存格陣列公式。", "SSE.Views.TableOptionsDialog.txtEmpty": "此欄位為必填欄位", "SSE.Views.TableOptionsDialog.txtFormat": "建立表格", "SSE.Views.TableOptionsDialog.txtInvalidRange": "錯誤！無效的儲存格範圍", "SSE.Views.TableOptionsDialog.txtNote": "標題必須保持在同一行，且結果表範圍必須重疊原始表範圍。", "SSE.Views.TableOptionsDialog.txtTitle": "標題", "SSE.Views.TableSettings.deleteColumnText": "刪除欄", "SSE.Views.TableSettings.deleteRowText": "刪除列", "SSE.Views.TableSettings.deleteTableText": "刪除表格", "SSE.Views.TableSettings.insertColumnLeftText": "在左側插入欄", "SSE.Views.TableSettings.insertColumnRightText": "在右側插入欄", "SSE.Views.TableSettings.insertRowAboveText": "在上方插入列", "SSE.Views.TableSettings.insertRowBelowText": "在下方插入列", "SSE.Views.TableSettings.notcriticalErrorTitle": "警告", "SSE.Views.TableSettings.selectColumnText": "選擇整欄", "SSE.Views.TableSettings.selectDataText": "選擇欄資料", "SSE.Views.TableSettings.selectRowText": "選擇列", "SSE.Views.TableSettings.selectTableText": "選擇表格", "SSE.Views.TableSettings.textActions": "表格操作", "SSE.Views.TableSettings.textAdvanced": "顯示進階設定", "SSE.Views.TableSettings.textBanded": "分隔", "SSE.Views.TableSettings.textColumns": "欄", "SSE.Views.TableSettings.textConvertRange": "轉換為範圍", "SSE.Views.TableSettings.textEdit": "列與欄", "SSE.Views.TableSettings.textEmptyTemplate": "沒有範本", "SSE.Views.TableSettings.textExistName": "錯誤！已存在具有該名稱的範圍", "SSE.Views.TableSettings.textFilter": "篩選按鈕", "SSE.Views.TableSettings.textFirst": "第一個", "SSE.Views.TableSettings.textHeader": "頁首", "SSE.Views.TableSettings.textInvalidName": "錯誤！無效的表格名稱", "SSE.Views.TableSettings.textIsLocked": "該元素正在被其他使用者編輯。", "SSE.Views.TableSettings.textLast": "最後", "SSE.Views.TableSettings.textLongOperation": "運行時間長", "SSE.Views.TableSettings.textPivot": "插入樞紐分析表", "SSE.Views.TableSettings.textRemDuplicates": "移除重複項目", "SSE.Views.TableSettings.textReservedName": "您嘗試使用的名稱已在單元格公式中引用。請使用其他名稱。", "SSE.Views.TableSettings.textResize": "調整表格大小", "SSE.Views.TableSettings.textRows": "列", "SSE.Views.TableSettings.textSelectData": "選取資料", "SSE.Views.TableSettings.textSlicer": "插入切片器", "SSE.Views.TableSettings.textTableName": "表格名稱", "SSE.Views.TableSettings.textTemplate": "從範本中選擇", "SSE.Views.TableSettings.textTotal": "總計", "SSE.Views.TableSettings.txtGroupTable_Custom": "自訂", "SSE.Views.TableSettings.txtGroupTable_Dark": "深色", "SSE.Views.TableSettings.txtGroupTable_Light": "淺色", "SSE.Views.TableSettings.txtGroupTable_Medium": "中等", "SSE.Views.TableSettings.txtTable_TableStyleDark": "深色表格樣式", "SSE.Views.TableSettings.txtTable_TableStyleLight": "亮色表格樣式", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "中等表格樣式", "SSE.Views.TableSettings.warnLongOperation": "您即將執行的操作可能需要較長時間才能完成。確定要繼續嗎？", "SSE.Views.TableSettingsAdvanced.textAlt": "替代文字", "SSE.Views.TableSettingsAdvanced.textAltDescription": "描述", "SSE.Views.TableSettingsAdvanced.textAltTip": "視覺物件的替代文字表示，將向視力或認知障礙的人讀取，以幫助他們更好地理解圖片、形狀、圖表或表格中的資訊。", "SSE.Views.TableSettingsAdvanced.textAltTitle": "標題", "SSE.Views.TableSettingsAdvanced.textTitle": "表格 - 進階設定", "SSE.Views.TextArtSettings.strBackground": "背景顏色", "SSE.Views.TextArtSettings.strColor": "顏色", "SSE.Views.TextArtSettings.strFill": "填入", "SSE.Views.TextArtSettings.strForeground": "前景顏色", "SSE.Views.TextArtSettings.strPattern": "圖案", "SSE.Views.TextArtSettings.strSize": "大小", "SSE.Views.TextArtSettings.strStroke": "折線圖", "SSE.Views.TextArtSettings.strTransparency": "透明度", "SSE.Views.TextArtSettings.strType": "類型", "SSE.Views.TextArtSettings.textAngle": "角度", "SSE.Views.TextArtSettings.textBorderSizeErr": "輸入的值不正確。&lt;br&gt;請輸入介於0pt和1584pt之間的值。", "SSE.Views.TextArtSettings.textColor": "填充顏色", "SSE.Views.TextArtSettings.textDirection": "方向", "SSE.Views.TextArtSettings.textEmptyPattern": "無圖案", "SSE.Views.TextArtSettings.textFromFile": "從檔案插入", "SSE.Views.TextArtSettings.textFromUrl": "從網址", "SSE.Views.TextArtSettings.textGradient": "漸層點", "SSE.Views.TextArtSettings.textGradientFill": "漸層填充", "SSE.Views.TextArtSettings.textImageTexture": "圖片或紋理", "SSE.Views.TextArtSettings.textLinear": "線性的", "SSE.Views.TextArtSettings.textNoFill": "無填滿", "SSE.Views.TextArtSettings.textPatternFill": "圖案", "SSE.Views.TextArtSettings.textPosition": "位置", "SSE.Views.TextArtSettings.textRadial": "放射狀", "SSE.Views.TextArtSettings.textSelectTexture": "選擇", "SSE.Views.TextArtSettings.textStretch": "延伸", "SSE.Views.TextArtSettings.textStyle": "樣式", "SSE.Views.TextArtSettings.textTemplate": "範本", "SSE.Views.TextArtSettings.textTexture": "從紋理", "SSE.Views.TextArtSettings.textTile": "磚瓦", "SSE.Views.TextArtSettings.textTransform": "轉變", "SSE.Views.TextArtSettings.tipAddGradientPoint": "添加漸變點", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "移除漸層點", "SSE.Views.TextArtSettings.txtBrownPaper": "棕色紙張", "SSE.Views.TextArtSettings.txtCanvas": "畫布", "SSE.Views.TextArtSettings.txtCarton": "紙盒", "SSE.Views.TextArtSettings.txtDarkFabric": "深色布料", "SSE.Views.TextArtSettings.txtGrain": "紋理", "SSE.Views.TextArtSettings.txtGranite": "花崗岩", "SSE.Views.TextArtSettings.txtGreyPaper": "灰色紙張", "SSE.Views.TextArtSettings.txtKnit": "編織", "SSE.Views.TextArtSettings.txtLeather": "皮革", "SSE.Views.TextArtSettings.txtNoBorders": "無線條", "SSE.Views.TextArtSettings.txtPapyrus": "帛書字體", "SSE.Views.TextArtSettings.txtWood": "木頭", "SSE.Views.Toolbar.capBtnAddComment": "新增註解", "SSE.Views.Toolbar.capBtnColorSchemas": "色彩配置", "SSE.Views.Toolbar.capBtnComment": "評論", "SSE.Views.Toolbar.capBtnInsHeader": "頁首和頁尾", "SSE.Views.Toolbar.capBtnInsSlicer": "切片器", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "符號", "SSE.Views.Toolbar.capBtnMargins": "邊框", "SSE.Views.Toolbar.capBtnPageBreak": "Breaks", "SSE.Views.Toolbar.capBtnPageOrient": "方向", "SSE.Views.Toolbar.capBtnPageSize": "大小", "SSE.Views.Toolbar.capBtnPrintArea": "列印範圍", "SSE.Views.Toolbar.capBtnPrintTitles": "列印標題", "SSE.Views.Toolbar.capBtnScale": "縮放以適合", "SSE.Views.Toolbar.capImgAlign": "對齊", "SSE.Views.Toolbar.capImgBackward": "向後發送", "SSE.Views.Toolbar.capImgForward": "向前移動", "SSE.Views.Toolbar.capImgGroup": "分組", "SSE.Views.Toolbar.capInsertChart": "圖表", "SSE.Views.Toolbar.capInsertChartRecommend": "Recommended Chart", "SSE.Views.Toolbar.capInsertEquation": "方程式", "SSE.Views.Toolbar.capInsertHyperlink": "超連結", "SSE.Views.Toolbar.capInsertImage": "圖像", "SSE.Views.Toolbar.capInsertShape": "形狀", "SSE.Views.Toolbar.capInsertSpark": "迷你圖", "SSE.Views.Toolbar.capInsertTable": "表格", "SSE.Views.Toolbar.capInsertText": "文字方塊", "SSE.Views.Toolbar.capInsertTextart": "文字藝術", "SSE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniCapitalizeWords": "每個單詞首字母大寫", "SSE.Views.Toolbar.mniImageFromFile": "從檔案插入圖片", "SSE.Views.Toolbar.mniImageFromStorage": "從儲存空間插入圖片", "SSE.Views.Toolbar.mniImageFromUrl": "從網址插入圖片", "SSE.Views.Toolbar.mniLowerCase": "小寫", "SSE.Views.Toolbar.mniSentenceCase": "句首大寫", "SSE.Views.Toolbar.mniToggleCase": "切換大小寫", "SSE.Views.Toolbar.mniUpperCase": "大寫", "SSE.Views.Toolbar.textAddPrintArea": "新增至列印範圍", "SSE.Views.Toolbar.textAlignBottom": "對齊底部", "SSE.Views.Toolbar.textAlignCenter": "置中對齊", "SSE.Views.Toolbar.textAlignJust": "兩端對齊", "SSE.Views.Toolbar.textAlignLeft": "靠左對齊", "SSE.Views.Toolbar.textAlignMiddle": "置於中央對齊", "SSE.Views.Toolbar.textAlignRight": "靠右對齊", "SSE.Views.Toolbar.textAlignTop": "靠上對齊", "SSE.Views.Toolbar.textAllBorders": "所有邊框", "SSE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "SSE.Views.Toolbar.textAuto": "自動", "SSE.Views.Toolbar.textAutoColor": "自動", "SSE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "SSE.Views.Toolbar.textBlackHeart": "Black Heart Suit", "SSE.Views.Toolbar.textBold": "粗體", "SSE.Views.Toolbar.textBordersColor": "邊框色彩", "SSE.Views.Toolbar.textBordersStyle": "邊框樣式", "SSE.Views.Toolbar.textBottom": "底部：", "SSE.Views.Toolbar.textBottomBorders": "底部邊框", "SSE.Views.Toolbar.textBullet": "Bullet", "SSE.Views.Toolbar.textCellAlign": "Format cell alignment", "SSE.Views.Toolbar.textCenterBorders": "內部垂直邊框", "SSE.Views.Toolbar.textClearPrintArea": "清除列印範圍", "SSE.Views.Toolbar.textClearRule": "清除規則", "SSE.Views.Toolbar.textClockwise": "順時針角度", "SSE.Views.Toolbar.textColorScales": "色階", "SSE.Views.Toolbar.textCopyright": "Copyright Sign", "SSE.Views.Toolbar.textCounterCw": "逆時針角度", "SSE.Views.Toolbar.textCustom": "自訂", "SSE.Views.Toolbar.textDataBars": "資料條", "SSE.Views.Toolbar.textDegree": "Degree Sign", "SSE.Views.Toolbar.textDelLeft": "向左移動儲存格", "SSE.Views.Toolbar.textDelPageBreak": "Remove page break", "SSE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "SSE.Views.Toolbar.textDelUp": "上移儲存格", "SSE.Views.Toolbar.textDiagDownBorder": "向下對角邊框", "SSE.Views.Toolbar.textDiagUpBorder": "對角上邊界", "SSE.Views.Toolbar.textDivision": "Division Sign", "SSE.Views.Toolbar.textDollar": "Dollar Sign", "SSE.Views.Toolbar.textDone": "完成", "SSE.Views.Toolbar.textDown": "Down", "SSE.Views.Toolbar.textEditVA": "編輯可見區域", "SSE.Views.Toolbar.textEntireCol": "整個欄位", "SSE.Views.Toolbar.textEntireRow": "整個列", "SSE.Views.Toolbar.textEuro": "Euro Sign", "SSE.Views.Toolbar.textFewPages": "頁數", "SSE.Views.Toolbar.textFillLeft": "Left", "SSE.Views.Toolbar.textFillRight": "Right", "SSE.Views.Toolbar.textFormatCellFill": "Format cell fill", "SSE.Views.Toolbar.textGreaterEqual": "Greater Than Or Equal To", "SSE.Views.Toolbar.textHeight": "高度", "SSE.Views.Toolbar.textHideVA": "隱藏可見區域", "SSE.Views.Toolbar.textHorizontal": "水平文字", "SSE.Views.Toolbar.textInfinity": "Infinity", "SSE.Views.Toolbar.textInsDown": "向下移動儲存格", "SSE.Views.Toolbar.textInsideBorders": "內部邊框", "SSE.Views.Toolbar.textInsPageBreak": "Insert page break", "SSE.Views.Toolbar.textInsRight": "向右移動儲存格", "SSE.Views.Toolbar.textItalic": "斜體", "SSE.Views.Toolbar.textItems": "項目", "SSE.Views.Toolbar.textLandscape": "橫向", "SSE.Views.Toolbar.textLeft": "左：", "SSE.Views.Toolbar.textLeftBorders": "左邊框", "SSE.Views.Toolbar.textLessEqual": "Less Than Or Equal To", "SSE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "SSE.Views.Toolbar.textManageRule": "管理規則", "SSE.Views.Toolbar.textManyPages": "頁數", "SSE.Views.Toolbar.textMarginsLast": "最後自訂", "SSE.Views.Toolbar.textMarginsNarrow": "窄", "SSE.Views.Toolbar.textMarginsNormal": "一般", "SSE.Views.Toolbar.textMarginsWide": "寬", "SSE.Views.Toolbar.textMiddleBorders": "內部水平邊框", "SSE.Views.Toolbar.textMoreBorders": "More borders", "SSE.Views.Toolbar.textMoreFormats": "更多格式", "SSE.Views.Toolbar.textMorePages": "更多頁面", "SSE.Views.Toolbar.textMoreSymbols": "More symbols", "SSE.Views.Toolbar.textNewColor": "更多顏色", "SSE.Views.Toolbar.textNewRule": "新增規則", "SSE.Views.Toolbar.textNoBorders": "無邊框", "SSE.Views.Toolbar.textNotEqualTo": "Not Equal To", "SSE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "SSE.Views.Toolbar.textOnePage": "頁面", "SSE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "SSE.Views.Toolbar.textOutBorders": "外部邊框", "SSE.Views.Toolbar.textPageMarginsCustom": "自訂邊界", "SSE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "SSE.Views.Toolbar.textPortrait": "直向方向", "SSE.Views.Toolbar.textPrint": "列印", "SSE.Views.Toolbar.textPrintGridlines": "列印網格線", "SSE.Views.Toolbar.textPrintHeadings": "列印標題", "SSE.Views.Toolbar.textPrintOptions": "列印面", "SSE.Views.Toolbar.textRegistered": "Registered Sign", "SSE.Views.Toolbar.textResetPageBreak": "Reset all page breaks", "SSE.Views.Toolbar.textRight": "右: ", "SSE.Views.Toolbar.textRightBorders": "右邊框", "SSE.Views.Toolbar.textRotateDown": "向下旋轉文字", "SSE.Views.Toolbar.textRotateUp": "向上旋轉文字", "SSE.Views.Toolbar.textRtlSheet": "Sheet Right-to-left", "SSE.Views.Toolbar.textScale": "縮放", "SSE.Views.Toolbar.textScaleCustom": "自訂", "SSE.Views.Toolbar.textSection": "Section Sign", "SSE.Views.Toolbar.textSelection": "從目前的選取範圍", "SSE.Views.Toolbar.textSeries": "Series", "SSE.Views.Toolbar.textSetPrintArea": "設定列印範圍", "SSE.Views.Toolbar.textShapesCombine": "Combine", "SSE.Views.Toolbar.textShapesFragment": "Fragment", "SSE.Views.Toolbar.textShapesIntersect": "Intersect", "SSE.Views.Toolbar.textShapesSubstract": "Subtract", "SSE.Views.Toolbar.textShapesUnion": "Union", "SSE.Views.Toolbar.textShowVA": "顯示可見範圍", "SSE.Views.Toolbar.textSmile": "White Smiling Face", "SSE.Views.Toolbar.textSquareRoot": "Square Root", "SSE.Views.Toolbar.textStrikeout": "刪除線", "SSE.Views.Toolbar.textSubscript": "下標", "SSE.Views.Toolbar.textSubSuperscript": "下標/上標", "SSE.Views.Toolbar.textSuperscript": "上標", "SSE.Views.Toolbar.textTabCollaboration": "共同編輯", "SSE.Views.Toolbar.textTabData": "資料", "SSE.Views.Toolbar.textTabDraw": "繪製", "SSE.Views.Toolbar.textTabFile": "檔案", "SSE.Views.Toolbar.textTabFormula": "公式", "SSE.Views.Toolbar.textTabHome": "首頁", "SSE.Views.Toolbar.textTabInsert": "插入", "SSE.Views.Toolbar.textTabLayout": "版面配置", "SSE.Views.Toolbar.textTabProtect": "保護", "SSE.Views.Toolbar.textTabView": "檢視", "SSE.Views.Toolbar.textThisPivot": "從這個樞紐分析表", "SSE.Views.Toolbar.textThisSheet": "從這個工作表", "SSE.Views.Toolbar.textThisTable": "從這個表格", "SSE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTop": "上：", "SSE.Views.Toolbar.textTopBorders": "上框線", "SSE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "SSE.Views.Toolbar.textUnderline": "下劃線", "SSE.Views.Toolbar.textUp": "Up", "SSE.Views.Toolbar.textVertical": "垂直文字", "SSE.Views.Toolbar.textWidth": "寬度", "SSE.Views.Toolbar.textYen": "Yen Sign", "SSE.Views.Toolbar.textZoom": "縮放", "SSE.Views.Toolbar.tipAlignBottom": "對齊底部", "SSE.Views.Toolbar.tipAlignCenter": "置中對齊", "SSE.Views.Toolbar.tipAlignJust": "兩端對齊", "SSE.Views.Toolbar.tipAlignLeft": "靠左對齊", "SSE.Views.Toolbar.tipAlignMiddle": "置於中央對齊", "SSE.Views.Toolbar.tipAlignRight": "靠右對齊", "SSE.Views.Toolbar.tipAlignTop": "靠上對齊", "SSE.Views.Toolbar.tipAutofilter": "排序和過濾", "SSE.Views.Toolbar.tipBack": "返回", "SSE.Views.Toolbar.tipBorders": "邊框", "SSE.Views.Toolbar.tipCellStyle": "儲存格樣式", "SSE.Views.Toolbar.tipChangeCase": "大小寫轉換", "SSE.Views.Toolbar.tipChangeChart": "變更圖表類型", "SSE.Views.Toolbar.tipClearStyle": "清除", "SSE.Views.Toolbar.tipColorSchemas": "變更色彩配置", "SSE.Views.Toolbar.tipCondFormat": "條件格式設定", "SSE.Views.Toolbar.tipCopy": "複製", "SSE.Views.Toolbar.tipCopyStyle": "複製格式", "SSE.Views.Toolbar.tipCut": "剪下", "SSE.Views.Toolbar.tipDecDecimal": "減少小數位數", "SSE.Views.Toolbar.tipDecFont": "縮小文字", "SSE.Views.Toolbar.tipDeleteOpt": "刪除儲存格", "SSE.Views.Toolbar.tipDigStyleAccounting": "會計格式", "SSE.Views.Toolbar.tipDigStyleComma": "Comma style", "SSE.Views.Toolbar.tipDigStyleCurrency": "貨幣樣式", "SSE.Views.Toolbar.tipDigStylePercent": "百分比樣式", "SSE.Views.Toolbar.tipEditChart": "編輯圖表", "SSE.Views.Toolbar.tipEditChartData": "選取資料", "SSE.Views.Toolbar.tipEditChartType": "更改圖表類型", "SSE.Views.Toolbar.tipEditHeader": "編輯頁首或頁尾", "SSE.Views.Toolbar.tipFontColor": "字型顏色", "SSE.Views.Toolbar.tipFontName": "字型", "SSE.Views.Toolbar.tipFontSize": "字型大小", "SSE.Views.Toolbar.tipHAlighOle": "水平對齊", "SSE.Views.Toolbar.tipImgAlign": "對齊物件", "SSE.Views.Toolbar.tipImgGroup": "群組物件", "SSE.Views.Toolbar.tipIncDecimal": "增加小數位數", "SSE.Views.Toolbar.tipIncFont": "放大文字", "SSE.Views.Toolbar.tipInsertChart": "插入圖表", "SSE.Views.Toolbar.tipInsertChartRecommend": "Insert recommended chart", "SSE.Views.Toolbar.tipInsertChartSpark": "插入圖表", "SSE.Views.Toolbar.tipInsertEquation": "插入方程式", "SSE.Views.Toolbar.tipInsertHorizontalText": "插入水平文字方塊", "SSE.Views.Toolbar.tipInsertHyperlink": "新增超連結", "SSE.Views.Toolbar.tipInsertImage": "插入圖片", "SSE.Views.Toolbar.tipInsertOpt": "插入儲存格", "SSE.Views.Toolbar.tipInsertShape": "插入形狀", "SSE.Views.Toolbar.tipInsertSlicer": "插入切片器", "SSE.Views.Toolbar.tipInsertSmartArt": "插入SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "插入迷你圖", "SSE.Views.Toolbar.tipInsertSymbol": "插入符號", "SSE.Views.Toolbar.tipInsertTable": "插入表格", "SSE.Views.Toolbar.tipInsertText": "插入文字方塊", "SSE.Views.Toolbar.tipInsertTextart": "插入藝術文字", "SSE.Views.Toolbar.tipInsertVerticalText": "插入垂直文字方塊", "SSE.Views.Toolbar.tipMerge": "合併和居中", "SSE.Views.Toolbar.tipNone": "無", "SSE.Views.Toolbar.tipNumFormat": "數字格式", "SSE.Views.Toolbar.tipPageBreak": "Add a break where you want the next page to begin in the printed copy", "SSE.Views.Toolbar.tipPageMargins": "頁邊距", "SSE.Views.Toolbar.tipPageOrient": "頁面方向", "SSE.Views.Toolbar.tipPageSize": "頁面大小", "SSE.Views.Toolbar.tipPaste": "貼上", "SSE.Views.Toolbar.tipPrColor": "填充顏色", "SSE.Views.Toolbar.tipPrint": "列印", "SSE.Views.Toolbar.tipPrintArea": "列印範圍", "SSE.Views.Toolbar.tipPrintQuick": "快速列印", "SSE.Views.Toolbar.tipPrintTitles": "列印標題", "SSE.Views.Toolbar.tipRedo": "重做", "SSE.Views.Toolbar.tipReplace": "Replace", "SSE.Views.Toolbar.tipRtlSheet": "Switch the sheet direction so that the first column is on the right side", "SSE.Views.Toolbar.tipSave": "儲存", "SSE.Views.Toolbar.tipSaveCoauth": "儲存您的更改，以供其他使用者查看。", "SSE.Views.Toolbar.tipScale": "縮放以適合", "SSE.Views.Toolbar.tipSelectAll": "全選", "SSE.Views.Toolbar.tipSendBackward": "向後發送", "SSE.Views.Toolbar.tipSendForward": "向前移動", "SSE.Views.Toolbar.tipShapesMerge": "Merge shapes", "SSE.Views.Toolbar.tipSynchronize": "文件已被其他使用者更改。請按一下以保存您的更改並重新載入更新。", "SSE.Views.Toolbar.tipTextFormatting": "更多文字格式化工具", "SSE.Views.Toolbar.tipTextOrientation": "方向", "SSE.Views.Toolbar.tipUndo": "復原", "SSE.Views.Toolbar.tipVAlighOle": "垂直對齊", "SSE.Views.Toolbar.tipVisibleArea": "可見範圍", "SSE.Views.Toolbar.tipWrap": "換行文字", "SSE.Views.Toolbar.txtAccounting": "會計", "SSE.Views.Toolbar.txtAdditional": "額外", "SSE.Views.Toolbar.txtAscending": "遞增排序", "SSE.Views.Toolbar.txtAutosumTip": "求和", "SSE.Views.Toolbar.txtCellStyle": "儲存格樣式", "SSE.Views.Toolbar.txtClearAll": "全部", "SSE.Views.Toolbar.txtClearComments": "評論", "SSE.Views.Toolbar.txtClearFilter": "清除篩選", "SSE.Views.Toolbar.txtClearFormat": "格式", "SSE.Views.Toolbar.txtClearFormula": "函數", "SSE.Views.Toolbar.txtClearHyper": "超連結", "SSE.Views.Toolbar.txtClearText": "文字", "SSE.Views.Toolbar.txtCurrency": "貨幣", "SSE.Views.Toolbar.txtCustom": "自訂", "SSE.Views.Toolbar.txtDate": "日期", "SSE.Views.Toolbar.txtDateLong": "長日期", "SSE.Views.Toolbar.txtDateShort": "短日期", "SSE.Views.Toolbar.txtDateTime": "日期和時間", "SSE.Views.Toolbar.txtDescending": "遞減排序", "SSE.Views.Toolbar.txtDollar": "$美元", "SSE.Views.Toolbar.txtEuro": "\n€歐元", "SSE.Views.Toolbar.txtExp": "指數", "SSE.Views.Toolbar.txtFillNum": "Fill", "SSE.Views.Toolbar.txtFilter": "篩選", "SSE.Views.Toolbar.txtFormula": "插入函數", "SSE.Views.Toolbar.txtFraction": "分數", "SSE.Views.Toolbar.txtFranc": "CHF 瑞士法郎", "SSE.Views.Toolbar.txtGeneral": "一般", "SSE.Views.Toolbar.txtInteger": "整數", "SSE.Views.Toolbar.txtManageRange": "名稱管理員", "SSE.Views.Toolbar.txtMergeAcross": "橫跨合併", "SSE.Views.Toolbar.txtMergeCells": "合併儲存格", "SSE.Views.Toolbar.txtMergeCenter": "合併並置中", "SSE.Views.Toolbar.txtNamedRange": "已命名範圍", "SSE.Views.Toolbar.txtNewRange": "定義名稱", "SSE.Views.Toolbar.txtNoBorders": "無邊框", "SSE.Views.Toolbar.txtNumber": "數字", "SSE.Views.Toolbar.txtPasteRange": "貼上名稱", "SSE.Views.Toolbar.txtPercentage": "百分比", "SSE.Views.Toolbar.txtPound": "£英鎊", "SSE.Views.Toolbar.txtRouble": "₽盧布", "SSE.Views.Toolbar.txtScientific": "科學計數法", "SSE.Views.Toolbar.txtSearch": "搜尋", "SSE.Views.Toolbar.txtSort": "排序", "SSE.Views.Toolbar.txtSortAZ": "升序", "SSE.Views.Toolbar.txtSortZA": "由大到小排序", "SSE.Views.Toolbar.txtSpecial": "特殊", "SSE.Views.Toolbar.txtTableTemplate": "格式作為表格樣板", "SSE.Views.Toolbar.txtText": "文字", "SSE.Views.Toolbar.txtTime": "時間", "SSE.Views.Toolbar.txtUnmerge": "取消合併儲存格", "SSE.Views.Toolbar.txtYen": "¥日元", "SSE.Views.Top10FilterDialog.textType": "顯示", "SSE.Views.Top10FilterDialog.txtBottom": "底部", "SSE.Views.Top10FilterDialog.txtBy": "依照", "SSE.Views.Top10FilterDialog.txtItems": "項目", "SSE.Views.Top10FilterDialog.txtPercent": "百分比", "SSE.Views.Top10FilterDialog.txtSum": "求和", "SSE.Views.Top10FilterDialog.txtTitle": "前 10 個自動篩選", "SSE.Views.Top10FilterDialog.txtTop": "頂部", "SSE.Views.Top10FilterDialog.txtValueTitle": "前 10 過濾器", "SSE.Views.ValueFieldSettingsDialog.textNext": "(下一個)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(上一個)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "值欄位設定", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "平均值", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "基礎欄位", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "基礎項目", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 的 %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "計數", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "計算數字", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "自訂名稱", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "與...的區別", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "索引", "SSE.Views.ValueFieldSettingsDialog.txtMax": "最大值", "SSE.Views.ValueFieldSettingsDialog.txtMin": "最小值", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "不計算", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "% 的", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "% 差異", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "欄百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "總計的百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "父總計的百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "父欄位總計的百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "父列總計的百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "累計在 %1 內", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "列百分比", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "乘積", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "將最小值排名至最大值", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "將最大值排名至最小值", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "累計總計", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "顯示值為", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "來源名稱:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "標準差", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "標準差p", "SSE.Views.ValueFieldSettingsDialog.txtSum": "求和", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "根據值欄位進行摘要", "SSE.Views.ValueFieldSettingsDialog.txtVar": "變異數", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "總體變異數", "SSE.Views.ViewManagerDlg.closeButtonText": "關閉", "SSE.Views.ViewManagerDlg.guestText": "訪客", "SSE.Views.ViewManagerDlg.lockText": "已鎖定", "SSE.Views.ViewManagerDlg.textDelete": "刪除", "SSE.Views.ViewManagerDlg.textDuplicate": "重複", "SSE.Views.ViewManagerDlg.textEmpty": "尚未建立任何檢視。", "SSE.Views.ViewManagerDlg.textGoTo": "前往檢視", "SSE.Views.ViewManagerDlg.textLongName": "請輸入少於128個字元的名稱。", "SSE.Views.ViewManagerDlg.textNew": "新增", "SSE.Views.ViewManagerDlg.textRename": "重新命名", "SSE.Views.ViewManagerDlg.textRenameError": "檢視名稱不能為空。", "SSE.Views.ViewManagerDlg.textRenameLabel": "重命名視圖", "SSE.Views.ViewManagerDlg.textViews": "工作表視圖", "SSE.Views.ViewManagerDlg.tipIsLocked": "該元素正在被其他使用者編輯。", "SSE.Views.ViewManagerDlg.txtTitle": "工作表檢視管理員", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Are you sure you want to delete this sheet view?", "SSE.Views.ViewManagerDlg.warnDeleteView": "您正試圖刪除當前啟用的檢視 %1。&lt;br&gt;關閉此檢視並刪除它嗎？", "SSE.Views.ViewTab.capBtnFreeze": "凍結窗格", "SSE.Views.ViewTab.capBtnSheetView": "工作表檢視", "SSE.Views.ViewTab.textAlwaysShowToolbar": "始終顯示工具列", "SSE.Views.ViewTab.textClose": "關閉", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "合併工作表和狀態列", "SSE.Views.ViewTab.textCreate": "新增", "SSE.Views.ViewTab.textDefault": "預設", "SSE.Views.ViewTab.textFill": "Fill", "SSE.Views.ViewTab.textFormula": "公式欄", "SSE.Views.ViewTab.textFreezeCol": "凍結第一欄", "SSE.Views.ViewTab.textFreezeRow": "凍結頂端列", "SSE.Views.ViewTab.textGridlines": "網格線", "SSE.Views.ViewTab.textHeadings": "標題", "SSE.Views.ViewTab.textInterfaceTheme": "介面主題", "SSE.Views.ViewTab.textLeftMenu": "左側面板", "SSE.Views.ViewTab.textLine": "Line", "SSE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "檢視管理員", "SSE.Views.ViewTab.textRightMenu": "右側面板", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "顯示固定窗格陰影", "SSE.Views.ViewTab.textTabStyle": "Tab style", "SSE.Views.ViewTab.textUnFreeze": "取消凍結窗格", "SSE.Views.ViewTab.textZeros": "顯示零值", "SSE.Views.ViewTab.textZoom": "縮放", "SSE.Views.ViewTab.tipClose": "關閉工作表視圖", "SSE.Views.ViewTab.tipCreate": "建立工作表檢視", "SSE.Views.ViewTab.tipFreeze": "凍結窗格", "SSE.Views.ViewTab.tipInterfaceTheme": "介面主題", "SSE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipSheetView": "工作表檢視", "SSE.Views.ViewTab.tipViewNormal": "在普通視圖中查看文件", "SSE.Views.ViewTab.tipViewPageBreak": "查看列印時文件的分頁位置", "SSE.Views.ViewTab.txtViewNormal": "一般", "SSE.Views.ViewTab.txtViewPageBreak": "分頁預覽", "SSE.Views.WatchDialog.closeButtonText": "關閉", "SSE.Views.WatchDialog.textAdd": "新增監視", "SSE.Views.WatchDialog.textBook": "書籍", "SSE.Views.WatchDialog.textCell": "儲存格", "SSE.Views.WatchDialog.textDelete": "刪除監視", "SSE.Views.WatchDialog.textDeleteAll": "刪除全部", "SSE.Views.WatchDialog.textFormula": "函數", "SSE.Views.WatchDialog.textName": "名稱", "SSE.Views.WatchDialog.textSheet": "工作表", "SSE.Views.WatchDialog.textValue": "值", "SSE.Views.WatchDialog.txtTitle": "監視視窗", "SSE.Views.WBProtection.hintAllowRanges": "允許編輯範圍", "SSE.Views.WBProtection.hintProtectRange": "保護範圍", "SSE.Views.WBProtection.hintProtectSheet": "保護工作表", "SSE.Views.WBProtection.hintProtectWB": "保護工作簿", "SSE.Views.WBProtection.txtAllowRanges": "允許編輯範圍", "SSE.Views.WBProtection.txtHiddenFormula": "隱藏公式", "SSE.Views.WBProtection.txtLockedCell": "已鎖定的儲存格", "SSE.Views.WBProtection.txtLockedShape": "鎖定圖形", "SSE.Views.WBProtection.txtLockedText": "鎖定文件", "SSE.Views.WBProtection.txtProtectRange": "保護範圍", "SSE.Views.WBProtection.txtProtectSheet": "保護工作表", "SSE.Views.WBProtection.txtProtectWB": "保護工作簿", "SSE.Views.WBProtection.txtSheetUnlockDescription": "輸入密碼以解除工作表的保護", "SSE.Views.WBProtection.txtSheetUnlockTitle": "解除工作表保護", "SSE.Views.WBProtection.txtWBUnlockDescription": "輸入密碼以解除工作簿的保護", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}