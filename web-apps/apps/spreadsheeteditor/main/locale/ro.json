{"cancelButtonText": "Revocare", "Common.Controllers.Chat.notcriticalErrorTitle": "Avertisment", "Common.Controllers.Desktop.hintBtnHome": "Afișare fereastră principală", "Common.Controllers.Desktop.itemCreateFromTemplate": "<PERSON><PERSON>re model formular", "Common.Controllers.History.notcriticalErrorTitle": "Avertisment", "Common.Controllers.History.txtErrorLoadHistory": "Încărcarea istoricului a eșuat", "Common.Controllers.Plugins.helpUseMacros": "Găsiți butonul Macro aici", "Common.Controllers.Plugins.helpUseMacrosHeader": "Accesul la macrocomenzi a fost actualizat", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugin-urile au fost instalate cu succes. Puteți accesa toate plugin-urile de fundal aici.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> a fost instalat cu succes. Puteți accesa toate plugin-urile de fundal aici.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Executare plugin-uri instalate", "Common.Controllers.Plugins.textRunPlugin": "Executare plugin", "Common.define.chartData.textArea": "Zona", "Common.define.chartData.textAreaStacked": "Arie stivuită", "Common.define.chartData.textAreaStackedPer": "Arie stivuită 100%", "Common.define.chartData.textBar": "Bară", "Common.define.chartData.textBarNormal": "Coloană grupată", "Common.define.chartData.textBarNormal3d": "Coloană grupată 3D   ", "Common.define.chartData.textBarNormal3dPerspective": "Coloană 3D ", "Common.define.chartData.textBarStacked": "Coloană stivuită", "Common.define.chartData.textBarStacked3d": "Coloană stivuită 3D", "Common.define.chartData.textBarStackedPer": "Coloană stivuită 100%", "Common.define.chartData.textBarStackedPer3d": "Coloană stivuită 100% în 3D", "Common.define.chartData.textCharts": "Diagrame", "Common.define.chartData.textColumn": "Coloană", "Common.define.chartData.textColumnSpark": "Coloană", "Common.define.chartData.textCombo": "Diagrame Combo", "Common.define.chartData.textComboAreaBar": "Arie stivuită – coloană grupată", "Common.define.chartData.textComboBarLine": "Coloană grupată - coloană linie", "Common.define.chartData.textComboBarLineSecondary": "Coloană grupată - linie în axa secundară", "Common.define.chartData.textComboCustom": "Combinații particularizate", "Common.define.chartData.textDoughnut": "Diagramă inelară", "Common.define.chartData.textHBarNormal": "Bare grupate", "Common.define.chartData.textHBarNormal3d": "Bare grupate 3D   ", "Common.define.chartData.textHBarStacked": "Bare stivuite", "Common.define.chartData.textHBarStacked3d": "Bare stivuite 3D  ", "Common.define.chartData.textHBarStackedPer": "Bare stivuite 100%", "Common.define.chartData.textHBarStackedPer3d": "Bare stivuite 100% în 3D ", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "<PERSON><PERSON>    ", "Common.define.chartData.textLineMarker": "<PERSON>ie cu marcaje", "Common.define.chartData.textLineSpark": "<PERSON><PERSON>", "Common.define.chartData.textLineStacked": "<PERSON><PERSON>", "Common.define.chartData.textLineStackedMarker": "<PERSON><PERSON> stivuită cu marcaje", "Common.define.chartData.textLineStackedPer": "<PERSON><PERSON> sti<PERSON> 100%", "Common.define.chartData.textLineStackedPerMarker": "<PERSON>ie stivuită 100% cu marcaje  ", "Common.define.chartData.textPie": "Radială", "Common.define.chartData.textPie3d": "Diagramă radială 3D    ", "Common.define.chartData.textPoint": "XY (diagramă prin puncte)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Radar plin", "Common.define.chartData.textRadarMarker": "Radar cu marcaje", "Common.define.chartData.textScatter": "Diagramă prin puncte", "Common.define.chartData.textScatterLine": "Diagramă prin puncte cu linii drepte", "Common.define.chartData.textScatterLineMarker": "Diagramă prin puncte cu linii drepte și marcatori", "Common.define.chartData.textScatterSmooth": "Diagramă prin puncte cu linii netede", "Common.define.chartData.textScatterSmoothMarker": "Diagramă prin puncte cu linii netede și marcatori", "Common.define.chartData.textSparks": "Diagrame sparkline", "Common.define.chartData.textStock": "Bursiere", "Common.define.chartData.textSurface": "Suprafața", "Common.define.chartData.textWinLossSpark": "Câștig/pierdere", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Niciun format definit ", "Common.define.conditionalData.text1Above": "1 abatere standard deasupra", "Common.define.conditionalData.text1Below": "1 abatere standard dedesubt ", "Common.define.conditionalData.text2Above": "2 abateri standard deasupra", "Common.define.conditionalData.text2Below": "2 abateri standard dedesubt", "Common.define.conditionalData.text3Above": "3 abateri standard deasupra", "Common.define.conditionalData.text3Below": "3 abateri standard dedesubt", "Common.define.conditionalData.textAbove": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textAverage": "Me<PERSON>", "Common.define.conditionalData.textBegins": "<PERSON>nce<PERSON> cu", "Common.define.conditionalData.textBelow": "Dedesubt", "Common.define.conditionalData.textBetween": "Între", "Common.define.conditionalData.textBlank": "Necompletat", "Common.define.conditionalData.textBlanks": "Conțime celule goale", "Common.define.conditionalData.textBottom": "<PERSON><PERSON>", "Common.define.conditionalData.textContains": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textDataBar": "Bară de date", "Common.define.conditionalData.textDate": "Dată", "Common.define.conditionalData.textDuplicate": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textEnds": "Se termină cu", "Common.define.conditionalData.textEqAbove": "Egal cu sau deasupra", "Common.define.conditionalData.textEqBelow": "Egal cu sau sub", "Common.define.conditionalData.textEqual": "Egal cu", "Common.define.conditionalData.textError": "Eroare", "Common.define.conditionalData.textErrors": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textFormula": "Formula", "Common.define.conditionalData.textGreater": "<PERSON> mare decât", "Common.define.conditionalData.textGreaterEq": "<PERSON> mare sau egal", "Common.define.conditionalData.textIconSets": "Ansamble de icoane", "Common.define.conditionalData.textLast7days": "În ultimele 7 zile", "Common.define.conditionalData.textLastMonth": "Ultima lună", "Common.define.conditionalData.textLastWeek": "Săptămâna anterioară", "Common.define.conditionalData.textLess": "Mai mic decât", "Common.define.conditionalData.textLessEq": "Mai mic sau egal cu", "Common.define.conditionalData.textNextMonth": "<PERSON>", "Common.define.conditionalData.textNextWeek": "Săptămân<PERSON> u<PERSON>", "Common.define.conditionalData.textNotBetween": "nu între", "Common.define.conditionalData.textNotBlanks": "Nu conțime celule goale", "Common.define.conditionalData.textNotContains": "<PERSON><PERSON>", "Common.define.conditionalData.textNotEqual": "Nu este egal cu", "Common.define.conditionalData.textNotErrors": "<PERSON>u con<PERSON>ine erori", "Common.define.conditionalData.textText": "Text", "Common.define.conditionalData.textThisMonth": "<PERSON>", "Common.define.conditionalData.textThisWeek": "Această săptămână", "Common.define.conditionalData.textToday": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textTomorrow": "M<PERSON><PERSON>", "Common.define.conditionalData.textTop": "<PERSON><PERSON>", "Common.define.conditionalData.textUnique": "Unice", "Common.define.conditionalData.textValue": "Valoarea este", "Common.define.conditionalData.textYesterday": "<PERSON><PERSON>", "Common.define.smartArt.textAccentedPicture": "Imagine cu accent", "Common.define.smartArt.textAccentProcess": "Proces accent", "Common.define.smartArt.textAlternatingFlow": "Flux alternativ", "Common.define.smartArt.textAlternatingHexagons": "Hexagoane alternante", "Common.define.smartArt.textAlternatingPictureBlocks": "<PERSON><PERSON> imagini alternative", "Common.define.smartArt.textAlternatingPictureCircles": "<PERSON><PERSON>uri de imagini alternative", "Common.define.smartArt.textArchitectureLayout": "Aspect arhitectură", "Common.define.smartArt.textArrowRibbon": "Panglică săgeată", "Common.define.smartArt.textAscendingPictureAccentProcess": "Proces de imagini ascendent cu accent", "Common.define.smartArt.textBalance": "Balanță", "Common.define.smartArt.textBasicBendingProcess": "Proces de îndoire de bază", "Common.define.smartArt.textBasicBlockList": "Listă de blocare de bază", "Common.define.smartArt.textBasicChevronProcess": "Proces zigzag de bază", "Common.define.smartArt.textBasicCycle": "Ciclu de bază", "Common.define.smartArt.textBasicMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textBasicPie": "Structură radială de bază", "Common.define.smartArt.textBasicProcess": "Proces de bază", "Common.define.smartArt.textBasicPyramid": "Piramidă de bază", "Common.define.smartArt.textBasicRadial": "Radială de bază", "Common.define.smartArt.textBasicTarget": "Țintă de bază", "Common.define.smartArt.textBasicTimeline": "Cronologie de bază", "Common.define.smartArt.textBasicVenn": "<PERSON><PERSON>n <PERSON>", "Common.define.smartArt.textBendingPictureAccentList": "Listă de accentuare imagini", "Common.define.smartArt.textBendingPictureBlocks": "<PERSON>uri de imagini cu îndoire", "Common.define.smartArt.textBendingPictureCaption": "Legendă de imagini cu îndoire", "Common.define.smartArt.textBendingPictureCaptionList": "Listă de legende de imagini cu îndoire", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Imagini cu îndoire și text semitransparent", "Common.define.smartArt.textBlockCycle": "Ciclu în bloc", "Common.define.smartArt.textBubblePictureList": "Listă de imagini cu bule", "Common.define.smartArt.textCaptionedPictures": "Imagini cu <PERSON>", "Common.define.smartArt.textChevronAccentProcess": "Proces accent zigzag", "Common.define.smartArt.textChevronList": "Listă zigzag", "Common.define.smartArt.textCircleAccentTimeline": "Cronologie accent circular", "Common.define.smartArt.textCircleArrowProcess": "Proces cu săgeți circular", "Common.define.smartArt.textCirclePictureHierarchy": "Ierarhie cu imagini circulare", "Common.define.smartArt.textCircleProcess": "Proces circular", "Common.define.smartArt.textCircleRelationship": "Relație circulară", "Common.define.smartArt.textCircularBendingProcess": "Proces de îndoire circulară", "Common.define.smartArt.textCircularPictureCallout": "Explicație cu imagini circulare", "Common.define.smartArt.textClosedChevronProcess": "Proces zigzag închis", "Common.define.smartArt.textContinuousArrowProcess": "Săgeți de proces continuu", "Common.define.smartArt.textContinuousBlockProcess": "Proces de blocare continuu", "Common.define.smartArt.textContinuousCycle": "Ciclu continuu", "Common.define.smartArt.textContinuousPictureList": "Listă continuă de imagini", "Common.define.smartArt.textConvergingArrows": "Săgeți convergente", "Common.define.smartArt.textConvergingRadial": "Radială convergentă", "Common.define.smartArt.textConvergingText": "Text convergent", "Common.define.smartArt.textCounterbalanceArrows": "Săgeți contrabalansate", "Common.define.smartArt.textCycle": "Ciclu", "Common.define.smartArt.textCycleMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textDescendingBlockList": "Listă de blocuri des<PERSON>", "Common.define.smartArt.textDescendingProcess": "Proces descendent", "Common.define.smartArt.textDetailedProcess": "Proces detaliat", "Common.define.smartArt.textDivergingArrows": "Săgeți divergente", "Common.define.smartArt.textDivergingRadial": "Radială divergentă", "Common.define.smartArt.textEquation": "Ecuație", "Common.define.smartArt.textFramedTextPicture": "Imagine cu text încadrat", "Common.define.smartArt.textFunnel": "Extrage", "Common.define.smartArt.textGear": "Roată dințată", "Common.define.smartArt.textGridMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textGroupedList": "Listă grupată", "Common.define.smartArt.textHalfCircleOrganizationChart": "Organigramă semicirculară", "Common.define.smartArt.textHexagonCluster": "Cluster hexagonal", "Common.define.smartArt.textHexagonRadial": "Hexagoane radiale", "Common.define.smartArt.textHierarchy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textHierarchyList": "Listă ierarhică", "Common.define.smartArt.textHorizontalBulletList": "Listă orizontală cu marcatori", "Common.define.smartArt.textHorizontalHierarchy": "Ierarhie orizontală", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Ierarhie orizontală etichetată", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Ierarhie orizontală pe mai multe niveluri", "Common.define.smartArt.textHorizontalOrganizationChart": "Organigramă orizontală", "Common.define.smartArt.textHorizontalPictureList": "Listă orizontală de imagini", "Common.define.smartArt.textIncreasingArrowProcess": "Proces cu săgeți crescător", "Common.define.smartArt.textIncreasingCircleProcess": "Proces circular crescător", "Common.define.smartArt.textInterconnectedBlockProcess": "Proces cu blocuri interconectate", "Common.define.smartArt.textInterconnectedRings": "Inele interconectate", "Common.define.smartArt.textInvertedPyramid": "Piramidă inversată", "Common.define.smartArt.textLabeledHierarchy": "Ierarhie etichetată", "Common.define.smartArt.textLinearVenn": "<PERSON><PERSON><PERSON> l<PERSON>r", "Common.define.smartArt.textLinedList": "Listă liniată", "Common.define.smartArt.textList": "Listă", "Common.define.smartArt.textMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textMultidirectionalCycle": "Ciclu multidirecțional", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Organigramă nume și titlu", "Common.define.smartArt.textNestedTarget": "Țintă imbricată", "Common.define.smartArt.textNondirectionalCycle": "Ciclu nondirecțional", "Common.define.smartArt.textOpposingArrows": "Săgeți opuse", "Common.define.smartArt.textOpposingIdeas": "Idei opuse", "Common.define.smartArt.textOrganizationChart": "Organigramă", "Common.define.smartArt.textOther": "Altele", "Common.define.smartArt.textPhasedProcess": "Proces în etape", "Common.define.smartArt.textPicture": "Imagine", "Common.define.smartArt.textPictureAccentBlocks": "<PERSON><PERSON> de imagini cu accent", "Common.define.smartArt.textPictureAccentList": "Listă de accentuare imagini", "Common.define.smartArt.textPictureAccentProcess": "Proces accent imagine", "Common.define.smartArt.textPictureCaptionList": "Listă legendă imagine", "Common.define.smartArt.textPictureFrame": "RamăDeFotografie", "Common.define.smartArt.textPictureGrid": "Grilă de imagini", "Common.define.smartArt.textPictureLineup": "Aliniere imagini", "Common.define.smartArt.textPictureOrganizationChart": "Organigramă cu imagini", "Common.define.smartArt.textPictureStrips": "Benzi cu imagini", "Common.define.smartArt.textPieProcess": "Proces structură radială", "Common.define.smartArt.textPlusAndMinus": "Plus și minus", "Common.define.smartArt.textProcess": "Proces", "Common.define.smartArt.textProcessArrows": "Săgeți proces", "Common.define.smartArt.textProcessList": "Listă proces", "Common.define.smartArt.textPyramid": "Piramidă", "Common.define.smartArt.textPyramidList": "Listă piramidală", "Common.define.smartArt.textRadialCluster": "Cluster radial", "Common.define.smartArt.textRadialCycle": "Ciclu radial", "Common.define.smartArt.textRadialList": "Listă radială", "Common.define.smartArt.textRadialPictureList": "Listă radială de imagini", "Common.define.smartArt.textRadialVenn": "Venn <PERSON>", "Common.define.smartArt.textRandomToResultProcess": "Proces de idei aleatoare cu rezultat", "Common.define.smartArt.textRelationship": "Re<PERSON><PERSON><PERSON>", "Common.define.smartArt.textRepeatingBendingProcess": "Proces de îndoire repetată", "Common.define.smartArt.textReverseList": "Inversare listă", "Common.define.smartArt.textSegmentedCycle": "Ciclu segmentat", "Common.define.smartArt.textSegmentedProcess": "Proces segmentat", "Common.define.smartArt.textSegmentedPyramid": "Piramidă segmentată", "Common.define.smartArt.textSnapshotPictureList": "Listă imagini instantanee", "Common.define.smartArt.textSpiralPicture": "Imagini în spirală", "Common.define.smartArt.textSquareAccentList": "Listă accent pătrat", "Common.define.smartArt.textStackedList": "Listă suprapusă", "Common.define.smartArt.textStackedVenn": "<PERSON><PERSON><PERSON> supra<PERSON>", "Common.define.smartArt.textStaggeredProcess": "Proces decalat", "Common.define.smartArt.textStepDownProcess": "Proces descendent", "Common.define.smartArt.textStepUpProcess": "Proces ascendent", "Common.define.smartArt.textSubStepProcess": "Proces cu subpași", "Common.define.smartArt.textTabbedArc": "Arc cu file", "Common.define.smartArt.textTableHierarchy": "<PERSON><PERSON><PERSON><PERSON><PERSON> tabel", "Common.define.smartArt.textTableList": "Listă tabel", "Common.define.smartArt.textTabList": "Listă file", "Common.define.smartArt.textTargetList": "Listă ținte", "Common.define.smartArt.textTextCycle": "Ciclu text", "Common.define.smartArt.textThemePictureAccent": "Imagini cu accent și temă", "Common.define.smartArt.textThemePictureAlternatingAccent": "Imagini cu accent alternativ și temă", "Common.define.smartArt.textThemePictureGrid": "Grilă de imagini cu temă", "Common.define.smartArt.textTitledMatrix": "<PERSON>rice cu titlu", "Common.define.smartArt.textTitledPictureAccentList": "Listă imagini cu titlu și accent", "Common.define.smartArt.textTitledPictureBlocks": "<PERSON><PERSON> de imagini cu titlu", "Common.define.smartArt.textTitlePictureLineup": "Imagini aliniate cu titlu", "Common.define.smartArt.textTrapezoidList": "Listă trapezoidală", "Common.define.smartArt.textUpwardArrow": "Săgeată ascendentă", "Common.define.smartArt.textVaryingWidthList": "Listă de lățime variabilă", "Common.define.smartArt.textVerticalAccentList": "Listă verticală cu accent", "Common.define.smartArt.textVerticalArrowList": "Listă săgeți verticale", "Common.define.smartArt.textVerticalBendingProcess": "Proces de îndoire verticală", "Common.define.smartArt.textVerticalBlockList": "Listă bloc vertical", "Common.define.smartArt.textVerticalBoxList": "Listă de blocuri verticală", "Common.define.smartArt.textVerticalBracketList": "Listă paranteze verticale", "Common.define.smartArt.textVerticalBulletList": "Listă verticală cu marcatori", "Common.define.smartArt.textVerticalChevronList": "Listă zigzag verticală", "Common.define.smartArt.textVerticalCircleList": "Listă cercuri verticale", "Common.define.smartArt.textVerticalCurvedList": "Listă curbată verticală", "Common.define.smartArt.textVerticalEquation": "Ecuație verticală", "Common.define.smartArt.textVerticalPictureAccentList": "Listă accent imagine verticală", "Common.define.smartArt.textVerticalPictureList": "Listă verticală imagine", "Common.define.smartArt.textVerticalProcess": "Proces vertical", "Common.Translation.textMoreButton": "<PERSON> multe", "Common.Translation.tipFileLocked": "Acest document este blocat pentru editare. Îl puteți modifica și salva mai târziu ca o copie pe unitatea locală.", "Common.Translation.tipFileReadOnly": "Documentul este disponibil doar în citire și este blocat pentru editare. Puteți îl modifica și salva mai târziu ca o copie pe unitatea locală.", "Common.Translation.warnFileLocked": "Fișierul este editat într-o altă aplicație. Puteți continua să editați și să-l salvați ca o copie.", "Common.Translation.warnFileLockedBtnEdit": "Creează o copie", "Common.Translation.warnFileLockedBtnView": "Deschide vizualizarea", "Common.UI.ButtonColored.textAutoColor": "Automat", "Common.UI.ButtonColored.textEyedropper": "Pipetă", "Common.UI.ButtonColored.textNewColor": "Mai multe culori", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> bord<PERSON>", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> bord<PERSON>", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "Common.UI.ExtendedColorDialog.addButtonText": "Adaugă", "Common.UI.ExtendedColorDialog.textCurrent": "<PERSON>nt", "Common.UI.ExtendedColorDialog.textHexErr": "Valoarea introdusă nu este corectă.<br>Selectați valoarea cuprinsă înte 000000 și FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Nou", "Common.UI.ExtendedColorDialog.textRGBErr": "Valoarea introdusă nu este corectă.<br>Introduceți valoarea numerică de la 0 până la 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON><PERSON><PERSON> culoare", "Common.UI.InputField.txtEmpty": "Câmp obligatoriu", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Ascunde parola", "Common.UI.InputFieldBtnPassword.textHintHold": "Apăsați și mențineți apăsat butonul până când se afișează parola", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Afișeaz<PERSON> parola", "Common.UI.SearchBar.textFind": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON> c<PERSON>", "Common.UI.SearchBar.tipNextResult": "Următorul rezultat", "Common.UI.SearchBar.tipOpenAdvancedSettings": "<PERSON><PERSON><PERSON>ă<PERSON> a<PERSON>", "Common.UI.SearchBar.tipPreviousResult": "Rezultatul anterior", "Common.UI.SearchDialog.textHighlight": "Evidențierea rezultatelor", "Common.UI.SearchDialog.textMatchCase": "Sensibil la litere mari și mici", "Common.UI.SearchDialog.textReplaceDef": "Introduceți textul înlocuitor", "Common.UI.SearchDialog.textSearchStart": "Introduceți textul aici", "Common.UI.SearchDialog.textTitle": "Găsire și înlocuire", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "<PERSON>umai cu<PERSON>nt<PERSON>gi", "Common.UI.SearchDialog.txtBtnHideReplace": "Ascundere înlocuire", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "Înlocuire peste tot", "Common.UI.SynchronizeTip.textDontShow": "<PERSON>u afișa acest mesaj din nou", "Common.UI.SynchronizeTip.textGotIt": "Am înțeles", "Common.UI.SynchronizeTip.textSynchronize": "Documentul a fost modificat de către un alt utilizator.<br>Salvați modificările făcute de dumneavoastră și reîmprospătați documentul.", "Common.UI.ThemeColorPalette.textRecentColors": "<PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textStandartColors": "Culori standard", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeClassicLight": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeContrastDark": "Contrast Întunecat", "Common.UI.Themes.txtThemeDark": "Întunecat", "Common.UI.Themes.txtThemeGray": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "Luminos", "Common.UI.Themes.txtThemeSystem": "La fel ca sistemul", "Common.UI.Window.cancelButtonText": "Revocare", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "<PERSON>u", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmare", "Common.UI.Window.textDontShow": "<PERSON>u afișa acest mesaj din nou", "Common.UI.Window.textError": "Eroare", "Common.UI.Window.textInformation": "Informații", "Common.UI.Window.textWarning": "Avertisment", "Common.UI.Window.yesButtonText": "Da", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Accent", "Common.Utils.ThemeColor.txtAqua": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtbackground": "Fundal", "Common.Utils.ThemeColor.txtBlack": "Negru", "Common.Utils.ThemeColor.txtBlue": "Albastru", "Common.Utils.ThemeColor.txtBrightGreen": "Verde aprins", "Common.Utils.ThemeColor.txtBrown": "Maro", "Common.Utils.ThemeColor.txtDarkBlue": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarker": "<PERSON>", "Common.Utils.ThemeColor.txtDarkGray": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkGreen": "<PERSON>is", "Common.Utils.ThemeColor.txtDarkPurple": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkRed": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkTeal": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkYellow": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtGold": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtGray": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtGreen": "Verde", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtLightBlue": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtLighter": "<PERSON>", "Common.Utils.ThemeColor.txtLightGray": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtLightGreen": "Verde deschis", "Common.Utils.ThemeColor.txtLightOrange": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtLightYellow": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtOrange": "Portocal<PERSON>", "Common.Utils.ThemeColor.txtPink": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtPurple": "<PERSON>v", "Common.Utils.ThemeColor.txtRed": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtRose": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Text", "Common.Utils.ThemeColor.txtTurquosie": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "Alb", "Common.Utils.ThemeColor.txtYellow": "<PERSON><PERSON><PERSON>", "Common.Views.About.txtAddress": "adresă:", "Common.Views.About.txtLicensee": "LICENȚIAT", "Common.Views.About.txtLicensor": "LICENȚIATOR", "Common.Views.About.txtMail": "e-mail:", "Common.Views.About.txtPoweredBy": "Dezvoltat de", "Common.Views.About.txtTel": "tel.:", "Common.Views.About.txtVersion": "Versiune", "Common.Views.AutoCorrectDialog.textAdd": "Adaugă", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Se aplică în timp ce lucrați", "Common.Views.AutoCorrectDialog.textAutoCorrect": "AutoCorecție", "Common.Views.AutoCorrectDialog.textAutoFormat": "Formatare automată la tastare", "Common.Views.AutoCorrectDialog.textBy": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textHyperlink": "Căi Internet și de rețea cu hyperlink-uri", "Common.Views.AutoCorrectDialog.textMathCorrect": "Autocorecție matematică", "Common.Views.AutoCorrectDialog.textNewRowCol": "Inserare de rânduri și coloane noi în tabel", "Common.Views.AutoCorrectDialog.textRecognized": "Funcțiile recunoscute", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Următoarele expresii sunt expresii matematice recuniscute. Acestea nu vor fi redate automat cu litere cursive.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "Înlocuire în timpul tastării", "Common.Views.AutoCorrectDialog.textReplaceType": "Înlocuire text în timpul tastării", "Common.Views.AutoCorrectDialog.textReset": "Resetare", "Common.Views.AutoCorrectDialog.textResetAll": "Restabili<PERSON> set<PERSON><PERSON> implicite", "Common.Views.AutoCorrectDialog.textRestore": "Restabilire", "Common.Views.AutoCorrectDialog.textTitle": "AutoCorecție", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Funcțiile recunoscute pot conține litere majuscule sau minuscule de la A la Z.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Orice expresii adăugate vor fi eliminate, dar cele care au fost eliminate vor fi restabilite. Doriți să continuați?", "Common.Views.AutoCorrectDialog.warnReplace": "Intrare de autocorectare pentru %1 deja existentă. Doriți să o înlocuiți?", "Common.Views.AutoCorrectDialog.warnReset": "Corectare automată va fi dezactivată și fișierul va fi restabilit la valori inițiale. Doriți să continuați?", "Common.Views.AutoCorrectDialog.warnRestore": "Intrare de autocorectare pentru %1 va fi restabilită la valorile inițiale. Doriți să continuați?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "<PERSON><PERSON><PERSON> chat-ul", "Common.Views.Chat.textEnterMessage": "Lăsați mesaj aici", "Common.Views.Chat.textSend": "Trimitere", "Common.Views.Comments.mniAuthorAsc": "Autor de la A la Z", "Common.Views.Comments.mniAuthorDesc": "Autor de la Z la A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON> mai vechi", "Common.Views.Comments.mniDateDesc": "<PERSON><PERSON> mai recente", "Common.Views.Comments.mniFilterGroups": "Filtrare după grup", "Common.Views.Comments.mniPositionAsc": "De sus", "Common.Views.Comments.mniPositionDesc": "De jos", "Common.Views.Comments.textAdd": "Adaugă", "Common.Views.Comments.textAddComment": "Adaugă comentariu", "Common.Views.Comments.textAddCommentToDoc": "Adăugare comentariu la document", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAll": "Toate", "Common.Views.Comments.textAnonym": "Invitat", "Common.Views.Comments.textCancel": "Revocare", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON> comenta<PERSON>i", "Common.Views.Comments.textComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textComments": "Comenta<PERSON><PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Comentați aici", "Common.Views.Comments.textHintAddComment": "Adaugă comentariu", "Common.Views.Comments.textOpenAgain": "Deschidere din nou", "Common.Views.Comments.textReply": "Răspunde", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "Rezolvat", "Common.Views.Comments.textSort": "<PERSON>rta<PERSON> comentarii", "Common.Views.Comments.textSortFilter": "Sortare și filtrare comentarii", "Common.Views.Comments.textSortFilterMore": "<PERSON><PERSON><PERSON>, filtrare și mai multe", "Common.Views.Comments.textSortMore": "Sortare și mai multe", "Common.Views.Comments.textViewResolved": "Dvs. nu aveți permisiunea de a redeschide comentariu", "Common.Views.Comments.txtEmpty": "Nu există niciun comentariu.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON>u afișa acest mesaj din nou", "Common.Views.CopyWarningDialog.textMsg": "Operațiuni de copiere, decupare și lipire din bară de instrumente sau meniul contextual al editorului se execută numai în editorul.<br><br>Pentru copiere și lipire din sau în aplicații exterioare folosiți următoarele combinații de taste:", "Common.Views.CopyWarningDialog.textTitle": "Comenzile de copiere, decupare și lipire", "Common.Views.CopyWarningDialog.textToCopy": "pentru copiere", "Common.Views.CopyWarningDialog.textToCut": "pentru decupare", "Common.Views.CopyWarningDialog.textToPaste": "pentru lipire", "Common.Views.CustomizeQuickAccessDialog.textDownload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Adăugați comenzile care vor fi afișate pe bara de instrumente Acces rapid", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Imp<PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Imprimare rapidă", "Common.Views.CustomizeQuickAccessDialog.textRedo": "<PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textSave": "<PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Particularizare acces rapid", "Common.Views.CustomizeQuickAccessDialog.textUndo": "<PERSON><PERSON><PERSON>", "Common.Views.DocumentAccessDialog.textLoading": "Se incarca...", "Common.Views.DocumentAccessDialog.textTitle": "<PERSON><PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.errorDate": "Puteți selecta o ziuă din calendar pentru a salva acestă valoare ca Dată.<br><PERSON><PERSON><PERSON> pe care le introduceți manual vor fi salvate ca text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "<PERSON>u", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Da", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Proprietate trebuie să poarte un nume", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Nume", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Da\" sau \"Nu\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Dată", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Tip", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": " Trebuie s<PERSON> introduceți un număr valabil", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Proprietate trebuie asociată cu o valoare", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Valoare", "Common.Views.DocumentPropertyDialog.txtTitle": "Nouă proprietate de document", "Common.Views.Draw.hintEraser": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.hintSelect": "Selectare", "Common.Views.Draw.txtEraser": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtHighlighter": "Evidențiere", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Stilou", "Common.Views.Draw.txtSelect": "Selectare", "Common.Views.Draw.txtSize": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.EditNameDialog.textLabel": "Etichetă:", "Common.Views.EditNameDialog.textLabelError": "Etichetă trebuie completată", "Common.Views.Header.ariaQuickAccessToolbar": "Bara de instrumente Acces rapid", "Common.Views.Header.labelCoUsersDescr": "Fișierul este editat de către:", "Common.Views.Header.textAddFavorite": "Marcare ca preferat", "Common.Views.Header.textAdvSettings": "<PERSON><PERSON><PERSON>", "Common.Views.Header.textBack": "Deschidere locația fișierului", "Common.Views.Header.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Header.textCompactView": "Ascundere bară de instrumente", "Common.Views.Header.textHideLines": "<PERSON><PERSON><PERSON><PERSON> rigle", "Common.Views.Header.textHideStatusBar": "A îmbina selectorii foii de lucru cu bara de stare", "Common.Views.Header.textPrint": "Imp<PERSON><PERSON>", "Common.Views.Header.textReadOnly": "Doar în citire", "Common.Views.Header.textRemoveFavorite": "Eliminare din Preferințe", "Common.Views.Header.textSaveBegin": "<PERSON><PERSON>e în progres...", "Common.Views.Header.textSaveChanged": "Modificat", "Common.Views.Header.textSaveEnd": "Toate modificările au fost salvate", "Common.Views.Header.textSaveExpander": "Toate modificările au fost salvate", "Common.Views.Header.textShare": "Partajează", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "Administrarea permisiunilor de acces la document", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Particularizare bară de instrumente Acces rapid", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipGoEdit": "Editare acest fiș<PERSON>", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipPrintQuick": "Imprimare rapidă", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Salvează", "Common.Views.Header.tipSearch": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Detașare într-o fereastră separată", "Common.Views.Header.tipUsers": "Vizualizare utilizatori", "Common.Views.Header.tipViewSettings": "<PERSON><PERSON><PERSON> vizual<PERSON>", "Common.Views.Header.tipViewUsers": "Vizualizare utilizatori și gestionare permisiuni de acces", "Common.Views.Header.txtAccessRights": "Modificare permisiuni", "Common.Views.Header.txtRename": "Redenumire", "Common.Views.History.textCloseHistory": "Închide istoricul", "Common.Views.History.textHideAll": "Ascundere modificări detaliate", "Common.Views.History.textHighlightDeleted": "Evidentiere șterse", "Common.Views.History.textMore": "<PERSON> multe", "Common.Views.History.textRestore": "Restabilire", "Common.Views.History.textShowAll": "Afișare detaliată a modificărilor", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Istoricul versiune", "Common.Views.ImageFromUrlDialog.textUrl": "Lipire imagine prin URL:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Câmp obligatoriu", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Câmpul trebuie să conțină adresa URL in format \"http://www.example.com\" ", "Common.Views.ListSettingsDialog.textBulleted": "<PERSON>u marcatori", "Common.Views.ListSettingsDialog.textFromFile": "<PERSON>", "Common.Views.ListSettingsDialog.textFromStorage": "Din serviciul stocare", "Common.Views.ListSettingsDialog.textFromUrl": "<PERSON><PERSON> URL-ul", "Common.Views.ListSettingsDialog.textNumbering": "Numerotat", "Common.Views.ListSettingsDialog.textSelect": "Selectare din", "Common.Views.ListSettingsDialog.tipChange": "Modificare marcator", "Common.Views.ListSettingsDialog.txtBullet": "Marcator", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "Imagine", "Common.Views.ListSettingsDialog.txtImport": "Import", "Common.Views.ListSettingsDialog.txtNewBullet": "Marcator nou", "Common.Views.ListSettingsDialog.txtNewImage": "Imagine nouă", "Common.Views.ListSettingsDialog.txtNone": "Niciunul", "Common.Views.ListSettingsDialog.txtOfText": "% din text", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "Pornire de la", "Common.Views.ListSettingsDialog.txtSymbol": "Simbol", "Common.Views.ListSettingsDialog.txtTitle": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtType": "Tip", "Common.Views.MacrosDialog.textCopy": "<PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textCustomFunction": "Funcția particularizată", "Common.Views.MacrosDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textLoading": "Se încarcă...", "Common.Views.MacrosDialog.textMacros": "Macrocomandă", "Common.Views.MacrosDialog.textMakeAutostart": "Activare lansare automată", "Common.Views.MacrosDialog.textRename": "Redenumire", "Common.Views.MacrosDialog.textRun": "Executare", "Common.Views.MacrosDialog.textSave": "<PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textTitle": "Macrocomandă", "Common.Views.MacrosDialog.textUnMakeAutostart": "Dezactivare lansare automată", "Common.Views.MacrosDialog.tipFunctionAdd": "Adăugare funcția particularizată", "Common.Views.MacrosDialog.tipMacrosAdd": "Adăugare macrocomandă", "Common.Views.MacrosDialog.tipMacrosRun": "Executare", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.textInvalidRange": "Zona de celule nu este validă", "Common.Views.OpenDialog.textSelectData": "Selectare date", "Common.Views.OpenDialog.txtAdvanced": "Avansat", "Common.Views.OpenDialog.txtColon": "Două puncte", "Common.Views.OpenDialog.txtComma": "Virgulă", "Common.Views.OpenDialog.txtDelimiter": "Delimitator", "Common.Views.OpenDialog.txtDestData": "Alegeți locul unde se va plasa datele", "Common.Views.OpenDialog.txtEmpty": "Câmp obligatoriu", "Common.Views.OpenDialog.txtEncoding": "Codificare", "Common.Views.OpenDialog.txtIncorrectPwd": "Parolă incorectă", "Common.Views.OpenDialog.txtOpenFile": "Introduceți parola pentru a deschide fișierul", "Common.Views.OpenDialog.txtOther": "Altele", "Common.Views.OpenDialog.txtPassword": "Pa<PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtProtected": "Parola curentă la fișierul va fi resetată de îndată ce este introdusă și fișierul este deschis.", "Common.Views.OpenDialog.txtSemicolon": "Punct și virgulă", "Common.Views.OpenDialog.txtSpace": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtTab": "Tabulator", "Common.Views.OpenDialog.txtTitle": "Selectare opțiuni %1", "Common.Views.OpenDialog.txtTitleProtected": "Fișierul protejat", "Common.Views.PasswordDialog.txtDescription": "Setați o parolă pentru protejarea documentului", "Common.Views.PasswordDialog.txtIncorrectPwd": "Parola și Confirmare parola nu este indentice", "Common.Views.PasswordDialog.txtPassword": "Pa<PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "Reintroduceți parola", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON>", "Common.Views.PasswordDialog.txtWarning": "Atenție: <PERSON><PERSON><PERSON> pierdeți sau uitați parola, ea nu poate fi recuperată. Să o păstrați într-un loc sigur.", "Common.Views.PluginDlg.textDock": "Fixare plugin", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.PluginPanel.textClosePanel": "Închide plugin-ul", "Common.Views.PluginPanel.textHidePanel": "Restrângere plugin", "Common.Views.PluginPanel.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.PluginPanel.textUndock": "Anulare fixare plugin", "Common.Views.Plugins.groupCaption": "<PERSON>lug<PERSON>-<PERSON><PERSON>", "Common.Views.Plugins.strPlugins": "<PERSON>lug<PERSON>-<PERSON><PERSON>", "Common.Views.Plugins.textBackgroundPlugins": "Plugin-<PERSON>ri în fundal", "Common.Views.Plugins.textSettings": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStart": "Pornire", "Common.Views.Plugins.textStop": "Oprire", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "Lista plugin-urilor în fundal", "Common.Views.Plugins.tipMore": "<PERSON> multe", "Common.Views.Protection.hintAddPwd": "Criptare utilizând o parolă", "Common.Views.Protection.hintDelPwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.hintPwd": "Modificarea sau eliminarea parolei", "Common.Views.Protection.hintSignature": "Adăugarea semnăturii digitale sau liniei de semnătură", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON><PERSON> parola", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtEncrypt": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtInvisibleSignature": "Adăugare semnătură digitală", "Common.Views.Protection.txtSignature": "Semnătura", "Common.Views.Protection.txtSignatureLine": "Adă<PERSON>re linie de semnătură", "Common.Views.RecentFiles.txtOpenRecent": "Deschidere recente", "Common.Views.RenameDialog.textName": "Numele fișierului", "Common.Views.RenameDialog.txtInvalidName": "Numele fișierului nu poate conține caracterele următoare:", "Common.Views.ReviewChanges.hintNext": "Modificarea următoare", "Common.Views.ReviewChanges.hintPrev": "Modificarea anterioară", "Common.Views.ReviewChanges.strFast": "Rapid", "Common.Views.ReviewChanges.strFastDesc": "Colaborareaa la documente în timp real. Toate modificările sunt salvate automat.", "Common.Views.ReviewChanges.strStrict": "Strict", "Common.Views.ReviewChanges.strStrictDesc": "Folosiți butonul de salvare pentru sincronizarea modificărilor pe care le faceți dvs. sau alți utilizatori.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Acceptați această modificare", "Common.Views.ReviewChanges.tipCoAuthMode": "Activare modul de colaborare", "Common.Views.ReviewChanges.tipCommentRem": "<PERSON><PERSON><PERSON><PERSON> comenta<PERSON>i", "Common.Views.ReviewChanges.tipCommentRemCurrent": "<PERSON><PERSON><PERSON> comenta<PERSON><PERSON> curente", "Common.Views.ReviewChanges.tipCommentResolve": "Soluționare comentarii", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Soluționarea comentariilor curente", "Common.Views.ReviewChanges.tipHistory": "Afișare istoricul versiunei", "Common.Views.ReviewChanges.tipRejectCurrent": "Respinge modificare", "Common.Views.ReviewChanges.tipReview": "Urm<PERSON><PERSON>rea modifică<PERSON>", "Common.Views.ReviewChanges.tipReviewView": "Selectați modul de afișare a modificărilor", "Common.Views.ReviewChanges.tipSetDocLang": "Specificarea limbii pentru document", "Common.Views.ReviewChanges.tipSetSpelling": "Verificarea ortografică", "Common.Views.ReviewChanges.tipSharing": "Administrarea permisiunilor de acces la document", "Common.Views.ReviewChanges.txtAccept": "Acceptare", "Common.Views.ReviewChanges.txtAcceptAll": "Accepta<PERSON><PERSON> toate modificările", "Common.Views.ReviewChanges.txtAcceptChanges": "Acceptați modificările", "Common.Views.ReviewChanges.txtAcceptCurrent": "Acceptați această modificare", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Modul de editare colaborativă", "Common.Views.ReviewChanges.txtCommentRemAll": "Șterge toate comenta<PERSON><PERSON> ", "Common.Views.ReviewChanges.txtCommentRemCurrent": "<PERSON><PERSON><PERSON> comenta<PERSON><PERSON> curente", "Common.Views.ReviewChanges.txtCommentRemMy": "<PERSON><PERSON><PERSON> comenta<PERSON><PERSON> mele", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "<PERSON><PERSON><PERSON> comentariile mele curente", "Common.Views.ReviewChanges.txtCommentRemove": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolve": "Soluționare", "Common.Views.ReviewChanges.txtCommentResolveAll": "March<PERSON>ză toate comentariile ca rezolvate", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "<PERSON><PERSON><PERSON><PERSON><PERSON> comentarii curente", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON>zolvar<PERSON> comentariilor mele", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Soluționarea comentariilor mele curente", "Common.Views.ReviewChanges.txtDocLang": "Limbă", "Common.Views.ReviewChanges.txtFinal": "Toate mod<PERSON><PERSON><PERSON><PERSON> sunt acceptate (Previzualizare)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Istoricul versiune", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON> mod<PERSON> (Editare)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "Următorul", "Common.Views.ReviewChanges.txtOriginal": "Toate mod<PERSON><PERSON><PERSON><PERSON> sunt respinse (Previzualizare)", "Common.Views.ReviewChanges.txtOriginalCap": "Inițial", "Common.Views.ReviewChanges.txtPrev": "Anterior", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Se resping toate modificările", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON><PERSON><PERSON> mod<PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "Respinge modificare", "Common.Views.ReviewChanges.txtSharing": "Partajare", "Common.Views.ReviewChanges.txtSpelling": "Verificarea ortografică", "Common.Views.ReviewChanges.txtTurnon": "Urm<PERSON><PERSON>rea modifică<PERSON>", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAdd": "Adaugă", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textCancel": "Revocare", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Comentați aici", "Common.Views.ReviewPopover.textMention": "+mentionare pentru a furniza accesul la document și a trimite un e-mail", "Common.Views.ReviewPopover.textMentionNotify": "+mentionare pentru a notifica utilizatorul prin e-mail", "Common.Views.ReviewPopover.textOpenAgain": "Deschidere din nou", "Common.Views.ReviewPopover.textReply": "Răspunde", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Dvs. nu aveți permisiunea de a redeschide comentariu", "Common.Views.ReviewPopover.txtDeleteTip": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.txtEditTip": "Editare", "Common.Views.SaveAsDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textTitle": "Folderul de salvare", "Common.Views.SearchPanel.textByColumns": "<PERSON><PERSON><PERSON> colo<PERSON>", "Common.Views.SearchPanel.textByRows": "<PERSON><PERSON><PERSON> r<PERSON>d", "Common.Views.SearchPanel.textCaseSensitive": "Sensibil la litere mari și mici", "Common.Views.SearchPanel.textCell": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON> c<PERSON>", "Common.Views.SearchPanel.textContentChanged": "Documentul a fost modificat.", "Common.Views.SearchPanel.textFind": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "Găsire și înlocuire", "Common.Views.SearchPanel.textFormula": "Formula", "Common.Views.SearchPanel.textFormulas": "Formule", "Common.Views.SearchPanel.textItemEntireCell": "Întreg conținut de celulă", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} elemente s-au înlocuit cu succes.", "Common.Views.SearchPanel.textLookIn": "Domenii <PERSON>", "Common.Views.SearchPanel.textMatchUsingRegExp": "Potrivire expresie regulată", "Common.Views.SearchPanel.textName": "Numele", "Common.Views.SearchPanel.textNoMatches": "<PERSON><PERSON>", "Common.Views.SearchPanel.textNoSearchResults": "<PERSON><PERSON><PERSON> rezultat de c<PERSON>uta<PERSON>", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} elemente s-au înlocuit. {2} elemente rămase sunt blocate de către alți utilizatori.", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceAll": "Înlocuire peste tot", "Common.Views.SearchPanel.textReplaceWith": "Înlocuire cu", "Common.Views.SearchPanel.textSearch": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSearchAgain": "{0}Efectuați o nouă căutare{1} pentru rezultate mai precise.", "Common.Views.SearchPanel.textSearchHasStopped": "Сăutarea s-a oprit", "Common.Views.SearchPanel.textSearchOptions": "Opțiuni de căutare", "Common.Views.SearchPanel.textSearchResults": "Rezultatele că<PERSON>ării: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Rezultatele căutării", "Common.Views.SearchPanel.textSelectDataRange": "Selectați zonă de date", "Common.Views.SearchPanel.textSheet": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSpecificRange": "Anumită zonă", "Common.Views.SearchPanel.textTooManyResults": "Prea multe rezultate ca să fie afișate aici", "Common.Views.SearchPanel.textValue": "Valoare", "Common.Views.SearchPanel.textValues": "Valori", "Common.Views.SearchPanel.textWholeWords": "<PERSON>umai cu<PERSON>nt<PERSON>gi", "Common.Views.SearchPanel.textWithin": "Între", "Common.Views.SearchPanel.textWorkbook": "Registru de calcul", "Common.Views.SearchPanel.tipNextResult": "Următorul rezultat", "Common.Views.SearchPanel.tipPreviousResult": "Rezultatul anterior", "Common.Views.SelectFileDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SelectFileDlg.textTitle": "Selectare sursă de date", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distan<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtSize": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtTitle": "Ajustare umbră", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparență", "Common.Views.SignDialog.textBold": "<PERSON><PERSON>", "Common.Views.SignDialog.textCertificate": "Certificat", "Common.Views.SignDialog.textChange": "Modificare", "Common.Views.SignDialog.textInputName": "Introduceți numele semnatarului", "Common.Views.SignDialog.textItalic": "Cursiv", "Common.Views.SignDialog.textNameError": "Numele semnatarului trebuie completat.", "Common.Views.SignDialog.textPurpose": "Scopul semnării acestui document", "Common.Views.SignDialog.textSelect": "Selectare", "Common.Views.SignDialog.textSelectImage": "Selectare imagine", "Common.Views.SignDialog.textSignature": "Semnătura arată ca", "Common.Views.SignDialog.textTitle": "Semnare document", "Common.Views.SignDialog.textUseImage": "sau face<PERSON>i clic pe Selectare imagine pentru a selecta o imagine de utilizat ca semnătură", "Common.Views.SignDialog.textValid": "Valabil de la %1 până la %2", "Common.Views.SignDialog.tipFontName": "Denumire font", "Common.Views.SignDialog.tipFontSize": "Dimensiune font", "Common.Views.SignSettingsDialog.textAllowComment": "Se permite semnatarului să adauge comentarii în dialogul Semnare", "Common.Views.SignSettingsDialog.textDefInstruction": "Înninte de semnarea documentului, verificați corectitudinea conținutului acestuia.", "Common.Views.SignSettingsDialog.textInfoEmail": "Adresa e-mail a semnatarului sugerat", "Common.Views.SignSettingsDialog.textInfoName": "Semnatar sugerat", "Common.Views.SignSettingsDialog.textInfoTitle": "Funcția semnatarului sugerat", "Common.Views.SignSettingsDialog.textInstructions": "<PERSON>ges<PERSON> pentru semnatar", "Common.Views.SignSettingsDialog.textShowDate": "Se afișează data semnării în linia semnăturii", "Common.Views.SignSettingsDialog.textTitle": "Configurare semnă<PERSON>", "Common.Views.SignSettingsDialog.txtEmpty": "Câmp obligatoriu", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Valoarea Unicode hexadecimală ", "Common.Views.SymbolTableDialog.textCopyright": "Simbolul drepturilor de autor", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON><PERSON><PERSON> închidere", "Common.Views.SymbolTableDialog.textDOQuote": "<PERSON><PERSON><PERSON><PERSON> dub<PERSON> de deschidere", "Common.Views.SymbolTableDialog.textEllipsis": "Puncte de suspensie", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON><PERSON> de dialog", "Common.Views.SymbolTableDialog.textEmSpace": "Spațiu lat", "Common.Views.SymbolTableDialog.textEnDash": "Liniuța de despărțire", "Common.Views.SymbolTableDialog.textEnSpace": "Spațiu <PERSON>", "Common.Views.SymbolTableDialog.textFont": "Font", "Common.Views.SymbolTableDialog.textNBHyphen": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBSpace": "<PERSON><PERSON><PERSON> neseparator", "Common.Views.SymbolTableDialog.textPilcrow": "<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 sp<PERSON><PERSON> lat", "Common.Views.SymbolTableDialog.textRange": "Zona", "Common.Views.SymbolTableDialog.textRecent": "Simboluri utilizate recent", "Common.Views.SymbolTableDialog.textRegistered": "Simbol Marcă înregistrată", "Common.Views.SymbolTableDialog.textSCQuote": "G<PERSON><PERSON>le simple de închidere", "Common.Views.SymbolTableDialog.textSection": "Semnul secțiune", "Common.Views.SymbolTableDialog.textShortcut": "Comenzi rapide de la tastatură", "Common.Views.SymbolTableDialog.textSHyphen": "Liniuța de despărțire în silabe", "Common.Views.SymbolTableDialog.textSOQuote": "Ghilimele simple de deschidere", "Common.Views.SymbolTableDialog.textSpecial": "Caractere speciale", "Common.Views.SymbolTableDialog.textSymbols": "Simbo<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textTitle": "Simbol", "Common.Views.SymbolTableDialog.textTradeMark": "Simbolul Marca comercială", "Common.Views.UserNameDialog.textDontShow": "<PERSON>u mai <PERSON>", "Common.Views.UserNameDialog.textLabel": "Etichetă:", "Common.Views.UserNameDialog.textLabelError": "Etichetă trebuie completată", "SSE.Controllers.DataTab.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textAddExternalData": "A fost adăugat linkul către sursă externă. Puteți să actualizați aceste linkuri în tabelul de date.", "SSE.Controllers.DataTab.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textContinue": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textDontUpdate": "Nu trebuie să actualizați", "SSE.Controllers.DataTab.textEmptyUrl": "Trebuie să specificaţi URL-ul.", "SSE.Controllers.DataTab.textRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textTurnOff": "Dezactivare Actualizare automată", "SSE.Controllers.DataTab.textUpdate": "Actualizare", "SSE.Controllers.DataTab.textWizard": "Text în coloane", "SSE.Controllers.DataTab.txtDataValidation": "Validarea datelor", "SSE.Controllers.DataTab.txtErrorExternalLink": "Eroare: act<PERSON><PERSON><PERSON> a <PERSON>", "SSE.Controllers.DataTab.txtExpand": "Extindere", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Datele lângă selecție nu se vor elimina. Doriți să extindeți selecția pentru a include datele adiacente sau doriți să continuați cu selecția curentă?", "SSE.Controllers.DataTab.txtExtendDataValidation": "Zona selectată conține celulele pentru care regulile de validare a datelor nu sunt configutare.<br>Do<PERSON>ți să aplicați regulile de validare acestor celule?", "SSE.Controllers.DataTab.txtImportWizard": "Expertul import text", "SSE.Controllers.DataTab.txtRemDuplicates": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.txtRemoveDataValidation": "Zona selectată conține mai multe tipuri de validare.<br>Vreți să ștergeți setările curente și să continuați? ", "SSE.Controllers.DataTab.txtRemSelected": "Continuare în selecția curentă", "SSE.Controllers.DataTab.txtUrlTitle": "Lipiti adresa URL a datelor", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "Foaia de calcul conține linkuri către surse externe care se actualizează automat. Acestea pot fi nesigure.<br><br>Dacă aveți încredere în aceste linkuri, apăsați Continuare.", "SSE.Controllers.DataTab.warnUpdateExternalData": "Foaia de calcul conține linkuri către surse externe care pot fi nesigure.<br> <PERSON><PERSON><PERSON> aveți încredere în aceste linkuri, actualizați-le pentru a obține cele mai recente date.", "SSE.Controllers.DocumentHolder.alignmentText": "Aliniere", "SSE.Controllers.DocumentHolder.centerText": "La centru", "SSE.Controllers.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON> colo<PERSON>", "SSE.Controllers.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.deleteText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Legătura la referință nu există. Corectați sau eliminați linkul.", "SSE.Controllers.DocumentHolder.guestText": "Invitat", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Coloană din stânga", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Coloană din dreapta", "SSE.Controllers.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertText": "Inserare", "SSE.Controllers.DocumentHolder.leftText": "Stânga", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Avertisment", "SSE.Controllers.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Opțiuni AutoCorecție", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Lățimea coloanei {0} simboluri ({1} pixeli)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Înălțime rând {0} puncte ({1} pixeli)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Faceți clic pe link pentu a-l deschide sau apăsați și țineți apăsat butonul mouse-ului pentru a selecta celula.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Inserare coloane la stânga", "SSE.Controllers.DocumentHolder.textInsertTop": "Inserare rând <PERSON>", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Lipire specială", "SSE.Controllers.DocumentHolder.textStopExpand": "Anularea extinderei automate de tabel", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Acest element este editat de către un alt utilizator.", "SSE.Controllers.DocumentHolder.txtAboveAve": "<PERSON><PERSON><PERSON>ra medie", "SSE.Controllers.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON><PERSON> bordur<PERSON> de jos", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Adăugare linia de fracție", "SSE.Controllers.DocumentHolder.txtAddHor": "Adăugare linie orizontală", "SSE.Controllers.DocumentHolder.txtAddLB": "Adăugare linia de jos din stânga", "SSE.Controllers.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON><PERSON> bordură stânga", "SSE.Controllers.DocumentHolder.txtAddLT": "Adăugare linia de sus din dreapta", "SSE.Controllers.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON><PERSON> bordur<PERSON> dreapta", "SSE.Controllers.DocumentHolder.txtAddTop": "<PERSON><PERSON><PERSON><PERSON> bordur<PERSON> de sus", "SSE.Controllers.DocumentHolder.txtAddVer": "Adăugare linia verticală", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Aliniere la caracter", "SSE.Controllers.DocumentHolder.txtAll": "(Toate)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Returnează tot conţinutul unui tabel sau coloanele selectate într-un tabel inclusiv anteturi de coloane, rânduri de date și rânduri de totaluri ", "SSE.Controllers.DocumentHolder.txtAnd": "și", "SSE.Controllers.DocumentHolder.txtBegins": "începe cu", "SSE.Controllers.DocumentHolder.txtBelowAve": "Sub medie", "SSE.Controllers.DocumentHolder.txtBlanks": "(Necompletate)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Propriet<PERSON><PERSON><PERSON> bord<PERSON>", "SSE.Controllers.DocumentHolder.txtBottom": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtByField": "%1 din %2", "SSE.Controllers.DocumentHolder.txtColumn": "Coloană", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Alinier<PERSON> co<PERSON>i", "SSE.Controllers.DocumentHolder.txtContains": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Link-ul copiat în clipboard", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Returnează celule de date dintr-un tabel sau colonele selectate într-un tabel", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Reducere argument dimensiune", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Eliminare argument", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Eliminare întrerupere <PERSON>ă", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Eliminare caractere de închidere", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Eliminare caractere de închidere și separatori", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Ștergere ecuație", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON><PERSON><PERSON> caracter", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Eliminare radical", "SSE.Controllers.DocumentHolder.txtEnds": "se termină cu", "SSE.Controllers.DocumentHolder.txtEquals": "Este egal cu", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "După culoarea celulei", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "După culoarea fontului", "SSE.Controllers.DocumentHolder.txtExpand": "Extindere și sortare", "SSE.Controllers.DocumentHolder.txtExpandSort": "Datele lângă selecție vor fi sortate. Doriți să extindeți selecția pentru a include datele adiacente sau doriți să continuați cu selecția curentă?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtFilterTop": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Modificare în fracție lineară", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Modificare în fracție oblică", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Modificare în fracție orizontală", "SSE.Controllers.DocumentHolder.txtGreater": "<PERSON> mare decât", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "<PERSON> mare sau egal", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Caracter de deasupra textului", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Caracter sub text", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Returnează titlurile coloanelor din tabel sau coloanele selectate într-un tabel ", "SSE.Controllers.DocumentHolder.txtHeight": "Înălțime", "SSE.Controllers.DocumentHolder.txtHideBottom": "Ascundere bordură de jos", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Ascundere limită inferioară", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Ascundere paranteză de închidere", "SSE.Controllers.DocumentHolder.txtHideDegree": "Ascundere gradul", "SSE.Controllers.DocumentHolder.txtHideHor": "Ascundere linie orizontală", "SSE.Controllers.DocumentHolder.txtHideLB": "Ascundere linia stânga de jos", "SSE.Controllers.DocumentHolder.txtHideLeft": "Ascundere bordură din stânga", "SSE.Controllers.DocumentHolder.txtHideLT": "Ascundere linia stânga de sus", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Ascundere paranteză de deschidere", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Ascundere substituent", "SSE.Controllers.DocumentHolder.txtHideRight": "Ascundere bordură din dreapta", "SSE.Controllers.DocumentHolder.txtHideTop": "Ascundere bordură de sus", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Ascundere limită superioară", "SSE.Controllers.DocumentHolder.txtHideVer": "Ascundere linie verticală", "SSE.Controllers.DocumentHolder.txtImportWizard": "Expertul import text", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "<PERSON><PERSON><PERSON><PERSON> dimensiu<PERSON> argument", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Inserare argument după", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Inserare argument înainte", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Inserare întrerupere manuală", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Inserare ecuație după", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Inserare ecuație înainte", "SSE.Controllers.DocumentHolder.txtItems": "element", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON><PERSON><PERSON> doar text", "SSE.Controllers.DocumentHolder.txtLess": "Mai mic decât", "SSE.Controllers.DocumentHolder.txtLessEquals": "Mai mic sau egal cu", "SSE.Controllers.DocumentHolder.txtLimitChange": "Modificare amplasări limite", "SSE.Controllers.DocumentHolder.txtLimitOver": "Limită deasupra textului", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Limită sub textul", "SSE.Controllers.DocumentHolder.txtLockSort": "Alături de celulele selectate au fost găsite datele dar nu aveți permisiuni suficente ca să le modificați<br>Doriți să continuați cu selecția curentă?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Portivire delimitatori la înălțimea argumentului", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Aliniere matrice", "SSE.Controllers.DocumentHolder.txtNoChoices": "Opțiunele de completare datelor în celulă<br><PERSON><PERSON><PERSON> înlocuire puteți selecta numai valori de text în coloană.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Nu începe cu", "SSE.Controllers.DocumentHolder.txtNotContains": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtNotEnds": "Nu se termină cu", "SSE.Controllers.DocumentHolder.txtNotEquals": "Nu este egal cu", "SSE.Controllers.DocumentHolder.txtOr": "sau", "SSE.Controllers.DocumentHolder.txtOverbar": "Bară deasupra textului", "SSE.Controllers.DocumentHolder.txtPaste": "Lipire", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formulă fă<PERSON><PERSON> borduri", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formula + lățimea coloană", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Destinație de formatare", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Lipire numai formatarea", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formula + format număr", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Lipire numai formulă", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formula + formatarea", "SSE.Controllers.DocumentHolder.txtPasteLink": "Lipire link", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Imagine legată", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Îmbinare formatare condiționată", "SSE.Controllers.DocumentHolder.txtPastePicture": "Imagine", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Formatare sursă", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Transpunere", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Valoare + formatare", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Valoare + formatare numere", "SSE.Controllers.DocumentHolder.txtPasteValues": "Lipire numai valoarea", "SSE.Controllers.DocumentHolder.txtPercent": "procent", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Reface<PERSON> extinderii automate a tabelului", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Eliminare bară de fracție", "SSE.Controllers.DocumentHolder.txtRemLimit": "Eliminare limită", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Eliminare caracter cu accent", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Eliminare bar<PERSON>", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Doriți să eliminați aceasta semnătura?<br>Va fi imposibil să anulați acțiunea.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Eliminare scripturi", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Eliminare indice", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Eliminare exponent", "SSE.Controllers.DocumentHolder.txtRowHeight": "Înălțime rând", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Scrip<PERSON><PERSON> după text", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Scriptul înaine de text", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Afișare limită inferioară", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Afișare paranteză de închidere", "SSE.Controllers.DocumentHolder.txtShowDegree": "Afișare grad", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Afișare paranteză de deschidere", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Afișare substituent", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Afișare limită superioară", "SSE.Controllers.DocumentHolder.txtSorting": "Sortare", "SSE.Controllers.DocumentHolder.txtSortSelected": "Sortarea se<PERSON>ției", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Întindere paranteze", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Numai acest rând din coloana selectată", "SSE.Controllers.DocumentHolder.txtTop": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Returnează rândurile de totaluri dintr-un tabel sau coloanele selectate într-un tabel", "SSE.Controllers.DocumentHolder.txtUnderbar": "Bară dedesubt textului", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Anulare extindere automată a tabelului ", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Utilizați expertul import text", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Un clic pe acest link ar putea căuza daune dispozitivului și datelor dvs.<br>Sunteți sigur că doriți să continuați?", "SSE.Controllers.DocumentHolder.txtWidth": "Lățime", "SSE.Controllers.DocumentHolder.warnFilterError": "Zonă de Valori trebuie să conțină cel puțin un câmp ca filtrul să fie aplicat. ", "SSE.Controllers.FormulaDialog.sCategoryAll": "Toate", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Particularizat", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Bază de date", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Dată și oră", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Inginerie", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Financiar", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Informații", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 utilizate recent", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logic", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Căutare și referință", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Funcții matematice și trigonometrice", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Funcții statistice", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Text și date", "SSE.Controllers.LeftMenu.newDocumentTitle": "Foaia de calcul fără numele", "SSE.Controllers.LeftMenu.textByColumns": "<PERSON><PERSON><PERSON> colo<PERSON>", "SSE.Controllers.LeftMenu.textByRows": "<PERSON><PERSON><PERSON> r<PERSON>d", "SSE.Controllers.LeftMenu.textFormulas": "Formule", "SSE.Controllers.LeftMenu.textItemEntireCell": "Întreg conținut de celulă", "SSE.Controllers.LeftMenu.textLoadHistory": "Încărcarea istoricului versiunii...", "SSE.Controllers.LeftMenu.textLookin": "Domenii <PERSON>", "SSE.Controllers.LeftMenu.textNoTextFound": "Datele căutate nu au fost găsite. Alegeți alte opțiuni de căutare.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "A avut loc o înlocuire. {0} apariții ignorate.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Căutarea a fost finalizată. Înlocuiri: {0}", "SSE.Controllers.LeftMenu.textSave": "Salvează", "SSE.Controllers.LeftMenu.textSearch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textSelectPath": "Introduceți un nume nou pentru salvarea copiei fișierului", "SSE.Controllers.LeftMenu.textSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textValues": "Valori", "SSE.Controllers.LeftMenu.textWarning": "Avertisment", "SSE.Controllers.LeftMenu.textWithin": "Între", "SSE.Controllers.LeftMenu.textWorkbook": "Registru de calcul", "SSE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON><PERSON><PERSON> titlu", "SSE.Controllers.LeftMenu.warnDownloadAs": "Dacă salvați în acest format de fișier, este posibil ca unele dintre caracteristici să se piardă, cu excepția textului.<br>Sunteți sigur că doriți să continuați?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "Formatul CSV nu acceptă salvarea fișierelor care conțin mai multe foi de calcul.<br>Pentru a păstra formatul selectat și a salva numai foaia de calcul curentă, apăsați Salvează.<br>Pentru a salva registrul de lucru curent, faceți clic pe Revocare și salvați în alt format de fișier.", "SSE.Controllers.Main.confirmAddCellWatches": "Această acţiune va adăuga {0} supravegheri în celule.<br><PERSON><PERSON><PERSON><PERSON> să continuați?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "Această acţiune va adăuga numai {0} supravegheri în celule din motivul economisirii spațiului de memorie.<br>Doriți să continuați?", "SSE.Controllers.Main.confirmMaxChangesSize": "Numărul comenzilor depășește limita prevăzută pentru serverul dvs.<br>Apăsați butonul Revocare pentru a anula ultima comanda dvs. sau apăsați butonul Continuare pentru a executa comanda în mod local (încărcați fișierul sau copiați conținutul pentru a se asigura că nu se pierde nimic).", "SSE.Controllers.Main.confirmMoveCellRange": "Celulele din zonă de destinație pot conține datele. Doriți să continuați?", "SSE.Controllers.Main.confirmPutMergeRange": "<PERSON><PERSON> sursă conțin celule imbinate.<br><PERSON><PERSON><PERSON><PERSON> au fost scindate înainte de lipire în tabel", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Formile din rând antet vor fi eliminate și transfomate în text static.<br><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>i să continuați?", "SSE.Controllers.Main.confirmReplaceHFPicture": "Doar o singură imagine poate fi inserată în fiecare secțiune a antetului.<br>Apăsați Înlocuire pentru a înlocui imaginea existentă.<br>Apăsați Păstrare pentru a păstra imaginea existentă.", "SSE.Controllers.Main.convertationTimeoutText": "Timpul de așteptare pentru conversie a expirat.", "SSE.Controllers.Main.criticalErrorExtText": "Apăsați OK pentru a vă întoarce la lista cu documente", "SSE.Controllers.Main.criticalErrorTitle": "Eroare", "SSE.Controllers.Main.downloadErrorText": "Descărcare <PERSON>.", "SSE.Controllers.Main.downloadTextText": "Descărcare foaie de calcul...", "SSE.Controllers.Main.downloadTitleText": "Descărcare foaie de calcul", "SSE.Controllers.Main.errNoDuplicates": "Valorile dublate nu au fost găsite.", "SSE.Controllers.Main.errorAccessDeny": "Nu aveți dreptul să efectuați acțiunea pe care doriți.<br>Contactați administratorul dumneavoastră de Server Documente.", "SSE.Controllers.Main.errorArgsRange": "<PERSON><PERSON><PERSON> în formulă.<br><PERSON><PERSON><PERSON> argument incorectă.", "SSE.Controllers.Main.errorAutoFilterChange": "Operațiunea nu este permisă deoarece este o încercare de a deplasa celulele în tabel din foaia de calcul dvs.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Operațiunea nu poate fi efectuată pentru celulele selectate deaorece nu puteți deplasa o parte din tabel.<br>Selectați o altă zonă de date pentru mutarea întregului tabel și încercați din nou.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Operațiunea nu poare fi efectuată pentru celulele selectate.<br>Selectați o altă zonă de date uniformă și încercați din nou.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Operațiunea nu poate fi efectuată deoarece zonă conține celule filtrate.<br>Reafișați elementele filtrate și încercați din nou.", "SSE.Controllers.Main.errorBadImageUrl": "URL-ul imaginii incorectă", "SSE.Controllers.Main.errorCalculatedItemInPageField": "Imposibil de adăugat sau de modificat acest element. Acest câmp este inclus în filtrele din raportul PivotTable.", "SSE.Controllers.Main.errorCannotPasteImg": "Imposibil de lipit imaginea din clipboardul, dar puteți să o salvați pe dispozitivul dvs și să o inserați de acolo, sau puteți să copiați imaginea fără text și să o lipiți în foaia de calcul.", "SSE.Controllers.Main.errorCannotUngroup": "Imposibil de anulat grupare. Pentru a crea o schiță, selectați rândurile sau coloanele de detalii și le grupați.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Nu puteți folosi această comandă într-o foaie de calcul protejată. Ca să o folosiți, protejarea foii trebuie anulată.<br> Introducerea parolei poate fi necesară.", "SSE.Controllers.Main.errorChangeArray": "Nu puteți modifica parte dintr-o matrice.", "SSE.Controllers.Main.errorChangeFilteredRange": "Operațiunea va afecta zonă filtrată din foaia de calcul.<br>Pentru a termina operațiunea dezactivați filtrarea automată. ", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "<PERSON><PERSON><PERSON> sau diagrama pe care încercați să schimbați este din foaia de calcul protejată<br>Dezactivați protejarea foii de calcul.Este posibil să fie necesară introducerea parolei.", "SSE.Controllers.Main.errorCircularReference": "Există una sau mai multe referințe circulare în formula care în mod direct sau indirect face referire la celula în care se află.<br> Încercați să eliminați sau să schimbați aceste referințe, sau să mutați formulele în celule diferite.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Conexiunea la server a fost pierdută. Deocamdată, imposibil de editat documentul.", "SSE.Controllers.Main.errorConnectToServer": "Salvarea documentului nu poate fi finalizată. Verificați configurarea conexeunii sau contactaţi administratorul dumneavoastră de reţea<br><PERSON><PERSON><PERSON> faceți clic pe OK, vi se va solicita să descărcați documentul.", "SSE.Controllers.Main.errorConvertXml": "Fișierul este în format neacceptat.<br>XML Spreadsheet 2003 este unicul format care poate fi utilizat.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Această comandă nu poate fi aplicată la selecții multiple<br>Selectați o singură zonă și încercați din nou.", "SSE.Controllers.Main.errorCountArg": "<PERSON><PERSON><PERSON> în formulă.<br><PERSON><PERSON><PERSON><PERSON> incorect de argumente.", "SSE.Controllers.Main.errorCountArgExceed": "<PERSON><PERSON>re în formulă.<br><PERSON><PERSON><PERSON><PERSON><PERSON> de argumente a fost depășit.", "SSE.Controllers.Main.errorCreateDefName": "Zone denumite existente nu pot fi editate, dar nici cele noi nu pot fi create<br>de<PERSON><PERSON>e unele dintre acestea sunt editate în momentul de față.", "SSE.Controllers.Main.errorCreateRange": "Imposibil de modificat zonele protejate existente sau de creat zone noi<br>în acest moment întrucât unele dintre ele sunt în curs de editare.", "SSE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON>re externă.<br><PERSON><PERSON><PERSON> de conectare la baza de date. <PERSON><PERSON><PERSON> eroar<PERSON> persistă, contactați Serviciul de Asistență Clienți.", "SSE.Controllers.Main.errorDataEncrypted": "Modificările primite sunt criptate, decriptarea este imposibilă.", "SSE.Controllers.Main.errorDataRange": "Zonă de date incorectă.", "SSE.Controllers.Main.errorDataValidate": "Valoarea introdusă nu este validă<br>Unul dintre utilizatori a restricționat valorile pe care utilizatorii le introduc într-o celulă. ", "SSE.Controllers.Main.errorDefaultMessage": "Codul de eroare: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Dvs încercați să ștergeți o coloană care conține celule blocate. Este imposibil să ștergeți celulele blocate până când foaia de calcul rămâne protejată.<br>Trebuie să anulați protecția foii de calcul ca să ștergeți celula blocată. Este posibil să fie necesară introducerea parolei.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Dvs încercați să ștergeți un rând care conține celule blocate. Este imposibil să ștergeți celulele blocate până când foaia de calcul rămâne protejată.<br>Trebuie să anulați protecția foii de calcul ca să ștergeți celula blocată. Este posibil să fie necesară introducerea parolei.", "SSE.Controllers.Main.errorDependentsNoFormulas": "Comanda T<PERSON>are dependenţe nu a găsit nicio formulă care face referire la celula activă.", "SSE.Controllers.Main.errorDirectUrl": "Verificați linkul la document.<br>Trebuie să fie un link direct spre fișierul de încărcat.", "SSE.Controllers.Main.errorEditingDownloadas": "S-a produs o eroare în timpul editării documentului.<br>Pentru copierea de rezervă pe PC utilizați opțiunea Descărcare ca", "SSE.Controllers.Main.errorEditingSaveas": "S-a produs o eroare în timpul editării documentului.<br>Pentru copierea de rezervă pe PC utilizați opțiunea Salvare ca...", "SSE.Controllers.Main.errorEditView": "Editările în vizualizarea curentă nu pot fi efectuate dar și o vizualizare nouă nu puteți crea deoarece unii dintre ei sunt editate în acest moment.", "SSE.Controllers.Main.errorEmailClient": "Client de poștă electronică imposibil de găsit. ", "SSE.Controllers.Main.errorFilePassProtect": "Fișierul este protejat cu parolă și deaceea nu poate fi deschis.", "SSE.Controllers.Main.errorFileRequest": "<PERSON><PERSON>re externă.<br><PERSON><PERSON><PERSON> la trimiterea solicitării de fișier. Dacă eroarea persistă, contactați Serviciul de Asistență Clienți.", "SSE.Controllers.Main.errorFileSizeExceed": "Dimensiunea fișierului depășește limita permisă de serverul Dvs.<br><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, contactați administratorul dumneavoastră de Server Documente.", "SSE.Controllers.Main.errorFileVKey": "Eroare externă.<br>Cheia de securitate incorectă. Dac<PERSON> eroarea persistă, contactați Seviciul de Asistență Clienți.", "SSE.Controllers.Main.errorFillRange": "Completarea celulelor selectate nu este posibilă.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> toate coloanele îmbinate la aceeași dimensiune.", "SSE.Controllers.Main.errorForceSave": "S-a produs o eroare în timpul salvării fișierului. Pentru copierea de rezervă pe PC utilizați opțiunea Descărcare ca... sau încercați din nou mai târziu.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Imposibil de adăugat o formulă pentru un nume de element sau un nume de câmp în raportul PivotTable.", "SSE.Controllers.Main.errorFormulaName": "Eroare în formulă.<br><PERSON>umele formulei incorect.", "SSE.Controllers.Main.errorFormulaParsing": "Eroare internă de parsare cu formulă.", "SSE.Controllers.Main.errorFrmlMaxLength": "Lungimea conținutului formulei depășește limita maximă de 8192 caractere.<br>Editați-o și încercați din nou.", "SSE.Controllers.Main.errorFrmlMaxReference": "Nu puteți introduce acestă formula deoarece are prea multe valori,<br>referințe de celulă, și/sau nume.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Valorile de tip text în o formulă pot conține maxim 255 caractere.<br>Utilizați funcția CONCATENATE sau operatorul de calcul (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Funcția se referă la o foaie inexistentă.<br>Verificați datele și încercați din nou.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Operațiunea nu poate fi efectuată pentru zonă de celule selectată.<br>Selectați o zonă de celule în așa fel încât primul rând din tabel să coincide<br>și tabelul rezultat să suprapune peste tabelul curent.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Operațiunea nu poate fi efectuată pentru zonă de celule selectată.<br>Selectați o zonă de celule care nu conține alte tabele.", "SSE.Controllers.Main.errorInconsistentExt": "Eroare la deschiderea fișierului.<br>Conținutul fișierului nu corespunde cu extensia numelui de fișier.", "SSE.Controllers.Main.errorInconsistentExtDocx": "Eroare la deschiderea fișierului.<br>Conținutul fișierului corespunde unui format de document text (ex. docx), dar extensia numelui de fișier nu se potrivește: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "Eroare la deschiderea fișierului.<br>Conținutul fișierului corespunde unuia dintre următoarele formate: pdf/djvu/xps/oxps, dar extensia numelui de fișier nu se potrivește: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "Eroare la deschiderea fișierului.<br>Conținutul fișierului corespunde unui format de prezentare (ex. pptx), dar extensia numelui de fișier nu se potrivește: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "Eroare la deschiderea fișierului.<br>Conținutul fișierului corespunde unui format de foaie de calcul (ex. xlsx), dar extensia numelui de fișier nu se potrivește: %1.", "SSE.Controllers.Main.errorInvalidRef": "Introduceți numele din selecție corect sau o referință validă de accesat.", "SSE.Controllers.Main.errorKeyEncrypt": "Descriptor cheie nerecunoscut", "SSE.Controllers.Main.errorKeyExpire": "Descriptor cheie a expirat", "SSE.Controllers.Main.errorLabledColumnsPivot": "Pentru a crea o tabelă Pivot, datele trebuie să fie organizate sub formă de listă care conține etichete pentru fiecare coloană. ", "SSE.Controllers.Main.errorLoadingFont": "Fonturile nu sunt încărcate.<br>Contactați administratorul dvs de Server Documente.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Referința de localizare sau zona de date nu este validă.", "SSE.Controllers.Main.errorLockedAll": "Operațiunea nu poate fi efectuată deoarece foaia a fost blocată de către un alt utilizator.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "Una dintre celulele incluse în procesul de căutare a obiectivului a fost modificată de un alt utilizator.", "SSE.Controllers.Main.errorLockedCellPivot": "Nu puteți modifica datele manipulând tabel Pivot.", "SSE.Controllers.Main.errorLockedWorksheetRename": "Deocamdată, nu puteți redenumi foaia deoarece foaia este redenumită de către un alt utilizator", "SSE.Controllers.Main.errorMaxPoints": "Numărul maxim de puncte de date pe serie în diagramă este limitat la 4096.", "SSE.Controllers.Main.errorMoveRange": "O parte din celulă îmbinată nu poate fi modificată ", "SSE.Controllers.Main.errorMoveSlicerError": "Slicere din tabel nu pot fi copiate dintr-un registru de calcul în altul.<br>Încercați din nou și selectați întregul tabel și slicere.", "SSE.Controllers.Main.errorMultiCellFormula": "Formule de matrice cu mai multe celule nu sunt permise în tabele.", "SSE.Controllers.Main.errorNoDataToParse": "<PERSON><PERSON> pentru parsare nu au fost selectate.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "Dacă una sau mai multe tabele dinamice conțin elemente calculate, niciun câmp nu poate fi utilizat de două sau mai multe ori în zona de date, sau în zona de date și în altă zonă în același timp.", "SSE.Controllers.Main.errorOpenWarning": "O formulă din fișier depășește limita maximă de 8192 caractere.<br>Formula a fost eliminată.", "SSE.Controllers.Main.errorOperandExpected": "Sintaxa funcției incorectă. Verificați încadrarea în paranteze - '(' sau ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "Parola introdusă este incorectă.<br>Verificaţi dacă nu este activată tasta CAPS LOCK și vă asigurați că utilizați introducerea corectă a majusculelor.", "SSE.Controllers.Main.errorPasteInPivot": "Este imposibilă modificarea celulelor selectate deoarece va afecta tabelul pivot.<br>Folosiți lista de câmpuri pentru a modifica raportul.", "SSE.Controllers.Main.errorPasteMaxRange": "Zona de copiere nu corespunde zonei de lipire.<br>Pentru lipirea celulelor copiate, selectați o zonă de aceeași demensiune sau faceți clic pe prima celula din rând.", "SSE.Controllers.Main.errorPasteMultiSelect": "Operaţia nu poate fi efectuată asupra zonelor selectate multiple.<br>Selectați o singură zonă și încercați încă o dată.", "SSE.Controllers.Main.errorPasteSlicerError": "Slicere din tabel nu pot fi copiate dintr-un registru de calcul în altul.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Nume de câmp în tabelul pivot deja existent.", "SSE.Controllers.Main.errorPivotGroup": "Obiectele selectate nu pot fi combinate într-un grup.", "SSE.Controllers.Main.errorPivotOverlap": "Un raport Pivot Table nu poate suprapune un tabel.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Raportul Pivot Table a fost salvat fără datele subiacente.<br><PERSON><PERSON><PERSON> clic pe butonul Actualizare ca raportul să fie actualizat.", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "<PERSON><PERSON><PERSON> precedente nu a găsit nicio formulă care face referire la celula activă.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "<PERSON>, imprimarea a 1500 de pagini deodată nu este posibilă la versiunea curentă<br>Această restricție va fi eliminată într-o versiunea nouă.", "SSE.Controllers.Main.errorProcessSaveResult": "Salverea a eșuat", "SSE.Controllers.Main.errorProtectedRange": "Modificarea acestei zone nu este permisă.", "SSE.Controllers.Main.errorSaveWatermark": "Fișierul conține o inscripționare care este legată de un alt domeniu.<br>Pentru a o face vizibilă în fișierul PDF, actualizați imaginea cu inscripționare astfel încât să fie legată la același domeniu la care este legat documentul dvs, sau încărcați-o de pe calculatorul dvs.", "SSE.Controllers.Main.errorServerVersion": "Editorul a fost actualizat. Pagina va fi reîmprospătată pentru a aplica această actualizare.", "SSE.Controllers.Main.errorSessionAbsolute": "Sesiunea de editare a expirat. Încercați să reîmprospătați pagina.", "SSE.Controllers.Main.errorSessionIdle": "Acțiunile de editare a documentului nu s-au efectuat de ceva timp. Încercați să reîmprospătați pagina.", "SSE.Controllers.Main.errorSessionToken": "Conexeunea la server s-a întrerupt. Încercați să reîmprospătati pagina.", "SSE.Controllers.Main.errorSetPassword": "Setarea parolei eșuată.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Referința de localizare nu este validă deoarece celulele nu sunt dintr-o singură coloană sau rând.<br>Selectați celulele dintr-o singură coloană sau rând.", "SSE.Controllers.Main.errorStockChart": "Sortarea rândurilor în ordinea incorectă. Pentru crearea unei diagrame de stoc, datele în foaie trebuie sortate în ordinea următoare:<br> prețul de deschidere, prețul maxim, prețul minim, prețul de închidere.", "SSE.Controllers.Main.errorToken": "Token de securitate din document este format în mod incorect.<br>Contactați administratorul dvs. de Server Documente.", "SSE.Controllers.Main.errorTokenExpire": "Token de securitate din document a expirat.<br>Contactați administratorul dvs. de Server Documente.", "SSE.Controllers.Main.errorUnexpectedGuid": "Eroare externă.<br>GUID neașteptat. Dacă eroarea persistă, contactați Seviciul de Asistență Clienți.", "SSE.Controllers.Main.errorUpdateVersion": "Versiunea fișierului s-a modificat. Pagina va fi reîmprospătată.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Conexiunea s-a restabilit și versiunea fișierului s-a schimbat.<br><PERSON>nainte de a continua, fișierul trebuie descărcat sau conținutul fișierului copiat ca să vă asigurați că nimic nu e pierdut, apoi reîmprospătați această pagină.", "SSE.Controllers.Main.errorUserDrop": "Fișierul nu poate fi accesat deocamdată.", "SSE.Controllers.Main.errorUsersExceed": "Limita de utilizatori stipulată de planul tarifar a fost depășită", "SSE.Controllers.Main.errorViewerDisconnect": "Conexiunea a fost pierdută. Încă mai puteți vizualiza documentul,<br>dar nu și să-l descărcați sau imprimați până când conexiunea se restabilește și pagina se reîmprospătează.", "SSE.Controllers.Main.errorWrongBracketsCount": "<PERSON><PERSON>re în formulă.<br><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> paranteze incorect.", "SSE.Controllers.Main.errorWrongOperator": "Eroare în formulă. Operator necorespunzător.<br>Corectați er<PERSON>.", "SSE.Controllers.Main.errorWrongPassword": "Parola introdusă este incorectă.", "SSE.Controllers.Main.errRemDuplicates": "Valori dublate au fost identificate și eliminate: {0}, valorile unice rămase: {1}.", "SSE.Controllers.Main.leavePageText": "Nu ați salvat modificările din foaia de calcul. Faceți clic pe  Rămâi în pagină și apoi pe Salvare dacă doriți să le salvați. Faceți clic pe Părăsește aceasta pagina ca să renunțați la toate modificările nesalvate.", "SSE.Controllers.Main.leavePageTextOnClose": "Toate modificările nesalvate din foaia de calcul vor fi pierdute.<br> <PERSON><PERSON><PERSON> a le salva, faceți clic pe Revocare și apoi pe Salvare. Apăsați OK dacă doriți să renunțați la modificările nesalvate.", "SSE.Controllers.Main.loadFontsTextText": "Încărcarea datelor...", "SSE.Controllers.Main.loadFontsTitleText": "<PERSON>nc<PERSON><PERSON><PERSON> datelor", "SSE.Controllers.Main.loadFontTextText": "Încărcarea datelor...", "SSE.Controllers.Main.loadFontTitleText": "Încărcare date", "SSE.Controllers.Main.loadImagesTextText": "Încărcarea imaginilor...", "SSE.Controllers.Main.loadImagesTitleText": "<PERSON>nc<PERSON><PERSON><PERSON> imagini", "SSE.Controllers.Main.loadImageTextText": "Încărcarea imaginii...", "SSE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON> imagine", "SSE.Controllers.Main.loadingDocumentTitleText": "Încărcare foaie de calcul", "SSE.Controllers.Main.notcriticalErrorTitle": "Avertisment", "SSE.Controllers.Main.openErrorText": "Eroare la deschiderea fișierului.", "SSE.Controllers.Main.openTextText": "Deschiderea foii de calcul...", "SSE.Controllers.Main.openTitleText": "Deschidere foaie de calcul", "SSE.Controllers.Main.pastInMergeAreaError": "O parte din celulă îmbinată nu poate fi modificată ", "SSE.Controllers.Main.printTextText": "Imprimarea foii de lucru...", "SSE.Controllers.Main.printTitleText": "Imprimarea foii de calcul", "SSE.Controllers.Main.reloadButtonText": "Reîmprospătar<PERSON> pagina", "SSE.Controllers.Main.requestEditFailedMessageText": "La moment, altcineva lucrează la documentul. Vă rugăm să încercați mai târziu.", "SSE.Controllers.Main.requestEditFailedTitleText": "Acces refuzat", "SSE.Controllers.Main.saveErrorText": "S-a produs o eroare în timpul încercării de salvare a fișierului.", "SSE.Controllers.Main.saveErrorTextDesktop": "Salvarea sau crearea fișierului imposibilă.<br><PERSON><PERSON><PERSON><PERSON> posibile: <br>1. Fișierul s-a deschis doar în citire. <br>2. Fișierul este editat de alt utilizator. <br>3. Hard-disk-ul ori este plin, ori are un defect anume.", "SSE.Controllers.Main.saveTextText": "Salvarea foii de calcul...", "SSE.Controllers.Main.saveTitleText": "Salvarea foaie de calcul", "SSE.Controllers.Main.scriptLoadError": "Conexeunea e prea lentă și unele elemente nu se încarcă. Încercați să reîmprospătati pagina.", "SSE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textApplyAll": "Se aplică pentru toate ecuații", "SSE.Controllers.Main.textBuyNow": "Vizitarea site-ul Web", "SSE.Controllers.Main.textChangesSaved": "Toate modificările au fost salvate", "SSE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textCloseTip": "Faceți clic pentru a închide sfatul", "SSE.Controllers.Main.textConfirm": "Confirmare", "SSE.Controllers.Main.textConnectionLost": "Incercare de conectare. Vă rugăm să verificați setările conexiunii.", "SSE.Controllers.Main.textContactUs": "Contactați Departamentul de Vânzări", "SSE.Controllers.Main.textContinue": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textConvertEquation": "Această ecuație a fost creată în versiunea mai veche a editorului de ecuații, care nu mai este acceptată. Dacă doriți să o editați, trebuie să o convertiți în formatul Office Math ML.<br>Doriți să o convertiți acum?", "SSE.Controllers.Main.textCustomLoader": "Vă rugăm să rețineți că conform termenilor de licență nu aveți dreptul să modificați programul de încărcare.<br>Pentru obținerea unei cotații de preț vă rugăm să contactați Departamentul de Vânzari.", "SSE.Controllers.Main.textDisconnect": "Conexiune pierdu<PERSON>", "SSE.Controllers.Main.textFillOtherRows": "Completează alte rânduri", "SSE.Controllers.Main.textFormulaFilledAllRows": "{0} r<PERSON><PERSON><PERSON> care conțin datele sunt completate cu formule. Completarea altor rânduri necompletate poate dura câteva minute.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Primele {0} r<PERSON><PERSON><PERSON> sunt completate cu formule. Completarea altor rân<PERSON>ri necompletate poate dura câteva minute.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "<PERSON>umai primele {0} rânduri care conțin datele sunt completate cu formule pentru economisirea memoriei. Încă {1} rânduri din această foaie conțin datele. Le puteți completa manual.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Numai primele {0} r<PERSON><PERSON><PERSON> sunt completate cu formule pentru economisirea memoriei. Alte rânduri din această foaie nu conțin datele.", "SSE.Controllers.Main.textGuest": "Invitat", "SSE.Controllers.Main.textHasMacros": "Fișierul conține macrocomenzi.<br><PERSON><PERSON><PERSON><PERSON> să le rulați?", "SSE.Controllers.Main.textKeep": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textLearnMore": "Aflați mai multe", "SSE.Controllers.Main.textLoadingDocument": "Încărcare foaie de calcul", "SSE.Controllers.Main.textLongName": "Numărul maxim de caractere dintr-un nume este 128 caractere.", "SSE.Controllers.Main.textNeedSynchronize": "Aveți actualizări disponibile", "SSE.Controllers.Main.textNo": "<PERSON>u", "SSE.Controllers.Main.textNoLicenseTitle": "Ați atins limita stabilită de licență", "SSE.Controllers.Main.textPaidFeature": "Funcția contra plată", "SSE.Controllers.Main.textPleaseWait": "Operațiunea durează mai mult decât s-a anticipat. Vă rugăm să așteptați...", "SSE.Controllers.Main.textReconnect": "Conexiunea este restabilită", "SSE.Controllers.Main.textRemember": "Salveaz<PERSON> setă<PERSON>e pentru toate fișier<PERSON>", "SSE.Controllers.Main.textRememberMacros": "<PERSON><PERSON><PERSON>ți alegerea mea pentru toate macrocomenzi", "SSE.Controllers.Main.textRenameError": "Numele utilizatorului trebuie completat.", "SSE.Controllers.Main.textRenameLabel": "Introduceți numele pentru lucrul în colaborare", "SSE.Controllers.Main.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textRequestMacros": "O macrocomandă trimite o solicitare URL. Doriți să permiteți ca solicitarea să fie trimisă către %1?", "SSE.Controllers.Main.textShape": "Forma", "SSE.Controllers.Main.textStrict": "<PERSON><PERSON><PERSON> <PERSON>", "SSE.Controllers.Main.textText": "Text", "SSE.Controllers.Main.textTryQuickPrint": "Dvs v-ați ales opțiunea de Imprimare rapidă: imprimanta este setată se inprime un document întreg este ultima imprimantă utilizată sau imprimantă implicită.<br><PERSON><PERSON><PERSON><PERSON> să continuați?", "SSE.Controllers.Main.textTryUndoRedo": "Funcții Anulare/Refacere sunt dezactivate în modul Rapid de editare colaborativă.<br><PERSON><PERSON><PERSON> clic pe Modul strict ca să comutați la modul Strict de editare colaborativă și să nu intrați în conflict cu alte persoane. Toate modificările vor fi trimise numai după ce le salvați. Ulilizati Setări avansate ale editorului ca să comutați între moduri de editare colaborativă. ", "SSE.Controllers.Main.textTryUndoRedoWarn": "Funcții Anulare/Refacere sunt dezactivate în modul Rapid de editare colaborativă.", "SSE.Controllers.Main.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textUpdateVersion": "Imposibil de editat documentul acum.<br>Incercare de a actualiza fișierul, vă rugăm să așteptați...", "SSE.Controllers.Main.textUpdating": "Actualizare", "SSE.Controllers.Main.textYes": "Da", "SSE.Controllers.Main.titleLicenseExp": "Licența a expirat", "SSE.Controllers.Main.titleLicenseNotActive": "Licența nu este activă", "SSE.Controllers.Main.titleServerVersion": "Editorul a fost actualizat", "SSE.Controllers.Main.titleUpdateVersion": "Versiunea s-a modificat", "SSE.Controllers.Main.txtAccent": "Accent", "SSE.Controllers.Main.txtAll": "(Toate)", "SSE.Controllers.Main.txtArt": "Textul dvs. aici", "SSE.Controllers.Main.txtBasicShapes": "Forme de bază", "SSE.Controllers.Main.txtBlank": "(necompletat)", "SSE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtByField": "%1 din %2", "SSE.Controllers.Main.txtCallouts": "Explicații", "SSE.Controllers.Main.txtCharts": "Diagrame", "SSE.Controllers.Main.txtClearFilter": "Golire filtru", "SSE.Controllers.Main.txtColLbls": "Etichete coloană", "SSE.Controllers.Main.txtColumn": "Coloană", "SSE.Controllers.Main.txtConfidential": "Confidențial", "SSE.Controllers.Main.txtDate": "Data", "SSE.Controllers.Main.txtDays": "Zile", "SSE.Controllers.Main.txtDiagramTitle": "Titlu diagramă", "SSE.Controllers.Main.txtEditingMode": "Setare modul de editare...", "SSE.Controllers.Main.txtErrorLoadHistory": "Încărcarea istoricului a eșuat", "SSE.Controllers.Main.txtFiguredArrows": "Săgeți în forme diferite", "SSE.Controllers.Main.txtFile": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtGrandTotal": "<PERSON>uri generale", "SSE.Controllers.Main.txtGroup": "Grup", "SSE.Controllers.Main.txtHours": "Oră", "SSE.Controllers.Main.txtInfo": "Informații", "SSE.Controllers.Main.txtLines": "<PERSON><PERSON>", "SSE.Controllers.Main.txtMath": "Matematice", "SSE.Controllers.Main.txtMinutes": "Minute", "SSE.Controllers.Main.txtMonths": "Lună", "SSE.Controllers.Main.txtMultiSelect": "Selecții multiple", "SSE.Controllers.Main.txtNone": "Niciunul", "SSE.Controllers.Main.txtOr": "%1 sau %2", "SSE.Controllers.Main.txtPage": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPageOf": "Pagina %1 din %2", "SSE.Controllers.Main.txtPages": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPicture": "Imagine", "SSE.Controllers.Main.txtPivotTable": "PivotTable", "SSE.Controllers.Main.txtPreparedBy": "Pregătit <PERSON>", "SSE.Controllers.Main.txtPrintArea": "Zonă_Imprimare", "SSE.Controllers.Main.txtQuarter": "<PERSON><PERSON>", "SSE.Controllers.Main.txtQuarters": "Trimestre", "SSE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRow": "Rând", "SSE.Controllers.Main.txtRowLbls": "Etichete de rând", "SSE.Controllers.Main.txtSaveCopyAsComplete": "Copia fișierului a fost salvată cu succes", "SSE.Controllers.Main.txtScheme_Aspect": "Aspect", "SSE.Controllers.Main.txtScheme_Blue": "Albastru", "SSE.Controllers.Main.txtScheme_Blue_Green": "Albastru verde", "SSE.Controllers.Main.txtScheme_Blue_II": "Albastru II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "Albastru cald", "SSE.Controllers.Main.txtScheme_Grayscale": "<PERSON><PERSON><PERSON> de gri", "SSE.Controllers.Main.txtScheme_Green": "Verde", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Verde galben", "SSE.Controllers.Main.txtScheme_Marquee": "Text defilant", "SSE.Controllers.Main.txtScheme_Median": "Median", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Portocal<PERSON>", "SSE.Controllers.Main.txtScheme_Orange_Red": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Paper": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Red": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Red_Orange": "R<PERSON><PERSON><PERSON> port<PERSON>aliu", "SSE.Controllers.Main.txtScheme_Red_Violet": "Roșu violet", "SSE.Controllers.Main.txtScheme_Slipstream": "Integrare în flux", "SSE.Controllers.Main.txtScheme_Violet": "Violet", "SSE.Controllers.Main.txtScheme_Violet_II": "Violet II", "SSE.Controllers.Main.txtScheme_Yellow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSeconds": "Secunde", "SSE.Controllers.Main.txtSeries": "Serie", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Rând de explicație 1 (bordură și bară de accentuare)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Rând de explicație 2 (bordură și bară de accentuare)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Rând de explicație 3 (bordură și bară de accentuare)", "SSE.Controllers.Main.txtShape_accentCallout1": "Rând de explicație 1 (cu bară de accentuare)", "SSE.Controllers.Main.txtShape_accentCallout2": "Rând de explicație 2 (cu bară de accentuare)", "SSE.Controllers.Main.txtShape_accentCallout3": "Rând de explicație 3 (cu bară de accentuare)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Butonul Înapoi sau Anterior", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Butonul de pornire", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Butonul liber", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Butonul Document", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Butonul End", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Butonul înainte sau următor", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Butonul Asistență", "SSE.Controllers.Main.txtShape_actionButtonHome": "Butonul Acasă", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Butonul Informații", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Butonul Video", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Butonul Întoarcere", "SSE.Controllers.Main.txtShape_actionButtonSound": "Buton de volum", "SSE.Controllers.Main.txtShape_arc": "Arc", "SSE.Controllers.Main.txtShape_bentArrow": "Săgeată îndoită", "SSE.Controllers.Main.txtShape_bentConnector5": "Conector colțit", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Conector colțit cu săgeată", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Conector colțit cu săgeată dublă", "SSE.Controllers.Main.txtShape_bentUpArrow": "Săgeată îndoită în sus", "SSE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_blockArc": "Arc plin", "SSE.Controllers.Main.txtShape_borderCallout1": "Rând de explicație 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Rând de explicație 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Rând de explicație 3", "SSE.Controllers.Main.txtShape_bracePair": "Acolad<PERSON>", "SSE.Controllers.Main.txtShape_callout1": "Rând de explicație 1 (<PERSON><PERSON><PERSON><PERSON> bordur<PERSON>)", "SSE.Controllers.Main.txtShape_callout2": "Rând de explicație 2 (<PERSON><PERSON><PERSON><PERSON> bord<PERSON>)", "SSE.Controllers.Main.txtShape_callout3": "Rând de explicație 3 (<PERSON><PERSON><PERSON><PERSON> bord<PERSON>)", "SSE.Controllers.Main.txtShape_can": "Cilindru", "SSE.Controllers.Main.txtShape_chevron": "Chevron", "SSE.Controllers.Main.txtShape_chord": "Acord", "SSE.Controllers.Main.txtShape_circularArrow": "Săgeată circulară", "SSE.Controllers.Main.txtShape_cloud": "Nor", "SSE.Controllers.Main.txtShape_cloudCallout": "Explicație nor", "SSE.Controllers.Main.txtShape_corner": "Forma de L", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Conector curbat", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Conector săgeat<PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Conector curbat cu săgeată dublă", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Săgeată curbată în jos", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Săgeată curbată la stânga", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Săgeată curbată la dreapta", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Săgeată curbată în sus", "SSE.Controllers.Main.txtShape_decagon": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_diagStripe": "Diagonale", "SSE.Controllers.Main.txtShape_diamond": "Romb", "SSE.Controllers.Main.txtShape_dodecagon": "Dodecaedru", "SSE.Controllers.Main.txtShape_donut": "Tor", "SSE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_downArrow": "Săgeată în jos", "SSE.Controllers.Main.txtShape_downArrowCallout": "Explicație cu săgeata în jos", "SSE.Controllers.Main.txtShape_ellipse": "Oval", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Panglică curbată în jos", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Panglică curbată în sus", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Schemă logică: Proces alternativ", "SSE.Controllers.Main.txtShape_flowChartCollate": "Sistemă logică: Colaționare", "SSE.Controllers.Main.txtShape_flowChartConnector": "Sistemă logică: Conector", "SSE.Controllers.Main.txtShape_flowChartDecision": "Sistemă logică: Decizie", "SSE.Controllers.Main.txtShape_flowChartDelay": "Sistemă logică: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Sistemă logică: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDocument": "Sistemă logică: Document", "SSE.Controllers.Main.txtShape_flowChartExtract": "Sistemă logică: Extragere", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Sistemă logică: Date", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Sistemă logică: <PERSON><PERSON><PERSON> internă", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Sistemă logică: Disc magnetic", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Sistemă logică: Memorie cu acces direct", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Sistemă logică: Memorie cu acces secvențial", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Sistemă logică: Introducere manuală", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Sistemă logică: Operație manuală", "SSE.Controllers.Main.txtShape_flowChartMerge": "Sistemă logică: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Sistemă logică: Document multiplu", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Sistemă logică: Conector în afara paginii", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Sistemă logică: Date stocate", "SSE.Controllers.Main.txtShape_flowChartOr": "Sistemă logică: <PERSON>u", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Sistemă logică: Proces predefinit", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Sistemă logică: Pregă<PERSON>re", "SSE.Controllers.Main.txtShape_flowChartProcess": "Sistemă logică: Proces", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Schemă logică: Cartelă", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Sistemă logică: <PERSON><PERSON> perforată", "SSE.Controllers.Main.txtShape_flowChartSort": "Sistemă logică: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Sistemă logică: Juncț<PERSON>ne de însumare", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Sistemă logică: Terminator", "SSE.Controllers.Main.txtShape_foldedCorner": "Colț îndoit", "SSE.Controllers.Main.txtShape_frame": "Cadru", "SSE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON><PERSON><PERSON> cadru", "SSE.Controllers.Main.txtShape_heart": "Inimă", "SSE.Controllers.Main.txtShape_heptagon": "Heptagon", "SSE.Controllers.Main.txtShape_hexagon": "Hexagon", "SSE.Controllers.Main.txtShape_homePlate": "Pentagon", "SSE.Controllers.Main.txtShape_horizontalScroll": "Bară de defilare orizontală", "SSE.Controllers.Main.txtShape_irregularSeal1": "Explosie 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Explosie 2", "SSE.Controllers.Main.txtShape_leftArrow": "Săgeată la stângă", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Explicație cu săgeta spre stânga", "SSE.Controllers.Main.txtShape_leftBrace": "Acoladă stânga", "SSE.Controllers.Main.txtShape_leftBracket": "Paranteză stânga", "SSE.Controllers.Main.txtShape_leftRightArrow": "Săgeată stânga-dreapta", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Explicație cu săgeata spre stânga-dreapta", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Săgeată stânga-dreapta-sus", "SSE.Controllers.Main.txtShape_leftUpArrow": "Săgeată stânga-sus", "SSE.Controllers.Main.txtShape_lightningBolt": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "Săgeata", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Săgeată dublă", "SSE.Controllers.Main.txtShape_mathDivide": "Împărț<PERSON>", "SSE.Controllers.Main.txtShape_mathEqual": "Egal", "SSE.Controllers.Main.txtShape_mathMinus": "Minus", "SSE.Controllers.Main.txtShape_mathMultiply": "Înmulțire", "SSE.Controllers.Main.txtShape_mathNotEqual": "Nu este egal", "SSE.Controllers.Main.txtShape_mathPlus": "Plus", "SSE.Controllers.Main.txtShape_moon": "Lună", "SSE.Controllers.Main.txtShape_noSmoking": "Simbolul \"Nu\"", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Săgeată dreaptă crestată", "SSE.Controllers.Main.txtShape_octagon": "Octogon", "SSE.Controllers.Main.txtShape_parallelogram": "Paralelogram", "SSE.Controllers.Main.txtShape_pentagon": "Pentagon", "SSE.Controllers.Main.txtShape_pie": "Radială", "SSE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_plus": "Plus", "SSE.Controllers.Main.txtShape_polyline1": "Mâzg<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_polyline2": "Formă liberă", "SSE.Controllers.Main.txtShape_quadArrow": "Săgeată în patru puncte", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Explicație cu săgeată în patru puncte", "SSE.Controllers.Main.txtShape_rect": "Dreptunghi", "SSE.Controllers.Main.txtShape_ribbon": "Panglică în jos", "SSE.Controllers.Main.txtShape_ribbon2": "Panglică în sus", "SSE.Controllers.Main.txtShape_rightArrow": "Săgeată la dreapta", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Explicație cu săgeata spre dreapta", "SSE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON><PERSON><PERSON> dreapta", "SSE.Controllers.Main.txtShape_rightBracket": "Parantez<PERSON> dreapta", "SSE.Controllers.Main.txtShape_round1Rect": "Dreptunghi cu un colț rotunjit", "SSE.Controllers.Main.txtShape_round2DiagRect": "Dreptunghi cu colțuri rotunjite pe diagonală", "SSE.Controllers.Main.txtShape_round2SameRect": "Dreptunghi cu colțuri rotunjite pe aceeași latură", "SSE.Controllers.Main.txtShape_roundRect": "<PERSON><PERSON><PERSON><PERSON> rotunjit", "SSE.Controllers.Main.txtShape_rtTriangle": "Triunghi drept", "SSE.Controllers.Main.txtShape_smileyFace": "Față zâmbitoare", "SSE.Controllers.Main.txtShape_snip1Rect": "Dreptunghi cu un colț tăiat", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Dreptunghi cu colțuri tăiate pe diagonală", "SSE.Controllers.Main.txtShape_snip2SameRect": "Dreptunghi cu colțuri tăiate pe aceeași latură", "SSE.Controllers.Main.txtShape_snipRoundRect": "Dreptunghi cu un colț tăiat și rotunjit", "SSE.Controllers.Main.txtShape_spline": "Curbă", "SSE.Controllers.Main.txtShape_star10": "Stea cu 10 colțuri", "SSE.Controllers.Main.txtShape_star12": "Stea cu 12 colțuri", "SSE.Controllers.Main.txtShape_star16": "Stea cu 16 colțuri", "SSE.Controllers.Main.txtShape_star24": "Stea cu 24 colțuri", "SSE.Controllers.Main.txtShape_star32": "Stea cu 32 col<PERSON>uri", "SSE.Controllers.Main.txtShape_star4": "Stea cu 4 colțuri", "SSE.Controllers.Main.txtShape_star5": "Stea cu 5 colțuri", "SSE.Controllers.Main.txtShape_star6": "Stea cu 6 colțuri", "SSE.Controllers.Main.txtShape_star7": "Stea cu 7 colțuri", "SSE.Controllers.Main.txtShape_star8": "Stea cu 8 colțuri", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Săgeată dreapta vărgată", "SSE.Controllers.Main.txtShape_sun": "Soare", "SSE.Controllers.Main.txtShape_teardrop": "Lacrimă", "SSE.Controllers.Main.txtShape_textRect": "Casetă text", "SSE.Controllers.Main.txtShape_trapezoid": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_triangle": "Triunghi", "SSE.Controllers.Main.txtShape_upArrow": "Săgeată în sus", "SSE.Controllers.Main.txtShape_upArrowCallout": "Explicație cu săgeata în sus", "SSE.Controllers.Main.txtShape_upDownArrow": "Săgeată în sus-jos", "SSE.Controllers.Main.txtShape_uturnArrow": "Săgeată în U", "SSE.Controllers.Main.txtShape_verticalScroll": "Defilare pe verticală", "SSE.Controllers.Main.txtShape_wave": "Ondulare", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Explicație în oval", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Explicație în dreptunghi", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Explicație în dreptunghi rotunjit", "SSE.Controllers.Main.txtSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSlicer": "<PERSON>licer", "SSE.Controllers.Main.txtStarsRibbons": "Stele și forme ondulate", "SSE.Controllers.Main.txtStyle_Bad": "E<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Calculation": "Calculare", "SSE.Controllers.Main.txtStyle_Check_Cell": "Verificarea celulei", "SSE.Controllers.Main.txtStyle_Comma": "Virgulă", "SSE.Controllers.Main.txtStyle_Currency": "Monedă", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Text explicativ", "SSE.Controllers.Main.txtStyle_Good": "<PERSON>un", "SSE.Controllers.Main.txtStyle_Heading_1": "Titlu 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Titlu 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Titlu 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Titlu 4", "SSE.Controllers.Main.txtStyle_Input": "Intrare", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Celulă legată", "SSE.Controllers.Main.txtStyle_Neutral": "Neutr<PERSON>", "SSE.Controllers.Main.txtStyle_Normal": "Normal", "SSE.Controllers.Main.txtStyle_Note": "Notă", "SSE.Controllers.Main.txtStyle_Output": "Ieșirea", "SSE.Controllers.Main.txtStyle_Percent": "Procent", "SSE.Controllers.Main.txtStyle_Title": "Titlu", "SSE.Controllers.Main.txtStyle_Total": "Total", "SSE.Controllers.Main.txtStyle_Warning_Text": "<PERSON><PERSON> de avertism<PERSON>", "SSE.Controllers.Main.txtTab": "<PERSON><PERSON>", "SSE.Controllers.Main.txtTable": "<PERSON><PERSON>", "SSE.Controllers.Main.txtTime": "Oră", "SSE.Controllers.Main.txtUnlock": "Deblocare", "SSE.Controllers.Main.txtUnlockRange": "Deblocare zonă", "SSE.Controllers.Main.txtUnlockRangeDescription": "Introduceți parola pentru modifacea zonei:", "SSE.Controllers.Main.txtUnlockRangeWarning": "<PERSON><PERSON><PERSON>, pe care doriţi să o modificaţi, este protejată prin parolă. ", "SSE.Controllers.Main.txtValues": "Valori", "SSE.Controllers.Main.txtView": "Vizualizare", "SSE.Controllers.Main.txtXAxis": "Axa X", "SSE.Controllers.Main.txtYAxis": "Axa Y", "SSE.Controllers.Main.txtYears": "<PERSON><PERSON>", "SSE.Controllers.Main.unknownErrorText": "<PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Browserul nu este compatibil.", "SSE.Controllers.Main.uploadDocExtMessage": "Format de fișier necunoscut.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Nu există nicun document încărcat.", "SSE.Controllers.Main.uploadDocSizeMessage": "Dimensiunea documentului depășește limita permisă.", "SSE.Controllers.Main.uploadImageExtMessage": "Format de imagine nerecunoscut.", "SSE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON> imagine nu a fost încărcată.", "SSE.Controllers.Main.uploadImageSizeMessage": "Dimensiunea imaginii depășește limita permisă.", "SSE.Controllers.Main.uploadImageTextText": "Încărcarea imaginii...", "SSE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON> imagine", "SSE.Controllers.Main.waitText": "<PERSON><PERSON> rugăm să așteptați...", "SSE.Controllers.Main.warnBrowserIE9": "Aplicația are o performanță slabă în IE9. Folosiți IE10 sau orice altă versiune mai nouă.", "SSE.Controllers.Main.warnBrowserZoom": "Setări zoom ale browserului dvs. nu sunt acceptate din plin. Restabiliți setările implicite ale zoomului prin apăsarea Ctrl+0.", "SSE.Controllers.Main.warnLicenseAnonymous": "Acces refuzat pentru utilizatori anonimi.<br>Acest document va fi accesibil numai în modul de vizualizare.", "SSE.Controllers.Main.warnLicenseBefore": "Licența nu este activă.<br>Contactaţi administratorul.", "SSE.Controllers.Main.warnLicenseExceeded": "Ați atins numărul maxim de conexiuni simultane la %1 de editoare. Documentul este disponibil numai pentru vizualizare.<br><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, contactați administratorul dvs.", "SSE.Controllers.Main.warnLicenseExp": "Licența dvs. a expirat.<br>Licența urmează să fie reînnoită iar pagina reîmprospătată.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Licența dvs. a expirat.<br>Nu aveți acces la funcții de editare a documentului.<br>Contactați administratorul dvs. de rețeea.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Licență urmează să fie reînnoită.<br>Funcțiile de editare sunt cu acces limitat.<br>Pentru a obține acces nelimitat, contactați administratorul dvs. de rețea.", "SSE.Controllers.Main.warnLicenseUsersExceeded": "<PERSON><PERSON>i atins numărul maxim de utilizatori care este prevăzut pentru editoare %1. <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, contactați administratorul dvs.", "SSE.Controllers.Main.warnNoLicense": "Ați atins numărul maxim de conexiuni simultane la %1 de editoare. Documentul este disponibil numai pentru vizualizare.<br>Contactați %1 Departamentul de Vânzări pentru acordarea condițiilor personale de licențiere.", "SSE.Controllers.Main.warnNoLicenseUsers": "Ați atins numărul maxim de utilizatori pentru editoare %1. Contactați Grup Vânzări %1 pentru acordarea condițiilor personale de licențiere.", "SSE.Controllers.Main.warnProcessRightsChange": "Accesul la editarea fișierului refuzat.", "SSE.Controllers.PivotTable.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "Imposibil de adăugat sau de modificat acest element. Acest câmp este inclus în filtrele din raportul PivotTable.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "Nicio operațiune cu elemente calculate nu este permisă în această celulă activă.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "Dacă una sau mai multe tabele dinamice conțin elemente calculate, niciun câmp nu poate fi utilizat de două sau mai multe ori în zona de date, sau în zona de date și în altă zonă în același timp.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Elementele calculate nu funcționează cu subtotaluri particularizate.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "Elementul nu este de găsit. Verificați dacă ați introdus corect numele și dacă elementul există în raportul PivotTable.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "<PERSON>ori medii, abateri standard și varianțe nu sunt acceptate dacă elemente într-un raport PivotTable au fost calculate.", "SSE.Controllers.Print.strAllSheets": "Toate foile", "SSE.Controllers.Print.textFirstCol": "Prima coloană", "SSE.Controllers.Print.textFirstRow": "Primul rând", "SSE.Controllers.Print.textFrozenCols": "Coloanele îng<PERSON>ț<PERSON>", "SSE.Controllers.Print.textFrozenRows": "Rândurile înghețate", "SSE.Controllers.Print.textInvalidRange": "EROARE! Zonă de celule nu este validă", "SSE.Controllers.Print.textNoRepeat": "Nu se repetă", "SSE.Controllers.Print.textRepeat": "Repetare...", "SSE.Controllers.Print.textSelectRange": "Selectați zonă", "SSE.Controllers.Print.txtCustom": "Particularizat", "SSE.Controllers.Print.txtZoomToPage": "Potrivire la pagină", "SSE.Controllers.Search.textInvalidRange": "EROARE! Zonă de celule nu este validă", "SSE.Controllers.Search.textNoTextFound": "Datele căutate nu au fost găsite. Alegeți alte opțiuni de căutare.", "SSE.Controllers.Search.textReplaceSkipped": "A avut loc o înlocuire. {0} apariții ignorate.", "SSE.Controllers.Search.textReplaceSuccess": "Căutarea a fost efectuată. {0} ocurențe înlocuite.", "SSE.Controllers.Statusbar.errorLastSheet": "În registrul de calcul trebuie să fie vizibilă cel puțin o foie de lucru. ", "SSE.Controllers.Statusbar.errorRemoveSheet": "Imposibil de șters foaia de calcul.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>Conexiunea a fost pierdută</b><br>Încercare de conectare. Verificați setările conexiunii.", "SSE.Controllers.Statusbar.textSheetViewTip": "Sunteți în modul de vizualizare foi. Filtre și sortare sunt vizibile numai de către dvs. și alți utilizatori care sunt în vizualizarea dată în acest moment.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Sunteți în modul de Afișare foaie. Criteriile de filtrare sunt vizibile numai pentru dumneavoastră și altor persoane în afișare.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Foile de calcul selectate pot conține datele. <PERSON><PERSON>r doriți să continuați?", "SSE.Controllers.Statusbar.zoomText": "Zoom {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Fonturi pe care doriți să le salvați nu sunt disponibile pe acest dispozitiv.<br>Textul va apărea scris cu fontul și stilul disponibil pe sistem, fontul salvat va fi aplicat de îndată ce devine disponibil.<br><PERSON><PERSON><PERSON>i să continuați?", "SSE.Controllers.Toolbar.errorComboSeries": "Pentru a crea o diagramă combinație, trebuie să selectați cel puțin două serii de date.", "SSE.Controllers.Toolbar.errorMaxPoints": "Numărul maxim de puncte de date pe serie în diagramă este limitat la 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "EROARE! Numărul maxim de serii de date dintr-o diagramă este 255", "SSE.Controllers.Toolbar.errorStockChart": "Sortarea rândurilor în ordinea incorectă. Pentru crearea unei diagrame de stoc, datele în foaie trebuie sortate în ordinea următoare:<br> prețul de deschidere, prețul maxim, prețul minim, prețul de închidere.", "SSE.Controllers.Toolbar.helpCalcItems": "Lucrați cu elemente calculate în Tabele dinamice.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Elemente calculate", "SSE.Controllers.Toolbar.helpFastUndo": "Anulați cu ușurință modificările în timpul colaborării la foi de calcul în modul Rapid.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "Funcția Anulare în modul de colaborare în timp real", "SSE.Controllers.Toolbar.helpMergeShapes": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, faceți să se intersectează, extrageți forme în câteva clipe pentru a crea elemente vizuale particularizate.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Îmbinare forme", "SSE.Controllers.Toolbar.textAccent": "Accente", "SSE.Controllers.Toolbar.textBracket": "Paranteze", "SSE.Controllers.Toolbar.textDirectional": "Direcțional", "SSE.Controllers.Toolbar.textFontSizeErr": "Valoarea introdusă nu este corectă.<br>Selectați valoarea cuprinsă înte 1 și 409.", "SSE.Controllers.Toolbar.textFraction": "Frac<PERSON><PERSON>", "SSE.Controllers.Toolbar.textFunction": "Funcții", "SSE.Controllers.Toolbar.textIndicator": "Indicatorii", "SSE.Controllers.Toolbar.textInsert": "Inserare", "SSE.Controllers.Toolbar.textIntegral": "Integrale", "SSE.Controllers.Toolbar.textLargeOperator": "Operatori mari", "SSE.Controllers.Toolbar.textLimitAndLog": "Limită și logaritm", "SSE.Controllers.Toolbar.textLongOperation": "Operațiunea de lungă durată", "SSE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.textOperator": "Operatori", "SSE.Controllers.Toolbar.textPivot": "Tabelă Pivot", "SSE.Controllers.Toolbar.textRadical": "Radicale", "SSE.Controllers.Toolbar.textRating": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRecentlyUsed": "Utilizate recent", "SSE.Controllers.Toolbar.textScript": "Scripturile", "SSE.Controllers.Toolbar.textShapes": "Forme", "SSE.Controllers.Toolbar.textSymbols": "Simbo<PERSON><PERSON>", "SSE.Controllers.Toolbar.textWarning": "Avertisment", "SSE.Controllers.Toolbar.txtAccent_Accent": "Ascuțit", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Săgeată deasupra de la dreapta la stînga", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Săgeată deasupra spre stânga ", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Săgeată deasupra spre dreapta", "SSE.Controllers.Toolbar.txtAccent_Bar": "Bară", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Bară de<PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarTop": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Formula încastrată (cu substituent)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Formula încastrată (exemplu)", "SSE.Controllers.Toolbar.txtAccent_Check": "Bifare", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Acoladă dedesubt", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Acoladă <PERSON>", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC cu bara deasupra", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y cu bară deasupra", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Trei puncte", "SSE.Controllers.Toolbar.txtAccent_DDot": "Două puncte", "SSE.Controllers.Toolbar.txtAccent_Dot": "Punct", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Bară dublă deasupra", "SSE.Controllers.Toolbar.txtAccent_Grave": "Grav", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Gruparea caracterelor de dedesupt", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Gruparea caracterelor deasupra", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Harpon orientat stânga sus", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Harpon orientat dreapta sus", "SSE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Smile": "Breve", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Tildă", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Paranteze unghiulare cu separator", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Paranteze unghiulare cu doi separatori", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Paranteză unghiulară dreaptă", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Paranteză unghiulară stângă", "SSE.Controllers.Toolbar.txtBracket_Curve": "Acolade", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Acolade cu separator", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Acolad<PERSON> dreaptă", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Acoladă stângă", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Sistem (două condiții)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Sistem (trei condiții)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Stiva de obiecte", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Obiect stivă în paranteze", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Exemplu de sistem", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Coeficient binominal", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Coeficientul binomial în paranteze unghiulare", "SSE.Controllers.Toolbar.txtBracket_Line": "Bare verticale", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Bara verticală din dreapta", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Bară verticală din stânga", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Bare verticale duble", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Bara verticală dublă din dreapta", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Bară verticală dublă din stânga", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Paranteză dreaptă inferioară", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Paranteză dreaptă inferioară din dreapta", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Paranteză dreaptă inferioară din stânga", "SSE.Controllers.Toolbar.txtBracket_Round": "Paranteze", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Paranteze cu separator", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Paranteză dreaptă", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Paranteză stângă", "SSE.Controllers.Toolbar.txtBracket_Square": "Paranteze pă<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Substituent între două paranteze pătrate din dreapta", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Paranteze pătrate inverse", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Paranteză pătrată dreaptă", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Paranteză pătrată stângă", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Substituent între două paranteze pătrate din stânga", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Paranteze pătrate duble", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Paranteză pătrată dreaptă dublă", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Paranteză pătrată dublă din stânga", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Paranteză dreaptă superioară", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Paranteză dreaptă superioară din dreapta", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Paranteză dreaptă superioară din stânga", "SSE.Controllers.Toolbar.txtDeleteCells": "<PERSON><PERSON><PERSON><PERSON> celule", "SSE.Controllers.Toolbar.txtExpand": "Extindere și sortare", "SSE.Controllers.Toolbar.txtExpandSort": "Datele lângă selecție vor fi sortate. Doriți să extindeți selecția pentru a include datele adiacente sau doriți să continuați cu selecția curentă?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Fracț<PERSON> oblic<PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "dx supra dy", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "cap delta y supra cap delta x", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "y parțial supra x parțial", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "delta y supra delta x", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Frac<PERSON><PERSON> l<PERSON>", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi supra 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Frac<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionVertical": "Fracție cu linia orizontală", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Fun<PERSON><PERSON><PERSON> arccos<PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Func<PERSON><PERSON> arccosinus hip<PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Funcția arccotangentă", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Funcția arccotangentă hiperbolică", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Funcția arccosecantă", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Funcția arcosecantă hiperbolică", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Funcția arcsecantă", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Funcția arcsecantă hiperbolică", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "<PERSON><PERSON><PERSON><PERSON> arcsinus", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "<PERSON><PERSON><PERSON><PERSON> arcsinus hiper<PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Funcția arctangentă", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Funcția arctangentă hiperbolică", "SSE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON><PERSON> cosinus", "SSE.Controllers.Toolbar.txtFunction_Cosh": "<PERSON><PERSON><PERSON><PERSON> cosinus hiper<PERSON>", "SSE.Controllers.Toolbar.txtFunction_Cot": "Funcția cotangentă", "SSE.Controllers.Toolbar.txtFunction_Coth": "Funcția cotangentă hiperbolică", "SSE.Controllers.Toolbar.txtFunction_Csc": "Funcția cosecantă ", "SSE.Controllers.Toolbar.txtFunction_Csch": "Funcția coscecantă hiberbolică", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sinus theta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Formula tangentei", "SSE.Controllers.Toolbar.txtFunction_Sec": "Funcția secantă", "SSE.Controllers.Toolbar.txtFunction_Sech": "Fincția secantă hiperbolică", "SSE.Controllers.Toolbar.txtFunction_Sin": "Funcția sinus", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Fincția sinus hiperbolic", "SSE.Controllers.Toolbar.txtFunction_Tan": "Funcția tangentă", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Fincția tangentă hiperbolică", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Particularizat", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Date și model", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Pozitiv, negativ și neutru", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "Fără nume", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Formatul de număr", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "<PERSON><PERSON><PERSON> celule tematice", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Titluri și anteturi", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Particularizat", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Întunecat", "SSE.Controllers.Toolbar.txtGroupTable_Light": "Luminos", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Me<PERSON>u", "SSE.Controllers.Toolbar.txtInsertCells": "Inserare celule", "SSE.Controllers.Toolbar.txtIntegral": "Integrală", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Diferențială de theta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Diferențială de x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Diferențială de y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integrală cu limite stivuite", "SSE.Controllers.Toolbar.txtIntegralDouble": "Integrală dublă", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Integrală dublă cu limite stivuite", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Integrală dublă cu limite", "SSE.Controllers.Toolbar.txtIntegralOriented": "Inegrală de contur", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Integrală de contur cu limite stivuite", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integrală de suprafața", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integrală de suprafață cu limite stivuite", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integrală de suprafață cu limite", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Integrală de contur cu limite", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integrală de volum", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integrală de volum cu limite stivuite", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integrală de volum cu limite", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integrală cu limite", "SSE.Controllers.Toolbar.txtIntegralTriple": "Integrală triplă", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Integrală triplă cu limite stivuite", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Integrală triplă cu limite", "SSE.Controllers.Toolbar.txtInvalidRange": "EROARE! Zonă de celule nu este validă", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Și logic", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Și logic cu limită inferioară", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Și logic cu limite", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Și logic cu limită inferioară pentru indice", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Și logic cu limite pentru indice/exponent", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Coprodus cu limită inferioară", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "<PERSON>p<PERSON>us cu limite", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Coprodus cu limită inferioară pentru indice", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Coprodus cu limite pentru indice/exponent", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Sumă de k combinări din n luate câte k", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Sumă de la i egal cu zero la n", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Exemplu de sumă folosind doi indici", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Exemplu de produs", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Exemplu de reuniune", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Sau logic", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Sau logic cu limită inferioară", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Sau logic cu limite", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Sau logic cu limită inferioară pentru indice", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Sau logic cu limite pentru indice/exponent", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersecție", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersecție cu limită inferioară", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersecție cu limite", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersecție cu limită inferioară pentru indice", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersecție cu limite pentru indice/exponent", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Produs", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produs cu limită inferioară", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produs cu limite", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produs cu limită inferioară pentru indice", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produs cu limite pentru indice/exponent", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Sumă", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Sumă cu limită inferioară", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Sumă cu limite", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Sumă cu limită inferioară pentru indice", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Sumă cu limite pentru indice/exponent", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Reuniune cu limită inferioară", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Reuniune cu limite", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Reuniune cu limită inferioară pentru indice", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Reuniune cu limite pentru indice/exponent", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Exemplu de limită", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Exemplu de maxim", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Limită", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritm natural", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logaritm", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritm", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Maxim", "SSE.Controllers.Toolbar.txtLimitLog_Min": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLockSort": "Alături de celulele selectate au fost găsite datele dar nu aveți permisiuni suficente ca să le modificați<br>Doriți să continuați cu selecția curentă?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "Matrice goală 1x2 ", "SSE.Controllers.Toolbar.txtMatrix_1_3": "Matrice goală 1x3", "SSE.Controllers.Toolbar.txtMatrix_2_1": "Matrice goală 2x1 ", "SSE.Controllers.Toolbar.txtMatrix_2_2": "Matrice goală 2x2", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Matrice goală de 2x2 între bare verticale duble", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Determinant gol de 2x2", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Matrice goală de 2x2 între paranteze", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Matrice goală de 2x2 între paranteze drepte", "SSE.Controllers.Toolbar.txtMatrix_2_3": "<PERSON>rice goală 2x3", "SSE.Controllers.Toolbar.txtMatrix_3_1": "Matrice goală 3x1", "SSE.Controllers.Toolbar.txtMatrix_3_2": "Matrice goală 3x2", "SSE.Controllers.Toolbar.txtMatrix_3_3": "Matrice goală 3x3", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Puncte pe linia de bază", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Puncte pe linia de mijloc", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Puncte pe diagonală", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Puncte pe verticală", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> r<PERSON> <PERSON>n paranteze", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON> rar<PERSON> în paranteze drepte", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "Matrice identitate de 2x2 cu zerouri", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Matrice identitate de 2x2 cu celule goale în afara diagonalei", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "Matrice identitate de 3x3 cu zerouri", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matrice identitate de 3x3 cu celule goale în afara diagonalei", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Săgeată dedesubt de la dreapta la stînga", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Săgeată deasupra de la dreapta la stînga", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Săgeată dedesupt spre stânga", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Săgeată deasupra spre stânga ", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Săgeată dedesubt spre dreapta", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Săgeată deasupra spre dreapta", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Două puncte egal", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Rezultă", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta rezultă", "SSE.Controllers.Toolbar.txtOperator_Definition": "Egal prin definiție", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta egal", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Săgeată dublă dedesubt de la dreapta la stînga", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Săgeată dublă deasupra de la dreapta la stînga", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Săgeată dedesupt spre stânga", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Săgeată deasupra spre stânga ", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Săgeată dedesubt spre dreapta", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Săgeată deasupra spre dreapta", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Egal egal", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Minus egal", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus egal", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Unitate de măsură", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Partea din dreapta a formulei pătratice", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Rădăcina pătrată din a pătrat plus b pătrat", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Rădăcina pătrată de ordin", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Rădăcină cubică", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Radicalul de ordin", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Rădăcină pătrată", "SSE.Controllers.Toolbar.txtScriptCustom_1": "x indice y la pătrat", "SSE.Controllers.Toolbar.txtScriptCustom_2": "e la minus i omega t", "SSE.Controllers.Toolbar.txtScriptCustom_3": "x la pătrat", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Y exponent stânga n indice stânga unu", "SSE.Controllers.Toolbar.txtScriptSub": "Indice", "SSE.Controllers.Toolbar.txtScriptSubSup": "Indice-exponenet", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Indice-exponent din stânga", "SSE.Controllers.Toolbar.txtScriptSup": "Exponent", "SSE.Controllers.Toolbar.txtSorting": "Sortare", "SSE.Controllers.Toolbar.txtSortSelected": "Sortarea se<PERSON>ției", "SSE.Controllers.Toolbar.txtSymbol_about": "Aproximativ", "SSE.Controllers.Toolbar.txtSymbol_additional": "Complementară", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "Aproape egal cu", "SSE.Controllers.Toolbar.txtSymbol_ast": "Operatorul asterisc", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Operator b<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cap": "Intersecție", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Rădăcină cubică", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Trei puncte orizontale la mijloc de linie", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Aproximativ egal cu", "SSE.Controllers.Toolbar.txtSymbol_cup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON>rei puncte în diagonal<PERSON>, drea<PERSON> jos", "SSE.Controllers.Toolbar.txtSymbol_degree": "Grade", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "<PERSON>m<PERSON><PERSON> împă<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Săgeată în jos", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Mulțimea vidă", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "Egal", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Identic", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "Există", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Factorial", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Grade Fahrenheit", "SSE.Controllers.Toolbar.txtSymbol_forall": "Oricare", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gama", "SSE.Controllers.Toolbar.txtSymbol_geq": "<PERSON> mare sau egal", "SSE.Controllers.Toolbar.txtSymbol_gg": "Mult mai mare", "SSE.Controllers.Toolbar.txtSymbol_greater": "<PERSON> mare decât", "SSE.Controllers.Toolbar.txtSymbol_in": "Aparținând la", "SSE.Controllers.Toolbar.txtSymbol_inc": "Creștere", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Infinit", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Săgeată la stângă", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Săgeată stânga-dreapta", "SSE.Controllers.Toolbar.txtSymbol_leq": "Mai mic sau egal cu", "SSE.Controllers.Toolbar.txtSymbol_less": "Mai mic decât", "SSE.Controllers.Toolbar.txtSymbol_ll": "Mult mai mic", "SSE.Controllers.Toolbar.txtSymbol_minus": "Minus", "SSE.Controllers.Toolbar.txtSymbol_mp": "Minus plus", "SSE.Controllers.Toolbar.txtSymbol_mu": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Nu este egal cu", "SSE.Controllers.Toolbar.txtSymbol_ni": "Conținut în", "SSE.Controllers.Toolbar.txtSymbol_not": "Semn negativ", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Nu există", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_o": "Omicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Diferențială parțială", "SSE.Controllers.Toolbar.txtSymbol_percent": "Procentaj", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus minus", "SSE.Controllers.Toolbar.txtSymbol_propto": "Proporțional", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Rădăcina de ordinul patru", "SSE.Controllers.Toolbar.txtSymbol_qed": "Finalul demonstrației", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Trei puncte în diagonală, dreapta sus", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Săgeată la dreapta", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Semn de radical", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Rezultă", "SSE.Controllers.Toolbar.txtSymbol_theta": "Teta", "SSE.Controllers.Toolbar.txtSymbol_times": "Semnul înmulțirii", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Săgeată în sus", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Variantă de epsilon", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Variantă de phi", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Variantă de Pi", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Variantă de Rho", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Variantă de sigma", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Variantă de teta", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Trei puncte verticale", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Stil tabel întunecat", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Stil tabel deschis", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Stil tabel mediu", "SSE.Controllers.Toolbar.warnLongOperation": "Operațiunea pe care doriți să o efectuați poate dura destul de mult timp<br><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>i să continuați?", "SSE.Controllers.Toolbar.warnMergeLostData": "În celula îmbinată se afișează numai conținutul celulei din stânga sus.<br>Sunteți sigur că doriți să continuați?", "SSE.Controllers.Toolbar.warnNoRecommended": "Pentru a crea o diagramă, selectați celulele care conțin datele pe care doriți să le utilizați.<br>Dacă ați adăugat nume pentru rânduri și coloane și doriți să le utilizați ca etichete, includeți-le în selecția dvs.", "SSE.Controllers.Viewport.textFreezePanes": "Înghețare panouri", "SSE.Controllers.Viewport.textFreezePanesShadow": "Afișare umbră pentru panouri înghețate", "SSE.Controllers.Viewport.textHideFBar": "Ascundere bară de formule", "SSE.Controllers.Viewport.textHideGridlines": "Ascundere linii de grilă", "SSE.Controllers.Viewport.textHideHeadings": "Ascundere titluri", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Separator <PERSON>", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Separator mii", "SSE.Views.AdvancedSeparatorDialog.textLabel": "<PERSON><PERSON><PERSON> pentru recunoașterea modelelor numerice", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Calificator text", "SSE.Views.AdvancedSeparatorDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(niciunul)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Filtru particularizat", "SSE.Views.AutoFilterDialog.textAddSelection": "Se adaugă selecția curentă la filtru", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Necompletate}", "SSE.Views.AutoFilterDialog.textSelectAll": "Selectare totală", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Se selectează toate rezultatele de căutare ", "SSE.Views.AutoFilterDialog.textWarning": "Avertisment", "SSE.Views.AutoFilterDialog.txtAboveAve": "<PERSON><PERSON><PERSON>ra medie", "SSE.Views.AutoFilterDialog.txtAfter": "Dup<PERSON>...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "Toate datele din perioada respectivă", "SSE.Views.AutoFilterDialog.txtApril": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtAugust": "August", "SSE.Views.AutoFilterDialog.txtBefore": "Până la...", "SSE.Views.AutoFilterDialog.txtBegins": "Începe cu...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Sub medie", "SSE.Views.AutoFilterDialog.txtBetween": "Între...", "SSE.Views.AutoFilterDialog.txtClear": "Golire", "SSE.Views.AutoFilterDialog.txtContains": "Conține...", "SSE.Views.AutoFilterDialog.txtDateFilter": "<PERSON><PERSON><PERSON> <PERSON>", "SSE.Views.AutoFilterDialog.txtDecember": "Decembrie", "SSE.Views.AutoFilterDialog.txtEmpty": "Adă<PERSON>re filtru celulă", "SSE.Views.AutoFilterDialog.txtEnds": "Se termină cu...", "SSE.Views.AutoFilterDialog.txtEquals": "Este egal cu...", "SSE.Views.AutoFilterDialog.txtFebruary": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filtrare după culoarea celulelor", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filtrarea după culoarea fontului", "SSE.Views.AutoFilterDialog.txtGreater": "Mai mare decât...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Mai mare sau egal...", "SSE.Views.AutoFilterDialog.txtJanuary": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtJuly": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtJune": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtLabelFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtLastMonth": "Ultima lună", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Ultimul trimestru", "SSE.Views.AutoFilterDialog.txtLastWeek": "Săptămâna trecută", "SSE.Views.AutoFilterDialog.txtLastYear": "<PERSON><PERSON> trecut", "SSE.Views.AutoFilterDialog.txtLess": "Mai mic decât...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Mai mic sau egal cu...", "SSE.Views.AutoFilterDialog.txtMarch": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtMay": "<PERSON>", "SSE.Views.AutoFilterDialog.txtNextMonth": "<PERSON>", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Trimestrul următor", "SSE.Views.AutoFilterDialog.txtNextWeek": "Săptămân<PERSON> u<PERSON>", "SSE.Views.AutoFilterDialog.txtNextYear": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtNotBegins": "Nu se începe cu...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Not between...", "SSE.Views.AutoFilterDialog.txtNotContains": "Nu conține...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Nu se termină cu...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Nu este egal cu...", "SSE.Views.AutoFilterDialog.txtNovember": "Noiembrie", "SSE.Views.AutoFilterDialog.txtNumFilter": "Filtru numeric", "SSE.Views.AutoFilterDialog.txtOctober": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtQuarter1": "Trimestrul 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Trimestrul 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Trimestrul 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Trimestrul 1", "SSE.Views.AutoFilterDialog.txtReapply": "Reaplicare", "SSE.Views.AutoFilterDialog.txtSeptember": "Septembrie", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Sortare pe baza culoare celulă", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Sortare pe baza culoare font", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Sortare de la cea mai mare la cea mai mică valoare", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Sortare de la cea mai mică la cea mai mare valoare", "SSE.Views.AutoFilterDialog.txtSortOption": "Mai multe opțiuni de sortare...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Filtru text", "SSE.Views.AutoFilterDialog.txtThisMonth": "<PERSON>", "SSE.Views.AutoFilterDialog.txtThisQuarter": "Acest trimestru", "SSE.Views.AutoFilterDialog.txtThisWeek": "Această săptămână", "SSE.Views.AutoFilterDialog.txtThisYear": "Acest an", "SSE.Views.AutoFilterDialog.txtTitle": "Filtrare", "SSE.Views.AutoFilterDialog.txtToday": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtTomorrow": "M<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtTop10": "Primele 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Filtru de valoare", "SSE.Views.AutoFilterDialog.txtYearToDate": "Cumulat de la începutul exercițiului financiar până în prezent", "SSE.Views.AutoFilterDialog.txtYesterday": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.warnFilterError": "Zonă de Valori trebuie să conțină cel puțin un câmp ca filtrul să fie aplicat. ", "SSE.Views.AutoFilterDialog.warnNoSelected": "Selectați cel puțin o valoare", "SSE.Views.CellEditor.textManager": "Manager nume", "SSE.Views.CellEditor.tipFormula": "Inserare funcție", "SSE.Views.CellRangeDialog.errorMaxRows": "EROARE! Numărul maxim de serii de date dintr-o diagramă este 255", "SSE.Views.CellRangeDialog.errorStockChart": "Sortarea rândurilor în ordinea incorectă. Pentru crearea unei diagrame de stoc, datele în foaie trebuie sortate în ordinea următoare:<br> prețul de deschidere, prețul maxim, prețul minim, prețul de închidere.", "SSE.Views.CellRangeDialog.txtEmpty": "Câmp obligatoriu", "SSE.Views.CellRangeDialog.txtInvalidRange": "EROARE! Zonă de celule nu este validă", "SSE.Views.CellRangeDialog.txtTitle": "Selectați zonă de date", "SSE.Views.CellSettings.strShrink": "Portivire prin reducere", "SSE.Views.CellSettings.strWrap": "Incadrarea textului", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "Culoare de fundal", "SSE.Views.CellSettings.textBackground": "Culoare de fundal", "SSE.Views.CellSettings.textBorderColor": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "<PERSON><PERSON> bord<PERSON>", "SSE.Views.CellSettings.textClearRule": "Go<PERSON><PERSON> reguli", "SSE.Views.CellSettings.textColor": "Umplere cu culoare", "SSE.Views.CellSettings.textColorScales": "Scale de culori", "SSE.Views.CellSettings.textCondFormat": "Formatarea condiționată", "SSE.Views.CellSettings.textControl": "Control de text", "SSE.Views.CellSettings.textDataBars": "Bare de date", "SSE.Views.CellSettings.textDirection": "Orientare", "SSE.Views.CellSettings.textFill": "Umplere", "SSE.Views.CellSettings.textForeground": "Culoarea de prim plan", "SSE.Views.CellSettings.textGradient": "Stop gradient", "SSE.Views.CellSettings.textGradientColor": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "Umplere gradient", "SSE.Views.CellSettings.textIndent": "Indentare", "SSE.Views.CellSettings.textItems": "Elemente", "SSE.Views.CellSettings.textLinear": "<PERSON><PERSON>", "SSE.Views.CellSettings.textManageRule": "Gestionare reguli", "SSE.Views.CellSettings.textNewRule": "Regulă nouă", "SSE.Views.CellSettings.textNoFill": "<PERSON><PERSON><PERSON><PERSON> um<PERSON>", "SSE.Views.CellSettings.textOrientation": "Orientarea textului", "SSE.Views.CellSettings.textPattern": "Model", "SSE.Views.CellSettings.textPatternFill": "Model", "SSE.Views.CellSettings.textPosition": "Poziție", "SSE.Views.CellSettings.textRadial": "Radială", "SSE.Views.CellSettings.textSelectBorders": "Selectați borduri pe care doriți să le modificați prin aplicarea stilului selectat", "SSE.Views.CellSettings.textSelection": "<PERSON> se<PERSON>ț<PERSON>", "SSE.Views.CellSettings.textThisPivot": "Din acest tabel pivot", "SSE.Views.CellSettings.textThisSheet": "Din această foaie de calcul", "SSE.Views.CellSettings.textThisTable": "Din acest tabel", "SSE.Views.CellSettings.tipAddGradientPoint": "Adăugare stop gradient", "SSE.Views.CellSettings.tipAll": "<PERSON><PERSON><PERSON><PERSON> bordură exterioară și toate borduri interioare", "SSE.Views.CellSettings.tipBottom": "<PERSON><PERSON><PERSON><PERSON> numai bordură exterioară de jos", "SSE.Views.CellSettings.tipDiagD": "<PERSON><PERSON><PERSON><PERSON> bordur<PERSON> diagonală descendentă", "SSE.Views.CellSettings.tipDiagU": "<PERSON><PERSON><PERSON><PERSON> bord<PERSON><PERSON> diagonală ascendentă", "SSE.Views.CellSettings.tipInner": "<PERSON><PERSON><PERSON><PERSON> numai borduri interioare", "SSE.Views.CellSettings.tipInnerHor": "Adăugare numai bordură interioară orizontală", "SSE.Views.CellSettings.tipInnerVert": "Ad<PERSON><PERSON>re numai borduri verticale interioare", "SSE.Views.CellSettings.tipLeft": "Ad<PERSON><PERSON>re numai bordură exterioară stânga", "SSE.Views.CellSettings.tipNone": "<PERSON><PERSON> f<PERSON> borduri", "SSE.Views.CellSettings.tipOuter": "<PERSON><PERSON><PERSON><PERSON> numai bordură exterioară", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Eliminare stop gradient", "SSE.Views.CellSettings.tipRight": "<PERSON><PERSON><PERSON><PERSON> numai bordură exterioară dreapta", "SSE.Views.CellSettings.tipTop": "Ad<PERSON><PERSON>re numai bordură exterioară de sus", "SSE.Views.ChartDataDialog.errorInFormula": "Formulă introdusă conține o eroare.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Referință nu este validă. Referire trebuie făcută la o foaie de calcul care este deschisă.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Numărul maxim de puncte de date pe serie în diagramă este limitat la 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Numărul maxim seriilor de date în diagramă este limitat la 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Referință nu este validă. Refer<PERSON> la titluri, valori, dimensiuni sau etichete de date se face pentru o singură celula, rând sau coloana.", "SSE.Views.ChartDataDialog.errorNoValues": "Pentru a crea o diagramă, seria trebuie să conțină cel puțin o valoare.", "SSE.Views.ChartDataDialog.errorStockChart": "Sortarea rândurilor în ordinea incorectă. Pentru crearea unei diagrame de stoc, datele în foaie trebuie sortate în ordinea următoare:<br> prețul de deschidere, prețul maxim, prețul minim, prețul de închidere.", "SSE.Views.ChartDataDialog.textAdd": "Adaugă", "SSE.Views.ChartDataDialog.textCategory": "Etichete axă orizontală (categorie)", "SSE.Views.ChartDataDialog.textData": "Zonă de date diagramă", "SSE.Views.ChartDataDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textDown": "În jos", "SSE.Views.ChartDataDialog.textEdit": "Editare", "SSE.Views.ChartDataDialog.textInvalidRange": "Zonă de celule nu este validă", "SSE.Views.ChartDataDialog.textSelectData": "Selectare date", "SSE.Views.ChartDataDialog.textSeries": "<PERSON><PERSON><PERSON><PERSON> (serial)", "SSE.Views.ChartDataDialog.textSwitch": "Comutare rând/coloană", "SSE.Views.ChartDataDialog.textTitle": "Date în diagramă", "SSE.Views.ChartDataDialog.textUp": "În sus", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Formulă introdusă conține o eroare.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Referință nu este validă. Referire trebuie făcută la o foaie de calcul care este deschisă.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Numărul maxim de puncte de date pe serie în diagramă este limitat la 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Numărul maxim seriilor de date în diagramă este limitat la 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Referință nu este validă. Refer<PERSON> la titluri, valori, dimensiuni sau etichete de date se face pentru o singură celula, rând sau coloana.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Pentru a crea o diagramă, seria trebuie să conțină cel puțin o valoare.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Sortarea rândurilor în ordinea incorectă. Pentru crearea unei diagrame de stoc, datele în foaie trebuie sortate în ordinea următoare:<br> prețul de deschidere, prețul maxim, prețul minim, prețul de închidere.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Zonă de celule nu este validă", "SSE.Views.ChartDataRangeDialog.textSelectData": "Selectare date", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Interval etichetă axă", "SSE.Views.ChartDataRangeDialog.txtChoose": "Selectare interval", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Nume serie", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Etichetele axei", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Modificare serii", "SSE.Views.ChartDataRangeDialog.txtValues": "Valori", "SSE.Views.ChartDataRangeDialog.txtXValues": "Valori X", "SSE.Views.ChartDataRangeDialog.txtYValues": "Valori Y", "SSE.Views.ChartSettings.errorMaxRows": "Numărul maxim seriilor de date în diagramă este limitat la 255.", "SSE.Views.ChartSettings.strLineWeight": "Stil linie", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "Șablon", "SSE.Views.ChartSettings.text3dDepth": "Adâncime (% din bază)", "SSE.Views.ChartSettings.text3dHeight": "Înălțime (% din bază)", "SSE.Views.ChartSettings.text3dRotation": "Rotație 3D", "SSE.Views.ChartSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textAutoscale": "Autoscalare", "SSE.Views.ChartSettings.textBorderSizeErr": "Valoarea introdusă nu este corectă.<br>Selectați valoarea cuprinsă înte 0 pt și 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "Modificare tip", "SSE.Views.ChartSettings.textChartType": "Modificare tip diagramă", "SSE.Views.ChartSettings.textDefault": "Rotație implicită", "SSE.Views.ChartSettings.textDown": "În jos", "SSE.Views.ChartSettings.textEditData": "Schimbare data și locația", "SSE.Views.ChartSettings.textFirstPoint": "Primul punct", "SSE.Views.ChartSettings.textHeight": "Înălțime", "SSE.Views.ChartSettings.textHighPoint": "Punct ridicat", "SSE.Views.ChartSettings.textKeepRatio": "Dimensi<PERSON> constante", "SSE.Views.ChartSettings.textLastPoint": "Ultimul punct", "SSE.Views.ChartSettings.textLeft": "Stânga", "SSE.Views.ChartSettings.textLowPoint": "<PERSON><PERSON><PERSON> sc<PERSON><PERSON>t", "SSE.Views.ChartSettings.textMarkers": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textNarrow": "Unghi de vizualizare îngust", "SSE.Views.ChartSettings.textNegativePoint": "Punct negativ", "SSE.Views.ChartSettings.textPerspective": "Perspectiv<PERSON>", "SSE.Views.ChartSettings.textRanges": "Zonă de date", "SSE.Views.ChartSettings.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textRightAngle": "<PERSON>xe în unghi drept", "SSE.Views.ChartSettings.textSelectData": "Selectare date", "SSE.Views.ChartSettings.textShow": "<PERSON><PERSON>ș<PERSON><PERSON>", "SSE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textStyle": "Stil", "SSE.Views.ChartSettings.textSwitch": "Comutare rând/coloană", "SSE.Views.ChartSettings.textType": "Tip", "SSE.Views.ChartSettings.textUp": "În sus", "SSE.Views.ChartSettings.textWiden": "<PERSON><PERSON><PERSON> de vizual<PERSON>re larg", "SSE.Views.ChartSettings.textWidth": "Lățime", "SSE.Views.ChartSettings.textX": "Axa de rotație X", "SSE.Views.ChartSettings.textY": "Axa de rotație Y", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "EROARE! Numărul maxim de puncte de date într-o serie de date pentru diagram este 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "EROARE! Numărul maxim de serii de date dintr-o diagramă este 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Sortarea rândurilor în ordinea incorectă. Pentru crearea unei diagrame de stoc, datele în foaie trebuie sortate în ordinea următoare:<br> prețul de deschidere, prețul maxim, prețul minim, prețul de închidere.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Fără mutare sau dimensionare cu celule", "SSE.Views.ChartSettingsDlg.textAlt": "Text alternativ", "SSE.Views.ChartSettingsDlg.textAltDescription": "Des<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAltTip": "Furnizarea textului alternativ pentru conținut vizual destinat persoanelor cu deficiențe de vedere și cognitive pentru a le ajuta să înțăleagă mai bine conținutul unei imagini, forme, diagramei sau tabele.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Titlu", "SSE.Views.ChartSettingsDlg.textAuto": "Auto", "SSE.Views.ChartSettingsDlg.textAutoEach": "Automat la fiecare", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Intersecția cu axă", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Opțiuni axă", "SSE.Views.ChartSettingsDlg.textAxisPos": "Poziție axă", "SSE.Views.ChartSettingsDlg.textAxisSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Titlu", "SSE.Views.ChartSettingsDlg.textBase": "Bază", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Între grada<PERSON>", "SSE.Views.ChartSettingsDlg.textBillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBottom": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCategoryName": "Nume de categorie", "SSE.Views.ChartSettingsDlg.textCenter": "La centru", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Elementele diagramei &<br><PERSON><PERSON> diagramei", "SSE.Views.ChartSettingsDlg.textChartTitle": "Titlu diagramă", "SSE.Views.ChartSettingsDlg.textCross": "Traversare", "SSE.Views.ChartSettingsDlg.textCustom": "Particularizat", "SSE.Views.ChartSettingsDlg.textDataColumns": "în coloane", "SSE.Views.ChartSettingsDlg.textDataLabels": "Etichetele de date", "SSE.Views.ChartSettingsDlg.textDataRows": "<PERSON>n r<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Afișare legendă", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Celulele ascunse sau libere", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Conectați puncte de date cu linie", "SSE.Views.ChartSettingsDlg.textFit": "Potrivir<PERSON>", "SSE.Views.ChartSettingsDlg.textFixed": "Fixă", "SSE.Views.ChartSettingsDlg.textFormat": "Format etichetă", "SSE.Views.ChartSettingsDlg.textGaps": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGridLines": "Linii de grilă", "SSE.Views.ChartSettingsDlg.textGroup": "Grupare diagrame sparkline", "SSE.Views.ChartSettingsDlg.textHide": "Ascunde", "SSE.Views.ChartSettingsDlg.textHideAxis": "Ascundere axă", "SSE.Views.ChartSettingsDlg.textHigh": "Ridicată", "SSE.Views.ChartSettingsDlg.textHorAxis": "Axă orizontală", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Axă orizontală secundară", "SSE.Views.ChartSettingsDlg.textHorizontal": "Orizontală", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Sute", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "în", "SSE.Views.ChartSettingsDlg.textInnerBottom": "În interior în jos", "SSE.Views.ChartSettingsDlg.textInnerTop": "În interor în sus", "SSE.Views.ChartSettingsDlg.textInvalidRange": "EROARE! Zonă de celule nu este validă", "SSE.Views.ChartSettingsDlg.textLabelDist": "Distanță între etichetă și axă", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Intervalul dintre etichete", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Opțiuni etichetă", "SSE.Views.ChartSettingsDlg.textLabelPos": "Amplasare etichetă", "SSE.Views.ChartSettingsDlg.textLayout": "Aspect", "SSE.Views.ChartSettingsDlg.textLeft": "Stânga", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Suprapunere din stânga", "SSE.Views.ChartSettingsDlg.textLegendBottom": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Stânga", "SSE.Views.ChartSettingsDlg.textLegendPos": "<PERSON>a", "SSE.Views.ChartSettingsDlg.textLegendRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendTop": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLines": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLocationRange": "Interval locație", "SSE.Views.ChartSettingsDlg.textLogScale": "Scară logaritmică", "SSE.Views.ChartSettingsDlg.textLow": "Sc<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajor": "Major", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Major <PERSON><PERSON> minor", "SSE.Views.ChartSettingsDlg.textMajorType": "Tip major", "SSE.Views.ChartSettingsDlg.textManual": "Manual", "SSE.Views.ChartSettingsDlg.textMarkers": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Intervalul dintre gradații", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON><PERSON> maxi<PERSON>", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "Minor", "SSE.Views.ChartSettingsDlg.textMinorType": "Tip minor", "SSE.Views.ChartSettingsDlg.textMinValue": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Lângă axă", "SSE.Views.ChartSettingsDlg.textNone": "Niciunul", "SSE.Views.ChartSettingsDlg.textNoOverlay": "<PERSON><PERSON><PERSON><PERSON> suprapu<PERSON>", "SSE.Views.ChartSettingsDlg.textOneCell": "<PERSON><PERSON><PERSON> făr<PERSON>are cu celulele", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Pe gradații", "SSE.Views.ChartSettingsDlg.textOut": "<PERSON>", "SSE.Views.ChartSettingsDlg.textOuterTop": "În exterior în sus", "SSE.Views.ChartSettingsDlg.textOverlay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textReverse": "Valori în ordine inversă ", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Ordinea inversă", "SSE.Views.ChartSettingsDlg.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Suprapunerea din dreapta", "SSE.Views.ChartSettingsDlg.textRotated": "Rotit", "SSE.Views.ChartSettingsDlg.textSameAll": "Toate la fel", "SSE.Views.ChartSettingsDlg.textSelectData": "Selectare date", "SSE.Views.ChartSettingsDlg.textSeparator": "Separator etichete de date", "SSE.Views.ChartSettingsDlg.textSeriesName": "Nume serie", "SSE.Views.ChartSettingsDlg.textShow": "<PERSON><PERSON>ș<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowBorders": "Afișarea bordurei de diagramă", "SSE.Views.ChartSettingsDlg.textShowData": "Se afișează datele din rândurile și coloanele ascunse", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Afișare celule goale ca", "SSE.Views.ChartSettingsDlg.textShowEquation": "Afișare ecuație pe diagramă", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "<PERSON><PERSON><PERSON><PERSON> axe", "SSE.Views.ChartSettingsDlg.textShowValues": "Afișarea valorilor de diagramă", "SSE.Views.ChartSettingsDlg.textSingle": "Singură diagramă sparkline", "SSE.Views.ChartSettingsDlg.textSmooth": "<PERSON><PERSON> netede", "SSE.Views.ChartSettingsDlg.textSnap": "<PERSON><PERSON><PERSON> c<PERSON>i", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Interval locație diagramei sparkline", "SSE.Views.ChartSettingsDlg.textStraight": "<PERSON><PERSON> drepte", "SSE.Views.ChartSettingsDlg.textStyle": "Stil", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTickOptions": "Opțiuni gradație", "SSE.Views.ChartSettingsDlg.textTitle": "Diagramă - <PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Diagrame sparkline - <PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTop": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Opțiuni linie de tendință", "SSE.Views.ChartSettingsDlg.textTrillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTwoCell": "Mutare și dimensionare cu celulele", "SSE.Views.ChartSettingsDlg.textType": "Tip", "SSE.Views.ChartSettingsDlg.textTypeData": "Tip și date", "SSE.Views.ChartSettingsDlg.textUnits": "Unități de afișare", "SSE.Views.ChartSettingsDlg.textValue": "Valoare", "SSE.Views.ChartSettingsDlg.textVertAxis": "Axa verticală", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Axă verticală secundară", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Titlul axei X", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Titlul axei Y", "SSE.Views.ChartSettingsDlg.textZero": "Zero", "SSE.Views.ChartSettingsDlg.txtEmpty": "Câmp obligatoriu", "SSE.Views.ChartTypeDialog.errorComboSeries": "Pentru a crea o diagramă combinație, trebuie să selectați cel puțin două serii de date.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Tipul de diagramă selectat necesită axa secundară dintr-o diagramă existentă. Selectați alt tip de diagramă.", "SSE.Views.ChartTypeDialog.textSecondary": "Axă secundară", "SSE.Views.ChartTypeDialog.textSeries": "Serie", "SSE.Views.ChartTypeDialog.textStyle": "Stil", "SSE.Views.ChartTypeDialog.textTitle": "<PERSON>ip de <PERSON>", "SSE.Views.ChartTypeDialog.textType": "Tip", "SSE.Views.ChartWizardDialog.errorComboSeries": "Pentru a crea o diagramă combinație, trebuie să selectați cel puțin două serii de date.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "Numărul maxim de puncte de date pe serie în diagramă este limitat la 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "Numărul maxim seriilor de date în diagramă este limitat la 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "Tipul de diagramă selectat necesită axa secundară dintr-o diagramă existentă. Selectați alt tip de diagramă.", "SSE.Views.ChartWizardDialog.errorStockChart": "Secvența incorectă a rândurilor. Pentru a crea o diagramă bursieră, asigurați-vă că organizați datele în următoarea ordine: preț de deschidere, valoarea maximă, valoarea minimă, preț de închidere.", "SSE.Views.ChartWizardDialog.textRecommended": "Recomandate", "SSE.Views.ChartWizardDialog.textSecondary": "Axă secundară", "SSE.Views.ChartWizardDialog.textSeries": "Serie", "SSE.Views.ChartWizardDialog.textTitle": "Inserare diagramă", "SSE.Views.ChartWizardDialog.textTitleChange": "Modificare tip diagramă", "SSE.Views.ChartWizardDialog.textType": "Tip", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Alegeți tipul de diagramă și axa pentru seria de date", "SSE.Views.CreatePivotDialog.textDataRange": "Zonă de date sursă", "SSE.Views.CreatePivotDialog.textDestination": "Alegeți locul unde se va plasa tabelul", "SSE.Views.CreatePivotDialog.textExist": "Foaie de calcul existentă", "SSE.Views.CreatePivotDialog.textInvalidRange": "Zonă de celule nu este validă", "SSE.Views.CreatePivotDialog.textNew": "Foaie de calcul nouă", "SSE.Views.CreatePivotDialog.textSelectData": "Selectare date", "SSE.Views.CreatePivotDialog.textTitle": "<PERSON><PERSON><PERSON> tabel <PERSON>", "SSE.Views.CreatePivotDialog.txtEmpty": "Câmp obligatoriu", "SSE.Views.CreateSparklineDialog.textDataRange": "Zonă de date sursă", "SSE.Views.CreateSparklineDialog.textDestination": "Alegeți locul unde se va plasa diagrama sparkline", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Zona de celule nu este validă", "SSE.Views.CreateSparklineDialog.textSelectData": "Selectare date", "SSE.Views.CreateSparklineDialog.textTitle": "Creare diagrame sparkline", "SSE.Views.CreateSparklineDialog.txtEmpty": "Câmp obligatoriu", "SSE.Views.DataTab.capBtnGroup": "Grupare", "SSE.Views.DataTab.capBtnTextCustomSort": "<PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextDataValidation": "Validarea datelor", "SSE.Views.DataTab.capBtnTextRemDuplicates": "<PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextToCol": "Text în coloane", "SSE.Views.DataTab.capBtnUngroup": "Anular<PERSON> g<PERSON>i", "SSE.Views.DataTab.capDataExternalLinks": "<PERSON><PERSON> externe", "SSE.Views.DataTab.capDataFromText": "Obținere date", "SSE.Views.DataTab.capGoalSeek": "<PERSON><PERSON><PERSON><PERSON> rezultat", "SSE.Views.DataTab.mniFromFile": "Din fișier local TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "Prin adresa URL a fișierului TXT/CSV", "SSE.Views.DataTab.mniFromXMLFile": "Din XML local", "SSE.Views.DataTab.textBelow": "Rânduri rezumative sub detalii", "SSE.Views.DataTab.textClear": "<PERSON><PERSON><PERSON>", "SSE.Views.DataTab.textColumns": "Anularea grupării colo<PERSON>lor", "SSE.Views.DataTab.textGroupColumns": "Grupare coloane", "SSE.Views.DataTab.textGroupRows": "Grupare rânduri", "SSE.Views.DataTab.textRightOf": "Rezumat coloane la dreapta detaliilor", "SSE.Views.DataTab.textRows": "Anularea grupării rându<PERSON>", "SSE.Views.DataTab.tipCustomSort": "<PERSON><PERSON><PERSON>", "SSE.Views.DataTab.tipDataFromText": "Colectare date din fișier", "SSE.Views.DataTab.tipDataValidation": "Validarea datelor", "SSE.Views.DataTab.tipExternalLinks": "Vizualizați toate celelalte fișiere de care este legată această foaie de calcul ", "SSE.Views.DataTab.tipGoalSeek": "Selectați formatul de intrare adecvat valorii dorite", "SSE.Views.DataTab.tipGroup": "Grupare zonă de celule", "SSE.Views.DataTab.tipRemDuplicates": "Eliminarea rândurilor dublate dintr-o foaie", "SSE.Views.DataTab.tipToColumns": "Scindarea textului din celulă în coloane", "SSE.Views.DataTab.tipUngroup": "Anularea grupării zonei de celule", "SSE.Views.DataValidationDialog.errorFormula": "Valoarea este evaluată la o eroare în momentul de faţă. Doriți să continuați?", "SSE.Views.DataValidationDialog.errorInvalid": "Valoarea pe care ați introdus-o în câmpul \"{0}\" nu este validă.", "SSE.Views.DataValidationDialog.errorInvalidDate": "Data introdusă în câmpul \"{0}\" nu este validă.", "SSE.Views.DataValidationDialog.errorInvalidList": "Sursa listei trebuie să fie o listă delimitată, sau o referire la un singur rând sau coloană.", "SSE.Views.DataValidationDialog.errorInvalidTime": "Ora introdusă în câmpul \"{0}\" nu este validă.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "Câmpul \"{1}\" trebuie să fie mai mare sau egal cu câmpul \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Valoarea trebuie introdusă în ambele câmpuri \"{0}\" și \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Valoarea trebuie introdusă în câmpul \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "Zona denumită specificată nu este de găsit.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Valorile negative nu sunt permise în condiționale \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "Câmpul \"{0}\" trebuie să conțină o valoare numerică, o expresie numerică, sau o referire la celula care conține o valoare numerică.", "SSE.Views.DataValidationDialog.strError": "Alertă de eroare", "SSE.Views.DataValidationDialog.strInput": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.strSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAlert": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAllow": "Permite", "SSE.Views.DataValidationDialog.textApply": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sunt aplicate altor celulele cu aceleași setări ", "SSE.Views.DataValidationDialog.textCellSelected": "Afișează acest mesaj de intrare atunci când celula este selectată", "SSE.Views.DataValidationDialog.textCompare": "Compar<PERSON> cu", "SSE.Views.DataValidationDialog.textData": "Date", "SSE.Views.DataValidationDialog.textEndDate": "Data de sfârșit ", "SSE.Views.DataValidationDialog.textEndTime": "Ora de terminare", "SSE.Views.DataValidationDialog.textError": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textFormula": "Formula", "SSE.Views.DataValidationDialog.textIgnore": "Ignorare spații necompletate", "SSE.Views.DataValidationDialog.textInput": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMax": "Maxim", "SSE.Views.DataValidationDialog.textMessage": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMin": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textSelectData": "Selectare date", "SSE.Views.DataValidationDialog.textShowDropDown": "Afișare lista derulantă în celulă", "SSE.Views.DataValidationDialog.textShowError": "Afișarea alertei de eroare după ce se introduc date nevalide", "SSE.Views.DataValidationDialog.textShowInput": "Afișarea mesajului de intrare atunci când este selectată celula", "SSE.Views.DataValidationDialog.textSource": "Sursă", "SSE.Views.DataValidationDialog.textStartDate": "Data de început", "SSE.Views.DataValidationDialog.textStartTime": "Ora de început", "SSE.Views.DataValidationDialog.textStop": "Oprire", "SSE.Views.DataValidationDialog.textStyle": "Stil", "SSE.Views.DataValidationDialog.textTitle": "Titlu", "SSE.Views.DataValidationDialog.textUserEnters": "Afișează această alertă de eroare atunci când utilizatorul introduce datele nevalide", "SSE.Views.DataValidationDialog.txtAny": "Orice valoare", "SSE.Views.DataValidationDialog.txtBetween": "între", "SSE.Views.DataValidationDialog.txtDate": "Data", "SSE.Views.DataValidationDialog.txtDecimal": "Zecimal", "SSE.Views.DataValidationDialog.txtElTime": "Tim<PERSON><PERSON> scurs", "SSE.Views.DataValidationDialog.txtEndDate": "Data de sfârșit ", "SSE.Views.DataValidationDialog.txtEndTime": "Ora de terminare", "SSE.Views.DataValidationDialog.txtEqual": "Este egal cu", "SSE.Views.DataValidationDialog.txtGreaterThan": "<PERSON> mare decât", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "<PERSON> mare sau egal", "SSE.Views.DataValidationDialog.txtLength": "Lungime", "SSE.Views.DataValidationDialog.txtLessThan": "mai mic decât", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "Mai mic sau egal cu", "SSE.Views.DataValidationDialog.txtList": "Listă", "SSE.Views.DataValidationDialog.txtNotBetween": "nu între", "SSE.Views.DataValidationDialog.txtNotEqual": "nu este egal cu", "SSE.Views.DataValidationDialog.txtOther": "Altă", "SSE.Views.DataValidationDialog.txtStartDate": "Data de început", "SSE.Views.DataValidationDialog.txtStartTime": "Ora de început", "SSE.Views.DataValidationDialog.txtTextLength": "Lungime text", "SSE.Views.DataValidationDialog.txtTime": "Oră", "SSE.Views.DataValidationDialog.txtWhole": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capAnd": "<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition1": "este egal cu", "SSE.Views.DigitalFilterDialog.capCondition10": "nu se termină cu", "SSE.Views.DigitalFilterDialog.capCondition11": "<PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition12": "nu conține", "SSE.Views.DigitalFilterDialog.capCondition2": "nu este egal cu", "SSE.Views.DigitalFilterDialog.capCondition3": "este mai mare decât", "SSE.Views.DigitalFilterDialog.capCondition30": "este dup<PERSON>", "SSE.Views.DigitalFilterDialog.capCondition4": "este mai mare sau egal cu", "SSE.Views.DigitalFilterDialog.capCondition40": "este după sau egal cu ", "SSE.Views.DigitalFilterDialog.capCondition5": "este mai mic decât", "SSE.Views.DigitalFilterDialog.capCondition50": "este înainte", "SSE.Views.DigitalFilterDialog.capCondition6": "este mai mic sau egal cu", "SSE.Views.DigitalFilterDialog.capCondition60": "este înainte sau egal cu", "SSE.Views.DigitalFilterDialog.capCondition7": "începe cu", "SSE.Views.DigitalFilterDialog.capCondition8": "nu începe cu", "SSE.Views.DigitalFilterDialog.capCondition9": "se termină cu", "SSE.Views.DigitalFilterDialog.capOr": "Sau", "SSE.Views.DigitalFilterDialog.textNoFilter": "<PERSON><PERSON><PERSON><PERSON> filtru", "SSE.Views.DigitalFilterDialog.textShowRows": "Afișare rânduri care", "SSE.Views.DigitalFilterDialog.textUse1": "Utilizați ? pentru a reprezenta orice caracter unic", "SSE.Views.DigitalFilterDialog.textUse2": "Utilizați * pentru a reprezenta o serie de caractere", "SSE.Views.DigitalFilterDialog.txtSelectDate": "Selectați data", "SSE.Views.DigitalFilterDialog.txtTitle": "Filtru particularizat", "SSE.Views.DocumentHolder.advancedEquationText": "Set<PERSON>ri <PERSON>", "SSE.Views.DocumentHolder.advancedImgText": "<PERSON><PERSON><PERSON> imagine", "SSE.Views.DocumentHolder.advancedShapeText": "<PERSON><PERSON><PERSON> a<PERSON> forma", "SSE.Views.DocumentHolder.advancedSlicerText": "<PERSON><PERSON><PERSON> a<PERSON> slicer", "SSE.Views.DocumentHolder.allLinearText": "Tot - Linear", "SSE.Views.DocumentHolder.allProfText": "Tot - Profesional", "SSE.Views.DocumentHolder.bottomCellText": "Aliniere jos", "SSE.Views.DocumentHolder.bulletsText": "Marcatori și numerotare", "SSE.Views.DocumentHolder.centerCellText": "Aliniere la mijloc", "SSE.Views.DocumentHolder.chartDataText": "Selectați datele diagramei", "SSE.Views.DocumentHolder.chartText": "<PERSON><PERSON><PERSON> a<PERSON>a", "SSE.Views.DocumentHolder.chartTypeText": "Modificare tip diagramă", "SSE.Views.DocumentHolder.currLinearText": "Curent - Linear", "SSE.Views.DocumentHolder.currProfText": "Curent - Profesional", "SSE.Views.DocumentHolder.deleteColumnText": "Coloană", "SSE.Views.DocumentHolder.deleteRowText": "Rând", "SSE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.direct270Text": "Rotirea textului în sus", "SSE.Views.DocumentHolder.direct90Text": "Rotirea textului în jos", "SSE.Views.DocumentHolder.directHText": "Orizontală", "SSE.Views.DocumentHolder.directionText": "Orientare text", "SSE.Views.DocumentHolder.editChartText": "Editare date", "SSE.Views.DocumentHolder.editHyperlinkText": "Editare Hyperlink", "SSE.Views.DocumentHolder.hideEqToolbar": "Ascunde bară de instrumente a ecuației", "SSE.Views.DocumentHolder.insertColumnLeftText": "Coloană din stânga", "SSE.Views.DocumentHolder.insertColumnRightText": "Coloană din dreapta", "SSE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "Dimensiunea reală", "SSE.Views.DocumentHolder.removeHyperlinkText": "Eliminare hyperlink", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON>nt<PERSON>ga colo<PERSON>", "SSE.Views.DocumentHolder.selectDataText": "<PERSON><PERSON> din coloană", "SSE.Views.DocumentHolder.selectRowText": "Rând", "SSE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.showEqToolbar": "Afișează bară de instrumente a ecuației", "SSE.Views.DocumentHolder.strDelete": "Eliminare semnătura", "SSE.Views.DocumentHolder.strDetails": "Detalii semnătura", "SSE.Views.DocumentHolder.strSetup": "Configurare semnă<PERSON>", "SSE.Views.DocumentHolder.strSign": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textAlign": "Aliniere", "SSE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "Trimitere în plan secundar", "SSE.Views.DocumentHolder.textArrangeBackward": "Trimitere în ultimul plan", "SSE.Views.DocumentHolder.textArrangeForward": "Aducere în plan apropiat", "SSE.Views.DocumentHolder.textArrangeFront": "Aducere în prim plan", "SSE.Views.DocumentHolder.textAverage": "Me<PERSON>", "SSE.Views.DocumentHolder.textBullets": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCopyCells": "<PERSON><PERSON>re celule", "SSE.Views.DocumentHolder.textCount": "Contorizare", "SSE.Views.DocumentHolder.textCrop": "Trunchiere", "SSE.Views.DocumentHolder.textCropFill": "Umplere", "SSE.Views.DocumentHolder.textCropFit": "Potrivire", "SSE.Views.DocumentHolder.textEditPoints": "Editare puncte", "SSE.Views.DocumentHolder.textEntriesList": "Alege din listă verticală", "SSE.Views.DocumentHolder.textFillDays": "Umplere zile", "SSE.Views.DocumentHolder.textFillFormatOnly": "Umplere numai formatarea", "SSE.Views.DocumentHolder.textFillMonths": "Umplere luni", "SSE.Views.DocumentHolder.textFillSeries": "Umplere serie", "SSE.Views.DocumentHolder.textFillWeekdays": "Umplere zilele săptămânii", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Umplere fără formatare", "SSE.Views.DocumentHolder.textFillYears": "<PERSON><PERSON><PERSON> ani", "SSE.Views.DocumentHolder.textFlashFill": "Umplere instant", "SSE.Views.DocumentHolder.textFlipH": "Răsturnare orizontală", "SSE.Views.DocumentHolder.textFlipV": "Răsturnare verticală", "SSE.Views.DocumentHolder.textFreezePanes": "Înghețare panouri", "SSE.Views.DocumentHolder.textFromFile": "<PERSON>", "SSE.Views.DocumentHolder.textFromStorage": "Din serviciul stocare", "SSE.Views.DocumentHolder.textFromUrl": "<PERSON><PERSON> URL-ul", "SSE.Views.DocumentHolder.textGrowthTrend": "Tendință de creștere", "SSE.Views.DocumentHolder.textLinearTrend": "Tendință liniară ", "SSE.Views.DocumentHolder.textListSettings": "<PERSON><PERSON><PERSON>a", "SSE.Views.DocumentHolder.textMacro": "Asocierea <PERSON>ii", "SSE.Views.DocumentHolder.textMax": "Max", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "<PERSON> multe funcții", "SSE.Views.DocumentHolder.textMoreFormats": "Mai multe formate", "SSE.Views.DocumentHolder.textNone": "Niciunul", "SSE.Views.DocumentHolder.textNumbering": "Numerotare", "SSE.Views.DocumentHolder.textReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON> imagine", "SSE.Views.DocumentHolder.textResetCrop": "Resetare trunchiere", "SSE.Views.DocumentHolder.textRotate": "Rotire", "SSE.Views.DocumentHolder.textRotate270": "Rotire 90° în sens contrar acelor de ceasornic", "SSE.Views.DocumentHolder.textRotate90": "Rotire 90° în sensul acelor de ceasornic", "SSE.Views.DocumentHolder.textSaveAsPicture": "<PERSON><PERSON><PERSON> ca <PERSON>", "SSE.Views.DocumentHolder.textSeries": "Serie", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Aliniere jos", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Aliniere la centru", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Aliniere la stânga", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Aliniere la mijloc", "SSE.Views.DocumentHolder.textShapeAlignRight": "Aliniere la dreapta", "SSE.Views.DocumentHolder.textShapeAlignTop": "Aliniere sus", "SSE.Views.DocumentHolder.textShapesMerge": "Îmbinare forme", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "Sumă", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "Dezghețare panouri", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "Marcatori săgeată", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Marcatori simbol de bifare", "SSE.Views.DocumentHolder.tipMarkersDash": "Marcatori cu o liniuță", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Marcatori romb umplut", "SSE.Views.DocumentHolder.tipMarkersFRound": "Marcatori cerc umplut", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Marcatori pătrat umplut", "SSE.Views.DocumentHolder.tipMarkersHRound": "Marcatori cerc gol ", "SSE.Views.DocumentHolder.tipMarkersStar": "Marc<PERSON>i stele", "SSE.Views.DocumentHolder.topCellText": "Aliniere sus", "SSE.Views.DocumentHolder.txtAccounting": "Contabilitate", "SSE.Views.DocumentHolder.txtAddComment": "<PERSON><PERSON><PERSON><PERSON> come<PERSON>", "SSE.Views.DocumentHolder.txtAddNamedRange": "Definire nume", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "Ascendent", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Potrivire automată lățime coloană", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Potrivire automată înălțime rând", "SSE.Views.DocumentHolder.txtAverage": "Me<PERSON>", "SSE.Views.DocumentHolder.txtCellFormat": "Formatare celule", "SSE.Views.DocumentHolder.txtClear": "Golire", "SSE.Views.DocumentHolder.txtClearAll": "Toate", "SSE.Views.DocumentHolder.txtClearComments": "Comenta<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearFormat": "Formatare", "SSE.Views.DocumentHolder.txtClearHyper": "Hyperlinkurile", "SSE.Views.DocumentHolder.txtClearPivotField": "Golire filtru din {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Golire grupurile de diagrame sparkline selectate", "SSE.Views.DocumentHolder.txtClearSparklines": "Golire diagrame sparkline selectate", "SSE.Views.DocumentHolder.txtClearText": "Text", "SSE.Views.DocumentHolder.txtCollapse": "Restrângere", "SSE.Views.DocumentHolder.txtCollapseEntire": "Restrânge câmpul întreg", "SSE.Views.DocumentHolder.txtColumn": "<PERSON>nt<PERSON>ga colo<PERSON>", "SSE.Views.DocumentHolder.txtColumnWidth": "Setarea coloanei la lățime", "SSE.Views.DocumentHolder.txtCondFormat": "Formatarea condiționată", "SSE.Views.DocumentHolder.txtCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCount": "Contorizare", "SSE.Views.DocumentHolder.txtCurrency": "Monedă", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Lățimea particularizată coloană ", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Înălțimea particularizată de rând", "SSE.Views.DocumentHolder.txtCustomSort": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCut": "<PERSON>up<PERSON>", "SSE.Views.DocumentHolder.txtDateLong": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDateShort": "<PERSON><PERSON><PERSON> scurtă", "SSE.Views.DocumentHolder.txtDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDelField": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDescending": "Descendent", "SSE.Views.DocumentHolder.txtDifference": "Diferență din", "SSE.Views.DocumentHolder.txtDistribHor": "Distribuire pe orizontală", "SSE.Views.DocumentHolder.txtDistribVert": "Distribuire pe verticală", "SSE.Views.DocumentHolder.txtEditComment": "Editare comentariu", "SSE.Views.DocumentHolder.txtEditObject": "Editare obiect", "SSE.Views.DocumentHolder.txtExpand": "Extindere", "SSE.Views.DocumentHolder.txtExpandCollapse": "Extindere/Restrângere", "SSE.Views.DocumentHolder.txtExpandEntire": "Extinde câmpul întreg", "SSE.Views.DocumentHolder.txtFieldSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtFilter": "Filtrare", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filtrare după culoarea celulei", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filtrarea după culoarea fontului", "SSE.Views.DocumentHolder.txtFilterValue": "Filtrarea după valoarea celulei selectate", "SSE.Views.DocumentHolder.txtFormula": "Inserare funcție", "SSE.Views.DocumentHolder.txtFraction": "Frac<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtGeneral": "General", "SSE.Views.DocumentHolder.txtGetLink": "Obțineți link-ul către această zonă", "SSE.Views.DocumentHolder.txtGrandTotal": "<PERSON>uri generale", "SSE.Views.DocumentHolder.txtGroup": "Grupare", "SSE.Views.DocumentHolder.txtHide": "Ascunde", "SSE.Views.DocumentHolder.txtIndex": "Index", "SSE.Views.DocumentHolder.txtInsert": "Inserare", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hyperlink", "SSE.Views.DocumentHolder.txtInsImage": "Inserare imagine dintr-un fisier", "SSE.Views.DocumentHolder.txtInsImageUrl": "Inserare imagine din URL", "SSE.Views.DocumentHolder.txtLabelFilter": "Filtre de etichetă", "SSE.Views.DocumentHolder.txtMax": "Max", "SSE.Views.DocumentHolder.txtMin": "Min", "SSE.Views.DocumentHolder.txtMoreOptions": "Mai multe opțiuni", "SSE.Views.DocumentHolder.txtNormal": "<PERSON>ăr<PERSON> calcul", "SSE.Views.DocumentHolder.txtNumber": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtNumFormat": "Formatul de număr", "SSE.Views.DocumentHolder.txtPaste": "Lipire", "SSE.Views.DocumentHolder.txtPercent": "% din", "SSE.Views.DocumentHolder.txtPercentage": "Procentaj", "SSE.Views.DocumentHolder.txtPercentDiff": "% diferență din", "SSE.Views.DocumentHolder.txtPercentOfCol": "% din totalul coloanelor", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% din total general", "SSE.Views.DocumentHolder.txtPercentOfParent": "% din total părinte", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% din total coloană părinte", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% din total rând părinte", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% total parțial în", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% din total rând", "SSE.Views.DocumentHolder.txtPivotSettings": "<PERSON><PERSON><PERSON> ta<PERSON>", "SSE.Views.DocumentHolder.txtProduct": "Produs", "SSE.Views.DocumentHolder.txtRankAscending": "Rang de la cel mai mic la cel mai mare", "SSE.Views.DocumentHolder.txtRankDescending": "Rang de la cel mai mare la cel mai mic", "SSE.Views.DocumentHolder.txtReapply": "Reaplicare", "SSE.Views.DocumentHolder.txtRefresh": "Actualizare", "SSE.Views.DocumentHolder.txtRow": "<PERSON>nt<PERSON>g r<PERSON>d", "SSE.Views.DocumentHolder.txtRowHeight": "Setare rând la înălțime", "SSE.Views.DocumentHolder.txtRunTotal": "Total în execuție în", "SSE.Views.DocumentHolder.txtScientific": "Științific ", "SSE.Views.DocumentHolder.txtSelect": "Selectare", "SSE.Views.DocumentHolder.txtShiftDown": "Deplasare celule în jos", "SSE.Views.DocumentHolder.txtShiftLeft": "Deplasare celule la stânga", "SSE.Views.DocumentHolder.txtShiftRight": "Deplasare celule la dreapta", "SSE.Views.DocumentHolder.txtShiftUp": "Deplasare celule în sus", "SSE.Views.DocumentHolder.txtShow": "<PERSON><PERSON>ș<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShowAs": "Afișare valori ca", "SSE.Views.DocumentHolder.txtShowComment": "<PERSON><PERSON><PERSON><PERSON> comentariu", "SSE.Views.DocumentHolder.txtShowDetails": "Afișare <PERSON>talii", "SSE.Views.DocumentHolder.txtSort": "Sortare", "SSE.Views.DocumentHolder.txtSortCellColor": "Culoarea celulei selectate în partea de sus", "SSE.Views.DocumentHolder.txtSortFontColor": "Culoarea fontului selectat în partea de sus", "SSE.Views.DocumentHolder.txtSortOption": "Mai multe opțiuni de sortare", "SSE.Views.DocumentHolder.txtSparklines": "Diagrame sparkline", "SSE.Views.DocumentHolder.txtSubtotalField": "Subtotal", "SSE.Views.DocumentHolder.txtSum": "Sumă", "SSE.Views.DocumentHolder.txtSummarize": "<PERSON><PERSON><PERSON> valori dup<PERSON>", "SSE.Views.DocumentHolder.txtText": "Text", "SSE.Views.DocumentHolder.txtTextAdvanced": "<PERSON><PERSON><PERSON> para<PERSON> ", "SSE.Views.DocumentHolder.txtTime": "Oră", "SSE.Views.DocumentHolder.txtTop10": "Primele 10", "SSE.Views.DocumentHolder.txtUngroup": "Anular<PERSON> g<PERSON>i", "SSE.Views.DocumentHolder.txtValueFieldSettings": "<PERSON><PERSON><PERSON> valo<PERSON>", "SSE.Views.DocumentHolder.txtValueFilter": "Filtre de valoare", "SSE.Views.DocumentHolder.txtWidth": "Lățime", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "Aliniere verticală", "SSE.Views.ExternalLinksDlg.closeButtonText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Actualizarea automată a datelor din sursele legate", "SSE.Views.ExternalLinksDlg.textChange": "Schimbare sursă", "SSE.Views.ExternalLinksDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.textDeleteAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> toate link<PERSON>", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textOpen": "Deschidere sursă", "SSE.Views.ExternalLinksDlg.textSource": "Sursă", "SSE.Views.ExternalLinksDlg.textStatus": "Stare", "SSE.Views.ExternalLinksDlg.textUnknown": "Necunoscut", "SSE.Views.ExternalLinksDlg.textUpdate": "Actualizare valori", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Actualizare totală", "SSE.Views.ExternalLinksDlg.textUpdating": "În curs de actualizare...", "SSE.Views.ExternalLinksDlg.txtTitle": "<PERSON><PERSON> externe", "SSE.Views.FieldSettingsDialog.strLayout": "Aspect", "SSE.Views.FieldSettingsDialog.strSubtotals": "Subtotaluri", "SSE.Views.FieldSettingsDialog.textNumFormat": "Formatul de număr", "SSE.Views.FieldSettingsDialog.textReport": "<PERSON><PERSON> de raport", "SSE.Views.FieldSettingsDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtAverage": "Me<PERSON>", "SSE.Views.FieldSettingsDialog.txtBlank": "Se inserează rânduri necompletate după fiecare etichetă de element", "SSE.Views.FieldSettingsDialog.txtBottom": "Afișează în partea de jos a grupului", "SSE.Views.FieldSettingsDialog.txtCompact": "Compact", "SSE.Views.FieldSettingsDialog.txtCount": "Contorizare", "SSE.Views.FieldSettingsDialog.txtCountNums": "Contorizare numere", "SSE.Views.FieldSettingsDialog.txtCustomName": "Nume particularizat", "SSE.Views.FieldSettingsDialog.txtEmpty": "Afișare elemente fără date", "SSE.Views.FieldSettingsDialog.txtMax": "Max", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "Schiță", "SSE.Views.FieldSettingsDialog.txtProduct": "Produs", "SSE.Views.FieldSettingsDialog.txtRepeat": "Repetare etichete element pentru fiecare rând", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Afișare subtotaluri", "SSE.Views.FieldSettingsDialog.txtSourceName": "Numele sursei:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "Sumă", "SSE.Views.FieldSettingsDialog.txtSummarize": "Funcția de subtotalizare", "SSE.Views.FieldSettingsDialog.txtTabular": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtTop": "Afișează în partea de sus a grupului", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.ariaFileMenu": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnBackCaption": "Deschidere locația fișierului", "SSE.Views.FileMenu.btnCloseEditor": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnCloseMenuCaption": "Înapoi", "SSE.Views.FileMenu.btnCreateNewCaption": "Crearea unui document nou", "SSE.Views.FileMenu.btnDownloadCaption": "Descărcare ca", "SSE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnExportToPDFCaption": "Export în format PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "Deschidere", "SSE.Views.FileMenu.btnHelpCaption": "Asistenț<PERSON>", "SSE.Views.FileMenu.btnHistoryCaption": "Istoricul versiune", "SSE.Views.FileMenu.btnInfoCaption": "Informații", "SSE.Views.FileMenu.btnPrintCaption": "Imp<PERSON><PERSON>", "SSE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnRecentFilesCaption": "Deschidere document recent", "SSE.Views.FileMenu.btnRenameCaption": "Redenumire", "SSE.Views.FileMenu.btnReturnCaption": "Înapoi la foia de calcul", "SSE.Views.FileMenu.btnRightsCaption": "Permisiuni de acces", "SSE.Views.FileMenu.btnSaveAsCaption": "<PERSON><PERSON><PERSON> ca", "SSE.Views.FileMenu.btnSaveCaption": "Salvează", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Salvare copie ca", "SSE.Views.FileMenu.btnSettingsCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Comutare la modul Mobil", "SSE.Views.FileMenu.btnToEditCaption": "Editare foaie de calcul", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Foaie de calcul necompletată", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON><PERSON> nou", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Aplicare", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "<PERSON><PERSON><PERSON><PERSON> autor", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Adă<PERSON>re proprietate", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Adăugare text", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplicația", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Modificare permisiuni", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "În comun", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "A fost creat", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Proprietate de document", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Modificat ultima dată de către", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Data ultimei modificări", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "<PERSON>u", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Proprietar", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Locația", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Propriet<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "O proprietate cu acest nume există deja", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Persoane care au dreptul de acces", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Informații despre foaia de calcul", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Subiect", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Etichete", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Titlu", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "S-a încărcat", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "Da", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "<PERSON><PERSON><PERSON> de acces", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Modificare permisiuni", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Persoane care au dreptul de acces", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Aplicare", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Modul de editare colaborativă", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Utilizează sistemul de dată 1904", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Separator <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Limbă de dicționar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Activare calcul iterativ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Rapid", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Sugestie font", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Limba formulă", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Exemplu: <PERSON><PERSON><PERSON>; <PERSON>; <PERSON>; <PERSON><PERSON>l", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Afișare sfaturi ecran pentru funcții", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Afișare bară de defilare horizontală", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Ignoră cuvintele cu MAJUSCULE", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Ignoră cuvintele care conțin numere", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "<PERSON><PERSON><PERSON> macrocomandă", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Modificare maximă", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Număr maxim de iterații", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Afișarea butonului Opțiuni lipire de fiecare dată când lipiți conținut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Stilul de referință R1C1", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Setări regionale", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Exemplu:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "Interfața care acceptă limbile cu scriere de la dreapta la stânga", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Afișare comentarii din foaia de calcul", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Afișarea modificărilor efectuate de către alți utilizatori", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Afișare comentarii rezolvate", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Fixare la grilă în timpul defilării", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Strict", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "<PERSON><PERSON> fil<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Tema interfeței", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Separator mii", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Unitate de măsură ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Utiluzați separatoarele care sunt definite în setările regionale ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Afișare bară de defilare verticală", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Valori implicite Zoom", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "La fiecare 10 minute", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "La fiecare 30 minute", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "La fiecare 5 minute", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "La fiecare oră", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Recuperare automată", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "<PERSON><PERSON><PERSON> automat<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Dezactivat", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "Umplere", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Salvarea versiunilor intermediare", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "La fiecare minută", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "Aspect", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Opțiuni AutoCorecție...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Belarusă", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Bulgară", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Catalană", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Mod implicit memoria Cache", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Calculare", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centimetru", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Colaborare", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Particularizare acces rapid", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Germană", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Editare și salvare", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "G<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "Engleză", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Datele introduse nu pot fi utilizate. Este posibil să fie nevoie de un număr întreg sau zecimal.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Spaniolă", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Co-editare în timp real. Toate modificările sunt salvate automat.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "Franceză", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "<PERSON>ean<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indoneziană", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Inch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Italiană", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Japoneză", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Ultima utilizare", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Laoțian<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Letonă", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "ca OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Nativ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Norvegiană", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Neerlandeză", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Poloneză", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Verificare", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Punct", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Portugeză (Brazilia)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Afișează butonul Imprimare rapidă în antetul aplicației de editare", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "Imprimanta pentru tipărirea documentului devine ultima imprimantă utilizată sau imprimantă implicită", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Regiune", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Română", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Se activează toate", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Se activează toate macrocomenzile, fără notificare", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Activare cititor de ecran", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Direcția particularizată a foii de calcul", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "Această setare va afecta numai foi de calcul noi.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Stânga-dreapta", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Dreapta-stânga", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Slovac<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Slovenă", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Se dezactivează toate", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Se dezactivează toate macrocomenzile, fără notificare", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Utilizați butonul Salvare pentru a sincroniza modificările pe care le efectuați dvs. sau alte persoane", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Culoarea barei de instrumente ca culoare de fundal pentru file", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ucraineană", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Utilizați tasta Alt pentru a naviga în interfața de utilizator cu ajutorul tastaturii", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Utilizați tasta Option pentru a naviga în interfața de utilizator cu ajutorul tastaturii", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vietnameză", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Afișare notificări", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Se dezactivează toate macrocomenzile, cu notificare   ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "ca Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Workspace", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "<PERSON>ez<PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Avertisment", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Protejarea foii de calcul", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Semnături valide au fost adăugate la foaia de calcul.<br>Foaia de calcul este protejată împotriva editării.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Asigurați integritatea foii de calcul prin adăugarea unei<br>semnături digitale invizibile", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Editare foaie de calcul", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "În rezultatul editării toate semnăturile din foaie de calcul vor fi șterse.<br>Sunteți sigur că doriți să continuați? ", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Foaia de calcul este protejată prin parolă", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Criptează foaia de calcul cu parolă", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Foaia de calcul trebuie să fie semnată.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Semnături valide au fost adăugate în foaia de calcul. Foaia de calcul este protejată împotriva editării.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "O parte din semnături electronice pe foaia de calcul nu sunt valide sau nu pot fi verificate. Foaia de calcul este protejată împotriva editării.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Vizualiza<PERSON> semn<PERSON><PERSON>i", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Descărcare ca", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Salvare copie ca", "SSE.Views.FillSeriesDialog.textAuto": "Completare automată", "SSE.Views.FillSeriesDialog.textCols": "<PERSON><PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textDate": "Dată", "SSE.Views.FillSeriesDialog.textDateUnit": "Unitate dată", "SSE.Views.FillSeriesDialog.textDay": "Ziu<PERSON>", "SSE.Views.FillSeriesDialog.textGrowth": "Creștere", "SSE.Views.FillSeriesDialog.textLinear": "<PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textMonth": "Lună", "SSE.Views.FillSeriesDialog.textRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textSeries": "Serie în", "SSE.Views.FillSeriesDialog.textStep": "Valoare pas", "SSE.Views.FillSeriesDialog.textStop": "Valoare oprire", "SSE.Views.FillSeriesDialog.textTitle": "Serie", "SSE.Views.FillSeriesDialog.textTrend": "Tendință", "SSE.Views.FillSeriesDialog.textType": "Tip", "SSE.Views.FillSeriesDialog.textWeek": "Zi din săptămână", "SSE.Views.FillSeriesDialog.textYear": "An", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Datele introduse nu pot fi utilizate. Este posibil să fie nevoie de un număr întreg sau zecimal.", "SSE.Views.FormatRulesEditDlg.fillColor": "Culoare de umplere", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Avertisment", "SSE.Views.FormatRulesEditDlg.text2Scales": "Scară cu două culori", "SSE.Views.FormatRulesEditDlg.text3Scales": "Scară cu trei culori", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Toate borduri", "SSE.Views.FormatRulesEditDlg.textAppearance": "Aspect bară", "SSE.Views.FormatRulesEditDlg.textApply": "Se aplică la zonă", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automat", "SSE.Views.FormatRulesEditDlg.textAxis": "Axă", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Direc<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBold": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBorder": "Bordura", "SSE.Views.FormatRulesEditDlg.textBordersColor": "<PERSON><PERSON><PERSON> borduri", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "<PERSON><PERSON> b<PERSON>", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Bord<PERSON>le în partea de jos", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Formatarea condiționată nu poate fi ajutată.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Punctul central al celulei", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Bordurile verticale în interiorul ", "SSE.Views.FormatRulesEditDlg.textClear": "Golire", "SSE.Views.FormatRulesEditDlg.textColor": "Culoare text", "SSE.Views.FormatRulesEditDlg.textContext": "Context", "SSE.Views.FormatRulesEditDlg.textCustom": "Particularizat", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Bordură diagonală descendentă", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "<PERSON><PERSON><PERSON><PERSON> diagonală ascendentă", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Introduceți o formulă valabilă.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "Formula pe care ați introdus-o nu evaluează număr, data, ora sau șir.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Introduceți o valoare.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "Valoarea pe care ați introdus-o nu este un număr, data, ora sau șir valid.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "Valoarea pentru {0} trebuie să fie mai mare mai mare decât {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Introduceți cifra între {0} și {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Umplere", "SSE.Views.FormatRulesEditDlg.textFormat": "Formatare", "SSE.Views.FormatRulesEditDlg.textFormula": "Formula", "SSE.Views.FormatRulesEditDlg.textGradient": "Gradient", "SSE.Views.FormatRulesEditDlg.textIconLabel": "atunci când {0} {1} și", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "atunci când {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "atunci când valoarea este", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Datele din una sau mai multe zone de pictograme se suprapun.<br>Ajustați valorile pentru zonele de pictograme ca zonele să nu se suprapună.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Stil icoană", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Borduri în interiorul ", "SSE.Views.FormatRulesEditDlg.textInvalid": "Zona de date nevalidă.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "EROARE! Zonă de celule nu este validă", "SSE.Views.FormatRulesEditDlg.textItalic": "Cursiv", "SSE.Views.FormatRulesEditDlg.textItem": "Element", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "De la stânga la dreapta", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Bordură la stânga", "SSE.Views.FormatRulesEditDlg.textLongBar": "bar<PERSON> cea mai lungă ", "SSE.Views.FormatRulesEditDlg.textMaximum": "Maxim", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Punct maxim", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Bordurile orizontale în interiorul ", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Punct median", "SSE.Views.FormatRulesEditDlg.textMinimum": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Punct minim", "SSE.Views.FormatRulesEditDlg.textNegative": "Negativ", "SSE.Views.FormatRulesEditDlg.textNewColor": "Mai multe culori", "SSE.Views.FormatRulesEditDlg.textNoBorders": "<PERSON><PERSON><PERSON><PERSON> bord<PERSON>", "SSE.Views.FormatRulesEditDlg.textNone": "Niciunul", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Una sau mai multe valori specificate sunt valorile procentuale nevalide", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Valoarea specificată {0} pentru procentaj nu este validă.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Una sau mail multe valori specificate sunt valorile de percentilă nevalide.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Valoarea specificată {0} pentru percentilă nu este validă.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "<PERSON><PERSON><PERSON>n <PERSON>", "SSE.Views.FormatRulesEditDlg.textPercent": "Procent", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentilă", "SSE.Views.FormatRulesEditDlg.textPosition": "Poziție", "SSE.Views.FormatRulesEditDlg.textPositive": "Pozitiv", "SSE.Views.FormatRulesEditDlg.textPresets": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPreview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Utilizarea referințelor relative nu este acceptată pentru formatarea condiţionată utilizând scală de culori, bare de date sau seturi de pictogramă.", "SSE.Views.FormatRulesEditDlg.textReverse": "Ordine pictograme inversată", "SSE.Views.FormatRulesEditDlg.textRight2Left": "De la dreapta la stânga", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Bord<PERSON> din dreapta", "SSE.Views.FormatRulesEditDlg.textRule": "Regulă", "SSE.Views.FormatRulesEditDlg.textSameAs": "La fel ca pozitiv", "SSE.Views.FormatRulesEditDlg.textSelectData": "Selectare date", "SSE.Views.FormatRulesEditDlg.textShortBar": "bară cea mai scurtă", "SSE.Views.FormatRulesEditDlg.textShowBar": "Se afișează doar bara", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Se afișează doar pictograma", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Acest tip de referință nu este acceptat în formula pentru formatarea condiționată.<br>Faceți referire la o singură celulă sau utilizați o funcție de referință din foia de calcul cum este =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Solidă", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Tăiere cu o linie", "SSE.Views.FormatRulesEditDlg.textSubscript": "Indice", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Exponent", "SSE.Views.FormatRulesEditDlg.textTopBorders": "<PERSON><PERSON><PERSON> de sus", "SSE.Views.FormatRulesEditDlg.textUnderline": "Subliniat", "SSE.Views.FormatRulesEditDlg.tipBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Formatul de număr", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Contabilitate", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Monedă", "SSE.Views.FormatRulesEditDlg.txtDate": "Dată", "SSE.Views.FormatRulesEditDlg.txtDateLong": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDateShort": "<PERSON><PERSON><PERSON> scurtă", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Câmp obligatoriu", "SSE.Views.FormatRulesEditDlg.txtFraction": "Frac<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtGeneral": "General", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "<PERSON><PERSON> pict<PERSON>", "SSE.Views.FormatRulesEditDlg.txtNumber": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Procentaj", "SSE.Views.FormatRulesEditDlg.txtScientific": "Științific ", "SSE.Views.FormatRulesEditDlg.txtText": "Text", "SSE.Views.FormatRulesEditDlg.txtTime": "Oră", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Editare regulă de formatare", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Nouă regulă de formatare", "SSE.Views.FormatRulesManagerDlg.guestText": "Invitat", "SSE.Views.FormatRulesManagerDlg.lockText": "Blocat", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 abatere standard deasupra medie", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 abatere standard sub medie", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 abateri standard deasupra medie", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 abateri standard sub medie", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 abateri standard deasupra medie", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 abateri standard sub medie", "SSE.Views.FormatRulesManagerDlg.textAbove": "<PERSON><PERSON><PERSON>ra medie", "SSE.Views.FormatRulesManagerDlg.textApply": "Se aplică la", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Valoarea din celulă se începe cu", "SSE.Views.FormatRulesManagerDlg.textBelow": "Sub medie", "SSE.Views.FormatRulesManagerDlg.textBetween": "este între {0} și {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Valoarea din celulă", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Scală de culoare cu gradare ", "SSE.Views.FormatRulesManagerDlg.textContains": "Valoarea din celulă conține", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "<PERSON><PERSON><PERSON> canț<PERSON> o valorae goală", "SSE.Views.FormatRulesManagerDlg.textContainsError": "<PERSON><PERSON><PERSON> conț<PERSON> o eroare", "SSE.Views.FormatRulesManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textDown": "<PERSON><PERSON>e regul<PERSON> în jos", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "<PERSON><PERSON> dub<PERSON>", "SSE.Views.FormatRulesManagerDlg.textEdit": "Editare", "SSE.Views.FormatRulesManagerDlg.textEnds": "Valoarea din celulă se termină cu", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Egal cu sau deasupra medie", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Egal cu sau sub medie", "SSE.Views.FormatRulesManagerDlg.textFormat": "Formatare", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Ansamblul de icoane", "SSE.Views.FormatRulesManagerDlg.textNew": "Nou", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "nu este între {0} și {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Valoarea din celulă nu conține", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "<PERSON><PERSON><PERSON> nu conține nicio valoare goală", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "<PERSON><PERSON><PERSON> nu conține nicio eroare", "SSE.Views.FormatRulesManagerDlg.textRules": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textScope": "Afișare reguli formatare pentru", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Selectare date", "SSE.Views.FormatRulesManagerDlg.textSelection": "Sele<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "Acest tabel pivot", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Această foaie de lucru ", "SSE.Views.FormatRulesManagerDlg.textThisTable": "Acest tabel", "SSE.Views.FormatRulesManagerDlg.textUnique": "Valorile unice", "SSE.Views.FormatRulesManagerDlg.textUp": "Mutare regulă în sus", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Acest element este editat de către un alt utilizator.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Formatarea condiționată", "SSE.Views.FormatSettingsDialog.textCategory": "Categorie", "SSE.Views.FormatSettingsDialog.textDecimal": "Zecimal", "SSE.Views.FormatSettingsDialog.textFormat": "Formatare", "SSE.Views.FormatSettingsDialog.textLinked": "Legătură cu sursă", "SSE.Views.FormatSettingsDialog.textSeparator": "Utilizați separatorul 1000", "SSE.Views.FormatSettingsDialog.textSymbols": "Simbo<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textTitle": "Formatul de număr", "SSE.Views.FormatSettingsDialog.txtAccounting": "Contabilitate", "SSE.Views.FormatSettingsDialog.txtAs10": "<PERSON><PERSON> <PERSON><PERSON>imi (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "<PERSON><PERSON> sutimi (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "Ca șaisprezecimi (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Ca jumătăți (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "<PERSON><PERSON> sferturi (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Ca optimi (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Monedă", "SSE.Views.FormatSettingsDialog.txtCustom": "Particularizat", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Vă rugăm să tastați formatul numeric particularizat cu atenție. Spreadsheet Editor nu verifică formate particularizate pentru a detecta posibilele erori, ceea ce poate afecta fișierul xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "Data", "SSE.Views.FormatSettingsDialog.txtFraction": "Frac<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtGeneral": "General", "SSE.Views.FormatSettingsDialog.txtNone": "Niciunul", "SSE.Views.FormatSettingsDialog.txtNumber": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtPercentage": "Procentaj", "SSE.Views.FormatSettingsDialog.txtSample": "Eșantion:", "SSE.Views.FormatSettingsDialog.txtScientific": "Științific ", "SSE.Views.FormatSettingsDialog.txtText": "Text", "SSE.Views.FormatSettingsDialog.txtTime": "Oră", "SSE.Views.FormatSettingsDialog.txtUpto1": "Până la o cifră (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Până la două cifre (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Până la trei cifre (131/135)", "SSE.Views.FormulaDialog.sDescription": "Des<PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.textGroupDescription": "Selectați grup de funcții", "SSE.Views.FormulaDialog.textListDescription": "Selectați funcția", "SSE.Views.FormulaDialog.txtRecommended": "Recomandate", "SSE.Views.FormulaDialog.txtSearch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtTitle": "Inserare funcție", "SSE.Views.FormulaTab.capBtnRemoveArr": "Eliminare <PERSON>", "SSE.Views.FormulaTab.capBtnTraceDep": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.capBtnTracePrec": "<PERSON><PERSON><PERSON>e", "SSE.Views.FormulaTab.textAutomatic": "Automat", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "<PERSON><PERSON><PERSON> foaia", "SSE.Views.FormulaTab.textCalculateWorkbook": "Calculare registrul de calcul", "SSE.Views.FormulaTab.textManual": "Manual", "SSE.Views.FormulaTab.tipCalculate": "Calculare", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Calculare întreg registrul de calcul", "SSE.Views.FormulaTab.tipRemoveArr": "Eliminare săgeți trasate cu Trasare precedente și Trasare dependențe ", "SSE.Views.FormulaTab.tipShowFormulas": "Afişare formule în celule în loc de rezultat calculat", "SSE.Views.FormulaTab.tipTraceDep": "Afișează săgețile care indică ce celule sunt afectate de valoarea celulei selectate", "SSE.Views.FormulaTab.tipTracePrec": "Afișează săgețile care indică ce celule sunt afectate de valoarea celulei selectate", "SSE.Views.FormulaTab.tipWatch": "Adăugare celule în lista Fereastră supraveghere", "SSE.Views.FormulaTab.txtAdditional": "Suplimentar", "SSE.Views.FormulaTab.txtAutosum": "Însumare automată", "SSE.Views.FormulaTab.txtAutosumTip": "Sumă", "SSE.Views.FormulaTab.txtCalculation": "Calculare", "SSE.Views.FormulaTab.txtFormula": "Fun<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormulaTip": "Inserare funcție", "SSE.Views.FormulaTab.txtMore": "<PERSON> multe funcții", "SSE.Views.FormulaTab.txtRecent": "Utilizate recent", "SSE.Views.FormulaTab.txtRemDep": "Eliminare săgeți dependente", "SSE.Views.FormulaTab.txtRemPrec": "Eliminare sageti anterioare", "SSE.Views.FormulaTab.txtShowFormulas": "Afișare formule", "SSE.Views.FormulaTab.txtWatch": "Fereastră de supraveghere", "SSE.Views.FormulaWizard.textAny": "Orice", "SSE.Views.FormulaWizard.textArgument": "Argument", "SSE.Views.FormulaWizard.textFunction": "Fun<PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textFunctionRes": "Rezultat funcție", "SSE.Views.FormulaWizard.textHelp": "Funcția de asistență", "SSE.Views.FormulaWizard.textLogical": "Logic", "SSE.Views.FormulaWizard.textNoArgs": "Funcția nu are argumente", "SSE.Views.FormulaWizard.textNoArgsDesc": "argumentul fără descriere", "SSE.Views.FormulaWizard.textNumber": "num<PERSON><PERSON>", "SSE.Views.FormulaWizard.textReadMore": "Citește mai departe", "SSE.Views.FormulaWizard.textRef": "referință", "SSE.Views.FormulaWizard.textText": "text", "SSE.Views.FormulaWizard.textTitle": "Argumente funcție", "SSE.Views.FormulaWizard.textValue": "Rezultat formulă", "SSE.Views.GoalSeekDlg.textChangingCell": "<PERSON><PERSON>chi<PERSON> celulei", "SSE.Views.GoalSeekDlg.textDataRangeError": "În formula lipsește intervalul", "SSE.Views.GoalSeekDlg.textMustContainFormula": "<PERSON><PERSON>la trebuie să conțină o formulă", "SSE.Views.GoalSeekDlg.textMustContainValue": "<PERSON><PERSON>la trebuie să conțină o valoare", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Rezultatul formulei în celula trebuie să fie un număr.", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Referința trebuie să fie o singură celulă ", "SSE.Views.GoalSeekDlg.textSelectData": "Selectare date", "SSE.Views.GoalSeekDlg.textSetCell": "Setare celulă", "SSE.Views.GoalSeekDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON> rezultat", "SSE.Views.GoalSeekDlg.textToValue": "Valoare în", "SSE.Views.GoalSeekDlg.txtEmpty": "Câmp obligatoriu", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Datele introduse nu pot fi utilizate. Este posibil să fie nevoie de un număr întreg sau zecimal.", "SSE.Views.GoalSeekStatusDlg.textContinue": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Valoarea actuală:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "<PERSON>ăutare rezultat din celula {0} a găsit o soluție. ", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "S-ar putea să nu fie găsită o soluție cu funcția Căutare rezultat din celula {0}.", "SSE.Views.GoalSeekStatusDlg.textPause": "Pauză", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Căutare rezultat din celula {0} la iterația #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "Pas", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Valoare țintă:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Stare Căutare rezultat", "SSE.Views.HeaderFooterDialog.textAlign": "Se aliniază la marginile paginii", "SSE.Views.HeaderFooterDialog.textAll": "Toate paginile", "SSE.Views.HeaderFooterDialog.textBold": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textCenter": "La centru", "SSE.Views.HeaderFooterDialog.textColor": "Culoare text", "SSE.Views.HeaderFooterDialog.textDate": "Data", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Prima pagina diferită", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Pagini pare și impare diferite", "SSE.Views.HeaderFooterDialog.textEven": "<PERSON><PERSON><PERSON> pară", "SSE.Views.HeaderFooterDialog.textFileName": "Numele fișierului", "SSE.Views.HeaderFooterDialog.textFirst": "Prima pagina", "SSE.Views.HeaderFooterDialog.textFooter": "Subsol", "SSE.Views.HeaderFooterDialog.textHeader": "Antet", "SSE.Views.HeaderFooterDialog.textImage": "Imagine", "SSE.Views.HeaderFooterDialog.textInsert": "Inserare", "SSE.Views.HeaderFooterDialog.textItalic": "Cursiv", "SSE.Views.HeaderFooterDialog.textLeft": "Stânga", "SSE.Views.HeaderFooterDialog.textMaxError": "Șirul de text este prea lung. Reduceți numărul de caractere introduse.", "SSE.Views.HeaderFooterDialog.textNewColor": "Mai multe culori", "SSE.Views.HeaderFooterDialog.textOdd": "Pagin<PERSON> impară", "SSE.Views.HeaderFooterDialog.textPageCount": "Numerotarea paginilor", "SSE.Views.HeaderFooterDialog.textPageNum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textPresets": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textScale": "Scalare la document", "SSE.Views.HeaderFooterDialog.textSheet": "<PERSON><PERSON><PERSON> foii", "SSE.Views.HeaderFooterDialog.textStrikeout": "Tăiere cu o linie", "SSE.Views.HeaderFooterDialog.textSubscript": "Indice", "SSE.Views.HeaderFooterDialog.textSuperscript": "Exponent", "SSE.Views.HeaderFooterDialog.textTime": "Oră", "SSE.Views.HeaderFooterDialog.textTitle": "Set<PERSON>ri antet/subsol", "SSE.Views.HeaderFooterDialog.textUnderline": "Subliniat", "SSE.Views.HeaderFooterDialog.tipFontName": "Font", "SSE.Views.HeaderFooterDialog.tipFontSize": "Dimensiune font", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Legătură la", "SSE.Views.HyperlinkSettingsDialog.strRange": "Zona", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Zona selectată", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Introduceți textul legendei aici", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Introduceți link-ul aici", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Introduceți textul sfatului aici", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Link extern", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Obținere link", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Zonă de date internă", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "EROARE! Zonă de celule nu este validă", "SSE.Views.HyperlinkSettingsDialog.textNames": "Numele definite", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Selectare date", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Selectare fișier", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Foile", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Text SfatEcran", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Setări hyperlink", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Câmp obligatoriu", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Câmpul trebuie să conțină adresa URL in format \"http://www.example.com\" ", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Acest câmp poate conține maxim 2083 caractere", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Introduceți adresa web sau selectați fișier", "SSE.Views.ImageSettings.strTransparency": "Transparență", "SSE.Views.ImageSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCrop": "Trunchiere", "SSE.Views.ImageSettings.textCropFill": "Umplere", "SSE.Views.ImageSettings.textCropFit": "Potrivire", "SSE.Views.ImageSettings.textCropToShape": "Trunchiere la formă", "SSE.Views.ImageSettings.textEdit": "Editare", "SSE.Views.ImageSettings.textEditObject": "Editare obiect", "SSE.Views.ImageSettings.textFlip": "R<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textFromFile": "<PERSON>", "SSE.Views.ImageSettings.textFromStorage": "Din serviciul stocare", "SSE.Views.ImageSettings.textFromUrl": "<PERSON><PERSON> URL-ul", "SSE.Views.ImageSettings.textHeight": "Înălțime", "SSE.Views.ImageSettings.textHint270": "Rotire 90° în sens contrar acelor de ceasornic", "SSE.Views.ImageSettings.textHint90": "Rotire 90° în sensul acelor de ceasornic", "SSE.Views.ImageSettings.textHintFlipH": "Răsturnare orizontală", "SSE.Views.ImageSettings.textHintFlipV": "Răsturnare verticală", "SSE.Views.ImageSettings.textInsert": "<PERSON><PERSON><PERSON><PERSON><PERSON> imagine", "SSE.Views.ImageSettings.textKeepRatio": "Dimensi<PERSON> constante", "SSE.Views.ImageSettings.textOriginalSize": "Dimensiunea reală", "SSE.Views.ImageSettings.textRecentlyUsed": "Utilizate recent", "SSE.Views.ImageSettings.textResetCrop": "Resetare trunchiere", "SSE.Views.ImageSettings.textRotate90": "Rotire 90°", "SSE.Views.ImageSettings.textRotation": "Rotație", "SSE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textWidth": "Lățime", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Fără mutare sau dimensionare cu celule", "SSE.Views.ImageSettingsAdvanced.textAlt": "Text alternativ", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Des<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Furnizarea textului alternativ pentru conținut vizual destinat persoanelor cu deficiențe de vedere și cognitive pentru a le ajuta să înțăleagă mai bine conținutul unei imagini, forme, diagramei sau tabele.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Titlu", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Răsturnat", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Orizontal", "SSE.Views.ImageSettingsAdvanced.textOneCell": "<PERSON><PERSON><PERSON> făr<PERSON>are cu celulele", "SSE.Views.ImageSettingsAdvanced.textRotation": "Rotație", "SSE.Views.ImageSettingsAdvanced.textSnap": "<PERSON><PERSON><PERSON> c<PERSON>i", "SSE.Views.ImageSettingsAdvanced.textTitle": "Imagine - <PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Mutare și dimensionare cu celulele", "SSE.Views.ImageSettingsAdvanced.textVertically": "Vertical", "SSE.Views.ImportFromXmlDialog.textDestination": "Alegeți o locație pentru datele", "SSE.Views.ImportFromXmlDialog.textExist": "Foaie de calcul existentă", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Zona de celule nu este validă", "SSE.Views.ImportFromXmlDialog.textNew": "Foaie de calcul nouă", "SSE.Views.ImportFromXmlDialog.textSelectData": "Selectare date", "SSE.Views.ImportFromXmlDialog.textTitle": "Import date", "SSE.Views.ImportFromXmlDialog.txtEmpty": "Câmp obligatoriu", "SSE.Views.LeftMenu.ariaLeftMenu": "Meniul din stânga", "SSE.Views.LeftMenu.tipAbout": "Informații", "SSE.Views.LeftMenu.tipChat": "Cha<PERSON>", "SSE.Views.LeftMenu.tipComments": "Comenta<PERSON><PERSON>", "SSE.Views.LeftMenu.tipFile": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "<PERSON>lug<PERSON>-<PERSON><PERSON>", "SSE.Views.LeftMenu.tipSearch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSpellcheck": "Verificarea ortografică", "SSE.Views.LeftMenu.tipSupport": "Feedback și asistența", "SSE.Views.LeftMenu.txtDeveloper": "MOD DEZVOLTATOR", "SSE.Views.LeftMenu.txtEditor": "Editor de foi de calcul", "SSE.Views.LeftMenu.txtLimit": "Limitare acces", "SSE.Views.LeftMenu.txtTrial": "MODUL DE ÎNCERCARE", "SSE.Views.LeftMenu.txtTrialDev": "Mod dezvoltator de încercare", "SSE.Views.MacroDialog.textMacro": "Nume macro", "SSE.Views.MacroDialog.textTitle": "Asocierea <PERSON>ii", "SSE.Views.MainSettingsPrint.okButtonText": "Salvează", "SSE.Views.MainSettingsPrint.strBottom": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLandscape": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLeft": "Stânga", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPrint": "Imp<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPrintTitles": "<PERSON><PERSON><PERSON><PERSON> titluri", "SSE.Views.MainSettingsPrint.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strTop": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textActualSize": "Dimensiunea reală", "SSE.Views.MainSettingsPrint.textCustom": "Particularizat", "SSE.Views.MainSettingsPrint.textCustomOptions": "Opțiuni particularizate", "SSE.Views.MainSettingsPrint.textFitCols": "Se potrivesc toate coloanele pe o singură pagină", "SSE.Views.MainSettingsPrint.textFitPage": "Se potrivește foaia pe o singură pagină", "SSE.Views.MainSettingsPrint.textFitRows": "Se potrivesc toate rândurile pe o pagină", "SSE.Views.MainSettingsPrint.textPageOrientation": "Orientare pagină", "SSE.Views.MainSettingsPrint.textPageScaling": "Scalare", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON><PERSON><PERSON><PERSON> pagină", "SSE.Views.MainSettingsPrint.textPrintGrid": "Imprimare linii de grilă", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Imprimare titluri rânduri și coloane", "SSE.Views.MainSettingsPrint.textRepeat": "Repetare...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Coloane de repetat la stânga", "SSE.Views.MainSettingsPrint.textRepeatTop": "Rânduri de repetat la început", "SSE.Views.MainSettingsPrint.textSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Zone denumite existente nu pot fi editate, dar nici cele noi nu pot fi create<br>de<PERSON><PERSON>e unele dintre acestea sunt editate în momentul de față.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Numele definit", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Avertisment", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Registru de calcul", "SSE.Views.NamedRangeEditDlg.textDataRange": "Zonă de date", "SSE.Views.NamedRangeEditDlg.textExistName": "EROARE! Numele de zonă există deja", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Numele trebuie să înceapă cu o litera sau cu un caracter de subliniere și nu poate conține caractere nevalide.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "EROARE! Zonă de celule nu este validă", "SSE.Views.NamedRangeEditDlg.textIsLocked": "EROARE! Acest element este editat de către un alt utilizator.", "SSE.Views.NamedRangeEditDlg.textName": "Nume", "SSE.Views.NamedRangeEditDlg.textReservedName": "Numele utilizat a fost deja definit anterior într-o referință de formulă. Introduceți un alt nume.", "SSE.Views.NamedRangeEditDlg.textScope": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textSelectData": "Selectare date", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Câmp obligatoriu", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Editare nume", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Nume nou", "SSE.Views.NamedRangePasteDlg.textNames": "Zonele denumite", "SSE.Views.NamedRangePasteDlg.txtTitle": "Lipire nume", "SSE.Views.NameManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.guestText": "Invitat", "SSE.Views.NameManagerDlg.lockText": "Blocat", "SSE.Views.NameManagerDlg.textDataRange": "Zonă de date", "SSE.Views.NameManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEdit": "Editare", "SSE.Views.NameManagerDlg.textEmpty": "<PERSON><PERSON> zonă denumită nu a fost creată până când.<br><PERSON><PERSON><PERSON><PERSON> cel puțin o zonă denumită și va apărea aici în acest câmp.", "SSE.Views.NameManagerDlg.textFilter": "Filtrare", "SSE.Views.NameManagerDlg.textFilterAll": "Toate", "SSE.Views.NameManagerDlg.textFilterDefNames": "Numele definite", "SSE.Views.NameManagerDlg.textFilterSheet": "Nume definite în foaia de calcul", "SSE.Views.NameManagerDlg.textFilterTableNames": "Numele de tabel", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Nume definite în registrul de calcul", "SSE.Views.NameManagerDlg.textNew": "Nou", "SSE.Views.NameManagerDlg.textnoNames": "<PERSON><PERSON><PERSON><PERSON> axe", "SSE.Views.NameManagerDlg.textRanges": "Zonele denumite", "SSE.Views.NameManagerDlg.textScope": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textWorkbook": "Registru de calcul", "SSE.Views.NameManagerDlg.tipIsLocked": "Acest element este editat de către un alt utilizator.", "SSE.Views.NameManagerDlg.txtTitle": "Manager nume", "SSE.Views.NameManagerDlg.warnDelete": "Sunteți sigur că doriți să ștergeți numele {0}?", "SSE.Views.PageMarginsDialog.textBottom": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textCenter": "<PERSON><PERSON><PERSON><PERSON> paginii", "SSE.Views.PageMarginsDialog.textHor": "Orizontal", "SSE.Views.PageMarginsDialog.textLeft": "Stânga", "SSE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textVert": "Vertical", "SSE.Views.PageMarginsDialog.textWarning": "Avertisment", "SSE.Views.PageMarginsDialog.warnCheckMargings": "<PERSON>gini incorecte", "SSE.Views.ParagraphSettings.strLineHeight": "Interlinie", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Spațiere paragraf", "SSE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingBefore": "Înainte", "SSE.Views.ParagraphSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAt": "La", "SSE.Views.ParagraphSettings.textAtLeast": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAuto": "Multiplu", "SSE.Views.ParagraphSettings.textExact": "exact", "SSE.Views.ParagraphSettings.txtAutoText": "Auto", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Tabulatorii specificați vor apărea în acest câmp", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Cu majuscule", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Tăiere cu două linii", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Stânga", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Interlinie", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Înainte", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Special", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Font", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Indentări și spațiere", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON> reduse", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Spațiere", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Tăiere cu o linie", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Indice", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Exponent", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Tabulatori", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Aliniere", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Multiplu", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Spațier<PERSON> caracterelor", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Tabulator implicit", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Efecte", "SSE.Views.ParagraphSettingsAdvanced.textExact": "exact", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Primul rând", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Agățat", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "<PERSON><PERSON>t stânga-dreapta", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(niciunul)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON> <PERSON><PERSON><PERSON> toate", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Specificare", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "La centru", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Stânga", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Poziționare tabulator", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Paragraf - <PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Editare", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Formula", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Nume de elemente", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "Nou", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Elemente calculate în", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "este egal cu", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "nu se termină cu", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "nu conține", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "între", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "not between", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "nu este egal cu", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "este mai mare decât", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "este mai mare sau egal cu", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "este mai mic decât", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "este mai mic sau egal cu", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "începe cu", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "nu începe cu", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "se termină cu", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Afișare elemente cu etichetă:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Afișare elemente cu:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Utilizați ? pentru a reprezenta orice caracter unic", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Utilizați * pentru a reprezenta o serie de caractere", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "și", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Filtru de valoare", "SSE.Views.PivotGroupDialog.textAuto": "Auto", "SSE.Views.PivotGroupDialog.textBy": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textDays": "Zile", "SSE.Views.PivotGroupDialog.textEnd": "Se încheie la", "SSE.Views.PivotGroupDialog.textError": "Acest câmp este o valoare numerică", "SSE.Views.PivotGroupDialog.textGreaterError": "Numărul la sfârșit nu poate fi mai mic decât numărul la început ", "SSE.Views.PivotGroupDialog.textHour": "Oră", "SSE.Views.PivotGroupDialog.textMin": "Minute", "SSE.Views.PivotGroupDialog.textMonth": "Lună", "SSE.Views.PivotGroupDialog.textNumDays": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textQuart": "Trimestre", "SSE.Views.PivotGroupDialog.textSec": "Secunde", "SSE.Views.PivotGroupDialog.textStart": "Incepere de la", "SSE.Views.PivotGroupDialog.textYear": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.txtTitle": "Grupare", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "Puteți folosi Elemente calculate pentru calcule de bază cu diferite elemente din același câmp.", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Inserare în formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "Element", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Nume de element", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Elemente", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Citește mai departe", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Inserare element calculat în", "SSE.Views.PivotSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textFields": "Selectați câmpurile", "SSE.Views.PivotSettings.textFilters": "Filtre", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "Valori", "SSE.Views.PivotSettings.txtAddColumn": "Se adaugă în coloane", "SSE.Views.PivotSettings.txtAddFilter": "Se adaugă la filtre", "SSE.Views.PivotSettings.txtAddRow": "Se adaugă la rânduri", "SSE.Views.PivotSettings.txtAddValues": "Se adaugă la valori", "SSE.Views.PivotSettings.txtFieldSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtMoveBegin": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtMoveColumn": "Se mută la etichetele de coloană", "SSE.Views.PivotSettings.txtMoveDown": "<PERSON><PERSON><PERSON>n jos", "SSE.Views.PivotSettings.txtMoveEnd": "<PERSON><PERSON><PERSON>â<PERSON>", "SSE.Views.PivotSettings.txtMoveFilter": "Se mută la filtrul de raport", "SSE.Views.PivotSettings.txtMoveRow": "Se mută la etichetele de rând", "SSE.Views.PivotSettings.txtMoveUp": "<PERSON><PERSON><PERSON> în sus", "SSE.Views.PivotSettings.txtMoveValues": "Se mută la valori", "SSE.Views.PivotSettings.txtRemove": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.strLayout": "Nume și aspect", "SSE.Views.PivotSettingsAdvanced.textAlt": "Text alternativ", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Des<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Furnizarea textului alternativ pentru conținut vizual destinat persoanelor cu deficiențe de vedere și cognitive pentru a le ajuta să înțăleagă mai bine conținutul unei imagini, forme, diagramei sau tabele.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Titlu", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Potrivire automată a lățimii coloanelor la actualizare", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Zonă de date", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Sursa de date", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Afișare câmpuri în zona de filtru raport", "SSE.Views.PivotSettingsAdvanced.textDown": "În jos, apoi lateral", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Totalurile generale", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Ante<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "EROARE! Zonă de celule nu este validă", "SSE.Views.PivotSettingsAdvanced.textOver": "Lateral, apoi în jos", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Selectare date", "SSE.Views.PivotSettingsAdvanced.textShowCols": "<PERSON><PERSON><PERSON><PERSON> pentru coloane", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Se afișează anteturile de câmp în rânduri și coloane", "SSE.Views.PivotSettingsAdvanced.textShowRows": "<PERSON><PERSON><PERSON><PERSON> pentru rându<PERSON>", "SSE.Views.PivotSettingsAdvanced.textTitle": "Tabelă Pivot - <PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Câmpuri de filtru raport pe coloană", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Câmpuri de filtru raport pe rânduri", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Câmp obligatoriu", "SSE.Views.PivotSettingsAdvanced.txtName": "Nume", "SSE.Views.PivotShowDetailDialog.textDescription": "Alegeți câmpul care conține detaliile pe care doriți să le afișați:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Afișare <PERSON>talii", "SSE.Views.PivotTable.capBlankRows": "Rândurile necompletate", "SSE.Views.PivotTable.capGrandTotals": "Totalurile generale", "SSE.Views.PivotTable.capLayout": "Aspect raport", "SSE.Views.PivotTable.capSubtotals": "Subtotaluri", "SSE.Views.PivotTable.mniBottomSubtotals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> toate subtotalurile în partea de jos a grupului", "SSE.Views.PivotTable.mniInsertBlankLine": "Se inserează o linie necompletată după fiecare etichetă de element", "SSE.Views.PivotTable.mniLayoutCompact": "Afișare în formă compactă", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Nu se repetă etichete de element", "SSE.Views.PivotTable.mniLayoutOutline": "Afișare sub formă de schiță", "SSE.Views.PivotTable.mniLayoutRepeat": "Repetare etichete element", "SSE.Views.PivotTable.mniLayoutTabular": "Afișare sub formă de tabel", "SSE.Views.PivotTable.mniNoSubtotals": "Nu se afișează subtotaluri", "SSE.Views.PivotTable.mniOffTotals": "Dezactivat pentru rânduri și coloane", "SSE.Views.PivotTable.mniOnColumnsTotals": "Activat numai pentru coloane", "SSE.Views.PivotTable.mniOnRowsTotals": "Activat numai pentru r<PERSON><PERSON>", "SSE.Views.PivotTable.mniOnTotals": "Activat pentru rânduri și coloane", "SSE.Views.PivotTable.mniRemoveBlankLine": "Se elimină linia necompletată de după fiecare etichetă de element.", "SSE.Views.PivotTable.mniTopSubtotals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> toate subtotalurile în partea de sus a grupului", "SSE.Views.PivotTable.textColBanded": "Coloane alternante", "SSE.Views.PivotTable.textColHeader": "An<PERSON><PERSON><PERSON> co<PERSON>", "SSE.Views.PivotTable.textRowBanded": "Rânduri alternante", "SSE.Views.PivotTable.textRowHeader": "<PERSON><PERSON><PERSON><PERSON> rând", "SSE.Views.PivotTable.tipCalculatedItems": "Elemente calculate", "SSE.Views.PivotTable.tipCreatePivot": "Inserare tabel Pivot", "SSE.Views.PivotTable.tipGrandTotals": "Afișare sau ascundere totaluri generale", "SSE.Views.PivotTable.tipRefresh": "Actualizarea sursei de informație", "SSE.Views.PivotTable.tipRefreshCurrent": "Actualizarea informațiilor din sursa de date pentru tabelul curent", "SSE.Views.PivotTable.tipSelect": "Selectați tabel Pivot întreg", "SSE.Views.PivotTable.tipSubtotals": "Afișare sau ascundere subtotaluri", "SSE.Views.PivotTable.txtCalculatedItems": "Elemente calculate", "SSE.Views.PivotTable.txtCollapseEntire": "Restrânge câmpul întreg", "SSE.Views.PivotTable.txtCreate": "Inserare tabel", "SSE.Views.PivotTable.txtExpandEntire": "Extinde câmpul întreg", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Particularizat", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Întunecat", "SSE.Views.PivotTable.txtGroupPivot_Light": "Luminos", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Me<PERSON>u", "SSE.Views.PivotTable.txtPivotTable": "Tabelă Pivot", "SSE.Views.PivotTable.txtRefresh": "Actualizare", "SSE.Views.PivotTable.txtRefreshAll": "Reîmprospătare totală", "SSE.Views.PivotTable.txtSelect": "Selectare", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Stil întunecat tabel pivot", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Stil luminos tabel pivot", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Stil mediu tabel pivot", "SSE.Views.PrintSettings.btnDownload": "Salvare și descărcare", "SSE.Views.PrintSettings.btnExport": "Salvare și Export", "SSE.Views.PrintSettings.btnPrint": "Salvare și imprimare", "SSE.Views.PrintSettings.strBottom": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strLandscape": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strLeft": "Stânga", "SSE.Views.PrintSettings.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strPrint": "Imp<PERSON><PERSON>", "SSE.Views.PrintSettings.strPrintTitles": "<PERSON><PERSON><PERSON><PERSON> titluri", "SSE.Views.PrintSettings.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strShow": "<PERSON><PERSON>ș<PERSON><PERSON>", "SSE.Views.PrintSettings.strTop": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textActiveSheets": "Foi active", "SSE.Views.PrintSettings.textActualSize": "Dimensiunea reală", "SSE.Views.PrintSettings.textAllSheets": "Toate foile", "SSE.Views.PrintSettings.textCurrentSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textCustom": "Particularizat", "SSE.Views.PrintSettings.textCustomOptions": "Opțiuni particularizate", "SSE.Views.PrintSettings.textFitCols": "Se potrivesc toate coloanele pe o singură pagină", "SSE.Views.PrintSettings.textFitPage": "Se potrivește foaia pe o singură pagină", "SSE.Views.PrintSettings.textFitRows": "Se potrivesc toate rândurile pe o pagină", "SSE.Views.PrintSettings.textHideDetails": "Ascundere de<PERSON>ii", "SSE.Views.PrintSettings.textIgnore": "Ignorare zonă de imprimare", "SSE.Views.PrintSettings.textLayout": "Aspect", "SSE.Views.PrintSettings.textMarginsNarrow": "Îngust", "SSE.Views.PrintSettings.textMarginsNormal": "Normal", "SSE.Views.PrintSettings.textMarginsWide": "Lat", "SSE.Views.PrintSettings.textPageOrientation": "Orientare pagină", "SSE.Views.PrintSettings.textPages": "Pagini:", "SSE.Views.PrintSettings.textPageScaling": "Scalare", "SSE.Views.PrintSettings.textPageSize": "<PERSON><PERSON><PERSON><PERSON> pagină", "SSE.Views.PrintSettings.textPrintGrid": "Imprimare linii de grilă", "SSE.Views.PrintSettings.textPrintHeadings": "Imprimare titluri rânduri și coloane", "SSE.Views.PrintSettings.textPrintRange": "Interval de imprimare", "SSE.Views.PrintSettings.textRange": "Zona", "SSE.Views.PrintSettings.textRepeat": "Repetare...", "SSE.Views.PrintSettings.textRepeatLeft": "Coloane de repetat la stânga", "SSE.Views.PrintSettings.textRepeatTop": "Rânduri de repetat la început", "SSE.Views.PrintSettings.textSelection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "<PERSON><PERSON><PERSON> foa<PERSON>", "SSE.Views.PrintSettings.textShowDetails": "Afișare <PERSON>talii", "SSE.Views.PrintSettings.textShowGrid": "Afișare linii de grilă", "SSE.Views.PrintSettings.textShowHeadings": "Afișare anteturi de rânduri și coloane", "SSE.Views.PrintSettings.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textTitlePDF": "Setări PDF", "SSE.Views.PrintSettings.textTo": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.txtMarginsLast": "Ultima setare particularizată", "SSE.Views.PrintTitlesDialog.textFirstCol": "Prima coloană", "SSE.Views.PrintTitlesDialog.textFirstRow": "Primul rând", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Coloanele îng<PERSON>ț<PERSON>", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Rândurile înghețate", "SSE.Views.PrintTitlesDialog.textInvalidRange": "EROARE! Zonă de celule nu este validă", "SSE.Views.PrintTitlesDialog.textLeft": "Coloane de repetat la stânga", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Nu se repetă", "SSE.Views.PrintTitlesDialog.textRepeat": "Repetare...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Selectați zonă", "SSE.Views.PrintTitlesDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> titluri", "SSE.Views.PrintTitlesDialog.textTop": "Rânduri de repetat la început", "SSE.Views.PrintWithPreview.txtActiveSheets": "Foi active", "SSE.Views.PrintWithPreview.txtActualSize": "Dimensiunea reală", "SSE.Views.PrintWithPreview.txtAllSheets": "Toate foile", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Se aplică la toate foile", "SSE.Views.PrintWithPreview.txtBothSides": "Imprimare pe ambele p<PERSON>i", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "<PERSON><PERSON><PERSON> se <PERSON>nto<PERSON> pe latura lungă", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "<PERSON><PERSON><PERSON> se întorc pe latura scurtă", "SSE.Views.PrintWithPreview.txtBottom": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCopies": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCurrentSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCustom": "Particularizat", "SSE.Views.PrintWithPreview.txtCustomOptions": "Opțiuni particularizate", "SSE.Views.PrintWithPreview.txtEmptyTable": "Nu s-a gă<PERSON>t nimic de tipărit deoarece tabelul este necompletat", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "Numărul primei pagini:", "SSE.Views.PrintWithPreview.txtFitCols": "Se potrivesc toate coloanele pe o singură pagină", "SSE.Views.PrintWithPreview.txtFitPage": "Se potrivește foaia pe o singură pagină", "SSE.Views.PrintWithPreview.txtFitRows": "Se potrivesc toate rândurile pe o pagină", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Linii de grilă și titluri", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Set<PERSON>ri antet/subsol", "SSE.Views.PrintWithPreview.txtIgnore": "Ignorare zonă de imprimare", "SSE.Views.PrintWithPreview.txtLandscape": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtLeft": "Stânga", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMarginsLast": "Ultima setare particularizată", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "Îngust", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Normal", "SSE.Views.PrintWithPreview.txtMarginsWide": "Lat", "SSE.Views.PrintWithPreview.txtOf": "din {0}", "SSE.Views.PrintWithPreview.txtOneSide": "Imprimare pe o parte", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Imprimare pe o singură parte a paginii", "SSE.Views.PrintWithPreview.txtPage": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "<PERSON><PERSON><PERSON><PERSON> de pagină nevalid", "SSE.Views.PrintWithPreview.txtPageOrientation": "Orientare pagină", "SSE.Views.PrintWithPreview.txtPages": "Pagini:", "SSE.Views.PrintWithPreview.txtPageSize": "<PERSON><PERSON><PERSON><PERSON> pagină", "SSE.Views.PrintWithPreview.txtPortrait": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPrint": "Imp<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPrintGrid": "Imprimare linii de grilă", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Imprimare titluri rânduri și coloane", "SSE.Views.PrintWithPreview.txtPrintRange": "Interval de imprimare", "SSE.Views.PrintWithPreview.txtPrintSides": "<PERSON><PERSON><PERSON><PERSON><PERSON> de imprimat", "SSE.Views.PrintWithPreview.txtPrintTitles": "<PERSON><PERSON><PERSON><PERSON> titluri", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Imprimare în PDF", "SSE.Views.PrintWithPreview.txtRepeat": "Repetare...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Coloane de repetat la stânga", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Rânduri de repetat la început", "SSE.Views.PrintWithPreview.txtRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSave": "Salvează", "SSE.Views.PrintWithPreview.txtScaling": "Scalare", "SSE.Views.PrintWithPreview.txtSelection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Paramat<PERSON>i foii", "SSE.Views.PrintWithPreview.txtSheet": "Foaia: {0}", "SSE.Views.PrintWithPreview.txtTo": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtTop": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.textExistName": "EROARE! Titlul de zonă există deja", "SSE.Views.ProtectDialog.textInvalidName": "Titlul zonei trebuie să înceapă cu o literă și poate conține numai litere, cifre și spații.", "SSE.Views.ProtectDialog.textInvalidRange": "EROARE! Zonă de celule nu este validă", "SSE.Views.ProtectDialog.textSelectData": "Selectare date", "SSE.Views.ProtectDialog.txtAllow": "Se permite tuturor utilizatorilor foii de calcul", "SSE.Views.ProtectDialog.txtAllowDescription": "Puteți debloca anumite zonele pentru a activa editarea.", "SSE.Views.ProtectDialog.txtAllowRanges": "Se permite modificarea zonelor", "SSE.Views.ProtectDialog.txtAutofilter": "U<PERSON><PERSON><PERSON>ltră<PERSON> automate", "SSE.Views.ProtectDialog.txtDelCols": "<PERSON><PERSON><PERSON><PERSON> colo<PERSON>", "SSE.Views.ProtectDialog.txtDelRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtEmpty": "Câmp obligatoriu", "SSE.Views.ProtectDialog.txtFormatCells": "Formatare celule", "SSE.Views.ProtectDialog.txtFormatCols": "Formatare coloane", "SSE.Views.ProtectDialog.txtFormatRows": "Formatare rânduri", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Parola și Confirmare parola nu este indentice", "SSE.Views.ProtectDialog.txtInsCols": "Inserare coloane", "SSE.Views.ProtectDialog.txtInsHyper": "Inserare hyperlink", "SSE.Views.ProtectDialog.txtInsRows": "Inserare rânduri", "SSE.Views.ProtectDialog.txtObjs": "Editare obiecte", "SSE.Views.ProtectDialog.txtOptional": "opțional", "SSE.Views.ProtectDialog.txtPassword": "Pa<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPivot": "Utilizarea PivotTable și PivotChart", "SSE.Views.ProtectDialog.txtProtect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRange": "Zona", "SSE.Views.ProtectDialog.txtRangeName": "Titlu", "SSE.Views.ProtectDialog.txtRepeat": "Reintroduceți parola", "SSE.Views.ProtectDialog.txtScen": "Editare scenarii", "SSE.Views.ProtectDialog.txtSelLocked": "Select<PERSON>z<PERSON> celulele blocate", "SSE.Views.ProtectDialog.txtSelUnLocked": "Select<PERSON>z<PERSON> celulele deblocate", "SSE.Views.ProtectDialog.txtSheetDescription": "Împiedicați modificările nedorite prin limitarea permisiunilor pentru editare.", "SSE.Views.ProtectDialog.txtSheetTitle": "<PERSON><PERSON><PERSON><PERSON> foaie", "SSE.Views.ProtectDialog.txtSort": "Sortare", "SSE.Views.ProtectDialog.txtWarning": "Atenție: <PERSON><PERSON><PERSON> pierdeți sau uitați parola, ea nu poate fi recuperată. Să o păstrați într-un loc sigur.", "SSE.Views.ProtectDialog.txtWBDescription": "Puteți proteja structura registrului de calcul prin parolă pentru împiedicarea utilizatorilor de a vizualiza registrele de calcul mascate, de a adăuga, deplasa, șterge, masca și redenumi registrele de calcul. ", "SSE.Views.ProtectDialog.txtWBTitle": "Protecție structură registru de lucru", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Orice utilizator", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Editare", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Refuzat", "SSE.Views.ProtectedRangesEditDlg.textCanView": "Vizualizare", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "Titlul zonei trebuie să înceapă cu o literă și poate conține numai litere, cifre și spații.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "EROARE! Zonă de celule nu este validă", "SSE.Views.ProtectedRangesEditDlg.textRemove": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "Selectare date", "SSE.Views.ProtectedRangesEditDlg.textYou": "dvs.", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Acces la zonă", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "Câmp obligatoriu", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.txtRange": "Zona", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "Titlu", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Doar dvs. puteți modifica acest interval", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Începeți să tastați un nume sau o adresă de poştă electronică", "SSE.Views.ProtectedRangesManagerDlg.guestText": "Invitat", "SSE.Views.ProtectedRangesManagerDlg.lockText": "Blocat", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "Editare", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "<PERSON><PERSON> zon<PERSON> protejatăt.<br> Trebuie să creați cel puțin o zonă protejată și aceasta va apărea în acest câmp.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "Filtrare", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "Toate", "SSE.Views.ProtectedRangesManagerDlg.textNew": "Nouă", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Protejarea foii de calcul", "SSE.Views.ProtectedRangesManagerDlg.textRange": "Zona", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "Puteți restricționa modificarea zonelor de către persoanele selectate.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "Titlu", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "Acest element este editat de către un alt utilizator.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Acces", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Refuzat", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "Editare", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "Modificare zonă", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "Zonă nou<PERSON>", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Zone protejate", "SSE.Views.ProtectedRangesManagerDlg.txtView": "Vizualizare", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Sunteți sigur că doriți să ștergeți zona protejată?{0}?<br>Orice utilizator cu permisiunea de modificare a foii de calcul va putea edita conținutul din această zonă. ", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Sunteți sigur că doriți să ștergeți zone protejate?{0}?<br>Orice utilizator cu permisiunea de modificare a foii de calcul va putea edita conținutul din aceste zone. ", "SSE.Views.ProtectRangesDlg.guestText": "Invitat", "SSE.Views.ProtectRangesDlg.lockText": "Blocat", "SSE.Views.ProtectRangesDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEdit": "Editare", "SSE.Views.ProtectRangesDlg.textEmpty": "Zonele permise pentru editare nu sunt", "SSE.Views.ProtectRangesDlg.textNew": "Nou", "SSE.Views.ProtectRangesDlg.textProtect": "<PERSON><PERSON><PERSON><PERSON> foaie", "SSE.Views.ProtectRangesDlg.textPwd": "Pa<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRange": "Zona", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Zonele deblocate prin parolă când foaia de calcul este protejată (se aplică numai celulelor blocate)", "SSE.Views.ProtectRangesDlg.textTitle": "Titlu", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Acest element este editat de către un alt utilizator.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Modificare zonă", "SSE.Views.ProtectRangesDlg.txtNewRange": "Zonă nou<PERSON>", "SSE.Views.ProtectRangesDlg.txtNo": "<PERSON>u", "SSE.Views.ProtectRangesDlg.txtTitle": "Se permite utilizatorilor să editeze zonele", "SSE.Views.ProtectRangesDlg.txtYes": "Da", "SSE.Views.ProtectRangesDlg.warnDelete": "Sunteți sigur că doriți să ștergeți numele {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Pentru a șterge valorile dublate, selectați una sau mai multe coloane care conțin dubluri.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Tabelul meu are anteturi", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Selectare totală", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.ariaRightMenu": "Meniul din dreapta", "SSE.Views.RightMenu.txtCellSettings": "<PERSON><PERSON><PERSON> cel<PERSON>", "SSE.Views.RightMenu.txtChartSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtImageSettings": "<PERSON><PERSON><PERSON> imagine", "SSE.Views.RightMenu.txtParagraphSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtPivotSettings": "<PERSON><PERSON><PERSON> ta<PERSON>", "SSE.Views.RightMenu.txtSettings": "Setări comune", "SSE.Views.RightMenu.txtShapeSettings": "<PERSON><PERSON><PERSON> forma", "SSE.Views.RightMenu.txtSignatureSettings": "<PERSON><PERSON><PERSON> se<PERSON>", "SSE.Views.RightMenu.txtSlicerSettings": "<PERSON><PERSON><PERSON> slicer", "SSE.Views.RightMenu.txtSparklineSettings": "Set<PERSON><PERSON> diagrame sparkline", "SSE.Views.RightMenu.txtTableSettings": "<PERSON><PERSON><PERSON> ta<PERSON>", "SSE.Views.RightMenu.txtTextArtSettings": "Setări TextArt", "SSE.Views.ScaleDialog.textAuto": "Auto", "SSE.Views.ScaleDialog.textError": "Valoarea introdusă nu este corectă.", "SSE.Views.ScaleDialog.textFewPages": "pagini", "SSE.Views.ScaleDialog.textFitTo": "Potrivire la", "SSE.Views.ScaleDialog.textHeight": "Înălțime", "SSE.Views.ScaleDialog.textManyPages": "pagini", "SSE.Views.ScaleDialog.textOnePage": "pagina", "SSE.Views.ScaleDialog.textScaleTo": "Scalare la", "SSE.Views.ScaleDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textWidth": "Lățime", "SSE.Views.SetValueDialog.txtMaxText": "Dimensiunea maximă a valorii de câmp {0}", "SSE.Views.SetValueDialog.txtMinText": "Dimensiunea minimă a valorii de câmp {0}", "SSE.Views.ShapeSettings.strBackground": "Culoare de fundal", "SSE.Views.ShapeSettings.strChange": "Modificare formă", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "Umplere", "SSE.Views.ShapeSettings.strForeground": "Culoarea de prim plan", "SSE.Views.ShapeSettings.strPattern": "Model", "SSE.Views.ShapeSettings.strShadow": "<PERSON><PERSON>șare umbră", "SSE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "Transparență", "SSE.Views.ShapeSettings.strType": "Tip", "SSE.Views.ShapeSettings.textAdjustShadow": "Ajustare umbră", "SSE.Views.ShapeSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "Valoarea introdusă nu este corectă.<br>Selectați valoarea cuprinsă înte 0 pt și 1584 pt.", "SSE.Views.ShapeSettings.textColor": "Umplere cu culoare", "SSE.Views.ShapeSettings.textDirection": "Orientare", "SSE.Views.ShapeSettings.textEditPoints": "Editare puncte", "SSE.Views.ShapeSettings.textEditShape": "Modificare formă", "SSE.Views.ShapeSettings.textEmptyPattern": "Fără model", "SSE.Views.ShapeSettings.textEyedropper": "Pipetă", "SSE.Views.ShapeSettings.textFlip": "R<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromFile": "<PERSON>", "SSE.Views.ShapeSettings.textFromStorage": "Din serviciul stocare", "SSE.Views.ShapeSettings.textFromUrl": "<PERSON><PERSON> URL-ul", "SSE.Views.ShapeSettings.textGradient": "Stop gradient", "SSE.Views.ShapeSettings.textGradientFill": "Umplere gradient", "SSE.Views.ShapeSettings.textHint270": "Rotire 90° în sens contrar acelor de ceasornic", "SSE.Views.ShapeSettings.textHint90": "Rotire 90° în sensul acelor de ceasornic", "SSE.Views.ShapeSettings.textHintFlipH": "Răsturnare orizontală", "SSE.Views.ShapeSettings.textHintFlipV": "Răsturnare verticală", "SSE.Views.ShapeSettings.textImageTexture": "Imagine sau textură", "SSE.Views.ShapeSettings.textLinear": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textMoreColors": "Mai multe culori", "SSE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON><PERSON><PERSON> um<PERSON>", "SSE.Views.ShapeSettings.textNoShadow": "<PERSON><PERSON><PERSON><PERSON> umbră", "SSE.Views.ShapeSettings.textOriginalSize": "Dimensiunea inițială", "SSE.Views.ShapeSettings.textPatternFill": "Model", "SSE.Views.ShapeSettings.textPosition": "Poziție", "SSE.Views.ShapeSettings.textRadial": "Radială", "SSE.Views.ShapeSettings.textRecentlyUsed": "Utilizate recent", "SSE.Views.ShapeSettings.textRotate90": "Rotire 90°", "SSE.Views.ShapeSettings.textRotation": "Rotație", "SSE.Views.ShapeSettings.textSelectImage": "Selectați imaginea", "SSE.Views.ShapeSettings.textSelectTexture": "Selectare", "SSE.Views.ShapeSettings.textShadow": "Umbră", "SSE.Views.ShapeSettings.textStretch": "Întindere", "SSE.Views.ShapeSettings.textStyle": "Stil", "SSE.Views.ShapeSettings.textTexture": "Din textură", "SSE.Views.ShapeSettings.textTile": "Placă", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Adăugare stop gradient", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Eliminare stop gradient", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON><PERSON> reciclat<PERSON>", "SSE.Views.ShapeSettings.txtCanvas": "Pânză", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "Pânză întunecată", "SSE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGranite": "Granit", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtKnit": "Dril", "SSE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> linie", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "Lemn", "SSE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Spațierea în jurul textului", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Fără mutare sau dimensionare cu celule", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Text alternativ", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Des<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Furnizarea textului alternativ pentru conținut vizual destinat persoanelor cu deficiențe de vedere și cognitive pentru a le ajuta să înțăleagă mai bine conținutul unei imagini, forme, diagramei sau tabele.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Titlu", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Săgeți", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Potrivire automată", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Dimensiune inițială", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Stil început", "SSE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Dimensiunea finală", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Stil final", "SSE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Răsturnat", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Înălțime", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Orizontal", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Tip unire", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Dimensi<PERSON> constante", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Stânga", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Stil linie", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Relief", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "<PERSON><PERSON><PERSON> făr<PERSON>are cu celulele", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Se permite textului să depășească forma", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Redimensionare formă pentru a se portivi cu textul", "SSE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Rotație", "SSE.Views.ShapeSettingsAdvanced.textRound": "Rotund", "SSE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSnap": "<PERSON><PERSON><PERSON> c<PERSON>i", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Spațiere între coloane", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Pătrat", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Casetă text", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Forma - <PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTop": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Mutare și dimensionare cu celulele", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Vertical", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Stil linie și setări săgeată", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Lățime", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Avertisment", "SSE.Views.SignatureSettings.strDelete": "Eliminare semnătura", "SSE.Views.SignatureSettings.strDetails": "Detalii semnătura", "SSE.Views.SignatureSettings.strInvalid": "Semnăturile nu sunt valide", "SSE.Views.SignatureSettings.strRequested": "Semnă<PERSON>i solicitate", "SSE.Views.SignatureSettings.strSetup": "Configurare semnă<PERSON>", "SSE.Views.SignatureSettings.strSign": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSignature": "Semnătura", "SSE.Views.SignatureSettings.strSigner": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strValid": "Semnături valide", "SSE.Views.SignatureSettings.txtContinueEditing": "Editare în aceste condiții", "SSE.Views.SignatureSettings.txtEditWarning": "În rezultatul editării toate semnăturile din foaie de calcul vor fi șterse.<br>Sunteți sigur că doriți să continuați? ", "SSE.Views.SignatureSettings.txtRemoveWarning": "Doriți să eliminați aceasta semnătura?<br>Va fi imposibil să anulați acțiunea.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Foaia de calcul trebuie să fie semnată.", "SSE.Views.SignatureSettings.txtSigned": "Semnături valide au fost adăugate în foaia de calcul. Foaia de calcul este protejată împotriva editării.", "SSE.Views.SignatureSettings.txtSignedInvalid": "O parte din semnături electronice pe foaia de calcul nu sunt valide sau nu pot fi verificate. Foaia de calcul este protejată împotriva editării.", "SSE.Views.SlicerAddDialog.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerAddDialog.txtTitle": "Incerare slicere", "SSE.Views.SlicerSettings.strHideNoData": "Ascundere elemente fără date", "SSE.Views.SlicerSettings.strIndNoData": "Indicare vizuală a elementelor fără date", "SSE.Views.SlicerSettings.strShowDel": "Se afișează elementele șterse din sursa de date", "SSE.Views.SlicerSettings.strShowNoData": "Afișare elemente fără date la sfârșit", "SSE.Views.SlicerSettings.strSorting": "Sortare și filtrare", "SSE.Views.SlicerSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textAsc": "Ascendent", "SSE.Views.SlicerSettings.textAZ": "de la A la Z", "SSE.Views.SlicerSettings.textButtons": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textDesc": "Descendent", "SSE.Views.SlicerSettings.textHeight": "Înălțime", "SSE.Views.SlicerSettings.textHor": "Orizontală", "SSE.Views.SlicerSettings.textKeepRatio": "Dimensi<PERSON> constante", "SSE.Views.SlicerSettings.textLargeSmall": "de la cel mai mare la cel mai mic", "SSE.Views.SlicerSettings.textLock": "Dezactivarea redimensionare sau mutare", "SSE.Views.SlicerSettings.textNewOld": "de la cel mai vechi la cel mai nou ", "SSE.Views.SlicerSettings.textOldNew": "de la cel mai vechi la cel mai nou", "SSE.Views.SlicerSettings.textPosition": "Poziție", "SSE.Views.SlicerSettings.textSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textSmallLarge": "de la cel mai mic la cel mai mare", "SSE.Views.SlicerSettings.textStyle": "Stil", "SSE.Views.SlicerSettings.textVert": "Verticală", "SSE.Views.SlicerSettings.textWidth": "Lățime", "SSE.Views.SlicerSettings.textZA": "de la Z la A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Înălțime", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Ascundere elemente fără date", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Indicare vizuală a elementelor fără date", "SSE.Views.SlicerSettingsAdvanced.strReferences": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Se afișează elementele șterse din sursa de date", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "<PERSON><PERSON><PERSON><PERSON> antet", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Afișare elemente fără date la sfârșit", "SSE.Views.SlicerSettingsAdvanced.strSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Sortare și filtrare", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Stil", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Stil și dimensiune", "SSE.Views.SlicerSettingsAdvanced.strWidth": "Lățime", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Fără mutare sau dimensionare cu celule", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Text alternativ", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Des<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Furnizarea textului alternativ pentru conținut vizual destinat persoanelor cu deficiențe de vedere și cognitive pentru a le ajuta să înțăleagă mai bine conținutul unei imagini, forme, diagramei sau tabele.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Titlu", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Ascendent", "SSE.Views.SlicerSettingsAdvanced.textAZ": "de la A la Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Descendent", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Numele de utilizat în formula", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Antet", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Dimensi<PERSON> constante", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "de la cel mai mare la cel mai mic", "SSE.Views.SlicerSettingsAdvanced.textName": "Nume", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "de la cel mai vechi la cel mai nou ", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "de la cel mai vechi la cel mai nou", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "<PERSON><PERSON><PERSON> făr<PERSON>are cu celulele", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "de la cel mai mic la cel mai mare", "SSE.Views.SlicerSettingsAdvanced.textSnap": "<PERSON><PERSON><PERSON> c<PERSON>i", "SSE.Views.SlicerSettingsAdvanced.textSort": "Sortare", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "<PERSON>umele sursei", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Slicer - <PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Mutare și dimensionare cu celulele", "SSE.Views.SlicerSettingsAdvanced.textZA": "de la Z la A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Câmp obligatoriu", "SSE.Views.SortDialog.errorEmpty": "Selectați o coloană sau un rând pentru fiecare criteriu de sortare.", "SSE.Views.SortDialog.errorMoreOneCol": "A fost selectată mai mult decât o singură coloană.", "SSE.Views.SortDialog.errorMoreOneRow": "A fost selectat mai mult decât un singur rând.", "SSE.Views.SortDialog.errorNotOriginalCol": "Coloană pe care ați selectat-o nu se referă la zonă selectată inițial.", "SSE.Views.SortDialog.errorNotOriginalRow": "Rândul pe care l-ați selectat nu se referă la zonă selectată inițial.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 este sortat după culoarea de mai multe ori.<br>Eliminați criterii de sortare pentru dubluri și încercați din nou.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 este sortat după valoarea de mai multe ori.<br>Eliminați criterii de sortare pentru dubluri și încercați din nou.", "SSE.Views.SortDialog.textAsc": "Ascendent", "SSE.Views.SortDialog.textAuto": "Automat", "SSE.Views.SortDialog.textAZ": "de la A la Z", "SSE.Views.SortDialog.textBelow": "dedesubt", "SSE.Views.SortDialog.textBtnCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textBtnDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textBtnNew": "Nou", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON><PERSON> celula", "SSE.Views.SortDialog.textColumn": "Coloană", "SSE.Views.SortDialog.textDesc": "Descendent", "SSE.Views.SortDialog.textDown": "Mutare cu un nivel mai jos", "SSE.Views.SortDialog.textFontColor": "Culoare font", "SSE.Views.SortDialog.textLeft": "Stânga", "SSE.Views.SortDialog.textLevels": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textMoreCols": "(Mai multe coloane...)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON> multe rându<PERSON>...)", "SSE.Views.SortDialog.textNone": "Niciunul", "SSE.Views.SortDialog.textOptions": "Opțiuni", "SSE.Views.SortDialog.textOrder": "Ordonare", "SSE.Views.SortDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textRow": "Rând", "SSE.Views.SortDialog.textSort": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textSortBy": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textThenBy": "<PERSON><PERSON><PERSON> du<PERSON>", "SSE.Views.SortDialog.textTop": "<PERSON><PERSON>", "SSE.Views.SortDialog.textUp": "Mutare cu un nivel mai sus", "SSE.Views.SortDialog.textValues": "Valori", "SSE.Views.SortDialog.textZA": "de la Z la A", "SSE.Views.SortDialog.txtInvalidRange": "Zonă de celule nu este validă.", "SSE.Views.SortDialog.txtTitle": "Sortare", "SSE.Views.SortFilterDialog.textAsc": "În ordinea ascendentă (de la A la Z) după", "SSE.Views.SortFilterDialog.textDesc": "În ordine descendentă (de la Z la A) după", "SSE.Views.SortFilterDialog.textNoSort": "<PERSON><PERSON><PERSON><PERSON> sortare", "SSE.Views.SortFilterDialog.txtTitle": "Sortare", "SSE.Views.SortFilterDialog.txtTitleValue": "<PERSON><PERSON><PERSON> după valoare", "SSE.Views.SortOptionsDialog.textCase": "Sensibil la litere mari și mici", "SSE.Views.SortOptionsDialog.textHeaders": "Tabelul meu are anteturi", "SSE.Views.SortOptionsDialog.textLeftRight": "Sortare de la stânga la dreapta", "SSE.Views.SortOptionsDialog.textOrientation": "Orientare", "SSE.Views.SortOptionsDialog.textTitle": "Opțiuni sortare", "SSE.Views.SortOptionsDialog.textTopBottom": "Sortarea de sus în jos", "SSE.Views.SpecialPasteDialog.textAdd": "Adaugă", "SSE.Views.SpecialPasteDialog.textAll": "Toate", "SSE.Views.SpecialPasteDialog.textBlanks": "Ignorare celule libere", "SSE.Views.SpecialPasteDialog.textColWidth": "Lățimea coloană", "SSE.Views.SpecialPasteDialog.textComments": "Comenta<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textDiv": "Împărț<PERSON>", "SSE.Views.SpecialPasteDialog.textFFormat": "Formule și formatare", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formule și formate de număr", "SSE.Views.SpecialPasteDialog.textFormats": "Formate", "SSE.Views.SpecialPasteDialog.textFormulas": "Formule", "SSE.Views.SpecialPasteDialog.textFWidth": "Formule și lățimile de coloană", "SSE.Views.SpecialPasteDialog.textMult": "Înmulțire", "SSE.Views.SpecialPasteDialog.textNone": "Niciunul", "SSE.Views.SpecialPasteDialog.textOperation": "Operațiune", "SSE.Views.SpecialPasteDialog.textPaste": "Lipire", "SSE.Views.SpecialPasteDialog.textSub": "Scădere", "SSE.Views.SpecialPasteDialog.textTitle": "Lipire specială", "SSE.Views.SpecialPasteDialog.textTranspose": "Transpunere", "SSE.Views.SpecialPasteDialog.textValues": "Valori", "SSE.Views.SpecialPasteDialog.textVFormat": "Valori și formatare", "SSE.Views.SpecialPasteDialog.textVNFormat": "Formatare valori și numere", "SSE.Views.SpecialPasteDialog.textWBorders": "<PERSON><PERSON> <PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.Spellcheck.noSuggestions": "Fără sugestii de corectare ortografică", "SSE.Views.Spellcheck.textChange": "Modificare", "SSE.Views.Spellcheck.textChangeAll": "Modificare peste tot", "SSE.Views.Spellcheck.textIgnore": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textIgnoreAll": "Ignorare totală", "SSE.Views.Spellcheck.txtAddToDictionary": "Adăugare la dicționar", "SSE.Views.Spellcheck.txtClosePanel": "Închide corectare ortografică", "SSE.Views.Spellcheck.txtComplete": "Verificarea ortografică este terminată", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Limbă de dicționar", "SSE.Views.Spellcheck.txtNextTip": "Salt la cuvântul următor", "SSE.Views.Spellcheck.txtSpelling": "Ortografie", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(<PERSON><PERSON><PERSON>)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "Creează o copie", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(<PERSON><PERSON>rea unei foi de calcul nouă)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "<PERSON><PERSON><PERSON> foii", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Foaie de calcul", "SSE.Views.Statusbar.filteredRecordsText": "{0} din {1} înregistrări au fost filtrate", "SSE.Views.Statusbar.filteredText": "<PERSON><PERSON><PERSON> de filtrare", "SSE.Views.Statusbar.itemAverage": "Me<PERSON>", "SSE.Views.Statusbar.itemCount": "Contorizare", "SSE.Views.Statusbar.itemDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemHidden": "Ascuns", "SSE.Views.Statusbar.itemHide": "Ascunde", "SSE.Views.Statusbar.itemInsert": "Inserare", "SSE.Views.Statusbar.itemMaximum": "Maxim", "SSE.Views.Statusbar.itemMinimum": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemMoveOrCopy": "Mutare sau copiere", "SSE.Views.Statusbar.itemProtect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemRename": "Redenumire", "SSE.Views.Statusbar.itemStatus": "Statutul de salvare", "SSE.Views.Statusbar.itemSum": "Sumă", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON><PERSON> filă", "SSE.Views.Statusbar.itemUnProtect": "Anularea protecției", "SSE.Views.Statusbar.RenameDialog.errNameExists": "O foaie de calcul cu același nume există deja.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Numele foii nu poate conține următoarele caracterele: \\/*?[]: sau caracterul ' la începutul sau sfârșitul", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Nume foaie", "SSE.Views.Statusbar.selectAllSheets": "Selectare totală foi", "SSE.Views.Statusbar.sheetIndexText": "<PERSON><PERSON>ia de calcul {0} din {1}", "SSE.Views.Statusbar.textAverage": "Me<PERSON>", "SSE.Views.Statusbar.textCount": "Contorizare", "SSE.Views.Statusbar.textMax": "Max", "SSE.Views.Statusbar.textMin": "Min", "SSE.Views.Statusbar.textNewColor": "Mai multe culori", "SSE.Views.Statusbar.textNoColor": "<PERSON><PERSON><PERSON><PERSON> culoare", "SSE.Views.Statusbar.textSum": "Sumă", "SSE.Views.Statusbar.tipAddTab": "<PERSON><PERSON><PERSON>re foaie de calcul", "SSE.Views.Statusbar.tipFirst": "Defilare la prima foie", "SSE.Views.Statusbar.tipLast": "Defilare la ultima foaie", "SSE.Views.Statusbar.tipListOfSheets": "Lista foilor de calcul", "SSE.Views.Statusbar.tipNext": "Defilare printr-o foaie din dreapta", "SSE.Views.Statusbar.tipPrev": "Defilare printr-o foaie din stânga", "SSE.Views.Statusbar.tipZoomFactor": "Zoom", "SSE.Views.Statusbar.tipZoomIn": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.ungroupSheets": "<PERSON>ular<PERSON> grupării foilor", "SSE.Views.Statusbar.zoomText": "Zoom {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Operațiunea nu poare fi efectuată pentru celulele selectate.<br>Selectați o altă zonă de date uniformă și încercați din nou.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Operațiunea nu poate fi efectuată pentru zonă de celule selectată.<br>Selectați o zonă de celule în așa fel încât primul rând din tabel să coincide<br>și tabelul rezultat să suprapune peste tabelul curent.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Operațiunea nu poate fi efectuată pentru zonă de celule selectată.<br>Selectați o zonă de celule care nu conține alte tabele.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Formule de matrice cu mai multe celule nu sunt permise în tabele.", "SSE.Views.TableOptionsDialog.txtEmpty": "Câmp obligatoriu", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON><PERSON> tabel", "SSE.Views.TableOptionsDialog.txtInvalidRange": "EROARE! Zonă de celule nu este validă", "SSE.Views.TableOptionsDialog.txtNote": "Anteturile trebuie să rămână pe același rând și zonă de tabel rezultat trebuie să se suprapune peste zonă de tabel inițial.", "SSE.Views.TableOptionsDialog.txtTitle": "Titlu", "SSE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON> colo<PERSON>", "SSE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON><PERSON> tabel", "SSE.Views.TableSettings.insertColumnLeftText": "Inserare coloane la stânga", "SSE.Views.TableSettings.insertColumnRightText": "Inserare coloane la dreapta", "SSE.Views.TableSettings.insertRowAboveText": "Inserare rând <PERSON>", "SSE.Views.TableSettings.insertRowBelowText": "Inserare rând de<PERSON>", "SSE.Views.TableSettings.notcriticalErrorTitle": "Avertisment", "SSE.Views.TableSettings.selectColumnText": "Selectați coloană întreagă ", "SSE.Views.TableSettings.selectDataText": "Selectare date din coloană", "SSE.Views.TableSettings.selectRowText": "Selectați rândul", "SSE.Views.TableSettings.selectTableText": "Selectați tabel", "SSE.Views.TableSettings.textActions": "Acțiuni tabel", "SSE.Views.TableSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textBanded": "Alternant", "SSE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textConvertRange": "Conversie la interval", "SSE.Views.TableSettings.textEdit": "Rânduri și coloane", "SSE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textExistName": "EROARE! Numele de zonă există deja", "SSE.Views.TableSettings.textFilter": "Butonul de filtrare", "SSE.Views.TableSettings.textFirst": "P<PERSON>ul", "SSE.Views.TableSettings.textHeader": "Antet", "SSE.Views.TableSettings.textInvalidName": "EROARE! Nume incorect de tabel", "SSE.Views.TableSettings.textIsLocked": "Acest element este editat de către un alt utilizator.", "SSE.Views.TableSettings.textLast": "Ultima", "SSE.Views.TableSettings.textLongOperation": "Operațiunea de lungă durată", "SSE.Views.TableSettings.textPivot": "Inserare tabelă Pivot", "SSE.Views.TableSettings.textRemDuplicates": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textReservedName": "Numele utilizat a fost deja definit anterior într-o referință de formulă. Introduceți un alt nume.", "SSE.Views.TableSettings.textResize": "Redimensionare tabel", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "Selectare date", "SSE.Views.TableSettings.textSlicer": "Inserare slicer", "SSE.Views.TableSettings.textTableName": "Nume de tabel", "SSE.Views.TableSettings.textTemplate": "Selectați din șablon", "SSE.Views.TableSettings.textTotal": "Total", "SSE.Views.TableSettings.txtGroupTable_Custom": "Particularizat", "SSE.Views.TableSettings.txtGroupTable_Dark": "Întunecat", "SSE.Views.TableSettings.txtGroupTable_Light": "Luminos", "SSE.Views.TableSettings.txtGroupTable_Medium": "Me<PERSON>u", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Stil tabel întunecat", "SSE.Views.TableSettings.txtTable_TableStyleLight": "Stil tabel deschis", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "Stil tabel mediu", "SSE.Views.TableSettings.warnLongOperation": "Operațiunea pe care doriți să o efectuați poate dura destul de mult timp<br><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>i să continuați?", "SSE.Views.TableSettingsAdvanced.textAlt": "Text alternativ", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Des<PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textAltTip": "Furnizarea textului alternativ pentru conținut vizual destinat persoanelor cu deficiențe de vedere și cognitive pentru a le ajuta să înțăleagă mai bine conținutul unei imagini, forme, diagramei sau tabele.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Titlu", "SSE.Views.TableSettingsAdvanced.textTitle": "<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strBackground": "Culoare de fundal", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "Umplere", "SSE.Views.TextArtSettings.strForeground": "Culoarea de prim plan", "SSE.Views.TextArtSettings.strPattern": "Model", "SSE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "Transparență", "SSE.Views.TextArtSettings.strType": "Tip", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "Valoarea introdusă nu este corectă.<br>Selectați valoarea cuprinsă înte 0 pt și 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Umplere cu culoare", "SSE.Views.TextArtSettings.textDirection": "Orientare", "SSE.Views.TextArtSettings.textEmptyPattern": "Fără model", "SSE.Views.TextArtSettings.textFromFile": "<PERSON>", "SSE.Views.TextArtSettings.textFromUrl": "<PERSON><PERSON> URL-ul", "SSE.Views.TextArtSettings.textGradient": "Stop gradient", "SSE.Views.TextArtSettings.textGradientFill": "Umplere gradient", "SSE.Views.TextArtSettings.textImageTexture": "Imagine sau textură", "SSE.Views.TextArtSettings.textLinear": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON><PERSON><PERSON> um<PERSON>", "SSE.Views.TextArtSettings.textPatternFill": "Model", "SSE.Views.TextArtSettings.textPosition": "Poziție", "SSE.Views.TextArtSettings.textRadial": "Radială", "SSE.Views.TextArtSettings.textSelectTexture": "Selectare", "SSE.Views.TextArtSettings.textStretch": "Întindere", "SSE.Views.TextArtSettings.textStyle": "Stil", "SSE.Views.TextArtSettings.textTemplate": "Șablon", "SSE.Views.TextArtSettings.textTexture": "Din textură", "SSE.Views.TextArtSettings.textTile": "Placă", "SSE.Views.TextArtSettings.textTransform": "Transformare", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Adăugare stop gradient", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Eliminare stop gradient", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON><PERSON> reciclat<PERSON>", "SSE.Views.TextArtSettings.txtCanvas": "Pânză", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "Pânză întunecată", "SSE.Views.TextArtSettings.txtGrain": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGranite": "Granit", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtKnit": "Dril", "SSE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> linie", "SSE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtWood": "Lemn", "SSE.Views.Toolbar.capBtnAddComment": "Adaugă comentariu", "SSE.Views.Toolbar.capBtnColorSchemas": "Schemă de culori", "SSE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnInsHeader": "Antet și subsol", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON>licer", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Simbol", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageBreak": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageOrient": "Orientare", "SSE.Views.Toolbar.capBtnPageSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintArea": "Zonă de imprimat", "SSE.Views.Toolbar.capBtnPrintTitles": "<PERSON><PERSON><PERSON><PERSON> titluri", "SSE.Views.Toolbar.capBtnScale": "Scalare pentru a se portivi", "SSE.Views.Toolbar.capImgAlign": "Aliniere", "SSE.Views.Toolbar.capImgBackward": "Trimitere în ultimul plan", "SSE.Views.Toolbar.capImgForward": "Aducere în plan apropiat", "SSE.Views.Toolbar.capImgGroup": "Grupare", "SSE.Views.Toolbar.capInsertChart": "Diagramă", "SSE.Views.Toolbar.capInsertChartRecommend": "Diagramă recomandată", "SSE.Views.Toolbar.capInsertEquation": "Ecuație", "SSE.Views.Toolbar.capInsertHyperlink": "Hyperlink", "SSE.Views.Toolbar.capInsertImage": "Imagine", "SSE.Views.Toolbar.capInsertShape": "Forma", "SSE.Views.Toolbar.capInsertSpark": "Diagramă sparkline", "SSE.Views.Toolbar.capInsertTable": "<PERSON><PERSON>", "SSE.Views.Toolbar.capInsertText": "Casetă text", "SSE.Views.Toolbar.capInsertTextart": "TextArt", "SSE.Views.Toolbar.capShapesMerge": "Îmbinare forme", "SSE.Views.Toolbar.mniCapitalizeWords": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON> <PERSON> fișier", "SSE.Views.Toolbar.mniImageFromStorage": "Imaginea din serviciul stocare", "SSE.Views.Toolbar.mniImageFromUrl": "Imaginea prin URL", "SSE.Views.Toolbar.mniLowerCase": "minuscule", "SSE.Views.Toolbar.mniSentenceCase": "Tip prop<PERSON>.", "SSE.Views.Toolbar.mniToggleCase": "cOMURARE LITERE MARI/MICI.", "SSE.Views.Toolbar.mniUpperCase": "MAJUSCULE", "SSE.Views.Toolbar.textAddPrintArea": "Se adaugă la zonă de imprimare", "SSE.Views.Toolbar.textAlignBottom": "Aliniere jos", "SSE.Views.Toolbar.textAlignCenter": "Aliniere la centru", "SSE.Views.Toolbar.textAlignJust": "<PERSON><PERSON>t stânga-dreapta", "SSE.Views.Toolbar.textAlignLeft": "Aliniere la stânga", "SSE.Views.Toolbar.textAlignMiddle": "Aliniere la mijloc", "SSE.Views.Toolbar.textAlignRight": "Aliniere la dreapta", "SSE.Views.Toolbar.textAlignTop": "Aliniere sus", "SSE.Views.Toolbar.textAllBorders": "Toate borduri", "SSE.Views.Toolbar.textAlpha": "Litera grecească minusculă Alfa", "SSE.Views.Toolbar.textAuto": "Auto", "SSE.Views.Toolbar.textAutoColor": "Automat", "SSE.Views.Toolbar.textBetta": "Litera grecească minusculă Beta", "SSE.Views.Toolbar.textBlackHeart": "Pi<PERSON>ă", "SSE.Views.Toolbar.textBold": "<PERSON><PERSON>", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON><PERSON> bordur<PERSON>", "SSE.Views.Toolbar.textBordersStyle": "<PERSON><PERSON> b<PERSON>", "SSE.Views.Toolbar.textBottom": "Jos: ", "SSE.Views.Toolbar.textBottomBorders": "Bord<PERSON>le în partea de jos", "SSE.Views.Toolbar.textBullet": "Marcator", "SSE.Views.Toolbar.textCellAlign": "Formatare aliniere celule", "SSE.Views.Toolbar.textCenterBorders": "Bordurile verticale în interiorul ", "SSE.Views.Toolbar.textClearPrintArea": "Golire zonă de imprimare", "SSE.Views.Toolbar.textClearRule": "Go<PERSON><PERSON> reguli", "SSE.Views.Toolbar.textClockwise": "Unghi de rotație în sens orar", "SSE.Views.Toolbar.textColorScales": "Scale de culori", "SSE.Views.Toolbar.textCopyright": "Simbolul drepturilor de autor", "SSE.Views.Toolbar.textCounterCw": "Unghi de rotație în sens antiorar", "SSE.Views.Toolbar.textCustom": "Particularizat", "SSE.Views.Toolbar.textDataBars": "Bare de date", "SSE.Views.Toolbar.textDegree": "Simbol grad", "SSE.Views.Toolbar.textDelLeft": "Deplasare celule la stânga", "SSE.Views.Toolbar.textDelPageBreak": "Eliminare sfârșit de pagină", "SSE.Views.Toolbar.textDelta": "Litera grecească minusculă Delta", "SSE.Views.Toolbar.textDelUp": "Deplasare celule în sus", "SSE.Views.Toolbar.textDiagDownBorder": "Bordură diagonală descendentă", "SSE.Views.Toolbar.textDiagUpBorder": "<PERSON><PERSON><PERSON><PERSON> diagonală ascendentă", "SSE.Views.Toolbar.textDivision": "<PERSON>m<PERSON><PERSON> împă<PERSON>", "SSE.Views.Toolbar.textDollar": "Sem<PERSON>l dolarului", "SSE.Views.Toolbar.textDone": "Gata", "SSE.Views.Toolbar.textDown": "În jos", "SSE.Views.Toolbar.textEditVA": "Editare zonă vizibilă", "SSE.Views.Toolbar.textEntireCol": "<PERSON>nt<PERSON>ga colo<PERSON>", "SSE.Views.Toolbar.textEntireRow": "<PERSON>nt<PERSON>g r<PERSON>d", "SSE.Views.Toolbar.textEuro": "Semnul euro", "SSE.Views.Toolbar.textFewPages": "pagini", "SSE.Views.Toolbar.textFillLeft": "Stânga", "SSE.Views.Toolbar.textFillRight": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textFormatCellFill": "Formatare umplere celulă", "SSE.Views.Toolbar.textGreaterEqual": "Mai mare sau egal cu", "SSE.Views.Toolbar.textHeight": "Înălțime", "SSE.Views.Toolbar.textHideVA": "Ascunde zonă vizibilă", "SSE.Views.Toolbar.textHorizontal": "Text orizontal", "SSE.Views.Toolbar.textInfinity": "Infinit", "SSE.Views.Toolbar.textInsDown": "Deplasare celule în jos", "SSE.Views.Toolbar.textInsideBorders": "Borduri în interiorul ", "SSE.Views.Toolbar.textInsPageBreak": "Inserare sfârșit de pagină", "SSE.Views.Toolbar.textInsRight": "Deplasare celule la dreapta", "SSE.Views.Toolbar.textItalic": "Cursiv", "SSE.Views.Toolbar.textItems": "Elemente", "SSE.Views.Toolbar.textLandscape": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textLeft": "Stânga: ", "SSE.Views.Toolbar.textLeftBorders": "Bordură la stânga", "SSE.Views.Toolbar.textLessEqual": "Mai mic sau egal cu", "SSE.Views.Toolbar.textLetterPi": "Litera grecească minusculă Pi", "SSE.Views.Toolbar.textManageRule": "Gestionare reguli", "SSE.Views.Toolbar.textManyPages": "pagini", "SSE.Views.Toolbar.textMarginsLast": "Ultima setare particularizată", "SSE.Views.Toolbar.textMarginsNarrow": "Îngust", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "Lat", "SSE.Views.Toolbar.textMiddleBorders": "Bordurile orizontale în interiorul ", "SSE.Views.Toolbar.textMoreBorders": "<PERSON> multe borduri", "SSE.Views.Toolbar.textMoreFormats": "Mai multe formate", "SSE.Views.Toolbar.textMorePages": "<PERSON> multe pagini", "SSE.Views.Toolbar.textMoreSymbols": "<PERSON> multe simboluri ", "SSE.Views.Toolbar.textNewColor": "Mai multe culori", "SSE.Views.Toolbar.textNewRule": "Regulă nouă", "SSE.Views.Toolbar.textNoBorders": "<PERSON><PERSON><PERSON><PERSON> bord<PERSON>", "SSE.Views.Toolbar.textNotEqualTo": "Nu este egal cu", "SSE.Views.Toolbar.textOneHalf": "Fracție ordinară o doime", "SSE.Views.Toolbar.textOnePage": "pagina", "SSE.Views.Toolbar.textOneQuarter": "Fracție ordinară un sfert", "SSE.Views.Toolbar.textOutBorders": "<PERSON><PERSON><PERSON>n <PERSON>", "SSE.Views.Toolbar.textPageMarginsCustom": "Margini particularizate", "SSE.Views.Toolbar.textPlusMinus": "Semn plus-minus", "SSE.Views.Toolbar.textPortrait": "<PERSON><PERSON>", "SSE.Views.Toolbar.textPrint": "Imp<PERSON><PERSON>", "SSE.Views.Toolbar.textPrintGridlines": "Imprimare linii de grilă", "SSE.Views.Toolbar.textPrintHeadings": "<PERSON><PERSON><PERSON><PERSON> titluri", "SSE.Views.Toolbar.textPrintOptions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textRegistered": "Simbol Marcă înregistrată", "SSE.Views.Toolbar.textResetPageBreak": "<PERSON><PERSON><PERSON><PERSON><PERSON> toate s<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> pagin<PERSON>", "SSE.Views.Toolbar.textRight": "Dreapta: ", "SSE.Views.Toolbar.textRightBorders": "Bord<PERSON> din dreapta", "SSE.Views.Toolbar.textRotateDown": "Rotirea textului în jos", "SSE.Views.Toolbar.textRotateUp": "Rotirea textului în sus", "SSE.Views.Toolbar.textRtlSheet": "Foaia de la dreapta la stânga", "SSE.Views.Toolbar.textScale": "Scară", "SSE.Views.Toolbar.textScaleCustom": "Particularizat", "SSE.Views.Toolbar.textSection": "Semnul secțiune", "SSE.Views.Toolbar.textSelection": "<PERSON> se<PERSON>ț<PERSON>", "SSE.Views.Toolbar.textSeries": "Serie", "SSE.Views.Toolbar.textSetPrintArea": "Setare zonă de imprimare", "SSE.Views.Toolbar.textShapesCombine": "Combinare", "SSE.Views.Toolbar.textShapesFragment": "Bucată", "SSE.Views.Toolbar.textShapesIntersect": "Intersectare", "SSE.Views.Toolbar.textShapesSubstract": "Scădere", "SSE.Views.Toolbar.textShapesUnion": "Uniune", "SSE.Views.Toolbar.textShowVA": "Afișează zona vizibilă", "SSE.Views.Toolbar.textSmile": "Față zâmbitoare albă ", "SSE.Views.Toolbar.textSquareRoot": "Rădăcină pătrată", "SSE.Views.Toolbar.textStrikeout": "Tăiere cu o linie", "SSE.Views.Toolbar.textSubscript": "Indice", "SSE.Views.Toolbar.textSubSuperscript": "Indice/Exponent", "SSE.Views.Toolbar.textSuperscript": "Exponent", "SSE.Views.Toolbar.textTabCollaboration": "Colaborare", "SSE.Views.Toolbar.textTabData": "Date", "SSE.Views.Toolbar.textTabDraw": "Desenare", "SSE.Views.Toolbar.textTabFile": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFormula": "Formula", "SSE.Views.Toolbar.textTabHome": "Acasă", "SSE.Views.Toolbar.textTabInsert": "Inserare", "SSE.Views.Toolbar.textTabLayout": "Aspect", "SSE.Views.Toolbar.textTabProtect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabView": "Vizualizare", "SSE.Views.Toolbar.textThisPivot": "Din acest tabel pivot", "SSE.Views.Toolbar.textThisSheet": "Din această foaie de calcul", "SSE.Views.Toolbar.textThisTable": "Din acest tabel", "SSE.Views.Toolbar.textTilde": "Tildă", "SSE.Views.Toolbar.textTop": "Sus: ", "SSE.Views.Toolbar.textTopBorders": "<PERSON><PERSON><PERSON> de sus", "SSE.Views.Toolbar.textTradeMark": "Simbol de marcă comercială", "SSE.Views.Toolbar.textUnderline": "Subliniat", "SSE.Views.Toolbar.textUp": "În sus", "SSE.Views.Toolbar.textVertical": "Text vertical", "SSE.Views.Toolbar.textWidth": "Lățime", "SSE.Views.Toolbar.textYen": "<PERSON><PERSON><PERSON><PERSON>n", "SSE.Views.Toolbar.textZoom": "Zoom", "SSE.Views.Toolbar.tipAlignBottom": "Aliniere jos", "SSE.Views.Toolbar.tipAlignCenter": "Aliniere la centru", "SSE.Views.Toolbar.tipAlignJust": "<PERSON><PERSON>t stânga-dreapta", "SSE.Views.Toolbar.tipAlignLeft": "Aliniere la stânga", "SSE.Views.Toolbar.tipAlignMiddle": "Aliniere la mijloc", "SSE.Views.Toolbar.tipAlignRight": "Aliniere la dreapta", "SSE.Views.Toolbar.tipAlignTop": "Aliniere sus", "SSE.Views.Toolbar.tipAutofilter": "Sortare și filtrare", "SSE.Views.Toolbar.tipBack": "Înapoi", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCellStyle": "Stil celula", "SSE.Views.Toolbar.tipChangeCase": "Modificarea scrierii cu majuscule sau minuscule", "SSE.Views.Toolbar.tipChangeChart": "Modificare tip diagramă", "SSE.Views.Toolbar.tipClearStyle": "Golire", "SSE.Views.Toolbar.tipColorSchemas": "Modificare schemă de culori", "SSE.Views.Toolbar.tipCondFormat": "Formatarea condiționată", "SSE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON> stil", "SSE.Views.Toolbar.tipCut": "<PERSON>up<PERSON>", "SSE.Views.Toolbar.tipDecDecimal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDecFont": "Reducere font", "SSE.Views.Toolbar.tipDeleteOpt": "<PERSON><PERSON><PERSON><PERSON> celule", "SSE.Views.Toolbar.tipDigStyleAccounting": "Stil Contabil", "SSE.Views.Toolbar.tipDigStyleComma": "Stil virgulă", "SSE.Views.Toolbar.tipDigStyleCurrency": "Stil moned<PERSON>", "SSE.Views.Toolbar.tipDigStylePercent": "Stil procent", "SSE.Views.Toolbar.tipEditChart": "Editare diagramă", "SSE.Views.Toolbar.tipEditChartData": "Selectare date", "SSE.Views.Toolbar.tipEditChartType": "Modificare tip diagramă", "SSE.Views.Toolbar.tipEditHeader": "Editare antet și subsol", "SSE.Views.Toolbar.tipFontColor": "Culoare font", "SSE.Views.Toolbar.tipFontName": "Font", "SSE.Views.Toolbar.tipFontSize": "Dimensiune font", "SSE.Views.Toolbar.tipHAlighOle": "Alinierea orizontală", "SSE.Views.Toolbar.tipImgAlign": "Aliniere obiecte", "SSE.Views.Toolbar.tipImgGroup": "Grupare obiecte", "SSE.Views.Toolbar.tipIncDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipIncFont": "Creștere font", "SSE.Views.Toolbar.tipInsertChart": "Inserare diagramă", "SSE.Views.Toolbar.tipInsertChartRecommend": "Inserare diagramă recomandată", "SSE.Views.Toolbar.tipInsertChartSpark": "Inserare diagramă", "SSE.Views.Toolbar.tipInsertEquation": "Inserare ecuație", "SSE.Views.Toolbar.tipInsertHorizontalText": "Inserare casetă text orizontală", "SSE.Views.Toolbar.tipInsertHyperlink": "Adăugare hyperlink", "SSE.Views.Toolbar.tipInsertImage": "Inserare imagine", "SSE.Views.Toolbar.tipInsertOpt": "Inserare celule", "SSE.Views.Toolbar.tipInsertShape": "Inserare formă", "SSE.Views.Toolbar.tipInsertSlicer": "Inserare slicer", "SSE.Views.Toolbar.tipInsertSmartArt": "Inserare SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Inserare diagramă sparkline", "SSE.Views.Toolbar.tipInsertSymbol": "Inserare simbol", "SSE.Views.Toolbar.tipInsertTable": "Inserare tabel", "SSE.Views.Toolbar.tipInsertText": "Inserare casetă text", "SSE.Views.Toolbar.tipInsertTextart": "Inserare TextArt", "SSE.Views.Toolbar.tipInsertVerticalText": "Inserare casetă text verticală", "SSE.Views.Toolbar.tipMerge": "Îmbinare și centrare", "SSE.Views.Toolbar.tipNone": "Niciuna", "SSE.Views.Toolbar.tipNumFormat": "Formatul de număr", "SSE.Views.Toolbar.tipPageBreak": "Inserați un sfârșit de pagină unde doriți să înceapă următoarea pagină în copia tipărită. ", "SSE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON><PERSON> de pagin<PERSON>", "SSE.Views.Toolbar.tipPageOrient": "Orientare pagină", "SSE.Views.Toolbar.tipPageSize": "<PERSON><PERSON><PERSON><PERSON> pagină", "SSE.Views.Toolbar.tipPaste": "Lipire", "SSE.Views.Toolbar.tipPrColor": "Culoare de umplere", "SSE.Views.Toolbar.tipPrint": "Imp<PERSON><PERSON>", "SSE.Views.Toolbar.tipPrintArea": "Zonă de imprimat", "SSE.Views.Toolbar.tipPrintQuick": "Imprimare rapidă", "SSE.Views.Toolbar.tipPrintTitles": "<PERSON><PERSON><PERSON><PERSON> titluri", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipRtlSheet": "Schimbarea orientării foii de lucru astfel încât prima coloană să fie în partea dreaptă", "SSE.Views.Toolbar.tipSave": "Salvează", "SSE.Views.Toolbar.tipSaveCoauth": "Salvați modificările dvs. ca alți utilizatorii să le vadă.", "SSE.Views.Toolbar.tipScale": "Scalare pentru a se portivi", "SSE.Views.Toolbar.tipSelectAll": "Selectare totală", "SSE.Views.Toolbar.tipSendBackward": "Trimitere în ultimul plan", "SSE.Views.Toolbar.tipSendForward": "Aducere în plan apropiat", "SSE.Views.Toolbar.tipShapesMerge": "Îmbinare forme", "SSE.Views.Toolbar.tipSynchronize": "Documentul a fost modificat de către un alt utilizator. Salvați modificările făcute de dumneavoastră și reîmprospătați documentul.", "SSE.Views.Toolbar.tipTextFormatting": "Mai multe instrumente de formatare", "SSE.Views.Toolbar.tipTextOrientation": "Orientare", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipVAlighOle": "Aliniere verticală", "SSE.Views.Toolbar.tipVisibleArea": "Zonă vizibilă", "SSE.Views.Toolbar.tipWrap": "Incadrarea textului", "SSE.Views.Toolbar.txtAccounting": "Contabilitate", "SSE.Views.Toolbar.txtAdditional": "Suplimentar", "SSE.Views.Toolbar.txtAscending": "Ascendent", "SSE.Views.Toolbar.txtAutosumTip": "Sumă", "SSE.Views.Toolbar.txtCellStyle": "Stil celula", "SSE.Views.Toolbar.txtClearAll": "Toate", "SSE.Views.Toolbar.txtClearComments": "Comenta<PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFilter": "Golire filtru", "SSE.Views.Toolbar.txtClearFormat": "Formatare", "SSE.Views.Toolbar.txtClearFormula": "Fun<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearHyper": "Hyperlinkurile", "SSE.Views.Toolbar.txtClearText": "Text", "SSE.Views.Toolbar.txtCurrency": "Monedă", "SSE.Views.Toolbar.txtCustom": "Particularizat", "SSE.Views.Toolbar.txtDate": "Data", "SSE.Views.Toolbar.txtDateLong": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDateShort": "<PERSON><PERSON><PERSON> scurtă", "SSE.Views.Toolbar.txtDateTime": "Dată și oră", "SSE.Views.Toolbar.txtDescending": "Descendent", "SSE.Views.Toolbar.txtDollar": "$ Dolar", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Exponențial", "SSE.Views.Toolbar.txtFillNum": "Umplere", "SSE.Views.Toolbar.txtFilter": "Filtrare", "SSE.Views.Toolbar.txtFormula": "Inserare funcție", "SSE.Views.Toolbar.txtFraction": "Frac<PERSON><PERSON>", "SSE.Views.Toolbar.txtFranc": "CHF Franc elvețian", "SSE.Views.Toolbar.txtGeneral": "General", "SSE.Views.Toolbar.txtInteger": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtManageRange": "Manager nume", "SSE.Views.Toolbar.txtMergeAcross": "Îmb<PERSON>re mai multe celule", "SSE.Views.Toolbar.txtMergeCells": "Îmbinare celule", "SSE.Views.Toolbar.txtMergeCenter": "Îmbinare și centrare", "SSE.Views.Toolbar.txtNamedRange": "Zonele denumite", "SSE.Views.Toolbar.txtNewRange": "Definire nume", "SSE.Views.Toolbar.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> bord<PERSON>", "SSE.Views.Toolbar.txtNumber": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtPasteRange": "Lipire nume", "SSE.Views.Toolbar.txtPercentage": "Procentaj", "SSE.Views.Toolbar.txtPound": "£ Lira sterlină", "SSE.Views.Toolbar.txtRouble": "₽ Rublă", "SSE.Views.Toolbar.txtScientific": "Științific ", "SSE.Views.Toolbar.txtSearch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSort": "Sortare", "SSE.Views.Toolbar.txtSortAZ": "Sortare ascendent", "SSE.Views.Toolbar.txtSortZA": "Sortare descendent", "SSE.Views.Toolbar.txtSpecial": "Special", "SSE.Views.Toolbar.txtTableTemplate": "Formatare ca șablon tabel", "SSE.Views.Toolbar.txtText": "Text", "SSE.Views.Toolbar.txtTime": "Oră", "SSE.Views.Toolbar.txtUnmerge": "Anularea îmbinării cel<PERSON>", "SSE.Views.Toolbar.txtYen": "¥ Yen", "SSE.Views.Top10FilterDialog.textType": "<PERSON><PERSON>ș<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBottom": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBy": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtItems": "Element", "SSE.Views.Top10FilterDialog.txtPercent": "Procent", "SSE.Views.Top10FilterDialog.txtSum": "Sumă", "SSE.Views.Top10FilterDialog.txtTitle": "Filtrarea automată primele 10 ", "SSE.Views.Top10FilterDialog.txtTop": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtValueTitle": "Filtrare primele 10 ", "SSE.Views.ValueFieldSettingsDialog.textNext": "(următorul)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "Formatul de număr", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(anteriorul)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "<PERSON><PERSON><PERSON> valo<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Me<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Câmp de bază", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Element de bază", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 din %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Contorizare", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Contorizare numere", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Nume particularizat", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Diferență din", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Index", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Max", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "<PERSON>ăr<PERSON> calcul", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "% din", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "% diferență din", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "% din coloană", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% din total general", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% din total părinte", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% din total coloană părinte", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% din total rând părinte", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% total parțial în", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "% din rând", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Produs", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Rang de la cel mai mic la cel mai mare", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Rang de la cel mai mare la cel mai mic", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Total în execuție în", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Afișare valori ca", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Numele sursei:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Sumă", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "<PERSON><PERSON><PERSON> valori dup<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.guestText": "Invitat", "SSE.Views.ViewManagerDlg.lockText": "Blocat", "SSE.Views.ViewManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textDuplicate": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textEmpty": "<PERSON>cio vizualizare nu a fost creată până când.", "SSE.Views.ViewManagerDlg.textGoTo": "Salt la vizualizarea", "SSE.Views.ViewManagerDlg.textLongName": "Numărul maxim de caractere dintr-un nume este 128 caractere.", "SSE.Views.ViewManagerDlg.textNew": "Nou", "SSE.Views.ViewManagerDlg.textRename": "Redenumire", "SSE.Views.ViewManagerDlg.textRenameError": "Numele de vizualizare trebuie completată.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Redenumire vizualizarea", "SSE.Views.ViewManagerDlg.textViews": "Vizualizări de foaie", "SSE.Views.ViewManagerDlg.tipIsLocked": "Acest element este editat de către un alt utilizator.", "SSE.Views.ViewManagerDlg.txtTitle": "Manager de vizualizare foi", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Sunteți sigur că doriți să ștergeți această vizualizare de foaie?", "SSE.Views.ViewManagerDlg.warnDeleteView": "Dvs. încercați să ștergeți o vizualizare activă pentru moment '%1'.<br>Doriți să o închideți și să o ștergeți?", "SSE.Views.ViewTab.capBtnFreeze": "Înghețare panouri", "SSE.Views.ViewTab.capBtnSheetView": "Vizualizare de foaie", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Afișează bară de instrumente permanent", "SSE.Views.ViewTab.textClose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "A îmbina selectorii foii de lucru cu bara de stare", "SSE.Views.ViewTab.textCreate": "Nou", "SSE.Views.ViewTab.textDefault": "Implicit", "SSE.Views.ViewTab.textFill": "Umplere", "SSE.Views.ViewTab.textFormula": "Bara de formule", "SSE.Views.ViewTab.textFreezeCol": "Înghețarea primei coloane", "SSE.Views.ViewTab.textFreezeRow": "Înghețarea rândului de sus", "SSE.Views.ViewTab.textGridlines": "Linii de grilă", "SSE.Views.ViewTab.textHeadings": "T<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textInterfaceTheme": "Tema de interfață", "SSE.Views.ViewTab.textLeftMenu": "<PERSON><PERSON>", "SSE.Views.ViewTab.textLine": "<PERSON><PERSON>", "SSE.Views.ViewTab.textMacros": "Macrocomandă", "SSE.Views.ViewTab.textManager": "Manager v<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textRightMenu": "<PERSON><PERSON> drea<PERSON>", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Afișare umbră pentru panouri înghețate", "SSE.Views.ViewTab.textTabStyle": "<PERSON><PERSON> fil<PERSON>", "SSE.Views.ViewTab.textUnFreeze": "Dezghețare panouri", "SSE.Views.ViewTab.textZeros": "Afișare un zero", "SSE.Views.ViewTab.textZoom": "Zoom", "SSE.Views.ViewTab.tipClose": "Închidere vizualizare de foi", "SSE.Views.ViewTab.tipCreate": "<PERSON><PERSON><PERSON> vizualiz<PERSON><PERSON> de foi", "SSE.Views.ViewTab.tipFreeze": "Înghețare panouri", "SSE.Views.ViewTab.tipInterfaceTheme": "Tema interfeței", "SSE.Views.ViewTab.tipMacros": "Macrocomandă", "SSE.Views.ViewTab.tipSheetView": "Vizualizare de foaie", "SSE.Views.ViewTab.tipViewNormal": "Vizualizați documentul în vizualizarea Normal", "SSE.Views.ViewTab.tipViewPageBreak": "<PERSON><PERSON><PERSON><PERSON> unde vor apărea sfârșiturile de pagină, când documentul este imprimat.", "SSE.Views.ViewTab.txtViewNormal": "Normal", "SSE.Views.ViewTab.txtViewPageBreak": "Examinare sfârșit de pagină", "SSE.Views.WatchDialog.closeButtonText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textBook": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textCell": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>pra<PERSON>", "SSE.Views.WatchDialog.textDeleteAll": "Șter<PERSON><PERSON> totală", "SSE.Views.WatchDialog.textFormula": "Formula", "SSE.Views.WatchDialog.textName": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textValue": "Valoare", "SSE.Views.WatchDialog.txtTitle": "Fereastră de supraveghere", "SSE.Views.WBProtection.hintAllowRanges": "Se permite modificarea zonelor", "SSE.Views.WBProtection.hintProtectRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.hintProtectSheet": "Protejarea foii de calcul", "SSE.Views.WBProtection.hintProtectWB": "Protejarea registrului de calcul", "SSE.Views.WBProtection.txtAllowRanges": "Se permite modificarea zonelor", "SSE.Views.WBProtection.txtHiddenFormula": "Formule mascate", "SSE.Views.WBProtection.txtLockedCell": "<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.WBProtection.txtLockedShape": "Forma blocată", "SSE.Views.WBProtection.txtLockedText": "Blocare text", "SSE.Views.WBProtection.txtProtectRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtProtectSheet": "Protejarea foii de calcul", "SSE.Views.WBProtection.txtProtectWB": "Protejarea registrului de calcul", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Introduceți parola pentru dezactivarea protejării a foii de calcul", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Anularea protecției foii de calcul", "SSE.Views.WBProtection.txtWBUnlockDescription": "Introduceți parola pentru dezactivarea protejării a registrului de calcul"}