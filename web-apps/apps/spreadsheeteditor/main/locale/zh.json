{"cancelButtonText": "取消", "Common.Controllers.Chat.notcriticalErrorTitle": "警告", "Common.Controllers.Desktop.hintBtnHome": "显示主窗口", "Common.Controllers.Desktop.itemCreateFromTemplate": "用模板创建", "Common.Controllers.History.notcriticalErrorTitle": "警告", "Common.Controllers.History.txtErrorLoadHistory": "历史记录加载失败", "Common.Controllers.Plugins.helpUseMacros": "在这里可以找到宏按钮", "Common.Controllers.Plugins.helpUseMacrosHeader": "更新了对宏的访问", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "插件已成功安装。您可以在这里访问所有后台插件。", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b>已成功安装。您可以在这里访问所有后台插件。", "Common.Controllers.Plugins.textRunInstalledPlugins": "运行已安装的插件", "Common.Controllers.Plugins.textRunPlugin": "运行插件", "Common.define.chartData.textArea": "面积图", "Common.define.chartData.textAreaStacked": "堆积面积", "Common.define.chartData.textAreaStackedPer": "100%堆叠区域", "Common.define.chartData.textBar": "条形图", "Common.define.chartData.textBarNormal": "簇状柱形图", "Common.define.chartData.textBarNormal3d": "三维分组柱形图", "Common.define.chartData.textBarNormal3dPerspective": "三维柱形图", "Common.define.chartData.textBarStacked": "堆积柱形图", "Common.define.chartData.textBarStacked3d": "三维堆积柱形图", "Common.define.chartData.textBarStackedPer": "100%堆积柱状图", "Common.define.chartData.textBarStackedPer3d": "三维100%堆积柱形图", "Common.define.chartData.textCharts": "图表", "Common.define.chartData.textColumn": "柱状图", "Common.define.chartData.textColumnSpark": "柱状图", "Common.define.chartData.textCombo": "组合图", "Common.define.chartData.textComboAreaBar": "堆叠面积-丛集柱状图", "Common.define.chartData.textComboBarLine": "簇状柱形图-折线图", "Common.define.chartData.textComboBarLineSecondary": "簇状柱形图-次坐标轴上的折线图", "Common.define.chartData.textComboCustom": "自定义组合", "Common.define.chartData.textDoughnut": "圆环图", "Common.define.chartData.textHBarNormal": "簇状条形图", "Common.define.chartData.textHBarNormal3d": "三维分组条形图", "Common.define.chartData.textHBarStacked": "堆积条形图", "Common.define.chartData.textHBarStacked3d": "三维堆积条形图", "Common.define.chartData.textHBarStackedPer": "100%堆积条形图", "Common.define.chartData.textHBarStackedPer3d": "三维100%堆积条形图", "Common.define.chartData.textLine": "折线图", "Common.define.chartData.textLine3d": "三维折线图", "Common.define.chartData.textLineMarker": "带标记的线条", "Common.define.chartData.textLineSpark": "折线图", "Common.define.chartData.textLineStacked": "堆叠折线图", "Common.define.chartData.textLineStackedMarker": "带标记的堆积线", "Common.define.chartData.textLineStackedPer": "100%堆积折线图", "Common.define.chartData.textLineStackedPerMarker": "带标记的100%堆积折线图", "Common.define.chartData.textPie": "圆饼图", "Common.define.chartData.textPie3d": "三维饼图", "Common.define.chartData.textPoint": "XY（散点图）", "Common.define.chartData.textRadar": "雷达图", "Common.define.chartData.textRadarFilled": "填充雷达", "Common.define.chartData.textRadarMarker": "带标记的雷达", "Common.define.chartData.textScatter": "散布图", "Common.define.chartData.textScatterLine": "直线散布图", "Common.define.chartData.textScatterLineMarker": "直线和标记散布图", "Common.define.chartData.textScatterSmooth": "平滑线条散布图", "Common.define.chartData.textScatterSmoothMarker": "平滑线条和标记的散布图", "Common.define.chartData.textSparks": "迷你图", "Common.define.chartData.textStock": "股票", "Common.define.chartData.textSurface": "平面", "Common.define.chartData.textWinLossSpark": "赢/输", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "未设置格式", "Common.define.conditionalData.text1Above": "高于1个标准偏差", "Common.define.conditionalData.text1Below": "低于1个标准偏差", "Common.define.conditionalData.text2Above": "高于2个标准偏差", "Common.define.conditionalData.text2Below": "低于2个标准偏差", "Common.define.conditionalData.text3Above": "高于3个标准偏差", "Common.define.conditionalData.text3Below": "低于3个标准偏差", "Common.define.conditionalData.textAbove": "以上", "Common.define.conditionalData.textAverage": "平均值", "Common.define.conditionalData.textBegins": "开头为", "Common.define.conditionalData.textBelow": "下方", "Common.define.conditionalData.textBetween": "位于之间", "Common.define.conditionalData.textBlank": "空白", "Common.define.conditionalData.textBlanks": "包含空白", "Common.define.conditionalData.textBottom": "底部", "Common.define.conditionalData.textContains": "包含", "Common.define.conditionalData.textDataBar": "数据栏", "Common.define.conditionalData.textDate": "日期", "Common.define.conditionalData.textDuplicate": "复制", "Common.define.conditionalData.textEnds": "结尾为", "Common.define.conditionalData.textEqAbove": "等于或高于", "Common.define.conditionalData.textEqBelow": "等于或小于", "Common.define.conditionalData.textEqual": "等于", "Common.define.conditionalData.textError": "错误", "Common.define.conditionalData.textErrors": "包含错误", "Common.define.conditionalData.textFormula": "公式", "Common.define.conditionalData.textGreater": "大于", "Common.define.conditionalData.textGreaterEq": "大于等于", "Common.define.conditionalData.textIconSets": "图标集", "Common.define.conditionalData.textLast7days": "最近7天", "Common.define.conditionalData.textLastMonth": "上个月", "Common.define.conditionalData.textLastWeek": "上周", "Common.define.conditionalData.textLess": "小于", "Common.define.conditionalData.textLessEq": "小于或等于", "Common.define.conditionalData.textNextMonth": "下个月", "Common.define.conditionalData.textNextWeek": "下周", "Common.define.conditionalData.textNotBetween": "不介于", "Common.define.conditionalData.textNotBlanks": "不包含空白", "Common.define.conditionalData.textNotContains": "不含", "Common.define.conditionalData.textNotEqual": "不等于", "Common.define.conditionalData.textNotErrors": "不包含错误", "Common.define.conditionalData.textText": "文字", "Common.define.conditionalData.textThisMonth": "这个月", "Common.define.conditionalData.textThisWeek": "本周", "Common.define.conditionalData.textToday": "今天", "Common.define.conditionalData.textTomorrow": "明天", "Common.define.conditionalData.textTop": "顶部", "Common.define.conditionalData.textUnique": "唯一的", "Common.define.conditionalData.textValue": "此值是", "Common.define.conditionalData.textYesterday": "昨天", "Common.define.smartArt.textAccentedPicture": "强调图片", "Common.define.smartArt.textAccentProcess": "强调流程", "Common.define.smartArt.textAlternatingFlow": "交替流程", "Common.define.smartArt.textAlternatingHexagons": "交替六边形", "Common.define.smartArt.textAlternatingPictureBlocks": "交替图片块", "Common.define.smartArt.textAlternatingPictureCircles": "交替图片圆形", "Common.define.smartArt.textArchitectureLayout": "架构布局", "Common.define.smartArt.textArrowRibbon": "带状箭头", "Common.define.smartArt.textAscendingPictureAccentProcess": "升序图片重点流程", "Common.define.smartArt.textBalance": "平衡", "Common.define.smartArt.textBasicBendingProcess": "基础弯曲流程", "Common.define.smartArt.textBasicBlockList": "基础块状列表", "Common.define.smartArt.textBasicChevronProcess": "基础V型流程", "Common.define.smartArt.textBasicCycle": "基础循环", "Common.define.smartArt.textBasicMatrix": "基础矩阵", "Common.define.smartArt.textBasicPie": "基础饼图", "Common.define.smartArt.textBasicProcess": "基础流程", "Common.define.smartArt.textBasicPyramid": "基础金字塔", "Common.define.smartArt.textBasicRadial": "基础放射图", "Common.define.smartArt.textBasicTarget": "基础目标", "Common.define.smartArt.textBasicTimeline": "基础时间轴", "Common.define.smartArt.textBasicVenn": "基础维恩图", "Common.define.smartArt.textBendingPictureAccentList": "蛇形图片重点列表", "Common.define.smartArt.textBendingPictureBlocks": "蛇形图片块", "Common.define.smartArt.textBendingPictureCaption": "蛇形图片标题", "Common.define.smartArt.textBendingPictureCaptionList": "蛇形图片标题列表", "Common.define.smartArt.textBendingPictureSemiTranparentText": "蛇形图片半透明文字", "Common.define.smartArt.textBlockCycle": "块循环", "Common.define.smartArt.textBubblePictureList": "气泡图列表", "Common.define.smartArt.textCaptionedPictures": "带标题的图片", "Common.define.smartArt.textChevronAccentProcess": "V型强调流程", "Common.define.smartArt.textChevronList": "V型列表", "Common.define.smartArt.textCircleAccentTimeline": "圆形强调时间线", "Common.define.smartArt.textCircleArrowProcess": "圆形箭头流程", "Common.define.smartArt.textCirclePictureHierarchy": "圆形图片层次结构", "Common.define.smartArt.textCircleProcess": "圆形流程", "Common.define.smartArt.textCircleRelationship": "圆形关系", "Common.define.smartArt.textCircularBendingProcess": "环状蛇形流程", "Common.define.smartArt.textCircularPictureCallout": "圆形图片标注", "Common.define.smartArt.textClosedChevronProcess": "闭合V型流程", "Common.define.smartArt.textContinuousArrowProcess": "连续箭头流程", "Common.define.smartArt.textContinuousBlockProcess": "连续块流程", "Common.define.smartArt.textContinuousCycle": "连续循环", "Common.define.smartArt.textContinuousPictureList": "连续图片列表", "Common.define.smartArt.textConvergingArrows": "汇聚箭头", "Common.define.smartArt.textConvergingRadial": "汇聚放射线", "Common.define.smartArt.textConvergingText": "汇聚文本", "Common.define.smartArt.textCounterbalanceArrows": "平衡箭头", "Common.define.smartArt.textCycle": "循环", "Common.define.smartArt.textCycleMatrix": "循环矩阵", "Common.define.smartArt.textDescendingBlockList": "降序块列表", "Common.define.smartArt.textDescendingProcess": "降序流程", "Common.define.smartArt.textDetailedProcess": "详细流程", "Common.define.smartArt.textDivergingArrows": "发散箭头", "Common.define.smartArt.textDivergingRadial": "发散径向", "Common.define.smartArt.textEquation": "公式", "Common.define.smartArt.textFramedTextPicture": "带边框的文本图片", "Common.define.smartArt.textFunnel": "漏斗", "Common.define.smartArt.textGear": "齿轮", "Common.define.smartArt.textGridMatrix": "网格矩阵", "Common.define.smartArt.textGroupedList": "分组列表", "Common.define.smartArt.textHalfCircleOrganizationChart": "半圆组织结构图", "Common.define.smartArt.textHexagonCluster": "六边形集群", "Common.define.smartArt.textHexagonRadial": "六边形射线", "Common.define.smartArt.textHierarchy": "层级结构", "Common.define.smartArt.textHierarchyList": "层级结构列表", "Common.define.smartArt.textHorizontalBulletList": "水平项目符号列表", "Common.define.smartArt.textHorizontalHierarchy": "水平层次结构", "Common.define.smartArt.textHorizontalLabeledHierarchy": "水平标记层次", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "水平多级层次结构", "Common.define.smartArt.textHorizontalOrganizationChart": "水平组织结构图", "Common.define.smartArt.textHorizontalPictureList": "水平图片列表", "Common.define.smartArt.textIncreasingArrowProcess": "递增箭头流程", "Common.define.smartArt.textIncreasingCircleProcess": "递增圆圈流程", "Common.define.smartArt.textInterconnectedBlockProcess": "互连块流程", "Common.define.smartArt.textInterconnectedRings": "互连环图", "Common.define.smartArt.textInvertedPyramid": "倒金字塔", "Common.define.smartArt.textLabeledHierarchy": "已标记层次", "Common.define.smartArt.textLinearVenn": "线性韦恩图", "Common.define.smartArt.textLinedList": "划线列表", "Common.define.smartArt.textList": "列表", "Common.define.smartArt.textMatrix": "矩阵", "Common.define.smartArt.textMultidirectionalCycle": "多方向循环", "Common.define.smartArt.textNameAndTitleOrganizationChart": "姓名和职务组织结构图", "Common.define.smartArt.textNestedTarget": "嵌套的目标", "Common.define.smartArt.textNondirectionalCycle": "非定向循环", "Common.define.smartArt.textOpposingArrows": "反向箭头", "Common.define.smartArt.textOpposingIdeas": "相对观点", "Common.define.smartArt.textOrganizationChart": "组织图", "Common.define.smartArt.textOther": "其它", "Common.define.smartArt.textPhasedProcess": "分阶段处理", "Common.define.smartArt.textPicture": "图片", "Common.define.smartArt.textPictureAccentBlocks": "图片强调块", "Common.define.smartArt.textPictureAccentList": "图片强调列表", "Common.define.smartArt.textPictureAccentProcess": "图片强调文字流程", "Common.define.smartArt.textPictureCaptionList": "图片标题列表", "Common.define.smartArt.textPictureFrame": "图片框架", "Common.define.smartArt.textPictureGrid": "图片网格", "Common.define.smartArt.textPictureLineup": "图片排列", "Common.define.smartArt.textPictureOrganizationChart": "图片组织图", "Common.define.smartArt.textPictureStrips": "图片条纹", "Common.define.smartArt.textPieProcess": "圆饼图流程", "Common.define.smartArt.textPlusAndMinus": "加减", "Common.define.smartArt.textProcess": "流程", "Common.define.smartArt.textProcessArrows": "流程箭头", "Common.define.smartArt.textProcessList": "流程列表", "Common.define.smartArt.textPyramid": "金字塔", "Common.define.smartArt.textPyramidList": "金字塔列表", "Common.define.smartArt.textRadialCluster": "放射状群集", "Common.define.smartArt.textRadialCycle": "径向循环", "Common.define.smartArt.textRadialList": "径向列表", "Common.define.smartArt.textRadialPictureList": "放射状图片列表", "Common.define.smartArt.textRadialVenn": "径向韦恩图", "Common.define.smartArt.textRandomToResultProcess": "随机结果流程", "Common.define.smartArt.textRelationship": "关系", "Common.define.smartArt.textRepeatingBendingProcess": "重复弯曲流程", "Common.define.smartArt.textReverseList": "反向列表", "Common.define.smartArt.textSegmentedCycle": "分段循环", "Common.define.smartArt.textSegmentedProcess": "分段流程", "Common.define.smartArt.textSegmentedPyramid": "分段金字塔", "Common.define.smartArt.textSnapshotPictureList": "快照图片列表", "Common.define.smartArt.textSpiralPicture": "螺旋图", "Common.define.smartArt.textSquareAccentList": "方形强调列表", "Common.define.smartArt.textStackedList": "堆积列表", "Common.define.smartArt.textStackedVenn": "堆积韦恩图", "Common.define.smartArt.textStaggeredProcess": "交错流程", "Common.define.smartArt.textStepDownProcess": "向下阶梯式流程", "Common.define.smartArt.textStepUpProcess": "向上阶梯式流程", "Common.define.smartArt.textSubStepProcess": "子步骤流程", "Common.define.smartArt.textTabbedArc": "已定位的弧形", "Common.define.smartArt.textTableHierarchy": "表格层次", "Common.define.smartArt.textTableList": "表格列表", "Common.define.smartArt.textTabList": "标签列表", "Common.define.smartArt.textTargetList": "目标列表", "Common.define.smartArt.textTextCycle": "文本循环", "Common.define.smartArt.textThemePictureAccent": "主题图片强调", "Common.define.smartArt.textThemePictureAlternatingAccent": "主题图片交替强调", "Common.define.smartArt.textThemePictureGrid": "主题图片网格", "Common.define.smartArt.textTitledMatrix": "标题矩阵", "Common.define.smartArt.textTitledPictureAccentList": "标题图片强调列表", "Common.define.smartArt.textTitledPictureBlocks": "标题图片块", "Common.define.smartArt.textTitlePictureLineup": "标题图片排列", "Common.define.smartArt.textTrapezoidList": "梯形列表", "Common.define.smartArt.textUpwardArrow": "向上箭头", "Common.define.smartArt.textVaryingWidthList": "可变宽度列表", "Common.define.smartArt.textVerticalAccentList": "垂直强调列表", "Common.define.smartArt.textVerticalArrowList": "垂直箭头列表", "Common.define.smartArt.textVerticalBendingProcess": "垂直弯曲流程", "Common.define.smartArt.textVerticalBlockList": "垂直块列表", "Common.define.smartArt.textVerticalBoxList": "垂直方框列表", "Common.define.smartArt.textVerticalBracketList": "垂直括号列表", "Common.define.smartArt.textVerticalBulletList": "垂直项目符号列表", "Common.define.smartArt.textVerticalChevronList": "垂直V型列表", "Common.define.smartArt.textVerticalCircleList": "垂直循环列表", "Common.define.smartArt.textVerticalCurvedList": "垂直曲线列表", "Common.define.smartArt.textVerticalEquation": "垂直方程式", "Common.define.smartArt.textVerticalPictureAccentList": "垂直图片强调列表", "Common.define.smartArt.textVerticalPictureList": "垂直图片列表", "Common.define.smartArt.textVerticalProcess": "垂直流程", "Common.Translation.textMoreButton": "更多", "Common.Translation.tipFileLocked": "文档编辑被锁定，您可以稍后进行更改并将其保存为本地副本。", "Common.Translation.tipFileReadOnly": "该文件是只读的。若要保留更改，请使用新名称或将文件保存在其他位置。", "Common.Translation.warnFileLocked": "正在另一个应用程序中编辑该文件。您可以继续编辑并将其另存为副本。", "Common.Translation.warnFileLockedBtnEdit": "创建副本", "Common.Translation.warnFileLockedBtnView": "打开查看", "Common.UI.ButtonColored.textAutoColor": "自动", "Common.UI.ButtonColored.textEyedropper": "拾色器", "Common.UI.ButtonColored.textNewColor": "更多颜色", "Common.UI.ComboBorderSize.txtNoBorders": "无边框", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "无边框", "Common.UI.ComboDataView.emptyComboText": "无样式", "Common.UI.ExtendedColorDialog.addButtonText": "添加", "Common.UI.ExtendedColorDialog.textCurrent": "当前", "Common.UI.ExtendedColorDialog.textHexErr": "输入的值不正确。<br>请输入000000和FFFFFF之间的值。", "Common.UI.ExtendedColorDialog.textNew": "新建", "Common.UI.ExtendedColorDialog.textRGBErr": "输入的值不正确。<br>请输入介于0和255之间的数值。", "Common.UI.HSBColorPicker.textNoColor": "没有颜色", "Common.UI.InputField.txtEmpty": "这是必填栏", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "隐藏密码", "Common.UI.InputFieldBtnPassword.textHintHold": "按住显示密码", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "显示密码", "Common.UI.SearchBar.textFind": "查找", "Common.UI.SearchBar.tipCloseSearch": "关闭搜索", "Common.UI.SearchBar.tipNextResult": "下一个结果", "Common.UI.SearchBar.tipOpenAdvancedSettings": "打开高级设置", "Common.UI.SearchBar.tipPreviousResult": "上一个结果", "Common.UI.SearchDialog.textHighlight": "高亮显示结果", "Common.UI.SearchDialog.textMatchCase": "区分大小写", "Common.UI.SearchDialog.textReplaceDef": "输入替换文字", "Common.UI.SearchDialog.textSearchStart": "在这里输入你的文字", "Common.UI.SearchDialog.textTitle": "查找和替换", "Common.UI.SearchDialog.textTitle2": "查找", "Common.UI.SearchDialog.textWholeWords": "仅限完整单词", "Common.UI.SearchDialog.txtBtnHideReplace": "隐藏替换", "Common.UI.SearchDialog.txtBtnReplace": "替换", "Common.UI.SearchDialog.txtBtnReplaceAll": "全部替换", "Common.UI.SynchronizeTip.textDontShow": "不要再显示此消息", "Common.UI.SynchronizeTip.textGotIt": "知道了", "Common.UI.SynchronizeTip.textSynchronize": "文档已被其他用户更改<br>请单击保存更改并重新加载更新。", "Common.UI.ThemeColorPalette.textRecentColors": "最近使用的颜色", "Common.UI.ThemeColorPalette.textStandartColors": "标准颜色", "Common.UI.ThemeColorPalette.textThemeColors": "主题颜色", "Common.UI.Themes.txtThemeClassicLight": "经典浅色", "Common.UI.Themes.txtThemeContrastDark": "高对比度深色", "Common.UI.Themes.txtThemeDark": "深色", "Common.UI.Themes.txtThemeGray": "灰色", "Common.UI.Themes.txtThemeLight": "浅色", "Common.UI.Themes.txtThemeSystem": "和系統一致", "Common.UI.Window.cancelButtonText": "取消", "Common.UI.Window.closeButtonText": "关闭", "Common.UI.Window.noButtonText": "不", "Common.UI.Window.okButtonText": "确定", "Common.UI.Window.textConfirmation": "确认", "Common.UI.Window.textDontShow": "不要再显示此消息", "Common.UI.Window.textError": "错误", "Common.UI.Window.textInformation": "信息", "Common.UI.Window.textWarning": "警告", "Common.UI.Window.yesButtonText": "是", "Common.Utils.Metric.txtCm": "厘米", "Common.Utils.Metric.txtPt": "点", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": "，", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "转移", "Common.Utils.ThemeColor.txtaccent": "重点色", "Common.Utils.ThemeColor.txtAqua": "湖绿色", "Common.Utils.ThemeColor.txtbackground": "背景", "Common.Utils.ThemeColor.txtBlack": "黑色", "Common.Utils.ThemeColor.txtBlue": "蓝色", "Common.Utils.ThemeColor.txtBrightGreen": "明亮绿色", "Common.Utils.ThemeColor.txtBrown": "棕色", "Common.Utils.ThemeColor.txtDarkBlue": "深蓝色", "Common.Utils.ThemeColor.txtDarker": "较深色的", "Common.Utils.ThemeColor.txtDarkGray": "深灰色", "Common.Utils.ThemeColor.txtDarkGreen": "深绿色", "Common.Utils.ThemeColor.txtDarkPurple": "深紫色", "Common.Utils.ThemeColor.txtDarkRed": "深红色", "Common.Utils.ThemeColor.txtDarkTeal": "深青色", "Common.Utils.ThemeColor.txtDarkYellow": "深黃色", "Common.Utils.ThemeColor.txtGold": "金色", "Common.Utils.ThemeColor.txtGray": "灰色", "Common.Utils.ThemeColor.txtGreen": "绿色", "Common.Utils.ThemeColor.txtIndigo": "靛蓝色", "Common.Utils.ThemeColor.txtLavender": "薰衣草色", "Common.Utils.ThemeColor.txtLightBlue": "浅蓝色", "Common.Utils.ThemeColor.txtLighter": "较浅色的", "Common.Utils.ThemeColor.txtLightGray": "浅灰色", "Common.Utils.ThemeColor.txtLightGreen": "浅绿色", "Common.Utils.ThemeColor.txtLightOrange": "浅橙色", "Common.Utils.ThemeColor.txtLightYellow": "浅黄色", "Common.Utils.ThemeColor.txtOrange": "橙色", "Common.Utils.ThemeColor.txtPink": "粉红色", "Common.Utils.ThemeColor.txtPurple": "紫色", "Common.Utils.ThemeColor.txtRed": "红色", "Common.Utils.ThemeColor.txtRose": "玫瑰色", "Common.Utils.ThemeColor.txtSkyBlue": "天蓝色", "Common.Utils.ThemeColor.txtTeal": "青色", "Common.Utils.ThemeColor.txttext": "文字", "Common.Utils.ThemeColor.txtTurquosie": "绿松石", "Common.Utils.ThemeColor.txtViolet": "紫罗兰色", "Common.Utils.ThemeColor.txtWhite": "白色", "Common.Utils.ThemeColor.txtYellow": "黃色", "Common.Views.About.txtAddress": "地址:", "Common.Views.About.txtLicensee": "被许可人", "Common.Views.About.txtLicensor": "许可商", "Common.Views.About.txtMail": "电子邮件：", "Common.Views.About.txtPoweredBy": "技术支持方", "Common.Views.About.txtTel": "电话：", "Common.Views.About.txtVersion": "版本", "Common.Views.AutoCorrectDialog.textAdd": "添加", "Common.Views.AutoCorrectDialog.textApplyAsWork": "工作时套用", "Common.Views.AutoCorrectDialog.textAutoCorrect": "自动更正", "Common.Views.AutoCorrectDialog.textAutoFormat": "键入时自动套用格式", "Common.Views.AutoCorrectDialog.textBy": "根据", "Common.Views.AutoCorrectDialog.textDelete": "刪除", "Common.Views.AutoCorrectDialog.textHyperlink": "具有超链接的网络路径", "Common.Views.AutoCorrectDialog.textMathCorrect": "数学自动更正", "Common.Views.AutoCorrectDialog.textNewRowCol": "包含新的行和列到表格中", "Common.Views.AutoCorrectDialog.textRecognized": "可识别函数", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "以下表达式是可识别的数学表达式。它们不会自动斜体显示。", "Common.Views.AutoCorrectDialog.textReplace": "替换", "Common.Views.AutoCorrectDialog.textReplaceText": "键入时替换", "Common.Views.AutoCorrectDialog.textReplaceType": "键入时替换文本", "Common.Views.AutoCorrectDialog.textReset": "重置", "Common.Views.AutoCorrectDialog.textResetAll": "重置为默认", "Common.Views.AutoCorrectDialog.textRestore": "恢复", "Common.Views.AutoCorrectDialog.textTitle": "自动更正", "Common.Views.AutoCorrectDialog.textWarnAddRec": "可识别函数只能包含字母 A 到 Z，大写或小写。", "Common.Views.AutoCorrectDialog.textWarnResetRec": "您添加的任何表达式都将被移除，已移除的表达式也将被还原。您想要继续吗？", "Common.Views.AutoCorrectDialog.warnReplace": "%1 的自动更正项已存在。您想要替换它吗？", "Common.Views.AutoCorrectDialog.warnReset": "您添加的所有自动更正项都将被移除，更改过的内容将还原为其原始值。您想要继续吗？", "Common.Views.AutoCorrectDialog.warnRestore": "%1 的自动更正项将重置为其原始值。您想要继续吗？", "Common.Views.Chat.textChat": "聊天", "Common.Views.Chat.textClosePanel": "关闭聊天", "Common.Views.Chat.textEnterMessage": "在这里输入你的信息", "Common.Views.Chat.textSend": "发送", "Common.Views.Comments.mniAuthorAsc": "作者 A到Z", "Common.Views.Comments.mniAuthorDesc": "作者 Z到A", "Common.Views.Comments.mniDateAsc": "最旧的", "Common.Views.Comments.mniDateDesc": "最新的", "Common.Views.Comments.mniFilterGroups": "按组筛选", "Common.Views.Comments.mniPositionAsc": "从顶部", "Common.Views.Comments.mniPositionDesc": "从底部", "Common.Views.Comments.textAdd": "添加", "Common.Views.Comments.textAddComment": "添加批注", "Common.Views.Comments.textAddCommentToDoc": "向文档添加批注", "Common.Views.Comments.textAddReply": "添加回复", "Common.Views.Comments.textAll": "全部", "Common.Views.Comments.textAnonym": "访客", "Common.Views.Comments.textCancel": "取消", "Common.Views.Comments.textClose": "关闭", "Common.Views.Comments.textClosePanel": "关闭批注", "Common.Views.Comments.textComment": "批注", "Common.Views.Comments.textComments": "批注", "Common.Views.Comments.textEdit": "确定", "Common.Views.Comments.textEnterCommentHint": "在这里输入您的批注", "Common.Views.Comments.textHintAddComment": "添加批注", "Common.Views.Comments.textOpenAgain": "再次打开", "Common.Views.Comments.textReply": "回复", "Common.Views.Comments.textResolve": "解决", "Common.Views.Comments.textResolved": "已解决", "Common.Views.Comments.textSort": "排序批注", "Common.Views.Comments.textSortFilter": "排序和过滤批注", "Common.Views.Comments.textSortFilterMore": "排序、过滤、以及更多", "Common.Views.Comments.textSortMore": "排序以及更多", "Common.Views.Comments.textViewResolved": "您无权重新打开批注", "Common.Views.Comments.txtEmpty": "工作表中没有注释。", "Common.Views.CopyWarningDialog.textDontShow": "不要再显示此消息", "Common.Views.CopyWarningDialog.textMsg": "使用编辑器工具栏按钮和右键快捷菜单进行的复制，剪切和粘贴操作将仅在此编辑器选项卡中执行。<br> <br>要在编辑器选项卡之外复制或粘贴到应用程序，请使用以下键盘组合：", "Common.Views.CopyWarningDialog.textTitle": "复制，剪切和粘贴操作", "Common.Views.CopyWarningDialog.textToCopy": "用于复制", "Common.Views.CopyWarningDialog.textToCut": "用于剪切", "Common.Views.CopyWarningDialog.textToPaste": "用于粘贴", "Common.Views.CustomizeQuickAccessDialog.textDownload": "下载", "Common.Views.CustomizeQuickAccessDialog.textMsg": "请检查显示在快速访问工具栏上的命令", "Common.Views.CustomizeQuickAccessDialog.textPrint": "打印", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "快速打印", "Common.Views.CustomizeQuickAccessDialog.textRedo": "重做", "Common.Views.CustomizeQuickAccessDialog.textSave": "保存", "Common.Views.CustomizeQuickAccessDialog.textTitle": "自定义快速访问", "Common.Views.CustomizeQuickAccessDialog.textUndo": "撤销", "Common.Views.DocumentAccessDialog.textLoading": "加载中…", "Common.Views.DocumentAccessDialog.textTitle": "分享设置", "Common.Views.DocumentPropertyDialog.errorDate": "您可以从日历中选择一个值并将其存储为日期。<br>如果您手动输入一个值，它将被存储为文本。", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "否", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "是", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "属性应该有一个标题", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "标题", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "“是”或“否”", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "日期", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "类型", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "数字", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "提供一个有效的数字", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "文本", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "属性应该有一个值", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "值", "Common.Views.DocumentPropertyDialog.txtTitle": "新文档属性", "Common.Views.Draw.hintEraser": "橡皮擦", "Common.Views.Draw.hintSelect": "请选择", "Common.Views.Draw.txtEraser": "橡皮擦", "Common.Views.Draw.txtHighlighter": "荧光笔", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "笔", "Common.Views.Draw.txtSelect": "请选择", "Common.Views.Draw.txtSize": "粗细", "Common.Views.EditNameDialog.textLabel": "标签：", "Common.Views.EditNameDialog.textLabelError": "标签不能为空。", "Common.Views.Header.ariaQuickAccessToolbar": "快速访问工具栏", "Common.Views.Header.labelCoUsersDescr": "以下用户正在编辑文件：", "Common.Views.Header.textAddFavorite": "收藏", "Common.Views.Header.textAdvSettings": "高级设置", "Common.Views.Header.textBack": "打开文件所在位置", "Common.Views.Header.textClose": "关闭文件", "Common.Views.Header.textCompactView": "隐藏工具栏", "Common.Views.Header.textHideLines": "隐藏标尺", "Common.Views.Header.textHideStatusBar": "合并工作表和状态栏", "Common.Views.Header.textPrint": "打印", "Common.Views.Header.textReadOnly": "只读", "Common.Views.Header.textRemoveFavorite": "从收藏夹中删除", "Common.Views.Header.textSaveBegin": "保存中...", "Common.Views.Header.textSaveChanged": "已修改", "Common.Views.Header.textSaveEnd": "所有更改已保存", "Common.Views.Header.textSaveExpander": "所有更改已保存", "Common.Views.Header.textShare": "分享", "Common.Views.Header.textZoom": "缩放", "Common.Views.Header.tipAccessRights": "管理文档访问权限", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "自定义快速访问工具栏", "Common.Views.Header.tipDownload": "下载文件", "Common.Views.Header.tipGoEdit": "编辑当前文件", "Common.Views.Header.tipPrint": "打印文件", "Common.Views.Header.tipPrintQuick": "快速打印", "Common.Views.Header.tipRedo": "重做", "Common.Views.Header.tipSave": "保存", "Common.Views.Header.tipSearch": "查找", "Common.Views.Header.tipUndo": "撤消", "Common.Views.Header.tipUndock": "移到单独的窗口中", "Common.Views.Header.tipUsers": "查看用户", "Common.Views.Header.tipViewSettings": "视图设置", "Common.Views.Header.tipViewUsers": "查看用户和管理文档访问权限", "Common.Views.Header.txtAccessRights": "更改访问权限", "Common.Views.Header.txtRename": "重新命名", "Common.Views.History.textCloseHistory": "关闭历史记录", "Common.Views.History.textHideAll": "隐藏详细的更改", "Common.Views.History.textHighlightDeleted": "突出显示已删除的内容", "Common.Views.History.textMore": "更多", "Common.Views.History.textRestore": "恢复", "Common.Views.History.textShowAll": "显示详细的更改", "Common.Views.History.textVer": "版本", "Common.Views.History.textVersionHistory": "版本历史", "Common.Views.ImageFromUrlDialog.textUrl": "粘贴图片URL网址：", "Common.Views.ImageFromUrlDialog.txtEmpty": "这是必填栏", "Common.Views.ImageFromUrlDialog.txtNotUrl": "该字段应该是“http://www.example.com”格式的URL", "Common.Views.ListSettingsDialog.textBulleted": "项目符号", "Common.Views.ListSettingsDialog.textFromFile": "从文件", "Common.Views.ListSettingsDialog.textFromStorage": "来自存储设备", "Common.Views.ListSettingsDialog.textFromUrl": "来自URL", "Common.Views.ListSettingsDialog.textNumbering": "已编号", "Common.Views.ListSettingsDialog.textSelect": "选择自", "Common.Views.ListSettingsDialog.tipChange": "更改项目符号", "Common.Views.ListSettingsDialog.txtBullet": "项目符号", "Common.Views.ListSettingsDialog.txtColor": "颜色", "Common.Views.ListSettingsDialog.txtImage": "图片", "Common.Views.ListSettingsDialog.txtImport": "导入", "Common.Views.ListSettingsDialog.txtNewBullet": "新项目符号", "Common.Views.ListSettingsDialog.txtNewImage": "新图像", "Common.Views.ListSettingsDialog.txtNone": "无", "Common.Views.ListSettingsDialog.txtOfText": "文字的百分比", "Common.Views.ListSettingsDialog.txtSize": "大小", "Common.Views.ListSettingsDialog.txtStart": "开始于", "Common.Views.ListSettingsDialog.txtSymbol": "符号", "Common.Views.ListSettingsDialog.txtTitle": "列表设置", "Common.Views.ListSettingsDialog.txtType": "类型", "Common.Views.MacrosDialog.textCopy": "复制", "Common.Views.MacrosDialog.textCustomFunction": "自定义函数", "Common.Views.MacrosDialog.textDelete": "删除", "Common.Views.MacrosDialog.textLoading": "加载中…", "Common.Views.MacrosDialog.textMacros": "宏", "Common.Views.MacrosDialog.textMakeAutostart": "自启动", "Common.Views.MacrosDialog.textRename": "重命名", "Common.Views.MacrosDialog.textRun": "运行", "Common.Views.MacrosDialog.textSave": "保存", "Common.Views.MacrosDialog.textTitle": "宏", "Common.Views.MacrosDialog.textUnMakeAutostart": "取消自启动", "Common.Views.MacrosDialog.tipFunctionAdd": "添加自定义函数", "Common.Views.MacrosDialog.tipMacrosAdd": "添加宏", "Common.Views.MacrosDialog.tipMacrosRun": "运行", "Common.Views.OpenDialog.closeButtonText": "关闭文件", "Common.Views.OpenDialog.textInvalidRange": "无效的单元格范围", "Common.Views.OpenDialog.textSelectData": "选择数据", "Common.Views.OpenDialog.txtAdvanced": "高级", "Common.Views.OpenDialog.txtColon": "冒号", "Common.Views.OpenDialog.txtComma": "逗号", "Common.Views.OpenDialog.txtDelimiter": "定界符", "Common.Views.OpenDialog.txtDestData": "选择数据的放置位置", "Common.Views.OpenDialog.txtEmpty": "这是必填栏", "Common.Views.OpenDialog.txtEncoding": "编码", "Common.Views.OpenDialog.txtIncorrectPwd": "密码不正确。", "Common.Views.OpenDialog.txtOpenFile": "输入密码来打开文件", "Common.Views.OpenDialog.txtOther": "其他", "Common.Views.OpenDialog.txtPassword": "密码", "Common.Views.OpenDialog.txtPreview": "预览", "Common.Views.OpenDialog.txtProtected": "输入密码并打开文件后，将重置文件的当前密码。", "Common.Views.OpenDialog.txtSemicolon": "分号", "Common.Views.OpenDialog.txtSpace": "空格", "Common.Views.OpenDialog.txtTab": "标签", "Common.Views.OpenDialog.txtTitle": "选择%1个选项", "Common.Views.OpenDialog.txtTitleProtected": "受保护的文件", "Common.Views.PasswordDialog.txtDescription": "设置密码以保护此文档", "Common.Views.PasswordDialog.txtIncorrectPwd": "确认密码不相同", "Common.Views.PasswordDialog.txtPassword": "密码", "Common.Views.PasswordDialog.txtRepeat": "重复密码", "Common.Views.PasswordDialog.txtTitle": "设置密码", "Common.Views.PasswordDialog.txtWarning": "警告：如果您丢失或忘记了密码，则无法恢复。请把它放在安全的地方。", "Common.Views.PluginDlg.textLoading": "载入中", "Common.Views.PluginPanel.textClosePanel": "关闭插件", "Common.Views.PluginPanel.textLoading": "载入中", "Common.Views.Plugins.groupCaption": "插件", "Common.Views.Plugins.strPlugins": "插件", "Common.Views.Plugins.textBackgroundPlugins": "后台插件", "Common.Views.Plugins.textSettings": "设置", "Common.Views.Plugins.textStart": "开始", "Common.Views.Plugins.textStop": "停止", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "后台插件列表", "Common.Views.Plugins.tipMore": "更多", "Common.Views.Protection.hintAddPwd": "使用密码加密电子表格", "Common.Views.Protection.hintDelPwd": "删除密码", "Common.Views.Protection.hintPwd": "更改或删除密码", "Common.Views.Protection.hintSignature": "添加数字签名或签名栏", "Common.Views.Protection.txtAddPwd": "添加密码", "Common.Views.Protection.txtChangePwd": "修改密码", "Common.Views.Protection.txtDeletePwd": "删除密码", "Common.Views.Protection.txtEncrypt": "加密", "Common.Views.Protection.txtInvisibleSignature": "添加数字签名", "Common.Views.Protection.txtSignature": "签名", "Common.Views.Protection.txtSignatureLine": "添加签名栏", "Common.Views.RecentFiles.txtOpenRecent": "打开最近", "Common.Views.RenameDialog.textName": "文件名", "Common.Views.RenameDialog.txtInvalidName": "文件名不能包含以下任何字符：", "Common.Views.ReviewChanges.hintNext": "跳转到下一处更改", "Common.Views.ReviewChanges.hintPrev": "跳转到上一处更改", "Common.Views.ReviewChanges.strFast": "快速", "Common.Views.ReviewChanges.strFastDesc": "实时共同编辑。所有更改都将自动保存。", "Common.Views.ReviewChanges.strStrict": "严格", "Common.Views.ReviewChanges.strStrictDesc": "使用“保存”按钮同步您和其他人所做的更改。", "Common.Views.ReviewChanges.tipAcceptCurrent": "同意当前更改", "Common.Views.ReviewChanges.tipCoAuthMode": "设置协同编辑模式", "Common.Views.ReviewChanges.tipCommentRem": "删除批注", "Common.Views.ReviewChanges.tipCommentRemCurrent": "删除当前批注", "Common.Views.ReviewChanges.tipCommentResolve": "标记批注为已解决", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "将批注标记为已解决", "Common.Views.ReviewChanges.tipHistory": "显示版本历史", "Common.Views.ReviewChanges.tipRejectCurrent": "否决当前更改", "Common.Views.ReviewChanges.tipReview": "跟踪更改", "Common.Views.ReviewChanges.tipReviewView": "选择要显示更改的模式", "Common.Views.ReviewChanges.tipSetDocLang": "设置文档语言", "Common.Views.ReviewChanges.tipSetSpelling": "拼写检查", "Common.Views.ReviewChanges.tipSharing": "管理文档访问权限", "Common.Views.ReviewChanges.txtAccept": "同意", "Common.Views.ReviewChanges.txtAcceptAll": "同意所有更改", "Common.Views.ReviewChanges.txtAcceptChanges": "同意更改", "Common.Views.ReviewChanges.txtAcceptCurrent": "同意当前更改", "Common.Views.ReviewChanges.txtChat": "聊天", "Common.Views.ReviewChanges.txtClose": "关闭", "Common.Views.ReviewChanges.txtCoAuthMode": "共同编辑模式", "Common.Views.ReviewChanges.txtCommentRemAll": "删除所有批注", "Common.Views.ReviewChanges.txtCommentRemCurrent": "删除当前批注", "Common.Views.ReviewChanges.txtCommentRemMy": "删除我的批注", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "删除我当前的批注", "Common.Views.ReviewChanges.txtCommentRemove": "刪除", "Common.Views.ReviewChanges.txtCommentResolve": "解决", "Common.Views.ReviewChanges.txtCommentResolveAll": "将所有批注标记为已解决", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "将当前批注标记为已解决", "Common.Views.ReviewChanges.txtCommentResolveMy": "将自己的注释标记为已解决", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "将自己当前的注释标记为已解决", "Common.Views.ReviewChanges.txtDocLang": "语言", "Common.Views.ReviewChanges.txtFinal": "同意所有更改 (预览)", "Common.Views.ReviewChanges.txtFinalCap": "最终状态", "Common.Views.ReviewChanges.txtHistory": "版本历史", "Common.Views.ReviewChanges.txtMarkup": "所有更改 (编辑)", "Common.Views.ReviewChanges.txtMarkupCap": "标记", "Common.Views.ReviewChanges.txtNext": "下一页", "Common.Views.ReviewChanges.txtOriginal": "否决所有更改 (预览)", "Common.Views.ReviewChanges.txtOriginalCap": "原始状态", "Common.Views.ReviewChanges.txtPrev": "上一个", "Common.Views.ReviewChanges.txtReject": "否决", "Common.Views.ReviewChanges.txtRejectAll": "否决所有更改", "Common.Views.ReviewChanges.txtRejectChanges": "否决更改", "Common.Views.ReviewChanges.txtRejectCurrent": "否决当前更改", "Common.Views.ReviewChanges.txtSharing": "分享", "Common.Views.ReviewChanges.txtSpelling": "拼写检查", "Common.Views.ReviewChanges.txtTurnon": "跟踪更改", "Common.Views.ReviewChanges.txtView": "显示模式", "Common.Views.ReviewPopover.textAdd": "添加", "Common.Views.ReviewPopover.textAddReply": "添加回复", "Common.Views.ReviewPopover.textCancel": "取消", "Common.Views.ReviewPopover.textClose": "关闭", "Common.Views.ReviewPopover.textComment": "批注", "Common.Views.ReviewPopover.textEdit": "确定", "Common.Views.ReviewPopover.textEnterComment": "在这里输入您的批注", "Common.Views.ReviewPopover.textMention": "+提及将提供对文档的访问权限并发送电子邮件", "Common.Views.ReviewPopover.textMentionNotify": "+提及将通过电子邮件通知用户", "Common.Views.ReviewPopover.textOpenAgain": "再次打开", "Common.Views.ReviewPopover.textReply": "回复", "Common.Views.ReviewPopover.textResolve": "解决", "Common.Views.ReviewPopover.textViewResolved": "您无权重新打开批注", "Common.Views.ReviewPopover.txtDeleteTip": "刪除", "Common.Views.ReviewPopover.txtEditTip": "编辑", "Common.Views.SaveAsDlg.textLoading": "载入中", "Common.Views.SaveAsDlg.textTitle": "要保存的文件夹", "Common.Views.SearchPanel.textByColumns": "按列", "Common.Views.SearchPanel.textByRows": "按行", "Common.Views.SearchPanel.textCaseSensitive": "区分大小写", "Common.Views.SearchPanel.textCell": "单元格", "Common.Views.SearchPanel.textCloseSearch": "关闭搜索", "Common.Views.SearchPanel.textContentChanged": "文件已更改。", "Common.Views.SearchPanel.textFind": "查找", "Common.Views.SearchPanel.textFindAndReplace": "查找和替换", "Common.Views.SearchPanel.textFormula": "公式", "Common.Views.SearchPanel.textFormulas": "公式", "Common.Views.SearchPanel.textItemEntireCell": "整个单元格内容", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "｛0｝个项目已成功替换。", "Common.Views.SearchPanel.textLookIn": "查询", "Common.Views.SearchPanel.textMatchUsingRegExp": "使用正则表达式匹配", "Common.Views.SearchPanel.textName": "名称", "Common.Views.SearchPanel.textNoMatches": "找不到匹配信息", "Common.Views.SearchPanel.textNoSearchResults": "没有搜索结果", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "已替换｛0｝/｛1｝项。其余｛2｝个项目已被其他用户锁定。", "Common.Views.SearchPanel.textReplace": "替换", "Common.Views.SearchPanel.textReplaceAll": "全部替换", "Common.Views.SearchPanel.textReplaceWith": "替换为", "Common.Views.SearchPanel.textSearch": "搜索", "Common.Views.SearchPanel.textSearchAgain": "{0}执行新的搜索{1}以获得准确的结果。", "Common.Views.SearchPanel.textSearchHasStopped": "搜索已停止", "Common.Views.SearchPanel.textSearchOptions": "搜索选项", "Common.Views.SearchPanel.textSearchResults": "搜索结果：｛0｝/｛1｝", "Common.Views.SearchPanel.textSearchResultsTable": "搜索结果", "Common.Views.SearchPanel.textSelectDataRange": "选择数据范围", "Common.Views.SearchPanel.textSheet": "工作表", "Common.Views.SearchPanel.textSpecificRange": "特定范围", "Common.Views.SearchPanel.textTooManyResults": "此处显示的结果太多", "Common.Views.SearchPanel.textValue": "值", "Common.Views.SearchPanel.textValues": "值", "Common.Views.SearchPanel.textWholeWords": "仅限完整单词", "Common.Views.SearchPanel.textWithin": "里边", "Common.Views.SearchPanel.textWorkbook": "工作簿", "Common.Views.SearchPanel.tipNextResult": "下一个结果", "Common.Views.SearchPanel.tipPreviousResult": "上一个结果", "Common.Views.SelectFileDlg.textLoading": "载入中", "Common.Views.SelectFileDlg.textTitle": "选择数据源", "Common.Views.ShapeShadowDialog.txtAngle": "角度", "Common.Views.ShapeShadowDialog.txtDistance": "距离", "Common.Views.ShapeShadowDialog.txtSize": "大小", "Common.Views.ShapeShadowDialog.txtTitle": "调整阴影", "Common.Views.ShapeShadowDialog.txtTransparency": "透明度", "Common.Views.SignDialog.textBold": "粗体", "Common.Views.SignDialog.textCertificate": "证书", "Common.Views.SignDialog.textChange": "修改", "Common.Views.SignDialog.textInputName": "输入签名者姓名", "Common.Views.SignDialog.textItalic": "斜体", "Common.Views.SignDialog.textNameError": "签名人姓名不能为空。", "Common.Views.SignDialog.textPurpose": "签署本文件的目的", "Common.Views.SignDialog.textSelect": "请选择", "Common.Views.SignDialog.textSelectImage": "选择图像", "Common.Views.SignDialog.textSignature": "签名外观如下", "Common.Views.SignDialog.textTitle": "签署文件", "Common.Views.SignDialog.textUseImage": "或单击“选择图像”，使用图片作为签名", "Common.Views.SignDialog.textValid": "有效期从%1到%2", "Common.Views.SignDialog.tipFontName": "字体名称", "Common.Views.SignDialog.tipFontSize": "字体大小", "Common.Views.SignSettingsDialog.textAllowComment": "允许签名者在签名对话框中添加批注", "Common.Views.SignSettingsDialog.textDefInstruction": "在签署此文档之前，请验证您正在签署的内容是否正确。", "Common.Views.SignSettingsDialog.textInfoEmail": "建议签署人的电子邮件", "Common.Views.SignSettingsDialog.textInfoName": "建议签署人", "Common.Views.SignSettingsDialog.textInfoTitle": "建议签署人称谓", "Common.Views.SignSettingsDialog.textInstructions": "签名人须知", "Common.Views.SignSettingsDialog.textShowDate": "在签名行中显示签名日期", "Common.Views.SignSettingsDialog.textTitle": "签名设置", "Common.Views.SignSettingsDialog.txtEmpty": "这是必填栏", "Common.Views.SymbolTableDialog.textCharacter": "字符", "Common.Views.SymbolTableDialog.textCode": "Unicode十六进制值", "Common.Views.SymbolTableDialog.textCopyright": "版权符号", "Common.Views.SymbolTableDialog.textDCQuote": "结束双引号", "Common.Views.SymbolTableDialog.textDOQuote": "开头双引号", "Common.Views.SymbolTableDialog.textEllipsis": "水平椭圆", "Common.Views.SymbolTableDialog.textEmDash": "破折号", "Common.Views.SymbolTableDialog.textEmSpace": "空格", "Common.Views.SymbolTableDialog.textEnDash": "虚线", "Common.Views.SymbolTableDialog.textEnSpace": "半形空格", "Common.Views.SymbolTableDialog.textFont": "字体 ", "Common.Views.SymbolTableDialog.textNBHyphen": "不可分连字符", "Common.Views.SymbolTableDialog.textNBSpace": "不换行空格", "Common.Views.SymbolTableDialog.textPilcrow": "段落符号", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 字宽空白", "Common.Views.SymbolTableDialog.textRange": "范围", "Common.Views.SymbolTableDialog.textRecent": "最近使用的符号", "Common.Views.SymbolTableDialog.textRegistered": "注册标志", "Common.Views.SymbolTableDialog.textSCQuote": "结束单引号", "Common.Views.SymbolTableDialog.textSection": "章节标志", "Common.Views.SymbolTableDialog.textShortcut": "快捷键", "Common.Views.SymbolTableDialog.textSHyphen": "软连字号", "Common.Views.SymbolTableDialog.textSOQuote": "开始单引号", "Common.Views.SymbolTableDialog.textSpecial": "特殊字符", "Common.Views.SymbolTableDialog.textSymbols": "符号", "Common.Views.SymbolTableDialog.textTitle": "符号", "Common.Views.SymbolTableDialog.textTradeMark": "商标符号", "Common.Views.UserNameDialog.textDontShow": "不要再次询问我", "Common.Views.UserNameDialog.textLabel": "标签：", "Common.Views.UserNameDialog.textLabelError": "标签不能为空。", "SSE.Controllers.DataTab.strSheet": "工作表", "SSE.Controllers.DataTab.textAddExternalData": "已添加到外部源的链接。您可以在“数据”选项卡中更新此类链接。", "SSE.Controllers.DataTab.textColumns": "列", "SSE.Controllers.DataTab.textContinue": "继续", "SSE.Controllers.DataTab.textDontUpdate": "不要更新", "SSE.Controllers.DataTab.textEmptyUrl": "你必须指定URL", "SSE.Controllers.DataTab.textRows": "行", "SSE.Controllers.DataTab.textTurnOff": "关闭自动更新", "SSE.Controllers.DataTab.textUpdate": "更新", "SSE.Controllers.DataTab.textWizard": "分列", "SSE.Controllers.DataTab.txtDataValidation": "数据验证", "SSE.Controllers.DataTab.txtErrorExternalLink": "错误：更新失败", "SSE.Controllers.DataTab.txtExpand": "展开", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "选择范围相邻的数据将不会被移除。您想要扩大选择范围以包含相邻的数据，还是继续仅移除当前选中的单元格？", "SSE.Controllers.DataTab.txtExtendDataValidation": "选择范围中包含了没有“数据验证”设置的单元格<br>您想要将数据验证扩展到这些单元格吗？", "SSE.Controllers.DataTab.txtImportWizard": "文本导入向导", "SSE.Controllers.DataTab.txtRemDuplicates": "删除重复项", "SSE.Controllers.DataTab.txtRemoveDataValidation": "所选内容包含多个验证类型<br>是否清除当前设置并继续？", "SSE.Controllers.DataTab.txtRemSelected": "在选定的中删除", "SSE.Controllers.DataTab.txtUrlTitle": "粘贴数据URL", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "此工作簿包含自动更新的外部源链接。这可能不安全。<br><br>如果您信任，请按继续。", "SSE.Controllers.DataTab.warnUpdateExternalData": "此工作簿包含指向一个或多个可能不安全的外部源的链接<br>如果您信任这些链接，请更新它们以获取最新数据。", "SSE.Controllers.DocumentHolder.alignmentText": "对齐", "SSE.Controllers.DocumentHolder.centerText": "中心", "SSE.Controllers.DocumentHolder.deleteColumnText": "删除列", "SSE.Controllers.DocumentHolder.deleteRowText": "删除行", "SSE.Controllers.DocumentHolder.deleteText": "删除", "SSE.Controllers.DocumentHolder.errorInvalidLink": "链接引用不存在。请更正链接或删除。", "SSE.Controllers.DocumentHolder.guestText": "访客", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "左栏", "SSE.Controllers.DocumentHolder.insertColumnRightText": "右栏", "SSE.Controllers.DocumentHolder.insertRowAboveText": "上面的行", "SSE.Controllers.DocumentHolder.insertRowBelowText": "下面的行", "SSE.Controllers.DocumentHolder.insertText": "插入", "SSE.Controllers.DocumentHolder.leftText": "左侧", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "警告", "SSE.Controllers.DocumentHolder.rightText": "右", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "自动更正选项", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "列宽{0}符号（{1}像素）", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "行高{0}分（{1}个像素）", "SSE.Controllers.DocumentHolder.textCtrlClick": "按CTRL并单击链接", "SSE.Controllers.DocumentHolder.textInsertLeft": "在左侧插入列", "SSE.Controllers.DocumentHolder.textInsertTop": "在上方插入行", "SSE.Controllers.DocumentHolder.textPasteSpecial": "选择性粘贴", "SSE.Controllers.DocumentHolder.textStopExpand": "停止自动展开表格", "SSE.Controllers.DocumentHolder.textSym": "符号", "SSE.Controllers.DocumentHolder.tipIsLocked": "此元素正在被其他用户编辑。", "SSE.Controllers.DocumentHolder.txtAboveAve": "平均水平以上", "SSE.Controllers.DocumentHolder.txtAddBottom": "添加底部边框", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "添加分数栏", "SSE.Controllers.DocumentHolder.txtAddHor": "添加水平线", "SSE.Controllers.DocumentHolder.txtAddLB": "添加左底边框", "SSE.Controllers.DocumentHolder.txtAddLeft": "添加左边框", "SSE.Controllers.DocumentHolder.txtAddLT": "添加左上顶行", "SSE.Controllers.DocumentHolder.txtAddRight": "添加右边框", "SSE.Controllers.DocumentHolder.txtAddTop": "添加上边框", "SSE.Controllers.DocumentHolder.txtAddVer": "添加垂直线", "SSE.Controllers.DocumentHolder.txtAlignToChar": "字符对齐", "SSE.Controllers.DocumentHolder.txtAll": "(全部)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "返回表或指定表列的全部内容，包括列标题、数据和汇总行", "SSE.Controllers.DocumentHolder.txtAnd": "和", "SSE.Controllers.DocumentHolder.txtBegins": "开头为", "SSE.Controllers.DocumentHolder.txtBelowAve": "在平均值以下", "SSE.Controllers.DocumentHolder.txtBlanks": "(空白)", "SSE.Controllers.DocumentHolder.txtBorderProps": "边框属性", "SSE.Controllers.DocumentHolder.txtBottom": "底部", "SSE.Controllers.DocumentHolder.txtByField": "%2的%1", "SSE.Controllers.DocumentHolder.txtColumn": "列", "SSE.Controllers.DocumentHolder.txtColumnAlign": "列对齐", "SSE.Controllers.DocumentHolder.txtContains": "包含", "SSE.Controllers.DocumentHolder.txtCopySuccess": "链接已复制到剪贴板", "SSE.Controllers.DocumentHolder.txtDataTableHint": "返回表格或指定表格列的数据单元格", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "减少参数大小", "SSE.Controllers.DocumentHolder.txtDeleteArg": "删除参数", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "删除手动的断点", "SSE.Controllers.DocumentHolder.txtDeleteChars": "删除封闭字符", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "删除封闭字符和分隔符", "SSE.Controllers.DocumentHolder.txtDeleteEq": "删除方程", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "删除字符", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "刪除根号", "SSE.Controllers.DocumentHolder.txtEnds": "结尾为", "SSE.Controllers.DocumentHolder.txtEquals": "等于", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "等于单元格颜色", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "等于字体颜色", "SSE.Controllers.DocumentHolder.txtExpand": "展开和排序", "SSE.Controllers.DocumentHolder.txtExpandSort": "选择范围相邻的数据将不会被排序。您想要扩大选择范围以包含相邻的数据，还是继续仅对当前选中的单元格进行排序？", "SSE.Controllers.DocumentHolder.txtFilterBottom": "底部", "SSE.Controllers.DocumentHolder.txtFilterTop": "顶部", "SSE.Controllers.DocumentHolder.txtFractionLinear": "改为线性分数", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "改为倾斜分数", "SSE.Controllers.DocumentHolder.txtFractionStacked": "改为堆积分数", "SSE.Controllers.DocumentHolder.txtGreater": "大于", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "大于等于", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "文字上方的字符", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "文字下的字符", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "返回表格或指定表格列的列标题", "SSE.Controllers.DocumentHolder.txtHeight": "\n高度", "SSE.Controllers.DocumentHolder.txtHideBottom": "隐藏底部边框", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "隐藏下限", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "隐藏右括号", "SSE.Controllers.DocumentHolder.txtHideDegree": "隐藏度数", "SSE.Controllers.DocumentHolder.txtHideHor": "隐藏水平线", "SSE.Controllers.DocumentHolder.txtHideLB": "隐藏左底线", "SSE.Controllers.DocumentHolder.txtHideLeft": "隐藏左边框", "SSE.Controllers.DocumentHolder.txtHideLT": "隐藏左顶线", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "隐藏左括号", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "隐藏占位符", "SSE.Controllers.DocumentHolder.txtHideRight": "隐藏右边框", "SSE.Controllers.DocumentHolder.txtHideTop": "隐藏顶部边框", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "隐藏上限", "SSE.Controllers.DocumentHolder.txtHideVer": "隐藏垂直线", "SSE.Controllers.DocumentHolder.txtImportWizard": "文本导入向导", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "增加参数大小", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "在后面插入参数", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "之前插入参数", "SSE.Controllers.DocumentHolder.txtInsertBreak": "插入手动分隔符", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "在之后插入方程式", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "在前面插入方程式", "SSE.Controllers.DocumentHolder.txtItems": "项目", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "仅保留文字", "SSE.Controllers.DocumentHolder.txtLess": "小于", "SSE.Controllers.DocumentHolder.txtLessEquals": "小于或等于", "SSE.Controllers.DocumentHolder.txtLimitChange": "更改界限位置", "SSE.Controllers.DocumentHolder.txtLimitOver": "文字上方限制", "SSE.Controllers.DocumentHolder.txtLimitUnder": "文字下方限制", "SSE.Controllers.DocumentHolder.txtLockSort": "在选择区附近找到数据，但是你没有足够的权限修改这些单元格。<br>你希望继续操作当前选择区吗？", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "括号与其内容的高度对齐", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "矩阵对齐", "SSE.Controllers.DocumentHolder.txtNoChoices": "无法选择要填充单元格的内容。<br>只能选择列中的文本值进行替换。", "SSE.Controllers.DocumentHolder.txtNotBegins": "开头不是", "SSE.Controllers.DocumentHolder.txtNotContains": "不含", "SSE.Controllers.DocumentHolder.txtNotEnds": "不结束于", "SSE.Controllers.DocumentHolder.txtNotEquals": "不等于", "SSE.Controllers.DocumentHolder.txtOr": "或", "SSE.Controllers.DocumentHolder.txtOverbar": "文本上横条", "SSE.Controllers.DocumentHolder.txtPaste": "粘贴", "SSE.Controllers.DocumentHolder.txtPasteBorders": "无框公式", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "公式+列宽", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "目标格式", "SSE.Controllers.DocumentHolder.txtPasteFormat": "仅粘贴格式", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "公式+数字格式", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "仅粘贴公式", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "公式+所有格式", "SSE.Controllers.DocumentHolder.txtPasteLink": "粘贴链接", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "链接图片", "SSE.Controllers.DocumentHolder.txtPasteMerge": "合并条件格式", "SSE.Controllers.DocumentHolder.txtPastePicture": "图片", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "源格式", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "颠倒", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "值+所有格式", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "值+数字格式", "SSE.Controllers.DocumentHolder.txtPasteValues": "仅粘贴值", "SSE.Controllers.DocumentHolder.txtPercent": "百分比", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "恢复表格自动扩展", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "删除分数栏", "SSE.Controllers.DocumentHolder.txtRemLimit": "删除限制", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "删除强调字符", "SSE.Controllers.DocumentHolder.txtRemoveBar": "删除栏", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "您想要移除此签名吗？<br>此操作无法撤销。", "SSE.Controllers.DocumentHolder.txtRemScripts": "删除脚本", "SSE.Controllers.DocumentHolder.txtRemSubscript": "删除下标", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "除去上标", "SSE.Controllers.DocumentHolder.txtRowHeight": "列高", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "文字后的脚本", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "文字前的脚本", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "显示底限", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "显示结束括号", "SSE.Controllers.DocumentHolder.txtShowDegree": "显示度数", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "显示开始括号", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "显示占位符", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "显示上限", "SSE.Controllers.DocumentHolder.txtSorting": "排序中", "SSE.Controllers.DocumentHolder.txtSortSelected": "对所选内容排序", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "延展括号", "SSE.Controllers.DocumentHolder.txtThisRowHint": "仅选择指定列的这一行", "SSE.Controllers.DocumentHolder.txtTop": "顶部", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "返回表格或指定表格列的总行数", "SSE.Controllers.DocumentHolder.txtUnderbar": "文本下方横条", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "撤消表格的自动扩展", "SSE.Controllers.DocumentHolder.txtUseTextImport": "使用文本导入向导", "SSE.Controllers.DocumentHolder.txtWarnUrl": "点击此链接可能对您的设备和数据有害<br>您确定要继续吗？", "SSE.Controllers.DocumentHolder.txtWidth": "宽度", "SSE.Controllers.DocumentHolder.warnFilterError": "“值”区域中至少需要一个字段才能应用值筛选器。", "SSE.Controllers.FormulaDialog.sCategoryAll": "全部", "SSE.Controllers.FormulaDialog.sCategoryCube": "立方体", "SSE.Controllers.FormulaDialog.sCategoryCustom": "自定义", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "数据库", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "日期和时间", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "工程", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "金融", "SSE.Controllers.FormulaDialog.sCategoryInformation": "信息", "SSE.Controllers.FormulaDialog.sCategoryLast10": "最后10个使用", "SSE.Controllers.FormulaDialog.sCategoryLogical": "合乎逻辑", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "查找和参考", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "数学和三角学", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "统计", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "文字和数据", "SSE.Controllers.LeftMenu.newDocumentTitle": "未命名的电子表格", "SSE.Controllers.LeftMenu.textByColumns": "按列", "SSE.Controllers.LeftMenu.textByRows": "按行", "SSE.Controllers.LeftMenu.textFormulas": "公式", "SSE.Controllers.LeftMenu.textItemEntireCell": "整个单元格内容", "SSE.Controllers.LeftMenu.textLoadHistory": "正在加载版本历史记录...", "SSE.Controllers.LeftMenu.textLookin": "查询", "SSE.Controllers.LeftMenu.textNoTextFound": "无法找到您搜索的数据，请调整您的搜索选项。", "SSE.Controllers.LeftMenu.textReplaceSkipped": "替换已完成。 {0}处跳过。", "SSE.Controllers.LeftMenu.textReplaceSuccess": "已完成搜索。已替换的次数：{0}。", "SSE.Controllers.LeftMenu.textSave": "保存", "SSE.Controllers.LeftMenu.textSearch": "搜索", "SSE.Controllers.LeftMenu.textSelectPath": "输入保存文件副本的路径", "SSE.Controllers.LeftMenu.textSheet": "工作表", "SSE.Controllers.LeftMenu.textValues": "值", "SSE.Controllers.LeftMenu.textWarning": "警告", "SSE.Controllers.LeftMenu.textWithin": "里边", "SSE.Controllers.LeftMenu.textWorkbook": "工作簿", "SSE.Controllers.LeftMenu.txtUntitled": "无标题", "SSE.Controllers.LeftMenu.warnDownloadAs": "如果您继续以此格式保存，除文本之外的所有功能将丢失。<br>您确定要继续吗？", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "CSV格式不支持保存多页文件<br>要保留所选格式并仅保存当前工作表，请按save<br>若要保存当前电子表格，请单击“取消”并以其他格式保存。", "SSE.Controllers.Main.confirmAddCellWatches": "此操作将添加 {0} 个单元格监视。<br>您想要继续吗？", "SSE.Controllers.Main.confirmAddCellWatchesMax": "此操作将仅添加 {0} 个单元格监视以节约内存消耗。<br>您想要继续吗？", "SSE.Controllers.Main.confirmMaxChangesSize": "您执行的操作超过了为服务器设置的大小限制<br>按“撤消”取消上次操作，或按“继续”在本地机器继续操作（您需要下载文件或复制其内容以确保不会丢失任何内容）。", "SSE.Controllers.Main.confirmMoveCellRange": "目标单元格范围可以包含数据。继续操作？", "SSE.Controllers.Main.confirmPutMergeRange": "源数据包含合并的单元格。<br>它们在粘贴到表格之前已经被取消了。", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "标题行中的公式将被移除并转换为静态文本<br>您想要继续吗？", "SSE.Controllers.Main.confirmReplaceHFPicture": "标题的每个部分只能插入一张图片<br>按“替换”替换现有图片<br>按“保留”以保留现有图片。", "SSE.Controllers.Main.convertationTimeoutText": "转换超时", "SSE.Controllers.Main.criticalErrorExtText": "按“确定”返回文档列表。", "SSE.Controllers.Main.criticalErrorTitle": "错误", "SSE.Controllers.Main.downloadErrorText": "下载失败", "SSE.Controllers.Main.downloadTextText": "电子表格下载中...", "SSE.Controllers.Main.downloadTitleText": "电子表格下载中", "SSE.Controllers.Main.errNoDuplicates": "未找到重复的值。", "SSE.Controllers.Main.errorAccessDeny": "您正在尝试执行您没有权限的操作。<br>请联系您的文档服务器管理员。", "SSE.Controllers.Main.errorArgsRange": "一个错误的输入公式。< br >使用不正确的参数范围。", "SSE.Controllers.Main.errorAutoFilterChange": "不允许操作，因为它正在尝试在工作表上的表格中移动单元格。", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "无法对所选单元格进行操作，因为您无法移动表格的一部分。<br>选择其他数据范围，以便整个表格被移动并重试。", "SSE.Controllers.Main.errorAutoFilterDataRange": "所选单元格区域无法进行操作。<br>选择与现有单元格不同的统一数据范围，然后重试。", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "无法执行操作，因为该区域包含已过滤的单元格。<br>请取消隐藏已过滤的元素，然后重试。", "SSE.Controllers.Main.errorBadImageUrl": "图片URL地址不正确", "SSE.Controllers.Main.errorCalculatedItemInPageField": "无法添加或修改该项。该字段已在数据透视表的筛选器中。", "SSE.Controllers.Main.errorCannotPasteImg": "我们无法从剪贴板上粘贴这个图片，但您可以把它保存到您的设备上并从那里插入。或者您可以复制不包含文字的图片，然后将它粘贴到电子表格中。", "SSE.Controllers.Main.errorCannotUngroup": "无法取消分组。若要启动大纲，请选择详细信息的行或列，并对其进行分组。", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "不能在受保护的工作表上使用此命令。若要使用此命令，请取消对图纸的保护<br>可能会要求您输入密码。", "SSE.Controllers.Main.errorChangeArray": "您不能更改数组的一部分。", "SSE.Controllers.Main.errorChangeFilteredRange": "这将更改工作表上的筛选区域<br>若要完成此任务，请删除自动筛选器。", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "您试图更改的单元格或图表位于受保护的工作表上<br>若要进行更改，请取消对工作表的保护。可能会要求您输入密码。", "SSE.Controllers.Main.errorCircularReference": "存在一个或多个循环引用，公式直接或间接引用其自己的单元格。<br>尝试删除或更改这些引用，或将公式移动到不同的单元格。", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "服务器连接失败。该文档现在无法编辑", "SSE.Controllers.Main.errorConnectToServer": "无法保存文档。请检查连接设置或与管理员联系<br>单击“确定”按钮时，系统将提示您下载文档。", "SSE.Controllers.Main.errorConvertXml": "文件具有不受支持的格式。<br>只能使用XML Spreadsheet 2003格式。", "SSE.Controllers.Main.errorCopyMultiselectArea": "该命令不能与多个选择一起使用。<br>选择一个范围，然后重试。", "SSE.Controllers.Main.errorCountArg": "一个错误的输入公式。< br >正确使用数量的参数。", "SSE.Controllers.Main.errorCountArgExceed": "一个错误的输入公式。< br >超过数量的参数。", "SSE.Controllers.Main.errorCreateDefName": "当前无法编辑现有的命名范围，也无法创建新的命名范围<br>，因为其中一些正在编辑中。", "SSE.Controllers.Main.errorCreateRange": "当前无法编辑现有范围，也无法创建新范围<br>，因为其中一些范围正在编辑中。", "SSE.Controllers.Main.errorDatabaseConnection": "外部错误。<br>数据库连接错误。如果错误仍然存​​在，请联系支持人员。", "SSE.Controllers.Main.errorDataEncrypted": "已收到加密的更改，无法对其解密。", "SSE.Controllers.Main.errorDataRange": "数据范围不正确", "SSE.Controllers.Main.errorDataValidate": "您输入的值无效<br>用户具有可在此单元格中输入的限制值。", "SSE.Controllers.Main.errorDefaultMessage": "错误代码：%1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "您正试图删除包含锁定单元格的列。在工作表受到保护时，您无法删除锁定的单元格<br>若要删除锁定的单元格，请取消对工作表的保护。可能会要求您输入密码。", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "您正试图删除包含锁定单元格的行。工作表受到保护时，无法删除锁定的单元格<br>若要删除锁定的单元格，请取消对工作表的保护。可能会要求您输入密码。", "SSE.Controllers.Main.errorDependentsNoFormulas": "Trace Dependents命令未找到引用活动单元格的公式。", "SSE.Controllers.Main.errorDirectUrl": "请验证指向文档的链接<br>此链接必须是要下载的文档的直接链接。", "SSE.Controllers.Main.errorEditingDownloadas": "使用文档时出错<br>使用“下载为”选项将文件备份副本保存到驱动器。", "SSE.Controllers.Main.errorEditingSaveas": "使用文档时出错<br>使用“另存为…”选项将文件备份副本保存到驱动器。", "SSE.Controllers.Main.errorEditView": "当前无法编辑现有工作表视图，也无法创建新的工作表视图，因为其中一些工作表视图正在编辑中。", "SSE.Controllers.Main.errorEmailClient": "找不到电子邮件客户端。", "SSE.Controllers.Main.errorFilePassProtect": "该文档受密码保护，无法被打开。", "SSE.Controllers.Main.errorFileRequest": "外部错误。<br>文件请求错误。如果错误仍然存​​在，请与支持部门联系。", "SSE.Controllers.Main.errorFileSizeExceed": "文件大小超出了为服务器设置的限制.<br>有关详细信息，请与文档服务器管理员联系。", "SSE.Controllers.Main.errorFileVKey": "外部错误。<br>安全密钥不正确。如果错误仍然存​​在，请与支持部门联系。", "SSE.Controllers.Main.errorFillRange": "无法填充所选范围的单元格。<br>所有合并的单元格的大小必须相同。", "SSE.Controllers.Main.errorForceSave": "保存文件时出错。请使用“下载为”选项将文件保存到驱动器，或稍后再试。", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "无法在数据透视表报告中输入项目或字段名称的公式。", "SSE.Controllers.Main.errorFormulaName": "一个错误的输入公式。< br >正确使用公式名称。", "SSE.Controllers.Main.errorFormulaParsing": "解析公式时出现内部错误。", "SSE.Controllers.Main.errorFrmlMaxLength": "公式添加失败：公式中字符的长度超出限制。<br>请编辑后重试。", "SSE.Controllers.Main.errorFrmlMaxReference": "无法输入此公式，因为它包含太多值、<br>单元格引用和/或名称。", "SSE.Controllers.Main.errorFrmlMaxTextLength": "公式中的文本值限制为255个字符。<br>使用连接函数或连接运算符（&）。", "SSE.Controllers.Main.errorFrmlWrongReferences": "函数引用不存在的工作表。<br>请检查数据并重试。", "SSE.Controllers.Main.errorFTChangeTableRangeError": "无法完成所选单元格范围的操作<br>选择一个范围，使第一个表行位于同一行<br>，并且生成的表与当前表重叠。", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "所选单元格范围无法完成操作。<br>选择不包括其他表格的范围。", "SSE.Controllers.Main.errorInconsistentExt": "打开文件时出错<br>文件内容与文件扩展名不匹配。", "SSE.Controllers.Main.errorInconsistentExtDocx": "打开文件时出错<br>文件内容对应于文本文档（例如docx），但文件的扩展名不一致：%1。", "SSE.Controllers.Main.errorInconsistentExtPdf": "打开文件时出错<br>文件内容对应于以下格式之一：pdf/djvu/xps/oxfs，但文件的扩展名不一致：%1。", "SSE.Controllers.Main.errorInconsistentExtPptx": "打开文件时出错<br>文件内容对应于演示文稿（例如pptx），但文件的扩展名不一致：%1。", "SSE.Controllers.Main.errorInconsistentExtXlsx": "打开文件时出错<br>文件内容对应于电子表格（例如xlsx），但文件的扩展名不一致：%1。", "SSE.Controllers.Main.errorInvalidRef": "输入选择的正确名称或有效参考。", "SSE.Controllers.Main.errorKeyEncrypt": "未知密钥描述符", "SSE.Controllers.Main.errorKeyExpire": "密钥描述符已过期", "SSE.Controllers.Main.errorLabledColumnsPivot": "若要创建数据透视表，请使用组织为带有标签列的列表的数据。", "SSE.Controllers.Main.errorLoadingFont": "字体未加载<br>请与您的文档服务器管理员联系。", "SSE.Controllers.Main.errorLocationOrDataRangeError": "位置或数据范围的引用无效。", "SSE.Controllers.Main.errorLockedAll": "由于工作表被其他用户锁定，因此无法进行操作。", "SSE.Controllers.Main.errorLockedCellGoalSeek": "单变量求解中涉及的一个单元格已被另一个用户修改。", "SSE.Controllers.Main.errorLockedCellPivot": "您不能更改数据透视表中的数据。", "SSE.Controllers.Main.errorLockedWorksheetRename": "此时由于其他用户重命名该表单，因此无法重命名该表", "SSE.Controllers.Main.errorMaxPoints": "每个图表的最大串联点数为4096。", "SSE.Controllers.Main.errorMoveRange": "不能改变合并单元的一部分", "SSE.Controllers.Main.errorMoveSlicerError": "无法将表格切片器从一个工作簿复制到另一个工作簿<br>请选择整个表和切片器重试。", "SSE.Controllers.Main.errorMultiCellFormula": "表格中不允许使用多单元格数组公式。", "SSE.Controllers.Main.errorNoDataToParse": "没有选择要分析的数据。", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "如果一个或多个数据透视表包含计算项，则数据区域中的字段不能重复使用两次或两次以上，也不能同时在数据区域和其他区域中使用。", "SSE.Controllers.Main.errorOpenWarning": "文件中的一个公式的长度超过了<br>允许的字符数，并被删除。", "SSE.Controllers.Main.errorOperandExpected": "输入的函数语法不正确。请检查你是否缺少一个括号 - '（'或'）'。", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "您提供的密码不正确<br>验证CAPS LOCK键是否关闭，并确保使用正确的大写字母。", "SSE.Controllers.Main.errorPasteInPivot": "我们无法对选中的单元格进行此更改，因为这会影响数据透视表。<br>使用字段列表来更改报告。", "SSE.Controllers.Main.errorPasteMaxRange": "复制和粘贴区域不匹配。<br>请选择相同尺寸的区域，或单击一行中的第一个单元格以粘贴复制的单元格。", "SSE.Controllers.Main.errorPasteMultiSelect": "无法对多个范围的选择执行此操作<br>选择一个范围，然后重试。", "SSE.Controllers.Main.errorPasteSlicerError": "无法将表格切片器从一个工作簿复制到另一个工作簿。", "SSE.Controllers.Main.errorPivotFieldNameExists": "数据透视表字段名称已存在。", "SSE.Controllers.Main.errorPivotGroup": "无法对所选内容进行分组。", "SSE.Controllers.Main.errorPivotOverlap": "透视表报表不能与表重叠。", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "保存数据透视表报表时没有底层数据<br>使用“刷新”按钮更新报告。", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "“追踪先例”命令要求活动单元格包含包含有效引用的公式。", "SSE.Controllers.Main.errorPrintMaxPagesCount": "不好意思，当前版本的程序无法一次打印超过 1500 页。<br>我们将在日后的版本中解除此限制。", "SSE.Controllers.Main.errorProcessSaveResult": "保存失败", "SSE.Controllers.Main.errorProtectedRange": "不允许编辑此范围。", "SSE.Controllers.Main.errorSaveWatermark": "该文件包含来自其他域名的水印图片。<br>要在 PDF 中显示水印，请将图片链接更新为与文档相同的域名，或从电脑上传图片。", "SSE.Controllers.Main.errorServerVersion": "编辑器版本已完成更新。为应用这些更改，该页将要刷新。", "SSE.Controllers.Main.errorSessionAbsolute": "文档编辑会话已过期。请重新加载页面。", "SSE.Controllers.Main.errorSessionIdle": "这份文件已经很长时间没有编辑了。请重新加载页面。", "SSE.Controllers.Main.errorSessionToken": "与服务器的连接已中断。请重新加载页面。", "SSE.Controllers.Main.errorSetPassword": "无法设置密码。", "SSE.Controllers.Main.errorSingleColumnOrRowError": "位置引用无效，因为这些单元格并非都在同一列或同一行中<br>选择全部位于一列或一行中的单元格。", "SSE.Controllers.Main.errorStockChart": "行顺序不正确，要建立股票图表，将数据按照以下顺序放置在表格上：<br>开盘价，最高价格，最低价格，收盘价。", "SSE.Controllers.Main.errorToken": "文档安全令牌的格式不正确<br>请与您的文档服务器管理员联系。", "SSE.Controllers.Main.errorTokenExpire": "文档安全令牌已过期<br>请与您的文档服务器管理员联系。", "SSE.Controllers.Main.errorUnexpectedGuid": "外部错误。<br>意外GUID。如果错误仍然存​​在，请联系支持人员。", "SSE.Controllers.Main.errorUpdateVersion": "文件版本已更改。该页面将被重新加载。", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "连接已恢复，文件版本已更改<br>在继续工作之前，您需要下载文件或复制其内容以确保不会丢失任何内容，然后重新加载此页面。", "SSE.Controllers.Main.errorUserDrop": "该文件现在无法访问。", "SSE.Controllers.Main.errorUsersExceed": "超出原服务计划可允许的帐户数量", "SSE.Controllers.Main.errorViewerDisconnect": "连接失败。您仍然可以查看文档<br>，但在连接恢复之前无法下载或打印。", "SSE.Controllers.Main.errorWrongBracketsCount": "一个错误的输入公式。< br >用括号打错了。", "SSE.Controllers.Main.errorWrongOperator": "输入的公式出错。使用了错误的运算符<br>请更正错误。", "SSE.Controllers.Main.errorWrongPassword": "您提供的密码不正确。", "SSE.Controllers.Main.errRemDuplicates": "找到并删除了重复的值：｛0｝，剩下唯一值：｛1｝。", "SSE.Controllers.Main.leavePageText": "您在此电子表格中有未保存的更改。点击“留在此页面”，然后点击“保存”保存。点击“离开此页面”以放弃所有未保存的更改。", "SSE.Controllers.Main.leavePageTextOnClose": "此电子表格中所有未保存的更改都将丢失<br>单击“取消”，然后单击“保存”以保存它们。单击“确定”放弃所有未保存的更改。", "SSE.Controllers.Main.loadFontsTextText": "数据加载中…", "SSE.Controllers.Main.loadFontsTitleText": "数据加载中", "SSE.Controllers.Main.loadFontTextText": "数据加载中…", "SSE.Controllers.Main.loadFontTitleText": "数据加载中", "SSE.Controllers.Main.loadImagesTextText": "图片加载中…", "SSE.Controllers.Main.loadImagesTitleText": "图片加载中", "SSE.Controllers.Main.loadImageTextText": "图片加载中…", "SSE.Controllers.Main.loadImageTitleText": "图片加载中", "SSE.Controllers.Main.loadingDocumentTitleText": "加载电子表格", "SSE.Controllers.Main.notcriticalErrorTitle": "警告", "SSE.Controllers.Main.openErrorText": "打开文件时发生错误", "SSE.Controllers.Main.openTextText": "打开电子表格...", "SSE.Controllers.Main.openTitleText": "打开电子表格", "SSE.Controllers.Main.pastInMergeAreaError": "不能改变合并单元的一部分", "SSE.Controllers.Main.printTextText": "打印电子表格...", "SSE.Controllers.Main.printTitleText": "打印电子表格", "SSE.Controllers.Main.reloadButtonText": "重新加载页面", "SSE.Controllers.Main.requestEditFailedMessageText": "有人正在编辑此文档。请稍后再试。", "SSE.Controllers.Main.requestEditFailedTitleText": "访问被拒绝", "SSE.Controllers.Main.saveErrorText": "保存文件时发生错误", "SSE.Controllers.Main.saveErrorTextDesktop": "无法保存或创建此文件<br>可能的原因有：<br>1.该文件是只读的<br>2.其他用户正在编辑该文件<br>3.磁盘已满或已损坏。", "SSE.Controllers.Main.saveTextText": "正在保存电子表格...", "SSE.Controllers.Main.saveTitleText": "保存电子表格", "SSE.Controllers.Main.scriptLoadError": "连接太慢，无法加载某些组件。请重新加载页面。", "SSE.Controllers.Main.textAnonymous": "匿名用户", "SSE.Controllers.Main.textApplyAll": "应用于所有公式", "SSE.Controllers.Main.textBuyNow": "访问网站", "SSE.Controllers.Main.textChangesSaved": "所有更改已保存", "SSE.Controllers.Main.textClose": "关闭", "SSE.Controllers.Main.textCloseTip": "点击关闭提示", "SSE.Controllers.Main.textConfirm": "确认", "SSE.Controllers.Main.textConnectionLost": "正在尝试连接。请检查连接设置。", "SSE.Controllers.Main.textContactUs": "联系销售人员", "SSE.Controllers.Main.textContinue": "继续", "SSE.Controllers.Main.textConvertEquation": "此方程式是使用旧版本的方程式编辑器创建的，该编辑器已不再受支持。若要编辑它，请将公式转换为Office Math ML格式<br>是否立即转换？", "SSE.Controllers.Main.textCustomLoader": "请注意，根据许可条款您无权更改加载程序。<br>请联系我们的销售部门获取报价。", "SSE.Controllers.Main.textDisconnect": "失去网络连接", "SSE.Controllers.Main.textFillOtherRows": "填充其他行", "SSE.Controllers.Main.textFormulaFilledAllRows": "公式填充的｛0｝行有数据。填充其他空行可能需要几分钟时间。", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "公式填充了前｛0｝行。填充其他空行可能需要几分钟时间。", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "由于节省内存原因，公式只填充前｛0｝行数据。此工作表中还有其他｛1｝行包含数据。您可以手动填充它们。", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "因为节省内存，公式只填充了前｛0｝行。此工作表中的其他行没有数据。", "SSE.Controllers.Main.textGuest": "访客", "SSE.Controllers.Main.textHasMacros": "这个文件带有自动宏。<br>您想要运行宏吗？", "SSE.Controllers.Main.textKeep": "保持", "SSE.Controllers.Main.textLearnMore": "了解更多", "SSE.Controllers.Main.textLoadingDocument": "加载电子表格", "SSE.Controllers.Main.textLongName": "输入一个少于128个字符的名称。", "SSE.Controllers.Main.textNeedSynchronize": "你有更新", "SSE.Controllers.Main.textNo": "否", "SSE.Controllers.Main.textNoLicenseTitle": "已达到许可证最大连接数限制", "SSE.Controllers.Main.textPaidFeature": "付费功能", "SSE.Controllers.Main.textPleaseWait": "该操作可能需要比预期的更多的时间。请稍候...", "SSE.Controllers.Main.textReconnect": "连接已恢复", "SSE.Controllers.Main.textRemember": "记住我对所有文件的选择", "SSE.Controllers.Main.textRememberMacros": "记住我的选择并应用到全部宏", "SSE.Controllers.Main.textRenameError": "用户名不能为空。", "SSE.Controllers.Main.textRenameLabel": "输入用于协作的名称", "SSE.Controllers.Main.textReplace": "取代", "SSE.Controllers.Main.textRequestMacros": "一个宏向 URL 发出请求。您想要允许向 %1 发出请求吗？", "SSE.Controllers.Main.textShape": "形状", "SSE.Controllers.Main.textStrict": "严格模式", "SSE.Controllers.Main.textText": "文字", "SSE.Controllers.Main.textTryQuickPrint": "您已选择“快速打印”：整个文档将被打印到最近选择的打印机或者默认打印机。<br>您想要继续吗？", "SSE.Controllers.Main.textTryUndoRedo": "“撤消/恢复”功能对于“快速协同编辑”模式是禁用的<br>单击“严格模式”按钮切换到严格协同编辑模式，在不受其他用户干扰的情况下编辑文件，并仅在保存更改后发送更改。您可以使用编辑器的高级设置在协同编辑模式之间切换。", "SSE.Controllers.Main.textTryUndoRedoWarn": "快速共同编辑模式下，撤销/重做功能被禁用。", "SSE.Controllers.Main.textUndo": "撤消", "SSE.Controllers.Main.textUpdateVersion": "现在无法编辑该文档。<br>正在尝试更新文件，请稍候...", "SSE.Controllers.Main.textUpdating": "更新中", "SSE.Controllers.Main.textYes": "是", "SSE.Controllers.Main.titleLicenseExp": "许可证过期", "SSE.Controllers.Main.titleLicenseNotActive": "授权证书未激活", "SSE.Controllers.Main.titleServerVersion": "编辑器已更新", "SSE.Controllers.Main.titleUpdateVersion": "版本已更改", "SSE.Controllers.Main.txtAccent": "重点色", "SSE.Controllers.Main.txtAll": "(全部)", "SSE.Controllers.Main.txtArt": "在这输入文字", "SSE.Controllers.Main.txtBasicShapes": "基础形状", "SSE.Controllers.Main.txtBlank": "(空白)", "SSE.Controllers.Main.txtButtons": "按钮", "SSE.Controllers.Main.txtByField": "%2的%1", "SSE.Controllers.Main.txtCallouts": "标注", "SSE.Controllers.Main.txtCharts": "图表", "SSE.Controllers.Main.txtClearFilter": "清空筛选条件", "SSE.Controllers.Main.txtColLbls": "列标签", "SSE.Controllers.Main.txtColumn": "列", "SSE.Controllers.Main.txtConfidential": "保密的", "SSE.Controllers.Main.txtDate": "日期", "SSE.Controllers.Main.txtDays": "天", "SSE.Controllers.Main.txtDiagramTitle": "图表标题", "SSE.Controllers.Main.txtEditingMode": "设置编辑模式..", "SSE.Controllers.Main.txtErrorLoadHistory": "历史加载失败", "SSE.Controllers.Main.txtFiguredArrows": "图形箭头", "SSE.Controllers.Main.txtFile": "文件", "SSE.Controllers.Main.txtGrandTotal": "总计", "SSE.Controllers.Main.txtGroup": "组", "SSE.Controllers.Main.txtHours": "小时", "SSE.Controllers.Main.txtInfo": "信息", "SSE.Controllers.Main.txtLines": "线", "SSE.Controllers.Main.txtMath": "数学", "SSE.Controllers.Main.txtMinutes": "分钟", "SSE.Controllers.Main.txtMonths": "月", "SSE.Controllers.Main.txtMultiSelect": "多选", "SSE.Controllers.Main.txtNone": "无", "SSE.Controllers.Main.txtOr": "1%或2%", "SSE.Controllers.Main.txtPage": "页面", "SSE.Controllers.Main.txtPageOf": "第%1页，共%2页", "SSE.Controllers.Main.txtPages": "页面", "SSE.Controllers.Main.txtPicture": "图片", "SSE.Controllers.Main.txtPivotTable": "数据透视表", "SSE.Controllers.Main.txtPreparedBy": "编写", "SSE.Controllers.Main.txtPrintArea": "打印区域", "SSE.Controllers.Main.txtQuarter": "季度", "SSE.Controllers.Main.txtQuarters": "季度", "SSE.Controllers.Main.txtRectangles": "矩形", "SSE.Controllers.Main.txtRow": "行", "SSE.Controllers.Main.txtRowLbls": "行标签", "SSE.Controllers.Main.txtSaveCopyAsComplete": "已成功保存文件副本", "SSE.Controllers.Main.txtScheme_Aspect": "切面", "SSE.Controllers.Main.txtScheme_Blue": "蓝色", "SSE.Controllers.Main.txtScheme_Blue_Green": "蓝绿色", "SSE.Controllers.Main.txtScheme_Blue_II": "蓝色2", "SSE.Controllers.Main.txtScheme_Blue_Warm": "暖蓝色", "SSE.Controllers.Main.txtScheme_Grayscale": "灰度", "SSE.Controllers.Main.txtScheme_Green": "绿色", "SSE.Controllers.Main.txtScheme_Green_Yellow": "黄绿色", "SSE.Controllers.Main.txtScheme_Marquee": "选框", "SSE.Controllers.Main.txtScheme_Median": "中位数", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "橙色", "SSE.Controllers.Main.txtScheme_Orange_Red": "橙红色", "SSE.Controllers.Main.txtScheme_Paper": "纸张", "SSE.Controllers.Main.txtScheme_Red": "红色", "SSE.Controllers.Main.txtScheme_Red_Orange": "红橙色", "SSE.Controllers.Main.txtScheme_Red_Violet": "红紫色", "SSE.Controllers.Main.txtScheme_Slipstream": "实时流处理引擎Slipstream", "SSE.Controllers.Main.txtScheme_Violet": "紫色", "SSE.Controllers.Main.txtScheme_Violet_II": "紫色2", "SSE.Controllers.Main.txtScheme_Yellow": "黄色", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "黄橙色", "SSE.Controllers.Main.txtSeconds": "秒", "SSE.Controllers.Main.txtSeries": "序列", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "线形标注1（带边框和强调线）", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "线形标注2（带边框和强调线）", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "线形标注3（带边框和强调线）", "SSE.Controllers.Main.txtShape_accentCallout1": "线形标注1（强调线）", "SSE.Controllers.Main.txtShape_accentCallout2": "线形标注2（强调线）", "SSE.Controllers.Main.txtShape_accentCallout3": "线形标注3（强调线）", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "返回或上一步按钮", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "开始按钮", "SSE.Controllers.Main.txtShape_actionButtonBlank": "空白按钮", "SSE.Controllers.Main.txtShape_actionButtonDocument": "“文档”按钮", "SSE.Controllers.Main.txtShape_actionButtonEnd": "结束按钮", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "前进或下一步按钮", "SSE.Controllers.Main.txtShape_actionButtonHelp": "帮助按钮", "SSE.Controllers.Main.txtShape_actionButtonHome": "主页按钮", "SSE.Controllers.Main.txtShape_actionButtonInformation": "信息按钮", "SSE.Controllers.Main.txtShape_actionButtonMovie": "电影按钮", "SSE.Controllers.Main.txtShape_actionButtonReturn": "返回按钮", "SSE.Controllers.Main.txtShape_actionButtonSound": "声音按钮", "SSE.Controllers.Main.txtShape_arc": "弧", "SSE.Controllers.Main.txtShape_bentArrow": "弯曲箭头", "SSE.Controllers.Main.txtShape_bentConnector5": "弯头连接器", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "弯头箭头连接器", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "弯头双箭头连接器", "SSE.Controllers.Main.txtShape_bentUpArrow": "向上弯曲箭头", "SSE.Controllers.Main.txtShape_bevel": "斜角", "SSE.Controllers.Main.txtShape_blockArc": "弧块", "SSE.Controllers.Main.txtShape_borderCallout1": "线形标注1", "SSE.Controllers.Main.txtShape_borderCallout2": "线形标注2", "SSE.Controllers.Main.txtShape_borderCallout3": "线形标注3", "SSE.Controllers.Main.txtShape_bracePair": "双花括号", "SSE.Controllers.Main.txtShape_callout1": "线形标注1（无边框）", "SSE.Controllers.Main.txtShape_callout2": "线形标注2（无边框）", "SSE.Controllers.Main.txtShape_callout3": "线形标注3（无边框）", "SSE.Controllers.Main.txtShape_can": "能", "SSE.Controllers.Main.txtShape_chevron": "V形", "SSE.Controllers.Main.txtShape_chord": "和弦", "SSE.Controllers.Main.txtShape_circularArrow": "圆形箭头", "SSE.Controllers.Main.txtShape_cloud": "云", "SSE.Controllers.Main.txtShape_cloudCallout": "云标注", "SSE.Controllers.Main.txtShape_corner": "角", "SSE.Controllers.Main.txtShape_cube": "立方体", "SSE.Controllers.Main.txtShape_curvedConnector3": "弯曲连接器", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "弯曲箭头连接器", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "弯曲双箭头连接器", "SSE.Controllers.Main.txtShape_curvedDownArrow": "向下弯曲箭头", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "弯曲左箭头", "SSE.Controllers.Main.txtShape_curvedRightArrow": "弯曲右箭头", "SSE.Controllers.Main.txtShape_curvedUpArrow": "向上弯曲箭头", "SSE.Controllers.Main.txtShape_decagon": "十边形", "SSE.Controllers.Main.txtShape_diagStripe": "对角线条纹", "SSE.Controllers.Main.txtShape_diamond": "菱形", "SSE.Controllers.Main.txtShape_dodecagon": "十二边形", "SSE.Controllers.Main.txtShape_donut": "圆环图", "SSE.Controllers.Main.txtShape_doubleWave": "双波浪线", "SSE.Controllers.Main.txtShape_downArrow": "向下箭头", "SSE.Controllers.Main.txtShape_downArrowCallout": "下箭头标注", "SSE.Controllers.Main.txtShape_ellipse": "椭圆", "SSE.Controllers.Main.txtShape_ellipseRibbon": "向下弯曲的丝带", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "向上弯曲丝带", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "流程图：交替流程", "SSE.Controllers.Main.txtShape_flowChartCollate": "流程图：整理", "SSE.Controllers.Main.txtShape_flowChartConnector": "流程图：连接器", "SSE.Controllers.Main.txtShape_flowChartDecision": "流程图：决策", "SSE.Controllers.Main.txtShape_flowChartDelay": "流程图：延迟", "SSE.Controllers.Main.txtShape_flowChartDisplay": "流程图：显示", "SSE.Controllers.Main.txtShape_flowChartDocument": "流程图：文件", "SSE.Controllers.Main.txtShape_flowChartExtract": "流程图：提取", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "流程图：数据", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "流程图：内部存储", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "流程图：磁盘", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "流程图：直接访问存储器", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "流程图：顺序访问存储器", "SSE.Controllers.Main.txtShape_flowChartManualInput": "流程图：手动输入", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "流程图：手动操作", "SSE.Controllers.Main.txtShape_flowChartMerge": "流程圖：合併", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "流程图：多文件", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "流程图：页外连接器", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "流程图：存储的数据", "SSE.Controllers.Main.txtShape_flowChartOr": "流程图：或", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "流程图：预定义程序", "SSE.Controllers.Main.txtShape_flowChartPreparation": "流程图：准备", "SSE.Controllers.Main.txtShape_flowChartProcess": "流程图：流程", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "流程图：卡片", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "流程图：穿孔纸带", "SSE.Controllers.Main.txtShape_flowChartSort": "流程图：排序", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "流程图：求和结点", "SSE.Controllers.Main.txtShape_flowChartTerminator": "流程图：终止符", "SSE.Controllers.Main.txtShape_foldedCorner": "折角", "SSE.Controllers.Main.txtShape_frame": "框架", "SSE.Controllers.Main.txtShape_halfFrame": "半框", "SSE.Controllers.Main.txtShape_heart": "心形", "SSE.Controllers.Main.txtShape_heptagon": "七边形", "SSE.Controllers.Main.txtShape_hexagon": "六边形", "SSE.Controllers.Main.txtShape_homePlate": "五角形", "SSE.Controllers.Main.txtShape_horizontalScroll": "水平滚动", "SSE.Controllers.Main.txtShape_irregularSeal1": "爆炸效果1", "SSE.Controllers.Main.txtShape_irregularSeal2": "爆炸效果2", "SSE.Controllers.Main.txtShape_leftArrow": "左箭头", "SSE.Controllers.Main.txtShape_leftArrowCallout": "左箭头标注", "SSE.Controllers.Main.txtShape_leftBrace": "左括号", "SSE.Controllers.Main.txtShape_leftBracket": "左括号", "SSE.Controllers.Main.txtShape_leftRightArrow": "左右箭头", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "左右箭头标注", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "左右向上箭头", "SSE.Controllers.Main.txtShape_leftUpArrow": "左上箭头", "SSE.Controllers.Main.txtShape_lightningBolt": "闪电符号", "SSE.Controllers.Main.txtShape_line": "折线图", "SSE.Controllers.Main.txtShape_lineWithArrow": "箭头", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "双箭头", "SSE.Controllers.Main.txtShape_mathDivide": "除法", "SSE.Controllers.Main.txtShape_mathEqual": "等于", "SSE.Controllers.Main.txtShape_mathMinus": "减去", "SSE.Controllers.Main.txtShape_mathMultiply": "乘号", "SSE.Controllers.Main.txtShape_mathNotEqual": "不等于", "SSE.Controllers.Main.txtShape_mathPlus": "加", "SSE.Controllers.Main.txtShape_moon": "月亮", "SSE.Controllers.Main.txtShape_noSmoking": "“否”符号", "SSE.Controllers.Main.txtShape_notchedRightArrow": "带凹口的右箭头", "SSE.Controllers.Main.txtShape_octagon": "八边形", "SSE.Controllers.Main.txtShape_parallelogram": "平行四边形", "SSE.Controllers.Main.txtShape_pentagon": "五角形", "SSE.Controllers.Main.txtShape_pie": "圆饼图", "SSE.Controllers.Main.txtShape_plaque": "签署", "SSE.Controllers.Main.txtShape_plus": "加", "SSE.Controllers.Main.txtShape_polyline1": "涂鸦", "SSE.Controllers.Main.txtShape_polyline2": "自由变形", "SSE.Controllers.Main.txtShape_quadArrow": "四向箭头", "SSE.Controllers.Main.txtShape_quadArrowCallout": "四箭头标注", "SSE.Controllers.Main.txtShape_rect": "矩形", "SSE.Controllers.Main.txtShape_ribbon": "向下丝带", "SSE.Controllers.Main.txtShape_ribbon2": "向上丝带", "SSE.Controllers.Main.txtShape_rightArrow": "右箭头", "SSE.Controllers.Main.txtShape_rightArrowCallout": "右箭头标注", "SSE.Controllers.Main.txtShape_rightBrace": "右大括号", "SSE.Controllers.Main.txtShape_rightBracket": "右括号", "SSE.Controllers.Main.txtShape_round1Rect": "圆形单角矩形", "SSE.Controllers.Main.txtShape_round2DiagRect": "圆斜角矩形", "SSE.Controllers.Main.txtShape_round2SameRect": "圆形同侧角矩形", "SSE.Controllers.Main.txtShape_roundRect": "圆角矩形", "SSE.Controllers.Main.txtShape_rtTriangle": "直角三角形", "SSE.Controllers.Main.txtShape_smileyFace": "笑脸", "SSE.Controllers.Main.txtShape_snip1Rect": "剪下单角矩形", "SSE.Controllers.Main.txtShape_snip2DiagRect": "减去对角矩形", "SSE.Controllers.Main.txtShape_snip2SameRect": "剪下同一边角矩形", "SSE.Controllers.Main.txtShape_snipRoundRect": "减去和圆形单角矩形", "SSE.Controllers.Main.txtShape_spline": "曲线", "SSE.Controllers.Main.txtShape_star10": "10角星", "SSE.Controllers.Main.txtShape_star12": "12 角星形", "SSE.Controllers.Main.txtShape_star16": "16角星", "SSE.Controllers.Main.txtShape_star24": "24角星", "SSE.Controllers.Main.txtShape_star32": "32角星", "SSE.Controllers.Main.txtShape_star4": "4 點星形", "SSE.Controllers.Main.txtShape_star5": "5角星", "SSE.Controllers.Main.txtShape_star6": "6 點星形", "SSE.Controllers.Main.txtShape_star7": "7 點星形", "SSE.Controllers.Main.txtShape_star8": "8角星", "SSE.Controllers.Main.txtShape_stripedRightArrow": "条纹右箭头", "SSE.Controllers.Main.txtShape_sun": "周日", "SSE.Controllers.Main.txtShape_teardrop": "泪珠", "SSE.Controllers.Main.txtShape_textRect": "文本框", "SSE.Controllers.Main.txtShape_trapezoid": "梯形", "SSE.Controllers.Main.txtShape_triangle": "三角形", "SSE.Controllers.Main.txtShape_upArrow": "向上箭头", "SSE.Controllers.Main.txtShape_upArrowCallout": "向上箭头标注", "SSE.Controllers.Main.txtShape_upDownArrow": "上下箭头", "SSE.Controllers.Main.txtShape_uturnArrow": "U形转弯箭头", "SSE.Controllers.Main.txtShape_verticalScroll": "垂直滚动", "SSE.Controllers.Main.txtShape_wave": "波浪", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "椭圆形标注", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "矩形标注", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "圆角矩形标注", "SSE.Controllers.Main.txtSheet": "工作表", "SSE.Controllers.Main.txtSlicer": "切片器", "SSE.Controllers.Main.txtStarsRibbons": "星星和丝带", "SSE.Controllers.Main.txtStyle_Bad": "坏", "SSE.Controllers.Main.txtStyle_Calculation": "计算", "SSE.Controllers.Main.txtStyle_Check_Cell": "检查单元格", "SSE.Controllers.Main.txtStyle_Comma": "逗号", "SSE.Controllers.Main.txtStyle_Currency": "货币", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "说明文本", "SSE.Controllers.Main.txtStyle_Good": "好", "SSE.Controllers.Main.txtStyle_Heading_1": "标题1", "SSE.Controllers.Main.txtStyle_Heading_2": "标题2", "SSE.Controllers.Main.txtStyle_Heading_3": "标题3", "SSE.Controllers.Main.txtStyle_Heading_4": "标题4", "SSE.Controllers.Main.txtStyle_Input": "输入", "SSE.Controllers.Main.txtStyle_Linked_Cell": "关联的单元格", "SSE.Controllers.Main.txtStyle_Neutral": "中性", "SSE.Controllers.Main.txtStyle_Normal": "正常", "SSE.Controllers.Main.txtStyle_Note": "备注", "SSE.Controllers.Main.txtStyle_Output": "输出", "SSE.Controllers.Main.txtStyle_Percent": "百分比", "SSE.Controllers.Main.txtStyle_Title": "标题", "SSE.Controllers.Main.txtStyle_Total": "总计", "SSE.Controllers.Main.txtStyle_Warning_Text": "警告文本", "SSE.Controllers.Main.txtTab": "标签", "SSE.Controllers.Main.txtTable": "表格", "SSE.Controllers.Main.txtTime": "时间", "SSE.Controllers.Main.txtUnlock": "解锁", "SSE.Controllers.Main.txtUnlockRange": "解锁范围", "SSE.Controllers.Main.txtUnlockRangeDescription": "输入密码以更改此范围：", "SSE.Controllers.Main.txtUnlockRangeWarning": "您尝试更改的范围受密码保护。", "SSE.Controllers.Main.txtValues": "值", "SSE.Controllers.Main.txtView": "视图", "SSE.Controllers.Main.txtXAxis": "X轴", "SSE.Controllers.Main.txtYAxis": "Y轴", "SSE.Controllers.Main.txtYears": "年", "SSE.Controllers.Main.unknownErrorText": "未知错误。", "SSE.Controllers.Main.unsupportedBrowserErrorText": "您的浏览器不受支持", "SSE.Controllers.Main.uploadDocExtMessage": "未知的文件格式。", "SSE.Controllers.Main.uploadDocFileCountMessage": "未上传任何文档。", "SSE.Controllers.Main.uploadDocSizeMessage": "超出最大文件大小限制。", "SSE.Controllers.Main.uploadImageExtMessage": "未知图片格式。", "SSE.Controllers.Main.uploadImageFileCountMessage": "没有上传图片", "SSE.Controllers.Main.uploadImageSizeMessage": "图像太大。最大大小为25 MB。", "SSE.Controllers.Main.uploadImageTextText": "图片上传中...", "SSE.Controllers.Main.uploadImageTitleText": "图片上传中", "SSE.Controllers.Main.waitText": "请稍候...", "SSE.Controllers.Main.warnBrowserIE9": "该应用程序在IE9上的功能很差。使用IE10或更高版本", "SSE.Controllers.Main.warnBrowserZoom": "您的浏览器当前缩放设置不完全支持。请按Ctrl + 0重设为默认缩放。", "SSE.Controllers.Main.warnLicenseAnonymous": "匿名用户的访问被拒绝<br>此文档将仅打开以供查看。", "SSE.Controllers.Main.warnLicenseBefore": "许可证未激活<br>请与管理员联系。", "SSE.Controllers.Main.warnLicenseExceeded": "您已达到同时连接到%1编辑器的限制。此文档将仅打开以供查看<br>请与管理员联系以了解更多信息。", "SSE.Controllers.Main.warnLicenseExp": "您的许可证已过期。<br>请更新您的许可证并刷新页面。", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "许可证已过期。<br>您现在不能使用文档编辑功能。<br>请联系您的管理员。", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "许可证需要续订<br>您只有部分的文档编辑功能的访问权限<br>请与管理员联系以获得完全访问权限", "SSE.Controllers.Main.warnLicenseUsersExceeded": "您已达到%1编辑器的用户限制。请与管理员联系以了解更多信息。", "SSE.Controllers.Main.warnNoLicense": "您已达到同时连接到%1编辑器的限制。此文档将仅打开以供查看<br>有关个人升级条款，请与%1销售团队联系。", "SSE.Controllers.Main.warnNoLicenseUsers": "您已达到%1编辑器的用户限制。有关个人升级条款，请与%1销售团队联系。", "SSE.Controllers.Main.warnProcessRightsChange": "您被拒绝了编辑文件的权限。", "SSE.Controllers.PivotTable.strSheet": "工作表", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "无法添加或修改该项。该字段已在数据透视表的筛选器中。", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "不允许对此活动单元格执行任何带有计算项的操作。", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "如果一个或多个数据透视表包含计算项，则数据区域中的字段不能重复使用两次或两次以上，也不能同时在数据区域和其他区域中使用。", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "计算项不适用于自定义小计。", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "无法找到项目名称。请检查您是否已正确输入名称，以及该项目是否存在于数据透视表中。", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "当数据透视表包含计算项时，不支持平均值、标准差和方差。", "SSE.Controllers.Print.strAllSheets": "所有工作表", "SSE.Controllers.Print.textFirstCol": "第一列", "SSE.Controllers.Print.textFirstRow": "第一行", "SSE.Controllers.Print.textFrozenCols": "冻结的列", "SSE.Controllers.Print.textFrozenRows": "冻结的行", "SSE.Controllers.Print.textInvalidRange": "错误！无效的单元格范围", "SSE.Controllers.Print.textNoRepeat": "不要重复", "SSE.Controllers.Print.textRepeat": "重复...", "SSE.Controllers.Print.textSelectRange": "选择范围", "SSE.Controllers.Print.txtCustom": "自定义", "SSE.Controllers.Print.txtZoomToPage": "缩放至页面", "SSE.Controllers.Search.textInvalidRange": "错误！无效的单元格范围", "SSE.Controllers.Search.textNoTextFound": "无法找到您搜索的数据，请调整您的搜索选项。", "SSE.Controllers.Search.textReplaceSkipped": "替换已完成。 {0}次跳过。", "SSE.Controllers.Search.textReplaceSuccess": "搜索已完成。已替换｛0｝处", "SSE.Controllers.Statusbar.errorLastSheet": "工作簿必须至少有一个可见的工作表。", "SSE.Controllers.Statusbar.errorRemoveSheet": "不能删除工作表", "SSE.Controllers.Statusbar.strSheet": "工作表", "SSE.Controllers.Statusbar.textDisconnect": "<b>连接失败</b><br>正在尝试连接。请检查连接设置。", "SSE.Controllers.Statusbar.textSheetViewTip": "您处于工作表视图模式。筛选和排序仅对您和仍在此视图中的用户可见。", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "您处于图纸视图模式。筛选仅对您和仍在此视图中的用户可见。", "SSE.Controllers.Statusbar.warnDeleteSheet": "工作表可能包含数据。您确定要继续吗？", "SSE.Controllers.Statusbar.zoomText": "缩放％{0}", "SSE.Controllers.Toolbar.confirmAddFontName": "您想要保存的字体在当前设备上不可用。<br>文本的样式将使用系统字体中的一种进行显示，保存的字体将在可用时被调用。<br>您想要继续吗？", "SSE.Controllers.Toolbar.errorComboSeries": "若要创建组合图表，请至少选择两个系列的数据。", "SSE.Controllers.Toolbar.errorMaxPoints": "每个图表的最大串联点数为4096。", "SSE.Controllers.Toolbar.errorMaxRows": "错误！每个图表的最大数据系列数为255", "SSE.Controllers.Toolbar.errorStockChart": "行顺序不正确，要建立股票图表，将数据按照以下顺序放置在表格上：<br>开盘价，最高价格，最低价格，收盘价。", "SSE.Controllers.Toolbar.helpCalcItems": "在数据透视表中使用计算项。", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "计算项", "SSE.Controllers.Toolbar.helpFastUndo": "在快速协同模式下协作编辑电子表格时，可轻松撤销更改。", "SSE.Controllers.Toolbar.helpFastUndoHeader": "在快速协同编辑模式中“撤销”功能现已可用", "SSE.Controllers.Toolbar.helpMergeShapes": "使用新的编辑选项，可以合并、减去和排除形状。", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "合并形状", "SSE.Controllers.Toolbar.textAccent": "重点", "SSE.Controllers.Toolbar.textBracket": "括号", "SSE.Controllers.Toolbar.textDirectional": "方向性的", "SSE.Controllers.Toolbar.textFontSizeErr": "输入的值不正确。<br>请输入1到409之间的数值", "SSE.Controllers.Toolbar.textFraction": "分数", "SSE.Controllers.Toolbar.textFunction": "函数", "SSE.Controllers.Toolbar.textIndicator": "指标", "SSE.Controllers.Toolbar.textInsert": "插入", "SSE.Controllers.Toolbar.textIntegral": "积分", "SSE.Controllers.Toolbar.textLargeOperator": "大型运算符", "SSE.Controllers.Toolbar.textLimitAndLog": "极限和对数", "SSE.Controllers.Toolbar.textLongOperation": "长操作", "SSE.Controllers.Toolbar.textMatrix": "矩阵", "SSE.Controllers.Toolbar.textOperator": "运算符", "SSE.Controllers.Toolbar.textPivot": "数据透视表", "SSE.Controllers.Toolbar.textRadical": "根号", "SSE.Controllers.Toolbar.textRating": "评级", "SSE.Controllers.Toolbar.textRecentlyUsed": "最近使用的", "SSE.Controllers.Toolbar.textScript": "脚本", "SSE.Controllers.Toolbar.textShapes": "形状", "SSE.Controllers.Toolbar.textSymbols": "符号", "SSE.Controllers.Toolbar.textWarning": "警告", "SSE.Controllers.Toolbar.txtAccent_Accent": "尖音符", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "上方的左右箭头", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "上方左箭头", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "上方向右箭头", "SSE.Controllers.Toolbar.txtAccent_Bar": "条", "SSE.Controllers.Toolbar.txtAccent_BarBot": "下划线", "SSE.Controllers.Toolbar.txtAccent_BarTop": "上划线", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "有方框的公式（包含占位符）", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "带框公式（示例）", "SSE.Controllers.Toolbar.txtAccent_Check": "检查", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "底括号", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "上大括号", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "向量A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "带有上划线的ABC", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x异或非y", "SSE.Controllers.Toolbar.txtAccent_DDDot": "三个点", "SSE.Controllers.Toolbar.txtAccent_DDot": "双点", "SSE.Controllers.Toolbar.txtAccent_Dot": "点", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "双重横杠", "SSE.Controllers.Toolbar.txtAccent_Grave": "严重", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "下面的分组字符", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "上面的分组字符", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "上方的向左鱼叉", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "上方的向右鱼叉", "SSE.Controllers.Toolbar.txtAccent_Hat": "帽子", "SSE.Controllers.Toolbar.txtAccent_Smile": "短音符", "SSE.Controllers.Toolbar.txtAccent_Tilde": "波浪号", "SSE.Controllers.Toolbar.txtBracket_Angle": "尖括号", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "带分隔符的尖括号", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "带两个分隔符的尖括号", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "直角括号", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "左尖括号", "SSE.Controllers.Toolbar.txtBracket_Curve": "花括号", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "带分隔符的花括号", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "右大括号", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "左大括号", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "案例（两种情况）", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "案例（三种情况）", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "堆栈对象", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "括号中的堆栈对象", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "案例示例", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "二项式系数", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "尖括号中的二项式系数", "SSE.Controllers.Toolbar.txtBracket_Line": "竖线", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "右竖线", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "左侧竖条", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "双竖条", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "右侧双竖条", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "左双竖条", "SSE.Controllers.Toolbar.txtBracket_LowLim": "地板", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "右地板", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "左地板", "SSE.Controllers.Toolbar.txtBracket_Round": "圆括号", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "带分隔符的括号", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "右括号", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "左括号", "SSE.Controllers.Toolbar.txtBracket_Square": "方括号", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "两个右方括号之间的占位符", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "倒置方括号", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "右侧方括号", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "左方括号", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "两个左方括号之间的占位符", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "双方括号", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "右侧双方括号", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "左双方括号", "SSE.Controllers.Toolbar.txtBracket_UppLim": "天花板", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "右天花板", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "左天花板", "SSE.Controllers.Toolbar.txtDeleteCells": "删除单元格", "SSE.Controllers.Toolbar.txtExpand": "展开和排序", "SSE.Controllers.Toolbar.txtExpandSort": "选择范围相邻的数据将不会被排序。您想要扩大选择范围以包含相邻的数据，还是继续仅对当前选中的单元格进行排序？", "SSE.Controllers.Toolbar.txtFractionDiagonal": "倾斜分数", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "dx 除 dy", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Δy 除以 Δx", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "微分", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Δx 除 Δy", "SSE.Controllers.Toolbar.txtFractionHorizontal": "线性分数", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi除以2", "SSE.Controllers.Toolbar.txtFractionSmall": "小分数", "SSE.Controllers.Toolbar.txtFractionVertical": "堆积分数", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "反余弦函数", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "双曲反余弦函数", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "反余切函数", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "双曲反余切函数", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "反余割函数", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "双曲反余割函数", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "反正割函數", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "双曲反正割函数", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "反正弦函数", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "双曲反正弦函数", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "反正切函数", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "双曲反正切函数", "SSE.Controllers.Toolbar.txtFunction_Cos": "余弦函数", "SSE.Controllers.Toolbar.txtFunction_Cosh": "双曲余弦函数", "SSE.Controllers.Toolbar.txtFunction_Cot": "余切函數", "SSE.Controllers.Toolbar.txtFunction_Coth": "双曲余切函数", "SSE.Controllers.Toolbar.txtFunction_Csc": "余割函數", "SSE.Controllers.Toolbar.txtFunction_Csch": "双曲余割函数", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "正弦波", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "cos2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "正切函数", "SSE.Controllers.Toolbar.txtFunction_Sec": "正割函数", "SSE.Controllers.Toolbar.txtFunction_Sech": "双曲正割函数", "SSE.Controllers.Toolbar.txtFunction_Sin": "正弦函数", "SSE.Controllers.Toolbar.txtFunction_Sinh": "双曲正弦函数", "SSE.Controllers.Toolbar.txtFunction_Tan": "正切函数", "SSE.Controllers.Toolbar.txtFunction_Tanh": "双曲正切函数", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "自定义", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "数据和模型", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "好、坏和中性", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "没有名称", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "数字格式", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "主题单元格样式", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "页面标题和文本标题", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "自定义", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "深色", "SSE.Controllers.Toolbar.txtGroupTable_Light": "浅色", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "中等", "SSE.Controllers.Toolbar.txtInsertCells": "插入单元格", "SSE.Controllers.Toolbar.txtIntegral": "积分", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "差分θ", "SSE.Controllers.Toolbar.txtIntegral_dx": "差分x", "SSE.Controllers.Toolbar.txtIntegral_dy": "差分y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "带堆叠限制的积分", "SSE.Controllers.Toolbar.txtIntegralDouble": "二重积分", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "具有堆叠限制的二重积分", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "带限制的二重积分", "SSE.Controllers.Toolbar.txtIntegralOriented": "轮廓积分", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "具有堆叠极限的等高线积分", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "曲面积分", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "带堆叠限制的曲面积分", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "带限制的曲面积分", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "带限制的等高线积分", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "体积积分", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "带堆叠限制的体积积分", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "带限制的体积积分", "SSE.Controllers.Toolbar.txtIntegralSubSup": "带限制的积分", "SSE.Controllers.Toolbar.txtIntegralTriple": "三重积分", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "带堆叠限制的三重积分", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "带限制的三重积分", "SSE.Controllers.Toolbar.txtInvalidRange": "错误！单元格范围无效", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "逻辑与", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "带下限的逻辑与", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "带限制的逻辑与", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "带下标下限的逻辑与", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "带上下标限制的逻辑与", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "联产品", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "具有下限的共同产品", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "有限制的共同产品", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "具有下标下限的共同产品", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "具有下标/上标限制的共同产品", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "求和", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "从i等于0到n的求和", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "使用两个索引的求和示例", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "乘积示例", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "并集示例", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "逻辑或", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "带下限的逻辑或", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "带限制的逻辑或", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "带下标下限的逻辑或", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "带下标/上标限制的逻辑或", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "交集", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "带下限的交集", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "带限制的交集", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "带下标下限的交集", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "带下标/上标限制的交集", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "乘积", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "带下限的乘积", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "带限制的乘积", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "带下标下限的乘积", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "带下标/上标极限的乘积", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "求和", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "带下限的求和", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "带限制的求和", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "带下标下限的求和", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "带上下标限制的求和", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "并集", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "带下限的并集", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "带限制的并集", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "带下标下限的并集", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "带上下标限制的并集", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "限制范例", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "最大范例", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "限制", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "自然对数", "SSE.Controllers.Toolbar.txtLimitLog_Log": "对数", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "对数", "SSE.Controllers.Toolbar.txtLimitLog_Max": "最大值", "SSE.Controllers.Toolbar.txtLimitLog_Min": "最小值", "SSE.Controllers.Toolbar.txtLockSort": "在选择区附近找到数据，但是你没有足够的权限修改这些单元格。<br>你希望继续操作当前选择区吗？", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 空矩阵", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 空矩阵", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 空矩阵", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 空矩阵", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "以双竖线表示的空的2x2矩阵", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "空的2x2行列式", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "带圆括号的2x2空矩阵", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "带方形括号的2x2空矩阵", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 空矩阵", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 空矩阵", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 空矩阵", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 空矩陣", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "基线点", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "中线点", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "对角点", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "垂直点", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "括号中的稀疏矩阵", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "括号中的稀疏矩阵", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2带零的单位矩阵", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "2x2除了对角线以外都是空白的单位矩阵", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "含有零的3x3单位矩阵", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3除了对角线以外都是空白的单位矩阵", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "下方的左右箭头", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "上方的左右箭头", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "下方向左箭头", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "上方左箭头", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "下方向右箭头", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "上方向右箭头", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "冒号等号", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "收益", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta 收益", "SSE.Controllers.Toolbar.txtOperator_Definition": "等同于定义", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta 等于", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "下方的左右双箭头", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "上方的左右双箭头", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "下方向左箭头", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "上方左箭头", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "下方向右箭头", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "上方向右箭头", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "等于等于", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "减号等号", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "加号等号", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "测量者", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "二次方程式的右侧", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "a的平方加b的平方的平方根", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "带次数的平方根", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "立方根", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "开n次根号", "SSE.Controllers.Toolbar.txtRadicalSqrt": "平方根", "SSE.Controllers.Toolbar.txtScriptCustom_1": "x下标y的平方", "SSE.Controllers.Toolbar.txtScriptCustom_2": "e 的负 i omega t 次方", "SSE.Controllers.Toolbar.txtScriptCustom_3": "x 的平方", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Y左上标n左下标1", "SSE.Controllers.Toolbar.txtScriptSub": "下标", "SSE.Controllers.Toolbar.txtScriptSubSup": "下标-上标", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "左下标上标", "SSE.Controllers.Toolbar.txtScriptSup": "上标", "SSE.Controllers.Toolbar.txtSorting": "排序中", "SSE.Controllers.Toolbar.txtSortSelected": "对所选内容排序", "SSE.Controllers.Toolbar.txtSymbol_about": "大约", "SSE.Controllers.Toolbar.txtSymbol_additional": "补充", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_approx": "几乎等于", "SSE.Controllers.Toolbar.txtSymbol_ast": "星号运算符", "SSE.Controllers.Toolbar.txtSymbol_beta": "测试版", "SSE.Controllers.Toolbar.txtSymbol_beth": "确信", "SSE.Controllers.Toolbar.txtSymbol_bullet": "项目符号运算符", "SSE.Controllers.Toolbar.txtSymbol_cap": "交集", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "立方根", "SSE.Controllers.Toolbar.txtSymbol_cdots": "中线水平省略号", "SSE.Controllers.Toolbar.txtSymbol_celsius": "摄氏度", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "约等于", "SSE.Controllers.Toolbar.txtSymbol_cup": "并集", "SSE.Controllers.Toolbar.txtSymbol_ddots": "向右对角线省略号", "SSE.Controllers.Toolbar.txtSymbol_degree": "度", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "除号", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "向下箭头", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "空集", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "等于", "SSE.Controllers.Toolbar.txtSymbol_equiv": "相同", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "存在", "SSE.Controllers.Toolbar.txtSymbol_factorial": "阶乘", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "华氏度", "SSE.Controllers.Toolbar.txtSymbol_forall": "全部", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "大于等于", "SSE.Controllers.Toolbar.txtSymbol_gg": "远大于", "SSE.Controllers.Toolbar.txtSymbol_greater": "大于", "SSE.Controllers.Toolbar.txtSymbol_in": "元素", "SSE.Controllers.Toolbar.txtSymbol_inc": "增量", "SSE.Controllers.Toolbar.txtSymbol_infinity": "无限", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "左箭头", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "左右箭头", "SSE.Controllers.Toolbar.txtSymbol_leq": "小于或等于", "SSE.Controllers.Toolbar.txtSymbol_less": "小于", "SSE.Controllers.Toolbar.txtSymbol_ll": "远小于", "SSE.Controllers.Toolbar.txtSymbol_minus": "减去", "SSE.Controllers.Toolbar.txtSymbol_mp": "减加号", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "不等于", "SSE.Controllers.Toolbar.txtSymbol_ni": "包含为成员", "SSE.Controllers.Toolbar.txtSymbol_not": "不签名", "SSE.Controllers.Toolbar.txtSymbol_notexists": "不存在", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "部分差分", "SSE.Controllers.Toolbar.txtSymbol_percent": "百分比", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "加", "SSE.Controllers.Toolbar.txtSymbol_pm": "加减", "SSE.Controllers.Toolbar.txtSymbol_propto": "成比例于", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "四次方根", "SSE.Controllers.Toolbar.txtSymbol_qed": "校验结束", "SSE.Controllers.Toolbar.txtSymbol_rddots": "向右对角线省略号", "SSE.Controllers.Toolbar.txtSymbol_rho": "希腊字母\"ρ\"", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "右箭头", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "根号", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "因此", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "乘法符号", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "向上箭头", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon变体", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Phi 变体", "SSE.Controllers.Toolbar.txtSymbol_varpi": "π变量", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Rho 变量", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma变量", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Theta 变量", "SSE.Controllers.Toolbar.txtSymbol_vdots": "垂直省略号", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "ζ", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "深色表格样式", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "浅色表格样式", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "中等深浅表格样式", "SSE.Controllers.Toolbar.warnLongOperation": "您即将执行的操作可能需要相当长的时间才能完成。<br>您确定要继续吗？", "SSE.Controllers.Toolbar.warnMergeLostData": "只有来自左上方单元格的数据将保留在合并的单元格中。 <br>您确定要继续吗？", "SSE.Controllers.Toolbar.warnNoRecommended": "若要创建图表，请选择包含要使用的数据的单元格<br>如果您有行和列的名称，并且希望将它们用作标签，请将它们包括在您的选择中。", "SSE.Controllers.Viewport.textFreezePanes": "冻结窗格", "SSE.Controllers.Viewport.textFreezePanesShadow": "显示冻结窗格的阴影", "SSE.Controllers.Viewport.textHideFBar": "隐藏公式栏", "SSE.Controllers.Viewport.textHideGridlines": "隐藏网格线", "SSE.Controllers.Viewport.textHideHeadings": "隐藏标题", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "小数分隔符", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "千位分隔符", "SSE.Views.AdvancedSeparatorDialog.textLabel": "用于识别数字数据的设置", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "文本限定符", "SSE.Views.AdvancedSeparatorDialog.textTitle": "高级设置", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(无)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "自定义筛选器", "SSE.Views.AutoFilterDialog.textAddSelection": "添加最新的选择到筛选依据中", "SSE.Views.AutoFilterDialog.textEmptyItem": "{空白}", "SSE.Views.AutoFilterDialog.textSelectAll": "全选", "SSE.Views.AutoFilterDialog.textSelectAllResults": "选择所有搜索结果", "SSE.Views.AutoFilterDialog.textWarning": "警告", "SSE.Views.AutoFilterDialog.txtAboveAve": "平均水平以上", "SSE.Views.AutoFilterDialog.txtAfter": "之后...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "时段中的所有日期", "SSE.Views.AutoFilterDialog.txtApril": "四月", "SSE.Views.AutoFilterDialog.txtAugust": "八月", "SSE.Views.AutoFilterDialog.txtBefore": "之前...", "SSE.Views.AutoFilterDialog.txtBegins": "开头为...", "SSE.Views.AutoFilterDialog.txtBelowAve": "在平均值以下", "SSE.Views.AutoFilterDialog.txtBetween": "在...之间", "SSE.Views.AutoFilterDialog.txtClear": "清除", "SSE.Views.AutoFilterDialog.txtContains": "包含...", "SSE.Views.AutoFilterDialog.txtDateFilter": "日期筛选器", "SSE.Views.AutoFilterDialog.txtDecember": "十二月", "SSE.Views.AutoFilterDialog.txtEmpty": "输入单元格过滤器", "SSE.Views.AutoFilterDialog.txtEnds": "结尾为...", "SSE.Views.AutoFilterDialog.txtEquals": "等于", "SSE.Views.AutoFilterDialog.txtFebruary": "二月", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "按单元格颜色筛选", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "按字体颜色筛选", "SSE.Views.AutoFilterDialog.txtGreater": "大于...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "大于或等于...", "SSE.Views.AutoFilterDialog.txtJanuary": "一月", "SSE.Views.AutoFilterDialog.txtJuly": "七月", "SSE.Views.AutoFilterDialog.txtJune": "六月", "SSE.Views.AutoFilterDialog.txtLabelFilter": "标签筛选", "SSE.Views.AutoFilterDialog.txtLastMonth": "上个月", "SSE.Views.AutoFilterDialog.txtLastQuarter": "上个季度", "SSE.Views.AutoFilterDialog.txtLastWeek": "上周", "SSE.Views.AutoFilterDialog.txtLastYear": "去年", "SSE.Views.AutoFilterDialog.txtLess": "少于...", "SSE.Views.AutoFilterDialog.txtLessEquals": "小于或等于", "SSE.Views.AutoFilterDialog.txtMarch": "三月", "SSE.Views.AutoFilterDialog.txtMay": "五月", "SSE.Views.AutoFilterDialog.txtNextMonth": "下个月", "SSE.Views.AutoFilterDialog.txtNextQuarter": "下季度", "SSE.Views.AutoFilterDialog.txtNextWeek": "下周", "SSE.Views.AutoFilterDialog.txtNextYear": "下一年", "SSE.Views.AutoFilterDialog.txtNotBegins": "不是从...开始", "SSE.Views.AutoFilterDialog.txtNotBetween": "不介于...", "SSE.Views.AutoFilterDialog.txtNotContains": "不含...", "SSE.Views.AutoFilterDialog.txtNotEnds": "不是以...结束", "SSE.Views.AutoFilterDialog.txtNotEquals": "不等于...", "SSE.Views.AutoFilterDialog.txtNovember": "十一月", "SSE.Views.AutoFilterDialog.txtNumFilter": "数字筛选", "SSE.Views.AutoFilterDialog.txtOctober": "十月", "SSE.Views.AutoFilterDialog.txtQuarter1": "一季度", "SSE.Views.AutoFilterDialog.txtQuarter2": "一季度", "SSE.Views.AutoFilterDialog.txtQuarter3": "一季度", "SSE.Views.AutoFilterDialog.txtQuarter4": "一季度", "SSE.Views.AutoFilterDialog.txtReapply": "重新应用", "SSE.Views.AutoFilterDialog.txtSeptember": "九月", "SSE.Views.AutoFilterDialog.txtSortCellColor": "按单元格颜色排序", "SSE.Views.AutoFilterDialog.txtSortFontColor": "按字体颜色排序", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "从高到低排序", "SSE.Views.AutoFilterDialog.txtSortLow2High": "从低到高排序", "SSE.Views.AutoFilterDialog.txtSortOption": "更多排序选项...", "SSE.Views.AutoFilterDialog.txtTextFilter": "文本筛选", "SSE.Views.AutoFilterDialog.txtThisMonth": "这个月", "SSE.Views.AutoFilterDialog.txtThisQuarter": "本季度", "SSE.Views.AutoFilterDialog.txtThisWeek": "本周", "SSE.Views.AutoFilterDialog.txtThisYear": "今年", "SSE.Views.AutoFilterDialog.txtTitle": "筛选", "SSE.Views.AutoFilterDialog.txtToday": "今天", "SSE.Views.AutoFilterDialog.txtTomorrow": "明天", "SSE.Views.AutoFilterDialog.txtTop10": "前十位", "SSE.Views.AutoFilterDialog.txtValueFilter": "按值筛选", "SSE.Views.AutoFilterDialog.txtYearToDate": "年初至今", "SSE.Views.AutoFilterDialog.txtYesterday": "昨天", "SSE.Views.AutoFilterDialog.warnFilterError": "“值”区域中至少需要一个字段才能应用值筛选器。", "SSE.Views.AutoFilterDialog.warnNoSelected": "您必须至少选择一个值", "SSE.Views.CellEditor.textManager": "名称管理", "SSE.Views.CellEditor.tipFormula": "插入函数", "SSE.Views.CellRangeDialog.errorMaxRows": "错误！每个图表的最大数据系列数为255", "SSE.Views.CellRangeDialog.errorStockChart": "行顺序不正确，要建立股票图表，将数据按照以下顺序放置在表格上：<br>开盘价，最高价格，最低价格，收盘价。", "SSE.Views.CellRangeDialog.txtEmpty": "这是必填栏", "SSE.Views.CellRangeDialog.txtInvalidRange": "错误！无效的单元格范围", "SSE.Views.CellRangeDialog.txtTitle": "选择数据范围", "SSE.Views.CellSettings.strShrink": "收缩以适应", "SSE.Views.CellSettings.strWrap": "文字换行", "SSE.Views.CellSettings.textAngle": "角度", "SSE.Views.CellSettings.textBackColor": "背景颜色", "SSE.Views.CellSettings.textBackground": "背景颜色", "SSE.Views.CellSettings.textBorderColor": "颜色", "SSE.Views.CellSettings.textBorders": "边框样式", "SSE.Views.CellSettings.textClearRule": "清除规则", "SSE.Views.CellSettings.textColor": "颜色填充", "SSE.Views.CellSettings.textColorScales": "色阶", "SSE.Views.CellSettings.textCondFormat": "条件格式", "SSE.Views.CellSettings.textControl": "文件控制", "SSE.Views.CellSettings.textDataBars": "数据栏", "SSE.Views.CellSettings.textDirection": "方向", "SSE.Views.CellSettings.textFill": "填充", "SSE.Views.CellSettings.textForeground": "前景色", "SSE.Views.CellSettings.textGradient": "渐变点", "SSE.Views.CellSettings.textGradientColor": "颜色", "SSE.Views.CellSettings.textGradientFill": "渐变填充", "SSE.Views.CellSettings.textIndent": "缩进", "SSE.Views.CellSettings.textItems": "项目", "SSE.Views.CellSettings.textLinear": "线性", "SSE.Views.CellSettings.textManageRule": "管理规则", "SSE.Views.CellSettings.textNewRule": "新规则", "SSE.Views.CellSettings.textNoFill": "无填充", "SSE.Views.CellSettings.textOrientation": "文本方向", "SSE.Views.CellSettings.textPattern": "模式", "SSE.Views.CellSettings.textPatternFill": "模式", "SSE.Views.CellSettings.textPosition": "位置", "SSE.Views.CellSettings.textRadial": "放射狀", "SSE.Views.CellSettings.textSelectBorders": "选择您要更改应用样式的边框", "SSE.Views.CellSettings.textSelection": "从当前选择", "SSE.Views.CellSettings.textThisPivot": "由此数据透视表", "SSE.Views.CellSettings.textThisSheet": "由此工作表", "SSE.Views.CellSettings.textThisTable": "由此表", "SSE.Views.CellSettings.tipAddGradientPoint": "添加渐变点", "SSE.Views.CellSettings.tipAll": "设置外边框和所有内框线", "SSE.Views.CellSettings.tipBottom": "仅设置外底边框", "SSE.Views.CellSettings.tipDiagD": "设置对角线下边框", "SSE.Views.CellSettings.tipDiagU": "设置对角线上边框", "SSE.Views.CellSettings.tipInner": "仅设定内部框线", "SSE.Views.CellSettings.tipInnerHor": "仅设置水平内框线", "SSE.Views.CellSettings.tipInnerVert": "仅设置垂直内线", "SSE.Views.CellSettings.tipLeft": "仅设置外部左边框", "SSE.Views.CellSettings.tipNone": "设置无边框", "SSE.Views.CellSettings.tipOuter": "仅设定外部边框", "SSE.Views.CellSettings.tipRemoveGradientPoint": "删除渐变点", "SSE.Views.CellSettings.tipRight": "仅设置右外边框", "SSE.Views.CellSettings.tipTop": "仅设定外部顶框线", "SSE.Views.ChartDataDialog.errorInFormula": "您输入的公式有错误。", "SSE.Views.ChartDataDialog.errorInvalidReference": "引用无效。引用必须指向打开的工作表。", "SSE.Views.ChartDataDialog.errorMaxPoints": "每个图表的最大串联点数为4096。", "SSE.Views.ChartDataDialog.errorMaxRows": "每个图表的最大数据序列数为255。", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "引用无效。标题、值、大小或数据标签的引用必须是单个单元格、行或列。", "SSE.Views.ChartDataDialog.errorNoValues": "若要创建图表，该序列必须至少包含一个值。", "SSE.Views.ChartDataDialog.errorStockChart": "行顺序不正确，要建立股票图表，将数据按照以下顺序放置在表格上：<br>开盘价，最高价格，最低价格，收盘价。", "SSE.Views.ChartDataDialog.textAdd": "添加", "SSE.Views.ChartDataDialog.textCategory": "水平（类别）轴标签", "SSE.Views.ChartDataDialog.textData": "图表数据范围", "SSE.Views.ChartDataDialog.textDelete": "删除", "SSE.Views.ChartDataDialog.textDown": "下", "SSE.Views.ChartDataDialog.textEdit": "编辑", "SSE.Views.ChartDataDialog.textInvalidRange": "无效的单元格范围", "SSE.Views.ChartDataDialog.textSelectData": "选择数据", "SSE.Views.ChartDataDialog.textSeries": "图例条目（系列）", "SSE.Views.ChartDataDialog.textSwitch": "切换行/列", "SSE.Views.ChartDataDialog.textTitle": "图表数据", "SSE.Views.ChartDataDialog.textUp": "上", "SSE.Views.ChartDataRangeDialog.errorInFormula": "您输入的公式有错误。", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "引用无效。引用必须指向打开的工作表。", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "每个图表的最大串联点数为4096。", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "每个图表的最大数据序列数为255。", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "引用无效。标题、值、大小或数据标签的引用必须是单个单元格、行或列。", "SSE.Views.ChartDataRangeDialog.errorNoValues": "若要创建图表，该序列必须至少包含一个值。", "SSE.Views.ChartDataRangeDialog.errorStockChart": "行顺序不正确，要建立股票图表，将数据按照以下顺序放置在表格上：<br>开盘价，最高价格，最低价格，收盘价。", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "无效的单元格范围", "SSE.Views.ChartDataRangeDialog.textSelectData": "选择数据", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "轴标签范围", "SSE.Views.ChartDataRangeDialog.txtChoose": "选择范围", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "序列名称", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "轴标签", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "编辑数据序列", "SSE.Views.ChartDataRangeDialog.txtValues": "值", "SSE.Views.ChartDataRangeDialog.txtXValues": "X值", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y值", "SSE.Views.ChartSettings.errorMaxRows": "每个图表的最大数据序列数为255。", "SSE.Views.ChartSettings.strLineWeight": "线宽", "SSE.Views.ChartSettings.strSparkColor": "颜色", "SSE.Views.ChartSettings.strTemplate": "模板", "SSE.Views.ChartSettings.text3dDepth": "深度（基准的%）", "SSE.Views.ChartSettings.text3dHeight": "高度（基准的%）", "SSE.Views.ChartSettings.text3dRotation": "三维旋转", "SSE.Views.ChartSettings.textAdvanced": "显示高级设置", "SSE.Views.ChartSettings.textAutoscale": "自动缩放", "SSE.Views.ChartSettings.textBorderSizeErr": "输入的值不正确。<br>请输入介于0 pt和1584 pt之间的值。", "SSE.Views.ChartSettings.textChangeType": "修改类型", "SSE.Views.ChartSettings.textChartType": "更改图表类型", "SSE.Views.ChartSettings.textDefault": "默认旋转", "SSE.Views.ChartSettings.textDown": "下", "SSE.Views.ChartSettings.textEditData": "编辑数据和位置", "SSE.Views.ChartSettings.textFirstPoint": "第一点", "SSE.Views.ChartSettings.textHeight": "高度", "SSE.Views.ChartSettings.textHighPoint": "高点", "SSE.Views.ChartSettings.textKeepRatio": "恒定比例", "SSE.Views.ChartSettings.textLastPoint": "最后一点", "SSE.Views.ChartSettings.textLeft": "左侧", "SSE.Views.ChartSettings.textLowPoint": "低点", "SSE.Views.ChartSettings.textMarkers": "标记", "SSE.Views.ChartSettings.textNarrow": "窄视野", "SSE.Views.ChartSettings.textNegativePoint": "负点", "SSE.Views.ChartSettings.textPerspective": "透视", "SSE.Views.ChartSettings.textRanges": "数据范围", "SSE.Views.ChartSettings.textRight": "右", "SSE.Views.ChartSettings.textRightAngle": "直角坐标轴", "SSE.Views.ChartSettings.textSelectData": "选择数据", "SSE.Views.ChartSettings.textShow": "显示", "SSE.Views.ChartSettings.textSize": "大小", "SSE.Views.ChartSettings.textStyle": "样式", "SSE.Views.ChartSettings.textSwitch": "切换行/列", "SSE.Views.ChartSettings.textType": "类型", "SSE.Views.ChartSettings.textUp": "上", "SSE.Views.ChartSettings.textWiden": "视野开阔", "SSE.Views.ChartSettings.textWidth": "宽度", "SSE.Views.ChartSettings.textX": "X轴旋转", "SSE.Views.ChartSettings.textY": "Y旋转", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "错误！每个图表的最大串联点数为4096。", "SSE.Views.ChartSettingsDlg.errorMaxRows": "错误！每个图表的最大数据系列数为255", "SSE.Views.ChartSettingsDlg.errorStockChart": "行顺序不正确，要建立股票图表，将数据按照以下顺序放置在表格上：<br>开盘价，最高价格，最低价格，收盘价。", "SSE.Views.ChartSettingsDlg.textAbsolute": "不随单元格移动或调整大小", "SSE.Views.ChartSettingsDlg.textAlt": "替代文本", "SSE.Views.ChartSettingsDlg.textAltDescription": "描述", "SSE.Views.ChartSettingsDlg.textAltTip": "视觉对象信息的另一种基于文本的表示方式，将读取给视力或认知障碍的人，以帮助他们更好地理解图像、形状、图表或表格中的信息。", "SSE.Views.ChartSettingsDlg.textAltTitle": "标题", "SSE.Views.ChartSettingsDlg.textAuto": "自动", "SSE.Views.ChartSettingsDlg.textAutoEach": "自动调整每一项", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "轴十字", "SSE.Views.ChartSettingsDlg.textAxisOptions": "轴选项", "SSE.Views.ChartSettingsDlg.textAxisPos": "轴位置", "SSE.Views.ChartSettingsDlg.textAxisSettings": "轴设置", "SSE.Views.ChartSettingsDlg.textAxisTitle": "标题", "SSE.Views.ChartSettingsDlg.textBase": "基础", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "刻度线之间", "SSE.Views.ChartSettingsDlg.textBillions": "十亿", "SSE.Views.ChartSettingsDlg.textBottom": "底部", "SSE.Views.ChartSettingsDlg.textCategoryName": "分类名称", "SSE.Views.ChartSettingsDlg.textCenter": "中心", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "图表元素&<br>图表图例", "SSE.Views.ChartSettingsDlg.textChartTitle": "图表标题", "SSE.Views.ChartSettingsDlg.textCross": "交叉", "SSE.Views.ChartSettingsDlg.textCustom": "自定义", "SSE.Views.ChartSettingsDlg.textDataColumns": "在列中", "SSE.Views.ChartSettingsDlg.textDataLabels": "数据标签", "SSE.Views.ChartSettingsDlg.textDataRows": "在行中", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "显示图例", "SSE.Views.ChartSettingsDlg.textEmptyCells": "隐藏和空白的单元格", "SSE.Views.ChartSettingsDlg.textEmptyLine": "用线连接数据点", "SSE.Views.ChartSettingsDlg.textFit": "调整至宽度大小", "SSE.Views.ChartSettingsDlg.textFixed": "固定", "SSE.Views.ChartSettingsDlg.textFormat": "标签格式", "SSE.Views.ChartSettingsDlg.textGaps": "间隙", "SSE.Views.ChartSettingsDlg.textGridLines": "网格线", "SSE.Views.ChartSettingsDlg.textGroup": "分组迷你图", "SSE.Views.ChartSettingsDlg.textHide": "隐藏", "SSE.Views.ChartSettingsDlg.textHideAxis": "隐藏轴", "SSE.Views.ChartSettingsDlg.textHigh": "高", "SSE.Views.ChartSettingsDlg.textHorAxis": "横轴", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "次横轴", "SSE.Views.ChartSettingsDlg.textHorizontal": "水平的", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "数以百计", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "在", "SSE.Views.ChartSettingsDlg.textInnerBottom": "内底", "SSE.Views.ChartSettingsDlg.textInnerTop": "内顶", "SSE.Views.ChartSettingsDlg.textInvalidRange": "错误！无效的单元格范围", "SSE.Views.ChartSettingsDlg.textLabelDist": "轴标签的距离", "SSE.Views.ChartSettingsDlg.textLabelInterval": "标签之间的间隔", "SSE.Views.ChartSettingsDlg.textLabelOptions": "标签选项", "SSE.Views.ChartSettingsDlg.textLabelPos": "标签位置", "SSE.Views.ChartSettingsDlg.textLayout": "布局", "SSE.Views.ChartSettingsDlg.textLeft": "左侧", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "左侧叠加", "SSE.Views.ChartSettingsDlg.textLegendBottom": "底部", "SSE.Views.ChartSettingsDlg.textLegendLeft": "左侧", "SSE.Views.ChartSettingsDlg.textLegendPos": "图例", "SSE.Views.ChartSettingsDlg.textLegendRight": "右", "SSE.Views.ChartSettingsDlg.textLegendTop": "顶部", "SSE.Views.ChartSettingsDlg.textLines": "线", "SSE.Views.ChartSettingsDlg.textLocationRange": "位置范围", "SSE.Views.ChartSettingsDlg.textLogScale": "对数刻度", "SSE.Views.ChartSettingsDlg.textLow": "低", "SSE.Views.ChartSettingsDlg.textMajor": "主要", "SSE.Views.ChartSettingsDlg.textMajorMinor": "主要和次要", "SSE.Views.ChartSettingsDlg.textMajorType": "主要类型", "SSE.Views.ChartSettingsDlg.textManual": "手动更改", "SSE.Views.ChartSettingsDlg.textMarkers": "标记", "SSE.Views.ChartSettingsDlg.textMarksInterval": "标记之间的间隔", "SSE.Views.ChartSettingsDlg.textMaxValue": "最大值", "SSE.Views.ChartSettingsDlg.textMillions": "百万", "SSE.Views.ChartSettingsDlg.textMinor": "次要", "SSE.Views.ChartSettingsDlg.textMinorType": "次要类型", "SSE.Views.ChartSettingsDlg.textMinValue": "最小值", "SSE.Views.ChartSettingsDlg.textNextToAxis": "在轴旁边", "SSE.Views.ChartSettingsDlg.textNone": "无", "SSE.Views.ChartSettingsDlg.textNoOverlay": "没有叠加", "SSE.Views.ChartSettingsDlg.textOneCell": "移动但不调整单元格大小", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "刻度标记", "SSE.Views.ChartSettingsDlg.textOut": "退出", "SSE.Views.ChartSettingsDlg.textOuterTop": "外上", "SSE.Views.ChartSettingsDlg.textOverlay": "覆盖", "SSE.Views.ChartSettingsDlg.textReverse": "按相反顺序排列的值", "SSE.Views.ChartSettingsDlg.textReverseOrder": "相反的顺序", "SSE.Views.ChartSettingsDlg.textRight": "右", "SSE.Views.ChartSettingsDlg.textRightOverlay": "右侧覆盖", "SSE.Views.ChartSettingsDlg.textRotated": "已旋转", "SSE.Views.ChartSettingsDlg.textSameAll": "全都相同", "SSE.Views.ChartSettingsDlg.textSelectData": "选择数据", "SSE.Views.ChartSettingsDlg.textSeparator": "数据标签分隔符", "SSE.Views.ChartSettingsDlg.textSeriesName": "序列名称", "SSE.Views.ChartSettingsDlg.textShow": "显示", "SSE.Views.ChartSettingsDlg.textShowBorders": "显示图表边框", "SSE.Views.ChartSettingsDlg.textShowData": "以隐藏的行和列显示数据", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "显示空单元格", "SSE.Views.ChartSettingsDlg.textShowEquation": "在图表上显示方程式", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "显示轴", "SSE.Views.ChartSettingsDlg.textShowValues": "显示图表值", "SSE.Views.ChartSettingsDlg.textSingle": "单一的迷你图", "SSE.Views.ChartSettingsDlg.textSmooth": "光滑", "SSE.Views.ChartSettingsDlg.textSnap": "单元捕捉", "SSE.Views.ChartSettingsDlg.textSparkRanges": "迷你图范围", "SSE.Views.ChartSettingsDlg.textStraight": "直行", "SSE.Views.ChartSettingsDlg.textStyle": "样式", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "成千上万", "SSE.Views.ChartSettingsDlg.textTickOptions": "勾选选项", "SSE.Views.ChartSettingsDlg.textTitle": "图表 - 高级设置", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "迷你图-高级设置", "SSE.Views.ChartSettingsDlg.textTop": "顶部", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "趋势线参数", "SSE.Views.ChartSettingsDlg.textTrillions": "万亿", "SSE.Views.ChartSettingsDlg.textTwoCell": "移动并调整单元格大小", "SSE.Views.ChartSettingsDlg.textType": "类型", "SSE.Views.ChartSettingsDlg.textTypeData": "类型和数据", "SSE.Views.ChartSettingsDlg.textUnits": "显示单位", "SSE.Views.ChartSettingsDlg.textValue": "值", "SSE.Views.ChartSettingsDlg.textVertAxis": "纵轴", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "副纵轴", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X轴标题", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y轴标题", "SSE.Views.ChartSettingsDlg.textZero": "零", "SSE.Views.ChartSettingsDlg.txtEmpty": "这是必填栏", "SSE.Views.ChartTypeDialog.errorComboSeries": "若要创建组合图表，请至少选择两个系列的数据。", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "所选图表类型需要现有图表正在使用的辅助轴。选择其他图表类型。", "SSE.Views.ChartTypeDialog.textSecondary": "副轴", "SSE.Views.ChartTypeDialog.textSeries": "序列", "SSE.Views.ChartTypeDialog.textStyle": "样式", "SSE.Views.ChartTypeDialog.textTitle": "图表类型", "SSE.Views.ChartTypeDialog.textType": "类型", "SSE.Views.ChartWizardDialog.errorComboSeries": "若要创建组合图表，请至少选择两个系列的数据。", "SSE.Views.ChartWizardDialog.errorMaxPoints": "每个图表的最大串联点数为4096。", "SSE.Views.ChartWizardDialog.errorMaxRows": "每个图表的最大数据序列数为255。", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "所选图表类型需要现有图表正在使用的辅助轴。选择其他图表类型。", "SSE.Views.ChartWizardDialog.errorStockChart": "行顺序不正确。要创建股票图表，请按以下顺序将数据放在工作表上：开盘价、最高价格、最低价格、收盘价。", "SSE.Views.ChartWizardDialog.textRecommended": "推荐的", "SSE.Views.ChartWizardDialog.textSecondary": "副轴", "SSE.Views.ChartWizardDialog.textSeries": "序列", "SSE.Views.ChartWizardDialog.textTitle": "插入图表", "SSE.Views.ChartWizardDialog.textTitleChange": "更改图表类型", "SSE.Views.ChartWizardDialog.textType": "类型", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "选择数据系列的图表类型和轴", "SSE.Views.CreatePivotDialog.textDataRange": "源数据范围", "SSE.Views.CreatePivotDialog.textDestination": "选择要放置表格的位置", "SSE.Views.CreatePivotDialog.textExist": "现有工作表", "SSE.Views.CreatePivotDialog.textInvalidRange": "无效的单元格范围", "SSE.Views.CreatePivotDialog.textNew": "新工作表", "SSE.Views.CreatePivotDialog.textSelectData": "选择数据", "SSE.Views.CreatePivotDialog.textTitle": "创建数据透视表", "SSE.Views.CreatePivotDialog.txtEmpty": "这是必填栏", "SSE.Views.CreateSparklineDialog.textDataRange": "源数据范围", "SSE.Views.CreateSparklineDialog.textDestination": "选择迷你图放置位置", "SSE.Views.CreateSparklineDialog.textInvalidRange": "无效的单元格范围", "SSE.Views.CreateSparklineDialog.textSelectData": "选择数据", "SSE.Views.CreateSparklineDialog.textTitle": "创建迷你图", "SSE.Views.CreateSparklineDialog.txtEmpty": "这是必填栏", "SSE.Views.DataTab.capBtnGroup": "组", "SSE.Views.DataTab.capBtnTextCustomSort": "自定义排序", "SSE.Views.DataTab.capBtnTextDataValidation": "数据验证", "SSE.Views.DataTab.capBtnTextRemDuplicates": "删除重复项", "SSE.Views.DataTab.capBtnTextToCol": "分列", "SSE.Views.DataTab.capBtnUngroup": "取消组合", "SSE.Views.DataTab.capDataExternalLinks": "外部链接", "SSE.Views.DataTab.capDataFromText": "获取数据", "SSE.Views.DataTab.capGoalSeek": "单变量求解", "SSE.Views.DataTab.mniFromFile": "来自本地TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "从TXT/CSV网址", "SSE.Views.DataTab.mniFromXMLFile": "来自本地XML", "SSE.Views.DataTab.textBelow": "详细信息下方的摘要行", "SSE.Views.DataTab.textClear": "清除大纲", "SSE.Views.DataTab.textColumns": "取消对列的分组", "SSE.Views.DataTab.textGroupColumns": "组合列", "SSE.Views.DataTab.textGroupRows": "分组行", "SSE.Views.DataTab.textRightOf": "详细信息右侧的摘要列", "SSE.Views.DataTab.textRows": "取消对行的分组", "SSE.Views.DataTab.tipCustomSort": "自定义排序", "SSE.Views.DataTab.tipDataFromText": "从文件中获取数据", "SSE.Views.DataTab.tipDataValidation": "数据验证", "SSE.Views.DataTab.tipExternalLinks": "查看此电子表格链接到的其他文件", "SSE.Views.DataTab.tipGoalSeek": "找到所需值的正确输入", "SSE.Views.DataTab.tipGroup": "单元格的组范围", "SSE.Views.DataTab.tipRemDuplicates": "从工作表中删除重复的行", "SSE.Views.DataTab.tipToColumns": "将单元格文本分隔成列", "SSE.Views.DataTab.tipUngroup": "取消对单元格范围的分组", "SSE.Views.DataValidationDialog.errorFormula": "此值当前计算结果为错误。您想要继续吗？", "SSE.Views.DataValidationDialog.errorInvalid": "您为字段“｛0｝”输入的值无效。", "SSE.Views.DataValidationDialog.errorInvalidDate": "您为字段“｛0｝”输入的日期无效。", "SSE.Views.DataValidationDialog.errorInvalidList": "列表源必须是分隔的列表，或者是对单行或单列的引用。", "SSE.Views.DataValidationDialog.errorInvalidTime": "您为字段“｛0｝”输入的时间无效。", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "“｛1｝”字段必须大于或等于“｛0｝”字段。", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "您必须在字段“｛0｝”和字段“{1｝”中都输入一个值。", "SSE.Views.DataValidationDialog.errorMustEnterValue": "您必须在字段“｛0｝”中输入一个值。", "SSE.Views.DataValidationDialog.errorNamedRange": "找不到您指定的命名区域。", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "在条件“｛0｝”中不能使用负值。", "SSE.Views.DataValidationDialog.errorNotNumeric": "字段“｛0｝”必须是数值、数值表达式或引用包含数值的单元格。", "SSE.Views.DataValidationDialog.strError": "错误警报", "SSE.Views.DataValidationDialog.strInput": "输入消息", "SSE.Views.DataValidationDialog.strSettings": "设置", "SSE.Views.DataValidationDialog.textAlert": "警示", "SSE.Views.DataValidationDialog.textAllow": "允许", "SSE.Views.DataValidationDialog.textApply": "将这些更改应用于具有相同设置的所有其他单元格", "SSE.Views.DataValidationDialog.textCellSelected": "选择单元格后，显示此输入消息", "SSE.Views.DataValidationDialog.textCompare": "相比于", "SSE.Views.DataValidationDialog.textData": "数据", "SSE.Views.DataValidationDialog.textEndDate": "结束日期", "SSE.Views.DataValidationDialog.textEndTime": "结束时间", "SSE.Views.DataValidationDialog.textError": "错误消息", "SSE.Views.DataValidationDialog.textFormula": "公式", "SSE.Views.DataValidationDialog.textIgnore": "忽略空白", "SSE.Views.DataValidationDialog.textInput": "输入消息", "SSE.Views.DataValidationDialog.textMax": "最大值", "SSE.Views.DataValidationDialog.textMessage": "消息", "SSE.Views.DataValidationDialog.textMin": "最小值", "SSE.Views.DataValidationDialog.textSelectData": "选择数据", "SSE.Views.DataValidationDialog.textShowDropDown": "在单元格中显示下拉列表", "SSE.Views.DataValidationDialog.textShowError": "输入无效数据后显示错误警报", "SSE.Views.DataValidationDialog.textShowInput": "选择单元格时显示输入消息", "SSE.Views.DataValidationDialog.textSource": "来源", "SSE.Views.DataValidationDialog.textStartDate": "开始日期", "SSE.Views.DataValidationDialog.textStartTime": "开始时间", "SSE.Views.DataValidationDialog.textStop": "停止", "SSE.Views.DataValidationDialog.textStyle": "样式", "SSE.Views.DataValidationDialog.textTitle": "标题", "SSE.Views.DataValidationDialog.textUserEnters": "当用户输入无效数据时，显示此错误警报", "SSE.Views.DataValidationDialog.txtAny": "任何值", "SSE.Views.DataValidationDialog.txtBetween": "位于之间", "SSE.Views.DataValidationDialog.txtDate": "日期", "SSE.Views.DataValidationDialog.txtDecimal": "十进制", "SSE.Views.DataValidationDialog.txtElTime": "所用时间", "SSE.Views.DataValidationDialog.txtEndDate": "结束日期", "SSE.Views.DataValidationDialog.txtEndTime": "结束时间", "SSE.Views.DataValidationDialog.txtEqual": "等于", "SSE.Views.DataValidationDialog.txtGreaterThan": "大于", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "大于等于", "SSE.Views.DataValidationDialog.txtLength": "长度", "SSE.Views.DataValidationDialog.txtLessThan": "小于", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "小于或等于", "SSE.Views.DataValidationDialog.txtList": "列表", "SSE.Views.DataValidationDialog.txtNotBetween": "不介于", "SSE.Views.DataValidationDialog.txtNotEqual": "不等于", "SSE.Views.DataValidationDialog.txtOther": "其它", "SSE.Views.DataValidationDialog.txtStartDate": "开始日期", "SSE.Views.DataValidationDialog.txtStartTime": "开始时间", "SSE.Views.DataValidationDialog.txtTextLength": "文本长度", "SSE.Views.DataValidationDialog.txtTime": "时间", "SSE.Views.DataValidationDialog.txtWhole": "完整的数字", "SSE.Views.DigitalFilterDialog.capAnd": "和", "SSE.Views.DigitalFilterDialog.capCondition1": "等于", "SSE.Views.DigitalFilterDialog.capCondition10": "不结束于", "SSE.Views.DigitalFilterDialog.capCondition11": "包含", "SSE.Views.DigitalFilterDialog.capCondition12": "不含", "SSE.Views.DigitalFilterDialog.capCondition2": "不等于", "SSE.Views.DigitalFilterDialog.capCondition3": "大于", "SSE.Views.DigitalFilterDialog.capCondition30": "在之后", "SSE.Views.DigitalFilterDialog.capCondition4": "大于或等于", "SSE.Views.DigitalFilterDialog.capCondition40": "在之后或等于", "SSE.Views.DigitalFilterDialog.capCondition5": "小于", "SSE.Views.DigitalFilterDialog.capCondition50": "在之前", "SSE.Views.DigitalFilterDialog.capCondition6": "小于或等于", "SSE.Views.DigitalFilterDialog.capCondition60": "在之前或等于", "SSE.Views.DigitalFilterDialog.capCondition7": "开头为", "SSE.Views.DigitalFilterDialog.capCondition8": "开头不是", "SSE.Views.DigitalFilterDialog.capCondition9": "结尾为", "SSE.Views.DigitalFilterDialog.capOr": "或", "SSE.Views.DigitalFilterDialog.textNoFilter": "无筛选", "SSE.Views.DigitalFilterDialog.textShowRows": "显示行", "SSE.Views.DigitalFilterDialog.textUse1": "使用 ？呈现任何单个字符", "SSE.Views.DigitalFilterDialog.textUse2": "使用*来呈现任何一系列的角色", "SSE.Views.DigitalFilterDialog.txtSelectDate": "选择日期", "SSE.Views.DigitalFilterDialog.txtTitle": "自定义筛选器", "SSE.Views.DocumentHolder.advancedEquationText": "方程式设置", "SSE.Views.DocumentHolder.advancedImgText": "高级图像设置", "SSE.Views.DocumentHolder.advancedShapeText": "形状高级设置", "SSE.Views.DocumentHolder.advancedSlicerText": "切片器高级设置", "SSE.Views.DocumentHolder.allLinearText": "全部-线性", "SSE.Views.DocumentHolder.allProfText": "全部-专业", "SSE.Views.DocumentHolder.bottomCellText": "底部对齐", "SSE.Views.DocumentHolder.bulletsText": "项目符号和编号", "SSE.Views.DocumentHolder.centerCellText": "居中对齐", "SSE.Views.DocumentHolder.chartDataText": "选择图表数据", "SSE.Views.DocumentHolder.chartText": "图表高级设置", "SSE.Views.DocumentHolder.chartTypeText": "更改图表类型", "SSE.Views.DocumentHolder.currLinearText": "当前-线性", "SSE.Views.DocumentHolder.currProfText": "当前-专业", "SSE.Views.DocumentHolder.deleteColumnText": "列", "SSE.Views.DocumentHolder.deleteRowText": "行", "SSE.Views.DocumentHolder.deleteTableText": "表格", "SSE.Views.DocumentHolder.direct270Text": "向上旋转文字", "SSE.Views.DocumentHolder.direct90Text": "向下旋转文字", "SSE.Views.DocumentHolder.directHText": "水平的", "SSE.Views.DocumentHolder.directionText": "文字方向", "SSE.Views.DocumentHolder.editChartText": "编辑数据", "SSE.Views.DocumentHolder.editHyperlinkText": "编辑超链接", "SSE.Views.DocumentHolder.hideEqToolbar": "隐藏公式工具栏", "SSE.Views.DocumentHolder.insertColumnLeftText": "左栏", "SSE.Views.DocumentHolder.insertColumnRightText": "右栏", "SSE.Views.DocumentHolder.insertRowAboveText": "上面的行", "SSE.Views.DocumentHolder.insertRowBelowText": "下面的行", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "实际大小", "SSE.Views.DocumentHolder.removeHyperlinkText": "删除超链接", "SSE.Views.DocumentHolder.selectColumnText": "整列", "SSE.Views.DocumentHolder.selectDataText": "列数据", "SSE.Views.DocumentHolder.selectRowText": "行", "SSE.Views.DocumentHolder.selectTableText": "表格", "SSE.Views.DocumentHolder.showEqToolbar": "显示公式工具栏", "SSE.Views.DocumentHolder.strDelete": "删除签名", "SSE.Views.DocumentHolder.strDetails": "签名详细信息", "SSE.Views.DocumentHolder.strSetup": "签名设置", "SSE.Views.DocumentHolder.strSign": "签署", "SSE.Views.DocumentHolder.textAlign": "对齐", "SSE.Views.DocumentHolder.textArrange": "安排", "SSE.Views.DocumentHolder.textArrangeBack": "置于底层", "SSE.Views.DocumentHolder.textArrangeBackward": "下移一层", "SSE.Views.DocumentHolder.textArrangeForward": "向前移动", "SSE.Views.DocumentHolder.textArrangeFront": "放到最上面", "SSE.Views.DocumentHolder.textAverage": "平均值", "SSE.Views.DocumentHolder.textBullets": "项目符号", "SSE.Views.DocumentHolder.textCopyCells": "复制单元格", "SSE.Views.DocumentHolder.textCount": "计数", "SSE.Views.DocumentHolder.textCrop": "裁剪", "SSE.Views.DocumentHolder.textCropFill": "填充", "SSE.Views.DocumentHolder.textCropFit": "适应", "SSE.Views.DocumentHolder.textEditPoints": "编辑点", "SSE.Views.DocumentHolder.textEntriesList": "从下拉列表中选择", "SSE.Views.DocumentHolder.textFillDays": "填充日期", "SSE.Views.DocumentHolder.textFillFormatOnly": "仅填充格式", "SSE.Views.DocumentHolder.textFillMonths": "填充月份", "SSE.Views.DocumentHolder.textFillSeries": "填充系列", "SSE.Views.DocumentHolder.textFillWeekdays": "填充星期", "SSE.Views.DocumentHolder.textFillWithoutFormat": "不带格式填充", "SSE.Views.DocumentHolder.textFillYears": "填充年份", "SSE.Views.DocumentHolder.textFlashFill": "快速填充", "SSE.Views.DocumentHolder.textFlipH": "水平翻转", "SSE.Views.DocumentHolder.textFlipV": "垂直翻转", "SSE.Views.DocumentHolder.textFreezePanes": "冻结窗格", "SSE.Views.DocumentHolder.textFromFile": "从文件", "SSE.Views.DocumentHolder.textFromStorage": "来自存储设备", "SSE.Views.DocumentHolder.textFromUrl": "来自URL", "SSE.Views.DocumentHolder.textGrowthTrend": "增长趋势", "SSE.Views.DocumentHolder.textLinearTrend": "线性趋势", "SSE.Views.DocumentHolder.textListSettings": "列表设置", "SSE.Views.DocumentHolder.textMacro": "指定宏", "SSE.Views.DocumentHolder.textMax": "最大值", "SSE.Views.DocumentHolder.textMin": "最小值", "SSE.Views.DocumentHolder.textMore": "更多函数", "SSE.Views.DocumentHolder.textMoreFormats": "更多格式", "SSE.Views.DocumentHolder.textNone": "无", "SSE.Views.DocumentHolder.textNumbering": "编号", "SSE.Views.DocumentHolder.textReplace": "替换图片", "SSE.Views.DocumentHolder.textResetCrop": "重置裁剪", "SSE.Views.DocumentHolder.textRotate": "旋转", "SSE.Views.DocumentHolder.textRotate270": "逆时针旋转90°", "SSE.Views.DocumentHolder.textRotate90": "顺时针旋转90°", "SSE.Views.DocumentHolder.textSaveAsPicture": "另存为图片", "SSE.Views.DocumentHolder.textSeries": "序列", "SSE.Views.DocumentHolder.textShapeAlignBottom": "底部对齐", "SSE.Views.DocumentHolder.textShapeAlignCenter": "居中对齐", "SSE.Views.DocumentHolder.textShapeAlignLeft": "左对齐", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "居中对齐", "SSE.Views.DocumentHolder.textShapeAlignRight": "右对齐", "SSE.Views.DocumentHolder.textShapeAlignTop": "顶端对齐", "SSE.Views.DocumentHolder.textShapesMerge": "合并形状", "SSE.Views.DocumentHolder.textStdDev": "标准差", "SSE.Views.DocumentHolder.textSum": "求和", "SSE.Views.DocumentHolder.textUndo": "撤消", "SSE.Views.DocumentHolder.textUnFreezePanes": "取消冻结窗格", "SSE.Views.DocumentHolder.textVar": "变量", "SSE.Views.DocumentHolder.tipMarkersArrow": "箭头项目符号", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "复选标记项目符号", "SSE.Views.DocumentHolder.tipMarkersDash": "连字符项目符号", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "实心菱形项目符号", "SSE.Views.DocumentHolder.tipMarkersFRound": "实心圆形项目符号", "SSE.Views.DocumentHolder.tipMarkersFSquare": "实心方形项目符号", "SSE.Views.DocumentHolder.tipMarkersHRound": "空心圆形项目符号", "SSE.Views.DocumentHolder.tipMarkersStar": "星形项目符号", "SSE.Views.DocumentHolder.topCellText": "顶端对齐", "SSE.Views.DocumentHolder.txtAccounting": "会计", "SSE.Views.DocumentHolder.txtAddComment": "添加批注", "SSE.Views.DocumentHolder.txtAddNamedRange": "定义名称", "SSE.Views.DocumentHolder.txtArrange": "安排", "SSE.Views.DocumentHolder.txtAscending": "上升的", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "自动调整列宽", "SSE.Views.DocumentHolder.txtAutoRowHeight": "自动调整行高", "SSE.Views.DocumentHolder.txtAverage": "平均值", "SSE.Views.DocumentHolder.txtCellFormat": "单元格格式", "SSE.Views.DocumentHolder.txtClear": "清除", "SSE.Views.DocumentHolder.txtClearAll": "全部", "SSE.Views.DocumentHolder.txtClearComments": "批注", "SSE.Views.DocumentHolder.txtClearFormat": "格式", "SSE.Views.DocumentHolder.txtClearHyper": "超链接", "SSE.Views.DocumentHolder.txtClearPivotField": "从{0}清除筛选", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "清除所选的迷你图组", "SSE.Views.DocumentHolder.txtClearSparklines": "清除所选的迷你曲", "SSE.Views.DocumentHolder.txtClearText": "文字", "SSE.Views.DocumentHolder.txtCollapse": "折叠", "SSE.Views.DocumentHolder.txtCollapseEntire": "折叠整个字段", "SSE.Views.DocumentHolder.txtColumn": "整列", "SSE.Views.DocumentHolder.txtColumnWidth": "设置列宽", "SSE.Views.DocumentHolder.txtCondFormat": "条件格式", "SSE.Views.DocumentHolder.txtCopy": "复制", "SSE.Views.DocumentHolder.txtCount": "计数", "SSE.Views.DocumentHolder.txtCurrency": "货币", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "自定义列宽", "SSE.Views.DocumentHolder.txtCustomRowHeight": "自定义行高", "SSE.Views.DocumentHolder.txtCustomSort": "自定义排序", "SSE.Views.DocumentHolder.txtCut": "剪切", "SSE.Views.DocumentHolder.txtDateLong": "长日期", "SSE.Views.DocumentHolder.txtDateShort": "短日期", "SSE.Views.DocumentHolder.txtDelete": "刪除", "SSE.Views.DocumentHolder.txtDelField": "删除", "SSE.Views.DocumentHolder.txtDescending": "降序", "SSE.Views.DocumentHolder.txtDifference": "不同于", "SSE.Views.DocumentHolder.txtDistribHor": "水平分布", "SSE.Views.DocumentHolder.txtDistribVert": "垂直分布", "SSE.Views.DocumentHolder.txtEditComment": "编辑批注", "SSE.Views.DocumentHolder.txtEditObject": "编辑对象", "SSE.Views.DocumentHolder.txtExpand": "展开", "SSE.Views.DocumentHolder.txtExpandCollapse": "展开/折叠", "SSE.Views.DocumentHolder.txtExpandEntire": "展开整个字段", "SSE.Views.DocumentHolder.txtFieldSettings": "字段设置", "SSE.Views.DocumentHolder.txtFilter": "筛选", "SSE.Views.DocumentHolder.txtFilterCellColor": "按单元格的颜色筛选", "SSE.Views.DocumentHolder.txtFilterFontColor": "按字体颜色筛选", "SSE.Views.DocumentHolder.txtFilterValue": "按选定单元格的值筛选", "SSE.Views.DocumentHolder.txtFormula": "插入函数", "SSE.Views.DocumentHolder.txtFraction": "分数", "SSE.Views.DocumentHolder.txtGeneral": "常规", "SSE.Views.DocumentHolder.txtGetLink": "获取此范围的链接", "SSE.Views.DocumentHolder.txtGrandTotal": "总计", "SSE.Views.DocumentHolder.txtGroup": "组", "SSE.Views.DocumentHolder.txtHide": "隐藏", "SSE.Views.DocumentHolder.txtIndex": "索引", "SSE.Views.DocumentHolder.txtInsert": "插入", "SSE.Views.DocumentHolder.txtInsHyperlink": "超链接", "SSE.Views.DocumentHolder.txtInsImage": "从文件中插入图片", "SSE.Views.DocumentHolder.txtInsImageUrl": "插入来自URL的图片", "SSE.Views.DocumentHolder.txtLabelFilter": "标签筛选", "SSE.Views.DocumentHolder.txtMax": "最大值", "SSE.Views.DocumentHolder.txtMin": "最小", "SSE.Views.DocumentHolder.txtMoreOptions": "更多选项", "SSE.Views.DocumentHolder.txtNormal": "没有计算", "SSE.Views.DocumentHolder.txtNumber": "数值", "SSE.Views.DocumentHolder.txtNumFormat": "数字格式", "SSE.Views.DocumentHolder.txtPaste": "粘贴", "SSE.Views.DocumentHolder.txtPercent": "的百分比", "SSE.Views.DocumentHolder.txtPercentage": "百分比", "SSE.Views.DocumentHolder.txtPercentDiff": "差异百分比", "SSE.Views.DocumentHolder.txtPercentOfCol": "列总和的%", "SSE.Views.DocumentHolder.txtPercentOfGrand": "列总计的百分比", "SSE.Views.DocumentHolder.txtPercentOfParent": "%父项合计的", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "父列总计的百分比", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "父总计的百分比", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "在…累计总和的%", "SSE.Views.DocumentHolder.txtPercentOfTotal": "行总和的百分比", "SSE.Views.DocumentHolder.txtPivotSettings": "数据透视表设置", "SSE.Views.DocumentHolder.txtProduct": "乘积", "SSE.Views.DocumentHolder.txtRankAscending": "从小到大排序", "SSE.Views.DocumentHolder.txtRankDescending": "从大到小排序", "SSE.Views.DocumentHolder.txtReapply": "重新应用", "SSE.Views.DocumentHolder.txtRefresh": "刷新", "SSE.Views.DocumentHolder.txtRow": "整行", "SSE.Views.DocumentHolder.txtRowHeight": "设置行高", "SSE.Views.DocumentHolder.txtRunTotal": "运行总计", "SSE.Views.DocumentHolder.txtScientific": "科学", "SSE.Views.DocumentHolder.txtSelect": "请选择", "SSE.Views.DocumentHolder.txtShiftDown": "向下移动单元格", "SSE.Views.DocumentHolder.txtShiftLeft": "向左移动单元格", "SSE.Views.DocumentHolder.txtShiftRight": "向右移动单元格", "SSE.Views.DocumentHolder.txtShiftUp": "向上移动单元格", "SSE.Views.DocumentHolder.txtShow": "显示", "SSE.Views.DocumentHolder.txtShowAs": "将值显示为", "SSE.Views.DocumentHolder.txtShowComment": "显示批注", "SSE.Views.DocumentHolder.txtShowDetails": "显示详细资料", "SSE.Views.DocumentHolder.txtSort": "分类", "SSE.Views.DocumentHolder.txtSortCellColor": "所选单元格颜色在顶部", "SSE.Views.DocumentHolder.txtSortFontColor": "选定的字体颜色在上面", "SSE.Views.DocumentHolder.txtSortOption": "更多排序选项", "SSE.Views.DocumentHolder.txtSparklines": "迷你图", "SSE.Views.DocumentHolder.txtSubtotalField": "小计", "SSE.Views.DocumentHolder.txtSum": "求和", "SSE.Views.DocumentHolder.txtSummarize": "值汇总依据", "SSE.Views.DocumentHolder.txtText": "文字", "SSE.Views.DocumentHolder.txtTextAdvanced": "段落高级设置", "SSE.Views.DocumentHolder.txtTime": "时间", "SSE.Views.DocumentHolder.txtTop10": "前十位", "SSE.Views.DocumentHolder.txtUngroup": "取消组合", "SSE.Views.DocumentHolder.txtValueFieldSettings": "值字段設置", "SSE.Views.DocumentHolder.txtValueFilter": "按值筛选", "SSE.Views.DocumentHolder.txtWidth": "宽度", "SSE.Views.DocumentHolder.unicodeText": "Unicode码", "SSE.Views.DocumentHolder.vertAlignText": "垂直对齐", "SSE.Views.ExternalLinksDlg.closeButtonText": "关闭", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "自动更新来自链接源的数据", "SSE.Views.ExternalLinksDlg.textChange": "更改来源", "SSE.Views.ExternalLinksDlg.textDelete": "断开链接", "SSE.Views.ExternalLinksDlg.textDeleteAll": "中断所有链接", "SSE.Views.ExternalLinksDlg.textOk": "确定", "SSE.Views.ExternalLinksDlg.textOpen": "开源", "SSE.Views.ExternalLinksDlg.textSource": "来源", "SSE.Views.ExternalLinksDlg.textStatus": "状态", "SSE.Views.ExternalLinksDlg.textUnknown": "未知", "SSE.Views.ExternalLinksDlg.textUpdate": "更新值", "SSE.Views.ExternalLinksDlg.textUpdateAll": "全部更新", "SSE.Views.ExternalLinksDlg.textUpdating": "正在更新中...", "SSE.Views.ExternalLinksDlg.txtTitle": "外部链接", "SSE.Views.FieldSettingsDialog.strLayout": "布局", "SSE.Views.FieldSettingsDialog.strSubtotals": "小计", "SSE.Views.FieldSettingsDialog.textNumFormat": "数字格式", "SSE.Views.FieldSettingsDialog.textReport": "报表", "SSE.Views.FieldSettingsDialog.textTitle": "字段设置", "SSE.Views.FieldSettingsDialog.txtAverage": "平均值", "SSE.Views.FieldSettingsDialog.txtBlank": "在每个项目后插入空行", "SSE.Views.FieldSettingsDialog.txtBottom": "显示在组的底部", "SSE.Views.FieldSettingsDialog.txtCompact": "紧凑", "SSE.Views.FieldSettingsDialog.txtCount": "计数", "SSE.Views.FieldSettingsDialog.txtCountNums": "计数", "SSE.Views.FieldSettingsDialog.txtCustomName": "自定义名称", "SSE.Views.FieldSettingsDialog.txtEmpty": "显示没有数据的项目", "SSE.Views.FieldSettingsDialog.txtMax": "最大值", "SSE.Views.FieldSettingsDialog.txtMin": "最小值", "SSE.Views.FieldSettingsDialog.txtOutline": "大纲", "SSE.Views.FieldSettingsDialog.txtProduct": "乘积", "SSE.Views.FieldSettingsDialog.txtRepeat": "在每行重复项目标签", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "显示小计", "SSE.Views.FieldSettingsDialog.txtSourceName": "来源名称", "SSE.Views.FieldSettingsDialog.txtStdDev": "标准差", "SSE.Views.FieldSettingsDialog.txtStdDevp": "标准差", "SSE.Views.FieldSettingsDialog.txtSum": "求和", "SSE.Views.FieldSettingsDialog.txtSummarize": "小计函数", "SSE.Views.FieldSettingsDialog.txtTabular": "表格式的", "SSE.Views.FieldSettingsDialog.txtTop": "显示在组的顶部", "SSE.Views.FieldSettingsDialog.txtVar": "变量", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.ariaFileMenu": "文件菜单", "SSE.Views.FileMenu.btnBackCaption": "打开文件所在位置", "SSE.Views.FileMenu.btnCloseEditor": "关闭文件", "SSE.Views.FileMenu.btnCloseMenuCaption": "返回", "SSE.Views.FileMenu.btnCreateNewCaption": "新建", "SSE.Views.FileMenu.btnDownloadCaption": "下载为", "SSE.Views.FileMenu.btnExitCaption": "关闭", "SSE.Views.FileMenu.btnExportToPDFCaption": "导出为PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "打开", "SSE.Views.FileMenu.btnHelpCaption": "帮助", "SSE.Views.FileMenu.btnHistoryCaption": "版本历史", "SSE.Views.FileMenu.btnInfoCaption": "信息", "SSE.Views.FileMenu.btnPrintCaption": "打印", "SSE.Views.FileMenu.btnProtectCaption": "保护", "SSE.Views.FileMenu.btnRecentFilesCaption": "打开最近", "SSE.Views.FileMenu.btnRenameCaption": "重新命名", "SSE.Views.FileMenu.btnReturnCaption": "回到电子表格", "SSE.Views.FileMenu.btnRightsCaption": "访问权限", "SSE.Views.FileMenu.btnSaveAsCaption": "另存为", "SSE.Views.FileMenu.btnSaveCaption": "保存", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "另存副本为", "SSE.Views.FileMenu.btnSettingsCaption": "高级设置", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "切换到移动模式", "SSE.Views.FileMenu.btnToEditCaption": "编辑电子表格", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "空白电子表格", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "新建", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "应用", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "新增作者", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "添加属性", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "添加文字", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "应用程序", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "作者", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "更改访问权限", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "批注", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "通用", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "已创建", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "文档属性", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "最后修改者", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "上一次更改", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "否", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "创建者", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "位置", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "属性", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "具有该标题的属性已存在", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "拥有权限的人", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "电子表格信息", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "主题", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "标签", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "标题", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "已上传", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "是", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "访问权限", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "更改访问权限", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "拥有权限的人", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "应用", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "共同编辑模式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "使用1904日期系统", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "小数分隔符", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "字典语言", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "启用迭代计算", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "快速", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "字体设置", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "公式语言", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "示例：\b总和;  最小; 最大;计数", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "显示功能提示", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "显示水平滚动条", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "忽略大写单词", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "忽略带数字的单词", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "宏设置", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "最大变化", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "最大迭代次数", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "粘贴内容时显示“粘贴选项”按钮", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "R1C1 参考样式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "区域设置", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "例：", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTL 界面 (文字从右到左)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "在工作表中显示注释", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "显示来自其他用户的更改", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "显示已解决的批注", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "滚动时对齐网格", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "严格", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "选项卡样式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "界面主题", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "千位分隔符", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "测量单位", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "根据区域设置使用分隔符", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "显示垂直滚动条", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "默认缩放值", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "每10分钟", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "每30分钟", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "每5分钟", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "每隔一小时", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "自动恢复", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "自动保存", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "已禁用", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "填写", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "保存中间版本", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "线", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "每隔一分钟", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "参考样式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "高级设置", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "外观", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "自动更正选项...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "白俄罗斯语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "保加利亚语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "加泰罗尼亚语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "默认缓存模式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "计算中", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "厘米", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "协作", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "捷克語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "自定义快速访问", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "丹麦语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "德語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "编辑并保存", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "希腊语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "英语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "您的启动项无法使用。可能需要整数或十进制数。", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "西班牙语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "实时共同编辑。所有更改都会自动保存", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "芬兰语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "法语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "匈牙利語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "亚美尼亚语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "印度尼西亚语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "英寸", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "意大利语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "日文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "韩文", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "上次使用", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "老挝语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "拉脱维亚语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "按照 OS X 样式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "本地", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "挪威语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "荷兰语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "波兰语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "校对", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "点", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "葡萄牙语（巴西）", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "葡萄牙语（葡萄牙）", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "编辑器标题栏显示“快速打印”按钮", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "文档将打印到最近选择的打印机或者默认打印机", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "地区", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "罗马尼亚语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "俄语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "全部启用", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "启用全部宏，不显示通知", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "打开屏幕朗读器支持", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "默认纸张方向", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "此设置仅对新工作表有影响", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "从左到右", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "从右到左", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "斯洛伐克語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "斯洛文尼亚语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "全部停用", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "禁用全部宏，不显示通知", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "使用「儲存」按鈕同步您和其他人所做的更改", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "瑞典語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "使用工具栏颜色作为选项卡背景", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "土耳其语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "乌克兰语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "按 Alt 键后可通过键盘在用户界面中导航", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "用Option键使用键盘浏览用户界面", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "越南语", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "显示通知", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "禁用全部宏，并显示通知", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "按照 Windows 样式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "工作区", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "中文", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "警告", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "密码保护", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "保护电子表格", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "签名保护", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "有效签名已添加到电子表格中<br>电子表格受到保护，不可编辑。", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "通过添加<br>不可见的数字签名来确保电子表格的完整性", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "编辑电子表格", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "编辑将删除电子表格中的签名<br>是否继续？", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "此电子表格已受密码保护", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "使用密码加密此电子表格", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "此电子表格需要签名。", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "有效签名已添加到电子表格中。电子表格受到保护，不可编辑。", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "电子表格中的某些数字签名无效或无法验证。电子表格受到保护，不可编辑。", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "查看签名", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "下载为", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "另存副本为", "SSE.Views.FillSeriesDialog.textAuto": "自动填充", "SSE.Views.FillSeriesDialog.textCols": "列", "SSE.Views.FillSeriesDialog.textDate": "日期", "SSE.Views.FillSeriesDialog.textDateUnit": "日期单位", "SSE.Views.FillSeriesDialog.textDay": "日", "SSE.Views.FillSeriesDialog.textGrowth": "增长", "SSE.Views.FillSeriesDialog.textLinear": "线性", "SSE.Views.FillSeriesDialog.textMonth": "月", "SSE.Views.FillSeriesDialog.textRows": "行", "SSE.Views.FillSeriesDialog.textSeries": "序列产生在", "SSE.Views.FillSeriesDialog.textStep": "步进值", "SSE.Views.FillSeriesDialog.textStop": "步进值", "SSE.Views.FillSeriesDialog.textTitle": "序列", "SSE.Views.FillSeriesDialog.textTrend": "趋势", "SSE.Views.FillSeriesDialog.textType": "类型", "SSE.Views.FillSeriesDialog.textWeek": "星期", "SSE.Views.FillSeriesDialog.textYear": "年", "SSE.Views.FillSeriesDialog.txtErrorNumber": "您的启动项无法使用。可能需要整数或十进制数。", "SSE.Views.FormatRulesEditDlg.fillColor": "填充顏色", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "警告", "SSE.Views.FormatRulesEditDlg.text2Scales": "2色标", "SSE.Views.FormatRulesEditDlg.text3Scales": "3色标", "SSE.Views.FormatRulesEditDlg.textAllBorders": "所有边框", "SSE.Views.FormatRulesEditDlg.textAppearance": "条形外观", "SSE.Views.FormatRulesEditDlg.textApply": "应用于范围", "SSE.Views.FormatRulesEditDlg.textAutomatic": "自动", "SSE.Views.FormatRulesEditDlg.textAxis": "轴", "SSE.Views.FormatRulesEditDlg.textBarDirection": "条形方向", "SSE.Views.FormatRulesEditDlg.textBold": "粗体", "SSE.Views.FormatRulesEditDlg.textBorder": "边框", "SSE.Views.FormatRulesEditDlg.textBordersColor": "边框颜色", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "边框样式", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "底部边框", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "无法新增条件格式设定。", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "单元格中点", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "内部垂直边框", "SSE.Views.FormatRulesEditDlg.textClear": "清除", "SSE.Views.FormatRulesEditDlg.textColor": "文字颜色", "SSE.Views.FormatRulesEditDlg.textContext": "上下文", "SSE.Views.FormatRulesEditDlg.textCustom": "自定义", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "对角线下边框", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "对角线上边框", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "请输入有效的公式。", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "您输入的公式的计算结果不是数字、日期、时间或字符串。", "SSE.Views.FormatRulesEditDlg.textEmptyText": "输入一个值。", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "您输入的值不是有效的数字、日期、时间或字符串。", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "｛0｝的值必须大于｛1｝的数值。", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "请输入一个介于｛0｝和｛1｝之间的数字。", "SSE.Views.FormatRulesEditDlg.textFill": "填充", "SSE.Views.FormatRulesEditDlg.textFormat": "格式", "SSE.Views.FormatRulesEditDlg.textFormula": "公式", "SSE.Views.FormatRulesEditDlg.textGradient": "渐变", "SSE.Views.FormatRulesEditDlg.textIconLabel": "当｛0｝｛1｝和", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "当｛0｝｛1｝时", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "当值为", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "一个或多个图标数据范围重叠<br>调整图标数据范围值，使范围不重叠。", "SSE.Views.FormatRulesEditDlg.textIconStyle": "图标样式", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "内部边框", "SSE.Views.FormatRulesEditDlg.textInvalid": "无效的单元格范围。", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "错误！无效的单元格范围", "SSE.Views.FormatRulesEditDlg.textItalic": "斜体", "SSE.Views.FormatRulesEditDlg.textItem": "项目", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "从左到右", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "左边框", "SSE.Views.FormatRulesEditDlg.textLongBar": "最长条", "SSE.Views.FormatRulesEditDlg.textMaximum": "最大值", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "最大点", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "内水平边框", "SSE.Views.FormatRulesEditDlg.textMidpoint": "中点", "SSE.Views.FormatRulesEditDlg.textMinimum": "最小值", "SSE.Views.FormatRulesEditDlg.textMinpoint": "最小点", "SSE.Views.FormatRulesEditDlg.textNegative": "负面的", "SSE.Views.FormatRulesEditDlg.textNewColor": "更多顏色", "SSE.Views.FormatRulesEditDlg.textNoBorders": "无边框", "SSE.Views.FormatRulesEditDlg.textNone": "无", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "一个或多个指定的值不是有效的百分比。", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "指定的{0}值不是有效的百分比。", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "一个或多个指定的值不是有效的百分比。", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "指定的{0}值不是有效的百分位数。", "SSE.Views.FormatRulesEditDlg.textOutBorders": "外部边框", "SSE.Views.FormatRulesEditDlg.textPercent": "百分比", "SSE.Views.FormatRulesEditDlg.textPercentile": "百分位数", "SSE.Views.FormatRulesEditDlg.textPosition": "位置", "SSE.Views.FormatRulesEditDlg.textPositive": "正数", "SSE.Views.FormatRulesEditDlg.textPresets": "预设", "SSE.Views.FormatRulesEditDlg.textPreview": "预览", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "不能在色标、数据栏和图标集的条件格式标准中使用相对引用。", "SSE.Views.FormatRulesEditDlg.textReverse": "反转图标顺序", "SSE.Views.FormatRulesEditDlg.textRight2Left": "从右到左", "SSE.Views.FormatRulesEditDlg.textRightBorders": "右边界", "SSE.Views.FormatRulesEditDlg.textRule": "规则", "SSE.Views.FormatRulesEditDlg.textSameAs": "等同于正", "SSE.Views.FormatRulesEditDlg.textSelectData": "选择数据", "SSE.Views.FormatRulesEditDlg.textShortBar": "最短的条", "SSE.Views.FormatRulesEditDlg.textShowBar": "仅显示条", "SSE.Views.FormatRulesEditDlg.textShowIcon": "仅显示图标", "SSE.Views.FormatRulesEditDlg.textSingleRef": "这种类型的引用不能用于条件格式设置公式<br>更改对单个单元格的引用，或将引用与工作表函数一起使用，例如=SUM（A1:B5）。", "SSE.Views.FormatRulesEditDlg.textSolid": "实心", "SSE.Views.FormatRulesEditDlg.textStrikeout": "删除", "SSE.Views.FormatRulesEditDlg.textSubscript": "下标", "SSE.Views.FormatRulesEditDlg.textSuperscript": "上标", "SSE.Views.FormatRulesEditDlg.textTopBorders": "顶部边框", "SSE.Views.FormatRulesEditDlg.textUnderline": "下划线", "SSE.Views.FormatRulesEditDlg.tipBorders": "边框", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "数字格式", "SSE.Views.FormatRulesEditDlg.txtAccounting": "会计", "SSE.Views.FormatRulesEditDlg.txtCurrency": "货币", "SSE.Views.FormatRulesEditDlg.txtDate": "日期", "SSE.Views.FormatRulesEditDlg.txtDateLong": "长日期", "SSE.Views.FormatRulesEditDlg.txtDateShort": "短日期", "SSE.Views.FormatRulesEditDlg.txtEmpty": "这是必填栏", "SSE.Views.FormatRulesEditDlg.txtFraction": "分数", "SSE.Views.FormatRulesEditDlg.txtGeneral": "常规", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "无图标", "SSE.Views.FormatRulesEditDlg.txtNumber": "数值", "SSE.Views.FormatRulesEditDlg.txtPercentage": "百分比", "SSE.Views.FormatRulesEditDlg.txtScientific": "科学", "SSE.Views.FormatRulesEditDlg.txtText": "文字", "SSE.Views.FormatRulesEditDlg.txtTime": "时间", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "编辑格式规则", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "新格式规则", "SSE.Views.FormatRulesManagerDlg.guestText": "访客", "SSE.Views.FormatRulesManagerDlg.lockText": "已锁定", "SSE.Views.FormatRulesManagerDlg.text1Above": "高于平均值1个标准偏差", "SSE.Views.FormatRulesManagerDlg.text1Below": "低于平均值1个标准偏差", "SSE.Views.FormatRulesManagerDlg.text2Above": "高于平均值2个标准偏差", "SSE.Views.FormatRulesManagerDlg.text2Below": "低于平均值2个标准偏差", "SSE.Views.FormatRulesManagerDlg.text3Above": "高于平均值3个标准偏差", "SSE.Views.FormatRulesManagerDlg.text3Below": "低于平均值3个标准偏差", "SSE.Views.FormatRulesManagerDlg.textAbove": "平均水平以上", "SSE.Views.FormatRulesManagerDlg.textApply": "应用于", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "单元格值开头为", "SSE.Views.FormatRulesManagerDlg.textBelow": "在平均值以下", "SSE.Views.FormatRulesManagerDlg.textBetween": "介于 {0} 和 {1} 之间", "SSE.Views.FormatRulesManagerDlg.textCellValue": "单元格值", "SSE.Views.FormatRulesManagerDlg.textColorScale": "分级色阶", "SSE.Views.FormatRulesManagerDlg.textContains": "单元格值包含", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "单元格包含空白值", "SSE.Views.FormatRulesManagerDlg.textContainsError": "单元格包含错误", "SSE.Views.FormatRulesManagerDlg.textDelete": "刪除", "SSE.Views.FormatRulesManagerDlg.textDown": "下移规则", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "重复值", "SSE.Views.FormatRulesManagerDlg.textEdit": "编辑", "SSE.Views.FormatRulesManagerDlg.textEnds": "单元格值以结尾", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "等于或高于平均值", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "等于或低于平均值", "SSE.Views.FormatRulesManagerDlg.textFormat": "格式", "SSE.Views.FormatRulesManagerDlg.textIconSet": "图标集", "SSE.Views.FormatRulesManagerDlg.textNew": "新建", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "不在 {0} 和 {1} 之间", "SSE.Views.FormatRulesManagerDlg.textNotContains": "单元格值不包含", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "单元格不包含空值", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "单元格不包含错误", "SSE.Views.FormatRulesManagerDlg.textRules": "规则", "SSE.Views.FormatRulesManagerDlg.textScope": "显示的格式规则", "SSE.Views.FormatRulesManagerDlg.textSelectData": "选择数据", "SSE.Views.FormatRulesManagerDlg.textSelection": "当前所选", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "此数据透视表", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "此工作表", "SSE.Views.FormatRulesManagerDlg.textThisTable": "此表", "SSE.Views.FormatRulesManagerDlg.textUnique": "唯一值", "SSE.Views.FormatRulesManagerDlg.textUp": "上移规则", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "此元素正在被其他用户编辑。", "SSE.Views.FormatRulesManagerDlg.txtTitle": "条件格式", "SSE.Views.FormatSettingsDialog.textCategory": "分类", "SSE.Views.FormatSettingsDialog.textDecimal": "十进制", "SSE.Views.FormatSettingsDialog.textFormat": "格式", "SSE.Views.FormatSettingsDialog.textLinked": "链接到来源", "SSE.Views.FormatSettingsDialog.textSeparator": "使用1000分隔符", "SSE.Views.FormatSettingsDialog.textSymbols": "符号", "SSE.Views.FormatSettingsDialog.textTitle": "数字格式", "SSE.Views.FormatSettingsDialog.txtAccounting": "会计", "SSE.Views.FormatSettingsDialog.txtAs10": "作为十分之一（5/10）", "SSE.Views.FormatSettingsDialog.txtAs100": "作为百分之五十 (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "作为十六分之八 (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "作为一半（1/2）", "SSE.Views.FormatSettingsDialog.txtAs4": "作为四分之一（2/4）", "SSE.Views.FormatSettingsDialog.txtAs8": "八分之一（4/8）", "SSE.Views.FormatSettingsDialog.txtCurrency": "货币", "SSE.Views.FormatSettingsDialog.txtCustom": "自定义", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "请仔细输入自定义数字格式。电子表格编辑器不会检查自定义格式中是否存在可能影响xlsx文件的错误。", "SSE.Views.FormatSettingsDialog.txtDate": "日期", "SSE.Views.FormatSettingsDialog.txtFraction": "分数", "SSE.Views.FormatSettingsDialog.txtGeneral": "常规", "SSE.Views.FormatSettingsDialog.txtNone": "无", "SSE.Views.FormatSettingsDialog.txtNumber": "数值", "SSE.Views.FormatSettingsDialog.txtPercentage": "百分比", "SSE.Views.FormatSettingsDialog.txtSample": "样品：", "SSE.Views.FormatSettingsDialog.txtScientific": "科学", "SSE.Views.FormatSettingsDialog.txtText": "文字", "SSE.Views.FormatSettingsDialog.txtTime": "时间", "SSE.Views.FormatSettingsDialog.txtUpto1": "最多一位数（1/3）", "SSE.Views.FormatSettingsDialog.txtUpto2": "最多两位数（12/25）", "SSE.Views.FormatSettingsDialog.txtUpto3": "最多三位数（131/135）", "SSE.Views.FormulaDialog.sDescription": "描述", "SSE.Views.FormulaDialog.textGroupDescription": "选择函数组", "SSE.Views.FormulaDialog.textListDescription": "选择函数", "SSE.Views.FormulaDialog.txtRecommended": "推荐的", "SSE.Views.FormulaDialog.txtSearch": "搜索", "SSE.Views.FormulaDialog.txtTitle": "插入函数", "SSE.Views.FormulaTab.capBtnRemoveArr": "删除箭头", "SSE.Views.FormulaTab.capBtnTraceDep": "追踪从属单元格", "SSE.Views.FormulaTab.capBtnTracePrec": "追踪引用单元格", "SSE.Views.FormulaTab.textAutomatic": "自动", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "计算当前工作表", "SSE.Views.FormulaTab.textCalculateWorkbook": "计算工作簿", "SSE.Views.FormulaTab.textManual": "手动更改", "SSE.Views.FormulaTab.tipCalculate": "计算", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "计算整个工作簿", "SSE.Views.FormulaTab.tipRemoveArr": "删除由跟踪先例或跟踪从属项绘制的箭头", "SSE.Views.FormulaTab.tipShowFormulas": "在每个单元格中显示公式，而不是结果值", "SSE.Views.FormulaTab.tipTraceDep": "显示箭头，指示哪些单元格受选定单元格值的影响", "SSE.Views.FormulaTab.tipTracePrec": "显示箭头，指示哪些单元格会影响选定单元格的值", "SSE.Views.FormulaTab.tipWatch": "将单元格添加到监视窗口列表", "SSE.Views.FormulaTab.txtAdditional": "更多", "SSE.Views.FormulaTab.txtAutosum": "自动求和", "SSE.Views.FormulaTab.txtAutosumTip": "求和", "SSE.Views.FormulaTab.txtCalculation": "计算", "SSE.Views.FormulaTab.txtFormula": "函数", "SSE.Views.FormulaTab.txtFormulaTip": "插入函数", "SSE.Views.FormulaTab.txtMore": "更多函数", "SSE.Views.FormulaTab.txtRecent": "最近使用的", "SSE.Views.FormulaTab.txtRemDep": "删除从属项箭头", "SSE.Views.FormulaTab.txtRemPrec": "删除引用单元格箭头", "SSE.Views.FormulaTab.txtShowFormulas": "显示公式", "SSE.Views.FormulaTab.txtWatch": "监视窗口", "SSE.Views.FormulaWizard.textAny": "任何", "SSE.Views.FormulaWizard.textArgument": "参数", "SSE.Views.FormulaWizard.textFunction": "函数", "SSE.Views.FormulaWizard.textFunctionRes": "函数结果", "SSE.Views.FormulaWizard.textHelp": "有关此函数的帮助", "SSE.Views.FormulaWizard.textLogical": "合乎逻辑", "SSE.Views.FormulaWizard.textNoArgs": "此函数没有参数", "SSE.Views.FormulaWizard.textNoArgsDesc": "该参数没有描述", "SSE.Views.FormulaWizard.textNumber": "数值", "SSE.Views.FormulaWizard.textReadMore": "了解更多", "SSE.Views.FormulaWizard.textRef": "引用", "SSE.Views.FormulaWizard.textText": "文本", "SSE.Views.FormulaWizard.textTitle": "函数参数", "SSE.Views.FormulaWizard.textValue": "公式结果", "SSE.Views.GoalSeekDlg.textChangingCell": "通过更改单元格", "SSE.Views.GoalSeekDlg.textDataRangeError": "该公式缺少范围", "SSE.Views.GoalSeekDlg.textMustContainFormula": "单元格必须包含公式", "SSE.Views.GoalSeekDlg.textMustContainValue": "单元格必须包含一个值", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "单元格中的公式结果必须为一个数字", "SSE.Views.GoalSeekDlg.textMustSingleCell": "引用必须指向单个单元格", "SSE.Views.GoalSeekDlg.textSelectData": "选择数据", "SSE.Views.GoalSeekDlg.textSetCell": "设置单元格", "SSE.Views.GoalSeekDlg.textTitle": "单变量求解", "SSE.Views.GoalSeekDlg.textToValue": "改为值", "SSE.Views.GoalSeekDlg.txtEmpty": "这是必填栏", "SSE.Views.GoalSeekDlg.txtErrorNumber": "您的启动项无法使用。可能需要整数或十进制数。", "SSE.Views.GoalSeekStatusDlg.textContinue": "继续", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "当前值：", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "使用单元格｛0｝进行单变量求解已找到解。", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "使用单元格｛0｝进行单变量求解可能找不到解。", "SSE.Views.GoalSeekStatusDlg.textPause": "暂停", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "在迭代#｛1｝中使用单元格｛0｝进行单变量求解。", "SSE.Views.GoalSeekStatusDlg.textStep": "Step", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "目标值：", "SSE.Views.GoalSeekStatusDlg.textTitle": "单变量求解状态", "SSE.Views.HeaderFooterDialog.textAlign": "与页面边界对齐", "SSE.Views.HeaderFooterDialog.textAll": "所有页面", "SSE.Views.HeaderFooterDialog.textBold": "粗体", "SSE.Views.HeaderFooterDialog.textCenter": "中心", "SSE.Views.HeaderFooterDialog.textColor": "文字颜色", "SSE.Views.HeaderFooterDialog.textDate": "日期", "SSE.Views.HeaderFooterDialog.textDiffFirst": "首页不同", "SSE.Views.HeaderFooterDialog.textDiffOdd": "奇偶页不同", "SSE.Views.HeaderFooterDialog.textEven": "偶数页", "SSE.Views.HeaderFooterDialog.textFileName": "文件名", "SSE.Views.HeaderFooterDialog.textFirst": "首页", "SSE.Views.HeaderFooterDialog.textFooter": "页脚", "SSE.Views.HeaderFooterDialog.textHeader": "页眉", "SSE.Views.HeaderFooterDialog.textImage": "图片", "SSE.Views.HeaderFooterDialog.textInsert": "插入", "SSE.Views.HeaderFooterDialog.textItalic": "斜体", "SSE.Views.HeaderFooterDialog.textLeft": "左侧", "SSE.Views.HeaderFooterDialog.textMaxError": "您输入的文本字符串太长。减少使用的字符数。", "SSE.Views.HeaderFooterDialog.textNewColor": "更多颜色", "SSE.Views.HeaderFooterDialog.textOdd": "奇数页", "SSE.Views.HeaderFooterDialog.textPageCount": "页数", "SSE.Views.HeaderFooterDialog.textPageNum": "页码", "SSE.Views.HeaderFooterDialog.textPresets": "预设", "SSE.Views.HeaderFooterDialog.textRight": "右", "SSE.Views.HeaderFooterDialog.textScale": "随文档缩放", "SSE.Views.HeaderFooterDialog.textSheet": "工作表名称", "SSE.Views.HeaderFooterDialog.textStrikeout": "删除线", "SSE.Views.HeaderFooterDialog.textSubscript": "下标", "SSE.Views.HeaderFooterDialog.textSuperscript": "上标", "SSE.Views.HeaderFooterDialog.textTime": "时间", "SSE.Views.HeaderFooterDialog.textTitle": "页眉/页脚设置", "SSE.Views.HeaderFooterDialog.textUnderline": "下划线", "SSE.Views.HeaderFooterDialog.tipFontName": "字体 ", "SSE.Views.HeaderFooterDialog.tipFontSize": "字体大小", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "显示", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "链接到", "SSE.Views.HyperlinkSettingsDialog.strRange": "范围", "SSE.Views.HyperlinkSettingsDialog.strSheet": "工作表", "SSE.Views.HyperlinkSettingsDialog.textCopy": "复制", "SSE.Views.HyperlinkSettingsDialog.textDefault": "选择范围", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "在这里输入标题", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "在这里输入链接", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "在这里输入工具提示", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "外部链接", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "获取链接", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "内部数据范围", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "错误！无效的单元格范围", "SSE.Views.HyperlinkSettingsDialog.textNames": "定义名称", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "选择数据", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "选择文件", "SSE.Views.HyperlinkSettingsDialog.textSheets": "工作表", "SSE.Views.HyperlinkSettingsDialog.textTipText": "屏幕提示文字", "SSE.Views.HyperlinkSettingsDialog.textTitle": "超链接设置", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "这是必填栏", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "该字段应该是“http://www.example.com”格式的URL", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "此字段限制为2083个字符", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "输入网址或选择文件", "SSE.Views.ImageSettings.strTransparency": "透明度", "SSE.Views.ImageSettings.textAdvanced": "显示高级设置", "SSE.Views.ImageSettings.textCrop": "裁剪", "SSE.Views.ImageSettings.textCropFill": "填充", "SSE.Views.ImageSettings.textCropFit": "适应", "SSE.Views.ImageSettings.textCropToShape": "裁剪成形状", "SSE.Views.ImageSettings.textEdit": "编辑", "SSE.Views.ImageSettings.textEditObject": "编辑对象", "SSE.Views.ImageSettings.textFlip": "翻转", "SSE.Views.ImageSettings.textFromFile": "从文件", "SSE.Views.ImageSettings.textFromStorage": "从存储设备", "SSE.Views.ImageSettings.textFromUrl": "来自URL", "SSE.Views.ImageSettings.textHeight": "高度", "SSE.Views.ImageSettings.textHint270": "逆时针旋转90°", "SSE.Views.ImageSettings.textHint90": "顺时针旋转90°", "SSE.Views.ImageSettings.textHintFlipH": "水平翻转", "SSE.Views.ImageSettings.textHintFlipV": "垂直翻转", "SSE.Views.ImageSettings.textInsert": "替换图片", "SSE.Views.ImageSettings.textKeepRatio": "恒定比例", "SSE.Views.ImageSettings.textOriginalSize": "实际大小", "SSE.Views.ImageSettings.textRecentlyUsed": "最近使用的", "SSE.Views.ImageSettings.textResetCrop": "重置裁剪", "SSE.Views.ImageSettings.textRotate90": "旋转90°", "SSE.Views.ImageSettings.textRotation": "旋转", "SSE.Views.ImageSettings.textSize": "大小", "SSE.Views.ImageSettings.textWidth": "宽度", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "不随单元格移动或调整大小", "SSE.Views.ImageSettingsAdvanced.textAlt": "替代文本", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "描述", "SSE.Views.ImageSettingsAdvanced.textAltTip": "视觉对象信息的另一种基于文本的表示方式，将读取给视力或认知障碍的人，以帮助他们更好地理解图像、形状、图表或表格中的信息。", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "标题", "SSE.Views.ImageSettingsAdvanced.textAngle": "角度", "SSE.Views.ImageSettingsAdvanced.textFlipped": "已翻转的", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "水平地", "SSE.Views.ImageSettingsAdvanced.textOneCell": "移动但不调整单元格大小", "SSE.Views.ImageSettingsAdvanced.textRotation": "旋转", "SSE.Views.ImageSettingsAdvanced.textSnap": "单元捕捉", "SSE.Views.ImageSettingsAdvanced.textTitle": "图片 - 高级设置", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "移动并调整单元格大小", "SSE.Views.ImageSettingsAdvanced.textVertically": "垂直", "SSE.Views.ImportFromXmlDialog.textDestination": "选择数据的放置位置", "SSE.Views.ImportFromXmlDialog.textExist": "现有工作表", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "无效的单元格范围", "SSE.Views.ImportFromXmlDialog.textNew": "新工作表", "SSE.Views.ImportFromXmlDialog.textSelectData": "选择数据", "SSE.Views.ImportFromXmlDialog.textTitle": "导入数据", "SSE.Views.ImportFromXmlDialog.txtEmpty": "这是必填栏", "SSE.Views.LeftMenu.ariaLeftMenu": "左侧菜单", "SSE.Views.LeftMenu.tipAbout": "关于", "SSE.Views.LeftMenu.tipChat": "聊天", "SSE.Views.LeftMenu.tipComments": "批注", "SSE.Views.LeftMenu.tipFile": "文件", "SSE.Views.LeftMenu.tipPlugins": "插件", "SSE.Views.LeftMenu.tipSearch": "查找", "SSE.Views.LeftMenu.tipSpellcheck": "拼写检查", "SSE.Views.LeftMenu.tipSupport": "反馈和支持", "SSE.Views.LeftMenu.txtDeveloper": "开发者模式", "SSE.Views.LeftMenu.txtEditor": "电子表格编辑器", "SSE.Views.LeftMenu.txtLimit": "限制访问", "SSE.Views.LeftMenu.txtTrial": "试用模式", "SSE.Views.LeftMenu.txtTrialDev": "试用开发者模式", "SSE.Views.MacroDialog.textMacro": "宏名称", "SSE.Views.MacroDialog.textTitle": "指定宏", "SSE.Views.MainSettingsPrint.okButtonText": "保存", "SSE.Views.MainSettingsPrint.strBottom": "底部", "SSE.Views.MainSettingsPrint.strLandscape": "横向", "SSE.Views.MainSettingsPrint.strLeft": "左侧", "SSE.Views.MainSettingsPrint.strMargins": "边距", "SSE.Views.MainSettingsPrint.strPortrait": "直向方向", "SSE.Views.MainSettingsPrint.strPrint": "打印", "SSE.Views.MainSettingsPrint.strPrintTitles": "打印标题", "SSE.Views.MainSettingsPrint.strRight": "右", "SSE.Views.MainSettingsPrint.strTop": "顶部", "SSE.Views.MainSettingsPrint.textActualSize": "实际大小", "SSE.Views.MainSettingsPrint.textCustom": "自定义", "SSE.Views.MainSettingsPrint.textCustomOptions": "自定义选项", "SSE.Views.MainSettingsPrint.textFitCols": "将所有列调整到一页", "SSE.Views.MainSettingsPrint.textFitPage": "将工作表调整为一页", "SSE.Views.MainSettingsPrint.textFitRows": "将所有行放在一页上", "SSE.Views.MainSettingsPrint.textPageOrientation": "页面方向", "SSE.Views.MainSettingsPrint.textPageScaling": "缩放", "SSE.Views.MainSettingsPrint.textPageSize": "页面大小", "SSE.Views.MainSettingsPrint.textPrintGrid": "打印网格线", "SSE.Views.MainSettingsPrint.textPrintHeadings": "打印行和列标题", "SSE.Views.MainSettingsPrint.textRepeat": "重复...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "在左侧重复列", "SSE.Views.MainSettingsPrint.textRepeatTop": "在顶部重复行", "SSE.Views.MainSettingsPrint.textSettings": "设置", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "当前无法编辑现有的命名范围，也无法创建新的命名范围<br>，因为其中一些正在编辑中。", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "定义名称", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "警告", "SSE.Views.NamedRangeEditDlg.strWorkbook": "工作簿", "SSE.Views.NamedRangeEditDlg.textDataRange": "数据范围", "SSE.Views.NamedRangeEditDlg.textExistName": "错误！具有这种名称的范围已经存在", "SSE.Views.NamedRangeEditDlg.textInvalidName": "名称必须以字母或下划线开头，不能包含无效字符。", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "错误！单元格范围无效", "SSE.Views.NamedRangeEditDlg.textIsLocked": "错误！此元素正在被其他用户编辑。", "SSE.Views.NamedRangeEditDlg.textName": "名称", "SSE.Views.NamedRangeEditDlg.textReservedName": "您尝试使用的名称已在单元格公式中引用。请使用其他名称。", "SSE.Views.NamedRangeEditDlg.textScope": "范围", "SSE.Views.NamedRangeEditDlg.textSelectData": "选择数据", "SSE.Views.NamedRangeEditDlg.txtEmpty": "这是必填栏", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "编辑名称", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "新名称", "SSE.Views.NamedRangePasteDlg.textNames": "命名范围", "SSE.Views.NamedRangePasteDlg.txtTitle": "粘贴名称", "SSE.Views.NameManagerDlg.closeButtonText": "关闭", "SSE.Views.NameManagerDlg.guestText": "访客", "SSE.Views.NameManagerDlg.lockText": "已锁定", "SSE.Views.NameManagerDlg.textDataRange": "数据范围", "SSE.Views.NameManagerDlg.textDelete": "刪除", "SSE.Views.NameManagerDlg.textEdit": "编辑", "SSE.Views.NameManagerDlg.textEmpty": "没有创建命名范围。<br>创建至少一个命名范围，它将显示在此字段中。", "SSE.Views.NameManagerDlg.textFilter": "筛选", "SSE.Views.NameManagerDlg.textFilterAll": "全部", "SSE.Views.NameManagerDlg.textFilterDefNames": "定义名称", "SSE.Views.NameManagerDlg.textFilterSheet": "工作表范围内的名称", "SSE.Views.NameManagerDlg.textFilterTableNames": "表格名称", "SSE.Views.NameManagerDlg.textFilterWorkbook": "名称适用于工作簿", "SSE.Views.NameManagerDlg.textNew": "新建", "SSE.Views.NameManagerDlg.textnoNames": "没有找到与您的过滤器匹配的命名范围", "SSE.Views.NameManagerDlg.textRanges": "命名范围", "SSE.Views.NameManagerDlg.textScope": "范围", "SSE.Views.NameManagerDlg.textWorkbook": "工作簿", "SSE.Views.NameManagerDlg.tipIsLocked": "此元素正在被其他用户编辑。", "SSE.Views.NameManagerDlg.txtTitle": "名称管理", "SSE.Views.NameManagerDlg.warnDelete": "是否确实要删除名称｛0｝？", "SSE.Views.PageMarginsDialog.textBottom": "底部", "SSE.Views.PageMarginsDialog.textCenter": "页面居中", "SSE.Views.PageMarginsDialog.textHor": "水平地", "SSE.Views.PageMarginsDialog.textLeft": "左侧", "SSE.Views.PageMarginsDialog.textRight": "右", "SSE.Views.PageMarginsDialog.textTitle": "边距", "SSE.Views.PageMarginsDialog.textTop": "顶部", "SSE.Views.PageMarginsDialog.textVert": "垂直地", "SSE.Views.PageMarginsDialog.textWarning": "警告", "SSE.Views.PageMarginsDialog.warnCheckMargings": "边距不正确", "SSE.Views.ParagraphSettings.strLineHeight": "行间距", "SSE.Views.ParagraphSettings.strParagraphSpacing": "段落间距", "SSE.Views.ParagraphSettings.strSpacingAfter": "之后", "SSE.Views.ParagraphSettings.strSpacingBefore": "之前", "SSE.Views.ParagraphSettings.textAdvanced": "显示高级设置", "SSE.Views.ParagraphSettings.textAt": "在", "SSE.Views.ParagraphSettings.textAtLeast": "至少", "SSE.Views.ParagraphSettings.textAuto": "多个", "SSE.Views.ParagraphSettings.textExact": "精确", "SSE.Views.ParagraphSettings.txtAutoText": "自动", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "指定的选项卡将显示在此字段中", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "全部大写", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "双删除线", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "缩进", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "左侧", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "行间距", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "对", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "之后", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "之前", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "特殊", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "根据", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "字体 ", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "缩进和间距", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "小型大写字母", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "间距", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "删除线", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "下标", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "上标", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "标签", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "对齐", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "多个", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "字符间距", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "默认选项卡", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "效果", "SSE.Views.ParagraphSettingsAdvanced.textExact": "精确", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "第一行", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "悬挂", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "两端对齐", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "（空）", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "删除", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "删除所有", "SSE.Views.ParagraphSettingsAdvanced.textSet": "指定", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "中心", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "左侧", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "标签的位置", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "右", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "段落 - 高级设置", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "自动", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "删除", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "复制", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "编辑", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "公式", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "项目名称", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "新建", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "计算项", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "等於", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "不结束于", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "包含", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "不含", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "位于之间", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "不介于", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "不等于", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "大于", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "大于或等于", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "小于", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "小于或等于", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "开头为", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "开头不是", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "结尾为", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "显示标签所如下的项目：", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "显示以下项目：", "SSE.Views.PivotDigitalFilterDialog.textUse1": "使用 ？呈现任何单个字符", "SSE.Views.PivotDigitalFilterDialog.textUse2": "使用*来呈现任何一系列的角色", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "和", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "标签筛选", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "按值筛选", "SSE.Views.PivotGroupDialog.textAuto": "自动", "SSE.Views.PivotGroupDialog.textBy": "根据", "SSE.Views.PivotGroupDialog.textDays": "天", "SSE.Views.PivotGroupDialog.textEnd": "结束于", "SSE.Views.PivotGroupDialog.textError": "此字段必须是数值", "SSE.Views.PivotGroupDialog.textGreaterError": "结束编号必须大于开始编号", "SSE.Views.PivotGroupDialog.textHour": "小时", "SSE.Views.PivotGroupDialog.textMin": "分钟", "SSE.Views.PivotGroupDialog.textMonth": "月", "SSE.Views.PivotGroupDialog.textNumDays": "天数", "SSE.Views.PivotGroupDialog.textQuart": "季度", "SSE.Views.PivotGroupDialog.textSec": "秒", "SSE.Views.PivotGroupDialog.textStart": "开始于", "SSE.Views.PivotGroupDialog.textYear": "年份", "SSE.Views.PivotGroupDialog.txtTitle": "分组", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "您可以使用计算项对单个字段内的不同项进行基本计算", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "公式", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "插入公式", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "项目", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "项目名称", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "项目", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "了解更多", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "插入计算项至", "SSE.Views.PivotSettings.textAdvanced": "显示高级设置", "SSE.Views.PivotSettings.textColumns": "列", "SSE.Views.PivotSettings.textFields": "选择字段", "SSE.Views.PivotSettings.textFilters": "筛选", "SSE.Views.PivotSettings.textRows": "行", "SSE.Views.PivotSettings.textValues": "值", "SSE.Views.PivotSettings.txtAddColumn": "添加到列", "SSE.Views.PivotSettings.txtAddFilter": "添加到筛选器", "SSE.Views.PivotSettings.txtAddRow": "添加到行", "SSE.Views.PivotSettings.txtAddValues": "新增至值", "SSE.Views.PivotSettings.txtFieldSettings": "字段设置", "SSE.Views.PivotSettings.txtMoveBegin": "移至开头", "SSE.Views.PivotSettings.txtMoveColumn": "移动到列", "SSE.Views.PivotSettings.txtMoveDown": "下移", "SSE.Views.PivotSettings.txtMoveEnd": "移至末尾", "SSE.Views.PivotSettings.txtMoveFilter": "转到筛选", "SSE.Views.PivotSettings.txtMoveRow": "移至行", "SSE.Views.PivotSettings.txtMoveUp": "上移", "SSE.Views.PivotSettings.txtMoveValues": "移动到值", "SSE.Views.PivotSettings.txtRemove": "删除字段", "SSE.Views.PivotSettingsAdvanced.strLayout": "名称和布局", "SSE.Views.PivotSettingsAdvanced.textAlt": "替代文本", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "描述", "SSE.Views.PivotSettingsAdvanced.textAltTip": "视觉对象信息的替代的基于文本的表示，将被视为具有视觉或认知障碍的人阅读，以帮助他们更好地了解图像，自动图像，图表或表中的哪些信息。", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "标题", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "更新时自动调整列宽", "SSE.Views.PivotSettingsAdvanced.textDataRange": "数据范围", "SSE.Views.PivotSettingsAdvanced.textDataSource": "数据来源", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "在报告筛选区域中显示字段", "SSE.Views.PivotSettingsAdvanced.textDown": "向下，然后结束", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "总计", "SSE.Views.PivotSettingsAdvanced.textHeaders": "字段标题", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "错误！无效的单元格范围", "SSE.Views.PivotSettingsAdvanced.textOver": "先行后列", "SSE.Views.PivotSettingsAdvanced.textSelectData": "选择数据", "SSE.Views.PivotSettingsAdvanced.textShowCols": "为列显示", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "显示行和列的字段标题", "SSE.Views.PivotSettingsAdvanced.textShowRows": "显示行", "SSE.Views.PivotSettingsAdvanced.textTitle": "数据透视表-高级设置", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "按列的报表筛选字段", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "按行报告筛选字段", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "这是必填栏", "SSE.Views.PivotSettingsAdvanced.txtName": "名称", "SSE.Views.PivotShowDetailDialog.textDescription": "选择含有要显示信息的字段：", "SSE.Views.PivotShowDetailDialog.txtTitle": "显示详情", "SSE.Views.PivotTable.capBlankRows": "空白行", "SSE.Views.PivotTable.capGrandTotals": "总计", "SSE.Views.PivotTable.capLayout": "报表布局", "SSE.Views.PivotTable.capSubtotals": "小计", "SSE.Views.PivotTable.mniBottomSubtotals": "在组底部显示所有小计", "SSE.Views.PivotTable.mniInsertBlankLine": "在每个项目后插入空行", "SSE.Views.PivotTable.mniLayoutCompact": "以紧凑形式显示", "SSE.Views.PivotTable.mniLayoutNoRepeat": "不要重复所有项目标签", "SSE.Views.PivotTable.mniLayoutOutline": "以大纲形式显示", "SSE.Views.PivotTable.mniLayoutRepeat": "重复所有项目标签", "SSE.Views.PivotTable.mniLayoutTabular": "以表格形式显示", "SSE.Views.PivotTable.mniNoSubtotals": "不显示小计", "SSE.Views.PivotTable.mniOffTotals": "对于行和列关闭", "SSE.Views.PivotTable.mniOnColumnsTotals": "仅对列启用", "SSE.Views.PivotTable.mniOnRowsTotals": "仅对行启用", "SSE.Views.PivotTable.mniOnTotals": "对于行和列启用", "SSE.Views.PivotTable.mniRemoveBlankLine": "删除每个项目后的空行", "SSE.Views.PivotTable.mniTopSubtotals": "在组顶部显示所有小计", "SSE.Views.PivotTable.textColBanded": "镶边列", "SSE.Views.PivotTable.textColHeader": "列标题", "SSE.Views.PivotTable.textRowBanded": "镶边行", "SSE.Views.PivotTable.textRowHeader": "行标头", "SSE.Views.PivotTable.tipCalculatedItems": "计算项", "SSE.Views.PivotTable.tipCreatePivot": "插入数据透视表", "SSE.Views.PivotTable.tipGrandTotals": "显示或隐藏总计", "SSE.Views.PivotTable.tipRefresh": "更新数据源中的信息", "SSE.Views.PivotTable.tipRefreshCurrent": "更新当前表的数据源信息", "SSE.Views.PivotTable.tipSelect": "选择整个数据透视表", "SSE.Views.PivotTable.tipSubtotals": "显示或隐藏小计", "SSE.Views.PivotTable.txtCalculatedItems": "计算项", "SSE.Views.PivotTable.txtCollapseEntire": "折叠整个字段", "SSE.Views.PivotTable.txtCreate": "插入表格", "SSE.Views.PivotTable.txtExpandEntire": "展开整个字段", "SSE.Views.PivotTable.txtGroupPivot_Custom": "自定义", "SSE.Views.PivotTable.txtGroupPivot_Dark": "深色", "SSE.Views.PivotTable.txtGroupPivot_Light": "浅色", "SSE.Views.PivotTable.txtGroupPivot_Medium": "中等", "SSE.Views.PivotTable.txtPivotTable": "数据透视表", "SSE.Views.PivotTable.txtRefresh": "刷新", "SSE.Views.PivotTable.txtRefreshAll": "全部刷新", "SSE.Views.PivotTable.txtSelect": "请选择", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "数据透视表深色样式", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "数据透视表浅色样式", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "数据透视表中等样式", "SSE.Views.PrintSettings.btnDownload": "保存和下载", "SSE.Views.PrintSettings.btnExport": "保存和导出", "SSE.Views.PrintSettings.btnPrint": "保存并打印", "SSE.Views.PrintSettings.strBottom": "底部", "SSE.Views.PrintSettings.strLandscape": "橫向", "SSE.Views.PrintSettings.strLeft": "左侧", "SSE.Views.PrintSettings.strMargins": "边距", "SSE.Views.PrintSettings.strPortrait": "直向方向", "SSE.Views.PrintSettings.strPrint": "打印", "SSE.Views.PrintSettings.strPrintTitles": "打印标题", "SSE.Views.PrintSettings.strRight": "右", "SSE.Views.PrintSettings.strShow": "显示", "SSE.Views.PrintSettings.strTop": "顶部", "SSE.Views.PrintSettings.textActiveSheets": "使用中的工作表", "SSE.Views.PrintSettings.textActualSize": "实际大小", "SSE.Views.PrintSettings.textAllSheets": "所有工作表", "SSE.Views.PrintSettings.textCurrentSheet": "当前工作表", "SSE.Views.PrintSettings.textCustom": "自定义", "SSE.Views.PrintSettings.textCustomOptions": "自定义选项", "SSE.Views.PrintSettings.textFitCols": "将所有列适合一页", "SSE.Views.PrintSettings.textFitPage": "将工作表调整为一页", "SSE.Views.PrintSettings.textFitRows": "将所有行放在一页上", "SSE.Views.PrintSettings.textHideDetails": "隐藏细节", "SSE.Views.PrintSettings.textIgnore": "忽略打印区域", "SSE.Views.PrintSettings.textLayout": "布局", "SSE.Views.PrintSettings.textMarginsNarrow": "狭窄", "SSE.Views.PrintSettings.textMarginsNormal": "正常", "SSE.Views.PrintSettings.textMarginsWide": "宽", "SSE.Views.PrintSettings.textPageOrientation": "页面方向", "SSE.Views.PrintSettings.textPages": "页面：", "SSE.Views.PrintSettings.textPageScaling": "缩放", "SSE.Views.PrintSettings.textPageSize": "頁面大小", "SSE.Views.PrintSettings.textPrintGrid": "打印网格线", "SSE.Views.PrintSettings.textPrintHeadings": "打印行和列标题", "SSE.Views.PrintSettings.textPrintRange": "打印范围", "SSE.Views.PrintSettings.textRange": "范围", "SSE.Views.PrintSettings.textRepeat": "重复...", "SSE.Views.PrintSettings.textRepeatLeft": "在左侧重复列", "SSE.Views.PrintSettings.textRepeatTop": "在顶部重复行", "SSE.Views.PrintSettings.textSelection": "选择", "SSE.Views.PrintSettings.textSettings": "工作表设置", "SSE.Views.PrintSettings.textShowDetails": "显示详细资料", "SSE.Views.PrintSettings.textShowGrid": "显示网格线", "SSE.Views.PrintSettings.textShowHeadings": "显示行和列标题", "SSE.Views.PrintSettings.textTitle": "打印设置", "SSE.Views.PrintSettings.textTitlePDF": "PDF设置", "SSE.Views.PrintSettings.textTo": "到", "SSE.Views.PrintSettings.txtMarginsLast": "上次自定义", "SSE.Views.PrintTitlesDialog.textFirstCol": "第一列", "SSE.Views.PrintTitlesDialog.textFirstRow": "第一行", "SSE.Views.PrintTitlesDialog.textFrozenCols": "冻结的列", "SSE.Views.PrintTitlesDialog.textFrozenRows": "冻结的行", "SSE.Views.PrintTitlesDialog.textInvalidRange": "错误！无效的单元格范围", "SSE.Views.PrintTitlesDialog.textLeft": "在左侧重复列", "SSE.Views.PrintTitlesDialog.textNoRepeat": "不要重复", "SSE.Views.PrintTitlesDialog.textRepeat": "重复...", "SSE.Views.PrintTitlesDialog.textSelectRange": "选择范围", "SSE.Views.PrintTitlesDialog.textTitle": "打印标题", "SSE.Views.PrintTitlesDialog.textTop": "在顶部重复行", "SSE.Views.PrintWithPreview.txtActiveSheets": "使用中的工作表", "SSE.Views.PrintWithPreview.txtActualSize": "实际大小", "SSE.Views.PrintWithPreview.txtAllSheets": "所有工作表", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "应用于所有工作表", "SSE.Views.PrintWithPreview.txtBothSides": "双面打印", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "长边翻页", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "短边翻页", "SSE.Views.PrintWithPreview.txtBottom": "底部", "SSE.Views.PrintWithPreview.txtCopies": "副本", "SSE.Views.PrintWithPreview.txtCurrentSheet": "当前工作表", "SSE.Views.PrintWithPreview.txtCustom": "自定义", "SSE.Views.PrintWithPreview.txtCustomOptions": "自定义选项", "SSE.Views.PrintWithPreview.txtEmptyTable": "由于表为空，没有可打印的内容", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "首页编号：", "SSE.Views.PrintWithPreview.txtFitCols": "将所有列调整到一页", "SSE.Views.PrintWithPreview.txtFitPage": "将工作表调整为一页", "SSE.Views.PrintWithPreview.txtFitRows": "将所有行放在一页上", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "网格线和标题", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "页眉/页脚设置", "SSE.Views.PrintWithPreview.txtIgnore": "忽略打印区域", "SSE.Views.PrintWithPreview.txtLandscape": "横向", "SSE.Views.PrintWithPreview.txtLeft": "左侧", "SSE.Views.PrintWithPreview.txtMargins": "边距", "SSE.Views.PrintWithPreview.txtMarginsLast": "上次自定义", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "狭窄", "SSE.Views.PrintWithPreview.txtMarginsNormal": "正常", "SSE.Views.PrintWithPreview.txtMarginsWide": "宽", "SSE.Views.PrintWithPreview.txtOf": "共 {0} 页", "SSE.Views.PrintWithPreview.txtOneSide": "单面打印", "SSE.Views.PrintWithPreview.txtOneSideDesc": "只打印单面", "SSE.Views.PrintWithPreview.txtPage": "页面", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "页码无效", "SSE.Views.PrintWithPreview.txtPageOrientation": "页面方向", "SSE.Views.PrintWithPreview.txtPages": "页面：", "SSE.Views.PrintWithPreview.txtPageSize": "页面大小", "SSE.Views.PrintWithPreview.txtPortrait": "直向方向", "SSE.Views.PrintWithPreview.txtPrint": "打印", "SSE.Views.PrintWithPreview.txtPrintGrid": "打印网格线", "SSE.Views.PrintWithPreview.txtPrintHeadings": "打印行和列标题", "SSE.Views.PrintWithPreview.txtPrintRange": "打印范围", "SSE.Views.PrintWithPreview.txtPrintSides": "打印面", "SSE.Views.PrintWithPreview.txtPrintTitles": "打印标题", "SSE.Views.PrintWithPreview.txtPrintToPDF": "打印为 PDF", "SSE.Views.PrintWithPreview.txtRepeat": "重复...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "在左侧重复列", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "在顶部重复行", "SSE.Views.PrintWithPreview.txtRight": "右", "SSE.Views.PrintWithPreview.txtSave": "保存", "SSE.Views.PrintWithPreview.txtScaling": "缩放", "SSE.Views.PrintWithPreview.txtSelection": "选择", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "工作表的设置", "SSE.Views.PrintWithPreview.txtSheet": "工作表：{0}", "SSE.Views.PrintWithPreview.txtTo": "到", "SSE.Views.PrintWithPreview.txtTop": "顶部", "SSE.Views.ProtectDialog.textExistName": "错误！具有此类标题的范围已存在", "SSE.Views.ProtectDialog.textInvalidName": "范围标题必须以字母开头，并且只能包含字母、数字和空格。。", "SSE.Views.ProtectDialog.textInvalidRange": "错误！无效的单元格范围", "SSE.Views.ProtectDialog.textSelectData": "选择数据", "SSE.Views.ProtectDialog.txtAllow": "允许此工作表的所有用户", "SSE.Views.ProtectDialog.txtAllowDescription": "您可以解锁特定范围以进行编辑。", "SSE.Views.ProtectDialog.txtAllowRanges": "允许编辑范围", "SSE.Views.ProtectDialog.txtAutofilter": "使用自动筛选", "SSE.Views.ProtectDialog.txtDelCols": "删除列", "SSE.Views.ProtectDialog.txtDelRows": "刪除行", "SSE.Views.ProtectDialog.txtEmpty": "这是必填栏", "SSE.Views.ProtectDialog.txtFormatCells": "单元格格式", "SSE.Views.ProtectDialog.txtFormatCols": "格式化列", "SSE.Views.ProtectDialog.txtFormatRows": "格式化行", "SSE.Views.ProtectDialog.txtIncorrectPwd": "确认密码不相同", "SSE.Views.ProtectDialog.txtInsCols": "插入列", "SSE.Views.ProtectDialog.txtInsHyper": "插入超链接", "SSE.Views.ProtectDialog.txtInsRows": "插入行", "SSE.Views.ProtectDialog.txtObjs": "编辑对象", "SSE.Views.ProtectDialog.txtOptional": "可选的", "SSE.Views.ProtectDialog.txtPassword": "密码", "SSE.Views.ProtectDialog.txtPivot": "使用透视表和透视图", "SSE.Views.ProtectDialog.txtProtect": "保护", "SSE.Views.ProtectDialog.txtRange": "范围", "SSE.Views.ProtectDialog.txtRangeName": "标题", "SSE.Views.ProtectDialog.txtRepeat": "重复密码", "SSE.Views.ProtectDialog.txtScen": "编辑方案", "SSE.Views.ProtectDialog.txtSelLocked": "选择锁定的单元格", "SSE.Views.ProtectDialog.txtSelUnLocked": "选择未锁定的单元格", "SSE.Views.ProtectDialog.txtSheetDescription": "防止其他用户编改，可限制其他用户的编辑权限。", "SSE.Views.ProtectDialog.txtSheetTitle": "保护工作表", "SSE.Views.ProtectDialog.txtSort": "分类", "SSE.Views.ProtectDialog.txtWarning": "警告：如果您丢失或忘记了密码，则无法恢复。请把它放在安全的地方。", "SSE.Views.ProtectDialog.txtWBDescription": "为了防止其他用户查看隐藏的工作表，添加、移动、删除或隐藏工作表以及重命名工作表，您可以使用密码保护工作簿的结构。", "SSE.Views.ProtectDialog.txtWBTitle": "保护工作簿结构", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "匿名用户", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "任何人", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "编辑", "SSE.Views.ProtectedRangesEditDlg.textCantView": "拒绝", "SSE.Views.ProtectedRangesEditDlg.textCanView": "查看", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "范围标题必须以字母开头，并且只能包含字母、数字和空格。。", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "错误！无效的单元格范围", "SSE.Views.ProtectedRangesEditDlg.textRemove": "删除", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "选择数据", "SSE.Views.ProtectedRangesEditDlg.textYou": "您", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "范围访问", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "这是必填栏", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "保护", "SSE.Views.ProtectedRangesEditDlg.txtRange": "范围", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "标题", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "只有您可以编辑此范围", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "开始键入姓名或电子邮件", "SSE.Views.ProtectedRangesManagerDlg.guestText": "访客", "SSE.Views.ProtectedRangesManagerDlg.lockText": "已锁定", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "刪除", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "编辑", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "尚未创建任何受保护的范围<br>至少创建一个受保护的范围，它将显示在此字段中。", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "筛选", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "全部", "SSE.Views.ProtectedRangesManagerDlg.textNew": "新建", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "保护工作表", "SSE.Views.ProtectedRangesManagerDlg.textRange": "范围", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "您可以为选定的对象限制编辑范围。", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "标题", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "此元素正在被其他用户编辑。", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "访问", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "拒绝", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "编辑", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "编辑范围", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "新范围", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "受保护的范围", "SSE.Views.ProtectedRangesManagerDlg.txtView": "视图", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "是否确实要删除受保护的范围｛0｝<br>任何对电子表格具有编辑访问权限的人都可以编辑该范围内的内容。", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "是否确实要删除受保护的范围<br>任何对电子表格具有编辑访问权限的人都可以编辑这些范围内的内容。", "SSE.Views.ProtectRangesDlg.guestText": "访客", "SSE.Views.ProtectRangesDlg.lockText": "已锁定", "SSE.Views.ProtectRangesDlg.textDelete": "刪除", "SSE.Views.ProtectRangesDlg.textEdit": "编辑", "SSE.Views.ProtectRangesDlg.textEmpty": "无范围可编辑", "SSE.Views.ProtectRangesDlg.textNew": "新建", "SSE.Views.ProtectRangesDlg.textProtect": "保护工作表", "SSE.Views.ProtectRangesDlg.textPwd": "密码", "SSE.Views.ProtectRangesDlg.textRange": "范围", "SSE.Views.ProtectRangesDlg.textRangesDesc": "工作表受保护时由密码解锁的范围（这仅适用于锁定的单元格）", "SSE.Views.ProtectRangesDlg.textTitle": "标题", "SSE.Views.ProtectRangesDlg.tipIsLocked": "此元素正在被其他用户编辑。", "SSE.Views.ProtectRangesDlg.txtEditRange": "编辑范围", "SSE.Views.ProtectRangesDlg.txtNewRange": "新范围", "SSE.Views.ProtectRangesDlg.txtNo": "否", "SSE.Views.ProtectRangesDlg.txtTitle": "允许用户编辑范围", "SSE.Views.ProtectRangesDlg.txtYes": "是", "SSE.Views.ProtectRangesDlg.warnDelete": "是否确实要删除名称｛0｝？", "SSE.Views.RemoveDuplicatesDialog.textColumns": "列", "SSE.Views.RemoveDuplicatesDialog.textDescription": "若要删除重复的值，请选择一个或多个包含重复值的列。", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "我的数据有标题", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "全选", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "删除重复项", "SSE.Views.RightMenu.ariaRightMenu": "右侧菜单", "SSE.Views.RightMenu.txtCellSettings": "单元格设置", "SSE.Views.RightMenu.txtChartSettings": "图表设置", "SSE.Views.RightMenu.txtImageSettings": "图像设置", "SSE.Views.RightMenu.txtParagraphSettings": "段落设置", "SSE.Views.RightMenu.txtPivotSettings": "数据透视表设置", "SSE.Views.RightMenu.txtSettings": "通用设置", "SSE.Views.RightMenu.txtShapeSettings": "形状设置", "SSE.Views.RightMenu.txtSignatureSettings": "签名设置", "SSE.Views.RightMenu.txtSlicerSettings": "切片器設置", "SSE.Views.RightMenu.txtSparklineSettings": "迷你图设置", "SSE.Views.RightMenu.txtTableSettings": "表格设置", "SSE.Views.RightMenu.txtTextArtSettings": "艺术字设置", "SSE.Views.ScaleDialog.textAuto": "自动", "SSE.Views.ScaleDialog.textError": "输入的值不正确。", "SSE.Views.ScaleDialog.textFewPages": "页面", "SSE.Views.ScaleDialog.textFitTo": "调整至", "SSE.Views.ScaleDialog.textHeight": "高度", "SSE.Views.ScaleDialog.textManyPages": "页面", "SSE.Views.ScaleDialog.textOnePage": "页面", "SSE.Views.ScaleDialog.textScaleTo": "缩放到", "SSE.Views.ScaleDialog.textTitle": "缩放设置", "SSE.Views.ScaleDialog.textWidth": "宽度", "SSE.Views.SetValueDialog.txtMaxText": "该字段的最大值为{0}", "SSE.Views.SetValueDialog.txtMinText": "该字段的最小值为{0}", "SSE.Views.ShapeSettings.strBackground": "背景颜色", "SSE.Views.ShapeSettings.strChange": "更改形状", "SSE.Views.ShapeSettings.strColor": "颜色", "SSE.Views.ShapeSettings.strFill": "填充", "SSE.Views.ShapeSettings.strForeground": "前景色", "SSE.Views.ShapeSettings.strPattern": "模式", "SSE.Views.ShapeSettings.strShadow": "显示阴影", "SSE.Views.ShapeSettings.strSize": "粗细", "SSE.Views.ShapeSettings.strStroke": "折线图", "SSE.Views.ShapeSettings.strTransparency": "不透明度", "SSE.Views.ShapeSettings.strType": "类型", "SSE.Views.ShapeSettings.textAdjustShadow": "调整阴影", "SSE.Views.ShapeSettings.textAdvanced": "显示高级设置", "SSE.Views.ShapeSettings.textAngle": "角度", "SSE.Views.ShapeSettings.textBorderSizeErr": "输入的值不正确。<br>请输入介于0 pt和1584 pt之间的值。", "SSE.Views.ShapeSettings.textColor": "颜色填充", "SSE.Views.ShapeSettings.textDirection": "方向", "SSE.Views.ShapeSettings.textEditPoints": "编辑点", "SSE.Views.ShapeSettings.textEditShape": "编辑形状", "SSE.Views.ShapeSettings.textEmptyPattern": "无图案", "SSE.Views.ShapeSettings.textEyedropper": "拾色器", "SSE.Views.ShapeSettings.textFlip": "翻转", "SSE.Views.ShapeSettings.textFromFile": "从文件", "SSE.Views.ShapeSettings.textFromStorage": "从存储设备", "SSE.Views.ShapeSettings.textFromUrl": "来自URL", "SSE.Views.ShapeSettings.textGradient": "渐变点", "SSE.Views.ShapeSettings.textGradientFill": "渐变填充", "SSE.Views.ShapeSettings.textHint270": "逆时针旋转90°", "SSE.Views.ShapeSettings.textHint90": "顺时针旋转90°", "SSE.Views.ShapeSettings.textHintFlipH": "水平翻转", "SSE.Views.ShapeSettings.textHintFlipV": "垂直翻转", "SSE.Views.ShapeSettings.textImageTexture": "图片或纹理", "SSE.Views.ShapeSettings.textLinear": "线性", "SSE.Views.ShapeSettings.textMoreColors": "更多颜色", "SSE.Views.ShapeSettings.textNoFill": "无填充", "SSE.Views.ShapeSettings.textNoShadow": "无阴影", "SSE.Views.ShapeSettings.textOriginalSize": "原始尺寸", "SSE.Views.ShapeSettings.textPatternFill": "模式", "SSE.Views.ShapeSettings.textPosition": "位置", "SSE.Views.ShapeSettings.textRadial": "放射狀", "SSE.Views.ShapeSettings.textRecentlyUsed": "最近使用的", "SSE.Views.ShapeSettings.textRotate90": "旋转90°", "SSE.Views.ShapeSettings.textRotation": "旋转", "SSE.Views.ShapeSettings.textSelectImage": "选择图片", "SSE.Views.ShapeSettings.textSelectTexture": "请选择", "SSE.Views.ShapeSettings.textShadow": "阴影", "SSE.Views.ShapeSettings.textStretch": "伸展", "SSE.Views.ShapeSettings.textStyle": "样式", "SSE.Views.ShapeSettings.textTexture": "从纹理", "SSE.Views.ShapeSettings.textTile": "瓦", "SSE.Views.ShapeSettings.tipAddGradientPoint": "添加渐变点", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "删除渐变点", "SSE.Views.ShapeSettings.txtBrownPaper": "牛皮纸", "SSE.Views.ShapeSettings.txtCanvas": "画布", "SSE.Views.ShapeSettings.txtCarton": "纸箱", "SSE.Views.ShapeSettings.txtDarkFabric": "深色织物", "SSE.Views.ShapeSettings.txtGrain": "纹理", "SSE.Views.ShapeSettings.txtGranite": "花岗岩", "SSE.Views.ShapeSettings.txtGreyPaper": "灰色纸张", "SSE.Views.ShapeSettings.txtKnit": "针织", "SSE.Views.ShapeSettings.txtLeather": "皮革", "SSE.Views.ShapeSettings.txtNoBorders": "无线条", "SSE.Views.ShapeSettings.txtPapyrus": "纸莎草", "SSE.Views.ShapeSettings.txtWood": "木头", "SSE.Views.ShapeSettingsAdvanced.strColumns": "列", "SSE.Views.ShapeSettingsAdvanced.strMargins": "文字內边距", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "不随单元格移动或调整大小", "SSE.Views.ShapeSettingsAdvanced.textAlt": "替代文本", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "描述", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "视觉对象信息的另一种基于文本的表示方式，将读取给视力或认知障碍的人，以帮助他们更好地理解图像、形状、图表或表格中的信息。", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "标题", "SSE.Views.ShapeSettingsAdvanced.textAngle": "角度", "SSE.Views.ShapeSettingsAdvanced.textArrows": "箭头", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "自动调整", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "初始大小", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "初始样式", "SSE.Views.ShapeSettingsAdvanced.textBevel": "斜角", "SSE.Views.ShapeSettingsAdvanced.textBottom": "底部", "SSE.Views.ShapeSettingsAdvanced.textCapType": "大写字母样式", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "列数", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "末端尺寸", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "结束样式", "SSE.Views.ShapeSettingsAdvanced.textFlat": "平面", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "已翻转的", "SSE.Views.ShapeSettingsAdvanced.textHeight": "高度", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "水平地", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "加入类型", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "恒定比例", "SSE.Views.ShapeSettingsAdvanced.textLeft": "左侧", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "线型", "SSE.Views.ShapeSettingsAdvanced.textMiter": "斜接角", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "移动但不调整单元格大小", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "允许文本溢出形状", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "调整形状大小以适应文本", "SSE.Views.ShapeSettingsAdvanced.textRight": "右", "SSE.Views.ShapeSettingsAdvanced.textRotation": "旋转", "SSE.Views.ShapeSettingsAdvanced.textRound": "圆", "SSE.Views.ShapeSettingsAdvanced.textSize": "大小", "SSE.Views.ShapeSettingsAdvanced.textSnap": "单元捕捉", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "列之间的间距", "SSE.Views.ShapeSettingsAdvanced.textSquare": "正方形", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "文本框", "SSE.Views.ShapeSettingsAdvanced.textTitle": "形状 - 高级设置", "SSE.Views.ShapeSettingsAdvanced.textTop": "顶部", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "移动并调整单元格大小", "SSE.Views.ShapeSettingsAdvanced.textVertically": "垂直", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "重量和箭头", "SSE.Views.ShapeSettingsAdvanced.textWidth": "宽度", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "警告", "SSE.Views.SignatureSettings.strDelete": "删除签名", "SSE.Views.SignatureSettings.strDetails": "签名详细信息", "SSE.Views.SignatureSettings.strInvalid": "无效签名", "SSE.Views.SignatureSettings.strRequested": "请求的签名", "SSE.Views.SignatureSettings.strSetup": "签名设置", "SSE.Views.SignatureSettings.strSign": "签署", "SSE.Views.SignatureSettings.strSignature": "签名", "SSE.Views.SignatureSettings.strSigner": "签名人", "SSE.Views.SignatureSettings.strValid": "有效签名", "SSE.Views.SignatureSettings.txtContinueEditing": "仍要编辑", "SSE.Views.SignatureSettings.txtEditWarning": "编辑将删除电子表格中的签名<br>是否继续？", "SSE.Views.SignatureSettings.txtRemoveWarning": "您想要移除此签名吗？<br>此操作无法撤销。", "SSE.Views.SignatureSettings.txtRequestedSignatures": "此电子表格需要签名。", "SSE.Views.SignatureSettings.txtSigned": "有效签名已添加到电子表格中。电子表格受到保护，不可编辑。", "SSE.Views.SignatureSettings.txtSignedInvalid": "电子表格中的某些数字签名无效或无法验证。电子表格受到保护，不可编辑。", "SSE.Views.SlicerAddDialog.textColumns": "列", "SSE.Views.SlicerAddDialog.txtTitle": "插入切片器", "SSE.Views.SlicerSettings.strHideNoData": "隐藏没有数据的项目", "SSE.Views.SlicerSettings.strIndNoData": "直观地指示没有数据的项目", "SSE.Views.SlicerSettings.strShowDel": "显示从数据源中删除的项目", "SSE.Views.SlicerSettings.strShowNoData": "最后显示没有数据的项目", "SSE.Views.SlicerSettings.strSorting": "排序与筛选", "SSE.Views.SlicerSettings.textAdvanced": "显示高级设置", "SSE.Views.SlicerSettings.textAsc": "上升的", "SSE.Views.SlicerSettings.textAZ": "A 到 Z", "SSE.Views.SlicerSettings.textButtons": "按钮", "SSE.Views.SlicerSettings.textColumns": "列", "SSE.Views.SlicerSettings.textDesc": "降序", "SSE.Views.SlicerSettings.textHeight": "高度", "SSE.Views.SlicerSettings.textHor": "水平的", "SSE.Views.SlicerSettings.textKeepRatio": "恒定比例", "SSE.Views.SlicerSettings.textLargeSmall": "最大到最小", "SSE.Views.SlicerSettings.textLock": "禁用调整大小或移动", "SSE.Views.SlicerSettings.textNewOld": "从最新到最旧", "SSE.Views.SlicerSettings.textOldNew": "从最旧到最新", "SSE.Views.SlicerSettings.textPosition": "位置", "SSE.Views.SlicerSettings.textSize": "大小", "SSE.Views.SlicerSettings.textSmallLarge": "由小到大排序", "SSE.Views.SlicerSettings.textStyle": "样式", "SSE.Views.SlicerSettings.textVert": "垂直", "SSE.Views.SlicerSettings.textWidth": "宽度", "SSE.Views.SlicerSettings.textZA": "从 Z 到 A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "按钮", "SSE.Views.SlicerSettingsAdvanced.strColumns": "列", "SSE.Views.SlicerSettingsAdvanced.strHeight": "高度", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "隐藏没有数据的项目", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "直观地指示没有数据的项目", "SSE.Views.SlicerSettingsAdvanced.strReferences": "引用", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "显示从数据源中删除的项目", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "显示标题", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "显示最后没有数据的项目", "SSE.Views.SlicerSettingsAdvanced.strSize": "大小", "SSE.Views.SlicerSettingsAdvanced.strSorting": "排序与筛选", "SSE.Views.SlicerSettingsAdvanced.strStyle": "样式", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "样式和大小", "SSE.Views.SlicerSettingsAdvanced.strWidth": "宽度", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "不随单元格移动或调整大小", "SSE.Views.SlicerSettingsAdvanced.textAlt": "替代文本", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "描述", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "视觉对象信息的替代的基于文本的表示，将被视为具有视觉或认知障碍的人阅读，以帮助他们更好地了解图像，自动图像，图表或表中的哪些信息。", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "标题", "SSE.Views.SlicerSettingsAdvanced.textAsc": "上升的", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A 到 Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "降序", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "要在公式中使用的名称", "SSE.Views.SlicerSettingsAdvanced.textHeader": "页眉", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "恒定比例", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "最大到最小", "SSE.Views.SlicerSettingsAdvanced.textName": "名称", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "从最新到最旧", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "从最旧到最新", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "移动但不调整单元格大小", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "由小到大排序", "SSE.Views.SlicerSettingsAdvanced.textSnap": "单元捕捉", "SSE.Views.SlicerSettingsAdvanced.textSort": "分类", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "来源名称", "SSE.Views.SlicerSettingsAdvanced.textTitle": "切片器-高级设置", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "移动并调整单元格大小", "SSE.Views.SlicerSettingsAdvanced.textZA": "从 Z 到 A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "这是必填栏", "SSE.Views.SortDialog.errorEmpty": "所有排序条件都必须指定一列或一行。", "SSE.Views.SortDialog.errorMoreOneCol": "选择了多个列。", "SSE.Views.SortDialog.errorMoreOneRow": "选择了多行。", "SSE.Views.SortDialog.errorNotOriginalCol": "所选列不在原始选定范围内。", "SSE.Views.SortDialog.errorNotOriginalRow": "您选择的行不在原始选定范围内。", "SSE.Views.SortDialog.errorSameColumnColor": "%1被同一颜色多次排序<br>删除重复的排序条件，然后重试。", "SSE.Views.SortDialog.errorSameColumnValue": "%1按值排序不止一次<br>删除重复的排序条件，然后重试。", "SSE.Views.SortDialog.textAsc": "上升的", "SSE.Views.SortDialog.textAuto": "自动", "SSE.Views.SortDialog.textAZ": "A 到 Z", "SSE.Views.SortDialog.textBelow": "下方", "SSE.Views.SortDialog.textBtnCopy": "复制", "SSE.Views.SortDialog.textBtnDelete": "刪除", "SSE.Views.SortDialog.textBtnNew": "新建", "SSE.Views.SortDialog.textCellColor": "单元格颜色", "SSE.Views.SortDialog.textColumn": "列", "SSE.Views.SortDialog.textDesc": "降序", "SSE.Views.SortDialog.textDown": "下移级别", "SSE.Views.SortDialog.textFontColor": "字体颜色", "SSE.Views.SortDialog.textLeft": "左侧", "SSE.Views.SortDialog.textLevels": "层级", "SSE.Views.SortDialog.textMoreCols": "（更多列...）", "SSE.Views.SortDialog.textMoreRows": "（更多行...）", "SSE.Views.SortDialog.textNone": "无", "SSE.Views.SortDialog.textOptions": "选项", "SSE.Views.SortDialog.textOrder": "排序", "SSE.Views.SortDialog.textRight": "右", "SSE.Views.SortDialog.textRow": "行", "SSE.Views.SortDialog.textSort": "排序依据", "SSE.Views.SortDialog.textSortBy": "排序方式", "SSE.Views.SortDialog.textThenBy": "然后", "SSE.Views.SortDialog.textTop": "顶部", "SSE.Views.SortDialog.textUp": "上移级别", "SSE.Views.SortDialog.textValues": "值", "SSE.Views.SortDialog.textZA": "从 Z 到 A", "SSE.Views.SortDialog.txtInvalidRange": "无效的单元格范围。", "SSE.Views.SortDialog.txtTitle": "分类", "SSE.Views.SortFilterDialog.textAsc": "由（A到Z）升序", "SSE.Views.SortFilterDialog.textDesc": "降序（Z到A）", "SSE.Views.SortFilterDialog.textNoSort": "无排序", "SSE.Views.SortFilterDialog.txtTitle": "分类", "SSE.Views.SortFilterDialog.txtTitleValue": "按值排序", "SSE.Views.SortOptionsDialog.textCase": "区分大小写", "SSE.Views.SortOptionsDialog.textHeaders": "我的数据有标题", "SSE.Views.SortOptionsDialog.textLeftRight": "由左到右排序", "SSE.Views.SortOptionsDialog.textOrientation": "方向", "SSE.Views.SortOptionsDialog.textTitle": "排序选项", "SSE.Views.SortOptionsDialog.textTopBottom": "由上到下排序", "SSE.Views.SpecialPasteDialog.textAdd": "添加", "SSE.Views.SpecialPasteDialog.textAll": "全部", "SSE.Views.SpecialPasteDialog.textBlanks": "忽略空白", "SSE.Views.SpecialPasteDialog.textColWidth": "列宽", "SSE.Views.SpecialPasteDialog.textComments": "批注", "SSE.Views.SpecialPasteDialog.textDiv": "除以", "SSE.Views.SpecialPasteDialog.textFFormat": "公式和格式", "SSE.Views.SpecialPasteDialog.textFNFormat": "公式和数字格式", "SSE.Views.SpecialPasteDialog.textFormats": "格式", "SSE.Views.SpecialPasteDialog.textFormulas": "公式", "SSE.Views.SpecialPasteDialog.textFWidth": "公式和列宽", "SSE.Views.SpecialPasteDialog.textMult": "乘号", "SSE.Views.SpecialPasteDialog.textNone": "无", "SSE.Views.SpecialPasteDialog.textOperation": "操作", "SSE.Views.SpecialPasteDialog.textPaste": "粘贴", "SSE.Views.SpecialPasteDialog.textSub": "减", "SSE.Views.SpecialPasteDialog.textTitle": "选择性粘贴", "SSE.Views.SpecialPasteDialog.textTranspose": "颠倒", "SSE.Views.SpecialPasteDialog.textValues": "值", "SSE.Views.SpecialPasteDialog.textVFormat": "值和格式", "SSE.Views.SpecialPasteDialog.textVNFormat": "值和数字格式", "SSE.Views.SpecialPasteDialog.textWBorders": "所有，除边框外", "SSE.Views.Spellcheck.noSuggestions": "没有拼写建议", "SSE.Views.Spellcheck.textChange": "修改", "SSE.Views.Spellcheck.textChangeAll": "全部更改", "SSE.Views.Spellcheck.textIgnore": "忽略", "SSE.Views.Spellcheck.textIgnoreAll": "全部忽略", "SSE.Views.Spellcheck.txtAddToDictionary": "添加到字典", "SSE.Views.Spellcheck.txtClosePanel": "关闭拼写检查", "SSE.Views.Spellcheck.txtComplete": "拼写检查已完成", "SSE.Views.Spellcheck.txtDictionaryLanguage": "字典语言", "SSE.Views.Spellcheck.txtNextTip": "转到下一个单词", "SSE.Views.Spellcheck.txtSpelling": "拼写", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "（移至結尾）", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "创建副本", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "（创建新电子表格）", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "在工作表之前移动", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "电子表格", "SSE.Views.Statusbar.filteredRecordsText": "{0}个记录的{1}已过滤", "SSE.Views.Statusbar.filteredText": "筛选模式", "SSE.Views.Statusbar.itemAverage": "平均值", "SSE.Views.Statusbar.itemCount": "计数", "SSE.Views.Statusbar.itemDelete": "刪除", "SSE.Views.Statusbar.itemHidden": "隐藏", "SSE.Views.Statusbar.itemHide": "隐藏", "SSE.Views.Statusbar.itemInsert": "插入", "SSE.Views.Statusbar.itemMaximum": "最大值", "SSE.Views.Statusbar.itemMinimum": "最小值", "SSE.Views.Statusbar.itemMoveOrCopy": "移动或复制", "SSE.Views.Statusbar.itemProtect": "保护", "SSE.Views.Statusbar.itemRename": "重新命名", "SSE.Views.Statusbar.itemStatus": "保存状态", "SSE.Views.Statusbar.itemSum": "求和", "SSE.Views.Statusbar.itemTabColor": "标签颜色", "SSE.Views.Statusbar.itemUnProtect": "解除保护", "SSE.Views.Statusbar.RenameDialog.errNameExists": "具有这样一个名称的工作表已经存在。", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "工作表名称不能包含以下字符：\\/*？[]：，且字符'不能作为第一个或最后一个字符", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "工作表名称", "SSE.Views.Statusbar.selectAllSheets": "选定全部工作表", "SSE.Views.Statusbar.sheetIndexText": "工作表{0}共{1}", "SSE.Views.Statusbar.textAverage": "平均值", "SSE.Views.Statusbar.textCount": "计数", "SSE.Views.Statusbar.textMax": "最大值", "SSE.Views.Statusbar.textMin": "最小", "SSE.Views.Statusbar.textNewColor": "更多颜色", "SSE.Views.Statusbar.textNoColor": "没有颜色", "SSE.Views.Statusbar.textSum": "求和", "SSE.Views.Statusbar.tipAddTab": "添加工作表", "SSE.Views.Statusbar.tipFirst": "滚动到第一张", "SSE.Views.Statusbar.tipLast": "滚动到最后一张", "SSE.Views.Statusbar.tipListOfSheets": "工作表列表", "SSE.Views.Statusbar.tipNext": "向右滚动工作表列表", "SSE.Views.Statusbar.tipPrev": "向左滚动表单", "SSE.Views.Statusbar.tipZoomFactor": "缩放", "SSE.Views.Statusbar.tipZoomIn": "放大", "SSE.Views.Statusbar.tipZoomOut": "缩小", "SSE.Views.Statusbar.ungroupSheets": "取消工作表分组", "SSE.Views.Statusbar.zoomText": "缩放％{0}", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "所选单元格区域无法进行操作。<br>选择与现有单元格不同的统一数据范围，然后重试。", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "无法完成所选单元格范围的操作<br>选择一个范围，使第一个表行位于同一行<br>，并且生成的表与当前表重叠。", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "所选单元格范围无法完成操作。<br>选择不包括其他表格的范围。", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "表格中不允许使用多单元格数组公式。", "SSE.Views.TableOptionsDialog.txtEmpty": "这是必填栏", "SSE.Views.TableOptionsDialog.txtFormat": "建立表格", "SSE.Views.TableOptionsDialog.txtInvalidRange": "错误！无效的单元格范围", "SSE.Views.TableOptionsDialog.txtNote": "标题必须保留在同一行中，并且生成的表范围必须与原始表范围重叠。", "SSE.Views.TableOptionsDialog.txtTitle": "标题", "SSE.Views.TableSettings.deleteColumnText": "删除列", "SSE.Views.TableSettings.deleteRowText": "删除行", "SSE.Views.TableSettings.deleteTableText": "刪除表格", "SSE.Views.TableSettings.insertColumnLeftText": "在左侧插入列", "SSE.Views.TableSettings.insertColumnRightText": "向右侧插入列", "SSE.Views.TableSettings.insertRowAboveText": "在上方插入行", "SSE.Views.TableSettings.insertRowBelowText": "在下方插入行", "SSE.Views.TableSettings.notcriticalErrorTitle": "警告", "SSE.Views.TableSettings.selectColumnText": "选择整个列", "SSE.Views.TableSettings.selectDataText": "选择列数据", "SSE.Views.TableSettings.selectRowText": "选择行", "SSE.Views.TableSettings.selectTableText": "选择表格", "SSE.Views.TableSettings.textActions": "表格操作", "SSE.Views.TableSettings.textAdvanced": "显示高级设置", "SSE.Views.TableSettings.textBanded": "镶边", "SSE.Views.TableSettings.textColumns": "列", "SSE.Views.TableSettings.textConvertRange": "转换为范围", "SSE.Views.TableSettings.textEdit": "行和列", "SSE.Views.TableSettings.textEmptyTemplate": "没有模板", "SSE.Views.TableSettings.textExistName": "错误！具有这种名称的范围已经存在", "SSE.Views.TableSettings.textFilter": "筛选按钮", "SSE.Views.TableSettings.textFirst": "第一", "SSE.Views.TableSettings.textHeader": "页眉", "SSE.Views.TableSettings.textInvalidName": "错误！表名无效", "SSE.Views.TableSettings.textIsLocked": "此元素正在被其他用户编辑。", "SSE.Views.TableSettings.textLast": "最后", "SSE.Views.TableSettings.textLongOperation": "长操作", "SSE.Views.TableSettings.textPivot": "插入数据透视表", "SSE.Views.TableSettings.textRemDuplicates": "删除重复项", "SSE.Views.TableSettings.textReservedName": "您尝试使用的名称已在单元格公式中引用。请使用其他名称。", "SSE.Views.TableSettings.textResize": "调整表大小", "SSE.Views.TableSettings.textRows": "行", "SSE.Views.TableSettings.textSelectData": "选择数据", "SSE.Views.TableSettings.textSlicer": "插入切片器", "SSE.Views.TableSettings.textTableName": "表格名称", "SSE.Views.TableSettings.textTemplate": "从模板中选择", "SSE.Views.TableSettings.textTotal": "总计", "SSE.Views.TableSettings.txtGroupTable_Custom": "自定义", "SSE.Views.TableSettings.txtGroupTable_Dark": "深色", "SSE.Views.TableSettings.txtGroupTable_Light": "浅色", "SSE.Views.TableSettings.txtGroupTable_Medium": "中等", "SSE.Views.TableSettings.txtTable_TableStyleDark": "深色表格样式", "SSE.Views.TableSettings.txtTable_TableStyleLight": "浅色表格样式", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "中等深浅表格样式", "SSE.Views.TableSettings.warnLongOperation": "您即将执行的操作可能需要相当长的时间才能完成。<br>您确定要继续吗？", "SSE.Views.TableSettingsAdvanced.textAlt": "替代文本", "SSE.Views.TableSettingsAdvanced.textAltDescription": "描述", "SSE.Views.TableSettingsAdvanced.textAltTip": "视觉对象信息的替代的基于文本的表示，将被视为具有视觉或认知障碍的人阅读，以帮助他们更好地了解图像，自动图像，图表或表中的哪些信息。", "SSE.Views.TableSettingsAdvanced.textAltTitle": "标题", "SSE.Views.TableSettingsAdvanced.textTitle": "表格-高级设置", "SSE.Views.TextArtSettings.strBackground": "背景颜色", "SSE.Views.TextArtSettings.strColor": "颜色", "SSE.Views.TextArtSettings.strFill": "填充", "SSE.Views.TextArtSettings.strForeground": "前景色", "SSE.Views.TextArtSettings.strPattern": "模式", "SSE.Views.TextArtSettings.strSize": "粗细", "SSE.Views.TextArtSettings.strStroke": "折线图", "SSE.Views.TextArtSettings.strTransparency": "不透明度", "SSE.Views.TextArtSettings.strType": "类型", "SSE.Views.TextArtSettings.textAngle": "角度", "SSE.Views.TextArtSettings.textBorderSizeErr": "输入的值不正确。<br>请输入介于0 pt和1584 pt之间的值。", "SSE.Views.TextArtSettings.textColor": "颜色填充", "SSE.Views.TextArtSettings.textDirection": "方向", "SSE.Views.TextArtSettings.textEmptyPattern": "无图案", "SSE.Views.TextArtSettings.textFromFile": "从文件", "SSE.Views.TextArtSettings.textFromUrl": "来自URL", "SSE.Views.TextArtSettings.textGradient": "渐变点", "SSE.Views.TextArtSettings.textGradientFill": "渐变填充", "SSE.Views.TextArtSettings.textImageTexture": "图片或纹理", "SSE.Views.TextArtSettings.textLinear": "线性", "SSE.Views.TextArtSettings.textNoFill": "无填充", "SSE.Views.TextArtSettings.textPatternFill": "模式", "SSE.Views.TextArtSettings.textPosition": "位置", "SSE.Views.TextArtSettings.textRadial": "放射狀", "SSE.Views.TextArtSettings.textSelectTexture": "请选择", "SSE.Views.TextArtSettings.textStretch": "伸展", "SSE.Views.TextArtSettings.textStyle": "样式", "SSE.Views.TextArtSettings.textTemplate": "模板", "SSE.Views.TextArtSettings.textTexture": "从纹理", "SSE.Views.TextArtSettings.textTile": "瓦", "SSE.Views.TextArtSettings.textTransform": "使转换", "SSE.Views.TextArtSettings.tipAddGradientPoint": "添加渐变点", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "删除渐变点", "SSE.Views.TextArtSettings.txtBrownPaper": "牛皮纸", "SSE.Views.TextArtSettings.txtCanvas": "画布", "SSE.Views.TextArtSettings.txtCarton": "纸箱", "SSE.Views.TextArtSettings.txtDarkFabric": "深色织物", "SSE.Views.TextArtSettings.txtGrain": "纹理", "SSE.Views.TextArtSettings.txtGranite": "花岗岩", "SSE.Views.TextArtSettings.txtGreyPaper": "灰色纸张", "SSE.Views.TextArtSettings.txtKnit": "针织", "SSE.Views.TextArtSettings.txtLeather": "皮革", "SSE.Views.TextArtSettings.txtNoBorders": "无线条", "SSE.Views.TextArtSettings.txtPapyrus": "纸莎草", "SSE.Views.TextArtSettings.txtWood": "木头", "SSE.Views.Toolbar.capBtnAddComment": "添加批注", "SSE.Views.Toolbar.capBtnColorSchemas": "颜色", "SSE.Views.Toolbar.capBtnComment": "批注", "SSE.Views.Toolbar.capBtnInsHeader": "页眉和页脚", "SSE.Views.Toolbar.capBtnInsSlicer": "切片器", "SSE.Views.Toolbar.capBtnInsSmartArt": "智能图形", "SSE.Views.Toolbar.capBtnInsSymbol": "符号", "SSE.Views.Toolbar.capBtnMargins": "边距", "SSE.Views.Toolbar.capBtnPageBreak": "换行符", "SSE.Views.Toolbar.capBtnPageOrient": "方向", "SSE.Views.Toolbar.capBtnPageSize": "大小", "SSE.Views.Toolbar.capBtnPrintArea": "打印区域", "SSE.Views.Toolbar.capBtnPrintTitles": "打印标题", "SSE.Views.Toolbar.capBtnScale": "缩放以适合", "SSE.Views.Toolbar.capImgAlign": "对齐", "SSE.Views.Toolbar.capImgBackward": "下移一层", "SSE.Views.Toolbar.capImgForward": "向前移动", "SSE.Views.Toolbar.capImgGroup": "组", "SSE.Views.Toolbar.capInsertChart": "图表", "SSE.Views.Toolbar.capInsertChartRecommend": "推荐的图表", "SSE.Views.Toolbar.capInsertEquation": "公式", "SSE.Views.Toolbar.capInsertHyperlink": "超链接", "SSE.Views.Toolbar.capInsertImage": "图片", "SSE.Views.Toolbar.capInsertShape": "形状", "SSE.Views.Toolbar.capInsertSpark": "迷你图", "SSE.Views.Toolbar.capInsertTable": "表格", "SSE.Views.Toolbar.capInsertText": "文本框", "SSE.Views.Toolbar.capInsertTextart": "艺术字", "SSE.Views.Toolbar.capShapesMerge": "合并形状", "SSE.Views.Toolbar.mniCapitalizeWords": "每个单词首字母大写", "SSE.Views.Toolbar.mniImageFromFile": "来自文件的图片", "SSE.Views.Toolbar.mniImageFromStorage": "存储设备中的图片", "SSE.Views.Toolbar.mniImageFromUrl": "来自URL地址的图片", "SSE.Views.Toolbar.mniLowerCase": "小写", "SSE.Views.Toolbar.mniSentenceCase": "句首字母大写", "SSE.Views.Toolbar.mniToggleCase": "大小写转换", "SSE.Views.Toolbar.mniUpperCase": "大写", "SSE.Views.Toolbar.textAddPrintArea": "添加到打印区域", "SSE.Views.Toolbar.textAlignBottom": "底部对齐", "SSE.Views.Toolbar.textAlignCenter": "居中对齐", "SSE.Views.Toolbar.textAlignJust": "两端对齐", "SSE.Views.Toolbar.textAlignLeft": "左对齐", "SSE.Views.Toolbar.textAlignMiddle": "对齐中间", "SSE.Views.Toolbar.textAlignRight": "右对齐", "SSE.Views.Toolbar.textAlignTop": "顶端对齐", "SSE.Views.Toolbar.textAllBorders": "所有边框", "SSE.Views.Toolbar.textAlpha": "希腊文小字母阿尔法", "SSE.Views.Toolbar.textAuto": "自动", "SSE.Views.Toolbar.textAutoColor": "自动", "SSE.Views.Toolbar.textBetta": "希腊文小字母贝塔", "SSE.Views.Toolbar.textBlackHeart": "黑心", "SSE.Views.Toolbar.textBold": "粗体", "SSE.Views.Toolbar.textBordersColor": "边框颜色", "SSE.Views.Toolbar.textBordersStyle": "边框样式", "SSE.Views.Toolbar.textBottom": "底部：", "SSE.Views.Toolbar.textBottomBorders": "底部边框", "SSE.Views.Toolbar.textBullet": "项目符号", "SSE.Views.Toolbar.textCellAlign": "设置单元格对齐方式", "SSE.Views.Toolbar.textCenterBorders": "内部垂直边框", "SSE.Views.Toolbar.textClearPrintArea": "取消打印区域", "SSE.Views.Toolbar.textClearRule": "清除规则", "SSE.Views.Toolbar.textClockwise": "顺时针角度", "SSE.Views.Toolbar.textColorScales": "色阶", "SSE.Views.Toolbar.textCopyright": "版权符号", "SSE.Views.Toolbar.textCounterCw": "逆时针角度", "SSE.Views.Toolbar.textCustom": "自定义", "SSE.Views.Toolbar.textDataBars": "数据栏", "SSE.Views.Toolbar.textDegree": "度数符号", "SSE.Views.Toolbar.textDelLeft": "向左移动单元格", "SSE.Views.Toolbar.textDelPageBreak": "删除分页符", "SSE.Views.Toolbar.textDelta": "希腊文小字母得尔塔", "SSE.Views.Toolbar.textDelUp": "向上移动单元格", "SSE.Views.Toolbar.textDiagDownBorder": "对角线下边框", "SSE.Views.Toolbar.textDiagUpBorder": "对角线上边框", "SSE.Views.Toolbar.textDivision": "除号", "SSE.Views.Toolbar.textDollar": "美元符号", "SSE.Views.Toolbar.textDone": "完成", "SSE.Views.Toolbar.textDown": "下", "SSE.Views.Toolbar.textEditVA": "编辑可见区域", "SSE.Views.Toolbar.textEntireCol": "整列", "SSE.Views.Toolbar.textEntireRow": "整行", "SSE.Views.Toolbar.textEuro": "欧元符号", "SSE.Views.Toolbar.textFewPages": "页数", "SSE.Views.Toolbar.textFillLeft": "左侧", "SSE.Views.Toolbar.textFillRight": "右", "SSE.Views.Toolbar.textFormatCellFill": "设置单元格填充", "SSE.Views.Toolbar.textGreaterEqual": "大于等于", "SSE.Views.Toolbar.textHeight": "高度", "SSE.Views.Toolbar.textHideVA": "隐藏可见区域", "SSE.Views.Toolbar.textHorizontal": "横向文本", "SSE.Views.Toolbar.textInfinity": "无限", "SSE.Views.Toolbar.textInsDown": "向下移动单元格", "SSE.Views.Toolbar.textInsideBorders": "内部边框", "SSE.Views.Toolbar.textInsPageBreak": "插入分页符", "SSE.Views.Toolbar.textInsRight": "向右移动单元格", "SSE.Views.Toolbar.textItalic": "斜体", "SSE.Views.Toolbar.textItems": "项目", "SSE.Views.Toolbar.textLandscape": "横向", "SSE.Views.Toolbar.textLeft": "左：", "SSE.Views.Toolbar.textLeftBorders": "左边框", "SSE.Views.Toolbar.textLessEqual": "小于或等于", "SSE.Views.Toolbar.textLetterPi": "希腊文小字母 Pi", "SSE.Views.Toolbar.textManageRule": "管理规则", "SSE.Views.Toolbar.textManyPages": "页数", "SSE.Views.Toolbar.textMarginsLast": "上次自定义", "SSE.Views.Toolbar.textMarginsNarrow": "狭窄", "SSE.Views.Toolbar.textMarginsNormal": "正常", "SSE.Views.Toolbar.textMarginsWide": "宽", "SSE.Views.Toolbar.textMiddleBorders": "内水平边框", "SSE.Views.Toolbar.textMoreBorders": "其他边框", "SSE.Views.Toolbar.textMoreFormats": "更多格式", "SSE.Views.Toolbar.textMorePages": "更多页面", "SSE.Views.Toolbar.textMoreSymbols": "更多符号", "SSE.Views.Toolbar.textNewColor": "更多颜色", "SSE.Views.Toolbar.textNewRule": "新规则", "SSE.Views.Toolbar.textNoBorders": "无边框", "SSE.Views.Toolbar.textNotEqualTo": "不等于", "SSE.Views.Toolbar.textOneHalf": "普通分数一半", "SSE.Views.Toolbar.textOnePage": "页面", "SSE.Views.Toolbar.textOneQuarter": "普通分数四分之一", "SSE.Views.Toolbar.textOutBorders": "外部边框", "SSE.Views.Toolbar.textPageMarginsCustom": "自定义边距", "SSE.Views.Toolbar.textPlusMinus": "正负号", "SSE.Views.Toolbar.textPortrait": "直向方向", "SSE.Views.Toolbar.textPrint": "打印", "SSE.Views.Toolbar.textPrintGridlines": "打印网格线", "SSE.Views.Toolbar.textPrintHeadings": "打印标题", "SSE.Views.Toolbar.textPrintOptions": "打印设置", "SSE.Views.Toolbar.textRegistered": "注册标志", "SSE.Views.Toolbar.textResetPageBreak": "重置所有分页符", "SSE.Views.Toolbar.textRight": "右: ", "SSE.Views.Toolbar.textRightBorders": "右边界", "SSE.Views.Toolbar.textRotateDown": "向下旋转文字", "SSE.Views.Toolbar.textRotateUp": "向上旋转文字", "SSE.Views.Toolbar.textRtlSheet": "工作表从右到左", "SSE.Views.Toolbar.textScale": "尺寸", "SSE.Views.Toolbar.textScaleCustom": "自定义", "SSE.Views.Toolbar.textSection": "章节标志", "SSE.Views.Toolbar.textSelection": "从当前选择", "SSE.Views.Toolbar.textSeries": "序列", "SSE.Views.Toolbar.textSetPrintArea": "设置打印区域", "SSE.Views.Toolbar.textShapesCombine": "组合", "SSE.Views.Toolbar.textShapesFragment": "拆分", "SSE.Views.Toolbar.textShapesIntersect": "相交", "SSE.Views.Toolbar.textShapesSubstract": "剪除", "SSE.Views.Toolbar.textShapesUnion": "结合", "SSE.Views.Toolbar.textShowVA": "显示可见区域", "SSE.Views.Toolbar.textSmile": "白色笑脸", "SSE.Views.Toolbar.textSquareRoot": "平方根", "SSE.Views.Toolbar.textStrikeout": "删除线", "SSE.Views.Toolbar.textSubscript": "下标", "SSE.Views.Toolbar.textSubSuperscript": "下标/上标", "SSE.Views.Toolbar.textSuperscript": "上标", "SSE.Views.Toolbar.textTabCollaboration": "协作", "SSE.Views.Toolbar.textTabData": "数据", "SSE.Views.Toolbar.textTabDraw": "绘图", "SSE.Views.Toolbar.textTabFile": "文件", "SSE.Views.Toolbar.textTabFormula": "公式", "SSE.Views.Toolbar.textTabHome": "开始", "SSE.Views.Toolbar.textTabInsert": "插入", "SSE.Views.Toolbar.textTabLayout": "布局", "SSE.Views.Toolbar.textTabProtect": "保护", "SSE.Views.Toolbar.textTabView": "视图", "SSE.Views.Toolbar.textThisPivot": "由此数据透视表", "SSE.Views.Toolbar.textThisSheet": "由此工作表", "SSE.Views.Toolbar.textThisTable": "由此表", "SSE.Views.Toolbar.textTilde": "波浪号", "SSE.Views.Toolbar.textTop": "顶部: ", "SSE.Views.Toolbar.textTopBorders": "顶部边框", "SSE.Views.Toolbar.textTradeMark": "商标标志", "SSE.Views.Toolbar.textUnderline": "下划线", "SSE.Views.Toolbar.textUp": "上", "SSE.Views.Toolbar.textVertical": "垂直文本", "SSE.Views.Toolbar.textWidth": "宽度", "SSE.Views.Toolbar.textYen": "日元符号", "SSE.Views.Toolbar.textZoom": "缩放", "SSE.Views.Toolbar.tipAlignBottom": "底部对齐", "SSE.Views.Toolbar.tipAlignCenter": "居中对齐", "SSE.Views.Toolbar.tipAlignJust": "两端对齐", "SSE.Views.Toolbar.tipAlignLeft": "左对齐", "SSE.Views.Toolbar.tipAlignMiddle": "居中对齐", "SSE.Views.Toolbar.tipAlignRight": "右对齐", "SSE.Views.Toolbar.tipAlignTop": "顶端对齐", "SSE.Views.Toolbar.tipAutofilter": "排序与筛选", "SSE.Views.Toolbar.tipBack": "返回", "SSE.Views.Toolbar.tipBorders": "边框", "SSE.Views.Toolbar.tipCellStyle": "单元格样式", "SSE.Views.Toolbar.tipChangeCase": "更改大小写", "SSE.Views.Toolbar.tipChangeChart": "更改图表类型", "SSE.Views.Toolbar.tipClearStyle": "清除", "SSE.Views.Toolbar.tipColorSchemas": "更改配色主题", "SSE.Views.Toolbar.tipCondFormat": "条件格式", "SSE.Views.Toolbar.tipCopy": "复制", "SSE.Views.Toolbar.tipCopyStyle": "复制样式", "SSE.Views.Toolbar.tipCut": "剪切", "SSE.Views.Toolbar.tipDecDecimal": "减少小数", "SSE.Views.Toolbar.tipDecFont": "减小字体大小", "SSE.Views.Toolbar.tipDeleteOpt": "删除单元格", "SSE.Views.Toolbar.tipDigStyleAccounting": "会计类型", "SSE.Views.Toolbar.tipDigStyleComma": "逗号样式", "SSE.Views.Toolbar.tipDigStyleCurrency": "货币样式", "SSE.Views.Toolbar.tipDigStylePercent": "百分比样式", "SSE.Views.Toolbar.tipEditChart": "编辑图表", "SSE.Views.Toolbar.tipEditChartData": "选择数据", "SSE.Views.Toolbar.tipEditChartType": "更改图表类型", "SSE.Views.Toolbar.tipEditHeader": "编辑页眉或页脚", "SSE.Views.Toolbar.tipFontColor": "字体颜色", "SSE.Views.Toolbar.tipFontName": "字体 ", "SSE.Views.Toolbar.tipFontSize": "字体大小", "SSE.Views.Toolbar.tipHAlighOle": "水平对齐", "SSE.Views.Toolbar.tipImgAlign": "对齐对象", "SSE.Views.Toolbar.tipImgGroup": "组对象", "SSE.Views.Toolbar.tipIncDecimal": "增加小数", "SSE.Views.Toolbar.tipIncFont": "增加字体大小", "SSE.Views.Toolbar.tipInsertChart": "插入图表", "SSE.Views.Toolbar.tipInsertChartRecommend": "插入推荐图表", "SSE.Views.Toolbar.tipInsertChartSpark": "插入图表", "SSE.Views.Toolbar.tipInsertEquation": "插入方程", "SSE.Views.Toolbar.tipInsertHorizontalText": "插入水平文本框", "SSE.Views.Toolbar.tipInsertHyperlink": "添加超链接", "SSE.Views.Toolbar.tipInsertImage": "插入图片", "SSE.Views.Toolbar.tipInsertOpt": "插入单元格", "SSE.Views.Toolbar.tipInsertShape": "插入形狀", "SSE.Views.Toolbar.tipInsertSlicer": "插入切片器", "SSE.Views.Toolbar.tipInsertSmartArt": "插入智能图形", "SSE.Views.Toolbar.tipInsertSpark": "插入迷你图", "SSE.Views.Toolbar.tipInsertSymbol": "插入符号", "SSE.Views.Toolbar.tipInsertTable": "插入表格", "SSE.Views.Toolbar.tipInsertText": "插入文本框", "SSE.Views.Toolbar.tipInsertTextart": "插入艺术字", "SSE.Views.Toolbar.tipInsertVerticalText": "插入垂直文本框", "SSE.Views.Toolbar.tipMerge": "合并和居中", "SSE.Views.Toolbar.tipNone": "无", "SSE.Views.Toolbar.tipNumFormat": "数字格式", "SSE.Views.Toolbar.tipPageBreak": "在打印副本中你希望下一页开始的位置添加一个分隔符", "SSE.Views.Toolbar.tipPageMargins": "页边距", "SSE.Views.Toolbar.tipPageOrient": "页面方向", "SSE.Views.Toolbar.tipPageSize": "页面大小", "SSE.Views.Toolbar.tipPaste": "粘贴", "SSE.Views.Toolbar.tipPrColor": "填充顏色", "SSE.Views.Toolbar.tipPrint": "打印", "SSE.Views.Toolbar.tipPrintArea": "打印区域", "SSE.Views.Toolbar.tipPrintQuick": "快速打印", "SSE.Views.Toolbar.tipPrintTitles": "打印标题", "SSE.Views.Toolbar.tipRedo": "重做", "SSE.Views.Toolbar.tipReplace": "替换", "SSE.Views.Toolbar.tipRtlSheet": "切换工作表方向，使第一列在右侧", "SSE.Views.Toolbar.tipSave": "保存", "SSE.Views.Toolbar.tipSaveCoauth": "保存您的更改以供其他用户查看", "SSE.Views.Toolbar.tipScale": "缩放以适合", "SSE.Views.Toolbar.tipSelectAll": "全选", "SSE.Views.Toolbar.tipSendBackward": "下移一层", "SSE.Views.Toolbar.tipSendForward": "向前移动", "SSE.Views.Toolbar.tipShapesMerge": "合并形状", "SSE.Views.Toolbar.tipSynchronize": "文档已被其他用户更改。请单击保存更改并重新加载更新。", "SSE.Views.Toolbar.tipTextFormatting": "更多文件格式化工具", "SSE.Views.Toolbar.tipTextOrientation": "方向", "SSE.Views.Toolbar.tipUndo": "撤消", "SSE.Views.Toolbar.tipVAlighOle": "垂直对齐", "SSE.Views.Toolbar.tipVisibleArea": "可视区域", "SSE.Views.Toolbar.tipWrap": "文字换行", "SSE.Views.Toolbar.txtAccounting": "会计", "SSE.Views.Toolbar.txtAdditional": "更多", "SSE.Views.Toolbar.txtAscending": "上升的", "SSE.Views.Toolbar.txtAutosumTip": "求和", "SSE.Views.Toolbar.txtCellStyle": "单元格样式", "SSE.Views.Toolbar.txtClearAll": "全部", "SSE.Views.Toolbar.txtClearComments": "批注", "SSE.Views.Toolbar.txtClearFilter": "清空筛选条件", "SSE.Views.Toolbar.txtClearFormat": "格式", "SSE.Views.Toolbar.txtClearFormula": "函数", "SSE.Views.Toolbar.txtClearHyper": "超链接", "SSE.Views.Toolbar.txtClearText": "文字", "SSE.Views.Toolbar.txtCurrency": "货币", "SSE.Views.Toolbar.txtCustom": "自定义", "SSE.Views.Toolbar.txtDate": "日期", "SSE.Views.Toolbar.txtDateLong": "长日期", "SSE.Views.Toolbar.txtDateShort": "短日期", "SSE.Views.Toolbar.txtDateTime": "日期和時間", "SSE.Views.Toolbar.txtDescending": "降序", "SSE.Views.Toolbar.txtDollar": "$美元", "SSE.Views.Toolbar.txtEuro": "欧元", "SSE.Views.Toolbar.txtExp": "指数", "SSE.Views.Toolbar.txtFillNum": "填充", "SSE.Views.Toolbar.txtFilter": "筛选", "SSE.Views.Toolbar.txtFormula": "插入函数", "SSE.Views.Toolbar.txtFraction": "分数", "SSE.Views.Toolbar.txtFranc": "瑞士法郎", "SSE.Views.Toolbar.txtGeneral": "常规", "SSE.Views.Toolbar.txtInteger": "整数", "SSE.Views.Toolbar.txtManageRange": "名称管理", "SSE.Views.Toolbar.txtMergeAcross": "合并", "SSE.Views.Toolbar.txtMergeCells": "合并单元格", "SSE.Views.Toolbar.txtMergeCenter": "合并与居中", "SSE.Views.Toolbar.txtNamedRange": "命名范围", "SSE.Views.Toolbar.txtNewRange": "定义名称", "SSE.Views.Toolbar.txtNoBorders": "无边框", "SSE.Views.Toolbar.txtNumber": "数值", "SSE.Views.Toolbar.txtPasteRange": "粘贴名称", "SSE.Views.Toolbar.txtPercentage": "百分比", "SSE.Views.Toolbar.txtPound": "£英镑", "SSE.Views.Toolbar.txtRouble": "卢布", "SSE.Views.Toolbar.txtScientific": "科学", "SSE.Views.Toolbar.txtSearch": "搜索", "SSE.Views.Toolbar.txtSort": "分类", "SSE.Views.Toolbar.txtSortAZ": "升序排序", "SSE.Views.Toolbar.txtSortZA": "降序排序", "SSE.Views.Toolbar.txtSpecial": "特别", "SSE.Views.Toolbar.txtTableTemplate": "格式化为表格模板", "SSE.Views.Toolbar.txtText": "文字", "SSE.Views.Toolbar.txtTime": "时间", "SSE.Views.Toolbar.txtUnmerge": "取消合并单元格", "SSE.Views.Toolbar.txtYen": "¥ 日元", "SSE.Views.Top10FilterDialog.textType": "显示", "SSE.Views.Top10FilterDialog.txtBottom": "底部", "SSE.Views.Top10FilterDialog.txtBy": "根据", "SSE.Views.Top10FilterDialog.txtItems": "项目", "SSE.Views.Top10FilterDialog.txtPercent": "百分比", "SSE.Views.Top10FilterDialog.txtSum": "求和", "SSE.Views.Top10FilterDialog.txtTitle": "前十位自动筛选", "SSE.Views.Top10FilterDialog.txtTop": "顶部", "SSE.Views.Top10FilterDialog.txtValueTitle": "前10位筛选", "SSE.Views.ValueFieldSettingsDialog.textNext": "(下一个)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "数字格式", "SSE.Views.ValueFieldSettingsDialog.textPrev": "（上一个）", "SSE.Views.ValueFieldSettingsDialog.textTitle": "值字段设置", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "平均值", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "基础字段", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "基础项目", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%2的%1", "SSE.Views.ValueFieldSettingsDialog.txtCount": "计数", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "计数", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "自定义名称", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "不同于", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "索引", "SSE.Views.ValueFieldSettingsDialog.txtMax": "最大值", "SSE.Views.ValueFieldSettingsDialog.txtMin": "最小值", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "没有计算", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "的百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "差异百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "列百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "列总计的百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "%父项合计的", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "父列总计的百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "父总计的百分比", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "在…累计总和的%", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "行的百分比", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "乘积", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "从小到大排序", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "从大到小排序", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "运行总计", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "将值显示为", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "来源名称：", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "标准差", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "标准差", "SSE.Views.ValueFieldSettingsDialog.txtSum": "求和", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "值字段汇总依据", "SSE.Views.ValueFieldSettingsDialog.txtVar": "变量", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "关闭", "SSE.Views.ViewManagerDlg.guestText": "访客", "SSE.Views.ViewManagerDlg.lockText": "已锁定", "SSE.Views.ViewManagerDlg.textDelete": "刪除", "SSE.Views.ViewManagerDlg.textDuplicate": "复制", "SSE.Views.ViewManagerDlg.textEmpty": "尚未创建任何视图。", "SSE.Views.ViewManagerDlg.textGoTo": "转到视图", "SSE.Views.ViewManagerDlg.textLongName": "输入一个少于128个字符的名称。", "SSE.Views.ViewManagerDlg.textNew": "新建", "SSE.Views.ViewManagerDlg.textRename": "重新命名", "SSE.Views.ViewManagerDlg.textRenameError": "视图名称不能为空。", "SSE.Views.ViewManagerDlg.textRenameLabel": "重命名视图", "SSE.Views.ViewManagerDlg.textViews": "工作表视图", "SSE.Views.ViewManagerDlg.tipIsLocked": "此元素正在被其他用户编辑。", "SSE.Views.ViewManagerDlg.txtTitle": "工作表视图管理器", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "您确定要删除此工作表视图吗？", "SSE.Views.ViewManagerDlg.warnDeleteView": "您正试图删除当前启用的视图“%1”<br>是否关闭此视图并将其删除？", "SSE.Views.ViewTab.capBtnFreeze": "冻结窗格", "SSE.Views.ViewTab.capBtnSheetView": "工作表视图", "SSE.Views.ViewTab.textAlwaysShowToolbar": "始终显示工具栏", "SSE.Views.ViewTab.textClose": "关闭", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "合并工作表和状态栏", "SSE.Views.ViewTab.textCreate": "新建", "SSE.Views.ViewTab.textDefault": "默认", "SSE.Views.ViewTab.textFill": "填写", "SSE.Views.ViewTab.textFormula": "公式栏", "SSE.Views.ViewTab.textFreezeCol": "冻结第一列", "SSE.Views.ViewTab.textFreezeRow": "冻结顶行", "SSE.Views.ViewTab.textGridlines": "网格线", "SSE.Views.ViewTab.textHeadings": "标题", "SSE.Views.ViewTab.textInterfaceTheme": "界面主题", "SSE.Views.ViewTab.textLeftMenu": "左侧面板", "SSE.Views.ViewTab.textLine": "线", "SSE.Views.ViewTab.textMacros": "宏", "SSE.Views.ViewTab.textManager": "视图管理器", "SSE.Views.ViewTab.textRightMenu": "右侧面板", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "显示冻结窗格的阴影", "SSE.Views.ViewTab.textTabStyle": "选项卡样式", "SSE.Views.ViewTab.textUnFreeze": "取消冻结窗格", "SSE.Views.ViewTab.textZeros": "显示零", "SSE.Views.ViewTab.textZoom": "缩放", "SSE.Views.ViewTab.tipClose": "关闭工作表视图", "SSE.Views.ViewTab.tipCreate": "建立工作表视图", "SSE.Views.ViewTab.tipFreeze": "冻结窗格", "SSE.Views.ViewTab.tipInterfaceTheme": "界面主题", "SSE.Views.ViewTab.tipMacros": "宏", "SSE.Views.ViewTab.tipSheetView": "工作表视图", "SSE.Views.ViewTab.tipViewNormal": "在普通视图中查看文档", "SSE.Views.ViewTab.tipViewPageBreak": "查看打印文档时分页符的显示位置", "SSE.Views.ViewTab.txtViewNormal": "正常", "SSE.Views.ViewTab.txtViewPageBreak": "分页预览", "SSE.Views.WatchDialog.closeButtonText": "关闭", "SSE.Views.WatchDialog.textAdd": "新增监视", "SSE.Views.WatchDialog.textBook": "书", "SSE.Views.WatchDialog.textCell": "单元格", "SSE.Views.WatchDialog.textDelete": "删除监视", "SSE.Views.WatchDialog.textDeleteAll": "删除全部", "SSE.Views.WatchDialog.textFormula": "公式", "SSE.Views.WatchDialog.textName": "名称", "SSE.Views.WatchDialog.textSheet": "工作表", "SSE.Views.WatchDialog.textValue": "值", "SSE.Views.WatchDialog.txtTitle": "监视窗口", "SSE.Views.WBProtection.hintAllowRanges": "允许编辑范围", "SSE.Views.WBProtection.hintProtectRange": "保护范围", "SSE.Views.WBProtection.hintProtectSheet": "保护工作表", "SSE.Views.WBProtection.hintProtectWB": "保护工作簿", "SSE.Views.WBProtection.txtAllowRanges": "允许编辑范围", "SSE.Views.WBProtection.txtHiddenFormula": "隐藏的公式", "SSE.Views.WBProtection.txtLockedCell": "锁定单元格", "SSE.Views.WBProtection.txtLockedShape": "形状已锁定", "SSE.Views.WBProtection.txtLockedText": "锁定文本", "SSE.Views.WBProtection.txtProtectRange": "保护范围", "SSE.Views.WBProtection.txtProtectSheet": "保护工作表", "SSE.Views.WBProtection.txtProtectWB": "保护工作簿", "SSE.Views.WBProtection.txtSheetUnlockDescription": "输入密码以取消工作表保护", "SSE.Views.WBProtection.txtSheetUnlockTitle": "撤消工作表保护", "SSE.Views.WBProtection.txtWBUnlockDescription": "输入密码以取消工作簿保护", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}