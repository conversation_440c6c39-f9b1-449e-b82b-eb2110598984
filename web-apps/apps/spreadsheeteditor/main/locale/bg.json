{"cancelButtonText": "Отказ", "Common.Controllers.Chat.notcriticalErrorTitle": "Внимание", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.History.notcriticalErrorTitle": "Warning", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "Площ", "Common.define.chartData.textAreaStacked": "Stacked area", "Common.define.chartData.textAreaStackedPer": "100% Stacked area", "Common.define.chartData.textBar": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textBarNormal": "Clustered column", "Common.define.chartData.textBarNormal3d": "3-D Clustered column", "Common.define.chartData.textBarNormal3dPerspective": "3-D column", "Common.define.chartData.textBarStacked": "Stacked column", "Common.define.chartData.textBarStacked3d": "3-<PERSON> Stacked column", "Common.define.chartData.textBarStackedPer": "100% Stacked column", "Common.define.chartData.textBarStackedPer3d": "3-D 100% Stacked column", "Common.define.chartData.textCharts": "Диаграми", "Common.define.chartData.textColumn": "Колона", "Common.define.chartData.textColumnSpark": "Колона", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Stacked area - clustered column", "Common.define.chartData.textComboBarLine": "Clustered column - line", "Common.define.chartData.textComboBarLineSecondary": "Clustered column - line on secondary axis", "Common.define.chartData.textComboCustom": "Custom combination", "Common.define.chartData.textDoughnut": "Doughnut", "Common.define.chartData.textHBarNormal": "Clustered bar", "Common.define.chartData.textHBarNormal3d": "3-D Clustered bar", "Common.define.chartData.textHBarStacked": "Stacked bar", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> Stacked bar", "Common.define.chartData.textHBarStackedPer": "100% Stacked bar", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% Stacked bar", "Common.define.chartData.textLine": "Линия", "Common.define.chartData.textLine3d": "3-D line", "Common.define.chartData.textLineMarker": "Line with markers", "Common.define.chartData.textLineSpark": "Линия", "Common.define.chartData.textLineStacked": "Stacked line", "Common.define.chartData.textLineStackedMarker": "Stacked line with markers", "Common.define.chartData.textLineStackedPer": "100% Stacked line", "Common.define.chartData.textLineStackedPerMarker": "100% Stacked line with markers", "Common.define.chartData.textPie": "Кръгова", "Common.define.chartData.textPie3d": "3-D pie", "Common.define.chartData.textPoint": "XY (точкова)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Filled radar", "Common.define.chartData.textRadarMarker": "Radar with markers", "Common.define.chartData.textScatter": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "Scatter with straight lines", "Common.define.chartData.textScatterLineMarker": "Scatter with straight lines and markers", "Common.define.chartData.textScatterSmooth": "Scatter with smooth lines", "Common.define.chartData.textScatterSmoothMarker": "Scatter with smooth lines and markers", "Common.define.chartData.textSparks": "Блещукащи", "Common.define.chartData.textStock": "Борсова", "Common.define.chartData.textSurface": "Повърхност", "Common.define.chartData.textWinLossSpark": "Печалба/Загуба", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "No format set", "Common.define.conditionalData.text1Above": "1 std dev above", "Common.define.conditionalData.text1Below": "1 std dev below", "Common.define.conditionalData.text2Above": "2 std dev above", "Common.define.conditionalData.text2Below": "2 std dev below", "Common.define.conditionalData.text3Above": "3 std dev above", "Common.define.conditionalData.text3Below": "3 std dev below", "Common.define.conditionalData.textAbove": "Above", "Common.define.conditionalData.textAverage": "Average", "Common.define.conditionalData.textBegins": "Begins with", "Common.define.conditionalData.textBelow": "Below", "Common.define.conditionalData.textBetween": "Between", "Common.define.conditionalData.textBlank": "Blank", "Common.define.conditionalData.textBlanks": "Contains blanks", "Common.define.conditionalData.textBottom": "Bottom", "Common.define.conditionalData.textContains": "Contains", "Common.define.conditionalData.textDataBar": "Data bar", "Common.define.conditionalData.textDate": "Date", "Common.define.conditionalData.textDuplicate": "Duplicate", "Common.define.conditionalData.textEnds": "Ends with", "Common.define.conditionalData.textEqAbove": "Equal to or above", "Common.define.conditionalData.textEqBelow": "Equal to or below", "Common.define.conditionalData.textEqual": "Equal to", "Common.define.conditionalData.textError": "Грешка", "Common.define.conditionalData.textErrors": "Contains errors", "Common.define.conditionalData.textFormula": "Формула", "Common.define.conditionalData.textGreater": "Greater than", "Common.define.conditionalData.textGreaterEq": "Greater than or equal to", "Common.define.conditionalData.textIconSets": "Icon sets", "Common.define.conditionalData.textLast7days": "In the last 7 days", "Common.define.conditionalData.textLastMonth": "Last month", "Common.define.conditionalData.textLastWeek": "Last week", "Common.define.conditionalData.textLess": "Less than", "Common.define.conditionalData.textLessEq": "Less than or equal to", "Common.define.conditionalData.textNextMonth": "Next month", "Common.define.conditionalData.textNextWeek": "Next week", "Common.define.conditionalData.textNotBetween": "Not between", "Common.define.conditionalData.textNotBlanks": "Does not contain blanks", "Common.define.conditionalData.textNotContains": "Does not contain", "Common.define.conditionalData.textNotEqual": "Not equal to", "Common.define.conditionalData.textNotErrors": "Does not contain errors", "Common.define.conditionalData.textText": "Text", "Common.define.conditionalData.textThisMonth": "This month", "Common.define.conditionalData.textThisWeek": "This week", "Common.define.conditionalData.textToday": "Today", "Common.define.conditionalData.textTomorrow": "Tomorrow", "Common.define.conditionalData.textTop": "Top", "Common.define.conditionalData.textUnique": "Unique", "Common.define.conditionalData.textValue": "Value is", "Common.define.conditionalData.textYesterday": "Yesterday", "Common.define.smartArt.textAccentedPicture": "Accented Picture", "Common.define.smartArt.textAccentProcess": "Accent Process", "Common.define.smartArt.textAlternatingFlow": "Alternating flow", "Common.define.smartArt.textAlternatingHexagons": "Alternating hexagons", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternating picture blocks", "Common.define.smartArt.textAlternatingPictureCircles": "Alternating picture circles", "Common.define.smartArt.textArchitectureLayout": "Architecture layout", "Common.define.smartArt.textArrowRibbon": "Arrow ribbon", "Common.define.smartArt.textAscendingPictureAccentProcess": "Ascending picture accent process", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Basic bending process", "Common.define.smartArt.textBasicBlockList": "Basic block list", "Common.define.smartArt.textBasicChevronProcess": "Basic chevron process", "Common.define.smartArt.textBasicCycle": "Basic cycle", "Common.define.smartArt.textBasicMatrix": "Basic matrix", "Common.define.smartArt.textBasicPie": "Basic Pie", "Common.define.smartArt.textBasicProcess": "Basic process", "Common.define.smartArt.textBasicPyramid": "Basic pyramid", "Common.define.smartArt.textBasicRadial": "Basic radial", "Common.define.smartArt.textBasicTarget": "Basic target", "Common.define.smartArt.textBasicTimeline": "Basic timeline", "Common.define.smartArt.textBasicVenn": "Basic Venn", "Common.define.smartArt.textBendingPictureAccentList": "Bending picture accent list", "Common.define.smartArt.textBendingPictureBlocks": "Bending picture blocks", "Common.define.smartArt.textBendingPictureCaption": "Bending picture caption", "Common.define.smartArt.textBendingPictureCaptionList": "Bending picture caption list", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Bending picture semi-transparent text", "Common.define.smartArt.textBlockCycle": "Block cycle", "Common.define.smartArt.textBubblePictureList": "Bubble picture list", "Common.define.smartArt.textCaptionedPictures": "Captioned pictures", "Common.define.smartArt.textChevronAccentProcess": "Chevron accent process", "Common.define.smartArt.textChevronList": "Chevron list", "Common.define.smartArt.textCircleAccentTimeline": "Circle accent timeline", "Common.define.smartArt.textCircleArrowProcess": "Circle arrow process", "Common.define.smartArt.textCirclePictureHierarchy": "Circle picture hierarchy", "Common.define.smartArt.textCircleProcess": "Circle process", "Common.define.smartArt.textCircleRelationship": "Circle relationship", "Common.define.smartArt.textCircularBendingProcess": "Circular bending process", "Common.define.smartArt.textCircularPictureCallout": "Circular picture callout", "Common.define.smartArt.textClosedChevronProcess": "Closed chevron process", "Common.define.smartArt.textContinuousArrowProcess": "Continuous arrow process", "Common.define.smartArt.textContinuousBlockProcess": "Continuous block process", "Common.define.smartArt.textContinuousCycle": "Continuous cycle", "Common.define.smartArt.textContinuousPictureList": "Continuous picture list", "Common.define.smartArt.textConvergingArrows": "Converging arrows", "Common.define.smartArt.textConvergingRadial": "Converging radial", "Common.define.smartArt.textConvergingText": "Converging text", "Common.define.smartArt.textCounterbalanceArrows": "Counterbalance arrows", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "Cycle matrix", "Common.define.smartArt.textDescendingBlockList": "Descending block list", "Common.define.smartArt.textDescendingProcess": "Descending process", "Common.define.smartArt.textDetailedProcess": "Detailed process", "Common.define.smartArt.textDivergingArrows": "Diverging arrows", "Common.define.smartArt.textDivergingRadial": "Diverging radial", "Common.define.smartArt.textEquation": "Equation", "Common.define.smartArt.textFramedTextPicture": "Framed text picture", "Common.define.smartArt.textFunnel": "Funnel", "Common.define.smartArt.textGear": "Gear", "Common.define.smartArt.textGridMatrix": "Grid matrix", "Common.define.smartArt.textGroupedList": "Grouped list", "Common.define.smartArt.textHalfCircleOrganizationChart": "Half circle organization chart", "Common.define.smartArt.textHexagonCluster": "Hexagon cluster", "Common.define.smartArt.textHexagonRadial": "Hexagon radial", "Common.define.smartArt.textHierarchy": "Hierarchy", "Common.define.smartArt.textHierarchyList": "Hierarchy list", "Common.define.smartArt.textHorizontalBulletList": "Horizontal bullet list", "Common.define.smartArt.textHorizontalHierarchy": "Horizontal hierarchy", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal labeled hierarchy", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontal multi-level hierarchy", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontal organization chart", "Common.define.smartArt.textHorizontalPictureList": "Horizontal picture list", "Common.define.smartArt.textIncreasingArrowProcess": "Increasing arrow process", "Common.define.smartArt.textIncreasingCircleProcess": "Increasing circle process", "Common.define.smartArt.textInterconnectedBlockProcess": "Interconnected block process", "Common.define.smartArt.textInterconnectedRings": "Interconnected rings", "Common.define.smartArt.textInvertedPyramid": "Inverted pyramid", "Common.define.smartArt.textLabeledHierarchy": "Labeled hierarchy", "Common.define.smartArt.textLinearVenn": "Linear Venn", "Common.define.smartArt.textLinedList": "Lined list", "Common.define.smartArt.textList": "List", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Multidirectional cycle", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Name and title organization chart", "Common.define.smartArt.textNestedTarget": "Nested target", "Common.define.smartArt.textNondirectionalCycle": "Nondirectional cycle", "Common.define.smartArt.textOpposingArrows": "Opposing arrows", "Common.define.smartArt.textOpposingIdeas": "Opposing ideas", "Common.define.smartArt.textOrganizationChart": "Organization chart", "Common.define.smartArt.textOther": "Other", "Common.define.smartArt.textPhasedProcess": "Phased process", "Common.define.smartArt.textPicture": "Picture", "Common.define.smartArt.textPictureAccentBlocks": "Picture accent blocks", "Common.define.smartArt.textPictureAccentList": "Picture accent list", "Common.define.smartArt.textPictureAccentProcess": "Picture accent process", "Common.define.smartArt.textPictureCaptionList": "Picture caption list", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "Picture grid", "Common.define.smartArt.textPictureLineup": "Picture lineup", "Common.define.smartArt.textPictureOrganizationChart": "Picture organization chart", "Common.define.smartArt.textPictureStrips": "Picture strips", "Common.define.smartArt.textPieProcess": "Pie process", "Common.define.smartArt.textPlusAndMinus": "Plus and minus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "Process arrows", "Common.define.smartArt.textProcessList": "Process list", "Common.define.smartArt.textPyramid": "Pyramid", "Common.define.smartArt.textPyramidList": "Pyramid list", "Common.define.smartArt.textRadialCluster": "Radial cluster", "Common.define.smartArt.textRadialCycle": "Radial cycle", "Common.define.smartArt.textRadialList": "Radial list", "Common.define.smartArt.textRadialPictureList": "Radial picture list", "Common.define.smartArt.textRadialVenn": "Radial Venn", "Common.define.smartArt.textRandomToResultProcess": "Random to result process", "Common.define.smartArt.textRelationship": "Relationship", "Common.define.smartArt.textRepeatingBendingProcess": "Repeating bending process", "Common.define.smartArt.textReverseList": "Reverse list", "Common.define.smartArt.textSegmentedCycle": "Segmented cycle", "Common.define.smartArt.textSegmentedProcess": "Segmented process", "Common.define.smartArt.textSegmentedPyramid": "Segmented pyramid", "Common.define.smartArt.textSnapshotPictureList": "Snapshot picture list", "Common.define.smartArt.textSpiralPicture": "Spiral picture", "Common.define.smartArt.textSquareAccentList": "Square accent list", "Common.define.smartArt.textStackedList": "Stacked list", "Common.define.smartArt.textStackedVenn": "Stacked <PERSON>n", "Common.define.smartArt.textStaggeredProcess": "Staggered process", "Common.define.smartArt.textStepDownProcess": "Step down process", "Common.define.smartArt.textStepUpProcess": "Step up process", "Common.define.smartArt.textSubStepProcess": "Sub-step process", "Common.define.smartArt.textTabbedArc": "Tabbed arc", "Common.define.smartArt.textTableHierarchy": "Table hierarchy", "Common.define.smartArt.textTableList": "Table list", "Common.define.smartArt.textTabList": "Tab List", "Common.define.smartArt.textTargetList": "Target list", "Common.define.smartArt.textTextCycle": "Text cycle", "Common.define.smartArt.textThemePictureAccent": "Theme picture accent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Theme picture alternating accent", "Common.define.smartArt.textThemePictureGrid": "Theme picture grid", "Common.define.smartArt.textTitledMatrix": "Titled matrix", "Common.define.smartArt.textTitledPictureAccentList": "Titled picture accent list", "Common.define.smartArt.textTitledPictureBlocks": "Titled picture blocks", "Common.define.smartArt.textTitlePictureLineup": "Title picture lineup", "Common.define.smartArt.textTrapezoidList": "Trapezoid list", "Common.define.smartArt.textUpwardArrow": "Upward arrow", "Common.define.smartArt.textVaryingWidthList": "Varying width list", "Common.define.smartArt.textVerticalAccentList": "Vertical accent list", "Common.define.smartArt.textVerticalArrowList": "Vertical arrow list", "Common.define.smartArt.textVerticalBendingProcess": "Vertical bending process", "Common.define.smartArt.textVerticalBlockList": "Vertical block list", "Common.define.smartArt.textVerticalBoxList": "Vertical box list", "Common.define.smartArt.textVerticalBracketList": "Vertical bracket list", "Common.define.smartArt.textVerticalBulletList": "Vertical bullet list", "Common.define.smartArt.textVerticalChevronList": "Vertical chevron list", "Common.define.smartArt.textVerticalCircleList": "Vertical circle list", "Common.define.smartArt.textVerticalCurvedList": "Vertical curved list", "Common.define.smartArt.textVerticalEquation": "Vertical equation", "Common.define.smartArt.textVerticalPictureAccentList": "Vertical picture accent list", "Common.define.smartArt.textVerticalPictureList": "Vertical picture list", "Common.define.smartArt.textVerticalProcess": "Vertical process", "Common.Translation.textMoreButton": "Повече", "Common.Translation.tipFileLocked": "Document is locked for editing. You can make changes and save it as local copy later.", "Common.Translation.tipFileReadOnly": "The file is read-only. To keep your changes, save the file with a new name or in a different location.", "Common.Translation.warnFileLocked": "The file is being edited in another app. You can continue editing and save it as a copy.", "Common.Translation.warnFileLockedBtnEdit": "Create a copy", "Common.Translation.warnFileLockedBtnView": "Open for viewing", "Common.UI.ButtonColored.textAutoColor": "Автоматичен", "Common.UI.ButtonColored.textEyedropper": "Eyedropper", "Common.UI.ButtonColored.textNewColor": "Нов Потребителски Цвят", "Common.UI.ComboBorderSize.txtNoBorders": "Няма граници", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Няма граници", "Common.UI.ComboDataView.emptyComboText": "Няма стилове", "Common.UI.ExtendedColorDialog.addButtonText": "Добави", "Common.UI.ExtendedColorDialog.textCurrent": "Текущ", "Common.UI.ExtendedColorDialog.textHexErr": "Въведената стойност е неправилна. <br> Моля, въведете стойност между 000000 и FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "Въведената стойност е неправилна. <br> Въведете числова стойност между 0 и 255.", "Common.UI.HSBColorPicker.textNoColor": "Няма цвят", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Hide password", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Show password", "Common.UI.SearchBar.textFind": "Find", "Common.UI.SearchBar.tipCloseSearch": "Close find", "Common.UI.SearchBar.tipNextResult": "Next result", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Open advanced settings", "Common.UI.SearchBar.tipPreviousResult": "Previous result", "Common.UI.SearchDialog.textHighlight": "Маркирайте резултатите", "Common.UI.SearchDialog.textMatchCase": "Различаващ главни от малки букви", "Common.UI.SearchDialog.textReplaceDef": "Въведете заместващия текст", "Common.UI.SearchDialog.textSearchStart": "Въведете текста тук", "Common.UI.SearchDialog.textTitle": "Намерете и заменете", "Common.UI.SearchDialog.textTitle2": "Намирам", "Common.UI.SearchDialog.textWholeWords": "Само цели думи", "Common.UI.SearchDialog.txtBtnHideReplace": "Скриване на замяна", "Common.UI.SearchDialog.txtBtnReplace": "Заменете", "Common.UI.SearchDialog.txtBtnReplaceAll": "Замяна на всички", "Common.UI.SynchronizeTip.textDontShow": "Не показвайте това съобщение отново", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "Документът е променен от друг потребител. <br> <PERSON><PERSON><PERSON><PERSON>, кликнете върху, за да запазите промените си и да презаредите актуализациите.", "Common.UI.ThemeColorPalette.textRecentColors": "Recent colors", "Common.UI.ThemeColorPalette.textStandartColors": "Стандартни цветове", "Common.UI.ThemeColorPalette.textThemeColors": "Цветовете на темата", "Common.UI.Themes.txtThemeClassicLight": "Класически светла", "Common.UI.Themes.txtThemeContrastDark": "Контра<PERSON>тна тъмна", "Common.UI.Themes.txtThemeDark": "Тъмна", "Common.UI.Themes.txtThemeGray": "Сива", "Common.UI.Themes.txtThemeLight": "Светла", "Common.UI.Themes.txtThemeSystem": "Същото като в системата", "Common.UI.Window.cancelButtonText": "Отказ", "Common.UI.Window.closeButtonText": "Затвори", "Common.UI.Window.noButtonText": "Не", "Common.UI.Window.okButtonText": "Добре", "Common.UI.Window.textConfirmation": "Потвърждаване", "Common.UI.Window.textDontShow": "Не показвайте това съобщение отново", "Common.UI.Window.textError": "Грешка", "Common.UI.Window.textInformation": "Информация", "Common.UI.Window.textWarning": "Внимание", "Common.UI.Window.yesButtonText": "Да", "Common.Utils.Metric.txtCm": "см", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Accent", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "Background", "Common.Utils.ThemeColor.txtBlack": "Black", "Common.Utils.ThemeColor.txtBlue": "Blue", "Common.Utils.ThemeColor.txtBrightGreen": "Bright green", "Common.Utils.ThemeColor.txtBrown": "<PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Dark blue", "Common.Utils.ThemeColor.txtDarker": "Darker", "Common.Utils.ThemeColor.txtDarkGray": "Dark gray", "Common.Utils.ThemeColor.txtDarkGreen": "Dark green", "Common.Utils.ThemeColor.txtDarkPurple": "Dark purple", "Common.Utils.ThemeColor.txtDarkRed": "Dark red", "Common.Utils.ThemeColor.txtDarkTeal": "Dark teal", "Common.Utils.ThemeColor.txtDarkYellow": "Dark yellow", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "Сива", "Common.Utils.ThemeColor.txtGreen": "Green", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "Light blue", "Common.Utils.ThemeColor.txtLighter": "Lighter", "Common.Utils.ThemeColor.txtLightGray": "Light gray", "Common.Utils.ThemeColor.txtLightGreen": "Light green", "Common.Utils.ThemeColor.txtLightOrange": "Light orange", "Common.Utils.ThemeColor.txtLightYellow": "Light yellow", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "Pink", "Common.Utils.ThemeColor.txtPurple": "Purple", "Common.Utils.ThemeColor.txtRed": "Red", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Sky blue", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Text", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "White", "Common.Utils.ThemeColor.txtYellow": "Yellow", "Common.Views.About.txtAddress": "адрес:", "Common.Views.About.txtLicensee": "ЛИЦЕНЗОПОЛУЧАТЕЛЯТ", "Common.Views.About.txtLicensor": "НОСИТЕЛЯТ", "Common.Views.About.txtMail": "електронна поща:", "Common.Views.About.txtPoweredBy": "Задвижвани от", "Common.Views.About.txtTel": "тел.: ", "Common.Views.About.txtVersion": "Версия", "Common.Views.AutoCorrectDialog.textAdd": "Add", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Apply as you work", "Common.Views.AutoCorrectDialog.textAutoCorrect": "AutoCorrect", "Common.Views.AutoCorrectDialog.textAutoFormat": "AutoFormat as you type", "Common.Views.AutoCorrectDialog.textBy": "By", "Common.Views.AutoCorrectDialog.textDelete": "Изтрий", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet and network paths with hyperlinks", "Common.Views.AutoCorrectDialog.textMathCorrect": "Math AutoCorrect", "Common.Views.AutoCorrectDialog.textNewRowCol": "Include new rows and columns in table", "Common.Views.AutoCorrectDialog.textRecognized": "Recognized functions", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "The following expressions are recognized math expressions. They will not be automatically italicized.", "Common.Views.AutoCorrectDialog.textReplace": "Replace", "Common.Views.AutoCorrectDialog.textReplaceText": "Replace as you type", "Common.Views.AutoCorrectDialog.textReplaceType": "Replace text as you type", "Common.Views.AutoCorrectDialog.textReset": "Reset", "Common.Views.AutoCorrectDialog.textResetAll": "Reset to default", "Common.Views.AutoCorrectDialog.textRestore": "Възстанови", "Common.Views.AutoCorrectDialog.textTitle": "AutoCorrect", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Recognized functions must contain only the letters A through Z, uppercase or lowercase.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Any expression you added will be removed and the removed ones will be restored. Do you want to continue?", "Common.Views.AutoCorrectDialog.warnReplace": "The autocorrect entry for %1 already exists. Do you want to replace it?", "Common.Views.AutoCorrectDialog.warnReset": "Any autocorrect you added will be removed and the changed ones will be restored to their original values. Do you want to continue?", "Common.Views.AutoCorrectDialog.warnRestore": "The autocorrect entry for %1 will be reset to its original value. Do you want to continue?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "Изпращам", "Common.Views.Comments.mniAuthorAsc": "Author A to Z", "Common.Views.Comments.mniAuthorDesc": "Author Z to A", "Common.Views.Comments.mniDateAsc": "Oldest", "Common.Views.Comments.mniDateDesc": "Newest", "Common.Views.Comments.mniFilterGroups": "Filter by Group", "Common.Views.Comments.mniPositionAsc": "From top", "Common.Views.Comments.mniPositionDesc": "From bottom", "Common.Views.Comments.textAdd": "Добави", "Common.Views.Comments.textAddComment": "Добави коментар ", "Common.Views.Comments.textAddCommentToDoc": "Добави коментар към документа", "Common.Views.Comments.textAddReply": "Добави отговор", "Common.Views.Comments.textAll": "All", "Common.Views.Comments.textAnonym": "Гост", "Common.Views.Comments.textCancel": "Отказ", "Common.Views.Comments.textClose": "Затвори", "Common.Views.Comments.textClosePanel": "Close comments", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "Коментари", "Common.Views.Comments.textEdit": "Добре", "Common.Views.Comments.textEnterCommentHint": "Въведете коментара си тук", "Common.Views.Comments.textHintAddComment": "Добави коментар", "Common.Views.Comments.textOpenAgain": "Отвори отново", "Common.Views.Comments.textReply": "Отговор", "Common.Views.Comments.textResolve": "Решение", "Common.Views.Comments.textResolved": "<PERSON>е<PERSON><PERSON>н", "Common.Views.Comments.textSort": "Sort comments", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "You have no permission to reopen the comment", "Common.Views.Comments.txtEmpty": "There are no comments in the sheet.", "Common.Views.CopyWarningDialog.textDontShow": "Не показвайте това съобщение отново", "Common.Views.CopyWarningDialog.textMsg": "Действията за копиране, изрязване и поставяне с помощта на бутоните на лентата с инструменти на редактора и действията в контекстното меню ще се изпълняват само в този раздел на редактора. <br> <br> За да копирате или поставите в или от приложения извън раздела за редактори, използвайте следните комбинации от клавиатури:", "Common.Views.CopyWarningDialog.textTitle": "Действия за копиране, изрязване и поставяне", "Common.Views.CopyWarningDialog.textToCopy": "за копиране", "Common.Views.CopyWarningDialog.textToCut": "за изрязване", "Common.Views.CopyWarningDialog.textToPaste": "за поставяне", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "Зареждане ...", "Common.Views.DocumentAccessDialog.textTitle": "Настройки за споделяне", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "Eraser", "Common.Views.Draw.hintSelect": "Select", "Common.Views.Draw.txtEraser": "Eraser", "Common.Views.Draw.txtHighlighter": "Highlighter", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Pen", "Common.Views.Draw.txtSelect": "Select", "Common.Views.Draw.txtSize": "Size", "Common.Views.EditNameDialog.textLabel": "Label:", "Common.Views.EditNameDialog.textLabelError": "Label must not be empty.", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "Понастоящем документът се редактира от няколко потребители.", "Common.Views.Header.textAddFavorite": "<PERSON> as favorite", "Common.Views.Header.textAdvSettings": "Разширени настройки", "Common.Views.Header.textBack": "Mестоположението на файла", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "Скриване на лентата с инструменти", "Common.Views.Header.textHideLines": "Скриване на владетели", "Common.Views.Header.textHideStatusBar": "Скриване на лентата на състоянието", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "Read only", "Common.Views.Header.textRemoveFavorite": "Remove from Favorites", "Common.Views.Header.textSaveBegin": "Се запазва ...", "Common.Views.Header.textSaveChanged": "Променено", "Common.Views.Header.textSaveEnd": "Всички промени са запазени", "Common.Views.Header.textSaveExpander": "Всички промени са запазени", "Common.Views.Header.textShare": "Share", "Common.Views.Header.textZoom": "Мащ<PERSON>б", "Common.Views.Header.tipAccessRights": "Управление на правата за достъп до документи", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "Свали файл", "Common.Views.Header.tipGoEdit": "Редактиране на текущия файл", "Common.Views.Header.tipPrint": "Печат на файла", "Common.Views.Header.tipPrintQuick": "Quick print", "Common.Views.Header.tipRedo": "Повтори", "Common.Views.Header.tipSave": "Запази", "Common.Views.Header.tipSearch": "Find", "Common.Views.Header.tipUndo": "Отмени", "Common.Views.Header.tipUndock": "Undock into separate window", "Common.Views.Header.tipUsers": "View users", "Common.Views.Header.tipViewSettings": "Преглед на настройките", "Common.Views.Header.tipViewUsers": "Преглеждайте потребителите и управлявайте правата за достъп до документи", "Common.Views.Header.txtAccessRights": "Промяна на правата за достъп", "Common.Views.Header.txtRename": "Преименувам", "Common.Views.History.textCloseHistory": "Close history", "Common.Views.History.textHideAll": "Hide detailed changes", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "Възстанови", "Common.Views.History.textShowAll": "Show detailed changes", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "Поставете URL адрес на изображение:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Това поле е задължително", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Това поле трябва да е URL адрес във формат \"http://www.example.com\"", "Common.Views.ListSettingsDialog.textBulleted": "Bulleted", "Common.Views.ListSettingsDialog.textFromFile": "From file", "Common.Views.ListSettingsDialog.textFromStorage": "From storage", "Common.Views.ListSettingsDialog.textFromUrl": "From URL", "Common.Views.ListSettingsDialog.textNumbering": "Numbered", "Common.Views.ListSettingsDialog.textSelect": "Select from", "Common.Views.ListSettingsDialog.tipChange": "Change bullet", "Common.Views.ListSettingsDialog.txtBullet": "Bullet", "Common.Views.ListSettingsDialog.txtColor": "Цвят", "Common.Views.ListSettingsDialog.txtImage": "Image", "Common.Views.ListSettingsDialog.txtImport": "Import", "Common.Views.ListSettingsDialog.txtNewBullet": "New bullet", "Common.Views.ListSettingsDialog.txtNewImage": "New image", "Common.Views.ListSettingsDialog.txtNone": "None", "Common.Views.ListSettingsDialog.txtOfText": "% of text", "Common.Views.ListSettingsDialog.txtSize": "Size", "Common.Views.ListSettingsDialog.txtStart": "Start at", "Common.Views.ListSettingsDialog.txtSymbol": "Symbol", "Common.Views.ListSettingsDialog.txtTitle": "List settings", "Common.Views.ListSettingsDialog.txtType": "Type", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "Затвори файла", "Common.Views.OpenDialog.textInvalidRange": "Invalid cells range", "Common.Views.OpenDialog.textSelectData": "Select data", "Common.Views.OpenDialog.txtAdvanced": "Допълнително", "Common.Views.OpenDialog.txtColon": "Дебело черво", "Common.Views.OpenDialog.txtComma": "Запетая", "Common.Views.OpenDialog.txtDelimiter": "Разделител", "Common.Views.OpenDialog.txtDestData": "Choose where to put the data", "Common.Views.OpenDialog.txtEmpty": "This field is required", "Common.Views.OpenDialog.txtEncoding": "Кодиране", "Common.Views.OpenDialog.txtIncorrectPwd": "Паролата е неправилна.", "Common.Views.OpenDialog.txtOpenFile": "Въведете парола, за да отворите файла", "Common.Views.OpenDialog.txtOther": "Друг", "Common.Views.OpenDialog.txtPassword": "Парола", "Common.Views.OpenDialog.txtPreview": "Предварителен преглед", "Common.Views.OpenDialog.txtProtected": "След като въведете паролата и отворите файла, текущата парола за файла ще бъде нулирана.", "Common.Views.OpenDialog.txtSemicolon": "Точка и запетая", "Common.Views.OpenDialog.txtSpace": "Пространство", "Common.Views.OpenDialog.txtTab": "Раздел", "Common.Views.OpenDialog.txtTitle": "Изберете опции %1", "Common.Views.OpenDialog.txtTitleProtected": "Защитен файл", "Common.Views.PasswordDialog.txtDescription": "Задайте парола, за да защитите този документ", "Common.Views.PasswordDialog.txtIncorrectPwd": "Паролата за потвърждение не е идентична", "Common.Views.PasswordDialog.txtPassword": "Парола", "Common.Views.PasswordDialog.txtRepeat": "Повтори паролата", "Common.Views.PasswordDialog.txtTitle": "Задайте парола", "Common.Views.PasswordDialog.txtWarning": "Внимание: Ако загубите или забравите паролата, тя не може да се възстанови. Го съхранявайте на сигурно място.", "Common.Views.PluginDlg.textLoading": "Зареждане", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "Добавки", "Common.Views.Plugins.strPlugins": "Добавки", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "Начало", "Common.Views.Plugins.textStop": "Спри се", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "Шифроване с парола", "Common.Views.Protection.hintDelPwd": "Delete password", "Common.Views.Protection.hintPwd": "Промяна или изтриване на парола", "Common.Views.Protection.hintSignature": "Добавете цифров подпис или линия за подпис", "Common.Views.Protection.txtAddPwd": "Добавяне на парола", "Common.Views.Protection.txtChangePwd": "Промяна на паролата", "Common.Views.Protection.txtDeletePwd": "Изтриване на паролата", "Common.Views.Protection.txtEncrypt": "Шифроване", "Common.Views.Protection.txtInvisibleSignature": "Добавете електронен подпис", "Common.Views.Protection.txtSignature": "Под<PERSON>ис", "Common.Views.Protection.txtSignatureLine": "Добавете линия за подпис", "Common.Views.RecentFiles.txtOpenRecent": "Open Recent", "Common.Views.RenameDialog.textName": "Име на файл", "Common.Views.RenameDialog.txtInvalidName": "Името на файла не може да съдържа нито един от следните знаци: ", "Common.Views.ReviewChanges.hintNext": "За следващата промяна", "Common.Views.ReviewChanges.hintPrev": "Към предишна промяна", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Съвместно редактиране в реално време. Всички промени се запазват автоматично.", "Common.Views.ReviewChanges.strStrict": "Стриктен", "Common.Views.ReviewChanges.strStrictDesc": "Използвайте бутона „Запазване“, за да синхронизирате промените, които правите вие ​​и другите.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Приеми текущата промяна", "Common.Views.ReviewChanges.tipCoAuthMode": "Задайте режим на съвместно редактиране", "Common.Views.ReviewChanges.tipCommentRem": "Delete comments", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Delete current comments", "Common.Views.ReviewChanges.tipCommentResolve": "Resolve comments", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolve current comments", "Common.Views.ReviewChanges.tipHistory": "Показване на историята на версиите", "Common.Views.ReviewChanges.tipRejectCurrent": "Отхвърляне на текущата промяна", "Common.Views.ReviewChanges.tipReview": "Проследнение на промените", "Common.Views.ReviewChanges.tipReviewView": "Изберете режима, в който искате да се показват промените", "Common.Views.ReviewChanges.tipSetDocLang": "Задайте език на документа", "Common.Views.ReviewChanges.tipSetSpelling": "Проверка на правописа", "Common.Views.ReviewChanges.tipSharing": "Управление на правата за достъп до документи", "Common.Views.ReviewChanges.txtAccept": "Приемам", "Common.Views.ReviewChanges.txtAcceptAll": "Приемете всички промени", "Common.Views.ReviewChanges.txtAcceptChanges": "Приемане на промените", "Common.Views.ReviewChanges.txtAcceptCurrent": "Приеми текущата промяна", "Common.Views.ReviewChanges.txtChat": "Чат", "Common.Views.ReviewChanges.txtClose": "Затвори", "Common.Views.ReviewChanges.txtCoAuthMode": "Режим на съвместно редактиране", "Common.Views.ReviewChanges.txtCommentRemAll": "Delete all comments", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Delete current comments", "Common.Views.ReviewChanges.txtCommentRemMy": "Delete my comments", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Delete my current comments", "Common.Views.ReviewChanges.txtCommentRemove": "Премахване", "Common.Views.ReviewChanges.txtCommentResolve": "Resolve", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolve all comments", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolve current comments", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolve my comments", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolve My Current Comments", "Common.Views.ReviewChanges.txtDocLang": "Език", "Common.Views.ReviewChanges.txtFinal": "Всички промени са приети (визуализация)", "Common.Views.ReviewChanges.txtFinalCap": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtHistory": "История на версиите", "Common.Views.ReviewChanges.txtMarkup": "Всички промени (редактиране)", "Common.Views.ReviewChanges.txtMarkupCap": "Промени", "Common.Views.ReviewChanges.txtNext": "Следващия", "Common.Views.ReviewChanges.txtOriginal": "Всички отхвърлени промени (предварителен преглед)", "Common.Views.ReviewChanges.txtOriginalCap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtPrev": "Предишен", "Common.Views.ReviewChanges.txtReject": "Отхвърляне", "Common.Views.ReviewChanges.txtRejectAll": "Отхвърляне на всички промени", "Common.Views.ReviewChanges.txtRejectChanges": "Отхвърляне на промените", "Common.Views.ReviewChanges.txtRejectCurrent": "Отхвърляне на текущата промяна", "Common.Views.ReviewChanges.txtSharing": "Споделяне", "Common.Views.ReviewChanges.txtSpelling": "Проверка на правописа", "Common.Views.ReviewChanges.txtTurnon": "Проследяване на промените", "Common.Views.ReviewChanges.txtView": "Режим на дисплея", "Common.Views.ReviewPopover.textAdd": "Добави", "Common.Views.ReviewPopover.textAddReply": "Добави отговор", "Common.Views.ReviewPopover.textCancel": "Отказ", "Common.Views.ReviewPopover.textClose": "Затвори", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "Добре", "Common.Views.ReviewPopover.textEnterComment": "Enter your comment here", "Common.Views.ReviewPopover.textMention": "+mention will provide access to the document and send an email", "Common.Views.ReviewPopover.textMentionNotify": "+mention will notify the user via email", "Common.Views.ReviewPopover.textOpenAgain": "Отвори отново", "Common.Views.ReviewPopover.textReply": "Отговор", "Common.Views.ReviewPopover.textResolve": "Решение", "Common.Views.ReviewPopover.textViewResolved": "You have no permission to reopen the comment", "Common.Views.ReviewPopover.txtDeleteTip": "Изтрий", "Common.Views.ReviewPopover.txtEditTip": "Edit", "Common.Views.SaveAsDlg.textLoading": "Зареждане", "Common.Views.SaveAsDlg.textTitle": "Папка за запис", "Common.Views.SearchPanel.textByColumns": "By columns", "Common.Views.SearchPanel.textByRows": "By rows", "Common.Views.SearchPanel.textCaseSensitive": "Case sensitive", "Common.Views.SearchPanel.textCell": "Cell", "Common.Views.SearchPanel.textCloseSearch": "Close find", "Common.Views.SearchPanel.textContentChanged": "Document changed.", "Common.Views.SearchPanel.textFind": "Find", "Common.Views.SearchPanel.textFindAndReplace": "Find and replace", "Common.Views.SearchPanel.textFormula": "Formula", "Common.Views.SearchPanel.textFormulas": "Formulas", "Common.Views.SearchPanel.textItemEntireCell": "Entire cell contents", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} items successfully replaced.", "Common.Views.SearchPanel.textLookIn": "Погледни в", "Common.Views.SearchPanel.textMatchUsingRegExp": "Match using regular expressions", "Common.Views.SearchPanel.textName": "Name", "Common.Views.SearchPanel.textNoMatches": "No matches", "Common.Views.SearchPanel.textNoSearchResults": "No search results", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} items replaced. Remaining {2} items are locked by other users.", "Common.Views.SearchPanel.textReplace": "Replace", "Common.Views.SearchPanel.textReplaceAll": "Replace All", "Common.Views.SearchPanel.textReplaceWith": "Replace with", "Common.Views.SearchPanel.textSearch": "Search", "Common.Views.SearchPanel.textSearchAgain": "{0}Perform new search{1} for accurate results.", "Common.Views.SearchPanel.textSearchHasStopped": "Search has stopped", "Common.Views.SearchPanel.textSearchOptions": "Search options", "Common.Views.SearchPanel.textSearchResults": "Search results: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textSelectDataRange": "Select Data range", "Common.Views.SearchPanel.textSheet": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSpecificRange": "Specific range", "Common.Views.SearchPanel.textTooManyResults": "There are too many results to show here", "Common.Views.SearchPanel.textValue": "Value", "Common.Views.SearchPanel.textValues": "Values", "Common.Views.SearchPanel.textWholeWords": "Whole words only", "Common.Views.SearchPanel.textWithin": "Within", "Common.Views.SearchPanel.textWorkbook": "Workbook", "Common.Views.SearchPanel.tipNextResult": "Next result", "Common.Views.SearchPanel.tipPreviousResult": "Previous result", "Common.Views.SelectFileDlg.textLoading": "Зареждане", "Common.Views.SelectFileDlg.textTitle": "Изберете източник на данни", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "Получер", "Common.Views.SignDialog.textCertificate": "Сертификат", "Common.Views.SignDialog.textChange": "Промяна", "Common.Views.SignDialog.textInputName": "Въведете името на подписващото лице", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "Signer name must not be empty.", "Common.Views.SignDialog.textPurpose": "Цел за подписване на този документ", "Common.Views.SignDialog.textSelect": "Изберете", "Common.Views.SignDialog.textSelectImage": "Изберете изображение", "Common.Views.SignDialog.textSignature": "Подписът изглежда като", "Common.Views.SignDialog.textTitle": "Подпишете документ", "Common.Views.SignDialog.textUseImage": "или кликнете върху „Избор на изображение“, за да използвате снимка като подпис", "Common.Views.SignDialog.textValid": "Валидно от %1 до %2", "Common.Views.SignDialog.tipFontName": "Име на шрифта", "Common.Views.SignDialog.tipFontSize": "Размер на шрифта", "Common.Views.SignSettingsDialog.textAllowComment": "Позволете на сигналиста да добавите коментар в диалога за подпис", "Common.Views.SignSettingsDialog.textDefInstruction": "Before signing this document, verify that the content you are signing is correct.", "Common.Views.SignSettingsDialog.textInfoEmail": "Електронна поща", "Common.Views.SignSettingsDialog.textInfoName": "Име", "Common.Views.SignSettingsDialog.textInfoTitle": "Заглавие на подписващия", "Common.Views.SignSettingsDialog.textInstructions": "Инструкции за подписващия", "Common.Views.SignSettingsDialog.textShowDate": "Покажете датата на знака в реда за подпис", "Common.Views.SignSettingsDialog.textTitle": "Настройка на подпис", "Common.Views.SignSettingsDialog.txtEmpty": "Това поле е задължително", "Common.Views.SymbolTableDialog.textCharacter": "Character", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX value", "Common.Views.SymbolTableDialog.textCopyright": "Copyright sign", "Common.Views.SymbolTableDialog.textDCQuote": "Closing double quote", "Common.Views.SymbolTableDialog.textDOQuote": "Opening double quote", "Common.Views.SymbolTableDialog.textEllipsis": "Horizontal ellipsis", "Common.Views.SymbolTableDialog.textEmDash": "Em dash", "Common.Views.SymbolTableDialog.textEmSpace": "Em space", "Common.Views.SymbolTableDialog.textEnDash": "En dash", "Common.Views.SymbolTableDialog.textEnSpace": "En space", "Common.Views.SymbolTableDialog.textFont": "Font", "Common.Views.SymbolTableDialog.textNBHyphen": "Non-breaking hyphen", "Common.Views.SymbolTableDialog.textNBSpace": "No-break space", "Common.Views.SymbolTableDialog.textPilcrow": "Pilcrow sign", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em space", "Common.Views.SymbolTableDialog.textRange": "Range", "Common.Views.SymbolTableDialog.textRecent": "Recently used symbols", "Common.Views.SymbolTableDialog.textRegistered": "Registered sign", "Common.Views.SymbolTableDialog.textSCQuote": "Closing single quote", "Common.Views.SymbolTableDialog.textSection": "Section sign", "Common.Views.SymbolTableDialog.textShortcut": "Shortcut key", "Common.Views.SymbolTableDialog.textSHyphen": "Soft hyphen", "Common.Views.SymbolTableDialog.textSOQuote": "Opening single quote", "Common.Views.SymbolTableDialog.textSpecial": "Special characters", "Common.Views.SymbolTableDialog.textSymbols": "Symbols", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Trademark symbol", "Common.Views.UserNameDialog.textDontShow": "Don't ask me again", "Common.Views.UserNameDialog.textLabel": "Label:", "Common.Views.UserNameDialog.textLabelError": "Label must not be empty.", "SSE.Controllers.DataTab.strSheet": "Sheet", "SSE.Controllers.DataTab.textAddExternalData": "The link to an external source has been added. You can update such links in the Data tab.", "SSE.Controllers.DataTab.textColumns": "Columns", "SSE.Controllers.DataTab.textContinue": "Continue", "SSE.Controllers.DataTab.textDontUpdate": "Don't Update", "SSE.Controllers.DataTab.textEmptyUrl": "You need to specify URL.", "SSE.Controllers.DataTab.textRows": "Rows", "SSE.Controllers.DataTab.textTurnOff": "Turn off AutoUpdate", "SSE.Controllers.DataTab.textUpdate": "Update", "SSE.Controllers.DataTab.textWizard": "Текст в колони", "SSE.Controllers.DataTab.txtDataValidation": "Data Validation", "SSE.Controllers.DataTab.txtErrorExternalLink": "Error: updating is failed", "SSE.Controllers.DataTab.txtExpand": "Разширете", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "The data next to the selection will not be removed. Do you want to expand the selection to include the adjacent data or continue with the currently selected cells only?", "SSE.Controllers.DataTab.txtExtendDataValidation": "The selection contains some cells without Data Validation settings.<br>Do you want to extend Data Validation to these cells?", "SSE.Controllers.DataTab.txtImportWizard": "Text Import Wizard", "SSE.Controllers.DataTab.txtRemDuplicates": "Remove Duplicates", "SSE.Controllers.DataTab.txtRemoveDataValidation": "The selection contains more than one type of validation.<br>Erase current settings and continue?", "SSE.Controllers.DataTab.txtRemSelected": "Remove in selected", "SSE.Controllers.DataTab.txtUrlTitle": "Paste a data URL", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "This workbook contains links to external sources which update automatically. This might be unsafe.<br><br>If you trust them, press Continue.", "SSE.Controllers.DataTab.warnUpdateExternalData": "This workbook contains links to one or more external sources that could be unsafe.<br>If you trust the links, update them to get the latest data.", "SSE.Controllers.DocumentHolder.alignmentText": "Подравняване", "SSE.Controllers.DocumentHolder.centerText": "Център", "SSE.Controllers.DocumentHolder.deleteColumnText": "Изтриване на колона", "SSE.Controllers.DocumentHolder.deleteRowText": "Изтриване на ред", "SSE.Controllers.DocumentHolder.deleteText": "Изтрий", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Референтната връзка не съществува. Моля, коригирайте връзката или я изтрийте.", "SSE.Controllers.DocumentHolder.guestText": "Гост", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Колона вляво", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Колона вдясно", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Ред по-горе", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Ред по-долу", "SSE.Controllers.DocumentHolder.insertText": "Вмъкни", "SSE.Controllers.DocumentHolder.leftText": "Наляво", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Внимание", "SSE.Controllers.DocumentHolder.rightText": "Прав", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "AutoCorrect options", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Ширина на колоната {0} символа ({1} пиксела)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Височина на реда {0} точки ({1} пиксела)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Натиснете CTRL и кликнете върху връзката", "SSE.Controllers.DocumentHolder.textInsertLeft": "Вмъкване в ляво", "SSE.Controllers.DocumentHolder.textInsertTop": "Вмъкване централно", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Paste special", "SSE.Controllers.DocumentHolder.textStopExpand": "Stop automatically expanding tables", "SSE.Controllers.DocumentHolder.textSym": "сим", "SSE.Controllers.DocumentHolder.tipIsLocked": "Този елемент се редактира от друг потребител.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Над средното", "SSE.Controllers.DocumentHolder.txtAddBottom": "Добавяне на долната граница", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Добавете лента за фракции", "SSE.Controllers.DocumentHolder.txtAddHor": "Добавете хоризонтална линия", "SSE.Controllers.DocumentHolder.txtAddLB": "Доабви лява долна линия", "SSE.Controllers.DocumentHolder.txtAddLeft": "Добави лява рамка", "SSE.Controllers.DocumentHolder.txtAddLT": "Доабви лява горна линия", "SSE.Controllers.DocumentHolder.txtAddRight": "Добавете дясна граница", "SSE.Controllers.DocumentHolder.txtAddTop": "Добавяне на горната граница", "SSE.Controllers.DocumentHolder.txtAddVer": "Добавете вертикална линия", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Подравняване по характер", "SSE.Controllers.DocumentHolder.txtAll": "(Всичко)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Returns the entire contents of the table or specified table columns including column headers, data and total rows", "SSE.Controllers.DocumentHolder.txtAnd": "и", "SSE.Controllers.DocumentHolder.txtBegins": "Започва с", "SSE.Controllers.DocumentHolder.txtBelowAve": "Под средното", "SSE.Controllers.DocumentHolder.txtBlanks": "(Заготовки)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Гранични свойства", "SSE.Controllers.DocumentHolder.txtBottom": "Отдоло", "SSE.Controllers.DocumentHolder.txtByField": "%1 of %2", "SSE.Controllers.DocumentHolder.txtColumn": "Колона", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Изравняване на колона", "SSE.Controllers.DocumentHolder.txtContains": "Съдържа", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Link copied to the clipboard", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Returns the data cells of the table or specified table columns", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Намалете размера на аргумента", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Изтриване на аргумент", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Изтриване на ръчно прекъсване", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Изтриване на заграждащи символи", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Изтрийте обграждащите символи и разделители", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Изтриване на уравнението", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Изтриване на char", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Изтриване на радикал", "SSE.Controllers.DocumentHolder.txtEnds": "Завършва със", "SSE.Controllers.DocumentHolder.txtEquals": "Равно на", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Равен на цвета на клетката", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Равен на цвета на шрифта", "SSE.Controllers.DocumentHolder.txtExpand": "Разширяване и сортиране", "SSE.Controllers.DocumentHolder.txtExpandSort": "Данните до селекцията няма да бъдат сортирани. Искате ли да разширите избора, за да включите съседните данни или да продължите с сортирането само на избраните в момента клетки?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Отдоло", "SSE.Controllers.DocumentHolder.txtFilterTop": "Отгоре", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Промяна към линейна фракция", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Промяна на изкривена фракция", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Промяна на натрупаната фракция", "SSE.Controllers.DocumentHolder.txtGreater": "По-голям от", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "По-голяма или равна на", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Char над текста", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Char под текста", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Returns the column headers for the table or specified table columns", "SSE.Controllers.DocumentHolder.txtHeight": "Висо<PERSON>ина", "SSE.Controllers.DocumentHolder.txtHideBottom": "Скриване на долната граница", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Скриване на долния лимит", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Скриване на затварящата скоба", "SSE.Controllers.DocumentHolder.txtHideDegree": "Скриване на степен", "SSE.Controllers.DocumentHolder.txtHideHor": "Скриване на хоризонталната линия", "SSE.Controllers.DocumentHolder.txtHideLB": "Скриване на долния ред вляво", "SSE.Controllers.DocumentHolder.txtHideLeft": "Скриване на лявата граница", "SSE.Controllers.DocumentHolder.txtHideLT": "Скриване на левия горен ред", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Скриване на отварящата скоба", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Скриване на контейнера", "SSE.Controllers.DocumentHolder.txtHideRight": "Скриване на дясната граница", "SSE.Controllers.DocumentHolder.txtHideTop": "Скриване на горната граница", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Скриване на горната граница", "SSE.Controllers.DocumentHolder.txtHideVer": "Скриване на вертикалната линия", "SSE.Controllers.DocumentHolder.txtImportWizard": "Съветник за импортиране на текст", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Увеличете размера на аргумента", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Вмъкване на аргумент след", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Вмъкнете аргумент преди", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Поставете ръчна почивка", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Поставете уравнение след", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Вмъкнете преди това уравнението", "SSE.Controllers.DocumentHolder.txtItems": "елементи", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Запазете само текста", "SSE.Controllers.DocumentHolder.txtLess": "По-малко от", "SSE.Controllers.DocumentHolder.txtLessEquals": "По-малко или равно на", "SSE.Controllers.DocumentHolder.txtLimitChange": "Място за промяна на ограниченията", "SSE.Controllers.DocumentHolder.txtLimitOver": "Ограничете текста", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Ограничете по текст", "SSE.Controllers.DocumentHolder.txtLockSort": "Data is found next to your selection, but you do not have sufficient permissions to change those cells.<br>Do you wish to continue with the current selection?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Сравнете скобите с височината на аргумента", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Матрично подравняване", "SSE.Controllers.DocumentHolder.txtNoChoices": "Няма избор за запълване на клетката. <br> За подмяна могат да се избират само текстови стойности от колоната.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Не започва с", "SSE.Controllers.DocumentHolder.txtNotContains": "Не съдържа", "SSE.Controllers.DocumentHolder.txtNotEnds": "Не завършва с", "SSE.Controllers.DocumentHolder.txtNotEquals": "Не е равно", "SSE.Controllers.DocumentHolder.txtOr": "или", "SSE.Controllers.DocumentHolder.txtOverbar": "Завършете текста", "SSE.Controllers.DocumentHolder.txtPaste": "Паста", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Формула без граници", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Формула + ширина на колоната", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Форматиране на дестинация", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Поставете само форматирането", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Формула + формат на номера", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Поставете само формулата", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Формула + всички формати", "SSE.Controllers.DocumentHolder.txtPasteLink": "Поставете връзката", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Свързана снимка", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Обединяване на условното форматиране", "SSE.Controllers.DocumentHolder.txtPastePicture": "Снимка", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Форматиране на източника", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Транспониране", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Стойност + всички форматиране", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Стойност + формат на номера", "SSE.Controllers.DocumentHolder.txtPasteValues": "Поставете само стойността", "SSE.Controllers.DocumentHolder.txtPercent": "на сто", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Възстановяване на авторазширяване на таблицата", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Премахване на фракционната лента", "SSE.Controllers.DocumentHolder.txtRemLimit": "Премахване на ограничението", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Премахване на акцент", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Премахване на лентата", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Do you want to remove this signature?<br>It can't be undone.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Премахване на скриптове", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Премахване на индекса", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Премахване на горен индекс", "SSE.Controllers.DocumentHolder.txtRowHeight": "Височина ред", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Скриптове след текст", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Скриптове преди текст", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Показване на долната граница", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Показване на затварящата скоба", "SSE.Controllers.DocumentHolder.txtShowDegree": "Покажете степен", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Показване на скоба за отваряне", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Показване на контейнер", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Показване на горната граница", "SSE.Controllers.DocumentHolder.txtSorting": "Сортиране", "SSE.Controllers.DocumentHolder.txtSortSelected": "Сортирането е избрано", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Разтягащи скоби", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Choose only this row of the specified column", "SSE.Controllers.DocumentHolder.txtTop": "Отгоре", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Returns the total rows for the table or specified table columns", "SSE.Controllers.DocumentHolder.txtUnderbar": "Бар под текст", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Отмяна на автоматичното разширяване на таблицата", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Използване на съветника за импортиране на текст", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Clicking this link can be harmful to your device and data.<br>Are you sure you want to continue?", "SSE.Controllers.DocumentHolder.txtWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.warnFilterError": "You need at least one field in the Values area in order to apply a value filter.", "SSE.Controllers.FormulaDialog.sCategoryAll": "All", "SSE.Controllers.FormulaDialog.sCategoryCube": "C<PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Custom", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Database", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Date & Time", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Engineering", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Information", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 last used", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Логичен", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Търсене и справка", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Math & Trig", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistical", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Текст и данни", "SSE.Controllers.LeftMenu.newDocumentTitle": "Електронна таблица без име", "SSE.Controllers.LeftMenu.textByColumns": "По колони", "SSE.Controllers.LeftMenu.textByRows": "По редове", "SSE.Controllers.LeftMenu.textFormulas": "Формули", "SSE.Controllers.LeftMenu.textItemEntireCell": "Цялото съдържание на клетката", "SSE.Controllers.LeftMenu.textLoadHistory": "Loading version history...", "SSE.Controllers.LeftMenu.textLookin": "Погледни вътре", "SSE.Controllers.LeftMenu.textNoTextFound": "Данните, които търсите, не можаха да бъдат намерени. Моля, коригирайте опциите си за търсене.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Замяната е направена. {0} събития бяха пропуснати.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Търсенето е направено. Заместени случаи: {0}", "SSE.Controllers.LeftMenu.textSave": "Save", "SSE.Controllers.LeftMenu.textSearch": "Търсене", "SSE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "SSE.Controllers.LeftMenu.textSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textValues": "Стойности", "SSE.Controllers.LeftMenu.textWarning": "Внимание", "SSE.Controllers.LeftMenu.textWithin": "В рамките на", "SSE.Controllers.LeftMenu.textWorkbook": "Работна книга", "SSE.Controllers.LeftMenu.txtUntitled": "Неозаглавен", "SSE.Controllers.LeftMenu.warnDownloadAs": "Ако продължите да записвате в този формат, всички функции, с изключение на текста, ще бъдат загубени. <br> Сигурни ли сте, че искате да продължите?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "The CSV format does not support saving a multi-sheet file.<br>To keep the selected format and save only the current sheet, press Save.<br>To save the current spreadsheet, click Cancel and save it in a different format.", "SSE.Controllers.Main.confirmAddCellWatches": "This action will add {0} cell watches.<br>Do you want to continue?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "This action will add only {0} cell watches by memory save reason.<br>Do you want to continue?", "SSE.Controllers.Main.confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "SSE.Controllers.Main.confirmMoveCellRange": "Диапазонът на дестинационните клетки може да съдържа данни. Продължете операцията?", "SSE.Controllers.Main.confirmPutMergeRange": "Изходните данни съдържаха обединени клетки. <br> Преди да бъдат поставени в таблицата, те бяха развързани.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Formulas in the header row will be removed and converted to static text.<br>Do you want to continue?", "SSE.Controllers.Main.confirmReplaceHFPicture": "Only one picture can be inserted in each section of the header.<br>Press \"Replace\" to replace existing picture.<br>Press \"Keep\" to keep existing picture.", "SSE.Controllers.Main.convertationTimeoutText": "Превишава се времето на изтичане на реализациите.", "SSE.Controllers.Main.criticalErrorExtText": "Натиснете \"OK\", за да се върнете към списъка с документи.", "SSE.Controllers.Main.criticalErrorTitle": "Грешка", "SSE.Controllers.Main.downloadErrorText": "Изтеглянето се провали.", "SSE.Controllers.Main.downloadTextText": "Електронната таблица се изтегли ...", "SSE.Controllers.Main.downloadTitleText": "Изтегляне на електронна таблица", "SSE.Controllers.Main.errNoDuplicates": "No duplicate values found.", "SSE.Controllers.Main.errorAccessDeny": "Опитвате се да извършите действие, за което нямате права. <br> Моля, свържете се с администратора на сървъра за документи.", "SSE.Controllers.Main.errorArgsRange": "Грешка във въведената формула. <br> Използва се неправилен диапазон на аргумента.", "SSE.Controllers.Main.errorAutoFilterChange": "Операцията не е разрешена, тъй като се опитва да измести клетките в таблица на работния ви лист.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Операцията не може да бъде извършена за избраните клетки, тъй като не можете да преместите част от таблицата. <br> Изберете друг диапазон от данни, така че цялата таблица да бъде изместена и опитайте отново.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Операцията не може да бъде извършена за избрания диапазон от клетки. <br> Изберете един и същ диапазон от данни, различен от съществуващия, и опитайте отново.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Операцията не може да се извърши, защото областта съдържа филтрирани клетки. <br> М<PERSON><PERSON><PERSON>, отворете филтрираните елементи и опитайте отново.", "SSE.Controllers.Main.errorBadImageUrl": "URL адресът на изображението е неправилен", "SSE.Controllers.Main.errorCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the spreadsheet.", "SSE.Controllers.Main.errorCannotUngroup": "Cannot ungroup. To start an outline, select the detail rows or columns and group them.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "You cannot use this command on a protected sheet. To use this command, unprotect the sheet.<br>You might be requested to enter a password.", "SSE.Controllers.Main.errorChangeArray": "Не можете да променяте част от масив.", "SSE.Controllers.Main.errorChangeFilteredRange": "This will change a filtered range on your worksheet.<br>To complete this task, please remove AutoFilters.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "The cell or chart you are trying to change is on a protected sheet.<br>To make a change, unprotect the sheet. You might be requested to enter a password.", "SSE.Controllers.Main.errorCircularReference": "There are one or more circular references where a formula refers to its own cell either directly or indirectly.<br>Try removing or changing these references, or moving the formulas to different cells.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Връзката със сървъра е загубена. Документът не може да бъде редактиран в момента.", "SSE.Controllers.Main.errorConnectToServer": "Документът не може да бъде запасен. Моля, проверете настройките за връзка или се свържете с администратора си.<br>Когато щракнете върху бутона 'OK', ще бъдете подканени да изтеглите документа.", "SSE.Controllers.Main.errorConvertXml": "The file has an unsupported format.<br>Only XML Spreadsheet 2003 format can be used.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Тази команда не може да се използва с многократни селекции. <br> Изберете единичен обхват и опитайте отново.", "SSE.Controllers.Main.errorCountArg": "Грешка в въведената формула. <br> Използва се неправилен брой аргументи.", "SSE.Controllers.Main.errorCountArgExceed": "Грешка във въведената формула. <br> Брой аргументи е надвишен.", "SSE.Controllers.Main.errorCreateDefName": "Съществуващите имена на обхвати не могат да бъдат редактирани и новите не могат да бъдат създадени <br> в момента, тъй като някои от тях се редактират.", "SSE.Controllers.Main.errorCreateRange": "The existing ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Controllers.Main.errorDatabaseConnection": "Външна грешка. <br> Грешка при свързване към база данни. Моля, свържете се с екипа за поддръжка, в случай че грешката продължава.", "SSE.Controllers.Main.errorDataEncrypted": "Получени са криптирани промени, които не могат да бъдат дешифрирани.", "SSE.Controllers.Main.errorDataRange": "Неправилен обхват от данни.", "SSE.Controllers.Main.errorDataValidate": "The value you entered is not valid.<br>A user has restricted values that can be entered into this cell.", "SSE.Controllers.Main.errorDefaultMessage": "Код на грешка: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "You are trying to delete a column that contains a locked cell. Locked cells cannot be deleted while the worksheet is protected.<br>To delete a locked cell, unprotect the sheet. You might be requested to enter a password.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "You are trying to delete a row that contains a locked cell. Locked cells cannot be deleted while the worksheet is protected.<br>To delete a locked cell, unprotect the sheet. You might be requested to enter a password.", "SSE.Controllers.Main.errorDependentsNoFormulas": "The Trace Dependents command found no formulas that refer to the active cell.", "SSE.Controllers.Main.errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "SSE.Controllers.Main.errorEditingDownloadas": "Възникна грешка по време на работа с документа. <br> Използвайте опцията 'Download as', за да запишете архивното копие на файла на твърдия диск на компютъра.", "SSE.Controllers.Main.errorEditingSaveas": "Възникна грешка по време на работа с документа. <br> Използвайте опцията „Запазване като ...“, за да запишете архивното копие на файла на твърдия диск на компютъра.", "SSE.Controllers.Main.errorEditView": "The existing sheet view cannot be edited and the new ones cannot be created at the moment as some of them are being edited.", "SSE.Controllers.Main.errorEmailClient": "Не може да се намери имейл клиент.", "SSE.Controllers.Main.errorFilePassProtect": "Файлът е защитен с парола и не може да бъде отворен.", "SSE.Controllers.Main.errorFileRequest": "Външна грешка. <br> Грешка в заявката за файл. Моля, свържете се с екипа за поддръжка, в случай че грешката продължава.", "SSE.Controllers.Main.errorFileSizeExceed": "The file size exceeds the limitation set for your server.<br>Please contact your Document Server administrator for details.", "SSE.Controllers.Main.errorFileVKey": "Външна грешка. <br> Неправилен ключ за защита. Моля, свържете се с екипа за поддръжка, в случай че грешката продължава.", "SSE.Controllers.Main.errorFillRange": "Избраният диапазон от клетки не може да се запълни. <br> Всички обединени клетки трябва да са с еднакъв размер.", "SSE.Controllers.Main.errorForceSave": "При запазването на файла възникна грешка. Моля, използвайте опцията \"Изтегляне като\", за да запишете файла на твърдия диск на компютъра или опитайте отново по-късно.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Cannot enter a formula for an item or field name in a pivot table report.", "SSE.Controllers.Main.errorFormulaName": "Грешка във въведената формула. <br> Използва се неправилно име на формула.", "SSE.Controllers.Main.errorFormulaParsing": "Вътрешна грешка при анализиране на формулата.", "SSE.Controllers.Main.errorFrmlMaxLength": "The length of your formula exceeds the limit of 8192 characters.<br>Please edit it and try again.", "SSE.Controllers.Main.errorFrmlMaxReference": "You cannot enter this formula because it has too many values,<br>cell references, and/or names.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Текстовите стойности във формули са ограничени до 255 знака.<br>Използвайте функцията CONCATENATE или оператора за свързване (&amp;).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Функцията се отнася за лист, който не съществува. <br> Моля, проверете данните и опитайте отново.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Operation could not be completed for the selected cell range.<br>Select a range so that the first table row was on the same row<br>and the resulting table overlapped the current one.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Operation could not be completed for the selected cell range.<br>Select a range which does not include other tables.", "SSE.Controllers.Main.errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "SSE.Controllers.Main.errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInvalidRef": "Въведете правилно име за избора или валидна референция, към която да отидете.", "SSE.Controllers.Main.errorKeyEncrypt": "Дескриптор на неизвестен ключ", "SSE.Controllers.Main.errorKeyExpire": "Дескрипторът на ключовете е изтекъл", "SSE.Controllers.Main.errorLabledColumnsPivot": "To create a pivot table, use data that is organized as a list with labeled columns.", "SSE.Controllers.Main.errorLoadingFont": "Fonts are not loaded.<br>Please contact your Document Server administrator.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "The reference for the location or data range is not valid.", "SSE.Controllers.Main.errorLockedAll": "Операцията не може да се извърши, тъй като листа е заключен от друг потребител.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "One of the cells involved in the goal seek process has been modified by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "Не можете да променяте данни в обобщена таблица.", "SSE.Controllers.Main.errorLockedWorksheetRename": "Листът не може да бъде преименуван в момента, тъй като се преименува от друг потребител", "SSE.Controllers.Main.errorMaxPoints": "Максималният брой точки в серия на графиката е 4096.", "SSE.Controllers.Main.errorMoveRange": "Не може да се промени част от обединена клетка", "SSE.Controllers.Main.errorMoveSlicerError": "Table slicers cannot be copied from one workbook to another.<br>Try again by selecting the entire table and the slicers.", "SSE.Controllers.Main.errorMultiCellFormula": "Формулирайте с множество клетки, които не са позволени в таблицата.", "SSE.Controllers.Main.errorNoDataToParse": "Не са избрани данни за анализиране.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.Main.errorOpenWarning": "Дължината на една от формулите във файла надвишава разрешения брой знаци и е премахната.", "SSE.Controllers.Main.errorOperandExpected": "Въведеният синтаксис на функцията не е правилен. Моля, проверете дали липсва една от скобите - '(' или ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "The password you supplied is not correct.<br>Verify that the CAPS LOCK key is off and be sure to use the correct capitalization.", "SSE.Controllers.Main.errorPasteInPivot": "We can't make this change for the selected cells because it will affect a pivot table.<br>Use the field list to change the report.", "SSE.Controllers.Main.errorPasteMaxRange": "Областта за копиране и поставяне не съвпада. <br> <PERSON><PERSON><PERSON><PERSON>, изберете област със същия размер или кликнете върху първата клетка в ред, за да поставите копираните клетки.", "SSE.Controllers.Main.errorPasteMultiSelect": "This action cannot be done on a multiple range selection.<br>Select a single range and try again.", "SSE.Controllers.Main.errorPasteSlicerError": "Table slicers cannot be copied from one workbook to another.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Pivot table field name already exists.", "SSE.Controllers.Main.errorPivotGroup": "<PERSON><PERSON> group that selection.", "SSE.Controllers.Main.errorPivotOverlap": "A pivot table report cannot overlap a table.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "The Pivot Table report was saved without the underlying data.<br>Use the 'Refresh' button to update the report.", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "The Trace Precedents command requires that the active cell contain a formula which includes a valid references.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "За съжаление, не е възможно да се отпечатат повече от 1500 страници едновременно в текущата версия на програмата. <br> Това ограничение ще бъде премахнато в предстоящите издания.", "SSE.Controllers.Main.errorProcessSaveResult": "Запазване не бе успешно", "SSE.Controllers.Main.errorProtectedRange": "This range is not allowed for editing.", "SSE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "SSE.Controllers.Main.errorServerVersion": "Версията на редактора е актуализирана. Страницата ще бъде презаредена, за да приложи промените.", "SSE.Controllers.Main.errorSessionAbsolute": "Сесията за редактиране на документ изтече. Моля, презаредете страницата.", "SSE.Controllers.Main.errorSessionIdle": "Документът не е редактиран дълго време. Моля, презаредете страницата.", "SSE.Controllers.Main.errorSessionToken": "Връзката със сървъра е прекъсната. Моля, презаредете страницата.", "SSE.Controllers.Main.errorSetPassword": "Password could not be set.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Location reference is not valid because the cells are not all in the same column or row.<br>Select cells that are all in a single column or row.", "SSE.Controllers.Main.errorStockChart": "Неправилен ред на ред. За изграждане на борсова карта поставете данните на листа в следния ред: <br> цена на отваряне, максимална цена, мин. цена, цена на затваряне.", "SSE.Controllers.Main.errorToken": "Токенът за защита на документа не е правилно оформен. <br> Моля, свържете се с вашия администратор на сървър за документи.", "SSE.Controllers.Main.errorTokenExpire": "Токенът за защита на документа е изтекъл. <br> <PERSON><PERSON><PERSON><PERSON>, свържете се с администратора на документа.", "SSE.Controllers.Main.errorUnexpectedGuid": "Външна грешка. <br> Неочакван GUID. Моля, свържете се с екипа за поддръжка, в случай че грешката продължава.", "SSE.Controllers.Main.errorUpdateVersion": "Версията на файла е променена. Страницата ще бъде презаредена.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.", "SSE.Controllers.Main.errorUserDrop": "Файлът не може да бъде достъпен в момента.", "SSE.Controllers.Main.errorUsersExceed": "Превишен е броят на потребителите, позволени от ценовия план", "SSE.Controllers.Main.errorViewerDisconnect": "Връзката е загубена. Все още можете да преглеждате документа,<br>но няма да можете да го изтеглите или отпечатате, докато връзката бъде възстановена.", "SSE.Controllers.Main.errorWrongBracketsCount": "Грешка във въведената формула. <br> Използва се грешен брой скоби.", "SSE.Controllers.Main.errorWrongOperator": "Грешка в въведената формула. Използва се грешен оператор. <br> <PERSON><PERSON><PERSON><PERSON>, коригирайте грешката.", "SSE.Controllers.Main.errorWrongPassword": "The password you supplied is not correct.", "SSE.Controllers.Main.errRemDuplicates": "Duplicate values found and deleted: {0}, unique values left: {1}.", "SSE.Controllers.Main.leavePageText": "Имате незапазени промени в тази електронна таблица. Кликнете върху „Остани на тази страница“ и след това върху „Запазване“, за да ги запазите. Кликнете върху „Оставете тази страница“, за да отхвърлите всички незапазени промени.", "SSE.Controllers.Main.leavePageTextOnClose": "All unsaved changes in this spreadsheet will be lost.<br> Click \"Cancel\" then \"Save\" to save them. Click \"OK\" to discard all the unsaved changes.", "SSE.Controllers.Main.loadFontsTextText": "Данните се зареждат ...", "SSE.Controllers.Main.loadFontsTitleText": "Зареждане на данни", "SSE.Controllers.Main.loadFontTextText": "Данните се заредете ...", "SSE.Controllers.Main.loadFontTitleText": "Зареждане на данни", "SSE.Controllers.Main.loadImagesTextText": "Изображения се зареждат ...", "SSE.Controllers.Main.loadImagesTitleText": "Зареждане на изображения", "SSE.Controllers.Main.loadImageTextText": "Изображението се се зарежда ...", "SSE.Controllers.Main.loadImageTitleText": "Зареждане на изображението", "SSE.Controllers.Main.loadingDocumentTitleText": "Електронната таблица се зарежда", "SSE.Controllers.Main.notcriticalErrorTitle": "Внимание", "SSE.Controllers.Main.openErrorText": "Възникна грешка при отварянето на файла", "SSE.Controllers.Main.openTextText": "Отваря се електронната таблица ...", "SSE.Controllers.Main.openTitleText": "Отваряне на електронна таблица", "SSE.Controllers.Main.pastInMergeAreaError": "Не може да се промени част от обединена клетка", "SSE.Controllers.Main.printTextText": "Отпечатване на електронна таблица ...", "SSE.Controllers.Main.printTitleText": "Отпечатване на електронна таблица", "SSE.Controllers.Main.reloadButtonText": "Презареждане на страницата", "SSE.Controllers.Main.requestEditFailedMessageText": "Някой редактира този документ в момента. Моля, опитайте отново по-късно.", "SSE.Controllers.Main.requestEditFailedTitleText": "Отказан достъп", "SSE.Controllers.Main.saveErrorText": "Възникна грешка при запазването на файла", "SSE.Controllers.Main.saveErrorTextDesktop": "This file cannot be saved or created.<br>Possible reasons are: <br>1. The file is read-only. <br>2. The file is being edited by other users. <br>3. The disk is full or corrupted.", "SSE.Controllers.Main.saveTextText": "Запазва се електронната таблица ...", "SSE.Controllers.Main.saveTitleText": "Запазване на електронна таблица", "SSE.Controllers.Main.scriptLoadError": "Връзката е твърде бавна, някои от компонентите не могат да бъдат заредени. Моля, презаредете страницата.", "SSE.Controllers.Main.textAnonymous": "Анонимен", "SSE.Controllers.Main.textApplyAll": "Apply to all equations", "SSE.Controllers.Main.textBuyNow": "Посетете уебсайта", "SSE.Controllers.Main.textChangesSaved": "Всички промени са запазени", "SSE.Controllers.Main.textClose": "Затвори", "SSE.Controllers.Main.textCloseTip": "Кликнете, за да затворите върха", "SSE.Controllers.Main.textConfirm": "Потвърждаване", "SSE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "SSE.Controllers.Main.textContactUs": "Свържете се с продажбите", "SSE.Controllers.Main.textContinue": "Continue", "SSE.Controllers.Main.textConvertEquation": "This equation was created with an old version of the equation editor which is no longer supported. To edit it, convert the equation to the Office Math ML format.<br>Convert now?", "SSE.Controllers.Main.textCustomLoader": "Моля, имайте предвид, че според условията на лиценза нямате право да сменяте товарача. <br> Моля, свържете се с нашия отдел Продажби, за да получите оферта.", "SSE.Controllers.Main.textDisconnect": "Connection is lost", "SSE.Controllers.Main.textFillOtherRows": "Fill other rows", "SSE.Controllers.Main.textFormulaFilledAllRows": "Formula filled {0} rows have data. Filling other empty rows may take a few minutes.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Formula filled first {0} rows. Filling other empty rows may take a few minutes.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Formula filled only first {0} rows have data by memory save reason. There are other {1} rows have data in this sheet. You can fill them manually.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Formula filled only first {0} rows by memory save reason. Other rows in this sheet don't have data.", "SSE.Controllers.Main.textGuest": "Гост", "SSE.Controllers.Main.textHasMacros": "The file contains automatic macros.<br>Do you want to run macros?", "SSE.Controllers.Main.textKeep": "Keep", "SSE.Controllers.Main.textLearnMore": "Learn more", "SSE.Controllers.Main.textLoadingDocument": "Електронната таблица се зарежда", "SSE.Controllers.Main.textLongName": "Enter a name that is less than 128 characters.", "SSE.Controllers.Main.textNeedSynchronize": "You have updates", "SSE.Controllers.Main.textNo": "Не", "SSE.Controllers.Main.textNoLicenseTitle": "Ограничение за връзка ONLYOFFICE", "SSE.Controllers.Main.textPaidFeature": "Пла<PERSON><PERSON>на функция", "SSE.Controllers.Main.textPleaseWait": "Операцията може да отнеме повече време от очакваното. Моля изчакай...", "SSE.Controllers.Main.textReconnect": "Connection is restored", "SSE.Controllers.Main.textRemember": "Remember my choice for all files", "SSE.Controllers.Main.textRememberMacros": "Remember my choice for all macros", "SSE.Controllers.Main.textRenameError": "User name must not be empty.", "SSE.Controllers.Main.textRenameLabel": "Enter a name to be used for collaboration", "SSE.Controllers.Main.textReplace": "Replace", "SSE.Controllers.Main.textRequestMacros": "A macro makes a request to URL. Do you want to allow the request to the %1?", "SSE.Controllers.Main.textShape": "Форма", "SSE.Controllers.Main.textStrict": "Строг режим", "SSE.Controllers.Main.textText": "Текст", "SSE.Controllers.Main.textTryQuickPrint": "You have selected Quick print: the entire document will be printed on the last selected or default printer.<br>Do you want to continue?", "SSE.Controllers.Main.textTryUndoRedo": "Функциите за отмяна/възстановяване са деактивирани за режима Бързо съвместно редактиране. <br> Кликнете върху бутона „Строг режим“, за да превключите в режим на стриктно съвместно редактиране, за да редактирате файла без намеса на други потребители и да изпращате промените само след като ги запазите тях. Можете да превключвате между режимите за съвместно редактиране с помощта на редактора Разширени настройки.", "SSE.Controllers.Main.textTryUndoRedoWarn": "The Undo/Redo functions are disabled for the Fast co-editing mode.", "SSE.Controllers.Main.textUndo": "Undo", "SSE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "SSE.Controllers.Main.textUpdating": "Updating", "SSE.Controllers.Main.textYes": "Да", "SSE.Controllers.Main.titleLicenseExp": "Лицензът е изтекъл", "SSE.Controllers.Main.titleLicenseNotActive": "License not active", "SSE.Controllers.Main.titleServerVersion": "Редакторът е актуализиран", "SSE.Controllers.Main.titleUpdateVersion": "Version changed", "SSE.Controllers.Main.txtAccent": "Акцент", "SSE.Controllers.Main.txtAll": "(All)", "SSE.Controllers.Main.txtArt": "Вашият текст тук", "SSE.Controllers.Main.txtBasicShapes": "Основни форми", "SSE.Controllers.Main.txtBlank": "(blank)", "SSE.Controllers.Main.txtButtons": "Бутони", "SSE.Controllers.Main.txtByField": "%1 of %2", "SSE.Controllers.Main.txtCallouts": "Допълнителните описания", "SSE.Controllers.Main.txtCharts": "Диаграми", "SSE.Controllers.Main.txtClearFilter": "Clear filter", "SSE.Controllers.Main.txtColLbls": "Column labels", "SSE.Controllers.Main.txtColumn": "Column", "SSE.Controllers.Main.txtConfidential": "Confidential", "SSE.Controllers.Main.txtDate": "Date", "SSE.Controllers.Main.txtDays": "Days", "SSE.Controllers.Main.txtDiagramTitle": "Заглавие на диаграмата", "SSE.Controllers.Main.txtEditingMode": "Задаване на режим на редактиране ...", "SSE.Controllers.Main.txtErrorLoadHistory": "History loading failed", "SSE.Controllers.Main.txtFiguredArrows": "Фигурни стрели", "SSE.Controllers.Main.txtFile": "File", "SSE.Controllers.Main.txtGrandTotal": "Grand Total", "SSE.Controllers.Main.txtGroup": "Гру<PERSON>а", "SSE.Controllers.Main.txtHours": "Hours", "SSE.Controllers.Main.txtInfo": "Info", "SSE.Controllers.Main.txtLines": "Линии", "SSE.Controllers.Main.txtMath": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON>и<PERSON>", "SSE.Controllers.Main.txtMinutes": "Minutes", "SSE.Controllers.Main.txtMonths": "Months", "SSE.Controllers.Main.txtMultiSelect": "Multi-Select", "SSE.Controllers.Main.txtNone": "None", "SSE.Controllers.Main.txtOr": "%1 or %2", "SSE.Controllers.Main.txtPage": "Page", "SSE.Controllers.Main.txtPageOf": "Page %1 of %2", "SSE.Controllers.Main.txtPages": "Pages", "SSE.Controllers.Main.txtPicture": "Picture", "SSE.Controllers.Main.txtPivotTable": "PivotTable", "SSE.Controllers.Main.txtPreparedBy": "Prepared by", "SSE.Controllers.Main.txtPrintArea": "Печат_зона", "SSE.Controllers.Main.txtQuarter": "Qtr", "SSE.Controllers.Main.txtQuarters": "Quarters", "SSE.Controllers.Main.txtRectangles": "Правоъгълници", "SSE.Controllers.Main.txtRow": "Row", "SSE.Controllers.Main.txtRowLbls": "Row Labels", "SSE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "SSE.Controllers.Main.txtScheme_Aspect": "Aspect", "SSE.Controllers.Main.txtScheme_Blue": "Blue", "SSE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "SSE.Controllers.Main.txtScheme_Blue_II": "Blue II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "SSE.Controllers.Main.txtScheme_Grayscale": "Нюанси на сивото", "SSE.Controllers.Main.txtScheme_Green": "Green", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "SSE.Controllers.Main.txtScheme_Marquee": "Marquee", "SSE.Controllers.Main.txtScheme_Median": "Median", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Orange", "SSE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "SSE.Controllers.Main.txtScheme_Paper": "Paper", "SSE.Controllers.Main.txtScheme_Red": "Red", "SSE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "SSE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "SSE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "SSE.Controllers.Main.txtScheme_Violet": "Violet", "SSE.Controllers.Main.txtScheme_Violet_II": "Violet II", "SSE.Controllers.Main.txtScheme_Yellow": "Yellow", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "SSE.Controllers.Main.txtSeconds": "Seconds", "SSE.Controllers.Main.txtSeries": "Серия", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Извикваща линия 1 (граница и акцент)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Извивка на линия 2 (граница и акцент)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Извикваща линия 3 (граница и акцент)", "SSE.Controllers.Main.txtShape_accentCallout1": "Позиция от ред 1 (акцентна лента)", "SSE.Controllers.Main.txtShape_accentCallout2": "Извикваща линия 2 (акцент)", "SSE.Controllers.Main.txtShape_accentCallout3": "Извикваща линия 3 (акцентна лента)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "<PERSON>у<PERSON><PERSON><PERSON> \"Назад\" или \"Предишен\"", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Нача<PERSON>ен бутон", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Празен бутон", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Бутон за документи", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Бутон за край", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Напред или Следващ бутон", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Бутон за помощ", "SSE.Controllers.Main.txtShape_actionButtonHome": "Нача<PERSON>ен бутон", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Бутон за информация", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Бутон за филми", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Бутон за връщане", "SSE.Controllers.Main.txtShape_actionButtonSound": "Бутон за звука", "SSE.Controllers.Main.txtShape_arc": "Дъга", "SSE.Controllers.Main.txtShape_bentArrow": "Сгъната стрелка", "SSE.Controllers.Main.txtShape_bentConnector5": "Коляновият конектор", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Съединител за стрелки с лакът", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Конектор с двойна стрелка", "SSE.Controllers.Main.txtShape_bentUpArrow": "Сгъната стрелка нагоре", "SSE.Controllers.Main.txtShape_bevel": "Откос", "SSE.Controllers.Main.txtShape_blockArc": "Блок arc", "SSE.Controllers.Main.txtShape_borderCallout1": "Извикваща линия 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Извикваща линия 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Линия за линия 3", "SSE.Controllers.Main.txtShape_bracePair": "Двойна скоба", "SSE.Controllers.Main.txtShape_callout1": "Очертание от ред 1 (без граница)", "SSE.Controllers.Main.txtShape_callout2": "Очертание от ред 2 (без граница)", "SSE.Controllers.Main.txtShape_callout3": "Обложка за линия 3 (без граница)", "SSE.Controllers.Main.txtShape_can": "Мога", "SSE.Controllers.Main.txtShape_chevron": "Орнамент във формата на", "SSE.Controllers.Main.txtShape_chord": "Акорд", "SSE.Controllers.Main.txtShape_circularArrow": "Кръгла стрелка", "SSE.Controllers.Main.txtShape_cloud": "Облак", "SSE.Controllers.Main.txtShape_cloudCallout": "Облаковидно изнесено означение", "SSE.Controllers.Main.txtShape_corner": "Ъглов", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Извитият конектор", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Съединител с крива стрелка", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Съединител с двойна стрелка", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Извита надолу стрелка", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Лява стрелка", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Извита дясна стрелка", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Извита стрелка нагоре", "SSE.Controllers.Main.txtShape_decagon": "Десетоъгълник", "SSE.Controllers.Main.txtShape_diagStripe": "Д<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>на лента", "SSE.Controllers.Main.txtShape_diamond": "<PERSON>и<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_dodecagon": "Дванадесетоъгълник", "SSE.Controllers.Main.txtShape_donut": "Поничка", "SSE.Controllers.Main.txtShape_doubleWave": "Дво<PERSON>на вълна", "SSE.Controllers.Main.txtShape_downArrow": "Стрелка надолу", "SSE.Controllers.Main.txtShape_downArrowCallout": "Стрелка надолу", "SSE.Controllers.Main.txtShape_ellipse": "Елипса", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Лентата е извита надолу", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Лентата е извита нагоре", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Блок-схема: Алтернативен процес", "SSE.Controllers.Main.txtShape_flowChartCollate": "Блок-схема: Сортиране", "SSE.Controllers.Main.txtShape_flowChartConnector": "Блок-схема: Съединител", "SSE.Controllers.Main.txtShape_flowChartDecision": "Блок-схема: Решение", "SSE.Controllers.Main.txtShape_flowChartDelay": "Блок-схема: Закъснение", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Блок-схема: Показване", "SSE.Controllers.Main.txtShape_flowChartDocument": "Блок-схема: До<PERSON>у<PERSON><PERSON><PERSON>т", "SSE.Controllers.Main.txtShape_flowChartExtract": "Блок-схема: Извличане", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Блок-схема: Да<PERSON>ни", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Блок-схема: Вътре<PERSON>на памет", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Блок-схема: Магнитен диск", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Блок-схема: Съхранение с директен достъп", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Блок-схема: Съхранение с последователен достъп", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Блок-схема: Ръчен вход", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Блок-схема: Ръчна операция", "SSE.Controllers.Main.txtShape_flowChartMerge": "Блок-схема: Обединяване", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Блок-схема: Мул<PERSON>и<PERSON><PERSON><PERSON>умент ", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Блок-схема: Съединител извън страницата", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Блок-схема: Съхранени данни", "SSE.Controllers.Main.txtShape_flowChartOr": "Блок-схема: Или", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Блок-схема: Предварително дефиниран процес", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Блок-схема: Подготовка", "SSE.Controllers.Main.txtShape_flowChartProcess": "Блок-схема: Процес", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Блок-схема: Карта", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Блок-схема: Перфорир<PERSON>на лента", "SSE.Controllers.Main.txtShape_flowChartSort": "Блок-схема: Сортиране", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Блок-схема: Сумираща свързване", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Блок-схема: Знак за край", "SSE.Controllers.Main.txtShape_foldedCorner": "Сгънат ъгъл", "SSE.Controllers.Main.txtShape_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_halfFrame": "<PERSON>ол<PERSON><PERSON><PERSON> кадър", "SSE.Controllers.Main.txtShape_heart": "Сърце", "SSE.Controllers.Main.txtShape_heptagon": "Седмоъгълник", "SSE.Controllers.Main.txtShape_hexagon": "Шестоъгълник", "SSE.Controllers.Main.txtShape_homePlate": "Пентагона", "SSE.Controllers.Main.txtShape_horizontalScroll": "Хоризонтален превъртане", "SSE.Controllers.Main.txtShape_irregularSeal1": "Експлозия 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Експлозия 2", "SSE.Controllers.Main.txtShape_leftArrow": "Лява стрелка", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Стрелка наляво", "SSE.Controllers.Main.txtShape_leftBrace": "Ляв скоба", "SSE.Controllers.Main.txtShape_leftBracket": "Лява скоба", "SSE.Controllers.Main.txtShape_leftRightArrow": "Лява стрелка надясно", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Лява дясна стрелка", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Стрелка наляво нагоре", "SSE.Controllers.Main.txtShape_leftUpArrow": "Стрелка наляво нагоре", "SSE.Controllers.Main.txtShape_lightningBolt": "Светкавица", "SSE.Controllers.Main.txtShape_line": "Линия", "SSE.Controllers.Main.txtShape_lineWithArrow": "Стрелка", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Дв<PERSON><PERSON>на стрелка", "SSE.Controllers.Main.txtShape_mathDivide": "Делене", "SSE.Controllers.Main.txtShape_mathEqual": "<PERSON>ав<PERSON>н", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Умножение", "SSE.Controllers.Main.txtShape_mathNotEqual": "Не е равно", "SSE.Controllers.Main.txtShape_mathPlus": "Плюс", "SSE.Controllers.Main.txtShape_moon": "Луна", "SSE.Controllers.Main.txtShape_noSmoking": "Символ \"Не\"", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Стрелка надясно", "SSE.Controllers.Main.txtShape_octagon": "Осмоъгълник", "SSE.Controllers.Main.txtShape_parallelogram": "Успоредник", "SSE.Controllers.Main.txtShape_pentagon": "Пентагона", "SSE.Controllers.Main.txtShape_pie": "Кръгова", "SSE.Controllers.Main.txtShape_plaque": "Знак", "SSE.Controllers.Main.txtShape_plus": "Плюс", "SSE.Controllers.Main.txtShape_polyline1": "Драсканица", "SSE.Controllers.Main.txtShape_polyline2": "Свободна форма", "SSE.Controllers.Main.txtShape_quadArrow": "Четириядрена стрелка", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Квар<PERSON><PERSON><PERSON>на стрелка", "SSE.Controllers.Main.txtShape_rect": "Правоъгълник", "SSE.Controllers.Main.txtShape_ribbon": "Долна лента", "SSE.Controllers.Main.txtShape_ribbon2": "Лентата нагоре", "SSE.Controllers.Main.txtShape_rightArrow": "<PERSON><PERSON><PERSON>на стрелка", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Стрелка надясно", "SSE.Controllers.Main.txtShape_rightBrace": "Надясно", "SSE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON><PERSON>на скоба", "SSE.Controllers.Main.txtShape_round1Rect": "Кръгъл правоъгълник с един ъгъл", "SSE.Controllers.Main.txtShape_round2DiagRect": "Кръгъл правоъгълник с диагонален ъгъл", "SSE.Controllers.Main.txtShape_round2SameRect": "Кръгла правоъгълник с една и съща страна", "SSE.Controllers.Main.txtShape_roundRect": "Кръгъл ъгъл правоъгълник", "SSE.Controllers.Main.txtShape_rtTriangle": "Прав триъгълник", "SSE.Controllers.Main.txtShape_smileyFace": "Усмивка на лицето", "SSE.Controllers.Main.txtShape_snip1Rect": "Правоъгълник с единичен ъгъл", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Правоъгълник с диагонален ъгъл", "SSE.Controllers.Main.txtShape_snip2SameRect": "Отрязан правоъгълник от същия страничен ъгъл", "SSE.Controllers.Main.txtShape_snipRoundRect": "Правоъгълник с изрязани и кръгли ъгли", "SSE.Controllers.Main.txtShape_spline": "Крива", "SSE.Controllers.Main.txtShape_star10": "10-точкова звезда", "SSE.Controllers.Main.txtShape_star12": "12-точкова звезда", "SSE.Controllers.Main.txtShape_star16": "16-точкова звезда", "SSE.Controllers.Main.txtShape_star24": "24-точкова звезда", "SSE.Controllers.Main.txtShape_star32": "Звезда с 32 точки", "SSE.Controllers.Main.txtShape_star4": "4-точкова звезда", "SSE.Controllers.Main.txtShape_star5": "5-точкова звезда", "SSE.Controllers.Main.txtShape_star6": "6-точкова звезда", "SSE.Controllers.Main.txtShape_star7": "7-точкова звезда", "SSE.Controllers.Main.txtShape_star8": "8-точкова звезда", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Стрелка надясно", "SSE.Controllers.Main.txtShape_sun": "Слънце", "SSE.Controllers.Main.txtShape_teardrop": "Капко образно", "SSE.Controllers.Main.txtShape_textRect": "Текстово поле", "SSE.Controllers.Main.txtShape_trapezoid": "Тра<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_triangle": "Триъгълник", "SSE.Controllers.Main.txtShape_upArrow": "Стрелка нагоре", "SSE.Controllers.Main.txtShape_upArrowCallout": "Стрелка нагоре", "SSE.Controllers.Main.txtShape_upDownArrow": "Стрелка нагоре надолу", "SSE.Controllers.Main.txtShape_uturnArrow": "Стрелка за завъртане", "SSE.Controllers.Main.txtShape_verticalScroll": "Вертикален скрол", "SSE.Controllers.Main.txtShape_wave": "Вълна", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Овална извивка", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Правоъгълна извивка", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Закръглена правоъгълна извивка", "SSE.Controllers.Main.txtSheet": "Sheet", "SSE.Controllers.Main.txtSlicer": "<PERSON>licer", "SSE.Controllers.Main.txtStarsRibbons": "Звезди и панделки", "SSE.Controllers.Main.txtStyle_Bad": "Лошо", "SSE.Controllers.Main.txtStyle_Calculation": "Изчисление", "SSE.Controllers.Main.txtStyle_Check_Cell": "Клетка за проверка", "SSE.Controllers.Main.txtStyle_Comma": "Запетая", "SSE.Controllers.Main.txtStyle_Currency": "Валута", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Обяснителен текст", "SSE.Controllers.Main.txtStyle_Good": "Добре", "SSE.Controllers.Main.txtStyle_Heading_1": "Заглавие 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Функция 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Функция 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Функция 4", "SSE.Controllers.Main.txtStyle_Input": "Вход", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Свързана клетка", "SSE.Controllers.Main.txtStyle_Neutral": "Неутрален", "SSE.Controllers.Main.txtStyle_Normal": "Нормален", "SSE.Controllers.Main.txtStyle_Note": "Забележка", "SSE.Controllers.Main.txtStyle_Output": "Продукция", "SSE.Controllers.Main.txtStyle_Percent": "На сто", "SSE.Controllers.Main.txtStyle_Title": "Заглавие", "SSE.Controllers.Main.txtStyle_Total": "Обща сума", "SSE.Controllers.Main.txtStyle_Warning_Text": "Предупреденилен текст", "SSE.Controllers.Main.txtTab": "Tab", "SSE.Controllers.Main.txtTable": "Таблица", "SSE.Controllers.Main.txtTime": "Time", "SSE.Controllers.Main.txtUnlock": "Unlock", "SSE.Controllers.Main.txtUnlockRange": "Unlock range", "SSE.Controllers.Main.txtUnlockRangeDescription": "Enter the password to change this range:", "SSE.Controllers.Main.txtUnlockRangeWarning": "A range you are trying to change is password protected.", "SSE.Controllers.Main.txtValues": "Values", "SSE.Controllers.Main.txtView": "View", "SSE.Controllers.Main.txtXAxis": "X ос", "SSE.Controllers.Main.txtYAxis": "Y ос", "SSE.Controllers.Main.txtYears": "Years", "SSE.Controllers.Main.unknownErrorText": "Неизвестна грешка.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Вашият браузър не се поддържа.", "SSE.Controllers.Main.uploadDocExtMessage": "Unknown document format.", "SSE.Controllers.Main.uploadDocFileCountMessage": "No documents uploaded.", "SSE.Controllers.Main.uploadDocSizeMessage": "Maximum document size limit exceeded.", "SSE.Controllers.Main.uploadImageExtMessage": "Неизвестен формат на изображението.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Няма качени изображения.", "SSE.Controllers.Main.uploadImageSizeMessage": "Превишено е ограничението за максимален размер на изображението.", "SSE.Controllers.Main.uploadImageTextText": "Качва се изображението ...", "SSE.Controllers.Main.uploadImageTitleText": "Качване на изображение", "SSE.Controllers.Main.waitText": "Моля, изчакайте...", "SSE.Controllers.Main.warnBrowserIE9": "Приложението има ниски възможности за IE9. Използвайте IE10 или по-висока", "SSE.Controllers.Main.warnBrowserZoom": "Текущата настройка за мащабиране на браузъра ви не се поддържа напълно. Моля, нулирайте стойността по подразбиране, като натиснете Ctrl + 0.", "SSE.Controllers.Main.warnLicenseAnonymous": "Access denied for anonymous users.<br>This document will be opened for viewing only.", "SSE.Controllers.Main.warnLicenseBefore": "License not active.<br>Please contact your administrator.", "SSE.Controllers.Main.warnLicenseExceeded": "Броят на едновременните връзки към сървъра за документи е превишен и документът ще бъде отворен само за преглед. <br> За повече информация се обърнете към администратора.", "SSE.Controllers.Main.warnLicenseExp": "Вашият лиценз е изтекъл. <br> <PERSON><PERSON><PERSON><PERSON>, актуализирайте лиценза си и опреснете страницата.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "License expired.<br>You have no access to document editing functionality.<br>Please contact your administrator.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "License needs to be renewed.<br>You have a limited access to document editing functionality.<br>Please contact your administrator to get full access", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Броят на едновременните потребители е надхвърлен и документът ще бъде отворен само за преглед. <br> За повече информация се свържете с администратора си.", "SSE.Controllers.Main.warnNoLicense": "Тази версия на редакторите на %1 има някои ограничения за едновременни връзки към сървъра за документи. <br> Ако имате нужда от повече, моля обмислете закупуването на търговски лиценз.", "SSE.Controllers.Main.warnNoLicenseUsers": "Тази версия на редакторите на %1 има някои ограничения за едновременни потребители. <br> Ако имате нужда от повече, моля обмислете закупуването на търговски лиценз.", "SSE.Controllers.Main.warnProcessRightsChange": "На вас е отказано правото да редактирате файла.", "SSE.Controllers.PivotTable.strSheet": "Sheet", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "No actions with calculated items are allowed for this active cell.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Calculated items do not work with custom subtotals.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "An item name cannot be found. Check that you've typed name correctly and the item is present in the PivotTable report.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Averages, standard deviations, and variances are not supported when a PivotTable report has calculated items.", "SSE.Controllers.Print.strAllSheets": "Всички листове", "SSE.Controllers.Print.textFirstCol": "First column", "SSE.Controllers.Print.textFirstRow": "First row", "SSE.Controllers.Print.textFrozenCols": "Frozen columns", "SSE.Controllers.Print.textFrozenRows": "Frozen rows", "SSE.Controllers.Print.textInvalidRange": "ERROR! Invalid cells range", "SSE.Controllers.Print.textNoRepeat": "Don't repeat", "SSE.Controllers.Print.textRepeat": "Repeat...", "SSE.Controllers.Print.textSelectRange": "Select range", "SSE.Controllers.Print.txtCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Controllers.Print.txtZoomToPage": "Zoom to page", "SSE.Controllers.Search.textInvalidRange": "ERROR! Invalid cells range", "SSE.Controllers.Search.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "SSE.Controllers.Search.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "SSE.Controllers.Search.textReplaceSuccess": "Search has been done. {0} occurrences have been replaced", "SSE.Controllers.Statusbar.errorLastSheet": "Работната книга трябва да има поне един видим работен лист.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Не можете да изтриете работния лист.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>Connection is lost</b><br>Trying to connect. Please check connection settings.", "SSE.Controllers.Statusbar.textSheetViewTip": "You are in Sheet View mode. Filters and sorting are visible only to you and those who are still in this view.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "You are in Sheet View mode. Filters are visible only to you and those who are still in this view.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Работният лист може да съдържа данни. Наистина ли искате да продължите?", "SSE.Controllers.Statusbar.zoomText": "Мащаб {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Шрифтът, който ще запазите, не е наличен в текущото устройство. <br> Стилът на текста ще се покаже с помощта на един от системните шрифтове, запазеният шрифт ще се използва, когато е налице. <br> Искате ли да продължите ?", "SSE.Controllers.Toolbar.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Controllers.Toolbar.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "ГРЕШКА! Максималният брой поредици данни за диаграма е 255", "SSE.Controllers.Toolbar.errorStockChart": "Неправилен ред на ред. За изграждане на борсова карта поставете данните на листа в следния ред: <br> цена на отваряне, максимална цена, мин. цена, цена на затваряне.", "SSE.Controllers.Toolbar.helpCalcItems": "Work with calculated items in Pivot Tables.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Calculated items", "SSE.Controllers.Toolbar.helpFastUndo": "Easily undo changes while collaborating on sheets in Fast mode.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "\"Undo\" in real-time co-editing", "SSE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "SSE.Controllers.Toolbar.textAccent": "Акценти", "SSE.Controllers.Toolbar.textBracket": "Скоби", "SSE.Controllers.Toolbar.textDirectional": "Directional", "SSE.Controllers.Toolbar.textFontSizeErr": "Въведената стойност е неправилна. <br> Въведете числова стойност между 1 и 409", "SSE.Controllers.Toolbar.textFraction": "Фракции", "SSE.Controllers.Toolbar.textFunction": "Функция", "SSE.Controllers.Toolbar.textIndicator": "Indicators", "SSE.Controllers.Toolbar.textInsert": "Вмъкни", "SSE.Controllers.Toolbar.textIntegral": "Интеграли", "SSE.Controllers.Toolbar.textLargeOperator": "Големи оператори", "SSE.Controllers.Toolbar.textLimitAndLog": "Граници и логаритми", "SSE.Controllers.Toolbar.textLongOperation": "Дълга операция", "SSE.Controllers.Toolbar.textMatrix": "Матрици", "SSE.Controllers.Toolbar.textOperator": "Операторите", "SSE.Controllers.Toolbar.textPivot": "Обобщена таблица", "SSE.Controllers.Toolbar.textRadical": "Радикалите", "SSE.Controllers.Toolbar.textRating": "Ratings", "SSE.Controllers.Toolbar.textRecentlyUsed": "Recently used", "SSE.Controllers.Toolbar.textScript": "Скриптове", "SSE.Controllers.Toolbar.textShapes": "Форми", "SSE.Controllers.Toolbar.textSymbols": "Символи", "SSE.Controllers.Toolbar.textWarning": "Внимание", "SSE.Controllers.Toolbar.txtAccent_Accent": "Ос<PERSON><PERSON>р", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Стрелка горе вдясно", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Стрелка наляво по-горе", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Стрелка нагоре отдясно", "SSE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Долен ред", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Бар горе", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Буквена формула (с контейнер)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Буквена формула (пример)", "SSE.Controllers.Toolbar.txtAccent_Check": "Проверка", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Долна скоба", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Горна скоба", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Вектор А", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC с горна черта", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y с горна черта", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Тройна точка", "SSE.Controllers.Toolbar.txtAccent_DDot": "Двойна точка", "SSE.Controllers.Toolbar.txtAccent_Dot": "Точка", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Двойна лента", "SSE.Controllers.Toolbar.txtAccent_Grave": "Ударение", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Групиране по-долу", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Символът за групиране по-горе", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "<PERSON><PERSON><PERSON> харпун отгоре", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Отдясно се намира харпунът по-горе", "SSE.Controllers.Toolbar.txtAccent_Hat": "Шапка", "SSE.Controllers.Toolbar.txtAccent_Smile": "Знак за краткост", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Тилда", "SSE.Controllers.Toolbar.txtBracket_Angle": "Скоби", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Скоби с разделители", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Скоби с разделители", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Curve": "Скоби", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Скоби с разделители", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Случаи (две условия)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Случаи (три условия)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Стек предмет", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Стек предмет", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Примерни случаи", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Биномиален коефициент", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Биномиален коефициент", "SSE.Controllers.Toolbar.txtBracket_Line": "Скоби", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Скоби", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Скоби", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Round": "Скоби", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Скоби с разделители", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Square": "Скоби", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Скоби", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Скоби", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Скоби", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Скоби", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Скоби", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Единична скоба", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Единична скоба", "SSE.Controllers.Toolbar.txtDeleteCells": "Delete cells", "SSE.Controllers.Toolbar.txtExpand": "Разширяване и сортиране", "SSE.Controllers.Toolbar.txtExpandSort": "Данните до селекцията няма да бъдат сортирани. Искате ли да разширите избора, за да включите съседните данни или да продължите с сортирането само на избраните в момента клетки?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Изкривена фракция", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Диференциал", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Диференциал", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Диференциал", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Диференциал", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Линейна фракция", "SSE.Controllers.Toolbar.txtFractionPi_2": "Пи над 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Малка фракция", "SSE.Controllers.Toolbar.txtFractionVertical": "Натрупана фракция", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Обратна косинусна функция", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Хиперболична инверсна косинусна функция", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Обратна котангенсна функция", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Хиперболична инверсна котангенс функция", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Обратна косекантна функция", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Хиперболична обратна косекантна функция", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Обратна секансова функция", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Хиперболична инверсна секундна функция", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Обратна функция на синуса", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Хиперболична инверсна синусова функция", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Функция за обратна допирателна", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Хиперболична инверсна тангенциална функция", "SSE.Controllers.Toolbar.txtFunction_Cos": "Косинусна функция", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Хиперболична косинусна функция", "SSE.Controllers.Toolbar.txtFunction_Cot": "Котангенсова функция", "SSE.Controllers.Toolbar.txtFunction_Coth": "Хиперболична котангенсна функция", "SSE.Controllers.Toolbar.txtFunction_Csc": "Функция на косекант", "SSE.Controllers.Toolbar.txtFunction_Csch": "Хиперболична косекантна функция", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sine theta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Косинус 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Формула на допирателната", "SSE.Controllers.Toolbar.txtFunction_Sec": "Секансова функция", "SSE.Controllers.Toolbar.txtFunction_Sech": "Хиперболична секундна функция", "SSE.Controllers.Toolbar.txtFunction_Sin": "Синусова функция", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Хиперболична функция на синуса", "SSE.Controllers.Toolbar.txtFunction_Tan": "Функция на допирателната", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Хиперболична допирателна функция", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Data and model", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Good, bad and neutral", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "No name", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Number format", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Themed cell styles", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Titles and headings", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Тъмна", "SSE.Controllers.Toolbar.txtGroupTable_Light": "Светла", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Medium", "SSE.Controllers.Toolbar.txtInsertCells": "Insert cells", "SSE.Controllers.Toolbar.txtIntegral": "Интеграл", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Диференциална тета", "SSE.Controllers.Toolbar.txtIntegral_dx": "Диференциал x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Диференциал y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Интеграл", "SSE.Controllers.Toolbar.txtIntegralDouble": "Двойна интегрална", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Двойна интегрална", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Двойна интегрална", "SSE.Controllers.Toolbar.txtIntegralOriented": "Контурен интеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Контурен интеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Повърхностен интеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Повърхностен интеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Повърхностен интеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Контурен интеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Интегрален обем", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Интегрален обем", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Интегрален обем", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Интеграл", "SSE.Controllers.Toolbar.txtIntegralTriple": "Троен интеграл", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Троен интеграл", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Троен интеграл", "SSE.Controllers.Toolbar.txtInvalidRange": "ГРЕШКА! Невалиден диапазон от клетки", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Ко-произведение", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Ко-произведение", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Ко-произведение", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Ко-произведение", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Ко-произведение", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Сумиране", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Сумиране", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Сумиране", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Произведение", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Съюз", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "V-образна", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "V-образна", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "V-образна", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "V-образна", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "V-образна", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Пресичане", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Пресичане", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Пресичане", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Пресичане", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Пресичане", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Произведение", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Произведение", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Произведение", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Произведение", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Произведение", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Сумиране", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Сумиране", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Сумиране", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Сумиране", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Сумиране", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Съюз", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Съюз", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Съюз", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Съюз", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Съюз", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Ограничен пример", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Максимален пример", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON>и<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Естествен логаритъм", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Лог<PERSON><PERSON><PERSON><PERSON>ъм", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Лог<PERSON><PERSON><PERSON><PERSON>ъм", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Максимален", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Мини<PERSON>ум", "SSE.Controllers.Toolbar.txtLockSort": "Data is found next to your selection, but you do not have sufficient permissions to change those cells.<br>Do you wish to continue with the current selection?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "Празна матрица 1 x 2", "SSE.Controllers.Toolbar.txtMatrix_1_3": "Празна матрица 1 x 3", "SSE.Controllers.Toolbar.txtMatrix_2_1": "Празна матрица 2 x 1", "SSE.Controllers.Toolbar.txtMatrix_2_2": "Празна матрица 2 x 2", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Празна матрица със скоби", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Празна матрица със скоби", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Празна матрица със скоби", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Празна матрица със скоби", "SSE.Controllers.Toolbar.txtMatrix_2_3": "Празна матрица 2 x 3", "SSE.Controllers.Toolbar.txtMatrix_3_1": "Празна матрица 3 x 1", "SSE.Controllers.Toolbar.txtMatrix_3_2": "Празна матрица 3 x 2", "SSE.Controllers.Toolbar.txtMatrix_3_3": "Празна матрица 3 x 3", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Базови точки", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Средни точки", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Диагонални точки", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Вертикални точки", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Разредена матрица", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Разредена матрица", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "Матрица за идентичност 2x2", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 матрица за идентичност", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 матрица за идентичност", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 матрица за идентичност", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Долу с дясна лява стрелка", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Стрелка горе вдясно", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Стрелка наляво долу", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Стрелка наляво по-горе", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Долу вдясно стрелка", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Стрелка нагоре отдясно", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Колонът е равен", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Добивите", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Делта добиви", "SSE.Controllers.Toolbar.txtOperator_Definition": "Равен на дефиницията", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Делта е равно на", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Долу с дясна лява стрелка", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Стрелка горе вдясно", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Стрелка наляво долу", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Стрелка наляво по-горе", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Долу вдясно стрелка", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Стрелка нагоре отдясно", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Равноправен", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Минус равен", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Плюс равни", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Измерено от", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Радикален", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Радикален", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Квад<PERSON>а<PERSON><PERSON><PERSON> корен със степен", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Кубичен корен", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Радикално със степен", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Корен квадратен", "SSE.Controllers.Toolbar.txtScriptCustom_1": "Скрипт", "SSE.Controllers.Toolbar.txtScriptCustom_2": "Скрипт", "SSE.Controllers.Toolbar.txtScriptCustom_3": "Скрипт", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Скрипт", "SSE.Controllers.Toolbar.txtScriptSub": "До<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSubSup": "Долен-горен", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Ляв индекс-горен индекс", "SSE.Controllers.Toolbar.txtScriptSup": "Горен индекс", "SSE.Controllers.Toolbar.txtSorting": "Сортиране", "SSE.Controllers.Toolbar.txtSortSelected": "Сортиране е избрано", "SSE.Controllers.Toolbar.txtSymbol_about": "Приблизително", "SSE.Controllers.Toolbar.txtSymbol_additional": "Допълнение", "SSE.Controllers.Toolbar.txtSymbol_aleph": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Алфа", "SSE.Controllers.Toolbar.txtSymbol_approx": "Почти равно на", "SSE.Controllers.Toolbar.txtSymbol_ast": "Оператор на звездичка", "SSE.Controllers.Toolbar.txtSymbol_beta": "Бета", "SSE.Controllers.Toolbar.txtSymbol_beth": "Залагане", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Оператор на куршум", "SSE.Controllers.Toolbar.txtSymbol_cap": "Пресичане", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Кубичен корен", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Хоризонтална елипса на средна линия", "SSE.Controllers.Toolbar.txtSymbol_celsius": "Градуса по Целзий", "SSE.Controllers.Toolbar.txtSymbol_chi": "Хи", "SSE.Controllers.Toolbar.txtSymbol_cong": "Приблизително равен на", "SSE.Controllers.Toolbar.txtSymbol_cup": "Съюз", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Долу в дясно диагонално елипса", "SSE.Controllers.Toolbar.txtSymbol_degree": "Степени", "SSE.Controllers.Toolbar.txtSymbol_delta": "Делта", "SSE.Controllers.Toolbar.txtSymbol_div": "Знак за разделяне", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Стрелка надолу", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Празен комплект", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Епсилон", "SSE.Controllers.Toolbar.txtSymbol_equals": "<PERSON>ав<PERSON>н", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Идентичен на", "SSE.Controllers.Toolbar.txtSymbol_eta": "Ета", "SSE.Controllers.Toolbar.txtSymbol_exists": "Съществуват", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Факториел", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Градуси по Фаренхайт", "SSE.Controllers.Toolbar.txtSymbol_forall": "За всички", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Гама", "SSE.Controllers.Toolbar.txtSymbol_geq": "По-голяма или равна на", "SSE.Controllers.Toolbar.txtSymbol_gg": "Много по-голяма от", "SSE.Controllers.Toolbar.txtSymbol_greater": "По-голям от", "SSE.Controllers.Toolbar.txtSymbol_in": "Елемент на", "SSE.Controllers.Toolbar.txtSymbol_inc": "Увеличение", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Безкрайност", "SSE.Controllers.Toolbar.txtSymbol_iota": "Йота", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Капа", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Ламбда", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Лява стрелка", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Стрелка наляво-надясно", "SSE.Controllers.Toolbar.txtSymbol_leq": "По-малко или равно на", "SSE.Controllers.Toolbar.txtSymbol_less": "По-малко от", "SSE.Controllers.Toolbar.txtSymbol_ll": "Много по-малко", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "Мин<PERSON>с плюс", "SSE.Controllers.Toolbar.txtSymbol_mu": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Набла", "SSE.Controllers.Toolbar.txtSymbol_neq": "Не е равно на", "SSE.Controllers.Toolbar.txtSymbol_ni": "Съдържа като член", "SSE.Controllers.Toolbar.txtSymbol_not": "Не се подписва", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Не съществува", "SSE.Controllers.Toolbar.txtSymbol_nu": "Ну", "SSE.Controllers.Toolbar.txtSymbol_o": "Омикрон", "SSE.Controllers.Toolbar.txtSymbol_omega": "Омега", "SSE.Controllers.Toolbar.txtSymbol_partial": "Частичен диференциал", "SSE.Controllers.Toolbar.txtSymbol_percent": "Процент", "SSE.Controllers.Toolbar.txtSymbol_phi": "Фи", "SSE.Controllers.Toolbar.txtSymbol_pi": "Пи", "SSE.Controllers.Toolbar.txtSymbol_plus": "Плюс", "SSE.Controllers.Toolbar.txtSymbol_pm": "Плюс минус", "SSE.Controllers.Toolbar.txtSymbol_propto": "Пропорционално на", "SSE.Controllers.Toolbar.txtSymbol_psi": "Пси", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Четвърти корен", "SSE.Controllers.Toolbar.txtSymbol_qed": "Край на доказателството", "SSE.Controllers.Toolbar.txtSymbol_rddots": "До дясната диагонална точка", "SSE.Controllers.Toolbar.txtSymbol_rho": "Ро", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON>на стрелка", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Сигма", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Радик<PERSON><PERSON><PERSON><PERSON> знак", "SSE.Controllers.Toolbar.txtSymbol_tau": "Тау", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Следователно", "SSE.Controllers.Toolbar.txtSymbol_theta": "Тета", "SSE.Controllers.Toolbar.txtSymbol_times": "Знак за умножение", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Стрелка нагоре", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Ипсилон", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Епсилон вариант", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Фи вариант", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Пи вариант", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Ро вариант", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Сигма вариант", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Тета вариант", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Вертикална елипса", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Зета", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Table style Dark", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Table style Light", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Table style Medium", "SSE.Controllers.Toolbar.warnLongOperation": "Операцията, която ще изпълните, може да отнеме доста време за изпълнение. <br> Наистина ли искате да продължите?", "SSE.Controllers.Toolbar.warnMergeLostData": "Само данните от горната лява клетка ще останат в обединената клетка. <br> Наистина ли искате да продължите?", "SSE.Controllers.Toolbar.warnNoRecommended": "To create a chart, select the cells that contain the data you'd like to use.<br>If you have names for the rows and columns and you'd like use them as labels, include them in your selection.", "SSE.Controllers.Viewport.textFreezePanes": "Фиксирай прозорците", "SSE.Controllers.Viewport.textFreezePanesShadow": "Show Frozen Panes Shadow", "SSE.Controllers.Viewport.textHideFBar": "Скриване на лентата за формули", "SSE.Controllers.Viewport.textHideGridlines": "Скриване на решетки", "SSE.Controllers.Viewport.textHideHeadings": "Скриване на заглавията", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Decimal separator", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Thousands separator", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Settings used to recognize numeric data", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Text qualifier", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Разширени настройки", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(none)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Персонализи<PERSON><PERSON>н филтър", "SSE.Views.AutoFilterDialog.textAddSelection": "Добавяне на текуща селекция за филтриране", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Заготовки}", "SSE.Views.AutoFilterDialog.textSelectAll": "Избери всички", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Изберете всички резултати от търсенето", "SSE.Views.AutoFilterDialog.textWarning": "Внимание", "SSE.Views.AutoFilterDialog.txtAboveAve": "Над средното", "SSE.Views.AutoFilterDialog.txtAfter": "After...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "All dates in the period", "SSE.Views.AutoFilterDialog.txtApril": "April", "SSE.Views.AutoFilterDialog.txtAugust": "August", "SSE.Views.AutoFilterDialog.txtBefore": "Before...", "SSE.Views.AutoFilterDialog.txtBegins": "Започва с...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Под средното", "SSE.Views.AutoFilterDialog.txtBetween": "Между ...", "SSE.Views.AutoFilterDialog.txtClear": "Изчисти", "SSE.Views.AutoFilterDialog.txtContains": "Съдържа...", "SSE.Views.AutoFilterDialog.txtDateFilter": "Date filter", "SSE.Views.AutoFilterDialog.txtDecember": "December", "SSE.Views.AutoFilterDialog.txtEmpty": "Въведете клетъчен филтър", "SSE.Views.AutoFilterDialog.txtEnds": "Завършва със...", "SSE.Views.AutoFilterDialog.txtEquals": "Равно на...", "SSE.Views.AutoFilterDialog.txtFebruary": "February", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Филтриране по цвят на клетките", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Филтриране по цвят на шрифта", "SSE.Views.AutoFilterDialog.txtGreater": "По-велик от...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "По-голяма или равна на ...", "SSE.Views.AutoFilterDialog.txtJanuary": "January", "SSE.Views.AutoFilterDialog.txtJuly": "July", "SSE.Views.AutoFilterDialog.txtJune": "June", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Label filter", "SSE.Views.AutoFilterDialog.txtLastMonth": "Last month", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Last quarter", "SSE.Views.AutoFilterDialog.txtLastWeek": "Last week", "SSE.Views.AutoFilterDialog.txtLastYear": "Last year", "SSE.Views.AutoFilterDialog.txtLess": "По-малко от...", "SSE.Views.AutoFilterDialog.txtLessEquals": "По-малко или равно на ...", "SSE.Views.AutoFilterDialog.txtMarch": "March", "SSE.Views.AutoFilterDialog.txtMay": "May", "SSE.Views.AutoFilterDialog.txtNextMonth": "Next month", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Next quarter", "SSE.Views.AutoFilterDialog.txtNextWeek": "Next week", "SSE.Views.AutoFilterDialog.txtNextYear": "Next year", "SSE.Views.AutoFilterDialog.txtNotBegins": "Не започва с ...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Not between...", "SSE.Views.AutoFilterDialog.txtNotContains": "Не съдържа...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Не завършва с ...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Не е равно ...", "SSE.Views.AutoFilterDialog.txtNovember": "November", "SSE.Views.AutoFilterDialog.txtNumFilter": "Филтър за номера", "SSE.Views.AutoFilterDialog.txtOctober": "October", "SSE.Views.AutoFilterDialog.txtQuarter1": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Quarter 1", "SSE.Views.AutoFilterDialog.txtReapply": "Повторно", "SSE.Views.AutoFilterDialog.txtSeptember": "September", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Сортиране по цвят на клетките", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Сортиране по цвят на шрифта", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Сортиране по низходящ ред", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Сортиране във възходящ ред", "SSE.Views.AutoFilterDialog.txtSortOption": "More sort options...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Текстов филтър", "SSE.Views.AutoFilterDialog.txtThisMonth": "This Month", "SSE.Views.AutoFilterDialog.txtThisQuarter": "This quarter", "SSE.Views.AutoFilterDialog.txtThisWeek": "This week", "SSE.Views.AutoFilterDialog.txtThisYear": "This Year", "SSE.Views.AutoFilterDialog.txtTitle": "<PERSON>и<PERSON><PERSON><PERSON>р", "SSE.Views.AutoFilterDialog.txtToday": "Today", "SSE.Views.AutoFilterDialog.txtTomorrow": "Tomorrow", "SSE.Views.AutoFilterDialog.txtTop10": "Топ 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Value filter", "SSE.Views.AutoFilterDialog.txtYearToDate": "Year to date", "SSE.Views.AutoFilterDialog.txtYesterday": "Yesterday", "SSE.Views.AutoFilterDialog.warnFilterError": "You need at least one field in the Values area in order to apply a value filter.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Трябва да изберете поне една стойност", "SSE.Views.CellEditor.textManager": "Мениджър на имена", "SSE.Views.CellEditor.tipFormula": "Вмъкване на функция", "SSE.Views.CellRangeDialog.errorMaxRows": "ГРЕШКА! Максималният брой поредици данни за диаграма е 255", "SSE.Views.CellRangeDialog.errorStockChart": "Неправилен ред на ред. За изграждане на борсова карта поставете данните на листа в следния ред: <br> цена на отваряне, максимална цена, мин. цена, цена на затваряне.", "SSE.Views.CellRangeDialog.txtEmpty": "Това поле е задължително", "SSE.Views.CellRangeDialog.txtInvalidRange": "ГРЕШКА! Невалиден диапазон от клетки", "SSE.Views.CellRangeDialog.txtTitle": "Изберете диапазон от данни", "SSE.Views.CellSettings.strShrink": "Shrink to fit", "SSE.Views.CellSettings.strWrap": "Wrap text", "SSE.Views.CellSettings.textAngle": "Ъгъл", "SSE.Views.CellSettings.textBackColor": "Цвят на фона", "SSE.Views.CellSettings.textBackground": "Background color", "SSE.Views.CellSettings.textBorderColor": "Цвят", "SSE.Views.CellSettings.textBorders": "Стил на границите", "SSE.Views.CellSettings.textClearRule": "Clear Rules", "SSE.Views.CellSettings.textColor": "Цветово пълнене", "SSE.Views.CellSettings.textColorScales": "Color scales", "SSE.Views.CellSettings.textCondFormat": "Conditional formatting", "SSE.Views.CellSettings.textControl": "Text control", "SSE.Views.CellSettings.textDataBars": "Data bars", "SSE.Views.CellSettings.textDirection": "Direction", "SSE.Views.CellSettings.textFill": "Напълнете", "SSE.Views.CellSettings.textForeground": "Цвят на преден план", "SSE.Views.CellSettings.textGradient": "Gradient points", "SSE.Views.CellSettings.textGradientColor": "Цвят", "SSE.Views.CellSettings.textGradientFill": "Gradient fill", "SSE.Views.CellSettings.textIndent": "Indent", "SSE.Views.CellSettings.textItems": "Items", "SSE.Views.CellSettings.textLinear": "Linear", "SSE.Views.CellSettings.textManageRule": "Manage rules", "SSE.Views.CellSettings.textNewRule": "New rule", "SSE.Views.CellSettings.textNoFill": "No fill", "SSE.Views.CellSettings.textOrientation": "Ориентация на текста", "SSE.Views.CellSettings.textPattern": "Pattern", "SSE.Views.CellSettings.textPatternFill": "Pattern", "SSE.Views.CellSettings.textPosition": "Позиция", "SSE.Views.CellSettings.textRadial": "Radial", "SSE.Views.CellSettings.textSelectBorders": "Изберете граници, които искате да промените, като използвате избрания по-горе стил", "SSE.Views.CellSettings.textSelection": "From current selection", "SSE.Views.CellSettings.textThisPivot": "From this pivot", "SSE.Views.CellSettings.textThisSheet": "From this worksheet", "SSE.Views.CellSettings.textThisTable": "From this table", "SSE.Views.CellSettings.tipAddGradientPoint": "Add gradient point", "SSE.Views.CellSettings.tipAll": "Задайте външната граница и всички вътрешни линии", "SSE.Views.CellSettings.tipBottom": "Задайте само външната долна граница", "SSE.Views.CellSettings.tipDiagD": "Задайте диагонална надолу граница", "SSE.Views.CellSettings.tipDiagU": "Задайте диагонална нагоре граница", "SSE.Views.CellSettings.tipInner": "Задайте само вътрешни линии", "SSE.Views.CellSettings.tipInnerHor": "Задайте само хоризонтални вътрешни линии", "SSE.Views.CellSettings.tipInnerVert": "Задайте само вертикални вътрешни линии", "SSE.Views.CellSettings.tipLeft": "Задайте само лявата граница", "SSE.Views.CellSettings.tipNone": "Без граници", "SSE.Views.CellSettings.tipOuter": "Задайте само външната граница", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Remove gradient point", "SSE.Views.CellSettings.tipRight": "Задайте само външна дясна граница", "SSE.Views.CellSettings.tipTop": "Задайте само външна горна граница", "SSE.Views.ChartDataDialog.errorInFormula": "There's an error in formula you entered.", "SSE.Views.ChartDataDialog.errorInvalidReference": "The reference is not valid. Reference must be to an open worksheet.", "SSE.Views.ChartDataDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "The reference is not valid. References for titles, values, sizes, or data labels must be a single cell, row, or column.", "SSE.Views.ChartDataDialog.errorNoValues": "To create a chart, the series must contain at least one value.", "SSE.Views.ChartDataDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "SSE.Views.ChartDataDialog.textAdd": "Add", "SSE.Views.ChartDataDialog.textCategory": "Horizontal (category) axis labels", "SSE.Views.ChartDataDialog.textData": "Chart data range", "SSE.Views.ChartDataDialog.textDelete": "Премахване", "SSE.Views.ChartDataDialog.textDown": "Down", "SSE.Views.ChartDataDialog.textEdit": "Edit", "SSE.Views.ChartDataDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ChartDataDialog.textSelectData": "Select data", "SSE.Views.ChartDataDialog.textSeries": "Legend entries (series)", "SSE.Views.ChartDataDialog.textSwitch": "Switch row/column", "SSE.Views.ChartDataDialog.textTitle": "Chart data", "SSE.Views.ChartDataDialog.textUp": "Up", "SSE.Views.ChartDataRangeDialog.errorInFormula": "There's an error in formula you entered.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "The reference is not valid. Reference must be to an open worksheet.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "The reference is not valid. References for titles, values, sizes, or data labels must be a single cell, row, or column.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "To create a chart, the series must contain at least one value.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ChartDataRangeDialog.textSelectData": "Select data", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Axis label range", "SSE.Views.ChartDataRangeDialog.txtChoose": "Choose range", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Име на серията", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Axis labels", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Edit series", "SSE.Views.ChartDataRangeDialog.txtValues": "Values", "SSE.Views.ChartDataRangeDialog.txtXValues": "X values", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y values", "SSE.Views.ChartSettings.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartSettings.strLineWeight": "Тегло на линията", "SSE.Views.ChartSettings.strSparkColor": "Цвят", "SSE.Views.ChartSettings.strTemplate": "Шабл<PERSON>н", "SSE.Views.ChartSettings.text3dDepth": "Depth (% of base)", "SSE.Views.ChartSettings.text3dHeight": "Height (% of base)", "SSE.Views.ChartSettings.text3dRotation": "3D Rotation", "SSE.Views.ChartSettings.textAdvanced": "Показване на разширените настройки", "SSE.Views.ChartSettings.textAutoscale": "Autoscale", "SSE.Views.ChartSettings.textBorderSizeErr": "Въведената стойност е неправилна. <br> Въведете стойност между 0 pt и 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "Change type", "SSE.Views.ChartSettings.textChartType": "Промяна на типа на диаграмата", "SSE.Views.ChartSettings.textDefault": "Default Rotation", "SSE.Views.ChartSettings.textDown": "Down", "SSE.Views.ChartSettings.textEditData": "Редактиране на данни и местоположение", "SSE.Views.ChartSettings.textFirstPoint": "Първа точка", "SSE.Views.ChartSettings.textHeight": "Висо<PERSON>ина", "SSE.Views.ChartSettings.textHighPoint": "Висока точка", "SSE.Views.ChartSettings.textKeepRatio": "Постоянни пропорции", "SSE.Views.ChartSettings.textLastPoint": "Последна точка", "SSE.Views.ChartSettings.textLeft": "Left", "SSE.Views.ChartSettings.textLowPoint": "Ниска точка", "SSE.Views.ChartSettings.textMarkers": "Маркери", "SSE.Views.ChartSettings.textNarrow": "Narrow field of view", "SSE.Views.ChartSettings.textNegativePoint": "Отрицателна точка", "SSE.Views.ChartSettings.textPerspective": "Perspective", "SSE.Views.ChartSettings.textRanges": "Диапазон на данните", "SSE.Views.ChartSettings.textRight": "Right", "SSE.Views.ChartSettings.textRightAngle": "Right angle axes", "SSE.Views.ChartSettings.textSelectData": "Изберете данни", "SSE.Views.ChartSettings.textShow": "Пока<PERSON>и", "SSE.Views.ChartSettings.textSize": "Размер", "SSE.Views.ChartSettings.textStyle": "Стил", "SSE.Views.ChartSettings.textSwitch": "Switch Row/Column", "SSE.Views.ChartSettings.textType": "Тип", "SSE.Views.ChartSettings.textUp": "Up", "SSE.Views.ChartSettings.textWiden": "Widen field of view", "SSE.Views.ChartSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textX": "X rotation", "SSE.Views.ChartSettings.textY": "Y rotation", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ГРЕШКА! Максималният брой точки в серия на графиката е 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "Максимален брой поредици данни за диаграма е 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Неправилен ред на ред. За изграждане на борсова карта поставете данните на листа в следния ред: <br> цена на отваряне, максимална цена, мин. цена, цена на затваряне.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Don't move or size with cells", "SSE.Views.ChartSettingsDlg.textAlt": "Алтернативен текст", "SSE.Views.ChartSettingsDlg.textAltDescription": "Описание", "SSE.Views.ChartSettingsDlg.textAltTip": "Алтернативното текстово представяне на визуалната информация за обекта, което ще бъде прочетено на хората с визуални или когнитивни увреждания, за да им помогне да разберат по-добре каква информация има в изображението, формата, диаграмата или таблицата.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Заглавие", "SSE.Views.ChartSettingsDlg.textAuto": "Автоматичен", "SSE.Views.ChartSettingsDlg.textAutoEach": "Авто за всеки", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Ос Кръстове", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Опции за ос", "SSE.Views.ChartSettingsDlg.textAxisPos": "Позиция на ос", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Настройки на ос", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Title", "SSE.Views.ChartSettingsDlg.textBase": "Base", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Между отметки с отметки", "SSE.Views.ChartSettingsDlg.textBillions": "Мил<PERSON><PERSON><PERSON><PERSON>и", "SSE.Views.ChartSettingsDlg.textBottom": "Отдоло", "SSE.Views.ChartSettingsDlg.textCategoryName": "Име на категория", "SSE.Views.ChartSettingsDlg.textCenter": "Център", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Елементи на диаграмата & <br> Легенда на диаграмата", "SSE.Views.ChartSettingsDlg.textChartTitle": "Заглавие на диаграмата", "SSE.Views.ChartSettingsDlg.textCross": "<PERSON>р<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.ChartSettingsDlg.textDataColumns": "в колони", "SSE.Views.ChartSettingsDlg.textDataLabels": "Етикети за данни", "SSE.Views.ChartSettingsDlg.textDataRows": "в редове", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Легенда на дисплея", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Скрити и празни клетки", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Свържете данни с линия", "SSE.Views.ChartSettingsDlg.textFit": "Поставя се в ширина", "SSE.Views.ChartSettingsDlg.textFixed": "Определен", "SSE.Views.ChartSettingsDlg.textFormat": "Label format", "SSE.Views.ChartSettingsDlg.textGaps": "Пропуски", "SSE.Views.ChartSettingsDlg.textGridLines": "Мрежови линии", "SSE.Views.ChartSettingsDlg.textGroup": "Група Sparkline", "SSE.Views.ChartSettingsDlg.textHide": "Скрий", "SSE.Views.ChartSettingsDlg.textHideAxis": "Hide axis", "SSE.Views.ChartSettingsDlg.textHigh": "Висок", "SSE.Views.ChartSettingsDlg.textHorAxis": "Хоризонтална ос", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Secondary horizontal axis", "SSE.Views.ChartSettingsDlg.textHorizontal": "Хоризонтален", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Стотици", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "В", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Вътрешно дъно", "SSE.Views.ChartSettingsDlg.textInnerTop": "Вътрешен връх", "SSE.Views.ChartSettingsDlg.textInvalidRange": "ГРЕШКА! Невалиден диапазон от клетки", "SSE.Views.ChartSettingsDlg.textLabelDist": "Разстояние на етикета на оста", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Интервал между етикети", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Опции за етикети", "SSE.Views.ChartSettingsDlg.textLabelPos": "Позиция на етикета", "SSE.Views.ChartSettingsDlg.textLayout": "Оформление", "SSE.Views.ChartSettingsDlg.textLeft": "Наляво", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Ляво наслагване", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Отдоло", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Наляво", "SSE.Views.ChartSettingsDlg.textLegendPos": "Легенда", "SSE.Views.ChartSettingsDlg.textLegendRight": "Прав", "SSE.Views.ChartSettingsDlg.textLegendTop": "Отгоре", "SSE.Views.ChartSettingsDlg.textLines": "Линии", "SSE.Views.ChartSettingsDlg.textLocationRange": "Обхват на местоположението", "SSE.Views.ChartSettingsDlg.textLogScale": "Logarithmic scale", "SSE.Views.ChartSettingsDlg.textLow": "Нисък", "SSE.Views.ChartSettingsDlg.textMajor": "Гол<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Майор и Мала", "SSE.Views.ChartSettingsDlg.textMajorType": "Основен тип", "SSE.Views.ChartSettingsDlg.textManual": "Наръчник", "SSE.Views.ChartSettingsDlg.textMarkers": "Маркери", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Интервал между знаците", "SSE.Views.ChartSettingsDlg.textMaxValue": "Максима<PERSON>на стойност", "SSE.Views.ChartSettingsDlg.textMillions": "Мили<PERSON><PERSON>и", "SSE.Views.ChartSettingsDlg.textMinor": "Незначителен", "SSE.Views.ChartSettingsDlg.textMinorType": "Малък тип", "SSE.Views.ChartSettingsDlg.textMinValue": "Мини<PERSON><PERSON><PERSON><PERSON> стойност", "SSE.Views.ChartSettingsDlg.textNextToAxis": "До ос", "SSE.Views.ChartSettingsDlg.textNone": "Нито един", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Няма наслагване", "SSE.Views.ChartSettingsDlg.textOneCell": "Move but don't size with cells", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Отбелязани марки", "SSE.Views.ChartSettingsDlg.textOut": "От", "SSE.Views.ChartSettingsDlg.textOuterTop": "Вън<PERSON>ен връх", "SSE.Views.ChartSettingsDlg.textOverlay": "Настилка", "SSE.Views.ChartSettingsDlg.textReverse": "Стойности в обратен ред", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Обратен ред", "SSE.Views.ChartSettingsDlg.textRight": "Прав", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Право наслагване", "SSE.Views.ChartSettingsDlg.textRotated": "Въртя", "SSE.Views.ChartSettingsDlg.textSameAll": "Същото е за всички", "SSE.Views.ChartSettingsDlg.textSelectData": "Изберете данни", "SSE.Views.ChartSettingsDlg.textSeparator": "Сепаратор за етикети за данни", "SSE.Views.ChartSettingsDlg.textSeriesName": "Име на серията", "SSE.Views.ChartSettingsDlg.textShow": "Пока<PERSON>и", "SSE.Views.ChartSettingsDlg.textShowBorders": "Показва граници на диаграмата", "SSE.Views.ChartSettingsDlg.textShowData": "Показване на данни в скрити редове и колони", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Показване на празни клетки като", "SSE.Views.ChartSettingsDlg.textShowEquation": "Display equation on chart", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Показване на ос", "SSE.Views.ChartSettingsDlg.textShowValues": "Показване на стойностите на диаграмата", "SSE.Views.ChartSettingsDlg.textSingle": "Единична Sparkline", "SSE.Views.ChartSettingsDlg.textSmooth": "Гл<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSnap": "Cell snapping", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Спарклайн диапазони", "SSE.Views.ChartSettingsDlg.textStraight": "Направо", "SSE.Views.ChartSettingsDlg.textStyle": "Стил", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Хиляди", "SSE.Views.ChartSettingsDlg.textTickOptions": "Отметнете опции", "SSE.Views.ChartSettingsDlg.textTitle": "Диаграма - Разширени настройки", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Спарклайн - Разширени настройки", "SSE.Views.ChartSettingsDlg.textTop": "Отгоре", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Trendline options", "SSE.Views.ChartSettingsDlg.textTrillions": "Трилиони", "SSE.Views.ChartSettingsDlg.textTwoCell": "Move and size with cells", "SSE.Views.ChartSettingsDlg.textType": "Тип", "SSE.Views.ChartSettingsDlg.textTypeData": "Тип & данни", "SSE.Views.ChartSettingsDlg.textUnits": "Дисплейни единици", "SSE.Views.ChartSettingsDlg.textValue": "Стойност", "SSE.Views.ChartSettingsDlg.textVertAxis": "Вертикална ос", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Secondary vertical axis", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X Заглавие на ос", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Заглавие на ос", "SSE.Views.ChartSettingsDlg.textZero": "Нула", "SSE.Views.ChartSettingsDlg.txtEmpty": "Това поле е задължително", "SSE.Views.ChartTypeDialog.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartTypeDialog.textSecondary": "Secondary axis", "SSE.Views.ChartTypeDialog.textSeries": "Серия", "SSE.Views.ChartTypeDialog.textStyle": "Style", "SSE.Views.ChartTypeDialog.textTitle": "Chart type", "SSE.Views.ChartTypeDialog.textType": "Type", "SSE.Views.ChartWizardDialog.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartWizardDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order: opening price, max price, min price, closing price.", "SSE.Views.ChartWizardDialog.textRecommended": "Recommended", "SSE.Views.ChartWizardDialog.textSecondary": "Secondary Axis", "SSE.Views.ChartWizardDialog.textSeries": "Series", "SSE.Views.ChartWizardDialog.textTitle": "Insert Chart", "SSE.Views.ChartWizardDialog.textTitleChange": "Change chart type", "SSE.Views.ChartWizardDialog.textType": "Type", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Choose the chart type and axis for your data series", "SSE.Views.CreatePivotDialog.textDataRange": "Source data range", "SSE.Views.CreatePivotDialog.textDestination": "Choose where to place the table", "SSE.Views.CreatePivotDialog.textExist": "Existing worksheet", "SSE.Views.CreatePivotDialog.textInvalidRange": "Invalid cells range", "SSE.Views.CreatePivotDialog.textNew": "New worksheet", "SSE.Views.CreatePivotDialog.textSelectData": "Select data", "SSE.Views.CreatePivotDialog.textTitle": "Create pivot table", "SSE.Views.CreatePivotDialog.txtEmpty": "This field is required", "SSE.Views.CreateSparklineDialog.textDataRange": "Source data range", "SSE.Views.CreateSparklineDialog.textDestination": "Choose, where to place the sparklines", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Invalid cells range", "SSE.Views.CreateSparklineDialog.textSelectData": "Select data", "SSE.Views.CreateSparklineDialog.textTitle": "Create sparklines", "SSE.Views.CreateSparklineDialog.txtEmpty": "This field is required", "SSE.Views.DataTab.capBtnGroup": "Гру<PERSON>а", "SSE.Views.DataTab.capBtnTextCustomSort": "Custom Sort", "SSE.Views.DataTab.capBtnTextDataValidation": "Data Validation", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Remove Duplicates", "SSE.Views.DataTab.capBtnTextToCol": "Текст в колони", "SSE.Views.DataTab.capBtnUngroup": "Разгрупира", "SSE.Views.DataTab.capDataExternalLinks": "External Links", "SSE.Views.DataTab.capDataFromText": "Get Data", "SSE.Views.DataTab.capGoalSeek": "Goal Seek", "SSE.Views.DataTab.mniFromFile": "From Local TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "From TXT/CSV Web Address", "SSE.Views.DataTab.mniFromXMLFile": "From Local XML", "SSE.Views.DataTab.textBelow": "Summary rows below detail", "SSE.Views.DataTab.textClear": "Clear outline", "SSE.Views.DataTab.textColumns": "Ungroup columns", "SSE.Views.DataTab.textGroupColumns": "Group columns", "SSE.Views.DataTab.textGroupRows": "Group rows", "SSE.Views.DataTab.textRightOf": "Summary columns to right of detail", "SSE.Views.DataTab.textRows": "Ungroup rows", "SSE.Views.DataTab.tipCustomSort": "Custom sort", "SSE.Views.DataTab.tipDataFromText": "Get data from file", "SSE.Views.DataTab.tipDataValidation": "Data validation", "SSE.Views.DataTab.tipExternalLinks": "View other files this spreadsheet is linked to", "SSE.Views.DataTab.tipGoalSeek": "Find the right input for the value you want", "SSE.Views.DataTab.tipGroup": "Group range of cells", "SSE.Views.DataTab.tipRemDuplicates": "Remove duplicate rows from a sheet", "SSE.Views.DataTab.tipToColumns": "Separate cell text into columns", "SSE.Views.DataTab.tipUngroup": "Ungroup range of cells", "SSE.Views.DataValidationDialog.errorFormula": "The value currently evaluates to an error. Do you want to continue?", "SSE.Views.DataValidationDialog.errorInvalid": "The value you entered for the field \"{0}\" is invalid.", "SSE.Views.DataValidationDialog.errorInvalidDate": "The date you entered for the field \"{0}\" is invalid.", "SSE.Views.DataValidationDialog.errorInvalidList": "The list source must be a delimited list, or a reference to single row or column.", "SSE.Views.DataValidationDialog.errorInvalidTime": "The time you entered for the field \"{0}\" is invalid.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "The \"{1}\" field must be greater than or equal to the \"{0}\" field.", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "You must enter a value in both field \"{0}\" and field \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "You must enter a value in field \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "A named range you specified cannot be found.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Negative values cannot be used in conditions \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "The field \"{0}\" must be a numeric value, numeric expression, or refer to a cell containing a numeric value.", "SSE.Views.DataValidationDialog.strError": "Error alert", "SSE.Views.DataValidationDialog.strInput": "Input message", "SSE.Views.DataValidationDialog.strSettings": "Настройки", "SSE.Views.DataValidationDialog.textAlert": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAllow": "Позволява", "SSE.Views.DataValidationDialog.textApply": "Apply these changes to all other cells with the same settings", "SSE.Views.DataValidationDialog.textCellSelected": "When cell is selected, show this input message", "SSE.Views.DataValidationDialog.textCompare": "Compare to", "SSE.Views.DataValidationDialog.textData": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndDate": "End date", "SSE.Views.DataValidationDialog.textEndTime": "End time", "SSE.Views.DataValidationDialog.textError": "Error message", "SSE.Views.DataValidationDialog.textFormula": "Формула", "SSE.Views.DataValidationDialog.textIgnore": "Ignore blank", "SSE.Views.DataValidationDialog.textInput": "Input message", "SSE.Views.DataValidationDialog.textMax": "Максимален", "SSE.Views.DataValidationDialog.textMessage": "Съобщение", "SSE.Views.DataValidationDialog.textMin": "Minimum", "SSE.Views.DataValidationDialog.textSelectData": "Select data", "SSE.Views.DataValidationDialog.textShowDropDown": "Show drop-down list in cell", "SSE.Views.DataValidationDialog.textShowError": "Show error alert after invalid data is entered", "SSE.Views.DataValidationDialog.textShowInput": "Show input message when cell is selected", "SSE.Views.DataValidationDialog.textSource": "Source", "SSE.Views.DataValidationDialog.textStartDate": "Start date", "SSE.Views.DataValidationDialog.textStartTime": "Start time", "SSE.Views.DataValidationDialog.textStop": "Stop", "SSE.Views.DataValidationDialog.textStyle": "Style", "SSE.Views.DataValidationDialog.textTitle": "Title", "SSE.Views.DataValidationDialog.textUserEnters": "When user enters invalid data, show this error alert", "SSE.Views.DataValidationDialog.txtAny": "Any value", "SSE.Views.DataValidationDialog.txtBetween": "between", "SSE.Views.DataValidationDialog.txtDate": "Date", "SSE.Views.DataValidationDialog.txtDecimal": "Decimal", "SSE.Views.DataValidationDialog.txtElTime": "Elapsed time", "SSE.Views.DataValidationDialog.txtEndDate": "End date", "SSE.Views.DataValidationDialog.txtEndTime": "End time", "SSE.Views.DataValidationDialog.txtEqual": "equals", "SSE.Views.DataValidationDialog.txtGreaterThan": "greater than", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "greater than or equal to", "SSE.Views.DataValidationDialog.txtLength": "Length", "SSE.Views.DataValidationDialog.txtLessThan": "less than", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "less than or equal to", "SSE.Views.DataValidationDialog.txtList": "List", "SSE.Views.DataValidationDialog.txtNotBetween": "not between", "SSE.Views.DataValidationDialog.txtNotEqual": "does not equal", "SSE.Views.DataValidationDialog.txtOther": "Other", "SSE.Views.DataValidationDialog.txtStartDate": "Start date", "SSE.Views.DataValidationDialog.txtStartTime": "Start time", "SSE.Views.DataValidationDialog.txtTextLength": "Text length", "SSE.Views.DataValidationDialog.txtTime": "Time", "SSE.Views.DataValidationDialog.txtWhole": "Whole number", "SSE.Views.DigitalFilterDialog.capAnd": "И", "SSE.Views.DigitalFilterDialog.capCondition1": "равно на", "SSE.Views.DigitalFilterDialog.capCondition10": "не завършва с", "SSE.Views.DigitalFilterDialog.capCondition11": "съдържа", "SSE.Views.DigitalFilterDialog.capCondition12": "не съдържа", "SSE.Views.DigitalFilterDialog.capCondition2": "не е равно", "SSE.Views.DigitalFilterDialog.capCondition3": "е по-голяма от", "SSE.Views.DigitalFilterDialog.capCondition30": "is after", "SSE.Views.DigitalFilterDialog.capCondition4": "е по-голямо или равно на", "SSE.Views.DigitalFilterDialog.capCondition40": "is after or equal to", "SSE.Views.DigitalFilterDialog.capCondition5": "е по-малко от", "SSE.Views.DigitalFilterDialog.capCondition50": "is before", "SSE.Views.DigitalFilterDialog.capCondition6": "е по-малко или равно на", "SSE.Views.DigitalFilterDialog.capCondition60": "is before or equal to", "SSE.Views.DigitalFilterDialog.capCondition7": "започва с", "SSE.Views.DigitalFilterDialog.capCondition8": "не започва с", "SSE.Views.DigitalFilterDialog.capCondition9": "завършва със", "SSE.Views.DigitalFilterDialog.capOr": "Или", "SSE.Views.DigitalFilterDialog.textNoFilter": "без филтър", "SSE.Views.DigitalFilterDialog.textShowRows": "Показване на редове, където", "SSE.Views.DigitalFilterDialog.textUse1": "Използвате ли? да представи всеки един символ", "SSE.Views.DigitalFilterDialog.textUse2": "Използвайте *, за да представите всяка серия от характер", "SSE.Views.DigitalFilterDialog.txtSelectDate": "Select date", "SSE.Views.DigitalFilterDialog.txtTitle": "Персонализи<PERSON><PERSON>н филтър", "SSE.Views.DocumentHolder.advancedEquationText": "Equation settings", "SSE.Views.DocumentHolder.advancedImgText": "Разширени настройки на изображението", "SSE.Views.DocumentHolder.advancedShapeText": "Разширени настройки за формата", "SSE.Views.DocumentHolder.advancedSlicerText": "Slicer advanced settings", "SSE.Views.DocumentHolder.allLinearText": "All - Linear", "SSE.Views.DocumentHolder.allProfText": "All - Professional", "SSE.Views.DocumentHolder.bottomCellText": "Подравняване отдолу", "SSE.Views.DocumentHolder.bulletsText": "Маркери и номериране", "SSE.Views.DocumentHolder.centerCellText": "Подравняване на средата", "SSE.Views.DocumentHolder.chartDataText": "Select Chart Data", "SSE.Views.DocumentHolder.chartText": "Разширени настройки на диаграмата", "SSE.Views.DocumentHolder.chartTypeText": "Change Chart Type", "SSE.Views.DocumentHolder.currLinearText": "Current - Linear", "SSE.Views.DocumentHolder.currProfText": "Current - Professional", "SSE.Views.DocumentHolder.deleteColumnText": "Колона", "SSE.Views.DocumentHolder.deleteRowText": "Ред", "SSE.Views.DocumentHolder.deleteTableText": "Таблица", "SSE.Views.DocumentHolder.direct270Text": "Завъртете текста нагоре", "SSE.Views.DocumentHolder.direct90Text": "Завъртете текста надолу", "SSE.Views.DocumentHolder.directHText": "Хоризонтален", "SSE.Views.DocumentHolder.directionText": "Текстова посока", "SSE.Views.DocumentHolder.editChartText": "Редактиране на данни", "SSE.Views.DocumentHolder.editHyperlinkText": "Редактиране на хипервръзка", "SSE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "SSE.Views.DocumentHolder.insertColumnLeftText": "Колона вляво", "SSE.Views.DocumentHolder.insertColumnRightText": "Колона вдясно", "SSE.Views.DocumentHolder.insertRowAboveText": "Ред по-горе", "SSE.Views.DocumentHolder.insertRowBelowText": "Ред по-долу", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "Размер по подразбиране", "SSE.Views.DocumentHolder.removeHyperlinkText": "Премахване на хипервръзка", "SSE.Views.DocumentHolder.selectColumnText": "Цяла колона", "SSE.Views.DocumentHolder.selectDataText": "Данни в колоната", "SSE.Views.DocumentHolder.selectRowText": "Ред", "SSE.Views.DocumentHolder.selectTableText": "Таблица", "SSE.Views.DocumentHolder.showEqToolbar": "Show Equation Toolbar", "SSE.Views.DocumentHolder.strDelete": "Премахване на подпис", "SSE.Views.DocumentHolder.strDetails": "Подробности за подпис", "SSE.Views.DocumentHolder.strSetup": "Настройка на подпис", "SSE.Views.DocumentHolder.strSign": "Знак", "SSE.Views.DocumentHolder.textAlign": "Изравнете", "SSE.Views.DocumentHolder.textArrange": "Подредете", "SSE.Views.DocumentHolder.textArrangeBack": "Изпращане до фона", "SSE.Views.DocumentHolder.textArrangeBackward": "Изпращане назад", "SSE.Views.DocumentHolder.textArrangeForward": "Изведи напред", "SSE.Views.DocumentHolder.textArrangeFront": "Доведете до преден план", "SSE.Views.DocumentHolder.textAverage": "Average", "SSE.Views.DocumentHolder.textBullets": "Bullets", "SSE.Views.DocumentHolder.textCopyCells": "Copy cells", "SSE.Views.DocumentHolder.textCount": "Count", "SSE.Views.DocumentHolder.textCrop": "Изрежете", "SSE.Views.DocumentHolder.textCropFill": "Напълнете", "SSE.Views.DocumentHolder.textCropFit": "Регулирате", "SSE.Views.DocumentHolder.textEditPoints": "Edit points", "SSE.Views.DocumentHolder.textEntriesList": "Изберете от падащия списък", "SSE.Views.DocumentHolder.textFillDays": "Fill days", "SSE.Views.DocumentHolder.textFillFormatOnly": "Fill formatting only", "SSE.Views.DocumentHolder.textFillMonths": "Fill months", "SSE.Views.DocumentHolder.textFillSeries": "Fill series", "SSE.Views.DocumentHolder.textFillWeekdays": "Fill weekdays", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Fill without formatting", "SSE.Views.DocumentHolder.textFillYears": "Fill years", "SSE.Views.DocumentHolder.textFlashFill": "Flash fill", "SSE.Views.DocumentHolder.textFlipH": "Отрязва по хоризонтала", "SSE.Views.DocumentHolder.textFlipV": "Отрязване по вертикала", "SSE.Views.DocumentHolder.textFreezePanes": "Фиксирай прозорците", "SSE.Views.DocumentHolder.textFromFile": "От файл ", "SSE.Views.DocumentHolder.textFromStorage": "From Storage", "SSE.Views.DocumentHolder.textFromUrl": "От URL", "SSE.Views.DocumentHolder.textGrowthTrend": "Growth trend", "SSE.Views.DocumentHolder.textLinearTrend": "Linear trend", "SSE.Views.DocumentHolder.textListSettings": "List Settings", "SSE.Views.DocumentHolder.textMacro": "Assign <PERSON>", "SSE.Views.DocumentHolder.textMax": "Max", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "More functions", "SSE.Views.DocumentHolder.textMoreFormats": "Още формати", "SSE.Views.DocumentHolder.textNone": "Нито един", "SSE.Views.DocumentHolder.textNumbering": "Numbering", "SSE.Views.DocumentHolder.textReplace": "Заменете изображението", "SSE.Views.DocumentHolder.textResetCrop": "Reset crop", "SSE.Views.DocumentHolder.textRotate": "Завъртане", "SSE.Views.DocumentHolder.textRotate270": "Завъртете на 90 ° обратно на часовниковата стрелка", "SSE.Views.DocumentHolder.textRotate90": "Завъртете на 90 ° по посока на часовниковата стрелка", "SSE.Views.DocumentHolder.textSaveAsPicture": "Save as picture", "SSE.Views.DocumentHolder.textSeries": "Series", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Подравняване отдолу", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Подравняване на центъра", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Подравняване вляво", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Подравняване на средата", "SSE.Views.DocumentHolder.textShapeAlignRight": "Подравняване надясно", "SSE.Views.DocumentHolder.textShapeAlignTop": "Подравняване отгоре", "SSE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "Sum", "SSE.Views.DocumentHolder.textUndo": "Отмени", "SSE.Views.DocumentHolder.textUnFreezePanes": "Размразете панелите", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "Arrow bullets", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Checkmark bullets", "SSE.Views.DocumentHolder.tipMarkersDash": "Dash bullets", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Filled rhombus bullets", "SSE.Views.DocumentHolder.tipMarkersFRound": "Filled round bullets", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Filled square bullets", "SSE.Views.DocumentHolder.tipMarkersHRound": "Hollow round bullets", "SSE.Views.DocumentHolder.tipMarkersStar": "Star bullets", "SSE.Views.DocumentHolder.topCellText": "Подравняване отгоре", "SSE.Views.DocumentHolder.txtAccounting": "Счетоводство", "SSE.Views.DocumentHolder.txtAddComment": "Добави коментар", "SSE.Views.DocumentHolder.txtAddNamedRange": "Определяне на име", "SSE.Views.DocumentHolder.txtArrange": "Подредете", "SSE.Views.DocumentHolder.txtAscending": "Възходящ", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Автоматично монтиране на ширината на колоната", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Автоматично монтиране на височината на ред", "SSE.Views.DocumentHolder.txtAverage": "Average", "SSE.Views.DocumentHolder.txtCellFormat": "Format cells", "SSE.Views.DocumentHolder.txtClear": "Изчисти", "SSE.Views.DocumentHolder.txtClearAll": "Всички", "SSE.Views.DocumentHolder.txtClearComments": "Коментари", "SSE.Views.DocumentHolder.txtClearFormat": "Формат", "SSE.Views.DocumentHolder.txtClearHyper": "Хипервръзки", "SSE.Views.DocumentHolder.txtClearPivotField": "Clear filter from {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Изчистване на избраните групи на Sparkline", "SSE.Views.DocumentHolder.txtClearSparklines": "Изчистване на избраните искрящи линии", "SSE.Views.DocumentHolder.txtClearText": "Текст", "SSE.Views.DocumentHolder.txtCollapse": "Collapse", "SSE.Views.DocumentHolder.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.DocumentHolder.txtColumn": "Цяла колона", "SSE.Views.DocumentHolder.txtColumnWidth": "Задайте ширина на колоната", "SSE.Views.DocumentHolder.txtCondFormat": "Conditional formatting", "SSE.Views.DocumentHolder.txtCopy": "Копие", "SSE.Views.DocumentHolder.txtCount": "Count", "SSE.Views.DocumentHolder.txtCurrency": "Валута", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Ширина на персонализираната колона", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Височина по избор", "SSE.Views.DocumentHolder.txtCustomSort": "Custom sort", "SSE.Views.DocumentHolder.txtCut": "Разрез", "SSE.Views.DocumentHolder.txtDateLong": "Long Date", "SSE.Views.DocumentHolder.txtDateShort": "Short Date", "SSE.Views.DocumentHolder.txtDelete": "Изтрий", "SSE.Views.DocumentHolder.txtDelField": "Remove", "SSE.Views.DocumentHolder.txtDescending": "Низходящо", "SSE.Views.DocumentHolder.txtDifference": "Difference from", "SSE.Views.DocumentHolder.txtDistribHor": "Разпределете хоризонтално", "SSE.Views.DocumentHolder.txtDistribVert": "Разпределете вертикално", "SSE.Views.DocumentHolder.txtEditComment": "Редактиране на коментара", "SSE.Views.DocumentHolder.txtEditObject": "Edit object", "SSE.Views.DocumentHolder.txtExpand": "Expand", "SSE.Views.DocumentHolder.txtExpandCollapse": "Expand/Collapse", "SSE.Views.DocumentHolder.txtExpandEntire": "Expand Entire Field", "SSE.Views.DocumentHolder.txtFieldSettings": "Field settings", "SSE.Views.DocumentHolder.txtFilter": "<PERSON>и<PERSON><PERSON><PERSON>р", "SSE.Views.DocumentHolder.txtFilterCellColor": "Филтриране по цвят на клетката", "SSE.Views.DocumentHolder.txtFilterFontColor": "Филтриране по цвят на шрифта", "SSE.Views.DocumentHolder.txtFilterValue": "Филтриране по стойност на избраната клетка", "SSE.Views.DocumentHolder.txtFormula": "Вмъкване на функция", "SSE.Views.DocumentHolder.txtFraction": "Фракция", "SSE.Views.DocumentHolder.txtGeneral": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtGetLink": "Get link to this range", "SSE.Views.DocumentHolder.txtGrandTotal": "Grand total", "SSE.Views.DocumentHolder.txtGroup": "Гру<PERSON>а", "SSE.Views.DocumentHolder.txtHide": "Скрий", "SSE.Views.DocumentHolder.txtIndex": "Index", "SSE.Views.DocumentHolder.txtInsert": "Вмъкни", "SSE.Views.DocumentHolder.txtInsHyperlink": "Хипервръзка", "SSE.Views.DocumentHolder.txtInsImage": "Insert image from file", "SSE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "SSE.Views.DocumentHolder.txtLabelFilter": "Label filters", "SSE.Views.DocumentHolder.txtMax": "Max", "SSE.Views.DocumentHolder.txtMin": "Min", "SSE.Views.DocumentHolder.txtMoreOptions": "More options", "SSE.Views.DocumentHolder.txtNormal": "No calculation", "SSE.Views.DocumentHolder.txtNumber": "Номер", "SSE.Views.DocumentHolder.txtNumFormat": "Формат на номера", "SSE.Views.DocumentHolder.txtPaste": "Паста", "SSE.Views.DocumentHolder.txtPercent": "% of", "SSE.Views.DocumentHolder.txtPercentage": "Процент", "SSE.Views.DocumentHolder.txtPercentDiff": "% difference from", "SSE.Views.DocumentHolder.txtPercentOfCol": "% of column total", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% of grand total", "SSE.Views.DocumentHolder.txtPercentOfParent": "% of parent total", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% of parent column total", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% of parent row total", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% running total in", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% of row total", "SSE.Views.DocumentHolder.txtPivotSettings": "Pivot Table settings", "SSE.Views.DocumentHolder.txtProduct": "Product", "SSE.Views.DocumentHolder.txtRankAscending": "Rank smallest to largest", "SSE.Views.DocumentHolder.txtRankDescending": "Rank largest to smallest", "SSE.Views.DocumentHolder.txtReapply": "Повторно", "SSE.Views.DocumentHolder.txtRefresh": "Refresh", "SSE.Views.DocumentHolder.txtRow": "Цял ред", "SSE.Views.DocumentHolder.txtRowHeight": "Задайте височина на редовете", "SSE.Views.DocumentHolder.txtRunTotal": "Running total in", "SSE.Views.DocumentHolder.txtScientific": "Научен", "SSE.Views.DocumentHolder.txtSelect": "Изберете", "SSE.Views.DocumentHolder.txtShiftDown": "Преместете клетките надолу", "SSE.Views.DocumentHolder.txtShiftLeft": "Преместване на клетките вляво", "SSE.Views.DocumentHolder.txtShiftRight": "Преместете клетките надясно", "SSE.Views.DocumentHolder.txtShiftUp": "Преместете клетки нагоре", "SSE.Views.DocumentHolder.txtShow": "Пока<PERSON>и", "SSE.Views.DocumentHolder.txtShowAs": "Show values as", "SSE.Views.DocumentHolder.txtShowComment": "Показване на коментара", "SSE.Views.DocumentHolder.txtShowDetails": "Show details", "SSE.Views.DocumentHolder.txtSort": "Вид", "SSE.Views.DocumentHolder.txtSortCellColor": "Избрани цветове на клетката отгоре", "SSE.Views.DocumentHolder.txtSortFontColor": "Цвят на избрания шрифт отгоре", "SSE.Views.DocumentHolder.txtSortOption": "More sort options", "SSE.Views.DocumentHolder.txtSparklines": "Блещукащи", "SSE.Views.DocumentHolder.txtSubtotalField": "Subtotal", "SSE.Views.DocumentHolder.txtSum": "Sum", "SSE.Views.DocumentHolder.txtSummarize": "Summarize values by", "SSE.Views.DocumentHolder.txtText": "Текст", "SSE.Views.DocumentHolder.txtTextAdvanced": "Разширени настройки на текста", "SSE.Views.DocumentHolder.txtTime": "Път", "SSE.Views.DocumentHolder.txtTop10": "Top 10", "SSE.Views.DocumentHolder.txtUngroup": "Разгрупира", "SSE.Views.DocumentHolder.txtValueFieldSettings": "Value field settings", "SSE.Views.DocumentHolder.txtValueFilter": "Value filters", "SSE.Views.DocumentHolder.txtWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "Вертикално подравняване", "SSE.Views.ExternalLinksDlg.closeButtonText": "Close", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Automatically update data from the linked sources", "SSE.Views.ExternalLinksDlg.textChange": "Change source", "SSE.Views.ExternalLinksDlg.textDelete": "Break links", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Break all links", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textOpen": "Open source", "SSE.Views.ExternalLinksDlg.textSource": "Source", "SSE.Views.ExternalLinksDlg.textStatus": "Status", "SSE.Views.ExternalLinksDlg.textUnknown": "Unknown", "SSE.Views.ExternalLinksDlg.textUpdate": "Update values", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Update all", "SSE.Views.ExternalLinksDlg.textUpdating": "Updating...", "SSE.Views.ExternalLinksDlg.txtTitle": "External links", "SSE.Views.FieldSettingsDialog.strLayout": "Layout", "SSE.Views.FieldSettingsDialog.strSubtotals": "Subtotals", "SSE.Views.FieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.FieldSettingsDialog.textReport": "Report form", "SSE.Views.FieldSettingsDialog.textTitle": "Field settings", "SSE.Views.FieldSettingsDialog.txtAverage": "Average", "SSE.Views.FieldSettingsDialog.txtBlank": "Insert blank rows after each item", "SSE.Views.FieldSettingsDialog.txtBottom": "Show at bottom of group", "SSE.Views.FieldSettingsDialog.txtCompact": "Compact", "SSE.Views.FieldSettingsDialog.txtCount": "Count", "SSE.Views.FieldSettingsDialog.txtCountNums": "Count numbers", "SSE.Views.FieldSettingsDialog.txtCustomName": "Custom name", "SSE.Views.FieldSettingsDialog.txtEmpty": "Show items with no data", "SSE.Views.FieldSettingsDialog.txtMax": "Max", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "Outline", "SSE.Views.FieldSettingsDialog.txtProduct": "Product", "SSE.Views.FieldSettingsDialog.txtRepeat": "Repeat items labels at each row", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Show subtotals", "SSE.Views.FieldSettingsDialog.txtSourceName": "Source name:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "Sum", "SSE.Views.FieldSettingsDialog.txtSummarize": "Functions for subtotals", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabular", "SSE.Views.FieldSettingsDialog.txtTop": "Show at top of group", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.ariaFileMenu": "File menu", "SSE.Views.FileMenu.btnBackCaption": "Mестоположението на файла", "SSE.Views.FileMenu.btnCloseEditor": "Close File", "SSE.Views.FileMenu.btnCloseMenuCaption": "Затваряне на менюто", "SSE.Views.FileMenu.btnCreateNewCaption": "Създай нов", "SSE.Views.FileMenu.btnDownloadCaption": "Изтеглете като", "SSE.Views.FileMenu.btnExitCaption": "Close", "SSE.Views.FileMenu.btnExportToPDFCaption": "Export to PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "Open", "SSE.Views.FileMenu.btnHelpCaption": "Помощ", "SSE.Views.FileMenu.btnHistoryCaption": "Version History", "SSE.Views.FileMenu.btnInfoCaption": "Информация за електронна таблица", "SSE.Views.FileMenu.btnPrintCaption": "Печат", "SSE.Views.FileMenu.btnProtectCaption": "Защитавам", "SSE.Views.FileMenu.btnRecentFilesCaption": "Отваряне на последните", "SSE.Views.FileMenu.btnRenameCaption": "Преименуване", "SSE.Views.FileMenu.btnReturnCaption": "Назад към електронна таблица", "SSE.Views.FileMenu.btnRightsCaption": "Права за достъп", "SSE.Views.FileMenu.btnSaveAsCaption": "Запази като", "SSE.Views.FileMenu.btnSaveCaption": "Запази", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Запазване на копието като", "SSE.Views.FileMenu.btnSettingsCaption": "Разширени настройки", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "SSE.Views.FileMenu.btnToEditCaption": "Редактиране на електронна таблица", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Blank Spreadsheet", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Create New", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Прило<PERSON>и", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Добави автор", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Add Text", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Приложение", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Промяна на правата за достъп", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Comment", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Created", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Last Modified By", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Last Modified", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Owner", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Местоположение", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON>и<PERSON><PERSON>, които имат права", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Spreadsheet info", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Subject", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Заглавие на електронната таблица", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Uploaded", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Access Rights", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Промяна на правата за достъп", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON>и<PERSON><PERSON>, които имат права", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Прило<PERSON>и", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Режим на съвместно редактиране", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Use 1904 date system", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Decimal separator", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Dictionary language", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Enable iterative calculation", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Подказване на шрифт", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Език на формулата", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Пример: SUM; MIN; MAX; БРОЯ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Show function tooltip", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Show horizontal scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Ignore words in UPPERCASE", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Ignore words with numbers", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Macros settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Maximum change", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Maximum iterations", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Show the paste options button when the content is pasted", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "R1C1 reference style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Регионални настройки", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Пример: ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTL interface", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Show comments in sheet", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Show changes from other users", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Show resolved comments", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Snapped to the grid while scrolling", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Стриктен", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Tab style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Interface theme", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Thousands separator", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Единица за измерване", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Use separators based on regional settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Show vertical scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Стойност на мащаба по подразбиране", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "На всеки 10 минути", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "На всеки 30 минути", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "На всеки 5 минути", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "Всеки час", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Автовъзстановяване", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Автоматично записване", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Хора с увреждания", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "Напълнете", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Запазване в сървър", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "Линия", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Всяка минута", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Рефе<PERSON><PERSON><PERSON><PERSON><PERSON>н стил", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "Advanced settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "Вън<PERSON>ен вид", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "AutoCorrect options...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Belarusian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Bulgarian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Catalan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Default cache mode", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Calculating", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Санти<PERSON>етър", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Collaboration", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Czech", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Customize quick access", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Danish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Немски", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Editing and saving", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Greek", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "Английски", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Испански", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Real-time co-editing. All changes are saved automatically", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Finnish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "Френски", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Hungarian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "Armenian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonesian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Италиански", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Japanese", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Korean", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Last used", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Lao", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Latvian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "като OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Ме<PERSON><PERSON><PERSON>н", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Norwegian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Dutch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Полски", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Proofing", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Точка", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Portuguese (Brazil)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "Portuguese (Portugal)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Show the Quick Print button in the editor header", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Region", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Romanian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Руски", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Enable All", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Enable all macros without a notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Turn on screen reader support", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Default sheet direction", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "This setting will affect only the new sheets", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Left-to-right", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Right-to-left", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Slovak", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Slovenian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Disable all", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Disable all macros without a notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Use the \"Save\" button to sync the changes you and others make", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Swedish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Use toolbar color as tabs background", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Turkish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ukrainian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Use Alt key to navigate the user interface using the keyboard", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Use Option key to navigate the user interface using the keyboard", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vietnamese", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Show Notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Disable all macros with a notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "като Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Workspace", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "Chinese", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Внимание", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "С парола", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Защитете електронната таблица", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "С подпис", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Valid signatures have been added to the spreadsheet.<br>The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Ensure the integrity of the spreadsheet by adding an<br>invisible digital signature", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Редактирайте електронната таблица", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Редактирането ще премахне подписите от електронната таблица. <br> Наистина ли искате да продължите?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Тази електронна таблица е защитена с парола", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Encrypt this spreadsheet with a password", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Тази електронна таблица трябва да бъде подписана.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "В електронната таблица са добавени валидни подписи. Електронната таблица е защитена от редактиране.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Някои от цифровите подписи в електронната таблица са невалидни или не можаха да бъдат потвърдени. Електронната таблица е защитена от редактиране.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Преглед на подписи", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Download as", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Save copy as", "SSE.Views.FillSeriesDialog.textAuto": "AutoFill", "SSE.Views.FillSeriesDialog.textCols": "Columns", "SSE.Views.FillSeriesDialog.textDate": "Date", "SSE.Views.FillSeriesDialog.textDateUnit": "Date unit", "SSE.Views.FillSeriesDialog.textDay": "Day", "SSE.Views.FillSeriesDialog.textGrowth": "Growth", "SSE.Views.FillSeriesDialog.textLinear": "Linear", "SSE.Views.FillSeriesDialog.textMonth": "Month", "SSE.Views.FillSeriesDialog.textRows": "Rows", "SSE.Views.FillSeriesDialog.textSeries": "Series in", "SSE.Views.FillSeriesDialog.textStep": "Step value", "SSE.Views.FillSeriesDialog.textStop": "Stop value", "SSE.Views.FillSeriesDialog.textTitle": "Series", "SSE.Views.FillSeriesDialog.textTrend": "Trend", "SSE.Views.FillSeriesDialog.textType": "Type", "SSE.Views.FillSeriesDialog.textWeek": "Weekday", "SSE.Views.FillSeriesDialog.textYear": "Year", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FormatRulesEditDlg.fillColor": "Цвят на запълване", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Warning", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 color scale", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 color scale", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Всички граници", "SSE.Views.FormatRulesEditDlg.textAppearance": "Bar appearance", "SSE.Views.FormatRulesEditDlg.textApply": "Apply to range", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Автоматичен", "SSE.Views.FormatRulesEditDlg.textAxis": "Axis", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Bar direction", "SSE.Views.FormatRulesEditDlg.textBold": "Bold", "SSE.Views.FormatRulesEditDlg.textBorder": "Border", "SSE.Views.FormatRulesEditDlg.textBordersColor": "Borders color", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Border style", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Bottom borders", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Cannot add the conditional formatting.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Cell midpoint", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Inside vertical borders", "SSE.Views.FormatRulesEditDlg.textClear": "Clear", "SSE.Views.FormatRulesEditDlg.textColor": "Цвят на текста", "SSE.Views.FormatRulesEditDlg.textContext": "Context", "SSE.Views.FormatRulesEditDlg.textCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Diagonal down border", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Diagonal up border", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Enter a valid formula.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "The formula you entered does not evaluate to a number, date, time or string.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Enter a value.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "The value you entered is not a valid number, date, time or string.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "The value for the {0} must be greater than the value for the {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Enter a number between {0} and {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Напълнете", "SSE.Views.FormatRulesEditDlg.textFormat": "Формат", "SSE.Views.FormatRulesEditDlg.textFormula": "Формула", "SSE.Views.FormatRulesEditDlg.textGradient": "Gradient", "SSE.Views.FormatRulesEditDlg.textIconLabel": "when {0} {1} and", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "when {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "when value is", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "One or more icon data ranges overlap.<br>Adjust icon data range values so that the ranges do not overlap.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Icon style", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Inside borders", "SSE.Views.FormatRulesEditDlg.textInvalid": "Invalid data range.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.FormatRulesEditDlg.textItalic": "Italic", "SSE.Views.FormatRulesEditDlg.textItem": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Left to right", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Left borders", "SSE.Views.FormatRulesEditDlg.textLongBar": "longest bar", "SSE.Views.FormatRulesEditDlg.textMaximum": "Максимален", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Maxpoint", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Inside horizontal borders", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Midpoint", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimum", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Minpoint", "SSE.Views.FormatRulesEditDlg.textNegative": "Negative", "SSE.Views.FormatRulesEditDlg.textNewColor": "Нов Потребителски Цвят", "SSE.Views.FormatRulesEditDlg.textNoBorders": "No borders", "SSE.Views.FormatRulesEditDlg.textNone": "None", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "One or more of the specified values is not a valid percentage.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "The specified {0} value is not a valid percentage.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "One or more of the specified values is not a valid percentile.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "The specified {0} value is not a valid percentile.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Outside borders", "SSE.Views.FormatRulesEditDlg.textPercent": "Percent", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentile", "SSE.Views.FormatRulesEditDlg.textPosition": "Позиция", "SSE.Views.FormatRulesEditDlg.textPositive": "Positive", "SSE.Views.FormatRulesEditDlg.textPresets": "Presets", "SSE.Views.FormatRulesEditDlg.textPreview": "Preview", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "You cannot use relative references in conditional formatting criteria for color scales, data bars, and icon sets.", "SSE.Views.FormatRulesEditDlg.textReverse": "Reverse icons order", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Right to left", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Right borders", "SSE.Views.FormatRulesEditDlg.textRule": "Rule", "SSE.Views.FormatRulesEditDlg.textSameAs": "Same as positive", "SSE.Views.FormatRulesEditDlg.textSelectData": "Select data", "SSE.Views.FormatRulesEditDlg.textShortBar": "shortest bar", "SSE.Views.FormatRulesEditDlg.textShowBar": "Show bar only", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Show icon only", "SSE.Views.FormatRulesEditDlg.textSingleRef": "This type of reference cannot be used in a conditional formatting formula.<br>Change the reference to a single cell, or use the reference with a worksheet function, such as =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Solid", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Strikeout", "SSE.Views.FormatRulesEditDlg.textSubscript": "Subscript", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Superscript", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Top borders", "SSE.Views.FormatRulesEditDlg.textUnderline": "Underline", "SSE.Views.FormatRulesEditDlg.tipBorders": "Borders", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Number format", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Accounting", "SSE.Views.FormatRulesEditDlg.txtCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDate": "Date", "SSE.Views.FormatRulesEditDlg.txtDateLong": "Long date", "SSE.Views.FormatRulesEditDlg.txtDateShort": "Short date", "SSE.Views.FormatRulesEditDlg.txtEmpty": "This field is required", "SSE.Views.FormatRulesEditDlg.txtFraction": "Фракция", "SSE.Views.FormatRulesEditDlg.txtGeneral": "General", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "No icon", "SSE.Views.FormatRulesEditDlg.txtNumber": "Number", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Percentage", "SSE.Views.FormatRulesEditDlg.txtScientific": "Scientific", "SSE.Views.FormatRulesEditDlg.txtText": "Text", "SSE.Views.FormatRulesEditDlg.txtTime": "Time", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Edit formatting rule", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "New formatting rule", "SSE.Views.FormatRulesManagerDlg.guestText": "Гост", "SSE.Views.FormatRulesManagerDlg.lockText": "Locked", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 std dev above average", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 std dev below average", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 std dev above average", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 std dev below average", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 std dev above average", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 std dev below average", "SSE.Views.FormatRulesManagerDlg.textAbove": "Above average", "SSE.Views.FormatRulesManagerDlg.textApply": "Apply to", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Cell value begins with", "SSE.Views.FormatRulesManagerDlg.textBelow": "Below average", "SSE.Views.FormatRulesManagerDlg.textBetween": "is between {0} and {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Cell value", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Graded color scale", "SSE.Views.FormatRulesManagerDlg.textContains": "Cell value contains", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Cell contains a blank value", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Cell contains an error", "SSE.Views.FormatRulesManagerDlg.textDelete": "Изтрий", "SSE.Views.FormatRulesManagerDlg.textDown": "Move rule down", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Duplicate values", "SSE.Views.FormatRulesManagerDlg.textEdit": "Edit", "SSE.Views.FormatRulesManagerDlg.textEnds": "Cell value ends with", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Equal to or above average", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Equal to or below average", "SSE.Views.FormatRulesManagerDlg.textFormat": "Формат", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Icon set", "SSE.Views.FormatRulesManagerDlg.textNew": "New", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "is not between {0} and {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Cell value does not contain", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Cell does not contain a blank value", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Cell does not contain an error", "SSE.Views.FormatRulesManagerDlg.textRules": "Rules", "SSE.Views.FormatRulesManagerDlg.textScope": "Show formatting rules for", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Select data", "SSE.Views.FormatRulesManagerDlg.textSelection": "Current selection", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "This pivot", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "This worksheet", "SSE.Views.FormatRulesManagerDlg.textThisTable": "This table", "SSE.Views.FormatRulesManagerDlg.textUnique": "Unique values", "SSE.Views.FormatRulesManagerDlg.textUp": "Move rule up", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Conditional formatting", "SSE.Views.FormatSettingsDialog.textCategory": "Категория", "SSE.Views.FormatSettingsDialog.textDecimal": "Десетичен", "SSE.Views.FormatSettingsDialog.textFormat": "Формат", "SSE.Views.FormatSettingsDialog.textLinked": "Linked to source", "SSE.Views.FormatSettingsDialog.textSeparator": "Използвайте 1000 разделител", "SSE.Views.FormatSettingsDialog.textSymbols": "Символи", "SSE.Views.FormatSettingsDialog.textTitle": "Формат на номера", "SSE.Views.FormatSettingsDialog.txtAccounting": "Счетоводство", "SSE.Views.FormatSettingsDialog.txtAs10": "Като десети (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "Като стотни (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "Като шестнайсети (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Като половинки (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "Като четвърти (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Като осми (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Валута", "SSE.Views.FormatSettingsDialog.txtCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Please enter the custom number format carefully. Spreadsheet Editor does not check custom formats for errors that may affect the xlsx file.", "SSE.Views.FormatSettingsDialog.txtDate": "Дата", "SSE.Views.FormatSettingsDialog.txtFraction": "Фракция", "SSE.Views.FormatSettingsDialog.txtGeneral": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNone": "Нито един", "SSE.Views.FormatSettingsDialog.txtNumber": "Номер", "SSE.Views.FormatSettingsDialog.txtPercentage": "Процент", "SSE.Views.FormatSettingsDialog.txtSample": "Извадка:", "SSE.Views.FormatSettingsDialog.txtScientific": "Научен", "SSE.Views.FormatSettingsDialog.txtText": "Текст", "SSE.Views.FormatSettingsDialog.txtTime": "Път", "SSE.Views.FormatSettingsDialog.txtUpto1": "До една цифра (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "До две цифри (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "До три цифри (131/135)", "SSE.Views.FormulaDialog.sDescription": "Описание", "SSE.Views.FormulaDialog.textGroupDescription": "Изберете функционална група", "SSE.Views.FormulaDialog.textListDescription": "Изберете функция", "SSE.Views.FormulaDialog.txtRecommended": "Recommended", "SSE.Views.FormulaDialog.txtSearch": "Search", "SSE.Views.FormulaDialog.txtTitle": "Вмъкване на функция", "SSE.Views.FormulaTab.capBtnRemoveArr": "Remove Arrows", "SSE.Views.FormulaTab.capBtnTraceDep": "Trace Dependents", "SSE.Views.FormulaTab.capBtnTracePrec": "Trace Precedents", "SSE.Views.FormulaTab.textAutomatic": "Автоматичен", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Calculate current sheet", "SSE.Views.FormulaTab.textCalculateWorkbook": "Calculate workbook", "SSE.Views.FormulaTab.textManual": "Manual", "SSE.Views.FormulaTab.tipCalculate": "Calculate", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Calculate the entire workbook", "SSE.Views.FormulaTab.tipRemoveArr": "Remove the arrows drawn by Trace Precedents or Trace Dependents", "SSE.Views.FormulaTab.tipShowFormulas": "Display the formula in each cell instead of the resulting value", "SSE.Views.FormulaTab.tipTraceDep": "Show arrows that indicate which cells are affected by the value of the selected cell", "SSE.Views.FormulaTab.tipTracePrec": "Show arrows that indicate which cells affect the value of the selected cell", "SSE.Views.FormulaTab.tipWatch": "Add cells to the Watch Window list", "SSE.Views.FormulaTab.txtAdditional": "Допълнителен", "SSE.Views.FormulaTab.txtAutosum": "Autosum", "SSE.Views.FormulaTab.txtAutosumTip": "Summation", "SSE.Views.FormulaTab.txtCalculation": "Изчисление", "SSE.Views.FormulaTab.txtFormula": "Функция", "SSE.Views.FormulaTab.txtFormulaTip": "Insert function", "SSE.Views.FormulaTab.txtMore": "More functions", "SSE.Views.FormulaTab.txtRecent": "Recently used", "SSE.Views.FormulaTab.txtRemDep": "Remove Dependents Arrows", "SSE.Views.FormulaTab.txtRemPrec": "Remove Precedents Arrows", "SSE.Views.FormulaTab.txtShowFormulas": "Show Formulas", "SSE.Views.FormulaTab.txtWatch": "Watch Window", "SSE.Views.FormulaWizard.textAny": "any", "SSE.Views.FormulaWizard.textArgument": "Argument", "SSE.Views.FormulaWizard.textFunction": "Функция", "SSE.Views.FormulaWizard.textFunctionRes": "Function result", "SSE.Views.FormulaWizard.textHelp": "Help on this function", "SSE.Views.FormulaWizard.textLogical": "логичен", "SSE.Views.FormulaWizard.textNoArgs": "This function has no arguments", "SSE.Views.FormulaWizard.textNoArgsDesc": "this argument has no description", "SSE.Views.FormulaWizard.textNumber": "number", "SSE.Views.FormulaWizard.textReadMore": "Read more", "SSE.Views.FormulaWizard.textRef": "reference", "SSE.Views.FormulaWizard.textText": "text", "SSE.Views.FormulaWizard.textTitle": "Function arguments", "SSE.Views.FormulaWizard.textValue": "Formula result", "SSE.Views.GoalSeekDlg.textChangingCell": "By changing cell", "SSE.Views.GoalSeekDlg.textDataRangeError": "The formula is missing a range", "SSE.Views.GoalSeekDlg.textMustContainFormula": "The cell must contain a formula", "SSE.Views.GoalSeekDlg.textMustContainValue": "Cell must contain a value", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Formula in cell must result in a number", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Reference must be to a single cell", "SSE.Views.GoalSeekDlg.textSelectData": "Select data", "SSE.Views.GoalSeekDlg.textSetCell": "Set cell", "SSE.Views.GoalSeekDlg.textTitle": "Goal seek", "SSE.Views.GoalSeekDlg.textToValue": "To value", "SSE.Views.GoalSeekDlg.txtEmpty": "This field is required", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.GoalSeekStatusDlg.textContinue": "Continue", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Current value:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Goal seeking with cell {0} found a solution.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Goal seeking with cell {0} may not have found a solution.", "SSE.Views.GoalSeekStatusDlg.textPause": "Pause", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Goal seeking with cell {0} on iteration #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "Step", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Target value:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Goal seek status", "SSE.Views.HeaderFooterDialog.textAlign": "Align with page margins", "SSE.Views.HeaderFooterDialog.textAll": "All pages", "SSE.Views.HeaderFooterDialog.textBold": "Bold", "SSE.Views.HeaderFooterDialog.textCenter": "Center", "SSE.Views.HeaderFooterDialog.textColor": "Цвят на текста", "SSE.Views.HeaderFooterDialog.textDate": "Date", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Different first page", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Различни четни и нечетни страници", "SSE.Views.HeaderFooterDialog.textEven": "Дори страница", "SSE.Views.HeaderFooterDialog.textFileName": "File name", "SSE.Views.HeaderFooterDialog.textFirst": "First page", "SSE.Views.HeaderFooterDialog.textFooter": "Footer", "SSE.Views.HeaderFooterDialog.textHeader": "Header", "SSE.Views.HeaderFooterDialog.textImage": "Picture", "SSE.Views.HeaderFooterDialog.textInsert": "Вмъкни", "SSE.Views.HeaderFooterDialog.textItalic": "Italic", "SSE.Views.HeaderFooterDialog.textLeft": "Left", "SSE.Views.HeaderFooterDialog.textMaxError": "The text string you entered is too long. Reduce the number of characters used.", "SSE.Views.HeaderFooterDialog.textNewColor": "Нов Потребителски Цвят", "SSE.Views.HeaderFooterDialog.textOdd": "Odd page", "SSE.Views.HeaderFooterDialog.textPageCount": "Page count", "SSE.Views.HeaderFooterDialog.textPageNum": "Page number", "SSE.Views.HeaderFooterDialog.textPresets": "Presets", "SSE.Views.HeaderFooterDialog.textRight": "Right", "SSE.Views.HeaderFooterDialog.textScale": "Scale with document", "SSE.Views.HeaderFooterDialog.textSheet": "Sheet name", "SSE.Views.HeaderFooterDialog.textStrikeout": "Strikethrough", "SSE.Views.HeaderFooterDialog.textSubscript": "Subscript", "SSE.Views.HeaderFooterDialog.textSuperscript": "Superscript", "SSE.Views.HeaderFooterDialog.textTime": "Time", "SSE.Views.HeaderFooterDialog.textTitle": "Header/Footer settings", "SSE.Views.HeaderFooterDialog.textUnderline": "Underline", "SSE.Views.HeaderFooterDialog.tipFontName": "Font", "SSE.Views.HeaderFooterDialog.tipFontSize": "Размер на шрифта", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Пок<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Връзка към", "SSE.Views.HyperlinkSettingsDialog.strRange": "Диа<PERSON>азон", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Copy", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Избрана област", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Въведете надпис тук", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Въведете връзката тук", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Въведете подсказка тук", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Вън<PERSON><PERSON>н линк", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Get link", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Вътрешен диапазон от данни", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "ГРЕШКА! Невалиден диапазон от клетки", "SSE.Views.HyperlinkSettingsDialog.textNames": "Defined names", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Select data", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Sheets", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Текст на екрана", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Настройки за хипервръзки", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Това поле е задължително", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Това поле трябва да е URL адрес във формат \"http://www.example.com\"", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "This field is limited to 2083 characters", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "SSE.Views.ImageSettings.strTransparency": "Opacity", "SSE.Views.ImageSettings.textAdvanced": "Показване на разширените настройки", "SSE.Views.ImageSettings.textCrop": "Изрежете", "SSE.Views.ImageSettings.textCropFill": "Напълнете", "SSE.Views.ImageSettings.textCropFit": "Регулирате", "SSE.Views.ImageSettings.textCropToShape": "Crop to shape", "SSE.Views.ImageSettings.textEdit": "Редактиране", "SSE.Views.ImageSettings.textEditObject": "Редактиране на обект", "SSE.Views.ImageSettings.textFlip": "Отразява", "SSE.Views.ImageSettings.textFromFile": "От файл ", "SSE.Views.ImageSettings.textFromStorage": "From storage", "SSE.Views.ImageSettings.textFromUrl": "От URL", "SSE.Views.ImageSettings.textHeight": "Висо<PERSON>ина", "SSE.Views.ImageSettings.textHint270": "Завъртете на 90 ° обратно на часовниковата стрелка", "SSE.Views.ImageSettings.textHint90": "Завъртете на 90 ° по посока на часовниковата стрелка", "SSE.Views.ImageSettings.textHintFlipH": "Отрязва по хоризонтала", "SSE.Views.ImageSettings.textHintFlipV": "Отрязване по вертикала", "SSE.Views.ImageSettings.textInsert": "Замяна на изображението", "SSE.Views.ImageSettings.textKeepRatio": "Постоянни пропорции", "SSE.Views.ImageSettings.textOriginalSize": "Размер по подразбиране", "SSE.Views.ImageSettings.textRecentlyUsed": "Recently used", "SSE.Views.ImageSettings.textResetCrop": "Reset crop", "SSE.Views.ImageSettings.textRotate90": "Завъртане на 90 °", "SSE.Views.ImageSettings.textRotation": "Завъртане", "SSE.Views.ImageSettings.textSize": "Размер", "SSE.Views.ImageSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Don't move or size with cells", "SSE.Views.ImageSettingsAdvanced.textAlt": "Алтернативен текст", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Описание", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Алтернативното текстово представяне на визуалната информация за обекта, което ще бъде прочетено на хората с визуални или когнитивни увреждания, за да им помогне да разберат по-добре каква информация има в изображението, формата, диаграмата или таблицата.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Заглавие", "SSE.Views.ImageSettingsAdvanced.textAngle": "Ъгъл", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Огледален", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Хоризонтално", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Move but don't size with cells", "SSE.Views.ImageSettingsAdvanced.textRotation": "Завъртане", "SSE.Views.ImageSettingsAdvanced.textSnap": "Cell snapping", "SSE.Views.ImageSettingsAdvanced.textTitle": "Изображение - Разширени настройки", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Move and size with cells", "SSE.Views.ImageSettingsAdvanced.textVertically": "Вертикално", "SSE.Views.ImportFromXmlDialog.textDestination": "Choose, where to place the data", "SSE.Views.ImportFromXmlDialog.textExist": "Existing worksheet", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ImportFromXmlDialog.textNew": "New worksheet", "SSE.Views.ImportFromXmlDialog.textSelectData": "Select data", "SSE.Views.ImportFromXmlDialog.textTitle": "Import data", "SSE.Views.ImportFromXmlDialog.txtEmpty": "This field is required", "SSE.Views.LeftMenu.ariaLeftMenu": "Left menu", "SSE.Views.LeftMenu.tipAbout": "Относно", "SSE.Views.LeftMenu.tipChat": "Чат", "SSE.Views.LeftMenu.tipComments": "Коментари", "SSE.Views.LeftMenu.tipFile": "<PERSON>а<PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "Добавки", "SSE.Views.LeftMenu.tipSearch": "Търсене", "SSE.Views.LeftMenu.tipSpellcheck": "Spell checking", "SSE.Views.LeftMenu.tipSupport": "Обратна връзка и поддръжка", "SSE.Views.LeftMenu.txtDeveloper": "РЕЖИМ ЗА ПРОГРАМИСТИ", "SSE.Views.LeftMenu.txtEditor": "Spreadsheet Editor", "SSE.Views.LeftMenu.txtLimit": "Limit access", "SSE.Views.LeftMenu.txtTrial": "ПРОВЕРКА", "SSE.Views.LeftMenu.txtTrialDev": "Trial Developer Mode", "SSE.Views.MacroDialog.textMacro": "Macro name", "SSE.Views.MacroDialog.textTitle": "Assign macro", "SSE.Views.MainSettingsPrint.okButtonText": "Запази", "SSE.Views.MainSettingsPrint.strBottom": "Отдоло", "SSE.Views.MainSettingsPrint.strLandscape": "Пей<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLeft": "Наляво", "SSE.Views.MainSettingsPrint.strMargins": "Полета", "SSE.Views.MainSettingsPrint.strPortrait": "Портрет", "SSE.Views.MainSettingsPrint.strPrint": "Печат", "SSE.Views.MainSettingsPrint.strPrintTitles": "Print titles", "SSE.Views.MainSettingsPrint.strRight": "Прав", "SSE.Views.MainSettingsPrint.strTop": "Отгоре", "SSE.Views.MainSettingsPrint.textActualSize": "Действителен размер", "SSE.Views.MainSettingsPrint.textCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.MainSettingsPrint.textCustomOptions": "Custom options", "SSE.Views.MainSettingsPrint.textFitCols": "Поставете всички колони на една страница", "SSE.Views.MainSettingsPrint.textFitPage": "Поставете лист на една страница", "SSE.Views.MainSettingsPrint.textFitRows": "Поставете всички редове на една страница", "SSE.Views.MainSettingsPrint.textPageOrientation": "Ориентация на страницата", "SSE.Views.MainSettingsPrint.textPageScaling": "Мащабиране", "SSE.Views.MainSettingsPrint.textPageSize": "Размер на страницата", "SSE.Views.MainSettingsPrint.textPrintGrid": "Печат на решетки", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Печат заглавия на редове и колони", "SSE.Views.MainSettingsPrint.textRepeat": "Repeat...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Repeat columns at left", "SSE.Views.MainSettingsPrint.textRepeatTop": "Repeat rows at top", "SSE.Views.MainSettingsPrint.textSettings": "Настройки за", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Съществуващите имена на обхвати не могат да бъдат редактирани и новите не могат да бъдат създадени <br> в момента, тъй като някои от тях се редактират.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Определено име", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Внимание", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Работна книга", "SSE.Views.NamedRangeEditDlg.textDataRange": "Диапазон на данните", "SSE.Views.NamedRangeEditDlg.textExistName": "ГРЕШКА! Обхватът с такова име вече съществува", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Името трябва да започва с буква или долна черта и не трябва да съдържа невалидни знаци.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ГРЕШКА! Невалиден диапазон от клетки", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ГРЕШКА! Този елемент се редактира от друг потребител.", "SSE.Views.NamedRangeEditDlg.textName": "Име", "SSE.Views.NamedRangeEditDlg.textReservedName": "Името, което се опитвате да използвате, вече е посочено в клетъчни формули. Моля, използвайте друго име.", "SSE.Views.NamedRangeEditDlg.textScope": "Об<PERSON><PERSON><PERSON>т", "SSE.Views.NamedRangeEditDlg.textSelectData": "Изберете данни", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Това поле е задължително", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Редактиране на името", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Ново име", "SSE.Views.NamedRangePasteDlg.textNames": "Наименувани диапазони", "SSE.Views.NamedRangePasteDlg.txtTitle": "Поставяне на името", "SSE.Views.NameManagerDlg.closeButtonText": "Затвори", "SSE.Views.NameManagerDlg.guestText": "Гост", "SSE.Views.NameManagerDlg.lockText": "Locked", "SSE.Views.NameManagerDlg.textDataRange": "Диапазон на данните", "SSE.Views.NameManagerDlg.textDelete": "Изтрий", "SSE.Views.NameManagerDlg.textEdit": "Редактиране", "SSE.Views.NameManagerDlg.textEmpty": "Все още не са създадени имена на диапазони. <br> Създайте поне един именуван диапазон и той ще се появи в това поле.", "SSE.Views.NameManagerDlg.textFilter": "<PERSON>и<PERSON><PERSON><PERSON>р", "SSE.Views.NameManagerDlg.textFilterAll": "Всички", "SSE.Views.NameManagerDlg.textFilterDefNames": "Определени имена", "SSE.Views.NameManagerDlg.textFilterSheet": "Имената, включени в листа", "SSE.Views.NameManagerDlg.textFilterTableNames": "Имената на таблиците", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Им<PERSON><PERSON>, включени в работна книга", "SSE.Views.NameManagerDlg.textNew": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textnoNames": "Няма намерени диапазони, които да отговарят на филтъра ви.", "SSE.Views.NameManagerDlg.textRanges": "Наименувани диапазони", "SSE.Views.NameManagerDlg.textScope": "Об<PERSON><PERSON><PERSON>т", "SSE.Views.NameManagerDlg.textWorkbook": "Работна книга", "SSE.Views.NameManagerDlg.tipIsLocked": "Този елемент се редактира от друг потребител.", "SSE.Views.NameManagerDlg.txtTitle": "Мениджър на имена", "SSE.Views.NameManagerDlg.warnDelete": "Are you sure you want to delete the name {0}?", "SSE.Views.PageMarginsDialog.textBottom": "Отдоло", "SSE.Views.PageMarginsDialog.textCenter": "Center on page", "SSE.Views.PageMarginsDialog.textHor": "Horizontally", "SSE.Views.PageMarginsDialog.textLeft": "Наляво", "SSE.Views.PageMarginsDialog.textRight": "Прав", "SSE.Views.PageMarginsDialog.textTitle": "Полета", "SSE.Views.PageMarginsDialog.textTop": "Отгоре", "SSE.Views.PageMarginsDialog.textVert": "Vertically", "SSE.Views.PageMarginsDialog.textWarning": "Warning", "SSE.Views.PageMarginsDialog.warnCheckMargings": "Margins are incorrect", "SSE.Views.ParagraphSettings.strLineHeight": "Интервал между редовете", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Разстояние между абзаците", "SSE.Views.ParagraphSettings.strSpacingAfter": "След", "SSE.Views.ParagraphSettings.strSpacingBefore": "Преди", "SSE.Views.ParagraphSettings.textAdvanced": "Показване на разширените настройки", "SSE.Views.ParagraphSettings.textAt": "При", "SSE.Views.ParagraphSettings.textAtLeast": "Поне", "SSE.Views.ParagraphSettings.textAuto": "Многократни", "SSE.Views.ParagraphSettings.textExact": "Точно", "SSE.Views.ParagraphSettings.txtAutoText": "Автоматичен", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Посочените раздели ще се появят в това поле", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Всички шапки", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Двойно зачертаване", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Indents", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Наляво", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Интервал между редовете", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Прав", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "After", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Before", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Special", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "By", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON>ри<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Отстъп и разположение", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Малки букви", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Spacing", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Зачеркнато", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "До<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Горен индекс", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Табуляция", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Подравняване", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Multiple", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Разстояние между знаците", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Разделът по подразбиране", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Ефекти", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Точно", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "First line", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Hanging", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Justified", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(none)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Премахване", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Премахнете всички", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Посочете", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Център", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Наляво", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Позиция на раздела", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Прав", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Параграф - Разширени настройки", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Автоматичен", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Delete", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "Duplicate", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Edit", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Formula", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Items Name", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "New", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Calculated Items in", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "equals", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "does not end with", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "contains", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "does not contain", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "between", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "not between", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "does not equal", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "is greater than", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "is greater than or equal to", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "is less than", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "is less than or equal to", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "begins with", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "does not begin with", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "ends with", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Show items for which the label:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Show items for which:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Use ? to present any single character", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Use * to present any series of character", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "and", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Label filter", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Value filter", "SSE.Views.PivotGroupDialog.textAuto": "Автоматичен", "SSE.Views.PivotGroupDialog.textBy": "By", "SSE.Views.PivotGroupDialog.textDays": "Days", "SSE.Views.PivotGroupDialog.textEnd": "Ending at", "SSE.Views.PivotGroupDialog.textError": "This field must be a numeric value", "SSE.Views.PivotGroupDialog.textGreaterError": "The end number must be greater than the start number", "SSE.Views.PivotGroupDialog.textHour": "Hours", "SSE.Views.PivotGroupDialog.textMin": "Minutes", "SSE.Views.PivotGroupDialog.textMonth": "Months", "SSE.Views.PivotGroupDialog.textNumDays": "Number of days", "SSE.Views.PivotGroupDialog.textQuart": "Quarters", "SSE.Views.PivotGroupDialog.textSec": "Seconds", "SSE.Views.PivotGroupDialog.textStart": "Starting at", "SSE.Views.PivotGroupDialog.textYear": "Years", "SSE.Views.PivotGroupDialog.txtTitle": "Grouping", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "You can use Calculated Items for basic calculations between different items within a single field", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Insert into formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "<PERSON><PERSON>", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Item name", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Items", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Read more", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Insert Calculated Item in", "SSE.Views.PivotSettings.textAdvanced": "Показване на разширените настройки", "SSE.Views.PivotSettings.textColumns": "Колони", "SSE.Views.PivotSettings.textFields": "Изберете полета", "SSE.Views.PivotSettings.textFilters": "Филтри", "SSE.Views.PivotSettings.textRows": "Редове", "SSE.Views.PivotSettings.textValues": "Стойности", "SSE.Views.PivotSettings.txtAddColumn": "Добавяне към колони", "SSE.Views.PivotSettings.txtAddFilter": "Добавяне към филтри", "SSE.Views.PivotSettings.txtAddRow": "Добавяне към редове", "SSE.Views.PivotSettings.txtAddValues": "Добавяне към стойности", "SSE.Views.PivotSettings.txtFieldSettings": "Настройки на полето", "SSE.Views.PivotSettings.txtMoveBegin": "Преместване в началото", "SSE.Views.PivotSettings.txtMoveColumn": "Преместване в колони", "SSE.Views.PivotSettings.txtMoveDown": "Премести надолу", "SSE.Views.PivotSettings.txtMoveEnd": "Преместване към край", "SSE.Views.PivotSettings.txtMoveFilter": "Преместване в филтри", "SSE.Views.PivotSettings.txtMoveRow": "Преместване в редове", "SSE.Views.PivotSettings.txtMoveUp": "Премести нагоре", "SSE.Views.PivotSettings.txtMoveValues": "Преместване към стойности", "SSE.Views.PivotSettings.txtRemove": "Премахване на полето", "SSE.Views.PivotSettingsAdvanced.strLayout": "Name and layout", "SSE.Views.PivotSettingsAdvanced.textAlt": "Alternative text", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Description", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Алтернативното текстово представяне на визуалната информация за обекта, което ще бъде прочетено на хората с визуални или когнитивни увреждания, за да им помогне да разберат по-добре каква информация има в изображението, формата, диаграмата или таблицата.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Title", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Autofit column widths on update", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Data range", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Data source", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Display fields in report filter area", "SSE.Views.PivotSettingsAdvanced.textDown": "Down, then over", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Grand totals", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Field headers", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.PivotSettingsAdvanced.textOver": "Over, then down", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Select data", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Show for columns", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Show field headers for rows and columns", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Show for rows", "SSE.Views.PivotSettingsAdvanced.textTitle": "Pivot Table - Advanced settings", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Report filter fields per column", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Report filter fields per row", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "This field is required", "SSE.Views.PivotSettingsAdvanced.txtName": "Name", "SSE.Views.PivotShowDetailDialog.textDescription": "Choose the field containing the detail you want to show:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Show Detail", "SSE.Views.PivotTable.capBlankRows": "Празни редове", "SSE.Views.PivotTable.capGrandTotals": "Големи суми", "SSE.Views.PivotTable.capLayout": "Оформление на отчета", "SSE.Views.PivotTable.capSubtotals": "Междинните суми", "SSE.Views.PivotTable.mniBottomSubtotals": "Показване на всички междинни суми в долната част на групата", "SSE.Views.PivotTable.mniInsertBlankLine": "Вмъкване на празен ред след всяка позиция", "SSE.Views.PivotTable.mniLayoutCompact": "Показване в компактен формуляр", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Не повтаряйте всички етикети на артикули", "SSE.Views.PivotTable.mniLayoutOutline": "Показване в очертана форма", "SSE.Views.PivotTable.mniLayoutRepeat": "Повторете всички етикети на елемента", "SSE.Views.PivotTable.mniLayoutTabular": "Показване в таблична форма", "SSE.Views.PivotTable.mniNoSubtotals": "Не показвайте междинни суми", "SSE.Views.PivotTable.mniOffTotals": "Изкл. за редове и колони", "SSE.Views.PivotTable.mniOnColumnsTotals": "Само за колони", "SSE.Views.PivotTable.mniOnRowsTotals": "Само за редове", "SSE.Views.PivotTable.mniOnTotals": "Включено за редове и колони", "SSE.Views.PivotTable.mniRemoveBlankLine": "Премахване на празната линия след всяка позиция", "SSE.Views.PivotTable.mniTopSubtotals": "Покажи всички междинни суми в началото на групата", "SSE.Views.PivotTable.textColBanded": "Колони с ленти", "SSE.Views.PivotTable.textColHeader": "Заглавия на колони", "SSE.Views.PivotTable.textRowBanded": "Обвързани редове", "SSE.Views.PivotTable.textRowHeader": "Заглавия на редове", "SSE.Views.PivotTable.tipCalculatedItems": "Calculated items", "SSE.Views.PivotTable.tipCreatePivot": "Вмъкване на обобщена таблица", "SSE.Views.PivotTable.tipGrandTotals": "Показване или скриване на общите суми", "SSE.Views.PivotTable.tipRefresh": "Актуализирайте информацията от източника на данни", "SSE.Views.PivotTable.tipRefreshCurrent": "Update the information from data source for the current table", "SSE.Views.PivotTable.tipSelect": "Изберете цялата върхова таблица", "SSE.Views.PivotTable.tipSubtotals": "Показване или скриване на междинни суми", "SSE.Views.PivotTable.txtCalculatedItems": "Calculated Items", "SSE.Views.PivotTable.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.PivotTable.txtCreate": "Вмъкване на таблица", "SSE.Views.PivotTable.txtExpandEntire": "Expand Entire Field", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Тъмна", "SSE.Views.PivotTable.txtGroupPivot_Light": "Светла", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Medium", "SSE.Views.PivotTable.txtPivotTable": "Pivot Table", "SSE.Views.PivotTable.txtRefresh": "Обновяване", "SSE.Views.PivotTable.txtRefreshAll": "Refresh all", "SSE.Views.PivotTable.txtSelect": "Изберете", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Pivot Table Style Dark", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Pivot Table Style Light", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Pivot Table Style Medium", "SSE.Views.PrintSettings.btnDownload": "Запазване и изтегляне", "SSE.Views.PrintSettings.btnExport": "Save & Export", "SSE.Views.PrintSettings.btnPrint": "Запазване и печат", "SSE.Views.PrintSettings.strBottom": "Отдоло", "SSE.Views.PrintSettings.strLandscape": "Пей<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strLeft": "Наляво", "SSE.Views.PrintSettings.strMargins": "Полета", "SSE.Views.PrintSettings.strPortrait": "Портрет", "SSE.Views.PrintSettings.strPrint": "Печат", "SSE.Views.PrintSettings.strPrintTitles": "Print titles", "SSE.Views.PrintSettings.strRight": "Прав", "SSE.Views.PrintSettings.strShow": "Пока<PERSON>и", "SSE.Views.PrintSettings.strTop": "Отгоре", "SSE.Views.PrintSettings.textActiveSheets": "Active sheets", "SSE.Views.PrintSettings.textActualSize": "Действителен размер", "SSE.Views.PrintSettings.textAllSheets": "Всички листове", "SSE.Views.PrintSettings.textCurrentSheet": "Текущ лист", "SSE.Views.PrintSettings.textCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.PrintSettings.textCustomOptions": "Custom options", "SSE.Views.PrintSettings.textFitCols": "Поставете всички колони на една страница", "SSE.Views.PrintSettings.textFitPage": "Поставете лист на една страница", "SSE.Views.PrintSettings.textFitRows": "Поставете всички редове на една страница", "SSE.Views.PrintSettings.textHideDetails": "Скриване на подробности", "SSE.Views.PrintSettings.textIgnore": "Игнорирай областта за печат", "SSE.Views.PrintSettings.textLayout": "Оформление", "SSE.Views.PrintSettings.textMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintSettings.textMarginsNormal": "Normal", "SSE.Views.PrintSettings.textMarginsWide": "Wide", "SSE.Views.PrintSettings.textPageOrientation": "Ориентация на страницата", "SSE.Views.PrintSettings.textPages": "Pages:", "SSE.Views.PrintSettings.textPageScaling": "Мащабиране", "SSE.Views.PrintSettings.textPageSize": "Размер на страницата", "SSE.Views.PrintSettings.textPrintGrid": "Печат на решетки", "SSE.Views.PrintSettings.textPrintHeadings": "Печат заглавия на редове и колони", "SSE.Views.PrintSettings.textPrintRange": "Диапазон на печат", "SSE.Views.PrintSettings.textRange": "Диа<PERSON>азон", "SSE.Views.PrintSettings.textRepeat": "Repeat...", "SSE.Views.PrintSettings.textRepeatLeft": "Repeat columns at left", "SSE.Views.PrintSettings.textRepeatTop": "Repeat rows at top", "SSE.Views.PrintSettings.textSelection": "Селекция", "SSE.Views.PrintSettings.textSettings": "Настройки на листа", "SSE.Views.PrintSettings.textShowDetails": "Покажи детайли", "SSE.Views.PrintSettings.textShowGrid": "Показване на мрежи", "SSE.Views.PrintSettings.textShowHeadings": "Показване на заглавията на редове и колони", "SSE.Views.PrintSettings.textTitle": "Настройки за печат", "SSE.Views.PrintSettings.textTitlePDF": "Настройки на PDF", "SSE.Views.PrintSettings.textTo": "to", "SSE.Views.PrintSettings.txtMarginsLast": "Last Custom", "SSE.Views.PrintTitlesDialog.textFirstCol": "First column", "SSE.Views.PrintTitlesDialog.textFirstRow": "First row", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Frozen columns", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Frozen rows", "SSE.Views.PrintTitlesDialog.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.PrintTitlesDialog.textLeft": "Repeat columns at left", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Don't repeat", "SSE.Views.PrintTitlesDialog.textRepeat": "Repeat...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Select range", "SSE.Views.PrintTitlesDialog.textTitle": "Print titles", "SSE.Views.PrintTitlesDialog.textTop": "Repeat rows at top", "SSE.Views.PrintWithPreview.txtActiveSheets": "Active sheets", "SSE.Views.PrintWithPreview.txtActualSize": "Actual size", "SSE.Views.PrintWithPreview.txtAllSheets": "All sheets", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Apply to all sheets", "SSE.Views.PrintWithPreview.txtBothSides": "Print on both sides", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "Flip pages on long edge", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "Flip pages on short edge", "SSE.Views.PrintWithPreview.txtBottom": "Bottom", "SSE.Views.PrintWithPreview.txtCopies": "Copies", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Current sheet", "SSE.Views.PrintWithPreview.txtCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.PrintWithPreview.txtCustomOptions": "Custom options", "SSE.Views.PrintWithPreview.txtEmptyTable": "There is nothing to print because the table is empty", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "First page number:", "SSE.Views.PrintWithPreview.txtFitCols": "Fit All Columns on One Page", "SSE.Views.PrintWithPreview.txtFitPage": "<PERSON>t Sheet on One Page", "SSE.Views.PrintWithPreview.txtFitRows": "Fit All Rows on One Page", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Gridlines and headings", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Header/footer settings", "SSE.Views.PrintWithPreview.txtIgnore": "Ignore print area", "SSE.Views.PrintWithPreview.txtLandscape": "Landscape", "SSE.Views.PrintWithPreview.txtLeft": "Left", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMarginsLast": "Last Custom", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Normal", "SSE.Views.PrintWithPreview.txtMarginsWide": "Wide", "SSE.Views.PrintWithPreview.txtOf": "of {0}", "SSE.Views.PrintWithPreview.txtOneSide": "Print one sided", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "SSE.Views.PrintWithPreview.txtPage": "Page", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Page number invalid", "SSE.Views.PrintWithPreview.txtPageOrientation": "Ориентация на страницата", "SSE.Views.PrintWithPreview.txtPages": "Pages:", "SSE.Views.PrintWithPreview.txtPageSize": "Размер на страницата", "SSE.Views.PrintWithPreview.txtPortrait": "Portrait", "SSE.Views.PrintWithPreview.txtPrint": "Print", "SSE.Views.PrintWithPreview.txtPrintGrid": "Print gridlines", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Print row and column headings", "SSE.Views.PrintWithPreview.txtPrintRange": "Print range", "SSE.Views.PrintWithPreview.txtPrintSides": "Print sides", "SSE.Views.PrintWithPreview.txtPrintTitles": "Print titles", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Print to PDF", "SSE.Views.PrintWithPreview.txtRepeat": "Repeat...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Repeat columns at left", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Repeat rows at top", "SSE.Views.PrintWithPreview.txtRight": "Right", "SSE.Views.PrintWithPreview.txtSave": "Save", "SSE.Views.PrintWithPreview.txtScaling": "Sc<PERSON>", "SSE.Views.PrintWithPreview.txtSelection": "Selection", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Settings of sheet", "SSE.Views.PrintWithPreview.txtSheet": "Sheet: {0}", "SSE.Views.PrintWithPreview.txtTo": "to", "SSE.Views.PrintWithPreview.txtTop": "Top", "SSE.Views.ProtectDialog.textExistName": "ERROR! Range with such a title already exists", "SSE.Views.ProtectDialog.textInvalidName": "The range title must begin with a letter and may only contain letters, numbers, and spaces.", "SSE.Views.ProtectDialog.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.ProtectDialog.textSelectData": "Select data", "SSE.Views.ProtectDialog.txtAllow": "Allow all users of this sheet to", "SSE.Views.ProtectDialog.txtAllowDescription": "You can unlock specific ranges for editing.", "SSE.Views.ProtectDialog.txtAllowRanges": "Allow edit ranges", "SSE.Views.ProtectDialog.txtAutofilter": "Use AutoFilter", "SSE.Views.ProtectDialog.txtDelCols": "Delete columns", "SSE.Views.ProtectDialog.txtDelRows": "Delete rows", "SSE.Views.ProtectDialog.txtEmpty": "This field is required", "SSE.Views.ProtectDialog.txtFormatCells": "Format cells", "SSE.Views.ProtectDialog.txtFormatCols": "Format columns", "SSE.Views.ProtectDialog.txtFormatRows": "Format rows", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Confirmation password is not identical", "SSE.Views.ProtectDialog.txtInsCols": "Insert columns", "SSE.Views.ProtectDialog.txtInsHyper": "Insert hyperlink", "SSE.Views.ProtectDialog.txtInsRows": "Insert rows", "SSE.Views.ProtectDialog.txtObjs": "Edit objects", "SSE.Views.ProtectDialog.txtOptional": "optional", "SSE.Views.ProtectDialog.txtPassword": "Password", "SSE.Views.ProtectDialog.txtPivot": "Use PivotTable and PivotChart", "SSE.Views.ProtectDialog.txtProtect": "Protect", "SSE.Views.ProtectDialog.txtRange": "Range", "SSE.Views.ProtectDialog.txtRangeName": "Title", "SSE.Views.ProtectDialog.txtRepeat": "Repeat password", "SSE.Views.ProtectDialog.txtScen": "Edit scenarios", "SSE.Views.ProtectDialog.txtSelLocked": "Select locked cells", "SSE.Views.ProtectDialog.txtSelUnLocked": "Select unlocked cells", "SSE.Views.ProtectDialog.txtSheetDescription": "Prevent unwanted changes from others by limiting their ability to edit.", "SSE.Views.ProtectDialog.txtSheetTitle": "Protect sheet", "SSE.Views.ProtectDialog.txtSort": "Sort", "SSE.Views.ProtectDialog.txtWarning": "Warning: If you lose or forget the password, it cannot be recovered. Please keep it in a safe place.", "SSE.Views.ProtectDialog.txtWBDescription": "To prevent other users from viewing hidden worksheets, adding, moving, deleting, or hiding worksheets and renaming worksheets, you can protect the structure of your workbook with a password.", "SSE.Views.ProtectDialog.txtWBTitle": "Protect workbook structure", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "Anonymous", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Anyone", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Edit", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Denied", "SSE.Views.ProtectedRangesEditDlg.textCanView": "View", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "The range title must begin with a letter and may only contain letters, numbers, and spaces.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Remove", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "Select data", "SSE.Views.ProtectedRangesEditDlg.textYou": "you", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Access to range", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "This field is required", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "Protect", "SSE.Views.ProtectedRangesEditDlg.txtRange": "Range", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "Title", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Only you can edit this range", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Start typing name or email", "SSE.Views.ProtectedRangesManagerDlg.guestText": "Guest", "SSE.Views.ProtectedRangesManagerDlg.lockText": "Locked", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "Delete", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "Edit", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "No protected ranges have been created yet.<br>Create at least one protected range and it will appear in this field.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "Filter", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "All", "SSE.Views.ProtectedRangesManagerDlg.textNew": "New", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Protect sheet", "SSE.Views.ProtectedRangesManagerDlg.textRange": "Range", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "You can restrict editing or viewing ranges to selected people.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "Title", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Access", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Denied", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "Edit", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "Edit range", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "New range", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Protected ranges", "SSE.Views.ProtectedRangesManagerDlg.txtView": "View", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Are you sure you want to delete the protected range {0}?<br>Anyone who has edit access to the spreadsheet will be able to edit content in the range.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Are you sure you want to delete the protected ranges?<br>Anyone who has edit access to the spreadsheet will be able to edit content in those ranges.", "SSE.Views.ProtectRangesDlg.guestText": "Гост", "SSE.Views.ProtectRangesDlg.lockText": "Locked", "SSE.Views.ProtectRangesDlg.textDelete": "Изтрий", "SSE.Views.ProtectRangesDlg.textEdit": "Edit", "SSE.Views.ProtectRangesDlg.textEmpty": "No ranges allowed for edit.", "SSE.Views.ProtectRangesDlg.textNew": "New", "SSE.Views.ProtectRangesDlg.textProtect": "Protect sheet", "SSE.Views.ProtectRangesDlg.textPwd": "Password", "SSE.Views.ProtectRangesDlg.textRange": "Range", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Ranges unlocked by a password when sheet is protected (this works only for locked cells)", "SSE.Views.ProtectRangesDlg.textTitle": "Title", "SSE.Views.ProtectRangesDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Edit range", "SSE.Views.ProtectRangesDlg.txtNewRange": "New range", "SSE.Views.ProtectRangesDlg.txtNo": "No", "SSE.Views.ProtectRangesDlg.txtTitle": "Allow users to edit ranges", "SSE.Views.ProtectRangesDlg.txtYes": "Yes", "SSE.Views.ProtectRangesDlg.warnDelete": "Are you sure you want to delete the name {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Columns", "SSE.Views.RemoveDuplicatesDialog.textDescription": "To delete duplicate values, select one or more columns that contain duplicates.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "My data has headers", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Select all", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Remove duplicates", "SSE.Views.RightMenu.ariaRightMenu": "Right menu", "SSE.Views.RightMenu.txtCellSettings": "Настройки на клетката", "SSE.Views.RightMenu.txtChartSettings": "Настройки на диаграмата", "SSE.Views.RightMenu.txtImageSettings": "Настройки на изображението", "SSE.Views.RightMenu.txtParagraphSettings": "Настройки за текст", "SSE.Views.RightMenu.txtPivotSettings": "Настройки на обобщените таблици", "SSE.Views.RightMenu.txtSettings": "Общи настройки", "SSE.Views.RightMenu.txtShapeSettings": "Настройки на формата", "SSE.Views.RightMenu.txtSignatureSettings": "Настройки за подпис", "SSE.Views.RightMenu.txtSlicerSettings": "Slicer settings", "SSE.Views.RightMenu.txtSparklineSettings": "Настройки за спарклайн", "SSE.Views.RightMenu.txtTableSettings": "Настройки на таблицата", "SSE.Views.RightMenu.txtTextArtSettings": "Настройки за текстово изкуство", "SSE.Views.ScaleDialog.textAuto": "Автоматичен", "SSE.Views.ScaleDialog.textError": "The entered value is incorrect.", "SSE.Views.ScaleDialog.textFewPages": "pages", "SSE.Views.ScaleDialog.textFitTo": "Fit to", "SSE.Views.ScaleDialog.textHeight": "Висо<PERSON>ина", "SSE.Views.ScaleDialog.textManyPages": "pages", "SSE.Views.ScaleDialog.textOnePage": "page", "SSE.Views.ScaleDialog.textScaleTo": "Scale to", "SSE.Views.ScaleDialog.textTitle": "Scale settings", "SSE.Views.ScaleDialog.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "Максималната стойност за това поле е {0}", "SSE.Views.SetValueDialog.txtMinText": "Минималната стойност за това поле е {0}", "SSE.Views.ShapeSettings.strBackground": "Цвят на фона", "SSE.Views.ShapeSettings.strChange": "Промяна на форма", "SSE.Views.ShapeSettings.strColor": "Цвят", "SSE.Views.ShapeSettings.strFill": "Напълнете", "SSE.Views.ShapeSettings.strForeground": "Цвят на преден план", "SSE.Views.ShapeSettings.strPattern": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strShadow": "Show shadow", "SSE.Views.ShapeSettings.strSize": "Размер", "SSE.Views.ShapeSettings.strStroke": "Удар", "SSE.Views.ShapeSettings.strTransparency": "Непрозрачност", "SSE.Views.ShapeSettings.strType": "Тип", "SSE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "SSE.Views.ShapeSettings.textAdvanced": "Показване на разширените настройки", "SSE.Views.ShapeSettings.textAngle": "Ъгъл", "SSE.Views.ShapeSettings.textBorderSizeErr": "Въведената стойност е неправилна. <br> Въведете стойност между 0 pt и 1584 pt.", "SSE.Views.ShapeSettings.textColor": "Цветово пълнене", "SSE.Views.ShapeSettings.textDirection": "Посока", "SSE.Views.ShapeSettings.textEditPoints": "Edit points", "SSE.Views.ShapeSettings.textEditShape": "Edit shape", "SSE.Views.ShapeSettings.textEmptyPattern": "Без модел", "SSE.Views.ShapeSettings.textEyedropper": "Eyedropper", "SSE.Views.ShapeSettings.textFlip": "Отразява", "SSE.Views.ShapeSettings.textFromFile": "От файл ", "SSE.Views.ShapeSettings.textFromStorage": "From storage", "SSE.Views.ShapeSettings.textFromUrl": "От URL", "SSE.Views.ShapeSettings.textGradient": "Град<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textGradientFill": "Градиентно запълване", "SSE.Views.ShapeSettings.textHint270": "Завъртете на 90 ° обратно на часовниковата стрелка", "SSE.Views.ShapeSettings.textHint90": "Завъртете на 90 ° по посока на часовниковата стрелка", "SSE.Views.ShapeSettings.textHintFlipH": "Отрязва по хоризонтала", "SSE.Views.ShapeSettings.textHintFlipV": "Отрязване по вертикала", "SSE.Views.ShapeSettings.textImageTexture": "Картина или текстура", "SSE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textMoreColors": "More colors", "SSE.Views.ShapeSettings.textNoFill": "Без попълване", "SSE.Views.ShapeSettings.textNoShadow": "No Shadow", "SSE.Views.ShapeSettings.textOriginalSize": "Оригинален размер", "SSE.Views.ShapeSettings.textPatternFill": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textPosition": "Позиция", "SSE.Views.ShapeSettings.textRadial": "Радиа<PERSON><PERSON>н", "SSE.Views.ShapeSettings.textRecentlyUsed": "Recently used", "SSE.Views.ShapeSettings.textRotate90": "Завъртане на 90 °", "SSE.Views.ShapeSettings.textRotation": "Завъртане", "SSE.Views.ShapeSettings.textSelectImage": "Select picture", "SSE.Views.ShapeSettings.textSelectTexture": "Изберете", "SSE.Views.ShapeSettings.textShadow": "Shadow", "SSE.Views.ShapeSettings.textStretch": "Разтягане", "SSE.Views.ShapeSettings.textStyle": "Стил", "SSE.Views.ShapeSettings.textTexture": "От текстура", "SSE.Views.ShapeSettings.textTile": "Плочка", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Add gradient point", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Remove gradient point", "SSE.Views.ShapeSettings.txtBrownPaper": "Кафява хартия", "SSE.Views.ShapeSettings.txtCanvas": "Платно", "SSE.Views.ShapeSettings.txtCarton": "Карто<PERSON><PERSON>на кутия", "SSE.Views.ShapeSettings.txtDarkFabric": "Тъмна тъкан", "SSE.Views.ShapeSettings.txtGrain": "Зърно", "SSE.Views.ShapeSettings.txtGranite": "<PERSON>р<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "Сива хартия", "SSE.Views.ShapeSettings.txtKnit": "Плета", "SSE.Views.ShapeSettings.txtLeather": "Кожа", "SSE.Views.ShapeSettings.txtNoBorders": "Няма линия", "SSE.Views.ShapeSettings.txtPapyrus": "Папир<PERSON>с", "SSE.Views.ShapeSettings.txtWood": "Дърво", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Колони", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Попълване на текст", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Don't move or size with cells", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Алтернативен текст", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Описание", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Алтернативното текстово представяне на визуалната информация за обекта, което ще бъде прочетено на хората с визуални или когнитивни увреждания, за да им помогне да разберат по-добре каква информация има в изображението, формата, диаграмата или таблицата.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Заглавие", "SSE.Views.ShapeSettingsAdvanced.textAngle": "Ъгъл", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Cтрелките", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "AutoFit", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Начален размер", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Започнете стила", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Откос", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Отдоло", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Тип на капачката", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Брой колони", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Размер на края", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "<PERSON><PERSON><PERSON><PERSON><PERSON> стил", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Плосък", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Огледален", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Висо<PERSON>ина", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Хоризонтално", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Тип на присъединяване", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Постоянни пропорции", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Наляво", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Стил на линията", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Митра", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Move but don't size with cells", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Allow text to overflow shape", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Resize shape to fit text", "SSE.Views.ShapeSettingsAdvanced.textRight": "Прав", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Завъртане", "SSE.Views.ShapeSettingsAdvanced.textRound": "Кръгъл", "SSE.Views.ShapeSettingsAdvanced.textSize": "Размер", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Cell snapping", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Разстояние между колоните", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Ква<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Text box", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Форма - Разширени настройки", "SSE.Views.ShapeSettingsAdvanced.textTop": "Отгоре", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Move and size with cells", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Вертикално", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Теглилки и стрелки", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Внимание", "SSE.Views.SignatureSettings.strDelete": "Премахване на подпис", "SSE.Views.SignatureSettings.strDetails": "Подробности за подпис", "SSE.Views.SignatureSettings.strInvalid": "Невалидни подписи", "SSE.Views.SignatureSettings.strRequested": "Искани подписи", "SSE.Views.SignatureSettings.strSetup": "Настройка на подпис", "SSE.Views.SignatureSettings.strSign": "Знак", "SSE.Views.SignatureSettings.strSignature": "Под<PERSON>ис", "SSE.Views.SignatureSettings.strSigner": "Подписал", "SSE.Views.SignatureSettings.strValid": "Валидни подписи", "SSE.Views.SignatureSettings.txtContinueEditing": "Все пак редактирайте", "SSE.Views.SignatureSettings.txtEditWarning": "Редактирането ще премахне подписите от електронната таблица. <br> Наистина ли искате да продължите?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Do you want to remove this signature?<br>It can't be undone.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Тази електронна таблица трябва да бъде подписана.", "SSE.Views.SignatureSettings.txtSigned": "В електронната таблица са добавени валидни подписи. Електронната таблица е защитена от редактиране.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Някои от цифровите подписи в електронната таблица са невалидни или не можаха да бъдат потвърдени. Електронната таблица е защитена от редактиране.", "SSE.Views.SlicerAddDialog.textColumns": "Columns", "SSE.Views.SlicerAddDialog.txtTitle": "Insert slicers", "SSE.Views.SlicerSettings.strHideNoData": "Hide items with no data", "SSE.Views.SlicerSettings.strIndNoData": "Visually indicate items with no data", "SSE.Views.SlicerSettings.strShowDel": "Show items deleted from the data source", "SSE.Views.SlicerSettings.strShowNoData": "Show items with no data last", "SSE.Views.SlicerSettings.strSorting": "Sorting and filtering", "SSE.Views.SlicerSettings.textAdvanced": "Show advanced settings", "SSE.Views.SlicerSettings.textAsc": "Възходящ", "SSE.Views.SlicerSettings.textAZ": "A to Z", "SSE.Views.SlicerSettings.textButtons": "Buttons", "SSE.Views.SlicerSettings.textColumns": "Columns", "SSE.Views.SlicerSettings.textDesc": "Descending", "SSE.Views.SlicerSettings.textHeight": "Висо<PERSON>ина", "SSE.Views.SlicerSettings.textHor": "Horizontal", "SSE.Views.SlicerSettings.textKeepRatio": "Constant Proportions", "SSE.Views.SlicerSettings.textLargeSmall": "largest to smallest", "SSE.Views.SlicerSettings.textLock": "Disable resizing or moving", "SSE.Views.SlicerSettings.textNewOld": "newest to oldest", "SSE.Views.SlicerSettings.textOldNew": "oldest to newest", "SSE.Views.SlicerSettings.textPosition": "Позиция", "SSE.Views.SlicerSettings.textSize": "Size", "SSE.Views.SlicerSettings.textSmallLarge": "smallest to largest", "SSE.Views.SlicerSettings.textStyle": "Style", "SSE.Views.SlicerSettings.textVert": "Vertical", "SSE.Views.SlicerSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "Z to A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Buttons", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Columns", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Висо<PERSON>ина", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Hide items with no data", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Visually indicate items with no data", "SSE.Views.SlicerSettingsAdvanced.strReferences": "References", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Show items deleted from the data source", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Display header", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Show items with no data last", "SSE.Views.SlicerSettingsAdvanced.strSize": "Size", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Sorting & Filtering", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Style", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Style & Size", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Don't move or size with cells", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Alternative text", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Description", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Алтернативното текстово представяне на визуалната информация за обекта, което ще бъде прочетено на хората с визуални или когнитивни увреждания, за да им помогне да разберат по-добре каква информация има в изображението, формата, диаграмата или таблицата.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Title", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Възходящ", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A to Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Descending", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Name to use in formulas", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Header", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Constant proportions", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "largest to smallest", "SSE.Views.SlicerSettingsAdvanced.textName": "Name", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "newest to oldest", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "oldest to newest", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Move but don't size with cells", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "smallest to largest", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Cell snapping", "SSE.Views.SlicerSettingsAdvanced.textSort": "Sort", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Source name", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Slicer - Advanced settings", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Move and size with cells", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z to A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "This field is required", "SSE.Views.SortDialog.errorEmpty": "All sort criteria must have a column or row specified.", "SSE.Views.SortDialog.errorMoreOneCol": "More than one column is selected.", "SSE.Views.SortDialog.errorMoreOneRow": "More than one row is selected.", "SSE.Views.SortDialog.errorNotOriginalCol": "The column you selected is not in the original selected range.", "SSE.Views.SortDialog.errorNotOriginalRow": "The row you selected is not in the original selected range.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 is being sorted by the same color more than once.<br>Delete the duplicate sort criteria and try again.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 is being sorted by values more than once.<br>Delete the duplicate sort criteria and try again.", "SSE.Views.SortDialog.textAsc": "Възходящ", "SSE.Views.SortDialog.textAuto": "Автоматичен", "SSE.Views.SortDialog.textAZ": "A to Z", "SSE.Views.SortDialog.textBelow": "Below", "SSE.Views.SortDialog.textBtnCopy": "Copy", "SSE.Views.SortDialog.textBtnDelete": "Delete", "SSE.Views.SortDialog.textBtnNew": "New", "SSE.Views.SortDialog.textCellColor": "Cell color", "SSE.Views.SortDialog.textColumn": "Column", "SSE.Views.SortDialog.textDesc": "Descending", "SSE.Views.SortDialog.textDown": "Move level down", "SSE.Views.SortDialog.textFontColor": "Цвят на шрифта", "SSE.Views.SortDialog.textLeft": "Left", "SSE.Views.SortDialog.textLevels": "Levels", "SSE.Views.SortDialog.textMoreCols": "(More columns...)", "SSE.Views.SortDialog.textMoreRows": "(More rows...)", "SSE.Views.SortDialog.textNone": "None", "SSE.Views.SortDialog.textOptions": "Options", "SSE.Views.SortDialog.textOrder": "Order", "SSE.Views.SortDialog.textRight": "Right", "SSE.Views.SortDialog.textRow": "Row", "SSE.Views.SortDialog.textSort": "Sort on", "SSE.Views.SortDialog.textSortBy": "Sort by", "SSE.Views.SortDialog.textThenBy": "Then by", "SSE.Views.SortDialog.textTop": "Top", "SSE.Views.SortDialog.textUp": "Move level up", "SSE.Views.SortDialog.textValues": "Values", "SSE.Views.SortDialog.textZA": "Z to A", "SSE.Views.SortDialog.txtInvalidRange": "Invalid cells range.", "SSE.Views.SortDialog.txtTitle": "Sort", "SSE.Views.SortFilterDialog.textAsc": "Ascending (A to Z) by", "SSE.Views.SortFilterDialog.textDesc": "Descending (Z to A) by", "SSE.Views.SortFilterDialog.textNoSort": "No sort", "SSE.Views.SortFilterDialog.txtTitle": "Sort", "SSE.Views.SortFilterDialog.txtTitleValue": "Sort by value", "SSE.Views.SortOptionsDialog.textCase": "Case sensitive", "SSE.Views.SortOptionsDialog.textHeaders": "My data has headers", "SSE.Views.SortOptionsDialog.textLeftRight": "Sort left to right", "SSE.Views.SortOptionsDialog.textOrientation": "Orientation", "SSE.Views.SortOptionsDialog.textTitle": "Sort options", "SSE.Views.SortOptionsDialog.textTopBottom": "Sort top to bottom", "SSE.Views.SpecialPasteDialog.textAdd": "Add", "SSE.Views.SpecialPasteDialog.textAll": "All", "SSE.Views.SpecialPasteDialog.textBlanks": "Skip blanks", "SSE.Views.SpecialPasteDialog.textColWidth": "Column widths", "SSE.Views.SpecialPasteDialog.textComments": "Comments", "SSE.Views.SpecialPasteDialog.textDiv": "Divide", "SSE.Views.SpecialPasteDialog.textFFormat": "Formulas & formatting", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formulas & number formats", "SSE.Views.SpecialPasteDialog.textFormats": "Формати", "SSE.Views.SpecialPasteDialog.textFormulas": "Формули", "SSE.Views.SpecialPasteDialog.textFWidth": "Formulas & column widths", "SSE.Views.SpecialPasteDialog.textMult": "Multiply", "SSE.Views.SpecialPasteDialog.textNone": "None", "SSE.Views.SpecialPasteDialog.textOperation": "Operation", "SSE.Views.SpecialPasteDialog.textPaste": "Paste", "SSE.Views.SpecialPasteDialog.textSub": "Subtract", "SSE.Views.SpecialPasteDialog.textTitle": "Paste special", "SSE.Views.SpecialPasteDialog.textTranspose": "Transpose", "SSE.Views.SpecialPasteDialog.textValues": "Values", "SSE.Views.SpecialPasteDialog.textVFormat": "Values & Formatting", "SSE.Views.SpecialPasteDialog.textVNFormat": "Values & Number formats", "SSE.Views.SpecialPasteDialog.textWBorders": "All except borders", "SSE.Views.Spellcheck.noSuggestions": "No spelling suggestions", "SSE.Views.Spellcheck.textChange": "Change", "SSE.Views.Spellcheck.textChangeAll": "Change all", "SSE.Views.Spellcheck.textIgnore": "Ignore", "SSE.Views.Spellcheck.textIgnoreAll": "Ignore all", "SSE.Views.Spellcheck.txtAddToDictionary": "Add to dictionary", "SSE.Views.Spellcheck.txtClosePanel": "Close spelling", "SSE.Views.Spellcheck.txtComplete": "Spellcheck has been completed", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Dictionary language", "SSE.Views.Spellcheck.txtNextTip": "Go to the next word", "SSE.Views.Spellcheck.txtSpelling": "Spelling", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Преместване в края)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "Create a copy", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(Create new spreadsheet)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Преместване преди лист", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Spreadsheet", "SSE.Views.Statusbar.filteredRecordsText": "{0} от {1} записа са филтрирани", "SSE.Views.Statusbar.filteredText": "Режим филтър", "SSE.Views.Statusbar.itemAverage": "Average", "SSE.Views.Statusbar.itemCount": "Count", "SSE.Views.Statusbar.itemDelete": "Изтрий", "SSE.Views.Statusbar.itemHidden": "Скрит", "SSE.Views.Statusbar.itemHide": "Скрий", "SSE.Views.Statusbar.itemInsert": "Вмъкни", "SSE.Views.Statusbar.itemMaximum": "Максимален", "SSE.Views.Statusbar.itemMinimum": "Minimum", "SSE.Views.Statusbar.itemMoveOrCopy": "Move or copy", "SSE.Views.Statusbar.itemProtect": "Protect", "SSE.Views.Statusbar.itemRename": "Преименувам", "SSE.Views.Statusbar.itemStatus": "Saving status", "SSE.Views.Statusbar.itemSum": "Sum", "SSE.Views.Statusbar.itemTabColor": "Цвят на раздела", "SSE.Views.Statusbar.itemUnProtect": "Unprotect", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Работният лист с такова име вече съществува.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Името на листа не може да съдържа следните знаци: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Име на листа", "SSE.Views.Statusbar.selectAllSheets": "Select All Sheets", "SSE.Views.Statusbar.sheetIndexText": "Sheet {0} of {1}", "SSE.Views.Statusbar.textAverage": "СРЕДНО АРИТМЕТИЧНО", "SSE.Views.Statusbar.textCount": "БРОЯ", "SSE.Views.Statusbar.textMax": "MAX", "SSE.Views.Statusbar.textMin": "MIN", "SSE.Views.Statusbar.textNewColor": "Нов потребителски цвят", "SSE.Views.Statusbar.textNoColor": "Няма цвят", "SSE.Views.Statusbar.textSum": "SUM", "SSE.Views.Statusbar.tipAddTab": "Добавете работен лист", "SSE.Views.Statusbar.tipFirst": "Превъртете до първия лист", "SSE.Views.Statusbar.tipLast": "Преминете към последния лист", "SSE.Views.Statusbar.tipListOfSheets": "List of sheets", "SSE.Views.Statusbar.tipNext": "Превъртете списъка с листа право", "SSE.Views.Statusbar.tipPrev": "Отидете в списъка с листа", "SSE.Views.Statusbar.tipZoomFactor": "Мащ<PERSON>б", "SSE.Views.Statusbar.tipZoomIn": "Увеличение", "SSE.Views.Statusbar.tipZoomOut": "Отдалечавам", "SSE.Views.Statusbar.ungroupSheets": "Ungroup sheets", "SSE.Views.Statusbar.zoomText": "Мащаб {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Операцията не може да бъде извършена за избрания диапазон от клетки. <br> Изберете един и същ диапазон от данни, различен от съществуващия, и опитайте отново.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Операцията не може да бъде завършена за избрания диапазон от клетки. <br> Изберете диапазон, така че първият ред на таблицата да е в същия ред <br> и получената таблица се припокрива с текущия.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Операцията не може да бъде завършена за избрания диапазон от клетки. <br> Изберете диапазон, който не включва други таблици.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Формулирайте с множество клетки, които не са позволени в таблицата.", "SSE.Views.TableOptionsDialog.txtEmpty": "Това поле е задължително", "SSE.Views.TableOptionsDialog.txtFormat": "Създаване на таблица", "SSE.Views.TableOptionsDialog.txtInvalidRange": "ГРЕШКА! Невалиден диапазон от клетки", "SSE.Views.TableOptionsDialog.txtNote": "The headers must remain in the same row, and the resulting table range must overlap the original table range.", "SSE.Views.TableOptionsDialog.txtTitle": "Заглавие", "SSE.Views.TableSettings.deleteColumnText": "Изтриване на колона", "SSE.Views.TableSettings.deleteRowText": "Изтриване на ред", "SSE.Views.TableSettings.deleteTableText": "Изтриване на таблицата", "SSE.Views.TableSettings.insertColumnLeftText": "Вмъкване на колона вляво", "SSE.Views.TableSettings.insertColumnRightText": "Вмъкване на колона вдясно", "SSE.Views.TableSettings.insertRowAboveText": "Вмъкване на ред отгоре", "SSE.Views.TableSettings.insertRowBelowText": "Вмъкнете ред по-долу", "SSE.Views.TableSettings.notcriticalErrorTitle": "Внимание", "SSE.Views.TableSettings.selectColumnText": "Изберете цяла колона", "SSE.Views.TableSettings.selectDataText": "Изберете данни за колона", "SSE.Views.TableSettings.selectRowText": "Изберете ред", "SSE.Views.TableSettings.selectTableText": "Изберете таблица", "SSE.Views.TableSettings.textActions": "Table actions", "SSE.Views.TableSettings.textAdvanced": "Показване на разширените настройки", "SSE.Views.TableSettings.textBanded": "На ивици", "SSE.Views.TableSettings.textColumns": "Колони", "SSE.Views.TableSettings.textConvertRange": "Преобразуване в обхват", "SSE.Views.TableSettings.textEdit": "Редове и колони", "SSE.Views.TableSettings.textEmptyTemplate": "Няма шаблони", "SSE.Views.TableSettings.textExistName": "ГРЕШКА! Вече съществува диапазон с такова име", "SSE.Views.TableSettings.textFilter": "Бутон за филтриране", "SSE.Views.TableSettings.textFirst": "Първи", "SSE.Views.TableSettings.textHeader": "Заглавие", "SSE.Views.TableSettings.textInvalidName": "ГРЕШКА! Невалидно име на таблица", "SSE.Views.TableSettings.textIsLocked": "Този елемент се редактира от друг потребител.", "SSE.Views.TableSettings.textLast": "Последно", "SSE.Views.TableSettings.textLongOperation": "Дълга операция", "SSE.Views.TableSettings.textPivot": "Insert pivot table", "SSE.Views.TableSettings.textRemDuplicates": "Remove duplicates", "SSE.Views.TableSettings.textReservedName": "Името, което се опитвате да използвате, вече е посочено в клетъчни формули. Моля, използвайте друго име.", "SSE.Views.TableSettings.textResize": "Преоразмеряване на таблицата", "SSE.Views.TableSettings.textRows": "Редове", "SSE.Views.TableSettings.textSelectData": "Изберете данни", "SSE.Views.TableSettings.textSlicer": "Insert slicer", "SSE.Views.TableSettings.textTableName": "Име на таблицата", "SSE.Views.TableSettings.textTemplate": "Изберете от шаблон", "SSE.Views.TableSettings.textTotal": "Обща сума", "SSE.Views.TableSettings.txtGroupTable_Custom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.TableSettings.txtGroupTable_Dark": "Тъмна", "SSE.Views.TableSettings.txtGroupTable_Light": "Светла", "SSE.Views.TableSettings.txtGroupTable_Medium": "Medium", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Table style dark", "SSE.Views.TableSettings.txtTable_TableStyleLight": "Table style light", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "Table style medium", "SSE.Views.TableSettings.warnLongOperation": "Операцията, която ще изпълните, може да отнеме доста време за изпълнение. <br> Наистина ли искате да продължите?", "SSE.Views.TableSettingsAdvanced.textAlt": "Алтернативен текст", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Описание", "SSE.Views.TableSettingsAdvanced.textAltTip": "Алтернативното текстово представяне на визуалната информация за обекта, което ще бъде прочетено на хората с визуални или когнитивни увреждания, за да им помогне да разберат по-добре каква информация има в изображението, формата, диаграмата или таблицата.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Заглавие", "SSE.Views.TableSettingsAdvanced.textTitle": "Таблица - Разширени настройки", "SSE.Views.TextArtSettings.strBackground": "Цвят на фона", "SSE.Views.TextArtSettings.strColor": "Цвят", "SSE.Views.TextArtSettings.strFill": "Напълнете", "SSE.Views.TextArtSettings.strForeground": "Цвят на преден план", "SSE.Views.TextArtSettings.strPattern": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strSize": "Размер", "SSE.Views.TextArtSettings.strStroke": "Удар", "SSE.Views.TextArtSettings.strTransparency": "Непрозрачност", "SSE.Views.TextArtSettings.strType": "Тип", "SSE.Views.TextArtSettings.textAngle": "Ъгъл", "SSE.Views.TextArtSettings.textBorderSizeErr": "Въведената стойност е неправилна. <br> Въведете стойност между 0 pt и 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Цветово пълнене", "SSE.Views.TextArtSettings.textDirection": "Посока", "SSE.Views.TextArtSettings.textEmptyPattern": "Без модел", "SSE.Views.TextArtSettings.textFromFile": "От файл ", "SSE.Views.TextArtSettings.textFromUrl": "От URL", "SSE.Views.TextArtSettings.textGradient": "Град<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textGradientFill": "Градиентно запълване", "SSE.Views.TextArtSettings.textImageTexture": "Картина или текстура", "SSE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textNoFill": "Без попълване", "SSE.Views.TextArtSettings.textPatternFill": "<PERSON>о<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textPosition": "Позиция", "SSE.Views.TextArtSettings.textRadial": "Радиа<PERSON><PERSON>н", "SSE.Views.TextArtSettings.textSelectTexture": "Изберете", "SSE.Views.TextArtSettings.textStretch": "Разтягане", "SSE.Views.TextArtSettings.textStyle": "Стил", "SSE.Views.TextArtSettings.textTemplate": "Шабл<PERSON>н", "SSE.Views.TextArtSettings.textTexture": "От текстура", "SSE.Views.TextArtSettings.textTile": "Плочка", "SSE.Views.TextArtSettings.textTransform": "Трансформирайте", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Add gradient point", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Remove gradient point", "SSE.Views.TextArtSettings.txtBrownPaper": "Кафява хартия", "SSE.Views.TextArtSettings.txtCanvas": "Платно", "SSE.Views.TextArtSettings.txtCarton": "Карто<PERSON><PERSON>на кутия", "SSE.Views.TextArtSettings.txtDarkFabric": "Тъмна тъкан", "SSE.Views.TextArtSettings.txtGrain": "Зърно", "SSE.Views.TextArtSettings.txtGranite": "<PERSON>р<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "Сива хартия", "SSE.Views.TextArtSettings.txtKnit": "Плета", "SSE.Views.TextArtSettings.txtLeather": "Кожа", "SSE.Views.TextArtSettings.txtNoBorders": "Няма линия", "SSE.Views.TextArtSettings.txtPapyrus": "Папир<PERSON>с", "SSE.Views.TextArtSettings.txtWood": "Дърво", "SSE.Views.Toolbar.capBtnAddComment": "Добави коментар", "SSE.Views.Toolbar.capBtnColorSchemas": "Цветова схема", "SSE.Views.Toolbar.capBtnComment": "Коментар", "SSE.Views.Toolbar.capBtnInsHeader": "Горен/долен колонтитул", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON>licer", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Symbol", "SSE.Views.Toolbar.capBtnMargins": "Полета", "SSE.Views.Toolbar.capBtnPageBreak": "Breaks", "SSE.Views.Toolbar.capBtnPageOrient": "Ориентация", "SSE.Views.Toolbar.capBtnPageSize": "Размер", "SSE.Views.Toolbar.capBtnPrintArea": "Площ за печат", "SSE.Views.Toolbar.capBtnPrintTitles": "Print Titles", "SSE.Views.Toolbar.capBtnScale": "Scale To Fit", "SSE.Views.Toolbar.capImgAlign": "Изравнете", "SSE.Views.Toolbar.capImgBackward": "Изпращане назад", "SSE.Views.Toolbar.capImgForward": "Изведи напред", "SSE.Views.Toolbar.capImgGroup": "Гру<PERSON>а", "SSE.Views.Toolbar.capInsertChart": "Диаграма", "SSE.Views.Toolbar.capInsertChartRecommend": "Recommended Chart", "SSE.Views.Toolbar.capInsertEquation": "Уравнение", "SSE.Views.Toolbar.capInsertHyperlink": "Хипервръзка", "SSE.Views.Toolbar.capInsertImage": "Изображение", "SSE.Views.Toolbar.capInsertShape": "Форма", "SSE.Views.Toolbar.capInsertSpark": "Sparkline", "SSE.Views.Toolbar.capInsertTable": "Таблица", "SSE.Views.Toolbar.capInsertText": "Текстово поле", "SSE.Views.Toolbar.capInsertTextart": "Text Art", "SSE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniCapitalizeWords": "Capitalize Each Word", "SSE.Views.Toolbar.mniImageFromFile": "Изображение от файл", "SSE.Views.Toolbar.mniImageFromStorage": "Изображение от хранилището", "SSE.Views.Toolbar.mniImageFromUrl": "Изображение от URL адрес", "SSE.Views.Toolbar.mniLowerCase": "lowercase", "SSE.Views.Toolbar.mniSentenceCase": "Sentence case.", "SSE.Views.Toolbar.mniToggleCase": "tOGGLE cASE", "SSE.Views.Toolbar.mniUpperCase": "UPPERCASE", "SSE.Views.Toolbar.textAddPrintArea": "Добавяне към областта за печат", "SSE.Views.Toolbar.textAlignBottom": "Подравняване отдолу", "SSE.Views.Toolbar.textAlignCenter": "Подравняване на центъра ", "SSE.Views.Toolbar.textAlignJust": "Двустранно", "SSE.Views.Toolbar.textAlignLeft": "Подравняване вляво", "SSE.Views.Toolbar.textAlignMiddle": "Подравняване на средата", "SSE.Views.Toolbar.textAlignRight": "Подравняване надясно", "SSE.Views.Toolbar.textAlignTop": "Подравняване отгоре", "SSE.Views.Toolbar.textAllBorders": "Всички граници", "SSE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "SSE.Views.Toolbar.textAuto": "Автоматичен", "SSE.Views.Toolbar.textAutoColor": "Автоматичен", "SSE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "SSE.Views.Toolbar.textBlackHeart": "Black Heart Suit", "SSE.Views.Toolbar.textBold": "Получер", "SSE.Views.Toolbar.textBordersColor": "Цвят на границата", "SSE.Views.Toolbar.textBordersStyle": "Стил на границата", "SSE.Views.Toolbar.textBottom": "Отдоло: ", "SSE.Views.Toolbar.textBottomBorders": "Долни граници", "SSE.Views.Toolbar.textBullet": "Bullet", "SSE.Views.Toolbar.textCellAlign": "Format cell alignment", "SSE.Views.Toolbar.textCenterBorders": "Вътрешни вертикални граници", "SSE.Views.Toolbar.textClearPrintArea": "Изчистване на зоната за печат", "SSE.Views.Toolbar.textClearRule": "Clear rules", "SSE.Views.Toolbar.textClockwise": "Ъгъл по часовниковата стрелка", "SSE.Views.Toolbar.textColorScales": "Color scales", "SSE.Views.Toolbar.textCopyright": "Copyright Sign", "SSE.Views.Toolbar.textCounterCw": "Ъгъл обратно на часовниковата стрелка", "SSE.Views.Toolbar.textCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.Toolbar.textDataBars": "Data Bars", "SSE.Views.Toolbar.textDegree": "Degree Sign", "SSE.Views.Toolbar.textDelLeft": "Преместване на клетките вляво", "SSE.Views.Toolbar.textDelPageBreak": "Remove page break", "SSE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "SSE.Views.Toolbar.textDelUp": "Преместете клетки нагоре", "SSE.Views.Toolbar.textDiagDownBorder": "Диа<PERSON><PERSON><PERSON>лна надолу граница", "SSE.Views.Toolbar.textDiagUpBorder": "Диагонална граница нагоре", "SSE.Views.Toolbar.textDivision": "Division Sign", "SSE.Views.Toolbar.textDollar": "Dollar Sign", "SSE.Views.Toolbar.textDone": "Done", "SSE.Views.Toolbar.textDown": "Down", "SSE.Views.Toolbar.textEditVA": "Edit Visible Area", "SSE.Views.Toolbar.textEntireCol": "Цяла колона", "SSE.Views.Toolbar.textEntireRow": "Цял ред", "SSE.Views.Toolbar.textEuro": "Euro Sign", "SSE.Views.Toolbar.textFewPages": "pages", "SSE.Views.Toolbar.textFillLeft": "Left", "SSE.Views.Toolbar.textFillRight": "Right", "SSE.Views.Toolbar.textFormatCellFill": "Format cell fill", "SSE.Views.Toolbar.textGreaterEqual": "Greater Than Or Equal To", "SSE.Views.Toolbar.textHeight": "Висо<PERSON>ина", "SSE.Views.Toolbar.textHideVA": "Hide Visible Area", "SSE.Views.Toolbar.textHorizontal": "Хоризонтален текст", "SSE.Views.Toolbar.textInfinity": "Infinity", "SSE.Views.Toolbar.textInsDown": "Преместете клетките надолу", "SSE.Views.Toolbar.textInsideBorders": "Вътрешни граници", "SSE.Views.Toolbar.textInsPageBreak": "Insert page break", "SSE.Views.Toolbar.textInsRight": "Преместете клетките надясно", "SSE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textItems": "Items", "SSE.Views.Toolbar.textLandscape": "Пей<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textLeft": "Наляво: ", "SSE.Views.Toolbar.textLeftBorders": "Ляви граници", "SSE.Views.Toolbar.textLessEqual": "Less Than Or Equal To", "SSE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "SSE.Views.Toolbar.textManageRule": "Manage rules", "SSE.Views.Toolbar.textManyPages": "pages", "SSE.Views.Toolbar.textMarginsLast": "Последно персонализирано", "SSE.Views.Toolbar.textMarginsNarrow": "Тесен", "SSE.Views.Toolbar.textMarginsNormal": "Нормален", "SSE.Views.Toolbar.textMarginsWide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMiddleBorders": "Хоризонтални граници", "SSE.Views.Toolbar.textMoreBorders": "More borders", "SSE.Views.Toolbar.textMoreFormats": "Още формати", "SSE.Views.Toolbar.textMorePages": "More pages", "SSE.Views.Toolbar.textMoreSymbols": "More symbols", "SSE.Views.Toolbar.textNewColor": "Нов потребителски цвят", "SSE.Views.Toolbar.textNewRule": "New rule", "SSE.Views.Toolbar.textNoBorders": "Няма граници", "SSE.Views.Toolbar.textNotEqualTo": "Not Equal To", "SSE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "SSE.Views.Toolbar.textOnePage": "page", "SSE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "SSE.Views.Toolbar.textOutBorders": "Външни граници", "SSE.Views.Toolbar.textPageMarginsCustom": "Персонализирани полета", "SSE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "SSE.Views.Toolbar.textPortrait": "Портрет", "SSE.Views.Toolbar.textPrint": "Печат", "SSE.Views.Toolbar.textPrintGridlines": "Print Gridlines", "SSE.Views.Toolbar.textPrintHeadings": "Print Headings", "SSE.Views.Toolbar.textPrintOptions": "Настройки за печат", "SSE.Views.Toolbar.textRegistered": "Registered Sign", "SSE.Views.Toolbar.textResetPageBreak": "Reset all page breaks", "SSE.Views.Toolbar.textRight": "Дясно: ", "SSE.Views.Toolbar.textRightBorders": "Дясна граница", "SSE.Views.Toolbar.textRotateDown": "Завъртете текста надолу", "SSE.Views.Toolbar.textRotateUp": "Завъртете текста нагоре", "SSE.Views.Toolbar.textRtlSheet": "Sheet Right-to-left", "SSE.Views.Toolbar.textScale": "Scale", "SSE.Views.Toolbar.textScaleCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.Toolbar.textSection": "Section Sign", "SSE.Views.Toolbar.textSelection": "From current selection", "SSE.Views.Toolbar.textSeries": "Series", "SSE.Views.Toolbar.textSetPrintArea": "Задайте област на печат", "SSE.Views.Toolbar.textShapesCombine": "Combine", "SSE.Views.Toolbar.textShapesFragment": "Fragment", "SSE.Views.Toolbar.textShapesIntersect": "Intersect", "SSE.Views.Toolbar.textShapesSubstract": "Subtract", "SSE.Views.Toolbar.textShapesUnion": "Union", "SSE.Views.Toolbar.textShowVA": "Show Visible Area", "SSE.Views.Toolbar.textSmile": "White Smiling Face", "SSE.Views.Toolbar.textSquareRoot": "Square Root", "SSE.Views.Toolbar.textStrikeout": "За<PERSON><PERSON><PERSON><PERSON><PERSON>т", "SSE.Views.Toolbar.textSubscript": "До<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSubSuperscript": "Долен/Горен индекс", "SSE.Views.Toolbar.textSuperscript": "Горен индекс", "SSE.Views.Toolbar.textTabCollaboration": "Сътрудничество", "SSE.Views.Toolbar.textTabData": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabDraw": "Изтегли", "SSE.Views.Toolbar.textTabFile": "досие", "SSE.Views.Toolbar.textTabFormula": "Формула", "SSE.Views.Toolbar.textTabHome": "У дома", "SSE.Views.Toolbar.textTabInsert": "Вмъкни", "SSE.Views.Toolbar.textTabLayout": "Оформление", "SSE.Views.Toolbar.textTabProtect": "Защита", "SSE.Views.Toolbar.textTabView": "Изглед", "SSE.Views.Toolbar.textThisPivot": "From this pivot", "SSE.Views.Toolbar.textThisSheet": "From this worksheet", "SSE.Views.Toolbar.textThisTable": "From this table", "SSE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTop": "Връх: ", "SSE.Views.Toolbar.textTopBorders": "Топ граници", "SSE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "SSE.Views.Toolbar.textUnderline": "Подчертавам", "SSE.Views.Toolbar.textUp": "Up", "SSE.Views.Toolbar.textVertical": "Вертикален текст", "SSE.Views.Toolbar.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textYen": "Yen Sign", "SSE.Views.Toolbar.textZoom": "Мащ<PERSON>б", "SSE.Views.Toolbar.tipAlignBottom": "Подравняване отдолу", "SSE.Views.Toolbar.tipAlignCenter": "Подравняване на центъра", "SSE.Views.Toolbar.tipAlignJust": "Двустранно", "SSE.Views.Toolbar.tipAlignLeft": "Подравняване вляво", "SSE.Views.Toolbar.tipAlignMiddle": "Подравняване на средата", "SSE.Views.Toolbar.tipAlignRight": "Подравняване надясно", "SSE.Views.Toolbar.tipAlignTop": "Подравняване отгоре", "SSE.Views.Toolbar.tipAutofilter": "Сортиране и филтриране", "SSE.Views.Toolbar.tipBack": "Обратно", "SSE.Views.Toolbar.tipBorders": "<PERSON>ра<PERSON><PERSON><PERSON>и", "SSE.Views.Toolbar.tipCellStyle": "Стил на клетката", "SSE.Views.Toolbar.tipChangeCase": "Change case", "SSE.Views.Toolbar.tipChangeChart": "Промяна на типа на диаграмата", "SSE.Views.Toolbar.tipClearStyle": "Изчисти", "SSE.Views.Toolbar.tipColorSchemas": "Промяна на цветовата схема", "SSE.Views.Toolbar.tipCondFormat": "Conditional formatting", "SSE.Views.Toolbar.tipCopy": "Копие", "SSE.Views.Toolbar.tipCopyStyle": "Копирайте стила", "SSE.Views.Toolbar.tipCut": "Cut", "SSE.Views.Toolbar.tipDecDecimal": "Намаляване на десетичната запетая", "SSE.Views.Toolbar.tipDecFont": "Размер на шрифта", "SSE.Views.Toolbar.tipDeleteOpt": "Изтриване на клетки", "SSE.Views.Toolbar.tipDigStyleAccounting": "Счетоводен стил", "SSE.Views.Toolbar.tipDigStyleComma": "Comma style", "SSE.Views.Toolbar.tipDigStyleCurrency": "Валу<PERSON><PERSON>н стил", "SSE.Views.Toolbar.tipDigStylePercent": "Процент стил", "SSE.Views.Toolbar.tipEditChart": "Редактиране на диаграма", "SSE.Views.Toolbar.tipEditChartData": "Select data", "SSE.Views.Toolbar.tipEditChartType": "Change chart type", "SSE.Views.Toolbar.tipEditHeader": "Edit header or footer", "SSE.Views.Toolbar.tipFontColor": "Цвят на шрифта", "SSE.Views.Toolbar.tipFontName": "<PERSON>ри<PERSON><PERSON>", "SSE.Views.Toolbar.tipFontSize": "Размер на шрифта", "SSE.Views.Toolbar.tipHAlighOle": "Хоризонтално подравняване", "SSE.Views.Toolbar.tipImgAlign": "Подравняване на обекти", "SSE.Views.Toolbar.tipImgGroup": "Групови обекти", "SSE.Views.Toolbar.tipIncDecimal": "Увеличаване на десетичната запетая", "SSE.Views.Toolbar.tipIncFont": "Увеливане на размера на шрифта", "SSE.Views.Toolbar.tipInsertChart": "Поставете диаграма", "SSE.Views.Toolbar.tipInsertChartRecommend": "Insert recommended chart", "SSE.Views.Toolbar.tipInsertChartSpark": "Поставете диаграма", "SSE.Views.Toolbar.tipInsertEquation": "Вмъкване на уравнение", "SSE.Views.Toolbar.tipInsertHorizontalText": "Insert horizontal text box", "SSE.Views.Toolbar.tipInsertHyperlink": "Добавете хипервръзка", "SSE.Views.Toolbar.tipInsertImage": "Вмъкване на изображение", "SSE.Views.Toolbar.tipInsertOpt": "Вмъкване на клетки", "SSE.Views.Toolbar.tipInsertShape": "Вмъкване на автоматична форма", "SSE.Views.Toolbar.tipInsertSlicer": "Insert slicer", "SSE.Views.Toolbar.tipInsertSmartArt": "Insert SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Insert sparkline", "SSE.Views.Toolbar.tipInsertSymbol": "Insert symbol", "SSE.Views.Toolbar.tipInsertTable": "Insert table", "SSE.Views.Toolbar.tipInsertText": "Вмъкни текстовото поле", "SSE.Views.Toolbar.tipInsertTextart": "Вмъкни текст Чл", "SSE.Views.Toolbar.tipInsertVerticalText": "Insert vertical text box", "SSE.Views.Toolbar.tipMerge": "Сливам", "SSE.Views.Toolbar.tipNone": "None", "SSE.Views.Toolbar.tipNumFormat": "Формат на номера", "SSE.Views.Toolbar.tipPageBreak": "Add a break where you want the next page to begin in the printed copy", "SSE.Views.Toolbar.tipPageMargins": "Полета на страницата", "SSE.Views.Toolbar.tipPageOrient": "Ориентация на страницата", "SSE.Views.Toolbar.tipPageSize": "Размер на страницата", "SSE.Views.Toolbar.tipPaste": "Паста", "SSE.Views.Toolbar.tipPrColor": "Цвят на фона", "SSE.Views.Toolbar.tipPrint": "Печат", "SSE.Views.Toolbar.tipPrintArea": "Площ за печат", "SSE.Views.Toolbar.tipPrintQuick": "Quick print", "SSE.Views.Toolbar.tipPrintTitles": "Print titles", "SSE.Views.Toolbar.tipRedo": "Повтори", "SSE.Views.Toolbar.tipReplace": "Replace", "SSE.Views.Toolbar.tipRtlSheet": "Switch the sheet direction so that the first column is on the right side", "SSE.Views.Toolbar.tipSave": "Запази", "SSE.Views.Toolbar.tipSaveCoauth": "Запазете промените си, за да ги видят другите потребители.", "SSE.Views.Toolbar.tipScale": "Scale to fit", "SSE.Views.Toolbar.tipSelectAll": "Select all", "SSE.Views.Toolbar.tipSendBackward": "Изпращане назад", "SSE.Views.Toolbar.tipSendForward": "Изведи напред", "SSE.Views.Toolbar.tipShapesMerge": "Merge shapes", "SSE.Views.Toolbar.tipSynchronize": "Документът е променен от друг потребител. Моля, кликнете, за да запазите промените си и да презаредите актуализациите.", "SSE.Views.Toolbar.tipTextFormatting": "More text formatting tools", "SSE.Views.Toolbar.tipTextOrientation": "Ориентация", "SSE.Views.Toolbar.tipUndo": "Отмени", "SSE.Views.Toolbar.tipVAlighOle": "Вертикално подравняване", "SSE.Views.Toolbar.tipVisibleArea": "Visible area", "SSE.Views.Toolbar.tipWrap": "Преливане на текст", "SSE.Views.Toolbar.txtAccounting": "Счетоводство", "SSE.Views.Toolbar.txtAdditional": "Допълнителен", "SSE.Views.Toolbar.txtAscending": "Възходящ", "SSE.Views.Toolbar.txtAutosumTip": "Summation", "SSE.Views.Toolbar.txtCellStyle": "Cell Style", "SSE.Views.Toolbar.txtClearAll": "Всички", "SSE.Views.Toolbar.txtClearComments": "Коментари", "SSE.Views.Toolbar.txtClearFilter": "Изчистване на филтъра", "SSE.Views.Toolbar.txtClearFormat": "Формат", "SSE.Views.Toolbar.txtClearFormula": "Функция", "SSE.Views.Toolbar.txtClearHyper": "Хипервръзки", "SSE.Views.Toolbar.txtClearText": "Текст", "SSE.Views.Toolbar.txtCurrency": "Валута", "SSE.Views.Toolbar.txtCustom": "Персонализ<PERSON><PERSON><PERSON>н", "SSE.Views.Toolbar.txtDate": "Дата", "SSE.Views.Toolbar.txtDateLong": "Long Date", "SSE.Views.Toolbar.txtDateShort": "Short Date", "SSE.Views.Toolbar.txtDateTime": "Време за среща", "SSE.Views.Toolbar.txtDescending": "Низходящо", "SSE.Views.Toolbar.txtDollar": "$ Долар", "SSE.Views.Toolbar.txtEuro": "€ Евро", "SSE.Views.Toolbar.txtExp": "Показателен", "SSE.Views.Toolbar.txtFillNum": "Напълнете", "SSE.Views.Toolbar.txtFilter": "<PERSON>и<PERSON><PERSON><PERSON>р", "SSE.Views.Toolbar.txtFormula": "Вмъкване на функция", "SSE.Views.Toolbar.txtFraction": "Фракция", "SSE.Views.Toolbar.txtFranc": "CHF швейцарски франк", "SSE.Views.Toolbar.txtGeneral": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtInteger": "Цяло число", "SSE.Views.Toolbar.txtManageRange": "Мениджър на имена", "SSE.Views.Toolbar.txtMergeAcross": "Обединяване", "SSE.Views.Toolbar.txtMergeCells": "Сливане на клетки", "SSE.Views.Toolbar.txtMergeCenter": "Обединяване и център", "SSE.Views.Toolbar.txtNamedRange": "Наименувани диапазони", "SSE.Views.Toolbar.txtNewRange": "Определяне на име", "SSE.Views.Toolbar.txtNoBorders": "Няма граници", "SSE.Views.Toolbar.txtNumber": "Номер", "SSE.Views.Toolbar.txtPasteRange": "Поставяне на името", "SSE.Views.Toolbar.txtPercentage": "Процент", "SSE.Views.Toolbar.txtPound": "£ Паунд", "SSE.Views.Toolbar.txtRouble": "₽ Рубли", "SSE.Views.Toolbar.txtScientific": "Научен", "SSE.Views.Toolbar.txtSearch": "Търсене", "SSE.Views.Toolbar.txtSort": "Вид", "SSE.Views.Toolbar.txtSortAZ": "Сортиране във възходящ ред", "SSE.Views.Toolbar.txtSortZA": "Сортиране по низходящ ред", "SSE.Views.Toolbar.txtSpecial": "Специален", "SSE.Views.Toolbar.txtTableTemplate": "Форматирайте като шаблон на таблица", "SSE.Views.Toolbar.txtText": "Текст", "SSE.Views.Toolbar.txtTime": "Път", "SSE.Views.Toolbar.txtUnmerge": "Изтеглете клетките", "SSE.Views.Toolbar.txtYen": "¥ Йена", "SSE.Views.Top10FilterDialog.textType": "Пока<PERSON>и", "SSE.Views.Top10FilterDialog.txtBottom": "Отдоло", "SSE.Views.Top10FilterDialog.txtBy": "by", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtPercent": "На сто", "SSE.Views.Top10FilterDialog.txtSum": "Sum", "SSE.Views.Top10FilterDialog.txtTitle": "Топ 10 на автофилтъра", "SSE.Views.Top10FilterDialog.txtTop": "Отгоре", "SSE.Views.Top10FilterDialog.txtValueTitle": "Top 10 filter", "SSE.Views.ValueFieldSettingsDialog.textNext": "(next)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(previous)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Value field settings", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Average", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Base field", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Base item", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 of %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Count", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Count numbers", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Custom name", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Difference from", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Index", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Max", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "No calculation", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "% of", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "% difference from", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "% of column", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% of grand total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% of parent total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% of parent column total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% of parent row total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% running total in", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "% of row", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Product", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Rank smallest to largest", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Rank largest to smallest", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Running total in", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Show values as", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Source name:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Sum", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Summarize value field by", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "Close", "SSE.Views.ViewManagerDlg.guestText": "Гост", "SSE.Views.ViewManagerDlg.lockText": "Locked", "SSE.Views.ViewManagerDlg.textDelete": "Изтрий", "SSE.Views.ViewManagerDlg.textDuplicate": "Duplicate", "SSE.Views.ViewManagerDlg.textEmpty": "No views have been created yet.", "SSE.Views.ViewManagerDlg.textGoTo": "Go to view", "SSE.Views.ViewManagerDlg.textLongName": "Enter a name that is less than 128 characters.", "SSE.Views.ViewManagerDlg.textNew": "New", "SSE.Views.ViewManagerDlg.textRename": "<PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textRenameError": "View name must not be empty.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Rename view", "SSE.Views.ViewManagerDlg.textViews": "Sheet views", "SSE.Views.ViewManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.ViewManagerDlg.txtTitle": "Sheet view manager", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Are you sure you want to delete this sheet view?", "SSE.Views.ViewManagerDlg.warnDeleteView": "You are trying to delete the currently enabled view '%1'.<br>Close this view and delete it?", "SSE.Views.ViewTab.capBtnFreeze": "Freeze Panes", "SSE.Views.ViewTab.capBtnSheetView": "Sheet View", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Always Show Toolbar", "SSE.Views.ViewTab.textClose": "Close", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Combine Sheet and Status Bars", "SSE.Views.ViewTab.textCreate": "New", "SSE.Views.ViewTab.textDefault": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textFill": "Напълнете", "SSE.Views.ViewTab.textFormula": "Formula Bar", "SSE.Views.ViewTab.textFreezeCol": "Freeze first column", "SSE.Views.ViewTab.textFreezeRow": "Freeze top row", "SSE.Views.ViewTab.textGridlines": "Мрежови линии", "SSE.Views.ViewTab.textHeadings": "Headings", "SSE.Views.ViewTab.textInterfaceTheme": "Interface Theme", "SSE.Views.ViewTab.textLeftMenu": "Left Panel", "SSE.Views.ViewTab.textLine": "Линия", "SSE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "View manager", "SSE.Views.ViewTab.textRightMenu": "Right Panel", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Show frozen panes shadow", "SSE.Views.ViewTab.textTabStyle": "Tab style", "SSE.Views.ViewTab.textUnFreeze": "Unfreeze panes", "SSE.Views.ViewTab.textZeros": "Show Zeros", "SSE.Views.ViewTab.textZoom": "Zoom", "SSE.Views.ViewTab.tipClose": "Close sheet view", "SSE.Views.ViewTab.tipCreate": "Create sheet view", "SSE.Views.ViewTab.tipFreeze": "Freeze panes", "SSE.Views.ViewTab.tipInterfaceTheme": "Interface theme", "SSE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipSheetView": "Sheet view", "SSE.Views.ViewTab.tipViewNormal": "See your document in Normal view", "SSE.Views.ViewTab.tipViewPageBreak": "See where the page breaks will appear when your document is printed", "SSE.Views.ViewTab.txtViewNormal": "Normal", "SSE.Views.ViewTab.txtViewPageBreak": "Page Break Preview", "SSE.Views.WatchDialog.closeButtonText": "Close", "SSE.Views.WatchDialog.textAdd": "Add watch", "SSE.Views.WatchDialog.textBook": "Book", "SSE.Views.WatchDialog.textCell": "Cell", "SSE.Views.WatchDialog.textDelete": "Delete watch", "SSE.Views.WatchDialog.textDeleteAll": "Delete all", "SSE.Views.WatchDialog.textFormula": "Formula", "SSE.Views.WatchDialog.textName": "Name", "SSE.Views.WatchDialog.textSheet": "Sheet", "SSE.Views.WatchDialog.textValue": "Value", "SSE.Views.WatchDialog.txtTitle": "Watch window", "SSE.Views.WBProtection.hintAllowRanges": "Allow edit ranges", "SSE.Views.WBProtection.hintProtectRange": "Protect range", "SSE.Views.WBProtection.hintProtectSheet": "Protect sheet", "SSE.Views.WBProtection.hintProtectWB": "Protect workbook", "SSE.Views.WBProtection.txtAllowRanges": "Allow edit ranges", "SSE.Views.WBProtection.txtHiddenFormula": "Hidden Formulas", "SSE.Views.WBProtection.txtLockedCell": "Locked Cell", "SSE.Views.WBProtection.txtLockedShape": "<PERSON><PERSON><PERSON> Locked", "SSE.Views.WBProtection.txtLockedText": "Lock Text", "SSE.Views.WBProtection.txtProtectRange": "Protect Range", "SSE.Views.WBProtection.txtProtectSheet": "Protect Sheet", "SSE.Views.WBProtection.txtProtectWB": "Protect workbook", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Enter a password to unprotect sheet", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Unprotect sheet", "SSE.Views.WBProtection.txtWBUnlockDescription": "Enter a password to unprotect workbook", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}