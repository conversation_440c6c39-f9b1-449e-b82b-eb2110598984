{"cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.Controllers.Chat.notcriticalErrorTitle": "Ostrzeżenie", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Utwórz z szablonu", "Common.Controllers.History.notcriticalErrorTitle": "Ostrzeżenie", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textAreaStacked": "Skumulowany warstwowy", "Common.define.chartData.textAreaStackedPer": "100% skumulowany warstwowy", "Common.define.chartData.textBar": "Pasek", "Common.define.chartData.textBarNormal": "Kolumnowy grupowany", "Common.define.chartData.textBarNormal3d": "Kolumnowy grupowany 3D", "Common.define.chartData.textBarNormal3dPerspective": "Kolumnowy 3D", "Common.define.chartData.textBarStacked": "Skumulowany kolumnowy", "Common.define.chartData.textBarStacked3d": "Skumulowany kolumnowy 3D", "Common.define.chartData.textBarStackedPer": "100% skumulowany kolumnowy", "Common.define.chartData.textBarStackedPer3d": "100% skumulowany kolumnowy 3D", "Common.define.chartData.textCharts": "Wykresy", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textColumnSpark": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textComboAreaBar": "Skumulowany warstwowy - kolumnowy grupowany", "Common.define.chartData.textComboBarLine": "Kolumnowy grupowany - liniowy", "Common.define.chartData.textComboBarLineSecondary": "Kolumnowy grupowany - liniowy na osi pomocniczej", "Common.define.chartData.textComboCustom": "Niestandardowy złożony", "Common.define.chartData.textDoughnut": "Pierś<PERSON>nio<PERSON>", "Common.define.chartData.textHBarNormal": "Słupkowy grupowany", "Common.define.chartData.textHBarNormal3d": "Słupkowy grupowany 3D", "Common.define.chartData.textHBarStacked": "Skumulowany słupkowy", "Common.define.chartData.textHBarStacked3d": "Skumulowany słupkowy 3D", "Common.define.chartData.textHBarStackedPer": "100% skumulowany słupkowy", "Common.define.chartData.textHBarStackedPer3d": "100% skumulowany słupkowy 3D", "Common.define.chartData.textLine": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textLine3d": "Liniowy 3D", "Common.define.chartData.textLineMarker": "Liniowy ze znacznikami", "Common.define.chartData.textLineSpark": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textLineStacked": "Skumulowany liniowy", "Common.define.chartData.textLineStackedMarker": "Skumulowany liniowy ze znacznikami", "Common.define.chartData.textLineStackedPer": "100% skumulowany liniowy", "Common.define.chartData.textLineStackedPerMarker": "100% skumulowany liniowy ze znacznikami", "Common.define.chartData.textPie": "Kołowe", "Common.define.chartData.textPie3d": "Kołowy 3D", "Common.define.chartData.textPoint": "XY (Punktowy)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Filled radar", "Common.define.chartData.textRadarMarker": "Radar with markers", "Common.define.chartData.textScatter": "Punktowy", "Common.define.chartData.textScatterLine": "Punktowy z prostymi liniami", "Common.define.chartData.textScatterLineMarker": "Punktowy z prostymi liniami i znacznikami", "Common.define.chartData.textScatterSmooth": "Punktowy z wygładzonymi liniami", "Common.define.chartData.textScatterSmoothMarker": "Punktowy z wygładzonymi liniami i znacznikami", "Common.define.chartData.textSparks": "Sparklines", "Common.define.chartData.textStock": "Zbiory", "Common.define.chartData.textSurface": "Powierzchnia", "Common.define.chartData.textWinLossSpark": "Wygrana/przegrana", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "<PERSON><PERSON> ok<PERSON>u", "Common.define.conditionalData.text1Above": "Na 1 odchylenie standardowe wyżej", "Common.define.conditionalData.text1Below": "Na 1 odchylenie standardowe niżej", "Common.define.conditionalData.text2Above": "Na 2 odchylenia standardowe wyżej", "Common.define.conditionalData.text2Below": "Na 2 odchylenia standardowe niżej", "Common.define.conditionalData.text3Above": "Na 3 odchylenia standardowe wyżej", "Common.define.conditionalData.text3Below": "Na 3 odchylenia standardowe niżej", "Common.define.conditionalData.textAbove": "Powyżej", "Common.define.conditionalData.textAverage": "Średnia", "Common.define.conditionalData.textBegins": "zaczyna się z", "Common.define.conditionalData.textBelow": "Poniżej", "Common.define.conditionalData.textBetween": "Pomiędzy", "Common.define.conditionalData.textBlank": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textBlanks": "Zawiera puste komórki", "Common.define.conditionalData.textBottom": "Najmniejsze", "Common.define.conditionalData.textContains": "zawiera", "Common.define.conditionalData.textDataBar": "Histogram", "Common.define.conditionalData.textDate": "Data", "Common.define.conditionalData.textDuplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textEnds": "kończy się na", "Common.define.conditionalData.textEqAbove": "Równy lub wyższy", "Common.define.conditionalData.textEqBelow": "Równy lub niższy", "Common.define.conditionalData.textEqual": "Równy", "Common.define.conditionalData.textError": "Błąd", "Common.define.conditionalData.textErrors": "Zawiera błędy", "Common.define.conditionalData.textFormula": "Formuła", "Common.define.conditionalData.textGreater": "<PERSON><PERSON><PERSON><PERSON><PERSON>ż", "Common.define.conditionalData.textGreaterEq": "<PERSON><PERSON><PERSON><PERSON><PERSON> lub równy niż", "Common.define.conditionalData.textIconSets": "Zestawy ikon", "Common.define.conditionalData.textLast7days": "W ciągu ostatnich 7 dni", "Common.define.conditionalData.textLastMonth": "W zeszłym <PERSON>", "Common.define.conditionalData.textLastWeek": "Zeszły tydzień", "Common.define.conditionalData.textLess": "Mniejszy niż", "Common.define.conditionalData.textLessEq": "Mniejszy lub równy niż", "Common.define.conditionalData.textNextMonth": "W następnym miesiącu", "Common.define.conditionalData.textNextWeek": "W następnym tygodniu", "Common.define.conditionalData.textNotBetween": "<PERSON><PERSON>", "Common.define.conditionalData.textNotBlanks": "<PERSON><PERSON> zawiera pustych komórek", "Common.define.conditionalData.textNotContains": "nie zaw<PERSON>", "Common.define.conditionalData.textNotEqual": "Różny od", "Common.define.conditionalData.textNotErrors": "Nie zawiera błędów", "Common.define.conditionalData.textText": "Tekst", "Common.define.conditionalData.textThisMonth": "<PERSON> tym <PERSON>", "Common.define.conditionalData.textThisWeek": "W tym tygodniu", "Common.define.conditionalData.textToday": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textTomorrow": "<PERSON><PERSON>", "Common.define.conditionalData.textTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textUnique": "Unikalne", "Common.define.conditionalData.textValue": "<PERSON><PERSON><PERSON><PERSON> to", "Common.define.conditionalData.textYesterday": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textAccentedPicture": "Accented Picture", "Common.define.smartArt.textAccentProcess": "Accent Process", "Common.define.smartArt.textAlternatingFlow": "Alternating flow", "Common.define.smartArt.textAlternatingHexagons": "Alternating hexagons", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternating picture blocks", "Common.define.smartArt.textAlternatingPictureCircles": "Alternating picture circles", "Common.define.smartArt.textArchitectureLayout": "Architecture layout", "Common.define.smartArt.textArrowRibbon": "Arrow ribbon", "Common.define.smartArt.textAscendingPictureAccentProcess": "Ascending picture accent process", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Basic bending process", "Common.define.smartArt.textBasicBlockList": "Basic block list", "Common.define.smartArt.textBasicChevronProcess": "Basic chevron process", "Common.define.smartArt.textBasicCycle": "Basic cycle", "Common.define.smartArt.textBasicMatrix": "Basic matrix", "Common.define.smartArt.textBasicPie": "Basic Pie", "Common.define.smartArt.textBasicProcess": "Basic process", "Common.define.smartArt.textBasicPyramid": "Basic pyramid", "Common.define.smartArt.textBasicRadial": "Basic radial", "Common.define.smartArt.textBasicTarget": "Basic target", "Common.define.smartArt.textBasicTimeline": "Basic timeline", "Common.define.smartArt.textBasicVenn": "Basic Venn", "Common.define.smartArt.textBendingPictureAccentList": "Bending picture accent list", "Common.define.smartArt.textBendingPictureBlocks": "Bending picture blocks", "Common.define.smartArt.textBendingPictureCaption": "Bending picture caption", "Common.define.smartArt.textBendingPictureCaptionList": "Bending picture caption list", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Bending picture semi-transparent text", "Common.define.smartArt.textBlockCycle": "Block cycle", "Common.define.smartArt.textBubblePictureList": "Bubble picture list", "Common.define.smartArt.textCaptionedPictures": "Captioned pictures", "Common.define.smartArt.textChevronAccentProcess": "Chevron accent process", "Common.define.smartArt.textChevronList": "Chevron list", "Common.define.smartArt.textCircleAccentTimeline": "Circle accent timeline", "Common.define.smartArt.textCircleArrowProcess": "Circle arrow process", "Common.define.smartArt.textCirclePictureHierarchy": "Circle picture hierarchy", "Common.define.smartArt.textCircleProcess": "Circle process", "Common.define.smartArt.textCircleRelationship": "Circle relationship", "Common.define.smartArt.textCircularBendingProcess": "Circular bending process", "Common.define.smartArt.textCircularPictureCallout": "Circular picture callout", "Common.define.smartArt.textClosedChevronProcess": "Closed chevron process", "Common.define.smartArt.textContinuousArrowProcess": "Continuous arrow process", "Common.define.smartArt.textContinuousBlockProcess": "Continuous block process", "Common.define.smartArt.textContinuousCycle": "Continuous cycle", "Common.define.smartArt.textContinuousPictureList": "Continuous picture list", "Common.define.smartArt.textConvergingArrows": "Converging arrows", "Common.define.smartArt.textConvergingRadial": "Converging radial", "Common.define.smartArt.textConvergingText": "Converging text", "Common.define.smartArt.textCounterbalanceArrows": "Counterbalance arrows", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "Cycle matrix", "Common.define.smartArt.textDescendingBlockList": "Descending block list", "Common.define.smartArt.textDescendingProcess": "Descending process", "Common.define.smartArt.textDetailedProcess": "Detailed process", "Common.define.smartArt.textDivergingArrows": "Diverging arrows", "Common.define.smartArt.textDivergingRadial": "Diverging radial", "Common.define.smartArt.textEquation": "Equation", "Common.define.smartArt.textFramedTextPicture": "Framed text picture", "Common.define.smartArt.textFunnel": "Funnel", "Common.define.smartArt.textGear": "Gear", "Common.define.smartArt.textGridMatrix": "Grid matrix", "Common.define.smartArt.textGroupedList": "Grouped list", "Common.define.smartArt.textHalfCircleOrganizationChart": "Half circle organization chart", "Common.define.smartArt.textHexagonCluster": "Hexagon cluster", "Common.define.smartArt.textHexagonRadial": "Hexagon radial", "Common.define.smartArt.textHierarchy": "Hierarchy", "Common.define.smartArt.textHierarchyList": "Hierarchy list", "Common.define.smartArt.textHorizontalBulletList": "Horizontal bullet list", "Common.define.smartArt.textHorizontalHierarchy": "Horizontal hierarchy", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal labeled hierarchy", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontal multi-level hierarchy", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontal organization chart", "Common.define.smartArt.textHorizontalPictureList": "Horizontal picture list", "Common.define.smartArt.textIncreasingArrowProcess": "Increasing arrow process", "Common.define.smartArt.textIncreasingCircleProcess": "Increasing circle process", "Common.define.smartArt.textInterconnectedBlockProcess": "Interconnected block process", "Common.define.smartArt.textInterconnectedRings": "Interconnected rings", "Common.define.smartArt.textInvertedPyramid": "Inverted pyramid", "Common.define.smartArt.textLabeledHierarchy": "Labeled hierarchy", "Common.define.smartArt.textLinearVenn": "Linear Venn", "Common.define.smartArt.textLinedList": "Lined list", "Common.define.smartArt.textList": "List", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Multidirectional cycle", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Name and title organization chart", "Common.define.smartArt.textNestedTarget": "Nested target", "Common.define.smartArt.textNondirectionalCycle": "Nondirectional cycle", "Common.define.smartArt.textOpposingArrows": "Opposing arrows", "Common.define.smartArt.textOpposingIdeas": "Opposing ideas", "Common.define.smartArt.textOrganizationChart": "Organization chart", "Common.define.smartArt.textOther": "Other", "Common.define.smartArt.textPhasedProcess": "Phased process", "Common.define.smartArt.textPicture": "Picture", "Common.define.smartArt.textPictureAccentBlocks": "Picture accent blocks", "Common.define.smartArt.textPictureAccentList": "Picture accent list", "Common.define.smartArt.textPictureAccentProcess": "Picture accent process", "Common.define.smartArt.textPictureCaptionList": "Picture caption list", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "Picture grid", "Common.define.smartArt.textPictureLineup": "Picture lineup", "Common.define.smartArt.textPictureOrganizationChart": "Picture organization chart", "Common.define.smartArt.textPictureStrips": "Picture strips", "Common.define.smartArt.textPieProcess": "Pie process", "Common.define.smartArt.textPlusAndMinus": "Plus and minus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "Process arrows", "Common.define.smartArt.textProcessList": "Process list", "Common.define.smartArt.textPyramid": "Pyramid", "Common.define.smartArt.textPyramidList": "Pyramid list", "Common.define.smartArt.textRadialCluster": "Radial cluster", "Common.define.smartArt.textRadialCycle": "Radial cycle", "Common.define.smartArt.textRadialList": "Radial list", "Common.define.smartArt.textRadialPictureList": "Radial picture list", "Common.define.smartArt.textRadialVenn": "Radial Venn", "Common.define.smartArt.textRandomToResultProcess": "Random to result process", "Common.define.smartArt.textRelationship": "Relationship", "Common.define.smartArt.textRepeatingBendingProcess": "Repeating bending process", "Common.define.smartArt.textReverseList": "Reverse list", "Common.define.smartArt.textSegmentedCycle": "Segmented cycle", "Common.define.smartArt.textSegmentedProcess": "Segmented process", "Common.define.smartArt.textSegmentedPyramid": "Segmented pyramid", "Common.define.smartArt.textSnapshotPictureList": "Snapshot picture list", "Common.define.smartArt.textSpiralPicture": "Spiral picture", "Common.define.smartArt.textSquareAccentList": "Square accent list", "Common.define.smartArt.textStackedList": "Stacked list", "Common.define.smartArt.textStackedVenn": "Stacked <PERSON>n", "Common.define.smartArt.textStaggeredProcess": "Staggered process", "Common.define.smartArt.textStepDownProcess": "Step down process", "Common.define.smartArt.textStepUpProcess": "Step up process", "Common.define.smartArt.textSubStepProcess": "Sub-step process", "Common.define.smartArt.textTabbedArc": "Tabbed arc", "Common.define.smartArt.textTableHierarchy": "Table hierarchy", "Common.define.smartArt.textTableList": "Table list", "Common.define.smartArt.textTabList": "Tab List", "Common.define.smartArt.textTargetList": "Target list", "Common.define.smartArt.textTextCycle": "Text cycle", "Common.define.smartArt.textThemePictureAccent": "Theme picture accent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Theme picture alternating accent", "Common.define.smartArt.textThemePictureGrid": "Theme picture grid", "Common.define.smartArt.textTitledMatrix": "Titled matrix", "Common.define.smartArt.textTitledPictureAccentList": "Titled picture accent list", "Common.define.smartArt.textTitledPictureBlocks": "Titled picture blocks", "Common.define.smartArt.textTitlePictureLineup": "Title picture lineup", "Common.define.smartArt.textTrapezoidList": "Trapezoid list", "Common.define.smartArt.textUpwardArrow": "Upward arrow", "Common.define.smartArt.textVaryingWidthList": "Varying width list", "Common.define.smartArt.textVerticalAccentList": "Vertical accent list", "Common.define.smartArt.textVerticalArrowList": "Vertical arrow list", "Common.define.smartArt.textVerticalBendingProcess": "Vertical bending process", "Common.define.smartArt.textVerticalBlockList": "Vertical block list", "Common.define.smartArt.textVerticalBoxList": "Vertical box list", "Common.define.smartArt.textVerticalBracketList": "Vertical bracket list", "Common.define.smartArt.textVerticalBulletList": "Vertical bullet list", "Common.define.smartArt.textVerticalChevronList": "Vertical chevron list", "Common.define.smartArt.textVerticalCircleList": "Vertical circle list", "Common.define.smartArt.textVerticalCurvedList": "Vertical curved list", "Common.define.smartArt.textVerticalEquation": "Vertical equation", "Common.define.smartArt.textVerticalPictureAccentList": "Vertical picture accent list", "Common.define.smartArt.textVerticalPictureList": "Vertical picture list", "Common.define.smartArt.textVerticalProcess": "Vertical process", "Common.Translation.textMoreButton": "More", "Common.Translation.tipFileLocked": "Dokument jest zablokowany do edycji. Zmiany można wprowadzić później i zapisać go jako kopię lokalną.", "Common.Translation.tipFileReadOnly": "The file is read-only. To keep your changes, save the file with a new name or in a different location.", "Common.Translation.warnFileLocked": "Plik jest edytowany w innej aplikacji. Możesz kontynuować edycję i zapisać go jako kopię.", "Common.Translation.warnFileLockedBtnEdit": "Utwórz kopię", "Common.Translation.warnFileLockedBtnView": "Otwórz do oglądania", "Common.UI.ButtonColored.textAutoColor": "Automatyczny", "Common.UI.ButtonColored.textEyedropper": "Eyedropper", "Common.UI.ButtonColored.textNewColor": "Nowy niestandardowy kolor", "Common.UI.ComboBorderSize.txtNoBorders": "Bez k<PERSON>ędzi", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Bez k<PERSON>ędzi", "Common.UI.ComboDataView.emptyComboText": "Brak styli", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Obecny", "Common.UI.ExtendedColorDialog.textHexErr": "Wprowadzon<PERSON> wartość jest nieprawidłowa.<br>W<PERSON><PERSON><PERSON><PERSON> wartość w zakresie od 000000 do FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Nowy", "Common.UI.ExtendedColorDialog.textRGBErr": "Wprowadzona wartość jest nieprawidłowa.<br><PERSON><PERSON><PERSON><PERSON><PERSON> wartość liczbową w zakresie od 0 do 255.", "Common.UI.HSBColorPicker.textNoColor": "Bez koloru", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "<PERSON><PERSON><PERSON> hasło", "Common.UI.SearchBar.textFind": "Znajdź", "Common.UI.SearchBar.tipCloseSearch": "Zamknij wyszukiwanie", "Common.UI.SearchBar.tipNextResult": "Następny wynik", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Open advanced settings", "Common.UI.SearchBar.tipPreviousResult": "Poprzedni wynik", "Common.UI.SearchDialog.textHighlight": "Podświetl wyniki", "Common.UI.SearchDialog.textMatchCase": "Rozróżniana wielkość liter", "Common.UI.SearchDialog.textReplaceDef": "Wprowadź tekst zastępczy", "Common.UI.SearchDialog.textSearchStart": "Wprowadź tekst tutaj", "Common.UI.SearchDialog.textTitle": "Znajdź i zamień", "Common.UI.SearchDialog.textTitle2": "Znajdź", "Common.UI.SearchDialog.textWholeWords": "Tylko całe słowa", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "Zamień", "Common.UI.SearchDialog.txtBtnReplaceAll": "Zamień wszystko", "Common.UI.SynchronizeTip.textDontShow": "<PERSON>e pokazuj tej wiadomości ponownie", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "Dokument został zmieniony przez innego użytkownika.<br><PERSON><PERSON><PERSON> k<PERSON>, aby zap<PERSON>ć swoje zmiany i ponownie załadować zmiany.", "Common.UI.ThemeColorPalette.textRecentColors": "Ostatnie kolory", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON><PERSON>e", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON><PERSON> moty<PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Klasyczny jasny", "Common.UI.Themes.txtThemeContrastDark": "Contrast Dark", "Common.UI.Themes.txtThemeDark": "Ciemny", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "Same as system", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "Zamknij", "Common.UI.Window.noButtonText": "<PERSON><PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Potwierdzenie", "Common.UI.Window.textDontShow": "<PERSON>e pokazuj tej wiadomości ponownie", "Common.UI.Window.textError": "Błąd", "Common.UI.Window.textInformation": "Informacja", "Common.UI.Window.textWarning": "Ostrzeżenie", "Common.UI.Window.yesButtonText": "Tak", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Accent", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "Background", "Common.Utils.ThemeColor.txtBlack": "Black", "Common.Utils.ThemeColor.txtBlue": "Blue", "Common.Utils.ThemeColor.txtBrightGreen": "Bright green", "Common.Utils.ThemeColor.txtBrown": "<PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Dark blue", "Common.Utils.ThemeColor.txtDarker": "Darker", "Common.Utils.ThemeColor.txtDarkGray": "Dark gray", "Common.Utils.ThemeColor.txtDarkGreen": "Dark green", "Common.Utils.ThemeColor.txtDarkPurple": "Dark purple", "Common.Utils.ThemeColor.txtDarkRed": "Dark red", "Common.Utils.ThemeColor.txtDarkTeal": "Dark teal", "Common.Utils.ThemeColor.txtDarkYellow": "Dark yellow", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "<PERSON>", "Common.Utils.ThemeColor.txtGreen": "Green", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "Light blue", "Common.Utils.ThemeColor.txtLighter": "Lighter", "Common.Utils.ThemeColor.txtLightGray": "Light gray", "Common.Utils.ThemeColor.txtLightGreen": "Light green", "Common.Utils.ThemeColor.txtLightOrange": "Light orange", "Common.Utils.ThemeColor.txtLightYellow": "Light yellow", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "Pink", "Common.Utils.ThemeColor.txtPurple": "Purple", "Common.Utils.ThemeColor.txtRed": "Red", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Sky blue", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Text", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "White", "Common.Utils.ThemeColor.txtYellow": "Yellow", "Common.Views.About.txtAddress": "adres:", "Common.Views.About.txtLicensee": "LICENCJOBIORCA", "Common.Views.About.txtLicensor": "LICENCJODAWCA", "Common.Views.About.txtMail": "e-mail:", "Common.Views.About.txtPoweredBy": "zasilany przez", "Common.Views.About.txtTel": "tel.:", "Common.Views.About.txtVersion": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Aplikuj podczas pracy", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autokorekta", "Common.Views.AutoCorrectDialog.textAutoFormat": "Formatuj automatycznie podczas pisania", "Common.Views.AutoCorrectDialog.textBy": "Na", "Common.Views.AutoCorrectDialog.textDelete": "Usuń", "Common.Views.AutoCorrectDialog.textHyperlink": "Ścieżki internetowe i sieciowe z hiperłączami", "Common.Views.AutoCorrectDialog.textMathCorrect": "Autokorekta matematyczna", "Common.Views.AutoCorrectDialog.textNewRowCol": "Uwzględnij nowe wiersze i kolumny w tabeli", "Common.Views.AutoCorrectDialog.textRecognized": "Rozpoznawane funkcje", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Następujące wyrażenia są rozpoznawanymi wyrażeniami matematycznymi. Nie będą one automatycznie pisane kursywą.", "Common.Views.AutoCorrectDialog.textReplace": "Zamień", "Common.Views.AutoCorrectDialog.textReplaceText": "Zamień podczas pisania", "Common.Views.AutoCorrectDialog.textReplaceType": "Zamień tekst podczas pisania", "Common.Views.AutoCorrectDialog.textReset": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textResetAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ustawienia domyślne", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Autokorekta", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Rozpoznawane funkcje muszą zawierać tylko litery od A do Z, wielkie lub małe.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Każde dodane wyrażenie zostanie usunięte, a usunięte zostaną przywrócone. <PERSON><PERSON><PERSON> k<PERSON>?", "Common.Views.AutoCorrectDialog.warnReplace": "Wpis autokorekty dla %1 już istnieje. <PERSON><PERSON> ch<PERSON>z go wymienić?", "Common.Views.AutoCorrectDialog.warnReset": "Wszelkie dodane autokorekty zostaną usunięte, a zmienione zostaną przywrócone oryginalne wartości. <PERSON><PERSON> ch<PERSON> kont<PERSON>?", "Common.Views.AutoCorrectDialog.warnRestore": "Wpis autokorekty dla %1 zostanie zresetowany do pierwotnej wartości. <PERSON><PERSON> ch<PERSON>z kontynuować?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "Autor <PERSON>", "Common.Views.Comments.mniAuthorDesc": "Autor <PERSON>", "Common.Views.Comments.mniDateAsc": "<PERSON>d starych do nowych", "Common.Views.Comments.mniDateDesc": "<PERSON>d nowych do starych", "Common.Views.Comments.mniFilterGroups": "Filter by Group", "Common.Views.Comments.mniPositionAsc": "Z góry", "Common.Views.Comments.mniPositionDesc": "Z dołu", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "Dodaj komentarz do", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "Zamknij", "Common.Views.Comments.textClosePanel": "Zamknij komentarze", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Wprowadź twój komentarz tutaj", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textOpenAgain": "Otwórz ponownie", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "Roz<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "Rozwiązany", "Common.Views.Comments.textSort": "Sort<PERSON>j komentarze", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "Nie masz uprawnień do ponownego otwarcia komentarza", "Common.Views.Comments.txtEmpty": "There are no comments in the sheet.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON>e pokazuj tej wiadomości ponownie", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON><PERSON>, wycinanie i wklejanie za pomocą przycisków edytora i działań menu kontekstowego zostanie przeprowadzone tylko w tej karcie edytora.<br><br><PERSON><PERSON> s<PERSON><PERSON><PERSON>ć lub w<PERSON><PERSON> do lub z aplikacji poza kartą edytora, użyj następujących kombinacji klawiszy:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>, Wytnij i Wklej", "Common.Views.CopyWarningDialog.textToCopy": "dla kopiowania", "Common.Views.CopyWarningDialog.textToCut": "do wycięcia", "Common.Views.CopyWarningDialog.textToPaste": "do wklejenia", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "Ładowanie...", "Common.Views.DocumentAccessDialog.textTitle": "Ustawienia udostępniania", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "Eraser", "Common.Views.Draw.hintSelect": "Select", "Common.Views.Draw.txtEraser": "Eraser", "Common.Views.Draw.txtHighlighter": "Highlighter", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Pen", "Common.Views.Draw.txtSelect": "Select", "Common.Views.Draw.txtSize": "Size", "Common.Views.EditNameDialog.textLabel": "Etykieta:", "Common.Views.EditNameDialog.textLabelError": "Etykieta nie może być pusta.", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "Użytkownicy aktualnie edytujący plik:", "Common.Views.Header.textAddFavorite": "Dodaj do ulubionych", "Common.Views.Header.textAdvSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textBack": "Otwórz lokalizację pliku", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "Ukryj pasek narzędzi", "Common.Views.Header.textHideLines": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideStatusBar": "Połącz arkusze i pasek stanu", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "Read only", "Common.Views.Header.textRemoveFavorite": "Usuń z ulubionych", "Common.Views.Header.textSaveBegin": "Zapisywanie ...", "Common.Views.Header.textSaveChanged": "Zmodyfikowan<PERSON>", "Common.Views.Header.textSaveEnd": "Wszystkie zmiany zapisane", "Common.Views.Header.textSaveExpander": "Wszystkie zmiany zapisane", "Common.Views.Header.textShare": "Udostępnij", "Common.Views.Header.textZoom": "Powiększenie", "Common.Views.Header.tipAccessRights": "Zarządzaj prawami dostępu do dokumentu", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "Pobierz plik", "Common.Views.Header.tipGoEdit": "Edytuj bieżący plik", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON> plik", "Common.Views.Header.tipPrintQuick": "Szybkie drukowanie", "Common.Views.Header.tipRedo": "<PERSON>yk<PERSON><PERSON> pono<PERSON>ie", "Common.Views.Header.tipSave": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipSearch": "Szukaj", "Common.Views.Header.tipUndo": "Cof<PERSON>j", "Common.Views.Header.tipUndock": "Odepnij w osobnym oknie", "Common.Views.Header.tipUsers": "Zobacz użytkowników", "Common.Views.Header.tipViewSettings": "Wyświ<PERSON>l us<PERSON>wienia", "Common.Views.Header.tipViewUsers": "Wyświetl użytkowników i zarządzaj prawami dostępu do dokumentu", "Common.Views.Header.txtAccessRights": "Zmień prawa dostępu", "Common.Views.Header.txtRename": "Zmień nazwę", "Common.Views.History.textCloseHistory": "Zamknij historię", "Common.Views.History.textHideAll": "Ukryj szczegółowe zmiany", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textShowAll": "Pokaż szczegółowe zmiany", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "Wklej link URL do obrazu:", "Common.Views.ImageFromUrlDialog.txtEmpty": "To pole jest wymagane", "Common.Views.ImageFromUrlDialog.txtNotUrl": "To pole powinno by<PERSON> adresem URL w formacie \"http://www.example.com\"", "Common.Views.ListSettingsDialog.textBulleted": "Punktowane", "Common.Views.ListSettingsDialog.textFromFile": "From file", "Common.Views.ListSettingsDialog.textFromStorage": "From storage", "Common.Views.ListSettingsDialog.textFromUrl": "From URL", "Common.Views.ListSettingsDialog.textNumbering": "Numerowane", "Common.Views.ListSettingsDialog.textSelect": "Select from", "Common.Views.ListSettingsDialog.tipChange": "Zmień znacznik", "Common.Views.ListSettingsDialog.txtBullet": "Znak punktora", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "Image", "Common.Views.ListSettingsDialog.txtImport": "Import", "Common.Views.ListSettingsDialog.txtNewBullet": "Nowy znacznik", "Common.Views.ListSettingsDialog.txtNewImage": "New image", "Common.Views.ListSettingsDialog.txtNone": "Żaden", "Common.Views.ListSettingsDialog.txtOfText": "% tekstu", "Common.Views.ListSettingsDialog.txtSize": "Rozmiar", "Common.Views.ListSettingsDialog.txtStart": "Rozpocznij od", "Common.Views.ListSettingsDialog.txtSymbol": "Symbole", "Common.Views.ListSettingsDialog.txtTitle": "Ustawienia listy", "Common.Views.ListSettingsDialog.txtType": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "Zamknij plik", "Common.Views.OpenDialog.textInvalidRange": "Błędny zakres komórek.", "Common.Views.OpenDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON> dane", "Common.Views.OpenDialog.txtAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtColon": "Dwukropek", "Common.Views.OpenDialog.txtComma": "P<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtDelimiter": "Separator", "Common.Views.OpenDialog.txtDestData": "<PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON> um<PERSON> swoje dane", "Common.Views.OpenDialog.txtEmpty": "To pole jest wymagane", "Common.Views.OpenDialog.txtEncoding": "Kodowanie", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON> jest nieprawidłowe.", "Common.Views.OpenDialog.txtOpenFile": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasło, aby otworzyć plik", "Common.Views.OpenDialog.txtOther": "Inny", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "Podgląd", "Common.Views.OpenDialog.txtProtected": "Po wprowadzeniu hasła i otwarciu pliku bieżące hasło do pliku zostanie zresetowane", "Common.Views.OpenDialog.txtSemicolon": "Średnik", "Common.Views.OpenDialog.txtSpace": "Odstęp", "Common.Views.OpenDialog.txtTab": "Karta", "Common.Views.OpenDialog.txtTitle": "Wybierz %1 opcji", "Common.Views.OpenDialog.txtTitleProtected": "Plik chroniony", "Common.Views.PasswordDialog.txtDescription": "Ustaw hasło aby zabezpieczyć ten dokument", "Common.Views.PasswordDialog.txtIncorrectPwd": "<PERSON><PERSON> nie są takie same", "Common.Views.PasswordDialog.txtPassword": "<PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtTitle": "Ustaw hasło", "Common.Views.PasswordDialog.txtWarning": "Uwaga: <PERSON><PERSON><PERSON> zapomnisz lub zgubisz hasło, nie będzie możliwości odzyskania go. Zapisz go i nikomu nie udostępniaj.", "Common.Views.PluginDlg.textLoading": "Ładowanie", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.strPlugins": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "Start", "Common.Views.Plugins.textStop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.hintDelPwd": "Delete password", "Common.Views.Protection.hintPwd": "<PERSON><PERSON><PERSON> lub usuń hasło", "Common.Views.Protection.hintSignature": "Dodaj podpis cyfrowy lub wiersz do podpisu", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON> hasło", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON><PERSON>o", "Common.Views.Protection.txtEncrypt": "Szyfruj", "Common.Views.Protection.txtInvisibleSignature": "<PERSON><PERSON><PERSON> c<PERSON>y", "Common.Views.Protection.txtSignature": "Sygnatura", "Common.Views.Protection.txtSignatureLine": "Dodaj wiersz do podpisu", "Common.Views.RecentFiles.txtOpenRecent": "Open Recent", "Common.Views.RenameDialog.textName": "Nazwa pliku", "Common.Views.RenameDialog.txtInvalidName": "Nazwa pliku nie może zawierać żadnego z następujących znaków:", "Common.Views.ReviewChanges.hintNext": "Do następnej zmiany", "Common.Views.ReviewChanges.hintPrev": "Do poprzedniej zmiany", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Współedycja w czasie rzeczywistym. Wszystkie zmiany są zapisywane automatycznie.", "Common.Views.ReviewChanges.strStrict": "Ścisły", "Common.Views.ReviewChanges.strStrictDesc": "Użyj przycisku „Zapisz”, aby zsynchronizować zmiany wprowadzane przez Ciebie i innych.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Zaakceptuj bieżącą zmianę", "Common.Views.ReviewChanges.tipCoAuthMode": "Ustaw tryb współedycji", "Common.Views.ReviewChanges.tipCommentRem": "Usuń komentarze", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Usuń aktualne komentarze", "Common.Views.ReviewChanges.tipCommentResolve": "Rozwiąż komentarze", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Rozwiąż bieżące komentarze", "Common.Views.ReviewChanges.tipHistory": "Pokaż historię we<PERSON>ji", "Common.Views.ReviewChanges.tipRejectCurrent": "Odrzuć bieżącą zmianę", "Common.Views.ReviewChanges.tipReview": "Śledzenie zmian", "Common.Views.ReviewChanges.tipReviewView": "<PERSON><PERSON><PERSON><PERSON> tryb, w kt<PERSON>rym mają być wyświetlane zmiany", "Common.Views.ReviewChanges.tipSetDocLang": "Ustaw język dokumentu", "Common.Views.ReviewChanges.tipSetSpelling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "Common.Views.ReviewChanges.tipSharing": "Zarządzaj prawami dostępu do dokumentu", "Common.Views.ReviewChanges.txtAccept": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptAll": "Zaakceptuj wszystkie zmiany", "Common.Views.ReviewChanges.txtAcceptChanges": "Zaak<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptCurrent": "Zaakceptuj bieżącą zmianę", "Common.Views.ReviewChanges.txtChat": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtClose": "Zamknij", "Common.Views.ReviewChanges.txtCoAuthMode": "Tryb współtworzenia", "Common.Views.ReviewChanges.txtCommentRemAll": "Usuń wszystkie komentarze", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Usuń aktualne komentarze", "Common.Views.ReviewChanges.txtCommentRemMy": "Usuń moje komentarze", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Usuń moje bieżące komentarze", "Common.Views.ReviewChanges.txtCommentRemove": "Usuń", "Common.Views.ReviewChanges.txtCommentResolve": "Roz<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "Rozwiąż wszystkie komentarze", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Rozwiąż bieżące komentarze", "Common.Views.ReviewChanges.txtCommentResolveMy": "Rozwiąż moje komentarze", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Rozwiąż moje bieżące komentarze", "Common.Views.ReviewChanges.txtDocLang": "Język", "Common.Views.ReviewChanges.txtFinal": "Wszystkie zmiany zostały zaakceptowane (podgląd)", "Common.Views.ReviewChanges.txtFinalCap": "Ostateczny", "Common.Views.ReviewChanges.txtHistory": "<PERSON> wersji", "Common.Views.ReviewChanges.txtMarkup": "Wszystkie zmiany (edycja)", "Common.Views.ReviewChanges.txtMarkupCap": "Zmiany", "Common.Views.ReviewChanges.txtNext": "Do następnej zmiany", "Common.Views.ReviewChanges.txtOriginal": "Wszystkie zmiany zostały odrzucone (podgląd)", "Common.Views.ReviewChanges.txtOriginalCap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtPrev": "Poprzedni", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Odrzuć wszystkie zmiany", "Common.Views.ReviewChanges.txtRejectChanges": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "Odrzuć bieżącą zmianę", "Common.Views.ReviewChanges.txtSharing": "Dostęp współdzielony", "Common.Views.ReviewChanges.txtSpelling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "Common.Views.ReviewChanges.txtTurnon": "Śledzenie zmian", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON> w<PERSON>świ<PERSON>", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "Zamknij", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Enter your comment here", "Common.Views.ReviewPopover.textMention": "+wzmianka zapewni użytkownikowi dostęp do pliku i wyśle e-maila", "Common.Views.ReviewPopover.textMentionNotify": "+wzmianka powiadomi użytkownika e-mailem", "Common.Views.ReviewPopover.textOpenAgain": "Otwórz ponownie", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "Roz<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Nie masz uprawnień do ponownego otwarcia komentarza", "Common.Views.ReviewPopover.txtDeleteTip": "Usuń", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Ładowanie", "Common.Views.SaveAsDlg.textTitle": "Folder do zapisu", "Common.Views.SearchPanel.textByColumns": "Przez kolumny", "Common.Views.SearchPanel.textByRows": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textCaseSensitive": "Rozróżniana wielkość liter", "Common.Views.SearchPanel.textCell": "Komórka", "Common.Views.SearchPanel.textCloseSearch": "Zamknij wyszukiwanie", "Common.Views.SearchPanel.textContentChanged": "Document changed.", "Common.Views.SearchPanel.textFind": "Znajdź", "Common.Views.SearchPanel.textFindAndReplace": "Znajdź i zamień", "Common.Views.SearchPanel.textFormula": "Formuła", "Common.Views.SearchPanel.textFormulas": "Form<PERSON>ł<PERSON>", "Common.Views.SearchPanel.textItemEntireCell": "Cała zawartość komórki", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} items successfully replaced.", "Common.Views.SearchPanel.textLookIn": "<PERSON><PERSON><PERSON> w", "Common.Views.SearchPanel.textMatchUsingRegExp": "Match using regular expressions", "Common.Views.SearchPanel.textName": "Nazwa", "Common.Views.SearchPanel.textNoMatches": "Brak dopasowań", "Common.Views.SearchPanel.textNoSearchResults": "Brak wyników wyszukiwania", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} items replaced. Remaining {2} items are locked by other users.", "Common.Views.SearchPanel.textReplace": "Zamień", "Common.Views.SearchPanel.textReplaceAll": "Zamień wszystko", "Common.Views.SearchPanel.textReplaceWith": "Replace with", "Common.Views.SearchPanel.textSearch": "Szukaj", "Common.Views.SearchPanel.textSearchAgain": "{0}Perform new search{1} for accurate results.", "Common.Views.SearchPanel.textSearchHasStopped": "Search has stopped", "Common.Views.SearchPanel.textSearchOptions": "<PERSON><PERSON>je w<PERSON>zukiwan<PERSON>", "Common.Views.SearchPanel.textSearchResults": "Search results: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textSelectDataRange": "<PERSON><PERSON><PERSON><PERSON> zak<PERSON> danych", "Common.Views.SearchPanel.textSheet": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSpecificRange": "Specific range", "Common.Views.SearchPanel.textTooManyResults": "Jest zbyt dużo wyników, aby je tutaj wyświ<PERSON>", "Common.Views.SearchPanel.textValue": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textValues": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textWholeWords": "Tylko całe słowa", "Common.Views.SearchPanel.textWithin": "W ciągu", "Common.Views.SearchPanel.textWorkbook": "Skoroszyt", "Common.Views.SearchPanel.tipNextResult": "Następny wynik", "Common.Views.SearchPanel.tipPreviousResult": "Wcześniejszy wynik", "Common.Views.SelectFileDlg.textLoading": "Ładowanie", "Common.Views.SelectFileDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "Pogrubione", "Common.Views.SignDialog.textCertificate": "Certy<PERSON><PERSON>", "Common.Views.SignDialog.textChange": "Zmień", "Common.Views.SignDialog.textInputName": "Wpisz imię i nazwisko osoby podpisującej", "Common.Views.SignDialog.textItalic": "Ku<PERSON>ywa", "Common.Views.SignDialog.textNameError": "Imię i nazwisko osoby podpisującej nie może być puste.", "Common.Views.SignDialog.textPurpose": "Cel podpisywania tego dokumentu", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON><PERSON><PERSON><PERSON> obraz", "Common.Views.SignDialog.textSignature": "<PERSON><PERSON> wyglą<PERSON> podpis:", "Common.Views.SignDialog.textTitle": "Podpisz dokument", "Common.Views.SignDialog.textUseImage": "lub k<PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON> obraz\", aby <PERSON><PERSON><PERSON><PERSON> obrazu jako pod<PERSON>u", "Common.Views.SignDialog.textValid": "Ważny od %1 do %2", "Common.Views.SignDialog.tipFontName": "Nazwa czcionki", "Common.Views.SignDialog.tipFontSize": "Rozmiar <PERSON>ki", "Common.Views.SignSettingsDialog.textAllowComment": "Zezwól podpisującemu na dodawanie komentarza w oknie podpisu", "Common.Views.SignSettingsDialog.textDefInstruction": "Before signing this document, verify that the content you are signing is correct.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail", "Common.Views.SignSettingsDialog.textInfoName": "Nazwa", "Common.Views.SignSettingsDialog.textInfoTitle": "Stanowisko osoby podpisującej", "Common.Views.SignSettingsDialog.textInstructions": "Instrukcje dla osoby podpisującej", "Common.Views.SignSettingsDialog.textShowDate": "Pokaż datę wykonania podpisu", "Common.Views.SignSettingsDialog.textTitle": "Konfiguracja podpisu", "Common.Views.SignSettingsDialog.txtEmpty": "To pole jest wymagane", "Common.Views.SymbolTableDialog.textCharacter": "Znak", "Common.Views.SymbolTableDialog.textCode": "Nazwa Unicode", "Common.Views.SymbolTableDialog.textCopyright": "Znak zastrzeżenia prawa autorskiego", "Common.Views.SymbolTableDialog.textDCQuote": "Podwójny cudzysłów zamykający", "Common.Views.SymbolTableDialog.textDOQuote": "Podwójny cudzysłów otwierający", "Common.Views.SymbolTableDialog.textEllipsis": "Wielokropek", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "Długa spacja", "Common.Views.SymbolTableDialog.textEnDash": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEnSpace": "Krótka spacja", "Common.Views.SymbolTableDialog.textFont": "Czcionka", "Common.Views.SymbolTableDialog.textNBHyphen": "Łącznik nierozdzielający", "Common.Views.SymbolTableDialog.textNBSpace": "Spacja nierozdzielająca", "Common.Views.SymbolTableDialog.textPilcrow": "Aka<PERSON>", "Common.Views.SymbolTableDialog.textQEmSpace": "<PERSON><PERSON><PERSON><PERSON> spa<PERSON>ja", "Common.Views.SymbolTableDialog.textRange": "Podzbiór", "Common.Views.SymbolTableDialog.textRecent": "Ostatnio używane symbole", "Common.Views.SymbolTableDialog.textRegistered": "Zastrzeżony znak towarowy", "Common.Views.SymbolTableDialog.textSCQuote": "Pojedynczy cudzysłów zamykający", "Common.Views.SymbolTableDialog.textSection": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "Common.Views.SymbolTableDialog.textSHyphen": "Łącznik miękki", "Common.Views.SymbolTableDialog.textSOQuote": "Pojedynczy cudzysłów otwierający", "Common.Views.SymbolTableDialog.textSpecial": "<PERSON><PERSON><PERSON> s<PERSON>", "Common.Views.SymbolTableDialog.textSymbols": "Symbole", "Common.Views.SymbolTableDialog.textTitle": "Symbole", "Common.Views.SymbolTableDialog.textTradeMark": "Znak towarowy", "Common.Views.UserNameDialog.textDontShow": "<PERSON><PERSON> pytaj mnie ponownie", "Common.Views.UserNameDialog.textLabel": "Etykieta:", "Common.Views.UserNameDialog.textLabelError": "Etykieta nie może być pusta.", "SSE.Controllers.DataTab.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textAddExternalData": "The link to an external source has been added. You can update such links in the Data tab.", "SSE.Controllers.DataTab.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textContinue": "Continue", "SSE.Controllers.DataTab.textDontUpdate": "Don't Update", "SSE.Controllers.DataTab.textEmptyUrl": "<PERSON><PERSON><PERSON>ć adres URL.", "SSE.Controllers.DataTab.textRows": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textTurnOff": "Turn off AutoUpdate", "SSE.Controllers.DataTab.textUpdate": "Update", "SSE.Controllers.DataTab.textWizard": "Tekst na kolumny", "SSE.Controllers.DataTab.txtDataValidation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> danych", "SSE.Controllers.DataTab.txtErrorExternalLink": "Error: updating is failed", "SSE.Controllers.DataTab.txtExpand": "Rozwiń", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Dane obok zaznaczenia nie zostaną usunięte. <PERSON><PERSON> rozszerzyć zaznaczenie, aby obej<PERSON>wało sąsiednie dane, czy kontyn<PERSON>wać tylko z aktualnie wybranymi komórkami?", "SSE.Controllers.DataTab.txtExtendDataValidation": "Zaznaczony obszar zawiera komórki bez warunków dla wartości. <br> <PERSON><PERSON> ch<PERSON>z zastosować warunki do tych komórek?", "SSE.Controllers.DataTab.txtImportWizard": "Kreator importu tekstu", "SSE.Controllers.DataTab.txtRemDuplicates": "Us<PERSON>ń duplikaty", "SSE.Controllers.DataTab.txtRemoveDataValidation": "Wybrany obszar zawiera więcej niż jeden warunek. <br> <PERSON>zy na pewno chcesz usunąć bieżące ustawienia i kontynuować?", "SSE.Controllers.DataTab.txtRemSelected": "Usuń w zaznaczonych", "SSE.Controllers.DataTab.txtUrlTitle": "W<PERSON>j adres URL danych", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "This workbook contains links to external sources which update automatically. This might be unsafe.<br><br>If you trust them, press Continue.", "SSE.Controllers.DataTab.warnUpdateExternalData": "This workbook contains links to one or more external sources that could be unsafe.<br>If you trust the links, update them to get the latest data.", "SSE.Controllers.DocumentHolder.alignmentText": "Wyrównanie", "SSE.Controllers.DocumentHolder.centerText": "Środek", "SSE.Controllers.DocumentHolder.deleteColumnText": "<PERSON><PERSON>ń kolumnę", "SSE.Controllers.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON> wiersz", "SSE.Controllers.DocumentHolder.deleteText": "Usuń", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Odnośnik nie istnieje. Popraw link lub usuń go.", "SSE.Controllers.DocumentHolder.guestText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON> lewa", "SSE.Controllers.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON> p<PERSON>a", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Wiersz powyżej", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Wiersz poniżej", "SSE.Controllers.DocumentHolder.insertText": "Wstaw", "SSE.Controllers.DocumentHolder.leftText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Ostrzeżenie", "SSE.Controllers.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Opcje autokorekty", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON><PERSON><PERSON>ść kolumny {0} symbole ({1} piksele)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON> wiersza: {0} punktów ({1} pikseli)", "SSE.Controllers.DocumentHolder.textCtrlClick": "<PERSON><PERSON><PERSON><PERSON>, aby je otworzyć lub naciśnij i przytrzymaj przycisk myszy, aby zaznaczyć komórkę.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Wstaw lewy", "SSE.Controllers.DocumentHolder.textInsertTop": "Wstaw górny", "SSE.Controllers.DocumentHolder.textPasteSpecial": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Controllers.DocumentHolder.textStopExpand": "Nie rozwijaj tabel automatycznie", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Ten element jest właśnie edytowany przez innego użytkownika.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Powyżej <PERSON>niej", "SSE.Controllers.DocumentHolder.txtAddBottom": "Dodaj dolną krawędź", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "<PERSON><PERSON> pasek ułamka", "SSE.Controllers.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON> poziomy wiesz", "SSE.Controllers.DocumentHolder.txtAddLB": "<PERSON><PERSON><PERSON> lewy dolny wiersz", "SSE.Controllers.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON> k<PERSON>", "SSE.Controllers.DocumentHolder.txtAddLT": "<PERSON><PERSON><PERSON> lewy górny w<PERSON>z", "SSE.Controllers.DocumentHolder.txtAddRight": "Dodaj p<PERSON>ą krawędź", "SSE.Controllers.DocumentHolder.txtAddTop": "Dodaj górną krawędź", "SSE.Controllers.DocumentHolder.txtAddVer": "Dodaj pionowy wiersz", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Wyrównaj do znaku", "SSE.Controllers.DocumentHolder.txtAll": "(wszystko)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Returns the entire contents of the table or specified table columns including column headers, data and total rows", "SSE.Controllers.DocumentHolder.txtAnd": "I", "SSE.Controllers.DocumentHolder.txtBegins": "zaczyna się z", "SSE.Controllers.DocumentHolder.txtBelowAve": "Poniżej średniej", "SSE.Controllers.DocumentHolder.txtBlanks": "(<PERSON><PERSON><PERSON>)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Ustawienia obra<PERSON>ia", "SSE.Controllers.DocumentHolder.txtBottom": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtByField": "%1 of %2", "SSE.Controllers.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Wyrównanie kolumny", "SSE.Controllers.DocumentHolder.txtContains": "zawiera", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Link skopiowany do schowka", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Returns the data cells of the table or specified table columns", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Zmniejsz rozmiar argumentu", "SSE.Controllers.DocumentHolder.txtDeleteArg": "<PERSON><PERSON><PERSON> argument", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Usuń ręczną przerwę", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Usuń zamknięte znaki", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Usuń zamknięte znaki i separatory", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Usuń równanie", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Usuń znak", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtEnds": "kończy się na", "SSE.Controllers.DocumentHolder.txtEquals": "Równa się", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Równy kolorowi komórki", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Równy kolorowi czcionki", "SSE.Controllers.DocumentHolder.txtExpand": "Rozszerz i posortuj", "SSE.Controllers.DocumentHolder.txtExpandSort": "Dane obok zaznaczenia nie będą sortowane. <PERSON><PERSON> rozsz<PERSON> wybó<PERSON>, aby u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ić sąsiednie dane lub kontynuować sortowanie tylko zaznaczonych komórek?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Najmniejsze", "SSE.Controllers.DocumentHolder.txtFilterTop": "Góra", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Zmień na ułamek liniowy", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Zmienić na ukośny prosty ułamek", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Zmień na ułożone ułamki", "SSE.Controllers.DocumentHolder.txtGreater": "<PERSON><PERSON><PERSON><PERSON><PERSON>ż", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "<PERSON><PERSON><PERSON><PERSON><PERSON> lub równy niż", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Znak nad tekstem", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Znak pod tekstem", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Returns the column headers for the table or specified table columns", "SSE.Controllers.DocumentHolder.txtHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideBottom": "Ukryj dolną krawędź", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Ukryj dolny limit", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Ukryj uchwyt zamykający", "SSE.Controllers.DocumentHolder.txtHideDegree": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideHor": "<PERSON><PERSON><PERSON><PERSON> poziomy w<PERSON>z", "SSE.Controllers.DocumentHolder.txtHideLB": "<PERSON><PERSON><PERSON><PERSON> lewy dolny wiersz", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ęd<PERSON>", "SSE.Controllers.DocumentHolder.txtHideLT": "<PERSON><PERSON><PERSON><PERSON> lewy górny wiersz", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Ukryj uchwyt otwierający", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Ukryj <PERSON> zastępczy", "SSE.Controllers.DocumentHolder.txtHideRight": "Uk<PERSON>j prawą krawędź", "SSE.Controllers.DocumentHolder.txtHideTop": "Ukryj górną krawędź", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Ukryj górny limit", "SSE.Controllers.DocumentHolder.txtHideVer": "Ukryj pionowy wiersz", "SSE.Controllers.DocumentHolder.txtImportWizard": "Kreator importu tekstu", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Zwiększ rozmiar argumentu", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Wstaw argument po", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Wstaw argument przed", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Wstaw ręczną przerwę", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Wstaw równanie po", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Wstaw równanie przed", "SSE.Controllers.DocumentHolder.txtItems": "elementów", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Zachowaj tylko tekst", "SSE.Controllers.DocumentHolder.txtLess": "Mniejszy niż", "SSE.Controllers.DocumentHolder.txtLessEquals": "Mniejszy lub równy niż", "SSE.Controllers.DocumentHolder.txtLimitChange": "Zmień lokalizację limitu", "SSE.Controllers.DocumentHolder.txtLimitOver": "Ograniczenie tekstu", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Limit w tekście", "SSE.Controllers.DocumentHolder.txtLockSort": "Obok Twojego wyboru znajdują się dane, ale nie masz wystarczaj<PERSON><PERSON>ch uprawnień, aby z<PERSON>ć te komórki.<br><PERSON><PERSON> ch<PERSON>z kontynuować bieżące zaznaczenie?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Dopasuj nawiasy do wysokości argumentu", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Wyrównanie matrycy", "SSE.Controllers.DocumentHolder.txtNoChoices": "Nie ma wyboru dla wypełnienia komórek.<br><PERSON><PERSON><PERSON> war<PERSON> tekstowe z kolumny mogą być wybrane dla zastąpienia.", "SSE.Controllers.DocumentHolder.txtNotBegins": "nie zaczyna się od", "SSE.Controllers.DocumentHolder.txtNotContains": "nie zaw<PERSON>", "SSE.Controllers.DocumentHolder.txtNotEnds": "nie kończy się na", "SSE.Controllers.DocumentHolder.txtNotEquals": "nie równa się", "SSE.Controllers.DocumentHolder.txtOr": "lub", "SSE.Controllers.DocumentHolder.txtOverbar": "Pasek nad tekstem", "SSE.Controllers.DocumentHolder.txtPaste": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formuła z obramowaniem", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formuła + szer<PERSON><PERSON>ć kolumny", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Formatowanie docelowe", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Wklej formatowanie", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formuła + numer format", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formuła + wszystkie formatowania", "SSE.Controllers.DocumentHolder.txtPasteLink": "Wklej link", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Podłączony obraz", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Scalanie warunkowego formatowania", "SSE.Controllers.DocumentHolder.txtPastePicture": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Formatowanie źródłowe", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Transponować", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Wartość + wszystkie formatowania", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Wartość + formatowanie liczbowe", "SSE.Controllers.DocumentHolder.txtPasteValues": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPercent": "Procentowe", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Ponów automatyczne rozszerzanie tabeli", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "<PERSON><PERSON><PERSON><PERSON> belkę frakcji", "SSE.Controllers.DocumentHolder.txtRemLimit": "Usuń limit", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Us<PERSON>ń pasek", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "<PERSON><PERSON> ch<PERSON> us<PERSON> ten podpis? <br> <PERSON><PERSON> można tego co<PERSON>.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Usuń indeksy", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Usuń indeks dolny", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Usuń górny indeks", "SSE.Controllers.DocumentHolder.txtRowHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Pismo po tekście", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Pismo przed tekstem", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Pokaż dolny limit", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Pokaż klamrę zamyk<PERSON>ącą", "SSE.Controllers.DocumentHolder.txtShowDegree": "Pokaż stopień", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Pokaż klamrę otwierającą", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Pokaż symbol zastępczy", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Pokaż górny limit", "SSE.Controllers.DocumentHolder.txtSorting": "Sort<PERSON>nie", "SSE.Controllers.DocumentHolder.txtSortSelected": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Uchwyty rozciągające", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Choose only this row of the specified column", "SSE.Controllers.DocumentHolder.txtTop": "Góra", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Returns the total rows for the table or specified table columns", "SSE.Controllers.DocumentHolder.txtUnderbar": "Pasek pod tekstem", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Cofnij automatyczne rozszerzanie tabeli", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Użyj kreatora importu tekstu", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Kliknięcie tego linku może być szkodliwe dla urządzenia i danych.<br><PERSON><PERSON> na pewno chcesz kontynuować?", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.warnFilterError": "You need at least one field in the Values area in order to apply a value filter.", "SSE.Controllers.FormulaDialog.sCategoryAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCube": "S<PERSON>ś<PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Custom", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "<PERSON><PERSON> danych", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Data i czas", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Inżyniera", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Finansowe", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Informacja", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 ostat<PERSON> używanych", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logiczny", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Wyszukiwanie i odniesienie", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Matematyczne i trygonometryczne", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statystyczny", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Tekst i data", "SSE.Controllers.LeftMenu.newDocumentTitle": "Nienazwany arkusz kalkulacyjny", "SSE.Controllers.LeftMenu.textByColumns": "Przez kolumny", "SSE.Controllers.LeftMenu.textByRows": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textFormulas": "Form<PERSON>ł<PERSON>", "SSE.Controllers.LeftMenu.textItemEntireCell": "Cała zawartość komórki", "SSE.Controllers.LeftMenu.textLoadHistory": "Wczytywanie historii wersji...", "SSE.Controllers.LeftMenu.textLookin": "<PERSON><PERSON><PERSON> w", "SSE.Controllers.LeftMenu.textNoTextFound": "<PERSON><PERSON> znale<PERSON>, k<PERSON><PERSON><PERSON><PERSON> s<PERSON>sz. <PERSON><PERSON><PERSON> dos<PERSON>uj opcje wyszukiwania.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Zastąpiono. {0} zdarzenia zostały pominięte.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Wyszukiwanie zakończone. Zastąpiono {0}", "SSE.Controllers.LeftMenu.textSave": "Save", "SSE.Controllers.LeftMenu.textSearch": "Przeglądaj", "SSE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "SSE.Controllers.LeftMenu.textSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWarning": "Ostrzeżenie", "SSE.Controllers.LeftMenu.textWithin": "W ciągu", "SSE.Controllers.LeftMenu.textWorkbook": "Skoroszyt", "SSE.Controllers.LeftMenu.txtUntitled": "Bez Nazwy", "SSE.Controllers.LeftMenu.warnDownloadAs": "<PERSON><PERSON>li kontynuujesz zapisywanie w tym formacie, wszystkie funkcje oprócz tekstu zostaną utracone.<br><PERSON><PERSON> na pewno chcesz kontynuować?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "The CSV format does not support saving a multi-sheet file.<br>To keep the selected format and save only the current sheet, press Save.<br>To save the current spreadsheet, click Cancel and save it in a different format.", "SSE.Controllers.Main.confirmAddCellWatches": "This action will add {0} cell watches.<br>Do you want to continue?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "This action will add only {0} cell watches by memory save reason.<br>Do you want to continue?", "SSE.Controllers.Main.confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "SSE.Controllers.Main.confirmMoveCellRange": "Zakres komórek docelowych może zawierać dane. Kontynuować operację?", "SSE.Controllers.Main.confirmPutMergeRange": "Dane źródłowe zawierały scalone komórki.<br>Zostały rozłączone, zanim zostały wklejone do tabeli.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Formuły w wierszu nagłówka zostaną usunięte i przekonwertowane na tekst statyczny.<br><PERSON><PERSON><PERSON> kontynuować?", "SSE.Controllers.Main.confirmReplaceHFPicture": "Only one picture can be inserted in each section of the header.<br>Press \"Replace\" to replace existing picture.<br>Press \"Keep\" to keep existing picture.", "SSE.Controllers.Main.convertationTimeoutText": "Przekroczono limit czasu konwersji.", "SSE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"OK\", aby p<PERSON><PERSON><PERSON><PERSON><PERSON> do listy dokumentów.", "SSE.Controllers.Main.criticalErrorTitle": "Błąd", "SSE.Controllers.Main.downloadErrorText": "Pobieranie ni<PERSON>.", "SSE.Controllers.Main.downloadTextText": "Pobieranie arkusza <PERSON>ego...", "SSE.Controllers.Main.downloadTitleText": "Pobierz arkusz <PERSON>", "SSE.Controllers.Main.errNoDuplicates": "Nie znaleziono zduplikowanych wartości.", "SSE.Controllers.Main.errorAccessDeny": "Próbujesz wykonać działanie, na które nie masz uprawnień.<br><PERSON><PERSON><PERSON> skontaktować się z <PERSON>em serwera dokumentów.", "SSE.Controllers.Main.errorArgsRange": "Wprowadzona formuła jest błędna.<br><PERSON><PERSON><PERSON><PERSON> niepoprawnego argumentu zakresu.", "SSE.Controllers.Main.errorAutoFilterChange": "Operacja nie jest dozwo<PERSON>a, ponieważ próbuje przesunąć komórki w tabeli w arkuszu.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Operacja nie mogła zostać wykonana dla wybranych komórek, ponieważ nie można przenieść części tabeli.<br><PERSON><PERSON><PERSON><PERSON> inny zakres danych, aby cała tabela została przesunięta i spróbuj ponownie.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Operacja nie mogła zostać wykonana dla wybranego zakresu komórek.<br><PERSON><PERSON><PERSON><PERSON> jednolity zakres danych inny niż istniejący i spróbuj ponownie.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Operacja nie może zostać wykonana, poniew<PERSON>ż obszar zawiera filtrowane komórki.<br>Proszę ukryj filtrowane elementy i spróbuj ponownie.", "SSE.Controllers.Main.errorBadImageUrl": "Adres URL obrazu jest błędny", "SSE.Controllers.Main.errorCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the spreadsheet.", "SSE.Controllers.Main.errorCannotUngroup": "Nie można rozgrupować. <PERSON>by rozpocząć konspekt, wybierz wiersze lub kolumny szczegółów i zgrupuj je.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Nie możesz użyć tego polecenia na chronionym arkuszu. Aby użyć tego polecenia, wyłącz ochronę arkusza.<br><PERSON><PERSON><PERSON><PERSON> zostać poproszony o podanie hasła.", "SSE.Controllers.Main.errorChangeArray": "Nie można zmienić części tablicy.", "SSE.Controllers.Main.errorChangeFilteredRange": "<PERSON><PERSON><PERSON><PERSON><PERSON> to zmianę filtrowanego zakresu w arkuszu.<br><PERSON><PERSON> to zadanie, usuń Autofiltry.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "Kom<PERSON><PERSON> lub w<PERSON>, k<PERSON><PERSON><PERSON> pr<PERSON><PERSON><PERSON>, znaj<PERSON><PERSON> si<PERSON> w chronionym arkuszu.<br><PERSON><PERSON> w<PERSON><PERSON><PERSON><PERSON><PERSON>, usuń ochronę arkusza. Możesz zostać poproszony o podanie hasła.", "SSE.Controllers.Main.errorCircularReference": "There are one or more circular references where a formula refers to its own cell either directly or indirectly.<br>Try removing or changing these references, or moving the formulas to different cells.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Połączenie z serwerem zostało utracone. Nie można teraz edytować dokumentu.", "SSE.Controllers.Main.errorConnectToServer": "Nie można zapisać dokumentu. Sprawdź ustawienia połączenia lub skontaktuj się z <PERSON>em.<br>Po kliknięciu przycisku \"OK\" zostanie wyświetlony monit o pobranie dokumentu.", "SSE.Controllers.Main.errorConvertXml": "The file has an unsupported format.<br>Only XML Spreadsheet 2003 format can be used.", "SSE.Controllers.Main.errorCopyMultiselectArea": "To polecenie nie może być użyte z wieloma wyborami.<br><PERSON><PERSON><PERSON><PERSON> jeden zakres i spróbuj ponownie.", "SSE.Controllers.Main.errorCountArg": "Wprowadzona formuła jest błędna.<br>Niepoprawna iloś<PERSON>ów.", "SSE.Controllers.Main.errorCountArgExceed": "Wprowadzona formuła jest błędna.<br>Przekroczono liczbę argumentów.", "SSE.Controllers.Main.errorCreateDefName": "Istniejące zakresy nazw nie mogą być edytowane, a nowe nie mogą zostać utworzone<br>w chwi<PERSON>, gdy niekt<PERSON> z nich są edytowane.", "SSE.Controllers.Main.errorCreateRange": "The existing ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Controllers.Main.errorDatabaseConnection": "Błąd zewnętrzny.<br>Błąd połączenia z bazą danych. W przypadku wystąpienia błędu należy skontaktować się z pomocą techniczną.", "SSE.Controllers.Main.errorDataEncrypted": "<PERSON><PERSON><PERSON><PERSON><PERSON> z<PERSON>, nie można ich odszyfrować.", "SSE.Controllers.Main.errorDataRange": "Błędny zakres danych.", "SSE.Controllers.Main.errorDataValidate": "Wprowadzon<PERSON> wartość jest nieprawidłowa.<br>Użytkownik ma ograniczone wartości, które można wprowadzić w tej komórce.", "SSE.Controllers.Main.errorDefaultMessage": "Kod błędu: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Próbujesz usunąć kolumnę zawierającą zablokowaną komórkę. Zablokowanych komórek nie można usunąć, gdy arkusz jest chroniony.<br><PERSON><PERSON> usunąć zablokowaną komórkę, usuń ochronę arkusza. Możesz zostać poproszony o podanie hasła.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Próbujesz usunąć wiersz zawierający zablokowaną komórkę. Zablokowanych komórek nie można usun<PERSON>, gdy arkusz jest chroniony.<br><PERSON><PERSON> usun<PERSON>ć zablokowaną komórkę, usuń ochronę arkusza. Możesz zostać poproszony o podanie hasła.", "SSE.Controllers.Main.errorDependentsNoFormulas": "The Trace Dependents command found no formulas that refer to the active cell.", "SSE.Controllers.Main.errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "SSE.Controllers.Main.errorEditingDownloadas": "Wystąpił błąd podczas pracy z dokumentem.<br><PERSON><PERSON><PERSON><PERSON> op<PERSON> \"Pobierz jako\", aby zapisać kopię zapasową pliku na dysku twardym komputera.", "SSE.Controllers.Main.errorEditingSaveas": "Wystą<PERSON>ł błąd podczas pracy z dokumentem.<br><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> \"Zapisz kopię jako...\", aby zapisać kopię zapasową pliku na dysku twardym komputera.", "SSE.Controllers.Main.errorEditView": "Nie można edytowa<PERSON> istniejące widoku arkusza, ani w tej chwili nie można utworzyć nowych, ponieważ niektóre z nich są w trakcie edycji.", "SSE.Controllers.Main.errorEmailClient": "Nie znaleziono klienta poczty e-mail.", "SSE.Controllers.Main.errorFilePassProtect": "Plik jest chroniony hasłem, bez którego nie może by<PERSON> ot<PERSON>.", "SSE.Controllers.Main.errorFileRequest": "Błąd zewnętrzny.<br>B<PERSON><PERSON>d zgłoszenia pliku. W przypadku wystąpienia błędu należy skontaktować się z pomocą techniczną.", "SSE.Controllers.Main.errorFileSizeExceed": "Rozmiar pliku przekracza ustalony limit dla twojego serwera.<br><PERSON><PERSON><PERSON> skontaktowa<PERSON> z administratorem twojego Serwera Dokumentów w celu uzyskania szczegółowych informacji.", "SSE.Controllers.Main.errorFileVKey": "Błąd zewnętrzny.<br>Ni<PERSON><PERSON>ściwy klucz bezpieczeństwa. W przypadku wystąpienia błędu należy skontaktować się z pomocą techniczną.", "SSE.Controllers.Main.errorFillRange": "Nie można wypełnić zaznaczonego zakresu komórek.<br>Wszystkie scalone komórki muszą mieć ten sam rozmiar.", "SSE.Controllers.Main.errorForceSave": "Wystąpił błąd podczas zapisywania pliku. Użyj opcji \"Pobierz jako\", aby zapisać plik na dysku twardym komputera lub spróbuj ponownie później.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Cannot enter a formula for an item or field name in a pivot table report.", "SSE.Controllers.Main.errorFormulaName": "Wprowadzona formuła jest błędna.<br><PERSON><PERSON><PERSON><PERSON> niepoprawnej nazwy formuły.", "SSE.Controllers.Main.errorFormulaParsing": "Błąd wewnętrzny podczas analizowania formuły.", "SSE.Controllers.Main.errorFrmlMaxLength": "Długość formuły przekracza limit 8192 znaków.<br>Edytuj ją i spróbuj ponownie.", "SSE.Controllers.Main.errorFrmlMaxReference": "<PERSON><PERSON> moż<PERSON><PERSON> wprowadzić tej formuły, poniew<PERSON>ż zawiera ona zbyt wiele wartości, <br> odwołań do komórek i/lub nazw.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Wartości tekstowe w formułach nie mogą przekraczać 255 znaków. <br> <PERSON><PERSON><PERSON>j funk<PERSON> ZŁĄCZ.TEKSTY lub operatora łączenia (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Funkcja odnosi się do arkusza, kt<PERSON><PERSON> nie istnieje.<br><PERSON><PERSON><PERSON> sprawdzić dane i spróbować ponownie.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Operacja nie może zostać zakończona dla wybranego zakresu komórek.<br> <PERSON><PERSON><PERSON><PERSON>, tak aby pierwszy wiersz tabeli znajdował się w tym samym wierszu, a tabela wynikowa pokrywała się z bieżącym.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Nie można zakończyć operacji dla wybranego zakresu komórek.<br><PERSON><PERSON><PERSON><PERSON>, kt<PERSON><PERSON> nie zawiera innych tabel.", "SSE.Controllers.Main.errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "SSE.Controllers.Main.errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> błąd podczas otwierania pliku.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pliku odpowiada arkuszowi kalkulacyjnemu (np. xlsx), ale plik ma nieprawidłowe rozszerzenie: %1.", "SSE.Controllers.Main.errorInvalidRef": "Wprowadź prawidłową nazwę dla wyboru lub prawidłowe odniesienie, aby prz<PERSON> do tego.", "SSE.Controllers.Main.errorKeyEncrypt": "Nieznany deskryptor klu<PERSON>a", "SSE.Controllers.Main.errorKeyExpire": "Okres ważności deskryptora klucza wygasł", "SSE.Controllers.Main.errorLabledColumnsPivot": "Aby utworz<PERSON>ć tabelę przestawną, użyj danych uporządkowanych na liście z nagłówkami kolumn.", "SSE.Controllers.Main.errorLoadingFont": "Czcionki nie zostały załadowane.<br>Skontaktuj się z administratorem usługi Document Server.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Nieprawidłowe odniesienie do lokalizacji lub zakresu danych.", "SSE.Controllers.Main.errorLockedAll": "Operacja nie mogła zostać wykonana, gdy ark<PERSON>z został zablokowany przez innego użytkownika.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "One of the cells involved in the goal seek process has been modified by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "Nie można zmienić dane w tabeli przestawnej.", "SSE.Controllers.Main.errorLockedWorksheetRename": "<PERSON><PERSON> można zmienić nazwy arkusza, g<PERSON> zmie<PERSON>a jest ona przez innego użytkownika", "SSE.Controllers.Main.errorMaxPoints": "Maksymalna liczba punktów w serii na wykres to 4096.", "SSE.Controllers.Main.errorMoveRange": "Nie można zmienić części scalonej komórki", "SSE.Controllers.Main.errorMoveSlicerError": "Fragmentatorów tabeli nie można kopiować z jednego skoroszytu do drugiego.<br>Spróbuj ponownie, zaznaczając całą tabelę i fragmentatory.", "SSE.Controllers.Main.errorMultiCellFormula": "Wielokomórkowe formuły tablicowe nie są dozwolone w tabelach.", "SSE.Controllers.Main.errorNoDataToParse": "Nie wybrano żadnych danych do analizy.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.Main.errorOpenWarning": "Długość jednej z formuł w pliku przekroczyła limit 8192 znaków.<br>Formuła została usunięta.", "SSE.Controllers.Main.errorOperandExpected": "Wprowadzona składnia funkcji jest nieprawidłowa. Sprawdź, czy nie brakuje jednego z nawiasów - '(' lub ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "Podane hasło jest nieprawidłowe.<br><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> klawisz CAPS LOCK jest wyłączony i użyj prawidłowej wielkości liter.", "SSE.Controllers.Main.errorPasteInPivot": "We can't make this change for the selected cells because it will affect a pivot table.<br>Use the field list to change the report.", "SSE.Controllers.Main.errorPasteMaxRange": "<PERSON><PERSON>zar kopiowania i wklejania nie pasuje.<br>Proszę zaznacz obszar o takim samym rozmiarze lub wybierz pierwszą komórkę w rzędzie do wklejenia skopiowanych komórek.", "SSE.Controllers.Main.errorPasteMultiSelect": "To działanie nie dotyczy wielu wybranych zakresów. <br> Zaznacz jeden zakres i spróbuj ponownie.", "SSE.Controllers.Main.errorPasteSlicerError": "Fragmentatorów tabel nie można kopiować z jednego skoroszytu do drugiego.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Pivot table field name already exists.", "SSE.Controllers.Main.errorPivotGroup": "<PERSON>e można pogrupować tego wyboru.", "SSE.Controllers.Main.errorPivotOverlap": "Raport tabeli przestawnej nie może nachodzić na tabelę.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Raport tabeli przestawnej został zapisany bez danych źródłowych.<br><PERSON><PERSON><PERSON>j przycis<PERSON> \"Odśwież\", a<PERSON> z<PERSON><PERSON><PERSON><PERSON> raport.", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "The Trace Precedents command requires that the active cell contain a formula which includes a valid references.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Niestety w bieżącej wersji programu nie można wydrukować więcej niż 1500 stron naraz.<br>To ograniczenie zostanie usunięte w przyszłych wersjach.", "SSE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.errorProtectedRange": "This range is not allowed for editing.", "SSE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "SSE.Controllers.Main.errorServerVersion": "Wersja edytora została zaktualizowana. Strona zostanie ponownie załadowana, aby z<PERSON><PERSON><PERSON><PERSON> z<PERSON>.", "SSE.Controllers.Main.errorSessionAbsolute": "Sesja edycji dokumentu wygasła. Proszę ponownie załadować stronę.", "SSE.Controllers.Main.errorSessionIdle": "Dokument nie był edytowany przez długi czas. Proszę ponownie załadować stronę.", "SSE.Controllers.Main.errorSessionToken": "Połączenie z serwerem zostało przerwane. Proszę ponownie załadować stronę.", "SSE.Controllers.Main.errorSetPassword": "<PERSON><PERSON> m<PERSON><PERSON><PERSON> hasła", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Odniesienie do lokalizacji jest nieprawidłowe, poniew<PERSON>ż nie wszystkie komórki znajdują się w tej samej kolumnie lub wierszu.<br>Zaznacz komórki, które znajdują się w jednej kolumnie lub wierszu.", "SSE.Controllers.Main.errorStockChart": "Nieprawidłowa kolejność wierszy. <PERSON><PERSON> zbu<PERSON><PERSON> wykres akcji, umie<PERSON><PERSON> dane na arkuszu w następującej kolejności:<br> cena <PERSON><PERSON>, cena ma<PERSON>, cena <PERSON>, cena zam<PERSON>.", "SSE.Controllers.Main.errorToken": "Token bezpieczeństwa dokumentu jest nieprawidłowo uformowany.<br>Prosimy o kontakt z <PERSON>em serwera dokumentów.", "SSE.Controllers.Main.errorTokenExpire": "Token zabezpieczeń dokumentu wygasł.<br>Prosimy o kontakt z <PERSON>em serwera dokumentów.", "SSE.Controllers.Main.errorUnexpectedGuid": "Błąd zewnętrzny.<br>Niespodziewany identyfikator GUID. W przypadku wystąpienia błędu należy skontaktować się z pomocą techniczną.", "SSE.Controllers.Main.errorUpdateVersion": "Wersja pliku została zmieniona. Strona zostanie ponownie załadowana.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Połączenie z internetem zostało odzyskane, a wersja pliku uległa zmianie.<br><PERSON><PERSON><PERSON> b<PERSON> mógł kont<PERSON> pracę, mus<PERSON>z pobrać plik albo skopiować jego <PERSON>, aby <PERSON><PERSON><PERSON>, że nic nie zostało utracone, a następnie odświeżyć stronę.", "SSE.Controllers.Main.errorUserDrop": "Nie można uzyskać dostępu do tego pliku.", "SSE.Controllers.Main.errorUsersExceed": "Przekroczono liczbę dozwolonych użytkowników określonych przez plan cenowy wersji", "SSE.Controllers.Main.errorViewerDisconnect": "Połączenie zostało utracone. Nadal moż<PERSON><PERSON> p<PERSON> dokument,<br>ale nie będ<PERSON>sz mógł pobrać ani wydrukować dokumentu do momentu przywrócenia połączenia.", "SSE.Controllers.Main.errorWrongBracketsCount": "Wprowadzona formuła jest błędna.<br><PERSON><PERSON><PERSON><PERSON> niepoprawnej liczby klamr.", "SSE.Controllers.Main.errorWrongOperator": "Wprowadzona formuła jest błędna. Został użyty błędny operator.<br><PERSON><PERSON><PERSON> błąd.", "SSE.Controllers.Main.errorWrongPassword": "Podane hasło jest nieprawidłowe.", "SSE.Controllers.Main.errRemDuplicates": "Znaleziono i usunięto zduplikowane wartości: {0}, pozostały unikalne wartości: {1}.", "SSE.Controllers.Main.leavePageText": "Masz niezapisane zmiany w tym arkuszu kalkulacyjnym. Kliknij przycisk \"Zostań na tej stronie\", a następnie \"Zapisz\", aby je zapisać. Kliknij przycisk \"Porzuć tę stronę\", aby usunąć wszystkie niezapisane zmiany.", "SSE.Controllers.Main.leavePageTextOnClose": "Wszystkie niezapisane zmiany w tym arkuszu kalkulacyjnym zostaną utracone.<br> <PERSON><PERSON><PERSON><PERSON> \"Anuluj\", a następnie \"Zapisz\", aby je zapisa<PERSON>. Kliknij \"OK\", aby <PERSON><PERSON><PERSON><PERSON><PERSON> wszystkie niezapisane zmiany.", "SSE.Controllers.Main.loadFontsTextText": "Ładowanie danych...", "SSE.Controllers.Main.loadFontsTitleText": "Łado<PERSON><PERSON> danych", "SSE.Controllers.Main.loadFontTextText": "Ładowanie danych...", "SSE.Controllers.Main.loadFontTitleText": "Łado<PERSON><PERSON> danych", "SSE.Controllers.Main.loadImagesTextText": "Ładowanie obrazów...", "SSE.Controllers.Main.loadImagesTitleText": "Ładowanie obrazów", "SSE.Controllers.Main.loadImageTextText": "Ładowanie obrazu...", "SSE.Controllers.Main.loadImageTitleText": "Ładowanie obrazu", "SSE.Controllers.Main.loadingDocumentTitleText": "Ładowanie arkusza <PERSON>ego", "SSE.Controllers.Main.notcriticalErrorTitle": "Ostrzeżenie", "SSE.Controllers.Main.openErrorText": "Wys<PERSON>ą<PERSON>ł błąd podczas otwierania pliku.", "SSE.Controllers.Main.openTextText": "Otwieranie arkusza kalkulacyjnego...", "SSE.Controllers.Main.openTitleText": "Otwieranie arkusza kalkulacyjnego", "SSE.Controllers.Main.pastInMergeAreaError": "Nie można zmienić części scalonej komórki", "SSE.Controllers.Main.printTextText": "Drukowanie arkusza kalkulacyjnego...", "SSE.Controllers.Main.printTitleText": "Drukowanie arkusza <PERSON>jnego", "SSE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.requestEditFailedMessageText": "Ktoś właśnie edytuje ten dokument. Proszę spróbuj później.", "SSE.Controllers.Main.requestEditFailedTitleText": "Odmo<PERSON> dos<PERSON>ępu", "SSE.Controllers.Main.saveErrorText": "Wys<PERSON>ą<PERSON>ł błąd podczas zapisywania pliku.", "SSE.Controllers.Main.saveErrorTextDesktop": "<PERSON>e można zapisać lub utwo<PERSON><PERSON>ć tego pliku.<br>Możliwe przyczyny to: <br>1. <PERSON>lik jest tylko do odczytu. <br>2. Plik jest edytowany przez innych użytkowników. <br>3. <PERSON><PERSON><PERSON> jest pełny lub uszkodzony.", "SSE.Controllers.Main.saveTextText": "Zapisywanie arkusza kalkulacyjnego...", "SSE.Controllers.Main.saveTitleText": "Zapisywanie arkusza <PERSON>jnego", "SSE.Controllers.Main.scriptLoadError": "Połączenie jest zbyt wolne, niektóre komponenty mogły nie zostać załadowane. Proszę odświeżyć stronę.", "SSE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textApplyAll": "Zastosuj do wszystkich równań", "SSE.Controllers.Main.textBuyNow": "Od<PERSON><PERSON><PERSON> stronę web", "SSE.Controllers.Main.textChangesSaved": "Wszystkie zmiany zapisane", "SSE.Controllers.Main.textClose": "Zamknij", "SSE.Controllers.Main.textCloseTip": "Kliknij, żeby zamknąć wskazówkę", "SSE.Controllers.Main.textConfirm": "Potwierdzenie", "SSE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "SSE.Controllers.Main.textContactUs": "Skontaktuj się z działem sprzedaży", "SSE.Controllers.Main.textContinue": "Continue", "SSE.Controllers.Main.textConvertEquation": "To równanie zostało utworzone za pomocą starej wersji edytora równań, kt<PERSON>ra nie jest już obsługiwana. <PERSON><PERSON> je edyt<PERSON>, przekonwertuj równanie na format Office Math ML.<br>Przekonwertować teraz?", "SSE.Controllers.Main.textCustomLoader": "<PERSON><PERSON><PERSON><PERSON>, że zgodnie z warunkami licencji nie jesteś uprawniony do zmiany ładowania. <br> W celu uzyskania wyceny prosimy o kontakt z naszym Działem Sprzedaży.", "SSE.Controllers.Main.textDisconnect": "Połączenie zostało utracone", "SSE.Controllers.Main.textFillOtherRows": "Fill other rows", "SSE.Controllers.Main.textFormulaFilledAllRows": "Formula filled {0} rows have data. Filling other empty rows may take a few minutes.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Formula filled first {0} rows. Filling other empty rows may take a few minutes.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Formuła wypełniona tylko pierwszymi wierszami {0} zawiera dane według przyczyny zapisania w pamięci. W tym arkuszu znajdują się inne {1} wiersze zawierające dane. Możesz je wypełnić ręcznie.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Formuła wypełniła tylko pierwsze {0} w<PERSON><PERSON> według przyczyny zapisania w pamięci. Inne wiersze w tym arkuszu nie zawierają danych.", "SSE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textHasMacros": "Plik zawiera automatyczne makra. <br> <PERSON><PERSON><PERSON>z uruchomić makra?", "SSE.Controllers.Main.textKeep": "Keep", "SSE.Controllers.Main.textLearnMore": "Dowiedz się więcej", "SSE.Controllers.Main.textLoadingDocument": "Ładowanie arkusza <PERSON>ego", "SSE.Controllers.Main.textLongName": "Wpisz nazwę krótszą niż 128 znaków.", "SSE.Controllers.Main.textNeedSynchronize": "Masz aktualizacje", "SSE.Controllers.Main.textNo": "<PERSON><PERSON>", "SSE.Controllers.Main.textNoLicenseTitle": "Osiągnięto limit licencji", "SSE.Controllers.Main.textPaidFeature": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textPleaseWait": "Operacja może potrwać dłużej niż oczekiwano. Proszę cze<PERSON>...", "SSE.Controllers.Main.textReconnect": "Połączenie przywrócone", "SSE.Controllers.Main.textRemember": "Zapamiętaj mój wybór dla wszystkich plików", "SSE.Controllers.Main.textRememberMacros": "Remember my choice for all macros", "SSE.Controllers.Main.textRenameError": "Nazwa użytkownika nie może być pusta.", "SSE.Controllers.Main.textRenameLabel": "Wpisz nazwę, która ma być używana do współpracy", "SSE.Controllers.Main.textReplace": "Replace", "SSE.Controllers.Main.textRequestMacros": "A macro makes a request to URL. Do you want to allow the request to the %1?", "SSE.Controllers.Main.textShape": "Kształt", "SSE.Controllers.Main.textStrict": "<PERSON><PERSON>", "SSE.Controllers.Main.textText": "Tekst", "SSE.Controllers.Main.textTryQuickPrint": "Wybrano opcję Szybkie drukowanie: cały dokument zostanie wydrukowany na ostatnio wybranej lub domyślnej drukarce.<br><PERSON><PERSON><PERSON> kontynuować?", "SSE.Controllers.Main.textTryUndoRedo": "Funkcje Cofnij/Ponów są wyłączone w trybie \"<PERSON><PERSON>b<PERSON>\" współtworzenia.<br><PERSON><PERSON><PERSON><PERSON> przy<PERSON> \"Tryb ścisły\", aby przejść do trybu ścisłego edytowania, aby edytować plik bez ingerencji innych użytkowników i wysyłać zmiany tylko po zapisaniu. Możesz przełączać się między trybami współtworzenia, używając edytora Ustawienia zaawansowane.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Funkcje Cofnij/Ponów są wyłączone w trybie Szybkim współtworzenia.", "SSE.Controllers.Main.textUndo": "Undo", "SSE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "SSE.Controllers.Main.textUpdating": "Updating", "SSE.Controllers.Main.textYes": "Tak", "SSE.Controllers.Main.titleLicenseExp": "Upłynął okres ważności licencji", "SSE.Controllers.Main.titleLicenseNotActive": "License not active", "SSE.Controllers.Main.titleServerVersion": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.titleUpdateVersion": "Version changed", "SSE.Controllers.Main.txtAccent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtAll": "(wszystko)", "SSE.Controllers.Main.txtArt": "Twój tekst tutaj", "SSE.Controllers.Main.txtBasicShapes": "Kształty podstawowe", "SSE.Controllers.Main.txtBlank": "(<PERSON><PERSON><PERSON>)", "SSE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtByField": "%1 z %2", "SSE.Controllers.Main.txtCallouts": "Objaśnienia", "SSE.Controllers.Main.txtCharts": "Wykresy", "SSE.Controllers.Main.txtClearFilter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> filtr", "SSE.Controllers.Main.txtColLbls": "Etykiety kolumn", "SSE.Controllers.Main.txtColumn": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtConfidential": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtDate": "Data", "SSE.Controllers.Main.txtDays": "dni", "SSE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtEditingMode": "Ustaw tryb edycji...", "SSE.Controllers.Main.txtErrorLoadHistory": "Ładowanie historii nie powiodło się", "SSE.Controllers.Main.txtFiguredArrows": "Strz<PERSON>ł<PERSON>", "SSE.Controllers.Main.txtFile": "Plik", "SSE.Controllers.Main.txtGrandTotal": "<PERSON><PERSON>", "SSE.Controllers.Main.txtGroup": "Grupuj", "SSE.Controllers.Main.txtHours": "god<PERSON>(y)", "SSE.Controllers.Main.txtInfo": "Info", "SSE.Controllers.Main.txtLines": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMath": "Matematyczne", "SSE.Controllers.Main.txtMinutes": "minut(y)", "SSE.Controllers.Main.txtMonths": "<PERSON><PERSON><PERSON><PERSON>(e)", "SSE.Controllers.Main.txtMultiSelect": "Wybór wielokrotny", "SSE.Controllers.Main.txtNone": "None", "SSE.Controllers.Main.txtOr": "%1 lub %2", "SSE.Controllers.Main.txtPage": "Strona", "SSE.Controllers.Main.txtPageOf": "Strona %1 z %2", "SSE.Controllers.Main.txtPages": "<PERSON><PERSON>", "SSE.Controllers.Main.txtPicture": "Picture", "SSE.Controllers.Main.txtPivotTable": "PivotTable", "SSE.Controllers.Main.txtPreparedBy": "Przygotowane przez:", "SSE.Controllers.Main.txtPrintArea": "O<PERSON><PERSON>_wydruku", "SSE.Controllers.Main.txtQuarter": "Qtr", "SSE.Controllers.Main.txtQuarters": "Kwartały", "SSE.Controllers.Main.txtRectangles": "Prostokąty", "SSE.Controllers.Main.txtRow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRowLbls": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "SSE.Controllers.Main.txtScheme_Aspect": "Aspect", "SSE.Controllers.Main.txtScheme_Blue": "Blue", "SSE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "SSE.Controllers.Main.txtScheme_Blue_II": "Blue II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "SSE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "SSE.Controllers.Main.txtScheme_Green": "Green", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "SSE.Controllers.Main.txtScheme_Marquee": "Marquee", "SSE.Controllers.Main.txtScheme_Median": "Median", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Orange", "SSE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "SSE.Controllers.Main.txtScheme_Paper": "Paper", "SSE.Controllers.Main.txtScheme_Red": "Red", "SSE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "SSE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "SSE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "SSE.Controllers.Main.txtScheme_Violet": "Violet", "SSE.Controllers.Main.txtScheme_Violet_II": "Violet II", "SSE.Controllers.Main.txtScheme_Yellow": "Yellow", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "SSE.Controllers.Main.txtSeconds": "Sekundy", "SSE.Controllers.Main.txtSeries": "Serie", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Objaśnienie: linia z obramowaniem i paskiem wyróżniającym", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Objaśnienie: wygięta linia z obramowaniem i paskiem wyróżniającym", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Objaśnienie: podwójna wygięta linia z obramowaniem i paskiem wyróżniającym", "SSE.Controllers.Main.txtShape_accentCallout1": "Objaśnienie: linia z paskiem wyróżniającym", "SSE.Controllers.Main.txtShape_accentCallout2": "Objaśnienie: wygięta linia z paskiem wyróżniającym", "SSE.Controllers.Main.txtShape_accentCallout3": "Objaśnienie: podwójna wygięta linia z paskiem wyróżniającym", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Przycisk poprzedni", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Przycisk rozpoczęcia", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Pusty przycisk", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Przycisk dokument", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Przycisk zakończenia ", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Przycisk następny", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Przycisk pomocy", "SSE.Controllers.Main.txtShape_actionButtonHome": "Przycisk dom", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Przycisk informacji", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Przycisk wideo", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Przycisk powrotu", "SSE.Controllers.Main.txtShape_actionButtonSound": "Przycisk dźwięku", "SSE.Controllers.Main.txtShape_arc": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentArrow": "Strzałka: wygięta", "SSE.Controllers.Main.txtShape_bentConnector5": "Łącznik: łamany", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Łącznik: łamany ze strzałką", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Łącznik: łamany z podwójną strzałką", "SSE.Controllers.Main.txtShape_bentUpArrow": "Strzałka: wygięta w górę", "SSE.Controllers.Main.txtShape_bevel": "Prostokąt: ze skosem", "SSE.Controllers.Main.txtShape_blockArc": "<PERSON><PERSON> blokowy", "SSE.Controllers.Main.txtShape_borderCallout1": "Objaśnienie: linia", "SSE.Controllers.Main.txtShape_borderCallout2": "Objaśnienie: wygięta linia", "SSE.Controllers.Main.txtShape_borderCallout3": "Objaśnienie: podwójna wygięta linia", "SSE.Controllers.Main.txtShape_bracePair": "Para nawiasów klamrowych", "SSE.Controllers.Main.txtShape_callout1": "Objaśnienie: linia bez obramowania", "SSE.Controllers.Main.txtShape_callout2": "Objaśnienie: wygięta linia bez obramowania", "SSE.Controllers.Main.txtShape_callout3": "Objaśnienie: podwójna wygięta linia bez obramowania", "SSE.Controllers.Main.txtShape_can": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_chevron": "Strzałka: pagon", "SSE.Controllers.Main.txtShape_chord": "Odcinek koła", "SSE.Controllers.Main.txtShape_circularArrow": "Strzałka: kolista", "SSE.Controllers.Main.txtShape_cloud": "Chmurka", "SSE.Controllers.Main.txtShape_cloudCallout": "Dymek <PERSON>śli: chmurka", "SSE.Controllers.Main.txtShape_corner": "Kształt litery L", "SSE.Controllers.Main.txtShape_cube": "S<PERSON>ś<PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Łącznik: zakrzywiony", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Łącznik: zakrzywiony ze strzałką", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Łącznik: zakrzywiony z podwójną strzałką", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Strzałka: zakrzywiona w dół", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Strzałka: zakrzywiona w lewo", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Strzałka: zakrzywiona w prawo", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Strzałka: zakrzywiona w górę", "SSE.Controllers.Main.txtShape_decagon": "Dziesięciokąt", "SSE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_diamond": "Romb", "SSE.Controllers.Main.txtShape_dodecagon": "Dwunastoką<PERSON>", "SSE.Controllers.Main.txtShape_donut": "Okrąg: pusty", "SSE.Controllers.Main.txtShape_doubleWave": "Podwójna fala", "SSE.Controllers.Main.txtShape_downArrow": "Strzałka: w dół", "SSE.Controllers.Main.txtShape_downArrowCallout": "Objaśnienie: strzałka w dół", "SSE.Controllers.Main.txtShape_ellipse": "Elipsa", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Wstęga: zakrzywiona i nachylona w dół", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Wstęga: zakrzywiona i nachylona w górę", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Schemat blokowy: proces alternatywny", "SSE.Controllers.Main.txtShape_flowChartCollate": "Schemat blokowy: zestawienie", "SSE.Controllers.Main.txtShape_flowChartConnector": "Schemat blokowy: łącznik", "SSE.Controllers.Main.txtShape_flowChartDecision": "Schemat blokowy: decyzja", "SSE.Controllers.Main.txtShape_flowChartDelay": "Schemat blokowy: opóźnienie", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Schemat blokowy: ekran", "SSE.Controllers.Main.txtShape_flowChartDocument": "Schemat blokowy: dokument", "SSE.Controllers.Main.txtShape_flowChartExtract": "Schemat blokowy: wyodrębnianie", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Schemat blokowy: decyzja", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Schemat blokowy: p<PERSON><PERSON><PERSON> wewnętrzna", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Schemat blokowy: dysk magnetyczny", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Schemat blokowy: pamięć o dostępie bezpośrednim", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Schemat blokowy: pamięć o dostępie sekwencyjnym", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Schemat blokowy: ręczne wprowadzenie danych", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Schemat blokowy: operacja ręczna", "SSE.Controllers.Main.txtShape_flowChartMerge": "Schemat blokowy: scalanie", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Schemat blokowy: wiele dokumentów", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Schemat blokowy: łącznik międzystronicowy", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Schemat blokowy: przechowywane dane", "SSE.Controllers.Main.txtShape_flowChartOr": "Schemat blokowy: lub", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Schemat blokowy: proces uprzednio zdefiniowany", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Schemat blokowy: przygotowanie", "SSE.Controllers.Main.txtShape_flowChartProcess": "Schemat blokowy: proces", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Schemat blokowy: karta", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Schemat blokowy: taśma dziurkowana", "SSE.Controllers.Main.txtShape_flowChartSort": "Schemat blokowy: sortowanie", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Schemat blokowy: operacja sumowania", "SSE.Controllers.Main.txtShape_flowChartTerminator": "<PERSON>hemat blokowy: terminator", "SSE.Controllers.Main.txtShape_foldedCorner": "Prostokąt: zagięty narożnik", "SSE.Controllers.Main.txtShape_frame": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_halfFrame": "Połowa ramki", "SSE.Controllers.Main.txtShape_heart": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heptagon": "Siedmiokąt", "SSE.Controllers.Main.txtShape_hexagon": "Sześciokąt", "SSE.Controllers.Main.txtShape_homePlate": "Pięcioką<PERSON>", "SSE.Controllers.Main.txtShape_horizontalScroll": "Zwój: poziomy", "SSE.Controllers.Main.txtShape_irregularSeal1": "Eksplozja 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Eksplozja 2", "SSE.Controllers.Main.txtShape_leftArrow": "Strzałka: w lewo", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Objaśnienie: strzałka w lewo", "SSE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON><PERSON> otwierający", "SSE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftRightArrow": "Strzałka: w lewo i w prawo", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Objaśnienie: strzałka w lewo i w prawo", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Strzałka: w lewo wprawo i w górę", "SSE.Controllers.Main.txtShape_leftUpArrow": "Strzałka: w lewo i w górę", "SSE.Controllers.Main.txtShape_lightningBolt": "Błyskawica", "SSE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "Strzałka liniowa", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Strzałka liniowa: podwójna", "SSE.Controllers.Main.txtShape_mathDivide": "Znak dzielenia", "SSE.Controllers.Main.txtShape_mathEqual": "Równy", "SSE.Controllers.Main.txtShape_mathMinus": "Znak minus", "SSE.Controllers.Main.txtShape_mathMultiply": "Znak mnożenia", "SSE.Controllers.Main.txtShape_mathNotEqual": "Różny od", "SSE.Controllers.Main.txtShape_mathPlus": "Znak plus", "SSE.Controllers.Main.txtShape_moon": "Księżyc", "SSE.Controllers.Main.txtShape_noSmoking": "Symbol \"nie\"", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Strzałka: w prawo z wcięciem", "SSE.Controllers.Main.txtShape_octagon": "Ośmiokąt", "SSE.Controllers.Main.txtShape_parallelogram": "Równoległobok", "SSE.Controllers.Main.txtShape_pentagon": "Pięcioką<PERSON>", "SSE.Controllers.Main.txtShape_pie": "Wycinek okręgu", "SSE.Controllers.Main.txtShape_plaque": "Plakietka", "SSE.Controllers.Main.txtShape_plus": "Znak plus", "SSE.Controllers.Main.txtShape_polyline1": "Dowolny kształt: bazgroły", "SSE.Controllers.Main.txtShape_polyline2": "Dowolny kształt: kształt", "SSE.Controllers.Main.txtShape_quadArrow": "Strzałka: w cztery strony", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Objaśnienie: strzałka w cztery strony", "SSE.Controllers.Main.txtShape_rect": "Prostokąt", "SSE.Controllers.Main.txtShape_ribbon": "Wstęga: nachylona w dół", "SSE.Controllers.Main.txtShape_ribbon2": "Wstęga: nachylona w górę", "SSE.Controllers.Main.txtShape_rightArrow": "Strzałka: w prawo", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Objaśnienie: strzałka w prawo", "SSE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON><PERSON>rowy zamykający", "SSE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_round1Rect": "Prostokąt: jeden zaokrąglony róg", "SSE.Controllers.Main.txtShape_round2DiagRect": "Prostokąt: zaokrąglone rogi po przekątnej", "SSE.Controllers.Main.txtShape_round2SameRect": "Prostokąt: zaokrąglone rogi u góry", "SSE.Controllers.Main.txtShape_roundRect": "Prostokąt: zaokrąglone rogi", "SSE.Controllers.Main.txtShape_rtTriangle": "Trójkąt równoramienny", "SSE.Controllers.Main.txtShape_smileyFace": "Uśmiechnię<PERSON> buźka", "SSE.Controllers.Main.txtShape_snip1Rect": "Prostokąt: jeden <PERSON> róg ", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Prostokąt: ścięte rogi po przekątnej", "SSE.Controllers.Main.txtShape_snip2SameRect": "Prostokąt: ścięte rogi u góry", "SSE.Controllers.Main.txtShape_snipRoundRect": "Prostokąt: zaokrąglony róg i ścięty róg u góry", "SSE.Controllers.Main.txtShape_spline": "Krzywa", "SSE.Controllers.Main.txtShape_star10": "10-<PERSON><PERSON><PERSON>wiazda", "SSE.Controllers.Main.txtShape_star12": "12-<PERSON><PERSON><PERSON> Gwiazda", "SSE.Controllers.Main.txtShape_star16": "16-<PERSON><PERSON><PERSON> Gwiazda", "SSE.Controllers.Main.txtShape_star24": "24-<PERSON><PERSON><PERSON> Gwiazda", "SSE.Controllers.Main.txtShape_star32": "32-<PERSON><PERSON><PERSON> Gwiazda", "SSE.Controllers.Main.txtShape_star4": "4-Ram<PERSON><PERSON> Gwiazda", "SSE.Controllers.Main.txtShape_star5": "5-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star6": "6-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star7": "7-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star8": "8-<PERSON><PERSON><PERSON> Gwiazda", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Strzałka: prążkowana w prawo", "SSE.Controllers.Main.txtShape_sun": "Sł<PERSON>cz<PERSON>", "SSE.Controllers.Main.txtShape_teardrop": "Ł<PERSON>", "SSE.Controllers.Main.txtShape_textRect": "Pole tekstowe", "SSE.Controllers.Main.txtShape_trapezoid": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_triangle": "Trójkąt równoramienny", "SSE.Controllers.Main.txtShape_upArrow": "Strzałka: w górę", "SSE.Controllers.Main.txtShape_upArrowCallout": "Objaśnienie: strzałka w górę", "SSE.Controllers.Main.txtShape_upDownArrow": "Strzałka: w górę i w dół", "SSE.Controllers.Main.txtShape_uturnArrow": "Strzałka: zawracanie", "SSE.Controllers.Main.txtShape_verticalScroll": "Zwój: pionowy", "SSE.Controllers.Main.txtShape_wave": "Fala", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Dymek mowy: owalny", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Dymek mowy: prostokąt", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Dymek mowy: prostokąt z zaokrąglonymi rogami", "SSE.Controllers.Main.txtSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSlicer": "<PERSON>licer", "SSE.Controllers.Main.txtStarsRibbons": "Gwiazdy i wstążki", "SSE.Controllers.Main.txtStyle_Bad": "Zły", "SSE.Controllers.Main.txtStyle_Calculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Check_Cell": "Sprawdź komórkę", "SSE.Controllers.Main.txtStyle_Comma": "P<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Tekst wyjaśniający", "SSE.Controllers.Main.txtStyle_Good": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Heading_1": "Nagłówek 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Nagłówek 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Nagłówek 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Nagłówek 4", "SSE.Controllers.Main.txtStyle_Input": "<PERSON>jś<PERSON>", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Połączona komórka", "SSE.Controllers.Main.txtStyle_Neutral": "Neutralny", "SSE.Controllers.Main.txtStyle_Normal": "Normalny", "SSE.Controllers.Main.txtStyle_Note": "Notka", "SSE.Controllers.Main.txtStyle_Output": "Wyjście", "SSE.Controllers.Main.txtStyle_Percent": "Procent", "SSE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Total": "Wszystkie", "SSE.Controllers.Main.txtStyle_Warning_Text": "Tekst ostrzegawczy", "SSE.Controllers.Main.txtTab": "Karta", "SSE.Controllers.Main.txtTable": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtTime": "Czas", "SSE.Controllers.Main.txtUnlock": "O<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtUnlockRange": "Odb<PERSON><PERSON><PERSON><PERSON> zak<PERSON>", "SSE.Controllers.Main.txtUnlockRangeDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasło, aby zmienić ten zakres:", "SSE.Controllers.Main.txtUnlockRangeWarning": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON>, jest chroniony hasłem.", "SSE.Controllers.Main.txtValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtView": "View", "SSE.Controllers.Main.txtXAxis": "Oś X", "SSE.Controllers.Main.txtYAxis": "<PERSON><PERSON>", "SSE.Controllers.Main.txtYears": "lata", "SSE.Controllers.Main.unknownErrorText": "Nieznany błąd.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Twoja przeglądarka nie jest wspierana.", "SSE.Controllers.Main.uploadDocExtMessage": "Nieznany format dokumentu.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Nie przesłano żadnych dokumentów.", "SSE.Controllers.Main.uploadDocSizeMessage": "Przekroczono maksymalny rozmiar dokumentu.", "SSE.Controllers.Main.uploadImageExtMessage": "Nieznany format obrazu.", "SSE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON>.", "SSE.Controllers.Main.uploadImageSizeMessage": "Obraz jest za duży. Maksymalny rozmiar to 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "Wysyłanie obrazu...", "SSE.Controllers.Main.uploadImageTitleText": "Wysyłanie obrazu", "SSE.Controllers.Main.waitText": "<PERSON><PERSON><PERSON> cze<PERSON>...", "SSE.Controllers.Main.warnBrowserIE9": "Aplikacja ma małe możliwości w IE9. Użyj przeglądarki IE10 lub nowszej.", "SSE.Controllers.Main.warnBrowserZoom": "Aktualne ustawienie powiększenia przeglądarki nie jest w pełni obsługiwane. Zresetuj domyślny zoom, naciskając Ctrl + 0.", "SSE.Controllers.Main.warnLicenseAnonymous": "Access denied for anonymous users.<br>This document will be opened for viewing only.", "SSE.Controllers.Main.warnLicenseBefore": "License not active.<br>Please contact your administrator.", "SSE.Controllers.Main.warnLicenseExceeded": "Ta wersja edytorów ONLYOFFICE ma pewne ograniczenia dla użytkowników.Dokument zostanie otwarty tylko do odczytu.<b><PERSON><PERSON><PERSON> potrzebujesz więcej, roz<PERSON>ż zakupienie licencji komercyjnej.", "SSE.Controllers.Main.warnLicenseExp": "Twoja licencja wygasła.<br>Zaktualizuj licencję i odśwież stronę.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Licencja wygasła.<br><PERSON><PERSON> <PERSON>sz <PERSON>tę<PERSON> do edycji dokumentu.<br><PERSON><PERSON><PERSON> skontaktować się ze swoim administratorem.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Licencja musi zostać odnowiona.<br><PERSON><PERSON> dostęp do edycji dokumentu.<br>Skontaktuj się ze swoim administratorem, aby uzyskać pełny dostęp.", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Osiągnąłeś limit dla użytkownia. Skontaktuj się z <PERSON>em, aby dowiedzieć się więcej.", "SSE.Controllers.Main.warnNoLicense": "Osiągnięto limit jednoczesnych połączeń z %1 edytorami. Ten dokument zostanie otwarty tylko do odczytu.<br>Skontaktuj się z %1 zespołem sprzedaży w celu omówienia indywidualnych warunków licencji.", "SSE.Controllers.Main.warnNoLicenseUsers": "Osiągnąłeś limit dla użytkownika. Skontaktuj się z zespołem sprzedaży %1 w celu uzyskania osobistych warunków aktualizacji.", "SSE.Controllers.Main.warnProcessRightsChange": "<PERSON>e masz prawa edytować tego pliku.", "SSE.Controllers.PivotTable.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "No actions with calculated items are allowed for this active cell.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Calculated items do not work with custom subtotals.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "An item name cannot be found. Check that you've typed name correctly and the item is present in the PivotTable report.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Averages, standard deviations, and variances are not supported when a PivotTable report has calculated items.", "SSE.Controllers.Print.strAllSheets": "Wszystkie arkusze", "SSE.Controllers.Print.textFirstCol": "<PERSON><PERSON><PERSON> kolumna", "SSE.Controllers.Print.textFirstRow": "Pierwszy rząd", "SSE.Controllers.Print.textFrozenCols": "Zablokuj kolumny", "SSE.Controllers.Print.textFrozenRows": "Zablokowane rzędy", "SSE.Controllers.Print.textInvalidRange": "BŁĄD! Niepoprawny zakres komórek", "SSE.Controllers.Print.textNoRepeat": "<PERSON><PERSON>", "SSE.Controllers.Print.textRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "SSE.Controllers.Print.textSelectRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.txtCustom": "Niestandardowy", "SSE.Controllers.Print.txtZoomToPage": "Zoom to page", "SSE.Controllers.Search.textInvalidRange": "BŁĄD! Niepoprawny zakres komórek", "SSE.Controllers.Search.textNoTextFound": "<PERSON><PERSON> znale<PERSON>, k<PERSON><PERSON><PERSON><PERSON> s<PERSON>sz. <PERSON><PERSON><PERSON> dos<PERSON>uj opcje wyszukiwania.", "SSE.Controllers.Search.textReplaceSkipped": "Zastąpiono. {0} zdarzenia zostały pominięte.", "SSE.Controllers.Search.textReplaceSuccess": "Search has been done. {0} occurrences have been replaced", "SSE.Controllers.Statusbar.errorLastSheet": "Skoroszyt musi zawierać co najmniej jeden widoczny arkusz roboczy.", "SSE.Controllers.Statusbar.errorRemoveSheet": "<PERSON><PERSON> można us<PERSON> ark<PERSON>za.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>Connection is lost</b><br>Trying to connect. Please check connection settings.", "SSE.Controllers.Statusbar.textSheetViewTip": "Jesteś w trybie widoku arkusza. Filtry i sortowanie są widoczne tylko dla Ciebie i tych, którzy nadal są w tym widoku.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Jesteś w trybie widoku arkusza. Filtry są widoczne tylko dla Ciebie i tych, którzy nadal są w tym widoku.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Wybrane arkusze mogą zawierać dane. Czy na pewno chcesz kontynuować?", "SSE.Controllers.Statusbar.zoomText": "Powięks<PERSON><PERSON> {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON> z<PERSON>, nie jest dostępna na bieżącym urządzeniu.<br>Styl tekstu zostanie wyświetlony przy użyciu jednej z czcionek systemowych, a zapisana czcionka będzie używana, jeśli będzie dostępna.<br><PERSON><PERSON> ch<PERSON> kont<PERSON>uo<PERSON>?", "SSE.Controllers.Toolbar.errorComboSeries": "Wybierz co najmniej dwie serie danych, aby utworzyć wykres kombi.", "SSE.Controllers.Toolbar.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "BŁĄD! Maks<PERSON>alna liczba serii danych na wykresie wynosi 255", "SSE.Controllers.Toolbar.errorStockChart": "Nieprawidłowa kolejność wierszy. <PERSON><PERSON> zbu<PERSON><PERSON> wykres akcji, umie<PERSON><PERSON> dane na arkuszu w następującej kolejności:<br> cena <PERSON><PERSON>, cena ma<PERSON>, cena <PERSON>, cena zam<PERSON>.", "SSE.Controllers.Toolbar.helpCalcItems": "Work with calculated items in Pivot Tables.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Calculated items", "SSE.Controllers.Toolbar.helpFastUndo": "Easily undo changes while collaborating on sheets in Fast mode.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "\"Undo\" in real-time co-editing", "SSE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "SSE.Controllers.Toolbar.textAccent": "Akcenty", "SSE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textDirectional": "Wskazówki", "SSE.Controllers.Toolbar.textFontSizeErr": "W<PERSON>rowad<PERSON><PERSON> wartość jest nieprawidłowa.<br><PERSON><PERSON><PERSON><PERSON><PERSON> wartość liczbową w zakresie od 1 do 409.", "SSE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textIndicator": "Wskaźniki", "SSE.Controllers.Toolbar.textInsert": "Wstaw", "SSE.Controllers.Toolbar.textIntegral": "Całki", "SSE.Controllers.Toolbar.textLargeOperator": "Duże operatory", "SSE.Controllers.Toolbar.textLimitAndLog": "Limity i algorytmy", "SSE.Controllers.Toolbar.textLongOperation": "Długa praca", "SSE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textOperator": "Operatory", "SSE.Controllers.Toolbar.textPivot": "<PERSON><PERSON><PERSON> p<PERSON>esta<PERSON>a", "SSE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRating": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRecentlyUsed": "Ostatnio używane", "SSE.Controllers.Toolbar.textScript": "Pisma", "SSE.Controllers.Toolbar.textShapes": "Kształty", "SSE.Controllers.Toolbar.textSymbols": "Symbole", "SSE.Controllers.Toolbar.textWarning": "Ostrzeżenie", "SSE.Controllers.Toolbar.txtAccent_Accent": "Ostry", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Strzałka w lewo - w prawo powyżej", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Lewa strzałka powyżej", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Strzałka w prawo powyżej", "SSE.Controllers.Toolbar.txtAccent_Bar": "Pasek", "SSE.Controllers.Toolbar.txtAccent_BarBot": " Kreska na dole", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Kreska od góry", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Formuła w ramce (z wypełnieniem)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Forma zamknięta (przykład)", "SSE.Controllers.Toolbar.txtAccent_Check": "Sprawdź", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "<PERSON><PERSON><PERSON> k<PERSON> na dole", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Wektor A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC Z Nadmiarem", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y z cechą góry", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Wielokropek", "SSE.Controllers.Toolbar.txtAccent_DDot": "Podwójna kropka", "SSE.Controllers.Toolbar.txtAccent_Dot": "Kropka", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Podwójna kreska od góry", "SSE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Grupowanie znaków poniżej", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Grupowanie znaków powyżej", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Ha<PERSON>un w lewo do góry", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Prawy Harpun Powyżej", "SSE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Klamry z separatorami", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Klamry z separatorami", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Klamry z separatorami", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON> (dwa warunki)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (trzy warunki)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Obiekt stosu", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Obiekt stosu", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Przykłady przypadków", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Współczynnik dwumianowy", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Współczynnik dwumianowy", "SSE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Klamry z separatorami", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Pojedyńcza klamra", "SSE.Controllers.Toolbar.txtDeleteCells": "Usuń komórki", "SSE.Controllers.Toolbar.txtExpand": "Rozszerz i posortuj", "SSE.Controllers.Toolbar.txtExpandSort": "Dane obok zaznaczenia nie będą sortowane. <PERSON><PERSON> rozsz<PERSON> wybó<PERSON>, aby u<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ić sąsiednie dane lub kontynuować sortowanie tylko zaznaczonych komórek?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Skrzywiona frakcja", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Zróżnicowany", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Zróżnicowany", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Zróżnicowany", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Zróżnicowany", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Ułamek liniowy", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi ponad 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Mała frakcja", "SSE.Controllers.Toolbar.txtFractionVertical": "Sku<PERSON>lowany fragment", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Funkcja Inverse Cosine", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Funkcja Inverse Cotangent", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Hiper<PERSON><PERSON>na <PERSON> Inverse Cotangent", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Funkcja Odwrotna Cosecans", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Hiperboliczny arcus cosecans", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "<PERSON><PERSON><PERSON><PERSON><PERSON>na", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Hiperboliczna odwrotna funkcja se<PERSON>na", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Funkcja Inverse Sine", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Funkcja odwrotnej sinusoidy hiperbolicznej", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "<PERSON><PERSON><PERSON><PERSON><PERSON> arcus tangens", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "<PERSON><PERSON>ja hiperboliczna odwrotna", "SSE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON> cosinus", "SSE.Controllers.Toolbar.txtFunction_Cosh": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Cot": "Funkcja ctg", "SSE.Controllers.Toolbar.txtFunction_Coth": "Cotangens hiperboliczny", "SSE.Controllers.Toolbar.txtFunction_Csc": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Csch": "Funkcja hipotonii hiperbola", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sinus theta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Wzór styczny", "SSE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sech": "<PERSON>er<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sinh": "<PERSON><PERSON>ja hiperboliczna <PERSON>", "SSE.Controllers.Toolbar.txtFunction_Tan": "Tangens", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Tangens hiperboliczny", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Custom", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Data and model", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Good, bad and neutral", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "No name", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Number format", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Themed cell styles", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Titles and headings", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Custom", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Dark", "SSE.Controllers.Toolbar.txtGroupTable_Light": "Light", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Medium", "SSE.Controllers.Toolbar.txtInsertCells": "Wstaw komórki", "SSE.Controllers.Toolbar.txtIntegral": "Całka", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Mechanizm różnicowy", "SSE.Controllers.Toolbar.txtIntegral_dx": "Mechanizm różnicowy x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Mechanizm różnicowy y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Całka", "SSE.Controllers.Toolbar.txtIntegralDouble": "Całka podwójna", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Całka podwójna", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Całka podwójna", "SSE.Controllers.Toolbar.txtIntegralOriented": "Całka krzywej", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Całka krzywej", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integral powierzchni", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integral powierzchni", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integral powierzchni", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Całka krzywej", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integralna objętość", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integralna objętość", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integralna objętość", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Całka", "SSE.Controllers.Toolbar.txtIntegralTriple": "Potr<PERSON>jny integral", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Potr<PERSON>jny integral", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Potr<PERSON>jny integral", "SSE.Controllers.Toolbar.txtInvalidRange": "BŁĄD! Niepoprawny zakres komórki", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Zak<PERSON><PERSON>j", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Zak<PERSON><PERSON>j", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Zak<PERSON><PERSON>j", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Zak<PERSON><PERSON>j", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Zak<PERSON><PERSON>j", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Współprodukt", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Współprodukt", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Współprodukt", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Współprodukt", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Współprodukt", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Unia", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Przecię<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Przecię<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Przecię<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Przecię<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Przecię<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produkt", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Unia", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Unia", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Unia", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Unia", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Unia", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Przykładowy limit", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Maksymalny przykład", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Limit", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Logarytm naturalny", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logarytm", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarytm", "SSE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "SSE.Controllers.Toolbar.txtLockSort": "Obok Twojego wyboru znajdują się dane, ale nie masz wystarczaj<PERSON><PERSON>ch uprawnień, aby z<PERSON>ć te komórki.<br><PERSON><PERSON> ch<PERSON>z kontynuować bieżące zaznaczenie?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Pusta macierz", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Pusta mac<PERSON>z", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 Pusta mac<PERSON>z", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Pusta macierz", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Pusta macierz z nawiasami", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Pusta macierz z nawiasami", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Pusta macierz z nawiasami", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Pusta macierz z nawiasami", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 Pusta mac<PERSON>z", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Pusta macierz", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Pusta macierz", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 Pusta mac<PERSON>z", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Kropki bazowe", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "<PERSON><PERSON> pomo<PERSON>niczne", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Ukośnie kropki", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Rozrzedzona matryca", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Rozrzedzona matryca", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 Macierz jednostkowa", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 Macierz jednostkowa", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 Macierz jednostkowa", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 Macierz jednostkowa", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Strzałka w lewo - w prawo powyżej", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Strzałka w lewo - w prawo powyżej", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Lewa strzałka poniżej", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Lewa strzałka powyżej", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Strzałka w prawo Poniżej", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Strzałka w prawo powyżej", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Dwukropek jest równy", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_Definition": "<PERSON><PERSON><PERSON> def<PERSON>", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta równa się", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Strzałka w lewo - w prawo powyżej", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Strzałka w lewo - w prawo powyżej", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Lewa strzałka poniżej", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Lewa strzałka powyżej", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Strzałka w prawo Poniżej", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Strzałka w prawo powyżej", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Równy równy", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Równy", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Zmierzone przez", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Pierwiastek drugiego <PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Pierwiastek sześcienny", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Pierwiastek ze stopniem", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Pierwiastek kwadratowy", "SSE.Controllers.Toolbar.txtScriptCustom_1": "Pi<PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "Pi<PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "Pi<PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Pi<PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSubSup": "Dolne i górne indeksy", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Le<PERSON> indeks dolny", "SSE.Controllers.Toolbar.txtScriptSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSorting": "Sort<PERSON>nie", "SSE.Controllers.Toolbar.txtSortSelected": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_about": "W przybliżeniu", "SSE.Controllers.Toolbar.txtSymbol_additional": "Uzupełnienie", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "Prawie równe do", "SSE.Controllers.Toolbar.txtSymbol_ast": "Operator <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Symbol kuli", "SSE.Controllers.Toolbar.txtSymbol_cap": "Przecię<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Pierwiastek sześcienny", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Poziome kropki w środku", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON>lcjusza", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "W przybliżeniu równe", "SSE.Controllers.Toolbar.txtSymbol_cup": "Unia", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Przekątny wielokropek w dół w prawo", "SSE.Controllers.Toolbar.txtSymbol_degree": "<PERSON>nie", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Znak dzielenia", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Strzałka w dół", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON><PERSON> zestaw", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "Równy", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Identyczny do", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "<PERSON> jest", "SSE.Controllers.Toolbar.txtSymbol_factorial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Stopnie Fahrenheit", "SSE.Controllers.Toolbar.txtSymbol_forall": "<PERSON><PERSON> wszystkich", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "<PERSON><PERSON><PERSON><PERSON><PERSON> lub równy niż", "SSE.Controllers.Toolbar.txtSymbol_gg": "Dużo wię<PERSON>zy niż", "SSE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON><PERSON><PERSON>ż", "SSE.Controllers.Toolbar.txtSymbol_in": "Element", "SSE.Controllers.Toolbar.txtSymbol_inc": "Przyrost", "SSE.Controllers.Toolbar.txtSymbol_infinity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_iota": "odr<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Lewa strzałka", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Lewa-Prawa strzałka", "SSE.Controllers.Toolbar.txtSymbol_leq": "Mniejszy lub równy niż", "SSE.Controllers.Toolbar.txtSymbol_less": "Mniejszy niż", "SSE.Controllers.Toolbar.txtSymbol_ll": "Dużo mniejszy niż", "SSE.Controllers.Toolbar.txtSymbol_minus": "Minus", "SSE.Controllers.Toolbar.txtSymbol_mp": "Minus Plus", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Różny od", "SSE.Controllers.Toolbar.txtSymbol_ni": "Zawiera jako c<PERSON>k", "SSE.Controllers.Toolbar.txtSymbol_not": "Znak negacji", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Tam nie ma", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omikron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Częściowe różnice", "SSE.Controllers.Toolbar.txtSymbol_percent": "Procentowo", "SSE.Controllers.Toolbar.txtSymbol_phi": "Fi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "Znak plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus minus", "SSE.Controllers.Toolbar.txtSymbol_propto": "Proporcjonalny do", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Pierwiastek poczwórny", "SSE.Controllers.Toolbar.txtSymbol_qed": "Koniec dowodu", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Przekątny wielokropek w górę w prawo", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Strzałka w prawo", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Znak <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "W związku z tym", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "Znak mnożenia", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Strzałka w górę", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Ypsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon (opcja)", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Phi <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Pi <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Wariant Rho", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Wariacja Sigma", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Wariant Theta", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Pionowe wyskakiwanie", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Styl tabeli: ciemny", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Styl tabeli: jasny", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Styl tabeli: <PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.warnLongOperation": "<PERSON><PERSON><PERSON>, kt<PERSON>rą zamierzasz wykonać może zająć trochę czasu.<br>Na pewno chcesz kontynuować?", "SSE.Controllers.Toolbar.warnMergeLostData": "Tylko dane z górnej lewej komórki pozostaną w scalonej komórce. <br><PERSON><PERSON> na pewno chcesz kontynuować?", "SSE.Controllers.Toolbar.warnNoRecommended": "To create a chart, select the cells that contain the data you'd like to use.<br>If you have names for the rows and columns and you'd like use them as labels, include them in your selection.", "SSE.Controllers.Viewport.textFreezePanes": "Zablokuj panele", "SSE.Controllers.Viewport.textFreezePanesShadow": "Pokaż cień dla zadokowanych obszarów", "SSE.Controllers.Viewport.textHideFBar": "<PERSON><PERSON><PERSON><PERSON> pasek formuły", "SSE.Controllers.Viewport.textHideGridlines": "<PERSON><PERSON><PERSON><PERSON> linie si<PERSON>ki", "SSE.Controllers.Viewport.textHideHeadings": "Ukryj nagłówki", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Separator l<PERSON><PERSON> dzie<PERSON>ch", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Separator <PERSON>", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Ustawienia używane do rozpoznawania danych liczbowych", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Text qualifier", "SSE.Views.AdvancedSeparatorDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(brak)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Niestandardowy filtr", "SSE.Views.AutoFilterDialog.textAddSelection": "Dodaj bieżący wybór do filtrowania", "SSE.Views.AutoFilterDialog.textEmptyItem": "{<PERSON><PERSON><PERSON>}", "SSE.Views.AutoFilterDialog.textSelectAll": "Zaznacz wszystko", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Wybierz wszystkie wyniki wyszukiwania", "SSE.Views.AutoFilterDialog.textWarning": "Ostrzeżenie", "SSE.Views.AutoFilterDialog.txtAboveAve": "Powyżej <PERSON>niej", "SSE.Views.AutoFilterDialog.txtAfter": "After...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "All dates in the period", "SSE.Views.AutoFilterDialog.txtApril": "April", "SSE.Views.AutoFilterDialog.txtAugust": "August", "SSE.Views.AutoFilterDialog.txtBefore": "Before...", "SSE.Views.AutoFilterDialog.txtBegins": "<PERSON><PERSON><PERSON>na się z...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Poniżej średniej", "SSE.Views.AutoFilterDialog.txtBetween": "Pomiędzy...", "SSE.Views.AutoFilterDialog.txtClear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtContains": "Zawiera...", "SSE.Views.AutoFilterDialog.txtDateFilter": "Date filter", "SSE.Views.AutoFilterDialog.txtDecember": "December", "SSE.Views.AutoFilterDialog.txtEmpty": "Wprowadź filtr komórki", "SSE.Views.AutoFilterDialog.txtEnds": "Kończy się z...", "SSE.Views.AutoFilterDialog.txtEquals": "R<PERSON>na się..", "SSE.Views.AutoFilterDialog.txtFebruary": "February", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filtruj po kolorze komórek", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filtruj po kolorze czcionki", "SSE.Views.AutoFilterDialog.txtGreater": "<PERSON><PERSON><PERSON><PERSON><PERSON> niż...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "<PERSON><PERSON><PERSON><PERSON><PERSON> lub równy niż...", "SSE.Views.AutoFilterDialog.txtJanuary": "January", "SSE.Views.AutoFilterDialog.txtJuly": "July", "SSE.Views.AutoFilterDialog.txtJune": "June", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Filtr etykiet", "SSE.Views.AutoFilterDialog.txtLastMonth": "Last month", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Last quarter", "SSE.Views.AutoFilterDialog.txtLastWeek": "Last week", "SSE.Views.AutoFilterDialog.txtLastYear": "Last year", "SSE.Views.AutoFilterDialog.txtLess": "Mniejszy niż...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Mniejszy lub równy niż...", "SSE.Views.AutoFilterDialog.txtMarch": "March", "SSE.Views.AutoFilterDialog.txtMay": "May", "SSE.Views.AutoFilterDialog.txtNextMonth": "Next month", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Next quarter", "SSE.Views.AutoFilterDialog.txtNextWeek": "Next week", "SSE.Views.AutoFilterDialog.txtNextYear": "Next year", "SSE.Views.AutoFilterDialog.txtNotBegins": "<PERSON>e zaczyna się od...", "SSE.Views.AutoFilterDialog.txtNotBetween": "<PERSON><PERSON> p<PERSON> ...", "SSE.Views.AutoFilterDialog.txtNotContains": "<PERSON><PERSON>...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Nie kończy się z...", "SSE.Views.AutoFilterDialog.txtNotEquals": "<PERSON>e równa się...", "SSE.Views.AutoFilterDialog.txtNovember": "November", "SSE.Views.AutoFilterDialog.txtNumFilter": "Filtr numeryczny", "SSE.Views.AutoFilterDialog.txtOctober": "October", "SSE.Views.AutoFilterDialog.txtQuarter1": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Quarter 1", "SSE.Views.AutoFilterDialog.txtReapply": "Ponownie zastosuj", "SSE.Views.AutoFilterDialog.txtSeptember": "September", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Sortuj po kolorze komórek", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Sortuj po kolorze czcionki", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Sortuj od najwyższych do najniższych", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Sortuj od najniższych do najwyższych", "SSE.Views.AutoFilterDialog.txtSortOption": "Dodatkowe opcje sortowania...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Filtrowanie tekstu", "SSE.Views.AutoFilterDialog.txtThisMonth": "This Month", "SSE.Views.AutoFilterDialog.txtThisQuarter": "This quarter", "SSE.Views.AutoFilterDialog.txtThisWeek": "This week", "SSE.Views.AutoFilterDialog.txtThisYear": "This Year", "SSE.Views.AutoFilterDialog.txtTitle": "Filtr", "SSE.Views.AutoFilterDialog.txtToday": "Today", "SSE.Views.AutoFilterDialog.txtTomorrow": "Tomorrow", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtYearToDate": "Year to date", "SSE.Views.AutoFilterDialog.txtYesterday": "Yesterday", "SSE.Views.AutoFilterDialog.warnFilterError": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> war<PERSON>, z<PERSON><PERSON> wartości musi zawierać co najmniej jedno pole.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Mu<PERSON><PERSON> wybrać co najmniej jedną wartość", "SSE.Views.CellEditor.textManager": "Menadżer zdefiniowanych zakresów", "SSE.Views.CellEditor.tipFormula": "Wstaw funkcję", "SSE.Views.CellRangeDialog.errorMaxRows": "BŁĄD! Maks<PERSON>alna liczba serii danych na wykresie wynosi 255", "SSE.Views.CellRangeDialog.errorStockChart": "Nieprawidłowa kolejność wierszy. <PERSON><PERSON> zbu<PERSON><PERSON> wykres akcji, umie<PERSON><PERSON> dane na arkuszu w następującej kolejności:<br> cena <PERSON><PERSON>, cena ma<PERSON>, cena <PERSON>, cena zam<PERSON>.", "SSE.Views.CellRangeDialog.txtEmpty": "To pole jest wymagane", "SSE.Views.CellRangeDialog.txtInvalidRange": "BŁĄD! Niepoprawny zakres komórek", "SSE.Views.CellRangeDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON> zak<PERSON> danych", "SSE.Views.CellSettings.strShrink": "Autodopasowanie szerokości", "SSE.Views.CellSettings.strWrap": "Zawijaj tekst", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBackground": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBorderColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "Style obramowań", "SSE.Views.CellSettings.textClearRule": "<PERSON><PERSON><PERSON> reguły", "SSE.Views.CellSettings.textColor": "Wypełnij kolorem", "SSE.Views.CellSettings.textColorScales": "Skala kolorów", "SSE.Views.CellSettings.textCondFormat": "Formatowanie warunkowe", "SSE.Views.CellSettings.textControl": "Wyświetlenie", "SSE.Views.CellSettings.textDataBars": "Histogramy", "SSE.Views.CellSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textFill": "Wypełnij", "SSE.Views.CellSettings.textForeground": "<PERSON><PERSON>", "SSE.Views.CellSettings.textGradient": "<PERSON><PERSON>", "SSE.Views.CellSettings.textGradientColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "Wypełnienie gradientem", "SSE.Views.CellSettings.textIndent": "Odstęp", "SSE.Views.CellSettings.textItems": "Elementów", "SSE.Views.CellSettings.textLinear": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textManageRule": "Zarządza<PERSON>guła<PERSON>", "SSE.Views.CellSettings.textNewRule": "Nowa reguła", "SSE.Views.CellSettings.textNoFill": "Brak wypełnienia", "SSE.Views.CellSettings.textOrientation": "Orientacja <PERSON>", "SSE.Views.CellSettings.textPattern": "Wzór", "SSE.Views.CellSettings.textPatternFill": "Wzór", "SSE.Views.CellSettings.textPosition": "<PERSON><PERSON><PERSON>ja", "SSE.Views.CellSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textSelectBorders": "<PERSON><PERSON><PERSON><PERSON>, które chcesz zmienić stosując styl wybrany powyżej", "SSE.Views.CellSettings.textSelection": "Z obecnie zaznaczonego fragmentu", "SSE.Views.CellSettings.textThisPivot": "Z tej tabeli przestawnej", "SSE.Views.CellSettings.textThisSheet": "Z tego arkusza roboczego", "SSE.Views.CellSettings.textThisTable": "Z tej tabeli", "SSE.Views.CellSettings.tipAddGradientPoint": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.tipAll": "Ustaw krawędź zewnętrzną i wszystkie wewnętrzne linie", "SSE.Views.CellSettings.tipBottom": "Ustaw tylko obramowanie dolnej krawędzi", "SSE.Views.CellSettings.tipDiagD": "Ustaw ukośną granicę od góry do dołu", "SSE.Views.CellSettings.tipDiagU": "Ustaw ukośną granicę od dołu do góry", "SSE.Views.CellSettings.tipInner": "Us<PERSON>wić tylko linie wewnętrzne", "SSE.Views.CellSettings.tipInnerHor": "<PERSON><PERSON><PERSON><PERSON> tylko poziome linie wewnętrzne", "SSE.Views.CellSettings.tipInnerVert": "Ustaw tylko wewnętrzne pionowe linie", "SSE.Views.CellSettings.tipLeft": "Ustaw tylko obramowanie lewej krawędzi", "SSE.Views.CellSettings.tipNone": "Wyłącz krawędzie", "SSE.Views.CellSettings.tipOuter": "Ustaw tylko obramowanie zewnętrzne", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Usuń punkt gradientu", "SSE.Views.CellSettings.tipRight": "Ustaw tylko obramowanie prawej krawędzi", "SSE.Views.CellSettings.tipTop": "Ustaw tylko obramowanie górnej krawędzi", "SSE.Views.ChartDataDialog.errorInFormula": "We wprowadzonej formule wystąpił błąd.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Odniesienie jest nieprawidłowe. Odniesienie musi odnosić się do otwartego arkusza roboczego.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Maksymalna liczba punktów w serii na wykres to 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Ma<PERSON><PERSON>alna liczba serii danych na wykres to 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Odniesienie jest nieprawidłowe. Odniesienia do tytułów, <PERSON>tości, rozmiarów lub etykiet danych muszą obejmować pojedynczą komórkę, wiersz lub kolumnę.", "SSE.Views.ChartDataDialog.errorNoValues": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>, seria musi zawierać co najmniej jedną wartość.", "SSE.Views.ChartDataDialog.errorStockChart": "Nieprawidłowa kolejność wierszy. <PERSON><PERSON> zbu<PERSON><PERSON> wykres akcji, umie<PERSON><PERSON> dane na arkuszu w następującej kolejności:<br> cena <PERSON><PERSON>, cena ma<PERSON>, cena <PERSON>, cena zam<PERSON>.", "SSE.Views.ChartDataDialog.textAdd": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textCategory": "Etykiety osi poziomej (kategorii)", "SSE.Views.ChartDataDialog.textData": "Zak<PERSON> danych wykresu", "SSE.Views.ChartDataDialog.textDelete": "Usuń", "SSE.Views.ChartDataDialog.textDown": "<PERSON> dół", "SSE.Views.ChartDataDialog.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textInvalidRange": "Błędny zakres komórek.", "SSE.Views.ChartDataDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.ChartDataDialog.textSeries": "Wpisy legendy (serie danych)", "SSE.Views.ChartDataDialog.textSwitch": "Przełącz wiersz/kolumnę", "SSE.Views.ChartDataDialog.textTitle": "<PERSON>", "SSE.Views.ChartDataDialog.textUp": "W górę", "SSE.Views.ChartDataRangeDialog.errorInFormula": "We wprowadzonej formule wystąpił błąd.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Odniesienie jest nieprawidłowe. Odniesienie musi odnosić się do otwartego arkusza roboczego.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Maksymalna liczba punktów w serii na wykres to 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Ma<PERSON><PERSON>alna liczba serii danych na wykres to 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Odniesienie jest nieprawidłowe. Odniesienia do tytułów, <PERSON>tości, rozmiarów lub etykiet danych muszą obejmować pojedynczą komórkę, wiersz lub kolumnę.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>, seria musi zawierać co najmniej jedną wartość.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Nieprawidłowa kolejność wierszy. <PERSON><PERSON> zbu<PERSON><PERSON> wykres akcji, umie<PERSON><PERSON> dane na arkuszu w następującej kolejności:<br> cena <PERSON><PERSON>, cena ma<PERSON>, cena <PERSON>, cena zam<PERSON>.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Błędny zakres komórek.", "SSE.Views.ChartDataRangeDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Zakres etykiet osi", "SSE.Views.ChartDataRangeDialog.txtChoose": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "<PERSON><PERSON><PERSON> serii", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Etykiety osi", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Edytowanie serii", "SSE.Views.ChartDataRangeDialog.txtValues": "Wartości serii", "SSE.Views.ChartDataRangeDialog.txtXValues": "Wartość X", "SSE.Views.ChartDataRangeDialog.txtYValues": "Wartość Y", "SSE.Views.ChartSettings.errorMaxRows": "Ma<PERSON><PERSON>alna liczba serii danych na wykres to 255.", "SSE.Views.ChartSettings.strLineWeight": "Waga linii", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "Szablon", "SSE.Views.ChartSettings.text3dDepth": "Depth (% of base)", "SSE.Views.ChartSettings.text3dHeight": "Height (% of base)", "SSE.Views.ChartSettings.text3dRotation": "3D Rotation", "SSE.Views.ChartSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "SSE.Views.ChartSettings.textAutoscale": "Autoscale", "SSE.Views.ChartSettings.textBorderSizeErr": "Wprowadzona wartość jest nieprawidłowa.<br><PERSON><PERSON><PERSON><PERSON><PERSON> wartość w zakresie od 0 do 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "Zmień typ", "SSE.Views.ChartSettings.textChartType": "Zmień typ wykresu", "SSE.Views.ChartSettings.textDefault": "Default Rotation", "SSE.Views.ChartSettings.textDown": "Down", "SSE.Views.ChartSettings.textEditData": "Edytuj datę i lokalizację", "SSE.Views.ChartSettings.textFirstPoint": "Pierwszy punkt", "SSE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textHighPoint": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textKeepRatio": "Stałe proporcje", "SSE.Views.ChartSettings.textLastPoint": "Punkt końcowy", "SSE.Views.ChartSettings.textLeft": "Left", "SSE.Views.ChartSettings.textLowPoint": "Najniższy punkt", "SSE.Views.ChartSettings.textMarkers": "Znaczniki", "SSE.Views.ChartSettings.textNarrow": "Narrow field of view", "SSE.Views.ChartSettings.textNegativePoint": "<PERSON><PERSON> negat<PERSON>y", "SSE.Views.ChartSettings.textPerspective": "Perspective", "SSE.Views.ChartSettings.textRanges": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textRight": "Right", "SSE.Views.ChartSettings.textRightAngle": "Right angle axes", "SSE.Views.ChartSettings.textSelectData": "<PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.ChartSettings.textShow": "Po<PERSON><PERSON>", "SSE.Views.ChartSettings.textSize": "Rozmiar", "SSE.Views.ChartSettings.textStyle": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textSwitch": "Przełącz wiersz/kolumnę", "SSE.Views.ChartSettings.textType": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textUp": "Up", "SSE.Views.ChartSettings.textWiden": "Widen field of view", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textX": "X rotation", "SSE.Views.ChartSettings.textY": "Y rotation", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "BŁĄD! Maksymalna liczba punktów w serii na wykres to 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "BŁĄD! Maks<PERSON>alna liczba serii danych na wykresie wynosi 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Nieprawidłowa kolejność wierszy. <PERSON><PERSON> zbu<PERSON><PERSON> wykres akcji, umie<PERSON><PERSON> dane na arkuszu w następującej kolejności:<br> cena <PERSON><PERSON>, cena ma<PERSON>, cena <PERSON>, cena zam<PERSON>.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Nie przesuwaj lub nie zmieniaj rozmiaru z komórkami", "SSE.Views.ChartSettingsDlg.textAlt": "Tekst alternatywny", "SSE.Views.ChartSettingsDlg.textAltDescription": "Opis", "SSE.Views.ChartSettingsDlg.textAltTip": "Alternatywna, tekstowa prezentacja wizualnych informacji o obiektach, która będzie czytana osobom z wadami wzroku lub zmysłu poznawczego, aby pomóc im lepiej zrozumieć, jakie informacje znajdują się na obrazie, ksz<PERSON>łcie, wykresie lub tabeli.", "SSE.Views.ChartSettingsDlg.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAuto": "Automatyczny", "SSE.Views.ChartSettingsDlg.textAutoEach": "Automatycznie dla każdego", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Krz<PERSON><PERSON> osie", "SSE.Views.ChartSettingsDlg.textAxisOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisPos": "Pozycja osi", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Ustawienia osi", "SSE.Views.ChartSettingsDlg.textAxisTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBase": "Base", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Pomiędzy znakami Tick", "SSE.Views.ChartSettingsDlg.textBillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCategoryName": "<PERSON><PERSON><PERSON> kate<PERSON>i", "SSE.Views.ChartSettingsDlg.textCenter": "Środek", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Elementy wykresu", "SSE.Views.ChartSettingsDlg.textChartTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCross": "Na skrzyżowaniu", "SSE.Views.ChartSettingsDlg.textCustom": "Niestandardowy", "SSE.Views.ChartSettingsDlg.textDataColumns": "w kolumnach", "SSE.Views.ChartSettingsDlg.textDataLabels": "Ciemne etykiety", "SSE.Views.ChartSettingsDlg.textDataRows": "w wiers<PERSON>h", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Pokaż legendę", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Ukryte i puste komórki", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Podłącz punkty danych do wierszu", "SSE.Views.ChartSettingsDlg.textFit": "Dopasuj do szerokości", "SSE.Views.ChartSettingsDlg.textFixed": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textFormat": "Format etykiety", "SSE.Views.ChartSettingsDlg.textGaps": "Przerwy", "SSE.Views.ChartSettingsDlg.textGridLines": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGroup": "Grupa Sparkline", "SSE.Views.ChartSettingsDlg.textHide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHideAxis": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHigh": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxis": "Pozioma oś", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Druga oś pozioma", "SSE.Views.ChartSettingsDlg.textHorizontal": "Poziomy", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "W", "SSE.Views.ChartSettingsDlg.textInnerBottom": "<PERSON><PERSON>a strona", "SSE.Views.ChartSettingsDlg.textInnerTop": "<PERSON><PERSON><PERSON> s<PERSON>a", "SSE.Views.ChartSettingsDlg.textInvalidRange": "BŁĄD! Niepoprawny zakres komórek", "SSE.Views.ChartSettingsDlg.textLabelDist": "Oznako<PERSON><PERSON> osi", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Odstęp między etykietami", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Ustawienia etykiety", "SSE.Views.ChartSettingsDlg.textLabelPos": "<PERSON><PERSON><PERSON>ja etykiety", "SSE.Views.ChartSettingsDlg.textLayout": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeft": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendLeft": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendPos": "<PERSON>a", "SSE.Views.ChartSettingsDlg.textLegendRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendTop": "Góra", "SSE.Views.ChartSettingsDlg.textLines": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLocationRange": "Zakres <PERSON>", "SSE.Views.ChartSettingsDlg.textLogScale": "Skala logarytmiczna", "SSE.Views.ChartSettingsDlg.textLow": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajor": "Główny", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Większy i mniejszy", "SSE.Views.ChartSettingsDlg.textMajorType": "Główny typ", "SSE.Views.ChartSettingsDlg.textManual": "Ręczny", "SSE.Views.ChartSettingsDlg.textMarkers": "Znaczniki", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Odstęp między znakami", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "Mniejszy", "SSE.Views.ChartSettingsDlg.textMinorType": "Mały typ", "SSE.Views.ChartSettingsDlg.textMinValue": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Obok osi", "SSE.Views.ChartSettingsDlg.textNone": "Żaden", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Brak pok<PERSON>", "SSE.Views.ChartSettingsDlg.textOneCell": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ale nie zmieniaj rozmiaru komórek", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textOut": "Wyjście", "SSE.Views.ChartSettingsDlg.textOuterTop": "Wierzchołek zewnętrzny", "SSE.Views.ChartSettingsDlg.textOverlay": "Nałożenie", "SSE.Views.ChartSettingsDlg.textReverse": "Wartości w odwrotnej kolejności", "SSE.Views.ChartSettingsDlg.textReverseOrder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Właściwa nakładka", "SSE.Views.ChartSettingsDlg.textRotated": "Obrócony", "SSE.Views.ChartSettingsDlg.textSameAll": "To samo dla wszystkich", "SSE.Views.ChartSettingsDlg.textSelectData": "<PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.ChartSettingsDlg.textSeparator": "Separator etykiet danych", "SSE.Views.ChartSettingsDlg.textSeriesName": "<PERSON><PERSON><PERSON> serii", "SSE.Views.ChartSettingsDlg.textShow": "Po<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowBorders": "Poka<PERSON> granice wykresu", "SSE.Views.ChartSettingsDlg.textShowData": "Pokaż dane w ukrytych wierszach i kolumnach", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Pokaż puste komórki jako", "SSE.Views.ChartSettingsDlg.textShowEquation": "Display equation on chart", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Po<PERSON><PERSON> oś", "SSE.Views.ChartSettingsDlg.textShowValues": "Pokaż wartości wykresu", "SSE.Views.ChartSettingsDlg.textSingle": "Pojedynczy Sparkline", "SSE.Views.ChartSettingsDlg.textSmooth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSnap": "Przesuwanie komórek", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Sparkline Ranges", "SSE.Views.ChartSettingsDlg.textStraight": "Prosty", "SSE.Views.ChartSettingsDlg.textStyle": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Tysiące", "SSE.Views.ChartSettingsDlg.textTickOptions": "Zaznacz opcje", "SSE.Views.ChartSettingsDlg.textTitle": "Wykres - <PERSON><PERSON><PERSON><PERSON>wane us<PERSON>wi<PERSON>", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Sparkline - Ustawienia zaawansowane", "SSE.Views.ChartSettingsDlg.textTop": "Góra", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Trendline options", "SSE.Views.ChartSettingsDlg.textTrillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTwoCell": "Przenieś i zmień rozmiar komórek", "SSE.Views.ChartSettingsDlg.textType": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTypeData": "Typ i data", "SSE.Views.ChartSettingsDlg.textUnits": "Pokaż jednostki", "SSE.Views.ChartSettingsDlg.textValue": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxis": "<PERSON>ś pionowa", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Druga oś pionowa", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Tytuł osi X", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Ty<PERSON>ł osi Y", "SSE.Views.ChartSettingsDlg.textZero": "Zero", "SSE.Views.ChartSettingsDlg.txtEmpty": "To pole jest wymagane", "SSE.Views.ChartTypeDialog.errorComboSeries": "Wybierz co najmniej dwie serie danych, aby utworzyć wykres kombi.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Wybrany typ wykresu wymaga osi pomocniczej, której używa istniejący wykres. Wybierz inny typ wykresu.", "SSE.Views.ChartTypeDialog.textSecondary": "<PERSON>ś pomo<PERSON>", "SSE.Views.ChartTypeDialog.textSeries": "<PERSON><PERSON><PERSON> serii", "SSE.Views.ChartTypeDialog.textStyle": "<PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textTitle": "<PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textType": "<PERSON><PERSON>", "SSE.Views.ChartWizardDialog.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartWizardDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order: opening price, max price, min price, closing price.", "SSE.Views.ChartWizardDialog.textRecommended": "Recommended", "SSE.Views.ChartWizardDialog.textSecondary": "Secondary Axis", "SSE.Views.ChartWizardDialog.textSeries": "Series", "SSE.Views.ChartWizardDialog.textTitle": "Insert Chart", "SSE.Views.ChartWizardDialog.textTitleChange": "Change chart type", "SSE.Views.ChartWizardDialog.textType": "Type", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Choose the chart type and axis for your data series", "SSE.Views.CreatePivotDialog.textDataRange": "Zak<PERSON>ódła da<PERSON>", "SSE.Views.CreatePivotDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON>, gdzie um<PERSON>ś<PERSON>ć tabele", "SSE.Views.CreatePivotDialog.textExist": "Istniejący arkusz roboczy", "SSE.Views.CreatePivotDialog.textInvalidRange": "Błędny zakres komórek.", "SSE.Views.CreatePivotDialog.textNew": "<PERSON><PERSON> arkusz", "SSE.Views.CreatePivotDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.CreatePivotDialog.textTitle": "Utwórz tabelę przestawną", "SSE.Views.CreatePivotDialog.txtEmpty": "To pole jest wymagane", "SSE.Views.CreateSparklineDialog.textDataRange": "Zak<PERSON>ódła da<PERSON>", "SSE.Views.CreateSparklineDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON>, gdzie umieścić krzywe", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Błędny zakres komórek.", "SSE.Views.CreateSparklineDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.CreateSparklineDialog.textTitle": "Utwórz Sparkline", "SSE.Views.CreateSparklineDialog.txtEmpty": "To pole jest wymagane", "SSE.Views.DataTab.capBtnGroup": "Grupuj", "SSE.Views.DataTab.capBtnTextCustomSort": "Niestandardowe sortowanie", "SSE.Views.DataTab.capBtnTextDataValidation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> danych", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Us<PERSON>ń duplikaty", "SSE.Views.DataTab.capBtnTextToCol": "Tekst na kolumny", "SSE.Views.DataTab.capBtnUngroup": "Rozgrupuj", "SSE.Views.DataTab.capDataExternalLinks": "External Links", "SSE.Views.DataTab.capDataFromText": "<PERSON><PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.DataTab.capGoalSeek": "Goal Seek", "SSE.Views.DataTab.mniFromFile": "Z lokalnego TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "Według adresu URL pliku TXT/CSV", "SSE.Views.DataTab.mniFromXMLFile": "From Local XML", "SSE.Views.DataTab.textBelow": "Podsumowanie w wierszach pod danymi", "SSE.Views.DataTab.textClear": "<PERSON><PERSON><PERSON> strukturę", "SSE.Views.DataTab.textColumns": "Rozgrupuj kolumny", "SSE.Views.DataTab.textGroupColumns": "Grupuj kolumny", "SSE.Views.DataTab.textGroupRows": "G<PERSON><PERSON><PERSON>", "SSE.Views.DataTab.textRightOf": "Podsumowanie w kolumnach po prawej stronie danych", "SSE.Views.DataTab.textRows": "Rozgrupuj <PERSON>", "SSE.Views.DataTab.tipCustomSort": "Niestandardowe sortowanie", "SSE.Views.DataTab.tipDataFromText": "Pobierz dane z pliku tekstowego/CSV", "SSE.Views.DataTab.tipDataValidation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> danych", "SSE.Views.DataTab.tipExternalLinks": "View other files this spreadsheet is linked to", "SSE.Views.DataTab.tipGoalSeek": "Find the right input for the value you want", "SSE.Views.DataTab.tipGroup": "Grupuj zakres komórek", "SSE.Views.DataTab.tipRemDuplicates": "Usuń zduplikowane wiersze z arkusza", "SSE.Views.DataTab.tipToColumns": "Podziel tekst komórki na kolumny", "SSE.Views.DataTab.tipUngroup": "Rozgrupuj zakres komórek", "SSE.Views.DataValidationDialog.errorFormula": "<PERSON><PERSON><PERSON><PERSON><PERSON>ł błąd podczas obliczania wartości. <PERSON><PERSON> k<PERSON>?", "SSE.Views.DataValidationDialog.errorInvalid": "W polu \"{0}\" wprow<PERSON><PERSON><PERSON> nieprawidłową warto<PERSON>.", "SSE.Views.DataValidationDialog.errorInvalidDate": "W polu \"{0}\" wprow<PERSON>zono nieprawidłową datę.", "SSE.Views.DataValidationDialog.errorInvalidList": "Źródłem listy musi być lista rozdzielana lub odwołanie do pojedynczego wiersza lub kolumny.", "SSE.Views.DataValidationDialog.errorInvalidTime": "W polu \"{0}\" wprow<PERSON><PERSON><PERSON> nieprawidłowy czas.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "<PERSON><PERSON><PERSON><PERSON> pola \"{1}\" musi być większa lub równa wartości pola \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "<PERSON><PERSON><PERSON> w<PERSON><PERSON><PERSON><PERSON><PERSON> wartoś<PERSON> zarówno w polu \"{0}\", jak i polu \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "<PERSON><PERSON><PERSON> w<PERSON><PERSON><PERSON><PERSON><PERSON> warto<PERSON> w polu \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "<PERSON>e można znaleźć nazwanego zakresu, kt<PERSON><PERSON> określił<PERSON>ś.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Wartości ujemne nie mogą być używane w warunkach \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "Pole \"{0}\" musi zaw<PERSON> wartość liczbową, wyrażenie liczbowe lub odwołanie do komórki z wartością liczbową.", "SSE.Views.DataValidationDialog.strError": "Komunikat błędu", "SSE.Views.DataValidationDialog.strInput": "Wprowadź wiadomość", "SSE.Views.DataValidationDialog.strSettings": "Ustawienia", "SSE.Views.DataValidationDialog.textAlert": "Ostrzeżenie", "SSE.Views.DataValidationDialog.textAllow": "Zezwól", "SSE.Views.DataValidationDialog.textApply": "Zastosuj te zmiany do wszystkich innych komórek z tymi samymi ustawieniami", "SSE.Views.DataValidationDialog.textCellSelected": "Gdy komórka jest zaznaczona, pokaż ten komunikat wejściowy", "SSE.Views.DataValidationDialog.textCompare": "Porównać z", "SSE.Views.DataValidationDialog.textData": "<PERSON>", "SSE.Views.DataValidationDialog.textEndDate": "Data zakończenia", "SSE.Views.DataValidationDialog.textEndTime": "Czas zakończenia", "SSE.Views.DataValidationDialog.textError": "Komunikat o błędzie", "SSE.Views.DataValidationDialog.textFormula": "Formuła", "SSE.Views.DataValidationDialog.textIgnore": "Ignoruj ​​puste komórki", "SSE.Views.DataValidationDialog.textInput": "Wprowadź wiadomość", "SSE.Views.DataValidationDialog.textMax": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMin": "Minimalny", "SSE.Views.DataValidationDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.DataValidationDialog.textShowDropDown": "Pokaż listę rozwijaną w komórce", "SSE.Views.DataValidationDialog.textShowError": "Pokaż komunikat błędu po wprowadzeniu nieprawidłowych danych", "SSE.Views.DataValidationDialog.textShowInput": "Pokaż komunikat we<PERSON>, gdy komórka jest zaznaczona", "SSE.Views.DataValidationDialog.textSource": "Źródło", "SSE.Views.DataValidationDialog.textStartDate": "Data rozpoczęcia", "SSE.Views.DataValidationDialog.textStartTime": "<PERSON><PERSON> roz<PERSON>", "SSE.Views.DataValidationDialog.textStop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStyle": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textUserEnters": "Pokaż ten komunikat błędu, gdy użytkownik wprowadzi nieprawidłowe dane", "SSE.Views.DataValidationDialog.txtAny": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtBetween": "pomiędzy", "SSE.Views.DataValidationDialog.txtDate": "Data", "SSE.Views.DataValidationDialog.txtDecimal": "Dziesiętny", "SSE.Views.DataValidationDialog.txtElTime": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtEndDate": "Data zakończenia", "SSE.Views.DataValidationDialog.txtEndTime": "Czas zakończenia", "SSE.Views.DataValidationDialog.txtEqual": "równa się", "SSE.Views.DataValidationDialog.txtGreaterThan": "<PERSON><PERSON><PERSON><PERSON><PERSON>ż", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "<PERSON><PERSON><PERSON><PERSON><PERSON> lub równy niż", "SSE.Views.DataValidationDialog.txtLength": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtLessThan": "Mniejszy niż", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "Mniejszy lub równy niż", "SSE.Views.DataValidationDialog.txtList": "Lista", "SSE.Views.DataValidationDialog.txtNotBetween": "nie pomi<PERSON>", "SSE.Views.DataValidationDialog.txtNotEqual": "nie równa się", "SSE.Views.DataValidationDialog.txtOther": "Inny", "SSE.Views.DataValidationDialog.txtStartDate": "Data rozpoczęcia", "SSE.Views.DataValidationDialog.txtStartTime": "<PERSON><PERSON> roz<PERSON>", "SSE.Views.DataValidationDialog.txtTextLength": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tekstu", "SSE.Views.DataValidationDialog.txtTime": "Czas", "SSE.Views.DataValidationDialog.txtWhole": "Cały numer", "SSE.Views.DigitalFilterDialog.capAnd": "I", "SSE.Views.DigitalFilterDialog.capCondition1": "równa się", "SSE.Views.DigitalFilterDialog.capCondition10": "nie kończy się z", "SSE.Views.DigitalFilterDialog.capCondition11": "zawiera", "SSE.Views.DigitalFilterDialog.capCondition12": "nie zaw<PERSON>", "SSE.Views.DigitalFilterDialog.capCondition2": "nie równa się", "SSE.Views.DigitalFilterDialog.capCondition3": "jest wi<PERSON><PERSON><PERSON> niż", "SSE.Views.DigitalFilterDialog.capCondition30": "is after", "SSE.Views.DigitalFilterDialog.capCondition4": "jest wi<PERSON><PERSON><PERSON> lub równy niż", "SSE.Views.DigitalFilterDialog.capCondition40": "is after or equal to", "SSE.Views.DigitalFilterDialog.capCondition5": "jest mnie<PERSON><PERSON><PERSON> niż", "SSE.Views.DigitalFilterDialog.capCondition50": "is before", "SSE.Views.DigitalFilterDialog.capCondition6": "jest mniej<PERSON>y lub równy niż", "SSE.Views.DigitalFilterDialog.capCondition60": "is before or equal to", "SSE.Views.DigitalFilterDialog.capCondition7": "zaczyna się z", "SSE.Views.DigitalFilterDialog.capCondition8": "nie zaczyna się od", "SSE.Views.DigitalFilterDialog.capCondition9": "kończy się z", "SSE.Views.DigitalFilterDialog.capOr": "lub", "SSE.Views.DigitalFilterDialog.textNoFilter": "Brak filtru", "SSE.Views.DigitalFilterDialog.textShowRows": "Pokaż wiersze gdzie", "SSE.Views.DigitalFilterDialog.textUse1": "Użyj ?, aby prz<PERSON><PERSON><PERSON><PERSON> pojedynczy znak", "SSE.Views.DigitalFilterDialog.textUse2": "Użyj *, aby przeds<PERSON>wić dowolną serię znaków", "SSE.Views.DigitalFilterDialog.txtSelectDate": "Select date", "SSE.Views.DigitalFilterDialog.txtTitle": "Niestandardowy filtr", "SSE.Views.DocumentHolder.advancedEquationText": "Equation settings", "SSE.Views.DocumentHolder.advancedImgText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> us<PERSON> obrazu", "SSE.Views.DocumentHolder.advancedShapeText": "Zaawansowane ustawienia kształtu", "SSE.Views.DocumentHolder.advancedSlicerText": "Zaawansowane ustawienia Fragmentatora", "SSE.Views.DocumentHolder.allLinearText": "All - Linear", "SSE.Views.DocumentHolder.allProfText": "All - Professional", "SSE.Views.DocumentHolder.bottomCellText": "Wyrównaj do dołu", "SSE.Views.DocumentHolder.bulletsText": "Kule i numeracja", "SSE.Views.DocumentHolder.centerCellText": "Wyrównaj do środka", "SSE.Views.DocumentHolder.chartDataText": "Select Chart Data", "SSE.Views.DocumentHolder.chartText": "<PERSON><PERSON><PERSON><PERSON><PERSON>e ustawienia wykresu", "SSE.Views.DocumentHolder.chartTypeText": "Zmień typ wykresu", "SSE.Views.DocumentHolder.currLinearText": "Current - Linear", "SSE.Views.DocumentHolder.currProfText": "Current - Professional", "SSE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.direct270Text": "Obróć tekst w górę", "SSE.Views.DocumentHolder.direct90Text": "Obróć tekst w dół", "SSE.Views.DocumentHolder.directHText": "Poziomy", "SSE.Views.DocumentHolder.directionText": "Kierunek tekstu", "SSE.Views.DocumentHolder.editChartText": "<PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "SSE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON> lewa", "SSE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON> p<PERSON>a", "SSE.Views.DocumentHolder.insertRowAboveText": "Wiersz powyżej", "SSE.Views.DocumentHolder.insertRowBelowText": "Wiersz poniżej", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "Rzeczywisty rozmiar", "SSE.Views.DocumentHolder.removeHyperlinkText": "<PERSON><PERSON><PERSON> hiperlink", "SSE.Views.DocumentHolder.selectColumnText": "Wstaw kolumnę", "SSE.Views.DocumentHolder.selectDataText": "Dane kolumny", "SSE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.showEqToolbar": "Show Equation Toolbar", "SSE.Views.DocumentHolder.strDelete": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.strDetails": "Szczegóły sygnatury", "SSE.Views.DocumentHolder.strSetup": "Konfiguracja podpisu", "SSE.Views.DocumentHolder.strSign": "Podpisz", "SSE.Views.DocumentHolder.textAlign": "Wyrównaj", "SSE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "Wyślij do tła", "SSE.Views.DocumentHolder.textArrangeBackward": "Przenieś do tyłu", "SSE.Views.DocumentHolder.textArrangeForward": "Przenieś do przodu", "SSE.Views.DocumentHolder.textArrangeFront": "Przejdź na pierwszy plan", "SSE.Views.DocumentHolder.textAverage": "Średnia", "SSE.Views.DocumentHolder.textBullets": "Lista punktowa", "SSE.Views.DocumentHolder.textCopyCells": "Ko<PERSON><PERSON>j komórki", "SSE.Views.DocumentHolder.textCount": "Zliczanie", "SSE.Views.DocumentHolder.textCrop": "Przytnij", "SSE.Views.DocumentHolder.textCropFill": "Wypełnij", "SSE.Views.DocumentHolder.textCropFit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textEditPoints": "Edit points", "SSE.Views.DocumentHolder.textEntriesList": "Wybierz z rozwijalnej listy", "SSE.Views.DocumentHolder.textFillDays": "Fill days", "SSE.Views.DocumentHolder.textFillFormatOnly": "Fill formatting only", "SSE.Views.DocumentHolder.textFillMonths": "Fill months", "SSE.Views.DocumentHolder.textFillSeries": "Fill series", "SSE.Views.DocumentHolder.textFillWeekdays": "Fill weekdays", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Fill without formatting", "SSE.Views.DocumentHolder.textFillYears": "Fill years", "SSE.Views.DocumentHolder.textFlashFill": "Flash fill", "SSE.Views.DocumentHolder.textFlipH": "Odwróć w poziomie", "SSE.Views.DocumentHolder.textFlipV": "Odwróć w pionie", "SSE.Views.DocumentHolder.textFreezePanes": "Zablokuj panele", "SSE.Views.DocumentHolder.textFromFile": "Z pliku", "SSE.Views.DocumentHolder.textFromStorage": "Z magazynu", "SSE.Views.DocumentHolder.textFromUrl": "Z adresu URL", "SSE.Views.DocumentHolder.textGrowthTrend": "Growth trend", "SSE.Views.DocumentHolder.textLinearTrend": "Linear trend", "SSE.Views.DocumentHolder.textListSettings": "Ustawienia listy", "SSE.Views.DocumentHolder.textMacro": "Przypisz makro", "SSE.Views.DocumentHolder.textMax": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textMin": "Minimum", "SSE.Views.DocumentHolder.textMore": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textMoreFormats": "Więcej formatów", "SSE.Views.DocumentHolder.textNone": "Żaden", "SSE.Views.DocumentHolder.textNumbering": "Numerowanie", "SSE.Views.DocumentHolder.textReplace": "Zamień obraz", "SSE.Views.DocumentHolder.textResetCrop": "Reset crop", "SSE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate270": "<PERSON><PERSON><PERSON><PERSON><PERSON> w lewo o 90° ", "SSE.Views.DocumentHolder.textRotate90": "Obróć w prawo o 90°", "SSE.Views.DocumentHolder.textSaveAsPicture": "Save as picture", "SSE.Views.DocumentHolder.textSeries": "Series", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Wyrównaj do dołu", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Wyrównaj do środka", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Wyrównaj do lewej", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Wyrównaj do środka", "SSE.Views.DocumentHolder.textShapeAlignRight": "Wyrównaj do prawej", "SSE.Views.DocumentHolder.textShapeAlignTop": "Wyrównaj do góry", "SSE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textUndo": "Cof<PERSON>j", "SSE.Views.DocumentHolder.textUnFreezePanes": "Odblokuj panele", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "Arrow bullets", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Checkmark bullets", "SSE.Views.DocumentHolder.tipMarkersDash": "Dash bullets", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Filled rhombus bullets", "SSE.Views.DocumentHolder.tipMarkersFRound": "Filled round bullets", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Filled square bullets", "SSE.Views.DocumentHolder.tipMarkersHRound": "Hollow round bullets", "SSE.Views.DocumentHolder.tipMarkersStar": "Star bullets", "SSE.Views.DocumentHolder.topCellText": "Wyrównaj do góry", "SSE.Views.DocumentHolder.txtAccounting": "Księgowy", "SSE.Views.DocumentHolder.txtAddComment": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAddNamedRange": "Zdefiniuj <PERSON>", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "Rosnąco", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Automatycznie dopasuj szerokość kolumny", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Automatycznie dopasuj wysokość wiersza", "SSE.Views.DocumentHolder.txtAverage": "Average", "SSE.Views.DocumentHolder.txtCellFormat": "Format cells", "SSE.Views.DocumentHolder.txtClear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearFormat": "Formatowanie", "SSE.Views.DocumentHolder.txtClearHyper": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearPivotField": "Clear filter from {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wybrane grupy Sparkline", "SSE.Views.DocumentHolder.txtClearSparklines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wybrane pola typu Sparkline", "SSE.Views.DocumentHolder.txtClearText": "Tekstowe", "SSE.Views.DocumentHolder.txtCollapse": "Collapse", "SSE.Views.DocumentHolder.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.DocumentHolder.txtColumn": "Wstaw kolumnę", "SSE.Views.DocumentHolder.txtColumnWidth": "Ustaw szerokość kolumny", "SSE.Views.DocumentHolder.txtCondFormat": "Formatowanie warunkowe", "SSE.Views.DocumentHolder.txtCopy": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCount": "Count", "SSE.Views.DocumentHolder.txtCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Niestandardowy rozmiar kolumny", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Niestandardowa wysokość wiersza", "SSE.Views.DocumentHolder.txtCustomSort": "Niestandardowe sortowanie", "SSE.Views.DocumentHolder.txtCut": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDateLong": "Long Date", "SSE.Views.DocumentHolder.txtDateShort": "Short Date", "SSE.Views.DocumentHolder.txtDelete": "Usuń", "SSE.Views.DocumentHolder.txtDelField": "Remove", "SSE.Views.DocumentHolder.txtDescending": "Malejąco", "SSE.Views.DocumentHolder.txtDifference": "Difference from", "SSE.Views.DocumentHolder.txtDistribHor": "Rozdziel poziomo", "SSE.Views.DocumentHolder.txtDistribVert": "Rozdziel pionowo", "SSE.Views.DocumentHolder.txtEditComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtEditObject": "Edit object", "SSE.Views.DocumentHolder.txtExpand": "Expand", "SSE.Views.DocumentHolder.txtExpandCollapse": "Expand/Collapse", "SSE.Views.DocumentHolder.txtExpandEntire": "Expand Entire Field", "SSE.Views.DocumentHolder.txtFieldSettings": "Field settings", "SSE.Views.DocumentHolder.txtFilter": "Filtr", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filtruj po kolorze komórki", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filtruj po kolorze czcionki", "SSE.Views.DocumentHolder.txtFilterValue": "Filtruj po wybranych wartościach komórki", "SSE.Views.DocumentHolder.txtFormula": "Wstaw funkcję", "SSE.Views.DocumentHolder.txtFraction": "Ułamkowe", "SSE.Views.DocumentHolder.txtGeneral": "Ogólny", "SSE.Views.DocumentHolder.txtGetLink": "Get link to this range", "SSE.Views.DocumentHolder.txtGrandTotal": "Grand total", "SSE.Views.DocumentHolder.txtGroup": "Grupa", "SSE.Views.DocumentHolder.txtHide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtIndex": "Index", "SSE.Views.DocumentHolder.txtInsert": "Wstaw", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hiperlink", "SSE.Views.DocumentHolder.txtInsImage": "Insert image from file", "SSE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "SSE.Views.DocumentHolder.txtLabelFilter": "Label filters", "SSE.Views.DocumentHolder.txtMax": "Max", "SSE.Views.DocumentHolder.txtMin": "Min", "SSE.Views.DocumentHolder.txtMoreOptions": "More options", "SSE.Views.DocumentHolder.txtNormal": "No calculation", "SSE.Views.DocumentHolder.txtNumber": "Numeryczny", "SSE.Views.DocumentHolder.txtNumFormat": "Format liczbowy", "SSE.Views.DocumentHolder.txtPaste": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtPercent": "% of", "SSE.Views.DocumentHolder.txtPercentage": "Procentowe", "SSE.Views.DocumentHolder.txtPercentDiff": "% difference from", "SSE.Views.DocumentHolder.txtPercentOfCol": "% of column total", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% of grand total", "SSE.Views.DocumentHolder.txtPercentOfParent": "% of parent total", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% of parent column total", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% of parent row total", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% running total in", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% of row total", "SSE.Views.DocumentHolder.txtPivotSettings": "Pivot Table settings", "SSE.Views.DocumentHolder.txtProduct": "Product", "SSE.Views.DocumentHolder.txtRankAscending": "Rank smallest to largest", "SSE.Views.DocumentHolder.txtRankDescending": "Rank largest to smallest", "SSE.Views.DocumentHolder.txtReapply": "Ponownie zastosuj", "SSE.Views.DocumentHolder.txtRefresh": "Refresh", "SSE.Views.DocumentHolder.txtRow": "Cały wiersz", "SSE.Views.DocumentHolder.txtRowHeight": "Ustaw wys<PERSON>ść w<PERSON>za", "SSE.Views.DocumentHolder.txtRunTotal": "Running total in", "SSE.Views.DocumentHolder.txtScientific": "Naukowy", "SSE.Views.DocumentHolder.txtSelect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftDown": "Przesuń komórki w dół", "SSE.Views.DocumentHolder.txtShiftLeft": "Przesuń komórki w lewo", "SSE.Views.DocumentHolder.txtShiftRight": "Przesuń komórki w prawo", "SSE.Views.DocumentHolder.txtShiftUp": "Przesuń komórki w górę", "SSE.Views.DocumentHolder.txtShow": "Po<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShowAs": "Show values as", "SSE.Views.DocumentHolder.txtShowComment": "Pokaż komentarz", "SSE.Views.DocumentHolder.txtShowDetails": "Show details", "SSE.Views.DocumentHolder.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSortCellColor": "Wybrany kolor komórki na górze", "SSE.Views.DocumentHolder.txtSortFontColor": "Wybrany kolor czcionki na górze", "SSE.Views.DocumentHolder.txtSortOption": "More sort options", "SSE.Views.DocumentHolder.txtSparklines": "Sparkline", "SSE.Views.DocumentHolder.txtSubtotalField": "Subtotal", "SSE.Views.DocumentHolder.txtSum": "Sum", "SSE.Views.DocumentHolder.txtSummarize": "Summarize values by", "SSE.Views.DocumentHolder.txtText": "Tekstowe", "SSE.Views.DocumentHolder.txtTextAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> us<PERSON> akapitu", "SSE.Views.DocumentHolder.txtTime": "Czas", "SSE.Views.DocumentHolder.txtTop10": "Top 10", "SSE.Views.DocumentHolder.txtUngroup": "Rozgrupuj", "SSE.Views.DocumentHolder.txtValueFieldSettings": "Value field settings", "SSE.Views.DocumentHolder.txtValueFilter": "Value filters", "SSE.Views.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "Wyrównaj wertykalnie", "SSE.Views.ExternalLinksDlg.closeButtonText": "Close", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Automatically update data from the linked sources", "SSE.Views.ExternalLinksDlg.textChange": "Change source", "SSE.Views.ExternalLinksDlg.textDelete": "Break links", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Break all links", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textOpen": "Open source", "SSE.Views.ExternalLinksDlg.textSource": "Source", "SSE.Views.ExternalLinksDlg.textStatus": "Status", "SSE.Views.ExternalLinksDlg.textUnknown": "Unknown", "SSE.Views.ExternalLinksDlg.textUpdate": "Update values", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Update all", "SSE.Views.ExternalLinksDlg.textUpdating": "Updating...", "SSE.Views.ExternalLinksDlg.txtTitle": "External links", "SSE.Views.FieldSettingsDialog.strLayout": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.strSubtotals": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.FieldSettingsDialog.textReport": "<PERSON><PERSON> raportu", "SSE.Views.FieldSettingsDialog.textTitle": "Parametry pól", "SSE.Views.FieldSettingsDialog.txtAverage": "Średnia", "SSE.Views.FieldSettingsDialog.txtBlank": "Wstaw puste wiersze po każdym elemencie", "SSE.Views.FieldSettingsDialog.txtBottom": "Pokaż na dole grupy", "SSE.Views.FieldSettingsDialog.txtCompact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtCount": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtCountNums": "Odliczać cyfry", "SSE.Views.FieldSettingsDialog.txtCustomName": "Niestandardowa nazwa", "SSE.Views.FieldSettingsDialog.txtEmpty": "Pokaż elementy bez danych", "SSE.Views.FieldSettingsDialog.txtMax": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtMin": "Minimum", "SSE.Views.FieldSettingsDialog.txtOutline": "Struktura", "SSE.Views.FieldSettingsDialog.txtProduct": "Produkt", "SSE.Views.FieldSettingsDialog.txtRepeat": "Powtórz etykiety elementów w każdym wierszu", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Pokaż sumy częściowe", "SSE.Views.FieldSettingsDialog.txtSourceName": "Nazwa źródła:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtSummarize": "Funkcje dla sum częściowych", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabelaryczny", "SSE.Views.FieldSettingsDialog.txtTop": "Pokaż na górze grupy", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.ariaFileMenu": "File menu", "SSE.Views.FileMenu.btnBackCaption": "Otwórz lokalizację pliku", "SSE.Views.FileMenu.btnCloseEditor": "Close File", "SSE.Views.FileMenu.btnCloseMenuCaption": "Zamknij menu", "SSE.Views.FileMenu.btnCreateNewCaption": "Utwórz nowy", "SSE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON> jako", "SSE.Views.FileMenu.btnExitCaption": "Wyjdź", "SSE.Views.FileMenu.btnExportToPDFCaption": "Export to PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "Otwórz", "SSE.Views.FileMenu.btnHelpCaption": "Pomoc", "SSE.Views.FileMenu.btnHistoryCaption": "<PERSON> wersji", "SSE.Views.FileMenu.btnInfoCaption": "Informacje o arkuszu kalk<PERSON>jnym", "SSE.Views.FileMenu.btnPrintCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnProtectCaption": "Chroń skoroszyt", "SSE.Views.FileMenu.btnRecentFilesCaption": "Otwórz ostatnie", "SSE.Views.FileMenu.btnRenameCaption": "Zmień nazwę", "SSE.Views.FileMenu.btnReturnCaption": "Powrót do arkusza", "SSE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveAsCaption": "<PERSON>ap<PERSON>z jako", "SSE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Zapisz kopię jako", "SSE.Views.FileMenu.btnSettingsCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "SSE.Views.FileMenu.btnToEditCaption": "Ed<PERSON><PERSON>j a<PERSON>z <PERSON>y", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Pusty arkusz <PERSON>y", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Utwórz nowy", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Zatwierdź", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Dodaj autora", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "<PERSON><PERSON>j te<PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplikacja", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Zmień prawa dostępu", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Komentarz", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Utworzono", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Ostatnio zmodyfikowany przez", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Ostatnia modyfikacja", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Właściciel", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Lokalizacja", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON><PERSON>, które mają prawa", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Informacje o arkuszu kalk<PERSON>jnym", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tagi", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Przesłano", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Access Rights", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Zmień prawa dostępu", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON><PERSON>, które mają prawa", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Zatwierdź", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Tryb współtworzenia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Use 1904 date system", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Separator l<PERSON><PERSON> dzie<PERSON>ch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Język słownika", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Enable iterative calculation", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Podpowiedź czcionki", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Język formuły", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Przykład: SUMA; MIN; MAX; LICZYĆ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Show function tooltip", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Show horizontal scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Ignoruj słowa pisane WIELKIMI LITERAMI", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Ignoruj słowa z liczbami", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Ustawienia Makr", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Maximum change", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Maximum iterations", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Pokaż przycisk opcji wklejania po wklejeniu zawartości", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "R1C1 reference style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Ustawienia regionaln", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Przykład:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTL interface", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Show comments in sheet", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Show changes from other users", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Show resolved comments", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Snapped to the grid while scrolling", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Ścisły", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Tab style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Motyw interfejsu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Separator <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Jednostka miary", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Używaj separatorów na podstawie ustawień regionalnych", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Show vertical scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Domyślna wartość powiększenia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Każde 10 minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Każde 30 minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Każde 5 minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Automatyczne odzyskiwanie", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Automatyczny zapis", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Wyłączony", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "Fill", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Zapisywanie wersji p<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "Line", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Każda minuta", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Styl linków", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "Advanced settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "Appearance", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Opcje Autokorekty...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Białoruski", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Domyślny tryb pamięci podręcznej", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Calculating", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centymetr", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Współpraca", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Customize quick access", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Editing and saving", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Real-time co-editing. All changes are saved automatically", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Węgierski", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "Armenian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonezyjski", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Koreański", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Last used", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "jak OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Norweski", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Sprawdzanie", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Portugalski (Brazylia)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "<PERSON>ski (Portugalia)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Pokaż przycisk szybkiego drukowania w nagłówku edytora", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Region", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Włącz Wszystkie", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Włącz wszystkie makra bez powiadomienia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Turn on screen reader support", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Default sheet direction", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "This setting will affect only the new sheets", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Left-to-right", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Right-to-left", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Wyłącz Wszystkie", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Wyłącz wszystkie makra bez powiadomienia", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Użyj przycisku \"Zapisz\", aby <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, które ty i inni dokonują", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Szwedzki", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Use toolbar color as tabs background", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Use Alt key to navigate the user interface using the keyboard", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Use Option key to navigate the user interface using the keyboard", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Pokaż powiadomienie", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Wyłącz wszystkie makra z powiadomieniem", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "jak <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "<PERSON><PERSON><PERSON> rob<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Ostrzeżenie", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "z hasłem", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "\nChroń arkusz kalkulacyjny", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Z s<PERSON>gnaturą", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Valid signatures have been added to the spreadsheet.<br>The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Ensure the integrity of the spreadsheet by adding an<br>invisible digital signature", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Ed<PERSON><PERSON>j a<PERSON>z <PERSON>y", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Edycja spowoduje usunięcie podpisów z arkusza kalkulacyjnego. <br> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Ten arkusz kalkulacyjny został zabezpieczony hasłem", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Encrypt this spreadsheet with a password", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Ten arkusz kalkulacyjny musi być pod<PERSON>.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Prawidłowe podpisy zostały dodane do arkusza kalkulacyjnego. Arkusz kalkulacyjny jest chroniony przed edycją.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Niektóre podpisy cyfrowe w arkuszu kalkulacyjnym są nieprawidłowe lub nie można ich zweryfikować. <PERSON><PERSON><PERSON> kalkula<PERSON>jny jest chroniony przed edycją", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Download as", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Zapisz kopię jako", "SSE.Views.FillSeriesDialog.textAuto": "AutoFill", "SSE.Views.FillSeriesDialog.textCols": "Columns", "SSE.Views.FillSeriesDialog.textDate": "Date", "SSE.Views.FillSeriesDialog.textDateUnit": "Date unit", "SSE.Views.FillSeriesDialog.textDay": "Day", "SSE.Views.FillSeriesDialog.textGrowth": "Growth", "SSE.Views.FillSeriesDialog.textLinear": "Linear", "SSE.Views.FillSeriesDialog.textMonth": "Month", "SSE.Views.FillSeriesDialog.textRows": "Rows", "SSE.Views.FillSeriesDialog.textSeries": "Series in", "SSE.Views.FillSeriesDialog.textStep": "Step value", "SSE.Views.FillSeriesDialog.textStop": "Stop value", "SSE.Views.FillSeriesDialog.textTitle": "Series", "SSE.Views.FillSeriesDialog.textTrend": "Trend", "SSE.Views.FillSeriesDialog.textType": "Type", "SSE.Views.FillSeriesDialog.textWeek": "Weekday", "SSE.Views.FillSeriesDialog.textYear": "Year", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FormatRulesEditDlg.fillColor": "<PERSON><PERSON> wypełnienia", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Ostrzeżenie", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 skala kolorów", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 skala kolorów", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Wszystkie krawędzie", "SSE.Views.FormatRulesEditDlg.textAppearance": "Wygląd kolumny", "SSE.Views.FormatRulesEditDlg.textApply": "Zastosuj do zakresu", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automatycznie", "SSE.Views.FormatRulesEditDlg.textAxis": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Kierunek kolumny", "SSE.Views.FormatRulesEditDlg.textBold": "Pogrubione", "SSE.Views.FormatRulesEditDlg.textBorder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersColor": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Styl obramowania", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Dolne krawędzie", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Nie można dodać formatowania warunkowego.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Środek komórki", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Wewnątrz pionowych granic", "SSE.Views.FormatRulesEditDlg.textClear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textColor": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textContext": "Kontekst", "SSE.Views.FormatRulesEditDlg.textCustom": "Specjalny", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Przekątna granica z góry na dół", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Przekątna granica od dołu do góry", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Wprowadź prawidłową formułę.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "<PERSON><PERSON><PERSON><PERSON> wprowadzonej formuły nie jest liczbą, da<PERSON><PERSON>, god<PERSON><PERSON> ani ciągiem.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "<PERSON><PERSON><PERSON>.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "Wprowadzona wartość nie jest prawidłową liczbą, da<PERSON><PERSON>, god<PERSON><PERSON> ani ciągiem.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "<PERSON><PERSON><PERSON><PERSON> {0} musi by<PERSON> wię<PERSON><PERSON> niż wartość {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Wprowadź liczbę od {0} do {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Wypełnij", "SSE.Views.FormatRulesEditDlg.textFormat": "Format", "SSE.Views.FormatRulesEditDlg.textFormula": "Formuła", "SSE.Views.FormatRulesEditDlg.textGradient": "Gradient", "SSE.Views.FormatRulesEditDlg.textIconLabel": "kiedy {0} {1} i", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "kiedy {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "g<PERSON> to", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Co najmniej jeden zakres danych ikony nakłada się. <br> <PERSON><PERSON><PERSON><PERSON> warto<PERSON> dla zakresów danych ikony, aby zakresy się nie nakładały.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Wewną<PERSON><PERSON> granic", "SSE.Views.FormatRulesEditDlg.textInvalid": "Nieprawidłowy zakres danych.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "BŁĄD! Niepoprawny zakres komórek", "SSE.Views.FormatRulesEditDlg.textItalic": "Ku<PERSON>ywa", "SSE.Views.FormatRulesEditDlg.textItem": "Element", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Od lewej do prawej", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLongBar": "Najdłuższa kolumna", "SSE.Views.FormatRulesEditDlg.textMaximum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Maks. punkt", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Wewnątrz poziomych granic", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Środkowy punkt", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimalny", "SSE.Views.FormatRulesEditDlg.textMinpoint": "<PERSON><PERSON> punkt", "SSE.Views.FormatRulesEditDlg.textNegative": "Negatywny", "SSE.Views.FormatRulesEditDlg.textNewColor": "Nowy niestandardowy kolor", "SSE.Views.FormatRulesEditDlg.textNoBorders": "Bez k<PERSON>ędzi", "SSE.Views.FormatRulesEditDlg.textNone": "Żaden", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Co najmniej jedna z podanych wartości nie jest prawidłową wartością procentową.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "<PERSON><PERSON><PERSON> {0} nie jest prawidłową wartością procentową.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Co najmniej jedna z podanych wartości nie jest prawidłowym percentylem.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "<PERSON><PERSON><PERSON> {0} nie jest prawidłowym percentylem.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Krawędzie zewnętrzne", "SSE.Views.FormatRulesEditDlg.textPercent": "Procent", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentyl", "SSE.Views.FormatRulesEditDlg.textPosition": "<PERSON><PERSON><PERSON>ja", "SSE.Views.FormatRulesEditDlg.textPositive": "Pozytywne", "SSE.Views.FormatRulesEditDlg.textPresets": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPreview": "Podgląd", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Nie można używać odniesień względnych dla skali kolorów, histogramów i zestawów ikon w warunku formatowania warunkowego.", "SSE.Views.FormatRulesEditDlg.textReverse": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> ikon", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Od prawej do lewej", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Prawe krawędzie", "SSE.Views.FormatRulesEditDlg.textRule": "Reguła", "SSE.Views.FormatRulesEditDlg.textSameAs": "Tak samo jak pozytywne", "SSE.Views.FormatRulesEditDlg.textSelectData": "<PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.FormatRulesEditDlg.textShortBar": "najkrótsza kolumna", "SSE.Views.FormatRulesEditDlg.textShowBar": "Pokaż tylko kolumnę", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Pokaż tylko ikonę", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Tego typu odwołania nie można użyć w formule formatowania warunkowego.<br>Zmień odwołanie na pojedynczą komórkę lub użyj odwołania z funkcją arkusza, taką jak =SUMA(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Pełny", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Przekreślenie", "SSE.Views.FormatRulesEditDlg.textSubscript": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSuperscript": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textTopBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textUnderline": "Podkreślenie", "SSE.Views.FormatRulesEditDlg.tipBorders": "Obramowania", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Format liczbowy", "SSE.Views.FormatRulesEditDlg.txtAccounting": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDate": "Data", "SSE.Views.FormatRulesEditDlg.txtDateLong": "Long date", "SSE.Views.FormatRulesEditDlg.txtDateShort": "Short date", "SSE.Views.FormatRulesEditDlg.txtEmpty": "To pole jest wymagane", "SSE.Views.FormatRulesEditDlg.txtFraction": "Ułamek", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Ogólny", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "Brak ikon<PERSON>", "SSE.Views.FormatRulesEditDlg.txtNumber": "Numeryczny", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Procentowe", "SSE.Views.FormatRulesEditDlg.txtScientific": "Naukowy", "SSE.Views.FormatRulesEditDlg.txtText": "Tekst", "SSE.Views.FormatRulesEditDlg.txtTime": "Czas", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Zmiana reguły formatowania", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Nowa reguła formatowania", "SSE.Views.FormatRulesManagerDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.lockText": "Zablokowany", "SSE.Views.FormatRulesManagerDlg.text1Above": "Na 1 odchylenie standardowe powyżej średniej", "SSE.Views.FormatRulesManagerDlg.text1Below": "Na 1 odchylenie standardowe poniżej średniej", "SSE.Views.FormatRulesManagerDlg.text2Above": "Na 2 odchylenia standardowe powyżej średniej", "SSE.Views.FormatRulesManagerDlg.text2Below": "Na 2 odchylenia standardowe poniżej średniej", "SSE.Views.FormatRulesManagerDlg.text3Above": "Na 3 odchylenia standardowe powyżej średniej", "SSE.Views.FormatRulesManagerDlg.text3Below": "Na 3 odchylenia standardowe poniżej średniej", "SSE.Views.FormatRulesManagerDlg.textAbove": "Powyżej <PERSON>niej", "SSE.Views.FormatRulesManagerDlg.textApply": "Zastosuj do", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Wartość komórki zaczyna się od", "SSE.Views.FormatRulesManagerDlg.textBelow": "Poniżej średniej", "SSE.Views.FormatRulesManagerDlg.textBetween": "znajduje się między {0} a {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Wartość komórki", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Skala kolorów", "SSE.Views.FormatRulesManagerDlg.textContains": "Wartość komórki zawiera", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Komórka zawiera pustą warto<PERSON>", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Komórka zawiera błąd", "SSE.Views.FormatRulesManagerDlg.textDelete": "Usuń", "SSE.Views.FormatRulesManagerDlg.textDown": "Przenieś regułę w dół", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Zduplikowane wartości", "SSE.Views.FormatRulesManagerDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEnds": "Wartość komórki kończy się na", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "R<PERSON>ne lub powyżej średniej", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "R<PERSON>ne lub poniż<PERSON>nie<PERSON>", "SSE.Views.FormatRulesManagerDlg.textFormat": "Format", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Zestaw ikon", "SSE.Views.FormatRulesManagerDlg.textNew": "Nowy", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "nie znajduje się między {0} a {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Wartość komórki nie zawiera", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Komórka nie zawiera pustej <PERSON>", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Komórka nie zawiera błędu", "SSE.Views.FormatRulesManagerDlg.textRules": "Reg<PERSON>ł<PERSON>", "SSE.Views.FormatRulesManagerDlg.textScope": "Pokaż reguły formatowania dla", "SSE.Views.FormatRulesManagerDlg.textSelectData": "<PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.FormatRulesManagerDlg.textSelection": "Obecnie zaznaczony fragment", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "Ta tabela przestawna", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Ten skoroszyt", "SSE.Views.FormatRulesManagerDlg.textThisTable": "Ta tabela", "SSE.Views.FormatRulesManagerDlg.textUnique": "Unikalne war<PERSON>", "SSE.Views.FormatRulesManagerDlg.textUp": "Przenieś regułę w górę", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Ten element jest właśnie edytowany przez innego użytkownika.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Formatowanie warunkowe", "SSE.Views.FormatSettingsDialog.textCategory": "Kategoria", "SSE.Views.FormatSettingsDialog.textDecimal": "Dziesiętny", "SSE.Views.FormatSettingsDialog.textFormat": "Formatowanie", "SSE.Views.FormatSettingsDialog.textLinked": "Połączone ze źródłem", "SSE.Views.FormatSettingsDialog.textSeparator": "Użyj separatora 1000", "SSE.Views.FormatSettingsDialog.textSymbols": "Symbole", "SSE.Views.FormatSettingsDialog.textTitle": "Format numeru", "SSE.Views.FormatSettingsDialog.txtAccounting": "Księgowy", "SSE.Views.FormatSettingsDialog.txtAs10": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "<PERSON><PERSON> (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "<PERSON><PERSON><PERSON> (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "<PERSON><PERSON><PERSON> (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "<PERSON><PERSON><PERSON><PERSON> (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Wyrównane poprawnie udziałów (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustom": "Niestandardowy", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Proszę ostrożnie wprowadzić niestandardowy format liczb. Edytor arkuszy kalkulacyjnych nie sprawdza formatów niestandardowych pod kątem błędów, które mogą mieć wpływ na plik xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "Data", "SSE.Views.FormatSettingsDialog.txtFraction": "Ułamkowe", "SSE.Views.FormatSettingsDialog.txtGeneral": "Ogólne", "SSE.Views.FormatSettingsDialog.txtNone": "Żaden", "SSE.Views.FormatSettingsDialog.txtNumber": "Numeryczny", "SSE.Views.FormatSettingsDialog.txtPercentage": "Procentowo", "SSE.Views.FormatSettingsDialog.txtSample": "Przykład:", "SSE.Views.FormatSettingsDialog.txtScientific": "Naukowy", "SSE.Views.FormatSettingsDialog.txtText": "Tekst", "SSE.Views.FormatSettingsDialog.txtTime": "Czas", "SSE.Views.FormatSettingsDialog.txtUpto1": "<PERSON><PERSON>j c<PERSON> (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Do dwóch cyfr (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "<PERSON> trzech cyfr (131/135)", "SSE.Views.FormulaDialog.sDescription": "Opis", "SSE.Views.FormulaDialog.textGroupDescription": "Wybierz grupę funk<PERSON>ji", "SSE.Views.FormulaDialog.textListDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtRecommended": "Rekomendowane", "SSE.Views.FormulaDialog.txtSearch": "Szukaj", "SSE.Views.FormulaDialog.txtTitle": "Wstaw funkcję", "SSE.Views.FormulaTab.capBtnRemoveArr": "Remove Arrows", "SSE.Views.FormulaTab.capBtnTraceDep": "Trace Dependents", "SSE.Views.FormulaTab.capBtnTracePrec": "Trace Precedents", "SSE.Views.FormulaTab.textAutomatic": "Automatycznie", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Oblicz bieżący arkusz", "SSE.Views.FormulaTab.textCalculateWorkbook": "<PERSON><PERSON><PERSON><PERSON> skoroszyt", "SSE.Views.FormulaTab.textManual": "Ręczny", "SSE.Views.FormulaTab.tipCalculate": "Ponowne obliczenie", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "<PERSON><PERSON>licz cały skoroszyt", "SSE.Views.FormulaTab.tipRemoveArr": "Remove the arrows drawn by Trace Precedents or Trace Dependents", "SSE.Views.FormulaTab.tipShowFormulas": "Display the formula in each cell instead of the resulting value", "SSE.Views.FormulaTab.tipTraceDep": "Show arrows that indicate which cells are affected by the value of the selected cell", "SSE.Views.FormulaTab.tipTracePrec": "Show arrows that indicate which cells affect the value of the selected cell", "SSE.Views.FormulaTab.tipWatch": "Add cells to the Watch Window list", "SSE.Views.FormulaTab.txtAdditional": "<PERSON><PERSON>", "SSE.Views.FormulaTab.txtAutosum": "Autosumowanie", "SSE.Views.FormulaTab.txtAutosumTip": "Podsumowanie", "SSE.Views.FormulaTab.txtCalculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormulaTip": "Wstaw funkcję", "SSE.Views.FormulaTab.txtMore": "<PERSON><PERSON>", "SSE.Views.FormulaTab.txtRecent": "Ostatnio używane", "SSE.Views.FormulaTab.txtRemDep": "Remove Dependents Arrows", "SSE.Views.FormulaTab.txtRemPrec": "Remove Precedents Arrows", "SSE.Views.FormulaTab.txtShowFormulas": "Show Formulas", "SSE.Views.FormulaTab.txtWatch": "Watch Window", "SSE.Views.FormulaWizard.textAny": "każ<PERSON>", "SSE.Views.FormulaWizard.textArgument": "Argument", "SSE.Views.FormulaWizard.textFunction": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textFunctionRes": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textHelp": "Pomoc w tej funkcji", "SSE.Views.FormulaWizard.textLogical": "Logiczny", "SSE.Views.FormulaWizard.textNoArgs": "<PERSON> funkcja nie ma <PERSON>", "SSE.Views.FormulaWizard.textNoArgsDesc": "this argument has no description", "SSE.Views.FormulaWizard.textNumber": "Numer", "SSE.Views.FormulaWizard.textReadMore": "Read more", "SSE.Views.FormulaWizard.textRef": "odwołanie", "SSE.Views.FormulaWizard.textText": "Tekst", "SSE.Views.FormulaWizard.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textValue": "Wynik formuły", "SSE.Views.GoalSeekDlg.textChangingCell": "By changing cell", "SSE.Views.GoalSeekDlg.textDataRangeError": "The formula is missing a range", "SSE.Views.GoalSeekDlg.textMustContainFormula": "The cell must contain a formula", "SSE.Views.GoalSeekDlg.textMustContainValue": "Cell must contain a value", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Formula in cell must result in a number", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Reference must be to a single cell", "SSE.Views.GoalSeekDlg.textSelectData": "Select data", "SSE.Views.GoalSeekDlg.textSetCell": "Set cell", "SSE.Views.GoalSeekDlg.textTitle": "Goal seek", "SSE.Views.GoalSeekDlg.textToValue": "To value", "SSE.Views.GoalSeekDlg.txtEmpty": "This field is required", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.GoalSeekStatusDlg.textContinue": "Continue", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Current value:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Goal seeking with cell {0} found a solution.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Goal seeking with cell {0} may not have found a solution.", "SSE.Views.GoalSeekStatusDlg.textPause": "Pause", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Goal seeking with cell {0} on iteration #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "Step", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Target value:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Goal seek status", "SSE.Views.HeaderFooterDialog.textAlign": "Wyrównaj do marginesów strony", "SSE.Views.HeaderFooterDialog.textAll": "Wszystkie strony", "SSE.Views.HeaderFooterDialog.textBold": "Pogrubione", "SSE.Views.HeaderFooterDialog.textCenter": "Środek", "SSE.Views.HeaderFooterDialog.textColor": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textDate": "Data", "SSE.Views.HeaderFooterDialog.textDiffFirst": "<PERSON><PERSON> pier<PERSON> strona", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Różne nieparzyste i parzyste strony", "SSE.Views.HeaderFooterDialog.textEven": "<PERSON><PERSON><PERSON><PERSON> strona", "SSE.Views.HeaderFooterDialog.textFileName": "Nazwa pliku", "SSE.Views.HeaderFooterDialog.textFirst": "<PERSON><PERSON><PERSON> strona", "SSE.Views.HeaderFooterDialog.textFooter": "Stopka", "SSE.Views.HeaderFooterDialog.textHeader": "Nagłówek", "SSE.Views.HeaderFooterDialog.textImage": "Picture", "SSE.Views.HeaderFooterDialog.textInsert": "Wstaw", "SSE.Views.HeaderFooterDialog.textItalic": "Ku<PERSON>ywa", "SSE.Views.HeaderFooterDialog.textLeft": "<PERSON>", "SSE.Views.HeaderFooterDialog.textMaxError": "Wpisany ciąg tekstowy jest za długi. Zmniejsz liczbę używanych znaków.", "SSE.Views.HeaderFooterDialog.textNewColor": "Nowy niestandardowy kolor", "SSE.Views.HeaderFooterDialog.textOdd": "<PERSON><PERSON><PERSON><PERSON><PERSON> strona", "SSE.Views.HeaderFooterDialog.textPageCount": "Liczba stron", "SSE.Views.HeaderFooterDialog.textPageNum": "Numer strony", "SSE.Views.HeaderFooterDialog.textPresets": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textRight": "Do <PERSON>", "SSE.Views.HeaderFooterDialog.textScale": "Skaluj z dokumentem", "SSE.Views.HeaderFooterDialog.textSheet": "Nazwa <PERSON>", "SSE.Views.HeaderFooterDialog.textStrikeout": "Przekreślenie", "SSE.Views.HeaderFooterDialog.textSubscript": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textSuperscript": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textTime": "Czas", "SSE.Views.HeaderFooterDialog.textTitle": "Ustawienia nagłówka/stopki", "SSE.Views.HeaderFooterDialog.textUnderline": "Podkreślenie", "SSE.Views.HeaderFooterDialog.tipFontName": "Czcionka", "SSE.Views.HeaderFooterDialog.tipFontSize": "Rozmiar <PERSON>ki", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Po<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Link do", "SSE.Views.HyperlinkSettingsDialog.strRange": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Wybrany zakres", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "<PERSON><PERSON>z tutaj podpis", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Wprowadź tutaj link", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "<PERSON><PERSON>j tutaj <PERSON>", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Link zew<PERSON>ętrzny", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Uzyskać link", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Wewnętrzny zakres danych", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "BŁĄD! Niepoprawny zakres komórek", "SSE.Views.HyperlinkSettingsDialog.textNames": "Zakresy zdefiniowane", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "SSE.Views.HyperlinkSettingsDialog.textSheets": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Tekst wskazówki na ekranie", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Ustawi<PERSON>link<PERSON>", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "To pole jest wymagane", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "To pole powinno by<PERSON> adresem URL w formacie \"http://www.example.com\"", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "To pole jest ogranicz<PERSON> do 2083 znaków", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "SSE.Views.ImageSettings.strTransparency": "Opacity", "SSE.Views.ImageSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "SSE.Views.ImageSettings.textCrop": "Przytnij", "SSE.Views.ImageSettings.textCropFill": "Wypełnij", "SSE.Views.ImageSettings.textCropFit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropToShape": "Przytnij do kształtu", "SSE.Views.ImageSettings.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textEditObject": "Ed<PERSON><PERSON>j obiekt", "SSE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textFromFile": "Z pliku", "SSE.Views.ImageSettings.textFromStorage": "Z magazynu", "SSE.Views.ImageSettings.textFromUrl": "Z URL", "SSE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textHint270": "<PERSON><PERSON><PERSON><PERSON><PERSON> w lewo o 90° ", "SSE.Views.ImageSettings.textHint90": "Obróć w prawo o 90°", "SSE.Views.ImageSettings.textHintFlipH": "Odwróć w poziomie", "SSE.Views.ImageSettings.textHintFlipV": "Odwróć w pionie", "SSE.Views.ImageSettings.textInsert": "Zamień obraz", "SSE.Views.ImageSettings.textKeepRatio": "Stałe proporcje", "SSE.Views.ImageSettings.textOriginalSize": "Rzeczywisty rozmiar", "SSE.Views.ImageSettings.textRecentlyUsed": "Ostatnio używane", "SSE.Views.ImageSettings.textResetCrop": "Reset crop", "SSE.Views.ImageSettings.textRotate90": "O<PERSON><PERSON>óć o 90°", "SSE.Views.ImageSettings.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textSize": "Rozmiar", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Nie przesuwaj lub nie zmieniaj rozmiaru z komórkami", "SSE.Views.ImageSettingsAdvanced.textAlt": "Tekst alternatywny", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Opis", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Alternatywna, tekstowa prezentacja wizualnych informacji o obiektach, która będzie czytana osobom z wadami wzroku lub zmysłu poznawczego, aby pomóc im lepiej zrozumieć, jakie informacje znajdują się na obrazie, ksz<PERSON>łcie, wykresie lub tabeli.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Odwrócony ", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Poziomo ", "SSE.Views.ImageSettingsAdvanced.textOneCell": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ale nie zmieniaj rozmiaru komórek", "SSE.Views.ImageSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textSnap": "Przyciągaj do komórki", "SSE.Views.ImageSettingsAdvanced.textTitle": "Obraz - <PERSON><PERSON><PERSON><PERSON>wane us<PERSON>wi<PERSON>", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Przenieś i zmień rozmiar komórek", "SSE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON> ", "SSE.Views.ImportFromXmlDialog.textDestination": "Choose, where to place the data", "SSE.Views.ImportFromXmlDialog.textExist": "Istniejący arkusz", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ImportFromXmlDialog.textNew": "<PERSON><PERSON> arkusz", "SSE.Views.ImportFromXmlDialog.textSelectData": "Select data", "SSE.Views.ImportFromXmlDialog.textTitle": "Import data", "SSE.Views.ImportFromXmlDialog.txtEmpty": "This field is required", "SSE.Views.LeftMenu.ariaLeftMenu": "Left menu", "SSE.Views.LeftMenu.tipAbout": "O programie", "SSE.Views.LeftMenu.tipChat": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipFile": "Plik", "SSE.Views.LeftMenu.tipPlugins": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSearch": "Szukaj", "SSE.Views.LeftMenu.tipSpellcheck": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "SSE.Views.LeftMenu.tipSupport": "Opinie i wsparcie", "SSE.Views.LeftMenu.txtDeveloper": "TRYB DEWELOPERA", "SSE.Views.LeftMenu.txtEditor": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.txtLimit": "Ograniczony dostęp", "SSE.Views.LeftMenu.txtTrial": "PRÓBNY TRYB", "SSE.Views.LeftMenu.txtTrialDev": "Próbny tryb programisty", "SSE.Views.MacroDialog.textMacro": "<PERSON><PERSON><PERSON> makro", "SSE.Views.MacroDialog.textTitle": "Przypisz makro", "SSE.Views.MainSettingsPrint.okButtonText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLandscape": "Pozioma", "SSE.Views.MainSettingsPrint.strLeft": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strMargins": "Mar<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPrintTitles": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>y", "SSE.Views.MainSettingsPrint.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strTop": "Góra", "SSE.Views.MainSettingsPrint.textActualSize": "Rzeczywisty rozmiar", "SSE.Views.MainSettingsPrint.textCustom": "Specjalny", "SSE.Views.MainSettingsPrint.textCustomOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textFitCols": "Dopasuj rozmiary wszystkich kolumn na stronie", "SSE.Views.MainSettingsPrint.textFitPage": "Dopasuj arkusz na jednej stronie", "SSE.Views.MainSettingsPrint.textFitRows": "Dopasuj wszystkie wiersze na jednej stronie", "SSE.Views.MainSettingsPrint.textPageOrientation": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.MainSettingsPrint.textPageScaling": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON><PERSON><PERSON><PERSON> strony", "SSE.Views.MainSettingsPrint.textPrintGrid": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Drukuj wiersze i kolumny", "SSE.Views.MainSettingsPrint.textRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Powtórz kolumny po lewej", "SSE.Views.MainSettingsPrint.textRepeatTop": "Powtórz wiersze z góry", "SSE.Views.MainSettingsPrint.textSettings": "Ustawienia dla", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Istniejące zakresy nazw nie mogą być edytowane, a nowe nie mogą zostać utworzone<br>w chwi<PERSON>, gdy niekt<PERSON> z nich są edytowane.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Zakresy zdefiniowane", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Ostrzeżenie", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Skoroszyt", "SSE.Views.NamedRangeEditDlg.textDataRange": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textExistName": "BŁĄD! Zakres o takiej samej nazwie już istnieje.", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Nazwa musi zaczynać się literą lub podkreśleniem i nie może zawierać nieprawidłowych znaków.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "BŁĄD! Niepoprawny zakres komórki", "SSE.Views.NamedRangeEditDlg.textIsLocked": "BŁĄD! Ten element jest edytowany przez innego użytkownika.", "SSE.Views.NamedRangeEditDlg.textName": "Nazwa", "SSE.Views.NamedRangeEditDlg.textReservedName": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON> pr<PERSON><PERSON><PERSON><PERSON>, jest już wzmiankowana w formułach komórkowych. Proszę użyć innej nazwy.", "SSE.Views.NamedRangeEditDlg.textScope": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textSelectData": "<PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.NamedRangeEditDlg.txtEmpty": "To pole jest wymagane", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Edytuj nazwę", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Nowa nazwa", "SSE.Views.NamedRangePasteDlg.textNames": "<PERSON><PERSON><PERSON><PERSON> zakresy", "SSE.Views.NamedRangePasteDlg.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.closeButtonText": "Zamknij", "SSE.Views.NameManagerDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.lockText": "Zablokowany", "SSE.Views.NameManagerDlg.textDataRange": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textDelete": "Usuń", "SSE.Views.NameManagerDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEmpty": "Nie utworzono jeszcze żadnych określonych zakresów.<br>Utwórz co najmniej jeden określony zakres i to pojawi się w tym polu.", "SSE.Views.NameManagerDlg.textFilter": "Filtr", "SSE.Views.NameManagerDlg.textFilterAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterDefNames": "Zakresy zdefiniowane", "SSE.Views.NameManagerDlg.textFilterSheet": "Nazwy przypisane do arkusza", "SSE.Views.NameManagerDlg.textFilterTableNames": "<PERSON><PERSON><PERSON> ta<PERSON>i", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Nazwy przypisane do skoroszytu", "SSE.Views.NameManagerDlg.textNew": "Nowy", "SSE.Views.NameManagerDlg.textnoNames": "Nie znaleziono żadnych zakresów pasujących do Twojego filtru.", "SSE.Views.NameManagerDlg.textRanges": "<PERSON><PERSON><PERSON><PERSON> zakresy", "SSE.Views.NameManagerDlg.textScope": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textWorkbook": "Skoroszyt", "SSE.Views.NameManagerDlg.tipIsLocked": "Ten element jest właśnie edytowany przez innego użytkownika.", "SSE.Views.NameManagerDlg.txtTitle": "Menadżer zdefiniowanych zakresów", "SSE.Views.NameManagerDlg.warnDelete": "<PERSON>zy na pewno chcesz usunąć nazwę {0}?", "SSE.Views.PageMarginsDialog.textBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textCenter": "Center on page", "SSE.Views.PageMarginsDialog.textHor": "Horizontally", "SSE.Views.PageMarginsDialog.textLeft": "<PERSON>", "SSE.Views.PageMarginsDialog.textRight": "Z prawej", "SSE.Views.PageMarginsDialog.textTitle": "Mar<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textVert": "Vertically", "SSE.Views.PageMarginsDialog.textWarning": "Warning", "SSE.Views.PageMarginsDialog.warnCheckMargings": "Margins are incorrect", "SSE.Views.ParagraphSettings.strLineHeight": "Rozstaw wierszy", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Odstępy akapitu", "SSE.Views.ParagraphSettings.strSpacingAfter": "Po", "SSE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "SSE.Views.ParagraphSettings.textAt": "W", "SSE.Views.ParagraphSettings.textAtLeast": "Co najmniej", "SSE.Views.ParagraphSettings.textAuto": "Mnożnik", "SSE.Views.ParagraphSettings.textExact": "Dokładnie", "SSE.Views.ParagraphSettings.txtAutoText": "Automatyczny", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "W tym polu zostaną wyświetlone określone karty", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Wszystkie duże znaki", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Podwójne przekreślenie", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Odstępy", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Interlinia", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Po", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Specjalne", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Na", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Czcionka", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Wcięcia i odstępy", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Małe litery", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Rozstaw", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Przekreślony", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Karta", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Wyrównanie", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Wielokrotne", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Rozstaw znaków", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Domyślna zakładka", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Efekty", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Dokładnie", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON><PERSON> w<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Wysunięcie", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Wyjustowany", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(brak)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Usuń", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Us<PERSON>ń wszystko", "SSE.Views.ParagraphSettingsAdvanced.textSet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Środek", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Pozycja karty", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Akapit - Ustawienia zaawansowane", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Automatyczny", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Delete", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "Duplicate", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Edit", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Formula", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Items Name", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "New", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Calculated Items in", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "równa się", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "nie kończy się z", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "zawiera", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "nie zaw<PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "pomiędzy", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "nie pomi<PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "nie równa się", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "jest wi<PERSON><PERSON><PERSON> niż", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "jest wi<PERSON><PERSON><PERSON> lub równy niż", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "jest mnie<PERSON><PERSON><PERSON> niż", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "jest mniej<PERSON>y lub równy niż", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "zaczyna się z", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "nie zaczyna się od", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "kończy się z", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Pokaż elementy z podpisem:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Pokaż elementy z:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Użyj ?, aby prz<PERSON><PERSON><PERSON><PERSON> pojedynczy znak", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Użyj *, aby przeds<PERSON>wić dowolną serię znaków", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "I", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Filtr etykiet", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textAuto": "Automatyczny", "SSE.Views.PivotGroupDialog.textBy": "Od", "SSE.Views.PivotGroupDialog.textDays": "dni", "SSE.Views.PivotGroupDialog.textEnd": "Kończąc się w", "SSE.Views.PivotGroupDialog.textError": "To pole musi zaw<PERSON> warto<PERSON> liczbową", "SSE.Views.PivotGroupDialog.textGreaterError": "Numer końcowy musi być większy niż numer początkowy", "SSE.Views.PivotGroupDialog.textHour": "god<PERSON>(y)", "SSE.Views.PivotGroupDialog.textMin": "minut(y)", "SSE.Views.PivotGroupDialog.textMonth": "<PERSON><PERSON><PERSON><PERSON>(e)", "SSE.Views.PivotGroupDialog.textNumDays": "Liczba dni", "SSE.Views.PivotGroupDialog.textQuart": "Kwartały", "SSE.Views.PivotGroupDialog.textSec": "Sekundy", "SSE.Views.PivotGroupDialog.textStart": "<PERSON><PERSON><PERSON><PERSON>ć od:", "SSE.Views.PivotGroupDialog.textYear": "lata", "SSE.Views.PivotGroupDialog.txtTitle": "Grupowanie", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "You can use Calculated Items for basic calculations between different items within a single field", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Insert into formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "<PERSON><PERSON>", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Item name", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Items", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Read more", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Insert Calculated Item in", "SSE.Views.PivotSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "SSE.Views.PivotSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textFields": "W<PERSON>bierz pola", "SSE.Views.PivotSettings.textFilters": "Filtry", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddColumn": "Dodaj do kolumn", "SSE.Views.PivotSettings.txtAddFilter": "Dodaj do filtrów", "SSE.Views.PivotSettings.txtAddRow": "Dodaj do wierszy", "SSE.Views.PivotSettings.txtAddValues": "Dodaj do wartości", "SSE.Views.PivotSettings.txtFieldSettings": "Parametry pól", "SSE.Views.PivotSettings.txtMoveBegin": "Przenieś na początek", "SSE.Views.PivotSettings.txtMoveColumn": "Przenieś do kolumn", "SSE.Views.PivotSettings.txtMoveDown": "Przenieś w dół", "SSE.Views.PivotSettings.txtMoveEnd": "Przenieś na koniec", "SSE.Views.PivotSettings.txtMoveFilter": "Przenieś do filtrów", "SSE.Views.PivotSettings.txtMoveRow": "Przenieś do wierszy", "SSE.Views.PivotSettings.txtMoveUp": "Przenieś do góry", "SSE.Views.PivotSettings.txtMoveValues": "Przenieś do wartości", "SSE.Views.PivotSettings.txtRemove": "<PERSON><PERSON><PERSON> pole", "SSE.Views.PivotSettingsAdvanced.strLayout": "Tytuł i układ", "SSE.Views.PivotSettingsAdvanced.textAlt": "Alternatywny tekst", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Opis", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Alternatywna prezentacja wizualnych informacji o obiektach, które będą czytane osobom z wadami wzroku lub zmysłu poznawczego, aby le<PERSON><PERSON>, jakie informacje znajdują się na obrazie, ksz<PERSON>łtach, wykresie lub tabeli.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Autofit column widths on update", "SSE.Views.PivotSettingsAdvanced.textDataRange": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Źródło danych", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Wyświetl pola w obszarze filtra raportu", "SSE.Views.PivotSettingsAdvanced.textDown": "W dół, potem w prawo", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Nagłówki pól", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "BŁĄD! Niepoprawny zakres komórek", "SSE.Views.PivotSettingsAdvanced.textOver": "W prawo, a potem w dół", "SSE.Views.PivotSettingsAdvanced.textSelectData": "<PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Pokaż dla kolumn", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Pokaż nagłówki pól dla wierszy i kolumn", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Pokaż dla wierszy", "SSE.Views.PivotSettingsAdvanced.textTitle": "Tabela p<PERSON>estawna — op<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Liczba pól filtra raportu w kolumnie", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Liczba pól filtra raportu na wiersz", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "To pole jest wymagane", "SSE.Views.PivotSettingsAdvanced.txtName": "Nazwa", "SSE.Views.PivotShowDetailDialog.textDescription": "Choose the field containing the detail you want to show:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Show Detail", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON><PERSON> w<PERSON>", "SSE.Views.PivotTable.capGrandTotals": "<PERSON><PERSON>", "SSE.Views.PivotTable.capLayout": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.capSubtotals": "<PERSON><PERSON>", "SSE.Views.PivotTable.mniBottomSubtotals": "Pokaż wszystkie sumy częściowe na dole grupy", "SSE.Views.PivotTable.mniInsertBlankLine": "Wstaw pustą linię po każdym elemencie", "SSE.Views.PivotTable.mniLayoutCompact": "Pokaż w skróconej formie", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Nie powtarzaj wszystkich etykiet przedmiotów", "SSE.Views.PivotTable.mniLayoutOutline": "Pokaż w formie struktury", "SSE.Views.PivotTable.mniLayoutRepeat": "Powtórz wszystkie etykiety elementów", "SSE.Views.PivotTable.mniLayoutTabular": "Pokaż w formie tabelarycznej", "SSE.Views.PivotTable.mniNoSubtotals": "Nie pokazuj sum częściowych", "SSE.Views.PivotTable.mniOffTotals": "Wyłączone dla wierszy i kolumn", "SSE.Views.PivotTable.mniOnColumnsTotals": "Włącz tylko dla kolumn", "SSE.Views.PivotTable.mniOnRowsTotals": "Włącz tylko dla rzędów", "SSE.Views.PivotTable.mniOnTotals": "Włącz dla wierszy i kolumn", "SSE.Views.PivotTable.mniRemoveBlankLine": "Usuń pustą linię po każdym elemencie", "SSE.Views.PivotTable.mniTopSubtotals": "Pokaż wszystkie sumy częściowe na początku grupy", "SSE.Views.PivotTable.textColBanded": "Kolumny naprzemienne", "SSE.Views.PivotTable.textColHeader": "Nagłówki kolumn", "SSE.Views.PivotTable.textRowBanded": "Wiersze naprzemienne", "SSE.Views.PivotTable.textRowHeader": "Nagłówki wierszy", "SSE.Views.PivotTable.tipCalculatedItems": "Calculated items", "SSE.Views.PivotTable.tipCreatePivot": "Wstaw tabelę przestawną", "SSE.Views.PivotTable.tipGrandTotals": "Pokaż/ukryj sumy", "SSE.Views.PivotTable.tipRefresh": "Odśwież informacje ze źródła danych", "SSE.Views.PivotTable.tipRefreshCurrent": "Update the information from data source for the current table", "SSE.Views.PivotTable.tipSelect": "Zaznacz całą tabelę przestawną", "SSE.Views.PivotTable.tipSubtotals": "Pokaż/ukryj sumy częściowe", "SSE.Views.PivotTable.txtCalculatedItems": "Calculated Items", "SSE.Views.PivotTable.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.PivotTable.txtCreate": "Wstaw tabelę", "SSE.Views.PivotTable.txtExpandEntire": "Expand Entire Field", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Custom", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Dark", "SSE.Views.PivotTable.txtGroupPivot_Light": "Light", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Medium", "SSE.Views.PivotTable.txtPivotTable": "<PERSON><PERSON><PERSON> p<PERSON>esta<PERSON>a", "SSE.Views.PivotTable.txtRefresh": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtRefreshAll": "Refresh all", "SSE.Views.PivotTable.txtSelect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Pivot Table Style Dark", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Pivot Table Style Light", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Pivot Table Style Medium", "SSE.Views.PrintSettings.btnDownload": "Zapisz i pobierz", "SSE.Views.PrintSettings.btnExport": "Save & Export", "SSE.Views.PrintSettings.btnPrint": "Zapisz i drukuj", "SSE.Views.PrintSettings.strBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strLandscape": "Pozioma", "SSE.Views.PrintSettings.strLeft": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strMargins": "Mar<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPrintTitles": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>y", "SSE.Views.PrintSettings.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strShow": "Po<PERSON><PERSON>", "SSE.Views.PrintSettings.strTop": "Góra", "SSE.Views.PrintSettings.textActiveSheets": "Active sheets", "SSE.Views.PrintSettings.textActualSize": "Rzeczywisty rozmiar", "SSE.Views.PrintSettings.textAllSheets": "Wszystkie arkusze", "SSE.Views.PrintSettings.textCurrentSheet": "Obecny arkusz", "SSE.Views.PrintSettings.textCustom": "Specjalny", "SSE.Views.PrintSettings.textCustomOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textFitCols": "Dopasuj rozmiary wszystkich kolumn na stronie", "SSE.Views.PrintSettings.textFitPage": "Dopasuj arkusz na jednej stronie", "SSE.Views.PrintSettings.textFitRows": "Dopasuj wszystkie wiersze na jednej stronie", "SSE.Views.PrintSettings.textHideDetails": "<PERSON>k<PERSON>j <PERSON>egóły", "SSE.Views.PrintSettings.textIgnore": "Ignoruj o<PERSON><PERSON> d<PERSON>", "SSE.Views.PrintSettings.textLayout": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintSettings.textMarginsNormal": "Normal", "SSE.Views.PrintSettings.textMarginsWide": "Wide", "SSE.Views.PrintSettings.textPageOrientation": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.PrintSettings.textPages": "Pages:", "SSE.Views.PrintSettings.textPageScaling": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPageSize": "<PERSON><PERSON><PERSON><PERSON> strony", "SSE.Views.PrintSettings.textPrintGrid": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPrintHeadings": "Drukuj wiersze i kolumny", "SSE.Views.PrintSettings.textPrintRange": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textRange": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "SSE.Views.PrintSettings.textRepeatLeft": "Powtórz kolumny po lewej", "SSE.Views.PrintSettings.textRepeatTop": "Powtórz wiersze z góry", "SSE.Views.PrintSettings.textSelection": "Zaznaczenie", "SSE.Views.PrintSettings.textSettings": "Ustawienia arkusza", "SSE.Views.PrintSettings.textShowDetails": "Pokaż szczegóły", "SSE.Views.PrintSettings.textShowGrid": "Pokaż linie siatki", "SSE.Views.PrintSettings.textShowHeadings": "Pokaż nagłówki wierszy i kolumn", "SSE.Views.PrintSettings.textTitle": "Ustawienia drukowania", "SSE.Views.PrintSettings.textTitlePDF": "Ustawienia PDF", "SSE.Views.PrintSettings.textTo": "to", "SSE.Views.PrintSettings.txtMarginsLast": "Last Custom", "SSE.Views.PrintTitlesDialog.textFirstCol": "<PERSON><PERSON><PERSON> kolumna", "SSE.Views.PrintTitlesDialog.textFirstRow": "Pierwszy rząd", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Zablokuj kolumny", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Zablokowane rzędy", "SSE.Views.PrintTitlesDialog.textInvalidRange": "BŁĄD! Niepoprawny zakres komórek", "SSE.Views.PrintTitlesDialog.textLeft": "Powtórz kolumny po lewej", "SSE.Views.PrintTitlesDialog.textNoRepeat": "<PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "SSE.Views.PrintTitlesDialog.textSelectRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>y", "SSE.Views.PrintTitlesDialog.textTop": "Powtórz wiersze z góry", "SSE.Views.PrintWithPreview.txtActiveSheets": "Active sheets", "SSE.Views.PrintWithPreview.txtActualSize": "Rzeczywisty rozmiar", "SSE.Views.PrintWithPreview.txtAllSheets": "Wszystkie arkusze", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Zastosuj do wszystkich arkuszy", "SSE.Views.PrintWithPreview.txtBothSides": "Print on both sides", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "Flip pages on long edge", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "Flip pages on short edge", "SSE.Views.PrintWithPreview.txtBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCopies": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Obecny arkusz", "SSE.Views.PrintWithPreview.txtCustom": "<PERSON><PERSON><PERSON>ne", "SSE.Views.PrintWithPreview.txtCustomOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtEmptyTable": "Nie ma niczego do wydrukowania", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "First page number:", "SSE.Views.PrintWithPreview.txtFitCols": "Dopasuj rozmiary wszystkich kolumn na stronie", "SSE.Views.PrintWithPreview.txtFitPage": "Dopasuj arkusz na jednej stronie", "SSE.Views.PrintWithPreview.txtFitRows": "Dopasuj wszystkie wiersze na jednej stronie", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Gridlines and headings", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Ustawienia nagłówka/stopki", "SSE.Views.PrintWithPreview.txtIgnore": "Ignoruj o<PERSON><PERSON> d<PERSON>", "SSE.Views.PrintWithPreview.txtLandscape": "Pozioma", "SSE.Views.PrintWithPreview.txtLeft": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMargins": "Mar<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMarginsLast": "Last Custom", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Normal", "SSE.Views.PrintWithPreview.txtMarginsWide": "Wide", "SSE.Views.PrintWithPreview.txtOf": "of {0}", "SSE.Views.PrintWithPreview.txtOneSide": "Print one sided", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "SSE.Views.PrintWithPreview.txtPage": "Strona", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Błędny numer strony", "SSE.Views.PrintWithPreview.txtPageOrientation": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.PrintWithPreview.txtPages": "Pages:", "SSE.Views.PrintWithPreview.txtPageSize": "<PERSON><PERSON><PERSON><PERSON> strony", "SSE.Views.PrintWithPreview.txtPortrait": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPrintGrid": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Drukuj wiersze i kolumny", "SSE.Views.PrintWithPreview.txtPrintRange": "<PERSON><PERSON><PERSON> w<PERSON>", "SSE.Views.PrintWithPreview.txtPrintSides": "Print sides", "SSE.Views.PrintWithPreview.txtPrintTitles": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>y", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Zapisz jako PDF", "SSE.Views.PrintWithPreview.txtRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Powtórz kolumny po lewej", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Powtórz wiersze z góry", "SSE.Views.PrintWithPreview.txtRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSave": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtScaling": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSelection": "Zaznaczenie", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Ustawienia arkusza", "SSE.Views.PrintWithPreview.txtSheet": "Arkusz: {0}", "SSE.Views.PrintWithPreview.txtTo": "to", "SSE.Views.PrintWithPreview.txtTop": "Góra", "SSE.Views.ProtectDialog.textExistName": "BŁĄD! Zakres o takim tytule już istnieje", "SSE.Views.ProtectDialog.textInvalidName": "Nazwy zakresów muszą zaczynać się od litery i mogą zawierać tylko litery, cyfry i spacje.", "SSE.Views.ProtectDialog.textInvalidRange": "BŁĄD! Niepoprawny zakres komórek", "SSE.Views.ProtectDialog.textSelectData": "<PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.ProtectDialog.txtAllow": "Zezwól wszystkim użytkownikom tego arkusza na", "SSE.Views.ProtectDialog.txtAllowDescription": "You can unlock specific ranges for editing.", "SSE.Views.ProtectDialog.txtAllowRanges": "Allow edit ranges", "SSE.Views.ProtectDialog.txtAutofilter": "Użyj autofiltra", "SSE.Views.ProtectDialog.txtDelCols": "Usuwać kolumny", "SSE.Views.ProtectDialog.txtDelRows": "<PERSON><PERSON><PERSON> w<PERSON>", "SSE.Views.ProtectDialog.txtEmpty": "To pole jest wymagane", "SSE.Views.ProtectDialog.txtFormatCells": "Formatuj komórki", "SSE.Views.ProtectDialog.txtFormatCols": "Formatuj kolumny", "SSE.Views.ProtectDialog.txtFormatRows": "Formatuj <PERSON>", "SSE.Views.ProtectDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON>zaj<PERSON>ce nie jest identyczne", "SSE.Views.ProtectDialog.txtInsCols": "Wstaw kolumny", "SSE.Views.ProtectDialog.txtInsHyper": "Wstaw hiperłącze", "SSE.Views.ProtectDialog.txtInsRows": "Wstaw wiersze", "SSE.Views.ProtectDialog.txtObjs": "<PERSON><PERSON><PERSON>j o<PERSON>", "SSE.Views.ProtectDialog.txtOptional": "Opcjonalny", "SSE.Views.ProtectDialog.txtPassword": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPivot": "Użyj tabeli przestawnej i wykresu przestawnego", "SSE.Views.ProtectDialog.txtProtect": "Zabezpiecz", "SSE.Views.ProtectDialog.txtRange": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRangeName": "Nazwa", "SSE.Views.ProtectDialog.txtRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtScen": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtSelLocked": "Zaznacz zablokowane komórki", "SSE.Views.ProtectDialog.txtSelUnLocked": "Zaznacz odblokowane komórki", "SSE.Views.ProtectDialog.txtSheetDescription": "Zapobiegaj niechcianym zmianom wprowadzanym przez inn<PERSON>, ograniczając ich możliwość edycji.", "SSE.Views.ProtectDialog.txtSheetTitle": "Chroń arkusz", "SSE.Views.ProtectDialog.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtWarning": "Uwaga: <PERSON><PERSON><PERSON> zapomnisz lub zgubisz hasło, nie będzie możliwości odzyskania go. Zapisz go i nikomu nie udostępniaj.", "SSE.Views.ProtectDialog.txtWBDescription": "Aby unie<PERSON><PERSON><PERSON><PERSON><PERSON> innym użytkownikom wyświetlanie ukrytych arkuszy, do<PERSON><PERSON><PERSON>, prz<PERSON>szenie, usuwanie lub ukrywanie arkuszy oraz zmianę nazwy arkuszy, m<PERSON><PERSON><PERSON><PERSON> z<PERSON><PERSON><PERSON>cz<PERSON>ć hasłem strukturę skoroszytu.", "SSE.Views.ProtectDialog.txtWBTitle": "Chroń strukturę skoroszytu", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "Anonymous", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Anyone", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Edit", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Denied", "SSE.Views.ProtectedRangesEditDlg.textCanView": "View", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "The range title must begin with a letter and may only contain letters, numbers, and spaces.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Remove", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "Select data", "SSE.Views.ProtectedRangesEditDlg.textYou": "you", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Access to range", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "This field is required", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "Protect", "SSE.Views.ProtectedRangesEditDlg.txtRange": "Range", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Only you can edit this range", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Start typing name or email", "SSE.Views.ProtectedRangesManagerDlg.guestText": "Guest", "SSE.Views.ProtectedRangesManagerDlg.lockText": "Locked", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "Delete", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "Edit", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "No protected ranges have been created yet.<br>Create at least one protected range and it will appear in this field.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "Filter", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "All", "SSE.Views.ProtectedRangesManagerDlg.textNew": "New", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Chroń arkusz", "SSE.Views.ProtectedRangesManagerDlg.textRange": "Range", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "You can restrict editing or viewing ranges to selected people.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Access", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Denied", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "Edit", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "Edit range", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "New range", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Protected ranges", "SSE.Views.ProtectedRangesManagerDlg.txtView": "Widok", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Are you sure you want to delete the protected range {0}?<br>Anyone who has edit access to the spreadsheet will be able to edit content in the range.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Are you sure you want to delete the protected ranges?<br>Anyone who has edit access to the spreadsheet will be able to edit content in those ranges.", "SSE.Views.ProtectRangesDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.lockText": "Zablokowany", "SSE.Views.ProtectRangesDlg.textDelete": "Usuń", "SSE.Views.ProtectRangesDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEmpty": "Brak zakresów dozwolonych do edycji.", "SSE.Views.ProtectRangesDlg.textNew": "Nowy", "SSE.Views.ProtectRangesDlg.textProtect": "Chroń arkusz", "SSE.Views.ProtectRangesDlg.textPwd": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRange": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Zakresy odblokowane hasłem, gdy a<PERSON><PERSON> jest chroniony (działa to tylko dla zablokowanych komórek)", "SSE.Views.ProtectRangesDlg.textTitle": "Nazwa", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Ten element jest właśnie edytowany przez innego użytkownika.", "SSE.Views.ProtectRangesDlg.txtEditRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.txtNewRange": "<PERSON><PERSON> zakres", "SSE.Views.ProtectRangesDlg.txtNo": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.txtTitle": "Zezwalaj użytkownikom na edycję zakresów", "SSE.Views.ProtectRangesDlg.txtYes": "Tak", "SSE.Views.ProtectRangesDlg.warnDelete": "<PERSON>zy na pewno chcesz usunąć nazwę {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.RemoveDuplicatesDialog.textDescription": "<PERSON><PERSON> <PERSON> zdu<PERSON><PERSON><PERSON>, w<PERSON><PERSON><PERSON> jedn<PERSON> lub więcej kolumn zawierających duplikaty.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "<PERSON>je dane zawierają nagłówki", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Zaznacz wszystko", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Us<PERSON>ń duplikaty", "SSE.Views.RightMenu.ariaRightMenu": "Right menu", "SSE.Views.RightMenu.txtCellSettings": "Ustawienia komórki", "SSE.Views.RightMenu.txtChartSettings": "Ustawienia wykresu", "SSE.Views.RightMenu.txtImageSettings": "Ustawi<PERSON> obrazu", "SSE.Views.RightMenu.txtParagraphSettings": "Ustawienia akapitu", "SSE.Views.RightMenu.txtPivotSettings": "Ustawienia tabeli przestawnej", "SSE.Views.RightMenu.txtSettings": "Ogólne ustawienia", "SSE.Views.RightMenu.txtShapeSettings": "Ustawienia kształtu", "SSE.Views.RightMenu.txtSignatureSettings": "Ustawienia podpisów", "SSE.Views.RightMenu.txtSlicerSettings": "Ustawienia fragmentatora", "SSE.Views.RightMenu.txtSparklineSettings": "Ustawienia Sparkline", "SSE.Views.RightMenu.txtTableSettings": "<PERSON><PERSON><PERSON><PERSON> tabeli", "SSE.Views.RightMenu.txtTextArtSettings": "Ustawienia tekstu", "SSE.Views.ScaleDialog.textAuto": "Automatyczna", "SSE.Views.ScaleDialog.textError": "Wprowadzon<PERSON> war<PERSON> jest nieprawidłowa.", "SSE.Views.ScaleDialog.textFewPages": "Strony", "SSE.Views.ScaleDialog.textFitTo": "Miejsce na nie więcej niż", "SSE.Views.ScaleDialog.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textManyPages": "Strony", "SSE.Views.ScaleDialog.textOnePage": "Strona", "SSE.Views.ScaleDialog.textScaleTo": "Skaluj do", "SSE.Views.ScaleDialog.textTitle": "Ustawienia skalowania", "SSE.Views.ScaleDialog.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "<PERSON><PERSON><PERSON><PERSON><PERSON> dla tego pola wynosi {0}", "SSE.Views.SetValueDialog.txtMinText": "Mini<PERSON><PERSON> dla tego pola wynosi {0}", "SSE.Views.ShapeSettings.strBackground": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strChange": "Zmień kształt", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "Wypełnij", "SSE.Views.ShapeSettings.strForeground": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strPattern": "Wzór", "SSE.Views.ShapeSettings.strShadow": "Pokaż cień ", "SSE.Views.ShapeSettings.strSize": "Rozmiar", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strType": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "SSE.Views.ShapeSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "Wprowadzona wartość jest nieprawidłowa.<br><PERSON><PERSON><PERSON><PERSON><PERSON> wartość w zakresie od 0 do 1584 pt.", "SSE.Views.ShapeSettings.textColor": "Wypełnij kolorem", "SSE.Views.ShapeSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textEditPoints": "Edit points", "SSE.Views.ShapeSettings.textEditShape": "Edit shape", "SSE.Views.ShapeSettings.textEmptyPattern": "Brak wzorca", "SSE.Views.ShapeSettings.textEyedropper": "Eyedropper", "SSE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromFile": "Z pliku", "SSE.Views.ShapeSettings.textFromStorage": "Z magazynu", "SSE.Views.ShapeSettings.textFromUrl": "Z URL", "SSE.Views.ShapeSettings.textGradient": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textGradientFill": "Wypełnienie gradientem", "SSE.Views.ShapeSettings.textHint270": "<PERSON><PERSON><PERSON><PERSON><PERSON> w lewo o 90° ", "SSE.Views.ShapeSettings.textHint90": "Obróć w prawo o 90°", "SSE.Views.ShapeSettings.textHintFlipH": "Odwróć w poziomie", "SSE.Views.ShapeSettings.textHintFlipV": "Odwróć w pionie", "SSE.Views.ShapeSettings.textImageTexture": "<PERSON><PERSON>z lub teks<PERSON>", "SSE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textMoreColors": "More colors", "SSE.Views.ShapeSettings.textNoFill": "Brak wypełnienia", "SSE.Views.ShapeSettings.textNoShadow": "No Shadow", "SSE.Views.ShapeSettings.textOriginalSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textPatternFill": "Wzór", "SSE.Views.ShapeSettings.textPosition": "<PERSON><PERSON><PERSON>ja", "SSE.Views.ShapeSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textRecentlyUsed": "Ostatnio używane", "SSE.Views.ShapeSettings.textRotate90": "O<PERSON><PERSON>óć o 90°", "SSE.Views.ShapeSettings.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON>rz zd<PERSON>", "SSE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textShadow": "Shadow", "SSE.Views.ShapeSettings.textStretch": "Roz<PERSON>ą<PERSON><PERSON>", "SSE.Views.ShapeSettings.textStyle": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textTexture": "Z tekstury", "SSE.Views.ShapeSettings.textTile": "Płytka", "SSE.Views.ShapeSettings.tipAddGradientPoint": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Usuń punkt gradientu", "SSE.Views.ShapeSettings.txtBrownPaper": "Brązowy papier", "SSE.Views.ShapeSettings.txtCanvas": "Płótno", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGrain": "Ziarno", "SSE.Views.ShapeSettings.txtGranite": "Granit", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON> papier", "SSE.Views.ShapeSettings.txtKnit": "Szydełkowanie", "SSE.Views.ShapeSettings.txtLeather": "Skórzany", "SSE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON> w<PERSON>", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "Drew<PERSON>", "SSE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Wypełnienie tekstem", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Nie przesuwaj lub nie zmieniaj rozmiaru z komórkami", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Tekst alternatywny", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Opis", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Alternatywna, tekstowa prezentacja wizualnych informacji o obiektach, która będzie czytana osobom z wadami wzroku lub zmysłu poznawczego, aby pomóc im lepiej zrozumieć, jakie informacje znajdują się na obrazie, ksz<PERSON>łcie, wykresie lub tabeli.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Strz<PERSON>ł<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Autodopasowanie", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Początkowy rozmiar", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Styl p<PERSON>z<PERSON>kowy", "SSE.Views.ShapeSettingsAdvanced.textBevel": "U<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON><PERSON><PERSON> kolumn", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Rozmiar k<PERSON>ńcowy", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Styl końcowy", "SSE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Odwrócony ", "SSE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Poziomo ", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Dołącz typ", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Stałe proporcje", "SSE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON> w<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textMiter": "prosty", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ale nie zmieniaj rozmiaru komórek", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Zezwalaj na przepełnienie kształtu tekstu", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Dopasuj rozmiar kształtu do tekstu", "SSE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRound": "Zaokrąglij", "SSE.Views.ShapeSettingsAdvanced.textSize": "Rozmiar", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Przyciągaj do komórki", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Przerwa między kolumnami", "SSE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Pole tekstowe", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Kształt - Zaawansowane ustawienia", "SSE.Views.ShapeSettingsAdvanced.textTop": "Góra", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Przenieś i zmień rozmiar komórek", "SSE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON> ", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Wagi i strzałki", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Ostrzeżenie", "SSE.Views.SignatureSettings.strDelete": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strDetails": "Szczegóły sygnatury", "SSE.Views.SignatureSettings.strInvalid": "Nieprawidłowe podpisy", "SSE.Views.SignatureSettings.strRequested": "Żądane podpisy", "SSE.Views.SignatureSettings.strSetup": "Konfiguracja podpisu", "SSE.Views.SignatureSettings.strSign": "Podpisz", "SSE.Views.SignatureSettings.strSignature": "Sygnatura", "SSE.Views.SignatureSettings.strSigner": "Podpisujący", "SSE.Views.SignatureSettings.strValid": "Ważne podpisy", "SSE.Views.SignatureSettings.txtContinueEditing": "<PERSON><PERSON><PERSON><PERSON> mimo w<PERSON>ko", "SSE.Views.SignatureSettings.txtEditWarning": "Edycja spowoduje usunięcie podpisów z arkusza kalkulacyjnego. <br> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>?", "SSE.Views.SignatureSettings.txtRemoveWarning": "<PERSON><PERSON> ch<PERSON> us<PERSON> ten podpis? <br> <PERSON><PERSON> można tego co<PERSON>.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Ten arkusz kalkulacyjny musi być pod<PERSON>.", "SSE.Views.SignatureSettings.txtSigned": "Prawidłowe podpisy zostały dodane do arkusza kalkulacyjnego. Arkusz kalkulacyjny jest chroniony przed edycją.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Niektóre podpisy cyfrowe w arkuszu kalkulacyjnym są nieprawidłowe lub nie można ich zweryfikować. <PERSON><PERSON><PERSON> kalkula<PERSON>jny jest chroniony przed edycją", "SSE.Views.SlicerAddDialog.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerAddDialog.txtTitle": "Wstawianie fragmentatorów", "SSE.Views.SlicerSettings.strHideNoData": "Ukryj <PERSON>y bez danych", "SSE.Views.SlicerSettings.strIndNoData": "Wyróżnij wizualnie puste elementy", "SSE.Views.SlicerSettings.strShowDel": "Pokaż elementy usunięte ze źródła danych", "SSE.Views.SlicerSettings.strShowNoData": "Po<PERSON>ż puste elementy jako ostatnie", "SSE.Views.SlicerSettings.strSorting": "Sortowanie i filtrowanie", "SSE.Views.SlicerSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "SSE.Views.SlicerSettings.textAsc": "Rosnąco", "SSE.Views.SlicerSettings.textAZ": "A do Z", "SSE.Views.SlicerSettings.textButtons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textDesc": "Malejąco", "SSE.Views.SlicerSettings.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textHor": "Poziomy", "SSE.Views.SlicerSettings.textKeepRatio": "Stałe proporcje", "SSE.Views.SlicerSettings.textLargeSmall": "od największego do najmniejszego", "SSE.Views.SlicerSettings.textLock": "Wyłącz zmianę rozmiaru lub przenoszenie", "SSE.Views.SlicerSettings.textNewOld": "od nowych do starych", "SSE.Views.SlicerSettings.textOldNew": "od starych do nowych", "SSE.Views.SlicerSettings.textPosition": "<PERSON><PERSON><PERSON>ja", "SSE.Views.SlicerSettings.textSize": "Rozmiar", "SSE.Views.SlicerSettings.textSmallLarge": "od najmniejszego do największego", "SSE.Views.SlicerSettings.textStyle": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textVert": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "Z do A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Ukryj <PERSON>y bez danych", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Wyróżnij wizualnie puste elementy", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Odwołania", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Pokaż elementy usunięte ze źródła danych", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Pokaż nagłówek", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Po<PERSON>ż puste elementy jako ostatnie", "SSE.Views.SlicerSettingsAdvanced.strSize": "Rozmiar", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Sortowanie i filtrowanie", "SSE.Views.SlicerSettingsAdvanced.strStyle": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Styl i rozmiar", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Nie przesuwaj lub nie zmieniaj rozmiaru z komórkami", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Alternatywny tekst", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Opis", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Alternatywna prezentacja wizualnych informacji o obiektach, które będą czytane osobom z wadami wzroku lub zmysłu poznawczego, aby le<PERSON><PERSON>, jakie informacje znajdują się na obrazie, ksz<PERSON>łtach, wykresie lub tabeli.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Nazwa", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Rosnąco", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A do Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Malejąco", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Nazwa do użycia w formułach", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Nagłówek", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Stałe proporcje", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "od największego do najmniejszego", "SSE.Views.SlicerSettingsAdvanced.textName": "Nazwa", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "od nowych do starych", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "od starych do nowych", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ale nie zmieniaj rozmiaru komórek", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "od najmniejszego do największego", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Przyciągaj do komórki", "SSE.Views.SlicerSettingsAdvanced.textSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Nazwa źródła", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Fragmentator — Ustawienia zaawansowane", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Przenieś i zmień rozmiar komórek", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z do A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "To pole jest wymagane", "SSE.Views.SortDialog.errorEmpty": "Wszystkie kryteria sortowania muszą mieć określoną kolumnę lub wiersz.", "SSE.Views.SortDialog.errorMoreOneCol": "Wybrano więcej niż jedną kolumnę.", "SSE.Views.SortDialog.errorMoreOneRow": "Wybrano więcej niż jeden wiersz.", "SSE.Views.SortDialog.errorNotOriginalCol": "Wybrana kolumna nie należy do pierwotnie wybranego zakresu.", "SSE.Views.SortDialog.errorNotOriginalRow": "Wybrany wiersz nie należy do pierwotnie wybranego zakresu.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 jest sortowana więcej niż raz według tego samego koloru.<br>Us<PERSON>ń zduplikowane kryteria sortowania i spróbuj ponownie.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 jest sortowana więcej niż raz według wartości.<br>Us<PERSON>ń zduplikowane warunki sortowania i spróbuj ponownie.", "SSE.Views.SortDialog.textAsc": "Rosnąco", "SSE.Views.SortDialog.textAuto": "Automatycznie", "SSE.Views.SortDialog.textAZ": "A do Z", "SSE.Views.SortDialog.textBelow": "Poniżej", "SSE.Views.SortDialog.textBtnCopy": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textBtnDelete": "Delete", "SSE.Views.SortDialog.textBtnNew": "New", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON> komórki", "SSE.Views.SortDialog.textColumn": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDesc": "Malejąco", "SSE.Views.SortDialog.textDown": "Przenieś poziom w dół", "SSE.Views.SortDialog.textFontColor": "<PERSON><PERSON>", "SSE.Views.SortDialog.textLeft": "<PERSON>", "SSE.Views.SortDialog.textLevels": "Levels", "SSE.Views.SortDialog.textMoreCols": "(Wię<PERSON>j kolumn...)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON><PERSON><PERSON><PERSON>...)", "SSE.Views.SortDialog.textNone": "Żaden", "SSE.Views.SortDialog.textOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textOrder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textRight": "Do <PERSON>", "SSE.Views.SortDialog.textRow": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textSort": "Sort<PERSON>nie", "SSE.Views.SortDialog.textSortBy": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textThenBy": "Następnie przez", "SSE.Views.SortDialog.textTop": "<PERSON><PERSON>", "SSE.Views.SortDialog.textUp": "Przejdź poziom wyżej", "SSE.Views.SortDialog.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textZA": "Z do A", "SSE.Views.SortDialog.txtInvalidRange": "Nieprawidłowy zakres komórek.", "SSE.Views.SortDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortFilterDialog.textAsc": "Rosnąco (A do Z)", "SSE.Views.SortFilterDialog.textDesc": "Malejąco (od Z do A)", "SSE.Views.SortFilterDialog.textNoSort": "No sort", "SSE.Views.SortFilterDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortFilterDialog.txtTitleValue": "Sort by value", "SSE.Views.SortOptionsDialog.textCase": "Rozróżnienie wielkich i małych liter", "SSE.Views.SortOptionsDialog.textHeaders": "<PERSON>je dane zawierają nagłówki", "SSE.Views.SortOptionsDialog.textLeftRight": "Sortuj od lewej do prawej", "SSE.Views.SortOptionsDialog.textOrientation": "Orientacja", "SSE.Views.SortOptionsDialog.textTitle": "<PERSON><PERSON>je <PERSON>nia", "SSE.Views.SortOptionsDialog.textTopBottom": "Sortuj od góry do dołu", "SSE.Views.SpecialPasteDialog.textAdd": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textBlanks": "Pomiń puste komórki", "SSE.Views.SpecialPasteDialog.textColWidth": "Szerokość kolumn", "SSE.Views.SpecialPasteDialog.textComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textDiv": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFFormat": "Formuły i formatowanie", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formuły i formaty liczb", "SSE.Views.SpecialPasteDialog.textFormats": "Dostę<PERSON><PERSON> formaty", "SSE.Views.SpecialPasteDialog.textFormulas": "Form<PERSON>ł<PERSON>", "SSE.Views.SpecialPasteDialog.textFWidth": "Wzory i szerokość kolumn", "SSE.Views.SpecialPasteDialog.textMult": "Znak mnożenia", "SSE.Views.SpecialPasteDialog.textNone": "(brak)", "SSE.Views.SpecialPasteDialog.textOperation": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textPaste": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textSub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textTitle": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.SpecialPasteDialog.textTranspose": "Transponować", "SSE.Views.SpecialPasteDialog.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textVFormat": "Wartości i formatowanie", "SSE.Views.SpecialPasteDialog.textVNFormat": "Wartości i formaty liczb", "SSE.Views.SpecialPasteDialog.textWBorders": "Wszystko oprócz granic", "SSE.Views.Spellcheck.noSuggestions": "Brak sugestii dotyczących pisowni", "SSE.Views.Spellcheck.textChange": "Zmień", "SSE.Views.Spellcheck.textChangeAll": "Zamień wszystko", "SSE.Views.Spellcheck.textIgnore": "Ignoruj", "SSE.Views.Spellcheck.textIgnoreAll": "Ignoruj wszystko", "SSE.Views.Spellcheck.txtAddToDictionary": "Dodaj do słownika", "SSE.Views.Spellcheck.txtClosePanel": "Close spelling", "SSE.Views.Spellcheck.txtComplete": "Sprawdzanie pisowni zostało zakończone", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Język słownika", "SSE.Views.Spellcheck.txtNextTip": "Przejdź do następnego słowa", "SSE.Views.Spellcheck.txtSpelling": "Pisownia", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Przenieś do końca)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "Create a copy", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(Create new spreadsheet)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Przenieś przed arkusz", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Spreadsheet", "SSE.Views.Statusbar.filteredRecordsText": "prz<PERSON><PERSON>rowano {0} z {1} rekordów", "SSE.Views.Statusbar.filteredText": "<PERSON><PERSON>lt<PERSON>", "SSE.Views.Statusbar.itemAverage": "Średnia", "SSE.Views.Statusbar.itemCount": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemDelete": "Usuń", "SSE.Views.Statusbar.itemHidden": "<PERSON>k<PERSON><PERSON>", "SSE.Views.Statusbar.itemHide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemInsert": "Wstaw", "SSE.Views.Statusbar.itemMaximum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMinimum": "Minimalny", "SSE.Views.Statusbar.itemMoveOrCopy": "Move or copy", "SSE.Views.Statusbar.itemProtect": "Zabezpiecz", "SSE.Views.Statusbar.itemRename": "Zmień nazwę", "SSE.Views.Statusbar.itemStatus": "<PERSON>", "SSE.Views.Statusbar.itemSum": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON> karty", "SSE.Views.Statusbar.itemUnProtect": "<PERSON><PERSON><PERSON> ochronę", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Istnieje już arkusz roboczy o takiej samej nazwie.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "<PERSON><PERSON><PERSON> nie może zawierać znaków: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Nazwa <PERSON>", "SSE.Views.Statusbar.selectAllSheets": "Zaznacz wszystkie arkusze", "SSE.Views.Statusbar.sheetIndexText": "<PERSON><PERSON><PERSON> {0} z {1}", "SSE.Views.Statusbar.textAverage": "ŚREDNIA", "SSE.Views.Statusbar.textCount": "Licz", "SSE.Views.Statusbar.textMax": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textMin": "Minimum", "SSE.Views.Statusbar.textNewColor": "Nowy niestandardowy kolor", "SSE.Views.Statusbar.textNoColor": "Bez koloru", "SSE.Views.Statusbar.textSum": "<PERSON><PERSON>", "SSE.Views.Statusbar.tipAddTab": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipFirst": "Przewiń do pierwszego arkusza", "SSE.Views.Statusbar.tipLast": "Przewiń do ostatniego arkusza", "SSE.Views.Statusbar.tipListOfSheets": "<PERSON><PERSON>", "SSE.Views.Statusbar.tipNext": "Przesuń listę arkuszy w prawo", "SSE.Views.Statusbar.tipPrev": "Przesuń listę arkuszy w lewo", "SSE.Views.Statusbar.tipZoomFactor": "Powiększenie", "SSE.Views.Statusbar.tipZoomIn": "Powię<PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomOut": "Po<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.ungroupSheets": "Rozgrupuj a<PERSON>", "SSE.Views.Statusbar.zoomText": "Powięks<PERSON><PERSON> {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Operacja nie mogła zostać wykonana dla wybranego zakresu komórek.<br><PERSON><PERSON><PERSON><PERSON> jednolity zakres danych inny niż istniejący i spróbuj ponownie.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Operacja nie może zostać zakończona dla wybranego zakresu komórek.<br> <PERSON><PERSON><PERSON><PERSON>, tak aby pierwszy wiersz tabeli znajdował się w tym samym wierszu, a tabela wynikowa pokrywała się z bieżącym.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Nie można zakończyć operacji dla wybranego zakresu komórek.<br><PERSON><PERSON><PERSON><PERSON>, kt<PERSON><PERSON> nie zawiera innych tabel.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Wielokomórkowe formuły tablicowe nie są dozwolone w tabelach.", "SSE.Views.TableOptionsDialog.txtEmpty": "To pole jest wymagane", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "SSE.Views.TableOptionsDialog.txtInvalidRange": "BŁĄD! Niepoprawny zakres komórek", "SSE.Views.TableOptionsDialog.txtNote": "Nagłówki muszą pozostać w tym samym w<PERSON>, a wynikowy zakres tabeli musi częściowo pokrywać się z oryginalnym zakresem.", "SSE.Views.TableOptionsDialog.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON>ń kolumnę", "SSE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON> wiersz", "SSE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON> ta<PERSON>", "SSE.Views.TableSettings.insertColumnLeftText": "Wstaw kolumnę z lewej", "SSE.Views.TableSettings.insertColumnRightText": "Wstaw kolumnę z prawej", "SSE.Views.TableSettings.insertRowAboveText": "Wstaw wiersz powyżej", "SSE.Views.TableSettings.insertRowBelowText": "Wstaw wiersz poniżej", "SSE.Views.TableSettings.notcriticalErrorTitle": "Ostrzeżenie", "SSE.Views.TableSettings.selectColumnText": "Zaznacz całe kolumny", "SSE.Views.TableSettings.selectDataText": "Zaznacz dane kolumny", "SSE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>z", "SSE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textActions": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>i", "SSE.Views.TableSettings.textAdvanced": "Po<PERSON>ż ustawienia zaawansowane", "SSE.Views.TableSettings.textBanded": "Na przemian", "SSE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textConvertRange": "Konwertuj do zakresu", "SSE.Views.TableSettings.textEdit": "Wiersze i Kolumny", "SSE.Views.TableSettings.textEmptyTemplate": "Brak szablonów", "SSE.Views.TableSettings.textExistName": "BŁĄD! Zakres o takiej samej nazwie już istnieje.", "SSE.Views.TableSettings.textFilter": "Przycisk filtru", "SSE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textHeader": "Nagłówek", "SSE.Views.TableSettings.textInvalidName": "BŁĄD! Błędna nazwa tabeli", "SSE.Views.TableSettings.textIsLocked": "Ten element jest właśnie edytowany przez innego użytkownika.", "SSE.Views.TableSettings.textLast": "Ostatni", "SSE.Views.TableSettings.textLongOperation": "Długa praca", "SSE.Views.TableSettings.textPivot": "Wstaw tabelę przestawną", "SSE.Views.TableSettings.textRemDuplicates": "Us<PERSON>ń duplikaty", "SSE.Views.TableSettings.textReservedName": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON><PERSON> pr<PERSON><PERSON><PERSON><PERSON>, jest już wzmiankowana w formułach komórkowych. Proszę użyć innej nazwy.", "SSE.Views.TableSettings.textResize": "Zmień rozmiar tabeli", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "<PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.TableSettings.textSlicer": "Wstaw fragmentator", "SSE.Views.TableSettings.textTableName": "<PERSON><PERSON><PERSON> tabeli", "SSE.Views.TableSettings.textTemplate": "Wybierz z szablonu", "SSE.Views.TableSettings.textTotal": "Wszystkie", "SSE.Views.TableSettings.txtGroupTable_Custom": "Custom", "SSE.Views.TableSettings.txtGroupTable_Dark": "Dark", "SSE.Views.TableSettings.txtGroupTable_Light": "Light", "SSE.Views.TableSettings.txtGroupTable_Medium": "Medium", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Table style dark", "SSE.Views.TableSettings.txtTable_TableStyleLight": "Table style light", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "Table style medium", "SSE.Views.TableSettings.warnLongOperation": "<PERSON><PERSON><PERSON>, kt<PERSON>rą zamierzasz wykonać może zająć trochę czasu.<br>Na pewno chcesz kontynuować?", "SSE.Views.TableSettingsAdvanced.textAlt": "Tekst alternatywny", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Opis", "SSE.Views.TableSettingsAdvanced.textAltTip": "Alternatywna prezentacja wizualnych informacji o obiektach, które będą czytane osobom z wadami wzroku lub zmysłu poznawczego, aby le<PERSON><PERSON>, jakie informacje znajdują się na obrazie, ksz<PERSON>łtach, wykresie lub tabeli.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textTitle": "Tabel<PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> us<PERSON>", "SSE.Views.TextArtSettings.strBackground": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "Wypełnij", "SSE.Views.TextArtSettings.strForeground": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strPattern": "Wzór", "SSE.Views.TextArtSettings.strSize": "Rozmiar", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strType": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "Wprowadzona wartość jest nieprawidłowa.<br><PERSON><PERSON><PERSON><PERSON><PERSON> wartość w zakresie od 0 do 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Wypełnij kolorem", "SSE.Views.TextArtSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textEmptyPattern": "Brak wzorca", "SSE.Views.TextArtSettings.textFromFile": "Z pliku", "SSE.Views.TextArtSettings.textFromUrl": "Z URL", "SSE.Views.TextArtSettings.textGradient": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textGradientFill": "Wypełnienie gradientem", "SSE.Views.TextArtSettings.textImageTexture": "<PERSON><PERSON>z lub teks<PERSON>", "SSE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textNoFill": "Brak wypełnienia", "SSE.Views.TextArtSettings.textPatternFill": "Wzór", "SSE.Views.TextArtSettings.textPosition": "<PERSON><PERSON><PERSON>ja", "SSE.Views.TextArtSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStretch": "Roz<PERSON>ą<PERSON><PERSON>", "SSE.Views.TextArtSettings.textStyle": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textTemplate": "Szablon", "SSE.Views.TextArtSettings.textTexture": "Z tekstury", "SSE.Views.TextArtSettings.textTile": "Płytka", "SSE.Views.TextArtSettings.textTransform": "Przekształcenie", "SSE.Views.TextArtSettings.tipAddGradientPoint": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Usuń punkt gradientu", "SSE.Views.TextArtSettings.txtBrownPaper": "Brązowy papier", "SSE.Views.TextArtSettings.txtCanvas": "Płótno", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGrain": "Ziarno", "SSE.Views.TextArtSettings.txtGranite": "Granit", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON> papier", "SSE.Views.TextArtSettings.txtKnit": "Szydełkowanie", "SSE.Views.TextArtSettings.txtLeather": "Skórzany", "SSE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON> w<PERSON>", "SSE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtWood": "Drew<PERSON>", "SSE.Views.Toolbar.capBtnAddComment": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnColorSchemas": "Schemat kolorów", "SSE.Views.Toolbar.capBtnComment": "Komentarz", "SSE.Views.Toolbar.capBtnInsHeader": "Nagłówek stopka", "SSE.Views.Toolbar.capBtnInsSlicer": "Fragmentator", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Symbole", "SSE.Views.Toolbar.capBtnMargins": "Mar<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageBreak": "Breaks", "SSE.Views.Toolbar.capBtnPageOrient": "Orientacja", "SSE.Views.Toolbar.capBtnPageSize": "Rozmiar", "SSE.Views.Toolbar.capBtnPrintArea": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintTitles": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>y", "SSE.Views.Toolbar.capBtnScale": "Skaluj do rozmiaru", "SSE.Views.Toolbar.capImgAlign": "Wyrównaj", "SSE.Views.Toolbar.capImgBackward": "Przenieś do tyłu", "SSE.Views.Toolbar.capImgForward": "Przenieś do przodu", "SSE.Views.Toolbar.capImgGroup": "Grupuj", "SSE.Views.Toolbar.capInsertChart": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertChartRecommend": "Recommended Chart", "SSE.Views.Toolbar.capInsertEquation": "Równanie", "SSE.Views.Toolbar.capInsertHyperlink": "Hiperlink", "SSE.Views.Toolbar.capInsertImage": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertShape": "Kształt", "SSE.Views.Toolbar.capInsertSpark": "Sparkline", "SSE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertText": "Pole tekstowe", "SSE.Views.Toolbar.capInsertTextart": "Text Art", "SSE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniCapitalizeWords": "Capitalize Each Word", "SSE.Views.Toolbar.mniImageFromFile": "Obraz z pliku", "SSE.Views.Toolbar.mniImageFromStorage": "Obraz z magazynu", "SSE.Views.Toolbar.mniImageFromUrl": "Obraz z URL", "SSE.Views.Toolbar.mniLowerCase": "lowercase", "SSE.Views.Toolbar.mniSentenceCase": "Sentence case.", "SSE.Views.Toolbar.mniToggleCase": "tOGGLE cASE", "SSE.Views.Toolbar.mniUpperCase": "UPPERCASE", "SSE.Views.Toolbar.textAddPrintArea": "Dodaj do obszaru drukowania", "SSE.Views.Toolbar.textAlignBottom": "Wyrównaj do dołu", "SSE.Views.Toolbar.textAlignCenter": "Wyrównaj do środka", "SSE.Views.Toolbar.textAlignJust": "Wyjustowany", "SSE.Views.Toolbar.textAlignLeft": "Wyrównaj do lewej", "SSE.Views.Toolbar.textAlignMiddle": "Wyrównaj do środka", "SSE.Views.Toolbar.textAlignRight": "Wyrównaj do prawej", "SSE.Views.Toolbar.textAlignTop": "Wyrównaj do góry", "SSE.Views.Toolbar.textAllBorders": "Wszystkie krawędzie", "SSE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "SSE.Views.Toolbar.textAuto": "Automatyczny", "SSE.Views.Toolbar.textAutoColor": "Automatyczny", "SSE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "SSE.Views.Toolbar.textBlackHeart": "Black Heart Suit", "SSE.Views.Toolbar.textBold": "Pogrubienie", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON>", "SSE.Views.Toolbar.textBordersStyle": "Styl obramowania", "SSE.Views.Toolbar.textBottom": "Dolne: ", "SSE.Views.Toolbar.textBottomBorders": "Dolne krawędzie", "SSE.Views.Toolbar.textBullet": "Bullet", "SSE.Views.Toolbar.textCellAlign": "Format cell alignment", "SSE.Views.Toolbar.textCenterBorders": "Wewnątrz pionowych granic", "SSE.Views.Toolbar.textClearPrintArea": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> obszar druk<PERSON>nia", "SSE.Views.Toolbar.textClearRule": "<PERSON><PERSON><PERSON> reguły", "SSE.Views.Toolbar.textClockwise": "Kąt w prawo", "SSE.Views.Toolbar.textColorScales": "Skala kolorów", "SSE.Views.Toolbar.textCopyright": "Copyright Sign", "SSE.Views.Toolbar.textCounterCw": "Kąt w lewo", "SSE.Views.Toolbar.textCustom": "Custom", "SSE.Views.Toolbar.textDataBars": "Histogramy", "SSE.Views.Toolbar.textDegree": "Degree Sign", "SSE.Views.Toolbar.textDelLeft": "Przesuń komórki w lewo", "SSE.Views.Toolbar.textDelPageBreak": "Remove page break", "SSE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "SSE.Views.Toolbar.textDelUp": "Przesuń komórki w górę", "SSE.Views.Toolbar.textDiagDownBorder": "Ukośna dolna krawędź", "SSE.Views.Toolbar.textDiagUpBorder": "Ukośna górna krawędź", "SSE.Views.Toolbar.textDivision": "Division Sign", "SSE.Views.Toolbar.textDollar": "Dollar Sign", "SSE.Views.Toolbar.textDone": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textDown": "Down", "SSE.Views.Toolbar.textEditVA": "Edit Visible Area", "SSE.Views.Toolbar.textEntireCol": "Wstaw kolumnę", "SSE.Views.Toolbar.textEntireRow": "Cały wiersz", "SSE.Views.Toolbar.textEuro": "Euro Sign", "SSE.Views.Toolbar.textFewPages": "stron", "SSE.Views.Toolbar.textFillLeft": "Left", "SSE.Views.Toolbar.textFillRight": "Right", "SSE.Views.Toolbar.textFormatCellFill": "Format cell fill", "SSE.Views.Toolbar.textGreaterEqual": "Greater Than Or Equal To", "SSE.Views.Toolbar.textHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textHideVA": "Hide Visible Area", "SSE.Views.Toolbar.textHorizontal": "Poziomy tekst", "SSE.Views.Toolbar.textInfinity": "Infinity", "SSE.Views.Toolbar.textInsDown": "Przesuń komórki w dół", "SSE.Views.Toolbar.textInsideBorders": "Wewną<PERSON><PERSON> granic", "SSE.Views.Toolbar.textInsPageBreak": "Insert page break", "SSE.Views.Toolbar.textInsRight": "Przesuń komórki w prawo", "SSE.Views.Toolbar.textItalic": "Ku<PERSON>ywa", "SSE.Views.Toolbar.textItems": "Elementy", "SSE.Views.Toolbar.textLandscape": "Pozioma", "SSE.Views.Toolbar.textLeft": "Lewo: ", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textLessEqual": "Less Than Or Equal To", "SSE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "SSE.Views.Toolbar.textManageRule": "Zarządza<PERSON>guła<PERSON>", "SSE.Views.Toolbar.textManyPages": "stron", "SSE.Views.Toolbar.textMarginsLast": "Ostatni niestandardowy", "SSE.Views.Toolbar.textMarginsNarrow": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsNormal": "Normalny", "SSE.Views.Toolbar.textMarginsWide": "Szeroki", "SSE.Views.Toolbar.textMiddleBorders": "Wewnątrz poziomych granic", "SSE.Views.Toolbar.textMoreBorders": "More borders", "SSE.Views.Toolbar.textMoreFormats": "Więcej formatów", "SSE.Views.Toolbar.textMorePages": "<PERSON><PERSON> strony", "SSE.Views.Toolbar.textMoreSymbols": "More symbols", "SSE.Views.Toolbar.textNewColor": "Nowy niestandardowy kolor", "SSE.Views.Toolbar.textNewRule": "Nowa reguła", "SSE.Views.Toolbar.textNoBorders": "Bez k<PERSON>ędzi", "SSE.Views.Toolbar.textNotEqualTo": "Not Equal To", "SSE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "SSE.Views.Toolbar.textOnePage": "Strona", "SSE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "SSE.Views.Toolbar.textOutBorders": "Krawędzie zewnętrzne", "SSE.Views.Toolbar.textPageMarginsCustom": "Niestandardowe marginesy", "SSE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "SSE.Views.Toolbar.textPortrait": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPrintGridlines": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPrintHeadings": "Print Headings", "SSE.Views.Toolbar.textPrintOptions": "Ustawienia drukowania", "SSE.Views.Toolbar.textRegistered": "Registered Sign", "SSE.Views.Toolbar.textResetPageBreak": "Reset all page breaks", "SSE.Views.Toolbar.textRight": "Prawo: ", "SSE.Views.Toolbar.textRightBorders": "Prawe krawędzie", "SSE.Views.Toolbar.textRotateDown": "Obróć tekst w dół", "SSE.Views.Toolbar.textRotateUp": "Obróć tekst w górę", "SSE.Views.Toolbar.textRtlSheet": "Sheet Right-to-left", "SSE.Views.Toolbar.textScale": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textScaleCustom": "Specjalny", "SSE.Views.Toolbar.textSection": "Section Sign", "SSE.Views.Toolbar.textSelection": "Z obecnie zaznaczonego fragmentu", "SSE.Views.Toolbar.textSeries": "Series", "SSE.Views.Toolbar.textSetPrintArea": "Usta<PERSON>", "SSE.Views.Toolbar.textShapesCombine": "Combine", "SSE.Views.Toolbar.textShapesFragment": "Fragment", "SSE.Views.Toolbar.textShapesIntersect": "Intersect", "SSE.Views.Toolbar.textShapesSubstract": "Subtract", "SSE.Views.Toolbar.textShapesUnion": "Union", "SSE.Views.Toolbar.textShowVA": "Show Visible Area", "SSE.Views.Toolbar.textSmile": "White Smiling Face", "SSE.Views.Toolbar.textSquareRoot": "Square Root", "SSE.Views.Toolbar.textStrikeout": "Przekreślenie", "SSE.Views.Toolbar.textSubscript": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSubSuperscript": "Indeksy dolne / indeksy górne", "SSE.Views.Toolbar.textSuperscript": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabCollaboration": "Współpraca", "SSE.Views.Toolbar.textTabData": "<PERSON>", "SSE.Views.Toolbar.textTabDraw": "R<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFile": "Plik", "SSE.Views.Toolbar.textTabFormula": "Formuła", "SSE.Views.Toolbar.textTabHome": "Narzędzia główne", "SSE.Views.Toolbar.textTabInsert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabLayout": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabProtect": "Ochrona", "SSE.Views.Toolbar.textTabView": "Widok", "SSE.Views.Toolbar.textThisPivot": "Z tej tabeli przestawnej", "SSE.Views.Toolbar.textThisSheet": "Z tego arkusza roboczego", "SSE.Views.Toolbar.textThisTable": "Z tej tabeli", "SSE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTop": "Góra: ", "SSE.Views.Toolbar.textTopBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "SSE.Views.Toolbar.textUnderline": "Podkreślenie", "SSE.Views.Toolbar.textUp": "Up", "SSE.Views.Toolbar.textVertical": "Pionowy tekst", "SSE.Views.Toolbar.textWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textYen": "Yen Sign", "SSE.Views.Toolbar.textZoom": "Powiększenie", "SSE.Views.Toolbar.tipAlignBottom": "Wyrównaj do dołu", "SSE.Views.Toolbar.tipAlignCenter": "Wyrównaj do środka", "SSE.Views.Toolbar.tipAlignJust": "Wyjustowany", "SSE.Views.Toolbar.tipAlignLeft": "Wyrównaj do lewej", "SSE.Views.Toolbar.tipAlignMiddle": "Wyrównaj do środka", "SSE.Views.Toolbar.tipAlignRight": "Wyrównaj do prawej", "SSE.Views.Toolbar.tipAlignTop": "Wyrównaj do góry", "SSE.Views.Toolbar.tipAutofilter": "Sortuj i filtruj", "SSE.Views.Toolbar.tipBack": "Powró<PERSON>", "SSE.Views.Toolbar.tipBorders": "Obramowania", "SSE.Views.Toolbar.tipCellStyle": "Styl komórki", "SSE.Views.Toolbar.tipChangeCase": "Change case", "SSE.Views.Toolbar.tipChangeChart": "Zmień typ wykresu", "SSE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipColorSchemas": "Zmień schemat kolorów", "SSE.Views.Toolbar.tipCondFormat": "Formatowanie warunkowe", "SSE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON><PERSON> styl", "SSE.Views.Toolbar.tipCut": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDecDecimal": "Zmniej<PERSON>o<PERSON> cyfr po przecinku", "SSE.Views.Toolbar.tipDecFont": "Zmniejsz rozmiar czcionki", "SSE.Views.Toolbar.tipDeleteOpt": "Usuń komórki", "SSE.Views.Toolbar.tipDigStyleAccounting": "Styl rachunkowy", "SSE.Views.Toolbar.tipDigStyleComma": "Comma style", "SSE.Views.Toolbar.tipDigStyleCurrency": "Styl waluty", "SSE.Views.Toolbar.tipDigStylePercent": "Styl procentowy", "SSE.Views.Toolbar.tipEditChart": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipEditChartData": "<PERSON><PERSON><PERSON><PERSON> dane", "SSE.Views.Toolbar.tipEditChartType": "Zmień typ wykresu", "SSE.Views.Toolbar.tipEditHeader": "Edytuj nagłówek lub stopkę", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipFontName": "Czcionka", "SSE.Views.Toolbar.tipFontSize": "Rozmiar <PERSON>ki", "SSE.Views.Toolbar.tipHAlighOle": "Horizontal align", "SSE.Views.Toolbar.tipImgAlign": "Wyrównaj obiekty", "SSE.Views.Toolbar.tipImgGroup": "Grupuj obiekty", "SSE.Views.Toolbar.tipIncDecimal": "Zwię<PERSON><PERSON> cyfr po przecinku", "SSE.Views.Toolbar.tipIncFont": "Zwiększ rozmiar czcionki", "SSE.Views.Toolbar.tipInsertChart": "Wstaw wykres", "SSE.Views.Toolbar.tipInsertChartRecommend": "Insert recommended chart", "SSE.Views.Toolbar.tipInsertChartSpark": "Wstaw wykres", "SSE.Views.Toolbar.tipInsertEquation": "Wstaw równanie", "SSE.Views.Toolbar.tipInsertHorizontalText": "Insert horizontal text box", "SSE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertImage": "Wstaw obraz", "SSE.Views.Toolbar.tipInsertOpt": "Wstaw komórki", "SSE.Views.Toolbar.tipInsertShape": "Wstaw kształt", "SSE.Views.Toolbar.tipInsertSlicer": "Wstaw fragmentator", "SSE.Views.Toolbar.tipInsertSmartArt": "Insert SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Wstaw krzywą", "SSE.Views.Toolbar.tipInsertSymbol": "Wstaw symbol", "SSE.Views.Toolbar.tipInsertTable": "Wstaw tabelę", "SSE.Views.Toolbar.tipInsertText": "Wstaw pole tekstowe", "SSE.Views.Toolbar.tipInsertTextart": "Wstaw tekst", "SSE.Views.Toolbar.tipInsertVerticalText": "Insert vertical text box", "SSE.Views.Toolbar.tipMerge": "Scal i wyśrodkuj", "SSE.Views.Toolbar.tipNone": "Brak", "SSE.Views.Toolbar.tipNumFormat": "Format numeru", "SSE.Views.Toolbar.tipPageBreak": "Add a break where you want the next page to begin in the printed copy", "SSE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON><PERSON>y strony", "SSE.Views.Toolbar.tipPageOrient": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.Toolbar.tipPageSize": "<PERSON><PERSON><PERSON><PERSON> strony", "SSE.Views.Toolbar.tipPaste": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrColor": "<PERSON><PERSON> wypełnienia", "SSE.Views.Toolbar.tipPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrintArea": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrintQuick": "Szybkie drukowanie", "SSE.Views.Toolbar.tipPrintTitles": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>y", "SSE.Views.Toolbar.tipRedo": "Ponów", "SSE.Views.Toolbar.tipReplace": "Replace", "SSE.Views.Toolbar.tipRtlSheet": "Switch the sheet direction so that the first column is on the right side", "SSE.Views.Toolbar.tipSave": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSaveCoauth": "Zapisz swoje zmiany, aby inni użytkownicy mogli je zobaczyć.", "SSE.Views.Toolbar.tipScale": "Skaluj do rozmiaru", "SSE.Views.Toolbar.tipSelectAll": "Zaznacz wszystko", "SSE.Views.Toolbar.tipSendBackward": "Przenieś do tyłu", "SSE.Views.Toolbar.tipSendForward": "Przenieś do przodu", "SSE.Views.Toolbar.tipShapesMerge": "Merge shapes", "SSE.Views.Toolbar.tipSynchronize": "Dokument został zmieniony przez innego użytkownika. Kliknij, aby zapisać swoje zmiany i ponownie załadować zmiany.", "SSE.Views.Toolbar.tipTextFormatting": "More text formatting tools", "SSE.Views.Toolbar.tipTextOrientation": "Orientacja", "SSE.Views.Toolbar.tipUndo": "Cof<PERSON>j", "SSE.Views.Toolbar.tipVAlighOle": "Vertical align", "SSE.Views.Toolbar.tipVisibleArea": "Visible area", "SSE.Views.Toolbar.tipWrap": "Zawijaj tekst", "SSE.Views.Toolbar.txtAccounting": "Księgowy", "SSE.Views.Toolbar.txtAdditional": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtAscending": "Rosnąco", "SSE.Views.Toolbar.txtAutosumTip": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtCellStyle": "Cell Style", "SSE.Views.Toolbar.txtClearAll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFilter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> filtr", "SSE.Views.Toolbar.txtClearFormat": "Formatowanie", "SSE.Views.Toolbar.txtClearFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearHyper": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearText": "Tekst", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCustom": "Niestandardowy", "SSE.Views.Toolbar.txtDate": "Data", "SSE.Views.Toolbar.txtDateLong": "Long Date", "SSE.Views.Toolbar.txtDateShort": "Short Date", "SSE.Views.Toolbar.txtDateTime": "Data i czas", "SSE.Views.Toolbar.txtDescending": "Malejąco", "SSE.Views.Toolbar.txtDollar": "$ Dolar", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Wykładniczy", "SSE.Views.Toolbar.txtFillNum": "Fill", "SSE.Views.Toolbar.txtFilter": "Filtr", "SSE.Views.Toolbar.txtFormula": "Wstaw funkcję", "SSE.Views.Toolbar.txtFraction": "Ułamek", "SSE.Views.Toolbar.txtFranc": "CHF frank s<PERSON>", "SSE.Views.Toolbar.txtGeneral": "Ogólne", "SSE.Views.Toolbar.txtInteger": "Liczba całkowita", "SSE.Views.Toolbar.txtManageRange": "Menadżer nazw", "SSE.Views.Toolbar.txtMergeAcross": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtMergeCells": "Scal komórki", "SSE.Views.Toolbar.txtMergeCenter": "Scal i wyśrodkuj", "SSE.Views.Toolbar.txtNamedRange": "<PERSON><PERSON><PERSON><PERSON> zakresy", "SSE.Views.Toolbar.txtNewRange": "Zdefiniuj <PERSON>", "SSE.Views.Toolbar.txtNoBorders": "Bez k<PERSON>ędzi", "SSE.Views.Toolbar.txtNumber": "Numer", "SSE.Views.Toolbar.txtPasteRange": "<PERSON><PERSON><PERSON> Imię", "SSE.Views.Toolbar.txtPercentage": "Procentowo", "SSE.Views.Toolbar.txtPound": "£ Funt", "SSE.Views.Toolbar.txtRouble": "₽ Rubel", "SSE.Views.Toolbar.txtScientific": "Naukowy", "SSE.Views.Toolbar.txtSearch": "Szukaj", "SSE.Views.Toolbar.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSortAZ": "Sortuj rosnąco", "SSE.Views.Toolbar.txtSortZA": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSpecial": "Specialny", "SSE.Views.Toolbar.txtTableTemplate": "Formatuj jako s<PERSON> tabeli", "SSE.Views.Toolbar.txtText": "Tekst", "SSE.Views.Toolbar.txtTime": "Czas", "SSE.Views.Toolbar.txtUnmerge": "Niescalone komórki", "SSE.Views.Toolbar.txtYen": "¥ Jena", "SSE.Views.Top10FilterDialog.textType": "Po<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBy": "od", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON><PERSON><PERSON>ja", "SSE.Views.Top10FilterDialog.txtPercent": "Procent", "SSE.Views.Top10FilterDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 automatyczne filtrowanie", "SSE.Views.Top10FilterDialog.txtTop": "Góra", "SSE.Views.Top10FilterDialog.txtValueTitle": "Filtry \"Pierwsze 10\"", "SSE.Views.ValueFieldSettingsDialog.textNext": "(next)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(previous)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Parametry pola war<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Średnia", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Pole bazowe", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Podstawowy element", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 z %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Odliczać cyfry", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Niestandardowa nazwa", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Różnica od", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMax": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Minimum", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "Brak obliczeń", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "Procent", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Różnica procentowa od", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "<PERSON><PERSON> kol<PERSON>ny", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% of grand total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% of parent total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% of parent column total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% of parent row total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% running total in", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Produkt", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Rank smallest to largest", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Rank largest to smallest", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Bieżąca suma w", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Dodatkowe obliczenia", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Nazwa źródła:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Podsum<PERSON>j pole wartości według", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "Zamknij", "SSE.Views.ViewManagerDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.lockText": "Zablokowany", "SSE.Views.ViewManagerDlg.textDelete": "Usuń", "SSE.Views.ViewManagerDlg.textDuplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textEmpty": "Widoki nie zostały jeszcze utworzone.", "SSE.Views.ViewManagerDlg.textGoTo": "Przejdź do widoku", "SSE.Views.ViewManagerDlg.textLongName": "Wpisz nazwę krótszą niż 128 znaków.", "SSE.Views.ViewManagerDlg.textNew": "Nowy", "SSE.Views.ViewManagerDlg.textRename": "Zmień nazwę", "SSE.Views.ViewManagerDlg.textRenameError": "Nazwa prezentacji nie może być pusta.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Zmień nazwę widoku", "SSE.Views.ViewManagerDlg.textViews": "Prezentacja arkusza", "SSE.Views.ViewManagerDlg.tipIsLocked": "Ten element jest właśnie edytowany przez innego użytkownika.", "SSE.Views.ViewManagerDlg.txtTitle": "Menedżer widoków arkuszy", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Are you sure you want to delete this sheet view?", "SSE.Views.ViewManagerDlg.warnDeleteView": "Próbujesz usunąć aktualnie włączony widok '%1'.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ten widok i go usunąć?", "SSE.Views.ViewTab.capBtnFreeze": "Zablokuj panele", "SSE.Views.ViewTab.capBtnSheetView": "Prezentacja arkusza", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Zawsze pokazuj pasek narzędzi", "SSE.Views.ViewTab.textClose": "Zamknij", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Połącz arkusze i pasek stanu", "SSE.Views.ViewTab.textCreate": "Nowy", "SSE.Views.ViewTab.textDefault": "Domyślny", "SSE.Views.ViewTab.textFill": "Fill", "SSE.Views.ViewTab.textFormula": "Pasek <PERSON>", "SSE.Views.ViewTab.textFreezeCol": "Zablokuj pierwszą kolumnę", "SSE.Views.ViewTab.textFreezeRow": "Zablokuj górną linię", "SSE.Views.ViewTab.textGridlines": "<PERSON><PERSON>", "SSE.Views.ViewTab.textHeadings": "Nagłówki", "SSE.Views.ViewTab.textInterfaceTheme": "Motyw interfejsu", "SSE.Views.ViewTab.textLeftMenu": "Left Panel", "SSE.Views.ViewTab.textLine": "Line", "SSE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textRightMenu": "Right Panel", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Pokaż cień dla zadokowanych obszarów", "SSE.Views.ViewTab.textTabStyle": "Tab style", "SSE.Views.ViewTab.textUnFreeze": "Odblokuj panele", "SSE.Views.ViewTab.textZeros": "Wyświetl zera", "SSE.Views.ViewTab.textZoom": "Powiększenie", "SSE.Views.ViewTab.tipClose": "Zamknij widok arkusza", "SSE.Views.ViewTab.tipCreate": "Utwórz widok arkusza", "SSE.Views.ViewTab.tipFreeze": "Zablokuj panele", "SSE.Views.ViewTab.tipInterfaceTheme": "Interface theme", "SSE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipSheetView": "Prezentacja arkusza", "SSE.Views.ViewTab.tipViewNormal": "See your document in Normal view", "SSE.Views.ViewTab.tipViewPageBreak": "See where the page breaks will appear when your document is printed", "SSE.Views.ViewTab.txtViewNormal": "Normal", "SSE.Views.ViewTab.txtViewPageBreak": "Page Break Preview", "SSE.Views.WatchDialog.closeButtonText": "Close", "SSE.Views.WatchDialog.textAdd": "Add watch", "SSE.Views.WatchDialog.textBook": "Book", "SSE.Views.WatchDialog.textCell": "Cell", "SSE.Views.WatchDialog.textDelete": "Delete watch", "SSE.Views.WatchDialog.textDeleteAll": "Delete all", "SSE.Views.WatchDialog.textFormula": "Formula", "SSE.Views.WatchDialog.textName": "Name", "SSE.Views.WatchDialog.textSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textValue": "Value", "SSE.Views.WatchDialog.txtTitle": "Watch window", "SSE.Views.WBProtection.hintAllowRanges": "Zezwól na edycję zakresów", "SSE.Views.WBProtection.hintProtectRange": "Protect range", "SSE.Views.WBProtection.hintProtectSheet": "Chroń arkusz", "SSE.Views.WBProtection.hintProtectWB": "Chroń skoroszyt", "SSE.Views.WBProtection.txtAllowRanges": "Zezwól na edycję zakresów", "SSE.Views.WBProtection.txtHiddenFormula": "<PERSON>k<PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedCell": "Zablokowana komórka", "SSE.Views.WBProtection.txtLockedShape": "Zablokowany kształt", "SSE.Views.WBProtection.txtLockedText": "Zablokuj tekst", "SSE.Views.WBProtection.txtProtectRange": "Protect Range", "SSE.Views.WBProtection.txtProtectSheet": "Chroń arkusz", "SSE.Views.WBProtection.txtProtectWB": "Chroń skoroszyt", "SSE.Views.WBProtection.txtSheetUnlockDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasło, aby odblokować arkusz", "SSE.Views.WBProtection.txtSheetUnlockTitle": "<PERSON><PERSON>ń ochronę arkusza", "SSE.Views.WBProtection.txtWBUnlockDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> hasło, aby wyłączyć ochronę skoroszytu", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}