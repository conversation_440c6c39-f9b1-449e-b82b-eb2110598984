{"cancelButtonText": "Peruuta", "Common.Controllers.Chat.notcriticalErrorTitle": "Varo<PERSON><PERSON>", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "<PERSON><PERSON> mall<PERSON>", "Common.Controllers.History.notcriticalErrorTitle": "Varo<PERSON><PERSON>", "Common.Controllers.History.txtErrorLoadHistory": "Historian <PERSON><PERSON><PERSON>", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Lisäosat on asennettu onnistuneesti. Kaikki lisäosat ovat nähtävissä täällä.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> on asennettu onnistuneesti. Voit nähdä kaikki asennetut lisäosat täällä.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Su<PERSON>ta asenne<PERSON>t l<PERSON>t", "Common.Controllers.Plugins.textRunPlugin": "<PERSON><PERSON><PERSON> lis<PERSON>", "Common.define.chartData.textArea": "Area", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textAreaStackedPer": "100% <PERSON><PERSON><PERSON> alue", "Common.define.chartData.textBar": "Bar", "Common.define.chartData.textBarNormal": "Ryhm<PERSON><PERSON><PERSON>", "Common.define.chartData.textBarNormal3d": "Kolmiulotteinen ryhmitelty sarake", "Common.define.chartData.textBarNormal3dPerspective": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sarake", "Common.define.chartData.textBarStacked": "<PERSON><PERSON><PERSON> sarake", "Common.define.chartData.textBarStacked3d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pinottu sarake", "Common.define.chartData.textBarStackedPer": "100% Pinottu sarake", "Common.define.chartData.textBarStackedPer3d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pinottu sarake", "Common.define.chartData.textCharts": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textColumn": "Column", "Common.define.chartData.textColumnSpark": "Column", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON><PERSON> al<PERSON> - r<PERSON><PERSON><PERSON><PERSON><PERSON> sarake", "Common.define.chartData.textComboBarLine": "Ryhm<PERSON><PERSON><PERSON> sarake- ja viivadiagrammi", "Common.define.chartData.textComboBarLineSecondary": "Ryhmitelty sa<PERSON>avio ja viiva toisella akselilla", "Common.define.chartData.textComboCustom": "Mukautettu yhdistelmä", "Common.define.chartData.textDoughnut": "Ympyräkaavio", "Common.define.chartData.textHBarNormal": "Ryhmitelty pylväsdiagrammi", "Common.define.chartData.textHBarNormal3d": "Kolmiulotteinen ryhmitelty palkki", "Common.define.chartData.textHBarStacked": "<PERSON><PERSON><PERSON> p<PERSON>", "Common.define.chartData.textHBarStacked3d": "<PERSON><PERSON><PERSON>lot<PERSON><PERSON> pinottu palkki", "Common.define.chartData.textHBarStackedPer": "100% Pinot<PERSON> palkki", "Common.define.chartData.textHBarStackedPer3d": "<PERSON><PERSON><PERSON>lot<PERSON><PERSON> pinottu palkki", "Common.define.chartData.textLine": "Viiva", "Common.define.chartData.textLine3d": "Kolmiulotteinen rivi", "Common.define.chartData.textLineMarker": "Line with markers", "Common.define.chartData.textLineSpark": "Viiva", "Common.define.chartData.textLineStacked": "Pinottu viiva", "Common.define.chartData.textLineStackedMarker": "Pinottu viiva ja merkit", "Common.define.chartData.textLineStackedPer": "100% Pinottu rivi", "Common.define.chartData.textLineStackedPerMarker": "100% Pinottu rivi ja merkit", "Common.define.chartData.textPie": "Pie", "Common.define.chartData.textPie3d": "Kolmiulotteinen ympyräkaavio", "Common.define.chartData.textPoint": "XY (Hajonta) ", "Common.define.chartData.textRadar": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textRadarFilled": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "Common.define.chartData.textRadarMarker": "<PERSON><PERSON><PERSON> ja merkit", "Common.define.chartData.textScatter": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "Pistekaavio suorilla viivoilla", "Common.define.chartData.textScatterLineMarker": "Pistekaavio suorilla viivoilla ja merkeillä", "Common.define.chartData.textScatterSmooth": "Pistekaavio pyöreillä viivoilla", "Common.define.chartData.textScatterSmoothMarker": "Pistekaavio pyöreillä viivoilla ja merkeillä", "Common.define.chartData.textSparks": "Sparklines", "Common.define.chartData.textStock": "Stock", "Common.define.chartData.textSurface": "Pinta", "Common.define.chartData.textWinLossSpark": "Voitto/Tappio", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "No format set", "Common.define.conditionalData.text1Above": "1 std dev above", "Common.define.conditionalData.text1Below": "1 std dev below", "Common.define.conditionalData.text2Above": "2 std dev above", "Common.define.conditionalData.text2Below": "2 std dev below", "Common.define.conditionalData.text3Above": "3 std dev above", "Common.define.conditionalData.text3Below": "3 std dev below", "Common.define.conditionalData.textAbove": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textAverage": "Average", "Common.define.conditionalData.textBegins": "Begins with", "Common.define.conditionalData.textBelow": "Below", "Common.define.conditionalData.textBetween": "Between", "Common.define.conditionalData.textBlank": "Tyhjä", "Common.define.conditionalData.textBlanks": "Contains blanks", "Common.define.conditionalData.textBottom": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textContains": "Contains", "Common.define.conditionalData.textDataBar": "Data bar", "Common.define.conditionalData.textDate": "Date", "Common.define.conditionalData.textDuplicate": "<PERSON><PERSON>", "Common.define.conditionalData.textEnds": "Ends with", "Common.define.conditionalData.textEqAbove": "Equal to or above", "Common.define.conditionalData.textEqBelow": "Equal to or below", "Common.define.conditionalData.textEqual": "Equal to", "Common.define.conditionalData.textError": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textErrors": "Contains errors", "Common.define.conditionalData.textFormula": "<PERSON><PERSON>", "Common.define.conditionalData.textGreater": "Greater than", "Common.define.conditionalData.textGreaterEq": "Greater than or equal to", "Common.define.conditionalData.textIconSets": "Icon sets", "Common.define.conditionalData.textLast7days": "In the last 7 days", "Common.define.conditionalData.textLastMonth": "Last month", "Common.define.conditionalData.textLastWeek": "Last week", "Common.define.conditionalData.textLess": "Vähemmä<PERSON> kuin", "Common.define.conditionalData.textLessEq": "Vähemmän kuin tai yhtäkuin", "Common.define.conditionalData.textNextMonth": "Next month", "Common.define.conditionalData.textNextWeek": "Next week", "Common.define.conditionalData.textNotBetween": "Not between", "Common.define.conditionalData.textNotBlanks": "Does not contain blanks", "Common.define.conditionalData.textNotContains": "Does not contain", "Common.define.conditionalData.textNotEqual": "Not equal to", "Common.define.conditionalData.textNotErrors": "Does not contain errors", "Common.define.conditionalData.textText": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textThisMonth": "<PERSON><PERSON><PERSON><PERSON> kuussa", "Common.define.conditionalData.textThisWeek": "This week", "Common.define.conditionalData.textToday": "Tänää<PERSON>", "Common.define.conditionalData.textTomorrow": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textTop": "Yläosa", "Common.define.conditionalData.textUnique": "Unique", "Common.define.conditionalData.textValue": "Value is", "Common.define.conditionalData.textYesterday": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textAccentedPicture": "Korostettu kuva", "Common.define.smartArt.textAccentProcess": "Prosessikaavio", "Common.define.smartArt.textAlternatingFlow": "<PERSON><PERSON><PERSON><PERSON><PERSON> virta", "Common.define.smartArt.textAlternatingHexagons": "Vaihtelevat kuusikulmiot", "Common.define.smartArt.textAlternatingPictureBlocks": "Vaihtelevat kuvapalkit", "Common.define.smartArt.textAlternatingPictureCircles": "Vaihtelevat kuvaympyrät", "Common.define.smartArt.textArchitectureLayout": "Architecture layout", "Common.define.smartArt.textArrowRibbon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textAscendingPictureAccentProcess": "Prosessikaavio no<PERSON>va", "Common.define.smartArt.textBalance": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicBendingProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicBlockList": "Lohkokaavio", "Common.define.smartArt.textBasicChevronProcess": "Chevron-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicMatrix": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicPie": "Ympyräkaavio", "Common.define.smartArt.textBasicProcess": "Prosessikaavio", "Common.define.smartArt.textBasicPyramid": "Pyramidikaavio", "Common.define.smartArt.textBasicRadial": "Säteittäinen kaavio", "Common.define.smartArt.textBasicTarget": "Maalitaulukaavio", "Common.define.smartArt.textBasicTimeline": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicVenn": "<PERSON><PERSON>n-ka<PERSON>o", "Common.define.smartArt.textBendingPictureAccentList": "<PERSON><PERSON><PERSON><PERSON>va<PERSON>avi<PERSON>", "Common.define.smartArt.textBendingPictureBlocks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textBendingPictureCaption": "Taivutettu kuvakaavio ja kuvatekstit", "Common.define.smartArt.textBendingPictureCaptionList": "Taivutettu kuvakaavio ja kuvatekstit", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Kuvakaavio ja läpikuultava teksti", "Common.define.smartArt.textBlockCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textBubblePictureList": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textCaptionedPictures": "Kuvat kuvateksteillä", "Common.define.smartArt.textChevronAccentProcess": "Chevron-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textChevronList": "Chevron-luettelo", "Common.define.smartArt.textCircleAccentTimeline": "Ympyröistä koostuva aikajana", "Common.define.smartArt.textCircleArrowProcess": "Ympyränuolet-prosessikaavio", "Common.define.smartArt.textCirclePictureHierarchy": "Hierarkiakaavio pyöreillä kuvilla", "Common.define.smartArt.textCircleProcess": "Ympyrä-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textCircleRelationship": "Suhdekaavio ympyröinä", "Common.define.smartArt.textCircularBendingProcess": "Taivutettu ympyräkaavio", "Common.define.smartArt.textCircularPictureCallout": "Ympyräkaavio ja kuva<PERSON>", "Common.define.smartArt.textClosedChevronProcess": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textContinuousArrowProcess": "Continuous arrow process", "Common.define.smartArt.textContinuousBlockProcess": "Continuous block process", "Common.define.smartArt.textContinuousCycle": "Continuous cycle", "Common.define.smartArt.textContinuousPictureList": "Continuous picture list", "Common.define.smartArt.textConvergingArrows": "Converging arrows", "Common.define.smartArt.textConvergingRadial": "Converging radial", "Common.define.smartArt.textConvergingText": "Converging text", "Common.define.smartArt.textCounterbalanceArrows": "Tasapainottavat nuolet", "Common.define.smartArt.textCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textCycleMatrix": "Ympyrämatriisi", "Common.define.smartArt.textDescendingBlockList": "Laskeutuvat lohkot", "Common.define.smartArt.textDescendingProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON> -ka<PERSON>o", "Common.define.smartArt.textDetailedProcess": "Detailed process", "Common.define.smartArt.textDivergingArrows": "Erkaantuvat nuolet", "Common.define.smartArt.textDivergingRadial": "Erkaantuvat säteet", "Common.define.smartArt.textEquation": "Yhtälö", "Common.define.smartArt.textFramedTextPicture": "<PERSON><PERSON><PERSON><PERSON> tekstigrafiikka", "Common.define.smartArt.textFunnel": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textGear": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textGridMatrix": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textGroupedList": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON>", "Common.define.smartArt.textHalfCircleOrganizationChart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> muotoinen organisaatiokaavio", "Common.define.smartArt.textHexagonCluster": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> klusteri", "Common.define.smartArt.textHexagonRadial": "Säteittäinen kuuusikulmio", "Common.define.smartArt.textHierarchy": "Hierarkia", "Common.define.smartArt.textHierarchyList": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textHorizontalBulletList": "<PERSON><PERSON><PERSON><PERSON><PERSON> l<PERSON><PERSON>", "Common.define.smartArt.textHorizontalHierarchy": "Horisontaalinen hierarkia", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horisontaalinen hierarkia tunnisteilla", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horisontaalinen monitasoinen hierarkia", "Common.define.smartArt.textHorizontalOrganizationChart": "Horisontaalinen organisaatiokaavio", "Common.define.smartArt.textHorizontalPictureList": "Horisontaalinen kuvaluettelo", "Common.define.smartArt.textIncreasingArrowProcess": "Kasvava nuoli -prose<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textIncreasingCircleProcess": "Kasvava ympyrä -prosessikaavio", "Common.define.smartArt.textInterconnectedBlockProcess": "<PERSON><PERSON><PERSON><PERSON> kiinn<PERSON>", "Common.define.smartArt.textInterconnectedRings": "Toisiinsa kiinnitetyt renkaat", "Common.define.smartArt.textInvertedPyramid": "Käännetty pyramidi", "Common.define.smartArt.textLabeledHierarchy": "<PERSON><PERSON><PERSON><PERSON> tunnisteilla", "Common.define.smartArt.textLinearVenn": "Linear Venn", "Common.define.smartArt.textLinedList": "Lined list", "Common.define.smartArt.textList": "List", "Common.define.smartArt.textMatrix": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textMultidirectionalCycle": "Multidirectional cycle", "Common.define.smartArt.textNameAndTitleOrganizationChart": "<PERSON><PERSON> ja titteli -organisa<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textNestedTarget": "Nested target", "Common.define.smartArt.textNondirectionalCycle": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textOpposingArrows": "Vastakkaiset nuolet", "Common.define.smartArt.textOpposingIdeas": "Vastakkaiset ideat", "Common.define.smartArt.textOrganizationChart": "Organization chart", "Common.define.smartArt.textOther": "Other", "Common.define.smartArt.textPhasedProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> prosessi", "Common.define.smartArt.textPicture": "<PERSON><PERSON>", "Common.define.smartArt.textPictureAccentBlocks": "Kuvakorostuslohkot", "Common.define.smartArt.textPictureAccentList": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPictureAccentProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPictureCaptionList": "Kuva+teksti -lista", "Common.define.smartArt.textPictureFrame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPictureGrid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPictureLineup": "<PERSON><PERSON><PERSON> j<PERSON>", "Common.define.smartArt.textPictureOrganizationChart": "Organisaatiokaavio - Kuvat", "Common.define.smartArt.textPictureStrips": "Kuvana<PERSON>at", "Common.define.smartArt.textPieProcess": "Ympyräprosessikaavio", "Common.define.smartArt.textPlusAndMinus": "Plus ja miinus", "Common.define.smartArt.textProcess": "Prosessi", "Common.define.smartArt.textProcessArrows": "Prosessi - Nuolet", "Common.define.smartArt.textProcessList": "Prosessi - Luettelo", "Common.define.smartArt.textPyramid": "Pyramidi", "Common.define.smartArt.textPyramidList": "Pyramidi-luettelo", "Common.define.smartArt.textRadialCluster": "Säteittäinen klusteri", "Common.define.smartArt.textRadialCycle": "Säteittäinen sykli", "Common.define.smartArt.textRadialList": "Säteittäinen lista", "Common.define.smartArt.textRadialPictureList": "Säteittäinen kuvalista", "Common.define.smartArt.textRadialVenn": "Säteittäinen Venn-kaavio", "Common.define.smartArt.textRandomToResultProcess": "Random to result process", "Common.define.smartArt.textRelationship": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textRepeatingBendingProcess": "Repeating bending process", "Common.define.smartArt.textReverseList": "Reverse list", "Common.define.smartArt.textSegmentedCycle": "Se<PERSON><PERSON><PERSON>", "Common.define.smartArt.textSegmentedProcess": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textSegmentedPyramid": "Segmentoitu pyramidi", "Common.define.smartArt.textSnapshotPictureList": "Snapshot picture list", "Common.define.smartArt.textSpiralPicture": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textSquareAccentList": "Square accent list", "Common.define.smartArt.textStackedList": "<PERSON><PERSON><PERSON> l<PERSON>", "Common.define.smartArt.textStackedVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStaggeredProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textStepDownProcess": "Step down process", "Common.define.smartArt.textStepUpProcess": "Step up process", "Common.define.smartArt.textSubStepProcess": "Sub-step process", "Common.define.smartArt.textTabbedArc": "<PERSON><PERSON>", "Common.define.smartArt.textTableHierarchy": "Table hierarchy", "Common.define.smartArt.textTableList": "Table list", "Common.define.smartArt.textTabList": "Välilehtiluettelo", "Common.define.smartArt.textTargetList": "Target list", "Common.define.smartArt.textTextCycle": "Text cycle", "Common.define.smartArt.textThemePictureAccent": "Theme picture accent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Theme picture alternating accent", "Common.define.smartArt.textThemePictureGrid": "Theme picture grid", "Common.define.smartArt.textTitledMatrix": "Titled matrix", "Common.define.smartArt.textTitledPictureAccentList": "Titled picture accent list", "Common.define.smartArt.textTitledPictureBlocks": "Titled picture blocks", "Common.define.smartArt.textTitlePictureLineup": "Title picture lineup", "Common.define.smartArt.textTrapezoidList": "Trapezoid list", "Common.define.smartArt.textUpwardArrow": "Upward arrow", "Common.define.smartArt.textVaryingWidthList": "Vaihtele<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "Common.define.smartArt.textVerticalAccentList": "Vertical accent list", "Common.define.smartArt.textVerticalArrowList": "Vertical arrow list", "Common.define.smartArt.textVerticalBendingProcess": "Vertical bending process", "Common.define.smartArt.textVerticalBlockList": "Vertical block list", "Common.define.smartArt.textVerticalBoxList": "Vertical box list", "Common.define.smartArt.textVerticalBracketList": "Vertical bracket list", "Common.define.smartArt.textVerticalBulletList": "Vertical bullet list", "Common.define.smartArt.textVerticalChevronList": "Vertical chevron list", "Common.define.smartArt.textVerticalCircleList": "Vertical circle list", "Common.define.smartArt.textVerticalCurvedList": "Vertical curved list", "Common.define.smartArt.textVerticalEquation": "Vertical equation", "Common.define.smartArt.textVerticalPictureAccentList": "Vertical picture accent list", "Common.define.smartArt.textVerticalPictureList": "Vertical picture list", "Common.define.smartArt.textVerticalProcess": "Vertical process", "Common.Translation.textMoreButton": "More", "Common.Translation.tipFileLocked": "Asiakirja on lukittu. <PERSON><PERSON> kuitenkin tehdä siihen muutoksia ja tallentaa sen paikallisena kopiona myöhemmin.", "Common.Translation.tipFileReadOnly": "<PERSON><PERSON><PERSON><PERSON> on vain luku -muodossa. Säilyyttääks<PERSON> tekem<PERSON> muuto<PERSON>, tallenna tiedosto eri ni<PERSON>ä tai eri tiedost<PERSON>iin.", "Common.Translation.warnFileLocked": "The file is being edited in another app. You can continue editing and save it as a copy.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON> kopio", "Common.Translation.warnFileLockedBtnView": "<PERSON><PERSON> ka<PERSON> varten", "Common.UI.ButtonColored.textAutoColor": "Automaattinen", "Common.UI.ButtonColored.textEyedropper": "<PERSON><PERSON><PERSON>", "Common.UI.ButtonColored.textNewColor": "Lisää uusi mukautettu väri", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON> reunuk<PERSON>", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON> reunuk<PERSON>", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON> t<PERSON>", "Common.UI.ExtendedColorDialog.addButtonText": "Lisää", "Common.UI.ExtendedColorDialog.textCurrent": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textHexErr": "Syötetty arvo ei ole o<PERSON>in. Ole hyvä ja syötä arvo välillä 000000 ja FFFFFF", "Common.UI.ExtendedColorDialog.textNew": "<PERSON>us<PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "Syötetty arvo ei ole o<PERSON>.<br>Ole hyvä ja syötä numeerinen arvo välillä 0 ja 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON> väriä", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "<PERSON><PERSON><PERSON> sa<PERSON>", "Common.UI.InputFieldBtnPassword.textHintHold": "Paina ja pidä pohjassa n<PERSON>ä<PERSON><PERSON><PERSON> sa<PERSON>n", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "Common.UI.SearchBar.textFind": "Etsi", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON> haku", "Common.UI.SearchBar.tipNextResult": "Next result", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Avaa lisäasetukset", "Common.UI.SearchBar.tipPreviousResult": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON><PERSON> t<PERSON>", "Common.UI.SearchDialog.textMatchCase": "<PERSON><PERSON><PERSON><PERSON>/pienten kirjainten mukaan", "Common.UI.SearchDialog.textReplaceDef": "Syötä korvaava teksti", "Common.UI.SearchDialog.textSearchStart": "Syötä tekstisi tässä", "Common.UI.SearchDialog.textTitle": "<PERSON>ts<PERSON> ja <PERSON>", "Common.UI.SearchDialog.textTitle2": "Etsi", "Common.UI.SearchDialog.textWholeWords": "<PERSON>ain koko<PERSON>set sanat", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON><PERSON><PERSON>", "Common.UI.SynchronizeTip.textDontShow": "Älä näytä tätä viestiä uudelleen", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "Asiakirja on toisen käyttäjän muuttama.<br>Ole hyvä ja klikkaa tallentaaksesi muutoksesi ja lataa uudelleen muutokset.", "Common.UI.ThemeColorPalette.textRecentColors": "Viimeaikaiset värit", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "Teeman värit", "Common.UI.Themes.txtThemeClassicLight": "Klassinen vaalea", "Common.UI.Themes.txtThemeContrastDark": "<PERSON><PERSON> k<PERSON>", "Common.UI.Themes.txtThemeDark": "Tumma", "Common.UI.Themes.txtThemeGray": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "Vaalea", "Common.UI.Themes.txtThemeSystem": "Sama kuin järjetelmässä", "Common.UI.Window.cancelButtonText": "Peruuta", "Common.UI.Window.closeButtonText": "Sulje", "Common.UI.Window.noButtonText": "<PERSON>i", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.textDontShow": "Älä näytä tätä viestiä uudelleen", "Common.UI.Window.textError": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textInformation": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textWarning": "Varo<PERSON><PERSON>", "Common.UI.Window.yesButtonText": "K<PERSON><PERSON>ä", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtAqua": "<PERSON><PERSON> tur<PERSON>i", "Common.Utils.ThemeColor.txtbackground": "Background", "Common.Utils.ThemeColor.txtBlack": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtBlue": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtBrightGreen": "Vaaleanvihreä", "Common.Utils.ThemeColor.txtBrown": "R<PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarker": "Tummempi", "Common.Utils.ThemeColor.txtDarkGray": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkGreen": "Tummanvihreä", "Common.Utils.ThemeColor.txtDarkPurple": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkRed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkTeal": "Tumma sinivihreä", "Common.Utils.ThemeColor.txtDarkYellow": "<PERSON><PERSON> keltainen", "Common.Utils.ThemeColor.txtGold": "Kulta", "Common.Utils.ThemeColor.txtGray": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtGreen": "Vihreä", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtLightBlue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtLighter": "Vaaleampi", "Common.Utils.ThemeColor.txtLightGray": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtLightGreen": "Vaaleanvihreä", "Common.Utils.ThemeColor.txtLightOrange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtLightYellow": "Vaaleankeltainen", "Common.Utils.ThemeColor.txtOrange": "Orans<PERSON>", "Common.Utils.ThemeColor.txtPink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtPurple": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtRed": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtRose": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtWhite": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtYellow": "<PERSON><PERSON><PERSON>", "Common.Views.About.txtAddress": "osoite: ", "Common.Views.About.txtLicensee": "LISENSSINSAAJA", "Common.Views.About.txtLicensor": "LISENSSINANTAJA", "Common.Views.About.txtMail": "sähköposti: ", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "puh.: ", "Common.Views.About.txtVersion": "Versio", "Common.Views.AutoCorrectDialog.textAdd": "Lisää", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Apply as you work", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Automaattinen korjaus", "Common.Views.AutoCorrectDialog.textAutoFormat": "Muotoile automaattisesti kir<PERSON> aikana", "Common.Views.AutoCorrectDialog.textBy": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textDelete": "Poista", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet- ja verkkotietueet hyperlinkkeinä", "Common.Views.AutoCorrectDialog.textMathCorrect": "Math AutoCorrect", "Common.Views.AutoCorrectDialog.textNewRowCol": "Include new rows and columns in table", "Common.Views.AutoCorrectDialog.textRecognized": "<PERSON><PERSON><PERSON><PERSON><PERSON> funk<PERSON>", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>k<PERSON> on tunnistettu matemaattisiksi. Niitä ei kursivoida automaattisesti.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "<PERSON><PERSON><PERSON> a<PERSON>na", "Common.Views.AutoCorrectDialog.textReplaceType": "<PERSON><PERSON><PERSON> te<PERSON> k<PERSON>", "Common.Views.AutoCorrectDialog.textReset": "Reset", "Common.Views.AutoCorrectDialog.textResetAll": "<PERSON><PERSON><PERSON> o<PERSON>us", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Automaattinen korjaus", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Tunnistetut funktiot voivat sisältää ainoastaan kirjaimia A-Z, pieniä tai isoja", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Kaik<PERSON> l<PERSON>ääm<PERSON><PERSON> il<PERSON> poistetaan ja poistetut kohdat palautetaan alkuperäisiksi. Haluatko jatkaa?", "Common.Views.AutoCorrectDialog.warnReplace": "Automaattinen korjaus sisältää jo kohdan %1. Haluatko korvata sen?", "Common.Views.AutoCorrectDialog.warnReset": "<PERSON><PERSON><PERSON> asettamasi automaattisen kor<PERSON><PERSON> asetukset poistetaan ja korjatut kohdat palautetaan alkuperäisiksi. Haluatko jatkaa?", "Common.Views.AutoCorrectDialog.warnRestore": "Automaattisen korjauksen kohta %1 palautetaan alkuperäiseen alkuperäiseen arvoonsa. Haluatko jatkaa?", "Common.Views.Chat.textChat": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Chat.textClosePanel": "<PERSON><PERSON> keskustelu", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "Lähetä", "Common.Views.Comments.mniAuthorAsc": "Tekijät A:sta Ö:hön", "Common.Views.Comments.mniAuthorDesc": "Tekijät Ö:stä A:han", "Common.Views.Comments.mniDateAsc": "Oldest", "Common.Views.Comments.mniDateDesc": "Newest", "Common.Views.Comments.mniFilterGroups": "<PERSON><PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON> mukaan", "Common.Views.Comments.mniPositionAsc": "From top", "Common.Views.Comments.mniPositionDesc": "From bottom", "Common.Views.Comments.textAdd": "Lisää", "Common.Views.Comments.textAddComment": "Lisää kommentti", "Common.Views.Comments.textAddCommentToDoc": "Lisää kommentti asiakirjaan", "Common.Views.Comments.textAddReply": "Lisää <PERSON>", "Common.Views.Comments.textAll": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAnonym": "Vierailija", "Common.Views.Comments.textCancel": "Peruuta", "Common.Views.Comments.textClose": "Sulje", "Common.Views.Comments.textClosePanel": "Sulje kommentit", "Common.Views.Comments.textComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textComments": "Ko<PERSON>ntit", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Syötä kommenttisi tässä", "Common.Views.Comments.textHintAddComment": "Lisää kommentti", "Common.Views.Comments.textOpenAgain": "<PERSON><PERSON>", "Common.Views.Comments.textReply": "Vastaus", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "Ratkaist<PERSON>", "Common.Views.Comments.textSort": "Järjestä kommentit", "Common.Views.Comments.textSortFilter": "Järjestele ja suodata kommentteja", "Common.Views.Comments.textSortFilterMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, suodata ja enemmän", "Common.Views.Comments.textSortMore": "Järjest<PERSON> ja enemmän", "Common.Views.Comments.textViewResolved": "<PERSON><PERSON>a ei ole o<PERSON>uksia avata kommenttia uude<PERSON>.", "Common.Views.Comments.txtEmpty": "There are no comments in the sheet.", "Common.Views.CopyWarningDialog.textDontShow": "Älä näytä tätä viestiä uudelleen", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON><PERSON>, le<PERSON><PERSON><PERSON><PERSON> ja liittämisen toiminnot muokkaajan työkaluvalikon painikkeilla ja valikoiden toiminnoilla suoritetaan vain tässä muokkaajan välilehdessä.<br><br>Jo<PERSON> haluat kopioida tai liittää muokkaajan ulkopuolisiin sovelluksiin tai sovelluksista, niin voit käyttää seuraavia näppäimistöyhdistelmiä:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON>, leikkaa ja liitä toim<PERSON>ot", "Common.Views.CopyWarningDialog.textToCopy": "<PERSON><PERSON><PERSON><PERSON> varten", "Common.Views.CopyWarningDialog.textToCut": "<PERSON><PERSON><PERSON><PERSON><PERSON> varten", "Common.Views.CopyWarningDialog.textToPaste": "Liittämistä varten", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Lataa", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Tarkista Pikakäyttö-työkalurivillä näkyvät komennot", "Common.Views.CustomizeQuickAccessDialog.textPrint": "<PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textTitle": "<PERSON>ok<PERSON><PERSON> p<PERSON>", "Common.Views.CustomizeQuickAccessDialog.textUndo": "<PERSON><PERSON><PERSON>", "Common.Views.DocumentAccessDialog.textLoading": "Ladataan...", "Common.Views.DocumentAccessDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "K<PERSON><PERSON>ä", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Tyyppi", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Numero", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "<PERSON><PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Arvo", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "Pyyhekumi", "Common.Views.Draw.hintSelect": "Valitse", "Common.Views.Draw.txtEraser": "Pyyhekumi", "Common.Views.Draw.txtHighlighter": "Korostukset", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Kynä", "Common.Views.Draw.txtSelect": "Valitse", "Common.Views.Draw.txtSize": "<PERSON><PERSON>", "Common.Views.EditNameDialog.textLabel": "<PERSON>nn<PERSON><PERSON>:", "Common.Views.EditNameDialog.textLabelError": "Label must not be empty.", "Common.Views.Header.ariaQuickAccessToolbar": "Pikakäyttö-työkalurivi", "Common.Views.Header.labelCoUsersDescr": "Useat käyttäjät muokkaavat tällä hetkellä asiakirjaa.", "Common.Views.Header.textAddFavorite": "Merkitse suosikiksi", "Common.Views.Header.textAdvSettings": "Advanced settings", "Common.Views.Header.textBack": "<PERSON><PERSON><PERSON>", "Common.Views.Header.textClose": "<PERSON><PERSON>", "Common.Views.Header.textCompactView": "<PERSON><PERSON>", "Common.Views.Header.textHideLines": "Piilota viivaimet", "Common.Views.Header.textHideStatusBar": "Combine sheet and status bars", "Common.Views.Header.textPrint": "<PERSON><PERSON><PERSON>", "Common.Views.Header.textReadOnly": "<PERSON>ain luku", "Common.Views.Header.textRemoveFavorite": "Poista suosikeista", "Common.Views.Header.textSaveBegin": "Saving...", "Common.Views.Header.textSaveChanged": "Muokattu", "Common.Views.Header.textSaveEnd": "Kaikki muutoks<PERSON> tallennettu", "Common.Views.Header.textSaveExpander": "Kaikki muutoks<PERSON> tallennettu", "Common.Views.Header.textShare": "Jaa", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "Hallinnoi as<PERSON>kirjan k<PERSON>uk<PERSON>", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Muokkaa Pikakäyttö-työkaluriviä", "Common.Views.Header.tipDownload": "Lataa <PERSON>", "Common.Views.Header.tipGoEdit": "Muokkaa nykyistä tiedostoa", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipPrintQuick": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipRedo": "Redo", "Common.Views.Header.tipSave": "Save", "Common.Views.Header.tipSearch": "Etsi", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Undock into separate window", "Common.Views.Header.tipUsers": "Näytä k<PERSON>yttäjät", "Common.Views.Header.tipViewSettings": "Näytä as<PERSON>uk<PERSON>", "Common.Views.Header.tipViewUsers": "Näytä k<PERSON>yt<PERSON>äj<PERSON> ja hallinnoi asiakirjan käyt<PERSON>öoikeuk<PERSON>", "Common.Views.Header.txtAccessRights": "Change access rights", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON>", "Common.Views.History.textCloseHistory": "Sulje historia", "Common.Views.History.textHideAll": "Piilota yksityiskohtaiset muutokset", "Common.Views.History.textHighlightDeleted": "<PERSON><PERSON><PERSON> p<PERSON>", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON>", "Common.Views.History.textShowAll": "Näytä yksityiskohtaiset muutokset", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Versiohistoria", "Common.Views.ImageFromUrlDialog.textUrl": "<PERSON>it<PERSON> kuvan verkko-osoite:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Tämä kenttä tarvitaan", "Common.Views.ImageFromUrlDialog.txtNotUrl": "<PERSON><PERSON><PERSON><PERSON><PERSON> tied<PERSON>on tulisi olla verkko-osoite \"http://www.esimerkki.com\" muodossa", "Common.Views.ListSettingsDialog.textBulleted": "Bulleted", "Common.Views.ListSettingsDialog.textFromFile": "Tiedostosta", "Common.Views.ListSettingsDialog.textFromStorage": "Tallennusvälineestä", "Common.Views.ListSettingsDialog.textFromUrl": "From URL", "Common.Views.ListSettingsDialog.textNumbering": "Numbered", "Common.Views.ListSettingsDialog.textSelect": "Select from", "Common.Views.ListSettingsDialog.tipChange": "Change bullet", "Common.Views.ListSettingsDialog.txtBullet": "Luettelomerkki", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImport": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNewBullet": "<PERSON><PERSON><PERSON> l<PERSON>", "Common.Views.ListSettingsDialog.txtNewImage": "New image", "Common.Views.ListSettingsDialog.txtNone": "None", "Common.Views.ListSettingsDialog.txtOfText": "% of text", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "Start at", "Common.Views.ListSettingsDialog.txtSymbol": "Symboli", "Common.Views.ListSettingsDialog.txtTitle": "Luette<PERSON> asetukset", "Common.Views.ListSettingsDialog.txtType": "Tyyppi", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Poista", "Common.Views.MacrosDialog.textLoading": "Ladataan...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON>", "Common.Views.OpenDialog.textInvalidRange": "Invalid cells range", "Common.Views.OpenDialog.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "Common.Views.OpenDialog.txtAdvanced": "Advanced", "Common.Views.OpenDialog.txtColon": "Colon", "Common.Views.OpenDialog.txtComma": "Pilk<PERSON>", "Common.Views.OpenDialog.txtDelimiter": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtDestData": "Choose where to put the data", "Common.Views.OpenDialog.txtEmpty": "This field is required", "Common.Views.OpenDialog.txtEncoding": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON>ää<PERSON><PERSON> sa<PERSON>.", "Common.Views.OpenDialog.txtOpenFile": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "Common.Views.OpenDialog.txtOther": "Other", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "Preview", "Common.Views.OpenDialog.txtProtected": "Once you enter the password and open the file, the current password to the file will be reset.", "Common.Views.OpenDialog.txtSemicolon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtSpace": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtTab": "Välilehti", "Common.Views.OpenDialog.txtTitle": "Valitse %1 vaihtoehtoa", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtDescription": "<PERSON><PERSON> salasana as<PERSON><PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtIncorrectPwd": "Salasanat eivät vastaa toisiaan", "Common.Views.PasswordDialog.txtPassword": "<PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON> sa<PERSON>", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON> sa<PERSON>", "Common.Views.PasswordDialog.txtWarning": "Varoitus: <PERSON><PERSON> ka<PERSON> tai unoh<PERSON>, sitä ei voi palauttaa. Säilytä sitä turvallisessa pai<PERSON>.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON><PERSON>", "Common.Views.PluginPanel.textClosePanel": "<PERSON><PERSON>", "Common.Views.PluginPanel.textLoading": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.groupCaption": "Laajennukset", "Common.Views.Plugins.strPlugins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Asetukset", "Common.Views.Plugins.textStart": "Aloita", "Common.Views.Plugins.textStop": "Pysäytä", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "<PERSON><PERSON><PERSON> taustalaaj<PERSON>nuksista", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "Käytä salas<PERSON>suojattua salausta", "Common.Views.Protection.hintDelPwd": "Poista sa<PERSON>", "Common.Views.Protection.hintPwd": "<PERSON><PERSON><PERSON>da tai poista salasana", "Common.Views.Protection.hintSignature": "Lisää digitaalinen allekirjoitus tai", "Common.Views.Protection.txtAddPwd": "Lis<PERSON><PERSON> sa<PERSON>", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON>", "Common.Views.Protection.txtDeletePwd": "Poista sa<PERSON>", "Common.Views.Protection.txtEncrypt": "Käytä salausta", "Common.Views.Protection.txtInvisibleSignature": "Lisää digitaalinen allekirjoitus", "Common.Views.Protection.txtSignature": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtSignatureLine": "Lisää allekirjoitusrivi", "Common.Views.RecentFiles.txtOpenRecent": "Avaa viimeaikainen", "Common.Views.RenameDialog.textName": "Tiedoston nimi", "Common.Views.RenameDialog.txtInvalidName": "Tiedoston nimessä ei voi olla seuraavia merkkejä:", "Common.Views.ReviewChanges.hintNext": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.hintPrev": "<PERSON><PERSON><PERSON><PERSON> mu<PERSON>", "Common.Views.ReviewChanges.strFast": "Fast", "Common.Views.ReviewChanges.strFastDesc": "Reaaliaikainen yhteismuokkaus. Kaikki muutokset tallennetaan automaattisesti.", "Common.Views.ReviewChanges.strStrict": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strStrictDesc": "<PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON>na\" -painiketta synkronoidaksesi sinun ja muiden tekemät muutokset.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Hyväksy äskettäiset muutokset", "Common.Views.ReviewChanges.tipCoAuthMode": "Aseta yhteismuokkaustila", "Common.Views.ReviewChanges.tipCommentRem": "Poista kommentit", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Poista nykyiset kommentit", "Common.Views.ReviewChanges.tipCommentResolve": "Ratkaise kommentit", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Ratkaise nykyiset kommentit", "Common.Views.ReviewChanges.tipHistory": "<PERSON><PERSON><PERSON><PERSON> versiot", "Common.Views.ReviewChanges.tipRejectCurrent": "Reject current change", "Common.Views.ReviewChanges.tipReview": "Track changes", "Common.Views.ReviewChanges.tipReviewView": "<PERSON><PERSON><PERSON> tila, jossa haluat mu<PERSON> n<PERSON>kyv<PERSON>n", "Common.Views.ReviewChanges.tipSetDocLang": "Aseta asiakirjan kieli", "Common.Views.ReviewChanges.tipSetSpelling": "Spell checking", "Common.Views.ReviewChanges.tipSharing": "Hallinnoi as<PERSON>kirjan k<PERSON>uk<PERSON>", "Common.Views.ReviewChanges.txtAccept": "Hyväksy", "Common.Views.ReviewChanges.txtAcceptAll": "Hyväksy kaikki muutokset", "Common.Views.ReviewChanges.txtAcceptChanges": "Hyväksy muutokset", "Common.Views.ReviewChanges.txtAcceptCurrent": "Hyväksy äskettäiset muutokset", "Common.Views.ReviewChanges.txtChat": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtClose": "Sulje", "Common.Views.ReviewChanges.txtCoAuthMode": "Co-editing Mode", "Common.Views.ReviewChanges.txtCommentRemAll": "Poista kaikki kommentit", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Poista nykyiset kommentit", "Common.Views.ReviewChanges.txtCommentRemMy": "Poista minun kommenttini", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Poista minun n<PERSON> kom<PERSON>i", "Common.Views.ReviewChanges.txtCommentRemove": "Poista", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "Ratkaise kaikki kommentit", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Ratkaise nykyiset kommentit", "Common.Views.ReviewChanges.txtCommentResolveMy": "Ratkaise omat kommentit", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Rat<PERSON>se omat n<PERSON> kommentit", "Common.Views.ReviewChanges.txtDocLang": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtFinal": "Kaikki muutokset hyväksytty (Esikatsele)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Versiohistoria", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON><PERSON> mu<PERSON> (muokkaukset)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "Next", "Common.Views.ReviewChanges.txtOriginal": "Kaikki muutokset hylätty (Esikatsele)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Previous", "Common.Views.ReviewChanges.txtReject": "Hylkää", "Common.Views.ReviewChanges.txtRejectAll": "Hylkää kaikki muutokset", "Common.Views.ReviewChanges.txtRejectChanges": "Hylkää muutokset", "Common.Views.ReviewChanges.txtRejectCurrent": "Reject Current Change", "Common.Views.ReviewChanges.txtSharing": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtSpelling": "Spell checking", "Common.Views.ReviewChanges.txtTurnon": "Track Changes", "Common.Views.ReviewChanges.txtView": "Display Mode", "Common.Views.ReviewPopover.textAdd": "Lisää", "Common.Views.ReviewPopover.textAddReply": "Lisää <PERSON>", "Common.Views.ReviewPopover.textCancel": "Peruuta", "Common.Views.ReviewPopover.textClose": "Sulje", "Common.Views.ReviewPopover.textComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Enter your comment here", "Common.Views.ReviewPopover.textMention": "+mainitseminen tarjoaa pääsyn asiakirjaan ja lähettää ilmoituksen sähköpostitse.", "Common.Views.ReviewPopover.textMentionNotify": "+mainitseminen lähettää käyttäjälle ilmoituksen sähköpostitse", "Common.Views.ReviewPopover.textOpenAgain": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textReply": "Reply", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "<PERSON><PERSON>a ei ole o<PERSON>uksia avata kommenttia uude<PERSON>.", "Common.Views.ReviewPopover.txtDeleteTip": "Poista", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "<PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textByColumns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textByRows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textCaseSensitive": "Case sensitive", "Common.Views.SearchPanel.textCell": "Solu", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON> haku", "Common.Views.SearchPanel.textContentChanged": "<PERSON><PERSON><PERSON><PERSON> mu<PERSON>.", "Common.Views.SearchPanel.textFind": "Etsi", "Common.Views.SearchPanel.textFindAndReplace": "<PERSON>ts<PERSON> ja <PERSON>", "Common.Views.SearchPanel.textFormula": "<PERSON><PERSON>", "Common.Views.SearchPanel.textFormulas": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textItemEntireCell": "<PERSON><PERSON> solun si<PERSON>", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} k<PERSON><PERSON><PERSON> k<PERSON> onnistuneesti.", "Common.Views.SearchPanel.textLookIn": "Look in", "Common.Views.SearchPanel.textMatchUsingRegExp": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textName": "Name", "Common.Views.SearchPanel.textNoMatches": "No matches", "Common.Views.SearchPanel.textNoSearchResults": "No search results", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} kohteista korvattu. Jäljellä olevat {2} kohdetta ovat muiden käyttäjien lukitsemia.", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceAll": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textReplaceWith": "<PERSON><PERSON><PERSON> tällä...", "Common.Views.SearchPanel.textSearch": "Etsi", "Common.Views.SearchPanel.textSearchAgain": "{0}<PERSON><PERSON><PERSON> uusi haku{1} saadaksesi tarkempia tuloksia.", "Common.Views.SearchPanel.textSearchHasStopped": "Haku on keskey<PERSON>yt", "Common.Views.SearchPanel.textSearchOptions": "Search options", "Common.Views.SearchPanel.textSearchResults": "Search results: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textSelectDataRange": "Valitse tietoalue", "Common.Views.SearchPanel.textSheet": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSpecificRange": "Specific range", "Common.Views.SearchPanel.textTooManyResults": "Tuloksia on liian paljon näytettäväksi tässä", "Common.Views.SearchPanel.textValue": "Arvo", "Common.Views.SearchPanel.textValues": "Arvot", "Common.Views.SearchPanel.textWholeWords": "<PERSON>ain koko<PERSON>set sanat", "Common.Views.SearchPanel.textWithin": "Sisällä", "Common.Views.SearchPanel.textWorkbook": "Työkirja", "Common.Views.SearchPanel.tipNextResult": "Next result", "Common.Views.SearchPanel.tipPreviousResult": "<PERSON><PERSON><PERSON>", "Common.Views.SelectFileDlg.textLoading": "<PERSON><PERSON><PERSON>", "Common.Views.SelectFileDlg.textTitle": "Valitse tietolähde", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Etäisyys", "Common.Views.ShapeShadowDialog.txtSize": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtTitle": "Säädä <PERSON>", "Common.Views.ShapeShadowDialog.txtTransparency": "Läpinäkyvyys", "Common.Views.SignDialog.textBold": "Bold", "Common.Views.SignDialog.textCertificate": "Ser<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "<PERSON><PERSON>", "Common.Views.SignDialog.textInputName": "Input signer name", "Common.Views.SignDialog.textItalic": "Kursivoit<PERSON>", "Common.Views.SignDialog.textNameError": "Allekirjoittajan nimi ei voi olla tyhjä.", "Common.Views.SignDialog.textPurpose": "<PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON><PERSON><PERSON><PERSON> tark<PERSON>", "Common.Views.SignDialog.textSelect": "Valitse", "Common.Views.SignDialog.textSelectImage": "Valitse kuva", "Common.Views.SignDialog.textSignature": "Allekirjoitus <PERSON>", "Common.Views.SignDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textUseImage": "tai klikkaa 'Valitse kuva' käyttääks<PERSON> kuvaa allekirjoituksena", "Common.Views.SignDialog.textValid": "Valid from %1 to %2", "Common.Views.SignDialog.tipFontName": "Font name", "Common.Views.SignDialog.tipFontSize": "Fonttikoko", "Common.Views.SignSettingsDialog.textAllowComment": "<PERSON><PERSON> allekirjoittajan lisätä kommentti", "Common.Views.SignSettingsDialog.textDefInstruction": "Tarkasta tietojen o<PERSON> ennen asiakirjan allekirjoittami<PERSON>.", "Common.Views.SignSettingsDialog.textInfoEmail": "Suggested signer's e-mail", "Common.Views.SignSettingsDialog.textInfoName": "Suggested signer", "Common.Views.SignSettingsDialog.textInfoTitle": "Suggested signer's title", "Common.Views.SignSettingsDialog.textInstructions": "<PERSON><PERSON><PERSON>", "Common.Views.SignSettingsDialog.textShowDate": "Näytä päivämäärä allekirjoitusrivillä", "Common.Views.SignSettingsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON><PERSON><PERSON>", "Common.Views.SignSettingsDialog.txtEmpty": "This field is required", "Common.Views.SymbolTableDialog.textCharacter": "Merk<PERSON>", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX value", "Common.Views.SymbolTableDialog.textCopyright": "Tekijänoikeusmer<PERSON>", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textDOQuote": "Opening double quote", "Common.Views.SymbolTableDialog.textEllipsis": "Horisontaalinen ellipsi", "Common.Views.SymbolTableDialog.textEmDash": "Ajatusviiva", "Common.Views.SymbolTableDialog.textEmSpace": "Em-väli", "Common.Views.SymbolTableDialog.textEnDash": "Yhdysmerk<PERSON>", "Common.Views.SymbolTableDialog.textEnSpace": "En-väli", "Common.Views.SymbolTableDialog.textFont": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBHyphen": "Sitova väliviiva", "Common.Views.SymbolTableDialog.textNBSpace": "<PERSON>ova välilyönti", "Common.Views.SymbolTableDialog.textPilcrow": "<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em välilyönti", "Common.Views.SymbolTableDialog.textRange": "Tietoalue", "Common.Views.SymbolTableDialog.textRecent": "Äskettäin k<PERSON>ytetyt symbolit", "Common.Views.SymbolTableDialog.textRegistered": "Rekisteröity tavaramerkki -symboli", "Common.Views.SymbolTableDialog.textSCQuote": "Yksittäisen lainauksen päätös", "Common.Views.SymbolTableDialog.textSection": "Pykälämerkki", "Common.Views.SymbolTableDialog.textShortcut": "Pi<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSHyphen": "Soft hyphen", "Common.Views.SymbolTableDialog.textSOQuote": "Opening single quote", "Common.Views.SymbolTableDialog.textSpecial": "Special characters", "Common.Views.SymbolTableDialog.textSymbols": "Symbolit", "Common.Views.SymbolTableDialog.textTitle": "Symboli", "Common.Views.SymbolTableDialog.textTradeMark": "Tavaramerkkisymboli", "Common.Views.UserNameDialog.textDontShow": "<PERSON><PERSON><PERSON> k<PERSON>", "Common.Views.UserNameDialog.textLabel": "<PERSON>nn<PERSON><PERSON>:", "Common.Views.UserNameDialog.textLabelError": "Label must not be empty.", "SSE.Controllers.DataTab.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textAddExternalData": "The link to an external source has been added. You can update such links in the Data tab.", "SSE.Controllers.DataTab.textColumns": "Columns", "SSE.Controllers.DataTab.textContinue": "Continue", "SSE.Controllers.DataTab.textDontUpdate": "Don't Update", "SSE.Controllers.DataTab.textEmptyUrl": "<PERSON>un tä<PERSON>y määritellä verkko-osoite.", "SSE.Controllers.DataTab.textRows": "Rivit", "SSE.Controllers.DataTab.textTurnOff": "Turn off AutoUpdate", "SSE.Controllers.DataTab.textUpdate": "Update", "SSE.Controllers.DataTab.textWizard": "Text to Columns", "SSE.Controllers.DataTab.txtDataValidation": "Data Validation", "SSE.Controllers.DataTab.txtErrorExternalLink": "Error: updating is failed", "SSE.Controllers.DataTab.txtExpand": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "The data next to the selection will not be removed. Do you want to expand the selection to include the adjacent data or continue with the currently selected cells only?", "SSE.Controllers.DataTab.txtExtendDataValidation": "The selection contains some cells without Data Validation settings.<br>Do you want to extend Data Validation to these cells?", "SSE.Controllers.DataTab.txtImportWizard": "Text Import Wizard", "SSE.Controllers.DataTab.txtRemDuplicates": "Remove Duplicates", "SSE.Controllers.DataTab.txtRemoveDataValidation": "The selection contains more than one type of validation.<br>Erase current settings and continue?", "SSE.Controllers.DataTab.txtRemSelected": "Remove in selected", "SSE.Controllers.DataTab.txtUrlTitle": "Liitä datan verkko-osoite", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "This workbook contains links to external sources which update automatically. This might be unsafe.<br><br>If you trust them, press Continue.", "SSE.Controllers.DataTab.warnUpdateExternalData": "This workbook contains links to one or more external sources that could be unsafe.<br>If you trust the links, update them to get the latest data.", "SSE.Controllers.DocumentHolder.alignmentText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.centerText": "Keskellä", "SSE.Controllers.DocumentHolder.deleteColumnText": "<PERSON>ista sarake", "SSE.Controllers.DocumentHolder.deleteRowText": "Poista rivi", "SSE.Controllers.DocumentHolder.deleteText": "Poista", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Linkin viittausta ei ole o<PERSON>. Ole hyvä ja korjaa linkki tai poista se.", "SSE.Controllers.DocumentHolder.guestText": "Vierailija", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON> sarake", "SSE.Controllers.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON> sarake", "SSE.Controllers.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertText": "Lisää", "SSE.Controllers.DocumentHolder.leftText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Varo<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "AutoCorrect options", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Sarak<PERSON><PERSON> Leveys {0} symbolit ({1} pikseliä)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "<PERSON><PERSON><PERSON> k<PERSON>us {0} p<PERSON><PERSON><PERSON> ({1} pik<PERSON>i<PERSON>)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Paina CTRL näppäintä ja klikkaa linkkiä", "SSE.Controllers.DocumentHolder.textInsertLeft": "Lisää vasemmalle", "SSE.Controllers.DocumentHolder.textInsertTop": "Lisää ylös", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Paste special", "SSE.Controllers.DocumentHolder.textStopExpand": "Stop automatically expanding tables", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "<PERSON><PERSON> k<PERSON>j<PERSON> on muokkaamassa tätä elementtiä. ", "SSE.Controllers.DocumentHolder.txtAboveAve": "Keskimääräistä enemmän", "SSE.Controllers.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON><PERSON> al<PERSON>", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Lisää murtoluku pylväs", "SSE.Controllers.DocumentHolder.txtAddHor": "Lisää vaakalinja", "SSE.Controllers.DocumentHolder.txtAddLB": "Lisää vasen alaviiva", "SSE.Controllers.DocumentHolder.txtAddLeft": "Lisää vasen reunus", "SSE.Controllers.DocumentHolder.txtAddLT": "Lisää vasen yläviiva", "SSE.Controllers.DocumentHolder.txtAddRight": "<PERSON>s<PERSON><PERSON> o<PERSON>a reunus", "SSE.Controllers.DocumentHolder.txtAddTop": "Lisää yläreunus", "SSE.Controllers.DocumentHolder.txtAddVer": "Lisää vertikaalinen linja", "SSE.Controllers.DocumentHolder.txtAlignToChar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAll": "(<PERSON><PERSON><PERSON>)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Returns the entire contents of the table or specified table columns including column headers, data and total rows", "SSE.Controllers.DocumentHolder.txtAnd": "and", "SSE.Controllers.DocumentHolder.txtBegins": "Begins with", "SSE.Controllers.DocumentHolder.txtBelowAve": "Below average", "SSE.Controllers.DocumentHolder.txtBlanks": "(Blanks)", "SSE.Controllers.DocumentHolder.txtBorderProps": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "SSE.Controllers.DocumentHolder.txtBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtByField": "%1 of %2", "SSE.Controllers.DocumentHolder.txtColumn": "Column", "SSE.Controllers.DocumentHolder.txtColumnAlign": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "SSE.Controllers.DocumentHolder.txtContains": "Contains", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Link copied to the clipboard", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Returns the data cells of the table or specified table columns", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Vähennä <PERSON>in kokoa", "SSE.Controllers.DocumentHolder.txtDeleteArg": "<PERSON><PERSON>ti", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Poista manuaalinen katkos", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Poista sisäänsulkevat kirjaimet", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Poista sisäänsulkevat kirjaimet ja erottimet", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Poista yhtälö", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "<PERSON>ista kirjain", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON> j<PERSON>", "SSE.Controllers.DocumentHolder.txtEnds": "Ends with", "SSE.Controllers.DocumentHolder.txtEquals": "Equals", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Equal to cell color", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Equal to font color", "SSE.Controllers.DocumentHolder.txtExpand": "Expand and sort", "SSE.Controllers.DocumentHolder.txtExpandSort": "The data next to the selection will not be sorted. Do you want to expand the selection to include the adjacent data or continue with sorting the currently selected cells only?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtFilterTop": "Yläosa", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Vaihda lineaariseen murtolukuun", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON><PERSON><PERSON> vinoutettuun murtolukuun", "SSE.Controllers.DocumentHolder.txtFractionStacked": "<PERSON><PERSON><PERSON><PERSON> pinottuun murtol<PERSON>un", "SSE.Controllers.DocumentHolder.txtGreater": "Greater than", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Greater than or equal to", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Returns the column headers for the table or specified table columns", "SSE.Controllers.DocumentHolder.txtHeight": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideBottom": "<PERSON><PERSON><PERSON> al<PERSON>us", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "<PERSON><PERSON><PERSON> raj<PERSON>", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "<PERSON><PERSON><PERSON> o<PERSON>", "SSE.Controllers.DocumentHolder.txtHideDegree": "<PERSON><PERSON><PERSON> aste", "SSE.Controllers.DocumentHolder.txtHideHor": "Piilota vaakaviiva", "SSE.Controllers.DocumentHolder.txtHideLB": "Piilota vasen alaviiva", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON> vasen reunus", "SSE.Controllers.DocumentHolder.txtHideLT": "Piilota vasen yläviiva", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Piilota vasen ha<PERSON>ulje", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "<PERSON><PERSON><PERSON> p<PERSON>", "SSE.Controllers.DocumentHolder.txtHideRight": "<PERSON><PERSON><PERSON> o<PERSON>a reunus", "SSE.Controllers.DocumentHolder.txtHideTop": "<PERSON><PERSON><PERSON> y<PERSON>", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "<PERSON><PERSON><PERSON> raj<PERSON>", "SSE.Controllers.DocumentHolder.txtHideVer": "Piilota vertikaalinen viiva", "SSE.Controllers.DocumentHolder.txtImportWizard": "Text Import Wizard", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Lisää argumentin kokoa", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Lisää <PERSON>ti jälk<PERSON>", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Lisää argumentti ennen", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Lisää manuaalinen katkaisu", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Lisää yhtälö jälkeen", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Lisää yhtälö ennen", "SSE.Controllers.DocumentHolder.txtItems": "items", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Säilytä pelkkä teksti", "SSE.Controllers.DocumentHolder.txtLess": "Vähemmä<PERSON> kuin", "SSE.Controllers.DocumentHolder.txtLessEquals": "Vähemmän kuin tai yhtäkuin", "SSE.Controllers.DocumentHolder.txtLimitChange": "<PERSON><PERSON> r<PERSON> si<PERSON>", "SSE.Controllers.DocumentHolder.txtLimitOver": "<PERSON><PERSON><PERSON> te<PERSON>", "SSE.Controllers.DocumentHolder.txtLimitUnder": "<PERSON><PERSON><PERSON> te<PERSON> alla", "SSE.Controllers.DocumentHolder.txtLockSort": "Data is found next to your selection, but you do not have sufficient permissions to change those cells.<br>Do you wish to continue with the current selection?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "<PERSON><PERSON><PERSON> argumentin k<PERSON>en", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>", "SSE.Controllers.DocumentHolder.txtNoChoices": "Ei ole vaiht<PERSON>htoja solun täyttämiseksi.<br><PERSON><PERSON> sarakkeen tekstiarvoja voidaan valita tai korvata.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Does not begin with", "SSE.Controllers.DocumentHolder.txtNotContains": "Does not contain", "SSE.Controllers.DocumentHolder.txtNotEnds": "Does not end with", "SSE.Controllers.DocumentHolder.txtNotEquals": "Does not equal", "SSE.Controllers.DocumentHolder.txtOr": "tai", "SSE.Controllers.DocumentHolder.txtOverbar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPaste": "Liit<PERSON>", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formula without borders", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formula + column width", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Destination formatting", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Paste only formatting", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formula + number format", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Paste only formula", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formula + all formatting", "SSE.Controllers.DocumentHolder.txtPasteLink": "Paste link", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Linked picture", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Merge conditional formatting", "SSE.Controllers.DocumentHolder.txtPastePicture": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Source formatting", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Transpose", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Value + all formatting", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Value + number format", "SSE.Controllers.DocumentHolder.txtPasteValues": "Paste only value", "SSE.Controllers.DocumentHolder.txtPercent": "prosent<PERSON>", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Redo table autoexpansion", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Poista murtoluku pylväs", "SSE.Controllers.DocumentHolder.txtRemLimit": "<PERSON><PERSON> r<PERSON>", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Poista kirjainaksentti ", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Poista pylväs", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "<PERSON><PERSON><PERSON><PERSON> poistaa tämän allekirjoit<PERSON>?<br><PERSON><PERSON><PERSON><PERSON> ei voi kumota.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Poista skriptit", "SSE.Controllers.DocumentHolder.txtRemSubscript": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Poista yläindeksi", "SSE.Controllers.DocumentHolder.txtRowHeight": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Skriptit tekstin j<PERSON>", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Skriptit ennen tekstiä", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "<PERSON>äyt<PERSON> raj<PERSON>", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Näytä oikea ha<PERSON>je", "SSE.Controllers.DocumentHolder.txtShowDegree": "Näytä aste", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Näytä vasen ha<PERSON>je", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Näytä paikkamerkki", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Näytä yläraj<PERSON>us", "SSE.Controllers.DocumentHolder.txtSorting": "Sorting", "SSE.Controllers.DocumentHolder.txtSortSelected": "Sort selected", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Venyvät Hakasulkeet", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Choose only this row of the specified column", "SSE.Controllers.DocumentHolder.txtTop": "Yläosa", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Returns the total rows for the table or specified table columns", "SSE.Controllers.DocumentHolder.txtUnderbar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Undo table autoexpansion", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Use text import wizard", "SSE.Controllers.DocumentHolder.txtWarnUrl": "<PERSON><PERSON><PERSON><PERSON><PERSON> linkin avaaminen voi vahingoittaa laitetta ja tietoja.<br><PERSON><PERSON><PERSON><PERSON> var<PERSON> jat<PERSON>?", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.warnFilterError": "You need at least one field in the Values area in order to apply a value filter.", "SSE.Controllers.FormulaDialog.sCategoryAll": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Muka<PERSON>ttu", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Tietokanta", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Pvm & Kellonaika", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Engineering", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Financial", "SSE.Controllers.FormulaDialog.sCategoryInformation": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 last used", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logical", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Lookup & Reference", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Math & Trig", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistical", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Text & Data", "SSE.Controllers.LeftMenu.newDocumentTitle": "Laskutaulukko on ilman nimeä", "SSE.Controllers.LeftMenu.textByColumns": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textByRows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textFormulas": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textItemEntireCell": "<PERSON><PERSON> solun si<PERSON>", "SSE.Controllers.LeftMenu.textLoadHistory": "Ladataan versioiden historiaa...", "SSE.Controllers.LeftMenu.textLookin": "<PERSON><PERSON> t<PERSON>:", "SSE.Controllers.LeftMenu.textNoTextFound": "Etsimääsi tietoa ei l<PERSON>. Ole hyvä ja muokkaa hakuva<PERSON>eh<PERSON>.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "<PERSON><PERSON><PERSON> on suoritettu. {0} tap<PERSON><PERSON> oh<PERSON>.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "<PERSON><PERSON> on suoritettu. Korvaustapahtumia: {0}", "SSE.Controllers.LeftMenu.textSave": "Save", "SSE.Controllers.LeftMenu.textSearch": "Etsi", "SSE.Controllers.LeftMenu.textSelectPath": "Syötä uusi nimi tallentaa<PERSON> kopion tiedostosta", "SSE.Controllers.LeftMenu.textSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textValues": "Arvot", "SSE.Controllers.LeftMenu.textWarning": "Varo<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWithin": "Sisällä", "SSE.Controllers.LeftMenu.textWorkbook": "Työkirja", "SSE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.warnDownloadAs": "<PERSON>s jatkat tässä muo<PERSON>, niin kaikki o<PERSON>, p<PERSON><PERSON> te<PERSON>, men<PERSON><PERSON><PERSON><PERSON><PERSON>.<br><PERSON><PERSON><PERSON> varma että haluat jatkaa?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "The CSV format does not support saving a multi-sheet file.<br>To keep the selected format and save only the current sheet, press Save.<br>To save the current spreadsheet, click Cancel and save it in a different format.", "SSE.Controllers.Main.confirmAddCellWatches": "This action will add {0} cell watches.<br>Do you want to continue?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "This action will add only {0} cell watches by memory save reason.<br>Do you want to continue?", "SSE.Controllers.Main.confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "SSE.Controllers.Main.confirmMoveCellRange": "Kohdesolujen tietoalue voi sisältää tietoa. Haluatko jatkaa toimintoa?", "SSE.Controllers.Main.confirmPutMergeRange": "Lähdetiedot sisälsivät yhdistettyjä soluja.<br>Ne on purettu ennenkuin ne liitettiin tauluk<PERSON>on.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Formulas in the header row will be removed and converted to static text.<br>Do you want to continue?", "SSE.Controllers.Main.confirmReplaceHFPicture": "Only one picture can be inserted in each section of the header.<br>Press \"Replace\" to replace existing picture.<br>Press \"Keep\" to keep existing picture.", "SSE.Controllers.Main.convertationTimeoutText": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON><PERSON><PERSON> \"OK\" niin voit palata asiakirjaluetteloon.", "SSE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON> e<PERSON>.", "SSE.Controllers.Main.downloadTextText": "Ladataan työkirjaa...", "SSE.Controllers.Main.downloadTitleText": "Ladataan työkirjaa", "SSE.Controllers.Main.errNoDuplicates": "No duplicate values found.", "SSE.Controllers.Main.errorAccessDeny": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> to<PERSON>, johon sinulla ei ole k<PERSON>töoikeuk<PERSON>.<br>Ole hyvä ja ota yhteyttä asiakirjan palvelimen pääkäyttäjään.", "SSE.Controllers.Main.errorArgsRange": "<PERSON><PERSON><PERSON> s<PERSON> ka<PERSON>.<br><PERSON><PERSON><PERSON><PERSON> vir<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>.", "SSE.Controllers.Main.errorAutoFilterChange": "Toiminto ei ole sallittu, koska se yrittää vaihtaa soluja työkirjasi tauluk<PERSON>on.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Toimintoa ei voida suorittaa valituille soluille koska et voi siirtää taulukon osaa.<bR><PERSON><PERSON><PERSON> toinen tietoalue niin että koko taulukko siirretään ja yritä uudelleen.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Toimintoa ei voida suorittaa valitulle solujen tietoalueelle.<br><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> tietoalue, joka eroa<PERSON>, ja yrit<PERSON> uude<PERSON>.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Toimintoa ei voida suorittaa, koska alue sisältää suodatettuja soluja.<br>Ole hyvä ja näytä suodatetut elementit ja yritä uudelleen.", "SSE.Controllers.Main.errorBadImageUrl": "<PERSON><PERSON> ve<PERSON>-osoite on virheellinen", "SSE.Controllers.Main.errorCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the spreadsheet.", "SSE.Controllers.Main.errorCannotUngroup": "Cannot ungroup. To start an outline, select the detail rows or columns and group them.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "You cannot use this command on a protected sheet. To use this command, unprotect the sheet.<br>You might be requested to enter a password.", "SSE.Controllers.Main.errorChangeArray": "You cannot change part of an array.", "SSE.Controllers.Main.errorChangeFilteredRange": "This will change a filtered range on your worksheet.<br>To complete this task, please remove AutoFilters.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "The cell or chart you are trying to change is on a protected sheet.<br>To make a change, unprotect the sheet. You might be requested to enter a password.", "SSE.Controllers.Main.errorCircularReference": "There are one or more circular references where a formula refers to its own cell either directly or indirectly.<br>Try removing or changing these references, or moving the formulas to different cells.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Palvelimen yhteys menetetty. Asiakirjaa ei voida tällä hetkellä muokata.", "SSE.Controllers.Main.errorConnectToServer": "Asiakirjaa ei voitu tallentaa. Ole hyvä ja tarkista yhteysasetukset tai ota yhteyttä pääkäyttäjään.<br>Ku<PERSON> klikkaat 'OK' painiketta, sinua pyydetään lataamaan asiakirja.", "SSE.Controllers.Main.errorConvertXml": "The file has an unsupported format.<br>Only XML Spreadsheet 2003 format can be used.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Tätä komentoa ei voida käyttää useisiin valintoihin.<br>Valitse yksi tietoalue ja yritä uudelleen.", "SSE.Controllers.Main.errorCountArg": "<PERSON><PERSON>he s<PERSON>ötety<PERSON> kaavassa.<br><PERSON><PERSON><PERSON><PERSON> väärää argumenttien määrää.", "SSE.Controllers.Main.errorCountArgExceed": "<PERSON><PERSON><PERSON> s<PERSON> ka<PERSON>.<br><PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON> y<PERSON>.", "SSE.Controllers.Main.errorCreateDefName": "Olemassaolevia nimettyjä tietoalueita ei voida muokata ja uusia ei voida luoda<br>tällä hetkellä koska jotain niistä muokataan tällä hetkellä.", "SSE.Controllers.Main.errorCreateRange": "The existing ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON><PERSON><PERSON> virhe.<br>Tietokannan yhteysvirhe. Ole hyvä ja ota yhteyttä asiakastukeen, jos virhe tuntuu pysyvän.", "SSE.Controllers.Main.errorDataEncrypted": "On tullut salattuja muuto<PERSON>, joita ei pystytä tulkitsemaan.", "SSE.Controllers.Main.errorDataRange": "<PERSON><PERSON><PERSON><PERSON><PERSON> tie<PERSON>", "SSE.Controllers.Main.errorDataValidate": "The value you entered is not valid.<br>A user has restricted values that can be entered into this cell.", "SSE.Controllers.Main.errorDefaultMessage": "Virhekoodi: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "You are trying to delete a column that contains a locked cell. Locked cells cannot be deleted while the worksheet is protected.<br>To delete a locked cell, unprotect the sheet. You might be requested to enter a password.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "You are trying to delete a row that contains a locked cell. Locked cells cannot be deleted while the worksheet is protected.<br>To delete a locked cell, unprotect the sheet. You might be requested to enter a password.", "SSE.Controllers.Main.errorDependentsNoFormulas": "The Trace Dependents command found no formulas that refer to the active cell.", "SSE.Controllers.Main.errorDirectUrl": "Ole hyvä ja tarkasta asia<PERSON><PERSON><PERSON> link<PERSON>.<br>Sen tulee olla suora linkki ladattavaan asia<PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.errorEditingDownloadas": "Tapaht<PERSON> virhe as<PERSON><PERSON><PERSON><PERSON> k<PERSON> a<PERSON>.<br> <PERSON><PERSON><PERSON><PERSON> 'Tallenna muodossa' -to<PERSON><PERSON><PERSON> luo<PERSON> tiedostosta paikallisen varmuuskopion.", "SSE.Controllers.Main.errorEditingSaveas": "Tapaht<PERSON> virhe asiakir<PERSON>.<br><PERSON><PERSON>yt<PERSON> '<PERSON><PERSON><PERSON>...' -to<PERSON><PERSON><PERSON> l<PERSON> tiedostosta paikallisen varmuuskopion", "SSE.Controllers.Main.errorEditView": "The existing sheet view cannot be edited and the new ones cannot be created at the moment as some of them are being edited.", "SSE.Controllers.Main.errorEmailClient": "Sähköpostiohjelmaa ei löydetty.", "SSE.Controllers.Main.errorFilePassProtect": "<PERSON><PERSON><PERSON><PERSON> on suojattu salasanalla ja sitä ei voitu avata.", "SSE.Controllers.Main.errorFileRequest": "<PERSON><PERSON><PERSON><PERSON> virhe.<br><PERSON>ied<PERSON><PERSON> py<PERSON><PERSON>n virhe. Ole hyvä ja ota yhteyttä asiakastukeen, jos vir<PERSON><PERSON><PERSON> jatku<PERSON>.", "SSE.Controllers.Main.errorFileSizeExceed": "Tiedoston koko ylittää palvelimelle asetetun rajan.<br><PERSON><PERSON> yhteytt<PERSON> asiakirjapalvelimen ylläpitäjään saadaksesi lisätietoja.", "SSE.Controllers.Main.errorFileVKey": "<PERSON><PERSON><PERSON><PERSON> virhe.<br><PERSON><PERSON><PERSON><PERSON><PERSON> turva-avain. Ole hyvä ja ota yhteyttä asiakastukeen, jos vir<PERSON><PERSON><PERSON> jatku<PERSON>.", "SSE.Controllers.Main.errorFillRange": "Ei voitu täyttää valittua solualuetta.<br><PERSON><PERSON><PERSON> y<PERSON>ste<PERSON>t solut tulee olla samaa kokoa.", "SSE.Controllers.Main.errorForceSave": "Tapaht<PERSON> virhe asiakir<PERSON>a tallennetta<PERSON>a. Käytä 'Lataa muodossa' -toimintoa tallentaaksesi tiedoston levylle tai yritä my<PERSON><PERSON><PERSON> u<PERSON>.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Cannot enter a formula for an item or field name in a pivot table report.", "SSE.Controllers.Main.errorFormulaName": "<PERSON><PERSON><PERSON> s<PERSON> kaavassa.<br><PERSON><PERSON><PERSON><PERSON> virheellistä kaavan nimeä.", "SSE.Controllers.Main.errorFormulaParsing": "<PERSON>säinen virhe jäsentäess<PERSON> kaavaa", "SSE.Controllers.Main.errorFrmlMaxLength": "The length of your formula exceeds the limit of 8192 characters.<br>Please edit it and try again.", "SSE.Controllers.Main.errorFrmlMaxReference": "You cannot enter this formula because it has too many values,<br>cell references, and/or names.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Text values in formulas are limited to 255 characters.<br>Use the CONCATENATE function or concatenation operator (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Funktio vii<PERSON><PERSON>, jota ei ole o<PERSON>.<br>Ole hyvä ja tarkista tiedot ja yritä uudelleen.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Toimintoa ei voitu suorittaa valitulle solun tietoalueelle.<br>Valitse tietoalue niin että ensimmäinen taulukon rivi on samalla rivillä<br>ja t<PERSON><PERSON><PERSON><PERSON><PERSON> on limitt<PERSON>in nykyisen kanssa.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Toimintoa ei voitu suorittaa valitulle solun tietoalueelle.<br>Valitse tietoalue mikä ei sisällä muita taulukkoja.", "SSE.Controllers.Main.errorInconsistentExt": "Tapahtui virhe avatessa tiedostoa.<br>Tiedoston sisältö ei vastaa tiedostopäätettä.", "SSE.Controllers.Main.errorInconsistentExtDocx": "Tapahtui virhe avatessa tiedostoa.<br>Tiedoston sisältö viitta<PERSON> te<PERSON><PERSON><PERSON><PERSON> (esim. .docx), mutta tied<PERSON><PERSON><PERSON> on virheellinen: %1", "SSE.Controllers.Main.errorInconsistentExtPdf": "Tapahtui virhe avatessa tiedostoa.<br>Tiedoston sisältö viittaa johonkin näistä formaateista: pdf/djvu/xps/oxps, mutta tiedostop<PERSON> on virheellinen: %1", "SSE.Controllers.Main.errorInconsistentExtPptx": "Tapahtui virhe avatessa tiedostoa.<br>Tiedoston sisältö viitta<PERSON> diaesity<PERSON> (esim. .pptx), mutta tied<PERSON><PERSON><PERSON> on virheellinen: %1", "SSE.Controllers.Main.errorInconsistentExtXlsx": "Tapahtui virhe avatessa tiedostoa.<br>Tiedoston sisältö vii<PERSON><PERSON> (esim. .xlsx), mutta tied<PERSON><PERSON> on virheellinen: %1.", "SSE.Controllers.Main.errorInvalidRef": "Syötä oikea nimi valinnalle tai oikea viittaus siirtym<PERSON>en.", "SSE.Controllers.Main.errorKeyEncrypt": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "SSE.Controllers.Main.errorKeyExpire": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.errorLabledColumnsPivot": "To create a pivot table, use data that is organized as a list with labeled columns.", "SSE.Controllers.Main.errorLoadingFont": "Fontteja ei ole ladattu.<br><PERSON><PERSON>ht<PERSON> asiakirjapalvelimen ylläpitäjään.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "The reference for the location or data range is not valid.", "SSE.Controllers.Main.errorLockedAll": "Toimintoa ei voida suorittaa koska taulukko on toisen käyttäjän lukitsema.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "One of the cells involved in the goal seek process has been modified by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "You cannot change data inside a pivot table.", "SSE.Controllers.Main.errorLockedWorksheetRename": "Taulukkoa ei voida nimetä uudelleen, koska sitä ollaan nimeämässä uudelleen toisen käyttäjän toimesta", "SSE.Controllers.Main.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Controllers.Main.errorMoveRange": "Ei voida muuttaa yhdi<PERSON>tyn solun osaa", "SSE.Controllers.Main.errorMoveSlicerError": "Table slicers cannot be copied from one workbook to another.<br>Try again by selecting the entire table and the slicers.", "SSE.Controllers.Main.errorMultiCellFormula": "Multi-cell array formulas are not allowed in tables.", "SSE.Controllers.Main.errorNoDataToParse": "No data was selected to parse.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.Main.errorOpenWarning": "<PERSON><PERSON><PERSON> kaavan pituus tied<PERSON><PERSON> on ylittänyt<br>salli<PERSON> merk<PERSON>r<PERSON>n ja se poiste<PERSON>in.", "SSE.Controllers.Main.errorOperandExpected": "Syötetty funktio ei ole o<PERSON>. Ole hyvä ja tarkista jos puuttuu yksi suluista - '(' tai ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "<PERSON><PERSON><PERSON> on väärä.<br>Varmista, ettei CAPS LOCK ole päällä ja että käytät oikeaa kirjainkokoa.", "SSE.Controllers.Main.errorPasteInPivot": "We can't make this change for the selected cells because it will affect a pivot table.<br>Use the field list to change the report.", "SSE.Controllers.Main.errorPasteMaxRange": "Ko<PERSON><PERSON>in ja liittämisen alueet eivät vastaa toisiaan.<br>Ole hyvä ja valitse alue, jolla on sama koko tai klikkaa rivin ensimmäistä solua, josta alkaen liität kopioitavat solut.", "SSE.Controllers.Main.errorPasteMultiSelect": "This action cannot be done on a multiple range selection.<br>Select a single range and try again.", "SSE.Controllers.Main.errorPasteSlicerError": "Table slicers cannot be copied from one workbook to another.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Pivot table field name already exists.", "SSE.Controllers.Main.errorPivotGroup": "<PERSON><PERSON> group that selection.", "SSE.Controllers.Main.errorPivotOverlap": "A pivot table report cannot overlap a table.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "The Pivot Table report was saved without the underlying data.<br>Use the 'Refresh' button to update the report.", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "The Trace Precedents command requires that the active cell contain a formula which includes a valid references.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Valitettavasti ei ole mahdollista tulostaa enempää kuin 1500 sivua kerralla nykyisessä ohjelman versiossa.<br>Tämä rajoitus tulee poistumaan tulevissa versioissa.", "SSE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON> e<PERSON>", "SSE.Controllers.Main.errorProtectedRange": "This range is not allowed for editing.", "SSE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "SSE.Controllers.Main.errorServerVersion": "The editor version has been updated. The page will be reloaded to apply the changes.", "SSE.Controllers.Main.errorSessionAbsolute": "<PERSON><PERSON><PERSON><PERSON> istunnon aika on erääntynyt. Ole hyvä ja lataa uudelleen sivu.", "SSE.Controllers.Main.errorSessionIdle": "Asiakirjaa ei ole muokattu pitk<PERSON>än a<PERSON>an. Ole hyvä ja lataa sivu uudelleen.", "SSE.Controllers.Main.errorSessionToken": "<PERSON><PERSON><PERSON><PERSON> pal<PERSON> on keskeytynyt. Ole hyvä ja lataa uudelleen sivu.", "SSE.Controllers.Main.errorSetPassword": "<PERSON><PERSON>nan asettaminen ei onnistunut.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Location reference is not valid because the cells are not all in the same column or row.<br>Select cells that are all in a single column or row.", "SSE.Controllers.Main.errorStockChart": "Virheellinen rivin järjestys. Jotta voit luoda pörssikaavion, niin aseta tiedot seuraavassa järjestyksessä: <br> a<PERSON><PERSON><PERSON><PERSON>, kor<PERSON><PERSON> hinta, halvin hinta, sulk<PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.errorToken": "Asiakirjan turvatunnus ei ole oikeassa muodossa.<br>Ole hyvä ja ota yhteyttä asiakirjan palvelimen pääkäyttäjään.", "SSE.Controllers.Main.errorTokenExpire": "Asiakirjan turvatun<PERSON> on erääntynyt.<br>Ole hyvä ja ota yhteyttä asiakirjan palvelimen pääkäyttäjään.", "SSE.Controllers.Main.errorUnexpectedGuid": "Ulk<PERSON>nen virhe.<br>Odottamaton GUID arvo. Ole hyvä ja ota yhteyttä asiakastukeen, jos vir<PERSON><PERSON><PERSON> jatku<PERSON>.", "SSE.Controllers.Main.errorUpdateVersion": "Tiedoston versio on muuttunut. Sivu ladataan uudelleen.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.", "SSE.Controllers.Main.errorUserDrop": "Tiedostoon ei ole pääsyä tällä hetkellä.", "SSE.Controllers.Main.errorUsersExceed": "Palvelupaketin sallittu käyttäjämäärä on ylitetty", "SSE.Controllers.Main.errorViewerDisconnect": "Yhteys on menetetty. Voit vielä selailla as<PERSON>,<br>mutta et voi ladata tai tulostaa sitä ennenkuin yhteys on palautettu.", "SSE.Controllers.Main.errorWrongBracketsCount": "<PERSON><PERSON><PERSON> s<PERSON> ka<PERSON>.<br><PERSON><PERSON><PERSON><PERSON> virheellinen määrä <PERSON>.", "SSE.Controllers.Main.errorWrongOperator": "Virhe syötetyssä kaavassa. Käytetty väärää operaattoria.<br>Ole hyvä ja korjaa virhe.", "SSE.Controllers.Main.errorWrongPassword": "The password you supplied is not correct.", "SSE.Controllers.Main.errRemDuplicates": "Duplicate values found and deleted: {0}, unique values left: {1}.", "SSE.Controllers.Main.leavePageText": "<PERSON><PERSON><PERSON> on tallentamattomia muutoksia tässä taulukossa. Klikkaa '<PERSON><PERSON><PERSON> tälle sivulle', ja sitten 'Tall<PERSON><PERSON>', jotta voit tallentaa muutokset. Klikkaa 'Jätä tämä sivu' niin voit jättää huomioimatta kaikki tallentamattomat muutokset.   ", "SSE.Controllers.Main.leavePageTextOnClose": "All unsaved changes in this spreadsheet will be lost.<br> Click \"Cancel\" then \"Save\" to save them. Click \"OK\" to discard all the unsaved changes.", "SSE.Controllers.Main.loadFontsTextText": "Ladataan tietoa...", "SSE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadFontTextText": "Ladataan tietoa...", "SSE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadImagesTextText": "Ladataan kuvia...", "SSE.Controllers.Main.loadImagesTitleText": "Ladataan kuvia", "SSE.Controllers.Main.loadImageTextText": "<PERSON><PERSON><PERSON> kuvaa...", "SSE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON><PERSON> kuvaa", "SSE.Controllers.Main.loadingDocumentTitleText": "Ladataan työkirjaa", "SSE.Controllers.Main.notcriticalErrorTitle": "Varo<PERSON><PERSON>", "SSE.Controllers.Main.openErrorText": "<PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.openTextText": "Avataan ta<PERSON>k<PERSON>laskentaohjelmaa...", "SSE.Controllers.Main.openTitleText": "Avataan ta<PERSON>las<PERSON>oh<PERSON>a", "SSE.Controllers.Main.pastInMergeAreaError": "Ei voida muuttaa yhdi<PERSON>tyn solun osaa", "SSE.Controllers.Main.printTextText": "Tulostetaan <PERSON>k<PERSON>a...", "SSE.Controllers.Main.printTitleText": "Tuloste<PERSON>an <PERSON>", "SSE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON> u<PERSON> sivu", "SSE.Controllers.Main.requestEditFailedMessageText": "<PERSON><PERSON> on paraikaa muokkaamassa tätä asiakirjaa. Ole hyvä ja yritä myö<PERSON> uudelleen.", "SSE.Controllers.Main.requestEditFailedTitleText": "Käyttö estetty. ", "SSE.Controllers.Main.saveErrorText": "<PERSON><PERSON><PERSON> tall<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.saveErrorTextDesktop": "<PERSON><PERSON><PERSON> tiedostoa ei voida tallentaa tai luoda.<br><PERSON><PERSON><PERSON><PERSON><PERSON> syit<PERSON>: <br>1. <PERSON><PERSON><PERSON><PERSON> on vain-luku -tilassa. <br>2. <PERSON><PERSON><PERSON><PERSON> on käytössä toisella käyttäjällä. <br>3. <PERSON><PERSON><PERSON><PERSON> on täysi tai vioittunut.", "SSE.Controllers.Main.saveTextText": "Tallennetaan <PERSON>...", "SSE.Controllers.Main.saveTitleText": "Tallenne<PERSON>an <PERSON>", "SSE.Controllers.Main.scriptLoadError": "The connection is too slow, some of the components could not be loaded. Please reload the page.", "SSE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textApplyAll": "Käytä kaikkiin y<PERSON>älöihin", "SSE.Controllers.Main.textBuyNow": "<PERSON><PERSON><PERSON> sivu<PERSON>", "SSE.Controllers.Main.textChangesSaved": "Kaikki muutoks<PERSON> tallennettu", "SSE.Controllers.Main.textClose": "Sulje", "SSE.Controllers.Main.textCloseTip": "Klikkaa ja sulje vinkki", "SSE.Controllers.Main.textConfirm": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "SSE.Controllers.Main.textContactUs": "<PERSON><PERSON> yht<PERSON> my<PERSON>", "SSE.Controllers.Main.textContinue": "Continue", "SSE.Controllers.Main.textConvertEquation": "<PERSON>äm<PERSON> yhtäl<PERSON> on luotu yhtälötyökalun vanhemmal<PERSON> versiolla, jota ei enää tueta. <PERSON><PERSON> haluat muokata sitä, se täytyy ensin muuntaa Office Math ML -muotoon.<br><PERSON><PERSON><PERSON><PERSON><PERSON>? ", "SSE.Controllers.Main.textCustomLoader": "Please note that according to the terms of the license you are not entitled to change the loader.<br>Please contact our Sales Department to get a quote.", "SSE.Controllers.Main.textDisconnect": "Yhteys on katkennut", "SSE.Controllers.Main.textFillOtherRows": "Fill other rows", "SSE.Controllers.Main.textFormulaFilledAllRows": "Formula filled {0} rows have data. Filling other empty rows may take a few minutes.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Formula filled first {0} rows. Filling other empty rows may take a few minutes.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Formula filled only first {0} rows have data by memory save reason. There are other {1} rows have data in this sheet. You can fill them manually.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Formula filled only first {0} rows by memory save reason. Other rows in this sheet don't have data.", "SSE.Controllers.Main.textGuest": "Vierailija", "SSE.Controllers.Main.textHasMacros": "The file contains automatic macros.<br>Do you want to run macros?", "SSE.Controllers.Main.textKeep": "Keep", "SSE.Controllers.Main.textLearnMore": "<PERSON><PERSON> l<PERSON>", "SSE.Controllers.Main.textLoadingDocument": "Ladataan työkirjaa", "SSE.Controllers.Main.textLongName": "<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON>, jonka pituus on alle 128 merkkiä.", "SSE.Controllers.Main.textNeedSynchronize": "<PERSON><PERSON><PERSON> on päivityksiä", "SSE.Controllers.Main.textNo": "<PERSON>i", "SSE.Controllers.Main.textNoLicenseTitle": "ONLYOFFICE avoimen lähdekoodin versio", "SSE.Controllers.Main.textPaidFeature": "<PERSON><PERSON><PERSON><PERSON> toim<PERSON>o", "SSE.Controllers.Main.textPleaseWait": "Toiminto voi kestää odotettua kauemmin. Ole hyvä ja odota....", "SSE.Controllers.Main.textReconnect": "Yhteys on palautettu", "SSE.Controllers.Main.textRemember": "Muista valintani koskevan kaik<PERSON>a <PERSON>", "SSE.Controllers.Main.textRememberMacros": "Muista valintani koskevan kaik<PERSON>a makroja", "SSE.Controllers.Main.textRenameError": "Käyttäjänimi ei voi olla tyhjä.", "SSE.Controllers.Main.textRenameLabel": "Syötä nimi, jota haluat käyttää yhteistoiminnassa", "SSE.Controllers.Main.textReplace": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textRequestMacros": "Makro yrittää lähettää pyynnön URL-osoitteeseen. Haluatko sallia pyynnön lähettämisen kohteeseen %1?", "SSE.Controllers.Main.textShape": "<PERSON><PERSON>", "SSE.Controllers.Main.textStrict": "<PERSON><PERSON><PERSON><PERSON> tila", "SSE.Controllers.Main.textText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textTryQuickPrint": "<PERSON><PERSON> vali<PERSON>ut Pikatulostuksen: <PERSON><PERSON> as<PERSON>kir<PERSON> tulostetaan käyttäen viimeksi valittua tai oletustulostinta.<br><PERSON><PERSON><PERSON><PERSON> jat<PERSON>?", "SSE.Controllers.Main.textTryUndoRedo": "Peruutus/tee uudelleen -toiminnot eivät ole käytössä yhteismuokkauksen pikatilassa.<br><PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON>\" tilan painiketta, jotta voit vaihtaa ehdottomaan yhteismuokkauksen tilaan tai muokata tiedostoa ilman että muut käyttäjät häiritsevät sitä. Tässä tilassa lähetät muutokset vain kun olet tallentanut ne. Voit vaihdella yhteismuokkauksen tilojen välillä editorin Laajennetuissa asetuksissa.", "SSE.Controllers.Main.textTryUndoRedoWarn": "The Undo/Redo functions are disabled for the Fast co-editing mode.", "SSE.Controllers.Main.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "SSE.Controllers.Main.textUpdating": "Updating", "SSE.Controllers.Main.textYes": "K<PERSON><PERSON>ä", "SSE.Controllers.Main.titleLicenseExp": "Lisenssi <PERSON>", "SSE.Controllers.Main.titleLicenseNotActive": "Lisenssi ei ole aktiivinen", "SSE.Controllers.Main.titleServerVersion": "Editori päivitetty", "SSE.Controllers.Main.titleUpdateVersion": "Version changed", "SSE.Controllers.Main.txtAccent": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtAll": "(<PERSON><PERSON><PERSON>)", "SSE.Controllers.Main.txtArt": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>n", "SSE.Controllers.Main.txtBasicShapes": "Perusmuodot", "SSE.Controllers.Main.txtBlank": "(blank)", "SSE.Controllers.Main.txtButtons": "Painikkeet", "SSE.Controllers.Main.txtByField": "%1 of %2", "SSE.Controllers.Main.txtCallouts": "Huomiotekstit", "SSE.Controllers.Main.txtCharts": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtClearFilter": "Poista suoda<PERSON>", "SSE.Controllers.Main.txtColLbls": "Column labels", "SSE.Controllers.Main.txtColumn": "Column", "SSE.Controllers.Main.txtConfidential": "Confidential", "SSE.Controllers.Main.txtDate": "Date", "SSE.Controllers.Main.txtDays": "Days", "SSE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtEditingMode": "<PERSON><PERSON> muo<PERSON> tila...", "SSE.Controllers.Main.txtErrorLoadHistory": "Historian <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtFile": "Tiedosto", "SSE.Controllers.Main.txtGrandTotal": "Grand Total", "SSE.Controllers.Main.txtGroup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtHours": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtInfo": "Info", "SSE.Controllers.Main.txtLines": "Viivat", "SSE.Controllers.Main.txtMath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMinutes": "Minutes", "SSE.Controllers.Main.txtMonths": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMultiSelect": "Multi-Select", "SSE.Controllers.Main.txtNone": "None", "SSE.Controllers.Main.txtOr": "%1 or %2", "SSE.Controllers.Main.txtPage": "<PERSON><PERSON>", "SSE.Controllers.Main.txtPageOf": "Page %1 of %2", "SSE.Controllers.Main.txtPages": "Pages", "SSE.Controllers.Main.txtPicture": "<PERSON><PERSON>", "SSE.Controllers.Main.txtPivotTable": "PivotTable", "SSE.Controllers.Main.txtPreparedBy": "Prepared by", "SSE.Controllers.Main.txtPrintArea": "Print_Area", "SSE.Controllers.Main.txtQuarter": "Qtr", "SSE.Controllers.Main.txtQuarters": "Quarters", "SSE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRow": "Row", "SSE.Controllers.Main.txtRowLbls": "Row Labels", "SSE.Controllers.Main.txtSaveCopyAsComplete": "Tiedoston kopio tallennettiin onnist<PERSON>.", "SSE.Controllers.Main.txtScheme_Aspect": "Aspect", "SSE.Controllers.Main.txtScheme_Blue": "<PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Blue_Green": "Sinivihreä", "SSE.Controllers.Main.txtScheme_Blue_II": "Sininen II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Grayscale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Green": "Vihreä", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Vihreänkeltainen", "SSE.Controllers.Main.txtScheme_Marquee": "Merk<PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Median": "<PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Orans<PERSON>", "SSE.Controllers.Main.txtScheme_Orange_Red": "Oranssinpunainen", "SSE.Controllers.Main.txtScheme_Paper": "<PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Red": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Red_Orange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Red_Violet": "Punavioletti", "SSE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "SSE.Controllers.Main.txtScheme_Violet": "<PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Violet_II": "Violetti II", "SSE.Controllers.Main.txtScheme_Yellow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSeconds": "Seconds", "SSE.Controllers.Main.txtSeries": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Line Callout 1 (Border and Accent Bar)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Line Callout 2 (Border and Accent Bar)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Line Callout 3 (Border and Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout1": "Line Callout 1 (Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout2": "Line Callout 2 (Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout3": "Line Callout 3 (Accent Bar)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Back or previous button", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Aloita -painike", "SSE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Asiakirja-pain<PERSON>", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Lopeta-pain<PERSON>", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Forward or next button", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Help-painike", "SSE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Lisätietoja-painike", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Movie Button", "SSE.Controllers.Main.txtShape_actionButtonReturn": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonSound": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_arc": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentArrow": "Taivutettu nuoli", "SSE.Controllers.Main.txtShape_bentConnector5": "Kulmaliitäntä", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Kulmaliitäntä nuolella", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Kulmaliitäntä tuplanuolella", "SSE.Controllers.Main.txtShape_bentUpArrow": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>u nuoli", "SSE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_blockArc": "Lohkokaari", "SSE.Controllers.Main.txtShape_borderCallout1": "Line Callout 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Line Callout 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Line Callout 3", "SSE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_callout1": "Line Callout 1 (No Border)", "SSE.Controllers.Main.txtShape_callout2": "Line Callout 2 (No Border)", "SSE.Controllers.Main.txtShape_callout3": "Line Callout 3 (No Border)", "SSE.Controllers.Main.txtShape_can": "Can", "SSE.Controllers.Main.txtShape_chevron": "Natsa", "SSE.Controllers.Main.txtShape_chord": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_circularArrow": "Ympyrän<PERSON><PERSON><PERSON> nuoli", "SSE.Controllers.Main.txtShape_cloud": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cloudCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_corner": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "<PERSON><PERSON><PERSON> lii<PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Kaartuvat nuolet", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "<PERSON><PERSON>va liitin: ka<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedDownArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON> kaartuva nuoli", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "<PERSON><PERSON><PERSON><PERSON> kaartuva nuoli", "SSE.Controllers.Main.txtShape_curvedRightArrow": "<PERSON><PERSON><PERSON> kaartuva nuoli", "SSE.Controllers.Main.txtShape_curvedUpArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kaartuva nuoli", "SSE.Controllers.Main.txtShape_decagon": "Kymmen<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_diagStripe": "Viistoraita", "SSE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_dodecagon": "Ka<PERSON><PERSON>istakulm<PERSON>", "SSE.Controllers.Main.txtShape_donut": "Tyhjä ympyrä", "SSE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON> nuoli", "SSE.Controllers.Main.txtShape_downArrowCallout": "Selitys: al<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ellipse": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ellipseRibbon": "<PERSON><PERSON><PERSON><PERSON><PERSON> kaartuva nauha", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kaartuva nauha", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Vuokaavio: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartCollate": "Vuokaavio: Yleiskatsaus", "SSE.Controllers.Main.txtShape_flowChartConnector": "Vuokaavio: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDecision": "Vuokaavio: <PERSON><PERSON><PERSON><PERSON>s", "SSE.Controllers.Main.txtShape_flowChartDelay": "Vuokaavio: Viive", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Vuokaavio: Näyttö", "SSE.Controllers.Main.txtShape_flowChartDocument": "Vuokaavio: Asiakirja", "SSE.Controllers.Main.txtShape_flowChartExtract": "Vuokaavio: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Vuokaavio: Data", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Vuokaavio: <PERSON><PERSON><PERSON><PERSON> tall<PERSON>", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Vuokaavio: <PERSON><PERSON><PERSON><PERSON> levy", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Vuokaavio: <PERSON><PERSON> p<PERSON>", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Vuokaavio: Jaksottaisen pääsyn tallennustila", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Vuokaavio: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Vuokaavio: Man<PERSON><PERSON>nen operaatio", "SSE.Controllers.Main.txtShape_flowChartMerge": "Vuokaavio: Yhdistäminen", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Vuokaavio: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Vuokaavio: <PERSON><PERSON><PERSON><PERSON> sivun <PERSON>", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Vuokaavio: Tallennettu data", "SSE.Controllers.Main.txtShape_flowChartOr": "Vuokaavio: Tai", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Vuokaava: <PERSON><PERSON><PERSON> m<PERSON> prosessi", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Vuokaavio: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartProcess": "Vuokaavio: Prosessi", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Vuokaavio: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Vuokaavio: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartSort": "Vuokaavio: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Vuokaavio: Summa", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Vuokaavio: Aloitus/lopetusmer<PERSON>ki", "SSE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON><PERSON><PERSON> kulma", "SSE.Controllers.Main.txtShape_frame": "<PERSON>ame", "SSE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heart": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heptagon": "Seitsenkulmio", "SSE.Controllers.Main.txtShape_hexagon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_homePlate": "Viisikulmio", "SSE.Controllers.Main.txtShape_horizontalScroll": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Controllers.Main.txtShape_irregularSeal1": "Räjähdys 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Räjähdys 2", "SSE.Controllers.Main.txtShape_leftArrow": "<PERSON><PERSON><PERSON> nuoli", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Nuoli vasemmalle -tekstilaatikko", "SSE.Controllers.Main.txtShape_leftBrace": "Left brace", "SSE.Controllers.Main.txtShape_leftBracket": "Left bracket", "SSE.Controllers.Main.txtShape_leftRightArrow": "<PERSON>uoli vasemmalle ja o<PERSON>", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Nuoli vasemmalle ja o<PERSON><PERSON> te<PERSON>", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Left right up arrow", "SSE.Controllers.Main.txtShape_leftUpArrow": "Nuoli vasemmalle ja yl<PERSON>s", "SSE.Controllers.Main.txtShape_lightningBolt": "Salama", "SSE.Controllers.Main.txtShape_line": "Viiva", "SSE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathDivide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Multiply", "SSE.Controllers.Main.txtShape_mathNotEqual": "Not Equal", "SSE.Controllers.Main.txtShape_mathPlus": "Plus", "SSE.Controllers.Main.txtShape_moon": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_noSmoking": "\"Ei\" Symbooli", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Notched right arrow", "SSE.Controllers.Main.txtShape_octagon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_parallelogram": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_pentagon": "Viisikulmio", "SSE.Controllers.Main.txtShape_pie": "Pie", "SSE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_plus": "Plus", "SSE.Controllers.Main.txtShape_polyline1": "Scribble", "SSE.Controllers.Main.txtShape_polyline2": "<PERSON><PERSON><PERSON> muoto", "SSE.Controllers.Main.txtShape_quadArrow": "Quad arrow", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Quad arrow callout", "SSE.Controllers.Main.txtShape_rect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ribbon": "Alaspäin viisto nauha", "SSE.Controllers.Main.txtShape_ribbon2": "Up ribbon", "SSE.Controllers.Main.txtShape_rightArrow": "Right Arrow", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Right arrow callout", "SSE.Controllers.Main.txtShape_rightBrace": "Right Brace", "SSE.Controllers.Main.txtShape_rightBracket": "Right Bracket", "SSE.Controllers.Main.txtShape_round1Rect": "<PERSON><PERSON><PERSON><PERSON>, jonka yksi kulma pyöristetty", "SSE.Controllers.Main.txtShape_round2DiagRect": "<PERSON><PERSON><PERSON><PERSON><PERSON>, jossa kaksi pyöristettyä vastakkaista kulmaa", "SSE.Controllers.Main.txtShape_round2SameRect": "<PERSON><PERSON><PERSON><PERSON>, jossa kaksi py<PERSON>y<PERSON> kulmaa samalla sivulla", "SSE.Controllers.Main.txtShape_roundRect": "Pyöreä<PERSON><PERSON><PERSON> suorakulmio", "SSE.Controllers.Main.txtShape_rtTriangle": "<PERSON><PERSON><PERSON> k<PERSON>", "SSE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_snip1Rect": "Snip single corner rectangle", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Snip diagonal corner rectangle", "SSE.Controllers.Main.txtShape_snip2SameRect": "Snip same side corner rectangle", "SSE.Controllers.Main.txtShape_snipRoundRect": "Snip and Round Single Corner Rectangle", "SSE.Controllers.Main.txtShape_spline": "Käyrä", "SSE.Controllers.Main.txtShape_star10": "10-<PERSON><PERSON><PERSON> Tähti", "SSE.Controllers.Main.txtShape_star12": "12-<PERSON><PERSON><PERSON> Tähti", "SSE.Controllers.Main.txtShape_star16": "16-<PERSON><PERSON><PERSON> Tähti", "SSE.Controllers.Main.txtShape_star24": "24-<PERSON><PERSON><PERSON> Tähti", "SSE.Controllers.Main.txtShape_star32": "32-<PERSON><PERSON><PERSON> T<PERSON>", "SSE.Controllers.Main.txtShape_star4": "4-<PERSON><PERSON><PERSON> Tähti", "SSE.Controllers.Main.txtShape_star5": "5-<PERSON><PERSON><PERSON> T<PERSON>i", "SSE.Controllers.Main.txtShape_star6": "6-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star7": "7-<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star8": "8-<PERSON><PERSON><PERSON> Tähti", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Striped Right Arrow", "SSE.Controllers.Main.txtShape_sun": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_teardrop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_textRect": "Tekstilaatikko", "SSE.Controllers.Main.txtShape_trapezoid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_triangle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_upArrow": "Up Arrow", "SSE.Controllers.Main.txtShape_upArrowCallout": "Up arrow callout", "SSE.Controllers.Main.txtShape_upDownArrow": "Up down arrow", "SSE.Controllers.Main.txtShape_uturnArrow": "U-Turn Arrow", "SSE.Controllers.Main.txtShape_verticalScroll": "Vertical scroll", "SSE.Controllers.Main.txtShape_wave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Oval callout", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Suorakulmion muotoinen puhekupla", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Puhekupla: Pyöreä<PERSON><PERSON><PERSON> suorakulmio", "SSE.Controllers.Main.txtSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSlicer": "<PERSON>licer", "SSE.Controllers.Main.txtStarsRibbons": "Tähdet & Nauhat", "SSE.Controllers.Main.txtStyle_Bad": "Bad", "SSE.Controllers.Main.txtStyle_Calculation": "Calculation", "SSE.Controllers.Main.txtStyle_Check_Cell": "Check Cell", "SSE.Controllers.Main.txtStyle_Comma": "Pilk<PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Explanatory Text", "SSE.Controllers.Main.txtStyle_Good": "Good", "SSE.Controllers.Main.txtStyle_Heading_1": "Otsikko 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Otsikko 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Otsikko 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Otsikko 4", "SSE.Controllers.Main.txtStyle_Input": "Input", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Linked Cell", "SSE.Controllers.Main.txtStyle_Neutral": "Neutral", "SSE.Controllers.Main.txtStyle_Normal": "Normal", "SSE.Controllers.Main.txtStyle_Note": "Note", "SSE.Controllers.Main.txtStyle_Output": "Output", "SSE.Controllers.Main.txtStyle_Percent": "Prosenttia", "SSE.Controllers.Main.txtStyle_Title": "Title", "SSE.Controllers.Main.txtStyle_Total": "Yhteensä", "SSE.Controllers.Main.txtStyle_Warning_Text": "Warning Text", "SSE.Controllers.Main.txtTab": "Välilehti", "SSE.Controllers.Main.txtTable": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtTime": "<PERSON><PERSON>", "SSE.Controllers.Main.txtUnlock": "<PERSON><PERSON> lukitus", "SSE.Controllers.Main.txtUnlockRange": "Unlock range", "SSE.Controllers.Main.txtUnlockRangeDescription": "Enter the password to change this range:", "SSE.Controllers.Main.txtUnlockRangeWarning": "A range you are trying to change is password protected.", "SSE.Controllers.Main.txtValues": "Arvot", "SSE.Controllers.Main.txtView": "Näytä", "SSE.Controllers.Main.txtXAxis": "X akseli", "SSE.Controllers.Main.txtYAxis": "<PERSON> akseli", "SSE.Controllers.Main.txtYears": "Years", "SSE.Controllers.Main.unknownErrorText": "<PERSON><PERSON><PERSON><PERSON> virhe.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Selaintasi ei ole tuettu", "SSE.Controllers.Main.uploadDocExtMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON> tied<PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.uploadDocFileCountMessage": "<PERSON><PERSON> as<PERSON><PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.uploadDocSizeMessage": "<PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.uploadImageExtMessage": "<PERSON><PERSON><PERSON><PERSON> kuvan muoto.", "SSE.Controllers.Main.uploadImageFileCountMessage": "<PERSON>i ladatt<PERSON>ja kuvia.", "SSE.Controllers.Main.uploadImageSizeMessage": "<PERSON><PERSON><PERSON><PERSON> kuvan koon raj<PERSON>us y<PERSON>.", "SSE.Controllers.Main.uploadImageTextText": "<PERSON><PERSON><PERSON> kuvaa...", "SSE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON><PERSON> kuvaa", "SSE.Controllers.Main.waitText": "Ole hyvä ja odota...", "SSE.Controllers.Main.warnBrowserIE9": "Sovelluksella on alhaiset käyttöominaisuudet IE9 selaimella tai aikaisemalla. Käytä IE10 selainta tai uudempaa", "SSE.Controllers.Main.warnBrowserZoom": "Selaimesi nykyinen suuren<PERSON> asetus ei ole täysin tuettu. Ole hyvä ja aseta päälle selaimesi oletussuurennos näppäinkomennolla Ctrl+0.", "SSE.Controllers.Main.warnLicenseAnonymous": "Pääsy tuntemattomilta käyttäjiltä estetty.<br>Tämä asiakirja avataan vain-luku -tilassa.", "SSE.Controllers.Main.warnLicenseBefore": "Lisenssi ei ole aktiivinen.<br><PERSON><PERSON> yht<PERSON> ylläpitäjää<PERSON>.", "SSE.Controllers.Main.warnLicenseExceeded": "Olet sa<PERSON>uttanut samanaikaisten yhteyksien rajan %1 muokkaustyökaluun. Asiakirja avataan vain luku-tilassa.<br><PERSON>ta yhteyttä ylläpitäjään saadaksesi lisätietoja.", "SSE.Controllers.Main.warnLicenseExp": "Lisenssisi on erääntynyt.<br>Ole hyvä ja päivitä lisenssisi ja virkistä sivu.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Lisenssi erääntynyt.<br><PERSON><PERSON><PERSON> ei ole käyttöoikeutta asiakirjan muokka<PERSON>.<br><PERSON>ta yhteyttä ylläpitäjään.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "<PERSON><PERSON><PERSON> on uusittava.<br><PERSON><PERSON><PERSON> on rajoitettu käyttöoikeus asiakirjan muokka<PERSON>un.<br><PERSON>ta yhteyttä ylläpitäjään saadaksesi täydet käyttöoikeudet.", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Olet saavuttanut käyttäjärajan %1 muokkaustyökalussa. Ota yhteyttä ylläpitäjään saadaksesi lisätietoja.", "SSE.Controllers.Main.warnNoLicense": "Olet käyttämässä %1 avoimen lähdekoodin versiota. Versiolla on rajoituksia yhtäaikaisten yhteyksien määrän suhteen asiakirjan palvelimelle (20 yhteyttä samaan aikaan).<br><PERSON><PERSON> lis<PERSON>, niin voit harkita kaupallista lisenssiä.", "SSE.Controllers.Main.warnNoLicenseUsers": "Olet saavuttanut käyttäjärajan %1 muokkaustyökalussa. Ota yhteyttä %1 myyntiosastoon käyttöoikeuden laajentamiseksi.", "SSE.Controllers.Main.warnProcessRightsChange": "Sinula ei ole riittävästi oikeuksia muokata tiedostoa.", "SSE.Controllers.PivotTable.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "No actions with calculated items are allowed for this active cell.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Calculated items do not work with custom subtotals.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "An item name cannot be found. Check that you've typed name correctly and the item is present in the PivotTable report.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Averages, standard deviations, and variances are not supported when a PivotTable report has calculated items.", "SSE.Controllers.Print.strAllSheets": "Kaikki taulukot", "SSE.Controllers.Print.textFirstCol": "<PERSON><PERSON><PERSON><PERSON><PERSON> sarake", "SSE.Controllers.Print.textFirstRow": "First row", "SSE.Controllers.Print.textFrozenCols": "Frozen columns", "SSE.Controllers.Print.textFrozenRows": "Frozen rows", "SSE.Controllers.Print.textInvalidRange": "VIRHE! Virheellinen solujen tietoalue", "SSE.Controllers.Print.textNoRepeat": "Don't repeat", "SSE.Controllers.Print.textRepeat": "Repeat...", "SSE.Controllers.Print.textSelectRange": "Select range", "SSE.Controllers.Print.txtCustom": "Muka<PERSON>ttu", "SSE.Controllers.Print.txtZoomToPage": "Zoom to page", "SSE.Controllers.Search.textInvalidRange": "VIRHE! Virheellinen solujen tietoalue", "SSE.Controllers.Search.textNoTextFound": "Etsimääsi tietoa ei l<PERSON>. Ole hyvä ja muokkaa hakuva<PERSON>eh<PERSON>.", "SSE.Controllers.Search.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "SSE.Controllers.Search.textReplaceSuccess": "<PERSON><PERSON> on valmis. {0} o<PERSON><PERSON><PERSON> k<PERSON>", "SSE.Controllers.Statusbar.errorLastSheet": "Työkirjalla tulee olla vähintään yksi näkyvä taulukko.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Ei voida poistaa tauluk<PERSON>a.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>Connection is lost</b><br>Trying to connect. Please check connection settings.", "SSE.Controllers.Statusbar.textSheetViewTip": "You are in Sheet View mode. Filters and sorting are visible only to you and those who are still in this view.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "You are in Sheet View mode. Filters are visible only to you and those who are still in this view.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Työkirja voi sisältää tietoja. <PERSON><PERSON><PERSON> varma että haluat jatkaa?", "SSE.Controllers.Statusbar.zoomText": "<PERSON><PERSON><PERSON> {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "<PERSON><PERSON><PERSON>, jota o<PERSON>, ei ole saatavilla nykyisessä laitteessa.<br><PERSON><PERSON><PERSON> tyyli näytetään käyttämällä järjestelmän fontteja, tallennettua fonttia käytetään kun se on saatavilla.<br><PERSON><PERSON><PERSON><PERSON> jatkaa?", "SSE.Controllers.Toolbar.errorComboSeries": "Luodaksesi y<PERSON>äkaavion valitse ainakin kaksi tie<PERSON>.", "SSE.Controllers.Toolbar.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "VIRHE! Tietosarjojen maksimimäärä kaaviossa on 255", "SSE.Controllers.Toolbar.errorStockChart": "Virheellinen rivin järjestys. Jotta voit luoda pörssikaavion, niin aseta tiedot seuraavassa järjestyksessä: <br> a<PERSON><PERSON><PERSON><PERSON>, kor<PERSON><PERSON> hinta, halvin hinta, sulk<PERSON><PERSON><PERSON>.", "SSE.Controllers.Toolbar.helpCalcItems": "Work with calculated items in Pivot Tables.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Calculated items", "SSE.Controllers.Toolbar.helpFastUndo": "Easily undo changes while collaborating on sheets in Fast mode.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "\"Undo\" in real-time co-editing", "SSE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "SSE.Controllers.Toolbar.textAccent": "Aksentit", "SSE.Controllers.Toolbar.textBracket": "Hakasulkeet", "SSE.Controllers.Toolbar.textDirectional": "Directional", "SSE.Controllers.Toolbar.textFontSizeErr": "Syötetty arvo ei ole o<PERSON>.<br>Ole hyvä ja syötä numeerinen arvo välillä 1-409", "SSE.Controllers.Toolbar.textFraction": "Murtoluvut", "SSE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textIndicator": "Indicators", "SSE.Controllers.Toolbar.textInsert": "Lisää", "SSE.Controllers.Toolbar.textIntegral": "Integraalit", "SSE.Controllers.Toolbar.textLargeOperator": "Isot operaattorit", "SSE.Controllers.Toolbar.textLimitAndLog": "<PERSON><PERSON><PERSON><PERSON> ja logaritmit", "SSE.Controllers.Toolbar.textLongOperation": "Long operation", "SSE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textOperator": "Operaattorit", "SSE.Controllers.Toolbar.textPivot": "Pivot Table", "SSE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRating": "Ratings", "SSE.Controllers.Toolbar.textRecentlyUsed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textScript": "Skriptit", "SSE.Controllers.Toolbar.textShapes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textSymbols": "Symbolit", "SSE.Controllers.Toolbar.textWarning": "Varo<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Oikea-Vasen nuoli y<PERSON>äällä", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Vasemmalle suunnattu nuoli ylhäällä", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Oikealle suunnattu nuoli ylhäällä ", "SSE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Alaviiva", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Yläviiva", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON> (paikkamerkillä)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "<PERSON>ati<PERSON><PERSON><PERSON>(esimerkki)", "SSE.Controllers.Toolbar.txtAccent_Check": "Tarkista", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Alasulku", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Yläsul<PERSON>", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vektori A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC yläpalkilla", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y yläviivalla", "SSE.Controllers.Toolbar.txtAccent_DDDot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Ryhmittelevä kirjain al<PERSON>", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Ryhmittelevä kirjain y<PERSON>", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Vasemmalle suunnattu harppuuna yläpuolella", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Oikealle suunnattu harppuuna ylhäällä", "SSE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Smile": "Breve", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Hakasulkeet erottimilla", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Hakasulkeet erottimilla", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Hakasulkeet erottimilla", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Tapauksia (<PERSON><PERSON><PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Tapauksia (<PERSON><PERSON><PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Pino-objekti", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Pino-objekti", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Binominen kerroin", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Binominen kerroin", "SSE.Controllers.Toolbar.txtBracket_Line": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Hakasulkeet erottimilla", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Hakasulkeet", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtDeleteCells": "Poista solut", "SSE.Controllers.Toolbar.txtExpand": "Expand and sort", "SSE.Controllers.Toolbar.txtExpandSort": "The data next to the selection will not be sorted. Do you want to expand the selection to include the adjacent data or continue with sorting the currently selected cells only?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Vinoutunut murtoluku", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Lineaarinen murtoluku", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi yli 2", "SSE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON> m<PERSON>", "SSE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON><PERSON> murtol<PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Käänteinen kosinifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Hyperbolinen Käänteinen Kosinifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Käänteinen Kotangenttifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Hyperbolinen Käänteinen Kotangenttifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Käänteinen Kosekanttifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Hyperbolinen Käänteinen Kosekanttifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Käänteinen Sekanttifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Hyperbolinen Käänteinen Sekanttifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Käänteinen Sinifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Hyperbolinen Käänteinen Sinifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Käänteinen Tangenttifunktio", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Hyperbolinen Käänteinen Tangenttifunktio", "SSE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Hyperbolinen Kosinifunktio", "SSE.Controllers.Toolbar.txtFunction_Cot": "Kotangenttifunktio", "SSE.Controllers.Toolbar.txtFunction_Coth": "Hyperbolinen Kotangenttifunktio", "SSE.Controllers.Toolbar.txtFunction_Csc": "Kosekanttifunktio", "SSE.Controllers.Toolbar.txtFunction_Csch": "Hyperbolinen Kosekanttifunktio", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sini theeta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sec": "Sekanttifunktio", "SSE.Controllers.Toolbar.txtFunction_Sech": "Hyperbolinen Sekanttifunktio", "SSE.Controllers.Toolbar.txtFunction_Sin": "Sinifunktio", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Hyperbolinen Sinifunktio", "SSE.Controllers.Toolbar.txtFunction_Tan": "Tangenttifunktio", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Hyperbolinen Tangenttifunktio", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Muka<PERSON>ttu", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Data and model", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Good, bad and neutral", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "No name", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Number format", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Themed cell styles", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Titles and headings", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Muka<PERSON>ttu", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Tumma", "SSE.Controllers.Toolbar.txtGroupTable_Light": "Vaalea", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Keski<PERSON>koinen", "SSE.Controllers.Toolbar.txtInsertCells": "Lisää solut", "SSE.Controllers.Toolbar.txtIntegral": "Integraali", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Differentiaalinen theeta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Differentiaalinen x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Differentiaalinen y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integraali", "SSE.Controllers.Toolbar.txtIntegralDouble": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOriented": "Inte<PERSON><PERSON><PERSON> muoto", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Inte<PERSON><PERSON><PERSON> muoto", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integra<PERSON><PERSON> pinta", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integra<PERSON><PERSON> pinta", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integra<PERSON><PERSON> pinta", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Inte<PERSON><PERSON><PERSON> muoto", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integra<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integra<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integra<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integraali", "SSE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtInvalidRange": "VIRHE! Virheellinen solun tietoalue", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON><PERSON> tuote", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "<PERSON><PERSON><PERSON><PERSON> tuote", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON> tuote", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "<PERSON><PERSON><PERSON><PERSON> tuote", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "<PERSON><PERSON><PERSON><PERSON> tuote", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Yhteenveto", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Yhteenveto", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Yhteenveto", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Liitos", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "leik<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Leikka<PERSON>piste", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Leikka<PERSON>piste", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Leikka<PERSON>piste", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Leikka<PERSON>piste", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Yhteenveto", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Yhteenveto", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Yhteenveto", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Yhteenveto", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Yhteenveto", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Liitos", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Liitos", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Liitos", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Liitos", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Liitos", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON><PERSON><PERSON><PERSON><PERSON> logaritmi", "SSE.Controllers.Toolbar.txtLimitLog_Log": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Min": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLockSort": "Data is found next to your selection, but you do not have sufficient permissions to change those cells.<br>Do you wish to continue with the current selection?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Tyhjä matriisi", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Tyhjä matriisi", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 Tyhjä matriisi", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Tyhjä matriisi", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Tyhj<PERSON> matri<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Tyhj<PERSON> matri<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Tyhj<PERSON> matri<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Tyhj<PERSON> matri<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 Tyhjä matriisi", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Tyhjä matriisi", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Tyhjä matriisi", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 Tyhjä matriisi", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Perusviivan pisteet", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "<PERSON><PERSON><PERSON><PERSON> pisteet", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON><PERSON><PERSON> pisteet", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON> pys<PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> matriisi", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON> matriisi", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 identiteetin matriisi", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 identiteetin matriisi", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 identiteetin matriisi", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 identiteetin matriisi", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Oikea-<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Oikea-Vasen nuoli y<PERSON>äällä", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Vasemmalle suunnattu nuoli alhaalla", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Vasemmalle suunnattu nuoli ylhäällä", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Oikealle suunnattu nuoli alhaalla", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Oikealle suunnattu nuoli ylhäällä ", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Kaksoispiste yhtäsuuri", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta tuotokset", "SSE.Controllers.Toolbar.txtOperator_Definition": "<PERSON><PERSON><PERSON><PERSON><PERSON> määritelmän mukaan", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta yhtäkuin", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Oikea-<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Oikea-Vasen nuoli y<PERSON>äällä", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Vasemmalle suunnattu nuoli alhaalla", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Vasemmalle suunnattu nuoli ylhäällä", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Oikealle suunnattu nuoli alhaalla", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Oikealle suunnattu nuoli ylhäällä ", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Yhtäsuuri", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Mitattuna:", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSubSup": "Alaindeksi-Yläindeksi", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Vasen AlaIndeksi-Yläindeksi", "SSE.Controllers.Toolbar.txtScriptSup": "Yläindeksi", "SSE.Controllers.Toolbar.txtSorting": "Sorting", "SSE.Controllers.Toolbar.txtSortSelected": "Sort selected", "SSE.Controllers.Toolbar.txtSymbol_about": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_additional": "Täydentää", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ast": "Asteriski Operaattori", "SSE.Controllers.Toolbar.txtSymbol_beta": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_beth": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Pallukka operaattori", "SSE.Controllers.Toolbar.txtSymbol_cap": "Leikka<PERSON>piste", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Keskilinjan v<PERSON>elli<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON> as<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Suunnilleen y<PERSON>äkuin", "SSE.Controllers.Toolbar.txtSymbol_cup": "Liitos", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Alas oikealla diagonaalinen ellipsi", "SSE.Controllers.Toolbar.txtSymbol_degree": "Astetta", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON><PERSON><PERSON><PERSON> nuoli", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Identtinen:", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "On olemassa", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Kertoma", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Fahrenheit astetta", "SSE.Controllers.Toolbar.txtSymbol_forall": "Kaikille", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "Suurempi kuin tai yhtä<PERSON>uri kuin", "SSE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON>jon suurempi kuin", "SSE.Controllers.Toolbar.txtSymbol_greater": "<PERSON>ure<PERSON><PERSON> kuin", "SSE.Controllers.Toolbar.txtSymbol_in": "Elementtinä:", "SSE.Controllers.Toolbar.txtSymbol_inc": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Ääretön", "SSE.Controllers.Toolbar.txtSymbol_iota": "Hitunen", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "<PERSON><PERSON><PERSON> nuoli", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_leq": "Vähemmän kuin tai yhtäkuin", "SSE.Controllers.Toolbar.txtSymbol_less": "Vähemmä<PERSON> kuin", "SSE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON>jon vähemmän kuin", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "<PERSON>i ole <PERSON>in", "SSE.Controllers.Toolbar.txtSymbol_ni": "Sisältyy j<PERSON>ä", "SSE.Controllers.Toolbar.txtSymbol_not": "<PERSON>i merkki", "SSE.Controllers.Toolbar.txtSymbol_notexists": "<PERSON><PERSON> <PERSON>le o<PERSON>a", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_percent": "Prosenttia", "SSE.Controllers.Toolbar.txtSymbol_phi": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_pi": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus Miinus", "SSE.Controllers.Toolbar.txtSymbol_propto": "<PERSON><PERSON><PERSON><PERSON>:", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON><PERSON><PERSON> ju<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON><PERSON><PERSON><PERSON> loppu", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Ylhäällä oikealla diagonaalinen ellipsi", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> nuoli", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "<PERSON><PERSON><PERSON><PERSON> me<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON>i", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theeta", "SSE.Controllers.Toolbar.txtSymbol_times": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon muunnos", "SSE.Controllers.Toolbar.txtSymbol_varphi": "<PERSON><PERSON> m<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON> muunnos", "SSE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON> muunnos", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma muunnos", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON> muunnos", "SSE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Table style Dark", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Table style Light", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Table style Medium", "SSE.Controllers.Toolbar.warnLongOperation": "The operation you are about to perform might take rather much time to complete.<br>Are you sure you want to continue?", "SSE.Controllers.Toolbar.warnMergeLostData": "Vain tieto ylhäällä vasemmalla olevassa solussa jää yhdistettyyn soluun.<br><PERSON><PERSON><PERSON> varma että haluat jatkaa?", "SSE.Controllers.Toolbar.warnNoRecommended": "To create a chart, select the cells that contain the data you'd like to use.<br>If you have names for the rows and columns and you'd like use them as labels, include them in your selection.", "SSE.Controllers.Viewport.textFreezePanes": "Freeze panes", "SSE.Controllers.Viewport.textFreezePanesShadow": "Show Frozen Panes Shadow", "SSE.Controllers.Viewport.textHideFBar": "<PERSON><PERSON><PERSON> ka<PERSON> p<PERSON>ki", "SSE.Controllers.Viewport.textHideGridlines": "Piilota ruudukon viivat", "SSE.Controllers.Viewport.textHideHeadings": "<PERSON><PERSON><PERSON>", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Decimal separator", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Thousands separator", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Settings used to recognize numeric data", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Text qualifier", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Advanced settings", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(ei mit<PERSON>än)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "<PERSON><PERSON><PERSON><PERSON><PERSON> suodatin", "SSE.Views.AutoFilterDialog.textAddSelection": "Lisää n<PERSON>yinen valinta suoda<PERSON>n", "SSE.Views.AutoFilterDialog.textEmptyItem": "{<PERSON><PERSON>j<PERSON>}", "SSE.Views.AutoFilterDialog.textSelectAll": "<PERSON><PERSON><PERSON> kaikki", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Valitse kaikki haku<PERSON>", "SSE.Views.AutoFilterDialog.textWarning": "Varo<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtAboveAve": "Keskimääräistä enemmän", "SSE.Views.AutoFilterDialog.txtAfter": "<PERSON><PERSON><PERSON><PERSON>...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "All dates in the period", "SSE.Views.AutoFilterDialog.txtApril": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtAugust": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtBefore": "Before...", "SSE.Views.AutoFilterDialog.txtBegins": "alkaa...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Keskimääräistä vähemmän", "SSE.Views.AutoFilterDialog.txtBetween": "Välillä...", "SSE.Views.AutoFilterDialog.txtClear": "Tyhjennä", "SSE.Views.AutoFilterDialog.txtContains": "Sisältää...", "SSE.Views.AutoFilterDialog.txtDateFilter": "Date filter", "SSE.Views.AutoFilterDialog.txtDecember": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtEmpty": "Syötä solun suodatin", "SSE.Views.AutoFilterDialog.txtEnds": "Loppuu...", "SSE.Views.AutoFilterDialog.txtEquals": "On yhtäkuin...", "SSE.Views.AutoFilterDialog.txtFebruary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Suodata solujen värien mukaan", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Suodata fontin v<PERSON><PERSON> mukaan", "SSE.Views.AutoFilterDialog.txtGreater": "Suure<PERSON><PERSON> kuin...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Suurempi kuin tai yhtäsuuri...", "SSE.Views.AutoFilterDialog.txtJanuary": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtJuly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtJune": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Label filter", "SSE.Views.AutoFilterDialog.txtLastMonth": "Last month", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Last quarter", "SSE.Views.AutoFilterDialog.txtLastWeek": "Last week", "SSE.Views.AutoFilterDialog.txtLastYear": "Last year", "SSE.Views.AutoFilterDialog.txtLess": "<PERSON><PERSON><PERSON>m<PERSON><PERSON> kuin...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Vähemmän kuin tai yhtäsuuri kuin...", "SSE.Views.AutoFilterDialog.txtMarch": "Maalis<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtMay": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtNextMonth": "Next month", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Next quarter", "SSE.Views.AutoFilterDialog.txtNextWeek": "Next week", "SSE.Views.AutoFilterDialog.txtNextYear": "Next year", "SSE.Views.AutoFilterDialog.txtNotBegins": "Ei ala...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Not between...", "SSE.Views.AutoFilterDialog.txtNotContains": "Ei sisäll<PERSON>...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Ei lopu...", "SSE.Views.AutoFilterDialog.txtNotEquals": "<PERSON>i ole <PERSON>in", "SSE.Views.AutoFilterDialog.txtNovember": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtNumFilter": "Numeron suodatin", "SSE.Views.AutoFilterDialog.txtOctober": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtQuarter1": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Quarter 1", "SSE.Views.AutoFilterDialog.txtReapply": "Käytä <PERSON>", "SSE.Views.AutoFilterDialog.txtSeptember": "Syyskuu", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Lajittele solujen värin mukaan", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Lajittele fontin värin mukaan", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Lajittele k<PERSON>", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Lajittele matali<PERSON> k<PERSON>", "SSE.Views.AutoFilterDialog.txtSortOption": "More sort options...", "SSE.Views.AutoFilterDialog.txtTextFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtThisMonth": "<PERSON><PERSON><PERSON><PERSON> kuussa", "SSE.Views.AutoFilterDialog.txtThisQuarter": "This quarter", "SSE.Views.AutoFilterDialog.txtThisWeek": "This week", "SSE.Views.AutoFilterDialog.txtThisYear": "This Year", "SSE.Views.AutoFilterDialog.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtToday": "Tänää<PERSON>", "SSE.Views.AutoFilterDialog.txtTomorrow": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Value filter", "SSE.Views.AutoFilterDialog.txtYearToDate": "Year to date", "SSE.Views.AutoFilterDialog.txtYesterday": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.warnFilterError": "You need at least one field in the Values area in order to apply a value filter.", "SSE.Views.AutoFilterDialog.warnNoSelected": "<PERSON>un tulee valita vähintään yksi arvo", "SSE.Views.CellEditor.textManager": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellEditor.tipFormula": "Lisää funktio", "SSE.Views.CellRangeDialog.errorMaxRows": "VIRHE! Tietosarjojen maksimimäärä kaaviossa on 255", "SSE.Views.CellRangeDialog.errorStockChart": "Virheellinen rivin järjestys. Jotta voit luoda pörssikaavion, niin aseta tiedot seuraavassa järjestyksessä: <br> a<PERSON><PERSON><PERSON><PERSON>, kor<PERSON><PERSON> hinta, halvin hinta, sulk<PERSON><PERSON><PERSON>.", "SSE.Views.CellRangeDialog.txtEmpty": "Tämä kenttä tarvitaan", "SSE.Views.CellRangeDialog.txtInvalidRange": "VIRHE! Virheellinen solujen tietoalue", "SSE.Views.CellRangeDialog.txtTitle": "Valitse tietoalue", "SSE.Views.CellSettings.strShrink": "Shrink to fit", "SSE.Views.CellSettings.strWrap": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "Background color", "SSE.Views.CellSettings.textBackground": "Background color", "SSE.Views.CellSettings.textBorderColor": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "<PERSON><PERSON><PERSON><PERSON> tyyli", "SSE.Views.CellSettings.textClearRule": "Clear Rules", "SSE.Views.CellSettings.textColor": "Väritäyttö", "SSE.Views.CellSettings.textColorScales": "Color scales", "SSE.Views.CellSettings.textCondFormat": "Conditional formatting", "SSE.Views.CellSettings.textControl": "Text control", "SSE.Views.CellSettings.textDataBars": "Data bars", "SSE.Views.CellSettings.textDirection": "<PERSON><PERSON>", "SSE.Views.CellSettings.textFill": "Täytä", "SSE.Views.CellSettings.textForeground": "Etualan väri", "SSE.Views.CellSettings.textGradient": "Gradient points", "SSE.Views.CellSettings.textGradientColor": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "Gradient fill", "SSE.Views.CellSettings.textIndent": "Indent", "SSE.Views.CellSettings.textItems": "Items", "SSE.Views.CellSettings.textLinear": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textManageRule": "Manage rules", "SSE.Views.CellSettings.textNewRule": "New rule", "SSE.Views.CellSettings.textNoFill": "Ei tä<PERSON>", "SSE.Views.CellSettings.textOrientation": "Text orientation", "SSE.Views.CellSettings.textPattern": "<PERSON><PERSON>", "SSE.Views.CellSettings.textPatternFill": "<PERSON><PERSON>", "SSE.Views.CellSettings.textPosition": "Position", "SSE.Views.CellSettings.textRadial": "S<PERSON><PERSON><PERSON>äinen", "SSE.Views.CellSettings.textSelectBorders": "Select borders you want to change applying style chosen above", "SSE.Views.CellSettings.textSelection": "From current selection", "SSE.Views.CellSettings.textThisPivot": "From this pivot", "SSE.Views.CellSettings.textThisSheet": "From this worksheet", "SSE.Views.CellSettings.textThisTable": "From this table", "SSE.Views.CellSettings.tipAddGradientPoint": "Lisää kaltev<PERSON>", "SSE.Views.CellSettings.tipAll": "Set outer border and all inner lines", "SSE.Views.CellSettings.tipBottom": "Set outer bottom border only", "SSE.Views.CellSettings.tipDiagD": "Set diagonal down border", "SSE.Views.CellSettings.tipDiagU": "Set diagonal up border", "SSE.Views.CellSettings.tipInner": "Set inner lines only", "SSE.Views.CellSettings.tipInnerHor": "Set horizontal inner lines only", "SSE.Views.CellSettings.tipInnerVert": "Set vertical inner lines only", "SSE.Views.CellSettings.tipLeft": "Set outer left border only", "SSE.Views.CellSettings.tipNone": "Aseta ilman reunuksia", "SSE.Views.CellSettings.tipOuter": "Set outer border only", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Poista gradienttipiste", "SSE.Views.CellSettings.tipRight": "Set outer right border only", "SSE.Views.CellSettings.tipTop": "Set outer top border only", "SSE.Views.ChartDataDialog.errorInFormula": "There's an error in formula you entered.", "SSE.Views.ChartDataDialog.errorInvalidReference": "The reference is not valid. Reference must be to an open worksheet.", "SSE.Views.ChartDataDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "The reference is not valid. References for titles, values, sizes, or data labels must be a single cell, row, or column.", "SSE.Views.ChartDataDialog.errorNoValues": "To create a chart, the series must contain at least one value.", "SSE.Views.ChartDataDialog.errorStockChart": "Virheellinen rivin järjestys. Jotta voit luoda pörssikaavion, niin aseta tiedot seuraavassa järjestyksessä: <br> a<PERSON><PERSON><PERSON><PERSON>, kor<PERSON><PERSON> hinta, halvin hinta, sulk<PERSON><PERSON><PERSON>.", "SSE.Views.ChartDataDialog.textAdd": "Lisää", "SSE.Views.ChartDataDialog.textCategory": "Horizontal (category) axis labels", "SSE.Views.ChartDataDialog.textData": "Chart data range", "SSE.Views.ChartDataDialog.textDelete": "Poista", "SSE.Views.ChartDataDialog.textDown": "Alas", "SSE.Views.ChartDataDialog.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ChartDataDialog.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.ChartDataDialog.textSeries": "Legend entries (series)", "SSE.Views.ChartDataDialog.textSwitch": "Switch row/column", "SSE.Views.ChartDataDialog.textTitle": "Chart data", "SSE.Views.ChartDataDialog.textUp": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.errorInFormula": "There's an error in formula you entered.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "The reference is not valid. Reference must be to an open worksheet.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "The reference is not valid. References for titles, values, sizes, or data labels must be a single cell, row, or column.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "To create a chart, the series must contain at least one value.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Virheellinen rivin järjestys. Jotta voit luoda pörssikaavion, niin aseta tiedot seuraavassa järjestyksessä: <br> a<PERSON><PERSON><PERSON><PERSON>, kor<PERSON><PERSON> hinta, halvin hinta, sulk<PERSON><PERSON><PERSON>.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ChartDataRangeDialog.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Axis label range", "SSE.Views.ChartDataRangeDialog.txtChoose": "Choose range", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Axis labels", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Edit series", "SSE.Views.ChartDataRangeDialog.txtValues": "Arvot", "SSE.Views.ChartDataRangeDialog.txtXValues": "X values", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y values", "SSE.Views.ChartSettings.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartSettings.strLineWeight": "<PERSON><PERSON><PERSON> paksuus", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.text3dDepth": "Syvyys (% perustasta)", "SSE.Views.ChartSettings.text3dHeight": "Korkeus (% pohjasta)", "SSE.Views.ChartSettings.text3dRotation": "Kolmiulotteinen pyöritys", "SSE.Views.ChartSettings.textAdvanced": "Näytä laajennetut asetukset", "SSE.Views.ChartSettings.textAutoscale": "Automaattinen skaalaus", "SSE.Views.ChartSettings.textBorderSizeErr": "Syötetty arvo ei ole o<PERSON>.<br>Ole hyvä ja syötä arvo välillä 0 pt ja 1584 pt", "SSE.Views.ChartSettings.textChangeType": "<PERSON><PERSON> tyyppiä", "SSE.Views.ChartSettings.textChartType": "<PERSON><PERSON> kaavion t<PERSON>", "SSE.Views.ChartSettings.textDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textDown": "Alas", "SSE.Views.ChartSettings.textEditData": "Muokkaa tietoja ja sijaintia", "SSE.Views.ChartSettings.textFirstPoint": "Ensimmmäinen piste", "SSE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textHighPoint": "Ko<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textLastPoint": "Viimeinen piste", "SSE.Views.ChartSettings.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textLowPoint": "Alapiste", "SSE.Views.ChartSettings.textMarkers": "Merkit", "SSE.Views.ChartSettings.textNarrow": "Narrow field of view", "SSE.Views.ChartSettings.textNegativePoint": "Negatiivinen piste", "SSE.Views.ChartSettings.textPerspective": "Perspektiivi", "SSE.Views.ChartSettings.textRanges": "Tietoalue", "SSE.Views.ChartSettings.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textRightAngle": "Right angle axes", "SSE.Views.ChartSettings.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.ChartSettings.textShow": "Näytä", "SSE.Views.ChartSettings.textSize": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSwitch": "Switch Row/Column", "SSE.Views.ChartSettings.textType": "Tyyppi", "SSE.Views.ChartSettings.textUp": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textWiden": "Laajenna näkökenttää", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textX": "Pyöritys X-akselilla", "SSE.Views.ChartSettings.textY": "Pyöritys Y-akselilla", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ERROR! The maximum number of points in series per chart is 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "VIRHE! Tietosarjojen maksimimäärä kaaviossa on 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Virheellinen rivin järjestys. Jotta voit luoda pörssikaavion, niin aseta tiedot seuraavassa järjestyksessä: <br> a<PERSON><PERSON><PERSON><PERSON>, kor<PERSON><PERSON> hinta, halvin hinta, sulk<PERSON><PERSON><PERSON>.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Don't move or size with cells", "SSE.Views.ChartSettingsDlg.textAlt": "Vaihtoehtoinen teksti", "SSE.Views.ChartSettingsDlg.textAltDescription": "Description", "SSE.Views.ChartSettingsDlg.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart, or table.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Title", "SSE.Views.ChartSettingsDlg.textAuto": "Automaattinen", "SSE.Views.ChartSettingsDlg.textAutoEach": "Automaattisesti jokaiselle", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Aks<PERSON><PERSON> le<PERSON>", "SSE.Views.ChartSettingsDlg.textAxisOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisPos": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Title", "SSE.Views.ChartSettingsDlg.textBase": "Base", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Valintamerkkien välillä", "SSE.Views.ChartSettingsDlg.textBillions": "Miljardia", "SSE.Views.ChartSettingsDlg.textBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCategoryName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCenter": "Keskellä", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "<PERSON><PERSON><PERSON> elementit & <br><PERSON><PERSON><PERSON> k<PERSON>", "SSE.Views.ChartSettingsDlg.textChartTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCross": "Ylittää", "SSE.Views.ChartSettingsDlg.textCustom": "Muka<PERSON>ttu", "SSE.Views.ChartSettingsDlg.textDataColumns": "Sarakkeissa", "SSE.Views.ChartSettingsDlg.textDataLabels": "Tieto-otsikot", "SSE.Views.ChartSettingsDlg.textDataRows": "riveissä", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Näytä kuvateksti", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Piilotetut ja Tyhjät solut ", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Yhdistä tietopisteet viivalla", "SSE.Views.ChartSettingsDlg.textFit": "<PERSON><PERSON><PERSON> le<PERSON> mukaan", "SSE.Views.ChartSettingsDlg.textFixed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textFormat": "Label format", "SSE.Views.ChartSettingsDlg.textGaps": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGridLines": "Ruudukon viivat", "SSE.Views.ChartSettingsDlg.textGroup": "Kipinäviivojen r<PERSON>", "SSE.Views.ChartSettingsDlg.textHide": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHideAxis": "Hide axis", "SSE.Views.ChartSettingsDlg.textHigh": "Korkea", "SSE.Views.ChartSettingsDlg.textHorAxis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Secondary horizontal axis", "SSE.Views.ChartSettingsDlg.textHorizontal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "Sisällä", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Sisä<PERSON><PERSON> al<PERSON>a", "SSE.Views.ChartSettingsDlg.textInnerTop": "Sisäreuna ylhäällä", "SSE.Views.ChartSettingsDlg.textInvalidRange": "VIRHE! Virheellinen solujen tietoalue", "SSE.Views.ChartSettingsDlg.textLabelDist": "<PERSON><PERSON>elin otsikon etäisyys", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Otsikoiden väli", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Otsikon vaihtoehdot", "SSE.Views.ChartSettingsDlg.textLabelPos": "Ots<PERSON><PERSON> asema", "SSE.Views.ChartSettingsDlg.textLayout": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Vasen päällekkäin", "SSE.Views.ChartSettingsDlg.textLegendBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendPos": "Ku<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendTop": "Yläosa", "SSE.Views.ChartSettingsDlg.textLines": "Viivat", "SSE.Views.ChartSettingsDlg.textLocationRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLogScale": "Logarithmic scale", "SSE.Views.ChartSettingsDlg.textLow": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajor": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorMinor": "<PERSON><PERSON> ja <PERSON>", "SSE.Views.ChartSettingsDlg.textMajorType": "<PERSON><PERSON> t<PERSON>", "SSE.Views.ChartSettingsDlg.textManual": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarkers": "Merkit", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Merkkien väli", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMillions": "Miljoonia", "SSE.Views.ChartSettingsDlg.textMinor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinorType": "V<PERSON>häinen tyyppi", "SSE.Views.ChartSettingsDlg.textMinValue": "Minimiarvo", "SSE.Views.ChartSettingsDlg.textNextToAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNone": "<PERSON><PERSON> mit<PERSON>n", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Ei päällekkäisyyksiä", "SSE.Views.ChartSettingsDlg.textOneCell": "Move but don't size with cells", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Viivainmerkit", "SSE.Views.ChartSettingsDlg.textOut": "Ulkona", "SSE.Views.ChartSettingsDlg.textOuterTop": "Ulompi Ylhäällä", "SSE.Views.ChartSettingsDlg.textOverlay": "Päällekkäin", "SSE.Views.ChartSettingsDlg.textReverse": "Arvot käänteisessä järjetyksessä", "SSE.Views.ChartSettingsDlg.textReverseOrder": "<PERSON><PERSON> til<PERSON>", "SSE.Views.ChartSettingsDlg.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Päällekkäin o<PERSON>", "SSE.Views.ChartSettingsDlg.textRotated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSameAll": "<PERSON><PERSON> ka<PERSON>", "SSE.Views.ChartSettingsDlg.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.ChartSettingsDlg.textSeparator": "Tieto-otsikoiden erotin", "SSE.Views.ChartSettingsDlg.textSeriesName": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShow": "Näytä", "SSE.Views.ChartSettingsDlg.textShowBorders": "<PERSON><PERSON><PERSON><PERSON> kaavion reunukset", "SSE.Views.ChartSettingsDlg.textShowData": "Näytä tiedot piilotetuissa riveissä ja sarakkeissa", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Näytä tyhjät solut kuten", "SSE.Views.ChartSettingsDlg.textShowEquation": "Display equation on chart", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Näytä akseli", "SSE.Views.ChartSettingsDlg.textShowValues": "Näytä kaavion arvot", "SSE.Views.ChartSettingsDlg.textSingle": "Yksi kipinäviiva", "SSE.Views.ChartSettingsDlg.textSmooth": "Pehmeä", "SSE.Views.ChartSettingsDlg.textSnap": "Cell snapping", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Kipinäviivan tietoalue", "SSE.Views.ChartSettingsDlg.textStraight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTickOptions": "Rastivaihtoehdot", "SSE.Views.ChartSettingsDlg.textTitle": "Kaavio - Laaj<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Kipinäviiva - Laajennetut asetukset", "SSE.Views.ChartSettingsDlg.textTop": "Yläosa", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Trendline options", "SSE.Views.ChartSettingsDlg.textTrillions": "Triljoonia", "SSE.Views.ChartSettingsDlg.textTwoCell": "Move and size with cells", "SSE.Views.ChartSettingsDlg.textType": "Tyyppi", "SSE.Views.ChartSettingsDlg.textTypeData": "Tyyppi & Tiedot", "SSE.Views.ChartSettingsDlg.textUnits": "Näyttöyksiköt", "SSE.Views.ChartSettingsDlg.textValue": "Arvo", "SSE.Views.ChartSettingsDlg.textVertAxis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Secondary vertical axis", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "A Akselin o<PERSON>ikko", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y Akseli otsikko", "SSE.Views.ChartSettingsDlg.textZero": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.txtEmpty": "Tämä kenttä tarvitaan", "SSE.Views.ChartTypeDialog.errorComboSeries": "Luodaksesi y<PERSON>äkaavion valitse ainakin kaksi tie<PERSON>.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartTypeDialog.textSecondary": "Secondary axis", "SSE.Views.ChartTypeDialog.textSeries": "Series", "SSE.Views.ChartTypeDialog.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textTitle": "Chart type", "SSE.Views.ChartTypeDialog.textType": "Tyyppi", "SSE.Views.ChartWizardDialog.errorComboSeries": "Luodaksesi y<PERSON>äkaavion valitse ainakin kaksi tie<PERSON>.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartWizardDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order: opening price, max price, min price, closing price.", "SSE.Views.ChartWizardDialog.textRecommended": "Recommended", "SSE.Views.ChartWizardDialog.textSecondary": "Secondary Axis", "SSE.Views.ChartWizardDialog.textSeries": "Series", "SSE.Views.ChartWizardDialog.textTitle": "Lisää <PERSON>", "SSE.Views.ChartWizardDialog.textTitleChange": "<PERSON><PERSON> kaavion t<PERSON>", "SSE.Views.ChartWizardDialog.textType": "Tyyppi", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Choose the chart type and axis for your data series", "SSE.Views.CreatePivotDialog.textDataRange": "Source data range", "SSE.Views.CreatePivotDialog.textDestination": "Choose where to place the table", "SSE.Views.CreatePivotDialog.textExist": "Existing worksheet", "SSE.Views.CreatePivotDialog.textInvalidRange": "Invalid cells range", "SSE.Views.CreatePivotDialog.textNew": "New worksheet", "SSE.Views.CreatePivotDialog.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.CreatePivotDialog.textTitle": "Create pivot table", "SSE.Views.CreatePivotDialog.txtEmpty": "This field is required", "SSE.Views.CreateSparklineDialog.textDataRange": "Source data range", "SSE.Views.CreateSparklineDialog.textDestination": "Choose, where to place the sparklines", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Invalid cells range", "SSE.Views.CreateSparklineDialog.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.CreateSparklineDialog.textTitle": "Create sparklines", "SSE.Views.CreateSparklineDialog.txtEmpty": "This field is required", "SSE.Views.DataTab.capBtnGroup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextCustomSort": "Custom Sort", "SSE.Views.DataTab.capBtnTextDataValidation": "Data Validation", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Remove Duplicates", "SSE.Views.DataTab.capBtnTextToCol": "Text to Columns", "SSE.Views.DataTab.capBtnUngroup": "<PERSON><PERSON> ryhm<PERSON>s", "SSE.Views.DataTab.capDataExternalLinks": "External Links", "SSE.Views.DataTab.capDataFromText": "Get Data", "SSE.Views.DataTab.capGoalSeek": "Goal Seek", "SSE.Views.DataTab.mniFromFile": "From Local TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "From TXT/CSV Web Address", "SSE.Views.DataTab.mniFromXMLFile": "From Local XML", "SSE.Views.DataTab.textBelow": "Summary rows below detail", "SSE.Views.DataTab.textClear": "Clear outline", "SSE.Views.DataTab.textColumns": "Ungroup columns", "SSE.Views.DataTab.textGroupColumns": "Group columns", "SSE.Views.DataTab.textGroupRows": "Group rows", "SSE.Views.DataTab.textRightOf": "Summary columns to right of detail", "SSE.Views.DataTab.textRows": "Ungroup rows", "SSE.Views.DataTab.tipCustomSort": "Custom sort", "SSE.Views.DataTab.tipDataFromText": "Get data from file", "SSE.Views.DataTab.tipDataValidation": "Data validation", "SSE.Views.DataTab.tipExternalLinks": "View other files this spreadsheet is linked to", "SSE.Views.DataTab.tipGoalSeek": "Find the right input for the value you want", "SSE.Views.DataTab.tipGroup": "Group range of cells", "SSE.Views.DataTab.tipRemDuplicates": "Remove duplicate rows from a sheet", "SSE.Views.DataTab.tipToColumns": "Separate cell text into columns", "SSE.Views.DataTab.tipUngroup": "Ungroup range of cells", "SSE.Views.DataValidationDialog.errorFormula": "The value currently evaluates to an error. Do you want to continue?", "SSE.Views.DataValidationDialog.errorInvalid": "The value you entered for the field \"{0}\" is invalid.", "SSE.Views.DataValidationDialog.errorInvalidDate": "The date you entered for the field \"{0}\" is invalid.", "SSE.Views.DataValidationDialog.errorInvalidList": "The list source must be a delimited list, or a reference to single row or column.", "SSE.Views.DataValidationDialog.errorInvalidTime": "The time you entered for the field \"{0}\" is invalid.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "The \"{1}\" field must be greater than or equal to the \"{0}\" field.", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "You must enter a value in both field \"{0}\" and field \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "You must enter a value in field \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "A named range you specified cannot be found.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Negative values cannot be used in conditions \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "The field \"{0}\" must be a numeric value, numeric expression, or refer to a cell containing a numeric value.", "SSE.Views.DataValidationDialog.strError": "Error alert", "SSE.Views.DataValidationDialog.strInput": "Input message", "SSE.Views.DataValidationDialog.strSettings": "Asetukset", "SSE.Views.DataValidationDialog.textAlert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAllow": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textApply": "Apply these changes to all other cells with the same settings", "SSE.Views.DataValidationDialog.textCellSelected": "When cell is selected, show this input message", "SSE.Views.DataValidationDialog.textCompare": "Compare to", "SSE.Views.DataValidationDialog.textData": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndDate": "Loppupäivä", "SSE.Views.DataValidationDialog.textEndTime": "End time", "SSE.Views.DataValidationDialog.textError": "Error message", "SSE.Views.DataValidationDialog.textFormula": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textIgnore": "Ignore blank", "SSE.Views.DataValidationDialog.textInput": "Input message", "SSE.Views.DataValidationDialog.textMax": "Maximum", "SSE.Views.DataValidationDialog.textMessage": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMin": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.DataValidationDialog.textShowDropDown": "Show drop-down list in cell", "SSE.Views.DataValidationDialog.textShowError": "Show error alert after invalid data is entered", "SSE.Views.DataValidationDialog.textShowInput": "Show input message when cell is selected", "SSE.Views.DataValidationDialog.textSource": "Lä<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStartDate": "Start date", "SSE.Views.DataValidationDialog.textStartTime": "Start time", "SSE.Views.DataValidationDialog.textStop": "Pysäytä", "SSE.Views.DataValidationDialog.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textTitle": "Title", "SSE.Views.DataValidationDialog.textUserEnters": "When user enters invalid data, show this error alert", "SSE.Views.DataValidationDialog.txtAny": "Any value", "SSE.Views.DataValidationDialog.txtBetween": "between", "SSE.Views.DataValidationDialog.txtDate": "Date", "SSE.Views.DataValidationDialog.txtDecimal": "Decimal", "SSE.Views.DataValidationDialog.txtElTime": "Elapsed time", "SSE.Views.DataValidationDialog.txtEndDate": "Loppupäivä", "SSE.Views.DataValidationDialog.txtEndTime": "End time", "SSE.Views.DataValidationDialog.txtEqual": "equals", "SSE.Views.DataValidationDialog.txtGreaterThan": "greater than", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "greater than or equal to", "SSE.Views.DataValidationDialog.txtLength": "Length", "SSE.Views.DataValidationDialog.txtLessThan": "Vähemmä<PERSON> kuin", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "Vähemmän kuin tai yhtäkuin", "SSE.Views.DataValidationDialog.txtList": "List", "SSE.Views.DataValidationDialog.txtNotBetween": "not between", "SSE.Views.DataValidationDialog.txtNotEqual": "does not equal", "SSE.Views.DataValidationDialog.txtOther": "Other", "SSE.Views.DataValidationDialog.txtStartDate": "Start date", "SSE.Views.DataValidationDialog.txtStartTime": "Start time", "SSE.Views.DataValidationDialog.txtTextLength": "Text length", "SSE.Views.DataValidationDialog.txtTime": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtWhole": "Whole number", "SSE.Views.DigitalFilterDialog.capAnd": "<PERSON>a", "SSE.Views.DigitalFilterDialog.capCondition1": "on yhtäkuin", "SSE.Views.DigitalFilterDialog.capCondition10": "ei lopu", "SSE.Views.DigitalFilterDialog.capCondition11": "sisältää", "SSE.Views.DigitalFilterDialog.capCondition12": "ei sis<PERSON>llä", "SSE.Views.DigitalFilterDialog.capCondition2": "ei ole yhtäkuin", "SSE.Views.DigitalFilterDialog.capCondition3": "on suurempi kuin", "SSE.Views.DigitalFilterDialog.capCondition30": "is after", "SSE.Views.DigitalFilterDialog.capCondition4": "on suurempi kuin tai yhtä<PERSON>uri kuin", "SSE.Views.DigitalFilterDialog.capCondition40": "is after or equal to", "SSE.Views.DigitalFilterDialog.capCondition5": "on vähemmän kuin", "SSE.Views.DigitalFilterDialog.capCondition50": "is before", "SSE.Views.DigitalFilterDialog.capCondition6": "on vä<PERSON>män tai yhtä<PERSON>uri kuin", "SSE.Views.DigitalFilterDialog.capCondition60": "is before or equal to", "SSE.Views.DigitalFilterDialog.capCondition7": "alkaa", "SSE.Views.DigitalFilterDialog.capCondition8": "ei ala ", "SSE.Views.DigitalFilterDialog.capCondition9": "loppuu", "SSE.Views.DigitalFilterDialog.capOr": "tai", "SSE.Views.DigitalFilterDialog.textNoFilter": "ei suodatinta", "SSE.Views.DigitalFilterDialog.textShowRows": "Näytä rivit missä", "SSE.Views.DigitalFilterDialog.textUse1": "Käytä ? merkkiä esittämään mitä tahansa yksittäistä kirjainta", "SSE.Views.DigitalFilterDialog.textUse2": "Käytä * merkkiä esittämään mitä tahansa kirjainsar<PERSON>a", "SSE.Views.DigitalFilterDialog.txtSelectDate": "Valitse päivämäärä", "SSE.Views.DigitalFilterDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> suodatin", "SSE.Views.DocumentHolder.advancedEquationText": "Y<PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON>", "SSE.Views.DocumentHolder.advancedImgText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.advancedShapeText": "<PERSON><PERSON><PERSON> la<PERSON> as<PERSON>", "SSE.Views.DocumentHolder.advancedSlicerText": "Slicer advanced settings", "SSE.Views.DocumentHolder.allLinearText": "Kaik<PERSON> <PERSON> <PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.allProfText": "<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.bottomCellText": "<PERSON><PERSON><PERSON> alas", "SSE.Views.DocumentHolder.bulletsText": "Luettelomerkit ja numerointi", "SSE.Views.DocumentHolder.centerCellText": "<PERSON><PERSON><PERSON> kes<PERSON>e", "SSE.Views.DocumentHolder.chartDataText": "Select Chart Data", "SSE.Views.DocumentHolder.chartText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.chartTypeText": "<PERSON><PERSON> kaavion t<PERSON>", "SSE.Views.DocumentHolder.currLinearText": "Nykyinen - Lineaarinen", "SSE.Views.DocumentHolder.currProfText": "Nykyinen <PERSON> <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteColumnText": "<PERSON>ista sarake", "SSE.Views.DocumentHolder.deleteRowText": "Poista rivi", "SSE.Views.DocumentHolder.deleteTableText": "Poista taulukko", "SSE.Views.DocumentHolder.direct270Text": "Käännä 270°", "SSE.Views.DocumentHolder.direct90Text": "Käännä 90°", "SSE.Views.DocumentHolder.directHText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.directionText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.editChartText": "Muokkaa tie<PERSON>", "SSE.Views.DocumentHolder.editHyperlinkText": "Muokkaa link<PERSON>ä", "SSE.Views.DocumentHolder.hideEqToolbar": "Piilota Yhtälö-työkalurivi", "SSE.Views.DocumentHolder.insertColumnLeftText": "Lisää sarake vasemmalle", "SSE.Views.DocumentHolder.insertColumnRightText": "Lisää sarake o<PERSON>", "SSE.Views.DocumentHolder.insertRowAboveText": "Lisää rivi ylös", "SSE.Views.DocumentHolder.insertRowBelowText": "Lisää rivi alas", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "Actual size", "SSE.Views.DocumentHolder.removeHyperlinkText": "Poista linkki", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON> koko sarake", "SSE.Views.DocumentHolder.selectDataText": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.DocumentHolder.selectRowText": "Valitse rivi", "SSE.Views.DocumentHolder.selectTableText": "Valitse taulukko", "SSE.Views.DocumentHolder.showEqToolbar": "Show Equation Toolbar", "SSE.Views.DocumentHolder.strDelete": "Poista allekirjoitus", "SSE.Views.DocumentHolder.strDetails": "Allekirjoituksen yksityiskohdat", "SSE.Views.DocumentHolder.strSetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.strSign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textAlign": "Ta<PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrange": "Järjestä", "SSE.Views.DocumentHolder.textArrangeBack": "Lähetä taustalle", "SSE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeFront": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textAverage": "Average", "SSE.Views.DocumentHolder.textBullets": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCopyCells": "Copy cells", "SSE.Views.DocumentHolder.textCount": "Count", "SSE.Views.DocumentHolder.textCrop": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFill": "Täytä", "SSE.Views.DocumentHolder.textCropFit": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textEditPoints": "Muokkaa pisteitä", "SSE.Views.DocumentHolder.textEntriesList": "Valitse tiputusvalikosta", "SSE.Views.DocumentHolder.textFillDays": "Fill days", "SSE.Views.DocumentHolder.textFillFormatOnly": "Fill formatting only", "SSE.Views.DocumentHolder.textFillMonths": "Fill months", "SSE.Views.DocumentHolder.textFillSeries": "Fill series", "SSE.Views.DocumentHolder.textFillWeekdays": "Fill weekdays", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Fill without formatting", "SSE.Views.DocumentHolder.textFillYears": "Fill years", "SSE.Views.DocumentHolder.textFlashFill": "Flash fill", "SSE.Views.DocumentHolder.textFlipH": "Käännä vaakasuunnassa", "SSE.Views.DocumentHolder.textFlipV": "Käännä pysty<PERSON>unnassa", "SSE.Views.DocumentHolder.textFreezePanes": "Jäädytä ruudut", "SSE.Views.DocumentHolder.textFromFile": "Tiedostosta", "SSE.Views.DocumentHolder.textFromStorage": "Tallennusvälineestä", "SSE.Views.DocumentHolder.textFromUrl": "From URL", "SSE.Views.DocumentHolder.textGrowthTrend": "Growth trend", "SSE.Views.DocumentHolder.textLinearTrend": "Linear trend", "SSE.Views.DocumentHolder.textListSettings": "Luette<PERSON> asetukset", "SSE.Views.DocumentHolder.textMacro": "Assign <PERSON>", "SSE.Views.DocumentHolder.textMax": "Max", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "More functions", "SSE.Views.DocumentHolder.textMoreFormats": "More formats", "SSE.Views.DocumentHolder.textNone": "None", "SSE.Views.DocumentHolder.textNumbering": "Numerointi", "SSE.Views.DocumentHolder.textReplace": "<PERSON><PERSON><PERSON> k<PERSON>va", "SSE.Views.DocumentHolder.textResetCrop": "Reset crop", "SSE.Views.DocumentHolder.textRotate": "<PERSON><PERSON>r<PERSON>", "SSE.Views.DocumentHolder.textRotate270": "Kierrä 90° vastapäivään", "SSE.Views.DocumentHolder.textRotate90": "Kierrä 90° myötäpäivään", "SSE.Views.DocumentHolder.textSaveAsPicture": "<PERSON><PERSON><PERSON> k<PERSON>", "SSE.Views.DocumentHolder.textSeries": "Series", "SSE.Views.DocumentHolder.textShapeAlignBottom": "<PERSON><PERSON><PERSON> alas", "SSE.Views.DocumentHolder.textShapeAlignCenter": "<PERSON><PERSON><PERSON> kes<PERSON>e", "SSE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON><PERSON> vasen", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "<PERSON><PERSON><PERSON> kes<PERSON>e", "SSE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON><PERSON> o<PERSON>a", "SSE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "Sum", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "Vapauta ruudut", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "<PERSON><PERSON><PERSON> muotoiset luettelomerkit", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Valintaruutusymbolit", "SSE.Views.DocumentHolder.tipMarkersDash": "Ranskalaiset viivat", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Täytetty vinoneliö -luettelomerkit", "SSE.Views.DocumentHolder.tipMarkersFRound": "Täytetty ympyrä -luettelomerkit", "SSE.Views.DocumentHolder.tipMarkersFSquare": "<PERSON><PERSON><PERSON><PERSON> -luettelomerkit", "SSE.Views.DocumentHolder.tipMarkersHRound": "Ontot pyöreät luettelomerkit", "SSE.Views.DocumentHolder.tipMarkersStar": "Tähti-luettelomerkit", "SSE.Views.DocumentHolder.topCellText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAddComment": "Lisää kommentti", "SSE.Views.DocumentHolder.txtAddNamedRange": "Määrittele nimi", "SSE.Views.DocumentHolder.txtArrange": "Järjestä", "SSE.Views.DocumentHolder.txtAscending": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Sovita automaattisesti sa<PERSON>en leveys", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Sovita automaattisesti rivin korkeus", "SSE.Views.DocumentHolder.txtAverage": "Average", "SSE.Views.DocumentHolder.txtCellFormat": "Format cells", "SSE.Views.DocumentHolder.txtClear": "Tyhjennä", "SSE.Views.DocumentHolder.txtClearAll": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearComments": "Ko<PERSON>ntit", "SSE.Views.DocumentHolder.txtClearFormat": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearHyper": "Linkit", "SSE.Views.DocumentHolder.txtClearPivotField": "Clear filter from {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Tyhjennä valitut kipinäviiva ryhmät", "SSE.Views.DocumentHolder.txtClearSparklines": "Tyhjennä valitut kipinäviivat", "SSE.Views.DocumentHolder.txtClearText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCollapse": "Tiivistä", "SSE.Views.DocumentHolder.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON> sarake", "SSE.Views.DocumentHolder.txtColumnWidth": "<PERSON><PERSON> sa<PERSON>en leveys", "SSE.Views.DocumentHolder.txtCondFormat": "Conditional formatting", "SSE.Views.DocumentHolder.txtCopy": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCount": "Count", "SSE.Views.DocumentHolder.txtCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Mukautettu sarakkeen leveys", "SSE.Views.DocumentHolder.txtCustomRowHeight": "<PERSON><PERSON><PERSON><PERSON> rivin korkeus", "SSE.Views.DocumentHolder.txtCustomSort": "Custom sort", "SSE.Views.DocumentHolder.txtCut": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDateLong": "Long Date", "SSE.Views.DocumentHolder.txtDateShort": "Short Date", "SSE.Views.DocumentHolder.txtDelete": "Poista", "SSE.Views.DocumentHolder.txtDelField": "Poista", "SSE.Views.DocumentHolder.txtDescending": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDifference": "Difference from", "SSE.Views.DocumentHolder.txtDistribHor": "Distribute horizontally", "SSE.Views.DocumentHolder.txtDistribVert": "Distribute vertically", "SSE.Views.DocumentHolder.txtEditComment": "Muokkaa kommenttia", "SSE.Views.DocumentHolder.txtEditObject": "Muokkaa objektia", "SSE.Views.DocumentHolder.txtExpand": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtExpandCollapse": "Expand/Collapse", "SSE.Views.DocumentHolder.txtExpandEntire": "Expand Entire Field", "SSE.Views.DocumentHolder.txtFieldSettings": "Field settings", "SSE.Views.DocumentHolder.txtFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtFilterCellColor": "Suodata solun värin mukaan", "SSE.Views.DocumentHolder.txtFilterFontColor": "Suodata fontin v<PERSON><PERSON> mukaan", "SSE.Views.DocumentHolder.txtFilterValue": "<PERSON><PERSON><PERSON> valitun solun arvon mukaan", "SSE.Views.DocumentHolder.txtFormula": "Lisää funktio", "SSE.Views.DocumentHolder.txtFraction": "Murtoluku", "SSE.Views.DocumentHolder.txtGeneral": "General", "SSE.Views.DocumentHolder.txtGetLink": "Get link to this range", "SSE.Views.DocumentHolder.txtGrandTotal": "Grand total", "SSE.Views.DocumentHolder.txtGroup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtHide": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtIndex": "Ha<PERSON>mist<PERSON>", "SSE.Views.DocumentHolder.txtInsert": "Lisää", "SSE.Views.DocumentHolder.txtInsHyperlink": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtInsImage": "Lisää kuva tiedostosta", "SSE.Views.DocumentHolder.txtInsImageUrl": "Lisää kuva verkko-osoitteesta", "SSE.Views.DocumentHolder.txtLabelFilter": "Label filters", "SSE.Views.DocumentHolder.txtMax": "Max", "SSE.Views.DocumentHolder.txtMin": "Min", "SSE.Views.DocumentHolder.txtMoreOptions": "More options", "SSE.Views.DocumentHolder.txtNormal": "No calculation", "SSE.Views.DocumentHolder.txtNumber": "Numero", "SSE.Views.DocumentHolder.txtNumFormat": "Number Format", "SSE.Views.DocumentHolder.txtPaste": "Liit<PERSON>", "SSE.Views.DocumentHolder.txtPercent": "% of", "SSE.Views.DocumentHolder.txtPercentage": "Percentage", "SSE.Views.DocumentHolder.txtPercentDiff": "% difference from", "SSE.Views.DocumentHolder.txtPercentOfCol": "% of column total", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% of grand total", "SSE.Views.DocumentHolder.txtPercentOfParent": "% of parent total", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% of parent column total", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% of parent row total", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% running total in", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% of row total", "SSE.Views.DocumentHolder.txtPivotSettings": "Pivot Table settings", "SSE.Views.DocumentHolder.txtProduct": "Product", "SSE.Views.DocumentHolder.txtRankAscending": "Rank smallest to largest", "SSE.Views.DocumentHolder.txtRankDescending": "Rank largest to smallest", "SSE.Views.DocumentHolder.txtReapply": "Käytä <PERSON>", "SSE.Views.DocumentHolder.txtRefresh": "Refresh", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON> rivi", "SSE.Views.DocumentHolder.txtRowHeight": "<PERSON><PERSON> rivin korkeus", "SSE.Views.DocumentHolder.txtRunTotal": "Running total in", "SSE.Views.DocumentHolder.txtScientific": "Scientific", "SSE.Views.DocumentHolder.txtSelect": "Valitse", "SSE.Views.DocumentHolder.txtShiftDown": "<PERSON><PERSON><PERSON>da solut alas", "SSE.Views.DocumentHolder.txtShiftLeft": "<PERSON><PERSON><PERSON>da solut vasemmalle", "SSE.Views.DocumentHolder.txtShiftRight": "<PERSON><PERSON><PERSON>da solut o<PERSON>alle", "SSE.Views.DocumentHolder.txtShiftUp": "<PERSON><PERSON><PERSON>da solut yl<PERSON>s", "SSE.Views.DocumentHolder.txtShow": "Näytä", "SSE.Views.DocumentHolder.txtShowAs": "Show values as", "SSE.Views.DocumentHolder.txtShowComment": "Show comment", "SSE.Views.DocumentHolder.txtShowDetails": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSort": "Lajittele", "SSE.Views.DocumentHolder.txtSortCellColor": "Valittu solun värin y<PERSON>häällä", "SSE.Views.DocumentHolder.txtSortFontColor": "Valittu fontin väri y<PERSON>", "SSE.Views.DocumentHolder.txtSortOption": "More sort options", "SSE.Views.DocumentHolder.txtSparklines": "Kipinäviivat", "SSE.Views.DocumentHolder.txtSubtotalField": "Subtotal", "SSE.Views.DocumentHolder.txtSum": "Sum", "SSE.Views.DocumentHolder.txtSummarize": "Summarize values by", "SSE.Views.DocumentHolder.txtText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtTextAdvanced": "<PERSON><PERSON><PERSON> as<PERSON>", "SSE.Views.DocumentHolder.txtTime": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtTop10": "Top 10", "SSE.Views.DocumentHolder.txtUngroup": "<PERSON><PERSON> ryhm<PERSON>s", "SSE.Views.DocumentHolder.txtValueFieldSettings": "Value field settings", "SSE.Views.DocumentHolder.txtValueFilter": "Value filters", "SSE.Views.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.closeButtonText": "Sulje", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Automatically update data from the linked sources", "SSE.Views.ExternalLinksDlg.textChange": "Change source", "SSE.Views.ExternalLinksDlg.textDelete": "Break links", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Break all links", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textOpen": "Open source", "SSE.Views.ExternalLinksDlg.textSource": "Lä<PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.textStatus": "Status", "SSE.Views.ExternalLinksDlg.textUnknown": "Tuntematon", "SSE.Views.ExternalLinksDlg.textUpdate": "Update values", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Update all", "SSE.Views.ExternalLinksDlg.textUpdating": "Updating...", "SSE.Views.ExternalLinksDlg.txtTitle": "External links", "SSE.Views.FieldSettingsDialog.strLayout": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.strSubtotals": "Subtotals", "SSE.Views.FieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.FieldSettingsDialog.textReport": "Report form", "SSE.Views.FieldSettingsDialog.textTitle": "Field settings", "SSE.Views.FieldSettingsDialog.txtAverage": "Average", "SSE.Views.FieldSettingsDialog.txtBlank": "Insert blank rows after each item", "SSE.Views.FieldSettingsDialog.txtBottom": "Show at bottom of group", "SSE.Views.FieldSettingsDialog.txtCompact": "Compact", "SSE.Views.FieldSettingsDialog.txtCount": "Count", "SSE.Views.FieldSettingsDialog.txtCountNums": "Count numbers", "SSE.Views.FieldSettingsDialog.txtCustomName": "Custom name", "SSE.Views.FieldSettingsDialog.txtEmpty": "Show items with no data", "SSE.Views.FieldSettingsDialog.txtMax": "Max", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "Outline", "SSE.Views.FieldSettingsDialog.txtProduct": "Product", "SSE.Views.FieldSettingsDialog.txtRepeat": "Repeat items labels at each row", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Show subtotals", "SSE.Views.FieldSettingsDialog.txtSourceName": "Source name:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "Sum", "SSE.Views.FieldSettingsDialog.txtSummarize": "Functions for subtotals", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabular", "SSE.Views.FieldSettingsDialog.txtTop": "Show at top of group", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.ariaFileMenu": "Tiedostosisältö", "SSE.Views.FileMenu.btnBackCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnCloseEditor": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON> vali<PERSON>", "SSE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON>i", "SSE.Views.FileMenu.btnDownloadCaption": "La<PERSON>a kuten", "SSE.Views.FileMenu.btnExitCaption": "Sulje", "SSE.Views.FileMenu.btnExportToPDFCaption": "Export to PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "Open", "SSE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnHistoryCaption": "Versiohistoria", "SSE.Views.FileMenu.btnInfoCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON> tiedot", "SSE.Views.FileMenu.btnPrintCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnRecentFilesCaption": "Avaa viimeaikainen", "SSE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnReturnCaption": "<PERSON><PERSON><PERSON> ta<PERSON>", "SSE.Views.FileMenu.btnRightsCaption": "Käyttöoikeudet", "SSE.Views.FileMenu.btnSaveAsCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "<PERSON><PERSON><PERSON> kop<PERSON>", "SSE.Views.FileMenu.btnSettingsCaption": "Laajennetut <PERSON>", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "SSE.Views.FileMenu.btnToEditCaption": "Muokkaa työkirjaa", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Blank Spreadsheet", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Käytä", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Add Author", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Add Text", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "<PERSON><PERSON> k<PERSON>öoikeuk<PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Viimeinen muokka<PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "<PERSON>iimeks<PERSON> muo<PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Omistaja", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON> ovat o<PERSON>t", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Spreadsheet info", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Laskutaulukon otsikko", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Uploaded", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "K<PERSON><PERSON>ä", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Access Rights", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "<PERSON><PERSON> k<PERSON>öoikeuk<PERSON>", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON><PERSON> ovat o<PERSON>t", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Käytä", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tila", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Use 1904 date system", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Decimal separator", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Dictionary language", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Enable iterative calculation", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Pika", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "<PERSON><PERSON> k<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Esimerkki: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Show function tooltip", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Show horizontal scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Sivuuta ISOLLA kirjoitetut sanat", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "<PERSON><PERSON>uta sanat, joissa on numeroita", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Macros settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Maximum change", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Maximum iterations", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Näytä Liittämisasetukset -painike kun Liitä-toimintoa käytetään", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "R1C1 reference style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Esimerkki:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTL interface", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Show comments in sheet", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Näytä toisten käyttäjien tekemät muutokset", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Näytä ratkaistut kommentit", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Snapped to the grid while scrolling", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Välilehden tyyli", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "K<PERSON>yttöliitt<PERSON><PERSON><PERSON> teema", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Thousands separator", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Mittausyksikkö", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Use separators based on regional settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Show vertical scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "<PERSON><PERSON> arvo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Joka 10 minuutti", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Joka 30 minuutti", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Joka 5 minuutti", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "<PERSON><PERSON> tunti", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Automaattinen palautus", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Automaattinen talletus", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "<PERSON><PERSON> k<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "Täytä", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Tallennetaan väliversioita", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "Viiva", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Reference Style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "Advanced settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "AutoCorrect options...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Belarusian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Bulgarian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Catalan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Välimuistin o<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Calculating", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Senttimetriä", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Y<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Czech", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "<PERSON>ok<PERSON><PERSON> p<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Danish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "<PERSON><PERSON><PERSON><PERSON> ja tallennus", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Greek", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Spanish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Reaaliaikainen yhteismuokkaus. Kaikki muutokset tallennetaan automaattisesti", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Finnish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "French", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Hungarian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "Armenian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonesian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Italian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Japanese", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Korean", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Lao", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Latvian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "kuten OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "<PERSON>yn<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Norwegian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Dutch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Polish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Proofing", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Portuguese (Brazil)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "Portuguese (Portugal)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Näytä <PERSON>-painike editorin yläpalkissa", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Region", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Romanian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Venäjän kieli", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "<PERSON><PERSON> k<PERSON> kaikki", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "<PERSON><PERSON> k<PERSON>n kaikki makrot ilman ilmoitusta", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "<PERSON><PERSON> k<PERSON> tuki ruud<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Default sheet direction", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "This setting will affect only the new sheets", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Left-to-right", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Right-to-left", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Slovak", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Slovenian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Poista kaikki k<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Poista käytöstä kaikki makrot ilman ilmoitusta", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "<PERSON><PERSON><PERSON><PERSON> \"<PERSON><PERSON>na\" -pain<PERSON>tta synkronoidaksesi sinun ja muiden tekemät muutokset", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Swedish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Käytä työkalurivin väriä välilehtien taustalla", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Turkish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ukrainian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Käytä Alt-näppäintä siirtyäksesi käyttöliittymässä näppäimistön avulla", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Käytä Option-näppäintä siirtyäksesi käyttöliittymässä näppäimistön avulla", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vietnamese", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Näyt<PERSON> ilmoitus", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Poista kaikki il<PERSON> k<PERSON>ytöstä", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "kuten Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "Chinese", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Varo<PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON><PERSON><PERSON> kera", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Protect Spreadsheet", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Allekirjoituksen kera", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Valid signatures have been added to the spreadsheet.<br>The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Ensure the integrity of the spreadsheet by adding an<br>invisible digital signature", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Muokkaa työkirjaa", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Editing will remove signatures from the spreadsheet.<br>Continue?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "This spreadsheet has been protected by password", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Encrypt this spreadsheet with a password", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "This spreadsheet needs to be signed.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Valid signatures have been added to the spreadsheet. The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Some of the digital signatures in spreadsheet are invalid or could not be verified. The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Näytä allekirjoitukset", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Download as", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "<PERSON><PERSON><PERSON> kop<PERSON>", "SSE.Views.FillSeriesDialog.textAuto": "AutoFill", "SSE.Views.FillSeriesDialog.textCols": "Columns", "SSE.Views.FillSeriesDialog.textDate": "Date", "SSE.Views.FillSeriesDialog.textDateUnit": "Date unit", "SSE.Views.FillSeriesDialog.textDay": "Day", "SSE.Views.FillSeriesDialog.textGrowth": "Growth", "SSE.Views.FillSeriesDialog.textLinear": "<PERSON><PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textMonth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textRows": "Rivit", "SSE.Views.FillSeriesDialog.textSeries": "Series in", "SSE.Views.FillSeriesDialog.textStep": "Step value", "SSE.Views.FillSeriesDialog.textStop": "Stop value", "SSE.Views.FillSeriesDialog.textTitle": "Series", "SSE.Views.FillSeriesDialog.textTrend": "Trend", "SSE.Views.FillSeriesDialog.textType": "Tyyppi", "SSE.Views.FillSeriesDialog.textWeek": "Weekday", "SSE.Views.FillSeriesDialog.textYear": "Year", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FormatRulesEditDlg.fillColor": "Taustan väri", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Varo<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 color scale", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 color scale", "SSE.Views.FormatRulesEditDlg.textAllBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textAppearance": "Bar appearance", "SSE.Views.FormatRulesEditDlg.textApply": "Apply to range", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automaattinen", "SSE.Views.FormatRulesEditDlg.textAxis": "Axis", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Bar direction", "SSE.Views.FormatRulesEditDlg.textBold": "Bold", "SSE.Views.FormatRulesEditDlg.textBorder": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersColor": "Borders color", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Border style", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Alareunukset", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Cannot add the conditional formatting.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Cell midpoint", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Pys<PERSON>reunust<PERSON> si<PERSON>", "SSE.Views.FormatRulesEditDlg.textClear": "Tyhjennä", "SSE.Views.FormatRulesEditDlg.textColor": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textContext": "Context", "SSE.Views.FormatRulesEditDlg.textCustom": "Muka<PERSON>ttu", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Diagonaalinen alareunus", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Diagonaalinen yläreunus", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Enter a valid formula.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "The formula you entered does not evaluate to a number, date, time or string.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Enter a value.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "The value you entered is not a valid number, date, time or string.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "The value for the {0} must be greater than the value for the {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Enter a number between {0} and {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Täytä", "SSE.Views.FormatRulesEditDlg.textFormat": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormula": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textGradient": "Gradient", "SSE.Views.FormatRulesEditDlg.textIconLabel": "when {0} {1} and", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "when {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "when value is", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "One or more icon data ranges overlap.<br>Adjust icon data range values so that the ranges do not overlap.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Icon style", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Sisäreunukset", "SSE.Views.FormatRulesEditDlg.textInvalid": "Invalid data range.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "VIRHE! Virheellinen solujen tietoalue", "SSE.Views.FormatRulesEditDlg.textItalic": "Kursivoit<PERSON>", "SSE.Views.FormatRulesEditDlg.textItem": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Left to right", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "<PERSON><PERSON><PERSON><PERSON> re<PERSON>", "SSE.Views.FormatRulesEditDlg.textLongBar": "longest bar", "SSE.Views.FormatRulesEditDlg.textMaximum": "Maximum", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Maxpoint", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Vaakareunusten sisällä", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Midpoint", "SSE.Views.FormatRulesEditDlg.textMinimum": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Minpoint", "SSE.Views.FormatRulesEditDlg.textNegative": "Negative", "SSE.Views.FormatRulesEditDlg.textNewColor": "Lisää uusi mukautettu väri", "SSE.Views.FormatRulesEditDlg.textNoBorders": "No borders", "SSE.Views.FormatRulesEditDlg.textNone": "None", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "One or more of the specified values is not a valid percentage.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "The specified {0} value is not a valid percentage.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "One or more of the specified values is not a valid percentile.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "The specified {0} value is not a valid percentile.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPercent": "Prosenttia", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentile", "SSE.Views.FormatRulesEditDlg.textPosition": "Position", "SSE.Views.FormatRulesEditDlg.textPositive": "Positive", "SSE.Views.FormatRulesEditDlg.textPresets": "Presets", "SSE.Views.FormatRulesEditDlg.textPreview": "Preview", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "You cannot use relative references in conditional formatting criteria for color scales, data bars, and icon sets.", "SSE.Views.FormatRulesEditDlg.textReverse": "Reverse icons order", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Right to left", "SSE.Views.FormatRulesEditDlg.textRightBorders": "<PERSON><PERSON><PERSON> reun<PERSON>", "SSE.Views.FormatRulesEditDlg.textRule": "Rule", "SSE.Views.FormatRulesEditDlg.textSameAs": "Same as positive", "SSE.Views.FormatRulesEditDlg.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.FormatRulesEditDlg.textShortBar": "shortest bar", "SSE.Views.FormatRulesEditDlg.textShowBar": "Show bar only", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Show icon only", "SSE.Views.FormatRulesEditDlg.textSingleRef": "This type of reference cannot be used in a conditional formatting formula.<br>Change the reference to a single cell, or use the reference with a worksheet function, such as =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Solid", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Strikeout", "SSE.Views.FormatRulesEditDlg.textSubscript": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Yläindeksi", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Top borders", "SSE.Views.FormatRulesEditDlg.textUnderline": "Alleviivaus", "SSE.Views.FormatRulesEditDlg.tipBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Number format", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Accounting", "SSE.Views.FormatRulesEditDlg.txtCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDate": "Date", "SSE.Views.FormatRulesEditDlg.txtDateLong": "Long date", "SSE.Views.FormatRulesEditDlg.txtDateShort": "Short date", "SSE.Views.FormatRulesEditDlg.txtEmpty": "This field is required", "SSE.Views.FormatRulesEditDlg.txtFraction": "Murtoluku", "SSE.Views.FormatRulesEditDlg.txtGeneral": "General", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "No icon", "SSE.Views.FormatRulesEditDlg.txtNumber": "Numero", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Percentage", "SSE.Views.FormatRulesEditDlg.txtScientific": "Scientific", "SSE.Views.FormatRulesEditDlg.txtText": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtTime": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Edit formatting rule", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "New formatting rule", "SSE.Views.FormatRulesManagerDlg.guestText": "Vierailija", "SSE.Views.FormatRulesManagerDlg.lockText": "Lukittu", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 std dev above average", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 std dev below average", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 std dev above average", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 std dev below average", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 std dev above average", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 std dev below average", "SSE.Views.FormatRulesManagerDlg.textAbove": "Keskimääräistä enemmän", "SSE.Views.FormatRulesManagerDlg.textApply": "Apply to", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Cell value begins with", "SSE.Views.FormatRulesManagerDlg.textBelow": "Below average", "SSE.Views.FormatRulesManagerDlg.textBetween": "is between {0} and {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Cell value", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Graded color scale", "SSE.Views.FormatRulesManagerDlg.textContains": "Cell value contains", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Cell contains a blank value", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Cell contains an error", "SSE.Views.FormatRulesManagerDlg.textDelete": "Poista", "SSE.Views.FormatRulesManagerDlg.textDown": "Move rule down", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Duplicate values", "SSE.Views.FormatRulesManagerDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEnds": "Cell value ends with", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Equal to or above average", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Equal to or below average", "SSE.Views.FormatRulesManagerDlg.textFormat": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Icon set", "SSE.Views.FormatRulesManagerDlg.textNew": "<PERSON>us<PERSON>", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "is not between {0} and {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Cell value does not contain", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Cell does not contain a blank value", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Cell does not contain an error", "SSE.Views.FormatRulesManagerDlg.textRules": "Rules", "SSE.Views.FormatRulesManagerDlg.textScope": "Show formatting rules for", "SSE.Views.FormatRulesManagerDlg.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.FormatRulesManagerDlg.textSelection": "Current selection", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "This pivot", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "This worksheet", "SSE.Views.FormatRulesManagerDlg.textThisTable": "This table", "SSE.Views.FormatRulesManagerDlg.textUnique": "Unique values", "SSE.Views.FormatRulesManagerDlg.textUp": "Move rule up", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "<PERSON><PERSON> k<PERSON>j<PERSON> on muokkaamassa tätä elementtiä. ", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Conditional formatting", "SSE.Views.FormatSettingsDialog.textCategory": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textDecimal": "Decimal", "SSE.Views.FormatSettingsDialog.textFormat": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textLinked": "Linked to source", "SSE.Views.FormatSettingsDialog.textSeparator": "Use 1000 separator", "SSE.Views.FormatSettingsDialog.textSymbols": "Symbolit", "SSE.Views.FormatSettingsDialog.textTitle": "Number format", "SSE.Views.FormatSettingsDialog.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtAs10": "As tenths (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "As hundredths (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "As sixteenths (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "As halves (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "As fourths (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "As eighths (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustom": "Muka<PERSON>ttu", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Please enter the custom number format carefully. Spreadsheet Editor does not check custom formats for errors that may affect the xlsx file.", "SSE.Views.FormatSettingsDialog.txtDate": "Date", "SSE.Views.FormatSettingsDialog.txtFraction": "Murtoluku", "SSE.Views.FormatSettingsDialog.txtGeneral": "General", "SSE.Views.FormatSettingsDialog.txtNone": "None", "SSE.Views.FormatSettingsDialog.txtNumber": "Numero", "SSE.Views.FormatSettingsDialog.txtPercentage": "Percentage", "SSE.Views.FormatSettingsDialog.txtSample": "Sample:", "SSE.Views.FormatSettingsDialog.txtScientific": "Scientific", "SSE.Views.FormatSettingsDialog.txtText": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtTime": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtUpto1": "Up to one digit (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Up to two digits (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Up to three digits (131/135)", "SSE.Views.FormulaDialog.sDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.textGroupDescription": "Valitse funktioryhmä", "SSE.Views.FormulaDialog.textListDescription": "Valitse funktio", "SSE.Views.FormulaDialog.txtRecommended": "Recommended", "SSE.Views.FormulaDialog.txtSearch": "Etsi", "SSE.Views.FormulaDialog.txtTitle": "Lisää funktio", "SSE.Views.FormulaTab.capBtnRemoveArr": "Remove Arrows", "SSE.Views.FormulaTab.capBtnTraceDep": "Trace Dependents", "SSE.Views.FormulaTab.capBtnTracePrec": "Trace Precedents", "SSE.Views.FormulaTab.textAutomatic": "Automaattinen", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Calculate current sheet", "SSE.Views.FormulaTab.textCalculateWorkbook": "Calculate workbook", "SSE.Views.FormulaTab.textManual": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculate": "Calculate", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Calculate the entire workbook", "SSE.Views.FormulaTab.tipRemoveArr": "Remove the arrows drawn by Trace Precedents or Trace Dependents", "SSE.Views.FormulaTab.tipShowFormulas": "Display the formula in each cell instead of the resulting value", "SSE.Views.FormulaTab.tipTraceDep": "Show arrows that indicate which cells are affected by the value of the selected cell", "SSE.Views.FormulaTab.tipTracePrec": "Show arrows that indicate which cells affect the value of the selected cell", "SSE.Views.FormulaTab.tipWatch": "Add cells to the Watch Window list", "SSE.Views.FormulaTab.txtAdditional": "Lisä", "SSE.Views.FormulaTab.txtAutosum": "Autosum", "SSE.Views.FormulaTab.txtAutosumTip": "Yhteenveto", "SSE.Views.FormulaTab.txtCalculation": "Calculation", "SSE.Views.FormulaTab.txtFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormulaTip": "Lisää funktio", "SSE.Views.FormulaTab.txtMore": "More functions", "SSE.Views.FormulaTab.txtRecent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtRemDep": "Remove Dependents Arrows", "SSE.Views.FormulaTab.txtRemPrec": "Remove Precedents Arrows", "SSE.Views.FormulaTab.txtShowFormulas": "Show Formulas", "SSE.Views.FormulaTab.txtWatch": "Watch Window", "SSE.Views.FormulaWizard.textAny": "any", "SSE.Views.FormulaWizard.textArgument": "Argument", "SSE.Views.FormulaWizard.textFunction": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textFunctionRes": "Function result", "SSE.Views.FormulaWizard.textHelp": "Help on this function", "SSE.Views.FormulaWizard.textLogical": "logical", "SSE.Views.FormulaWizard.textNoArgs": "This function has no arguments", "SSE.Views.FormulaWizard.textNoArgsDesc": "this argument has no description", "SSE.Views.FormulaWizard.textNumber": "numero", "SSE.Views.FormulaWizard.textReadMore": "Read more", "SSE.Views.FormulaWizard.textRef": "reference", "SSE.Views.FormulaWizard.textText": "text", "SSE.Views.FormulaWizard.textTitle": "Function arguments", "SSE.Views.FormulaWizard.textValue": "Formula result", "SSE.Views.GoalSeekDlg.textChangingCell": "By changing cell", "SSE.Views.GoalSeekDlg.textDataRangeError": "The formula is missing a range", "SSE.Views.GoalSeekDlg.textMustContainFormula": "The cell must contain a formula", "SSE.Views.GoalSeekDlg.textMustContainValue": "Cell must contain a value", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Formula in cell must result in a number", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Reference must be to a single cell", "SSE.Views.GoalSeekDlg.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.GoalSeekDlg.textSetCell": "Set cell", "SSE.Views.GoalSeekDlg.textTitle": "Goal seek", "SSE.Views.GoalSeekDlg.textToValue": "To value", "SSE.Views.GoalSeekDlg.txtEmpty": "This field is required", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.GoalSeekStatusDlg.textContinue": "Continue", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Current value:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Goal seeking with cell {0} found a solution.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Goal seeking with cell {0} may not have found a solution.", "SSE.Views.GoalSeekStatusDlg.textPause": "Keskeytä", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Goal seeking with cell {0} on iteration #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "Step", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Target value:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Goal seek status", "SSE.Views.HeaderFooterDialog.textAlign": "Align with page margins", "SSE.Views.HeaderFooterDialog.textAll": "<PERSON><PERSON><PERSON> sivut", "SSE.Views.HeaderFooterDialog.textBold": "Bold", "SSE.Views.HeaderFooterDialog.textCenter": "Center", "SSE.Views.HeaderFooterDialog.textColor": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textDate": "Date", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Erilainen alkusivu", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Eril<PERSON><PERSON> parittomat ja parilliset sivut", "SSE.Views.HeaderFooterDialog.textEven": "<PERSON><PERSON><PERSON> sivu", "SSE.Views.HeaderFooterDialog.textFileName": "File name", "SSE.Views.HeaderFooterDialog.textFirst": "Ensimmäinen sivu", "SSE.Views.HeaderFooterDialog.textFooter": "Footer", "SSE.Views.HeaderFooterDialog.textHeader": "Header", "SSE.Views.HeaderFooterDialog.textImage": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textInsert": "Lisää", "SSE.Views.HeaderFooterDialog.textItalic": "Kursivoit<PERSON>", "SSE.Views.HeaderFooterDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textMaxError": "The text string you entered is too long. Reduce the number of characters used.", "SSE.Views.HeaderFooterDialog.textNewColor": "Lisää uusi mukautettu väri", "SSE.Views.HeaderFooterDialog.textOdd": "Pariton sivu", "SSE.Views.HeaderFooterDialog.textPageCount": "Page count", "SSE.Views.HeaderFooterDialog.textPageNum": "Sivunumero", "SSE.Views.HeaderFooterDialog.textPresets": "Presets", "SSE.Views.HeaderFooterDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textScale": "Scale with document", "SSE.Views.HeaderFooterDialog.textSheet": "<PERSON><PERSON><PERSON> nimi", "SSE.Views.HeaderFooterDialog.textStrikeout": "Strikethrough", "SSE.Views.HeaderFooterDialog.textSubscript": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textSuperscript": "Yläindeksi", "SSE.Views.HeaderFooterDialog.textTime": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textTitle": "Header/Footer settings", "SSE.Views.HeaderFooterDialog.textUnderline": "Alleviivaus", "SSE.Views.HeaderFooterDialog.tipFontName": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.tipFontSize": "Fonttikoko", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Näyttö", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Linkitä:", "SSE.Views.HyperlinkSettingsDialog.strRange": "Tietoalue", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Copy", "SSE.Views.HyperlinkSettingsDialog.textDefault": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Syötä kuvateksti tähän", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Syötä linkki tässä", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Syötä työkaluvinkki tähän", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Get link", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "<PERSON><PERSON><PERSON><PERSON> tie<PERSON>", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "VIRHE! Virheellinen solujen tietoalue", "SSE.Views.HyperlinkSettingsDialog.textNames": "Määritellyt nimet", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Valitse tiedosto", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Sheets", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Näyttövinkin teksti", "SSE.Views.HyperlinkSettingsDialog.textTitle": "<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Tämä kenttä tarvitaan", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "<PERSON><PERSON><PERSON><PERSON><PERSON> tied<PERSON>on tulisi olla verkko-osoite \"http://www.esimerkki.com\" muodossa", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Tämä kenttä on rajoitettu 2083 merkkiin", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Syötä verkko-osoite tai valitse tiedosto", "SSE.Views.ImageSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textAdvanced": "Näytä laajennetut asetukset", "SSE.Views.ImageSettings.textCrop": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFill": "Täytä", "SSE.Views.ImageSettings.textCropFit": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropToShape": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textEditObject": "Muokkaa objektia", "SSE.Views.ImageSettings.textFlip": "Flip", "SSE.Views.ImageSettings.textFromFile": "Tiedostosta", "SSE.Views.ImageSettings.textFromStorage": "Tallennusvälineestä", "SSE.Views.ImageSettings.textFromUrl": "URL-osoitteesta", "SSE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textHint270": "Kierrä 90° vastapäivään", "SSE.Views.ImageSettings.textHint90": "Kierrä 90° myötäpäivään", "SSE.Views.ImageSettings.textHintFlipH": "Käännä vaakasuunnassa", "SSE.Views.ImageSettings.textHintFlipV": "Käännä pysty<PERSON>unnassa", "SSE.Views.ImageSettings.textInsert": "<PERSON><PERSON><PERSON> k<PERSON>va", "SSE.Views.ImageSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textRecentlyUsed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textResetCrop": "Reset crop", "SSE.Views.ImageSettings.textRotate90": "Kierrä 90°", "SSE.Views.ImageSettings.textRotation": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textSize": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Don't move or size with cells", "SSE.Views.ImageSettingsAdvanced.textAlt": "Vaihtoehtoinen teksti", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Description", "SSE.Views.ImageSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart, or table.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Title", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Move but don't size with cells", "SSE.Views.ImageSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textSnap": "Cell snapping", "SSE.Views.ImageSettingsAdvanced.textTitle": "Kuva <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Move and size with cells", "SSE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImportFromXmlDialog.textDestination": "Choose, where to place the data", "SSE.Views.ImportFromXmlDialog.textExist": "Existing worksheet", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ImportFromXmlDialog.textNew": "New worksheet", "SSE.Views.ImportFromXmlDialog.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.ImportFromXmlDialog.textTitle": "Import data", "SSE.Views.ImportFromXmlDialog.txtEmpty": "This field is required", "SSE.Views.LeftMenu.ariaLeftMenu": "<PERSON><PERSON><PERSON> v<PERSON>", "SSE.Views.LeftMenu.tipAbout": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipChat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipComments": "Ko<PERSON>ntit", "SSE.Views.LeftMenu.tipFile": "Tiedosto", "SSE.Views.LeftMenu.tipPlugins": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSearch": "Etsi", "SSE.Views.LeftMenu.tipSpellcheck": "Spell checking", "SSE.Views.LeftMenu.tipSupport": "Palaute & Tuki", "SSE.Views.LeftMenu.txtDeveloper": "DEVELOPER MODE", "SSE.Views.LeftMenu.txtEditor": "Taulukkolaskentaohjelma", "SSE.Views.LeftMenu.txtLimit": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.txtTrial": "KOKEILUVERSIO", "SSE.Views.LeftMenu.txtTrialDev": "Kehittäjätilan kokeiluversio", "SSE.Views.MacroDialog.textMacro": "Macro name", "SSE.Views.MacroDialog.textTitle": "Assign macro", "SSE.Views.MainSettingsPrint.okButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLandscape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strMargins": "Marginaalit", "SSE.Views.MainSettingsPrint.strPortrait": "Pystysuunta", "SSE.Views.MainSettingsPrint.strPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPrintTitles": "Print titles", "SSE.Views.MainSettingsPrint.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strTop": "Yläosa", "SSE.Views.MainSettingsPrint.textActualSize": "Todellinen koko", "SSE.Views.MainSettingsPrint.textCustom": "Muka<PERSON>ttu", "SSE.Views.MainSettingsPrint.textCustomOptions": "Custom options", "SSE.Views.MainSettingsPrint.textFitCols": "So<PERSON>ta kaikki sa<PERSON> yh<PERSON>e sivulle", "SSE.Views.MainSettingsPrint.textFitPage": "<PERSON><PERSON>ta ta<PERSON> sivulle", "SSE.Views.MainSettingsPrint.textFitRows": "Sovita kaikki rivit yh<PERSON>e sivulle", "SSE.Views.MainSettingsPrint.textPageOrientation": "<PERSON><PERSON><PERSON> su<PERSON>", "SSE.Views.MainSettingsPrint.textPageScaling": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON><PERSON>n koko", "SSE.Views.MainSettingsPrint.textPrintGrid": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.MainSettingsPrint.textPrintHeadings": "<PERSON><PERSON><PERSON> ja <PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textRepeat": "Repeat...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Repeat columns at left", "SSE.Views.MainSettingsPrint.textRepeatTop": "Repeat rows at top", "SSE.Views.MainSettingsPrint.textSettings": "Asetukset:", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Olemassaolevia nimettyjä tietoalueita ei voida muokata ja uusia ei voida luoda<br>tällä hetkellä koska jotain niistä muokataan tällä hetkellä.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Määritelty nimi", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Varo<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Työkirja", "SSE.Views.NamedRangeEditDlg.textDataRange": "Tietoalue", "SSE.Views.NamedRangeEditDlg.textExistName": "VIRHE! Samanniminen tietoalue on jo olemassa", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Nimen tulee alkaa kirja<PERSON>lla tai alaviivalla ja se ei saa sisältää virheellisiä merkkejä.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "VIRHE! Virheellinen solun tietoalue", "SSE.Views.NamedRangeEditDlg.textIsLocked": "VIRHE! Toinen käyttäjä muokkaa tätä elementtiä.", "SSE.Views.NamedRangeEditDlg.textName": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textReservedName": "<PERSON><PERSON>, jota yrität kä<PERSON>tää, on jo viitattu solujen kaav<PERSON>sa. Ole hyvä ja käytä muuta nimeä.", "SSE.Views.NamedRangeEditDlg.textScope": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Tämä kenttä tarvitaan", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "<PERSON><PERSON><PERSON><PERSON> nime<PERSON>", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangePasteDlg.textNames": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangePasteDlg.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.closeButtonText": "Sulje", "SSE.Views.NameManagerDlg.guestText": "Vierailija", "SSE.Views.NameManagerDlg.lockText": "Lukittu", "SSE.Views.NameManagerDlg.textDataRange": "Tietoalue", "SSE.Views.NameManagerDlg.textDelete": "Poista", "SSE.Views.NameManagerDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEmpty": "Ei ole vielä luotu nimettyjä tietoalueita.<br><PERSON><PERSON> v<PERSON>hintään yksi nimetty tietoalue ja se ilmaantuu tähän kenttään.", "SSE.Views.NameManagerDlg.textFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterAll": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterDefNames": "Määritellyt nimet", "SSE.Views.NameManagerDlg.textFilterSheet": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterTableNames": "<PERSON>lukon nimet", "SSE.Views.NameManagerDlg.textFilterWorkbook": "<PERSON><PERSON> k<PERSON>ttävissä työkirjaan", "SSE.Views.NameManagerDlg.textNew": "<PERSON>us<PERSON>", "SSE.Views.NameManagerDlg.textnoNames": "Suodatintasi vastaa<PERSON>a nimett<PERSON>ä tietoal<PERSON>tta ei löyt<PERSON>.", "SSE.Views.NameManagerDlg.textRanges": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textScope": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textWorkbook": "Työkirja", "SSE.Views.NameManagerDlg.tipIsLocked": "<PERSON><PERSON> k<PERSON>j<PERSON> on muokkaamassa tätä elementtiä. ", "SSE.Views.NameManagerDlg.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.warnDelete": "Are you sure you want to delete the name {0}?", "SSE.Views.PageMarginsDialog.textBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textCenter": "Center on page", "SSE.Views.PageMarginsDialog.textHor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTitle": "Marginaalit", "SSE.Views.PageMarginsDialog.textTop": "Yläosa", "SSE.Views.PageMarginsDialog.textVert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textWarning": "Varo<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.warnCheckMargings": "<PERSON><PERSON><PERSON><PERSON> ovat virheellis<PERSON>ä", "SSE.Views.ParagraphSettings.strLineHeight": "Viivan väli", "SSE.Views.ParagraphSettings.strParagraphSpacing": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAdvanced": "Näytä laajennetut asetukset", "SSE.Views.ParagraphSettings.textAt": "/", "SSE.Views.ParagraphSettings.textAtLeast": "vähintään", "SSE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textExact": "Täsmälleen", "SSE.Views.ParagraphSettings.txtAutoText": "Automaattinen", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Määritellyt välilehdet ilmaantuvat tässä kentässä", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON><PERSON> is<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Sisennykset", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Line spacing", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Special", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Sisennykset & Asettelut", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Ka<PERSON>eel<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Spacing", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Yliviivaus", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Yläindeksi", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Välilehti", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Multiple", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Efektit", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Exactly", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "First line", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Riippuva", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Justified", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(ei mit<PERSON>än)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Poista", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Poista kaikki", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Määrittele", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Keskellä", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> asema", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Kappale - Laajenne<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Poista", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "<PERSON><PERSON>", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "<PERSON><PERSON>", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Items Name", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "<PERSON>us<PERSON>", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Calculated Items in", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "equals", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "does not end with", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "contains", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "does not contain", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "between", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "not between", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "does not equal", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "is greater than", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "on suurempi kuin tai yhtä<PERSON>uri kuin", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "on vähemmän kuin", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "on vä<PERSON>män tai yhtä<PERSON>uri kuin", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "begins with", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "does not begin with", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "ends with", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Show items for which the label:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Show items for which:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Käytä ? merkkiä esittämään mitä tahansa yksittäistä kirjainta", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Käytä * merkkiä esittämään mitä tahansa kirjainsar<PERSON>a", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "and", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Label filter", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Value filter", "SSE.Views.PivotGroupDialog.textAuto": "Auto", "SSE.Views.PivotGroupDialog.textBy": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textDays": "Days", "SSE.Views.PivotGroupDialog.textEnd": "Ending at", "SSE.Views.PivotGroupDialog.textError": "This field must be a numeric value", "SSE.Views.PivotGroupDialog.textGreaterError": "The end number must be greater than the start number", "SSE.Views.PivotGroupDialog.textHour": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMin": "Minutes", "SSE.Views.PivotGroupDialog.textMonth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textNumDays": "Number of days", "SSE.Views.PivotGroupDialog.textQuart": "Quarters", "SSE.Views.PivotGroupDialog.textSec": "Seconds", "SSE.Views.PivotGroupDialog.textStart": "Starting at", "SSE.Views.PivotGroupDialog.textYear": "Years", "SSE.Views.PivotGroupDialog.txtTitle": "Grouping", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "You can use Calculated Items for basic calculations between different items within a single field", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "<PERSON><PERSON>", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Insert into formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Item name", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Items", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Read more", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Insert Calculated Item in", "SSE.Views.PivotSettings.textAdvanced": "Näytä laajennetut asetukset", "SSE.Views.PivotSettings.textColumns": "Columns", "SSE.Views.PivotSettings.textFields": "Select fields", "SSE.Views.PivotSettings.textFilters": "Filters", "SSE.Views.PivotSettings.textRows": "Rivit", "SSE.Views.PivotSettings.textValues": "Arvot", "SSE.Views.PivotSettings.txtAddColumn": "Add to columns", "SSE.Views.PivotSettings.txtAddFilter": "Add to filters", "SSE.Views.PivotSettings.txtAddRow": "Add to rows", "SSE.Views.PivotSettings.txtAddValues": "Add to values", "SSE.Views.PivotSettings.txtFieldSettings": "Field settings", "SSE.Views.PivotSettings.txtMoveBegin": "Move to beginning", "SSE.Views.PivotSettings.txtMoveColumn": "Move to columns", "SSE.Views.PivotSettings.txtMoveDown": "Move down", "SSE.Views.PivotSettings.txtMoveEnd": "Move to end", "SSE.Views.PivotSettings.txtMoveFilter": "Move to filters", "SSE.Views.PivotSettings.txtMoveRow": "Move to rows", "SSE.Views.PivotSettings.txtMoveUp": "Move up", "SSE.Views.PivotSettings.txtMoveValues": "Move to values", "SSE.Views.PivotSettings.txtRemove": "Remove field", "SSE.Views.PivotSettingsAdvanced.strLayout": "Name and layout", "SSE.Views.PivotSettingsAdvanced.textAlt": "Vaihtoehtoinen teksti", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Description", "SSE.Views.PivotSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart or table.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Title", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Autofit column widths on update", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Tietoalue", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Tietolähde", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Display fields in report filter area", "SSE.Views.PivotSettingsAdvanced.textDown": "Down, then over", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Grand totals", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Field headers", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "VIRHE! Virheellinen solujen tietoalue", "SSE.Views.PivotSettingsAdvanced.textOver": "Over, then down", "SSE.Views.PivotSettingsAdvanced.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Show for columns", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Show field headers for rows and columns", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Show for rows", "SSE.Views.PivotSettingsAdvanced.textTitle": "Pivot Table - Advanced settings", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Report filter fields per column", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Report filter fields per row", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "This field is required", "SSE.Views.PivotSettingsAdvanced.txtName": "Name", "SSE.Views.PivotShowDetailDialog.textDescription": "Choose the field containing the detail you want to show:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Show Detail", "SSE.Views.PivotTable.capBlankRows": "Blank Rows", "SSE.Views.PivotTable.capGrandTotals": "Grand Totals", "SSE.Views.PivotTable.capLayout": "Report Layout", "SSE.Views.PivotTable.capSubtotals": "Subtotals", "SSE.Views.PivotTable.mniBottomSubtotals": "Show all subtotals at bottom of group", "SSE.Views.PivotTable.mniInsertBlankLine": "Insert blank line after each item", "SSE.Views.PivotTable.mniLayoutCompact": "Show in compact form", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Don't repeat all item labels", "SSE.Views.PivotTable.mniLayoutOutline": "Show in outline form", "SSE.Views.PivotTable.mniLayoutRepeat": "Repeat all item labels", "SSE.Views.PivotTable.mniLayoutTabular": "Show in tabular form", "SSE.Views.PivotTable.mniNoSubtotals": "Don't show subtotals", "SSE.Views.PivotTable.mniOffTotals": "Off for rows and columns", "SSE.Views.PivotTable.mniOnColumnsTotals": "On for columns only", "SSE.Views.PivotTable.mniOnRowsTotals": "On for rows only", "SSE.Views.PivotTable.mniOnTotals": "On for rows and columns", "SSE.Views.PivotTable.mniRemoveBlankLine": "Remove blank line after each item", "SSE.Views.PivotTable.mniTopSubtotals": "Show all subtotals at top of group", "SSE.Views.PivotTable.textColBanded": "Banded Columns", "SSE.Views.PivotTable.textColHeader": "Column headers", "SSE.Views.PivotTable.textRowBanded": "Banded Rows", "SSE.Views.PivotTable.textRowHeader": "Row Headers", "SSE.Views.PivotTable.tipCalculatedItems": "Calculated items", "SSE.Views.PivotTable.tipCreatePivot": "Insert Pivot Table", "SSE.Views.PivotTable.tipGrandTotals": "Show or hide grand totals", "SSE.Views.PivotTable.tipRefresh": "Update the information from data source", "SSE.Views.PivotTable.tipRefreshCurrent": "Update the information from data source for the current table", "SSE.Views.PivotTable.tipSelect": "Select entire pivot table", "SSE.Views.PivotTable.tipSubtotals": "Show or hide subtotals", "SSE.Views.PivotTable.txtCalculatedItems": "Calculated Items", "SSE.Views.PivotTable.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.PivotTable.txtCreate": "Lisää taulukko", "SSE.Views.PivotTable.txtExpandEntire": "Expand Entire Field", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Muka<PERSON>ttu", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Tumma", "SSE.Views.PivotTable.txtGroupPivot_Light": "Vaalea", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Keski<PERSON>koinen", "SSE.Views.PivotTable.txtPivotTable": "Pivot Table", "SSE.Views.PivotTable.txtRefresh": "Refresh", "SSE.Views.PivotTable.txtRefreshAll": "Refresh all", "SSE.Views.PivotTable.txtSelect": "Valitse", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Pivot Table Style Dark", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Pivot Table Style Light", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Pivot Table Style Medium", "SSE.Views.PrintSettings.btnDownload": "Save & Download", "SSE.Views.PrintSettings.btnExport": "Save & Export", "SSE.Views.PrintSettings.btnPrint": "Tallenna & Tulosta", "SSE.Views.PrintSettings.strBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strLandscape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strMargins": "Marginaalit", "SSE.Views.PrintSettings.strPortrait": "Pystysuunta", "SSE.Views.PrintSettings.strPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPrintTitles": "Print titles", "SSE.Views.PrintSettings.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strShow": "Näytä", "SSE.Views.PrintSettings.strTop": "Yläosa", "SSE.Views.PrintSettings.textActiveSheets": "Active sheets", "SSE.Views.PrintSettings.textActualSize": "Todellinen koko", "SSE.Views.PrintSettings.textAllSheets": "Kaikki taulukot", "SSE.Views.PrintSettings.textCurrentSheet": "Nykyinen taulukko", "SSE.Views.PrintSettings.textCustom": "Muka<PERSON>ttu", "SSE.Views.PrintSettings.textCustomOptions": "Custom options", "SSE.Views.PrintSettings.textFitCols": "So<PERSON>ta kaikki sa<PERSON> yh<PERSON>e sivulle", "SSE.Views.PrintSettings.textFitPage": "<PERSON><PERSON>ta ta<PERSON> sivulle", "SSE.Views.PrintSettings.textFitRows": "Sovita kaikki rivit yh<PERSON>e sivulle", "SSE.Views.PrintSettings.textHideDetails": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textIgnore": "Ignore print area", "SSE.Views.PrintSettings.textLayout": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintSettings.textMarginsNormal": "Normal", "SSE.Views.PrintSettings.textMarginsWide": "Leveä", "SSE.Views.PrintSettings.textPageOrientation": "<PERSON><PERSON><PERSON> su<PERSON>", "SSE.Views.PrintSettings.textPages": "Pages:", "SSE.Views.PrintSettings.textPageScaling": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPageSize": "<PERSON><PERSON>n koko", "SSE.Views.PrintSettings.textPrintGrid": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.PrintSettings.textPrintHeadings": "<PERSON><PERSON><PERSON> ja <PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPrintRange": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textRange": "Tietoalue", "SSE.Views.PrintSettings.textRepeat": "Repeat...", "SSE.Views.PrintSettings.textRepeatLeft": "Repeat columns at left", "SSE.Views.PrintSettings.textRepeatTop": "Repeat rows at top", "SSE.Views.PrintSettings.textSelection": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "Taulukon asetukset", "SSE.Views.PrintSettings.textShowDetails": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textShowGrid": "Show gridlines", "SSE.Views.PrintSettings.textShowHeadings": "Show rows and columns headings", "SSE.Views.PrintSettings.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textTitlePDF": "PDF settings", "SSE.Views.PrintSettings.textTo": "to", "SSE.Views.PrintSettings.txtMarginsLast": "Viimeinen mukautettu", "SSE.Views.PrintTitlesDialog.textFirstCol": "<PERSON><PERSON><PERSON><PERSON><PERSON> sarake", "SSE.Views.PrintTitlesDialog.textFirstRow": "First row", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Frozen columns", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Frozen rows", "SSE.Views.PrintTitlesDialog.textInvalidRange": "VIRHE! Virheellinen solujen tietoalue", "SSE.Views.PrintTitlesDialog.textLeft": "Repeat columns at left", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Don't repeat", "SSE.Views.PrintTitlesDialog.textRepeat": "Repeat...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Select range", "SSE.Views.PrintTitlesDialog.textTitle": "Print titles", "SSE.Views.PrintTitlesDialog.textTop": "Repeat rows at top", "SSE.Views.PrintWithPreview.txtActiveSheets": "Active sheets", "SSE.Views.PrintWithPreview.txtActualSize": "Actual size", "SSE.Views.PrintWithPreview.txtAllSheets": "Kaikki taulukot", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Apply to all sheets", "SSE.Views.PrintWithPreview.txtBothSides": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "Käännä pitkän sivun ympäri", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "Käännä lyhyen sivun ympäri", "SSE.Views.PrintWithPreview.txtBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCopies": "Copies", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Nykyinen taulukko", "SSE.Views.PrintWithPreview.txtCustom": "Muka<PERSON>ttu", "SSE.Views.PrintWithPreview.txtCustomOptions": "Custom options", "SSE.Views.PrintWithPreview.txtEmptyTable": "There is nothing to print because the table is empty", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "First page number:", "SSE.Views.PrintWithPreview.txtFitCols": "So<PERSON>ta kaikki sa<PERSON> yh<PERSON>e sivulle", "SSE.Views.PrintWithPreview.txtFitPage": "<PERSON><PERSON>ta ta<PERSON> sivulle", "SSE.Views.PrintWithPreview.txtFitRows": "Sovita kaikki rivit yh<PERSON>e sivulle", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Gridlines and headings", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Header/footer settings", "SSE.Views.PrintWithPreview.txtIgnore": "Ignore print area", "SSE.Views.PrintWithPreview.txtLandscape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMargins": "Marginaalit", "SSE.Views.PrintWithPreview.txtMarginsLast": "Viimeinen mukautettu", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Normal", "SSE.Views.PrintWithPreview.txtMarginsWide": "Leveä", "SSE.Views.PrintWithPreview.txtOf": "of {0}", "SSE.Views.PrintWithPreview.txtOneSide": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "SSE.Views.PrintWithPreview.txtPage": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Sivunum<PERSON>", "SSE.Views.PrintWithPreview.txtPageOrientation": "Page orientation", "SSE.Views.PrintWithPreview.txtPages": "Pages:", "SSE.Views.PrintWithPreview.txtPageSize": "<PERSON><PERSON>n koko", "SSE.Views.PrintWithPreview.txtPortrait": "Pystysuunta", "SSE.Views.PrintWithPreview.txtPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPrintGrid": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Print row and column headings", "SSE.Views.PrintWithPreview.txtPrintRange": "Print range", "SSE.Views.PrintWithPreview.txtPrintSides": "Print sides", "SSE.Views.PrintWithPreview.txtPrintTitles": "Print titles", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Tulosta PDF-tiedostoon", "SSE.Views.PrintWithPreview.txtRepeat": "Repeat...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Repeat columns at left", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Repeat rows at top", "SSE.Views.PrintWithPreview.txtRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSave": "Save", "SSE.Views.PrintWithPreview.txtScaling": "Sc<PERSON>", "SSE.Views.PrintWithPreview.txtSelection": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Settings of sheet", "SSE.Views.PrintWithPreview.txtSheet": "Sheet: {0}", "SSE.Views.PrintWithPreview.txtTo": "to", "SSE.Views.PrintWithPreview.txtTop": "Yläosa", "SSE.Views.ProtectDialog.textExistName": "ERROR! Range with such a title already exists", "SSE.Views.ProtectDialog.textInvalidName": "The range title must begin with a letter and may only contain letters, numbers, and spaces.", "SSE.Views.ProtectDialog.textInvalidRange": "VIRHE! Virheellinen solujen tietoalue", "SSE.Views.ProtectDialog.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.ProtectDialog.txtAllow": "Allow all users of this sheet to", "SSE.Views.ProtectDialog.txtAllowDescription": "You can unlock specific ranges for editing.", "SSE.Views.ProtectDialog.txtAllowRanges": "Allow edit ranges", "SSE.Views.ProtectDialog.txtAutofilter": "Use AutoFilter", "SSE.Views.ProtectDialog.txtDelCols": "Delete columns", "SSE.Views.ProtectDialog.txtDelRows": "Delete rows", "SSE.Views.ProtectDialog.txtEmpty": "This field is required", "SSE.Views.ProtectDialog.txtFormatCells": "Format cells", "SSE.Views.ProtectDialog.txtFormatCols": "Format columns", "SSE.Views.ProtectDialog.txtFormatRows": "Format rows", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Salasanat eivät vastaa toisiaan", "SSE.Views.ProtectDialog.txtInsCols": "Lisää sarakkeet", "SSE.Views.ProtectDialog.txtInsHyper": "Insert hyperlink", "SSE.Views.ProtectDialog.txtInsRows": "Insert rows", "SSE.Views.ProtectDialog.txtObjs": "Edit objects", "SSE.Views.ProtectDialog.txtOptional": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPassword": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPivot": "Use PivotTable and PivotChart", "SSE.Views.ProtectDialog.txtProtect": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRange": "Tietoalue", "SSE.Views.ProtectDialog.txtRangeName": "Title", "SSE.Views.ProtectDialog.txtRepeat": "<PERSON><PERSON> sa<PERSON>", "SSE.Views.ProtectDialog.txtScen": "Edit scenarios", "SSE.Views.ProtectDialog.txtSelLocked": "Select locked cells", "SSE.Views.ProtectDialog.txtSelUnLocked": "Select unlocked cells", "SSE.Views.ProtectDialog.txtSheetDescription": "Prevent unwanted changes from others by limiting their ability to edit.", "SSE.Views.ProtectDialog.txtSheetTitle": "Protect sheet", "SSE.Views.ProtectDialog.txtSort": "Sort", "SSE.Views.ProtectDialog.txtWarning": "Varoitus: <PERSON><PERSON> ka<PERSON> tai unoh<PERSON>, sitä ei voi palauttaa. Säilytä sitä turvallisessa pai<PERSON>.", "SSE.Views.ProtectDialog.txtWBDescription": "To prevent other users from viewing hidden worksheets, adding, moving, deleting, or hiding worksheets and renaming worksheets, you can protect the structure of your workbook with a password.", "SSE.Views.ProtectDialog.txtWBTitle": "Protect workbook structure", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "Anonymous", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "<PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Denied", "SSE.Views.ProtectedRangesEditDlg.textCanView": "Näytä", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "The range title must begin with a letter and may only contain letters, numbers, and spaces.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "VIRHE! Virheellinen solujen tietoalue", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Poista", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.ProtectedRangesEditDlg.textYou": "you", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Access to range", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "This field is required", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.txtRange": "Tietoalue", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "Title", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Only you can edit this range", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Start typing name or email", "SSE.Views.ProtectedRangesManagerDlg.guestText": "Vierailija", "SSE.Views.ProtectedRangesManagerDlg.lockText": "Lukittu", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "Poista", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "No protected ranges have been created yet.<br>Create at least one protected range and it will appear in this field.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.textNew": "<PERSON>us<PERSON>", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Protect sheet", "SSE.Views.ProtectedRangesManagerDlg.textRange": "Tietoalue", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "You can restrict editing or viewing ranges to selected people.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "Title", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "<PERSON><PERSON> k<PERSON>j<PERSON> on muokkaamassa tätä elementtiä. ", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Denied", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "Edit range", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "New range", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Protected ranges", "SSE.Views.ProtectedRangesManagerDlg.txtView": "Näytä", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Are you sure you want to delete the protected range {0}?<br>Anyone who has edit access to the spreadsheet will be able to edit content in the range.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Are you sure you want to delete the protected ranges?<br>Anyone who has edit access to the spreadsheet will be able to edit content in those ranges.", "SSE.Views.ProtectRangesDlg.guestText": "Vierailija", "SSE.Views.ProtectRangesDlg.lockText": "Lukittu", "SSE.Views.ProtectRangesDlg.textDelete": "Poista", "SSE.Views.ProtectRangesDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEmpty": "No ranges allowed for edit.", "SSE.Views.ProtectRangesDlg.textNew": "<PERSON>us<PERSON>", "SSE.Views.ProtectRangesDlg.textProtect": "Protect sheet", "SSE.Views.ProtectRangesDlg.textPwd": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRange": "Tietoalue", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Ranges unlocked by a password when sheet is protected (this works only for locked cells)", "SSE.Views.ProtectRangesDlg.textTitle": "Title", "SSE.Views.ProtectRangesDlg.tipIsLocked": "<PERSON><PERSON> k<PERSON>j<PERSON> on muokkaamassa tätä elementtiä. ", "SSE.Views.ProtectRangesDlg.txtEditRange": "Edit range", "SSE.Views.ProtectRangesDlg.txtNewRange": "New range", "SSE.Views.ProtectRangesDlg.txtNo": "No", "SSE.Views.ProtectRangesDlg.txtTitle": "Allow users to edit ranges", "SSE.Views.ProtectRangesDlg.txtYes": "K<PERSON><PERSON>ä", "SSE.Views.ProtectRangesDlg.warnDelete": "Are you sure you want to delete the name {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Columns", "SSE.Views.RemoveDuplicatesDialog.textDescription": "To delete duplicate values, select one or more columns that contain duplicates.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "My data has headers", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "<PERSON><PERSON><PERSON> kaikki", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Remove duplicates", "SSE.Views.RightMenu.ariaRightMenu": "<PERSON><PERSON><PERSON> v<PERSON>", "SSE.Views.RightMenu.txtCellSettings": "Cell settings", "SSE.Views.RightMenu.txtChartSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtImageSettings": "<PERSON><PERSON>", "SSE.Views.RightMenu.txtParagraphSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtPivotSettings": "Pivot Table settings", "SSE.Views.RightMenu.txtSettings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtShapeSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtSignatureSettings": "Allekirjoitusasetukset", "SSE.Views.RightMenu.txtSlicerSettings": "Slicer settings", "SSE.Views.RightMenu.txtSparklineSettings": "Kipinäviivan asetukset", "SSE.Views.RightMenu.txtTableSettings": "Taulukon asetukset", "SSE.Views.RightMenu.txtTextArtSettings": "Tai<PERSON><PERSON><PERSON> te<PERSON>", "SSE.Views.ScaleDialog.textAuto": "Auto", "SSE.Views.ScaleDialog.textError": "Syötetty arvo ei ole o<PERSON>.", "SSE.Views.ScaleDialog.textFewPages": "pages", "SSE.Views.ScaleDialog.textFitTo": "Fit to", "SSE.Views.ScaleDialog.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textManyPages": "pages", "SSE.Views.ScaleDialog.textOnePage": "sivu", "SSE.Views.ScaleDialog.textScaleTo": "Scale to", "SSE.Views.ScaleDialog.textTitle": "Scale settings", "SSE.Views.ScaleDialog.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "<PERSON><PERSON><PERSON><PERSON><PERSON> kentän maksim<PERSON> on {0}", "SSE.Views.SetValueDialog.txtMinText": "<PERSON><PERSON><PERSON><PERSON><PERSON> kentän minimiarvo on {0}", "SSE.Views.ShapeSettings.strBackground": "Taustan väri", "SSE.Views.ShapeSettings.strChange": "<PERSON><PERSON> muotoa", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "Täytä", "SSE.Views.ShapeSettings.strForeground": "Etualan väri", "SSE.Views.ShapeSettings.strPattern": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strShadow": "<PERSON><PERSON><PERSON><PERSON>jo", "SSE.Views.ShapeSettings.strSize": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strStroke": "Tikkuviiva", "SSE.Views.ShapeSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strType": "Tyyppi", "SSE.Views.ShapeSettings.textAdjustShadow": "Säädä <PERSON>", "SSE.Views.ShapeSettings.textAdvanced": "Näytä laajennetut asetukset", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "Syötetty arvo ei ole o<PERSON>.<br>Ole hyvä ja syötä arvo välillä 0 pt ja 1584 pt", "SSE.Views.ShapeSettings.textColor": "Väritäyttö", "SSE.Views.ShapeSettings.textDirection": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textEditPoints": "Muokkaa pisteitä", "SSE.Views.ShapeSettings.textEditShape": "<PERSON><PERSON><PERSON><PERSON> muotoa", "SSE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON> ku<PERSON>ta", "SSE.Views.ShapeSettings.textEyedropper": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFlip": "Flip", "SSE.Views.ShapeSettings.textFromFile": "Tiedostosta", "SSE.Views.ShapeSettings.textFromStorage": "Tallennusvälineestä", "SSE.Views.ShapeSettings.textFromUrl": "URL-osoitteesta", "SSE.Views.ShapeSettings.textGradient": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON>eva täyttö", "SSE.Views.ShapeSettings.textHint270": "Kierrä 90° vastapäivään", "SSE.Views.ShapeSettings.textHint90": "Kierrä 90° myötäpäivään", "SSE.Views.ShapeSettings.textHintFlipH": "Käännä vaakasuunnassa", "SSE.Views.ShapeSettings.textHintFlipV": "Käännä pysty<PERSON>unnassa", "SSE.Views.ShapeSettings.textImageTexture": "Kuva tai pintarakenne", "SSE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textMoreColors": "More colors", "SSE.Views.ShapeSettings.textNoFill": "Ei tä<PERSON>", "SSE.Views.ShapeSettings.textNoShadow": "<PERSON><PERSON> var<PERSON>a", "SSE.Views.ShapeSettings.textOriginalSize": "Alkuperäinen koko", "SSE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textPosition": "Position", "SSE.Views.ShapeSettings.textRadial": "S<PERSON><PERSON><PERSON>äinen", "SSE.Views.ShapeSettings.textRecentlyUsed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textRotate90": "Kierrä 90°", "SSE.Views.ShapeSettings.textRotation": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textSelectImage": "Valitse kuva", "SSE.Views.ShapeSettings.textSelectTexture": "Valitse", "SSE.Views.ShapeSettings.textShadow": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStretch": "Venytä", "SSE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textTexture": "Pintarakenteesta", "SSE.Views.ShapeSettings.textTile": "Laatta", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Lisää kaltev<PERSON>", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Poista gradienttipiste", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON>i", "SSE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "Tumma kangas", "SSE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtKnit": "Sid<PERSON>", "SSE.Views.ShapeSettings.txtLeather": "Nahka", "SSE.Views.ShapeSettings.txtNoBorders": "Ei viivaa", "SSE.Views.ShapeSettings.txtPapyrus": "Papyrus", "SSE.Views.ShapeSettings.txtWood": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Columns", "SSE.Views.ShapeSettingsAdvanced.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Don't move or size with cells", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Vaihtoehtoinen teksti", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Description", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart, or table.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Title", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "AutoFit", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Aloituskoko", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Al<PERSON>ust<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Viiste", "SSE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Ison kirjaimen tyyppi", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Sarakkeiden määrä", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "<PERSON><PERSON><PERSON><PERSON> koko", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Lo<PERSON><PERSON><PERSON> tyyli", "SSE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Liitoksen t<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Viivan tyyli", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Move but don't size with cells", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Allow text to overflow shape", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "<PERSON><PERSON> muo<PERSON>a sopi<PERSON> te<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRotation": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRound": "Pyöristä", "SSE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Cell snapping", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Spacing between columns", "SSE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Tekstilaatikko", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Muoto - Laaj<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTop": "Yläosa", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Move and size with cells", "SSE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Vahvuudet & Nuolet", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Varo<PERSON><PERSON>", "SSE.Views.SignatureSettings.strDelete": "Poista allekirjoitus", "SSE.Views.SignatureSettings.strDetails": "Allekirjoituksen yksityiskohdat", "SSE.Views.SignatureSettings.strInvalid": "Virheelliset allekirjoitukset", "SSE.Views.SignatureSettings.strRequested": "Pyydetyt allekirjoitukset", "SSE.Views.SignatureSettings.strSetup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSignature": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSigner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strValid": "Kelvolliset allekirjoitukset", "SSE.Views.SignatureSettings.txtContinueEditing": "<PERSON>ok<PERSON><PERSON> silti", "SSE.Views.SignatureSettings.txtEditWarning": "Editing will remove signatures from the spreadsheet.<br>Continue?", "SSE.Views.SignatureSettings.txtRemoveWarning": "<PERSON><PERSON><PERSON><PERSON> poistaa tämän allekirjoit<PERSON>?<br><PERSON><PERSON><PERSON><PERSON> ei voi kumota.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "This spreadsheet needs to be signed.", "SSE.Views.SignatureSettings.txtSigned": "Valid signatures have been added to the spreadsheet. The spreadsheet is protected from editing.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Some of the digital signatures in spreadsheet are invalid or could not be verified. The spreadsheet is protected from editing.", "SSE.Views.SlicerAddDialog.textColumns": "Columns", "SSE.Views.SlicerAddDialog.txtTitle": "Insert slicers", "SSE.Views.SlicerSettings.strHideNoData": "Hide items with no data", "SSE.Views.SlicerSettings.strIndNoData": "Visually indicate items with no data", "SSE.Views.SlicerSettings.strShowDel": "Show items deleted from the data source", "SSE.Views.SlicerSettings.strShowNoData": "Show items with no data last", "SSE.Views.SlicerSettings.strSorting": "Sorting and filtering", "SSE.Views.SlicerSettings.textAdvanced": "Näytä laajennetut asetukset", "SSE.Views.SlicerSettings.textAsc": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textAZ": "A to Z", "SSE.Views.SlicerSettings.textButtons": "Painikkeet", "SSE.Views.SlicerSettings.textColumns": "Columns", "SSE.Views.SlicerSettings.textDesc": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textHor": "Horizontal", "SSE.Views.SlicerSettings.textKeepRatio": "Constant Proportions", "SSE.Views.SlicerSettings.textLargeSmall": "largest to smallest", "SSE.Views.SlicerSettings.textLock": "Disable resizing or moving", "SSE.Views.SlicerSettings.textNewOld": "newest to oldest", "SSE.Views.SlicerSettings.textOldNew": "oldest to newest", "SSE.Views.SlicerSettings.textPosition": "Position", "SSE.Views.SlicerSettings.textSize": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textSmallLarge": "smallest to largest", "SSE.Views.SlicerSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textVert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "Z to A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Painikkeet", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Columns", "SSE.Views.SlicerSettingsAdvanced.strHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Hide items with no data", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Visually indicate items with no data", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Viittaukset", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Show items deleted from the data source", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Display header", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Show items with no data last", "SSE.Views.SlicerSettingsAdvanced.strSize": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Sorting & Filtering", "SSE.Views.SlicerSettingsAdvanced.strStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Style & Size", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Don't move or size with cells", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Vaihtoehtoinen teksti", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Description", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart or table.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Title", "SSE.Views.SlicerSettingsAdvanced.textAsc": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A to Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Name to use in formulas", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Header", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Constant proportions", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "largest to smallest", "SSE.Views.SlicerSettingsAdvanced.textName": "Name", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "newest to oldest", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "oldest to newest", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Move but don't size with cells", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "smallest to largest", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Cell snapping", "SSE.Views.SlicerSettingsAdvanced.textSort": "Sort", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Source name", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Slicer - Advanced settings", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Move and size with cells", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z to A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "This field is required", "SSE.Views.SortDialog.errorEmpty": "All sort criteria must have a column or row specified.", "SSE.Views.SortDialog.errorMoreOneCol": "More than one column is selected.", "SSE.Views.SortDialog.errorMoreOneRow": "More than one row is selected.", "SSE.Views.SortDialog.errorNotOriginalCol": "The column you selected is not in the original selected range.", "SSE.Views.SortDialog.errorNotOriginalRow": "The row you selected is not in the original selected range.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 is being sorted by the same color more than once.<br>Delete the duplicate sort criteria and try again.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 is being sorted by values more than once.<br>Delete the duplicate sort criteria and try again.", "SSE.Views.SortDialog.textAsc": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textAuto": "Automaattinen", "SSE.Views.SortDialog.textAZ": "A to Z", "SSE.Views.SortDialog.textBelow": "Below", "SSE.Views.SortDialog.textBtnCopy": "Copy", "SSE.Views.SortDialog.textBtnDelete": "Poista", "SSE.Views.SortDialog.textBtnNew": "<PERSON>us<PERSON>", "SSE.Views.SortDialog.textCellColor": "Cell color", "SSE.Views.SortDialog.textColumn": "Column", "SSE.Views.SortDialog.textDesc": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textDown": "Move level down", "SSE.Views.SortDialog.textFontColor": "<PERSON><PERSON><PERSON> väri", "SSE.Views.SortDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textLevels": "Tasot", "SSE.Views.SortDialog.textMoreCols": "(More columns...)", "SSE.Views.SortDialog.textMoreRows": "(More rows...)", "SSE.Views.SortDialog.textNone": "None", "SSE.Views.SortDialog.textOptions": "Vaihtoehdot", "SSE.Views.SortDialog.textOrder": "<PERSON><PERSON>", "SSE.Views.SortDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textRow": "Row", "SSE.Views.SortDialog.textSort": "Sort on", "SSE.Views.SortDialog.textSortBy": "Lajitteluperuste", "SSE.Views.SortDialog.textThenBy": "Then by", "SSE.Views.SortDialog.textTop": "Yläosa", "SSE.Views.SortDialog.textUp": "Move level up", "SSE.Views.SortDialog.textValues": "Arvot", "SSE.Views.SortDialog.textZA": "Z to A", "SSE.Views.SortDialog.txtInvalidRange": "Invalid cells range.", "SSE.Views.SortDialog.txtTitle": "Sort", "SSE.Views.SortFilterDialog.textAsc": "Ascending (A to Z) by", "SSE.Views.SortFilterDialog.textDesc": "Descending (Z to A) by", "SSE.Views.SortFilterDialog.textNoSort": "No sort", "SSE.Views.SortFilterDialog.txtTitle": "Sort", "SSE.Views.SortFilterDialog.txtTitleValue": "Sort by value", "SSE.Views.SortOptionsDialog.textCase": "Case sensitive", "SSE.Views.SortOptionsDialog.textHeaders": "My data has headers", "SSE.Views.SortOptionsDialog.textLeftRight": "Sort left to right", "SSE.Views.SortOptionsDialog.textOrientation": "Orientation", "SSE.Views.SortOptionsDialog.textTitle": "Sort options", "SSE.Views.SortOptionsDialog.textTopBottom": "Sort top to bottom", "SSE.Views.SpecialPasteDialog.textAdd": "Lisää", "SSE.Views.SpecialPasteDialog.textAll": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textBlanks": "Skip blanks", "SSE.Views.SpecialPasteDialog.textColWidth": "Column widths", "SSE.Views.SpecialPasteDialog.textComments": "Ko<PERSON>ntit", "SSE.Views.SpecialPasteDialog.textDiv": "Divide", "SSE.Views.SpecialPasteDialog.textFFormat": "Formulas & formatting", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formulas & number formats", "SSE.Views.SpecialPasteDialog.textFormats": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFormulas": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFWidth": "Formulas & column widths", "SSE.Views.SpecialPasteDialog.textMult": "Multiply", "SSE.Views.SpecialPasteDialog.textNone": "None", "SSE.Views.SpecialPasteDialog.textOperation": "Operation", "SSE.Views.SpecialPasteDialog.textPaste": "Liit<PERSON>", "SSE.Views.SpecialPasteDialog.textSub": "Subtract", "SSE.Views.SpecialPasteDialog.textTitle": "Paste special", "SSE.Views.SpecialPasteDialog.textTranspose": "Transpose", "SSE.Views.SpecialPasteDialog.textValues": "Arvot", "SSE.Views.SpecialPasteDialog.textVFormat": "Values & Formatting", "SSE.Views.SpecialPasteDialog.textVNFormat": "Values & Number formats", "SSE.Views.SpecialPasteDialog.textWBorders": "All except borders", "SSE.Views.Spellcheck.noSuggestions": "No spelling suggestions", "SSE.Views.Spellcheck.textChange": "<PERSON><PERSON>", "SSE.Views.Spellcheck.textChangeAll": "Change all", "SSE.Views.Spellcheck.textIgnore": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textIgnoreAll": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.txtAddToDictionary": "Lisää sanastoon", "SSE.Views.Spellcheck.txtClosePanel": "Close spelling", "SSE.Views.Spellcheck.txtComplete": "Spellcheck has been completed", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Dictionary language", "SSE.Views.Spellcheck.txtNextTip": "Go to the next word", "SSE.Views.Spellcheck.txtSpelling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(<PERSON><PERSON><PERSON> lo<PERSON>)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "<PERSON><PERSON> kopio", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(Create new spreadsheet)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "<PERSON><PERSON><PERSON><PERSON> ennen ta<PERSON>a", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Spreadsheet", "SSE.Views.Statusbar.filteredRecordsText": "{0} of {1} records filtered", "SSE.Views.Statusbar.filteredText": "Filter mode", "SSE.Views.Statusbar.itemAverage": "Average", "SSE.Views.Statusbar.itemCount": "Count", "SSE.Views.Statusbar.itemDelete": "Poista", "SSE.Views.Statusbar.itemHidden": "Piilotettu", "SSE.Views.Statusbar.itemHide": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemInsert": "Lisää", "SSE.Views.Statusbar.itemMaximum": "Maximum", "SSE.Views.Statusbar.itemMinimum": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemMoveOrCopy": "Move or copy", "SSE.Views.Statusbar.itemProtect": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemRename": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemStatus": "Saving status", "SSE.Views.Statusbar.itemSum": "Sum", "SSE.Views.Statusbar.itemTabColor": "Välilehden väri", "SSE.Views.Statusbar.itemUnProtect": "Unprotect", "SSE.Views.Statusbar.RenameDialog.errNameExists": "<PERSON><PERSON><PERSON> sa<PERSON> on jo luotu.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Taulukon nimessä ei voi olla seuraavia merkkejä: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "<PERSON><PERSON><PERSON> nimi", "SSE.Views.Statusbar.selectAllSheets": "Select All Sheets", "SSE.Views.Statusbar.sheetIndexText": "Sheet {0} of {1}", "SSE.Views.Statusbar.textAverage": "KESKIMÄÄRÄINEN", "SSE.Views.Statusbar.textCount": "LASKE", "SSE.Views.Statusbar.textMax": "Max", "SSE.Views.Statusbar.textMin": "Min", "SSE.Views.Statusbar.textNewColor": "Lisää uusi mukautettu väri", "SSE.Views.Statusbar.textNoColor": "<PERSON><PERSON> väriä", "SSE.Views.Statusbar.textSum": "Summa", "SSE.Views.Statusbar.tipAddTab": "Lisää taulukko", "SSE.Views.Statusbar.tipFirst": "Rullaa ensimmäiseen taulukkoon", "SSE.Views.Statusbar.tipLast": "Rullaa viimeiseen taulukkoon", "SSE.Views.Statusbar.tipListOfSheets": "List of sheets", "SSE.Views.Statusbar.tipNext": "<PERSON><PERSON><PERSON> ta<PERSON><PERSON>loa o<PERSON>a", "SSE.Views.Statusbar.tipPrev": "<PERSON><PERSON><PERSON> ta<PERSON>luetteloa vasemmalla", "SSE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomIn": "Lähennä", "SSE.Views.Statusbar.tipZoomOut": "Loitonna", "SSE.Views.Statusbar.ungroupSheets": "Ungroup sheets", "SSE.Views.Statusbar.zoomText": "<PERSON><PERSON><PERSON> {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Toimintoa ei voida suorittaa valitulle solujen tietoalueelle.<br><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> tietoalue, joka eroa<PERSON>, ja yrit<PERSON> uude<PERSON>.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Toimintoa ei voitu suorittaa valitulle solun tietoalueelle.<br>Valitse tietoalue niin että ensimmäinen taulukon rivi on samalla rivillä<br>ja t<PERSON><PERSON><PERSON><PERSON><PERSON> on limitt<PERSON>in nykyisen kanssa.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Toimintoa ei voitu suorittaa valitulle solun tietoalueelle.<br>Valitse tietoalue mikä ei sisällä muita taulukkoja.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Multi-cell array formulas are not allowed in tables.", "SSE.Views.TableOptionsDialog.txtEmpty": "Tämä kenttä tarvitaan", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON>", "SSE.Views.TableOptionsDialog.txtInvalidRange": "VIRHE! Virheellinen solujen tietoalue", "SSE.Views.TableOptionsDialog.txtNote": "The headers must remain in the same row, and the resulting table range must overlap the original table range.", "SSE.Views.TableOptionsDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.deleteColumnText": "<PERSON>ista sarake", "SSE.Views.TableSettings.deleteRowText": "Poista rivi", "SSE.Views.TableSettings.deleteTableText": "Poista taulukko", "SSE.Views.TableSettings.insertColumnLeftText": "Lisää sarake vasemmalle", "SSE.Views.TableSettings.insertColumnRightText": "Lisää sarake o<PERSON>", "SSE.Views.TableSettings.insertRowAboveText": "Lisää rivi ylös", "SSE.Views.TableSettings.insertRowBelowText": "Lisää rivi alas", "SSE.Views.TableSettings.notcriticalErrorTitle": "Varo<PERSON><PERSON>", "SSE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON> koko sarake", "SSE.Views.TableSettings.selectDataText": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.TableSettings.selectRowText": "Valitse rivi", "SSE.Views.TableSettings.selectTableText": "Valitse taulukko", "SSE.Views.TableSettings.textActions": "Table actions", "SSE.Views.TableSettings.textAdvanced": "Näytä laajennetut asetukset", "SSE.Views.TableSettings.textBanded": "Niputettu", "SSE.Views.TableSettings.textColumns": "Sarakkeet", "SSE.Views.TableSettings.textConvertRange": "Convert to range", "SSE.Views.TableSettings.textEdit": "Rivit & Sarakkeet", "SSE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON>", "SSE.Views.TableSettings.textExistName": "VIRHE: <PERSON><PERSON><PERSON><PERSON> on jo o<PERSON><PERSON>a", "SSE.Views.TableSettings.textFilter": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textFirst": "<PERSON><PERSON>mmä<PERSON>", "SSE.Views.TableSettings.textHeader": "Ylävyöhyke", "SSE.Views.TableSettings.textInvalidName": "VRIHE!Virheellinen taulukon nimi", "SSE.Views.TableSettings.textIsLocked": "<PERSON><PERSON> k<PERSON>j<PERSON> on muokkaamassa tätä elementtiä. ", "SSE.Views.TableSettings.textLast": "Viimeinen", "SSE.Views.TableSettings.textLongOperation": "Long operation", "SSE.Views.TableSettings.textPivot": "Insert pivot table", "SSE.Views.TableSettings.textRemDuplicates": "Remove duplicates", "SSE.Views.TableSettings.textReservedName": "<PERSON><PERSON>, jota yrität kä<PERSON>tää, on jo viitattu solujen kaav<PERSON>sa. Ole hyvä ja käytä muuta nimeä.", "SSE.Views.TableSettings.textResize": "Taulukon koko", "SSE.Views.TableSettings.textRows": "Rivit", "SSE.Views.TableSettings.textSelectData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.TableSettings.textSlicer": "Insert slicer", "SSE.Views.TableSettings.textTableName": "<PERSON><PERSON><PERSON> nimi", "SSE.Views.TableSettings.textTemplate": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textTotal": "Yhteensä", "SSE.Views.TableSettings.txtGroupTable_Custom": "Muka<PERSON>ttu", "SSE.Views.TableSettings.txtGroupTable_Dark": "Tumma", "SSE.Views.TableSettings.txtGroupTable_Light": "Vaalea", "SSE.Views.TableSettings.txtGroupTable_Medium": "Keski<PERSON>koinen", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Table style dark", "SSE.Views.TableSettings.txtTable_TableStyleLight": "Table style light", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "Table style medium", "SSE.Views.TableSettings.warnLongOperation": "The operation you are about to perform might take rather much time to complete.<br>Are you sure you want to continue?", "SSE.Views.TableSettingsAdvanced.textAlt": "Vaihtoehtoinen teksti", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Description", "SSE.Views.TableSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart or table.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Title", "SSE.Views.TableSettingsAdvanced.textTitle": "Taulukko - Laajennetut asetukset", "SSE.Views.TextArtSettings.strBackground": "Taustan väri", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "Täytä", "SSE.Views.TextArtSettings.strForeground": "Etualan väri", "SSE.Views.TextArtSettings.strPattern": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strSize": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strStroke": "Tikkuviiva", "SSE.Views.TextArtSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strType": "Tyyppi", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "Syötetty arvo ei ole o<PERSON>.<br>Ole hyvä ja syötä arvo välillä 0 pt ja 1584 pt", "SSE.Views.TextArtSettings.textColor": "Väritäyttö", "SSE.Views.TextArtSettings.textDirection": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON> ku<PERSON>ta", "SSE.Views.TextArtSettings.textFromFile": "Tiedostosta", "SSE.Views.TextArtSettings.textFromUrl": "URL-osoitteesta", "SSE.Views.TextArtSettings.textGradient": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON>eva täyttö", "SSE.Views.TextArtSettings.textImageTexture": "Kuva tai pintarakenne", "SSE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textNoFill": "Ei tä<PERSON>", "SSE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textPosition": "Position", "SSE.Views.TextArtSettings.textRadial": "S<PERSON><PERSON><PERSON>äinen", "SSE.Views.TextArtSettings.textSelectTexture": "Valitse", "SSE.Views.TextArtSettings.textStretch": "Venytä", "SSE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textTemplate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textTexture": "Pintarakenteesta", "SSE.Views.TextArtSettings.textTile": "Laatta", "SSE.Views.TextArtSettings.textTransform": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Lisää kaltev<PERSON>", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Poista gradienttipiste", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON>i", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "Tumma kangas", "SSE.Views.TextArtSettings.txtGrain": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtKnit": "Sid<PERSON>", "SSE.Views.TextArtSettings.txtLeather": "Nahka", "SSE.Views.TextArtSettings.txtNoBorders": "Ei viivaa", "SSE.Views.TextArtSettings.txtPapyrus": "Papyrus", "SSE.Views.TextArtSettings.txtWood": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnAddComment": "Lisää kommentti", "SSE.Views.Toolbar.capBtnColorSchemas": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnInsHeader": "Ylätunniste & alatunniste", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON>licer", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Symboli", "SSE.Views.Toolbar.capBtnMargins": "Marginaalit", "SSE.Views.Toolbar.capBtnPageBreak": "Vai<PERSON>dot", "SSE.Views.Toolbar.capBtnPageOrient": "Orientation", "SSE.Views.Toolbar.capBtnPageSize": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintArea": "Print Area", "SSE.Views.Toolbar.capBtnPrintTitles": "Print Titles", "SSE.Views.Toolbar.capBtnScale": "Scale To Fit", "SSE.Views.Toolbar.capImgAlign": "Ta<PERSON><PERSON>", "SSE.Views.Toolbar.capImgBackward": "Send Backward", "SSE.Views.Toolbar.capImgForward": "Bring Forward", "SSE.Views.Toolbar.capImgGroup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertChart": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertChartRecommend": "Recommended Chart", "SSE.Views.Toolbar.capInsertEquation": "Yhtälö", "SSE.Views.Toolbar.capInsertHyperlink": "Hyperlink", "SSE.Views.Toolbar.capInsertImage": "<PERSON><PERSON>", "SSE.Views.Toolbar.capInsertShape": "<PERSON><PERSON>", "SSE.Views.Toolbar.capInsertSpark": "Sparkline", "SSE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertText": "Tekstilaatikko", "SSE.Views.Toolbar.capInsertTextart": "Text Art", "SSE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniCapitalizeWords": "Jokainen sana isolla al<PERSON>", "SSE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniImageFromStorage": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniImageFromUrl": "<PERSON><PERSON> ve<PERSON>-o<PERSON><PERSON>", "SSE.Views.Toolbar.mniLowerCase": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.mniSentenceCase": "Lauseen ensimmäinen kirjain isolla", "SSE.Views.Toolbar.mniToggleCase": "vAIHDA kIRJAINKOKOA", "SSE.Views.Toolbar.mniUpperCase": "ISOIN KIRJAIMIN", "SSE.Views.Toolbar.textAddPrintArea": "Add to print area", "SSE.Views.Toolbar.textAlignBottom": "<PERSON><PERSON><PERSON> alas", "SSE.Views.Toolbar.textAlignCenter": "<PERSON><PERSON><PERSON> kes<PERSON>e", "SSE.Views.Toolbar.textAlignJust": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON><PERSON> vasen", "SSE.Views.Toolbar.textAlignMiddle": "<PERSON><PERSON><PERSON> kes<PERSON>e", "SSE.Views.Toolbar.textAlignRight": "<PERSON><PERSON><PERSON> o<PERSON>a", "SSE.Views.Toolbar.textAlignTop": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAllBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlpha": "Pieni kreikkalainen alfa-merkki", "SSE.Views.Toolbar.textAuto": "Auto", "SSE.Views.Toolbar.textAutoColor": "Automaattinen", "SSE.Views.Toolbar.textBetta": "Pieni kreikkalainen beta-merkki", "SSE.Views.Toolbar.textBlackHeart": "<PERSON><PERSON>", "SSE.Views.Toolbar.textBold": "Liha<PERSON>int<PERSON>", "SSE.Views.Toolbar.textBordersColor": "Reunuksen väri", "SSE.Views.Toolbar.textBordersStyle": "Border style", "SSE.Views.Toolbar.textBottom": "Alhaalla: ", "SSE.Views.Toolbar.textBottomBorders": "Alareunukset", "SSE.Views.Toolbar.textBullet": "Luettelomerkki", "SSE.Views.Toolbar.textCellAlign": "Format cell alignment", "SSE.Views.Toolbar.textCenterBorders": "Pys<PERSON>reunust<PERSON> si<PERSON>", "SSE.Views.Toolbar.textClearPrintArea": "Clear print area", "SSE.Views.Toolbar.textClearRule": "Clear rules", "SSE.Views.Toolbar.textClockwise": "<PERSON><PERSON>", "SSE.Views.Toolbar.textColorScales": "Color scales", "SSE.Views.Toolbar.textCopyright": "Tekijänoikeusmer<PERSON>", "SSE.Views.Toolbar.textCounterCw": "<PERSON><PERSON>", "SSE.Views.Toolbar.textCustom": "Muka<PERSON>ttu", "SSE.Views.Toolbar.textDataBars": "Data Bars", "SSE.Views.Toolbar.textDegree": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textDelLeft": "<PERSON><PERSON><PERSON>da solut vasemmalle", "SSE.Views.Toolbar.textDelPageBreak": "Remove page break", "SSE.Views.Toolbar.textDelta": "Pieni kreikkalainen delta-merkki", "SSE.Views.Toolbar.textDelUp": "<PERSON><PERSON><PERSON>da solut yl<PERSON>s", "SSE.Views.Toolbar.textDiagDownBorder": "Diagonaalinen alareunus", "SSE.Views.Toolbar.textDiagUpBorder": "Diagonaalinen yläreunus", "SSE.Views.Toolbar.textDivision": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textDollar": "Dollarimerk<PERSON>", "SSE.Views.Toolbar.textDone": "Val<PERSON>", "SSE.Views.Toolbar.textDown": "Alas", "SSE.Views.Toolbar.textEditVA": "Edit Visible Area", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON> sarake", "SSE.Views.Toolbar.textEntireRow": "<PERSON><PERSON> rivi", "SSE.Views.Toolbar.textEuro": "Eurosymboli", "SSE.Views.Toolbar.textFewPages": "pages", "SSE.Views.Toolbar.textFillLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textFillRight": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textFormatCellFill": "Format cell fill", "SSE.Views.Toolbar.textGreaterEqual": "Greater Than Or Equal To", "SSE.Views.Toolbar.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textHideVA": "Hide Visible Area", "SSE.Views.Toolbar.textHorizontal": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>", "SSE.Views.Toolbar.textInfinity": "Infinity", "SSE.Views.Toolbar.textInsDown": "<PERSON><PERSON><PERSON>da solut alas", "SSE.Views.Toolbar.textInsideBorders": "Sisäreunukset", "SSE.Views.Toolbar.textInsPageBreak": "Lisää sivun katkaisu", "SSE.Views.Toolbar.textInsRight": "<PERSON><PERSON><PERSON>da solut o<PERSON>alle", "SSE.Views.Toolbar.textItalic": "Kursivoit<PERSON>", "SSE.Views.Toolbar.textItems": "Items", "SSE.Views.Toolbar.textLandscape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textLeft": "Vasen: ", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON><PERSON><PERSON> re<PERSON>", "SSE.Views.Toolbar.textLessEqual": "Vähemmän kuin tai yhtäkuin", "SSE.Views.Toolbar.textLetterPi": "Pieni kreikkalainen pi-merkki", "SSE.Views.Toolbar.textManageRule": "Manage rules", "SSE.Views.Toolbar.textManyPages": "pages", "SSE.Views.Toolbar.textMarginsLast": "Viimeinen mukautettu", "SSE.Views.Toolbar.textMarginsNarrow": "<PERSON>rrow", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "Leveä", "SSE.Views.Toolbar.textMiddleBorders": "Vaakareunusten sisällä", "SSE.Views.Toolbar.textMoreBorders": "More borders", "SSE.Views.Toolbar.textMoreFormats": "More formats", "SSE.Views.Toolbar.textMorePages": "More pages", "SSE.Views.Toolbar.textMoreSymbols": "More symbols", "SSE.Views.Toolbar.textNewColor": "Lisää uusi mukautettu väri", "SSE.Views.Toolbar.textNewRule": "New rule", "SSE.Views.Toolbar.textNoBorders": "<PERSON><PERSON> reunuk<PERSON>", "SSE.Views.Toolbar.textNotEqualTo": "Not Equal To", "SSE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "SSE.Views.Toolbar.textOnePage": "sivu", "SSE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "SSE.Views.Toolbar.textOutBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPageMarginsCustom": "Muokatut marginaalit", "SSE.Views.Toolbar.textPlusMinus": "Plus-<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPortrait": "Pystysuunta", "SSE.Views.Toolbar.textPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPrintGridlines": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.Toolbar.textPrintHeadings": "Print Headings", "SSE.Views.Toolbar.textPrintOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textRegistered": "Rekisteröity tavaramerkki -symboli", "SSE.Views.Toolbar.textResetPageBreak": "Reset all page breaks", "SSE.Views.Toolbar.textRight": "Oikea: ", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON><PERSON> reun<PERSON>", "SSE.Views.Toolbar.textRotateDown": "Käännä teksti alas", "SSE.Views.Toolbar.textRotateUp": "Käännä teksti ylös", "SSE.Views.Toolbar.textRtlSheet": "Sheet Right-to-left", "SSE.Views.Toolbar.textScale": "Scale", "SSE.Views.Toolbar.textScaleCustom": "Muka<PERSON>ttu", "SSE.Views.Toolbar.textSection": "Pykälämerkki", "SSE.Views.Toolbar.textSelection": "From current selection", "SSE.Views.Toolbar.textSeries": "Series", "SSE.Views.Toolbar.textSetPrintArea": "Set print area", "SSE.Views.Toolbar.textShapesCombine": "Combine", "SSE.Views.Toolbar.textShapesFragment": "Fragment", "SSE.Views.Toolbar.textShapesIntersect": "Intersect", "SSE.Views.Toolbar.textShapesSubstract": "Subtract", "SSE.Views.Toolbar.textShapesUnion": "Union", "SSE.Views.Toolbar.textShowVA": "Show Visible Area", "SSE.Views.Toolbar.textSmile": "Valkoinen hymiö", "SSE.Views.Toolbar.textSquareRoot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textStrikeout": "Strikethrough", "SSE.Views.Toolbar.textSubscript": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSubSuperscript": "Subscript/Superscript", "SSE.Views.Toolbar.textSuperscript": "Yläindeksi", "SSE.Views.Toolbar.textTabCollaboration": "Y<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabData": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabDraw": "Piirrä", "SSE.Views.Toolbar.textTabFile": "Tiedosto", "SSE.Views.Toolbar.textTabFormula": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTabHome": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabInsert": "Lisää", "SSE.Views.Toolbar.textTabLayout": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabProtect": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabView": "Näytä", "SSE.Views.Toolbar.textThisPivot": "From this pivot", "SSE.Views.Toolbar.textThisSheet": "From this worksheet", "SSE.Views.Toolbar.textThisTable": "From this table", "SSE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTop": "Top: ", "SSE.Views.Toolbar.textTopBorders": "Yläreunukset", "SSE.Views.Toolbar.textTradeMark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textUnderline": "Alleviivaus", "SSE.Views.Toolbar.textUp": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textVertical": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textYen": "<PERSON><PERSON>", "SSE.Views.Toolbar.textZoom": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignBottom": "<PERSON><PERSON><PERSON> alas", "SSE.Views.Toolbar.tipAlignCenter": "<PERSON><PERSON><PERSON> kes<PERSON>e", "SSE.Views.Toolbar.tipAlignJust": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignLeft": "<PERSON><PERSON><PERSON> vasen", "SSE.Views.Toolbar.tipAlignMiddle": "<PERSON><PERSON><PERSON> kes<PERSON>e", "SSE.Views.Toolbar.tipAlignRight": "<PERSON><PERSON><PERSON> o<PERSON>a", "SSE.Views.Toolbar.tipAlignTop": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAutofilter": "Lajittele ja Suodata", "SSE.Views.Toolbar.tipBack": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCellStyle": "<PERSON><PERSON> ty<PERSON>i", "SSE.Views.Toolbar.tipChangeCase": "<PERSON><PERSON><PERSON>da <PERSON>", "SSE.Views.Toolbar.tipChangeChart": "<PERSON><PERSON> kaavion t<PERSON>", "SSE.Views.Toolbar.tipClearStyle": "Tyhjennä", "SSE.Views.Toolbar.tipColorSchemas": "Muuta väriluonnosta", "SSE.Views.Toolbar.tipCondFormat": "Conditional formatting", "SSE.Views.Toolbar.tipCopy": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON> t<PERSON>i", "SSE.Views.Toolbar.tipCut": "Cut", "SSE.Views.Toolbar.tipDecDecimal": "Vähennä desimaalia", "SSE.Views.Toolbar.tipDecFont": "Vähennä fontin kokoa", "SSE.Views.Toolbar.tipDeleteOpt": "Poista solut", "SSE.Views.Toolbar.tipDigStyleAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>i", "SSE.Views.Toolbar.tipDigStyleComma": "Comma style", "SSE.Views.Toolbar.tipDigStyleCurrency": "Valuutan tyyli", "SSE.Views.Toolbar.tipDigStylePercent": "<PERSON><PERSON><PERSON> t<PERSON>i", "SSE.Views.Toolbar.tipEditChart": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "SSE.Views.Toolbar.tipEditChartData": "<PERSON><PERSON><PERSON> tiedot", "SSE.Views.Toolbar.tipEditChartType": "<PERSON><PERSON> kaavion t<PERSON>", "SSE.Views.Toolbar.tipEditHeader": "Edit header or footer", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON><PERSON> väri", "SSE.Views.Toolbar.tipFontName": "Fontin nimi", "SSE.Views.Toolbar.tipFontSize": "Fonttikoko", "SSE.Views.Toolbar.tipHAlighOle": "Horizontal align", "SSE.Views.Toolbar.tipImgAlign": "Tasaa objektit", "SSE.Views.Toolbar.tipImgGroup": "Ryhmittele objektit", "SSE.Views.Toolbar.tipIncDecimal": "Lisää desimaalia", "SSE.Views.Toolbar.tipIncFont": "Lisää fontin kokoa", "SSE.Views.Toolbar.tipInsertChart": "Lisää <PERSON>", "SSE.Views.Toolbar.tipInsertChartRecommend": "Insert recommended chart", "SSE.Views.Toolbar.tipInsertChartSpark": "Lisää kaavio tai kipinäviiva", "SSE.Views.Toolbar.tipInsertEquation": "Lisää yhtälö", "SSE.Views.Toolbar.tipInsertHorizontalText": "Lisää vaakasuora tekstilaatikko", "SSE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertImage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertOpt": "Lisää solut", "SSE.Views.Toolbar.tipInsertShape": "<PERSON><PERSON><PERSON><PERSON> muoto", "SSE.Views.Toolbar.tipInsertSlicer": "Insert slicer", "SSE.Views.Toolbar.tipInsertSmartArt": "Insert SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Insert sparkline", "SSE.Views.Toolbar.tipInsertSymbol": "Lisää merkki", "SSE.Views.Toolbar.tipInsertTable": "Lisää taulukko", "SSE.Views.Toolbar.tipInsertText": "Lisää teksti", "SSE.Views.Toolbar.tipInsertTextart": "Insert Text Art", "SSE.Views.Toolbar.tipInsertVerticalText": "Lisää pystysuora tekstikenttä", "SSE.Views.Toolbar.tipMerge": "Yhdistä", "SSE.Views.Toolbar.tipNone": "None", "SSE.Views.Toolbar.tipNumFormat": "Numeron muoto", "SSE.Views.Toolbar.tipPageBreak": "Add a break where you want the next page to begin in the printed copy", "SSE.Views.Toolbar.tipPageMargins": "Sivun marginaalit", "SSE.Views.Toolbar.tipPageOrient": "Page orientation", "SSE.Views.Toolbar.tipPageSize": "<PERSON><PERSON>n koko", "SSE.Views.Toolbar.tipPaste": "Liit<PERSON>", "SSE.Views.Toolbar.tipPrColor": "Taustan väri", "SSE.Views.Toolbar.tipPrint": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrintArea": "Print area", "SSE.Views.Toolbar.tipPrintQuick": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrintTitles": "Print titles", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipReplace": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipRtlSheet": "Switch the sheet direction so that the first column is on the right side", "SSE.Views.Toolbar.tipSave": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSaveCoauth": "<PERSON><PERSON><PERSON>, jotta muut käyttäjät näkevät ne.", "SSE.Views.Toolbar.tipScale": "Scale to fit", "SSE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON><PERSON> kaikki", "SSE.Views.Toolbar.tipSendBackward": "Send backward", "SSE.Views.Toolbar.tipSendForward": "Bring forward", "SSE.Views.Toolbar.tipShapesMerge": "Merge shapes", "SSE.Views.Toolbar.tipSynchronize": "Asiakirja on toisen käyttäjän muuttama. Ole hyvä ja klikkaa tallentaaksesi muutoksesi ja lataa uudelleen muutokset.", "SSE.Views.Toolbar.tipTextFormatting": "More text formatting tools", "SSE.Views.Toolbar.tipTextOrientation": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipVAlighOle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipVisibleArea": "Visible area", "SSE.Views.Toolbar.tipWrap": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAdditional": "Lisä", "SSE.Views.Toolbar.txtAscending": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAutosumTip": "Yhteenveto", "SSE.Views.Toolbar.txtCellStyle": "<PERSON><PERSON> ty<PERSON>i", "SSE.Views.Toolbar.txtClearAll": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearComments": "Ko<PERSON>ntit", "SSE.Views.Toolbar.txtClearFilter": "Poista suoda<PERSON>", "SSE.Views.Toolbar.txtClearFormat": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearHyper": "Linkit", "SSE.Views.Toolbar.txtClearText": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCustom": "Muka<PERSON>ttu", "SSE.Views.Toolbar.txtDate": "Päivämäärä", "SSE.Views.Toolbar.txtDateLong": "Long Date", "SSE.Views.Toolbar.txtDateShort": "Short Date", "SSE.Views.Toolbar.txtDateTime": "Pvm & Kellonaika", "SSE.Views.Toolbar.txtDescending": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDollar": "$ Dollari", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Eksponentiaalinen", "SSE.Views.Toolbar.txtFillNum": "Täytä", "SSE.Views.Toolbar.txtFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtFormula": "Lisää funktio", "SSE.Views.Toolbar.txtFraction": "Murtoluku", "SSE.Views.Toolbar.txtFranc": "CHF Sveitsin frangi", "SSE.Views.Toolbar.txtGeneral": "<PERSON><PERSON>ist<PERSON>", "SSE.Views.Toolbar.txtInteger": "Kokonaisluku", "SSE.Views.Toolbar.txtManageRange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtMergeAcross": "<PERSON><PERSON><PERSON><PERSON> yli", "SSE.Views.Toolbar.txtMergeCells": "Yhdistä solut", "SSE.Views.Toolbar.txtMergeCenter": "Yhdistä & Keskitä", "SSE.Views.Toolbar.txtNamedRange": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtNewRange": "Määrittele nimi", "SSE.Views.Toolbar.txtNoBorders": "<PERSON><PERSON> reunuk<PERSON>", "SSE.Views.Toolbar.txtNumber": "Numero", "SSE.Views.Toolbar.txtPasteRange": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtPercentage": "Prosenttia", "SSE.Views.Toolbar.txtPound": "£ Punta", "SSE.Views.Toolbar.txtRouble": "₽ <PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScientific": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSearch": "Etsi", "SSE.Views.Toolbar.txtSort": "Lajittele", "SSE.Views.Toolbar.txtSortAZ": "Lajittele nousevaan järjestykseen", "SSE.Views.Toolbar.txtSortZA": "Lajittele laskevaan järjestykseen", "SSE.Views.Toolbar.txtSpecial": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtTableTemplate": "<PERSON><PERSON><PERSON> ta<PERSON> mallip<PERSON>", "SSE.Views.Toolbar.txtText": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtTime": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtUnmerge": "<PERSON><PERSON>a solut", "SSE.Views.Toolbar.txtYen": "¥ Jeni", "SSE.Views.Top10FilterDialog.textType": "Näytä", "SSE.Views.Top10FilterDialog.txtBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBy": "mukaan", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtPercent": "Prosenttia", "SSE.Views.Top10FilterDialog.txtSum": "Sum", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 automaattinen suodatin", "SSE.Views.Top10FilterDialog.txtTop": "Yläosa", "SSE.Views.Top10FilterDialog.txtValueTitle": "Top 10 filter", "SSE.Views.ValueFieldSettingsDialog.textNext": "(next)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(previous)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Value field settings", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Average", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Base field", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Base item", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 of %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Count", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Count numbers", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Custom name", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Difference from", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Ha<PERSON>mist<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Max", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "No calculation", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "% of", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "% difference from", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "% of column", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% of grand total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% of parent total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% of parent column total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% of parent row total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% running total in", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "% of row", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Product", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Rank smallest to largest", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Rank largest to smallest", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Running total in", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Show values as", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Source name:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Sum", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Summarize value field by", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "Sulje", "SSE.Views.ViewManagerDlg.guestText": "Vierailija", "SSE.Views.ViewManagerDlg.lockText": "Lukittu", "SSE.Views.ViewManagerDlg.textDelete": "Poista", "SSE.Views.ViewManagerDlg.textDuplicate": "<PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textEmpty": "No views have been created yet.", "SSE.Views.ViewManagerDlg.textGoTo": "Go to view", "SSE.Views.ViewManagerDlg.textLongName": "<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON>, jonka pituus on alle 128 merkkiä.", "SSE.Views.ViewManagerDlg.textNew": "<PERSON>us<PERSON>", "SSE.Views.ViewManagerDlg.textRename": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textRenameError": "View name must not be empty.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Rename view", "SSE.Views.ViewManagerDlg.textViews": "Sheet views", "SSE.Views.ViewManagerDlg.tipIsLocked": "<PERSON><PERSON> k<PERSON>j<PERSON> on muokkaamassa tätä elementtiä. ", "SSE.Views.ViewManagerDlg.txtTitle": "Sheet view manager", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Are you sure you want to delete this sheet view?", "SSE.Views.ViewManagerDlg.warnDeleteView": "You are trying to delete the currently enabled view '%1'.<br>Close this view and delete it?", "SSE.Views.ViewTab.capBtnFreeze": "Freeze Panes", "SSE.Views.ViewTab.capBtnSheetView": "Sheet View", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Näytä työkalurivi aina", "SSE.Views.ViewTab.textClose": "Sulje", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Combine Sheet and Status Bars", "SSE.Views.ViewTab.textCreate": "<PERSON>us<PERSON>", "SSE.Views.ViewTab.textDefault": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textFill": "Täytä", "SSE.Views.ViewTab.textFormula": "Formula Bar", "SSE.Views.ViewTab.textFreezeCol": "Freeze first column", "SSE.Views.ViewTab.textFreezeRow": "Freeze top row", "SSE.Views.ViewTab.textGridlines": "Ruudukon viivat", "SSE.Views.ViewTab.textHeadings": "Otsikot", "SSE.Views.ViewTab.textInterfaceTheme": "K<PERSON>yttöliitt<PERSON><PERSON><PERSON> teema", "SSE.Views.ViewTab.textLeftMenu": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textLine": "Viiva", "SSE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "View manager", "SSE.Views.ViewTab.textRightMenu": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Show frozen panes shadow", "SSE.Views.ViewTab.textTabStyle": "Välilehden tyyli", "SSE.Views.ViewTab.textUnFreeze": "Unfreeze panes", "SSE.Views.ViewTab.textZeros": "Show Zeros", "SSE.Views.ViewTab.textZoom": "Zoom", "SSE.Views.ViewTab.tipClose": "Close sheet view", "SSE.Views.ViewTab.tipCreate": "Create sheet view", "SSE.Views.ViewTab.tipFreeze": "Freeze panes", "SSE.Views.ViewTab.tipInterfaceTheme": "K<PERSON>yttöliitt<PERSON><PERSON><PERSON> teema", "SSE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipSheetView": "Sheet view", "SSE.Views.ViewTab.tipViewNormal": "See your document in Normal view", "SSE.Views.ViewTab.tipViewPageBreak": "See where the page breaks will appear when your document is printed", "SSE.Views.ViewTab.txtViewNormal": "Normal", "SSE.Views.ViewTab.txtViewPageBreak": "Page Break Preview", "SSE.Views.WatchDialog.closeButtonText": "Sulje", "SSE.Views.WatchDialog.textAdd": "Add watch", "SSE.Views.WatchDialog.textBook": "Book", "SSE.Views.WatchDialog.textCell": "Solu", "SSE.Views.WatchDialog.textDelete": "Delete watch", "SSE.Views.WatchDialog.textDeleteAll": "Poista kaikki", "SSE.Views.WatchDialog.textFormula": "<PERSON><PERSON>", "SSE.Views.WatchDialog.textName": "Name", "SSE.Views.WatchDialog.textSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textValue": "Arvo", "SSE.Views.WatchDialog.txtTitle": "Watch window", "SSE.Views.WBProtection.hintAllowRanges": "Allow edit ranges", "SSE.Views.WBProtection.hintProtectRange": "Protect range", "SSE.Views.WBProtection.hintProtectSheet": "Protect sheet", "SSE.Views.WBProtection.hintProtectWB": "Protect workbook", "SSE.Views.WBProtection.txtAllowRanges": "Allow edit ranges", "SSE.Views.WBProtection.txtHiddenFormula": "Hidden Formulas", "SSE.Views.WBProtection.txtLockedCell": "Locked Cell", "SSE.Views.WBProtection.txtLockedShape": "<PERSON><PERSON><PERSON> Locked", "SSE.Views.WBProtection.txtLockedText": "Lock Text", "SSE.Views.WBProtection.txtProtectRange": "Protect Range", "SSE.Views.WBProtection.txtProtectSheet": "Protect Sheet", "SSE.Views.WBProtection.txtProtectWB": "Protect workbook", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Enter a password to unprotect sheet", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Unprotect sheet", "SSE.Views.WBProtection.txtWBUnlockDescription": "Enter a password to unprotect workbook", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}