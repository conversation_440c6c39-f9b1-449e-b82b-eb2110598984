{"cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.Controllers.Chat.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.History.notcriticalErrorTitle": "Warning", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textAreaStacked": "Stacked area", "Common.define.chartData.textAreaStackedPer": "100% Stacked area", "Common.define.chartData.textBar": "Gạch", "Common.define.chartData.textBarNormal": "Clustered column", "Common.define.chartData.textBarNormal3d": "3-D Clustered column", "Common.define.chartData.textBarNormal3dPerspective": "3-D column", "Common.define.chartData.textBarStacked": "Stacked column", "Common.define.chartData.textBarStacked3d": "3-<PERSON> Stacked column", "Common.define.chartData.textBarStackedPer": "100% Stacked column", "Common.define.chartData.textBarStackedPer3d": "3-D 100% Stacked column", "Common.define.chartData.textCharts": "Charts", "Common.define.chartData.textColumn": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textColumnSpark": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Stacked area - clustered column", "Common.define.chartData.textComboBarLine": "Clustered column - line", "Common.define.chartData.textComboBarLineSecondary": "Clustered column - line on secondary axis", "Common.define.chartData.textComboCustom": "Custom combination", "Common.define.chartData.textDoughnut": "Doughnut", "Common.define.chartData.textHBarNormal": "Clustered bar", "Common.define.chartData.textHBarNormal3d": "3-D Clustered bar", "Common.define.chartData.textHBarStacked": "Stacked bar", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> Stacked bar", "Common.define.chartData.textHBarStackedPer": "100% Stacked bar", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% Stacked bar", "Common.define.chartData.textLine": "Đường kẻ", "Common.define.chartData.textLine3d": "3-D line", "Common.define.chartData.textLineMarker": "Line with markers", "Common.define.chartData.textLineSpark": "Đường kẻ", "Common.define.chartData.textLineStacked": "Stacked line", "Common.define.chartData.textLineStackedMarker": "Stacked line with markers", "Common.define.chartData.textLineStackedPer": "100% Stacked line", "Common.define.chartData.textLineStackedPerMarker": "100% Stacked line with markers", "Common.define.chartData.textPie": "<PERSON><PERSON><PERSON> b<PERSON>", "Common.define.chartData.textPie3d": "3-D pie", "Common.define.chartData.textPoint": "XY (Phân tán)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Filled radar", "Common.define.chartData.textRadarMarker": "Radar with markers", "Common.define.chartData.textScatter": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "Scatter with straight lines", "Common.define.chartData.textScatterLineMarker": "Scatter with straight lines and markers", "Common.define.chartData.textScatterSmooth": "Scatter with smooth lines", "Common.define.chartData.textScatterSmoothMarker": "Scatter with smooth lines and markers", "Common.define.chartData.textSparks": "Sparklines", "Common.define.chartData.textStock": "<PERSON><PERSON> phi<PERSON>u", "Common.define.chartData.textSurface": "Bề mặt", "Common.define.chartData.textWinLossSpark": "Win/Loss", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "No format set", "Common.define.conditionalData.text1Above": "1 std dev above", "Common.define.conditionalData.text1Below": "1 std dev below", "Common.define.conditionalData.text2Above": "2 std dev above", "Common.define.conditionalData.text2Below": "2 std dev below", "Common.define.conditionalData.text3Above": "3 std dev above", "Common.define.conditionalData.text3Below": "3 std dev below", "Common.define.conditionalData.textAbove": "Above", "Common.define.conditionalData.textAverage": "Average", "Common.define.conditionalData.textBegins": "Begins with", "Common.define.conditionalData.textBelow": "Below", "Common.define.conditionalData.textBetween": "Between", "Common.define.conditionalData.textBlank": "Blank", "Common.define.conditionalData.textBlanks": "Contains blanks", "Common.define.conditionalData.textBottom": "Bottom", "Common.define.conditionalData.textContains": "Contains", "Common.define.conditionalData.textDataBar": "Data bar", "Common.define.conditionalData.textDate": "Date", "Common.define.conditionalData.textDuplicate": "Duplicate", "Common.define.conditionalData.textEnds": "Ends with", "Common.define.conditionalData.textEqAbove": "Equal to or above", "Common.define.conditionalData.textEqBelow": "Equal to or below", "Common.define.conditionalData.textEqual": "Equal to", "Common.define.conditionalData.textError": "Error", "Common.define.conditionalData.textErrors": "Contains errors", "Common.define.conditionalData.textFormula": "Formula", "Common.define.conditionalData.textGreater": "Greater than", "Common.define.conditionalData.textGreaterEq": "Greater than or equal to", "Common.define.conditionalData.textIconSets": "Icon sets", "Common.define.conditionalData.textLast7days": "In the last 7 days", "Common.define.conditionalData.textLastMonth": "Last month", "Common.define.conditionalData.textLastWeek": "Last week", "Common.define.conditionalData.textLess": "Less than", "Common.define.conditionalData.textLessEq": "Less than or equal to", "Common.define.conditionalData.textNextMonth": "Next month", "Common.define.conditionalData.textNextWeek": "Next week", "Common.define.conditionalData.textNotBetween": "Not between", "Common.define.conditionalData.textNotBlanks": "Does not contain blanks", "Common.define.conditionalData.textNotContains": "Does not contain", "Common.define.conditionalData.textNotEqual": "Not equal to", "Common.define.conditionalData.textNotErrors": "Does not contain errors", "Common.define.conditionalData.textText": "Text", "Common.define.conditionalData.textThisMonth": "This month", "Common.define.conditionalData.textThisWeek": "This week", "Common.define.conditionalData.textToday": "Today", "Common.define.conditionalData.textTomorrow": "Tomorrow", "Common.define.conditionalData.textTop": "Top", "Common.define.conditionalData.textUnique": "Unique", "Common.define.conditionalData.textValue": "Value is", "Common.define.conditionalData.textYesterday": "Yesterday", "Common.define.smartArt.textAccentedPicture": "Accented Picture", "Common.define.smartArt.textAccentProcess": "Accent Process", "Common.define.smartArt.textAlternatingFlow": "Alternating flow", "Common.define.smartArt.textAlternatingHexagons": "Alternating hexagons", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternating picture blocks", "Common.define.smartArt.textAlternatingPictureCircles": "Alternating picture circles", "Common.define.smartArt.textArchitectureLayout": "Architecture layout", "Common.define.smartArt.textArrowRibbon": "Arrow ribbon", "Common.define.smartArt.textAscendingPictureAccentProcess": "Ascending picture accent process", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Basic bending process", "Common.define.smartArt.textBasicBlockList": "Basic block list", "Common.define.smartArt.textBasicChevronProcess": "Basic chevron process", "Common.define.smartArt.textBasicCycle": "Basic cycle", "Common.define.smartArt.textBasicMatrix": "Basic matrix", "Common.define.smartArt.textBasicPie": "Basic Pie", "Common.define.smartArt.textBasicProcess": "Basic process", "Common.define.smartArt.textBasicPyramid": "Basic pyramid", "Common.define.smartArt.textBasicRadial": "Basic radial", "Common.define.smartArt.textBasicTarget": "Basic target", "Common.define.smartArt.textBasicTimeline": "Basic timeline", "Common.define.smartArt.textBasicVenn": "Basic Venn", "Common.define.smartArt.textBendingPictureAccentList": "Bending picture accent list", "Common.define.smartArt.textBendingPictureBlocks": "Bending picture blocks", "Common.define.smartArt.textBendingPictureCaption": "Bending picture caption", "Common.define.smartArt.textBendingPictureCaptionList": "Bending picture caption list", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Bending picture semi-transparent text", "Common.define.smartArt.textBlockCycle": "Block cycle", "Common.define.smartArt.textBubblePictureList": "Bubble picture list", "Common.define.smartArt.textCaptionedPictures": "Captioned pictures", "Common.define.smartArt.textChevronAccentProcess": "Chevron accent process", "Common.define.smartArt.textChevronList": "Chevron list", "Common.define.smartArt.textCircleAccentTimeline": "Circle accent timeline", "Common.define.smartArt.textCircleArrowProcess": "Circle arrow process", "Common.define.smartArt.textCirclePictureHierarchy": "Circle picture hierarchy", "Common.define.smartArt.textCircleProcess": "Circle process", "Common.define.smartArt.textCircleRelationship": "Circle relationship", "Common.define.smartArt.textCircularBendingProcess": "Circular bending process", "Common.define.smartArt.textCircularPictureCallout": "Circular picture callout", "Common.define.smartArt.textClosedChevronProcess": "Closed chevron process", "Common.define.smartArt.textContinuousArrowProcess": "Continuous arrow process", "Common.define.smartArt.textContinuousBlockProcess": "Continuous block process", "Common.define.smartArt.textContinuousCycle": "Continuous cycle", "Common.define.smartArt.textContinuousPictureList": "Continuous picture list", "Common.define.smartArt.textConvergingArrows": "Converging arrows", "Common.define.smartArt.textConvergingRadial": "Converging radial", "Common.define.smartArt.textConvergingText": "Converging text", "Common.define.smartArt.textCounterbalanceArrows": "Counterbalance arrows", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "Cycle matrix", "Common.define.smartArt.textDescendingBlockList": "Descending block list", "Common.define.smartArt.textDescendingProcess": "Descending process", "Common.define.smartArt.textDetailedProcess": "Detailed process", "Common.define.smartArt.textDivergingArrows": "Diverging arrows", "Common.define.smartArt.textDivergingRadial": "Diverging radial", "Common.define.smartArt.textEquation": "Equation", "Common.define.smartArt.textFramedTextPicture": "Framed text picture", "Common.define.smartArt.textFunnel": "Funnel", "Common.define.smartArt.textGear": "Gear", "Common.define.smartArt.textGridMatrix": "Grid matrix", "Common.define.smartArt.textGroupedList": "Grouped list", "Common.define.smartArt.textHalfCircleOrganizationChart": "Half circle organization chart", "Common.define.smartArt.textHexagonCluster": "Hexagon cluster", "Common.define.smartArt.textHexagonRadial": "Hexagon radial", "Common.define.smartArt.textHierarchy": "Hierarchy", "Common.define.smartArt.textHierarchyList": "Hierarchy list", "Common.define.smartArt.textHorizontalBulletList": "Horizontal bullet list", "Common.define.smartArt.textHorizontalHierarchy": "Horizontal hierarchy", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal labeled hierarchy", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontal multi-level hierarchy", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontal organization chart", "Common.define.smartArt.textHorizontalPictureList": "Horizontal picture list", "Common.define.smartArt.textIncreasingArrowProcess": "Increasing arrow process", "Common.define.smartArt.textIncreasingCircleProcess": "Increasing circle process", "Common.define.smartArt.textInterconnectedBlockProcess": "Interconnected block process", "Common.define.smartArt.textInterconnectedRings": "Interconnected rings", "Common.define.smartArt.textInvertedPyramid": "Inverted pyramid", "Common.define.smartArt.textLabeledHierarchy": "Labeled hierarchy", "Common.define.smartArt.textLinearVenn": "Linear Venn", "Common.define.smartArt.textLinedList": "Lined list", "Common.define.smartArt.textList": "List", "Common.define.smartArt.textMatrix": "Matrix", "Common.define.smartArt.textMultidirectionalCycle": "Multidirectional cycle", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Name and title organization chart", "Common.define.smartArt.textNestedTarget": "Nested target", "Common.define.smartArt.textNondirectionalCycle": "Nondirectional cycle", "Common.define.smartArt.textOpposingArrows": "Opposing arrows", "Common.define.smartArt.textOpposingIdeas": "Opposing ideas", "Common.define.smartArt.textOrganizationChart": "Organization chart", "Common.define.smartArt.textOther": "Other", "Common.define.smartArt.textPhasedProcess": "Phased process", "Common.define.smartArt.textPicture": "Picture", "Common.define.smartArt.textPictureAccentBlocks": "Picture accent blocks", "Common.define.smartArt.textPictureAccentList": "Picture accent list", "Common.define.smartArt.textPictureAccentProcess": "Picture accent process", "Common.define.smartArt.textPictureCaptionList": "Picture caption list", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "Picture grid", "Common.define.smartArt.textPictureLineup": "Picture lineup", "Common.define.smartArt.textPictureOrganizationChart": "Picture organization chart", "Common.define.smartArt.textPictureStrips": "Picture strips", "Common.define.smartArt.textPieProcess": "Pie process", "Common.define.smartArt.textPlusAndMinus": "Plus and minus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "Process arrows", "Common.define.smartArt.textProcessList": "Process list", "Common.define.smartArt.textPyramid": "Pyramid", "Common.define.smartArt.textPyramidList": "Pyramid list", "Common.define.smartArt.textRadialCluster": "Radial cluster", "Common.define.smartArt.textRadialCycle": "Radial cycle", "Common.define.smartArt.textRadialList": "Radial list", "Common.define.smartArt.textRadialPictureList": "Radial picture list", "Common.define.smartArt.textRadialVenn": "Radial Venn", "Common.define.smartArt.textRandomToResultProcess": "Random to result process", "Common.define.smartArt.textRelationship": "Relationship", "Common.define.smartArt.textRepeatingBendingProcess": "Repeating bending process", "Common.define.smartArt.textReverseList": "Reverse list", "Common.define.smartArt.textSegmentedCycle": "Segmented cycle", "Common.define.smartArt.textSegmentedProcess": "Segmented process", "Common.define.smartArt.textSegmentedPyramid": "Segmented pyramid", "Common.define.smartArt.textSnapshotPictureList": "Snapshot picture list", "Common.define.smartArt.textSpiralPicture": "Spiral picture", "Common.define.smartArt.textSquareAccentList": "Square accent list", "Common.define.smartArt.textStackedList": "Stacked list", "Common.define.smartArt.textStackedVenn": "Stacked <PERSON>n", "Common.define.smartArt.textStaggeredProcess": "Staggered process", "Common.define.smartArt.textStepDownProcess": "Step down process", "Common.define.smartArt.textStepUpProcess": "Step up process", "Common.define.smartArt.textSubStepProcess": "Sub-step process", "Common.define.smartArt.textTabbedArc": "Tabbed arc", "Common.define.smartArt.textTableHierarchy": "Table hierarchy", "Common.define.smartArt.textTableList": "Table list", "Common.define.smartArt.textTabList": "Tab List", "Common.define.smartArt.textTargetList": "Target list", "Common.define.smartArt.textTextCycle": "Text cycle", "Common.define.smartArt.textThemePictureAccent": "Theme picture accent", "Common.define.smartArt.textThemePictureAlternatingAccent": "Theme picture alternating accent", "Common.define.smartArt.textThemePictureGrid": "Theme picture grid", "Common.define.smartArt.textTitledMatrix": "Titled matrix", "Common.define.smartArt.textTitledPictureAccentList": "Titled picture accent list", "Common.define.smartArt.textTitledPictureBlocks": "Titled picture blocks", "Common.define.smartArt.textTitlePictureLineup": "Title picture lineup", "Common.define.smartArt.textTrapezoidList": "Trapezoid list", "Common.define.smartArt.textUpwardArrow": "Upward arrow", "Common.define.smartArt.textVaryingWidthList": "Varying width list", "Common.define.smartArt.textVerticalAccentList": "Vertical accent list", "Common.define.smartArt.textVerticalArrowList": "Vertical arrow list", "Common.define.smartArt.textVerticalBendingProcess": "Vertical bending process", "Common.define.smartArt.textVerticalBlockList": "Vertical block list", "Common.define.smartArt.textVerticalBoxList": "Vertical box list", "Common.define.smartArt.textVerticalBracketList": "Vertical bracket list", "Common.define.smartArt.textVerticalBulletList": "Vertical bullet list", "Common.define.smartArt.textVerticalChevronList": "Vertical chevron list", "Common.define.smartArt.textVerticalCircleList": "Vertical circle list", "Common.define.smartArt.textVerticalCurvedList": "Vertical curved list", "Common.define.smartArt.textVerticalEquation": "Vertical equation", "Common.define.smartArt.textVerticalPictureAccentList": "Vertical picture accent list", "Common.define.smartArt.textVerticalPictureList": "Vertical picture list", "Common.define.smartArt.textVerticalProcess": "Vertical process", "Common.Translation.textMoreButton": "More", "Common.Translation.tipFileLocked": "Document is locked for editing. You can make changes and save it as local copy later.", "Common.Translation.tipFileReadOnly": "The file is read-only. To keep your changes, save the file with a new name or in a different location.", "Common.Translation.warnFileLocked": "The file is being edited in another app. You can continue editing and save it as a copy.", "Common.Translation.warnFileLockedBtnEdit": "Create a copy", "Common.Translation.warnFileLockedBtnView": "Open for viewing", "Common.UI.ButtonColored.textAutoColor": "Automatic", "Common.UI.ButtonColored.textEyedropper": "Eyedropper", "Common.UI.ButtonColored.textNewColor": "<PERSON><PERSON><PERSON> tùy chỉnh", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON><PERSON><PERSON> có kiểu", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "<PERSON><PERSON><PERSON> t<PERSON>i", "Common.UI.ExtendedColorDialog.textHexErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị thuộc từ 000000 đến FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị số thuộc từ 0 đến 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON><PERSON><PERSON> màu", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Hide password", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Show password", "Common.UI.SearchBar.textFind": "Find", "Common.UI.SearchBar.tipCloseSearch": "Close find", "Common.UI.SearchBar.tipNextResult": "Next result", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Open advanced settings", "Common.UI.SearchBar.tipPreviousResult": "Previous result", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON> sáng kết quả", "Common.UI.SearchDialog.textMatchCase": "<PERSON>ân biệt chữ hoa chữ thường", "Common.UI.SearchDialog.textReplaceDef": "<PERSON><PERSON><PERSON><PERSON> văn bản thay thế", "Common.UI.SearchDialog.textSearchStart": "<PERSON><PERSON><PERSON><PERSON> từ khóa của bạn ở đây", "Common.UI.SearchDialog.textTitle": "<PERSON><PERSON><PERSON> và Thay thế", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "Chỉ toàn bộ từ", "Common.UI.SearchDialog.txtBtnHideReplace": "Ẩn Thay thế", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON>hay thế", "Common.UI.SearchDialog.txtBtnReplaceAll": "<PERSON><PERSON> thế tất cả", "Common.UI.SynchronizeTip.textDontShow": "<PERSON><PERSON>ông hiển thị lại thông báo này", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "Tài liệu đã được thay đổi bởi người dùng khác.<br><PERSON><PERSON> lòng nhấp để lưu thay đổi của bạn và tải lại các cập nhật.", "Common.UI.ThemeColorPalette.textRecentColors": "Recent colors", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "Màu theme", "Common.UI.Themes.txtThemeClassicLight": "Classic Light", "Common.UI.Themes.txtThemeContrastDark": "Contrast Dark", "Common.UI.Themes.txtThemeDark": "Dark", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "Light", "Common.UI.Themes.txtThemeSystem": "Same as system", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "Đ<PERSON><PERSON>", "Common.UI.Window.noButtonText": "K<PERSON>ô<PERSON>", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textDontShow": "<PERSON><PERSON>ông hiển thị lại thông báo này", "Common.UI.Window.textError": "Lỗi", "Common.UI.Window.textInformation": "Thông tin", "Common.UI.Window.textWarning": "<PERSON><PERSON><PERSON> b<PERSON>o", "Common.UI.Window.yesButtonText": "<PERSON><PERSON>", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Accent", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "Background", "Common.Utils.ThemeColor.txtBlack": "Black", "Common.Utils.ThemeColor.txtBlue": "Blue", "Common.Utils.ThemeColor.txtBrightGreen": "Bright green", "Common.Utils.ThemeColor.txtBrown": "<PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Dark blue", "Common.Utils.ThemeColor.txtDarker": "Darker", "Common.Utils.ThemeColor.txtDarkGray": "Dark gray", "Common.Utils.ThemeColor.txtDarkGreen": "Dark green", "Common.Utils.ThemeColor.txtDarkPurple": "Dark purple", "Common.Utils.ThemeColor.txtDarkRed": "Dark red", "Common.Utils.ThemeColor.txtDarkTeal": "Dark teal", "Common.Utils.ThemeColor.txtDarkYellow": "Dark yellow", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "<PERSON>", "Common.Utils.ThemeColor.txtGreen": "Green", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "Light blue", "Common.Utils.ThemeColor.txtLighter": "Lighter", "Common.Utils.ThemeColor.txtLightGray": "Light gray", "Common.Utils.ThemeColor.txtLightGreen": "Light green", "Common.Utils.ThemeColor.txtLightOrange": "Light orange", "Common.Utils.ThemeColor.txtLightYellow": "Light yellow", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "Pink", "Common.Utils.ThemeColor.txtPurple": "Purple", "Common.Utils.ThemeColor.txtRed": "Red", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Sky blue", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Text", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "White", "Common.Utils.ThemeColor.txtYellow": "Yellow", "Common.Views.About.txtAddress": "địa chỉ:", "Common.Views.About.txtLicensee": "NGƯỜI ĐƯỢC CẤP GIẤY PHÉP", "Common.Views.About.txtLicensor": "NGƯỜI CẤP GIẤY PHÉP", "Common.Views.About.txtMail": "email:", "Common.Views.About.txtPoweredBy": "Được hỗ trợ bởi", "Common.Views.About.txtTel": "ĐT.: ", "Common.Views.About.txtVersion": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "Add", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Apply as you work", "Common.Views.AutoCorrectDialog.textAutoCorrect": "AutoCorrect", "Common.Views.AutoCorrectDialog.textAutoFormat": "AutoFormat as you type", "Common.Views.AutoCorrectDialog.textBy": "By", "Common.Views.AutoCorrectDialog.textDelete": "Delete", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet and network paths with hyperlinks", "Common.Views.AutoCorrectDialog.textMathCorrect": "Math AutoCorrect", "Common.Views.AutoCorrectDialog.textNewRowCol": "Include new rows and columns in table", "Common.Views.AutoCorrectDialog.textRecognized": "Recognized functions", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "The following expressions are recognized math expressions. They will not be automatically italicized.", "Common.Views.AutoCorrectDialog.textReplace": "Replace", "Common.Views.AutoCorrectDialog.textReplaceText": "Replace as you type", "Common.Views.AutoCorrectDialog.textReplaceType": "Replace text as you type", "Common.Views.AutoCorrectDialog.textReset": "Reset", "Common.Views.AutoCorrectDialog.textResetAll": "Reset to default", "Common.Views.AutoCorrectDialog.textRestore": "Rest<PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "AutoCorrect", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Recognized functions must contain only the letters A through Z, uppercase or lowercase.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Any expression you added will be removed and the removed ones will be restored. Do you want to continue?", "Common.Views.AutoCorrectDialog.warnReplace": "The autocorrect entry for %1 already exists. Do you want to replace it?", "Common.Views.AutoCorrectDialog.warnReset": "Any autocorrect you added will be removed and the changed ones will be restored to their original values. Do you want to continue?", "Common.Views.AutoCorrectDialog.warnRestore": "The autocorrect entry for %1 will be reset to its original value. Do you want to continue?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "Author A to Z", "Common.Views.Comments.mniAuthorDesc": "Author Z to A", "Common.Views.Comments.mniDateAsc": "Oldest", "Common.Views.Comments.mniDateDesc": "Newest", "Common.Views.Comments.mniFilterGroups": "Filter by Group", "Common.Views.Comments.mniPositionAsc": "From top", "Common.Views.Comments.mniPositionDesc": "From bottom", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> lu<PERSON>n", "Common.Views.Comments.textAddCommentToDoc": "<PERSON><PERSON><PERSON><PERSON> bình luận vào tài liệu", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAll": "All", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "Đ<PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "Close comments", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON> lu<PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "<PERSON><PERSON><PERSON><PERSON> bình luận của bạn ở đây", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> lu<PERSON>n", "Common.Views.Comments.textOpenAgain": "Mở lại", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON> lờ<PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON> gi<PERSON>i quy<PERSON>", "Common.Views.Comments.textSort": "Sort comments", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "You have no permission to reopen the comment", "Common.Views.Comments.txtEmpty": "There are no comments in the sheet.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON><PERSON>ông hiển thị lại thông báo này", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON><PERSON>, cắt và dán bằng cách sử dụng các nút trên thanh công cụ của trình soạn thảo và các tác vụ trình đơn ngữ cảnh sẽ chỉ được thực hiện trong tab trình soạn thảo này.<br><br> Để sao chép hoặc dán vào hoặc từ các ứng dụng bên ngoài tab trình soạn thảo sử dụng các kết hợp bàn phím sau đây:", "Common.Views.CopyWarningDialog.textTitle": "Sao ché<PERSON>, Cắt và Dán", "Common.Views.CopyWarningDialog.textToCopy": "để sao chép", "Common.Views.CopyWarningDialog.textToCut": "<PERSON><PERSON>", "Common.Views.CopyWarningDialog.textToPaste": "<PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "<PERSON><PERSON> tả<PERSON>...", "Common.Views.DocumentAccessDialog.textTitle": "Cài đặt chia sẻ", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "Eraser", "Common.Views.Draw.hintSelect": "Select", "Common.Views.Draw.txtEraser": "Eraser", "Common.Views.Draw.txtHighlighter": "Highlighter", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Pen", "Common.Views.Draw.txtSelect": "Select", "Common.Views.Draw.txtSize": "Size", "Common.Views.EditNameDialog.textLabel": "Label:", "Common.Views.EditNameDialog.textLabelError": "Label must not be empty.", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "<PERSON>ài liệu hiện đang được chỉnh sửa bởi nhiều người dùng.", "Common.Views.Header.textAddFavorite": "<PERSON> as favorite", "Common.Views.Header.textAdvSettings": "Advanced settings", "Common.Views.Header.textBack": "<PERSON><PERSON> tới Tài liệu", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "<PERSON><PERSON>", "Common.Views.Header.textHideLines": "Hide Rulers", "Common.Views.Header.textHideStatusBar": "Combine sheet and status bars", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "Read only", "Common.Views.Header.textRemoveFavorite": "Remove from Favorites", "Common.Views.Header.textSaveBegin": "<PERSON><PERSON> l<PERSON>...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON> sửa đổi", "Common.Views.Header.textSaveEnd": "<PERSON><PERSON> lưu mọi thay đổi", "Common.Views.Header.textSaveExpander": "<PERSON><PERSON> lưu mọi thay đổi", "Common.Views.Header.textShare": "Share", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "<PERSON><PERSON><PERSON><PERSON> lý quyền truy cập tài liệu", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipGoEdit": "Chỉnh sửa file hiện tại", "Common.Views.Header.tipPrint": "In file", "Common.Views.Header.tipPrintQuick": "Quick print", "Common.Views.Header.tipRedo": "Redo", "Common.Views.Header.tipSave": "Save", "Common.Views.Header.tipSearch": "Find", "Common.Views.Header.tipUndo": "Undo", "Common.Views.Header.tipUndock": "Undock into separate window", "Common.Views.Header.tipUsers": "View users", "Common.Views.Header.tipViewSettings": "View settings", "Common.Views.Header.tipViewUsers": "<PERSON>em người dùng và quản lý quyền truy cập tài liệu", "Common.Views.Header.txtAccessRights": "<PERSON><PERSON> đ<PERSON>i quyền truy cập", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON> tên", "Common.Views.History.textCloseHistory": "Close history", "Common.Views.History.textHideAll": "Hide detailed changes", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "Rest<PERSON>", "Common.Views.History.textShowAll": "Show detailed changes", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "Dán URL hình ảnh:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Trường này phải là một URL có định dạng \"http://www.example.com\"", "Common.Views.ListSettingsDialog.textBulleted": "Bulleted", "Common.Views.ListSettingsDialog.textFromFile": "From file", "Common.Views.ListSettingsDialog.textFromStorage": "From storage", "Common.Views.ListSettingsDialog.textFromUrl": "From URL", "Common.Views.ListSettingsDialog.textNumbering": "Numbered", "Common.Views.ListSettingsDialog.textSelect": "Select from", "Common.Views.ListSettingsDialog.tipChange": "Change bullet", "Common.Views.ListSettingsDialog.txtBullet": "Bullet", "Common.Views.ListSettingsDialog.txtColor": "Color", "Common.Views.ListSettingsDialog.txtImage": "Image", "Common.Views.ListSettingsDialog.txtImport": "Import", "Common.Views.ListSettingsDialog.txtNewBullet": "New bullet", "Common.Views.ListSettingsDialog.txtNewImage": "New image", "Common.Views.ListSettingsDialog.txtNone": "None", "Common.Views.ListSettingsDialog.txtOfText": "% of text", "Common.Views.ListSettingsDialog.txtSize": "Size", "Common.Views.ListSettingsDialog.txtStart": "Start at", "Common.Views.ListSettingsDialog.txtSymbol": "Symbol", "Common.Views.ListSettingsDialog.txtTitle": "List settings", "Common.Views.ListSettingsDialog.txtType": "Type", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "Close file", "Common.Views.OpenDialog.textInvalidRange": "Invalid cells range", "Common.Views.OpenDialog.textSelectData": "Select data", "Common.Views.OpenDialog.txtAdvanced": "Advanced", "Common.Views.OpenDialog.txtColon": "Colon", "Common.Views.OpenDialog.txtComma": "Comma", "Common.Views.OpenDialog.txtDelimiter": "<PERSON><PERSON><PERSON> phân cách", "Common.Views.OpenDialog.txtDestData": "Choose where to put the data", "Common.Views.OpenDialog.txtEmpty": "This field is required", "Common.Views.OpenDialog.txtEncoding": "Mã hóa", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON> kh<PERSON>u không đúng.", "Common.Views.OpenDialog.txtOpenFile": "Nhập mật khẩu để mở tệp", "Common.Views.OpenDialog.txtOther": "K<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtPassword": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "Preview", "Common.Views.OpenDialog.txtProtected": "Once you enter the password and open the file, the current password to the file will be reset.", "Common.Views.OpenDialog.txtSemicolon": "Semicolon", "Common.Views.OpenDialog.txtSpace": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch", "Common.Views.OpenDialog.txtTab": "Tab", "Common.Views.OpenDialog.txtTitle": "Chọn %1 lựa chọn", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON> <PERSON><PERSON><PERSON><PERSON> b<PERSON> vệ", "Common.Views.PasswordDialog.txtDescription": "Set a password to protect this document", "Common.Views.PasswordDialog.txtIncorrectPwd": "Confirmation password is not identical", "Common.Views.PasswordDialog.txtPassword": "Password", "Common.Views.PasswordDialog.txtRepeat": "Repeat password", "Common.Views.PasswordDialog.txtTitle": "Set password", "Common.Views.PasswordDialog.txtWarning": "Chú ý: Nếu bạn mất hoặc quên mật khẩu, bạn không thể khôi phục mật khẩu.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON> t<PERSON>", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "Plugin", "Common.Views.Plugins.strPlugins": "Phần mở rộng", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "<PERSON><PERSON><PERSON> đ<PERSON>u", "Common.Views.Plugins.textStop": "Dừng", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "Encrypt with password", "Common.Views.Protection.hintDelPwd": "Delete password", "Common.Views.Protection.hintPwd": "Change or delete password", "Common.Views.Protection.hintSignature": "Add digital signature or signature line", "Common.Views.Protection.txtAddPwd": "Add password", "Common.Views.Protection.txtChangePwd": "Change password", "Common.Views.Protection.txtDeletePwd": "Delete password", "Common.Views.Protection.txtEncrypt": "Encrypt", "Common.Views.Protection.txtInvisibleSignature": "Add digital signature", "Common.Views.Protection.txtSignature": "Signature", "Common.Views.Protection.txtSignatureLine": "Add signature line", "Common.Views.RecentFiles.txtOpenRecent": "Open Recent", "Common.Views.RenameDialog.textName": "Tên file", "Common.Views.RenameDialog.txtInvalidName": "Tên file không đư<PERSON><PERSON> chứa bất kỳ ký tự nào sau đây:", "Common.Views.ReviewChanges.hintNext": "To next change", "Common.Views.ReviewChanges.hintPrev": "To previous change", "Common.Views.ReviewChanges.strFast": "Fast", "Common.Views.ReviewChanges.strFastDesc": "Real-time co-editing. All changes are saved automatically.", "Common.Views.ReviewChanges.strStrict": "Strict", "Common.Views.ReviewChanges.strStrictDesc": "Use the 'Save' button to sync the changes you and others make.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Accept current change", "Common.Views.ReviewChanges.tipCoAuthMode": "Set co-editing mode", "Common.Views.ReviewChanges.tipCommentRem": "Delete comments", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Delete current comments", "Common.Views.ReviewChanges.tipCommentResolve": "Resolve comments", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolve current comments", "Common.Views.ReviewChanges.tipHistory": "Show version history", "Common.Views.ReviewChanges.tipRejectCurrent": "Reject current change", "Common.Views.ReviewChanges.tipReview": "Track changes", "Common.Views.ReviewChanges.tipReviewView": "Select the mode you want the changes to be displayed", "Common.Views.ReviewChanges.tipSetDocLang": "Set document language", "Common.Views.ReviewChanges.tipSetSpelling": "Spell checking", "Common.Views.ReviewChanges.tipSharing": "Manage document access rights", "Common.Views.ReviewChanges.txtAccept": "Accept", "Common.Views.ReviewChanges.txtAcceptAll": "Accept all changes", "Common.Views.ReviewChanges.txtAcceptChanges": "Accept changes", "Common.Views.ReviewChanges.txtAcceptCurrent": "Accept current change", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "Close", "Common.Views.ReviewChanges.txtCoAuthMode": "Co-editing Mode", "Common.Views.ReviewChanges.txtCommentRemAll": "Delete all comments", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Delete current comments", "Common.Views.ReviewChanges.txtCommentRemMy": "Delete my comments", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Delete my current comments", "Common.Views.ReviewChanges.txtCommentRemove": "Delete", "Common.Views.ReviewChanges.txtCommentResolve": "Resolve", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolve all comments", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolve current comments", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolve my comments", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolve My Current Comments", "Common.Views.ReviewChanges.txtDocLang": "Language", "Common.Views.ReviewChanges.txtFinal": "All changes accepted (Preview)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Version History", "Common.Views.ReviewChanges.txtMarkup": "All changes (Editing)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "Next", "Common.Views.ReviewChanges.txtOriginal": "All changes rejected (Preview)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Previous", "Common.Views.ReviewChanges.txtReject": "Reject", "Common.Views.ReviewChanges.txtRejectAll": "Reject All Changes", "Common.Views.ReviewChanges.txtRejectChanges": "Reject changes", "Common.Views.ReviewChanges.txtRejectCurrent": "Reject Current Change", "Common.Views.ReviewChanges.txtSharing": "Sharing", "Common.Views.ReviewChanges.txtSpelling": "Spell checking", "Common.Views.ReviewChanges.txtTurnon": "Track Changes", "Common.Views.ReviewChanges.txtView": "Display Mode", "Common.Views.ReviewPopover.textAdd": "Add", "Common.Views.ReviewPopover.textAddReply": "Add reply", "Common.Views.ReviewPopover.textCancel": "Cancel", "Common.Views.ReviewPopover.textClose": "Close", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Enter your comment here", "Common.Views.ReviewPopover.textMention": "+mention will provide access to the document and send an email", "Common.Views.ReviewPopover.textMentionNotify": "+mention will notify the user via email", "Common.Views.ReviewPopover.textOpenAgain": "Open again", "Common.Views.ReviewPopover.textReply": "Reply", "Common.Views.ReviewPopover.textResolve": "Resolve", "Common.Views.ReviewPopover.textViewResolved": "You have no permission to reopen the comment", "Common.Views.ReviewPopover.txtDeleteTip": "Delete", "Common.Views.ReviewPopover.txtEditTip": "Edit", "Common.Views.SaveAsDlg.textLoading": "Loading", "Common.Views.SaveAsDlg.textTitle": "Folder for save", "Common.Views.SearchPanel.textByColumns": "By columns", "Common.Views.SearchPanel.textByRows": "By rows", "Common.Views.SearchPanel.textCaseSensitive": "Case sensitive", "Common.Views.SearchPanel.textCell": "Cell", "Common.Views.SearchPanel.textCloseSearch": "Close find", "Common.Views.SearchPanel.textContentChanged": "Document changed.", "Common.Views.SearchPanel.textFind": "Find", "Common.Views.SearchPanel.textFindAndReplace": "Find and replace", "Common.Views.SearchPanel.textFormula": "Formula", "Common.Views.SearchPanel.textFormulas": "Formulas", "Common.Views.SearchPanel.textItemEntireCell": "Entire cell contents", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} items successfully replaced.", "Common.Views.SearchPanel.textLookIn": "Look in", "Common.Views.SearchPanel.textMatchUsingRegExp": "Match using regular expressions", "Common.Views.SearchPanel.textName": "Name", "Common.Views.SearchPanel.textNoMatches": "No matches", "Common.Views.SearchPanel.textNoSearchResults": "No search results", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} items replaced. Remaining {2} items are locked by other users.", "Common.Views.SearchPanel.textReplace": "Replace", "Common.Views.SearchPanel.textReplaceAll": "Replace All", "Common.Views.SearchPanel.textReplaceWith": "Replace with", "Common.Views.SearchPanel.textSearch": "Search", "Common.Views.SearchPanel.textSearchAgain": "{0}Perform new search{1} for accurate results.", "Common.Views.SearchPanel.textSearchHasStopped": "Search has stopped", "Common.Views.SearchPanel.textSearchOptions": "Search options", "Common.Views.SearchPanel.textSearchResults": "Search results: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textSelectDataRange": "Select Data range", "Common.Views.SearchPanel.textSheet": "Sheet", "Common.Views.SearchPanel.textSpecificRange": "Specific range", "Common.Views.SearchPanel.textTooManyResults": "There are too many results to show here", "Common.Views.SearchPanel.textValue": "Value", "Common.Views.SearchPanel.textValues": "Values", "Common.Views.SearchPanel.textWholeWords": "Whole words only", "Common.Views.SearchPanel.textWithin": "Within", "Common.Views.SearchPanel.textWorkbook": "Workbook", "Common.Views.SearchPanel.tipNextResult": "Next result", "Common.Views.SearchPanel.tipPreviousResult": "Previous result", "Common.Views.SelectFileDlg.textLoading": "Loading", "Common.Views.SelectFileDlg.textTitle": "Select data source", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "Bold", "Common.Views.SignDialog.textCertificate": "Certificate", "Common.Views.SignDialog.textChange": "Change", "Common.Views.SignDialog.textInputName": "Input signer name", "Common.Views.SignDialog.textItalic": "Italic", "Common.Views.SignDialog.textNameError": "Signer name must not be empty.", "Common.Views.SignDialog.textPurpose": "Purpose for signing this document", "Common.Views.SignDialog.textSelect": "Select", "Common.Views.SignDialog.textSelectImage": "Select image", "Common.Views.SignDialog.textSignature": "Signature looks as", "Common.Views.SignDialog.textTitle": "Sign document", "Common.Views.SignDialog.textUseImage": "or click 'Select Image' to use a picture as signature", "Common.Views.SignDialog.textValid": "Valid from %1 to %2", "Common.Views.SignDialog.tipFontName": "Font name", "Common.Views.SignDialog.tipFontSize": "Font size", "Common.Views.SignSettingsDialog.textAllowComment": "Allow signer to add comment in the signature dialog", "Common.Views.SignSettingsDialog.textDefInstruction": "Before signing this document, verify that the content you are signing is correct.", "Common.Views.SignSettingsDialog.textInfoEmail": "Suggested signer's e-mail", "Common.Views.SignSettingsDialog.textInfoName": "Suggested signer", "Common.Views.SignSettingsDialog.textInfoTitle": "Suggested signer's title", "Common.Views.SignSettingsDialog.textInstructions": "Instructions for signer", "Common.Views.SignSettingsDialog.textShowDate": "Show sign date in signature line", "Common.Views.SignSettingsDialog.textTitle": "Signature setup", "Common.Views.SignSettingsDialog.txtEmpty": "This field is required", "Common.Views.SymbolTableDialog.textCharacter": "Character", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX value", "Common.Views.SymbolTableDialog.textCopyright": "Copyright sign", "Common.Views.SymbolTableDialog.textDCQuote": "Closing double quote", "Common.Views.SymbolTableDialog.textDOQuote": "Opening double quote", "Common.Views.SymbolTableDialog.textEllipsis": "Horizontal ellipsis", "Common.Views.SymbolTableDialog.textEmDash": "Em dash", "Common.Views.SymbolTableDialog.textEmSpace": "Em space", "Common.Views.SymbolTableDialog.textEnDash": "En dash", "Common.Views.SymbolTableDialog.textEnSpace": "En space", "Common.Views.SymbolTableDialog.textFont": "Font", "Common.Views.SymbolTableDialog.textNBHyphen": "Non-breaking hyphen", "Common.Views.SymbolTableDialog.textNBSpace": "No-break space", "Common.Views.SymbolTableDialog.textPilcrow": "Pilcrow sign", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em space", "Common.Views.SymbolTableDialog.textRange": "Range", "Common.Views.SymbolTableDialog.textRecent": "Recently used symbols", "Common.Views.SymbolTableDialog.textRegistered": "Registered sign", "Common.Views.SymbolTableDialog.textSCQuote": "Closing single quote", "Common.Views.SymbolTableDialog.textSection": "Section sign", "Common.Views.SymbolTableDialog.textShortcut": "Shortcut key", "Common.Views.SymbolTableDialog.textSHyphen": "Soft hyphen", "Common.Views.SymbolTableDialog.textSOQuote": "Opening single quote", "Common.Views.SymbolTableDialog.textSpecial": "Special characters", "Common.Views.SymbolTableDialog.textSymbols": "Symbols", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Trademark symbol", "Common.Views.UserNameDialog.textDontShow": "Don't ask me again", "Common.Views.UserNameDialog.textLabel": "Label:", "Common.Views.UserNameDialog.textLabelError": "Label must not be empty.", "SSE.Controllers.DataTab.strSheet": "Sheet", "SSE.Controllers.DataTab.textAddExternalData": "The link to an external source has been added. You can update such links in the Data tab.", "SSE.Controllers.DataTab.textColumns": "Columns", "SSE.Controllers.DataTab.textContinue": "Continue", "SSE.Controllers.DataTab.textDontUpdate": "Don't Update", "SSE.Controllers.DataTab.textEmptyUrl": "You need to specify URL.", "SSE.Controllers.DataTab.textRows": "Rows", "SSE.Controllers.DataTab.textTurnOff": "Turn off AutoUpdate", "SSE.Controllers.DataTab.textUpdate": "Update", "SSE.Controllers.DataTab.textWizard": "Text to Columns", "SSE.Controllers.DataTab.txtDataValidation": "Data Validation", "SSE.Controllers.DataTab.txtErrorExternalLink": "Error: updating is failed", "SSE.Controllers.DataTab.txtExpand": "Expand", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "The data next to the selection will not be removed. Do you want to expand the selection to include the adjacent data or continue with the currently selected cells only?", "SSE.Controllers.DataTab.txtExtendDataValidation": "The selection contains some cells without Data Validation settings.<br>Do you want to extend Data Validation to these cells?", "SSE.Controllers.DataTab.txtImportWizard": "Text Import Wizard", "SSE.Controllers.DataTab.txtRemDuplicates": "Remove Duplicates", "SSE.Controllers.DataTab.txtRemoveDataValidation": "The selection contains more than one type of validation.<br>Erase current settings and continue?", "SSE.Controllers.DataTab.txtRemSelected": "Remove in selected", "SSE.Controllers.DataTab.txtUrlTitle": "Paste a data URL", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "This workbook contains links to external sources which update automatically. This might be unsafe.<br><br>If you trust them, press Continue.", "SSE.Controllers.DataTab.warnUpdateExternalData": "This workbook contains links to one or more external sources that could be unsafe.<br>If you trust the links, update them to get the latest data.", "SSE.Controllers.DocumentHolder.alignmentText": "<PERSON><PERSON>n chỉnh", "SSE.Controllers.DocumentHolder.centerText": "Trung tâm", "SSE.Controllers.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON> h<PERSON>", "SSE.Controllers.DocumentHolder.deleteText": "Xóa", "SSE.Controllers.DocumentHolder.errorInvalidLink": "<PERSON><PERSON> chiếu liên kết không tồn tại. <PERSON><PERSON> lòng sửa liên kết hoặc xóa nó.", "SSE.Controllers.DocumentHolder.guestText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON> trái", "SSE.Controllers.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON> trên", "SSE.Controllers.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.leftText": "Trái", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "SSE.Controllers.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "AutoCorrect options", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON><PERSON><PERSON> rộng cột {0} ký hiệu ({1} pixel)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "<PERSON><PERSON><PERSON> cao hàng {0} đi<PERSON>m ({1} pixel)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Ấn CTRL và nhấp vào liên kết", "SSE.Controllers.DocumentHolder.textInsertLeft": "<PERSON><PERSON><PERSON> sang trái", "SSE.Controllers.DocumentHolder.textInsertTop": "<PERSON><PERSON><PERSON> lên đ<PERSON>u", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Paste special", "SSE.Controllers.DocumentHolder.textStopExpand": "Stop automatically expanding tables", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Phần tử này đang được chỉnh sửa bởi một người dùng khác.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Above average", "SSE.Controllers.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON><PERSON> đườ<PERSON> viền dưới cùng", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "<PERSON><PERSON><PERSON><PERSON> dấu phân số", "SSE.Controllers.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON><PERSON> đường kẻ ngang", "SSE.Controllers.DocumentHolder.txtAddLB": "Thê<PERSON> đường kẻ dưới cùng bên trái", "SSE.Controllers.DocumentHolder.txtAddLeft": "<PERSON><PERSON>ê<PERSON> đườ<PERSON> viền trái", "SSE.Controllers.DocumentHolder.txtAddLT": "Thê<PERSON> đường kẻ trên cùng bên trái", "SSE.Controllers.DocumentHolder.txtAddRight": "<PERSON><PERSON>ê<PERSON> đườ<PERSON> viền bên phải", "SSE.Controllers.DocumentHolder.txtAddTop": "<PERSON>hê<PERSON> đường viền trên cùng", "SSE.Controllers.DocumentHolder.txtAddVer": "<PERSON><PERSON><PERSON><PERSON> đường kẻ dọc", "SSE.Controllers.DocumentHolder.txtAlignToChar": "<PERSON><PERSON>n chỉnh theo ký tự", "SSE.Controllers.DocumentHolder.txtAll": "(All)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Returns the entire contents of the table or specified table columns including column headers, data and total rows", "SSE.Controllers.DocumentHolder.txtAnd": "and", "SSE.Controllers.DocumentHolder.txtBegins": "Begins with", "SSE.Controllers.DocumentHolder.txtBelowAve": "Below average", "SSE.Controllers.DocumentHolder.txtBlanks": "(Blanks)", "SSE.Controllers.DocumentHolder.txtBorderProps": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h đư<PERSON> vền", "SSE.Controllers.DocumentHolder.txtBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Controllers.DocumentHolder.txtByField": "%1 of %2", "SSE.Controllers.DocumentHolder.txtColumn": "Column", "SSE.Controllers.DocumentHolder.txtColumnAlign": "<PERSON><PERSON><PERSON> chỉnh cột", "SSE.Controllers.DocumentHolder.txtContains": "Contains", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Link copied to the clipboard", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Returns the data cells of the table or specified table columns", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ch thư<PERSON><PERSON> đ<PERSON>i số", "SSE.Controllers.DocumentHolder.txtDeleteArg": "<PERSON><PERSON><PERSON> đ<PERSON>i số", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "<PERSON><PERSON><PERSON> ng<PERSON>t thủ công", "SSE.Controllers.DocumentHolder.txtDeleteChars": "<PERSON><PERSON><PERSON> các ký tự kèm theo", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "<PERSON><PERSON><PERSON> các ký tự và dấu phân cách kèm theo", "SSE.Controllers.DocumentHolder.txtDeleteEq": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> trình", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON><PERSON> biểu đồ", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON> c<PERSON>c", "SSE.Controllers.DocumentHolder.txtEnds": "Ends with", "SSE.Controllers.DocumentHolder.txtEquals": "Equals", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Equal to cell color", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Equal to font color", "SSE.Controllers.DocumentHolder.txtExpand": "Mở rộng và sắp xếp", "SSE.Controllers.DocumentHolder.txtExpandSort": "Dữ liệu bên cạnh vùng đã chọn sẽ không được sắp xếp. Bạn muốn mở rộng vùng chọn để bao gồm dữ liệu liền kề hay tiếp tục sắp xếp các ô đã chọn hiện tại?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Bottom", "SSE.Controllers.DocumentHolder.txtFilterTop": "Top", "SSE.Controllers.DocumentHolder.txtFractionLinear": "<PERSON><PERSON> sang phân số viết ngang", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON> sang phân số viết lệch", "SSE.Controllers.DocumentHolder.txtFractionStacked": "<PERSON><PERSON> đ<PERSON> sang phân số viết đứng", "SSE.Controllers.DocumentHolder.txtGreater": "Greater than", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Greater than or equal to", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "<PERSON><PERSON><PERSON><PERSON> đồ trên văn bản", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> dư<PERSON><PERSON> văn bản", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Returns the column headers for the table or specified table columns", "SSE.Controllers.DocumentHolder.txtHeight": "<PERSON><PERSON><PERSON> cao", "SSE.Controllers.DocumentHolder.txtHideBottom": "Ẩn đường viền dưới cùng", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Ẩn giới hạn dưới", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Ẩn dấu ngoặc đóng", "SSE.Controllers.DocumentHolder.txtHideDegree": "Ẩn cấp độ", "SSE.Controllers.DocumentHolder.txtHideHor": "Ẩn đường kẻ ngang", "SSE.Controllers.DocumentHolder.txtHideLB": "Ẩn đường kẻ dưới cùng bên trái", "SSE.Controllers.DocumentHolder.txtHideLeft": "Ẩn đường viền trái", "SSE.Controllers.DocumentHolder.txtHideLT": "Ẩn đường kẻ trên cùng bên trái", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Ẩn dấu ngoặc mở", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Ẩn placeholder", "SSE.Controllers.DocumentHolder.txtHideRight": "Ẩn đường viền bên phải", "SSE.Controllers.DocumentHolder.txtHideTop": "Ẩn đường viền trên cùng", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Ẩn giới hạn trên cùng", "SSE.Controllers.DocumentHolder.txtHideVer": "Ẩn đường kẻ dọc", "SSE.Controllers.DocumentHolder.txtImportWizard": "Text Import Wizard", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "<PERSON><PERSON><PERSON> k<PERSON>ch thư<PERSON><PERSON> đối số", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "<PERSON><PERSON><PERSON> đ<PERSON>i số sau", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "<PERSON><PERSON><PERSON> đ<PERSON>i số trước", "SSE.Controllers.DocumentHolder.txtInsertBreak": "<PERSON><PERSON><PERSON> ng<PERSON>t thủ công", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON>ng trình sau", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> trình trước", "SSE.Controllers.DocumentHolder.txtItems": "items", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Keep text only", "SSE.Controllers.DocumentHolder.txtLess": "Less than", "SSE.Controllers.DocumentHolder.txtLessEquals": "Less than or equal to", "SSE.Controllers.DocumentHolder.txtLimitChange": "<PERSON>hay đổi giới hạn địa điểm", "SSE.Controllers.DocumentHolder.txtLimitOver": "<PERSON><PERSON><PERSON><PERSON> hạn trên văn bản", "SSE.Controllers.DocumentHolder.txtLimitUnder": "<PERSON><PERSON><PERSON><PERSON> hạn dưới văn bản", "SSE.Controllers.DocumentHolder.txtLockSort": "Data is found next to your selection, but you do not have sufficient permissions to change those cells.<br>Do you wish to continue with the current selection?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Chỉnh dấu ngoặc phù hợp với độ cao đối số", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON><PERSON> chỉnh ma trận", "SSE.Controllers.DocumentHolder.txtNoChoices": "<PERSON><PERSON><PERSON>ng có lựa chọn để điền vào ô.<br>Chỉ có thể chọn các giá trị văn bản từ cột này để thay thế.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Does not begin with", "SSE.Controllers.DocumentHolder.txtNotContains": "Does not contain", "SSE.Controllers.DocumentHolder.txtNotEnds": "Does not end with", "SSE.Controllers.DocumentHolder.txtNotEquals": "Does not equal", "SSE.Controllers.DocumentHolder.txtOr": "or", "SSE.Controllers.DocumentHolder.txtOverbar": "<PERSON><PERSON><PERSON> trên v<PERSON><PERSON> bản", "SSE.Controllers.DocumentHolder.txtPaste": "Dán", "SSE.Controllers.DocumentHolder.txtPasteBorders": "<PERSON><PERSON><PERSON> thức không có đường viền", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "<PERSON><PERSON>ng thức + chiều rộng cột", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "<PERSON><PERSON><PERSON> dạng đ<PERSON>ch", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Chỉ dán định dạng", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "<PERSON><PERSON><PERSON> thức + định dạng số", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Chỉ dán công thức", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "<PERSON><PERSON><PERSON> thức + tất cả định dạng", "SSE.Controllers.DocumentHolder.txtPasteLink": "<PERSON><PERSON> li<PERSON> k<PERSON>", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> liên kết", "SSE.Controllers.DocumentHolder.txtPasteMerge": "<PERSON><PERSON><PERSON> dạng gộp có điều kiện", "SSE.Controllers.DocumentHolder.txtPastePicture": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "<PERSON><PERSON><PERSON> dạng nguồn", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "<PERSON><PERSON> vị", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "<PERSON><PERSON><PERSON> trị + tất cả định dạng", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "<PERSON><PERSON><PERSON> trị + đ<PERSON><PERSON> dạng số", "SSE.Controllers.DocumentHolder.txtPasteValues": "Chỉ dán giá trị", "SSE.Controllers.DocumentHolder.txtPercent": "percent", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Redo table autoexpansion", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "<PERSON><PERSON><PERSON> d<PERSON>u phân số", "SSE.Controllers.DocumentHolder.txtRemLimit": "<PERSON>óa gi<PERSON> hạn", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Xóa ký tự dấu phụ", "SSE.Controllers.DocumentHolder.txtRemoveBar": "<PERSON>óa v<PERSON>", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Do you want to remove this signature?<br>It can't be undone.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Xóa script", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Xóa chỉ số dưới", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Xóa chỉ số trên", "SSE.Controllers.DocumentHolder.txtRowHeight": "<PERSON><PERSON><PERSON> cao hàng", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Các script sau văn bản", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Các script trước văn bản", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "<PERSON><PERSON><PERSON> thị giới hạn dưới", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Hiển thị dấu ngoặc đóng", "SSE.Controllers.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON> thị cấp độ", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Hiển thị dấu ngoặc mở", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Hiển thị placeholder", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "<PERSON><PERSON><PERSON> thị giới hạn trên", "SSE.Controllers.DocumentHolder.txtSorting": "<PERSON><PERSON><PERSON>p", "SSE.Controllers.DocumentHolder.txtSortSelected": "<PERSON><PERSON><PERSON> x<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "<PERSON>éo dài ngoặc", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Choose only this row of the specified column", "SSE.Controllers.DocumentHolder.txtTop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Returns the total rows for the table or specified table columns", "SSE.Controllers.DocumentHolder.txtUnderbar": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> v<PERSON><PERSON> bản", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Undo table autoexpansion", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Use text import wizard", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Clicking this link can be harmful to your device and data.<br>Are you sure you want to continue?", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Controllers.DocumentHolder.warnFilterError": "You need at least one field in the Values area in order to apply a value filter.", "SSE.Controllers.FormulaDialog.sCategoryAll": "All", "SSE.Controllers.FormulaDialog.sCategoryCube": "C<PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Custom", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Database", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Date & Time", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Engineering", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Financial", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Information", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 last used", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logical", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Lookup & Reference", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Math & Trig", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistical", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Text & Data", "SSE.Controllers.LeftMenu.newDocumentTitle": "<PERSON><PERSON><PERSON> t<PERSON>h không tên", "SSE.Controllers.LeftMenu.textByColumns": "<PERSON>", "SSE.Controllers.LeftMenu.textByRows": "<PERSON>", "SSE.Controllers.LeftMenu.textFormulas": "<PERSON><PERSON><PERSON> thức", "SSE.Controllers.LeftMenu.textItemEntireCell": "<PERSON><PERSON>n bộ nội dung ô", "SSE.Controllers.LeftMenu.textLoadHistory": "Loading version history...", "SSE.Controllers.LeftMenu.textLookin": "<PERSON>hìn v<PERSON>o", "SSE.Controllers.LeftMenu.textNoTextFound": "<PERSON><PERSON><PERSON><PERSON> thể tìm thấy dữ liệu bạn đang tìm kiếm. Vui lòng điều chỉnh các tùy chọn tìm kiếm của bạn.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "<PERSON><PERSON> thực hiện thay thế. {0} lần xuất hiện đã bị bỏ qua.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "<PERSON><PERSON> thực hiện tìm kiếm. Số lần thay thế: {0}", "SSE.Controllers.LeftMenu.textSave": "Save", "SSE.Controllers.LeftMenu.textSearch": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "SSE.Controllers.LeftMenu.textSheet": "<PERSON><PERSON> t<PERSON>h", "SSE.Controllers.LeftMenu.textValues": "<PERSON><PERSON><PERSON> trị", "SSE.Controllers.LeftMenu.textWarning": "<PERSON><PERSON><PERSON> b<PERSON>o", "SSE.Controllers.LeftMenu.textWithin": "<PERSON><PERSON> k<PERSON>ng", "SSE.Controllers.LeftMenu.textWorkbook": "Workbook", "SSE.Controllers.LeftMenu.txtUntitled": "Untitled", "SSE.Controllers.LeftMenu.warnDownloadAs": "Nếu bạn tiếp tục lưu ở định dạng này tất cả các tính năng trừ văn bản sẽ bị mất.<br>Bạn có chắc là muốn tiếp tục?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "The CSV format does not support saving a multi-sheet file.<br>To keep the selected format and save only the current sheet, press Save.<br>To save the current spreadsheet, click Cancel and save it in a different format.", "SSE.Controllers.Main.confirmAddCellWatches": "This action will add {0} cell watches.<br>Do you want to continue?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "This action will add only {0} cell watches by memory save reason.<br>Do you want to continue?", "SSE.Controllers.Main.confirmMaxChangesSize": "The size of actions exceeds the limitation set for your server.<br>Press \"Undo\" to cancel your last action or press \"Continue\" to keep action locally (you need to download the file or copy its content to make sure nothing is lost).", "SSE.Controllers.Main.confirmMoveCellRange": "Phạm vi ô đích có thể chứa dữ liệu. Tiế<PERSON> tục thao tác?", "SSE.Controllers.Main.confirmPutMergeRange": "<PERSON><PERSON> liệu nguồn chứa các ô được gộp.<br><PERSON>úng đã không được gộp trước khi chúng được dán vào bảng này.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Formulas in the header row will be removed and converted to static text.<br>Do you want to continue?", "SSE.Controllers.Main.confirmReplaceHFPicture": "Only one picture can be inserted in each section of the header.<br>Press \"Replace\" to replace existing picture.<br>Press \"Keep\" to keep existing picture.", "SSE.Controllers.Main.convertationTimeoutText": "<PERSON><PERSON> quá thời gian chờ chuyển đổi.", "SSE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON><PERSON><PERSON> \"OK\" để trở lại danh sách tài liệu.", "SSE.Controllers.Main.criticalErrorTitle": "Lỗi", "SSE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON> về không thành công.", "SSE.Controllers.Main.downloadTextText": "<PERSON><PERSON> tải về bảng t<PERSON>h...", "SSE.Controllers.Main.downloadTitleText": "<PERSON><PERSON> tả<PERSON> về Bảng t<PERSON>h", "SSE.Controllers.Main.errNoDuplicates": "No duplicate values found.", "SSE.Controllers.Main.errorAccessDeny": "Bạn đang cố gắng thực hiện hành động mà bạn không có quyền.<br><PERSON><PERSON> lòng liên hệ với quản trị viên Server <PERSON><PERSON><PERSON> li<PERSON> củ<PERSON> bạn.", "SSE.Controllers.Main.errorArgsRange": "Lỗi trong công thức đã nhập.<br><PERSON><PERSON><PERSON> vi đối số không chính xác.", "SSE.Controllers.Main.errorAutoFilterChange": "<PERSON><PERSON> tác này không đ<PERSON><PERSON><PERSON>, vì nó đang cố gắng chuyển các ô trong một bảng vào trang tính của bạn.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "<PERSON>h<PERSON>ng thể thực hiện thao tác với các ô đã chọn vì bạn không thể di chuyển một phần của bảng.<br>Chọn phạm vi dữ liệu khác để toàn bộ bảng được di chuyển và thử lại.", "SSE.Controllers.Main.errorAutoFilterDataRange": "<PERSON><PERSON><PERSON><PERSON> thể thực hiện thao tác cho phạm vi ô đã chọn.<br><PERSON>ọn phạm vi dữ liệu thống nhất khác với phạm vi hiện tại và thử lại.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "<PERSON><PERSON><PERSON><PERSON> thể thực hiện thao tác vì vùng này chứa các ô được lọc.<br><PERSON><PERSON> lòng bỏ ẩn các phần tử được lọc và thử lại.", "SSE.Controllers.Main.errorBadImageUrl": "URL hình ảnh không chính xác", "SSE.Controllers.Main.errorCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the spreadsheet.", "SSE.Controllers.Main.errorCannotUngroup": "Cannot ungroup. To start an outline, select the detail rows or columns and group them.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "You cannot use this command on a protected sheet. To use this command, unprotect the sheet.<br>You might be requested to enter a password.", "SSE.Controllers.Main.errorChangeArray": "You cannot change part of an array.", "SSE.Controllers.Main.errorChangeFilteredRange": "This will change a filtered range on your worksheet.<br>To complete this task, please remove AutoFilters.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "The cell or chart you are trying to change is on a protected sheet.<br>To make a change, unprotect the sheet. You might be requested to enter a password.", "SSE.Controllers.Main.errorCircularReference": "There are one or more circular references where a formula refers to its own cell either directly or indirectly.<br>Try removing or changing these references, or moving the formulas to different cells.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "<PERSON>ất kết nối server. K<PERSON>ông thể chỉnh sửa tài liệu ngay lúc này.", "SSE.Controllers.Main.errorConnectToServer": "<PERSON><PERSON><PERSON><PERSON> thể lưu tài liệu. Vui lòng kiểm tra cài đặt kết nối hoặc liên hệ với quản trị viên của bạn.<br><PERSON><PERSON> bạn nhấp vào nút 'OK', bạn sẽ được nhắc tải xuống tài liệu.", "SSE.Controllers.Main.errorConvertXml": "The file has an unsupported format.<br>Only XML Spreadsheet 2003 format can be used.", "SSE.Controllers.Main.errorCopyMultiselectArea": "<PERSON><PERSON><PERSON>ng thể sử dụng lệnh này với nhiều lựa chọn.<br><PERSON><PERSON><PERSON> một phạm vi và thử lại.", "SSE.Controllers.Main.errorCountArg": "Lỗi trong công thức đã nhập.<br><PERSON><PERSON><PERSON><PERSON> dùng đúng số lượng đối số.", "SSE.Controllers.Main.errorCountArgExceed": "Lỗi trong công thức đã nhập.<br><PERSON><PERSON> vượt quá số lượng đối số.", "SSE.Controllers.Main.errorCreateDefName": "Không thể chỉnh sửa các phạm vi được đặt tên hiện tại và hiện thời không thể tạo các phạm vi mới vì một số trong đó đang được chỉnh sửa.", "SSE.Controllers.Main.errorCreateRange": "The existing ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Controllers.Main.errorDatabaseConnection": "Lỗi bên ngoài.<br>Lỗi kết nối cơ sở dữ liệu. <PERSON>ui lòng liên hệ bộ phận hỗ trợ trong trường hợp lỗi vẫn còn.", "SSE.Controllers.Main.errorDataEncrypted": "Encrypted changes have been received, they cannot be deciphered.", "SSE.Controllers.Main.errorDataRange": "Phạm vi dữ liệu không ch<PERSON>h xác.", "SSE.Controllers.Main.errorDataValidate": "The value you entered is not valid.<br>A user has restricted values that can be entered into this cell.", "SSE.Controllers.Main.errorDefaultMessage": "Mã lỗi: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "You are trying to delete a column that contains a locked cell. Locked cells cannot be deleted while the worksheet is protected.<br>To delete a locked cell, unprotect the sheet. You might be requested to enter a password.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "You are trying to delete a row that contains a locked cell. Locked cells cannot be deleted while the worksheet is protected.<br>To delete a locked cell, unprotect the sheet. You might be requested to enter a password.", "SSE.Controllers.Main.errorDependentsNoFormulas": "The Trace Dependents command found no formulas that refer to the active cell.", "SSE.Controllers.Main.errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "SSE.Controllers.Main.errorEditingDownloadas": "An error occurred during the work with the document.<br>Use the 'Download as' option to save the file backup copy to a drive.", "SSE.Controllers.Main.errorEditingSaveas": "An error occurred during the work with the document.<br>Use the 'Save as...' option to save the file backup copy to a drive.", "SSE.Controllers.Main.errorEditView": "The existing sheet view cannot be edited and the new ones cannot be created at the moment as some of them are being edited.", "SSE.Controllers.Main.errorEmailClient": "No email client could be found.", "SSE.Controllers.Main.errorFilePassProtect": "<PERSON><PERSON><PERSON> liệu được bảo vệ bằng mật khẩu và không thể mở được.", "SSE.Controllers.Main.errorFileRequest": "Lỗi bên ngoài.<br>Lỗi yêu cầu file. <PERSON><PERSON> lòng liên hệ với bộ phận hỗ trợ trong trường hợp lỗi vẫn còn.", "SSE.Controllers.Main.errorFileSizeExceed": "The file size exceeds the limitation set for your server.<br>Please contact your Document Server administrator for details.", "SSE.Controllers.Main.errorFileVKey": "Lỗi bên ngoài.<br>Key bảo mật không chính xác. <PERSON><PERSON> lòng liên hệ với bộ phận hỗ trợ trong trường hợp lỗi vẫn còn.", "SSE.Controllers.Main.errorFillRange": "<PERSON>h<PERSON>ng thể điền vào phạm vi các ô đã chọn.<br>T<PERSON><PERSON> cả các ô được gộp phải có cùng kích thước.", "SSE.Controllers.Main.errorForceSave": "An error occurred while saving the file. Please use the 'Download as' option to save the file to a drive or try again later.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Cannot enter a formula for an item or field name in a pivot table report.", "SSE.Controllers.Main.errorFormulaName": "Lỗi trong công thức đã nhập.<br>Tê<PERSON> công thức không chính xác.", "SSE.Controllers.Main.errorFormulaParsing": "Lỗi nội bộ khi phân tích cú pháp công thức.", "SSE.Controllers.Main.errorFrmlMaxLength": "The length of your formula exceeds the limit of 8192 characters.<br>Please edit it and try again.", "SSE.Controllers.Main.errorFrmlMaxReference": "You cannot enter this formula because it has too many values,<br>cell references, and/or names.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Text values in formulas are limited to 255 characters.<br>Use the CONCATENATE function or concatenation operator (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "<PERSON><PERSON>m này đề cập đến một trang tính không tồn tại.<br><PERSON><PERSON> lòng kiểm tra dữ liệu và thử lại.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Operation could not be completed for the selected cell range.<br>Select a range so that the first table row was on the same row<br>and the resulting table overlapped the current one.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Operation could not be completed for the selected cell range.<br>Select a range which does not include other tables.", "SSE.Controllers.Main.errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "SSE.Controllers.Main.errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "SSE.Controllers.Main.errorInvalidRef": "<PERSON><PERSON><PERSON><PERSON> đúng tên cho vùng chọn hoặc tham chiếu hợp lệ để đi đến.", "SSE.Controllers.Main.errorKeyEncrypt": "Key descriptor k<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.errorKeyExpire": "Key của descriptor đ<PERSON> hết hạn", "SSE.Controllers.Main.errorLabledColumnsPivot": "To create a pivot table, use data that is organized as a list with labeled columns.", "SSE.Controllers.Main.errorLoadingFont": "Fonts are not loaded.<br>Please contact your Document Server administrator.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "The reference for the location or data range is not valid.", "SSE.Controllers.Main.errorLockedAll": "<PERSON><PERSON> tác không thể được thực hiện vì trang tính đã bị khóa bởi một người dùng khác.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "One of the cells involved in the goal seek process has been modified by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "<PERSON><PERSON><PERSON> không thể thay đổi dữ liệu bên trong pivot table.", "SSE.Controllers.Main.errorLockedWorksheetRename": "Hiện tại bạn không thể đổi tên trang tính này vì nó đang được đổi tên bởi một người dùng khác", "SSE.Controllers.Main.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Controllers.Main.errorMoveRange": "<PERSON><PERSON><PERSON><PERSON> thể thay đổi phần ô đã gộp", "SSE.Controllers.Main.errorMoveSlicerError": "Table slicers cannot be copied from one workbook to another.<br>Try again by selecting the entire table and the slicers.", "SSE.Controllers.Main.errorMultiCellFormula": "Multi-cell array formulas are not allowed in tables.", "SSE.Controllers.Main.errorNoDataToParse": "No data was selected to parse.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.Main.errorOpenWarning": "Chi<PERSON>u dài của một trong các công thức trong file này vượt quá<br>số ký tự cho phép và nó đã bị xóa.", "SSE.Controllers.Main.errorOperandExpected": "<PERSON><PERSON> pháp hàm được nhập vào không chính xác. <PERSON>ui lòng kiểm tra xem bạn có thiếu một trong các dấu ngoặc - '(' hoặc ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "The password you supplied is not correct.<br>Verify that the CAPS LOCK key is off and be sure to use the correct capitalization.", "SSE.Controllers.Main.errorPasteInPivot": "We can't make this change for the selected cells because it will affect a pivot table.<br>Use the field list to change the report.", "SSE.Controllers.Main.errorPasteMaxRange": "Vùng sao chép và dán không trùng khớp.<br><PERSON>ui lòng chọn vùng có cùng kích thước hoặc nhấp vào ô đầu tiên trong một hàng để dán các ô được sao chép.", "SSE.Controllers.Main.errorPasteMultiSelect": "This action cannot be done on a multiple range selection.<br>Select a single range and try again.", "SSE.Controllers.Main.errorPasteSlicerError": "Table slicers cannot be copied from one workbook to another.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Pivot table field name already exists.", "SSE.Controllers.Main.errorPivotGroup": "<PERSON><PERSON> group that selection.", "SSE.Controllers.Main.errorPivotOverlap": "A pivot table report cannot overlap a table.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "The Pivot Table report was saved without the underlying data.<br>Use the 'Refresh' button to update the report.", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "The Trace Precedents command requires that the active cell contain a formula which includes a valid references.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "<PERSON><PERSON>t tiếc là không thể cùng lúc in nhiều hơn 1500 trang trong phiên bản chương trình hiện tại.<br><PERSON>ạn chế này sẽ bị xóa trong các bản phát hành sắp tới.", "SSE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON><PERSON> không thành công", "SSE.Controllers.Main.errorProtectedRange": "This range is not allowed for editing.", "SSE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "SSE.Controllers.Main.errorServerVersion": "<PERSON><PERSON><PERSON> bản trình chỉnh sửa này đã được cập nhật. Trang sẽ được tải lại để áp dụng các thay đổi.", "SSE.Controllers.Main.errorSessionAbsolute": "Phiên chỉnh sửa tài liệu đã hết hạn. <PERSON><PERSON> lòng tải lại trang.", "SSE.Controllers.Main.errorSessionIdle": "Tài liệu đã không được chỉnh sửa trong một thời gian khá dài. <PERSON>ui lòng tải lại trang.", "SSE.Controllers.Main.errorSessionToken": "<PERSON><PERSON><PERSON> nối với server bị gi<PERSON> đ<PERSON>. <PERSON><PERSON> lòng tải lại trang.", "SSE.Controllers.Main.errorSetPassword": "Password could not be set.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Location reference is not valid because the cells are not all in the same column or row.<br>Select cells that are all in a single column or row.", "SSE.Controllers.Main.errorStockChart": "Thứ tự hàng không chính xác. <PERSON><PERSON> xây dựng một biểu đồ chứng khoán đặt dữ liệu trên giấy theo thứ tự sau:<br>gi<PERSON> mở phiên, gi<PERSON> cao nh<PERSON>t, g<PERSON><PERSON> thấ<PERSON> nhất, gi<PERSON> đóng phiên.", "SSE.Controllers.Main.errorToken": "To<PERSON> bảo mật tài liệu không đư<PERSON><PERSON> tạo đúng.<br><PERSON><PERSON> lòng liên hệ với quản trị viên Server <PERSON><PERSON><PERSON> liệu của bạn.", "SSE.Controllers.Main.errorTokenExpire": "To<PERSON> bảo mật tài liệu đã hết hạn.<br><PERSON><PERSON> lòng liên hệ với quản trị viên Server <PERSON><PERSON><PERSON> liệu của bạn.", "SSE.Controllers.Main.errorUnexpectedGuid": "Lỗi bên ngoài.<br>GUID ngoài ý muốn. <PERSON><PERSON> lòng liên hệ với bộ phận hỗ trợ trong trường hợp lỗi vẫn còn.", "SSE.Controllers.Main.errorUpdateVersion": "<PERSON><PERSON><PERSON> bản file này đã được thay đổi. Trang này sẽ được tải lại.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.", "SSE.Controllers.Main.errorUserDrop": "<PERSON><PERSON><PERSON><PERSON> thể truy cập file ngay lúc này.", "SSE.Controllers.Main.errorUsersExceed": "<PERSON><PERSON> vư<PERSON>t quá số người dùng đ<PERSON><PERSON><PERSON> phép của gói dịch vụ này", "SSE.Controllers.Main.errorViewerDisconnect": "<PERSON><PERSON>t kết nối. Bạn vẫn có thể xem tài liệu,<br>nhưng không thể tải về hoặc in cho đến khi kết nối được khôi phục.", "SSE.Controllers.Main.errorWrongBracketsCount": "Lỗi trong công thức đã nhập.<br>Đã sử dụng sai số dấu ngoặc.", "SSE.Controllers.Main.errorWrongOperator": "Lỗi trong công thức đã nhập. <PERSON><PERSON>ng sai toán tử.<br><PERSON><PERSON> lòng sửa lỗi.", "SSE.Controllers.Main.errorWrongPassword": "The password you supplied is not correct.", "SSE.Controllers.Main.errRemDuplicates": "Duplicate values found and deleted: {0}, unique values left: {1}.", "SSE.Controllers.Main.leavePageText": "Bạn có các thay đổi chưa lưu trong bảng tính này. Nhấp vào 'Ở lại Trang này' rồi 'Lư<PERSON>' để lưu chúng. Nhấp vào 'Rời trang này' để bỏ tất cả các thay đổi chưa lưu.", "SSE.Controllers.Main.leavePageTextOnClose": "All unsaved changes in this spreadsheet will be lost.<br> Click \"Cancel\" then \"Save\" to save them. Click \"OK\" to discard all the unsaved changes.", "SSE.Controllers.Main.loadFontsTextText": "<PERSON><PERSON> tải dữ liệu...", "SSE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON> t<PERSON> liệu", "SSE.Controllers.Main.loadFontTextText": "<PERSON><PERSON> tải dữ liệu...", "SSE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON> t<PERSON> liệu", "SSE.Controllers.Main.loadImagesTextText": "<PERSON><PERSON> tải h<PERSON>nh <PERSON>...", "SSE.Controllers.Main.loadImagesTitleText": "<PERSON><PERSON> tả<PERSON> h<PERSON>nh <PERSON>", "SSE.Controllers.Main.loadImageTextText": "<PERSON><PERSON> tải h<PERSON>nh <PERSON>...", "SSE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON> tả<PERSON> h<PERSON>nh <PERSON>", "SSE.Controllers.Main.loadingDocumentTitleText": "<PERSON><PERSON> tải bảng t<PERSON>h", "SSE.Controllers.Main.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "SSE.Controllers.Main.openErrorText": "<PERSON><PERSON><PERSON> ra lỗi khi mở file", "SSE.Controllers.Main.openTextText": "<PERSON><PERSON> mở bảng tính...", "SSE.Controllers.Main.openTitleText": "Mở bảng t<PERSON>h", "SSE.Controllers.Main.pastInMergeAreaError": "<PERSON><PERSON><PERSON><PERSON> thể thay đổi phần ô đã gộp", "SSE.Controllers.Main.printTextText": "<PERSON><PERSON> in bảng tính...", "SSE.Controllers.Main.printTitleText": "<PERSON>ang in bảng tính", "SSE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON> l<PERSON>", "SSE.Controllers.Main.requestEditFailedMessageText": "Hiện có ai đó đang chỉnh sửa tài liệu này. <PERSON><PERSON> lòng thử lại sau.", "SSE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> bị từ chối", "SSE.Controllers.Main.saveErrorText": "<PERSON><PERSON><PERSON> ra lỗi khi lưu file", "SSE.Controllers.Main.saveErrorTextDesktop": "This file cannot be saved or created.<br>Possible reasons are: <br>1. The file is read-only. <br>2. The file is being edited by other users. <br>3. The disk is full or corrupted.", "SSE.Controllers.Main.saveTextText": "<PERSON><PERSON> l<PERSON> bảng t<PERSON>...", "SSE.Controllers.Main.saveTitleText": "<PERSON><PERSON> l<PERSON> bảng <PERSON>", "SSE.Controllers.Main.scriptLoadError": "The connection is too slow, some of the components could not be loaded. Please reload the page.", "SSE.Controllers.Main.textAnonymous": "Nặc danh", "SSE.Controllers.Main.textApplyAll": "Apply to all equations", "SSE.Controllers.Main.textBuyNow": "<PERSON><PERSON><PERSON> c<PERSON>p trang web", "SSE.Controllers.Main.textChangesSaved": "All changes saved", "SSE.Controllers.Main.textClose": "Close", "SSE.Controllers.Main.textCloseTip": "<PERSON><PERSON><PERSON><PERSON> để đóng gợi ý", "SSE.Controllers.Main.textConfirm": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "SSE.Controllers.Main.textContactUs": "<PERSON><PERSON><PERSON> hệ bộ phận bán hàng", "SSE.Controllers.Main.textContinue": "Continue", "SSE.Controllers.Main.textConvertEquation": "This equation was created with an old version of the equation editor which is no longer supported. To edit it, convert the equation to the Office Math ML format.<br>Convert now?", "SSE.Controllers.Main.textCustomLoader": "Please note that according to the terms of the license you are not entitled to change the loader.<br>Please contact our Sales Department to get a quote.", "SSE.Controllers.Main.textDisconnect": "Connection is lost", "SSE.Controllers.Main.textFillOtherRows": "Fill other rows", "SSE.Controllers.Main.textFormulaFilledAllRows": "Formula filled {0} rows have data. Filling other empty rows may take a few minutes.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Formula filled first {0} rows. Filling other empty rows may take a few minutes.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Formula filled only first {0} rows have data by memory save reason. There are other {1} rows have data in this sheet. You can fill them manually.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Formula filled only first {0} rows by memory save reason. Other rows in this sheet don't have data.", "SSE.Controllers.Main.textGuest": "Guest", "SSE.Controllers.Main.textHasMacros": "The file contains automatic macros.<br>Do you want to run macros?", "SSE.Controllers.Main.textKeep": "Keep", "SSE.Controllers.Main.textLearnMore": "Learn more", "SSE.Controllers.Main.textLoadingDocument": "<PERSON><PERSON> tải bảng t<PERSON>h", "SSE.Controllers.Main.textLongName": "Enter a name that is less than 128 characters.", "SSE.Controllers.Main.textNeedSynchronize": "You have updates", "SSE.Controllers.Main.textNo": "K<PERSON>ô<PERSON>", "SSE.Controllers.Main.textNoLicenseTitle": "<PERSON><PERSON><PERSON> bản mã nguồn mở ONLYOFFICE", "SSE.Controllers.Main.textPaidFeature": "Paid feature", "SSE.Controllers.Main.textPleaseWait": "<PERSON><PERSON> t<PERSON> này có thể lâu hơn dự kiến. <PERSON><PERSON> lòng chờ...", "SSE.Controllers.Main.textReconnect": "Connection is restored", "SSE.Controllers.Main.textRemember": "Remember my choice for all files", "SSE.Controllers.Main.textRememberMacros": "Remember my choice for all macros", "SSE.Controllers.Main.textRenameError": "User name must not be empty.", "SSE.Controllers.Main.textRenameLabel": "Enter a name to be used for collaboration", "SSE.Controllers.Main.textReplace": "Replace", "SSE.Controllers.Main.textRequestMacros": "A macro makes a request to URL. Do you want to allow the request to the %1?", "SSE.Controllers.Main.textShape": "<PERSON><PERSON><PERSON> d<PERSON>", "SSE.Controllers.Main.textStrict": "<PERSON><PERSON> độ nghiêm ngặt", "SSE.Controllers.Main.textText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textTryQuickPrint": "You have selected Quick print: the entire document will be printed on the last selected or default printer.<br>Do you want to continue?", "SSE.Controllers.Main.textTryUndoRedo": "<PERSON><PERSON><PERSON> chức năng Hoàn tác/<PERSON>à<PERSON> lại bị vô hiệu hóa cho chế độ đồng chỉnh sửa Nhanh.<br>Nhấp vào nút 'Chế độ nghiêm ngặt' để chuyển sang chế độ đồng chỉnh sửa Nghiêm ngặt để chỉnh sửa các file mà không có sự can thiệp của người dùng khác và gửi các thay đổi của bạn chỉ sau khi bạn đã lưu. Bạn có thể chuyển đổi giữa các chế độ đồng chỉnh sửa bằng cách sử dụng Cài đặt Nâng cao trình biên tập.", "SSE.Controllers.Main.textTryUndoRedoWarn": "The Undo/Redo functions are disabled for the Fast co-editing mode.", "SSE.Controllers.Main.textUndo": "Undo", "SSE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "SSE.Controllers.Main.textUpdating": "Updating", "SSE.Controllers.Main.textYes": "<PERSON><PERSON>", "SSE.Controllers.Main.titleLicenseExp": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> hết hạn", "SSE.Controllers.Main.titleLicenseNotActive": "License not active", "SSE.Controllers.Main.titleServerVersion": "<PERSON><PERSON> cập nhật trình chỉnh sửa", "SSE.Controllers.Main.titleUpdateVersion": "Version changed", "SSE.Controllers.Main.txtAccent": "<PERSON><PERSON><PERSON> phụ", "SSE.Controllers.Main.txtAll": "(All)", "SSE.Controllers.Main.txtArt": "<PERSON><PERSON><PERSON> bản của bạn ở đây", "SSE.Controllers.Main.txtBasicShapes": "<PERSON><PERSON><PERSON> d<PERSON>ng c<PERSON> bản", "SSE.Controllers.Main.txtBlank": "(blank)", "SSE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtByField": "%1 of %2", "SSE.Controllers.Main.txtCallouts": "Callout", "SSE.Controllers.Main.txtCharts": "<PERSON><PERSON><PERSON><PERSON> đồ", "SSE.Controllers.Main.txtClearFilter": "Clear filter", "SSE.Controllers.Main.txtColLbls": "Column labels", "SSE.Controllers.Main.txtColumn": "Column", "SSE.Controllers.Main.txtConfidential": "Confidential", "SSE.Controllers.Main.txtDate": "Date", "SSE.Controllers.Main.txtDays": "Days", "SSE.Controllers.Main.txtDiagramTitle": "Tiêu đề biểu đồ", "SSE.Controllers.Main.txtEditingMode": "Đặt chế độ chỉnh sửa...", "SSE.Controllers.Main.txtErrorLoadHistory": "History loading failed", "SSE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON> tên có hình vẽ", "SSE.Controllers.Main.txtFile": "File", "SSE.Controllers.Main.txtGrandTotal": "Grand Total", "SSE.Controllers.Main.txtGroup": "Group", "SSE.Controllers.Main.txtHours": "Hours", "SSE.Controllers.Main.txtInfo": "Info", "SSE.Controllers.Main.txtLines": "Đường kẻ", "SSE.Controllers.Main.txtMath": "<PERSON><PERSON>", "SSE.Controllers.Main.txtMinutes": "Minutes", "SSE.Controllers.Main.txtMonths": "Months", "SSE.Controllers.Main.txtMultiSelect": "Multi-Select", "SSE.Controllers.Main.txtNone": "None", "SSE.Controllers.Main.txtOr": "%1 or %2", "SSE.Controllers.Main.txtPage": "Page", "SSE.Controllers.Main.txtPageOf": "Page %1 of %2", "SSE.Controllers.Main.txtPages": "Pages", "SSE.Controllers.Main.txtPicture": "Picture", "SSE.Controllers.Main.txtPivotTable": "PivotTable", "SSE.Controllers.Main.txtPreparedBy": "Prepared by", "SSE.Controllers.Main.txtPrintArea": "Print_Area", "SSE.Controllers.Main.txtQuarter": "Qtr", "SSE.Controllers.Main.txtQuarters": "Quarters", "SSE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON> chữ nhật", "SSE.Controllers.Main.txtRow": "Row", "SSE.Controllers.Main.txtRowLbls": "Row Labels", "SSE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "SSE.Controllers.Main.txtScheme_Aspect": "Aspect", "SSE.Controllers.Main.txtScheme_Blue": "Blue", "SSE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "SSE.Controllers.Main.txtScheme_Blue_II": "Blue II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "SSE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "SSE.Controllers.Main.txtScheme_Green": "Green", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "SSE.Controllers.Main.txtScheme_Marquee": "Marquee", "SSE.Controllers.Main.txtScheme_Median": "Median", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Orange", "SSE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "SSE.Controllers.Main.txtScheme_Paper": "Paper", "SSE.Controllers.Main.txtScheme_Red": "Red", "SSE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "SSE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "SSE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "SSE.Controllers.Main.txtScheme_Violet": "Violet", "SSE.Controllers.Main.txtScheme_Violet_II": "Violet II", "SSE.Controllers.Main.txtScheme_Yellow": "Yellow", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "SSE.Controllers.Main.txtSeconds": "Seconds", "SSE.Controllers.Main.txtSeries": "Chuỗi", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Line Callout 1 (Border and Accent Bar)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Line Callout 2 (Border and Accent Bar)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Line Callout 3 (Border and Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout1": "Line Callout 1 (Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout2": "Line Callout 2 (Accent Bar)", "SSE.Controllers.Main.txtShape_accentCallout3": "Line Callout 3 (Accent Bar)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Back or previous button", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Beginning button", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Blank button", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Document Button", "SSE.Controllers.Main.txtShape_actionButtonEnd": "End button", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Forward or next button", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Help button", "SSE.Controllers.Main.txtShape_actionButtonHome": "Home button", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Information button", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Movie Button", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Return Button", "SSE.Controllers.Main.txtShape_actionButtonSound": "Sound Button", "SSE.Controllers.Main.txtShape_arc": "Arc", "SSE.Controllers.Main.txtShape_bentArrow": "Bent arrow", "SSE.Controllers.Main.txtShape_bentConnector5": "Elbow connector", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Elbow arrow connector", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Elbow double-arrow connector", "SSE.Controllers.Main.txtShape_bentUpArrow": "Bent up arrow", "SSE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_blockArc": "Block arc", "SSE.Controllers.Main.txtShape_borderCallout1": "Line Callout 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Line Callout 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Line Callout 3", "SSE.Controllers.Main.txtShape_bracePair": "Double brace", "SSE.Controllers.Main.txtShape_callout1": "Line Callout 1 (No Border)", "SSE.Controllers.Main.txtShape_callout2": "Line Callout 2 (No Border)", "SSE.Controllers.Main.txtShape_callout3": "Line Callout 3 (No Border)", "SSE.Controllers.Main.txtShape_can": "Can", "SSE.Controllers.Main.txtShape_chevron": "Chevron", "SSE.Controllers.Main.txtShape_chord": "Chord", "SSE.Controllers.Main.txtShape_circularArrow": "Circular arrow", "SSE.Controllers.Main.txtShape_cloud": "Cloud", "SSE.Controllers.Main.txtShape_cloudCallout": "Cloud callout", "SSE.Controllers.Main.txtShape_corner": "Corner", "SSE.Controllers.Main.txtShape_cube": "C<PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Curved connector", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Curved arrow connector", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Curved double-arrow connector", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Curved down arrow", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Curved left arrow", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Curved right arrow", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Curved up arrow", "SSE.Controllers.Main.txtShape_decagon": "Decagon", "SSE.Controllers.Main.txtShape_diagStripe": "Diagonal stripe", "SSE.Controllers.Main.txtShape_diamond": "Diamond", "SSE.Controllers.Main.txtShape_dodecagon": "Dodecagon", "SSE.Controllers.Main.txtShape_donut": "Donut", "SSE.Controllers.Main.txtShape_doubleWave": "Double Wave", "SSE.Controllers.Main.txtShape_downArrow": "Down Arrow", "SSE.Controllers.Main.txtShape_downArrowCallout": "Down Arrow Callout", "SSE.Controllers.Main.txtShape_ellipse": "Ellipse", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Curved down ribbon", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Curved up ribbon", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Flowchart: Alternate process", "SSE.Controllers.Main.txtShape_flowChartCollate": "Flowchart: Collate", "SSE.Controllers.Main.txtShape_flowChartConnector": "Flowchart: Connector", "SSE.Controllers.Main.txtShape_flowChartDecision": "Flowchart: Decision", "SSE.Controllers.Main.txtShape_flowChartDelay": "Flowchart: Delay", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Flowchart: Display", "SSE.Controllers.Main.txtShape_flowChartDocument": "Flowchart: Document", "SSE.Controllers.Main.txtShape_flowChartExtract": "Flowchart: Extract", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Flowchart: Data", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Flowchart: Internal storage", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Flowchart: Magnetic disk", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Flowchart: Direct access storage", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Flowchart: Sequential access storage", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Flowchart: Manual input", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Flowchart: Manual operation", "SSE.Controllers.Main.txtShape_flowChartMerge": "Flowchart: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Flowchart: Multidocument ", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Flowchart: Off-page Connector", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Flowchart: Stored data", "SSE.Controllers.Main.txtShape_flowChartOr": "Flowchart: Or", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Flowchart: Predefined Process", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Flowchart: Preparation", "SSE.Controllers.Main.txtShape_flowChartProcess": "Flowchart: Process", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Flowchart: Card", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Flowchart: Punched tape", "SSE.Controllers.Main.txtShape_flowChartSort": "Flowchart: Sort", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Flowchart: Summing junction", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Flowchart: Terminator", "SSE.Controllers.Main.txtShape_foldedCorner": "Folded Corner", "SSE.Controllers.Main.txtShape_frame": "<PERSON>ame", "SSE.Controllers.Main.txtShape_halfFrame": "Half frame", "SSE.Controllers.Main.txtShape_heart": "Heart", "SSE.Controllers.Main.txtShape_heptagon": "Heptagon", "SSE.Controllers.Main.txtShape_hexagon": "Hexagon", "SSE.Controllers.Main.txtShape_homePlate": "Pentagon", "SSE.Controllers.Main.txtShape_horizontalScroll": "Horizontal scroll", "SSE.Controllers.Main.txtShape_irregularSeal1": "Explosion 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Explosion 2", "SSE.Controllers.Main.txtShape_leftArrow": "Left Arrow", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Left arrow callout", "SSE.Controllers.Main.txtShape_leftBrace": "Left brace", "SSE.Controllers.Main.txtShape_leftBracket": "Left bracket", "SSE.Controllers.Main.txtShape_leftRightArrow": "Left right arrow", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Left right arrow callout", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Left right up arrow", "SSE.Controllers.Main.txtShape_leftUpArrow": "Left up arrow", "SSE.Controllers.Main.txtShape_lightningBolt": "Lightning bolt", "SSE.Controllers.Main.txtShape_line": "Line", "SSE.Controllers.Main.txtShape_lineWithArrow": "Arrow", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Double arrow", "SSE.Controllers.Main.txtShape_mathDivide": "Division", "SSE.Controllers.Main.txtShape_mathEqual": "Equal", "SSE.Controllers.Main.txtShape_mathMinus": "Minus", "SSE.Controllers.Main.txtShape_mathMultiply": "Multiply", "SSE.Controllers.Main.txtShape_mathNotEqual": "Not Equal", "SSE.Controllers.Main.txtShape_mathPlus": "Plus", "SSE.Controllers.Main.txtShape_moon": "Moon", "SSE.Controllers.Main.txtShape_noSmoking": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> \"<PERSON>hông\"", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Notched right arrow", "SSE.Controllers.Main.txtShape_octagon": "Octagon", "SSE.Controllers.Main.txtShape_parallelogram": "Parallelogram", "SSE.Controllers.Main.txtShape_pentagon": "Pentagon", "SSE.Controllers.Main.txtShape_pie": "Pie", "SSE.Controllers.Main.txtShape_plaque": "Sign", "SSE.Controllers.Main.txtShape_plus": "Plus", "SSE.Controllers.Main.txtShape_polyline1": "Scribble", "SSE.Controllers.Main.txtShape_polyline2": "Freeform", "SSE.Controllers.Main.txtShape_quadArrow": "Quad arrow", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Quad arrow callout", "SSE.Controllers.Main.txtShape_rect": "Rectangle", "SSE.Controllers.Main.txtShape_ribbon": "Down Ribbon", "SSE.Controllers.Main.txtShape_ribbon2": "Up ribbon", "SSE.Controllers.Main.txtShape_rightArrow": "Right Arrow", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Right arrow callout", "SSE.Controllers.Main.txtShape_rightBrace": "Right Brace", "SSE.Controllers.Main.txtShape_rightBracket": "Right Bracket", "SSE.Controllers.Main.txtShape_round1Rect": "Round Single Corner Rectangle", "SSE.Controllers.Main.txtShape_round2DiagRect": "Round Diagonal Corner Rectangle", "SSE.Controllers.Main.txtShape_round2SameRect": "Round Same Side Corner Rectangle", "SSE.Controllers.Main.txtShape_roundRect": "Round Corner Rectangle", "SSE.Controllers.Main.txtShape_rtTriangle": "Right Triangle", "SSE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_snip1Rect": "Snip single corner rectangle", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Snip diagonal corner rectangle", "SSE.Controllers.Main.txtShape_snip2SameRect": "Snip same side corner rectangle", "SSE.Controllers.Main.txtShape_snipRoundRect": "Snip and Round Single Corner Rectangle", "SSE.Controllers.Main.txtShape_spline": "Curve", "SSE.Controllers.Main.txtShape_star10": "10-Point Star", "SSE.Controllers.Main.txtShape_star12": "12-Point Star", "SSE.Controllers.Main.txtShape_star16": "16-Point Star", "SSE.Controllers.Main.txtShape_star24": "24-Point Star", "SSE.Controllers.Main.txtShape_star32": "32-Point Star", "SSE.Controllers.Main.txtShape_star4": "Sao 4 cánh", "SSE.Controllers.Main.txtShape_star5": "Sao 5 cánh", "SSE.Controllers.Main.txtShape_star6": "6-<PERSON> Star", "SSE.Controllers.Main.txtShape_star7": "7-Point Star", "SSE.Controllers.Main.txtShape_star8": "8-Point Star", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Striped Right Arrow", "SSE.Controllers.Main.txtShape_sun": "Sun", "SSE.Controllers.Main.txtShape_teardrop": "Teardrop", "SSE.Controllers.Main.txtShape_textRect": "Text Box", "SSE.Controllers.Main.txtShape_trapezoid": "Trapezoid", "SSE.Controllers.Main.txtShape_triangle": "Triangle", "SSE.Controllers.Main.txtShape_upArrow": "Up Arrow", "SSE.Controllers.Main.txtShape_upArrowCallout": "Up arrow callout", "SSE.Controllers.Main.txtShape_upDownArrow": "Up down arrow", "SSE.Controllers.Main.txtShape_uturnArrow": "U-Turn Arrow", "SSE.Controllers.Main.txtShape_verticalScroll": "Vertical scroll", "SSE.Controllers.Main.txtShape_wave": "Wave", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Oval callout", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Rectangular Callout", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Rounded Rectangular Callout", "SSE.Controllers.Main.txtSheet": "Sheet", "SSE.Controllers.Main.txtSlicer": "<PERSON>licer", "SSE.Controllers.Main.txtStarsRibbons": "Sao & Ruy-băng", "SSE.Controllers.Main.txtStyle_Bad": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Calculation": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Check_Cell": "Ô đ<PERSON>h dấu kiểm", "SSE.Controllers.Main.txtStyle_Comma": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "<PERSON><PERSON><PERSON> bản g<PERSON><PERSON> th<PERSON>ch", "SSE.Controllers.Main.txtStyle_Good": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Heading_1": "Tiêu đề 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Tiêu đề 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Tiêu đề 3", "SSE.Controllers.Main.txtStyle_Heading_4": "T<PERSON>êu <PERSON> 4", "SSE.Controllers.Main.txtStyle_Input": "<PERSON><PERSON><PERSON> vào", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Ô đư<PERSON><PERSON> liên kết", "SSE.Controllers.Main.txtStyle_Neutral": "Trung tính", "SSE.Controllers.Main.txtStyle_Normal": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Note": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Output": "<PERSON><PERSON><PERSON> ra", "SSE.Controllers.Main.txtStyle_Percent": "<PERSON><PERSON><PERSON> tr<PERSON>m", "SSE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "SSE.Controllers.Main.txtStyle_Total": "<PERSON><PERSON><PERSON> cộng", "SSE.Controllers.Main.txtStyle_Warning_Text": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> b<PERSON>o", "SSE.Controllers.Main.txtTab": "Tab", "SSE.Controllers.Main.txtTable": "Table", "SSE.Controllers.Main.txtTime": "Time", "SSE.Controllers.Main.txtUnlock": "Unlock", "SSE.Controllers.Main.txtUnlockRange": "Unlock range", "SSE.Controllers.Main.txtUnlockRangeDescription": "Enter the password to change this range:", "SSE.Controllers.Main.txtUnlockRangeWarning": "A range you are trying to change is password protected.", "SSE.Controllers.Main.txtValues": "Values", "SSE.Controllers.Main.txtView": "View", "SSE.Controllers.Main.txtXAxis": "Trục X", "SSE.Controllers.Main.txtYAxis": "<PERSON><PERSON><PERSON><PERSON> Y", "SSE.Controllers.Main.txtYears": "Years", "SSE.Controllers.Main.unknownErrorText": "Lỗi không xác định.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Tr<PERSON><PERSON> du<PERSON>t của bạn không được hỗ trợ.", "SSE.Controllers.Main.uploadDocExtMessage": "Unknown document format.", "SSE.Controllers.Main.uploadDocFileCountMessage": "No documents uploaded.", "SSE.Controllers.Main.uploadDocSizeMessage": "Maximum document size limit exceeded.", "SSE.Controllers.Main.uploadImageExtMessage": "Định dạng hình <PERSON>nh không xác đ<PERSON>nh.", "SSE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON><PERSON><PERSON> có hình <PERSON>nh đư<PERSON> tải lên.", "SSE.Controllers.Main.uploadImageSizeMessage": "<PERSON><PERSON> vượt quá giới hạn kích thước tối đa của hình <PERSON>nh.", "SSE.Controllers.Main.uploadImageTextText": "<PERSON><PERSON> tải lên h<PERSON>nh <PERSON>...", "SSE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON> tải lên h<PERSON>nh <PERSON>", "SSE.Controllers.Main.waitText": "Please, wait...", "SSE.Controllers.Main.warnBrowserIE9": "Ứng dụng vận hành kém trên IE9. Sử dụng IE10 hoặc cao hơn", "SSE.Controllers.Main.warnBrowserZoom": "Hiện cài đặt thu phóng trình duyệt của bạn không được hỗ trợ đầy đủ. <PERSON>ui lòng thiết lập lại chế độ thu phóng mặc định bằng cách nhấn Ctrl+0.", "SSE.Controllers.Main.warnLicenseAnonymous": "Access denied for anonymous users.<br>This document will be opened for viewing only.", "SSE.Controllers.Main.warnLicenseBefore": "License not active.<br>Please contact your administrator.", "SSE.Controllers.Main.warnLicenseExceeded": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only.<br>Contact your administrator to learn more.", "SSE.Controllers.Main.warnLicenseExp": "G<PERSON><PERSON><PERSON> phép của bạn đã hết hạn.<br><PERSON><PERSON> lòng cập nhật gi<PERSON>y phép và làm mới trang.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "License expired.<br>You have no access to document editing functionality.<br>Please contact your administrator.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "License needs to be renewed.<br>You have a limited access to document editing functionality.<br>Please contact your administrator to get full access", "SSE.Controllers.Main.warnLicenseUsersExceeded": "You've reached the user limit for %1 editors. Contact your administrator to learn more.", "SSE.Controllers.Main.warnNoLicense": "Bạn đang sử dụng phiên bản nguồn mở của %1. <PERSON><PERSON>n bản có giới hạn các kết nối đồng thời với server tà<PERSON> liệu (20 kết nối cùng một lúc).<br><PERSON><PERSON><PERSON> bạn cần thêm, h<PERSON><PERSON> cân nhắc mua giấy phép thương mại.", "SSE.Controllers.Main.warnNoLicenseUsers": "You've reached the user limit for %1 editors. Contact %1 sales team for personal upgrade terms.", "SSE.Controllers.Main.warnProcessRightsChange": "Bạn đã bị từ chối quyền chỉnh sửa file này.", "SSE.Controllers.PivotTable.strSheet": "Sheet", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "No actions with calculated items are allowed for this active cell.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Calculated items do not work with custom subtotals.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "An item name cannot be found. Check that you've typed name correctly and the item is present in the PivotTable report.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Averages, standard deviations, and variances are not supported when a PivotTable report has calculated items.", "SSE.Controllers.Print.strAllSheets": "<PERSON><PERSON><PERSON> cả các trang t<PERSON>h", "SSE.Controllers.Print.textFirstCol": "First column", "SSE.Controllers.Print.textFirstRow": "First row", "SSE.Controllers.Print.textFrozenCols": "Frozen columns", "SSE.Controllers.Print.textFrozenRows": "Frozen rows", "SSE.Controllers.Print.textInvalidRange": "ERROR! Invalid cells range", "SSE.Controllers.Print.textNoRepeat": "Don't repeat", "SSE.Controllers.Print.textRepeat": "Repeat...", "SSE.Controllers.Print.textSelectRange": "Select range", "SSE.Controllers.Print.txtCustom": "Custom", "SSE.Controllers.Print.txtZoomToPage": "Zoom to page", "SSE.Controllers.Search.textInvalidRange": "ERROR! Invalid cells range", "SSE.Controllers.Search.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "SSE.Controllers.Search.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "SSE.Controllers.Search.textReplaceSuccess": "Search has been done. {0} occurrences have been replaced", "SSE.Controllers.Statusbar.errorLastSheet": "Workbook phải có ít nhất một bảng tính hiển thị.", "SSE.Controllers.Statusbar.errorRemoveSheet": "<PERSON><PERSON><PERSON><PERSON> thể xóa bảng t<PERSON>.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON> t<PERSON>h", "SSE.Controllers.Statusbar.textDisconnect": "<b>Connection is lost</b><br>Trying to connect. Please check connection settings.", "SSE.Controllers.Statusbar.textSheetViewTip": "You are in Sheet View mode. Filters and sorting are visible only to you and those who are still in this view.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "You are in Sheet View mode. Filters are visible only to you and those who are still in this view.", "SSE.Controllers.Statusbar.warnDeleteSheet": "<PERSON><PERSON><PERSON> t<PERSON>h có thể chứa dữ liệu. Bạn có chắc là muốn tiếp tục?", "SSE.Controllers.Statusbar.zoomText": "<PERSON>hu phóng {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Phông chữ bạn sẽ lưu không có sẵn trên thiết bị hiện tại.<br>Ki<PERSON><PERSON> văn bản sẽ được hiển thị bằng một trong các phông chữ hệ thống, phông chữ đã lưu sẽ được sử dụng khi có sẵn.<br>Bạn có muốn tiếp tục?", "SSE.Controllers.Toolbar.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Controllers.Toolbar.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "LỖI! Số chuỗi dữ liệu tối đa cho mỗi biểu đồ là 255", "SSE.Controllers.Toolbar.errorStockChart": "Thứ tự hàng không chính xác. <PERSON><PERSON> xây dựng một biểu đồ chứng khoán đặt dữ liệu trên giấy theo thứ tự sau:<br>gi<PERSON> mở phiên, gi<PERSON> cao nh<PERSON>t, g<PERSON><PERSON> thấ<PERSON> nhất, gi<PERSON> đóng phiên.", "SSE.Controllers.Toolbar.helpCalcItems": "Work with calculated items in Pivot Tables.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Calculated items", "SSE.Controllers.Toolbar.helpFastUndo": "Easily undo changes while collaborating on sheets in Fast mode.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "\"Undo\" in real-time co-editing", "SSE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "SSE.Controllers.Toolbar.textAccent": "<PERSON><PERSON><PERSON> phụ", "SSE.Controllers.Toolbar.textBracket": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.textDirectional": "Directional", "SSE.Controllers.Toolbar.textFontSizeErr": "Gi<PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON> lòng nhập một giá trị số giữa 1 và 409", "SSE.Controllers.Toolbar.textFraction": "<PERSON><PERSON> số", "SSE.Controllers.Toolbar.textFunction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textIndicator": "Indicators", "SSE.Controllers.Toolbar.textInsert": "Insert", "SSE.Controllers.Toolbar.textIntegral": "<PERSON><PERSON><PERSON> phân", "SSE.Controllers.Toolbar.textLargeOperator": "<PERSON><PERSON> tử lớn", "SSE.Controllers.Toolbar.textLimitAndLog": "Giới hạn và Lô-ga", "SSE.Controllers.Toolbar.textLongOperation": "<PERSON><PERSON><PERSON> t<PERSON> dài", "SSE.Controllers.Toolbar.textMatrix": "<PERSON> trận", "SSE.Controllers.Toolbar.textOperator": "<PERSON><PERSON> tử", "SSE.Controllers.Toolbar.textPivot": "Pivot Table", "SSE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRating": "Ratings", "SSE.Controllers.Toolbar.textRecentlyUsed": "Recently used", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textShapes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textWarning": "<PERSON><PERSON><PERSON> b<PERSON>o", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "<PERSON><PERSON><PERSON> tên hai chiều ở trên", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "<PERSON><PERSON><PERSON> tên trên hướng về trái", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON><PERSON><PERSON> tên phải ở trên", "SSE.Controllers.Toolbar.txtAccent_Bar": "Gạch", "SSE.Controllers.Toolbar.txtAccent_BarBot": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarTop": "<PERSON><PERSON><PERSON> trên", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON> thứ<PERSON> đ<PERSON> (Có Placeholder)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "<PERSON><PERSON><PERSON> thức đ<PERSON>g khung (Ví dụ)", "SSE.Controllers.Toolbar.txtAccent_Check": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Ngoặc ôm ở dưới", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Ngoặc ôm ở trên", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vector A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC với Gạch trên", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y Vớ<PERSON> trên", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Ba chấm", "SSE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON><PERSON> đ<PERSON>i", "SSE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "<PERSON><PERSON><PERSON> trên k<PERSON>", "SSE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "<PERSON><PERSON> nhóm ký tự ở dưới", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "<PERSON><PERSON> nhóm ký tự ở trên", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "<PERSON><PERSON><PERSON> c<PERSON>u trên hư<PERSON> về trái", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "<PERSON><PERSON><PERSON> móc phải ở trên", "SSE.Controllers.Toolbar.txtAccent_Hat": "Mũ", "SSE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON><PERSON> ng<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Dấu ngoặc với Dấu phân cách", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Dấu ngoặc với Dấu phân cách", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Dấu ngoặc với Dấu phân cách", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON> (hai đi<PERSON><PERSON> ki<PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Ba điều kiện)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "<PERSON><PERSON><PERSON> t<PERSON> xếp chồng", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "<PERSON><PERSON><PERSON> t<PERSON> xếp chồng", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "<PERSON><PERSON> dụ cho tr<PERSON><PERSON><PERSON> hợp", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "<PERSON><PERSON> số nhị thức", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "<PERSON><PERSON> số nhị thức", "SSE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Dấu ngoặc với Dấu phân cách", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON>u ngoặc", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Ngoặc đơn", "SSE.Controllers.Toolbar.txtDeleteCells": "Delete cells", "SSE.Controllers.Toolbar.txtExpand": "Mở rộng và sắp xếp", "SSE.Controllers.Toolbar.txtExpandSort": "Dữ liệu bên cạnh vùng đã chọn sẽ không được sắp xếp. Bạn muốn mở rộng vùng chọn để bao gồm dữ liệu liền kề hay tiếp tục sắp xếp các ô đã chọn hiện tại?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "<PERSON><PERSON> số vi<PERSON><PERSON> l<PERSON>ch", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Vi phân", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Vi phân", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Vi phân", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Vi phân", "SSE.Controllers.Toolbar.txtFractionHorizontal": "<PERSON><PERSON> số viết ngang", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi hơn 2", "SSE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON> số nhỏ", "SSE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON> số xếp chồng", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "<PERSON><PERSON><PERSON> cos ngh<PERSON><PERSON> đ<PERSON>o", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "<PERSON><PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "<PERSON><PERSON><PERSON> cotg ngh<PERSON><PERSON> đ<PERSON>o", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "<PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "<PERSON><PERSON><PERSON> cosec ngh<PERSON><PERSON> đ<PERSON>o", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "<PERSON><PERSON><PERSON> cosec <PERSON>-péc-b<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "<PERSON><PERSON><PERSON> sec ngh<PERSON><PERSON> đ<PERSON>o", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "<PERSON><PERSON><PERSON> sec <PERSON>y-p<PERSON><PERSON>-b<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "<PERSON><PERSON><PERSON> sin ngh<PERSON>ch đ<PERSON>o", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "<PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "<PERSON><PERSON><PERSON> tan ngh<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "<PERSON><PERSON><PERSON><PERSON>-b<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON>m cosin", "SSE.Controllers.Toolbar.txtFunction_Cosh": "<PERSON><PERSON><PERSON> cos <PERSON>-bôn", "SSE.Controllers.Toolbar.txtFunction_Cot": "<PERSON><PERSON><PERSON> co<PERSON>g", "SSE.Controllers.Toolbar.txtFunction_Coth": "<PERSON><PERSON><PERSON>b<PERSON>n", "SSE.Controllers.Toolbar.txtFunction_Csc": "<PERSON><PERSON><PERSON> cos", "SSE.Controllers.Toolbar.txtFunction_Csch": "<PERSON><PERSON><PERSON> cosec <PERSON>bôn", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sin theta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON><PERSON> thức tan", "SSE.Controllers.Toolbar.txtFunction_Sec": "Hàm sec", "SSE.Controllers.Toolbar.txtFunction_Sech": "<PERSON><PERSON><PERSON>n", "SSE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON><PERSON> sin", "SSE.Controllers.Toolbar.txtFunction_Sinh": "<PERSON><PERSON><PERSON>n", "SSE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON><PERSON> tan", "SSE.Controllers.Toolbar.txtFunction_Tanh": "<PERSON><PERSON><PERSON>n", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Custom", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Data and model", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Good, bad and neutral", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "No name", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Number format", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Themed cell styles", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Titles and headings", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Custom", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Dark", "SSE.Controllers.Toolbar.txtGroupTable_Light": "Light", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Medium", "SSE.Controllers.Toolbar.txtInsertCells": "Insert cells", "SSE.Controllers.Toolbar.txtIntegral": "<PERSON><PERSON><PERSON> phân", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Vi phân của theta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Vi phân của x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Vi phân của y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "<PERSON><PERSON><PERSON> phân", "SSE.Controllers.Toolbar.txtIntegralDouble": "<PERSON><PERSON><PERSON> phân kép", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "<PERSON><PERSON><PERSON> phân kép", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "<PERSON><PERSON><PERSON> phân kép", "SSE.Controllers.Toolbar.txtIntegralOriented": "<PERSON><PERSON><PERSON> phân theo chu tuyến", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "<PERSON><PERSON><PERSON> phân theo chu tuyến", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "<PERSON><PERSON><PERSON> phân bề mặt", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "<PERSON><PERSON><PERSON> phân bề mặt", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "<PERSON><PERSON><PERSON> phân bề mặt", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "<PERSON><PERSON><PERSON> phân theo chu tuyến", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "<PERSON><PERSON><PERSON> phân kh<PERSON>i", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "<PERSON><PERSON><PERSON> phân kh<PERSON>i", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "<PERSON><PERSON><PERSON> phân kh<PERSON>i", "SSE.Controllers.Toolbar.txtIntegralSubSup": "<PERSON><PERSON><PERSON> phân", "SSE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON><PERSON> phân ba lớp", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "<PERSON><PERSON><PERSON> phân ba lớp", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "<PERSON><PERSON><PERSON> phân ba lớp", "SSE.Controllers.Toolbar.txtInvalidRange": "LỖI! <PERSON>ạm vi ô không hợp lệ", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "<PERSON><PERSON><PERSON> sản ph<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "<PERSON><PERSON><PERSON> p<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "V", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "<PERSON><PERSON><PERSON> <PERSON>hau", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "<PERSON><PERSON><PERSON> <PERSON>hau", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "<PERSON><PERSON><PERSON> <PERSON>hau", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "<PERSON><PERSON><PERSON> <PERSON>hau", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "<PERSON><PERSON><PERSON> <PERSON>hau", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "<PERSON><PERSON><PERSON> p<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "<PERSON><PERSON><PERSON> p<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "<PERSON><PERSON><PERSON> p<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "<PERSON><PERSON><PERSON> p<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "<PERSON><PERSON><PERSON> p<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "<PERSON>í dụ giới hạn", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON> dụ Lớn nhất", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON><PERSON><PERSON><PERSON> hạn", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "<PERSON><PERSON>-ga-r<PERSON>t tự nhiên", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Lô-ga-r<PERSON>t", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Lô-ga-r<PERSON>t", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Lớn n<PERSON>t", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Nhỏ nhất", "SSE.Controllers.Toolbar.txtLockSort": "Data is found next to your selection, but you do not have sufficient permissions to change those cells.<br>Do you wish to continue with the current selection?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "<PERSON> trận rỗng 1x2", "SSE.Controllers.Toolbar.txtMatrix_1_3": "<PERSON> trận rỗng 1x3", "SSE.Controllers.Toolbar.txtMatrix_2_1": "<PERSON> trận rỗng 2x1", "SSE.Controllers.Toolbar.txtMatrix_2_2": "<PERSON> trận rỗng 2x2", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Ma trận rỗng với dấu ngoặc", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Ma trận rỗng với dấu ngoặc", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Ma trận rỗng với dấu ngoặc", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Ma trận rỗng với dấu ngoặc", "SSE.Controllers.Toolbar.txtMatrix_2_3": "<PERSON> trận rỗng 2x3", "SSE.Controllers.Toolbar.txtMatrix_3_1": "<PERSON> trận rỗng 3x1", "SSE.Controllers.Toolbar.txtMatrix_3_2": "<PERSON> trận rỗng 3x2", "SSE.Controllers.Toolbar.txtMatrix_3_3": "<PERSON> trận rỗng 3x3", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "<PERSON><PERSON><PERSON> chấm câu", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "<PERSON><PERSON>m gi<PERSON>a dòng", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON> chấm chéo", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON> trận thưa", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON> trận thưa", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "<PERSON> trận Đơn vị 2x2", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "<PERSON> trận Đơn vị 3x3", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "<PERSON> trận Đơn vị 3x3", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "<PERSON> trận Đơn vị 3x3", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "<PERSON><PERSON><PERSON> tên hai chiều ở dưới", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "<PERSON><PERSON><PERSON> tên hai chiều ở trên", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "<PERSON><PERSON><PERSON> tên dư<PERSON>i hướng về trái", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "<PERSON><PERSON><PERSON> tên trên hướng về trái", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "<PERSON><PERSON><PERSON> tên phải ở dưới", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON><PERSON><PERSON> tên phải ở trên", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "<PERSON> chấm Bằng", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Yields", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Lợi suất Delta", "SSE.Controllers.Toolbar.txtOperator_Definition": "Bằng với theo <PERSON> ngh<PERSON>a", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta bằng với", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "<PERSON><PERSON><PERSON> tên hai chiều ở dưới", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "<PERSON><PERSON><PERSON> tên hai chiều ở trên", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "<PERSON><PERSON><PERSON> tên dư<PERSON>i hướng về trái", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "<PERSON><PERSON><PERSON> tên trên hướng về trái", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "<PERSON><PERSON><PERSON> tên phải ở dưới", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON><PERSON><PERSON> tên phải ở trên", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Bằng bằng", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Trừ Bằng", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Cộng Bằng", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON><PERSON> đo bằng", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON> bậc hai có bậc", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON> b<PERSON>c ba", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hai", "SSE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "Chỉ số dưới", "SSE.Controllers.Toolbar.txtScriptSubSup": "Chỉ số dưới-Chỉ số trên", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Chỉ số dưới-Chỉ số trên bên trái", "SSE.Controllers.Toolbar.txtScriptSup": "Chỉ số trên", "SSE.Controllers.Toolbar.txtSorting": "<PERSON><PERSON><PERSON>p", "SSE.Controllers.Toolbar.txtSortSelected": "<PERSON><PERSON><PERSON> x<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_about": "Xấp xỉ", "SSE.Controllers.Toolbar.txtSymbol_additional": "<PERSON><PERSON> sung", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "SSE.Controllers.Toolbar.txtSymbol_approx": "Gần bằng với", "SSE.Controllers.Toolbar.txtSymbol_ast": "<PERSON><PERSON> t<PERSON> *", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "<PERSON><PERSON> tử Dấu đầu dòng", "SSE.Controllers.Toolbar.txtSymbol_cap": "<PERSON><PERSON><PERSON> <PERSON>hau", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON> b<PERSON>c ba", "SSE.Controllers.Toolbar.txtSymbol_cdots": "<PERSON><PERSON>m lửng nằm ngang giữa dòng", "SSE.Controllers.Toolbar.txtSymbol_celsius": "Độ C", "SSE.Controllers.Toolbar.txtSymbol_chi": "X", "SSE.Controllers.Toolbar.txtSymbol_cong": "Xấp xỉ bằng với", "SSE.Controllers.Toolbar.txtSymbol_cup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ddots": "<PERSON><PERSON><PERSON> lửng chéo xuống bên ph<PERSON>i", "SSE.Controllers.Toolbar.txtSymbol_degree": "Độ", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON><PERSON> chia", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON><PERSON> tên xu<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON><PERSON> hợp rỗng", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "Bằng", "SSE.Controllers.Toolbar.txtSymbol_equiv": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "<PERSON><PERSON> tồn tại", "SSE.Controllers.Toolbar.txtSymbol_factorial": "<PERSON><PERSON><PERSON> th<PERSON>a", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Độ F", "SSE.Controllers.Toolbar.txtSymbol_forall": "<PERSON> tất cả", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "Lớn hơn hoặc bằng", "SSE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON><PERSON> h<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_in": "<PERSON>ần tử của", "SSE.Controllers.Toolbar.txtSymbol_inc": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_infinity": "<PERSON><PERSON> h<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "<PERSON><PERSON><PERSON> tên trái", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "<PERSON><PERSON><PERSON> tên <PERSON>-<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_leq": "Nhỏ hơn hoặc Bằng", "SSE.Controllers.Toolbar.txtSymbol_less": "Nhỏ hơn", "SSE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_minus": "Trừ", "SSE.Controllers.Toolbar.txtSymbol_mp": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Không bằng", "SSE.Controllers.Toolbar.txtSymbol_ni": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_not": "<PERSON><PERSON><PERSON><PERSON> ký hiệu", "SSE.Controllers.Toolbar.txtSymbol_notexists": "<PERSON><PERSON><PERSON><PERSON> tồn tại", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "chữ 'o' ngắn", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Sai phân riêng", "SSE.Controllers.Toolbar.txtSymbol_percent": "<PERSON><PERSON><PERSON> tr<PERSON>m", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "<PERSON><PERSON>ng", "SSE.Controllers.Toolbar.txtSymbol_pm": "Cộng Trừ", "SSE.Controllers.Toolbar.txtSymbol_propto": "Tỷ lệ", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON> thứ tư", "SSE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> chứ<PERSON>h", "SSE.Controllers.Toolbar.txtSymbol_rddots": "<PERSON><PERSON><PERSON> lửng chéo lên ph<PERSON>i", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON><PERSON><PERSON> tên bên ph<PERSON>i", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON> v<PERSON>y", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON><PERSON> tên chỉ lên", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "<PERSON><PERSON><PERSON><PERSON> thể Epsilon", "SSE.Controllers.Toolbar.txtSymbol_varphi": "<PERSON><PERSON><PERSON><PERSON> thể <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON><PERSON><PERSON> thể <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON><PERSON><PERSON> thể của <PERSON>ho", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "<PERSON><PERSON><PERSON><PERSON> thể <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON><PERSON> thể theta", "SSE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON><PERSON> l<PERSON> d<PERSON>c", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Table style Dark", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Table style Light", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Table style Medium", "SSE.Controllers.Toolbar.warnLongOperation": "<PERSON><PERSON> tác mà bạn sắp thực hiện có thể mất khá nhiều thời gian để hoàn thành.<br>Bạn có chắc là muốn tiếp tục?", "SSE.Controllers.Toolbar.warnMergeLostData": "Chỉ dữ liệu từ ô phía trên bên trái sẽ vẫn nằm trong ô được gộp.<br>Bạn có chắc là muốn tiếp tục?", "SSE.Controllers.Toolbar.warnNoRecommended": "To create a chart, select the cells that contain the data you'd like to use.<br>If you have names for the rows and columns and you'd like use them as labels, include them in your selection.", "SSE.Controllers.Viewport.textFreezePanes": "Freeze panes", "SSE.Controllers.Viewport.textFreezePanesShadow": "Show Frozen Panes Shadow", "SSE.Controllers.Viewport.textHideFBar": "Hide Formula Bar", "SSE.Controllers.Viewport.textHideGridlines": "Hide Gridlines", "SSE.Controllers.Viewport.textHideHeadings": "<PERSON><PERSON> Headings", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Decimal separator", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Thousands separator", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Settings used to recognize numeric data", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Text qualifier", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Advanced settings", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(none)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "<PERSON><PERSON> lọc tùy chỉnh", "SSE.Views.AutoFilterDialog.textAddSelection": "<PERSON><PERSON><PERSON><PERSON> lựa chọn hiện tại để lọc", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Blanks}", "SSE.Views.AutoFilterDialog.textSelectAll": "<PERSON><PERSON><PERSON> tất cả", "SSE.Views.AutoFilterDialog.textSelectAllResults": "<PERSON><PERSON><PERSON> tất cả kết quả tìm kiếm", "SSE.Views.AutoFilterDialog.textWarning": "<PERSON><PERSON><PERSON> b<PERSON>o", "SSE.Views.AutoFilterDialog.txtAboveAve": "<PERSON>r<PERSON><PERSON> trung bình", "SSE.Views.AutoFilterDialog.txtAfter": "After...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "All dates in the period", "SSE.Views.AutoFilterDialog.txtApril": "April", "SSE.Views.AutoFilterDialog.txtAugust": "August", "SSE.Views.AutoFilterDialog.txtBefore": "Before...", "SSE.Views.AutoFilterDialog.txtBegins": "<PERSON><PERSON>t đầu với...", "SSE.Views.AutoFilterDialog.txtBelowAve": "<PERSON><PERSON><PERSON><PERSON> trung bình", "SSE.Views.AutoFilterDialog.txtBetween": "Giữa...", "SSE.Views.AutoFilterDialog.txtClear": "Xóa", "SSE.Views.AutoFilterDialog.txtContains": "Chứa...", "SSE.Views.AutoFilterDialog.txtDateFilter": "Date filter", "SSE.Views.AutoFilterDialog.txtDecember": "December", "SSE.Views.AutoFilterDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON> bộ lọc ô", "SSE.Views.AutoFilterDialog.txtEnds": "<PERSON><PERSON><PERSON> thúc bằng...", "SSE.Views.AutoFilterDialog.txtEquals": "Bằng...", "SSE.Views.AutoFilterDialog.txtFebruary": "February", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "<PERSON><PERSON><PERSON> theo màu của ô", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "<PERSON><PERSON><PERSON> theo màu chữ", "SSE.Views.AutoFilterDialog.txtGreater": "L<PERSON><PERSON> hơn...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Lớn hơn hoặc bằng...", "SSE.Views.AutoFilterDialog.txtJanuary": "January", "SSE.Views.AutoFilterDialog.txtJuly": "July", "SSE.Views.AutoFilterDialog.txtJune": "June", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Label filter", "SSE.Views.AutoFilterDialog.txtLastMonth": "Last month", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Last quarter", "SSE.Views.AutoFilterDialog.txtLastWeek": "Last week", "SSE.Views.AutoFilterDialog.txtLastYear": "Last year", "SSE.Views.AutoFilterDialog.txtLess": "Nhỏ hơn...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Nhỏ hơn hoặc bằng...", "SSE.Views.AutoFilterDialog.txtMarch": "March", "SSE.Views.AutoFilterDialog.txtMay": "May", "SSE.Views.AutoFilterDialog.txtNextMonth": "Next month", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Next quarter", "SSE.Views.AutoFilterDialog.txtNextWeek": "Next week", "SSE.Views.AutoFilterDialog.txtNextYear": "Next year", "SSE.Views.AutoFilterDialog.txtNotBegins": "<PERSON><PERSON><PERSON><PERSON> bắt đầu bằng...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Not between...", "SSE.Views.AutoFilterDialog.txtNotContains": "<PERSON><PERSON><PERSON><PERSON> chứa...", "SSE.Views.AutoFilterDialog.txtNotEnds": "kh<PERSON>ng kết thúc bằng...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Không bằng...", "SSE.Views.AutoFilterDialog.txtNovember": "November", "SSE.Views.AutoFilterDialog.txtNumFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtOctober": "October", "SSE.Views.AutoFilterDialog.txtQuarter1": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Quarter 1", "SSE.Views.AutoFilterDialog.txtReapply": "<PERSON><PERSON> d<PERSON> lại", "SSE.Views.AutoFilterDialog.txtSeptember": "September", "SSE.Views.AutoFilterDialog.txtSortCellColor": "<PERSON><PERSON><PERSON> xếp theo màu ô", "SSE.Views.AutoFilterDialog.txtSortFontColor": "<PERSON><PERSON><PERSON> xếp theo màu chữ", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "<PERSON><PERSON><PERSON> xếp cao nhất đến thấp nhất", "SSE.Views.AutoFilterDialog.txtSortLow2High": "<PERSON><PERSON><PERSON> x<PERSON><PERSON><PERSON> nhất lê<PERSON> nhất", "SSE.Views.AutoFilterDialog.txtSortOption": "More sort options...", "SSE.Views.AutoFilterDialog.txtTextFilter": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "SSE.Views.AutoFilterDialog.txtThisMonth": "This Month", "SSE.Views.AutoFilterDialog.txtThisQuarter": "This quarter", "SSE.Views.AutoFilterDialog.txtThisWeek": "This week", "SSE.Views.AutoFilterDialog.txtThisYear": "This Year", "SSE.Views.AutoFilterDialog.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtToday": "Today", "SSE.Views.AutoFilterDialog.txtTomorrow": "Tomorrow", "SSE.Views.AutoFilterDialog.txtTop10": "Tốp 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Value filter", "SSE.Views.AutoFilterDialog.txtYearToDate": "Year to date", "SSE.Views.AutoFilterDialog.txtYesterday": "Yesterday", "SSE.Views.AutoFilterDialog.warnFilterError": "You need at least one field in the Values area in order to apply a value filter.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Bạn phải chọn ít nhất một giá trị", "SSE.Views.CellEditor.textManager": "<PERSON><PERSON><PERSON><PERSON> lý tên", "SSE.Views.CellEditor.tipFormula": "<PERSON><PERSON><PERSON> h<PERSON> s<PERSON>", "SSE.Views.CellRangeDialog.errorMaxRows": "LỖI! Số chuỗi dữ liệu tối đa cho mỗi biểu đồ là 255", "SSE.Views.CellRangeDialog.errorStockChart": "Thứ tự hàng không chính xác. <PERSON><PERSON> xây dựng một biểu đồ chứng khoán đặt dữ liệu trên giấy theo thứ tự sau:<br>gi<PERSON> mở phiên, gi<PERSON> cao nh<PERSON>t, g<PERSON><PERSON> thấ<PERSON> nhất, gi<PERSON> đóng phiên.", "SSE.Views.CellRangeDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "SSE.Views.CellRangeDialog.txtInvalidRange": "LỖI! <PERSON>ạm vi ô không hợp lệ", "SSE.Views.CellRangeDialog.txtTitle": "<PERSON><PERSON><PERSON> phạm vi dữ liệu", "SSE.Views.CellSettings.strShrink": "Shrink to fit", "SSE.Views.CellSettings.strWrap": "Wrap text", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "Background color", "SSE.Views.CellSettings.textBackground": "Background color", "SSE.Views.CellSettings.textBorderColor": "Color", "SSE.Views.CellSettings.textBorders": "Borders style", "SSE.Views.CellSettings.textClearRule": "Clear Rules", "SSE.Views.CellSettings.textColor": "Color fill", "SSE.Views.CellSettings.textColorScales": "Color scales", "SSE.Views.CellSettings.textCondFormat": "Conditional formatting", "SSE.Views.CellSettings.textControl": "Text control", "SSE.Views.CellSettings.textDataBars": "Data bars", "SSE.Views.CellSettings.textDirection": "Direction", "SSE.Views.CellSettings.textFill": "Fill", "SSE.Views.CellSettings.textForeground": "Foreground color", "SSE.Views.CellSettings.textGradient": "Gradient points", "SSE.Views.CellSettings.textGradientColor": "Color", "SSE.Views.CellSettings.textGradientFill": "Gradient fill", "SSE.Views.CellSettings.textIndent": "Indent", "SSE.Views.CellSettings.textItems": "Items", "SSE.Views.CellSettings.textLinear": "Linear", "SSE.Views.CellSettings.textManageRule": "Manage rules", "SSE.Views.CellSettings.textNewRule": "New rule", "SSE.Views.CellSettings.textNoFill": "No fill", "SSE.Views.CellSettings.textOrientation": "Text orientation", "SSE.Views.CellSettings.textPattern": "Pattern", "SSE.Views.CellSettings.textPatternFill": "Pattern", "SSE.Views.CellSettings.textPosition": "Position", "SSE.Views.CellSettings.textRadial": "Radial", "SSE.Views.CellSettings.textSelectBorders": "Select borders you want to change applying style chosen above", "SSE.Views.CellSettings.textSelection": "From current selection", "SSE.Views.CellSettings.textThisPivot": "From this pivot", "SSE.Views.CellSettings.textThisSheet": "From this worksheet", "SSE.Views.CellSettings.textThisTable": "From this table", "SSE.Views.CellSettings.tipAddGradientPoint": "Add gradient point", "SSE.Views.CellSettings.tipAll": "Set outer border and all inner lines", "SSE.Views.CellSettings.tipBottom": "Set outer bottom border only", "SSE.Views.CellSettings.tipDiagD": "Set diagonal down border", "SSE.Views.CellSettings.tipDiagU": "Set diagonal up border", "SSE.Views.CellSettings.tipInner": "Set inner lines only", "SSE.Views.CellSettings.tipInnerHor": "Set horizontal inner lines only", "SSE.Views.CellSettings.tipInnerVert": "Set vertical inner lines only", "SSE.Views.CellSettings.tipLeft": "Set outer left border only", "SSE.Views.CellSettings.tipNone": "Set no borders", "SSE.Views.CellSettings.tipOuter": "Set outer border only", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Remove gradient point", "SSE.Views.CellSettings.tipRight": "Set outer right border only", "SSE.Views.CellSettings.tipTop": "Set outer top border only", "SSE.Views.ChartDataDialog.errorInFormula": "There's an error in formula you entered.", "SSE.Views.ChartDataDialog.errorInvalidReference": "The reference is not valid. Reference must be to an open worksheet.", "SSE.Views.ChartDataDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "The reference is not valid. References for titles, values, sizes, or data labels must be a single cell, row, or column.", "SSE.Views.ChartDataDialog.errorNoValues": "To create a chart, the series must contain at least one value.", "SSE.Views.ChartDataDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "SSE.Views.ChartDataDialog.textAdd": "Add", "SSE.Views.ChartDataDialog.textCategory": "Horizontal (category) axis labels", "SSE.Views.ChartDataDialog.textData": "Chart data range", "SSE.Views.ChartDataDialog.textDelete": "Remove", "SSE.Views.ChartDataDialog.textDown": "Down", "SSE.Views.ChartDataDialog.textEdit": "Edit", "SSE.Views.ChartDataDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ChartDataDialog.textSelectData": "Select data", "SSE.Views.ChartDataDialog.textSeries": "Legend entries (series)", "SSE.Views.ChartDataDialog.textSwitch": "Switch row/column", "SSE.Views.ChartDataDialog.textTitle": "Chart data", "SSE.Views.ChartDataDialog.textUp": "Up", "SSE.Views.ChartDataRangeDialog.errorInFormula": "There's an error in formula you entered.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "The reference is not valid. Reference must be to an open worksheet.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "The reference is not valid. References for titles, values, sizes, or data labels must be a single cell, row, or column.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "To create a chart, the series must contain at least one value.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order:<br> opening price, max price, min price, closing price.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ChartDataRangeDialog.textSelectData": "Select data", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Axis label range", "SSE.Views.ChartDataRangeDialog.txtChoose": "Choose range", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Series name", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Axis labels", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Edit series", "SSE.Views.ChartDataRangeDialog.txtValues": "Values", "SSE.Views.ChartDataRangeDialog.txtXValues": "X values", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y values", "SSE.Views.ChartSettings.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartSettings.strLineWeight": "<PERSON><PERSON> dày đường kẻ", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "Template", "SSE.Views.ChartSettings.text3dDepth": "Depth (% of base)", "SSE.Views.ChartSettings.text3dHeight": "Height (% of base)", "SSE.Views.ChartSettings.text3dRotation": "3D Rotation", "SSE.Views.ChartSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "SSE.Views.ChartSettings.textAutoscale": "Autoscale", "SSE.Views.ChartSettings.textBorderSizeErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị từ thuộc từ 0 pt đến 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "Change type", "SSE.Views.ChartSettings.textChartType": "Thay đổi Loại biểu đồ", "SSE.Views.ChartSettings.textDefault": "Default Rotation", "SSE.Views.ChartSettings.textDown": "Down", "SSE.Views.ChartSettings.textEditData": "Chỉnh sửa <PERSON> liệu và Vị trí", "SSE.Views.ChartSettings.textFirstPoint": "<PERSON><PERSON><PERSON><PERSON> đầu tiên", "SSE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON> cao", "SSE.Views.ChartSettings.textHighPoint": "<PERSON><PERSON><PERSON><PERSON> cao", "SSE.Views.ChartSettings.textKeepRatio": "Tỷ lệ không đổi", "SSE.Views.ChartSettings.textLastPoint": "<PERSON><PERSON><PERSON><PERSON> cu<PERSON>i", "SSE.Views.ChartSettings.textLeft": "Left", "SSE.Views.ChartSettings.textLowPoint": "<PERSON><PERSON><PERSON><PERSON> thấp", "SSE.Views.ChartSettings.textMarkers": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textNarrow": "Narrow field of view", "SSE.Views.ChartSettings.textNegativePoint": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textPerspective": "Perspective", "SSE.Views.ChartSettings.textRanges": "Phạm vi dữ liệu", "SSE.Views.ChartSettings.textRight": "Right", "SSE.Views.ChartSettings.textRightAngle": "Right angle axes", "SSE.Views.ChartSettings.textSelectData": "<PERSON><PERSON><PERSON> dữ liệu", "SSE.Views.ChartSettings.textShow": "<PERSON><PERSON><PERSON> thị", "SSE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSwitch": "Switch Row/Column", "SSE.Views.ChartSettings.textType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textUp": "Up", "SSE.Views.ChartSettings.textWiden": "Widen field of view", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.ChartSettings.textX": "X rotation", "SSE.Views.ChartSettings.textY": "Y rotation", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ERROR! The maximum number of points in series per chart is 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "LỖI! Số chuỗi dữ liệu tối đa cho mỗi biểu đồ là 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Thứ tự hàng không chính xác. <PERSON><PERSON> xây dựng một biểu đồ chứng khoán đặt dữ liệu trên giấy theo thứ tự sau:<br>gi<PERSON> mở phiên, gi<PERSON> cao nh<PERSON>t, g<PERSON><PERSON> thấ<PERSON> nhất, gi<PERSON> đóng phiên.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Don't move or size with cells", "SSE.Views.ChartSettingsDlg.textAlt": "<PERSON><PERSON><PERSON> bản thay thế", "SSE.Views.ChartSettingsDlg.textAltDescription": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAltTip": "<PERSON><PERSON><PERSON> tả thay thế dưới dạng văn bản thông tin đối tượng trực quan, sẽ được đọc cho những người bị suy giảm thị lực hoặc nhận thức để giúp họ hiểu rõ hơn về những thông tin có trong hình ảnh, autoshape, biểu đồ hoặc bảng.", "SSE.Views.ChartSettingsDlg.textAltTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "SSE.Views.ChartSettingsDlg.textAuto": "<PERSON><PERSON> động", "SSE.Views.ChartSettingsDlg.textAutoEach": "T<PERSON> động cho từng", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "<PERSON><PERSON><PERSON><PERSON> giao nhau", "SSE.Views.ChartSettingsDlg.textAxisOptions": "<PERSON><PERSON><PERSON> ch<PERSON> tr<PERSON>", "SSE.Views.ChartSettingsDlg.textAxisPos": "<PERSON><PERSON> trí trục", "SSE.Views.ChartSettingsDlg.textAxisSettings": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> tr<PERSON>", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Title", "SSE.Views.ChartSettingsDlg.textBase": "Base", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>c d<PERSON>u ki<PERSON>m", "SSE.Views.ChartSettingsDlg.textBillions": "Hàng tỷ", "SSE.Views.ChartSettingsDlg.textBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.ChartSettingsDlg.textCategoryName": "<PERSON><PERSON><PERSON> da<PERSON> mục", "SSE.Views.ChartSettingsDlg.textCenter": "Trung tâm", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "<PERSON> tiết <PERSON><PERSON><PERSON><PERSON> đồ &<br><PERSON><PERSON> thích", "SSE.Views.ChartSettingsDlg.textChartTitle": "Tiêu đề biểu đồ", "SSE.Views.ChartSettingsDlg.textCross": "Chéo", "SSE.Views.ChartSettingsDlg.textCustom": "Tuỳ chỉnh", "SSE.Views.ChartSettingsDlg.textDataColumns": "trong cột", "SSE.Views.ChartSettingsDlg.textDataLabels": "<PERSON><PERSON><PERSON><PERSON> dữ liệu", "SSE.Views.ChartSettingsDlg.textDataRows": "trong hàng", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "<PERSON><PERSON><PERSON> thị chú g<PERSON>i", "SSE.Views.ChartSettingsDlg.textEmptyCells": "<PERSON><PERSON><PERSON> ô ẩn và ô rỗng", "SSE.Views.ChartSettingsDlg.textEmptyLine": "<PERSON><PERSON>t nối các điểm dữ liệu với đường kẻ", "SSE.Views.ChartSettingsDlg.textFit": "Vừa với <PERSON> rộng", "SSE.Views.ChartSettingsDlg.textFixed": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textFormat": "Label format", "SSE.Views.ChartSettingsDlg.textGaps": "<PERSON><PERSON><PERSON><PERSON> trống", "SSE.Views.ChartSettingsDlg.textGridLines": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "SSE.Views.ChartSettingsDlg.textGroup": "Sparkline Nhóm", "SSE.Views.ChartSettingsDlg.textHide": "Ẩn", "SSE.Views.ChartSettingsDlg.textHideAxis": "Hide axis", "SSE.Views.ChartSettingsDlg.textHigh": "<PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxis": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Secondary horizontal axis", "SSE.Views.ChartSettingsDlg.textHorizontal": "Nằm ngang", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "<PERSON><PERSON><PERSON> tr<PERSON>m", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "Trong", "SSE.Views.ChartSettingsDlg.textInnerBottom": "<PERSON><PERSON><PERSON><PERSON> cùng bên trong", "SSE.Views.ChartSettingsDlg.textInnerTop": "Trên cùng bên trong", "SSE.Views.ChartSettingsDlg.textInvalidRange": "LỖI! <PERSON>ạm vi ô không hợp lệ", "SSE.Views.ChartSettingsDlg.textLabelDist": "<PERSON><PERSON><PERSON><PERSON> cách nhãn trên trục", "SSE.Views.ChartSettingsDlg.textLabelInterval": "<PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLabelOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLabelPos": "<PERSON><PERSON> t<PERSON>", "SSE.Views.ChartSettingsDlg.textLayout": "Bố cục", "SSE.Views.ChartSettingsDlg.textLeft": "Trái", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "<PERSON><PERSON><PERSON> chồng bên tr<PERSON>i", "SSE.Views.ChartSettingsDlg.textLegendBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Trái", "SSE.Views.ChartSettingsDlg.textLegendPos": "<PERSON><PERSON> th<PERSON>ch", "SSE.Views.ChartSettingsDlg.textLegendRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendTop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLines": "Đường kẻ", "SSE.Views.ChartSettingsDlg.textLocationRange": "<PERSON>ạm vi địa điểm", "SSE.Views.ChartSettingsDlg.textLogScale": "Logarithmic scale", "SSE.Views.ChartSettingsDlg.textLow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Chính và Phụ", "SSE.Views.ChartSettingsDlg.textMajorType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textManual": "<PERSON><PERSON><PERSON> công", "SSE.Views.ChartSettingsDlg.textMarkers": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarksInterval": "<PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON><PERSON><PERSON> trị lớn nhất", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON> tri<PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinorType": "Loại phụ", "SSE.Views.ChartSettingsDlg.textMinValue": "<PERSON><PERSON><PERSON> trị nhỏ nhất", "SSE.Views.ChartSettingsDlg.textNextToAxis": "<PERSON><PERSON><PERSON> trục", "SSE.Views.ChartSettingsDlg.textNone": "K<PERSON>ô<PERSON>", "SSE.Views.ChartSettingsDlg.textNoOverlay": "<PERSON><PERSON><PERSON><PERSON> chồng", "SSE.Views.ChartSettingsDlg.textOneCell": "Move but don't size with cells", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Bằng dấu kiểm", "SSE.Views.ChartSettingsDlg.textOut": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textOuterTop": "<PERSON><PERSON><PERSON><PERSON> cùng bên ngo<PERSON>i", "SSE.Views.ChartSettingsDlg.textOverlay": "<PERSON><PERSON><PERSON> chồ<PERSON>", "SSE.Views.ChartSettingsDlg.textReverse": "<PERSON><PERSON><PERSON> trị theo thứ tự ngược", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Tr<PERSON><PERSON> tự đả<PERSON>", "SSE.Views.ChartSettingsDlg.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRightOverlay": "<PERSON><PERSON><PERSON> chồng bên <PERSON>", "SSE.Views.ChartSettingsDlg.textRotated": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSameAll": "<PERSON><PERSON><PERSON><PERSON> tự cho Tất cả", "SSE.Views.ChartSettingsDlg.textSelectData": "<PERSON><PERSON><PERSON> dữ liệu", "SSE.Views.ChartSettingsDlg.textSeparator": "<PERSON><PERSON><PERSON> phân cách nhãn dữ liệu", "SSE.Views.ChartSettingsDlg.textSeriesName": "<PERSON><PERSON>n chuỗi", "SSE.Views.ChartSettingsDlg.textShow": "<PERSON><PERSON><PERSON> thị", "SSE.Views.ChartSettingsDlg.textShowBorders": "<PERSON>ển thị đường viền biểu đồ", "SSE.Views.ChartSettingsDlg.textShowData": "<PERSON><PERSON><PERSON> thị dữ liệu trong các hàng và cột ẩn", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "<PERSON><PERSON><PERSON> thị các ô trống dưới dạng", "SSE.Views.ChartSettingsDlg.textShowEquation": "Display equation on chart", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "<PERSON><PERSON><PERSON> thị trục", "SSE.Views.ChartSettingsDlg.textShowValues": "<PERSON><PERSON><PERSON> thị các giá trị biểu đồ", "SSE.Views.ChartSettingsDlg.textSingle": "Sparkline đơn", "SSE.Views.ChartSettingsDlg.textSmooth": "Trơn", "SSE.Views.ChartSettingsDlg.textSnap": "Cell snapping", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Phạm vi Sparkline", "SSE.Views.ChartSettingsDlg.textStraight": "Thẳng", "SSE.Views.ChartSettingsDlg.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON><PERSON><PERSON>n", "SSE.Views.ChartSettingsDlg.textTickOptions": "<PERSON><PERSON><PERSON> ch<PERSON>n d<PERSON>u k<PERSON>", "SSE.Views.ChartSettingsDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON> đồ - <PERSON>ài đặt Nâng cao", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Sparkline - Cài đặt Nâng cao", "SSE.Views.ChartSettingsDlg.textTop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Trendline options", "SSE.Views.ChartSettingsDlg.textTrillions": "Hàng tỷ", "SSE.Views.ChartSettingsDlg.textTwoCell": "Move and size with cells", "SSE.Views.ChartSettingsDlg.textType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTypeData": "Loại & Dữ liệu", "SSE.Views.ChartSettingsDlg.textUnits": "<PERSON><PERSON><PERSON> thị đơn vị", "SSE.Views.ChartSettingsDlg.textValue": "<PERSON><PERSON><PERSON> trị", "SSE.Views.ChartSettingsDlg.textVertAxis": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Secondary vertical axis", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Ti<PERSON><PERSON> đề Trục X", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "<PERSON>i<PERSON><PERSON> đ<PERSON>ụ<PERSON>", "SSE.Views.ChartSettingsDlg.textZero": "0", "SSE.Views.ChartSettingsDlg.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "SSE.Views.ChartTypeDialog.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartTypeDialog.textSecondary": "Secondary axis", "SSE.Views.ChartTypeDialog.textSeries": "Series", "SSE.Views.ChartTypeDialog.textStyle": "Style", "SSE.Views.ChartTypeDialog.textTitle": "Chart type", "SSE.Views.ChartTypeDialog.textType": "Type", "SSE.Views.ChartWizardDialog.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartWizardDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order: opening price, max price, min price, closing price.", "SSE.Views.ChartWizardDialog.textRecommended": "Recommended", "SSE.Views.ChartWizardDialog.textSecondary": "Secondary Axis", "SSE.Views.ChartWizardDialog.textSeries": "Series", "SSE.Views.ChartWizardDialog.textTitle": "Insert Chart", "SSE.Views.ChartWizardDialog.textTitleChange": "Change chart type", "SSE.Views.ChartWizardDialog.textType": "Type", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Choose the chart type and axis for your data series", "SSE.Views.CreatePivotDialog.textDataRange": "Source data range", "SSE.Views.CreatePivotDialog.textDestination": "Choose where to place the table", "SSE.Views.CreatePivotDialog.textExist": "Existing worksheet", "SSE.Views.CreatePivotDialog.textInvalidRange": "Invalid cells range", "SSE.Views.CreatePivotDialog.textNew": "New worksheet", "SSE.Views.CreatePivotDialog.textSelectData": "Select data", "SSE.Views.CreatePivotDialog.textTitle": "Create pivot table", "SSE.Views.CreatePivotDialog.txtEmpty": "This field is required", "SSE.Views.CreateSparklineDialog.textDataRange": "Source data range", "SSE.Views.CreateSparklineDialog.textDestination": "Choose, where to place the sparklines", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Invalid cells range", "SSE.Views.CreateSparklineDialog.textSelectData": "Select data", "SSE.Views.CreateSparklineDialog.textTitle": "Create sparklines", "SSE.Views.CreateSparklineDialog.txtEmpty": "This field is required", "SSE.Views.DataTab.capBtnGroup": "Group", "SSE.Views.DataTab.capBtnTextCustomSort": "Custom Sort", "SSE.Views.DataTab.capBtnTextDataValidation": "Data Validation", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Remove Duplicates", "SSE.Views.DataTab.capBtnTextToCol": "Text to Columns", "SSE.Views.DataTab.capBtnUngroup": "Ungroup", "SSE.Views.DataTab.capDataExternalLinks": "External Links", "SSE.Views.DataTab.capDataFromText": "Get Data", "SSE.Views.DataTab.capGoalSeek": "Goal Seek", "SSE.Views.DataTab.mniFromFile": "From Local TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "From TXT/CSV Web Address", "SSE.Views.DataTab.mniFromXMLFile": "From Local XML", "SSE.Views.DataTab.textBelow": "Summary rows below detail", "SSE.Views.DataTab.textClear": "Clear outline", "SSE.Views.DataTab.textColumns": "Ungroup columns", "SSE.Views.DataTab.textGroupColumns": "Group columns", "SSE.Views.DataTab.textGroupRows": "Group rows", "SSE.Views.DataTab.textRightOf": "Summary columns to right of detail", "SSE.Views.DataTab.textRows": "Ungroup rows", "SSE.Views.DataTab.tipCustomSort": "Custom sort", "SSE.Views.DataTab.tipDataFromText": "Get data from file", "SSE.Views.DataTab.tipDataValidation": "Data validation", "SSE.Views.DataTab.tipExternalLinks": "View other files this spreadsheet is linked to", "SSE.Views.DataTab.tipGoalSeek": "Find the right input for the value you want", "SSE.Views.DataTab.tipGroup": "Group range of cells", "SSE.Views.DataTab.tipRemDuplicates": "Remove duplicate rows from a sheet", "SSE.Views.DataTab.tipToColumns": "Separate cell text into columns", "SSE.Views.DataTab.tipUngroup": "Ungroup range of cells", "SSE.Views.DataValidationDialog.errorFormula": "The value currently evaluates to an error. Do you want to continue?", "SSE.Views.DataValidationDialog.errorInvalid": "The value you entered for the field \"{0}\" is invalid.", "SSE.Views.DataValidationDialog.errorInvalidDate": "The date you entered for the field \"{0}\" is invalid.", "SSE.Views.DataValidationDialog.errorInvalidList": "The list source must be a delimited list, or a reference to single row or column.", "SSE.Views.DataValidationDialog.errorInvalidTime": "The time you entered for the field \"{0}\" is invalid.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "The \"{1}\" field must be greater than or equal to the \"{0}\" field.", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "You must enter a value in both field \"{0}\" and field \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "You must enter a value in field \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "A named range you specified cannot be found.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Negative values cannot be used in conditions \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "The field \"{0}\" must be a numeric value, numeric expression, or refer to a cell containing a numeric value.", "SSE.Views.DataValidationDialog.strError": "Error alert", "SSE.Views.DataValidationDialog.strInput": "Input message", "SSE.Views.DataValidationDialog.strSettings": "Settings", "SSE.Views.DataValidationDialog.textAlert": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAllow": "Allow", "SSE.Views.DataValidationDialog.textApply": "Apply these changes to all other cells with the same settings", "SSE.Views.DataValidationDialog.textCellSelected": "When cell is selected, show this input message", "SSE.Views.DataValidationDialog.textCompare": "Compare to", "SSE.Views.DataValidationDialog.textData": "Data", "SSE.Views.DataValidationDialog.textEndDate": "End date", "SSE.Views.DataValidationDialog.textEndTime": "End time", "SSE.Views.DataValidationDialog.textError": "Error message", "SSE.Views.DataValidationDialog.textFormula": "Formula", "SSE.Views.DataValidationDialog.textIgnore": "Ignore blank", "SSE.Views.DataValidationDialog.textInput": "Input message", "SSE.Views.DataValidationDialog.textMax": "Maximum", "SSE.Views.DataValidationDialog.textMessage": "Message", "SSE.Views.DataValidationDialog.textMin": "Minimum", "SSE.Views.DataValidationDialog.textSelectData": "Select data", "SSE.Views.DataValidationDialog.textShowDropDown": "Show drop-down list in cell", "SSE.Views.DataValidationDialog.textShowError": "Show error alert after invalid data is entered", "SSE.Views.DataValidationDialog.textShowInput": "Show input message when cell is selected", "SSE.Views.DataValidationDialog.textSource": "Source", "SSE.Views.DataValidationDialog.textStartDate": "Start date", "SSE.Views.DataValidationDialog.textStartTime": "Start time", "SSE.Views.DataValidationDialog.textStop": "Stop", "SSE.Views.DataValidationDialog.textStyle": "Style", "SSE.Views.DataValidationDialog.textTitle": "Title", "SSE.Views.DataValidationDialog.textUserEnters": "When user enters invalid data, show this error alert", "SSE.Views.DataValidationDialog.txtAny": "Any value", "SSE.Views.DataValidationDialog.txtBetween": "between", "SSE.Views.DataValidationDialog.txtDate": "Date", "SSE.Views.DataValidationDialog.txtDecimal": "Decimal", "SSE.Views.DataValidationDialog.txtElTime": "Elapsed time", "SSE.Views.DataValidationDialog.txtEndDate": "End date", "SSE.Views.DataValidationDialog.txtEndTime": "End time", "SSE.Views.DataValidationDialog.txtEqual": "equals", "SSE.Views.DataValidationDialog.txtGreaterThan": "greater than", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "greater than or equal to", "SSE.Views.DataValidationDialog.txtLength": "Length", "SSE.Views.DataValidationDialog.txtLessThan": "less than", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "less than or equal to", "SSE.Views.DataValidationDialog.txtList": "List", "SSE.Views.DataValidationDialog.txtNotBetween": "not between", "SSE.Views.DataValidationDialog.txtNotEqual": "does not equal", "SSE.Views.DataValidationDialog.txtOther": "Other", "SSE.Views.DataValidationDialog.txtStartDate": "Start date", "SSE.Views.DataValidationDialog.txtStartTime": "Start time", "SSE.Views.DataValidationDialog.txtTextLength": "Text length", "SSE.Views.DataValidationDialog.txtTime": "Time", "SSE.Views.DataValidationDialog.txtWhole": "Whole number", "SSE.Views.DigitalFilterDialog.capAnd": "Và", "SSE.Views.DigitalFilterDialog.capCondition1": "bằng", "SSE.Views.DigitalFilterDialog.capCondition10": "kh<PERSON>ng kết thúc bằng", "SSE.Views.DigitalFilterDialog.capCondition11": "ch<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition12": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>a", "SSE.Views.DigitalFilterDialog.capCondition2": "không bằng", "SSE.Views.DigitalFilterDialog.capCondition3": "l<PERSON><PERSON> h<PERSON>n", "SSE.Views.DigitalFilterDialog.capCondition30": "is after", "SSE.Views.DigitalFilterDialog.capCondition4": "lớn hơn hoặc bằng", "SSE.Views.DigitalFilterDialog.capCondition40": "is after or equal to", "SSE.Views.DigitalFilterDialog.capCondition5": "nhỏ hơn", "SSE.Views.DigitalFilterDialog.capCondition50": "is before", "SSE.Views.DigitalFilterDialog.capCondition6": "nhỏ hơn hoặc bằng", "SSE.Views.DigitalFilterDialog.capCondition60": "is before or equal to", "SSE.Views.DigitalFilterDialog.capCondition7": "b<PERSON>t đ<PERSON>u với", "SSE.Views.DigitalFilterDialog.capCondition8": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>t đầu bằng", "SSE.Views.DigitalFilterDialog.capCondition9": "kết thúc bằng", "SSE.Views.DigitalFilterDialog.capOr": "Hoặc", "SSE.Views.DigitalFilterDialog.textNoFilter": "k<PERSON><PERSON><PERSON> có bộ lọc", "SSE.Views.DigitalFilterDialog.textShowRows": "<PERSON><PERSON><PERSON> thị các hàng có", "SSE.Views.DigitalFilterDialog.textUse1": "Sử dụng ? để thể hiện ký tự đơn bất kỳ", "SSE.Views.DigitalFilterDialog.textUse2": "Sử dụng * để hiển thị chuỗi ký tự bất kỳ", "SSE.Views.DigitalFilterDialog.txtSelectDate": "Select date", "SSE.Views.DigitalFilterDialog.txtTitle": "<PERSON><PERSON> lọc tùy chỉnh", "SSE.Views.DocumentHolder.advancedEquationText": "Equation settings", "SSE.Views.DocumentHolder.advancedImgText": "Cài đặt Hình <PERSON>nh <PERSON> cao", "SSE.Views.DocumentHolder.advancedShapeText": "Cài đặt Nâng cao Hình dạng", "SSE.Views.DocumentHolder.advancedSlicerText": "Slicer advanced settings", "SSE.Views.DocumentHolder.allLinearText": "All - Linear", "SSE.Views.DocumentHolder.allProfText": "All - Professional", "SSE.Views.DocumentHolder.bottomCellText": "<PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.DocumentHolder.bulletsText": "<PERSON><PERSON><PERSON> đầu dòng và Số thứ tự", "SSE.Views.DocumentHolder.centerCellText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.chartDataText": "Select Chart Data", "SSE.Views.DocumentHolder.chartText": "Cài đặt Nâng cao Biểu đồ", "SSE.Views.DocumentHolder.chartTypeText": "Change Chart Type", "SSE.Views.DocumentHolder.currLinearText": "Current - Linear", "SSE.Views.DocumentHolder.currProfText": "Current - Professional", "SSE.Views.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.direct270Text": "<PERSON><PERSON><PERSON> v<PERSON>n bản lên", "SSE.Views.DocumentHolder.direct90Text": "<PERSON><PERSON><PERSON> v<PERSON>n bản x<PERSON>", "SSE.Views.DocumentHolder.directHText": "Nằm ngang", "SSE.Views.DocumentHolder.directionText": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> bản", "SSE.Views.DocumentHolder.editChartText": "Chỉnh sửa <PERSON> liệu", "SSE.Views.DocumentHolder.editHyperlinkText": "Chỉnh sửa <PERSON><PERSON><PERSON> liên kết", "SSE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "SSE.Views.DocumentHolder.insertColumnLeftText": "<PERSON><PERSON><PERSON> trái", "SSE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON> trên", "SSE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "Actual size", "SSE.Views.DocumentHolder.removeHyperlinkText": "<PERSON><PERSON><PERSON> si<PERSON>u liên k<PERSON>t", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON> bộ cột", "SSE.Views.DocumentHolder.selectDataText": "<PERSON><PERSON> li<PERSON> c<PERSON>t", "SSE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.showEqToolbar": "Show Equation Toolbar", "SSE.Views.DocumentHolder.strDelete": "Remove Signature", "SSE.Views.DocumentHolder.strDetails": "Signature Details", "SSE.Views.DocumentHolder.strSetup": "Signature Setup", "SSE.Views.DocumentHolder.strSign": "Sign", "SSE.Views.DocumentHolder.textAlign": "Align", "SSE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "<PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.DocumentHolder.textArrangeBackward": "Gửi về phía sau", "SSE.Views.DocumentHolder.textArrangeForward": "<PERSON> chuyển tiến lên", "SSE.Views.DocumentHolder.textArrangeFront": "<PERSON><PERSON><PERSON> lên <PERSON> cảnh", "SSE.Views.DocumentHolder.textAverage": "TRUNG BÌNH", "SSE.Views.DocumentHolder.textBullets": "Bullets", "SSE.Views.DocumentHolder.textCopyCells": "Copy cells", "SSE.Views.DocumentHolder.textCount": "Count", "SSE.Views.DocumentHolder.textCrop": "Crop", "SSE.Views.DocumentHolder.textCropFill": "Fill", "SSE.Views.DocumentHolder.textCropFit": "Fit", "SSE.Views.DocumentHolder.textEditPoints": "Edit points", "SSE.Views.DocumentHolder.textEntriesList": "<PERSON><PERSON><PERSON> từ danh sách thả xuống", "SSE.Views.DocumentHolder.textFillDays": "Fill days", "SSE.Views.DocumentHolder.textFillFormatOnly": "Fill formatting only", "SSE.Views.DocumentHolder.textFillMonths": "Fill months", "SSE.Views.DocumentHolder.textFillSeries": "Fill series", "SSE.Views.DocumentHolder.textFillWeekdays": "Fill weekdays", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Fill without formatting", "SSE.Views.DocumentHolder.textFillYears": "Fill years", "SSE.Views.DocumentHolder.textFlashFill": "Flash fill", "SSE.Views.DocumentHolder.textFlipH": "Flip Horizontally", "SSE.Views.DocumentHolder.textFlipV": "Flip Vertically", "SSE.Views.DocumentHolder.textFreezePanes": "Freeze Panes", "SSE.Views.DocumentHolder.textFromFile": "From File", "SSE.Views.DocumentHolder.textFromStorage": "From Storage", "SSE.Views.DocumentHolder.textFromUrl": "From URL", "SSE.Views.DocumentHolder.textGrowthTrend": "Growth trend", "SSE.Views.DocumentHolder.textLinearTrend": "Linear trend", "SSE.Views.DocumentHolder.textListSettings": "List Settings", "SSE.Views.DocumentHolder.textMacro": "Assign <PERSON>", "SSE.Views.DocumentHolder.textMax": "Max", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "More functions", "SSE.Views.DocumentHolder.textMoreFormats": "More formats", "SSE.Views.DocumentHolder.textNone": "K<PERSON>ô<PERSON>", "SSE.Views.DocumentHolder.textNumbering": "Numbering", "SSE.Views.DocumentHolder.textReplace": "Replace image", "SSE.Views.DocumentHolder.textResetCrop": "Reset crop", "SSE.Views.DocumentHolder.textRotate": "Rotate", "SSE.Views.DocumentHolder.textRotate270": "Rotate 90В° Counterclockwise", "SSE.Views.DocumentHolder.textRotate90": "Rotate 90В° Clockwise", "SSE.Views.DocumentHolder.textSaveAsPicture": "Save as picture", "SSE.Views.DocumentHolder.textSeries": "Series", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Align bottom", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Align center", "SSE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON> left", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Align middle", "SSE.Views.DocumentHolder.textShapeAlignRight": "Align right", "SSE.Views.DocumentHolder.textShapeAlignTop": "Align top", "SSE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "Sum", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "Unfreeze Panes", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "Arrow bullets", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Checkmark bullets", "SSE.Views.DocumentHolder.tipMarkersDash": "Dash bullets", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Filled rhombus bullets", "SSE.Views.DocumentHolder.tipMarkersFRound": "Filled round bullets", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Filled square bullets", "SSE.Views.DocumentHolder.tipMarkersHRound": "Hollow round bullets", "SSE.Views.DocumentHolder.tipMarkersStar": "Star bullets", "SSE.Views.DocumentHolder.topCellText": "<PERSON><PERSON><PERSON> trên c<PERSON>ng", "SSE.Views.DocumentHolder.txtAccounting": "Accounting", "SSE.Views.DocumentHolder.txtAddComment": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> lu<PERSON>n", "SSE.Views.DocumentHolder.txtAddNamedRange": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON>p", "SSE.Views.DocumentHolder.txtAscending": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Tự động vừa với chiều rộng của cột", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Tự động vừa với chiều cao của hàng", "SSE.Views.DocumentHolder.txtAverage": "Average", "SSE.Views.DocumentHolder.txtCellFormat": "Format cells", "SSE.Views.DocumentHolder.txtClear": "Xóa", "SSE.Views.DocumentHolder.txtClearAll": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.DocumentHolder.txtClearComments": "<PERSON><PERSON><PERSON> lu<PERSON>", "SSE.Views.DocumentHolder.txtClearFormat": "<PERSON><PERSON><PERSON> d<PERSON>ng", "SSE.Views.DocumentHolder.txtClearHyper": "<PERSON><PERSON><PERSON> li<PERSON> k<PERSON>", "SSE.Views.DocumentHolder.txtClearPivotField": "Clear filter from {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "<PERSON><PERSON><PERSON> c<PERSON>c nhóm Sparkline đã Chọn", "SSE.Views.DocumentHolder.txtClearSparklines": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> Sparkline đã chọn", "SSE.Views.DocumentHolder.txtClearText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCollapse": "Collapse", "SSE.Views.DocumentHolder.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON> bộ cột", "SSE.Views.DocumentHolder.txtColumnWidth": "Đặt chiều rộng cột", "SSE.Views.DocumentHolder.txtCondFormat": "Conditional formatting", "SSE.Views.DocumentHolder.txtCopy": "Sao chép", "SSE.Views.DocumentHolder.txtCount": "Count", "SSE.Views.DocumentHolder.txtCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "<PERSON><PERSON><PERSON> rộng cột tùy chỉnh", "SSE.Views.DocumentHolder.txtCustomRowHeight": "<PERSON><PERSON><PERSON> cao cột tùy chỉnh", "SSE.Views.DocumentHolder.txtCustomSort": "Custom sort", "SSE.Views.DocumentHolder.txtCut": "Cắt", "SSE.Views.DocumentHolder.txtDateLong": "Long Date", "SSE.Views.DocumentHolder.txtDateShort": "Short Date", "SSE.Views.DocumentHolder.txtDelete": "Xóa", "SSE.Views.DocumentHolder.txtDelField": "Remove", "SSE.Views.DocumentHolder.txtDescending": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDifference": "Difference from", "SSE.Views.DocumentHolder.txtDistribHor": "Distribute horizontally", "SSE.Views.DocumentHolder.txtDistribVert": "Distribute vertically", "SSE.Views.DocumentHolder.txtEditComment": "Chỉnh sửa bình luận", "SSE.Views.DocumentHolder.txtEditObject": "Edit object", "SSE.Views.DocumentHolder.txtExpand": "Expand", "SSE.Views.DocumentHolder.txtExpandCollapse": "Expand/Collapse", "SSE.Views.DocumentHolder.txtExpandEntire": "Expand Entire Field", "SSE.Views.DocumentHolder.txtFieldSettings": "Field settings", "SSE.Views.DocumentHolder.txtFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtFilterCellColor": "<PERSON><PERSON><PERSON> theo màu của ô", "SSE.Views.DocumentHolder.txtFilterFontColor": "<PERSON><PERSON><PERSON> theo màu chữ", "SSE.Views.DocumentHolder.txtFilterValue": "<PERSON><PERSON><PERSON> theo giá trị của ô đã chọn", "SSE.Views.DocumentHolder.txtFormula": "<PERSON><PERSON><PERSON> h<PERSON> s<PERSON>", "SSE.Views.DocumentHolder.txtFraction": "Fraction", "SSE.Views.DocumentHolder.txtGeneral": "General", "SSE.Views.DocumentHolder.txtGetLink": "Get link to this range", "SSE.Views.DocumentHolder.txtGrandTotal": "Grand total", "SSE.Views.DocumentHolder.txtGroup": "Nhóm", "SSE.Views.DocumentHolder.txtHide": "Ẩn", "SSE.Views.DocumentHolder.txtIndex": "Index", "SSE.Views.DocumentHolder.txtInsert": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtInsHyperlink": "<PERSON><PERSON><PERSON> li<PERSON> k<PERSON>", "SSE.Views.DocumentHolder.txtInsImage": "Insert image from file", "SSE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "SSE.Views.DocumentHolder.txtLabelFilter": "Label filters", "SSE.Views.DocumentHolder.txtMax": "Max", "SSE.Views.DocumentHolder.txtMin": "Min", "SSE.Views.DocumentHolder.txtMoreOptions": "More options", "SSE.Views.DocumentHolder.txtNormal": "No calculation", "SSE.Views.DocumentHolder.txtNumber": "Number", "SSE.Views.DocumentHolder.txtNumFormat": "Number Format", "SSE.Views.DocumentHolder.txtPaste": "Dán", "SSE.Views.DocumentHolder.txtPercent": "% của", "SSE.Views.DocumentHolder.txtPercentage": "Percentage", "SSE.Views.DocumentHolder.txtPercentDiff": "% khác biệt so với", "SSE.Views.DocumentHolder.txtPercentOfCol": "% of column total", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% of grand total", "SSE.Views.DocumentHolder.txtPercentOfParent": "% of parent total", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% of parent column total", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% of parent row total", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% running total in", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% of row total", "SSE.Views.DocumentHolder.txtPivotSettings": "Pivot Table settings", "SSE.Views.DocumentHolder.txtProduct": "Product", "SSE.Views.DocumentHolder.txtRankAscending": "Rank smallest to largest", "SSE.Views.DocumentHolder.txtRankDescending": "Rank largest to smallest", "SSE.Views.DocumentHolder.txtReapply": "<PERSON><PERSON> d<PERSON> lại", "SSE.Views.DocumentHolder.txtRefresh": "Refresh", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON><PERSON> bộ hàng", "SSE.Views.DocumentHolder.txtRowHeight": "Đặt chi<PERSON>u cao cột", "SSE.Views.DocumentHolder.txtRunTotal": "Running total in", "SSE.Views.DocumentHolder.txtScientific": "Scientific", "SSE.Views.DocumentHolder.txtSelect": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftDown": "<PERSON><PERSON><PERSON><PERSON> ô xuống dưới", "SSE.Views.DocumentHolder.txtShiftLeft": "<PERSON><PERSON><PERSON><PERSON> ô sang trái", "SSE.Views.DocumentHolder.txtShiftRight": "<PERSON><PERSON><PERSON><PERSON> ô sang phải", "SSE.Views.DocumentHolder.txtShiftUp": "<PERSON><PERSON><PERSON><PERSON> ô lên trên", "SSE.Views.DocumentHolder.txtShow": "<PERSON><PERSON><PERSON> thị", "SSE.Views.DocumentHolder.txtShowAs": "Show values as", "SSE.Views.DocumentHolder.txtShowComment": "<PERSON><PERSON><PERSON> thị bình luận", "SSE.Views.DocumentHolder.txtShowDetails": "Show details", "SSE.Views.DocumentHolder.txtSort": "<PERSON><PERSON><PERSON>p", "SSE.Views.DocumentHolder.txtSortCellColor": "<PERSON><PERSON><PERSON> được chọn trên cùng", "SSE.Views.DocumentHolder.txtSortFontColor": "<PERSON><PERSON><PERSON> phông chữ được chọn trên cùng", "SSE.Views.DocumentHolder.txtSortOption": "More sort options", "SSE.Views.DocumentHolder.txtSparklines": "Sparklines", "SSE.Views.DocumentHolder.txtSubtotalField": "Subtotal", "SSE.Views.DocumentHolder.txtSum": "Sum", "SSE.Views.DocumentHolder.txtSummarize": "Summarize values by", "SSE.Views.DocumentHolder.txtText": "Text", "SSE.Views.DocumentHolder.txtTextAdvanced": "Cài đặt Nâng cao <PERSON> bản", "SSE.Views.DocumentHolder.txtTime": "Time", "SSE.Views.DocumentHolder.txtTop10": "Top 10", "SSE.Views.DocumentHolder.txtUngroup": "Bỏ nhóm", "SSE.Views.DocumentHolder.txtValueFieldSettings": "Value field settings", "SSE.Views.DocumentHolder.txtValueFilter": "Value filters", "SSE.Views.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON> chỉnh dọc", "SSE.Views.ExternalLinksDlg.closeButtonText": "Close", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Automatically update data from the linked sources", "SSE.Views.ExternalLinksDlg.textChange": "Change source", "SSE.Views.ExternalLinksDlg.textDelete": "Break links", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Break all links", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textOpen": "Open source", "SSE.Views.ExternalLinksDlg.textSource": "Source", "SSE.Views.ExternalLinksDlg.textStatus": "Status", "SSE.Views.ExternalLinksDlg.textUnknown": "Unknown", "SSE.Views.ExternalLinksDlg.textUpdate": "Update values", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Update all", "SSE.Views.ExternalLinksDlg.textUpdating": "Updating...", "SSE.Views.ExternalLinksDlg.txtTitle": "External links", "SSE.Views.FieldSettingsDialog.strLayout": "Layout", "SSE.Views.FieldSettingsDialog.strSubtotals": "Subtotals", "SSE.Views.FieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.FieldSettingsDialog.textReport": "Report form", "SSE.Views.FieldSettingsDialog.textTitle": "Field settings", "SSE.Views.FieldSettingsDialog.txtAverage": "Average", "SSE.Views.FieldSettingsDialog.txtBlank": "Insert blank rows after each item", "SSE.Views.FieldSettingsDialog.txtBottom": "Show at bottom of group", "SSE.Views.FieldSettingsDialog.txtCompact": "Compact", "SSE.Views.FieldSettingsDialog.txtCount": "Count", "SSE.Views.FieldSettingsDialog.txtCountNums": "Count numbers", "SSE.Views.FieldSettingsDialog.txtCustomName": "Custom name", "SSE.Views.FieldSettingsDialog.txtEmpty": "Show items with no data", "SSE.Views.FieldSettingsDialog.txtMax": "Max", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "Outline", "SSE.Views.FieldSettingsDialog.txtProduct": "Product", "SSE.Views.FieldSettingsDialog.txtRepeat": "Repeat items labels at each row", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Show subtotals", "SSE.Views.FieldSettingsDialog.txtSourceName": "Source name:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "Sum", "SSE.Views.FieldSettingsDialog.txtSummarize": "Functions for subtotals", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabular", "SSE.Views.FieldSettingsDialog.txtTop": "Show at top of group", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.ariaFileMenu": "File menu", "SSE.Views.FileMenu.btnBackCaption": "<PERSON><PERSON> tới Tài liệu", "SSE.Views.FileMenu.btnCloseEditor": "Close File", "SSE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON><PERSON> mới", "SSE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON> về dưới dạng", "SSE.Views.FileMenu.btnExitCaption": "Close", "SSE.Views.FileMenu.btnExportToPDFCaption": "Export to PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "Open", "SSE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON> g<PERSON>", "SSE.Views.FileMenu.btnHistoryCaption": "Version History", "SSE.Views.FileMenu.btnInfoCaption": "Thông tin Bảng tính", "SSE.Views.FileMenu.btnPrintCaption": "In", "SSE.Views.FileMenu.btnProtectCaption": "Protect", "SSE.Views.FileMenu.btnRecentFilesCaption": "Mở gần đây", "SSE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON> tên", "SSE.Views.FileMenu.btnReturnCaption": "Quay lại B<PERSON>h", "SSE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON><PERSON> truy cập", "SSE.Views.FileMenu.btnSaveAsCaption": "<PERSON><PERSON><PERSON> dạng", "SSE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Save Copy As", "SSE.Views.FileMenu.btnSettingsCaption": "Cài đặt nâng cao", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "SSE.Views.FileMenu.btnToEditCaption": "Chỉnh s<PERSON>a <PERSON>h", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Blank Spreadsheet", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Create New", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Apply", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Add Author", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Add Text", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Application", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Tác g<PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "<PERSON><PERSON> đ<PERSON>i quyền truy cập", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Comment", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Created", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Last Modified By", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Last Modified", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Owner", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON><PERSON><PERSON> cá nhân có quyền", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Spreadsheet info", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Subject", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>ảng t<PERSON>h", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Uploaded", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Access Rights", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "<PERSON><PERSON> đ<PERSON>i quyền truy cập", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON><PERSON><PERSON> cá nhân có quyền", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Chế độ đồng chỉnh sửa", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Use 1904 date system", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Decimal separator", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Dictionary language", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Enable iterative calculation", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Phông chữ gợi ý", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "<PERSON><PERSON><PERSON> ngữ công thức", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Ví dụ: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Show function tooltip", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Show horizontal scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Ignore words in UPPERCASE", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Ignore words with numbers", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Macros settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Maximum change", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Maximum iterations", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Show the paste options button when the content is pasted", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "R1C1 reference style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "<PERSON><PERSON><PERSON><PERSON> lập khu vực", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "<PERSON><PERSON> dụ:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTL interface", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Show comments in sheet", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Show changes from other users", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Show resolved comments", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Snapped to the grid while scrolling", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Nghiêm ngặt", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Tab style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Interface theme", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Thousands separator", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Đơn vị đo lư<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Use separators based on regional settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Show vertical scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "<PERSON><PERSON><PERSON> trị <PERSON> to Mặc định", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Mỗi 10 phút", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Mỗi 30 phút", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Mỗi 5 phút", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "Mỗi giờ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Tự động khôi phục", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "<PERSON><PERSON> động lưu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Tắt", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "Fill", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Lưu vào Server", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "Line", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Mỗi phút", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Reference Style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "Advanced settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "Appearance", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "AutoCorrect options...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Belarusian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Bulgarian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Catalan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Default cache mode", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Calculating", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Collaboration", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Czech", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Customize quick access", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Danish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "German", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Editing and saving", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Greek", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "<PERSON><PERSON><PERSON><PERSON> <PERSON>h", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Spanish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Real-time co-editing. All changes are saved automatically", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Finnish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "French", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Hungarian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "Armenian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonesian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Inch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Italian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Japanese", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Korean", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Last used", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Lao", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Latvian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "như OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Norwegian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Dutch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Proofing", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Portuguese (Brazil)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "Portuguese (Portugal)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Show the Quick Print button in the editor header", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Region", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Romanian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Enable All", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Enable all macros without a notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Turn on screen reader support", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Default sheet direction", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "This setting will affect only the new sheets", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Left-to-right", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Right-to-left", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Slovak", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Slovenian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Disable all", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Disable all macros without a notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Use the \"Save\" button to sync the changes you and others make", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Swedish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Use toolbar color as tabs background", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Turkish", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ukrainian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Use Alt key to navigate the user interface using the keyboard", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Use Option key to navigate the user interface using the keyboard", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vietnamese", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Show Notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Disable all macros with a notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "như Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Workspace", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "Chinese", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Warning", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "With password", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Protect Spreadsheet", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "With signature", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Valid signatures have been added to the spreadsheet.<br>The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Ensure the integrity of the spreadsheet by adding an<br>invisible digital signature", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Edit spreadsheet", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Editing will remove signatures from the spreadsheet.<br>Continue?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "This spreadsheet has been protected by password", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Encrypt this spreadsheet with a password", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "This spreadsheet needs to be signed.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Valid signatures have been added to the spreadsheet. The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Some of the digital signatures in spreadsheet are invalid or could not be verified. The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "View signatures", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Download as", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Save copy as", "SSE.Views.FillSeriesDialog.textAuto": "Tự động điền", "SSE.Views.FillSeriesDialog.textCols": "Columns", "SSE.Views.FillSeriesDialog.textDate": "Date", "SSE.Views.FillSeriesDialog.textDateUnit": "Date unit", "SSE.Views.FillSeriesDialog.textDay": "Day", "SSE.Views.FillSeriesDialog.textGrowth": "Growth", "SSE.Views.FillSeriesDialog.textLinear": "Linear", "SSE.Views.FillSeriesDialog.textMonth": "Month", "SSE.Views.FillSeriesDialog.textRows": "Rows", "SSE.Views.FillSeriesDialog.textSeries": "Series in", "SSE.Views.FillSeriesDialog.textStep": "Step value", "SSE.Views.FillSeriesDialog.textStop": "Stop value", "SSE.Views.FillSeriesDialog.textTitle": "Series", "SSE.Views.FillSeriesDialog.textTrend": "Trend", "SSE.Views.FillSeriesDialog.textType": "Type", "SSE.Views.FillSeriesDialog.textWeek": "Weekday", "SSE.Views.FillSeriesDialog.textYear": "Year", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FormatRulesEditDlg.fillColor": "Fill color", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Warning", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 color scale", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 color scale", "SSE.Views.FormatRulesEditDlg.textAllBorders": "All borders", "SSE.Views.FormatRulesEditDlg.textAppearance": "Bar appearance", "SSE.Views.FormatRulesEditDlg.textApply": "Apply to range", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automatic", "SSE.Views.FormatRulesEditDlg.textAxis": "Axis", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Bar direction", "SSE.Views.FormatRulesEditDlg.textBold": "Bold", "SSE.Views.FormatRulesEditDlg.textBorder": "Border", "SSE.Views.FormatRulesEditDlg.textBordersColor": "Borders color", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Border style", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Bottom borders", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Cannot add the conditional formatting.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Cell midpoint", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Inside vertical borders", "SSE.Views.FormatRulesEditDlg.textClear": "Clear", "SSE.Views.FormatRulesEditDlg.textColor": "Text color", "SSE.Views.FormatRulesEditDlg.textContext": "Context", "SSE.Views.FormatRulesEditDlg.textCustom": "Custom", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Diagonal down border", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Diagonal up border", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Enter a valid formula.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "The formula you entered does not evaluate to a number, date, time or string.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Enter a value.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "The value you entered is not a valid number, date, time or string.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "The value for the {0} must be greater than the value for the {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Enter a number between {0} and {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Fill", "SSE.Views.FormatRulesEditDlg.textFormat": "Format", "SSE.Views.FormatRulesEditDlg.textFormula": "Formula", "SSE.Views.FormatRulesEditDlg.textGradient": "Gradient", "SSE.Views.FormatRulesEditDlg.textIconLabel": "when {0} {1} and", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "when {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "when value is", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "One or more icon data ranges overlap.<br>Adjust icon data range values so that the ranges do not overlap.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Icon style", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Inside borders", "SSE.Views.FormatRulesEditDlg.textInvalid": "Invalid data range.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.FormatRulesEditDlg.textItalic": "Italic", "SSE.Views.FormatRulesEditDlg.textItem": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Left to right", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Left borders", "SSE.Views.FormatRulesEditDlg.textLongBar": "longest bar", "SSE.Views.FormatRulesEditDlg.textMaximum": "Maximum", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Maxpoint", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Inside horizontal borders", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Midpoint", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimum", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Minpoint", "SSE.Views.FormatRulesEditDlg.textNegative": "Negative", "SSE.Views.FormatRulesEditDlg.textNewColor": "<PERSON><PERSON><PERSON> tùy chỉnh", "SSE.Views.FormatRulesEditDlg.textNoBorders": "No borders", "SSE.Views.FormatRulesEditDlg.textNone": "None", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "One or more of the specified values is not a valid percentage.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "The specified {0} value is not a valid percentage.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "One or more of the specified values is not a valid percentile.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "The specified {0} value is not a valid percentile.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Outside borders", "SSE.Views.FormatRulesEditDlg.textPercent": "Percent", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentile", "SSE.Views.FormatRulesEditDlg.textPosition": "Position", "SSE.Views.FormatRulesEditDlg.textPositive": "Positive", "SSE.Views.FormatRulesEditDlg.textPresets": "Presets", "SSE.Views.FormatRulesEditDlg.textPreview": "Preview", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "You cannot use relative references in conditional formatting criteria for color scales, data bars, and icon sets.", "SSE.Views.FormatRulesEditDlg.textReverse": "Reverse icons order", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Right to left", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Right borders", "SSE.Views.FormatRulesEditDlg.textRule": "Rule", "SSE.Views.FormatRulesEditDlg.textSameAs": "Same as positive", "SSE.Views.FormatRulesEditDlg.textSelectData": "Select data", "SSE.Views.FormatRulesEditDlg.textShortBar": "shortest bar", "SSE.Views.FormatRulesEditDlg.textShowBar": "Show bar only", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Show icon only", "SSE.Views.FormatRulesEditDlg.textSingleRef": "This type of reference cannot be used in a conditional formatting formula.<br>Change the reference to a single cell, or use the reference with a worksheet function, such as =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Solid", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Strikeout", "SSE.Views.FormatRulesEditDlg.textSubscript": "Subscript", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Superscript", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Top borders", "SSE.Views.FormatRulesEditDlg.textUnderline": "Underline", "SSE.Views.FormatRulesEditDlg.tipBorders": "Borders", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Number format", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Accounting", "SSE.Views.FormatRulesEditDlg.txtCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDate": "Date", "SSE.Views.FormatRulesEditDlg.txtDateLong": "Long date", "SSE.Views.FormatRulesEditDlg.txtDateShort": "Short date", "SSE.Views.FormatRulesEditDlg.txtEmpty": "This field is required", "SSE.Views.FormatRulesEditDlg.txtFraction": "Fraction", "SSE.Views.FormatRulesEditDlg.txtGeneral": "General", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "No icon", "SSE.Views.FormatRulesEditDlg.txtNumber": "Number", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Percentage", "SSE.Views.FormatRulesEditDlg.txtScientific": "Scientific", "SSE.Views.FormatRulesEditDlg.txtText": "Text", "SSE.Views.FormatRulesEditDlg.txtTime": "Time", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Edit formatting rule", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "New formatting rule", "SSE.Views.FormatRulesManagerDlg.guestText": "Guest", "SSE.Views.FormatRulesManagerDlg.lockText": "Locked", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 std dev above average", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 std dev below average", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 std dev above average", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 std dev below average", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 std dev above average", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 std dev below average", "SSE.Views.FormatRulesManagerDlg.textAbove": "Above average", "SSE.Views.FormatRulesManagerDlg.textApply": "Apply to", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Cell value begins with", "SSE.Views.FormatRulesManagerDlg.textBelow": "Below average", "SSE.Views.FormatRulesManagerDlg.textBetween": "is between {0} and {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Cell value", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Graded color scale", "SSE.Views.FormatRulesManagerDlg.textContains": "Cell value contains", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Cell contains a blank value", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Cell contains an error", "SSE.Views.FormatRulesManagerDlg.textDelete": "Delete", "SSE.Views.FormatRulesManagerDlg.textDown": "Move rule down", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Duplicate values", "SSE.Views.FormatRulesManagerDlg.textEdit": "Edit", "SSE.Views.FormatRulesManagerDlg.textEnds": "Cell value ends with", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Equal to or above average", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Equal to or below average", "SSE.Views.FormatRulesManagerDlg.textFormat": "Format", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Icon set", "SSE.Views.FormatRulesManagerDlg.textNew": "New", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "is not between {0} and {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Cell value does not contain", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Cell does not contain a blank value", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Cell does not contain an error", "SSE.Views.FormatRulesManagerDlg.textRules": "Rules", "SSE.Views.FormatRulesManagerDlg.textScope": "Show formatting rules for", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Select data", "SSE.Views.FormatRulesManagerDlg.textSelection": "Current selection", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "This pivot", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "This worksheet", "SSE.Views.FormatRulesManagerDlg.textThisTable": "This table", "SSE.Views.FormatRulesManagerDlg.textUnique": "Unique values", "SSE.Views.FormatRulesManagerDlg.textUp": "Move rule up", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Conditional formatting", "SSE.Views.FormatSettingsDialog.textCategory": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textDecimal": "<PERSON><PERSON><PERSON><PERSON> phân", "SSE.Views.FormatSettingsDialog.textFormat": "<PERSON><PERSON><PERSON> d<PERSON>ng", "SSE.Views.FormatSettingsDialog.textLinked": "Linked to source", "SSE.Views.FormatSettingsDialog.textSeparator": "Sử dụng phân tách 1000", "SSE.Views.FormatSettingsDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textTitle": "<PERSON><PERSON><PERSON> dạng số", "SSE.Views.FormatSettingsDialog.txtAccounting": "<PERSON><PERSON> toán", "SSE.Views.FormatSettingsDialog.txtAs10": "Bằng ph<PERSON>n mư<PERSON> (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "Bằng phần trăm (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "Bằng ph<PERSON>n mư<PERSON><PERSON> s<PERSON> (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Bằng m<PERSON><PERSON> n<PERSON>a (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "Bằng ph<PERSON>n tư (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Bằng ph<PERSON>n tám (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.FormatSettingsDialog.txtCustom": "Tuỳ chỉnh", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Please enter the custom number format carefully. Spreadsheet Editor does not check custom formats for errors that may affect the xlsx file.", "SSE.Views.FormatSettingsDialog.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtFraction": "<PERSON><PERSON> số", "SSE.Views.FormatSettingsDialog.txtGeneral": "<PERSON><PERSON><PERSON> quát", "SSE.Views.FormatSettingsDialog.txtNone": "None", "SSE.Views.FormatSettingsDialog.txtNumber": "Số", "SSE.Views.FormatSettingsDialog.txtPercentage": "<PERSON><PERSON><PERSON> tr<PERSON>m", "SSE.Views.FormatSettingsDialog.txtSample": "Mẫu:", "SSE.Views.FormatSettingsDialog.txtScientific": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtText": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtTime": "<PERSON><PERSON><PERSON><PERSON> gian", "SSE.Views.FormatSettingsDialog.txtUpto1": "<PERSON><PERSON><PERSON> đa một chữ số (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "<PERSON><PERSON>i đa hai chữ số (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "<PERSON><PERSON><PERSON> đa ba chữ số (131/135)", "SSE.Views.FormulaDialog.sDescription": "<PERSON><PERSON>", "SSE.Views.FormulaDialog.textGroupDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.textListDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtRecommended": "Recommended", "SSE.Views.FormulaDialog.txtSearch": "Search", "SSE.Views.FormulaDialog.txtTitle": "<PERSON><PERSON><PERSON> h<PERSON> s<PERSON>", "SSE.Views.FormulaTab.capBtnRemoveArr": "Remove Arrows", "SSE.Views.FormulaTab.capBtnTraceDep": "Trace Dependents", "SSE.Views.FormulaTab.capBtnTracePrec": "Trace Precedents", "SSE.Views.FormulaTab.textAutomatic": "Automatic", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Calculate current sheet", "SSE.Views.FormulaTab.textCalculateWorkbook": "Calculate workbook", "SSE.Views.FormulaTab.textManual": "Manual", "SSE.Views.FormulaTab.tipCalculate": "Calculate", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Calculate the entire workbook", "SSE.Views.FormulaTab.tipRemoveArr": "Remove the arrows drawn by Trace Precedents or Trace Dependents", "SSE.Views.FormulaTab.tipShowFormulas": "Display the formula in each cell instead of the resulting value", "SSE.Views.FormulaTab.tipTraceDep": "Show arrows that indicate which cells are affected by the value of the selected cell", "SSE.Views.FormulaTab.tipTracePrec": "Show arrows that indicate which cells affect the value of the selected cell", "SSE.Views.FormulaTab.tipWatch": "Add cells to the Watch Window list", "SSE.Views.FormulaTab.txtAdditional": "Additional", "SSE.Views.FormulaTab.txtAutosum": "Autosum", "SSE.Views.FormulaTab.txtAutosumTip": "Summation", "SSE.Views.FormulaTab.txtCalculation": "Calculation", "SSE.Views.FormulaTab.txtFormula": "Function", "SSE.Views.FormulaTab.txtFormulaTip": "Insert function", "SSE.Views.FormulaTab.txtMore": "More functions", "SSE.Views.FormulaTab.txtRecent": "Recently used", "SSE.Views.FormulaTab.txtRemDep": "Remove Dependents Arrows", "SSE.Views.FormulaTab.txtRemPrec": "Remove Precedents Arrows", "SSE.Views.FormulaTab.txtShowFormulas": "Show Formulas", "SSE.Views.FormulaTab.txtWatch": "Watch Window", "SSE.Views.FormulaWizard.textAny": "any", "SSE.Views.FormulaWizard.textArgument": "Argument", "SSE.Views.FormulaWizard.textFunction": "Function", "SSE.Views.FormulaWizard.textFunctionRes": "Function result", "SSE.Views.FormulaWizard.textHelp": "Help on this function", "SSE.Views.FormulaWizard.textLogical": "logical", "SSE.Views.FormulaWizard.textNoArgs": "This function has no arguments", "SSE.Views.FormulaWizard.textNoArgsDesc": "this argument has no description", "SSE.Views.FormulaWizard.textNumber": "number", "SSE.Views.FormulaWizard.textReadMore": "Read more", "SSE.Views.FormulaWizard.textRef": "reference", "SSE.Views.FormulaWizard.textText": "text", "SSE.Views.FormulaWizard.textTitle": "Function arguments", "SSE.Views.FormulaWizard.textValue": "Formula result", "SSE.Views.GoalSeekDlg.textChangingCell": "By changing cell", "SSE.Views.GoalSeekDlg.textDataRangeError": "The formula is missing a range", "SSE.Views.GoalSeekDlg.textMustContainFormula": "The cell must contain a formula", "SSE.Views.GoalSeekDlg.textMustContainValue": "Cell must contain a value", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Formula in cell must result in a number", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Reference must be to a single cell", "SSE.Views.GoalSeekDlg.textSelectData": "Select data", "SSE.Views.GoalSeekDlg.textSetCell": "Set cell", "SSE.Views.GoalSeekDlg.textTitle": "Goal seek", "SSE.Views.GoalSeekDlg.textToValue": "To value", "SSE.Views.GoalSeekDlg.txtEmpty": "This field is required", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.GoalSeekStatusDlg.textContinue": "Continue", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Current value:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Goal seeking with cell {0} found a solution.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Goal seeking with cell {0} may not have found a solution.", "SSE.Views.GoalSeekStatusDlg.textPause": "Pause", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Goal seeking with cell {0} on iteration #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "Step", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Target value:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Goal seek status", "SSE.Views.HeaderFooterDialog.textAlign": "Align with page margins", "SSE.Views.HeaderFooterDialog.textAll": "All pages", "SSE.Views.HeaderFooterDialog.textBold": "Bold", "SSE.Views.HeaderFooterDialog.textCenter": "Center", "SSE.Views.HeaderFooterDialog.textColor": "Text color", "SSE.Views.HeaderFooterDialog.textDate": "Date", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Different first page", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Different odd and even pages", "SSE.Views.HeaderFooterDialog.textEven": "Even page", "SSE.Views.HeaderFooterDialog.textFileName": "File name", "SSE.Views.HeaderFooterDialog.textFirst": "First page", "SSE.Views.HeaderFooterDialog.textFooter": "Footer", "SSE.Views.HeaderFooterDialog.textHeader": "Header", "SSE.Views.HeaderFooterDialog.textImage": "Picture", "SSE.Views.HeaderFooterDialog.textInsert": "Insert", "SSE.Views.HeaderFooterDialog.textItalic": "Italic", "SSE.Views.HeaderFooterDialog.textLeft": "Left", "SSE.Views.HeaderFooterDialog.textMaxError": "The text string you entered is too long. Reduce the number of characters used.", "SSE.Views.HeaderFooterDialog.textNewColor": "<PERSON><PERSON><PERSON> tùy chỉnh", "SSE.Views.HeaderFooterDialog.textOdd": "Odd page", "SSE.Views.HeaderFooterDialog.textPageCount": "Page count", "SSE.Views.HeaderFooterDialog.textPageNum": "Page number", "SSE.Views.HeaderFooterDialog.textPresets": "Presets", "SSE.Views.HeaderFooterDialog.textRight": "Right", "SSE.Views.HeaderFooterDialog.textScale": "Scale with document", "SSE.Views.HeaderFooterDialog.textSheet": "Sheet name", "SSE.Views.HeaderFooterDialog.textStrikeout": "Strikethrough", "SSE.Views.HeaderFooterDialog.textSubscript": "Subscript", "SSE.Views.HeaderFooterDialog.textSuperscript": "Superscript", "SSE.Views.HeaderFooterDialog.textTime": "Time", "SSE.Views.HeaderFooterDialog.textTitle": "Header/Footer settings", "SSE.Views.HeaderFooterDialog.textUnderline": "Underline", "SSE.Views.HeaderFooterDialog.tipFontName": "Font", "SSE.Views.HeaderFooterDialog.tipFontSize": "Font size", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON> thị", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "<PERSON><PERSON><PERSON> kết t<PERSON>i", "SSE.Views.HyperlinkSettingsDialog.strRange": "Phạm vi", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON> t<PERSON>h", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Copy", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Phạm vi đã chọn", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "<PERSON><PERSON><PERSON><PERSON> đầu đề ở đây", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "<PERSON><PERSON><PERSON><PERSON> liên kết ở đây", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Nhập tooltip ở đây", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "<PERSON><PERSON><PERSON> k<PERSON> ng<PERSON>i", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Get link", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Phạm vi dữ liệu nội bộ", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "LỖI! <PERSON>ạm vi ô không hợp lệ", "SSE.Views.HyperlinkSettingsDialog.textNames": "Defined names", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Select data", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Sheets", "SSE.Views.HyperlinkSettingsDialog.textTipText": "<PERSON><PERSON><PERSON> b<PERSON>n <PERSON>ip", "SSE.Views.HyperlinkSettingsDialog.textTitle": "<PERSON><PERSON><PERSON> đặt <PERSON><PERSON>u liên kết", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Trường này phải là một URL có định dạng \"http://www.example.com\"", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "This field is limited to 2083 characters", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "SSE.Views.ImageSettings.strTransparency": "Opacity", "SSE.Views.ImageSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "SSE.Views.ImageSettings.textCrop": "Crop", "SSE.Views.ImageSettings.textCropFill": "Fill", "SSE.Views.ImageSettings.textCropFit": "Fit", "SSE.Views.ImageSettings.textCropToShape": "Crop to shape", "SSE.Views.ImageSettings.textEdit": "Chỉnh sửa", "SSE.Views.ImageSettings.textEditObject": "Chỉnh sửa <PERSON><PERSON> tư<PERSON>", "SSE.Views.ImageSettings.textFlip": "Flip", "SSE.Views.ImageSettings.textFromFile": "Từ file", "SSE.Views.ImageSettings.textFromStorage": "From storage", "SSE.Views.ImageSettings.textFromUrl": "Từ URL", "SSE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON> cao", "SSE.Views.ImageSettings.textHint270": "Rotate 90В° Counterclockwise", "SSE.Views.ImageSettings.textHint90": "Rotate 90В° Clockwise", "SSE.Views.ImageSettings.textHintFlipH": "Flip horizontally", "SSE.Views.ImageSettings.textHintFlipV": "Flip vertically", "SSE.Views.ImageSettings.textInsert": "<PERSON><PERSON> thế <PERSON>nh", "SSE.Views.ImageSettings.textKeepRatio": "Tỷ lệ không đổi", "SSE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON><PERSON> thước mặc định", "SSE.Views.ImageSettings.textRecentlyUsed": "Recently used", "SSE.Views.ImageSettings.textResetCrop": "Reset crop", "SSE.Views.ImageSettings.textRotate90": "Rotate 90В°", "SSE.Views.ImageSettings.textRotation": "Rotation", "SSE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Don't move or size with cells", "SSE.Views.ImageSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON> bản thay thế", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAltTip": "<PERSON><PERSON><PERSON> tả thay thế dưới dạng văn bản thông tin đối tượng trực quan, sẽ được đọc cho những người bị suy giảm thị lực hoặc nhận thức để giúp họ hiểu rõ hơn về những thông tin có trong hình ảnh, autoshape, biểu đồ hoặc bảng.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Flipped", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontally", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Move but don't size with cells", "SSE.Views.ImageSettingsAdvanced.textRotation": "Rotation", "SSE.Views.ImageSettingsAdvanced.textSnap": "Cell snapping", "SSE.Views.ImageSettingsAdvanced.textTitle": "Hình <PERSON>nh - Cài đặt Nâng cao", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Move and size with cells", "SSE.Views.ImageSettingsAdvanced.textVertically": "Vertically", "SSE.Views.ImportFromXmlDialog.textDestination": "Choose, where to place the data", "SSE.Views.ImportFromXmlDialog.textExist": "Existing worksheet", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ImportFromXmlDialog.textNew": "New worksheet", "SSE.Views.ImportFromXmlDialog.textSelectData": "Select data", "SSE.Views.ImportFromXmlDialog.textTitle": "Import data", "SSE.Views.ImportFromXmlDialog.txtEmpty": "This field is required", "SSE.Views.LeftMenu.ariaLeftMenu": "Left menu", "SSE.Views.LeftMenu.tipAbout": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "SSE.Views.LeftMenu.tipChat": "Cha<PERSON>", "SSE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON> lu<PERSON>", "SSE.Views.LeftMenu.tipFile": "File", "SSE.Views.LeftMenu.tipPlugins": "Plugin", "SSE.Views.LeftMenu.tipSearch": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSpellcheck": "Spell checking", "SSE.Views.LeftMenu.tipSupport": "<PERSON><PERSON><PERSON> & Hỗ trợ", "SSE.Views.LeftMenu.txtDeveloper": "CHẾ ĐỘ NHÀ PHÁT TRIỂN", "SSE.Views.LeftMenu.txtEditor": "Spreadsheet Editor", "SSE.Views.LeftMenu.txtLimit": "Limit access", "SSE.Views.LeftMenu.txtTrial": "TRIAL MODE", "SSE.Views.LeftMenu.txtTrialDev": "Trial Developer Mode", "SSE.Views.MacroDialog.textMacro": "Macro name", "SSE.Views.MacroDialog.textTitle": "Assign macro", "SSE.Views.MainSettingsPrint.okButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.MainSettingsPrint.strLandscape": "Nằm ngang", "SSE.Views.MainSettingsPrint.strLeft": "Trái", "SSE.Views.MainSettingsPrint.strMargins": "Lề", "SSE.Views.MainSettingsPrint.strPortrait": "Thẳng đứng", "SSE.Views.MainSettingsPrint.strPrint": "In", "SSE.Views.MainSettingsPrint.strPrintTitles": "Print titles", "SSE.Views.MainSettingsPrint.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strTop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textActualSize": "<PERSON><PERSON><PERSON> th<PERSON> thực", "SSE.Views.MainSettingsPrint.textCustom": "Custom", "SSE.Views.MainSettingsPrint.textCustomOptions": "Custom options", "SSE.Views.MainSettingsPrint.textFitCols": "<PERSON><PERSON><PERSON> cho vừa Tất cả các <PERSON>t trên <PERSON> trang", "SSE.Views.MainSettingsPrint.textFitPage": "<PERSON><PERSON><PERSON> cho vừa Bảng t<PERSON>h trên <PERSON> trang", "SSE.Views.MainSettingsPrint.textFitRows": "<PERSON><PERSON><PERSON> cho vừa Tất cả các <PERSON>ng trên <PERSON> trang", "SSE.Views.MainSettingsPrint.textPageOrientation": "<PERSON><PERSON><PERSON><PERSON> trang", "SSE.Views.MainSettingsPrint.textPageScaling": "Cân chỉnh", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON><PERSON><PERSON> trang", "SSE.Views.MainSettingsPrint.textPrintGrid": "In đường lưới", "SSE.Views.MainSettingsPrint.textPrintHeadings": "In tiêu đề hàng và cột", "SSE.Views.MainSettingsPrint.textRepeat": "Repeat...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Repeat columns at left", "SSE.Views.MainSettingsPrint.textRepeatTop": "Repeat rows at top", "SSE.Views.MainSettingsPrint.textSettings": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>p cho", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Không thể chỉnh sửa các phạm vi được đặt tên hiện tại và hiện thời không thể tạo các phạm vi mới vì một số trong đó đang được chỉnh sửa.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "<PERSON>ên đã định ngh<PERSON>a", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Workbook", "SSE.Views.NamedRangeEditDlg.textDataRange": "Phạm vi dữ liệu", "SSE.Views.NamedRangeEditDlg.textExistName": "LỖI! Phạ<PERSON> vi có tên như vậy đã tồn tại", "SSE.Views.NamedRangeEditDlg.textInvalidName": "<PERSON><PERSON>n phải bắt đầu bằng một chữ cái hoặc một gạch dưới và không được chứa các ký tự không hợp lệ.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "LỖI! <PERSON>ạm vi ô không hợp lệ", "SSE.Views.NamedRangeEditDlg.textIsLocked": "LỖI! Phần tử này đang được chỉnh sửa bởi một người dùng khác.", "SSE.Views.NamedRangeEditDlg.textName": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textReservedName": "Tên bạn đang cố gắng sử dụng đã đư<PERSON><PERSON> tham chiếu trong các công thức ô. <PERSON><PERSON> lòng sử dụng tên khác.", "SSE.Views.NamedRangeEditDlg.textScope": "Phạm vi", "SSE.Views.NamedRangeEditDlg.textSelectData": "<PERSON><PERSON><PERSON> dữ liệu", "SSE.Views.NamedRangeEditDlg.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Chỉnh s<PERSON>a <PERSON>", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "<PERSON><PERSON><PERSON> m<PERSON>i", "SSE.Views.NamedRangePasteDlg.textNames": "<PERSON><PERSON><PERSON> vi được đặt tên", "SSE.Views.NamedRangePasteDlg.txtTitle": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.closeButtonText": "Đ<PERSON><PERSON>", "SSE.Views.NameManagerDlg.guestText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.lockText": "Locked", "SSE.Views.NameManagerDlg.textDataRange": "Phạm vi dữ liệu", "SSE.Views.NameManagerDlg.textDelete": "Xóa", "SSE.Views.NameManagerDlg.textEdit": "Chỉnh sửa", "SSE.Views.NameManagerDlg.textEmpty": "<PERSON><PERSON><PERSON> có phạm vi được đặt tên nào được tạo.<br>Tạo ít nhất một phạm vi được đặt tên và nó sẽ xuất hiện trong trường này.", "SSE.Views.NameManagerDlg.textFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterAll": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.NameManagerDlg.textFilterDefNames": "<PERSON>ên đã định ngh<PERSON>a", "SSE.Views.NameManagerDlg.textFilterSheet": "<PERSON><PERSON><PERSON><PERSON><PERSON> tìm cho trang tính", "SSE.Views.NameManagerDlg.textFilterTableNames": "<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.NameManagerDlg.textFilterWorkbook": "<PERSON><PERSON><PERSON><PERSON><PERSON> tìm cho Workbook", "SSE.Views.NameManagerDlg.textNew": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textnoNames": "<PERSON><PERSON><PERSON><PERSON> tìm thấy phạm vi được đặt tên phù hợp với tiêu chí lọc của bạn.", "SSE.Views.NameManagerDlg.textRanges": "<PERSON><PERSON><PERSON> vi được đặt tên", "SSE.Views.NameManagerDlg.textScope": "Phạm vi", "SSE.Views.NameManagerDlg.textWorkbook": "Workbook", "SSE.Views.NameManagerDlg.tipIsLocked": "Phần tử này đang được chỉnh sửa bởi một người dùng khác.", "SSE.Views.NameManagerDlg.txtTitle": "<PERSON><PERSON><PERSON><PERSON> lý tên", "SSE.Views.NameManagerDlg.warnDelete": "Are you sure you want to delete the name {0}?", "SSE.Views.PageMarginsDialog.textBottom": "Bottom", "SSE.Views.PageMarginsDialog.textCenter": "Center on page", "SSE.Views.PageMarginsDialog.textHor": "Horizontally", "SSE.Views.PageMarginsDialog.textLeft": "Left", "SSE.Views.PageMarginsDialog.textRight": "Right", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "Top", "SSE.Views.PageMarginsDialog.textVert": "Vertically", "SSE.Views.PageMarginsDialog.textWarning": "Warning", "SSE.Views.PageMarginsDialog.warnCheckMargings": "Margins are incorrect", "SSE.Views.ParagraphSettings.strLineHeight": "<PERSON><PERSON><PERSON><PERSON> cách dòng", "SSE.Views.ParagraphSettings.strParagraphSpacing": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch đo<PERSON>n", "SSE.Views.ParagraphSettings.strSpacingAfter": "Sau", "SSE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "SSE.Views.ParagraphSettings.textAt": "Tại", "SSE.Views.ParagraphSettings.textAtLeast": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textExact": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.txtAutoText": "<PERSON><PERSON> động", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "<PERSON><PERSON><PERSON> tab được chỉ định sẽ xuất hiện trong trường này", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Tất cả Drop cap", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON>ch đôi giữa chữ", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Indents", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Trái", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Line spacing", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "After", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Before", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Special", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "By", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Phông chữ", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Thụt lề & Căn chỉnh", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Drop cap nhỏ", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Spacing", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON><PERSON> gi<PERSON>a chữ", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Chỉ số dưới", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Chỉ số trên", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Tab", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON>n chỉnh", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Multiple", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "<PERSON><PERSON><PERSON> c<PERSON>ch trắng", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Tab mặc định", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Exactly", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "First line", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Hanging", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Justified", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(none)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Xóa", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "<PERSON><PERSON><PERSON> tất cả", "SSE.Views.ParagraphSettingsAdvanced.textSet": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Trung tâm", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Trái", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON> trí <PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "<PERSON><PERSON><PERSON><PERSON> văn bản - <PERSON><PERSON>i đặt Nâng cao", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Delete", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "Duplicate", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Edit", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Formula", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Items Name", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "New", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Calculated Items in", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "equals", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "does not end with", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "contains", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "does not contain", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "between", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "not between", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "does not equal", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "is greater than", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "is greater than or equal to", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "is less than", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "is less than or equal to", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "begins with", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "does not begin with", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "ends with", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Show items for which the label:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Show items for which:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Use ? to present any single character", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Use * to present any series of character", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "and", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Label filter", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Value filter", "SSE.Views.PivotGroupDialog.textAuto": "Auto", "SSE.Views.PivotGroupDialog.textBy": "By", "SSE.Views.PivotGroupDialog.textDays": "Days", "SSE.Views.PivotGroupDialog.textEnd": "Ending at", "SSE.Views.PivotGroupDialog.textError": "This field must be a numeric value", "SSE.Views.PivotGroupDialog.textGreaterError": "The end number must be greater than the start number", "SSE.Views.PivotGroupDialog.textHour": "Hours", "SSE.Views.PivotGroupDialog.textMin": "Minutes", "SSE.Views.PivotGroupDialog.textMonth": "Months", "SSE.Views.PivotGroupDialog.textNumDays": "Number of days", "SSE.Views.PivotGroupDialog.textQuart": "Quarters", "SSE.Views.PivotGroupDialog.textSec": "Seconds", "SSE.Views.PivotGroupDialog.textStart": "Starting at", "SSE.Views.PivotGroupDialog.textYear": "Years", "SSE.Views.PivotGroupDialog.txtTitle": "Grouping", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "You can use Calculated Items for basic calculations between different items within a single field", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Insert into formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "<PERSON><PERSON>", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Item name", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Items", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Read more", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Insert Calculated Item in", "SSE.Views.PivotSettings.textAdvanced": "Show advanced settings", "SSE.Views.PivotSettings.textColumns": "Columns", "SSE.Views.PivotSettings.textFields": "Select fields", "SSE.Views.PivotSettings.textFilters": "Filters", "SSE.Views.PivotSettings.textRows": "Rows", "SSE.Views.PivotSettings.textValues": "Values", "SSE.Views.PivotSettings.txtAddColumn": "Add to columns", "SSE.Views.PivotSettings.txtAddFilter": "Add to filters", "SSE.Views.PivotSettings.txtAddRow": "Add to rows", "SSE.Views.PivotSettings.txtAddValues": "Add to values", "SSE.Views.PivotSettings.txtFieldSettings": "Field settings", "SSE.Views.PivotSettings.txtMoveBegin": "Move to beginning", "SSE.Views.PivotSettings.txtMoveColumn": "Move to columns", "SSE.Views.PivotSettings.txtMoveDown": "Move down", "SSE.Views.PivotSettings.txtMoveEnd": "Move to end", "SSE.Views.PivotSettings.txtMoveFilter": "Move to filters", "SSE.Views.PivotSettings.txtMoveRow": "Move to rows", "SSE.Views.PivotSettings.txtMoveUp": "Move up", "SSE.Views.PivotSettings.txtMoveValues": "Move to values", "SSE.Views.PivotSettings.txtRemove": "Remove field", "SSE.Views.PivotSettingsAdvanced.strLayout": "Name and layout", "SSE.Views.PivotSettingsAdvanced.textAlt": "Alternative text", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Description", "SSE.Views.PivotSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart or table.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Title", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Tự động khớp độ rộng cột khi cập nhật", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Data range", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Data source", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Display fields in report filter area", "SSE.Views.PivotSettingsAdvanced.textDown": "Down, then over", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Grand totals", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Field headers", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.PivotSettingsAdvanced.textOver": "Over, then down", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Select data", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Show for columns", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Show field headers for rows and columns", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Show for rows", "SSE.Views.PivotSettingsAdvanced.textTitle": "Pivot Table - Advanced settings", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Report filter fields per column", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Report filter fields per row", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "This field is required", "SSE.Views.PivotSettingsAdvanced.txtName": "Name", "SSE.Views.PivotShowDetailDialog.textDescription": "Choose the field containing the detail you want to show:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Show Detail", "SSE.Views.PivotTable.capBlankRows": "Blank Rows", "SSE.Views.PivotTable.capGrandTotals": "Grand Totals", "SSE.Views.PivotTable.capLayout": "Report Layout", "SSE.Views.PivotTable.capSubtotals": "Subtotals", "SSE.Views.PivotTable.mniBottomSubtotals": "Show all subtotals at bottom of group", "SSE.Views.PivotTable.mniInsertBlankLine": "Insert blank line after each item", "SSE.Views.PivotTable.mniLayoutCompact": "Show in compact form", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Don't repeat all item labels", "SSE.Views.PivotTable.mniLayoutOutline": "Show in outline form", "SSE.Views.PivotTable.mniLayoutRepeat": "Repeat all item labels", "SSE.Views.PivotTable.mniLayoutTabular": "Show in tabular form", "SSE.Views.PivotTable.mniNoSubtotals": "Don't show subtotals", "SSE.Views.PivotTable.mniOffTotals": "Off for rows and columns", "SSE.Views.PivotTable.mniOnColumnsTotals": "On for columns only", "SSE.Views.PivotTable.mniOnRowsTotals": "On for rows only", "SSE.Views.PivotTable.mniOnTotals": "On for rows and columns", "SSE.Views.PivotTable.mniRemoveBlankLine": "Remove blank line after each item", "SSE.Views.PivotTable.mniTopSubtotals": "Show all subtotals at top of group", "SSE.Views.PivotTable.textColBanded": "Banded Columns", "SSE.Views.PivotTable.textColHeader": "Column headers", "SSE.Views.PivotTable.textRowBanded": "Banded Rows", "SSE.Views.PivotTable.textRowHeader": "Row Headers", "SSE.Views.PivotTable.tipCalculatedItems": "Calculated items", "SSE.Views.PivotTable.tipCreatePivot": "Insert Pivot Table", "SSE.Views.PivotTable.tipGrandTotals": "Show or hide grand totals", "SSE.Views.PivotTable.tipRefresh": "Update the information from data source", "SSE.Views.PivotTable.tipRefreshCurrent": "Update the information from data source for the current table", "SSE.Views.PivotTable.tipSelect": "Select entire pivot table", "SSE.Views.PivotTable.tipSubtotals": "Show or hide subtotals", "SSE.Views.PivotTable.txtCalculatedItems": "Calculated Items", "SSE.Views.PivotTable.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.PivotTable.txtCreate": "Insert Table", "SSE.Views.PivotTable.txtExpandEntire": "Expand Entire Field", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Custom", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Dark", "SSE.Views.PivotTable.txtGroupPivot_Light": "Light", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Medium", "SSE.Views.PivotTable.txtPivotTable": "Pivot Table", "SSE.Views.PivotTable.txtRefresh": "Refresh", "SSE.Views.PivotTable.txtRefreshAll": "Refresh all", "SSE.Views.PivotTable.txtSelect": "Select", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Pivot Table Style Dark", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Pivot Table Style Light", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Pivot Table Style Medium", "SSE.Views.PrintSettings.btnDownload": "Save & Download", "SSE.Views.PrintSettings.btnExport": "Save & Export", "SSE.Views.PrintSettings.btnPrint": "Lưu & In", "SSE.Views.PrintSettings.strBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.PrintSettings.strLandscape": "Nằm ngang", "SSE.Views.PrintSettings.strLeft": "Trái", "SSE.Views.PrintSettings.strMargins": "Lề", "SSE.Views.PrintSettings.strPortrait": "Thẳng đứng", "SSE.Views.PrintSettings.strPrint": "In", "SSE.Views.PrintSettings.strPrintTitles": "Print titles", "SSE.Views.PrintSettings.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strShow": "Show", "SSE.Views.PrintSettings.strTop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textActiveSheets": "Active sheets", "SSE.Views.PrintSettings.textActualSize": "<PERSON><PERSON><PERSON> th<PERSON> thực", "SSE.Views.PrintSettings.textAllSheets": "<PERSON><PERSON><PERSON> cả các trang t<PERSON>h", "SSE.Views.PrintSettings.textCurrentSheet": "<PERSON><PERSON> t<PERSON>h hiện tại", "SSE.Views.PrintSettings.textCustom": "Custom", "SSE.Views.PrintSettings.textCustomOptions": "Custom options", "SSE.Views.PrintSettings.textFitCols": "<PERSON><PERSON><PERSON> cho vừa Tất cả các <PERSON>t trên <PERSON> trang", "SSE.Views.PrintSettings.textFitPage": "<PERSON><PERSON><PERSON> cho vừa Bảng t<PERSON>h trên <PERSON> trang", "SSE.Views.PrintSettings.textFitRows": "<PERSON><PERSON><PERSON> cho vừa Tất cả các <PERSON>ng trên <PERSON> trang", "SSE.Views.PrintSettings.textHideDetails": "Ẩn chi tiết", "SSE.Views.PrintSettings.textIgnore": "Ignore print area", "SSE.Views.PrintSettings.textLayout": "Bố cục", "SSE.Views.PrintSettings.textMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintSettings.textMarginsNormal": "Normal", "SSE.Views.PrintSettings.textMarginsWide": "Wide", "SSE.Views.PrintSettings.textPageOrientation": "<PERSON><PERSON><PERSON><PERSON> trang", "SSE.Views.PrintSettings.textPages": "Pages:", "SSE.Views.PrintSettings.textPageScaling": "Cân chỉnh", "SSE.Views.PrintSettings.textPageSize": "<PERSON><PERSON><PERSON> trang", "SSE.Views.PrintSettings.textPrintGrid": "In đường lưới", "SSE.Views.PrintSettings.textPrintHeadings": "In tiêu đề hàng và cột", "SSE.Views.PrintSettings.textPrintRange": "Phạm vi in", "SSE.Views.PrintSettings.textRange": "Range", "SSE.Views.PrintSettings.textRepeat": "Repeat...", "SSE.Views.PrintSettings.textRepeatLeft": "Repeat columns at left", "SSE.Views.PrintSettings.textRepeatTop": "Repeat rows at top", "SSE.Views.PrintSettings.textSelection": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "Cài đặt trang tính", "SSE.Views.PrintSettings.textShowDetails": "<PERSON><PERSON><PERSON> thị chi tiết", "SSE.Views.PrintSettings.textShowGrid": "Show gridlines", "SSE.Views.PrintSettings.textShowHeadings": "Show rows and columns headings", "SSE.Views.PrintSettings.textTitle": "Cài đặt In", "SSE.Views.PrintSettings.textTitlePDF": "PDF settings", "SSE.Views.PrintSettings.textTo": "to", "SSE.Views.PrintSettings.txtMarginsLast": "Last Custom", "SSE.Views.PrintTitlesDialog.textFirstCol": "First column", "SSE.Views.PrintTitlesDialog.textFirstRow": "First row", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Frozen columns", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Frozen rows", "SSE.Views.PrintTitlesDialog.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.PrintTitlesDialog.textLeft": "Repeat columns at left", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Don't repeat", "SSE.Views.PrintTitlesDialog.textRepeat": "Repeat...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Select range", "SSE.Views.PrintTitlesDialog.textTitle": "Print titles", "SSE.Views.PrintTitlesDialog.textTop": "Repeat rows at top", "SSE.Views.PrintWithPreview.txtActiveSheets": "Active sheets", "SSE.Views.PrintWithPreview.txtActualSize": "Actual size", "SSE.Views.PrintWithPreview.txtAllSheets": "All sheets", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Apply to all sheets", "SSE.Views.PrintWithPreview.txtBothSides": "Print on both sides", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "Flip pages on long edge", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "Flip pages on short edge", "SSE.Views.PrintWithPreview.txtBottom": "Bottom", "SSE.Views.PrintWithPreview.txtCopies": "Copies", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Current sheet", "SSE.Views.PrintWithPreview.txtCustom": "Custom", "SSE.Views.PrintWithPreview.txtCustomOptions": "Custom options", "SSE.Views.PrintWithPreview.txtEmptyTable": "There is nothing to print because the table is empty", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "First page number:", "SSE.Views.PrintWithPreview.txtFitCols": "Fit All Columns on One Page", "SSE.Views.PrintWithPreview.txtFitPage": "<PERSON>t Sheet on One Page", "SSE.Views.PrintWithPreview.txtFitRows": "Fit All Rows on One Page", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Gridlines and headings", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Header/footer settings", "SSE.Views.PrintWithPreview.txtIgnore": "Ignore print area", "SSE.Views.PrintWithPreview.txtLandscape": "Landscape", "SSE.Views.PrintWithPreview.txtLeft": "Left", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMarginsLast": "Last Custom", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Normal", "SSE.Views.PrintWithPreview.txtMarginsWide": "Wide", "SSE.Views.PrintWithPreview.txtOf": "of {0}", "SSE.Views.PrintWithPreview.txtOneSide": "Print one sided", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "SSE.Views.PrintWithPreview.txtPage": "Page", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Page number invalid", "SSE.Views.PrintWithPreview.txtPageOrientation": "Page orientation", "SSE.Views.PrintWithPreview.txtPages": "Pages:", "SSE.Views.PrintWithPreview.txtPageSize": "Page size", "SSE.Views.PrintWithPreview.txtPortrait": "Portrait", "SSE.Views.PrintWithPreview.txtPrint": "Print", "SSE.Views.PrintWithPreview.txtPrintGrid": "Print gridlines", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Print row and column headings", "SSE.Views.PrintWithPreview.txtPrintRange": "Print range", "SSE.Views.PrintWithPreview.txtPrintSides": "Print sides", "SSE.Views.PrintWithPreview.txtPrintTitles": "Print titles", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Print to PDF", "SSE.Views.PrintWithPreview.txtRepeat": "Repeat...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Repeat columns at left", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Repeat rows at top", "SSE.Views.PrintWithPreview.txtRight": "Right", "SSE.Views.PrintWithPreview.txtSave": "Save", "SSE.Views.PrintWithPreview.txtScaling": "Sc<PERSON>", "SSE.Views.PrintWithPreview.txtSelection": "Selection", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Settings of sheet", "SSE.Views.PrintWithPreview.txtSheet": "Sheet: {0}", "SSE.Views.PrintWithPreview.txtTo": "to", "SSE.Views.PrintWithPreview.txtTop": "Top", "SSE.Views.ProtectDialog.textExistName": "ERROR! Range with such a title already exists", "SSE.Views.ProtectDialog.textInvalidName": "The range title must begin with a letter and may only contain letters, numbers, and spaces.", "SSE.Views.ProtectDialog.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.ProtectDialog.textSelectData": "Select data", "SSE.Views.ProtectDialog.txtAllow": "Allow all users of this sheet to", "SSE.Views.ProtectDialog.txtAllowDescription": "You can unlock specific ranges for editing.", "SSE.Views.ProtectDialog.txtAllowRanges": "Allow edit ranges", "SSE.Views.ProtectDialog.txtAutofilter": "Use AutoFilter", "SSE.Views.ProtectDialog.txtDelCols": "Delete columns", "SSE.Views.ProtectDialog.txtDelRows": "Delete rows", "SSE.Views.ProtectDialog.txtEmpty": "This field is required", "SSE.Views.ProtectDialog.txtFormatCells": "Format cells", "SSE.Views.ProtectDialog.txtFormatCols": "Format columns", "SSE.Views.ProtectDialog.txtFormatRows": "Format rows", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Confirmation password is not identical", "SSE.Views.ProtectDialog.txtInsCols": "Insert columns", "SSE.Views.ProtectDialog.txtInsHyper": "Insert hyperlink", "SSE.Views.ProtectDialog.txtInsRows": "Insert rows", "SSE.Views.ProtectDialog.txtObjs": "Edit objects", "SSE.Views.ProtectDialog.txtOptional": "optional", "SSE.Views.ProtectDialog.txtPassword": "Password", "SSE.Views.ProtectDialog.txtPivot": "Use PivotTable and PivotChart", "SSE.Views.ProtectDialog.txtProtect": "Protect", "SSE.Views.ProtectDialog.txtRange": "Range", "SSE.Views.ProtectDialog.txtRangeName": "Title", "SSE.Views.ProtectDialog.txtRepeat": "Repeat password", "SSE.Views.ProtectDialog.txtScen": "Edit scenarios", "SSE.Views.ProtectDialog.txtSelLocked": "Select locked cells", "SSE.Views.ProtectDialog.txtSelUnLocked": "Select unlocked cells", "SSE.Views.ProtectDialog.txtSheetDescription": "Prevent unwanted changes from others by limiting their ability to edit.", "SSE.Views.ProtectDialog.txtSheetTitle": "Protect sheet", "SSE.Views.ProtectDialog.txtSort": "Sort", "SSE.Views.ProtectDialog.txtWarning": "Warning: If you lose or forget the password, it cannot be recovered. Please keep it in a safe place.", "SSE.Views.ProtectDialog.txtWBDescription": "To prevent other users from viewing hidden worksheets, adding, moving, deleting, or hiding worksheets and renaming worksheets, you can protect the structure of your workbook with a password.", "SSE.Views.ProtectDialog.txtWBTitle": "Protect workbook structure", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "Anonymous", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Anyone", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Edit", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Denied", "SSE.Views.ProtectedRangesEditDlg.textCanView": "View", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "The range title must begin with a letter and may only contain letters, numbers, and spaces.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Remove", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "Select data", "SSE.Views.ProtectedRangesEditDlg.textYou": "you", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Access to range", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "This field is required", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "Protect", "SSE.Views.ProtectedRangesEditDlg.txtRange": "Range", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "Title", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Only you can edit this range", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Start typing name or email", "SSE.Views.ProtectedRangesManagerDlg.guestText": "Guest", "SSE.Views.ProtectedRangesManagerDlg.lockText": "Locked", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "Delete", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "Edit", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "No protected ranges have been created yet.<br>Create at least one protected range and it will appear in this field.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "Filter", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "All", "SSE.Views.ProtectedRangesManagerDlg.textNew": "New", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Protect sheet", "SSE.Views.ProtectedRangesManagerDlg.textRange": "Range", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "You can restrict editing or viewing ranges to selected people.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "Title", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Access", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Denied", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "Edit", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "Edit range", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "New range", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Protected ranges", "SSE.Views.ProtectedRangesManagerDlg.txtView": "View", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Are you sure you want to delete the protected range {0}?<br>Anyone who has edit access to the spreadsheet will be able to edit content in the range.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Are you sure you want to delete the protected ranges?<br>Anyone who has edit access to the spreadsheet will be able to edit content in those ranges.", "SSE.Views.ProtectRangesDlg.guestText": "Guest", "SSE.Views.ProtectRangesDlg.lockText": "Locked", "SSE.Views.ProtectRangesDlg.textDelete": "Delete", "SSE.Views.ProtectRangesDlg.textEdit": "Edit", "SSE.Views.ProtectRangesDlg.textEmpty": "No ranges allowed for edit.", "SSE.Views.ProtectRangesDlg.textNew": "New", "SSE.Views.ProtectRangesDlg.textProtect": "Protect sheet", "SSE.Views.ProtectRangesDlg.textPwd": "Password", "SSE.Views.ProtectRangesDlg.textRange": "Range", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Ranges unlocked by a password when sheet is protected (this works only for locked cells)", "SSE.Views.ProtectRangesDlg.textTitle": "Title", "SSE.Views.ProtectRangesDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Edit range", "SSE.Views.ProtectRangesDlg.txtNewRange": "New range", "SSE.Views.ProtectRangesDlg.txtNo": "No", "SSE.Views.ProtectRangesDlg.txtTitle": "Allow users to edit ranges", "SSE.Views.ProtectRangesDlg.txtYes": "Yes", "SSE.Views.ProtectRangesDlg.warnDelete": "Are you sure you want to delete the name {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Columns", "SSE.Views.RemoveDuplicatesDialog.textDescription": "To delete duplicate values, select one or more columns that contain duplicates.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "My data has headers", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Select all", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Remove duplicates", "SSE.Views.RightMenu.ariaRightMenu": "Right menu", "SSE.Views.RightMenu.txtCellSettings": "Cell settings", "SSE.Views.RightMenu.txtChartSettings": "<PERSON><PERSON><PERSON><PERSON> lậ<PERSON>", "SSE.Views.RightMenu.txtImageSettings": "<PERSON>ài đặt hình ảnh", "SSE.Views.RightMenu.txtParagraphSettings": "<PERSON>ài đặt văn bản", "SSE.Views.RightMenu.txtPivotSettings": "Pivot Table settings", "SSE.Views.RightMenu.txtSettings": "<PERSON>ài đặt chung", "SSE.Views.RightMenu.txtShapeSettings": "<PERSON>ài đặt hình dạng", "SSE.Views.RightMenu.txtSignatureSettings": "Signature settings", "SSE.Views.RightMenu.txtSlicerSettings": "Slicer settings", "SSE.Views.RightMenu.txtSparklineSettings": "Cài đặt Sparkline", "SSE.Views.RightMenu.txtTableSettings": "<PERSON><PERSON>i đặt bảng", "SSE.Views.RightMenu.txtTextArtSettings": "Cài đặt chữ Nghệ thuật", "SSE.Views.ScaleDialog.textAuto": "Auto", "SSE.Views.ScaleDialog.textError": "The entered value is incorrect.", "SSE.Views.ScaleDialog.textFewPages": "pages", "SSE.Views.ScaleDialog.textFitTo": "Fit to", "SSE.Views.ScaleDialog.textHeight": "Height", "SSE.Views.ScaleDialog.textManyPages": "pages", "SSE.Views.ScaleDialog.textOnePage": "page", "SSE.Views.ScaleDialog.textScaleTo": "Scale to", "SSE.Views.ScaleDialog.textTitle": "Scale settings", "SSE.Views.ScaleDialog.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "<PERSON><PERSON><PERSON> trị lớn nhất cho trường này là {0}", "SSE.Views.SetValueDialog.txtMinText": "<PERSON><PERSON><PERSON> trị nhỏ nhất cho trường này là {0}", "SSE.Views.ShapeSettings.strBackground": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strChange": "Thay đ<PERSON>i Autoshape", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "<PERSON><PERSON> màu", "SSE.Views.ShapeSettings.strForeground": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strPattern": "<PERSON><PERSON> văn", "SSE.Views.ShapeSettings.strShadow": "Show shadow", "SSE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "<PERSON><PERSON> mờ", "SSE.Views.ShapeSettings.strType": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "SSE.Views.ShapeSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị từ thuộc từ 0 pt đến 1584 pt.", "SSE.Views.ShapeSettings.textColor": "<PERSON><PERSON> màu", "SSE.Views.ShapeSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textEditPoints": "Edit points", "SSE.Views.ShapeSettings.textEditShape": "Edit shape", "SSE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON><PERSON>ng hoa văn", "SSE.Views.ShapeSettings.textEyedropper": "Eyedropper", "SSE.Views.ShapeSettings.textFlip": "Flip", "SSE.Views.ShapeSettings.textFromFile": "Từ file", "SSE.Views.ShapeSettings.textFromStorage": "From storage", "SSE.Views.ShapeSettings.textFromUrl": "Từ URL", "SSE.Views.ShapeSettings.textGradient": "Gradient", "SSE.Views.ShapeSettings.textGradientFill": "<PERSON><PERSON> màu <PERSON>", "SSE.Views.ShapeSettings.textHint270": "Rotate 90В° Counterclockwise", "SSE.Views.ShapeSettings.textHint90": "Rotate 90В° Clockwise", "SSE.Views.ShapeSettings.textHintFlipH": "Flip horizontally", "SSE.Views.ShapeSettings.textHintFlipV": "Flip vertically", "SSE.Views.ShapeSettings.textImageTexture": "H<PERSON>nh <PERSON>nh hoặc Texture", "SSE.Views.ShapeSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textMoreColors": "More colors", "SSE.Views.ShapeSettings.textNoFill": "<PERSON><PERSON>ông đổ màu", "SSE.Views.ShapeSettings.textNoShadow": "No Shadow", "SSE.Views.ShapeSettings.textOriginalSize": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> ban đầu", "SSE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON> văn", "SSE.Views.ShapeSettings.textPosition": "Position", "SSE.Views.ShapeSettings.textRadial": "Tỏa tròn", "SSE.Views.ShapeSettings.textRecentlyUsed": "Recently used", "SSE.Views.ShapeSettings.textRotate90": "Rotate 90В°", "SSE.Views.ShapeSettings.textRotation": "Rotation", "SSE.Views.ShapeSettings.textSelectImage": "Select picture", "SSE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textShadow": "Shadow", "SSE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON> d<PERSON>i", "SSE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textTexture": "Từ Texture", "SSE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Add gradient point", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Remove gradient point", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>u", "SSE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON><PERSON> t<PERSON>i màu", "SSE.Views.ShapeSettings.txtGrain": "Thớ gỗ", "SSE.Views.ShapeSettings.txtGranite": "Đá granite", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON>m", "SSE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON> xen", "SSE.Views.ShapeSettings.txtLeather": "Da", "SSE.Views.ShapeSettings.txtNoBorders": "Không đường kẻ", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.ShapeSettings.txtWood": "Gỗ", "SSE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strMargins": "<PERSON><PERSON><PERSON><PERSON> padding cho vă<PERSON> bản", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Don't move or size with cells", "SSE.Views.ShapeSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON> bản thay thế", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "<PERSON><PERSON><PERSON> tả thay thế dưới dạng văn bản thông tin đối tượng trực quan, sẽ được đọc cho những người bị suy giảm thị lực hoặc nhận thức để giúp họ hiểu rõ hơn về những thông tin có trong hình ảnh, autoshape, biểu đồ hoặc bảng.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON> tên", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Tự động khớp", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON><PERSON><PERSON> thước khởi đầu", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON>u khởi đầu", "SSE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Số cột", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "<PERSON><PERSON><PERSON> kết th<PERSON>c", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Phẳng", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Flipped", "SSE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON> cao", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontally", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "<PERSON><PERSON><PERSON> k<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Tỷ lệ không đổi", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Trái", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON> đường kẻ", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Góc 45 độ", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Move but don't size with cells", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Allow text to overflow shape", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Resize shape to fit text", "SSE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Rotation", "SSE.Views.ShapeSettingsAdvanced.textRound": "Tròn", "SSE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Cell snapping", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ch gi<PERSON><PERSON> các c<PERSON>t", "SSE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Text box", "SSE.Views.ShapeSettingsAdvanced.textTitle": "<PERSON><PERSON>nh dạng - <PERSON>ài đặt Nâng cao", "SSE.Views.ShapeSettingsAdvanced.textTop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Move and size with cells", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Vertically", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "<PERSON><PERSON> & <PERSON><PERSON><PERSON> tên", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Warning", "SSE.Views.SignatureSettings.strDelete": "Remove Signature", "SSE.Views.SignatureSettings.strDetails": "Signature details", "SSE.Views.SignatureSettings.strInvalid": "Invalid signatures", "SSE.Views.SignatureSettings.strRequested": "Requested signatures", "SSE.Views.SignatureSettings.strSetup": "Signature setup", "SSE.Views.SignatureSettings.strSign": "Sign", "SSE.Views.SignatureSettings.strSignature": "Signature", "SSE.Views.SignatureSettings.strSigner": "Signer", "SSE.Views.SignatureSettings.strValid": "Valid signatures", "SSE.Views.SignatureSettings.txtContinueEditing": "Edit anyway", "SSE.Views.SignatureSettings.txtEditWarning": "Editing will remove signatures from the spreadsheet.<br>Continue?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Do you want to remove this signature?<br>It can't be undone.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "This spreadsheet needs to be signed.", "SSE.Views.SignatureSettings.txtSigned": "Valid signatures have been added to the spreadsheet. The spreadsheet is protected from editing.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Some of the digital signatures in spreadsheet are invalid or could not be verified. The spreadsheet is protected from editing.", "SSE.Views.SlicerAddDialog.textColumns": "Columns", "SSE.Views.SlicerAddDialog.txtTitle": "Insert slicers", "SSE.Views.SlicerSettings.strHideNoData": "Hide items with no data", "SSE.Views.SlicerSettings.strIndNoData": "Visually indicate items with no data", "SSE.Views.SlicerSettings.strShowDel": "Show items deleted from the data source", "SSE.Views.SlicerSettings.strShowNoData": "Show items with no data last", "SSE.Views.SlicerSettings.strSorting": "Sorting and filtering", "SSE.Views.SlicerSettings.textAdvanced": "Show advanced settings", "SSE.Views.SlicerSettings.textAsc": "Ascending", "SSE.Views.SlicerSettings.textAZ": "A to Z", "SSE.Views.SlicerSettings.textButtons": "Buttons", "SSE.Views.SlicerSettings.textColumns": "Columns", "SSE.Views.SlicerSettings.textDesc": "Descending", "SSE.Views.SlicerSettings.textHeight": "Height", "SSE.Views.SlicerSettings.textHor": "Horizontal", "SSE.Views.SlicerSettings.textKeepRatio": "Constant Proportions", "SSE.Views.SlicerSettings.textLargeSmall": "largest to smallest", "SSE.Views.SlicerSettings.textLock": "Disable resizing or moving", "SSE.Views.SlicerSettings.textNewOld": "newest to oldest", "SSE.Views.SlicerSettings.textOldNew": "oldest to newest", "SSE.Views.SlicerSettings.textPosition": "Position", "SSE.Views.SlicerSettings.textSize": "Size", "SSE.Views.SlicerSettings.textSmallLarge": "smallest to largest", "SSE.Views.SlicerSettings.textStyle": "Style", "SSE.Views.SlicerSettings.textVert": "Vertical", "SSE.Views.SlicerSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "Z to A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Buttons", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Columns", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Height", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Hide items with no data", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Visually indicate items with no data", "SSE.Views.SlicerSettingsAdvanced.strReferences": "References", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Show items deleted from the data source", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Display header", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Show items with no data last", "SSE.Views.SlicerSettingsAdvanced.strSize": "Size", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Sorting & Filtering", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Style", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Style & Size", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Don't move or size with cells", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Alternative text", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Description", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "The alternative text-based representation of the visual object information, which will be read to the people with vision or cognitive impairments to help them better understand what information there is in the image, shape, chart or table.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Title", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Ascending", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A to Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Descending", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Name to use in formulas", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Header", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Constant proportions", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "largest to smallest", "SSE.Views.SlicerSettingsAdvanced.textName": "Name", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "newest to oldest", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "oldest to newest", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Move but don't size with cells", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "smallest to largest", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Cell snapping", "SSE.Views.SlicerSettingsAdvanced.textSort": "Sort", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Source name", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Slicer - Advanced settings", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Move and size with cells", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z to A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "This field is required", "SSE.Views.SortDialog.errorEmpty": "All sort criteria must have a column or row specified.", "SSE.Views.SortDialog.errorMoreOneCol": "More than one column is selected.", "SSE.Views.SortDialog.errorMoreOneRow": "More than one row is selected.", "SSE.Views.SortDialog.errorNotOriginalCol": "The column you selected is not in the original selected range.", "SSE.Views.SortDialog.errorNotOriginalRow": "The row you selected is not in the original selected range.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 is being sorted by the same color more than once.<br>Delete the duplicate sort criteria and try again.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 is being sorted by values more than once.<br>Delete the duplicate sort criteria and try again.", "SSE.Views.SortDialog.textAsc": "Ascending", "SSE.Views.SortDialog.textAuto": "Automatic", "SSE.Views.SortDialog.textAZ": "A to Z", "SSE.Views.SortDialog.textBelow": "Below", "SSE.Views.SortDialog.textBtnCopy": "Copy", "SSE.Views.SortDialog.textBtnDelete": "Delete", "SSE.Views.SortDialog.textBtnNew": "New", "SSE.Views.SortDialog.textCellColor": "Cell color", "SSE.Views.SortDialog.textColumn": "Column", "SSE.Views.SortDialog.textDesc": "Descending", "SSE.Views.SortDialog.textDown": "Move level down", "SSE.Views.SortDialog.textFontColor": "Font color", "SSE.Views.SortDialog.textLeft": "Left", "SSE.Views.SortDialog.textLevels": "Levels", "SSE.Views.SortDialog.textMoreCols": "(More columns...)", "SSE.Views.SortDialog.textMoreRows": "(More rows...)", "SSE.Views.SortDialog.textNone": "None", "SSE.Views.SortDialog.textOptions": "Options", "SSE.Views.SortDialog.textOrder": "Order", "SSE.Views.SortDialog.textRight": "Right", "SSE.Views.SortDialog.textRow": "Row", "SSE.Views.SortDialog.textSort": "Sort on", "SSE.Views.SortDialog.textSortBy": "Sort by", "SSE.Views.SortDialog.textThenBy": "Then by", "SSE.Views.SortDialog.textTop": "Top", "SSE.Views.SortDialog.textUp": "Move level up", "SSE.Views.SortDialog.textValues": "Values", "SSE.Views.SortDialog.textZA": "Z to A", "SSE.Views.SortDialog.txtInvalidRange": "Invalid cells range.", "SSE.Views.SortDialog.txtTitle": "Sort", "SSE.Views.SortFilterDialog.textAsc": "Ascending (A to Z) by", "SSE.Views.SortFilterDialog.textDesc": "Descending (Z to A) by", "SSE.Views.SortFilterDialog.textNoSort": "No sort", "SSE.Views.SortFilterDialog.txtTitle": "Sort", "SSE.Views.SortFilterDialog.txtTitleValue": "Sort by value", "SSE.Views.SortOptionsDialog.textCase": "Case sensitive", "SSE.Views.SortOptionsDialog.textHeaders": "My data has headers", "SSE.Views.SortOptionsDialog.textLeftRight": "Sort left to right", "SSE.Views.SortOptionsDialog.textOrientation": "Orientation", "SSE.Views.SortOptionsDialog.textTitle": "Sort options", "SSE.Views.SortOptionsDialog.textTopBottom": "Sort top to bottom", "SSE.Views.SpecialPasteDialog.textAdd": "Add", "SSE.Views.SpecialPasteDialog.textAll": "All", "SSE.Views.SpecialPasteDialog.textBlanks": "Skip blanks", "SSE.Views.SpecialPasteDialog.textColWidth": "Column widths", "SSE.Views.SpecialPasteDialog.textComments": "Comments", "SSE.Views.SpecialPasteDialog.textDiv": "Divide", "SSE.Views.SpecialPasteDialog.textFFormat": "Formulas & formatting", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formulas & number formats", "SSE.Views.SpecialPasteDialog.textFormats": "Formats", "SSE.Views.SpecialPasteDialog.textFormulas": "Formulas", "SSE.Views.SpecialPasteDialog.textFWidth": "Formulas & column widths", "SSE.Views.SpecialPasteDialog.textMult": "Multiply", "SSE.Views.SpecialPasteDialog.textNone": "None", "SSE.Views.SpecialPasteDialog.textOperation": "Operation", "SSE.Views.SpecialPasteDialog.textPaste": "Paste", "SSE.Views.SpecialPasteDialog.textSub": "Subtract", "SSE.Views.SpecialPasteDialog.textTitle": "Paste special", "SSE.Views.SpecialPasteDialog.textTranspose": "Transpose", "SSE.Views.SpecialPasteDialog.textValues": "Values", "SSE.Views.SpecialPasteDialog.textVFormat": "Values & Formatting", "SSE.Views.SpecialPasteDialog.textVNFormat": "Values & Number formats", "SSE.Views.SpecialPasteDialog.textWBorders": "All except borders", "SSE.Views.Spellcheck.noSuggestions": "No spelling suggestions", "SSE.Views.Spellcheck.textChange": "Change", "SSE.Views.Spellcheck.textChangeAll": "Change all", "SSE.Views.Spellcheck.textIgnore": "Ignore", "SSE.Views.Spellcheck.textIgnoreAll": "Ignore all", "SSE.Views.Spellcheck.txtAddToDictionary": "Add to dictionary", "SSE.Views.Spellcheck.txtClosePanel": "Close spelling", "SSE.Views.Spellcheck.txtComplete": "Spellcheck has been completed", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Dictionary language", "SSE.Views.Spellcheck.txtNextTip": "Go to the next word", "SSE.Views.Spellcheck.txtSpelling": "Spelling", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(<PERSON> chuyển đến cuối)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "Create a copy", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(Create new spreadsheet)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "<PERSON> chuyển trư<PERSON><PERSON> trang tính", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Spreadsheet", "SSE.Views.Statusbar.filteredRecordsText": "{0} trên {1} bản <PERSON><PERSON><PERSON><PERSON> l<PERSON>c", "SSE.Views.Statusbar.filteredText": "<PERSON><PERSON> độ bộ lọc", "SSE.Views.Statusbar.itemAverage": "TRUNG BÌNH", "SSE.Views.Statusbar.itemCount": "Count", "SSE.Views.Statusbar.itemDelete": "Xóa", "SSE.Views.Statusbar.itemHidden": "Ẩn", "SSE.Views.Statusbar.itemHide": "Ẩn", "SSE.Views.Statusbar.itemInsert": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMaximum": "Maximum", "SSE.Views.Statusbar.itemMinimum": "Minimum", "SSE.Views.Statusbar.itemMoveOrCopy": "Move or copy", "SSE.Views.Statusbar.itemProtect": "Protect", "SSE.Views.Statusbar.itemRename": "<PERSON><PERSON><PERSON> tên", "SSE.Views.Statusbar.itemStatus": "Saving status", "SSE.Views.Statusbar.itemSum": "Sum", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemUnProtect": "Unprotect", "SSE.Views.Statusbar.RenameDialog.errNameExists": "<PERSON><PERSON><PERSON> t<PERSON>h với tên như vậy đã tồn tại.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Tên trang tính không đ<PERSON><PERSON><PERSON> chứa các ký tự sau: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "<PERSON><PERSON><PERSON> trang t<PERSON>h", "SSE.Views.Statusbar.selectAllSheets": "Select All Sheets", "SSE.Views.Statusbar.sheetIndexText": "Sheet {0} of {1}", "SSE.Views.Statusbar.textAverage": "TRUNG BÌNH", "SSE.Views.Statusbar.textCount": "COUNT", "SSE.Views.Statusbar.textMax": "Max", "SSE.Views.Statusbar.textMin": "Min", "SSE.Views.Statusbar.textNewColor": "<PERSON><PERSON><PERSON> tùy chỉnh", "SSE.Views.Statusbar.textNoColor": "<PERSON><PERSON><PERSON><PERSON> màu", "SSE.Views.Statusbar.textSum": "SUM", "SSE.Views.Statusbar.tipAddTab": "<PERSON><PERSON><PERSON><PERSON> bảng <PERSON>h", "SSE.Views.Statusbar.tipFirst": "<PERSON><PERSON><PERSON><PERSON> bảng t<PERSON>h đầu tiên", "SSE.Views.Statusbar.tipLast": "<PERSON><PERSON><PERSON><PERSON> bảng t<PERSON>h cuối cùng", "SSE.Views.Statusbar.tipListOfSheets": "List of sheets", "SSE.Views.Statusbar.tipNext": "<PERSON><PERSON><PERSON><PERSON> danh s<PERSON>ch bả<PERSON> t<PERSON> sang phải", "SSE.Views.Statusbar.tipPrev": "<PERSON><PERSON><PERSON><PERSON> danh s<PERSON>ch bả<PERSON> t<PERSON> sang trái", "SSE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON> ph<PERSON>g", "SSE.Views.Statusbar.tipZoomIn": "<PERSON><PERSON><PERSON> to", "SSE.Views.Statusbar.tipZoomOut": "<PERSON>hu nhỏ", "SSE.Views.Statusbar.ungroupSheets": "Ungroup sheets", "SSE.Views.Statusbar.zoomText": "<PERSON>hu phóng {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "<PERSON><PERSON><PERSON><PERSON> thể thực hiện thao tác cho phạm vi ô đã chọn.<br><PERSON>ọn phạm vi dữ liệu thống nhất khác với phạm vi hiện tại và thử lại.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "<PERSON>hông thể hoàn tất thao tác cho phạm vi ô đã chọn.<br>Chọn một phạm vi sao cho cùng trùng hàng đầu của bảng<br> và bảng kết quả chồng chéo lên bảng hiện tại.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "<PERSON>hông thể hoàn tất thao tác cho phạm vi ô đã chọn.<br><PERSON>ọn một phạm vi không bao gồm các bảng khác.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Multi-cell array formulas are not allowed in tables.", "SSE.Views.TableOptionsDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>c", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.TableOptionsDialog.txtInvalidRange": "LỖI! <PERSON>ạm vi ô không hợp lệ", "SSE.Views.TableOptionsDialog.txtNote": "The headers must remain in the same row, and the resulting table range must overlap the original table range.", "SSE.Views.TableOptionsDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "SSE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON> h<PERSON>", "SSE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.insertColumnLeftText": "<PERSON><PERSON><PERSON> c<PERSON>t bên trái", "SSE.Views.TableSettings.insertColumnRightText": "<PERSON><PERSON><PERSON> c<PERSON>t bên ph<PERSON>i", "SSE.Views.TableSettings.insertRowAboveText": "<PERSON><PERSON><PERSON> hàng bên trên", "SSE.Views.TableSettings.insertRowBelowText": "<PERSON><PERSON><PERSON> hàng bên <PERSON>", "SSE.Views.TableSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "SSE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON> toàn bộ cột", "SSE.Views.TableSettings.selectDataText": "<PERSON><PERSON><PERSON> dữ liệu cột", "SSE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.TableSettings.textActions": "Table actions", "SSE.Views.TableSettings.textAdvanced": "Hiển thị Cài đặt Nâng cao", "SSE.Views.TableSettings.textBanded": "<PERSON><PERSON><PERSON> d<PERSON>i màu", "SSE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textConvertRange": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> sang phạm vi", "SSE.Views.TableSettings.textEdit": "Hàng & Cột", "SSE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON><PERSON>ng có template", "SSE.Views.TableSettings.textExistName": "LỖI! Phạ<PERSON> vi có tên như vậy đã tồn tại", "SSE.Views.TableSettings.textFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON> tiên", "SSE.Views.TableSettings.textHeader": "Header", "SSE.Views.TableSettings.textInvalidName": "LỖI! T<PERSON>n bảng không hợp lệ", "SSE.Views.TableSettings.textIsLocked": "Phần tử này đang được chỉnh sửa bởi một người dùng khác.", "SSE.Views.TableSettings.textLast": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.TableSettings.textLongOperation": "<PERSON><PERSON><PERSON> t<PERSON> dài", "SSE.Views.TableSettings.textPivot": "Insert pivot table", "SSE.Views.TableSettings.textRemDuplicates": "Remove duplicates", "SSE.Views.TableSettings.textReservedName": "Tên bạn đang cố gắng sử dụng đã đư<PERSON><PERSON> tham chiếu trong các công thức ô. <PERSON><PERSON> lòng sử dụng tên khác.", "SSE.Views.TableSettings.textResize": "<PERSON><PERSON> đ<PERSON>i kích th<PERSON><PERSON><PERSON> bảng", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "<PERSON><PERSON><PERSON> dữ liệu", "SSE.Views.TableSettings.textSlicer": "Insert slicer", "SSE.Views.TableSettings.textTableName": "<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.TableSettings.textTemplate": "<PERSON><PERSON><PERSON> từ Template", "SSE.Views.TableSettings.textTotal": "<PERSON><PERSON><PERSON> cộng", "SSE.Views.TableSettings.txtGroupTable_Custom": "Custom", "SSE.Views.TableSettings.txtGroupTable_Dark": "Dark", "SSE.Views.TableSettings.txtGroupTable_Light": "Light", "SSE.Views.TableSettings.txtGroupTable_Medium": "Medium", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Table style dark", "SSE.Views.TableSettings.txtTable_TableStyleLight": "Table style light", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "Table style medium", "SSE.Views.TableSettings.warnLongOperation": "<PERSON><PERSON> tác mà bạn sắp thực hiện có thể mất khá nhiều thời gian để hoàn thành.<br>Bạn có chắc là muốn tiếp tục?", "SSE.Views.TableSettingsAdvanced.textAlt": "<PERSON><PERSON><PERSON> bản thay thế", "SSE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textAltTip": "<PERSON><PERSON><PERSON> tả thay thế dưới dạng văn bản thông tin đối tượng trực quan, sẽ được đọc cho những người bị suy giảm thị lực hoặc nhận thức để giúp họ hiểu rõ hơn về những thông tin có trong hình ảnh, autoshape, biểu đồ hoặc bảng.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>", "SSE.Views.TableSettingsAdvanced.textTitle": "Bảng - Cài đặt Nâng cao", "SSE.Views.TextArtSettings.strBackground": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "<PERSON><PERSON> màu", "SSE.Views.TextArtSettings.strForeground": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strPattern": "<PERSON><PERSON> văn", "SSE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "<PERSON><PERSON> mờ", "SSE.Views.TextArtSettings.strType": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "<PERSON><PERSON><PERSON> trị đã nhập không ch<PERSON>h xác.<br><PERSON><PERSON><PERSON><PERSON> một giá trị từ thuộc từ 0 pt đến 1584 pt.", "SSE.Views.TextArtSettings.textColor": "<PERSON><PERSON> màu", "SSE.Views.TextArtSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON><PERSON>ng hoa văn", "SSE.Views.TextArtSettings.textFromFile": "Từ file", "SSE.Views.TextArtSettings.textFromUrl": "Từ URL", "SSE.Views.TextArtSettings.textGradient": "Gradient", "SSE.Views.TextArtSettings.textGradientFill": "<PERSON><PERSON> màu <PERSON>", "SSE.Views.TextArtSettings.textImageTexture": "H<PERSON>nh <PERSON>nh hoặc Texture", "SSE.Views.TextArtSettings.textLinear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textNoFill": "<PERSON><PERSON>ông đổ màu", "SSE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON> văn", "SSE.Views.TextArtSettings.textPosition": "Position", "SSE.Views.TextArtSettings.textRadial": "Tỏa tròn", "SSE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON> d<PERSON>i", "SSE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textTemplate": "Template", "SSE.Views.TextArtSettings.textTexture": "Từ Texture", "SSE.Views.TextArtSettings.textTile": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textTransform": "<PERSON><PERSON><PERSON><PERSON> đổi", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Add gradient point", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Remove gradient point", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>u", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON><PERSON> t<PERSON>i màu", "SSE.Views.TextArtSettings.txtGrain": "Thớ gỗ", "SSE.Views.TextArtSettings.txtGranite": "Đá granite", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON><PERSON><PERSON>m", "SSE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON> xen", "SSE.Views.TextArtSettings.txtLeather": "Da", "SSE.Views.TextArtSettings.txtNoBorders": "Không đường kẻ", "SSE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.TextArtSettings.txtWood": "Gỗ", "SSE.Views.Toolbar.capBtnAddComment": "Add Comment", "SSE.Views.Toolbar.capBtnColorSchemas": "Colors", "SSE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON> lu<PERSON>", "SSE.Views.Toolbar.capBtnInsHeader": "Header & Footer", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON>licer", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Symbol", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageBreak": "Breaks", "SSE.Views.Toolbar.capBtnPageOrient": "Orientation", "SSE.Views.Toolbar.capBtnPageSize": "Size", "SSE.Views.Toolbar.capBtnPrintArea": "Print Area", "SSE.Views.Toolbar.capBtnPrintTitles": "Print Titles", "SSE.Views.Toolbar.capBtnScale": "Scale To Fit", "SSE.Views.Toolbar.capImgAlign": "Align", "SSE.Views.Toolbar.capImgBackward": "Send Backward", "SSE.Views.Toolbar.capImgForward": "Bring Forward", "SSE.Views.Toolbar.capImgGroup": "Group", "SSE.Views.Toolbar.capInsertChart": "<PERSON><PERSON><PERSON><PERSON> đồ", "SSE.Views.Toolbar.capInsertChartRecommend": "Recommended Chart", "SSE.Views.Toolbar.capInsertEquation": "<PERSON><PERSON><PERSON><PERSON> trình", "SSE.Views.Toolbar.capInsertHyperlink": "<PERSON><PERSON><PERSON> li<PERSON> k<PERSON>", "SSE.Views.Toolbar.capInsertImage": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertShape": "<PERSON><PERSON><PERSON> d<PERSON>", "SSE.Views.Toolbar.capInsertSpark": "Sparkline", "SSE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertText": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "SSE.Views.Toolbar.capInsertTextart": "Text Art", "SSE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniCapitalizeWords": "Capitalize Each Word", "SSE.Views.Toolbar.mniImageFromFile": "<PERSON><PERSON><PERSON> từ file", "SSE.Views.Toolbar.mniImageFromStorage": "Image from Storage", "SSE.Views.Toolbar.mniImageFromUrl": "<PERSON><PERSON><PERSON> từ URL", "SSE.Views.Toolbar.mniLowerCase": "lowercase", "SSE.Views.Toolbar.mniSentenceCase": "Sentence case.", "SSE.Views.Toolbar.mniToggleCase": "tOGGLE cASE", "SSE.Views.Toolbar.mniUpperCase": "UPPERCASE", "SSE.Views.Toolbar.textAddPrintArea": "Add to print area", "SSE.Views.Toolbar.textAlignBottom": "<PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.Toolbar.textAlignCenter": "<PERSON><PERSON><PERSON> trung tâm", "SSE.Views.Toolbar.textAlignJust": "<PERSON><PERSON> đ<PERSON>u", "SSE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.Toolbar.textAlignMiddle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignRight": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignTop": "<PERSON><PERSON><PERSON> trên c<PERSON>ng", "SSE.Views.Toolbar.textAllBorders": "<PERSON><PERSON><PERSON> cả viền", "SSE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "SSE.Views.Toolbar.textAuto": "Auto", "SSE.Views.Toolbar.textAutoColor": "Automatic", "SSE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "SSE.Views.Toolbar.textBlackHeart": "Black Heart Suit", "SSE.Views.Toolbar.textBold": "Đậm", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON><PERSON> viền", "SSE.Views.Toolbar.textBordersStyle": "<PERSON><PERSON><PERSON> viền", "SSE.Views.Toolbar.textBottom": "Bottom: ", "SSE.Views.Toolbar.textBottomBorders": "<PERSON><PERSON><PERSON><PERSON> viền dư<PERSON>i cùng", "SSE.Views.Toolbar.textBullet": "Bullet", "SSE.Views.Toolbar.textCellAlign": "Format cell alignment", "SSE.Views.Toolbar.textCenterBorders": "<PERSON><PERSON><PERSON><PERSON> viền dọc bên trong", "SSE.Views.Toolbar.textClearPrintArea": "Clear print area", "SSE.Views.Toolbar.textClearRule": "Clear rules", "SSE.Views.Toolbar.textClockwise": "<PERSON><PERSON><PERSON> theo chiều kim đồng hồ", "SSE.Views.Toolbar.textColorScales": "Color scales", "SSE.Views.Toolbar.textCopyright": "Copyright Sign", "SSE.Views.Toolbar.textCounterCw": "<PERSON><PERSON><PERSON><PERSON><PERSON> chiều kim đồng hồ", "SSE.Views.Toolbar.textCustom": "Custom", "SSE.Views.Toolbar.textDataBars": "Data Bars", "SSE.Views.Toolbar.textDegree": "Degree Sign", "SSE.Views.Toolbar.textDelLeft": "<PERSON><PERSON><PERSON><PERSON> ô sang trái", "SSE.Views.Toolbar.textDelPageBreak": "Remove page break", "SSE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "SSE.Views.Toolbar.textDelUp": "<PERSON><PERSON><PERSON><PERSON> ô lên trên", "SSE.Views.Toolbar.textDiagDownBorder": "<PERSON><PERSON><PERSON><PERSON> viền đường chéo xuống", "SSE.Views.Toolbar.textDiagUpBorder": "<PERSON><PERSON><PERSON><PERSON> viền đường chéo lên", "SSE.Views.Toolbar.textDivision": "Division Sign", "SSE.Views.Toolbar.textDollar": "Dollar Sign", "SSE.Views.Toolbar.textDone": "Done", "SSE.Views.Toolbar.textDown": "Down", "SSE.Views.Toolbar.textEditVA": "Edit Visible Area", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON><PERSON> bộ cột", "SSE.Views.Toolbar.textEntireRow": "<PERSON><PERSON><PERSON> bộ hàng", "SSE.Views.Toolbar.textEuro": "Euro Sign", "SSE.Views.Toolbar.textFewPages": "pages", "SSE.Views.Toolbar.textFillLeft": "Left", "SSE.Views.Toolbar.textFillRight": "Right", "SSE.Views.Toolbar.textFormatCellFill": "Format cell fill", "SSE.Views.Toolbar.textGreaterEqual": "Greater Than Or Equal To", "SSE.Views.Toolbar.textHeight": "Height", "SSE.Views.Toolbar.textHideVA": "Hide Visible Area", "SSE.Views.Toolbar.textHorizontal": "<PERSON><PERSON><PERSON> b<PERSON>n ngang", "SSE.Views.Toolbar.textInfinity": "Infinity", "SSE.Views.Toolbar.textInsDown": "<PERSON><PERSON><PERSON><PERSON> ô xuống dưới", "SSE.Views.Toolbar.textInsideBorders": "<PERSON><PERSON><PERSON><PERSON> viền trong", "SSE.Views.Toolbar.textInsPageBreak": "Insert page break", "SSE.Views.Toolbar.textInsRight": "<PERSON><PERSON><PERSON><PERSON> ô sang phải", "SSE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textItems": "Items", "SSE.Views.Toolbar.textLandscape": "Landscape", "SSE.Views.Toolbar.textLeft": "Left: ", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON><PERSON><PERSON> viền bên trái", "SSE.Views.Toolbar.textLessEqual": "Less Than Or Equal To", "SSE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "SSE.Views.Toolbar.textManageRule": "Manage rules", "SSE.Views.Toolbar.textManyPages": "pages", "SSE.Views.Toolbar.textMarginsLast": "Last Custom", "SSE.Views.Toolbar.textMarginsNarrow": "<PERSON>rrow", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "Wide", "SSE.Views.Toolbar.textMiddleBorders": "<PERSON><PERSON><PERSON><PERSON> viền ngang bên trong", "SSE.Views.Toolbar.textMoreBorders": "More borders", "SSE.Views.Toolbar.textMoreFormats": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> dạng", "SSE.Views.Toolbar.textMorePages": "More pages", "SSE.Views.Toolbar.textMoreSymbols": "More symbols", "SSE.Views.Toolbar.textNewColor": "<PERSON><PERSON><PERSON> tùy chỉnh", "SSE.Views.Toolbar.textNewRule": "New rule", "SSE.Views.Toolbar.textNoBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "SSE.Views.Toolbar.textNotEqualTo": "Not Equal To", "SSE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "SSE.Views.Toolbar.textOnePage": "page", "SSE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "SSE.Views.Toolbar.textOutBorders": "<PERSON><PERSON><PERSON><PERSON> viền ngoài", "SSE.Views.Toolbar.textPageMarginsCustom": "Custom margins", "SSE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "SSE.Views.Toolbar.textPortrait": "Portrait", "SSE.Views.Toolbar.textPrint": "In", "SSE.Views.Toolbar.textPrintGridlines": "Print Gridlines", "SSE.Views.Toolbar.textPrintHeadings": "Print Headings", "SSE.Views.Toolbar.textPrintOptions": "Cài đặt In", "SSE.Views.Toolbar.textRegistered": "Registered Sign", "SSE.Views.Toolbar.textResetPageBreak": "Reset all page breaks", "SSE.Views.Toolbar.textRight": "Right: ", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON><PERSON><PERSON> viền phải", "SSE.Views.Toolbar.textRotateDown": "<PERSON><PERSON><PERSON> v<PERSON>n bản x<PERSON>", "SSE.Views.Toolbar.textRotateUp": "<PERSON><PERSON><PERSON> v<PERSON>n bản lên", "SSE.Views.Toolbar.textRtlSheet": "Sheet Right-to-left", "SSE.Views.Toolbar.textScale": "Scale", "SSE.Views.Toolbar.textScaleCustom": "Custom", "SSE.Views.Toolbar.textSection": "Section Sign", "SSE.Views.Toolbar.textSelection": "From current selection", "SSE.Views.Toolbar.textSeries": "Series", "SSE.Views.Toolbar.textSetPrintArea": "Set print area", "SSE.Views.Toolbar.textShapesCombine": "Combine", "SSE.Views.Toolbar.textShapesFragment": "Fragment", "SSE.Views.Toolbar.textShapesIntersect": "Intersect", "SSE.Views.Toolbar.textShapesSubstract": "Subtract", "SSE.Views.Toolbar.textShapesUnion": "Union", "SSE.Views.Toolbar.textShowVA": "Show Visible Area", "SSE.Views.Toolbar.textSmile": "White Smiling Face", "SSE.Views.Toolbar.textSquareRoot": "Square Root", "SSE.Views.Toolbar.textStrikeout": "Strikethrough", "SSE.Views.Toolbar.textSubscript": "Subscript", "SSE.Views.Toolbar.textSubSuperscript": "Subscript/Superscript", "SSE.Views.Toolbar.textSuperscript": "Superscript", "SSE.Views.Toolbar.textTabCollaboration": "Collaboration", "SSE.Views.Toolbar.textTabData": "Data", "SSE.Views.Toolbar.textTabDraw": "Draw", "SSE.Views.Toolbar.textTabFile": "File", "SSE.Views.Toolbar.textTabFormula": "Formula", "SSE.Views.Toolbar.textTabHome": "Trang chủ", "SSE.Views.Toolbar.textTabInsert": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabLayout": "Layout", "SSE.Views.Toolbar.textTabProtect": "Protection", "SSE.Views.Toolbar.textTabView": "View", "SSE.Views.Toolbar.textThisPivot": "From this pivot", "SSE.Views.Toolbar.textThisSheet": "From this worksheet", "SSE.Views.Toolbar.textThisTable": "From this table", "SSE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTop": "Top: ", "SSE.Views.Toolbar.textTopBorders": "<PERSON><PERSON><PERSON><PERSON> viền trên cùng", "SSE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "SSE.Views.Toolbar.textUnderline": "G<PERSON><PERSON> chân", "SSE.Views.Toolbar.textUp": "Up", "SSE.Views.Toolbar.textVertical": "Vertical text", "SSE.Views.Toolbar.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textYen": "Yen Sign", "SSE.Views.Toolbar.textZoom": "<PERSON><PERSON> ph<PERSON>g", "SSE.Views.Toolbar.tipAlignBottom": "<PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.Toolbar.tipAlignCenter": "<PERSON><PERSON><PERSON> trung tâm", "SSE.Views.Toolbar.tipAlignJust": "<PERSON><PERSON> đ<PERSON>u", "SSE.Views.Toolbar.tipAlignLeft": "<PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.Toolbar.tipAlignMiddle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignRight": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignTop": "<PERSON><PERSON><PERSON> trên c<PERSON>ng", "SSE.Views.Toolbar.tipAutofilter": "Sắp xếp v<PERSON>", "SSE.Views.Toolbar.tipBack": "Quay lại", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "SSE.Views.Toolbar.tipCellStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipChangeCase": "Change case", "SSE.Views.Toolbar.tipChangeChart": "Change chart type", "SSE.Views.Toolbar.tipClearStyle": "Xóa", "SSE.Views.Toolbar.tipColorSchemas": "<PERSON>hay đ<PERSON>i <PERSON> màu", "SSE.Views.Toolbar.tipCondFormat": "Conditional formatting", "SSE.Views.Toolbar.tipCopy": "Sao chép", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON>o ch<PERSON>p kiểu", "SSE.Views.Toolbar.tipCut": "Cut", "SSE.Views.Toolbar.tipDecDecimal": "<PERSON><PERSON><PERSON><PERSON> thập phân", "SSE.Views.Toolbar.tipDecFont": "Giảm cỡ chữ", "SSE.Views.Toolbar.tipDeleteOpt": "Xóa Ô", "SSE.Views.Toolbar.tipDigStyleAccounting": "<PERSON><PERSON><PERSON> k<PERSON> toán", "SSE.Views.Toolbar.tipDigStyleComma": "Comma style", "SSE.Views.Toolbar.tipDigStyleCurrency": "<PERSON><PERSON><PERSON> ti<PERSON>n tệ", "SSE.Views.Toolbar.tipDigStylePercent": "<PERSON><PERSON><PERSON> ph<PERSON>n trăm", "SSE.Views.Toolbar.tipEditChart": "Chỉnh sửa biểu đồ", "SSE.Views.Toolbar.tipEditChartData": "Select data", "SSE.Views.Toolbar.tipEditChartType": "Change chart type", "SSE.Views.Toolbar.tipEditHeader": "Edit header or footer", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON><PERSON> chữ", "SSE.Views.Toolbar.tipFontName": "Phông chữ", "SSE.Views.Toolbar.tipFontSize": "Cỡ chữ", "SSE.Views.Toolbar.tipHAlighOle": "Horizontal align", "SSE.Views.Toolbar.tipImgAlign": "Align objects", "SSE.Views.Toolbar.tipImgGroup": "Group objects", "SSE.Views.Toolbar.tipIncDecimal": "<PERSON><PERSON><PERSON> thập phân", "SSE.Views.Toolbar.tipIncFont": "<PERSON><PERSON><PERSON> kích thước phông chữ", "SSE.Views.Toolbar.tipInsertChart": "<PERSON><PERSON><PERSON> bi<PERSON>u đồ", "SSE.Views.Toolbar.tipInsertChartRecommend": "Insert recommended chart", "SSE.Views.Toolbar.tipInsertChartSpark": "<PERSON><PERSON>n biểu đồ hoặc Sparkline", "SSE.Views.Toolbar.tipInsertEquation": "<PERSON><PERSON><PERSON> ph<PERSON><PERSON><PERSON> trình", "SSE.Views.Toolbar.tipInsertHorizontalText": "Insert horizontal text box", "SSE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON> siêu liên kết", "SSE.Views.Toolbar.tipInsertImage": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertOpt": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertShape": "Chèn Autoshape", "SSE.Views.Toolbar.tipInsertSlicer": "Insert slicer", "SSE.Views.Toolbar.tipInsertSmartArt": "Insert SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Insert sparkline", "SSE.Views.Toolbar.tipInsertSymbol": "Insert symbol", "SSE.Views.Toolbar.tipInsertTable": "Insert table", "SSE.Views.Toolbar.tipInsertText": "<PERSON><PERSON><PERSON> v<PERSON><PERSON> b<PERSON>n", "SSE.Views.Toolbar.tipInsertTextart": "<PERSON><PERSON><PERSON> chữ nghệ thuật", "SSE.Views.Toolbar.tipInsertVerticalText": "Insert vertical text box", "SSE.Views.Toolbar.tipMerge": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipNone": "None", "SSE.Views.Toolbar.tipNumFormat": "<PERSON><PERSON><PERSON> dạng số", "SSE.Views.Toolbar.tipPageBreak": "Add a break where you want the next page to begin in the printed copy", "SSE.Views.Toolbar.tipPageMargins": "Page margins", "SSE.Views.Toolbar.tipPageOrient": "Page orientation", "SSE.Views.Toolbar.tipPageSize": "Page size", "SSE.Views.Toolbar.tipPaste": "Dán", "SSE.Views.Toolbar.tipPrColor": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrint": "In", "SSE.Views.Toolbar.tipPrintArea": "Print area", "SSE.Views.Toolbar.tipPrintQuick": "Quick print", "SSE.Views.Toolbar.tipPrintTitles": "Print titles", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipReplace": "Replace", "SSE.Views.Toolbar.tipRtlSheet": "Switch the sheet direction so that the first column is on the right side", "SSE.Views.Toolbar.tipSave": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSaveCoauth": "<PERSON><PERSON><PERSON> thay đổi của bạn để những người dùng khác thấy chúng.", "SSE.Views.Toolbar.tipScale": "Scale to fit", "SSE.Views.Toolbar.tipSelectAll": "Select all", "SSE.Views.Toolbar.tipSendBackward": "Send backward", "SSE.Views.Toolbar.tipSendForward": "Bring forward", "SSE.Views.Toolbar.tipShapesMerge": "Merge shapes", "SSE.Views.Toolbar.tipSynchronize": "Tài liệu đã được thay đổi bởi một người dùng khác. <PERSON><PERSON> lòng nhấp để lưu thay đổi của bạn và tải lại các cập nhật.", "SSE.Views.Toolbar.tipTextFormatting": "More text formatting tools", "SSE.Views.Toolbar.tipTextOrientation": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipVAlighOle": "Vertical align", "SSE.Views.Toolbar.tipVisibleArea": "Visible area", "SSE.Views.Toolbar.tipWrap": "<PERSON><PERSON><PERSON> dòng", "SSE.Views.Toolbar.txtAccounting": "<PERSON><PERSON> toán", "SSE.Views.Toolbar.txtAdditional": "<PERSON><PERSON> sung", "SSE.Views.Toolbar.txtAscending": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAutosumTip": "Summation", "SSE.Views.Toolbar.txtCellStyle": "Cell Style", "SSE.Views.Toolbar.txtClearAll": "<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.Toolbar.txtClearComments": "<PERSON><PERSON><PERSON> lu<PERSON>", "SSE.Views.Toolbar.txtClearFilter": "Xóa bộ lọc", "SSE.Views.Toolbar.txtClearFormat": "<PERSON><PERSON><PERSON> d<PERSON>ng", "SSE.Views.Toolbar.txtClearFormula": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearHyper": "<PERSON><PERSON><PERSON> li<PERSON> k<PERSON>", "SSE.Views.Toolbar.txtClearText": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.Toolbar.txtCustom": "Tuỳ chỉnh", "SSE.Views.Toolbar.txtDate": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDateLong": "Long Date", "SSE.Views.Toolbar.txtDateShort": "Short Date", "SSE.Views.Toolbar.txtDateTime": "Ngày & Giờ", "SSE.Views.Toolbar.txtDescending": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDollar": "$ Đô la", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "<PERSON><PERSON><PERSON> th<PERSON>a", "SSE.Views.Toolbar.txtFillNum": "Fill", "SSE.Views.Toolbar.txtFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtFormula": "<PERSON><PERSON><PERSON> h<PERSON> s<PERSON>", "SSE.Views.Toolbar.txtFraction": "<PERSON><PERSON> số", "SSE.Views.Toolbar.txtFranc": "CHF franc <PERSON>", "SSE.Views.Toolbar.txtGeneral": "<PERSON><PERSON><PERSON> quát", "SSE.Views.Toolbar.txtInteger": "<PERSON><PERSON> ng<PERSON>ên", "SSE.Views.Toolbar.txtManageRange": "<PERSON><PERSON><PERSON><PERSON> lý tên", "SSE.Views.Toolbar.txtMergeAcross": "<PERSON><PERSON><PERSON> hợ<PERSON> ô sang cột & không canh giữa", "SSE.Views.Toolbar.txtMergeCells": "<PERSON><PERSON><PERSON> nhi<PERSON>u ô và không canh giữa", "SSE.Views.Toolbar.txtMergeCenter": "<PERSON><PERSON><PERSON> h<PERSON> & <PERSON><PERSON><PERSON> g<PERSON>", "SSE.Views.Toolbar.txtNamedRange": "<PERSON><PERSON><PERSON> vi được đặt tên", "SSE.Views.Toolbar.txtNewRange": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtNoBorders": "<PERSON><PERSON><PERSON><PERSON> viền", "SSE.Views.Toolbar.txtNumber": "Số", "SSE.Views.Toolbar.txtPasteRange": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtPercentage": "<PERSON><PERSON><PERSON> tr<PERSON>m", "SSE.Views.Toolbar.txtPound": "£ Bảng", "SSE.Views.Toolbar.txtRouble": "₽ <PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScientific": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSearch": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSort": "<PERSON><PERSON><PERSON>p", "SSE.Views.Toolbar.txtSortAZ": "<PERSON><PERSON><PERSON> xếp tăng d<PERSON>n", "SSE.Views.Toolbar.txtSortZA": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>", "SSE.Views.Toolbar.txtSpecial": "Đặc biệt", "SSE.Views.Toolbar.txtTableTemplate": "<PERSON><PERSON><PERSON> d<PERSON> n<PERSON>ư Template <PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtText": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtTime": "<PERSON><PERSON><PERSON><PERSON> gian", "SSE.Views.Toolbar.txtUnmerge": "Bỏ gộp ô", "SSE.Views.Toolbar.txtYen": "¥ Yên", "SSE.Views.Top10FilterDialog.textType": "<PERSON><PERSON><PERSON> thị", "SSE.Views.Top10FilterDialog.txtBottom": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>ng", "SSE.Views.Top10FilterDialog.txtBy": "by", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtPercent": "<PERSON><PERSON><PERSON> tr<PERSON>m", "SSE.Views.Top10FilterDialog.txtSum": "Sum", "SSE.Views.Top10FilterDialog.txtTitle": "<PERSON><PERSON> động l<PERSON> Tốp 10", "SSE.Views.Top10FilterDialog.txtTop": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtValueTitle": "Top 10 filter", "SSE.Views.ValueFieldSettingsDialog.textNext": "(next)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(previous)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Value field settings", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Average", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Base field", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Base item", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 of %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Count", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Count numbers", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Custom name", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Difference from", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Index", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Max", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "No calculation", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "% của", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "% khác biệt so với", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "% của cột", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% of grand total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% of parent total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% of parent column total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% of parent row total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% running total in", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "% of row", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Product", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Rank smallest to largest", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Rank largest to smallest", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Running total in", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Show values as", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Source name:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Sum", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Summarize value field by", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "Close", "SSE.Views.ViewManagerDlg.guestText": "Guest", "SSE.Views.ViewManagerDlg.lockText": "Locked", "SSE.Views.ViewManagerDlg.textDelete": "Delete", "SSE.Views.ViewManagerDlg.textDuplicate": "Duplicate", "SSE.Views.ViewManagerDlg.textEmpty": "No views have been created yet.", "SSE.Views.ViewManagerDlg.textGoTo": "Go to view", "SSE.Views.ViewManagerDlg.textLongName": "Enter a name that is less than 128 characters.", "SSE.Views.ViewManagerDlg.textNew": "New", "SSE.Views.ViewManagerDlg.textRename": "<PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textRenameError": "View name must not be empty.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Rename view", "SSE.Views.ViewManagerDlg.textViews": "Sheet views", "SSE.Views.ViewManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.ViewManagerDlg.txtTitle": "Sheet view manager", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Are you sure you want to delete this sheet view?", "SSE.Views.ViewManagerDlg.warnDeleteView": "You are trying to delete the currently enabled view '%1'.<br>Close this view and delete it?", "SSE.Views.ViewTab.capBtnFreeze": "Freeze Panes", "SSE.Views.ViewTab.capBtnSheetView": "Sheet View", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Always Show Toolbar", "SSE.Views.ViewTab.textClose": "Close", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Combine Sheet and Status Bars", "SSE.Views.ViewTab.textCreate": "New", "SSE.Views.ViewTab.textDefault": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textFill": "Fill", "SSE.Views.ViewTab.textFormula": "Formula Bar", "SSE.Views.ViewTab.textFreezeCol": "Freeze first column", "SSE.Views.ViewTab.textFreezeRow": "Freeze top row", "SSE.Views.ViewTab.textGridlines": "Gridlines", "SSE.Views.ViewTab.textHeadings": "Headings", "SSE.Views.ViewTab.textInterfaceTheme": "Interface Theme", "SSE.Views.ViewTab.textLeftMenu": "Left Panel", "SSE.Views.ViewTab.textLine": "Line", "SSE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "View manager", "SSE.Views.ViewTab.textRightMenu": "Right Panel", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Show frozen panes shadow", "SSE.Views.ViewTab.textTabStyle": "Tab style", "SSE.Views.ViewTab.textUnFreeze": "Unfreeze panes", "SSE.Views.ViewTab.textZeros": "Show Zeros", "SSE.Views.ViewTab.textZoom": "Zoom", "SSE.Views.ViewTab.tipClose": "Close sheet view", "SSE.Views.ViewTab.tipCreate": "Create sheet view", "SSE.Views.ViewTab.tipFreeze": "Freeze panes", "SSE.Views.ViewTab.tipInterfaceTheme": "Interface theme", "SSE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipSheetView": "Sheet view", "SSE.Views.ViewTab.tipViewNormal": "See your document in Normal view", "SSE.Views.ViewTab.tipViewPageBreak": "See where the page breaks will appear when your document is printed", "SSE.Views.ViewTab.txtViewNormal": "Normal", "SSE.Views.ViewTab.txtViewPageBreak": "Page Break Preview", "SSE.Views.WatchDialog.closeButtonText": "Close", "SSE.Views.WatchDialog.textAdd": "Add watch", "SSE.Views.WatchDialog.textBook": "Book", "SSE.Views.WatchDialog.textCell": "Cell", "SSE.Views.WatchDialog.textDelete": "Delete watch", "SSE.Views.WatchDialog.textDeleteAll": "Delete all", "SSE.Views.WatchDialog.textFormula": "Formula", "SSE.Views.WatchDialog.textName": "Name", "SSE.Views.WatchDialog.textSheet": "Sheet", "SSE.Views.WatchDialog.textValue": "Value", "SSE.Views.WatchDialog.txtTitle": "Watch window", "SSE.Views.WBProtection.hintAllowRanges": "Allow edit ranges", "SSE.Views.WBProtection.hintProtectRange": "Protect range", "SSE.Views.WBProtection.hintProtectSheet": "Protect sheet", "SSE.Views.WBProtection.hintProtectWB": "Protect workbook", "SSE.Views.WBProtection.txtAllowRanges": "Allow edit ranges", "SSE.Views.WBProtection.txtHiddenFormula": "Hidden Formulas", "SSE.Views.WBProtection.txtLockedCell": "Locked Cell", "SSE.Views.WBProtection.txtLockedShape": "<PERSON><PERSON><PERSON> Locked", "SSE.Views.WBProtection.txtLockedText": "Lock Text", "SSE.Views.WBProtection.txtProtectRange": "Protect Range", "SSE.Views.WBProtection.txtProtectSheet": "Protect Sheet", "SSE.Views.WBProtection.txtProtectWB": "Protect workbook", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Enter a password to unprotect sheet", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Unprotect sheet", "SSE.Views.WBProtection.txtWBUnlockDescription": "Enter a password to unprotect workbook", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}