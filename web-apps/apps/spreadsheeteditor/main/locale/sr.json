{"cancelButtonText": "Ot<PERSON>ži", "Common.Controllers.Chat.notcriticalErrorTitle": "Upozorenje ", "Common.Controllers.Desktop.hintBtnHome": "Prikaži Glavni prozor", "Common.Controllers.Desktop.itemCreateFromTemplate": "Kreiraj iz šablona", "Common.Controllers.History.notcriticalErrorTitle": "Upozorenje ", "Common.Controllers.History.txtErrorLoadHistory": "Učitavanje istorije nije uspelo", "Common.Controllers.Plugins.helpUseMacros": "Pronađite dugme <PERSON> ovde", "Common.Controllers.Plugins.helpUseMacrosHeader": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Dodaci su uspešno instalirani. Možete im pristupiti ovde.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> je uspešno instaliran. Možete pristupiti svim pozadinskim dodacima ovde.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Pokreni instalirane do<PERSON>ke", "Common.Controllers.Plugins.textRunPlugin": "Pokreni dodatak", "Common.define.chartData.textArea": "Oblast", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON><PERSON><PERSON> ", "Common.define.chartData.textAreaStackedPer": "100% <PERSON><PERSON><PERSON><PERSON> ", "Common.define.chartData.textBar": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textBarNormal": "Grupisana kolona", "Common.define.chartData.textBarNormal3d": "3-<PERSON> Klasterisana kolo<PERSON>", "Common.define.chartData.textBarNormal3dPerspective": "3-<PERSON> kolona", "Common.define.chartData.textBarStacked": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textBarStacked3d": "3-<PERSON> Nagomilana <PERSON>", "Common.define.chartData.textBarStackedPer": "100% <PERSON><PERSON><PERSON><PERSON> kolona", "Common.define.chartData.textBarStackedPer3d": "3-D 100% <PERSON><PERSON><PERSON><PERSON> kolona", "Common.define.chartData.textCharts": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textColumn": "Kolona", "Common.define.chartData.textColumnSpark": "Kolona", "Common.define.chartData.textCombo": "Kombinacija", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> - grupisana kolona", "Common.define.chartData.textComboBarLine": "Grupisana kolona - linije", "Common.define.chartData.textComboBarLineSecondary": "Grupisana kolona - linije na sekundarnoj osi", "Common.define.chartData.textComboCustom": "Prilagođena kombinacija", "Common.define.chartData.textDoughnut": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "Grupisana traka", "Common.define.chartData.textHBarNormal3d": "3-<PERSON> Klasterisana traka", "Common.define.chartData.textHBarStacked": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>a", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> Nagomilana traka", "Common.define.chartData.textHBarStackedPer": "100% Na<PERSON><PERSON>lana traka", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% <PERSON><PERSON><PERSON><PERSON> traka", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "3-D linija", "Common.define.chartData.textLineMarker": "<PERSON><PERSON>", "Common.define.chartData.textLineSpark": "<PERSON><PERSON>", "Common.define.chartData.textLineStacked": "<PERSON><PERSON><PERSON><PERSON> lini<PERSON>", "Common.define.chartData.textLineStackedMarker": "Nagomilana linija sa <PERSON>", "Common.define.chartData.textLineStackedPer": "100% Nagomilana linija", "Common.define.chartData.textLineStackedPerMarker": "100% Nagomilana linija sa markerima ", "Common.define.chartData.textPie": "Pita", "Common.define.chartData.textPie3d": "3-<PERSON> pita", "Common.define.chartData.textPoint": "XY (Raštrkano)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Popunjen radar", "Common.define.chartData.textRadarMarker": "Radar sa markerima ", "Common.define.chartData.textScatter": "Raštrkaj", "Common.define.chartData.textScatterLine": "Raštrkaj sa pravim linijama", "Common.define.chartData.textScatterLineMarker": "Raštrkaj sa pravim linijama i markerima", "Common.define.chartData.textScatterSmooth": "Raštrkaj sa glatkim linijama", "Common.define.chartData.textScatterSmoothMarker": "Raštrkaj sa glatkim linijama i markerima", "Common.define.chartData.textSparks": "Iskrice", "Common.define.chartData.textStock": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textSurface": "Površ<PERSON> ", "Common.define.chartData.textWinLossSpark": "Dobitak/Gubitak", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Nijedan format postavljen", "Common.define.conditionalData.text1Above": "1 standardna devijacija iznad", "Common.define.conditionalData.text1Below": "1 standardna devijacija ispod", "Common.define.conditionalData.text2Above": "2 standardne devijacije iznad", "Common.define.conditionalData.text2Below": "2 standardne devijacije ispod", "Common.define.conditionalData.text3Above": "3 standardne devijacije iznad", "Common.define.conditionalData.text3Below": "3 standardne devijacije ispod", "Common.define.conditionalData.textAbove": "Iznad", "Common.define.conditionalData.textAverage": "Prosečno", "Common.define.conditionalData.textBegins": "Počinje sa", "Common.define.conditionalData.textBelow": "Ispod", "Common.define.conditionalData.textBetween": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textBlank": "Prazno", "Common.define.conditionalData.textBlanks": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>na mesta", "Common.define.conditionalData.textBottom": "Dno", "Common.define.conditionalData.textContains": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textDataBar": "<PERSON>raka podataka", "Common.define.conditionalData.textDate": "Datum", "Common.define.conditionalData.textDuplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textEnds": "<PERSON><PERSON><PERSON><PERSON><PERSON> se sa", "Common.define.conditionalData.textEqAbove": "Jednako sa ili iznad", "Common.define.conditionalData.textEqBelow": "Jednako sa ili ispod", "Common.define.conditionalData.textEqual": "<PERSON><PERSON><PERSON> sa", "Common.define.conditionalData.textError": "Greška", "Common.define.conditionalData.textErrors": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textFormula": "Formula", "Common.define.conditionalData.textGreater": "Veće od", "Common.define.conditionalData.textGreaterEq": "Veće od ili jednako sa", "Common.define.conditionalData.textIconSets": "Setovi ikonica", "Common.define.conditionalData.textLast7days": "U zadnjih 7 dana", "Common.define.conditionalData.textLastMonth": "<PERSON><PERSON><PERSON> me<PERSON>c", "Common.define.conditionalData.textLastWeek": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textLess": "<PERSON>je od", "Common.define.conditionalData.textLessEq": "Manje od ili jednako sa", "Common.define.conditionalData.textNextMonth": "Sledeć<PERSON> mesec", "Common.define.conditionalData.textNextWeek": "<PERSON>ledeć<PERSON>", "Common.define.conditionalData.textNotBetween": "<PERSON><PERSON>", "Common.define.conditionalData.textNotBlanks": "Ne sadrži praznine", "Common.define.conditionalData.textNotContains": "<PERSON>e sadrži", "Common.define.conditionalData.textNotEqual": "<PERSON><PERSON> j<PERSON> sa", "Common.define.conditionalData.textNotErrors": "Ne sadrži g<PERSON>", "Common.define.conditionalData.textText": "Tekst", "Common.define.conditionalData.textThisMonth": "<PERSON><PERSON><PERSON> me<PERSON>c", "Common.define.conditionalData.textThisWeek": "<PERSON><PERSON>", "Common.define.conditionalData.textToday": "<PERSON><PERSON>", "Common.define.conditionalData.textTomorrow": "Sutra", "Common.define.conditionalData.textTop": "Vrh", "Common.define.conditionalData.textUnique": "Jedinstven", "Common.define.conditionalData.textValue": "Vrednost je", "Common.define.conditionalData.textYesterday": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textAccentedPicture": "Naglašena Slika", "Common.define.smartArt.textAccentProcess": "<PERSON><PERSON>g<PERSON>", "Common.define.smartArt.textAlternatingFlow": "Naizmeničan tok", "Common.define.smartArt.textAlternatingHexagons": "Naizmenični šestougaonici", "Common.define.smartArt.textAlternatingPictureBlocks": "Smenjujući blokovi slika", "Common.define.smartArt.textAlternatingPictureCircles": "Smenjujući krugovi slike", "Common.define.smartArt.textArchitectureLayout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> raspored", "Common.define.smartArt.textArrowRibbon": "<PERSON><PERSON><PERSON> strelice", "Common.define.smartArt.textAscendingPictureAccentProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON> proces sa slikama", "Common.define.smartArt.textBalance": "Balans", "Common.define.smartArt.textBasicBendingProcess": "Osnovni proces savijanja ", "Common.define.smartArt.textBasicBlockList": "Osnovna blok lista", "Common.define.smartArt.textBasicChevronProcess": "Osnovni chevron proces", "Common.define.smartArt.textBasicCycle": "Osnovni ciklus", "Common.define.smartArt.textBasicMatrix": "Osnovna matrica", "Common.define.smartArt.textBasicPie": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicProcess": "<PERSON><PERSON><PERSON><PERSON> proces", "Common.define.smartArt.textBasicPyramid": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicRadial": "Osnovni radijalni", "Common.define.smartArt.textBasicTarget": "Osnovni target", "Common.define.smartArt.textBasicTimeline": "Osnovna vremenska linija", "Common.define.smartArt.textBasicVenn": "Osnovni Venn <PERSON> ", "Common.define.smartArt.textBendingPictureAccentList": "Lista savijenih slika za naglašavanje ", "Common.define.smartArt.textBendingPictureBlocks": "<PERSON><PERSON><PERSON><PERSON> blokova slika", "Common.define.smartArt.textBendingPictureCaption": "Opis savijene slike", "Common.define.smartArt.textBendingPictureCaptionList": "Lista naslova savijene slike", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Polu-providni tekst savijene slike", "Common.define.smartArt.textBlockCycle": "Blok ciklus", "Common.define.smartArt.textBubblePictureList": "Lista slika u obliku balona", "Common.define.smartArt.textCaptionedPictures": "Slike sa natpisima", "Common.define.smartArt.textChevronAccentProcess": "Chevron proces za dodavanje akcenta", "Common.define.smartArt.textChevronList": "Chevron lista", "Common.define.smartArt.textCircleAccentTimeline": "Vremenska linija sa akcentom u obliku kruga", "Common.define.smartArt.textCircleArrowProcess": "Proces kružne strelice", "Common.define.smartArt.textCirclePictureHierarchy": "Hijerarhija kružne slike", "Common.define.smartArt.textCircleProcess": "K<PERSON>ž<PERSON> proces", "Common.define.smartArt.textCircleRelationship": "Kružna veza", "Common.define.smartArt.textCircularBendingProcess": "Kružni proces savijanja", "Common.define.smartArt.textCircularPictureCallout": "Kružni oblačić sa slikom", "Common.define.smartArt.textClosedChevronProcess": "Zatvoren chevron proces", "Common.define.smartArt.textContinuousArrowProcess": "Neprekidan proces strelice", "Common.define.smartArt.textContinuousBlockProcess": "Neprekidan blok proces", "Common.define.smartArt.textContinuousCycle": "Neprekid<PERSON> ciklus", "Common.define.smartArt.textContinuousPictureList": "Neprekidna lista slike", "Common.define.smartArt.textConvergingArrows": "Konvergirajuće strelice", "Common.define.smartArt.textConvergingRadial": "Konvergirajuće radijalno", "Common.define.smartArt.textConvergingText": "Konvergirajući tekst", "Common.define.smartArt.textCounterbalanceArrows": "Protivteža strelice", "Common.define.smartArt.textCycle": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textCycleMatrix": "<PERSON><PERSON> c<PERSON>a", "Common.define.smartArt.textDescendingBlockList": "Silazna blok lista", "Common.define.smartArt.textDescendingProcess": "<PERSON><PERSON><PERSON><PERSON> proces", "Common.define.smartArt.textDetailedProcess": "<PERSON><PERSON><PERSON><PERSON> proces", "Common.define.smartArt.textDivergingArrows": "Ra<PERSON><PERSON><PERSON><PERSON><PERSON> strelice", "Common.define.smartArt.textDivergingRadial": "Razilazeći radijalni", "Common.define.smartArt.textEquation": "Jednačina", "Common.define.smartArt.textFramedTextPicture": "Uokvirena tekst slika", "Common.define.smartArt.textFunnel": "<PERSON><PERSON>", "Common.define.smartArt.textGear": "Zupčanik", "Common.define.smartArt.textGridMatrix": "Mrežna matrica", "Common.define.smartArt.textGroupedList": "Grupisana lista", "Common.define.smartArt.textHalfCircleOrganizationChart": "Polu kružni organizacioni grafikon", "Common.define.smartArt.textHexagonCluster": "<PERSON><PERSON><PERSON> figura", "Common.define.smartArt.textHexagonRadial": "Šestostrana figura sa zračnim rasporedom", "Common.define.smartArt.textHierarchy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textHierarchyList": "Lista hijerarhije", "Common.define.smartArt.textHorizontalBulletList": "Horizontalna lista oznaka", "Common.define.smartArt.textHorizontalHierarchy": "<PERSON><PERSON><PERSON> ", "Common.define.smartArt.textHorizontalLabeledHierarchy": "<PERSON><PERSON><PERSON> hi<PERSON>", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontalna multi-level hijerarhija ", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontalni organizacioni grafikon", "Common.define.smartArt.textHorizontalPictureList": "Horizontalna lista slika", "Common.define.smartArt.textIncreasingArrowProcess": "Proces sa rastućim strelicama", "Common.define.smartArt.textIncreasingCircleProcess": "Povećavanje procesa kruga", "Common.define.smartArt.textInterconnectedBlockProcess": "Međusobno povezani blok proces", "Common.define.smartArt.textInterconnectedRings": "Međus<PERSON><PERSON> povezani prstenovi", "Common.define.smartArt.textInvertedPyramid": "<PERSON><PERSON><PERSON><PERSON> piramida", "Common.define.smartArt.textLabeledHierarchy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textLinearVenn": "Linearni Venn <PERSON> ", "Common.define.smartArt.textLinedList": "<PERSON><PERSON><PERSON> lista", "Common.define.smartArt.textList": "Lista", "Common.define.smartArt.textMatrix": "Matrica", "Common.define.smartArt.textMultidirectionalCycle": "Višesmerni ciklus", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Ime i naslov organizacionog grafikona", "Common.define.smartArt.textNestedTarget": "Ugneždena meta", "Common.define.smartArt.textNondirectionalCycle": "Ciklus bez direkcije", "Common.define.smartArt.textOpposingArrows": "Suprotne strelice", "Common.define.smartArt.textOpposingIdeas": "Suprotne ideje", "Common.define.smartArt.textOrganizationChart": "Organizacijski grafikon", "Common.define.smartArt.textOther": "Drugo", "Common.define.smartArt.textPhasedProcess": "Fazirani proces", "Common.define.smartArt.textPicture": "Slika", "Common.define.smartArt.textPictureAccentBlocks": "Blokovi za naglašavanje slika", "Common.define.smartArt.textPictureAccentList": "Lista slika za naglašavanje ", "Common.define.smartArt.textPictureAccentProcess": "Proces naglašavanja slika", "Common.define.smartArt.textPictureCaptionList": "Slikovna lista opisa", "Common.define.smartArt.textPictureFrame": "OkvirSlike", "Common.define.smartArt.textPictureGrid": "Slikovna mreža", "Common.define.smartArt.textPictureLineup": "Slikovni raspored", "Common.define.smartArt.textPictureOrganizationChart": "Slikovni organizacijski grafikon", "Common.define.smartArt.textPictureStrips": "Slike u traci", "Common.define.smartArt.textPieProcess": "Pita proces", "Common.define.smartArt.textPlusAndMinus": "Plus i minus", "Common.define.smartArt.textProcess": "Proces", "Common.define.smartArt.textProcessArrows": "Proces strelice", "Common.define.smartArt.textProcessList": "Proces lista", "Common.define.smartArt.textPyramid": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textPyramidList": "Piramida lista", "Common.define.smartArt.textRadialCluster": "<PERSON><PERSON><PERSON><PERSON><PERSON> klaster", "Common.define.smartArt.textRadialCycle": "Radi<PERSON><PERSON><PERSON> ciklus", "Common.define.smartArt.textRadialList": "Radijalna lista", "Common.define.smartArt.textRadialPictureList": "Radijalna slikovna lista", "Common.define.smartArt.textRadialVenn": "Radijalni Venn <PERSON> ", "Common.define.smartArt.textRandomToResultProcess": "Nasumični do procesa sa rezultatom", "Common.define.smartArt.textRelationship": "Veza", "Common.define.smartArt.textRepeatingBendingProcess": "Ponavljajući proces savijanja ", "Common.define.smartArt.textReverseList": "Obrnuta lista", "Common.define.smartArt.textSegmentedCycle": "<PERSON><PERSON><PERSON><PERSON> ciklus", "Common.define.smartArt.textSegmentedProcess": "<PERSON><PERSON><PERSON><PERSON> proces", "Common.define.smartArt.textSegmentedPyramid": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Common.define.smartArt.textSnapshotPictureList": "Lista snapshot slika", "Common.define.smartArt.textSpiralPicture": "<PERSON><PERSON><PERSON><PERSON> slika", "Common.define.smartArt.textSquareAccentList": "Kvadratna lista sa akcentom", "Common.define.smartArt.textStackedList": "<PERSON><PERSON><PERSON><PERSON> lista ", "Common.define.smartArt.textStackedVenn": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textStaggeredProcess": "Proces sa zakašnjenjem ", "Common.define.smartArt.textStepDownProcess": "Proces smanje<PERSON> koraka", "Common.define.smartArt.textStepUpProcess": "Step Up proces", "Common.define.smartArt.textSubStepProcess": "Podproces koraka", "Common.define.smartArt.textTabbedArc": "Kartica sa lukom", "Common.define.smartArt.textTableHierarchy": "<PERSON><PERSON><PERSON><PERSON><PERSON> tabele", "Common.define.smartArt.textTableList": "Tabela lista", "Common.define.smartArt.textTabList": "Kart<PERSON>", "Common.define.smartArt.textTargetList": "Lista ciljeva", "Common.define.smartArt.textTextCycle": "<PERSON><PERSON><PERSON> te<PERSON>", "Common.define.smartArt.textThemePictureAccent": "Naglasak na temi slike", "Common.define.smartArt.textThemePictureAlternatingAccent": "Alternirani akcent tematske slike", "Common.define.smartArt.textThemePictureGrid": "Mreža tematske slike", "Common.define.smartArt.textTitledMatrix": "Naslovljena matrica", "Common.define.smartArt.textTitledPictureAccentList": "Lista slika za naglašavanje sa naslovom", "Common.define.smartArt.textTitledPictureBlocks": "Slikovni blokovi sa naslovom", "Common.define.smartArt.textTitlePictureLineup": "Poravnanje naslovne slike", "Common.define.smartArt.textTrapezoidList": "Trapez lista", "Common.define.smartArt.textUpwardArrow": "Strelica na gore", "Common.define.smartArt.textVaryingWidthList": "Varirajuća lista širine ", "Common.define.smartArt.textVerticalAccentList": "Vertikalna lista naglašavanja", "Common.define.smartArt.textVerticalArrowList": "Vertikalna lista strelica ", "Common.define.smartArt.textVerticalBendingProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON> proces savijanja", "Common.define.smartArt.textVerticalBlockList": "Vertikalna blok lista", "Common.define.smartArt.textVerticalBoxList": "Vertikalna boks lista", "Common.define.smartArt.textVerticalBracketList": "Vertikalna lista zagrada", "Common.define.smartArt.textVerticalBulletList": "Vertikalne oznake lista", "Common.define.smartArt.textVerticalChevronList": "Vertikalna chevron lista", "Common.define.smartArt.textVerticalCircleList": "Vertikalna krug lista", "Common.define.smartArt.textVerticalCurvedList": "Vertikalno zakrivljena lista", "Common.define.smartArt.textVerticalEquation": "Vertikalna jednačina", "Common.define.smartArt.textVerticalPictureAccentList": "Vertikalna lista slika za naglašavanje ", "Common.define.smartArt.textVerticalPictureList": "Vertikalna lista slike", "Common.define.smartArt.textVerticalProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON> proces", "Common.Translation.textMoreButton": "<PERSON><PERSON><PERSON><PERSON> ", "Common.Translation.tipFileLocked": "Dokument je zaključan za uređivanje. Možete da napravite promene i sačuvate ga kao lokalni copy kasnije.", "Common.Translation.tipFileReadOnly": "Fajl je samo za čitanje. Da biste sačuvali promene, sačuvajte fajl sa novim imenom ili na drugoj lokaciji.", "Common.Translation.warnFileLocked": "Fajl se uređuje u drugoj aplikaciji. Možete nastaviti uređivanje i sačuvati ga kao kopiju.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON><PERSON>j kop<PERSON>", "Common.Translation.warnFileLockedBtnView": "Otvori za pregled", "Common.UI.ButtonColored.textAutoColor": "Automatski ", "Common.UI.ButtonColored.textEyedropper": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ButtonColored.textNewColor": "<PERSON><PERSON><PERSON><PERSON> boja", "Common.UI.ComboBorderSize.txtNoBorders": "Bez granica", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Bez granica", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON>", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Trenutno", "Common.UI.ExtendedColorDialog.textHexErr": "Uneta vrednost je netačna. Molimo vas unesite vrednost između 000000 i FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Novo", "Common.UI.ExtendedColorDialog.textRGBErr": "Uneta vrednost je netačna.<br>Mo<PERSON>o unesite numeričku vrednost između 0 i 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON> boje", "Common.UI.InputField.txtEmpty": "Ovo polje je obavezno", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.InputFieldBtnPassword.textHintHold": "Pritisni i drži da se prikaže lozinka", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Pokaži lozinku", "Common.UI.SearchBar.textFind": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "Zatvori pretragu", "Common.UI.SearchBar.tipNextResult": "Sledeći rezultat", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Otvori napredna podešavanja", "Common.UI.SearchBar.tipPreviousResult": "<PERSON><PERSON><PERSON><PERSON> rezultat", "Common.UI.SearchDialog.textHighlight": "Istakni rezultate", "Common.UI.SearchDialog.textMatchCase": "Osetljivo na velika/mala slova", "Common.UI.SearchDialog.textReplaceDef": "Unesi zamenski tekst", "Common.UI.SearchDialog.textSearchStart": "Unesi svoj tekst ovde ", "Common.UI.SearchDialog.textTitle": "Pronađi i zameni", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "<PERSON><PERSON>i samo", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "Zameni", "Common.UI.SearchDialog.txtBtnReplaceAll": "Zameni sve", "Common.UI.SynchronizeTip.textDontShow": "Ne pokazuj ovu poruku ponovo", "Common.UI.SynchronizeTip.textGotIt": "Razumem", "Common.UI.SynchronizeTip.textSynchronize": "Dokument je promenjen od strane drugog korisnika.<br><PERSON><PERSON> da sačuvate vaše promene i ponovo učitajte ažuriranja.", "Common.UI.ThemeColorPalette.textRecentColors": "Skorije boje", "Common.UI.ThemeColorPalette.textStandartColors": "Standardne boje", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON> boje", "Common.UI.Themes.txtThemeClassicLight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeContrastDark": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeDark": "Tamno", "Common.UI.Themes.txtThemeGray": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "<PERSON><PERSON> kao sistem", "Common.UI.Window.cancelButtonText": "Ot<PERSON>ži", "Common.UI.Window.closeButtonText": "Zatvori", "Common.UI.Window.noButtonText": "Ne", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Potvrda", "Common.UI.Window.textDontShow": "Ne pokazuj ovu poruku ponovo", "Common.UI.Window.textError": "Greška", "Common.UI.Window.textInformation": "Informacija", "Common.UI.Window.textWarning": "Upozorenje ", "Common.UI.Window.yesButtonText": "Da", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "tačka", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Ak<PERSON>nat ", "Common.Utils.ThemeColor.txtAqua": "Voda", "Common.Utils.ThemeColor.txtbackground": "Pozadina", "Common.Utils.ThemeColor.txtBlack": "Crno", "Common.Utils.ThemeColor.txtBlue": "Plavo", "Common.Utils.ThemeColor.txtBrightGreen": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtBrown": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Tamno plavo", "Common.Utils.ThemeColor.txtDarker": "Tamnije", "Common.Utils.ThemeColor.txtDarkGray": "Tamno sivo", "Common.Utils.ThemeColor.txtDarkGreen": "Tamno zeleno", "Common.Utils.ThemeColor.txtDarkPurple": "Tamno ljubičasto", "Common.Utils.ThemeColor.txtDarkRed": "Tamno crveno", "Common.Utils.ThemeColor.txtDarkTeal": "Tamno tirkizna", "Common.Utils.ThemeColor.txtDarkYellow": "Tamno žuta", "Common.Utils.ThemeColor.txtGold": "Zlat<PERSON>", "Common.Utils.ThemeColor.txtGray": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtGreen": "<PERSON>ele<PERSON>", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavan<PERSON>", "Common.Utils.ThemeColor.txtLightBlue": "<PERSON><PERSON><PERSON>lava", "Common.Utils.ThemeColor.txtLighter": "Svetlije", "Common.Utils.ThemeColor.txtLightGray": "<PERSON><PERSON><PERSON> siva", "Common.Utils.ThemeColor.txtLightGreen": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtLightOrange": "<PERSON><PERSON><PERSON> ", "Common.Utils.ThemeColor.txtLightYellow": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtOrange": "Narandžasta", "Common.Utils.ThemeColor.txtPink": "Roze", "Common.Utils.ThemeColor.txtPurple": "Ljubičasta", "Common.Utils.ThemeColor.txtRed": "Crvena", "Common.Utils.ThemeColor.txtRose": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Nebo plava", "Common.Utils.ThemeColor.txtTeal": "Tirkizna", "Common.Utils.ThemeColor.txttext": "Tekst", "Common.Utils.ThemeColor.txtTurquosie": "Tirkizna", "Common.Utils.ThemeColor.txtViolet": "Ljubičasta ", "Common.Utils.ThemeColor.txtWhite": "Belo", "Common.Utils.ThemeColor.txtYellow": "<PERSON><PERSON> ", "Common.Views.About.txtAddress": "adresa:", "Common.Views.About.txtLicensee": "LICENCIRANI", "Common.Views.About.txtLicensor": "DAVALAC LICENCE ", "Common.Views.About.txtMail": "email:", "Common.Views.About.txtPoweredBy": "Pokreće ", "Common.Views.About.txtTel": "tel.:", "Common.Views.About.txtVersion": "Verzija", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Primeni dok radiš", "Common.Views.AutoCorrectDialog.textAutoCorrect": "AutoIspravka", "Common.Views.AutoCorrectDialog.textAutoFormat": "AutoFormatiraj dok kucaš ", "Common.Views.AutoCorrectDialog.textBy": "Po", "Common.Views.AutoCorrectDialog.textDelete": "Izbriši", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet i mrežni putevi sa hiperlinkovima", "Common.Views.AutoCorrectDialog.textMathCorrect": "Matematička AutoIspravka", "Common.Views.AutoCorrectDialog.textNewRowCol": "Obuhvati nove redove i kolone u tabelu", "Common.Views.AutoCorrectDialog.textRecognized": "Prepoznate funkcije", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Sledeći izrazi se prepoznaju kao matematički izrazi. Oni se neće automatski podebljavati.", "Common.Views.AutoCorrectDialog.textReplace": "Zameni", "Common.Views.AutoCorrectDialog.textReplaceText": "Zameni dok kucaš", "Common.Views.AutoCorrectDialog.textReplaceType": "Zameni tekst dok kucaš", "Common.Views.AutoCorrectDialog.textReset": "Reset", "Common.Views.AutoCorrectDialog.textResetAll": "Resetuj na podrazumevano", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "AutoIspravka", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Prepoznate funkcije moraju sadržati samo slova od A do Z, velika ili mala.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Bilo koji izraz koji ste dodali će biti uklonjen, a uklonjeni će biti vraćeni. <PERSON>elite li da nastavite?", "Common.Views.AutoCorrectDialog.warnReplace": "Unos za automatsku ispravku za %1 već postoji. Da li želite da ga zamenite? ", "Common.Views.AutoCorrectDialog.warnReset": "<PERSON><PERSON> god autoispravku dodate biće odstranjena a promenjene će biti obnovljene na svoju originalnu vrednost. Da li želite da nastavite?", "Common.Views.AutoCorrectDialog.warnRestore": "Autoispravka unos za %1 će biti resetovana na svoju originalnu vrednost. Da li želite da nastavite? ", "Common.Views.Chat.textChat": "Čet", "Common.Views.Chat.textClosePanel": "Zatvori čet", "Common.Views.Chat.textEnterMessage": "Unesite svoju poruku ovde", "Common.Views.Chat.textSend": "Pošalji ", "Common.Views.Comments.mniAuthorAsc": "Autor A do Z", "Common.Views.Comments.mniAuthorDesc": "Autor Z do A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniDateDesc": "Najnovije", "Common.Views.Comments.mniFilterGroups": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniPositionAsc": "Odozgo", "Common.Views.Comments.mniPositionDesc": "Odozdo", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "Do<PERSON>j komentar dokumentu", "Common.Views.Comments.textAddReply": "Dodaj odgovor ", "Common.Views.Comments.textAll": "Sve", "Common.Views.Comments.textAnonym": "Gost", "Common.Views.Comments.textCancel": "Ot<PERSON>ži", "Common.Views.Comments.textClose": "Zatvori", "Common.Views.Comments.textClosePanel": "Zatvori komentare", "Common.Views.Comments.textComment": "Komentar", "Common.Views.Comments.textComments": "Komentari", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Unesi svoj komentar ovde", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON> k<PERSON>", "Common.Views.Comments.textOpenAgain": "Otvori ponovo", "Common.Views.Comments.textReply": "Odgovori", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "<PERSON><PERSON><PERSON><PERSON> komentare ", "Common.Views.Comments.textSortFilter": "Sortiraj i filtriraj komentare", "Common.Views.Comments.textSortFilterMore": "<PERSON>rt<PERSON>j, filtriraj i više", "Common.Views.Comments.textSortMore": "Sortiraj i više", "Common.Views.Comments.textViewResolved": "Nemate dozvolu da ponovo otvorite komentar ", "Common.Views.Comments.txtEmpty": "<PERSON>ema komentara u listu.", "Common.Views.CopyWarningDialog.textDontShow": "Ne pokazuj ovu poruku ponovo", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON><PERSON>, isecanje i lepljenje akcije koristeći dugmad na traci uređivača i kontekst meni akcije biće izvedene samo u okviru ove uređivačke kartice.<br><br>Da biste kopirali ili nalepili do ili iz aplikacija izvan uređivačke kartice koristite sledeće kombinacije tastature:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON>, iseci i nalepi akcije", "Common.Views.CopyWarningDialog.textToCopy": "za Kopiranje ", "Common.Views.CopyWarningDialog.textToCut": "za Isecanje", "Common.Views.CopyWarningDialog.textToPaste": "za Lepljenje", "Common.Views.CustomizeQuickAccessDialog.textDownload": "<PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Proveri komande koje će biti prikazane na Traci za Brzi Pristup", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Štampaj", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "<PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Uradi ponovo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Sačuvaj", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Prilagodi brzi pristup", "Common.Views.CustomizeQuickAccessDialog.textUndo": "<PERSON><PERSON><PERSON><PERSON> ", "Common.Views.DocumentAccessDialog.textLoading": "Učitavanje...", "Common.Views.DocumentAccessDialog.textTitle": "Deljenje podešavanja", "Common.Views.DocumentPropertyDialog.errorDate": "Možete izabrati vrednost iz kalendara da biste sačuvali vrednost kao Datum.<br><PERSON><PERSON> unosite vrednost ručno, ona će biti sačuvana kao Tekst.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "Ne", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Da", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Svojstvo treba da ima naslov", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "<PERSON><PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Da\" ili \"Ne\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Datum", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "<PERSON><PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "<PERSON><PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Unesite validan broj", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Tekst", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Svojstvo treba da ima vrednost", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Vrednost", "Common.Views.DocumentPropertyDialog.txtTitle": "Novo svojstvo dokumenta", "Common.Views.Draw.hintEraser": "Gumica za brisanje", "Common.Views.Draw.hintSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Draw.txtEraser": "Gumica za brisanje", "Common.Views.Draw.txtHighlighter": "<PERSON><PERSON>", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Olovka", "Common.Views.Draw.txtSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Draw.txtSize": "Veličina ", "Common.Views.EditNameDialog.textLabel": "Etiketa:", "Common.Views.EditNameDialog.textLabelError": "<PERSON>tiketa ne sme biti prazna.", "Common.Views.Header.ariaQuickAccessToolbar": "Traka za brzi pristup", "Common.Views.Header.labelCoUsersDescr": "Korisnici koji uređuju fajl:", "Common.Views.Header.textAddFavorite": "Označi kao omil<PERSON>no", "Common.Views.Header.textAdvSettings": "Napredna podešavanja ", "Common.Views.Header.textBack": "Otvori fajl lokaciju", "Common.Views.Header.textClose": "Zatvori fajl", "Common.Views.Header.textCompactView": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideLines": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideStatusBar": "Kombinuj list i status trake", "Common.Views.Header.textPrint": "Štampaj", "Common.Views.Header.textReadOnly": "Samo za čitanje ", "Common.Views.Header.textRemoveFavorite": "Ukloni iz Omiljenog", "Common.Views.Header.textSaveBegin": "Čuvanje...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "<PERSON>ve promene <PERSON>", "Common.Views.Header.textSaveExpander": "<PERSON>ve promene <PERSON>", "Common.Views.Header.textShare": "<PERSON><PERSON><PERSON>", "Common.Views.Header.textZoom": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipAccessRights": "Upravljaj pravima pristupa dokumentu", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Prilagodi traku za brzi pristup", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON> fajl", "Common.Views.Header.tipGoEdit": "Uredi trenutni fajl", "Common.Views.Header.tipPrint": "Štampaj fajl", "Common.Views.Header.tipPrintQuick": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipRedo": "Uradi ponovo", "Common.Views.Header.tipSave": "Sačuvaj", "Common.Views.Header.tipSearch": "Pretraga", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON><PERSON> ", "Common.Views.Header.tipUndock": "Odvoji u zaseban prozor", "Common.Views.Header.tipUsers": "Pogledaj k<PERSON>", "Common.Views.Header.tipViewSettings": "Podešavanja prikaza", "Common.Views.Header.tipViewUsers": "Pogledaj korisnike i upravljaj pravima pristupa dokumentu", "Common.Views.Header.txtAccessRights": "Promeni prava pristupa", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textCloseHistory": "Zatvori Istoriju", "Common.Views.History.textHideAll": "<PERSON><PERSON><PERSON><PERSON> izmene", "Common.Views.History.textHighlightDeleted": "Istakni izbrisano", "Common.Views.History.textMore": "<PERSON><PERSON><PERSON><PERSON> ", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textShowAll": "<PERSON><PERSON><PERSON><PERSON>e promene", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Istorija verzija", "Common.Views.ImageFromUrlDialog.textUrl": "Nalepi URL slike:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON>vo polje je neophodno", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Ovo polje bi trebalo da bude URL u \"http://www.example.com\" formatu", "Common.Views.ListSettingsDialog.textBulleted": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.textFromFile": "<PERSON><PERSON> fajla", "Common.Views.ListSettingsDialog.textFromStorage": "<PERSON>z s<PERSON>š<PERSON>", "Common.Views.ListSettingsDialog.textFromUrl": "Iz URL", "Common.Views.ListSettingsDialog.textNumbering": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.textSelect": "Odaberi iz", "Common.Views.ListSettingsDialog.tipChange": "<PERSON><PERSON>i <PERSON>", "Common.Views.ListSettingsDialog.txtBullet": "Oznaka", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "Slika", "Common.Views.ListSettingsDialog.txtImport": "Uvoz", "Common.Views.ListSettingsDialog.txtNewBullet": "Nova oznaka", "Common.Views.ListSettingsDialog.txtNewImage": "Nova slika", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtOfText": "% od teksta", "Common.Views.ListSettingsDialog.txtSize": "Veličina ", "Common.Views.ListSettingsDialog.txtStart": "Počni od", "Common.Views.ListSettingsDialog.txtSymbol": "Simbol", "Common.Views.ListSettingsDialog.txtTitle": "Podešavanja Liste", "Common.Views.ListSettingsDialog.txtType": "<PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textCopy": "<PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textCustomFunction": "Prilagođena <PERSON>", "Common.Views.MacrosDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textLoading": "Učitavanje...", "Common.Views.MacrosDialog.textMacros": "Makroi", "Common.Views.MacrosDialog.textMakeAutostart": "Omogući automatsko pokretanje", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Pokreni", "Common.Views.MacrosDialog.textSave": "Sačuvaj", "Common.Views.MacrosDialog.textTitle": "Makroi", "Common.Views.MacrosDialog.textUnMakeAutostart": "Onemogući automatsko pokretanje", "Common.Views.MacrosDialog.tipFunctionAdd": "Dodaj prilagođ<PERSON>u <PERSON>", "Common.Views.MacrosDialog.tipMacrosAdd": "<PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.tipMacrosRun": "Pokreni", "Common.Views.OpenDialog.closeButtonText": "Zatvori fajl", "Common.Views.OpenDialog.textInvalidRange": "Nevažeći opseg ćelija ", "Common.Views.OpenDialog.textSelectData": "Odaberi podatak", "Common.Views.OpenDialog.txtAdvanced": "Napredno", "Common.Views.OpenDialog.txtColon": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtComma": "Zarez", "Common.Views.OpenDialog.txtDelimiter": "Delimiter", "Common.Views.OpenDialog.txtDestData": "Odaberi gde da staviš podatke", "Common.Views.OpenDialog.txtEmpty": "<PERSON>vo polje je neophodno", "Common.Views.OpenDialog.txtEncoding": "Enkodiranje", "Common.Views.OpenDialog.txtIncorrectPwd": "Lozinka je netačna.", "Common.Views.OpenDialog.txtOpenFile": "Unesi lozinku da otvoriš fajl", "Common.Views.OpenDialog.txtOther": "Drugo", "Common.Views.OpenDialog.txtPassword": "Lozinka", "Common.Views.OpenDialog.txtPreview": "Pregled", "Common.Views.OpenDialog.txtProtected": "<PERSON>da unesete lozinku i otvorite fajl, trenutna lozinka koja vodi do fajla će biti resetovana.", "Common.Views.OpenDialog.txtSemicolon": "Tačka-zarez", "Common.Views.OpenDialog.txtSpace": "Razmak", "Common.Views.OpenDialog.txtTab": "Kartica ", "Common.Views.OpenDialog.txtTitle": "Izaberi %1 opcije", "Common.Views.OpenDialog.txtTitleProtected": "Zaštićen fajl", "Common.Views.PasswordDialog.txtDescription": "Postavite lozinku da zaštitite ovaj dokument ", "Common.Views.PasswordDialog.txtIncorrectPwd": "Potvrda da lozinka nije identična", "Common.Views.PasswordDialog.txtPassword": "Lozinka", "Common.Views.PasswordDialog.txtRepeat": "Ponovi lozinku", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON>", "Common.Views.PasswordDialog.txtWarning": "Upozorenje: <PERSON><PERSON> i<PERSON>e ili zaboravite lozinku, ne može biti oporavljena. <PERSON><PERSON> vas čuvajte je na sigurnom mestu. ", "Common.Views.PluginDlg.textDock": "Prikači dodatak", "Common.Views.PluginDlg.textLoading": "Učitavanje ", "Common.Views.PluginPanel.textClosePanel": "Zatvori plagin", "Common.Views.PluginPanel.textHidePanel": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.PluginPanel.textLoading": "Učitavanje ", "Common.Views.PluginPanel.textUndock": "Otkači dodatak", "Common.Views.Plugins.groupCaption": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.strPlugins": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textBackgroundPlugins": "Dodaci za pozadinu", "Common.Views.Plugins.textSettings": "Podešavanja", "Common.Views.Plugins.textStart": "Početak", "Common.Views.Plugins.textStop": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "Lista dodataka za pozadinu", "Common.Views.Plugins.tipMore": "<PERSON><PERSON><PERSON><PERSON> ", "Common.Views.Protection.hintAddPwd": "Enkriptuj sa lozinkom", "Common.Views.Protection.hintDelPwd": "Izbriši lozinku", "Common.Views.Protection.hintPwd": "Promeni ili izbriši lozinku", "Common.Views.Protection.hintSignature": "<PERSON><PERSON><PERSON> <PERSON>ni potpis ili potpisnu liniju", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON> lo<PERSON>", "Common.Views.Protection.txtDeletePwd": "Izbriši lozinku", "Common.Views.Protection.txtEncrypt": "Enkriptovanje", "Common.Views.Protection.txtInvisibleSignature": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtSignature": "Potpis", "Common.Views.Protection.txtSignatureLine": "<PERSON><PERSON><PERSON> lini<PERSON>", "Common.Views.RecentFiles.txtOpenRecent": "Otvori Skorašnje ", "Common.Views.RenameDialog.textName": "<PERSON><PERSON>", "Common.Views.RenameDialog.txtInvalidName": "Naziv fajla ne sme sadržati bilo koji od sledećih znakova:", "Common.Views.ReviewChanges.hintNext": "Do sledeće promene ", "Common.Views.ReviewChanges.hintPrev": "Do prethodne promene", "Common.Views.ReviewChanges.strFast": "Brz<PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Ko-uređivanje u stvarnom vremenu. Sve promene su automatski sačuvane.", "Common.Views.ReviewChanges.strStrict": "Striktno", "Common.Views.ReviewChanges.strStrictDesc": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" dugme da sinhronizuješ promene koje ti i drugi pravite.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Prihvati trenutnu promenu", "Common.Views.ReviewChanges.tipCoAuthMode": "<PERSON><PERSON> ko<PERSON>u<PERSON>đ<PERSON>", "Common.Views.ReviewChanges.tipCommentRem": "Izbriši komentare", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Izbriši trenutne komentare", "Common.Views.ReviewChanges.tipCommentResolve": "<PERSON><PERSON>jasni komentare", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "<PERSON><PERSON>jasni trenutne komentare", "Common.Views.ReviewChanges.tipHistory": "Prikaži istoriju verziju", "Common.Views.ReviewChanges.tipRejectCurrent": "Odbaci trenutnu promenu", "Common.Views.ReviewChanges.tipReview": "Prati promene", "Common.Views.ReviewChanges.tipReviewView": "Odaberi režim u kojem želiš da promene budu prikazane", "Common.Views.ReviewChanges.tipSetDocLang": "<PERSON>avi jezik dokumenta", "Common.Views.ReviewChanges.tipSetSpelling": "Provera pravopisa ", "Common.Views.ReviewChanges.tipSharing": "Upravljaj pravima pristupa dokumentu", "Common.Views.ReviewChanges.txtAccept": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptAll": "Prihvati sve promene", "Common.Views.ReviewChanges.txtAcceptChanges": "P<PERSON>hvati promene", "Common.Views.ReviewChanges.txtAcceptCurrent": "Prihvati trenutnu promenu", "Common.Views.ReviewChanges.txtChat": "Čet", "Common.Views.ReviewChanges.txtClose": "Zatvori", "Common.Views.ReviewChanges.txtCoAuthMode": "<PERSON><PERSON><PERSON>u<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentRemAll": "Izbriši sve komentare", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Izbriši trenutne komentare", "Common.Views.ReviewChanges.txtCommentRemMy": "Izbriši moje komentare", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Izbriši moje trenutne komentare", "Common.Views.ReviewChanges.txtCommentRemove": "Izbriši", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "Objasni sve komentare", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "<PERSON><PERSON>jasni trenutne komentare", "Common.Views.ReviewChanges.txtCommentResolveMy": "Objasni moje komentare", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Objasni Moje Trenutne Komentare", "Common.Views.ReviewChanges.txtDocLang": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtFinal": "<PERSON>ve <PERSON>mene <PERSON> (Pregled)", "Common.Views.ReviewChanges.txtFinalCap": "Konačno", "Common.Views.ReviewChanges.txtHistory": "Istorija Verzija", "Common.Views.ReviewChanges.txtMarkup": "Sve promene (Uređivanje)", "Common.Views.ReviewChanges.txtMarkupCap": "Obeležja", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON><PERSON>ć<PERSON>", "Common.Views.ReviewChanges.txtOriginal": "<PERSON>ve promene <PERSON> (Pregled)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Prethodno", "Common.Views.ReviewChanges.txtReject": "<PERSON>d<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Odbaci Sve Promene", "Common.Views.ReviewChanges.txtRejectChanges": "Odbaci promene", "Common.Views.ReviewChanges.txtRejectCurrent": "Odbaci Trenutnu Promenu", "Common.Views.ReviewChanges.txtSharing": "<PERSON>je<PERSON><PERSON>", "Common.Views.ReviewChanges.txtSpelling": "Provera pravopisa ", "Common.Views.ReviewChanges.txtTurnon": "Prati promene", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "Dodaj odgovor ", "Common.Views.ReviewPopover.textCancel": "Ot<PERSON>ži", "Common.Views.ReviewPopover.textClose": "Zatvori", "Common.Views.ReviewPopover.textComment": "Komentar", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Unesi svoj komentar ovde", "Common.Views.ReviewPopover.textMention": "+navođenje će da obezbedi pristup dokumentu i poslati email", "Common.Views.ReviewPopover.textMentionNotify": "+navođenje će da obavesti korisnika putem email-a", "Common.Views.ReviewPopover.textOpenAgain": "Otvori ponovo", "Common.Views.ReviewPopover.textReply": "Odgovori", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Nemate dozvolu da ponovo otvorite komentar ", "Common.Views.ReviewPopover.txtDeleteTip": "Izbriši", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Učitavanje ", "Common.Views.SaveAsDlg.textTitle": "Folder za čuvanje", "Common.Views.SearchPanel.textByColumns": "Po kolonama", "Common.Views.SearchPanel.textByRows": "Po redovima", "Common.Views.SearchPanel.textCaseSensitive": "Osetljivo na velika/mala slova", "Common.Views.SearchPanel.textCell": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textCloseSearch": "Zatvori pretragu", "Common.Views.SearchPanel.textContentChanged": "Dokument promenjen.", "Common.Views.SearchPanel.textFind": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "Pronađi i zameni", "Common.Views.SearchPanel.textFormula": "Formula", "Common.Views.SearchPanel.textFormulas": "Formule", "Common.Views.SearchPanel.textItemEntireCell": "Celokupni sadržaj <PERSON>", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} stavki us<PERSON>šno zamenjene.", "Common.Views.SearchPanel.textLookIn": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textMatchUsingRegExp": "Uklopi koristeći regularne izraze", "Common.Views.SearchPanel.textName": "Ime", "Common.Views.SearchPanel.textNoMatches": "<PERSON><PERSON>", "Common.Views.SearchPanel.textNoSearchResults": "Bez rezultata pretrage", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} stavke zamenjene. Preostale {2} stavke su zaključane od strane drugih korisnika.", "Common.Views.SearchPanel.textReplace": "Zameni", "Common.Views.SearchPanel.textReplaceAll": "Zameni Sve", "Common.Views.SearchPanel.textReplaceWith": "Zameni sa", "Common.Views.SearchPanel.textSearch": "Pretraga", "Common.Views.SearchPanel.textSearchAgain": "{0}Izvedi novu pretragu{1} za tačne rezultate.", "Common.Views.SearchPanel.textSearchHasStopped": "Pretraga je zaustavljena", "Common.Views.SearchPanel.textSearchOptions": "Opcije pretrage", "Common.Views.SearchPanel.textSearchResults": "<PERSON><PERSON><PERSON><PERSON> pretrage: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "<PERSON><PERSON><PERSON><PERSON> pretrage", "Common.Views.SearchPanel.textSelectDataRange": "Odaberi Opseg podataka", "Common.Views.SearchPanel.textSheet": "List", "Common.Views.SearchPanel.textSpecificRange": "Specifični opseg", "Common.Views.SearchPanel.textTooManyResults": "<PERSON><PERSON> previ<PERSON>e rezultata za prikaz ovde", "Common.Views.SearchPanel.textValue": "Vrednost ", "Common.Views.SearchPanel.textValues": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textWholeWords": "<PERSON><PERSON>i samo", "Common.Views.SearchPanel.textWithin": "U sklopu", "Common.Views.SearchPanel.textWorkbook": "Radna s<PERSON>ka", "Common.Views.SearchPanel.tipNextResult": "Sledeći rezultat", "Common.Views.SearchPanel.tipPreviousResult": "<PERSON><PERSON><PERSON><PERSON> rezultat", "Common.Views.SelectFileDlg.textLoading": "Učitavanje ", "Common.Views.SelectFileDlg.textTitle": "Odaberi izvor podataka ", "Common.Views.ShapeShadowDialog.txtAngle": "Ugao", "Common.Views.ShapeShadowDialog.txtDistance": "Udaljenost", "Common.Views.ShapeShadowDialog.txtSize": "Veličina ", "Common.Views.ShapeShadowDialog.txtTitle": "Po<PERSON><PERSON><PERSON><PERSON> senke", "Common.Views.ShapeShadowDialog.txtTransparency": "Providnost", "Common.Views.SignDialog.textBold": "Podebljano", "Common.Views.SignDialog.textCertificate": "Ser<PERSON><PERSON><PERSON> ", "Common.Views.SignDialog.textChange": "Promeni", "Common.Views.SignDialog.textInputName": "Ubaci ime potpisnika", "Common.Views.SignDialog.textItalic": "Kurziv", "Common.Views.SignDialog.textNameError": "<PERSON><PERSON> potpis<PERSON>a ne sme biti prazno.", "Common.Views.SignDialog.textPurpose": "Svrha potpisivanja ovog dokumenta", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "Odaberi sliku ", "Common.Views.SignDialog.textSignature": "Potpis izgleda kao ", "Common.Views.SignDialog.textTitle": "Potpiši dokument ", "Common.Views.SignDialog.textUseImage": "ili k<PERSON><PERSON><PERSON> \"Odaberi Sliku\" da koristite sliku kao potpis", "Common.Views.SignDialog.textValid": "Validno od %1 do %2", "Common.Views.SignDialog.tipFontName": "<PERSON><PERSON> fonta ", "Common.Views.SignDialog.tipFontSize": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignSettingsDialog.textAllowComment": "Dozvoli potpisniku da ostavi komentar u potpisni dijalog", "Common.Views.SignSettingsDialog.textDefInstruction": "Pre potpisivanja ovog dokumenta, proveri da je sadr<PERSON>aj koji pot<PERSON><PERSON><PERSON><PERSON>.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail predloženog potpisnika", "Common.Views.SignSettingsDialog.textInfoName": "Predloženi potpisnik", "Common.Views.SignSettingsDialog.textInfoTitle": "Predloženi naslov potpisnika", "Common.Views.SignSettingsDialog.textInstructions": "Instrukcije za potpisnika", "Common.Views.SignSettingsDialog.textShowDate": "Prikaži datum potpisivanja u redu za potpis", "Common.Views.SignSettingsDialog.textTitle": "Podeša<PERSON><PERSON>", "Common.Views.SignSettingsDialog.txtEmpty": "<PERSON>vo polje je neophodno", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX vrednost", "Common.Views.SymbolTableDialog.textCopyright": "Kopirajt znak", "Common.Views.SymbolTableDialog.textDCQuote": "Zatvarajući dvostruki navodnik", "Common.Views.SymbolTableDialog.textDOQuote": "Otvarajući dvostruki znak navoda", "Common.Views.SymbolTableDialog.textEllipsis": "<PERSON><PERSON><PERSON> elip<PERSON>", "Common.Views.SymbolTableDialog.textEmDash": "Em crtica", "Common.Views.SymbolTableDialog.textEmSpace": "Em razmak", "Common.Views.SymbolTableDialog.textEnDash": "En crtica", "Common.Views.SymbolTableDialog.textEnSpace": "En razmak", "Common.Views.SymbolTableDialog.textFont": "Font", "Common.Views.SymbolTableDialog.textNBHyphen": "Crtica bez preloma", "Common.Views.SymbolTableDialog.textNBSpace": "Razmak bez preloma", "Common.Views.SymbolTableDialog.textPilcrow": "Znak za novi red", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em razmak", "Common.Views.SymbolTableDialog.textRange": "Opseg", "Common.Views.SymbolTableDialog.textRecent": "Nedavno korišćeni simboli", "Common.Views.SymbolTableDialog.textRegistered": "Registrovani znak", "Common.Views.SymbolTableDialog.textSCQuote": "Zatvarajući jednostruki navodnik", "Common.Views.SymbolTableDialog.textSection": "Znak <PERSON>", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSHyphen": "Mekano crtanje crte", "Common.Views.SymbolTableDialog.textSOQuote": "Otvarajući jednostruki znak navoda", "Common.Views.SymbolTableDialog.textSpecial": "Specijalni karakteri", "Common.Views.SymbolTableDialog.textSymbols": "Simboli", "Common.Views.SymbolTableDialog.textTitle": "Simbol", "Common.Views.SymbolTableDialog.textTradeMark": "Zaštitni znak simbol", "Common.Views.UserNameDialog.textDontShow": "Ne pitaj me ponovo", "Common.Views.UserNameDialog.textLabel": "Etiketa:", "Common.Views.UserNameDialog.textLabelError": "<PERSON>tiketa ne sme biti prazna.", "SSE.Controllers.DataTab.strSheet": "List", "SSE.Controllers.DataTab.textAddExternalData": "Link do eksternog izvora je dodat. Možete ažurirati takve linkove u Podaci kartici.", "SSE.Controllers.DataTab.textColumns": "<PERSON><PERSON>", "SSE.Controllers.DataTab.textContinue": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textDontUpdate": "<PERSON>e Až<PERSON>", "SSE.Controllers.DataTab.textEmptyUrl": "Morate da naznačite URL.", "SSE.Controllers.DataTab.textRows": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textTurnOff": "Isključi automatsko ažuriranje", "SSE.Controllers.DataTab.textUpdate": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Controllers.DataTab.textWizard": "Tekst u Kolone", "SSE.Controllers.DataTab.txtDataValidation": "Podaci validacija", "SSE.Controllers.DataTab.txtErrorExternalLink": "Greška: a<PERSON><PERSON><PERSON><PERSON> je ne<PERSON>", "SSE.Controllers.DataTab.txtExpand": "Proširi ", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Podaci pored selekcije neće biti uklonjeni. Želite li da proširite selekciju da uključite susedne podatke ili da nastavite sa trenutno odabranim ćelijama samo?", "SSE.Controllers.DataTab.txtExtendDataValidation": "Selekcija sadrži neke ćelije bez Validacije Podataka podešavanja.<br>Da li želite da proširite Validacije Podataka na te ćelije?", "SSE.Controllers.DataTab.txtImportWizard": "Čarobnjak Za Uvoz Teksta", "SSE.Controllers.DataTab.txtRemDuplicates": "Ukloni Duplikate", "SSE.Controllers.DataTab.txtRemoveDataValidation": "Selekcija sadrži više od jednog tipa validacije.<br>Obrisati trenutna podešavanja i nastaviti?", "SSE.Controllers.DataTab.txtRemSelected": "Ukloni u odabranim", "SSE.Controllers.DataTab.txtUrlTitle": "Nalepi URL podataka", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "Ova radna sveska sadrži veze ka spoljnim izvorima koje se automatski ažuriraju. Ovo može biti nebezbedno.<br><br><PERSON><PERSON> im verujete, pritisnite Nastavi.", "SSE.Controllers.DataTab.warnUpdateExternalData": "Ova radna knjiga sadrži linkove do jednog ili više eksternih izvora koji mogu biti nebezbedni.<br><PERSON><PERSON> veru<PERSON> linkov<PERSON>, a<PERSON><PERSON>rajte ih da dobijete poslednje podatke.", "SSE.Controllers.DocumentHolder.alignmentText": "Poravnanje", "SSE.Controllers.DocumentHolder.centerText": "Centar", "SSE.Controllers.DocumentHolder.deleteColumnText": "Izbriši Kolonu", "SSE.Controllers.DocumentHolder.deleteRowText": "Izbriši Red", "SSE.Controllers.DocumentHolder.deleteText": "Izbriši", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Referenca linka ne postoji. Molimo vas ispravite link ili ga izbrišite.", "SSE.Controllers.DocumentHolder.guestText": "Gost", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Kolona levo", "SSE.Controllers.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Red Iznad", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Red Ispod", "SSE.Controllers.DocumentHolder.insertText": "Ubaci", "SSE.Controllers.DocumentHolder.leftText": "Levo", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Upozorenje ", "SSE.Controllers.DocumentHolder.rightText": "Des<PERSON>", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "AutoIspravka opcije", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Kolona širina {0} simboli ({1} p<PERSON><PERSON><PERSON>)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "<PERSON><PERSON><PERSON> {0} ta<PERSON><PERSON> ({1} pik<PERSON>i) ", "SSE.Controllers.DocumentHolder.textCtrlClick": "Kliknite na link da ga otvorite ili kliknite i držite dugme na mišu da odaberete ćeliju.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Ubaci kolonu sa leve strane", "SSE.Controllers.DocumentHolder.textInsertTop": "Ubaci red iznad", "SSE.Controllers.DocumentHolder.textPasteSpecial": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textStopExpand": "Zaustavi automatsko širenje tabela", "SSE.Controllers.DocumentHolder.textSym": "Simbolički", "SSE.Controllers.DocumentHolder.tipIsLocked": "Ovaj element se uređuje od strane drugog korisnika.", "SSE.Controllers.DocumentHolder.txtAboveAve": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Controllers.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "<PERSON><PERSON><PERSON> crtic<PERSON> r<PERSON>lo<PERSON>", "SSE.Controllers.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON> lini<PERSON> ", "SSE.Controllers.DocumentHolder.txtAddLB": "<PERSON><PERSON><PERSON> levu donju liniju ", "SSE.Controllers.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddLT": "<PERSON><PERSON><PERSON> le<PERSON> gornju liniju", "SSE.Controllers.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddTop": "<PERSON><PERSON><PERSON> ", "SSE.Controllers.DocumentHolder.txtAddVer": "<PERSON><PERSON><PERSON> vertikalnu liniju ", "SSE.Controllers.DocumentHolder.txtAlignToChar": "<PERSON><PERSON><PERSON><PERSON><PERSON> prema karakteru", "SSE.Controllers.DocumentHolder.txtAll": "(Sve)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Vraća celokupan sadržaj tabele ili određene kolone tabele uključujući zaglavlja kolone, podatke i celokupne redove", "SSE.Controllers.DocumentHolder.txtAnd": "i", "SSE.Controllers.DocumentHolder.txtBegins": "Počinje sa", "SSE.Controllers.DocumentHolder.txtBelowAve": "Ispod <PERSON>", "SSE.Controllers.DocumentHolder.txtBlanks": "(<PERSON><PERSON><PERSON>)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Svojstva granice", "SSE.Controllers.DocumentHolder.txtBottom": "Dno", "SSE.Controllers.DocumentHolder.txtByField": "%1 od %2", "SSE.Controllers.DocumentHolder.txtColumn": "Kolona", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Kolona p<PERSON>vnanje", "SSE.Controllers.DocumentHolder.txtContains": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Link kopiran na klipbord", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Vraća ćelije podataka tabele ili određene kolone tabele", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON> ve<PERSON> argumenta", "SSE.Controllers.DocumentHolder.txtDeleteArg": "<PERSON><PERSON><PERSON><PERSON><PERSON> argument", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Izbriši ručni <PERSON>", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Izbriši okružujuće znakove", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Izbriši okružujuće znakove i separatore", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Izbriši jednačinu", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Izbriši karakter", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Izbriši fundamentalni", "SSE.Controllers.DocumentHolder.txtEnds": "<PERSON><PERSON><PERSON><PERSON><PERSON> se sa", "SSE.Controllers.DocumentHolder.txtEquals": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Jednako sa bojom ćelije", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Jednako sa bojom fonta", "SSE.Controllers.DocumentHolder.txtExpand": "Proširi i sortiraj", "SSE.Controllers.DocumentHolder.txtExpandSort": "Podatak pored selekcije neće biti sortiran. Da li želite da proširite selekciju da uključite susedne podatke ili nastavite sa sortiranjem samo trenutno odabranih ćelija?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Dno", "SSE.Controllers.DocumentHolder.txtFilterTop": "Vrh", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Promeni u linearni razlomak", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Promeni u iskrivljeni razlomak", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Promeni u naslagani razlomak", "SSE.Controllers.DocumentHolder.txtGreater": "Veće od", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Veće od ili jednako sa", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Karakter iznad teksta", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "<PERSON><PERSON><PERSON> ispod teksta", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Vraća zaglavlja kolone za tabelu ili određene kolone tabele", "SSE.Controllers.DocumentHolder.txtHeight": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideBottom": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Sak<PERSON>j donji limit", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Sak<PERSON>j zatvorenu zagradu", "SSE.Controllers.DocumentHolder.txtHideDegree": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideHor": "<PERSON><PERSON><PERSON><PERSON> lini<PERSON>", "SSE.Controllers.DocumentHolder.txtHideLB": "<PERSON><PERSON><PERSON><PERSON> levu donju liniju", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideLT": "<PERSON><PERSON><PERSON><PERSON> levu gornju liniju", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Sak<PERSON>j otvorenu zagradu", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "<PERSON><PERSON><PERSON><PERSON> mesto za unos", "SSE.Controllers.DocumentHolder.txtHideRight": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideTop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Sakrij gornji limit", "SSE.Controllers.DocumentHolder.txtHideVer": "<PERSON><PERSON><PERSON><PERSON> vertika<PERSON>u liniju", "SSE.Controllers.DocumentHolder.txtImportWizard": "Čarobnjak za uvoz teksta", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Povećaj veličinu argumenta", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Ubaci argument nakon", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Ubaci argument pre", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Ubaci ručni prelaz", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Ubaci jednačinu posle", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Ubaci jednačinu pre", "SSE.Controllers.DocumentHolder.txtItems": "stavke", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Zadrži tekst samo", "SSE.Controllers.DocumentHolder.txtLess": "<PERSON>je od", "SSE.Controllers.DocumentHolder.txtLessEquals": "Manje od ili jednako sa", "SSE.Controllers.DocumentHolder.txtLimitChange": "Promeni lokaciju granica", "SSE.Controllers.DocumentHolder.txtLimitOver": "Ograničenje preko teksta", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Ograničenje ispod teksta", "SSE.Controllers.DocumentHolder.txtLockSort": "Podaci su pronađeni pored va<PERSON>e sele<PERSON>, ali nemate dovoljno dozvola da promenite te ćelije.<br>Da li želite da nastavite sa trenutnom selekcijom?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Uklopi zagrade do visine argumenta", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Porav<PERSON><PERSON> matrice", "SSE.Controllers.DocumentHolder.txtNoChoices": "Nema opcija za ispunjavanje ćelije.<br>Samo tekst vrednosti iz kolone mogu biti odabrane za zamenu.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Ne počinje sa", "SSE.Controllers.DocumentHolder.txtNotContains": "<PERSON>e sadrži", "SSE.Controllers.DocumentHolder.txtNotEnds": "Ne završava se sa", "SSE.Controllers.DocumentHolder.txtNotEquals": "Ne jednači se", "SSE.Controllers.DocumentHolder.txtOr": "ili", "SSE.Controllers.DocumentHolder.txtOverbar": "Traka preko teksta", "SSE.Controllers.DocumentHolder.txtPaste": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formula bez granica", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formula + <PERSON><PERSON>na kolone", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Odredišno formatiranje", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Nalepi sa<PERSON>je", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formula + broj formata", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "<PERSON><PERSON><PERSON> samo formulu", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formula + sve formatiranje", "SSE.Controllers.DocumentHolder.txtPasteLink": "Nalepi link", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "<PERSON><PERSON><PERSON> s<PERSON>a", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Spoji uslovno formatiranje", "SSE.Controllers.DocumentHolder.txtPastePicture": "Slika", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Izvorno formatiranje", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Transponuj", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Vrednost + sva formatiranja", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Vrednost + format broja", "SSE.Controllers.DocumentHolder.txtPasteValues": "Nalepi samo vrednost", "SSE.Controllers.DocumentHolder.txtPercent": "procenat", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Ponovi automatsko proširivanje tabele", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Ukloni liniju razlomka", "SSE.Controllers.DocumentHolder.txtRemLimit": "<PERSON>k<PERSON><PERSON> grani<PERSON>", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Ukloni naglašeni karakter ", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Ukloni traku", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Da li želite da odstranite ovaj potpis?<br>Ne može se poništiti.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Ukloni skripte", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Ukloni donji indeks ", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Uklon<PERSON> nadindek<PERSON>", "SSE.Controllers.DocumentHolder.txtRowHeight": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Skripte posle teksta", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Skripte pre teksta", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Prikaži donju granicu", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Prikaži zatvorenu zagradu ", "SSE.Controllers.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Prikaži otvorenu zagradu", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Prikaži rezervisani prostor", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Prikaži gornju granicu", "SSE.Controllers.DocumentHolder.txtSorting": "Sortiranje", "SSE.Controllers.DocumentHolder.txtSortSelected": "Sortiraj o<PERSON>no", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Razvuci zagrade ", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Odaberi samo ovaj red navedene kolone", "SSE.Controllers.DocumentHolder.txtTop": "Vrh", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Vraća celokupne redove za tabelu ili određene kolone tabele", "SSE.Controllers.DocumentHolder.txtUnderbar": "Traka ispod teksta", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Poništi automatsko proširivanje tabele", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Koristi čarobnjaka za uvoz teksta", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Kliknuti na ovaj link može biti štetno za vaš uređaj i podatke.<br>Da li ste sigurni da želite da nastavite?", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON> ", "SSE.Controllers.DocumentHolder.warnFilterError": "Potrebno vam je bar jedno polje u Vrednosti oblasti da biste primenili vrednost filter.", "SSE.Controllers.FormulaDialog.sCategoryAll": "Sve", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Baza podataka", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Datum i vreme", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Inženjerstvo ", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Finansijski ", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Informacija", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 pos<PERSON><PERSON><PERSON> k<PERSON>š<PERSON>h ", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logično", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Pretraga i referenca", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Matematika i trigonometrija", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistički", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Tekst i podaci", "SSE.Controllers.LeftMenu.newDocumentTitle": "Neimenovana proračunska tabela", "SSE.Controllers.LeftMenu.textByColumns": "Po kolonama", "SSE.Controllers.LeftMenu.textByRows": "Po redovima", "SSE.Controllers.LeftMenu.textFormulas": "Formule", "SSE.Controllers.LeftMenu.textItemEntireCell": "Celokupni sadržaj <PERSON>", "SSE.Controllers.LeftMenu.textLoadHistory": "Učitavanje istorije verzija...", "SSE.Controllers.LeftMenu.textLookin": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textNoTextFound": "Podaci koje ste pretraživali ne mogu biti pronađeni. Molimo podesite svoje opcije pretraživanja. ", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Zamena je učinjena. {0} pojave su preskočene.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Pretraga je učinjena. Pojave zamenjene: {0}", "SSE.Controllers.LeftMenu.textSave": "Sačuvaj", "SSE.Controllers.LeftMenu.textSearch": "Pretraga", "SSE.Controllers.LeftMenu.textSelectPath": "Unesite novo ime za čuvanje kopije datoteke", "SSE.Controllers.LeftMenu.textSheet": "List", "SSE.Controllers.LeftMenu.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWarning": "Upozorenje ", "SSE.Controllers.LeftMenu.textWithin": "U sklopu", "SSE.Controllers.LeftMenu.textWorkbook": "Radna s<PERSON>ka", "SSE.Controllers.LeftMenu.txtUntitled": "Bez naslova", "SSE.Controllers.LeftMenu.warnDownloadAs": "Ako nastavite da čuvate u ovom formatu sve karakteristike osim teksta će biti izgubljene.<br>Da li ste sigurni da želite da nastavite?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "CSV format ne podržava čuvanje datoteke sa više listova.<br>Da biste zadržali odabrani format i sačuvali samo trenutni list, pritisnite Sačuvaj.<br>Da biste sačuvali trenutni radni list, kliknite Otkaži i sačuvajte ga u drugom formatu.", "SSE.Controllers.Main.confirmAddCellWatches": "<PERSON><PERSON> radnja <PERSON>e do<PERSON>ti {0} posmatrača ćelija.<br>Da li želite da nastavite?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "<PERSON>va radnja će dodati samo {0} posmatrače ćelija iz razloga čuvanja u memoriji.<br><PERSON><PERSON><PERSON> li da nastavite?", "SSE.Controllers.Main.confirmMaxChangesSize": "Veličina akcija prevazilazi ograničenja postavljena za vaš server.<br>Pritisnite \"Undo\" da otkažete vašu poslednju akciju ili pritisnite \"Continue\" da zadržite akciju lokalno (morate da preuzmete fajl ili kopirate njegov sadržaj da se uverite da ništa nije izgubljeno).", "SSE.Controllers.Main.confirmMoveCellRange": "Opseg ćelija odredišta može sadržavati podatke. Nastaviti operaciju?", "SSE.Controllers.Main.confirmPutMergeRange": "<PERSON><PERSON>vor pod<PERSON>ka je sadržao spojene ćelije.<br>One su bile odvojene pre nego što su nalepljene u tabelu.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Formule u zaglavlju će biti uklonjene i konvertovane u statički tekst.<br>Ž<PERSON><PERSON> li da nastavite?", "SSE.Controllers.Main.confirmReplaceHFPicture": "Samo jedna slika može biti ubačena u svaki odeljak zaglavlja.<br>Prit<PERSON>ni<PERSON> \"Zameni\" da zamenite postojeću sliku.<br>Prit<PERSON>ni<PERSON> \"Zad<PERSON>ž<PERSON>\" da zadržite postojeću sliku.", "SSE.Controllers.Main.convertationTimeoutText": "Vremensko ograničenje preobraćanja prekoračeno.", "SSE.Controllers.Main.criticalErrorExtText": "<PERSON><PERSON><PERSON><PERSON> \"OK\" da se vratiš na listu dokumenta.", "SSE.Controllers.Main.criticalErrorTitle": "Greška", "SSE.Controllers.Main.downloadErrorText": "Preuziman<PERSON>.", "SSE.Controllers.Main.downloadTextText": "Preuzimanje proračunske tabele...", "SSE.Controllers.Main.downloadTitleText": "Preuzimanje Proračunske Tabele", "SSE.Controllers.Main.errNoDuplicates": "Nijedna duplikat vrednost nije pronađena.", "SSE.Controllers.Main.errorAccessDeny": "Pokušavate da izvedete akciju na koju nemate prava.<br><PERSON><PERSON><PERSON> kontaktirajte vašeg Dokument Server administratora.", "SSE.Controllers.Main.errorArgsRange": "Greška u unetoj formuli.<br>Netačan opseg argumenta je koriš<PERSON>en.", "SSE.Controllers.Main.errorAutoFilterChange": "Operacija nije dozvol<PERSON>, jer p<PERSON> da pomeri ćelije unutar tabele na vašem listu.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Operacija nije mogla biti učinjena za odabrane ćelije zato što ne možete pomeriti deo tabele.<br>Odaberite drugi opseg podataka tako da se cela tabela pomeri i pokušajte ponovo.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Operacija nije mogla biti učinjena za odabrani opseg ćelija.<br>Izaberite jedinstveni opseg podataka koji se razlikuje od postojećeg i pokušajte ponovo.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Operacija ne može biti izvedena zato što oblast sadrži filtrirane ćelije.<br><PERSON><PERSON> vas otkrijte filtrirane elemente i pokušajte ponovo.", "SSE.Controllers.Main.errorBadImageUrl": "URL slike je netačan", "SSE.Controllers.Main.errorCalculatedItemInPageField": "Stavka ne može biti dodata ili izmenjena. Pivot tabela ima ovo polje u filterima.", "SSE.Controllers.Main.errorCannotPasteImg": "Ne možemo nalepiti ovu sliku iz Klipborda, ali možete je sačuvati na vaš uređaj i uneti je odatle, ili možete kopirati sliku bez teksta i nalepiti u proračunsku tabelu.", "SSE.Controllers.Main.errorCannotUngroup": "Ne može da se razgrupiše. Da biste započeli nacrt, odaberite redove ili kolone i grupišite ih.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Ne možete koristiti ovu komandu na zaštićen list. Da biste koristili ovu komandu, ne<PERSON>šti<PERSON> list.<br>Možete dobiti zahtev da unesete lozinku.", "SSE.Controllers.Main.errorChangeArray": "Ne možete promeniti deo niza.", "SSE.Controllers.Main.errorChangeFilteredRange": "<PERSON>vo će da promeni filtriran opseg na vašem listu.<br>Da završite ovaj zadatak, molim vas uklonite AutoFiltere.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "Ćelija ili grafikon koji pokušavate da promenite je na zaštićenom listu.<br>Da napravite izmene, nezaštitite list. Možda će biti zatraženo da unesete lozinku.", "SSE.Controllers.Main.errorCircularReference": "Postoji jedna ili više kružnih referenci gde formula upućuje na svoju sopstvenu ćeliju, bilo direktno ili indirektno.<br>Pokušajte da uklonite ili promenite te reference ili premestite formule u druge ćelije.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Konekcija sa serverom izgubljena. Dokument ne može biti uređen sada.", "SSE.Controllers.Main.errorConnectToServer": "Dokument nije mogao biti sačuvan. Molimo proverite podešavanja konekcije ili kontaktirajte svog administratora.<br>Kada kliknete na \"OK\" dugme, bićete podstaknuti da preuzmete dokument.", "SSE.Controllers.Main.errorConvertXml": "Fajl ima nepodržan format.<br>Samo format XML Proračunske tabele 2003 može biti korišćen.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Ova komanda ne može biti korišćena sa višestrukim selekcijama.<br>Odaberite jedan opseg i pokušajte ponovo.", "SSE.Controllers.Main.errorCountArg": "Greška u unetoj formuli.<br>Netačan broj argumenata je koriš<PERSON>.", "SSE.Controllers.Main.errorCountArgExceed": "Greška u unetoj formuli.<br><PERSON><PERSON><PERSON> argumenata je prekoračen.", "SSE.Controllers.Main.errorCreateDefName": "Postojeći imenovani opsezi ne mogu biti uređeni i novi ne mogu biti kreirani<br>u ovom trenutku zato što se neki od njih uređuju.", "SSE.Controllers.Main.errorCreateRange": "Postojeći opsezi ne mogu biti uređeni i novi ne mogu biti kreirani<br>u ovom trenutku jer se neki od njih uređuju.", "SSE.Controllers.Main.errorDatabaseConnection": "Eksterna greška.<br>Greška pri povezivanju sa bazom podataka. Molimo kontaktirajte podršku u slučaju da se greška nastavi. ", "SSE.Controllers.Main.errorDataEncrypted": "Enkriptovane promene su primljene, ne mogu biti dešifrovane.", "SSE.Controllers.Main.errorDataRange": "<PERSON><PERSON><PERSON><PERSON> domet podataka.", "SSE.Controllers.Main.errorDataValidate": "Vrednost koju ste uneli nije validna.<br><PERSON><PERSON><PERSON> ima ograničene vrednosti koje mogu biti unete u ovu ćeliju.", "SSE.Controllers.Main.errorDefaultMessage": "Greška kod: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Pokušavate da izbrišete kolonu koja sadrži zaključanu ćeliju. Zaključane ćelije ne mogu biti obrisane dok je list zaštićen.<br>Da biste obrisali zaključanu ćeliju, prvo uklonite zaštitu lista. Možda će biti potrebno da unesete lozinku.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Pokušavate da izbrišete red koji sadrži zaključanu ćeliju. Zaključane ćeije ne mogu biti izbrisane dok je list zaštićen.<br>Da biste izbrisali zaključanu ćeliju, nezaštitite list. Možete biti zatraženi da unesete lozinku.", "SSE.Controllers.Main.errorDependentsNoFormulas": "Naredba 'P<PERSON><PERSON> Ćelije' nije pronašla formule koje se odnose na aktivnu ćeliju.", "SSE.Controllers.Main.errorDirectUrl": "Mo<PERSON> vas verifikujte link do dokumenta.<br>Ovaj link mora biti direktan link do fajla za preuzimanje.", "SSE.Controllers.Main.errorEditingDownloadas": "<PERSON><PERSON>š<PERSON> je nastala u toku rada sa dokumentom.<br><PERSON><PERSON><PERSON> \"<PERSON>uz<PERSON> kao\" opciju da sačuvaš rezervnu kopiju fajla na drajv.", "SSE.Controllers.Main.errorEditingSaveas": "<PERSON><PERSON>š<PERSON> je nastala u toku rada sa dokumentom.<br><PERSON><PERSON><PERSON> \"<PERSON>ču<PERSON>j kao...\" opci<PERSON> da sačuvaš rezervnu kopiju fajla na drajv.", "SSE.Controllers.Main.errorEditView": "Postojeći prikaz lista ne može biti uređen i novi ne mogu biti kreirani u ovom trenutku zato što se neki od njih uređuju.", "SSE.Controllers.Main.errorEmailClient": "Nijedan email klijent nije mogao biti nađen.", "SSE.Controllers.Main.errorFilePassProtect": "Fajl je zaštićen lozinkom i ne može biti otvoren.", "SSE.Controllers.Main.errorFileRequest": "Eksterna greška.<br>Greška u zahtevu za fajlom. Molimo vas kontaktirajte podršku u slučaju da se greška nastavi.", "SSE.Controllers.Main.errorFileSizeExceed": "Veličina fajla prevazilazi granice postavljene za vaš server.<br><PERSON><PERSON><PERSON> kontaktirajte vašeg Dokument Server administratora za detalje.", "SSE.Controllers.Main.errorFileVKey": "Eksterna greška.<br>Netačan bezbednosni ključ. Molimo vas da kontaktirate podršku u slučaju da se greška nastavi.", "SSE.Controllers.Main.errorFillRange": "<PERSON>je mogao da se ispuni odabran opseg ćelija.<br><PERSON><PERSON> spojene ćelije moraju biti iste veličine.", "SSE.Controllers.Main.errorForceSave": "Greška je nastala u toku čuvanja fajla. <PERSON><PERSON><PERSON> \"Preuzmi kao\" opciju da sačuvate fajl na drajv ili probajte ponovo kasnije.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "<PERSON><PERSON> moguće uneti formulu za stavku ili naziv polja u izveštaju okretne tabele (pivot table).", "SSE.Controllers.Main.errorFormulaName": "Greška u unetoj formuli.<br>Netačno ime formule je koriš<PERSON>eno.", "SSE.Controllers.Main.errorFormulaParsing": "Interna greška dok se formula parsira.", "SSE.Controllers.Main.errorFrmlMaxLength": "Dužina vaše formule prelazi limit od 8192 karaktera.<br><PERSON><PERSON><PERSON> vas uredite je i pokušajte ponovo.", "SSE.Controllers.Main.errorFrmlMaxReference": "Ne možete uneti ovu formulu zato što ima previše vrednosti,<br>refer<PERSON><PERSON>, i/ili imena.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Tekstualne vrednosti u formulama su ograničene na 255 karaktera.<br>Koristite KONKATENACIJA funkciju ili operatora konkatenacije (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Funkcija se odnosi na list koji ne postoji.<br><PERSON><PERSON> vas proverite podatke i pokušajte ponovo.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Operacija nije mogla da bude završena za odabran opseg ćelije.<br>Izaberite opseg tako da prvi red tabele bude u istom redu i rezultirajuća tabela preklapa trenutnu.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Operacija nije mogla da bude završena za odabran opseg ćelije.<br>Odaberite opseg koji ne uključuje druge tabele.", "SSE.Controllers.Main.errorInconsistentExt": "G<PERSON>š<PERSON> se pojavila u toku otvaranja fajla.<br><PERSON><PERSON><PERSON><PERSON> fajla se ne poklapa sa fajl ekstenzijom.", "SSE.Controllers.Main.errorInconsistentExtDocx": "Greš<PERSON> se pojavila prilikom otvaranja fajla.<br><PERSON><PERSON><PERSON><PERSON> fajla odgovara tekstualnim dokumentima (npr docx), ali fajl ima nedoslednu ekstenziju: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "Pojavila se greška pri otvaranju fajla.<br><PERSON><PERSON><PERSON><PERSON> fajla odgovara jednom od sledećih formata: pdf/djvu/xps/oxps, ali fajl ima nedoslednu ekstenziju: %1", "SSE.Controllers.Main.errorInconsistentExtPptx": "Greš<PERSON> se pojavila prilikom otvaranja fajla.<br><PERSON><PERSON><PERSON><PERSON> fajla odgovara prezentacijama (npr pptx), ali fajl ima nedoslednu ekstenziju: %1", "SSE.Controllers.Main.errorInconsistentExtXlsx": "Greš<PERSON> se pojavila prilikom otvaranja fajla.<br><PERSON><PERSON><PERSON><PERSON> fajla odgovara proračunskim tabel<PERSON> (npr xlsx), ali fajl ima nedoslednu ekstenziju: %1.", "SSE.Controllers.Main.errorInvalidRef": "Unesite tačno ime za selekciju ili validnu referencu na koju da odete.", "SSE.Controllers.Main.errorKeyEncrypt": "Nepoznat opisivač ključa ", "SSE.Controllers.Main.errorKeyExpire": "Ključni opis istekao", "SSE.Controllers.Main.errorLabledColumnsPivot": "Da biste kreirali pivot tabelu, koristite podatke koji su organizovani kao lista sa etiketiranim kolonama.", "SSE.Controllers.Main.errorLoadingFont": "Fontovi nisu učitani.<br><PERSON><PERSON><PERSON> kontaktirajte svog Dokument Server administratora", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Referenca za opseg lokacije ili podataka nije validna.", "SSE.Controllers.Main.errorLockedAll": "Operacija nije mogla biti ispunjena zato što je list zaključan od strane drugog korisnika.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "Jedna od ćelija uključena u proces traženja cilja je modifikovana od strane drugog korisnika.", "SSE.Controllers.Main.errorLockedCellPivot": "Ne možete promeniti podatke unutar pivot tabele.", "SSE.Controllers.Main.errorLockedWorksheetRename": "List ne može biti preimenovan u ovom trenutku zato što se preimenuje od strane drugog korisnika", "SSE.Controllers.Main.errorMaxPoints": "Maksimalan broj tačaka u serijama po grafikonu je 4096.", "SSE.Controllers.Main.errorMoveRange": "<PERSON><PERSON> mogu<PERSON>e promeniti deo spojene ćelije", "SSE.Controllers.Main.errorMoveSlicerError": "Filteri tabele ne mogu biti kopirani iz jedne radne knjige u drugu.<br>Pokušajte ponovo tako što ćete odabrati celokupnu tabelu i filtere.", "SSE.Controllers.Main.errorMultiCellFormula": "Višećelijske niz-formule nisu dozvoljene u tabelama.", "SSE.Controllers.Main.errorNoDataToParse": "<PERSON><PERSON> izabrani podaci za parsiranje.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "Ako jedna ili više Pivot tabela sadrži izračunate stavke, nijedno polje ne može biti korišćeno u oblasti podataka više puta, niti u oblasti podataka i nekoj drugoj oblasti istovremeno.", "SSE.Controllers.Main.errorOpenWarning": "Jedna od formula fajla prevazilazi limit od 8192 karaktera.<br>Formula je uklonjena.", "SSE.Controllers.Main.errorOperandExpected": "Sintaksa unete funkcije nije ispravna. <PERSON><PERSON> vas proverite da li vam nedostaje jedna od zagrada - '(' ili ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "Lozinka koju ste snabdeli nije tačna.<br>Verifikuj da je CAPS LOCK dugme isključeno i budite sigurni da koristite odgovarajuću veličinu slova.", "SSE.Controllers.Main.errorPasteInPivot": "Ne možemo napraviti ovu promenu za odabrane ćelije jer će to uticati na okretnu tabelu (pivot table).<br>Koristite listu polja za izmene u izveštaju.  ", "SSE.Controllers.Main.errorPasteMaxRange": "Kopiraj i nalepi oblasti se ne poklapaju.<br>Molimo vas odaberite oblast sa istom veličinom ili kliknite na prvu ćeliju u redu da nalepite kopirane ćelije.", "SSE.Controllers.Main.errorPasteMultiSelect": "Ova akcija ne može biti urađena na višestrukim selekcijama opsega.<br>Odaberite jedan opseg i pokušajte ponovo.", "SSE.Controllers.Main.errorPasteSlicerError": "Filteri tabele ne mogu biti kopirani iz jedne radne knjige u drugu.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Naziv polja okretne tabele već postoji.", "SSE.Controllers.Main.errorPivotGroup": "<PERSON><PERSON> mogu<PERSON>e grupisati tu selekciju.", "SSE.Controllers.Main.errorPivotOverlap": "Izveštaj pivot tabele ne može da preklopi tabelu.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Izveštaj Pivot Tabele je sačuvan bez osnovnih podataka.<br><PERSON><PERSON><PERSON> 'Osve<PERSON><PERSON>' dugme da ažurirate izveštaj.", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "Naredba 'P<PERSON>i <PERSON>' zahteva da aktivna ćelija sadrži formulu koja uključuje ispravne reference.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "<PERSON><PERSON><PERSON><PERSON>, nije moguće štampati više od 1500 stranica odjednom u trenutnoj verziji programa.<br>Ova restrikcija će biti uklonjena u nadolazećim izdanjima.", "SSE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.errorProtectedRange": "Ovaj opseg nije dozvoljen za uređivanje.", "SSE.Controllers.Main.errorSaveWatermark": "Ovaj fajl sadrži sliku vodenog žiga koja je povezana sa drugim domenom.<br>Da bi bila vidljiva u PDF-u, ažurirajte sliku vodenog žiga tako da bude povezana sa istim domenom kao i vaš dokument ili je otpremite sa svog računara.", "SSE.Controllers.Main.errorServerVersion": "Verzija uređivača je ažurirana. Stranica će biti ponovo učitana da se primene promene.", "SSE.Controllers.Main.errorSessionAbsolute": "Faza uređivanja dokumenta je istekla. Molimo ponovo učitajte stranicu.", "SSE.Controllers.Main.errorSessionIdle": "Dokument se nije uređivao već duže vreme. Molimo ponovo učitajte stranicu.", "SSE.Controllers.Main.errorSessionToken": "Veza sa <PERSON>om je prekinuta. Molimo vas ponovo učitajte stranicu.", "SSE.Controllers.Main.errorSetPassword": "Lozinka nije mogla da se postavi.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Referenca lokacije nije validna zato što ćelije nisu sve u istoj koloni ili redu.<br>Odaberite ćelije koje su sve u jednoj koloni ili redu.", "SSE.Controllers.Main.errorStockChart": "Pogrešan redosled redova. Da biste napravili grafikon akcija, postavite podatke na listu u sledećem redosledu:<br> p<PERSON><PERSON><PERSON><PERSON> cena, ma<PERSON><PERSON><PERSON><PERSON> cena, <PERSON>na cena, z<PERSON><PERSON><PERSON><PERSON> cena.", "SSE.Controllers.Main.errorToken": "Sigurnosni token dokumenta nije ispravno formiran.<br><PERSON><PERSON><PERSON> kontaktirajte vašeg Dokument Server administratora.", "SSE.Controllers.Main.errorTokenExpire": "<PERSON><PERSON><PERSON><PERSON><PERSON> token dokumenta je istekao.<br><PERSON><PERSON><PERSON> k<PERSON>aktirajte vašeg Dokument Server administratora.", "SSE.Controllers.Main.errorUnexpectedGuid": "Eksterna greška.<br>Neočekivan GUID. Molimo vas da kontaktirate podršku u slučaju da se greška nastavi.", "SSE.Controllers.Main.errorUpdateVersion": "Verzija fajla je promenjena. Stranica će biti ponovo učitana.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Konekcija je obnovljena, i verzija fajla je promenjena.<br>Pre nego što nastavite da radite, morate preuzeti fajl ili kopirate njegov sadržaj da se uverite da ništa nije izgubljeno, i onda ponovo učitate ovu stranicu.", "SSE.Controllers.Main.errorUserDrop": "Fajl ne može biti pristupljen sada.", "SSE.Controllers.Main.errorUsersExceed": "<PERSON><PERSON>j korisnika dozvoljen prema planu cena je prekoračen", "SSE.Controllers.Main.errorViewerDisconnect": "Konekcija je izgubljena. Možete i dalje videti dokument,<br>ali nećete moći da preuzmete ili štampate dok se konekcija ne uspostavi i stranica ponovo učita.", "SSE.Controllers.Main.errorWrongBracketsCount": "Greška u unetoj formuli.<br>Pogrešan broj zagrada je koriš<PERSON>.", "SSE.Controllers.Main.errorWrongOperator": "Greška u unetoj formuli. Pogrešan operator je kori<PERSON>.<br><PERSON><PERSON> ispravite grešku.", "SSE.Controllers.Main.errorWrongPassword": "Lozinka koju ste snabdeli nije tačna.", "SSE.Controllers.Main.errRemDuplicates": "Duplikat vrednosti pronađene i izbrisane: {0}, unikatne vrednosti ostale: {1}.", "SSE.Controllers.Main.leavePageText": "Imate nesačuvane promene u ovom proračunskom listu. Kliknite 'Ostani na ovoj Stranici' onda 'Sačuvaj' da ih sačuvate. Kliknite 'Napusti ovu Stranicu' da odbacite sve nesačuvane promene.", "SSE.Controllers.Main.leavePageTextOnClose": "Sve nesačuvane promene u ovoj proračunskoj tabeli će biti izgubljene.<br> <PERSON><PERSON><PERSON><PERSON> \"Otkaži\" onda \"Sačuvaj\" da ih sačuvate. Kliknite \"OK\" da odbacite sve nesačuvane promene.", "SSE.Controllers.Main.loadFontsTextText": "Učitavanje podataka...", "SSE.Controllers.Main.loadFontsTitleText": "Učitavanje Podataka ", "SSE.Controllers.Main.loadFontTextText": "Učitavanje podataka...", "SSE.Controllers.Main.loadFontTitleText": "Učitavanje Podataka ", "SSE.Controllers.Main.loadImagesTextText": "Učitavanje slika...", "SSE.Controllers.Main.loadImagesTitleText": "Učitavanje Slika", "SSE.Controllers.Main.loadImageTextText": "Učitavanje slike...", "SSE.Controllers.Main.loadImageTitleText": "Učitavanje Slike", "SSE.Controllers.Main.loadingDocumentTitleText": "Učitavanje proračunske tabele", "SSE.Controllers.Main.notcriticalErrorTitle": "Upozorenje ", "SSE.Controllers.Main.openErrorText": "Greška se pojavila u toku otvaranja fajla.", "SSE.Controllers.Main.openTextText": "Otvaranje proračunske tabele...", "SSE.Controllers.Main.openTitleText": "Otvaranje Proračunske tabele", "SSE.Controllers.Main.pastInMergeAreaError": "<PERSON><PERSON> mogu<PERSON>e promeniti deo spojene ćelije", "SSE.Controllers.Main.printTextText": "Štampanje proračunske tabele...", "SSE.Controllers.Main.printTitleText": "Štampanje proračunske tabele", "SSE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.requestEditFailedMessageText": "Neko uređuje ovaj dokument sada. Molimo pokušajte kasnije.", "SSE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.saveErrorText": "Greška se pojavila u toku čuvanja fajla.", "SSE.Controllers.Main.saveErrorTextDesktop": "<PERSON><PERSON><PERSON> fajl ne može biti sačuvan ili napravljen.<br><PERSON><PERSON><PERSON><PERSON> razlozi su:<br>1. fajl je samo za čitanje.<br>2. <PERSON>ajl se uređuje od strane drugih korisnika.<br>3. <PERSON>sk je pun ili oštećen. ", "SSE.Controllers.Main.saveTextText": "Čuvanje proračunske tabele...", "SSE.Controllers.Main.saveTitleText": "Čuvanje proračunske tabele", "SSE.Controllers.Main.scriptLoadError": "Konekcija je previše spora, neke od komponenata ne mogu biti učitane. Molimo vas ponovo učitajte stranicu.", "SSE.Controllers.Main.textAnonymous": "Anonimno", "SSE.Controllers.Main.textApplyAll": "Primeni za sve jednačine ", "SSE.Controllers.Main.textBuyNow": "<PERSON><PERSON><PERSON> veb sajt", "SSE.Controllers.Main.textChangesSaved": "<PERSON>ve promene <PERSON>", "SSE.Controllers.Main.textClose": "Zatvori", "SSE.Controllers.Main.textCloseTip": "Klikni da zatvoriš savet", "SSE.Controllers.Main.textConfirm": "Potvrda", "SSE.Controllers.Main.textConnectionLost": "Pokušavam da se povežem. Molim vas proverite postavke veze.", "SSE.Controllers.Main.textContactUs": "Kontakt prodaje", "SSE.Controllers.Main.textContinue": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textConvertEquation": "<PERSON>va jednačina je napravljena sa starom verzijom uređivača jednačina koji nije više podržan. Da je uredite, konvertujte jednačinu u Office Math ML format.<br>Konvertujte sada?", "SSE.Controllers.Main.textCustomLoader": "<PERSON><PERSON> obra<PERSON> pa<PERSON><PERSON><PERSON> da prema uslovima licence niste ovlašćeni da promenite učitavač.<br><PERSON><PERSON> kontaktirajte našu Prodajnu Službu kako biste dobili ponudu.", "SSE.Controllers.Main.textDisconnect": "Konekcija je izgubljena", "SSE.Controllers.Main.textFillOtherRows": "Popuni druge redove", "SSE.Controllers.Main.textFormulaFilledAllRows": "Formulom ispunjeni {0} redovi imaju podatke. Ispunjavanje ostalih praznih redova može da potraje nekoliko minuta.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Formulom ispunjeni prvi {0} redovi. Ispunjavanje drugih praznih redova može potrajati nekoliko minuta.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Formula popunjena samo u prvih {0} redova zbog ograničenja memorije. Ima ostalih {1} redova na ovom listu koji imaju podatke. Možete ih popuniti manuelno.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Formula popunjena samo u prvih {0} redova zbog ograničenja memorije. Ostali redovi na ovoj tabeli nemaju podatke.", "SSE.Controllers.Main.textGuest": "Gost", "SSE.Controllers.Main.textHasMacros": "<PERSON>ajl sadrži automatske makroe.<br><PERSON><PERSON><PERSON> li da pokrenete makroe?", "SSE.Controllers.Main.textKeep": "Zadrži", "SSE.Controllers.Main.textLearnMore": "Saznaj više", "SSE.Controllers.Main.textLoadingDocument": "Učitavanje proračunske tabele", "SSE.Controllers.Main.textLongName": "Unesi ime koje je manje od 128 karaktera.", "SSE.Controllers.Main.textNeedSynchronize": "I<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textNo": "Ne", "SSE.Controllers.Main.textNoLicenseTitle": "Limit licence dostignut", "SSE.Controllers.Main.textPaidFeature": "Plaćena funkcija ", "SSE.Controllers.Main.textPleaseWait": "Operacija može potrajati duže nego očekivano. Molim vas sačekajte...", "SSE.Controllers.Main.textReconnect": "Konekcija je uspostavljena", "SSE.Controllers.Main.textRemember": "Zapamti moj izbor za sve fajlove", "SSE.Controllers.Main.textRememberMacros": "Zapamti moj izbor za sve makroe", "SSE.Controllers.Main.textRenameError": "Korisničko ime ne sme biti prazno.", "SSE.Controllers.Main.textRenameLabel": "Unesite ime da bude korišćeno za kolaboraciju ", "SSE.Controllers.Main.textReplace": "Zameni", "SSE.Controllers.Main.textRequestMacros": "Makro pravi zahtev za URL. Da li želite da dozvolite zahtev za %1?", "SSE.Controllers.Main.textShape": "Oblik", "SSE.Controllers.Main.textStrict": "Striktni režim", "SSE.Controllers.Main.textText": "Tekst", "SSE.Controllers.Main.textTryQuickPrint": "Izabrali ste Brzo štampanje: ceo dokument će biti odštampan na poslednje odabranom ili podrazumevanom štampaču.<br>Da li želite da nastavite?", "SSE.Controllers.Main.textTryUndoRedo": "Funkcije Poništi/Vrati su onemogućene za Brzi režim zajedničkog uređivanja.<br>Kliknite na dugme 'Strogi režim' da biste prešli u Strogi režim zajedničkog uređivanja kako biste uređivali datoteku bez mešanja drugih korisnika i slali vaše promene tek nakon što ih sačuvate. Možete prelaziti između režima zajedničkog uređivanja koristeći Napredne postavke uređivača.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Poništi/Ponovi funkcije su onemogućene za Brzi režim ko-uređivanja.", "SSE.Controllers.Main.textUndo": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Controllers.Main.textUpdateVersion": "Dokument trenutno nije moguće uređivati.<br><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> a<PERSON> da<PERSON>, moli<PERSON>...", "SSE.Controllers.Main.textUpdating": "Ažuriranje", "SSE.Controllers.Main.textYes": "Da", "SSE.Controllers.Main.titleLicenseExp": "Licenca istekla", "SSE.Controllers.Main.titleLicenseNotActive": "Licenca nije aktivna", "SSE.Controllers.Main.titleServerVersion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.titleUpdateVersion": "Verzija promenjena", "SSE.Controllers.Main.txtAccent": "Ak<PERSON>nat ", "SSE.Controllers.Main.txtAll": "(Sve)", "SSE.Controllers.Main.txtArt": "Vaš tekst ovde ", "SSE.Controllers.Main.txtBasicShapes": "Osnovni oblici", "SSE.Controllers.Main.txtBlank": "(prazno)", "SSE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtByField": "%1 od %2", "SSE.Controllers.Main.txtCallouts": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtCharts": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtClearFilter": "Obriši filter", "SSE.Controllers.Main.txtColLbls": "Etikete kolone", "SSE.Controllers.Main.txtColumn": "Kolona", "SSE.Controllers.Main.txtConfidential": "Poverljivo", "SSE.Controllers.Main.txtDate": "Datum", "SSE.Controllers.Main.txtDays": "<PERSON>", "SSE.Controllers.Main.txtDiagramTitle": "Grafikon Naslov", "SSE.Controllers.Main.txtEditingMode": "<PERSON><PERSON>...", "SSE.Controllers.Main.txtErrorLoadHistory": "Učitavanje istorije <PERSON>", "SSE.Controllers.Main.txtFiguredArrows": "Oblikovane strelice ", "SSE.Controllers.Main.txtFile": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtGrandTotal": "Konačni Zbir", "SSE.Controllers.Main.txtGroup": "Grupa", "SSE.Controllers.Main.txtHours": "<PERSON><PERSON>", "SSE.Controllers.Main.txtInfo": "Info", "SSE.Controllers.Main.txtLines": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMath": "Matematika ", "SSE.Controllers.Main.txtMinutes": "Minute", "SSE.Controllers.Main.txtMonths": "Meseci", "SSE.Controllers.Main.txtMultiSelect": "Multi-Selektuj", "SSE.Controllers.Main.txtNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtOr": "%1 od %2", "SSE.Controllers.Main.txtPage": "Stranica", "SSE.Controllers.Main.txtPageOf": "Stranica %1 od %2", "SSE.Controllers.Main.txtPages": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPicture": "Slika", "SSE.Controllers.Main.txtPivotTable": "Pivot tabela", "SSE.Controllers.Main.txtPreparedBy": "Spremljeno od strane", "SSE.Controllers.Main.txtPrintArea": "Štampanje_Oblast", "SSE.Controllers.Main.txtQuarter": "Tromesečje", "SSE.Controllers.Main.txtQuarters": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRectangles": "Pravougaonici", "SSE.Controllers.Main.txtRow": "Red", "SSE.Controllers.Main.txtRowLbls": "<PERSON>ti<PERSON><PERSON>", "SSE.Controllers.Main.txtSaveCopyAsComplete": "<PERSON><PERSON><PERSON> dato<PERSON>ke je uspeš<PERSON> sačuvana", "SSE.Controllers.Main.txtScheme_Aspect": "Aspekt", "SSE.Controllers.Main.txtScheme_Blue": "Plavo", "SSE.Controllers.Main.txtScheme_Blue_Green": "<PERSON><PERSON><PERSON> zeleno", "SSE.Controllers.Main.txtScheme_Blue_II": "Plavo II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "<PERSON><PERSON> plavo", "SSE.Controllers.Main.txtScheme_Grayscale": "<PERSON><PERSON><PERSON> sive boje", "SSE.Controllers.Main.txtScheme_Green": "<PERSON>ele<PERSON>", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Zeleno žuta", "SSE.Controllers.Main.txtScheme_Marquee": "Marquee", "SSE.Controllers.Main.txtScheme_Median": "Medijana", "SSE.Controllers.Main.txtScheme_Office": "<PERSON>is", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "<PERSON><PERSON> 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Ofis 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Orange_Red": "Narandžasto crvena", "SSE.Controllers.Main.txtScheme_Paper": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Red": "Crve<PERSON>", "SSE.Controllers.Main.txtScheme_Red_Orange": "Crveno narandžasta", "SSE.Controllers.Main.txtScheme_Red_Violet": "Crveno ljubičasta", "SSE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "SSE.Controllers.Main.txtScheme_Violet": "Ljubičasta ", "SSE.Controllers.Main.txtScheme_Violet_II": "Ljubičasta II", "SSE.Controllers.Main.txtScheme_Yellow": "<PERSON><PERSON> ", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "<PERSON><PERSON>", "SSE.Controllers.Main.txtSeconds": "Sekunde", "SSE.Controllers.Main.txtSeries": "Serije", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Linija Oblačić 1 (Okvir i Akcentovana Traka)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Linija O<PERSON>čić 2 (<PERSON><PERSON> i Akcentovana Traka)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Linija Oblačić 3 (<PERSON><PERSON> i Akcentovana Traka)", "SSE.Controllers.Main.txtShape_accentCallout1": "Linija Oblačić 1 (Akcentovana Traka)", "SSE.Controllers.Main.txtShape_accentCallout2": "Linija <PERSON>čić 2 (<PERSON><PERSON><PERSON><PERSON><PERSON>rak<PERSON>)", "SSE.Controllers.Main.txtShape_accentCallout3": "Linija Oblačić 3 (<PERSON><PERSON><PERSON><PERSON><PERSON> Traka)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "<PERSON><PERSON> il<PERSON>me", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Početno dugme", "SSE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Dokument Dugme", "SSE.Controllers.Main.txtShape_actionButtonEnd": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "<PERSON><PERSON><PERSON> ili s<PERSON>me", "SSE.Controllers.Main.txtShape_actionButtonHelp": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonHome": "Početna stranica dugme", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Dugme za informaciju", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Film Dugme", "SSE.Controllers.Main.txtShape_actionButtonReturn": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonSound": "Dugme za Zvuk", "SSE.Controllers.Main.txtShape_arc": "Luk", "SSE.Controllers.Main.txtShape_bentArrow": "Savijena strelica", "SSE.Controllers.Main.txtShape_bentConnector5": "Konektor sa lakatnim savijanjem ", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Konektor sa lakatnim savijanjem i strelicom", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Laktasti dvostruki konektor sa strelicama", "SSE.Controllers.Main.txtShape_bentUpArrow": "Sa<PERSON>jena gore strelica", "SSE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_blockArc": "Blok luk", "SSE.Controllers.Main.txtShape_borderCallout1": "Linija Oblačić 1", "SSE.Controllers.Main.txtShape_borderCallout2": "<PERSON><PERSON> 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Linija <PERSON> 3", "SSE.Controllers.Main.txtShape_bracePair": "Dup<PERSON> vitičata zagrada", "SSE.Controllers.Main.txtShape_callout1": "Linija Oblačić 1 (<PERSON><PERSON> Okvira)", "SSE.Controllers.Main.txtShape_callout2": "Linija <PERSON> 2 (<PERSON><PERSON>)", "SSE.Controllers.Main.txtShape_callout3": "Linija Oblačić 3 (<PERSON><PERSON>)", "SSE.Controllers.Main.txtShape_can": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_chevron": "Chevron", "SSE.Controllers.Main.txtShape_chord": "Akord", "SSE.Controllers.Main.txtShape_circularArrow": "Kružna strelica", "SSE.Controllers.Main.txtShape_cloud": "Oblak", "SSE.Controllers.Main.txtShape_cloudCallout": "<PERSON>", "SSE.Controllers.Main.txtShape_corner": "Ugao", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Zakrivljeni konektor", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Zakrivljeni strelica konektor", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Zakrivljeni dupla - strelica konektor", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Zakrivljena dole strelica", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Zakrivljena levo strelica", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Zakrivljena des<PERSON> strelica", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Zakrivljena gore strelica", "SSE.Controllers.Main.txtShape_decagon": "Dekagon", "SSE.Controllers.Main.txtShape_diagStripe": "<PERSON>ja<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_dodecagon": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_donut": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_downArrow": "Dole Strelica", "SSE.Controllers.Main.txtShape_downArrowCallout": "Oblačić sa strelicom dole", "SSE.Controllers.Main.txtShape_ellipse": "Elipsa", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Zakrivljena dole traka", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Savijeni ribbon prema gore", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Dijagram toka: Alternativni proces", "SSE.Controllers.Main.txtShape_flowChartCollate": "Dijagram Toka: Kolek<PERSON>ja", "SSE.Controllers.Main.txtShape_flowChartConnector": "Dijagram toka: Konektor", "SSE.Controllers.Main.txtShape_flowChartDecision": "Dijagram toka: Odluka", "SSE.Controllers.Main.txtShape_flowChartDelay": "Dijagram toka: Zakašnjenje", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Dijagram toka: <PERSON>kran", "SSE.Controllers.Main.txtShape_flowChartDocument": "Dijagram toka: Dokument", "SSE.Controllers.Main.txtShape_flowChartExtract": "Dijagram Toka: Izdvajanje", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Dijagram toka: Podaci", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Dijagram toka: Interno skladištenje ", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Dijagram toka: Magne<PERSON><PERSON> disk", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Dijagram toka: Direktan pristup skladištenju", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Dijagram toka: Sekvencijalni pristup ", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Dijagram toka: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Dijagram toka: Ručna operacija", "SSE.Controllers.Main.txtShape_flowChartMerge": "Dijagram Toka: Spajanje", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Dijagram toka: Multidokument", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Dijagram Toka: <PERSON><PERSON><PERSON><PERSON> ", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Dijagram toka: Skladišteni podaci", "SSE.Controllers.Main.txtShape_flowChartOr": "Dijagram Toka: Ili", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Dijagram toka: Pre-definisani Proces", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Dijagram toka: P<PERSON><PERSON>a", "SSE.Controllers.Main.txtShape_flowChartProcess": "Dijagram toka: Proces", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Dijagram toka: Kartica", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Dijagram toka: <PERSON><PERSON><PERSON><PERSON> traka", "SSE.Controllers.Main.txtShape_flowChartSort": "Dijagram toka: Sortiranje", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Dijagram toka: <PERSON><PERSON><PERSON><PERSON> čvor za sabiranje ", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Dijagram Toka: <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_foldedCorner": "Savijeni u<PERSON>o", "SSE.Controllers.Main.txtShape_frame": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON> <PERSON> <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heart": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heptagon": "Sedmougao", "SSE.Controllers.Main.txtShape_hexagon": "Šestougaonik", "SSE.Controllers.Main.txtShape_homePlate": "Pentagon ", "SSE.Controllers.Main.txtShape_horizontalScroll": "Horizontalni skrol", "SSE.Controllers.Main.txtShape_irregularSeal1": "Eksplozija 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Eksplozija 2", "SSE.Controllers.Main.txtShape_leftArrow": "Leva Strelica", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Oblačić sa strelicom levo", "SSE.Controllers.Main.txtShape_leftBrace": "Leva vitičasta zagrada", "SSE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftRightArrow": "Leva desna strelica", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Oblačić sa strelicama levo desno", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "<PERSON>a desna gore strelica", "SSE.Controllers.Main.txtShape_leftUpArrow": "Leva gornja strelica", "SSE.Controllers.Main.txtShape_lightningBolt": "Munja", "SSE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "Strelica", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Dup<PERSON> strelica", "SSE.Controllers.Main.txtShape_mathDivide": "<PERSON>je<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMinus": "Minus", "SSE.Controllers.Main.txtShape_mathMultiply": "Pomnoži", "SSE.Controllers.Main.txtShape_mathNotEqual": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathPlus": "Plus", "SSE.Controllers.Main.txtShape_moon": "Mesec", "SSE.Controllers.Main.txtShape_noSmoking": "\"Ne\" Simbol", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Šiljasta desna strelica", "SSE.Controllers.Main.txtShape_octagon": "Osmougaonik", "SSE.Controllers.Main.txtShape_parallelogram": "Paralelogram", "SSE.Controllers.Main.txtShape_pentagon": "Pentagon ", "SSE.Controllers.Main.txtShape_pie": "Pita", "SSE.Controllers.Main.txtShape_plaque": "Potpiši", "SSE.Controllers.Main.txtShape_plus": "Plus", "SSE.Controllers.Main.txtShape_polyline1": "Škrabaj", "SSE.Controllers.Main.txtShape_polyline2": "Slobodna forma", "SSE.Controllers.Main.txtShape_quadArrow": "Četvorostruka strelica", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Oblačić sa četiri strelice", "SSE.Controllers.Main.txtShape_rect": "Pravougaonik", "SSE.Controllers.Main.txtShape_ribbon": "<PERSON><PERSON> traka", "SSE.Controllers.Main.txtShape_ribbon2": "Gornja traka", "SSE.Controllers.Main.txtShape_rightArrow": "Desna Strelica ", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Oblačić sa desnom strelicom", "SSE.Controllers.Main.txtShape_rightBrace": "Desna vitičasta zagrada ", "SSE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_round1Rect": "Pravougaonik sa zaobljenim jednim uglom", "SSE.Controllers.Main.txtShape_round2DiagRect": "Zaobljen dijagonalni ugao pravougaonika", "SSE.Controllers.Main.txtShape_round2SameRect": "Zaobljen ugao iste strane pravougaonika", "SSE.Controllers.Main.txtShape_roundRect": "Pravougaonik sa zaobljenim uglovima", "SSE.Controllers.Main.txtShape_rtTriangle": "<PERSON><PERSON> t<PERSON>", "SSE.Controllers.Main.txtShape_smileyFace": "Osmeh<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_snip1Rect": "Izreži pravougaonik jednog ugla", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Iseci dijagonalni ugao pravougaonika", "SSE.Controllers.Main.txtShape_snip2SameRect": "Izreži pravougaonik istog ugla", "SSE.Controllers.Main.txtShape_snipRoundRect": "Iseci i zaobli jedan ugao pravougaonika", "SSE.Controllers.Main.txtShape_spline": "Krivulja", "SSE.Controllers.Main.txtShape_star10": "Desetokraka Zvezda", "SSE.Controllers.Main.txtShape_star12": "<PERSON><PERSON>estok<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star16": "Šesnaestokraka Zvezda", "SSE.Controllers.Main.txtShape_star24": "Zvezda sa 24 tačke ", "SSE.Controllers.Main.txtShape_star32": "Zvezda Sa 32 Tačke ", "SSE.Controllers.Main.txtShape_star4": "Četvorokraka zvezda", "SSE.Controllers.Main.txtShape_star5": "Petokraka zvezda", "SSE.Controllers.Main.txtShape_star6": "Šestokraka Zvezda", "SSE.Controllers.Main.txtShape_star7": "Sedmokraka Zvezda", "SSE.Controllers.Main.txtShape_star8": "Osmokraka Zvezda", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Prugasta Desna Strelica", "SSE.Controllers.Main.txtShape_sun": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_teardrop": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_textRect": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_trapezoid": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_triangle": "Trouga<PERSON>", "SSE.Controllers.Main.txtShape_upArrow": "Gore <PERSON>", "SSE.Controllers.Main.txtShape_upArrowCallout": "Oblačić sa strelicom na gore", "SSE.Controllers.Main.txtShape_upDownArrow": "<PERSON> dole strelica", "SSE.Controllers.Main.txtShape_uturnArrow": "Strela Za Polukružni Okret", "SSE.Controllers.Main.txtShape_verticalScroll": "Vertikalni skrol", "SSE.Controllers.Main.txtShape_wave": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Zaobljeni Pravo<PERSON>", "SSE.Controllers.Main.txtSheet": "List", "SSE.Controllers.Main.txtSlicer": "Sečenje", "SSE.Controllers.Main.txtStarsRibbons": "Zvezde i Trake", "SSE.Controllers.Main.txtStyle_Bad": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Calculation": "Proračun", "SSE.Controllers.Main.txtStyle_Check_Cell": "<PERSON><PERSON><PERSON> ", "SSE.Controllers.Main.txtStyle_Comma": "Zarez", "SSE.Controllers.Main.txtStyle_Currency": "Valuta", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Tekst Objašnjenja", "SSE.Controllers.Main.txtStyle_Good": "Dobro", "SSE.Controllers.Main.txtStyle_Heading_1": "Naslov 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Naslov 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Naslov 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Naslov 4", "SSE.Controllers.Main.txtStyle_Input": "Unos", "SSE.Controllers.Main.txtStyle_Linked_Cell": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Neutral": "Neutralno", "SSE.Controllers.Main.txtStyle_Normal": "Normalno", "SSE.Controllers.Main.txtStyle_Note": "Beleška", "SSE.Controllers.Main.txtStyle_Output": "<PERSON><PERSON><PERSON>z", "SSE.Controllers.Main.txtStyle_Percent": "Procenat", "SSE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Total": "Ukupno", "SSE.Controllers.Main.txtStyle_Warning_Text": "Tekst Upozorenja", "SSE.Controllers.Main.txtTab": "Kartica ", "SSE.Controllers.Main.txtTable": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtTime": "Vreme", "SSE.Controllers.Main.txtUnlock": "Otkl<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtUnlockRange": "Otključaj opseg", "SSE.Controllers.Main.txtUnlockRangeDescription": "Unesite lozinku da promenite ovaj opseg:", "SSE.Controllers.Main.txtUnlockRangeWarning": "Opseg koji pokušavate da promenite je zaštićen lozinkom.", "SSE.Controllers.Main.txtValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtView": "Pregled", "SSE.Controllers.Main.txtXAxis": "Osa X", "SSE.Controllers.Main.txtYAxis": "<PERSON><PERSON>", "SSE.Controllers.Main.txtYears": "<PERSON><PERSON>", "SSE.Controllers.Main.unknownErrorText": "Nepoznata greška.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON><PERSON><PERSON> nije podržan.", "SSE.Controllers.Main.uploadDocExtMessage": "Nepoznati format dokumenta.", "SSE.Controllers.Main.uploadDocFileCountMessage": "<PERSON><PERSON><PERSON>h dokumenata.", "SSE.Controllers.Main.uploadDocSizeMessage": "Maksimum limit veličine dokumenta prekoračen.", "SSE.Controllers.Main.uploadImageExtMessage": "Nepoznati format slike.", "SSE.Controllers.Main.uploadImageFileCountMessage": "<PERSON><PERSON> o<PERSON>prem<PERSON>h slika.", "SSE.Controllers.Main.uploadImageSizeMessage": "Slika je prevelika. Maksimum veličina je 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "Otpremanje slike...", "SSE.Controllers.Main.uploadImageTitleText": "Otpremanje Slike", "SSE.Controllers.Main.waitText": "Molimo, sačekajte...", "SSE.Controllers.Main.warnBrowserIE9": "Aplikacija ima niske sposobnosti na IE9. Koristite IE10 ili noviju verziju", "SSE.Controllers.Main.warnBrowserZoom": "Trenutna podešavanja zumiranja vašeg pregledača nisu potpuno podržana. Molimo vas resetujte na podrazumevano zumiranje pritiskom Ctrl+0.", "SSE.Controllers.Main.warnLicenseAnonymous": "<PERSON><PERSON><PERSON> odbijen za anonimne korisnike.<br><PERSON><PERSON><PERSON> dokument će biti otvoren samo za prikaz.", "SSE.Controllers.Main.warnLicenseBefore": "Licenca nije aktivna.<br><PERSON><PERSON> vas kontaktirajte vašeg administratora.", "SSE.Controllers.Main.warnLicenseExceeded": "Dostigli ste limit za istovremene konekcije za %1 urednike. Ovaj dokument će biti otvoren samo za prikaz.<br>Kontaktirajte vašeg administratora da saznate više.", "SSE.Controllers.Main.warnLicenseExp": "<PERSON><PERSON><PERSON> licenca je istekla.<br><PERSON><PERSON> vas ažurirajte vašu licencu i osvežite stranicu.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Licenca istekla.<br>Nemate pristup funkcionalnosti uređivanja dokumenta.<br><PERSON><PERSON> vas kontaktirajte vašeg administratora.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Licenca treba da bude obnovljena.<br><PERSON><PERSON> ograni<PERSON>en pristup funkcionalnosti uređivanja dokumenta.<br><PERSON><PERSON> vas kontaktirajte vašeg administratora da dobijete potpuni pristup ", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Dostigli ste korisnički limit za %1 urednike. Kontaktirajte svog administratora da saznate više.", "SSE.Controllers.Main.warnNoLicense": "Dostigli ste limit za istovremene konekcije za %1 urednike. Ovaj dokument će biti otvoren samo za prikaz.<br>Kontaktirajte %1 tim za prodaju za lične uslove nadogradnje.", "SSE.Controllers.Main.warnNoLicenseUsers": "Dostigli ste korisnički limit za %1 urednike. Kontaktirajte %1 tim za prodaju za lične uslove nadogradnje.", "SSE.Controllers.Main.warnProcessRightsChange": "Uskraćeno vam je pravo da uredite ovaj fajl.", "SSE.Controllers.PivotTable.strSheet": "List", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "Stavka ne može biti dodata ili izmenjena. Pivot tabela ima ovo polje u filterima.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "Nisu dozvoljene radnje sa izračunatim stavkama za aktivnu ćeliju.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "Ako jedna ili više Pivot tabela sadrži izračunate stavke, nijedno polje ne može biti korišćeno u oblasti podataka više puta, niti u oblasti podataka i nekoj drugoj oblasti istovremeno.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Izračunate stavke ne funkcionišu sa prilagođenim međuzbirovima.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "Ime stavke ne može se pronaći. Proverite da li ste ispravno uneli ime i da li je stavka prisutna u izveštaju Pivot tabele.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Proseci, standardne devijacije i varijanse nisu podržani kada izveštaj Pivot tabele sadrži izračunate stavke.", "SSE.Controllers.Print.strAllSheets": "<PERSON><PERSON>ovi", "SSE.Controllers.Print.textFirstCol": "Prva kolona", "SSE.Controllers.Print.textFirstRow": "Prvi red", "SSE.Controllers.Print.textFrozenCols": "Zamrznute kolone", "SSE.Controllers.Print.textFrozenRows": "<PERSON><PERSON><PERSON><PERSON><PERSON> red<PERSON>i", "SSE.Controllers.Print.textInvalidRange": "GREŠKA! Nevažeći opseg ćelija", "SSE.Controllers.Print.textNoRepeat": "Ne ponavljaj", "SSE.Controllers.Print.textRepeat": "<PERSON><PERSON><PERSON>...", "SSE.Controllers.Print.textSelectRange": "Odaberi opseg", "SSE.Controllers.Print.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.txtZoomToPage": "Zumiraj na stranicu", "SSE.Controllers.Search.textInvalidRange": "GREŠKA! Nevažeći opseg ćelija", "SSE.Controllers.Search.textNoTextFound": "Podaci koje ste pretraživali ne mogu biti pronađeni. Molimo podesite svoje opcije pretraživanja. ", "SSE.Controllers.Search.textReplaceSkipped": "Zamena je učinjena. {0} pojave su preskočene.", "SSE.Controllers.Search.textReplaceSuccess": "Pretraga je učinjena. {0} pojave su zamenjene", "SSE.Controllers.Statusbar.errorLastSheet": "Radna sveska mora imati bar jedan vidljiv list.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Nije moguće obrisati list.", "SSE.Controllers.Statusbar.strSheet": "List", "SSE.Controllers.Statusbar.textDisconnect": "<b>Konekcija je izgubljena</b><br><PERSON><PERSON><PERSON><PERSON> konektova<PERSON>. Molim vas proverite podešavanja konekcije.", "SSE.Controllers.Statusbar.textSheetViewTip": "Vi ste u List Prikaz režimu. Filteri i sortiranje su vidljivi samo vama i onima koji su i dalje u ovom prikazu.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Vi ste u List Prikaz režimu. Filteri su vidljivi samo vama i onima koji su i dalje u ovom prikazu. ", "SSE.Controllers.Statusbar.warnDeleteSheet": "Odabrani radni listovi mogu sadržati podatke. Da li ste sigurni da želite da nastavite?", "SSE.Controllers.Statusbar.zoomText": "<PERSON><PERSON><PERSON> {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Font koji ćete sačuvati nije dostupan na trenutnom uređaju.<br>Stil teksta će biti prikazan koristeći jedan od sistemski<PERSON> fontova, sačuvani font će biti korišćen kada bude dostupan.<br>Da li želite da nastavite?", "SSE.Controllers.Toolbar.errorComboSeries": "Da biste napravili kombinovani grafikon, odaberite bar dve serije podataka.", "SSE.Controllers.Toolbar.errorMaxPoints": "Maksimalan broj tačaka u serijama po grafikonu je 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "GREŠKA! Maksimum broj serije podataka po grafikonu je 255", "SSE.Controllers.Toolbar.errorStockChart": "Pogrešan redosled redova. Da biste napravili grafikon akcija, postavite podatke na listu u sledećem redosledu:<br> p<PERSON><PERSON><PERSON><PERSON> cena, ma<PERSON><PERSON><PERSON><PERSON> cena, <PERSON>na cena, z<PERSON><PERSON><PERSON><PERSON> cena.", "SSE.Controllers.Toolbar.helpCalcItems": "Radite sa izračunatim stavkama u pivot tabelama.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Izračunate stavke", "SSE.Controllers.Toolbar.helpFastUndo": "Lako poništi izmene dok sarađuješ na listovima u brzom režimu.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "„Poništi“ u režimu zajedničkog uređivanja u realnom vremenu", "SSE.Controllers.Toolbar.helpMergeShapes": "Ko<PERSON><PERSON><PERSON><PERSON><PERSON>, delite, preklapajte i oduzimajte oblike u sekundi kako biste kreirali prilagođene vizuale.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "<PERSON><PERSON><PERSON> oblike", "SSE.Controllers.Toolbar.textAccent": "Akcenti", "SSE.Controllers.Toolbar.textBracket": "Zagrade", "SSE.Controllers.Toolbar.textDirectional": "Usmereno", "SSE.Controllers.Toolbar.textFontSizeErr": "Uneta vrednost je netačna.<br><PERSON><PERSON> vas unesite numeričku vrednost između 1 i 409", "SSE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textFunction": "Funkcije", "SSE.Controllers.Toolbar.textIndicator": "Indikatori", "SSE.Controllers.Toolbar.textInsert": "Ubaci", "SSE.Controllers.Toolbar.textIntegral": "Integrali", "SSE.Controllers.Toolbar.textLargeOperator": "Ogromni operatori", "SSE.Controllers.Toolbar.textLimitAndLog": "Granice i logaritmi", "SSE.Controllers.Toolbar.textLongOperation": "Dugačka operacija", "SSE.Controllers.Toolbar.textMatrix": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.textOperator": "Operatori", "SSE.Controllers.Toolbar.textPivot": "Pivot Tabela", "SSE.Controllers.Toolbar.textRadical": "Radi<PERSON><PERSON>", "SSE.Controllers.Toolbar.textRating": "Ocene", "SSE.Controllers.Toolbar.textRecentlyUsed": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textScript": "Skripte", "SSE.Controllers.Toolbar.textShapes": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textSymbols": "Simboli", "SSE.Controllers.Toolbar.textWarning": "Upozorenje ", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Desno-levo strelica iznad", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Ulevo strelica iznad", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Udesno strelica iznad", "SSE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarBot": "<PERSON><PERSON> crta", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Gornja crta", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Kutijasta formula (sa mestom za unos)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "<PERSON><PERSON><PERSON><PERSON> formula (primer)", "SSE.Controllers.Toolbar.txtAccent_Check": "Provera", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Donja vitičasta zagrada", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Gorn<PERSON> zagrada", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vektor A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC sa vodoravnom crtom iznad", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y sa crtom iznad", "SSE.Controllers.Toolbar.txtAccent_DDDot": "<PERSON><PERSON><PERSON><PERSON> (…)", "SSE.Controllers.Toolbar.txtAccent_DDot": "Du<PERSON><PERSON>                                                                                                                                                                                                                                                                                                                                                                                          ", "SSE.Controllers.Toolbar.txtAccent_Dot": "Tačka", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Dvostruka gornja crta", "SSE.Controllers.Toolbar.txtAccent_Grave": "Ozbiljno", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "G<PERSON><PERSON><PERSON> karakter ispod", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Grupisati karakter iznad", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "<PERSON> harpon nagore", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Udesno harpon iznad", "SSE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Smile": "Breve ", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Tilda", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON> zagrade", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Uglaste zagrade sa separatorom", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Uglaste zagrade sa dva separatora", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "<PERSON><PERSON> ug<PERSON> zag<PERSON> ", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Leva uglasta zag<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve": "Vitičaste zagrade", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Vitičaste zagrade sa separatorom", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Desna vitičasta zagrada", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Leva vitičasta zagrada", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (dva uslova)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (tri uslova)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Složi objekat", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Stavi objekat u zagrade", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Slučajevi primer", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Binomni k<PERSON>i<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Binomi koeficijent u uglastim zagradama", "SSE.Controllers.Toolbar.txtBracket_Line": "Vertikalne trake", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "<PERSON><PERSON> vertikalna traka", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Vertikalna traka s leva", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON> vertikalne trake", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "<PERSON><PERSON> dupla vertikalna traka ", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Leva dvostruka vertikalna traka", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Pod", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "<PERSON>ni pod", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "<PERSON> pod", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Zagrade sa separatorom", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON><PERSON> zagrade ", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Zamenski element između dve desne uglaste zagrade ", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Obrnute uglaste zagrade", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "<PERSON><PERSON> ug<PERSON> zag<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Leva uglasta zag<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Zamenski element između dve leve uglaste zagrade ", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON> uglaste zagrade", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "<PERSON><PERSON> dupla uglasta zagrada ", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "<PERSON>a dupla uglasta zagrada", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Plafon", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON>ni p<PERSON>n", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "<PERSON> plafon", "SSE.Controllers.Toolbar.txtDeleteCells": "Izbriši ćelije", "SSE.Controllers.Toolbar.txtExpand": "Proširi i sortiraj", "SSE.Controllers.Toolbar.txtExpandSort": "Podatak pored selekcije neće biti sortiran. Da li želite da proširite selekciju da uključite susedne podatke ili nastavite sa sortiranjem samo trenutno odabranih ćelija?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Iskrivljeni razlomak", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "dx preko dy", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Δy nad Δx", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "delimično y preko delimično x", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "delta y preko delta x", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Linearna frakci<PERSON> ", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi preko 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Mali razlomak", "SSE.Controllers.Toolbar.txtFractionVertical": "Naslagani razlomak", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Inverzna kosinus <PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Hiperbolička inverzna kosinus funkcija", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Inverzna kotangens funkcija", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Hiperbolička inverzna kotangens funkcija", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Inverzna kosekans funkcija", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Hiperbolička inverzna kosekans funkcija", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Inverzna sekans funkcija", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Hiperbolička inverzna sekans funkcija", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Inverzna sinus funkcija", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Hiperbolička inverzna sinus funkcija", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Inverzna tangens funkcija", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Hiperbolička inverzna tangens funkcija", "SSE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Hiperbolička kosinusna funkcija", "SSE.Controllers.Toolbar.txtFunction_Cot": "Kotangens funkcija", "SSE.Controllers.Toolbar.txtFunction_Coth": "Hiperbolička kotangens funkcija", "SSE.Controllers.Toolbar.txtFunction_Csc": "Kosekans funkcija", "SSE.Controllers.Toolbar.txtFunction_Csch": "Hiperbolička funkcija kosekansa", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sinus teta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Kos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Tangens formula", "SSE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sech": "Hiperbolička sekans funkcija", "SSE.Controllers.Toolbar.txtFunction_Sin": "Sinus funkcija", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Hiperbolička sinus funkcija", "SSE.Controllers.Toolbar.txtFunction_Tan": "Tangens funkcija", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Hiperbolička tangens funkcija", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Podatak i model", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Dobro, loše i neutralno", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "<PERSON><PERSON> imena", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Format broja", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Stilovi tematskih ćelija", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Naslovi i hedinzi", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Tamno", "SSE.Controllers.Toolbar.txtGroupTable_Light": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Srednje", "SSE.Controllers.Toolbar.txtInsertCells": "Ubaci ćelije", "SSE.Controllers.Toolbar.txtIntegral": "Integral", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Diferencijalni teta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Diferencijalni x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Diferencijalni y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integral sa naslaganim granicama", "SSE.Controllers.Toolbar.txtIntegralDouble": "Du<PERSON><PERSON> integral", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Dvostruki integral sa naslaganim granicama", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Dupli integral sa granicama", "SSE.Controllers.Toolbar.txtIntegralOriented": "<PERSON><PERSON><PERSON><PERSON> integral", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Konturni integral sa naslaganim granicama", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> integral", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Površinski integral sa naslaganim granicama", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Površinski integral sa granicama", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Konturni integral sa granicama", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integral volumena", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integral volumena sa naslaganim granicama", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integral volumena sa limitima", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integral sa granicama", "SSE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON><PERSON><PERSON> integral", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Trostruki integral sa naslaganim granicama", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Trostruki integral sa limitima", "SSE.Controllers.Toolbar.txtInvalidRange": "GREŠKA! Nevažeći opseg ćelije", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Logično I", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Logično I sa donjom granicom ", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Logično I sa granicama", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Logično I sa donjim indeksom ", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Logično I sa donjim/gornjim indeksom ", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Ko-proizvod", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Ko-proizvod sa donjom granicom", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Ko-proizvod sa granicama", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Ko-proizvod sa indeksom niže granice", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Ko-proizvod sa donjim/gornjim limitima u indeksu/eksponentu", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Sumacija nad k od n izbor k", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Sumacija od i jednako nula do n", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Primer <PERSON><PERSON><PERSON><PERSON> k<PERSON> dva indek<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Proizvod primer", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Unija primer", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Logično Ili", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Logično Ili sa donjom granicom ", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Logično Ili sa granicama", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Logično Ili sa donjim indeksom ", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Logično Ili sa donjim/gornjim indeksima", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Presecanje", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Presecanje sa donjom granicom", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Presecanje sa granicama", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Presek sa donjom granicom podindeksa", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Presek sa granicama podindeksa/nadindeksa", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Proizvod", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Proizvod sa donjom granicom", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Proizvod sa granicama", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Proizvod sa donjom granicom u potpisu", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Proizvod sa potpisanim/eksponentnim granicama", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Sumacija", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Sumacija sa donjim limitom", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Sumacija sa limitima", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Sumacija sa donjim limitom označenim indeksom", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Sumacija sa donjim/gornjim indeksima", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Unija", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Unija sa donjom granicom", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Unija sa granicama", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Unija s donjim indeksom ", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Unija sa donjim/gornjim indeksima", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Granica primer", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON><PERSON> primer", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Granica", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Prirodni logaritam ", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logaritam", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritam", "SSE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimum ", "SSE.Controllers.Toolbar.txtLockSort": "Podaci su pronađeni pored va<PERSON>e sele<PERSON>, ali nemate dovoljno dozvola da promenite te ćelije.<br>Da li želite da nastavite sa trenutnom selekcijom?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "Matrica 1x2 sa praznim ćelijama ", "SSE.Controllers.Toolbar.txtMatrix_1_3": "Matrica 1x3 sa praznim ćelijama ", "SSE.Controllers.Toolbar.txtMatrix_2_1": "Matrica 2x1 sa praznim ćelijama ", "SSE.Controllers.Toolbar.txtMatrix_2_2": "Matrica 2x2 sa praznim ćelijama", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Prazne 2 sa 2 matrice u duplim vertikalnim trakama", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Prazna 2x2 determinanta", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Prazna matrica 2x2 u zagradama", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Prazne 2 sa 2 matrice u zagradama", "SSE.Controllers.Toolbar.txtMatrix_2_3": "Matrica 2x3 sa praznim ćelijama ", "SSE.Controllers.Toolbar.txtMatrix_3_1": "Matrica 3x1 sa praznim ćelijama ", "SSE.Controllers.Toolbar.txtMatrix_3_2": "Matrica 3x2 sa praznim ćelijama", "SSE.Controllers.Toolbar.txtMatrix_3_3": "Matrica 3x3 sa praznim ćelijama ", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Polazne tačke ", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Tačke na srednjoj liniji", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Dija<PERSON>ne ta<PERSON> ", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Vertikalne tačke ", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Razređena matrica u zagradama", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Razređena matrica u zagradama", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "Matrica identiteta 2x2 sa nulama", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Matrica identiteta 2x2 sa praznim ćelijama van dijagonale", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "Matrica identiteta 3x3 sa nulama", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matrica identiteta 3x3 sa praznim ćelijama van dijagonale ", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Desno-levo strelica ispod", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Desno-levo strelica iznad", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Ulevo strelica ispod", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Ulevo strelica iznad", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Udesno strelica ispod ", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Udesno strelica iznad", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Dvotačka jednako", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "<PERSON><PERSON><PERSON> ", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta prinosi", "SSE.Controllers.Toolbar.txtOperator_Definition": "Jednako sa prema definiciji ", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta jednako sa", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Dvostruka strelica desno-levo ispod", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Dvostruka strelica desno-levo iznad", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Ulevo strelica ispod", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Ulevo strelica iznad", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Udesno strelica ispod ", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Udesno strelica iznad", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON><PERSON> j<PERSON>", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON> j<PERSON>", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus jednako", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Mereno od strane", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Desna strana kvadratne formule", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Kvadratni koren iz a kvadrata plus b kvadrata", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Kvadratni koren sa <PERSON>enom ", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Kvadratni koren", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Radikali sa <PERSON>enom", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Kvadratni koren", "SSE.Controllers.Toolbar.txtScriptCustom_1": "x podskript y kvadrat", "SSE.Controllers.Toolbar.txtScriptCustom_2": "e na minus i omega t", "SSE.Controllers.Toolbar.txtScriptCustom_3": "x na kvadrat ", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Y levo gornji indeks n levo donji indeks jedan", "SSE.Controllers.Toolbar.txtScriptSub": "<PERSON><PERSON> in<PERSON>s", "SSE.Controllers.Toolbar.txtScriptSubSup": "Podindeks-<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSorting": "Sortiranje", "SSE.Controllers.Toolbar.txtSortSelected": "Sortiraj o<PERSON>no", "SSE.Controllers.Toolbar.txtSymbol_about": "O<PERSON>p<PERSON><PERSON> ", "SSE.Controllers.Toolbar.txtSymbol_additional": "Do<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "Skoro jednako sa", "SSE.Controllers.Toolbar.txtSymbol_ast": "Operator <PERSON>vez<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Operator oznake", "SSE.Controllers.Toolbar.txtSymbol_cap": "Presecanje", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Kvadratni koren", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Srednja horizontal<PERSON> el<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>n", "SSE.Controllers.Toolbar.txtSymbol_cong": "<PERSON><PERSON><PERSON><PERSON><PERSON> jed<PERSON> sa", "SSE.Controllers.Toolbar.txtSymbol_cup": "Unija", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Dijagonalna trouglasta tačka naniže-desno", "SSE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Znak deljenja", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON> strelica", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON><PERSON> skup", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Identično sa", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "To postoji", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Faktorijel", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_forall": "Za sve", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gama", "SSE.Controllers.Toolbar.txtSymbol_geq": "Veće od ili jednako sa", "SSE.Controllers.Toolbar.txtSymbol_gg": "Mnogo veće od", "SSE.Controllers.Toolbar.txtSymbol_greater": "Veće od", "SSE.Controllers.Toolbar.txtSymbol_in": "Element od", "SSE.Controllers.Toolbar.txtSymbol_inc": "Inkrement", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Beskonačnost ", "SSE.Controllers.Toolbar.txtSymbol_iota": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Leva strelica", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Levo-desno strelica", "SSE.Controllers.Toolbar.txtSymbol_leq": "Manje od ili jednako sa", "SSE.Controllers.Toolbar.txtSymbol_less": "<PERSON>je od", "SSE.Controllers.Toolbar.txtSymbol_ll": "Mnogo manje od", "SSE.Controllers.Toolbar.txtSymbol_minus": "Minus", "SSE.Controllers.Toolbar.txtSymbol_mp": "Minus plus", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "<PERSON><PERSON> j<PERSON> sa", "SSE.Controllers.Toolbar.txtSymbol_ni": "<PERSON><PERSON><PERSON><PERSON> kao <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_not": "<PERSON>je z<PERSON>k", "SSE.Controllers.Toolbar.txtSymbol_notexists": "To ne postoji", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omikron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Parcijalni diferencijal", "SSE.Controllers.Toolbar.txtSymbol_percent": "Procenat", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus minus", "SSE.Controllers.Toolbar.txtSymbol_propto": "Proporcionalno sa", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Četvrti koren", "SSE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_rddots": "<PERSON><PERSON><PERSON>na dija<PERSON>na elipsa", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "<PERSON>na strelica ", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Korenski znak", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_theta": "Teta", "SSE.Controllers.Toolbar.txtSymbol_times": "Znak množenja", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON> strelica", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilon variant", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Phi variant", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Pi variant", "SSE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON> varijanta", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma variant", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Theta variant", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Vertikaln<PERSON> elipsa", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "<PERSON><PERSON><PERSON> stil <PERSON>", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Controllers.Toolbar.warnLongOperation": "Operacija koju pokušavate da izvedete može potrajati dugo vremena da se završi.<br>Da li ste sigurni da želite da nastavite?", "SSE.Controllers.Toolbar.warnMergeLostData": "Samo podatak iz gornje-leve ćelije će da ostane u spojenoj ćeliji.<br>Da li ste sigurni da želite da nastavite?", "SSE.Controllers.Toolbar.warnNoRecommended": "Da biste kreirali grafikon, odaberite ćelije koje sadrže podatke koje biste želeli da koristite.<br><PERSON>ko imate imena za redove i kolone i želeli biste da ih koristite kao etikete, uključite ih u svoju selekciju.", "SSE.Controllers.Viewport.textFreezePanes": "Zamrzni okvire", "SSE.Controllers.Viewport.textFreezePanesShadow": "Prikaži Senku Zamrznutih Prozora", "SSE.Controllers.Viewport.textHideFBar": "Sakrij formula traku", "SSE.Controllers.Viewport.textHideGridlines": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Viewport.textHideHeadings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Separator de<PERSON><PERSON><PERSON> brojeva", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "<PERSON><PERSON><PERSON><PERSON> separator", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Podešavanja korišćena da prepoznaju numeričke podatke", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Kvalif<PERSON><PERSON> te<PERSON>", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Napredna podešavanja ", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(nijedan)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Prilagođeni filter", "SSE.Views.AutoFilterDialog.textAddSelection": "Dodaj trenutnu selekciju u filter", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Praznine}", "SSE.Views.AutoFilterDialog.textSelectAll": "Odaberi sve", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Odaberi sve rezultate pretrage", "SSE.Views.AutoFilterDialog.textWarning": "Upozorenje ", "SSE.Views.AutoFilterDialog.txtAboveAve": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.AutoFilterDialog.txtAfter": "Posle...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "<PERSON>vi datumi u periodu", "SSE.Views.AutoFilterDialog.txtApril": "April", "SSE.Views.AutoFilterDialog.txtAugust": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtBefore": "Pre...", "SSE.Views.AutoFilterDialog.txtBegins": "Počinje sa...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Ispod <PERSON>", "SSE.Views.AutoFilterDialog.txtBetween": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "SSE.Views.AutoFilterDialog.txtClear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtContains": "Sad<PERSON><PERSON><PERSON>...", "SSE.Views.AutoFilterDialog.txtDateFilter": "Filter podataka", "SSE.Views.AutoFilterDialog.txtDecember": "Decembar", "SSE.Views.AutoFilterDialog.txtEmpty": "Unesite filter ćelije", "SSE.Views.AutoFilterDialog.txtEnds": "Završava se sa...", "SSE.Views.AutoFilterDialog.txtEquals": "Jed<PERSON><PERSON>...", "SSE.Views.AutoFilterDialog.txtFebruary": "<PERSON><PERSON><PERSON> ", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "<PERSON><PERSON><PERSON><PERSON> prema b<PERSON><PERSON> ", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "<PERSON><PERSON><PERSON><PERSON> prema boji fonta", "SSE.Views.AutoFilterDialog.txtGreater": "Veće od...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Veće od ili jednako sa...", "SSE.Views.AutoFilterDialog.txtJanuary": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtJuly": "Jul", "SSE.Views.AutoFilterDialog.txtJune": "Jun", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Etiketa filter", "SSE.Views.AutoFilterDialog.txtLastMonth": "<PERSON><PERSON><PERSON> me<PERSON>c", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Poslednji  kvartal", "SSE.Views.AutoFilterDialog.txtLastWeek": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtLastYear": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtLess": "Manje od...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Manje od ili jednako sa...", "SSE.Views.AutoFilterDialog.txtMarch": "Mart", "SSE.Views.AutoFilterDialog.txtMay": "Maj", "SSE.Views.AutoFilterDialog.txtNextMonth": "Sledeć<PERSON> mesec", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Sledeć<PERSON> k<PERSON>", "SSE.Views.AutoFilterDialog.txtNextWeek": "<PERSON>ledeć<PERSON>", "SSE.Views.AutoFilterDialog.txtNextYear": "<PERSON><PERSON><PERSON><PERSON><PERSON> godina", "SSE.Views.AutoFilterDialog.txtNotBegins": "Ne počinje sa...", "SSE.Views.AutoFilterDialog.txtNotBetween": "<PERSON><PERSON> i<PERSON>...", "SSE.Views.AutoFilterDialog.txtNotContains": "Ne sadrži...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Ne završava se sa...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Ne jednači se...", "SSE.Views.AutoFilterDialog.txtNovember": "Novembar", "SSE.Views.AutoFilterDialog.txtNumFilter": "<PERSON><PERSON> bro<PERSON>", "SSE.Views.AutoFilterDialog.txtOctober": "Oktobar", "SSE.Views.AutoFilterDialog.txtQuarter1": "Kvartal 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Kvartal 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Kvartal 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Kvartal 1", "SSE.Views.AutoFilterDialog.txtReapply": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtSeptember": "Septembar ", "SSE.Views.AutoFilterDialog.txtSortCellColor": "<PERSON><PERSON><PERSON><PERSON> prema b<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtSortFontColor": "<PERSON><PERSON><PERSON><PERSON> prema boji fonta", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Sortiraj najviše do najniže ", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Sortiraj najniže do najviše ", "SSE.Views.AutoFilterDialog.txtSortOption": "Više opcija sortiranja...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Tekst filter", "SSE.Views.AutoFilterDialog.txtThisMonth": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtThisQuarter": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtThisWeek": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtThisYear": "<PERSON>ve godine", "SSE.Views.AutoFilterDialog.txtTitle": "Filter", "SSE.Views.AutoFilterDialog.txtToday": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtTomorrow": "Sutra", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Vrednost filter ", "SSE.Views.AutoFilterDialog.txtYearToDate": "Od poč<PERSON>ka godine do danas", "SSE.Views.AutoFilterDialog.txtYesterday": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.warnFilterError": "Potrebno vam je bar jedno polje u Vrednosti oblasti da biste primenili vrednost filter.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Morate odabrati bar jednu vrednost", "SSE.Views.CellEditor.textManager": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellEditor.tipFormula": "Ubaci funkciju", "SSE.Views.CellRangeDialog.errorMaxRows": "GREŠKA! Maksimum broj serije podataka po grafikonu je 255", "SSE.Views.CellRangeDialog.errorStockChart": "Pogrešan redosled redova. Da biste napravili grafikon akcija, postavite podatke na listu u sledećem redosledu:<br> p<PERSON><PERSON><PERSON><PERSON> cena, ma<PERSON><PERSON><PERSON><PERSON> cena, <PERSON>na cena, z<PERSON><PERSON><PERSON><PERSON> cena.", "SSE.Views.CellRangeDialog.txtEmpty": "<PERSON>vo polje je neophodno", "SSE.Views.CellRangeDialog.txtInvalidRange": "GREŠKA! Nevažeći opseg ćelija", "SSE.Views.CellRangeDialog.txtTitle": "Odaberi opseg podataka", "SSE.Views.CellSettings.strShrink": "<PERSON><PERSON><PERSON> da stane", "SSE.Views.CellSettings.strWrap": "Upak<PERSON>j tekst", "SSE.Views.CellSettings.textAngle": "Ugao", "SSE.Views.CellSettings.textBackColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBackground": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBorderColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "Stil granica", "SSE.Views.CellSettings.textClearRule": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textColor": "Bojenje", "SSE.Views.CellSettings.textColorScales": "<PERSON><PERSON><PERSON> boja", "SSE.Views.CellSettings.textCondFormat": "Uslovno formatiranje", "SSE.Views.CellSettings.textControl": "Tekst kontrola", "SSE.Views.CellSettings.textDataBars": "<PERSON><PERSON><PERSON> podataka", "SSE.Views.CellSettings.textDirection": "Direkcija", "SSE.Views.CellSettings.textFill": "<PERSON><PERSON>", "SSE.Views.CellSettings.textForeground": "<PERSON><PERSON> prednjeg plana", "SSE.Views.CellSettings.textGradient": "Tačke gradijenta", "SSE.Views.CellSettings.textGradientColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "Gradijentno punjenje", "SSE.Views.CellSettings.textIndent": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textItems": "Stavke", "SSE.Views.CellSettings.textLinear": "Linearno", "SSE.Views.CellSettings.textManageRule": "Uprav<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textNewRule": "Novo pravilo", "SSE.Views.CellSettings.textNoFill": "Bez pun<PERSON>", "SSE.Views.CellSettings.textOrientation": "Tekst orijentacija ", "SSE.Views.CellSettings.textPattern": "Šablon", "SSE.Views.CellSettings.textPatternFill": "Šablon", "SSE.Views.CellSettings.textPosition": "Pozicija", "SSE.Views.CellSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textSelectBorders": "Odaberi granice koje <PERSON> da promeniš primenjujući stil odabran iznad", "SSE.Views.CellSettings.textSelection": "<PERSON>z trenutne sele<PERSON>", "SSE.Views.CellSettings.textThisPivot": "Iz ovog pivota", "SSE.Views.CellSettings.textThisSheet": "Iz ovog lista", "SSE.Views.CellSettings.textThisTable": "Iz ove tabele", "SSE.Views.CellSettings.tipAddGradientPoint": "Dodaj tačku gradijenta", "SSE.Views.CellSettings.tipAll": "Postavi spoljašnju ivicu i sve unutrašnje linije", "SSE.Views.CellSettings.tipBottom": "Postavi samo spoljašnju donju ivicu", "SSE.Views.CellSettings.tipDiagD": "<PERSON><PERSON> di<PERSON> don<PERSON> i<PERSON>u", "SSE.Views.CellSettings.tipDiagU": "<PERSON><PERSON> di<PERSON> gorn<PERSON> i<PERSON>u", "SSE.Views.CellSettings.tipInner": "Postavi samo unutrašnje linije", "SSE.Views.CellSettings.tipInnerHor": "Postavi samo <PERSON>ne unutrašnje linije", "SSE.Views.CellSettings.tipInnerVert": "Postavi samo vertikalne unutrašnje linije", "SSE.Views.CellSettings.tipLeft": "Postavi samo s<PERSON>jaš<PERSON> levu ivicu ", "SSE.Views.CellSettings.tipNone": "Ne postavljaj ivice", "SSE.Views.CellSettings.tipOuter": "Postavi samo spoljaš<PERSON> i<PERSON>u ", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Ukloni tačku gradienta", "SSE.Views.CellSettings.tipRight": "Postavi samo spoljaš<PERSON><PERSON> des<PERSON> i<PERSON>u ", "SSE.Views.CellSettings.tipTop": "Postavi samo spoljašnju gornju ivicu", "SSE.Views.ChartDataDialog.errorInFormula": "Postoji greška u formuli koju ste uneli.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Referenca nije validna. Referenca mora biti do otvorenog lista.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Maksimalan broj tačaka u serijama po grafikonu je 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Maks<PERSON>lan broj serija podataka po grafikonu je 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Referenca nije validna. Reference za <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ve<PERSON><PERSON><PERSON> ili etikete podataka mora biti jedna <PERSON>, red, ili kolona.", "SSE.Views.ChartDataDialog.errorNoValues": "Da biste kreirali grafi<PERSON>, seri<PERSON> moraju sadržati bar jednu vrednost.", "SSE.Views.ChartDataDialog.errorStockChart": "Pogrešan redosled redova. Da biste napravili grafikon akcija, postavite podatke na listu u sledećem redosledu:<br> p<PERSON><PERSON><PERSON><PERSON> cena, ma<PERSON><PERSON><PERSON><PERSON> cena, <PERSON>na cena, z<PERSON><PERSON><PERSON><PERSON> cena.", "SSE.Views.ChartDataDialog.textAdd": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textCategory": "Horizontalne (kategorija) oznake ose", "SSE.Views.ChartDataDialog.textData": "Opseg podataka grafikona", "SSE.Views.ChartDataDialog.textDelete": "Ukloni", "SSE.Views.ChartDataDialog.textDown": "<PERSON><PERSON>", "SSE.Views.ChartDataDialog.textEdit": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textInvalidRange": "Nevažeći opseg ćelija ", "SSE.Views.ChartDataDialog.textSelectData": "Odaberi podatak", "SSE.Views.ChartDataDialog.textSeries": "<PERSON><PERSON> (serije)", "SSE.Views.ChartDataDialog.textSwitch": "Zameni red/kolonu", "SSE.Views.ChartDataDialog.textTitle": "Podaci gra<PERSON>a", "SSE.Views.ChartDataDialog.textUp": "<PERSON>", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Postoji greška u formuli koju ste uneli.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Referenca nije validna. Referenca mora biti do otvorenog lista.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Maksimalan broj tačaka u serijama po grafikonu je 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Maks<PERSON>lan broj serija podataka po grafikonu je 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Referenca nije validna. Reference za <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ve<PERSON><PERSON><PERSON> ili etikete podataka mora biti jedna <PERSON>, red, ili kolona.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Da biste kreirali grafi<PERSON>, seri<PERSON> moraju sadržati bar jednu vrednost.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Pogrešan redosled redova. Da biste napravili grafikon akcija, postavite podatke na listu u sledećem redosledu:<br> p<PERSON><PERSON><PERSON><PERSON> cena, ma<PERSON><PERSON><PERSON><PERSON> cena, <PERSON>na cena, z<PERSON><PERSON><PERSON><PERSON> cena.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Nevažeći opseg ćelija ", "SSE.Views.ChartDataRangeDialog.textSelectData": "Odaberi podatak", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Opseg oznake ose", "SSE.Views.ChartDataRangeDialog.txtChoose": "Odaberi opseg", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "<PERSON><PERSON> se<PERSON>ja", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Oznake ose", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "<PERSON>redi se<PERSON>", "SSE.Views.ChartDataRangeDialog.txtValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtXValues": "X vrednosti", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y vrednosti ", "SSE.Views.ChartSettings.errorMaxRows": "Maks<PERSON>lan broj serija podataka po grafikonu je 255.", "SSE.Views.ChartSettings.strLineWeight": "Težina linije", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "Šablon", "SSE.Views.ChartSettings.text3dDepth": "Dubina (% od baze)", "SSE.Views.ChartSettings.text3dHeight": "Visina (% od osnovice)", "SSE.Views.ChartSettings.text3dRotation": "3D Rotacija ", "SSE.Views.ChartSettings.textAdvanced": "Prikaži napredna podešavanja ", "SSE.Views.ChartSettings.textAutoscale": "AutoRazmeravanje", "SSE.Views.ChartSettings.textBorderSizeErr": "Uneta vrednost je netačna.<br><PERSON><PERSON><PERSON> unesite vrednost između 0 tačke i 1584 tačke.", "SSE.Views.ChartSettings.textChangeType": "Promeni tip", "SSE.Views.ChartSettings.textChartType": "Promeni tip grafikona", "SSE.Views.ChartSettings.textDefault": "Podrazumevana Rotacija", "SSE.Views.ChartSettings.textDown": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textEditData": "Uredi podatke i lokaciju", "SSE.Views.ChartSettings.textFirstPoint": "Prva tačka", "SSE.Views.ChartSettings.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textHighPoint": "Visoka tačka", "SSE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON> propor<PERSON>", "SSE.Views.ChartSettings.textLastPoint": "Poslednja tačka", "SSE.Views.ChartSettings.textLeft": "Levo", "SSE.Views.ChartSettings.textLowPoint": "Niska tačka", "SSE.Views.ChartSettings.textMarkers": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textNarrow": "Usko vidno polje", "SSE.Views.ChartSettings.textNegativePoint": "Negativna tačka", "SSE.Views.ChartSettings.textPerspective": "Perspek<PERSON>va", "SSE.Views.ChartSettings.textRanges": "Opseg podataka", "SSE.Views.ChartSettings.textRight": "Des<PERSON>", "SSE.Views.ChartSettings.textRightAngle": "Ose pod pravim uglom", "SSE.Views.ChartSettings.textSelectData": "Odaberi podatak", "SSE.Views.ChartSettings.textShow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSize": "Veličina ", "SSE.Views.ChartSettings.textStyle": "Stil", "SSE.Views.ChartSettings.textSwitch": "Zameni Red/Kolonu", "SSE.Views.ChartSettings.textType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textUp": "<PERSON>", "SSE.Views.ChartSettings.textWiden": "Proširi vidno polje", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON> ", "SSE.Views.ChartSettings.textX": "X rotacija", "SSE.Views.ChartSettings.textY": "Y rotacija", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "GREŠKA! Maksimum broj tačaka u serijama po grafikonu je 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "GREŠKA! Maksimum broj serije podataka po grafikonu je 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Pogrešan redosled redova. Da biste napravili grafikon akcija, postavite podatke na listu u sledećem redosledu:<br> p<PERSON><PERSON><PERSON><PERSON> cena, ma<PERSON><PERSON><PERSON><PERSON> cena, <PERSON>na cena, z<PERSON><PERSON><PERSON><PERSON> cena.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Ne pomeraj ili menjaj sa <PERSON>", "SSE.Views.ChartSettingsDlg.textAlt": "Alternativni tekst", "SSE.Views.ChartSettingsDlg.textAltDescription": "Opis", "SSE.Views.ChartSettingsDlg.textAltTip": "Alternativni tekstualni prikaz informacija o vizuelnom objektu, koji će se čitati osobama sa oštećenjem vida ili kognitivnih sposobnosti kako bi im pomogao da bolje razumeju koje informacije se nalaze na slici, ob<PERSON>u, grafikonu ili tabeli.", "SSE.Views.ChartSettingsDlg.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAuto": "Auto", "SSE.Views.ChartSettingsDlg.textAutoEach": "Auto za svaki", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Presecanje <PERSON>", "SSE.Views.ChartSettingsDlg.textAxisOptions": "<PERSON><PERSON> op<PERSON>je", "SSE.Views.ChartSettingsDlg.textAxisPos": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisSettings": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBase": "Baza", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Između k<PERSON>č<PERSON>", "SSE.Views.ChartSettingsDlg.textBillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBottom": "Dno", "SSE.Views.ChartSettingsDlg.textCategoryName": "Kategorija ime", "SSE.Views.ChartSettingsDlg.textCenter": "Centar", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Grafikon elementi i<br>Grafikon legenda", "SSE.Views.ChartSettingsDlg.textChartTitle": "Grafikon naslov", "SSE.Views.ChartSettingsDlg.textCross": "Krst", "SSE.Views.ChartSettingsDlg.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDataColumns": "u kolonama", "SSE.Views.ChartSettingsDlg.textDataLabels": "Etikete Podataka", "SSE.Views.ChartSettingsDlg.textDataRows": "u redovima", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Prikaži legendu", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Sakrivene i prazne ćelije", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Povežite tačke podataka sa linijom", "SSE.Views.ChartSettingsDlg.textFit": "Prilagodi širini ", "SSE.Views.ChartSettingsDlg.textFixed": "Fiksirano", "SSE.Views.ChartSettingsDlg.textFormat": "Etiketa format", "SSE.Views.ChartSettingsDlg.textGaps": "Praznine", "SSE.Views.ChartSettingsDlg.textGridLines": "Mrežne linije", "SSE.Views.ChartSettingsDlg.textGroup": "Grupiši iskrice", "SSE.Views.ChartSettingsDlg.textHide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHideAxis": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHigh": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxis": "<PERSON><PERSON><PERSON> osa", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Sekunda<PERSON> horizontalna osa", "SSE.Views.ChartSettingsDlg.textHorizontal": "Horizontalno", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Stotine", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Unutrašnje <PERSON>", "SSE.Views.ChartSettingsDlg.textInnerTop": "Unutrašnji Vrh", "SSE.Views.ChartSettingsDlg.textInvalidRange": "GREŠKA! Nevažeći opseg ćelija", "SSE.Views.ChartSettingsDlg.textLabelDist": "Udaljenost oznake ose", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Interval između etike<PERSON>", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Opcije etike<PERSON>", "SSE.Views.ChartSettingsDlg.textLabelPos": "Pozicija etikete", "SSE.Views.ChartSettingsDlg.textLayout": "Struktura", "SSE.Views.ChartSettingsDlg.textLeft": "Levo", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Levo preklapanje", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Dno", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Levo", "SSE.Views.ChartSettingsDlg.textLegendPos": "<PERSON>a", "SSE.Views.ChartSettingsDlg.textLegendRight": "Des<PERSON>", "SSE.Views.ChartSettingsDlg.textLegendTop": "Vrh", "SSE.Views.ChartSettingsDlg.textLines": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLocationRange": "Opseg lokacije", "SSE.Views.ChartSettingsDlg.textLogScale": "Logaritmička skala", "SSE.Views.ChartSettingsDlg.textLow": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajor": "Glavno", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Glavni i manji", "SSE.Views.ChartSettingsDlg.textMajorType": "Glavni tip", "SSE.Views.ChartSettingsDlg.textManual": "Manualno", "SSE.Views.ChartSettingsDlg.textMarkers": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Interval između oz<PERSON>", "SSE.Views.ChartSettingsDlg.textMaxValue": "Maks<PERSON>um vrednost", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinorType": "Manji tip", "SSE.Views.ChartSettingsDlg.textMinValue": "Minimalna vrednoat", "SSE.Views.ChartSettingsDlg.textNextToAxis": "<PERSON>red ose", "SSE.Views.ChartSettingsDlg.textNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Bez preklapanja", "SSE.Views.ChartSettingsDlg.textOneCell": "Pomeri ali ne menjaj sa <PERSON>", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Na oznakama kvačice", "SSE.Views.ChartSettingsDlg.textOut": "<PERSON><PERSON><PERSON>z", "SSE.Views.ChartSettingsDlg.textOuterTop": "Spoljni vrh", "SSE.Views.ChartSettingsDlg.textOverlay": "Preklapanje", "SSE.Views.ChartSettingsDlg.textReverse": "Vrednosti u obrnutom redosledu ", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Obrnuti red", "SSE.Views.ChartSettingsDlg.textRight": "Des<PERSON>", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Desno preklapanje", "SSE.Views.ChartSettingsDlg.textRotated": "Rotirano", "SSE.Views.ChartSettingsDlg.textSameAll": "Isto za sve", "SSE.Views.ChartSettingsDlg.textSelectData": "Odaberi podatak", "SSE.Views.ChartSettingsDlg.textSeparator": "Separator etiketa podataka", "SSE.Views.ChartSettingsDlg.textSeriesName": "<PERSON><PERSON> se<PERSON>ja", "SSE.Views.ChartSettingsDlg.textShow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowBorders": "Prika<PERSON><PERSON> grani<PERSON> gra<PERSON>a", "SSE.Views.ChartSettingsDlg.textShowData": "Prikaži podatke u skrivenim redovima i kolonama ", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Prikaži prazne ćelije kao", "SSE.Views.ChartSettingsDlg.textShowEquation": "Prikaži jednačinu na grafikonu", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowValues": "Prikaži vrednosti grafikona", "SSE.Views.ChartSettingsDlg.textSingle": "Jedna is<PERSON>", "SSE.Views.ChartSettingsDlg.textSmooth": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSnap": "Priključivanje ćelija", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Iskrica opsezi", "SSE.Views.ChartSettingsDlg.textStraight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textStyle": "Stil", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTickOptions": "Opcije obeležja", "SSE.Views.ChartSettingsDlg.textTitle": "Grafikon - nap<PERSON>na <PERSON>ša<PERSON>", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Iskrica - Napredna podešavanja", "SSE.Views.ChartSettingsDlg.textTop": "Vrh", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Opcije trenda linije", "SSE.Views.ChartSettingsDlg.textTrillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTwoCell": "Pomeraj ili menjaj sa <PERSON>", "SSE.Views.ChartSettingsDlg.textType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTypeData": "Tip i Podaci", "SSE.Views.ChartSettingsDlg.textUnits": "Prika<PERSON><PERSON> j<PERSON>", "SSE.Views.ChartSettingsDlg.textValue": "Vrednost ", "SSE.Views.ChartSettingsDlg.textVertAxis": "Vertikalna osa", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Se<PERSON><PERSON>rna vertikalna osa", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Osa X naslov", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y osa naslov", "SSE.Views.ChartSettingsDlg.textZero": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.txtEmpty": "<PERSON>vo polje je neophodno", "SSE.Views.ChartTypeDialog.errorComboSeries": "Da biste napravili kombinovani grafikon, odaberite bar dve serije podataka.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Odabrani tip grafikona zahteva sekundarnu osu koju postojeći grafikon koristi. Odaberite drugi tip grafikona.", "SSE.Views.ChartTypeDialog.textSecondary": "Se<PERSON><PERSON><PERSON> osa", "SSE.Views.ChartTypeDialog.textSeries": "Serije", "SSE.Views.ChartTypeDialog.textStyle": "Stil", "SSE.Views.ChartTypeDialog.textTitle": "<PERSON><PERSON> g<PERSON>", "SSE.Views.ChartTypeDialog.textType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartWizardDialog.errorComboSeries": "Da biste napravili kombinovani grafikon, odaberite bar dve serije podataka.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "Maksimalan broj tačaka u serijama po grafikonu je 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "Maks<PERSON>lan broj serija podataka po grafikonu je 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "Odabrani tip grafikona zahteva sekundarnu osu koju postojeći grafikon koristi. Odaberite drugi tip grafikona.", "SSE.Views.ChartWizardDialog.errorStockChart": "Netačan red redova. Da biste napravili grafikon zaliha postavite podatke na list u sledećem redosledu: ot<PERSON><PERSON><PERSON><PERSON><PERSON> cena, ma<PERSON><PERSON><PERSON><PERSON> cena, <PERSON><PERSON> cena, zat<PERSON><PERSON><PERSON><PERSON><PERSON> cena.", "SSE.Views.ChartWizardDialog.textRecommended": "Preporučeno", "SSE.Views.ChartWizardDialog.textSecondary": "Se<PERSON><PERSON><PERSON> osa", "SSE.Views.ChartWizardDialog.textSeries": "Serije", "SSE.Views.ChartWizardDialog.textTitle": "Ubaci grafikon", "SSE.Views.ChartWizardDialog.textTitleChange": "Promeni tip grafikona", "SSE.Views.ChartWizardDialog.textType": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Odaberi tip grafikona i osu za tvoje serije podataka", "SSE.Views.CreatePivotDialog.textDataRange": "Opseg izvora podataka", "SSE.Views.CreatePivotDialog.textDestination": "Odaberi gde da staviš tabelu", "SSE.Views.CreatePivotDialog.textExist": "Postojeći list", "SSE.Views.CreatePivotDialog.textInvalidRange": "Nevažeći opseg ćelija ", "SSE.Views.CreatePivotDialog.textNew": "Novi list", "SSE.Views.CreatePivotDialog.textSelectData": "Odaberi podatak", "SSE.Views.CreatePivotDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> pivot tabelu", "SSE.Views.CreatePivotDialog.txtEmpty": "<PERSON>vo polje je neophodno", "SSE.Views.CreateSparklineDialog.textDataRange": "Opseg izvora podataka", "SSE.Views.CreateSparklineDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON>, gde da <PERSON>š iskrice", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Nevažeći opseg ćelija ", "SSE.Views.CreateSparklineDialog.textSelectData": "Odaberi podatak", "SSE.Views.CreateSparklineDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> is<PERSON>", "SSE.Views.CreateSparklineDialog.txtEmpty": "<PERSON>vo polje je neophodno", "SSE.Views.DataTab.capBtnGroup": "Grupa", "SSE.Views.DataTab.capBtnTextCustomSort": "Prilagođeno Sortiranje", "SSE.Views.DataTab.capBtnTextDataValidation": "Podaci validacija", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Ukloni Duplikate", "SSE.Views.DataTab.capBtnTextToCol": "Tekst u kolone", "SSE.Views.DataTab.capBtnUngroup": "Razgrupiši", "SSE.Views.DataTab.capDataExternalLinks": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capDataFromText": "<PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capGoalSeek": "Traženje Cilja", "SSE.Views.DataTab.mniFromFile": "Iz lokalnog TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "Sa TXT/CSV Web Adrese", "SSE.Views.DataTab.mniFromXMLFile": "Iz lokalnog XML-a", "SSE.Views.DataTab.textBelow": "Kolone sa rezimeom ispod detalja", "SSE.Views.DataTab.textClear": "<PERSON><PERSON><PERSON><PERSON> pre<PERSON>", "SSE.Views.DataTab.textColumns": "Razgrupiši kolone", "SSE.Views.DataTab.textGroupColumns": "Grupne kolone", "SSE.Views.DataTab.textGroupRows": "Grupni <PERSON>ovi", "SSE.Views.DataTab.textRightOf": "Kolone sa rezimeom desno od detalja", "SSE.Views.DataTab.textRows": "Razgrupiši redove", "SSE.Views.DataTab.tipCustomSort": "Prilagođeno <PERSON>", "SSE.Views.DataTab.tipDataFromText": "Dobij podatke iz fajla", "SSE.Views.DataTab.tipDataValidation": "Podaci validacija", "SSE.Views.DataTab.tipExternalLinks": "Pogledajte druge fajlove sa kojima je povezana ova proračunska tabela", "SSE.Views.DataTab.tipGoalSeek": "Pronađite odgovarajući unos za vrednost koju želite", "SSE.Views.DataTab.tipGroup": "Grupni opseg ćelija", "SSE.Views.DataTab.tipRemDuplicates": "Ukloni duple redove iz lista", "SSE.Views.DataTab.tipToColumns": "Odvoji tekst ćelija u kolone", "SSE.Views.DataTab.tipUngroup": "Razgrupiši opseg ćelija ", "SSE.Views.DataValidationDialog.errorFormula": "Vrednost se trenutno procenjuje kao greška. Da li želite da nastavite?", "SSE.Views.DataValidationDialog.errorInvalid": "Vrednost koju ste uneli za polje \"{0}\" je neva<PERSON>a.", "SSE.Views.DataValidationDialog.errorInvalidDate": "Da<PERSON> koji ste uneli za polje \"{0}\" je neva<PERSON>.", "SSE.Views.DataValidationDialog.errorInvalidList": "Izvor liste mora biti lista sa razdelnicima ili referenca na pojedinačni red ili kolonu.", "SSE.Views.DataValidationDialog.errorInvalidTime": "<PERSON><PERSON><PERSON> koje ste uneli za polje \"{0}\" je neva<PERSON>e.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "\"{1}\" polje mora biti veće od ili jednako sa \"{0}\" poljem.", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Morate uneti vrednost u oba polja \"{0}\" i polje \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Morate uneti vrednost u polje \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "Imenovani opseg koji ste naznačili ne može biti pronađen.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Negativne vrednosti se ne mogu koristiti u uslovima \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "Polje \"{0}\" mora imati numeričku vrednost, numeričku ekspresiju, ili se odnositi na ćeliju koja sadrži numeričku vrednost.", "SSE.Views.DataValidationDialog.strError": "Greška uzbuna", "SSE.Views.DataValidationDialog.strInput": "Unos poruka", "SSE.Views.DataValidationDialog.strSettings": "Podešavanja", "SSE.Views.DataValidationDialog.textAlert": "Alarm", "SSE.Views.DataValidationDialog.textAllow": "Dozvoli", "SSE.Views.DataValidationDialog.textApply": "Primeni ove promene na sve ostale ćelije sa istim podešavanjima", "SSE.Views.DataValidationDialog.textCellSelected": "<PERSON>da je <PERSON><PERSON> o<PERSON>, p<PERSON><PERSON><PERSON> ovu unos poruku", "SSE.Views.DataValidationDialog.textCompare": "Up<PERSON>i sa", "SSE.Views.DataValidationDialog.textData": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndDate": "<PERSON><PERSON><PERSON> datum", "SSE.Views.DataValidationDialog.textEndTime": "Vreme završetka", "SSE.Views.DataValidationDialog.textError": "Greška poruka", "SSE.Views.DataValidationDialog.textFormula": "Formula", "SSE.Views.DataValidationDialog.textIgnore": "Ignoriši prazno", "SSE.Views.DataValidationDialog.textInput": "Unos poruka", "SSE.Views.DataValidationDialog.textMax": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMessage": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMin": "Minimum ", "SSE.Views.DataValidationDialog.textSelectData": "Odaberi podatak", "SSE.Views.DataValidationDialog.textShowDropDown": "Prikaži padajuću listu u ćeliji", "SSE.Views.DataValidationDialog.textShowError": "Prikaži upozorenje o greš<PERSON> nakon što je nevažeći podatak unet", "SSE.Views.DataValidationDialog.textShowInput": "Prikaži poruku za unos kada je ćelija odabrana", "SSE.Views.DataValidationDialog.textSource": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStartDate": "Početni datum", "SSE.Views.DataValidationDialog.textStartTime": "Početno vreme", "SSE.Views.DataValidationDialog.textStop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStyle": "Stil", "SSE.Views.DataValidationDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textUserEnters": "Kada korisnik unese nevažeći podatak, prika<PERSON>i ovo upozorenje o grešci", "SSE.Views.DataValidationDialog.txtAny": "Bilo koja vrednost", "SSE.Views.DataValidationDialog.txtBetween": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtDate": "Datum", "SSE.Views.DataValidationDialog.txtDecimal": "Decimalni", "SSE.Views.DataValidationDialog.txtElTime": "<PERSON>šlo vreme", "SSE.Views.DataValidationDialog.txtEndDate": "<PERSON><PERSON><PERSON> datum", "SSE.Views.DataValidationDialog.txtEndTime": "Vreme završetka", "SSE.Views.DataValidationDialog.txtEqual": "j<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtGreaterThan": "veće od", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "veće od ili jednako sa", "SSE.Views.DataValidationDialog.txtLength": "Dužina", "SSE.Views.DataValidationDialog.txtLessThan": "manje od", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "manje od ili jednako sa", "SSE.Views.DataValidationDialog.txtList": "Lista", "SSE.Views.DataValidationDialog.txtNotBetween": "nije i<PERSON>", "SSE.Views.DataValidationDialog.txtNotEqual": "ne jednači se", "SSE.Views.DataValidationDialog.txtOther": "Drugo", "SSE.Views.DataValidationDialog.txtStartDate": "Početni datum", "SSE.Views.DataValidationDialog.txtStartTime": "Početno vreme", "SSE.Views.DataValidationDialog.txtTextLength": "Tekst dužina", "SSE.Views.DataValidationDialog.txtTime": "Vreme", "SSE.Views.DataValidationDialog.txtWhole": "<PERSON><PERSON> broj", "SSE.Views.DigitalFilterDialog.capAnd": "I", "SSE.Views.DigitalFilterDialog.capCondition1": "j<PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition10": "ne zav<PERSON>va se sa", "SSE.Views.DigitalFilterDialog.capCondition11": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition12": "ne <PERSON><PERSON><PERSON>i", "SSE.Views.DigitalFilterDialog.capCondition2": "ne jednači se", "SSE.Views.DigitalFilterDialog.capCondition3": "je veće od", "SSE.Views.DigitalFilterDialog.capCondition30": "je posle", "SSE.Views.DigitalFilterDialog.capCondition4": "je veće ili jednako sa", "SSE.Views.DigitalFilterDialog.capCondition40": "je posle ili jednako sa", "SSE.Views.DigitalFilterDialog.capCondition5": "je manje od", "SSE.Views.DigitalFilterDialog.capCondition50": "je pre", "SSE.Views.DigitalFilterDialog.capCondition6": "je manje od ili jednako sa", "SSE.Views.DigitalFilterDialog.capCondition60": "je pre ili jednako sa", "SSE.Views.DigitalFilterDialog.capCondition7": "po<PERSON><PERSON><PERSON> sa", "SSE.Views.DigitalFilterDialog.capCondition8": "ne počinje sa", "SSE.Views.DigitalFilterDialog.capCondition9": "završava se sa", "SSE.Views.DigitalFilterDialog.capOr": "<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.textNoFilter": "bez <PERSON>a ", "SSE.Views.DigitalFilterDialog.textShowRows": "Prikaži redove gde", "SSE.Views.DigitalFilterDialog.textUse1": "Koristi ? da predstaviš bilo koji pojedinačni karakter", "SSE.Views.DigitalFilterDialog.textUse2": "<PERSON><PERSON><PERSON> * da predstaviš bilo koju seriju ka<PERSON>tera", "SSE.Views.DigitalFilterDialog.txtSelectDate": "<PERSON><PERSON><PERSON><PERSON> datum ", "SSE.Views.DigitalFilterDialog.txtTitle": "Prilagođeni filter", "SSE.Views.DocumentHolder.advancedEquationText": "Podešavanja j<PERSON>nač<PERSON> ", "SSE.Views.DocumentHolder.advancedImgText": "Napredna podešavanja slike", "SSE.Views.DocumentHolder.advancedShapeText": "Oblik napredna podešavanja ", "SSE.Views.DocumentHolder.advancedSlicerText": "Sečenje napredna podešavanja", "SSE.Views.DocumentHolder.allLinearText": "Sve - Linearno", "SSE.Views.DocumentHolder.allProfText": "Sve - Profesionalno ", "SSE.Views.DocumentHolder.bottomCellText": "Poravnaj Dno", "SSE.Views.DocumentHolder.bulletsText": "Tačkice i Numerisanje", "SSE.Views.DocumentHolder.centerCellText": "Porav<PERSON><PERSON> s<PERSON>", "SSE.Views.DocumentHolder.chartDataText": "Odaberi podatke grafikona", "SSE.Views.DocumentHolder.chartText": "Grafikon napredna podešavanja", "SSE.Views.DocumentHolder.chartTypeText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.currLinearText": "Trenutno - Linearno", "SSE.Views.DocumentHolder.currProfText": "Trenutno - Profesionalno", "SSE.Views.DocumentHolder.deleteColumnText": "Kolona", "SSE.Views.DocumentHolder.deleteRowText": "Red", "SSE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.direct270Text": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.direct90Text": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.DocumentHolder.directHText": "Horizontalno", "SSE.Views.DocumentHolder.directionText": "Tekst pravac", "SSE.Views.DocumentHolder.editChartText": "<PERSON><PERSON><PERSON> ", "SSE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.hideEqToolbar": "<PERSON><PERSON><PERSON><PERSON> al<PERSON> traku jed<PERSON>", "SSE.Views.DocumentHolder.insertColumnLeftText": "Kolona levo", "SSE.Views.DocumentHolder.insertColumnRightText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.insertRowAboveText": "Red Iznad", "SSE.Views.DocumentHolder.insertRowBelowText": "Red Ispod", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.removeHyperlinkText": "Ukloni Hiperlink", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.selectDataText": "Podaci o koloni", "SSE.Views.DocumentHolder.selectRowText": "Red", "SSE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.showEqToolbar": "Prikaži Traku Sa Alatkama Jednačine ", "SSE.Views.DocumentHolder.strDelete": "Ukloni Potpis", "SSE.Views.DocumentHolder.strDetails": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.strSetup": "Podeša<PERSON><PERSON>", "SSE.Views.DocumentHolder.strSign": "Potpiši", "SSE.Views.DocumentHolder.textAlign": "Poravnaj", "SSE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.DocumentHolder.textArrangeForward": "<PERSON><PERSON><PERSON> napred", "SSE.Views.DocumentHolder.textArrangeFront": "Dovedi u prednji plan", "SSE.Views.DocumentHolder.textAverage": "Prosečno", "SSE.Views.DocumentHolder.textBullets": "Tač<PERSON>ce", "SSE.Views.DocumentHolder.textCopyCells": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCount": "<PERSON>zbroj", "SSE.Views.DocumentHolder.textCrop": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFit": "Prilagodi", "SSE.Views.DocumentHolder.textEditPoints": "<PERSON><PERSON><PERSON> ", "SSE.Views.DocumentHolder.textEntriesList": "Odaberi iz padajuće liste", "SSE.Views.DocumentHolder.textFillDays": "<PERSON><PERSON><PERSON> dane", "SSE.Views.DocumentHolder.textFillFormatOnly": "Ispuni samo formatiranje", "SSE.Views.DocumentHolder.textFillMonths": "Ispuni mesece", "SSE.Views.DocumentHolder.textFillSeries": "Ispuni serije", "SSE.Views.DocumentHolder.textFillWeekdays": "<PERSON><PERSON><PERSON> radne dane", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Ispuni bez formatiranja", "SSE.Views.DocumentHolder.textFillYears": "<PERSON><PERSON><PERSON> godine", "SSE.Views.DocumentHolder.textFlashFill": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textFlipH": "Obrni <PERSON>", "SSE.Views.DocumentHolder.textFlipV": "Obrn<PERSON> Vertikalno", "SSE.Views.DocumentHolder.textFreezePanes": "Zamrz<PERSON>", "SSE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textFromStorage": "<PERSON>z S<PERSON>", "SSE.Views.DocumentHolder.textFromUrl": "Iz URL", "SSE.Views.DocumentHolder.textGrowthTrend": "Trend rasta", "SSE.Views.DocumentHolder.textLinearTrend": "Linearni trend", "SSE.Views.DocumentHolder.textListSettings": "Podešavanja Liste", "SSE.Views.DocumentHolder.textMacro": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textMax": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "Više funk<PERSON>", "SSE.Views.DocumentHolder.textMoreFormats": "Više formata", "SSE.Views.DocumentHolder.textNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textNumbering": "Numerisanje", "SSE.Views.DocumentHolder.textReplace": "Zameni sliku", "SSE.Views.DocumentHolder.textResetCrop": "Resetujte izrezivanje", "SSE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate270": "Rotiraj 90° Suprotno Od Smera Kazaljke Na Satu", "SSE.Views.DocumentHolder.textRotate90": "Rotiraj 90° U Smeru Kazaljke Na Satu", "SSE.Views.DocumentHolder.textSaveAsPicture": "Saču<PERSON><PERSON> kao sliku", "SSE.Views.DocumentHolder.textSeries": "Serije", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Poravnaj dno", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Poravnaj centar", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Poravnaj levo", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Porav<PERSON><PERSON> s<PERSON>", "SSE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> gore", "SSE.Views.DocumentHolder.textShapesMerge": "<PERSON><PERSON><PERSON> oblike", "SSE.Views.DocumentHolder.textStdDev": "Standardna Devijacija", "SSE.Views.DocumentHolder.textSum": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.DocumentHolder.textUnFreezePanes": "Odmrzni okvire", "SSE.Views.DocumentHolder.textVar": "Promen<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersArrow": "Strelica tačkice", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Tačkice sa kvačicom", "SSE.Views.DocumentHolder.tipMarkersDash": "Tačkice sa crticom", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Popunjene romb tačkice", "SSE.Views.DocumentHolder.tipMarkersFRound": "Popunjene okrugle tačkice", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Popunjene kvadratne tačkice ", "SSE.Views.DocumentHolder.tipMarkersHRound": "Prazne okrugle tačkice", "SSE.Views.DocumentHolder.tipMarkersStar": "Zvezda tačkice", "SSE.Views.DocumentHolder.topCellText": "<PERSON><PERSON><PERSON><PERSON><PERSON> gore", "SSE.Views.DocumentHolder.txtAccounting": "Računovodstvo", "SSE.Views.DocumentHolder.txtAddComment": "<PERSON><PERSON><PERSON> k<PERSON>", "SSE.Views.DocumentHolder.txtAddNamedRange": "<PERSON><PERSON><PERSON><PERSON><PERSON> ime", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Auto prilagodi širinu kolone", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Auto prilagodi visinu reda", "SSE.Views.DocumentHolder.txtAverage": "Prosečno", "SSE.Views.DocumentHolder.txtCellFormat": "Format ćelije", "SSE.Views.DocumentHolder.txtClear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearAll": "Sve", "SSE.Views.DocumentHolder.txtClearComments": "Komentari", "SSE.Views.DocumentHolder.txtClearFormat": "Format", "SSE.Views.DocumentHolder.txtClearHyper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearPivotField": "<PERSON><PERSON><PERSON><PERSON> filter iz {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Obriši odabrane grupe iskrica", "SSE.Views.DocumentHolder.txtClearSparklines": "Obriši odabrane iskrice", "SSE.Views.DocumentHolder.txtClearText": "Tekst", "SSE.Views.DocumentHolder.txtCollapse": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCollapseEntire": "<PERSON><PERSON><PERSON><PERSON> celo polje", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON> kolona", "SSE.Views.DocumentHolder.txtColumnWidth": "<PERSON><PERSON> kolo<PERSON>", "SSE.Views.DocumentHolder.txtCondFormat": "Uslovno formatiranje", "SSE.Views.DocumentHolder.txtCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCount": "<PERSON>zbroj", "SSE.Views.DocumentHolder.txtCurrency": "Valuta", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Prilagođena <PERSON> kolo<PERSON>", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Prilagođena visina reda", "SSE.Views.DocumentHolder.txtCustomSort": "Prilagođeno <PERSON>", "SSE.Views.DocumentHolder.txtCut": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDateLong": "Potpuni Datum", "SSE.Views.DocumentHolder.txtDateShort": "Kratak <PERSON>tum", "SSE.Views.DocumentHolder.txtDelete": "Izbriši", "SSE.Views.DocumentHolder.txtDelField": "Ukloni", "SSE.Views.DocumentHolder.txtDescending": "Silazno", "SSE.Views.DocumentHolder.txtDifference": "Razlika od", "SSE.Views.DocumentHolder.txtDistribHor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDistribVert": "Raz<PERSON><PERSON> vert<PERSON>lno", "SSE.Views.DocumentHolder.txtEditComment": "<PERSON><PERSON><PERSON> komenta<PERSON>", "SSE.Views.DocumentHolder.txtEditObject": "<PERSON>z<PERSON><PERSON> obje<PERSON>", "SSE.Views.DocumentHolder.txtExpand": "Proširi ", "SSE.Views.DocumentHolder.txtExpandCollapse": "Proširi/Naruši", "SSE.Views.DocumentHolder.txtExpandEntire": "Proširi celo polje", "SSE.Views.DocumentHolder.txtFieldSettings": "Polje podešavanja", "SSE.Views.DocumentHolder.txtFilter": "Filter", "SSE.Views.DocumentHolder.txtFilterCellColor": "<PERSON><PERSON><PERSON><PERSON> prema b<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtFilterFontColor": "<PERSON><PERSON><PERSON><PERSON> prema boji fonta", "SSE.Views.DocumentHolder.txtFilterValue": "Filtriraj prema odabranoj vrednosti ćelije", "SSE.Views.DocumentHolder.txtFormula": "Ubaci funkciju", "SSE.Views.DocumentHolder.txtFraction": "Razlomak", "SSE.Views.DocumentHolder.txtGeneral": "General<PERSON>", "SSE.Views.DocumentHolder.txtGetLink": "Dobij link za ovaj opseg", "SSE.Views.DocumentHolder.txtGrandTotal": "Konačni zbir", "SSE.Views.DocumentHolder.txtGroup": "Grupa", "SSE.Views.DocumentHolder.txtHide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtIndex": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtInsert": "Ubaci", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hiperlink", "SSE.Views.DocumentHolder.txtInsImage": "Ubaci sliku iz fajla", "SSE.Views.DocumentHolder.txtInsImageUrl": "Ubaci sliku iz URL", "SSE.Views.DocumentHolder.txtLabelFilter": "<PERSON><PERSON><PERSON><PERSON> filteri", "SSE.Views.DocumentHolder.txtMax": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtMin": "Min", "SSE.Views.DocumentHolder.txtMoreOptions": "Više opcija", "SSE.Views.DocumentHolder.txtNormal": "Bez kalk<PERSON>je", "SSE.Views.DocumentHolder.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtNumFormat": "Format broja", "SSE.Views.DocumentHolder.txtPaste": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtPercent": "% od", "SSE.Views.DocumentHolder.txtPercentage": "Procenat", "SSE.Views.DocumentHolder.txtPercentDiff": "% razlika od", "SSE.Views.DocumentHolder.txtPercentOfCol": "% kolone ukupno", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% od ukupnog iznosa", "SSE.Views.DocumentHolder.txtPercentOfParent": "% od roditeljskog ukupno", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% od ukupnog broja roditeljskih kolona", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% od roditeljskog reda ukupno", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% tekući ukupno u", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% od reda ukupno", "SSE.Views.DocumentHolder.txtPivotSettings": "Pivot Tabela podešavanja", "SSE.Views.DocumentHolder.txtProduct": "Proizvod", "SSE.Views.DocumentHolder.txtRankAscending": "Rangiraj od najmanjeg ka najvećem", "SSE.Views.DocumentHolder.txtRankDescending": "Rangiraj od najvećeg ka najmanjem", "SSE.Views.DocumentHolder.txtReapply": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtRefresh": "Osveži", "SSE.Views.DocumentHolder.txtRow": "Ceo red", "SSE.Views.DocumentHolder.txtRowHeight": "<PERSON><PERSON> visinu reda", "SSE.Views.DocumentHolder.txtRunTotal": "Ukupan z<PERSON> u", "SSE.Views.DocumentHolder.txtScientific": "Na<PERSON>čno", "SSE.Views.DocumentHolder.txtSelect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftDown": "Pomeri <PERSON>", "SSE.Views.DocumentHolder.txtShiftLeft": "Pomeri ć<PERSON>", "SSE.Views.DocumentHolder.txtShiftRight": "Pomeri ć<PERSON>", "SSE.Views.DocumentHolder.txtShiftUp": "Pomeri ćelije gore", "SSE.Views.DocumentHolder.txtShow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShowAs": "Prikaži vrednosti kao", "SSE.Views.DocumentHolder.txtShowComment": "Prikaži komentar", "SSE.Views.DocumentHolder.txtShowDetails": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSort": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.DocumentHolder.txtSortCellColor": "Odabrana boja ćeli<PERSON> na vrhu", "SSE.Views.DocumentHolder.txtSortFontColor": "Odabrana boja fonta na vrhu", "SSE.Views.DocumentHolder.txtSortOption": "Više opcija sortiranja", "SSE.Views.DocumentHolder.txtSparklines": "Iskrice", "SSE.Views.DocumentHolder.txtSubtotalField": "Podsuma", "SSE.Views.DocumentHolder.txtSum": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSummarize": "Sumiraj vrednosti po", "SSE.Views.DocumentHolder.txtText": "Tekst", "SSE.Views.DocumentHolder.txtTextAdvanced": "Paragraf <PERSON>šavan<PERSON> ", "SSE.Views.DocumentHolder.txtTime": "Vreme", "SSE.Views.DocumentHolder.txtTop10": "Top 10", "SSE.Views.DocumentHolder.txtUngroup": "Razgrupiši", "SSE.Views.DocumentHolder.txtValueFieldSettings": "Vrednosna polja podešavanja", "SSE.Views.DocumentHolder.txtValueFilter": "Vrednost filteri", "SSE.Views.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON> ", "SSE.Views.DocumentHolder.unicodeText": "Unikod", "SSE.Views.DocumentHolder.vertAlignText": "Vertikalno poravnanje ", "SSE.Views.ExternalLinksDlg.closeButtonText": "Zatvori", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Automatski ažuriraj podatke iz povezanih izvora", "SSE.Views.ExternalLinksDlg.textChange": "Promeni izvor", "SSE.Views.ExternalLinksDlg.textDelete": "Prekini linkove", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Prekini sve linkove", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textOpen": "Otvoreni izvor", "SSE.Views.ExternalLinksDlg.textSource": "<PERSON><PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.textStatus": "Status", "SSE.Views.ExternalLinksDlg.textUnknown": "Nepoznato ", "SSE.Views.ExternalLinksDlg.textUpdate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Ažuriraj sve", "SSE.Views.ExternalLinksDlg.textUpdating": "Ažuriranje...", "SSE.Views.ExternalLinksDlg.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.strLayout": "Struktura", "SSE.Views.FieldSettingsDialog.strSubtotals": "Podsumi", "SSE.Views.FieldSettingsDialog.textNumFormat": "Format broja", "SSE.Views.FieldSettingsDialog.textReport": "Podnesi izveštaj formulara", "SSE.Views.FieldSettingsDialog.textTitle": "Polje podešavanja", "SSE.Views.FieldSettingsDialog.txtAverage": "Prosečno", "SSE.Views.FieldSettingsDialog.txtBlank": "Ubaci prazne redove posle svake stavke", "SSE.Views.FieldSettingsDialog.txtBottom": "Prikaži na dnu grupe", "SSE.Views.FieldSettingsDialog.txtCompact": "Kompaktno", "SSE.Views.FieldSettingsDialog.txtCount": "<PERSON>zbroj", "SSE.Views.FieldSettingsDialog.txtCountNums": "Izbroj brojeve", "SSE.Views.FieldSettingsDialog.txtCustomName": "<PERSON><PERSON><PERSON><PERSON><PERSON> ime", "SSE.Views.FieldSettingsDialog.txtEmpty": "Prikaži stavke bez podataka", "SSE.Views.FieldSettingsDialog.txtMax": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "Kontura", "SSE.Views.FieldSettingsDialog.txtProduct": "Proizvod", "SSE.Views.FieldSettingsDialog.txtRepeat": "Ponovi sve etikete stavki na svakom redu", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Prikaži podsume", "SSE.Views.FieldSettingsDialog.txtSourceName": "<PERSON><PERSON>:", "SSE.Views.FieldSettingsDialog.txtStdDev": "Standardna devijacija", "SSE.Views.FieldSettingsDialog.txtStdDevp": "Standardna devijacija populacije", "SSE.Views.FieldSettingsDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtSummarize": "Funkcije za podsume", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabular", "SSE.Views.FieldSettingsDialog.txtTop": "Prikaži od vrha grupe", "SSE.Views.FieldSettingsDialog.txtVar": "Promen<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.ariaFileMenu": "<PERSON><PERSON> da<PERSON>", "SSE.Views.FileMenu.btnBackCaption": "Otvori fajl lokaciju", "SSE.Views.FileMenu.btnCloseEditor": "Zatvori fajl", "SSE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnCreateNewCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON> kao", "SSE.Views.FileMenu.btnExitCaption": "Zatvori", "SSE.Views.FileMenu.btnExportToPDFCaption": "Izvoz u PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "Otvori", "SSE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnHistoryCaption": "Istorija Verzija", "SSE.Views.FileMenu.btnInfoCaption": "Informacije", "SSE.Views.FileMenu.btnPrintCaption": "Štampaj", "SSE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnRecentFilesCaption": "Otvori Skorašnje ", "SSE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnReturnCaption": "Nazad na Proračunsku tabelu", "SSE.Views.FileMenu.btnRightsCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveAsCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON> kao", "SSE.Views.FileMenu.btnSaveCaption": "Sačuvaj", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Sačuvaj kopiju kao", "SSE.Views.FileMenu.btnSettingsCaption": "Na<PERSON><PERSON><PERSON> ", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Prebaci na mobilnu verziju", "SSE.Views.FileMenu.btnToEditCaption": "Uredi Proračunsku tabelu", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Prazna Proračunska tabela", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Dodaj svojstvo", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplikacija", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Promeni prava pristupa", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Komentar", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Svojstvo dokumenata", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Poslednje Modifikovano Od", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Poslednje <PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "Ne", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Vlasnik", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Lokacija", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Svojstva", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Svojstvo sa ovim naslovom već postoji", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Osobe koje imaju prava", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Informacije proračunske tabele ", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Subjekat", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tagovi", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Otpremljeno", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "Da", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Promeni prava pristupa", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Osobe koje imaju prava", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "<PERSON><PERSON><PERSON>u<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Koristi 1904 datum sistem", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Separator de<PERSON><PERSON><PERSON> brojeva", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Omogući iterativno računanje", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Brz<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Sugestija fonta", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Jezik formule", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Primer:SUM;MIN;MAX;COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Prikaži funkciju pomoću iskačuće poruke", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Prikaži horizontalnu traku za pomeranje", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Ignoriši reči napisane VELIKIM SLOVIMA", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Ignoriši reči sa brojevima", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Makroi podešavanja", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "<PERSON><PERSON><PERSON><PERSON>a promena", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Maksimalne iteracije", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Prikaži dugme za opcije Nalepljenog sadržaja kada je sadržaj nalepljen", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Stil Referenci R1C1", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Regionalna podešavanja", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Primer:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTL interfejs", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Prikaži komentare u listu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Prikaži promene od ostalih korisnika ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Prikaži re<PERSON> komentare", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Pomeranje u skladu sa mrežom tokom skrolovanja", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Striktno", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Stil kartice", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Tema interfejsa", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "<PERSON><PERSON><PERSON><PERSON> separator", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Jedinica mere", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Koristi separatore zasnovane na regionalnim podešavanjima", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Prikaži vertikalnu traku za pomeranje", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Podrazumevana zoom vrednost", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Svakih 10 minuta", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Svakih 30 minuta", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Svakih 5 minuta", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "<PERSON><PERSON><PERSON> sat", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Auto-oporavka", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Auto-čuvanje", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Čuvanje među verzija", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "<PERSON><PERSON><PERSON> minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Stil Reference", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "Napredna podešavanja ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "AutoIspravka opcije...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Beloruski ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Bugarski", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Podrazumevani re<PERSON> k<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Računanje", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centimetar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Kolaboracija", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Češki", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Prilagodi brzi pristup", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Nemački", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Uređivanje i čuvanje", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Grč<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "<PERSON>a<PERSON> unos ne može biti korišćen. Možda je potreban ceo broj ili decimalni broj.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Ko-uređivanje u stvarnom vremenu. Sve promene su automatski sačuvane", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonezijski", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Inč", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Poslednje korišćeno", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Lao", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "kao OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Provera", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Tačka ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "<PERSON><PERSON> (Brazil)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "<PERSON><PERSON> (Portugal)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Prikaži dugme za Brzo Štampanje u zaglavlju uređivača", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "Dokument će biti štampan na poslednje odabranom ili podrazumevanom štampaču", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Regija", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Omogući Sve", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Omogući sve makroe bez notifikacije", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Uključi podršku za čitač ekrana", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Podrazumevani smer lista", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "<PERSON><PERSON> podešavanje će uticati samo na nove listove.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "S leva na desno", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "S desna na levo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Slovački", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Slovenski", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Onemogući sve", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Onemogući sve makroe bez obaveštenja", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "<PERSON><PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" dugme da sinhronizuješ promene koje ti i drugi pravite", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "<PERSON><PERSON><PERSON> boju trake kao pozadinu kartica", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Koristite Alt dugme da upravljate korisničkim interfejsom koristeći tastaturu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "<PERSON><PERSON><PERSON> dugme da upravljaš interfejsom korisnika koristeći tastaturu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vijetnamski", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Prikaži Notifikaciju", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Onemogući sve makroe sa obaveštenjem", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "kao <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "<PERSON>dn<PERSON> prostor ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Upozorenje ", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Sa lozinkom", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Zaštiti Proračunsku tabelu", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Sa potpisom", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Validni potpisi su dodati na proračunsku tabelu.<br>Proračunska tabela je zaštićena od uređivanja.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Osigurajte integritet proračunske tabele tako što ćete dodati<br>nevidljivi digitalni potpis", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Uredi proračunsku tabelu", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Uređivanje će da ukloni potpise iz proračunske tabele.<br><PERSON><PERSON><PERSON><PERSON>?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "<PERSON>va <PERSON>č<PERSON>ka tabela je zaštićena lozinkom", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Enkriptuj ovu proračunsku tabelu sa lozinkom", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "<PERSON><PERSON> tabela mora da bude potpisana.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Validni potpisi su dodati na proračunsku tabelu. Proračunska tabela je zaštićena od uređivanja.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Neki od digitalnih potpisa u proračunskoj tabeli su nevažeći ili ne mogu biti verifikovani. Proračunska tabela je zaštićena od uređivanja.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON><PERSON><PERSON>e", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "<PERSON><PERSON><PERSON> kao", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Sačuvaj kopiju kao", "SSE.Views.FillSeriesDialog.textAuto": "Autofil", "SSE.Views.FillSeriesDialog.textCols": "<PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textDate": "Datum", "SSE.Views.FillSeriesDialog.textDateUnit": "Jedini<PERSON> da<PERSON>a", "SSE.Views.FillSeriesDialog.textDay": "<PERSON>", "SSE.Views.FillSeriesDialog.textGrowth": "<PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textLinear": "Linearno", "SSE.Views.FillSeriesDialog.textMonth": "Mesec", "SSE.Views.FillSeriesDialog.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textSeries": "Serije u", "SSE.Views.FillSeriesDialog.textStep": "Vrednost koraka", "SSE.Views.FillSeriesDialog.textStop": "Stop vrednost", "SSE.Views.FillSeriesDialog.textTitle": "Serije", "SSE.Views.FillSeriesDialog.textTrend": "Trend", "SSE.Views.FillSeriesDialog.textType": "<PERSON><PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textWeek": "<PERSON><PERSON><PERSON> dan", "SSE.Views.FillSeriesDialog.textYear": "<PERSON><PERSON>", "SSE.Views.FillSeriesDialog.txtErrorNumber": "<PERSON>a<PERSON> unos ne može biti korišćen. Možda je potreban ceo broj ili decimalni broj.", "SSE.Views.FormatRulesEditDlg.fillColor": "<PERSON><PERSON> boju", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Upozorenje ", "SSE.Views.FormatRulesEditDlg.text2Scales": "d<PERSON><PERSON>jna skala", "SSE.Views.FormatRulesEditDlg.text3Scales": "skala sa 3 boje", "SSE.Views.FormatRulesEditDlg.textAllBorders": "<PERSON><PERSON> granice", "SSE.Views.FormatRulesEditDlg.textAppearance": "<PERSON><PERSON><PERSON> i<PERSON>", "SSE.Views.FormatRulesEditDlg.textApply": "Primeni na opseg", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automatski ", "SSE.Views.FormatRulesEditDlg.textAxis": "O<PERSON>", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Traka <PERSON>", "SSE.Views.FormatRulesEditDlg.textBold": "Podebljano", "SSE.Views.FormatRulesEditDlg.textBorder": "Granica", "SSE.Views.FormatRulesEditDlg.textBordersColor": "Granice boje", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Stil granice", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "<PERSON><PERSON> grani<PERSON>", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Ne može se dodati uslovno formatiranje.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Unutar vertikalnih granica", "SSE.Views.FormatRulesEditDlg.textClear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textColor": "Te<PERSON><PERSON> boja", "SSE.Views.FormatRulesEditDlg.textContext": "Kontekst", "SSE.Views.FormatRulesEditDlg.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Dijagonalna donja granica", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Dijagonalna gornja granica", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Unesite validnu formulu.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "Formula koju ste uneli ne vrednuje se kao broj, datum, vreme ili string.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Unesi vrednost.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "Vrednost koju ste uneli nije validan broj, datum, vreme ili string.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "Vrednost za {0} mora biti veća od vrednosti za {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Unesite broj između {0} i {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormat": "Format", "SSE.Views.FormatRulesEditDlg.textFormula": "Formula", "SSE.Views.FormatRulesEditDlg.textGradient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textIconLabel": "kada {0} {1} i", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "kada {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "kada je vrednost", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Jedan ili više opsega ikonica podataka se preklapaju.<br>Podesi opseg vrednosti ikonice podataka tako da se opsezi ne preklapaju.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Stil ikonice", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Unutrašnje granice", "SSE.Views.FormatRulesEditDlg.textInvalid": "Nevažeći opseg podataka.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "GREŠKA! Nevažeći opseg ćelija", "SSE.Views.FormatRulesEditDlg.textItalic": "Kurziv", "SSE.Views.FormatRulesEditDlg.textItem": "Stavka", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Levo ka desnom", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "<PERSON><PERSON> granice", "SSE.Views.FormatRulesEditDlg.textLongBar": "naj<PERSON>ža traka", "SSE.Views.FormatRulesEditDlg.textMaximum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Maksimalna tačka", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Unutar horizontalnih granica", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Središnja tačka", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimum ", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Minimalna tačka", "SSE.Views.FormatRulesEditDlg.textNegative": "Negativno", "SSE.Views.FormatRulesEditDlg.textNewColor": "<PERSON><PERSON><PERSON><PERSON> boja", "SSE.Views.FormatRulesEditDlg.textNoBorders": "Bez granica", "SSE.Views.FormatRulesEditDlg.textNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Jedna ili više od navedenih vrednosti nije validan procenat.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Navedena {0} vrednost nije validni procenat.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Jedna ili više određenih vrednosti nije validan percentil.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Navedena vrednost {0} nije važeći procentil.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Spoljašnje granice", "SSE.Views.FormatRulesEditDlg.textPercent": "Procenat", "SSE.Views.FormatRulesEditDlg.textPercentile": "Procenat", "SSE.Views.FormatRulesEditDlg.textPosition": "Pozicija", "SSE.Views.FormatRulesEditDlg.textPositive": "Pozitivno", "SSE.Views.FormatRulesEditDlg.textPresets": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPreview": "Pregled", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Ne možete koristiti relativne reference u kriterijumima za uslovno oblikovanje za skale boja, trake podataka i setove ikonica.", "SSE.Views.FormatRulesEditDlg.textReverse": "Obrni red ikonica", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Desno ka levom", "SSE.Views.FormatRulesEditDlg.textRightBorders": "<PERSON><PERSON> granice", "SSE.Views.FormatRulesEditDlg.textRule": "Pravil<PERSON>", "SSE.Views.FormatRulesEditDlg.textSameAs": "<PERSON><PERSON> kao poziti<PERSON>", "SSE.Views.FormatRulesEditDlg.textSelectData": "Odaberi podatak", "SSE.Views.FormatRulesEditDlg.textShortBar": "najkraća traka", "SSE.Views.FormatRulesEditDlg.textShowBar": "Prikaži samo traku", "SSE.Views.FormatRulesEditDlg.textShowIcon": "P<PERSON>ž<PERSON> sa<PERSON> i<PERSON>", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Ova vrsta reference ne može se koristiti u formuli za uslovno formatiranje.<br>Promenite referencu na jednu <PERSON>, ili koristite referencu sa funkcijom lista, kao što je =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Solidno", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Precrtano", "SSE.Views.FormatRulesEditDlg.textSubscript": "<PERSON><PERSON> in<PERSON>s", "SSE.Views.FormatRulesEditDlg.textSuperscript": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Gornje granice", "SSE.Views.FormatRulesEditDlg.textUnderline": "<PERSON>d<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipBorders": "Granice", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Format broja", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Računovodstvo", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Valuta", "SSE.Views.FormatRulesEditDlg.txtDate": "Datum", "SSE.Views.FormatRulesEditDlg.txtDateLong": "Potpuni datum", "SSE.Views.FormatRulesEditDlg.txtDateShort": "Kratak datum", "SSE.Views.FormatRulesEditDlg.txtEmpty": "<PERSON>vo polje je neophodno", "SSE.Views.FormatRulesEditDlg.txtFraction": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtGeneral": "General<PERSON>", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "Bez ikonice", "SSE.Views.FormatRulesEditDlg.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Procenat", "SSE.Views.FormatRulesEditDlg.txtScientific": "Na<PERSON>čno", "SSE.Views.FormatRulesEditDlg.txtText": "Tekst", "SSE.Views.FormatRulesEditDlg.txtTime": "Vreme", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Uredi pravilo format<PERSON>", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Novo pravilo formatiranja", "SSE.Views.FormatRulesManagerDlg.guestText": "Gost", "SSE.Views.FormatRulesManagerDlg.lockText": "Zak<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 standardna devijacija iznad proseka", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 standardna devijacija ispod proseka", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 standardne devijacije iznad proseka", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 standardne devijacije ispod proseka", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 standardne devijacije iznad proseka", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 standardne devijacije ispod proseka", "SSE.Views.FormatRulesManagerDlg.textAbove": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.FormatRulesManagerDlg.textApply": "Primeni na", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Vrednost ćelije počinje sa", "SSE.Views.FormatRulesManagerDlg.textBelow": "Ispod <PERSON>", "SSE.Views.FormatRulesManagerDlg.textBetween": "je iz<PERSON><PERSON><PERSON> {0} i {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Vrednost ćelije", "SSE.Views.FormatRulesManagerDlg.textColorScale": "<PERSON><PERSON><PERSON><PERSON><PERSON> boja", "SSE.Views.FormatRulesManagerDlg.textContains": "Vrednost ćelije sadrži", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Ćelija sadrži praznu vrednost", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Ćelija sadrži grešku", "SSE.Views.FormatRulesManagerDlg.textDelete": "Izbriši", "SSE.Views.FormatRulesManagerDlg.textDown": "Pomeri pravilo dole", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Dupliciraj vrednosti", "SSE.Views.FormatRulesManagerDlg.textEdit": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEnds": "Vrednost ćelije se završava sa", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Jednako sa ili iznad proseka", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Jednako sa ili ispod proseka", "SSE.Views.FormatRulesManagerDlg.textFormat": "Format", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Set ikonica", "SSE.Views.FormatRulesManagerDlg.textNew": "Novo", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "nije i<PERSON> {0} i {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Vrednost ćelije ne sadrži", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Ćelija ne sadrži praznu vrednost", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Ćelija ne sadrži grešku", "SSE.Views.FormatRulesManagerDlg.textRules": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textScope": "Prikaži pravila formatiranja za", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Odaberi podatak", "SSE.Views.FormatRulesManagerDlg.textSelection": "Trenutna <PERSON>", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "<PERSON><PERSON><PERSON> pivot", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Ovaj list", "SSE.Views.FormatRulesManagerDlg.textThisTable": "<PERSON><PERSON> tabela", "SSE.Views.FormatRulesManagerDlg.textUnique": "Jedinstvene vrednosti", "SSE.Views.FormatRulesManagerDlg.textUp": "Pomeri pravilo gore", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Ovaj element se uređuje od strane drugog korisnika.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Uslovno formatiranje", "SSE.Views.FormatSettingsDialog.textCategory": "Kategorija", "SSE.Views.FormatSettingsDialog.textDecimal": "Decimalni", "SSE.Views.FormatSettingsDialog.textFormat": "Format", "SSE.Views.FormatSettingsDialog.textLinked": "Linkovano do izvora", "SSE.Views.FormatSettingsDialog.textSeparator": "<PERSON><PERSON>i 1000 separator", "SSE.Views.FormatSettingsDialog.textSymbols": "Simboli", "SSE.Views.FormatSettingsDialog.textTitle": "Format broja", "SSE.Views.FormatSettingsDialog.txtAccounting": "Računovodstvo", "SSE.Views.FormatSettingsDialog.txtAs10": "<PERSON><PERSON> (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "<PERSON><PERSON> <PERSON> (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "<PERSON><PERSON> (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "<PERSON><PERSON> (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "<PERSON><PERSON> (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "<PERSON><PERSON> (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Valuta", "SSE.Views.FormatSettingsDialog.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Molimo vas unesite prilagođeni format broja pažljivo. Editor proračunske tabele ne proverava prilagođene formate na greške koje mogu uticati na xlsx datoteku.", "SSE.Views.FormatSettingsDialog.txtDate": "Datum", "SSE.Views.FormatSettingsDialog.txtFraction": "Razlomak", "SSE.Views.FormatSettingsDialog.txtGeneral": "General<PERSON>", "SSE.Views.FormatSettingsDialog.txtNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtPercentage": "Procenat", "SSE.Views.FormatSettingsDialog.txtSample": "Uzorak:", "SSE.Views.FormatSettingsDialog.txtScientific": "Na<PERSON>čno", "SSE.Views.FormatSettingsDialog.txtText": "Tekst", "SSE.Views.FormatSettingsDialog.txtTime": "Vreme", "SSE.Views.FormatSettingsDialog.txtUpto1": "<PERSON> jedne cifre (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "<PERSON> dve cifre (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Do tri cifre (131/135)", "SSE.Views.FormulaDialog.sDescription": "Opis", "SSE.Views.FormulaDialog.textGroupDescription": "Odaberi funkcionalnu grupu", "SSE.Views.FormulaDialog.textListDescription": "Odaberi funkci<PERSON>", "SSE.Views.FormulaDialog.txtRecommended": "Preporučeno", "SSE.Views.FormulaDialog.txtSearch": "Pretraga", "SSE.Views.FormulaDialog.txtTitle": "Ubaci funkciju", "SSE.Views.FormulaTab.capBtnRemoveArr": "Ukloni strelice", "SSE.Views.FormulaTab.capBtnTraceDep": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.capBtnTracePrec": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.textAutomatic": "Automatski ", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Izračunaj trenutni list", "SSE.Views.FormulaTab.textCalculateWorkbook": "Izračunaj radnu knjigu", "SSE.Views.FormulaTab.textManual": "Manualno", "SSE.Views.FormulaTab.tipCalculate": "Izračunaj", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Izračunaj celu radnu knjigu ", "SSE.Views.FormulaTab.tipRemoveArr": "Ukloni strelice nacrtane pomoću Praćenja Prethodnika ili Praćenja Zavisnika", "SSE.Views.FormulaTab.tipShowFormulas": "Prikaži formulu u svakoj ćeliji umesto rezultirajuće vrednosti", "SSE.Views.FormulaTab.tipTraceDep": "Prikaži strelice koje pokazuju koje ćelije su pod uticajem vrednosti odabranih ćelija", "SSE.Views.FormulaTab.tipTracePrec": "Prikaži strelice koje pokazuju koje ćelije utiču na vrednost odabrane ćelije", "SSE.Views.FormulaTab.tipWatch": "Dodaj ćelije na listu Prozora za praćenje ", "SSE.Views.FormulaTab.txtAdditional": "Dodatno", "SSE.Views.FormulaTab.txtAutosum": "Automatski zbir", "SSE.Views.FormulaTab.txtAutosumTip": "Sumacija", "SSE.Views.FormulaTab.txtCalculation": "Proračun", "SSE.Views.FormulaTab.txtFormula": "Funkcija", "SSE.Views.FormulaTab.txtFormulaTip": "Ubaci funkciju", "SSE.Views.FormulaTab.txtMore": "Više funk<PERSON>", "SSE.Views.FormulaTab.txtRecent": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtRemDep": "Ukloni strelice z<PERSON>ti", "SSE.Views.FormulaTab.txtRemPrec": "Ukloni strelice prethodnika", "SSE.Views.FormulaTab.txtShowFormulas": "Prikaži formule", "SSE.Views.FormulaTab.txtWatch": "Prozor Za Praćenje", "SSE.Views.FormulaWizard.textAny": "bilo koji", "SSE.Views.FormulaWizard.textArgument": "Argument", "SSE.Views.FormulaWizard.textFunction": "Funkcija", "SSE.Views.FormulaWizard.textFunctionRes": "Rezultat funkcije", "SSE.Views.FormulaWizard.textHelp": "Pomoć na ovoj funkciji", "SSE.Views.FormulaWizard.textLogical": "logično", "SSE.Views.FormulaWizard.textNoArgs": "<PERSON>va funkcija nema argumente", "SSE.Views.FormulaWizard.textNoArgsDesc": "ovaj argument nema opis", "SSE.Views.FormulaWizard.textNumber": "broj", "SSE.Views.FormulaWizard.textReadMore": "Opširnije", "SSE.Views.FormulaWizard.textRef": "referenca", "SSE.Views.FormulaWizard.textText": "tekst", "SSE.Views.FormulaWizard.textTitle": "Argumenti <PERSON>", "SSE.Views.FormulaWizard.textValue": "Rezultat formule", "SSE.Views.GoalSeekDlg.textChangingCell": "Menjajući ćeliju", "SSE.Views.GoalSeekDlg.textDataRangeError": "Formuli nedostaje opseg", "SSE.Views.GoalSeekDlg.textMustContainFormula": "Ćelija mora sadržati formulu", "SSE.Views.GoalSeekDlg.textMustContainValue": "Ćelija mora sadržati vrednost", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Formula u ćeliji mora imati brojčani rezultat", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Referenca mora biti na jednu ćeliju", "SSE.Views.GoalSeekDlg.textSelectData": "Odaberi podatak", "SSE.Views.GoalSeekDlg.textSetCell": "<PERSON><PERSON>", "SSE.Views.GoalSeekDlg.textTitle": "Traženje cilja", "SSE.Views.GoalSeekDlg.textToValue": "Do vrednosti", "SSE.Views.GoalSeekDlg.txtEmpty": "<PERSON>vo polje je neophodno", "SSE.Views.GoalSeekDlg.txtErrorNumber": "<PERSON>a<PERSON> unos ne može biti korišćen. Možda je potreban ceo broj ili decimalni broj.", "SSE.Views.GoalSeekStatusDlg.textContinue": "<PERSON><PERSON><PERSON>", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Trenutna vrednost:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Traženje cilja sa ćelijom {0} pro<PERSON><PERSON><PERSON>.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Traženje cilja sa ćelijom {0} možda nije pronašao re<PERSON>je.", "SSE.Views.GoalSeekStatusDlg.textPause": "<PERSON><PERSON>", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Traženje cilja sa ćelijom {0} na iteraciji #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "<PERSON><PERSON>", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Target vrednost:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Traženje cilja status", "SSE.Views.HeaderFooterDialog.textAlign": "Porav<PERSON>j sa <PERSON>ama stranice", "SSE.Views.HeaderFooterDialog.textAll": "<PERSON>ve stranice", "SSE.Views.HeaderFooterDialog.textBold": "Podebljano", "SSE.Views.HeaderFooterDialog.textCenter": "Centar", "SSE.Views.HeaderFooterDialog.textColor": "Te<PERSON><PERSON> boja", "SSE.Views.HeaderFooterDialog.textDate": "Datum", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Različita prva stranica", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Različite neparne i parne stranice", "SSE.Views.HeaderFooterDialog.textEven": "Jednaka stranica", "SSE.Views.HeaderFooterDialog.textFileName": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textFirst": "Prva stranica", "SSE.Views.HeaderFooterDialog.textFooter": "Podnožje", "SSE.Views.HeaderFooterDialog.textHeader": "Zaglavlje", "SSE.Views.HeaderFooterDialog.textImage": "Slika", "SSE.Views.HeaderFooterDialog.textInsert": "Ubaci", "SSE.Views.HeaderFooterDialog.textItalic": "Kurziv", "SSE.Views.HeaderFooterDialog.textLeft": "Levo", "SSE.Views.HeaderFooterDialog.textMaxError": "Tekst string koji ste uneli je predugačak. Smanjite broj karaktera koji je koriš<PERSON>en.", "SSE.Views.HeaderFooterDialog.textNewColor": "<PERSON><PERSON><PERSON><PERSON> boja", "SSE.Views.HeaderFooterDialog.textOdd": "Neparna stranica", "SSE.Views.HeaderFooterDialog.textPageCount": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textPageNum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textPresets": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textRight": "Des<PERSON>", "SSE.Views.HeaderFooterDialog.textScale": "Prilagodi razmeri uz dokument", "SSE.Views.HeaderFooterDialog.textSheet": "List ime", "SSE.Views.HeaderFooterDialog.textStrikeout": "Precrtano", "SSE.Views.HeaderFooterDialog.textSubscript": "<PERSON><PERSON> in<PERSON>s", "SSE.Views.HeaderFooterDialog.textSuperscript": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textTime": "Vreme", "SSE.Views.HeaderFooterDialog.textTitle": "Zaglavlje/Podnožje podešavanja", "SSE.Views.HeaderFooterDialog.textUnderline": "<PERSON>d<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.tipFontName": "Font", "SSE.Views.HeaderFooterDialog.tipFontSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Link do", "SSE.Views.HyperlinkSettingsDialog.strRange": "Opseg", "SSE.Views.HyperlinkSettingsDialog.strSheet": "List", "SSE.Views.HyperlinkSettingsDialog.textCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Odabrani Opseg", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Unesi opis ovde", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Unesi link ovde", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Unesite opis alatke ovde", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Spoljni link", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Dobij link", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Unutrašnji opseg podataka", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "GREŠKA! Nevažeći opseg ćelija", "SSE.Views.HyperlinkSettingsDialog.textNames": "Definisana imena", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Odaberi podatak", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "<PERSON><PERSON><PERSON><PERSON> datoteku", "SSE.Views.HyperlinkSettingsDialog.textSheets": "<PERSON>ov<PERSON>", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Tekst za savet o ekranu", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Hiperlink podešavanja", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON>vo polje je neophodno", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Ovo polje bi trebalo da bude URL u \"http://www.example.com\" formatu", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Ovo polje je ograničeno na 2083 karaktera", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Unesi web adresu ili odaberi datoteku", "SSE.Views.ImageSettings.strTransparency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textAdvanced": "Prikaži napredna podešavanja ", "SSE.Views.ImageSettings.textCrop": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFill": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFit": "Prilagodi", "SSE.Views.ImageSettings.textCropToShape": "<PERSON><PERSON><PERSON> da oblikuješ", "SSE.Views.ImageSettings.textEdit": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textEditObject": "<PERSON><PERSON><PERSON> ob<PERSON>", "SSE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textFromFile": "<PERSON><PERSON> fajla", "SSE.Views.ImageSettings.textFromStorage": "<PERSON>z s<PERSON>š<PERSON>", "SSE.Views.ImageSettings.textFromUrl": "Iz URL", "SSE.Views.ImageSettings.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textHint270": "Rotiraj 90° Suprotno Od Smera Kazaljke Na Satu", "SSE.Views.ImageSettings.textHint90": "Rotiraj 90° u Smeru Kazaljke Na Satu", "SSE.Views.ImageSettings.textHintFlipH": "Obrni horizontalno", "SSE.Views.ImageSettings.textHintFlipV": "<PERSON><PERSON><PERSON><PERSON> vertikalno", "SSE.Views.ImageSettings.textInsert": "Zameni sliku", "SSE.Views.ImageSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON> propor<PERSON>", "SSE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textRecentlyUsed": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textResetCrop": "Resetujte izrezivanje", "SSE.Views.ImageSettings.textRotate90": "Rotiraj 90°", "SSE.Views.ImageSettings.textRotation": "Rotacija", "SSE.Views.ImageSettings.textSize": "Veličina ", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON> ", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Ne pomeraj ili menjaj sa <PERSON>", "SSE.Views.ImageSettingsAdvanced.textAlt": "Alternativni tekst", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Opis", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Alternativni tekstualni prikaz informacija o vizuelnom objektu, koji će se čitati osobama sa oštećenjem vida ili kognitivnih sposobnosti kako bi im pomogao da bolje razumeju koje informacije se nalaze na slici, ob<PERSON>u, grafikonu ili tabeli.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAngle": "Ugao", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Obrn<PERSON>", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontalno", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Pomeri ali ne menjaj sa <PERSON>", "SSE.Views.ImageSettingsAdvanced.textRotation": "Rotacija", "SSE.Views.ImageSettingsAdvanced.textSnap": "Priključivanje ćelija", "SSE.Views.ImageSettingsAdvanced.textTitle": "Slika - Napredna podešavanja ", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Pomeraj ili menjaj sa <PERSON>", "SSE.Views.ImageSettingsAdvanced.textVertically": "Vertikalno", "SSE.Views.ImportFromXmlDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON>, gde da staviš podatke", "SSE.Views.ImportFromXmlDialog.textExist": "Postojeći list", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Nevažeći opseg ćelija ", "SSE.Views.ImportFromXmlDialog.textNew": "Novi list", "SSE.Views.ImportFromXmlDialog.textSelectData": "Odaberi podatak", "SSE.Views.ImportFromXmlDialog.textTitle": "<PERSON><PERSON><PERSON> pod<PERSON>", "SSE.Views.ImportFromXmlDialog.txtEmpty": "<PERSON>vo polje je neophodno", "SSE.Views.LeftMenu.ariaLeftMenu": "<PERSON> meni", "SSE.Views.LeftMenu.tipAbout": "U vezi", "SSE.Views.LeftMenu.tipChat": "Čet", "SSE.Views.LeftMenu.tipComments": "Komentari", "SSE.Views.LeftMenu.tipFile": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSearch": "Pretraga", "SSE.Views.LeftMenu.tipSpellcheck": "Provera pravopisa ", "SSE.Views.LeftMenu.tipSupport": "Povratne Informacije i Podrška", "SSE.Views.LeftMenu.txtDeveloper": "REŽIM RAZVOJNOG PROGRAMERA", "SSE.Views.LeftMenu.txtEditor": "Uređ<PERSON>č Proračunske Tabele", "SSE.Views.LeftMenu.txtLimit": "Ogranič<PERSON> pristup", "SSE.Views.LeftMenu.txtTrial": "PROBNI REŽIM", "SSE.Views.LeftMenu.txtTrialDev": "Probni Režim Razvojnog Programera", "SSE.Views.MacroDialog.textMacro": "<PERSON><PERSON><PERSON> ime", "SSE.Views.MacroDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.okButtonText": "Sačuvaj", "SSE.Views.MainSettingsPrint.strBottom": "Dno", "SSE.Views.MainSettingsPrint.strLandscape": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLeft": "Levo", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPrint": "Štampaj", "SSE.Views.MainSettingsPrint.strPrintTitles": "Štampaj naslove", "SSE.Views.MainSettingsPrint.strRight": "Des<PERSON>", "SSE.Views.MainSettingsPrint.strTop": "Vrh", "SSE.Views.MainSettingsPrint.textActualSize": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textCustomOptions": "Prilagođene opcije", "SSE.Views.MainSettingsPrint.textFitCols": "Prilagodi sve kolone na jednu stranicu ", "SSE.Views.MainSettingsPrint.textFitPage": "Prilagodi list na jednu stranicu ", "SSE.Views.MainSettingsPrint.textFitRows": "Prilagodi sve redove na jednu stranicu", "SSE.Views.MainSettingsPrint.textPageOrientation": "Orijentacija stranice", "SSE.Views.MainSettingsPrint.textPageScaling": "Prilagođavanje razmeri", "SSE.Views.MainSettingsPrint.textPageSize": "Veličina stranice", "SSE.Views.MainSettingsPrint.textPrintGrid": "Štampaj mrežne linije", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Štampaj naslove redova i kolona", "SSE.Views.MainSettingsPrint.textRepeat": "<PERSON><PERSON><PERSON>...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Ponovi kolone levo", "SSE.Views.MainSettingsPrint.textRepeatTop": "Ponovi redove na vrhu", "SSE.Views.MainSettingsPrint.textSettings": "Podešavanja za", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Postojeći imenovani opsezi ne mogu biti uređeni i novi ne mogu biti kreirani<br>u ovom trenutku zato što se neki od njih uređuju.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Definisano ime", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Upozorenje ", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Radna s<PERSON>ka", "SSE.Views.NamedRangeEditDlg.textDataRange": "Opseg podataka", "SSE.Views.NamedRangeEditDlg.textExistName": "GREŠKA! Opseg sa takvim imenom već postoji", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Ime mora početi sa slovom ili donjom crticom i ne sme sadržati nevažeće karaktere.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "GREŠKA! Nevažeći opseg ćelije", "SSE.Views.NamedRangeEditDlg.textIsLocked": "GREŠKA! Ovaj element se uređuje od strane drugog korisnika.", "SSE.Views.NamedRangeEditDlg.textName": "Ime", "SSE.Views.NamedRangeEditDlg.textReservedName": "<PERSON><PERSON> koje poku<PERSON>vate da koristite je već referencirano u ćelijskim formulama. <PERSON><PERSON> vas koristite neko drugo ime.", "SSE.Views.NamedRangeEditDlg.textScope": "Obim", "SSE.Views.NamedRangeEditDlg.textSelectData": "Odaberi podatak", "SSE.Views.NamedRangeEditDlg.txtEmpty": "<PERSON>vo polje je neophodno", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "<PERSON><PERSON>i ime", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Novo ime", "SSE.Views.NamedRangePasteDlg.textNames": "Imenovani opsezi", "SSE.Views.NamedRangePasteDlg.txtTitle": "<PERSON><PERSON><PERSON> ime", "SSE.Views.NameManagerDlg.closeButtonText": "Zatvori", "SSE.Views.NameManagerDlg.guestText": "Gost", "SSE.Views.NameManagerDlg.lockText": "Zak<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textDataRange": "Opseg podataka", "SSE.Views.NameManagerDlg.textDelete": "Izbriši", "SSE.Views.NameManagerDlg.textEdit": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEmpty": "<PERSON><PERSON><PERSON> imenovani opseg nije jo<PERSON> k<PERSON>.<br>Kreirajte bar jedan imenovani opseg i on će se pojaviti u ovom polju.", "SSE.Views.NameManagerDlg.textFilter": "Filter", "SSE.Views.NameManagerDlg.textFilterAll": "Sve", "SSE.Views.NameManagerDlg.textFilterDefNames": "Definisana imena", "SSE.Views.NameManagerDlg.textFilterSheet": "Imena ograničena na listu", "SSE.Views.NameManagerDlg.textFilterTableNames": "<PERSON><PERSON><PERSON> imena", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Imena ograničena na radnu knjigu", "SSE.Views.NameManagerDlg.textNew": "Novo", "SSE.Views.NameManagerDlg.textnoNames": "<PERSON><PERSON><PERSON> imenovani opseg koji se poklapa sa vašim filterom nije mogao biti nađen.", "SSE.Views.NameManagerDlg.textRanges": "Imenovani opsezi", "SSE.Views.NameManagerDlg.textScope": "Obim", "SSE.Views.NameManagerDlg.textWorkbook": "Radna s<PERSON>ka", "SSE.Views.NameManagerDlg.tipIsLocked": "Ovaj element se uređuje od strane drugog korisnika.", "SSE.Views.NameManagerDlg.txtTitle": "<PERSON><PERSON><PERSON><PERSON> imena", "SSE.Views.NameManagerDlg.warnDelete": "Da li ste sigurni da želite da izbrišete ime {0}?", "SSE.Views.PageMarginsDialog.textBottom": "Dno", "SSE.Views.PageMarginsDialog.textCenter": "Centriraj na stranicu", "SSE.Views.PageMarginsDialog.textHor": "Horizontalno", "SSE.Views.PageMarginsDialog.textLeft": "Levo", "SSE.Views.PageMarginsDialog.textRight": "Des<PERSON>", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "Vrh", "SSE.Views.PageMarginsDialog.textVert": "Vertikalno", "SSE.Views.PageMarginsDialog.textWarning": "Upozorenje ", "SSE.Views.PageMarginsDialog.warnCheckMargings": "Margine su netačne", "SSE.Views.ParagraphSettings.strLineHeight": "Razmak između linija", "SSE.Views.ParagraphSettings.strParagraphSpacing": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingAfter": "Posle", "SSE.Views.ParagraphSettings.strSpacingBefore": "Pre", "SSE.Views.ParagraphSettings.textAdvanced": "Prikaži napredna podešavanja ", "SSE.Views.ParagraphSettings.textAt": "Na", "SSE.Views.ParagraphSettings.textAtLeast": "Bar", "SSE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textExact": "Tačno tako", "SSE.Views.ParagraphSettings.txtAutoText": "Auto", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Navedene kartice će se pojaviti u ovom polju", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Samo velika slova", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Uvlačenja", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Levo", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Razmak između linija", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Des<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Posle", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Pre", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Specijalni", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Po", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Font", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Uvlačenja i Razmaci", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Mali kapital", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Pravljenje razmaka", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Precrtano", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "<PERSON><PERSON> in<PERSON>s", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Kartice", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Poravnanje", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Razmak izmedju karaktera", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Podrazumevana kartica", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Efekti", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Tačno tako", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Prva linija", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Obostrano poravnan", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(nijedan)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Ukloni", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Ukloni sve", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Specifik<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Centar", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "Levo", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Kartica pozicija", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Des<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Paragraf - Napredna pod<PERSON>šavanja ", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Izbriši", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Izmeni", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Formula", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "<PERSON><PERSON> sta<PERSON>", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "Novo", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Izračunate stavke u", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "j<PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "ne zav<PERSON>va se sa", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "ne <PERSON><PERSON><PERSON>i", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "nije i<PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "ne jednači se", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "je veće od", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "je veće ili jednako sa", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "je manje od", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "je manje od ili jednako sa", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "po<PERSON><PERSON><PERSON> sa", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "ne počinje sa", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "završava se sa", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Prikaži stavke za koje etiketa:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Prikaži stavke za koje:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Koristi ? da predstaviš bilo koji pojedinačni karakter", "SSE.Views.PivotDigitalFilterDialog.textUse2": "<PERSON><PERSON><PERSON> * da predstaviš bilo koju seriju ka<PERSON>tera", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "i", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Etiketa filter", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Vrednost filter ", "SSE.Views.PivotGroupDialog.textAuto": "Auto", "SSE.Views.PivotGroupDialog.textBy": "Po", "SSE.Views.PivotGroupDialog.textDays": "<PERSON>", "SSE.Views.PivotGroupDialog.textEnd": "Završava na", "SSE.Views.PivotGroupDialog.textError": "Ovo polje mora biti numerička vrednost", "SSE.Views.PivotGroupDialog.textGreaterError": "Krajnji broj mora biti veći od početnog broja", "SSE.Views.PivotGroupDialog.textHour": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMin": "Minute", "SSE.Views.PivotGroupDialog.textMonth": "Meseci", "SSE.Views.PivotGroupDialog.textNumDays": "<PERSON><PERSON><PERSON> <PERSON>na", "SSE.Views.PivotGroupDialog.textQuart": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textSec": "Sekunde", "SSE.Views.PivotGroupDialog.textStart": "Sa početkom u", "SSE.Views.PivotGroupDialog.textYear": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.txtTitle": "Grupisanje", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "Možete koristiti izračunate stavke za osnovne proračune između različitih stavki unutar jednog polja", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Umetni u formulu", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "Stavka", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "<PERSON><PERSON> stavke", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Stavke", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Pročitaj više", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Umetni izračunatu stavku u", "SSE.Views.PivotSettings.textAdvanced": "Prikaži napredna podešavanja ", "SSE.Views.PivotSettings.textColumns": "<PERSON><PERSON>", "SSE.Views.PivotSettings.textFields": "Odaberi polja", "SSE.Views.PivotSettings.textFilters": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddColumn": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddFilter": "Dodaj u filtere", "SSE.Views.PivotSettings.txtAddRow": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddValues": "<PERSON><PERSON>j vrednostima", "SSE.Views.PivotSettings.txtFieldSettings": "Polje podešavanja", "SSE.Views.PivotSettings.txtMoveBegin": "Pomeri na početak", "SSE.Views.PivotSettings.txtMoveColumn": "Pomeri na kolone", "SSE.Views.PivotSettings.txtMoveDown": "<PERSON><PERSON><PERSON> dole", "SSE.Views.PivotSettings.txtMoveEnd": "Pomeri do kraja", "SSE.Views.PivotSettings.txtMoveFilter": "Pomeri do filtera", "SSE.Views.PivotSettings.txtMoveRow": "Pomeri do redova", "SSE.Views.PivotSettings.txtMoveUp": "Po<PERSON>i gore", "SSE.Views.PivotSettings.txtMoveValues": "Pomeri do vrednosti ", "SSE.Views.PivotSettings.txtRemove": "Ukloni polje", "SSE.Views.PivotSettingsAdvanced.strLayout": "<PERSON><PERSON> i raspored", "SSE.Views.PivotSettingsAdvanced.textAlt": "Alternativni tekst", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Opis", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Alternativno tekstualno reprezentiranje informacija o vizualnom objektu, koje će biti pročitano osobama s oštećenjem vida ili kognitivnim oštećenjima kako bi im pomoglo bolje razumeti koje informacije se nalaze na slici, ob<PERSON><PERSON>, grafikonu ili tabeli.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Auto prilagodi širine kolona na ažuriranju", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Opseg podataka", "SSE.Views.PivotSettingsAdvanced.textDataSource": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Prikaži polja u oblasti izveštaja filtera", "SSE.Views.PivotSettingsAdvanced.textDown": "Do<PERSON>, onda preko", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Ukupni iznosi", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Polja zaglavlja", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "GREŠKA! Nevažeći opseg ćelija", "SSE.Views.PivotSettingsAdvanced.textOver": "Preko, onda dole", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Odaberi podatak", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Prikaži za kolone", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Prikaži zaglavlja polja za redove i kolone", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Prikaži za redove", "SSE.Views.PivotSettingsAdvanced.textTitle": "Pivot tabela - Napredna podešavanja", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Podnesi izveštaj za polja filtera po koloni", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Podnesi izveštaj za polja filtera po redu", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "<PERSON>vo polje je neophodno", "SSE.Views.PivotSettingsAdvanced.txtName": "Ime", "SSE.Views.PivotShowDetailDialog.textDescription": "Izaberite polje koje sadrži detalje koje želite da prikažete:", "SSE.Views.PivotShowDetailDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON><PERSON>i", "SSE.Views.PivotTable.capGrandTotals": "Ukupni Iznosi", "SSE.Views.PivotTable.capLayout": "Podnesi izveštaj strukture", "SSE.Views.PivotTable.capSubtotals": "Podsumi", "SSE.Views.PivotTable.mniBottomSubtotals": "Prikaži sve podsume na dnu grupe", "SSE.Views.PivotTable.mniInsertBlankLine": "Ubaci praznu liniju posle svake stavke", "SSE.Views.PivotTable.mniLayoutCompact": "Prikaži u kompaktnoj formi", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Ne ponavljaj sve etikete stavki", "SSE.Views.PivotTable.mniLayoutOutline": "Prikaz u obliku pregleda", "SSE.Views.PivotTable.mniLayoutRepeat": "Ponovi sve etikete stavki", "SSE.Views.PivotTable.mniLayoutTabular": "Prikaži u tabularnoj formi", "SSE.Views.PivotTable.mniNoSubtotals": "Ne prikazuj podsume", "SSE.Views.PivotTable.mniOffTotals": "Isključeno za redove i kolone", "SSE.Views.PivotTable.mniOnColumnsTotals": "Uključeno samo za kolone", "SSE.Views.PivotTable.mniOnRowsTotals": "Uključeno samo za redove", "SSE.Views.PivotTable.mniOnTotals": "Uključeno za redove i kolone", "SSE.Views.PivotTable.mniRemoveBlankLine": "Ukloni praznu liniju posle svake stavke", "SSE.Views.PivotTable.mniTopSubtotals": "Prikaži sve podsume na vrhu grupe", "SSE.Views.PivotTable.textColBanded": "Pojasne kolone", "SSE.Views.PivotTable.textColHeader": "Zaglavlja kolone", "SSE.Views.PivotTable.textRowBanded": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.textRowHeader": "Zaglav<PERSON><PERSON>", "SSE.Views.PivotTable.tipCalculatedItems": "Izračunate stavke", "SSE.Views.PivotTable.tipCreatePivot": "Ubaci Pivot Tabelu", "SSE.Views.PivotTable.tipGrandTotals": "Prikaži ili sakrij ukupne iznose", "SSE.Views.PivotTable.tipRefresh": "Ažuriraj informaciju iz izvora podataka", "SSE.Views.PivotTable.tipRefreshCurrent": "Ažuriraj informaciju iz izvora podataka za trenutnu tabelu", "SSE.Views.PivotTable.tipSelect": "Odaberi celu pivot tabelu", "SSE.Views.PivotTable.tipSubtotals": "Prikaži ili sakrij podsume", "SSE.Views.PivotTable.txtCalculatedItems": "Izračunate stavke", "SSE.Views.PivotTable.txtCollapseEntire": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtCreate": "U<PERSON><PERSON>", "SSE.Views.PivotTable.txtExpandEntire": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Tamno", "SSE.Views.PivotTable.txtGroupPivot_Light": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Srednje", "SSE.Views.PivotTable.txtPivotTable": "Pivot Tabela", "SSE.Views.PivotTable.txtRefresh": "Osveži", "SSE.Views.PivotTable.txtRefreshAll": "Osveži sve", "SSE.Views.PivotTable.txtSelect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Pivot tabela tamni stil", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Pivot tabela svetli stil", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Pivot tabela srednji stil", "SSE.Views.PrintSettings.btnDownload": "Sačuvaj i Preuzmi", "SSE.Views.PrintSettings.btnExport": "Sačuvaj i Izvezi", "SSE.Views.PrintSettings.btnPrint": "Sačuvaj i Štampaj", "SSE.Views.PrintSettings.strBottom": "Dno", "SSE.Views.PrintSettings.strLandscape": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strLeft": "Levo", "SSE.Views.PrintSettings.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strPrint": "Štampaj", "SSE.Views.PrintSettings.strPrintTitles": "Štampaj naslove", "SSE.Views.PrintSettings.strRight": "Des<PERSON>", "SSE.Views.PrintSettings.strShow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strTop": "Vrh", "SSE.Views.PrintSettings.textActiveSheets": "Aktivni listovi", "SSE.Views.PrintSettings.textActualSize": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textAllSheets": "<PERSON><PERSON> <PERSON><PERSON>i", "SSE.Views.PrintSettings.textCurrentSheet": "Trenutni list", "SSE.Views.PrintSettings.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textCustomOptions": "Prilagođene opcije", "SSE.Views.PrintSettings.textFitCols": "Prilagodi sve kolone na jednu stranicu ", "SSE.Views.PrintSettings.textFitPage": "Prilagodi list na jednu stranicu ", "SSE.Views.PrintSettings.textFitRows": "Prilagodi sve redove na jednu stranicu ", "SSE.Views.PrintSettings.textHideDetails": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textIgnore": "Ignoriši oblast za štampanje", "SSE.Views.PrintSettings.textLayout": "Struktura", "SSE.Views.PrintSettings.textMarginsNarrow": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textMarginsNormal": "Normalno", "SSE.Views.PrintSettings.textMarginsWide": "<PERSON><PERSON><PERSON> ", "SSE.Views.PrintSettings.textPageOrientation": "Orijentacija stranice", "SSE.Views.PrintSettings.textPages": "Stranice:", "SSE.Views.PrintSettings.textPageScaling": "Prilagođavanje razmeri", "SSE.Views.PrintSettings.textPageSize": "Veličina stranice", "SSE.Views.PrintSettings.textPrintGrid": "Štampaj mrežne linije", "SSE.Views.PrintSettings.textPrintHeadings": "Štampaj naslove redova i kolona", "SSE.Views.PrintSettings.textPrintRange": "Opseg štampe", "SSE.Views.PrintSettings.textRange": "Opseg", "SSE.Views.PrintSettings.textRepeat": "<PERSON><PERSON><PERSON>...", "SSE.Views.PrintSettings.textRepeatLeft": "Ponovi kolone levo", "SSE.Views.PrintSettings.textRepeatTop": "Ponovi redove na vrhu", "SSE.Views.PrintSettings.textSelection": "Selek<PERSON>ja", "SSE.Views.PrintSettings.textSettings": "List podešavanja", "SSE.Views.PrintSettings.textShowDetails": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textShowGrid": "Prikaži mrežne linije", "SSE.Views.PrintSettings.textShowHeadings": "Prikaži naslove redova i kolona", "SSE.Views.PrintSettings.textTitle": "Podešavanja štampe", "SSE.Views.PrintSettings.textTitlePDF": "PDF podešavanja", "SSE.Views.PrintSettings.textTo": "da biste", "SSE.Views.PrintSettings.txtMarginsLast": "Poslednje Prilagođeno", "SSE.Views.PrintTitlesDialog.textFirstCol": "Prva kolona", "SSE.Views.PrintTitlesDialog.textFirstRow": "Prvi red", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Zamrznute kolone", "SSE.Views.PrintTitlesDialog.textFrozenRows": "<PERSON><PERSON><PERSON><PERSON><PERSON> red<PERSON>i", "SSE.Views.PrintTitlesDialog.textInvalidRange": "GREŠKA! Nevažeći opseg ćelija", "SSE.Views.PrintTitlesDialog.textLeft": "Ponovi kolone levo", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Ne ponavljaj", "SSE.Views.PrintTitlesDialog.textRepeat": "<PERSON><PERSON><PERSON>...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Odaberi opseg", "SSE.Views.PrintTitlesDialog.textTitle": "Štampaj naslove", "SSE.Views.PrintTitlesDialog.textTop": "Ponovi redove na vrhu", "SSE.Views.PrintWithPreview.txtActiveSheets": "Aktivni listovi", "SSE.Views.PrintWithPreview.txtActualSize": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtAllSheets": "<PERSON><PERSON> <PERSON><PERSON>i", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Primeni na sve listove", "SSE.Views.PrintWithPreview.txtBothSides": "Štampaj na obe strane", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "Obrni stranice na dugačku ivicu", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "Obrni stranice na kratku ivicu", "SSE.Views.PrintWithPreview.txtBottom": "Dno", "SSE.Views.PrintWithPreview.txtCopies": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Trenutni list", "SSE.Views.PrintWithPreview.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCustomOptions": "Prilagođene opcije", "SSE.Views.PrintWithPreview.txtEmptyTable": "Nema ništa za štampanje zato što je tabela prazna", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "<PERSON>rvi broj stranice:", "SSE.Views.PrintWithPreview.txtFitCols": "Prilagodi Sve kolone na Jednu Stranicu ", "SSE.Views.PrintWithPreview.txtFitPage": "Prilagodi List na Jednu <PERSON> ", "SSE.Views.PrintWithPreview.txtFitRows": "Prilagodi Sve Redove na Jednu <PERSON>ranicu", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Mrežne linije i naslovi", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Zaglavlje/Podnožje podešavanja", "SSE.Views.PrintWithPreview.txtIgnore": "Ignoriši oblast za štampanje", "SSE.Views.PrintWithPreview.txtLandscape": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtLeft": "Levo", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMarginsLast": "Poslednje Prilagođeno", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Normalno", "SSE.Views.PrintWithPreview.txtMarginsWide": "<PERSON><PERSON><PERSON> ", "SSE.Views.PrintWithPreview.txtOf": "od {0}", "SSE.Views.PrintWithPreview.txtOneSide": "Štampaj jednostrano", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Samo štampaj na jednoj stranici", "SSE.Views.PrintWithPreview.txtPage": "Stranica", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "<PERSON><PERSON><PERSON> strane <PERSON> ", "SSE.Views.PrintWithPreview.txtPageOrientation": "Orijentacija stranice", "SSE.Views.PrintWithPreview.txtPages": "Stranice:", "SSE.Views.PrintWithPreview.txtPageSize": "Veličina stranice", "SSE.Views.PrintWithPreview.txtPortrait": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPrint": "Štampaj", "SSE.Views.PrintWithPreview.txtPrintGrid": "Štampaj mrežne linije", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Štampaj naslove redova i kolona", "SSE.Views.PrintWithPreview.txtPrintRange": "Opseg štampe", "SSE.Views.PrintWithPreview.txtPrintSides": "Štampaj sa strane", "SSE.Views.PrintWithPreview.txtPrintTitles": "Štampaj naslove", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Štampaj kao PDF", "SSE.Views.PrintWithPreview.txtRepeat": "<PERSON><PERSON><PERSON>...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Ponovi kolone levo", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Ponovi redove na vrhu", "SSE.Views.PrintWithPreview.txtRight": "Des<PERSON>", "SSE.Views.PrintWithPreview.txtSave": "Sačuvaj", "SSE.Views.PrintWithPreview.txtScaling": "Prilagođavanje razmeri", "SSE.Views.PrintWithPreview.txtSelection": "Selek<PERSON>ja", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Podešavanja lista", "SSE.Views.PrintWithPreview.txtSheet": "List: {0} ", "SSE.Views.PrintWithPreview.txtTo": "da biste", "SSE.Views.PrintWithPreview.txtTop": "Vrh", "SSE.Views.ProtectDialog.textExistName": "GREŠKA! Opseg sa takvim naslovom već postoji", "SSE.Views.ProtectDialog.textInvalidName": "Naslov opsega mora početi sa slovom i može sadržati samo slova, brojeve, i razmake.", "SSE.Views.ProtectDialog.textInvalidRange": "GREŠKA! Nevažeći opseg ćelija", "SSE.Views.ProtectDialog.textSelectData": "Odaberi podatak", "SSE.Views.ProtectDialog.txtAllow": "Dozvoli svim korisnicima ovog lista da", "SSE.Views.ProtectDialog.txtAllowDescription": "Možete otključati specifične opsege za uređivanje.", "SSE.Views.ProtectDialog.txtAllowRanges": "Dozvoli uređivanje opsega", "SSE.Views.ProtectDialog.txtAutofilter": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtDelCols": "Izbriši kolone", "SSE.Views.ProtectDialog.txtDelRows": "<PERSON><PERSON><PERSON><PERSON><PERSON> redove", "SSE.Views.ProtectDialog.txtEmpty": "<PERSON>vo polje je neophodno", "SSE.Views.ProtectDialog.txtFormatCells": "Format ćelije", "SSE.Views.ProtectDialog.txtFormatCols": "Format kolone", "SSE.Views.ProtectDialog.txtFormatRows": "Format redovi", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Potvrda da lozinka nije identična", "SSE.Views.ProtectDialog.txtInsCols": "Ubaci kolone", "SSE.Views.ProtectDialog.txtInsHyper": "Ubaci hip<PERSON>link", "SSE.Views.ProtectDialog.txtInsRows": "<PERSON><PERSON><PERSON> redove", "SSE.Views.ProtectDialog.txtObjs": "Uredi objekte", "SSE.Views.ProtectDialog.txtOptional": "opciono", "SSE.Views.ProtectDialog.txtPassword": "Lozinka", "SSE.Views.ProtectDialog.txtPivot": "Koristi PivotTabelu i PivotGrafikon", "SSE.Views.ProtectDialog.txtProtect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRange": "Opseg", "SSE.Views.ProtectDialog.txtRangeName": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRepeat": "Ponovi lozinku", "SSE.Views.ProtectDialog.txtScen": "<PERSON>redi scenarije", "SSE.Views.ProtectDialog.txtSelLocked": "Odaberi zaključane ćelije", "SSE.Views.ProtectDialog.txtSelUnLocked": "Odaberi otključane ćelije", "SSE.Views.ProtectDialog.txtSheetDescription": "Spreči neželjene promene od drugih ograničavajući njihovu sposobnost da uređuju.", "SSE.Views.ProtectDialog.txtSheetTitle": "Zaštiti list", "SSE.Views.ProtectDialog.txtSort": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.ProtectDialog.txtWarning": "Upozorenje: <PERSON><PERSON> i<PERSON>e ili zaboravite lozinku, ne može biti oporavljena. <PERSON><PERSON> vas čuvajte je na sigurnom mestu. ", "SSE.Views.ProtectDialog.txtWBDescription": "Da biste sprečili druge korisnike da vide sakrivene listove, doda<PERSON>, pomeraj<PERSON>, bri<PERSON>u ili sakrivaju listove, kao i da preimenuju listove, mož<PERSON> zaštititi strukturu radne sveske lozinkom.", "SSE.Views.ProtectDialog.txtWBTitle": "Zaš<PERSON>ti strukturu radne knjige", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "Anonimno", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Bilo ko", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Izmeni", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Odbijeno", "SSE.Views.ProtectedRangesEditDlg.textCanView": "Pregled", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "Naslov opsega mora početi sa slovom i može sadržati samo slova, brojeve, i razmake.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "GREŠKA! Nevažeći opseg ćelija", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Odstrani", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "Odaberi podatak", "SSE.Views.ProtectedRangesEditDlg.textYou": "vi", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "<PERSON><PERSON><PERSON> opsegu", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "<PERSON>vo polje je neophodno", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.txtRange": "Opseg", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Samo ti možeš da urediš ovaj opseg", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Počni da kucaš ime ili email", "SSE.Views.ProtectedRangesManagerDlg.guestText": "Gost", "SSE.Views.ProtectedRangesManagerDlg.lockText": "Zak<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "Izbriši", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "Nijedan zaštićen opseg nije još kreiran.<br>Kreirajte bar jedan zaštićen opseg i pojaviće se u ovom polju.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "Filter", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "Sve", "SSE.Views.ProtectedRangesManagerDlg.textNew": "Novo", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Zaštiti list", "SSE.Views.ProtectedRangesManagerDlg.textRange": "Opseg", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "Možete ograničiti uređivanje opsega za odabrane osobe.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "Ovaj element se uređuje od strane drugog korisnika.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Odbijeno", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "Uredi opseg", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "Novi opseg", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Zaštićeni opsezi", "SSE.Views.ProtectedRangesManagerDlg.txtView": "Prikaz", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Da li ste sigurni da želite da izbrišete zaštićeni opseg{0}?<br>Bilo ko sa pristupom uređivanja proračunske tabele će moći da uređuje sadržaj u opsegu.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Da li ste sigurni da želite da izbrišete zaštićene opsege?<br>Bilo ko sa pristupom uređivanja proračunske tabele će moći da uređuje sadržaj u tim opsezima.", "SSE.Views.ProtectRangesDlg.guestText": "Gost", "SSE.Views.ProtectRangesDlg.lockText": "Zak<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textDelete": "Izbriši", "SSE.Views.ProtectRangesDlg.textEdit": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEmpty": "Nema opsega dozvoljenih za uređivanje.", "SSE.Views.ProtectRangesDlg.textNew": "Novo", "SSE.Views.ProtectRangesDlg.textProtect": "Zaštiti list", "SSE.Views.ProtectRangesDlg.textPwd": "Lozinka", "SSE.Views.ProtectRangesDlg.textRange": "Opseg", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Opsezi otključani lozinkom kada je list zaštićen (ovo radi samo za zaključane ćelije)", "SSE.Views.ProtectRangesDlg.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Ovaj element se uređuje od strane drugog korisnika.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Uredi opseg", "SSE.Views.ProtectRangesDlg.txtNewRange": "Novi opseg", "SSE.Views.ProtectRangesDlg.txtNo": "Ne", "SSE.Views.ProtectRangesDlg.txtTitle": "Dozvoli korisnicima da uređuju opsege", "SSE.Views.ProtectRangesDlg.txtYes": "Da", "SSE.Views.ProtectRangesDlg.warnDelete": "Da li ste sigurni da želite da izbrišete ime {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "<PERSON><PERSON>", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Da izbrišete duplikat vrednosti, odaberite jednu ili više koloni koje sadrže duplikate.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "<PERSON><PERSON> imaju zaglavl<PERSON>", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Odaberi sve", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Ukloni duplikate", "SSE.Views.RightMenu.ariaRightMenu": "<PERSON><PERSON> meni", "SSE.Views.RightMenu.txtCellSettings": "Ćelija podešavanja", "SSE.Views.RightMenu.txtChartSettings": "Grafikon podešavanja", "SSE.Views.RightMenu.txtImageSettings": "Podešavanja slike ", "SSE.Views.RightMenu.txtParagraphSettings": "Paragraf <PERSON>", "SSE.Views.RightMenu.txtPivotSettings": "Pivot Tabela podešavanja", "SSE.Views.RightMenu.txtSettings": "Uobičajena podešavanja", "SSE.Views.RightMenu.txtShapeSettings": "Oblik podešavanja", "SSE.Views.RightMenu.txtSignatureSettings": "Podešavanja <PERSON>", "SSE.Views.RightMenu.txtSlicerSettings": "Sečenje podešavanja", "SSE.Views.RightMenu.txtSparklineSettings": "Iskrica podešavanja", "SSE.Views.RightMenu.txtTableSettings": "Podešavanja tabele", "SSE.Views.RightMenu.txtTextArtSettings": "Tekst Umetnost podešavanja", "SSE.Views.ScaleDialog.textAuto": "Auto", "SSE.Views.ScaleDialog.textError": "Uneta vrednost je netačna.", "SSE.Views.ScaleDialog.textFewPages": "stranice", "SSE.Views.ScaleDialog.textFitTo": "Prilagodi", "SSE.Views.ScaleDialog.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textManyPages": "stranice", "SSE.Views.ScaleDialog.textOnePage": "stranica", "SSE.Views.ScaleDialog.textScaleTo": "Prilagodi na", "SSE.Views.ScaleDialog.textTitle": "Razmera podešavanja", "SSE.Views.ScaleDialog.textWidth": "<PERSON><PERSON><PERSON> ", "SSE.Views.SetValueDialog.txtMaxText": "Maksimalna vrednost za ovo polje je {0}", "SSE.Views.SetValueDialog.txtMinText": "Minimalna vrednost za ovo polje je {0}", "SSE.Views.ShapeSettings.strBackground": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strChange": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strForeground": "<PERSON><PERSON> prednjeg plana", "SSE.Views.ShapeSettings.strPattern": "Šablon", "SSE.Views.ShapeSettings.strShadow": "<PERSON><PERSON><PERSON><PERSON> senku", "SSE.Views.ShapeSettings.strSize": "Veličina ", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "Neprozirnost", "SSE.Views.ShapeSettings.strType": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textAdjustShadow": "Po<PERSON><PERSON><PERSON><PERSON> senke", "SSE.Views.ShapeSettings.textAdvanced": "Prikaži napredna podešavanja ", "SSE.Views.ShapeSettings.textAngle": "Ugao", "SSE.Views.ShapeSettings.textBorderSizeErr": "Uneta vrednost je netačna.<br><PERSON><PERSON><PERSON> unesite vrednost između 0 tačke i 1584 tačke.", "SSE.Views.ShapeSettings.textColor": "Bojenje", "SSE.Views.ShapeSettings.textDirection": "Direkcija", "SSE.Views.ShapeSettings.textEditPoints": "<PERSON><PERSON><PERSON> ", "SSE.Views.ShapeSettings.textEditShape": "Uredi oblik", "SSE.Views.ShapeSettings.textEmptyPattern": "Bez š<PERSON>a", "SSE.Views.ShapeSettings.textEyedropper": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON> fajla", "SSE.Views.ShapeSettings.textFromStorage": "<PERSON>z s<PERSON>š<PERSON>", "SSE.Views.ShapeSettings.textFromUrl": "Iz URL", "SSE.Views.ShapeSettings.textGradient": "Tačke gradijenta", "SSE.Views.ShapeSettings.textGradientFill": "Gradijentno punjenje", "SSE.Views.ShapeSettings.textHint270": "Rotiraj 90° Suprotno Od Smera Kazaljke Na Satu", "SSE.Views.ShapeSettings.textHint90": "Rotiraj 90° u Smeru Kazaljke Na Satu", "SSE.Views.ShapeSettings.textHintFlipH": "Obrni horizontalno", "SSE.Views.ShapeSettings.textHintFlipV": "<PERSON><PERSON><PERSON><PERSON> vertikalno", "SSE.Views.ShapeSettings.textImageTexture": "Slika ili tekstura ", "SSE.Views.ShapeSettings.textLinear": "Linearno", "SSE.Views.ShapeSettings.textMoreColors": "<PERSON><PERSON><PERSON><PERSON> boja", "SSE.Views.ShapeSettings.textNoFill": "Bez pun<PERSON>", "SSE.Views.ShapeSettings.textNoShadow": "<PERSON><PERSON> senke", "SSE.Views.ShapeSettings.textOriginalSize": "<PERSON><PERSON> veli<PERSON>", "SSE.Views.ShapeSettings.textPatternFill": "Šablon", "SSE.Views.ShapeSettings.textPosition": "Pozicija", "SSE.Views.ShapeSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textRecentlyUsed": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textRotate90": "Rotiraj 90°", "SSE.Views.ShapeSettings.textRotation": "Rotacija", "SSE.Views.ShapeSettings.textSelectImage": "Odaberi sliku", "SSE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textShadow": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textStretch": "Ra<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStyle": "Stil", "SSE.Views.ShapeSettings.textTexture": "<PERSON>z teksture", "SSE.Views.ShapeSettings.textTile": "Pločica ", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Dodaj tačku gradijenta", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Ukloni tačku gradienta", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> papir", "SSE.Views.ShapeSettings.txtCanvas": "Platno", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGrain": "Zrno", "SSE.Views.ShapeSettings.txtGranite": "Granit", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON> papir ", "SSE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON> linije", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "Drvo ", "SSE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Razmak između teksta i okvira", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Ne pomeraj ili menjaj sa <PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Alternativni tekst", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Opis", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Alternativni tekstualni prikaz informacija o vizuelnom objektu, koji će se čitati osobama sa oštećenjem vida ili kognitivnih sposobnosti kako bi im pomogao da bolje razumeju koje informacije se nalaze na slici, ob<PERSON>u, grafikonu ili tabeli.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAngle": "Ugao", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Strelice", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "AutoPrilagođavanje", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Počni veličinu ", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON><PERSON> stil", "SSE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Dno", "SSE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Završna veli<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "<PERSON><PERSON><PERSON><PERSON><PERSON> stil", "SSE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Obrn<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontalno", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON> propor<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Levo", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Stil linije", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Pomeri ali ne menjaj sa <PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Dozvoli tekstu da preliva oblik ", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "<PERSON><PERSON><PERSON> veli<PERSON> da stane tekst", "SSE.Views.ShapeSettingsAdvanced.textRight": "Des<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Rotacija", "SSE.Views.ShapeSettingsAdvanced.textRound": "Okruglo", "SSE.Views.ShapeSettingsAdvanced.textSize": "Veličina ", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Priključivanje ćelija", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Razmak između kolona", "SSE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "<PERSON><PERSON> za tekst", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Oblik - Napredna podešavanja ", "SSE.Views.ShapeSettingsAdvanced.textTop": "Vrh", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Pomeraj ili menjaj sa <PERSON>", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Vertikalno", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Težine i strelice", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON> ", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Upozorenje ", "SSE.Views.SignatureSettings.strDelete": "Ukloni Potpis", "SSE.Views.SignatureSettings.strDetails": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strInvalid": "Nevažeći <PERSON>i", "SSE.Views.SignatureSettings.strRequested": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSetup": "Podeša<PERSON><PERSON>", "SSE.Views.SignatureSettings.strSign": "Potpiši", "SSE.Views.SignatureSettings.strSignature": "Potpis", "SSE.Views.SignatureSettings.strSigner": "Potpisnik", "SSE.Views.SignatureSettings.strValid": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.txtContinueEditing": "Uredi i<PERSON>k", "SSE.Views.SignatureSettings.txtEditWarning": "Uređivanje će da ukloni potpise iz proračunske tabele.<br><PERSON><PERSON><PERSON><PERSON>?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Da li želite da odstranite ovaj potpis?<br>Ne može se poništiti.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "<PERSON><PERSON> tabela mora da bude potpisana.", "SSE.Views.SignatureSettings.txtSigned": "Validni potpisi su dodati na proračunsku tabelu. Proračunska tabela je zaštićena od uređivanja.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Neki od digitalnih potpisa u proračunskoj tabeli su nevažeći ili ne mogu biti verifikovani. Proračunska tabela je zaštićena od uređivanja.", "SSE.Views.SlicerAddDialog.textColumns": "<PERSON><PERSON>", "SSE.Views.SlicerAddDialog.txtTitle": "Umetni <PERSON>", "SSE.Views.SlicerSettings.strHideNoData": "Sakrij stavke bez podataka", "SSE.Views.SlicerSettings.strIndNoData": "Vizuelno naznači stavke bez podataka", "SSE.Views.SlicerSettings.strShowDel": "Prikaži izbrisane stavke iz izvora podataka", "SSE.Views.SlicerSettings.strShowNoData": "Prikaži stavke bez poslednjeg podatka", "SSE.Views.SlicerSettings.strSorting": "Raspoređivanje i filtriranje", "SSE.Views.SlicerSettings.textAdvanced": "Prikaži napredna podešavanja ", "SSE.Views.SlicerSettings.textAsc": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.SlicerSettings.textAZ": "A do Z", "SSE.Views.SlicerSettings.textButtons": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textColumns": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textDesc": "Silazno", "SSE.Views.SlicerSettings.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textHor": "Horizontalno", "SSE.Views.SlicerSettings.textKeepRatio": "Konstantne Proporcije", "SSE.Views.SlicerSettings.textLargeSmall": "najveće ka najmanjem", "SSE.Views.SlicerSettings.textLock": "Onemogući promenu veličine ili pomeranje", "SSE.Views.SlicerSettings.textNewOld": "od najnovijeg do najstarijeg", "SSE.Views.SlicerSettings.textOldNew": "najstarije do najnovije", "SSE.Views.SlicerSettings.textPosition": "Pozicija", "SSE.Views.SlicerSettings.textSize": "Veličina ", "SSE.Views.SlicerSettings.textSmallLarge": "najmanje do najvećeg", "SSE.Views.SlicerSettings.textStyle": "Stil", "SSE.Views.SlicerSettings.textVert": "Vertikalno ", "SSE.Views.SlicerSettings.textWidth": "<PERSON><PERSON><PERSON> ", "SSE.Views.SlicerSettings.textZA": "Z do A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strColumns": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Sakrij stavke bez podataka", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Vizuelno naznači stavke bez podataka", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Reference", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Prikaži izbrisane stavke iz izvora podataka", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Prikaži zaglavlje", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Prikaži stavke bez poslednjeg podatka", "SSE.Views.SlicerSettingsAdvanced.strSize": "Veličina ", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Raspoređivanje i Filtriranje", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Stil", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Stil i Veličina ", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON><PERSON><PERSON> ", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Ne pomeraj ili menjaj sa <PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Alternativni tekst", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Opis", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Alternativno tekstualno reprezentiranje informacija o vizualnom objektu, koje će biti pročitano osobama s oštećenjem vida ili kognitivnim oštećenjima kako bi im pomoglo bolje razumeti koje informacije se nalaze na slici, ob<PERSON><PERSON>, grafikonu ili tabeli.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAsc": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A do Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Silazno", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "<PERSON>me da se koristi u formulama", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Zaglavlje", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON> propor<PERSON>", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "najveće ka najmanjem", "SSE.Views.SlicerSettingsAdvanced.textName": "Ime", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "od najnovijeg do najstarijeg", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "najstarije do najnovije", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Pomeri ali ne menjaj sa <PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "najmanje do najvećeg", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Priključivanje ćelija", "SSE.Views.SlicerSettingsAdvanced.textSort": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Sečenje - Napredna podešavanja", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Pomeraj ili menjaj sa <PERSON>", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z do A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "<PERSON>vo polje je neophodno", "SSE.Views.SortDialog.errorEmpty": "Svi kriterijumi sortiranja moraju imati kolonu ili red naznačen.", "SSE.Views.SortDialog.errorMoreOneCol": "Više od jedne kolone je odabrano.", "SSE.Views.SortDialog.errorMoreOneRow": "V<PERSON>še od jedne reda je odabrano.", "SSE.Views.SortDialog.errorNotOriginalCol": "Kolona koju ste odabrali nije u originalnom odabranom opsegu.", "SSE.Views.SortDialog.errorNotOriginalRow": "Red koji ste odabrali nije u originalnom odabranom opsegu.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 se sortira istom bojom više od jednog puta.<br>Izbrišite duplicirane kriterijume sortiranja i pokušajte ponovo.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 se sortira prema vrednostima više od jednog puta.<br>Izbrišite duplicirane kriterijume sortiranja i pokušajte ponovo.", "SSE.Views.SortDialog.textAsc": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.SortDialog.textAuto": "Automatski ", "SSE.Views.SortDialog.textAZ": "A do Z", "SSE.Views.SortDialog.textBelow": "Ispod", "SSE.Views.SortDialog.textBtnCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textBtnDelete": "Izbriši", "SSE.Views.SortDialog.textBtnNew": "Novo", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON><PERSON> boja", "SSE.Views.SortDialog.textColumn": "Kolona", "SSE.Views.SortDialog.textDesc": "Silazno", "SSE.Views.SortDialog.textDown": "Pomeri nivo dole", "SSE.Views.SortDialog.textFontColor": "<PERSON><PERSON>a", "SSE.Views.SortDialog.textLeft": "Levo", "SSE.Views.SortDialog.textLevels": "Nivoi", "SSE.Views.SortDialog.textMoreCols": "(<PERSON><PERSON><PERSON><PERSON> kolo<PERSON>...)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON><PERSON><PERSON>...)", "SSE.Views.SortDialog.textNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textOptions": "Opcije", "SSE.Views.SortDialog.textOrder": "Red", "SSE.Views.SortDialog.textRight": "Des<PERSON>", "SSE.Views.SortDialog.textRow": "Red", "SSE.Views.SortDialog.textSort": "Sort<PERSON>j na", "SSE.Views.SortDialog.textSortBy": "<PERSON><PERSON><PERSON><PERSON> pre<PERSON>", "SSE.Views.SortDialog.textThenBy": "Zatim po", "SSE.Views.SortDialog.textTop": "Vrh", "SSE.Views.SortDialog.textUp": "Pomeri nivo gore", "SSE.Views.SortDialog.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textZA": "Z do A", "SSE.Views.SortDialog.txtInvalidRange": "Nevažeći opseg ćelija.", "SSE.Views.SortDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.SortFilterDialog.textAsc": "<PERSON><PERSON><PERSON><PERSON><PERSON> (A do Z) od", "SSE.Views.SortFilterDialog.textDesc": "Opadajući (od Z do A) po", "SSE.Views.SortFilterDialog.textNoSort": "<PERSON><PERSON>", "SSE.Views.SortFilterDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.SortFilterDialog.txtTitleValue": "<PERSON><PERSON><PERSON><PERSON> prema v<PERSON>nos<PERSON>", "SSE.Views.SortOptionsDialog.textCase": "Osetljivo na velika/mala slova", "SSE.Views.SortOptionsDialog.textHeaders": "<PERSON><PERSON> imaju zaglavl<PERSON>", "SSE.Views.SortOptionsDialog.textLeftRight": "Sortiraj levo do desno", "SSE.Views.SortOptionsDialog.textOrientation": "Orijentacija ", "SSE.Views.SortOptionsDialog.textTitle": "Sortiraj opcije", "SSE.Views.SortOptionsDialog.textTopBottom": "Sortiraj od vrha do dna", "SSE.Views.SpecialPasteDialog.textAdd": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textAll": "Sve", "SSE.Views.SpecialPasteDialog.textBlanks": "Preskoči prazne", "SSE.Views.SpecialPasteDialog.textColWidth": "Širine kolone", "SSE.Views.SpecialPasteDialog.textComments": "Komentari", "SSE.Views.SpecialPasteDialog.textDiv": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFFormat": "Formule i formatiranje", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formule i brojni formati", "SSE.Views.SpecialPasteDialog.textFormats": "Formati", "SSE.Views.SpecialPasteDialog.textFormulas": "Formule", "SSE.Views.SpecialPasteDialog.textFWidth": "Formule i širine kolone", "SSE.Views.SpecialPasteDialog.textMult": "Pomnoži", "SSE.Views.SpecialPasteDialog.textNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textOperation": "Operacija", "SSE.Views.SpecialPasteDialog.textPaste": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textSub": "Oduzimanje ", "SSE.Views.SpecialPasteDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textTranspose": "Transponuj", "SSE.Views.SpecialPasteDialog.textValues": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textVFormat": "Vrednosti i Formatiranje", "SSE.Views.SpecialPasteDialog.textVNFormat": "Vrednosti i formati brojeva", "SSE.Views.SpecialPasteDialog.textWBorders": "Sve osim granica", "SSE.Views.Spellcheck.noSuggestions": "Bez pravopisnih sugestija ", "SSE.Views.Spellcheck.textChange": "Promeni", "SSE.Views.Spellcheck.textChangeAll": "Promeni sve", "SSE.Views.Spellcheck.textIgnore": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.Spellcheck.textIgnoreAll": "Ignoriši sve", "SSE.Views.Spellcheck.txtAddToDictionary": "Dodaj u rečnik", "SSE.Views.Spellcheck.txtClosePanel": "Zatvori pravopis", "SSE.Views.Spellcheck.txtComplete": "Provera pravopisa je završena", "SSE.Views.Spellcheck.txtDictionaryLanguage": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.txtNextTip": "Idi na sledeću reč", "SSE.Views.Spellcheck.txtSpelling": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Pomeri da završiš)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "<PERSON><PERSON><PERSON>j kop<PERSON>", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(Na<PERSON>vi proračunsku tabelu)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Pomeri pre lista", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Proračunska tabela", "SSE.Views.Statusbar.filteredRecordsText": "{0} od {1} z<PERSON><PERSON> filt<PERSON>", "SSE.Views.Statusbar.filteredText": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemAverage": "Prosečno", "SSE.Views.Statusbar.itemCount": "<PERSON>zbroj", "SSE.Views.Statusbar.itemDelete": "Izbriši", "SSE.Views.Statusbar.itemHidden": "Sakriveno", "SSE.Views.Statusbar.itemHide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemInsert": "Ubaci", "SSE.Views.Statusbar.itemMaximum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMinimum": "Minimum ", "SSE.Views.Statusbar.itemMoveOrCopy": "<PERSON><PERSON><PERSON> il<PERSON> k<PERSON>", "SSE.Views.Statusbar.itemProtect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemRename": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemStatus": "Čuvanje statusa", "SSE.Views.Statusbar.itemSum": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON><PERSON> boja", "SSE.Views.Statusbar.itemUnProtect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.RenameDialog.errNameExists": "List sa ovim imenom već postoji.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Ime lista ne može da sadrži sledeće karaktere: \\/*?[]: ili karakter ' kao prvi ili poslednji karakter ", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "List ime", "SSE.Views.Statusbar.selectAllSheets": "Odaberi sve listove", "SSE.Views.Statusbar.sheetIndexText": "List {0} od {1} ", "SSE.Views.Statusbar.textAverage": "Prosečno", "SSE.Views.Statusbar.textCount": "<PERSON>zbroj", "SSE.Views.Statusbar.textMax": "<PERSON><PERSON>", "SSE.Views.Statusbar.textMin": "Min", "SSE.Views.Statusbar.textNewColor": "<PERSON><PERSON><PERSON><PERSON> boja", "SSE.Views.Statusbar.textNoColor": "Bez Boje", "SSE.Views.Statusbar.textSum": "<PERSON><PERSON>", "SSE.Views.Statusbar.tipAddTab": "Dodaj list", "SSE.Views.Statusbar.tipFirst": "Skroluj do prvog lista", "SSE.Views.Statusbar.tipLast": "Skroluj do poslednjeg lista", "SSE.Views.Statusbar.tipListOfSheets": "<PERSON>e listova", "SSE.Views.Statusbar.tipNext": "Skroluj listu lista desno", "SSE.Views.Statusbar.tipPrev": "Skroluj listu lista levo", "SSE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomIn": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.ungroupSheets": "Razgrupiši listove", "SSE.Views.Statusbar.zoomText": "<PERSON><PERSON><PERSON> {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Operacija nije mogla biti učinjena za odabrani opseg ćelija.<br>Izaberite jedinstveni opseg podataka koji se razlikuje od postojećeg i pokušajte ponovo.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Operacija nije mogla da bude završena za odabran opseg ćelije.<br>Izaberite opseg tako da prvi red tabele bude u istom redu i rezultirajuća tabela preklapa trenutnu.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Operacija nije mogla da bude završena za odabran opseg ćelije.<br>Odaberite opseg koji ne uključuje druge tabele.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Višećelijske niz-formule nisu dozvoljene u tabelama.", "SSE.Views.TableOptionsDialog.txtEmpty": "<PERSON>vo polje je neophodno", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>u", "SSE.Views.TableOptionsDialog.txtInvalidRange": "GREŠKA! Nevažeći opseg ćelija", "SSE.Views.TableOptionsDialog.txtNote": "Zaglavlja moraju ostati u istom redu, i rezultirajući opseg tabele mora da se preklapa sa originalnim opsegom tabele.", "SSE.Views.TableOptionsDialog.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.deleteColumnText": "Izbriši kolonu", "SSE.Views.TableSettings.deleteRowText": "Izbriši red", "SSE.Views.TableSettings.deleteTableText": "Izbriši tabelu", "SSE.Views.TableSettings.insertColumnLeftText": "Ubaci kolonu levo", "SSE.Views.TableSettings.insertColumnRightText": "Ubaci kolonu <PERSON>", "SSE.Views.TableSettings.insertRowAboveText": "Ubaci red iznad", "SSE.Views.TableSettings.insertRowBelowText": "Ubaci red ispod", "SSE.Views.TableSettings.notcriticalErrorTitle": "Upozorenje ", "SSE.Views.TableSettings.selectColumnText": "Odaber<PERSON> celu kolonu", "SSE.Views.TableSettings.selectDataText": "Odaberi podatke kolone", "SSE.Views.TableSettings.selectRowText": "Odaberi red", "SSE.Views.TableSettings.selectTableText": "<PERSON><PERSON><PERSON><PERSON> tabelu", "SSE.Views.TableSettings.textActions": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textAdvanced": "Prikaži napredna podešavanja ", "SSE.Views.TableSettings.textBanded": "Pojasno", "SSE.Views.TableSettings.textColumns": "<PERSON><PERSON>", "SSE.Views.TableSettings.textConvertRange": "Konvertuj na opseg", "SSE.Views.TableSettings.textEdit": "Redovi i Kolone", "SSE.Views.TableSettings.textEmptyTemplate": "Bez š<PERSON>a ", "SSE.Views.TableSettings.textExistName": "GREŠKA! Opseg sa takvim imenom već postoji", "SSE.Views.TableSettings.textFilter": "Dugme za filter", "SSE.Views.TableSettings.textFirst": "Prvi", "SSE.Views.TableSettings.textHeader": "Zaglavlje", "SSE.Views.TableSettings.textInvalidName": "GREŠKA! Nevažeće ime tabele", "SSE.Views.TableSettings.textIsLocked": "Ovaj element se uređuje od strane drugog korisnika.", "SSE.Views.TableSettings.textLast": "Poslednje", "SSE.Views.TableSettings.textLongOperation": "Dugačka operacija", "SSE.Views.TableSettings.textPivot": "Ubaci pivot tabelu", "SSE.Views.TableSettings.textRemDuplicates": "Ukloni duplikate", "SSE.Views.TableSettings.textReservedName": "<PERSON><PERSON> koje poku<PERSON>vate da koristite je već referencirano u ćelijskim formulama. <PERSON><PERSON> vas koristite neko drugo ime.", "SSE.Views.TableSettings.textResize": "<PERSON><PERSON><PERSON> veli<PERSON> tabele", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "Odaberi podatak", "SSE.Views.TableSettings.textSlicer": "Um<PERSON><PERSON>", "SSE.Views.TableSettings.textTableName": "Ta<PERSON>a ime", "SSE.Views.TableSettings.textTemplate": "Izaberite iz šablona", "SSE.Views.TableSettings.textTotal": "Ukupno", "SSE.Views.TableSettings.txtGroupTable_Custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.txtGroupTable_Dark": "Tamno", "SSE.Views.TableSettings.txtGroupTable_Light": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.txtGroupTable_Medium": "Srednje", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Ta<PERSON>a stil tamno", "SSE.Views.TableSettings.txtTable_TableStyleLight": "<PERSON><PERSON><PERSON> stil svetlo", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "<PERSON><PERSON><PERSON> stil srednje", "SSE.Views.TableSettings.warnLongOperation": "Operacija koju pokušavate da izvedete može potrajati dugo vremena da se završi.<br>Da li ste sigurni da želite da nastavite?", "SSE.Views.TableSettingsAdvanced.textAlt": "Alternativni tekst", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Opis", "SSE.Views.TableSettingsAdvanced.textAltTip": "Alternativno tekstualno reprezentiranje informacija o vizualnom objektu, koje će biti pročitano osobama s oštećenjem vida ili kognitivnim oštećenjima kako bi im pomoglo bolje razumeti koje informacije se nalaze na slici, ob<PERSON><PERSON>, grafikonu ili tabeli.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textTitle": "Tabela - Napredna podešavanja ", "SSE.Views.TextArtSettings.strBackground": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strForeground": "<PERSON><PERSON> prednjeg plana", "SSE.Views.TextArtSettings.strPattern": "Šablon", "SSE.Views.TextArtSettings.strSize": "Veličina ", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "Neprozirnost", "SSE.Views.TextArtSettings.strType": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textAngle": "Ugao", "SSE.Views.TextArtSettings.textBorderSizeErr": "Uneta vrednost je netačna.<br><PERSON><PERSON><PERSON> unesite vrednost između 0 tačke i 1584 tačke.", "SSE.Views.TextArtSettings.textColor": "Bojenje", "SSE.Views.TextArtSettings.textDirection": "Direkcija", "SSE.Views.TextArtSettings.textEmptyPattern": "Bez š<PERSON>a", "SSE.Views.TextArtSettings.textFromFile": "<PERSON><PERSON> fajla", "SSE.Views.TextArtSettings.textFromUrl": "Iz URL", "SSE.Views.TextArtSettings.textGradient": "Tačke gradijenta", "SSE.Views.TextArtSettings.textGradientFill": "Gradijentno punjenje", "SSE.Views.TextArtSettings.textImageTexture": "Slika ili tekstura ", "SSE.Views.TextArtSettings.textLinear": "Linearno", "SSE.Views.TextArtSettings.textNoFill": "Bez pun<PERSON>", "SSE.Views.TextArtSettings.textPatternFill": "Šablon", "SSE.Views.TextArtSettings.textPosition": "Pozicija", "SSE.Views.TextArtSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStretch": "Ra<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStyle": "Stil", "SSE.Views.TextArtSettings.textTemplate": "Šablon", "SSE.Views.TextArtSettings.textTexture": "<PERSON>z teksture", "SSE.Views.TextArtSettings.textTile": "Pločica ", "SSE.Views.TextArtSettings.textTransform": "Transformiši", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Dodaj tačku gradijenta", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Ukloni tačku gradienta", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> papir", "SSE.Views.TextArtSettings.txtCanvas": "Platno", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGrain": "Zrno", "SSE.Views.TextArtSettings.txtGranite": "Granit", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON> papir ", "SSE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtWood": "Drvo ", "SSE.Views.Toolbar.capBtnAddComment": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnColorSchemas": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnComment": "Komentar", "SSE.Views.Toolbar.capBtnInsHeader": "Zaglavlje i Podnožje", "SSE.Views.Toolbar.capBtnInsSlicer": "Sečenje", "SSE.Views.Toolbar.capBtnInsSmartArt": "Pametna <PERSON>", "SSE.Views.Toolbar.capBtnInsSymbol": "Simbol", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageBreak": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageOrient": "Orijentacija ", "SSE.Views.Toolbar.capBtnPageSize": "Veličina ", "SSE.Views.Toolbar.capBtnPrintArea": "Štampaj Oblast", "SSE.Views.Toolbar.capBtnPrintTitles": "Štampaj <PERSON>", "SSE.Views.Toolbar.capBtnScale": "Prilagodi razmeri", "SSE.Views.Toolbar.capImgAlign": "Poravnaj", "SSE.Views.Toolbar.capImgBackward": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.Toolbar.capImgForward": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capImgGroup": "Grupa", "SSE.Views.Toolbar.capInsertChart": "Grafikon", "SSE.Views.Toolbar.capInsertChartRecommend": "Preporučeni grafikon", "SSE.Views.Toolbar.capInsertEquation": "Jednačina", "SSE.Views.Toolbar.capInsertHyperlink": "Hiperlink", "SSE.Views.Toolbar.capInsertImage": "Slika", "SSE.Views.Toolbar.capInsertShape": "Oblik", "SSE.Views.Toolbar.capInsertSpark": "Iskrica", "SSE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertText": "<PERSON><PERSON>", "SSE.Views.Toolbar.capInsertTextart": "Tekst Umetnost", "SSE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON><PERSON> oblike", "SSE.Views.Toolbar.mniCapitalizeWords": "Velika Početna Slova Za Svaku Reč", "SSE.Views.Toolbar.mniImageFromFile": "Slika iz Fajla", "SSE.Views.Toolbar.mniImageFromStorage": "Slika iz Skladišta ", "SSE.Views.Toolbar.mniImageFromUrl": "Slika iz URL", "SSE.Views.Toolbar.mniLowerCase": "mala slova ", "SSE.Views.Toolbar.mniSentenceCase": "Veliko početno slovo u rečenici.", "SSE.Views.Toolbar.mniToggleCase": "pROMENI sLUČAJ sLOVA", "SSE.Views.Toolbar.mniUpperCase": "VELIKA SLOVA", "SSE.Views.Toolbar.textAddPrintArea": "Dodaj u oblast za štampanje ", "SSE.Views.Toolbar.textAlignBottom": "Poravnaj Dno", "SSE.Views.Toolbar.textAlignCenter": "Poravnaj <PERSON>ntar", "SSE.Views.Toolbar.textAlignJust": "Obostrano poravnan", "SSE.Views.Toolbar.textAlignLeft": "Poravnaj Levo", "SSE.Views.Toolbar.textAlignMiddle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignRight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignTop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAllBorders": "Sve Granice", "SSE.Views.Toolbar.textAlpha": "Grčko Malo Slovo Alfa", "SSE.Views.Toolbar.textAuto": "Auto", "SSE.Views.Toolbar.textAutoColor": "Automatski ", "SSE.Views.Toolbar.textBetta": "Grčko Malo Slovo Beta ", "SSE.Views.Toolbar.textBlackHeart": "Crni Simbol Srca", "SSE.Views.Toolbar.textBold": "Podebljano", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON> grani<PERSON>", "SSE.Views.Toolbar.textBordersStyle": "Stil granice", "SSE.Views.Toolbar.textBottom": "Dno: ", "SSE.Views.Toolbar.textBottomBorders": "<PERSON><PERSON> grani<PERSON>", "SSE.Views.Toolbar.textBullet": "Oznaka", "SSE.Views.Toolbar.textCellAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textCenterBorders": "Unutar vertikalnih granica", "SSE.Views.Toolbar.textClearPrintArea": "Obriši oblast za štampanje", "SSE.Views.Toolbar.textClearRule": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textClockwise": "Ugao u smeru kazaljke na satu", "SSE.Views.Toolbar.textColorScales": "<PERSON><PERSON><PERSON> boja", "SSE.Views.Toolbar.textCopyright": "Kopirajt znak", "SSE.Views.Toolbar.textCounterCw": "Ugao Suprotan Smeru <PERSON>jke Na Satu", "SSE.Views.Toolbar.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textDataBars": "<PERSON><PERSON><PERSON> podataka", "SSE.Views.Toolbar.textDegree": "<PERSON><PERSON>", "SSE.Views.Toolbar.textDelLeft": "Pomeri ć<PERSON>", "SSE.Views.Toolbar.textDelPageBreak": "Ukloni prelom stranice", "SSE.Views.Toolbar.textDelta": "Grčko Malo Slovo Delta", "SSE.Views.Toolbar.textDelUp": "Pomeri ćelije gore", "SSE.Views.Toolbar.textDiagDownBorder": "Dijagonalna donja granica", "SSE.Views.Toolbar.textDiagUpBorder": "Dijagonalna gornja granica", "SSE.Views.Toolbar.textDivision": "Znak <PERSON>", "SSE.Views.Toolbar.textDollar": "<PERSON><PERSON>", "SSE.Views.Toolbar.textDone": "Gotovo", "SSE.Views.Toolbar.textDown": "<PERSON><PERSON>", "SSE.Views.Toolbar.textEditVA": "Uredi vidljivu oblast", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON> kolona", "SSE.Views.Toolbar.textEntireRow": "Ceo red", "SSE.Views.Toolbar.textEuro": "Euro Znak", "SSE.Views.Toolbar.textFewPages": "stranice", "SSE.Views.Toolbar.textFillLeft": "Levo", "SSE.Views.Toolbar.textFillRight": "Des<PERSON>", "SSE.Views.Toolbar.textFormatCellFill": "Ispuna <PERSON>", "SSE.Views.Toolbar.textGreaterEqual": "Veće Od Ili Jednako Sa", "SSE.Views.Toolbar.textHeight": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textHideVA": "Sakrij vidljivu oblast", "SSE.Views.Toolbar.textHorizontal": "Horizontalni tekst", "SSE.Views.Toolbar.textInfinity": "Beskonačnost ", "SSE.Views.Toolbar.textInsDown": "Pomeri <PERSON>", "SSE.Views.Toolbar.textInsideBorders": "Unutrašnje granice", "SSE.Views.Toolbar.textInsPageBreak": "Ubaci prelaz na novu stranicu ", "SSE.Views.Toolbar.textInsRight": "Pomeri ć<PERSON>", "SSE.Views.Toolbar.textItalic": "Kurziv", "SSE.Views.Toolbar.textItems": "Stavke", "SSE.Views.Toolbar.textLandscape": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textLeft": "Levo: ", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON> granice", "SSE.Views.Toolbar.textLessEqual": "Manje Od Ili Jednako Sa", "SSE.Views.Toolbar.textLetterPi": "Grčko Malo Slovo Pi", "SSE.Views.Toolbar.textManageRule": "Uprav<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textManyPages": "stranice", "SSE.Views.Toolbar.textMarginsLast": "Poslednje Prilagođeno", "SSE.Views.Toolbar.textMarginsNarrow": "<PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsNormal": "Normalno", "SSE.Views.Toolbar.textMarginsWide": "<PERSON><PERSON><PERSON> ", "SSE.Views.Toolbar.textMiddleBorders": "Unutar horizontalnih granica", "SSE.Views.Toolbar.textMoreBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMoreFormats": "Više formata", "SSE.Views.Toolbar.textMorePages": "Više stranica", "SSE.Views.Toolbar.textMoreSymbols": "<PERSON><PERSON>š<PERSON> simbola", "SSE.Views.Toolbar.textNewColor": "<PERSON><PERSON><PERSON><PERSON> boja", "SSE.Views.Toolbar.textNewRule": "Novo pravilo", "SSE.Views.Toolbar.textNoBorders": "Bez granica", "SSE.Views.Toolbar.textNotEqualTo": "<PERSON><PERSON>", "SSE.Views.Toolbar.textOneHalf": "Vulgarni Razlomak Polovina", "SSE.Views.Toolbar.textOnePage": "stranica", "SSE.Views.Toolbar.textOneQuarter": "Vulgarni Razlomak Četvrtina", "SSE.Views.Toolbar.textOutBorders": "Spoljašnje granice", "SSE.Views.Toolbar.textPageMarginsCustom": "Prilagođene margine", "SSE.Views.Toolbar.textPlusMinus": "Plus-<PERSON><PERSON>", "SSE.Views.Toolbar.textPortrait": "<PERSON><PERSON>", "SSE.Views.Toolbar.textPrint": "Štampaj", "SSE.Views.Toolbar.textPrintGridlines": "Štampaj Mrežne Lini<PERSON>", "SSE.Views.Toolbar.textPrintHeadings": "Štampaj naslove", "SSE.Views.Toolbar.textPrintOptions": "Podešavanja štampe", "SSE.Views.Toolbar.textRegistered": "Registrovani Znak", "SSE.Views.Toolbar.textResetPageBreak": "Resetuj sve prelome stranica", "SSE.Views.Toolbar.textRight": "Desno: ", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON> granice", "SSE.Views.Toolbar.textRotateDown": "Rotiraj tekst dole ", "SSE.Views.Toolbar.textRotateUp": "Rotiraj tekst gore", "SSE.Views.Toolbar.textRtlSheet": "Radni list desno-levo", "SSE.Views.Toolbar.textScale": "Razmera", "SSE.Views.Toolbar.textScaleCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSection": "<PERSON>na<PERSON>", "SSE.Views.Toolbar.textSelection": "<PERSON>z trenutne sele<PERSON>", "SSE.Views.Toolbar.textSeries": "Serije", "SSE.Views.Toolbar.textSetPrintArea": "Postavi oblast za štampanje ", "SSE.Views.Toolbar.textShapesCombine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textShapesFragment": "Fragmentiši", "SSE.Views.Toolbar.textShapesIntersect": "Preseci", "SSE.Views.Toolbar.textShapesSubstract": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textShapesUnion": "Unija", "SSE.Views.Toolbar.textShowVA": "Prikaži vidljivu oblast", "SSE.Views.Toolbar.textSmile": "Belo Nasmejano <PERSON>", "SSE.Views.Toolbar.textSquareRoot": "Kvadra<PERSON><PERSON>", "SSE.Views.Toolbar.textStrikeout": "Precrtano", "SSE.Views.Toolbar.textSubscript": "<PERSON><PERSON> in<PERSON>s", "SSE.Views.Toolbar.textSubSuperscript": "<PERSON><PERSON><PERSON><PERSON>/<PERSON>din<PERSON>s", "SSE.Views.Toolbar.textSuperscript": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabCollaboration": "Kolaboracija", "SSE.Views.Toolbar.textTabData": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabDraw": "Crtaj", "SSE.Views.Toolbar.textTabFile": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFormula": "Formula", "SSE.Views.Toolbar.textTabHome": "Početna Stranica", "SSE.Views.Toolbar.textTabInsert": "Ubaci", "SSE.Views.Toolbar.textTabLayout": "Struktura", "SSE.Views.Toolbar.textTabProtect": "<PERSON><PERSON>š<PERSON><PERSON>", "SSE.Views.Toolbar.textTabView": "Prikaz", "SSE.Views.Toolbar.textThisPivot": "Iz ovog pivota", "SSE.Views.Toolbar.textThisSheet": "Iz ovog lista", "SSE.Views.Toolbar.textThisTable": "Iz ove tabele", "SSE.Views.Toolbar.textTilde": "Tilda", "SSE.Views.Toolbar.textTop": "Vrh: ", "SSE.Views.Toolbar.textTopBorders": "Gornje granice", "SSE.Views.Toolbar.textTradeMark": "Zaštitni Znak", "SSE.Views.Toolbar.textUnderline": "<PERSON>d<PERSON><PERSON>", "SSE.Views.Toolbar.textUp": "<PERSON>", "SSE.Views.Toolbar.textVertical": "Vertikalni tekst", "SSE.Views.Toolbar.textWidth": "<PERSON><PERSON><PERSON> ", "SSE.Views.Toolbar.textYen": "<PERSON><PERSON>", "SSE.Views.Toolbar.textZoom": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignBottom": "Poravnaj dno", "SSE.Views.Toolbar.tipAlignCenter": "Poravnaj centar", "SSE.Views.Toolbar.tipAlignJust": "Obostrano poravnan", "SSE.Views.Toolbar.tipAlignLeft": "Poravnaj levo", "SSE.Views.Toolbar.tipAlignMiddle": "Porav<PERSON><PERSON> s<PERSON>", "SSE.Views.Toolbar.tipAlignRight": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> gore", "SSE.Views.Toolbar.tipAutofilter": "Sortiraj i filtriraj", "SSE.Views.Toolbar.tipBack": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipBorders": "Granice", "SSE.Views.Toolbar.tipCellStyle": "Stil <PERSON> ", "SSE.Views.Toolbar.tipChangeCase": "<PERSON><PERSON><PERSON> ve<PERSON> s<PERSON>a", "SSE.Views.Toolbar.tipChangeChart": "Promeni tip grafikona", "SSE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipColorSchemas": "Promeni temu boja", "SSE.Views.Toolbar.tipCondFormat": "Uslovno formatiranje", "SSE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON> k<PERSON>", "SSE.Views.Toolbar.tipCut": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDecDecimal": "<PERSON><PERSON><PERSON> de<PERSON>", "SSE.Views.Toolbar.tipDecFont": "<PERSON><PERSON><PERSON>a", "SSE.Views.Toolbar.tipDeleteOpt": "Izbriši ćelije", "SSE.Views.Toolbar.tipDigStyleAccounting": "Stil računovodstva", "SSE.Views.Toolbar.tipDigStyleComma": "Stil sa zarezima", "SSE.Views.Toolbar.tipDigStyleCurrency": "Stil valute", "SSE.Views.Toolbar.tipDigStylePercent": "Procenat stil", "SSE.Views.Toolbar.tipEditChart": "<PERSON>redi gra<PERSON>", "SSE.Views.Toolbar.tipEditChartData": "Odaberi podatak", "SSE.Views.Toolbar.tipEditChartType": "Promeni tip grafika", "SSE.Views.Toolbar.tipEditHeader": "Uredi zaglavlje ili podnožje ", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON>a", "SSE.Views.Toolbar.tipFontName": "Font", "SSE.Views.Toolbar.tipFontSize": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipHAlighOle": "Poravnaj <PERSON>", "SSE.Views.Toolbar.tipImgAlign": "Poravnaj objekte", "SSE.Views.Toolbar.tipImgGroup": "Grupni objekti", "SSE.Views.Toolbar.tipIncDecimal": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "SSE.Views.Toolbar.tipIncFont": "Pove<PERSON><PERSON> veli<PERSON> fonta", "SSE.Views.Toolbar.tipInsertChart": "Ubaci grafikon", "SSE.Views.Toolbar.tipInsertChartRecommend": "Ubaci preporučeni grafikon", "SSE.Views.Toolbar.tipInsertChartSpark": "Ubaci grafikon", "SSE.Views.Toolbar.tipInsertEquation": "Ubaci jednačinu", "SSE.Views.Toolbar.tipInsertHorizontalText": "Ubaci horizontalno polje za tekst", "SSE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertImage": "Ubaci sliku", "SSE.Views.Toolbar.tipInsertOpt": "Ubaci ćelije", "SSE.Views.Toolbar.tipInsertShape": "Ubaci oblik", "SSE.Views.Toolbar.tipInsertSlicer": "Um<PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertSmartArt": "Umetni SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Ubaci iskricu", "SSE.Views.Toolbar.tipInsertSymbol": "Ubaci simbol", "SSE.Views.Toolbar.tipInsertTable": "U<PERSON><PERSON> tabelu", "SSE.Views.Toolbar.tipInsertText": "Ubaci tekst box", "SSE.Views.Toolbar.tipInsertTextart": "Ubaci Text Umetnost", "SSE.Views.Toolbar.tipInsertVerticalText": "Ubaci vertikalni tekst box", "SSE.Views.Toolbar.tipMerge": "Spoji i centriraj", "SSE.Views.Toolbar.tipNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipNumFormat": "Format broja", "SSE.Views.Toolbar.tipPageBreak": "Dodajte prelom tamo gde želite da sledeća strana počne u štampanoj kopiji", "SSE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON>e stranice", "SSE.Views.Toolbar.tipPageOrient": "Orijentacija stranice", "SSE.Views.Toolbar.tipPageSize": "Veličina stranice", "SSE.Views.Toolbar.tipPaste": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrColor": "<PERSON><PERSON> boju", "SSE.Views.Toolbar.tipPrint": "Štampaj", "SSE.Views.Toolbar.tipPrintArea": "Štampaj oblast", "SSE.Views.Toolbar.tipPrintQuick": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrintTitles": "Naslovi za ispis", "SSE.Views.Toolbar.tipRedo": "Uradi ponovo", "SSE.Views.Toolbar.tipReplace": "Zameni", "SSE.Views.Toolbar.tipRtlSheet": "Promeni pravac lista tako da je prva kolona na desnoj strani", "SSE.Views.Toolbar.tipSave": "Sačuvaj", "SSE.Views.Toolbar.tipSaveCoauth": "Sačuvaj svoje promene da ih drugi korisnici vide.", "SSE.Views.Toolbar.tipScale": "Prilagodi razmeri", "SSE.Views.Toolbar.tipSelectAll": "Odaberi sve", "SSE.Views.Toolbar.tipSendBackward": "Po<PERSON><PERSON><PERSON> ", "SSE.Views.Toolbar.tipSendForward": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipShapesMerge": "<PERSON><PERSON><PERSON> oblike", "SSE.Views.Toolbar.tipSynchronize": "Dokument je promenjen od strane drugog korisnika. Molimo kliknite da sačuvate promene i ponovo učitajte ažuriranja.", "SSE.Views.Toolbar.tipTextFormatting": "Više alata za formatiranje teksta", "SSE.Views.Toolbar.tipTextOrientation": "Orijentacija ", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.Toolbar.tipVAlighOle": "Vertikalno poravnaj", "SSE.Views.Toolbar.tipVisibleArea": "Vidljiva oblast", "SSE.Views.Toolbar.tipWrap": "Upak<PERSON>j tekst", "SSE.Views.Toolbar.txtAccounting": "Računovodstvo", "SSE.Views.Toolbar.txtAdditional": "Dodatno", "SSE.Views.Toolbar.txtAscending": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.Toolbar.txtAutosumTip": "Sumacija", "SSE.Views.Toolbar.txtCellStyle": "<PERSON><PERSON> ", "SSE.Views.Toolbar.txtClearAll": "Sve", "SSE.Views.Toolbar.txtClearComments": "Komentari", "SSE.Views.Toolbar.txtClearFilter": "Obriši filter", "SSE.Views.Toolbar.txtClearFormat": "Format", "SSE.Views.Toolbar.txtClearFormula": "Funkcija", "SSE.Views.Toolbar.txtClearHyper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearText": "Tekst", "SSE.Views.Toolbar.txtCurrency": "Valuta", "SSE.Views.Toolbar.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDate": "Datum", "SSE.Views.Toolbar.txtDateLong": "Potpuni Datum", "SSE.Views.Toolbar.txtDateShort": "Kratak <PERSON>tum", "SSE.Views.Toolbar.txtDateTime": "Datum & Vreme", "SSE.Views.Toolbar.txtDescending": "Silazno", "SSE.Views.Toolbar.txtDollar": "$ Dolar", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Eksponencijalno", "SSE.Views.Toolbar.txtFillNum": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtFilter": "Filter", "SSE.Views.Toolbar.txtFormula": "Ubaci funkciju", "SSE.Views.Toolbar.txtFraction": "Razlomak", "SSE.Views.Toolbar.txtFranc": "Švajcars<PERSON> franak (CHF)", "SSE.Views.Toolbar.txtGeneral": "General<PERSON>", "SSE.Views.Toolbar.txtInteger": "<PERSON><PERSON> broj", "SSE.Views.Toolbar.txtManageRange": "<PERSON><PERSON><PERSON><PERSON> imena", "SSE.Views.Toolbar.txtMergeAcross": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtMergeCells": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtMergeCenter": "Spoji i Centriraj", "SSE.Views.Toolbar.txtNamedRange": "Imenovani opsezi", "SSE.Views.Toolbar.txtNewRange": "<PERSON><PERSON><PERSON><PERSON><PERSON> ime", "SSE.Views.Toolbar.txtNoBorders": "Bez granica", "SSE.Views.Toolbar.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtPasteRange": "<PERSON><PERSON><PERSON> ime", "SSE.Views.Toolbar.txtPercentage": "Procenat", "SSE.Views.Toolbar.txtPound": "£ Funta", "SSE.Views.Toolbar.txtRouble": "₽ <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScientific": "Na<PERSON>čno", "SSE.Views.Toolbar.txtSearch": "Pretraga", "SSE.Views.Toolbar.txtSort": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.Toolbar.txtSortAZ": "Sortiraj uzlazno", "SSE.Views.Toolbar.txtSortZA": "Sort<PERSON>j <PERSON>", "SSE.Views.Toolbar.txtSpecial": "Specijalni", "SSE.Views.Toolbar.txtTableTemplate": "Formatiraj kao šablon tabele", "SSE.Views.Toolbar.txtText": "Tekst", "SSE.Views.Toolbar.txtTime": "Vreme", "SSE.Views.Toolbar.txtUnmerge": "Odvoji <PERSON>", "SSE.Views.Toolbar.txtYen": "¥ Jen", "SSE.Views.Top10FilterDialog.textType": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBottom": "Dno", "SSE.Views.Top10FilterDialog.txtBy": "po", "SSE.Views.Top10FilterDialog.txtItems": "Stavka", "SSE.Views.Top10FilterDialog.txtPercent": "Procenat", "SSE.Views.Top10FilterDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 AutoFilter", "SSE.Views.Top10FilterDialog.txtTop": "Vrh", "SSE.Views.Top10FilterDialog.txtValueTitle": "Top 10 filter", "SSE.Views.ValueFieldSettingsDialog.textNext": "(sled<PERSON><PERSON><PERSON>)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "Format broja", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(pre<PERSON><PERSON><PERSON>)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Vrednosna polja podešavanja", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Prosečno", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Baza polje", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Baza stavka", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 od %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "<PERSON>zbroj", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Izbroj brojeve", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "<PERSON><PERSON><PERSON><PERSON><PERSON> ime", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Razlika od", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMax": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "Bez kalk<PERSON>je", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "% od", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "% razlika od", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "% kolone", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% od ukupnog iznosa", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% od roditeljskog ukupno", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% od ukupnog broja roditeljskih kolona", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% od roditeljskog reda ukupno", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% tekući ukupno u", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "% od reda", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Proizvod", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Rangiraj od najmanjeg ka najvećem", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Rangiraj od najvećeg ka najmanjem", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Ukupan z<PERSON> u", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Prikaži vrednosti kao", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "<PERSON><PERSON>:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "Standardna Devijacija", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "Standardna Devijacija Populacije", "SSE.Views.ValueFieldSettingsDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Sumiraj polje vrednosti po", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Promen<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "Zatvori", "SSE.Views.ViewManagerDlg.guestText": "Gost", "SSE.Views.ViewManagerDlg.lockText": "Zak<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textDelete": "Izbriši", "SSE.Views.ViewManagerDlg.textDuplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textEmpty": "<PERSON><PERSON><PERSON> pregled nije kreiran još.", "SSE.Views.ViewManagerDlg.textGoTo": "Idi na prikaz", "SSE.Views.ViewManagerDlg.textLongName": "Unesi ime koje je manje od 128 karaktera.", "SSE.Views.ViewManagerDlg.textNew": "Novo", "SSE.Views.ViewManagerDlg.textRename": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textRenameError": "Ime prikaza ne sme biti prazno.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Preimenuj prikaz", "SSE.Views.ViewManagerDlg.textViews": "List prikazi", "SSE.Views.ViewManagerDlg.tipIsLocked": "Ovaj element se uređuje od strane drugog korisnika.", "SSE.Views.ViewManagerDlg.txtTitle": "List menadžer prikaza", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Da li ste sigurni da želite da obrišete ovaj prikaz radnog lista?", "SSE.Views.ViewManagerDlg.warnDeleteView": "Pokušavate da izbrišete trenutno omogućeni prikaz '%1'.<br>Zatvorite ovaj prikaz i izbrišete ga?", "SSE.Views.ViewTab.capBtnFreeze": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.capBtnSheetView": "List Prikaz", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Uvek Prikaži Alatnu Traku", "SSE.Views.ViewTab.textClose": "Zatvori", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Kombinuj List i Status Trake", "SSE.Views.ViewTab.textCreate": "Novo", "SSE.Views.ViewTab.textDefault": "Podra<PERSON>mevan<PERSON>", "SSE.Views.ViewTab.textFill": "<PERSON><PERSON>", "SSE.Views.ViewTab.textFormula": "Formula traka", "SSE.Views.ViewTab.textFreezeCol": "Zamrzni prvu kolonu", "SSE.Views.ViewTab.textFreezeRow": "Zamrzni gornji red", "SSE.Views.ViewTab.textGridlines": "Mrežne linije", "SSE.Views.ViewTab.textHeadings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textInterfaceTheme": "Te<PERSON>", "SSE.Views.ViewTab.textLeftMenu": "Levi Panel", "SSE.Views.ViewTab.textLine": "<PERSON><PERSON>", "SSE.Views.ViewTab.textMacros": "Makroi", "SSE.Views.ViewTab.textManager": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textRightMenu": "Desni Panel", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Prikaži senku zamrznutih prozora", "SSE.Views.ViewTab.textTabStyle": "Stil kartice", "SSE.Views.ViewTab.textUnFreeze": "Odmrzni okvire", "SSE.Views.ViewTab.textZeros": "Prikaži nule", "SSE.Views.ViewTab.textZoom": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.tipClose": "Zatvori prikaz lista", "SSE.Views.ViewTab.tipCreate": "Kreiraj prikaz lista", "SSE.Views.ViewTab.tipFreeze": "Zamrz<PERSON>", "SSE.Views.ViewTab.tipInterfaceTheme": "Tema interfejsa", "SSE.Views.ViewTab.tipMacros": "Makroi", "SSE.Views.ViewTab.tipSheetView": "List prikaz", "SSE.Views.ViewTab.tipViewNormal": "Vidi svoj dokument u Normalnom prikazu", "SSE.Views.ViewTab.tipViewPageBreak": "Pogledajte gde će se prikazati prelom strane kada se dokument odštampa", "SSE.Views.ViewTab.txtViewNormal": "Normalno", "SSE.Views.ViewTab.txtViewPageBreak": "Prikaz preloma stranice", "SSE.Views.WatchDialog.closeButtonText": "Zatvori", "SSE.Views.WatchDialog.textAdd": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textBook": "Knji<PERSON>", "SSE.Views.WatchDialog.textCell": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textDelete": "Izbriši praćenje", "SSE.Views.WatchDialog.textDeleteAll": "Izbriši sve", "SSE.Views.WatchDialog.textFormula": "Formula", "SSE.Views.WatchDialog.textName": "Ime", "SSE.Views.WatchDialog.textSheet": "List", "SSE.Views.WatchDialog.textValue": "Vrednost ", "SSE.Views.WatchDialog.txtTitle": "Prozor za praćenje", "SSE.Views.WBProtection.hintAllowRanges": "Dozvoli uređivanje opsega", "SSE.Views.WBProtection.hintProtectRange": "Zaštiti opseg", "SSE.Views.WBProtection.hintProtectSheet": "Zaštiti list", "SSE.Views.WBProtection.hintProtectWB": "Zaštiti radni list", "SSE.Views.WBProtection.txtAllowRanges": "Dozvoli uređivanje opsega", "SSE.Views.WBProtection.txtHiddenFormula": "Sakrivene formule", "SSE.Views.WBProtection.txtLockedCell": "Zaključana ćelija", "SSE.Views.WBProtection.txtLockedShape": "Oblik zaključan", "SSE.Views.WBProtection.txtLockedText": "Zaključaj tekst", "SSE.Views.WBProtection.txtProtectRange": "Zaštiti opseg", "SSE.Views.WBProtection.txtProtectSheet": "Zaštiti list", "SSE.Views.WBProtection.txtProtectWB": "Zaštiti radni list", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Unesite lozinku da nezaštitite list", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Nezaštiti list", "SSE.Views.WBProtection.txtWBUnlockDescription": "Unesite lozinku da nezaštitite radnu knjigu"}