{"cancelButtonText": "Atcelt", "Common.Controllers.Chat.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.History.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "Apgabals", "Common.define.chartData.textAreaStacked": "Sagrupēts apgabals", "Common.define.chartData.textAreaStackedPer": "100% grupēts apgabals", "Common.define.chartData.textBar": "<PERSON><PERSON>", "Common.define.chartData.textBarNormal": "Klasterveida kolonna", "Common.define.chartData.textBarNormal3d": "3-<PERSON> klasterveida kolonna", "Common.define.chartData.textBarNormal3dPerspective": "3-<PERSON> kolonna", "Common.define.chartData.textBarStacked": "Sagrupēta kolonna", "Common.define.chartData.textBarStacked3d": "3-<PERSON> klasterveida kolonna", "Common.define.chartData.textBarStackedPer": "100% grupēta kolonna", "Common.define.chartData.textBarStackedPer3d": "3-D 100% grupēta kolonna", "Common.define.chartData.textCharts": "Diagram<PERSON>", "Common.define.chartData.textColumn": "Kolonna", "Common.define.chartData.textColumnSpark": "Kolonna", "Common.define.chartData.textCombo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON><PERSON> gr<PERSON> <PERSON> sag<PERSON><PERSON><PERSON>i", "Common.define.chartData.textComboBarLine": "Klasterveida kolonna – līnija", "Common.define.chartData.textComboBarLineSecondary": "Klasterveida kolonna – līnija uz sekundārās ass", "Common.define.chartData.textComboCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textDoughnut": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "Klasterveida j<PERSON>la", "Common.define.chartData.textHBarNormal3d": "3-<PERSON> klasterveida josla", "Common.define.chartData.textHBarStacked": "Sagrupēta grēda", "Common.define.chartData.textHBarStacked3d": "3-<PERSON> klasterveida josla", "Common.define.chartData.textHBarStackedPer": "100% grupēta josla", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% grupēta josla", "Common.define.chartData.textLine": "Lī<PERSON><PERSON>", "Common.define.chartData.textLine3d": "3-D līnija", "Common.define.chartData.textLineMarker": "Lī<PERSON><PERSON> ar marķieriem", "Common.define.chartData.textLineSpark": "Lī<PERSON><PERSON>", "Common.define.chartData.textLineStacked": "Sagrupēta līnija", "Common.define.chartData.textLineStackedMarker": "Sagrupēta līnija ar marķieriem", "Common.define.chartData.textLineStackedPer": "100% grupēta līnija", "Common.define.chartData.textLineStackedPerMarker": "100% grupēta līnija ar marķieriem", "Common.define.chartData.textPie": "<PERSON><PERSON><PERSON><PERSON> diagramma", "Common.define.chartData.textPie3d": "3-<PERSON> sektors", "Common.define.chartData.textPoint": "Punkts (XY)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Filled radar", "Common.define.chartData.textRadarMarker": "Radar with markers", "Common.define.chartData.textScatter": "Izkliedēt", "Common.define.chartData.textScatterLine": "Izkliedēt ar taisnām līnijām", "Common.define.chartData.textScatterLineMarker": "Izkliedēt ar taisnām līnijām un marķieriem", "Common.define.chartData.textScatterSmooth": "Izkliedēt ar gludām līni<PERSON>m", "Common.define.chartData.textScatterSmoothMarker": "Izkliedēt ar gludām līnijām un marķieriem", "Common.define.chartData.textSparks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textStock": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textSurface": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textWinLossSpark": "Ieguvums/zaudējums", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Nav iestatīts formāts", "Common.define.conditionalData.text1Above": "1 std dev virs", "Common.define.conditionalData.text1Below": "1 std dev zem", "Common.define.conditionalData.text2Above": "2 std dev virs", "Common.define.conditionalData.text2Below": "2 std dev zem", "Common.define.conditionalData.text3Above": "3 std dev virs", "Common.define.conditionalData.text3Below": "3 std dev zem", "Common.define.conditionalData.textAbove": "Virs", "Common.define.conditionalData.textAverage": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textBegins": "<PERSON><PERSON><PERSON> ar", "Common.define.conditionalData.textBelow": "Zem", "Common.define.conditionalData.textBetween": "Star<PERSON>", "Common.define.conditionalData.textBlank": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textBlanks": "<PERSON><PERSON> t<PERSON>", "Common.define.conditionalData.textBottom": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textContains": "Satur", "Common.define.conditionalData.textDataBar": "<PERSON><PERSON> j<PERSON>", "Common.define.conditionalData.textDate": "Datums", "Common.define.conditionalData.textDuplicate": "Dub<PERSON>āts", "Common.define.conditionalData.textEnds": "<PERSON><PERSON><PERSON> ar", "Common.define.conditionalData.textEqAbove": "Vienāds ar vai virs", "Common.define.conditionalData.textEqBelow": "Vienāds ar vai zem", "Common.define.conditionalData.textEqual": "<PERSON><PERSON><PERSON><PERSON> ar", "Common.define.conditionalData.textError": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textErrors": "<PERSON><PERSON>", "Common.define.conditionalData.textFormula": "Formula", "Common.define.conditionalData.textGreater": "<PERSON><PERSON><PERSON><PERSON> nekā", "Common.define.conditionalData.textGreaterEq": "Lie<PERSON>ā<PERSON> par vai vienāds ar", "Common.define.conditionalData.textIconSets": "Ikonu komplekti", "Common.define.conditionalData.textLast7days": "Iekšā pēd<PERSON><PERSON> 7 dienu laikā", "Common.define.conditionalData.textLastMonth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mēne<PERSON> laikā", "Common.define.conditionalData.textLastWeek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textLess": "<PERSON><PERSON><PERSON><PERSON> nek<PERSON>", "Common.define.conditionalData.textLessEq": "<PERSON><PERSON>āk nekā vai vienāds ar", "Common.define.conditionalData.textNextMonth": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>is", "Common.define.conditionalData.textNextWeek": "<PERSON>āka<PERSON><PERSON>", "Common.define.conditionalData.textNotBetween": "Ne starp", "Common.define.conditionalData.textNotBlanks": "Nesatur tuk<PERSON>", "Common.define.conditionalData.textNotContains": "Nesatur", "Common.define.conditionalData.textNotEqual": "Nav vien<PERSON>ds ar", "Common.define.conditionalData.textNotErrors": "Nesatur <PERSON>", "Common.define.conditionalData.textText": "Teksts", "Common.define.conditionalData.textThisMonth": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textThisWeek": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textToday": "Šodien", "Common.define.conditionalData.textTomorrow": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textTop": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textUnique": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textValue": "<PERSON><PERSON><PERSON><PERSON><PERSON> ir", "Common.define.conditionalData.textYesterday": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textAccentedPicture": "Akcentēts attēls", "Common.define.smartArt.textAccentProcess": "Akcentu process", "Common.define.smartArt.textAlternatingFlow": "<PERSON>ī<PERSON> pl<PERSON>", "Common.define.smartArt.textAlternatingHexagons": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textAlternatingPictureBlocks": "<PERSON><PERSON><PERSON> at<PERSON> bloki", "Common.define.smartArt.textAlternatingPictureCircles": "<PERSON><PERSON><PERSON> a<PERSON>", "Common.define.smartArt.textArchitectureLayout": "Arhitektūras izkārtojums", "Common.define.smartArt.textArrowRibbon": "<PERSON><PERSON><PERSON><PERSON><PERSON> ente", "Common.define.smartArt.textAscendingPictureAccentProcess": "<PERSON><PERSON><PERSON><PERSON> se<PERSON> attē<PERSON> a<PERSON> process", "Common.define.smartArt.textBalance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicBendingProcess": "Pamata izliekts process", "Common.define.smartArt.textBasicBlockList": "Pamata bloku saraksts", "Common.define.smartArt.textBasicChevronProcess": "Pamata skujiņu process", "Common.define.smartArt.textBasicCycle": "Pamata cikls", "Common.define.smartArt.textBasicMatrix": "Pamata matrica", "Common.define.smartArt.textBasicPie": "Pamata pīr<PERSON>gs", "Common.define.smartArt.textBasicProcess": "Pamata process", "Common.define.smartArt.textBasicPyramid": "<PERSON><PERSON> pira<PERSON>da", "Common.define.smartArt.textBasicRadial": "<PERSON>ata radi<PERSON>ls", "Common.define.smartArt.textBasicTarget": "Pamata mērķis", "Common.define.smartArt.textBasicTimeline": "<PERSON><PERSON> laika skala", "Common.define.smartArt.textBasicVenn": "<PERSON><PERSON>", "Common.define.smartArt.textBendingPictureAccentList": "Izliekts attēlu akcentu saraksts", "Common.define.smartArt.textBendingPictureBlocks": "Izliekti attēlu bloki", "Common.define.smartArt.textBendingPictureCaption": "Izliekti attēlu paraksti", "Common.define.smartArt.textBendingPictureCaptionList": "Izliekts attēlu parakstu saraksts", "Common.define.smartArt.textBendingPictureSemiTranparentText": "<PERSON><PERSON><PERSON><PERSON><PERSON> caurspīdīga teksta izliekšana", "Common.define.smartArt.textBlockCycle": "Bloka cikls", "Common.define.smartArt.textBubblePictureList": "Burbuļ<PERSON> attēlu sarak<PERSON>", "Common.define.smartArt.textCaptionedPictures": "Att<PERSON><PERSON> ar parastiem", "Common.define.smartArt.textChevronAccentProcess": "Skujiņas akcentu process", "Common.define.smartArt.textChevronList": "Skujiņ<PERSON> sa<PERSON>", "Common.define.smartArt.textCircleAccentTimeline": "A<PERSON>ļ<PERSON> izcēlumu laika skala", "Common.define.smartArt.textCircleArrowProcess": "Apļa bultiņas process", "Common.define.smartArt.textCirclePictureHierarchy": "<PERSON><PERSON><PERSON><PERSON> att<PERSON> hierarhija", "Common.define.smartArt.textCircleProcess": "Apļa process", "Common.define.smartArt.textCircleRelationship": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textCircularBendingProcess": "Riņķveida izliekts process", "Common.define.smartArt.textCircularPictureCallout": "Riņķveida attēlu remarka", "Common.define.smartArt.textClosedChevronProcess": "Slēgts s<PERSON> process", "Common.define.smartArt.textContinuousArrowProcess": "Nepārtraukts bultiņas process", "Common.define.smartArt.textContinuousBlockProcess": "Nepārtraukts bloķēšanas process", "Common.define.smartArt.textContinuousCycle": "Nepārtraukts cikls", "Common.define.smartArt.textContinuousPictureList": "Nepārtraukts attēlu saraksts", "Common.define.smartArt.textConvergingArrows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textConvergingRadial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textConvergingText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>", "Common.define.smartArt.textCounterbalanceArrows": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "Common.define.smartArt.textCycle": "Cikls", "Common.define.smartArt.textCycleMatrix": "Cikla matrica", "Common.define.smartArt.textDescendingBlockList": "<PERSON><PERSON><PERSON><PERSON><PERSON> bloku sarak<PERSON>", "Common.define.smartArt.textDescendingProcess": "Dilstošs process", "Common.define.smartArt.textDetailedProcess": "Detalizēts process", "Common.define.smartArt.textDivergingArrows": "Novirzī<PERSON> bultiņas", "Common.define.smartArt.textDivergingRadial": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "Common.define.smartArt.textEquation": "Vienādojums", "Common.define.smartArt.textFramedTextPicture": "Ierāmēta teksta attēls", "Common.define.smartArt.textFunnel": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textGear": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textGridMatrix": "Režģa matrica", "Common.define.smartArt.textGroupedList": "Grupēts saraksts", "Common.define.smartArt.textHalfCircleOrganizationChart": "Pusapļa organizācijas diagramma", "Common.define.smartArt.textHexagonCluster": "Sešst<PERSON><PERSON> kopa", "Common.define.smartArt.textHexagonRadial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textHierarchy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textHierarchyList": "<PERSON>erar<PERSON><PERSON> sarak<PERSON>", "Common.define.smartArt.textHorizontalBulletList": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textHorizontalHierarchy": "<PERSON><PERSON><PERSON><PERSON><PERSON> hierarhija", "Common.define.smartArt.textHorizontalLabeledHierarchy": "<PERSON><PERSON><PERSON><PERSON><PERSON> iezīmētā hierarhija", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "<PERSON><PERSON><PERSON><PERSON><PERSON> hierarhija", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontālā organizācijas diagramma", "Common.define.smartArt.textHorizontalPictureList": "Horizont<PERSON><PERSON> at<PERSON> sarak<PERSON>", "Common.define.smartArt.textIncreasingArrowProcess": "Pieau<PERSON><PERSON><PERSON> b<PERSON> process", "Common.define.smartArt.textIncreasingCircleProcess": "Pieaugošs a<PERSON>ļ<PERSON> process", "Common.define.smartArt.textInterconnectedBlockProcess": "Savstarpēji savienots bloķēšanas process", "Common.define.smartArt.textInterconnectedRings": "Savstarpēji sa<PERSON>noti g<PERSON>i", "Common.define.smartArt.textInvertedPyramid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>", "Common.define.smartArt.textLabeledHierarchy": "Iezīmētā hierarhija", "Common.define.smartArt.textLinearVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textLinedList": "Izklāts saraksts", "Common.define.smartArt.textList": "Saraksts", "Common.define.smartArt.textMatrix": "Matrica", "Common.define.smartArt.textMultidirectionalCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> cikls", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Vārda un nosaukuma organizācijas diagramma", "Common.define.smartArt.textNestedTarget": "Ligzdots mērķis", "Common.define.smartArt.textNondirectionalCycle": "Cikls bez virzieniem", "Common.define.smartArt.textOpposingArrows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textOpposingIdeas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textOrganizationChart": "Organizācijas diagramma", "Common.define.smartArt.textOther": "Citi", "Common.define.smartArt.textPhasedProcess": "Pakāpenisks process", "Common.define.smartArt.textPicture": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPictureAccentBlocks": "Attēlu akcentu bloki", "Common.define.smartArt.textPictureAccentList": "Attēlu akcent<PERSON> sa<PERSON>", "Common.define.smartArt.textPictureAccentProcess": "Attēla akcentēšanas process", "Common.define.smartArt.textPictureCaptionList": "Attēlu parakstu sarak<PERSON>", "Common.define.smartArt.textPictureFrame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPictureGrid": "Attēlu režģis", "Common.define.smartArt.textPictureLineup": "Attēlu izkārtojums līnijā", "Common.define.smartArt.textPictureOrganizationChart": "Attēla organizā<PERSON> diagram<PERSON>", "Common.define.smartArt.textPictureStrips": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPieProcess": "Sektoru process", "Common.define.smartArt.textPlusAndMinus": "Plus un mīnus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textProcessList": "Procesu sarak<PERSON>", "Common.define.smartArt.textPyramid": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textPyramidList": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "Common.define.smartArt.textRadialCluster": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textRadialCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON> cikls", "Common.define.smartArt.textRadialList": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "Common.define.smartArt.textRadialPictureList": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON> sarak<PERSON>", "Common.define.smartArt.textRadialVenn": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textRandomToResultProcess": "Nejaušs process līdz rezultātam", "Common.define.smartArt.textRelationship": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textRepeatingBendingProcess": "Atkārtots izliekts process", "Common.define.smartArt.textReverseList": "Apgriezt sarakstu", "Common.define.smartArt.textSegmentedCycle": "Segmentēts cikls", "Common.define.smartArt.textSegmentedProcess": "Segmentēts process", "Common.define.smartArt.textSegmentedPyramid": "Se<PERSON><PERSON><PERSON><PERSON> p<PERSON>da", "Common.define.smartArt.textSnapshotPictureList": "Momentuzņēmuma attēlu saraksts", "Common.define.smartArt.textSpiralPicture": "Spirā<PERSON><PERSON> attēls", "Common.define.smartArt.textSquareAccentList": "Kvadrātveida akcentu saraksts", "Common.define.smartArt.textStackedList": "Sagrupēts saraksts", "Common.define.smartArt.textStackedVenn": "Sagrupēta V<PERSON>", "Common.define.smartArt.textStaggeredProcess": "Zigzagveida process", "Common.define.smartArt.textStepDownProcess": "Atkāpšanās process", "Common.define.smartArt.textStepUpProcess": "Palielinā<PERSON>nas process", "Common.define.smartArt.textSubStepProcess": "Pakārtoto darbību process", "Common.define.smartArt.textTabbedArc": "Ciļņu loks", "Common.define.smartArt.textTableHierarchy": "Tabulas hierarhija", "Common.define.smartArt.textTableList": "Tabulas saraksts", "Common.define.smartArt.textTabList": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textTargetList": "Mērķa saraksts", "Common.define.smartArt.textTextCycle": "Teksta cikls", "Common.define.smartArt.textThemePictureAccent": "<PERSON><PERSON><PERSON> at<PERSON>", "Common.define.smartArt.textThemePictureAlternatingAccent": "<PERSON><PERSON><PERSON> at<PERSON>s a<PERSON>s", "Common.define.smartArt.textThemePictureGrid": "<PERSON><PERSON><PERSON> att<PERSON> režģis", "Common.define.smartArt.textTitledMatrix": "Matrica ar nosaukumu", "Common.define.smartArt.textTitledPictureAccentList": "Attēlu akcentu sarak<PERSON> ar no<PERSON><PERSON>mu", "Common.define.smartArt.textTitledPictureBlocks": "Attēlu bloki ar no<PERSON><PERSON>mu", "Common.define.smartArt.textTitlePictureLineup": "<PERSON><PERSON><PERSON><PERSON> at<PERSON> sarak<PERSON>", "Common.define.smartArt.textTrapezoidList": "Trapecveida sarak<PERSON>", "Common.define.smartArt.textUpwardArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "Common.define.smartArt.textVaryingWidthList": "Dažādu platumu sarak<PERSON>", "Common.define.smartArt.textVerticalAccentList": "Vertikāls i<PERSON> sa<PERSON>", "Common.define.smartArt.textVerticalArrowList": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalBendingProcess": "Vertikāli izliekts process", "Common.define.smartArt.textVerticalBlockList": "Vertik<PERSON><PERSON> bloku sarak<PERSON>", "Common.define.smartArt.textVerticalBoxList": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "Common.define.smartArt.textVerticalBracketList": "<PERSON><PERSON><PERSON><PERSON><PERSON> ieka<PERSON> sarak<PERSON>", "Common.define.smartArt.textVerticalBulletList": "<PERSON><PERSON><PERSON><PERSON><PERSON> aizzī<PERSON> sa<PERSON>", "Common.define.smartArt.textVerticalChevronList": "<PERSON><PERSON><PERSON><PERSON><PERSON> sku<PERSON><PERSON> sa<PERSON>", "Common.define.smartArt.textVerticalCircleList": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON> sa<PERSON>", "Common.define.smartArt.textVerticalCurvedList": "Vertikāli izliekts saraksts", "Common.define.smartArt.textVerticalEquation": "<PERSON><PERSON><PERSON><PERSON><PERSON> vien<PERSON>", "Common.define.smartArt.textVerticalPictureAccentList": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON> a<PERSON>cent<PERSON> sa<PERSON>", "Common.define.smartArt.textVerticalPictureList": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textVerticalProcess": "Vertikāls process", "Common.Translation.textMoreButton": "<PERSON><PERSON><PERSON><PERSON>", "Common.Translation.tipFileLocked": "Dokuments ir bloķēts rediģēšanai. <PERSON><PERSON><PERSON> varat veikt izmaiņas un saglabāt to kā vietējo kopiju vēlāk.", "Common.Translation.tipFileReadOnly": "Fails ir tikai lasāms. <PERSON> sag<PERSON>, saglab<PERSON><PERSON><PERSON> failu ar jaunu nosaukumu vai citā vietā.", "Common.Translation.warnFileLocked": "Fails tiek rediģēts citā lietotnē. Jūs varat turpināt rediģēt un saglabāt to kā kopiju.", "Common.Translation.warnFileLockedBtnEdit": "Izveidot kopiju", "Common.Translation.warnFileLockedBtnView": "Atvērts skatīšanai", "Common.UI.ButtonColored.textAutoColor": "Automātisks", "Common.UI.ButtonColored.textEyedropper": "Eyedropper", "Common.UI.ButtonColored.textNewColor": "<PERSON><PERSON><PERSON> jau<PERSON>", "Common.UI.ComboBorderSize.txtNoBorders": "Nav a<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Nav a<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ComboDataView.emptyComboText": "Nav stilu", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textHexErr": "Ievadītā vērtība ir nepareiza.<br>Ievadiet vērtību no 000000 līdz FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "<PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textRGBErr": "Ievadītā vērtība ir nepareiza.<br>Ievadiet skaitlisku vērtību no 0 līdz 255.", "Common.UI.HSBColorPicker.textNoColor": "Nav kr<PERSON><PERSON>", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "<PERSON><PERSON><PERSON><PERSON> paroli", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON> paroli", "Common.UI.SearchBar.textFind": "<PERSON>rast", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipNextResult": "<PERSON><PERSON><PERSON><PERSON><PERSON> re<PERSON>", "Common.UI.SearchBar.tipOpenAdvancedSettings": "<PERSON><PERSON><PERSON><PERSON> papildu i<PERSON>", "Common.UI.SearchBar.tipPreviousResult": "Iepriekš<PERSON><PERSON><PERSON> re<PERSON>", "Common.UI.SearchDialog.textHighlight": "<PERSON><PERSON><PERSON><PERSON> rezultā<PERSON>", "Common.UI.SearchDialog.textMatchCase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> burtu <PERSON>", "Common.UI.SearchDialog.textReplaceDef": "Ievadiet aizstājo<PERSON>", "Common.UI.SearchDialog.textSearchStart": "Ievadiet jū<PERSON> te<PERSON>", "Common.UI.SearchDialog.textTitle": "Atrast un aizvietot", "Common.UI.SearchDialog.textTitle2": "<PERSON>rast", "Common.UI.SearchDialog.textWholeWords": "<PERSON>ikai veseli vārdi", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "Aizvietot", "Common.UI.SearchDialog.txtBtnReplaceAll": "Aizvietot visus", "Common.UI.SynchronizeTip.textDontShow": "<PERSON><PERSON><PERSON><PERSON> nerād<PERSON>t šo zi<PERSON>u", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "Doku<PERSON> ir mainījies.<br> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dokumentu, lai red<PERSON> i<PERSON>.", "Common.UI.ThemeColorPalette.textRecentColors": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeClassicLight": "<PERSON>las<PERSON><PERSON> gaisma", "Common.UI.Themes.txtThemeContrastDark": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeDark": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "<PERSON><PERSON><PERSON> pats kā sistēma", "Common.UI.Window.cancelButtonText": "Atcelt", "Common.UI.Window.closeButtonText": "Aizvērt", "Common.UI.Window.noButtonText": "Nē", "Common.UI.Window.okButtonText": "<PERSON><PERSON>", "Common.UI.Window.textConfirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.textDontShow": "<PERSON><PERSON><PERSON><PERSON> nerād<PERSON>t šo zi<PERSON>u", "Common.UI.Window.textError": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.textInformation": "Informācija", "Common.UI.Window.textWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.yesButtonText": "Jā", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtaccent": "Ak<PERSON>s", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "Fons", "Common.Utils.ThemeColor.txtBlack": "Black", "Common.Utils.ThemeColor.txtBlue": "Zils", "Common.Utils.ThemeColor.txtBrightGreen": "Bright green", "Common.Utils.ThemeColor.txtBrown": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "Dark blue", "Common.Utils.ThemeColor.txtDarker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkGray": "Dark gray", "Common.Utils.ThemeColor.txtDarkGreen": "Tumši zaļa", "Common.Utils.ThemeColor.txtDarkPurple": "Dark purple", "Common.Utils.ThemeColor.txtDarkRed": "Dark red", "Common.Utils.ThemeColor.txtDarkTeal": "Dark teal", "Common.Utils.ThemeColor.txtDarkYellow": "Dark yellow", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "<PERSON>", "Common.Utils.ThemeColor.txtGreen": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "Light blue", "Common.Utils.ThemeColor.txtLighter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtLightGray": "Light gray", "Common.Utils.ThemeColor.txtLightGreen": "Light green", "Common.Utils.ThemeColor.txtLightOrange": "Light orange", "Common.Utils.ThemeColor.txtLightYellow": "Light yellow", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "Sārts", "Common.Utils.ThemeColor.txtPurple": "Purple", "Common.Utils.ThemeColor.txtRed": "Sarkans", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Sky blue", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Teksts", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "Balts", "Common.Utils.ThemeColor.txtYellow": "Yellow", "Common.Views.About.txtAddress": "adrese: ", "Common.Views.About.txtLicensee": "LICENSES ĪPAŠNIEKS", "Common.Views.About.txtLicensor": "LICENZĒTĀJS", "Common.Views.About.txtMail": "e-pasts: ", "Common.Views.About.txtPoweredBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "<PERSON><PERSON><PERSON> ", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyAsWork": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Automātis<PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textAutoFormat": "Automātiski formatēt rakstot", "Common.Views.AutoCorrectDialog.textBy": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textHyperlink": "Interneta un tīkla ceļi ar hip<PERSON>aitēm", "Common.Views.AutoCorrectDialog.textMathCorrect": "Mat<PERSON><PERSON><PERSON><PERSON> simbolu automātiskā koriģēšana", "Common.Views.AutoCorrectDialog.textNewRowCol": "<PERSON><PERSON><PERSON><PERSON> jaunas rindas un kolonnas tabulā", "Common.Views.AutoCorrectDialog.textRecognized": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Šīs izteiksmes ir atpazītas matemātiskās izteiksmes. Tās netiks automātiski formatētas slīprakstā.", "Common.Views.AutoCorrectDialog.textReplace": "Aizvietot", "Common.Views.AutoCorrectDialog.textReplaceText": "Aizvietot rakstot", "Common.Views.AutoCorrectDialog.textReplaceType": "Aizvietot tekstu rakstot", "Common.Views.AutoCorrectDialog.textReset": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textResetAll": "Atiestatīt uz noklusējuma", "Common.Views.AutoCorrectDialog.textRestore": "At<PERSON>uno<PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Automātis<PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Atpaz<PERSON><PERSON><PERSON><PERSON><PERSON>j<PERSON> drī<PERSON> būt tikai burti A līdz Z, lielie vai mazie burti.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "<PERSON><PERSON><PERSON><PERSON><PERSON> izteiksmes labojums tiks noņemts, un noņemtie tiks atjaunoti. Vai vēlaties turpināt?", "Common.Views.AutoCorrectDialog.warnReplace": "Automātiskās korekcijas ieraksts %1 jau pastāv. Vai vēlaties to aizstāt?", "Common.Views.AutoCorrectDialog.warnReset": "<PERSON><PERSON><PERSON><PERSON><PERSON>vienotais automātiskais labojums tiks noņemts, un mainītajiem tiks atjaunotas sākotnējās vērtības. Vai vēlaties turpināt?", "Common.Views.AutoCorrectDialog.warnRestore": "Automātiskās korekcijas ieraksts %1 tiks atiestatīts uz tā sākotnējo vērtību. Vai vēlaties turpināt?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "Autors no A līdz Z", "Common.Views.Comments.mniAuthorDesc": "Autors no Z līdz A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniDateDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniFilterGroups": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc grupas", "Common.Views.Comments.mniPositionAsc": "No augšas", "Common.Views.Comments.mniPositionDesc": "No apakšas", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "<PERSON><PERSON><PERSON> komentāru dokumentam", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAll": "Visi", "Common.Views.Comments.textAnonym": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textCancel": "Atcelt", "Common.Views.Comments.textClose": "Aizvērt", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON><PERSON><PERSON> koment<PERSON>", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "<PERSON><PERSON>", "Common.Views.Comments.textEnterCommentHint": "Ievadiet jū<PERSON> koment<PERSON>", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textOpenAgain": "Atvērt vēlreiz", "Common.Views.Comments.textReply": "Atbildēt", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "<PERSON><PERSON><PERSON><PERSON> koment<PERSON>", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "Jums nav atļaujas atkārtoti atvērt komentāru", "Common.Views.Comments.txtEmpty": "<PERSON><PERSON><PERSON> lapā nav virsrakstu.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON><PERSON><PERSON><PERSON> nerād<PERSON>t šo zi<PERSON>u", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON><PERSON> un iel<PERSON><PERSON><PERSON><PERSON> darb<PERSON>, i<PERSON><PERSON><PERSON><PERSON> redaktora izvēln<PERSON> darb<PERSON>, tiks veiktas tikai šajā redaktora cilnē.<br><br><PERSON> kopētu vai ielīmētu programmās vai no tām ārpus redaktora cilnes, i<PERSON><PERSON><PERSON>et tālāk norādītā<PERSON> tastatū<PERSON> kombin<PERSON>:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>, izg<PERSON>šanas un ielīmēšanas darbības", "Common.Views.CopyWarningDialog.textToCopy": "kopijai", "Common.Views.CopyWarningDialog.textToCut": "izgriešanai", "Common.Views.CopyWarningDialog.textToPaste": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "Ielā<PERSON><PERSON>...", "Common.Views.DocumentAccessDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> iestatījumi", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "Eraser", "Common.Views.Draw.hintSelect": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtEraser": "Eraser", "Common.Views.Draw.txtHighlighter": "Highlighter", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Pen", "Common.Views.Draw.txtSelect": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtSize": "Izmērs", "Common.Views.EditNameDialog.textLabel": "Etiķete:", "Common.Views.EditNameDialog.textLabelError": "Etiķete nedr<PERSON><PERSON>t būt tuk<PERSON>.", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "Šobrīd dokumentu rediģē vairāki lietotāji:", "Common.Views.Header.textAddFavorite": "Atzīmēt kā i<PERSON>i", "Common.Views.Header.textAdvSettings": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textBack": "<PERSON><PERSON><PERSON><PERSON> faila atra<PERSON> vietu", "Common.Views.Header.textClose": "<PERSON><PERSON><PERSON><PERSON><PERSON> failu", "Common.Views.Header.textCompactView": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textHideLines": "Pa<PERSON><PERSON>ē<PERSON> lineā<PERSON>", "Common.Views.Header.textHideStatusBar": "<PERSON><PERSON><PERSON><PERSON> lapu un statusu joslas", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "<PERSON><PERSON><PERSON>", "Common.Views.Header.textRemoveFavorite": "Noņemt no izlases", "Common.Views.Header.textSaveBegin": "Saglabā ...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "Visas izmaiņas saglabātas", "Common.Views.Header.textSaveExpander": "Visas izmaiņas saglabātas", "Common.Views.Header.textShare": "Dalīties", "Common.Views.Header.textZoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipAccessRights": "Pārvaldīt dokumenta piekļuves <PERSON>", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "Common.Views.Header.tipGoEdit": "Rediģēt šībrīža failu", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON><PERSON>u", "Common.Views.Header.tipPrintQuick": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipSearch": "Meklēt", "Common.Views.Header.tipUndo": "Atsaukt", "Common.Views.Header.tipUndock": "Atdokot atsevišķā logā", "Common.Views.Header.tipUsers": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipViewSettings": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipViewUsers": "<PERSON><PERSON><PERSON><PERSON>t lietotājus un pārvaldīt dokumentu piekļ<PERSON>", "Common.Views.Header.txtAccessRights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.txtRename": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.History.textCloseHistory": "Aizv<PERSON>rt vēst<PERSON>", "Common.Views.History.textHideAll": "<PERSON><PERSON><PERSON><PERSON> detaliz<PERSON><PERSON>", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "At<PERSON>uno<PERSON>", "Common.Views.History.textShowAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> detaliz<PERSON>tas izmaiņas", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "Ielīmēt attēla vietrādi URL:", "Common.Views.ImageFromUrlDialog.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "Common.Views.ImageFromUrlDialog.txtNotUrl": "<PERSON><PERSON> la<PERSON> ir jāb<PERSON>t vietrāža URL formātā \"http://www.example.com\"", "Common.Views.ListSettingsDialog.textBulleted": "Aizzīmots", "Common.Views.ListSettingsDialog.textFromFile": "No faila", "Common.Views.ListSettingsDialog.textFromStorage": "No glabātuves", "Common.Views.ListSettingsDialog.textFromUrl": "No URL", "Common.Views.ListSettingsDialog.textNumbering": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.textSelect": "Atlasīt no", "Common.Views.ListSettingsDialog.tipChange": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImport": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNewBullet": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNewImage": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNone": "Neviens", "Common.Views.ListSettingsDialog.txtOfText": "% no teksta", "Common.Views.ListSettingsDialog.txtSize": "Izmērs", "Common.Views.ListSettingsDialog.txtStart": "<PERSON><PERSON><PERSON> ar", "Common.Views.ListSettingsDialog.txtSymbol": "Simbols", "Common.Views.ListSettingsDialog.txtTitle": "Saraksta iestatījumi", "Common.Views.ListSettingsDialog.txtType": "Tips", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON> failu", "Common.Views.OpenDialog.textInvalidRange": "<PERSON><PERSON><PERSON><PERSON> diapazons", "Common.Views.OpenDialog.textSelectData": "<PERSON><PERSON><PERSON> da<PERSON>", "Common.Views.OpenDialog.txtAdvanced": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtColon": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtComma": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtDelimiter": "Nor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtDestData": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kur novietot datus", "Common.Views.OpenDialog.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "Common.Views.OpenDialog.txtEncoding": "<PERSON><PERSON><PERSON><PERSON><PERSON> ", "Common.Views.OpenDialog.txtIncorrectPwd": "Parole nav pareiza.", "Common.Views.OpenDialog.txtOpenFile": "<PERSON>eva<PERSON>t paroli, lai atvērtu failu", "Common.Views.OpenDialog.txtOther": "Citi", "Common.Views.OpenDialog.txtPassword": "Parole", "Common.Views.OpenDialog.txtPreview": "Priekšskatījums", "Common.Views.OpenDialog.txtProtected": "Kad ievad<PERSON>t paroli un atverat failu, pa<PERSON><PERSON><PERSON><PERSON>jā faila parole tiks atiestatīta.", "Common.Views.OpenDialog.txtSemicolon": "Semikols", "Common.Views.OpenDialog.txtSpace": "Atstarpe", "Common.Views.OpenDialog.txtTab": "Cilne", "Common.Views.OpenDialog.txtTitle": "Izvēlēties %1 iespējas", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fails", "Common.Views.PasswordDialog.txtDescription": "<PERSON> pasa<PERSON>tu <PERSON>o dokumentu, uz<PERSON><PERSON><PERSON><PERSON> paroli", "Common.Views.PasswordDialog.txtIncorrectPwd": "Aps<PERSON>rinājuma <PERSON>", "Common.Views.PasswordDialog.txtPassword": "Parole", "Common.Views.PasswordDialog.txtRepeat": "Atk<PERSON><PERSON><PERSON> paroli", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> paroli", "Common.Views.PasswordDialog.txtWarning": "Brīdinājums: <PERSON><PERSON><PERSON><PERSON> vai aizmirstu paroli nevar atgūt. Glabājiet drošā vietā.", "Common.Views.PluginDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.strPlugins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Iestatījumi", "Common.Views.Plugins.textStart": "<PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar paroli", "Common.Views.Protection.hintDelPwd": "<PERSON><PERSON><PERSON><PERSON> paroli", "Common.Views.Protection.hintPwd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vai d<PERSON>st paroli", "Common.Views.Protection.hintSignature": "<PERSON><PERSON><PERSON> para<PERSON>tu vai paraksta līniju", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON><PERSON><PERSON> paroli", "Common.Views.Protection.txtEncrypt": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtInvisibleSignature": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtSignature": "Paraksts", "Common.Views.Protection.txtSignatureLine": "Paraksta līnija", "Common.Views.RecentFiles.txtOpenRecent": "<PERSON><PERSON><PERSON><PERSON> pēd<PERSON>", "Common.Views.RenameDialog.textName": "<PERSON><PERSON><PERSON>", "Common.Views.RenameDialog.txtInvalidName": "<PERSON>aila nosaukums nedrīkst saturēt šādas z<PERSON>: ", "Common.Views.ReviewChanges.hintNext": "Uz nākamo izmaiņu", "Common.Views.ReviewChanges.hintPrev": "Uz iepriekšē<PERSON> izmaiņu", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "<PERSON><PERSON><PERSON><PERSON> koprediģēšana. Visas izmaiņas tiek saglabātas automātiski.", "Common.Views.ReviewChanges.strStrict": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strStrictDesc": "<PERSON>zman<PERSON>jiet 'Saglabāt' ta<PERSON>, lai sinhronizētu sevis un citu veiktās izmai<PERSON>as.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Pieņemt šībrīža izmaiņas", "Common.Views.ReviewChanges.tipCoAuthMode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kop<PERSON>g<PERSON> rediģēšanas režīmu", "Common.Views.ReviewChanges.tipCommentRem": "Noņemt komentārus", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Noņemt pašreizējos koment<PERSON>", "Common.Views.ReviewChanges.tipCommentResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Atrisināt pa<PERSON><PERSON><PERSON><PERSON><PERSON> koment<PERSON>", "Common.Views.ReviewChanges.tipHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON> versiju vēsturi", "Common.Views.ReviewChanges.tipRejectCurrent": "<PERSON><PERSON><PERSON><PERSON> š<PERSON>brīža izmaiņas", "Common.Views.ReviewChanges.tipReview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reģistrēšana", "Common.Views.ReviewChanges.tipReviewView": "Izvēlēties režīmu, kurā vēlaties atainot izmaiņas", "Common.Views.ReviewChanges.tipSetDocLang": "Uzst<PERSON><PERSON><PERSON>t dokumenta valodu", "Common.Views.ReviewChanges.tipSetSpelling": "Notiek pareizrakstības p<PERSON>aude", "Common.Views.ReviewChanges.tipSharing": "Pārvaldīt dokumenta piekļuves <PERSON>", "Common.Views.ReviewChanges.txtAccept": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptAll": "Pieņemt visas izmaiņas", "Common.Views.ReviewChanges.txtAcceptChanges": "Pieņemt izmaiņas", "Common.Views.ReviewChanges.txtAcceptCurrent": "Pieņemt šībrīža izmaiņas", "Common.Views.ReviewChanges.txtChat": "Čats", "Common.Views.ReviewChanges.txtClose": "Aizvērt", "Common.Views.ReviewChanges.txtCoAuthMode": "Ko<PERSON>īgā<PERSON> rediģēšanas režīms", "Common.Views.ReviewChanges.txtCommentRemAll": "Noņemt visus komentārus", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Noņemt pašreizējos koment<PERSON>", "Common.Views.ReviewChanges.txtCommentRemMy": "<PERSON>ņ<PERSON>t manus komentārus", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Noņemt manus pašre<PERSON><PERSON><PERSON> komentārus", "Common.Views.ReviewChanges.txtCommentRemove": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> visus komentārus", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Atrisināt pa<PERSON><PERSON><PERSON><PERSON><PERSON> koment<PERSON>", "Common.Views.ReviewChanges.txtCommentResolveMy": "<PERSON><PERSON><PERSON><PERSON><PERSON> manus komentārus", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Atrisinā<PERSON> manus pa<PERSON><PERSON><PERSON><PERSON> koment<PERSON>", "Common.Views.ReviewChanges.txtDocLang": "Valoda", "Common.Views.ReviewChanges.txtFinal": "Visas izmaiņ<PERSON> (priekšskats)", "Common.Views.ReviewChanges.txtFinalCap": "Gala", "Common.Views.ReviewChanges.txtHistory": "Versiju vēsture", "Common.Views.ReviewChanges.txtMarkup": "Visa<PERSON> <PERSON><PERSON><PERSON> (rediģēšana)", "Common.Views.ReviewChanges.txtMarkupCap": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtNext": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Visas izmai<PERSON> (priekšskats)", "Common.Views.ReviewChanges.txtOriginalCap": "Oriģināls", "Common.Views.ReviewChanges.txtPrev": "Iepriek<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Noraidīt visas izmaiņas", "Common.Views.ReviewChanges.txtRejectChanges": "Nora<PERSON><PERSON><PERSON> izmaiņ<PERSON>", "Common.Views.ReviewChanges.txtRejectCurrent": "<PERSON><PERSON><PERSON><PERSON>rīža izmaiņu", "Common.Views.ReviewChanges.txtSharing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtSpelling": "Notiek pareizrakstības p<PERSON>aude", "Common.Views.ReviewChanges.txtTurnon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> reģistrēšana", "Common.Views.ReviewChanges.txtView": "Attēlošanas režī<PERSON>", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textCancel": "Atcelt", "Common.Views.ReviewPopover.textClose": "Aizvērt", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "<PERSON><PERSON>", "Common.Views.ReviewPopover.textEnterComment": "Ievadiet jū<PERSON> koment<PERSON>", "Common.Views.ReviewPopover.textMention": "+pie<PERSON><PERSON><PERSON><PERSON>s piekļuvi dokumentam un nosūtīs e-pastu", "Common.Views.ReviewPopover.textMentionNotify": "+piemin<PERSON><PERSON>na informēs lietotāju pa e-pastu", "Common.Views.ReviewPopover.textOpenAgain": "Atvērt vēlreiz", "Common.Views.ReviewPopover.textReply": "Atbildēt", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Jums nav atļaujas atkārtoti atvērt komentāru", "Common.Views.ReviewPopover.txtDeleteTip": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.txtEditTip": "Rediģēt", "Common.Views.SaveAsDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textTitle": "Mape saglabāšanai", "Common.Views.SearchPanel.textByColumns": "<PERSON><PERSON><PERSON> kolo<PERSON>", "Common.Views.SearchPanel.textByRows": "<PERSON><PERSON><PERSON> r<PERSON>", "Common.Views.SearchPanel.textCaseSensitive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> burtu <PERSON>", "Common.Views.SearchPanel.textCell": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textContentChanged": "Do<PERSON><PERSON> ir main<PERSON>ts.", "Common.Views.SearchPanel.textFind": "<PERSON>rast", "Common.Views.SearchPanel.textFindAndReplace": "Atrast un aizvietot", "Common.Views.SearchPanel.textFormula": "Formula", "Common.Views.SearchPanel.textFormulas": "Formulas", "Common.Views.SearchPanel.textItemEntireCell": "<PERSON><PERSON>nu saturi", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} vien<PERSON> ir ve<PERSON> a<PERSON>.", "Common.Views.SearchPanel.textLookIn": "Ieskatīties", "Common.Views.SearchPanel.textMatchUsingRegExp": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> regu<PERSON><PERSON>", "Common.Views.SearchPanel.textName": "Nosa<PERSON>ms", "Common.Views.SearchPanel.textNoMatches": "Nav atbilstību", "Common.Views.SearchPanel.textNoSearchResults": "Nav mek<PERSON><PERSON><PERSON><PERSON> rezultātu", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "<PERSON><PERSON><PERSON><PERSON><PERSON> vienumi: {0}/{1}. Citi lietotāji ir bloķējuši atlikušo (-s) {2} vienumu (-s).", "Common.Views.SearchPanel.textReplace": "Aizvietot", "Common.Views.SearchPanel.textReplaceAll": "Aizvietot visus", "Common.Views.SearchPanel.textReplaceWith": "Aizvietot ar", "Common.Views.SearchPanel.textSearch": "Meklēt", "Common.Views.SearchPanel.textSearchAgain": "{0}Veiciet jaunu <PERSON>{1}, lai i<PERSON><PERSON><PERSON> prec<PERSON><PERSON><PERSON> rezult<PERSON>.", "Common.Views.SearchPanel.textSearchHasStopped": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir <PERSON>", "Common.Views.SearchPanel.textSearchOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> op<PERSON>", "Common.Views.SearchPanel.textSearchResults": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rezultāti: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textSelectDataRange": "<PERSON><PERSON><PERSON> datu di<PERSON>u", "Common.Views.SearchPanel.textSheet": "<PERSON><PERSON>", "Common.Views.SearchPanel.textSpecificRange": "Noteikts diapazons", "Common.Views.SearchPanel.textTooManyResults": "<PERSON>r <PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON>, ko parā<PERSON><PERSON><PERSON> šeit", "Common.Views.SearchPanel.textValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textValues": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textWholeWords": "<PERSON>ikai veseli vārdi", "Common.Views.SearchPanel.textWithin": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textWorkbook": "Darba burtnīca", "Common.Views.SearchPanel.tipNextResult": "<PERSON><PERSON><PERSON><PERSON><PERSON> re<PERSON>", "Common.Views.SearchPanel.tipPreviousResult": "Iepriekš<PERSON><PERSON><PERSON> re<PERSON>", "Common.Views.SelectFileDlg.textLoading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SelectFileDlg.textTitle": "<PERSON><PERSON>t datu avotu", "Common.Views.ShapeShadowDialog.txtAngle": "Leņķis", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Izmērs", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "Treknraksts", "Common.Views.SignDialog.textCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textInputName": "Ievadiet parakstīt<PERSON><PERSON> vārdu", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "Parakstītāja nosaukums nedrīkst būt tuk<PERSON>.", "Common.Views.SignDialog.textPurpose": "Šī dokumenta parakstīšanas mērķis", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "Izvēlēties attēlu", "Common.Views.SignDialog.textSignature": "Kā izskatās paraksts", "Common.Views.SignDialog.textTitle": "Parakstīt dokumentu", "Common.Views.SignDialog.textUseImage": "vai spiediet 'Izvēlēties attēlu', lai i<PERSON>totu attēlu kā parakstu", "Common.Views.SignDialog.textValid": "Derīgs no %1 līdz %2", "Common.Views.SignDialog.tipFontName": "<PERSON>ont<PERSON> no<PERSON>", "Common.Views.SignDialog.tipFontSize": "Fonta izmērs", "Common.Views.SignSettingsDialog.textAllowComment": "Atļaut parakstī<PERSON><PERSON><PERSON>m pievienot komentāru paraksta logā", "Common.Views.SignSettingsDialog.textDefInstruction": "Pirms parakstāt šo doku<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, vai saturs, zem kura parakst<PERSON><PERSON>, ir pareizs.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-pasts", "Common.Views.SignSettingsDialog.textInfoName": "<PERSON><PERSON><PERSON>", "Common.Views.SignSettingsDialog.textInfoTitle": "Parakstī<PERSON><PERSON><PERSON> amats", "Common.Views.SignSettingsDialog.textInstructions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> para<PERSON>", "Common.Views.SignSettingsDialog.textShowDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> datumu paraksta līnijā", "Common.Views.SignSettingsDialog.textTitle": "Paraksta uzstādīšana", "Common.Views.SignSettingsDialog.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Unikoda HEX vērtība", "Common.Views.SymbolTableDialog.textCopyright": "Autortiesību <PERSON>", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textDOQuote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEllipsis": "<PERSON><PERSON><PERSON><PERSON><PERSON> elipse", "Common.Views.SymbolTableDialog.textEmDash": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEmSpace": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEnDash": "De<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textEnSpace": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textFont": "Fonts", "Common.Views.SymbolTableDialog.textNBHyphen": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> defise", "Common.Views.SymbolTableDialog.textNBSpace": "Nelaužamā atstarpe", "Common.Views.SymbolTableDialog.textPilcrow": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Em atstarpe", "Common.Views.SymbolTableDialog.textRange": "Diapazons", "Common.Views.SymbolTableDialog.textRecent": "<PERSON><PERSON><PERSON> simboli", "Common.Views.SymbolTableDialog.textRegistered": "Reģistrēta zīme", "Common.Views.SymbolTableDialog.textSCQuote": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSection": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSHyphen": "<PERSON><PERSON><PERSON><PERSON> defise", "Common.Views.SymbolTableDialog.textSOQuote": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "Common.Views.SymbolTableDialog.textSpecial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textSymbols": "Simboli", "Common.Views.SymbolTableDialog.textTitle": "Simbols", "Common.Views.SymbolTableDialog.textTradeMark": "<PERSON><PERSON><PERSON> z<PERSON> simbols", "Common.Views.UserNameDialog.textDontShow": "<PERSON><PERSON><PERSON> neja<PERSON><PERSON><PERSON> man", "Common.Views.UserNameDialog.textLabel": "Etiķete:", "Common.Views.UserNameDialog.textLabelError": "Etiķete nedr<PERSON><PERSON>t būt tuk<PERSON>.", "SSE.Controllers.DataTab.strSheet": "<PERSON><PERSON>", "SSE.Controllers.DataTab.textAddExternalData": "<PERSON>r pievienota saite uz ārē<PERSON> avotu. Š<PERSON>das saites varat at<PERSON>unināt cilnē Dati.", "SSE.Controllers.DataTab.textColumns": "Kolonnas", "SSE.Controllers.DataTab.textContinue": "Continue", "SSE.Controllers.DataTab.textDontUpdate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textEmptyUrl": "Ju<PERSON> ir j<PERSON><PERSON><PERSON><PERSON> vietrādi URL.", "SSE.Controllers.DataTab.textRows": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textTurnOff": "Turn off AutoUpdate", "SSE.Controllers.DataTab.textUpdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textWizard": "Teksts par kolonnām", "SSE.Controllers.DataTab.txtDataValidation": "<PERSON><PERSON>", "SSE.Controllers.DataTab.txtErrorExternalLink": "Kļūda: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.txtExpand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "<PERSON>ti blakus atlasei netiks noņemti. Vai vēlaties paplašināt atlasi, lai iek<PERSON><PERSON><PERSON> blakus eso<PERSON> da<PERSON>, vai turpināt tikai ar pašlaik atlasītajām šūnām?", "SSE.Controllers.DataTab.txtExtendDataValidation": "Atlasē ir dažas šū<PERSON> bez datu validācijas iestatījumiem.<br>Vai vēlaties paplašināt datu validāciju uz šīm šūnām?", "SSE.Controllers.DataTab.txtImportWizard": "Teks<PERSON> vedn<PERSON>", "SSE.Controllers.DataTab.txtRemDuplicates": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.txtRemoveDataValidation": "Atlasē ir vairāk nekā viens validācijas tips.<br>Vai dzēst pašreizējos iestatījumus un turpināt?", "SSE.Controllers.DataTab.txtRemSelected": "Noņemt no atlasītā", "SSE.Controllers.DataTab.txtUrlTitle": "Ielīmēt datu vietrādi URL", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "This workbook contains links to external sources which update automatically. This might be unsafe.<br><br>If you trust them, press Continue.", "SSE.Controllers.DataTab.warnUpdateExternalData": "<PERSON><PERSON><PERSON> da<PERSON>rāmatā ir saites uz vienu vai vairākiem ārējiem avotiem, kas varētu būt nedro<PERSON>i.<br>Ja uzticaties saitēm, at<PERSON><PERSON><PERSON><PERSON> tās, lai iegūtu jaun<PERSON> datus.", "SSE.Controllers.DocumentHolder.alignmentText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.centerText": "Centrā", "SSE.Controllers.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON> kolonnu", "SSE.Controllers.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON><PERSON> rindu", "SSE.Controllers.DocumentHolder.deleteText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.errorInvalidLink": "<PERSON><PERSON> atsauce nepastā<PERSON>. <PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON><PERSON> saiti vai to dz<PERSON><PERSON><PERSON>.", "SSE.Controllers.DocumentHolder.guestText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Kolonna pa kreisi", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Kolonna pa labi", "SSE.Controllers.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON> augst<PERSON>k", "SSE.Controllers.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.leftText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.rightText": "Labais", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Automā<PERSON><PERSON><PERSON><PERSON> opcijas", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Kolonnas platums: {0} simboli ({1} p<PERSON><PERSON><PERSON><PERSON>)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "<PERSON><PERSON><PERSON> augstums {0} <PERSON><PERSON> ({1} p<PERSON><PERSON><PERSON><PERSON>)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Noklikšķiniet uz saites, lai to atvērtu, vai noklikšķiniet un turiet peles pogu, lai atlas<PERSON>tu <PERSON>.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Ievietot kolonnu pa kreisi", "SSE.Controllers.DocumentHolder.textInsertTop": "Ievietot rindu augstāk", "SSE.Controllers.DocumentHolder.textPasteSpecial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textStopExpand": "Pārtraukt tabulu automātisku izvēr<PERSON>nu", "SSE.Controllers.DocumentHolder.textSym": "sim", "SSE.Controllers.DocumentHolder.tipIsLocked": "Šo elementu lieto cits lietotājs.", "SSE.Controllers.DocumentHolder.txtAboveAve": "<PERSON><PERSON> vidējā", "SSE.Controllers.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON> l<PERSON>", "SSE.Controllers.DocumentHolder.txtAddLB": "<PERSON><PERSON>not līniju k<PERSON> apakšējā stūrī", "SSE.Controllers.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON> k<PERSON>", "SSE.Controllers.DocumentHolder.txtAddLT": "<PERSON><PERSON><PERSON> lī<PERSON>ju kreisajā augšējā stūrī", "SSE.Controllers.DocumentHolder.txtAddRight": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddVer": "<PERSON><PERSON><PERSON> l<PERSON>", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Saskaņot ar simbolu", "SSE.Controllers.DocumentHolder.txtAll": "(Visi)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Atgriež visu tabulas saturu vai norādītās tabulas kolonnas, tostarp kolonnu galvenes, datus un kopējās rindas", "SSE.Controllers.DocumentHolder.txtAnd": "un", "SSE.Controllers.DocumentHolder.txtBegins": "<PERSON><PERSON><PERSON> ar", "SSE.Controllers.DocumentHolder.txtBelowAve": "<PERSON><PERSON> vidēj<PERSON>", "SSE.Controllers.DocumentHolder.txtBlanks": "(Tuk<PERSON><PERSON>)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Apmales parametri", "SSE.Controllers.DocumentHolder.txtBottom": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtByField": "%1 no %2", "SSE.Controllers.DocumentHolder.txtColumn": "Kolonna", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Kolonnas izlīdzināšana", "SSE.Controllers.DocumentHolder.txtContains": "Satur", "SSE.Controllers.DocumentHolder.txtCopySuccess": "<PERSON><PERSON> kop<PERSON>ta starpliktuv<PERSON>", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Atgriež tabulas datu šūnas tabulai vai norādītajām tabulas kolonnām", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Samazināt argumenta izmēru", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Dzēst argumentu", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "<PERSON><PERSON><PERSON><PERSON> man<PERSON><PERSON><PERSON> at<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtDeleteChars": "<PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "<PERSON><PERSON><PERSON><PERSON> iet<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> un atdalītājus", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Dzēst vienādojumu", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON><PERSON><PERSON> simbolu", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "<PERSON><PERSON><PERSON><PERSON> radi<PERSON>", "SSE.Controllers.DocumentHolder.txtEnds": "<PERSON><PERSON><PERSON> ar", "SSE.Controllers.DocumentHolder.txtEquals": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Vienāds ar <PERSON> krāsu", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Vienāds ar fonta krāsu", "SSE.Controllers.DocumentHolder.txtExpand": "<PERSON><PERSON><PERSON><PERSON><PERSON> un šķirot", "SSE.Controllers.DocumentHolder.txtExpandSort": "<PERSON>ti blakus izvēlētajam laukam netiks šķiroti. Vai vēlaties paplašin<PERSON>t atlasi, lai iek<PERSON><PERSON>u blakus eso<PERSON> da<PERSON>, vai turpināt tikai ar pašlaik atlasītajām šūnām?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtFilterTop": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtFractionLinear": "<PERSON>īt uz lineāru da<PERSON>", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "<PERSON><PERSON><PERSON> uz <PERSON> da<PERSON>", "SSE.Controllers.DocumentHolder.txtFractionStacked": "<PERSON><PERSON><PERSON> uz vert<PERSON>", "SSE.Controllers.DocumentHolder.txtGreater": "<PERSON><PERSON><PERSON><PERSON> nekā", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Lie<PERSON>ā<PERSON> par vai vienāds ar", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Rakstz. virs teksta", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Rakstz. zem teksta", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Atgriež tabulas kolonnu galvenes tabulai vai norādītajām tabulas kolonnām", "SSE.Controllers.DocumentHolder.txtHeight": "Augstums", "SSE.Controllers.DocumentHolder.txtHideBottom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Nerādīt a<PERSON>šējo ierobežojumu", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideDegree": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideHor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideLB": "Nerādīt kreiso apakšējo līniju", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "SSE.Controllers.DocumentHolder.txtHideLT": "Nerā<PERSON><PERSON>t k<PERSON>o augšējo līniju", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "SSE.Controllers.DocumentHolder.txtHideRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> labo <PERSON>", "SSE.Controllers.DocumentHolder.txtHideTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>š<PERSON> ierobežojumu", "SSE.Controllers.DocumentHolder.txtHideVer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vert<PERSON>", "SSE.Controllers.DocumentHolder.txtImportWizard": "Teks<PERSON> vedn<PERSON>", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Palielināt argumenta izmēru", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Ievietot argumentu pēc", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Ievietot argumentu pirms", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Ieviet<PERSON> man<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Ievietot vien<PERSON><PERSON><PERSON><PERSON> p<PERSON>c", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Ievietot vienā<PERSON><PERSON><PERSON> pirms", "SSE.Controllers.DocumentHolder.txtItems": "vienumi", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON><PERSON><PERSON> tikai te<PERSON>tu", "SSE.Controllers.DocumentHolder.txtLess": "<PERSON><PERSON><PERSON><PERSON> nek<PERSON>", "SSE.Controllers.DocumentHolder.txtLessEquals": "<PERSON><PERSON>āk nekā vai vienāds ar", "SSE.Controllers.DocumentHolder.txtLimitChange": "<PERSON><PERSON><PERSON> v<PERSON>u", "SSE.Controllers.DocumentHolder.txtLimitOver": "Ierobežot virs teksta", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Ierobežot zem teksta", "SSE.Controllers.DocumentHolder.txtLockSort": "Dati ir atrasti blakus jūsu atlasei, bet jums nav pietiekamu atļauju, lai main<PERSON>tu <PERSON>.<br>Vai vēlaties turpināt pašreizējo atlasi?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "<PERSON><PERSON><PERSON><PERSON> argumenta augstumam", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtNoChoices": "Nav variantu šū<PERSON> a<PERSON>.<br>Tikai teksts no kolonnas var tikt ievietots.", "SSE.Controllers.DocumentHolder.txtNotBegins": "<PERSON><PERSON><PERSON><PERSON> ar", "SSE.Controllers.DocumentHolder.txtNotContains": "Nesatur", "SSE.Controllers.DocumentHolder.txtNotEnds": "N<PERSON><PERSON><PERSON><PERSON> ar", "SSE.Controllers.DocumentHolder.txtNotEquals": "Nav vien<PERSON>ds ar", "SSE.Controllers.DocumentHolder.txtOr": "vai", "SSE.Controllers.DocumentHolder.txtOverbar": "Josla virs teksta", "SSE.Controllers.DocumentHolder.txtPaste": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formula bez robežām", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formula + kolonnas platums", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Gala formatējums", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Ielīmēt tikai formatējumu", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formula + skait<PERSON>u formāts", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Ielīmēt tikai formulu", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formula + viss formatējums", "SSE.Controllers.DocumentHolder.txtPasteLink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> saiti", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON>", "SSE.Controllers.DocumentHolder.txtPasteMerge": "<PERSON><PERSON><PERSON><PERSON> nosacījuma <PERSON>ēju<PERSON>", "SSE.Controllers.DocumentHolder.txtPastePicture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Avotu formatēšana", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Vērtība + viss formatējums", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Vērtība + skaitļa formatējums", "SSE.Controllers.DocumentHolder.txtPasteValues": "Ielīmēt tikai vērtību", "SSE.Controllers.DocumentHolder.txtPercent": "procenti", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Atsaukt tabulas automā<PERSON>ko <PERSON>", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "SSE.Controllers.DocumentHolder.txtRemLimit": "Noņemt limitu", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Noņemt diakritisko zīmi", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Noņ<PERSON><PERSON> j<PERSON>", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Vai vēlaties noņemt šo parakstu?<br>To nevar at<PERSON>.", "SSE.Controllers.DocumentHolder.txtRemScripts": "<PERSON><PERSON><PERSON><PERSON> sk<PERSON>tus", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Noņemt apakšrakstu", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Noņemt augšrakstu", "SSE.Controllers.DocumentHolder.txtRowHeight": "<PERSON><PERSON><PERSON> augstums", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Skripti p<PERSON> te<PERSON>ta", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Skripti pirms teksta", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> ierobežojumu", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "<PERSON><PERSON><PERSON><PERSON><PERSON> au<PERSON>", "SSE.Controllers.DocumentHolder.txtSorting": "Šķirošana", "SSE.Controllers.DocumentHolder.txtSortSelected": "Šķirot izvēlēto", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Izvēlē<PERSON> tikai šo norādīt<PERSON> kolonnas rindu", "SSE.Controllers.DocumentHolder.txtTop": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Atgriež tabulas kopējās rindas tabulai vai norādītajām tabulas kolonnām", "SSE.Controllers.DocumentHolder.txtUnderbar": "Josla zem teksta", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Atsaukt tabulas automā<PERSON>ko <PERSON>", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Izmantot teksta importēšanas vedni", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Noklikšķinot uz šīs sa<PERSON> var kaitēt jūsu ierīcei un datiem.<br>Vai tiešām vēlaties turpināt?", "SSE.Controllers.DocumentHolder.txtWidth": "Platums", "SSE.Controllers.DocumentHolder.warnFilterError": "Lai lietotu vērt<PERSON><PERSON> filtru, apgabalā Vērtības ir nepieciešams vismaz viens lauks.", "SSE.Controllers.FormulaDialog.sCategoryAll": "Visi", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Custom", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "<PERSON><PERSON> b<PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Datums un laiks", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Inženieru", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Finansu", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Informācija", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 pēdējie lieto<PERSON>", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Loģiskie", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> un atsauces", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>s un trigonometriskās", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistiskie", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "<PERSON><PERSON><PERSON> un dati", "SSE.Controllers.LeftMenu.newDocumentTitle": "Izklājlapa bez <PERSON>ukuma", "SSE.Controllers.LeftMenu.textByColumns": "<PERSON><PERSON><PERSON> kolo<PERSON>", "SSE.Controllers.LeftMenu.textByRows": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Controllers.LeftMenu.textFormulas": "Formulas", "SSE.Controllers.LeftMenu.textItemEntireCell": "<PERSON><PERSON>nu saturi", "SSE.Controllers.LeftMenu.textLoadHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON> versiju vēsturi...", "SSE.Controllers.LeftMenu.textLookin": "Ieskatīties", "SSE.Controllers.LeftMenu.textNoTextFound": "<PERSON><PERSON><PERSON> meklētos datus nevarēja atrast. Pielāgojiet meklēšanas opcijas.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "<PERSON>r ve<PERSON>ta <PERSON>. <PERSON><PERSON> {0} gadījumi.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "<PERSON>r ve<PERSON>ta <PERSON>. Aizstātie gadīju<PERSON>: {0}", "SSE.Controllers.LeftMenu.textSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textSearch": "Meklēt", "SSE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "SSE.Controllers.LeftMenu.textSheet": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textValues": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWithin": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWorkbook": "Darba burtnīca", "SSE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.warnDownloadAs": "Ja jūs izvēlēsieties turpināt saglabāt šajā formātā visas diagrammas un attēli tiks zaudēti.<br>Vai tiešām vēlaties turpināt?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "The CSV format does not support saving a multi-sheet file.<br>To keep the selected format and save only the current sheet, press Save.<br>To save the current spreadsheet, click Cancel and save it in a different format.", "SSE.Controllers.Main.confirmAddCellWatches": "<PERSON><PERSON> darb<PERSON> pievie<PERSON> {0} <PERSON><PERSON><PERSON> (-<PERSON><PERSON>).<br><PERSON>ai vēlaties turpināt?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "<PERSON><PERSON> darb<PERSON>ba pēc atmiņas taupīšanas iem<PERSON>la pievienos tikai {0} <PERSON><PERSON><PERSON> (-<PERSON><PERSON>).<br>Vai vēlaties turpināt?", "SSE.Controllers.Main.confirmMaxChangesSize": "Darbību lielums pārsniedz jūsu serverim iestatīto iero<PERSON>.<br>Nospiediet \"Atsaukt\" lai atceltu pēdējo darb<PERSON>bu, vai nospiediet \"Turpināt\", lai darb<PERSON>bu turpin<PERSON>tu lokāli (jums ir jālejupielādē failu vai jākopē tā saturu, lai p<PERSON><PERSON><PERSON><PERSON>, ka nekas nav zaudēts).", "SSE.Controllers.Main.confirmMoveCellRange": "Galamērķa šūnu diapazons var saturēt datus. Vai turpināt operāciju?", "SSE.Controllers.Main.confirmPutMergeRange": "Avotu dati saturēja sapludin<PERSON> šū<PERSON>.<br>Tie tika atvienoti pirms ielīm<PERSON><PERSON><PERSON> tabulā.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Formulas galvenes rindā tiks noņemtas un pārveidotas par statisku tekstu.<br>Vai vēlaties turpināt?", "SSE.Controllers.Main.confirmReplaceHFPicture": "Only one picture can be inserted in each section of the header.<br>Press \"Replace\" to replace existing picture.<br>Press \"Keep\" to keep existing picture.", "SSE.Controllers.Main.convertationTimeoutText": "Konversijas ta<PERSON> p<PERSON>.", "SSE.Controllers.Main.criticalErrorExtText": "Nospiediet \"Labi\", lai atgrieztos dokumentu sarakstā.", "SSE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.downloadErrorText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.downloadTextText": "Le<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>...", "SSE.Controllers.Main.downloadTitleText": "Le<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>", "SSE.Controllers.Main.errNoDuplicates": "Nav atrasta vērt<PERSON><PERSON> dub<PERSON>.", "SSE.Controllers.Main.errorAccessDeny": "<PERSON><PERSON><PERSON> mēģināt veikt da<PERSON>, kuru nedrīkstat veikt.<br><PERSON><PERSON><PERSON><PERSON>, sazinieties ar savu dokumentu servera administratoru.", "SSE.Controllers.Main.errorArgsRange": "<PERSON><PERSON><PERSON><PERSON> ievadītā formulā.<br><PERSON>r <PERSON><PERSON><PERSON><PERSON> nederīgs argumentu diapazons.", "SSE.Controllers.Main.errorAutoFilterChange": "Operācija nav at<PERSON><PERSON><PERSON>, jo tā mēģina pārvie<PERSON>t š<PERSON> jūsu darb<PERSON>as tabulā.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Nevarēja veikt operāciju atlasītaj<PERSON>m <PERSON>n<PERSON>m, jo nevarat pārvietot tabulas daļu.<br>Atlasiet citu datu diapazonu, lai tiktu pārbīdīta visa tabula, un mēģiniet vēlreiz.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Darbību nevarēja veikt atlasītajam šūnu diapazonam.<br>Atlasiet vienotu datu diapazonu, kas atšķiras no esošā, un mēģiniet vēlreiz.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "<PERSON><PERSON><PERSON><PERSON> ne<PERSON> ve<PERSON>, jo a<PERSON><PERSON><PERSON> ir filtrētas <PERSON>.<br><PERSON><PERSON><PERSON><PERSON><PERSON>t filtrētos elementus un mēģiniet vēlreiz.", "SSE.Controllers.Main.errorBadImageUrl": "Nav pareizs attēla vietrāža URL", "SSE.Controllers.Main.errorCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.Main.errorCannotPasteImg": "<PERSON><PERSON><PERSON> nevaram ielīmēt šo attēlu no starpliktuves, taču varat to saglabāt savā ierīcē un \nievietot to no turienes, vai arī varat kopēt attēlu bez teksta un ielīmēt to izk<PERSON><PERSON><PERSON><PERSON>ā.", "SSE.Controllers.Main.errorCannotUngroup": "Nevar atgrupēt. <PERSON> sāktu struk<PERSON><PERSON>, atlasiet detalizētās rindas vai kolonnas un grupējiet tās.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "<PERSON><PERSON><PERSON> nevarat izmantot šo komandu aizsargātā lapā. <PERSON> izmantotu šo komandu, noņemiet lapas aizsardzību.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jums tiks lūgts ievad<PERSON>t paroli.", "SSE.Controllers.Main.errorChangeArray": "<PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.errorChangeFilteredRange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tiks mainīts filtrēts diapazons jū<PERSON> da<PERSON>.<br>Noņemiet automātiskos filtrus, lai pabe<PERSON>tu šo u<PERSON>.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "<PERSON><PERSON><PERSON>, kuru mēģināt main<PERSON>, atrodas aizsargātā lapā.<br><PERSON> ve<PERSON>, noņemiet lapas aizsardzību. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jums tiks lūgts ievad<PERSON>t paroli.", "SSE.Controllers.Main.errorCircularReference": "There are one or more circular references where a formula refers to its own cell either directly or indirectly.<br>Try removing or changing these references, or moving the formulas to different cells.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Servera savienojums ir zaud<PERSON>. Dokumentu pašlaik nevar rediģēt.", "SSE.Controllers.Main.errorConnectToServer": "Dokumentu neizdevās nog<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, pārbaudiet savienojuma uzstādījumus vai sazinieties ar savu administratoru.<br><PERSON><PERSON><PERSON><PERSON><PERSON> 'OK', jūs varēsit lejupielādēt dokumentu.", "SSE.Controllers.Main.errorConvertXml": "<PERSON><PERSON><PERSON> ir neatbalst<PERSON>ts formāts.<br>Var izmantot tikai XML izklājlapas 2003 formātu.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Šo komandu nevar izman<PERSON>t nesaistītiem diapazoniem.<br>Izvēlieties vienu diapazonu un mēģiniet vēlreiz.", "SSE.Controllers.Main.errorCountArg": "<PERSON><PERSON><PERSON><PERSON> ievadītā formulā.<br><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> nepareizs argumentu skaits.", "SSE.Controllers.Main.errorCountArgExceed": "<PERSON><PERSON><PERSON><PERSON> ievadītā formulā.<br><PERSON>r <PERSON><PERSON><PERSON><PERSON> argumentu s<PERSON>.", "SSE.Controllers.Main.errorCreateDefName": "<PERSON><PERSON><PERSON><PERSON> no<PERSON><PERSON> di<PERSON><PERSON>us nevar rediģēt, bet jaunus nevar<br><PERSON><PERSON><PERSON><PERSON><PERSON>, jo daži no tiem tiek rediģēti.", "SSE.Controllers.Main.errorCreateRange": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>us nevar rediģēt, bet jaunus nevar<br><PERSON><PERSON><PERSON><PERSON><PERSON>, jo daži no tiem tiek rediģēti.", "SSE.Controllers.Main.errorDatabaseConnection": "<PERSON><PERSON><PERSON><PERSON><PERSON>.<br><PERSON><PERSON> bā<PERSON> k<PERSON>. Sazinieties ar at<PERSON><PERSON> die<PERSON>, ja kļūda joprojām pastāv.", "SSE.Controllers.Main.errorDataEncrypted": "<PERSON>r sa<PERSON><PERSON> šifr<PERSON>, tā<PERSON> nevar <PERSON>.", "SSE.Controllers.Main.errorDataRange": "Nepareizs datu diapazons.", "SSE.Controllers.Main.errorDataValidate": "Ievadītā vērtība ir nederīga.<br>Lietotājam ir ierobežota<PERSON> vērtības, kuras var ievadīt šajā šūn<PERSON>.", "SSE.Controllers.Main.errorDefaultMessage": "Kļūdas kods: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Jūs mēģināt izdzēst kolonnu, kurā ir bloķēta šūna. Bloķētās šūnas nevar izdz<PERSON>st, kamēr darblapa ir aizsargāta.<br>Lai izdzēstu bloķētu šūnu, noņemiet lapas aizsardzību. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jums tiks lūgts ievad<PERSON>t paroli.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Jūs izmēģināt dzēst rindu, kur<PERSON> ir bloķēta šūna. Bloķētās šūnas nevar i<PERSON>, kamēr darblapa ir aizsargāta.<br><PERSON> izdzēstu bloķētu šūnu, noņemiet lapas aizsardzību. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jums tiks lūgts ievad<PERSON>t paroli.", "SSE.Controllers.Main.errorDependentsNoFormulas": "The Trace Dependents command found no formulas that refer to the active cell.", "SSE.Controllers.Main.errorDirectUrl": "Verificējiet saiti uz dokumentu.<br><PERSON><PERSON> saitei ir jābūt tiešai saitei uz failu lejupielādei.", "SSE.Controllers.Main.errorEditingDownloadas": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>da darba laikā ar dokumentu.<br><PERSON><PERSON><PERSON><PERSON><PERSON> opciju Leju<PERSON>lādēt kā, lai saglabātu faila dublējum<PERSON>pi<PERSON> diskā.", "SSE.Controllers.Main.errorEditingSaveas": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> darba laikā ar dokumentu.<br><PERSON><PERSON><PERSON><PERSON><PERSON> opciju Saglabāt kā, lai saglabātu faila dublējumkopi<PERSON> diskā.", "SSE.Controllers.Main.errorEditView": "<PERSON><PERSON><PERSON><PERSON> lapas skatu nevar rediģēt, bet jaunus nevar šobr<PERSON><PERSON>, jo daži no tiem tiek rediģēti.", "SSE.Controllers.Main.errorEmailClient": "Nevarēja atrast e-pasta klientu.", "SSE.Controllers.Main.errorFilePassProtect": "Fails ir aizsargāts ar paroli un to nevar atvērt.", "SSE.Controllers.Main.errorFileRequest": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>.<br><PERSON><PERSON><PERSON> pie<PERSON> k<PERSON>. Sazinieties ar at<PERSON><PERSON> die<PERSON>, ja kļūda joprojām pastāv.", "SSE.Controllers.Main.errorFileSizeExceed": "<PERSON><PERSON>a lielums pārsniedz jūsu serverim iestatīto ierobežojumu.<br><PERSON> iegūtu sīk<PERSON>ku inform<PERSON>, sazinieties ar savu dokumentu servera administratoru.", "SSE.Controllers.Main.errorFileVKey": "<PERSON><PERSON><PERSON><PERSON>.<br>Nepare<PERSON> drošības atslēga. Sazinieties ar at<PERSON><PERSON> die<PERSON>, ja kļūda joprojām pastāv.", "SSE.Controllers.Main.errorFillRange": "Neizdev<PERSON><PERSON> a<PERSON>t izvēl<PERSON>to <PERSON>u di<PERSON>.<br>Visā<PERSON> sapludinātajām šūnām ir jābūt viena iz<PERSON>ēra.", "SSE.Controllers.Main.errorForceSave": "<PERSON>g<PERSON><PERSON><PERSON><PERSON><PERSON> failu, r<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> iespēju 'Lejupielādēt kā', lai noglabātu failu datora cietajā diskā, vai mēģiniet vēlāk vēlreiz.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Cannot enter a formula for an item or field name in a pivot table report.", "SSE.Controllers.Main.errorFormulaName": "<PERSON><PERSON><PERSON><PERSON> ievadītā formulā.<br><PERSON><PERSON> <PERSON><PERSON><PERSON> nepareizs formulas nosaukums.", "SSE.Controllers.Main.errorFormulaParsing": "Notika iekšēja kļūda analizējot formulu.", "SSE.Controllers.Main.errorFrmlMaxLength": "Jūsu formulas garums pārsniedz 8192 rakstzīmju ierobežojumu.<br>Rediģējiet to un mēģiniet vēlreiz.", "SSE.Controllers.Main.errorFrmlMaxReference": "<PERSON><PERSON><PERSON> nevarat ievadīt šo formulu, jo tai ir pārāk daudz vērt<PERSON>bu,<br><PERSON><PERSON><PERSON> atsauces un/vai nosaukumu.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Teksta vērtības formulās ir ierobežotas līdz 255 rakstzīmēm.<br>Izmantojiet funkciju APVIENOT vai konkatenācijas operatoru (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Funkcija attiecas uz lapu, kas ne<PERSON>.<br>Pārbaud<PERSON> datus un mēģiniet vēlreiz.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Darbī<PERSON> neizdevās izpildīt izvēlētajā šūnu diapazon<PERSON>.<br>Izv<PERSON><PERSON><PERSON> diapazonu, lai pirmā tabulas rinda būtu tajā pašā rindā<br>un gala tabula pārklātos ar esošo.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Operāciju neizdevās izpild<PERSON>t izvēlēta<PERSON><PERSON> diapazon<PERSON>.<br>Izv<PERSON><PERSON><PERSON> diapazonu, kas neietver citas tabulas.", "SSE.Controllers.Main.errorInconsistentExt": "<PERSON><PERSON><PERSON> failu, r<PERSON><PERSON><PERSON>.<br><PERSON><PERSON>a saturs neatbilst faila papla<PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.errorInconsistentExtDocx": "At<PERSON>ot failu, r<PERSON><PERSON><PERSON>.<br><PERSON><PERSON><PERSON> saturs atbilst teksta dokumentiem (piem., docx), taču failam ir neatbilstīgs paplašinājums: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "At<PERSON>ot failu, r<PERSON><PERSON><PERSON>.<br><PERSON><PERSON>a saturs atbilst vienam no šiem formātiem: pdf/djvu/xps/oxps, taču failam ir neatbilstīgs paplašinājums: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "<PERSON><PERSON>ot failu, r<PERSON><PERSON><PERSON>.<br><PERSON><PERSON><PERSON> saturs atbilst prezentācijām (piem., pptx), taču failam ir neatbilstīgs paplašinājums: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "At<PERSON>ot failu, r<PERSON><PERSON><PERSON>.<br><PERSON><PERSON><PERSON> saturs atbilst izklājlapām (piem., xlsx), taču failam ir neatbilstīgs paplašinājums: %1.", "SSE.Controllers.Main.errorInvalidRef": "Ievadiet pareizu izvēlētā diapazona nosaukumu vai derīgu saiti, uz kuru doties.", "SSE.Controllers.Main.errorKeyEncrypt": "Nezināms atslēgas deskriptors", "SSE.Controllers.Main.errorKeyExpire": "Atslēgas deskrip<PERSON>", "SSE.Controllers.Main.errorLabledColumnsPivot": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, kas ir sakārtoti kā saraksts ar atzīmētām kolonnām.", "SSE.Controllers.Main.errorLoadingFont": "Fonti netiek ielādēti.<br>Sazinieties ar savu dokumentu servera administratoru.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Atsauce uz atrašanās vietu vai datu diapazonu ir nederīga.", "SSE.Controllers.Main.errorLockedAll": "Nevarēja ve<PERSON>, jo lapu ir bloķējis cits lietotājs.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "One of the cells involved in the goal seek process has been modified by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "<PERSON><PERSON><PERSON> ne<PERSON>at <PERSON>t da<PERSON>, kas atrodas pivot tabulā.", "SSE.Controllers.Main.errorLockedWorksheetRename": "<PERSON><PERSON><PERSON><PERSON><PERSON> lapu šobrīd nevar pārd<PERSON>t, jo to pārdēv<PERSON> cits lietotājs", "SSE.Controllers.Main.errorMaxPoints": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sērijas skaits diagrammā ir 4096.", "SSE.Controllers.Main.errorMoveRange": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "SSE.Controllers.Main.errorMoveSlicerError": "Tabulas datu griezumus nevar kopēt no vienas darbgrāmatas uz citu.<br>Mēģiniet vēlre<PERSON>, atlasot visu tabulu un datu griezumus.", "SSE.Controllers.Main.errorMultiCellFormula": "<PERSON><PERSON><PERSON><PERSON> nav at<PERSON><PERSON><PERSON> vair<PERSON>ku šūnu masīvu formulas.", "SSE.Controllers.Main.errorNoDataToParse": "<PERSON><PERSON> at<PERSON> dati par<PERSON>.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.Main.errorOpenWarning": "Viena no faila formulām pārsniedz 8192 rakstzīmju ierobežojumu.<br>Formula tika noņemta.", "SSE.Controllers.Main.errorOperandExpected": "Ievadītā funkcijas sintakse nav pareiza. P<PERSON><PERSON><PERSON><PERSON>, vai trūkst kādas no iekavām – '(' vai ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "<PERSON><PERSON><PERSON> sniegtā parole nav pareiza.<br><PERSON><PERSON><PERSON><PERSON><PERSON>, vai CAPS LOCK taustiņš ir iz<PERSON>lēgt<PERSON>, un vai pareizi izmanto<PERSON>t lielos un mazos burtus.", "SSE.Controllers.Main.errorPasteInPivot": "We can't make this change for the selected cells because it will affect a pivot table.<br>Use the field list to change the report.", "SSE.Controllers.Main.errorPasteMaxRange": "<PERSON><PERSON><PERSON>t un ielīmēt apgabals nesakrīt.<br>Atlasiet apgabalu ar tādu pašu izmēru vai noklikšķiniet uz pirmās šūnas pēc kārtas, lai ielīmētu kopētās š<PERSON>.", "SSE.Controllers.Main.errorPasteMultiSelect": "<PERSON><PERSON> darb<PERSON>bu nevar veikt vairāku diapazonu atlasē.<br>Izvēlieties vienu diapazonu un mēģiniet vēlreiz.", "SSE.Controllers.Main.errorPasteSlicerError": "Tabulas datu griezumus nevar pārkop<PERSON>t no vienas darbgrāmatas uz citu.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Pivot table field name already exists.", "SSE.Controllers.Main.errorPivotGroup": "Nevar grupēt šo atlasi.", "SSE.Controllers.Main.errorPivotOverlap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pā<PERSON> nevar pā<PERSON>l<PERSON> ar tabulu.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pārskats tika saglabāts bez pamatā esošajiem datiem.<br><PERSON><PERSON><PERSON><PERSON><PERSON> pogu At<PERSON>idzin<PERSON>t, lai at<PERSON> pārskatu.", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "The Trace Precedents command requires that the active cell contain a formula which includes a valid references.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Diemžēl vienlaikus nav iespējams izdrukāt vairāk par 1500 lapām ar šībrīža programmas versiju.<br>Šis ierobežojums tiks noņemts jaunākajām versijām.", "SSE.Controllers.Main.errorProcessSaveResult": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.errorProtectedRange": "This range is not allowed for editing.", "SSE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "SSE.Controllers.Main.errorServerVersion": "Redaktora versija ir at<PERSON>. Lapa tiks pā<PERSON>, lai pie<PERSON>rotu i<PERSON>.", "SSE.Controllers.Main.errorSessionAbsolute": "Dokumentu rediģēšanas sesija ir beigusies. Ielādējiet lapu atkārtoti.", "SSE.Controllers.Main.errorSessionIdle": "Dokuments nav rediģēts ilgāku laiku. Ielādējiet lapu atkārtoti.", "SSE.Controllers.Main.errorSessionToken": "Pārtraukts savienojums serverim. Ielādējiet lapu atkārtoti.", "SSE.Controllers.Main.errorSetPassword": "Nevarēja i<PERSON> par<PERSON>.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Atrašan<PERSON>s vietas atsauce ir ne<PERSON>, jo visas šūnas neatrodas vienā kolonnā vai rindā.<br>Atlasiet šūnas, kas visas atrodas vienā kolonnā vai rindā.", "SSE.Controllers.Main.errorStockChart": "Nepareiza rindu secība. Lai izveido<PERSON> ak<PERSON>ju diagrammu novietojiet datus lapā šādā secībā:<br> s<PERSON><PERSON><PERSON><PERSON><PERSON>, maks<PERSON><PERSON><PERSON><PERSON> cena, mini<PERSON><PERSON><PERSON><PERSON> cena, slē<PERSON><PERSON><PERSON> cena.", "SSE.Controllers.Main.errorToken": "Nav pareizi noformēts dokumenta drošības marķieris.<br><PERSON><PERSON><PERSON><PERSON>, sazinieties ar savu dokumenta servera administratoru.", "SSE.Controllers.Main.errorTokenExpire": "Ir be<PERSON><PERSON>s dokumenta drošības marķiera termiņš.<br><PERSON><PERSON><PERSON><PERSON>, sazinieties ar savu dokumentu servera administratoru.", "SSE.Controllers.Main.errorUnexpectedGuid": "<PERSON><PERSON><PERSON><PERSON>.<br><PERSON><PERSON><PERSON><PERSON><PERSON> GUID. Sazinieties ar <PERSON><PERSON><PERSON>, ja k<PERSON><PERSON><PERSON> joprojām pastāv.", "SSE.Controllers.Main.errorUpdateVersion": "Faila versija ir main<PERSON>ta. Lapa tiks atkārtoti ielādēta.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Savienojums ir atja<PERSON><PERSON>, un faila versija ir mainīta.<br>Pirms varat turpināt darbu, jums ir jālejupielādē failu vai kopējiet tā saturu, lai p<PERSON><PERSON><PERSON>, ka nekas nav zaud<PERSON>, un pēc tam atkārtoti ielādējiet šo lapu.", "SSE.Controllers.Main.errorUserDrop": "<PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.errorUsersExceed": "<PERSON><PERSON> p<PERSON><PERSON>s cenu plāna atļautais lietotāju skaits.", "SSE.Controllers.Main.errorViewerDisconnect": "Savienojums ir zaud<PERSON>ts. <PERSON><PERSON><PERSON>m varat aplūkot dokumentu,<br>ta<PERSON>u nevarē<PERSON>t lejupielādēt vai drukāt, līdz nav atjaunots savienojums.", "SSE.Controllers.Main.errorWrongBracketsCount": "<PERSON><PERSON><PERSON><PERSON> ievadītā formulā.<br><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> nepareiz<PERSON> i<PERSON> s<PERSON>.", "SSE.Controllers.Main.errorWrongOperator": "<PERSON><PERSON><PERSON><PERSON> ievadītā formulā. Ir izmantots nepareizs operators.<br><PERSON><PERSON><PERSON><PERSON>, labo<PERSON><PERSON> k<PERSON>.", "SSE.Controllers.Main.errorWrongPassword": "<PERSON><PERSON><PERSON> sniegtā parole ir nepareiza.", "SSE.Controllers.Main.errRemDuplicates": "Atrastas un izdzēstas dublikātu vērtības: {0}, atlikušas unikālas vērtības: {1}.", "SSE.Controllers.Main.leavePageText": "<PERSON><PERSON><PERSON> izklājlapā ir nesaglabātas izmaiņas. Noklikšķiniet uz \"Palikt šajā lapā\" un pēc tam uz \"Saglabāt\", lai tos saglabātu. Noklikšķiniet uz 'Pamest lapu', lai atmestu visas nesaglabātās izmaiņas.", "SSE.Controllers.Main.leavePageTextOnClose": "Visas nesaglabātās izmaiņas šajā izklājlapā tiks zaudētas.<br> Noklikšķiniet uz \"Atcelt\", pēc tam uz \"Saglabāt\", lai tās saglabātu. Noklikšķiniet uz \"Labi\", lai atmestu visas nesaglabātās izmaiņas.", "SSE.Controllers.Main.loadFontsTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON> datus...", "SSE.Controllers.Main.loadFontsTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON> datus", "SSE.Controllers.Main.loadFontTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON> datus...", "SSE.Controllers.Main.loadFontTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON> datus", "SSE.Controllers.Main.loadImagesTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON> attēlus...", "SSE.Controllers.Main.loadImagesTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON> att<PERSON>", "SSE.Controllers.Main.loadImageTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON> attē<PERSON>...", "SSE.Controllers.Main.loadImageTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.loadingDocumentTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.openErrorText": "<PERSON><PERSON><PERSON> laik<PERSON> radā<PERSON>.", "SSE.Controllers.Main.openTextText": "Atvēras Izklājlapa...", "SSE.Controllers.Main.openTitleText": "Atvēras Izklājlapa", "SSE.Controllers.Main.pastInMergeAreaError": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "SSE.Controllers.Main.printTextText": "<PERSON><PERSON><PERSON>...", "SSE.Controllers.Main.printTitleText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.reloadButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>u", "SSE.Controllers.Main.requestEditFailedMessageText": "Šo dokumentu kāds šobrīd rediģē. Pamēģiniet vēlreiz vēlāk.", "SSE.Controllers.Main.requestEditFailedTitleText": "Piek<PERSON><PERSON> liegta", "SSE.Controllers.Main.saveErrorText": "<PERSON>aila nogla<PERSON><PERSON><PERSON><PERSON> laik<PERSON> radā<PERSON>.", "SSE.Controllers.Main.saveErrorTextDesktop": "Šo failu nevar saglab<PERSON>t vai izveidot.<br>Iespēja<PERSON>: <br>1. <PERSON><PERSON> ir tikai lasāms. <br>2. <PERSON>o failu rediģē cits lietotājs. <br>3. <PERSON>sks ir pilns vai bojāts.", "SSE.Controllers.Main.saveTextText": "Saglabā Izklājlapu...", "SSE.Controllers.Main.saveTitleText": "Saglabā Izklā<PERSON>lapu", "SSE.Controllers.Main.scriptLoadError": "Savienojums ir pār<PERSON><PERSON> lē<PERSON>, da<PERSON><PERSON> komponentus nevarēja ielādēt. Ielādējiet lapu atkārtoti.", "SSE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textApplyAll": "Piemērot visiem vienādojumiem", "SSE.Controllers.Main.textBuyNow": "Apmeklēt vietni", "SSE.Controllers.Main.textChangesSaved": "Visas izmaiņas saglabātas", "SSE.Controllers.Main.textClose": "Aizvērt", "SSE.Controllers.Main.textCloseTip": "Noklikšķiniet, lai aizvērtu galu", "SSE.Controllers.Main.textConfirm": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "SSE.Controllers.Main.textContactUs": "Sazināties ar p<PERSON><PERSON>", "SSE.Controllers.Main.textContinue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textConvertEquation": "<PERSON>is vienādojums tika i<PERSON>, i<PERSON><PERSON><PERSON><PERSON> veco vienādojumu redaktora versiju, kas vairs netiek atbalstīta. Lai to rediģētu, konvertējiet vienādojumu Office Math ML formātā.<br>Vai konvertēt tagad?", "SSE.Controllers.Main.textCustomLoader": "Ņemiet vērā, ka saskaņā ar licences noteikumiem jums nav tiesību mainīt ielādētāju.<br>Sazinieties ar mūsu pārdo<PERSON> noda<PERSON>, lai saņemtu piedāvājumu.", "SSE.Controllers.Main.textDisconnect": "Savienojums ir <PERSON>", "SSE.Controllers.Main.textFillOtherRows": "Aiz<PERSON><PERSON><PERSON>t citas rindas", "SSE.Controllers.Main.textFormulaFilledAllRows": "Ar formul<PERSON>m aizpild<PERSON>tā (-s) {0} rindā (-s) ir dati. Citu tukšu rindu aizpildīšana var aizņ<PERSON>t da<PERSON> min<PERSON>.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Formula aizpildīja pirmā (-s) {0} rinda (-s). Citu tukšu rindu aizpildīšana var aizņemt da<PERSON>as <PERSON>.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Formula aizpildīja tikai pirmajā (-s) {0} rindā (-s) ir dati pēc atmiņas saglabāšanas iemesla. <PERSON><PERSON><PERSON> lapā ir arī cita (-s) {1} rinda (-s) ar datiem. <PERSON><PERSON>s varat tos aizpildīt manuāli.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Formula aizpildīja tikai pirmo (-ās) {0} r<PERSON><PERSON> pēc atmiņas saglabāšanas iemesla. Citās šīs lapas rindās nav datu.", "SSE.Controllers.Main.textGuest": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textHasMacros": "Fails satur automātis<PERSON> makro.<br><PERSON><PERSON> vēlaties palaist makro?", "SSE.Controllers.Main.textKeep": "Keep", "SSE.Controllers.Main.textLearnMore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textLoadingDocument": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textLongName": "<PERSON><PERSON><PERSON><PERSON>, kas ir ma<PERSON> par 128 rakstzīm<PERSON>m.", "SSE.Controllers.Main.textNeedSynchronize": "<PERSON><PERSON> ir at<PERSON><PERSON>", "SSE.Controllers.Main.textNo": "Nē", "SSE.Controllers.Main.textNoLicenseTitle": "ONLYOFFICE pieslēguma ierobežojums", "SSE.Controllers.Main.textPaidFeature": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textPleaseWait": "Operācija var a<PERSON> vair<PERSON> la<PERSON>, nek<PERSON>aid<PERSON>. Uzgaidiet...", "SSE.Controllers.Main.textReconnect": "Savienojums ir atjaunots", "SSE.Controllers.Main.textRemember": "Atcerēties manu izvēli visiem failiem", "SSE.Controllers.Main.textRememberMacros": "Atcerēties manu izvēli visiem makro", "SSE.Controllers.Main.textRenameError": "Lietotāja nosaukums nedrīkst būt tuk<PERSON>.", "SSE.Controllers.Main.textRenameLabel": "Ievadiet no<PERSON>, ko izman<PERSON>t sadarbī<PERSON>i", "SSE.Controllers.Main.textReplace": "Aizvietot", "SSE.Controllers.Main.textRequestMacros": "<PERSON><PERSON><PERSON> veic pieprasījumu vietrādim URL. Vai vēlaties atļaut pieprasījumu %1?", "SSE.Controllers.Main.textShape": "Forma", "SSE.Controllers.Main.textStrict": "Precīz<PERSON>tai<PERSON>", "SSE.Controllers.Main.textText": "Teksts", "SSE.Controllers.Main.textTryQuickPrint": "<PERSON><PERSON><PERSON> atlas<PERSON>t <PERSON>: viss dokuments tiks drukāts ar pēdējo atlasīto vai noklusējuma printeri.<br>Vai vēlaties turpināt?", "SSE.Controllers.Main.textTryUndoRedo": "Funkcijas Atsaukt/Atcelt ir atspējotas ātrās koprediģēšanas režīmam.<br>Noklikšķiniet uz pogas 'Precīzais režīms', lai pārslēgtos uz 'Precīzo koprediģēšanas režīmu', lai rediģētu failu bez citu lietotāju iejaukšanās un nosūtītu izmaiņas tikai pēc tam, kad saglabājāt tos. Varat pārslēgties starp koprediģēšanas režīmiem, izmantojot redaktoru Papildu iestatījumi.", "SSE.Controllers.Main.textTryUndoRedoWarn": "At<PERSON><PERSON><PERSON><PERSON>/atkārto<PERSON> funkcijas ātrās koprediģēšanas režīmā ir atspējotas.", "SSE.Controllers.Main.textUndo": "Atsaukt", "SSE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "SSE.Controllers.Main.textUpdating": "Updating", "SSE.Controllers.Main.textYes": "Jā", "SSE.Controllers.Main.titleLicenseExp": "Licencei ir be<PERSON><PERSON>", "SSE.Controllers.Main.titleLicenseNotActive": "License not active", "SSE.Controllers.Main.titleServerVersion": "Atjaunināts redaktors", "SSE.Controllers.Main.titleUpdateVersion": "Version changed", "SSE.Controllers.Main.txtAccent": "Ak<PERSON>s", "SSE.Controllers.Main.txtAll": "(Visi)", "SSE.Controllers.Main.txtArt": "Ievadiet savu tekstu", "SSE.Controllers.Main.txtBasicShapes": "Pamata formas", "SSE.Controllers.Main.txtBlank": "(tukšs)", "SSE.Controllers.Main.txtButtons": "Pogas", "SSE.Controllers.Main.txtByField": "%1 no %2", "SSE.Controllers.Main.txtCallouts": "Remarkas", "SSE.Controllers.Main.txtCharts": "Diagram<PERSON>", "SSE.Controllers.Main.txtClearFilter": "<PERSON><PERSON><PERSON><PERSON><PERSON> filtru", "SSE.Controllers.Main.txtColLbls": "Kolonnas etiķetes", "SSE.Controllers.Main.txtColumn": "Kolonna", "SSE.Controllers.Main.txtConfidential": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtDate": "Datums", "SSE.Controllers.Main.txtDays": "<PERSON><PERSON>", "SSE.Controllers.Main.txtDiagramTitle": "<PERSON><PERSON><PERSON><PERSON> nosaukums", "SSE.Controllers.Main.txtEditingMode": "Uzstāda rediģēšanas režīmu...", "SSE.Controllers.Main.txtErrorLoadHistory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vē<PERSON>", "SSE.Controllers.Main.txtFiguredArrows": "Figu<PERSON>ē<PERSON> bultiņas", "SSE.Controllers.Main.txtFile": "Fails", "SSE.Controllers.Main.txtGrandTotal": "Lielās gala vērt<PERSON>bas", "SSE.Controllers.Main.txtGroup": "Grupa", "SSE.Controllers.Main.txtHours": "Stundas", "SSE.Controllers.Main.txtInfo": "Informācija", "SSE.Controllers.Main.txtLines": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMath": "<PERSON><PERSON>.", "SSE.Controllers.Main.txtMinutes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMonths": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMultiSelect": "Daudzkārtēja izvēle", "SSE.Controllers.Main.txtNone": "Neviens", "SSE.Controllers.Main.txtOr": "%1 vai %2", "SSE.Controllers.Main.txtPage": "<PERSON><PERSON>", "SSE.Controllers.Main.txtPageOf": "%1 no %2 lapas (-ām)", "SSE.Controllers.Main.txtPages": "Lapas", "SSE.Controllers.Main.txtPicture": "Picture", "SSE.Controllers.Main.txtPivotTable": "PivotTable", "SSE.Controllers.Main.txtPreparedBy": "Sagatavoja", "SSE.Controllers.Main.txtPrintArea": "<PERSON><PERSON><PERSON>_ap<PERSON><PERSON>s", "SSE.Controllers.Main.txtQuarter": "Cetur.", "SSE.Controllers.Main.txtQuarters": "Cetur<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtRowLbls": "R<PERSON>u etiķetes", "SSE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "SSE.Controllers.Main.txtScheme_Aspect": "Aspect", "SSE.Controllers.Main.txtScheme_Blue": "Blue", "SSE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "SSE.Controllers.Main.txtScheme_Blue_II": "Blue II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "SSE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "SSE.Controllers.Main.txtScheme_Green": "Green", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "SSE.Controllers.Main.txtScheme_Marquee": "Marquee", "SSE.Controllers.Main.txtScheme_Median": "Median", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Orange", "SSE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "SSE.Controllers.Main.txtScheme_Paper": "Paper", "SSE.Controllers.Main.txtScheme_Red": "Red", "SSE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "SSE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "SSE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "SSE.Controllers.Main.txtScheme_Violet": "Violet", "SSE.Controllers.Main.txtScheme_Violet_II": "Violet II", "SSE.Controllers.Main.txtScheme_Yellow": "Yellow", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "SSE.Controllers.Main.txtSeconds": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSeries": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "1. r<PERSON><PERSON><PERSON><PERSON> (apmales un izcēluma j<PERSON>la)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "2. r<PERSON><PERSON><PERSON><PERSON> (apmales un izcēluma j<PERSON>la)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "3. r<PERSON><PERSON><PERSON><PERSON> (apmales un izcēluma j<PERSON>la)", "SSE.Controllers.Main.txtShape_accentCallout1": "1. <PERSON><PERSON><PERSON><PERSON><PERSON> (izcēluma j<PERSON>)", "SSE.Controllers.Main.txtShape_accentCallout2": "2. <PERSON><PERSON><PERSON><PERSON><PERSON> (izcēluma j<PERSON>)", "SSE.Controllers.Main.txtShape_accentCallout3": "3. <PERSON><PERSON><PERSON><PERSON><PERSON> (izcēluma j<PERSON>)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Atpakaļ vai iepriekšējā poga", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "<PERSON><PERSON><PERSON><PERSON> poga", "SSE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON><PERSON><PERSON> poga", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Dokumenta poga", "SSE.Controllers.Main.txtShape_actionButtonEnd": "<PERSON><PERSON><PERSON> poga", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Uz priekšu vai nākamā poga", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Palīdzības poga", "SSE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON><PERSON><PERSON> poga", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Informācijas poga", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Filmas poga", "SSE.Controllers.Main.txtShape_actionButtonReturn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> poga", "SSE.Controllers.Main.txtShape_actionButtonSound": "Skaņas poga", "SSE.Controllers.Main.txtShape_arc": "Arh.", "SSE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5": "Leņķveida savienotājs", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Leņķveida bulti<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Leņķveida dubult<PERSON><PERSON><PERSON><PERSON> sa<PERSON>s", "SSE.Controllers.Main.txtShape_bentUpArrow": "<PERSON>z augšu izliekta bultiņa", "SSE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON>ī<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_blockArc": "Bloka arh.", "SSE.Controllers.Main.txtShape_borderCallout1": "1. <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_borderCallout2": "2. <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_borderCallout3": "3. <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bracePair": "Fig<PERSON>rie<PERSON><PERSON> pāris", "SSE.Controllers.Main.txtShape_callout1": "1. r<PERSON><PERSON><PERSON><PERSON> (nav apmales)", "SSE.Controllers.Main.txtShape_callout2": "2. r<PERSON><PERSON><PERSON><PERSON> (nav apmales)", "SSE.Controllers.Main.txtShape_callout3": "3. <PERSON><PERSON><PERSON><PERSON><PERSON> (nav apmales)", "SSE.Controllers.Main.txtShape_can": "Atc.", "SSE.Controllers.Main.txtShape_chevron": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_chord": "Akords", "SSE.Controllers.Main.txtShape_circularArrow": "Riņķveida bultiņa", "SSE.Controllers.Main.txtShape_cloud": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cloudCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON>a", "SSE.Controllers.Main.txtShape_corner": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Izliekts savienotājs", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Izliektas bultiņas <PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Izliektas dubultbultiņas savienotājs", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Uz leju izliekta bultiņa", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Pa kreisi izliekta bultiņa", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Pa labi izliekta bultiņa", "SSE.Controllers.Main.txtShape_curvedUpArrow": "<PERSON>z augšu izliekta bultiņa", "SSE.Controllers.Main.txtShape_decagon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_diagStripe": "Diagonāla svītra", "SSE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_dodecagon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_donut": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON><PERSON> vilnis", "SSE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON><PERSON> le<PERSON>", "SSE.Controllers.Main.txtShape_downArrowCallout": "Bultiņas uz leju remarka", "SSE.Controllers.Main.txtShape_ellipse": "Elipse", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Uz leju izliekta lente", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Uz augšu izliekta lente", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Blokshēma Alternatīvs process", "SSE.Controllers.Main.txtShape_flowChartCollate": "Blokshēma Sašķirot komplektos", "SSE.Controllers.Main.txtShape_flowChartConnector": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDecision": "Blokshē<PERSON>", "SSE.Controllers.Main.txtShape_flowChartDelay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDisplay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDocument": "Blokshēma Dokuments", "SSE.Controllers.Main.txtShape_flowChartExtract": "Blokshēma Ekstrakts", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Blokshēma <PERSON> g<PERSON>", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Blokshēma Ma<PERSON>ētis<PERSON> disks", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Blokshēma Tiešās <PERSON> k<PERSON>ve", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Blokshēma Secīgās <PERSON>", "SSE.Controllers.Main.txtShape_flowChartManualInput": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Blokshēma Manuālā operācija", "SSE.Controllers.Main.txtShape_flowChartMerge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Blokshē<PERSON> ", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Blokshē<PERSON>", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dati", "SSE.Controllers.Main.txtShape_flowChartOr": "Blokshēma Vai", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Blokshēma Iepriekš noteikts process", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Blokshēma Sagatavošana", "SSE.Controllers.Main.txtShape_flowChartProcess": "Blokshēma Process", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Blokshē<PERSON>", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Blokshēma Caurumota lente", "SSE.Controllers.Main.txtShape_flowChartSort": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Blokshēma Summējoša<PERSON>", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Blokshēma Terminators", "SSE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON><PERSON><PERSON> stūris", "SSE.Controllers.Main.txtShape_frame": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON> rā<PERSON>ja", "SSE.Controllers.Main.txtShape_heart": "Sirds", "SSE.Controllers.Main.txtShape_heptagon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_hexagon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_homePlate": "Pentagons", "SSE.Controllers.Main.txtShape_horizontalScroll": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_irregularSeal1": "1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_irregularSeal2": "2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftArrow": "Bulta pa kreisi", "SSE.Controllers.Main.txtShape_leftArrowCallout": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftRightArrow": "Pa kreisi un pa labi vērstā bultiņa", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Pa kreisi un pa labi vērstās bulti<PERSON> remarka", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Bultiņa pa kreisi un labi uz augšu", "SSE.Controllers.Main.txtShape_leftUpArrow": "Bultiņa pa kreisi uz augšu", "SSE.Controllers.Main.txtShape_lightningBolt": "Zibens", "SSE.Controllers.Main.txtShape_line": "Lī<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON><PERSON> b<PERSON>", "SSE.Controllers.Main.txtShape_mathDivide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathNotEqual": "Nav vienāds", "SSE.Controllers.Main.txtShape_mathPlus": "Plus", "SSE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_noSmoking": "\"Ne\" simbols", "SSE.Controllers.Main.txtShape_notchedRightArrow": "<PERSON><PERSON> bulti<PERSON>a ar ierobojumu", "SSE.Controllers.Main.txtShape_octagon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_parallelogram": "Paralelogramma", "SSE.Controllers.Main.txtShape_pentagon": "Pentagons", "SSE.Controllers.Main.txtShape_pie": "<PERSON><PERSON><PERSON><PERSON> diagramma", "SSE.Controllers.Main.txtShape_plaque": "Parakstīt", "SSE.Controllers.Main.txtShape_plus": "Plus", "SSE.Controllers.Main.txtShape_polyline1": "Skricelējums", "SSE.Controllers.Main.txtShape_polyline2": "Brīvforma", "SSE.Controllers.Main.txtShape_quadArrow": "Č<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Četrvir<PERSON><PERSON> bult<PERSON>a", "SSE.Controllers.Main.txtShape_rect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ribbon": "Lente uz leju", "SSE.Controllers.Main.txtShape_ribbon2": "Lente uz augšu", "SSE.Controllers.Main.txtShape_rightArrow": "Bulta pa labi", "SSE.Controllers.Main.txtShape_rightArrowCallout": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_rightBrace": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_rightBracket": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_round1Rect": "<PERSON><PERSON><PERSON><PERSON> viena stūra ta<PERSON>", "SSE.Controllers.Main.txtShape_round2DiagRect": "<PERSON><PERSON><PERSON><PERSON> stūra ta<PERSON>", "SSE.Controllers.Main.txtShape_round2SameRect": "<PERSON><PERSON><PERSON><PERSON> tās pa<PERSON>s puses stūra ta<PERSON>ris", "SSE.Controllers.Main.txtShape_roundRect": "<PERSON><PERSON><PERSON><PERSON> stū<PERSON>", "SSE.Controllers.Main.txtShape_rtTriangle": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON><PERSON><PERSON><PERSON> seja", "SSE.Controllers.Main.txtShape_snip1Rect": "Tais<PERSON><PERSON><PERSON> ar vienu nogrie<PERSON> stū<PERSON>,", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Taisnstūris ar nogrieztiem pretējiem stūriem", "SSE.Controllers.Main.txtShape_snip2SameRect": "Taisnstūris ar vienā pusē nogrieztiem stūriem", "SSE.Controllers.Main.txtShape_snipRoundRect": "Taisnstūris ar vienu nogrieztu un noapaļotu stūri", "SSE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star10": "10 punktu zvaigzne", "SSE.Controllers.Main.txtShape_star12": "12 punktu zvaigzne", "SSE.Controllers.Main.txtShape_star16": "16 punk<PERSON> zvaigzne", "SSE.Controllers.Main.txtShape_star24": "24 punktu zvaigzne", "SSE.Controllers.Main.txtShape_star32": "32 punktu zvaigzne", "SSE.Controllers.Main.txtShape_star4": "4 punktu zvaigzne", "SSE.Controllers.Main.txtShape_star5": "5 punktu zvaigzne", "SSE.Controllers.Main.txtShape_star6": "6 punktu zvaigzne", "SSE.Controllers.Main.txtShape_star7": "7 punktu zvaigzne", "SSE.Controllers.Main.txtShape_star8": "8 punktu zvaigzne", "SSE.Controllers.Main.txtShape_stripedRightArrow": "<PERSON><PERSON> bulti<PERSON>a ar svītrām", "SSE.Controllers.Main.txtShape_sun": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_teardrop": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_textRect": "Uzraksts", "SSE.Controllers.Main.txtShape_trapezoid": "Trapecveida", "SSE.Controllers.Main.txtShape_triangle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_upArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_upArrowCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_upDownArrow": "Bultiņa lejup un augšup", "SSE.Controllers.Main.txtShape_uturnArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_verticalScroll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Noapaļota taisnstūra remarka", "SSE.Controllers.Main.txtSheet": "<PERSON><PERSON>", "SSE.Controllers.Main.txtSlicer": "<PERSON><PERSON> grie<PERSON>", "SSE.Controllers.Main.txtStarsRibbons": "Zvaigznes un lentes", "SSE.Controllers.Main.txtStyle_Bad": "Slik<PERSON>", "SSE.Controllers.Main.txtStyle_Calculation": "Aprēķins", "SSE.Controllers.Main.txtStyle_Check_Cell": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Comma": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Paskaidrojums", "SSE.Controllers.Main.txtStyle_Good": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Heading_1": "Virsraksts 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Virsraksts 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Virsraksts 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Virsraksts 4", "SSE.Controllers.Main.txtStyle_Input": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Linked_Cell": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Neutral": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Normal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Note": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Output": "Izvade", "SSE.Controllers.Main.txtStyle_Percent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Title": "Nosa<PERSON>ms", "SSE.Controllers.Main.txtStyle_Total": "Kopā", "SSE.Controllers.Main.txtStyle_Warning_Text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> te<PERSON>", "SSE.Controllers.Main.txtTab": "Cilne", "SSE.Controllers.Main.txtTable": "Tabula", "SSE.Controllers.Main.txtTime": "Laiks", "SSE.Controllers.Main.txtUnlock": "Atbloķēt", "SSE.Controllers.Main.txtUnlockRange": "Atbloķēt diapazonu", "SSE.Controllers.Main.txtUnlockRangeDescription": "Ievadiet paroli, lai mainītu <PERSON> di<PERSON>:", "SSE.Controllers.Main.txtUnlockRangeWarning": "Diapazons, kuru mēģināt mainīt, ir a<PERSON>sar<PERSON>ts ar paroli.", "SSE.Controllers.Main.txtValues": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtView": "View", "SSE.Controllers.Main.txtXAxis": "X ass", "SSE.Controllers.Main.txtYAxis": "Y ass", "SSE.Controllers.Main.txtYears": "Gadi", "SSE.Controllers.Main.unknownErrorText": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON><PERSON><PERSON> p<PERSON>kprogramma nav atbalstīta.", "SSE.Controllers.Main.uploadDocExtMessage": "Nezinā<PERSON> attēla formāts.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Nav aug<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.uploadDocSizeMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON> i<PERSON> ir pā<PERSON>.", "SSE.Controllers.Main.uploadImageExtMessage": "Nezinā<PERSON> attēla formāts.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Nav aug<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.uploadImageSizeMessage": "Attēls ir pār<PERSON>k liels. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> izmērs ir 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> att<PERSON>...", "SSE.Controllers.Main.uploadImageTitleText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.waitText": "Uzgaidiet...", "SSE.Controllers.Main.warnBrowserIE9": "Programmai ir zemas iespējas IE9. Izmantojiet IE10 vai jaunāku versiju", "SSE.Controllers.Main.warnBrowserZoom": "Jūsu pārlūkprogrammas pašreizējais tālummaiņas iestatījums netiek pilnībā atbalstīts. Atiestatiet uz noklusējuma tā<PERSON>, nospiežot Ctrl+0.", "SSE.Controllers.Main.warnLicenseAnonymous": "Access denied for anonymous users.<br>This document will be opened for viewing only.", "SSE.Controllers.Main.warnLicenseBefore": "License not active.<br>Please contact your administrator.", "SSE.Controllers.Main.warnLicenseExceeded": "<PERSON>ūs sa<PERSON>z<PERSON>t vienlaicīgu savienojumu ierobežojumu ar %1 redaktoriem. Šis dokuments tiks atvērts tikai apskatei.<br>Sazin<PERSON>ies ar <PERSON>u, lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON>.", "SSE.Controllers.Main.warnLicenseExp": "<PERSON><PERSON><PERSON> licencei ir beid<PERSON> term<PERSON>.<br><PERSON><PERSON><PERSON><PERSON>, atjauniniet savu licenci un pārlādējiet lapu.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Licences derī<PERSON>a <PERSON>.<br>Jums nav piekļuves dokumentu rediģēšanas funkcionalitātei.<br>Sazinieties ar savu administratoru.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Licence ir jā<PERSON><PERSON><PERSON>.<br>Jums ir ierobežota piekļuve dokumentu rediģēšanas funkcionalitātei.<br>Sazinieties ar savu <PERSON>u, lai ieg<PERSON>tu pilnu piek<PERSON>uvi", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Jūs sasniedzāt %1 redaktoru lietotāju ierobežojumu. Sazinieties ar savu <PERSON>, lai u<PERSON><PERSON><PERSON><PERSON> v<PERSON>.", "SSE.Controllers.Main.warnNoLicense": "<PERSON>ūs sas<PERSON>z<PERSON>t vienlaicīgu savienojumu ierobežojumu ar %1 redaktoriem. Šis dokuments tiks atvērts tikai apskatei.<br>Sazinieties ar %1 p<PERSON><PERSON><PERSON><PERSON> koman<PERSON>, lai uzzin<PERSON>tu personīgos jau<PERSON> noteikumus.", "SSE.Controllers.Main.warnNoLicenseUsers": "Jūs sasniedzāt %1 redaktoru lietotāju ierobežojumu. Sazinieties ar %1 p<PERSON><PERSON><PERSON><PERSON> komandu, lai uzzin<PERSON>tu person<PERSON><PERSON> j<PERSON> noteikum<PERSON>.", "SSE.Controllers.Main.warnProcessRightsChange": "<PERSON><PERSON> ir liegtas <PERSON> rediģēt failu.", "SSE.Controllers.PivotTable.strSheet": "<PERSON><PERSON>", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "No actions with calculated items are allowed for this active cell.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Calculated items do not work with custom subtotals.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "An item name cannot be found. Check that you've typed name correctly and the item is present in the PivotTable report.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Averages, standard deviations, and variances are not supported when a PivotTable report has calculated items.", "SSE.Controllers.Print.strAllSheets": "Visas lapas", "SSE.Controllers.Print.textFirstCol": "<PERSON><PERSON><PERSON> kolonna", "SSE.Controllers.Print.textFirstRow": "<PERSON><PERSON><PERSON> rinda", "SSE.Controllers.Print.textFrozenCols": "Sasaldētas kolo<PERSON>s", "SSE.Controllers.Print.textFrozenRows": "Sasaldētas rindas", "SSE.Controllers.Print.textInvalidRange": "KĻŪDA! Nederīgs šūnu diapazons", "SSE.Controllers.Print.textNoRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textRepeat": "Atkārtot...", "SSE.Controllers.Print.textSelectRange": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.txtZoomToPage": "Zoom to page", "SSE.Controllers.Search.textInvalidRange": "KĻŪDA! Nederīgs šūnu diapazons", "SSE.Controllers.Search.textNoTextFound": "<PERSON><PERSON><PERSON> meklētos datus nevarēja atrast. Pielāgojiet meklēšanas opcijas.", "SSE.Controllers.Search.textReplaceSkipped": "<PERSON>r ve<PERSON>ta <PERSON>. <PERSON><PERSON> {0} gadījumi.", "SSE.Controllers.Search.textReplaceSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ir veikta. {0} gadīju<PERSON> (-i) ir aizst<PERSON>ts (-i)", "SSE.Controllers.Statusbar.errorLastSheet": "Darbgrāmatai jāb<PERSON>t vismaz viena redzama darblapa.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Nevar izdz<PERSON> darblapu.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>Savienoju<PERSON> ir <PERSON></b><br>Mēģina izveidot savienojumu. Pārbaudiet savienojuma iestatījumus.", "SSE.Controllers.Statusbar.textSheetViewTip": "Jūs esat iz<PERSON>lājlap<PERSON> skata režīmā. Filtri un kārtošana ir redzami tikai jums un tiem, kas joprojām atrodas šajā skatā.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Jūs esat iz<PERSON>lājlapas skata režīmā. <PERSON>lt<PERSON> ir redzami tikai jums un tiem, kas joprojām atrodas šajā skatā.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Darblapa var saturēt datus. Vai tiešām vēlaties turpināt?", "SSE.Controllers.Statusbar.zoomText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "<PERSON><PERSON><PERSON>, kuru vēlaties saglabāt, pa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ierīcē nav pieejams.<br><PERSON><PERSON><PERSON> stils tiks parādīts, i<PERSON><PERSON><PERSON>t vienu no sistēmas fontiem, saglab<PERSON><PERSON>s fonts tiks izmantots, kad tas būs pieejams.<br><PERSON>ai vēlaties turpināt?", "SSE.Controllers.Toolbar.errorComboSeries": "<PERSON> izve<PERSON>tu kombinēto diagrammu, atlasiet vismaz divas datu sērijas.", "SSE.Controllers.Toolbar.errorMaxPoints": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sērijas skaits diagrammā ir 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "KĻŪDA! <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datu sēriju skaits diagrammā ir 255", "SSE.Controllers.Toolbar.errorStockChart": "Nepareiza rindu secība. Lai izveido<PERSON> ak<PERSON>ju diagrammu novietojiet datus lapā šādā secībā:<br> s<PERSON><PERSON><PERSON><PERSON><PERSON>, maks<PERSON><PERSON><PERSON><PERSON> cena, mini<PERSON><PERSON><PERSON><PERSON> cena, slē<PERSON><PERSON><PERSON> cena.", "SSE.Controllers.Toolbar.helpCalcItems": "Work with calculated items in Pivot Tables.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Calculated items", "SSE.Controllers.Toolbar.helpFastUndo": "Easily undo changes while collaborating on sheets in Fast mode.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "\"Undo\" in real-time co-editing", "SSE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "SSE.Controllers.Toolbar.textAccent": "Uzsvari", "SSE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textDirectional": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textFontSizeErr": "Ievadītā vērtība ir nepareiza.<br>Ievadiet skaitlisku vērtību no 1 līdz 409", "SSE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textFunction": "Funkcijas", "SSE.Controllers.Toolbar.textIndicator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textInsert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textIntegral": "<PERSON>teg<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textLargeOperator": "Lielie operatori", "SSE.Controllers.Toolbar.textLimitAndLog": "Robežas un logaritmi", "SSE.Controllers.Toolbar.textLongOperation": "Ilga darbība", "SSE.Controllers.Toolbar.textMatrix": "Matricas", "SSE.Controllers.Toolbar.textOperator": "Operatori", "SSE.Controllers.Toolbar.textPivot": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRating": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRecentlyUsed": "Nesen lietots", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textShapes": "Formas", "SSE.Controllers.Toolbar.textSymbols": "Simboli", "SSE.Controllers.Toolbar.textWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON>z<PERSON>var<PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Bultiņa pa labi un kreisi augšā", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "<PERSON>ultiņa augšā pa kreisi", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "<PERSON><PERSON><PERSON>ņa augšā pa labi", "SSE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Apakš<PERSON>la", "SSE.Controllers.Toolbar.txtAccent_BarTop": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Formula rāmī (ar vietturi)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Formula rāmī (piemērs)", "SSE.Controllers.Toolbar.txtAccent_Check": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Apak<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Apvie<PERSON>jo<PERSON><PERSON> iekava no augšas", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vektors A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC ar aug<PERSON><PERSON><PERSON> j<PERSON>", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y ar jos<PERSON> pāri", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Trīspunkte", "SSE.Controllers.Toolbar.txtAccent_DDot": "Divpunkte", "SSE.Controllers.Toolbar.txtAccent_Dot": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "<PERSON><PERSON><PERSON> j<PERSON> p<PERSON>", "SSE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Grupēt apakšējo <PERSON>", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Grupēt au<PERSON>š<PERSON>", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "<PERSON><PERSON><PERSON><PERSON> augš<PERSON> pa kreisi", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "<PERSON><PERSON><PERSON><PERSON> augš<PERSON> pa labi", "SSE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "Izliektas pēdiņas", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Izliektas pēdiņas ar atdal<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Izliektas pēdiņas ar diviem atdal<PERSON>em", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "<PERSON><PERSON> leņķa iekava", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Figūriekavas ar <PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON> (divi no<PERSON><PERSON><PERSON><PERSON><PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (trī<PERSON>)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Grupēts objekts", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Grupēts objekts iekavās", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "G<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Binomiālais koe<PERSON> leņķiekavās", "SSE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "<PERSON><PERSON> dubultā vertik<PERSON> j<PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "<PERSON><PERSON><PERSON> dub<PERSON> vertik<PERSON> j<PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Kreis<PERSON> stāvs", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "<PERSON><PERSON><PERSON> ar <PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Vietturis starp divām labajām kvadrātiekavām", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Apgrieztas kvadrātiekavas", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Vietturis starp divām kreisajām kvadrātiekavām", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Atsevišķa iekava", "SSE.Controllers.Toolbar.txtDeleteCells": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtExpand": "<PERSON><PERSON><PERSON><PERSON><PERSON> un šķirot", "SSE.Controllers.Toolbar.txtExpandSort": "<PERSON>ti blakus izvēlētajam laukam netiks šķiroti. Vai vēlaties paplašin<PERSON>t atlasi, lai iek<PERSON><PERSON>u blakus eso<PERSON> da<PERSON>, vai turpināt tikai ar pašlaik atlasītajām šūnām?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Diagon<PERSON><PERSON> dalījums", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "dx pār dy", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "lietot lielos burtus delta y nevis cap delta x", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "da<PERSON><PERSON><PERSON><PERSON> y pār da<PERSON> x", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "delta y virs delta x", "SSE.Controllers.Toolbar.txtFractionHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionPi_2": "<PERSON> dalīts ar divi", "SSE.Controllers.Toolbar.txtFractionSmall": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFractionVertical": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Apgriez<PERSON>s k<PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Hiperboliskais apgrieztais kosīnuss", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Apgrieztais kotange<PERSON>s", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Hiperboliskais apgrieztais kotangenss", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Atpgriezta kosekansa funkci<PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Hiperboliskais apgrieztais kosekanss", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Apgrieztais se<PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Hiperboliskais apgrieztais sekanss", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "<PERSON>pg<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Hiperboliskais apgrieztais sīnuss", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Apgriez<PERSON><PERSON> tan<PERSON>s", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Hiperboliskais apgrieztais tangenss", "SSE.Controllers.Toolbar.txtFunction_Cos": "Kosinusa funkcija", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Hiperboliskais kosīnuss", "SSE.Controllers.Toolbar.txtFunction_Cot": "Kotangensa funkcija", "SSE.Controllers.Toolbar.txtFunction_Coth": "Hiperboliskais kotangenss", "SSE.Controllers.Toolbar.txtFunction_Csc": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Csch": "Hiperboliskā kosekanse", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sin θ", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Tangensa formula", "SSE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sech": "Hiperboliskais sekanss", "SSE.Controllers.Toolbar.txtFunction_Sin": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Hiperboliskais sīnuss", "SSE.Controllers.Toolbar.txtFunction_Tan": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Hiperboliskais tangenss", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "<PERSON><PERSON> un modelis", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Labs, slikts un neitrāls", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "Nav no<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON> form<PERSON>", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Temat<PERSON><PERSON> stili", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Nosaukumi un virsraksti", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Light": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtInsertCells": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Diferenci<PERSON><PERSON> teta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Diferenciā<PERSON> x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Diferen<PERSON><PERSON><PERSON> y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Integrālis ar sagrupētiem ierobežojumiem", "SSE.Controllers.Toolbar.txtIntegralDouble": "<PERSON><PERSON><PERSON> integr<PERSON>", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Dubultais integrālis ar grupētiem ierobežojumiem", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Dubultais integrālis ar ierobežojumiem", "SSE.Controllers.Toolbar.txtIntegralOriented": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Kontūrintegrālis ar grupētiem ierobežojumiem", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "<PERSON><PERSON><PERSON> integr<PERSON> ar grupētiem ierobežojumiem", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "<PERSON><PERSON><PERSON> in<PERSON> ar ierobežojumiem", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "<PERSON><PERSON><PERSON><PERSON> integr<PERSON>", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Apjoma integrālis ar grupētiem limitiem", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "<PERSON><PERSON><PERSON><PERSON> integr<PERSON>", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Integrālis ar limit<PERSON>m", "SSE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> integrā<PERSON> ar grupētiem limitiem", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> integr<PERSON> ar limit<PERSON>m", "SSE.Controllers.Toolbar.txtInvalidRange": "KĻŪDA! Nederīgs šūnas diapazons", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Loģiski un", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Loģiski un ar apakšindeksa apakšējo robežu", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Loģiski un ar ierobežojumiem", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Loģiski un ar apakšindeksa apakšējo robežu", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Loģiski un ar apakšraksta/augšraksta ierobežojumiem", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Kopražojums", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Kopražojums ar apakšējo ierobežojumu", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Kopražojums ar ierobežojumiem", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Kopražojums ar apakšindeksa ierobežojumu", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Kopražojums ar apakšindeksa/virsraksta ierobežojumiem", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Summēšana virs k no n izvēlieties k", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Summēšana no i vienāda ar nulli līdz n", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "<PERSON><PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> divus in<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produkta pie<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Apvienības piemē<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Loģiski vai", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Loģiski vai ar apa<PERSON><PERSON><PERSON><PERSON>u", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Loģiski vai ar ierobežojumiem", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Loģiski vai ar apakšraksta apakšējo robežu", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Loģiski vai ar apa<PERSON>š<PERSON>/augšraksta ierobežojumiem", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar a<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON>ā<PERSON> ar ierobežojumie<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "<PERSON><PERSON><PERSON><PERSON><PERSON>ā<PERSON> ar apakš<PERSON> apakšējo <PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Krustošanās ar a<PERSON>/augšraksta ierobežojumiem", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Produkts", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produkts ar a<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produkts ar ierobežojumiem", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produkts ar apakšraksta apakšējo robežu", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produkts ar apakšindeksa/augšraksts ierobežojumiem", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar a<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Summēšana ar ierobežojumiem", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Summēšana ar apakš<PERSON> apakš<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Summēšana ar a<PERSON>kšindek<PERSON>/virsraksta ierobežojumiem", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Apvienība", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Apvienība ar apa<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Apvienība ar limit<PERSON>m", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Savienība ar apakšraksta apakšējo <PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Savienība ar a<PERSON>kšindeksa/virsraksta ierobežojumiem", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Ierobežot pie<PERSON>ēru", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Limits", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Nat<PERSON><PERSON><PERSON>s logaritms", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logaritms", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritms", "SSE.Controllers.Toolbar.txtLimitLog_Max": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimums", "SSE.Controllers.Toolbar.txtLockSort": "Dati ir atrasti blakus jūsu atlasei, bet jums nav pietiekamu atļauju, lai main<PERSON>tu <PERSON>.<br>Vai vēlaties turpināt pašreizējo atlasi?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "Tukša matrica 1x2", "SSE.Controllers.Toolbar.txtMatrix_1_3": "<PERSON>kša matrica 1x3", "SSE.Controllers.Toolbar.txtMatrix_2_1": "<PERSON><PERSON>ša matrica 2x1", "SSE.Controllers.Toolbar.txtMatrix_2_2": "<PERSON>kša matrica 2x2", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "<PERSON><PERSON><PERSON> matrica ar iekavām", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "<PERSON><PERSON><PERSON> matrica ar iekavām", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "<PERSON><PERSON><PERSON> matrica ar iekavām", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "<PERSON><PERSON><PERSON> matrica ar iekavām", "SSE.Controllers.Toolbar.txtMatrix_2_3": "<PERSON><PERSON><PERSON> matrica 2x3", "SSE.Controllers.Toolbar.txtMatrix_3_1": "<PERSON>kša matrica 3x1", "SSE.Controllers.Toolbar.txtMatrix_3_2": "<PERSON>kša matrica 3x2", "SSE.Controllers.Toolbar.txtMatrix_3_3": "<PERSON>kša matrica 3x3", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Pamatlīnijas punkti", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Vid<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON>tā matrica iekavās", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Retā matrica", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "Identitātes matrica 2 x 2", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "2x2 identitātes matrica ar tukšām ārpus diagonālām šūnām", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "Identitātes matrica 3x3", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 identitātes matrica ar tukšām ārpus diagonālām šūnām", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Bultiņa pa labi un kreisi apakšā", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Bultiņa pa labi un kreisi augšā", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Bultiņa apa<PERSON>š<PERSON> pa kreisi", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "<PERSON>ultiņa augšā pa kreisi", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Bultiņa apa<PERSON>š<PERSON> pa kreisi", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "<PERSON><PERSON><PERSON>ņa augšā pa labi", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Divpunktu vienāds", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Izeja", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Izejas delta", "SSE.Controllers.Toolbar.txtOperator_Definition": "<PERSON><PERSON><PERSON><PERSON> pēc defin<PERSON>", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta vienāda ar", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Dubultbultiņa pa labi un kreisi apakšā", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Dubultbultiņa pa labi un kreisi augšā", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Bultiņa apa<PERSON>š<PERSON> pa kreisi", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "<PERSON>ultiņa augšā pa kreisi", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Bultiņa apa<PERSON>š<PERSON> pa kreisi", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "<PERSON><PERSON><PERSON>ņa augšā pa labi", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Vienāds vienāds", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus vienāds", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "K<PERSON><PERSON>ātiskās formulas labā puse", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ne no kvadrāta plus b kva<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar pak<PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar <PERSON>", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_1": "x apakšindekss y kvadrātā", "SSE.Controllers.Toolbar.txtScriptCustom_2": "no e līdz mīnus i omega t", "SSE.Controllers.Toolbar.txtScriptCustom_3": "x kvadrātā", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Y kreisais augšraksts un kreisais apakšraksts viens", "SSE.Controllers.Toolbar.txtScriptSub": "Apakšraksts", "SSE.Controllers.Toolbar.txtScriptSubSup": "Apakšraksts-augšraksts", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Kreisais apakšraksts-augšraksts", "SSE.Controllers.Toolbar.txtScriptSup": "Augšraksts", "SSE.Controllers.Toolbar.txtSorting": "Šķirošana", "SSE.Controllers.Toolbar.txtSortSelected": "Šķirot izvēlēto", "SSE.Controllers.Toolbar.txtSymbol_about": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_additional": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON> ar", "SSE.Controllers.Toolbar.txtSymbol_ast": "Operators-zvaigznīte", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Bet", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Aizzīmes operators", "SSE.Controllers.Toolbar.txtSymbol_cap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Viduslī<PERSON>jas horizontālā elipse", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "Ķīn.", "SSE.Controllers.Toolbar.txtSymbol_cong": "Aptuveni vienāds ar", "SSE.Controllers.Toolbar.txtSymbol_cup": "Apvienība", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Diagon<PERSON><PERSON><PERSON> elipse lejā pa labi", "SSE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilons", "SSE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Identisks ar", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_factorial": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_forall": "Visiem", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "Lie<PERSON>ā<PERSON> par vai vienāds ar", "SSE.Controllers.Toolbar.txtSymbol_gg": "<PERSON><PERSON><PERSON> par", "SSE.Controllers.Toolbar.txtSymbol_greater": "<PERSON><PERSON><PERSON><PERSON> nekā", "SSE.Controllers.Toolbar.txtSymbol_in": "Elements no", "SSE.Controllers.Toolbar.txtSymbol_inc": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Bezgalība", "SSE.Controllers.Toolbar.txtSymbol_iota": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Bultiņa pa kreisi", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Bultiņa pa labi un kreisi", "SSE.Controllers.Toolbar.txtSymbol_leq": "<PERSON><PERSON>āk nekā vai vienāds ar", "SSE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON><PERSON><PERSON> nek<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ll": "<PERSON><PERSON><PERSON> par", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "Mīnuss pluss", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Nav vien<PERSON>ds ar", "SSE.Controllers.Toolbar.txtSymbol_ni": "Satur kā dalībnieks", "SSE.Controllers.Toolbar.txtSymbol_not": "Negatī<PERSON><PERSON> z<PERSON>me", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Nepastāv", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_percent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_phi": "Fi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus, mīnus", "SSE.Controllers.Toolbar.txtSymbol_propto": "<PERSON>por<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> sakne", "SSE.Controllers.Toolbar.txtSymbol_qed": "<PERSON> <PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Diagonā<PERSON>ā elipse augšā pa labi", "SSE.Controllers.Toolbar.txtSymbol_rho": "Ro", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Bultiņa pa labi", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_theta": "Theta", "SSE.Controllers.Toolbar.txtSymbol_times": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Ypsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Epsilona variants", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Fī variants", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Pi variants", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Ro variants", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Sigma variants", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Theta variants", "SSE.Controllers.Toolbar.txtSymbol_vdots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> elipse", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Ksi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Tumšs tabulas stils", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Gaišs tabulas stils", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Vid<PERSON>js tabulas stils", "SSE.Controllers.Toolbar.warnLongOperation": "<PERSON><PERSON><PERSON><PERSON>, ko gras<PERSON><PERSON> veikt, var prasīt diezgan daudz laika.<br>Vai vēlaties turpināt?", "SSE.Controllers.Toolbar.warnMergeLostData": "A<PERSON><PERSON><PERSON><PERSON><PERSON>ū<PERSON>ā paliks tikai dati no augšējās kreis<PERSON>. <br>Vai tiešām vēlaties turpināt?", "SSE.Controllers.Toolbar.warnNoRecommended": "To create a chart, select the cells that contain the data you'd like to use.<br>If you have names for the rows and columns and you'd like use them as labels, include them in your selection.", "SSE.Controllers.Viewport.textFreezePanes": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "SSE.Controllers.Viewport.textFreezePanesShadow": "<PERSON><PERSON><PERSON><PERSON><PERSON> iesaldētu rūšu ēnu", "SSE.Controllers.Viewport.textHideFBar": "Slēpt formulas joslu", "SSE.Controllers.Viewport.textHideGridlines": "Slēpt režģlīnijas", "SSE.Controllers.Viewport.textHideHeadings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AdvancedSeparatorDialog.textLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kas tiek izmantoti ciparu datu atpaz<PERSON>", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Teksta kvalifikators", "SSE.Views.AdvancedSeparatorDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(neviens)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Pielāgots filtrs", "SSE.Views.AutoFilterDialog.textAddSelection": "Pievienot izvēlēto fragmentu filtram", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Blanks}", "SSE.Views.AutoFilterDialog.textSelectAll": "Izvēlēties visu", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Izvē<PERSON>ē<PERSON> visus meklē<PERSON> rezultātus", "SSE.Views.AutoFilterDialog.textWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtAboveAve": "<PERSON><PERSON> vidējā", "SSE.Views.AutoFilterDialog.txtAfter": "Pēc...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "All dates in the period", "SSE.Views.AutoFilterDialog.txtApril": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtAugust": "Augusts", "SSE.Views.AutoFilterDialog.txtBefore": "Pirms...", "SSE.Views.AutoFilterDialog.txtBegins": "<PERSON><PERSON><PERSON> ar...", "SSE.Views.AutoFilterDialog.txtBelowAve": "<PERSON><PERSON> vidēj<PERSON>", "SSE.Views.AutoFilterDialog.txtBetween": "Starp...", "SSE.Views.AutoFilterDialog.txtClear": "Nodzēst visu", "SSE.Views.AutoFilterDialog.txtContains": "Satur...", "SSE.Views.AutoFilterDialog.txtDateFilter": "Date filter", "SSE.Views.AutoFilterDialog.txtDecember": "Decembris", "SSE.Views.AutoFilterDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> filtru", "SSE.Views.AutoFilterDialog.txtEnds": "<PERSON><PERSON><PERSON> ar...", "SSE.Views.AutoFilterDialog.txtEquals": "Vienāds...", "SSE.Views.AutoFilterDialog.txtFebruary": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc <PERSON> kr<PERSON>s", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc fonta krā<PERSON>s", "SSE.Views.AutoFilterDialog.txtGreater": "<PERSON><PERSON><PERSON><PERSON> nekā...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "<PERSON><PERSON><PERSON><PERSON> par vai vienāds ar...", "SSE.Views.AutoFilterDialog.txtJanuary": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtJuly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtJune": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Etiķetes filtrs", "SSE.Views.AutoFilterDialog.txtLastMonth": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mēnesis", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Last quarter", "SSE.Views.AutoFilterDialog.txtLastWeek": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtLastYear": "Last year", "SSE.Views.AutoFilterDialog.txtLess": "<PERSON><PERSON><PERSON><PERSON> nekā...", "SSE.Views.AutoFilterDialog.txtLessEquals": "<PERSON><PERSON><PERSON><PERSON> nekā vai vienāds ar...", "SSE.Views.AutoFilterDialog.txtMarch": "Marts", "SSE.Views.AutoFilterDialog.txtMay": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtNextMonth": "<PERSON><PERSON><PERSON><PERSON><PERSON> m<PERSON>is", "SSE.Views.AutoFilterDialog.txtNextQuarter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtNextWeek": "<PERSON>āka<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtNextYear": "<PERSON>āka<PERSON><PERSON> gads", "SSE.Views.AutoFilterDialog.txtNotBegins": "<PERSON><PERSON><PERSON><PERSON> ar...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Ne starp...", "SSE.Views.AutoFilterDialog.txtNotContains": "Nesatur...", "SSE.Views.AutoFilterDialog.txtNotEnds": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Nav vienāds ar...", "SSE.Views.AutoFilterDialog.txtNovember": "Novembris", "SSE.Views.AutoFilterDialog.txtNumFilter": "Numuru filtrs", "SSE.Views.AutoFilterDialog.txtOctober": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtQuarter1": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Quarter 1", "SSE.Views.AutoFilterDialog.txtReapply": "<PERSON><PERSON><PERSON><PERSON> no jauna", "SSE.Views.AutoFilterDialog.txtSeptember": "Septembris", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Šķirot pēc šūnu kr<PERSON>s", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Šķirot pēc fonta krāsas", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Šķirot no augstākā uz zemāko", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Šķirot no zemākā uz augstāko", "SSE.Views.AutoFilterDialog.txtSortOption": "Citas kārtošanas opcijas...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Teksta filtrs", "SSE.Views.AutoFilterDialog.txtThisMonth": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtThisQuarter": "This quarter", "SSE.Views.AutoFilterDialog.txtThisWeek": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtThisYear": "This Year", "SSE.Views.AutoFilterDialog.txtTitle": "Filtrs", "SSE.Views.AutoFilterDialog.txtToday": "Šodien", "SSE.Views.AutoFilterDialog.txtTomorrow": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtTop10": "Pirmie 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Vērtību filtrs", "SSE.Views.AutoFilterDialog.txtYearToDate": "Year to date", "SSE.Views.AutoFilterDialog.txtYesterday": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.warnFilterError": "Lai lietotu vērt<PERSON><PERSON> filtru, apgabalā Vērtības ir nepieciešams vismaz viens lauks.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Jums j<PERSON>izv<PERSON>las vismaz viena vērtība", "SSE.Views.CellEditor.textManager": "<PERSON><PERSON><PERSON>", "SSE.Views.CellEditor.tipFormula": "<PERSON>ev<PERSON><PERSON>", "SSE.Views.CellRangeDialog.errorMaxRows": "KĻŪDA! <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datu sēriju skaits diagrammā ir 255", "SSE.Views.CellRangeDialog.errorStockChart": "Nepareiza rindu secība. Lai izveido<PERSON> ak<PERSON>ju diagrammu novietojiet datus lapā šādā secībā:<br> s<PERSON><PERSON><PERSON><PERSON><PERSON>, maks<PERSON><PERSON><PERSON><PERSON> cena, mini<PERSON><PERSON><PERSON><PERSON> cena, slē<PERSON><PERSON><PERSON> cena.", "SSE.Views.CellRangeDialog.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "SSE.Views.CellRangeDialog.txtInvalidRange": "KĻŪDA! Nederīgs šūnu diapazons", "SSE.Views.CellRangeDialog.txtTitle": "<PERSON><PERSON><PERSON> datu di<PERSON>u", "SSE.Views.CellSettings.strShrink": "<PERSON><PERSON><PERSON><PERSON><PERSON>, lai ietilptu", "SSE.Views.CellSettings.strWrap": "Aplauzt tekstu", "SSE.Views.CellSettings.textAngle": "Leņķis", "SSE.Views.CellSettings.textBackColor": "Fona krāsa", "SSE.Views.CellSettings.textBackground": "Fona krāsa", "SSE.Views.CellSettings.textBorderColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "Robežas stils", "SSE.Views.CellSettings.textClearRule": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textColorScales": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.CellSettings.textCondFormat": "Nosacījuma <PERSON>", "SSE.Views.CellSettings.textControl": "Teksta vadīkla", "SSE.Views.CellSettings.textDataBars": "<PERSON><PERSON>", "SSE.Views.CellSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textFill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textForeground": "Priekšplāna krāsa", "SSE.Views.CellSettings.textGradient": "Gradienta punkti", "SSE.Views.CellSettings.textGradientColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "Gradienta aizpildīju<PERSON>", "SSE.Views.CellSettings.textIndent": "Atkāpe", "SSE.Views.CellSettings.textItems": "V<PERSON><PERSON>", "SSE.Views.CellSettings.textLinear": "Lineārs", "SSE.Views.CellSettings.textManageRule": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "SSE.Views.CellSettings.textNewRule": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textNoFill": "<PERSON>z <PERSON>", "SSE.Views.CellSettings.textOrientation": "Teksta orientācija", "SSE.Views.CellSettings.textPattern": "Raksts", "SSE.Views.CellSettings.textPatternFill": "Raksts", "SSE.Views.CellSettings.textPosition": "Pozīcija", "SSE.Views.CellSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textSelectBorders": "Apmales stilu <PERSON>", "SSE.Views.CellSettings.textSelection": "No pašreizējās atlases", "SSE.Views.CellSettings.textThisPivot": "No <PERSON>ī rakursa", "SSE.Views.CellSettings.textThisSheet": "<PERSON> <PERSON><PERSON>", "SSE.Views.CellSettings.textThisTable": "No šīs tabulas", "SSE.Views.CellSettings.tipAddGradientPoint": "Pievienot gradienta punktu", "SSE.Views.CellSettings.tipAll": "Iestatīt ārē<PERSON> a<PERSON>ali un visas iekšējās lī<PERSON>", "SSE.Views.CellSettings.tipBottom": "Iestatīt tikai ārējo a<PERSON> a<PERSON>ali", "SSE.Views.CellSettings.tipDiagD": "Iestatīt <PERSON> le<PERSON>", "SSE.Views.CellSettings.tipDiagU": "<PERSON>estat<PERSON><PERSON> au<PERSON>", "SSE.Views.CellSettings.tipInner": "Iestatīt tikai iekšēj<PERSON> l<PERSON>", "SSE.Views.CellSettings.tipInnerHor": "Iestatīt tikai <PERSON> i<PERSON> l<PERSON>", "SSE.Views.CellSettings.tipInnerVert": "Iestatīt tikai vertik<PERSON> i<PERSON> l<PERSON>", "SSE.Views.CellSettings.tipLeft": "Iestatīt tikai ār<PERSON><PERSON> k<PERSON> a<PERSON>", "SSE.Views.CellSettings.tipNone": "Iestatīt bez a<PERSON>alēm", "SSE.Views.CellSettings.tipOuter": "<PERSON>estat<PERSON>t tikai ā<PERSON><PERSON>", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Noņemt gradienta punktu", "SSE.Views.CellSettings.tipRight": "Iestatīt tikai ār<PERSON>jo labo a<PERSON>ali", "SSE.Views.CellSettings.tipTop": "Iestatīt tikai ārējo aug<PERSON> a<PERSON>ali", "SSE.Views.ChartDataDialog.errorInFormula": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> form<PERSON>ā ir k<PERSON>.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Atsauce ir ne<PERSON><PERSON>ga. Atsaucei ir jābūt uz atvērtu darblapu.", "SSE.Views.ChartDataDialog.errorMaxPoints": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sērijas skaits diagrammā ir 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datu s<PERSON><PERSON>ju skaits diagrammā ir 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Atsauce ir nederīga. Atsaucēm uz nosaukumiem, vē<PERSON><PERSON><PERSON><PERSON><PERSON>, izmēriem vai datu etiķetēm jābūt vienā <PERSON>ūn<PERSON>, rindā vai kolonnā.", "SSE.Views.ChartDataDialog.errorNoValues": "<PERSON>, s<PERSON><PERSON><PERSON><PERSON> ir jā<PERSON><PERSON><PERSON> vismaz vienai vērtībai.", "SSE.Views.ChartDataDialog.errorStockChart": "Nepareiza rindu secība. Lai izveido<PERSON> ak<PERSON>ju diagrammu novietojiet datus lapā šādā secībā:<br> s<PERSON><PERSON><PERSON><PERSON><PERSON>, maks<PERSON><PERSON><PERSON><PERSON> cena, mini<PERSON><PERSON><PERSON><PERSON> cena, slē<PERSON><PERSON><PERSON> cena.", "SSE.Views.ChartDataDialog.textAdd": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textCategory": "Horizont<PERSON><PERSON><PERSON><PERSON> (kategorijas) asu etiķetes", "SSE.Views.ChartDataDialog.textData": "Diagrammas datu diapazons", "SSE.Views.ChartDataDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textDown": "Uz leju", "SSE.Views.ChartDataDialog.textEdit": "Rediģēt", "SSE.Views.ChartDataDialog.textInvalidRange": "<PERSON><PERSON><PERSON><PERSON> diapazons", "SSE.Views.ChartDataDialog.textSelectData": "<PERSON><PERSON><PERSON> da<PERSON>", "SSE.Views.ChartDataDialog.textSeries": "Leģendas ieraksti (s<PERSON>ri<PERSON>)", "SSE.Views.ChartDataDialog.textSwitch": "<PERSON><PERSON>t rindu/kolonnu", "SSE.Views.ChartDataDialog.textTitle": "Diagram<PERSON> dati", "SSE.Views.ChartDataDialog.textUp": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.errorInFormula": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> form<PERSON>ā ir k<PERSON>.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Atsauce ir ne<PERSON><PERSON>ga. Atsaucei ir jābūt uz atvērtu darblapu.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sērijas skaits diagrammā ir 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datu s<PERSON><PERSON>ju skaits diagrammā ir 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Atsauce ir nederīga. Atsaucēm uz nosaukumiem, vē<PERSON><PERSON><PERSON><PERSON><PERSON>, izmēriem vai datu etiķetēm jābūt vienā <PERSON>ūn<PERSON>, rindā vai kolonnā.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "<PERSON>, s<PERSON><PERSON><PERSON><PERSON> ir jā<PERSON><PERSON><PERSON> vismaz vienai vērtībai.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Nepareiza rindu secība. Lai izveido<PERSON> ak<PERSON>ju diagrammu novietojiet datus lapā šādā secībā:<br> s<PERSON><PERSON><PERSON><PERSON><PERSON>, maks<PERSON><PERSON><PERSON><PERSON> cena, mini<PERSON><PERSON><PERSON><PERSON> cena, slē<PERSON><PERSON><PERSON> cena.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "<PERSON><PERSON><PERSON><PERSON> diapazons", "SSE.Views.ChartDataRangeDialog.textSelectData": "<PERSON><PERSON><PERSON> da<PERSON>", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Ass etiķetes diapazons", "SSE.Views.ChartDataRangeDialog.txtChoose": "Izvēlēties diapazonu", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Ass etiķetes", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Rediģēt sērijas", "SSE.Views.ChartDataRangeDialog.txtValues": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtXValues": "X vērtības", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y vērtības", "SSE.Views.ChartSettings.errorMaxRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datu s<PERSON><PERSON>ju skaits diagrammā ir 255.", "SSE.Views.ChartSettings.strLineWeight": "Līnijas biezums", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.text3dDepth": "Dziļums (% no bāzes)", "SSE.Views.ChartSettings.text3dHeight": "Augstums (% no bāzes)", "SSE.Views.ChartSettings.text3dRotation": "3D rotācija", "SSE.Views.ChartSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pap<PERSON>du i<PERSON>", "SSE.Views.ChartSettings.textAutoscale": "Automātiska <PERSON>", "SSE.Views.ChartSettings.textBorderSizeErr": "Ievadītā vērtība nav pareiza.<br>Ievadiet vērtību starp 0 pt un 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "<PERSON><PERSON><PERSON> veidu", "SSE.Views.ChartSettings.textChartType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> diagrammas veidu", "SSE.Views.ChartSettings.textDefault": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textDown": "Uz leju", "SSE.Views.ChartSettings.textEditData": "Rediģēt datus un atrašanās vietu", "SSE.Views.ChartSettings.textFirstPoint": "<PERSON>rmais <PERSON>", "SSE.Views.ChartSettings.textHeight": "Augstums", "SSE.Views.ChartSettings.textHighPoint": "Augstais punkts", "SSE.Views.ChartSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textLastPoint": "<PERSON>ē<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textLowPoint": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textMarkers": "Marķieri", "SSE.Views.ChartSettings.textNarrow": "Šaurs redzes lauks", "SSE.Views.ChartSettings.textNegativePoint": "Negatīvais punkts", "SSE.Views.ChartSettings.textPerspective": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textRanges": "Datu diapazons", "SSE.Views.ChartSettings.textRight": "Labais", "SSE.Views.ChartSettings.textRightAngle": "Taisnā leņķa asis", "SSE.Views.ChartSettings.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datus", "SSE.Views.ChartSettings.textShow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSize": "Izmērs", "SSE.Views.ChartSettings.textStyle": "Stils", "SSE.Views.ChartSettings.textSwitch": "<PERSON><PERSON>t rindu/kolonnu", "SSE.Views.ChartSettings.textType": "Tips", "SSE.Views.ChartSettings.textUp": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textWiden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> redzes lauku", "SSE.Views.ChartSettings.textWidth": "Platums", "SSE.Views.ChartSettings.textX": "X rotācija", "SSE.Views.ChartSettings.textY": "<PERSON>", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "KĻŪDA! <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> punktu sērijas skaits diagrammā ir 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "KĻŪDA! <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datu sēriju skaits diagrammā ir 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Nepareiza rindu secība. Lai izveido<PERSON> ak<PERSON>ju diagrammu novietojiet datus lapā šādā secībā:<br> s<PERSON><PERSON><PERSON><PERSON><PERSON>, maks<PERSON><PERSON><PERSON><PERSON> cena, mini<PERSON><PERSON><PERSON><PERSON> cena, slē<PERSON><PERSON><PERSON> cena.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Nepārvietot un neveidot iz<PERSON>ērus ar šū<PERSON>m", "SSE.Views.ChartSettingsDlg.textAlt": "Alternatīvs teksts", "SSE.Views.ChartSettingsDlg.textAltDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAltTip": "Vizuālās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, fig<PERSON><PERSON><PERSON>, diagramm<PERSON> vai tabulā.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Nosa<PERSON>ms", "SSE.Views.ChartSettingsDlg.textAuto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAutoEach": "Automā<PERSON><PERSON> katram", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisOptions": "<PERSON><PERSON> op<PERSON>", "SSE.Views.ChartSettingsDlg.textAxisPos": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisSettings": "<PERSON><PERSON> <PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Nosa<PERSON>ms", "SSE.Views.ChartSettingsDlg.textBase": "Pamata", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Starp ķeksīšiem", "SSE.Views.ChartSettingsDlg.textBillions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCategoryName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCenter": "Centrā", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Diagrammas elementi un<br>Diagrammas leģenda", "SSE.Views.ChartSettingsDlg.textChartTitle": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCross": "<PERSON><PERSON>punkts", "SSE.Views.ChartSettingsDlg.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDataColumns": "<PERSON><PERSON> s<PERSON><PERSON> kolo<PERSON>", "SSE.Views.ChartSettingsDlg.textDataLabels": "Datu etiķetes", "SSE.Views.ChartSettingsDlg.textDataRows": "<PERSON><PERSON> s<PERSON><PERSON> rind<PERSON>s", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textEmptyCells": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> un tukšās <PERSON>", "SSE.Views.ChartSettingsDlg.textEmptyLine": "<PERSON><PERSON><PERSON> datu <PERSON> ar l<PERSON>", "SSE.Views.ChartSettingsDlg.textFit": "Saskaņot ar platumu", "SSE.Views.ChartSettingsDlg.textFixed": "<PERSON><PERSON>ēts", "SSE.Views.ChartSettingsDlg.textFormat": "Etiķetes formāts", "SSE.Views.ChartSettingsDlg.textGaps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGridLines": "Režģlīnijas", "SSE.Views.ChartSettingsDlg.textGroup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> grupa", "SSE.Views.ChartSettingsDlg.textHide": "Paslēpt", "SSE.Views.ChartSettingsDlg.textHideAxis": "Paslēpt asi", "SSE.Views.ChartSettingsDlg.textHigh": "Augsts", "SSE.Views.ChartSettingsDlg.textHorAxis": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorizontal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textInnerBottom": "<PERSON>ek<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textInnerTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> aug<PERSON>", "SSE.Views.ChartSettingsDlg.textInvalidRange": "KĻŪDA! Nederīgs šūnu diapazons", "SSE.Views.ChartSettingsDlg.textLabelDist": "Ass etiķetes attālums", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Intervāls starp etiķetēm ", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Etiķetes opcijas", "SSE.Views.ChartSettingsDlg.textLabelPos": "Etiķetes novietojums", "SSE.Views.ChartSettingsDlg.textLayout": "Izklājums", "SSE.Views.ChartSettingsDlg.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa k<PERSON>i", "SSE.Views.ChartSettingsDlg.textLegendBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendPos": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendRight": "Labais", "SSE.Views.ChartSettingsDlg.textLegendTop": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLines": "<PERSON><PERSON><PERSON><PERSON> ", "SSE.Views.ChartSettingsDlg.textLocationRange": "Atrašanās vietas diapazons", "SSE.Views.ChartSettingsDlg.textLogScale": "Logaritmiska skala", "SSE.Views.ChartSettingsDlg.textLow": "Zems", "SSE.Views.ChartSettingsDlg.textMajor": "Galvenais", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Pamata un papildus", "SSE.Views.ChartSettingsDlg.textMajorType": "Pamattips", "SSE.Views.ChartSettingsDlg.textManual": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarkers": "Marķieri", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Intervāls starp atzī<PERSON>ēm", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vērt<PERSON>", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinorType": "Papildu tips", "SSE.Views.ChartSettingsDlg.textMinValue": "Minimā<PERSON>ā vērtība", "SSE.Views.ChartSettingsDlg.textNextToAxis": "<PERSON><PERSON><PERSON>j", "SSE.Views.ChartSettingsDlg.textNone": "Neviens", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Nav p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textOneCell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bet neveidot izmērus ar <PERSON>m", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textOut": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textOuterTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> no augšas", "SSE.Views.ChartSettingsDlg.textOverlay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textReverse": "Vērtības apgrieztā secībā", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Apgrieztā secībā", "SSE.Views.ChartSettingsDlg.textRight": "Labais", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Labais pārklājums", "SSE.Views.ChartSettingsDlg.textRotated": "Pagriezts", "SSE.Views.ChartSettingsDlg.textSameAll": "<PERSON><PERSON><PERSON><PERSON> visiem", "SSE.Views.ChartSettingsDlg.textSelectData": "<PERSON><PERSON><PERSON> da<PERSON>", "SSE.Views.ChartSettingsDlg.textSeparator": "Datu etiķešu atdalītājs", "SSE.Views.ChartSettingsDlg.textSeriesName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gra<PERSON> a<PERSON>", "SSE.Views.ChartSettingsDlg.textShowData": "<PERSON><PERSON><PERSON><PERSON><PERSON> slēpto rindu un kolonnu datus", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "<PERSON><PERSON><PERSON><PERSON><PERSON> tukš<PERSON> š<PERSON> kā", "SSE.Views.ChartSettingsDlg.textShowEquation": "Display equation on chart", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "<PERSON><PERSON><PERSON><PERSON><PERSON> asi", "SSE.Views.ChartSettingsDlg.textShowValues": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> grafika vērt<PERSON>", "SSE.Views.ChartSettingsDlg.textSingle": "Atsevišķs spārk<PERSON>s", "SSE.Views.ChartSettingsDlg.textSmooth": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSnap": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSparkRanges": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textStraight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textStyle": "Stils", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTickOptions": "Atzī<PERSON><PERSON><PERSON><PERSON> i<PERSON>", "SSE.Views.ChartSettingsDlg.textTitle": "Diagramma <PERSON> <PERSON><PERSON><PERSON><PERSON> i<PERSON>", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "S<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTop": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Trendline options", "SSE.Views.ChartSettingsDlg.textTrillions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTwoCell": "Pārvietot un izmērīt ar šūnām", "SSE.Views.ChartSettingsDlg.textType": "Tips", "SSE.Views.ChartSettingsDlg.textTypeData": "Tips un dati", "SSE.Views.ChartSettingsDlg.textUnits": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vertik<PERSON> ass", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X ass virsraksts", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y ass virsraksts", "SSE.Views.ChartSettingsDlg.textZero": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "SSE.Views.ChartTypeDialog.errorComboSeries": "<PERSON> izve<PERSON>tu kombinēto diagrammu, atlasiet vismaz divas datu sērijas.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Atlasītajam diagrammas veidam ir nepieciešama sekundārā ass, ko izmanto esošā diagramma. Izvēlieties citu diagrammas veidu.", "SSE.Views.ChartTypeDialog.textSecondary": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ass", "SSE.Views.ChartTypeDialog.textSeries": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textStyle": "Stils", "SSE.Views.ChartTypeDialog.textTitle": "Diagrammas tips", "SSE.Views.ChartTypeDialog.textType": "Tips", "SSE.Views.ChartWizardDialog.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartWizardDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order: opening price, max price, min price, closing price.", "SSE.Views.ChartWizardDialog.textRecommended": "Recommended", "SSE.Views.ChartWizardDialog.textSecondary": "Secondary Axis", "SSE.Views.ChartWizardDialog.textSeries": "Series", "SSE.Views.ChartWizardDialog.textTitle": "Insert Chart", "SSE.Views.ChartWizardDialog.textTitleChange": "Change chart type", "SSE.Views.ChartWizardDialog.textType": "Type", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Choose the chart type and axis for your data series", "SSE.Views.CreatePivotDialog.textDataRange": "Avota datu diapazons", "SSE.Views.CreatePivotDialog.textDestination": "Izvēlieties, kur novietot tabulu", "SSE.Views.CreatePivotDialog.textExist": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CreatePivotDialog.textInvalidRange": "<PERSON><PERSON><PERSON><PERSON> diapazons", "SSE.Views.CreatePivotDialog.textNew": "<PERSON><PERSON><PERSON>", "SSE.Views.CreatePivotDialog.textSelectData": "<PERSON><PERSON><PERSON> da<PERSON>", "SSE.Views.CreatePivotDialog.textTitle": "Izveidot rakurstabulu", "SSE.Views.CreatePivotDialog.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "SSE.Views.CreateSparklineDialog.textDataRange": "Avota datu diapazons", "SSE.Views.CreateSparklineDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kur novietot sīkdiagrammas", "SSE.Views.CreateSparklineDialog.textInvalidRange": "<PERSON><PERSON><PERSON><PERSON> diapazons", "SSE.Views.CreateSparklineDialog.textSelectData": "<PERSON><PERSON><PERSON> da<PERSON>", "SSE.Views.CreateSparklineDialog.textTitle": "Izveidot sīkdiagrammas", "SSE.Views.CreateSparklineDialog.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "SSE.Views.DataTab.capBtnGroup": "Grupa", "SSE.Views.DataTab.capBtnTextCustomSort": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextDataValidation": "<PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextRemDuplicates": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capBtnTextToCol": "Teksts par kolonnām", "SSE.Views.DataTab.capBtnUngroup": "Atgrupēt", "SSE.Views.DataTab.capDataExternalLinks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataTab.capDataFromText": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "SSE.Views.DataTab.capGoalSeek": "Goal Seek", "SSE.Views.DataTab.mniFromFile": "No lokālā TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "No TXT/CSV tīmekļa adreses", "SSE.Views.DataTab.mniFromXMLFile": "No lokālā XML", "SSE.Views.DataTab.textBelow": "<PERSON><PERSON><PERSON><PERSON>uma rindas zem detalizētas informācijas", "SSE.Views.DataTab.textClear": "<PERSON><PERSON><PERSON><PERSON><PERSON> struk<PERSON>", "SSE.Views.DataTab.textColumns": "Atgrupēt kolonnas", "SSE.Views.DataTab.textGroupColumns": "Grupu kolonnas", "SSE.Views.DataTab.textGroupRows": "Grupu rindas", "SSE.Views.DataTab.textRightOf": "<PERSON><PERSON><PERSON><PERSON><PERSON> kolonnas pa labi no detalizētas informācijas", "SSE.Views.DataTab.textRows": "Atgrupēt rindas", "SSE.Views.DataTab.tipCustomSort": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataTab.tipDataFromText": "<PERSON><PERSON><PERSON><PERSON> datus no faila", "SSE.Views.DataTab.tipDataValidation": "<PERSON><PERSON>", "SSE.Views.DataTab.tipExternalLinks": "<PERSON><PERSON><PERSON><PERSON> citus failus, ar kuriem ir saistīta <PERSON> i<PERSON>a", "SSE.Views.DataTab.tipGoalSeek": "Find the right input for the value you want", "SSE.Views.DataTab.tipGroup": "Šūnu grupas diapazons", "SSE.Views.DataTab.tipRemDuplicates": "Noņ<PERSON>t dublik<PERSON>ta rindas no lapas", "SSE.Views.DataTab.tipToColumns": "Atdaliet šū<PERSON> tekstu kolonn<PERSON>s", "SSE.Views.DataTab.tipUngroup": "Atgrupēt š<PERSON>", "SSE.Views.DataValidationDialog.errorFormula": "Vērtība pašlaik tiek novērtēta kā kļūda. Vai vēlaties turpināt?", "SSE.Views.DataValidationDialog.errorInvalid": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ko ievadīj<PERSON>t lauk<PERSON> \"{0}\" ir ne<PERSON><PERSON>ga.", "SSE.Views.DataValidationDialog.errorInvalidDate": "<PERSON>tum<PERSON>, ko ievadīj<PERSON>t lauk<PERSON> \"{0}\", ir ne<PERSON><PERSON><PERSON>.", "SSE.Views.DataValidationDialog.errorInvalidList": "Saraksta avotam ir jābūt norobežotam sarakstam vai atsaucei uz vienu rindu vai kolonnu.", "SSE.Views.DataValidationDialog.errorInvalidTime": "<PERSON><PERSON>, kuru ievadī<PERSON><PERSON><PERSON> la<PERSON> \"{0}\", ir ne<PERSON><PERSON><PERSON>.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "<PERSON><PERSON><PERSON> \"{1}\" ir jāb<PERSON>t lielākam vai vienādam ar lauku \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Jums jāievada vērtība gan laukā \"{0}\", gan laukā \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Jums jāievada vērtība laukā \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "Nevar at<PERSON>t j<PERSON><PERSON><PERSON><PERSON><PERSON>.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Negatīvā<PERSON> vē<PERSON><PERSON><PERSON> nevar izman<PERSON>t nosacījumos \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "Lauka<PERSON> \"{0}\" ir jāb<PERSON>t skaitliskai vērtībai, skaitliskai izteiksmei vai j<PERSON>attiecas u<PERSON>, kur<PERSON> ir skaitliska vērtība.", "SSE.Views.DataValidationDialog.strError": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.strInput": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.strSettings": "Iestatījumi", "SSE.Views.DataValidationDialog.textAlert": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAllow": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textApply": "Lietot š<PERSON> i<PERSON> visām pārējām šūnām ar tādiem pašiem iestatījumiem", "SSE.Views.DataValidationDialog.textCellSelected": "Kad ir atlas<PERSON><PERSON>, parād<PERSON>t šo ievades ziņ<PERSON>", "SSE.Views.DataValidationDialog.textCompare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar", "SSE.Views.DataValidationDialog.textData": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndDate": "Beigu datums", "SSE.Views.DataValidationDialog.textEndTime": "<PERSON><PERSON><PERSON> laiks", "SSE.Views.DataValidationDialog.textError": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textFormula": "Formula", "SSE.Views.DataValidationDialog.textIgnore": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.DataValidationDialog.textInput": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMax": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMessage": "<PERSON><PERSON>ņ<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textMin": "Minimums", "SSE.Views.DataValidationDialog.textSelectData": "<PERSON><PERSON><PERSON> da<PERSON>", "SSE.Views.DataValidationDialog.textShowDropDown": "<PERSON><PERSON><PERSON><PERSON><PERSON>u", "SSE.Views.DataValidationDialog.textShowError": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> brīdin<PERSON><PERSON><PERSON> pēc nederīgu datu ievad<PERSON>", "SSE.Views.DataValidationDialog.textShowInput": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>, kad ir atlas<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textSource": "Avots", "SSE.Views.DataValidationDialog.textStartDate": "<PERSON><PERSON><PERSON><PERSON> datums", "SSE.Views.DataValidationDialog.textStartTime": "<PERSON><PERSON><PERSON> laiku", "SSE.Views.DataValidationDialog.textStop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStyle": "Stils", "SSE.Views.DataValidationDialog.textTitle": "Nosa<PERSON>ms", "SSE.Views.DataValidationDialog.textUserEnters": "<PERSON>d lietotā<PERSON><PERSON> ievada nederīgus datus, parād<PERSON>t <PERSON>o k<PERSON> brī<PERSON>jumu", "SSE.Views.DataValidationDialog.txtAny": "Jeb<PERSON><PERSON><PERSON> vērtība", "SSE.Views.DataValidationDialog.txtBetween": "Star<PERSON>", "SSE.Views.DataValidationDialog.txtDate": "Datums", "SSE.Views.DataValidationDialog.txtDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtElTime": "Beigu datums", "SSE.Views.DataValidationDialog.txtEndDate": "Beigu datums", "SSE.Views.DataValidationDialog.txtEndTime": "<PERSON><PERSON><PERSON> laiks", "SSE.Views.DataValidationDialog.txtEqual": "vien<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtGreaterThan": "<PERSON><PERSON><PERSON><PERSON> nekā", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "Lie<PERSON>ā<PERSON> par vai vienāds ar", "SSE.Views.DataValidationDialog.txtLength": "Garums", "SSE.Views.DataValidationDialog.txtLessThan": "maz<PERSON>k nekā", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "mazāk nekā vai vienāds ar", "SSE.Views.DataValidationDialog.txtList": "Saraksts", "SSE.Views.DataValidationDialog.txtNotBetween": "ne starp", "SSE.Views.DataValidationDialog.txtNotEqual": "nav vienāds ar", "SSE.Views.DataValidationDialog.txtOther": "Citi", "SSE.Views.DataValidationDialog.txtStartDate": "<PERSON><PERSON><PERSON><PERSON> datums", "SSE.Views.DataValidationDialog.txtStartTime": "<PERSON><PERSON><PERSON> laiku", "SSE.Views.DataValidationDialog.txtTextLength": "Teksta garums", "SSE.Views.DataValidationDialog.txtTime": "Laiks", "SSE.Views.DataValidationDialog.txtWhole": "<PERSON>iss numurs", "SSE.Views.DigitalFilterDialog.capAnd": "Un", "SSE.Views.DigitalFilterDialog.capCondition1": "vien<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition10": "ne<PERSON><PERSON><PERSON> ar", "SSE.Views.DigitalFilterDialog.capCondition11": "satur", "SSE.Views.DigitalFilterDialog.capCondition12": "nesatur", "SSE.Views.DigitalFilterDialog.capCondition2": "nav vienāds ar", "SSE.Views.DigitalFilterDialog.capCondition3": "ir liel<PERSON><PERSON> nekā", "SSE.Views.DigitalFilterDialog.capCondition30": "is after", "SSE.Views.DigitalFilterDialog.capCondition4": "ir lielāks par vai vienāds ar", "SSE.Views.DigitalFilterDialog.capCondition40": "is after or equal to", "SSE.Views.DigitalFilterDialog.capCondition5": "ir maz<PERSON><PERSON> par", "SSE.Views.DigitalFilterDialog.capCondition50": "is before", "SSE.Views.DigitalFilterDialog.capCondition6": "ir mazāks par vai vienāds ar", "SSE.Views.DigitalFilterDialog.capCondition60": "is before or equal to", "SSE.Views.DigitalFilterDialog.capCondition7": "s<PERSON><PERSON> ar", "SSE.Views.DigitalFilterDialog.capCondition8": "ne<PERSON><PERSON><PERSON> ar", "SSE.Views.DigitalFilterDialog.capCondition9": "be<PERSON><PERSON> ar", "SSE.Views.DigitalFilterDialog.capOr": "Vai", "SSE.Views.DigitalFilterDialog.textNoFilter": "bez filtra", "SSE.Views.DigitalFilterDialog.textShowRows": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.DigitalFilterDialog.textUse1": "<PERSON><PERSON><PERSON>jiet ?, lai parā<PERSON><PERSON><PERSON> j<PERSON><PERSON> r<PERSON>", "SSE.Views.DigitalFilterDialog.textUse2": "<PERSON><PERSON><PERSON><PERSON><PERSON> *, lai parā<PERSON><PERSON><PERSON> jeb<PERSON> r<PERSON><PERSON><PERSON> s<PERSON>ju", "SSE.Views.DigitalFilterDialog.txtSelectDate": "<PERSON><PERSON><PERSON> da<PERSON>", "SSE.Views.DigitalFilterDialog.txtTitle": "Pielāgots filtrs", "SSE.Views.DocumentHolder.advancedEquationText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.advancedImgText": "<PERSON><PERSON><PERSON><PERSON> pap<PERSON> i<PERSON>", "SSE.Views.DocumentHolder.advancedShapeText": "Forma – papildu i<PERSON>īju<PERSON>", "SSE.Views.DocumentHolder.advancedSlicerText": "<PERSON><PERSON> griezuma papildu i<PERSON>īju<PERSON>", "SSE.Views.DocumentHolder.allLinearText": "Visi – lineāri", "SSE.Views.DocumentHolder.allProfText": "Visi – profesionāli", "SSE.Views.DocumentHolder.bottomCellText": "Līdzināt pie apakšas", "SSE.Views.DocumentHolder.bulletsText": "Aizzīmes un numerācija", "SSE.Views.DocumentHolder.centerCellText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz vidu", "SSE.Views.DocumentHolder.chartDataText": "<PERSON><PERSON><PERSON> diagrammas datus", "SSE.Views.DocumentHolder.chartText": "Diagrammas papildu i<PERSON>īju<PERSON>", "SSE.Views.DocumentHolder.chartTypeText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> diagrammas veidu", "SSE.Views.DocumentHolder.currLinearText": "Pašreizējais – lineāri", "SSE.Views.DocumentHolder.currProfText": "Pašreizējais – profesionāls", "SSE.Views.DocumentHolder.deleteColumnText": "Kolonna", "SSE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteTableText": "Tabula", "SSE.Views.DocumentHolder.direct270Text": "<PERSON><PERSON><PERSON><PERSON> te<PERSON> au<PERSON>", "SSE.Views.DocumentHolder.direct90Text": "Rot<PERSON><PERSON> te<PERSON> le<PERSON>", "SSE.Views.DocumentHolder.directHText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.directionText": "Teksta virziens", "SSE.Views.DocumentHolder.editChartText": "Rediģēt datus", "SSE.Views.DocumentHolder.editHyperlinkText": "Rediģēt hip<PERSON>aiti", "SSE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "SSE.Views.DocumentHolder.insertColumnLeftText": "Kolonna pa kreisi", "SSE.Views.DocumentHolder.insertColumnRightText": "Kolonna pa labi", "SSE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON><PERSON> augst<PERSON>k", "SSE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "<PERSON>ak<PERSON><PERSON><PERSON> lie<PERSON>", "SSE.Views.DocumentHolder.removeHyperlinkText": "<PERSON><PERSON><PERSON><PERSON> Hipersaiti", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON>u kolonnu", "SSE.Views.DocumentHolder.selectDataText": "<PERSON><PERSON><PERSON> dati", "SSE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.selectTableText": "Tabula", "SSE.Views.DocumentHolder.showEqToolbar": "Show Equation Toolbar", "SSE.Views.DocumentHolder.strDelete": "Noņemt parakstu", "SSE.Views.DocumentHolder.strDetails": "Paraksta de<PERSON>ļas", "SSE.Views.DocumentHolder.strSetup": "Paraksta uzstādīšana", "SSE.Views.DocumentHolder.strSign": "Parakstīt", "SSE.Views.DocumentHolder.textAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "Pārnest uz fonu", "SSE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeForward": "Pārnest uz priekšu", "SSE.Views.DocumentHolder.textArrangeFront": "Nest uz priekšplānu", "SSE.Views.DocumentHolder.textAverage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textBullets": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCopyCells": "Copy cells", "SSE.Views.DocumentHolder.textCount": "Skaits", "SSE.Views.DocumentHolder.textCrop": "Apgriezt", "SSE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFit": "Saskaņ<PERSON>", "SSE.Views.DocumentHolder.textEditPoints": "Rediģēt punktus", "SSE.Views.DocumentHolder.textEntriesList": "Izvēlēties no nolaižamā saraksta", "SSE.Views.DocumentHolder.textFillDays": "Fill days", "SSE.Views.DocumentHolder.textFillFormatOnly": "Fill formatting only", "SSE.Views.DocumentHolder.textFillMonths": "Fill months", "SSE.Views.DocumentHolder.textFillSeries": "Fill series", "SSE.Views.DocumentHolder.textFillWeekdays": "Fill weekdays", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Fill without formatting", "SSE.Views.DocumentHolder.textFillYears": "Fill years", "SSE.Views.DocumentHolder.textFlashFill": "Flash fill", "SSE.Views.DocumentHolder.textFlipH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textFlipV": "<PERSON><PERSON><PERSON><PERSON><PERSON> vert<PERSON>", "SSE.Views.DocumentHolder.textFreezePanes": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.DocumentHolder.textFromFile": "No faila", "SSE.Views.DocumentHolder.textFromStorage": "No glabātuves", "SSE.Views.DocumentHolder.textFromUrl": "No URL", "SSE.Views.DocumentHolder.textGrowthTrend": "Growth trend", "SSE.Views.DocumentHolder.textLinearTrend": "Linear trend", "SSE.Views.DocumentHolder.textListSettings": "Saraksta iestatījumi", "SSE.Views.DocumentHolder.textMacro": "Piešķirt makro", "SSE.Views.DocumentHolder.textMax": "Ma<PERSON>.", "SSE.Views.DocumentHolder.textMin": "<PERSON>.", "SSE.Views.DocumentHolder.textMore": "Vairāk <PERSON>", "SSE.Views.DocumentHolder.textMoreFormats": "<PERSON><PERSON><PERSON><PERSON>ātu", "SSE.Views.DocumentHolder.textNone": "Neviens", "SSE.Views.DocumentHolder.textNumbering": "Numerācija", "SSE.Views.DocumentHolder.textReplace": "Aizvietot attēlu", "SSE.Views.DocumentHolder.textResetCrop": "Reset crop", "SSE.Views.DocumentHolder.textRotate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate270": "Pagriezt par 90° pret<PERSON><PERSON> pulksteņrā<PERSON><PERSON><PERSON><PERSON><PERSON> virzienam", "SSE.Views.DocumentHolder.textRotate90": "Pagriezt par 90° pulkste<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> virzienā", "SSE.Views.DocumentHolder.textSaveAsPicture": "Saglabāt kā attēlu", "SSE.Views.DocumentHolder.textSeries": "Series", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Līdzināt pie apakšas", "SSE.Views.DocumentHolder.textShapeAlignCenter": "L<PERSON><PERSON><PERSON>āt pa centru", "SSE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa kreisi", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz vidu", "SSE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa labi", "SSE.Views.DocumentHolder.textShapeAlignTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz augšu", "SSE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "SSE.Views.DocumentHolder.textStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textSum": "Summa", "SSE.Views.DocumentHolder.textUndo": "Atsaukt", "SSE.Views.DocumentHolder.textUnFreezePanes": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.DocumentHolder.textVar": "Daž.", "SSE.Views.DocumentHolder.tipMarkersArrow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "SSE.Views.DocumentHolder.tipMarkersDash": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Pildītas rombveida a<PERSON>zī<PERSON>", "SSE.Views.DocumentHolder.tipMarkersFRound": "Pildītas apaļas aizzīmes", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Pildītas četrstūra a<PERSON>", "SSE.Views.DocumentHolder.tipMarkersHRound": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersStar": "Zvaig<PERSON><PERSON><PERSON> a<PERSON>", "SSE.Views.DocumentHolder.topCellText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz augšu", "SSE.Views.DocumentHolder.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAddComment": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAddNamedRange": "Definēt no<PERSON>ukumu", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "<PERSON><PERSON><PERSON><PERSON> secībā", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Automātiskā saskaņ<PERSON>šana ar kolonnas platumu", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Automātiska <PERSON>ņ<PERSON>šana ar rindas augstumu", "SSE.Views.DocumentHolder.txtAverage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCellFormat": "Format cells", "SSE.Views.DocumentHolder.txtClear": "Nodzēst visu", "SSE.Views.DocumentHolder.txtClearAll": "Visi", "SSE.Views.DocumentHolder.txtClearComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearHyper": "Hipersait<PERSON>", "SSE.Views.DocumentHolder.txtClearPivotField": "Clear filter from {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Att<PERSON><PERSON><PERSON><PERSON> izv<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>u grupas", "SSE.Views.DocumentHolder.txtClearSparklines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearText": "Teksts", "SSE.Views.DocumentHolder.txtCollapse": "Collapse", "SSE.Views.DocumentHolder.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON>u kolonnu", "SSE.Views.DocumentHolder.txtColumnWidth": "<PERSON>z<PERSON><PERSON><PERSON><PERSON><PERSON> kolonnas platums", "SSE.Views.DocumentHolder.txtCondFormat": "Nosacījuma <PERSON>", "SSE.Views.DocumentHolder.txtCopy": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCount": "Skaits", "SSE.Views.DocumentHolder.txtCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Pielāgotais kolonnas platums", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Pielāgotais rindas augstums", "SSE.Views.DocumentHolder.txtCustomSort": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCut": "Izgriezt", "SSE.Views.DocumentHolder.txtDateLong": "<PERSON><PERSON><PERSON> datuma formāts", "SSE.Views.DocumentHolder.txtDateShort": "Īss datums", "SSE.Views.DocumentHolder.txtDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDelField": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDescending": "<PERSON><PERSON><PERSON><PERSON><PERSON> secībā", "SSE.Views.DocumentHolder.txtDifference": "Atšķirība no", "SSE.Views.DocumentHolder.txtDistribHor": "<PERSON><PERSON><PERSON><PERSON><PERSON> pa <PERSON>", "SSE.Views.DocumentHolder.txtDistribVert": "<PERSON><PERSON><PERSON><PERSON><PERSON> pa vertik<PERSON>li", "SSE.Views.DocumentHolder.txtEditComment": "Rediģēt komentāru", "SSE.Views.DocumentHolder.txtEditObject": "Rediģēt objektu", "SSE.Views.DocumentHolder.txtExpand": "Expand", "SSE.Views.DocumentHolder.txtExpandCollapse": "Expand/Collapse", "SSE.Views.DocumentHolder.txtExpandEntire": "Expand Entire Field", "SSE.Views.DocumentHolder.txtFieldSettings": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtFilter": "Filtrs", "SSE.Views.DocumentHolder.txtFilterCellColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc <PERSON> krā<PERSON>s", "SSE.Views.DocumentHolder.txtFilterFontColor": "<PERSON><PERSON><PERSON><PERSON><PERSON> pēc fonta krā<PERSON>s", "SSE.Views.DocumentHolder.txtFilterValue": "Filtrēt pēc izvēlēt<PERSON><PERSON> vērtības", "SSE.Views.DocumentHolder.txtFormula": "<PERSON>ev<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtFraction": "Daļskaitlis", "SSE.Views.DocumentHolder.txtGeneral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtGetLink": "<PERSON>eg<PERSON>t saiti uz š<PERSON> di<PERSON>u", "SSE.Views.DocumentHolder.txtGrandTotal": "Lielās gala vērt<PERSON>bas", "SSE.Views.DocumentHolder.txtGroup": "Grupa", "SSE.Views.DocumentHolder.txtHide": "Paslēpt", "SSE.Views.DocumentHolder.txtIndex": "Indekss", "SSE.Views.DocumentHolder.txtInsert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtInsHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtInsImage": "Insert image from file", "SSE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "SSE.Views.DocumentHolder.txtLabelFilter": "Etiķetes filtrs", "SSE.Views.DocumentHolder.txtMax": "Ma<PERSON>.", "SSE.Views.DocumentHolder.txtMin": "<PERSON>.", "SSE.Views.DocumentHolder.txtMoreOptions": "More options", "SSE.Views.DocumentHolder.txtNormal": "Nav aprēķina", "SSE.Views.DocumentHolder.txtNumber": "Skaits", "SSE.Views.DocumentHolder.txtNumFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON> form<PERSON>", "SSE.Views.DocumentHolder.txtPaste": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtPercent": "% no", "SSE.Views.DocumentHolder.txtPercentage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtPercentDiff": "% difference from", "SSE.Views.DocumentHolder.txtPercentOfCol": "% of column total", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% no kopējās summas", "SSE.Views.DocumentHolder.txtPercentOfParent": "% no vecāka kopsummas", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% no vecākkolonnas kopsummas", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% no vecāka rindas kopsummas", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% darbojas kop<PERSON>", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% of row total", "SSE.Views.DocumentHolder.txtPivotSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtProduct": "Produkts", "SSE.Views.DocumentHolder.txtRankAscending": "Rangs no mazākā līdz <PERSON>m", "SSE.Views.DocumentHolder.txtRankDescending": "Rangs no lielākā līdz ma<PERSON>m", "SSE.Views.DocumentHolder.txtReapply": "<PERSON><PERSON><PERSON><PERSON> no jauna", "SSE.Views.DocumentHolder.txtRefresh": "Atsvaidzināt", "SSE.Views.DocumentHolder.txtRow": "<PERSON>isu rindu", "SSE.Views.DocumentHolder.txtRowHeight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rindas augstums", "SSE.Views.DocumentHolder.txtRunTotal": "Rezultāts šeit:", "SSE.Views.DocumentHolder.txtScientific": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSelect": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftDown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz leju", "SSE.Views.DocumentHolder.txtShiftLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa k<PERSON>i", "SSE.Views.DocumentHolder.txtShiftRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa labi", "SSE.Views.DocumentHolder.txtShiftUp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz augšu", "SSE.Views.DocumentHolder.txtShow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShowAs": "<PERSON><PERSON><PERSON><PERSON><PERSON> vē<PERSON><PERSON><PERSON> kā", "SSE.Views.DocumentHolder.txtShowComment": "<PERSON><PERSON><PERSON><PERSON><PERSON> koment<PERSON>", "SSE.Views.DocumentHolder.txtShowDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.DocumentHolder.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSortCellColor": "Izvēlēties šūnas krāsu augšā", "SSE.Views.DocumentHolder.txtSortFontColor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> fonta krāsu augšā", "SSE.Views.DocumentHolder.txtSortOption": "Citas kārtošanas opcijas", "SSE.Views.DocumentHolder.txtSparklines": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSubtotalField": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSum": "Summa", "SSE.Views.DocumentHolder.txtSummarize": "Summarize values by", "SSE.Views.DocumentHolder.txtText": "Teksts", "SSE.Views.DocumentHolder.txtTextAdvanced": "Rindkopas papildu iestatījumi", "SSE.Views.DocumentHolder.txtTime": "Laiks", "SSE.Views.DocumentHolder.txtTop10": "Pirmie 10", "SSE.Views.DocumentHolder.txtUngroup": "Atgrupēt", "SSE.Views.DocumentHolder.txtValueFieldSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON> lauka i<PERSON>", "SSE.Views.DocumentHolder.txtValueFilter": "Value filters", "SSE.Views.DocumentHolder.txtWidth": "Platums", "SSE.Views.DocumentHolder.unicodeText": "Unikods", "SSE.Views.DocumentHolder.vertAlignText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.closeButtonText": "Aizvērt", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Automatically update data from the linked sources", "SSE.Views.ExternalLinksDlg.textChange": "Change source", "SSE.Views.ExternalLinksDlg.textDelete": "Pārtraukt sa<PERSON>", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Pārtraukt visas saites", "SSE.Views.ExternalLinksDlg.textOk": "<PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.textOpen": "Open source", "SSE.Views.ExternalLinksDlg.textSource": "Avots", "SSE.Views.ExternalLinksDlg.textStatus": "Statuss", "SSE.Views.ExternalLinksDlg.textUnknown": "<PERSON><PERSON>inā<PERSON>", "SSE.Views.ExternalLinksDlg.textUpdate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Atjaunot visu", "SSE.Views.ExternalLinksDlg.textUpdating": "Notiek atjauni<PERSON>...", "SSE.Views.ExternalLinksDlg.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.strLayout": "Izklājums", "SSE.Views.FieldSettingsDialog.strSubtotals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.textNumFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON> form<PERSON>", "SSE.Views.FieldSettingsDialog.textReport": "Ziņot par formu", "SSE.Views.FieldSettingsDialog.textTitle": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtAverage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtBlank": "Ievietot tuk<PERSON> lī<PERSON> pēc katras vienības", "SSE.Views.FieldSettingsDialog.txtBottom": "<PERSON><PERSON><PERSON><PERSON><PERSON> grupas apak<PERSON>", "SSE.Views.FieldSettingsDialog.txtCompact": "Kompakts", "SSE.Views.FieldSettingsDialog.txtCount": "Skaits", "SSE.Views.FieldSettingsDialog.txtCountNums": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.FieldSettingsDialog.txtCustomName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtEmpty": "<PERSON><PERSON><PERSON><PERSON><PERSON> vienumus bez datiem", "SSE.Views.FieldSettingsDialog.txtMax": "Ma<PERSON>.", "SSE.Views.FieldSettingsDialog.txtMin": "<PERSON>.", "SSE.Views.FieldSettingsDialog.txtOutline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtProduct": "Produkts", "SSE.Views.FieldSettingsDialog.txtRepeat": "Atk<PERSON><PERSON>ot vienumu etiķetes katrā rindā", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "<PERSON><PERSON><PERSON><PERSON><PERSON> star<PERSON>", "SSE.Views.FieldSettingsDialog.txtSourceName": "Avota nosaukums:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "Summa", "SSE.Views.FieldSettingsDialog.txtSummarize": "Starpsummu funkcijas", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabulveida", "SSE.Views.FieldSettingsDialog.txtTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> grupas augšā", "SSE.Views.FieldSettingsDialog.txtVar": "Daž.", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.ariaFileMenu": "File menu", "SSE.Views.FileMenu.btnBackCaption": "<PERSON><PERSON><PERSON><PERSON> faila atra<PERSON> vietu", "SSE.Views.FileMenu.btnCloseEditor": "<PERSON><PERSON><PERSON><PERSON><PERSON> failu", "SSE.Views.FileMenu.btnCloseMenuCaption": "Aizvērt izvēlni", "SSE.Views.FileMenu.btnCreateNewCaption": "<PERSON>zve<PERSON><PERSON> jaunu", "SSE.Views.FileMenu.btnDownloadCaption": "Lejupielā<PERSON><PERSON><PERSON> kā", "SSE.Views.FileMenu.btnExitCaption": "Aizvērt", "SSE.Views.FileMenu.btnExportToPDFCaption": "Export to PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "Atv<PERSON><PERSON>", "SSE.Views.FileMenu.btnHelpCaption": "Palīdzī<PERSON>", "SSE.Views.FileMenu.btnHistoryCaption": "Versiju vēsture", "SSE.Views.FileMenu.btnInfoCaption": "Informācija par rēķintabulu", "SSE.Views.FileMenu.btnPrintCaption": "Printēt", "SSE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON><PERSON><PERSON> pēd<PERSON>", "SSE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnReturnCaption": "Atpakaļ uz izklājlapu", "SSE.Views.FileMenu.btnRightsCaption": "Piek<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveAsCaption": "Sag<PERSON><PERSON><PERSON>t kā...", "SSE.Views.FileMenu.btnSaveCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Saglabāt kopiju kā", "SSE.Views.FileMenu.btnSettingsCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "SSE.Views.FileMenu.btnToEditCaption": "Rediģēt dokumentu", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "<PERSON><PERSON>ša <PERSON>lājlap<PERSON>", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "<PERSON>zve<PERSON><PERSON> jaunu", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Pievienot autoru", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Programma", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autors", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Izveidots", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Pēdējo reizi modificēja", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "<PERSON><PERSON><PERSON><PERSON><PERSON> reizi modificēts", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Īpašnieks", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Novietojums", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON> kuriem i<PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Informācija par rēķintabulu", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tagi", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Nosa<PERSON>ms", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Piek<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON> kuriem i<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Ko<PERSON>īgā<PERSON> rediģēšanas režīms", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Izmantot 1904. gada datumu sistēmu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Vārdnīcas valoda", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Enable iterative calculation", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Formulas valoda", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Piemērs: SUM; MIN.; MAKS.; SKAITS", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Show function tooltip", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Show horizontal scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Ignorēt vārdus ar LIELAJIEM BURTIEM", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "<PERSON><PERSON><PERSON><PERSON><PERSON> vārdus ar cipariem", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Maximum change", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Maximum iterations", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON><PERSON><PERSON> opciju pogu, kad saturs ir ielīm<PERSON>ts", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "R1C1 atsauces stils", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Reģionālie iestatījumi", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Piemērs: ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTL interface", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "<PERSON><PERSON><PERSON><PERSON><PERSON> koment<PERSON><PERSON> lap<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "R<PERSON><PERSON><PERSON>t citu lietotāju veiktās i<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "<PERSON><PERSON><PERSON><PERSON><PERSON> at<PERSON> k<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Snapped to the grid while scrolling", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Tab style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Interfeisa tēma", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Mērvienība", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "<PERSON><PERSON><PERSON><PERSON>, pamatojoties uz reģionālajiem iestatījumiem", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Show vertical scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Noklusēju<PERSON> tā<PERSON>iņas vērtība", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Ik pēc 10 minūtēm", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Ik pēc 30 minūtēm", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Ik pēc 5 minūtēm", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "<PERSON><PERSON> stundu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Automātiskā <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Automātisk<PERSON> sagla<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "Fill", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Saglabā vidējā līmeņa versijas", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "Line", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Atsauces stils", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Automā<PERSON><PERSON><PERSON><PERSON> opcijas...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Baltkrievu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Noklusējuma kešatmiņas režīms", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Aprēķina", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centimetrs", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Sad<PERSON>bī<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Čeh<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Customize quick access", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Rediģēšana un saglabāšana", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Grieķu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "<PERSON><PERSON><PERSON><PERSON> koprediģēšana. Visas izmaiņas tiek saglabātas automātiski", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonēziešu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Col<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "<PERSON><PERSON>ļ<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Last used", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "kā OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Dzimtais", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Norvēģu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Portug<PERSON><PERSON><PERSON> (Brazīlija)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "Portug<PERSON><PERSON><PERSON> (Portugāle)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON> pogu redaktora galvenē", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "Dokuments tiks drukāts ar pēdējo atlasīto vai noklusējuma printeri", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Reģions", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "K<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Iespējo<PERSON> visu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Iespējot visus makro bez paziņojuma", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Turn on screen reader support", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Default sheet direction", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "This setting will affect only the new sheets", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Left-to-right", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Right-to-left", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Slovā<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Atspējot visu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Atspējot visus makro bez paziņojuma", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "<PERSON><PERSON><PERSON><PERSON><PERSON> \"Saglabā<PERSON>\" tausti<PERSON><PERSON>, lai sinhronizētu sevis un citu veiktās izmaiņas", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Zviedru", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Use toolbar color as tabs background", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Turku", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Izmantojiet taustiņu Alt, lai pārvietotos lietotāja interfeisā, izman<PERSON>jot tastatūru", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "<PERSON>zmantojiet op<PERSON>ju <PERSON>, lai pārvie<PERSON>tos lietotāja interfeisā, i<PERSON><PERSON><PERSON><PERSON> ta<PERSON>t<PERSON>ru", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vjetnamiešu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Atspējot visus makro ar p<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "kā Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Darbvieta", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "Ķīniešu", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "<PERSON>z<PERSON><PERSON><PERSON><PERSON> rēķintabulu", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "<PERSON><PERSON> <PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Izklājlapai ir pievienoti derīgi paraksti.<br><PERSON>z<PERSON><PERSON><PERSON><PERSON><PERSON>a ir aizsargāta pret rediģēšanu.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Nodrošiniet izklājlapas integritāti, pievienojot<br><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Rediģēt dokumentu", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Rediģēšana no tabulas noņems parakstus.<br>Vai tiešām vēlaties turpināt?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Šo rēķintabulu aizsargā parole", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Šifrēt šo iz<PERSON>ā<PERSON> ar paroli", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "<PERSON><PERSON> i<PERSON>lapa ir j<PERSON>.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Izklājlapai ir pievienoti derīgi paraksti. Izklājlapu nevar rediģēt.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Daži rēķintabulas digitālie paraksti nav derīgi vai tos nevar pārbaud<PERSON>t. Izklājlapu nevar rediģēt.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "<PERSON><PERSON><PERSON><PERSON><PERSON> para<PERSON>", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Lejupielā<PERSON><PERSON><PERSON> kā", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Saglabāt kopiju kā", "SSE.Views.FillSeriesDialog.textAuto": "AutoFill", "SSE.Views.FillSeriesDialog.textCols": "Columns", "SSE.Views.FillSeriesDialog.textDate": "Date", "SSE.Views.FillSeriesDialog.textDateUnit": "Date unit", "SSE.Views.FillSeriesDialog.textDay": "Day", "SSE.Views.FillSeriesDialog.textGrowth": "Growth", "SSE.Views.FillSeriesDialog.textLinear": "Linear", "SSE.Views.FillSeriesDialog.textMonth": "Month", "SSE.Views.FillSeriesDialog.textRows": "Rows", "SSE.Views.FillSeriesDialog.textSeries": "Series in", "SSE.Views.FillSeriesDialog.textStep": "Step value", "SSE.Views.FillSeriesDialog.textStop": "Stop value", "SSE.Views.FillSeriesDialog.textTitle": "Series", "SSE.Views.FillSeriesDialog.textTrend": "Trend", "SSE.Views.FillSeriesDialog.textType": "Type", "SSE.Views.FillSeriesDialog.textWeek": "Weekday", "SSE.Views.FillSeriesDialog.textYear": "Year", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FormatRulesEditDlg.fillColor": "Fona krāsa", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 krāsu skala", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 krāsu skala", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Visas apmales", "SSE.Views.FormatRulesEditDlg.textAppearance": "<PERSON><PERSON>s <PERSON>", "SSE.Views.FormatRulesEditDlg.textApply": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automātisks", "SSE.Views.FormatRulesEditDlg.textAxis": "<PERSON>s", "SSE.Views.FormatRulesEditDlg.textBarDirection": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBold": "Treknraksts", "SSE.Views.FormatRulesEditDlg.textBorder": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersColor": "Apmales krāsa", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Robežas stils", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Nevar pie<PERSON><PERSON>.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "<PERSON><PERSON><PERSON> viduspunk<PERSON>", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textClear": "Nodzēst visu", "SSE.Views.FormatRulesEditDlg.textColor": "Teks<PERSON>", "SSE.Views.FormatRulesEditDlg.textContext": "Konteksts", "SSE.Views.FormatRulesEditDlg.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "<PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON> robe<PERSON>a", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "<PERSON><PERSON><PERSON><PERSON> robe<PERSON>a uz augšu", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Ievadiet derīgu formulu.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "<PERSON><PERSON><PERSON> ieva<PERSON>ās formulas rezultāts nav skaitlis, datums, laiks vai virkne.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Ievadiet vērtību.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "<PERSON><PERSON><PERSON> ievadītā vērtība nav derīgs cipars, datums, laiks vai virkne.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {0} ir jāb<PERSON>t liel<PERSON>kai par vērtību {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Ievadiet skaitli no {0} līdz {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormula": "Formula", "SSE.Views.FormatRulesEditDlg.textGradient": "Gradients", "SSE.Views.FormatRulesEditDlg.textIconLabel": "kad {0} {1} un", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "kad {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "kad vērt<PERSON>ba ir", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Viena vai vairāki ikonu datu diapazoni pārklājas.<br>Pielāgojiet ikonu datu diapazona vērt<PERSON><PERSON>, lai diapazoni nepārklātos.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "<PERSON><PERSON><PERSON> stils", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textInvalid": "Nederīgs datu diapazons", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "KĻŪDA! Nederīgs šūnu diapazons", "SSE.Views.FormatRulesEditDlg.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textItem": "Pozīcija", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "No kreisās uz labo", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLongBar": "vis<PERSON><PERSON><PERSON><PERSON> j<PERSON>la", "SSE.Views.FormatRulesEditDlg.textMaximum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMidpoint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimums", "SSE.Views.FormatRulesEditDlg.textMinpoint": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNegative": "Negatīvs", "SSE.Views.FormatRulesEditDlg.textNewColor": "<PERSON><PERSON><PERSON> jau<PERSON>", "SSE.Views.FormatRulesEditDlg.textNoBorders": "Nav a<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNone": "Neviens", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Viena vai vairākas no norādītajām vērtībām nav derīgas procentuālās vērtī<PERSON>.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {0} vērtība nav derīga procentuālā vērtība.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Viena vai vairākas no norādītajām vērtībām nav derīga procentile.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {0} vē<PERSON><PERSON>ba nav derīga procentile.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPercent": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPercentile": "Procentile", "SSE.Views.FormatRulesEditDlg.textPosition": "Pozīcija", "SSE.Views.FormatRulesEditDlg.textPositive": "Pozīcija.", "SSE.Views.FormatRulesEditDlg.textPresets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPreview": "Priekšskatījums", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Nevar <PERSON><PERSON><PERSON> relatīvas atsauces kr<PERSON><PERSON> skalu, datu joslu un ikonu kopu nosacītās formatēšanas kritērijos.", "SSE.Views.FormatRulesEditDlg.textReverse": "Apgriezt ikonu secību", "SSE.Views.FormatRulesEditDlg.textRight2Left": "No labās uz kreiso", "SSE.Views.FormatRulesEditDlg.textRightBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRule": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSameAs": "<PERSON><PERSON><PERSON> pats kā pozitīvs", "SSE.Views.FormatRulesEditDlg.textSelectData": "<PERSON><PERSON><PERSON> da<PERSON>", "SSE.Views.FormatRulesEditDlg.textShortBar": "vis<PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>la", "SSE.Views.FormatRulesEditDlg.textShowBar": "<PERSON><PERSON><PERSON><PERSON><PERSON> tikai j<PERSON>lu", "SSE.Views.FormatRulesEditDlg.textShowIcon": "<PERSON><PERSON><PERSON><PERSON><PERSON> tikai ikonu", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Šo veidu atsauci nevar izmantot nosacījumformatēšanas formulā.<br>Mainiet atsauci uz vienu šūnu vai izmantojiet atsauci ar darb<PERSON> funkciju, piemēram, =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Ciets", "SSE.Views.FormatRulesEditDlg.textStrikeout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSubscript": "Apakšraksts", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Augšraksts", "SSE.Views.FormatRulesEditDlg.textTopBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textUnderline": "Pasvītrot", "SSE.Views.FormatRulesEditDlg.tipBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON> form<PERSON>", "SSE.Views.FormatRulesEditDlg.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDate": "Datums", "SSE.Views.FormatRulesEditDlg.txtDateLong": "<PERSON><PERSON><PERSON> datuma formāts", "SSE.Views.FormatRulesEditDlg.txtDateShort": "Īss datums", "SSE.Views.FormatRulesEditDlg.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "SSE.Views.FormatRulesEditDlg.txtFraction": "Daļskaitlis", "SSE.Views.FormatRulesEditDlg.txtGeneral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "Bez ikonas", "SSE.Views.FormatRulesEditDlg.txtNumber": "Skaits", "SSE.Views.FormatRulesEditDlg.txtPercentage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtScientific": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtText": "Teksts", "SSE.Views.FormatRulesEditDlg.txtTime": "Laiks", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Rediģēt formatē<PERSON><PERSON> kārtulu", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "<PERSON><PERSON><PERSON> <PERSON> k<PERSON>", "SSE.Views.FormatRulesManagerDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.lockText": "Bloķēta", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 std dev virs vidējā", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 std dev zem vidējā", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 std dev virs vidējā", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 std dev zem vidējā", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 std dev virs vidējā", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 std dev zem vidējā", "SSE.Views.FormatRulesManagerDlg.textAbove": "<PERSON><PERSON> vidējā", "SSE.Views.FormatRulesManagerDlg.textApply": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "<PERSON><PERSON><PERSON> vērtība sākas ar", "SSE.Views.FormatRulesManagerDlg.textBelow": "<PERSON><PERSON> vidēj<PERSON>", "SSE.Views.FormatRulesManagerDlg.textBetween": "ir starp {0} un {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Šūnas vērtība", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Gradienta kr<PERSON><PERSON> skala", "SSE.Views.FormatRulesManagerDlg.textContains": "<PERSON><PERSON><PERSON> vērt<PERSON><PERSON> satur", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "<PERSON><PERSON><PERSON><PERSON> ir tukša vērtība", "SSE.Views.FormatRulesManagerDlg.textContainsError": "<PERSON><PERSON><PERSON><PERSON> ir k<PERSON>", "SSE.Views.FormatRulesManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textDown": "Pārvietot kārtulu uz leju", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEdit": "Rediģēt", "SSE.Views.FormatRulesManagerDlg.textEnds": "Šūnas vērtība beidzas ar", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Vienāds ar vidējo vai virs vidējā", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Vienāds ar vidējo vai zem vidējā", "SSE.Views.FormatRulesManagerDlg.textFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Ikonu komplekts", "SSE.Views.FormatRulesManagerDlg.textNew": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "nav starp {0} un {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Šūnas vērtība nesatur", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Šūnā nav tukšas vērt<PERSON>", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Šūnā nav kļūdas", "SSE.Views.FormatRulesManagerDlg.textRules": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textScope": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> note<PERSON>", "SSE.Views.FormatRulesManagerDlg.textSelectData": "<PERSON><PERSON><PERSON> da<PERSON>", "SSE.Views.FormatRulesManagerDlg.textSelection": "Pašreizējā atlase", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textThisTable": "Šī tabula", "SSE.Views.FormatRulesManagerDlg.textUnique": "Unik<PERSON><PERSON> v<PERSON>", "SSE.Views.FormatRulesManagerDlg.textUp": "Pārvietot kārtulu uz augšu", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Šo elementu lieto cits lietotājs.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Nosacījuma <PERSON>", "SSE.Views.FormatSettingsDialog.textCategory": "Kategorija", "SSE.Views.FormatSettingsDialog.textDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textLinked": "Saistīts ar avotu", "SSE.Views.FormatSettingsDialog.textSeparator": "<PERSON>zmantot tū<PERSON> at<PERSON>", "SSE.Views.FormatSettingsDialog.textSymbols": "Simboli", "SSE.Views.FormatSettingsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> form<PERSON>", "SSE.Views.FormatSettingsDialog.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtAs10": "<PERSON><PERSON><PERSON> (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "<PERSON><PERSON><PERSON><PERSON> (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Uzmanīgi ievadiet pielāgoto skaitļu formātu. Izklājlapu redaktors ne<PERSON><PERSON><PERSON>, vai pielāgotajos formātos nav kļūdu, kas var ietekmēt xlsx failu.", "SSE.Views.FormatSettingsDialog.txtDate": "Datums", "SSE.Views.FormatSettingsDialog.txtFraction": "Daļskaitlis", "SSE.Views.FormatSettingsDialog.txtGeneral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNone": "Neviens", "SSE.Views.FormatSettingsDialog.txtNumber": "Skaits", "SSE.Views.FormatSettingsDialog.txtPercentage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtSample": "Paraugs:", "SSE.Views.FormatSettingsDialog.txtScientific": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtText": "Teksts", "SSE.Views.FormatSettingsDialog.txtTime": "Laiks", "SSE.Views.FormatSettingsDialog.txtUpto1": "Līdz vienam ciparam (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "<PERSON><PERSON><PERSON>z diviem cipariem (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Līdz trim cipariem (131/135)", "SSE.Views.FormulaDialog.sDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.textGroupDescription": "Izvēlieties funkcijas grupu", "SSE.Views.FormulaDialog.textListDescription": "Izvēlieties funkciju", "SSE.Views.FormulaDialog.txtRecommended": "Ieteicams", "SSE.Views.FormulaDialog.txtSearch": "Meklēt", "SSE.Views.FormulaDialog.txtTitle": "<PERSON>ev<PERSON><PERSON>", "SSE.Views.FormulaTab.capBtnRemoveArr": "Remove Arrows", "SSE.Views.FormulaTab.capBtnTraceDep": "Trace Dependents", "SSE.Views.FormulaTab.capBtnTracePrec": "Trace Precedents", "SSE.Views.FormulaTab.textAutomatic": "Automātisks", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Aprēķināt pa<PERSON><PERSON><PERSON><PERSON><PERSON> lapu", "SSE.Views.FormulaTab.textCalculateWorkbook": "Aprēķināt darbgrāmatu", "SSE.Views.FormulaTab.textManual": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculate": "Aprēķināt", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Aprēķināt visu darbgrāmatu", "SSE.Views.FormulaTab.tipRemoveArr": "Remove the arrows drawn by Trace Precedents or Trace Dependents", "SSE.Views.FormulaTab.tipShowFormulas": "Display the formula in each cell instead of the resulting value", "SSE.Views.FormulaTab.tipTraceDep": "Show arrows that indicate which cells are affected by the value of the selected cell", "SSE.Views.FormulaTab.tipTracePrec": "Show arrows that indicate which cells affect the value of the selected cell", "SSE.Views.FormulaTab.tipWatch": "<PERSON><PERSON><PERSON> s<PERSON> loga sarak<PERSON>m", "SSE.Views.FormulaTab.txtAdditional": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtAutosum": "Automātiskā summē<PERSON>na", "SSE.Views.FormulaTab.txtAutosumTip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtCalculation": "Aprēķins", "SSE.Views.FormulaTab.txtFormula": "Funkcija", "SSE.Views.FormulaTab.txtFormulaTip": "<PERSON>ev<PERSON><PERSON>", "SSE.Views.FormulaTab.txtMore": "Vairāk <PERSON>", "SSE.Views.FormulaTab.txtRecent": "Nesen lietots", "SSE.Views.FormulaTab.txtRemDep": "Remove Dependents Arrows", "SSE.Views.FormulaTab.txtRemPrec": "Remove Precedents Arrows", "SSE.Views.FormulaTab.txtShowFormulas": "Show Formulas", "SSE.Views.FormulaTab.txtWatch": "Uzraudzības logs", "SSE.Views.FormulaWizard.textAny": "j<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textArgument": "Arguments", "SSE.Views.FormulaWizard.textFunction": "Funkcija", "SSE.Views.FormulaWizard.textFunctionRes": "Funkcijas rezultāts", "SSE.Views.FormulaWizard.textHelp": "Palīdzība par šo <PERSON>ju", "SSE.Views.FormulaWizard.textLogical": "loģiskie", "SSE.Views.FormulaWizard.textNoArgs": "Šai funkcijai nav argumentu", "SSE.Views.FormulaWizard.textNoArgsDesc": "this argument has no description", "SSE.Views.FormulaWizard.textNumber": "numurs", "SSE.Views.FormulaWizard.textReadMore": "Read more", "SSE.Views.FormulaWizard.textRef": "atsauce", "SSE.Views.FormulaWizard.textText": "teksts", "SSE.Views.FormulaWizard.textTitle": "Funkciju argumenti", "SSE.Views.FormulaWizard.textValue": "Formulas rezultāts", "SSE.Views.GoalSeekDlg.textChangingCell": "By changing cell", "SSE.Views.GoalSeekDlg.textDataRangeError": "The formula is missing a range", "SSE.Views.GoalSeekDlg.textMustContainFormula": "The cell must contain a formula", "SSE.Views.GoalSeekDlg.textMustContainValue": "Cell must contain a value", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Formula in cell must result in a number", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Reference must be to a single cell", "SSE.Views.GoalSeekDlg.textSelectData": "Select data", "SSE.Views.GoalSeekDlg.textSetCell": "Set cell", "SSE.Views.GoalSeekDlg.textTitle": "Goal seek", "SSE.Views.GoalSeekDlg.textToValue": "To value", "SSE.Views.GoalSeekDlg.txtEmpty": "This field is required", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.GoalSeekStatusDlg.textContinue": "Continue", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Current value:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Goal seeking with cell {0} found a solution.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Goal seeking with cell {0} may not have found a solution.", "SSE.Views.GoalSeekStatusDlg.textPause": "Pause", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Goal seeking with cell {0} on iteration #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "Step", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Target value:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Goal seek status", "SSE.Views.HeaderFooterDialog.textAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar lapas piemal<PERSON>m", "SSE.Views.HeaderFooterDialog.textAll": "Visas lapas", "SSE.Views.HeaderFooterDialog.textBold": "Treknraksts", "SSE.Views.HeaderFooterDialog.textCenter": "Centrā", "SSE.Views.HeaderFooterDialog.textColor": "Teks<PERSON>", "SSE.Views.HeaderFooterDialog.textDate": "Datums", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Atšķirīga pirmā lappuse", "SSE.Views.HeaderFooterDialog.textDiffOdd": "<PERSON><PERSON><PERSON><PERSON> nepāra un pāra lapas", "SSE.Views.HeaderFooterDialog.textEven": "<PERSON><PERSON><PERSON><PERSON><PERSON> lapa", "SSE.Views.HeaderFooterDialog.textFileName": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textFirst": "<PERSON><PERSON><PERSON> lapa", "SSE.Views.HeaderFooterDialog.textFooter": "Zemteksta piezī<PERSON>", "SSE.Views.HeaderFooterDialog.textHeader": "G<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textImage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textInsert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textMaxError": "Ievadītā teksta virkne ir pārāk gara. Samaziniet izmantoto r<PERSON> s<PERSON>.", "SSE.Views.HeaderFooterDialog.textNewColor": "<PERSON><PERSON><PERSON> jau<PERSON>", "SSE.Views.HeaderFooterDialog.textOdd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textPageCount": "Lapas numurs", "SSE.Views.HeaderFooterDialog.textPageNum": "Lapas numuri", "SSE.Views.HeaderFooterDialog.textPresets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textRight": "Labais", "SSE.Views.HeaderFooterDialog.textScale": "Mērogot ar dokumentu", "SSE.Views.HeaderFooterDialog.textSheet": "Lapas nosaukums", "SSE.Views.HeaderFooterDialog.textStrikeout": "Pārsvītrots", "SSE.Views.HeaderFooterDialog.textSubscript": "Apakšraksts", "SSE.Views.HeaderFooterDialog.textSuperscript": "Augšraksts", "SSE.Views.HeaderFooterDialog.textTime": "Laiks", "SSE.Views.HeaderFooterDialog.textTitle": "G<PERSON><PERSON><PERSON>/kājenes i<PERSON>ī<PERSON>", "SSE.Views.HeaderFooterDialog.textUnderline": "Pasvītrot", "SSE.Views.HeaderFooterDialog.tipFontName": "Fonts", "SSE.Views.HeaderFooterDialog.tipFontSize": "Fonta izmērs", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "<PERSON><PERSON><PERSON><PERSON> ar", "SSE.Views.HyperlinkSettingsDialog.strRange": "Diapazons", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Atlasītais diapazons", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Ievadiet parakstu šeit", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Ievadiet saiti šeit", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Ievadiet rīka padomu <PERSON>eit", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Iek<PERSON><PERSON>jo datu diapazons", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "KĻŪDA! Nederīgs šūnu diapazons", "SSE.Views.HyperlinkSettingsDialog.textNames": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "<PERSON><PERSON><PERSON> da<PERSON>", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Lapas", "SSE.Views.HyperlinkSettingsDialog.textTipText": "<PERSON><PERSON><PERSON><PERSON><PERSON> padomu teksts", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Hipersait<PERSON>", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "<PERSON><PERSON> la<PERSON> ir jāb<PERSON>t vietrāža URL formātā \"http://www.example.com\"", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Lauks ir ierobežots līdz 2083 rakstzīmēm", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "SSE.Views.ImageSettings.strTransparency": "Opacity", "SSE.Views.ImageSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pap<PERSON>du i<PERSON>", "SSE.Views.ImageSettings.textCrop": "Apgriezt", "SSE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFit": "Saskaņ<PERSON>", "SSE.Views.ImageSettings.textCropToShape": "Apgriezt pēc formas", "SSE.Views.ImageSettings.textEdit": "Rediģēt", "SSE.Views.ImageSettings.textEditObject": "Rediģēt objektu", "SSE.Views.ImageSettings.textFlip": "Uzsist", "SSE.Views.ImageSettings.textFromFile": "No faila", "SSE.Views.ImageSettings.textFromStorage": "No glabātuves", "SSE.Views.ImageSettings.textFromUrl": "No URL", "SSE.Views.ImageSettings.textHeight": "Augstums", "SSE.Views.ImageSettings.textHint270": "Pagriezt par 90° pret<PERSON><PERSON> pulksteņrā<PERSON><PERSON><PERSON><PERSON><PERSON> virzienam", "SSE.Views.ImageSettings.textHint90": "Pagriezt par 90° pulkste<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> virzienā", "SSE.Views.ImageSettings.textHintFlipH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textHintFlipV": "<PERSON><PERSON><PERSON><PERSON><PERSON> vert<PERSON>", "SSE.Views.ImageSettings.textInsert": "Aizvietot attēlu", "SSE.Views.ImageSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textOriginalSize": "<PERSON>ak<PERSON><PERSON><PERSON> lie<PERSON>", "SSE.Views.ImageSettings.textRecentlyUsed": "Nesen lietots", "SSE.Views.ImageSettings.textResetCrop": "Reset crop", "SSE.Views.ImageSettings.textRotate90": "Pagriezt par 90°", "SSE.Views.ImageSettings.textRotation": "Rotā<PERSON>ja", "SSE.Views.ImageSettings.textSize": "Izmērs", "SSE.Views.ImageSettings.textWidth": "Platums", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Nepārvietot un neveidot iz<PERSON>ērus ar šū<PERSON>m", "SSE.Views.ImageSettingsAdvanced.textAlt": "Alternatīvs teksts", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Vizuālās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, fig<PERSON><PERSON><PERSON>, diagramm<PERSON> vai tabulā.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Nosa<PERSON>ms", "SSE.Views.ImageSettingsAdvanced.textAngle": "Leņķis", "SSE.Views.ImageSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textOneCell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bet neveidot izmērus ar <PERSON>m", "SSE.Views.ImageSettingsAdvanced.textRotation": "Rotā<PERSON>ja", "SSE.Views.ImageSettingsAdvanced.textSnap": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textTitle": "Attēls - <PERSON><PERSON><PERSON><PERSON>ī<PERSON>", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Pārvietot un izmērīt ar šūnām", "SSE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImportFromXmlDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kur novietot datus", "SSE.Views.ImportFromXmlDialog.textExist": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "<PERSON><PERSON><PERSON><PERSON> diapazons", "SSE.Views.ImportFromXmlDialog.textNew": "<PERSON><PERSON><PERSON>", "SSE.Views.ImportFromXmlDialog.textSelectData": "<PERSON><PERSON><PERSON> da<PERSON>", "SSE.Views.ImportFromXmlDialog.textTitle": "I<PERSON>rt<PERSON><PERSON> datus", "SSE.Views.ImportFromXmlDialog.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "SSE.Views.LeftMenu.ariaLeftMenu": "Left menu", "SSE.Views.LeftMenu.tipAbout": "Par", "SSE.Views.LeftMenu.tipChat": "Čats", "SSE.Views.LeftMenu.tipComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipFile": "Fails", "SSE.Views.LeftMenu.tipPlugins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSearch": "Meklēt", "SSE.Views.LeftMenu.tipSpellcheck": "Notiek pareizrakstības p<PERSON>aude", "SSE.Views.LeftMenu.tipSupport": "Atsauksmes un atbalsts", "SSE.Views.LeftMenu.txtDeveloper": "IZSTRĀDĀTĀJA REŽĪMS", "SSE.Views.LeftMenu.txtEditor": "Izklājlapu redaktors", "SSE.Views.LeftMenu.txtLimit": "Ierobežot <PERSON>ļuvi", "SSE.Views.LeftMenu.txtTrial": "IZMĒĢINĀJUMA REŽĪMS", "SSE.Views.LeftMenu.txtTrialDev": "Izmēģinājuma izstrā<PERSON><PERSON><PERSON><PERSON><PERSON> re<PERSON>", "SSE.Views.MacroDialog.textMacro": "<PERSON><PERSON><PERSON>", "SSE.Views.MacroDialog.textTitle": "Piešķirt makro", "SSE.Views.MainSettingsPrint.okButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLandscape": "Ainava", "SSE.Views.MainSettingsPrint.strLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strMargins": "Piemales", "SSE.Views.MainSettingsPrint.strPortrait": "Portrets", "SSE.Views.MainSettingsPrint.strPrint": "Printēt", "SSE.Views.MainSettingsPrint.strPrintTitles": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strRight": "Labais", "SSE.Views.MainSettingsPrint.strTop": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textActualSize": "<PERSON>ak<PERSON><PERSON><PERSON> lie<PERSON>", "SSE.Views.MainSettingsPrint.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textCustomOptions": "Pielāgot<PERSON> opcijas", "SSE.Views.MainSettingsPrint.textFitCols": "Salikt visas kolonnas vienā lap<PERSON>", "SSE.Views.MainSettingsPrint.textFitPage": "<PERSON><PERSON><PERSON> lapu vienā lap<PERSON>", "SSE.Views.MainSettingsPrint.textFitRows": "Salikt visas rindas vien<PERSON> lap<PERSON>", "SSE.Views.MainSettingsPrint.textPageOrientation": "Lapas orientācija", "SSE.Views.MainSettingsPrint.textPageScaling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPageSize": "Lapas izmērs", "SSE.Views.MainSettingsPrint.textPrintGrid": "Druk<PERSON>t režģa līnijas", "SSE.Views.MainSettingsPrint.textPrintHeadings": "<PERSON><PERSON><PERSON><PERSON> rindas un kolonas galvenes", "SSE.Views.MainSettingsPrint.textRepeat": "Atkārtot...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Atk<PERSON>rtot kolonnas kreis<PERSON> pusē", "SSE.Views.MainSettingsPrint.textRepeatTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> rinda<PERSON> au<PERSON>", "SSE.Views.MainSettingsPrint.textSettings": "Iestatījumi", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "<PERSON><PERSON><PERSON><PERSON> no<PERSON><PERSON> di<PERSON><PERSON>us nevar rediģēt, bet jaunus nevar<br><PERSON><PERSON><PERSON><PERSON><PERSON>, jo daži no tiem tiek rediģēti.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Definēts nosaukums", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Darba burtnīca", "SSE.Views.NamedRangeEditDlg.textDataRange": "Datu diapazons", "SSE.Views.NamedRangeEditDlg.textExistName": "KĻŪDA! Jau pastāv diapazons ar šādu vārdu", "SSE.Views.NamedRangeEditDlg.textInvalidName": "<PERSON><PERSON><PERSON><PERSON><PERSON> jās<PERSON>kas ar burtu vai pasvītru, un tajā nedrīkst būt nederīgas rakstz<PERSON>.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "KĻŪDA! Nederīgs šūnas diapazons", "SSE.Views.NamedRangeEditDlg.textIsLocked": "KĻŪDA! Šo elementu lieto cits lietotājs.", "SSE.Views.NamedRangeEditDlg.textName": "Nosa<PERSON>ms", "SSE.Views.NamedRangeEditDlg.textReservedName": "<PERSON><PERSON><PERSON><PERSON>, kuru mēģināt i<PERSON>, jau ir norād<PERSON>ts šūnu formulās. Izmantojiet kādu citu nosaukumu.", "SSE.Views.NamedRangeEditDlg.textScope": "Tvērums", "SSE.Views.NamedRangeEditDlg.textSelectData": "<PERSON><PERSON><PERSON> da<PERSON>", "SSE.Views.NamedRangeEditDlg.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Rediģēt nosaukumu", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangePasteDlg.textNames": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NamedRangePasteDlg.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vā<PERSON>u", "SSE.Views.NameManagerDlg.closeButtonText": "Aizvērt", "SSE.Views.NameManagerDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.lockText": "Bloķēta", "SSE.Views.NameManagerDlg.textDataRange": "Datu diapazons", "SSE.Views.NameManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEdit": "Rediģēt", "SSE.Views.NameManagerDlg.textEmpty": "Vēl nav izveidots neviens nosaukts diapazons.<br>Izveidojiet vismaz vienu nosauktu diapazonu, un tas parādīsies šajā laukā.", "SSE.Views.NameManagerDlg.textFilter": "Filtrs", "SSE.Views.NameManagerDlg.textFilterAll": "Visi", "SSE.Views.NameManagerDlg.textFilterDefNames": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterSheet": "<PERSON><PERSON><PERSON> i<PERSON><PERSON> no<PERSON>", "SSE.Views.NameManagerDlg.textFilterTableNames": "Ta<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Darbg<PERSON><PERSON><PERSON><PERSON> i<PERSON> no<PERSON>", "SSE.Views.NameManagerDlg.textNew": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textnoNames": "Nevar<PERSON>ja atrast nevienu <PERSON>, kas atbilst jū<PERSON> filtram.", "SSE.Views.NameManagerDlg.textRanges": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textScope": "Tvērums", "SSE.Views.NameManagerDlg.textWorkbook": "Darba burtnīca", "SSE.Views.NameManagerDlg.tipIsLocked": "Šo elementu lieto cits lietotājs.", "SSE.Views.NameManagerDlg.txtTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.warnDelete": "Vai tiešām vēlaties dzēst vārdu {0}?", "SSE.Views.PageMarginsDialog.textBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textCenter": "Center on page", "SSE.Views.PageMarginsDialog.textHor": "Horizontally", "SSE.Views.PageMarginsDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textRight": "Labais", "SSE.Views.PageMarginsDialog.textTitle": "Piemales", "SSE.Views.PageMarginsDialog.textTop": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textVert": "Vertically", "SSE.Views.PageMarginsDialog.textWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.warnCheckMargings": "Piemales ir nepareizas", "SSE.Views.ParagraphSettings.strLineHeight": "Rindstarpas", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Atstatums", "SSE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingBefore": "Pirms", "SSE.Views.ParagraphSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pap<PERSON>du i<PERSON>", "SSE.Views.ParagraphSettings.textAt": "Uz", "SSE.Views.ParagraphSettings.textAtLeast": "<PERSON><PERSON>az", "SSE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textExact": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.txtAutoText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "<PERSON><PERSON><PERSON> la<PERSON> parād<PERSON> norādītās cilnes", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Visi lielie burti", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Atkāpes", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Rindstarpas", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Labais", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Pirms", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Īpašie", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Fonts", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Atkāpes un atstarpes", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON> b<PERSON>i", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Atstarpe", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Pārsvītrots", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Apakšraksts", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Augšraksts", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Cilnes", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Rakstzīmju atstarpes", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Noklusējuma cilne", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Efekti", "SSE.Views.ParagraphSettingsAdvanced.textExact": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "<PERSON><PERSON><PERSON> lī<PERSON>ja", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Taisnot<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(neviens)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Noņemt visus", "SSE.Views.ParagraphSettingsAdvanced.textSet": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Centrā", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Cilnes pozīcija", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Labais", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Rindkopa - Papildu iestatījumi", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Delete", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "Dub<PERSON>āts", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Rediģēt", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Formula", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Items Name", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "New", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Calculated Items in", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "vien<PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "ne<PERSON><PERSON><PERSON> ar", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "satur", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "nesatur", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "Star<PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "ne starp", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "nav vienāds ar", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "ir liel<PERSON><PERSON> nekā", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "ir lielāks par vai vienāds ar", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "ir maz<PERSON><PERSON> par", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "ir mazāks par vai vienāds ar", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "s<PERSON><PERSON> ar", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "ne<PERSON><PERSON><PERSON> ar", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "be<PERSON><PERSON> ar", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> vien<PERSON>, kuri<PERSON> etiķete:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>, kuri<PERSON>:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "<PERSON><PERSON><PERSON>jiet ?, lai parā<PERSON><PERSON><PERSON> j<PERSON><PERSON> r<PERSON>", "SSE.Views.PivotDigitalFilterDialog.textUse2": "<PERSON><PERSON><PERSON><PERSON><PERSON> *, lai parā<PERSON><PERSON><PERSON> jeb<PERSON> r<PERSON><PERSON><PERSON> s<PERSON>ju", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "un", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Etiķetes filtrs", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Vērtību filtrs", "SSE.Views.PivotGroupDialog.textAuto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textBy": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textDays": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textEnd": "<PERSON><PERSON><PERSON> plkst", "SSE.Views.PivotGroupDialog.textError": "This field must be a numeric value", "SSE.Views.PivotGroupDialog.textGreaterError": "<PERSON><PERSON><PERSON> numuram jābūt lielākam par sākuma numuru", "SSE.Views.PivotGroupDialog.textHour": "Stundas", "SSE.Views.PivotGroupDialog.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMonth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textNumDays": "<PERSON><PERSON> s<PERSON>", "SSE.Views.PivotGroupDialog.textQuart": "Cetur<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textSec": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textStart": "<PERSON><PERSON><PERSON> ar", "SSE.Views.PivotGroupDialog.textYear": "Gadi", "SSE.Views.PivotGroupDialog.txtTitle": "Grupēša<PERSON>", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "You can use Calculated Items for basic calculations between different items within a single field", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Insert into formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "<PERSON><PERSON>", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Item name", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Items", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Read more", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Insert Calculated Item in", "SSE.Views.PivotSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pap<PERSON>du i<PERSON>", "SSE.Views.PivotSettings.textColumns": "Kolonnas", "SSE.Views.PivotSettings.textFields": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> laukus", "SSE.Views.PivotSettings.textFilters": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddColumn": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddFilter": "<PERSON><PERSON><PERSON> filt<PERSON>m", "SSE.Views.PivotSettings.txtAddRow": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtAddValues": "<PERSON><PERSON><PERSON> v<PERSON>", "SSE.Views.PivotSettings.txtFieldSettings": "<PERSON><PERSON>", "SSE.Views.PivotSettings.txtMoveBegin": "Pārvietot uz sākumu", "SSE.Views.PivotSettings.txtMoveColumn": "Pārvietot uz kolonnām", "SSE.Views.PivotSettings.txtMoveDown": "Pārvietot uz leju", "SSE.Views.PivotSettings.txtMoveEnd": "Pārvietot uz beigām", "SSE.Views.PivotSettings.txtMoveFilter": "Pārvietot uz filtriem", "SSE.Views.PivotSettings.txtMoveRow": "Pārvietot uz rindām", "SSE.Views.PivotSettings.txtMoveUp": "Pārvietot uz augšu", "SSE.Views.PivotSettings.txtMoveValues": "Pārvietot uz vērtībām", "SSE.Views.PivotSettings.txtRemove": "<PERSON><PERSON><PERSON><PERSON> la<PERSON>", "SSE.Views.PivotSettingsAdvanced.strLayout": "Nosaukums un izkārtojums", "SSE.Views.PivotSettingsAdvanced.textAlt": "Alternatīvs teksts", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Vizuālās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, fig<PERSON><PERSON><PERSON>, diagramm<PERSON> vai tabulā.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Nosa<PERSON>ms", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Automātiski pie<PERSON><PERSON> kolonnu platumus at<PERSON>", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Datu diapazons", "SSE.Views.PivotSettingsAdvanced.textDataSource": "<PERSON><PERSON> avots", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON> pārskata filtra apgabalā", "SSE.Views.PivotSettingsAdvanced.textDown": "<PERSON><PERSON> leju, tad pāri", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Lielās gala vērt<PERSON>bas", "SSE.Views.PivotSettingsAdvanced.textHeaders": "<PERSON><PERSON> galvenes", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "KĻŪDA! Nederīgs šūnu diapazons", "SSE.Views.PivotSettingsAdvanced.textOver": "<PERSON><PERSON><PERSON>, pēc tam uz leju", "SSE.Views.PivotSettingsAdvanced.textSelectData": "<PERSON><PERSON><PERSON> da<PERSON>", "SSE.Views.PivotSettingsAdvanced.textShowCols": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>m", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "<PERSON><PERSON><PERSON><PERSON><PERSON> rindu un kolonnu lauku galvenes", "SSE.Views.PivotSettingsAdvanced.textShowRows": "<PERSON><PERSON><PERSON><PERSON><PERSON> rind<PERSON>m", "SSE.Views.PivotSettingsAdvanced.textTitle": "Rakurstabula - Papild<PERSON> i<PERSON>īju<PERSON>", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Zi<PERSON>ot par filtra laukiem katrā kolonnā", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Zi<PERSON>ot par filtra laukiem katrā rindā", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "SSE.Views.PivotSettingsAdvanced.txtName": "Nosa<PERSON>ms", "SSE.Views.PivotShowDetailDialog.textDescription": "Choose the field containing the detail you want to show:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Show Detail", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.capGrandTotals": "Lielās gala vērt<PERSON>bas", "SSE.Views.PivotTable.capLayout": "Ziņojuma izklājums", "SSE.Views.PivotTable.capSubtotals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.mniBottomSubtotals": "Rādīt visas apakšvērtības grupas apakšā", "SSE.Views.PivotTable.mniInsertBlankLine": "<PERSON><PERSON><PERSON><PERSON><PERSON> tukšu līniju pēc katras vienības", "SSE.Views.PivotTable.mniLayoutCompact": "<PERSON><PERSON><PERSON><PERSON><PERSON> kompaktā režīmā", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Neatkārtotu visu vienību birkas", "SSE.Views.PivotTable.mniLayoutOutline": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> re<PERSON>", "SSE.Views.PivotTable.mniLayoutRepeat": "Atkārtot visu vienību birkas", "SSE.Views.PivotTable.mniLayoutTabular": "Rā<PERSON>īt tabulas režīmā", "SSE.Views.PivotTable.mniNoSubtotals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.mniOffTotals": "Atspējot rindām un kolonnām", "SSE.Views.PivotTable.mniOnColumnsTotals": "Iespē<PERSON><PERSON> vien<PERSON>gi kolonn<PERSON>m", "SSE.Views.PivotTable.mniOnRowsTotals": "<PERSON><PERSON><PERSON><PERSON><PERSON> vien<PERSON>gi rind<PERSON>m", "SSE.Views.PivotTable.mniOnTotals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rindām un kolonnām", "SSE.Views.PivotTable.mniRemoveBlankLine": "Noņemt tukšo līniju pēc katras vienības", "SSE.Views.PivotTable.mniTopSubtotals": "Rādīt visas apakšvērtības grupas augšā", "SSE.Views.PivotTable.textColBanded": "<PERSON><PERSON><PERSON> kolo<PERSON>", "SSE.Views.PivotTable.textColHeader": "Kolonnas galvenes", "SSE.Views.PivotTable.textRowBanded": "<PERSON><PERSON><PERSON> r<PERSON><PERSON>", "SSE.Views.PivotTable.textRowHeader": "<PERSON><PERSON><PERSON> galvenes", "SSE.Views.PivotTable.tipCalculatedItems": "Calculated items", "SSE.Views.PivotTable.tipCreatePivot": "<PERSON>ev<PERSON><PERSON> r<PERSON>", "SSE.Views.PivotTable.tipGrandTotals": "<PERSON><PERSON><PERSON><PERSON><PERSON> vai slēpt kopsummas", "SSE.Views.PivotTable.tipRefresh": "Atjaunināt informāciju no datu avota", "SSE.Views.PivotTable.tipRefreshCurrent": "Atjaunināt pašreizējās tabulas informāciju no datu avota", "SSE.Views.PivotTable.tipSelect": "Izvēlēties visu rakurstabulu", "SSE.Views.PivotTable.tipSubtotals": "<PERSON><PERSON><PERSON><PERSON><PERSON> vai slēpt a<PERSON>", "SSE.Views.PivotTable.txtCalculatedItems": "Calculated Items", "SSE.Views.PivotTable.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.PivotTable.txtCreate": "Ievietot tabulu", "SSE.Views.PivotTable.txtExpandEntire": "Expand Entire Field", "SSE.Views.PivotTable.txtGroupPivot_Custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Dark": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Light": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Medium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtPivotTable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtRefresh": "Atsvaidzināt", "SSE.Views.PivotTable.txtRefreshAll": "Atsvaidzināt visu", "SSE.Views.PivotTable.txtSelect": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "<PERSON><PERSON><PERSON><PERSON> rak<PERSON>s stils", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>s stils", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>s stils", "SSE.Views.PrintSettings.btnDownload": "Saglabāt un lejupielādēt", "SSE.Views.PrintSettings.btnExport": "Save & Export", "SSE.Views.PrintSettings.btnPrint": "Saglabāt un drukāt", "SSE.Views.PrintSettings.strBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strLandscape": "Ainava", "SSE.Views.PrintSettings.strLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strMargins": "Piemales", "SSE.Views.PrintSettings.strPortrait": "Portrets", "SSE.Views.PrintSettings.strPrint": "Printēt", "SSE.Views.PrintSettings.strPrintTitles": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strRight": "Labais", "SSE.Views.PrintSettings.strShow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strTop": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textActiveSheets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>as", "SSE.Views.PrintSettings.textActualSize": "<PERSON>ak<PERSON><PERSON><PERSON> lie<PERSON>", "SSE.Views.PrintSettings.textAllSheets": "Visas lapas", "SSE.Views.PrintSettings.textCurrentSheet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "SSE.Views.PrintSettings.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textCustomOptions": "Pielāgot<PERSON> opcijas", "SSE.Views.PrintSettings.textFitCols": "Salikt visas kolonnas vienā lap<PERSON>", "SSE.Views.PrintSettings.textFitPage": "<PERSON><PERSON><PERSON> lapu vienā lap<PERSON>", "SSE.Views.PrintSettings.textFitRows": "Salikt visas rindas vien<PERSON> lap<PERSON>", "SSE.Views.PrintSettings.textHideDetails": "<PERSON>lē<PERSON> s<PERSON>", "SSE.Views.PrintSettings.textIgnore": "<PERSON><PERSON><PERSON><PERSON><PERSON> drukas a<PERSON>", "SSE.Views.PrintSettings.textLayout": "Izklājums", "SSE.Views.PrintSettings.textMarginsNarrow": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textMarginsNormal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textMarginsWide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPageOrientation": "Lapas orientācija", "SSE.Views.PrintSettings.textPages": "Lapas:", "SSE.Views.PrintSettings.textPageScaling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPageSize": "Lapas izmērs", "SSE.Views.PrintSettings.textPrintGrid": "Druk<PERSON>t režģa līnijas", "SSE.Views.PrintSettings.textPrintHeadings": "<PERSON><PERSON><PERSON><PERSON> rindas un kollonas no<PERSON>", "SSE.Views.PrintSettings.textPrintRange": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textRange": "Diapazons", "SSE.Views.PrintSettings.textRepeat": "Atkārtot...", "SSE.Views.PrintSettings.textRepeatLeft": "Atk<PERSON>rtot kolonnas kreis<PERSON> pusē", "SSE.Views.PrintSettings.textRepeatTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> rinda<PERSON> au<PERSON>", "SSE.Views.PrintSettings.textSelection": "Atlase", "SSE.Views.PrintSettings.textSettings": "Lapas uzstādīju<PERSON>", "SSE.Views.PrintSettings.textShowDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.PrintSettings.textShowGrid": "<PERSON><PERSON><PERSON><PERSON><PERSON> režģlīnijas", "SSE.Views.PrintSettings.textShowHeadings": "<PERSON><PERSON><PERSON><PERSON><PERSON> rindas un kolonas virsrak<PERSON>us", "SSE.Views.PrintSettings.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textTitlePDF": "PDF iestatījumi", "SSE.Views.PrintSettings.textTo": "Uz", "SSE.Views.PrintSettings.txtMarginsLast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textFirstCol": "<PERSON><PERSON><PERSON> kolonna", "SSE.Views.PrintTitlesDialog.textFirstRow": "<PERSON><PERSON><PERSON> rinda", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Sasaldētas kolo<PERSON>s", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Sasaldētas rindas", "SSE.Views.PrintTitlesDialog.textInvalidRange": "KĻŪDA! Nederīgs šūnu diapazons", "SSE.Views.PrintTitlesDialog.textLeft": "Atk<PERSON>rtot kolonnas kreis<PERSON> pusē", "SSE.Views.PrintTitlesDialog.textNoRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textRepeat": "Atkārtot...", "SSE.Views.PrintTitlesDialog.textSelectRange": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> rinda<PERSON> au<PERSON>", "SSE.Views.PrintWithPreview.txtActiveSheets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>as", "SSE.Views.PrintWithPreview.txtActualSize": "<PERSON>ak<PERSON><PERSON><PERSON> lie<PERSON>", "SSE.Views.PrintWithPreview.txtAllSheets": "Visas lapas", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "<PERSON><PERSON><PERSON><PERSON> visām lapām", "SSE.Views.PrintWithPreview.txtBothSides": "Print on both sides", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "Flip pages on long edge", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "Flip pages on short edge", "SSE.Views.PrintWithPreview.txtBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCopies": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCurrentSheet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a", "SSE.Views.PrintWithPreview.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCustomOptions": "Pielāgot<PERSON> opcijas", "SSE.Views.PrintWithPreview.txtEmptyTable": "Nav ko druk<PERSON>t, jo tabula ir tukša", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "First page number:", "SSE.Views.PrintWithPreview.txtFitCols": "Salikt visas kolonnas vienā lap<PERSON>", "SSE.Views.PrintWithPreview.txtFitPage": "<PERSON><PERSON><PERSON> lapu vienā lap<PERSON>", "SSE.Views.PrintWithPreview.txtFitRows": "Salikt visas rindas vien<PERSON> lap<PERSON>", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Režģlīnijas un virsraksti", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "G<PERSON><PERSON><PERSON>/kājenes i<PERSON>ī<PERSON>", "SSE.Views.PrintWithPreview.txtIgnore": "<PERSON><PERSON><PERSON><PERSON><PERSON> drukas a<PERSON>", "SSE.Views.PrintWithPreview.txtLandscape": "Ainava", "SSE.Views.PrintWithPreview.txtLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMargins": "Piemales", "SSE.Views.PrintWithPreview.txtMarginsLast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMarginsNormal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMarginsWide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtOf": "no {0}", "SSE.Views.PrintWithPreview.txtOneSide": "Print one sided", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "SSE.Views.PrintWithPreview.txtPage": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "<PERSON><PERSON><PERSON><PERSON> lapas numurs", "SSE.Views.PrintWithPreview.txtPageOrientation": "Lapas orientācija", "SSE.Views.PrintWithPreview.txtPages": "Lapas:", "SSE.Views.PrintWithPreview.txtPageSize": "Lapas izmērs", "SSE.Views.PrintWithPreview.txtPortrait": "Portrets", "SSE.Views.PrintWithPreview.txtPrint": "Printēt", "SSE.Views.PrintWithPreview.txtPrintGrid": "Druk<PERSON>t režģa līnijas", "SSE.Views.PrintWithPreview.txtPrintHeadings": "<PERSON><PERSON><PERSON><PERSON> rindas un kollonas no<PERSON>", "SSE.Views.PrintWithPreview.txtPrintRange": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPrintSides": "Print sides", "SSE.Views.PrintWithPreview.txtPrintTitles": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Drukāt PDF formātā", "SSE.Views.PrintWithPreview.txtRepeat": "Atkārtot...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Atk<PERSON>rtot kolonnas kreis<PERSON> pusē", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> rinda<PERSON> au<PERSON>", "SSE.Views.PrintWithPreview.txtRight": "Labais", "SSE.Views.PrintWithPreview.txtSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtScaling": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSelection": "Atlase", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Lapas iestatījumi", "SSE.Views.PrintWithPreview.txtSheet": "Lapa: {0}", "SSE.Views.PrintWithPreview.txtTo": "uz", "SSE.Views.PrintWithPreview.txtTop": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.textExistName": "KĻŪDA! Jau pastāv diapazons ar <PERSON><PERSON><PERSON> nosaukumu", "SSE.Views.ProtectDialog.textInvalidName": "Diapazona nosaukumam ir jās<PERSON>kas ar burtu, un tajā var būt tikai burti, cipari un atstarpes.", "SSE.Views.ProtectDialog.textInvalidRange": "KĻŪDA! Nederīgs šūnu diapazons", "SSE.Views.ProtectDialog.textSelectData": "<PERSON><PERSON><PERSON> da<PERSON>", "SSE.Views.ProtectDialog.txtAllow": "<PERSON><PERSON><PERSON> visiem <PERSON> lap<PERSON> lie<PERSON>", "SSE.Views.ProtectDialog.txtAllowDescription": "You can unlock specific ranges for editing.", "SSE.Views.ProtectDialog.txtAllowRanges": "Atļaut rediģēt diapazonus", "SSE.Views.ProtectDialog.txtAutofilter": "Izmantot automātisko filtru", "SSE.Views.ProtectDialog.txtDelCols": "<PERSON><PERSON><PERSON><PERSON> kolo<PERSON>", "SSE.Views.ProtectDialog.txtDelRows": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.ProtectDialog.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "SSE.Views.ProtectDialog.txtFormatCells": "Formatēt <PERSON>", "SSE.Views.ProtectDialog.txtFormatCols": "Formatēt kolonnas", "SSE.Views.ProtectDialog.txtFormatRows": "Formatēt rindas", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Aps<PERSON>rinājuma <PERSON>", "SSE.Views.ProtectDialog.txtInsCols": "Iev<PERSON><PERSON> kolonnas", "SSE.Views.ProtectDialog.txtInsHyper": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtInsRows": "<PERSON><PERSON><PERSON><PERSON> rindas", "SSE.Views.ProtectDialog.txtObjs": "Rediģēt objektus", "SSE.Views.ProtectDialog.txtOptional": "pē<PERSON> iz<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPassword": "Parole", "SSE.Views.ProtectDialog.txtPivot": "Izmantot rakurstabulu un rakursdiagrammu", "SSE.Views.ProtectDialog.txtProtect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRange": "Diapazons", "SSE.Views.ProtectDialog.txtRangeName": "Nosa<PERSON>ms", "SSE.Views.ProtectDialog.txtRepeat": "Atk<PERSON><PERSON><PERSON> paroli", "SSE.Views.ProtectDialog.txtScen": "Rediģēt scenārijus", "SSE.Views.ProtectDialog.txtSelLocked": "Atlasīt bloķētās šūnas", "SSE.Views.ProtectDialog.txtSelUnLocked": "Atlasīt atbloķētās šūnas", "SSE.Views.ProtectDialog.txtSheetDescription": "Novērsiet nevēlamas citu izmaiņas, ierobežojot viņu rediģēšanas iespējas.", "SSE.Views.ProtectDialog.txtSheetTitle": "Lapas aizsardzība", "SSE.Views.ProtectDialog.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtWarning": "Brīdinājums: <PERSON><PERSON><PERSON><PERSON> vai aizmirstu paroli nevar atgūt. Glabājiet drošā vietā.", "SSE.Views.ProtectDialog.txtWBDescription": "<PERSON> ne<PERSON>u citiem lietotājiem skat<PERSON>t slēpt<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dzēst vai slēpt darblapas un pārdēvēt darblapas, varat aizsargāt darbgr<PERSON><PERSON>s struktūru ar paroli.", "SSE.Views.ProtectDialog.txtWBTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> da<PERSON><PERSON><PERSON> struk<PERSON>", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Anyone", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Rediģēt", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Denied", "SSE.Views.ProtectedRangesEditDlg.textCanView": "View", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "Diapazona nosaukumam ir jās<PERSON>kas ar burtu, un tajā var būt tikai burti, cipari un atstarpes.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "KĻŪDA! Nederīgs šūnu diapazons", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Remove", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datus", "SSE.Views.ProtectedRangesEditDlg.textYou": "you", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Access to range", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.txtRange": "Diapazons", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "Nosa<PERSON>ms", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Only you can edit this range", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Start typing name or email", "SSE.Views.ProtectedRangesManagerDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.lockText": "Bloķēta", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "Rediģēt", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "No protected ranges have been created yet.<br>Create at least one protected range and it will appear in this field.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "Filtrs", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "Visi", "SSE.Views.ProtectedRangesManagerDlg.textNew": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Lapas aizsardzība", "SSE.Views.ProtectedRangesManagerDlg.textRange": "Diapazons", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "You can restrict editing ranges to selected people.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "Nosa<PERSON>ms", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "Šo elementu lieto cits lietotājs.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Access", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Denied", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "Rediģēt", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "Rediģēt diapazonu", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "Jauns diapazons", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Protected ranges", "SSE.Views.ProtectedRangesManagerDlg.txtView": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Are you sure you want to delete the protected range {0}?<br>Anyone who has edit access to the spreadsheet will be able to edit content in the range.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Are you sure you want to delete the protected ranges?<br>Anyone who has edit access to the spreadsheet will be able to edit content in those ranges.", "SSE.Views.ProtectRangesDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.lockText": "Bloķēta", "SSE.Views.ProtectRangesDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEdit": "Rediģēt", "SSE.Views.ProtectRangesDlg.textEmpty": "Nav atļauts rediģēt nevienu di<PERSON>.", "SSE.Views.ProtectRangesDlg.textNew": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textProtect": "Lapas aizsardzība", "SSE.Views.ProtectRangesDlg.textPwd": "Parole", "SSE.Views.ProtectRangesDlg.textRange": "Diapazons", "SSE.Views.ProtectRangesDlg.textRangesDesc": "<PERSON><PERSON><PERSON><PERSON>, kas atbloķēti ar paroli, ja lapa ir aizsar<PERSON>ta (tas darbojas tikai bloķētām šūnām)", "SSE.Views.ProtectRangesDlg.textTitle": "Nosa<PERSON>ms", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Šo elementu lieto cits lietotājs.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Rediģēt diapazonu", "SSE.Views.ProtectRangesDlg.txtNewRange": "Jauns diapazons", "SSE.Views.ProtectRangesDlg.txtNo": "Nē", "SSE.Views.ProtectRangesDlg.txtTitle": "<PERSON><PERSON><PERSON> rediģēt diapazonus", "SSE.Views.ProtectRangesDlg.txtYes": "Jā", "SSE.Views.ProtectRangesDlg.warnDelete": "Vai tiešām vēlaties dzēst vārdu {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Kolonnas", "SSE.Views.RemoveDuplicatesDialog.textDescription": "To delete duplicate values, select one or more columns that contain duplicates.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Maniem datiem ir galvenes", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Izvēlēties visu", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.ariaRightMenu": "Right menu", "SSE.Views.RightMenu.txtCellSettings": "<PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtChartSettings": "Diagram<PERSON> i<PERSON>ī<PERSON>", "SSE.Views.RightMenu.txtImageSettings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtParagraphSettings": "Rindkopas iestatījumi", "SSE.Views.RightMenu.txtPivotSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtShapeSettings": "Formas iestatījumi", "SSE.Views.RightMenu.txtSignatureSettings": "Paraksta uzstādījumi", "SSE.Views.RightMenu.txtSlicerSettings": "<PERSON><PERSON> grie<PERSON>", "SSE.Views.RightMenu.txtSparklineSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RightMenu.txtTableSettings": "Tabulas i<PERSON>ī<PERSON>", "SSE.Views.RightMenu.txtTextArtSettings": "<PERSON><PERSON><PERSON> m<PERSON>", "SSE.Views.ScaleDialog.textAuto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textError": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vērtība ir nepareiza.", "SSE.Views.ScaleDialog.textFewPages": "lapas", "SSE.Views.ScaleDialog.textFitTo": "Saskaņot ar", "SSE.Views.ScaleDialog.textHeight": "Augstums", "SSE.Views.ScaleDialog.textManyPages": "lapas", "SSE.Views.ScaleDialog.textOnePage": "lapa", "SSE.Views.ScaleDialog.textScaleTo": "Mērogot uz", "SSE.Views.ScaleDialog.textTitle": "Mērog<PERSON><PERSON><PERSON> iestatīju<PERSON>", "SSE.Views.ScaleDialog.textWidth": "Platums", "SSE.Views.SetValueDialog.txtMaxText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vērtība šajā jomā ir {0}", "SSE.Views.SetValueDialog.txtMinText": "<PERSON><PERSON><PERSON><PERSON><PERSON> vērtība šajā jomā ir {0}", "SSE.Views.ShapeSettings.strBackground": "Fona krāsa", "SSE.Views.ShapeSettings.strChange": "<PERSON><PERSON><PERSON> formu", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strForeground": "Priekšplāna krāsa", "SSE.Views.ShapeSettings.strPattern": "Raksts", "SSE.Views.ShapeSettings.strShadow": "<PERSON><PERSON><PERSON><PERSON><PERSON>nu", "SSE.Views.ShapeSettings.strSize": "Izmērs", "SSE.Views.ShapeSettings.strStroke": "Lī<PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "Necaurred<PERSON><PERSON>ba", "SSE.Views.ShapeSettings.strType": "Tips", "SSE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "SSE.Views.ShapeSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pap<PERSON>du i<PERSON>", "SSE.Views.ShapeSettings.textAngle": "Leņķis", "SSE.Views.ShapeSettings.textBorderSizeErr": "Ievadītā vērtība nav pareiza.<br>Ievadiet vērtību starp 0 pt un 1584 pt.", "SSE.Views.ShapeSettings.textColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textEditPoints": "Rediģēt punktus", "SSE.Views.ShapeSettings.textEditShape": "Edit shape", "SSE.Views.ShapeSettings.textEmptyPattern": "Nav modeļa", "SSE.Views.ShapeSettings.textEyedropper": "Eyedropper", "SSE.Views.ShapeSettings.textFlip": "Uzsist", "SSE.Views.ShapeSettings.textFromFile": "No faila", "SSE.Views.ShapeSettings.textFromStorage": "No glabātuves", "SSE.Views.ShapeSettings.textFromUrl": "No URL", "SSE.Views.ShapeSettings.textGradient": "Gradienta punkti", "SSE.Views.ShapeSettings.textGradientFill": "Gradienta aizpildīju<PERSON>", "SSE.Views.ShapeSettings.textHint270": "Pagriezt par 90° pret<PERSON><PERSON> pulksteņrā<PERSON><PERSON><PERSON><PERSON><PERSON> virzienam", "SSE.Views.ShapeSettings.textHint90": "Pagriezt par 90° pulkste<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> virzienā", "SSE.Views.ShapeSettings.textHintFlipH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textHintFlipV": "<PERSON><PERSON><PERSON><PERSON><PERSON> vert<PERSON>", "SSE.Views.ShapeSettings.textImageTexture": "Attēls vai tekstūra", "SSE.Views.ShapeSettings.textLinear": "Lineārs", "SSE.Views.ShapeSettings.textMoreColors": "More colors", "SSE.Views.ShapeSettings.textNoFill": "<PERSON>z <PERSON>", "SSE.Views.ShapeSettings.textNoShadow": "No Shadow", "SSE.Views.ShapeSettings.textOriginalSize": "Oriģinālais izmērs", "SSE.Views.ShapeSettings.textPatternFill": "Raksts", "SSE.Views.ShapeSettings.textPosition": "Pozīcija", "SSE.Views.ShapeSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textRecentlyUsed": "Nesen lietots", "SSE.Views.ShapeSettings.textRotate90": "Pagriezt par 90°", "SSE.Views.ShapeSettings.textRotation": "Rotā<PERSON>ja", "SSE.Views.ShapeSettings.textSelectImage": "<PERSON><PERSON><PERSON> at<PERSON>", "SSE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textShadow": "Ēna", "SSE.Views.ShapeSettings.textStretch": "Stiept", "SSE.Views.ShapeSettings.textStyle": "Stils", "SSE.Views.ShapeSettings.textTexture": "No tekstūras", "SSE.Views.ShapeSettings.textTile": "Elements", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Pievienot gradienta punktu", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Noņemt gradienta punktu", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON><PERSON> pap<PERSON>", "SSE.Views.ShapeSettings.txtCanvas": "Pamatne", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "<PERSON><PERSON><PERSON><PERSON> audums", "SSE.Views.ShapeSettings.txtGrain": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "Pelēks papī<PERSON>", "SSE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtNoBorders": "Nav līni<PERSON>", "SSE.Views.ShapeSettings.txtPapyrus": "Papiruss", "SSE.Views.ShapeSettings.txtWood": "Koks", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Kolonnas", "SSE.Views.ShapeSettingsAdvanced.strMargins": "<PERSON><PERSON><PERSON> i<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Nepārvietot un neveidot iz<PERSON>ērus ar šū<PERSON>m", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Alternatīvs teksts", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Vizuālās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, fig<PERSON><PERSON><PERSON>, diagramm<PERSON> vai tabulā.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Nosa<PERSON>ms", "SSE.Views.ShapeSettingsAdvanced.textAngle": "Leņķis", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Automātis<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Sāk. izmērs", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Sāk. stils", "SSE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON>ī<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON> burtu <PERSON>", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Beig<PERSON> izmē<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "<PERSON><PERSON><PERSON> stils", "SSE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Augstums", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tips", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Rindu stils", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Taisns", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bet neveidot izmērus ar <PERSON>m", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Atļaut tekstam pārpild<PERSON>t formu", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "<PERSON><PERSON>t formas i<PERSON>, lai tas atbilstu tekstam", "SSE.Views.ShapeSettingsAdvanced.textRight": "Labais", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Rotā<PERSON>ja", "SSE.Views.ShapeSettingsAdvanced.textRound": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSize": "Izmērs", "SSE.Views.ShapeSettingsAdvanced.textSnap": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Atstarpe starp kolonn<PERSON>m", "SSE.Views.ShapeSettingsAdvanced.textSquare": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Uzraksts", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Forma – <PERSON><PERSON>ld<PERSON> i<PERSON>īju<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTop": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Pārvietot un izmērīt ar šūnām", "SSE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Svari un bultiņas", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Platums", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strDelete": "Noņemt parakstu", "SSE.Views.SignatureSettings.strDetails": "Paraksta de<PERSON>ļas", "SSE.Views.SignatureSettings.strInvalid": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strRequested": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSetup": "Paraksta uzstādīšana", "SSE.Views.SignatureSettings.strSign": "Parakstīt", "SSE.Views.SignatureSettings.strSignature": "Paraksts", "SSE.Views.SignatureSettings.strSigner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strValid": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.txtContinueEditing": "Vienalga rediģēt", "SSE.Views.SignatureSettings.txtEditWarning": "Rediģēšana no tabulas noņems parakstus.<br>Vai tiešām vēlaties turpināt?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Vai vēlaties noņemt šo parakstu?<br>To nevar at<PERSON>.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "<PERSON><PERSON> i<PERSON>lapa ir j<PERSON>.", "SSE.Views.SignatureSettings.txtSigned": "Izklājlapai ir pievienoti derīgi paraksti. Izklājlapu nevar rediģēt.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Daži rēķintabulas digitālie paraksti nav derīgi vai tos nevar pārbaud<PERSON>t. Izklājlapu nevar rediģēt.", "SSE.Views.SlicerAddDialog.textColumns": "Kolonnas", "SSE.Views.SlicerAddDialog.txtTitle": "Ieviet<PERSON> datu griezumus", "SSE.Views.SlicerSettings.strHideNoData": "<PERSON><PERSON><PERSON><PERSON> vienumus bez datiem", "SSE.Views.SlicerSettings.strIndNoData": "V<PERSON><PERSON><PERSON><PERSON> nor<PERSON><PERSON>t vienumus bez datiem", "SSE.Views.SlicerSettings.strShowDel": "<PERSON><PERSON><PERSON><PERSON>t no datu avota izdz<PERSON> vienumus", "SSE.Views.SlicerSettings.strShowNoData": "<PERSON><PERSON><PERSON><PERSON><PERSON> vienumus bez datiem pēdējos", "SSE.Views.SlicerSettings.strSorting": "Šķirošana un filtrēšana", "SSE.Views.SlicerSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pap<PERSON>du i<PERSON>", "SSE.Views.SlicerSettings.textAsc": "<PERSON><PERSON><PERSON><PERSON> secībā", "SSE.Views.SlicerSettings.textAZ": "No A līdz Z", "SSE.Views.SlicerSettings.textButtons": "Pogas", "SSE.Views.SlicerSettings.textColumns": "Kolonnas", "SSE.Views.SlicerSettings.textDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON> secībā", "SSE.Views.SlicerSettings.textHeight": "Augstums", "SSE.Views.SlicerSettings.textHor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textLargeSmall": "no lielākā uz vismazāko", "SSE.Views.SlicerSettings.textLock": "Atspējot izmēru maiņu vai pār<PERSON>", "SSE.Views.SlicerSettings.textNewOld": "no jaunākā uz vecāko", "SSE.Views.SlicerSettings.textOldNew": "no vecākā uz jaunāko", "SSE.Views.SlicerSettings.textPosition": "Pozīcija", "SSE.Views.SlicerSettings.textSize": "Izmērs", "SSE.Views.SlicerSettings.textSmallLarge": "no mazākā līdz <PERSON>m", "SSE.Views.SlicerSettings.textStyle": "Stils", "SSE.Views.SlicerSettings.textVert": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textWidth": "Platums", "SSE.Views.SlicerSettings.textZA": "No Z līdz A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Pogas", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Kolonnas", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Augstums", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "<PERSON><PERSON><PERSON><PERSON> vienumus bez datiem", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "V<PERSON><PERSON><PERSON><PERSON> nor<PERSON><PERSON>t vienumus bez datiem", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Atsauces", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "<PERSON><PERSON><PERSON><PERSON>t no datu avota izdz<PERSON> vienumus", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>i", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "<PERSON><PERSON><PERSON><PERSON><PERSON> vienumus bez datiem pēdējos", "SSE.Views.SlicerSettingsAdvanced.strSize": "Izmērs", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Šķirošana un filtrēšana", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Stils", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Stils un izmērs", "SSE.Views.SlicerSettingsAdvanced.strWidth": "Platums", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Nepārvietot un neveidot iz<PERSON>ērus ar šū<PERSON>m", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Alternatīvs teksts", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Vizuālās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, fig<PERSON><PERSON><PERSON>, diagramm<PERSON> vai tabulā.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Nosa<PERSON>ms", "SSE.Views.SlicerSettingsAdvanced.textAsc": "<PERSON><PERSON><PERSON><PERSON> secībā", "SSE.Views.SlicerSettingsAdvanced.textAZ": "No A līdz Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON> secībā", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textHeader": "G<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "no lielākā uz vismazāko", "SSE.Views.SlicerSettingsAdvanced.textName": "Nosa<PERSON>ms", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "no jaunākā uz vecāko", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "no vecākā uz jaunāko", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bet neveidot izmērus ar <PERSON>m", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "no mazākā līdz <PERSON>m", "SSE.Views.SlicerSettingsAdvanced.textSnap": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Avota no<PERSON>uku<PERSON>", "SSE.Views.SlicerSettingsAdvanced.textTitle": "<PERSON><PERSON> grie<PERSON> – papildu iestatījumi", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Pārvietot un izmērīt ar šūnām", "SSE.Views.SlicerSettingsAdvanced.textZA": "No Z līdz A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "SSE.Views.SortDialog.errorEmpty": "<PERSON><PERSON><PERSON> k<PERSON> kritērijiem ir jābūt norād<PERSON>tai kolonnai vai rindai.", "SSE.Views.SortDialog.errorMoreOneCol": "<PERSON>r atlas<PERSON><PERSON> vairāk nekā viena kolonna.", "SSE.Views.SortDialog.errorMoreOneRow": "<PERSON>r atlas<PERSON><PERSON> vairāk nekā viena rinda.", "SSE.Views.SortDialog.errorNotOriginalCol": "<PERSON><PERSON><PERSON> atlasītā kolonna neatrodas sākotnēji atlasītajā diapazon<PERSON>.", "SSE.Views.SortDialog.errorNotOriginalRow": "<PERSON><PERSON><PERSON> atlas<PERSON>tā rinda <PERSON>as sākotn<PERSON>ji atlasītajā diapazon<PERSON>.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 tiek kārtots pēc tās pašas krāsas vairāk nekā vienu reizi.<br>Izdz<PERSON>siet kārtošanas kritēriju dublikātus un mēģiniet vēlreiz.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 tiek kārtots pēc vērtībām vairāk nekā vienu reizi.<br>Izdz<PERSON>siet kārtošanas kritēriju dublik<PERSON>tus un mēģiniet vēlreiz.", "SSE.Views.SortDialog.textAsc": "<PERSON><PERSON><PERSON><PERSON> secībā", "SSE.Views.SortDialog.textAuto": "Automātisks", "SSE.Views.SortDialog.textAZ": "No A līdz Z", "SSE.Views.SortDialog.textBelow": "Zem", "SSE.Views.SortDialog.textBtnCopy": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textBtnDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textBtnNew": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textColumn": "Kolonna", "SSE.Views.SortDialog.textDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON> secībā", "SSE.Views.SortDialog.textDown": "Pārvietot līmeni uz leju", "SSE.Views.SortDialog.textFontColor": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textLevels": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textMoreCols": "(<PERSON><PERSON><PERSON><PERSON> kolonnu...)", "SSE.Views.SortDialog.textMoreRows": "(Vair<PERSON><PERSON> rindu...)", "SSE.Views.SortDialog.textNone": "Neviens", "SSE.Views.SortDialog.textOptions": "Opcijas", "SSE.Views.SortDialog.textOrder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textRight": "Labais", "SSE.Views.SortDialog.textRow": "<PERSON><PERSON>", "SSE.Views.SortDialog.textSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textSortBy": "Šķirot pēc", "SSE.Views.SortDialog.textThenBy": "<PERSON><PERSON><PERSON> tam pēc", "SSE.Views.SortDialog.textTop": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textUp": "Pārvietot līmeni uz augšu", "SSE.Views.SortDialog.textValues": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textZA": "No Z līdz A", "SSE.Views.SortDialog.txtInvalidRange": "<PERSON><PERSON><PERSON><PERSON> diapazons", "SSE.Views.SortDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortFilterDialog.textAsc": "<PERSON><PERSON><PERSON><PERSON> secībā (A līdz Z) par", "SSE.Views.SortFilterDialog.textDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON> (no Z līdz A)", "SSE.Views.SortFilterDialog.textNoSort": "No sort", "SSE.Views.SortFilterDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortFilterDialog.txtTitleValue": "Sort by value", "SSE.Views.SortOptionsDialog.textCase": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> burtu <PERSON>", "SSE.Views.SortOptionsDialog.textHeaders": "Maniem datiem ir galvenes", "SSE.Views.SortOptionsDialog.textLeftRight": "<PERSON><PERSON>rtot no kreisās uz labo", "SSE.Views.SortOptionsDialog.textOrientation": "Orientācija", "SSE.Views.SortOptionsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> op<PERSON>", "SSE.Views.SortOptionsDialog.textTopBottom": "<PERSON><PERSON><PERSON><PERSON> no augšas uz leju", "SSE.Views.SpecialPasteDialog.textAdd": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textAll": "Visi", "SSE.Views.SpecialPasteDialog.textBlanks": "<PERSON><PERSON><PERSON><PERSON> tuk<PERSON> vietas", "SSE.Views.SpecialPasteDialog.textColWidth": "Kolonnas platumi", "SSE.Views.SpecialPasteDialog.textComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textDiv": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFFormat": "Formulas un formatēšana", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formulas un skaitļu formāti", "SSE.Views.SpecialPasteDialog.textFormats": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFormulas": "Formulas", "SSE.Views.SpecialPasteDialog.textFWidth": "Formulu un kolonnu platumi", "SSE.Views.SpecialPasteDialog.textMult": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textNone": "Neviens", "SSE.Views.SpecialPasteDialog.textOperation": "Operācija", "SSE.Views.SpecialPasteDialog.textPaste": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textSub": "Atņemt", "SSE.Views.SpecialPasteDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textTranspose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textValues": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textVFormat": "Vērtības un formatējums", "SSE.Views.SpecialPasteDialog.textVNFormat": "Vērtības un skaitļu formāti", "SSE.Views.SpecialPasteDialog.textWBorders": "Visi, izņ<PERSON>ot <PERSON>", "SSE.Views.Spellcheck.noSuggestions": "Nav pareizrakstības ieteikumu", "SSE.Views.Spellcheck.textChange": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textChangeAll": "<PERSON><PERSON><PERSON> visu", "SSE.Views.Spellcheck.textIgnore": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textIgnoreAll": "Igno<PERSON><PERSON>t visu", "SSE.Views.Spellcheck.txtAddToDictionary": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.txtClosePanel": "<PERSON>z<PERSON><PERSON>rt <PERSON>", "SSE.Views.Spellcheck.txtComplete": "Pareizrakst<PERSON><PERSON> p<PERSON> ir p<PERSON>ta", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Vārdnīcas valoda", "SSE.Views.Spellcheck.txtNextTip": "Doties uz nākamo vārdu", "SSE.Views.Spellcheck.txtSpelling": "Pareizrakstība", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Pārvietot uz beigām)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "Create a copy", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(Create new spreadsheet)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Pārvietot pirms lapas", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Spreadsheet", "SSE.Views.Statusbar.filteredRecordsText": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>: {0} no {1}", "SSE.Views.Statusbar.filteredText": "Filtra režīms", "SSE.Views.Statusbar.itemAverage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemCount": "Skaits", "SSE.Views.Statusbar.itemDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemHidden": "Paslēpts", "SSE.Views.Statusbar.itemHide": "Paslēpt", "SSE.Views.Statusbar.itemInsert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMaximum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMinimum": "Minimums", "SSE.Views.Statusbar.itemMoveOrCopy": "Move or copy", "SSE.Views.Statusbar.itemProtect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemRename": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemStatus": "Saglabāšanas statuss", "SSE.Views.Statusbar.itemSum": "Summa", "SSE.Views.Statusbar.itemTabColor": "Cilnes krāsa", "SSE.Views.Statusbar.itemUnProtect": "Noņemt aizsardzību", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Darbla<PERSON> ar <PERSON> no<PERSON> jau <PERSON>āv.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Lapas nosaukums nedrīkst saturēt šādas rakstzīmes: \\\\/*?[]: vai rakstzīme ' kā pirmā vai pēdējā rakstzīme", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Lapas nosaukums", "SSE.Views.Statusbar.selectAllSheets": "Atlasīt visas lapas", "SSE.Views.Statusbar.sheetIndexText": "{0}. no {1} lapa", "SSE.Views.Statusbar.textAverage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textCount": "Skaits", "SSE.Views.Statusbar.textMax": "Ma<PERSON>.", "SSE.Views.Statusbar.textMin": "<PERSON>.", "SSE.Views.Statusbar.textNewColor": "<PERSON><PERSON><PERSON> jau<PERSON>", "SSE.Views.Statusbar.textNoColor": "Nav kr<PERSON><PERSON>", "SSE.Views.Statusbar.textSum": "Summa", "SSE.Views.Statusbar.tipAddTab": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipFirst": "Ritiniet līdz pirma<PERSON>i lapai", "SSE.Views.Statusbar.tipLast": "Ritiniet lī<PERSON>z pē<PERSON><PERSON><PERSON><PERSON> lapai", "SSE.Views.Statusbar.tipListOfSheets": "Izklājlapu sarak<PERSON>", "SSE.Views.Statusbar.tipNext": "<PERSON><PERSON><PERSON><PERSON><PERSON> lapu sarakstu pa labi", "SSE.Views.Statusbar.tipPrev": "<PERSON><PERSON><PERSON><PERSON><PERSON> lapu sarakstu pa kreisi", "SSE.Views.Statusbar.tipZoomFactor": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.ungroupSheets": "Atgrup<PERSON><PERSON> lapas", "SSE.Views.Statusbar.zoomText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Darbību nevarēja veikt atlasītajam šūnu diapazonam.<br>Atlasiet vienotu datu diapazonu, kas atšķiras no esošā, un mēģiniet vēlreiz.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Darbī<PERSON> neizdevās izpildīt izvēlētajā šūnu diapazon<PERSON>.<br>Izv<PERSON><PERSON><PERSON> diapazonu, lai pirmā tabulas rinda būtu tajā pašā rindā<br>un gala tabula pārklātos ar esošo.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Operāciju neizdevās izpild<PERSON>t izvēlēta<PERSON><PERSON> diapazon<PERSON>.<br>Izv<PERSON><PERSON><PERSON> diapazonu, kas neietver citas tabulas.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "<PERSON><PERSON><PERSON><PERSON> nav at<PERSON><PERSON><PERSON> vair<PERSON>ku šūnu masīvu formulas.", "SSE.Views.TableOptionsDialog.txtEmpty": "<PERSON><PERSON> lauks ir oblig<PERSON>", "SSE.Views.TableOptionsDialog.txtFormat": "Izveidot tabulu", "SSE.Views.TableOptionsDialog.txtInvalidRange": "KĻŪDA! Nederīgs šūnu diapazons", "SSE.Views.TableOptionsDialog.txtNote": "Galvenēm jāpaliek tajā pašā rindā, un iegūtajam tabulas diapazonam ir jāpārklājas ar sākotnējo tabulas diapazonu.", "SSE.Views.TableOptionsDialog.txtTitle": "Nosa<PERSON>ms", "SSE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON> kolonnu", "SSE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON><PERSON> rindu", "SSE.Views.TableSettings.deleteTableText": "<PERSON><PERSON><PERSON><PERSON> tabulu", "SSE.Views.TableSettings.insertColumnLeftText": "Ievietot kolonnu pa kreisi", "SSE.Views.TableSettings.insertColumnRightText": "Ieviet<PERSON> kolonnu pa labi", "SSE.Views.TableSettings.insertRowAboveText": "Ievietot rindu augstāk", "SSE.Views.TableSettings.insertRowBelowText": "Ievietot rindu <PERSON>", "SSE.Views.TableSettings.notcriticalErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectColumnText": "Izvēlēties visu kolonnu", "SSE.Views.TableSettings.selectDataText": "Izvēlē<PERSON> kolonnas datus", "SSE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON> rindu", "SSE.Views.TableSettings.selectTableText": "Izvēlēties tabulu", "SSE.Views.TableSettings.textActions": "Tabulas <PERSON>", "SSE.Views.TableSettings.textAdvanced": "<PERSON><PERSON><PERSON><PERSON><PERSON> pap<PERSON>du i<PERSON>", "SSE.Views.TableSettings.textBanded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textColumns": "Kolonnas", "SSE.Views.TableSettings.textConvertRange": "Konvertēt uz diapazonu", "SSE.Views.TableSettings.textEdit": "<PERSON><PERSON><PERSON> un kolonnas", "SSE.Views.TableSettings.textEmptyTemplate": "<PERSON>z <PERSON>ē<PERSON>", "SSE.Views.TableSettings.textExistName": "KĻŪDA! Jau pastāv diapazons ar šādu vārdu", "SSE.Views.TableSettings.textFilter": "Filtra poga", "SSE.Views.TableSettings.textFirst": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textHeader": "G<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textInvalidName": "KĻŪDA! Nederīgs tabulas nosaukums", "SSE.Views.TableSettings.textIsLocked": "Šo elementu lieto cits lietotājs.", "SSE.Views.TableSettings.textLast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textLongOperation": "Ilga darbība", "SSE.Views.TableSettings.textPivot": "<PERSON>ev<PERSON><PERSON> r<PERSON>", "SSE.Views.TableSettings.textRemDuplicates": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textReservedName": "<PERSON><PERSON><PERSON><PERSON>, kuru mēģināt i<PERSON>, jau ir norād<PERSON>ts šūnu formulās. Izmantojiet kādu citu nosaukumu.", "SSE.Views.TableSettings.textResize": "Tabulas izmēra maiņa", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datus", "SSE.Views.TableSettings.textSlicer": "Ievietot datu griezumu", "SSE.Views.TableSettings.textTableName": "Tabulas no<PERSON>", "SSE.Views.TableSettings.textTemplate": "<PERSON><PERSON><PERSON> no veidnes", "SSE.Views.TableSettings.textTotal": "Kopā", "SSE.Views.TableSettings.txtGroupTable_Custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.txtGroupTable_Dark": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.txtGroupTable_Light": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.txtGroupTable_Medium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Tumšs tabulas stils", "SSE.Views.TableSettings.txtTable_TableStyleLight": "Gaišs tabulas stils", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "Vid<PERSON>js tabulas stils", "SSE.Views.TableSettings.warnLongOperation": "<PERSON><PERSON><PERSON><PERSON>, ko gras<PERSON><PERSON> veikt, var prasīt diezgan daudz laika.<br>Vai vēlaties turpināt?", "SSE.Views.TableSettingsAdvanced.textAlt": "Alternatīvs teksts", "SSE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textAltTip": "Vizuālās objekta informācijas attainojums alternatīvā teksta veidā, kuru lasīs cilvēki ar redze vai uztveres traucējumiem un kuriem tas labāk palīdz<PERSON>, kāda informācija ir ietverta tekstā, fig<PERSON><PERSON><PERSON>, diagramm<PERSON> vai tabulā.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Nosa<PERSON>ms", "SSE.Views.TableSettingsAdvanced.textTitle": "Tabula - Papildu i<PERSON>ī<PERSON>", "SSE.Views.TextArtSettings.strBackground": "Fona krāsa", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strForeground": "Priekšplāna krāsa", "SSE.Views.TextArtSettings.strPattern": "Raksts", "SSE.Views.TextArtSettings.strSize": "Izmērs", "SSE.Views.TextArtSettings.strStroke": "Lī<PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "Necaurred<PERSON><PERSON>ba", "SSE.Views.TextArtSettings.strType": "Tips", "SSE.Views.TextArtSettings.textAngle": "Leņķis", "SSE.Views.TextArtSettings.textBorderSizeErr": "Ievadītā vērtība nav pareiza.<br>Ievadiet vērtību starp 0 pt un 1584 pt.", "SSE.Views.TextArtSettings.textColor": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textDirection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textEmptyPattern": "Nav modeļa", "SSE.Views.TextArtSettings.textFromFile": "No faila", "SSE.Views.TextArtSettings.textFromUrl": "No URL", "SSE.Views.TextArtSettings.textGradient": "Gradienta punkti", "SSE.Views.TextArtSettings.textGradientFill": "Gradienta aizpildīju<PERSON>", "SSE.Views.TextArtSettings.textImageTexture": "Attēls vai tekstūra", "SSE.Views.TextArtSettings.textLinear": "Lineārs", "SSE.Views.TextArtSettings.textNoFill": "<PERSON>z <PERSON>", "SSE.Views.TextArtSettings.textPatternFill": "Raksts", "SSE.Views.TextArtSettings.textPosition": "Pozīcija", "SSE.Views.TextArtSettings.textRadial": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStretch": "Stiept", "SSE.Views.TextArtSettings.textStyle": "Stils", "SSE.Views.TextArtSettings.textTemplate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textTexture": "No tekstūras", "SSE.Views.TextArtSettings.textTile": "Elements", "SSE.Views.TextArtSettings.textTransform": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Pievienot gradienta punktu", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Noņemt gradienta punktu", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON><PERSON> pap<PERSON>", "SSE.Views.TextArtSettings.txtCanvas": "Pamatne", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "<PERSON><PERSON><PERSON><PERSON> audums", "SSE.Views.TextArtSettings.txtGrain": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "Pelēks papī<PERSON>", "SSE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtNoBorders": "Nav līni<PERSON>", "SSE.Views.TextArtSettings.txtPapyrus": "Papiruss", "SSE.Views.TextArtSettings.txtWood": "Koks", "SSE.Views.Toolbar.capBtnAddComment": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnColorSchemas": "<PERSON><PERSON><PERSON><PERSON> skala", "SSE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnInsHeader": "Galvene un Kājene", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON><PERSON> grie<PERSON>", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Simbols", "SSE.Views.Toolbar.capBtnMargins": "Piemales", "SSE.Views.Toolbar.capBtnPageBreak": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageOrient": "Orientācija", "SSE.Views.Toolbar.capBtnPageSize": "Izmērs", "SSE.Views.Toolbar.capBtnPrintArea": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintTitles": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnScale": "<PERSON><PERSON><PERSON><PERSON>, lai ietilptu", "SSE.Views.Toolbar.capImgAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capImgBackward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capImgForward": "Pārnest uz priekšu", "SSE.Views.Toolbar.capImgGroup": "Grupa", "SSE.Views.Toolbar.capInsertChart": "Di<PERSON>ram<PERSON>", "SSE.Views.Toolbar.capInsertChartRecommend": "Recommended Chart", "SSE.Views.Toolbar.capInsertEquation": "Vienādojums", "SSE.Views.Toolbar.capInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertImage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertShape": "Forma", "SSE.Views.Toolbar.capInsertSpark": "Sīkdiagramma", "SSE.Views.Toolbar.capInsertTable": "Tabula", "SSE.Views.Toolbar.capInsertText": "Uzraksts", "SSE.Views.Toolbar.capInsertTextart": "Text Art", "SSE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniCapitalizeWords": "<PERSON><PERSON><PERSON> lielos burtus katram vārdam", "SSE.Views.Toolbar.mniImageFromFile": "Att<PERSON><PERSON> no faila", "SSE.Views.Toolbar.mniImageFromStorage": "Attēls no glabātuves", "SSE.Views.Toolbar.mniImageFromUrl": "Attēls no vietrāža URL", "SSE.Views.Toolbar.mniLowerCase": "mazie burti", "SSE.Views.Toolbar.mniSentenceCase": "<PERSON><PERSON><PERSON> reģistrs.", "SSE.Views.Toolbar.mniToggleCase": "pĀRSLĒGT rEĢISTRU", "SSE.Views.Toolbar.mniUpperCase": "LIELIE BURTI", "SSE.Views.Toolbar.textAddPrintArea": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignBottom": "Līdzināt pie apakšas", "SSE.Views.Toolbar.textAlignCenter": "L<PERSON><PERSON><PERSON>āt pa centru", "SSE.Views.Toolbar.textAlignJust": "Taisnot<PERSON>", "SSE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa kreisi", "SSE.Views.Toolbar.textAlignMiddle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz vidu", "SSE.Views.Toolbar.textAlignRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa labi", "SSE.Views.Toolbar.textAlignTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz augšu", "SSE.Views.Toolbar.textAllBorders": "Visas apmales", "SSE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "SSE.Views.Toolbar.textAuto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAutoColor": "Automātisks", "SSE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "SSE.Views.Toolbar.textBlackHeart": "Black Heart Suit", "SSE.Views.Toolbar.textBold": "Treknraksts", "SSE.Views.Toolbar.textBordersColor": "Apmales krāsa", "SSE.Views.Toolbar.textBordersStyle": "Robežas stils", "SSE.Views.Toolbar.textBottom": "<PERSON><PERSON><PERSON> ", "SSE.Views.Toolbar.textBottomBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textBullet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textCellAlign": "Format cell alignment", "SSE.Views.Toolbar.textCenterBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vert<PERSON>", "SSE.Views.Toolbar.textClearPrintArea": "<PERSON><PERSON><PERSON><PERSON><PERSON> druk<PERSON> a<PERSON>", "SSE.Views.Toolbar.textClearRule": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textClockwise": "Izliekums pulksteņrādītāja virzienā", "SSE.Views.Toolbar.textColorScales": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.Toolbar.textCopyright": "Autortiesību <PERSON>", "SSE.Views.Toolbar.textCounterCw": "Izliekums pretēji pulksteņrādītāja virzienam", "SSE.Views.Toolbar.textCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textDataBars": "<PERSON><PERSON>", "SSE.Views.Toolbar.textDegree": "Degree Sign", "SSE.Views.Toolbar.textDelLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa k<PERSON>i", "SSE.Views.Toolbar.textDelPageBreak": "Remove page break", "SSE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "SSE.Views.Toolbar.textDelUp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz augšu", "SSE.Views.Toolbar.textDiagDownBorder": "<PERSON><PERSON><PERSON><PERSON><PERSON> le<PERSON> robe<PERSON>a", "SSE.Views.Toolbar.textDiagUpBorder": "<PERSON><PERSON><PERSON><PERSON> robe<PERSON>a uz augšu", "SSE.Views.Toolbar.textDivision": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textDollar": "Dollar Sign", "SSE.Views.Toolbar.textDone": "Gatavs", "SSE.Views.Toolbar.textDown": "Down", "SSE.Views.Toolbar.textEditVA": "Rediģēt red<PERSON>o a<PERSON>u", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON>u kolonnu", "SSE.Views.Toolbar.textEntireRow": "<PERSON>isu rindu", "SSE.Views.Toolbar.textEuro": "Euro Sign", "SSE.Views.Toolbar.textFewPages": "lapas", "SSE.Views.Toolbar.textFillLeft": "Left", "SSE.Views.Toolbar.textFillRight": "Right", "SSE.Views.Toolbar.textFormatCellFill": "Format cell fill", "SSE.Views.Toolbar.textGreaterEqual": "Lie<PERSON>ā<PERSON> par vai vienāds ar", "SSE.Views.Toolbar.textHeight": "Augstums", "SSE.Views.Toolbar.textHideVA": "Paslēpt <PERSON> a<PERSON>", "SSE.Views.Toolbar.textHorizontal": "<PERSON><PERSON><PERSON><PERSON> te<PERSON>", "SSE.Views.Toolbar.textInfinity": "Bezgalība", "SSE.Views.Toolbar.textInsDown": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz leju", "SSE.Views.Toolbar.textInsideBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textInsPageBreak": "Ievietot lapas p<PERSON>", "SSE.Views.Toolbar.textInsRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa labi", "SSE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textItems": "V<PERSON><PERSON>", "SSE.Views.Toolbar.textLandscape": "Ainava", "SSE.Views.Toolbar.textLeft": "Kreisais: ", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textLessEqual": "<PERSON><PERSON>āk nekā vai vienāds ar", "SSE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "SSE.Views.Toolbar.textManageRule": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>", "SSE.Views.Toolbar.textManyPages": "lapas", "SSE.Views.Toolbar.textMarginsLast": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsNarrow": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsNormal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsWide": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMiddleBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMoreBorders": "More borders", "SSE.Views.Toolbar.textMoreFormats": "<PERSON><PERSON><PERSON><PERSON>ātu", "SSE.Views.Toolbar.textMorePages": "<PERSON><PERSON><PERSON><PERSON>u", "SSE.Views.Toolbar.textMoreSymbols": "More symbols", "SSE.Views.Toolbar.textNewColor": "<PERSON><PERSON><PERSON> jau<PERSON>", "SSE.Views.Toolbar.textNewRule": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textNoBorders": "Nav a<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textNotEqualTo": "Nav vien<PERSON>ds ar", "SSE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "SSE.Views.Toolbar.textOnePage": "lapa", "SSE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "SSE.Views.Toolbar.textOutBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPageMarginsCustom": "Pielāgotas piemales", "SSE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "SSE.Views.Toolbar.textPortrait": "Portrets", "SSE.Views.Toolbar.textPrint": "Printēt", "SSE.Views.Toolbar.textPrintGridlines": "Druk<PERSON>t režģa līnijas", "SSE.Views.Toolbar.textPrintHeadings": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPrintOptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textRegistered": "Reģistrēta zīme", "SSE.Views.Toolbar.textResetPageBreak": "Reset all page breaks", "SSE.Views.Toolbar.textRight": "Labais: ", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textRotateDown": "Rot<PERSON><PERSON> te<PERSON> le<PERSON>", "SSE.Views.Toolbar.textRotateUp": "<PERSON><PERSON><PERSON><PERSON> te<PERSON> au<PERSON>", "SSE.Views.Toolbar.textRtlSheet": "Sheet Right-to-left", "SSE.Views.Toolbar.textScale": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textScaleCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSelection": "No pašreizējās atlases", "SSE.Views.Toolbar.textSeries": "Series", "SSE.Views.Toolbar.textSetPrintArea": "Iestatīt drukas a<PERSON>", "SSE.Views.Toolbar.textShapesCombine": "Combine", "SSE.Views.Toolbar.textShapesFragment": "Fragment", "SSE.Views.Toolbar.textShapesIntersect": "Intersect", "SSE.Views.Toolbar.textShapesSubstract": "Subtract", "SSE.Views.Toolbar.textShapesUnion": "Union", "SSE.Views.Toolbar.textShowVA": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "SSE.Views.Toolbar.textSmile": "White Smiling Face", "SSE.Views.Toolbar.textSquareRoot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textStrikeout": "Pārsvītrots", "SSE.Views.Toolbar.textSubscript": "Apakšraksts", "SSE.Views.Toolbar.textSubSuperscript": "Apakšraksts/augšraksts", "SSE.Views.Toolbar.textSuperscript": "Augšraksts", "SSE.Views.Toolbar.textTabCollaboration": "Sad<PERSON>bī<PERSON>", "SSE.Views.Toolbar.textTabData": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTabDraw": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFile": "Fails", "SSE.Views.Toolbar.textTabFormula": "Formula", "SSE.Views.Toolbar.textTabHome": "Sā<PERSON><PERSON>", "SSE.Views.Toolbar.textTabInsert": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabLayout": "Izklājums", "SSE.Views.Toolbar.textTabProtect": "Aizsardzība", "SSE.Views.Toolbar.textTabView": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textThisPivot": "No <PERSON>ī rakursa", "SSE.Views.Toolbar.textThisSheet": "<PERSON> <PERSON><PERSON>", "SSE.Views.Toolbar.textThisTable": "No šīs tabulas", "SSE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTop": "Augšā: ", "SSE.Views.Toolbar.textTopBorders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "SSE.Views.Toolbar.textUnderline": "Pasvītrot", "SSE.Views.Toolbar.textUp": "Up", "SSE.Views.Toolbar.textVertical": "<PERSON>ert<PERSON><PERSON><PERSON> teks<PERSON>", "SSE.Views.Toolbar.textWidth": "Platums", "SSE.Views.Toolbar.textYen": "Yen Sign", "SSE.Views.Toolbar.textZoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignBottom": "Līdzināt pie apakšas", "SSE.Views.Toolbar.tipAlignCenter": "L<PERSON><PERSON><PERSON>āt pa centru", "SSE.Views.Toolbar.tipAlignJust": "Taisnot<PERSON>", "SSE.Views.Toolbar.tipAlignLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa kreisi", "SSE.Views.Toolbar.tipAlignMiddle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz vidu", "SSE.Views.Toolbar.tipAlignRight": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pa labi", "SSE.Views.Toolbar.tipAlignTop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> uz augšu", "SSE.Views.Toolbar.tipAutofilter": "Šķirot un filtrēt", "SSE.Views.Toolbar.tipBack": "Atpakaļ", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCellStyle": "<PERSON><PERSON><PERSON> stils", "SSE.Views.Toolbar.tipChangeCase": "<PERSON><PERSON><PERSON> lielo/mazo burtu", "SSE.Views.Toolbar.tipChangeChart": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> diagrammas veidu", "SSE.Views.Toolbar.tipClearStyle": "Nodzēst visu", "SSE.Views.Toolbar.tipColorSchemas": "<PERSON><PERSON><PERSON> kr<PERSON> sh<PERSON>mu", "SSE.Views.Toolbar.tipCondFormat": "Nosacījuma <PERSON>", "SSE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON><PERSON> stilu", "SSE.Views.Toolbar.tipCut": "Izgriezt", "SSE.Views.Toolbar.tipDecDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDecFont": "<PERSON><PERSON><PERSON><PERSON><PERSON>u", "SSE.Views.Toolbar.tipDeleteOpt": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDigStyleAccounting": "Uzskaites stils", "SSE.Views.Toolbar.tipDigStyleComma": "Comma style", "SSE.Views.Toolbar.tipDigStyleCurrency": "Valū<PERSON> stils", "SSE.Views.Toolbar.tipDigStylePercent": "Pro<PERSON><PERSON> stils", "SSE.Views.Toolbar.tipEditChart": "Rediģēt diagrammu", "SSE.Views.Toolbar.tipEditChartData": "<PERSON><PERSON><PERSON> da<PERSON>", "SSE.Views.Toolbar.tipEditChartType": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> diagrammas veidu", "SSE.Views.Toolbar.tipEditHeader": "Rediģēt <PERSON><PERSON><PERSON><PERSON> vai Galveni", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipFontName": "Fonts", "SSE.Views.Toolbar.tipFontSize": "Fonta izmērs", "SSE.Views.Toolbar.tipHAlighOle": "Horizontā<PERSON>ā <PERSON>", "SSE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> obje<PERSON>us", "SSE.Views.Toolbar.tipImgGroup": "Grupas objekti", "SSE.Views.Toolbar.tipIncDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipIncFont": "<PERSON><PERSON><PERSON><PERSON><PERSON>u", "SSE.Views.Toolbar.tipInsertChart": "Ievietot grafiku", "SSE.Views.Toolbar.tipInsertChartRecommend": "Insert recommended chart", "SSE.Views.Toolbar.tipInsertChartSpark": "Ievietot grafiku", "SSE.Views.Toolbar.tipInsertEquation": "Ievietot vienā<PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertHorizontalText": "Ievietot horizontālu tekstlodziņu", "SSE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertImage": "<PERSON><PERSON><PERSON><PERSON> at<PERSON>", "SSE.Views.Toolbar.tipInsertOpt": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertShape": "Insert Autoshape", "SSE.Views.Toolbar.tipInsertSlicer": "Ievietot datu griezumu", "SSE.Views.Toolbar.tipInsertSmartArt": "Ievietot SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.Toolbar.tipInsertSymbol": "Ievietot simbolu", "SSE.Views.Toolbar.tipInsertTable": "Ievietot tabulu", "SSE.Views.Toolbar.tipInsertText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertTextart": "Ievietot Text Art objektu", "SSE.Views.Toolbar.tipInsertVerticalText": "Ievietot vertikālu tekstlodziņu", "SSE.Views.Toolbar.tipMerge": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipNone": "Neviens", "SSE.Views.Toolbar.tipNumFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON> form<PERSON>", "SSE.Views.Toolbar.tipPageBreak": "Add a break where you want the next page to begin in the printed copy", "SSE.Views.Toolbar.tipPageMargins": "Lapas malas", "SSE.Views.Toolbar.tipPageOrient": "Lapas orientācija", "SSE.Views.Toolbar.tipPageSize": "Lapas izmērs", "SSE.Views.Toolbar.tipPaste": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrColor": "<PERSON>z<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrint": "Printēt", "SSE.Views.Toolbar.tipPrintArea": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrintQuick": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrintTitles": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipReplace": "Replace", "SSE.Views.Toolbar.tipRtlSheet": "Switch the sheet direction so that the first column is on the right side", "SSE.Views.Toolbar.tipSave": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSaveCoauth": "Saglabājiet <PERSON>, lai tās redz<PERSON>tu citi lietotāji.", "SSE.Views.Toolbar.tipScale": "<PERSON><PERSON><PERSON><PERSON>, lai ietilptu", "SSE.Views.Toolbar.tipSelectAll": "Izvēlēties visu", "SSE.Views.Toolbar.tipSendBackward": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSendForward": "Pārnest uz priekšu", "SSE.Views.Toolbar.tipShapesMerge": "Merge shapes", "SSE.Views.Toolbar.tipSynchronize": "Dokumentu mainīja cits lietotājs. Noklikšķiniet, lai saglabātu izmaiņas un atkārtoti ielādētu atjauninājumus.", "SSE.Views.Toolbar.tipTextFormatting": "Vairāk teksta formatēšanas rīku", "SSE.Views.Toolbar.tipTextOrientation": "Orientācija", "SSE.Views.Toolbar.tipUndo": "Atsaukt", "SSE.Views.Toolbar.tipVAlighOle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipVisibleArea": "Redzamais apgabals", "SSE.Views.Toolbar.tipWrap": "Aplauzt tekstu", "SSE.Views.Toolbar.txtAccounting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAdditional": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtAscending": "<PERSON><PERSON><PERSON><PERSON> secībā", "SSE.Views.Toolbar.txtAutosumTip": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCellStyle": "<PERSON><PERSON><PERSON> stils", "SSE.Views.Toolbar.txtClearAll": "Visi", "SSE.Views.Toolbar.txtClearComments": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFilter": "<PERSON><PERSON><PERSON><PERSON><PERSON> filtru", "SSE.Views.Toolbar.txtClearFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFormula": "Funkcija", "SSE.Views.Toolbar.txtClearHyper": "Hipersait<PERSON>", "SSE.Views.Toolbar.txtClearText": "Teksts", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDate": "Datums", "SSE.Views.Toolbar.txtDateLong": "<PERSON><PERSON><PERSON> datuma formāts", "SSE.Views.Toolbar.txtDateShort": "Īss datums", "SSE.Views.Toolbar.txtDateTime": "Datums un Laiks", "SSE.Views.Toolbar.txtDescending": "<PERSON><PERSON><PERSON><PERSON><PERSON> secībā", "SSE.Views.Toolbar.txtDollar": "$ dolārs", "SSE.Views.Toolbar.txtEuro": "€ Eiro", "SSE.Views.Toolbar.txtExp": "Eksponen<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtFillNum": "Fill", "SSE.Views.Toolbar.txtFilter": "Filtrs", "SSE.Views.Toolbar.txtFormula": "<PERSON>ev<PERSON><PERSON>", "SSE.Views.Toolbar.txtFraction": "Daļskaitlis", "SSE.Views.Toolbar.txtFranc": "CHF Šveices franks", "SSE.Views.Toolbar.txtGeneral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtInteger": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtManageRange": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtMergeAcross": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pāri", "SSE.Views.Toolbar.txtMergeCells": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtMergeCenter": "Sa<PERSON><PERSON>dināt pa centru", "SSE.Views.Toolbar.txtNamedRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtNewRange": "Definēt no<PERSON>ukumu", "SSE.Views.Toolbar.txtNoBorders": "Nav a<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtNumber": "Skaits", "SSE.Views.Toolbar.txtPasteRange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vā<PERSON>u", "SSE.Views.Toolbar.txtPercentage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtPound": "£ mārciņa", "SSE.Views.Toolbar.txtRouble": "₽ <PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtScientific": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSearch": "Meklēt", "SSE.Views.Toolbar.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSortAZ": "<PERSON><PERSON><PERSON><PERSON> augošā secībā", "SSE.Views.Toolbar.txtSortZA": "<PERSON><PERSON><PERSON><PERSON> dilstoš<PERSON> secībā", "SSE.Views.Toolbar.txtSpecial": "Īpašie", "SSE.Views.Toolbar.txtTableTemplate": "Formatēt kā tabulas veidni", "SSE.Views.Toolbar.txtText": "Teksts", "SSE.Views.Toolbar.txtTime": "Laiks", "SSE.Views.Toolbar.txtUnmerge": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtYen": "¥ jena", "SSE.Views.Top10FilterDialog.textType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBottom": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBy": "pēc", "SSE.Views.Top10FilterDialog.txtItems": "Pozīcija", "SSE.Views.Top10FilterDialog.txtPercent": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtSum": "Summa", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 automātiskais filtrs", "SSE.Views.Top10FilterDialog.txtTop": "<PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtValueTitle": "Top 10 filtrs", "SSE.Views.ValueFieldSettingsDialog.textNext": "(tāl<PERSON>k)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "<PERSON><PERSON><PERSON><PERSON><PERSON> form<PERSON>", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(iepriekš<PERSON><PERSON><PERSON>)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> lauka i<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "<PERSON><PERSON> lauks", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Pamata vienība", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 no %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Skaits", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Atšķirība no", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Indekss", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Ma<PERSON>.", "SSE.Views.ValueFieldSettingsDialog.txtMin": "<PERSON>.", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "Nav aprēķina", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "% no", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "% difference from", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "% of column", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% no kopējās summas", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% no vecāka kopsummas", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% no vecākkolonnas kopsummas", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% no vecāka rindas kopsummas", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% darbojas kop<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "% of row", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Produkts", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Rangs no mazākā līdz <PERSON>m", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Rangs no lielākā līdz ma<PERSON>m", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Rezultāts šeit:", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "<PERSON><PERSON><PERSON><PERSON><PERSON> vē<PERSON><PERSON><PERSON> kā", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Avota nosaukums:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Summa", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Apkopot vērtību lauku pēc", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Daž.", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "Aizvērt", "SSE.Views.ViewManagerDlg.guestText": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.lockText": "Bloķēta", "SSE.Views.ViewManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textDuplicate": "Dub<PERSON>āts", "SSE.Views.ViewManagerDlg.textEmpty": "Vēl nav izveidots neviens skats.", "SSE.Views.ViewManagerDlg.textGoTo": "Doties uz skatu", "SSE.Views.ViewManagerDlg.textLongName": "<PERSON><PERSON><PERSON><PERSON>, kas ir ma<PERSON> par 128 rakstzīm<PERSON>m.", "SSE.Views.ViewManagerDlg.textNew": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textRename": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textRenameError": "Skata nosaukuma lauks nedr<PERSON>t būt tuk<PERSON>.", "SSE.Views.ViewManagerDlg.textRenameLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> skatu", "SSE.Views.ViewManagerDlg.textViews": "Lapas skati", "SSE.Views.ViewManagerDlg.tipIsLocked": "Šo elementu lieto cits lietotājs.", "SSE.Views.ViewManagerDlg.txtTitle": "Lapu skata pārvaldnieks", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Are you sure you want to delete this sheet view?", "SSE.Views.ViewManagerDlg.warnDeleteView": "Jūs mēģināt izdzēst pašlaik iespējoto skatu '%1'.<br>Vai aizvērt šo skatu un izdzēst to?", "SSE.Views.ViewTab.capBtnFreeze": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.ViewTab.capBtnSheetView": "Lapas skats", "SSE.Views.ViewTab.textAlwaysShowToolbar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textClose": "Aizvērt", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "<PERSON><PERSON><PERSON><PERSON> lapu un statusu joslas", "SSE.Views.ViewTab.textCreate": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textDefault": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textFill": "Fill", "SSE.Views.ViewTab.textFormula": "Formulas bārs", "SSE.Views.ViewTab.textFreezeCol": "<PERSON><PERSON><PERSON><PERSON><PERSON> pirmo kolonnu", "SSE.Views.ViewTab.textFreezeRow": "<PERSON><PERSON><PERSON><PERSON><PERSON> aug<PERSON><PERSON><PERSON> rindu", "SSE.Views.ViewTab.textGridlines": "Režģlīnijas", "SSE.Views.ViewTab.textHeadings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textInterfaceTheme": "Interfeisa tēma", "SSE.Views.ViewTab.textLeftMenu": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textLine": "Line", "SSE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textRightMenu": "<PERSON><PERSON> panelis", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "<PERSON><PERSON><PERSON><PERSON><PERSON> iesaldētu rūšu ēnu", "SSE.Views.ViewTab.textTabStyle": "Tab style", "SSE.Views.ViewTab.textUnFreeze": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.ViewTab.textZeros": "<PERSON><PERSON><PERSON><PERSON><PERSON> nulles", "SSE.Views.ViewTab.textZoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.tipClose": "<PERSON><PERSON><PERSON><PERSON><PERSON> lapas skatu", "SSE.Views.ViewTab.tipCreate": "Izveidot lapas skatu", "SSE.Views.ViewTab.tipFreeze": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.ViewTab.tipInterfaceTheme": "Interfeisa tēma", "SSE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipSheetView": "Lapas skats", "SSE.Views.ViewTab.tipViewNormal": "See your document in Normal view", "SSE.Views.ViewTab.tipViewPageBreak": "See where the page breaks will appear when your document is printed", "SSE.Views.ViewTab.txtViewNormal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.txtViewPageBreak": "Page Break Preview", "SSE.Views.WatchDialog.closeButtonText": "Aizvērt", "SSE.Views.WatchDialog.textAdd": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textBook": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textCell": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textDeleteAll": "Izdzēst visu", "SSE.Views.WatchDialog.textFormula": "Formula", "SSE.Views.WatchDialog.textName": "Nosa<PERSON>ms", "SSE.Views.WatchDialog.textSheet": "<PERSON><PERSON>", "SSE.Views.WatchDialog.textValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.txtTitle": "Uzraudzības logs", "SSE.Views.WBProtection.hintAllowRanges": "Atļaut rediģēt diapazonus", "SSE.Views.WBProtection.hintProtectRange": "Protect range", "SSE.Views.WBProtection.hintProtectSheet": "Lapas aizsardzība", "SSE.Views.WBProtection.hintProtectWB": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtAllowRanges": "Atļaut rediģēt diapazonus", "SSE.Views.WBProtection.txtHiddenFormula": "Slēptas formulas", "SSE.Views.WBProtection.txtLockedCell": "Bloķēta šūna", "SSE.Views.WBProtection.txtLockedShape": "Forma ir bloķēta", "SSE.Views.WBProtection.txtLockedText": "Bloķēt tekstu", "SSE.Views.WBProtection.txtProtectRange": "Protect Range", "SSE.Views.WBProtection.txtProtectSheet": "Lapas aizsardzība", "SSE.Views.WBProtection.txtProtectWB": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Ievadiet paroli, lai noņem<PERSON> lapas a<PERSON>sardzību", "SSE.Views.WBProtection.txtSheetUnlockTitle": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "SSE.Views.WBProtection.txtWBUnlockDescription": "<PERSON><PERSON><PERSON><PERSON> par<PERSON>, lai no<PERSON><PERSON> da<PERSON>r<PERSON><PERSON> a<PERSON>", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}