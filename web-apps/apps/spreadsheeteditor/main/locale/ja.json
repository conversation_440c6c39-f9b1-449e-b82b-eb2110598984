{"cancelButtonText": "キャンセル", "Common.Controllers.Chat.notcriticalErrorTitle": "警告", "Common.Controllers.Desktop.hintBtnHome": "メインウィンドウを表示する", "Common.Controllers.Desktop.itemCreateFromTemplate": "テンプレートから作成", "Common.Controllers.History.notcriticalErrorTitle": "警告", "Common.Controllers.History.txtErrorLoadHistory": "履歴の読み込みに失敗しました", "Common.Controllers.Plugins.helpUseMacros": "「マクロ」ボタンはここに移動しました", "Common.Controllers.Plugins.helpUseMacrosHeader": "マクロへのアクセスを更新しました", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "プラグインは正常にインストールされました。すべてのバックグラウンドプラグインは、ここにアクセスできます。", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b>は正常にインストールされました。すべてのバックグラウンドプラグインは、ここにアクセスできます。", "Common.Controllers.Plugins.textRunInstalledPlugins": "インストールされたプラグインの実行", "Common.Controllers.Plugins.textRunPlugin": "プラグインの実行", "Common.define.chartData.textArea": "面グラフ", "Common.define.chartData.textAreaStacked": "積み上げ面", "Common.define.chartData.textAreaStackedPer": "100% 積み上げ面", "Common.define.chartData.textBar": "横棒グラフ", "Common.define.chartData.textBarNormal": "集合縦棒", "Common.define.chartData.textBarNormal3d": "3-D 集合縦棒", "Common.define.chartData.textBarNormal3dPerspective": "3-D 縦棒", "Common.define.chartData.textBarStacked": "積み上げ縦棒", "Common.define.chartData.textBarStacked3d": "3-D 積み上げ縦棒", "Common.define.chartData.textBarStackedPer": "100% 積み上げ縦棒", "Common.define.chartData.textBarStackedPer3d": "3-D 100% 積み上げ縦棒", "Common.define.chartData.textCharts": "グラフ", "Common.define.chartData.textColumn": "縦棒グラフ", "Common.define.chartData.textColumnSpark": "縦棒グラフ", "Common.define.chartData.textCombo": "複合", "Common.define.chartData.textComboAreaBar": "積み上げ面 - 集合縦棒", "Common.define.chartData.textComboBarLine": "集合縦棒 - 線", "Common.define.chartData.textComboBarLineSecondary": "集合縦棒 - 第2軸の折れ線", "Common.define.chartData.textComboCustom": "組み合わせ", "Common.define.chartData.textDoughnut": "ドーナツ", "Common.define.chartData.textHBarNormal": "集合横棒", "Common.define.chartData.textHBarNormal3d": "3-D 集合横棒", "Common.define.chartData.textHBarStacked": "積み上げ横棒", "Common.define.chartData.textHBarStacked3d": "3-D 積み上げ横棒", "Common.define.chartData.textHBarStackedPer": "100%積み上げ横棒", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% 積み上げ横棒", "Common.define.chartData.textLine": "グラフ", "Common.define.chartData.textLine3d": "3-D 折れ線", "Common.define.chartData.textLineMarker": "マーカー付き折れ線", "Common.define.chartData.textLineSpark": "グラフ", "Common.define.chartData.textLineStacked": "積み上げ折れ線", "Common.define.chartData.textLineStackedMarker": "マーク付き積み上げ折れ線", "Common.define.chartData.textLineStackedPer": "100% 積み上げ折れ線", "Common.define.chartData.textLineStackedPerMarker": "マーカー付き 100% 積み上げ折れ線", "Common.define.chartData.textPie": "円グラフ", "Common.define.chartData.textPie3d": "3-D 円グラフ", "Common.define.chartData.textPoint": "XY (散布図)", "Common.define.chartData.textRadar": "レーダーチャート", "Common.define.chartData.textRadarFilled": "塗りつぶしレーダー", "Common.define.chartData.textRadarMarker": "マーカー付きレーダー", "Common.define.chartData.textScatter": "散布図", "Common.define.chartData.textScatterLine": "直線付き散布図", "Common.define.chartData.textScatterLineMarker": "マーカーと直線付き散布図", "Common.define.chartData.textScatterSmooth": "平滑線付き散布図", "Common.define.chartData.textScatterSmoothMarker": "マーカーと平滑線付き散布図", "Common.define.chartData.textSparks": "スパークライン", "Common.define.chartData.textStock": "株価グラフ", "Common.define.chartData.textSurface": "表面", "Common.define.chartData.textWinLossSpark": "勝ち/負け", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "書式設定しない", "Common.define.conditionalData.text1Above": "より 1 標準偏差上", "Common.define.conditionalData.text1Below": "より 1 標準偏差下", "Common.define.conditionalData.text2Above": "より 2 標準偏差上", "Common.define.conditionalData.text2Below": "より 2 標準偏差下", "Common.define.conditionalData.text3Above": "より 3 標準偏差上", "Common.define.conditionalData.text3Below": "より 3 標準偏差下", "Common.define.conditionalData.textAbove": "上に", "Common.define.conditionalData.textAverage": "平均", "Common.define.conditionalData.textBegins": "で始まる", "Common.define.conditionalData.textBelow": "下に", "Common.define.conditionalData.textBetween": "間", "Common.define.conditionalData.textBlank": "空白", "Common.define.conditionalData.textBlanks": "空白を含んでいる", "Common.define.conditionalData.textBottom": "最低", "Common.define.conditionalData.textContains": "含んでいる", "Common.define.conditionalData.textDataBar": "データ バー", "Common.define.conditionalData.textDate": "日付", "Common.define.conditionalData.textDuplicate": "コピー", "Common.define.conditionalData.textEnds": "終了", "Common.define.conditionalData.textEqAbove": "次の値に等しいまたは以上", "Common.define.conditionalData.textEqBelow": "次の値に等しいまたは以下", "Common.define.conditionalData.textEqual": "次の値に等しい", "Common.define.conditionalData.textError": "エラー", "Common.define.conditionalData.textErrors": "エラーを含んでいる", "Common.define.conditionalData.textFormula": "数式", "Common.define.conditionalData.textGreater": "次の値より大きい", "Common.define.conditionalData.textGreaterEq": "次の値より大きいか等しい", "Common.define.conditionalData.textIconSets": "アイコン​​セット", "Common.define.conditionalData.textLast7days": "過去7日以内", "Common.define.conditionalData.textLastMonth": "先月", "Common.define.conditionalData.textLastWeek": "先週", "Common.define.conditionalData.textLess": "次の値より小さい", "Common.define.conditionalData.textLessEq": "以下か等号", "Common.define.conditionalData.textNextMonth": "来月", "Common.define.conditionalData.textNextWeek": "来週", "Common.define.conditionalData.textNotBetween": "間ではない", "Common.define.conditionalData.textNotBlanks": "空白を含んでいない", "Common.define.conditionalData.textNotContains": "含んでいない", "Common.define.conditionalData.textNotEqual": "と等しくない", "Common.define.conditionalData.textNotErrors": "エラーを含んでいない", "Common.define.conditionalData.textText": "テキスト", "Common.define.conditionalData.textThisMonth": "今月", "Common.define.conditionalData.textThisWeek": "今週", "Common.define.conditionalData.textToday": "今日", "Common.define.conditionalData.textTomorrow": "明日", "Common.define.conditionalData.textTop": "トップ", "Common.define.conditionalData.textUnique": "一意", "Common.define.conditionalData.textValue": "値が", "Common.define.conditionalData.textYesterday": "昨日", "Common.define.smartArt.textAccentedPicture": "アクセント付きの図", "Common.define.smartArt.textAccentProcess": "アクセントプロセス", "Common.define.smartArt.textAlternatingFlow": "波型ステップ", "Common.define.smartArt.textAlternatingHexagons": "左右交替積み上げ六角形", "Common.define.smartArt.textAlternatingPictureBlocks": "左右交替積み上げ画像ブロック", "Common.define.smartArt.textAlternatingPictureCircles": "円形付き画像ジグザグ表示", "Common.define.smartArt.textArchitectureLayout": "アーキテクチャ レイアウト", "Common.define.smartArt.textArrowRibbon": "リボン状の矢印", "Common.define.smartArt.textAscendingPictureAccentProcess": "アクセント画像付き上昇ステップ", "Common.define.smartArt.textBalance": "バランス", "Common.define.smartArt.textBasicBendingProcess": "基本蛇行ステップ", "Common.define.smartArt.textBasicBlockList": "カード型リスト", "Common.define.smartArt.textBasicChevronProcess": "プロセス", "Common.define.smartArt.textBasicCycle": "基本の循環", "Common.define.smartArt.textBasicMatrix": "基本マトリックス", "Common.define.smartArt.textBasicPie": "円グラフ", "Common.define.smartArt.textBasicProcess": "基本ステップ", "Common.define.smartArt.textBasicPyramid": "基本ピラミッド", "Common.define.smartArt.textBasicRadial": "基本放射", "Common.define.smartArt.textBasicTarget": "ターゲット", "Common.define.smartArt.textBasicTimeline": "タイムライン", "Common.define.smartArt.textBasicVenn": "基本ベン図", "Common.define.smartArt.textBendingPictureAccentList": "画像付きカード型リスト", "Common.define.smartArt.textBendingPictureBlocks": "自動配置の画像ブロック", "Common.define.smartArt.textBendingPictureCaption": "自動配置の表題付き画像", "Common.define.smartArt.textBendingPictureCaptionList": "自動配置の表題付き画像レイアウト", "Common.define.smartArt.textBendingPictureSemiTranparentText": "自動配置の半透明テキスト付き画像", "Common.define.smartArt.textBlockCycle": "ボックス循環", "Common.define.smartArt.textBubblePictureList": "バブル状画像リスト", "Common.define.smartArt.textCaptionedPictures": "表題付き画像", "Common.define.smartArt.textChevronAccentProcess": "アクセントステップ", "Common.define.smartArt.textChevronList": "プロセス リスト", "Common.define.smartArt.textCircleAccentTimeline": "円形組み合わせタイムライン", "Common.define.smartArt.textCircleArrowProcess": "円形矢印プロセス", "Common.define.smartArt.textCirclePictureHierarchy": "円形画像を使用した階層", "Common.define.smartArt.textCircleProcess": "円形プロセス", "Common.define.smartArt.textCircleRelationship": "円の関連付け", "Common.define.smartArt.textCircularBendingProcess": "円形蛇行ステップ", "Common.define.smartArt.textCircularPictureCallout": "円形画像を使った吹き出し", "Common.define.smartArt.textClosedChevronProcess": "開始点強調型プロセス", "Common.define.smartArt.textContinuousArrowProcess": "大きな矢印のプロセス", "Common.define.smartArt.textContinuousBlockProcess": "矢印と長方形のプロセス", "Common.define.smartArt.textContinuousCycle": "連続性強調循環", "Common.define.smartArt.textContinuousPictureList": "矢印付き画像リスト", "Common.define.smartArt.textConvergingArrows": "内向き矢印", "Common.define.smartArt.textConvergingRadial": "収束ラジアル", "Common.define.smartArt.textConvergingText": "内向きテキスト", "Common.define.smartArt.textCounterbalanceArrows": "対立とバランスの矢印", "Common.define.smartArt.textCycle": "循環", "Common.define.smartArt.textCycleMatrix": "循環マトリックス", "Common.define.smartArt.textDescendingBlockList": "ブロックの降順リスト", "Common.define.smartArt.textDescendingProcess": "降順プロセス", "Common.define.smartArt.textDetailedProcess": "詳述プロセス", "Common.define.smartArt.textDivergingArrows": "左右逆方向矢印", "Common.define.smartArt.textDivergingRadial": "矢印付き放射", "Common.define.smartArt.textEquation": "数式", "Common.define.smartArt.textFramedTextPicture": "フレームに表示されるテキスト画像", "Common.define.smartArt.textFunnel": "漏斗", "Common.define.smartArt.textGear": "歯車", "Common.define.smartArt.textGridMatrix": "グリッド マトリックス", "Common.define.smartArt.textGroupedList": "グループ リスト", "Common.define.smartArt.textHalfCircleOrganizationChart": "アーチ型線で飾られた組織図", "Common.define.smartArt.textHexagonCluster": "蜂の巣状の六角形", "Common.define.smartArt.textHexagonRadial": "六角形放射", "Common.define.smartArt.textHierarchy": "階層", "Common.define.smartArt.textHierarchyList": "階層リスト", "Common.define.smartArt.textHorizontalBulletList": "横方向箇条書きリスト", "Common.define.smartArt.textHorizontalHierarchy": "横方向階層", "Common.define.smartArt.textHorizontalLabeledHierarchy": "ラベル付き横方向階層", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "複数レベル対応の横方向階層", "Common.define.smartArt.textHorizontalOrganizationChart": "水平方向の組織図", "Common.define.smartArt.textHorizontalPictureList": "横方向画像リスト", "Common.define.smartArt.textIncreasingArrowProcess": "上昇矢印のプロセス", "Common.define.smartArt.textIncreasingCircleProcess": "上昇円プロセス", "Common.define.smartArt.textInterconnectedBlockProcess": "相互接続された長方形のプロセス", "Common.define.smartArt.textInterconnectedRings": "互いにつながったリング", "Common.define.smartArt.textInvertedPyramid": "反転ピラミッド", "Common.define.smartArt.textLabeledHierarchy": "ラベル付き階層", "Common.define.smartArt.textLinearVenn": "横方向ベン図", "Common.define.smartArt.textLinedList": "線区切りリスト", "Common.define.smartArt.textList": "リスト", "Common.define.smartArt.textMatrix": "マトリックス", "Common.define.smartArt.textMultidirectionalCycle": "双方向循環", "Common.define.smartArt.textNameAndTitleOrganizationChart": "氏名/役職名付き組織図", "Common.define.smartArt.textNestedTarget": "包含", "Common.define.smartArt.textNondirectionalCycle": "矢印無し循環", "Common.define.smartArt.textOpposingArrows": "上下逆方向矢印", "Common.define.smartArt.textOpposingIdeas": "対立する案", "Common.define.smartArt.textOrganizationChart": "組織図", "Common.define.smartArt.textOther": "その他", "Common.define.smartArt.textPhasedProcess": "フェーズ プロセス", "Common.define.smartArt.textPicture": "画像", "Common.define.smartArt.textPictureAccentBlocks": "画像アクセントのブロック", "Common.define.smartArt.textPictureAccentList": "画像アクセントのリスト", "Common.define.smartArt.textPictureAccentProcess": "画像アクセントのプロセス", "Common.define.smartArt.textPictureCaptionList": "画像キャプションのリスト", "Common.define.smartArt.textPictureFrame": "フォトフレーム", "Common.define.smartArt.textPictureGrid": "画像グリッド", "Common.define.smartArt.textPictureLineup": "画像ラインアップ", "Common.define.smartArt.textPictureOrganizationChart": "画像付き組織図", "Common.define.smartArt.textPictureStrips": "画像付きラベル", "Common.define.smartArt.textPieProcess": "円グラフのプロセス", "Common.define.smartArt.textPlusAndMinus": "プラスとマイナス", "Common.define.smartArt.textProcess": "プロセス", "Common.define.smartArt.textProcessArrows": "矢印型ステップ", "Common.define.smartArt.textProcessList": "プロセスのリスト", "Common.define.smartArt.textPyramid": "ピラミッド", "Common.define.smartArt.textPyramidList": "ピラミッドのリスト", "Common.define.smartArt.textRadialCluster": "放射ブロック", "Common.define.smartArt.textRadialCycle": "中心付き循環", "Common.define.smartArt.textRadialList": "放射リスト", "Common.define.smartArt.textRadialPictureList": "放射画像リスト", "Common.define.smartArt.textRadialVenn": "放射型ベン図", "Common.define.smartArt.textRandomToResultProcess": "複数案をまとめるステップ", "Common.define.smartArt.textRelationship": "関係", "Common.define.smartArt.textRepeatingBendingProcess": "改行型蛇行ステップ", "Common.define.smartArt.textReverseList": "逆順リスト", "Common.define.smartArt.textSegmentedCycle": "円型循環", "Common.define.smartArt.textSegmentedProcess": "分割ステップ", "Common.define.smartArt.textSegmentedPyramid": "分割ピラミッド", "Common.define.smartArt.textSnapshotPictureList": "スナップショット画像リスト", "Common.define.smartArt.textSpiralPicture": "渦巻き画像", "Common.define.smartArt.textSquareAccentList": "箇条書き記号アクセントのリスト", "Common.define.smartArt.textStackedList": "積み上げリスト", "Common.define.smartArt.textStackedVenn": "包含型ベン図", "Common.define.smartArt.textStaggeredProcess": "段違いステップ", "Common.define.smartArt.textStepDownProcess": "ステップ ダウンのプロセス", "Common.define.smartArt.textStepUpProcess": "ステップアップのプロセス", "Common.define.smartArt.textSubStepProcess": "サブステップのプロセス", "Common.define.smartArt.textTabbedArc": "円弧状タブ", "Common.define.smartArt.textTableHierarchy": "積み木型の階層", "Common.define.smartArt.textTableList": "表型リスト", "Common.define.smartArt.textTabList": "タブ付きリスト", "Common.define.smartArt.textTargetList": "ターゲットのリスト", "Common.define.smartArt.textTextCycle": "テキスト循環", "Common.define.smartArt.textThemePictureAccent": "テーマ画像アクセント", "Common.define.smartArt.textThemePictureAlternatingAccent": "テーマ画像交互のアクセント", "Common.define.smartArt.textThemePictureGrid": "テーマ画像グリッド", "Common.define.smartArt.textTitledMatrix": "タイトル付きマトリックス", "Common.define.smartArt.textTitledPictureAccentList": "画像付き横方向リスト", "Common.define.smartArt.textTitledPictureBlocks": "タイトル付き画像ブロック", "Common.define.smartArt.textTitlePictureLineup": "タイトル付き画像ラインアップ", "Common.define.smartArt.textTrapezoidList": "台形リスト", "Common.define.smartArt.textUpwardArrow": "上向き矢印", "Common.define.smartArt.textVaryingWidthList": "可変幅リスト", "Common.define.smartArt.textVerticalAccentList": "縦方向アクセントのリスト", "Common.define.smartArt.textVerticalArrowList": "縦方向矢印リスト", "Common.define.smartArt.textVerticalBendingProcess": "縦型蛇行ステップ", "Common.define.smartArt.textVerticalBlockList": "縦方向ボックス リスト", "Common.define.smartArt.textVerticalBoxList": "縦方向リスト", "Common.define.smartArt.textVerticalBracketList": "縦方向ブラケット リスト", "Common.define.smartArt.textVerticalBulletList": "縦方向箇条書きリスト", "Common.define.smartArt.textVerticalChevronList": "縦方向プロセス", "Common.define.smartArt.textVerticalCircleList": "縦方向円リスト", "Common.define.smartArt.textVerticalCurvedList": "縦方向カーブのリスト", "Common.define.smartArt.textVerticalEquation": "縦型の数式", "Common.define.smartArt.textVerticalPictureAccentList": "縦方向円形画像リスト", "Common.define.smartArt.textVerticalPictureList": "縦方向画像リスト", "Common.define.smartArt.textVerticalProcess": "縦方向ステップ", "Common.Translation.textMoreButton": "もっと", "Common.Translation.tipFileLocked": "ドキュメントが編集用にロックされています。後で変更し、ローカルコピーとして保存することができます。", "Common.Translation.tipFileReadOnly": "このファイルは読み取り専用です。変更内容を保持するには、新しい名前または別の場所にファイルを保存してください。", "Common.Translation.warnFileLocked": "文書が他のアプリで編集されています。編集を続けて、コピーとして保存できます。", "Common.Translation.warnFileLockedBtnEdit": "コピーを作成する", "Common.Translation.warnFileLockedBtnView": "見に開く", "Common.UI.ButtonColored.textAutoColor": "自動", "Common.UI.ButtonColored.textEyedropper": "スポイト", "Common.UI.ButtonColored.textNewColor": "その他の色", "Common.UI.ComboBorderSize.txtNoBorders": "枠線なし", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "枠線なし", "Common.UI.ComboDataView.emptyComboText": "スタイルなし", "Common.UI.ExtendedColorDialog.addButtonText": "追加", "Common.UI.ExtendedColorDialog.textCurrent": "現在", "Common.UI.ExtendedColorDialog.textHexErr": "入力された値が正しくありません。<br>000000〜FFFFFFの数値を入力してください。", "Common.UI.ExtendedColorDialog.textNew": "新しい", "Common.UI.ExtendedColorDialog.textRGBErr": "入力された値が正しくありません。<br>0〜255の数値を入力してください。", "Common.UI.HSBColorPicker.textNoColor": "色なし", "Common.UI.InputField.txtEmpty": "このフィールドは必須です", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "パスワードを表示しない", "Common.UI.InputFieldBtnPassword.textHintHold": "長押しでパスワード表示", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "パスワードを表示する", "Common.UI.SearchBar.textFind": "検索する", "Common.UI.SearchBar.tipCloseSearch": "検索を閉じる", "Common.UI.SearchBar.tipNextResult": "次の結果", "Common.UI.SearchBar.tipOpenAdvancedSettings": "詳細設定を開く", "Common.UI.SearchBar.tipPreviousResult": "前の結果", "Common.UI.SearchDialog.textHighlight": "結果を強調表示", "Common.UI.SearchDialog.textMatchCase": "大文字と小文字の区別", "Common.UI.SearchDialog.textReplaceDef": "代替テキストを入力してください", "Common.UI.SearchDialog.textSearchStart": "ここでテキストを挿入してください。", "Common.UI.SearchDialog.textTitle": "検索と置換", "Common.UI.SearchDialog.textTitle2": "検索", "Common.UI.SearchDialog.textWholeWords": "単語全体", "Common.UI.SearchDialog.txtBtnHideReplace": "変更を表示しない", "Common.UI.SearchDialog.txtBtnReplace": "置き換え", "Common.UI.SearchDialog.txtBtnReplaceAll": "全ての置き換え", "Common.UI.SynchronizeTip.textDontShow": "今後このメッセージを表示しない", "Common.UI.SynchronizeTip.textGotIt": "OK", "Common.UI.SynchronizeTip.textSynchronize": "ドキュメントは他のユーザーによって変更されました。<br>変更を保存するためにここでクリックし、アップデートを再ロードしてください。", "Common.UI.ThemeColorPalette.textRecentColors": "最近使った色", "Common.UI.ThemeColorPalette.textStandartColors": "標準色", "Common.UI.ThemeColorPalette.textThemeColors": "テーマの色", "Common.UI.Themes.txtThemeClassicLight": "明るい(クラシック)", "Common.UI.Themes.txtThemeContrastDark": "ダークコントラスト", "Common.UI.Themes.txtThemeDark": "暗い", "Common.UI.Themes.txtThemeGray": "灰色", "Common.UI.Themes.txtThemeLight": "明るい", "Common.UI.Themes.txtThemeSystem": "システム設定と同じ", "Common.UI.Window.cancelButtonText": "キャンセル", "Common.UI.Window.closeButtonText": "閉じる", "Common.UI.Window.noButtonText": "いいえ", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "確認", "Common.UI.Window.textDontShow": "今後このメッセージを表示しない", "Common.UI.Window.textError": "エラー", "Common.UI.Window.textInformation": "情報", "Common.UI.Window.textWarning": " 警告", "Common.UI.Window.yesButtonText": "はい", "Common.Utils.Metric.txtCm": "センチ", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": "、", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "アクセント", "Common.Utils.ThemeColor.txtAqua": "水色", "Common.Utils.ThemeColor.txtbackground": "背景", "Common.Utils.ThemeColor.txtBlack": "黒色", "Common.Utils.ThemeColor.txtBlue": "青色", "Common.Utils.ThemeColor.txtBrightGreen": "明るい緑", "Common.Utils.ThemeColor.txtBrown": "茶色", "Common.Utils.ThemeColor.txtDarkBlue": "濃い青色", "Common.Utils.ThemeColor.txtDarker": "より濃い", "Common.Utils.ThemeColor.txtDarkGray": "濃い灰色", "Common.Utils.ThemeColor.txtDarkGreen": "濃い緑色", "Common.Utils.ThemeColor.txtDarkPurple": "濃い紫色", "Common.Utils.ThemeColor.txtDarkRed": "濃い赤色", "Common.Utils.ThemeColor.txtDarkTeal": "濃い青緑色", "Common.Utils.ThemeColor.txtDarkYellow": "濃い黄色", "Common.Utils.ThemeColor.txtGold": "金色", "Common.Utils.ThemeColor.txtGray": "灰色", "Common.Utils.ThemeColor.txtGreen": "緑色", "Common.Utils.ThemeColor.txtIndigo": "インディゴ", "Common.Utils.ThemeColor.txtLavender": "ラベンダー", "Common.Utils.ThemeColor.txtLightBlue": "明るい青色", "Common.Utils.ThemeColor.txtLighter": "より明るい", "Common.Utils.ThemeColor.txtLightGray": "明るい灰色", "Common.Utils.ThemeColor.txtLightGreen": "明るい緑色", "Common.Utils.ThemeColor.txtLightOrange": "明るいオレンジ色", "Common.Utils.ThemeColor.txtLightYellow": "明るい黄色", "Common.Utils.ThemeColor.txtOrange": "オレンジ色", "Common.Utils.ThemeColor.txtPink": "ピンク色", "Common.Utils.ThemeColor.txtPurple": "紫色", "Common.Utils.ThemeColor.txtRed": "赤色", "Common.Utils.ThemeColor.txtRose": "ローズ色", "Common.Utils.ThemeColor.txtSkyBlue": "スカイブルー色", "Common.Utils.ThemeColor.txtTeal": "青緑色", "Common.Utils.ThemeColor.txttext": "テキスト", "Common.Utils.ThemeColor.txtTurquosie": "ターコイズ色", "Common.Utils.ThemeColor.txtViolet": "バイオレット色", "Common.Utils.ThemeColor.txtWhite": "白色", "Common.Utils.ThemeColor.txtYellow": "黄色", "Common.Views.About.txtAddress": "アドレス：", "Common.Views.About.txtLicensee": "ライセンス所有者", "Common.Views.About.txtLicensor": "ライセンサー", "Common.Views.About.txtMail": "メール:", "Common.Views.About.txtPoweredBy": "によって提供されています", "Common.Views.About.txtTel": "電話番号：", "Common.Views.About.txtVersion": "バージョン", "Common.Views.AutoCorrectDialog.textAdd": "追加", "Common.Views.AutoCorrectDialog.textApplyAsWork": "作業中に適用する", "Common.Views.AutoCorrectDialog.textAutoCorrect": "オートコレクト", "Common.Views.AutoCorrectDialog.textAutoFormat": "入力オートフォーマット", "Common.Views.AutoCorrectDialog.textBy": "幅", "Common.Views.AutoCorrectDialog.textDelete": "削除する", "Common.Views.AutoCorrectDialog.textHyperlink": "インターネットとネットワークのアドレスをハイパーリンクに変更する", "Common.Views.AutoCorrectDialog.textMathCorrect": "数式オートコレクト", "Common.Views.AutoCorrectDialog.textNewRowCol": "テーブルに新しい行と列を含める", "Common.Views.AutoCorrectDialog.textRecognized": "認識された関数", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "以下の式は、認識される数式です。 自動的にイタリック体になることはありません。", "Common.Views.AutoCorrectDialog.textReplace": "置き換える", "Common.Views.AutoCorrectDialog.textReplaceText": "入力時に置き換える\n\t", "Common.Views.AutoCorrectDialog.textReplaceType": "入力時にテキストを置き換える", "Common.Views.AutoCorrectDialog.textReset": "リセット", "Common.Views.AutoCorrectDialog.textResetAll": "既定値にリセットする", "Common.Views.AutoCorrectDialog.textRestore": "復元する", "Common.Views.AutoCorrectDialog.textTitle": "オートコレクト", "Common.Views.AutoCorrectDialog.textWarnAddRec": "認識される関数には、大文字または小文字のAからZまでの文字のみを含める必要があります。", "Common.Views.AutoCorrectDialog.textWarnResetRec": "追加した式はすべて削除され、削除された式が復元されます。 このまま続けますか？", "Common.Views.AutoCorrectDialog.warnReplace": "％1のオートコレクトのエントリはすでに存在します。 取り替えますか？", "Common.Views.AutoCorrectDialog.warnReset": "追加したオートコレクトはすべて削除され、変更されたものは元の値に復元されます。 このまま続けますか？", "Common.Views.AutoCorrectDialog.warnRestore": "％1のオートコレクトエントリは元の値にリセットされます。 続けますか？", "Common.Views.Chat.textChat": "チャット", "Common.Views.Chat.textClosePanel": "チャットを閉じる", "Common.Views.Chat.textEnterMessage": "ここでメッセージを挿入してください", "Common.Views.Chat.textSend": "送信", "Common.Views.Comments.mniAuthorAsc": "AからZで作成者を表示する", "Common.Views.Comments.mniAuthorDesc": "ZからAで作成者を表示する", "Common.Views.Comments.mniDateAsc": "最も古い", "Common.Views.Comments.mniDateDesc": "最も新しい", "Common.Views.Comments.mniFilterGroups": "グループでフィルター", "Common.Views.Comments.mniPositionAsc": "上から", "Common.Views.Comments.mniPositionDesc": "下から", "Common.Views.Comments.textAdd": "追加", "Common.Views.Comments.textAddComment": "コメントを追加", "Common.Views.Comments.textAddCommentToDoc": "ドキュメントにコメントを追加", "Common.Views.Comments.textAddReply": "返信を追加", "Common.Views.Comments.textAll": "すべて", "Common.Views.Comments.textAnonym": "ゲスト", "Common.Views.Comments.textCancel": "キャンセル", "Common.Views.Comments.textClose": "閉じる", "Common.Views.Comments.textClosePanel": "コメントを閉じる", "Common.Views.Comments.textComment": "コメント", "Common.Views.Comments.textComments": "コメント", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "ここでコメントを挿入してください。", "Common.Views.Comments.textHintAddComment": "コメントを追加", "Common.Views.Comments.textOpenAgain": "もう一度開く", "Common.Views.Comments.textReply": "返信する", "Common.Views.Comments.textResolve": "解決", "Common.Views.Comments.textResolved": "解決済み", "Common.Views.Comments.textSort": "コメントを並べ替える", "Common.Views.Comments.textSortFilter": "コメントの並べ替えとフィルター", "Common.Views.Comments.textSortFilterMore": "並び替え、フィルター、その他", "Common.Views.Comments.textSortMore": "並び替えなど", "Common.Views.Comments.textViewResolved": "コメントを再開する権限がありません", "Common.Views.Comments.txtEmpty": "シートにはコメントがありません", "Common.Views.CopyWarningDialog.textDontShow": "今後このメッセージを表示しない", "Common.Views.CopyWarningDialog.textMsg": "エディターツールバーのボタンやコンテキストメニューの操作によるコピー、カット、ペーストの動作は、このエディタータブ内でのみ実行されます。<br><br> エディタータブ以外のアプリケーションとの間でコピーまたは貼り付けを行うには、次のキーボードの組み合わせを使用して下さい:", "Common.Views.CopyWarningDialog.textTitle": "コピー、カット、ペーストのアクション", "Common.Views.CopyWarningDialog.textToCopy": "コピーのため", "Common.Views.CopyWarningDialog.textToCut": "切り取りのため", "Common.Views.CopyWarningDialog.textToPaste": "貼り付けのため", "Common.Views.CustomizeQuickAccessDialog.textDownload": "ダウンロード", "Common.Views.CustomizeQuickAccessDialog.textMsg": "クイックアクセスツールバーに表示されるコマンドをチェックしてください", "Common.Views.CustomizeQuickAccessDialog.textPrint": "印刷", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "クイックプリント", "Common.Views.CustomizeQuickAccessDialog.textRedo": "やり直す", "Common.Views.CustomizeQuickAccessDialog.textSave": "保存", "Common.Views.CustomizeQuickAccessDialog.textTitle": "クイックアクセスのカスタマイズ", "Common.Views.CustomizeQuickAccessDialog.textUndo": "元に戻す", "Common.Views.DocumentAccessDialog.textLoading": "読み込み中...", "Common.Views.DocumentAccessDialog.textTitle": "共有設定", "Common.Views.DocumentPropertyDialog.errorDate": "カレンダーから値を選択して日付として保存できます。<br>値を手動で入力した場合は、テキストとして保存されます。", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "いいえ", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "はい", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "プロパティはタイトルが必要です", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "タイトル", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "「はい」または「いいえ」", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "日付", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "タイプ", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "数", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "有効な数値を入力してください", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "テキスト", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "プロパティには値が必要です", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "値", "Common.Views.DocumentPropertyDialog.txtTitle": "新しいドキュメントのプロパティ", "Common.Views.Draw.hintEraser": "消しゴム", "Common.Views.Draw.hintSelect": "選択", "Common.Views.Draw.txtEraser": "消しゴム", "Common.Views.Draw.txtHighlighter": "蛍光ペン", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "ペン", "Common.Views.Draw.txtSelect": "選択", "Common.Views.Draw.txtSize": "サイズ", "Common.Views.EditNameDialog.textLabel": "ラベル：", "Common.Views.EditNameDialog.textLabelError": "ラベルは空白にできません。", "Common.Views.Header.ariaQuickAccessToolbar": "クイックアクセスツールバー", "Common.Views.Header.labelCoUsersDescr": "ファイルを編集しているユーザー：", "Common.Views.Header.textAddFavorite": "お気に入りとしてマーク", "Common.Views.Header.textAdvSettings": "詳細設定", "Common.Views.Header.textBack": "ファイルの場所を開く", "Common.Views.Header.textClose": "ファイルを閉じる", "Common.Views.Header.textCompactView": "ツールバーを表示しない", "Common.Views.Header.textHideLines": "ルーラーを表示しない", "Common.Views.Header.textHideStatusBar": "ステータスバーとシートを結合する", "Common.Views.Header.textPrint": "印刷", "Common.Views.Header.textReadOnly": "閲覧のみ", "Common.Views.Header.textRemoveFavorite": "お気に入りから削除", "Common.Views.Header.textSaveBegin": "保存中...", "Common.Views.Header.textSaveChanged": "更新された", "Common.Views.Header.textSaveEnd": "すべての変更が保存されました", "Common.Views.Header.textSaveExpander": "すべての変更が保存されました", "Common.Views.Header.textShare": "共有", "Common.Views.Header.textZoom": "ズーム", "Common.Views.Header.tipAccessRights": "文書のアクセス許可の管理", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "クイックアクセスツールバーのカスタマイズ", "Common.Views.Header.tipDownload": "ファイルをダウンロード", "Common.Views.Header.tipGoEdit": "このファイルを編集する", "Common.Views.Header.tipPrint": "印刷", "Common.Views.Header.tipPrintQuick": "クイックプリント", "Common.Views.Header.tipRedo": "やり直し", "Common.Views.Header.tipSave": "保存", "Common.Views.Header.tipSearch": "検索", "Common.Views.Header.tipUndo": "元に戻す", "Common.Views.Header.tipUndock": "別のウィンドウにドッキングを解除する", "Common.Views.Header.tipUsers": "ユーザーを表示する", "Common.Views.Header.tipViewSettings": "表示の設定", "Common.Views.Header.tipViewUsers": "ユーザーの表示と文書のアクセス権の管理", "Common.Views.Header.txtAccessRights": "アクセス許可の変更", "Common.Views.Header.txtRename": "名前を変更する", "Common.Views.History.textCloseHistory": "履歴を閉じる", "Common.Views.History.textHideAll": "詳細な変更を非表示", "Common.Views.History.textHighlightDeleted": "削除されたところをハイライトする", "Common.Views.History.textMore": "もっと見る", "Common.Views.History.textRestore": "復元する", "Common.Views.History.textShowAll": "詳細な変更を表示する", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "バージョン履歴", "Common.Views.ImageFromUrlDialog.textUrl": "画像のURLを貼り付け", "Common.Views.ImageFromUrlDialog.txtEmpty": "このフィールドは必須項目です", "Common.Views.ImageFromUrlDialog.txtNotUrl": "リンクの入力内容は「http://www.example.com」形式のURLである必要があります。", "Common.Views.ListSettingsDialog.textBulleted": "箇条書きがある", "Common.Views.ListSettingsDialog.textFromFile": "ファイルから", "Common.Views.ListSettingsDialog.textFromStorage": "ストレージから", "Common.Views.ListSettingsDialog.textFromUrl": "URLから", "Common.Views.ListSettingsDialog.textNumbering": "番号付き", "Common.Views.ListSettingsDialog.textSelect": "選択する", "Common.Views.ListSettingsDialog.tipChange": "箇条書きを変更", "Common.Views.ListSettingsDialog.txtBullet": "箇条書き", "Common.Views.ListSettingsDialog.txtColor": "色", "Common.Views.ListSettingsDialog.txtImage": "画像", "Common.Views.ListSettingsDialog.txtImport": "挿入", "Common.Views.ListSettingsDialog.txtNewBullet": "新しい箇条書き", "Common.Views.ListSettingsDialog.txtNewImage": "新しい画像", "Common.Views.ListSettingsDialog.txtNone": "なし", "Common.Views.ListSettingsDialog.txtOfText": "テキストの%", "Common.Views.ListSettingsDialog.txtSize": "サイズ", "Common.Views.ListSettingsDialog.txtStart": "から始まる", "Common.Views.ListSettingsDialog.txtSymbol": "記号", "Common.Views.ListSettingsDialog.txtTitle": "リストの設定", "Common.Views.ListSettingsDialog.txtType": "タイプ", "Common.Views.MacrosDialog.textCopy": "コピー", "Common.Views.MacrosDialog.textCustomFunction": "カスタム関数", "Common.Views.MacrosDialog.textDelete": "削除", "Common.Views.MacrosDialog.textLoading": "読み込み中...", "Common.Views.MacrosDialog.textMacros": "マ<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "自動起動に設定", "Common.Views.MacrosDialog.textRename": "名前を変更", "Common.Views.MacrosDialog.textRun": "実行", "Common.Views.MacrosDialog.textSave": "保存", "Common.Views.MacrosDialog.textTitle": "マ<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "自動起動を解除", "Common.Views.MacrosDialog.tipFunctionAdd": "カスタム関数を追加", "Common.Views.MacrosDialog.tipMacrosAdd": "マクロを追加", "Common.Views.MacrosDialog.tipMacrosRun": "実行", "Common.Views.OpenDialog.closeButtonText": "ファイルを閉じる", "Common.Views.OpenDialog.textInvalidRange": "無効なセル範囲", "Common.Views.OpenDialog.textSelectData": "データの選択", "Common.Views.OpenDialog.txtAdvanced": "詳細", "Common.Views.OpenDialog.txtColon": "コロン", "Common.Views.OpenDialog.txtComma": "カンマ", "Common.Views.OpenDialog.txtDelimiter": "区切り文字", "Common.Views.OpenDialog.txtDestData": "データの付ける場所を選択してください", "Common.Views.OpenDialog.txtEmpty": "このフィールドは必須項目です", "Common.Views.OpenDialog.txtEncoding": "文字コード", "Common.Views.OpenDialog.txtIncorrectPwd": "パスワードが正しくありません。", "Common.Views.OpenDialog.txtOpenFile": "ファイルを開くためにパスワードを入力してください。", "Common.Views.OpenDialog.txtOther": "その他", "Common.Views.OpenDialog.txtPassword": "パスワード", "Common.Views.OpenDialog.txtPreview": "プレビュー", "Common.Views.OpenDialog.txtProtected": "パスワードを入力してファイルを開くと、ファイルの既存のパスワードがリセットされます。", "Common.Views.OpenDialog.txtSemicolon": "セミコロン", "Common.Views.OpenDialog.txtSpace": "スペース", "Common.Views.OpenDialog.txtTab": "タブ", "Common.Views.OpenDialog.txtTitle": "%1オプションを選択", "Common.Views.OpenDialog.txtTitleProtected": "保護されたファイル", "Common.Views.PasswordDialog.txtDescription": "この文書を保護するためのパスワードを設定してください。", "Common.Views.PasswordDialog.txtIncorrectPwd": "先に入力したパスワードと一致しません。", "Common.Views.PasswordDialog.txtPassword": "パスワード", "Common.Views.PasswordDialog.txtRepeat": "パスワードを再入力", "Common.Views.PasswordDialog.txtTitle": "パスワードの設定", "Common.Views.PasswordDialog.txtWarning": "警告: パスワードを忘れると元に戻せません。安全な場所に記録してください。", "Common.Views.PluginDlg.textLoading": "読み込み中", "Common.Views.PluginPanel.textClosePanel": "プラグインを閉じる", "Common.Views.PluginPanel.textLoading": "読み込み中", "Common.Views.Plugins.groupCaption": "プラグイン", "Common.Views.Plugins.strPlugins": "プラグイン", "Common.Views.Plugins.textBackgroundPlugins": "バックグラウンド・プラグイン", "Common.Views.Plugins.textSettings": "設定", "Common.Views.Plugins.textStart": "スタート", "Common.Views.Plugins.textStop": "停止", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "バックグラウンド・プラグインのリスト", "Common.Views.Plugins.tipMore": "もっと", "Common.Views.Protection.hintAddPwd": "パスワードを使用して、暗号化する", "Common.Views.Protection.hintDelPwd": "パスワードの削除", "Common.Views.Protection.hintPwd": "パスワードを変更するか削除する", "Common.Views.Protection.hintSignature": "デジタル署名かデジタル署名行を追加", "Common.Views.Protection.txtAddPwd": "パスワードを追加", "Common.Views.Protection.txtChangePwd": "パスワードを変更", "Common.Views.Protection.txtDeletePwd": "パスワードを削除する", "Common.Views.Protection.txtEncrypt": "暗号化する", "Common.Views.Protection.txtInvisibleSignature": "デジタル署名を追加", "Common.Views.Protection.txtSignature": "署名", "Common.Views.Protection.txtSignatureLine": "署名欄を追加", "Common.Views.RecentFiles.txtOpenRecent": "最近使ったファイルを開く", "Common.Views.RenameDialog.textName": "ファイル名", "Common.Views.RenameDialog.txtInvalidName": "ファイル名に次の文字を使うことはできません。", "Common.Views.ReviewChanges.hintNext": "次の変更箇所へ", "Common.Views.ReviewChanges.hintPrev": "前の​​変更箇所へ", "Common.Views.ReviewChanges.strFast": "即時反映モード", "Common.Views.ReviewChanges.strFastDesc": "リアルタイムの共同編集です。すべての変更は自動的に保存されます。", "Common.Views.ReviewChanges.strStrict": "厳密モード", "Common.Views.ReviewChanges.strStrictDesc": "あなたや他の人が行った変更を同期するために、「保存」ボタンを押してください", "Common.Views.ReviewChanges.tipAcceptCurrent": "現在の変更を承諾する", "Common.Views.ReviewChanges.tipCoAuthMode": "共同編集モードを設定する", "Common.Views.ReviewChanges.tipCommentRem": "コメントを削除する", "Common.Views.ReviewChanges.tipCommentRemCurrent": "このコメントを削除する", "Common.Views.ReviewChanges.tipCommentResolve": "コメントを解決する", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "現在のコメントを解決する", "Common.Views.ReviewChanges.tipHistory": "バージョン履歴を表示する", "Common.Views.ReviewChanges.tipRejectCurrent": "現在の変更を元に戻す", "Common.Views.ReviewChanges.tipReview": "変更履歴", "Common.Views.ReviewChanges.tipReviewView": "変更を表示するモードをご選択ください", "Common.Views.ReviewChanges.tipSetDocLang": "文書の言語を設定する", "Common.Views.ReviewChanges.tipSetSpelling": "スペルチェック", "Common.Views.ReviewChanges.tipSharing": "文書のアクセス許可の管理", "Common.Views.ReviewChanges.txtAccept": "承諾", "Common.Views.ReviewChanges.txtAcceptAll": "すべての変更を承諾する", "Common.Views.ReviewChanges.txtAcceptChanges": "変更を承諾する", "Common.Views.ReviewChanges.txtAcceptCurrent": "現在の変更を承諾する", "Common.Views.ReviewChanges.txtChat": "チャット", "Common.Views.ReviewChanges.txtClose": "閉じる", "Common.Views.ReviewChanges.txtCoAuthMode": "共同編集モード", "Common.Views.ReviewChanges.txtCommentRemAll": "全てのコメントを削除する", "Common.Views.ReviewChanges.txtCommentRemCurrent": "現在のコメントを削除する", "Common.Views.ReviewChanges.txtCommentRemMy": "自分のコメントを削除する", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "自分の今のコメントを削除する", "Common.Views.ReviewChanges.txtCommentRemove": "削除", "Common.Views.ReviewChanges.txtCommentResolve": "解決する", "Common.Views.ReviewChanges.txtCommentResolveAll": "すべてのコメントを解決する", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "現在のコメントを解決する", "Common.Views.ReviewChanges.txtCommentResolveMy": "自分のコメントを解決する", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "自分のコメントを解決する", "Common.Views.ReviewChanges.txtDocLang": "言語", "Common.Views.ReviewChanges.txtFinal": "すべての変更が承認されました（プレビュー）", "Common.Views.ReviewChanges.txtFinalCap": "最終版", "Common.Views.ReviewChanges.txtHistory": "バージョン履歴", "Common.Views.ReviewChanges.txtMarkup": "全ての変更（編集）", "Common.Views.ReviewChanges.txtMarkupCap": "マークアップ", "Common.Views.ReviewChanges.txtNext": "次へ", "Common.Views.ReviewChanges.txtOriginal": "すべての変更が拒否されました（プレビュー）", "Common.Views.ReviewChanges.txtOriginalCap": "初版", "Common.Views.ReviewChanges.txtPrev": "前のへ", "Common.Views.ReviewChanges.txtReject": "拒否する", "Common.Views.ReviewChanges.txtRejectAll": "すべての変更を元に戻す", "Common.Views.ReviewChanges.txtRejectChanges": "変更を拒否する", "Common.Views.ReviewChanges.txtRejectCurrent": "現在の変更を元に戻す", "Common.Views.ReviewChanges.txtSharing": "共有", "Common.Views.ReviewChanges.txtSpelling": "スペルチェック", "Common.Views.ReviewChanges.txtTurnon": "変更履歴", "Common.Views.ReviewChanges.txtView": "表示モード", "Common.Views.ReviewPopover.textAdd": "追加", "Common.Views.ReviewPopover.textAddReply": "返信を追加", "Common.Views.ReviewPopover.textCancel": "キャンセル", "Common.Views.ReviewPopover.textClose": "閉じる", "Common.Views.ReviewPopover.textComment": "コメント", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "ここにコメントを入力してください。", "Common.Views.ReviewPopover.textMention": "+言及されるユーザーに文書にアクセスを提供して、メールで通知する", "Common.Views.ReviewPopover.textMentionNotify": "＋言及されるユーザーはメールで通知される", "Common.Views.ReviewPopover.textOpenAgain": "もう一度開く", "Common.Views.ReviewPopover.textReply": "返信する", "Common.Views.ReviewPopover.textResolve": "解決する", "Common.Views.ReviewPopover.textViewResolved": "コメントを再開する権限がありません", "Common.Views.ReviewPopover.txtDeleteTip": "削除する", "Common.Views.ReviewPopover.txtEditTip": "編集", "Common.Views.SaveAsDlg.textLoading": "読み込み中", "Common.Views.SaveAsDlg.textTitle": "保存先のフォルダ", "Common.Views.SearchPanel.textByColumns": "列で", "Common.Views.SearchPanel.textByRows": "行で", "Common.Views.SearchPanel.textCaseSensitive": "大文字と小文字を区別する", "Common.Views.SearchPanel.textCell": "セル", "Common.Views.SearchPanel.textCloseSearch": "検索を閉じる", "Common.Views.SearchPanel.textContentChanged": "ドキュメントが変更されました", "Common.Views.SearchPanel.textFind": "検索する", "Common.Views.SearchPanel.textFindAndReplace": "検索して置換する", "Common.Views.SearchPanel.textFormula": "数式", "Common.Views.SearchPanel.textFormulas": "数式", "Common.Views.SearchPanel.textItemEntireCell": "セル全体の内容", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0}個のアイテムが正常に交換されました。", "Common.Views.SearchPanel.textLookIn": "検索の範囲", "Common.Views.SearchPanel.textMatchUsingRegExp": "正規表現によるマッチング", "Common.Views.SearchPanel.textName": "名前", "Common.Views.SearchPanel.textNoMatches": "一致する結果がありません", "Common.Views.SearchPanel.textNoSearchResults": "検索結果は見つかりませんでした", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1}のアイテムが交換されました。残りの{2}個のアイテムは他のユーザーによってロックされています。", "Common.Views.SearchPanel.textReplace": "置換する", "Common.Views.SearchPanel.textReplaceAll": "全てを置換する", "Common.Views.SearchPanel.textReplaceWith": "置換後の文字列", "Common.Views.SearchPanel.textSearch": "検索", "Common.Views.SearchPanel.textSearchAgain": "正確な結果を得るために{0}新規検索を行う{1}。", "Common.Views.SearchPanel.textSearchHasStopped": "検索が停止しました", "Common.Views.SearchPanel.textSearchOptions": "検索オプション", "Common.Views.SearchPanel.textSearchResults": "検索結果：{0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "検索結果", "Common.Views.SearchPanel.textSelectDataRange": "データ範囲を選択する", "Common.Views.SearchPanel.textSheet": "シート", "Common.Views.SearchPanel.textSpecificRange": "特定の範囲", "Common.Views.SearchPanel.textTooManyResults": "検索結果が多すぎるため、ここに表示できません", "Common.Views.SearchPanel.textValue": "値", "Common.Views.SearchPanel.textValues": "値", "Common.Views.SearchPanel.textWholeWords": "単語全体のみ", "Common.Views.SearchPanel.textWithin": "範囲", "Common.Views.SearchPanel.textWorkbook": "ワークブック", "Common.Views.SearchPanel.tipNextResult": "次の結果", "Common.Views.SearchPanel.tipPreviousResult": "前の結果", "Common.Views.SelectFileDlg.textLoading": "読み込み中", "Common.Views.SelectFileDlg.textTitle": "データソースを選択する", "Common.Views.ShapeShadowDialog.txtAngle": "角", "Common.Views.ShapeShadowDialog.txtDistance": "距離", "Common.Views.ShapeShadowDialog.txtSize": "サイズ", "Common.Views.ShapeShadowDialog.txtTitle": "影の調整", "Common.Views.ShapeShadowDialog.txtTransparency": "透過性", "Common.Views.SignDialog.textBold": "太字", "Common.Views.SignDialog.textCertificate": "証明書", "Common.Views.SignDialog.textChange": "変更", "Common.Views.SignDialog.textInputName": "署名者の名前を入力してください", "Common.Views.SignDialog.textItalic": "イタリック体", "Common.Views.SignDialog.textNameError": "署名者の名前を空にしておくことはできません。", "Common.Views.SignDialog.textPurpose": "この文書にサインする目的", "Common.Views.SignDialog.textSelect": "選択する", "Common.Views.SignDialog.textSelectImage": "画像を選択", "Common.Views.SignDialog.textSignature": "署名は次のようになります:", "Common.Views.SignDialog.textTitle": "文書のサイン", "Common.Views.SignDialog.textUseImage": "または画像を署名として使用するため、「画像の選択」をクリックしてください", "Common.Views.SignDialog.textValid": "%1から%2までは有効", "Common.Views.SignDialog.tipFontName": "フォント名", "Common.Views.SignDialog.tipFontSize": "フォントのサイズ", "Common.Views.SignSettingsDialog.textAllowComment": "署名者が署名ダイアログボックスにコメントを追加できるようにする", "Common.Views.SignSettingsDialog.textDefInstruction": "このドキュメントに署名する前に、署名するコンテンツが正しいことを確認してください。", "Common.Views.SignSettingsDialog.textInfoEmail": "署名候補者のメールアドレス", "Common.Views.SignSettingsDialog.textInfoName": "署名候補者", "Common.Views.SignSettingsDialog.textInfoTitle": "署名候補者の役職", "Common.Views.SignSettingsDialog.textInstructions": "署名者への説明書", "Common.Views.SignSettingsDialog.textShowDate": "署名欄に署名日を表示する", "Common.Views.SignSettingsDialog.textTitle": "サインの設定", "Common.Views.SignSettingsDialog.txtEmpty": "この項目は必須です", "Common.Views.SymbolTableDialog.textCharacter": "文字", "Common.Views.SymbolTableDialog.textCode": "UnicodeHEX値", "Common.Views.SymbolTableDialog.textCopyright": "著作権マーク", "Common.Views.SymbolTableDialog.textDCQuote": "二重引用符（右）", "Common.Views.SymbolTableDialog.textDOQuote": "二重の引用符(左）", "Common.Views.SymbolTableDialog.textEllipsis": "水平の省略記号", "Common.Views.SymbolTableDialog.textEmDash": "全角ダッシュ", "Common.Views.SymbolTableDialog.textEmSpace": "全角スペース", "Common.Views.SymbolTableDialog.textEnDash": "半角ダッシュ", "Common.Views.SymbolTableDialog.textEnSpace": "半角スペース", "Common.Views.SymbolTableDialog.textFont": "フォント", "Common.Views.SymbolTableDialog.textNBHyphen": "改行をしないハイフン", "Common.Views.SymbolTableDialog.textNBSpace": "改行をしないスペース", "Common.Views.SymbolTableDialog.textPilcrow": "段落記号", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4スペース", "Common.Views.SymbolTableDialog.textRange": "範囲", "Common.Views.SymbolTableDialog.textRecent": "最近使用した記号", "Common.Views.SymbolTableDialog.textRegistered": "登録商標マーク", "Common.Views.SymbolTableDialog.textSCQuote": "単一引用符（右）", "Common.Views.SymbolTableDialog.textSection": "「節」記号", "Common.Views.SymbolTableDialog.textShortcut": "ショートカットキー", "Common.Views.SymbolTableDialog.textSHyphen": "ソフトハイフン", "Common.Views.SymbolTableDialog.textSOQuote": "単一引用符（左）", "Common.Views.SymbolTableDialog.textSpecial": "特殊文字", "Common.Views.SymbolTableDialog.textSymbols": "記号と特殊文字", "Common.Views.SymbolTableDialog.textTitle": "記号", "Common.Views.SymbolTableDialog.textTradeMark": "商標マーク", "Common.Views.UserNameDialog.textDontShow": "二度と表示しない", "Common.Views.UserNameDialog.textLabel": "ラベル：", "Common.Views.UserNameDialog.textLabelError": "ラベルは空白にできません。", "SSE.Controllers.DataTab.strSheet": "シート", "SSE.Controllers.DataTab.textAddExternalData": "外部ソースへのリンクが追加されました。このようなリンクは、「データ」タブで更新することができます。", "SSE.Controllers.DataTab.textColumns": "列", "SSE.Controllers.DataTab.textContinue": "続ける", "SSE.Controllers.DataTab.textDontUpdate": "アップデートしない", "SSE.Controllers.DataTab.textEmptyUrl": "URLを指定してください。", "SSE.Controllers.DataTab.textRows": "行", "SSE.Controllers.DataTab.textTurnOff": "自動アップデートをオフにする", "SSE.Controllers.DataTab.textUpdate": "更新", "SSE.Controllers.DataTab.textWizard": "テキスト区切り", "SSE.Controllers.DataTab.txtDataValidation": "データの入力規則", "SSE.Controllers.DataTab.txtErrorExternalLink": "エラー：アップデートに失敗しました", "SSE.Controllers.DataTab.txtExpand": "拡張する", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "選択範囲の横のデータは削除されません。選択範囲を拡大して隣接するデータを含めるか、現在選択されているセルのみを続行しますか？", "SSE.Controllers.DataTab.txtExtendDataValidation": "選択範囲には、データバリデーション設定のないセルがいくつか含まれています。<br>データバリデーションをこれらのセルに拡張しますか？", "SSE.Controllers.DataTab.txtImportWizard": "テキスト取り込みウィザード", "SSE.Controllers.DataTab.txtRemDuplicates": "重複データを削除", "SSE.Controllers.DataTab.txtRemoveDataValidation": "選択には複数のタイプのバリデーションが含まれます。<br>現在の設定を消去して続行しますか？", "SSE.Controllers.DataTab.txtRemSelected": "選択した範囲で削除する", "SSE.Controllers.DataTab.txtUrlTitle": "データのURLを貼り付け", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "このワークブックには、自動的に更新される外部ソースへのリンクが含まれています。これは安全ではない可能性があります。<br><br>リンク先を信頼する場合は、「続行」をクリックしてください。", "SSE.Controllers.DataTab.warnUpdateExternalData": "このワークブックには、安全でない可能性のある1つまたは複数の外部ソースへのリンクが含まれています。<br>リンクを信頼する場合は、最新のデータを取得するためにそれらを更新してください。", "SSE.Controllers.DocumentHolder.alignmentText": "配置", "SSE.Controllers.DocumentHolder.centerText": "中央揃え", "SSE.Controllers.DocumentHolder.deleteColumnText": "列を削除", "SSE.Controllers.DocumentHolder.deleteRowText": "行を削除", "SSE.Controllers.DocumentHolder.deleteText": "削除", "SSE.Controllers.DocumentHolder.errorInvalidLink": "リンク参照が存在しません。 リンクを修正するか、ご削除ください。", "SSE.Controllers.DocumentHolder.guestText": "ゲスト", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "左に列の挿入", "SSE.Controllers.DocumentHolder.insertColumnRightText": "右に列の挿入", "SSE.Controllers.DocumentHolder.insertRowAboveText": "行 (上)", "SSE.Controllers.DocumentHolder.insertRowBelowText": "行(下)", "SSE.Controllers.DocumentHolder.insertText": "挿入", "SSE.Controllers.DocumentHolder.leftText": "左", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "警告", "SSE.Controllers.DocumentHolder.rightText": "右", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "オートコレクトの設定", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "列の幅{0}記号({1}ピクセル)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "行の高さ{0}ポイント({1}ピクセル)", "SSE.Controllers.DocumentHolder.textCtrlClick": "リンク先に移動するには、クリックします。このセルを選択するには、マウスのボタンを押し続け、ポインターの形が変わったらマウスのボタンを離します。", "SSE.Controllers.DocumentHolder.textInsertLeft": "左に挿入", "SSE.Controllers.DocumentHolder.textInsertTop": "上に挿入", "SSE.Controllers.DocumentHolder.textPasteSpecial": "特殊貼付け", "SSE.Controllers.DocumentHolder.textStopExpand": "テーブルの自動拡を停止する", "SSE.Controllers.DocumentHolder.textSym": "記号", "SSE.Controllers.DocumentHolder.tipIsLocked": "この要素が別のユーザーによって編集されています。", "SSE.Controllers.DocumentHolder.txtAboveAve": "平均より上", "SSE.Controllers.DocumentHolder.txtAddBottom": "下罫線を追加", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "分数線を追加", "SSE.Controllers.DocumentHolder.txtAddHor": "水平線を追加", "SSE.Controllers.DocumentHolder.txtAddLB": "左下罫線を追加", "SSE.Controllers.DocumentHolder.txtAddLeft": "左罫線を追加", "SSE.Controllers.DocumentHolder.txtAddLT": "左上罫線を追加", "SSE.Controllers.DocumentHolder.txtAddRight": "右罫線を追加", "SSE.Controllers.DocumentHolder.txtAddTop": "上罫線を追加", "SSE.Controllers.DocumentHolder.txtAddVer": "縦線を追加", "SSE.Controllers.DocumentHolder.txtAlignToChar": "文字に合わせる", "SSE.Controllers.DocumentHolder.txtAll": "（すべて）", "SSE.Controllers.DocumentHolder.txtAllTableHint": "テーブルのすべての値、または、指定したテーブル列と列番号、データおよび集計行を返す", "SSE.Controllers.DocumentHolder.txtAnd": "と", "SSE.Controllers.DocumentHolder.txtBegins": "で始まる", "SSE.Controllers.DocumentHolder.txtBelowAve": "平均より下​​", "SSE.Controllers.DocumentHolder.txtBlanks": "（空白）", "SSE.Controllers.DocumentHolder.txtBorderProps": "罫線の​​プロパティ", "SSE.Controllers.DocumentHolder.txtBottom": "下", "SSE.Controllers.DocumentHolder.txtByField": "%2分の%1", "SSE.Controllers.DocumentHolder.txtColumn": "列", "SSE.Controllers.DocumentHolder.txtColumnAlign": "列の配置", "SSE.Controllers.DocumentHolder.txtContains": "含んでいる\t", "SSE.Controllers.DocumentHolder.txtCopySuccess": "リンクがクリップボードにコピーされました", "SSE.Controllers.DocumentHolder.txtDataTableHint": "テーブルまたは指定したテーブル列のデータセルを返す", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "引数のサイズの縮小", "SSE.Controllers.DocumentHolder.txtDeleteArg": "引数を削除", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "手動ブレークを削除する", "SSE.Controllers.DocumentHolder.txtDeleteChars": "囲まれた文字を削除", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "開始文字、終了文字と区切り文字を削除", "SSE.Controllers.DocumentHolder.txtDeleteEq": "数式を削除", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "文字を削除", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "冪根を削除する", "SSE.Controllers.DocumentHolder.txtEnds": "終了", "SSE.Controllers.DocumentHolder.txtEquals": "等号", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "セルの色に等号", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "フォントの色に等号", "SSE.Controllers.DocumentHolder.txtExpand": "拡張と並べ替え", "SSE.Controllers.DocumentHolder.txtExpandSort": "選択範囲の横のデータは並べ替えられません。 選択範囲を拡張して隣接するデータを含めるか、現在選択されているセルのみの並べ替えを続行しますか？", "SSE.Controllers.DocumentHolder.txtFilterBottom": "下", "SSE.Controllers.DocumentHolder.txtFilterTop": "上", "SSE.Controllers.DocumentHolder.txtFractionLinear": "分数(横)に変更", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "斜めの分数罫に変更", "SSE.Controllers.DocumentHolder.txtFractionStacked": "分数(縦)に変更\t", "SSE.Controllers.DocumentHolder.txtGreater": "次の値より大きい", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "次の値より大きいか等しい", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "テキストの上の文字", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "テキストの下の文字", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "テーブルまたは指定されたテーブルカラムのカラムヘッダを返す", "SSE.Controllers.DocumentHolder.txtHeight": "高さ", "SSE.Controllers.DocumentHolder.txtHideBottom": "下罫線を表示しない", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "下限を表示しない", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "右かっこを表示しない", "SSE.Controllers.DocumentHolder.txtHideDegree": "次数を表示しない", "SSE.Controllers.DocumentHolder.txtHideHor": "水平線を表示しない", "SSE.Controllers.DocumentHolder.txtHideLB": "左(下)の線を表示しない", "SSE.Controllers.DocumentHolder.txtHideLeft": "左罫線を表示しない", "SSE.Controllers.DocumentHolder.txtHideLT": "左(上)の線を表示しない", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "左かっこを表示しない", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "プレースホルダを表示しない", "SSE.Controllers.DocumentHolder.txtHideRight": "右罫線を表示しない", "SSE.Controllers.DocumentHolder.txtHideTop": "上罫線を表示しない", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "上限を表示しない", "SSE.Controllers.DocumentHolder.txtHideVer": "縦線を表示しない", "SSE.Controllers.DocumentHolder.txtImportWizard": "テキストインポートウィザード", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "引数のサイズの拡大", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "後に引数を挿入", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "前に引数を挿入", "SSE.Controllers.DocumentHolder.txtInsertBreak": "手動ブレークを挿入", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "後に方程式を挿入", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "前に方程式を挿入", "SSE.Controllers.DocumentHolder.txtItems": "アイテム", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "テキストのみ保存", "SSE.Controllers.DocumentHolder.txtLess": "次の値より小さい", "SSE.Controllers.DocumentHolder.txtLessEquals": "次の値より小さいか等しい", "SSE.Controllers.DocumentHolder.txtLimitChange": "極限の位置を変更", "SSE.Controllers.DocumentHolder.txtLimitOver": "テキストの上の限定", "SSE.Controllers.DocumentHolder.txtLimitUnder": "テキストの下の限定", "SSE.Controllers.DocumentHolder.txtLockSort": "選択の範囲の近くにデータが見つけられたけどこのセルを変更するに十分なアクセス許可がありません。<br> 選択の範囲を続行してもよろしいですか？", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "括弧を引数の高さに合わせる", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "行列の整列", "SSE.Controllers.DocumentHolder.txtNoChoices": "セルを記入する選択肢はありません。<br>置換対象として選択できるのは、列のテキスト値のみです。", "SSE.Controllers.DocumentHolder.txtNotBegins": "次の文字から始まらない", "SSE.Controllers.DocumentHolder.txtNotContains": "次の文字を含まない", "SSE.Controllers.DocumentHolder.txtNotEnds": "次の文字列で終わらない", "SSE.Controllers.DocumentHolder.txtNotEquals": "次の値に等しくない", "SSE.Controllers.DocumentHolder.txtOr": "または", "SSE.Controllers.DocumentHolder.txtOverbar": "テキストの上にバー", "SSE.Controllers.DocumentHolder.txtPaste": "貼り付け", "SSE.Controllers.DocumentHolder.txtPasteBorders": "罫線のない数式", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "数式と列幅", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "貼り付け先の書式に合わせる", "SSE.Controllers.DocumentHolder.txtPasteFormat": "書式のみ貼り付け", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "数式と数値の書式", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "数式だけを貼り付ける", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "数式と全ての書式", "SSE.Controllers.DocumentHolder.txtPasteLink": "リンクを貼り付け", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "リンクされた画像", "SSE.Controllers.DocumentHolder.txtPasteMerge": "条件付き書式を結合する", "SSE.Controllers.DocumentHolder.txtPastePicture": "画像", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "ソースのフォーマット", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "入れ替える", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "値と全ての書式", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "値と数値の書式", "SSE.Controllers.DocumentHolder.txtPasteValues": "値のみを貼り付け", "SSE.Controllers.DocumentHolder.txtPercent": "パーセント", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "テーブルの自動拡張のやり直し", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "分数線の削除", "SSE.Controllers.DocumentHolder.txtRemLimit": "制限を削除する", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "アクセント記号を削除", "SSE.Controllers.DocumentHolder.txtRemoveBar": "線を削除する", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "この署名を削除しますか？<br>この操作は元に戻せません。", "SSE.Controllers.DocumentHolder.txtRemScripts": "スクリプトの削除", "SSE.Controllers.DocumentHolder.txtRemSubscript": "下付きの削除", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "上付きの削除", "SSE.Controllers.DocumentHolder.txtRowHeight": "行の高さ", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "テキストの後のスクリプト", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "テキストの前のスクリプト", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "下限を表示する", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "右かっこの表示", "SSE.Controllers.DocumentHolder.txtShowDegree": "次数を表示する", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "左かっこの表示", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "プレースホルダーの表示", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "上限を表示する", "SSE.Controllers.DocumentHolder.txtSorting": "並べ替え", "SSE.Controllers.DocumentHolder.txtSortSelected": "選択した内容を並べ替える", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "かっこの拡大", "SSE.Controllers.DocumentHolder.txtThisRowHint": "指定した列のこの行のみを選択", "SSE.Controllers.DocumentHolder.txtTop": "上", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "テーブルまたは指定したテーブル列の集計行を返す", "SSE.Controllers.DocumentHolder.txtUnderbar": "テキストの下にバー", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "テーブルの自動拡をキャンセルする", "SSE.Controllers.DocumentHolder.txtUseTextImport": "テキスト取り込みウィザードを使う", "SSE.Controllers.DocumentHolder.txtWarnUrl": "このリンクをクリックすると、デバイスに害を及ぼす可能性があります。このまま続けますか？", "SSE.Controllers.DocumentHolder.txtWidth": "幅", "SSE.Controllers.DocumentHolder.warnFilterError": "値フィルターを適用するには、「値」範囲に少なくとも1つのフィールドが必要です。", "SSE.Controllers.FormulaDialog.sCategoryAll": "すべて", "SSE.Controllers.FormulaDialog.sCategoryCube": "立方体", "SSE.Controllers.FormulaDialog.sCategoryCustom": "カスタム", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "データベース", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "日付と時刻", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "エンジニアリング", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "財務", "SSE.Controllers.FormulaDialog.sCategoryInformation": "情報", "SSE.Controllers.FormulaDialog.sCategoryLast10": "最後に使用した10", "SSE.Controllers.FormulaDialog.sCategoryLogical": "論理", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "検索/行列", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "数学と三角法", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "統計", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "テキストとデータ", "SSE.Controllers.LeftMenu.newDocumentTitle": "名前が付けられていないスプレッドシート", "SSE.Controllers.LeftMenu.textByColumns": "列で", "SSE.Controllers.LeftMenu.textByRows": "行で", "SSE.Controllers.LeftMenu.textFormulas": "数式", "SSE.Controllers.LeftMenu.textItemEntireCell": "ここでセルのの​​内容を挿入してください。", "SSE.Controllers.LeftMenu.textLoadHistory": "バリエーションの履歴の読み込み中...", "SSE.Controllers.LeftMenu.textLookin": "検索の範囲", "SSE.Controllers.LeftMenu.textNoTextFound": "検索データが見つかりませんでした。他の検索設定を選択してください。", "SSE.Controllers.LeftMenu.textReplaceSkipped": "置換が完了しました。{0}つスキップされました。", "SSE.Controllers.LeftMenu.textReplaceSuccess": "検索完了しました。更新件数は、{0} です。", "SSE.Controllers.LeftMenu.textSave": "保存", "SSE.Controllers.LeftMenu.textSearch": "検索", "SSE.Controllers.LeftMenu.textSelectPath": "ファイルのコピーを保存するパスを入力してください", "SSE.Controllers.LeftMenu.textSheet": "シート", "SSE.Controllers.LeftMenu.textValues": "値", "SSE.Controllers.LeftMenu.textWarning": "警告", "SSE.Controllers.LeftMenu.textWithin": "範囲", "SSE.Controllers.LeftMenu.textWorkbook": "ブック", "SSE.Controllers.LeftMenu.txtUntitled": "タイトルなし", "SSE.Controllers.LeftMenu.warnDownloadAs": "この形式で保存し続ける場合は、テキスト以外のすべての機能が失われます。<br>続行してもよろしいですか？", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "CSV形式は複数シートファイルの保存をサポートしていません。<br>選択した形式を維持し、現在のシートだけを保存するには、Saveを押します。<br>現在のスプレッドシートを保存するには、Cancelをクリックして、別の形式で保存してください。", "SSE.Controllers.Main.confirmAddCellWatches": "このアクションは {0} セル時計を追加します。<br>このまま続けますか？", "SSE.Controllers.Main.confirmAddCellWatchesMax": "このアクションは、メモリ保存の理由によって {0} セルウォッチのみを追加します。<br>このまま続けますか？", "SSE.Controllers.Main.confirmMaxChangesSize": "アクションのサイズがサーバーに設定された制限を超えています。<br>「元に戻す」ボタンを押して最後のアクションをキャンセルするか、「続ける」を押してローカルにアクションを維持してください（何も失われないことを確認するために、ファイルをダウンロードするか、その内容をコピーする必要があります）。", "SSE.Controllers.Main.confirmMoveCellRange": "展開先のセルにはデータがあります。続けますか？", "SSE.Controllers.Main.confirmPutMergeRange": "ソースデータは結合されたセルを含まれています。<br>テーブルに貼り付る前にマージを削除しました。", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "ヘーダ行の数式が削除されて、固定テキストに変換されます。続けてもよろしいですか？", "SSE.Controllers.Main.confirmReplaceHFPicture": "ヘッダーの各セクションに挿入できる写真は1枚のみです。<br>「置き換える」を押すと、既存の画像を置き換えます。<br>「キープ」を押すと、既存の画像を保持します。", "SSE.Controllers.Main.convertationTimeoutText": "変換のタイムアウトを超過しました。", "SSE.Controllers.Main.criticalErrorExtText": "OKボタンを押すと文書リストに戻ります", "SSE.Controllers.Main.criticalErrorTitle": "エラー", "SSE.Controllers.Main.downloadErrorText": "ダウンロードに失敗しました", "SSE.Controllers.Main.downloadTextText": "スプレッドシートのダウンロード中...", "SSE.Controllers.Main.downloadTitleText": "スプレッドシートのダウンロード中", "SSE.Controllers.Main.errNoDuplicates": "重複した値はありません", "SSE.Controllers.Main.errorAccessDeny": "権限のない操作を実行しようとしています。<br>ドキュメントサーバーの管理者にご連絡ください。", "SSE.Controllers.Main.errorArgsRange": "入力した数式は正しくありません。<br>引数の範囲が正しくありません。", "SSE.Controllers.Main.errorAutoFilterChange": "ワークシートの表内のセルをシフトしようとしているので、この操作は許可されません。", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "テーブルの一部を移動することはできないので、操作を実行することができません。<br>テーブル全体がシフトしたように、ほかのデータの範囲を選択し、もう一度お試しください。", "SSE.Controllers.Main.errorAutoFilterDataRange": "選んだ範囲にこの操作を適用できません。<br>範囲内の1つのセルを選んでから、もう一度お試しください。", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "エリアをフィルタされたセルが含まれているので、操作を実行できません。<br>フィルタリングの要素を表示して、もう一度お試しください", "SSE.Controllers.Main.errorBadImageUrl": "画像のURLが正しくありません。", "SSE.Controllers.Main.errorCalculatedItemInPageField": "項目を追加または変更できません。ピボットテーブルレポートのフィルタにこのフィールドがあります。", "SSE.Controllers.Main.errorCannotPasteImg": "この画像はクリップボードから貼り付けることはできませんが、端末に保存してそこから挿入するか、\nテキストを含まない画像をコピーしてスプレッドシートに貼り付けることが可能です。", "SSE.Controllers.Main.errorCannotUngroup": "グループ化を解除できません。 アウトラインを開始するには、詳細の行または列を選択してグループ化ください。", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "このコマンドは、保護されたシートでは使用できません。このコマンドを使用するには、シートの保護を解除してください。<br>パスワードの入力を求められる場合があります。", "SSE.Controllers.Main.errorChangeArray": "配列の一部を変更することはできません。", "SSE.Controllers.Main.errorChangeFilteredRange": "これにより、ワークシートのフィルター範囲が変更されます。<br>このタスクを完了するには、オートフィルターをご削除ください。", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "変更しようとしているチャートには、保護されたシートにあります。変更するには保護を解除が必要です。パスワードの入力を要求されることもあります。", "SSE.Controllers.Main.errorCircularReference": "数式が自分のセルを直接または間接的に参照する循環参照が1つ以上あります。<br>これらの参照を削除または変更するか、数式を別のセルに移動してみてください。", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "サーバーとの接続が失われました。今、文書を編集することができません。", "SSE.Controllers.Main.errorConnectToServer": "文書を保存できませんでした。接続設定を確認するか、管理者にお問い合わせください。<br>OKボタンをクリックするとドキュメントをダウンロードするように求められます。", "SSE.Controllers.Main.errorConvertXml": "<br>サポートされていない形式のファイルです。<br>XML Spreadsheet 2003形式のみ使用可能です。", "SSE.Controllers.Main.errorCopyMultiselectArea": "このコマンドを複数選択において使用することはできません。<br>単一の範囲を選択して、再ご試行ください。", "SSE.Controllers.Main.errorCountArg": "入力した数式は正しくありません。<br>引数の数が一致していません。", "SSE.Controllers.Main.errorCountArgExceed": "入力した数式は正しくありません。<br>引数の数を超過しました。", "SSE.Controllers.Main.errorCreateDefName": "存在する名前付き範囲を編集することはできません。<br>今、範囲が編集されているので、新しい名前付き範囲を作成することはできません。", "SSE.Controllers.Main.errorCreateRange": "既存のレンジは編集できず、新しいレンジは編集中のものがあるため、<br>現時点では作成することができません。", "SSE.Controllers.Main.errorDatabaseConnection": "外部エラーです。<br>データベース接続のエラーです。この問題は解決しない場合は、サポートにお問い合わせください。", "SSE.Controllers.Main.errorDataEncrypted": "暗号化された変更を受け取りましたが、解読できません。", "SSE.Controllers.Main.errorDataRange": "データ範囲が正しくありません", "SSE.Controllers.Main.errorDataValidate": "入力した値は無効です。<br>ユーザーには、このセルに入力できる値が制限されています。", "SSE.Controllers.Main.errorDefaultMessage": "エラー コード:%1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "削除しようとしている列には、ロックされたセルが含まれています。ワークシートが保護されている場合、ロックされたセルを削除することはできません。ロックされたセルを削除するには、ワークシートの保護を解除します。パスワードの入力を要求されることもあります。", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "削除しようとしている行には、ロックされたセルが含まれています。ワークシートが保護されている場合、ロックされたセルを削除することはできません。ロックされたセルを削除するには、ワークシートの保護を解除します。パスワードの入力を要求されることもあります。", "SSE.Controllers.Main.errorDependentsNoFormulas": "「参照先のトレース」コマンドで、アクティブセルを参照する数式が見つかりませんでした。", "SSE.Controllers.Main.errorDirectUrl": "ドキュメントへのリンクを確認してください。<br>このリンクは、ダウンロード用のファイルへの直接リンクである必要があります。", "SSE.Controllers.Main.errorEditingDownloadas": "文書の処理中にエラーが発生しました。<br>コンピューターにファイルのバックアップコピーを保存するために、「名前を付けてダウンロード」をご使用ください。", "SSE.Controllers.Main.errorEditingSaveas": "文書の処理中にエラーが発生しました。<br>コンピューターにファイルのバックアップを保存するために、「名前を付けてダウンロード」をご使用ください。", "SSE.Controllers.Main.errorEditView": "既存のシートの表示を編集することはできません。今、編集されているので、新しいのを作成することはできません。", "SSE.Controllers.Main.errorEmailClient": "メールクライアントが見つかりませんでした。", "SSE.Controllers.Main.errorFilePassProtect": "文書がパスワードで保護されているため、開くことができません。", "SSE.Controllers.Main.errorFileRequest": "外部エラーです。<br>ファイルリクエストのエラーです。この問題は解決しない場合は、サポートにお問い合わせください。", "SSE.Controllers.Main.errorFileSizeExceed": "ファイルサイズがサーバーで設定された制限を超過しています。<br>Documentサーバー管理者に詳細をお問い合わせください。", "SSE.Controllers.Main.errorFileVKey": "外部エラーです。<br>セキュリティキーが正しくありません。この問題は解決しない場合は、サポートにお問い合わせください。", "SSE.Controllers.Main.errorFillRange": "選択した範囲を塗りつぶせません。<br>\n結合されたセルは同じサイズである必要があります。", "SSE.Controllers.Main.errorForceSave": "文書の保存中にエラーが発生しました。コンピューターにファイルを保存するために、「名前を付けてダウンロード」を使用し、または後で再お試しください。", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "ピボットテーブルレポートの項目またはフィールド名に数式を入力できません。", "SSE.Controllers.Main.errorFormulaName": "入力した数式は正しくありません。<br>数式の名前が正しくありません。", "SSE.Controllers.Main.errorFormulaParsing": "数式を解析中に内部エラーが発生", "SSE.Controllers.Main.errorFrmlMaxLength": "数式の長さが8192文字の制限を超えています。<br>編集して再びお試しください。", "SSE.Controllers.Main.errorFrmlMaxReference": "値、<br>セル参照、名前が多すぎるため、この数式を入力できません。", "SSE.Controllers.Main.errorFrmlMaxTextLength": "数式のテキスト値は255文字に制限されています。 <br> コンカチネート関数または連結演算子（＆）をご使用ください。", "SSE.Controllers.Main.errorFrmlWrongReferences": "関数が存在しないシートを参照します。<br>データを確認して、もう一度お試しください。", "SSE.Controllers.Main.errorFTChangeTableRangeError": "選択したセル範囲で操作を完了できませんでした。<br>最初のテーブルの行は同じ行にあったように、範囲をご選択ください。<br>新しいテーブル範囲が元のテーブル範囲に重なるようにしてください。", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "選択したセル範囲で操作を完了できませんでした。<br>他のテーブルが含まれていない範囲をご選択ください。", "SSE.Controllers.Main.errorInconsistentExt": "ファイルを開くときにエラーが発生しました。<br>ファイルの内容がファイルの拡張子と一致しません。", "SSE.Controllers.Main.errorInconsistentExtDocx": "ファイルを開くときにエラーが発生しました。<br>ファイルの内容はドキュメント (docx など) に対応していますが、ファイルの拡張子が一致していません: %1", "SSE.Controllers.Main.errorInconsistentExtPdf": "ファイルを開くときにエラーが発生しました。<br>ファイルの内容は次のいずれかの形式に対応しています: pdf/djvu/xps/oxps が、ファイルの拡張子が一致していません: %1", "SSE.Controllers.Main.errorInconsistentExtPptx": "ファイルを開くときにエラーが発生しました。<br>ファイルの内容はプレゼンテーション (pptx など) に対応していますが、ファイルの拡張子が一致していません: %1", "SSE.Controllers.Main.errorInconsistentExtXlsx": "ファイルを開くときにエラーが発生しました。<br>ファイルの内容はスプレッドシート (xlsx など) に対応していますが、ファイルの拡張子が一致していません: %1", "SSE.Controllers.Main.errorInvalidRef": "選択のための正しい名前、または移動の正しい参照を入力してください。", "SSE.Controllers.Main.errorKeyEncrypt": "不明なキーの記述子", "SSE.Controllers.Main.errorKeyExpire": "署名キーは期限切れました。", "SSE.Controllers.Main.errorLabledColumnsPivot": "ピボットテーブルを作成するには、ラベル付きの列を持つリストとして編成されたデータをご使用ください。", "SSE.Controllers.Main.errorLoadingFont": "フォントが読み込まれていません。<br>ドキュメントサーバーの管理者に連絡してください。", "SSE.Controllers.Main.errorLocationOrDataRangeError": "場所またはデータ範囲の参照が正しくありません。", "SSE.Controllers.Main.errorLockedAll": "シートは他のユーザーによってロックされているので、操作を実行することができません。", "SSE.Controllers.Main.errorLockedCellGoalSeek": "パラメータ選択プロセスに関与するセルの1つが、他のユーザーによって変更された。", "SSE.Controllers.Main.errorLockedCellPivot": "ピボットテーブル内のデータを変更することはできません。", "SSE.Controllers.Main.errorLockedWorksheetRename": "他のユーザーによって名前が変更されているのでシートの名前を変更することはできません。", "SSE.Controllers.Main.errorMaxPoints": "グラフごとの直列のポイントの最大数は4096です。", "SSE.Controllers.Main.errorMoveRange": "結合されたセルの一部を変更することはできません。", "SSE.Controllers.Main.errorMoveSlicerError": "テーブルスライサーをあるワークブックから別のワークブックにコピーすることはできません。<br>テーブル全体とスライサーを選択して、再ご試行ください。", "SSE.Controllers.Main.errorMultiCellFormula": "複数セルの配列数式はテーブルでは使用できません。", "SSE.Controllers.Main.errorNoDataToParse": "解析するデータが選択されていません。", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "ピボットテーブルに計算された項目がある場合、フィールドをデータエリアで2回以上使用したり、データエリアと別のエリアで同時に使用したりすることはできません。", "SSE.Controllers.Main.errorOpenWarning": "ファイル式の1つが8192文字の制限を超えています。<br>この式が削除されました。", "SSE.Controllers.Main.errorOperandExpected": "入力した関数の構文が正しくありません。かっこ「（」または「）」のいずれかが欠落していないかどうかをご確認ください。", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "入力したパスワードは間違っています。<br> CapsLock キーがオフになっていることを確認し、大文字と小文字が正しく使われていることを確認してください。　", "SSE.Controllers.Main.errorPasteInPivot": "選択したセルに対してこの変更を行うことはできません。ピボットテーブルに影響を与えるためです。<br>フィールドリストを使用してレポートを変更してください。", "SSE.Controllers.Main.errorPasteMaxRange": "コピーと貼り付けエリアが一致していません。<br>同じサイズの領域を選択するか、またはコピーしたセルを貼り付けるために行の最初のセルをクリックしてください。", "SSE.Controllers.Main.errorPasteMultiSelect": "この操作は、複数の範囲を選択した場合には実行できません。<br>単一の範囲を選択して、再試行してください。", "SSE.Controllers.Main.errorPasteSlicerError": "テーブルスライサーは、あるブックから別のブックにコピーすることはできません。", "SSE.Controllers.Main.errorPivotFieldNameExists": "ピボットテーブルのフィールド名がすでに存在します。", "SSE.Controllers.Main.errorPivotGroup": "その選択をグループ化できません。", "SSE.Controllers.Main.errorPivotOverlap": "ピボットテーブルレポートにはテーブルを重ねることができません。", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "ピボットテーブルのレポートが、基礎となるデータなしで保存されました。<br>[更新]ボタンを使用して、レポートを更新します。", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "「参照元のトレース」コマンドは、アクティブなセルに有効な参照を含む数式が含まれている必要があります。", "SSE.Controllers.Main.errorPrintMaxPagesCount": "残念ながら、現在のプログラムバージョンでは一度に1500ページを超える印刷はできません。<br>この制限は今後のリリースで削除される予定です。", "SSE.Controllers.Main.errorProcessSaveResult": "保存に失敗しました。", "SSE.Controllers.Main.errorProtectedRange": "この範囲は編集不可です。", "SSE.Controllers.Main.errorSaveWatermark": "このファイルには、別のドメインにリンクされた透かし画像が含まれています。<br>PDFで見えるようにするには、文書と同じドメインからリンクされるように透かし画像を更新するか、コンピュータからアップロードしてください。", "SSE.Controllers.Main.errorServerVersion": "エディターのバージョンが更新されました。 変更を適用するために、ページが再読み込みされます。", "SSE.Controllers.Main.errorSessionAbsolute": "ドキュメント編集セッションが終了しました。 ページを再度お読み込みください。", "SSE.Controllers.Main.errorSessionIdle": "このドキュメントは長い間編集されていませんでした。このページを再度読み込んでください。", "SSE.Controllers.Main.errorSessionToken": "サーバーとの接続が中断されました。このページを再度読み込んでください。", "SSE.Controllers.Main.errorSetPassword": "パスワードを設定できませんでした。", "SSE.Controllers.Main.errorSingleColumnOrRowError": "場所の参照が有効ではありません。すべてのセルが同じ行または列に含まれていません。<br>　すべてのセルが 1 つの行または列に含まれるように選択してください", "SSE.Controllers.Main.errorStockChart": "行の順序が正しくありません。この株価チャートを作成するには、<br>始値、高値、安値、終値の順でシートのデータを配置してください。", "SSE.Controllers.Main.errorToken": "ドキュメントセキュリティトークンが正しく形成されていません。<br>ドキュメントサーバーの管理者にご連絡ください。", "SSE.Controllers.Main.errorTokenExpire": "ドキュメントセキュリティトークンの有効期限が切れています。<br>ドキュメントサーバーの管理者に連絡してください。", "SSE.Controllers.Main.errorUnexpectedGuid": "外部エラーです。<br>予期しないGuidです。この問題は解決しない場合は、サポートにお問い合わせください。", "SSE.Controllers.Main.errorUpdateVersion": "ファイルのバージョンが変更されました。ページが再ロードされます。", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "インターネット接続が復旧し、ファイルのバージョンが更新されました。<br>作業を継続する前に、ファイルをダウンロードするか、内容をコピーして、変更が消えてしまわないように確認してから、ページを再びお読み込みください。", "SSE.Controllers.Main.errorUserDrop": "今、ファイルにアクセスすることはできません。", "SSE.Controllers.Main.errorUsersExceed": "料金プランで許可されているユーザー数を超過しました。", "SSE.Controllers.Main.errorViewerDisconnect": "接続が失われました。文書の表示は可能ですが、<br>再度接続されてページが再ロードされるまで、ダウンロードまたは印刷することはできません。", "SSE.Controllers.Main.errorWrongBracketsCount": "入力した数式は正しくありません。<br>かっこの数が正しくありません。", "SSE.Controllers.Main.errorWrongOperator": "入力した数式は正しくありません。誤った演算子が使用されました。<br>エラーを確認して修正してください。または、数式の編集をキャンセルするためにESCボタンを使用してください。", "SSE.Controllers.Main.errorWrongPassword": "パスワードが正しくありません。", "SSE.Controllers.Main.errRemDuplicates": "削除した重複した数: {0}、残した一意の数: {1}", "SSE.Controllers.Main.leavePageText": "このスプレッドシートの保存されていない変更があります。保存するために「このページにとどまる」、「保存」をクリックしてください。全ての保存しない変更をキャンサルするために「このページを離れる」をクリックしてください。", "SSE.Controllers.Main.leavePageTextOnClose": "このスプレッドシートにある保存されていない変更が失われます。保存するように「キャンセル」クリックして「保存」クリックしてください。保存されていない変更を破棄ように「OK」をクリックしてください。", "SSE.Controllers.Main.loadFontsTextText": "データを読み込んでいます...", "SSE.Controllers.Main.loadFontsTitleText": "データを読み込んでいます", "SSE.Controllers.Main.loadFontTextText": "データを読み込んでいます...", "SSE.Controllers.Main.loadFontTitleText": "データを読み込んでいます", "SSE.Controllers.Main.loadImagesTextText": "イメージを読み込み中...", "SSE.Controllers.Main.loadImagesTitleText": "イメージを読み込み中", "SSE.Controllers.Main.loadImageTextText": "イメージを読み込み中...", "SSE.Controllers.Main.loadImageTitleText": "イメージを読み込み中", "SSE.Controllers.Main.loadingDocumentTitleText": "スプレッドシートの読み込み中", "SSE.Controllers.Main.notcriticalErrorTitle": " 警告", "SSE.Controllers.Main.openErrorText": "ファイルを読み込み中にエラーが発生しました。", "SSE.Controllers.Main.openTextText": "スプレッドシートを開いています...", "SSE.Controllers.Main.openTitleText": "スプレッドシートを開いています", "SSE.Controllers.Main.pastInMergeAreaError": "結合されたセルの一部を変更することはできません。", "SSE.Controllers.Main.printTextText": "スプレッドシートの印刷...", "SSE.Controllers.Main.printTitleText": "スプレッドシートの印刷", "SSE.Controllers.Main.reloadButtonText": "ページの再読み込み", "SSE.Controllers.Main.requestEditFailedMessageText": "この文書は他のユーザによって編集しています。後でもう一度試してみてください。", "SSE.Controllers.Main.requestEditFailedTitleText": "アクセスが拒否されました", "SSE.Controllers.Main.saveErrorText": "ファイルを保存中にエラーが発生しました。", "SSE.Controllers.Main.saveErrorTextDesktop": "このファイルは作成または保存できません。<br>考えられる理由は次のとおりです：<br>1. 閲覧のみのファイルです。<br>2. ファイルが他のユーザーによって編集されています。<br>3. ディスクが満杯か破損しています。", "SSE.Controllers.Main.saveTextText": "スプレッドシートを保存中...", "SSE.Controllers.Main.saveTitleText": "スプレッドシートを保存中", "SSE.Controllers.Main.scriptLoadError": "インターネット接続が遅いため、一部のコンポーネントをロードできませんでした。ページを再度お読み込みください。", "SSE.Controllers.Main.textAnonymous": "匿名者", "SSE.Controllers.Main.textApplyAll": "全ての数式に適用する", "SSE.Controllers.Main.textBuyNow": "ウェブサイトを訪問する", "SSE.Controllers.Main.textChangesSaved": "すべての変更が保存されました", "SSE.Controllers.Main.textClose": "閉じる", "SSE.Controllers.Main.textCloseTip": "ヒントを閉じるためにクリックしてください。", "SSE.Controllers.Main.textConfirm": "確認", "SSE.Controllers.Main.textConnectionLost": "接続中です。接続設定をご確認ください。", "SSE.Controllers.Main.textContactUs": "営業部に連絡する", "SSE.Controllers.Main.textContinue": "続ける", "SSE.Controllers.Main.textConvertEquation": "この数式は、サポートされなくなった古いバージョンの数式エディタで作成されました。 編集するには、方程式をOffice Math ML形式に変換します。<br>今すぐ変換しますか？", "SSE.Controllers.Main.textCustomLoader": "ライセンスの条件によっては、ローダーを変更する権利がないことにご注意ください。<br>見積もりについては、営業部門にお問い合わせください。", "SSE.Controllers.Main.textDisconnect": "接続が切断されました ", "SSE.Controllers.Main.textFillOtherRows": "他の列を埋める", "SSE.Controllers.Main.textFormulaFilledAllRows": "{0}で埋められた数式列はデータが挿入されてます。他の空の列の挿入は数分かかる場合があります。", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "数式は最初の{0}列で挿入されてます。他の空の列の挿入は数分かかる場合があります。", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "メモリ保存により数式で挿入されている最初の{0}列はデータが含めれています。このシート内にその他の{1}列にデータが含まれています。手動でそれらを入力が可能です。", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "メモリ保存により最初の{0}列は数式のみで挿入されています。このシートの他の列にはデータが含まれていません。", "SSE.Controllers.Main.textGuest": "ゲスト", "SSE.Controllers.Main.textHasMacros": "ファイルには自動マクロが含まれています。<br>マクロを実行しますか？", "SSE.Controllers.Main.textKeep": "キープ", "SSE.Controllers.Main.textLearnMore": "更に詳しく", "SSE.Controllers.Main.textLoadingDocument": "スプレッドシートの読み込み中", "SSE.Controllers.Main.textLongName": "128文字未満の名前を入力してください。", "SSE.Controllers.Main.textNeedSynchronize": "更新があります。", "SSE.Controllers.Main.textNo": "いいえ", "SSE.Controllers.Main.textNoLicenseTitle": "ライセンス制限に達しました", "SSE.Controllers.Main.textPaidFeature": "有料機能", "SSE.Controllers.Main.textPleaseWait": "操作が予想以上に時間がかかります。しばらくお待ちください...", "SSE.Controllers.Main.textReconnect": "接続が回復しました", "SSE.Controllers.Main.textRemember": "すべてのファイルに選択を保存する", "SSE.Controllers.Main.textRememberMacros": "すべてのマクロに、この選択を記憶する", "SSE.Controllers.Main.textRenameError": "ユーザー名は空にできません。", "SSE.Controllers.Main.textRenameLabel": "コラボレーションに使用する名前を入力してください。", "SSE.Controllers.Main.textReplace": "置き換え", "SSE.Controllers.Main.textRequestMacros": "マクロがURLに対してリクエストを行います。%1へのリクエストを許可しますか？", "SSE.Controllers.Main.textShape": "図形", "SSE.Controllers.Main.textStrict": "厳密なモード", "SSE.Controllers.Main.textText": "テキスト", "SSE.Controllers.Main.textTryQuickPrint": "クイックプリントが選択されています。ドキュメント全体が、最後に選択したプリンタまたはデフォルトのプリンタで印刷されます。<br>続行しますか?", "SSE.Controllers.Main.textTryUndoRedo": "即時反映共同編集モードでは元に戻す/やり直しの機能は無効になります。<br>他のユーザーの干渉なし編集するために「厳密モード」をクリックして、厳密な共同編集モードに切り替えてください。保存した後にのみ、変更を送信してください。編集の詳細設定を使用して共同編集モードを切り替えることができます。", "SSE.Controllers.Main.textTryUndoRedoWarn": "即時反映の共同編集モードでは、元に戻す/やり直し機能が無効になります。", "SSE.Controllers.Main.textUndo": "元に戻す", "SSE.Controllers.Main.textUpdateVersion": "この文書は現在編集できません。<br>ファイルを更新しようとしています。しばらくお待ちください...", "SSE.Controllers.Main.textUpdating": "アップデート中", "SSE.Controllers.Main.textYes": "はい", "SSE.Controllers.Main.titleLicenseExp": "ライセンスの有効期限が切れています", "SSE.Controllers.Main.titleLicenseNotActive": "ライセンスが無効になっています", "SSE.Controllers.Main.titleServerVersion": "エディターが更新された", "SSE.Controllers.Main.titleUpdateVersion": "バージョンが変更されました", "SSE.Controllers.Main.txtAccent": "アクセント", "SSE.Controllers.Main.txtAll": "（すべて）", "SSE.Controllers.Main.txtArt": "ここにテキストを入力してください", "SSE.Controllers.Main.txtBasicShapes": "基本図形", "SSE.Controllers.Main.txtBlank": "（空白）", "SSE.Controllers.Main.txtButtons": "ボタン", "SSE.Controllers.Main.txtByField": "%2 の %1", "SSE.Controllers.Main.txtCallouts": "吹き出し", "SSE.Controllers.Main.txtCharts": "グラフ", "SSE.Controllers.Main.txtClearFilter": "フィルタをクリアする", "SSE.Controllers.Main.txtColLbls": "列ラベル", "SSE.Controllers.Main.txtColumn": "列", "SSE.Controllers.Main.txtConfidential": "機密", "SSE.Controllers.Main.txtDate": "日付", "SSE.Controllers.Main.txtDays": "日", "SSE.Controllers.Main.txtDiagramTitle": "グラフのタイトル", "SSE.Controllers.Main.txtEditingMode": "編集モードを設定します...", "SSE.Controllers.Main.txtErrorLoadHistory": "履歴の読み込みに失敗しました。", "SSE.Controllers.Main.txtFiguredArrows": "図形矢印", "SSE.Controllers.Main.txtFile": "ファイル", "SSE.Controllers.Main.txtGrandTotal": "総計", "SSE.Controllers.Main.txtGroup": "グループ", "SSE.Controllers.Main.txtHours": "時間", "SSE.Controllers.Main.txtInfo": "情報", "SSE.Controllers.Main.txtLines": "線", "SSE.Controllers.Main.txtMath": "数学", "SSE.Controllers.Main.txtMinutes": "分", "SSE.Controllers.Main.txtMonths": "月", "SSE.Controllers.Main.txtMultiSelect": "複数選択", "SSE.Controllers.Main.txtNone": "なし", "SSE.Controllers.Main.txtOr": "%1か%2", "SSE.Controllers.Main.txtPage": "ページ", "SSE.Controllers.Main.txtPageOf": "1%/2%ページ", "SSE.Controllers.Main.txtPages": "ページ", "SSE.Controllers.Main.txtPicture": "画像", "SSE.Controllers.Main.txtPivotTable": "ピボットテーブル", "SSE.Controllers.Main.txtPreparedBy": "作成者：", "SSE.Controllers.Main.txtPrintArea": "印刷範囲", "SSE.Controllers.Main.txtQuarter": "四半期", "SSE.Controllers.Main.txtQuarters": "四半期", "SSE.Controllers.Main.txtRectangles": "四角形", "SSE.Controllers.Main.txtRow": "行", "SSE.Controllers.Main.txtRowLbls": "行ラベル", "SSE.Controllers.Main.txtSaveCopyAsComplete": "ファイルのコピーが正常に保存されました", "SSE.Controllers.Main.txtScheme_Aspect": "アスペクト", "SSE.Controllers.Main.txtScheme_Blue": "青色", "SSE.Controllers.Main.txtScheme_Blue_Green": "ブルーグリーン", "SSE.Controllers.Main.txtScheme_Blue_II": "青色II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "ブルーウォーム", "SSE.Controllers.Main.txtScheme_Grayscale": "グレースケール", "SSE.Controllers.Main.txtScheme_Green": "緑色", "SSE.Controllers.Main.txtScheme_Green_Yellow": "黄緑色", "SSE.Controllers.Main.txtScheme_Marquee": "マーキー", "SSE.Controllers.Main.txtScheme_Median": "中位数", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "オレンジ色", "SSE.Controllers.Main.txtScheme_Orange_Red": "オレンジ赤色", "SSE.Controllers.Main.txtScheme_Paper": "紙", "SSE.Controllers.Main.txtScheme_Red": "赤色", "SSE.Controllers.Main.txtScheme_Red_Orange": "オレンジ赤色", "SSE.Controllers.Main.txtScheme_Red_Violet": "赤紫色", "SSE.Controllers.Main.txtScheme_Slipstream": "スリップストリーム", "SSE.Controllers.Main.txtScheme_Violet": "バイオレット色", "SSE.Controllers.Main.txtScheme_Violet_II": "バイオレット II", "SSE.Controllers.Main.txtScheme_Yellow": "黄色", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "オレンジ黄色", "SSE.Controllers.Main.txtSeconds": "秒", "SSE.Controllers.Main.txtSeries": "系列", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "引き出し　１（枠付きと強調線）", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "引き出し　２　（枠付きと強調線）", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "引き出し　３（枠付きと強調線）", "SSE.Controllers.Main.txtShape_accentCallout1": "引き出し線　１（強調線）", "SSE.Controllers.Main.txtShape_accentCallout2": "引き出し　２　（強調線）", "SSE.Controllers.Main.txtShape_accentCallout3": "引き出し　３（強調線）", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "「戻る」ボタン", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "「始めに」ボタン", "SSE.Controllers.Main.txtShape_actionButtonBlank": "「空白」ボタン", "SSE.Controllers.Main.txtShape_actionButtonDocument": "「文書」ボタン", "SSE.Controllers.Main.txtShape_actionButtonEnd": "「最後」ボタン", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "「次へ」ボタン", "SSE.Controllers.Main.txtShape_actionButtonHelp": "「ヘルプ」ボタン", "SSE.Controllers.Main.txtShape_actionButtonHome": "「ホーム」ボタン", "SSE.Controllers.Main.txtShape_actionButtonInformation": "「情報」ボタン", "SSE.Controllers.Main.txtShape_actionButtonMovie": "「動画」ボタン", "SSE.Controllers.Main.txtShape_actionButtonReturn": "「戻る」ボタン", "SSE.Controllers.Main.txtShape_actionButtonSound": "「音」ボタン", "SSE.Controllers.Main.txtShape_arc": "円弧", "SSE.Controllers.Main.txtShape_bentArrow": "曲線の矢印", "SSE.Controllers.Main.txtShape_bentConnector5": "カギ線コネクター", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "カギ線矢印コネクター", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "カギ線の二重矢印コネクター", "SSE.Controllers.Main.txtShape_bentUpArrow": "曲線の矢印（上）", "SSE.Controllers.Main.txtShape_bevel": "額縁", "SSE.Controllers.Main.txtShape_blockArc": "アーチ", "SSE.Controllers.Main.txtShape_borderCallout1": "引き出し　１　", "SSE.Controllers.Main.txtShape_borderCallout2": "引き出し　２", "SSE.Controllers.Main.txtShape_borderCallout3": "引き出し　３", "SSE.Controllers.Main.txtShape_bracePair": "中かっこ", "SSE.Controllers.Main.txtShape_callout1": "引き出し　１（枠付き無し）", "SSE.Controllers.Main.txtShape_callout2": "引き出し　２（枠付き無し）", "SSE.Controllers.Main.txtShape_callout3": "引き出し　３（枠付き無し）", "SSE.Controllers.Main.txtShape_can": "円柱", "SSE.Controllers.Main.txtShape_chevron": "シェブロン", "SSE.Controllers.Main.txtShape_chord": "コード", "SSE.Controllers.Main.txtShape_circularArrow": "円弧の矢印", "SSE.Controllers.Main.txtShape_cloud": "クラウド", "SSE.Controllers.Main.txtShape_cloudCallout": "雲形吹き出し", "SSE.Controllers.Main.txtShape_corner": "角", "SSE.Controllers.Main.txtShape_cube": "立方体", "SSE.Controllers.Main.txtShape_curvedConnector3": "曲線コネクタ", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "曲線矢印コネクタ", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "曲線の二重矢印コネクタ", "SSE.Controllers.Main.txtShape_curvedDownArrow": "曲線の下向きの矢印", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "曲線の左矢印", "SSE.Controllers.Main.txtShape_curvedRightArrow": "曲線の右矢印", "SSE.Controllers.Main.txtShape_curvedUpArrow": "曲線の上矢印", "SSE.Controllers.Main.txtShape_decagon": "十角形", "SSE.Controllers.Main.txtShape_diagStripe": "斜めストライプ", "SSE.Controllers.Main.txtShape_diamond": "ひし型", "SSE.Controllers.Main.txtShape_dodecagon": "12角形", "SSE.Controllers.Main.txtShape_donut": "ドーナツ グラフ", "SSE.Controllers.Main.txtShape_doubleWave": "二重波", "SSE.Controllers.Main.txtShape_downArrow": "下矢印", "SSE.Controllers.Main.txtShape_downArrowCallout": "下矢印引き出し", "SSE.Controllers.Main.txtShape_ellipse": "楕円", "SSE.Controllers.Main.txtShape_ellipseRibbon": "曲線下向けのリボン", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "曲線上向けのリボン", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "フローチャート：代替処理", "SSE.Controllers.Main.txtShape_flowChartCollate": "フローチャート：照合", "SSE.Controllers.Main.txtShape_flowChartConnector": "フローチャート：コネクタ", "SSE.Controllers.Main.txtShape_flowChartDecision": "フローチャート：判断", "SSE.Controllers.Main.txtShape_flowChartDelay": "フローチャート：遅延", "SSE.Controllers.Main.txtShape_flowChartDisplay": "フローチャート：表示", "SSE.Controllers.Main.txtShape_flowChartDocument": "フローチャート：文書", "SSE.Controllers.Main.txtShape_flowChartExtract": "フローチャート：抜き出し", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "フローチャート：データ", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "フローチャート：内部ストレージ", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "フローチャート：磁気ディスク", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "フローチャート：直接アクセスのストレージ", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "フローチャート：順次アクセス記憶", "SSE.Controllers.Main.txtShape_flowChartManualInput": "フローチャート：手動入力", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "フローチャート：手作業", "SSE.Controllers.Main.txtShape_flowChartMerge": "フローチャート：統合", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "フローチャート：複数文書", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "フローチャート：他ページへのリンク", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "フローチャート：保存されたデータ", "SSE.Controllers.Main.txtShape_flowChartOr": "フローチャート: 論理和", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "フローチャート：事前定義されたプロセス", "SSE.Controllers.Main.txtShape_flowChartPreparation": "フローチャート：準備", "SSE.Controllers.Main.txtShape_flowChartProcess": "フローチャート：プロセス", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "フローチャート：カード", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "フローチャート: せん孔テープ", "SSE.Controllers.Main.txtShape_flowChartSort": "フローチャート：並べ替え", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "フローチャート：和接合", "SSE.Controllers.Main.txtShape_flowChartTerminator": "フローチャート：端子", "SSE.Controllers.Main.txtShape_foldedCorner": "折り曲げコーナー", "SSE.Controllers.Main.txtShape_frame": "フレーム", "SSE.Controllers.Main.txtShape_halfFrame": "半フレーム", "SSE.Controllers.Main.txtShape_heart": "ハート", "SSE.Controllers.Main.txtShape_heptagon": "七角形", "SSE.Controllers.Main.txtShape_hexagon": "六角形", "SSE.Controllers.Main.txtShape_homePlate": "五角形", "SSE.Controllers.Main.txtShape_horizontalScroll": "水平スクロール", "SSE.Controllers.Main.txtShape_irregularSeal1": "爆発　１", "SSE.Controllers.Main.txtShape_irregularSeal2": "爆発　２", "SSE.Controllers.Main.txtShape_leftArrow": "左矢印", "SSE.Controllers.Main.txtShape_leftArrowCallout": "左矢印引き出し", "SSE.Controllers.Main.txtShape_leftBrace": "左中かっこ", "SSE.Controllers.Main.txtShape_leftBracket": "左かっこ", "SSE.Controllers.Main.txtShape_leftRightArrow": "左右矢印", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "左右矢印引き出し", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "三方向矢印(左・右・上）", "SSE.Controllers.Main.txtShape_leftUpArrow": "左上矢印", "SSE.Controllers.Main.txtShape_lightningBolt": "稲妻", "SSE.Controllers.Main.txtShape_line": "線", "SSE.Controllers.Main.txtShape_lineWithArrow": "矢印", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "二重矢印", "SSE.Controllers.Main.txtShape_mathDivide": "除法", "SSE.Controllers.Main.txtShape_mathEqual": "等しい", "SSE.Controllers.Main.txtShape_mathMinus": "マイナス", "SSE.Controllers.Main.txtShape_mathMultiply": "乗算", "SSE.Controllers.Main.txtShape_mathNotEqual": "不等号", "SSE.Controllers.Main.txtShape_mathPlus": "プラス", "SSE.Controllers.Main.txtShape_moon": "月形", "SSE.Controllers.Main.txtShape_noSmoking": "「禁止」マーク", "SSE.Controllers.Main.txtShape_notchedRightArrow": "切り欠き右矢印", "SSE.Controllers.Main.txtShape_octagon": "八角形", "SSE.Controllers.Main.txtShape_parallelogram": "平行四辺形", "SSE.Controllers.Main.txtShape_pentagon": "五角形", "SSE.Controllers.Main.txtShape_pie": "円グラフ", "SSE.Controllers.Main.txtShape_plaque": "ブローチ", "SSE.Controllers.Main.txtShape_plus": "プラス", "SSE.Controllers.Main.txtShape_polyline1": "殴り書き", "SSE.Controllers.Main.txtShape_polyline2": "フリーフォーム", "SSE.Controllers.Main.txtShape_quadArrow": "四方向矢印", "SSE.Controllers.Main.txtShape_quadArrowCallout": "四方向矢印の吹き出し", "SSE.Controllers.Main.txtShape_rect": "矩形", "SSE.Controllers.Main.txtShape_ribbon": "下リボン", "SSE.Controllers.Main.txtShape_ribbon2": "上リボン", "SSE.Controllers.Main.txtShape_rightArrow": "右矢印", "SSE.Controllers.Main.txtShape_rightArrowCallout": "右矢印引き出し", "SSE.Controllers.Main.txtShape_rightBrace": "右中かっこ", "SSE.Controllers.Main.txtShape_rightBracket": "右かっこ", "SSE.Controllers.Main.txtShape_round1Rect": "1つの角を丸めた四角形", "SSE.Controllers.Main.txtShape_round2DiagRect": "対角する 2 つの角を丸めた四角形", "SSE.Controllers.Main.txtShape_round2SameRect": "片側の 2 つの角を丸めた四角形", "SSE.Controllers.Main.txtShape_roundRect": "角を丸めた四角形", "SSE.Controllers.Main.txtShape_rtTriangle": "直角三角形", "SSE.Controllers.Main.txtShape_smileyFace": "スマイル", "SSE.Controllers.Main.txtShape_snip1Rect": "1つの角を切り取った四角形", "SSE.Controllers.Main.txtShape_snip2DiagRect": "対角する2つの角を切り取った四角形", "SSE.Controllers.Main.txtShape_snip2SameRect": "片側の2つの角を切り取った四角形", "SSE.Controllers.Main.txtShape_snipRoundRect": "1つの角を切り取り1つの角を丸めた四角形", "SSE.Controllers.Main.txtShape_spline": "曲線", "SSE.Controllers.Main.txtShape_star10": "星10", "SSE.Controllers.Main.txtShape_star12": "星12", "SSE.Controllers.Main.txtShape_star16": "星16", "SSE.Controllers.Main.txtShape_star24": "星24", "SSE.Controllers.Main.txtShape_star32": "星32", "SSE.Controllers.Main.txtShape_star4": "星4", "SSE.Controllers.Main.txtShape_star5": "星5", "SSE.Controllers.Main.txtShape_star6": "星6", "SSE.Controllers.Main.txtShape_star7": "星7", "SSE.Controllers.Main.txtShape_star8": "星8", "SSE.Controllers.Main.txtShape_stripedRightArrow": "ストライプの右矢印", "SSE.Controllers.Main.txtShape_sun": "太陽形", "SSE.Controllers.Main.txtShape_teardrop": "滴", "SSE.Controllers.Main.txtShape_textRect": "テキストボックス", "SSE.Controllers.Main.txtShape_trapezoid": "台形", "SSE.Controllers.Main.txtShape_triangle": "三角", "SSE.Controllers.Main.txtShape_upArrow": "上矢印", "SSE.Controllers.Main.txtShape_upArrowCallout": "上矢印引き出し", "SSE.Controllers.Main.txtShape_upDownArrow": "上下の双方向矢印", "SSE.Controllers.Main.txtShape_uturnArrow": "U形矢印", "SSE.Controllers.Main.txtShape_verticalScroll": "垂直スクロール", "SSE.Controllers.Main.txtShape_wave": "波", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "円形吹き出し", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "長方形の吹き出し", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "角丸長方形の引き出し", "SSE.Controllers.Main.txtSheet": "シート", "SSE.Controllers.Main.txtSlicer": "スライサー", "SSE.Controllers.Main.txtStarsRibbons": "スター＆リボン", "SSE.Controllers.Main.txtStyle_Bad": "悪い", "SSE.Controllers.Main.txtStyle_Calculation": "計算", "SSE.Controllers.Main.txtStyle_Check_Cell": "チェックセル", "SSE.Controllers.Main.txtStyle_Comma": "カンマ", "SSE.Controllers.Main.txtStyle_Currency": "通貨", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "説明文", "SSE.Controllers.Main.txtStyle_Good": "良い", "SSE.Controllers.Main.txtStyle_Heading_1": "見出し１", "SSE.Controllers.Main.txtStyle_Heading_2": "見出し２", "SSE.Controllers.Main.txtStyle_Heading_3": "見出し３", "SSE.Controllers.Main.txtStyle_Heading_4": "見出し４", "SSE.Controllers.Main.txtStyle_Input": "入力", "SSE.Controllers.Main.txtStyle_Linked_Cell": "リンクされたセル", "SSE.Controllers.Main.txtStyle_Neutral": "ニュートラル", "SSE.Controllers.Main.txtStyle_Normal": "標準", "SSE.Controllers.Main.txtStyle_Note": "注意", "SSE.Controllers.Main.txtStyle_Output": "出力", "SSE.Controllers.Main.txtStyle_Percent": "パーセント", "SSE.Controllers.Main.txtStyle_Title": "表題", "SSE.Controllers.Main.txtStyle_Total": "合計", "SSE.Controllers.Main.txtStyle_Warning_Text": "警告テキスト", "SSE.Controllers.Main.txtTab": "タブ", "SSE.Controllers.Main.txtTable": "表", "SSE.Controllers.Main.txtTime": "時刻", "SSE.Controllers.Main.txtUnlock": "ロックを解除する", "SSE.Controllers.Main.txtUnlockRange": "範囲のロック解除", "SSE.Controllers.Main.txtUnlockRangeDescription": "範囲を変更するようにパスワードを入力してください", "SSE.Controllers.Main.txtUnlockRangeWarning": "変更しようとしている範囲がパスワードで保護されています。", "SSE.Controllers.Main.txtValues": "値", "SSE.Controllers.Main.txtView": "表示", "SSE.Controllers.Main.txtXAxis": "X 軸", "SSE.Controllers.Main.txtYAxis": "Y軸", "SSE.Controllers.Main.txtYears": "年", "SSE.Controllers.Main.unknownErrorText": "不明なエラー", "SSE.Controllers.Main.unsupportedBrowserErrorText": "お使いのブラウザがサポートされていません。", "SSE.Controllers.Main.uploadDocExtMessage": "不明な文書形式", "SSE.Controllers.Main.uploadDocFileCountMessage": "アップロードされた文書がありません", "SSE.Controllers.Main.uploadDocSizeMessage": "文書の最大サイズ制限を超えました", "SSE.Controllers.Main.uploadImageExtMessage": "不明な画像形式", "SSE.Controllers.Main.uploadImageFileCountMessage": "アップロードした画像なし", "SSE.Controllers.Main.uploadImageSizeMessage": "イメージのサイズの上限が超えさせました。サイズの上限が２５MB。", "SSE.Controllers.Main.uploadImageTextText": "イメージをアップロードしています...", "SSE.Controllers.Main.uploadImageTitleText": "イメージをアップロードしています", "SSE.Controllers.Main.waitText": "少々お待ちください...", "SSE.Controllers.Main.warnBrowserIE9": "IE9にアプリケーションの機能のレベルが低いです。IE10または次のバージョンを使ってください。", "SSE.Controllers.Main.warnBrowserZoom": "お使いのブラウザの現在のズームの設定は完全にサポートされていません。Ctrl+0を押して、デフォルトのズームにリセットしてください。", "SSE.Controllers.Main.warnLicenseAnonymous": "匿名ユーザーのアクセスは拒否されます。<br>このドキュメントは閲覧専用に開かれます。", "SSE.Controllers.Main.warnLicenseBefore": "ライセンスが無効になっています。<br>管理者までご連絡ください。", "SSE.Controllers.Main.warnLicenseExceeded": "％1エディターへの同時接続の制限に達しました。 このドキュメントは表示専用で開かれます。<br>詳細については、管理者にお問い合わせください。", "SSE.Controllers.Main.warnLicenseExp": "ライセンスの有効期限が切れています。<br>ライセンスを更新してページを再読み込みしてください。", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "ライセンスの有効期限が切れています。<br>ドキュメント編集機能にアクセスできません。<br>管理者にご連絡ください。", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "ライセンスを更新する必要があります。<br>ドキュメント編集機能へのアクセスが制限されています。<br>フルアクセスを取得するには、管理者にご連絡ください", "SSE.Controllers.Main.warnLicenseUsersExceeded": "％1エディターのユーザー制限に達しました。 詳細については、管理者にお問い合わせください。", "SSE.Controllers.Main.warnNoLicense": "％1エディターへの同時接続の制限に達しました。 このドキュメントは閲覧のみを目的として開かれます。<br>個人的なアップグレード条件については、％1セールスチームにお問い合わせください。", "SSE.Controllers.Main.warnNoLicenseUsers": "％1エディターのユーザー制限に達しました。 個人的なアップグレード条件については、％1営業チームにお問い合わせください。", "SSE.Controllers.Main.warnProcessRightsChange": "ファイルを編集する権限を拒否されています。", "SSE.Controllers.PivotTable.strSheet": "シート", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "項目を追加または変更できません。ピボットテーブルレポートのフィルタにこのフィールドがあります。", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "このアクティブセルでは、計算項目に対する操作は許可されていません。", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "ピボットテーブルに計算された項目がある場合、フィールドをデータエリアで2回以上使用したり、データエリアと別のエリアで同時に使用したりすることはできません。", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "計算項目はカスタム小計では動作しません。", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "項目名が見つかりません。名前が正しく入力されているか確認し、その項目がピボットテーブルレポートに存在しているか確認してください。", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "ピボットテーブルレポートで計算された項目がある場合、平均、標準偏差、分散はサポートされません。", "SSE.Controllers.Print.strAllSheets": "全シート", "SSE.Controllers.Print.textFirstCol": "最初の列", "SSE.Controllers.Print.textFirstRow": "最初の行", "SSE.Controllers.Print.textFrozenCols": "固定された列", "SSE.Controllers.Print.textFrozenRows": "固定された行", "SSE.Controllers.Print.textInvalidRange": "エラー！セルの範囲は無効です。", "SSE.Controllers.Print.textNoRepeat": "繰り返なし", "SSE.Controllers.Print.textRepeat": "繰り返す．．．", "SSE.Controllers.Print.textSelectRange": "範囲の選択", "SSE.Controllers.Print.txtCustom": "ユーザー設定", "SSE.Controllers.Print.txtZoomToPage": "ページ全体に合わせる", "SSE.Controllers.Search.textInvalidRange": "エラー！セルの範囲が正しくありません", "SSE.Controllers.Search.textNoTextFound": "検索データが見つかりませんでした。検索オプションを変更してください。", "SSE.Controllers.Search.textReplaceSkipped": "置換が行われました。スキップされた発生回数は{0}です。", "SSE.Controllers.Search.textReplaceSuccess": "検索が実行されました。{0}発生が置換されました", "SSE.Controllers.Statusbar.errorLastSheet": "最低 1 つのワークシートが含まれていなければなりません。", "SSE.Controllers.Statusbar.errorRemoveSheet": "ワークシートを削除することができません。", "SSE.Controllers.Statusbar.strSheet": "シート", "SSE.Controllers.Statusbar.textDisconnect": "<b>接続が切断されました</b><br>接続を試みています。接続設定を確認してください。", "SSE.Controllers.Statusbar.textSheetViewTip": "シートビューモードになっています。 フィルタと並べ替えは、あなたとまだこのビューにいる人だけに表示されます。", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "シート表示モードになっています。 フィルタは、あなたとまだこの表示にいる人だけに表示されます。", "SSE.Controllers.Statusbar.warnDeleteSheet": "選択したシートにはデータが含まれている可能性があります。続行してもよろしいですか？", "SSE.Controllers.Statusbar.zoomText": "ズーム{0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "保存しようとしているフォントを現在のデバイスで使用することができません。<br>システムフォントを使って、テキストのスタイルが表示されます。利用できます時、保存されたフォントが使用されます。<br>続行しますか。", "SSE.Controllers.Toolbar.errorComboSeries": "組み合わせグラフを作成するには、最低2つのデータを選択します。", "SSE.Controllers.Toolbar.errorMaxPoints": "グラフごとの直列のポイントの最大数は4096です。", "SSE.Controllers.Toolbar.errorMaxRows": "エラー！使用可能なデータ系列の数は、1グラフあたり最大255個です。", "SSE.Controllers.Toolbar.errorStockChart": "行の順序が正しくありません。この株価チャートを作成するには、<br>始値、高値、安値、終値の順でシートのデータを配置してください。", "SSE.Controllers.Toolbar.helpCalcItems": "ピボットテーブルの計算済み項目で作業します。", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "計算項目", "SSE.Controllers.Toolbar.helpFastUndo": "高速モードでシートを共同編集している際に、変更を簡単に元に戻すことができます。", "SSE.Controllers.Toolbar.helpFastUndoHeader": "リアルタイム共同編集で「元に戻す」", "SSE.Controllers.Toolbar.helpMergeShapes": "カスタムビジュアルを作成するために、形状を結合、断片化、交差、減算する操作を数秒で行うことができます。", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "図形を結合", "SSE.Controllers.Toolbar.textAccent": "ダイアクリティカル・マーク", "SSE.Controllers.Toolbar.textBracket": "括弧", "SSE.Controllers.Toolbar.textDirectional": "方向", "SSE.Controllers.Toolbar.textFontSizeErr": "入力された値が正しくありません。<br>1〜409の数値を入力してください。", "SSE.Controllers.Toolbar.textFraction": "分数", "SSE.Controllers.Toolbar.textFunction": "関数", "SSE.Controllers.Toolbar.textIndicator": "インジケーター", "SSE.Controllers.Toolbar.textInsert": "挿入", "SSE.Controllers.Toolbar.textIntegral": "積分", "SSE.Controllers.Toolbar.textLargeOperator": "大型演算子", "SSE.Controllers.Toolbar.textLimitAndLog": "極限と対数", "SSE.Controllers.Toolbar.textLongOperation": "長時間の操作", "SSE.Controllers.Toolbar.textMatrix": "行列", "SSE.Controllers.Toolbar.textOperator": "演算子", "SSE.Controllers.Toolbar.textPivot": "ピボットテーブル", "SSE.Controllers.Toolbar.textRadical": "冪根", "SSE.Controllers.Toolbar.textRating": "評価", "SSE.Controllers.Toolbar.textRecentlyUsed": "最近使った項目", "SSE.Controllers.Toolbar.textScript": "スクリプト", "SSE.Controllers.Toolbar.textShapes": "図形", "SSE.Controllers.Toolbar.textSymbols": "記号と特殊文字", "SSE.Controllers.Toolbar.textWarning": "警告", "SSE.Controllers.Toolbar.txtAccent_Accent": "アキュート", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "左右双方向矢印 (上)", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "左に矢印 (上)", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "右向き矢印 (上)", "SSE.Controllers.Toolbar.txtAccent_Bar": "横棒グラフ", "SSE.Controllers.Toolbar.txtAccent_BarBot": "下の棒", "SSE.Controllers.Toolbar.txtAccent_BarTop": "上の棒", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "四角囲み数式 (プレースホルダ付き)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "四角囲み数式 (例)", "SSE.Controllers.Toolbar.txtAccent_Check": "チェック", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "下かっこ", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "上かっこ", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "ベクトル A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "上線付きABC", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XORと上線", "SSE.Controllers.Toolbar.txtAccent_DDDot": "３重ドット", "SSE.Controllers.Toolbar.txtAccent_DDot": "二重ドット", "SSE.Controllers.Toolbar.txtAccent_Dot": "点", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "二重上線", "SSE.Controllers.Toolbar.txtAccent_Grave": "グレーブ・アクセント", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "グループ化文字(下)", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "グループ化文字(上)", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "左半矢印(上)", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "右向き半矢印 (上)", "SSE.Controllers.Toolbar.txtAccent_Hat": "ハット", "SSE.Controllers.Toolbar.txtAccent_Smile": "ブリーブ", "SSE.Controllers.Toolbar.txtAccent_Tilde": "チルダ", "SSE.Controllers.Toolbar.txtBracket_Angle": "括弧", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "括弧と区切り記号", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "括弧と区切り記号", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "終わり山かっこ", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "単一かっこ", "SSE.Controllers.Toolbar.txtBracket_Curve": "括弧", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "括弧と区切り記号", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "右中かっこ", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "単一かっこ", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "場合分け(条件2つ)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "場合分け (条件3つ)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "縦並びオブジェクト", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "縦並びオブジェクト (かっこ付き)", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "場合分けの例", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "二項係数", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "二項係数", "SSE.Controllers.Toolbar.txtBracket_Line": "縦棒", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "縦棒 (右のみ)", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "縦棒 (左のみ)", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "括弧", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "二重縦棒 (右のみ)", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "二重縦棒 (左のみ)", "SSE.Controllers.Toolbar.txtBracket_LowLim": "括弧", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "床関数 (右記号)", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "床関数 (左記号)", "SSE.Controllers.Toolbar.txtBracket_Round": "括弧", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "括弧と区切り記号", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "右かっこ", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "左かっこ", "SSE.Controllers.Toolbar.txtBracket_Square": "大かっこ", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "右の角括弧の間のプレースホルダー", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "反転した角括弧", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "右角かっこ", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "左角かっこ", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "左の角括弧の間のプレースホルダー", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "括弧", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "右ダブル角型かっこ", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "左ダブル角型かっこ", "SSE.Controllers.Toolbar.txtBracket_UppLim": "括弧", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "天井関数 (右記号)", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "単一かっこ", "SSE.Controllers.Toolbar.txtDeleteCells": "セルを削除", "SSE.Controllers.Toolbar.txtExpand": "拡張と並べ替え", "SSE.Controllers.Toolbar.txtExpandSort": "選択範囲の横のデータは並べ替えられません。 選択範囲を拡張して隣接するデータを含めるか、現在選択されているセルのみの並べ替えを続行しますか？", "SSE.Controllers.Toolbar.txtFractionDiagonal": "分数 (斜め)", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "微分", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "微分", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "部分的なxに対する部分的なy", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "微分", "SSE.Controllers.Toolbar.txtFractionHorizontal": "分数 (横)", "SSE.Controllers.Toolbar.txtFractionPi_2": "円周率を2で割る", "SSE.Controllers.Toolbar.txtFractionSmall": "分数 (小)", "SSE.Controllers.Toolbar.txtFractionVertical": "分数 (縦)", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "逆余弦関数", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "逆双曲線余弦", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "逆余接関数", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "双曲線逆余接", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "逆余割関数", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "逆双曲線余割関数", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "逆正割関数", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "逆双曲線正割", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "逆正弦関数", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "双曲線逆正弦関数", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "逆正接関数", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "双曲線逆正接関数", "SSE.Controllers.Toolbar.txtFunction_Cos": "余弦関数", "SSE.Controllers.Toolbar.txtFunction_Cosh": "双曲線余弦関数", "SSE.Controllers.Toolbar.txtFunction_Cot": "余接関数", "SSE.Controllers.Toolbar.txtFunction_Coth": "双曲線余接関数", "SSE.Controllers.Toolbar.txtFunction_Csc": "余割関数\t", "SSE.Controllers.Toolbar.txtFunction_Csch": "双曲線余割関数", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sin θ", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "正接数式", "SSE.Controllers.Toolbar.txtFunction_Sec": "正割関数", "SSE.Controllers.Toolbar.txtFunction_Sech": "双曲線正割関数", "SSE.Controllers.Toolbar.txtFunction_Sin": "正弦関数", "SSE.Controllers.Toolbar.txtFunction_Sinh": "双曲線正弦関数", "SSE.Controllers.Toolbar.txtFunction_Tan": "逆正接関数", "SSE.Controllers.Toolbar.txtFunction_Tanh": "双曲線正接関数", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "ユーザー設定", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "データとモデル", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "良い、悪い、どちらでもない", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "名前なし", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "数値の書式", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "テーマのセル スタイル", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "タイトルと見出し", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "ユーザー設定", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "ダーク", "SSE.Controllers.Toolbar.txtGroupTable_Light": "ライト", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "中", "SSE.Controllers.Toolbar.txtInsertCells": "セルを挿入", "SSE.Controllers.Toolbar.txtIntegral": "積分", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "微分 dθ", "SSE.Controllers.Toolbar.txtIntegral_dx": "微分ｄx", "SSE.Controllers.Toolbar.txtIntegral_dy": "微分 ｄy", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "積分", "SSE.Controllers.Toolbar.txtIntegralDouble": "二重積分", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "二重積分", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "二重積分", "SSE.Controllers.Toolbar.txtIntegralOriented": "周回積分", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "周回積分", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "面積分", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "面積分 (上下端値を上下に配置)", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "面積分 (上下端値あり)", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "周回積分", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "体積積分", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "体積積分 (上下端値を上下に配置)", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "体積積分 (上下端値あり)", "SSE.Controllers.Toolbar.txtIntegralSubSup": "積分", "SSE.Controllers.Toolbar.txtIntegralTriple": "三重積分", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "三重積分 (上下端値を上下に配置)", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "三重積分 (上下端値あり)", "SSE.Controllers.Toolbar.txtInvalidRange": "エラー！セルの範囲が正しくありません。", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "論理積", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "論理積 (下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "論理積 (上下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "論理積 (下付き文字の下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "論理積 (上付き/下付き文字の上下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "余積", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "下端付き余積", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "極限付き余積", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "下端下付き双対積", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "上下付き極限付き双対積", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "n から k を選ぶ場合の k の総和", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "総和 (i = 0 から n まで)", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "添え字 2 個を使う総和の例", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "積の例", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "和集合の例", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "論理和", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "論理和 (下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "論理和 (上下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "論理和 (下付き文字の下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "論理和 (上付き/下付き文字の上下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "共通集合", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "積集合 (下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "積集合 (上下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "積集合 (下付き文字の下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "積集合 (上付き/下付き文字の上下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "乗積", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "積 (下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "積 (上下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "積 (下付き文字の下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "積 (上付き/下付き文字の上下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "合計", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "総和 (下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "総和 (上下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "総和 (下付き文字の下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "総和 (上付き/下付き文字の上下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "和集合", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "和集合 (下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "和集合 (上下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "和集合 (下付き文字の下端値あり)", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "和集合 (下付き/上付き文字の上下端値あり)", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "極限の例", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "最大値の例", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "極限", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "自然対数", "SSE.Controllers.Toolbar.txtLimitLog_Log": "対数", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "対数", "SSE.Controllers.Toolbar.txtLimitLog_Max": "最大", "SSE.Controllers.Toolbar.txtLimitLog_Min": "最小", "SSE.Controllers.Toolbar.txtLockSort": "選択の範囲の近くにデータが見つけられたけどこのセルを変更するに十分なアクセス許可がありません。<br> 選択の範囲を続行してもよろしいですか？", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2空行列", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3空行列", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 空行列", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 空行列", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "かっこ付き空行列", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "かっこ付き空行列", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "かっこ付き空行列", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "かっこ付き空行列", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 空行列", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 空行列", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 空行列", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 空行列", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "ベースライン ドット", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "ミッドライン・ドット", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "斜めドット", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "縦向きドット", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "疎行列 (かっこ付き)", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "疎行列 (大かっこ付き)", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 単位行列", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "3x3 単位行列", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 単位行列", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 単位行列", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "左右双方向矢印 (下)", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "左右双方向矢印 (上)", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "左に矢印 (下)", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "左に矢印 (上)", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "右向き矢印 (下)", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "右向き矢印 (上)", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "コロン付き等号", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "導出", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "誤差導出", "SSE.Controllers.Toolbar.txtOperator_Definition": "定義により等しい", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "デルタは等しい", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "左右双方向矢印 (下)", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "左右双方向矢印 (上)", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "左に矢印 (下)", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "左に矢印 (上)", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "右向き矢印 (下)", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "右向き矢印 (上)", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "等号等号", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "マイナス付き等号", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "プラス付き等号", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "測度", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "二次方程式の解の公式の右辺", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "a の 2 乗と b の 2 乗の和の平方根", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "次数付き平方根", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "立方根", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "次数付きべき乗根", "SSE.Controllers.Toolbar.txtRadicalSqrt": "平方根", "SSE.Controllers.Toolbar.txtScriptCustom_1": "x 下付き文字 y の 2 乗", "SSE.Controllers.Toolbar.txtScriptCustom_2": "スクリプト", "SSE.Controllers.Toolbar.txtScriptCustom_3": "x の 2 乗", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Y 左上付き文字 n 左下付き文字 1", "SSE.Controllers.Toolbar.txtScriptSub": "下付き", "SSE.Controllers.Toolbar.txtScriptSubSup": "下付き文字 - 上付き文字", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "左下付き文字 - 上付き文字", "SSE.Controllers.Toolbar.txtScriptSup": "上付き", "SSE.Controllers.Toolbar.txtSorting": "並べ替え", "SSE.Controllers.Toolbar.txtSortSelected": "選択した内容を並べ替える", "SSE.Controllers.Toolbar.txtSymbol_about": "近似", "SSE.Controllers.Toolbar.txtSymbol_additional": "補集合", "SSE.Controllers.Toolbar.txtSymbol_aleph": "アレフ", "SSE.Controllers.Toolbar.txtSymbol_alpha": "アルファ", "SSE.Controllers.Toolbar.txtSymbol_approx": "ほぼ等しい", "SSE.Controllers.Toolbar.txtSymbol_ast": "アスタリスク", "SSE.Controllers.Toolbar.txtSymbol_beta": "ベータ", "SSE.Controllers.Toolbar.txtSymbol_beth": "ベート", "SSE.Controllers.Toolbar.txtSymbol_bullet": "箇条書きの演算子", "SSE.Controllers.Toolbar.txtSymbol_cap": "共通集合", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "立方根", "SSE.Controllers.Toolbar.txtSymbol_cdots": "水平中央の省略記号", "SSE.Controllers.Toolbar.txtSymbol_celsius": "摂氏", "SSE.Controllers.Toolbar.txtSymbol_chi": "カイ", "SSE.Controllers.Toolbar.txtSymbol_cong": "ほぼ等しい", "SSE.Controllers.Toolbar.txtSymbol_cup": "和集合", "SSE.Controllers.Toolbar.txtSymbol_ddots": "下右斜めの省略記号", "SSE.Controllers.Toolbar.txtSymbol_degree": "度", "SSE.Controllers.Toolbar.txtSymbol_delta": "デルタ", "SSE.Controllers.Toolbar.txtSymbol_div": "「除算」記号", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "下矢印", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "空集合", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "イプシロン", "SSE.Controllers.Toolbar.txtSymbol_equals": "等しい", "SSE.Controllers.Toolbar.txtSymbol_equiv": "恒等", "SSE.Controllers.Toolbar.txtSymbol_eta": "エータ", "SSE.Controllers.Toolbar.txtSymbol_exists": "存在します\t", "SSE.Controllers.Toolbar.txtSymbol_factorial": "階乗", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "華氏", "SSE.Controllers.Toolbar.txtSymbol_forall": "全てに", "SSE.Controllers.Toolbar.txtSymbol_gamma": "ガンマ", "SSE.Controllers.Toolbar.txtSymbol_geq": "次の値より大きいか等しい", "SSE.Controllers.Toolbar.txtSymbol_gg": "次の値よりはるかに大きい", "SSE.Controllers.Toolbar.txtSymbol_greater": "次の値より大きい", "SSE.Controllers.Toolbar.txtSymbol_in": "属する", "SSE.Controllers.Toolbar.txtSymbol_inc": "増分", "SSE.Controllers.Toolbar.txtSymbol_infinity": "無限大", "SSE.Controllers.Toolbar.txtSymbol_iota": "イオタ", "SSE.Controllers.Toolbar.txtSymbol_kappa": "カッパ", "SSE.Controllers.Toolbar.txtSymbol_lambda": "ラムダ", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "左矢印", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "左右矢印", "SSE.Controllers.Toolbar.txtSymbol_leq": "次の値より小さいか等しい", "SSE.Controllers.Toolbar.txtSymbol_less": "次の値より小さい", "SSE.Controllers.Toolbar.txtSymbol_ll": "次の値よりはるかに小さい", "SSE.Controllers.Toolbar.txtSymbol_minus": "マイナス", "SSE.Controllers.Toolbar.txtSymbol_mp": "マイナスプラス", "SSE.Controllers.Toolbar.txtSymbol_mu": "ミュー", "SSE.Controllers.Toolbar.txtSymbol_nabla": "ナブラ", "SSE.Controllers.Toolbar.txtSymbol_neq": "と等しくない", "SSE.Controllers.Toolbar.txtSymbol_ni": "含む", "SSE.Controllers.Toolbar.txtSymbol_not": "「否定」記号", "SSE.Controllers.Toolbar.txtSymbol_notexists": "存在しません", "SSE.Controllers.Toolbar.txtSymbol_nu": "ニュー", "SSE.Controllers.Toolbar.txtSymbol_o": "オミクロン", "SSE.Controllers.Toolbar.txtSymbol_omega": "オメガ", "SSE.Controllers.Toolbar.txtSymbol_partial": "偏微分方程式", "SSE.Controllers.Toolbar.txtSymbol_percent": "パーセンテージ", "SSE.Controllers.Toolbar.txtSymbol_phi": "ファイ", "SSE.Controllers.Toolbar.txtSymbol_pi": "パイ", "SSE.Controllers.Toolbar.txtSymbol_plus": "プラス", "SSE.Controllers.Toolbar.txtSymbol_pm": "プラスとマイナス", "SSE.Controllers.Toolbar.txtSymbol_propto": "比例", "SSE.Controllers.Toolbar.txtSymbol_psi": "プサイ", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "四乗根", "SSE.Controllers.Toolbar.txtSymbol_qed": "証明終了", "SSE.Controllers.Toolbar.txtSymbol_rddots": "斜め(右上)の省略記号", "SSE.Controllers.Toolbar.txtSymbol_rho": "ロー", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "右矢印", "SSE.Controllers.Toolbar.txtSymbol_sigma": "シグマ", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "根号", "SSE.Controllers.Toolbar.txtSymbol_tau": "タウ", "SSE.Controllers.Toolbar.txtSymbol_therefore": "従って", "SSE.Controllers.Toolbar.txtSymbol_theta": "シータ", "SSE.Controllers.Toolbar.txtSymbol_times": "「乗算」記号", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "上矢印", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "ウプシロン", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "イプシロン (別形)", "SSE.Controllers.Toolbar.txtSymbol_varphi": "ファイ (別形)", "SSE.Controllers.Toolbar.txtSymbol_varpi": "パイ", "SSE.Controllers.Toolbar.txtSymbol_varrho": "ロー (別形)", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "シグマ (別形)", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "シータ (別形)", "SSE.Controllers.Toolbar.txtSymbol_vdots": "垂直線の省略記号", "SSE.Controllers.Toolbar.txtSymbol_xsi": "グザイ", "SSE.Controllers.Toolbar.txtSymbol_zeta": "ゼータ", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "表のスタイル：暗", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "表のスタイル：明るい", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "表のスタイル：中", "SSE.Controllers.Toolbar.warnLongOperation": "実行しようとしている操作は、完了するまでにかなり時間がかかる可能性があります。<br>続行しますか？", "SSE.Controllers.Toolbar.warnMergeLostData": "セルを結合すると、左上の値のみが保持され、他のセルの値は破棄されます。<br>続行してもよろしいです？", "SSE.Controllers.Toolbar.warnNoRecommended": "グラフを作成するには、使用したいデータを含むセルを選択します。<br>行や列に名前があり、それらをラベルとして使用したい場合は、選択範囲に含めてください。", "SSE.Controllers.Viewport.textFreezePanes": "ウィンドウ枠の固定", "SSE.Controllers.Viewport.textFreezePanesShadow": "固定されたウィンドウ枠の影を表示する", "SSE.Controllers.Viewport.textHideFBar": "数式バーを表示しない", "SSE.Controllers.Viewport.textHideGridlines": "枠線を非表示にする", "SSE.Controllers.Viewport.textHideHeadings": "見出しを表示しない", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "小数点区切り", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "桁区切り", "SSE.Views.AdvancedSeparatorDialog.textLabel": "数値の桁区切り設定", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "文字列の引用符", "SSE.Views.AdvancedSeparatorDialog.textTitle": "詳細設定", "SSE.Views.AdvancedSeparatorDialog.txtNone": "（なし）", "SSE.Views.AutoFilterDialog.btnCustomFilter": "ユーザー設定フィルター", "SSE.Views.AutoFilterDialog.textAddSelection": "現在の選択範囲をフィルターに追加する", "SSE.Views.AutoFilterDialog.textEmptyItem": "{空白}", "SSE.Views.AutoFilterDialog.textSelectAll": "すべてを選択", "SSE.Views.AutoFilterDialog.textSelectAllResults": "検索の全ての結果を選択する", "SSE.Views.AutoFilterDialog.textWarning": "警告", "SSE.Views.AutoFilterDialog.txtAboveAve": "平均より上", "SSE.Views.AutoFilterDialog.txtAfter": "その後...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "期間中の全日程", "SSE.Views.AutoFilterDialog.txtApril": "4月", "SSE.Views.AutoFilterDialog.txtAugust": "8月", "SSE.Views.AutoFilterDialog.txtBefore": "その前...", "SSE.Views.AutoFilterDialog.txtBegins": "...で始まる", "SSE.Views.AutoFilterDialog.txtBelowAve": "平均より下​​", "SSE.Views.AutoFilterDialog.txtBetween": "…の間に", "SSE.Views.AutoFilterDialog.txtClear": "消去", "SSE.Views.AutoFilterDialog.txtContains": "...が値を含む", "SSE.Views.AutoFilterDialog.txtDateFilter": "日付フィルター", "SSE.Views.AutoFilterDialog.txtDecember": "12月", "SSE.Views.AutoFilterDialog.txtEmpty": "セルのフィルタを挿入してください。", "SSE.Views.AutoFilterDialog.txtEnds": "終了", "SSE.Views.AutoFilterDialog.txtEquals": "...に等しい", "SSE.Views.AutoFilterDialog.txtFebruary": "2月", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "セルの色でフィルター", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "フォントの色でフィルター", "SSE.Views.AutoFilterDialog.txtGreater": "...より大きい", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "次の値より大きいか等しい", "SSE.Views.AutoFilterDialog.txtJanuary": "1月", "SSE.Views.AutoFilterDialog.txtJuly": "7月", "SSE.Views.AutoFilterDialog.txtJune": "6月", "SSE.Views.AutoFilterDialog.txtLabelFilter": "ラベル・フィルター", "SSE.Views.AutoFilterDialog.txtLastMonth": "先月", "SSE.Views.AutoFilterDialog.txtLastQuarter": "前四半期", "SSE.Views.AutoFilterDialog.txtLastWeek": "先週", "SSE.Views.AutoFilterDialog.txtLastYear": "去年", "SSE.Views.AutoFilterDialog.txtLess": "...より小", "SSE.Views.AutoFilterDialog.txtLessEquals": "より小か等しい", "SSE.Views.AutoFilterDialog.txtMarch": "3月", "SSE.Views.AutoFilterDialog.txtMay": "5月", "SSE.Views.AutoFilterDialog.txtNextMonth": "来月", "SSE.Views.AutoFilterDialog.txtNextQuarter": "来四半期", "SSE.Views.AutoFilterDialog.txtNextWeek": "来週", "SSE.Views.AutoFilterDialog.txtNextYear": "来年", "SSE.Views.AutoFilterDialog.txtNotBegins": "…の値で始まらない", "SSE.Views.AutoFilterDialog.txtNotBetween": "間ではない", "SSE.Views.AutoFilterDialog.txtNotContains": "次の値を含まない...", "SSE.Views.AutoFilterDialog.txtNotEnds": "次の値で終わらない...", "SSE.Views.AutoFilterDialog.txtNotEquals": "...と等しくない", "SSE.Views.AutoFilterDialog.txtNovember": "11月", "SSE.Views.AutoFilterDialog.txtNumFilter": "番号フィルター", "SSE.Views.AutoFilterDialog.txtOctober": "10月", "SSE.Views.AutoFilterDialog.txtQuarter1": "第1四半期", "SSE.Views.AutoFilterDialog.txtQuarter2": "第1四半期", "SSE.Views.AutoFilterDialog.txtQuarter3": "第1四半期", "SSE.Views.AutoFilterDialog.txtQuarter4": "第1四半期", "SSE.Views.AutoFilterDialog.txtReapply": "再適用​​", "SSE.Views.AutoFilterDialog.txtSeptember": "9月", "SSE.Views.AutoFilterDialog.txtSortCellColor": "セルの色を並べ替える", "SSE.Views.AutoFilterDialog.txtSortFontColor": "フォントの色を並べ替える", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "大きい順に並べ替え", "SSE.Views.AutoFilterDialog.txtSortLow2High": "小さい順に並べ替え", "SSE.Views.AutoFilterDialog.txtSortOption": "並べ替えの他の設定．．．", "SSE.Views.AutoFilterDialog.txtTextFilter": "テキストのフィルタ-", "SSE.Views.AutoFilterDialog.txtThisMonth": "今月", "SSE.Views.AutoFilterDialog.txtThisQuarter": "今期", "SSE.Views.AutoFilterDialog.txtThisWeek": "今週", "SSE.Views.AutoFilterDialog.txtThisYear": "今年", "SSE.Views.AutoFilterDialog.txtTitle": "フィルター​​", "SSE.Views.AutoFilterDialog.txtToday": "今日", "SSE.Views.AutoFilterDialog.txtTomorrow": "明日", "SSE.Views.AutoFilterDialog.txtTop10": "トップ10", "SSE.Views.AutoFilterDialog.txtValueFilter": "値フィルター", "SSE.Views.AutoFilterDialog.txtYearToDate": "今年度", "SSE.Views.AutoFilterDialog.txtYesterday": "昨日", "SSE.Views.AutoFilterDialog.warnFilterError": "値フィルターを適用するには、「値」範囲に少なくとも1つのフィールドが必要です。", "SSE.Views.AutoFilterDialog.warnNoSelected": "値を少なくとも1つを指定してください。", "SSE.Views.CellEditor.textManager": "名前の管理", "SSE.Views.CellEditor.tipFormula": "関数を挿入", "SSE.Views.CellRangeDialog.errorMaxRows": "エラー！使用可能なデータ系列の数は、1グラフあたり最大255個です。", "SSE.Views.CellRangeDialog.errorStockChart": "行の順序が正しくありません。この株価チャートを作成するには、<br>始値、高値、安値、終値の順でシートのデータを配置してください。", "SSE.Views.CellRangeDialog.txtEmpty": "このフィールドは必須項目です", "SSE.Views.CellRangeDialog.txtInvalidRange": "エラー！セルの範囲が正しくありません。", "SSE.Views.CellRangeDialog.txtTitle": "データ範囲の選択", "SSE.Views.CellSettings.strShrink": "縮小して全体を表示する", "SSE.Views.CellSettings.strWrap": "テキストの折り返し", "SSE.Views.CellSettings.textAngle": "角", "SSE.Views.CellSettings.textBackColor": "背景色", "SSE.Views.CellSettings.textBackground": "背景色", "SSE.Views.CellSettings.textBorderColor": "色", "SSE.Views.CellSettings.textBorders": "罫線のスタイル", "SSE.Views.CellSettings.textClearRule": "ルールを解除", "SSE.Views.CellSettings.textColor": "色で塗りつぶし", "SSE.Views.CellSettings.textColorScales": "色​​スケール", "SSE.Views.CellSettings.textCondFormat": "条件付き書式", "SSE.Views.CellSettings.textControl": "テキストコントロール", "SSE.Views.CellSettings.textDataBars": "データ バー", "SSE.Views.CellSettings.textDirection": "方向", "SSE.Views.CellSettings.textFill": "塗りつぶし", "SSE.Views.CellSettings.textForeground": "前景色", "SSE.Views.CellSettings.textGradient": "グラデーションのポイント", "SSE.Views.CellSettings.textGradientColor": "色", "SSE.Views.CellSettings.textGradientFill": "グラデーション塗りつぶし", "SSE.Views.CellSettings.textIndent": "インデント", "SSE.Views.CellSettings.textItems": "アイテム", "SSE.Views.CellSettings.textLinear": "線形", "SSE.Views.CellSettings.textManageRule": "ルールの管理", "SSE.Views.CellSettings.textNewRule": "新しいルール", "SSE.Views.CellSettings.textNoFill": "塗りつぶしなし", "SSE.Views.CellSettings.textOrientation": "テキストの方向", "SSE.Views.CellSettings.textPattern": "パターン", "SSE.Views.CellSettings.textPatternFill": "パターン", "SSE.Views.CellSettings.textPosition": "位置", "SSE.Views.CellSettings.textRadial": "放射状", "SSE.Views.CellSettings.textSelectBorders": "選択したスタイルを適用する罫線をご選択ください", "SSE.Views.CellSettings.textSelection": "現在の選択項目から", "SSE.Views.CellSettings.textThisPivot": "このピボットから", "SSE.Views.CellSettings.textThisSheet": "このシートから", "SSE.Views.CellSettings.textThisTable": "この表から", "SSE.Views.CellSettings.tipAddGradientPoint": "グラデーションポイントを追加", "SSE.Views.CellSettings.tipAll": "外部の罫線と全ての内部の線", "SSE.Views.CellSettings.tipBottom": "外部の罫線（下）だけを設定する", "SSE.Views.CellSettings.tipDiagD": "斜め罫線 (右下がり)を設定する", "SSE.Views.CellSettings.tipDiagU": "斜め罫線 (右上がり)を設定する", "SSE.Views.CellSettings.tipInner": "内側の線のみを設定する", "SSE.Views.CellSettings.tipInnerHor": "水平方向の内側の線のみを設定する", "SSE.Views.CellSettings.tipInnerVert": "垂直の内側の線のみを設定する", "SSE.Views.CellSettings.tipLeft": "外部の罫線（左）だけを設定する", "SSE.Views.CellSettings.tipNone": "罫線の設定なし", "SSE.Views.CellSettings.tipOuter": "外部の罫線だけを設定する", "SSE.Views.CellSettings.tipRemoveGradientPoint": "グラデーションポイントを削除する", "SSE.Views.CellSettings.tipRight": "外部の罫線（右）だけを設定する", "SSE.Views.CellSettings.tipTop": "外部の罫線（上）だけを設定する", "SSE.Views.ChartDataDialog.errorInFormula": "入力した数式にエラーがあります。", "SSE.Views.ChartDataDialog.errorInvalidReference": "参照が無効です。 開いているワークシートを参照する必要があります。", "SSE.Views.ChartDataDialog.errorMaxPoints": "グラフごとの直列のポイントの最大数は4096です。", "SSE.Views.ChartDataDialog.errorMaxRows": "グラフのデータ系列の最大数は255です。", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "参照が無効です。 タイトル、値、サイズ、またはデータラベルの参照は、単一のセル、行、または列である必要があります。", "SSE.Views.ChartDataDialog.errorNoValues": "グラフを作成するには、系列に少なくとも1つの値がある必要があります。", "SSE.Views.ChartDataDialog.errorStockChart": "行の順序が正しくありません。この株価チャートを作成するには、<br>始値、高値、安値、終値の順でシートのデータを配置してください。", "SSE.Views.ChartDataDialog.textAdd": "追加", "SSE.Views.ChartDataDialog.textCategory": "水平（カテゴリ）軸ラベル", "SSE.Views.ChartDataDialog.textData": "グラフのデータ範囲", "SSE.Views.ChartDataDialog.textDelete": "削除する", "SSE.Views.ChartDataDialog.textDown": "下", "SSE.Views.ChartDataDialog.textEdit": "編集", "SSE.Views.ChartDataDialog.textInvalidRange": "無効なセル範囲", "SSE.Views.ChartDataDialog.textSelectData": "データの選択", "SSE.Views.ChartDataDialog.textSeries": "凡例項目 (系列)", "SSE.Views.ChartDataDialog.textSwitch": "行/列を切り替える", "SSE.Views.ChartDataDialog.textTitle": "グラフのデータ", "SSE.Views.ChartDataDialog.textUp": "上", "SSE.Views.ChartDataRangeDialog.errorInFormula": "入力した数式にエラーがあります。", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "参照が無効です。 開いているワークシートを参照する必要があります。", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "グラフごとの直列のポイントの最大数は4096です。", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "グラフのデータ系列の最大数は255です。", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "参照が無効です。 タイトル、値、サイズ、またはデータラベルの参照は、単一のセル、行、または列である必要があります。", "SSE.Views.ChartDataRangeDialog.errorNoValues": "グラフを作成するには、系列に少なくとも1つの値がある必要があります。", "SSE.Views.ChartDataRangeDialog.errorStockChart": "行の順序が正しくありません。この株価チャートを作成するには、<br>始値、高値、安値、終値の順でシートのデータを配置してください。", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "無効なセル範囲", "SSE.Views.ChartDataRangeDialog.textSelectData": "データの選択", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "軸ラベル範囲", "SSE.Views.ChartDataRangeDialog.txtChoose": "範囲を選択", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "系列の名前", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "軸ラベル", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "行を編集する", "SSE.Views.ChartDataRangeDialog.txtValues": "値", "SSE.Views.ChartDataRangeDialog.txtXValues": "X値", "SSE.Views.ChartDataRangeDialog.txtYValues": "Y値", "SSE.Views.ChartSettings.errorMaxRows": "グラフのデータ系列の最大数は255です", "SSE.Views.ChartSettings.strLineWeight": "線の太さ", "SSE.Views.ChartSettings.strSparkColor": "色", "SSE.Views.ChartSettings.strTemplate": "テンプレート", "SSE.Views.ChartSettings.text3dDepth": "深さ(ベースに対する割合)", "SSE.Views.ChartSettings.text3dHeight": "高<PERSON>(ベースに対する割合)", "SSE.Views.ChartSettings.text3dRotation": "3D回転", "SSE.Views.ChartSettings.textAdvanced": "詳細設定の表示", "SSE.Views.ChartSettings.textAutoscale": "自動スケーリング", "SSE.Views.ChartSettings.textBorderSizeErr": "入力された値が正しくありません。<br>0〜1584の数値をご入力ください。", "SSE.Views.ChartSettings.textChangeType": "タイプを変更", "SSE.Views.ChartSettings.textChartType": "グラフ種類の変更", "SSE.Views.ChartSettings.textDefault": "デフォルト回転", "SSE.Views.ChartSettings.textDown": "下", "SSE.Views.ChartSettings.textEditData": "データと場所を編集", "SSE.Views.ChartSettings.textFirstPoint": "最初のポイント", "SSE.Views.ChartSettings.textHeight": "高さ", "SSE.Views.ChartSettings.textHighPoint": "最高ポイント", "SSE.Views.ChartSettings.textKeepRatio": "比例の定数", "SSE.Views.ChartSettings.textLastPoint": "最後ポイント", "SSE.Views.ChartSettings.textLeft": "左", "SSE.Views.ChartSettings.textLowPoint": "最低ポイント", "SSE.Views.ChartSettings.textMarkers": "マーカー", "SSE.Views.ChartSettings.textNarrow": "狭角", "SSE.Views.ChartSettings.textNegativePoint": "マイナスのポイント", "SSE.Views.ChartSettings.textPerspective": "分析観点", "SSE.Views.ChartSettings.textRanges": "データ範囲", "SSE.Views.ChartSettings.textRight": "右", "SSE.Views.ChartSettings.textRightAngle": "軸の直交", "SSE.Views.ChartSettings.textSelectData": "データの選択", "SSE.Views.ChartSettings.textShow": "表示する", "SSE.Views.ChartSettings.textSize": "サイズ", "SSE.Views.ChartSettings.textStyle": "スタイル", "SSE.Views.ChartSettings.textSwitch": "行/列を切り替える", "SSE.Views.ChartSettings.textType": "タイプ", "SSE.Views.ChartSettings.textUp": "上", "SSE.Views.ChartSettings.textWiden": "広角", "SSE.Views.ChartSettings.textWidth": "幅", "SSE.Views.ChartSettings.textX": "X 回転", "SSE.Views.ChartSettings.textY": "Y 回転", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "エラー！グラフごとの直列のポイントの最大数は4096です。", "SSE.Views.ChartSettingsDlg.errorMaxRows": "エラー！使用可能なデータ系列の数は、1グラフあたり最大255個です。", "SSE.Views.ChartSettingsDlg.errorStockChart": "行の順序が正しくありません。この株価チャートを作成するには、<br>始値、高値、安値、終値の順でシートのデータを配置してください。", "SSE.Views.ChartSettingsDlg.textAbsolute": "セルで移動したりサイズを変更したりしない", "SSE.Views.ChartSettingsDlg.textAlt": "代替テキスト", "SSE.Views.ChartSettingsDlg.textAltDescription": "説明", "SSE.Views.ChartSettingsDlg.textAltTip": "視覚障害や認知障害のある人が、画像や図形、図表にどのような情報が含まれているかを理解しやすくするため、そのオブジェクトについて目視できる情報を文章で表現したものです。", "SSE.Views.ChartSettingsDlg.textAltTitle": "タイトル", "SSE.Views.ChartSettingsDlg.textAuto": "自動", "SSE.Views.ChartSettingsDlg.textAutoEach": "各に自動的", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "軸との交点", "SSE.Views.ChartSettingsDlg.textAxisOptions": "軸の設定", "SSE.Views.ChartSettingsDlg.textAxisPos": "軸の位置", "SSE.Views.ChartSettingsDlg.textAxisSettings": "軸の設定", "SSE.Views.ChartSettingsDlg.textAxisTitle": "タイトル", "SSE.Views.ChartSettingsDlg.textBase": "ベース", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "目盛りの間", "SSE.Views.ChartSettingsDlg.textBillions": "十億", "SSE.Views.ChartSettingsDlg.textBottom": "下", "SSE.Views.ChartSettingsDlg.textCategoryName": "カテゴリ名", "SSE.Views.ChartSettingsDlg.textCenter": "中央揃え", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "グラフ要素&<br>グラフの凡例", "SSE.Views.ChartSettingsDlg.textChartTitle": "グラフのタイトル", "SSE.Views.ChartSettingsDlg.textCross": "十字形", "SSE.Views.ChartSettingsDlg.textCustom": "ユーザー設定", "SSE.Views.ChartSettingsDlg.textDataColumns": "列に", "SSE.Views.ChartSettingsDlg.textDataLabels": "データ ラベル", "SSE.Views.ChartSettingsDlg.textDataRows": "行に", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "凡例を表示", "SSE.Views.ChartSettingsDlg.textEmptyCells": "空のセルと非表示のセル", "SSE.Views.ChartSettingsDlg.textEmptyLine": "データポイントを線で接続する", "SSE.Views.ChartSettingsDlg.textFit": "幅に合わせる", "SSE.Views.ChartSettingsDlg.textFixed": "固定", "SSE.Views.ChartSettingsDlg.textFormat": "ラベルの書式", "SSE.Views.ChartSettingsDlg.textGaps": "空隙", "SSE.Views.ChartSettingsDlg.textGridLines": "枠線表示", "SSE.Views.ChartSettingsDlg.textGroup": "スパークラインをグループ化する", "SSE.Views.ChartSettingsDlg.textHide": "表示しない", "SSE.Views.ChartSettingsDlg.textHideAxis": "軸を非表示", "SSE.Views.ChartSettingsDlg.textHigh": "高", "SSE.Views.ChartSettingsDlg.textHorAxis": "横軸", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "二次横軸", "SSE.Views.ChartSettingsDlg.textHorizontal": "水平", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "百", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "に", "SSE.Views.ChartSettingsDlg.textInnerBottom": "内部（下）", "SSE.Views.ChartSettingsDlg.textInnerTop": "内部（上）", "SSE.Views.ChartSettingsDlg.textInvalidRange": "エラー！セルの範囲が正しくありません。", "SSE.Views.ChartSettingsDlg.textLabelDist": "軸ラベルの距離", "SSE.Views.ChartSettingsDlg.textLabelInterval": "ラベルの間の間隔", "SSE.Views.ChartSettingsDlg.textLabelOptions": "ラベル オプション\t", "SSE.Views.ChartSettingsDlg.textLabelPos": "ラベルの位置", "SSE.Views.ChartSettingsDlg.textLayout": "レイアウト", "SSE.Views.ChartSettingsDlg.textLeft": "左", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "左の重ね合わせ", "SSE.Views.ChartSettingsDlg.textLegendBottom": "下", "SSE.Views.ChartSettingsDlg.textLegendLeft": "左", "SSE.Views.ChartSettingsDlg.textLegendPos": "凡例", "SSE.Views.ChartSettingsDlg.textLegendRight": "右に", "SSE.Views.ChartSettingsDlg.textLegendTop": "トップ", "SSE.Views.ChartSettingsDlg.textLines": "線", "SSE.Views.ChartSettingsDlg.textLocationRange": "場所の範囲", "SSE.Views.ChartSettingsDlg.textLogScale": "対数目盛", "SSE.Views.ChartSettingsDlg.textLow": "低", "SSE.Views.ChartSettingsDlg.textMajor": "メジャー", "SSE.Views.ChartSettingsDlg.textMajorMinor": "メジャーまたはマイナー", "SSE.Views.ChartSettingsDlg.textMajorType": "メジャータイプ", "SSE.Views.ChartSettingsDlg.textManual": "手動的に", "SSE.Views.ChartSettingsDlg.textMarkers": "マーカー", "SSE.Views.ChartSettingsDlg.textMarksInterval": "マークの間の間隔", "SSE.Views.ChartSettingsDlg.textMaxValue": "最大値", "SSE.Views.ChartSettingsDlg.textMillions": "百万", "SSE.Views.ChartSettingsDlg.textMinor": "マイナー", "SSE.Views.ChartSettingsDlg.textMinorType": "マイナータイプ", "SSE.Views.ChartSettingsDlg.textMinValue": "最小値", "SSE.Views.ChartSettingsDlg.textNextToAxis": "軸の下/左", "SSE.Views.ChartSettingsDlg.textNone": "なし", "SSE.Views.ChartSettingsDlg.textNoOverlay": "重ね合わせなし", "SSE.Views.ChartSettingsDlg.textOneCell": "移動するが、セルでサイズを変更しない", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "目盛", "SSE.Views.ChartSettingsDlg.textOut": "外", "SSE.Views.ChartSettingsDlg.textOuterTop": "外トップ", "SSE.Views.ChartSettingsDlg.textOverlay": "オーバーレイ", "SSE.Views.ChartSettingsDlg.textReverse": "軸を反転する", "SSE.Views.ChartSettingsDlg.textReverseOrder": "逆順", "SSE.Views.ChartSettingsDlg.textRight": "右に", "SSE.Views.ChartSettingsDlg.textRightOverlay": "右の重ね合わせ", "SSE.Views.ChartSettingsDlg.textRotated": "回転", "SSE.Views.ChartSettingsDlg.textSameAll": "すべてに同じ", "SSE.Views.ChartSettingsDlg.textSelectData": "データの選択", "SSE.Views.ChartSettingsDlg.textSeparator": "日付のラベルの区切り記号", "SSE.Views.ChartSettingsDlg.textSeriesName": "系列の名前", "SSE.Views.ChartSettingsDlg.textShow": "表示", "SSE.Views.ChartSettingsDlg.textShowBorders": "グラフの罫線を表示", "SSE.Views.ChartSettingsDlg.textShowData": "非表示の行と列にデータを表示する", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "空のセルを表示する", "SSE.Views.ChartSettingsDlg.textShowEquation": "チャートに数式を表示", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "軸を表示する", "SSE.Views.ChartSettingsDlg.textShowValues": "グラフ値を表示", "SSE.Views.ChartSettingsDlg.textSingle": "単一スパークライン", "SSE.Views.ChartSettingsDlg.textSmooth": "スムーズ", "SSE.Views.ChartSettingsDlg.textSnap": "セルに合わせる", "SSE.Views.ChartSettingsDlg.textSparkRanges": "スパークライン範囲", "SSE.Views.ChartSettingsDlg.textStraight": "直線", "SSE.Views.ChartSettingsDlg.textStyle": "スタイル", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "千", "SSE.Views.ChartSettingsDlg.textTickOptions": "ティックのオプション", "SSE.Views.ChartSettingsDlg.textTitle": "グラフ - 詳細設定", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "スパークラインー詳細設定", "SSE.Views.ChartSettingsDlg.textTop": "上", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "傾向線のオプション", "SSE.Views.ChartSettingsDlg.textTrillions": "兆", "SSE.Views.ChartSettingsDlg.textTwoCell": "セルで移動してサイズを変更する", "SSE.Views.ChartSettingsDlg.textType": "タイプ", "SSE.Views.ChartSettingsDlg.textTypeData": "タイプ＆データ", "SSE.Views.ChartSettingsDlg.textUnits": "表示単位", "SSE.Views.ChartSettingsDlg.textValue": "値", "SSE.Views.ChartSettingsDlg.textVertAxis": "縦軸", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "二次縦軸", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "X軸のタイトル", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Y軸のタイトル", "SSE.Views.ChartSettingsDlg.textZero": "ゼロ", "SSE.Views.ChartSettingsDlg.txtEmpty": "このフィールドは必須項目です", "SSE.Views.ChartTypeDialog.errorComboSeries": "組み合わせグラフを作成するには、最低2つのデータを選択します。", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "選択したチャートタイプでは、既存のチャートが使用している2次軸が必要です。他のチャートタイプを選択してください。", "SSE.Views.ChartTypeDialog.textSecondary": "二次軸", "SSE.Views.ChartTypeDialog.textSeries": "系列", "SSE.Views.ChartTypeDialog.textStyle": "スタイル", "SSE.Views.ChartTypeDialog.textTitle": "グラフの種類", "SSE.Views.ChartTypeDialog.textType": "タイプ", "SSE.Views.ChartWizardDialog.errorComboSeries": "組み合わせチャートを作成するには、最低2つのデータを選択します。", "SSE.Views.ChartWizardDialog.errorMaxPoints": "グラプごとの直列のポイントの最大数は4096です。", "SSE.Views.ChartWizardDialog.errorMaxRows": "グラフのデータ系列の最大数は255です。", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "選択したチャートタイプでは、既存のチャートが使用している2次軸が必要です。他のチャートタイプを選択してください。", "SSE.Views.ChartWizardDialog.errorStockChart": "行の順序が正しくありません。この株価チャートを作成するには、始値、最大値、最小値、終値の順でシートのデータを配置してください。", "SSE.Views.ChartWizardDialog.textRecommended": "おすすめ", "SSE.Views.ChartWizardDialog.textSecondary": "二次軸", "SSE.Views.ChartWizardDialog.textSeries": "系列", "SSE.Views.ChartWizardDialog.textTitle": "グラフの挿入", "SSE.Views.ChartWizardDialog.textTitleChange": "グラフの種類の変更", "SSE.Views.ChartWizardDialog.textType": "タイプ", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "データ・シリーズのチャート・タイプと軸を選択してください", "SSE.Views.CreatePivotDialog.textDataRange": "ソースデータ範囲", "SSE.Views.CreatePivotDialog.textDestination": "テーブルを配置する場所を選択してください", "SSE.Views.CreatePivotDialog.textExist": "既存のワークシート", "SSE.Views.CreatePivotDialog.textInvalidRange": "無効なセル範囲", "SSE.Views.CreatePivotDialog.textNew": "新しいワークシート", "SSE.Views.CreatePivotDialog.textSelectData": "データの選択", "SSE.Views.CreatePivotDialog.textTitle": "ピボット表を作成する", "SSE.Views.CreatePivotDialog.txtEmpty": "この項目は必須です", "SSE.Views.CreateSparklineDialog.textDataRange": "ソースデータ範囲", "SSE.Views.CreateSparklineDialog.textDestination": "スパークラインの付ける場所を選択してください", "SSE.Views.CreateSparklineDialog.textInvalidRange": "無効なセル範囲", "SSE.Views.CreateSparklineDialog.textSelectData": "データの選択", "SSE.Views.CreateSparklineDialog.textTitle": "スパークラインを作成する", "SSE.Views.CreateSparklineDialog.txtEmpty": "このフィールドは必須項目です", "SSE.Views.DataTab.capBtnGroup": "グループ化", "SSE.Views.DataTab.capBtnTextCustomSort": "ユーザー設定の並べ替え", "SSE.Views.DataTab.capBtnTextDataValidation": "データの入力規則", "SSE.Views.DataTab.capBtnTextRemDuplicates": "重複データを削除", "SSE.Views.DataTab.capBtnTextToCol": "テキスト区切り", "SSE.Views.DataTab.capBtnUngroup": "グループ解除", "SSE.Views.DataTab.capDataExternalLinks": "外部リンク", "SSE.Views.DataTab.capDataFromText": "データの挿入", "SSE.Views.DataTab.capGoalSeek": "ゴールシーク", "SSE.Views.DataTab.mniFromFile": "ローカルTXT/CSVから", "SSE.Views.DataTab.mniFromUrl": "TXT/CSVWeb アドレスから", "SSE.Views.DataTab.mniFromXMLFile": "ローカルXMLから", "SSE.Views.DataTab.textBelow": "詳細の下の要約行", "SSE.Views.DataTab.textClear": "グループを解除", "SSE.Views.DataTab.textColumns": "列のグループを解除", "SSE.Views.DataTab.textGroupColumns": "列をグループ化", "SSE.Views.DataTab.textGroupRows": "行をグループ化", "SSE.Views.DataTab.textRightOf": "詳細の右側にある要約列", "SSE.Views.DataTab.textRows": "行のグループを解除", "SSE.Views.DataTab.tipCustomSort": "ユーザー設定の並べ替え", "SSE.Views.DataTab.tipDataFromText": "テキスト/CSVファイルからデータ挿入", "SSE.Views.DataTab.tipDataValidation": "データの入力規則", "SSE.Views.DataTab.tipExternalLinks": "このスプレッドシートがリンクしている他のファイルを見る", "SSE.Views.DataTab.tipGoalSeek": "必要な値に対する正しい入力を見つける", "SSE.Views.DataTab.tipGroup": "セルの範囲をグループ化する", "SSE.Views.DataTab.tipRemDuplicates": "シート内の重複を削除", "SSE.Views.DataTab.tipToColumns": "セルテキストを列に分割する", "SSE.Views.DataTab.tipUngroup": "セルの範囲をグループ解除", "SSE.Views.DataValidationDialog.errorFormula": "現在、値がエラーと評価されています。続けますか？", "SSE.Views.DataValidationDialog.errorInvalid": "フィールド \"{0}\"に入力した値が無効です。", "SSE.Views.DataValidationDialog.errorInvalidDate": "フィールド \"{0}\"に入力した日付が無効です。", "SSE.Views.DataValidationDialog.errorInvalidList": "リストソースは、区切られたリスト、または単一の行または列への参照である必要があります。", "SSE.Views.DataValidationDialog.errorInvalidTime": "フィールド \"{0}\"に入力した時刻が無効です。", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "\"{1}\"フィールドは\"{0}\" フィールド以上である必要があります。", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "フィールド \"{0}\"とフィールド \"{1}\"の両方に値を入力する必要があります。", "SSE.Views.DataValidationDialog.errorMustEnterValue": "フィールド \"{0}\"に値を入力する必要があります。", "SSE.Views.DataValidationDialog.errorNamedRange": "指定した名前付き範囲が見つかりません。", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "条件 \"{0}\"では負の値を使用できません。", "SSE.Views.DataValidationDialog.errorNotNumeric": "フィールド \"{0}\"は、数値または数式であるか、数値を含むセルを参照している必要があります。", "SSE.Views.DataValidationDialog.strError": "エラー警告", "SSE.Views.DataValidationDialog.strInput": "メッセージを入力", "SSE.Views.DataValidationDialog.strSettings": "設定", "SSE.Views.DataValidationDialog.textAlert": "警告", "SSE.Views.DataValidationDialog.textAllow": "許可", "SSE.Views.DataValidationDialog.textApply": "これらの変更を同じ設定の他のすべてのセルに適用する", "SSE.Views.DataValidationDialog.textCellSelected": "セルを選択すると、この入力メッセージを表示します", "SSE.Views.DataValidationDialog.textCompare": "次と比較", "SSE.Views.DataValidationDialog.textData": "データ", "SSE.Views.DataValidationDialog.textEndDate": "終了日", "SSE.Views.DataValidationDialog.textEndTime": "終了時間", "SSE.Views.DataValidationDialog.textError": "エラーメッセージ", "SSE.Views.DataValidationDialog.textFormula": "数式", "SSE.Views.DataValidationDialog.textIgnore": "空白を無視", "SSE.Views.DataValidationDialog.textInput": "メッセージを入力", "SSE.Views.DataValidationDialog.textMax": "最大", "SSE.Views.DataValidationDialog.textMessage": "メッセージ", "SSE.Views.DataValidationDialog.textMin": "最小", "SSE.Views.DataValidationDialog.textSelectData": "データの選択", "SSE.Views.DataValidationDialog.textShowDropDown": "セルにドロップダウンリストを表示する", "SSE.Views.DataValidationDialog.textShowError": "無効なデータが入力された後にエラー警告を表示する", "SSE.Views.DataValidationDialog.textShowInput": "セルが選択されたときに入力メッセージを表示する", "SSE.Views.DataValidationDialog.textSource": "ソース", "SSE.Views.DataValidationDialog.textStartDate": "開始日", "SSE.Views.DataValidationDialog.textStartTime": "開始時間", "SSE.Views.DataValidationDialog.textStop": "停止", "SSE.Views.DataValidationDialog.textStyle": "スタイル", "SSE.Views.DataValidationDialog.textTitle": "タイトル", "SSE.Views.DataValidationDialog.textUserEnters": "ユーザーが無効なデータを入力した場合、このエラーアラートを表示します", "SSE.Views.DataValidationDialog.txtAny": "すべての値", "SSE.Views.DataValidationDialog.txtBetween": "間", "SSE.Views.DataValidationDialog.txtDate": "日付", "SSE.Views.DataValidationDialog.txtDecimal": "小数点数", "SSE.Views.DataValidationDialog.txtElTime": "経過時間", "SSE.Views.DataValidationDialog.txtEndDate": "終了日", "SSE.Views.DataValidationDialog.txtEndTime": "終了時間", "SSE.Views.DataValidationDialog.txtEqual": "次の値に等しい", "SSE.Views.DataValidationDialog.txtGreaterThan": "次の値より大きい", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "次の値より大きいか等しい", "SSE.Views.DataValidationDialog.txtLength": "長さ", "SSE.Views.DataValidationDialog.txtLessThan": "次の値より小さい", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "次の値より小さいか等しい", "SSE.Views.DataValidationDialog.txtList": "リスト", "SSE.Views.DataValidationDialog.txtNotBetween": "間ではない", "SSE.Views.DataValidationDialog.txtNotEqual": "次の値に等しくない", "SSE.Views.DataValidationDialog.txtOther": "その他", "SSE.Views.DataValidationDialog.txtStartDate": "開始日", "SSE.Views.DataValidationDialog.txtStartTime": "開始時間", "SSE.Views.DataValidationDialog.txtTextLength": "テキストの長さ", "SSE.Views.DataValidationDialog.txtTime": "時間", "SSE.Views.DataValidationDialog.txtWhole": "整数", "SSE.Views.DigitalFilterDialog.capAnd": "と", "SSE.Views.DigitalFilterDialog.capCondition1": "次の値と等しい", "SSE.Views.DigitalFilterDialog.capCondition10": "次の文字列で終わらない", "SSE.Views.DigitalFilterDialog.capCondition11": "含んでいる\t", "SSE.Views.DigitalFilterDialog.capCondition12": "次の文字を含まない", "SSE.Views.DigitalFilterDialog.capCondition2": "指定の値に等しくない", "SSE.Views.DigitalFilterDialog.capCondition3": "がより大きい", "SSE.Views.DigitalFilterDialog.capCondition30": "の次", "SSE.Views.DigitalFilterDialog.capCondition4": "次の値より大きいか等しい", "SSE.Views.DigitalFilterDialog.capCondition40": "次の後であるか、等しい", "SSE.Views.DigitalFilterDialog.capCondition5": "より小さい", "SSE.Views.DigitalFilterDialog.capCondition50": "次の前", "SSE.Views.DigitalFilterDialog.capCondition6": "より小か等しい", "SSE.Views.DigitalFilterDialog.capCondition60": "次の前であるか、等しい", "SSE.Views.DigitalFilterDialog.capCondition7": "で始まる", "SSE.Views.DigitalFilterDialog.capCondition8": "次の文字列で始まらない", "SSE.Views.DigitalFilterDialog.capCondition9": "終了", "SSE.Views.DigitalFilterDialog.capOr": "または", "SSE.Views.DigitalFilterDialog.textNoFilter": "フィルタなし", "SSE.Views.DigitalFilterDialog.textShowRows": "抽出条件の指定:", "SSE.Views.DigitalFilterDialog.textUse1": "?を使って、任意の1文字を表すことができます。", "SSE.Views.DigitalFilterDialog.textUse2": "* を使って、任意の文字列を表すことができます。", "SSE.Views.DigitalFilterDialog.txtSelectDate": "日付の選択", "SSE.Views.DigitalFilterDialog.txtTitle": "ユーザー設定フィルター", "SSE.Views.DocumentHolder.advancedEquationText": "数式設定", "SSE.Views.DocumentHolder.advancedImgText": "画像の詳細設定", "SSE.Views.DocumentHolder.advancedShapeText": "図形の詳細設定", "SSE.Views.DocumentHolder.advancedSlicerText": "スライサーの高度な設定", "SSE.Views.DocumentHolder.allLinearText": "すべて - 線形", "SSE.Views.DocumentHolder.allProfText": "すべて - プロフェッショナル", "SSE.Views.DocumentHolder.bottomCellText": "下揃え", "SSE.Views.DocumentHolder.bulletsText": "箇条書きと段落番号", "SSE.Views.DocumentHolder.centerCellText": "中央揃え", "SSE.Views.DocumentHolder.chartDataText": "グラフデータを選択する", "SSE.Views.DocumentHolder.chartText": "グラフの詳細設定", "SSE.Views.DocumentHolder.chartTypeText": "グラフの種類を変更する", "SSE.Views.DocumentHolder.currLinearText": "現在 - 線形", "SSE.Views.DocumentHolder.currProfText": "現在 - プロフェッショナル", "SSE.Views.DocumentHolder.deleteColumnText": "列", "SSE.Views.DocumentHolder.deleteRowText": "行の削除", "SSE.Views.DocumentHolder.deleteTableText": "表", "SSE.Views.DocumentHolder.direct270Text": "270度回転", "SSE.Views.DocumentHolder.direct90Text": "90度回転", "SSE.Views.DocumentHolder.directHText": "水平", "SSE.Views.DocumentHolder.directionText": "文字列の方向", "SSE.Views.DocumentHolder.editChartText": "データを編集", "SSE.Views.DocumentHolder.editHyperlinkText": "ハイパーリンクを編集", "SSE.Views.DocumentHolder.hideEqToolbar": "方程式ツールバーを非表示にする", "SSE.Views.DocumentHolder.insertColumnLeftText": "左に列の挿入", "SSE.Views.DocumentHolder.insertColumnRightText": "右に列の挿入", "SSE.Views.DocumentHolder.insertRowAboveText": "上に行の挿入", "SSE.Views.DocumentHolder.insertRowBelowText": "下に行の挿入", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "実際のサイズ", "SSE.Views.DocumentHolder.removeHyperlinkText": "ハイパーリンクの削除", "SSE.Views.DocumentHolder.selectColumnText": "列全体", "SSE.Views.DocumentHolder.selectDataText": "列のデータ", "SSE.Views.DocumentHolder.selectRowText": "行の選択", "SSE.Views.DocumentHolder.selectTableText": "テーブルの選択", "SSE.Views.DocumentHolder.showEqToolbar": "方程式ツールバーの表示", "SSE.Views.DocumentHolder.strDelete": "署名の削除", "SSE.Views.DocumentHolder.strDetails": "サインの詳細", "SSE.Views.DocumentHolder.strSetup": "サインの設定", "SSE.Views.DocumentHolder.strSign": "サインする", "SSE.Views.DocumentHolder.textAlign": "配置", "SSE.Views.DocumentHolder.textArrange": "順序", "SSE.Views.DocumentHolder.textArrangeBack": "最背面ヘ移動", "SSE.Views.DocumentHolder.textArrangeBackward": "背面ヘ移動", "SSE.Views.DocumentHolder.textArrangeForward": "前面ヘ移動", "SSE.Views.DocumentHolder.textArrangeFront": "最前面ヘ移動", "SSE.Views.DocumentHolder.textAverage": "平均", "SSE.Views.DocumentHolder.textBullets": "箇条書き", "SSE.Views.DocumentHolder.textCopyCells": "セルのコピー", "SSE.Views.DocumentHolder.textCount": "データの個数", "SSE.Views.DocumentHolder.textCrop": "トリミング", "SSE.Views.DocumentHolder.textCropFill": "塗りつぶし", "SSE.Views.DocumentHolder.textCropFit": "合わせる", "SSE.Views.DocumentHolder.textEditPoints": "頂点の編集", "SSE.Views.DocumentHolder.textEntriesList": "ドロップダウンリストから選択する", "SSE.Views.DocumentHolder.textFillDays": "日別に入力", "SSE.Views.DocumentHolder.textFillFormatOnly": "フォーマットのみ入力", "SSE.Views.DocumentHolder.textFillMonths": "月別に入力", "SSE.Views.DocumentHolder.textFillSeries": "数列の入力", "SSE.Views.DocumentHolder.textFillWeekdays": "平日別に入力", "SSE.Views.DocumentHolder.textFillWithoutFormat": "フォーマットなしの入力", "SSE.Views.DocumentHolder.textFillYears": "年別に入力", "SSE.Views.DocumentHolder.textFlashFill": "高速入力", "SSE.Views.DocumentHolder.textFlipH": "左右反転", "SSE.Views.DocumentHolder.textFlipV": "上下反転", "SSE.Views.DocumentHolder.textFreezePanes": "枠の固定", "SSE.Views.DocumentHolder.textFromFile": "ファイルから", "SSE.Views.DocumentHolder.textFromStorage": "ストレージから", "SSE.Views.DocumentHolder.textFromUrl": "URLから", "SSE.Views.DocumentHolder.textGrowthTrend": "指数増加傾向", "SSE.Views.DocumentHolder.textLinearTrend": "線形トレンド", "SSE.Views.DocumentHolder.textListSettings": "リストの設定", "SSE.Views.DocumentHolder.textMacro": "マクロの登録", "SSE.Views.DocumentHolder.textMax": "最大", "SSE.Views.DocumentHolder.textMin": "最小", "SSE.Views.DocumentHolder.textMore": "その他の関数", "SSE.Views.DocumentHolder.textMoreFormats": "その他のフォーマット", "SSE.Views.DocumentHolder.textNone": "なし", "SSE.Views.DocumentHolder.textNumbering": "番号付け", "SSE.Views.DocumentHolder.textReplace": "画像の置き換え", "SSE.Views.DocumentHolder.textResetCrop": "トリミングをリセット", "SSE.Views.DocumentHolder.textRotate": "回転", "SSE.Views.DocumentHolder.textRotate270": "反時計回りに90度回転", "SSE.Views.DocumentHolder.textRotate90": "時計回りに90度回転", "SSE.Views.DocumentHolder.textSaveAsPicture": "画像として保存", "SSE.Views.DocumentHolder.textSeries": "系列", "SSE.Views.DocumentHolder.textShapeAlignBottom": "下揃え", "SSE.Views.DocumentHolder.textShapeAlignCenter": "中央揃え", "SSE.Views.DocumentHolder.textShapeAlignLeft": "左揃え", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "中央揃え", "SSE.Views.DocumentHolder.textShapeAlignRight": "右揃え", "SSE.Views.DocumentHolder.textShapeAlignTop": "上揃え", "SSE.Views.DocumentHolder.textShapesMerge": "図形を結合", "SSE.Views.DocumentHolder.textStdDev": "標準偏差", "SSE.Views.DocumentHolder.textSum": "合計", "SSE.Views.DocumentHolder.textUndo": "元に戻す", "SSE.Views.DocumentHolder.textUnFreezePanes": "ウインドウ枠固定の解除", "SSE.Views.DocumentHolder.textVar": "標本分散", "SSE.Views.DocumentHolder.tipMarkersArrow": "箇条書き（矢印）", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "箇条書き（チェックマーク）", "SSE.Views.DocumentHolder.tipMarkersDash": "「ダッシュ」記号", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "箇条書き（ひし形）", "SSE.Views.DocumentHolder.tipMarkersFRound": "箇条書き（丸）", "SSE.Views.DocumentHolder.tipMarkersFSquare": "箇条書き（四角）", "SSE.Views.DocumentHolder.tipMarkersHRound": "箇条書き（円）", "SSE.Views.DocumentHolder.tipMarkersStar": "箇条書き（星）", "SSE.Views.DocumentHolder.topCellText": "上揃え", "SSE.Views.DocumentHolder.txtAccounting": "会計", "SSE.Views.DocumentHolder.txtAddComment": "コメントを追加", "SSE.Views.DocumentHolder.txtAddNamedRange": "名前の定義", "SSE.Views.DocumentHolder.txtArrange": "順序", "SSE.Views.DocumentHolder.txtAscending": "昇順", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "列幅の自動調整", "SSE.Views.DocumentHolder.txtAutoRowHeight": "列の幅の自動調整", "SSE.Views.DocumentHolder.txtAverage": "平均", "SSE.Views.DocumentHolder.txtCellFormat": "セルをフォーマットする", "SSE.Views.DocumentHolder.txtClear": "消去", "SSE.Views.DocumentHolder.txtClearAll": "すべて", "SSE.Views.DocumentHolder.txtClearComments": "コメント", "SSE.Views.DocumentHolder.txtClearFormat": "形式", "SSE.Views.DocumentHolder.txtClearHyper": "ハイパーリンク", "SSE.Views.DocumentHolder.txtClearPivotField": "{0} のフィルターをクリア", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "選択されたスパークライン・グループを解除", "SSE.Views.DocumentHolder.txtClearSparklines": "選択されたスパークラインを解除", "SSE.Views.DocumentHolder.txtClearText": "テキスト", "SSE.Views.DocumentHolder.txtCollapse": "折りたたみ", "SSE.Views.DocumentHolder.txtCollapseEntire": "フィールド全体を折りたたむ", "SSE.Views.DocumentHolder.txtColumn": "列全体", "SSE.Views.DocumentHolder.txtColumnWidth": "列の幅", "SSE.Views.DocumentHolder.txtCondFormat": "条件付き書式", "SSE.Views.DocumentHolder.txtCopy": "コピー", "SSE.Views.DocumentHolder.txtCount": "カウント", "SSE.Views.DocumentHolder.txtCurrency": "通貨", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "ユーザー設定の列幅", "SSE.Views.DocumentHolder.txtCustomRowHeight": "ユーザー設定の行の高さ", "SSE.Views.DocumentHolder.txtCustomSort": "ユーザー設定の並べ替え", "SSE.Views.DocumentHolder.txtCut": "切り取り", "SSE.Views.DocumentHolder.txtDateLong": "長い日付形式", "SSE.Views.DocumentHolder.txtDateShort": "日付 (短い形式)", "SSE.Views.DocumentHolder.txtDelete": "削除", "SSE.Views.DocumentHolder.txtDelField": "削除", "SSE.Views.DocumentHolder.txtDescending": "降順", "SSE.Views.DocumentHolder.txtDifference": "基準値との差分", "SSE.Views.DocumentHolder.txtDistribHor": "左右に整列", "SSE.Views.DocumentHolder.txtDistribVert": "上下に整列", "SSE.Views.DocumentHolder.txtEditComment": "コメントの編集", "SSE.Views.DocumentHolder.txtEditObject": "オブジェクトを編集", "SSE.Views.DocumentHolder.txtExpand": "拡張する", "SSE.Views.DocumentHolder.txtExpandCollapse": "拡張/折りたたみ", "SSE.Views.DocumentHolder.txtExpandEntire": "フィールド全体を拡張する", "SSE.Views.DocumentHolder.txtFieldSettings": "フィールド設定", "SSE.Views.DocumentHolder.txtFilter": "フィルター​​", "SSE.Views.DocumentHolder.txtFilterCellColor": "セルの色でフィルター", "SSE.Views.DocumentHolder.txtFilterFontColor": "フォントの色でフィルター", "SSE.Views.DocumentHolder.txtFilterValue": "選択したセルの値でフィルター", "SSE.Views.DocumentHolder.txtFormula": "関数を挿入", "SSE.Views.DocumentHolder.txtFraction": "分数", "SSE.Views.DocumentHolder.txtGeneral": "標準", "SSE.Views.DocumentHolder.txtGetLink": "この範囲のリンクを取得する", "SSE.Views.DocumentHolder.txtGrandTotal": "総計", "SSE.Views.DocumentHolder.txtGroup": "グループ化", "SSE.Views.DocumentHolder.txtHide": "表示しない", "SSE.Views.DocumentHolder.txtIndex": "インデックス", "SSE.Views.DocumentHolder.txtInsert": "挿入", "SSE.Views.DocumentHolder.txtInsHyperlink": "ハイパーリンク", "SSE.Views.DocumentHolder.txtInsImage": "画像をファイルから挿入する", "SSE.Views.DocumentHolder.txtInsImageUrl": "画像をURLから挿入する", "SSE.Views.DocumentHolder.txtLabelFilter": "ラベル フィルター", "SSE.Views.DocumentHolder.txtMax": "最大", "SSE.Views.DocumentHolder.txtMin": "最小", "SSE.Views.DocumentHolder.txtMoreOptions": "他のオプション", "SSE.Views.DocumentHolder.txtNormal": "計算なし", "SSE.Views.DocumentHolder.txtNumber": "数値", "SSE.Views.DocumentHolder.txtNumFormat": "数値の書式", "SSE.Views.DocumentHolder.txtPaste": "貼り付け", "SSE.Views.DocumentHolder.txtPercent": "%", "SSE.Views.DocumentHolder.txtPercentage": "パーセンテージ", "SSE.Views.DocumentHolder.txtPercentDiff": "基準値に対する比率の差", "SSE.Views.DocumentHolder.txtPercentOfCol": "カラムサマリーパーセンテージ", "SSE.Views.DocumentHolder.txtPercentOfGrand": "%合計", "SSE.Views.DocumentHolder.txtPercentOfParent": "親集計に対する比率", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "親列集計に対する比率", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "親行集計に対する比率", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "累計", "SSE.Views.DocumentHolder.txtPercentOfTotal": "行集計に対する比率", "SSE.Views.DocumentHolder.txtPivotSettings": "ピボットテーブルの設定", "SSE.Views.DocumentHolder.txtProduct": "積", "SSE.Views.DocumentHolder.txtRankAscending": "昇順での順位", "SSE.Views.DocumentHolder.txtRankDescending": "降順での順位", "SSE.Views.DocumentHolder.txtReapply": "再適用​​", "SSE.Views.DocumentHolder.txtRefresh": "更新する", "SSE.Views.DocumentHolder.txtRow": "行全体", "SSE.Views.DocumentHolder.txtRowHeight": "行の高さ", "SSE.Views.DocumentHolder.txtRunTotal": "累計", "SSE.Views.DocumentHolder.txtScientific": "指数", "SSE.Views.DocumentHolder.txtSelect": "選択", "SSE.Views.DocumentHolder.txtShiftDown": "下方向にシフト", "SSE.Views.DocumentHolder.txtShiftLeft": "左方向にシフト", "SSE.Views.DocumentHolder.txtShiftRight": "右方向にシフト", "SSE.Views.DocumentHolder.txtShiftUp": "上方向にシフト", "SSE.Views.DocumentHolder.txtShow": "表示", "SSE.Views.DocumentHolder.txtShowAs": "計算の種類を表示", "SSE.Views.DocumentHolder.txtShowComment": "コメントの表示", "SSE.Views.DocumentHolder.txtShowDetails": "詳細の表示", "SSE.Views.DocumentHolder.txtSort": "並べ替え", "SSE.Views.DocumentHolder.txtSortCellColor": "選択したセルの色を上に表示", "SSE.Views.DocumentHolder.txtSortFontColor": "選択したフォントの色を上に表示", "SSE.Views.DocumentHolder.txtSortOption": "その他の並べ替えオプション", "SSE.Views.DocumentHolder.txtSparklines": "スパークライン", "SSE.Views.DocumentHolder.txtSubtotalField": "小計", "SSE.Views.DocumentHolder.txtSum": "合計", "SSE.Views.DocumentHolder.txtSummarize": "値の集計方法", "SSE.Views.DocumentHolder.txtText": "テキスト", "SSE.Views.DocumentHolder.txtTextAdvanced": "段落の詳細設定", "SSE.Views.DocumentHolder.txtTime": "時刻", "SSE.Views.DocumentHolder.txtTop10": "トップ10", "SSE.Views.DocumentHolder.txtUngroup": "グループ解除", "SSE.Views.DocumentHolder.txtValueFieldSettings": "値フィールド設定", "SSE.Views.DocumentHolder.txtValueFilter": "値フィルター", "SSE.Views.DocumentHolder.txtWidth": "幅", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "垂直方向の配置", "SSE.Views.ExternalLinksDlg.closeButtonText": "閉じる", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "リンクされたソースからデータを自動的に更新する", "SSE.Views.ExternalLinksDlg.textChange": "変更元", "SSE.Views.ExternalLinksDlg.textDelete": "リンクの解除", "SSE.Views.ExternalLinksDlg.textDeleteAll": "すべてのリンクを解除", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textOpen": "オープンソース", "SSE.Views.ExternalLinksDlg.textSource": "ソース", "SSE.Views.ExternalLinksDlg.textStatus": "ステータス", "SSE.Views.ExternalLinksDlg.textUnknown": "不明", "SSE.Views.ExternalLinksDlg.textUpdate": "値を更新", "SSE.Views.ExternalLinksDlg.textUpdateAll": "すべて更新", "SSE.Views.ExternalLinksDlg.textUpdating": "アップデート中...", "SSE.Views.ExternalLinksDlg.txtTitle": "外部リンク", "SSE.Views.FieldSettingsDialog.strLayout": "レイアウト", "SSE.Views.FieldSettingsDialog.strSubtotals": "小計", "SSE.Views.FieldSettingsDialog.textNumFormat": "番号の書式", "SSE.Views.FieldSettingsDialog.textReport": "レポートフォーム", "SSE.Views.FieldSettingsDialog.textTitle": "フィールド設定", "SSE.Views.FieldSettingsDialog.txtAverage": "平均", "SSE.Views.FieldSettingsDialog.txtBlank": "各項目の後に空白行を挿入する", "SSE.Views.FieldSettingsDialog.txtBottom": "グループの下部に表示する", "SSE.Views.FieldSettingsDialog.txtCompact": "コンパクト", "SSE.Views.FieldSettingsDialog.txtCount": "データの個数", "SSE.Views.FieldSettingsDialog.txtCountNums": "数値の個数", "SSE.Views.FieldSettingsDialog.txtCustomName": "ユーザー設定の名前", "SSE.Views.FieldSettingsDialog.txtEmpty": "データのないアイテムを表示する", "SSE.Views.FieldSettingsDialog.txtMax": "最大", "SSE.Views.FieldSettingsDialog.txtMin": "最小", "SSE.Views.FieldSettingsDialog.txtOutline": "アウトライン", "SSE.Views.FieldSettingsDialog.txtProduct": "乗積", "SSE.Views.FieldSettingsDialog.txtRepeat": "各行でアイテムラベルを繰り返す", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "小計を表示する", "SSE.Views.FieldSettingsDialog.txtSourceName": "ソース名：", "SSE.Views.FieldSettingsDialog.txtStdDev": "標準偏差", "SSE.Views.FieldSettingsDialog.txtStdDevp": "標準偏差", "SSE.Views.FieldSettingsDialog.txtSum": "合計", "SSE.Views.FieldSettingsDialog.txtSummarize": "小計の関数", "SSE.Views.FieldSettingsDialog.txtTabular": "表形式", "SSE.Views.FieldSettingsDialog.txtTop": "グループのトップに表示する", "SSE.Views.FieldSettingsDialog.txtVar": "標本分散", "SSE.Views.FieldSettingsDialog.txtVarp": "分散", "SSE.Views.FileMenu.ariaFileMenu": "ファイルメニュー", "SSE.Views.FileMenu.btnBackCaption": "ファイルの場所を開く", "SSE.Views.FileMenu.btnCloseEditor": "ファイルを閉じる", "SSE.Views.FileMenu.btnCloseMenuCaption": "戻る", "SSE.Views.FileMenu.btnCreateNewCaption": "新規作成", "SSE.Views.FileMenu.btnDownloadCaption": "名前を付けてダウンロード", "SSE.Views.FileMenu.btnExitCaption": "閉じる", "SSE.Views.FileMenu.btnExportToPDFCaption": "PDFへの変換", "SSE.Views.FileMenu.btnFileOpenCaption": "開く", "SSE.Views.FileMenu.btnHelpCaption": "ヘルプ", "SSE.Views.FileMenu.btnHistoryCaption": "バージョン履歴", "SSE.Views.FileMenu.btnInfoCaption": "詳細情報", "SSE.Views.FileMenu.btnPrintCaption": "印刷", "SSE.Views.FileMenu.btnProtectCaption": "保護する", "SSE.Views.FileMenu.btnRecentFilesCaption": "最近使ったファイルを開く", "SSE.Views.FileMenu.btnRenameCaption": "名前を変更する", "SSE.Views.FileMenu.btnReturnCaption": "スプレッドシートに戻る", "SSE.Views.FileMenu.btnRightsCaption": "アクセス権", "SSE.Views.FileMenu.btnSaveAsCaption": "名前を付けて保存", "SSE.Views.FileMenu.btnSaveCaption": "保存", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "別名で保存", "SSE.Views.FileMenu.btnSettingsCaption": "詳細設定", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "モバイル版に切り替える", "SSE.Views.FileMenu.btnToEditCaption": "スプレッドシートを編集", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "空白のスプレッドシート", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "新規作成", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "適用", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "著者を追加", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "プロパティの追加", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "テキストを追加", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "アプリ", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "作成者", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "アクセス許可の変更", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "コメント", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "共通", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "作成しました", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "ドキュメントのプロパティ", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "最終更新者", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "最終更新", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "いいえ", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "所有者", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "場所", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "プロパティ", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "このタイトルのプロパティはすでに存在します", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "権利を持っている者", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "スプレッドシート情報", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "件名", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "タグ", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "タイトル", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "アップロードされました", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "はい", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "アクセス権", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "アクセス許可の変更", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "権利を持っている者", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "適用", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "共同編集モード", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "1904年の日付システムを使用する", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "小数点区切り", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "辞書言語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "反復計算を有効にする", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "即時反映モード", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "フォント・ヒンティング", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "数式の言語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "例えば：合計；最小；最大；カウント", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "数式のヒントを表示", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Show horizontal scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "大文字がある言葉を無視する", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "数字のある単語は無視する", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "マクロの設定", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "相対誤差", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "最大反復回数", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "貼り付けるときに[貼り付けオプション]ボタンを表示する", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "R1C1参照形式", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "地域の設定", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "例えば：", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTLインターフェース", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "シートにコメントを表示する", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "他のユーザーの変更点を表示する", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "解決済みコメントを表示する", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "スクロール中にグリッドに固定", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "厳密モード", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "タブのスタイル", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "インターフェイスのテーマ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "桁区切り", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "測定単位", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "地域の設定に基づいて桁区切りを使用する", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Show vertical scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "既定のズーム値", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "10 分ごと", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "30 分ごと", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "5 分ごと", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "1 時間ごと", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "自動バックアップ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "自動保存", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "無効", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "塗りつぶし", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "中間バージョンの保存", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "線", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "1 分ごと", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "参照スタイル", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "詳細設定", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "外観", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "オートコレクト設定…", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "ベラルーシ語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "ブルガリア語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "カタルニア語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "既定のキャッシュ モード", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "計算", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "センチ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "共同編集", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "チェコ語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "クイックアクセスのカスタマイズ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "デンマーク語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "ドイツ語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "編集と保存", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "ギリシャ語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "英語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "入力した内容は使用できません。恐らく整数または10進数である必要があります。", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "スペイン語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "リアルタイムの共同編集　すべての変更は自動的に保存されます", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "フィンランド語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "フランス語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "ハンガリー語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "アルメニア語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "インドネシア語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "インチ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "イタリア語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "日本語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "韓国語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "最近使用", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "ラオス語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "ラトビア語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "OSXのように", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "ネイティブ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "ノルウェー語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "オランダ語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "ポーランド語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "校正", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "ポイント", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "ポルトガル語 (ブラジル)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "ポルトガル語（ポルトガル）", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "クイックプリントボタンをエディタヘッダーに表示", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "最後に選択した、またはデフォルトのプリンターで印刷されます。", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "地域", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "ルーマニア語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "ロシア語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "全てを有効にする", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "マクロを有効にして、通知しない", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "スクリーンリーダーのサポートをオンにする", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "デフォルトのシート方向", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "この設定は新規シートのみに影響します", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "左から右へ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "右から左へ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "スロバキア語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "スロベニア語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "全てを無効にする", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "マクロを無効にして、通知しない", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "「保存」ボタンを使用して、あなたや他人が行った変更を同期させることができます", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "スウェーデン語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "ツールバーの色をタブの背景に使う", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "トルコ語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "ウクライナ語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "キーボードでユーザーインターフェイスで移動するには、Altキーを使用します", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "「Option」キーを使用して、キーボードでユーザーインターフェイスで移動します", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "ベトナム語", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "通知を表示する", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "マクロを無効にして、通知する", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "Windowsのように", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "ワークスペース", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "中国語", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "警告", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "パスワード付きで", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "スプレッドシートを保護する", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "サインを使って", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "有効な署名がスプレッドシートに追加されました。<br>スプレッドシートは編集から保護されています。", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "<br>目に見えないデジタル署名を追加することで、スプレッドシートの完全性を確保する", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "スプレッドシートを編集する", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "編集すると、スプレッドシートから署名が削除されます。<br>このまま続けますか？", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "このスプレッドシートはパスワードで保護されています", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "このスプレッドシートをパスワードで暗号化する", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "このスプレッドシートはサインする必要があります。", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "有効な署名がスプレッドシートに追加されました。 スプレッドシートは編集から保護されています。", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "スプレッドシートの一部のデジタル署名が無効であるか、検証できませんでした。 スプレッドシートは編集から保護されています。", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "署名の表示", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "名前を付けてダウンロード", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "コピーを別名で保存する", "SSE.Views.FillSeriesDialog.textAuto": "自動入力", "SSE.Views.FillSeriesDialog.textCols": "列", "SSE.Views.FillSeriesDialog.textDate": "日付", "SSE.Views.FillSeriesDialog.textDateUnit": "日付単位", "SSE.Views.FillSeriesDialog.textDay": "日", "SSE.Views.FillSeriesDialog.textGrowth": "乗算", "SSE.Views.FillSeriesDialog.textLinear": "線形", "SSE.Views.FillSeriesDialog.textMonth": "月", "SSE.Views.FillSeriesDialog.textRows": "行", "SSE.Views.FillSeriesDialog.textSeries": "系列", "SSE.Views.FillSeriesDialog.textStep": "ステップ", "SSE.Views.FillSeriesDialog.textStop": "ストップ値", "SSE.Views.FillSeriesDialog.textTitle": "系列", "SSE.Views.FillSeriesDialog.textTrend": "傾向", "SSE.Views.FillSeriesDialog.textType": "タイプ", "SSE.Views.FillSeriesDialog.textWeek": "平日", "SSE.Views.FillSeriesDialog.textYear": "年", "SSE.Views.FillSeriesDialog.txtErrorNumber": "入力した内容は使用できません。整数または10進数が必要な場合があります。", "SSE.Views.FormatRulesEditDlg.fillColor": "塗りつぶしの色", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "警告", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 色スケール", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 色スケール", "SSE.Views.FormatRulesEditDlg.textAllBorders": "すべての枠線", "SSE.Views.FormatRulesEditDlg.textAppearance": "列の外観", "SSE.Views.FormatRulesEditDlg.textApply": "範囲に適用", "SSE.Views.FormatRulesEditDlg.textAutomatic": "自動", "SSE.Views.FormatRulesEditDlg.textAxis": "軸", "SSE.Views.FormatRulesEditDlg.textBarDirection": "列の方向", "SSE.Views.FormatRulesEditDlg.textBold": "太字", "SSE.Views.FormatRulesEditDlg.textBorder": "境界線", "SSE.Views.FormatRulesEditDlg.textBordersColor": "境界線の色", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "枠線のスタイル", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "下の枠線", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "条件付き書式を追加できません。", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "セルの中点", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "内側の垂直枠線", "SSE.Views.FormatRulesEditDlg.textClear": "消去", "SSE.Views.FormatRulesEditDlg.textColor": "文字の色", "SSE.Views.FormatRulesEditDlg.textContext": "コンテキスト", "SSE.Views.FormatRulesEditDlg.textCustom": "ユーザー設定", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "斜め（上から下）", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "斜め（下から上）", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "有効な数式を入力してください", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "入力した数式は、有効な数値、日付、時刻、または文字列に評価されません。", "SSE.Views.FormatRulesEditDlg.textEmptyText": "値を入力してください", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "入力した値は、有効な数値、日付、時刻、または文字列ではありません。", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "｛０｝値は、｛１｝値より大きい値でなければなりません。", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "{0} と {1} の間の数値を入力してください。", "SSE.Views.FormatRulesEditDlg.textFill": "塗りつぶし", "SSE.Views.FormatRulesEditDlg.textFormat": "形式", "SSE.Views.FormatRulesEditDlg.textFormula": "数式", "SSE.Views.FormatRulesEditDlg.textGradient": "グラデーション", "SSE.Views.FormatRulesEditDlg.textIconLabel": "いつ {0} {1} と", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "いつ {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "値が", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "1 つまたは複数のアイコンのデータ範囲が重複しています。<br> アイコンのデータ範囲が重複しないようにデータ範囲の値を調整してください。", "SSE.Views.FormatRulesEditDlg.textIconStyle": "アイコンのスタイル", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "内枠線", "SSE.Views.FormatRulesEditDlg.textInvalid": "無効なデータ範囲", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "エラー！セルの範囲が正しくありません。", "SSE.Views.FormatRulesEditDlg.textItalic": "イタリック", "SSE.Views.FormatRulesEditDlg.textItem": "アイテム", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "左から右へ", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "左の枠線", "SSE.Views.FormatRulesEditDlg.textLongBar": "最長の列", "SSE.Views.FormatRulesEditDlg.textMaximum": "最大", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "最大のポイント", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "内側の水平枠線", "SSE.Views.FormatRulesEditDlg.textMidpoint": "中央のポイント", "SSE.Views.FormatRulesEditDlg.textMinimum": "最小", "SSE.Views.FormatRulesEditDlg.textMinpoint": "最小のポイント", "SSE.Views.FormatRulesEditDlg.textNegative": "負", "SSE.Views.FormatRulesEditDlg.textNewColor": "その他の色", "SSE.Views.FormatRulesEditDlg.textNoBorders": "枠線なし", "SSE.Views.FormatRulesEditDlg.textNone": "なし", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "指定した 1 つまたは複数の値が、有効なパーセント値ではありません。", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "指定した｛０｝値は、有効なパーセント値ではありません。", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "指定した 1 つまたは複数の値が、有効なパーセンタイル値ではありません。", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "指定した｛０｝値は、有効なパーセンタイル値ではありません。", "SSE.Views.FormatRulesEditDlg.textOutBorders": "外枠線", "SSE.Views.FormatRulesEditDlg.textPercent": "パーセント", "SSE.Views.FormatRulesEditDlg.textPercentile": "百分位", "SSE.Views.FormatRulesEditDlg.textPosition": "場所", "SSE.Views.FormatRulesEditDlg.textPositive": "正", "SSE.Views.FormatRulesEditDlg.textPresets": "プリセット", "SSE.Views.FormatRulesEditDlg.textPreview": "プレビュー", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "カラースケール、データバー、アイコンセットの条件付き書式設定では、相対参照は使用できません。", "SSE.Views.FormatRulesEditDlg.textReverse": "反転なアイコンの並び順", "SSE.Views.FormatRulesEditDlg.textRight2Left": "右から左に", "SSE.Views.FormatRulesEditDlg.textRightBorders": "右の枠線", "SSE.Views.FormatRulesEditDlg.textRule": "ルール", "SSE.Views.FormatRulesEditDlg.textSameAs": "正として", "SSE.Views.FormatRulesEditDlg.textSelectData": "データの選択", "SSE.Views.FormatRulesEditDlg.textShortBar": "もっとも短い列", "SSE.Views.FormatRulesEditDlg.textShowBar": "バーのみ表示する", "SSE.Views.FormatRulesEditDlg.textShowIcon": "アイコンのみ表示", "SSE.Views.FormatRulesEditDlg.textSingleRef": "条件付き書式の数式ではこの種類の参照は使用できません。単一セルの参照に変更します。または =SUM(A1:B5) のようなワークシート関数による参照を使ってください。", "SSE.Views.FormatRulesEditDlg.textSolid": "実線", "SSE.Views.FormatRulesEditDlg.textStrikeout": "取り消し線", "SSE.Views.FormatRulesEditDlg.textSubscript": "下付き文字", "SSE.Views.FormatRulesEditDlg.textSuperscript": "上付き文字", "SSE.Views.FormatRulesEditDlg.textTopBorders": "上の境界線", "SSE.Views.FormatRulesEditDlg.textUnderline": "下線", "SSE.Views.FormatRulesEditDlg.tipBorders": "境界線", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "数の書式", "SSE.Views.FormatRulesEditDlg.txtAccounting": "会計", "SSE.Views.FormatRulesEditDlg.txtCurrency": "通貨", "SSE.Views.FormatRulesEditDlg.txtDate": "日付", "SSE.Views.FormatRulesEditDlg.txtDateLong": "長い日付形式", "SSE.Views.FormatRulesEditDlg.txtDateShort": "日付 (短い形式)", "SSE.Views.FormatRulesEditDlg.txtEmpty": "このフィールドは必須項目です", "SSE.Views.FormatRulesEditDlg.txtFraction": "分数", "SSE.Views.FormatRulesEditDlg.txtGeneral": "全般", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "アイコン無し", "SSE.Views.FormatRulesEditDlg.txtNumber": "数", "SSE.Views.FormatRulesEditDlg.txtPercentage": "パーセンテージ", "SSE.Views.FormatRulesEditDlg.txtScientific": "科学的", "SSE.Views.FormatRulesEditDlg.txtText": "テキスト", "SSE.Views.FormatRulesEditDlg.txtTime": "時刻", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "フォーマットルールを編集する", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "新しい書式ルール", "SSE.Views.FormatRulesManagerDlg.guestText": "ゲスト", "SSE.Views.FormatRulesManagerDlg.lockText": "ロックされた", "SSE.Views.FormatRulesManagerDlg.text1Above": "平均より 1 標準偏差上", "SSE.Views.FormatRulesManagerDlg.text1Below": "平均より 1 標準偏差下", "SSE.Views.FormatRulesManagerDlg.text2Above": "平均より 2 標準偏差上", "SSE.Views.FormatRulesManagerDlg.text2Below": "平均より 2 標準偏差下", "SSE.Views.FormatRulesManagerDlg.text3Above": "平均より 3 標準偏差上", "SSE.Views.FormatRulesManagerDlg.text3Below": "平均より 3 標準偏差下", "SSE.Views.FormatRulesManagerDlg.textAbove": "平均より上", "SSE.Views.FormatRulesManagerDlg.textApply": "に適用する", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "セルの値の先頭 ", "SSE.Views.FormatRulesManagerDlg.textBelow": "平均より下​​", "SSE.Views.FormatRulesManagerDlg.textBetween": "｛０｝と｛１｝の間に", "SSE.Views.FormatRulesManagerDlg.textCellValue": "セルの値", "SSE.Views.FormatRulesManagerDlg.textColorScale": "グラデーション色スケール", "SSE.Views.FormatRulesManagerDlg.textContains": "セルの値に含まれる", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "セルは空白の値があります", "SSE.Views.FormatRulesManagerDlg.textContainsError": "セルはエラーがあります", "SSE.Views.FormatRulesManagerDlg.textDelete": "削除する", "SSE.Views.FormatRulesManagerDlg.textDown": "ルールを下に動かす", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "重複値", "SSE.Views.FormatRulesManagerDlg.textEdit": "編集", "SSE.Views.FormatRulesManagerDlg.textEnds": "セルの値の末尾", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "次の値に等しいまたは平均以上", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "次の値に等しいまたは平均以下", "SSE.Views.FormatRulesManagerDlg.textFormat": "形式", "SSE.Views.FormatRulesManagerDlg.textIconSet": "アイコンセット", "SSE.Views.FormatRulesManagerDlg.textNew": "新しい", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "｛０｝と｛１｝の間にない", "SSE.Views.FormatRulesManagerDlg.textNotContains": "セルの値に含まれない", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "セルは空白の値がありません", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "セルはエラーがありません", "SSE.Views.FormatRulesManagerDlg.textRules": "ルール", "SSE.Views.FormatRulesManagerDlg.textScope": "のフォーマットルールを表示する", "SSE.Views.FormatRulesManagerDlg.textSelectData": "データの選択", "SSE.Views.FormatRulesManagerDlg.textSelection": "現在の選択", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "このピボット", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "このシート", "SSE.Views.FormatRulesManagerDlg.textThisTable": "この表", "SSE.Views.FormatRulesManagerDlg.textUnique": "一意の値", "SSE.Views.FormatRulesManagerDlg.textUp": "ルールを上に動かす", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "この要素が別のユーザーによって編集されています。", "SSE.Views.FormatRulesManagerDlg.txtTitle": "条件付き書式", "SSE.Views.FormatSettingsDialog.textCategory": "カテゴリー", "SSE.Views.FormatSettingsDialog.textDecimal": "小数点", "SSE.Views.FormatSettingsDialog.textFormat": "形式", "SSE.Views.FormatSettingsDialog.textLinked": "ソースにリンクした", "SSE.Views.FormatSettingsDialog.textSeparator": "1000 の区切り文字を使用する", "SSE.Views.FormatSettingsDialog.textSymbols": "記号と特殊文字", "SSE.Views.FormatSettingsDialog.textTitle": "数値の書式", "SSE.Views.FormatSettingsDialog.txtAccounting": "会計", "SSE.Views.FormatSettingsDialog.txtAs10": "分母を１０に設定 (５/１０)", "SSE.Views.FormatSettingsDialog.txtAs100": "分母を１００に設定 (５０/１００)", "SSE.Views.FormatSettingsDialog.txtAs16": "分母を１６に設定 (８/１６)", "SSE.Views.FormatSettingsDialog.txtAs2": "分母を２に設定 (１/２)", "SSE.Views.FormatSettingsDialog.txtAs4": "分母を４に設定 (２/４)", "SSE.Views.FormatSettingsDialog.txtAs8": "分母を8に設定 (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "通貨", "SSE.Views.FormatSettingsDialog.txtCustom": "ユーザー設定", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "カスタム番号の形式を慎重に入力してください。 Spreadsheet Editorは、xlsxファイルに影響を与える可能性のあるエラーについてカスタム形式をチェックしません。", "SSE.Views.FormatSettingsDialog.txtDate": "日付", "SSE.Views.FormatSettingsDialog.txtFraction": "分数", "SSE.Views.FormatSettingsDialog.txtGeneral": "標準", "SSE.Views.FormatSettingsDialog.txtNone": "なし", "SSE.Views.FormatSettingsDialog.txtNumber": "数値", "SSE.Views.FormatSettingsDialog.txtPercentage": "パーセンテージ", "SSE.Views.FormatSettingsDialog.txtSample": "例：", "SSE.Views.FormatSettingsDialog.txtScientific": "指数", "SSE.Views.FormatSettingsDialog.txtText": "テキスト", "SSE.Views.FormatSettingsDialog.txtTime": "時刻", "SSE.Views.FormatSettingsDialog.txtUpto1": "最大1桁（1/3）", "SSE.Views.FormatSettingsDialog.txtUpto2": "最大2桁（12/25）", "SSE.Views.FormatSettingsDialog.txtUpto3": "最大3桁（131/135）", "SSE.Views.FormulaDialog.sDescription": "説明", "SSE.Views.FormulaDialog.textGroupDescription": "機能グループの選択", "SSE.Views.FormulaDialog.textListDescription": "機能の選択", "SSE.Views.FormulaDialog.txtRecommended": "おすすめ", "SSE.Views.FormulaDialog.txtSearch": "検索", "SSE.Views.FormulaDialog.txtTitle": "関数を挿入", "SSE.Views.FormulaTab.capBtnRemoveArr": "矢印を削除", "SSE.Views.FormulaTab.capBtnTraceDep": "参照先のトレース", "SSE.Views.FormulaTab.capBtnTracePrec": "参照元のトレース", "SSE.Views.FormulaTab.textAutomatic": "自動", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "このシートを計算する", "SSE.Views.FormulaTab.textCalculateWorkbook": "ワークブックを計算する", "SSE.Views.FormulaTab.textManual": "手動的に", "SSE.Views.FormulaTab.tipCalculate": "計算", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "ワークブック全体を計算する", "SSE.Views.FormulaTab.tipRemoveArr": "「参照元のトレース」または「参照先のトレース」で表示された矢印を削除します。", "SSE.Views.FormulaTab.tipShowFormulas": "各セルに、結果の値の代わりに数式を表示する", "SSE.Views.FormulaTab.tipTraceDep": "選択したセルの値によって影響を受けるセルを示す矢印を表示する", "SSE.Views.FormulaTab.tipTracePrec": "選択したセルの値に影響を与えるセルを示す矢印を表示する", "SSE.Views.FormulaTab.tipWatch": "ウォッチウィンドウの一覧にセルを追加する", "SSE.Views.FormulaTab.txtAdditional": "追加", "SSE.Views.FormulaTab.txtAutosum": "自動合計", "SSE.Views.FormulaTab.txtAutosumTip": "合計", "SSE.Views.FormulaTab.txtCalculation": "計算", "SSE.Views.FormulaTab.txtFormula": "関数", "SSE.Views.FormulaTab.txtFormulaTip": "関数を挿入", "SSE.Views.FormulaTab.txtMore": "その他の関数", "SSE.Views.FormulaTab.txtRecent": "最近使った項目", "SSE.Views.FormulaTab.txtRemDep": "参照先トレースの矢印を削除", "SSE.Views.FormulaTab.txtRemPrec": "参照元トレースの矢印を削除", "SSE.Views.FormulaTab.txtShowFormulas": "数式を表示する", "SSE.Views.FormulaTab.txtWatch": "ウォッチ ウィンドウ", "SSE.Views.FormulaWizard.textAny": "すべて", "SSE.Views.FormulaWizard.textArgument": "引数", "SSE.Views.FormulaWizard.textFunction": "関数", "SSE.Views.FormulaWizard.textFunctionRes": "関数の結果", "SSE.Views.FormulaWizard.textHelp": "この関数について", "SSE.Views.FormulaWizard.textLogical": "論理", "SSE.Views.FormulaWizard.textNoArgs": "この関数には引数がありません", "SSE.Views.FormulaWizard.textNoArgsDesc": "この引数には説明がありません", "SSE.Views.FormulaWizard.textNumber": "数", "SSE.Views.FormulaWizard.textReadMore": "続きを読む", "SSE.Views.FormulaWizard.textRef": "参照", "SSE.Views.FormulaWizard.textText": "テキスト", "SSE.Views.FormulaWizard.textTitle": "関数の引数", "SSE.Views.FormulaWizard.textValue": "数式の計算結果", "SSE.Views.GoalSeekDlg.textChangingCell": "変化させるセル", "SSE.Views.GoalSeekDlg.textDataRangeError": "数式に範囲がありません", "SSE.Views.GoalSeekDlg.textMustContainFormula": "セルは数式を含んでいなければならない", "SSE.Views.GoalSeekDlg.textMustContainValue": "セルは値を含まなければならない", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "セル内の数式は数値にならなければならない", "SSE.Views.GoalSeekDlg.textMustSingleCell": "参照は単一セルでなければならない", "SSE.Views.GoalSeekDlg.textSelectData": "データの選択", "SSE.Views.GoalSeekDlg.textSetCell": "セルを設定する", "SSE.Views.GoalSeekDlg.textTitle": "ゴールシーク", "SSE.Views.GoalSeekDlg.textToValue": "値", "SSE.Views.GoalSeekDlg.txtEmpty": "このフィールドは必須項目です", "SSE.Views.GoalSeekDlg.txtErrorNumber": "入力した内容は使用できません。恐らく整数または10進数である必要があります。", "SSE.Views.GoalSeekStatusDlg.textContinue": "続ける", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "現在の値：", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "セル{0}のゴールシークで解が見つかりました。", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "セル{0}のゴールシークで解が見つからなかった可能性があります。", "SSE.Views.GoalSeekStatusDlg.textPause": "休止", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "セル{0}のゴールシークの反復 #{1} を実行中です。", "SSE.Views.GoalSeekStatusDlg.textStep": "ステップ", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "ターゲット値：", "SSE.Views.GoalSeekStatusDlg.textTitle": "ゴールシークのステータス", "SSE.Views.HeaderFooterDialog.textAlign": "ページの余白に合わせて整列する", "SSE.Views.HeaderFooterDialog.textAll": "全ページ", "SSE.Views.HeaderFooterDialog.textBold": "太字", "SSE.Views.HeaderFooterDialog.textCenter": "中央揃え", "SSE.Views.HeaderFooterDialog.textColor": "文字の色", "SSE.Views.HeaderFooterDialog.textDate": "日付", "SSE.Views.HeaderFooterDialog.textDiffFirst": "先頭ページ​​のみ別指定", "SSE.Views.HeaderFooterDialog.textDiffOdd": "奇数/偶数ページ別指定", "SSE.Views.HeaderFooterDialog.textEven": "偶数ページ", "SSE.Views.HeaderFooterDialog.textFileName": "ファイル名", "SSE.Views.HeaderFooterDialog.textFirst": "最初のページ", "SSE.Views.HeaderFooterDialog.textFooter": "フッター", "SSE.Views.HeaderFooterDialog.textHeader": "ヘッダー", "SSE.Views.HeaderFooterDialog.textImage": "画像", "SSE.Views.HeaderFooterDialog.textInsert": "挿入", "SSE.Views.HeaderFooterDialog.textItalic": "イタリック体", "SSE.Views.HeaderFooterDialog.textLeft": "左", "SSE.Views.HeaderFooterDialog.textMaxError": "入力したテキスト文字列が長すぎます。 入力文字数を減らしてください。", "SSE.Views.HeaderFooterDialog.textNewColor": "その他の色", "SSE.Views.HeaderFooterDialog.textOdd": "奇数ページ", "SSE.Views.HeaderFooterDialog.textPageCount": "ページの数", "SSE.Views.HeaderFooterDialog.textPageNum": "ページ番号", "SSE.Views.HeaderFooterDialog.textPresets": "プリセット", "SSE.Views.HeaderFooterDialog.textRight": "右に", "SSE.Views.HeaderFooterDialog.textScale": "ドキュメントに合わせて拡大縮小", "SSE.Views.HeaderFooterDialog.textSheet": "シートの名前", "SSE.Views.HeaderFooterDialog.textStrikeout": "取り消し線", "SSE.Views.HeaderFooterDialog.textSubscript": "下付き", "SSE.Views.HeaderFooterDialog.textSuperscript": "上付き", "SSE.Views.HeaderFooterDialog.textTime": "時刻", "SSE.Views.HeaderFooterDialog.textTitle": "ヘッダー/フッター設定", "SSE.Views.HeaderFooterDialog.textUnderline": "下線", "SSE.Views.HeaderFooterDialog.tipFontName": "フォント", "SSE.Views.HeaderFooterDialog.tipFontSize": "フォントのサイズ", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "表示", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "リンク", "SSE.Views.HyperlinkSettingsDialog.strRange": "範囲", "SSE.Views.HyperlinkSettingsDialog.strSheet": "シート", "SSE.Views.HyperlinkSettingsDialog.textCopy": "コピー", "SSE.Views.HyperlinkSettingsDialog.textDefault": "選択されたデータ範囲", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "ここでキャプションを挿入してください。", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "ここでリンクを挿入してください。", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "ここでヒントを挿入してください。", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "外部のリンク", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "リンクを取得する", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "内部のデータ範囲", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "エラー！セルの範囲が正しくありません。", "SSE.Views.HyperlinkSettingsDialog.textNames": "定義された名前", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "データの選択", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "ファイル選択", "SSE.Views.HyperlinkSettingsDialog.textSheets": "シート", "SSE.Views.HyperlinkSettingsDialog.textTipText": "ヒントのテキスト:", "SSE.Views.HyperlinkSettingsDialog.textTitle": "ハイパーリンクの設定", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "このフィールドは必須項目です", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "リンクの入力内容は「http://www.example.com」形式のURLである必要があります。", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "このフィールドは2083文字に制限されています", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "ウェブアドレスを入力するか、ファイルを選択してください", "SSE.Views.ImageSettings.strTransparency": "不透明度", "SSE.Views.ImageSettings.textAdvanced": "詳細設定の表示", "SSE.Views.ImageSettings.textCrop": "トリミング", "SSE.Views.ImageSettings.textCropFill": "塗りつぶし", "SSE.Views.ImageSettings.textCropFit": "合わせる", "SSE.Views.ImageSettings.textCropToShape": "図形に合わせてトリミング", "SSE.Views.ImageSettings.textEdit": "編集", "SSE.Views.ImageSettings.textEditObject": "オブジェクトを編集する", "SSE.Views.ImageSettings.textFlip": "反転する", "SSE.Views.ImageSettings.textFromFile": "ファイルから", "SSE.Views.ImageSettings.textFromStorage": "ストレージから", "SSE.Views.ImageSettings.textFromUrl": "URLから", "SSE.Views.ImageSettings.textHeight": "高さ", "SSE.Views.ImageSettings.textHint270": "反時計回りに90度回転", "SSE.Views.ImageSettings.textHint90": "時計回りに90度回転", "SSE.Views.ImageSettings.textHintFlipH": "左右反転", "SSE.Views.ImageSettings.textHintFlipV": "上下反転", "SSE.Views.ImageSettings.textInsert": "画像の置き換え", "SSE.Views.ImageSettings.textKeepRatio": "比例の定数", "SSE.Views.ImageSettings.textOriginalSize": "実際のサイズ", "SSE.Views.ImageSettings.textRecentlyUsed": "最近使った項目", "SSE.Views.ImageSettings.textResetCrop": "トリミングをリセット", "SSE.Views.ImageSettings.textRotate90": "90度回転", "SSE.Views.ImageSettings.textRotation": "回転", "SSE.Views.ImageSettings.textSize": "サイズ", "SSE.Views.ImageSettings.textWidth": "幅", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "セルで移動したりサイズを変更したりしない", "SSE.Views.ImageSettingsAdvanced.textAlt": "代替テキスト", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "説明", "SSE.Views.ImageSettingsAdvanced.textAltTip": "視覚障害や認知障害のある人が、画像や図形、図表にどのような情報が含まれているかを理解しやすくするため、そのオブジェクトについて目視できる情報を文章で表現したものです。", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "タイトル", "SSE.Views.ImageSettingsAdvanced.textAngle": "角", "SSE.Views.ImageSettingsAdvanced.textFlipped": "反転", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "水平に", "SSE.Views.ImageSettingsAdvanced.textOneCell": "移動するが、セルでサイズを変更しない", "SSE.Views.ImageSettingsAdvanced.textRotation": "回転", "SSE.Views.ImageSettingsAdvanced.textSnap": "セルに合わせる", "SSE.Views.ImageSettingsAdvanced.textTitle": "画像 - 詳細設定", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "セルで移動してサイズを変更する", "SSE.Views.ImageSettingsAdvanced.textVertically": "縦に", "SSE.Views.ImportFromXmlDialog.textDestination": "データをどこに置くか、選択してください", "SSE.Views.ImportFromXmlDialog.textExist": "既存のワークシート", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "無効なセル範囲", "SSE.Views.ImportFromXmlDialog.textNew": "新しいワークシート", "SSE.Views.ImportFromXmlDialog.textSelectData": "データの選択", "SSE.Views.ImportFromXmlDialog.textTitle": "データのインポート", "SSE.Views.ImportFromXmlDialog.txtEmpty": "この項目は必須です", "SSE.Views.LeftMenu.ariaLeftMenu": "左メニュー", "SSE.Views.LeftMenu.tipAbout": "詳細情報", "SSE.Views.LeftMenu.tipChat": "チャット", "SSE.Views.LeftMenu.tipComments": "コメント", "SSE.Views.LeftMenu.tipFile": "ファイル", "SSE.Views.LeftMenu.tipPlugins": "プラグイン", "SSE.Views.LeftMenu.tipSearch": "検索", "SSE.Views.LeftMenu.tipSpellcheck": "スペルチェック", "SSE.Views.LeftMenu.tipSupport": "フィードバック＆サポート", "SSE.Views.LeftMenu.txtDeveloper": "開発者モード", "SSE.Views.LeftMenu.txtEditor": "スプレッドシートエディター", "SSE.Views.LeftMenu.txtLimit": "制限されたアクセス", "SSE.Views.LeftMenu.txtTrial": "試用モード", "SSE.Views.LeftMenu.txtTrialDev": "試用開発者モード", "SSE.Views.MacroDialog.textMacro": "マクロの名", "SSE.Views.MacroDialog.textTitle": "マクロの登録", "SSE.Views.MainSettingsPrint.okButtonText": "保存", "SSE.Views.MainSettingsPrint.strBottom": "下", "SSE.Views.MainSettingsPrint.strLandscape": "横", "SSE.Views.MainSettingsPrint.strLeft": "左", "SSE.Views.MainSettingsPrint.strMargins": "余白", "SSE.Views.MainSettingsPrint.strPortrait": "縦", "SSE.Views.MainSettingsPrint.strPrint": "印刷", "SSE.Views.MainSettingsPrint.strPrintTitles": "タイトルを印刷する", "SSE.Views.MainSettingsPrint.strRight": "右に", "SSE.Views.MainSettingsPrint.strTop": "トップ", "SSE.Views.MainSettingsPrint.textActualSize": "実際のサイズ", "SSE.Views.MainSettingsPrint.textCustom": "ユーザー設定", "SSE.Views.MainSettingsPrint.textCustomOptions": "ユーザー設定", "SSE.Views.MainSettingsPrint.textFitCols": "すべての列を 1 ページに表示", "SSE.Views.MainSettingsPrint.textFitPage": "シートを 1 ページに表示", "SSE.Views.MainSettingsPrint.textFitRows": "すべての行を 1 ページに表示", "SSE.Views.MainSettingsPrint.textPageOrientation": "印刷の向き", "SSE.Views.MainSettingsPrint.textPageScaling": "拡大縮小", "SSE.Views.MainSettingsPrint.textPageSize": "ページのサイズ", "SSE.Views.MainSettingsPrint.textPrintGrid": "枠線の印刷", "SSE.Views.MainSettingsPrint.textPrintHeadings": "行と列の見出しを印刷", "SSE.Views.MainSettingsPrint.textRepeat": "繰り返す．．．", "SSE.Views.MainSettingsPrint.textRepeatLeft": "左側の列を繰り返す", "SSE.Views.MainSettingsPrint.textRepeatTop": "上の行を繰り返す", "SSE.Views.MainSettingsPrint.textSettings": "設定", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "存在する名前付き範囲を編集することはできません。<br>今、範囲が編集されているので、新しい名前付き範囲を作成することはできません。", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "定義された名前", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "警告", "SSE.Views.NamedRangeEditDlg.strWorkbook": "ブック", "SSE.Views.NamedRangeEditDlg.textDataRange": "データ範囲", "SSE.Views.NamedRangeEditDlg.textExistName": "エラー！すでに同じ名前がある範囲も存在しています。", "SSE.Views.NamedRangeEditDlg.textInvalidName": "エラー！範囲の名前が正しくありません。", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "エラー！セルの範囲が正しくありません。", "SSE.Views.NamedRangeEditDlg.textIsLocked": "エラー!要素が他のユーザーによって編集されています。", "SSE.Views.NamedRangeEditDlg.textName": "名前", "SSE.Views.NamedRangeEditDlg.textReservedName": "使用しようとしている名前は、既にセルの数式で参照されています。他の名前を使用してください。", "SSE.Views.NamedRangeEditDlg.textScope": "スコープ", "SSE.Views.NamedRangeEditDlg.textSelectData": "データの選択", "SSE.Views.NamedRangeEditDlg.txtEmpty": "このフィールドは必須項目です", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "名前を編集", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "新しい名前", "SSE.Views.NamedRangePasteDlg.textNames": "名前付き一覧\t", "SSE.Views.NamedRangePasteDlg.txtTitle": "名前の貼り付け", "SSE.Views.NameManagerDlg.closeButtonText": "閉じる", "SSE.Views.NameManagerDlg.guestText": "ゲスト", "SSE.Views.NameManagerDlg.lockText": "ロックされた", "SSE.Views.NameManagerDlg.textDataRange": "データ範囲", "SSE.Views.NameManagerDlg.textDelete": "削除", "SSE.Views.NameManagerDlg.textEdit": "編集", "SSE.Views.NameManagerDlg.textEmpty": "名前付き範囲は、まだ作成されていません。<br>最低で一つの名前付き範囲を作成すると、このフィールドに表示されます。", "SSE.Views.NameManagerDlg.textFilter": "フィルター​​", "SSE.Views.NameManagerDlg.textFilterAll": "すべて", "SSE.Views.NameManagerDlg.textFilterDefNames": "定義された名前", "SSE.Views.NameManagerDlg.textFilterSheet": "シートに名前の範囲指定", "SSE.Views.NameManagerDlg.textFilterTableNames": "表の名前", "SSE.Views.NameManagerDlg.textFilterWorkbook": "ワークブックに名前の範囲指定", "SSE.Views.NameManagerDlg.textNew": "新しい", "SSE.Views.NameManagerDlg.textnoNames": "フィルタ条件に一致する名前付き一覧が見つかりませんでした。", "SSE.Views.NameManagerDlg.textRanges": "名前付き一覧\t", "SSE.Views.NameManagerDlg.textScope": "スコープ", "SSE.Views.NameManagerDlg.textWorkbook": "ブック", "SSE.Views.NameManagerDlg.tipIsLocked": "この要素が別のユーザーによって編集されています。", "SSE.Views.NameManagerDlg.txtTitle": "名前の管理", "SSE.Views.NameManagerDlg.warnDelete": "{0}名前を削除してもよろしいですか？", "SSE.Views.PageMarginsDialog.textBottom": "下", "SSE.Views.PageMarginsDialog.textCenter": "ページの中央", "SSE.Views.PageMarginsDialog.textHor": "水平に", "SSE.Views.PageMarginsDialog.textLeft": "左", "SSE.Views.PageMarginsDialog.textRight": "右", "SSE.Views.PageMarginsDialog.textTitle": "余白", "SSE.Views.PageMarginsDialog.textTop": "上", "SSE.Views.PageMarginsDialog.textVert": "縦に", "SSE.Views.PageMarginsDialog.textWarning": "警告", "SSE.Views.PageMarginsDialog.warnCheckMargings": "余白が正しくありません", "SSE.Views.ParagraphSettings.strLineHeight": "行間", "SSE.Views.ParagraphSettings.strParagraphSpacing": "段落の間隔", "SSE.Views.ParagraphSettings.strSpacingAfter": "後", "SSE.Views.ParagraphSettings.strSpacingBefore": "前", "SSE.Views.ParagraphSettings.textAdvanced": "詳細設定の表示", "SSE.Views.ParagraphSettings.textAt": "行間", "SSE.Views.ParagraphSettings.textAtLeast": "最小", "SSE.Views.ParagraphSettings.textAuto": "複数", "SSE.Views.ParagraphSettings.textExact": "固定値", "SSE.Views.ParagraphSettings.txtAutoText": "自動", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "指定されたタブは、このフィールドに表示されます。", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "すべて大文字", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "二重取り消し線", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "インデント", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "左", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "行間", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "右に", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "後", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "前", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "特殊", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "幅", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "フォント", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "インデント＆行間隔", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "小型英大文字\t", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "間隔", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "取り消し線", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "下付き", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "上付き文字", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "タブ", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "配置", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "複数", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "文字間隔", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "既定のタブ", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "効果", "SSE.Views.ParagraphSettingsAdvanced.textExact": "固定値", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "先頭行", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "ぶら下がり", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "両端揃え(英文)", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "（なし）", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "削除", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "全てを削除", "SSE.Views.ParagraphSettingsAdvanced.textSet": "指定", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "中央揃え", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "左", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "タブの位置", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "右揃え", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "段落 - 詳細設定", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "自動", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "削除", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "複製", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "編集", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "数式", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "アイテム名", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "新しい", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "計算項目", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "等号", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "次の文字列で終わらない", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "含んでいる\t", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "次の文字を含まない", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "間", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "間ではない", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "指定の値に等しくない", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "がより大きい", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "より以上か等しい", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "がより小さい", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "より以下か等しい", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "で始まる", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "次の文字から始まらない", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "終了", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "ラベルが次の条件に一致する項目を表示する", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "次の条件に一致する項目を表示する：", "SSE.Views.PivotDigitalFilterDialog.textUse1": "?を使って、任意の1文字を表すことができます。", "SSE.Views.PivotDigitalFilterDialog.textUse2": "一連の文字の代わりに*をご使用ください", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "と", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "ラベル・フィルター", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "値フィルター", "SSE.Views.PivotGroupDialog.textAuto": "自動", "SSE.Views.PivotGroupDialog.textBy": "幅", "SSE.Views.PivotGroupDialog.textDays": "日", "SSE.Views.PivotGroupDialog.textEnd": "終了", "SSE.Views.PivotGroupDialog.textError": "このフィールドは数値である必要があります", "SSE.Views.PivotGroupDialog.textGreaterError": "終了番号は開始番号より大きくなければなりません", "SSE.Views.PivotGroupDialog.textHour": "時間", "SSE.Views.PivotGroupDialog.textMin": "分", "SSE.Views.PivotGroupDialog.textMonth": "月", "SSE.Views.PivotGroupDialog.textNumDays": "日数", "SSE.Views.PivotGroupDialog.textQuart": "四半期", "SSE.Views.PivotGroupDialog.textSec": "秒", "SSE.Views.PivotGroupDialog.textStart": "から開始する", "SSE.Views.PivotGroupDialog.textYear": "年", "SSE.Views.PivotGroupDialog.txtTitle": "グループ化", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "単一フィールド内の異なる項目間の基本的な計算には、計算項目を使用できます", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "数式", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "数式に挿入", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "アイテム", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "項目名", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "アイテム", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "続きを読む", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "計算項目を挿入", "SSE.Views.PivotSettings.textAdvanced": "詳細設定の表示", "SSE.Views.PivotSettings.textColumns": "列", "SSE.Views.PivotSettings.textFields": "フィールドを選択する", "SSE.Views.PivotSettings.textFilters": "フィルター", "SSE.Views.PivotSettings.textRows": "行", "SSE.Views.PivotSettings.textValues": "値", "SSE.Views.PivotSettings.txtAddColumn": "カラムを追加", "SSE.Views.PivotSettings.txtAddFilter": "フィルターに追加", "SSE.Views.PivotSettings.txtAddRow": "行に追加", "SSE.Views.PivotSettings.txtAddValues": "値に追加", "SSE.Views.PivotSettings.txtFieldSettings": "フィールド設定", "SSE.Views.PivotSettings.txtMoveBegin": "はじめに移動する", "SSE.Views.PivotSettings.txtMoveColumn": "列に移動する", "SSE.Views.PivotSettings.txtMoveDown": "下に移動する", "SSE.Views.PivotSettings.txtMoveEnd": "終わりに移動する", "SSE.Views.PivotSettings.txtMoveFilter": "フィルターに移動する", "SSE.Views.PivotSettings.txtMoveRow": "行に移動する", "SSE.Views.PivotSettings.txtMoveUp": "上に移動する", "SSE.Views.PivotSettings.txtMoveValues": "値に移動する", "SSE.Views.PivotSettings.txtRemove": "フィールドを削除する", "SSE.Views.PivotSettingsAdvanced.strLayout": "名前とレイアウト", "SSE.Views.PivotSettingsAdvanced.textAlt": "代替テキスト", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "説明", "SSE.Views.PivotSettingsAdvanced.textAltTip": "視覚障害や認知障害のある人が、画像や図形、図表にどのような情報が含まれているかを理解しやすくするため、そのオブジェクトについて目視できる情報を文章で表現したものです。", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "タイトル", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "更新時に列幅を自動調整する", "SSE.Views.PivotSettingsAdvanced.textDataRange": "データ範囲", "SSE.Views.PivotSettingsAdvanced.textDataSource": "データソース", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "レポート・フィルター範囲にフィールドを表示する", "SSE.Views.PivotSettingsAdvanced.textDown": "上から下", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "総計", "SSE.Views.PivotSettingsAdvanced.textHeaders": "フィールドのヘッダー", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "エラー！セルの範囲は無効です。", "SSE.Views.PivotSettingsAdvanced.textOver": "左から右", "SSE.Views.PivotSettingsAdvanced.textSelectData": "データの選択", "SSE.Views.PivotSettingsAdvanced.textShowCols": "列に表示", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "行と列のフィールドヘッダーを表示する", "SSE.Views.PivotSettingsAdvanced.textShowRows": "行に表示", "SSE.Views.PivotSettingsAdvanced.textTitle": "ピボットテーブルの詳細設定", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "列ごとのレポートフィルターフィールド", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "行ごとのレポートフィルターフィールド", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "この項目は必須です", "SSE.Views.PivotSettingsAdvanced.txtName": "名前", "SSE.Views.PivotShowDetailDialog.textDescription": "表示したい詳細を含むフィールドを選択する：", "SSE.Views.PivotShowDetailDialog.txtTitle": "詳細を表示", "SSE.Views.PivotTable.capBlankRows": "空行", "SSE.Views.PivotTable.capGrandTotals": "総計", "SSE.Views.PivotTable.capLayout": "レポートのレイアウト", "SSE.Views.PivotTable.capSubtotals": "小計", "SSE.Views.PivotTable.mniBottomSubtotals": "すべての小計をグループの下に表示する", "SSE.Views.PivotTable.mniInsertBlankLine": "各項目の後に空白行を挿入する", "SSE.Views.PivotTable.mniLayoutCompact": "コンパクト形式で表示", "SSE.Views.PivotTable.mniLayoutNoRepeat": "すべてのアイテムラベルを繰り返さない", "SSE.Views.PivotTable.mniLayoutOutline": "アウトライン形式で表示", "SSE.Views.PivotTable.mniLayoutRepeat": "すべてのアイテムラベルを繰り返す", "SSE.Views.PivotTable.mniLayoutTabular": "表形式で表示", "SSE.Views.PivotTable.mniNoSubtotals": "小計を表示しない", "SSE.Views.PivotTable.mniOffTotals": "行と列には無効にする", "SSE.Views.PivotTable.mniOnColumnsTotals": "行と列には有効にする", "SSE.Views.PivotTable.mniOnRowsTotals": "行のみに有効にする", "SSE.Views.PivotTable.mniOnTotals": "行と列には有効にする", "SSE.Views.PivotTable.mniRemoveBlankLine": "各項目の後の空白行を削除する", "SSE.Views.PivotTable.mniTopSubtotals": "すべての小計をグループの上に表示する", "SSE.Views.PivotTable.textColBanded": "縞模様の例", "SSE.Views.PivotTable.textColHeader": "列のヘッダー", "SSE.Views.PivotTable.textRowBanded": "縞模様の行", "SSE.Views.PivotTable.textRowHeader": "行のヘッダー", "SSE.Views.PivotTable.tipCalculatedItems": "計算項目", "SSE.Views.PivotTable.tipCreatePivot": "ピボットテーブルを挿入", "SSE.Views.PivotTable.tipGrandTotals": "総計を表示か非表示する", "SSE.Views.PivotTable.tipRefresh": "データソースからの情報を更新する", "SSE.Views.PivotTable.tipRefreshCurrent": "データソースから現在のテーブルの情報を更新する", "SSE.Views.PivotTable.tipSelect": "ピボットテーブル全体を選択する", "SSE.Views.PivotTable.tipSubtotals": "小計を表示か非表示する", "SSE.Views.PivotTable.txtCalculatedItems": "計算項目", "SSE.Views.PivotTable.txtCollapseEntire": "フィールド全体を折りたたむ", "SSE.Views.PivotTable.txtCreate": "表を挿入", "SSE.Views.PivotTable.txtExpandEntire": "フィールド全体を拡張する", "SSE.Views.PivotTable.txtGroupPivot_Custom": "ユーザー設定", "SSE.Views.PivotTable.txtGroupPivot_Dark": "ダーク", "SSE.Views.PivotTable.txtGroupPivot_Light": "ライト", "SSE.Views.PivotTable.txtGroupPivot_Medium": "中", "SSE.Views.PivotTable.txtPivotTable": "ピボットテーブル", "SSE.Views.PivotTable.txtRefresh": "更新", "SSE.Views.PivotTable.txtRefreshAll": "すべて更新", "SSE.Views.PivotTable.txtSelect": "選択する", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "ダークスタイルのピボットテーブル", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "ライトスタイルのピボットテーブル", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "ミディアムスタイルのピボットテーブル", "SSE.Views.PrintSettings.btnDownload": "保存してダウンロード", "SSE.Views.PrintSettings.btnExport": "保存＆書き出し", "SSE.Views.PrintSettings.btnPrint": "保存&印刷", "SSE.Views.PrintSettings.strBottom": "下", "SSE.Views.PrintSettings.strLandscape": "横", "SSE.Views.PrintSettings.strLeft": "左", "SSE.Views.PrintSettings.strMargins": "余白", "SSE.Views.PrintSettings.strPortrait": "縦", "SSE.Views.PrintSettings.strPrint": "印刷", "SSE.Views.PrintSettings.strPrintTitles": "タイトルを印刷する", "SSE.Views.PrintSettings.strRight": "右に", "SSE.Views.PrintSettings.strShow": "表示する", "SSE.Views.PrintSettings.strTop": "トップ", "SSE.Views.PrintSettings.textActiveSheets": "作業中のシート", "SSE.Views.PrintSettings.textActualSize": "実際のサイズ", "SSE.Views.PrintSettings.textAllSheets": "全シート", "SSE.Views.PrintSettings.textCurrentSheet": "現在のシート", "SSE.Views.PrintSettings.textCustom": "ユーザー設定", "SSE.Views.PrintSettings.textCustomOptions": "ユーザー設定", "SSE.Views.PrintSettings.textFitCols": "すべての列を 1 ページに表示", "SSE.Views.PrintSettings.textFitPage": "シートを 1 ページに表示", "SSE.Views.PrintSettings.textFitRows": "すべての行を 1 ページに表示", "SSE.Views.PrintSettings.textHideDetails": "詳細を非表示", "SSE.Views.PrintSettings.textIgnore": "印刷範囲を無視する", "SSE.Views.PrintSettings.textLayout": "レイアウト", "SSE.Views.PrintSettings.textMarginsNarrow": "狭い", "SSE.Views.PrintSettings.textMarginsNormal": "標準", "SSE.Views.PrintSettings.textMarginsWide": "広い", "SSE.Views.PrintSettings.textPageOrientation": "印刷の向き", "SSE.Views.PrintSettings.textPages": "ページ：", "SSE.Views.PrintSettings.textPageScaling": "拡大縮小", "SSE.Views.PrintSettings.textPageSize": "ページのサイズ", "SSE.Views.PrintSettings.textPrintGrid": "枠線の印刷", "SSE.Views.PrintSettings.textPrintHeadings": "行と列の見出しを印刷", "SSE.Views.PrintSettings.textPrintRange": "印刷範囲\t", "SSE.Views.PrintSettings.textRange": "範囲", "SSE.Views.PrintSettings.textRepeat": "繰り返す．．．", "SSE.Views.PrintSettings.textRepeatLeft": "左側の列を繰り返す", "SSE.Views.PrintSettings.textRepeatTop": "上の行を繰り返す", "SSE.Views.PrintSettings.textSelection": "選択", "SSE.Views.PrintSettings.textSettings": "シートの設定", "SSE.Views.PrintSettings.textShowDetails": "詳細の表示", "SSE.Views.PrintSettings.textShowGrid": "枠線を表示する", "SSE.Views.PrintSettings.textShowHeadings": "行と列の見出しを表示する", "SSE.Views.PrintSettings.textTitle": "印刷の設定", "SSE.Views.PrintSettings.textTitlePDF": "PDFの設定", "SSE.Views.PrintSettings.textTo": "まで", "SSE.Views.PrintSettings.txtMarginsLast": "最後に適用した設定", "SSE.Views.PrintTitlesDialog.textFirstCol": "最初の列", "SSE.Views.PrintTitlesDialog.textFirstRow": "最初の行", "SSE.Views.PrintTitlesDialog.textFrozenCols": "固定された列", "SSE.Views.PrintTitlesDialog.textFrozenRows": "固定された行", "SSE.Views.PrintTitlesDialog.textInvalidRange": "エラー！セルの範囲は無効です。", "SSE.Views.PrintTitlesDialog.textLeft": "左側の列を繰り返す", "SSE.Views.PrintTitlesDialog.textNoRepeat": "繰り返なし", "SSE.Views.PrintTitlesDialog.textRepeat": "繰り返す．．．", "SSE.Views.PrintTitlesDialog.textSelectRange": "範囲の選択", "SSE.Views.PrintTitlesDialog.textTitle": "タイトルを印刷する", "SSE.Views.PrintTitlesDialog.textTop": "上の行を繰り返す", "SSE.Views.PrintWithPreview.txtActiveSheets": "作業中のシート", "SSE.Views.PrintWithPreview.txtActualSize": "実際のサイズ", "SSE.Views.PrintWithPreview.txtAllSheets": "全シート", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "全シートに適用", "SSE.Views.PrintWithPreview.txtBothSides": "両面印刷", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "長辺を綴じる", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "短辺を綴じる", "SSE.Views.PrintWithPreview.txtBottom": "下", "SSE.Views.PrintWithPreview.txtCopies": "コピー", "SSE.Views.PrintWithPreview.txtCurrentSheet": "現在のシート", "SSE.Views.PrintWithPreview.txtCustom": "ユーザー設定", "SSE.Views.PrintWithPreview.txtCustomOptions": "ユーザー設定", "SSE.Views.PrintWithPreview.txtEmptyTable": "テーブルが空で印刷できるものはありません", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "先頭ページの番号：", "SSE.Views.PrintWithPreview.txtFitCols": "すべての列を 1 ページに表示", "SSE.Views.PrintWithPreview.txtFitPage": "シートを 1 ページに表示", "SSE.Views.PrintWithPreview.txtFitRows": "すべての行を 1 ページに表示", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "グリッド線と見出し​​", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "ヘッダー/フッター設定", "SSE.Views.PrintWithPreview.txtIgnore": "印刷範囲を無視する", "SSE.Views.PrintWithPreview.txtLandscape": "横", "SSE.Views.PrintWithPreview.txtLeft": "左", "SSE.Views.PrintWithPreview.txtMargins": "余白", "SSE.Views.PrintWithPreview.txtMarginsLast": "最後に適用した設定", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "狭い", "SSE.Views.PrintWithPreview.txtMarginsNormal": "標準", "SSE.Views.PrintWithPreview.txtMarginsWide": "広い", "SSE.Views.PrintWithPreview.txtOf": "{0}から", "SSE.Views.PrintWithPreview.txtOneSide": "片面印刷", "SSE.Views.PrintWithPreview.txtOneSideDesc": "ページの片面のみを印刷する", "SSE.Views.PrintWithPreview.txtPage": "ページ", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "ページ番号が正しくありません。", "SSE.Views.PrintWithPreview.txtPageOrientation": "印刷の向き", "SSE.Views.PrintWithPreview.txtPages": "ページ：", "SSE.Views.PrintWithPreview.txtPageSize": "ページ サイズ", "SSE.Views.PrintWithPreview.txtPortrait": "縦", "SSE.Views.PrintWithPreview.txtPrint": "印刷", "SSE.Views.PrintWithPreview.txtPrintGrid": "枠線の印刷", "SSE.Views.PrintWithPreview.txtPrintHeadings": "行と列の見出しを印刷", "SSE.Views.PrintWithPreview.txtPrintRange": "印刷範囲\t", "SSE.Views.PrintWithPreview.txtPrintSides": "両面印刷", "SSE.Views.PrintWithPreview.txtPrintTitles": "タイトルを印刷する", "SSE.Views.PrintWithPreview.txtPrintToPDF": "PDFに印刷", "SSE.Views.PrintWithPreview.txtRepeat": "繰り返す…", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "左側の列を繰り返す", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "上の行を繰り返す", "SSE.Views.PrintWithPreview.txtRight": "右", "SSE.Views.PrintWithPreview.txtSave": "保存", "SSE.Views.PrintWithPreview.txtScaling": "拡大縮小", "SSE.Views.PrintWithPreview.txtSelection": "選択", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "シート設定", "SSE.Views.PrintWithPreview.txtSheet": "シート：{0}", "SSE.Views.PrintWithPreview.txtTo": "まで", "SSE.Views.PrintWithPreview.txtTop": "トップ", "SSE.Views.ProtectDialog.textExistName": "エラー！すでに同じ名前の範囲があります。", "SSE.Views.ProtectDialog.textInvalidName": "範囲の名前に含めることができるのは、文字、数字、およびスペースだけです", "SSE.Views.ProtectDialog.textInvalidRange": "エラー！セルの範囲が正しくありません。", "SSE.Views.ProtectDialog.textSelectData": "データを選択する", "SSE.Views.ProtectDialog.txtAllow": "このシートのユーザーに許可する", "SSE.Views.ProtectDialog.txtAllowDescription": "特定の範囲の編集を解除することができます。", "SSE.Views.ProtectDialog.txtAllowRanges": "範囲の編集を許可する", "SSE.Views.ProtectDialog.txtAutofilter": "オートフィルター", "SSE.Views.ProtectDialog.txtDelCols": "列を削除", "SSE.Views.ProtectDialog.txtDelRows": "行を削除", "SSE.Views.ProtectDialog.txtEmpty": "このフィールドは必須項目です", "SSE.Views.ProtectDialog.txtFormatCells": "セルをフォーマットする", "SSE.Views.ProtectDialog.txtFormatCols": "列をフォーマットする", "SSE.Views.ProtectDialog.txtFormatRows": "行をフォーマットする", "SSE.Views.ProtectDialog.txtIncorrectPwd": "先に入力したパスワードと一致しません。", "SSE.Views.ProtectDialog.txtInsCols": "列を挿入する", "SSE.Views.ProtectDialog.txtInsHyper": "ハイパーリンクを挿入する", "SSE.Views.ProtectDialog.txtInsRows": "行を挿入する", "SSE.Views.ProtectDialog.txtObjs": "オブジェクトを編集する", "SSE.Views.ProtectDialog.txtOptional": "任意", "SSE.Views.ProtectDialog.txtPassword": "パスワード", "SSE.Views.ProtectDialog.txtPivot": "ピボット表とピボットチャートを使う", "SSE.Views.ProtectDialog.txtProtect": "保護する", "SSE.Views.ProtectDialog.txtRange": "範囲", "SSE.Views.ProtectDialog.txtRangeName": "タイトル", "SSE.Views.ProtectDialog.txtRepeat": "パスワードを再入力", "SSE.Views.ProtectDialog.txtScen": "シナリオを編集する", "SSE.Views.ProtectDialog.txtSelLocked": "ロックしたセルを選択する", "SSE.Views.ProtectDialog.txtSelUnLocked": "アンロックセルを選択する", "SSE.Views.ProtectDialog.txtSheetDescription": "編集権限を制限して、他のユーザーが不用意にデータを変更することを防ぎます", "SSE.Views.ProtectDialog.txtSheetTitle": "シートを保護する", "SSE.Views.ProtectDialog.txtSort": "並べ替え", "SSE.Views.ProtectDialog.txtWarning": "警告: パスワードを忘れると元に戻せません。安全な場所に記録してください。", "SSE.Views.ProtectDialog.txtWBDescription": "他のユーザは非表示のワークシートを表示したり、シート追加、移動、削除したり、シートを非表示、名の変更することができないようにブックの構造をパスワードで保護できます", "SSE.Views.ProtectDialog.txtWBTitle": "ブック構成を保護する", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "匿名", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "誰でも", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "編集", "SSE.Views.ProtectedRangesEditDlg.textCantView": "拒否された", "SSE.Views.ProtectedRangesEditDlg.textCanView": "閲覧", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "範囲の名前に含めることができるのは、文字、数字、およびスペースだけです", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "エラー！セルの範囲が正しくありません。", "SSE.Views.ProtectedRangesEditDlg.textRemove": "削除", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "データの選択", "SSE.Views.ProtectedRangesEditDlg.textYou": "あなた", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "範囲へのアクセス", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "この項目は必須です", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "保護する", "SSE.Views.ProtectedRangesEditDlg.txtRange": "範囲", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "タイトル", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "この範囲を編集できるのは自分だけ", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "名前またはメールアドレスを入力してください", "SSE.Views.ProtectedRangesManagerDlg.guestText": "ゲスト", "SSE.Views.ProtectedRangesManagerDlg.lockText": "ロックされた", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "削除", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "編集", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "保護範囲はまだ作成されていません。<br>少なくとも1つの保護範囲を作成すると、このフィールドに表示されます。", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "フィルタ", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "すべて", "SSE.Views.ProtectedRangesManagerDlg.textNew": "新しい", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "シートを保護する", "SSE.Views.ProtectedRangesManagerDlg.textRange": "範囲", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "編集範囲を選択した人に限定することができます。", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "タイトル", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "この要素が別のユーザーによって編集されています。", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "アクセス", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "拒否された", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "編集", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "範囲を編集する", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "新しい範囲", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "保護された範囲", "SSE.Views.ProtectedRangesManagerDlg.txtView": "表示", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "保護された範囲{0}を削除してよろしいですか？<br>スプレッドシートの編集アクセス権を持っている人は、誰でも範囲のコンテンツを編集することができます。", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "保護された範囲を削除してよろしいですか？<br>スプレッドシートの編集権限を持つ人は誰でも、その範囲のコンテンツを編集することができます。", "SSE.Views.ProtectRangesDlg.guestText": "ゲスト", "SSE.Views.ProtectRangesDlg.lockText": "ロックされた", "SSE.Views.ProtectRangesDlg.textDelete": "削除する", "SSE.Views.ProtectRangesDlg.textEdit": "編集", "SSE.Views.ProtectRangesDlg.textEmpty": "編集可能な範囲がありません", "SSE.Views.ProtectRangesDlg.textNew": "新しい", "SSE.Views.ProtectRangesDlg.textProtect": "シートを保護する", "SSE.Views.ProtectRangesDlg.textPwd": "パスワード", "SSE.Views.ProtectRangesDlg.textRange": "範囲", "SSE.Views.ProtectRangesDlg.textRangesDesc": "シートが保護されているときにパスワードでロックを解除する範囲（ロックされたセルのみ）", "SSE.Views.ProtectRangesDlg.textTitle": "タイトル", "SSE.Views.ProtectRangesDlg.tipIsLocked": "この要素が別のユーザーによって編集されています。", "SSE.Views.ProtectRangesDlg.txtEditRange": "範囲を編集する", "SSE.Views.ProtectRangesDlg.txtNewRange": "新しい範囲", "SSE.Views.ProtectRangesDlg.txtNo": "いいえ", "SSE.Views.ProtectRangesDlg.txtTitle": "ユーザーに範囲の編集を許可する", "SSE.Views.ProtectRangesDlg.txtYes": "はい", "SSE.Views.ProtectRangesDlg.warnDelete": "{0}名前を削除してもよろしいですか？", "SSE.Views.RemoveDuplicatesDialog.textColumns": "列", "SSE.Views.RemoveDuplicatesDialog.textDescription": "重複する値を削除するには、1つ以上の列を選択してください。", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "先頭行に見出しが含まれる場合", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "すべてを選択", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "重複データを削除", "SSE.Views.RightMenu.ariaRightMenu": "右メニュー", "SSE.Views.RightMenu.txtCellSettings": "セル設定", "SSE.Views.RightMenu.txtChartSettings": "グラフの設定", "SSE.Views.RightMenu.txtImageSettings": "画像の設定", "SSE.Views.RightMenu.txtParagraphSettings": "段落の設定", "SSE.Views.RightMenu.txtPivotSettings": "ピボットテーブルの設定", "SSE.Views.RightMenu.txtSettings": "共通設定", "SSE.Views.RightMenu.txtShapeSettings": "図形の設定", "SSE.Views.RightMenu.txtSignatureSettings": "サインの設定", "SSE.Views.RightMenu.txtSlicerSettings": "スライサーの設定", "SSE.Views.RightMenu.txtSparklineSettings": "スパークライン設定", "SSE.Views.RightMenu.txtTableSettings": "表の設定", "SSE.Views.RightMenu.txtTextArtSettings": "テキストアートの設定", "SSE.Views.ScaleDialog.textAuto": "自動", "SSE.Views.ScaleDialog.textError": "入力した値が正しくありません。", "SSE.Views.ScaleDialog.textFewPages": "ページ", "SSE.Views.ScaleDialog.textFitTo": "合わせる:", "SSE.Views.ScaleDialog.textHeight": "高さ", "SSE.Views.ScaleDialog.textManyPages": "ページ", "SSE.Views.ScaleDialog.textOnePage": "ページ", "SSE.Views.ScaleDialog.textScaleTo": "拡大縮小", "SSE.Views.ScaleDialog.textTitle": "スケール設定", "SSE.Views.ScaleDialog.textWidth": "幅", "SSE.Views.SetValueDialog.txtMaxText": "このフィールドの最大値は、{0}です。", "SSE.Views.SetValueDialog.txtMinText": "このフィールドの最小値は、{0}です。", "SSE.Views.ShapeSettings.strBackground": "背景色", "SSE.Views.ShapeSettings.strChange": "図形の変更", "SSE.Views.ShapeSettings.strColor": "色", "SSE.Views.ShapeSettings.strFill": "塗りつぶし", "SSE.Views.ShapeSettings.strForeground": "前景色", "SSE.Views.ShapeSettings.strPattern": "パターン", "SSE.Views.ShapeSettings.strShadow": "影を表示する", "SSE.Views.ShapeSettings.strSize": "サイズ", "SSE.Views.ShapeSettings.strStroke": "線", "SSE.Views.ShapeSettings.strTransparency": "不透明度", "SSE.Views.ShapeSettings.strType": "タイプ", "SSE.Views.ShapeSettings.textAdjustShadow": "影の調整", "SSE.Views.ShapeSettings.textAdvanced": "詳細設定の表示", "SSE.Views.ShapeSettings.textAngle": "角", "SSE.Views.ShapeSettings.textBorderSizeErr": "入力された値が正しくありません。<br>0〜1584の数値を入力してください。", "SSE.Views.ShapeSettings.textColor": "色での塗りつぶし", "SSE.Views.ShapeSettings.textDirection": "方向", "SSE.Views.ShapeSettings.textEditPoints": "頂点の編集", "SSE.Views.ShapeSettings.textEditShape": "図形の編集", "SSE.Views.ShapeSettings.textEmptyPattern": "パターンなし", "SSE.Views.ShapeSettings.textEyedropper": "スポイト", "SSE.Views.ShapeSettings.textFlip": "反転する", "SSE.Views.ShapeSettings.textFromFile": "ファイルから", "SSE.Views.ShapeSettings.textFromStorage": "ストレージから", "SSE.Views.ShapeSettings.textFromUrl": "URLから", "SSE.Views.ShapeSettings.textGradient": "グラデーションのポイント", "SSE.Views.ShapeSettings.textGradientFill": "塗りつぶし(グラデーション)", "SSE.Views.ShapeSettings.textHint270": "反時計回りに90度回転", "SSE.Views.ShapeSettings.textHint90": "時計回りに90度回転", "SSE.Views.ShapeSettings.textHintFlipH": "左右反転", "SSE.Views.ShapeSettings.textHintFlipV": "上下反転", "SSE.Views.ShapeSettings.textImageTexture": "画像またはテクスチャ", "SSE.Views.ShapeSettings.textLinear": "線形", "SSE.Views.ShapeSettings.textMoreColors": "その他の色", "SSE.Views.ShapeSettings.textNoFill": "塗りつぶしなし", "SSE.Views.ShapeSettings.textNoShadow": "影なし", "SSE.Views.ShapeSettings.textOriginalSize": "元のサイズ", "SSE.Views.ShapeSettings.textPatternFill": "パターン", "SSE.Views.ShapeSettings.textPosition": "位置", "SSE.Views.ShapeSettings.textRadial": "放射状", "SSE.Views.ShapeSettings.textRecentlyUsed": "最近使った項目", "SSE.Views.ShapeSettings.textRotate90": "90度回転", "SSE.Views.ShapeSettings.textRotation": "回転", "SSE.Views.ShapeSettings.textSelectImage": "画像の選択", "SSE.Views.ShapeSettings.textSelectTexture": "選択", "SSE.Views.ShapeSettings.textShadow": "影", "SSE.Views.ShapeSettings.textStretch": "ストレッチ", "SSE.Views.ShapeSettings.textStyle": "スタイル", "SSE.Views.ShapeSettings.textTexture": "テクスチャから", "SSE.Views.ShapeSettings.textTile": "タイル", "SSE.Views.ShapeSettings.tipAddGradientPoint": "グラデーションポイントを追加", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "グラデーションポイントを削除する", "SSE.Views.ShapeSettings.txtBrownPaper": "クラフト紙", "SSE.Views.ShapeSettings.txtCanvas": "キャンバス", "SSE.Views.ShapeSettings.txtCarton": "カートン", "SSE.Views.ShapeSettings.txtDarkFabric": "ダークファブリック", "SSE.Views.ShapeSettings.txtGrain": "粒子", "SSE.Views.ShapeSettings.txtGranite": "みかげ石", "SSE.Views.ShapeSettings.txtGreyPaper": "グレー紙", "SSE.Views.ShapeSettings.txtKnit": "ニット", "SSE.Views.ShapeSettings.txtLeather": "レザー", "SSE.Views.ShapeSettings.txtNoBorders": "線なし", "SSE.Views.ShapeSettings.txtPapyrus": "パピルス", "SSE.Views.ShapeSettings.txtWood": "木", "SSE.Views.ShapeSettingsAdvanced.strColumns": "列", "SSE.Views.ShapeSettingsAdvanced.strMargins": "テキストの埋め込み文字", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "セルで移動したりサイズを変更したりしない", "SSE.Views.ShapeSettingsAdvanced.textAlt": "代替テキスト", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "説明", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "視覚障害や認知障害のある人が、画像や図形、図表にどのような情報が含まれているかを理解しやすくするため、そのオブジェクトについて目視できる情報を文章で表現したものです。", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "タイトル", "SSE.Views.ShapeSettingsAdvanced.textAngle": "角", "SSE.Views.ShapeSettingsAdvanced.textArrows": "矢印", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "自動調整", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "始点のサイズ", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "始点のスタイル", "SSE.Views.ShapeSettingsAdvanced.textBevel": "面取り", "SSE.Views.ShapeSettingsAdvanced.textBottom": "下", "SSE.Views.ShapeSettingsAdvanced.textCapType": "線の先端", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "列数", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "終点のサイズ", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "終点のスタイル", "SSE.Views.ShapeSettingsAdvanced.textFlat": "フラット", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "反転", "SSE.Views.ShapeSettingsAdvanced.textHeight": "高さ", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "水平に", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "結合の種類", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "比例の定数", "SSE.Views.ShapeSettingsAdvanced.textLeft": "左", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "線のスタイル", "SSE.Views.ShapeSettingsAdvanced.textMiter": "角", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "移動するが、セルでサイズを変更しない", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "テキストを図形からはみ出して表示する", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "テキストに合わせて図形を調整", "SSE.Views.ShapeSettingsAdvanced.textRight": "右に", "SSE.Views.ShapeSettingsAdvanced.textRotation": "回転", "SSE.Views.ShapeSettingsAdvanced.textRound": "ラウンド", "SSE.Views.ShapeSettingsAdvanced.textSize": "サイズ", "SSE.Views.ShapeSettingsAdvanced.textSnap": "セルに合わせる", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "列の間隔", "SSE.Views.ShapeSettingsAdvanced.textSquare": "四角の", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "テキストボックス", "SSE.Views.ShapeSettingsAdvanced.textTitle": "図形 - 詳細設定", "SSE.Views.ShapeSettingsAdvanced.textTop": "トップ", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "セルで移動してサイズを変更する", "SSE.Views.ShapeSettingsAdvanced.textVertically": "縦に", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "太さ&矢印", "SSE.Views.ShapeSettingsAdvanced.textWidth": "幅", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "警告", "SSE.Views.SignatureSettings.strDelete": "署名の削除", "SSE.Views.SignatureSettings.strDetails": "サインの詳細", "SSE.Views.SignatureSettings.strInvalid": "無効な署名", "SSE.Views.SignatureSettings.strRequested": "要求された署名", "SSE.Views.SignatureSettings.strSetup": "サインの設定", "SSE.Views.SignatureSettings.strSign": "サインする", "SSE.Views.SignatureSettings.strSignature": "署名", "SSE.Views.SignatureSettings.strSigner": "署名者", "SSE.Views.SignatureSettings.strValid": "有効な署名", "SSE.Views.SignatureSettings.txtContinueEditing": "無視して編集する", "SSE.Views.SignatureSettings.txtEditWarning": "編集すると、スプレッドシートから署名が削除されます。<br>このまま続けますか？", "SSE.Views.SignatureSettings.txtRemoveWarning": "この署名を削除しますか？<br>この操作は元に戻せません。", "SSE.Views.SignatureSettings.txtRequestedSignatures": "このスプレッドシートはサインする必要があります。", "SSE.Views.SignatureSettings.txtSigned": "有効な署名がスプレッドシートに追加されました。 スプレッドシートは編集から保護されています。", "SSE.Views.SignatureSettings.txtSignedInvalid": "スプレッドシートの一部のデジタル署名が無効であるか、検証できませんでした。 スプレッドシートは編集から保護されています。", "SSE.Views.SlicerAddDialog.textColumns": "列", "SSE.Views.SlicerAddDialog.txtTitle": "スライサーを挿入", "SSE.Views.SlicerSettings.strHideNoData": "データがないのを非表示", "SSE.Views.SlicerSettings.strIndNoData": "データのないアイテムを視覚的に示す", "SSE.Views.SlicerSettings.strShowDel": "データソースから削除されたアイテムを表示する", "SSE.Views.SlicerSettings.strShowNoData": "最後にデータのないアイテムを表示する", "SSE.Views.SlicerSettings.strSorting": "並べ替えとフィルター", "SSE.Views.SlicerSettings.textAdvanced": "詳細設定の表示", "SSE.Views.SlicerSettings.textAsc": "昇順", "SSE.Views.SlicerSettings.textAZ": "昇順", "SSE.Views.SlicerSettings.textButtons": "ボタン", "SSE.Views.SlicerSettings.textColumns": "列", "SSE.Views.SlicerSettings.textDesc": "降順", "SSE.Views.SlicerSettings.textHeight": "高さ", "SSE.Views.SlicerSettings.textHor": "水平", "SSE.Views.SlicerSettings.textKeepRatio": "一定の割合", "SSE.Views.SlicerSettings.textLargeSmall": "最大から最小へ", "SSE.Views.SlicerSettings.textLock": "サイズ変更または移動を無効にする", "SSE.Views.SlicerSettings.textNewOld": "最も新しいものから最も古いものへ", "SSE.Views.SlicerSettings.textOldNew": "最も古いものから最も新しいものへ", "SSE.Views.SlicerSettings.textPosition": "位置", "SSE.Views.SlicerSettings.textSize": "サイズ", "SSE.Views.SlicerSettings.textSmallLarge": "最小から最大へ", "SSE.Views.SlicerSettings.textStyle": "スタイル", "SSE.Views.SlicerSettings.textVert": "縦に", "SSE.Views.SlicerSettings.textWidth": "幅", "SSE.Views.SlicerSettings.textZA": "降順", "SSE.Views.SlicerSettingsAdvanced.strButtons": "ボタン", "SSE.Views.SlicerSettingsAdvanced.strColumns": "列", "SSE.Views.SlicerSettingsAdvanced.strHeight": "高さ", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "データがないのを非表示", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "データのないアイテムを視覚的に示す", "SSE.Views.SlicerSettingsAdvanced.strReferences": "参考資料", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "データソースから削除されたアイテムを表示する", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "ヘッダーを表示する", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "最後にデータのないアイテムを表示する", "SSE.Views.SlicerSettingsAdvanced.strSize": "サイズ", "SSE.Views.SlicerSettingsAdvanced.strSorting": "並べ替えとフィルター", "SSE.Views.SlicerSettingsAdvanced.strStyle": "スタイル", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "スタイルとサイズ", "SSE.Views.SlicerSettingsAdvanced.strWidth": "幅", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "セルで移動したりサイズを変更したりしない", "SSE.Views.SlicerSettingsAdvanced.textAlt": "代替テキスト", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "説明", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "視覚障害や認知障害のある人が、画像や図形、図表にどのような情報が含まれているかを理解しやすくするため、そのオブジェクトについて目視できる情報を文章で表現したものです。", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "タイトル", "SSE.Views.SlicerSettingsAdvanced.textAsc": "昇順", "SSE.Views.SlicerSettingsAdvanced.textAZ": "昇順", "SSE.Views.SlicerSettingsAdvanced.textDesc": "降順", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "数式で使用する名前", "SSE.Views.SlicerSettingsAdvanced.textHeader": "ヘッダー", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "一定の割合", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "最大から最小へ", "SSE.Views.SlicerSettingsAdvanced.textName": "名前", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "最も新しいものから最も古いものへ", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "最も古いものから最も新しいものへ", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "移動するが、セルでサイズを変更しない", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "最小から最大へ", "SSE.Views.SlicerSettingsAdvanced.textSnap": "セルに合わせる", "SSE.Views.SlicerSettingsAdvanced.textSort": "並べ替え", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "ソース名", "SSE.Views.SlicerSettingsAdvanced.textTitle": "スライサーの高度な設定", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "セルで移動してサイズを変更する", "SSE.Views.SlicerSettingsAdvanced.textZA": "降順", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "この項目は必須です", "SSE.Views.SortDialog.errorEmpty": "すべての並べ替えの基準には、列または行を指定する必要があります。", "SSE.Views.SortDialog.errorMoreOneCol": "複数の列が選択されています。", "SSE.Views.SortDialog.errorMoreOneRow": "複数の行が選択されています。", "SSE.Views.SortDialog.errorNotOriginalCol": "選択した列が元の選択範囲にありません。", "SSE.Views.SortDialog.errorNotOriginalRow": "選択した行が元の選択範囲にありません。", "SSE.Views.SortDialog.errorSameColumnColor": "%1は同じ色で複数回並べ替えられています。<br>重複する並べ替えの基準を削除して、再びお試しください。", "SSE.Views.SortDialog.errorSameColumnValue": "%1は値で複数回並べ替えられています。<br>重複する並べ替えの基準を削除して、再びお試しください。", "SSE.Views.SortDialog.textAsc": "昇順", "SSE.Views.SortDialog.textAuto": "自動", "SSE.Views.SortDialog.textAZ": "昇順", "SSE.Views.SortDialog.textBelow": "下", "SSE.Views.SortDialog.textBtnCopy": "コピー", "SSE.Views.SortDialog.textBtnDelete": "削除", "SSE.Views.SortDialog.textBtnNew": "新しい", "SSE.Views.SortDialog.textCellColor": "セルの背景色", "SSE.Views.SortDialog.textColumn": "列", "SSE.Views.SortDialog.textDesc": "降順", "SSE.Views.SortDialog.textDown": "レベルを下げる", "SSE.Views.SortDialog.textFontColor": "フォントの色", "SSE.Views.SortDialog.textLeft": "左", "SSE.Views.SortDialog.textLevels": "レベル", "SSE.Views.SortDialog.textMoreCols": "（他の行．．．）", "SSE.Views.SortDialog.textMoreRows": "（他の列．．．）", "SSE.Views.SortDialog.textNone": "なし", "SSE.Views.SortDialog.textOptions": "設定", "SSE.Views.SortDialog.textOrder": "順", "SSE.Views.SortDialog.textRight": "右に", "SSE.Views.SortDialog.textRow": "行", "SSE.Views.SortDialog.textSort": "並べ替え", "SSE.Views.SortDialog.textSortBy": "並べ替え", "SSE.Views.SortDialog.textThenBy": "次に優先されるキー", "SSE.Views.SortDialog.textTop": "上", "SSE.Views.SortDialog.textUp": "レベルを上げる", "SSE.Views.SortDialog.textValues": "値", "SSE.Views.SortDialog.textZA": "降順", "SSE.Views.SortDialog.txtInvalidRange": "無効なセル範囲", "SSE.Views.SortDialog.txtTitle": "並べ替え", "SSE.Views.SortFilterDialog.textAsc": "次の値で降順", "SSE.Views.SortFilterDialog.textDesc": "次の値で降順", "SSE.Views.SortFilterDialog.textNoSort": "並べ替えなし", "SSE.Views.SortFilterDialog.txtTitle": "並べ替え", "SSE.Views.SortFilterDialog.txtTitleValue": "値で並べ替え", "SSE.Views.SortOptionsDialog.textCase": "大文字と小文字の区別する", "SSE.Views.SortOptionsDialog.textHeaders": "先頭行をデータの見出しとして使用する", "SSE.Views.SortOptionsDialog.textLeftRight": "左から右に並べ替え", "SSE.Views.SortOptionsDialog.textOrientation": "印刷方向", "SSE.Views.SortOptionsDialog.textTitle": "並べ替えの設定", "SSE.Views.SortOptionsDialog.textTopBottom": "上から下に並べ替え", "SSE.Views.SpecialPasteDialog.textAdd": "追加", "SSE.Views.SpecialPasteDialog.textAll": "すべて", "SSE.Views.SpecialPasteDialog.textBlanks": "空白セルを無視する", "SSE.Views.SpecialPasteDialog.textColWidth": "列幅", "SSE.Views.SpecialPasteDialog.textComments": "コメント", "SSE.Views.SpecialPasteDialog.textDiv": "除法", "SSE.Views.SpecialPasteDialog.textFFormat": "数式と書式", "SSE.Views.SpecialPasteDialog.textFNFormat": "数式と数値書式", "SSE.Views.SpecialPasteDialog.textFormats": "書式", "SSE.Views.SpecialPasteDialog.textFormulas": "数式", "SSE.Views.SpecialPasteDialog.textFWidth": "数式と列幅", "SSE.Views.SpecialPasteDialog.textMult": "乗算", "SSE.Views.SpecialPasteDialog.textNone": "なし", "SSE.Views.SpecialPasteDialog.textOperation": "演算", "SSE.Views.SpecialPasteDialog.textPaste": "貼り付け", "SSE.Views.SpecialPasteDialog.textSub": "減算", "SSE.Views.SpecialPasteDialog.textTitle": "特殊貼付け", "SSE.Views.SpecialPasteDialog.textTranspose": "入れ替える", "SSE.Views.SpecialPasteDialog.textValues": "値", "SSE.Views.SpecialPasteDialog.textVFormat": "値と書式", "SSE.Views.SpecialPasteDialog.textVNFormat": "値と数値の書式", "SSE.Views.SpecialPasteDialog.textWBorders": "罫線を除くすべて", "SSE.Views.Spellcheck.noSuggestions": "修正候補なし", "SSE.Views.Spellcheck.textChange": "変更", "SSE.Views.Spellcheck.textChangeAll": "すべて修正", "SSE.Views.Spellcheck.textIgnore": "無視", "SSE.Views.Spellcheck.textIgnoreAll": "全てを無視する", "SSE.Views.Spellcheck.txtAddToDictionary": "辞書に追加", "SSE.Views.Spellcheck.txtClosePanel": "スペルを閉じる", "SSE.Views.Spellcheck.txtComplete": "スペルチェックが完了しました", "SSE.Views.Spellcheck.txtDictionaryLanguage": "辞書言語", "SSE.Views.Spellcheck.txtNextTip": "次の言葉へ", "SSE.Views.Spellcheck.txtSpelling": "スペル", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(末尾へ移動)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "コピーを作成する", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(新しいスプレッドシートを作成)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "シートの前へ移動", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "スプレッドシート", "SSE.Views.Statusbar.filteredRecordsText": "{0}の{1}がフィルタリングされた", "SSE.Views.Statusbar.filteredText": "フィルタモード", "SSE.Views.Statusbar.itemAverage": "平均", "SSE.Views.Statusbar.itemCount": "データの個数", "SSE.Views.Statusbar.itemDelete": "削除", "SSE.Views.Statusbar.itemHidden": "非表示", "SSE.Views.Statusbar.itemHide": "表示しない", "SSE.Views.Statusbar.itemInsert": "挿入", "SSE.Views.Statusbar.itemMaximum": "最大", "SSE.Views.Statusbar.itemMinimum": "最小", "SSE.Views.Statusbar.itemMoveOrCopy": "移動またはコピーする", "SSE.Views.Statusbar.itemProtect": "保護する", "SSE.Views.Statusbar.itemRename": "名前を変更する", "SSE.Views.Statusbar.itemStatus": "保存の状況​​", "SSE.Views.Statusbar.itemSum": "合計", "SSE.Views.Statusbar.itemTabColor": "シート見出しの色", "SSE.Views.Statusbar.itemUnProtect": "保護を解除する", "SSE.Views.Statusbar.RenameDialog.errNameExists": "指定された名前のワークシートが既に存在します。", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "シート名に次の文字を含むことはできません：\\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "シートの名前", "SSE.Views.Statusbar.selectAllSheets": "すべてのシートを選択する", "SSE.Views.Statusbar.sheetIndexText": "シート{0}/{1}", "SSE.Views.Statusbar.textAverage": "平均", "SSE.Views.Statusbar.textCount": "データの個数", "SSE.Views.Statusbar.textMax": "最大", "SSE.Views.Statusbar.textMin": "最小", "SSE.Views.Statusbar.textNewColor": "その他の色", "SSE.Views.Statusbar.textNoColor": "色なし", "SSE.Views.Statusbar.textSum": "合計", "SSE.Views.Statusbar.tipAddTab": "ワークシートを追加", "SSE.Views.Statusbar.tipFirst": "最初のリストまでスクロール", "SSE.Views.Statusbar.tipLast": "最後のリストまでスクロール", "SSE.Views.Statusbar.tipListOfSheets": "シートリスト", "SSE.Views.Statusbar.tipNext": "シートリストの右にスクロール", "SSE.Views.Statusbar.tipPrev": "シートリストの左にスクロール", "SSE.Views.Statusbar.tipZoomFactor": "ズーム", "SSE.Views.Statusbar.tipZoomIn": "拡大", "SSE.Views.Statusbar.tipZoomOut": "縮小", "SSE.Views.Statusbar.ungroupSheets": "シートをグループ解除する", "SSE.Views.Statusbar.zoomText": "ズーム{0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "選んだ範囲にこの操作を適用できません。<br>範囲内の1つのセルを選んでから、もう一度お試しください。", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "選択したセル範囲で操作を完了できませんでした。<br>最初のテーブルの行は同じ行にあったように、範囲を選択してください。<br>新しいテーブル範囲が元のテーブル範囲に重なるようにしてください。", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "選択したセル範囲で操作を完了できませんでした。<br>他のテーブルが含まれていない範囲を選択してください。", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "複数セルの配列数式はテーブルでは使用できません。", "SSE.Views.TableOptionsDialog.txtEmpty": "このフィールドは必須項目です", "SSE.Views.TableOptionsDialog.txtFormat": "表を作成する", "SSE.Views.TableOptionsDialog.txtInvalidRange": "エラー！セルの範囲が正しくありません。", "SSE.Views.TableOptionsDialog.txtNote": "ヘッダーは同じ行に残しておく必要があり、結果のテーブル範囲は元のテーブル範囲と重ねる必要があります。", "SSE.Views.TableOptionsDialog.txtTitle": "タイトル", "SSE.Views.TableSettings.deleteColumnText": "列の削除", "SSE.Views.TableSettings.deleteRowText": "行を削除", "SSE.Views.TableSettings.deleteTableText": "表を削除", "SSE.Views.TableSettings.insertColumnLeftText": "左に列を挿入", "SSE.Views.TableSettings.insertColumnRightText": "右に列を挿入", "SSE.Views.TableSettings.insertRowAboveText": "上に行を挿入", "SSE.Views.TableSettings.insertRowBelowText": "下に行を挿入", "SSE.Views.TableSettings.notcriticalErrorTitle": "警告", "SSE.Views.TableSettings.selectColumnText": "列全体の選択", "SSE.Views.TableSettings.selectDataText": "列データの選択", "SSE.Views.TableSettings.selectRowText": "行の選択", "SSE.Views.TableSettings.selectTableText": "テーブルの選択", "SSE.Views.TableSettings.textActions": "テーブルアクション", "SSE.Views.TableSettings.textAdvanced": "詳細設定の表示", "SSE.Views.TableSettings.textBanded": "縞模様", "SSE.Views.TableSettings.textColumns": "列", "SSE.Views.TableSettings.textConvertRange": "範囲に変換する", "SSE.Views.TableSettings.textEdit": "行&列", "SSE.Views.TableSettings.textEmptyTemplate": "テンプレートなし", "SSE.Views.TableSettings.textExistName": "エラー！すでに同じ名前がある範囲も存在しています。", "SSE.Views.TableSettings.textFilter": "「フィルター」ボタン", "SSE.Views.TableSettings.textFirst": "最初の", "SSE.Views.TableSettings.textHeader": "ヘッダー", "SSE.Views.TableSettings.textInvalidName": "エラー！表の名前が正しくありません。", "SSE.Views.TableSettings.textIsLocked": "この要素が別のユーザーによって編集されています。", "SSE.Views.TableSettings.textLast": "最後の", "SSE.Views.TableSettings.textLongOperation": "長時間の操作", "SSE.Views.TableSettings.textPivot": "ピボットテーブルを挿入", "SSE.Views.TableSettings.textRemDuplicates": "重複データを削除", "SSE.Views.TableSettings.textReservedName": "使用しようとしている名前は、既にセルの数式で参照されています。他の名前を使用してください。", "SSE.Views.TableSettings.textResize": "テーブルのサイズ変更", "SSE.Views.TableSettings.textRows": "行", "SSE.Views.TableSettings.textSelectData": "データの選択", "SSE.Views.TableSettings.textSlicer": "スライサーを挿入", "SSE.Views.TableSettings.textTableName": "表の名前", "SSE.Views.TableSettings.textTemplate": "テンプレートから選択する", "SSE.Views.TableSettings.textTotal": "合計", "SSE.Views.TableSettings.txtGroupTable_Custom": "カスタム", "SSE.Views.TableSettings.txtGroupTable_Dark": "ダーク", "SSE.Views.TableSettings.txtGroupTable_Light": "ライト", "SSE.Views.TableSettings.txtGroupTable_Medium": "中", "SSE.Views.TableSettings.txtTable_TableStyleDark": "表のスタイル：暗", "SSE.Views.TableSettings.txtTable_TableStyleLight": "表のスタイル：明るい", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "表のスタイル：中", "SSE.Views.TableSettings.warnLongOperation": "実行しようとしている操作は、完了するまでにかなり時間がかかる可能性があります。<br>続行しますか？", "SSE.Views.TableSettingsAdvanced.textAlt": "代替テキスト", "SSE.Views.TableSettingsAdvanced.textAltDescription": "説明", "SSE.Views.TableSettingsAdvanced.textAltTip": "視覚障害や認知障害のある人が、画像や図形、図表にどのような情報が含まれているかを理解しやすくするため、そのオブジェクトについて目視できる情報を文章で表現したものです。", "SSE.Views.TableSettingsAdvanced.textAltTitle": "タイトル", "SSE.Views.TableSettingsAdvanced.textTitle": "テーブル - 詳細設定", "SSE.Views.TextArtSettings.strBackground": "背景色", "SSE.Views.TextArtSettings.strColor": "色", "SSE.Views.TextArtSettings.strFill": "塗りつぶし", "SSE.Views.TextArtSettings.strForeground": "前景色", "SSE.Views.TextArtSettings.strPattern": "パターン", "SSE.Views.TextArtSettings.strSize": "サイズ", "SSE.Views.TextArtSettings.strStroke": "線", "SSE.Views.TextArtSettings.strTransparency": "不透明度", "SSE.Views.TextArtSettings.strType": "タイプ", "SSE.Views.TextArtSettings.textAngle": "角", "SSE.Views.TextArtSettings.textBorderSizeErr": "入力された値が正しくありません。<br>0〜1584の数値を入力してください。", "SSE.Views.TextArtSettings.textColor": "色での塗りつぶし", "SSE.Views.TextArtSettings.textDirection": "方向", "SSE.Views.TextArtSettings.textEmptyPattern": "パターンなし", "SSE.Views.TextArtSettings.textFromFile": "ファイルから", "SSE.Views.TextArtSettings.textFromUrl": "URLから", "SSE.Views.TextArtSettings.textGradient": "グラデーションのポイント", "SSE.Views.TextArtSettings.textGradientFill": "塗りつぶし(グラデーション)", "SSE.Views.TextArtSettings.textImageTexture": "画像またはテクスチャ", "SSE.Views.TextArtSettings.textLinear": "線形", "SSE.Views.TextArtSettings.textNoFill": "塗りつぶしなし", "SSE.Views.TextArtSettings.textPatternFill": "パターン", "SSE.Views.TextArtSettings.textPosition": "位置", "SSE.Views.TextArtSettings.textRadial": "放射状", "SSE.Views.TextArtSettings.textSelectTexture": "選択", "SSE.Views.TextArtSettings.textStretch": "ストレッチ", "SSE.Views.TextArtSettings.textStyle": "スタイル", "SSE.Views.TextArtSettings.textTemplate": "テンプレート", "SSE.Views.TextArtSettings.textTexture": "テクスチャから", "SSE.Views.TextArtSettings.textTile": "タイル", "SSE.Views.TextArtSettings.textTransform": "変換", "SSE.Views.TextArtSettings.tipAddGradientPoint": "グラデーションポイントを追加", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "グラデーションポイントを削除する", "SSE.Views.TextArtSettings.txtBrownPaper": "クラフト紙", "SSE.Views.TextArtSettings.txtCanvas": "キャンバス", "SSE.Views.TextArtSettings.txtCarton": "カートン", "SSE.Views.TextArtSettings.txtDarkFabric": "ダークファブリック", "SSE.Views.TextArtSettings.txtGrain": "粒子", "SSE.Views.TextArtSettings.txtGranite": "みかげ石", "SSE.Views.TextArtSettings.txtGreyPaper": "グレー紙", "SSE.Views.TextArtSettings.txtKnit": "ニット", "SSE.Views.TextArtSettings.txtLeather": "レザー", "SSE.Views.TextArtSettings.txtNoBorders": "線なし", "SSE.Views.TextArtSettings.txtPapyrus": "パピルス", "SSE.Views.TextArtSettings.txtWood": "木", "SSE.Views.Toolbar.capBtnAddComment": "コメントを追加", "SSE.Views.Toolbar.capBtnColorSchemas": "色", "SSE.Views.Toolbar.capBtnComment": "コメント", "SSE.Views.Toolbar.capBtnInsHeader": "ヘッダー/フッター", "SSE.Views.Toolbar.capBtnInsSlicer": "スライサー", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "記号", "SSE.Views.Toolbar.capBtnMargins": "余白", "SSE.Views.Toolbar.capBtnPageBreak": "区切り", "SSE.Views.Toolbar.capBtnPageOrient": "印刷の向き", "SSE.Views.Toolbar.capBtnPageSize": "サイズ", "SSE.Views.Toolbar.capBtnPrintArea": "印刷範囲", "SSE.Views.Toolbar.capBtnPrintTitles": "タイトルを印刷する", "SSE.Views.Toolbar.capBtnScale": "拡大縮小印刷", "SSE.Views.Toolbar.capImgAlign": "配置", "SSE.Views.Toolbar.capImgBackward": "背面ヘ移動", "SSE.Views.Toolbar.capImgForward": "前面ヘ移動", "SSE.Views.Toolbar.capImgGroup": "グループ化", "SSE.Views.Toolbar.capInsertChart": "グラフ", "SSE.Views.Toolbar.capInsertChartRecommend": "おすすめのチャート", "SSE.Views.Toolbar.capInsertEquation": "方程式", "SSE.Views.Toolbar.capInsertHyperlink": "ハイパーリンク", "SSE.Views.Toolbar.capInsertImage": "画像", "SSE.Views.Toolbar.capInsertShape": "図形", "SSE.Views.Toolbar.capInsertSpark": "スパークライン", "SSE.Views.Toolbar.capInsertTable": "表", "SSE.Views.Toolbar.capInsertText": "テキストボックス", "SSE.Views.Toolbar.capInsertTextart": "テキストアート", "SSE.Views.Toolbar.capShapesMerge": "図形を結合", "SSE.Views.Toolbar.mniCapitalizeWords": "各単語を大文字にする", "SSE.Views.Toolbar.mniImageFromFile": "ファイルから画像", "SSE.Views.Toolbar.mniImageFromStorage": "ストレージから画像", "SSE.Views.Toolbar.mniImageFromUrl": "URLから画像", "SSE.Views.Toolbar.mniLowerCase": "小文字", "SSE.Views.Toolbar.mniSentenceCase": "センテンスケース", "SSE.Views.Toolbar.mniToggleCase": "大文字と小文字を入れ替える", "SSE.Views.Toolbar.mniUpperCase": "大文字", "SSE.Views.Toolbar.textAddPrintArea": "印刷範囲に追加", "SSE.Views.Toolbar.textAlignBottom": "下揃え", "SSE.Views.Toolbar.textAlignCenter": "中央揃え", "SSE.Views.Toolbar.textAlignJust": "両端揃え", "SSE.Views.Toolbar.textAlignLeft": "左揃え", "SSE.Views.Toolbar.textAlignMiddle": "中央揃え", "SSE.Views.Toolbar.textAlignRight": "右揃え", "SSE.Views.Toolbar.textAlignTop": "上揃え", "SSE.Views.Toolbar.textAllBorders": "すべての枠線", "SSE.Views.Toolbar.textAlpha": "ギリシャ小文字アルファ", "SSE.Views.Toolbar.textAuto": "自動", "SSE.Views.Toolbar.textAutoColor": "自動", "SSE.Views.Toolbar.textBetta": "ギリシャ小文字ベータ", "SSE.Views.Toolbar.textBlackHeart": "ブラック・ハート・スーツ", "SSE.Views.Toolbar.textBold": "太字", "SSE.Views.Toolbar.textBordersColor": "線の色", "SSE.Views.Toolbar.textBordersStyle": "線のスタイル", "SSE.Views.Toolbar.textBottom": "低：", "SSE.Views.Toolbar.textBottomBorders": "下の枠線", "SSE.Views.Toolbar.textBullet": "箇条書き", "SSE.Views.Toolbar.textCellAlign": "セルの整列の書式設定", "SSE.Views.Toolbar.textCenterBorders": "内側の垂直枠線", "SSE.Views.Toolbar.textClearPrintArea": "印刷範囲を解除", "SSE.Views.Toolbar.textClearRule": "ルールを消去", "SSE.Views.Toolbar.textClockwise": "右回りに​​回転", "SSE.Views.Toolbar.textColorScales": "色​​スケール", "SSE.Views.Toolbar.textCopyright": "著作権マーク", "SSE.Views.Toolbar.textCounterCw": "左回りに​​回転", "SSE.Views.Toolbar.textCustom": "ユーザー設定", "SSE.Views.Toolbar.textDataBars": "データ バー", "SSE.Views.Toolbar.textDegree": "度記号", "SSE.Views.Toolbar.textDelLeft": "左方向にシフト", "SSE.Views.Toolbar.textDelPageBreak": "改ページの削除", "SSE.Views.Toolbar.textDelta": "ギリシャ小文字デルタ", "SSE.Views.Toolbar.textDelUp": "上方向にシフト", "SSE.Views.Toolbar.textDiagDownBorder": "斜め（上から下）", "SSE.Views.Toolbar.textDiagUpBorder": "斜め（下から上）", "SSE.Views.Toolbar.textDivision": "除算記号", "SSE.Views.Toolbar.textDollar": "ドル記号", "SSE.Views.Toolbar.textDone": "完了", "SSE.Views.Toolbar.textDown": "下", "SSE.Views.Toolbar.textEditVA": "表示エリアを編集する", "SSE.Views.Toolbar.textEntireCol": "列全体", "SSE.Views.Toolbar.textEntireRow": "行全体", "SSE.Views.Toolbar.textEuro": "ユーロ記号", "SSE.Views.Toolbar.textFewPages": "ページ", "SSE.Views.Toolbar.textFillLeft": "左", "SSE.Views.Toolbar.textFillRight": "右", "SSE.Views.Toolbar.textFormatCellFill": "セルの塗りつぶしの書式設定", "SSE.Views.Toolbar.textGreaterEqual": "以上", "SSE.Views.Toolbar.textHeight": "高さ", "SSE.Views.Toolbar.textHideVA": "表示エリアを非表示にする", "SSE.Views.Toolbar.textHorizontal": "横書きテキスト", "SSE.Views.Toolbar.textInfinity": "無限", "SSE.Views.Toolbar.textInsDown": "下方向にシフト", "SSE.Views.Toolbar.textInsideBorders": "内枠線", "SSE.Views.Toolbar.textInsPageBreak": "改ページの挿入", "SSE.Views.Toolbar.textInsRight": "右方向にシフト", "SSE.Views.Toolbar.textItalic": "イタリック", "SSE.Views.Toolbar.textItems": "アイテム", "SSE.Views.Toolbar.textLandscape": "横向き", "SSE.Views.Toolbar.textLeft": "左：", "SSE.Views.Toolbar.textLeftBorders": "左の枠線", "SSE.Views.Toolbar.textLessEqual": "以下", "SSE.Views.Toolbar.textLetterPi": "ギリシャの小文字ピー", "SSE.Views.Toolbar.textManageRule": "ルールの管理", "SSE.Views.Toolbar.textManyPages": "ページ", "SSE.Views.Toolbar.textMarginsLast": "最後に適用した設定", "SSE.Views.Toolbar.textMarginsNarrow": "狭い", "SSE.Views.Toolbar.textMarginsNormal": "標準", "SSE.Views.Toolbar.textMarginsWide": "広い", "SSE.Views.Toolbar.textMiddleBorders": "内側の水平枠線", "SSE.Views.Toolbar.textMoreBorders": "さらに多くの枠線", "SSE.Views.Toolbar.textMoreFormats": "その他のフォーマット", "SSE.Views.Toolbar.textMorePages": "その他のページ", "SSE.Views.Toolbar.textMoreSymbols": "その他の記号", "SSE.Views.Toolbar.textNewColor": "その他の色", "SSE.Views.Toolbar.textNewRule": "新しいルール", "SSE.Views.Toolbar.textNoBorders": "枠線なし", "SSE.Views.Toolbar.textNotEqualTo": "同等ではない", "SSE.Views.Toolbar.textOneHalf": "普通分数の1/2", "SSE.Views.Toolbar.textOnePage": "ページ", "SSE.Views.Toolbar.textOneQuarter": "普通分数の1/4", "SSE.Views.Toolbar.textOutBorders": "外枠線", "SSE.Views.Toolbar.textPageMarginsCustom": "ユーザー設定の余白", "SSE.Views.Toolbar.textPlusMinus": "プラスマイナス記号", "SSE.Views.Toolbar.textPortrait": "縦向き", "SSE.Views.Toolbar.textPrint": "印刷", "SSE.Views.Toolbar.textPrintGridlines": "枠線の印刷", "SSE.Views.Toolbar.textPrintHeadings": "見出しの印刷", "SSE.Views.Toolbar.textPrintOptions": "印刷の設定", "SSE.Views.Toolbar.textRegistered": "登録商標マーク", "SSE.Views.Toolbar.textResetPageBreak": "すべての改ページをリセットする", "SSE.Views.Toolbar.textRight": "右：", "SSE.Views.Toolbar.textRightBorders": "右の枠線", "SSE.Views.Toolbar.textRotateDown": "右へ90度回転", "SSE.Views.Toolbar.textRotateUp": "左へ90度回転", "SSE.Views.Toolbar.textRtlSheet": "シート（右から左）", "SSE.Views.Toolbar.textScale": "規模", "SSE.Views.Toolbar.textScaleCustom": "ユーザー設定", "SSE.Views.Toolbar.textSection": "節記号", "SSE.Views.Toolbar.textSelection": "現在の選択項目から", "SSE.Views.Toolbar.textSeries": "系列", "SSE.Views.Toolbar.textSetPrintArea": "印刷範囲を設定する", "SSE.Views.Toolbar.textShapesCombine": "結合", "SSE.Views.Toolbar.textShapesFragment": "断片", "SSE.Views.Toolbar.textShapesIntersect": "交差", "SSE.Views.Toolbar.textShapesSubstract": "減算", "SSE.Views.Toolbar.textShapesUnion": "連合", "SSE.Views.Toolbar.textShowVA": "表示エリアを表示にする", "SSE.Views.Toolbar.textSmile": "白い笑顔", "SSE.Views.Toolbar.textSquareRoot": "平方根", "SSE.Views.Toolbar.textStrikeout": "取り消し線", "SSE.Views.Toolbar.textSubscript": "下付き", "SSE.Views.Toolbar.textSubSuperscript": "下付き/上付きの文字", "SSE.Views.Toolbar.textSuperscript": "上付き", "SSE.Views.Toolbar.textTabCollaboration": "共同編集", "SSE.Views.Toolbar.textTabData": "データ", "SSE.Views.Toolbar.textTabDraw": "描画", "SSE.Views.Toolbar.textTabFile": "ファイル", "SSE.Views.Toolbar.textTabFormula": "数式", "SSE.Views.Toolbar.textTabHome": "ホーム", "SSE.Views.Toolbar.textTabInsert": "挿入", "SSE.Views.Toolbar.textTabLayout": "レイアウト", "SSE.Views.Toolbar.textTabProtect": "保護", "SSE.Views.Toolbar.textTabView": "表示", "SSE.Views.Toolbar.textThisPivot": "このピボットから", "SSE.Views.Toolbar.textThisSheet": "このシートから", "SSE.Views.Toolbar.textThisTable": "この表から", "SSE.Views.Toolbar.textTilde": "チルダ", "SSE.Views.Toolbar.textTop": "トップ： ", "SSE.Views.Toolbar.textTopBorders": "上の枠線", "SSE.Views.Toolbar.textTradeMark": "商標マーク", "SSE.Views.Toolbar.textUnderline": "下線", "SSE.Views.Toolbar.textUp": "上", "SSE.Views.Toolbar.textVertical": "縦書きテキスト", "SSE.Views.Toolbar.textWidth": "幅", "SSE.Views.Toolbar.textYen": "円記号", "SSE.Views.Toolbar.textZoom": "ズーム", "SSE.Views.Toolbar.tipAlignBottom": "下揃え", "SSE.Views.Toolbar.tipAlignCenter": "中央揃え", "SSE.Views.Toolbar.tipAlignJust": "両端揃え", "SSE.Views.Toolbar.tipAlignLeft": "左揃え", "SSE.Views.Toolbar.tipAlignMiddle": "中央揃え", "SSE.Views.Toolbar.tipAlignRight": "右揃え", "SSE.Views.Toolbar.tipAlignTop": "上揃え", "SSE.Views.Toolbar.tipAutofilter": "並べ替えとフィルタ", "SSE.Views.Toolbar.tipBack": "戻る", "SSE.Views.Toolbar.tipBorders": "表の枠線", "SSE.Views.Toolbar.tipCellStyle": "セルのスタイル", "SSE.Views.Toolbar.tipChangeCase": "大文字小文字を変更", "SSE.Views.Toolbar.tipChangeChart": "グラフの種類を変更", "SSE.Views.Toolbar.tipClearStyle": "消去", "SSE.Views.Toolbar.tipColorSchemas": "配色の変更", "SSE.Views.Toolbar.tipCondFormat": "条件付き書式", "SSE.Views.Toolbar.tipCopy": "コピー", "SSE.Views.Toolbar.tipCopyStyle": "スタイルをコピーする", "SSE.Views.Toolbar.tipCut": "切り取り", "SSE.Views.Toolbar.tipDecDecimal": "小数点以下の表示桁数を減らす", "SSE.Views.Toolbar.tipDecFont": "フォントサイズの縮小", "SSE.Views.Toolbar.tipDeleteOpt": "セルを削除", "SSE.Views.Toolbar.tipDigStyleAccounting": "会計のスタイル", "SSE.Views.Toolbar.tipDigStyleComma": "カンマスタイル", "SSE.Views.Toolbar.tipDigStyleCurrency": "通貨スタイル", "SSE.Views.Toolbar.tipDigStylePercent": "パーセントのスタイル", "SSE.Views.Toolbar.tipEditChart": "グラフの編集", "SSE.Views.Toolbar.tipEditChartData": "データの選択", "SSE.Views.Toolbar.tipEditChartType": "グラフの種類を変更", "SSE.Views.Toolbar.tipEditHeader": "ヘッダーまたはフッターの編集", "SSE.Views.Toolbar.tipFontColor": "フォントの色", "SSE.Views.Toolbar.tipFontName": "フォント", "SSE.Views.Toolbar.tipFontSize": "フォントのサイズ", "SSE.Views.Toolbar.tipHAlighOle": "左右の整列", "SSE.Views.Toolbar.tipImgAlign": "オブジェクトを整列する", "SSE.Views.Toolbar.tipImgGroup": "オブジェクトをグループ化する", "SSE.Views.Toolbar.tipIncDecimal": "小数点以下の表示桁数を増やす", "SSE.Views.Toolbar.tipIncFont": "フォントサイズの拡大", "SSE.Views.Toolbar.tipInsertChart": "グラフを挿入", "SSE.Views.Toolbar.tipInsertChartRecommend": "推奨チャートを挿入", "SSE.Views.Toolbar.tipInsertChartSpark": "グラフを挿入", "SSE.Views.Toolbar.tipInsertEquation": "方程式を挿入", "SSE.Views.Toolbar.tipInsertHorizontalText": "横書きテキストボックスの挿入", "SSE.Views.Toolbar.tipInsertHyperlink": "ハイパーリンクを追加", "SSE.Views.Toolbar.tipInsertImage": "画像を挿入", "SSE.Views.Toolbar.tipInsertOpt": "セルを挿入", "SSE.Views.Toolbar.tipInsertShape": "図形を挿入", "SSE.Views.Toolbar.tipInsertSlicer": "スライサーを挿入", "SSE.Views.Toolbar.tipInsertSmartArt": "SmartArtの挿入", "SSE.Views.Toolbar.tipInsertSpark": "スパークラインを挿入する", "SSE.Views.Toolbar.tipInsertSymbol": "記号を挿入", "SSE.Views.Toolbar.tipInsertTable": "表の挿入", "SSE.Views.Toolbar.tipInsertText": "テキストボックスを挿入する", "SSE.Views.Toolbar.tipInsertTextart": "テキストアートの挿入", "SSE.Views.Toolbar.tipInsertVerticalText": "縦書きテキストボックスの挿入", "SSE.Views.Toolbar.tipMerge": "結合して、中央に配置する", "SSE.Views.Toolbar.tipNone": "なし", "SSE.Views.Toolbar.tipNumFormat": "数値の書式", "SSE.Views.Toolbar.tipPageBreak": "印刷物で次のページを開始する位置に改行を追加する", "SSE.Views.Toolbar.tipPageMargins": "余白", "SSE.Views.Toolbar.tipPageOrient": "印刷の向き", "SSE.Views.Toolbar.tipPageSize": "ページのサイズ", "SSE.Views.Toolbar.tipPaste": "貼り付け", "SSE.Views.Toolbar.tipPrColor": "塗りつぶしの色", "SSE.Views.Toolbar.tipPrint": "印刷", "SSE.Views.Toolbar.tipPrintArea": "印刷範囲", "SSE.Views.Toolbar.tipPrintQuick": "クイックプリント", "SSE.Views.Toolbar.tipPrintTitles": "タイトルを印刷する", "SSE.Views.Toolbar.tipRedo": "やり直す", "SSE.Views.Toolbar.tipReplace": "置き換え", "SSE.Views.Toolbar.tipRtlSheet": "最初の列が右側に来るようにシートの方向を切り替える", "SSE.Views.Toolbar.tipSave": "保存", "SSE.Views.Toolbar.tipSaveCoauth": "他のユーザが変更を見れるために変更を保存します。", "SSE.Views.Toolbar.tipScale": "拡大縮小印刷", "SSE.Views.Toolbar.tipSelectAll": "すべて選択", "SSE.Views.Toolbar.tipSendBackward": "背面ヘ移動", "SSE.Views.Toolbar.tipSendForward": "前面ヘ移動", "SSE.Views.Toolbar.tipShapesMerge": "図形を結合", "SSE.Views.Toolbar.tipSynchronize": "ドキュメントは他のユーザーによって変更されました。変更を保存するためにここでクリックし、アップデートを再ロードしてください。", "SSE.Views.Toolbar.tipTextFormatting": "その他のテキスト編集ツール", "SSE.Views.Toolbar.tipTextOrientation": "印刷の向き", "SSE.Views.Toolbar.tipUndo": "元に戻す", "SSE.Views.Toolbar.tipVAlighOle": "垂直揃え", "SSE.Views.Toolbar.tipVisibleArea": "表示エリア", "SSE.Views.Toolbar.tipWrap": "折り返して​​全体を表示する", "SSE.Views.Toolbar.txtAccounting": "会計", "SSE.Views.Toolbar.txtAdditional": "追加", "SSE.Views.Toolbar.txtAscending": "昇順", "SSE.Views.Toolbar.txtAutosumTip": "合計", "SSE.Views.Toolbar.txtCellStyle": "セルのスタイル", "SSE.Views.Toolbar.txtClearAll": "すべて", "SSE.Views.Toolbar.txtClearComments": "コメント", "SSE.Views.Toolbar.txtClearFilter": "フィルタをクリアする", "SSE.Views.Toolbar.txtClearFormat": "形式", "SSE.Views.Toolbar.txtClearFormula": "関数", "SSE.Views.Toolbar.txtClearHyper": "ハイパーリンク", "SSE.Views.Toolbar.txtClearText": "テキスト", "SSE.Views.Toolbar.txtCurrency": "通貨", "SSE.Views.Toolbar.txtCustom": "ユーザー設定", "SSE.Views.Toolbar.txtDate": "日付", "SSE.Views.Toolbar.txtDateLong": "長い日付形式", "SSE.Views.Toolbar.txtDateShort": "日付 (短い形式)", "SSE.Views.Toolbar.txtDateTime": "日付＆時刻", "SSE.Views.Toolbar.txtDescending": "降順", "SSE.Views.Toolbar.txtDollar": "$ ドル", "SSE.Views.Toolbar.txtEuro": "€ ユーロ", "SSE.Views.Toolbar.txtExp": "指数", "SSE.Views.Toolbar.txtFillNum": "塗りつぶし", "SSE.Views.Toolbar.txtFilter": "フィルター​​", "SSE.Views.Toolbar.txtFormula": "関数を挿入", "SSE.Views.Toolbar.txtFraction": "分数", "SSE.Views.Toolbar.txtFranc": "CHF スイス フラン", "SSE.Views.Toolbar.txtGeneral": "標準", "SSE.Views.Toolbar.txtInteger": "整数", "SSE.Views.Toolbar.txtManageRange": "名前の管理", "SSE.Views.Toolbar.txtMergeAcross": "横方向に​​結合", "SSE.Views.Toolbar.txtMergeCells": "セルの結合", "SSE.Views.Toolbar.txtMergeCenter": "結合して中央揃え", "SSE.Views.Toolbar.txtNamedRange": "名前付き一覧\t", "SSE.Views.Toolbar.txtNewRange": "名前の定義", "SSE.Views.Toolbar.txtNoBorders": "枠線なし", "SSE.Views.Toolbar.txtNumber": "数値", "SSE.Views.Toolbar.txtPasteRange": "名前の貼り付け", "SSE.Views.Toolbar.txtPercentage": "パーセンテージ", "SSE.Views.Toolbar.txtPound": "£ ポンド", "SSE.Views.Toolbar.txtRouble": "₽ ルーブル", "SSE.Views.Toolbar.txtScientific": "指数", "SSE.Views.Toolbar.txtSearch": "検索", "SSE.Views.Toolbar.txtSort": "並べ替え", "SSE.Views.Toolbar.txtSortAZ": "昇順並べ替え", "SSE.Views.Toolbar.txtSortZA": "降順並べ替え", "SSE.Views.Toolbar.txtSpecial": "特殊", "SSE.Views.Toolbar.txtTableTemplate": "表として書式設定", "SSE.Views.Toolbar.txtText": "テキスト", "SSE.Views.Toolbar.txtTime": "時刻", "SSE.Views.Toolbar.txtUnmerge": "セル結合の解除", "SSE.Views.Toolbar.txtYen": "¥ 円", "SSE.Views.Top10FilterDialog.textType": "表示", "SSE.Views.Top10FilterDialog.txtBottom": "最低", "SSE.Views.Top10FilterDialog.txtBy": "対象", "SSE.Views.Top10FilterDialog.txtItems": "アイテム", "SSE.Views.Top10FilterDialog.txtPercent": "パーセント", "SSE.Views.Top10FilterDialog.txtSum": "合計", "SSE.Views.Top10FilterDialog.txtTitle": "トップ 10 オートフィルタ", "SSE.Views.Top10FilterDialog.txtTop": "トップ", "SSE.Views.Top10FilterDialog.txtValueTitle": "トップ10フィルター", "SSE.Views.ValueFieldSettingsDialog.textNext": "（次）", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "番号の書式", "SSE.Views.ValueFieldSettingsDialog.textPrev": "（前）", "SSE.Views.ValueFieldSettingsDialog.textTitle": "値フィールド設定", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "平均", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "基本フィールド", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "基本アイテム\n\t", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%2 の %1", "SSE.Views.ValueFieldSettingsDialog.txtCount": "データの個数", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "数値の個数", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "ユーザー設定の名前", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "基準値との差分", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "インデックス", "SSE.Views.ValueFieldSettingsDialog.txtMax": "最大", "SSE.Views.ValueFieldSettingsDialog.txtMin": "最小", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "計算なし", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "基準値に対する比率", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "基準値に対する比率の差", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "列のパーセント", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "%合計", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "親集計に対する比率", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "親列集計に対する比率", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "親行集計に対する比率", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "累計", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "行のパーセント", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "乗積", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "昇順での順位", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "降順での順位", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "累計", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "計算の種類を表示", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "ソース名：", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "標準偏差", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "標準偏差", "SSE.Views.ValueFieldSettingsDialog.txtSum": "合計", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "値フィールドを次のように要約する：", "SSE.Views.ValueFieldSettingsDialog.txtVar": "標本分散", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "分散", "SSE.Views.ViewManagerDlg.closeButtonText": "閉じる", "SSE.Views.ViewManagerDlg.guestText": "ゲスト", "SSE.Views.ViewManagerDlg.lockText": "ロックされた", "SSE.Views.ViewManagerDlg.textDelete": "削除する", "SSE.Views.ViewManagerDlg.textDuplicate": "複製する", "SSE.Views.ViewManagerDlg.textEmpty": "表示はまだ作成されていません。", "SSE.Views.ViewManagerDlg.textGoTo": "表示に移動する", "SSE.Views.ViewManagerDlg.textLongName": "128文字未満の名前を入力してください。", "SSE.Views.ViewManagerDlg.textNew": "新しい", "SSE.Views.ViewManagerDlg.textRename": "名前を変更する", "SSE.Views.ViewManagerDlg.textRenameError": "表示名は空であってはなりません。", "SSE.Views.ViewManagerDlg.textRenameLabel": "ビューの名前を変更する", "SSE.Views.ViewManagerDlg.textViews": "シート表示", "SSE.Views.ViewManagerDlg.tipIsLocked": "この要素が別のユーザーによって編集されています。", "SSE.Views.ViewManagerDlg.txtTitle": "シート表示マネージャー", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "このシートビューを削除してもよろしいですか？", "SSE.Views.ViewManagerDlg.warnDeleteView": "現在有効になっている表示 '％1'を削除しようとしています。<br>この表示を閉じて削除しますか？", "SSE.Views.ViewTab.capBtnFreeze": "ウィンドウ枠の固定", "SSE.Views.ViewTab.capBtnSheetView": "シートの表示", "SSE.Views.ViewTab.textAlwaysShowToolbar": "ツールバーを常に表示する", "SSE.Views.ViewTab.textClose": "閉じる", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "ステータスバーとシートを結合する", "SSE.Views.ViewTab.textCreate": "新しい", "SSE.Views.ViewTab.textDefault": "デフォルト", "SSE.Views.ViewTab.textFill": "塗りつぶし", "SSE.Views.ViewTab.textFormula": "数式バー", "SSE.Views.ViewTab.textFreezeCol": "先頭列を固定する", "SSE.Views.ViewTab.textFreezeRow": "先頭行を固定する", "SSE.Views.ViewTab.textGridlines": "枠線表示", "SSE.Views.ViewTab.textHeadings": "見出し", "SSE.Views.ViewTab.textInterfaceTheme": "インターフェイスのテーマ", "SSE.Views.ViewTab.textLeftMenu": "左パネル", "SSE.Views.ViewTab.textLine": "線", "SSE.Views.ViewTab.textMacros": "マ<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "表示マネージャー", "SSE.Views.ViewTab.textRightMenu": "右パネル", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "固定されたウィンドウ枠の影を表示する", "SSE.Views.ViewTab.textTabStyle": "タブのスタイル", "SSE.Views.ViewTab.textUnFreeze": "ウインドウ枠固定の解除", "SSE.Views.ViewTab.textZeros": "０を表示する", "SSE.Views.ViewTab.textZoom": "ズーム", "SSE.Views.ViewTab.tipClose": "シートの表示を閉じる", "SSE.Views.ViewTab.tipCreate": "シート表示を作成する", "SSE.Views.ViewTab.tipFreeze": "ウィンドウ枠の固定", "SSE.Views.ViewTab.tipInterfaceTheme": "インターフェースのテーマ", "SSE.Views.ViewTab.tipMacros": "マ<PERSON><PERSON>", "SSE.Views.ViewTab.tipSheetView": "シートの表示", "SSE.Views.ViewTab.tipViewNormal": "通常表示で原稿を見る", "SSE.Views.ViewTab.tipViewPageBreak": "原稿を印刷したときに、改ページがどこに表示されるかを確認する", "SSE.Views.ViewTab.txtViewNormal": "標準", "SSE.Views.ViewTab.txtViewPageBreak": "改ページ プレビュー", "SSE.Views.WatchDialog.closeButtonText": "閉じる", "SSE.Views.WatchDialog.textAdd": "ウォッチ式の追加", "SSE.Views.WatchDialog.textBook": "ブック", "SSE.Views.WatchDialog.textCell": "セル", "SSE.Views.WatchDialog.textDelete": "ウォッチ式の削除", "SSE.Views.WatchDialog.textDeleteAll": "全部削除する", "SSE.Views.WatchDialog.textFormula": "数式", "SSE.Views.WatchDialog.textName": "名前", "SSE.Views.WatchDialog.textSheet": "シート", "SSE.Views.WatchDialog.textValue": "値", "SSE.Views.WatchDialog.txtTitle": "ウォッチ ウィンドウ", "SSE.Views.WBProtection.hintAllowRanges": "範囲の編集を許可する", "SSE.Views.WBProtection.hintProtectRange": "範囲を保護する", "SSE.Views.WBProtection.hintProtectSheet": "シートを保護する", "SSE.Views.WBProtection.hintProtectWB": "ブックを保護する", "SSE.Views.WBProtection.txtAllowRanges": "範囲の編集を許可する", "SSE.Views.WBProtection.txtHiddenFormula": "非表示の数式", "SSE.Views.WBProtection.txtLockedCell": "ロックされたセル", "SSE.Views.WBProtection.txtLockedShape": "図形をロック", "SSE.Views.WBProtection.txtLockedText": "テキストをロックする", "SSE.Views.WBProtection.txtProtectRange": "範囲を保護する", "SSE.Views.WBProtection.txtProtectSheet": "シートを保護する", "SSE.Views.WBProtection.txtProtectWB": "ブックを保護する", "SSE.Views.WBProtection.txtSheetUnlockDescription": "シートを保護解除するようにパスワードを入力してください", "SSE.Views.WBProtection.txtSheetUnlockTitle": "シートを保護を解除する", "SSE.Views.WBProtection.txtWBUnlockDescription": "ブックを保護解除するようにパスワードを入力してください", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}