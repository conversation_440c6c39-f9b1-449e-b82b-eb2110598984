{"cancelButtonText": "Storno", "Common.Controllers.Chat.notcriticalErrorTitle": "Varování", "Common.Controllers.Desktop.hintBtnHome": "Zobrazit hlavní okno", "Common.Controllers.Desktop.itemCreateFromTemplate": "Vytvořit ze šablony", "Common.Controllers.History.notcriticalErrorTitle": "Varování", "Common.Controllers.History.txtErrorLoadHistory": "Načítání historie se nezdařilo", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Zásuvné moduly úspěšně nainstalovány. Ke všem zásuvným modulům na pozadí můžete přistupovat zde.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> úspěšně nainstalován. Ke všem zásuvným modulům na pozadí můžete přistupovat zde.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Spuštění nainstalovaných zásuvných modulů", "Common.Controllers.Plugins.textRunPlugin": "Spuštění zásuvného modulu", "Common.define.chartData.textArea": "Plošný graf", "Common.define.chartData.textAreaStacked": "Skládaný plošný", "Common.define.chartData.textAreaStackedPer": "100% skládaný plošný", "Common.define.chartData.textBar": "Pruhov<PERSON> graf", "Common.define.chartData.textBarNormal": "Skupinový sloupcový", "Common.define.chartData.textBarNormal3d": "3D skupinový sloupcový", "Common.define.chartData.textBarNormal3dPerspective": "3D sloupcový", "Common.define.chartData.textBarStacked": "Skl<PERSON>dan<PERSON> slou<PERSON>vý", "Common.define.chartData.textBarStacked3d": "3D skládaný sloupcový", "Common.define.chartData.textBarStackedPer": "100% sk<PERSON><PERSON><PERSON><PERSON> sloupcový", "Common.define.chartData.textBarStackedPer3d": "3D 100% skl<PERSON>daný sloupcový", "Common.define.chartData.textCharts": "<PERSON><PERSON>", "Common.define.chartData.textColumn": "Sloupcový graf", "Common.define.chartData.textColumnSpark": "Sloupec", "Common.define.chartData.textCombo": "<PERSON><PERSON><PERSON><PERSON>ý", "Common.define.chartData.textComboAreaBar": "Plošný - skupinový sloupcový", "Common.define.chartData.textComboBarLine": "Skupinový sloupcový - spojnice", "Common.define.chartData.textComboBarLineSecondary": "Skupinový sloupcový - spojnice na sekundární ose", "Common.define.chartData.textComboCustom": "<PERSON>last<PERSON><PERSON> k<PERSON>", "Common.define.chartData.textDoughnut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "Skupinový pruhový", "Common.define.chartData.textHBarNormal3d": "3D Skupinový pruhový", "Common.define.chartData.textHBarStacked": "Skl<PERSON>daný", "Common.define.chartData.textHBarStacked3d": "3D skládaný sloupcový", "Common.define.chartData.textHBarStackedPer": "100% sk<PERSON><PERSON><PERSON><PERSON> sloupcový", "Common.define.chartData.textHBarStackedPer3d": "3D 100% skl<PERSON>daný sloupcový", "Common.define.chartData.textLine": "<PERSON><PERSON><PERSON><PERSON> graf", "Common.define.chartData.textLine3d": "3D spojnicový", "Common.define.chartData.textLineMarker": "Spojnicový se značkami", "Common.define.chartData.textLineSpark": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textLineStacked": "Skládaný spojnicový", "Common.define.chartData.textLineStackedMarker": "Skládaný spojnicový se značkami", "Common.define.chartData.textLineStackedPer": "100% skl<PERSON>daný spojnicový", "Common.define.chartData.textLineStackedPerMarker": "100% skládaný spojnicový se značkami", "Common.define.chartData.textPie": "<PERSON><PERSON><PERSON><PERSON> diagram", "Common.define.chartData.textPie3d": "3D výsečový", "Common.define.chartData.textPoint": "Bodov<PERSON> graf", "Common.define.chartData.textRadar": "Paprskový", "Common.define.chartData.textRadarFilled": "Paprskový s výplní", "Common.define.chartData.textRadarMarker": "Paprskový se značkami", "Common.define.chartData.textScatter": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textScatterLine": "Bodový s rovnými spojnicemi", "Common.define.chartData.textScatterLineMarker": "Bodový s vyhlazenými spojnicemi a značkami", "Common.define.chartData.textScatterSmooth": "Bodový s vyhlazenými spojnicemi", "Common.define.chartData.textScatterSmoothMarker": "Bodový s vyhlazenými spojnicemi a značkami", "Common.define.chartData.textSparks": "Mikrografy", "Common.define.chartData.textStock": "Burzovní graf", "Common.define.chartData.textSurface": "Povrch", "Common.define.chartData.textWinLossSpark": "Zisk/<PERSON>tr<PERSON> ", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Nenastaven žádný <PERSON>", "Common.define.conditionalData.text1Above": "Na 1 standardní odchylka výše", "Common.define.conditionalData.text1Below": "Na 1 standardní odchylka níže", "Common.define.conditionalData.text2Above": "2 Směrodatná odchylka nad", "Common.define.conditionalData.text2Below": "2 Směrodatná odchylka pod", "Common.define.conditionalData.text3Above": "3 Směrodatná odchylka nad", "Common.define.conditionalData.text3Below": "3 Směrodatná odchylka pod", "Common.define.conditionalData.textAbove": "Nad", "Common.define.conditionalData.textAverage": "Průměrné", "Common.define.conditionalData.textBegins": "Začíná na", "Common.define.conditionalData.textBelow": "Pod", "Common.define.conditionalData.textBetween": "<PERSON><PERSON>", "Common.define.conditionalData.textBlank": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textBlanks": "Obsahuje prázdno", "Common.define.conditionalData.textBottom": "<PERSON><PERSON>", "Common.define.conditionalData.textContains": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textDataBar": "Datový pruh", "Common.define.conditionalData.textDate": "Datum", "Common.define.conditionalData.textDuplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textEnds": "Končí na", "Common.define.conditionalData.textEqAbove": "Větší nebo rovno", "Common.define.conditionalData.textEqBelow": "Menší nebo rovno", "Common.define.conditionalData.textEqual": "Rovná se", "Common.define.conditionalData.textError": "Chyba", "Common.define.conditionalData.textErrors": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textFormula": "Vzorec", "Common.define.conditionalData.textGreater": "větš<PERSON> než", "Common.define.conditionalData.textGreaterEq": "Větší nebo rovno", "Common.define.conditionalData.textIconSets": "<PERSON><PERSON> <PERSON>", "Common.define.conditionalData.textLast7days": "V uplynulých 7 dnech", "Common.define.conditionalData.textLastMonth": "Poslední <PERSON>", "Common.define.conditionalData.textLastWeek": "Poslední týden", "Common.define.conditionalData.textLess": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textLessEq": "Méně než nebo rovno", "Common.define.conditionalData.textNextMonth": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textNextWeek": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textNotBetween": "Ne mezi", "Common.define.conditionalData.textNotBlanks": "Neobsahuje prázdné", "Common.define.conditionalData.textNotContains": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textNotEqual": "Nerov<PERSON> se", "Common.define.conditionalData.textNotErrors": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textText": "Text", "Common.define.conditionalData.textThisMonth": "<PERSON><PERSON>", "Common.define.conditionalData.textThisWeek": "<PERSON><PERSON>", "Common.define.conditionalData.textToday": "Dnes", "Common.define.conditionalData.textTomorrow": "<PERSON>í<PERSON>", "Common.define.conditionalData.textTop": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textUnique": "Neopakující se", "Common.define.conditionalData.textValue": "Hodnota je", "Common.define.conditionalData.textYesterday": "Včera", "Common.define.smartArt.textAccentedPicture": "Zvýrazněný obrázek", "Common.define.smartArt.textAccentProcess": "Zvýraznění proces", "Common.define.smartArt.textAlternatingFlow": "Střídavý tok", "Common.define.smartArt.textAlternatingHexagons": "Alternující šestiúhelníky", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternující bloky obrázků", "Common.define.smartArt.textAlternatingPictureCircles": "Alternující kruhy o<PERSON>ů", "Common.define.smartArt.textArchitectureLayout": "Architektonické rozvržení", "Common.define.smartArt.textArrowRibbon": "<PERSON><PERSON><PERSON> ze š<PERSON>", "Common.define.smartArt.textAscendingPictureAccentProcess": "Vzestupné obrázkové zvýraznění - proces", "Common.define.smartArt.textBalance": "Zůstate<PERSON>", "Common.define.smartArt.textBasicBendingProcess": "Základní proces s ohybem", "Common.define.smartArt.textBasicBlockList": "Seznam - základní blok", "Common.define.smartArt.textBasicChevronProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON> - proces", "Common.define.smartArt.textBasicCycle": "Základ<PERSON><PERSON>", "Common.define.smartArt.textBasicMatrix": "Základní matice", "Common.define.smartArt.textBasicPie": "Základní v<PERSON>", "Common.define.smartArt.textBasicProcess": "Základní proces", "Common.define.smartArt.textBasicPyramid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicRadial": "Zák<PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicTarget": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicTimeline": "Zák<PERSON><PERSON><PERSON> osa", "Common.define.smartArt.textBasicVenn": "Základ<PERSON><PERSON>", "Common.define.smartArt.textBendingPictureAccentList": "Zalamovaný obrázkový zvýrazněný seznam ", "Common.define.smartArt.textBendingPictureBlocks": "Bloky obrázků s ohybem", "Common.define.smartArt.textBendingPictureCaption": "<PERSON><PERSON><PERSON> o<PERSON><PERSON><PERSON> pop<PERSON>y", "Common.define.smartArt.textBendingPictureCaptionList": "Seznam titulků k obrázkům s ohybem", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Ohnutý poloprůhledný text s obrázkem", "Common.define.smartArt.textBlockCycle": "Blokový koloběh", "Common.define.smartArt.textBubblePictureList": "Bublinový obrázkový seznam", "Common.define.smartArt.textCaptionedPictures": "Obrázky s titulky", "Common.define.smartArt.textChevronAccentProcess": "Proces se zdůrazně<PERSON><PERSON><PERSON>", "Common.define.smartArt.textChevronList": "Seznam - šipka", "Common.define.smartArt.textCircleAccentTimeline": "Časová osa s kruhovým zvýrazněním", "Common.define.smartArt.textCircleArrowProcess": "<PERSON><PERSON><PERSON><PERSON> - proces", "Common.define.smartArt.textCirclePictureHierarchy": "Kruhové obrázky - hierarchie ", "Common.define.smartArt.textCircleProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON> - proces", "Common.define.smartArt.textCircleRelationship": "Kruhový vztahový", "Common.define.smartArt.textCircularBendingProcess": "<PERSON><PERSON><PERSON><PERSON><PERSON> s ohybem - proces", "Common.define.smartArt.textCircularPictureCallout": "Bubliny s kruhovým obrázkem", "Common.define.smartArt.textClosedChevronProcess": "<PERSON><PERSON><PERSON><PERSON> - uzavřený proces", "Common.define.smartArt.textContinuousArrowProcess": "Nepřetrž<PERSON><PERSON> - proces", "Common.define.smartArt.textContinuousBlockProcess": "Nepřetržitý blok - proces", "Common.define.smartArt.textContinuousCycle": "Nepřetržitý koloběh", "Common.define.smartArt.textContinuousPictureList": "Nepřetržitý seznam obrázků", "Common.define.smartArt.textConvergingArrows": "Sbíhající se <PERSON>", "Common.define.smartArt.textConvergingRadial": "Sbíhající p<PERSON>ový", "Common.define.smartArt.textConvergingText": "Sbíhající se text", "Common.define.smartArt.textCounterbalanceArrows": "Vyvažova<PERSON><PERSON>", "Common.define.smartArt.textCycle": "Kruh", "Common.define.smartArt.textCycleMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textDescendingBlockList": "Seznam - sestupný blok", "Common.define.smartArt.textDescendingProcess": "Sestupn<PERSON> proces", "Common.define.smartArt.textDetailedProcess": "Detailní proces", "Common.define.smartArt.textDivergingArrows": "Rozbíhající se <PERSON>", "Common.define.smartArt.textDivergingRadial": "Rozbíhající p<PERSON>rskový", "Common.define.smartArt.textEquation": "Rovnice", "Common.define.smartArt.textFramedTextPicture": "Obrázek orámovaný textem", "Common.define.smartArt.textFunnel": "<PERSON><PERSON>lev<PERSON>", "Common.define.smartArt.textGear": "Př<PERSON>d", "Common.define.smartArt.textGridMatrix": "Mřížková matice", "Common.define.smartArt.textGroupedList": "Seskupený seznam", "Common.define.smartArt.textHalfCircleOrganizationChart": "Organizační diagram s půlkruhy", "Common.define.smartArt.textHexagonCluster": "<PERSON><PERSON><PERSON> š<PERSON>iúhelník<PERSON>", "Common.define.smartArt.textHexagonRadial": "Šestiúhelníkový paprskový", "Common.define.smartArt.textHierarchy": "Hierarchie", "Common.define.smartArt.textHierarchyList": "Seznam - hierarchie", "Common.define.smartArt.textHorizontalBulletList": "Vodorovný odrážkový seznam", "Common.define.smartArt.textHorizontalHierarchy": "Vodorovná hierarchie", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Vodorovná štítková hierarchie", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Vodorovná multiúrovňová hierarchie", "Common.define.smartArt.textHorizontalOrganizationChart": "Vodorovný organizační graf", "Common.define.smartArt.textHorizontalPictureList": "Vodorovný seznam obrázků", "Common.define.smartArt.textIncreasingArrowProcess": "Zvětšující se šipka - proces", "Common.define.smartArt.textIncreasingCircleProcess": "Zvětšující se kruh - proces", "Common.define.smartArt.textInterconnectedBlockProcess": "Seskupený blokový proces", "Common.define.smartArt.textInterconnectedRings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textInvertedPyramid": "Invert<PERSON><PERSON><PERSON> jehlan", "Common.define.smartArt.textLabeledHierarchy": "Hierarchie <PERSON>kov<PERSON>", "Common.define.smartArt.textLinearVenn": "Lineárn<PERSON>", "Common.define.smartArt.textLinedList": "Seznam s podtržením", "Common.define.smartArt.textList": "Seznam", "Common.define.smartArt.textMatrix": "Mat<PERSON>", "Common.define.smartArt.textMultidirectionalCycle": "Vícesměrný koloběh", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Organizační diagram s jménem a titulem", "Common.define.smartArt.textNestedTarget": "Vnořený terčový", "Common.define.smartArt.textNondirectionalCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON> beze směru", "Common.define.smartArt.textOpposingArrows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textOpposingIdeas": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textOrganizationChart": "Organizační graf", "Common.define.smartArt.textOther": "<PERSON><PERSON>", "Common.define.smartArt.textPhasedProcess": "<PERSON>oz<PERSON><PERSON><PERSON><PERSON><PERSON> proces", "Common.define.smartArt.textPicture": "Obrázek", "Common.define.smartArt.textPictureAccentBlocks": "Zvýrazněné bloky obrázků", "Common.define.smartArt.textPictureAccentList": "Obrázkový zvýrazněný seznam", "Common.define.smartArt.textPictureAccentProcess": "Zvýraznění obrázku - proces", "Common.define.smartArt.textPictureCaptionList": "Seznam titulků k obrázkům", "Common.define.smartArt.textPictureFrame": "Obrázek s orámovaním", "Common.define.smartArt.textPictureGrid": "Mřížka obrázků", "Common.define.smartArt.textPictureLineup": "Obrázkov<PERSON> sestava", "Common.define.smartArt.textPictureOrganizationChart": "Diagram organizace s obrázky", "Common.define.smartArt.textPictureStrips": "Obrázkov<PERSON> pro<PERSON>žek", "Common.define.smartArt.textPieProcess": "Výsečový proces", "Common.define.smartArt.textPlusAndMinus": "Plus a mínus", "Common.define.smartArt.textProcess": "Proces", "Common.define.smartArt.textProcessArrows": "<PERSON><PERSON><PERSON> procesu", "Common.define.smartArt.textProcessList": "Seznam - proces", "Common.define.smartArt.textPyramid": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textPyramidList": "Jehlanový seznam", "Common.define.smartArt.textRadialCluster": "Paprskový shluk", "Common.define.smartArt.textRadialCycle": "Paprskový kruhový", "Common.define.smartArt.textRadialList": "Paprskový seznam", "Common.define.smartArt.textRadialPictureList": "Paprskový obrázkový seznam", "Common.define.smartArt.textRadialVenn": "Paprskový Vennův", "Common.define.smartArt.textRandomToResultProcess": "Proces od náhody k výsledku", "Common.define.smartArt.textRelationship": "Vztah", "Common.define.smartArt.textRepeatingBendingProcess": "Opakovaný proces s ohybem", "Common.define.smartArt.textReverseList": "Reverzní se<PERSON>nam", "Common.define.smartArt.textSegmentedCycle": "Segmentový koloběh", "Common.define.smartArt.textSegmentedProcess": "Segmentový proces", "Common.define.smartArt.textSegmentedPyramid": "Segment<PERSON><PERSON> jehlan", "Common.define.smartArt.textSnapshotPictureList": "Seznam snímků", "Common.define.smartArt.textSpiralPicture": "Spirálovitý obrázek", "Common.define.smartArt.textSquareAccentList": "Čtvercový zvýrazněný seznam", "Common.define.smartArt.textStackedList": "Skládaný seznam", "Common.define.smartArt.textStackedVenn": "Skládaný Vennův", "Common.define.smartArt.textStaggeredProcess": "Střídavý proces", "Common.define.smartArt.textStepDownProcess": "Sestupn<PERSON> proces", "Common.define.smartArt.textStepUpProcess": "Vzestupný proces", "Common.define.smartArt.textSubStepProcess": "Proces s dílčími kroky", "Common.define.smartArt.textTabbedArc": "Odstavcový oblouk", "Common.define.smartArt.textTableHierarchy": "Hierarch<PERSON>", "Common.define.smartArt.textTableList": "Tabulkový seznam", "Common.define.smartArt.textTabList": "Odstavcový seznam", "Common.define.smartArt.textTargetList": "Terčový seznam", "Common.define.smartArt.textTextCycle": "<PERSON><PERSON><PERSON><PERSON><PERSON> textu", "Common.define.smartArt.textThemePictureAccent": "Obrázkové prostředí - zvýraznění", "Common.define.smartArt.textThemePictureAlternatingAccent": "Střídavé obrázkové prostředí - zvýraznění", "Common.define.smartArt.textThemePictureGrid": "Prostředí mřížka obrázků", "Common.define.smartArt.textTitledMatrix": "Matice s názvem", "Common.define.smartArt.textTitledPictureAccentList": "Obrázkový zvýrazněný seznam s nadpisem", "Common.define.smartArt.textTitledPictureBlocks": "Obrázkové bloky s nadpisem", "Common.define.smartArt.textTitlePictureLineup": "Obrázková sestava s názvy", "Common.define.smartArt.textTrapezoidList": "Lichoběžníkový seznam", "Common.define.smartArt.textUpwardArrow": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textVaryingWidthList": "Seznam s variabilní šířkou", "Common.define.smartArt.textVerticalAccentList": "Vertikální zvýrazněný seznam", "Common.define.smartArt.textVerticalArrowList": "Šipka - svislý seznam", "Common.define.smartArt.textVerticalBendingProcess": "Vzestupný proces s ohybem", "Common.define.smartArt.textVerticalBlockList": "Seznam - vertikální blok", "Common.define.smartArt.textVerticalBoxList": "Svislý rámeček - seznam", "Common.define.smartArt.textVerticalBracketList": "Vertikální odrážkový seznam", "Common.define.smartArt.textVerticalBulletList": "Vertikální odrážkový seznam", "Common.define.smartArt.textVerticalChevronList": "Svislý š<PERSON>kový seznam", "Common.define.smartArt.textVerticalCircleList": "Svislý kruhový seznam", "Common.define.smartArt.textVerticalCurvedList": "Svislý zahnutý seznam", "Common.define.smartArt.textVerticalEquation": "Svislá rovnice", "Common.define.smartArt.textVerticalPictureAccentList": "Svislý obrázkový zvýrazněný seznam", "Common.define.smartArt.textVerticalPictureList": "Svislý seznam obrázků", "Common.define.smartArt.textVerticalProcess": "<PERSON><PERSON><PERSON><PERSON> proces", "Common.Translation.textMoreButton": "V<PERSON>ce", "Common.Translation.tipFileLocked": "Dokument je uzamčen pro editaci. Můžete provádět změny a uložit je později, jako lo<PERSON>ální kopii.", "Common.Translation.tipFileReadOnly": "<PERSON><PERSON>or zob<PERSON>n pouze jako n<PERSON>. Chcete-li zach<PERSON>t <PERSON>, ulo<PERSON><PERSON> soubor s jiným názvem, nebo na jin<PERSON> m<PERSON>.", "Common.Translation.warnFileLocked": "Soubor je upravován v jiné aplikaci. Můžete pokračovat v úpravách a uložit ho jako kopii.", "Common.Translation.warnFileLockedBtnEdit": "Vytvořit kopii", "Common.Translation.warnFileLockedBtnView": "Otevřít pro náhled", "Common.UI.ButtonColored.textAutoColor": "<PERSON>ky", "Common.UI.ButtonColored.textEyedropper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.ButtonColored.textNewColor": "<PERSON><PERSON><PERSON>", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON>z oh<PERSON>í", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON>z oh<PERSON>í", "Common.UI.ComboDataView.emptyComboText": "<PERSON><PERSON><PERSON><PERSON> styly", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Stávající", "Common.UI.ExtendedColorDialog.textHexErr": "Zadaná hodnota není správná.<br>Zadejte hodnotu v rozmezí 000000 až FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Nová", "Common.UI.ExtendedColorDialog.textRGBErr": "Zadaná hodnota není správná.<br>Zadejte hodnotu v rozmezí 0 až 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON> barvy", "Common.UI.InputField.txtEmpty": "Toto pole je povinné", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "<PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON>", "Common.UI.InputFieldBtnPassword.textHintHold": "Stiskněte a podržte tlačítko, dokud se nezobrazí heslo.", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Zobrazit heslo", "Common.UI.SearchBar.textFind": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipNextResult": "Následující", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Otevřít pokročilé nastavení", "Common.UI.SearchBar.tipPreviousResult": "Předchozí", "Common.UI.SearchDialog.textHighlight": "Zvýraznit výsledky", "Common.UI.SearchDialog.textMatchCase": "Roz<PERSON>šovat malá a velká písmena", "Common.UI.SearchDialog.textReplaceDef": "<PERSON><PERSON><PERSON><PERSON> text, k<PERSON><PERSON>m nahradit", "Common.UI.SearchDialog.textSearchStart": "Zde vložte text", "Common.UI.SearchDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> a na<PERSON>", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "<PERSON>uze celá slova", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "Nahradit", "Common.UI.SearchDialog.txtBtnReplaceAll": "Nahradit vše", "Common.UI.SynchronizeTip.textDontShow": "<PERSON><PERSON> už nezobrazovat", "Common.UI.SynchronizeTip.textGotIt": "Rozumím", "Common.UI.SynchronizeTip.textSynchronize": "Dokument byl mezitím změněn jiným uživatelem.<br>Kliknutím uložte změny provedené vámi a načtěte ty od ostatních.", "Common.UI.ThemeColorPalette.textRecentColors": "Aktuální barvy", "Common.UI.ThemeColorPalette.textStandartColors": "Standardní barvy", "Common.UI.ThemeColorPalette.textThemeColors": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeClassicLight": "Standartní světlost", "Common.UI.Themes.txtThemeContrastDark": "Kontrastní tmavá", "Common.UI.Themes.txtThemeDark": "Tmavé", "Common.UI.Themes.txtThemeGray": "Šedá", "Common.UI.Themes.txtThemeLight": "Světl<PERSON>", "Common.UI.Themes.txtThemeSystem": "<PERSON><PERSON><PERSON><PERSON> jako <PERSON>", "Common.UI.Window.cancelButtonText": "Storno", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Ne", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Potvrzení", "Common.UI.Window.textDontShow": "<PERSON><PERSON> už nezobrazovat", "Common.UI.Window.textError": "Chyba", "Common.UI.Window.textInformation": "Informace", "Common.UI.Window.textWarning": "Varování", "Common.UI.Window.yesButtonText": "<PERSON><PERSON>", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Zvýraznění", "Common.Utils.ThemeColor.txtAqua": "Voda", "Common.Utils.ThemeColor.txtbackground": "Pozadí", "Common.Utils.ThemeColor.txtBlack": "Černá", "Common.Utils.ThemeColor.txtBlue": "Modrá", "Common.Utils.ThemeColor.txtBrightGreen": "Světlá zelená", "Common.Utils.ThemeColor.txtBrown": "Hnědá", "Common.Utils.ThemeColor.txtDarkBlue": "<PERSON><PERSON><PERSON><PERSON> mod<PERSON>", "Common.Utils.ThemeColor.txtDarker": "Tmavší", "Common.Utils.ThemeColor.txtDarkGray": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkGreen": "Tmavě <PERSON>", "Common.Utils.ThemeColor.txtDarkPurple": "<PERSON><PERSON><PERSON><PERSON>alo<PERSON>", "Common.Utils.ThemeColor.txtDarkRed": "T<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkTeal": "Tmavě tyrkysov<PERSON>", "Common.Utils.ThemeColor.txtDarkYellow": "Tmavě <PERSON>", "Common.Utils.ThemeColor.txtGold": "Zlatá", "Common.Utils.ThemeColor.txtGray": "Šedá", "Common.Utils.ThemeColor.txtGreen": "Zelená", "Common.Utils.ThemeColor.txtIndigo": "Indigová", "Common.Utils.ThemeColor.txtLavender": "Levandulová", "Common.Utils.ThemeColor.txtLightBlue": "<PERSON><PERSON>ětle modrá", "Common.Utils.ThemeColor.txtLighter": "Světlejší", "Common.Utils.ThemeColor.txtLightGray": "Světle šedá", "Common.Utils.ThemeColor.txtLightGreen": "Světle zelená", "Common.Utils.ThemeColor.txtLightOrange": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtLightYellow": "Světle žlutá", "Common.Utils.ThemeColor.txtOrange": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtPink": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtPurple": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtRed": "Červená", "Common.Utils.ThemeColor.txtRose": "Ma<PERSON><PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Nebeská modrá", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Text", "Common.Utils.ThemeColor.txtTurquosie": "Tyrkysová", "Common.Utils.ThemeColor.txtViolet": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtWhite": "Bílá", "Common.Utils.ThemeColor.txtYellow": "Žlutá", "Common.Views.About.txtAddress": "adresa:", "Common.Views.About.txtLicensee": "DRŽITEL LICENCE", "Common.Views.About.txtLicensor": "UDĚLOVATEL LICENCE", "Common.Views.About.txtMail": "e-mail:", "Common.Views.About.txtPoweredBy": "Poháněno", "Common.Views.About.txtTel": "tel.:", "Common.Views.About.txtVersion": "Verze", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Aplikovat během práce", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Autokorekce", "Common.Views.AutoCorrectDialog.textAutoFormat": "Automaticky formátovat během psaní", "Common.Views.AutoCorrectDialog.textBy": "Od", "Common.Views.AutoCorrectDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textHyperlink": "Internetové a síťové přístupy s hypertextovými odkazy", "Common.Views.AutoCorrectDialog.textMathCorrect": "Autokorekce pro matematiku", "Common.Views.AutoCorrectDialog.textNewRowCol": "Zahrnout nové řádky a sloupce v tabulce", "Common.Views.AutoCorrectDialog.textRecognized": "Rozpozná<PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Následující výrazy jsou rozpoznány jako matematic<PERSON>. Nebudou aplikována pravidla týkajících se velkých a malých písmen.", "Common.Views.AutoCorrectDialog.textReplace": "Nahradit", "Common.Views.AutoCorrectDialog.textReplaceText": "Nahraz<PERSON>t b<PERSON><PERSON> psaní", "Common.Views.AutoCorrectDialog.textReplaceType": "Nahrazovat text během psaní", "Common.Views.AutoCorrectDialog.textReset": "Obnovit", "Common.Views.AutoCorrectDialog.textResetAll": "Vrátit zpět do výchozích hodnot", "Common.Views.AutoCorrectDialog.textRestore": "Obnovit", "Common.Views.AutoCorrectDialog.textTitle": "Autokorekce", "Common.Views.AutoCorrectDialog.textWarnAddRec": "<PERSON> třeba, aby r<PERSON><PERSON><PERSON><PERSON><PERSON> obsahovaly pouze písmena A až Z (velká či malá).", "Common.Views.AutoCorrectDialog.textWarnResetRec": "<PERSON><PERSON><PERSON> z<PERSON> výrazy budou odstraněny a odstraněné budou obnoveny. Opravdu chcete pokračovat? ", "Common.Views.AutoCorrectDialog.warnReplace": "Hodnota pro automatické opravy %1 již existuje. Opravdu ji chcete nahradit?", "Common.Views.AutoCorrectDialog.warnReset": "Vámi přidané autokorekce budou odstraněny a změněné budou obnoveny do výchozích hodnot. Opravdu chcete pokračovat? ", "Common.Views.AutoCorrectDialog.warnRestore": "Hodnota pro automatické opravy %1 bude nastavena na původní hodnotu. Opravdu chcete pokračovat?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "<PERSON>av<PERSON><PERSON><PERSON> chat", "Common.Views.Chat.textEnterMessage": "Zde napište svou zprávu", "Common.Views.Chat.textSend": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.mniAuthorAsc": "Autor A až Z", "Common.Views.Comments.mniAuthorDesc": "Autor Z až A", "Common.Views.Comments.mniDateAsc": "Nejstarší", "Common.Views.Comments.mniDateDesc": "Nejnovější", "Common.Views.Comments.mniFilterGroups": "Filtrovat podle seskupení", "Common.Views.Comments.mniPositionAsc": "Shora", "Common.Views.Comments.mniPositionDesc": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "Přidat komentář", "Common.Views.Comments.textAddCommentToDoc": "Přidat komentář k dokumentu", "Common.Views.Comments.textAddReply": "Přidat odpověď", "Common.Views.Comments.textAll": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textAnonym": "Návštěvník", "Common.Views.Comments.textCancel": "Storno", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "Zavřít komentář<PERSON>", "Common.Views.Comments.textComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textComments": "Ko<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Zde napište svůj komentář", "Common.Views.Comments.textHintAddComment": "Přidat komentář", "Common.Views.Comments.textOpenAgain": "Znovu otevřít", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "Vyřešit", "Common.Views.Comments.textResolved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textSort": "Řadit koment<PERSON><PERSON>e", "Common.Views.Comments.textSortFilter": "Seřadit a filtrovat komentáře", "Common.Views.Comments.textSortFilterMore": "Seřazení, filtrování a více", "Common.Views.Comments.textSortMore": "Seřazení a více", "Common.Views.Comments.textViewResolved": "Nemáte oprávnění pro opětovné otevír<PERSON><PERSON> k<PERSON>", "Common.Views.Comments.txtEmpty": "Tento list neobs<PERSON><PERSON> k<PERSON>.", "Common.Views.CopyWarningDialog.textDontShow": "<PERSON><PERSON> už nezobrazovat", "Common.Views.CopyWarningDialog.textMsg": "<PERSON><PERSON><PERSON> k<PERSON>, vyjmout a vložit použitím lišty nástrojů editoru a kontextové nabídky budou prováděny pouze v tomto okně editoru.<br><br>Pro kopírování do nebo vkládání z aplikací mimo okno editoru použijte následující klávesové zkratky:", "Common.Views.CopyWarningDialog.textTitle": "<PERSON><PERSON><PERSON> k<PERSON>, vyjmout a vložit", "Common.Views.CopyWarningDialog.textToCopy": "pro zkopírování", "Common.Views.CopyWarningDialog.textToCut": "pro vyjmutí", "Common.Views.CopyWarningDialog.textToPaste": "pro vložení", "Common.Views.CustomizeQuickAccessDialog.textDownload": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Zkontrolovat příkazy, k<PERSON>é budou zobrazeny v Panelu rychlého přístupu", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Tisk", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Rychlý tisk", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Znovu", "Common.Views.CustomizeQuickAccessDialog.textSave": "Uložit", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Přizpůsobení rychlého přístupu", "Common.Views.CustomizeQuickAccessDialog.textUndo": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.DocumentAccessDialog.textLoading": "Načít<PERSON><PERSON>…", "Common.Views.DocumentAccessDialog.textTitle": "Nastavení sdílení", "Common.Views.DocumentPropertyDialog.errorDate": "Můžete vybrat hodnotu z kalendáře, takto vybraná hodnota bude uložena jako datum.<br>Pokud zadáte hodnotu manuálně, bude uložena jako text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "Ne", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "<PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Vlastnost by <PERSON><PERSON><PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Ano\" nebo \"Ne\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Datum", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "<PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "<PERSON><PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Zadejte plat<PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Vlastnost by <PERSON><PERSON><PERSON> o<PERSON><PERSON> hodnotu", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Hodnota", "Common.Views.DocumentPropertyDialog.txtTitle": "Nová vlastnost souboru", "Common.Views.Draw.hintEraser": "<PERSON><PERSON>", "Common.Views.Draw.hintSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Draw.txtEraser": "<PERSON><PERSON>", "Common.Views.Draw.txtHighlighter": "Zvýrazňovač", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "<PERSON><PERSON>", "Common.Views.Draw.txtSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Draw.txtSize": "Velikost", "Common.Views.EditNameDialog.textLabel": "Štítek:", "Common.Views.EditNameDialog.textLabelError": "Štítek je nutné vyplnit.", "Common.Views.Header.ariaQuickAccessToolbar": "Panel rychlého přístupu", "Common.Views.Header.labelCoUsersDescr": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON> soubor právě upravují:", "Common.Views.Header.textAddFavorite": "Označit jako ob<PERSON>", "Common.Views.Header.textAdvSettings": "Pokročilá nastavení", "Common.Views.Header.textBack": "Otev<PERSON><PERSON><PERSON>ě<PERSON>í so<PERSON>u", "Common.Views.Header.textClose": "<PERSON><PERSON><PERSON><PERSON><PERSON> so<PERSON>", "Common.Views.Header.textCompactView": "Skrýt panel nástrojů", "Common.Views.Header.textHideLines": "Skrýt pravítka", "Common.Views.Header.textHideStatusBar": "Zkombinovat listy a datové pruhy", "Common.Views.Header.textPrint": "Tisk", "Common.Views.Header.textReadOnly": "Pouze pro čtení", "Common.Views.Header.textRemoveFavorite": "Odebrat z oblíbených", "Common.Views.Header.textSaveBegin": "Ukládání...", "Common.Views.Header.textSaveChanged": "Změněno", "Common.Views.Header.textSaveEnd": "Všechny změny uloženy", "Common.Views.Header.textSaveExpander": "Všechny změny uloženy", "Common.Views.Header.textShare": "Sdílet", "Common.Views.Header.textZoom": "Přiblížení", "Common.Views.Header.tipAccessRights": "Spravovat přístupová práva k dokumentům", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Přizpůsobit panel rychlého přístupu", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipGoEdit": "Upravit stávající soubor", "Common.Views.Header.tipPrint": "Vytisknout soubor", "Common.Views.Header.tipPrintQuick": "Rychlý tisk", "Common.Views.Header.tipRedo": "Znovu", "Common.Views.Header.tipSave": "Uložit", "Common.Views.Header.tipSearch": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Oddělit do zvlášť okna", "Common.Views.Header.tipUsers": "Zobrazit uživatele", "Common.Views.Header.tipViewSettings": "Zobrazit nastavení", "Common.Views.Header.tipViewUsers": "Zobrazte uživatele a spravujte přístupová práva k dokumentům", "Common.Views.Header.txtAccessRights": "Změnit přístupová práva", "Common.Views.Header.txtRename": "Př<PERSON>menovat", "Common.Views.History.textCloseHistory": "Zavřít historii", "Common.Views.History.textHideAll": "Skrýt podrobné změny", "Common.Views.History.textHighlightDeleted": "Zvýraznění odstraněno", "Common.Views.History.textMore": "V<PERSON>ce", "Common.Views.History.textRestore": "Obnovit", "Common.Views.History.textShowAll": "Zobrazit změny podrobně", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Historie verzí", "Common.Views.ImageFromUrlDialog.textUrl": "Vložte URL adresu obrázku:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Toto pole je povinné", "Common.Views.ImageFromUrlDialog.txtNotUrl": "<PERSON><PERSON><PERSON><PERSON> pole by <PERSON><PERSON><PERSON> b<PERSON>t URL adresa ve formátu „http://www.example.com“", "Common.Views.ListSettingsDialog.textBulleted": "S odrážkami", "Common.Views.ListSettingsDialog.textFromFile": "<PERSON><PERSON> souboru", "Common.Views.ListSettingsDialog.textFromStorage": "Z úložiště", "Common.Views.ListSettingsDialog.textFromUrl": "Z adresy URL", "Common.Views.ListSettingsDialog.textNumbering": "Číslovaný", "Common.Views.ListSettingsDialog.textSelect": "Vybrat z", "Common.Views.ListSettingsDialog.tipChange": "Změnit odrážku", "Common.Views.ListSettingsDialog.txtBullet": "Odr<PERSON>ž<PERSON>", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "Obrázek", "Common.Views.ListSettingsDialog.txtImport": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtNewBullet": "Nová odrážka", "Common.Views.ListSettingsDialog.txtNewImage": "Nový obrázek", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtOfText": "% z textu", "Common.Views.ListSettingsDialog.txtSize": "Velikost", "Common.Views.ListSettingsDialog.txtStart": "Začít na", "Common.Views.ListSettingsDialog.txtSymbol": "Symbol", "Common.Views.ListSettingsDialog.txtTitle": "Nastavení seznamu", "Common.Views.ListSettingsDialog.txtType": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textCopy": "Kopírovat", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Vymazat", "Common.Views.MacrosDialog.textLoading": "Nahrávám ...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "Př<PERSON>menovat", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Uložit", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON> so<PERSON>", "Common.Views.OpenDialog.textInvalidRange": "Neplatný rozsah buněk", "Common.Views.OpenDialog.textSelectData": "Vybrat data", "Common.Views.OpenDialog.txtAdvanced": "Pokroč<PERSON><PERSON>", "Common.Views.OpenDialog.txtColon": "Dvojtečka", "Common.Views.OpenDialog.txtComma": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtDelimiter": "Odd<PERSON><PERSON><PERSON>č", "Common.Views.OpenDialog.txtDestData": "Zvolte kam umístit data", "Common.Views.OpenDialog.txtEmpty": "Toto pole je povinné", "Common.Views.OpenDialog.txtEncoding": "Kódován<PERSON>", "Common.Views.OpenDialog.txtIncorrectPwd": "<PERSON><PERSON><PERSON> není správn<PERSON>.", "Common.Views.OpenDialog.txtOpenFile": "Zadejte heslo pro otevření souboru", "Common.Views.OpenDialog.txtOther": "Ostatní", "Common.Views.OpenDialog.txtPassword": "He<PERSON><PERSON>", "Common.Views.OpenDialog.txtPreview": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtProtected": "<PERSON><PERSON><PERSON> heslo a soubor otevř<PERSON>, stávající heslo k souboru bude reset<PERSON>no.", "Common.Views.OpenDialog.txtSemicolon": "Středník", "Common.Views.OpenDialog.txtSpace": "Mezera", "Common.Views.OpenDialog.txtTab": "Tabulátory", "Common.Views.OpenDialog.txtTitle": "Vyberte %1 možností", "Common.Views.OpenDialog.txtTitleProtected": "Zabezpečený soubor", "Common.Views.PasswordDialog.txtDescription": "Nastavit heslo pro zabezpečení tohoto dokumentu", "Common.Views.PasswordDialog.txtIncorrectPwd": "<PERSON><PERSON> se neshoduj<PERSON>", "Common.Views.PasswordDialog.txtPassword": "He<PERSON><PERSON>", "Common.Views.PasswordDialog.txtRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON>", "Common.Views.PasswordDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.PasswordDialog.txtWarning": "Varování: Ztracené nebo zapomenuté he<PERSON>lo nelze obnovit. Uložte ji na bezpečném místě.", "Common.Views.PluginDlg.textLoading": "Načítání", "Common.Views.PluginPanel.textClosePanel": "Zavřít zásuvný modul", "Common.Views.PluginPanel.textLoading": "Načítání", "Common.Views.Plugins.groupCaption": "Z<PERSON>uv<PERSON><PERSON> moduly", "Common.Views.Plugins.strPlugins": "Z<PERSON>uv<PERSON><PERSON> moduly", "Common.Views.Plugins.textBackgroundPlugins": "Zásuvné moduly na pozadí", "Common.Views.Plugins.textSettings": "Nastavení", "Common.Views.Plugins.textStart": "Začátek", "Common.Views.Plugins.textStop": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "Seznam zásuvných modulů na pozadí", "Common.Views.Plugins.tipMore": "V<PERSON>ce", "Common.Views.Protection.hintAddPwd": "Šifrovat heslem", "Common.Views.Protection.hintDelPwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.hintPwd": "Změnit nebo smazat he<PERSON>lo", "Common.Views.Protection.hintSignature": "Přidat digitální podpis nebo", "Common.Views.Protection.txtAddPwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtChangePwd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtDeletePwd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Protection.txtEncrypt": "Šifrovat", "Common.Views.Protection.txtInvisibleSignature": "Přidat digit<PERSON>lní <PERSON>", "Common.Views.Protection.txtSignature": "Podpis", "Common.Views.Protection.txtSignatureLine": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.RecentFiles.txtOpenRecent": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.RenameDialog.textName": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.RenameDialog.txtInvalidName": "Název souboru nemůže obsahovat žádný z následujících znaků:", "Common.Views.ReviewChanges.hintNext": "K další změně", "Common.Views.ReviewChanges.hintPrev": "K předchozí změně", "Common.Views.ReviewChanges.strFast": "Automatický", "Common.Views.ReviewChanges.strFastDesc": "Společně prováděná úprava v reálném čase. Veškeré změny jsou ukládány automaticky.", "Common.Views.ReviewChanges.strStrict": "Statický", "Common.Views.ReviewChanges.strStrictDesc": "Pro synchronizaci změn, k<PERSON><PERSON> jste udělali vy a ostatní, použijte tlačítko „Uložit“.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Přijmout aktuální změnu", "Common.Views.ReviewChanges.tipCoAuthMode": "Nastavit režim spolupráce na úpravách", "Common.Views.ReviewChanges.tipCommentRem": "Smazat komentáře", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Smazat aktuální komentáře", "Common.Views.ReviewChanges.tipCommentResolve": "Vyřešit komentáře", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Vyřešit aktuální komentáře", "Common.Views.ReviewChanges.tipHistory": "Zobrazit historii verzí", "Common.Views.ReviewChanges.tipRejectCurrent": "Odmítnout aktuální změnu", "Common.Views.ReviewChanges.tipReview": "Sledovat změny", "Common.Views.ReviewChanges.tipReviewView": "<PERSON><PERSON><PERSON><PERSON>, ve k<PERSON><PERSON><PERSON>, aby se změny zobrazovaly", "Common.Views.ReviewChanges.tipSetDocLang": "Nastavit jazyk dokumentu", "Common.Views.ReviewChanges.tipSetSpelling": "Kontrola pra<PERSON>pisu", "Common.Views.ReviewChanges.tipSharing": "Spravovat přístupová práva k dokumentům", "Common.Views.ReviewChanges.txtAccept": "Př<PERSON><PERSON>mout", "Common.Views.ReviewChanges.txtAcceptAll": "Přijmout všechny změny", "Common.Views.ReviewChanges.txtAcceptChanges": "Přijmout změny", "Common.Views.ReviewChanges.txtAcceptCurrent": "Přijmout aktuální změnu", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "<PERSON><PERSON><PERSON> s<PERSON>upráce na úpravách", "Common.Views.ReviewChanges.txtCommentRemAll": "Smazat všechny komentáře", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Smazat aktuální komentáře", "Common.Views.ReviewChanges.txtCommentRemMy": "S<PERSON>zat mé komentáře", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Smazat mé aktuální komentáře", "Common.Views.ReviewChanges.txtCommentRemove": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolve": "Vyřešit", "Common.Views.ReviewChanges.txtCommentResolveAll": "Vyřešit všechny komentáře", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Vyřešit aktuální komentáře", "Common.Views.ReviewChanges.txtCommentResolveMy": "Vyřešit moje komentáře", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Vyřešit moje aktuální komentáře", "Common.Views.ReviewChanges.txtDocLang": "Jazyk", "Common.Views.ReviewChanges.txtFinal": "Všechny změny přijaty (náhled)", "Common.Views.ReviewChanges.txtFinalCap": "Konečné", "Common.Views.ReviewChanges.txtHistory": "Historie verzí", "Common.Views.ReviewChanges.txtMarkup": "Všechny změny (upravení)", "Common.Views.ReviewChanges.txtMarkupCap": "Značka", "Common.Views.ReviewChanges.txtNext": "Dalš<PERSON>", "Common.Views.ReviewChanges.txtOriginal": "Všechny změny by<PERSON> (náhled)", "Common.Views.ReviewChanges.txtOriginalCap": "Původní", "Common.Views.ReviewChanges.txtPrev": "Předchozí", "Common.Views.ReviewChanges.txtReject": "Odmítnout", "Common.Views.ReviewChanges.txtRejectAll": "Odmítnout všechny změny", "Common.Views.ReviewChanges.txtRejectChanges": "Odmítnout změny", "Common.Views.ReviewChanges.txtRejectCurrent": "Odmítnout aktuální změnu", "Common.Views.ReviewChanges.txtSharing": "Sdílení", "Common.Views.ReviewChanges.txtSpelling": "Kontrola pra<PERSON>pisu", "Common.Views.ReviewChanges.txtTurnon": "Sledovat změny", "Common.Views.ReviewChanges.txtView": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "Přidat odpověď", "Common.Views.ReviewPopover.textCancel": "Storno", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Zde napište svůj komentář", "Common.Views.ReviewPopover.textMention": "+zmínka poskytne přístup k dokumentu a pošle e-mail", "Common.Views.ReviewPopover.textMentionNotify": "+zmínka upozorní uživatele e-mailem", "Common.Views.ReviewPopover.textOpenAgain": "Znovu otevřít", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "Vyřešit", "Common.Views.ReviewPopover.textViewResolved": "Nemáte oprávnění pro opětovné otevír<PERSON><PERSON> k<PERSON>", "Common.Views.ReviewPopover.txtDeleteTip": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "Načítání", "Common.Views.SaveAsDlg.textTitle": "Složka do které uložit", "Common.Views.SearchPanel.textByColumns": "<PERSON> slou<PERSON>", "Common.Views.SearchPanel.textByRows": "<PERSON>", "Common.Views.SearchPanel.textCaseSensitive": "Roz<PERSON>šovat malá a velká písmena", "Common.Views.SearchPanel.textCell": "Buňka", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textContentChanged": "Došlo ke změně dokumentu.", "Common.Views.SearchPanel.textFind": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "<PERSON><PERSON><PERSON><PERSON> a na<PERSON>", "Common.Views.SearchPanel.textFormula": "Vzorec", "Common.Views.SearchPanel.textFormulas": "Vzorce", "Common.Views.SearchPanel.textItemEntireCell": "<PERSON><PERSON><PERSON> o<PERSON><PERSON> b<PERSON>", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} <PERSON><PERSON><PERSON> úsp<PERSON>šně na<PERSON>zeno.", "Common.Views.SearchPanel.textLookIn": "Hledat v", "Common.Views.SearchPanel.textMatchUsingRegExp": "Argument používá regulární výraz", "Common.Views.SearchPanel.textName": "Jméno", "Common.Views.SearchPanel.textNoMatches": "<PERSON><PERSON><PERSON> nebyla na<PERSON>a", "Common.Views.SearchPanel.textNoSearchResults": "Žádné výsledky vyhledávání", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} položek nahrazeno. Zbývající {2} položky jsou uzamčeny jinými už<PERSON>li. ", "Common.Views.SearchPanel.textReplace": "Nahradit", "Common.Views.SearchPanel.textReplaceAll": "Nahradit vše", "Common.Views.SearchPanel.textReplaceWith": "Nahradit pomocí", "Common.Views.SearchPanel.textSearch": "Hledat", "Common.Views.SearchPanel.textSearchAgain": "{0} Prov<PERSON><PERSON> nové h<PERSON> {1} pro př<PERSON><PERSON><PERSON> v<PERSON>.", "Common.Views.SearchPanel.textSearchHasStopped": "Vyhledávání bylo z<PERSON>o", "Common.Views.SearchPanel.textSearchOptions": "Možnosti hledání", "Common.Views.SearchPanel.textSearchResults": "<PERSON><PERSON><PERSON>dek hledání: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "<PERSON><PERSON><PERSON>dek hledání", "Common.Views.SearchPanel.textSelectDataRange": "<PERSON><PERSON><PERSON><PERSON> rozsah dat", "Common.Views.SearchPanel.textSheet": "List", "Common.Views.SearchPanel.textSpecificRange": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textTooManyResults": "Př<PERSON><PERSON>š mnoho výsledků pro zobrazení", "Common.Views.SearchPanel.textValue": "Hodnota", "Common.Views.SearchPanel.textValues": "Hodnoty", "Common.Views.SearchPanel.textWholeWords": "<PERSON>uze celá slova", "Common.Views.SearchPanel.textWithin": "V rámci", "Common.Views.SearchPanel.textWorkbook": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.tipNextResult": "Následující", "Common.Views.SearchPanel.tipPreviousResult": "Předchozí", "Common.Views.SelectFileDlg.textLoading": "Načítání", "Common.Views.SelectFileDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON> zdroj dat", "Common.Views.ShapeShadowDialog.txtAngle": "Úhel", "Common.Views.ShapeShadowDialog.txtDistance": "Vzdálenost", "Common.Views.ShapeShadowDialog.txtSize": "Velikost", "Common.Views.ShapeShadowDialog.txtTitle": "Upravit stíny", "Common.Views.ShapeShadowDialog.txtTransparency": "Průhlednost", "Common.Views.SignDialog.textBold": "Tučn<PERSON>", "Common.Views.SignDialog.textCertificate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textChange": "Změnit", "Common.Views.SignDialog.textInputName": "Zadat j<PERSON> podep<PERSON>uj<PERSON>", "Common.Views.SignDialog.textItalic": "Skloněné", "Common.Views.SignDialog.textNameError": "Je třeba vyplnit jméno podepisujícího.", "Common.Views.SignDialog.textPurpose": "Účel podepsání tohoto dokumentu", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "<PERSON>y<PERSON><PERSON> o<PERSON>", "Common.Views.SignDialog.textSignature": "Podpis vypadá jako", "Common.Views.SignDialog.textTitle": "Podepsat dokument", "Common.Views.SignDialog.textUseImage": "Nebo klikněte „Vybrat obrázek“ a použijte ho jako podpis", "Common.Views.SignDialog.textValid": "Platné od %1 do %2", "Common.Views.SignDialog.tipFontName": "Název písma", "Common.Views.SignDialog.tipFontSize": "Velikost písma", "Common.Views.SignSettingsDialog.textAllowComment": "Umožnit podepisujícímu přidat komentář", "Common.Views.SignSettingsDialog.textDefInstruction": "<PERSON><PERSON><PERSON> pod<PERSON><PERSON><PERSON>, ov<PERSON><PERSON><PERSON> správnost podepisovaného obsahu. ", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail navrhov<PERSON><PERSON><PERSON>tele", "Common.Views.SignSettingsDialog.textInfoName": "Navrhovaný podpisovatel", "Common.Views.SignSettingsDialog.textInfoTitle": "Název navrhovaného podepisujícího", "Common.Views.SignSettingsDialog.textInstructions": "Pokyny pro podepisujícího", "Common.Views.SignSettingsDialog.textShowDate": "Na řádku s podpisem zobrazit datum podpisu", "Common.Views.SignSettingsDialog.textTitle": "Nastavení podpisu", "Common.Views.SignSettingsDialog.txtEmpty": "Toto pole je povinné", "Common.Views.SymbolTableDialog.textCharacter": "Znak", "Common.Views.SymbolTableDialog.textCode": "Unicode HEX hodnota", "Common.Views.SymbolTableDialog.textCopyright": "Znak autorských práv", "Common.Views.SymbolTableDialog.textDCQuote": "Uzavírací dvojitá uvozovka", "Common.Views.SymbolTableDialog.textDOQuote": "Otevírací dvojitá uvozovka", "Common.Views.SymbolTableDialog.textEllipsis": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> el<PERSON>", "Common.Views.SymbolTableDialog.textEmDash": "Dlouhá pomlčka", "Common.Views.SymbolTableDialog.textEmSpace": "Dlouhá mezera", "Common.Views.SymbolTableDialog.textEnDash": "Krátká pomlčka", "Common.Views.SymbolTableDialog.textEnSpace": "Krátká mezera", "Common.Views.SymbolTableDialog.textFont": "Písmo", "Common.Views.SymbolTableDialog.textNBHyphen": "pevná pomlčka", "Common.Views.SymbolTableDialog.textNBSpace": "Nezlomitelná mezera", "Common.Views.SymbolTableDialog.textPilcrow": "Značka odstavce", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 em mezera", "Common.Views.SymbolTableDialog.textRange": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textRecent": "Nedávno použité symboly", "Common.Views.SymbolTableDialog.textRegistered": "Symbol registrované ochranné z<PERSON>ky", "Common.Views.SymbolTableDialog.textSCQuote": "Uzavírací jednoduchá uvozovka", "Common.Views.SymbolTableDialog.textSection": "Paragra<PERSON>", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON><PERSON><PERSON><PERSON>ová zkratka", "Common.Views.SymbolTableDialog.textSHyphen": "Měkká pomlčka", "Common.Views.SymbolTableDialog.textSOQuote": "Otevírací jednoduchá uvozovka", "Common.Views.SymbolTableDialog.textSpecial": "Speciální znaky", "Common.Views.SymbolTableDialog.textSymbols": "Symboly", "Common.Views.SymbolTableDialog.textTitle": "Symbol", "Common.Views.SymbolTableDialog.textTradeMark": "Symbol užívané obchodní značky", "Common.Views.UserNameDialog.textDontShow": "<PERSON>novu se neptat", "Common.Views.UserNameDialog.textLabel": "Štítek:", "Common.Views.UserNameDialog.textLabelError": "Štítek je nutné vyplnit.", "SSE.Controllers.DataTab.strSheet": "List", "SSE.Controllers.DataTab.textAddExternalData": "Byl přidán odkaz na externí zdroj. Takovéto odkazy můžete aktualizovat v sekci Data.", "SSE.Controllers.DataTab.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textContinue": "Continue", "SSE.Controllers.DataTab.textDontUpdate": "Neaktualizovat", "SSE.Controllers.DataTab.textEmptyUrl": "Je třeba zadat URL adresu.", "SSE.Controllers.DataTab.textRows": "Řádky", "SSE.Controllers.DataTab.textTurnOff": "Turn off AutoUpdate", "SSE.Controllers.DataTab.textUpdate": "Aktualizace", "SSE.Controllers.DataTab.textWizard": "Text na sloupce", "SSE.Controllers.DataTab.txtDataValidation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dat", "SSE.Controllers.DataTab.txtErrorExternalLink": "Chyba: Aktual<PERSON><PERSON> se<PERSON>", "SSE.Controllers.DataTab.txtExpand": "Rozbalit", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Data v okolí výběru nebudou odstraněny. Chcete rozšířit výběr, aby zahrnoval sousední data, nebo pokračovat pouze s aktuálně vybranými buňkami?", "SSE.Controllers.DataTab.txtExtendDataValidation": "Výb<PERSON>r obsahuje některé buňky bez nastaveného ověření dat.<br> <PERSON>cete rozšířit ověřování dat na tyto buňky?", "SSE.Controllers.DataTab.txtImportWizard": "Průvodce importem textu", "SSE.Controllers.DataTab.txtRemDuplicates": "<PERSON><PERSON><PERSON><PERSON> duplicity", "SSE.Controllers.DataTab.txtRemoveDataValidation": "Výběr obsahuje více než jeden typ ověření.<br>Vymazat aktuální nastavení a pokračovat?", "SSE.Controllers.DataTab.txtRemSelected": "Vyjmout vybrané", "SSE.Controllers.DataTab.txtUrlTitle": "Zadejte URL, na které se nacházejí data", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "This workbook contains links to external sources which update automatically. This might be unsafe.<br><br>If you trust them, press Continue.", "SSE.Controllers.DataTab.warnUpdateExternalData": "Sešit obsahuje odkazy na jeden nebo více externí<PERSON>, kter<PERSON> mohou být nebez<PERSON>č<PERSON>é.<br>Pokud od<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON><PERSON><PERSON>, aktualiz<PERSON>j<PERSON> je, pro získ<PERSON><PERSON> ne<PERSON> dat.", "SSE.Controllers.DocumentHolder.alignmentText": "Zarovnání", "SSE.Controllers.DocumentHolder.centerText": "<PERSON> střed", "SSE.Controllers.DocumentHolder.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Controllers.DocumentHolder.deleteRowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.deleteText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Odkaz neexistuje. Prosím, opravte odkaz nebo jej s<PERSON>.", "SSE.Controllers.DocumentHolder.guestText": "Návštěvník", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Sloupec vlevo", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Sloupec vpravo", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Řádek nad", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Řádek pod", "SSE.Controllers.DocumentHolder.insertText": "Vložit", "SSE.Controllers.DocumentHolder.leftText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Varování", "SSE.Controllers.DocumentHolder.rightText": "Vpravo", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Autokorekce možnosti", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON><PERSON><PERSON><PERSON> sloupce {0} symboly ({1} pixelů)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Výška řádku {0} bod<PERSON> ({1} pixelů)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Buď klikněte a otevřete odkaz nebo klikněte a podržte a pro označení buňky.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Vložit vlevo", "SSE.Controllers.DocumentHolder.textInsertTop": "Vložit řádek nad", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Vložit jinak", "SSE.Controllers.DocumentHolder.textStopExpand": "Zastavit <PERSON> r<PERSON>řování tabulek", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Prvek je upravován jiným uživatelem.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Nadprůměrné", "SSE.Controllers.DocumentHolder.txtAddBottom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Přidat zlomkovou čáru", "SSE.Controllers.DocumentHolder.txtAddHor": "Přidat vodorovnou čáru", "SSE.Controllers.DocumentHolder.txtAddLB": "Přidat přidat čáru vlevo dole", "SSE.Controllers.DocumentHolder.txtAddLeft": "Přidat ohraničení vlevo", "SSE.Controllers.DocumentHolder.txtAddLT": "Přidat čáru vlevo nahoře", "SSE.Controllers.DocumentHolder.txtAddRight": "Přidat ohraničení vpravo", "SSE.Controllers.DocumentHolder.txtAddTop": "Přidat ohraničení nahoře", "SSE.Controllers.DocumentHolder.txtAddVer": "Přidat svislou čáru", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Zarovnat vůči znaku", "SSE.Controllers.DocumentHolder.txtAll": "(vše)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON> obs<PERSON> tabul<PERSON>, nebo def<PERSON><PERSON><PERSON><PERSON> sloup<PERSON> tabulky tj. <PERSON><PERSON><PERSON><PERSON><PERSON>, data a celkový počet řádků", "SSE.Controllers.DocumentHolder.txtAnd": "a", "SSE.Controllers.DocumentHolder.txtBegins": "Začíná na", "SSE.Controllers.DocumentHolder.txtBelowAve": "Podprůměrné", "SSE.Controllers.DocumentHolder.txtBlanks": "(prázdné)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Vlastnosti ohraničení", "SSE.Controllers.DocumentHolder.txtBottom": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtByField": "%1 z %2", "SSE.Controllers.DocumentHolder.txtColumn": "Sloupec", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Zarovnání sloupce", "SSE.Controllers.DocumentHolder.txtContains": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Odkaz zkopírován do schránky", "SSE.Controllers.DocumentHolder.txtDataTableHint": "<PERSON><PERSON><PERSON><PERSON> hodnotu buněk tabulky, nebo def<PERSON><PERSON><PERSON><PERSON> slou<PERSON><PERSON> tabulky", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Snížit velikost argumentu", "SSE.Controllers.DocumentHolder.txtDeleteArg": "<PERSON><PERSON><PERSON><PERSON> argument", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Smazat ruční zalomení", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Smazat uzavřené z<PERSON>", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Smazat uzavřené znaky a oddělovače", "SSE.Controllers.DocumentHolder.txtDeleteEq": "<PERSON><PERSON><PERSON><PERSON> rovnici", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Smazat znak", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "S<PERSON>zat odmocninu", "SSE.Controllers.DocumentHolder.txtEnds": "Končí na", "SSE.Controllers.DocumentHolder.txtEquals": "Rovná se", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Shodné s barvou buňky", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Shodné s barvou písma", "SSE.Controllers.DocumentHolder.txtExpand": "Rozbalit a seřadit", "SSE.Controllers.DocumentHolder.txtExpandSort": "Data vedle výběru nebudou seřazena. Chcete rozšířit výběr tak, aby zahrnoval sousední data, nebo pokračovat v seřazení pouze vybraných buněk?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtFilterTop": "Nahoře", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Změnit na lineární zlomek", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Změnit na zkosený zlomek", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Změnit na složený zlomek", "SSE.Controllers.DocumentHolder.txtGreater": "Větš<PERSON> než", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Větší nebo rovno", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Znak nad textem", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Znak pod textem", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Získá hlavičky v tabulce, nebo hlavičky definovaných sloupců tabulky", "SSE.Controllers.DocumentHolder.txtHeight": "Výška", "SSE.Controllers.DocumentHolder.txtHideBottom": "Skrýt dolní <PERSON>", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Skrýt dolní limit", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Skrýt uzavírací závorku", "SSE.Controllers.DocumentHolder.txtHideDegree": "Skrýt stupeň", "SSE.Controllers.DocumentHolder.txtHideHor": "Skrýt vodorovnou čáru", "SSE.Controllers.DocumentHolder.txtHideLB": "Skrýt levou dolní čáru", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON><PERSON><PERSON> lev<PERSON>", "SSE.Controllers.DocumentHolder.txtHideLT": "Skrýt levou horní čáru", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Skrýt otevírací závorku", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Skrýt výplňový objekt", "SSE.Controllers.DocumentHolder.txtHideRight": "Skr<PERSON><PERSON> prav<PERSON>", "SSE.Controllers.DocumentHolder.txtHideTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> oh<PERSON>", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Skrýt horní limit", "SSE.Controllers.DocumentHolder.txtHideVer": "Skr<PERSON>t s<PERSON> č<PERSON>", "SSE.Controllers.DocumentHolder.txtImportWizard": "Průvodce importem textu", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Zvětšit velikost argumentu", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Vložit argument za", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Vložit argument před", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Vložit ruční zalomení", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Vložit rovnici za", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Vložit rovnici před", "SSE.Controllers.DocumentHolder.txtItems": "Polož<PERSON>", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Ponechat pouze text", "SSE.Controllers.DocumentHolder.txtLess": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtLessEquals": "Méně než nebo rovno", "SSE.Controllers.DocumentHolder.txtLimitChange": "Změnit umístění limitu", "SSE.Controllers.DocumentHolder.txtLimitOver": "Limita nad textem", "SSE.Controllers.DocumentHolder.txtLimitUnder": "<PERSON>ita pod textem", "SSE.Controllers.DocumentHolder.txtLockSort": "Poblíž Vašeho výběru existují data, nemáte však dostatečná oprávnění k úpravě těchto buněk.<br>Chcete pokračovat s aktuálním výběrem?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Přizpůsobit závorky výšce argumentu", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Zarovnání matice", "SSE.Controllers.DocumentHolder.txtNoChoices": "Neexistují <PERSON><PERSON>é možnosti pro vyplnění buňky.<br><PERSON><PERSON><PERSON> n<PERSON> je možné vybrat pouze textové hodnoty ze sloupce.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Nezačíná na", "SSE.Controllers.DocumentHolder.txtNotContains": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtNotEnds": "Nekončí na", "SSE.Controllers.DocumentHolder.txtNotEquals": "<PERSON><PERSON><PERSON> r<PERSON>", "SSE.Controllers.DocumentHolder.txtOr": "nebo", "SSE.Controllers.DocumentHolder.txtOverbar": "Čárka nad textem", "SSE.Controllers.DocumentHolder.txtPaste": "Vložit", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Vzorec bez ohraničení", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Vzorec + ší<PERSON><PERSON> sloupce", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Formátov<PERSON><PERSON> c<PERSON>", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Vložit pouze formátování", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Vzorec + form<PERSON>t <PERSON>", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Vložit pouze vzorec", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Vzorec + všechna formátování", "SSE.Controllers.DocumentHolder.txtPasteLink": "Vložit odkaz", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Připojený obrázek", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Sloučit podmíněné <PERSON>", "SSE.Controllers.DocumentHolder.txtPastePicture": "Obrázek", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Formátování zdroje", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Přemístit", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Hodnota + všechna formátování", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Hodnota + formátování č<PERSON>el", "SSE.Controllers.DocumentHolder.txtPasteValues": "Vložit pouze hodnotu", "SSE.Controllers.DocumentHolder.txtPercent": "procento", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Znovu provést automatické zvětšení tabulky", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Odstranit zlomkovou čáru", "SSE.Controllers.DocumentHolder.txtRemLimit": "Odebrat limit", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Odstranit diakritiku ", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Odstranit čáru", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Chcete tento podpis odstranit?<br><PERSON>to krok je nevrat<PERSON>. ", "SSE.Controllers.DocumentHolder.txtRemScripts": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Odebrat dolní index", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Odebrat horní index", "SSE.Controllers.DocumentHolder.txtRowHeight": "Výška řádku", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Skripty po textu", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "<PERSON><PERSON><PERSON><PERSON> před textem", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Zobrazit dolní limit", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Zobrazit uzavírací závorku", "SSE.Controllers.DocumentHolder.txtShowDegree": "Zobrazit stupeň", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Zobrazit otevírací závorku", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Zobrazit výplňový objekt", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Zobrazit horní limit", "SSE.Controllers.DocumentHolder.txtSorting": "Řazení", "SSE.Controllers.DocumentHolder.txtSortSelected": "Se<PERSON><PERSON><PERSON> v<PERSON>né", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Roztáhn<PERSON> z<PERSON>ky", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Zvolte pouze tento řádek zadaného slou<PERSON>ce", "SSE.Controllers.DocumentHolder.txtTop": "Nahoře", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Získá hodnotu celkového p<PERSON><PERSON>, nebo def<PERSON><PERSON><PERSON><PERSON> sloup<PERSON><PERSON> tabulky", "SSE.Controllers.DocumentHolder.txtUnderbar": "Čárka pod textem", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Vzít zpět automatické rozšíření tabulky", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Použít průvodce importem textu", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Kliknutí na tento odkaz může být škodlivé pro Vaše zařízení a Vaše data.<br>Jste si jistí, že chcete pok<PERSON>č<PERSON>t?", "SSE.Controllers.DocumentHolder.txtWidth": "Šířka", "SSE.Controllers.DocumentHolder.warnFilterError": "Aby bylo mož<PERSON>é p<PERSON>t filtr hodnot, je třeba, aby v oblasti Hodnoty byla alespoň jedna hodnota.", "SSE.Controllers.FormulaDialog.sCategoryAll": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Vlastní", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Datum a čas", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Technické", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Finanční", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Informace", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 pos<PERSON><PERSON><PERSON> použ<PERSON>ých", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logické", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Vyhledávání a odkazování", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Matematika a trigonometrie", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistické", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Text a data", "SSE.Controllers.LeftMenu.newDocumentTitle": "Nepojmenovan<PERSON> sešit", "SSE.Controllers.LeftMenu.textByColumns": "<PERSON> slou<PERSON>", "SSE.Controllers.LeftMenu.textByRows": "<PERSON>", "SSE.Controllers.LeftMenu.textFormulas": "Vzorce", "SSE.Controllers.LeftMenu.textItemEntireCell": "<PERSON><PERSON><PERSON> o<PERSON><PERSON> b<PERSON>", "SSE.Controllers.LeftMenu.textLoadHistory": "Načítání historie verzí...", "SSE.Controllers.LeftMenu.textLookin": "Hledat v", "SSE.Controllers.LeftMenu.textNoTextFound": "<PERSON>, <PERSON><PERSON><PERSON> h<PERSON>, <PERSON><PERSON><PERSON>. Upravte parametry vyhledávání.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Nahrazení bylo <PERSON>. {0} v<PERSON>skyt<PERSON> bylo p<PERSON>.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "<PERSON>led<PERSON><PERSON> bylo <PERSON>. Nahrazeno výskytů: {0}", "SSE.Controllers.LeftMenu.textSave": "Uložit", "SSE.Controllers.LeftMenu.textSearch": "Hledat", "SSE.Controllers.LeftMenu.textSelectPath": "Zadejte nový název pro uložení kopie souboru", "SSE.Controllers.LeftMenu.textSheet": "List", "SSE.Controllers.LeftMenu.textValues": "Hodnoty", "SSE.Controllers.LeftMenu.textWarning": "Varování", "SSE.Controllers.LeftMenu.textWithin": "V rámci", "SSE.Controllers.LeftMenu.textWorkbook": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.txtUntitled": "Bez názvu", "SSE.Controllers.LeftMenu.warnDownloadAs": "Pokud budete pokračovat v ukládání v tomto formátu, vše kromě textu bude ztraceno.<br>Opravdu chcete pokračovat?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "Formát CSV nepodporuje ukládání souboru s více listy.<br>Chcete-li zachovat zvolený formát a uložit pouze aktuální list, stiskněte tlačítko Uložit.<br>Chcete-li uložit aktuální sešit, klikněte na tlačítko Zrušit a uložte jej v jiném formátu.", "SSE.Controllers.Main.confirmAddCellWatches": "<PERSON>uto akcí přidáte {0} kukátek do buněk.<br>Opravdu chcete pokračovat?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "<PERSON>uto akcí přidá<PERSON> pouze {0} kukátek do buněk, z důvodu ukládání do paměti.<br>Opravdu chcete pokračovat?", "SSE.Controllers.Main.confirmMaxChangesSize": "Množství akcí překračuje limity nastavené pro váš server.<br>Stisknutím tlačítka \"Zpět\" zruš<PERSON>te p<PERSON>led<PERSON>í a<PERSON>, nebo stisknutí<PERSON> tlačí<PERSON> \"Pokračovat\" ponecháte akci lokálně (soubor si musí<PERSON> st<PERSON>, nebo zkopírovat jeho obsah, abyste se ujistili, že se nic neztratí).", "SSE.Controllers.Main.confirmMoveCellRange": "Rozsah cílových buněk může obsahovat data. Pokračovat v operaci?", "SSE.Controllers.Main.confirmPutMergeRange": "Zdrojová data obsahovala sloučené buňky.<br><PERSON><PERSON> roz<PERSON>ny již před tím, než byly vloženy do tabulky.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Vzorce v záhlaví budou odstraněny a převedeny na statický text.<br>Opravdu chcete pokračovat?", "SSE.Controllers.Main.confirmReplaceHFPicture": "Do každé části záhlaví lze vložit pouze jeden obrázek.<br>Stisknutím tlačítka \"Nahradit\" nahradíte stávající obrázek.<br>Stisknutím tlačí<PERSON>ka \"<PERSON><PERSON><PERSON>\" ponecháte stávající obrázek.", "SSE.Controllers.Main.convertationTimeoutText": "Překročen časový limit pro provedení převodu.", "SSE.Controllers.Main.criticalErrorExtText": "Pro návrat na seznam dokumentů klikněte na „OK“.", "SSE.Controllers.Main.criticalErrorTitle": "Chyba", "SSE.Controllers.Main.downloadErrorText": "Stahování se nezdařilo.", "SSE.Controllers.Main.downloadTextText": "Stahování sešitu...", "SSE.Controllers.Main.downloadTitleText": "Stahování sešitu", "SSE.Controllers.Main.errNoDuplicates": "<PERSON><PERSON><PERSON><PERSON><PERSON> hodnoty nebyly na<PERSON>.", "SSE.Controllers.Main.errorAccessDeny": "Pokoušíte se provést a<PERSON>, na kterou nemáte oprávnění.<br>Obraťte se na správce vámi využívaného dokumentového serveru.", "SSE.Controllers.Main.errorArgsRange": "Chyba v zadaném vzorci.<br>Použit nesprávný rozsah argumentu.", "SSE.Controllers.Main.errorAutoFilterChange": "Operace není p<PERSON>, protože se pokouší posunout buňky v tabulce na listu.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Operaci nelze provést pro vybrané <PERSON>, proto<PERSON><PERSON> nelze přesunout část tabulky.<br>Vyberte jinou oblast dat tak, aby byla celá tabulka byla posunuta a zkuste to znovu.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Operaci nelze pro zvolený rozsah buněk provést.<br>Vyberte jednotnou oblast dat odlišnou od už existující a zkuste to znovu.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Operaci nelze <PERSON>, protože oblast obsahuje filtrované buňky.<br>Odkryjte filtrované prvky a zkuste to znovu.", "SSE.Controllers.Main.errorBadImageUrl": "URL adresa obrázku není správně", "SSE.Controllers.Main.errorCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.Main.errorCannotPasteImg": "Tento obrázek nelze vložit ze <PERSON>, ale můžete jej uložit do zařízení a vložit jej odtud, nebo můžete zkopírovat obrázek bez textu a vložit jej do sešitu.", "SSE.Controllers.Main.errorCannotUngroup": "Nelze zrušit seskupení. Chcete-li za<PERSON><PERSON><PERSON>, vyberte řádky nebo sloupce a seskupte je.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Tento příkaz nelze použít na zabezpečený list. Pro použití příkazu, zrušte zabezpečení listu.<br><PERSON><PERSON><PERSON><PERSON> bý<PERSON> vyžadováno zadání hesla.", "SSE.Controllers.Main.errorChangeArray": "Nemů<PERSON><PERSON> mě<PERSON>t <PERSON> pole.", "SSE.Controllers.Main.errorChangeFilteredRange": "Dojde ke změně rozsahu filtrů v listě.<br>Pro provedení je nutné vypnout automatické filtry.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "Buňka nebo graf, který se pokoušíte změnit je na zabezpečeném listu. Pro provedení změny, vypněte zabezpečení listu. Může být vyžadováno zadání hesla.", "SSE.Controllers.Main.errorCircularReference": "Jeden nebo více kruhových odkazů, kde vzorec odkazuje na vlastní buňku přímo či nepřímo, je obsa<PERSON><PERSON>.<br>Zkuste tyto odkazy ods<PERSON>nit, <PERSON><PERSON><PERSON><PERSON><PERSON>, nebo přesunout vzorce do jiných buněk.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Spojení se serverem ztraceno. Dokument v tuto chvíli nelze upravovat.", "SSE.Controllers.Main.errorConnectToServer": "Dokument se nedaří uložit. Zkontrolujte nastavení vašeho připojení nebo se obraťte na svého správce.<br> <PERSON><PERSON><PERSON>net<PERSON> na „OK“ budete vyzváni k tomu, abyste si dokument stáhli.", "SSE.Controllers.Main.errorConvertXml": "Soubor je v nepodporovaném formátu.<br>Pouze formát Tabulka XML 2003 může být použita.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Tento příkaz nelze použít s více výběry.<br><PERSON><PERSON><PERSON><PERSON> jeden z rozsahů a zkuste to znovu.", "SSE.Controllers.Main.errorCountArg": "Chyba v zadaném vzorci.<br>Použit nesprávný počet argumentů.", "SSE.Controllers.Main.errorCountArgExceed": "Chyba v zadaném vzorci.<br>Překročen počet argumentů.", "SSE.Controllers.Main.errorCreateDefName": "Stávající pojmenované rozsahy nelze upravovat a nové nyní nelze vytvořit<br>proto<PERSON><PERSON> některé z nich jsou právě upravovány.", "SSE.Controllers.Main.errorCreateRange": "Stávající rozsahy nemohou být upraveny a nové nemohou být vytvořeny<br>v tuto chv<PERSON><PERSON>, protože některé z nich se upravují.", "SSE.Controllers.Main.errorDatabaseConnection": "Externí chyba.<br>Chyba spojení s databází. Pokud chyba přetrvává, obraťte se na podporu.", "SSE.Controllers.Main.errorDataEncrypted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> – bez hesla je není možné zobrazit.", "SSE.Controllers.Main.errorDataRange": "Nesprávný datový rozsah.", "SSE.Controllers.Main.errorDataValidate": "Zadaná hodnota není platná.<br><PERSON><PERSON><PERSON><PERSON> má omezeny hodnoty, které je možné zadat do této buňky.", "SSE.Controllers.Main.errorDefaultMessage": "<PERSON><PERSON><PERSON>: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Pokoušíte se smazat sloupec, který obsahuje uzamčenou buňku. Uzamčené buňky nemohou být smazány dokud list je zabezpečen.<br>Pro smazání uzamčené buňky, vypněte zabezpečení listu. Může být vyžadováno zadání hesla.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Pokoušíte se s<PERSON><PERSON><PERSON>, který obsahuje uzamčené buňky. Uzamčené buňky nemohou být smazány dokud je list zabezpečen.<br>Pro smazání uzam<PERSON>ené buňky, vypněte zabezpečení listu. Může být vyžadováno zadání hesla.", "SSE.Controllers.Main.errorDependentsNoFormulas": "Příkaz Sledovat následovníky nenalezl žá<PERSON><PERSON>, kter<PERSON> by by<PERSON> mo<PERSON>.", "SSE.Controllers.Main.errorDirectUrl": "Ověřte správnost odkazu na dokument.<br>Musí jít o přímý odkaz pro stažení souboru.", "SSE.Controllers.Main.errorEditingDownloadas": "Při práci s dokumentem došlo k chybě.<br>Použijte volbu „Stáhnout jako“ a uložte si do souboru jako záložní kopii na svůj počítač.", "SSE.Controllers.Main.errorEditingSaveas": "Při práci s dokumentem došlo k chybě.<br>Použijte volbu „Uložit jako...“ a uložte si do souboru jako záložní kopii na svůj počítač.", "SSE.Controllers.Main.errorEditView": "Během úpravy některého se zobrazení se<PERSON>, není možné upravovat existující zobrazení sešitů a vytvářet nové.", "SSE.Controllers.Main.errorEmailClient": "Nenalezen žádný e-mailový klient.", "SSE.Controllers.Main.errorFilePassProtect": "<PERSON><PERSON>or je z<PERSON>en hesle<PERSON>, bez kterého ho nelze otevřít.", "SSE.Controllers.Main.errorFileRequest": "Externí chyba.<br>Chyba požadavku na soubor. Pokud chyba přetrvává, obraťte se na podporu.", "SSE.Controllers.Main.errorFileSizeExceed": "Velikost souboru překračuje omezení nastavená na serveru, který využíváte.<br>Ohledně podrobností se obraťte na správce dokumentového serveru.", "SSE.Controllers.Main.errorFileVKey": "Externí chyba.<br>Nesprávný klíč <PERSON>zpečení. V případě, že chyba přetrvává, obraťte se na podporu.", "SSE.Controllers.Main.errorFillRange": "Nelze vyplnit vybranou oblast buněk.<br>Všechny sloučené buňky musí být stejně velké.", "SSE.Controllers.Main.errorForceSave": "Došlo k chybě při ukládání souboru. Použijte volbu „Stáhnout jako“ a uložte si do souboru na svůj počítač nebo to zkuste později znovu.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Cannot enter a formula for an item or field name in a pivot table report.", "SSE.Controllers.Main.errorFormulaName": "Chyba v zadaném vzorci.<br>Použit nesprávný název vzorce.", "SSE.Controllers.Main.errorFormulaParsing": "Interní chyba při analýze vzorce.", "SSE.Controllers.Main.errorFrmlMaxLength": "Délka vzorce je delší než limit 8192 znaků.<br>Prosím upravte vzorec a akci opakujte.", "SSE.Controllers.Main.errorFrmlMaxReference": "Takový vzorec není m<PERSON>, proto<PERSON>e obsahuje pří<PERSON>š mnoho hodnot,<br> od<PERSON><PERSON><PERSON> na tabulky nebo jmen.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "<PERSON><PERSON><PERSON><PERSON> textové hodnoty ve vzorcích je omezena na 255 znaků.<br>Po<PERSON><PERSON><PERSON>jte funkci SPOJIT nebo spojovací operátor (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Funkce odkazuje na list, který neexistuje.<br>Zkontrolujte data a zkuste to znovu.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Operace nemohla být pro vybraný rozsah buněk dokončena.<br><PERSON><PERSON><PERSON><PERSON> rozsah tak, aby první řádek tabulky byl na stejném řádku<br>a výsledná tabulka překrývala tu stávající.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Operace nemohla být pro vybraný rozsah buněk dokončena.<br><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> neobsahuje jin<PERSON>.", "SSE.Controllers.Main.errorInconsistentExt": "<PERSON><PERSON><PERSON> otevírání souboru došlo k chybě.<br><PERSON><PERSON><PERSON> souboru neodpovídá příponě souboru.", "SSE.Controllers.Main.errorInconsistentExtDocx": "<PERSON><PERSON>i otevírání souboru došlo k chybě.<br><PERSON><PERSON><PERSON> souboru odpovídá textovým dokumentům (např. docx), ale má nekorespondující příponu: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "<PERSON>ři otevírání souboru došlo k chybě.<br><PERSON><PERSON><PERSON> souboru odpovídá některému z následujících formátů: pdf/djvu/xps/oxps, ale má nekorespondující příponu: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "Při otevírání souboru došlo k chybě.<br><PERSON><PERSON><PERSON> souboru odpovídá dokumentům vzniklým v editoru prezentací (např. pptx), ale soubor má nekorespondující příponu: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "Při otevírání souboru došlo k chybě.<br><PERSON><PERSON>ah souboru odpovídá dokumentům vzniklým v tabulkovém editoru (např. xlsx), ale má nekorespondující příponu: %1.", "SSE.Controllers.Main.errorInvalidRef": "Zadejte správný název pro výběr nebo platnou referenci.", "SSE.Controllers.Main.errorKeyEncrypt": "Neznámý popisovač klíče", "SSE.Controllers.Main.errorKeyExpire": "Platnost popisovače klíče skončila", "SSE.Controllers.Main.errorLabledColumnsPivot": "Pro vytvoření kontingenční ta<PERSON>, použijte data uspořádaná jako seznam s označenými sloupci.", "SSE.Controllers.Main.errorLoadingFont": "Písma nejsou načtená.<br>Obraťte se na správce vámi využívaného dokumentového serveru.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Odkaz na umístnění nebo rozsah dat je neplatný. ", "SSE.Controllers.Main.errorLockedAll": "Operace nemůže být <PERSON>, protože list byl uzamčen jiným uživatelem.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "Jedna z buněk zahrnutých v procesu Hledání cíle byla upravena jiným uživatelem. ", "SSE.Controllers.Main.errorLockedCellPivot": "Nemůžete měnit data uvnitř kontingenční tabulky.", "SSE.Controllers.Main.errorLockedWorksheetRename": "V tuto chvíli list nelze př<PERSON>, proto<PERSON>e je přejmenováván jiným uživatelem", "SSE.Controllers.Main.errorMaxPoints": "Nejvyšší možný počet bodů v řadě na graf je 4096.", "SSE.Controllers.Main.errorMoveRange": "Nelze změnit část sloučené buňky", "SSE.Controllers.Main.errorMoveSlicerError": "Průřezy tabulky nemůžou být zkopírovány z jednoho sešitu do druhého.<br>Akci opakujte vybráním celé tabulky včetně průřezů.", "SSE.Controllers.Main.errorMultiCellFormula": "V tabulkách nejsou dovoleny vzorce pro pole s vícero buňkami.", "SSE.Controllers.Main.errorNoDataToParse": "Nebyla vybrána žádná data pro zpracování.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.Main.errorOpenWarning": "Délka jednoho ze vzorců v souboru překročila<br>povolený počet znaků, proto byl vzorec odstraněn.", "SSE.Controllers.Main.errorOperandExpected": "Form<PERSON> (syntaxe) funkce není správná. Zkontrolujte zda nechybí jedna ze závorek – „(“ nebo „)“.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "<PERSON><PERSON><PERSON><PERSON>.<br> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, že máte vypnutý CAPS LOCK a správnou velikosti písmen.", "SSE.Controllers.Main.errorPasteInPivot": "We can't make this change for the selected cells because it will affect a pivot table.<br>Use the field list to change the report.", "SSE.Controllers.Main.errorPasteMaxRange": "Kopírovaná oblast a oblast pro vložení se neshodují.<br>Vyberte oblast se stejnou velikosti nebo klikněte na první buňku v řádku a vložte zkopírované buňky.", "SSE.Controllers.Main.errorPasteMultiSelect": "Tuto akci nelze provést v případě výběru více rozsahů.<br><PERSON><PERSON><PERSON><PERSON> jeden rozsah a akci opakujte.", "SSE.Controllers.Main.errorPasteSlicerError": "Průřezy tabulky nemůžou být zkopírovány z jednoho sešitu do druhého.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Pivot table field name already exists.", "SSE.Controllers.Main.errorPivotGroup": "Vybrané objekty nelze seskupit.", "SSE.Controllers.Main.errorPivotOverlap": "Výstup z kontingenční tabulky nesmí překrývat tabulku.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Výkaz z kontingenční tabulky byl uložen bez podkladových dat.<br>Pro aktualizaci klikněte na tlačítko \"Aktualizovat\".", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "Příkaz vyhledat předchůdce vyžaduje, aby buňka obsahovala vzorec zahrnující platné vazby. ", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Je ná<PERSON> líto, ale ve stávajícíí verzi programu není možné vytisknout více jak 1500 stránek najedn<PERSON>.<br><PERSON><PERSON> omezení bude nadcházejících vydáních odstraněno.", "SSE.Controllers.Main.errorProcessSaveResult": "Ukládání se nezdařilo", "SSE.Controllers.Main.errorProtectedRange": "V této oblasti není editace povolena.", "SSE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "SSE.Controllers.Main.errorServerVersion": "Verze editoru byla aktualizována. Stránka bude znovu načtena, aby se změny uplatnily.", "SSE.Controllers.Main.errorSessionAbsolute": "Platnost relace upravování dokumentu skončila. Načtete stránku znovu.", "SSE.Controllers.Main.errorSessionIdle": "Po dost dlouhou dobu jste s otevřeným dokumentem nepracovali. Načtete stránku znovu.", "SSE.Controllers.Main.errorSessionToken": "Spojení se <PERSON>em bylo <PERSON>. Načtěte stránku znovu.", "SSE.Controllers.Main.errorSetPassword": "<PERSON><PERSON><PERSON> b<PERSON><PERSON>", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Umístnění odkazu je chybné, proto<PERSON>e buňky nejsou na stejném řádku nebo sloupci.<br>Vyberte buňky na stejném řádku nebo sloupci.", "SSE.Controllers.Main.errorStockChart": "Nesprávné pořadí řádků. Pro vytvoření burzovního grafu umístěte data na list v následujícím pořadí:<br> oteví<PERSON><PERSON> cena, maxim<PERSON>lní cena, minim<PERSON>lní cena, uzavírací cena.", "SSE.Controllers.Main.errorToken": "Token zabezpečení dokumentu nemá správný formát.<br> Obraťte se na Vašeho správce dokumentového serveru.", "SSE.Controllers.Main.errorTokenExpire": "Platnost tokenu zabezpečení dokumentu skončila.<br>Obraťte se na správce vámi využívaného dokumentového serveru.", "SSE.Controllers.Main.errorUnexpectedGuid": "Externí chyba.<br>Neočekávané GUID. V případě, že chyba přetrvává, obraťte se na podporu.", "SSE.Controllers.Main.errorUpdateVersion": "Verze souboru byla změněna. Stránka bude znovu načtena.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Připojení k Internetu bylo obnoveno a verze souboru byla změněna.<br><PERSON><PERSON>ž budete moci pokračovat v práci, musíte stáhnout nebo zkopírovat obsah souboru, aby ne<PERSON><PERSON><PERSON> ke ztrátě dat. Poté tuto stránku obnovte.", "SSE.Controllers.Main.errorUserDrop": "<PERSON>to soubor nyní není př<PERSON>ý.", "SSE.Controllers.Main.errorUsersExceed": "Počet uživatelů pro daný tarif byl překročen", "SSE.Controllers.Main.errorViewerDisconnect": "Spojení bylo z<PERSON>. Dokument zůstává zobrazen,<br>ale do obnovení spojení (a znovunačtení stránky) ho není možné si stáhnout a ani vytisknout.", "SSE.Controllers.Main.errorWrongBracketsCount": "Chyba v zadaném vzorci.<br>Použit nesprávný počet závorek.", "SSE.Controllers.Main.errorWrongOperator": "Chyba v zadaném vzorci. Použit nesprávný operátor.<br>Prosím, opravte chybu.", "SSE.Controllers.Main.errorWrongPassword": "<PERSON><PERSON><PERSON><PERSON>.", "SSE.Controllers.Main.errRemDuplicates": "<PERSON><PERSON> na<PERSON> a smazány duplicitní hodnoty: {0}, zbývá neopakujících se hodnot: {1}.", "SSE.Controllers.Main.leavePageText": "V tomto sešitu máte neuložené změny. Pokud o ně nechcete přijít, klikněte na „Zůstat na této stránce“, poté na „Uložit“. V opačném případě klikněte na „Opustit tuto stránku“ a všechny neuložené změny budou zahozeny.", "SSE.Controllers.Main.leavePageTextOnClose": "Veškeré neuložené změny v tomto sešitu budou ztraceny.<br> Pokud je chcete uložit, klikněte na „Storno“ a poté na „Uložit“. Pokud chcete veškeré neuložené změny zahodit, klikněte na „OK“.", "SSE.Controllers.Main.loadFontsTextText": "Načítání dat...", "SSE.Controllers.Main.loadFontsTitleText": "Nač<PERSON><PERSON><PERSON><PERSON> dat", "SSE.Controllers.Main.loadFontTextText": "Načítání dat...", "SSE.Controllers.Main.loadFontTitleText": "Nač<PERSON><PERSON><PERSON><PERSON> dat", "SSE.Controllers.Main.loadImagesTextText": "Načítání obrázků...", "SSE.Controllers.Main.loadImagesTitleText": "Načítání obr<PERSON>ů", "SSE.Controllers.Main.loadImageTextText": "Načítání obrázku...", "SSE.Controllers.Main.loadImageTitleText": "Načítání obrázku", "SSE.Controllers.Main.loadingDocumentTitleText": "Načítán<PERSON> se<PERSON>itu", "SSE.Controllers.Main.notcriticalErrorTitle": "Varování", "SSE.Controllers.Main.openErrorText": "Při otevírání souboru došlo k chybě.", "SSE.Controllers.Main.openTextText": "Otevírán<PERSON> sešitu...", "SSE.Controllers.Main.openTitleText": "Oteví<PERSON><PERSON><PERSON> se<PERSON>", "SSE.Controllers.Main.pastInMergeAreaError": "Nelze změnit část sloučené buňky", "SSE.Controllers.Main.printTextText": "Tisk sešitu...", "SSE.Controllers.Main.printTitleText": "Tisk sešitu", "SSE.Controllers.Main.reloadButtonText": "Načíst stránku znovu", "SSE.Controllers.Main.requestEditFailedMessageText": "Tento dokument měkdo právě upravuje. Prosím zkuste to znovu později.", "SSE.Controllers.Main.requestEditFailedTitleText": "Přístup odepřen", "SSE.Controllers.Main.saveErrorText": "Při ukládání souboru došlo k chybě.", "SSE.Controllers.Main.saveErrorTextDesktop": "<PERSON><PERSON><PERSON> ne<PERSON><PERSON> být uložen nebo vytvořen.<br><PERSON><PERSON><PERSON><PERSON>: <br>1. <PERSON><PERSON><PERSON> je pouze pro čtení. <br>2. <PERSON><PERSON>or je editován jinými u<PERSON>. <br>3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> je plně zaplněno nebo poš<PERSON>o.", "SSE.Controllers.Main.saveTextText": "Ukládání sešitu...", "SSE.Controllers.Main.saveTitleText": "Ukládán<PERSON> sešitu", "SSE.Controllers.Main.scriptLoadError": "Připojení je p<PERSON><PERSON>, některé součás<PERSON> se nepodařilo na<PERSON>. Načtěte stránku znovu.", "SSE.Controllers.Main.textAnonymous": "Anonymní", "SSE.Controllers.Main.textApplyAll": "Uplatnit na všechny rovnice", "SSE.Controllers.Main.textBuyNow": "Navštívit webovou stránku", "SSE.Controllers.Main.textChangesSaved": "Všechny změny uloženy", "SSE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textCloseTip": "Tip zavřete kliknutím", "SSE.Controllers.Main.textConfirm": "Potvrzení", "SSE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "SSE.Controllers.Main.textContactUs": "Obraťte se na obchodní oddělení", "SSE.Controllers.Main.textContinue": "Po<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textConvertEquation": "Tato rovnice byla vytvořena starou verzí editoru rovnic, k<PERSON><PERSON> už není podporovaná. Pro jej<PERSON> upraven<PERSON>, převeďte rovnici do formátu Office Math ML.<br>Převést nyní?", "SSE.Controllers.Main.textCustomLoader": "Mějte na paměti, že dle podmínek licence nejste oprávněni měnit zavaděč.<br>Pro získání nabídky se obraťte na naše obchodní oddělení.", "SSE.Controllers.Main.textDisconnect": "Spojení je ztraceno", "SSE.Controllers.Main.textFillOtherRows": "Vyplnit ostatní <PERSON>", "SSE.Controllers.Main.textFormulaFilledAllRows": "Vzorec zadaný v {0} řádcích obsahuje data. Doplnění dat do ostatních řád<PERSON>ů, může trvat několik minut.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Vzorec je zadán zadaný prvních {0} řádcích. Doplnění dat do ostatních <PERSON>, může trvat několik minut.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Vzorec je zadaný pouze v prvních {0} ř<PERSON>dcích, z důvodu ukládání do paměti. Dalších {1} řádků v tomto listu neobsahuje data. Data můžete zapsat manuálně.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Vzorec je zadaný pouze v prvních {0} řádcích, z důvodu ukládání do paměti. Ostatní řádky v tomto listu neobsahují data.", "SSE.Controllers.Main.textGuest": "Návštěvník", "SSE.Controllers.Main.textHasMacros": "<PERSON><PERSON><PERSON> obsahu<PERSON> makra.<br>Opravdu chcete makra spustit?", "SSE.Controllers.Main.textKeep": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textLearnMore": "Více informací", "SSE.Controllers.Main.textLoadingDocument": "Načítán<PERSON> se<PERSON>itu", "SSE.Controllers.Main.textLongName": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> má méně než 128 znaků.", "SSE.Controllers.Main.textNeedSynchronize": "Máte nové aktualizace", "SSE.Controllers.Main.textNo": "Ne", "SSE.Controllers.Main.textNoLicenseTitle": "Došlo k dosažení limitu licence", "SSE.Controllers.Main.textPaidFeature": "Placená funkce", "SSE.Controllers.Main.textPleaseWait": "Operace může trvat déle, než se předpokládalo. Prosím čekejte…", "SSE.Controllers.Main.textReconnect": "Spojení je obnovené", "SSE.Controllers.Main.textRemember": "Zapamatovat si mou volbu pro všechny soubory", "SSE.Controllers.Main.textRememberMacros": "Zapamatovat moji volbu pro všechna makra", "SSE.Controllers.Main.textRenameError": "Jméno uživatele nesmí být prázdné. ", "SSE.Controllers.Main.textRenameLabel": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> bude použ<PERSON> pro spolupráci", "SSE.Controllers.Main.textReplace": "Nahradit", "SSE.Controllers.Main.textRequestMacros": "Makro vytváří požadavek na URL. Chcete povolit požadavek k přístupu na %1?", "SSE.Controllers.Main.textShape": "O<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textStrict": "Statický režim", "SSE.Controllers.Main.textText": "Text", "SSE.Controllers.Main.textTryQuickPrint": "Vybrali jste rychlý tisk: celý dokument bude vytisknut na vybrané, nebo výchozí tiskárně.<br>Opravdu chcete pokračovat? ", "SSE.Controllers.Main.textTryUndoRedo": "Funkce zpět/znovu nejsou v režimu rychlé spolupráce na úpravách k dispozici.<br>Kliknutím na tlačítko „Striktní režim“ přejdete do striktního režimu spolupráce na úpravách, ve kterém soubor upravujte bez vyrušování ostatními uživateli a vámi provedené změny odesíláte pouze po jejich uložení. Mezi oběma režimy spolupráce na úpravách je možné přepínat v pokročilých nastaveních editoru.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Funkce Zpět/Znovu jsou vypnuty pro rychlý rež<PERSON> s<PERSON>.", "SSE.Controllers.Main.textUndo": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "SSE.Controllers.Main.textUpdating": "Updating", "SSE.Controllers.Main.textYes": "<PERSON><PERSON>", "SSE.Controllers.Main.titleLicenseExp": "Platnost licence skončila", "SSE.Controllers.Main.titleLicenseNotActive": "Licence není aktivní", "SSE.Controllers.Main.titleServerVersion": "Editor by<PERSON> <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.titleUpdateVersion": "Verze změněna", "SSE.Controllers.Main.txtAccent": "Zvýraznění", "SSE.Controllers.Main.txtAll": "(vše)", "SSE.Controllers.Main.txtArt": "Zde napište text", "SSE.Controllers.Main.txtBasicShapes": "Základní obrazce", "SSE.Controllers.Main.txtBlank": "(p<PERSON><PERSON><PERSON><PERSON>ý)", "SSE.Controllers.Main.txtButtons": "Tlačítka", "SSE.Controllers.Main.txtByField": "\n%1 z %2", "SSE.Controllers.Main.txtCallouts": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtCharts": "<PERSON><PERSON>", "SSE.Controllers.Main.txtClearFilter": "Vyčistit filtr", "SSE.Controllers.Main.txtColLbls": "Štítky buňky", "SSE.Controllers.Main.txtColumn": "Sloupec", "SSE.Controllers.Main.txtConfidential": "Důvěrné", "SSE.Controllers.Main.txtDate": "Datum", "SSE.Controllers.Main.txtDays": "Dny", "SSE.Controllers.Main.txtDiagramTitle": "Název grafu", "SSE.Controllers.Main.txtEditingMode": "Na<PERSON><PERSON><PERSON>…", "SSE.Controllers.Main.txtErrorLoadHistory": "Načítání historie se nezdařilo", "SSE.Controllers.Main.txtFiguredArrows": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtFile": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtGrandTotal": "Celkový součet", "SSE.Controllers.Main.txtGroup": "Seskupení", "SSE.Controllers.Main.txtHours": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtInfo": "Informace", "SSE.Controllers.Main.txtLines": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMath": "Matematika", "SSE.Controllers.Main.txtMinutes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMonths": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMultiSelect": "Vícenásobný výběr", "SSE.Controllers.Main.txtNone": "Není", "SSE.Controllers.Main.txtOr": "%1 nebo %2", "SSE.Controllers.Main.txtPage": "Strán<PERSON>", "SSE.Controllers.Main.txtPageOf": "Stránka %1 z %2", "SSE.Controllers.Main.txtPages": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPicture": "Obrázek", "SSE.Controllers.Main.txtPivotTable": "Kontingenční tabulka", "SSE.Controllers.Main.txtPreparedBy": "Připravil(a)", "SSE.Controllers.Main.txtPrintArea": "Oblast_tisku", "SSE.Controllers.Main.txtQuarter": "Čtvrtiny", "SSE.Controllers.Main.txtQuarters": "Čtvrtiny", "SSE.Controllers.Main.txtRectangles": "Obdélníky", "SSE.Controllers.Main.txtRow": "Řádek", "SSE.Controllers.Main.txtRowLbls": "Štíky řádku", "SSE.Controllers.Main.txtSaveCopyAsComplete": "<PERSON><PERSON> souboru byla úspěšně ul<PERSON>", "SSE.Controllers.Main.txtScheme_Aspect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Blue": "Modrá", "SSE.Controllers.Main.txtScheme_Blue_Green": "Modro-zelená", "SSE.Controllers.Main.txtScheme_Blue_II": "Modrá II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "Teplá modrá", "SSE.Controllers.Main.txtScheme_Grayscale": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Green": "Zelená", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Zeleno-žlutá", "SSE.Controllers.Main.txtScheme_Marquee": "Rolovatelný text", "SSE.Controllers.Main.txtScheme_Median": "Medián", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Orange_Red": "Oranžovo-červená", "SSE.Controllers.Main.txtScheme_Paper": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Red": "Červená", "SSE.Controllers.Main.txtScheme_Red_Orange": "Červeno-oranžová", "SSE.Controllers.Main.txtScheme_Red_Violet": "Červeno-fialková", "SSE.Controllers.Main.txtScheme_Slipstream": "Proudění vzduchu", "SSE.Controllers.Main.txtScheme_Violet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Violet_II": "Fialková II", "SSE.Controllers.Main.txtScheme_Yellow": "Žlutá", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "Žluto-oran<PERSON>ová", "SSE.Controllers.Main.txtSeconds": "Sekundy", "SSE.Controllers.Main.txtSeries": "Řady", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Bublina s čárou 1 (ohraničení a zvýraznění)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Bublina s čárou 2 (ohraničení a zvýraznění)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Bublina s čárou 3 (ohraničení a zvýraznění)", "SSE.Controllers.Main.txtShape_accentCallout1": "Bublina s čárou 1 (zvýraznění)", "SSE.Controllers.Main.txtShape_accentCallout2": "Bublina s čárou 2 (zvýraznění)", "SSE.Controllers.Main.txtShape_accentCallout3": "Bublina s čárou 3 (zvýraznění)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Tlačí<PERSON><PERSON>t nebo Předchozí", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Tlačítko <PERSON>", "SSE.Controllers.Main.txtShape_actionButtonBlank": "<PERSON>r<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Tlačítko Dokument", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Tlačí<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "T<PERSON>č<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonHelp": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonHome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Tlačítko informace", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Tlačítko Video", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Tlačítko <PERSON>v<PERSON>", "SSE.Controllers.Main.txtShape_actionButtonSound": "Tlačítko Zvuk", "SSE.Controllers.Main.txtShape_arc": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "<PERSON><PERSON><PERSON> propo<PERSON>a se <PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "<PERSON><PERSON><PERSON> propojka se dv<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_bentUpArrow": "Šipka ohnut<PERSON> nahoru", "SSE.Controllers.Main.txtShape_bevel": "Zkosení", "SSE.Controllers.Main.txtShape_blockArc": "Blokový oblouk", "SSE.Controllers.Main.txtShape_borderCallout1": "Bublina s čárou 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Bublina s čárou 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Bublina s čárou 3", "SSE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_callout1": "Bublina s čárou 1 (bez ohraničení)", "SSE.Controllers.Main.txtShape_callout2": "Bublina s čárou 2 (bez ohraničení)", "SSE.Controllers.Main.txtShape_callout3": "Bublina s čárou 3 (bez ohraničení)", "SSE.Controllers.Main.txtShape_can": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_chevron": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_chord": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_circularArrow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cloud": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cloudCallout": "Bublina-mrak", "SSE.Controllers.Main.txtShape_corner": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Zakřivená propojka", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Zakřivená propojka se š<PERSON>u", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Zakřivená propojka se dv<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Zakřivená šipka dolů", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Zakřivená šipka vlevo", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Zakřivená šipka vpravo", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Zakřivená šipka nahoru", "SSE.Controllers.Main.txtShape_decagon": "Desetiúhelník", "SSE.Controllers.Main.txtShape_diagStripe": "Proužek po úhlopříčce", "SSE.Controllers.Main.txtShape_diamond": "Kosodélník", "SSE.Controllers.Main.txtShape_dodecagon": "Dvanáctiúhelník", "SSE.Controllers.Main.txtShape_donut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_doubleWave": "Dvojitá vlnovka", "SSE.Controllers.Main.txtShape_downArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_downArrowCallout": "<PERSON><PERSON><PERSON> se <PERSON> do<PERSON>", "SSE.Controllers.Main.txtShape_ellipse": "Elipsa", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Zakřivená stuha dolů", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Zakřivená stuha nahoru", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Diagram: alternativní proces", "SSE.Controllers.Main.txtShape_flowChartCollate": "Diagram: kompletovat", "SSE.Controllers.Main.txtShape_flowChartConnector": "Diagram: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDecision": "Diagram: roz<PERSON>dnutí", "SSE.Controllers.Main.txtShape_flowChartDelay": "Diagram: pro<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Diagram: o<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDocument": "Diagram: dokument", "SSE.Controllers.Main.txtShape_flowChartExtract": "Diagram: r<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Diagram: data", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Diagram: <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Diagram: magnetický disk", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Diagram: <PERSON><PERSON><PERSON><PERSON>št<PERSON> s přímým přístupem", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Diagram: <PERSON><PERSON><PERSON><PERSON>št<PERSON> se sekvenčním přístupem", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Diagram: ru<PERSON><PERSON><PERSON> vstup", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Diagram: ruční operace", "SSE.Controllers.Main.txtShape_flowChartMerge": "Diagram: s<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Diagram: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Diagram: prop<PERSON><PERSON>a mimo str<PERSON>", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Diagram: uložená data", "SSE.Controllers.Main.txtShape_flowChartOr": "Diagram: nebo", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Diagram: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> proces", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Diagram: <PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartProcess": "Diagram: proces", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Diagram: karta", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Diagram: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartSort": "Diagram: řazení", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Diagram: křižovatka sčítání", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Diagram: ukončení", "SSE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON><PERSON><PERSON><PERSON> roh", "SSE.Controllers.Main.txtShape_frame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heart": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heptagon": "Sed<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_hexagon": "Šestiúhelník", "SSE.Controllers.Main.txtShape_homePlate": "Pě<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_horizontalScroll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_irregularSeal1": "Výbuch 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Výbuch 2", "SSE.Controllers.Main.txtShape_leftArrow": "Šipka vlevo", "SSE.Controllers.Main.txtShape_leftArrowCallout": "<PERSON><PERSON><PERSON> se <PERSON> vlevo", "SSE.Controllers.Main.txtShape_leftBrace": "Levá složená závorka", "SSE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftRightArrow": "Šipka vlevo a vpravo", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "<PERSON><PERSON><PERSON> se šipkou vpravo a vlevo", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Šip<PERSON> vlevo, vpravo a nahoru", "SSE.Controllers.Main.txtShape_leftUpArrow": "Šipka vlevo a nahoru", "SSE.Controllers.Main.txtShape_lightningBolt": "Blesk", "SSE.Controllers.Main.txtShape_line": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "Šipka", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathDivide": "Dělení", "SSE.Controllers.Main.txtShape_mathEqual": "Rovná se", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Násobit", "SSE.Controllers.Main.txtShape_mathNotEqual": "Nerov<PERSON> se", "SSE.Controllers.Main.txtShape_mathPlus": "Plus", "SSE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_noSmoking": "Symbol „Ne“", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Šipka vpravo se zářezem", "SSE.Controllers.Main.txtShape_octagon": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_parallelogram": "Rovnoběžník", "SSE.Controllers.Main.txtShape_pentagon": "Pě<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_pie": "Kruhová výseč", "SSE.Controllers.Main.txtShape_plaque": "Podpis", "SSE.Controllers.Main.txtShape_plus": "Plus", "SSE.Controllers.Main.txtShape_polyline1": "<PERSON><PERSON> ruky", "SSE.Controllers.Main.txtShape_polyline2": "Volná forma", "SSE.Controllers.Main.txtShape_quadArrow": "Šipka do čtyř stran", "SSE.Controllers.Main.txtShape_quadArrowCallout": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_rect": "Obdélník", "SSE.Controllers.Main.txtShape_ribbon": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ribbon2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_rightArrow": "Šipka vpravo", "SSE.Controllers.Main.txtShape_rightArrowCallout": "<PERSON><PERSON><PERSON> se šip<PERSON>u vpravo", "SSE.Controllers.Main.txtShape_rightBrace": "Pravá složená závorka", "SSE.Controllers.Main.txtShape_rightBracket": "Pravá závorka", "SSE.Controllers.Main.txtShape_round1Rect": "Obdélník s jedn<PERSON><PERSON> zaobleným rohem", "SSE.Controllers.Main.txtShape_round2DiagRect": "Obdélník se dvě<PERSON> protilehlými zaoblenými rohy", "SSE.Controllers.Main.txtShape_round2SameRect": "Obdélník se dvěma zaoblenými rohy na stejné straně", "SSE.Controllers.Main.txtShape_roundRect": "Obdélník se zaoblenými rohy", "SSE.Controllers.Main.txtShape_rtTriangle": "Pravoúhlý trojúhelník", "SSE.Controllers.Main.txtShape_smileyFace": "Usměvavý obličej", "SSE.Controllers.Main.txtShape_snip1Rect": "Obdélník s jedním ustřiženým rohem", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Obdélník se dvěma protilehlými ustřiženými rohy", "SSE.Controllers.Main.txtShape_snip2SameRect": "Obdélník se dvěma ustřiženými rohy na stejné straně", "SSE.Controllers.Main.txtShape_snipRoundRect": "Obdélník s jedním zaobleným a jedním ustřiženým rohem na stejné straně", "SSE.Controllers.Main.txtShape_spline": "Křivka", "SSE.Controllers.Main.txtShape_star10": "Hvězda s 10 paprsky", "SSE.Controllers.Main.txtShape_star12": "Hvězda s 12 paprsky", "SSE.Controllers.Main.txtShape_star16": "Hvězda s 16 paprsky", "SSE.Controllers.Main.txtShape_star24": "Hvězda se 24 paprsky", "SSE.Controllers.Main.txtShape_star32": "Hvězda se 32 paprsky", "SSE.Controllers.Main.txtShape_star4": "Hvězda se 4 paprsky", "SSE.Controllers.Main.txtShape_star5": "Hvězda s 5 paprsky", "SSE.Controllers.Main.txtShape_star6": "Hvězda se 6 paprsky", "SSE.Controllers.Main.txtShape_star7": "Hvězda se 7 paprsky", "SSE.Controllers.Main.txtShape_star8": "Hvězda s 8 paprsky", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Proužkovaná šipka vpravo", "SSE.Controllers.Main.txtShape_sun": "Slunce", "SSE.Controllers.Main.txtShape_teardrop": "Slza", "SSE.Controllers.Main.txtShape_textRect": "<PERSON><PERSON><PERSON> pole", "SSE.Controllers.Main.txtShape_trapezoid": "Lichoběžník", "SSE.Controllers.Main.txtShape_triangle": "Trojúhelník", "SSE.Controllers.Main.txtShape_upArrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_upArrowCallout": "<PERSON><PERSON><PERSON> se <PERSON> na<PERSON>u", "SSE.Controllers.Main.txtShape_upDownArrow": "Šipka nahoru a dolů", "SSE.Controllers.Main.txtShape_uturnArrow": "Šipka s otočkou", "SSE.Controllers.Main.txtShape_verticalScroll": "Svislé posouvání", "SSE.Controllers.Main.txtShape_wave": "Vlnovka", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "<PERSON>v<PERSON><PERSON><PERSON> bublina", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bublina", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "<PERSON><PERSON><PERSON> ve tvaru obdélníku se zaoblenými rohy", "SSE.Controllers.Main.txtSheet": "List", "SSE.Controllers.Main.txtSlicer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStarsRibbons": "Hvě<PERSON><PERSON> a stuhy", "SSE.Controllers.Main.txtStyle_Bad": "Špatný", "SSE.Controllers.Main.txtStyle_Calculation": "Přepočítání", "SSE.Controllers.Main.txtStyle_Check_Cell": "Zkontrolovat buňku", "SSE.Controllers.Main.txtStyle_Comma": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "Měna", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Vysvětlující text", "SSE.Controllers.Main.txtStyle_Good": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Heading_1": "Nadpis 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Nadpis 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Nadpis 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Nadpis 4", "SSE.Controllers.Main.txtStyle_Input": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Spojená buňka", "SSE.Controllers.Main.txtStyle_Neutral": "Neutrální", "SSE.Controllers.Main.txtStyle_Normal": "Normální", "SSE.Controllers.Main.txtStyle_Note": "Poznámka", "SSE.Controllers.Main.txtStyle_Output": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Percent": "Procento", "SSE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Total": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Warning_Text": "Varovný text", "SSE.Controllers.Main.txtTab": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtTable": "Tabulka", "SSE.Controllers.Main.txtTime": "Čas", "SSE.Controllers.Main.txtUnlock": "Odemknout", "SSE.Controllers.Main.txtUnlockRange": "Odemknout rozsah", "SSE.Controllers.Main.txtUnlockRangeDescription": "Pokud chcete tento rozsah změnit, zadej<PERSON> he<PERSON>:", "SSE.Controllers.Main.txtUnlockRangeWarning": "Oblast, kterou se pokoušíte upravit je zabezpečena heslem.", "SSE.Controllers.Main.txtValues": "Hodnoty", "SSE.Controllers.Main.txtView": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtXAxis": "Osa X", "SSE.Controllers.Main.txtYAxis": "<PERSON><PERSON>", "SSE.Controllers.Main.txtYears": "Roky", "SSE.Controllers.Main.unknownErrorText": "Neznámá chyba.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON><PERSON><PERSON>ý webový prohlížeč není podporován.", "SSE.Controllers.Main.uploadDocExtMessage": "Neznámý formát dokumentu.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Nebyly nahrány dokumenty.", "SSE.Controllers.Main.uploadDocSizeMessage": "Překročena maximální velikost dokumentu.", "SSE.Controllers.Main.uploadImageExtMessage": "Neznámý formát obrázku.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Nenahr<PERSON><PERSON> o<PERSON>.", "SSE.Controllers.Main.uploadImageSizeMessage": "Obrázek je p<PERSON><PERSON><PERSON><PERSON> velk<PERSON>. Maximální velikost je 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "Nahrávání obrázku...", "SSE.Controllers.Main.uploadImageTitleText": "Nahrávání obrázku", "SSE.Controllers.Main.waitText": "Čekejte prosím…", "SSE.Controllers.Main.warnBrowserIE9": "Aplikace nemůže v IE9 fungovat správně. Použijte IE10 a novější", "SSE.Controllers.Main.warnBrowserZoom": "Aktuální přiblížení prohlížeče není plně podporováno. Obnovte prosím původní přiblížení stiskem CTRL+0.", "SSE.Controllers.Main.warnLicenseAnonymous": "Přístup zamítnut pro anonymní uživatele.<br>Tento dokument bude zobrazen pouze jako n<PERSON>.", "SSE.Controllers.Main.warnLicenseBefore": "Licence není aktivní.<br>Prosím kontaktujte svého správce.", "SSE.Controllers.Main.warnLicenseExceeded": "Došlo k dosažení limitu počtu souběžných spojení %1 editor<PERSON><PERSON> Dokument bude otevřen pouze pro náhled.<br>Pro více podrobností kontaktujte svého správce.", "SSE.Controllers.Main.warnLicenseExp": "Platnost vaší licence skončila.<br>Obnovte si svou licenci a načtěte stránku znovu.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Platnost vaší licence skončila.<br>Nemáte přístup k upravování dokumentů.<br>Obraťte se na svého správce.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Vaši licenci je nutné obnovit.<br>Přístup k možnostem editace dokumentu je omezen.<br>Pro získání plného přístupu prosím kontaktujte svého administrátora.", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Došlo dosažení limitu %1 editorů v režimu spolupráce na úpravách. Ohledně podrobností se obraťte na svého správce.", "SSE.Controllers.Main.warnNoLicense": "<PERSON><PERSON><PERSON>žení limitu souběžných připojení %1 editor<PERSON>. Dokument bude otevřen pouze pro náhled.<br>Pro rozšíření funkcí kontaktujte %1 obchodní oddělení.", "SSE.Controllers.Main.warnNoLicenseUsers": "Došlo k dosažení limitu %1 editor<PERSON>. Pro rozšíření funkcí kontaktujte %1 obchodní oddělení.", "SSE.Controllers.Main.warnProcessRightsChange": "<PERSON><PERSON> vám odepřeno oprávnění soubor upravovat.", "SSE.Controllers.PivotTable.strSheet": "List", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "No actions with calculated items are allowed for this active cell.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Calculated items do not work with custom subtotals.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "An item name cannot be found. Check that you've typed name correctly and the item is present in the PivotTable report.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Averages, standard deviations, and variances are not supported when a PivotTable report has calculated items.", "SSE.Controllers.Print.strAllSheets": "Všechny listy", "SSE.Controllers.Print.textFirstCol": "První sloupec", "SSE.Controllers.Print.textFirstRow": "Prvn<PERSON>", "SSE.Controllers.Print.textFrozenCols": "<PERSON><PERSON><PERSON><PERSON> slou<PERSON>", "SSE.Controllers.Print.textFrozenRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Controllers.Print.textNoRepeat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textRepeat": "Opakovat...", "SSE.Controllers.Print.textSelectRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.txtCustom": "Vlastní", "SSE.Controllers.Print.txtZoomToPage": "Přizpůsobit stránce", "SSE.Controllers.Search.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Controllers.Search.textNoTextFound": "<PERSON>, <PERSON><PERSON><PERSON> h<PERSON>, <PERSON><PERSON><PERSON>. Upravte parametry vyhledávání.", "SSE.Controllers.Search.textReplaceSkipped": "Nahrazení bylo <PERSON>. {0} v<PERSON>skyt<PERSON> bylo p<PERSON>.", "SSE.Controllers.Search.textReplaceSuccess": "Hledání bylo <PERSON>eno. <PERSON><PERSON><PERSON> {0} nahrazení", "SSE.Controllers.Statusbar.errorLastSheet": "Sešit musí mít alespoň jeden viditelný list", "SSE.Controllers.Statusbar.errorRemoveSheet": "List se neda<PERSON><PERSON> s<PERSON>t.", "SSE.Controllers.Statusbar.strSheet": "List", "SSE.Controllers.Statusbar.textDisconnect": "<b>Spojení je z<PERSON></b><br>Pokus o opětovné připojení. Zkontrolujte nastavení připojení.", "SSE.Controllers.Statusbar.textSheetViewTip": "Jste v režimu náhledu listu. Filtry a řazení je viditelné pouze pro Vás a uživatele v tomto náhledu. ", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Jste v režimu náhledu listu. Filtry jsou viditelné pouze pro Vás a uživatele v tomto náhledu. ", "SSE.Controllers.Statusbar.warnDeleteSheet": "List může obsahovat data. Opravdu chcete pokračovat?", "SSE.Controllers.Statusbar.zoomText": "Přiblížení {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Písmo (font) ve kterém se chystáte uložit, není na tomto zařízení k dispozici.<br>Text bude zobrazen pomocí některého ze systémových písem s tím, že uložené písmo bude použito v případě budoucí dostupnosti.<br>Chcete pokračovat?", "SSE.Controllers.Toolbar.errorComboSeries": "Pro vytvoření kombino<PERSON>ho grafu, zvolte alespoň dvě skupiny dat. ", "SSE.Controllers.Toolbar.errorMaxPoints": "Nejvyšší možný počet bodů v řadě na graf je 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "CHYBA! Nejvyšší možný počet datových řad v každém grafu je 255", "SSE.Controllers.Toolbar.errorStockChart": "Nesprávné pořadí řádků. Pro vytvoření burzovního grafu umístěte data na list v následujícím pořadí:<br> oteví<PERSON><PERSON> cena, maxim<PERSON>lní cena, minim<PERSON>lní cena, uzavírací cena.", "SSE.Controllers.Toolbar.helpCalcItems": "Work with calculated items in Pivot Tables.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Calculated items", "SSE.Controllers.Toolbar.helpFastUndo": "Easily undo changes while collaborating on sheets in Fast mode.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "\"Undo\" in real-time co-editing", "SSE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "SSE.Controllers.Toolbar.textAccent": "Zvýraznění", "SSE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textDirectional": "Směrové", "SSE.Controllers.Toolbar.textFontSizeErr": "Zadaná hodnota není správná.<br>Zadejte hodnotu v rozmezí 1 až 409", "SSE.Controllers.Toolbar.textFraction": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textFunction": "Funkce", "SSE.Controllers.Toolbar.textIndicator": "Indikátory", "SSE.Controllers.Toolbar.textInsert": "Vložit", "SSE.Controllers.Toolbar.textIntegral": "Integ<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textLargeOperator": "Velké operátory", "SSE.Controllers.Toolbar.textLimitAndLog": "Limity a logaritmy", "SSE.Controllers.Toolbar.textLongOperation": "Dlouhá operace", "SSE.Controllers.Toolbar.textMatrix": "Mat<PERSON>", "SSE.Controllers.Toolbar.textOperator": "Operátory", "SSE.Controllers.Toolbar.textPivot": "Kontingenční tabulka", "SSE.Controllers.Toolbar.textRadical": "Odmo<PERSON>niny", "SSE.Controllers.Toolbar.textRating": "Hodnocení", "SSE.Controllers.Toolbar.textRecentlyUsed": "Nedávno p<PERSON>žité", "SSE.Controllers.Toolbar.textScript": "Skripty", "SSE.Controllers.Toolbar.textShapes": "Obrazce", "SSE.Controllers.Toolbar.textSymbols": "Symboly", "SSE.Controllers.Toolbar.textWarning": "Varování", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Šipka vpravo-vlevo nad", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Šipka vlevo nad", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Šipka vpravo nad", "SSE.Controllers.Toolbar.txtAccent_Bar": "Vodorovná čárka", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Čára pod", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Čára nad", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Vzorec v rámečku (s výplňovým objektem)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Vzorec v rámečku (příklad)", "SSE.Controllers.Toolbar.txtAccent_Check": "Kontrola", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Dolní složená závorka", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Horní složená závorka ", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vektor A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC s čárou nad", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y s čárou nad", "SSE.Controllers.Toolbar.txtAccent_DDDot": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_DDot": "Dvojtečka", "SSE.Controllers.Toolbar.txtAccent_Dot": "Tečka", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Dvojitá vodorovná čárka", "SSE.Controllers.Toolbar.txtAccent_Grave": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Seskupení znaků pod", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Seskupení znaků nad", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Harpuna vlevo nad", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Harpuna vpravo nad", "SSE.Controllers.Toolbar.txtAccent_Hat": "Stříška", "SSE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Tilda", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "<PERSON><PERSON><PERSON> s oddělovačem", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "<PERSON><PERSON><PERSON> se dvě<PERSON> odd<PERSON>lov<PERSON>č<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "pravá lomená závorka", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "<PERSON><PERSON> lo<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Složené závorky s oddělovačem", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Pravá složená závorka", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Levá složená závorka", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "<PERSON><PERSON><PERSON><PERSON><PERSON> (dv<PERSON>ínky)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "<PERSON><PERSON><PERSON><PERSON><PERSON> (t<PERSON>i <PERSON>ínky)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Složený objekt", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Složený objekt z závorkách", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Ukázka případů", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Kombinační <PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Kombinační číslo v lomených závorkách", "SSE.Controllers.Toolbar.txtBracket_Line": "Svislá <PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Závorka svislá s navazující odrážkou vpravo", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Závorka svislá s navazující odrážkou vlevo", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "D<PERSON>jitá s<PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Závorka svislá s dvojitou navazující odrážkou vpravo", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Závorka svislá s dvojitou navazující odrážkou vlevo", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Dolní celá <PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Pravá dolní <PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Dolní celá č<PERSON>t - <PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Závorky s oddělovačem", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Pravá závorka", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Výplňový objekt mezi dvěma pravými hranatými závorkami", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Invert<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Pravá hranatá závorka", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "<PERSON><PERSON> h<PERSON> zá<PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Výplňový objekt mezi dvěma levými hranatými závorkami", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Pravá dvojitá hranatá závorka", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "<PERSON><PERSON> d<PERSON> hranatá závorka", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Horní celá <PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Horní celá část - Pravá č<PERSON>t", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Horní celá č<PERSON>t - <PERSON><PERSON>", "SSE.Controllers.Toolbar.txtDeleteCells": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtExpand": "Rozbalit a seřadit", "SSE.Controllers.Toolbar.txtExpandSort": "Data vedle výběru nebudou seřazena. Chcete rozšířit výběr tak, aby zahrnoval sousední data, nebo pokračovat v seřazení pouze vybraných buněk?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Zkosený zlomek", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "dx/dy", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Δy/Δx", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Parciální derivace y děleno parciální derivace x", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "δy/δx", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Lineární zlomek", "SSE.Controllers.Toolbar.txtFractionPi_2": "<PERSON><PERSON> lomeno dv<PERSON>ma", "SSE.Controllers.Toolbar.txtFractionSmall": "Malý zlo<PERSON>k", "SSE.Controllers.Toolbar.txtFractionVertical": "Složený zlomek", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Inverzní funk<PERSON> kos<PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Inverzní funkce hyperbolický kosinus", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Inverzní funkce kotangens", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Inverzní funkce hyperbolický kotangens", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Inverzní funkce kosekans", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Inverzní funkce hyperbolický kosekans", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Inverzní <PERSON>ce sekans", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Inverzní funkce hyperbolický sekans", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Inverzní funkce sinus", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Inverzní funkce hyperbolický sinus", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Inverzní <PERSON>ce tangens", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Inverzní funkce hyperbolický tangens", "SSE.Controllers.Toolbar.txtFunction_Cos": "<PERSON><PERSON> cosinus", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Funkce hyperbolický kosinus", "SSE.Controllers.Toolbar.txtFunction_Cot": "<PERSON>ce kotangens", "SSE.Controllers.Toolbar.txtFunction_Coth": "Funkce Hyperbolický kotangens", "SSE.Controllers.Toolbar.txtFunction_Csc": "Funkce kosekans", "SSE.Controllers.Toolbar.txtFunction_Csch": "Funkce hyperbolický kosekans", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON><PERSON> théta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Vzorec tangens", "SSE.Controllers.Toolbar.txtFunction_Sec": "<PERSON><PERSON> se<PERSON>s", "SSE.Controllers.Toolbar.txtFunction_Sech": "Funkce hyperbolický sekans", "SSE.Controllers.Toolbar.txtFunction_Sin": "<PERSON>ce sinus", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Funkce hyperbolický sinus", "SSE.Controllers.Toolbar.txtFunction_Tan": "<PERSON>ce tangens", "SSE.Controllers.Toolbar.txtFunction_Tanh": "<PERSON>ce hyperbolický tangens", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Vlastní", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Data a model", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "<PERSON><PERSON><PERSON><PERSON>, špatný a neutrální", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "Bez jména", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "<PERSON><PERSON><PERSON><PERSON> styly bun<PERSON>k", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Nadpisy a záhlaví", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Vlastní", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Tmavé", "SSE.Controllers.Toolbar.txtGroupTable_Light": "Světl<PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Střední", "SSE.Controllers.Toolbar.txtInsertCells": "Vložit buňky", "SSE.Controllers.Toolbar.txtIntegral": "Integrál", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Diferenci<PERSON>l theta", "SSE.Controllers.Toolbar.txtIntegral_dx": "Diferenciál x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Diferenci<PERSON><PERSON> y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Urč<PERSON><PERSON> integrál", "SSE.Controllers.Toolbar.txtIntegralDouble": "Dvojný integrál", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Určitý dvojný integrál", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Určitý dvojný integrál", "SSE.Controllers.Toolbar.txtIntegralOriented": "Křivkový integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Určitý křivkový integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Plošný integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Určitý plošný integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Určitý plošný integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Určitý křivkový integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Prostorový integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Určitý prostorový integrál", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Určitý prostorový integrál", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Urč<PERSON><PERSON> integrál", "SSE.Controllers.Toolbar.txtIntegralTriple": "<PERSON><PERSON>jn<PERSON> integrál", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Určitý trojný integrál", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Určitý trojný integrál", "SSE.Controllers.Toolbar.txtInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Logic<PERSON><PERSON> sou<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Logický součin s <PERSON>y dole", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Logick<PERSON> součin s limity", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Logický součin s dolním indexem", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Logický součin s horním a dolním indexem", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s dolním limetem", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s <PERSON>y", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s dolním indexem", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s horním a dolním indexem", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Sumace od k pro N nebo K", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Sumace od i=0 do n", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Příklad součtu pomocí dvou indexů", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Sjednocení příklad", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Logický součet", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Logický součet s limity dole", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Logický součet s limity", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Logický součet s dolním indexem", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Logický součet s horním a dolním indexem", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Průnik", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Průnik s limitem dole", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Prů<PERSON> s limity", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Průnik s dolním indexem", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Průnik s horním a dolním indexem", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "<PERSON><PERSON><PERSON><PERSON> do<PERSON>em", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON> s <PERSON>y", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "So<PERSON><PERSON><PERSON> s dolním indexem", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "<PERSON><PERSON><PERSON><PERSON> s dolním a horním indexem", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Su<PERSON>ce", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Sumace s dolním indexem", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Su<PERSON>ce s limity", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Sumace s dolním indexem", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Sumace s horním a dolním indexem", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Sjednocení", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Sjednocení s limity dole", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Sjednocení s limity", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Sjednocení s dolním indexem", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Sjednocení s dolním a horním indexem", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Příklad limity", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Příklad maxima", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Přirozený logaritmus", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmus", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmus", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Maximum", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "SSE.Controllers.Toolbar.txtLockSort": "Poblíž Vašeho výběru existují data, nemáte však dostatečná oprávnění k úpravě těchto buněk.<br>Chcete pokračovat s aktuálním výběrem?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "1x2 Prázdná matice", "SSE.Controllers.Toolbar.txtMatrix_1_3": "1x3 Prázdná matice", "SSE.Controllers.Toolbar.txtMatrix_2_1": "2x1 Prázdná matice", "SSE.Controllers.Toolbar.txtMatrix_2_2": "2x2 Prázdná matice", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Prázdná 2 x 2 matice v dvojitých svislých čárách", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Prázdná matice se závorkami", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Prázdná 2 x 2 matice v závorkách", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Prázdná 2 x 2 matice v hranatých závorkách", "SSE.Controllers.Toolbar.txtMatrix_2_3": "2x3 Prázdná matice", "SSE.Controllers.Toolbar.txtMatrix_3_1": "3x1 Prázdná matice", "SSE.Controllers.Toolbar.txtMatrix_3_2": "3x2 Prázdná matice", "SSE.Controllers.Toolbar.txtMatrix_3_3": "3x3 Prázdná matice", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Tečky na řádku", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Tečky v řádku", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Úhlop<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Svislé tečky", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Řídká matice v závorkách", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Řídká matice v hranatých závorkách", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "2x2 jednotkov<PERSON> matice s nulami", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "2x2 jednotková matice v prázdnými nediagonálními buňkami", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "3x3 jednot<PERSON><PERSON> matice s nulami", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "3x3 jednotková matice v prázdnými nediagonálními buňkami", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Šipka vlevo-vpravo pod", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Šipka vpravo-vlevo nad", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Šipka vlevo pod", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Šipka vlevo nad", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Šipka vpravo pod", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Šipka vpravo nad", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Dvojtečka rovná se", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Vzniká", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Delta vzniká", "SSE.Controllers.Toolbar.txtOperator_Definition": "Rovná se podle definice", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta rovná se", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Dvojitá šipka vlevo-vpravo pod", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Dvojitá šipka vlevo-vpravo nad", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Šipka vlevo pod", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Šipka vlevo nad", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Šipka vpravo pod", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Šipka vpravo nad", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON><PERSON><PERSON> rov<PERSON> se", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON><PERSON> r<PERSON> se", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus rovná se", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "M<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Pravá část kvadratické rovnice", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Odmocnina z (druhá mocnina A plus druhá mocnina B)", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Odmocnina se stupněm", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Třetí odmocnina", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "n-tá odmocnina", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON><PERSON> od<PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_1": "X dolní index Y na druhou", "SSE.Controllers.Toolbar.txtScriptCustom_2": "e^-iωt", "SSE.Controllers.Toolbar.txtScriptCustom_3": "x na druhou", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Y kde levý horní index je n, levý dolní index je 1 ", "SSE.Controllers.Toolbar.txtScriptSub": "Dolní index", "SSE.Controllers.Toolbar.txtScriptSubSup": "Dolní-horní index", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Levý horní-dolní index", "SSE.Controllers.Toolbar.txtScriptSup": "Horní index", "SSE.Controllers.Toolbar.txtSorting": "Řazení", "SSE.Controllers.Toolbar.txtSortSelected": "Se<PERSON><PERSON><PERSON> v<PERSON>né", "SSE.Controllers.Toolbar.txtSymbol_about": "Přibližně", "SSE.Controllers.Toolbar.txtSymbol_additional": "Doplněk", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "Téměř se rovná", "SSE.Controllers.Toolbar.txtSymbol_ast": "<PERSON>er<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_bullet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cap": "Průnik", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Třetí odmocnina", "SSE.Controllers.Toolbar.txtSymbol_cdots": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> el<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_celsius": "Stupně Celsia", "SSE.Controllers.Toolbar.txtSymbol_chi": "Chí", "SSE.Controllers.Toolbar.txtSymbol_cong": "Přibližně se rovná", "SSE.Controllers.Toolbar.txtSymbol_cup": "Sjednocení", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Elipsy diagonálně do<PERSON>ů", "SSE.Controllers.Toolbar.txtSymbol_degree": "Stupně", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Znak <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Prázdná množina", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "Rovná se", "SSE.Controllers.Toolbar.txtSymbol_equiv": "<PERSON><PERSON><PERSON><PERSON> s", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "<PERSON>ist<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Faktor<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Stupně Fahrenheita", "SSE.Controllers.Toolbar.txtSymbol_forall": "Pro všechny", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gama", "SSE.Controllers.Toolbar.txtSymbol_geq": "Větší nebo rovno", "SSE.Controllers.Toolbar.txtSymbol_gg": "Mnohem větší než", "SSE.Controllers.Toolbar.txtSymbol_greater": "Větš<PERSON> než", "SSE.Controllers.Toolbar.txtSymbol_in": "Prvek náleží", "SSE.Controllers.Toolbar.txtSymbol_inc": "Přír<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Nekonečno", "SSE.Controllers.Toolbar.txtSymbol_iota": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Šipka vlevo", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Levá-p<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_leq": "Méně než nebo rovno", "SSE.Controllers.Toolbar.txtSymbol_less": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_ll": "Mnohem menší než", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "<PERSON><PERSON><PERSON> plus", "SSE.Controllers.Toolbar.txtSymbol_mu": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Symbol Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Nerov<PERSON> se", "SSE.Controllers.Toolbar.txtSymbol_ni": "<PERSON><PERSON><PERSON><PERSON> jako p<PERSON>k", "SSE.Controllers.Toolbar.txtSymbol_not": "Znak negace", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Neexistuje", "SSE.Controllers.Toolbar.txtSymbol_nu": "Ný", "SSE.Controllers.Toolbar.txtSymbol_o": "Omikron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Parciální diferenciál", "SSE.Controllers.Toolbar.txtSymbol_percent": "Procento", "SSE.Controllers.Toolbar.txtSymbol_phi": "Fí", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pí", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus mínus", "SSE.Controllers.Toolbar.txtSymbol_propto": "Úměrný k", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psí", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Čtvrtá odmocnina", "SSE.Controllers.Toolbar.txtSymbol_qed": "Znak Konec důkazu", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Elipsy diagonálně nahoru", "SSE.Controllers.Toolbar.txtSymbol_rho": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Šipka vpravo", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Znak odmocniny", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "A proto", "SSE.Controllers.Toolbar.txtSymbol_theta": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_times": "Znak násobení", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Ypsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Varianta epsilon", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Varianta Fí", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Varianta <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Varianta Sigma", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Svislé elipsy", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Ksí", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Tmavý styl tabulky", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Svě<PERSON>ý styl tabulky", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Střední styl tabulky ", "SSE.Controllers.Toolbar.warnLongOperation": "Dokončení operace, kterou se chyst<PERSON>te prov<PERSON>t, by m<PERSON><PERSON> trvat opravdu dlouho.<br>Opravdu chcete pokračovat?", "SSE.Controllers.Toolbar.warnMergeLostData": "Pouze data z levé horní buňky zůstanou ve sloučené buňce.<br>Opravdu chcete pokračovat?", "SSE.Controllers.Toolbar.warnNoRecommended": "Pro vytvoření grafu v<PERSON><PERSON>, <PERSON><PERSON><PERSON> o<PERSON> data, je<PERSON>cete použít.<br>Pokud máte názvy řádků a sloupců a chcete je použít jako pop<PERSON>, zahrňte je do výběru.", "SSE.Controllers.Viewport.textFreezePanes": "Ukotvit <PERSON>", "SSE.Controllers.Viewport.textFreezePanesShadow": "Zobrazit stín ukotvených příček", "SSE.Controllers.Viewport.textHideFBar": "Skrý<PERSON> ř<PERSON>dek v<PERSON>ů", "SSE.Controllers.Viewport.textHideGridlines": "Skrýt mřížku", "SSE.Controllers.Viewport.textHideHeadings": "Skr<PERSON><PERSON> z<PERSON>av<PERSON>", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Odd<PERSON>lov<PERSON><PERSON> desetinný<PERSON> m<PERSON>t", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Nastavení použitá pro rozpoznání číselných dat", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Textový kvalifikátor", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Pokročilá nastavení", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(ž<PERSON><PERSON><PERSON>)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Vlastn<PERSON> filtr", "SSE.Views.AutoFilterDialog.textAddSelection": "Přidat aktuální výběr k filtrování", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Blanks}", "SSE.Views.AutoFilterDialog.textSelectAll": "Vybrat vše", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Vybrat všechny výsledky hledání", "SSE.Views.AutoFilterDialog.textWarning": "Varování", "SSE.Views.AutoFilterDialog.txtAboveAve": "Nadprůměrný", "SSE.Views.AutoFilterDialog.txtAfter": "Po...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "Všechny data v období", "SSE.Views.AutoFilterDialog.txtApril": "duben", "SSE.Views.AutoFilterDialog.txtAugust": "srpen", "SSE.Views.AutoFilterDialog.txtBefore": "Před...", "SSE.Views.AutoFilterDialog.txtBegins": "Začíná na…", "SSE.Views.AutoFilterDialog.txtBelowAve": "Podprůměrný", "SSE.Views.AutoFilterDialog.txtBetween": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtClear": "Vyčistit", "SSE.Views.AutoFilterDialog.txtContains": "<PERSON><PERSON><PERSON><PERSON>…", "SSE.Views.AutoFilterDialog.txtDateFilter": "Datumový filtr", "SSE.Views.AutoFilterDialog.txtDecember": "prosinec", "SSE.Views.AutoFilterDialog.txtEmpty": "Zadejte filtr buněk", "SSE.Views.AutoFilterDialog.txtEnds": "Končí na…", "SSE.Views.AutoFilterDialog.txtEquals": "Rovná se…", "SSE.Views.AutoFilterDialog.txtFebruary": "únor", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filtrovat podle barvy buněk", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filtrovat podle barvy písma", "SSE.Views.AutoFilterDialog.txtGreater": "<PERSON><PERSON><PERSON><PERSON><PERSON> ne<PERSON>…", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Větší než nebo rovno…", "SSE.Views.AutoFilterDialog.txtJanuary": "leden", "SSE.Views.AutoFilterDialog.txtJuly": "červenec", "SSE.Views.AutoFilterDialog.txtJune": "červen", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Filtrování štítků", "SSE.Views.AutoFilterDialog.txtLastMonth": "Poslední <PERSON>", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Poslední kvartál", "SSE.Views.AutoFilterDialog.txtLastWeek": "Poslední týden", "SSE.Views.AutoFilterDialog.txtLastYear": "Poslední rok", "SSE.Views.AutoFilterDialog.txtLess": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtLessEquals": "<PERSON><PERSON>ě než nebo rovná se…", "SSE.Views.AutoFilterDialog.txtMarch": "b<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtMay": "k<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtNextMonth": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Další <PERSON>vrtletí", "SSE.Views.AutoFilterDialog.txtNextWeek": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtNextYear": "<PERSON>š<PERSON> rok", "SSE.Views.AutoFilterDialog.txtNotBegins": "Nezačíná na…", "SSE.Views.AutoFilterDialog.txtNotBetween": "Ne mezi...", "SSE.Views.AutoFilterDialog.txtNotContains": "<PERSON><PERSON><PERSON><PERSON>…", "SSE.Views.AutoFilterDialog.txtNotEnds": "Nekončí na…", "SSE.Views.AutoFilterDialog.txtNotEquals": "<PERSON><PERSON><PERSON> se…", "SSE.Views.AutoFilterDialog.txtNovember": "listopad", "SSE.Views.AutoFilterDialog.txtNumFilter": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtOctober": "říjen", "SSE.Views.AutoFilterDialog.txtQuarter1": "Kvartál 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Kvartál 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Kvartál 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Kvartál 1", "SSE.Views.AutoFilterDialog.txtReapply": "Použ<PERSON>t znov<PERSON>", "SSE.Views.AutoFilterDialog.txtSeptember": "září", "SSE.Views.AutoFilterDialog.txtSortCellColor": "<PERSON><PERSON><PERSON><PERSON> podle barvy buněk", "SSE.Views.AutoFilterDialog.txtSortFontColor": "<PERSON><PERSON><PERSON><PERSON> podle barvy p<PERSON>", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Seřadit od nejvyššího po nejnižší", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Seřadit od nejnižšího po nejvyšší", "SSE.Views.AutoFilterDialog.txtSortOption": "<PERSON><PERSON><PERSON> možnosti řazení…", "SSE.Views.AutoFilterDialog.txtTextFilter": "Filtr textu", "SSE.Views.AutoFilterDialog.txtThisMonth": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtThisQuarter": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtThisWeek": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtThisYear": "<PERSON>to rok", "SSE.Views.AutoFilterDialog.txtTitle": "Filtr", "SSE.Views.AutoFilterDialog.txtToday": "Dnes", "SSE.Views.AutoFilterDialog.txtTomorrow": "<PERSON>í<PERSON>", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Filtr hodnot", "SSE.Views.AutoFilterDialog.txtYearToDate": "Od začátku roku k aktuálnímu datu", "SSE.Views.AutoFilterDialog.txtYesterday": "Včera", "SSE.Views.AutoFilterDialog.warnFilterError": "Aby bylo mož<PERSON>é p<PERSON>t filtr hodnot, je třeba, aby v oblasti Hodnoty byla alespoň jedna hodnota.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Je třeba zvolit alespoň jednu hodnotu", "SSE.Views.CellEditor.textManager": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> názvů", "SSE.Views.CellEditor.tipFormula": "Vlož<PERSON>", "SSE.Views.CellRangeDialog.errorMaxRows": "CHYBA! Nejvyšší možný počet datových řad v každém grafu je 255", "SSE.Views.CellRangeDialog.errorStockChart": "Nesprávné pořadí řádků. Pro vytvoření burzovního grafu umístěte data na list v následujícím pořadí:<br> oteví<PERSON><PERSON> cena, maxim<PERSON>lní cena, minim<PERSON>lní cena, uzavírací cena.", "SSE.Views.CellRangeDialog.txtEmpty": "Toto pole je povinné", "SSE.Views.CellRangeDialog.txtInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.CellRangeDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON> rozsah dat", "SSE.Views.CellSettings.strShrink": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, aby se vešlo", "SSE.Views.CellSettings.strWrap": "Zalomit text", "SSE.Views.CellSettings.textAngle": "Úhel", "SSE.Views.CellSettings.textBackColor": "<PERSON>va p<PERSON>adí", "SSE.Views.CellSettings.textBackground": "<PERSON>va p<PERSON>adí", "SSE.Views.CellSettings.textBorderColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "<PERSON><PERSON>", "SSE.Views.CellSettings.textClearRule": "Vyčistit pravidla", "SSE.Views.CellSettings.textColor": "Vyplnit barvou", "SSE.Views.CellSettings.textColorScales": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textCondFormat": "Podmín<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textControl": "Kontrola textu", "SSE.Views.CellSettings.textDataBars": "Histogramy", "SSE.Views.CellSettings.textDirection": "Směr", "SSE.Views.CellSettings.textFill": "Výplň", "SSE.Views.CellSettings.textForeground": "<PERSON>va popředí", "SSE.Views.CellSettings.textGradient": "Stínování", "SSE.Views.CellSettings.textGradientColor": "<PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "Výplň přechodem", "SSE.Views.CellSettings.textIndent": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textItems": "Polož<PERSON>", "SSE.Views.CellSettings.textLinear": "Lineární", "SSE.Views.CellSettings.textManageRule": "Spravovat pravidla", "SSE.Views.CellSettings.textNewRule": "Nové pravidlo", "SSE.Views.CellSettings.textNoFill": "Bez výplně", "SSE.Views.CellSettings.textOrientation": "Orientace textu", "SSE.Views.CellSettings.textPattern": "Vzor", "SSE.Views.CellSettings.textPatternFill": "Vzor", "SSE.Views.CellSettings.textPosition": "Pozice", "SSE.Views.CellSettings.textRadial": "Paprskový", "SSE.Views.CellSettings.textSelectBorders": "<PERSON><PERSON><PERSON><PERSON>, na které chcete p<PERSON>žít výše zvolený styl.", "SSE.Views.CellSettings.textSelection": "<PERSON>d st<PERSON>jícího výběru", "SSE.Views.CellSettings.textThisPivot": "Z této kontingenční tabulky", "SSE.Views.CellSettings.textThisSheet": "Z tohoto listu", "SSE.Views.CellSettings.textThisTable": "Z této tabulky", "SSE.Views.CellSettings.tipAddGradientPoint": "P<PERSON><PERSON>t stínování", "SSE.Views.CellSettings.tipAll": "Nastavit vnější ohraničení a všechny vnitřní čáry", "SSE.Views.CellSettings.tipBottom": "Nastavit pouze vnější spodní ohraničení", "SSE.Views.CellSettings.tipDiagD": "Nastavit <PERSON>říčný okraj dolů", "SSE.Views.CellSettings.tipDiagU": "Nastavit <PERSON>říčný okraj nahoru", "SSE.Views.CellSettings.tipInner": "Nastavit pouze vnitřní <PERSON>", "SSE.Views.CellSettings.tipInnerHor": "Nastavit pouze vodorovné vnitřní <PERSON>", "SSE.Views.CellSettings.tipInnerVert": "Nastavit pouze svislé vnitřní <PERSON>", "SSE.Views.CellSettings.tipLeft": "Nastavit pouze vnější levé <PERSON>", "SSE.Views.CellSettings.tipNone": "Nastavit bez ohraničení", "SSE.Views.CellSettings.tipOuter": "Nastavit pouze vnější <PERSON>í", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Odstranit stínování", "SSE.Views.CellSettings.tipRight": "Nastavit pouze vnější pravé ohraničení", "SSE.Views.CellSettings.tipTop": "Nastavit pouze vnější horní ohraničení", "SSE.Views.ChartDataDialog.errorInFormula": "V zadaném vzorci je chyba.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Odkaz není platný. Musí odkazovat na otevřený list.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Nejvyšší možný počet bodů v řadě na graf je 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Maximální počet datových řad na graf je 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Odkaz je neplatný. Odkazy na názvy, hodnoty, velikosti, štítky dat musí  být samost<PERSON> buň<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, nebo s<PERSON>. ", "SSE.Views.ChartDataDialog.errorNoValues": "Pokud ch<PERSON>te v<PERSON> gra<PERSON>, je t<PERSON><PERSON><PERSON>, aby pos<PERSON><PERSON><PERSON>t obsahovala alespoň jednu hodnotu.", "SSE.Views.ChartDataDialog.errorStockChart": "Nesprávné pořadí řádků. Pro vytvoření burzovního grafu umístěte data na list v následujícím pořadí:<br> oteví<PERSON><PERSON> cena, maxim<PERSON>lní cena, minim<PERSON>lní cena, uzavírací cena.", "SSE.Views.ChartDataDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textCategory": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Kategorie) štítků osy", "SSE.Views.ChartDataDialog.textData": "<PERSON><PERSON><PERSON><PERSON> dat grafu", "SSE.Views.ChartDataDialog.textDelete": "Odstranit", "SSE.Views.ChartDataDialog.textDown": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textInvalidRange": "Neplatný rozsah buněk", "SSE.Views.ChartDataDialog.textSelectData": "Vybrat data", "SSE.Views.ChartDataDialog.textSeries": "Záznamy v legendě (série)", "SSE.Views.ChartDataDialog.textSwitch": "Pohodit Řádky/Sloupce", "SSE.Views.ChartDataDialog.textTitle": "Data grafu", "SSE.Views.ChartDataDialog.textUp": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.errorInFormula": "V zadaném vzorci je chyba.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Odkaz není platný. Musí odkazovat na otevřený list.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Nejvyšší možný počet bodů v řadě na graf je 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Maximální počet datových řad na graf je 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Odkaz je neplatný. Odkazy na názvy, hodnoty, velikosti, štítky dat musí  být samost<PERSON> buň<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, nebo s<PERSON>. ", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Pokud ch<PERSON>te v<PERSON> gra<PERSON>, je t<PERSON><PERSON><PERSON>, aby pos<PERSON><PERSON><PERSON>t obsahovala alespoň jednu hodnotu.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Nespravné pořadí <PERSON>ů. Chcete-li vytvořit burzovní graf umístěte data na list v následujícím pořadí:<br> otev<PERSON><PERSON><PERSON> cena, maxim<PERSON>lní cena, minim<PERSON>lní cena, uzavírací cena.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Neplatný rozsah buněk", "SSE.Views.ChartDataRangeDialog.textSelectData": "Vybrat data", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Rozsah štítků osy", "SSE.Views.ChartDataRangeDialog.txtChoose": "Zvolte rozsah", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "<PERSON><PERSON><PERSON>v řady", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Štítky osy", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtValues": "Hodnoty", "SSE.Views.ChartDataRangeDialog.txtXValues": "Hodnoty osy X", "SSE.Views.ChartDataRangeDialog.txtYValues": "Hodnoty osy Y", "SSE.Views.ChartSettings.errorMaxRows": "Maximální počet datových řad na graf je 255.", "SSE.Views.ChartSettings.strLineWeight": "Tloušťka čáry", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "Šablona", "SSE.Views.ChartSettings.text3dDepth": "Hloubka (% ze základny)", "SSE.Views.ChartSettings.text3dHeight": "Výška (% ze základny)", "SSE.Views.ChartSettings.text3dRotation": "3D rotace", "SSE.Views.ChartSettings.textAdvanced": "Zobrazit pokročilá nastavení", "SSE.Views.ChartSettings.textAutoscale": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textBorderSizeErr": "Zadaná hodnota není správná.<br>Zadejte hodnotu v rozmezí 0 až 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "Změnit typ", "SSE.Views.ChartSettings.textChartType": "Změnit typ grafu", "SSE.Views.ChartSettings.textDefault": "Výchozí rotace", "SSE.Views.ChartSettings.textDown": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textEditData": "Upravit data a umístění", "SSE.Views.ChartSettings.textFirstPoint": "Prvn<PERSON> bod", "SSE.Views.ChartSettings.textHeight": "Výška", "SSE.Views.ChartSettings.textHighPoint": "<PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.ChartSettings.textKeepRatio": "Konstantní proporce", "SSE.Views.ChartSettings.textLastPoint": "Poslední bod", "SSE.Views.ChartSettings.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textLowPoint": "Nízký bod", "SSE.Views.ChartSettings.textMarkers": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textNarrow": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>", "SSE.Views.ChartSettings.textNegativePoint": "<PERSON><PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.ChartSettings.textPerspective": "Perspek<PERSON>va", "SSE.Views.ChartSettings.textRanges": "<PERSON><PERSON><PERSON><PERSON> dat", "SSE.Views.ChartSettings.textRight": "Vpravo", "SSE.Views.ChartSettings.textRightAngle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSelectData": "Vybrat data", "SSE.Views.ChartSettings.textShow": "Zobrazit", "SSE.Views.ChartSettings.textSize": "Velikost", "SSE.Views.ChartSettings.textStyle": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textSwitch": "Pohodit Řádky/Sloupce", "SSE.Views.ChartSettings.textType": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textUp": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textWiden": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pole", "SSE.Views.ChartSettings.textWidth": "Šířka", "SSE.Views.ChartSettings.textX": "kolem osy X", "SSE.Views.ChartSettings.textY": "kolem o<PERSON>", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "CHYBA! Nejvyšší možný počet bodů za sebou v jednom grafu je 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "CHYBA! Nejvyšší možný počet datových řad pro jeden graf je 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Nesprávné pořadí řádků. Pro vytvoření burzovního grafu umístěte data na list v následujícím pořadí:<br> oteví<PERSON><PERSON> cena, maxim<PERSON>lní cena, minim<PERSON>lní cena, uzavírací cena.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Neposouvat nebo neměnit velikost s buňkami", "SSE.Views.ChartSettingsDlg.textAlt": "Alternativní text", "SSE.Views.ChartSettingsDlg.textAltDescription": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAltTip": "Alternativní textová reprezentace informací vizuálního objektu, která bude čtena lidem se zrakovým nebo kognitivním postižením, aby jim pomohla lépe porozumět informacím, které se nacházejí v obrázku, grafu, obrazci nebo v tabulce.", "SSE.Views.ChartSettingsDlg.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAuto": "<PERSON>ky", "SSE.Views.ChartSettingsDlg.textAutoEach": "Automaticky pro každý", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Křížení os", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Možnosti osy", "SSE.Views.ChartSettingsDlg.textAxisPos": "Umístěn<PERSON>", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Nastavení osy", "SSE.Views.ChartSettingsDlg.textAxisTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBase": "funkce Base", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Mezi značkami zaškrtnutí", "SSE.Views.ChartSettingsDlg.textBillions": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBottom": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCategoryName": "<PERSON><PERSON><PERSON>v kategorie", "SSE.Views.ChartSettingsDlg.textCenter": "<PERSON> střed", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Prvky grafu a<br>Legenda grafu", "SSE.Views.ChartSettingsDlg.textChartTitle": "Název grafu", "SSE.Views.ChartSettingsDlg.textCross": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textCustom": "Vlastní", "SSE.Views.ChartSettingsDlg.textDataColumns": "ve slou<PERSON><PERSON><PERSON>ch", "SSE.Views.ChartSettingsDlg.textDataLabels": "Š<PERSON><PERSON><PERSON><PERSON> dat", "SSE.Views.ChartSettingsDlg.textDataRows": "v řádcích", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Zobrazit legendu", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Skryté a prázdné buňky", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Připojit datové body k řádku", "SSE.Views.ChartSettingsDlg.textFit": "Přizpůsobit šířce", "SSE.Views.ChartSettingsDlg.textFixed": "Napevno", "SSE.Views.ChartSettingsDlg.textFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textGaps": "Mezery", "SSE.Views.ChartSettingsDlg.textGridLines": "Mřížka", "SSE.Views.ChartSettingsDlg.textGroup": "Seskupit mikrografy", "SSE.Views.ChartSettingsDlg.textHide": "Skr<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHideAxis": "Skr<PERSON><PERSON> o<PERSON>", "SSE.Views.ChartSettingsDlg.textHigh": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxis": "Vodorovná osa", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Pomocná vodorovná osa", "SSE.Views.ChartSettingsDlg.textHorizontal": "<PERSON><PERSON><PERSON>v<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "V rámci", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Uvni<PERSON><PERSON> dole", "SSE.Views.ChartSettingsDlg.textInnerTop": "Uvnitř nahoře", "SSE.Views.ChartSettingsDlg.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.ChartSettingsDlg.textLabelDist": "Vzdálenost štítku osy", "SSE.Views.ChartSettingsDlg.textLabelInterval": "<PERSON>val <PERSON>", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Možnosti štítku", "SSE.Views.ChartSettingsDlg.textLabelPos": "Pozice štítku", "SSE.Views.ChartSettingsDlg.textLayout": "Rozvržení", "SSE.Views.ChartSettingsDlg.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Překrytí vlevo", "SSE.Views.ChartSettingsDlg.textLegendBottom": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendPos": "<PERSON>a", "SSE.Views.ChartSettingsDlg.textLegendRight": "Vpravo", "SSE.Views.ChartSettingsDlg.textLegendTop": "Nahoře", "SSE.Views.ChartSettingsDlg.textLines": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLocationRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLogScale": "Logaritmické <PERSON>", "SSE.Views.ChartSettingsDlg.textLow": "<PERSON><PERSON>z<PERSON>", "SSE.Views.ChartSettingsDlg.textMajor": "Hlavní", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Hlavní a vedlejší", "SSE.Views.ChartSettingsDlg.textMajorType": "Hlavní typ", "SSE.Views.ChartSettingsDlg.textManual": "R<PERSON>č<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarkers": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Interval mezi značkami", "SSE.Views.ChartSettingsDlg.textMaxValue": "Maximální hodn<PERSON>", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "Vedlejší", "SSE.Views.ChartSettingsDlg.textMinorType": "Vedlejší typ", "SSE.Views.ChartSettingsDlg.textMinValue": "Minimální hodnota", "SSE.Views.ChartSettingsDlg.textNextToAxis": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Bez překrytí", "SSE.Views.ChartSettingsDlg.textOneCell": "Přesouvat ale neměnit velikost společně s buňkami", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Na značkách zaškrtnutí", "SSE.Views.ChartSettingsDlg.textOut": "Vně", "SSE.Views.ChartSettingsDlg.textOuterTop": "Vnějš<PERSON>", "SSE.Views.ChartSettingsDlg.textOverlay": "Překrytí", "SSE.Views.ChartSettingsDlg.textReverse": "Hodnoty v opačném pořadí", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Obrácené p<PERSON>ř<PERSON>í", "SSE.Views.ChartSettingsDlg.textRight": "Vpravo", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Překrytí vpravo", "SSE.Views.ChartSettingsDlg.textRotated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSameAll": "Stejné pro všechny", "SSE.Views.ChartSettingsDlg.textSelectData": "Vybrat data", "SSE.Views.ChartSettingsDlg.textSeparator": "Oddělovače popisků dat", "SSE.Views.ChartSettingsDlg.textSeriesName": "<PERSON><PERSON><PERSON>v řady", "SSE.Views.ChartSettingsDlg.textShow": "Zobrazit", "SSE.Views.ChartSettingsDlg.textShowBorders": "Zobrazit ohraničení grafu", "SSE.Views.ChartSettingsDlg.textShowData": "Zobrazit data v uzavřených řádcích a sloupcích", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Zobrazit prázdné buňky jako", "SSE.Views.ChartSettingsDlg.textShowEquation": "Display equation on chart", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Zobrazit osu", "SSE.Views.ChartSettingsDlg.textShowValues": "Zobrazit hodnoty grafu", "SSE.Views.ChartSettingsDlg.textSingle": "Jednoduchý mikrograf", "SSE.Views.ChartSettingsDlg.textSmooth": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSnap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textSparkRanges": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textStraight": "Rov<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textStyle": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTickOptions": "Možnosti zaškrtávání", "SSE.Views.ChartSettingsDlg.textTitle": "<PERSON> <PERSON> pokročilá nastavení", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Mikrograf – pokročilá nastavení", "SSE.Views.ChartSettingsDlg.textTop": "Nahoře", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Trendline options", "SSE.Views.ChartSettingsDlg.textTrillions": "Biliony", "SSE.Views.ChartSettingsDlg.textTwoCell": "Přesouvat a měnit velikost společně s buňkami", "SSE.Views.ChartSettingsDlg.textType": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTypeData": "Typy a data", "SSE.Views.ChartSettingsDlg.textUnits": "Zob<PERSON><PERSON> j<PERSON>", "SSE.Views.ChartSettingsDlg.textValue": "Hodnota", "SSE.Views.ChartSettingsDlg.textVertAxis": "Svislá osa", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Pomocná svislá osa", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Název osy x", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "N<PERSON>zev o<PERSON> y", "SSE.Views.ChartSettingsDlg.textZero": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.txtEmpty": "Toto pole je povinné", "SSE.Views.ChartTypeDialog.errorComboSeries": "Pro vytvoření kombino<PERSON>ho grafu, zvolte alespoň dvě skupiny dat. ", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Vybraný graf v<PERSON><PERSON>, která je v použita ve vybraném grafu. Vyberte jiný typ grafu.", "SSE.Views.ChartTypeDialog.textSecondary": "Pomocná osa", "SSE.Views.ChartTypeDialog.textSeries": "Série", "SSE.Views.ChartTypeDialog.textStyle": "<PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textTitle": "Typ grafu", "SSE.Views.ChartTypeDialog.textType": "<PERSON><PERSON>", "SSE.Views.ChartWizardDialog.errorComboSeries": "Pro vytvoření kombino<PERSON>ho grafu, zvolte alespoň dvě skupiny dat. ", "SSE.Views.ChartWizardDialog.errorMaxPoints": "Nejvyšší možný počet bodů v řadě na graf je 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "Maximální počet datových řad na graf je 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "Vybraný graf v<PERSON><PERSON>, která je v použita ve vybraném grafu. Vyberte jiný typ grafu.", "SSE.Views.ChartWizardDialog.errorStockChart": "Nesprávné pořadí <PERSON>ů. Chcete-li sestavit bur<PERSON>vní graf, zapište údaje na list v následujícím pořadí: zahajovací cena, maximální cena, minimální cena, konečná cena.", "SSE.Views.ChartWizardDialog.textRecommended": "Doporučeno", "SSE.Views.ChartWizardDialog.textSecondary": "Pomocná osa", "SSE.Views.ChartWizardDialog.textSeries": "Řady", "SSE.Views.ChartWizardDialog.textTitle": "Vložit graf", "SSE.Views.ChartWizardDialog.textTitleChange": "Změnit typ grafu", "SSE.Views.ChartWizardDialog.textType": "<PERSON><PERSON>", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Výběr typu grafu a osy pro datové <PERSON>ady", "SSE.Views.CreatePivotDialog.textDataRange": "<PERSON><PERSON><PERSON><PERSON> zdrojov<PERSON>ch dat", "SSE.Views.CreatePivotDialog.textDestination": "Zvolte kam umístit tabulku", "SSE.Views.CreatePivotDialog.textExist": "Existující list", "SSE.Views.CreatePivotDialog.textInvalidRange": "Neplatný rozsah buněk", "SSE.Views.CreatePivotDialog.textNew": "Nový list", "SSE.Views.CreatePivotDialog.textSelectData": "Vybrat data", "SSE.Views.CreatePivotDialog.textTitle": "Vytvořit kontingenční tabulku", "SSE.Views.CreatePivotDialog.txtEmpty": "Toto pole je povinné", "SSE.Views.CreateSparklineDialog.textDataRange": "<PERSON><PERSON><PERSON><PERSON> zdrojov<PERSON>ch dat", "SSE.Views.CreateSparklineDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON>, kde umístit mikrograf", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Neplatný rozsah buněk", "SSE.Views.CreateSparklineDialog.textSelectData": "Vybrat data", "SSE.Views.CreateSparklineDialog.textTitle": "Vytvořit mikrografy", "SSE.Views.CreateSparklineDialog.txtEmpty": "Toto pole je povinné", "SSE.Views.DataTab.capBtnGroup": "Seskupení", "SSE.Views.DataTab.capBtnTextCustomSort": "Vlastní <PERSON>", "SSE.Views.DataTab.capBtnTextDataValidation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dat", "SSE.Views.DataTab.capBtnTextRemDuplicates": "<PERSON><PERSON><PERSON><PERSON> duplicity", "SSE.Views.DataTab.capBtnTextToCol": "Text na sloupce", "SSE.Views.DataTab.capBtnUngroup": "Zrušit seskupení", "SSE.Views.DataTab.capDataExternalLinks": "Externí <PERSON>", "SSE.Views.DataTab.capDataFromText": "Získat data", "SSE.Views.DataTab.capGoalSeek": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.DataTab.mniFromFile": "Z místního TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "Z webové adresy TXT/CSV", "SSE.Views.DataTab.mniFromXMLFile": "Z lokálního XML", "SSE.Views.DataTab.textBelow": "Součtové řádky pod detailem", "SSE.Views.DataTab.textClear": "Vyčistit osnovu", "SSE.Views.DataTab.textColumns": "Zrušit seskupení sloupců", "SSE.Views.DataTab.textGroupColumns": "Seskupit sloupce", "SSE.Views.DataTab.textGroupRows": "Seskupit <PERSON>ky", "SSE.Views.DataTab.textRightOf": "Součtové sloupce vpravo od podrobností", "SSE.Views.DataTab.textRows": "Zrušit seskupení řádků", "SSE.Views.DataTab.tipCustomSort": "Vlastní <PERSON>", "SSE.Views.DataTab.tipDataFromText": "Získat data ze souboru", "SSE.Views.DataTab.tipDataValidation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dat", "SSE.Views.DataTab.tipExternalLinks": "Zobrazit <PERSON><PERSON><PERSON><PERSON>, se k<PERSON><PERSON>mi je sešit propojen", "SSE.Views.DataTab.tipGoalSeek": "Umožňuje najít správný vstup pro vybranou hodnotu", "SSE.Views.DataTab.tipGroup": "Seskupit rozsah buněk", "SSE.Views.DataTab.tipRemDuplicates": "Odebrat z listu duplicitní <PERSON>", "SSE.Views.DataTab.tipToColumns": "Rozdělit text buňky do sloupců", "SSE.Views.DataTab.tipUngroup": "Zrušit seskupení rozsahu buněk", "SSE.Views.DataValidationDialog.errorFormula": "Hodnota je aktuálně vyhodnocena jako chy<PERSON>ná. Opravdu chcete pokračovat?", "SSE.Views.DataValidationDialog.errorInvalid": "Zadaná hodnota pole \"{0}\" je chyb<PERSON>.", "SSE.Views.DataValidationDialog.errorInvalidDate": "<PERSON><PERSON><PERSON> datum \"{0}\" nen<PERSON> platn<PERSON>.", "SSE.Views.DataValidationDialog.errorInvalidList": "Zdrojový seznam musí obsahovat oddělovače, nebo odkazovat na jeden řádek či sloupec.", "SSE.Views.DataValidationDialog.errorInvalidTime": "Zadaný čas do pole \"{0}\" je chyb<PERSON><PERSON>.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "Hodnota pole \"{1}\" mus<PERSON> být vět<PERSON><PERSON> nebo rovna hodnotě pole \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "<PERSON><PERSON><PERSON><PERSON> zadat hodnotu do pole \"{0}\" and pole \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "<PERSON> kolonky „{0}“ je třeba zadat hodnotu.", "SSE.Views.DataValidationDialog.errorNamedRange": "<PERSON>j<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, se ne<PERSON><PERSON><PERSON>.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "V podmínce „{0}“ není možné p<PERSON>žít záporné hodnoty.", "SSE.Views.DataValidationDialog.errorNotNumeric": "Pole \"{0}\" m<PERSON><PERSON> <PERSON><PERSON><PERSON>, mate<PERSON><PERSON><PERSON> v<PERSON>, nebo odkazovat na buňku obsahující číselnou hodnotu. ", "SSE.Views.DataValidationDialog.strError": "Chybová hláška", "SSE.Views.DataValidationDialog.strInput": "Zpráva při zadávání", "SSE.Views.DataValidationDialog.strSettings": "Nastavení", "SSE.Views.DataValidationDialog.textAlert": "Upozornění", "SSE.Views.DataValidationDialog.textAllow": "Povolit", "SSE.Views.DataValidationDialog.textApply": "Uplatnit tyto změny na veškeré ostatní buňky, kter<PERSON> mají stejn<PERSON> nastavení", "SSE.Views.DataValidationDialog.textCellSelected": "Zprávu při zadávání zobrazit při výběru buňky", "SSE.Views.DataValidationDialog.textCompare": "Porovnat s", "SSE.Views.DataValidationDialog.textData": "Data", "SSE.Views.DataValidationDialog.textEndDate": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndTime": "<PERSON>as <PERSON>", "SSE.Views.DataValidationDialog.textError": "Chybové hlášení", "SSE.Views.DataValidationDialog.textFormula": "Vzorec", "SSE.Views.DataValidationDialog.textIgnore": "Ignorovat prázdné buňky", "SSE.Views.DataValidationDialog.textInput": "Zpráva při zadávání", "SSE.Views.DataValidationDialog.textMax": "Maximum", "SSE.Views.DataValidationDialog.textMessage": "Zpráva", "SSE.Views.DataValidationDialog.textMin": "Minimum", "SSE.Views.DataValidationDialog.textSelectData": "Vybrat data", "SSE.Views.DataValidationDialog.textShowDropDown": "Zobrazit rozbalovací seznam v buňce", "SSE.Views.DataValidationDialog.textShowError": "Zobrazovat chybovou hlášku po zadání chybných dat", "SSE.Views.DataValidationDialog.textShowInput": "Zprávu při zadávání zobrazit při výběru buňky", "SSE.Views.DataValidationDialog.textSource": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStartDate": "Datum z<PERSON>čá<PERSON>", "SSE.Views.DataValidationDialog.textStartTime": "Čas začátku", "SSE.Views.DataValidationDialog.textStop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStyle": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textUserEnters": "Pokud uživatel zadá nepovolená data, zobrazit tuto zprávu", "SSE.Views.DataValidationDialog.txtAny": "Libovolná hodnota", "SSE.Views.DataValidationDialog.txtBetween": "mezi", "SSE.Views.DataValidationDialog.txtDate": "Datum", "SSE.Views.DataValidationDialog.txtDecimal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtElTime": "Uplynulý čas", "SSE.Views.DataValidationDialog.txtEndDate": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtEndTime": "<PERSON>as <PERSON>", "SSE.Views.DataValidationDialog.txtEqual": "rovná se", "SSE.Views.DataValidationDialog.txtGreaterThan": "větš<PERSON> než", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "Větší nebo rovno", "SSE.Views.DataValidationDialog.txtLength": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtLessThan": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "Méně než nebo rovno", "SSE.Views.DataValidationDialog.txtList": "Seznam", "SSE.Views.DataValidationDialog.txtNotBetween": "ne mezi", "SSE.Views.DataValidationDialog.txtNotEqual": "nerovná se", "SSE.Views.DataValidationDialog.txtOther": "Ostatní", "SSE.Views.DataValidationDialog.txtStartDate": "Datum z<PERSON>čá<PERSON>", "SSE.Views.DataValidationDialog.txtStartTime": "Čas začátku", "SSE.Views.DataValidationDialog.txtTextLength": "<PERSON><PERSON><PERSON><PERSON> textu", "SSE.Views.DataValidationDialog.txtTime": "Čas", "SSE.Views.DataValidationDialog.txtWhole": "<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capAnd": "A", "SSE.Views.DigitalFilterDialog.capCondition1": "rovná se", "SSE.Views.DigitalFilterDialog.capCondition10": "nekončí na", "SSE.Views.DigitalFilterDialog.capCondition11": "obs<PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition12": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DigitalFilterDialog.capCondition2": "nerovná se", "SSE.Views.DigitalFilterDialog.capCondition3": "je v<PERSON><PERSON><PERSON><PERSON> než", "SSE.Views.DigitalFilterDialog.capCondition30": "je po", "SSE.Views.DigitalFilterDialog.capCondition4": "je vet<PERSON><PERSON> než nebo rovno", "SSE.Views.DigitalFilterDialog.capCondition40": "je po nebo stejné", "SSE.Views.DigitalFilterDialog.capCondition5": "je men<PERSON><PERSON> ne<PERSON>", "SSE.Views.DigitalFilterDialog.capCondition50": "je p<PERSON>ed", "SSE.Views.DigitalFilterDialog.capCondition6": "je men<PERSON><PERSON> než nebo rovno", "SSE.Views.DigitalFilterDialog.capCondition60": "je p<PERSON>ed nebo stejn<PERSON>", "SSE.Views.DigitalFilterDialog.capCondition7": "začíná", "SSE.Views.DigitalFilterDialog.capCondition8": "nezačíná na", "SSE.Views.DigitalFilterDialog.capCondition9": "končí na", "SSE.Views.DigitalFilterDialog.capOr": "Nebo", "SSE.Views.DigitalFilterDialog.textNoFilter": "ž<PERSON><PERSON><PERSON> filtr", "SSE.Views.DigitalFilterDialog.textShowRows": "Zobrazit řádky kde", "SSE.Views.DigitalFilterDialog.textUse1": "Použijte ? pro zastoupení libovolného znaku", "SSE.Views.DigitalFilterDialog.textUse2": "Použijte * pro zastoupení libovolné řady znaků", "SSE.Views.DigitalFilterDialog.txtSelectDate": "<PERSON><PERSON><PERSON><PERSON> datum", "SSE.Views.DigitalFilterDialog.txtTitle": "Vlastn<PERSON> filtr", "SSE.Views.DocumentHolder.advancedEquationText": "Nastavení rovnice", "SSE.Views.DocumentHolder.advancedImgText": "Pokročilá nastavení obrázku", "SSE.Views.DocumentHolder.advancedShapeText": "Pokročilá nastavení obrazců", "SSE.Views.DocumentHolder.advancedSlicerText": "Pokročilé nastavení průřezu", "SSE.Views.DocumentHolder.allLinearText": "Vše - lineární", "SSE.Views.DocumentHolder.allProfText": "Vše - profesionální", "SSE.Views.DocumentHolder.bottomCellText": "Zarovnat dolů", "SSE.Views.DocumentHolder.bulletsText": "Odrážky a číslování", "SSE.Views.DocumentHolder.centerCellText": "Zarovnat na střed", "SSE.Views.DocumentHolder.chartDataText": "Vybrat data grafu", "SSE.Views.DocumentHolder.chartText": "Pokročilé nastavení grafu", "SSE.Views.DocumentHolder.chartTypeText": "Změnit typ grafu", "SSE.Views.DocumentHolder.currLinearText": "Aktuální - lineární", "SSE.Views.DocumentHolder.currProfText": "Aktuální - profesionální", "SSE.Views.DocumentHolder.deleteColumnText": "Sloupec", "SSE.Views.DocumentHolder.deleteRowText": "Řádek", "SSE.Views.DocumentHolder.deleteTableText": "Tabulka", "SSE.Views.DocumentHolder.direct270Text": "Otočit text nahoru", "SSE.Views.DocumentHolder.direct90Text": "Otočit text dolů", "SSE.Views.DocumentHolder.directHText": "<PERSON><PERSON><PERSON>v<PERSON><PERSON>", "SSE.Views.DocumentHolder.directionText": "<PERSON><PERSON><PERSON><PERSON>u", "SSE.Views.DocumentHolder.editChartText": "Upravit data", "SSE.Views.DocumentHolder.editHyperlinkText": "Upravit hypertextový odkaz", "SSE.Views.DocumentHolder.hideEqToolbar": "Skrýt panel nástrojů rovnic", "SSE.Views.DocumentHolder.insertColumnLeftText": "Sloupec vlevo", "SSE.Views.DocumentHolder.insertColumnRightText": "Sloupec vpravo", "SSE.Views.DocumentHolder.insertRowAboveText": "Řádek nad", "SSE.Views.DocumentHolder.insertRowBelowText": "Řádek pod", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "Skutečná velikost", "SSE.Views.DocumentHolder.removeHyperlinkText": "Odebrat hypertextový odkaz", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.DocumentHolder.selectDataText": "Data sloupce", "SSE.Views.DocumentHolder.selectRowText": "Řádek", "SSE.Views.DocumentHolder.selectTableText": "Tabulka", "SSE.Views.DocumentHolder.showEqToolbar": "Zobrazit panel nástroj<PERSON> rovnic", "SSE.Views.DocumentHolder.strDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.strDetails": "Podrobnosti podpisu", "SSE.Views.DocumentHolder.strSetup": "Nastavení podpisu", "SSE.Views.DocumentHolder.strSign": "Podpis", "SSE.Views.DocumentHolder.textAlign": "Zarovnání", "SSE.Views.DocumentHolder.textArrange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeBack": "Přenést do pozadí", "SSE.Views.DocumentHolder.textArrangeBackward": "Přenést o vrstvu níž", "SSE.Views.DocumentHolder.textArrangeForward": "Přenést výše", "SSE.Views.DocumentHolder.textArrangeFront": "Přenést do popředí", "SSE.Views.DocumentHolder.textAverage": "Průměrné", "SSE.Views.DocumentHolder.textBullets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCopyCells": "Kopírovat buňky", "SSE.Views.DocumentHolder.textCount": "Počet", "SSE.Views.DocumentHolder.textCrop": "Oříznout", "SSE.Views.DocumentHolder.textCropFill": "Výplň", "SSE.Views.DocumentHolder.textCropFit": "Přizpůsobit", "SSE.Views.DocumentHolder.textEditPoints": "Upravit body", "SSE.Views.DocumentHolder.textEntriesList": "Vybrat z rozbalovacího seznamu", "SSE.Views.DocumentHolder.textFillDays": "Vyplnit dny", "SSE.Views.DocumentHolder.textFillFormatOnly": "Vyplnit pouze formátování", "SSE.Views.DocumentHolder.textFillMonths": "Vyplnit měsíce", "SSE.Views.DocumentHolder.textFillSeries": "Vyplnit řadu", "SSE.Views.DocumentHolder.textFillWeekdays": "Vyplnit pracovní dny", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Vyplnit bez formátování", "SSE.Views.DocumentHolder.textFillYears": "Vyplnit roky", "SSE.Views.DocumentHolder.textFlashFill": "Rychlé vyplnění", "SSE.Views.DocumentHolder.textFlipH": "Převrátit vodorovně", "SSE.Views.DocumentHolder.textFlipV": "Př<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.DocumentHolder.textFreezePanes": "Ukotvit <PERSON>", "SSE.Views.DocumentHolder.textFromFile": "<PERSON><PERSON> souboru", "SSE.Views.DocumentHolder.textFromStorage": "Z úložiště", "SSE.Views.DocumentHolder.textFromUrl": "Z adresy URL", "SSE.Views.DocumentHolder.textGrowthTrend": "Geometrický trend", "SSE.Views.DocumentHolder.textLinearTrend": "Lineární trend", "SSE.Views.DocumentHolder.textListSettings": "Nastavení seznamu", "SSE.Views.DocumentHolder.textMacro": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON>ro", "SSE.Views.DocumentHolder.textMax": "Maximum", "SSE.Views.DocumentHolder.textMin": "Minimum", "SSE.Views.DocumentHolder.textMore": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textMoreFormats": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textNumbering": "Číslování", "SSE.Views.DocumentHolder.textReplace": "Nahradit obrázek", "SSE.Views.DocumentHolder.textResetCrop": "Reset crop", "SSE.Views.DocumentHolder.textRotate": "O<PERSON>č<PERSON>", "SSE.Views.DocumentHolder.textRotate270": "Otočit o 90° doleva", "SSE.Views.DocumentHolder.textRotate90": "Otočit o 90° doprava", "SSE.Views.DocumentHolder.textSaveAsPicture": "Uložit jako o<PERSON>", "SSE.Views.DocumentHolder.textSeries": "Řady", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Zarovnat dolů", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Zarovnat na střed", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Zarovnat vlevo", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Zarovnat na střed", "SSE.Views.DocumentHolder.textShapeAlignRight": "Zarovnat vpravo", "SSE.Views.DocumentHolder.textShapeAlignTop": "Zarovnat nahoru", "SSE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "SSE.Views.DocumentHolder.textStdDev": "Směrodatná odchylka", "SSE.Views.DocumentHolder.textSum": "SUMA", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "Zrušit ukotvení příček", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Zatržítkové o<PERSON>", "SSE.Views.DocumentHolder.tipMarkersDash": "Pomlčkové o<PERSON>žky", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Kosočtvercové odrážky s výplní", "SSE.Views.DocumentHolder.tipMarkersFRound": "Vyplně<PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Plné čtvercové <PERSON>", "SSE.Views.DocumentHolder.tipMarkersHRound": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersStar": "Hvězdičkové odrážky", "SSE.Views.DocumentHolder.topCellText": "Zarovnat nahoru", "SSE.Views.DocumentHolder.txtAccounting": "Účetnictví", "SSE.Views.DocumentHolder.txtAddComment": "Přidat komentář", "SSE.Views.DocumentHolder.txtAddNamedRange": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>zev", "SSE.Views.DocumentHolder.txtArrange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAscending": "Vzestupně", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Automatické nastavení <PERSON> slou<PERSON>ce", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Automatické nastavení výšky řádku", "SSE.Views.DocumentHolder.txtAverage": "Pr<PERSON><PERSON>ě<PERSON>", "SSE.Views.DocumentHolder.txtCellFormat": "Formátovat buňky", "SSE.Views.DocumentHolder.txtClear": "Vyčistit", "SSE.Views.DocumentHolder.txtClearAll": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearComments": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearHyper": "Hypertextové odkazy", "SSE.Views.DocumentHolder.txtClearPivotField": "Vyčistit filtr z {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Vyčistit vybrané skupiny mikrografů", "SSE.Views.DocumentHolder.txtClearSparklines": "Vyčistit vybrané mikrografy", "SSE.Views.DocumentHolder.txtClearText": "Text", "SSE.Views.DocumentHolder.txtCollapse": "Sbal<PERSON>", "SSE.Views.DocumentHolder.txtCollapseEntire": "Sbalit celé pole", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.DocumentHolder.txtColumnWidth": "Nastavit šířku slou<PERSON>ce", "SSE.Views.DocumentHolder.txtCondFormat": "Podmín<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCopy": "Zkopírovat", "SSE.Views.DocumentHolder.txtCount": "Počet", "SSE.Views.DocumentHolder.txtCurrency": "Měna", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Vlastní <PERSON> slou<PERSON>", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Vlastní výška řádku", "SSE.Views.DocumentHolder.txtCustomSort": "Vlastní <PERSON>", "SSE.Views.DocumentHolder.txtCut": "Vyjmout", "SSE.Views.DocumentHolder.txtDateLong": "Dlouhý formát data", "SSE.Views.DocumentHolder.txtDateShort": "Krátký formát data", "SSE.Views.DocumentHolder.txtDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDelField": "Odstranit", "SSE.Views.DocumentHolder.txtDescending": "Sestupně", "SSE.Views.DocumentHolder.txtDifference": "Rozdílné <PERSON>i", "SSE.Views.DocumentHolder.txtDistribHor": "Rozmístit vodorovně", "SSE.Views.DocumentHolder.txtDistribVert": "Rozmístit svisle", "SSE.Views.DocumentHolder.txtEditComment": "Upravit komentář", "SSE.Views.DocumentHolder.txtEditObject": "Upravit objekt", "SSE.Views.DocumentHolder.txtExpand": "Rozšířit", "SSE.Views.DocumentHolder.txtExpandCollapse": "Rozšířit/Sbalit", "SSE.Views.DocumentHolder.txtExpandEntire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> celé pole", "SSE.Views.DocumentHolder.txtFieldSettings": "<PERSON><PERSON><PERSON><PERSON> pole", "SSE.Views.DocumentHolder.txtFilter": "Filtr", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filtrovat podle barvy buňky", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filtrovat podle barvy písma", "SSE.Views.DocumentHolder.txtFilterValue": "Filtrování podle hodnoty vybrané buňky", "SSE.Views.DocumentHolder.txtFormula": "Vlož<PERSON>", "SSE.Views.DocumentHolder.txtFraction": "Zlomek", "SSE.Views.DocumentHolder.txtGeneral": "Obecné", "SSE.Views.DocumentHolder.txtGetLink": "Získat odkaz pro tento rozsah", "SSE.Views.DocumentHolder.txtGrandTotal": "Celkový součet", "SSE.Views.DocumentHolder.txtGroup": "Seskupení", "SSE.Views.DocumentHolder.txtHide": "Skr<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtIndex": "Index", "SSE.Views.DocumentHolder.txtInsert": "Vložit", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hypertextový odkaz", "SSE.Views.DocumentHolder.txtInsImage": "Vložit obrázek ze souboru", "SSE.Views.DocumentHolder.txtInsImageUrl": "Vložit obrázek z URL", "SSE.Views.DocumentHolder.txtLabelFilter": "Filtry štítků", "SSE.Views.DocumentHolder.txtMax": "Maximum", "SSE.Views.DocumentHolder.txtMin": "Minimum", "SSE.Views.DocumentHolder.txtMoreOptions": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtNormal": "Bez výpočtu", "SSE.Views.DocumentHolder.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtNumFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtPaste": "Vložit", "SSE.Views.DocumentHolder.txtPercent": "% z", "SSE.Views.DocumentHolder.txtPercentage": "Procento", "SSE.Views.DocumentHolder.txtPercentDiff": "% rozdíl mezi", "SSE.Views.DocumentHolder.txtPercentOfCol": "% z celkového součtu", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% z celkového součtu", "SSE.Views.DocumentHolder.txtPercentOfParent": "% součtu nadřazené položky", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% součtu nadřazeného sloupce", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% součtu nadřazeného řádku", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% mezisoučtu v", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% ze součtu řádku", "SSE.Views.DocumentHolder.txtPivotSettings": "Nastavení kontingenční ta<PERSON>", "SSE.Views.DocumentHolder.txtProduct": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtRankAscending": "Seřadit od nejmenšího po největší", "SSE.Views.DocumentHolder.txtRankDescending": "Seřadit od největšího po nejmenší", "SSE.Views.DocumentHolder.txtReapply": "Použ<PERSON>t znov<PERSON>", "SSE.Views.DocumentHolder.txtRefresh": "Aktualizovat", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtRowHeight": "Nastavit výšku řádku", "SSE.Views.DocumentHolder.txtRunTotal": "Průběžný součet v", "SSE.Views.DocumentHolder.txtScientific": "Vědecké", "SSE.Views.DocumentHolder.txtSelect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftDown": "Posunout buňky do<PERSON>ů", "SSE.Views.DocumentHolder.txtShiftLeft": "Posunout buňky vlevo", "SSE.Views.DocumentHolder.txtShiftRight": "Posunout buňky vpravo", "SSE.Views.DocumentHolder.txtShiftUp": "Posunout buňky nahoru", "SSE.Views.DocumentHolder.txtShow": "Zobrazit", "SSE.Views.DocumentHolder.txtShowAs": "Zobrazit hodnoty jako", "SSE.Views.DocumentHolder.txtShowComment": "Zobrazit komentář", "SSE.Views.DocumentHolder.txtShowDetails": "Zobrazit podrobnosti", "SSE.Views.DocumentHolder.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSortCellColor": "Vybraná barva buňky v horní části", "SSE.Views.DocumentHolder.txtSortFontColor": "Vybraná barva písma v horní části", "SSE.Views.DocumentHolder.txtSortOption": "Více možností řazení", "SSE.Views.DocumentHolder.txtSparklines": "Mikrografy", "SSE.Views.DocumentHolder.txtSubtotalField": "Mezisoučet", "SSE.Views.DocumentHolder.txtSum": "SUMA", "SSE.Views.DocumentHolder.txtSummarize": "<PERSON><PERSON><PERSON><PERSON> hodnoty dle", "SSE.Views.DocumentHolder.txtText": "Text", "SSE.Views.DocumentHolder.txtTextAdvanced": "Pokročilá nastavení odstavce", "SSE.Views.DocumentHolder.txtTime": "Čas", "SSE.Views.DocumentHolder.txtTop10": "Top 10", "SSE.Views.DocumentHolder.txtUngroup": "Zrušit seskupení", "SSE.Views.DocumentHolder.txtValueFieldSettings": "<PERSON><PERSON><PERSON><PERSON> hodnot pole", "SSE.Views.DocumentHolder.txtValueFilter": "Hodnot<PERSON> filtrů", "SSE.Views.DocumentHolder.txtWidth": "Šířka", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "Svislé zarovnání", "SSE.Views.ExternalLinksDlg.closeButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Automatically update data from the linked sources", "SSE.Views.ExternalLinksDlg.textChange": "Změnit zdroj", "SSE.Views.ExternalLinksDlg.textDelete": "Přerušit odkazy", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Přerušit všechny odkazy", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textOpen": "Otevř<PERSON><PERSON> zdroj", "SSE.Views.ExternalLinksDlg.textSource": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.textStatus": "Stav", "SSE.Views.ExternalLinksDlg.textUnknown": "Neznámý", "SSE.Views.ExternalLinksDlg.textUpdate": "Aktualizovat hodnoty", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Aktualizovat vše", "SSE.Views.ExternalLinksDlg.textUpdating": "Aktualizuje se...", "SSE.Views.ExternalLinksDlg.txtTitle": "Externí <PERSON>", "SSE.Views.FieldSettingsDialog.strLayout": "Rozvržení", "SSE.Views.FieldSettingsDialog.strSubtotals": "Dílčí souč<PERSON>", "SSE.Views.FieldSettingsDialog.textNumFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.textReport": "Formulář výkazu", "SSE.Views.FieldSettingsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> pole", "SSE.Views.FieldSettingsDialog.txtAverage": "Průměrné", "SSE.Views.FieldSettingsDialog.txtBlank": "Vložit prázdné řádky za každou položku", "SSE.Views.FieldSettingsDialog.txtBottom": "Zobrazit zápatí skupiny", "SSE.Views.FieldSettingsDialog.txtCompact": "Kompaktní", "SSE.Views.FieldSettingsDialog.txtCount": "Počet", "SSE.Views.FieldSettingsDialog.txtCountNums": "Spočítat čísla", "SSE.Views.FieldSettingsDialog.txtCustomName": "Vlast<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtEmpty": "Zobrazit položky bez dat", "SSE.Views.FieldSettingsDialog.txtMax": "Maximum", "SSE.Views.FieldSettingsDialog.txtMin": "Minimum", "SSE.Views.FieldSettingsDialog.txtOutline": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtProduct": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtRepeat": "Opakovat štítky položek na každém řádku", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Zobrazit dílčí celkové součty", "SSE.Views.FieldSettingsDialog.txtSourceName": "Název zdroje:", "SSE.Views.FieldSettingsDialog.txtStdDev": "Směrodatná odchylka", "SSE.Views.FieldSettingsDialog.txtStdDevp": "Populační směrodatná odchylka", "SSE.Views.FieldSettingsDialog.txtSum": "SUMA", "SSE.Views.FieldSettingsDialog.txtSummarize": "Funkce pro dílčí součty", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabulkov<PERSON>", "SSE.Views.FieldSettingsDialog.txtTop": "Zobrazit v záhlaví skupiny.", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.ariaFileMenu": "<PERSON><PERSON><PERSON><PERSON><PERSON> so<PERSON>u", "SSE.Views.FileMenu.btnBackCaption": "Otev<PERSON><PERSON><PERSON>ě<PERSON>í so<PERSON>u", "SSE.Views.FileMenu.btnCloseEditor": "<PERSON><PERSON><PERSON><PERSON><PERSON> so<PERSON>", "SSE.Views.FileMenu.btnCloseMenuCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnCreateNewCaption": "Vytvořit nový", "SSE.Views.FileMenu.btnDownloadCaption": "St<PERSON><PERSON><PERSON> jako", "SSE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnExportToPDFCaption": "Export do PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnHelpCaption": "Nápověda", "SSE.Views.FileMenu.btnHistoryCaption": "Historie verzí", "SSE.Views.FileMenu.btnInfoCaption": "Informace", "SSE.Views.FileMenu.btnPrintCaption": "Tisk", "SSE.Views.FileMenu.btnProtectCaption": "Zabezpečení", "SSE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnRenameCaption": "Př<PERSON>menovat", "SSE.Views.FileMenu.btnReturnCaption": "Zpátky do sešitu", "SSE.Views.FileMenu.btnRightsCaption": "Přístupová práva", "SSE.Views.FileMenu.btnSaveAsCaption": "Uložit jako", "SSE.Views.FileMenu.btnSaveCaption": "Uložit", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Uložit kopii jako", "SSE.Views.FileMenu.btnSettingsCaption": "Pokročilá nastavení", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "SSE.Views.FileMenu.btnToEditCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Prázdný sešit", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Vytvořit nový", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Přidat autora", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Přidat vlastnost", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Přidat text", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplikace", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Změnit přístupová práva", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Obecné", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Vytvořeno", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Vlastnosti dokumentu", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Naposledy upravil(a)", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "<PERSON><PERSON><PERSON><PERSON> up<PERSON>no", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "Ne", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Vlastník", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Umístění", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "V<PERSON><PERSON>ti", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Vlastnost s tímto názvem již existuje", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> mají <PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Informace o sešitu", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tagy", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Přístupová práva", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Změnit přístupová práva", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "<PERSON><PERSON><PERSON>, k<PERSON><PERSON> mají <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "<PERSON><PERSON><PERSON> s<PERSON>upráce na úpravách", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Použít kalendářní systém z roku 1904", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Odd<PERSON>lov<PERSON><PERSON> desetinný<PERSON> m<PERSON>t", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Jazyk slovníku", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Povolit iterační metody", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Automatický", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Vyhlazování hran znaků", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Jazyk vzorce", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Příklad: SUMA; MIN; MAX; POČET", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Zobrazit popisek funkce", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Show horizontal scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Ignorovat slova psaná pouze VELKÝMI PÍSMENY", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Ignorovat slova obsahující čísla", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Nastavení maker", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Maximální změna", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Maximální počet iterací", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Při vkládání obsahu zobrazit tlačítko Možnosti vložení", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Styl reference R1C1", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Místní nastavení", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Příklad:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "Rozhraní RTL (písmo zprava doleva)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Zobrazit komentáře v listu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Zobrazit změny od jiných uživatelů", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Zobrazit vyřešené komentáře", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Snapped to the grid while scrolling", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Statický", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "<PERSON><PERSON> <PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Vzhled uživatelského rozhraní", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Měřit v jednotkách", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Použ<PERSON>t oddělovače podle místních nastavení (locale)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Show vertical scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Výchozí měřítko zobrazení", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Každých 10 minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Každých 30 minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Každých 5 minut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "<PERSON><PERSON><PERSON> obnova", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Automatic<PERSON>é <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Vypnuto", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "Výplň", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Uložit dočasnou verzi", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "<PERSON><PERSON><PERSON><PERSON> minutu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "<PERSON><PERSON> od<PERSON>zu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "Pokročilá nastavení", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "Vzhled", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Autokorekce možnosti...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "b<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Katalánský", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Výchozí re<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Přepočítání", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centimetry", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "čeština", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Přizpůsobení rychlého přístupu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "němčina", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Úprava a uložení", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "řečtina", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "anglič<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Hodnotu nelze použít. Může být v<PERSON>žadováno celé nebo desetinné číslo.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "š<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Režim spolupráce v reálném čase. Veškeré změny jsou ukládány automaticky", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "ma<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "indonézština", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "<PERSON><PERSON><PERSON> (míra 2,54 cm)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "<PERSON><PERSON>š<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "korejš<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Poslední použité", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "jako OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Nativní", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Kontrola pra<PERSON>pisu", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Body", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "<PERSON><PERSON><PERSON>š<PERSON> (Brazílie)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "<PERSON><PERSON><PERSON>š<PERSON> (Portugalsko)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Zobrazit tlačítko Rychlý tisk v záhlaví editoru", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "Dokument bude vytisknut na poslední vybrané, nebo výchozí tiskárně", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Místní", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Zapnout vše", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Zapnout všechna makra bez oznámení", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Zapnout podporu čtečky obrazovky", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Default sheet direction", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "This setting will affect only the new sheets", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Left-to-right", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Right-to-left", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "slovenština", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Vypnout vše", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Vypnout všechna makra bez oznámení", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Pro synchronizaci provedených změn klikněte na tlačítko \"Uložit\"", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Použít panel nástrojů jako pozadí panelů", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Použít klávesu Alt pro navigaci uživatelským rozhraním za použití klávesnice", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Použít klávesu Option (Alt) pro navigaci uživatelským rozhraním za použití klávesnice", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "vietnamština", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Zobrazit oznámení", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Vypnout všechna makra s oznámením", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "jako <PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Pracovní prostředí", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Varování", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Heslem", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Zabezpečit sešit", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Podpisem", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Platné podpisy byly přidány do sešitu.<br><PERSON><PERSON><PERSON> je zabezpečen proti úpravám.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Zajistěte integritu sešitu přidáním<br>neviditelného digitálního pod<PERSON>u", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Upravením budou ze sešitu odebrány podpisy.<br>Opravdu chcete pokračovat?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "<PERSON>to se<PERSON> je zabez<PERSON>čen heslem", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Zašifrujte tento sešit pomocí hesla", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Tento se<PERSON>it je třeba podep<PERSON>.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Do sešitu byly přidány platné podpisy. Sešit je zabezpečen před <PERSON>.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Některé z digitálních podpisů v listu nejsou platné nebo je není možné ověřit. Sešit je zabezpečen před úpravami.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Zobrazit podpisy", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "St<PERSON><PERSON><PERSON> jako", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Uložit kopii jako", "SSE.Views.FillSeriesDialog.textAuto": "Dynamické <PERSON>ňování", "SSE.Views.FillSeriesDialog.textCols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textDate": "Datum", "SSE.Views.FillSeriesDialog.textDateUnit": "Jednotka data", "SSE.Views.FillSeriesDialog.textDay": "Den", "SSE.Views.FillSeriesDialog.textGrowth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textLinear": "Lineární", "SSE.Views.FillSeriesDialog.textMonth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textRows": "Řádky", "SSE.Views.FillSeriesDialog.textSeries": "Řady v", "SSE.Views.FillSeriesDialog.textStep": "Velikost kroku", "SSE.Views.FillSeriesDialog.textStop": "Konečná hodnota:", "SSE.Views.FillSeriesDialog.textTitle": "Řady", "SSE.Views.FillSeriesDialog.textTrend": "Trend", "SSE.Views.FillSeriesDialog.textType": "<PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textWeek": "Pracovní den", "SSE.Views.FillSeriesDialog.textYear": "Rok", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Hodnotu nelze použít. Může být v<PERSON>žadováno celé nebo desetinné číslo.", "SSE.Views.FormatRulesEditDlg.fillColor": "Barva výplně", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Varování", "SSE.Views.FormatRulesEditDlg.text2Scales": "dvoubarevná škála", "SSE.Views.FormatRulesEditDlg.text3Scales": "t<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Všechna ohraničení", "SSE.Views.FormatRulesEditDlg.textAppearance": "<PERSON><PERSON><PERSON>ed pruhu", "SSE.Views.FormatRulesEditDlg.textApply": "Uplatnit na rozsah", "SSE.Views.FormatRulesEditDlg.textAutomatic": "<PERSON>ky", "SSE.Views.FormatRulesEditDlg.textAxis": "O<PERSON>", "SSE.Views.FormatRulesEditDlg.textBarDirection": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBold": "Tučn<PERSON>", "SSE.Views.FormatRulesEditDlg.textBorder": "Ohraničení", "SSE.Views.FormatRulesEditDlg.textBordersColor": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Spodní <PERSON>", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Není možno přidat podmíněné <PERSON>", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Vnitřní svislé ohraničení", "SSE.Views.FormatRulesEditDlg.textClear": "Vyčistit", "SSE.Views.FormatRulesEditDlg.textColor": "<PERSON>va textu", "SSE.Views.FormatRulesEditDlg.textContext": "Kontext", "SSE.Views.FormatRulesEditDlg.textCustom": "Vlastní", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Ohraničení diagonálně dolů", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Ohraničení diagonálně nahoru", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Zadejte platný vzorec.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "Zadaný vzorec neodpovídá formátu <PERSON>, data, času nebo řetězci.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "<PERSON><PERSON>j<PERSON> hodnotu.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "Zadaná hodnota neodpovídá formátu <PERSON>, data, času nebo řetězci.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "Hodnota pro {0} musí výt větší než hodnota pro {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "<PERSON>ade<PERSON><PERSON> {0} a {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Výplň", "SSE.Views.FormatRulesEditDlg.textFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormula": "Vzorec", "SSE.Views.FormatRulesEditDlg.textGradient": "Př<PERSON>od", "SSE.Views.FormatRulesEditDlg.textIconLabel": "když {0} {1} a", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "k<PERSON><PERSON> {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "k<PERSON><PERSON> je hodnota", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Dochází k překrytí jednoho nebo více rozsahů dat ikon.<br>Upravte hodnoty rozsahů dat ikon, aby nedocházelo k překrytí. ", "SSE.Views.FormatRulesEditDlg.textIconStyle": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Vnitř<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textInvalid": "Neplatný rozsah dat.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.FormatRulesEditDlg.textItalic": "Skloněné", "SSE.Views.FormatRulesEditDlg.textItem": "Položka", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLongBar": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.FormatRulesEditDlg.textMaximum": "Maximum", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Vnitřní vodorovné <PERSON>í", "SSE.Views.FormatRulesEditDlg.textMidpoint": "<PERSON><PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimum", "SSE.Views.FormatRulesEditDlg.textMinpoint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bod", "SSE.Views.FormatRulesEditDlg.textNegative": "Záporné", "SSE.Views.FormatRulesEditDlg.textNewColor": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNoBorders": "<PERSON>z oh<PERSON>í", "SSE.Views.FormatRulesEditDlg.textNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Jedna nebo více hodnot není platným procentuálním podílem.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Hodnota {0} není platný procentuální podíl.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Jedna nebo více hodnot není platným percentilem.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Hodnota {0} není platný percentil.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Vn<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPercent": "Procento", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentil", "SSE.Views.FormatRulesEditDlg.textPosition": "Pozice", "SSE.Views.FormatRulesEditDlg.textPositive": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPresets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPreview": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Není možné použít relativní odkaz při aplikovaném podmíněném formátování bare<PERSON>, histogram<PERSON> a sad ikon.", "SSE.Views.FormatRulesEditDlg.textReverse": "Obrácené pořadí ikon", "SSE.Views.FormatRulesEditDlg.textRight2Left": "<PERSON><PERSON><PERSON> doleva", "SSE.Views.FormatRulesEditDlg.textRightBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRule": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSameAs": "<PERSON><PERSON><PERSON><PERSON> jako k<PERSON>", "SSE.Views.FormatRulesEditDlg.textSelectData": "Vybrat data", "SSE.Views.FormatRulesEditDlg.textShortBar": "nejk<PERSON><PERSON><PERSON> slou<PERSON>", "SSE.Views.FormatRulesEditDlg.textShowBar": "Zobrazit pouze sloupce", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Zobrazit pouze ikony", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Tento odkaz nemůže být použit ve vzorci s podmíněným formátováním.<br>Změňte odkaz tak, aby odkazoval na jednu buňku, nebo vložte odkaz do funkce. Například: =СУММ(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Přeškrnuté", "SSE.Views.FormatRulesEditDlg.textSubscript": "Dolní index", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Horní index", "SSE.Views.FormatRulesEditDlg.textTopBorders": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textUnderline": "Podtržené", "SSE.Views.FormatRulesEditDlg.tipBorders": "Ohraničení", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Účetnictví", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Měna", "SSE.Views.FormatRulesEditDlg.txtDate": "Datum", "SSE.Views.FormatRulesEditDlg.txtDateLong": "Dlouhý formát data", "SSE.Views.FormatRulesEditDlg.txtDateShort": "Krátký formát data", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Toto pole je povinné", "SSE.Views.FormatRulesEditDlg.txtFraction": "Zlomek", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Obecné", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "Žádná ikona", "SSE.Views.FormatRulesEditDlg.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Procento", "SSE.Views.FormatRulesEditDlg.txtScientific": "Vědecké", "SSE.Views.FormatRulesEditDlg.txtText": "Text", "SSE.Views.FormatRulesEditDlg.txtTime": "Čas", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Upravit formá<PERSON><PERSON> pra<PERSON>", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "<PERSON><PERSON> formátovací pra<PERSON>", "SSE.Views.FormatRulesManagerDlg.guestText": "Návštěvník", "SSE.Views.FormatRulesManagerDlg.lockText": "Uzamčeno", "SSE.Views.FormatRulesManagerDlg.text1Above": "Na 1 standardní odchylka výše průměru", "SSE.Views.FormatRulesManagerDlg.text1Below": "Na 1 standardní odchylka níže průměru", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 Směrodatná odchylka nad průměrem", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 Směrodatná odchylka pod průměrem", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 Směrodatná odchylka nad průměrem", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 Směrodatná odchylka pod průměrem", "SSE.Views.FormatRulesManagerDlg.textAbove": "Nadprůměrný", "SSE.Views.FormatRulesManagerDlg.textApply": "Uplatnit na", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Hodnota buňky začíná na", "SSE.Views.FormatRulesManagerDlg.textBelow": "Podprůměrné", "SSE.Views.FormatRulesManagerDlg.textBetween": "je z rozmezí {0} až {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Hodnota v buňce", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Gradační barevná škála", "SSE.Views.FormatRulesManagerDlg.textContains": "Hodnota buňky obsahuje", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Buňka obsahuje prázdnou hodnotu", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Chybný obsah buňky", "SSE.Views.FormatRulesManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textDown": "Přesunout pravidlo do<PERSON>ů", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Duplikovat hodnoty", "SSE.Views.FormatRulesManagerDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEnds": "Hodnota buňky končí na", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Větší nebo rovno průměru", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Menší nebo rovno průměru", "SSE.Views.FormatRulesManagerDlg.textFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textIconSet": "<PERSON><PERSON> <PERSON>kon", "SSE.Views.FormatRulesManagerDlg.textNew": "Nové", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "nen<PERSON> z rozmezí {0} až {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Hodnota buňky neobsahuje", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Buňka neobsahuje prázdnou hodnotu", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Buňka neobsahuje chybu.", "SSE.Views.FormatRulesManagerDlg.textRules": "Pravid<PERSON>", "SSE.Views.FormatRulesManagerDlg.textScope": "Zobrazit pravidla formátování pro", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Vybrat data", "SSE.Views.FormatRulesManagerDlg.textSelection": "Stávající výběr", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "<PERSON><PERSON> tabulka", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Tento list", "SSE.Views.FormatRulesManagerDlg.textThisTable": "<PERSON><PERSON> ta<PERSON>", "SSE.Views.FormatRulesManagerDlg.textUnique": "Neopakující se hodnoty", "SSE.Views.FormatRulesManagerDlg.textUp": "Přesunout pravidlo nahoru", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Prvek je upravován jiným uživatelem.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Podmín<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textCategory": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textDecimal": "Desetinn<PERSON>ch m<PERSON>t", "SSE.Views.FormatSettingsDialog.textFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textLinked": "Odkázat na zdroj", "SSE.Views.FormatSettingsDialog.textSeparator": "<PERSON><PERSON><PERSON><PERSON><PERSON> 1000", "SSE.Views.FormatSettingsDialog.textSymbols": "Symboly", "SSE.Views.FormatSettingsDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtAccounting": "Účetní", "SSE.Views.FormatSettingsDialog.txtAs10": "<PERSON><PERSON><PERSON> deseti<PERSON> (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "<PERSON><PERSON><PERSON> (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "<PERSON><PERSON><PERSON> (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "<PERSON><PERSON><PERSON> (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "<PERSON><PERSON><PERSON> (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "<PERSON><PERSON><PERSON> (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Měna", "SSE.Views.FormatSettingsDialog.txtCustom": "Vlastní", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Zadávejte prosím vlastní číselný formát obezřetně. Tabulkový editor nekontroluje součtové hodnoty formátu na chyby, kter<PERSON> mohou ovlivnit soubor xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "Datum", "SSE.Views.FormatSettingsDialog.txtFraction": "Zlomek", "SSE.Views.FormatSettingsDialog.txtGeneral": "Obecné", "SSE.Views.FormatSettingsDialog.txtNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtPercentage": "Procento", "SSE.Views.FormatSettingsDialog.txtSample": "Ukázka:", "SSE.Views.FormatSettingsDialog.txtScientific": "Vědecké", "SSE.Views.FormatSettingsDialog.txtText": "Text", "SSE.Views.FormatSettingsDialog.txtTime": "Čas", "SSE.Views.FormatSettingsDialog.txtUpto1": "Až na jednu číslici (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "<PERSON>ž na dvě číslice (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "<PERSON>ž na tři číslice (131/135)", "SSE.Views.FormulaDialog.sDescription": "<PERSON><PERSON>", "SSE.Views.FormulaDialog.textGroupDescription": "<PERSON><PERSON><PERSON><PERSON> sku<PERSON>u funkcí", "SSE.Views.FormulaDialog.textListDescription": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtRecommended": "Doporučeno", "SSE.Views.FormulaDialog.txtSearch": "Hledat", "SSE.Views.FormulaDialog.txtTitle": "Vlož<PERSON>", "SSE.Views.FormulaTab.capBtnRemoveArr": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.capBtnTraceDep": "Sledovat následovníky", "SSE.Views.FormulaTab.capBtnTracePrec": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "SSE.Views.FormulaTab.textAutomatic": "<PERSON>ky", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Přepočítat aktuální list", "SSE.Views.FormulaTab.textCalculateWorkbook": "Propočítat sešit", "SSE.Views.FormulaTab.textManual": "R<PERSON>č<PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculate": "Přepočítat", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Propočítat celý sešit", "SSE.Views.FormulaTab.tipRemoveArr": "<PERSON><PERSON><PERSON><PERSON> nak<PERSON>lené pomocí Sledovat předchůdce nebo Sledovat následovníky", "SSE.Views.FormulaTab.tipShowFormulas": "Zobrazení vzorce v každé buňce namísto výsledné hodnoty", "SSE.Views.FormulaTab.tipTraceDep": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>, k<PERSON><PERSON> b<PERSON> jsou ovlivněny hodnotou vybrané buň<PERSON>.", "SSE.Views.FormulaTab.tipTracePrec": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON>, k<PERSON><PERSON> b<PERSON> jsou ovlivněny hodnotou vybrané buňky", "SSE.Views.FormulaTab.tipWatch": "Přidat buňky do seznamu okna kukátka", "SSE.Views.FormulaTab.txtAdditional": "Dalš<PERSON>", "SSE.Views.FormulaTab.txtAutosum": "Automatický součet", "SSE.Views.FormulaTab.txtAutosumTip": "Su<PERSON>ce", "SSE.Views.FormulaTab.txtCalculation": "Přepočítání", "SSE.Views.FormulaTab.txtFormula": "Funkce", "SSE.Views.FormulaTab.txtFormulaTip": "Vlož<PERSON>", "SSE.Views.FormulaTab.txtMore": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtRecent": "Nedávno p<PERSON>žité", "SSE.Views.FormulaTab.txtRemDep": "<PERSON><PERSON><PERSON><PERSON>ledovn<PERSON>", "SSE.Views.FormulaTab.txtRemPrec": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "SSE.Views.FormulaTab.txtShowFormulas": "Zobrazit vzorce", "SSE.Views.FormulaTab.txtWatch": "<PERSON>no kukát<PERSON>", "SSE.Views.FormulaWizard.textAny": "libovolné", "SSE.Views.FormulaWizard.textArgument": "Argument", "SSE.Views.FormulaWizard.textFunction": "Funkce", "SSE.Views.FormulaWizard.textFunctionRes": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textHelp": "Nápověda k této funkci", "SSE.Views.FormulaWizard.textLogical": "Logické", "SSE.Views.FormulaWizard.textNoArgs": "<PERSON><PERSON> neo<PERSON> argumenty", "SSE.Views.FormulaWizard.textNoArgsDesc": "this argument has no description", "SSE.Views.FormulaWizard.textNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textReadMore": "Čtěte více", "SSE.Views.FormulaWizard.textRef": "odkaz", "SSE.Views.FormulaWizard.textText": "text", "SSE.Views.FormulaWizard.textTitle": "Argumenty funkce", "SSE.Views.FormulaWizard.textValue": "Výsledek vzorce", "SSE.Views.GoalSeekDlg.textChangingCell": "Změnou buňky", "SSE.Views.GoalSeekDlg.textDataRangeError": "Vzorci chybí rozsah", "SSE.Views.GoalSeekDlg.textMustContainFormula": "Buňka musí obsahovat vzorec", "SSE.Views.GoalSeekDlg.textMustContainValue": "Buňka musí obsahovat hodnotu", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Výsledkem vzorce v buňce musí být číslo", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Odkaz musí být na jednu buňku", "SSE.Views.GoalSeekDlg.textSelectData": "Vybrat data", "SSE.Views.GoalSeekDlg.textSetCell": "Nastavit buňku", "SSE.Views.GoalSeekDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.GoalSeekDlg.textToValue": "do hodnoty", "SSE.Views.GoalSeekDlg.txtEmpty": "Toto pole je povinné", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Hodnotu nelze použít. Může být v<PERSON>žadováno celé nebo desetinné číslo.", "SSE.Views.GoalSeekStatusDlg.textContinue": "Po<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Aktuální hodnota", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Hledání cíle s buň<PERSON><PERSON> {0} našlo řešení.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Hledání cíle s buň<PERSON>u {0} možná nenašlo <PERSON>ení.", "SSE.Views.GoalSeekStatusDlg.textPause": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>le s bu<PERSON><PERSON>u {0} p<PERSON><PERSON> b<PERSON><PERSON> #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "<PERSON><PERSON>", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Cílová hodnota:", "SSE.Views.GoalSeekStatusDlg.textTitle": "<PERSON><PERSON> h<PERSON><PERSON><PERSON> c<PERSON>", "SSE.Views.HeaderFooterDialog.textAlign": "Zarovnat vůči okrajům stránky", "SSE.Views.HeaderFooterDialog.textAll": "Všechny stránky", "SSE.Views.HeaderFooterDialog.textBold": "Tučn<PERSON>", "SSE.Views.HeaderFooterDialog.textCenter": "<PERSON> střed", "SSE.Views.HeaderFooterDialog.textColor": "<PERSON>va textu", "SSE.Views.HeaderFooterDialog.textDate": "Datum", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Odlišná první stránka", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Rozdílné liché a sudé str<PERSON>ky", "SSE.Views.HeaderFooterDialog.textEven": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.HeaderFooterDialog.textFileName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textFirst": "První strán<PERSON>", "SSE.Views.HeaderFooterDialog.textFooter": "Zápatí", "SSE.Views.HeaderFooterDialog.textHeader": "Záhlaví", "SSE.Views.HeaderFooterDialog.textImage": "Obrázek", "SSE.Views.HeaderFooterDialog.textInsert": "Vložit", "SSE.Views.HeaderFooterDialog.textItalic": "Skloněné", "SSE.Views.HeaderFooterDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textMaxError": "Textov<PERSON>, k<PERSON><PERSON>, je p<PERSON><PERSON><PERSON>. Snižte počet použitých znaků.", "SSE.Views.HeaderFooterDialog.textNewColor": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textOdd": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.HeaderFooterDialog.textPageCount": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textPageNum": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textPresets": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textRight": "Vpravo", "SSE.Views.HeaderFooterDialog.textScale": "Měnit velikost podle dokumentu", "SSE.Views.HeaderFooterDialog.textSheet": "Název listu", "SSE.Views.HeaderFooterDialog.textStrikeout": "Přeškrtnutí", "SSE.Views.HeaderFooterDialog.textSubscript": "Dolní index", "SSE.Views.HeaderFooterDialog.textSuperscript": "Horní index", "SSE.Views.HeaderFooterDialog.textTime": "Čas", "SSE.Views.HeaderFooterDialog.textTitle": "Nastavení záhlaví/zápatí", "SSE.Views.HeaderFooterDialog.textUnderline": "Podtržené", "SSE.Views.HeaderFooterDialog.tipFontName": "Písmo", "SSE.Views.HeaderFooterDialog.tipFontSize": "Velikost písma", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Zobrazit", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Odkaz na", "SSE.Views.HyperlinkSettingsDialog.strRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strSheet": "List", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Kopírovat", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Vybraný rozsah", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Zde zadejte titulek", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Zde napište odkaz", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "<PERSON><PERSON> zadej<PERSON> popisek", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Externí odkaz", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Získat odkaz", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Vnitřní roz<PERSON> dat", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.HyperlinkSettingsDialog.textNames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Vybrat data", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "<PERSON><PERSON><PERSON><PERSON> soubor", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Listy", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Text nápovědy", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Nastavení hypertextového odkazu", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Toto pole je povinné", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "<PERSON><PERSON><PERSON><PERSON> pole by <PERSON><PERSON><PERSON> b<PERSON>t URL adresa ve formátu „http://www.example.com“", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Toto pole je omezeno na 2083 znaků", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Zadejte webovou adresu nebo vyberte soubor", "SSE.Views.ImageSettings.strTransparency": "Průhlednost", "SSE.Views.ImageSettings.textAdvanced": "Zobrazit pokročilá nastavení", "SSE.Views.ImageSettings.textCrop": "Oříznout", "SSE.Views.ImageSettings.textCropFill": "Výplň", "SSE.Views.ImageSettings.textCropFit": "Přizpůsobit", "SSE.Views.ImageSettings.textCropToShape": "Oříz<PERSON>ut podle tvaru", "SSE.Views.ImageSettings.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textEditObject": "Upravit objekt", "SSE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textFromFile": "<PERSON><PERSON> souboru", "SSE.Views.ImageSettings.textFromStorage": "Z úložiště", "SSE.Views.ImageSettings.textFromUrl": "Z adresy URL", "SSE.Views.ImageSettings.textHeight": "Výška", "SSE.Views.ImageSettings.textHint270": "Otočit o 90° doleva", "SSE.Views.ImageSettings.textHint90": "Otočit o 90° doprava", "SSE.Views.ImageSettings.textHintFlipH": "Převrátit vodorovně", "SSE.Views.ImageSettings.textHintFlipV": "Př<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.ImageSettings.textInsert": "Nahradit obrázek", "SSE.Views.ImageSettings.textKeepRatio": "Konstantní proporce", "SSE.Views.ImageSettings.textOriginalSize": "Skutečná velikost", "SSE.Views.ImageSettings.textRecentlyUsed": "Nedávno p<PERSON>žité", "SSE.Views.ImageSettings.textResetCrop": "Reset crop", "SSE.Views.ImageSettings.textRotate90": "Otočit o 90°", "SSE.Views.ImageSettings.textRotation": "Otočení", "SSE.Views.ImageSettings.textSize": "Velikost", "SSE.Views.ImageSettings.textWidth": "Šířka", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Neposouvat nebo neměnit velikost s buňkami", "SSE.Views.ImageSettingsAdvanced.textAlt": "Alternativní text", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Alternativní textová reprezentace informací vizuálního objektu, která bude čtena lidem se zrakovým nebo kognitivním postižením, aby jim pomohla lépe porozumět informacím, které se nacházejí v obrázku, grafu, obrazci nebo v tabulce.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAngle": "Úhel", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Převrá<PERSON>né", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Vodorovně", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Přesouvat ale neměnit velikost společně s buňkami", "SSE.Views.ImageSettingsAdvanced.textRotation": "Otočení", "SSE.Views.ImageSettingsAdvanced.textSnap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textTitle": "Obrázek – pokročilá nastavení", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Přesouvat a měnit velikost společně s buňkami", "SSE.Views.ImageSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "SSE.Views.ImportFromXmlDialog.textDestination": "Vybrat místo pro vložení dat", "SSE.Views.ImportFromXmlDialog.textExist": "Existující list", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Neplatný rozsah buněk", "SSE.Views.ImportFromXmlDialog.textNew": "Nový list", "SSE.Views.ImportFromXmlDialog.textSelectData": "Vybrat data", "SSE.Views.ImportFromXmlDialog.textTitle": "Importovat data", "SSE.Views.ImportFromXmlDialog.txtEmpty": "Toto pole je povinné", "SSE.Views.LeftMenu.ariaLeftMenu": "Nabídka vlevo", "SSE.Views.LeftMenu.tipAbout": "O aplikaci", "SSE.Views.LeftMenu.tipChat": "Cha<PERSON>", "SSE.Views.LeftMenu.tipComments": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipFile": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "Z<PERSON>uv<PERSON><PERSON> moduly", "SSE.Views.LeftMenu.tipSearch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSpellcheck": "Kontrola pra<PERSON>pisu", "SSE.Views.LeftMenu.tipSupport": "Zpětná vazba a technická podpora", "SSE.Views.LeftMenu.txtDeveloper": "REŽIM PRO VÝVOJÁŘE", "SSE.Views.LeftMenu.txtEditor": "Tabul<PERSON>ý editor", "SSE.Views.LeftMenu.txtLimit": "Omezit přístup", "SSE.Views.LeftMenu.txtTrial": "ZKUŠEBNÍ REŽIM", "SSE.Views.LeftMenu.txtTrialDev": "Zkušební vývojářský režim", "SSE.Views.MacroDialog.textMacro": "Název makra", "SSE.Views.MacroDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ma<PERSON>ro", "SSE.Views.MainSettingsPrint.okButtonText": "Uložit", "SSE.Views.MainSettingsPrint.strBottom": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strLandscape": "Na šířku", "SSE.Views.MainSettingsPrint.strLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPortrait": "Na výšku", "SSE.Views.MainSettingsPrint.strPrint": "Tisk", "SSE.Views.MainSettingsPrint.strPrintTitles": "Tisk názvů", "SSE.Views.MainSettingsPrint.strRight": "Vpravo", "SSE.Views.MainSettingsPrint.strTop": "Nahoře", "SSE.Views.MainSettingsPrint.textActualSize": "Skutečná velikost", "SSE.Views.MainSettingsPrint.textCustom": "Vlastní", "SSE.Views.MainSettingsPrint.textCustomOptions": "<PERSON>last<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textFitCols": "Přizpůsobit všechny sloupce na jedné stránce", "SSE.Views.MainSettingsPrint.textFitPage": "Přizpůsobit list jed<PERSON><PERSON> s<PERSON>", "SSE.Views.MainSettingsPrint.textFitRows": "Přizpůsobit všechny řádky na jedné stránce", "SSE.Views.MainSettingsPrint.textPageOrientation": "<PERSON><PERSON> s<PERSON>", "SSE.Views.MainSettingsPrint.textPageScaling": "Škálování", "SSE.Views.MainSettingsPrint.textPageSize": "Velikos<PERSON> s<PERSON>ánky", "SSE.Views.MainSettingsPrint.textPrintGrid": "Vytisknout mřížku", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Vytisknout záhlaví řádků a sloupců", "SSE.Views.MainSettingsPrint.textRepeat": "Opakovat...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "<PERSON><PERSON><PERSON> slou<PERSON>", "SSE.Views.MainSettingsPrint.textRepeatTop": "Nahoře opak<PERSON>", "SSE.Views.MainSettingsPrint.textSettings": "Nastavení pro", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Stávající pojmenované rozsahy nelze upravovat a nové nyní nelze vytvořit<br>proto<PERSON><PERSON> některé z nich jsou právě upravovány.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Varování", "SSE.Views.NamedRangeEditDlg.strWorkbook": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textDataRange": "<PERSON><PERSON><PERSON><PERSON> dat", "SSE.Views.NamedRangeEditDlg.textExistName": "CHYBA! Rozsah se stejným názvem už existuje", "SSE.Views.NamedRangeEditDlg.textInvalidName": "<PERSON> třeba, aby název začínal písmenem nebo podtržítkem a neobsahoval neplatné zna<PERSON>.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.NamedRangeEditDlg.textIsLocked": "CHYBA! Prvek je upravován jiným uživatelem", "SSE.Views.NamedRangeEditDlg.textName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textReservedName": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> se p<PERSON><PERSON><PERSON><PERSON><PERSON>, je už uveden ve vzorcích buněk. Použijte nějaký jiný název.", "SSE.Views.NamedRangeEditDlg.textScope": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textSelectData": "Vybrat data", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Toto pole je povinné", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Upravi<PERSON>", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Nový název", "SSE.Views.NamedRangePasteDlg.textNames": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NamedRangePasteDlg.txtTitle": "Vložit název", "SSE.Views.NameManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.guestText": "Návštěvník", "SSE.Views.NameManagerDlg.lockText": "Uzamčeno", "SSE.Views.NameManagerDlg.textDataRange": "<PERSON><PERSON><PERSON><PERSON> dat", "SSE.Views.NameManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEmpty": "Doposud nebyly vytvořeny žádné pojmenované roz<PERSON>hy.<br>Vytvořte alespoň jeden takový a objeví se v této kolonce.", "SSE.Views.NameManagerDlg.textFilter": "Filtr", "SSE.Views.NameManagerDlg.textFilterAll": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterDefNames": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterSheet": "<PERSON>bor názvů do listu", "SSE.Views.NameManagerDlg.textFilterTableNames": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Obor názvů do sešitu", "SSE.Views.NameManagerDlg.textNew": "Nový", "SSE.Views.NameManagerDlg.textnoNames": "Pro daný filtr se nepodařilo nalézt žádné pojmenované roz<PERSON>.", "SSE.Views.NameManagerDlg.textRanges": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textScope": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textWorkbook": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.tipIsLocked": "Prvek je upravován jiným uživatelem.", "SSE.Views.NameManagerDlg.txtTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> názvů", "SSE.Views.NameManagerDlg.warnDelete": "Opravdu chcete název {0} smazat?", "SSE.Views.PageMarginsDialog.textBottom": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textCenter": "Vycentrováno na stránce", "SSE.Views.PageMarginsDialog.textHor": "Vodorovně", "SSE.Views.PageMarginsDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textRight": "Vpravo", "SSE.Views.PageMarginsDialog.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTop": "Nahoře", "SSE.Views.PageMarginsDialog.textVert": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textWarning": "Varování", "SSE.Views.PageMarginsDialog.warnCheckMargings": "<PERSON><PERSON><PERSON> ne<PERSON> správn<PERSON>", "SSE.Views.ParagraphSettings.strLineHeight": "Řádkování", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Mezery mezi odstavci", "SSE.Views.ParagraphSettings.strSpacingAfter": "Po", "SSE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAdvanced": "Zobrazit pokročilá nastavení", "SSE.Views.ParagraphSettings.textAt": "Výška", "SSE.Views.ParagraphSettings.textAtLeast": "Alespoň", "SSE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textExact": "Přesně", "SSE.Views.ParagraphSettings.txtAutoText": "<PERSON>ky", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Specifikované tabulátory se objeví v tomto poli", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Všechno velkými", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Dvojité <PERSON>š<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Odsazení", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Řádkování", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Vpravo", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "Po", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Speciální", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Od", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Písmo", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Odsazení a mezery", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Malá písmena", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Mezery", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Přeškrtnutí", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Dolní index", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Horní index", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Tabulátory", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Zarovnání", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Vícero", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Mezery mezi písmeny", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Výchozí tabulátor", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Efekty", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Přesně", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Prvn<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Do bloku", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(ž<PERSON><PERSON><PERSON>)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Odstranit", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Odstranit vše", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Upřesnit", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "<PERSON> střed", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "<PERSON><PERSON><PERSON> ta<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "Vpravo", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Odstavec – pokročilá nastavení", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "<PERSON>ky", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Vymazat", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Vzorec", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Items Name", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "Nový", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Calculated Items in", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "rovná se", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "Nekončí na", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "obs<PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "mezi", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "ne mezi", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "nerovná se", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "je v<PERSON><PERSON><PERSON><PERSON> než", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "je vet<PERSON><PERSON> než nebo rovno", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "je men<PERSON><PERSON> ne<PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "je men<PERSON><PERSON> než nebo rovno", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "začíná na", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "nezačíná na", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "končí na", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Zobrazit položky se štítkem:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Zobrazit položky pro které:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Použijte ? pro zastoupení libovolného znaku", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Použijte * pro zastoupení libovolné řady znaků", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "a", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Filtrování štítků", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Filtr hodnot", "SSE.Views.PivotGroupDialog.textAuto": "<PERSON>ky", "SSE.Views.PivotGroupDialog.textBy": "Od", "SSE.Views.PivotGroupDialog.textDays": "Dny", "SSE.Views.PivotGroupDialog.textEnd": "Končí na", "SSE.Views.PivotGroupDialog.textError": "<PERSON> třeba, aby obsahem této kolonky byla č<PERSON> hodnota", "SSE.Views.PivotGroupDialog.textGreaterError": "Číslo na konci musí být větší než číslo na začátku", "SSE.Views.PivotGroupDialog.textHour": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMonth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textNumDays": "Počet dnů", "SSE.Views.PivotGroupDialog.textQuart": "Čtvrtiny", "SSE.Views.PivotGroupDialog.textSec": "Sekundy", "SSE.Views.PivotGroupDialog.textStart": "Začínající v", "SSE.Views.PivotGroupDialog.textYear": "Roky", "SSE.Views.PivotGroupDialog.txtTitle": "Seskupování", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "You can use Calculated Items for basic calculations between different items within a single field", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Vzorec", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Insert into formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "Položka", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Název <PERSON>žky", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Polož<PERSON>", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Čtěte více", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Insert Calculated Item in", "SSE.Views.PivotSettings.textAdvanced": "Zobrazit pokročilá nastavení", "SSE.Views.PivotSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textFields": "<PERSON><PERSON><PERSON><PERSON> pole", "SSE.Views.PivotSettings.textFilters": "Filtry", "SSE.Views.PivotSettings.textRows": "Řádky", "SSE.Views.PivotSettings.textValues": "Hodnoty", "SSE.Views.PivotSettings.txtAddColumn": "Přidat do sloupců", "SSE.Views.PivotSettings.txtAddFilter": "Přidat do filtrů", "SSE.Views.PivotSettings.txtAddRow": "Přidat do řádků", "SSE.Views.PivotSettings.txtAddValues": "Přidat do hodnot", "SSE.Views.PivotSettings.txtFieldSettings": "<PERSON><PERSON><PERSON><PERSON> pole", "SSE.Views.PivotSettings.txtMoveBegin": "Přesunout na začátek", "SSE.Views.PivotSettings.txtMoveColumn": "Přesunout do sloupců", "SSE.Views.PivotSettings.txtMoveDown": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtMoveEnd": "Přesunout na konec", "SSE.Views.PivotSettings.txtMoveFilter": "Přesunout do filtrů", "SSE.Views.PivotSettings.txtMoveRow": "Přesunout do řádků", "SSE.Views.PivotSettings.txtMoveUp": "Př<PERSON>unout nahoru", "SSE.Views.PivotSettings.txtMoveValues": "Přesunout do hodnot", "SSE.Views.PivotSettings.txtRemove": "<PERSON><PERSON><PERSON><PERSON> pole", "SSE.Views.PivotSettingsAdvanced.strLayout": "Název a rozvržení", "SSE.Views.PivotSettingsAdvanced.textAlt": "Alternativní text", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Alternativní textová reprezentace informací vizuálního objektu, která bude čtena lidem se zrakovým nebo kognitivním postižením, aby jim pomohla lépe porozumět informacím, které se nacházejí v obrázku, obrazci, grafu nebo v tabulce.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Při aktualizaci automaticky přizpůsobit šířku sloupců", "SSE.Views.PivotSettingsAdvanced.textDataRange": "<PERSON><PERSON><PERSON><PERSON> dat", "SSE.Views.PivotSettingsAdvanced.textDataSource": "<PERSON><PERSON><PERSON><PERSON> dat", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Zobrazit pole ve oblasti pro výkaz filtru", "SSE.Views.PivotSettingsAdvanced.textDown": "<PERSON><PERSON><PERSON>, pak příčně", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Celkov<PERSON> souč<PERSON>", "SSE.Views.PivotSettingsAdvanced.textHeaders": "<PERSON><PERSON><PERSON><PERSON><PERSON> pole", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.PivotSettingsAdvanced.textOver": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pak dolů", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Vybrat data", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Zobrazit pro sloupce", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Zobrazit záhlaví pro řádky a sloupce", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Zobrazit pro řádky", "SSE.Views.PivotSettingsAdvanced.textTitle": "Kontingenční tabul<PERSON> – pokročilá nastavení", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Počet polí filtru výkazu na sloupec ", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Počet polí filtru výkazu na řádek", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Toto pole je povinné", "SSE.Views.PivotSettingsAdvanced.txtName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotShowDetailDialog.textDescription": "<PERSON><PERSON><PERSON><PERSON> pole obsahuj<PERSON><PERSON><PERSON> informace, k<PERSON><PERSON>te zobrazit:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Zobrazit více informací", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.capGrandTotals": "Celkov<PERSON> souč<PERSON>", "SSE.Views.PivotTable.capLayout": "Rozvržení výkazu", "SSE.Views.PivotTable.capSubtotals": "Dílčí souč<PERSON>", "SSE.Views.PivotTable.mniBottomSubtotals": "Zobrazit všechny dílčí součty na konci skupiny", "SSE.Views.PivotTable.mniInsertBlankLine": "Za každou z položek vložit prázdný řádek", "SSE.Views.PivotTable.mniLayoutCompact": "Zobrazit ve zhuštěné podobě", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Neopakovat štítky všech položek", "SSE.Views.PivotTable.mniLayoutOutline": "Zobrazit jako o<PERSON>", "SSE.Views.PivotTable.mniLayoutRepeat": "Zopakovat všechny štítky položky", "SSE.Views.PivotTable.mniLayoutTabular": "Zobrazit v podobě tabulky", "SSE.Views.PivotTable.mniNoSubtotals": "Nezobrazovat dílčí součty", "SSE.Views.PivotTable.mniOffTotals": "Vypnuto <PERSON>dky a sloupce", "SSE.Views.PivotTable.mniOnColumnsTotals": "Zapnuto pouze pro sloupce", "SSE.Views.PivotTable.mniOnRowsTotals": "<PERSON>ap<PERSON><PERSON> p<PERSON>", "SSE.Views.PivotTable.mniOnTotals": "Zapnuto pro řádky a sloupce", "SSE.Views.PivotTable.mniRemoveBlankLine": "Odebrat prázdný řádek za každou z položek", "SSE.Views.PivotTable.mniTopSubtotals": "Zobrazit všechny dílčí součty nad skupinou", "SSE.Views.PivotTable.textColBanded": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.PivotTable.textColHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.PivotTable.textRowBanded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.textRowHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.tipCalculatedItems": "Calculated items", "SSE.Views.PivotTable.tipCreatePivot": "vložit kontingenční tabulku", "SSE.Views.PivotTable.tipGrandTotals": "Zobrazit nebo skrýt celkové součty", "SSE.Views.PivotTable.tipRefresh": "Aktualizovat informace z datového zdroje", "SSE.Views.PivotTable.tipRefreshCurrent": "Aktualizovat informace ze zdroje data pro aktuální tabulku", "SSE.Views.PivotTable.tipSelect": "Vybrat celou kontingenční tabulku", "SSE.Views.PivotTable.tipSubtotals": "Zobrazit nebo skrýt dílčí součty", "SSE.Views.PivotTable.txtCalculatedItems": "Calculated Items", "SSE.Views.PivotTable.txtCollapseEntire": "Sbalit celé pole", "SSE.Views.PivotTable.txtCreate": "Vložit tabulku", "SSE.Views.PivotTable.txtExpandEntire": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> celé pole", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Vlastní", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Tmavé", "SSE.Views.PivotTable.txtGroupPivot_Light": "Světl<PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Střední", "SSE.Views.PivotTable.txtPivotTable": "Kontingenční tabulka", "SSE.Views.PivotTable.txtRefresh": "Aktualizovat", "SSE.Views.PivotTable.txtRefreshAll": "Aktualizovat vše", "SSE.Views.PivotTable.txtSelect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Tmavý styl kontingenční tabulky", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Světlý styl kontingenční tabulky", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Střední styl kontingenční tabulky", "SSE.Views.PrintSettings.btnDownload": "Uložit a stáhnout si", "SSE.Views.PrintSettings.btnExport": "Uložit a Exportovat", "SSE.Views.PrintSettings.btnPrint": "Uložit a vytisknout", "SSE.Views.PrintSettings.strBottom": "<PERSON><PERSON>", "SSE.Views.PrintSettings.strLandscape": "Na šířku", "SSE.Views.PrintSettings.strLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPortrait": "Na výšku", "SSE.Views.PrintSettings.strPrint": "Tisk", "SSE.Views.PrintSettings.strPrintTitles": "Tisk názvů", "SSE.Views.PrintSettings.strRight": "Vpravo", "SSE.Views.PrintSettings.strShow": "Zobrazit", "SSE.Views.PrintSettings.strTop": "Nahoře", "SSE.Views.PrintSettings.textActiveSheets": "Aktivní listy", "SSE.Views.PrintSettings.textActualSize": "Skutečná velikost", "SSE.Views.PrintSettings.textAllSheets": "Všechny listy", "SSE.Views.PrintSettings.textCurrentSheet": "Aktuální list", "SSE.Views.PrintSettings.textCustom": "Vlastní", "SSE.Views.PrintSettings.textCustomOptions": "<PERSON>last<PERSON><PERSON>", "SSE.Views.PrintSettings.textFitCols": "Přizpůsobit všechny sloupce na jedné stránce", "SSE.Views.PrintSettings.textFitPage": "Přizpůsobit list jed<PERSON><PERSON> s<PERSON>", "SSE.Views.PrintSettings.textFitRows": "Přizpůsobit všechny řádky na jedné stránce", "SSE.Views.PrintSettings.textHideDetails": "<PERSON>k<PERSON><PERSON><PERSON> podro<PERSON>", "SSE.Views.PrintSettings.textIgnore": "Ignorovat oblast tisku", "SSE.Views.PrintSettings.textLayout": "Rozvržení", "SSE.Views.PrintSettings.textMarginsNarrow": "Úzké", "SSE.Views.PrintSettings.textMarginsNormal": "Normální", "SSE.Views.PrintSettings.textMarginsWide": "Šířka", "SSE.Views.PrintSettings.textPageOrientation": "<PERSON><PERSON> s<PERSON>", "SSE.Views.PrintSettings.textPages": "<PERSON><PERSON><PERSON><PERSON>:", "SSE.Views.PrintSettings.textPageScaling": "Škálování", "SSE.Views.PrintSettings.textPageSize": "Velikos<PERSON> s<PERSON>ánky", "SSE.Views.PrintSettings.textPrintGrid": "Vytisknout mřížku", "SSE.Views.PrintSettings.textPrintHeadings": "Vytisknout záhlaví řádků a sloupců", "SSE.Views.PrintSettings.textPrintRange": "<PERSON><PERSON><PERSON><PERSON> tisku", "SSE.Views.PrintSettings.textRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textRepeat": "Opakovat...", "SSE.Views.PrintSettings.textRepeatLeft": "<PERSON><PERSON><PERSON> slou<PERSON>", "SSE.Views.PrintSettings.textRepeatTop": "Nahoře opak<PERSON>", "SSE.Views.PrintSettings.textSelection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "Nastavení listu", "SSE.Views.PrintSettings.textShowDetails": "Zobrazit podrobnosti", "SSE.Views.PrintSettings.textShowGrid": "Zobrazit mřížku", "SSE.Views.PrintSettings.textShowHeadings": "Zobrazit záhlaví řádků a sloupců", "SSE.Views.PrintSettings.textTitle": "Nastavení tisku", "SSE.Views.PrintSettings.textTitlePDF": "Nastavení PDF", "SSE.Views.PrintSettings.textTo": "do", "SSE.Views.PrintSettings.txtMarginsLast": "Nejnovější vlastní", "SSE.Views.PrintTitlesDialog.textFirstCol": "První sloupec", "SSE.Views.PrintTitlesDialog.textFirstRow": "Prvn<PERSON>", "SSE.Views.PrintTitlesDialog.textFrozenCols": "<PERSON><PERSON><PERSON><PERSON> slou<PERSON>", "SSE.Views.PrintTitlesDialog.textFrozenRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.PrintTitlesDialog.textLeft": "<PERSON><PERSON><PERSON> slou<PERSON>", "SSE.Views.PrintTitlesDialog.textNoRepeat": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textRepeat": "Opakovat...", "SSE.Views.PrintTitlesDialog.textSelectRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textTitle": "Tisk názvů", "SSE.Views.PrintTitlesDialog.textTop": "Nahoře opak<PERSON>", "SSE.Views.PrintWithPreview.txtActiveSheets": "Aktivní listy", "SSE.Views.PrintWithPreview.txtActualSize": "Skutečná velikost", "SSE.Views.PrintWithPreview.txtAllSheets": "Všechny listy", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Uplatnit na všechny listy", "SSE.Views.PrintWithPreview.txtBothSides": "Oboustranný tisk", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "Překlápí stránky po delší hraně", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "Překlápí stránky po kratší hraně", "SSE.Views.PrintWithPreview.txtBottom": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCopies": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Stávající list", "SSE.Views.PrintWithPreview.txtCustom": "Vlastní", "SSE.Views.PrintWithPreview.txtCustomOptions": "<PERSON>last<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtEmptyTable": "Není co vytisknout, protože tabulka je prázdná.", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "Číslo první s<PERSON>:", "SSE.Views.PrintWithPreview.txtFitCols": "Přizpůsobit všechny sloupce na jedné stránce", "SSE.Views.PrintWithPreview.txtFitPage": "Přizpůsobit list jed<PERSON><PERSON> s<PERSON>", "SSE.Views.PrintWithPreview.txtFitRows": "Přizpůsobit všechny řádky na jedné stránce", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Mřížky a nadpisy", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Nastavení záhlaví/zápatí", "SSE.Views.PrintWithPreview.txtIgnore": "Ignorovat oblast tisku", "SSE.Views.PrintWithPreview.txtLandscape": "Na šířku", "SSE.Views.PrintWithPreview.txtLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtMarginsLast": "Nejnovější vlastní", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "Úzké", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Normální", "SSE.Views.PrintWithPreview.txtMarginsWide": "Šířka", "SSE.Views.PrintWithPreview.txtOf": "z {0}", "SSE.Views.PrintWithPreview.txtOneSide": "Jednostranný tisk", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Tisknout pouze na jednu stránku", "SSE.Views.PrintWithPreview.txtPage": "Strán<PERSON>", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Neplat<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageOrientation": "<PERSON><PERSON> s<PERSON>", "SSE.Views.PrintWithPreview.txtPages": "<PERSON><PERSON><PERSON><PERSON>:", "SSE.Views.PrintWithPreview.txtPageSize": "Velikos<PERSON> s<PERSON>ánky", "SSE.Views.PrintWithPreview.txtPortrait": "Na výšku", "SSE.Views.PrintWithPreview.txtPrint": "Tisk", "SSE.Views.PrintWithPreview.txtPrintGrid": "Vytisknout mřížku", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Vytisknout záhlaví řádků a sloupců", "SSE.Views.PrintWithPreview.txtPrintRange": "<PERSON><PERSON><PERSON><PERSON> tisku", "SSE.Views.PrintWithPreview.txtPrintSides": "Tisknout strany", "SSE.Views.PrintWithPreview.txtPrintTitles": "Tisk názvů", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Tisk do PDF", "SSE.Views.PrintWithPreview.txtRepeat": "<PERSON><PERSON><PERSON><PERSON>…", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "<PERSON><PERSON><PERSON> slou<PERSON>", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Nahoře opak<PERSON>", "SSE.Views.PrintWithPreview.txtRight": "Vpravo", "SSE.Views.PrintWithPreview.txtSave": "Uložit", "SSE.Views.PrintWithPreview.txtScaling": "Změna <PERSON>", "SSE.Views.PrintWithPreview.txtSelection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Nastavení listu", "SSE.Views.PrintWithPreview.txtSheet": "List: {0}", "SSE.Views.PrintWithPreview.txtTo": "do", "SSE.Views.PrintWithPreview.txtTop": "Nahoře", "SSE.Views.ProtectDialog.textExistName": "CHYBA! Rozsah s takovým názvem už existuje", "SSE.Views.ProtectDialog.textInvalidName": "Název musí začínat písmenem a musí obsahovat pouze písmena, čísla nebo mezery.", "SSE.Views.ProtectDialog.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.ProtectDialog.textSelectData": "Vybrat data", "SSE.Views.ProtectDialog.txtAllow": "Umožnit všem uživatelům tohoto listu", "SSE.Views.ProtectDialog.txtAllowDescription": "Můžete odemknout rozsah pro editaci.", "SSE.Views.ProtectDialog.txtAllowRanges": "Umožnit upravovat rozsahy", "SSE.Views.ProtectDialog.txtAutofilter": "Použít <PERSON>", "SSE.Views.ProtectDialog.txtDelCols": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.ProtectDialog.txtDelRows": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtEmpty": "Toto pole je povinné", "SSE.Views.ProtectDialog.txtFormatCells": "Formátovat buňky", "SSE.Views.ProtectDialog.txtFormatCols": "Formátovat sloupce", "SSE.Views.ProtectDialog.txtFormatRows": "Formátovat <PERSON>dky", "SSE.Views.ProtectDialog.txtIncorrectPwd": "<PERSON><PERSON> se neshoduj<PERSON>", "SSE.Views.ProtectDialog.txtInsCols": "Vlož<PERSON> s<PERSON>", "SSE.Views.ProtectDialog.txtInsHyper": "Vložit hypertextový odkaz", "SSE.Views.ProtectDialog.txtInsRows": "Vlož<PERSON>", "SSE.Views.ProtectDialog.txtObjs": "Upravit objekty", "SSE.Views.ProtectDialog.txtOptional": "volitelné", "SSE.Views.ProtectDialog.txtPassword": "He<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPivot": "Použít kontingenční tabulku a kontingenční graf", "SSE.Views.ProtectDialog.txtProtect": "Zabezpečení", "SSE.Views.ProtectDialog.txtRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRangeName": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON> he<PERSON>", "SSE.Views.ProtectDialog.txtScen": "Upravi<PERSON>", "SSE.Views.ProtectDialog.txtSelLocked": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "SSE.Views.ProtectDialog.txtSelUnLocked": "<PERSON>ybrat odemč<PERSON>é b<PERSON>", "SSE.Views.ProtectDialog.txtSheetDescription": "Pokud chcete zabránit nechtěným změnám ostatními, omezte jejich možnost upravovat.", "SSE.Views.ProtectDialog.txtSheetTitle": "Zabezpečit list", "SSE.Views.ProtectDialog.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtWarning": "Varování: Ztracené nebo zapomenuté heslo nelze obnovit. Uložte si ho na bezpečném místě.", "SSE.Views.ProtectDialog.txtWBDescription": "Můžete p<PERSON>žít he<PERSON>lo k zabránění zobrazení, <PERSON><PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON>, odstranění, <PERSON><PERSON><PERSON><PERSON><PERSON>, nebo přejmenování souboru jinými <PERSON>. ", "SSE.Views.ProtectDialog.txtWBTitle": "Zabezpečit strukturu sešitu", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "Anonymní", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Kdokoliv", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Odepřeno", "SSE.Views.ProtectedRangesEditDlg.textCanView": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "Název musí začínat písmenem a musí obsahovat pouze písmena, čísla nebo mezery.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Odstranit", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "Vybrat data", "SSE.Views.ProtectedRangesEditDlg.textYou": "Vy", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Přístup k rozsahu", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "Toto pole je povinné", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "Zabezpečení", "SSE.Views.ProtectedRangesEditDlg.txtRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Tento roz<PERSON>h můžete upravovat pouze vy.", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Začněte psát jméno nebo e-mail", "SSE.Views.ProtectedRangesManagerDlg.guestText": "Návštěvník", "SSE.Views.ProtectedRangesManagerDlg.lockText": "Uzamčeno", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "Žádný zabezpečený rozsah nebyl vytvořen.<br>Vytvořte alespoň jeden zabezpečený rozsah pro zobrazení v tomto poli.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "Filtr", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.textNew": "Nové", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Zabezpečit list", "SSE.Views.ProtectedRangesManagerDlg.textRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "Můžete omezit právo editace rozsahů na vybrané osoby. ", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "Prvek je upravován jiným uživatelem.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Přístup", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Odepřeno", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "Nový rozsah", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Zabezpeč<PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.txtView": "Zobrazit", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Opravdu chcete smazat zabezpečený rozsah {0}?<br>Každý kdo má v sešitu možnost editace, bude moci obsah v rozsahu upravovat.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Opravdu chcete smazat zabezpečené rozsahy?<br><PERSON><PERSON><PERSON><PERSON> kdo má k sešitu přístup, bude moci obsah v rozsahu upravovat.", "SSE.Views.ProtectRangesDlg.guestText": "Návštěvník", "SSE.Views.ProtectRangesDlg.lockText": "Uzamčeno", "SSE.Views.ProtectRangesDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEdit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEmpty": "Nejsou povoleny žádné rozsahy pro úpravy.", "SSE.Views.ProtectRangesDlg.textNew": "Nové", "SSE.Views.ProtectRangesDlg.textProtect": "Zabezpečit list", "SSE.Views.ProtectRangesDlg.textPwd": "He<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRange": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Rozsahy odemčení heslem když je list zabezpečen(toto funguje pouze pro uzamčené buňky)", "SSE.Views.ProtectRangesDlg.textTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Prvek je upravován jiným uživatelem.", "SSE.Views.ProtectRangesDlg.txtEditRange": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "SSE.Views.ProtectRangesDlg.txtNewRange": "Nový rozsah", "SSE.Views.ProtectRangesDlg.txtNo": "Ne", "SSE.Views.ProtectRangesDlg.txtTitle": "Umožnit uživatelům upravovat rozsahy", "SSE.Views.ProtectRangesDlg.txtYes": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.warnDelete": "Opravdu chcete název {0} smazat?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Pro smazání duplicitn<PERSON>ch hodnot <PERSON> j<PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON>, k<PERSON><PERSON> obsahují duplicitní hodnoty.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Data mají <PERSON>", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Vybrat vše", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON> duplicity", "SSE.Views.RightMenu.ariaRightMenu": "Nabídka vpravo", "SSE.Views.RightMenu.txtCellSettings": "Nastavení b<PERSON>ň<PERSON>", "SSE.Views.RightMenu.txtChartSettings": "Nastavení grafu", "SSE.Views.RightMenu.txtImageSettings": "Nastavení obrázku", "SSE.Views.RightMenu.txtParagraphSettings": "Nastavení odstavce", "SSE.Views.RightMenu.txtPivotSettings": "Nastavení kontingenční ta<PERSON>", "SSE.Views.RightMenu.txtSettings": "Obecné nastavení", "SSE.Views.RightMenu.txtShapeSettings": "Nastavení obrazce", "SSE.Views.RightMenu.txtSignatureSettings": "Nastavení podpisu", "SSE.Views.RightMenu.txtSlicerSettings": "Nastavení průřezu", "SSE.Views.RightMenu.txtSparklineSettings": "Nastavení mikrografu", "SSE.Views.RightMenu.txtTableSettings": "Nastavení ta<PERSON>", "SSE.Views.RightMenu.txtTextArtSettings": "Nastavení Text Art", "SSE.Views.ScaleDialog.textAuto": "<PERSON>ky", "SSE.Views.ScaleDialog.textError": "Zadaná hodnota není platná.", "SSE.Views.ScaleDialog.textFewPages": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textFitTo": "Přizpůsobit vůči", "SSE.Views.ScaleDialog.textHeight": "Výška", "SSE.Views.ScaleDialog.textManyPages": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textOnePage": "Strán<PERSON>", "SSE.Views.ScaleDialog.textScaleTo": "Změnit měřítko na", "SSE.Views.ScaleDialog.textTitle": "Nastavení měří<PERSON>ka", "SSE.Views.ScaleDialog.textWidth": "Šířka", "SSE.Views.SetValueDialog.txtMaxText": "Nejvyšší možná hodnota v této kolonce je {0}", "SSE.Views.SetValueDialog.txtMinText": "Nejnižší možná hodnota v této kolonce je {0}", "SSE.Views.ShapeSettings.strBackground": "<PERSON>va p<PERSON>adí", "SSE.Views.ShapeSettings.strChange": "Zm<PERSON><PERSON><PERSON> o<PERSON>", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "Výplň", "SSE.Views.ShapeSettings.strForeground": "<PERSON>va popředí", "SSE.Views.ShapeSettings.strPattern": "Vzor", "SSE.Views.ShapeSettings.strShadow": "Zobrazit stín", "SSE.Views.ShapeSettings.strSize": "Velikost", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "Průhlednost", "SSE.Views.ShapeSettings.strType": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textAdjustShadow": "Upravit stíny", "SSE.Views.ShapeSettings.textAdvanced": "Zobrazit pokročilá nastavení", "SSE.Views.ShapeSettings.textAngle": "Úhel", "SSE.Views.ShapeSettings.textBorderSizeErr": "Zadaná hodnota není správná.<br>Zadejte hodnotu v rozmezí 0 až 1584 pt.", "SSE.Views.ShapeSettings.textColor": "Vyplnit barvou", "SSE.Views.ShapeSettings.textDirection": "Směr", "SSE.Views.ShapeSettings.textEditPoints": "Upravit body", "SSE.Views.ShapeSettings.textEditShape": "Upravit obrazce", "SSE.Views.ShapeSettings.textEmptyPattern": "Bez vzoru", "SSE.Views.ShapeSettings.textEyedropper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromFile": "<PERSON><PERSON> souboru", "SSE.Views.ShapeSettings.textFromStorage": "Z úložiště", "SSE.Views.ShapeSettings.textFromUrl": "Z adresy URL", "SSE.Views.ShapeSettings.textGradient": "Stínování", "SSE.Views.ShapeSettings.textGradientFill": "Výplň přechodem", "SSE.Views.ShapeSettings.textHint270": "Otočit o 90° doleva", "SSE.Views.ShapeSettings.textHint90": "Otočit o 90° doprava", "SSE.Views.ShapeSettings.textHintFlipH": "Převrátit vodorovně", "SSE.Views.ShapeSettings.textHintFlipV": "Př<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.ShapeSettings.textImageTexture": "Obrázek nebo textura", "SSE.Views.ShapeSettings.textLinear": "Lineární", "SSE.Views.ShapeSettings.textMoreColors": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textNoFill": "Bez výplně", "SSE.Views.ShapeSettings.textNoShadow": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textOriginalSize": "Původní velikost", "SSE.Views.ShapeSettings.textPatternFill": "Vzor", "SSE.Views.ShapeSettings.textPosition": "Pozice", "SSE.Views.ShapeSettings.textRadial": "Paprskový", "SSE.Views.ShapeSettings.textRecentlyUsed": "Nedávno p<PERSON>žité", "SSE.Views.ShapeSettings.textRotate90": "Otočit o 90°", "SSE.Views.ShapeSettings.textRotation": "Otočení", "SSE.Views.ShapeSettings.textSelectImage": "<PERSON>y<PERSON><PERSON> o<PERSON>", "SSE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textShadow": "Stín", "SSE.Views.ShapeSettings.textStretch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textStyle": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textTexture": "Z textury", "SSE.Views.ShapeSettings.textTile": "D<PERSON>ž<PERSON>", "SSE.Views.ShapeSettings.tipAddGradientPoint": "P<PERSON><PERSON>t stínování", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Odstranit stínování", "SSE.Views.ShapeSettings.txtBrownPaper": "Kraftový papír", "SSE.Views.ShapeSettings.txtCanvas": "Pl<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "Tmavá tkanina", "SSE.Views.ShapeSettings.txtGrain": "Zrnitost", "SSE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "Šedý papír", "SSE.Views.ShapeSettings.txtKnit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtLeather": "Kůže", "SSE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtPapyrus": "Papyrus", "SSE.Views.ShapeSettings.txtWood": "Dřevo", "SSE.Views.ShapeSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Vnitřní odsazení textu", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Neposouvat nebo neměnit velikost s buňkami", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Alternativní text", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Alternativní textová reprezentace informací vizuálního objektu, která bude čtena lidem se zrakovým nebo kognitivním postižením, aby jim pomohla lépe porozumět informacím, které se nacházejí v obrázku, grafu, obrazci nebo v tabulce.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAngle": "Úhel", "SSE.Views.ShapeSettingsAdvanced.textArrows": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Velikost začátku", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Styl začátku", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Zkosení", "SSE.Views.ShapeSettingsAdvanced.textBottom": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Typ in<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Velikost konce", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "<PERSON><PERSON> konce", "SSE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON><PERSON>ý", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Převrá<PERSON>né", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Výška", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Vodorovně", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Typ s<PERSON>ní", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Konstantní proporce", "SSE.Views.ShapeSettingsAdvanced.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textMiter": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Přesouvat ale neměnit velikost společně s buňkami", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Povolit přetékání textu přes obrazec", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Upravit velikost podle textu", "SSE.Views.ShapeSettingsAdvanced.textRight": "Vpravo", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Otočení", "SSE.Views.ShapeSettingsAdvanced.textRound": "Z<PERSON><PERSON>n<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSize": "Velikost", "SSE.Views.ShapeSettingsAdvanced.textSnap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Vzdálenost mezi sloupci", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Čtverec", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "<PERSON><PERSON><PERSON> pole", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Obrazce – pokročilá nastavení", "SSE.Views.ShapeSettingsAdvanced.textTop": "Nahoře", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Přesouvat a měnit velikost společně s buňkami", "SSE.Views.ShapeSettingsAdvanced.textVertically": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Tloušťka a šipky", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Šířka", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Varování", "SSE.Views.SignatureSettings.strDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strDetails": "Podrobnosti podpisu", "SSE.Views.SignatureSettings.strInvalid": "Neplat<PERSON><PERSON>", "SSE.Views.SignatureSettings.strRequested": "Požadované pod<PERSON>", "SSE.Views.SignatureSettings.strSetup": "Nastavení podpisu", "SSE.Views.SignatureSettings.strSign": "Podpis", "SSE.Views.SignatureSettings.strSignature": "Podpis", "SSE.Views.SignatureSettings.strSigner": "Podepsal", "SSE.Views.SignatureSettings.strValid": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.txtContinueEditing": "Upravit i tak", "SSE.Views.SignatureSettings.txtEditWarning": "Upravením budou ze sešitu odebrány podpisy.<br>Opravdu chcete pokračovat?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Chcete tento podpis odstranit?<br><PERSON>to krok je nevrat<PERSON>. ", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Tento se<PERSON>it je třeba podep<PERSON>.", "SSE.Views.SignatureSettings.txtSigned": "Do sešitu byly přidány platné podpisy. Sešit je zabezpečen před <PERSON>.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Některé z digitálních podpisů v listu nejsou platné nebo je není možné ověřit. Sešit je zabezpečen před úpravami.", "SSE.Views.SlicerAddDialog.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerAddDialog.txtTitle": "Vložit průřezy", "SSE.Views.SlicerSettings.strHideNoData": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> data", "SSE.Views.SlicerSettings.strIndNoData": "Vizuáln<PERSON>, k<PERSON><PERSON> data", "SSE.Views.SlicerSettings.strShowDel": "Zobrazit smazané položky ze zdroje dat", "SSE.Views.SlicerSettings.strShowNoData": "jako poslední zobrazit položky bez dat ", "SSE.Views.SlicerSettings.strSorting": "Řazení a filtrování", "SSE.Views.SlicerSettings.textAdvanced": "Zobrazit pokročilé nastavení", "SSE.Views.SlicerSettings.textAsc": "Vzestupně", "SSE.Views.SlicerSettings.textAZ": "A po Z", "SSE.Views.SlicerSettings.textButtons": "Tlačítka", "SSE.Views.SlicerSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textDesc": "Sestupně", "SSE.Views.SlicerSettings.textHeight": "Výška", "SSE.Views.SlicerSettings.textHor": "<PERSON><PERSON><PERSON>v<PERSON><PERSON>", "SSE.Views.SlicerSettings.textKeepRatio": "Konstantní proporce", "SSE.Views.SlicerSettings.textLargeSmall": "od nejv<PERSON><PERSON>š<PERSON>ch po nejmenší", "SSE.Views.SlicerSettings.textLock": "Vypnout možnost změny velikosti a přesunu", "SSE.Views.SlicerSettings.textNewOld": "od nejnovějš<PERSON>ch po nejstarší", "SSE.Views.SlicerSettings.textOldNew": "od nejstaršího po nejnovější", "SSE.Views.SlicerSettings.textPosition": "Pozice", "SSE.Views.SlicerSettings.textSize": "Velikost", "SSE.Views.SlicerSettings.textSmallLarge": "od nej<PERSON>š<PERSON>ch po největší", "SSE.Views.SlicerSettings.textStyle": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textVert": "Svislé", "SSE.Views.SlicerSettings.textWidth": "Šířka", "SSE.Views.SlicerSettings.textZA": "Z po A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Tlačítka", "SSE.Views.SlicerSettingsAdvanced.strColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Výška", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> data", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Vizuáln<PERSON>, k<PERSON><PERSON> data", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Od<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Zobrazit smazané položky ze zdroje dat", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Zobrazit záhlaví", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Jako poslední zobrazit položky bez dat ", "SSE.Views.SlicerSettingsAdvanced.strSize": "Velikost", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Řazení a filtrování", "SSE.Views.SlicerSettingsAdvanced.strStyle": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Styl a velikost", "SSE.Views.SlicerSettingsAdvanced.strWidth": "Šířka", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Neposouvat nebo neměnit velikost s buňkami", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Alternativní text", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Alternativní textová reprezentace informací vizuálního objektu, která bude čtena lidem se zrakovým nebo kognitivním postižením, aby jim pomohla lépe porozumět informacím, které se nacházejí v obrázku, obrazci, grafu nebo v tabulce.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Vzestupně", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A po Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Sestupně", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Název který použít ve vzorcích", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Záhlaví", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Konstantní proporce", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "od nejv<PERSON><PERSON>š<PERSON>ch po nejmenší", "SSE.Views.SlicerSettingsAdvanced.textName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "od nejnovějš<PERSON>ch po nejstarší", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "od nejstaršího po nejnovější", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Přesouvat ale neměnit velikost společně s buňkami", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "od nej<PERSON>š<PERSON>ch po největší", "SSE.Views.SlicerSettingsAdvanced.textSnap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Název zdroje", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Průřez - Pokročilá nastavení", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Přesouvat a měnit velikost společně s buňkami", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z po A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Toto pole je povinné", "SSE.Views.SortDialog.errorEmpty": "Je třeba, aby u všech kritérií řazení byl uveden sloupec nebo řádek.", "SSE.Views.SortDialog.errorMoreOneCol": "Je vybrán více než jeden sloupec.", "SSE.Views.SortDialog.errorMoreOneRow": "Je vybrán více než jeden <PERSON>.", "SSE.Views.SortDialog.errorNotOriginalCol": "Sloupec který jste vybrali se nenachází v původně vybraném rozsahu.", "SSE.Views.SortDialog.errorNotOriginalRow": "Řádek, který jste vybrali se nenachází v původně vybraném rozsahu.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 je řazeno dle stejné barvy více než jednou.<br>Smažte duplicitní kritérium řazení a zkuste to znovu.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 je řazeno dle hodnot více než jednou.<br>Smažte duplicitní kritérium řazení a zkuste to znovu.", "SSE.Views.SortDialog.textAsc": "Vzestupně", "SSE.Views.SortDialog.textAuto": "<PERSON>ky", "SSE.Views.SortDialog.textAZ": "A po Z", "SSE.Views.SortDialog.textBelow": "Pod", "SSE.Views.SortDialog.textBtnCopy": "Kopírovat", "SSE.Views.SortDialog.textBtnDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textBtnNew": "Nové", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON> b<PERSON>", "SSE.Views.SortDialog.textColumn": "Sloupec", "SSE.Views.SortDialog.textDesc": "Sestupně", "SSE.Views.SortDialog.textDown": "Přesunout o stupeň dolů", "SSE.Views.SortDialog.textFontColor": "<PERSON><PERSON>", "SSE.Views.SortDialog.textLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textLevels": "Stupně", "SSE.Views.SortDialog.textMoreCols": "(<PERSON><PERSON><PERSON><PERSON> s<PERSON>)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON><PERSON><PERSON>)", "SSE.Views.SortDialog.textNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textOptions": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textOrder": "Pořadí", "SSE.Views.SortDialog.textRight": "Vpravo", "SSE.Views.SortDialog.textRow": "Řádek", "SSE.Views.SortDialog.textSort": "Seřadit na", "SSE.Views.SortDialog.textSortBy": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textThenBy": "Následn<PERSON> podle", "SSE.Views.SortDialog.textTop": "Nahoře", "SSE.Views.SortDialog.textUp": "Přesunout o stupeň nahoru", "SSE.Views.SortDialog.textValues": "Hodnoty", "SSE.Views.SortDialog.textZA": "Z po A", "SSE.Views.SortDialog.txtInvalidRange": "Neplatný rozsah buněk", "SSE.Views.SortDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortFilterDialog.textAsc": "Vzestupně (A do Z) od", "SSE.Views.SortFilterDialog.textDesc": "Sestupně (od Z do A) dle", "SSE.Views.SortFilterDialog.textNoSort": "Bez řazení", "SSE.Views.SortFilterDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortFilterDialog.txtTitleValue": "<PERSON><PERSON><PERSON><PERSON> dle hodnoty", "SSE.Views.SortOptionsDialog.textCase": "Roz<PERSON>šovat malá a velká písmena", "SSE.Views.SortOptionsDialog.textHeaders": "Data mají <PERSON>", "SSE.Views.SortOptionsDialog.textLeftRight": "<PERSON><PERSON><PERSON><PERSON> zleva doprava", "SSE.Views.SortOptionsDialog.textOrientation": "Orientace", "SSE.Views.SortOptionsDialog.textTitle": "Možnosti řazení", "SSE.Views.SortOptionsDialog.textTopBottom": "Seřadit odshora dolů", "SSE.Views.SpecialPasteDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textAll": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textBlanks": "Přeskočit prázdné", "SSE.Views.SpecialPasteDialog.textColWidth": "Šíř<PERSON> slou<PERSON>", "SSE.Views.SpecialPasteDialog.textComments": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textDiv": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFFormat": "Vzorce a formátování", "SSE.Views.SpecialPasteDialog.textFNFormat": "Vzorce a formáty čísla", "SSE.Views.SpecialPasteDialog.textFormats": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFormulas": "Vzorce", "SSE.Views.SpecialPasteDialog.textFWidth": "Vzorce & šířka slou<PERSON>ců", "SSE.Views.SpecialPasteDialog.textMult": "Násobit", "SSE.Views.SpecialPasteDialog.textNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textOperation": "Operace", "SSE.Views.SpecialPasteDialog.textPaste": "Vložit", "SSE.Views.SpecialPasteDialog.textSub": "O<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textTitle": "Vložit jinak", "SSE.Views.SpecialPasteDialog.textTranspose": "Přemístit", "SSE.Views.SpecialPasteDialog.textValues": "Hodnoty", "SSE.Views.SpecialPasteDialog.textVFormat": "Hodnoty a formátování", "SSE.Views.SpecialPasteDialog.textVNFormat": "<PERSON><PERSON><PERSON> hodnot a čísel", "SSE.Views.SpecialPasteDialog.textWBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.noSuggestions": "Žádná doporučení ohledně pravopisu", "SSE.Views.Spellcheck.textChange": "Změnit", "SSE.Views.Spellcheck.textChangeAll": "Změnit vše", "SSE.Views.Spellcheck.textIgnore": "Ignorovat", "SSE.Views.Spellcheck.textIgnoreAll": "Ignorovat vše", "SSE.Views.Spellcheck.txtAddToDictionary": "Přidat do slovníku", "SSE.Views.Spellcheck.txtClosePanel": "Zav<PERSON><PERSON><PERSON> h<PERSON>kování", "SSE.Views.Spellcheck.txtComplete": "Kontrola pravopisu dokončena", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Jazyk slovníku", "SSE.Views.Spellcheck.txtNextTip": "Přejít na další slovo", "SSE.Views.Spellcheck.txtSpelling": "Kontrola pra<PERSON>pisu", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(přesunout na konec)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "Vytvořit kopii", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(Vytvořit nový sešit)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "<PERSON><PERSON><PERSON><PERSON><PERSON> před list", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.filteredRecordsText": "<PERSON>lt<PERSON><PERSON><PERSON>: {0} z {1}", "SSE.Views.Statusbar.filteredText": "<PERSON><PERSON><PERSON> filt<PERSON>", "SSE.Views.Statusbar.itemAverage": "Průměrné", "SSE.Views.Statusbar.itemCount": "Počet", "SSE.Views.Statusbar.itemDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemHidden": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemHide": "Skr<PERSON><PERSON>", "SSE.Views.Statusbar.itemInsert": "Vložit", "SSE.Views.Statusbar.itemMaximum": "Maximum", "SSE.Views.Statusbar.itemMinimum": "Minimum", "SSE.Views.Statusbar.itemMoveOrCopy": "Přesunout nebo kopírovat", "SSE.Views.Statusbar.itemProtect": "Zabezpečení", "SSE.Views.Statusbar.itemRename": "Př<PERSON>menovat", "SSE.Views.Statusbar.itemStatus": "Status ukládání", "SSE.Views.Statusbar.itemSum": "SUMA", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemUnProtect": "Zrušit zabezpečení", "SSE.Views.Statusbar.RenameDialog.errNameExists": "List se stejným názvem již existuje", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "List nemůže obsahovat znaky: \\, /, *, ?, [, ], : nebo znak ' jako první nebo poslední písmeno", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Název listu", "SSE.Views.Statusbar.selectAllSheets": "Vybrat všechny listy", "SSE.Views.Statusbar.sheetIndexText": "List {0} z {1}", "SSE.Views.Statusbar.textAverage": "Průměrné", "SSE.Views.Statusbar.textCount": "Počet", "SSE.Views.Statusbar.textMax": "Maximum", "SSE.Views.Statusbar.textMin": "<PERSON>ejmé<PERSON>", "SSE.Views.Statusbar.textNewColor": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textNoColor": "<PERSON><PERSON> barvy", "SSE.Views.Statusbar.textSum": "<PERSON><PERSON>", "SSE.Views.Statusbar.tipAddTab": "Přidat list", "SSE.Views.Statusbar.tipFirst": "Přejít na první list", "SSE.Views.Statusbar.tipLast": "Přejít na poslední list", "SSE.Views.Statusbar.tipListOfSheets": "Seznam listů", "SSE.Views.Statusbar.tipNext": "Posunout seznam list<PERSON> doprava", "SSE.Views.Statusbar.tipPrev": "Posunout seznam list<PERSON> doleva", "SSE.Views.Statusbar.tipZoomFactor": "Přiblížení", "SSE.Views.Statusbar.tipZoomIn": "Přiblížit", "SSE.Views.Statusbar.tipZoomOut": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.ungroupSheets": "Zrušit seskupení listů", "SSE.Views.Statusbar.zoomText": "Přiblížení {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Operaci nelze provést pro zvolený rozsah buněk.<br>Vyberte jednotnou oblast dat odlišnou od již existující a zkuste to znovu.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Operace nemohla být pro vybraný rozsah buněk dokončena.<br><PERSON><PERSON><PERSON><PERSON> rozsah tak, aby první řádek tabulky byl na stejném řádku<br>a výsledná tabulka překrývala tu stávající.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Operace nemohla být pro vybraný rozsah buněk dokončena.<br><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> neobsahuje jin<PERSON>.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "V tabulkách nejsou dovoleny vzorce pro pole s vícero buňkami.", "SSE.Views.TableOptionsDialog.txtEmpty": "Toto pole je povinné", "SSE.Views.TableOptionsDialog.txtFormat": "Vytvořit tabulku", "SSE.Views.TableOptionsDialog.txtInvalidRange": "CHYBA! Neplatný rozsah buněk", "SSE.Views.TableOptionsDialog.txtNote": "Záhlaví musí zůstat na stejném řádku a výsledný rozsah tabulky musí překrývat původní rozsah.", "SSE.Views.TableOptionsDialog.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.deleteColumnText": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.TableSettings.deleteRowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.deleteTableText": "S<PERSON>zat tabulku", "SSE.Views.TableSettings.insertColumnLeftText": "Vložit sloupec vlevo", "SSE.Views.TableSettings.insertColumnRightText": "Vložit sloupec vpravo", "SSE.Views.TableSettings.insertRowAboveText": "Vložit řádek nad", "SSE.Views.TableSettings.insertRowBelowText": "Vložit ř<PERSON>dek pod", "SSE.Views.TableSettings.notcriticalErrorTitle": "Varování", "SSE.Views.TableSettings.selectColumnText": "<PERSON><PERSON><PERSON><PERSON> cel<PERSON> sloupec", "SSE.Views.TableSettings.selectDataText": "Vybrat data sloupců", "SSE.Views.TableSettings.selectRowText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.selectTableText": "<PERSON>ybrat tabulku", "SSE.Views.TableSettings.textActions": "Ak<PERSON> s tabulkou", "SSE.Views.TableSettings.textAdvanced": "Zobrazit pokročilá nastavení", "SSE.Views.TableSettings.textBanded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textColumns": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textConvertRange": "Převést na rozsah", "SSE.Views.TableSettings.textEdit": "Řádky a sloupce", "SSE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textExistName": "CHYBA! Rozsah s takovým názvem už existuje", "SSE.Views.TableSettings.textFilter": "Tlačí<PERSON><PERSON> filt<PERSON>", "SSE.Views.TableSettings.textFirst": "První", "SSE.Views.TableSettings.textHeader": "Záhlaví", "SSE.Views.TableSettings.textInvalidName": "CHYBA! Neplatný název tabulky", "SSE.Views.TableSettings.textIsLocked": "Prvek je upravován jiným uživatelem.", "SSE.Views.TableSettings.textLast": "Poslední", "SSE.Views.TableSettings.textLongOperation": "Dlouhá operace", "SSE.Views.TableSettings.textPivot": "vložit kontingenční tabulku", "SSE.Views.TableSettings.textRemDuplicates": "<PERSON><PERSON><PERSON><PERSON> duplicity", "SSE.Views.TableSettings.textReservedName": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> se p<PERSON><PERSON><PERSON><PERSON><PERSON>, je už uveden ve vzorcích buněk. Použijte nějaký jiný název.", "SSE.Views.TableSettings.textResize": "Velikost tabulky", "SSE.Views.TableSettings.textRows": "Řádky", "SSE.Views.TableSettings.textSelectData": "Vybrat data", "SSE.Views.TableSettings.textSlicer": "Vložit průřez", "SSE.Views.TableSettings.textTableName": "N<PERSON>zev tabulky", "SSE.Views.TableSettings.textTemplate": "Vybrat ze šablony", "SSE.Views.TableSettings.textTotal": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.txtGroupTable_Custom": "Vlastní", "SSE.Views.TableSettings.txtGroupTable_Dark": "Tmavé", "SSE.Views.TableSettings.txtGroupTable_Light": "Světl<PERSON>", "SSE.Views.TableSettings.txtGroupTable_Medium": "Střední", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Tmavý styl tabulky", "SSE.Views.TableSettings.txtTable_TableStyleLight": "Styl tabulky světlý", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "Styl tabulky střední", "SSE.Views.TableSettings.warnLongOperation": "Dokončení operace, kterou se chyst<PERSON>te prov<PERSON>t, by m<PERSON><PERSON> trvat opravdu dlouho.<br>Opravdu chcete pokračovat?", "SSE.Views.TableSettingsAdvanced.textAlt": "Alternativní text", "SSE.Views.TableSettingsAdvanced.textAltDescription": "<PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textAltTip": "Alternativní textová reprezentace informací vizuálního objektu, která bude čtena lidem se zrakovým nebo kognitivním postižením, aby jim pomohla lépe porozumět informacím, které se nacházejí v obrázku, obrazci, grafu nebo v tabulce.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textTitle": "Tabulka - pokročilá nastavení", "SSE.Views.TextArtSettings.strBackground": "<PERSON>va p<PERSON>adí", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "Výplň", "SSE.Views.TextArtSettings.strForeground": "<PERSON>va popředí", "SSE.Views.TextArtSettings.strPattern": "Vzor", "SSE.Views.TextArtSettings.strSize": "Velikost", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "Průhlednost", "SSE.Views.TextArtSettings.strType": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textAngle": "Úhel", "SSE.Views.TextArtSettings.textBorderSizeErr": "Zadaná hodnota není správná.<br>Zadejte hodnotu v rozmezí 0 až 1584 pt.", "SSE.Views.TextArtSettings.textColor": "Vyplnit barvou", "SSE.Views.TextArtSettings.textDirection": "Směr", "SSE.Views.TextArtSettings.textEmptyPattern": "Bez vzoru", "SSE.Views.TextArtSettings.textFromFile": "<PERSON><PERSON> souboru", "SSE.Views.TextArtSettings.textFromUrl": "Z adresy URL", "SSE.Views.TextArtSettings.textGradient": "Stínování", "SSE.Views.TextArtSettings.textGradientFill": "Výplň přechodem", "SSE.Views.TextArtSettings.textImageTexture": "Obrázek nebo textura", "SSE.Views.TextArtSettings.textLinear": "Lineární", "SSE.Views.TextArtSettings.textNoFill": "Bez výplně", "SSE.Views.TextArtSettings.textPatternFill": "Vzor", "SSE.Views.TextArtSettings.textPosition": "Pozice", "SSE.Views.TextArtSettings.textRadial": "Paprskový", "SSE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStretch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStyle": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textTemplate": "Šablona", "SSE.Views.TextArtSettings.textTexture": "Z textury", "SSE.Views.TextArtSettings.textTile": "D<PERSON>ž<PERSON>", "SSE.Views.TextArtSettings.textTransform": "Transformovat", "SSE.Views.TextArtSettings.tipAddGradientPoint": "P<PERSON><PERSON>t stínování", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Odstranit stínování", "SSE.Views.TextArtSettings.txtBrownPaper": "Kraftový papír", "SSE.Views.TextArtSettings.txtCanvas": "Pl<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "Tmavá tkanina", "SSE.Views.TextArtSettings.txtGrain": "Zrnitost", "SSE.Views.TextArtSettings.txtGranite": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "Šedý papír", "SSE.Views.TextArtSettings.txtKnit": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtLeather": "Kůže", "SSE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtPapyrus": "Papyrus", "SSE.Views.TextArtSettings.txtWood": "Dřevo", "SSE.Views.Toolbar.capBtnAddComment": "Přidat komentář", "SSE.Views.Toolbar.capBtnColorSchemas": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnInsHeader": "Záhlaví & Zápatí", "SSE.Views.Toolbar.capBtnInsSlicer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Symbol", "SSE.Views.Toolbar.capBtnMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPageBreak": "Rozd<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.Toolbar.capBtnPageOrient": "Orientace", "SSE.Views.Toolbar.capBtnPageSize": "Velikost", "SSE.Views.Toolbar.capBtnPrintArea": "Oblast tisku", "SSE.Views.Toolbar.capBtnPrintTitles": "Tisk názvů", "SSE.Views.Toolbar.capBtnScale": "Přizpůsobit měřítko velikosti", "SSE.Views.Toolbar.capImgAlign": "Zarovnání", "SSE.Views.Toolbar.capImgBackward": "Přenést o vrstvu níž", "SSE.Views.Toolbar.capImgForward": "Přenést výše", "SSE.Views.Toolbar.capImgGroup": "Seskupení", "SSE.Views.Toolbar.capInsertChart": "<PERSON>", "SSE.Views.Toolbar.capInsertChartRecommend": "Doporučený graf", "SSE.Views.Toolbar.capInsertEquation": "Rovnice", "SSE.Views.Toolbar.capInsertHyperlink": "Hypertextový odkaz", "SSE.Views.Toolbar.capInsertImage": "Obrázek", "SSE.Views.Toolbar.capInsertShape": "O<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertSpark": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertTable": "Tabulka", "SSE.Views.Toolbar.capInsertText": "<PERSON><PERSON><PERSON> pole", "SSE.Views.Toolbar.capInsertTextart": "Text Art", "SSE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniCapitalizeWords": "Velká na začátku každého slova", "SSE.Views.Toolbar.mniImageFromFile": "Obrázek ze souboru", "SSE.Views.Toolbar.mniImageFromStorage": "Obrázek z úložiště", "SSE.Views.Toolbar.mniImageFromUrl": "Obrázek z adresy URL", "SSE.Views.Toolbar.mniLowerCase": "všechna malá", "SSE.Views.Toolbar.mniSentenceCase": "Velká na začátku věty.", "SSE.Views.Toolbar.mniToggleCase": "zAMĚNIT mALÁ a vELKÁ", "SSE.Views.Toolbar.mniUpperCase": "VŠECHNA VELKÁ", "SSE.Views.Toolbar.textAddPrintArea": "Přidat do tisknutelné oblasti", "SSE.Views.Toolbar.textAlignBottom": "Zarovnat dolů", "SSE.Views.Toolbar.textAlignCenter": "Zarovnat na střed", "SSE.Views.Toolbar.textAlignJust": "Do bloku", "SSE.Views.Toolbar.textAlignLeft": "Zarovnat vlevo", "SSE.Views.Toolbar.textAlignMiddle": "Zarovnat na střed", "SSE.Views.Toolbar.textAlignRight": "Zarovnat vpravo", "SSE.Views.Toolbar.textAlignTop": "Zarovnat nahoru", "SSE.Views.Toolbar.textAllBorders": "Všechna ohraničení", "SSE.Views.Toolbar.textAlpha": "Řecké malé písmeno Alfa", "SSE.Views.Toolbar.textAuto": "<PERSON>ky", "SSE.Views.Toolbar.textAutoColor": "<PERSON>ky", "SSE.Views.Toolbar.textBetta": "Řecké malé písmeno Beta", "SSE.Views.Toolbar.textBlackHeart": "<PERSON><PERSON><PERSON> s<PERSON>ce", "SSE.Views.Toolbar.textBold": "Tučně", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON>", "SSE.Views.Toolbar.textBordersStyle": "<PERSON><PERSON>", "SSE.Views.Toolbar.textBottom": "<PERSON><PERSON>: ", "SSE.Views.Toolbar.textBottomBorders": "Spodní <PERSON>", "SSE.Views.Toolbar.textBullet": "Odr<PERSON>ž<PERSON>", "SSE.Views.Toolbar.textCellAlign": "Formátovat zarovnání buňek", "SSE.Views.Toolbar.textCenterBorders": "Vnitřní svislé ohraničení", "SSE.Views.Toolbar.textClearPrintArea": "Vyčistit oblast tisku", "SSE.Views.Toolbar.textClearRule": "Vyčistit pravidla", "SSE.Views.Toolbar.textClockwise": "Otočit ve směru hodinových ručiček", "SSE.Views.Toolbar.textColorScales": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textCopyright": "Znak autorských práv", "SSE.Views.Toolbar.textCounterCw": "Otočit proti směru hodinových ručiček", "SSE.Views.Toolbar.textCustom": "Vlastní", "SSE.Views.Toolbar.textDataBars": "Histogramy", "SSE.Views.Toolbar.textDegree": "Znak stupně", "SSE.Views.Toolbar.textDelLeft": "Posunout buňky vlevo", "SSE.Views.Toolbar.textDelPageBreak": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.Toolbar.textDelta": "Řecké malé písmeno Delta", "SSE.Views.Toolbar.textDelUp": "Posunout buňky nahoru", "SSE.Views.Toolbar.textDiagDownBorder": "Ohraničení diagonálně dolů", "SSE.Views.Toolbar.textDiagUpBorder": "Ohraničení diagonálně nahoru", "SSE.Views.Toolbar.textDivision": "Znak <PERSON>", "SSE.Views.Toolbar.textDollar": "Znak dolaru", "SSE.Views.Toolbar.textDone": "Hotovo", "SSE.Views.Toolbar.textDown": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textEditVA": "Upravit viditelnou oblast", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.Toolbar.textEntireRow": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textEuro": "Znak euro", "SSE.Views.Toolbar.textFewPages": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textFillLeft": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textFillRight": "Vpravo", "SSE.Views.Toolbar.textFormatCellFill": "Formátovat výplň buňky", "SSE.Views.Toolbar.textGreaterEqual": "Větší nebo rovno", "SSE.Views.Toolbar.textHeight": "Výška", "SSE.Views.Toolbar.textHideVA": "Skrýt viditelnou oblast", "SSE.Views.Toolbar.textHorizontal": "Vodorovný text", "SSE.Views.Toolbar.textInfinity": "Nekonečno", "SSE.Views.Toolbar.textInsDown": "Posunout buňky do<PERSON>ů", "SSE.Views.Toolbar.textInsideBorders": "Vnitř<PERSON><PERSON>", "SSE.Views.Toolbar.textInsPageBreak": "Vložit zalomení s<PERSON>", "SSE.Views.Toolbar.textInsRight": "Posunout buňky vpravo", "SSE.Views.Toolbar.textItalic": "Skloněné", "SSE.Views.Toolbar.textItems": "Polož<PERSON>", "SSE.Views.Toolbar.textLandscape": "Na šířku", "SSE.Views.Toolbar.textLeft": "Vlevo: ", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textLessEqual": "Méně než nebo rovno", "SSE.Views.Toolbar.textLetterPi": "Řecké malé písmeno P<PERSON>", "SSE.Views.Toolbar.textManageRule": "Spravovat pravidla", "SSE.Views.Toolbar.textManyPages": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsLast": "Nejnovější vlastní", "SSE.Views.Toolbar.textMarginsNarrow": "Úzké", "SSE.Views.Toolbar.textMarginsNormal": "Normální", "SSE.Views.Toolbar.textMarginsWide": "Šířka", "SSE.Views.Toolbar.textMiddleBorders": "Vnitřní vodorovné <PERSON>í", "SSE.Views.Toolbar.textMoreBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMoreFormats": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMorePages": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMoreSymbols": "Dalš<PERSON> symboly", "SSE.Views.Toolbar.textNewColor": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textNewRule": "Nové pravidlo", "SSE.Views.Toolbar.textNoBorders": "<PERSON>z oh<PERSON>í", "SSE.Views.Toolbar.textNotEqualTo": "Nerov<PERSON> se", "SSE.Views.Toolbar.textOneHalf": "Obyčejný zlomek - jedna polovina", "SSE.Views.Toolbar.textOnePage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textOneQuarter": "Obyčejný zlomek - jedna čtvrtina", "SSE.Views.Toolbar.textOutBorders": "Vn<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPageMarginsCustom": "Vlastn<PERSON> ok<PERSON>", "SSE.Views.Toolbar.textPlusMinus": "Znak plus-mínus", "SSE.Views.Toolbar.textPortrait": "Na výšku", "SSE.Views.Toolbar.textPrint": "Tisk", "SSE.Views.Toolbar.textPrintGridlines": "Vytisknout mřížku", "SSE.Views.Toolbar.textPrintHeadings": "Tisk záhlaví", "SSE.Views.Toolbar.textPrintOptions": "Nastavení tisku", "SSE.Views.Toolbar.textRegistered": "Symbol registrované ochranné z<PERSON>ky", "SSE.Views.Toolbar.textResetPageBreak": "Aktualizovat všechna zalomení stránky", "SSE.Views.Toolbar.textRight": "Vpravo: ", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textRotateDown": "Otočit text dolů", "SSE.Views.Toolbar.textRotateUp": "Otočit text nahoru", "SSE.Views.Toolbar.textRtlSheet": "Sheet Right-to-left", "SSE.Views.Toolbar.textScale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textScaleCustom": "Vlastní", "SSE.Views.Toolbar.textSection": "Paragra<PERSON>", "SSE.Views.Toolbar.textSelection": "<PERSON>d st<PERSON>jícího výběru", "SSE.Views.Toolbar.textSeries": "Řady", "SSE.Views.Toolbar.textSetPrintArea": "Nastavit oblast tisku", "SSE.Views.Toolbar.textShapesCombine": "Slouč<PERSON>", "SSE.Views.Toolbar.textShapesFragment": "Fragment", "SSE.Views.Toolbar.textShapesIntersect": "Intersect", "SSE.Views.Toolbar.textShapesSubstract": "Subtract", "SSE.Views.Toolbar.textShapesUnion": "Sjednocení", "SSE.Views.Toolbar.textShowVA": "Zobrazit viditelnou oblast", "SSE.Views.Toolbar.textSmile": "Veselý obličej", "SSE.Views.Toolbar.textSquareRoot": "<PERSON><PERSON><PERSON> od<PERSON>", "SSE.Views.Toolbar.textStrikeout": "Přeškrtnutí", "SSE.Views.Toolbar.textSubscript": "Dolní index", "SSE.Views.Toolbar.textSubSuperscript": "Dolní/horní index", "SSE.Views.Toolbar.textSuperscript": "Horní index", "SSE.Views.Toolbar.textTabCollaboration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabData": "Data", "SSE.Views.Toolbar.textTabDraw": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFile": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFormula": "Vzorec", "SSE.Views.Toolbar.textTabHome": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTabInsert": "Vložit", "SSE.Views.Toolbar.textTabLayout": "Rozvržení", "SSE.Views.Toolbar.textTabProtect": "Zabezpečení", "SSE.Views.Toolbar.textTabView": "Zobrazit", "SSE.Views.Toolbar.textThisPivot": "Z této kontingenční tabulky", "SSE.Views.Toolbar.textThisSheet": "Z tohoto listu", "SSE.Views.Toolbar.textThisTable": "Z této tabulky", "SSE.Views.Toolbar.textTilde": "Tilda", "SSE.Views.Toolbar.textTop": "Nahoře: ", "SSE.Views.Toolbar.textTopBorders": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTradeMark": "Znak neregistrovaná obchodní značka", "SSE.Views.Toolbar.textUnderline": "Podtržení", "SSE.Views.Toolbar.textUp": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textVertical": "Svislý text", "SSE.Views.Toolbar.textWidth": "Šířka", "SSE.Views.Toolbar.textYen": "<PERSON><PERSON><PERSON> jen", "SSE.Views.Toolbar.textZoom": "Přiblížení", "SSE.Views.Toolbar.tipAlignBottom": "Zarovnat dolů", "SSE.Views.Toolbar.tipAlignCenter": "Zarovnat na střed", "SSE.Views.Toolbar.tipAlignJust": "Do bloku", "SSE.Views.Toolbar.tipAlignLeft": "Zarovnat vlevo", "SSE.Views.Toolbar.tipAlignMiddle": "Zarovnat na střed", "SSE.Views.Toolbar.tipAlignRight": "Zarovnat vpravo", "SSE.Views.Toolbar.tipAlignTop": "Zarovnat nahoru", "SSE.Views.Toolbar.tipAutofilter": "Seřadit a filtrovat", "SSE.Views.Toolbar.tipBack": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipBorders": "Ohraničení", "SSE.Views.Toolbar.tipCellStyle": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipChangeCase": "Změna nastavení pravidel pro velká a malá písmena", "SSE.Views.Toolbar.tipChangeChart": "Změnit typ grafu", "SSE.Views.Toolbar.tipClearStyle": "Vyčistit", "SSE.Views.Toolbar.tipColorSchemas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCondFormat": "Podmín<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCopy": "Zkopírovat", "SSE.Views.Toolbar.tipCopyStyle": "Zkopírovat styl", "SSE.Views.Toolbar.tipCut": "Vyjmout", "SSE.Views.Toolbar.tipDecDecimal": "<PERSON>de<PERSON><PERSON> desetinn<PERSON> m<PERSON>to", "SSE.Views.Toolbar.tipDecFont": "Zmenšit velikost písma", "SSE.Views.Toolbar.tipDeleteOpt": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDigStyleAccounting": "Účetnický formát", "SSE.Views.Toolbar.tipDigStyleComma": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipDigStyleCurrency": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDigStylePercent": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipEditChart": "<PERSON><PERSON><PERSON><PERSON> graf", "SSE.Views.Toolbar.tipEditChartData": "Vybrat data", "SSE.Views.Toolbar.tipEditChartType": "Změnit typ grafu", "SSE.Views.Toolbar.tipEditHeader": "Upravit záhlaví nebo zápatí", "SSE.Views.Toolbar.tipFontColor": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipFontName": "Font", "SSE.Views.Toolbar.tipFontSize": "Velikost písma", "SSE.Views.Toolbar.tipHAlighOle": "Zarovnat vodorovně", "SSE.Views.Toolbar.tipImgAlign": "Zarovnat objekty", "SSE.Views.Toolbar.tipImgGroup": "Seskupit objekty", "SSE.Views.Toolbar.tipIncDecimal": "Přidat desetinné m<PERSON>", "SSE.Views.Toolbar.tipIncFont": "Zvětšit velikost písma", "SSE.Views.Toolbar.tipInsertChart": "Vložit graf", "SSE.Views.Toolbar.tipInsertChartRecommend": "Vložit doporučený graf", "SSE.Views.Toolbar.tipInsertChartSpark": "Vložit graf", "SSE.Views.Toolbar.tipInsertEquation": "Vložit rovnici", "SSE.Views.Toolbar.tipInsertHorizontalText": "Vložit vodorov<PERSON><PERSON> textov<PERSON>", "SSE.Views.Toolbar.tipInsertHyperlink": "Přidat hypertextový odkaz", "SSE.Views.Toolbar.tipInsertImage": "Vložit obrázek", "SSE.Views.Toolbar.tipInsertOpt": "Vložit buňky", "SSE.Views.Toolbar.tipInsertShape": "Vložit obrazec", "SSE.Views.Toolbar.tipInsertSlicer": "Vložit průřez", "SSE.Views.Toolbar.tipInsertSmartArt": "Vložit SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Vložit mikrograf", "SSE.Views.Toolbar.tipInsertSymbol": "Vložit symbol", "SSE.Views.Toolbar.tipInsertTable": "Vložit tabulku", "SSE.Views.Toolbar.tipInsertText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertTextart": "Vložit Text art", "SSE.Views.Toolbar.tipInsertVerticalText": "Vložit vertikální textov<PERSON>", "SSE.Views.Toolbar.tipMerge": "Sloučit a vystředit", "SSE.Views.Toolbar.tipNone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipNumFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPageBreak": "Přidat zalomení v místě, kde má začínat další stránka v tištěné kopii", "SSE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPageOrient": "<PERSON><PERSON> s<PERSON>", "SSE.Views.Toolbar.tipPageSize": "Velikos<PERSON> s<PERSON>ánky", "SSE.Views.Toolbar.tipPaste": "Vložit", "SSE.Views.Toolbar.tipPrColor": "Barva výplně", "SSE.Views.Toolbar.tipPrint": "Tisk", "SSE.Views.Toolbar.tipPrintArea": "Oblast tisku", "SSE.Views.Toolbar.tipPrintQuick": "Rychlý tisk", "SSE.Views.Toolbar.tipPrintTitles": "Tisk názvů", "SSE.Views.Toolbar.tipRedo": "Znovu", "SSE.Views.Toolbar.tipReplace": "Nahradit", "SSE.Views.Toolbar.tipRtlSheet": "Switch the sheet direction so that the first column is on the right side", "SSE.Views.Toolbar.tipSave": "Uložit", "SSE.Views.Toolbar.tipSaveCoauth": "Uložte změny, aby je viděli i ostatní uživatelé.", "SSE.Views.Toolbar.tipScale": "Přizpůsobit měřítko velikosti", "SSE.Views.Toolbar.tipSelectAll": "Vybrat vše", "SSE.Views.Toolbar.tipSendBackward": "Přenést o vrstvu níž", "SSE.Views.Toolbar.tipSendForward": "Přenést výše", "SSE.Views.Toolbar.tipShapesMerge": "Merge shapes", "SSE.Views.Toolbar.tipSynchronize": "Dokument byl mezitím pozměněn jiným uživatelem. Klikněte pro uložení vašich změn a načtení úprav.", "SSE.Views.Toolbar.tipTextFormatting": "<PERSON><PERSON><PERSON> n<PERSON>ů pro formátování textu", "SSE.Views.Toolbar.tipTextOrientation": "Orientace", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipVAlighOle": "Svislé zarovnání", "SSE.Views.Toolbar.tipVisibleArea": "Viditelná oblast", "SSE.Views.Toolbar.tipWrap": "Zalamovat text", "SSE.Views.Toolbar.txtAccounting": "Účetnictví", "SSE.Views.Toolbar.txtAdditional": "Dalš<PERSON>", "SSE.Views.Toolbar.txtAscending": "Vzestupně", "SSE.Views.Toolbar.txtAutosumTip": "Su<PERSON>ce", "SSE.Views.Toolbar.txtCellStyle": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtClearAll": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearComments": "Ko<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFilter": "Vyčistit filtr", "SSE.Views.Toolbar.txtClearFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearFormula": "Funkce", "SSE.Views.Toolbar.txtClearHyper": "Hypertextové odkazy", "SSE.Views.Toolbar.txtClearText": "Text", "SSE.Views.Toolbar.txtCurrency": "Měna", "SSE.Views.Toolbar.txtCustom": "Vlastní", "SSE.Views.Toolbar.txtDate": "Datum", "SSE.Views.Toolbar.txtDateLong": "Dlouhý formát data", "SSE.Views.Toolbar.txtDateShort": "Krátký formát data", "SSE.Views.Toolbar.txtDateTime": "Datum a čas", "SSE.Views.Toolbar.txtDescending": "Sestupně", "SSE.Views.Toolbar.txtDollar": "$ Dolar", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Exponenciální", "SSE.Views.Toolbar.txtFillNum": "Výplň", "SSE.Views.Toolbar.txtFilter": "Filtr", "SSE.Views.Toolbar.txtFormula": "Vlož<PERSON>", "SSE.Views.Toolbar.txtFraction": "Zlomek", "SSE.Views.Toolbar.txtFranc": "CHF Švýcarský frank", "SSE.Views.Toolbar.txtGeneral": "Obecné", "SSE.Views.Toolbar.txtInteger": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtManageRange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> názvů", "SSE.Views.Toolbar.txtMergeAcross": "Slouč<PERSON>", "SSE.Views.Toolbar.txtMergeCells": "Sloučit buňky", "SSE.Views.Toolbar.txtMergeCenter": "Sloučit a vystředit", "SSE.Views.Toolbar.txtNamedRange": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtNewRange": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>zev", "SSE.Views.Toolbar.txtNoBorders": "<PERSON>z oh<PERSON>í", "SSE.Views.Toolbar.txtNumber": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtPasteRange": "Vložit název", "SSE.Views.Toolbar.txtPercentage": "Procento", "SSE.Views.Toolbar.txtPound": "£ Libra", "SSE.Views.Toolbar.txtRouble": "₽ Rubl", "SSE.Views.Toolbar.txtScientific": "Vědecké", "SSE.Views.Toolbar.txtSearch": "Hledat", "SSE.Views.Toolbar.txtSort": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSortAZ": "Seřadit vzestupně", "SSE.Views.Toolbar.txtSortZA": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSpecial": "Speciální", "SSE.Views.Toolbar.txtTableTemplate": "Formátovat podle šablony tabulky", "SSE.Views.Toolbar.txtText": "Text", "SSE.Views.Toolbar.txtTime": "Čas", "SSE.Views.Toolbar.txtUnmerge": "Zrušit sloučení", "SSE.Views.Toolbar.txtYen": "¥ Jen", "SSE.Views.Top10FilterDialog.textType": "Zobrazit", "SSE.Views.Top10FilterDialog.txtBottom": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBy": "Od", "SSE.Views.Top10FilterDialog.txtItems": "Položka", "SSE.Views.Top10FilterDialog.txtPercent": "Procento", "SSE.Views.Top10FilterDialog.txtSum": "SUMA", "SSE.Views.Top10FilterDialog.txtTitle": "Top 10 automatického filtru", "SSE.Views.Top10FilterDialog.txtTop": "Nahoře", "SSE.Views.Top10FilterDialog.txtValueTitle": "Filtry \"Top 10\"", "SSE.Views.ValueFieldSettingsDialog.textNext": "(následující)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(předchozí)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON> hodnot pole", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Průměrné", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "<PERSON><PERSON><PERSON><PERSON><PERSON> pole", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Základní <PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 z %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Počet", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Spočítat čísla", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Vlast<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Rozdílné <PERSON>i", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Index", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Maximum", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Minimum", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "Bez výpočtu", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "% z", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "% rozdíl mezi", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "% ze sloupce", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% z celkového součtu", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% součtu nadřazené položky", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% součtu nadřazeného sloupce", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% součtu nadřazeného řádku", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% mezisoučtu v", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "% z řádku", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Seřadit od nejmenšího po největší", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Seřadit od největšího po nejmenší", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Průběžný součet v", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Zobrazit hodnoty jako", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Název zdroje:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "Směrodatná odchylka", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "Populační směrodatná odchylka", "SSE.Views.ValueFieldSettingsDialog.txtSum": "SUMA", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "<PERSON><PERSON><PERSON><PERSON> hodnotu pole dle", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.guestText": "Návštěvník", "SSE.Views.ViewManagerDlg.lockText": "Uzamčeno", "SSE.Views.ViewManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textDuplicate": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textEmpty": "Žádné zobrazení nebyly prozatím vytvořeny.", "SSE.Views.ViewManagerDlg.textGoTo": "Přejít na zobrazení", "SSE.Views.ViewManagerDlg.textLongName": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> má méně než 128 znaků.", "SSE.Views.ViewManagerDlg.textNew": "Nový", "SSE.Views.ViewManagerDlg.textRename": "Př<PERSON>menovat", "SSE.Views.ViewManagerDlg.textRenameError": "Je třeba vyplnit název zobrazení.", "SSE.Views.ViewManagerDlg.textRenameLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pohled", "SSE.Views.ViewManagerDlg.textViews": "Zobrazení sešitu", "SSE.Views.ViewManagerDlg.tipIsLocked": "Prvek je upravován jiným uživatelem.", "SSE.Views.ViewManagerDlg.txtTitle": "Správce zobrazení listů", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Are you sure you want to delete this sheet view?", "SSE.Views.ViewManagerDlg.warnDeleteView": "Pokoušíte se smazat aktuálně zapnuté zobrazení'%1'.<br>Opravdu chcete toto zobrazení zavřít a smazat?", "SSE.Views.ViewTab.capBtnFreeze": "Ukotvit <PERSON>", "SSE.Views.ViewTab.capBtnSheetView": "Zobrazení sešitu", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Vždy zobrazovat panel nástrojů", "SSE.Views.ViewTab.textClose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Zkombinovat lišty listů a stavu", "SSE.Views.ViewTab.textCreate": "Nový", "SSE.Views.ViewTab.textDefault": "Výchozí", "SSE.Views.ViewTab.textFill": "Výplň", "SSE.Views.ViewTab.textFormula": "Řádek vzorců", "SSE.Views.ViewTab.textFreezeCol": "Ukotvit první sloupec", "SSE.Views.ViewTab.textFreezeRow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textGridlines": "Mřížka", "SSE.Views.ViewTab.textHeadings": "Záhlaví", "SSE.Views.ViewTab.textInterfaceTheme": "Vzhled uživatelského rozhraní", "SSE.Views.ViewTab.textLeftMenu": "Levý panel", "SSE.Views.ViewTab.textLine": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "Správce zobrazení", "SSE.Views.ViewTab.textRightMenu": "Pravý panel", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Zobrazit stín ukotvených příček", "SSE.Views.ViewTab.textTabStyle": "<PERSON><PERSON> <PERSON><PERSON>", "SSE.Views.ViewTab.textUnFreeze": "Zrušit ukotvení příček", "SSE.Views.ViewTab.textZeros": "Zobrazit nuly", "SSE.Views.ViewTab.textZoom": "Přiblížení", "SSE.Views.ViewTab.tipClose": "<PERSON>av<PERSON><PERSON><PERSON> n<PERSON> se<PERSON>itu", "SSE.Views.ViewTab.tipCreate": "Vytvořit nové zobrazení sešitu", "SSE.Views.ViewTab.tipFreeze": "Ukotvit <PERSON>", "SSE.Views.ViewTab.tipInterfaceTheme": "Vzhled uživatelského rozhraní", "SSE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipSheetView": "Zobrazení sešitu", "SSE.Views.ViewTab.tipViewNormal": "Ukázat dokument v normálním zobrazení", "SSE.Views.ViewTab.tipViewPageBreak": "<PERSON><PERSON><PERSON><PERSON>, kde bude zobrazeno zalomení stránky při tisku dokumentu", "SSE.Views.ViewTab.txtViewNormal": "Normální", "SSE.Views.ViewTab.txtViewPageBreak": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "SSE.Views.WatchDialog.closeButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textBook": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textCell": "Buňka", "SSE.Views.WatchDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textDeleteAll": "Smazat vše", "SSE.Views.WatchDialog.textFormula": "Vzorec", "SSE.Views.WatchDialog.textName": "Jméno", "SSE.Views.WatchDialog.textSheet": "List", "SSE.Views.WatchDialog.textValue": "Hodnota", "SSE.Views.WatchDialog.txtTitle": "<PERSON>no kukát<PERSON>", "SSE.Views.WBProtection.hintAllowRanges": "Umožnit upravovat rozsahy", "SSE.Views.WBProtection.hintProtectRange": "Zabezpečený rozsah", "SSE.Views.WBProtection.hintProtectSheet": "Zabezpečit list", "SSE.Views.WBProtection.hintProtectWB": "Zabezpečit sešit", "SSE.Views.WBProtection.txtAllowRanges": "Umožnit upravovat rozsahy", "SSE.Views.WBProtection.txtHiddenFormula": "Skryté vzorce", "SSE.Views.WBProtection.txtLockedCell": "Uzamčená buňka", "SSE.Views.WBProtection.txtLockedShape": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtLockedText": "Uzamknout text", "SSE.Views.WBProtection.txtProtectRange": "Zabezpečený rozsah", "SSE.Views.WBProtection.txtProtectSheet": "Zabezpečit list", "SSE.Views.WBProtection.txtProtectWB": "Zabezpečit sešit", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Zadejte heslo pro deaktivaci zabezpečení listu", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Zrušit zabezpečení listu", "SSE.Views.WBProtection.txtWBUnlockDescription": "Vložte heslo pro přístup k sešitu", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}