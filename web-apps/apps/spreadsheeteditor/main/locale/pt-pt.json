{"cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.Controllers.Chat.notcriticalErrorTitle": "Aviso", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.History.notcriticalErrorTitle": "Aviso", "Common.Controllers.History.txtErrorLoadHistory": "History loading failed", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.define.chartData.textArea": "Á<PERSON>", "Common.define.chartData.textAreaStacked": "<PERSON><PERSON> empi<PERSON>", "Common.define.chartData.textAreaStackedPer": "Área 100% alinhada", "Common.define.chartData.textBar": "Barr<PERSON>", "Common.define.chartData.textBarNormal": "Coluna agrupada", "Common.define.chartData.textBarNormal3d": "Coluna 3-D agrupada", "Common.define.chartData.textBarNormal3dPerspective": "Coluna 3-D", "Common.define.chartData.textBarStacked": "Coluna empilhada", "Common.define.chartData.textBarStacked3d": "Coluna 3-D agrupada", "Common.define.chartData.textBarStackedPer": "Coluna 100% alinhada", "Common.define.chartData.textBarStackedPer3d": "Coluna 3-D 100% alinhada", "Common.define.chartData.textCharts": "Grá<PERSON><PERSON>", "Common.define.chartData.textColumn": "Coluna", "Common.define.chartData.textColumnSpark": "Coluna", "Common.define.chartData.textCombo": "Combinação", "Common.define.chartData.textComboAreaBar": "<PERSON><PERSON> empi<PERSON> – coluna agrupada", "Common.define.chartData.textComboBarLine": "Coluna agrupada – linha", "Common.define.chartData.textComboBarLineSecondary": "Coluna agrupada – linha num eixo secundário", "Common.define.chartData.textComboCustom": "Combinação personalizada", "Common.define.chartData.textDoughnut": "Rosquin<PERSON>", "Common.define.chartData.textHBarNormal": "Barra Agrupada", "Common.define.chartData.textHBarNormal3d": "Barra 3-D agrupada", "Common.define.chartData.textHBarStacked": "<PERSON><PERSON> empilhada", "Common.define.chartData.textHBarStacked3d": "Barra 3-D agrupada", "Common.define.chartData.textHBarStackedPer": "Barra 100% alinhada", "Common.define.chartData.textHBarStackedPer3d": "Barra 3-D 100% alinhada", "Common.define.chartData.textLine": "<PERSON><PERSON>", "Common.define.chartData.textLine3d": "Linha 3-D", "Common.define.chartData.textLineMarker": "Linha com marcadores", "Common.define.chartData.textLineSpark": "<PERSON><PERSON>", "Common.define.chartData.textLineStacked": "<PERSON><PERSON> em<PERSON>", "Common.define.chartData.textLineStackedMarker": "Linha empilhada com marcadores", "Common.define.chartData.textLineStackedPer": "100% Ali<PERSON><PERSON>", "Common.define.chartData.textLineStackedPerMarker": "Alinhado com 100%", "Common.define.chartData.textPie": "Tarte", "Common.define.chartData.textPie3d": "Tarte 3-D", "Common.define.chartData.textPoint": "XY (gráfico de dispersão)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Filled radar", "Common.define.chartData.textRadarMarker": "Radar with markers", "Common.define.chartData.textScatter": "Di<PERSON>são", "Common.define.chartData.textScatterLine": "Dispersão com Linhas Retas", "Common.define.chartData.textScatterLineMarker": "Dispersão com Linhas e Marcadores Retos", "Common.define.chartData.textScatterSmooth": "Dispersão com Linhas Suaves", "Common.define.chartData.textScatterSmoothMarker": "Dispersão com Linhas Suaves e Marcadores", "Common.define.chartData.textSparks": "Sparklines", "Common.define.chartData.textStock": "Gráfico de ações", "Common.define.chartData.textSurface": "Superfície", "Common.define.chartData.textWinLossSpark": "Ganho/Perda", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Nenhum formato definido", "Common.define.conditionalData.text1Above": "1 acima do desv. padr. ", "Common.define.conditionalData.text1Below": "1 abaixo do desv. padr.", "Common.define.conditionalData.text2Above": "2 desv. padr. a<PERSON>ma", "Common.define.conditionalData.text2Below": "2 abaixo do desv. padr.", "Common.define.conditionalData.text3Above": "3 acima do desv. padr. ", "Common.define.conditionalData.text3Below": "3 abaixo do desv. padr.", "Common.define.conditionalData.textAbove": "Acima", "Common.define.conditionalData.textAverage": "Média", "Common.define.conditionalData.textBegins": "Começa com", "Common.define.conditionalData.textBelow": "Abaixo", "Common.define.conditionalData.textBetween": "<PERSON><PERSON>", "Common.define.conditionalData.textBlank": "Branco", "Common.define.conditionalData.textBlanks": "Contém espaços em branco", "Common.define.conditionalData.textBottom": "Baixo", "Common.define.conditionalData.textContains": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textDataBar": "Barra de dados", "Common.define.conditionalData.textDate": "Data", "Common.define.conditionalData.textDuplicate": "Duplicar", "Common.define.conditionalData.textEnds": "termina com", "Common.define.conditionalData.textEqAbove": "Igual ou maior", "Common.define.conditionalData.textEqBelow": "Igual ou menor", "Common.define.conditionalData.textEqual": "Igual a", "Common.define.conditionalData.textError": "Erro", "Common.define.conditionalData.textErrors": "<PERSON><PERSON><PERSON> erros", "Common.define.conditionalData.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "Common.define.conditionalData.textGreater": "Superior a", "Common.define.conditionalData.textGreaterEq": "Superior a ou igual a", "Common.define.conditionalData.textIconSets": "Conjuntos de ícones", "Common.define.conditionalData.textLast7days": "Nos últimos 7 dias", "Common.define.conditionalData.textLastMonth": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textLastWeek": "A semana passada", "Common.define.conditionalData.textLess": "Inferior a", "Common.define.conditionalData.textLessEq": "Inferior a ou igual a", "Common.define.conditionalData.textNextMonth": "<PERSON>ró<PERSON><PERSON>", "Common.define.conditionalData.textNextWeek": "Próxima semana", "Common.define.conditionalData.textNotBetween": "Não está entre", "Common.define.conditionalData.textNotBlanks": "Não contém células em branco", "Common.define.conditionalData.textNotContains": "não contém", "Common.define.conditionalData.textNotEqual": "Não igual a", "Common.define.conditionalData.textNotErrors": "Não contem erros", "Common.define.conditionalData.textText": "Тexto", "Common.define.conditionalData.textThisMonth": "<PERSON><PERSON> mês", "Common.define.conditionalData.textThisWeek": "<PERSON><PERSON> semana", "Common.define.conditionalData.textToday": "Hoje", "Common.define.conditionalData.textTomorrow": "Amanhã", "Common.define.conditionalData.textTop": "Parte superior", "Common.define.conditionalData.textUnique": "Único", "Common.define.conditionalData.textValue": "Valor é", "Common.define.conditionalData.textYesterday": "Ontem", "Common.define.smartArt.textAccentedPicture": "Imagem com destaque", "Common.define.smartArt.textAccentProcess": "Processo em destaque", "Common.define.smartArt.textAlternatingFlow": "Alternating flow", "Common.define.smartArt.textAlternatingHexagons": "Alternating hexagons", "Common.define.smartArt.textAlternatingPictureBlocks": "Alternating picture blocks", "Common.define.smartArt.textAlternatingPictureCircles": "Alternating picture circles", "Common.define.smartArt.textArchitectureLayout": "Architecture layout", "Common.define.smartArt.textArrowRibbon": "Arrow ribbon", "Common.define.smartArt.textAscendingPictureAccentProcess": "Processo de destaques da imagem ascendente", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Basic bending process", "Common.define.smartArt.textBasicBlockList": "Basic block list", "Common.define.smartArt.textBasicChevronProcess": "Basic chevron process", "Common.define.smartArt.textBasicCycle": "Basic cycle", "Common.define.smartArt.textBasicMatrix": "Basic matrix", "Common.define.smartArt.textBasicPie": "Basic Pie", "Common.define.smartArt.textBasicProcess": "Basic process", "Common.define.smartArt.textBasicPyramid": "Basic pyramid", "Common.define.smartArt.textBasicRadial": "Basic radial", "Common.define.smartArt.textBasicTarget": "Basic target", "Common.define.smartArt.textBasicTimeline": "Basic timeline", "Common.define.smartArt.textBasicVenn": "Basic Venn", "Common.define.smartArt.textBendingPictureAccentList": "Lista de destaques de imagem dobrada", "Common.define.smartArt.textBendingPictureBlocks": "Bending picture blocks", "Common.define.smartArt.textBendingPictureCaption": "Bending picture caption", "Common.define.smartArt.textBendingPictureCaptionList": "Bending picture caption list", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Bending picture semi-transparent text", "Common.define.smartArt.textBlockCycle": "Block cycle", "Common.define.smartArt.textBubblePictureList": "Bubble picture list", "Common.define.smartArt.textCaptionedPictures": "Captioned pictures", "Common.define.smartArt.textChevronAccentProcess": "Processo de destaque em divisas", "Common.define.smartArt.textChevronList": "Chevron list", "Common.define.smartArt.textCircleAccentTimeline": "Linha do tempo de destaque do círculo", "Common.define.smartArt.textCircleArrowProcess": "Circle arrow process", "Common.define.smartArt.textCirclePictureHierarchy": "Circle picture hierarchy", "Common.define.smartArt.textCircleProcess": "Circle process", "Common.define.smartArt.textCircleRelationship": "Circle relationship", "Common.define.smartArt.textCircularBendingProcess": "Circular bending process", "Common.define.smartArt.textCircularPictureCallout": "Circular picture callout", "Common.define.smartArt.textClosedChevronProcess": "Closed chevron process", "Common.define.smartArt.textContinuousArrowProcess": "Continuous arrow process", "Common.define.smartArt.textContinuousBlockProcess": "Continuous block process", "Common.define.smartArt.textContinuousCycle": "Continuous cycle", "Common.define.smartArt.textContinuousPictureList": "Continuous picture list", "Common.define.smartArt.textConvergingArrows": "Converging arrows", "Common.define.smartArt.textConvergingRadial": "Converging radial", "Common.define.smartArt.textConvergingText": "Converging text", "Common.define.smartArt.textCounterbalanceArrows": "Counterbalance arrows", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "Cycle matrix", "Common.define.smartArt.textDescendingBlockList": "Descending block list", "Common.define.smartArt.textDescendingProcess": "Descending process", "Common.define.smartArt.textDetailedProcess": "Detailed process", "Common.define.smartArt.textDivergingArrows": "Diverging arrows", "Common.define.smartArt.textDivergingRadial": "Diverging radial", "Common.define.smartArt.textEquation": "Equation", "Common.define.smartArt.textFramedTextPicture": "Framed text picture", "Common.define.smartArt.textFunnel": "Funnel", "Common.define.smartArt.textGear": "Gear", "Common.define.smartArt.textGridMatrix": "Grid matrix", "Common.define.smartArt.textGroupedList": "Grouped list", "Common.define.smartArt.textHalfCircleOrganizationChart": "Half circle organization chart", "Common.define.smartArt.textHexagonCluster": "Hexagon cluster", "Common.define.smartArt.textHexagonRadial": "Hexagon radial", "Common.define.smartArt.textHierarchy": "Hierarchy", "Common.define.smartArt.textHierarchyList": "Hierarchy list", "Common.define.smartArt.textHorizontalBulletList": "Horizontal bullet list", "Common.define.smartArt.textHorizontalHierarchy": "Horizontal hierarchy", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Horizontal labeled hierarchy", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Horizontal multi-level hierarchy", "Common.define.smartArt.textHorizontalOrganizationChart": "Horizontal organization chart", "Common.define.smartArt.textHorizontalPictureList": "Horizontal picture list", "Common.define.smartArt.textIncreasingArrowProcess": "Increasing arrow process", "Common.define.smartArt.textIncreasingCircleProcess": "Increasing circle process", "Common.define.smartArt.textInterconnectedBlockProcess": "Interconnected block process", "Common.define.smartArt.textInterconnectedRings": "Interconnected rings", "Common.define.smartArt.textInvertedPyramid": "Inverted pyramid", "Common.define.smartArt.textLabeledHierarchy": "Labeled hierarchy", "Common.define.smartArt.textLinearVenn": "Linear Venn", "Common.define.smartArt.textLinedList": "Lined list", "Common.define.smartArt.textList": "Lista", "Common.define.smartArt.textMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textMultidirectionalCycle": "Ciclo multidirecional", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Organograma de Nome e Título", "Common.define.smartArt.textNestedTarget": "Nested target", "Common.define.smartArt.textNondirectionalCycle": "Nondirectional cycle", "Common.define.smartArt.textOpposingArrows": "Opposing arrows", "Common.define.smartArt.textOpposingIdeas": "Opposing ideas", "Common.define.smartArt.textOrganizationChart": "Organization chart", "Common.define.smartArt.textOther": "Outros", "Common.define.smartArt.textPhasedProcess": "Processo em fases", "Common.define.smartArt.textPicture": "Imagem", "Common.define.smartArt.textPictureAccentBlocks": "Blocos de destaque de imagem", "Common.define.smartArt.textPictureAccentList": "Lista de destaque da imagem", "Common.define.smartArt.textPictureAccentProcess": "Processo de destaque da imagem", "Common.define.smartArt.textPictureCaptionList": "Picture caption list", "Common.define.smartArt.textPictureFrame": "PictureFrame", "Common.define.smartArt.textPictureGrid": "Picture grid", "Common.define.smartArt.textPictureLineup": "Picture lineup", "Common.define.smartArt.textPictureOrganizationChart": "Picture organization chart", "Common.define.smartArt.textPictureStrips": "Picture strips", "Common.define.smartArt.textPieProcess": "Pie process", "Common.define.smartArt.textPlusAndMinus": "Plus and minus", "Common.define.smartArt.textProcess": "Process", "Common.define.smartArt.textProcessArrows": "Process arrows", "Common.define.smartArt.textProcessList": "Process list", "Common.define.smartArt.textPyramid": "Pyramid", "Common.define.smartArt.textPyramidList": "Pyramid list", "Common.define.smartArt.textRadialCluster": "Radial cluster", "Common.define.smartArt.textRadialCycle": "Radial cycle", "Common.define.smartArt.textRadialList": "Radial list", "Common.define.smartArt.textRadialPictureList": "Radial picture list", "Common.define.smartArt.textRadialVenn": "Radial Venn", "Common.define.smartArt.textRandomToResultProcess": "Random to result process", "Common.define.smartArt.textRelationship": "Relação", "Common.define.smartArt.textRepeatingBendingProcess": "Repeating bending process", "Common.define.smartArt.textReverseList": "Reverse list", "Common.define.smartArt.textSegmentedCycle": "Segmented cycle", "Common.define.smartArt.textSegmentedProcess": "Segmented process", "Common.define.smartArt.textSegmentedPyramid": "Segmented pyramid", "Common.define.smartArt.textSnapshotPictureList": "Lista de fotos instantâneas", "Common.define.smartArt.textSpiralPicture": "Imagem em espiral", "Common.define.smartArt.textSquareAccentList": "Lista de destaque quadrados", "Common.define.smartArt.textStackedList": "Stacked list", "Common.define.smartArt.textStackedVenn": "Stacked <PERSON>n", "Common.define.smartArt.textStaggeredProcess": "Staggered process", "Common.define.smartArt.textStepDownProcess": "Step down process", "Common.define.smartArt.textStepUpProcess": "Step up process", "Common.define.smartArt.textSubStepProcess": "Sub-step process", "Common.define.smartArt.textTabbedArc": "Arco com separadores", "Common.define.smartArt.textTableHierarchy": "Table hierarchy", "Common.define.smartArt.textTableList": "Lista de tabelas", "Common.define.smartArt.textTabList": "Lista de separadores", "Common.define.smartArt.textTargetList": "Lista de alvos", "Common.define.smartArt.textTextCycle": "Ciclo de texto", "Common.define.smartArt.textThemePictureAccent": "Destaque da imagem do tema", "Common.define.smartArt.textThemePictureAlternatingAccent": "Destaque alternado da imagem do tema", "Common.define.smartArt.textThemePictureGrid": "Theme picture grid", "Common.define.smartArt.textTitledMatrix": "Titled matrix", "Common.define.smartArt.textTitledPictureAccentList": "Lista de destaques de imagem intitulada", "Common.define.smartArt.textTitledPictureBlocks": "Titled picture blocks", "Common.define.smartArt.textTitlePictureLineup": "Title picture lineup", "Common.define.smartArt.textTrapezoidList": "Trapezoid list", "Common.define.smartArt.textUpwardArrow": "Upward arrow", "Common.define.smartArt.textVaryingWidthList": "Varying width list", "Common.define.smartArt.textVerticalAccentList": "Lista de destaques verticais", "Common.define.smartArt.textVerticalArrowList": "Vertical arrow list", "Common.define.smartArt.textVerticalBendingProcess": "Vertical bending process", "Common.define.smartArt.textVerticalBlockList": "Vertical block list", "Common.define.smartArt.textVerticalBoxList": "Vertical box list", "Common.define.smartArt.textVerticalBracketList": "Vertical bracket list", "Common.define.smartArt.textVerticalBulletList": "Vertical bullet list", "Common.define.smartArt.textVerticalChevronList": "Vertical chevron list", "Common.define.smartArt.textVerticalCircleList": "Vertical circle list", "Common.define.smartArt.textVerticalCurvedList": "Vertical curved list", "Common.define.smartArt.textVerticalEquation": "Vertical equation", "Common.define.smartArt.textVerticalPictureAccentList": "Lista vertical de destaque de imagem", "Common.define.smartArt.textVerticalPictureList": "Vertical picture list", "Common.define.smartArt.textVerticalProcess": "Vertical process", "Common.Translation.textMoreButton": "<PERSON><PERSON>", "Common.Translation.tipFileLocked": "Document is locked for editing. You can make changes and save it as local copy later.", "Common.Translation.tipFileReadOnly": "Este ficheiro está aberto no modo de leitura. Para manter as alterações, guarde o ficheiro com um novo nome ou noutra localização.", "Common.Translation.warnFileLocked": "O ficheiro está a ser editado por outra aplicação. Pode continuar a trabalhar mas terá que guardar uma cópia.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON><PERSON> uma c<PERSON>pia", "Common.Translation.warnFileLockedBtnView": "Abrir para visualizar", "Common.UI.ButtonColored.textAutoColor": "Automático", "Common.UI.ButtonColored.textEyedropper": "Eyedropper", "Common.UI.ButtonColored.textNewColor": "Adicionar nova cor personalizada", "Common.UI.ComboBorderSize.txtNoBorders": "<PERSON><PERSON> bordas", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "<PERSON><PERSON> bordas", "Common.UI.ComboDataView.emptyComboText": "Sem estilos", "Common.UI.ExtendedColorDialog.addButtonText": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.ExtendedColorDialog.textCurrent": "Atual", "Common.UI.ExtendedColorDialog.textHexErr": "O valor inserido não está correto.<br>Introduza um valor entre 000000 e FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Novo", "Common.UI.ExtendedColorDialog.textRGBErr": "O valor inserido não está correto.<br>Introduza um valor numérico entre 0 e 255.", "Common.UI.HSBColorPicker.textNoColor": "Sem cor", "Common.UI.InputField.txtEmpty": "This field is required", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Ocultar palavra-passe", "Common.UI.InputFieldBtnPassword.textHintHold": "Press and hold to show password", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Mostrar palavra-passe", "Common.UI.SearchBar.textFind": "Localizar", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON><PERSON>es<PERSON>", "Common.UI.SearchBar.tipNextResult": "<PERSON><PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Abrir opções avançadas", "Common.UI.SearchBar.tipPreviousResult": "Resultado anterior", "Common.UI.SearchDialog.textHighlight": "Destacar resultados", "Common.UI.SearchDialog.textMatchCase": "Diferenciar maiús<PERSON>s de minúsculas", "Common.UI.SearchDialog.textReplaceDef": "Inserir o texto de substituição", "Common.UI.SearchDialog.textSearchStart": "Insira seu texto aqui", "Common.UI.SearchDialog.textTitle": "Localizar e Substituir", "Common.UI.SearchDialog.textTitle2": "Localizar", "Common.UI.SearchDialog.textWholeWords": "Palavras inteiras apenas", "Common.UI.SearchDialog.txtBtnHideReplace": "Ocultar substituição", "Common.UI.SearchDialog.txtBtnReplace": "Substituir", "Common.UI.SearchDialog.txtBtnReplaceAll": "Substituir tudo", "Common.UI.SynchronizeTip.textDontShow": "Não exibir esta mensagem novamente", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "O documento foi alterado por outro utilizador.<br>Clique para guardar as suas alterações e recarregar o documento.", "Common.UI.ThemeColorPalette.textRecentColors": "Cores recentes", "Common.UI.ThemeColorPalette.textStandartColors": "<PERSON><PERSON>", "Common.UI.ThemeColorPalette.textThemeColors": "Cores do tema", "Common.UI.Themes.txtThemeClassicLight": "Clássico claro", "Common.UI.Themes.txtThemeContrastDark": "Contraste escuro", "Common.UI.Themes.txtThemeDark": "Escuro", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "<PERSON><PERSON><PERSON>", "Common.UI.Themes.txtThemeSystem": "O mesmo que o sistema", "Common.UI.Window.cancelButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Não", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmação", "Common.UI.Window.textDontShow": "Não exibir esta mensagem novamente", "Common.UI.Window.textError": "Erro", "Common.UI.Window.textInformation": "Informações", "Common.UI.Window.textWarning": "Aviso", "Common.UI.Window.yesButtonText": "<PERSON>m", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Accent", "Common.Utils.ThemeColor.txtAqua": "Aqua", "Common.Utils.ThemeColor.txtbackground": "Fundo", "Common.Utils.ThemeColor.txtBlack": "Preto", "Common.Utils.ThemeColor.txtBlue": "Azul", "Common.Utils.ThemeColor.txtBrightGreen": "Verde brilhante", "Common.Utils.ThemeColor.txtBrown": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "A<PERSON>l escuro", "Common.Utils.ThemeColor.txtDarker": "Darker", "Common.Utils.ThemeColor.txtDarkGray": "Dark gray", "Common.Utils.ThemeColor.txtDarkGreen": "Verde escuro", "Common.Utils.ThemeColor.txtDarkPurple": "Dark purple", "Common.Utils.ThemeColor.txtDarkRed": "<PERSON>er<PERSON><PERSON> es<PERSON>ro", "Common.Utils.ThemeColor.txtDarkTeal": "Dark teal", "Common.Utils.ThemeColor.txtDarkYellow": "Dark yellow", "Common.Utils.ThemeColor.txtGold": "Gold", "Common.Utils.ThemeColor.txtGray": "<PERSON>", "Common.Utils.ThemeColor.txtGreen": "Verde", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "Lavender", "Common.Utils.ThemeColor.txtLightBlue": "<PERSON><PERSON>l claro", "Common.Utils.ThemeColor.txtLighter": "Lighter", "Common.Utils.ThemeColor.txtLightGray": "Light gray", "Common.Utils.ThemeColor.txtLightGreen": "Verde claro", "Common.Utils.ThemeColor.txtLightOrange": "Light orange", "Common.Utils.ThemeColor.txtLightYellow": "Light yellow", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "Pink", "Common.Utils.ThemeColor.txtPurple": "Purple", "Common.Utils.ThemeColor.txtRed": "Vermelho", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "<PERSON><PERSON><PERSON> celeste", "Common.Utils.ThemeColor.txtTeal": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txttext": "Text", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "White", "Common.Utils.ThemeColor.txtYellow": "Yellow", "Common.Views.About.txtAddress": "endereço:", "Common.Views.About.txtLicensee": "LICENCIADO", "Common.Views.About.txtLicensor": "LICENCIANTE", "Common.Views.About.txtMail": "e-mail:", "Common.Views.About.txtPoweredBy": "Desenvolvido por", "Common.Views.About.txtTel": "tel.:", "Common.Views.About.txtVersion": "Vers<PERSON>", "Common.Views.AutoCorrectDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Aplicar ao trabalhar", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Correção automática", "Common.Views.AutoCorrectDialog.textAutoFormat": "Formato automático ao escrever", "Common.Views.AutoCorrectDialog.textBy": "Por", "Common.Views.AutoCorrectDialog.textDelete": "Eliminar", "Common.Views.AutoCorrectDialog.textHyperlink": "Internet e locais de rede com hiperligações", "Common.Views.AutoCorrectDialog.textMathCorrect": " Correção automática de matemática", "Common.Views.AutoCorrectDialog.textNewRowCol": "Incluir novas linhas e colunas na tabela", "Common.Views.AutoCorrectDialog.textRecognized": "Funções Reconhecidas", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "As seguintes expressões são expressões matemáticas reconhecidas. Não serão colocadas automaticamente em itálico.", "Common.Views.AutoCorrectDialog.textReplace": "Substituir", "Common.Views.AutoCorrectDialog.textReplaceText": "Substituir à medida que digita", "Common.Views.AutoCorrectDialog.textReplaceType": "Substitua o texto à medida que digita", "Common.Views.AutoCorrectDialog.textReset": "Redefinir", "Common.Views.AutoCorrectDialog.textResetAll": "Repor para a predefinição", "Common.Views.AutoCorrectDialog.textRestore": "Restaurar", "Common.Views.AutoCorrectDialog.textTitle": "Correção automática", "Common.Views.AutoCorrectDialog.textWarnAddRec": "As funções reconhecidas devem conter apenas as letras de A a Z, maiúsculas ou minúsculas.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Qualquer expressão que tenha adicionado será removida e as expressões removidas serão restauradas. Quer continuar?", "Common.Views.AutoCorrectDialog.warnReplace": "A correção automática para %1 já existe. Quer substituí-la?", "Common.Views.AutoCorrectDialog.warnReset": "Qualquer correção automática que tenha adicionado será removida e as alterações serão restauradas aos seus valores originais. Quer continuar?", "Common.Views.AutoCorrectDialog.warnRestore": "A correção automática para %1 já existe. Quer substituí-la?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "Enviar", "Common.Views.Comments.mniAuthorAsc": "Autor de A a Z", "Common.Views.Comments.mniAuthorDesc": "Autor Z a A", "Common.Views.Comments.mniDateAsc": "<PERSON><PERSON> anti<PERSON>", "Common.Views.Comments.mniDateDesc": "Novidades", "Common.Views.Comments.mniFilterGroups": "Filtrar por Grupo", "Common.Views.Comments.mniPositionAsc": "De cima", "Common.Views.Comments.mniPositionDesc": "Do fundo", "Common.Views.Comments.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textAddCommentToDoc": "Adicionar comentário ao documento", "Common.Views.Comments.textAddReply": "<PERSON><PERSON><PERSON><PERSON> resposta", "Common.Views.Comments.textAll": "Todos", "Common.Views.Comments.textAnonym": "Visitante", "Common.Views.Comments.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textComment": "Comment", "Common.Views.Comments.textComments": "Comentários", "Common.Views.Comments.textEdit": "<PERSON><PERSON>", "Common.Views.Comments.textEnterCommentHint": "Inserir seu coment<PERSON>rio aqui", "Common.Views.Comments.textHintAddComment": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textOpenAgain": "Abrir novamente", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "Resolver", "Common.Views.Comments.textResolved": "Resolvido", "Common.Views.Comments.textSort": "Ordenar comentá<PERSON>s", "Common.Views.Comments.textSortFilter": "Sort and filter comments", "Common.Views.Comments.textSortFilterMore": "Sort, filter and more", "Common.Views.Comments.textSortMore": "Sort and more", "Common.Views.Comments.textViewResolved": "Não tem permissão para reabrir o comentário", "Common.Views.Comments.txtEmpty": "Não há comentários na folha.", "Common.Views.CopyWarningDialog.textDontShow": "Não exibir esta mensagem novamente", "Common.Views.CopyWarningDialog.textMsg": "As ações copiar, cortar e colar através dos botões da barra de ferramentas ou através do menu de contexto apenas serão executadas neste separador.<br><br>Para copiar ou colar de outras aplicações deve utilizar estas teclas de atalho:", "Common.Views.CopyWarningDialog.textTitle": "Ações <PERSON>, cortar e colar", "Common.Views.CopyWarningDialog.textToCopy": "para copiar", "Common.Views.CopyWarningDialog.textToCut": "para cortar", "Common.Views.CopyWarningDialog.textToPaste": "para Colar", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Download", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Check the commands that will be displayed on the Quick Access Toolbar", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Print", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Quick Print", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Redo", "Common.Views.CustomizeQuickAccessDialog.textSave": "Save", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Customize quick access", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Undo", "Common.Views.DocumentAccessDialog.textLoading": "A carregar...", "Common.Views.DocumentAccessDialog.textTitle": "Definições de partilha", "Common.Views.DocumentPropertyDialog.errorDate": "You can choose a value from the calendar to store the value as Date.<br>If you enter a value manually, it will be stored as Text.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "No", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Yes", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "Property should have a title", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Title", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Yes\" or \"No\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Provide a valid number", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Text", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "Property should have a value", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Value", "Common.Views.DocumentPropertyDialog.txtTitle": "New Document Property", "Common.Views.Draw.hintEraser": "Borracha", "Common.Views.Draw.hintSelect": "Selecionar", "Common.Views.Draw.txtEraser": "Borracha", "Common.Views.Draw.txtHighlighter": "Marcador", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "Caneta", "Common.Views.Draw.txtSelect": "Selecionar", "Common.Views.Draw.txtSize": "<PERSON><PERSON><PERSON>", "Common.Views.EditNameDialog.textLabel": "Etiqueta:", "Common.Views.EditNameDialog.textLabelError": "Etiqueta não deve estar em branco.", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "Utilizadores que estão a editar o ficheiro:", "Common.Views.Header.textAddFavorite": "Marcar como favorito", "Common.Views.Header.textAdvSettings": "Definições avançadas", "Common.Views.Header.textBack": "Abrir localização do ficheiro", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "Ocultar barra de ferramentas", "Common.Views.Header.textHideLines": "Ocultar r<PERSON>", "Common.Views.Header.textHideStatusBar": "<PERSON><PERSON><PERSON><PERSON> as barras da folha e de estado", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "Apenas leitura", "Common.Views.Header.textRemoveFavorite": "Remover dos favoritos", "Common.Views.Header.textSaveBegin": "A guardar...", "Common.Views.Header.textSaveChanged": "Modificado", "Common.Views.Header.textSaveEnd": "<PERSON><PERSON> as alterações guardadas", "Common.Views.Header.textSaveExpander": "<PERSON><PERSON> as alterações guardadas", "Common.Views.Header.textShare": "Partilhar", "Common.Views.Header.textZoom": "Ampliação", "Common.Views.Header.tipAccessRights": "<PERSON><PERSON><PERSON> direitos de acesso ao documento", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipGoEdit": "<PERSON><PERSON> atual", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipPrintQuick": "Quick print", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Guardar", "Common.Views.Header.tipSearch": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndo": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndock": "Desacoplar em janela separada", "Common.Views.Header.tipUsers": "Ver utilizadores", "Common.Views.Header.tipViewSettings": "Definições de visualização", "Common.Views.Header.tipViewUsers": "Ver utilizadores e gerir direitos de acesso", "Common.Views.Header.txtAccessRights": "Alterar direitos de acesso", "Common.Views.Header.txtRename": "<PERSON>dar nome", "Common.Views.History.textCloseHistory": "<PERSON><PERSON><PERSON>", "Common.Views.History.textHideAll": "Ocultar alterações detalhadas", "Common.Views.History.textHighlightDeleted": "Highlight deleted", "Common.Views.History.textMore": "More", "Common.Views.History.textRestore": "Restaurar", "Common.Views.History.textShowAll": "Mostrar alterações detalhadas", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Version History", "Common.Views.ImageFromUrlDialog.textUrl": "Colar URL de uma imagem:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Este campo é obrigatório", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Este campo deve ser uma URL no formato \"http://www.example.com\"", "Common.Views.ListSettingsDialog.textBulleted": "Com Marcas de Lista", "Common.Views.ListSettingsDialog.textFromFile": "De um ficheiro", "Common.Views.ListSettingsDialog.textFromStorage": "Do armazenamento", "Common.Views.ListSettingsDialog.textFromUrl": "De um URL", "Common.Views.ListSettingsDialog.textNumbering": "Numerado", "Common.Views.ListSettingsDialog.textSelect": "Selecionar de", "Common.Views.ListSettingsDialog.tipChange": "Alterar lista", "Common.Views.ListSettingsDialog.txtBullet": "Marcador", "Common.Views.ListSettingsDialog.txtColor": "Cor", "Common.Views.ListSettingsDialog.txtImage": "Imagem", "Common.Views.ListSettingsDialog.txtImport": "Importar", "Common.Views.ListSettingsDialog.txtNewBullet": "Nova marca", "Common.Views.ListSettingsDialog.txtNewImage": "Nova imagem", "Common.Views.ListSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtOfText": "% do texto", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "Iniciar em", "Common.Views.ListSettingsDialog.txtSymbol": "Símbolo", "Common.Views.ListSettingsDialog.txtTitle": "Definições da lista", "Common.Views.ListSettingsDialog.txtType": "Tipo", "Common.Views.MacrosDialog.textCopy": "Copy", "Common.Views.MacrosDialog.textCustomFunction": "Custom function", "Common.Views.MacrosDialog.textDelete": "Delete", "Common.Views.MacrosDialog.textLoading": "Loading...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Make autostart", "Common.Views.MacrosDialog.textRename": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textRun": "Run", "Common.Views.MacrosDialog.textSave": "Save", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Unmake autostart", "Common.Views.MacrosDialog.tipFunctionAdd": "Add custom function", "Common.Views.MacrosDialog.tipMacrosAdd": "Add macros", "Common.Views.MacrosDialog.tipMacrosRun": "Run", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.textInvalidRange": "Intervalo de células inválido", "Common.Views.OpenDialog.textSelectData": "Selecionar dados", "Common.Views.OpenDialog.txtAdvanced": "Avançado", "Common.Views.OpenDialog.txtColon": "<PERSON><PERSON> pontos", "Common.Views.OpenDialog.txtComma": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtDelimiter": "Delimiter", "Common.Views.OpenDialog.txtDestData": "<PERSON><PERSON><PERSON>her onde colocar os dados", "Common.Views.OpenDialog.txtEmpty": "Este campo é obrigatório", "Common.Views.OpenDialog.txtEncoding": "Codificação", "Common.Views.OpenDialog.txtIncorrectPwd": "Palavra-passe inválida.", "Common.Views.OpenDialog.txtOpenFile": "Introduza a palavra-passe para abrir o ficheiro", "Common.Views.OpenDialog.txtOther": "Outro", "Common.Views.OpenDialog.txtPassword": "Palavra-passe", "Common.Views.OpenDialog.txtPreview": "Pré-visualizar", "Common.Views.OpenDialog.txtProtected": "Assim que introduzir a palavra-passe e abrir o ficheiro, a palavra-passe atual será reposta.", "Common.Views.OpenDialog.txtSemicolon": "Ponto e vírgula", "Common.Views.OpenDialog.txtSpace": "Espaço", "Common.Views.OpenDialog.txtTab": "Separador", "Common.Views.OpenDialog.txtTitle": "Escolher opções %1", "Common.Views.OpenDialog.txtTitleProtected": "<PERSON><PERSON><PERSON> protegido", "Common.Views.PasswordDialog.txtDescription": "Defina uma palavra-passe para proteger este documento", "Common.Views.PasswordDialog.txtIncorrectPwd": "Disparidade nas palavras-passe introduzidas", "Common.Views.PasswordDialog.txtPassword": "Palavra-passe", "Common.Views.PasswordDialog.txtRepeat": "Repetição de palavra-passe", "Common.Views.PasswordDialog.txtTitle": "Definir palavra-passe", "Common.Views.PasswordDialog.txtWarning": "Aviso: Se perder ou esquecer a palavra-passe, não será possível recuperá-la. Guarde-a num local seguro.", "Common.Views.PluginDlg.textLoading": "A carregar", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "Plugins", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "Iniciar", "Common.Views.Plugins.textStop": "<PERSON><PERSON>", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.Plugins.tipMore": "More", "Common.Views.Protection.hintAddPwd": "Cifrar com palavra-passe", "Common.Views.Protection.hintDelPwd": "Eliminar palavra-passe", "Common.Views.Protection.hintPwd": "Alt<PERSON>r ou eliminar palavra-passe", "Common.Views.Protection.hintSignature": "Adicionar assinatura digital ou linha de assinatura", "Common.Views.Protection.txtAddPwd": "Adicionar pala<PERSON>-passe", "Common.Views.Protection.txtChangePwd": "Alterar palavra-passe", "Common.Views.Protection.txtDeletePwd": "Eliminar palavra-passe", "Common.Views.Protection.txtEncrypt": "Encriptar", "Common.Views.Protection.txtInvisibleSignature": "Adicionar assinatura digital", "Common.Views.Protection.txtSignature": "Assinatura", "Common.Views.Protection.txtSignatureLine": "Adicionar linha de assinatura", "Common.Views.RecentFiles.txtOpenRecent": "Abrir recente", "Common.Views.RenameDialog.textName": "Nome do ficheiro", "Common.Views.RenameDialog.txtInvalidName": "O nome do ficheiro não pode ter qualquer um dos seguintes caracteres:", "Common.Views.ReviewChanges.hintNext": "Para a próxima alteração", "Common.Views.ReviewChanges.hintPrev": "Para a alteração anterior", "Common.Views.ReviewChanges.strFast": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.strFastDesc": "Coedição em tempo real. Todas as alterações são guardadas automaticamente.", "Common.Views.ReviewChanges.strStrict": "Estrito", "Common.Views.ReviewChanges.strStrictDesc": "Utilize o bot<PERSON> \"Guardar\" para sincronizar as alterações efetuadas por todos os utilizadores", "Common.Views.ReviewChanges.tipAcceptCurrent": "Aceitar alteração atual", "Common.Views.ReviewChanges.tipCoAuthMode": "Definir modo de coedição", "Common.Views.ReviewChanges.tipCommentRem": "Remover comentários", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Remover comentários atuais", "Common.Views.ReviewChanges.tipCommentResolve": "Resolver comentários", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Resolver comentários atuais", "Common.Views.ReviewChanges.tipHistory": "<PERSON>rar histó<PERSON>", "Common.Views.ReviewChanges.tipRejectCurrent": "Rejeitar alterações atuais", "Common.Views.ReviewChanges.tipReview": "Rastreio de alterações", "Common.Views.ReviewChanges.tipReviewView": "Selecione o modo em que pretende que as alterações sejam apresentadas", "Common.Views.ReviewChanges.tipSetDocLang": "Definir idioma do documento", "Common.Views.ReviewChanges.tipSetSpelling": "Verificação ortográfica", "Common.Views.ReviewChanges.tipSharing": "<PERSON><PERSON><PERSON> direitos de acesso ao documento", "Common.Views.ReviewChanges.txtAccept": "Aceitar", "Common.Views.ReviewChanges.txtAcceptAll": "Aceitar to<PERSON> as alteraç<PERSON><PERSON>", "Common.Views.ReviewChanges.txtAcceptChanges": "Aceitar alterações", "Common.Views.ReviewChanges.txtAcceptCurrent": "Aceitar alteração atual", "Common.Views.ReviewChanges.txtChat": "Gráfico", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Modo de co-edição", "Common.Views.ReviewChanges.txtCommentRemAll": "Remover todos os comentários", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Remover comentários atuais", "Common.Views.ReviewChanges.txtCommentRemMy": "Remover os meus comentários", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Remover os meus comentários atuais", "Common.Views.ReviewChanges.txtCommentRemove": "Remover", "Common.Views.ReviewChanges.txtCommentResolve": "Resolver", "Common.Views.ReviewChanges.txtCommentResolveAll": "Resolver todos os comentários", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Resolver comentários atuais", "Common.Views.ReviewChanges.txtCommentResolveMy": "Resolver os meus comentários", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Resolver os meus comentários atuais", "Common.Views.ReviewChanges.txtDocLang": "Língua", "Common.Views.ReviewChanges.txtFinal": "<PERSON><PERSON> as alteraç<PERSON>es ace<PERSON> (Pré-visualizar)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Hist<PERSON><PERSON><PERSON> <PERSON> ve<PERSON>ão", "Common.Views.ReviewChanges.txtMarkup": "<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON> (Editar)", "Common.Views.ReviewChanges.txtMarkupCap": "Marcação", "Common.Views.ReviewChanges.txtNext": "Próximo", "Common.Views.ReviewChanges.txtOriginal": "<PERSON><PERSON> as alteraç<PERSON>es recusadas (Pré-visualizar)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Anterior", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "<PERSON><PERSON><PERSON><PERSON> as alteraç<PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectChanges": "Rejeitar alterações", "Common.Views.ReviewChanges.txtRejectCurrent": "Rejeitar alterações atuais", "Common.Views.ReviewChanges.txtSharing": "Partilhar", "Common.Views.ReviewChanges.txtSpelling": "Verificação ortográfica", "Common.Views.ReviewChanges.txtTurnon": "Rastreio de alterações", "Common.Views.ReviewChanges.txtView": "Modo de exibição", "Common.Views.ReviewPopover.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textAddReply": "<PERSON><PERSON><PERSON><PERSON> resposta", "Common.Views.ReviewPopover.textCancel": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textComment": "Comment", "Common.Views.ReviewPopover.textEdit": "Ok", "Common.Views.ReviewPopover.textEnterComment": "Enter your comment here", "Common.Views.ReviewPopover.textMention": "+menção disponibiliza o acesso ao documento e envia um e-mail ao utilizador", "Common.Views.ReviewPopover.textMentionNotify": "+menção notifica o utilizador por e-mail", "Common.Views.ReviewPopover.textOpenAgain": "Abrir novamente", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "Resolver", "Common.Views.ReviewPopover.textViewResolved": "Não tem permissão para reabrir o comentário", "Common.Views.ReviewPopover.txtDeleteTip": "Eliminar", "Common.Views.ReviewPopover.txtEditTip": "<PERSON><PERSON>", "Common.Views.SaveAsDlg.textLoading": "A carregar", "Common.Views.SaveAsDlg.textTitle": "Pasta para guardar", "Common.Views.SearchPanel.textByColumns": "<PERSON><PERSON> colunas", "Common.Views.SearchPanel.textByRows": "<PERSON><PERSON> linhas", "Common.Views.SearchPanel.textCaseSensitive": "Diferenciar maiús<PERSON>s de minúsculas", "Common.Views.SearchPanel.textCell": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON><PERSON>es<PERSON>", "Common.Views.SearchPanel.textContentChanged": "Documento alterado.", "Common.Views.SearchPanel.textFind": "Localizar", "Common.Views.SearchPanel.textFindAndReplace": "Localizar e substituir", "Common.Views.SearchPanel.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFormulas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textItemEntireCell": "<PERSON><PERSON><PERSON><PERSON> da célula", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} items successfully replaced.", "Common.Views.SearchPanel.textLookIn": "Procurar em", "Common.Views.SearchPanel.textMatchUsingRegExp": "Correspondência ao usar expressões regulares", "Common.Views.SearchPanel.textName": "Nome", "Common.Views.SearchPanel.textNoMatches": "Sem correspondência", "Common.Views.SearchPanel.textNoSearchResults": "Sem resultados de pesquisa", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} items replaced. Remaining {2} items are locked by other users.", "Common.Views.SearchPanel.textReplace": "Substituir", "Common.Views.SearchPanel.textReplaceAll": "Substituir tudo", "Common.Views.SearchPanel.textReplaceWith": "Substituir com", "Common.Views.SearchPanel.textSearch": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSearchAgain": "{0}Executar nova pesquisa{1} para resultados precisos.", "Common.Views.SearchPanel.textSearchHasStopped": "A pesquisa parou", "Common.Views.SearchPanel.textSearchOptions": "Opções de pesquisa", "Common.Views.SearchPanel.textSearchResults": "Resultados da pesquisa: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textSelectDataRange": "Selecionar intervalo de dados", "Common.Views.SearchPanel.textSheet": "Fol<PERSON>", "Common.Views.SearchPanel.textSpecificRange": "Intervalo específico", "Common.Views.SearchPanel.textTooManyResults": "Existem demasiados resultados para poderem ser mostrados aqui", "Common.Views.SearchPanel.textValue": "Valor", "Common.Views.SearchPanel.textValues": "Valores", "Common.Views.SearchPanel.textWholeWords": "Palavras inteiras apenas", "Common.Views.SearchPanel.textWithin": "<PERSON><PERSON>", "Common.Views.SearchPanel.textWorkbook": "<PERSON><PERSON>", "Common.Views.SearchPanel.tipNextResult": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.tipPreviousResult": "Resultado anterior", "Common.Views.SelectFileDlg.textLoading": "A carregar", "Common.Views.SelectFileDlg.textTitle": "Selecionar fonte de dados", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "Size", "Common.Views.ShapeShadowDialog.txtTitle": "Adjust Shadow", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparency", "Common.Views.SignDialog.textBold": "Negrito", "Common.Views.SignDialog.textCertificate": "Certificado", "Common.Views.SignDialog.textChange": "Alterar", "Common.Views.SignDialog.textInputName": "Inserir nome do assinante", "Common.Views.SignDialog.textItalic": "Itálico", "Common.Views.SignDialog.textNameError": "O nome do assinante não pode estar vazio", "Common.Views.SignDialog.textPurpose": "Objetivo para assinar este documento", "Common.Views.SignDialog.textSelect": "Selecionar", "Common.Views.SignDialog.textSelectImage": "Selecionar imagem", "Common.Views.SignDialog.textSignature": "A assinatura parece ser", "Common.Views.SignDialog.textTitle": "Assinar o documento", "Common.Views.SignDialog.textUseImage": "ou clique \"Selecionar imagem\" para a utilizar como assinatura", "Common.Views.SignDialog.textValid": "Válida de %1 até %2", "Common.Views.SignDialog.tipFontName": "Nome do tipo de letra", "Common.Views.SignDialog.tipFontSize": "Tamanho do tipo de letra", "Common.Views.SignSettingsDialog.textAllowComment": "Permitir ao signatário inserir comentários no diálogo de assinatura", "Common.Views.SignSettingsDialog.textDefInstruction": "Antes de assinar este documento, verifique se o conteúdo que está a assinar está correto.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail", "Common.Views.SignSettingsDialog.textInfoName": "Nome", "Common.Views.SignSettingsDialog.textInfoTitle": "Título do Assinante", "Common.Views.SignSettingsDialog.textInstructions": "Instruções para o assinante", "Common.Views.SignSettingsDialog.textShowDate": "Mostrar data na linha de assinatura", "Common.Views.SignSettingsDialog.textTitle": "Definições de Assinatura", "Common.Views.SignSettingsDialog.txtEmpty": "Este campo é obrigatório", "Common.Views.SymbolTableDialog.textCharacter": "Caractere", "Common.Views.SymbolTableDialog.textCode": "Valor Unicode HEX", "Common.Views.SymbolTableDialog.textCopyright": "Assinatura Copyright", "Common.Views.SymbolTableDialog.textDCQuote": "Aspas Duplas de Fechamento", "Common.Views.SymbolTableDialog.textDOQuote": "Aspas de abertura", "Common.Views.SymbolTableDialog.textEllipsis": "Elipse horizontal", "Common.Views.SymbolTableDialog.textEmDash": "Travessão", "Common.Views.SymbolTableDialog.textEmSpace": "Espaço", "Common.Views.SymbolTableDialog.textEnDash": "Travessão", "Common.Views.SymbolTableDialog.textEnSpace": "Espaço", "Common.Views.SymbolTableDialog.textFont": "<PERSON><PERSON><PERSON> de letra", "Common.Views.SymbolTableDialog.textNBHyphen": "Hífen inseparável", "Common.Views.SymbolTableDialog.textNBSpace": "Espaço sem interrupção", "Common.Views.SymbolTableDialog.textPilcrow": "Sinal de antígrafo", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 de espaço (Em)", "Common.Views.SymbolTableDialog.textRange": "Intervalo", "Common.Views.SymbolTableDialog.textRecent": "Símbolos usados recentemente", "Common.Views.SymbolTableDialog.textRegistered": "Sinal Registado", "Common.Views.SymbolTableDialog.textSCQuote": "Aspas de Fechamento", "Common.Views.SymbolTableDialog.textSection": "Sinal de secção", "Common.Views.SymbolTableDialog.textShortcut": "Tecla de atalho", "Common.Views.SymbolTableDialog.textSHyphen": "Hífen virtual", "Common.Views.SymbolTableDialog.textSOQuote": "Apóstrofo de abertura", "Common.Views.SymbolTableDialog.textSpecial": "Caracteres especiais", "Common.Views.SymbolTableDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textTitle": "Símbolo", "Common.Views.SymbolTableDialog.textTradeMark": "Símbolo de Marca Registada.", "Common.Views.UserNameDialog.textDontShow": "Não perguntar novamente", "Common.Views.UserNameDialog.textLabel": "Etiqueta:", "Common.Views.UserNameDialog.textLabelError": "Etiqueta não deve estar em branco.", "SSE.Controllers.DataTab.strSheet": "Fol<PERSON>", "SSE.Controllers.DataTab.textAddExternalData": "A ligação a uma fonte externa foi adicionada. Pode atualizar essas ligações no separador Dados.", "SSE.Controllers.DataTab.textColumns": "Colunas", "SSE.Controllers.DataTab.textContinue": "Continue", "SSE.Controllers.DataTab.textDontUpdate": "<PERSON>ão atual<PERSON>", "SSE.Controllers.DataTab.textEmptyUrl": "Precisa de especificar o URL.", "SSE.Controllers.DataTab.textRows": "<PERSON><PERSON>", "SSE.Controllers.DataTab.textTurnOff": "Turn off AutoUpdate", "SSE.Controllers.DataTab.textUpdate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textWizard": "Texto para colunas", "SSE.Controllers.DataTab.txtDataValidation": "Validação dos dados", "SSE.Controllers.DataTab.txtErrorExternalLink": "Error: updating is failed", "SSE.Controllers.DataTab.txtExpand": "Expandir", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Os dados adjacentes à seleção não serão removidos. Deseja expandir a seleção para incluir os dados adjacentes ou continuar com a ordenação apenas das células atualmente selecionadas?", "SSE.Controllers.DataTab.txtExtendDataValidation": "A seleção contém algumas células sem definições de Validação de dados.<br>Deseja estender a validação de dados a estas células?", "SSE.Controllers.DataTab.txtImportWizard": "Wizard de Importação de Texto", "SSE.Controllers.DataTab.txtRemDuplicates": "Remover duplicados", "SSE.Controllers.DataTab.txtRemoveDataValidation": "A seleção contém mais do que um tipo de validação.<br>Eliminar as definições atuais e continuar?", "SSE.Controllers.DataTab.txtRemSelected": "Remover nos Selecionados", "SSE.Controllers.DataTab.txtUrlTitle": "Colar um URL de dados", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "This workbook contains links to external sources which update automatically. This might be unsafe.<br><br>If you trust them, press Continue.", "SSE.Controllers.DataTab.warnUpdateExternalData": "Esta pasta de trabalho contém ligações a uma ou mais fontes externas que podem não ser seguras.<br>Se confiar nas ligações, atualize-as para obter os dados mais recentes.", "SSE.Controllers.DocumentHolder.alignmentText": "Alinhamento", "SSE.Controllers.DocumentHolder.centerText": "Centro", "SSE.Controllers.DocumentHolder.deleteColumnText": "Eliminar coluna", "SSE.Controllers.DocumentHolder.deleteRowText": "Eliminar linha", "SSE.Controllers.DocumentHolder.deleteText": "Eliminar", "SSE.Controllers.DocumentHolder.errorInvalidLink": "A referência da ligação não existe. Deve corrigir ou eliminar a ligação.", "SSE.Controllers.DocumentHolder.guestText": "Visitante", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Coluna esquerda", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Coluna direita", "SSE.Controllers.DocumentHolder.insertRowAboveText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertRowBelowText": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.insertText": "Inserir", "SSE.Controllers.DocumentHolder.leftText": "E<PERSON>rda", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Aviso", "SSE.Controllers.DocumentHolder.rightText": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Opções de correção automática", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON><PERSON><PERSON> da coluna {0} símbolos ({1} pixels)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Altura da linha {0} pontos ({1} pixels)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Clique na ligação para a abrir ou clique e prima o botão do rato para selecionar a célula.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Insert Left", "SSE.Controllers.DocumentHolder.textInsertTop": "Inserir em cima", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Colar especial", "SSE.Controllers.DocumentHolder.textStopExpand": "Parar de expandir automaticamente as tabelas", "SSE.Controllers.DocumentHolder.textSym": "Sim.", "SSE.Controllers.DocumentHolder.tipIsLocked": "Este elemento está a ser editado por outro utilizador.", "SSE.Controllers.DocumentHolder.txtAboveAve": "<PERSON><PERSON><PERSON><PERSON> da média", "SSE.Controllers.DocumentHolder.txtAddBottom": "Adicionar contorno inferior", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Adicionar barra de fração", "SSE.Controllers.DocumentHolder.txtAddHor": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "SSE.Controllers.DocumentHolder.txtAddLB": "Adicionar linha inferior esquerda", "SSE.Controllers.DocumentHolder.txtAddLeft": "<PERSON><PERSON><PERSON><PERSON> con<PERSON> es<PERSON>", "SSE.Controllers.DocumentHolder.txtAddLT": "Adicionar linha superior esquerda", "SSE.Controllers.DocumentHolder.txtAddRight": "Adicionar contorno direito", "SSE.Controllers.DocumentHolder.txtAddTop": "Adicionar contorno superior", "SSE.Controllers.DocumentHolder.txtAddVer": "Adicionar lin<PERSON>", "SSE.Controllers.DocumentHolder.txtAlignToChar": "<PERSON><PERSON><PERSON> ao car<PERSON>", "SSE.Controllers.DocumentHolder.txtAll": "(Tudo)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Retorna todo o conteúdo da tabela ou colunas especificadas da tabela, incluindo os cabeçalhos de coluna, dados e linhas totais", "SSE.Controllers.DocumentHolder.txtAnd": "e", "SSE.Controllers.DocumentHolder.txtBegins": "Começa com", "SSE.Controllers.DocumentHolder.txtBelowAve": "<PERSON><PERSON><PERSON><PERSON> da média", "SSE.Controllers.DocumentHolder.txtBlanks": "(<PERSON>azi<PERSON>)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Propriedades do contorno", "SSE.Controllers.DocumentHolder.txtBottom": "Baixo", "SSE.Controllers.DocumentHolder.txtByField": "%1 de %2", "SSE.Controllers.DocumentHolder.txtColumn": "Coluna", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Alinhamento de colunas", "SSE.Controllers.DocumentHolder.txtContains": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Ligação copiada para a área de transferência", "SSE.Controllers.DocumentHolder.txtDataTableHint": "<PERSON><PERSON><PERSON> as c<PERSON><PERSON><PERSON> de dados da tabela ou as colu<PERSON> da tabela especificadas", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "<PERSON><PERSON><PERSON><PERSON> tamanho do argumento", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Eliminar argumento", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Eliminar quebra manual", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Eliminar caracteres anexos ", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Eliminar separadores e caracteres anexos", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Eliminar equação", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Eliminar radical", "SSE.Controllers.DocumentHolder.txtEnds": "termina com", "SSE.Controllers.DocumentHolder.txtEquals": "igual", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Igual à cor da célula", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Igual à cor do tipo de letra", "SSE.Controllers.DocumentHolder.txtExpand": "Expandir e Ordenar", "SSE.Controllers.DocumentHolder.txtExpandSort": "Os dados adjacentes à seleção não serão classificados. Deseja expandir a seleção para incluir os dados adjacentes ou continuar com a ordenação apenas das células atualmente selecionadas?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Baixo", "SSE.Controllers.DocumentHolder.txtFilterTop": "Parte superior", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Alterar para fração linear", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Alterar para fração inclinada", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Alterar para fração empilhada", "SSE.Controllers.DocumentHolder.txtGreater": "Superior a", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Superior a ou igual a", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Caractere sobre texto", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Carácter sob texto", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Devolve os cabeçalhos das colunas para a tabela ou colunas de tabela especificadas", "SSE.Controllers.DocumentHolder.txtHeight": "Altura", "SSE.Controllers.DocumentHolder.txtHideBottom": "Ocultar contorno inferior", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Ocultar limite inferior", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Ocultar o parêntesis de fecho", "SSE.Controllers.DocumentHolder.txtHideDegree": "Ocultar grau", "SSE.Controllers.DocumentHolder.txtHideHor": "Ocultar linha horizontal", "SSE.Controllers.DocumentHolder.txtHideLB": "Ocultar linha inferior esquerda", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON><PERSON><PERSON>r borda esquer<PERSON>", "SSE.Controllers.DocumentHolder.txtHideLT": "Ocultar linha superior esquerda", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Ocultar parêntese de abertura", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Ocultar espaço reservado", "SSE.Controllers.DocumentHolder.txtHideRight": "O<PERSON>ltar borda direita", "SSE.Controllers.DocumentHolder.txtHideTop": "Ocultar contorno superior", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Ocultar limite superior", "SSE.Controllers.DocumentHolder.txtHideVer": "Ocultar linha vertical", "SSE.Controllers.DocumentHolder.txtImportWizard": "Wizard de Importação de Texto", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Aumentar tamanho do argumento", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Inserir argumento após", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Inserir argumento antes", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Inserir quebra manual", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Inserir equação a seguir", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Inserir equação à frente", "SSE.Controllers.DocumentHolder.txtItems": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "<PERSON><PERSON> apenas texto", "SSE.Controllers.DocumentHolder.txtLess": "Inferior a", "SSE.Controllers.DocumentHolder.txtLessEquals": "Inferior a ou igual a", "SSE.Controllers.DocumentHolder.txtLimitChange": "Alterar localização de limites", "SSE.Controllers.DocumentHolder.txtLimitOver": "Limite sobre o texto", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Limite sob o texto", "SSE.Controllers.DocumentHolder.txtLockSort": "Os dados foram encontrados ao lado da sua seleção, mas não tem permissões suficientes para alterar essas células.<br>Deseja continuar com a seleção atual?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Combinar parênteses com a altura do argumento", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Alinhamento de matriz", "SSE.Controllers.DocumentHolder.txtNoChoices": "Não há escolhas para preencher a célula.<br>Apenas os valores de texto da coluna podem ser selecionados para substituição.", "SSE.Controllers.DocumentHolder.txtNotBegins": "não começa com", "SSE.Controllers.DocumentHolder.txtNotContains": "não contém", "SSE.Controllers.DocumentHolder.txtNotEnds": "não termina com", "SSE.Controllers.DocumentHolder.txtNotEquals": "não é igual a", "SSE.Controllers.DocumentHolder.txtOr": "Ou", "SSE.Controllers.DocumentHolder.txtOverbar": "Barra por cima do texto", "SSE.Controllers.DocumentHolder.txtPaste": "Colar", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formula sem limites", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Fórmula + largura da coluna", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Formatação de destino", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Colar apenas a formatação", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Fórmula + formato dos números", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Colar apenas a fórmula", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Fórmula + toda a formatação", "SSE.Controllers.DocumentHolder.txtPasteLink": "Colar ligação", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Imagem vinculada", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Intercalar Formatação Condicional", "SSE.Controllers.DocumentHolder.txtPastePicture": "Imagem", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Formatação da origem", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Transpor", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Valor + toda a formatação", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Valor + formato numérico", "SSE.Controllers.DocumentHolder.txtPasteValues": "Colar apenas os valores", "SSE.Controllers.DocumentHolder.txtPercent": "Percentagem", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Refazer a Expansão Automática da Tabela", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Remover barra de fração", "SSE.Controllers.DocumentHolder.txtRemLimit": "Remover limite", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Remover caractere destacado", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Remover barra", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Quer remover esta assinatura?<br><PERSON><PERSON> não pode ser anulado.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Remover scripts", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Remover subscrito", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Remover sobrescrito", "SSE.Controllers.DocumentHolder.txtRowHeight": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Scripts após o texto", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Scripts antes do texto", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Mostrar limite inferior", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Mostrar colchetes de fechamento", "SSE.Controllers.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON> gra<PERSON>", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Mostrar chaveta de abertura", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Exibir espaço reservado", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Mostrar limite superior", "SSE.Controllers.DocumentHolder.txtSorting": "A Ordenar", "SSE.Controllers.DocumentHolder.txtSortSelected": "Ordenar o que está selecionado", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Esticar colchetes", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Escolher apenas esta linha de uma coluna específica", "SSE.Controllers.DocumentHolder.txtTop": "Parte superior", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Devolve o total de linhas para a tabela ou coluna da tabela especificada", "SSE.Controllers.DocumentHolder.txtUnderbar": "Barra por baixo do texto", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Anular a Expansão Automática da Tabela", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Utilizar Assistente de Importação de Texto", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Clicar nesta ligação pode ser prejudicial ao seu dispositivo e dados.<br>Deseja continuar?", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.warnFilterError": "You need at least one field in the Values area in order to apply a value filter.", "SSE.Controllers.FormulaDialog.sCategoryAll": "<PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Custom", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Base de dados", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Data e hora", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Engenharia", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Financeiras", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Informação", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 últimos utilizados", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logical", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Procura e referência", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Matemática e trigonometria", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Estatísticas", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Texto e Dados", "SSE.Controllers.LeftMenu.newDocumentTitle": "<PERSON><PERSON><PERSON> de cálculo sem nome", "SSE.Controllers.LeftMenu.textByColumns": "<PERSON><PERSON> colunas", "SSE.Controllers.LeftMenu.textByRows": "<PERSON><PERSON> linhas", "SSE.Controllers.LeftMenu.textFormulas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textItemEntireCell": "Inser<PERSON> con<PERSON>lu<PERSON>", "SSE.Controllers.LeftMenu.textLoadHistory": "A carregar o histórico de versões...", "SSE.Controllers.LeftMenu.textLookin": "<PERSON><PERSON><PERSON> <PERSON>", "SSE.Controllers.LeftMenu.textNoTextFound": "Não foi possível localizar os dados procurados. Por favor ajuste as opções de pesquisa.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "A substituição foi realizada. {0} ocorrências foram ignoradas.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "A pesquisa foi realizada. Ocorrências substituídas: {0}", "SSE.Controllers.LeftMenu.textSave": "Save", "SSE.Controllers.LeftMenu.textSearch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "SSE.Controllers.LeftMenu.textSheet": "Fol<PERSON>", "SSE.Controllers.LeftMenu.textValues": "Valores", "SSE.Controllers.LeftMenu.textWarning": "Aviso", "SSE.Controllers.LeftMenu.textWithin": "<PERSON><PERSON>", "SSE.Controllers.LeftMenu.textWorkbook": "Pasta de trabalho", "SSE.Controllers.LeftMenu.txtUntitled": "<PERSON><PERSON> tí<PERSON>lo", "SSE.Controllers.LeftMenu.warnDownloadAs": "Se guardar o documento neste formato, perderá todos os atributos com exceção do texto.<br>Quer continuar?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "The CSV format does not support saving a multi-sheet file.<br>To keep the selected format and save only the current sheet, press Save.<br>To save the current spreadsheet, click Cancel and save it in a different format.", "SSE.Controllers.Main.confirmAddCellWatches": "Esta ação adicionará {0} células de observação.<br>Quer continuar?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "Esta ação apenas adiciona {0} células de observação para poupar memória.<br><PERSON><PERSON><PERSON><PERSON>?", "SSE.Controllers.Main.confirmMaxChangesSize": "O tamanho das ações excede o limite definido para o seu servidor.<br>Clique em \"Desfazer\" para cancelar a sua última ação ou em \"Continuar\" para manter a ação localmente (tem que descarregar o ficheiro ou copiar o seu conteúdo para garantir que nada seja perdido).", "SSE.Controllers.Main.confirmMoveCellRange": "O intervalo de célula de destino pode conter dados. Continuar a operação?", "SSE.Controllers.Main.confirmPutMergeRange": "Os dados de origem continham células unidas.<br>Elas não estavam unidas antes de serem coladas na tabela.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "As fórmulas na linha de cabeçalho serão removidas e convertidas para texto estático.<br> <PERSON><PERSON><PERSON> continuar?", "SSE.Controllers.Main.confirmReplaceHFPicture": "Only one picture can be inserted in each section of the header.<br>Press \"Replace\" to replace existing picture.<br>Press \"Keep\" to keep existing picture.", "SSE.Controllers.Main.convertationTimeoutText": "Excedeu o tempo limite de conversão.", "SSE.Controllers.Main.criticalErrorExtText": "Prima \"OK\" para voltar para a lista de documentos.", "SSE.Controllers.Main.criticalErrorTitle": "Erro", "SSE.Controllers.Main.downloadErrorText": "<PERSON>alha ao <PERSON>.", "SSE.Controllers.Main.downloadTextText": "A descarregar folha de cálculo...", "SSE.Controllers.Main.downloadTitleText": "A descarregar folha de cálculo", "SSE.Controllers.Main.errNoDuplicates": "Nenhum valor duplicado foi encontrado.", "SSE.Controllers.Main.errorAccessDeny": "Está a tentar executar uma ação para a qual não tem permissões.<br>Por favor contacte o administrador do servidor de documentos.", "SSE.Controllers.Main.errorArgsRange": "Existe um erro na fórmula.<br>U<PERSON>iz<PERSON> um argumento inválido.", "SSE.Controllers.Main.errorAutoFilterChange": "A operação não é permitida, uma vez que está a tentar deslocar células numa tabela da sua folha de cálculo.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "A operação não pode ser executada porque não é possível mover apenas uma parte da tabela.<br>Selecione outro intervalo de dados para que possa mover toda a tabela e tente novamente.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Não foi possível concluir a operação para o intervalo de células selecionado.<br>Selecione um intervalo de dados diferente e tente novamente.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "The operation cannot be performed because the area contains filtered cells.<br>Please unhide the filtered elements and try again.", "SSE.Controllers.Main.errorBadImageUrl": "URL inválido", "SSE.Controllers.Main.errorCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the spreadsheet.", "SSE.Controllers.Main.errorCannotUngroup": "Não foi possível desagrupar. Para começar um contorno do gráfico, selecione as linhas ou colunas e agrupe-as.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Não se pode utilizar este comando numa folha protegida. Para utilizar este comando, desproteja a folha. <br>Poderá ser-lhe solicitado que introduza uma palavra-passe.", "SSE.Controllers.Main.errorChangeArray": "Não se pode alterar parte de uma matriz.", "SSE.Controllers.Main.errorChangeFilteredRange": "Isto irá alterar um intervalo filtrado na sua folha de cálculo.<br>Para completar esta tarefa, por favor remova os filtros automáticos.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "A célula ou gráfico que está a tentar mudar está numa folha protegida.<br>Para fazer uma mudança, desbloqueia a folha. Poderá ser-lhe solicitado que introduza uma palavra-passe.", "SSE.Controllers.Main.errorCircularReference": "There are one or more circular references where a formula refers to its own cell either directly or indirectly.<br>Try removing or changing these references, or moving the formulas to different cells.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Conexão com servidor perdida. O documento não pode ser editado neste momento.", "SSE.Controllers.Main.errorConnectToServer": "Não foi possível guardar o documento. Verifique a sua ligação de rede ou contacte o administrador.<br>Ao clicar em 'OK', surgirá uma caixa de diálogo para descarregar o documento.", "SSE.Controllers.Main.errorConvertXml": "The file has an unsupported format.<br>Only XML Spreadsheet 2003 format can be used.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Este comando não pode ser utilizado com várias seleções.<br>Selecionar uma única gama e tentar novamente.", "SSE.Controllers.Main.errorCountArg": "Existe um erro na fórmula.<br>Utilizou um número de argumentos inválido.", "SSE.Controllers.Main.errorCountArgExceed": "Um erro na fórmula inserida.<br>Número de argumentos está excedido.", "SSE.Controllers.Main.errorCreateDefName": "Os intervalos nomeados existentes não podem ser criados e os novos também não podem ser editados porque alguns deles estão a ser editados.", "SSE.Controllers.Main.errorCreateRange": "The existing ranges cannot be edited and the new ones cannot be created<br>at the moment as some of them are being edited.", "SSE.Controllers.Main.errorDatabaseConnection": "Erro externo.<br><PERSON><PERSON> de conexão ao banco de dados. Entre em contato com o suporte caso o erro persista.", "SSE.Controllers.Main.errorDataEncrypted": "Foram recebidas alterações cifradas que não puderam ser decifradas.", "SSE.Controllers.Main.errorDataRange": "Intervalo de dados inválido.", "SSE.Controllers.Main.errorDataValidate": "O valor introduzido não é válido.<br>Um utilizador restringiu os valores que podem ser utilizados neste campo.", "SSE.Controllers.Main.errorDefaultMessage": "Código do erro: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Está a tentar apagar uma coluna que contém uma célula protegida. As células protegidas não podem ser apagadas enquanto a folha de cálculo estiver protegida.<br>Para apagar uma célula bloqueada, desproteja a folha. É possível ter que introduzir uma palavra-passe.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Está a tentar apagar uma linha que contém uma célula protegida. As células protegidas não podem ser apagadas enquanto a folha de cálculo estiver protegida.<br>Para apagar uma célula bloqueada, desproteja a folha. É possível ter que introduzir uma palavra-passe.", "SSE.Controllers.Main.errorDependentsNoFormulas": "O comando Rastrear Dependentes não encontrou fórmulas que se refiram à célula ativa.", "SSE.Controllers.Main.errorDirectUrl": "Verifique a ligação ao documento.<br><PERSON>e ser uma ligação direta para o ficheiro a descarregar.", "SSE.Controllers.Main.errorEditingDownloadas": "Ocorreu um erro ao trabalhar no documento.<br>Utilize a opção 'Descarregar como...' para guardar uma cópia do ficheiro num disco.", "SSE.Controllers.Main.errorEditingSaveas": "Ocorreu um erro ao trabalhar no documento.<br>Utilize a opção 'Descarregar como...' para guardar a cópia do ficheiro num disco.", "SSE.Controllers.Main.errorEditView": "A vista de folha existente não pode ser editada e as novas também não podem ser editadas porque algumas delas estão a ser editadas.", "SSE.Controllers.Main.errorEmailClient": "Não foi possível encontrar nenhum cliente de e-mail.", "SSE.Controllers.Main.errorFilePassProtect": "O ficheiro está protegido por palavra-passe e não pode ser aberto.", "SSE.Controllers.Main.errorFileRequest": "Erro externo.<br>Erro ao pedir o ficheiro. Se o erro se mantiver, deve contactar o suporte.", "SSE.Controllers.Main.errorFileSizeExceed": "O tamanho do documento excede o limite permitido pelo servidor.<br>Contacte o administrador do servidor de documentos para mais informações.", "SSE.Controllers.Main.errorFileVKey": "<PERSON>rro externo.<br>Chave de segurança inválida. Se este erro se mantiver, deve contactar o suporte.", "SSE.Controllers.Main.errorFillRange": "Não foi possível preencher o intervalo de células.<br><PERSON><PERSON> as células unidas têm que ter o mesmo tamanho.", "SSE.Controllers.Main.errorForceSave": "Ocorreu um erro ao guardar o ficheiro. Utilize a opção 'Descarregar como' para guardar o ficheiro num disco ou, em alternativa, tente mais tarde.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Cannot enter a formula for an item or field name in a pivot table report.", "SSE.Controllers.Main.errorFormulaName": "Existe um erro na fórmula.<br>Utilizou um nome de fórmula inválido.", "SSE.Controllers.Main.errorFormulaParsing": "Erro interno ao analisar a fórmula.", "SSE.Controllers.Main.errorFrmlMaxLength": "O comprimento da sua fórmula excede o limite de 8192 caracteres.<br>Por favor, edite-a e tente novamente.", "SSE.Controllers.Main.errorFrmlMaxReference": "Não pode inserir esta fórmula porque possui demasiados valores,<br>referências de célula, e/ou nomes.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Os valores de texto em fórmulas estão limitados a 255 caracteres.<br>Utilize a função CONCATENATE ou o operador de concatenação (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "A função refere-se a uma folha que não existe.<br>Por favor, verifique os dados e tente novamente.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Não foi possível completar a operação para o intervalo de células selecionado. <br>Selecionou um intervalo de uma forma que a primeira linha da tabela estava na mesma linha <br>então a tabela que criou sobrepôs-se à atual.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Não foi possível completar a operação para o intervalo de células selecionado. Selecione um intervalo que não inclua outras tabelas.", "SSE.Controllers.Main.errorInconsistentExt": "Ocorreu um erro ao abrir o ficheiro.<br>O conteúdo do ficheiro não coincide com a sua extensão.", "SSE.Controllers.Main.errorInconsistentExtDocx": "Ocorreu um erro ao abrir o ficheiro.<br>O conteúdo do ficheiro corresponde a um documento de texto (por exemplo, docx), mas a extensão de ficheiro não é consistente: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "Ocorreu um erro ao abrir o ficheiro.<br>O conteúdo do ficheiro corresponde a um dos seguintes formatos: pdf/djvu/xps/oxps, mas a extensão de ficheiro não é consistente: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "Ocorreu um erro ao abrir o ficheiro.<br>O conteúdo do ficheiro corresponde a uma apresentação (por exemplo, pptx), mas a extensão de ficheiro não é consistente: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "Ocorreu um erro ao abrir o ficheiro.<br>O conteúdo do ficheiro corresponde a uma folha de cálculo (por exemplo, xlsx), mas a extensão de ficheiro não é consistente: %1.", "SSE.Controllers.Main.errorInvalidRef": "Enter a correct name for the selection or a valid reference to go to.", "SSE.Controllers.Main.errorKeyEncrypt": "Descritor de chave desconhecido", "SSE.Controllers.Main.errorKeyExpire": "Descritor de chave caducado", "SSE.Controllers.Main.errorLabledColumnsPivot": "Para criar uma tabela dinâmica, deve utilizar dados organizados em lista e com cabeçalho de colunas.", "SSE.Controllers.Main.errorLoadingFont": "Tipos de letra não carregados.<br>Contacte o administrador do servidor de documentos.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "A referência para a localização ou intervalo de dados não é válida.", "SSE.Controllers.Main.errorLockedAll": "Não foi possível efetuar a ação porque a folha está bloqueada por outro utilizador.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "One of the cells involved in the goal seek process has been modified by another user.", "SSE.Controllers.Main.errorLockedCellPivot": "<PERSON>ão pode alterar dados dentro de uma tabela dinâmica.", "SSE.Controllers.Main.errorLockedWorksheetRename": "Não foi possível alterar o nome da folha porque o nome está a ser alterado por outro utilizador.", "SSE.Controllers.Main.errorMaxPoints": "O número máximo de pontos em série, por gráfico, é 4096.", "SSE.Controllers.Main.errorMoveRange": "Não é possível alterar parte de uma célula mesclada", "SSE.Controllers.Main.errorMoveSlicerError": "As segmentações de dados não podem ser copiadas de um livro para outro.<br>Tente de novo selecionando a tabela inteira tal como as segmentações de dados.", "SSE.Controllers.Main.errorMultiCellFormula": "Intervalo de fórmulas multi-célula não são permitidas em tabelas.", "SSE.Controllers.Main.errorNoDataToParse": "Nenhum dado foi selecionado para análise.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.Main.errorOpenWarning": "Uma das fórmulas excede o limite de 8192 caracteres.<br>A fórmula foi removida.", "SSE.Controllers.Main.errorOperandExpected": "Operando esperado", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "A palavra-passe que introduziu não está correta.<br>Verifique se a tecla CAPS LOCK está desligada e não se esqueça de utilizar a capitalização correta.", "SSE.Controllers.Main.errorPasteInPivot": "We can't make this change for the selected cells because it will affect a pivot table.<br>Use the field list to change the report.", "SSE.Controllers.Main.errorPasteMaxRange": "Disparidade nas áreas copiar e colar.<br>Deve selecionar áreas com o mesmo tamanho ou então clique na primeira célula de uma linha para colar as cé<PERSON><PERSON> copiadas.", "SSE.Controllers.Main.errorPasteMultiSelect": "Esta ação não pode ser feita com uma seleção de múltiplos intervalos.<br>Selecionar uma única gama e tentar novamente.", "SSE.Controllers.Main.errorPasteSlicerError": "As segmentações de dados não podem ser copiadas de um livro para outro.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Pivot table field name already exists.", "SSE.Controllers.Main.errorPivotGroup": "Não foi possível agrupar essa seleção.", "SSE.Controllers.Main.errorPivotOverlap": "O relatório da tabela dinâmica não se pode sobrepor a uma tabela.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "O relatório da tabela dinâmica foi guardado sem os dados relacionados.<br>Utilize o botão 'Recarregar' para atualizar o relatório.", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "The Trace Precedents command requires that the active cell contain a formula which includes a valid references.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Infelizmente, não é possível imprimir mais do que 1500 páginas de uma vez na atual versão do programa.<br>Esta restrição será removida em versões futuras.", "SSE.Controllers.Main.errorProcessSaveResult": "<PERSON>alha ao guardar", "SSE.Controllers.Main.errorProtectedRange": "This range is not allowed for editing.", "SSE.Controllers.Main.errorSaveWatermark": "This file contains a watermark image linked to another domain.<br>To make it visible in PDF, update the watermark image so it links from the same domain as your document, or upload it from your computer.", "SSE.Controllers.Main.errorServerVersion": "A versão do editor foi atualizada. A página será recarregada para aplicar as alterações.", "SSE.Controllers.Main.errorSessionAbsolute": "A sessão de edição caducou. Recarregue a página.", "SSE.Controllers.Main.errorSessionIdle": "Este documento não foi editado durante muito tempo. Tente recarregar a página.", "SSE.Controllers.Main.errorSessionToken": "A ligação ao servidor foi interrompida. Tente recarregar a página.", "SSE.Controllers.Main.errorSetPassword": "Não foi possível definir a palavra-passe.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "A referência de localização não é válida porque as células não estão todas na mesma coluna ou linha. <br><PERSON><PERSON><PERSON><PERSON> as c<PERSON>lu<PERSON> que estejam todas numa única coluna ou linha.", "SSE.Controllers.Main.errorStockChart": "Ordem de linha inválida. Para criar um gráfico de cotações, coloque os dados na folha pela seguinte ordem:<br>preço de abertura, preço máximo, preço mínimo, preço de fecho.", "SSE.Controllers.Main.errorToken": "O 'token' de segurança do documento não está formatado corretamente.<br>Contacte o administrador do servidor de documentos.", "SSE.Controllers.Main.errorTokenExpire": "O 'token' de segurança do documento caducou.<br>Contacte o administrador do servidor de documentos.", "SSE.Controllers.Main.errorUnexpectedGuid": "Erro externo.<br>GUID inesperado. Entre em contato com o suporte caso o erro persista.", "SSE.Controllers.Main.errorUpdateVersion": "A versão do ficheiro foi alterada. A página será recarregada.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "A ligação foi restaurada e a versão do ficheiro alterada.<br><PERSON><PERSON> de poder trabalhar, deve descarregar o ficheiro ou copiar o seu conteúdo para garantir que nada se perde e, depois, recarregar esta página.", "SSE.Controllers.Main.errorUserDrop": "De momento, não é possível aceder ao ficheiro.", "SSE.Controllers.Main.errorUsersExceed": "Excedeu o número máximo de utilizadores permitidos pelo seu plano", "SSE.Controllers.Main.errorViewerDisconnect": "Ligação perdida. Ainda pode ver o documento mas<br>não o conseguirá descarregar até que a ligação seja restaurada e a página recarregada.", "SSE.Controllers.Main.errorWrongBracketsCount": "Um erro na fórmula inserida.<br><PERSON>úmero errado de parênteses está sendo usado.", "SSE.Controllers.Main.errorWrongOperator": "Um erro na fórmula inserida.<br>Operador errado está sendo usado.", "SSE.Controllers.Main.errorWrongPassword": "A palavra-passe que introduziu não está correta.", "SSE.Controllers.Main.errRemDuplicates": "Duplicar valores encontrados e apagados; {0}, valores únicos restantes: {1}.", "SSE.Controllers.Main.leavePageText": "Este documento tem alterações não guardadas. Clique 'Ficar na página' para que o documento seja guardado automaticamente. Clique 'Sair da página' para rejeitar todas as alterações.", "SSE.Controllers.Main.leavePageTextOnClose": "<PERSON><PERSON> as alterações não guardadas nesta folha de cálculo serão perdidas.<br> Clique \"Cancelar\" e depois \"Guardar\" para as guardar. Clique \"OK\" para descartar todas as alterações não guardadas.", "SSE.Controllers.Main.loadFontsTextText": "A carregar dados...", "SSE.Controllers.Main.loadFontsTitleText": "A carregar dados", "SSE.Controllers.Main.loadFontTextText": "A carregar dados...", "SSE.Controllers.Main.loadFontTitleText": "A carregar dados", "SSE.Controllers.Main.loadImagesTextText": "A carregar imagens...", "SSE.Controllers.Main.loadImagesTitleText": "A carregar imagens", "SSE.Controllers.Main.loadImageTextText": "A carregar imagem...", "SSE.Controllers.Main.loadImageTitleText": "A carregar imagem", "SSE.Controllers.Main.loadingDocumentTitleText": "A carregar folha de cálculo", "SSE.Controllers.Main.notcriticalErrorTitle": "Aviso", "SSE.Controllers.Main.openErrorText": "Ocorreu um erro ao abrir o ficheiro.", "SSE.Controllers.Main.openTextText": "A abrir Folha de Cálculo...", "SSE.Controllers.Main.openTitleText": "A abrir Folha de Cálculo", "SSE.Controllers.Main.pastInMergeAreaError": "Não é possível alterar parte de uma célula mesclada", "SSE.Controllers.Main.printTextText": "A imprimir folha de cálculo...", "SSE.Controllers.Main.printTitleText": "A imprimir folha de cálculo", "SSE.Controllers.Main.reloadButtonText": "Re<PERSON><PERSON><PERSON> p<PERSON>gina", "SSE.Controllers.Main.requestEditFailedMessageText": "Alguém está editando este documento neste momento. Tente novamente mais tarde.", "SSE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON>", "SSE.Controllers.Main.saveErrorText": "Ocorreu um erro ao guardar o ficheiro.", "SSE.Controllers.Main.saveErrorTextDesktop": "Este ficheiro não pode ser guardado ou criado.<br>Os motivos podem ser: <br>1. O ficheiro é apenas de leitura. <br>2. O ficheiro está a ser editado por outro utilizador.<br>3. O disco está cheio ou danificado.", "SSE.Controllers.Main.saveTextText": "A guardar Folha de Cálculo...", "SSE.Controllers.Main.saveTitleText": "A guardar Folha de Cálculo", "SSE.Controllers.Main.scriptLoadError": "A ligação está muito lenta e alguns dos componentes não foram carregados. Tente recarregar a página.", "SSE.Controllers.Main.textAnonymous": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textApplyAll": "Aplicar a todas as equações", "SSE.Controllers.Main.textBuyNow": "Visitar website", "SSE.Controllers.Main.textChangesSaved": "<PERSON><PERSON> as alterações guardadas", "SSE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textCloseTip": "Clique para fechar a dica", "SSE.Controllers.Main.textConfirm": "Confirmação", "SSE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "SSE.Controllers.Main.textContactUs": "Contacte a equipa comercial", "SSE.Controllers.Main.textContinue": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textConvertEquation": "Esta equação foi criada com uma versão anterior da aplicação e já não é suportada. Para a editar, tem que converter a equação para o formato Office Math ML.<br>Converter agora?", "SSE.Controllers.Main.textCustomLoader": "Tenha em conta de que, de acordo com os termos da licença, não tem permissões para alterar o carregador.<br><PERSON>r favor contacte a equipa comercial.", "SSE.Controllers.Main.textDisconnect": "A ligação está perdida", "SSE.Controllers.Main.textFillOtherRows": "<PERSON><PERSON><PERSON> outras linhas", "SSE.Controllers.Main.textFormulaFilledAllRows": "As linhas preenchidas pela fórmula{0} têm dados. O preenchimento das outras linhas vazias pode demorar alguns minutos.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "As primeiras {0} linhas foram preenchidas pela fórmula. O preenchimento das outras linhas vazias pode demorar alguns minutos.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "<PERSON><PERSON><PERSON> as primeiras {0} linhas foram preenchidas pela fórmula por razões de poupança de memória. Existem outras {1} linhas nesta folha. Pode preenchê-las manualmente.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "<PERSON><PERSON><PERSON> as primeiras {0} linhas foram preenchidas pela fórmula por razões de poupança de memória. As outras linhas nesta folha não têm dados.", "SSE.Controllers.Main.textGuest": "Visitante", "SSE.Controllers.Main.textHasMacros": "O ficheiro contém macros automáticas.<br><PERSON><PERSON><PERSON> executar as macros?", "SSE.Controllers.Main.textKeep": "Keep", "SSE.Controllers.Main.textLearnMore": "<PERSON><PERSON> mais", "SSE.Controllers.Main.textLoadingDocument": "A carregar folha de cálculo", "SSE.Controllers.Main.textLongName": "Introduza um nome com menos de 128 caracteres.", "SSE.Controllers.Main.textNeedSynchronize": "Você tem atualizações", "SSE.Controllers.Main.textNo": "Não", "SSE.Controllers.Main.textNoLicenseTitle": "Atingiu o limite da licença", "SSE.Controllers.Main.textPaidFeature": "Funcionalidade paga", "SSE.Controllers.Main.textPleaseWait": "A operação pode demorar mais tempo do que o esperado. Aguarde...", "SSE.Controllers.Main.textReconnect": "A ligação foi reposta", "SSE.Controllers.Main.textRemember": "Memorizar esta escolha para todos os ficheiros", "SSE.Controllers.Main.textRememberMacros": "Memor<PERSON>r escolha para todas as macros", "SSE.Controllers.Main.textRenameError": "O nome de utilizador não pode estar em branco.", "SSE.Controllers.Main.textRenameLabel": "Introduza um nome a ser usado para colaboração", "SSE.Controllers.Main.textReplace": "Replace", "SSE.Controllers.Main.textRequestMacros": "Uma macro faz um pedido de URL. Quer permitir o pedido à %1?", "SSE.Controllers.Main.textShape": "Forma", "SSE.Controllers.Main.textStrict": "Modo estrito", "SSE.Controllers.Main.textText": "Texto", "SSE.Controllers.Main.textTryQuickPrint": "You have selected Quick print: the entire document will be printed on the last selected or default printer.<br>Do you want to continue?", "SSE.Controllers.Main.textTryUndoRedo": "As funções Desfazer/<PERSON><PERSON><PERSON> foram desativadas para se poder coeditar o documento.<br>Clique no botão 'Modo estrito' para ativar este modo de edição e editar o ficheiro sem ser incomodado por outros utilizadores enviando apenas as suas alterações assim que terminar e guardar. Pode alternar entre modos de coedição através das definições avançadas.", "SSE.Controllers.Main.textTryUndoRedoWarn": "As funções Desfazer/Refazer estão desativadas no modo de co-edição rápida.", "SSE.Controllers.Main.textUndo": "Undo", "SSE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "SSE.Controllers.Main.textUpdating": "Updating", "SSE.Controllers.Main.textYes": "<PERSON>m", "SSE.Controllers.Main.titleLicenseExp": "Licença caducada", "SSE.Controllers.Main.titleLicenseNotActive": "License not active", "SSE.Controllers.Main.titleServerVersion": "Editor atual<PERSON><PERSON>", "SSE.Controllers.Main.titleUpdateVersion": "Version changed", "SSE.Controllers.Main.txtAccent": "Destaque", "SSE.Controllers.Main.txtAll": "(Tudo)", "SSE.Controllers.Main.txtArt": "Your text here", "SSE.Controllers.Main.txtBasicShapes": "Formas básicas", "SSE.Controllers.Main.txtBlank": "(vazio)", "SSE.Controllers.Main.txtButtons": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtByField": "%1 de %2", "SSE.Controllers.Main.txtCallouts": "Textos explicativos", "SSE.Controllers.Main.txtCharts": "Grá<PERSON><PERSON>", "SSE.Controllers.Main.txtClearFilter": "Limpar filtro", "SSE.Controllers.Main.txtColLbls": "Etiquetas da coluna", "SSE.Controllers.Main.txtColumn": "Coluna", "SSE.Controllers.Main.txtConfidential": "Confidencial", "SSE.Controllers.Main.txtDate": "Data", "SSE.Controllers.Main.txtDays": "<PERSON><PERSON>", "SSE.Controllers.Main.txtDiagramTitle": "Título do <PERSON>a", "SSE.Controllers.Main.txtEditingMode": "Definir modo de edição...", "SSE.Controllers.Main.txtErrorLoadHistory": "Falha ao carregar histórico", "SSE.Controllers.Main.txtFiguredArrows": "Setas figuradas", "SSE.Controllers.Main.txtFile": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtGrandTotal": "Total Geral", "SSE.Controllers.Main.txtGroup": "Grupo", "SSE.Controllers.Main.txtHours": "<PERSON><PERSON>", "SSE.Controllers.Main.txtInfo": "Info", "SSE.Controllers.Main.txtLines": "<PERSON><PERSON>", "SSE.Controllers.Main.txtMath": "Matemática", "SSE.Controllers.Main.txtMinutes": "minutos", "SSE.Controllers.Main.txtMonths": "Meses", "SSE.Controllers.Main.txtMultiSelect": "Selecionar-Múltiplo", "SSE.Controllers.Main.txtNone": "None", "SSE.Controllers.Main.txtOr": "%1 ou %2", "SSE.Controllers.Main.txtPage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPageOf": "Página %1 de %2", "SSE.Controllers.Main.txtPages": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPicture": "Picture", "SSE.Controllers.Main.txtPivotTable": "PivotTable", "SSE.Controllers.Main.txtPreparedBy": "Elaborado por", "SSE.Controllers.Main.txtPrintArea": "<PERSON><PERSON>_<PERSON>_<PERSON><PERSON>", "SSE.Controllers.Main.txtQuarter": "Quart.", "SSE.Controllers.Main.txtQuarters": "Quartos", "SSE.Controllers.Main.txtRectangles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtRow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtRowLbls": "Etiquetas das Linhas", "SSE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "SSE.Controllers.Main.txtScheme_Aspect": "Aspect", "SSE.Controllers.Main.txtScheme_Blue": "Blue", "SSE.Controllers.Main.txtScheme_Blue_Green": "Blue Green", "SSE.Controllers.Main.txtScheme_Blue_II": "Blue II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "Blue Warm", "SSE.Controllers.Main.txtScheme_Grayscale": "Grayscale", "SSE.Controllers.Main.txtScheme_Green": "Green", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Green Yellow", "SSE.Controllers.Main.txtScheme_Marquee": "Marquee", "SSE.Controllers.Main.txtScheme_Median": "Median", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Orange", "SSE.Controllers.Main.txtScheme_Orange_Red": "Orange Red", "SSE.Controllers.Main.txtScheme_Paper": "Paper", "SSE.Controllers.Main.txtScheme_Red": "Red", "SSE.Controllers.Main.txtScheme_Red_Orange": "Red Orange", "SSE.Controllers.Main.txtScheme_Red_Violet": "Red Violet", "SSE.Controllers.Main.txtScheme_Slipstream": "Slipstream", "SSE.Controllers.Main.txtScheme_Violet": "Violet", "SSE.Controllers.Main.txtScheme_Violet_II": "Violet II", "SSE.Controllers.Main.txtScheme_Yellow": "Yellow", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "Yellow Orange", "SSE.Controllers.Main.txtSeconds": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSeries": "Série", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Chamada da linha 1 (contorno e barra de destaque)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Chamada da linha 2 (contorno e barra de destaque)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Chamada da linha 3 (contorno e barra de destaque)", "SSE.Controllers.Main.txtShape_accentCallout1": "Chamada da linha 1 (barra de destaque)", "SSE.Controllers.Main.txtShape_accentCallout2": "Chamada da linha 2 (barra de destaque)", "SSE.Controllers.Main.txtShape_accentCallout3": "Chamada da linha 3 (barra de destaque)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "<PERSON><PERSON><PERSON> ou Anterior", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Botão de início", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Botão vazio", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Botão Documento", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Botão Final", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Bot<PERSON><PERSON> e/ou Avançar", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Botão Ajuda", "SSE.Controllers.Main.txtShape_actionButtonHome": "Botão Base", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Botão Informação", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Botão Filme", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Botão de Voltar", "SSE.Controllers.Main.txtShape_actionButtonSound": "Botão Som", "SSE.Controllers.Main.txtShape_arc": "Arco", "SSE.Controllers.Main.txtShape_bentArrow": "Seta curvada", "SSE.Controllers.Main.txtShape_bentConnector5": "Conector angular", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Conector de seta angular", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Conector de seta dupla angulada", "SSE.Controllers.Main.txtShape_bentUpArrow": "Seta para cima dobrada", "SSE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_blockArc": "Arco de bloco", "SSE.Controllers.Main.txtShape_borderCallout1": "Chamada da linha 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Chamada da linha 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Chamada da linha 3", "SSE.Controllers.Main.txtShape_bracePair": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_callout1": "Chamada da linha 1 (sem contorno)", "SSE.Controllers.Main.txtShape_callout2": "Chamada da linha 2 (sem contorno)", "SSE.Controllers.Main.txtShape_callout3": "Chamada da linha 3 (sem contorno)", "SSE.Controllers.Main.txtShape_can": "Pode", "SSE.Controllers.Main.txtShape_chevron": "Divisa", "SSE.Controllers.Main.txtShape_chord": "Acorde", "SSE.Controllers.Main.txtShape_circularArrow": "Seta circular", "SSE.Controllers.Main.txtShape_cloud": "Nuvem", "SSE.Controllers.Main.txtShape_cloudCallout": "Texto explicativo na nuvem", "SSE.Controllers.Main.txtShape_corner": "Canto", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Conector curvado", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Conector de seta curvada", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Conector de seta dupla curvado", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Seta curvada para baixo", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Seta curvada para a esquerda", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Seta curvava para a direita", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Seta curvada para cima", "SSE.Controllers.Main.txtShape_decagon": "Decágono", "SSE.Controllers.Main.txtShape_diagStripe": "<PERSON><PERSON><PERSON> diagonal", "SSE.Controllers.Main.txtShape_diamond": "Diamante", "SSE.Controllers.Main.txtShape_dodecagon": "Dodecágono", "SSE.Controllers.Main.txtShape_donut": "Dónute", "SSE.Controllers.Main.txtShape_doubleWave": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_downArrow": "Seta para baixo", "SSE.Controllers.Main.txtShape_downArrowCallout": "Chamada com seta para baixo", "SSE.Controllers.Main.txtShape_ellipse": "Elipse", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Faixa curvada para baixo", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Faixa curvada para cima", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Fluxograma: Processo alternativo", "SSE.Controllers.Main.txtShape_flowChartCollate": "Fluxograma: Agrupar", "SSE.Controllers.Main.txtShape_flowChartConnector": "Fluxograma: Conector", "SSE.Controllers.Main.txtShape_flowChartDecision": "Fluxograma: Decisão", "SSE.Controllers.Main.txtShape_flowChartDelay": "Fluxograma: Atraso", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Fluxograma: Exibir", "SSE.Controllers.Main.txtShape_flowChartDocument": "Fluxograma: Documento", "SSE.Controllers.Main.txtShape_flowChartExtract": "Fluxograma: Extrair", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Fluxograma: <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Fluxograma: Armazenamento interno", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Fluxograma: Disco magnético", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Fluxograma: Armazenamento de acesso direto", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Fluxograma: Armazenamento de acesso sequencial", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Fluxograma: Entrada manual", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Fluxograma: Operação manual", "SSE.Controllers.Main.txtShape_flowChartMerge": "Fluxograma: Unir", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Fluxograma: <PERSON><PERSON><PERSON><PERSON> documentos", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Fluxograma: Conector fora da página", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Fluxograma: Dad<PERSON> armaz<PERSON>", "SSE.Controllers.Main.txtShape_flowChartOr": "Fluxograma: Ou", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Fluxograma: Processo predefinido", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Fluxograma: Preparação", "SSE.Controllers.Main.txtShape_flowChartProcess": "Fluxograma: Processo", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Fluxograma: Cartão", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Fluxograma: <PERSON><PERSON> perfurada", "SSE.Controllers.Main.txtShape_flowChartSort": "Fluxograma: Ordenar", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Fluxograma: Junção de Soma", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Fluxograma: Exterminador", "SSE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON> do<PERSON>", "SSE.Controllers.Main.txtShape_frame": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_halfFrame": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_heart": "Coração", "SSE.Controllers.Main.txtShape_heptagon": "<PERSON>pt<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_hexagon": "Hexágono", "SSE.Controllers.Main.txtShape_homePlate": "Pentágono", "SSE.Controllers.Main.txtShape_horizontalScroll": "Deslocação horizontal", "SSE.Controllers.Main.txtShape_irregularSeal1": "Explosão 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Explosão 2", "SSE.Controllers.Main.txtShape_leftArrow": "Seta para esquerda", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Chamada com seta para a esquerda", "SSE.Controllers.Main.txtShape_leftBrace": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_leftRightArrow": "Seta para a esquerda e para a direita", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Chamada com seta para a esquerda e direita", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Seta para cima, para a direita e para a esquerda", "SSE.Controllers.Main.txtShape_leftUpArrow": "Seta para a esquerda e para cima", "SSE.Controllers.Main.txtShape_lightningBolt": "Relâmpago", "SSE.Controllers.Main.txtShape_line": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithArrow": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "<PERSON>a dupla", "SSE.Controllers.Main.txtShape_mathDivide": "Divisão", "SSE.Controllers.Main.txtShape_mathEqual": "Igual", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Multiplicar", "SSE.Controllers.Main.txtShape_mathNotEqual": "Não é igual", "SSE.Controllers.Main.txtShape_mathPlus": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_moon": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_noSmoking": "Símbolo \"Não\"", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Seta entalhada para a direita", "SSE.Controllers.Main.txtShape_octagon": "Octógono", "SSE.Controllers.Main.txtShape_parallelogram": "Paralelograma", "SSE.Controllers.Main.txtShape_pentagon": "Pentágono", "SSE.Controllers.Main.txtShape_pie": "Tarte", "SSE.Controllers.Main.txtShape_plaque": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_plus": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_polyline1": "Rabisco", "SSE.Controllers.Main.txtShape_polyline2": "Forma livre", "SSE.Controllers.Main.txtShape_quadArrow": "<PERSON>a cruzada", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Chamada com seta cruzada", "SSE.Controllers.Main.txtShape_rect": "Re<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ribbon": "Faixa para baixo", "SSE.Controllers.Main.txtShape_ribbon2": "Faixa para cima", "SSE.Controllers.Main.txtShape_rightArrow": "Seta para direita", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Chamada com seta para a direita", "SSE.Controllers.Main.txtShape_rightBrace": "Chaveta à Direita", "SSE.Controllers.Main.txtShape_rightBracket": "Parê<PERSON><PERSON> direito", "SSE.Controllers.Main.txtShape_round1Rect": "Retângulo de Apenas Um Canto Redondo", "SSE.Controllers.Main.txtShape_round2DiagRect": "Retângulo Diagonal Redondo", "SSE.Controllers.Main.txtShape_round2SameRect": "Retângulo do Mesmo Lado Redondo", "SSE.Controllers.Main.txtShape_roundRect": "Retângulo de Cantos Redondos", "SSE.Controllers.Main.txtShape_rtTriangle": "Triângulo à Direita", "SSE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_snip1Rect": "Retângulo de Canto Cortado", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Retângulo de Cantos Diagonais Cortados", "SSE.Controllers.Main.txtShape_snip2SameRect": "Retângulo de Cantos Cortados No Mesmo Lado", "SSE.Controllers.Main.txtShape_snipRoundRect": "Retângulo Com Canto Arredondado e Canto Cortado", "SSE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star10": "Estrela de 10 pontos", "SSE.Controllers.Main.txtShape_star12": "Estrela de 12 pontos", "SSE.Controllers.Main.txtShape_star16": "Estrela de 16 pontos", "SSE.Controllers.Main.txtShape_star24": "Estrela de 24 pontos", "SSE.Controllers.Main.txtShape_star32": "Estrela de 32 pontos", "SSE.Controllers.Main.txtShape_star4": "Estrela de 4 pontos", "SSE.Controllers.Main.txtShape_star5": "Estrela de 5 pontos", "SSE.Controllers.Main.txtShape_star6": "Estrela de 6 pontos", "SSE.Controllers.Main.txtShape_star7": "Estrela de 7 pontos", "SSE.Controllers.Main.txtShape_star8": "Estrela de 8 pontos", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Seta riscada para a direita", "SSE.Controllers.Main.txtShape_sun": "Sol", "SSE.Controllers.Main.txtShape_teardrop": "Lágrima", "SSE.Controllers.Main.txtShape_textRect": "Caixa de texto", "SSE.Controllers.Main.txtShape_trapezoid": "Trapé<PERSON>", "SSE.Controllers.Main.txtShape_triangle": "Triângulo", "SSE.Controllers.Main.txtShape_upArrow": "Seta para cima", "SSE.Controllers.Main.txtShape_upArrowCallout": "Chamada com seta para cima", "SSE.Controllers.Main.txtShape_upDownArrow": "Seta para cima e para baixo", "SSE.Controllers.Main.txtShape_uturnArrow": "Seta em forma de U", "SSE.Controllers.Main.txtShape_verticalScroll": "Deslocação vertical", "SSE.Controllers.Main.txtShape_wave": "On<PERSON>", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Chamada oval", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "<PERSON><PERSON> retangular", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "<PERSON><PERSON> retangular arredondada", "SSE.Controllers.Main.txtSheet": "Sheet", "SSE.Controllers.Main.txtSlicer": "<PERSON>licer", "SSE.Controllers.Main.txtStarsRibbons": "Estrelas e arco-íris", "SSE.Controllers.Main.txtStyle_Bad": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Calculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Check_Cell": "Verifique a célula", "SSE.Controllers.Main.txtStyle_Comma": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Texto explicativo", "SSE.Controllers.Main.txtStyle_Good": "Bo<PERSON>", "SSE.Controllers.Main.txtStyle_Heading_1": "Título 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Título 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Título 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Título 4", "SSE.Controllers.Main.txtStyle_Input": "Introdução", "SSE.Controllers.Main.txtStyle_Linked_Cell": "<PERSON><PERSON><PERSON><PERSON> vinculada", "SSE.Controllers.Main.txtStyle_Neutral": "Neutro", "SSE.Controllers.Main.txtStyle_Normal": "Normal", "SSE.Controllers.Main.txtStyle_Note": "<PERSON>a", "SSE.Controllers.Main.txtStyle_Output": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Percent": "Percentagem", "SSE.Controllers.Main.txtStyle_Title": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Total": "Total", "SSE.Controllers.Main.txtStyle_Warning_Text": "Texto de aviso", "SSE.Controllers.Main.txtTab": "Separador", "SSE.Controllers.Main.txtTable": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtTime": "<PERSON><PERSON>", "SSE.Controllers.Main.txtUnlock": "Desb<PERSON>que<PERSON>", "SSE.Controllers.Main.txtUnlockRange": "Desbloquear Intervalo", "SSE.Controllers.Main.txtUnlockRangeDescription": "Introduza a palavra-passe para alterar este intervalo:", "SSE.Controllers.Main.txtUnlockRangeWarning": "O intervalo que está a tentar alterar está protegido por uma palavra-passe.", "SSE.Controllers.Main.txtValues": "Valores", "SSE.Controllers.Main.txtView": "View", "SSE.Controllers.Main.txtXAxis": "Eixo X", "SSE.Controllers.Main.txtYAxis": "Eixo Y", "SSE.Controllers.Main.txtYears": "<PERSON><PERSON>", "SSE.Controllers.Main.unknownErrorText": "<PERSON><PERSON> desconhecido.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "<PERSON>u navegador não é suportado.", "SSE.Controllers.Main.uploadDocExtMessage": "Formato de documento desconhecido.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Nenhum documento foi carregado.", "SSE.Controllers.Main.uploadDocSizeMessage": "Excedeu o limite de tamanho para o documento.", "SSE.Controllers.Main.uploadImageExtMessage": "Formato desconhecido.", "SSE.Controllers.Main.uploadImageFileCountMessage": "<PERSON>enhuma imagem foi carregada.", "SSE.Controllers.Main.uploadImageSizeMessage": "A imagem é demasiado grande. O tamanho máximo é de 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "A carregar imagem...", "SSE.Controllers.Main.uploadImageTitleText": "A carregar imagem", "SSE.Controllers.Main.waitText": "Por favor, aguarde...", "SSE.Controllers.Main.warnBrowserIE9": "A aplicação não funciona corretamente com IE9. Deve utilizar IE10 ou superior", "SSE.Controllers.Main.warnBrowserZoom": "A definição de ampliação atual do navegador não é totalmente suportada. Prima Ctrl+0 para repor o valor padrão.", "SSE.Controllers.Main.warnLicenseAnonymous": "<PERSON><PERSON> negado a utilizadores anónimos.<br>Este documento será aberto apenas para visualização.", "SSE.Controllers.Main.warnLicenseBefore": "License not active.<br>Please contact your administrator.", "SSE.Controllers.Main.warnLicenseExceeded": "Atingiu o limite de ligações simultâneas a %1 editores. Este documento será aberto no modo de leitura.<br>Contacte o administrador para obter mais de<PERSON>hes.", "SSE.Controllers.Main.warnLicenseExp": "A sua licença caducou.<br>Deve atualizar a licença e recarregar a página.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Licença caducada.<br>Não pode editar o documento.<br>Por favor contacte o seu administrador.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Tem que renovar a sua licença.<br>A edição de documentos está limitada.<br>Contacte o administrador de sistemas para obter acesso completo.", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Atingiu o limite de %1 editores. Contacte o seu administrador para obter detalhes.", "SSE.Controllers.Main.warnNoLicense": "Atingiu o limite de ligações simultâneas a %1 editores. Este documento será aberto no modo de leitura.<br>Contacte a equipa comercial %1 para saber mais sobre os termos de licenciamento.", "SSE.Controllers.Main.warnNoLicenseUsers": "Atingiu o limite de %1 editores. Contacte a equipa comercial %1 para obter mais informações.", "SSE.Controllers.Main.warnProcessRightsChange": "Você não tem permissões para editar o ficheiro.", "SSE.Controllers.PivotTable.strSheet": "Sheet", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "The item cannot be added or modified. PivotTable report has this field in Filters.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "No actions with calculated items are allowed for this active cell.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "If one or more PivotTable have calculated items, no fields can be used in data area two or more times, or in the data area and another area at the same time.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Calculated items do not work with custom subtotals.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "An item name cannot be found. Check that you've typed name correctly and the item is present in the PivotTable report.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Averages, standard deviations, and variances are not supported when a PivotTable report has calculated items.", "SSE.Controllers.Print.strAllSheets": "<PERSON><PERSON> as folhas", "SSE.Controllers.Print.textFirstCol": "Primeira coluna", "SSE.Controllers.Print.textFirstRow": "Primeira linha", "SSE.Controllers.Print.textFrozenCols": "Colunas fixadas", "SSE.Controllers.Print.textFrozenRows": "<PERSON><PERSON>", "SSE.Controllers.Print.textInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Controllers.Print.textNoRepeat": "Não repetir", "SSE.Controllers.Print.textRepeat": "Repetir…", "SSE.Controllers.Print.textSelectRange": "Selecionar intervalo", "SSE.Controllers.Print.txtCustom": "Personalizado", "SSE.Controllers.Print.txtZoomToPage": "Zoom to page", "SSE.Controllers.Search.textInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Controllers.Search.textNoTextFound": "Não foi possível localizar os dados procurados. Ajuste as opções de pesquisa.", "SSE.Controllers.Search.textReplaceSkipped": "A substituição foi realizada. {0} ocorrências foram ignoradas.", "SSE.Controllers.Search.textReplaceSuccess": "A pesquisa foi concluída. {0} ocorrências foram substituídas", "SSE.Controllers.Statusbar.errorLastSheet": "Pasta de trabalho deve ter no mínimo uma planilha visível.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Não é possível excluir uma planilha.", "SSE.Controllers.Statusbar.strSheet": "Fol<PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>Sem Ligação</b><br>A tentar ligar. Por favor, verifique as definições de ligação.", "SSE.Controllers.Statusbar.textSheetViewTip": "Está em modo de Vista de Folha. Os filtros e a classificação são visíveis apenas para si e para aqueles que ainda se encontram nesta vista.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Está em modo de Vista de Folha. Os filtros são visíveis apenas para si e para aqueles que ainda se encontram nesta vista.", "SSE.Controllers.Statusbar.warnDeleteSheet": "As folhas de trabalho selecionadas podem conter dados. Quer prosseguir?", "SSE.Controllers.Statusbar.zoomText": "Ampliação {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "O tipo de letra que está prestes a guardar não está disponível no dispositivo atual.<br>O texto será mostrado com um dos tipos de letra do sistema e o tipo de letra guardado será utilizado quando estiver disponível.<br><PERSON><PERSON><PERSON><PERSON>?", "SSE.Controllers.Toolbar.errorComboSeries": "Para criar um gráfico de combinação, selecione pelo menos duas séries de dados.", "SSE.Controllers.Toolbar.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "ERRO! O número máximo de séries de dados, por gráfico, é 255.", "SSE.Controllers.Toolbar.errorStockChart": "Ordem de linha inválida. Para criar um gráfico de cotações, coloque os dados na folha pela seguinte ordem:<br>preço de abertura, preço máximo, preço mínimo, preço de fecho.", "SSE.Controllers.Toolbar.helpCalcItems": "Work with calculated items in Pivot Tables.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Calculated items", "SSE.Controllers.Toolbar.helpFastUndo": "Easily undo changes while collaborating on sheets in Fast mode.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "\"Undo\" in real-time co-editing", "SSE.Controllers.Toolbar.helpMergeShapes": "Combine, fragment, intersect, subtract shapes in seconds to create custom visuals.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Merge shapes", "SSE.Controllers.Toolbar.textAccent": "Destaques", "SSE.Controllers.Toolbar.textBracket": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textDirectional": "Direcional", "SSE.Controllers.Toolbar.textFontSizeErr": "O valor inserido não está correto.<br>Introduza um valor numérico entre 1 e 409.", "SSE.Controllers.Toolbar.textFraction": "Frações", "SSE.Controllers.Toolbar.textFunction": "Funções", "SSE.Controllers.Toolbar.textIndicator": "Indicadores", "SSE.Controllers.Toolbar.textInsert": "Inserir", "SSE.Controllers.Toolbar.textIntegral": "Inteiros", "SSE.Controllers.Toolbar.textLargeOperator": "Grandes operadores", "SSE.Controllers.Toolbar.textLimitAndLog": "Limites e logaritmos", "SSE.Controllers.Toolbar.textLongOperation": "Operação longa", "SSE.Controllers.Toolbar.textMatrix": "Matrizes", "SSE.Controllers.Toolbar.textOperator": "Operadores", "SSE.Controllers.Toolbar.textPivot": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRadical": "Radicais", "SSE.Controllers.Toolbar.textRating": "Classificações", "SSE.Controllers.Toolbar.textRecentlyUsed": "Utilizado recentemente", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textShapes": "Formas", "SSE.Controllers.Toolbar.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textWarning": "Aviso", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Seta para direita-esquerda acima", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Seta adiante para cima", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Seta para direita acima", "SSE.Controllers.Toolbar.txtAccent_Bar": "Barr<PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Barra inferior", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Barra superior", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "<PERSON><PERSON><PERSON><PERSON> embal<PERSON> (com Placeholder)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "F<PERSON>rmula embalada (Exemplo)", "SSE.Controllers.Toolbar.txtAccent_Check": "Verificar", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Chave <PERSON>", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Chave Superior", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vetor A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC com barra superior ", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y com barra superior", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Ponto <PERSON>lo", "SSE.Controllers.Toolbar.txtAccent_DDot": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Dot": "Ponto", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Barra superior dupla", "SSE.Controllers.Toolbar.txtAccent_Grave": "Grave", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Agrupamento de caracteres abaixo", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Agrupamento de caracteres acima", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Arpão adiante para cima", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Arpão para direita acima", "SSE.Controllers.Toolbar.txtAccent_Hat": "Acento circunflexo", "SSE.Controllers.Toolbar.txtAccent_Smile": "Breve", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Til", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Parênteses com separadores", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Parênteses com separadores", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_Curve": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Parênteses com separadores", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Casos (Duas Condições)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Casos (Três Condições)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Exemplo de casos", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Coeficiente binominal", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Coeficiente binominal", "SSE.Controllers.Toolbar.txtBracket_Line": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_Round": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parênteses com separadores", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_Square": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_UppLim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Colchete Simples", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Colchete Simples", "SSE.Controllers.Toolbar.txtDeleteCells": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtExpand": "Expandir e Ordenar", "SSE.Controllers.Toolbar.txtExpandSort": "Os dados adjacentes à seleção não serão classificados. Deseja expandir a seleção para incluir os dados adjacentes ou continuar com a ordenação apenas das células atualmente selecionadas?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Fração inclinada", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "Diferencial", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "Diferencial", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "Diferencial", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "Diferencial", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Fração linear", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi sobre 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Fração pequena", "SSE.Controllers.Toolbar.txtFractionVertical": "Fração Empilhada", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Função cosseno inverso", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Função cosseno inverso hiperbólico", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Função cotangente inversa", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Função cotangente inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Função cossecante inversa", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Função cossecante inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Função secante inversa", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Função secante inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Função seno inverso", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Função seno inverso hiperbólico", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Função tangente inversa", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Função tangente inversa hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Cos": "Função cosseno", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Função cosseno hiperbólico", "SSE.Controllers.Toolbar.txtFunction_Cot": "Função cotangente", "SSE.Controllers.Toolbar.txtFunction_Coth": "Função cotangente hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Csc": "Função cossecante", "SSE.Controllers.Toolbar.txtFunction_Csch": "Função co-secante hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON>ta seno", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "<PERSON><PERSON><PERSON><PERSON> da tangente", "SSE.Controllers.Toolbar.txtFunction_Sec": "Função secante", "SSE.Controllers.Toolbar.txtFunction_Sech": "Função secante hiperbólica", "SSE.Controllers.Toolbar.txtFunction_Sin": "Função de seno", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Função seno hiperbólico", "SSE.Controllers.Toolbar.txtFunction_Tan": "Função da tangente", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Função tangente hiperbólica", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Personalizado", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Data e Modelo", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Bom, Mau e Neutro", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "Sem nome", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Formato de número", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Estilo de textos explicativos com tema", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Títulos e Cabeçalhos", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Personalizado", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Escuro", "SSE.Controllers.Toolbar.txtGroupTable_Light": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Médio", "SSE.Controllers.Toolbar.txtInsertCells": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegral": "Inteiro", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Teta diferencial", "SSE.Controllers.Toolbar.txtIntegral_dx": "Diferencial x", "SSE.Controllers.Toolbar.txtIntegral_dy": "Diferencial y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Inteiro", "SSE.Controllers.Toolbar.txtIntegralDouble": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtIntegralOriented": "Contorno integral", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Contorno integral", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Integral de Superfície", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Integral de Superfície", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Integral de Superfície", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Contorno integral", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Integral de Volume", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Integral de Volume", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Integral de Volume", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Inteiro", "SSE.Controllers.Toolbar.txtIntegralTriple": "Inteiro <PERSON>", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Inteiro <PERSON>", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Inteiro <PERSON>", "SSE.Controllers.Toolbar.txtInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Triangular", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Triangular", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Triangular", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Triangular", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Triangular", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "So<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "So<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "So<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Produ<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "União", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Interseção", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Interseção", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Interseção", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Interseção", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Interseção", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Produ<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produ<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produ<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produ<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produ<PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "So<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "So<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "So<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "So<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "So<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "União", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "União", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "União", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "União", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "União", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Exemplo limite", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Exemplo máximo", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Limite", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Logaritmo natural", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logaritmo", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logaritmo", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Máximo", "SSE.Controllers.Toolbar.txtLimitLog_Min": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtLockSort": "Os dados foram encontrados ao lado da sua seleção, mas não tem permissões suficientes para alterar essas células.<br>Deseja continuar com a seleção atual?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "Matriz vazia 1x2", "SSE.Controllers.Toolbar.txtMatrix_1_3": "Matriz vazia 1x3", "SSE.Controllers.Toolbar.txtMatrix_2_1": "Matriz vazia 2x1", "SSE.Controllers.Toolbar.txtMatrix_2_2": "Matriz vazia 2x2", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Matriz vazia com parênteses", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Matriz vazia com parênteses", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Matriz vazia com parênteses", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Matriz vazia com parênteses", "SSE.Controllers.Toolbar.txtMatrix_2_3": "Matriz vazia 2x3", "SSE.Controllers.Toolbar.txtMatrix_3_1": "Matriz vazia 3x1", "SSE.Controllers.Toolbar.txtMatrix_3_2": "Matriz vazia 3x2", "SSE.Controllers.Toolbar.txtMatrix_3_3": "Matriz vazia 3x3", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Pontos da linha base", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Pontos de linha média", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Pontos diagonais", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Pontos verticais", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "<PERSON><PERSON> dispersa", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "<PERSON><PERSON> dispersa", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "Matriz de identidade 2x2", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Matriz de identidade 3x3", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "Matriz de identidade 3x3", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matriz de identidade 3x3", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Seta para direita esquerda abaixo", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Seta para direita-esquerda acima", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Seta adiante para baixo", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Seta adiante para cima", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Seta para direita abaixo", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Seta para direita acima", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Dois-pontos-Sinal de Igual", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Resul<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Resultados de Delta", "SSE.Controllers.Toolbar.txtOperator_Definition": "Igual a por definição", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta igual a", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Seta para direita esquerda abaixo", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Seta para direita-esquerda acima", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Seta adiante para baixo", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Seta adiante para cima", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Seta para direita abaixo", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Seta para direita acima", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Sinal de Igual-Sinal de Igual", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Sinal de Menos-Sinal de Igual", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Sinal de Mais-Sinal de Igual", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Medido por", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Radical", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Radical", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Raiz quadrada com grau", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Raiz cúbica", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Radical com grau", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Raiz quadrada", "SSE.Controllers.Toolbar.txtScriptCustom_1": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_2": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_3": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_4": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptSub": "Subscrito", "SSE.Controllers.Toolbar.txtScriptSubSup": "Subscrito-Sobrescrito", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Subscrito-Sobrescrito Esquerdo", "SSE.Controllers.Toolbar.txtScriptSup": "Sobrescrito", "SSE.Controllers.Toolbar.txtSorting": "A Ordenar", "SSE.Controllers.Toolbar.txtSortSelected": "Ordenar o que está selecionado", "SSE.Controllers.Toolbar.txtSymbol_about": "Aproximadamente", "SSE.Controllers.Toolbar.txtSymbol_additional": "Complemento", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Alef", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alfa", "SSE.Controllers.Toolbar.txtSymbol_approx": "Quase igual a", "SSE.Controllers.Toolbar.txtSymbol_ast": "Operador de asterisco", "SSE.Controllers.Toolbar.txtSymbol_beta": "Beta", "SSE.Controllers.Toolbar.txtSymbol_beth": "Aposta", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Operador de marcador", "SSE.Controllers.Toolbar.txtSymbol_cap": "Interseção", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Raiz cúbica", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Reticências horizontais de linha média", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Aproximadamente igual a", "SSE.Controllers.Toolbar.txtSymbol_cup": "União", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Reticências diagonal para baixo à direita", "SSE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Sinal de divisão", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Seta para baixo", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Conjunto vazio", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsílon", "SSE.Controllers.Toolbar.txtSymbol_equals": "Igual", "SSE.Controllers.Toolbar.txtSymbol_equiv": "<PERSON><PERSON><PERSON><PERSON><PERSON> a", "SSE.Controllers.Toolbar.txtSymbol_eta": "Eta", "SSE.Controllers.Toolbar.txtSymbol_exists": "Existe", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Fatorial", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "<PERSON><PERSON>us Fahrenheit", "SSE.Controllers.Toolbar.txtSymbol_forall": "Para todos", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gama", "SSE.Controllers.Toolbar.txtSymbol_geq": "Superior a ou igual a", "SSE.Controllers.Toolbar.txtSymbol_gg": "Muito superior a", "SSE.Controllers.Toolbar.txtSymbol_greater": "Superior a", "SSE.Controllers.Toolbar.txtSymbol_in": "Elemento de", "SSE.Controllers.Toolbar.txtSymbol_inc": "Incremento", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Infinidade", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Capa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Seta para esquerda", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Seta esquerda-direita", "SSE.Controllers.Toolbar.txtSymbol_leq": "Inferior a ou igual a", "SSE.Controllers.Toolbar.txtSymbol_less": "Inferior a", "SSE.Controllers.Toolbar.txtSymbol_ll": "Muito inferior a", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "Sinal de Menos-Sinal de <PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "Não igual a", "SSE.Controllers.Toolbar.txtSymbol_ni": "Contém como membro", "SSE.Controllers.Toolbar.txtSymbol_not": "Não entrar", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Não existe", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Ômega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Diferencial parcial", "SSE.Controllers.Toolbar.txtSymbol_percent": "Percentagem", "SSE.Controllers.Toolbar.txtSymbol_phi": "Fi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_pm": "Sinal de Menos-Sinal de Igual", "SSE.Controllers.Toolbar.txtSymbol_propto": "Proporcional a", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Quarta raiz", "SSE.Controllers.Toolbar.txtSymbol_qed": "<PERSON><PERSON> da prova", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Reticências diagonal direitas para cima", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rô", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Seta para direita", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Sinal de Radical", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Portanto", "SSE.Controllers.Toolbar.txtSymbol_theta": "Teta", "SSE.Controllers.Toolbar.txtSymbol_times": "Sinal de multiplicação", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Seta para cima", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Ípsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "<PERSON><PERSON><PERSON> de Epsílon", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Variante de fi", "SSE.Controllers.Toolbar.txtSymbol_varpi": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varrho": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Reticências verticais", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Zeta", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "<PERSON><PERSON><PERSON> Tabela Escuro", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Estilo de Tabela Claro", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Estilo de Tabela Médio", "SSE.Controllers.Toolbar.warnLongOperation": "A operação que está prestes a realizar pode levar muito tempo a concluir.<br>Tem a certeza de que quer continuar?", "SSE.Controllers.Toolbar.warnMergeLostData": "Apenas os dados da célula superior esquerda permanecerão na célula unida.<br>Tem a certeza de que deseja continuar? ", "SSE.Controllers.Toolbar.warnNoRecommended": "To create a chart, select the cells that contain the data you'd like to use.<br>If you have names for the rows and columns and you'd like use them as labels, include them in your selection.", "SSE.Controllers.Viewport.textFreezePanes": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Viewport.textFreezePanesShadow": "Mostrar sombra dos painéis fixados", "SSE.Controllers.Viewport.textHideFBar": "Ocultar barra de fórmula<PERSON>", "SSE.Controllers.Viewport.textHideGridlines": "O<PERSON>ltar lin<PERSON> da g<PERSON>", "SSE.Controllers.Viewport.textHideHeadings": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Separador decimal", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Separador de milhares", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Definições utilizadas para reconhecimento numérico", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Qualificador de texto", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Definições avançadas", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(nenhum)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Filtro personalizado", "SSE.Views.AutoFilterDialog.textAddSelection": "Adicionar seleção atual ao filtro", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Brancos}", "SSE.Views.AutoFilterDialog.textSelectAll": "Selecionar todos", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Selecionar todos os resultados", "SSE.Views.AutoFilterDialog.textWarning": "Aviso", "SSE.Views.AutoFilterDialog.txtAboveAve": "<PERSON><PERSON><PERSON> da média", "SSE.Views.AutoFilterDialog.txtAfter": "After...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "Todas as datas no período", "SSE.Views.AutoFilterDialog.txtApril": "April", "SSE.Views.AutoFilterDialog.txtAugust": "August", "SSE.Views.AutoFilterDialog.txtBefore": "Before...", "SSE.Views.AutoFilterDialog.txtBegins": "Começa com...", "SSE.Views.AutoFilterDialog.txtBelowAve": "<PERSON><PERSON><PERSON><PERSON> da média", "SSE.Views.AutoFilterDialog.txtBetween": "Entre...", "SSE.Views.AutoFilterDialog.txtClear": "Limpar", "SSE.Views.AutoFilterDialog.txtContains": "Contém...", "SSE.Views.AutoFilterDialog.txtDateFilter": "Filtro de data", "SSE.Views.AutoFilterDialog.txtDecember": "December", "SSE.Views.AutoFilterDialog.txtEmpty": "Inserir filtro de célula", "SSE.Views.AutoFilterDialog.txtEnds": "<PERSON><PERSON><PERSON> em…", "SSE.Views.AutoFilterDialog.txtEquals": "É Igual…", "SSE.Views.AutoFilterDialog.txtFebruary": "February", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filtrar pela cor da célula", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filtrar pela cor do tipo de letra", "SSE.Views.AutoFilterDialog.txtGreater": "<PERSON><PERSON> do que…", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "<PERSON><PERSON> ou igual a…", "SSE.Views.AutoFilterDialog.txtJanuary": "January", "SSE.Views.AutoFilterDialog.txtJuly": "July", "SSE.Views.AutoFilterDialog.txtJune": "June", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Filtro de Etiqueta", "SSE.Views.AutoFilterDialog.txtLastMonth": "Last month", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Last quarter", "SSE.Views.AutoFilterDialog.txtLastWeek": "Last week", "SSE.Views.AutoFilterDialog.txtLastYear": "Last year", "SSE.Views.AutoFilterDialog.txtLess": "<PERSON>or que…", "SSE.Views.AutoFilterDialog.txtLessEquals": "<PERSON>or que ou igual a…", "SSE.Views.AutoFilterDialog.txtMarch": "March", "SSE.Views.AutoFilterDialog.txtMay": "May", "SSE.Views.AutoFilterDialog.txtNextMonth": "Next month", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Next quarter", "SSE.Views.AutoFilterDialog.txtNextWeek": "Next week", "SSE.Views.AutoFilterDialog.txtNextYear": "Next year", "SSE.Views.AutoFilterDialog.txtNotBegins": "Não começa com…", "SSE.Views.AutoFilterDialog.txtNotBetween": "Não está entre...", "SSE.Views.AutoFilterDialog.txtNotContains": "<PERSON><PERSON> contem…", "SSE.Views.AutoFilterDialog.txtNotEnds": "Não acaba com…", "SSE.Views.AutoFilterDialog.txtNotEquals": "Não é igual a…", "SSE.Views.AutoFilterDialog.txtNovember": "November", "SSE.Views.AutoFilterDialog.txtNumFilter": "Numerar filtro", "SSE.Views.AutoFilterDialog.txtOctober": "October", "SSE.Views.AutoFilterDialog.txtQuarter1": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Quarter 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Quarter 1", "SSE.Views.AutoFilterDialog.txtReapply": "Aplicar", "SSE.Views.AutoFilterDialog.txtSeptember": "September", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Ordenar pela cor da célula", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Ordenar pela cor do tipo de letra", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Ordenar do Maior para o Menor", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Ordenar do Menor para o Maior", "SSE.Views.AutoFilterDialog.txtSortOption": "Mais opções de ordenação…", "SSE.Views.AutoFilterDialog.txtTextFilter": "Filtro de texto", "SSE.Views.AutoFilterDialog.txtThisMonth": "This Month", "SSE.Views.AutoFilterDialog.txtThisQuarter": "This quarter", "SSE.Views.AutoFilterDialog.txtThisWeek": "This week", "SSE.Views.AutoFilterDialog.txtThisYear": "This Year", "SSE.Views.AutoFilterDialog.txtTitle": "Filtro", "SSE.Views.AutoFilterDialog.txtToday": "Today", "SSE.Views.AutoFilterDialog.txtTomorrow": "Tomorrow", "SSE.Views.AutoFilterDialog.txtTop10": "Top 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Filtro de Valor", "SSE.Views.AutoFilterDialog.txtYearToDate": "No acumulado do ano", "SSE.Views.AutoFilterDialog.txtYesterday": "Yesterday", "SSE.Views.AutoFilterDialog.warnFilterError": "É necessário pelo menos um campo na área de Valores para aplicar um filtro de valores.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Você deve escolher no mínimo um valor", "SSE.Views.CellEditor.textManager": "Manager", "SSE.Views.CellEditor.tipFormula": "Inserir função", "SSE.Views.CellRangeDialog.errorMaxRows": "ERRO! O número máximo de séries de dados, por gráfico, é 255.", "SSE.Views.CellRangeDialog.errorStockChart": "Ordem de linha inválida. Para criar um gráfico de cotações, coloque os dados na folha pela seguinte ordem:<br>preço de abertura, preço máximo, preço mínimo, preço de fecho.", "SSE.Views.CellRangeDialog.txtEmpty": "Este campo é obrigatório", "SSE.Views.CellRangeDialog.txtInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Views.CellRangeDialog.txtTitle": "Selecionar intervalo de dados", "SSE.Views.CellSettings.strShrink": "<PERSON><PERSON><PERSON><PERSON> para ajustar", "SSE.Views.CellSettings.strWrap": "<PERSON><PERSON><PERSON> texto", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "Cor de fundo", "SSE.Views.CellSettings.textBackground": "Cor do plano de fundo", "SSE.Views.CellSettings.textBorderColor": "Cor", "SSE.Views.CellSettings.textBorders": "Estilo do contorno", "SSE.Views.CellSettings.textClearRule": "<PERSON><PERSON> regras", "SSE.Views.CellSettings.textColor": "<PERSON><PERSON> <PERSON> pre<PERSON>", "SSE.Views.CellSettings.textColorScales": "Escalas de cores", "SSE.Views.CellSettings.textCondFormat": "Formatação condicional", "SSE.Views.CellSettings.textControl": "Controlo de texto", "SSE.Views.CellSettings.textDataBars": "Barras de dados", "SSE.Views.CellSettings.textDirection": "Direção", "SSE.Views.CellSettings.textFill": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textForeground": "Cor principal", "SSE.Views.CellSettings.textGradient": "Gradiente", "SSE.Views.CellSettings.textGradientColor": "Cor", "SSE.Views.CellSettings.textGradientFill": "Preenchimento gradiente", "SSE.Views.CellSettings.textIndent": "Indentar", "SSE.Views.CellSettings.textItems": "<PERSON><PERSON>", "SSE.Views.CellSettings.textLinear": "Linear", "SSE.Views.CellSettings.textManageRule": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textNewRule": "Nova Regra", "SSE.Views.CellSettings.textNoFill": "Sem preenchimento", "SSE.Views.CellSettings.textOrientation": "Orientação do texto", "SSE.Views.CellSettings.textPattern": "Padrão", "SSE.Views.CellSettings.textPatternFill": "Padrão", "SSE.Views.CellSettings.textPosition": "Posição", "SSE.Views.CellSettings.textRadial": "Radial", "SSE.Views.CellSettings.textSelectBorders": "Selecione os contornos aos quais pretende aplicar o estilo escolhido", "SSE.Views.CellSettings.textSelection": "Da seleção atual", "SSE.Views.CellSettings.textThisPivot": "A partir deste pivot", "SSE.Views.CellSettings.textThisSheet": "A partir desta folha de cálculo", "SSE.Views.CellSettings.textThisTable": "A partir deste pivot", "SSE.Views.CellSettings.tipAddGradientPoint": "Adicionar ponto de gradiente", "SSE.Views.CellSettings.tipAll": "Definir contorno externo e todas as linhas internas", "SSE.Views.CellSettings.tipBottom": "Definir apenas contorno inferior externo", "SSE.Views.CellSettings.tipDiagD": "Definir Limite Diagonal Inferior", "SSE.Views.CellSettings.tipDiagU": "Definir Limite Diagonal Superior", "SSE.Views.CellSettings.tipInner": "Definir apenas linhas internas", "SSE.Views.CellSettings.tipInnerHor": "Definir apenas linhas internas horizontais", "SSE.Views.CellSettings.tipInnerVert": "Definir apenas linhas internas verticais", "SSE.Views.CellSettings.tipLeft": "Definir apenas contorno esquerdo externo", "SSE.Views.CellSettings.tipNone": "Definir sem bordas", "SSE.Views.CellSettings.tipOuter": "Definir apenas contorno externo", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Remover Ponto de Gradiente", "SSE.Views.CellSettings.tipRight": "Definir apenas contorno direito externo", "SSE.Views.CellSettings.tipTop": "Definir apenas contorno superior externo", "SSE.Views.ChartDataDialog.errorInFormula": "Há um erro na fórmula que introduziu.", "SSE.Views.ChartDataDialog.errorInvalidReference": "A referência não é válida. A referência tem de ser uma folha de cálculo aberta.", "SSE.Views.ChartDataDialog.errorMaxPoints": "O número máximo de pontos em série, por gráfico, é 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "O número máximo de séries de dados, por grá<PERSON>o, é 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Referência inválida. As referências para títulos, valores, tamanhos e etiquetas de dados tem que ser apenas uma célula, linha ou coluna.", "SSE.Views.ChartDataDialog.errorNoValues": "Para criar um gráfico, a série utilizada tem que ter, pelo menos, um valor.", "SSE.Views.ChartDataDialog.errorStockChart": "Ordem de linha inválida. Para criar um gráfico de cotações, coloque os dados na folha pela seguinte ordem:<br>preço de abertura, preço máximo, preço mínimo, preço de fecho.", "SSE.Views.ChartDataDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textCategory": "<PERSON><PERSON><PERSON><PERSON> (Categoria) Horizontais", "SSE.Views.ChartDataDialog.textData": "Intervalo da tabela de dados", "SSE.Views.ChartDataDialog.textDelete": "Remover", "SSE.Views.ChartDataDialog.textDown": "Para baixo", "SSE.Views.ChartDataDialog.textEdit": "<PERSON><PERSON>", "SSE.Views.ChartDataDialog.textInvalidRange": "Intervalo de células inválido", "SSE.Views.ChartDataDialog.textSelectData": "Selecionar dados", "SSE.Views.ChartDataDialog.textSeries": "<PERSON><PERSON><PERSON> da legenda (Série)", "SSE.Views.ChartDataDialog.textSwitch": "Trocar Linha/Coluna", "SSE.Views.ChartDataDialog.textTitle": "Tabela de dados", "SSE.Views.ChartDataDialog.textUp": "Para cima", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Há um erro na fórmula que introduziu.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "A referência não é válida. A referência tem de ser uma folha de cálculo aberta.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "O número máximo de pontos em série, por gráfico, é 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "O número máximo de séries de dados, por grá<PERSON>o, é 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Referência inválida. As referências para títulos, valores, tamanhos e etiquetas de dados tem que ser apenas uma célula, linha ou coluna.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Para criar uma tabela dinâmica, a série utilizada tem que ter, pelo menos, um valor.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Ordem de linha inválida. Para criar um gráfico de cotações, coloque os dados na folha pela seguinte ordem:<br>preço de abertura, preço máximo, preço mínimo, preço de fecho.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Intervalo de células inválido", "SSE.Views.ChartDataRangeDialog.textSelectData": "Selecionar dados", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Intervalo do rótulo do eixo", "SSE.Views.ChartDataRangeDialog.txtChoose": "Esco<PERSON>her <PERSON>o", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Nome da série", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Etiqueta dos eixos", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "<PERSON><PERSON>", "SSE.Views.ChartDataRangeDialog.txtValues": "Valores", "SSE.Views.ChartDataRangeDialog.txtXValues": "Valores do X", "SSE.Views.ChartDataRangeDialog.txtYValues": "Valores do Y", "SSE.Views.ChartSettings.errorMaxRows": "O número máximo de séries de dados, por grá<PERSON>o, é 255.", "SSE.Views.ChartSettings.strLineWeight": "<PERSON><PERSON><PERSON><PERSON> da linha", "SSE.Views.ChartSettings.strSparkColor": "Cor", "SSE.Views.ChartSettings.strTemplate": "<PERSON><PERSON>", "SSE.Views.ChartSettings.text3dDepth": "Profundidade (% da base)", "SSE.Views.ChartSettings.text3dHeight": "Altura (% da base)", "SSE.Views.ChartSettings.text3dRotation": "Rotação 3D", "SSE.Views.ChartSettings.textAdvanced": "Mostrar definições avançadas", "SSE.Views.ChartSettings.textAutoscale": "Ajuste automático", "SSE.Views.ChartSettings.textBorderSizeErr": "O valor inserido não está correto.<br>Introduza um valor entre 0 pt e 1584 pt.", "SSE.Views.ChartSettings.textChangeType": "Alterar tipo", "SSE.Views.ChartSettings.textChartType": "Alterar tipo de gráfico", "SSE.Views.ChartSettings.textDefault": "Rotação padrão", "SSE.Views.ChartSettings.textDown": "Para baixo", "SSE.Views.ChartSettings.textEditData": "<PERSON><PERSON> dad<PERSON>", "SSE.Views.ChartSettings.textFirstPoint": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textHeight": "Altura", "SSE.Views.ChartSettings.textHighPoint": "Ponto Alto", "SSE.Views.ChartSettings.textKeepRatio": "Proporções constantes", "SSE.Views.ChartSettings.textLastPoint": "Último ponto", "SSE.Views.ChartSettings.textLeft": "E<PERSON>rda", "SSE.Views.ChartSettings.textLowPoint": "Pont<PERSON> Bai<PERSON>o", "SSE.Views.ChartSettings.textMarkers": "Marcadores", "SSE.Views.ChartSettings.textNarrow": "Campo de visão estreito", "SSE.Views.ChartSettings.textNegativePoint": "Ponto Negativo", "SSE.Views.ChartSettings.textPerspective": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textRanges": "Intervalo de dados", "SSE.Views.ChartSettings.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textRightAngle": "Eixos de ângulo reto", "SSE.Views.ChartSettings.textSelectData": "Selecionar dados", "SSE.Views.ChartSettings.textShow": "Mostrar", "SSE.Views.ChartSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSwitch": "Trocar Linha/Coluna", "SSE.Views.ChartSettings.textType": "Tipo", "SSE.Views.ChartSettings.textUp": "Para cima", "SSE.Views.ChartSettings.textWiden": "Ampliar o campo de visão", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textX": "Rotação X", "SSE.Views.ChartSettings.textY": "Rotação Y", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "O número máximo de pontos em série, por gráfico, é 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "ERRO! O número máximo de séries de dados, por gráfico, é 255.", "SSE.Views.ChartSettingsDlg.errorStockChart": "Ordem de linha inválida. Para criar um gráfico de cotações, coloque os dados na folha pela seguinte ordem:<br>preço de abertura, preço máximo, preço mínimo, preço de fecho.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Não mover ou redimensionar com células", "SSE.Views.ChartSettingsDlg.textAlt": "Texto alternativo", "SSE.Views.ChartSettingsDlg.textAltDescription": "Descrição", "SSE.Views.ChartSettingsDlg.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma, gráfico ou tabela existe na imagem.", "SSE.Views.ChartSettingsDlg.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textAuto": "Automático", "SSE.Views.ChartSettingsDlg.textAutoEach": "Automático para cada", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Eixos cruzam", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Opções de eixo", "SSE.Views.ChartSettingsDlg.textAxisPos": "Posição de eixos", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Definições de eixo", "SSE.Views.ChartSettingsDlg.textAxisTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textBase": "Base", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Entre marcas de escala", "SSE.Views.ChartSettingsDlg.textBillions": "Bilhões", "SSE.Views.ChartSettingsDlg.textBottom": "Baixo", "SSE.Views.ChartSettingsDlg.textCategoryName": "Nome da categoria", "SSE.Views.ChartSettingsDlg.textCenter": "Centro", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Elementos do gráfico e<br>Legenda do gráfico", "SSE.Views.ChartSettingsDlg.textChartTitle": "Título do gráfico", "SSE.Views.ChartSettingsDlg.textCross": "<PERSON>", "SSE.Views.ChartSettingsDlg.textCustom": "Personalizar", "SSE.Views.ChartSettingsDlg.textDataColumns": "em colunas", "SSE.Views.ChartSettingsDlg.textDataLabels": "Ró<PERSON><PERSON> de dados", "SSE.Views.ChartSettingsDlg.textDataRows": "em linhas", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "<PERSON><PERSON><PERSON> legenda", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Células vazias e ocultas", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Ligar pontos de dados com linha", "SSE.Views.ChartSettingsDlg.textFit": "Ajustar à largura", "SSE.Views.ChartSettingsDlg.textFixed": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textFormat": "Formato da Etiqueta", "SSE.Views.ChartSettingsDlg.textGaps": "Espaços", "SSE.Views.ChartSettingsDlg.textGridLines": "Gridlines", "SSE.Views.ChartSettingsDlg.textGroup": "Agrupar Sparkline", "SSE.Views.ChartSettingsDlg.textHide": "Ocultar", "SSE.Views.ChartSettingsDlg.textHideAxis": "Ocultar eixo", "SSE.Views.ChartSettingsDlg.textHigh": "Alto", "SSE.Views.ChartSettingsDlg.textHorAxis": "Eixo horizontal", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorizontal": "Horizontal", "SSE.Views.ChartSettingsDlg.textHundredMil": "100.000.000 ", "SSE.Views.ChartSettingsDlg.textHundreds": "Centenas", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100.000 ", "SSE.Views.ChartSettingsDlg.textIn": "Em", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Parte inferior interna", "SSE.Views.ChartSettingsDlg.textInnerTop": "Superior interna", "SSE.Views.ChartSettingsDlg.textInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Views.ChartSettingsDlg.textLabelDist": "Distância da etiqueta de eixos", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Intervalo entre Etiquetas", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Opções de etiqueta", "SSE.Views.ChartSettingsDlg.textLabelPos": "Posição da etiqueta", "SSE.Views.ChartSettingsDlg.textLayout": "Disposição", "SSE.Views.ChartSettingsDlg.textLeft": "E<PERSON>rda", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Sobreposição esquerda", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Baixo", "SSE.Views.ChartSettingsDlg.textLegendLeft": "E<PERSON>rda", "SSE.Views.ChartSettingsDlg.textLegendPos": "<PERSON>a", "SSE.Views.ChartSettingsDlg.textLegendRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLegendTop": "Parte superior", "SSE.Views.ChartSettingsDlg.textLines": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLocationRange": "Intervalo de localização", "SSE.Views.ChartSettingsDlg.textLogScale": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLow": "Baixo", "SSE.Views.ChartSettingsDlg.textMajor": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorMinor": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMajorType": "Tipo principal", "SSE.Views.ChartSettingsDlg.textManual": "Manual", "SSE.Views.ChartSettingsDlg.textMarkers": "Marcadores", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Intervalo entre Marcas", "SSE.Views.ChartSettingsDlg.textMaxValue": "<PERSON>or máxi<PERSON>", "SSE.Views.ChartSettingsDlg.textMillions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinor": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMinorType": "<PERSON>ipo menor", "SSE.Views.ChartSettingsDlg.textMinValue": "<PERSON><PERSON> m<PERSON>", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Próximo ao eixo", "SSE.Views.ChartSettingsDlg.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Sem sobreposição", "SSE.Views.ChartSettingsDlg.textOneCell": "Mover mas não redimensionar células", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Nas marcas de escala", "SSE.Views.ChartSettingsDlg.textOut": "Fora", "SSE.Views.ChartSettingsDlg.textOuterTop": "Superior externa", "SSE.Views.ChartSettingsDlg.textOverlay": "Sobreposição", "SSE.Views.ChartSettingsDlg.textReverse": "Valores na ordem reversa", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Ordem Inversa", "SSE.Views.ChartSettingsDlg.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Sobreposição direita", "SSE.Views.ChartSettingsDlg.textRotated": "Girado", "SSE.Views.ChartSettingsDlg.textSameAll": "O mesmo para Todos", "SSE.Views.ChartSettingsDlg.textSelectData": "Selecionar dados", "SSE.Views.ChartSettingsDlg.textSeparator": "Separador de rótulos de dados", "SSE.Views.ChartSettingsDlg.textSeriesName": "Nome da série", "SSE.Views.ChartSettingsDlg.textShow": "Mostrar", "SSE.Views.ChartSettingsDlg.textShowBorders": "Exibir bordas do gráfico", "SSE.Views.ChartSettingsDlg.textShowData": "Mostrar dados nas linhas e colunas ocultas", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Mostrar células vazias como", "SSE.Views.ChartSettingsDlg.textShowEquation": "Display equation on chart", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowValues": "Exibir valores do gráfico", "SSE.Views.ChartSettingsDlg.textSingle": "Sparkline Única", "SSE.Views.ChartSettingsDlg.textSmooth": "Suave", "SSE.Views.ChartSettingsDlg.textSnap": "Alinhamento de <PERSON>lulas", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Intervalos de Sparkline", "SSE.Views.ChartSettingsDlg.textStraight": "Reto", "SSE.Views.ChartSettingsDlg.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTenMillions": "10.000.000 ", "SSE.Views.ChartSettingsDlg.textTenThousands": "10.000 ", "SSE.Views.ChartSettingsDlg.textThousands": "Milhares", "SSE.Views.ChartSettingsDlg.textTickOptions": "Opções de escala", "SSE.Views.ChartSettingsDlg.textTitle": "Gráfico - Definições avançadas", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Sparkline - Definições avançadas", "SSE.Views.ChartSettingsDlg.textTop": "Parte superior", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Trendline options", "SSE.Views.ChartSettingsDlg.textTrillions": "<PERSON>l<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textTwoCell": "Mover e redimensionar com células", "SSE.Views.ChartSettingsDlg.textType": "Tipo", "SSE.Views.ChartSettingsDlg.textTypeData": "Tipo e Dados", "SSE.Views.ChartSettingsDlg.textUnits": "Exibir unidades", "SSE.Views.ChartSettingsDlg.textValue": "Valor", "SSE.Views.ChartSettingsDlg.textVertAxis": "Eixo vertical", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Eixo Secundário Vertical", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Título do eixo X", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Título do eixo Y", "SSE.Views.ChartSettingsDlg.textZero": "Zero", "SSE.Views.ChartSettingsDlg.txtEmpty": "Este campo é obrigatório", "SSE.Views.ChartTypeDialog.errorComboSeries": "Para criar um gráfico de combinação, selecione pelo menos duas séries de dados.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "O tipo de gráfico selecionado requer o eixo secundário que um gráfico existente está a utilizar. Selecione outro tipo de gráfico.", "SSE.Views.ChartTypeDialog.textSecondary": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textSeries": "Série", "SSE.Views.ChartTypeDialog.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartTypeDialog.textTitle": "Tipo de tabela de dados", "SSE.Views.ChartTypeDialog.textType": "Tipo", "SSE.Views.ChartWizardDialog.errorComboSeries": "To create a combination chart, select at least two series of data.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "The maximum number of points in series per chart is 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "The maximum number of data series per chart is 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "The selected chart type requires the secondary axis that an existing chart is using. Select another chart type.", "SSE.Views.ChartWizardDialog.errorStockChart": "Incorrect row order. To build a stock chart place the data on the sheet in the following order: opening price, max price, min price, closing price.", "SSE.Views.ChartWizardDialog.textRecommended": "Recommended", "SSE.Views.ChartWizardDialog.textSecondary": "Secondary Axis", "SSE.Views.ChartWizardDialog.textSeries": "Series", "SSE.Views.ChartWizardDialog.textTitle": "Insert Chart", "SSE.Views.ChartWizardDialog.textTitleChange": "Change chart type", "SSE.Views.ChartWizardDialog.textType": "Type", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Choose the chart type and axis for your data series", "SSE.Views.CreatePivotDialog.textDataRange": "Intervalo de dados de origem", "SSE.Views.CreatePivotDialog.textDestination": "Escolha o local para colocar a tabela", "SSE.Views.CreatePivotDialog.textExist": "Folha de cálculo existente", "SSE.Views.CreatePivotDialog.textInvalidRange": "Intervalo de células inválido", "SSE.Views.CreatePivotDialog.textNew": "Nova Folha de Cálculo", "SSE.Views.CreatePivotDialog.textSelectData": "Selecionar dados", "SSE.Views.CreatePivotDialog.textTitle": "<PERSON><PERSON><PERSON> tabela din<PERSON>a", "SSE.Views.CreatePivotDialog.txtEmpty": "Este campo é obrigatório", "SSE.Views.CreateSparklineDialog.textDataRange": "Intervalo de dados de origem", "SSE.Views.CreateSparklineDialog.textDestination": "<PERSON><PERSON><PERSON><PERSON>, onde colocar as sparklines", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Intervalo de células inválido", "SSE.Views.CreateSparklineDialog.textSelectData": "Selecionar dados", "SSE.Views.CreateSparklineDialog.textTitle": "Criar <PERSON>", "SSE.Views.CreateSparklineDialog.txtEmpty": "Este campo é obrigatório", "SSE.Views.DataTab.capBtnGroup": "Grupo", "SSE.Views.DataTab.capBtnTextCustomSort": "Ordenação personalizada", "SSE.Views.DataTab.capBtnTextDataValidation": "Validação dos dados", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Remover duplicados", "SSE.Views.DataTab.capBtnTextToCol": "Texto para colunas", "SSE.Views.DataTab.capBtnUngroup": "Desagrupar", "SSE.Views.DataTab.capDataExternalLinks": "Ligações externas", "SSE.Views.DataTab.capDataFromText": "Obter dados", "SSE.Views.DataTab.capGoalSeek": "Goal Seek", "SSE.Views.DataTab.mniFromFile": "De um TXT/CSV local", "SSE.Views.DataTab.mniFromUrl": "Do endereço da web TXT/CSV", "SSE.Views.DataTab.mniFromXMLFile": "From Local XML", "SSE.Views.DataTab.textBelow": "Linhas de resumo por baixo do detalhe", "SSE.Views.DataTab.textClear": "Limpar Contorno do Gráfico", "SSE.Views.DataTab.textColumns": "Desagrupar Colunas", "SSE.Views.DataTab.textGroupColumns": "Agrupar colunas", "SSE.Views.DataTab.textGroupRows": "Agrupar linhas", "SSE.Views.DataTab.textRightOf": "Colunas de resumo à direita do detalhe", "SSE.Views.DataTab.textRows": "Desagrupar linhas", "SSE.Views.DataTab.tipCustomSort": "Ordenação personalizada", "SSE.Views.DataTab.tipDataFromText": "Obter dados a partir de um ficheiro", "SSE.Views.DataTab.tipDataValidation": "Validação dos dados", "SSE.Views.DataTab.tipExternalLinks": "Ver ficheiros aos quais folha de cálculo está vinculada", "SSE.Views.DataTab.tipGoalSeek": "Find the right input for the value you want", "SSE.Views.DataTab.tipGroup": "Agrupar intervalo de células", "SSE.Views.DataTab.tipRemDuplicates": "Remover linhas duplicadas na folha", "SSE.Views.DataTab.tipToColumns": "Separar o texto da célula em colunas", "SSE.Views.DataTab.tipUngroup": "Desagrupar intervalo de células", "SSE.Views.DataValidationDialog.errorFormula": "O valor atual corresponde a um erro. Quer continuar?", "SSE.Views.DataValidationDialog.errorInvalid": "O valor que introduziu para o campo \"{0}\" é inválido.", "SSE.Views.DataValidationDialog.errorInvalidDate": "A data que introduziu para o campo \"{0}\" é inválido.", "SSE.Views.DataValidationDialog.errorInvalidList": "A origem da lista deve ser uma lista delimitada, ou uma referência a uma única linha ou coluna.", "SSE.Views.DataValidationDialog.errorInvalidTime": "O tempo que introduziu para o campo \"{0}\" é inválido.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "O campo \"{1}\" deve ser maior ou igual ao campo \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Deve introduzir um valor tanto no campo \"{0}\" como no campo \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Tem de introduzir um valor no campo \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "O intervalo com nome que especificou não foi encontrado.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Os valores negativos não podem ser utilizados em condições \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "O campo \"{0}\" deve ser um valor numérico, expressão numérica, ou referir-se a uma célula contendo um valor numérico.", "SSE.Views.DataValidationDialog.strError": "<PERSON><PERSON><PERSON>rro", "SSE.Views.DataValidationDialog.strInput": "Mensagem de Entrada", "SSE.Views.DataValidationDialog.strSettings": "Definições", "SSE.Views.DataValidationDialog.textAlert": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAllow": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textApply": "Aplicar estas alterações a todas células com as mesmas definições", "SSE.Views.DataValidationDialog.textCellSelected": "Quando a célula é selecionada, mostrar esta mensagem de entrada", "SSE.Views.DataValidationDialog.textCompare": "Comparar com", "SSE.Views.DataValidationDialog.textData": "Data", "SSE.Views.DataValidationDialog.textEndDate": "Data de conclusão", "SSE.Views.DataValidationDialog.textEndTime": "Hora de fim", "SSE.Views.DataValidationDialog.textError": "Mensagem de Erro", "SSE.Views.DataValidationDialog.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textIgnore": "Ignorar Em Branco", "SSE.Views.DataValidationDialog.textInput": "Mensagem de Entrada", "SSE.Views.DataValidationDialog.textMax": "Máximo", "SSE.Views.DataValidationDialog.textMessage": "Mensagem", "SSE.Views.DataValidationDialog.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textSelectData": "Selecionar dados", "SSE.Views.DataValidationDialog.textShowDropDown": "Mostrar uma lista pendente na célula", "SSE.Views.DataValidationDialog.textShowError": "Mostrar alerta de erro após a introdução de dados inválidos", "SSE.Views.DataValidationDialog.textShowInput": "Mostrar mensagem de entrada quando a célula é selecionada", "SSE.Views.DataValidationDialog.textSource": "Origem", "SSE.Views.DataValidationDialog.textStartDate": "Data de início", "SSE.Views.DataValidationDialog.textStartTime": "Hora de Início", "SSE.Views.DataValidationDialog.textStop": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textUserEnters": "Quando o utilizador introduz dados inválidos, mostrar este alerta de erro", "SSE.Views.DataValidationDialog.txtAny": "Qualquer valor", "SSE.Views.DataValidationDialog.txtBetween": "entre", "SSE.Views.DataValidationDialog.txtDate": "Data", "SSE.Views.DataValidationDialog.txtDecimal": "Decimal", "SSE.Views.DataValidationDialog.txtElTime": "Tempo decorrido", "SSE.Views.DataValidationDialog.txtEndDate": "Data de conclusão", "SSE.Views.DataValidationDialog.txtEndTime": "Hora de fim", "SSE.Views.DataValidationDialog.txtEqual": "igual", "SSE.Views.DataValidationDialog.txtGreaterThan": "Superior a", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "Superior a ou igual a", "SSE.Views.DataValidationDialog.txtLength": "Comprimento", "SSE.Views.DataValidationDialog.txtLessThan": "Inferior a", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "Inferior a ou igual a", "SSE.Views.DataValidationDialog.txtList": "Lista", "SSE.Views.DataValidationDialog.txtNotBetween": "Não está entre", "SSE.Views.DataValidationDialog.txtNotEqual": "não é igual a", "SSE.Views.DataValidationDialog.txtOther": "Outro", "SSE.Views.DataValidationDialog.txtStartDate": "Data de início", "SSE.Views.DataValidationDialog.txtStartTime": "Hora de Início", "SSE.Views.DataValidationDialog.txtTextLength": "Comprimento do Texto", "SSE.Views.DataValidationDialog.txtTime": "Tempo", "SSE.Views.DataValidationDialog.txtWhole": "Número <PERSON>iro", "SSE.Views.DigitalFilterDialog.capAnd": "E", "SSE.Views.DigitalFilterDialog.capCondition1": "igual", "SSE.Views.DigitalFilterDialog.capCondition10": "não termina com", "SSE.Views.DigitalFilterDialog.capCondition11": "contém", "SSE.Views.DigitalFilterDialog.capCondition12": "não contém", "SSE.Views.DigitalFilterDialog.capCondition2": "não é igual a", "SSE.Views.DigitalFilterDialog.capCondition3": "é superior a", "SSE.Views.DigitalFilterDialog.capCondition30": "is after", "SSE.Views.DigitalFilterDialog.capCondition4": "é superior ou igual a", "SSE.Views.DigitalFilterDialog.capCondition40": "is after or equal to", "SSE.Views.DigitalFilterDialog.capCondition5": "é inferior a", "SSE.Views.DigitalFilterDialog.capCondition50": "is before", "SSE.Views.DigitalFilterDialog.capCondition6": "é inferior ou igual a", "SSE.Views.DigitalFilterDialog.capCondition60": "is before or equal to", "SSE.Views.DigitalFilterDialog.capCondition7": "começa com", "SSE.Views.DigitalFilterDialog.capCondition8": "não começa com", "SSE.Views.DigitalFilterDialog.capCondition9": "termina com", "SSE.Views.DigitalFilterDialog.capOr": "Ou", "SSE.Views.DigitalFilterDialog.textNoFilter": "sem filtro", "SSE.Views.DigitalFilterDialog.textShowRows": "<PERSON><PERSON><PERSON> onde", "SSE.Views.DigitalFilterDialog.textUse1": "Usar ? para apresentar qualquer caractere único", "SSE.Views.DigitalFilterDialog.textUse2": "Usar * para apresentar qualquer série de caracteres", "SSE.Views.DigitalFilterDialog.txtSelectDate": "Selecionar data", "SSE.Views.DigitalFilterDialog.txtTitle": "Filtro personalizado", "SSE.Views.DocumentHolder.advancedEquationText": "Equation settings", "SSE.Views.DocumentHolder.advancedImgText": "Definições avançadas de imagem", "SSE.Views.DocumentHolder.advancedShapeText": "Definições avançadas de forma", "SSE.Views.DocumentHolder.advancedSlicerText": "Definições avançadas de 'slicer'", "SSE.Views.DocumentHolder.allLinearText": "All - Linear", "SSE.Views.DocumentHolder.allProfText": "All - Professional", "SSE.Views.DocumentHolder.bottomCellText": "<PERSON><PERSON><PERSON> em baixo", "SSE.Views.DocumentHolder.bulletsText": "Marcadores e numeração", "SSE.Views.DocumentHolder.centerCellText": "Alinhar ao centro", "SSE.Views.DocumentHolder.chartDataText": "Selecionar dados do gráfico", "SSE.Views.DocumentHolder.chartText": "Definições avançadas de gráfico", "SSE.Views.DocumentHolder.chartTypeText": "Alterar tipo de gráfico", "SSE.Views.DocumentHolder.currLinearText": "Current - Linear", "SSE.Views.DocumentHolder.currProfText": "Current - Professional", "SSE.Views.DocumentHolder.deleteColumnText": "Coluna", "SSE.Views.DocumentHolder.deleteRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.direct270Text": "Rodar texto para cima", "SSE.Views.DocumentHolder.direct90Text": "Rotate at 90°", "SSE.Views.DocumentHolder.directHText": "Horizontal", "SSE.Views.DocumentHolder.directionText": "Text Direction", "SSE.Views.DocumentHolder.editChartText": "<PERSON><PERSON> dad<PERSON>", "SSE.Views.DocumentHolder.editHyperlinkText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.hideEqToolbar": "Hide equation toolbar", "SSE.Views.DocumentHolder.insertColumnLeftText": "Coluna esquerda", "SSE.Views.DocumentHolder.insertColumnRightText": "Coluna direita", "SSE.Views.DocumentHolder.insertRowAboveText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.insertRowBelowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "Tamanho real", "SSE.Views.DocumentHolder.removeHyperlinkText": "Remover hiperligação", "SSE.Views.DocumentHolder.selectColumnText": "Coluna inteira", "SSE.Views.DocumentHolder.selectDataText": "<PERSON><PERSON> da coluna", "SSE.Views.DocumentHolder.selectRowText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.showEqToolbar": "Show Equation Toolbar", "SSE.Views.DocumentHolder.strDelete": "Remover assinatura", "SSE.Views.DocumentHolder.strDetails": "Detalhes da assinatura", "SSE.Views.DocumentHolder.strSetup": "Definições de Assinatura", "SSE.Views.DocumentHolder.strSign": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrange": "Dispor", "SSE.Views.DocumentHolder.textArrangeBack": "Enviar para plano de fundo", "SSE.Views.DocumentHolder.textArrangeBackward": "Enviar para trás", "SSE.Views.DocumentHolder.textArrangeForward": "Trazer para frente", "SSE.Views.DocumentHolder.textArrangeFront": "Trazer para primeiro plano", "SSE.Views.DocumentHolder.textAverage": "Média", "SSE.Views.DocumentHolder.textBullets": "Marcadores", "SSE.Views.DocumentHolder.textCopyCells": "Copy cells", "SSE.Views.DocumentHolder.textCount": "Contagem", "SSE.Views.DocumentHolder.textCrop": "Recortar", "SSE.Views.DocumentHolder.textCropFill": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFit": "Ajustar", "SSE.Views.DocumentHolder.textEditPoints": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textEntriesList": "Selecionar da lista suspensa", "SSE.Views.DocumentHolder.textFillDays": "Fill days", "SSE.Views.DocumentHolder.textFillFormatOnly": "Fill formatting only", "SSE.Views.DocumentHolder.textFillMonths": "Fill months", "SSE.Views.DocumentHolder.textFillSeries": "Fill series", "SSE.Views.DocumentHolder.textFillWeekdays": "Fill weekdays", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Fill without formatting", "SSE.Views.DocumentHolder.textFillYears": "Fill years", "SSE.Views.DocumentHolder.textFlashFill": "Flash fill", "SSE.Views.DocumentHolder.textFlipH": "Virar horizontalmente", "SSE.Views.DocumentHolder.textFlipV": "Virar verticalmente", "SSE.Views.DocumentHolder.textFreezePanes": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textFromFile": "De um ficheiro", "SSE.Views.DocumentHolder.textFromStorage": "De um armazenamento", "SSE.Views.DocumentHolder.textFromUrl": "De um URL", "SSE.Views.DocumentHolder.textGrowthTrend": "Growth trend", "SSE.Views.DocumentHolder.textLinearTrend": "Linear trend", "SSE.Views.DocumentHolder.textListSettings": "Definições da lista", "SSE.Views.DocumentHolder.textMacro": "Atribuir Macro", "SSE.Views.DocumentHolder.textMax": "Máx", "SSE.Views.DocumentHolder.textMin": "<PERSON>.", "SSE.Views.DocumentHolder.textMore": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textMoreFormats": "<PERSON><PERSON> formatos", "SSE.Views.DocumentHolder.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textNumbering": "Numeração", "SSE.Views.DocumentHolder.textReplace": "Substituir imagem", "SSE.Views.DocumentHolder.textResetCrop": "Reset crop", "SSE.Views.DocumentHolder.textRotate": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textRotate270": "Rodar 90º à esquerda", "SSE.Views.DocumentHolder.textRotate90": "Rodar 90º à direita", "SSE.Views.DocumentHolder.textSaveAsPicture": "Save as picture", "SSE.Views.DocumentHolder.textSeries": "Series", "SSE.Views.DocumentHolder.textShapeAlignBottom": "<PERSON><PERSON><PERSON> em baixo", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Alinhar ao centro", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Alinhar à esquerda", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Alinhar ao centro", "SSE.Views.DocumentHolder.textShapeAlignRight": "Alinhar à direita", "SSE.Views.DocumentHolder.textShapeAlignTop": "Alinhar em cima", "SSE.Views.DocumentHolder.textShapesMerge": "Merge shapes", "SSE.Views.DocumentHolder.textStdDev": "<PERSON>v.<PERSON>", "SSE.Views.DocumentHolder.textSum": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.textUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textUnFreezePanes": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "Marcas em <PERSON>a", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Marcas de verificação", "SSE.Views.DocumentHolder.tipMarkersDash": "Marcadores de traços", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Listas Rômbicas Preenchidas", "SSE.Views.DocumentHolder.tipMarkersFRound": "Listas Redondas Preenchidas", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Listas Quadradas Preenchidas", "SSE.Views.DocumentHolder.tipMarkersHRound": "Marcas de lista redondas vazias", "SSE.Views.DocumentHolder.tipMarkersStar": "Marcas em estrela", "SSE.Views.DocumentHolder.topCellText": "Alinhar em cima", "SSE.Views.DocumentHolder.txtAccounting": "Contabilidade", "SSE.Views.DocumentHolder.txtAddComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAddNamedRange": "Define Name", "SSE.Views.DocumentHolder.txtArrange": "Dispor", "SSE.Views.DocumentHolder.txtAscending": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Ajuste automático à largura da coluna", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Ajuste automática à altura da linha", "SSE.Views.DocumentHolder.txtAverage": "Média", "SSE.Views.DocumentHolder.txtCellFormat": "Format cells", "SSE.Views.DocumentHolder.txtClear": "Limpar", "SSE.Views.DocumentHolder.txtClearAll": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearComments": "Comentários", "SSE.Views.DocumentHolder.txtClearFormat": "Formato", "SSE.Views.DocumentHolder.txtClearHyper": "Hiperligações", "SSE.Views.DocumentHolder.txtClearPivotField": "Clear filter from {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Limpar os Grupos Sparkline Selecionados", "SSE.Views.DocumentHolder.txtClearSparklines": "Limpar Sparklines Selecionados", "SSE.Views.DocumentHolder.txtClearText": "Texto", "SSE.Views.DocumentHolder.txtCollapse": "Collapse", "SSE.Views.DocumentHolder.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.DocumentHolder.txtColumn": "Coluna inteira", "SSE.Views.DocumentHolder.txtColumnWidth": "<PERSON><PERSON><PERSON> da coluna", "SSE.Views.DocumentHolder.txtCondFormat": "Formatação condicional", "SSE.Views.DocumentHolder.txtCopy": "Copiar", "SSE.Views.DocumentHolder.txtCount": "Count", "SSE.Views.DocumentHolder.txtCurrency": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Largura de coluna personalizada", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Altura de linha personalizada", "SSE.Views.DocumentHolder.txtCustomSort": "Ordenação personalizada", "SSE.Views.DocumentHolder.txtCut": "Cortar", "SSE.Views.DocumentHolder.txtDateLong": "Data completa", "SSE.Views.DocumentHolder.txtDateShort": "Data curta", "SSE.Views.DocumentHolder.txtDelete": "Eliminar", "SSE.Views.DocumentHolder.txtDelField": "Remove", "SSE.Views.DocumentHolder.txtDescending": "Decrescente", "SSE.Views.DocumentHolder.txtDifference": "Difference from", "SSE.Views.DocumentHolder.txtDistribHor": "Distribuir horizontalmente", "SSE.Views.DocumentHolder.txtDistribVert": "Distribuir verticalmente", "SSE.Views.DocumentHolder.txtEditComment": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtEditObject": "Edit object", "SSE.Views.DocumentHolder.txtExpand": "Expand", "SSE.Views.DocumentHolder.txtExpandCollapse": "Expand/Collapse", "SSE.Views.DocumentHolder.txtExpandEntire": "Expand Entire Field", "SSE.Views.DocumentHolder.txtFieldSettings": "Field settings", "SSE.Views.DocumentHolder.txtFilter": "Filtro", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filtrar pela cor da célula", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filtrar pela cor do tipo de letra", "SSE.Views.DocumentHolder.txtFilterValue": "Filtrar por valor da célula selecionada", "SSE.Views.DocumentHolder.txtFormula": "Inserir função", "SSE.Views.DocumentHolder.txtFraction": "Fração", "SSE.Views.DocumentHolder.txtGeneral": "G<PERSON>", "SSE.Views.DocumentHolder.txtGetLink": "Obter ligação para este intervalo", "SSE.Views.DocumentHolder.txtGrandTotal": "Grand total", "SSE.Views.DocumentHolder.txtGroup": "Grupo", "SSE.Views.DocumentHolder.txtHide": "Ocultar", "SSE.Views.DocumentHolder.txtIndex": "Index", "SSE.Views.DocumentHolder.txtInsert": "Inserir", "SSE.Views.DocumentHolder.txtInsHyperlink": "Hiperligação", "SSE.Views.DocumentHolder.txtInsImage": "Inserir imagem de um ficheiro", "SSE.Views.DocumentHolder.txtInsImageUrl": "Insert image from URL", "SSE.Views.DocumentHolder.txtLabelFilter": "Label filters", "SSE.Views.DocumentHolder.txtMax": "Max", "SSE.Views.DocumentHolder.txtMin": "Min", "SSE.Views.DocumentHolder.txtMoreOptions": "More options", "SSE.Views.DocumentHolder.txtNormal": "No calculation", "SSE.Views.DocumentHolder.txtNumber": "Número", "SSE.Views.DocumentHolder.txtNumFormat": "Formato de número", "SSE.Views.DocumentHolder.txtPaste": "Colar", "SSE.Views.DocumentHolder.txtPercent": "% of", "SSE.Views.DocumentHolder.txtPercentage": "Percentagem", "SSE.Views.DocumentHolder.txtPercentDiff": "% difference from", "SSE.Views.DocumentHolder.txtPercentOfCol": "% of column total", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% of grand total", "SSE.Views.DocumentHolder.txtPercentOfParent": "% of parent total", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% of parent column total", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% of parent row total", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% running total in", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% of row total", "SSE.Views.DocumentHolder.txtPivotSettings": "Pivot Table settings", "SSE.Views.DocumentHolder.txtProduct": "Product", "SSE.Views.DocumentHolder.txtRankAscending": "Rank smallest to largest", "SSE.Views.DocumentHolder.txtRankDescending": "Rank largest to smallest", "SSE.Views.DocumentHolder.txtReapply": "Aplicar", "SSE.Views.DocumentHolder.txtRefresh": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtRow": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtRowHeight": "<PERSON>ura da linha", "SSE.Views.DocumentHolder.txtRunTotal": "Running total in", "SSE.Views.DocumentHolder.txtScientific": "Científico", "SSE.Views.DocumentHolder.txtSelect": "Selecionar", "SSE.Views.DocumentHolder.txtShiftDown": "Deslocar células para baixo", "SSE.Views.DocumentHolder.txtShiftLeft": "Deslocar células para a esquerda", "SSE.Views.DocumentHolder.txtShiftRight": "Deslocar células para a direita", "SSE.Views.DocumentHolder.txtShiftUp": "Deslocar células para cima", "SSE.Views.DocumentHolder.txtShow": "Mostrar", "SSE.Views.DocumentHolder.txtShowAs": "Show values as", "SSE.Views.DocumentHolder.txtShowComment": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShowDetails": "Show details", "SSE.Views.DocumentHolder.txtSort": "Classificar", "SSE.Views.DocumentHolder.txtSortCellColor": "Cor da célula selecionada em cima", "SSE.Views.DocumentHolder.txtSortFontColor": "Cor do tipo de letra selecionada em cima", "SSE.Views.DocumentHolder.txtSortOption": "More sort options", "SSE.Views.DocumentHolder.txtSparklines": "Sparklines", "SSE.Views.DocumentHolder.txtSubtotalField": "Subtotal", "SSE.Views.DocumentHolder.txtSum": "Sum", "SSE.Views.DocumentHolder.txtSummarize": "Summarize values by", "SSE.Views.DocumentHolder.txtText": "Тexto", "SSE.Views.DocumentHolder.txtTextAdvanced": "Configurações avançadas de parágrafo", "SSE.Views.DocumentHolder.txtTime": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtTop10": "Top 10", "SSE.Views.DocumentHolder.txtUngroup": "Desagrupar", "SSE.Views.DocumentHolder.txtValueFieldSettings": "Value field settings", "SSE.Views.DocumentHolder.txtValueFilter": "Value filters", "SSE.Views.DocumentHolder.txtWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "Alinhamento vertical", "SSE.Views.ExternalLinksDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Automatically update data from the linked sources", "SSE.Views.ExternalLinksDlg.textChange": "Change source", "SSE.Views.ExternalLinksDlg.textDelete": "Quebrar ligações", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Quebra todas as ligações", "SSE.Views.ExternalLinksDlg.textOk": "Aceitar", "SSE.Views.ExternalLinksDlg.textOpen": "<PERSON><PERSON><PERSON> a<PERSON>", "SSE.Views.ExternalLinksDlg.textSource": "Fonte", "SSE.Views.ExternalLinksDlg.textStatus": "Estado", "SSE.Views.ExternalLinksDlg.textUnknown": "Unknown", "SSE.Views.ExternalLinksDlg.textUpdate": "Atualizar valores", "SSE.Views.ExternalLinksDlg.textUpdateAll": "<PERSON><PERSON><PERSON><PERSON> tudo", "SSE.Views.ExternalLinksDlg.textUpdating": "Updating...", "SSE.Views.ExternalLinksDlg.txtTitle": "Ligações externas", "SSE.Views.FieldSettingsDialog.strLayout": "Disposição", "SSE.Views.FieldSettingsDialog.strSubtotals": "Subtotais", "SSE.Views.FieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.FieldSettingsDialog.textReport": "Reportar Forma", "SSE.Views.FieldSettingsDialog.textTitle": "Definições de campo", "SSE.Views.FieldSettingsDialog.txtAverage": "Média", "SSE.Views.FieldSettingsDialog.txtBlank": "Inserir <PERSON> em Branco depois de cada item", "SSE.Views.FieldSettingsDialog.txtBottom": "Mostrar no fundo do grupo", "SSE.Views.FieldSettingsDialog.txtCompact": "Compactar", "SSE.Views.FieldSettingsDialog.txtCount": "Contagem", "SSE.Views.FieldSettingsDialog.txtCountNums": "Contar nú<PERSON>", "SSE.Views.FieldSettingsDialog.txtCustomName": "Nome personalizado", "SSE.Views.FieldSettingsDialog.txtEmpty": "Mostrar itens sem dados", "SSE.Views.FieldSettingsDialog.txtMax": "Máx", "SSE.Views.FieldSettingsDialog.txtMin": "<PERSON>.", "SSE.Views.FieldSettingsDialog.txtOutline": "Destaque", "SSE.Views.FieldSettingsDialog.txtProduct": "Produ<PERSON>", "SSE.Views.FieldSettingsDialog.txtRepeat": "Repetir as etiquetas dos itens em cada linha", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Mostra subtotais", "SSE.Views.FieldSettingsDialog.txtSourceName": "Nome da origem:", "SSE.Views.FieldSettingsDialog.txtStdDev": "<PERSON>v.<PERSON>", "SSE.Views.FieldSettingsDialog.txtStdDevp": "Desv.Padr<PERSON>", "SSE.Views.FieldSettingsDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtSummarize": "Funções para subtotais", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabular", "SSE.Views.FieldSettingsDialog.txtTop": "Mostrar no topo do grupo", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.ariaFileMenu": "File menu", "SSE.Views.FileMenu.btnBackCaption": "Abrir localização do ficheiro", "SSE.Views.FileMenu.btnCloseEditor": "Close File", "SSE.Views.FileMenu.btnCloseMenuCaption": "Fechar menu", "SSE.Views.FileMenu.btnCreateNewCaption": "Criar novo", "SSE.Views.FileMenu.btnDownloadCaption": "<PERSON><PERSON><PERSON><PERSON> como", "SSE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnExportToPDFCaption": "Export to PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "Abrir", "SSE.Views.FileMenu.btnHelpCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnHistoryCaption": "Hist<PERSON><PERSON><PERSON> <PERSON> ve<PERSON>ão", "SSE.Views.FileMenu.btnInfoCaption": "Informação da Folha de Cálculo", "SSE.Views.FileMenu.btnPrintCaption": "Imprimir", "SSE.Views.FileMenu.btnProtectCaption": "Proteger", "SSE.Views.FileMenu.btnRecentFilesCaption": "Abrir recente", "SSE.Views.FileMenu.btnRenameCaption": "<PERSON>dar nome", "SSE.Views.FileMenu.btnReturnCaption": "Voltar para Folha de Cálculo", "SSE.Views.FileMenu.btnRightsCaption": "Direitos de Acesso", "SSE.Views.FileMenu.btnSaveAsCaption": "Guardar como", "SSE.Views.FileMenu.btnSaveCaption": "Guardar", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Guardar cópia como", "SSE.Views.FileMenu.btnSettingsCaption": "Definições avançadas", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "SSE.Views.FileMenu.btnToEditCaption": "<PERSON><PERSON> Cálculo", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Folha de Cálculo em branco", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Criar novo", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Aplicar", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Adicionar autor", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Add property", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Adicionar texto", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Aplicação", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Autor", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Alterar direitos de acesso", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Document Property", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Última modificação por", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Última modificação", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Localização", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Properties", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Property with this title already exists", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Pessoas que têm direitos", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Spreadsheet info", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Etiquetas", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Carregado", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Access Rights", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Alterar direitos de acesso", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Pessoas que têm direitos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Aplicar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Co-editing mode", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Usar o sistema de datas de 1904", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Separador decimal", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Idioma do Dicionário", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Enable iterative calculation", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Dicas de tipo de letra", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Formula Language", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Example: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Show function tooltip", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Show horizontal scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Ignorar palavras em MAÍSCULAS", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Ignorar palavras com números", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Definições de macros", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Maximum change", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Maximum iterations", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Mostrar botão Opções de colagem ao colar conteúdo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Estilo de referência R1C1", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Definições regionais", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Example:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTL interface", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Mostrar comentários na folha", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Mostrar alterações de outros utilizadores", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "<PERSON>rar coment<PERSON>rios resolvidos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Snapped to the grid while scrolling", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Strict", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Tab style", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Separador de milhares", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Unidade de medida", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Utilizar separadores de acordo com as definições regionais", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Show vertical scroll bar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Ampliação padrão", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "A cada 10 minutos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "A cada 30 minutos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "A cada 5 minutos", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "A cada hora", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Recuperação automática", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Guardar automático", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Desabilitado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "Fill", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Guardar versões intermédias", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "Line", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "A cada minuto", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Estilo de Referência", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "Advanced settings", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "Appearance", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Opções de correção automática...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Bielorrusso", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Búlgaro", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Catalão", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Modo de cache padrão", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Calculando", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Centímetro", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Colaboração", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Checo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Customize quick access", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "De<PERSON>ch", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Editar e Guardar", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "English", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Espanhol", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Coedição em tempo real. Todas as alterações são guardadas automaticamente.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "Armenian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonésio", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "Polegada", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Italiano", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Last used", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Lao", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "como SO X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Nativo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Neerlandês", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Polaco", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Correção", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Ponto", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Brasil)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "<PERSON>ug<PERSON><PERSON><PERSON> (Portugal)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Show the Quick Print button in the editor header", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Região", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Romeno", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Russian", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Ativar tudo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Ativar todas as macros sem notificação", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Turn on screen reader support", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Default sheet direction", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "This setting will affect only the new sheets", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Left-to-right", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Right-to-left", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Eslovaco", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Esloveno", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Desativar tudo", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "<PERSON><PERSON><PERSON> to<PERSON> as macros sem uma notificação", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Utilize o bot<PERSON> \"Guardar\" para sincronizar as alterações efetuadas por todos os utilizadores", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Sueco", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Use toolbar color as tabs background", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Ucraniano", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Utilize a tecla 'Alt' para navegar na interface através do teclado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Utilize a tecla 'Opção' para navegar na interface através do teclado", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Vietnamita", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Mostrar notificação", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "<PERSON><PERSON><PERSON> todas as macros com uma notificação", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "como Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Aviso", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Com palavra-passe", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Proteger fol<PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Com assinatura", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Valid signatures have been added to the spreadsheet.<br>The spreadsheet is protected from editing.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Ensure the integrity of the spreadsheet by adding an<br>invisible digital signature", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "<PERSON><PERSON> Cálculo", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "A edição irá remover as assinaturas da folha de cálculo.<br>Continuar?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "A folha de cálculo está protegida com uma palavra-passe", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Encrypt this spreadsheet with a password", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Esta folha de cálculo precisa de ser assinada.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Assinaturas adicionadas ao documento. Esta folha de cálculo não pode ser editada.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Algumas das assinaturas digitais são inválidas ou não puderam ser verificadas. Esta folha de cálculo documento não pode ser editada.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Ver assinaturas", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Download as", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Save copy as", "SSE.Views.FillSeriesDialog.textAuto": "AutoFill", "SSE.Views.FillSeriesDialog.textCols": "Columns", "SSE.Views.FillSeriesDialog.textDate": "Date", "SSE.Views.FillSeriesDialog.textDateUnit": "Date unit", "SSE.Views.FillSeriesDialog.textDay": "Day", "SSE.Views.FillSeriesDialog.textGrowth": "Growth", "SSE.Views.FillSeriesDialog.textLinear": "Linear", "SSE.Views.FillSeriesDialog.textMonth": "Month", "SSE.Views.FillSeriesDialog.textRows": "Rows", "SSE.Views.FillSeriesDialog.textSeries": "Series in", "SSE.Views.FillSeriesDialog.textStep": "Step value", "SSE.Views.FillSeriesDialog.textStop": "Stop value", "SSE.Views.FillSeriesDialog.textTitle": "Series", "SSE.Views.FillSeriesDialog.textTrend": "Trend", "SSE.Views.FillSeriesDialog.textType": "Type", "SSE.Views.FillSeriesDialog.textWeek": "Weekday", "SSE.Views.FillSeriesDialog.textYear": "Year", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.FormatRulesEditDlg.fillColor": "<PERSON><PERSON> <PERSON> pre<PERSON>", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Aviso", "SSE.Views.FormatRulesEditDlg.text2Scales": "2 Escala de cor", "SSE.Views.FormatRulesEditDlg.text3Scales": "3 Escala de cor", "SSE.Views.FormatRulesEditDlg.textAllBorders": "<PERSON><PERSON> as bordas", "SSE.Views.FormatRulesEditDlg.textAppearance": "Aparência da Barra", "SSE.Views.FormatRulesEditDlg.textApply": "Aplicar a Intervalo", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automático", "SSE.Views.FormatRulesEditDlg.textAxis": "Eixo", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Direção da Barra", "SSE.Views.FormatRulesEditDlg.textBold": "Negrito", "SSE.Views.FormatRulesEditDlg.textBorder": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBordersColor": "<PERSON><PERSON> bordas", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Contornos inferiores", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Não foi possível adicionar a formatação condicional.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Ponto intermédio da célula", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Bordas verticais interiores", "SSE.Views.FormatRulesEditDlg.textClear": "Limpar", "SSE.Views.FormatRulesEditDlg.textColor": "Cor do texto", "SSE.Views.FormatRulesEditDlg.textContext": "Contexto", "SSE.Views.FormatRulesEditDlg.textCustom": "Personalizado", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Borda inferior diagonal", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Borda superior diagonal", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Introduza uma fórmula válida", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "A fórmula introduzida não devolve um número, uma data, hora ou cadeia.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Introduza um valor.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "O valor que introduziu não é um número, data, hora ou string válido.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "O valor para o {0} deve ser maior do que o valor para o {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Introduza um número entre {0} e {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textFormat": "Formato", "SSE.Views.FormatRulesEditDlg.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textGradient": "Gradiente", "SSE.Views.FormatRulesEditDlg.textIconLabel": "quando {0} {1} e", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "quando {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "quando o valor é", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Um ou mais intervalos de dados de ícone estão sobrepostos. <br> A<PERSON>ste os valores dos intervalos de dados de ícone de forma a que os intervalos não se sobreponham.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Estilo do Ícone", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Bordas interiores", "SSE.Views.FormatRulesEditDlg.textInvalid": "Intervalo de dados inválido.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Views.FormatRulesEditDlg.textItalic": "Itálico", "SSE.Views.FormatRulesEditDlg.textItem": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Da esquerda para a direita", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textLongBar": "<PERSON>a mais longa", "SSE.Views.FormatRulesEditDlg.textMaximum": "Máximo", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Ponto máxi<PERSON>", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Bordas horizontais interiores", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Ponto médio", "SSE.Views.FormatRulesEditDlg.textMinimum": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textMinpoint": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNegative": "Negativo", "SSE.Views.FormatRulesEditDlg.textNewColor": "Adicionar nova cor personalizada", "SSE.Views.FormatRulesEditDlg.textNoBorders": "<PERSON><PERSON> bordas", "SSE.Views.FormatRulesEditDlg.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Um ou mais dos valores especificados não é uma percentagem válida.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "O valor especificado {0} não é uma percentagem válida.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Um ou mais dos valores especificados não é um percentil válido.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "O valor especificado {0} não é um percentil válido.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "<PERSON><PERSON><PERSON> externas", "SSE.Views.FormatRulesEditDlg.textPercent": "Percentagem", "SSE.Views.FormatRulesEditDlg.textPercentile": "Percentil", "SSE.Views.FormatRulesEditDlg.textPosition": "Posição", "SSE.Views.FormatRulesEditDlg.textPositive": "Positivo", "SSE.Views.FormatRulesEditDlg.textPresets": "Predefinições", "SSE.Views.FormatRulesEditDlg.textPreview": "Pré-visualizar", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Não é possível utilizar referências relativas em critérios de formatação condicional para escalas de cor, barras de dados e conjuntos de ícones.", "SSE.Views.FormatRulesEditDlg.textReverse": "Ordem Inversa de Ícones", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Da Direita para a esquerda", "SSE.Views.FormatRulesEditDlg.textRightBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRule": "Regra", "SSE.Views.FormatRulesEditDlg.textSameAs": "O mesmo que positivo", "SSE.Views.FormatRulesEditDlg.textSelectData": "Selecionar dados", "SSE.Views.FormatRulesEditDlg.textShortBar": "<PERSON>a mais curta", "SSE.Views.FormatRulesEditDlg.textShowBar": "Mostrar apenas a barra", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Mostrar apenas o ícone", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Este tipo de referência não pode ser usado numa fórmula de formatação condicional.<br>Altere a referência para uma única célula, ou utilize a referência com uma função de folha de cálculo, tal como =SUM(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textStrikeout": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSubscript": "Subscrito", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Sobrescrito", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Bordas superiores", "SSE.Views.FormatRulesEditDlg.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Formato de número", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Contabilidade", "SSE.Views.FormatRulesEditDlg.txtCurrency": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDate": "Data", "SSE.Views.FormatRulesEditDlg.txtDateLong": "Data completa", "SSE.Views.FormatRulesEditDlg.txtDateShort": "Data Curta", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Este campo é obrigatório", "SSE.Views.FormatRulesEditDlg.txtFraction": "Fração", "SSE.Views.FormatRulesEditDlg.txtGeneral": "G<PERSON>", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtNumber": "Número", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Percentagem", "SSE.Views.FormatRulesEditDlg.txtScientific": "Científico", "SSE.Views.FormatRulesEditDlg.txtText": "Тexto", "SSE.Views.FormatRulesEditDlg.txtTime": "Tempo", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Editar Regra de Formatação", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Nova Regra de Formatação", "SSE.Views.FormatRulesManagerDlg.guestText": "Visitante", "SSE.Views.FormatRulesManagerDlg.lockText": "Bloqueada", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 acima da média do desv. padr. ", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 desv. padr. a<PERSON><PERSON><PERSON> da média ", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 acima da média do desv. padr. ", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 desv. padr. a<PERSON><PERSON><PERSON> da média", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 acima da média do desv. padr. ", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 desv. padr. a<PERSON><PERSON><PERSON> da média", "SSE.Views.FormatRulesManagerDlg.textAbove": "<PERSON><PERSON><PERSON> da média", "SSE.Views.FormatRulesManagerDlg.textApply": "Aplicar a", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "O valor da célula começa por", "SSE.Views.FormatRulesManagerDlg.textBelow": "<PERSON><PERSON><PERSON><PERSON> da média", "SSE.Views.FormatRulesManagerDlg.textBetween": "<PERSON><PERSON><PERSON> entre {0} e {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "<PERSON><PERSON>lula", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Escala de cor progressiva", "SSE.Views.FormatRulesManagerDlg.textContains": "O valor da célula contém", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "A célula contém um valor em branco", "SSE.Views.FormatRulesManagerDlg.textContainsError": "A célula contém um erro", "SSE.Views.FormatRulesManagerDlg.textDelete": "Eliminar", "SSE.Views.FormatRulesManagerDlg.textDown": "Mover regra para baixo", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Duplicar valores", "SSE.Views.FormatRulesManagerDlg.textEdit": "<PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textEnds": "O valor da célula acaba em", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Igual ou maior que a média", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Igual ou menor que a média", "SSE.Views.FormatRulesManagerDlg.textFormat": "Formato", "SSE.Views.FormatRulesManagerDlg.textIconSet": "Conjunto de ícones", "SSE.Views.FormatRulesManagerDlg.textNew": "Novo", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "Não está entre {0} e {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "O valor da célula não contém", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "A célula não contém um valor branco", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "A célula não tem nenhum erro", "SSE.Views.FormatRulesManagerDlg.textRules": "Regras", "SSE.Views.FormatRulesManagerDlg.textScope": "Mostrar regras de formatação para", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Selecionar dados", "SSE.Views.FormatRulesManagerDlg.textSelection": "Seleção Atual", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "<PERSON><PERSON> p<PERSON>", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Esta folha de cálculo", "SSE.Views.FormatRulesManagerDlg.textThisTable": "<PERSON><PERSON> tabela", "SSE.Views.FormatRulesManagerDlg.textUnique": "Valores únicos", "SSE.Views.FormatRulesManagerDlg.textUp": "Mover regra para cima", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Este elemento está sendo editado por outro usuário.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Formatação condicional", "SSE.Views.FormatSettingsDialog.textCategory": "Categoria", "SSE.Views.FormatSettingsDialog.textDecimal": "Decimal", "SSE.Views.FormatSettingsDialog.textFormat": "Formato", "SSE.Views.FormatSettingsDialog.textLinked": "Ligado à origem", "SSE.Views.FormatSettingsDialog.textSeparator": "Usar o separador 1000", "SSE.Views.FormatSettingsDialog.textSymbols": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textTitle": "Formato de número", "SSE.Views.FormatSettingsDialog.txtAccounting": "Contabilidade", "SSE.Views.FormatSettingsDialog.txtAs10": "<PERSON> décimas (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "Como centenas (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "Como 16 avos (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "<PERSON> (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "Como quartos (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Como oitavos (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustom": "Personalizado", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Introduza cuidadosamente o formato do número personalizado. O editor de folhas de cálculo não verifica os formatos personalizados para aferir possíveis erros que possam afetar o ficheiro xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "Data", "SSE.Views.FormatSettingsDialog.txtFraction": "Fração", "SSE.Views.FormatSettingsDialog.txtGeneral": "G<PERSON>", "SSE.Views.FormatSettingsDialog.txtNone": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtNumber": "Número", "SSE.Views.FormatSettingsDialog.txtPercentage": "Percentagem", "SSE.Views.FormatSettingsDialog.txtSample": "Amos<PERSON>:", "SSE.Views.FormatSettingsDialog.txtScientific": "Científico", "SSE.Views.FormatSettingsDialog.txtText": "Тexto", "SSE.Views.FormatSettingsDialog.txtTime": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtUpto1": "Até um dígito (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Até dois dígitos (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "<PERSON><PERSON> tr<PERSON><PERSON> (131/135)", "SSE.Views.FormulaDialog.sDescription": "Descrição", "SSE.Views.FormulaDialog.textGroupDescription": "Selecionar grupo de função", "SSE.Views.FormulaDialog.textListDescription": "Selecionar função", "SSE.Views.FormulaDialog.txtRecommended": "Recomendado", "SSE.Views.FormulaDialog.txtSearch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtTitle": "Inserir função", "SSE.Views.FormulaTab.capBtnRemoveArr": "Remove Arrows", "SSE.Views.FormulaTab.capBtnTraceDep": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.capBtnTracePrec": "Trace Precedents", "SSE.Views.FormulaTab.textAutomatic": "Automático", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Calcular folha completa", "SSE.Views.FormulaTab.textCalculateWorkbook": "Calcular livro", "SSE.Views.FormulaTab.textManual": "Manual", "SSE.Views.FormulaTab.tipCalculate": "Calcular", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Calcular livro", "SSE.Views.FormulaTab.tipRemoveArr": "<PERSON><PERSON><PERSON> as setas desenhadas por Ra<PERSON><PERSON>r Precedentes ou <PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.tipShowFormulas": "Display the formula in each cell instead of the resulting value", "SSE.Views.FormulaTab.tipTraceDep": "Mostrar setas que indicam quais células são afetadas pelo valor da célula selecionada", "SSE.Views.FormulaTab.tipTracePrec": "Mostrar setas que indicam quais células afetam o valor da célula selecionada", "SSE.Views.FormulaTab.tipWatch": "Adicionar c<PERSON>lulas à lista da Janela de observação", "SSE.Views.FormulaTab.txtAdditional": "Adicional", "SSE.Views.FormulaTab.txtAutosum": "Soma automática", "SSE.Views.FormulaTab.txtAutosumTip": "So<PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtCalculation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaTab.txtFormula": "Função", "SSE.Views.FormulaTab.txtFormulaTip": "Inserir função", "SSE.Views.FormulaTab.txtMore": "<PERSON><PERSON>", "SSE.Views.FormulaTab.txtRecent": "Utilizado recentemente", "SSE.Views.FormulaTab.txtRemDep": "Remover setas dependentes", "SSE.Views.FormulaTab.txtRemPrec": "Remove Precedents Arrows", "SSE.Views.FormulaTab.txtShowFormulas": "Show Formulas", "SSE.Views.FormulaTab.txtWatch": "Janela de observação", "SSE.Views.FormulaWizard.textAny": "qualquer", "SSE.Views.FormulaWizard.textArgument": "Argumento", "SSE.Views.FormulaWizard.textFunction": "Função", "SSE.Views.FormulaWizard.textFunctionRes": "Resultado da Função", "SSE.Views.FormulaWizard.textHelp": "<PERSON><PERSON><PERSON> nesta função", "SSE.Views.FormulaWizard.textLogical": "Logical", "SSE.Views.FormulaWizard.textNoArgs": "Esta função não tem argumentos", "SSE.Views.FormulaWizard.textNoArgsDesc": "this argument has no description", "SSE.Views.FormulaWizard.textNumber": "Número", "SSE.Views.FormulaWizard.textReadMore": "Read more", "SSE.Views.FormulaWizard.textRef": "Referência", "SSE.Views.FormulaWizard.textText": "Тexto", "SSE.Views.FormulaWizard.textTitle": "Argumentos da Função", "SSE.Views.FormulaWizard.textValue": "Resultado da fórmula", "SSE.Views.GoalSeekDlg.textChangingCell": "By changing cell", "SSE.Views.GoalSeekDlg.textDataRangeError": "The formula is missing a range", "SSE.Views.GoalSeekDlg.textMustContainFormula": "The cell must contain a formula", "SSE.Views.GoalSeekDlg.textMustContainValue": "Cell must contain a value", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Formula in cell must result in a number", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Reference must be to a single cell", "SSE.Views.GoalSeekDlg.textSelectData": "Selecionar dados", "SSE.Views.GoalSeekDlg.textSetCell": "Set cell", "SSE.Views.GoalSeekDlg.textTitle": "Goal seek", "SSE.Views.GoalSeekDlg.textToValue": "To value", "SSE.Views.GoalSeekDlg.txtEmpty": "This field is required", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Your entry cannot be used. An integer or decimal number may be required.", "SSE.Views.GoalSeekStatusDlg.textContinue": "Continue", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Current value:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Goal seeking with cell {0} found a solution.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Goal seeking with cell {0} may not have found a solution.", "SSE.Views.GoalSeekStatusDlg.textPause": "Pause", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Goal seeking with cell {0} on iteration #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "Step", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Target value:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Goal seek status", "SSE.Views.HeaderFooterDialog.textAlign": "Alinhas com as ma<PERSON><PERSON> <PERSON><PERSON><PERSON>a", "SSE.Views.HeaderFooterDialog.textAll": "<PERSON><PERSON> as p<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textBold": "Negrito", "SSE.Views.HeaderFooterDialog.textCenter": "Centro", "SSE.Views.HeaderFooterDialog.textColor": "Cor do texto", "SSE.Views.HeaderFooterDialog.textDate": "Data", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Primeira página diferente", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Páginas pares e ímpares diferentes", "SSE.Views.HeaderFooterDialog.textEven": "Página par", "SSE.Views.HeaderFooterDialog.textFileName": "Nome do ficheiro", "SSE.Views.HeaderFooterDialog.textFirst": "Primeira página", "SSE.Views.HeaderFooterDialog.textFooter": "Rodapé", "SSE.Views.HeaderFooterDialog.textHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textImage": "Picture", "SSE.Views.HeaderFooterDialog.textInsert": "Inserir", "SSE.Views.HeaderFooterDialog.textItalic": "Itálico", "SSE.Views.HeaderFooterDialog.textLeft": "E<PERSON>rda", "SSE.Views.HeaderFooterDialog.textMaxError": "A cadeia de texto que introduziu é demasiado longa. Reduza o número de caracteres utilizados.", "SSE.Views.HeaderFooterDialog.textNewColor": "Adicionar nova cor personalizada", "SSE.Views.HeaderFooterDialog.textOdd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textPageCount": "Número de páginas", "SSE.Views.HeaderFooterDialog.textPageNum": "Número de página", "SSE.Views.HeaderFooterDialog.textPresets": "Predefinições", "SSE.Views.HeaderFooterDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textScale": "Ajustar ao documento", "SSE.Views.HeaderFooterDialog.textSheet": "<PERSON>me da folha", "SSE.Views.HeaderFooterDialog.textStrikeout": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textSubscript": "Subscrito", "SSE.Views.HeaderFooterDialog.textSuperscript": "Sobrescrito", "SSE.Views.HeaderFooterDialog.textTime": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textTitle": "Definições de cabeçalho/rodapé", "SSE.Views.HeaderFooterDialog.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.tipFontName": "<PERSON><PERSON><PERSON> de letra", "SSE.Views.HeaderFooterDialog.tipFontSize": "Tamanho do tipo de letra", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Ligação a", "SSE.Views.HyperlinkSettingsDialog.strRange": "Intervalo", "SSE.Views.HyperlinkSettingsDialog.strSheet": "Fol<PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Copiar", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Intervalo selecionado", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Inserir legenda aqui", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Introduzir ligação aqui", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Inserir dica de ferramenta aqui", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Ligação externa", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Obter ligação", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Intervalo de dados interno", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Views.HyperlinkSettingsDialog.textNames": "<PERSON><PERSON> definidos", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Selecionar dados", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Select file", "SSE.Views.HyperlinkSettingsDialog.textSheets": "Fol<PERSON>", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Texto de dica de tela:", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Definições de hiperligação", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Este campo é obrigatório", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Este campo deve ser uma URL no formato \"http://www.example.com\"", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Este campo está limitado a 2083 caracteres", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Enter the web address or select a file", "SSE.Views.ImageSettings.strTransparency": "Opacity", "SSE.Views.ImageSettings.textAdvanced": "Mostrar definições avançadas", "SSE.Views.ImageSettings.textCrop": "Recortar", "SSE.Views.ImageSettings.textCropFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFit": "Ajustar", "SSE.Views.ImageSettings.textCropToShape": "Recortar com Forma", "SSE.Views.ImageSettings.textEdit": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textEditObject": "<PERSON>ar objeto", "SSE.Views.ImageSettings.textFlip": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textFromFile": "De um ficheiro", "SSE.Views.ImageSettings.textFromStorage": "De um armazenamento", "SSE.Views.ImageSettings.textFromUrl": "De um URL", "SSE.Views.ImageSettings.textHeight": "Altura", "SSE.Views.ImageSettings.textHint270": "Rodar 90º à esquerda", "SSE.Views.ImageSettings.textHint90": "Rodar 90º à direita", "SSE.Views.ImageSettings.textHintFlipH": "Virar horizontalmente", "SSE.Views.ImageSettings.textHintFlipV": "Virar verticalmente", "SSE.Views.ImageSettings.textInsert": "Substituir imagem", "SSE.Views.ImageSettings.textKeepRatio": "Proporções constantes", "SSE.Views.ImageSettings.textOriginalSize": "Tamanho real", "SSE.Views.ImageSettings.textRecentlyUsed": "Utilizado recentemente", "SSE.Views.ImageSettings.textResetCrop": "Reset crop", "SSE.Views.ImageSettings.textRotate90": "Rodar 90°", "SSE.Views.ImageSettings.textRotation": "Rotação", "SSE.Views.ImageSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Não mover ou redimensionar com células", "SSE.Views.ImageSettingsAdvanced.textAlt": "Texto alternativo", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Descrição", "SSE.Views.ImageSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma, gráfico ou tabela existe na imagem.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Invertido", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontalmente", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Mover mas não redimensionar células", "SSE.Views.ImageSettingsAdvanced.textRotation": "Rotação", "SSE.Views.ImageSettingsAdvanced.textSnap": "Alinhamento de <PERSON>lulas", "SSE.Views.ImageSettingsAdvanced.textTitle": "Imagem - Definições avançadas", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Mover e redimensionar com células", "SSE.Views.ImageSettingsAdvanced.textVertically": "Verticalmente", "SSE.Views.ImportFromXmlDialog.textDestination": "Choose, where to place the data", "SSE.Views.ImportFromXmlDialog.textExist": "Existing worksheet", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Invalid cells range", "SSE.Views.ImportFromXmlDialog.textNew": "New worksheet", "SSE.Views.ImportFromXmlDialog.textSelectData": "Selecionar dados", "SSE.Views.ImportFromXmlDialog.textTitle": "Import data", "SSE.Views.ImportFromXmlDialog.txtEmpty": "This field is required", "SSE.Views.LeftMenu.ariaLeftMenu": "Left menu", "SSE.Views.LeftMenu.tipAbout": "Sobre", "SSE.Views.LeftMenu.tipChat": "Gráfico", "SSE.Views.LeftMenu.tipComments": "Comentários", "SSE.Views.LeftMenu.tipFile": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "Plugins", "SSE.Views.LeftMenu.tipSearch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSpellcheck": "Verificação ortográfica", "SSE.Views.LeftMenu.tipSupport": "Feedback e Suporte", "SSE.Views.LeftMenu.txtDeveloper": "MODO DE DESENVOLVEDOR", "SSE.Views.LeftMenu.txtEditor": "Editor de Folha de Cálculo", "SSE.Views.LeftMenu.txtLimit": "Limitar o acesso", "SSE.Views.LeftMenu.txtTrial": "MODO DE TESTE", "SSE.Views.LeftMenu.txtTrialDev": "Versão de Avaliação do Modo de Programador", "SSE.Views.MacroDialog.textMacro": "<PERSON>me da Macro", "SSE.Views.MacroDialog.textTitle": "Atribuir Macro", "SSE.Views.MainSettingsPrint.okButtonText": "Guardar", "SSE.Views.MainSettingsPrint.strBottom": "Baixo", "SSE.Views.MainSettingsPrint.strLandscape": "Horizontal", "SSE.Views.MainSettingsPrint.strLeft": "E<PERSON>rda", "SSE.Views.MainSettingsPrint.strMargins": "Margens", "SSE.Views.MainSettingsPrint.strPortrait": "Vertical", "SSE.Views.MainSettingsPrint.strPrint": "Imprimir", "SSE.Views.MainSettingsPrint.strPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strTop": "Parte superior", "SSE.Views.MainSettingsPrint.textActualSize": "Tamanho real", "SSE.Views.MainSettingsPrint.textCustom": "Personalizado", "SSE.Views.MainSettingsPrint.textCustomOptions": "Opções de Personalização", "SSE.Views.MainSettingsPrint.textFitCols": "Ajustar colunas a uma página", "SSE.Views.MainSettingsPrint.textFitPage": "Ajustar folha a uma página", "SSE.Views.MainSettingsPrint.textFitRows": "Ajustar linhas a uma página", "SSE.Views.MainSettingsPrint.textPageOrientation": "Orientação da página", "SSE.Views.MainSettingsPrint.textPageScaling": "A ajustar", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPrintGrid": "Imprimir linhas de grade", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Imprimir títulos de linhas e de colunas", "SSE.Views.MainSettingsPrint.textRepeat": "Repetir…", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Repetir colunas à esquerda", "SSE.Views.MainSettingsPrint.textRepeatTop": "<PERSON><PERSON>r linhas no topo", "SSE.Views.MainSettingsPrint.textSettings": "Definições para", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Os intervalos nomeados existentes não podem ser criados e os novos também não<br> podem ser editados porque alguns deles estão a ser editados.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Defined name", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Aviso", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Workbook", "SSE.Views.NamedRangeEditDlg.textDataRange": "Intervalo de dados", "SSE.Views.NamedRangeEditDlg.textExistName": "ERROR! Range with such a name already exists", "SSE.Views.NamedRangeEditDlg.textInvalidName": "ERROR! Invalid range name", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ERROR! Invalid cell range", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ERRO! Este elemento está a ser editado por outro utilizador.", "SSE.Views.NamedRangeEditDlg.textName": "Nome", "SSE.Views.NamedRangeEditDlg.textReservedName": "The name you are trying to use is already referenced in cell formulas. Please use some other name.", "SSE.Views.NamedRangeEditDlg.textScope": "<PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textSelectData": "Selecionar dados", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Este campo é obrigatório", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Edit Name", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Novo nome", "SSE.Views.NamedRangePasteDlg.textNames": "Intervalos nomeados", "SSE.Views.NamedRangePasteDlg.txtTitle": "Paste Name", "SSE.Views.NameManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.guestText": "Visitante", "SSE.Views.NameManagerDlg.lockText": "Bloqueada", "SSE.Views.NameManagerDlg.textDataRange": "Intervalo de dados", "SSE.Views.NameManagerDlg.textDelete": "Eliminar", "SSE.Views.NameManagerDlg.textEdit": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEmpty": "Ainda não existem intervalos nomeados.<br>Te<PERSON> que criar, pelo menos, um intervalo nomeado para poder aparecer neste campo.", "SSE.Views.NameManagerDlg.textFilter": "Filtro", "SSE.Views.NameManagerDlg.textFilterAll": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textFilterDefNames": "Defined names", "SSE.Views.NameManagerDlg.textFilterSheet": "Names Scoped to Sheet", "SSE.Views.NameManagerDlg.textFilterTableNames": "Nomes de tabela", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Names Scoped to Workbook", "SSE.Views.NameManagerDlg.textNew": "Novo", "SSE.Views.NameManagerDlg.textnoNames": "Nenhum intervalo nomeado correspondente ao seu filtro foi encontrado.", "SSE.Views.NameManagerDlg.textRanges": "Intervalos nomeados", "SSE.Views.NameManagerDlg.textScope": "<PERSON><PERSON>", "SSE.Views.NameManagerDlg.textWorkbook": "Workbook", "SSE.Views.NameManagerDlg.tipIsLocked": "Este elemento está a ser editado por outro utilizador.", "SSE.Views.NameManagerDlg.txtTitle": "Name Manager", "SSE.Views.NameManagerDlg.warnDelete": "Tem a certeza que quer apagar o nome{0}?", "SSE.Views.PageMarginsDialog.textBottom": "Baixo", "SSE.Views.PageMarginsDialog.textCenter": "Center on page", "SSE.Views.PageMarginsDialog.textHor": "Horizontally", "SSE.Views.PageMarginsDialog.textLeft": "E<PERSON>rda", "SSE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTitle": "Margens", "SSE.Views.PageMarginsDialog.textTop": "Parte superior", "SSE.Views.PageMarginsDialog.textVert": "Vertically", "SSE.Views.PageMarginsDialog.textWarning": "Warning", "SSE.Views.PageMarginsDialog.warnCheckMargings": "Margins are incorrect", "SSE.Views.ParagraphSettings.strLineHeight": "Espaçamento de linha", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Espaçamento", "SSE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingBefore": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAdvanced": "Mostrar definições avançadas", "SSE.Views.ParagraphSettings.textAt": "Em", "SSE.Views.ParagraphSettings.textAtLeast": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettings.textExact": "Exatamente", "SSE.Views.ParagraphSettings.txtAutoText": "Automático", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Os separadores especificados aparecerão neste campo", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "<PERSON><PERSON> ma<PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "E<PERSON>rda", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Espaçamento entre linhas", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Especial", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "Por", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON><PERSON><PERSON> de letra", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Avanços e espaçamento", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Espaçamento", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Subscrito", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Sobrescrito", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Separadores", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Alinhamento", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Espaçamento entre caracteres", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Separador predefinido", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Efeitos", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Exatamente", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Primeira linha", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Suspensão", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "Justificado", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(nenhum)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Remover", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Remover todos", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Especificar", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Centro", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "E<PERSON>rda", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Posição do separador", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Parágrafo - Definições avançadas", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Automático", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Delete", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "Duplicate", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Edit", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Formula", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Items Name", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "New", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Calculated Items in", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "igual", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "não termina com", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "não contém", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "entre", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "Não está entre", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "não é igual a", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "é superior a", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "é superior ou igual a", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "é inferior a", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "é inferior ou igual a", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "começa com", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "não começa com", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "termina com", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Mostrar itens para os quais a etiqueta:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Mostrar itens para os quais:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Usar ? para apresentar qualquer caractere único", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Utilize * para mostrar qualquer série de caracteres", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "e", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Filtro de Etiqueta", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Filtro de Valor", "SSE.Views.PivotGroupDialog.textAuto": "Auto", "SSE.Views.PivotGroupDialog.textBy": "Por", "SSE.Views.PivotGroupDialog.textDays": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textEnd": "A terminar em", "SSE.Views.PivotGroupDialog.textError": "Este campo tem de ser um valor numérico", "SSE.Views.PivotGroupDialog.textGreaterError": "O número final tem de ser maior que o número inicial.", "SSE.Views.PivotGroupDialog.textHour": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textMonth": "Meses", "SSE.Views.PivotGroupDialog.textNumDays": "Número de <PERSON>as", "SSE.Views.PivotGroupDialog.textQuart": "Quartos", "SSE.Views.PivotGroupDialog.textSec": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotGroupDialog.textStart": "A partir das", "SSE.Views.PivotGroupDialog.textYear": "<PERSON><PERSON>", "SSE.Views.PivotGroupDialog.txtTitle": "A agrupar", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "You can use Calculated Items for basic calculations between different items within a single field", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Insert into formula", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "<PERSON><PERSON>", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Item name", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Items", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Read more", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Insert Calculated Item in", "SSE.Views.PivotSettings.textAdvanced": "Mostrar definições avançadas", "SSE.Views.PivotSettings.textColumns": "Colunas", "SSE.Views.PivotSettings.textFields": "Selecionar Campos", "SSE.Views.PivotSettings.textFilters": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "Valores", "SSE.Views.PivotSettings.txtAddColumn": "Adicionar a colunas", "SSE.Views.PivotSettings.txtAddFilter": "Adicionar a Filtros", "SSE.Views.PivotSettings.txtAddRow": "Adicionar a linhas", "SSE.Views.PivotSettings.txtAddValues": "Adicionar a valores", "SSE.Views.PivotSettings.txtFieldSettings": "Definições de campo", "SSE.Views.PivotSettings.txtMoveBegin": "Mover para o Início", "SSE.Views.PivotSettings.txtMoveColumn": "Mover para as <PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.txtMoveDown": "Mover para baixo", "SSE.Views.PivotSettings.txtMoveEnd": "Mover para o Fim", "SSE.Views.PivotSettings.txtMoveFilter": "Mover para os Filtros", "SSE.Views.PivotSettings.txtMoveRow": "Mover <PERSON> as <PERSON><PERSON>", "SSE.Views.PivotSettings.txtMoveUp": "Mover para cima", "SSE.Views.PivotSettings.txtMoveValues": "Mover para Valores", "SSE.Views.PivotSettings.txtRemove": "Remover Campo", "SSE.Views.PivotSettingsAdvanced.strLayout": "Nome e disposição", "SSE.Views.PivotSettingsAdvanced.textAlt": "Texto alternativo", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Descrição", "SSE.Views.PivotSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação do objeto visual, que será lida às pessoas com deficiências visuais ou cognitivas para as ajudar a compreender melhor a informação que existe na imagem, forma, gráfico ou tabela.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Ajustar automaticamente largura de coluna ao atualizar", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Intervalo de dados", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Fonte de dados", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Mostrar campos na área de filtros de relatórios", "SSE.Views.PivotSettingsAdvanced.textDown": "<PERSON><PERSON><PERSON>o, depois por cima", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "To<PERSON><PERSON>", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Cabeçalhos de Campos", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Views.PivotSettingsAdvanced.textOver": "De cima para Baixo", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Selecionar dados", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Mostrar para colunas", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Mostrar cabeçalhos dos campos para linhas e colunas", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Mostrar para linhas", "SSE.Views.PivotSettingsAdvanced.textTitle": "Tabela dinâmica - Definições avançadas", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Reportar campos de filtro do relatório por coluna", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Reportar campos de filtro do relatório por linha", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Este campo é obrigatório", "SSE.Views.PivotSettingsAdvanced.txtName": "Nome", "SSE.Views.PivotShowDetailDialog.textDescription": "Choose the field containing the detail you want to show:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Show Detail", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON> vazias", "SSE.Views.PivotTable.capGrandTotals": "To<PERSON><PERSON>", "SSE.Views.PivotTable.capLayout": "Disposição do relatório", "SSE.Views.PivotTable.capSubtotals": "Subtotais", "SSE.Views.PivotTable.mniBottomSubtotals": "Mostrar todos os subtotais no fundo do grupo", "SSE.Views.PivotTable.mniInsertBlankLine": "Inserir Linha em Branco depois de Cada Item", "SSE.Views.PivotTable.mniLayoutCompact": "Mostrar em Formato Compacto", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Não Repetir Todos os Rótulos dos Itens", "SSE.Views.PivotTable.mniLayoutOutline": "Mostrar em Formato de Destaques", "SSE.Views.PivotTable.mniLayoutRepeat": "<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>em", "SSE.Views.PivotTable.mniLayoutTabular": "Mostrar em Formato Tabular", "SSE.Views.PivotTable.mniNoSubtotals": "Não Mostrar Subtotais", "SSE.Views.PivotTable.mniOffTotals": "Ativado para Linhas e Colunas", "SSE.Views.PivotTable.mniOnColumnsTotals": "Ativado apenas para Colunas", "SSE.Views.PivotTable.mniOnRowsTotals": "Ativado apenas para Linhas ", "SSE.Views.PivotTable.mniOnTotals": "Ativado para Linhas e Colunas", "SSE.Views.PivotTable.mniRemoveBlankLine": "Remover a Linha Branca a Seguir a Cada Item", "SSE.Views.PivotTable.mniTopSubtotals": "Mostrar todos os subtotais no topo do grupo", "SSE.Views.PivotTable.textColBanded": "Colunas em bandas", "SSE.Views.PivotTable.textColHeader": "Cabeçalhos de coluna", "SSE.Views.PivotTable.textRowBanded": "Linhas em bandas", "SSE.Views.PivotTable.textRowHeader": "Cabeçalhos das Linhas", "SSE.Views.PivotTable.tipCalculatedItems": "Calculated items", "SSE.Views.PivotTable.tipCreatePivot": "Inserir tabela dinâ<PERSON>a", "SSE.Views.PivotTable.tipGrandTotals": "Mostrar ou esconder os totais gerais", "SSE.Views.PivotTable.tipRefresh": "Atualizar informação da origem de dados", "SSE.Views.PivotTable.tipRefreshCurrent": "Atualizar a informação da fonte de dados para a tabela atual", "SSE.Views.PivotTable.tipSelect": "Selecionar toda a tabela dinâmica", "SSE.Views.PivotTable.tipSubtotals": "Mostrar ou esconder subtotais", "SSE.Views.PivotTable.txtCalculatedItems": "Calculated Items", "SSE.Views.PivotTable.txtCollapseEntire": "Collapse Entire Field", "SSE.Views.PivotTable.txtCreate": "<PERSON><PERSON><PERSON> tabela", "SSE.Views.PivotTable.txtExpandEntire": "Expand Entire Field", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Personalizado", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Escuro", "SSE.Views.PivotTable.txtGroupPivot_Light": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Médio", "SSE.Views.PivotTable.txtPivotTable": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtRefresh": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtRefreshAll": "<PERSON><PERSON><PERSON>gar tudo", "SSE.Views.PivotTable.txtSelect": "Selecionar", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Pivot Table de estilo escuro", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Pivot Table de estilo claro", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Pivot table de estilo médio", "SSE.Views.PrintSettings.btnDownload": "Guardar e descarregar", "SSE.Views.PrintSettings.btnExport": "Save & Export", "SSE.Views.PrintSettings.btnPrint": "Guardar e imprimir", "SSE.Views.PrintSettings.strBottom": "Baixo", "SSE.Views.PrintSettings.strLandscape": "Horizontal", "SSE.Views.PrintSettings.strLeft": "E<PERSON>rda", "SSE.Views.PrintSettings.strMargins": "Margens", "SSE.Views.PrintSettings.strPortrait": "Vertical", "SSE.Views.PrintSettings.strPrint": "Imprimir", "SSE.Views.PrintSettings.strPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strShow": "Mostrar", "SSE.Views.PrintSettings.strTop": "Parte superior", "SSE.Views.PrintSettings.textActiveSheets": "Active sheets", "SSE.Views.PrintSettings.textActualSize": "Tamanho real", "SSE.Views.PrintSettings.textAllSheets": "<PERSON><PERSON> as folhas", "SSE.Views.PrintSettings.textCurrentSheet": "<PERSON><PERSON><PERSON> atual", "SSE.Views.PrintSettings.textCustom": "Personalizado", "SSE.Views.PrintSettings.textCustomOptions": "Opções de Personalização", "SSE.Views.PrintSettings.textFitCols": "Ajustar colunas a uma página", "SSE.Views.PrintSettings.textFitPage": "Ajustar folha a uma página", "SSE.Views.PrintSettings.textFitRows": "Ajustar linhas a uma página", "SSE.Views.PrintSettings.textHideDetails": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "SSE.Views.PrintSettings.textIgnore": "Ignorar <PERSON> Impressão", "SSE.Views.PrintSettings.textLayout": "Disposição", "SSE.Views.PrintSettings.textMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintSettings.textMarginsNormal": "Normal", "SSE.Views.PrintSettings.textMarginsWide": "Wide", "SSE.Views.PrintSettings.textPageOrientation": "Orientação da página", "SSE.Views.PrintSettings.textPages": "Pages:", "SSE.Views.PrintSettings.textPageScaling": "A ajustar", "SSE.Views.PrintSettings.textPageSize": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPrintGrid": "Imprimir linhas de grade", "SSE.Views.PrintSettings.textPrintHeadings": "Imprimir títulos de linhas e de colunas", "SSE.Views.PrintSettings.textPrintRange": "Imprimir intervalo", "SSE.Views.PrintSettings.textRange": "Intervalo", "SSE.Views.PrintSettings.textRepeat": "Repetir…", "SSE.Views.PrintSettings.textRepeatLeft": "Repetir colunas à esquerda", "SSE.Views.PrintSettings.textRepeatTop": "<PERSON><PERSON>r linhas no topo", "SSE.Views.PrintSettings.textSelection": "Se<PERSON><PERSON>", "SSE.Views.PrintSettings.textSettings": "Definições de folha", "SSE.Views.PrintSettings.textShowDetails": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textShowGrid": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textShowHeadings": "Mostrar títulos de linhas e de colunas", "SSE.Views.PrintSettings.textTitle": "Definições de impressão", "SSE.Views.PrintSettings.textTitlePDF": "Definições de PDF", "SSE.Views.PrintSettings.textTo": "to", "SSE.Views.PrintSettings.txtMarginsLast": "Last Custom", "SSE.Views.PrintTitlesDialog.textFirstCol": "Primeira coluna", "SSE.Views.PrintTitlesDialog.textFirstRow": "Primeira linha", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Colunas fixadas", "SSE.Views.PrintTitlesDialog.textFrozenRows": "<PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Views.PrintTitlesDialog.textLeft": "Repetir colunas à esquerda", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Não repetir", "SSE.Views.PrintTitlesDialog.textRepeat": "Repetir…", "SSE.Views.PrintTitlesDialog.textSelectRange": "Selecionar intervalo", "SSE.Views.PrintTitlesDialog.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textTop": "<PERSON><PERSON>r linhas no topo", "SSE.Views.PrintWithPreview.txtActiveSheets": "Active sheets", "SSE.Views.PrintWithPreview.txtActualSize": "Tamanho real", "SSE.Views.PrintWithPreview.txtAllSheets": "<PERSON><PERSON> as folhas", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Aplicar a todas as folhas", "SSE.Views.PrintWithPreview.txtBothSides": "Print on both sides", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "Flip pages on long edge", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "Flip pages on short edge", "SSE.Views.PrintWithPreview.txtBottom": "Baixo", "SSE.Views.PrintWithPreview.txtCopies": "Copies", "SSE.Views.PrintWithPreview.txtCurrentSheet": "<PERSON><PERSON><PERSON> atual", "SSE.Views.PrintWithPreview.txtCustom": "Personalizado", "SSE.Views.PrintWithPreview.txtCustomOptions": "Opções de Personalização", "SSE.Views.PrintWithPreview.txtEmptyTable": "Não há nada para imprimir porque a tabela está vazia.", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "First page number:", "SSE.Views.PrintWithPreview.txtFitCols": "Ajustar colunas a uma página", "SSE.Views.PrintWithPreview.txtFitPage": "Ajustar folha a uma página", "SSE.Views.PrintWithPreview.txtFitRows": "Ajustar linhas a uma página", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "<PERSON><PERSON> grelha e títulos", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Definições de cabeçalho/rodapé", "SSE.Views.PrintWithPreview.txtIgnore": "Ignorar <PERSON> Impressão", "SSE.Views.PrintWithPreview.txtLandscape": "Horizontal", "SSE.Views.PrintWithPreview.txtLeft": "E<PERSON>rda", "SSE.Views.PrintWithPreview.txtMargins": "Margens", "SSE.Views.PrintWithPreview.txtMarginsLast": "Last Custom", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "<PERSON>rrow", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Normal", "SSE.Views.PrintWithPreview.txtMarginsWide": "Wide", "SSE.Views.PrintWithPreview.txtOf": "de {0}", "SSE.Views.PrintWithPreview.txtOneSide": "Print one sided", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Only print on one side of the page", "SSE.Views.PrintWithPreview.txtPage": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Número da página inválido", "SSE.Views.PrintWithPreview.txtPageOrientation": "Orientação da página", "SSE.Views.PrintWithPreview.txtPages": "Pages:", "SSE.Views.PrintWithPreview.txtPageSize": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPortrait": "Vertical", "SSE.Views.PrintWithPreview.txtPrint": "Imprimir", "SSE.Views.PrintWithPreview.txtPrintGrid": "Imprimir linhas de grade", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Imprimir títulos de linhas e de colunas", "SSE.Views.PrintWithPreview.txtPrintRange": "Imprimir intervalo", "SSE.Views.PrintWithPreview.txtPrintSides": "Print sides", "SSE.Views.PrintWithPreview.txtPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Print to PDF", "SSE.Views.PrintWithPreview.txtRepeat": "Repetir…", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Repetir colunas à esquerda", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "<PERSON><PERSON>r linhas no topo", "SSE.Views.PrintWithPreview.txtRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSave": "Guardar", "SSE.Views.PrintWithPreview.txtScaling": "A ajustar", "SSE.Views.PrintWithPreview.txtSelection": "Se<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Definições da folha de cálculo", "SSE.Views.PrintWithPreview.txtSheet": "Folha: {0}", "SSE.Views.PrintWithPreview.txtTo": "to", "SSE.Views.PrintWithPreview.txtTop": "Parte superior", "SSE.Views.ProtectDialog.textExistName": "ERRO! Um intervalo com esse título já existe", "SSE.Views.ProtectDialog.textInvalidName": "O título do intervalo tem de começar com uma letra e só pode conter letras, números, e espaços.", "SSE.Views.ProtectDialog.textInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Views.ProtectDialog.textSelectData": "Selecionar dados", "SSE.Views.ProtectDialog.txtAllow": "Per<PERSON><PERSON> que todos os utilizadores utilizam esta folha de cálculo para", "SSE.Views.ProtectDialog.txtAllowDescription": "You can unlock specific ranges for editing.", "SSE.Views.ProtectDialog.txtAllowRanges": "Allow edit ranges", "SSE.Views.ProtectDialog.txtAutofilter": "Utilizar filtro automático", "SSE.Views.ProtectDialog.txtDelCols": "Eliminar colunas", "SSE.Views.ProtectDialog.txtDelRows": "Eliminar linhas", "SSE.Views.ProtectDialog.txtEmpty": "Este campo é obrigatório", "SSE.Views.ProtectDialog.txtFormatCells": "Formatar células", "SSE.Views.ProtectDialog.txtFormatCols": "Formatar colunas", "SSE.Views.ProtectDialog.txtFormatRows": "Formatar linhas", "SSE.Views.ProtectDialog.txtIncorrectPwd": "A palavra-passe de confirmação não é idêntica", "SSE.Views.ProtectDialog.txtInsCols": "Inserir colunas", "SSE.Views.ProtectDialog.txtInsHyper": "Inserir hip<PERSON>", "SSE.Views.ProtectDialog.txtInsRows": "<PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtObjs": "<PERSON><PERSON> objet<PERSON>", "SSE.Views.ProtectDialog.txtOptional": "Opcional", "SSE.Views.ProtectDialog.txtPassword": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtPivot": "Usar Tabela e Gráfico Dinâmico", "SSE.Views.ProtectDialog.txtProtect": "Proteger", "SSE.Views.ProtectDialog.txtRange": "Intervalo", "SSE.Views.ProtectDialog.txtRangeName": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectDialog.txtRepeat": "Repetição de palavra-passe", "SSE.Views.ProtectDialog.txtScen": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtSelLocked": "Selecionar células bloqueadas", "SSE.Views.ProtectDialog.txtSelUnLocked": "Selecionar células desblo<PERSON>s", "SSE.Views.ProtectDialog.txtSheetDescription": "Impeça alterações indesejadas feitas por outros ao limitar a permissão para editar.", "SSE.Views.ProtectDialog.txtSheetTitle": "<PERSON>te<PERSON> folha", "SSE.Views.ProtectDialog.txtSort": "Classificar", "SSE.Views.ProtectDialog.txtWarning": "Aviso: Se perder ou esquecer a palavra-passe, não será possível recuperá-la. Guarde-a num local seguro.", "SSE.Views.ProtectDialog.txtWBDescription": "Para impedir que outros utilizadores vejam folhas de cálculo ocultas, adicionar, mover, apagar, ou esconder folhas de trabalho e renomear folhas de cálculo, pode proteger a estrutura da sua folha de cálculo com uma palavra-passe.", "SSE.Views.ProtectDialog.txtWBTitle": "Proteger a Estrutura do Livro", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "Anonymous", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Anyone", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Edit", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Denied", "SSE.Views.ProtectedRangesEditDlg.textCanView": "View", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "The range title must begin with a letter and may only contain letters, numbers, and spaces.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "ERROR! Invalid cells range", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Remove", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "Selecionar dados", "SSE.Views.ProtectedRangesEditDlg.textYou": "you", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Access to range", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "This field is required", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "Protect", "SSE.Views.ProtectedRangesEditDlg.txtRange": "Range", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "Title", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Only you can edit this range", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Start typing name or email", "SSE.Views.ProtectedRangesManagerDlg.guestText": "Guest", "SSE.Views.ProtectedRangesManagerDlg.lockText": "Locked", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "Eliminar", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "Edit", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "No protected ranges have been created yet.<br>Create at least one protected range and it will appear in this field.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "Filter", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "All", "SSE.Views.ProtectedRangesManagerDlg.textNew": "New", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Protect sheet", "SSE.Views.ProtectedRangesManagerDlg.textRange": "Range", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "You can restrict editing or viewing ranges to selected people.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "Title", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "This element is being edited by another user.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Access", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Denied", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "Edit", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "Edit range", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "New range", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Protected ranges", "SSE.Views.ProtectedRangesManagerDlg.txtView": "View", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Are you sure you want to delete the protected range {0}?<br>Anyone who has edit access to the spreadsheet will be able to edit content in the range.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Are you sure you want to delete the protected ranges?<br>Anyone who has edit access to the spreadsheet will be able to edit content in those ranges.", "SSE.Views.ProtectRangesDlg.guestText": "Visitante", "SSE.Views.ProtectRangesDlg.lockText": "Bloqueada", "SSE.Views.ProtectRangesDlg.textDelete": "Eliminar", "SSE.Views.ProtectRangesDlg.textEdit": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEmpty": "Não é permitido editar nenhum intervalo.", "SSE.Views.ProtectRangesDlg.textNew": "Novo", "SSE.Views.ProtectRangesDlg.textProtect": "<PERSON>te<PERSON> folha", "SSE.Views.ProtectRangesDlg.textPwd": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textRange": "Intervalo", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Os intervalos são desbloqueados através de uma palavra-passe quando a folha de cálculo está protegida (isto apenas funciona para células bloqueadas)", "SSE.Views.ProtectRangesDlg.textTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Este elemento está sendo editado por outro usuário.", "SSE.Views.ProtectRangesDlg.txtEditRange": "<PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.txtNewRange": "Novo Intervalo", "SSE.Views.ProtectRangesDlg.txtNo": "Não", "SSE.Views.ProtectRangesDlg.txtTitle": "Permitir que os Utilizadores Editem Intervalos", "SSE.Views.ProtectRangesDlg.txtYes": "<PERSON>m", "SSE.Views.ProtectRangesDlg.warnDelete": "Tem a certeza que quer apagar o nome{0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Colunas", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Para eliminar valores duplicados, selecione uma ou mais colunas que contenham duplicados.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Os meus dados têm cabeçalhos", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Selecionar todos", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Remover duplicados", "SSE.Views.RightMenu.ariaRightMenu": "Right menu", "SSE.Views.RightMenu.txtCellSettings": "Definições de célula", "SSE.Views.RightMenu.txtChartSettings": "Definições de gráfico", "SSE.Views.RightMenu.txtImageSettings": "Definições de imagem", "SSE.Views.RightMenu.txtParagraphSettings": "Configurações do parágrafo", "SSE.Views.RightMenu.txtPivotSettings": "Definições de tabela dinâmica", "SSE.Views.RightMenu.txtSettings": "Definições comuns", "SSE.Views.RightMenu.txtShapeSettings": "Definições de forma", "SSE.Views.RightMenu.txtSignatureSettings": "Definições de assinatura", "SSE.Views.RightMenu.txtSlicerSettings": "Definições de 'slicer'", "SSE.Views.RightMenu.txtSparklineSettings": "Definições avançadas de 'sparkline'", "SSE.Views.RightMenu.txtTableSettings": "Definições de tabela", "SSE.Views.RightMenu.txtTextArtSettings": "Definições de texto artístico", "SSE.Views.ScaleDialog.textAuto": "Automático", "SSE.Views.ScaleDialog.textError": "O valor introduzido não está correto.", "SSE.Views.ScaleDialog.textFewPages": "p<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textFitTo": "Ajustar a", "SSE.Views.ScaleDialog.textHeight": "Altura", "SSE.Views.ScaleDialog.textManyPages": "p<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textOnePage": "p<PERSON><PERSON><PERSON>", "SSE.Views.ScaleDialog.textScaleTo": "Ajustar Para", "SSE.Views.ScaleDialog.textTitle": "Definições de escala", "SSE.Views.ScaleDialog.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "O valor máximo para este campo é {0}", "SSE.Views.SetValueDialog.txtMinText": "O valor mínimo para este campo é {0}", "SSE.Views.ShapeSettings.strBackground": "Cor de fundo", "SSE.Views.ShapeSettings.strChange": "Alterar forma automática", "SSE.Views.ShapeSettings.strColor": "Cor", "SSE.Views.ShapeSettings.strFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strForeground": "Cor principal", "SSE.Views.ShapeSettings.strPattern": "Padrão", "SSE.Views.ShapeSettings.strShadow": "Mostrar sombra", "SSE.Views.ShapeSettings.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "Opacidade", "SSE.Views.ShapeSettings.strType": "Tipo", "SSE.Views.ShapeSettings.textAdjustShadow": "Adjust Shadow", "SSE.Views.ShapeSettings.textAdvanced": "Mostrar definições avançadas", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "O valor inserido não está correto.<br>Introduza um valor entre 0 pt e 1584 pt.", "SSE.Views.ShapeSettings.textColor": "<PERSON><PERSON> <PERSON> pre<PERSON>", "SSE.Views.ShapeSettings.textDirection": "Direção", "SSE.Views.ShapeSettings.textEditPoints": "Edit points", "SSE.Views.ShapeSettings.textEditShape": "Edit shape", "SSE.Views.ShapeSettings.textEmptyPattern": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textEyedropper": "Eyedropper", "SSE.Views.ShapeSettings.textFlip": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textFromFile": "De um ficheiro", "SSE.Views.ShapeSettings.textFromStorage": "De um armazenamento", "SSE.Views.ShapeSettings.textFromUrl": "De um URL", "SSE.Views.ShapeSettings.textGradient": "Ponto de gradiente", "SSE.Views.ShapeSettings.textGradientFill": "Preenchimento gradiente", "SSE.Views.ShapeSettings.textHint270": "Rodar 90º à esquerda", "SSE.Views.ShapeSettings.textHint90": "Rodar 90º à direita", "SSE.Views.ShapeSettings.textHintFlipH": "Virar horizontalmente", "SSE.Views.ShapeSettings.textHintFlipV": "Virar verticalmente", "SSE.Views.ShapeSettings.textImageTexture": "<PERSON><PERSON> ou Textura", "SSE.Views.ShapeSettings.textLinear": "Linear", "SSE.Views.ShapeSettings.textMoreColors": "More colors", "SSE.Views.ShapeSettings.textNoFill": "Sem preenchimento", "SSE.Views.ShapeSettings.textNoShadow": "No Shadow", "SSE.Views.ShapeSettings.textOriginalSize": "<PERSON><PERSON><PERSON> original", "SSE.Views.ShapeSettings.textPatternFill": "Padrão", "SSE.Views.ShapeSettings.textPosition": "Posição", "SSE.Views.ShapeSettings.textRadial": "Radial", "SSE.Views.ShapeSettings.textRecentlyUsed": "Utilizado recentemente", "SSE.Views.ShapeSettings.textRotate90": "Rodar 90°", "SSE.Views.ShapeSettings.textRotation": "Rotação", "SSE.Views.ShapeSettings.textSelectImage": "Selecionar imagem", "SSE.Views.ShapeSettings.textSelectTexture": "Selecionar", "SSE.Views.ShapeSettings.textShadow": "Shadow", "SSE.Views.ShapeSettings.textStretch": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textTexture": "De uma textura", "SSE.Views.ShapeSettings.textTile": "Lado a lado", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Adicionar ponto de gradiente", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Remover Ponto de Gradiente", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON>pel pardo", "SSE.Views.ShapeSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtCarton": "Papelão", "SSE.Views.ShapeSettings.txtDarkFabric": "Tela escura", "SSE.Views.ShapeSettings.txtGrain": "Granulação", "SSE.Views.ShapeSettings.txtGranite": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "Papel cinza", "SSE.Views.ShapeSettings.txtKnit": "Unir", "SSE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON> linha", "SSE.Views.ShapeSettings.txtPapyrus": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtWood": "Madeira", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Colunas", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Preenchimento de texto", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Não mover ou redimensionar com células", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Texto alternativo", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Descrição", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação visual do objeto, que será lida para as pessoas com deficiências visuais ou cognitivas para ajudá-las a entender melhor que informação, forma, gráfico ou tabela existe na imagem.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Set<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Ajuste automático", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON><PERSON><PERSON> inicial", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "<PERSON><PERSON><PERSON> inici<PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Baixo", "SSE.Views.ShapeSettingsAdvanced.textCapType": "<PERSON><PERSON><PERSON> de letra", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Número de <PERSON>nas", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Tamanho final", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Estilo final", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Plano", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Invertido", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Altura", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontalmente", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Tipo de junção", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Proporções constantes", "SSE.Views.ShapeSettingsAdvanced.textLeft": "E<PERSON>rda", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Malhete", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Mover mas não redimensionar células", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Permitir que o texto ultrapasse os limites da forma", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Redimensionar forma para se ajustar ao texto", "SSE.Views.ShapeSettingsAdvanced.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Rotação", "SSE.Views.ShapeSettingsAdvanced.textRound": "Rodada", "SSE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Alinhamento de <PERSON>lulas", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Espaçamento entre colunas", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Quadrado", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Caixa de texto", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Forma - Definições avançadas", "SSE.Views.ShapeSettingsAdvanced.textTop": "Parte superior", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Mover e redimensionar com células", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Verticalmente", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Pesos e Setas", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Aviso", "SSE.Views.SignatureSettings.strDelete": "Remover assinatura", "SSE.Views.SignatureSettings.strDetails": "Detalhes da assinatura", "SSE.Views.SignatureSettings.strInvalid": "Assinaturas inválidas", "SSE.Views.SignatureSettings.strRequested": "Assinaturas solicitadas", "SSE.Views.SignatureSettings.strSetup": "Definições de Assinatura", "SSE.Views.SignatureSettings.strSign": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strSignature": "Assinatura", "SSE.Views.SignatureSettings.strSigner": "<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.strValid": "Assinaturas válidas", "SSE.Views.SignatureSettings.txtContinueEditing": "<PERSON>ar mesmo assim", "SSE.Views.SignatureSettings.txtEditWarning": "A edição irá remover as assinaturas da folha de cálculo.<br>Continuar?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Quer remover esta assinatura?<br><PERSON><PERSON> não pode ser anulado.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Esta folha de cálculo precisa de ser assinada.", "SSE.Views.SignatureSettings.txtSigned": "Assinaturas adicionadas ao documento. Esta folha de cálculo não pode ser editada.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Algumas das assinaturas digitais são inválidas ou não puderam ser verificadas. Esta folha de cálculo documento não pode ser editada.", "SSE.Views.SlicerAddDialog.textColumns": "Colunas", "SSE.Views.SlicerAddDialog.txtTitle": "Inserir segmentaçãoes de dados", "SSE.Views.SlicerSettings.strHideNoData": "O<PERSON>ltar itens sem dados", "SSE.Views.SlicerSettings.strIndNoData": "Indicar visualmente itens sem dados", "SSE.Views.SlicerSettings.strShowDel": "Mostrar itens eliminados da origem de dados", "SSE.Views.SlicerSettings.strShowNoData": "Mostrar itens sem dados no fim", "SSE.Views.SlicerSettings.strSorting": "A Ordenar e a Filtrar", "SSE.Views.SlicerSettings.textAdvanced": "Mostrar definições avançadas", "SSE.Views.SlicerSettings.textAsc": "Ascendente", "SSE.Views.SlicerSettings.textAZ": "A -> Z", "SSE.Views.SlicerSettings.textButtons": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textColumns": "Colunas", "SSE.Views.SlicerSettings.textDesc": "Decrescente", "SSE.Views.SlicerSettings.textHeight": "Altura", "SSE.Views.SlicerSettings.textHor": "Horizontal", "SSE.Views.SlicerSettings.textKeepRatio": "Proporções constantes", "SSE.Views.SlicerSettings.textLargeSmall": "Do maior para o mais pequeno", "SSE.Views.SlicerSettings.textLock": "Desativar redimensionamento ou movimentações", "SSE.Views.SlicerSettings.textNewOld": "Do mais recente para o mais antigo", "SSE.Views.SlicerSettings.textOldNew": "Do mais antigo para o mais recente", "SSE.Views.SlicerSettings.textPosition": "Posição", "SSE.Views.SlicerSettings.textSize": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textSmallLarge": "Do menor para o maior", "SSE.Views.SlicerSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textVert": "Vertical", "SSE.Views.SlicerSettings.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "Z -> A ", "SSE.Views.SlicerSettingsAdvanced.strButtons": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Colunas", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Altura", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "O<PERSON>ltar itens sem dados", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Indicar visualmente itens sem dados", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Referências", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Mostrar itens eliminados da origem de dados", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Mostrar cab<PERSON>ho", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Mostrar itens sem dados no fim", "SSE.Views.SlicerSettingsAdvanced.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strSorting": "A Ordenar e a Filtrar", "SSE.Views.SlicerSettingsAdvanced.strStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Estilo e tamanho", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Não mover ou redimensionar com células", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Texto alternativo", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Descrição", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação do objeto visual, que será lida às pessoas com deficiências visuais ou cognitivas para as ajudar a compreender melhor a informação que existe na imagem, forma, gráfico ou tabela.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Ascendente", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A -> Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Decrescente", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Nome para usar nas fórmulas", "SSE.Views.SlicerSettingsAdvanced.textHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Proporções constantes", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "Do maior para o mais pequeno", "SSE.Views.SlicerSettingsAdvanced.textName": "Nome", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "Do mais recente para o mais antigo", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "Do mais antigo para o mais recente", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Mover mas não redimensionar células", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "Do menor para o maior", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Alinhamento de <PERSON>lulas", "SSE.Views.SlicerSettingsAdvanced.textSort": "Classificar", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Nome da origem", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Sclicer - Definições avançadas", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Mover e redimensionar com células", "SSE.Views.SlicerSettingsAdvanced.textZA": "Z -> A ", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Este campo é obrigatório", "SSE.Views.SortDialog.errorEmpty": "Todos os critérios de ordenação têm de ter uma coluna ou linha especificada.", "SSE.Views.SortDialog.errorMoreOneCol": "Selecionou mais do que uma coluna.", "SSE.Views.SortDialog.errorMoreOneRow": "Selecionou mais do que uma linha.", "SSE.Views.SortDialog.errorNotOriginalCol": "A coluna que selecionou não está no intervalo que selecionou originalmente.", "SSE.Views.SortDialog.errorNotOriginalRow": "A linha selecionada não se encontra no intervalo original selecionado.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 está a ser ordenada pela mesma cor mais do que uma vez.<br><PERSON><PERSON> o critério duplicado e tente novamente.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 está a ser ordenado mais do que uma vez por valores.<br><PERSON><PERSON> os critérios duplicados e tente novamente.", "SSE.Views.SortDialog.textAsc": "Ascendente", "SSE.Views.SortDialog.textAuto": "Automático", "SSE.Views.SortDialog.textAZ": "A -> Z", "SSE.Views.SortDialog.textBelow": "Abaixo", "SSE.Views.SortDialog.textBtnCopy": "Copy", "SSE.Views.SortDialog.textBtnDelete": "Eliminar", "SSE.Views.SortDialog.textBtnNew": "New", "SSE.Views.SortDialog.textCellColor": "<PERSON><PERSON> <PERSON>", "SSE.Views.SortDialog.textColumn": "Coluna", "SSE.Views.SortDialog.textDesc": "Decrescente", "SSE.Views.SortDialog.textDown": "Mover para nível mais baixo", "SSE.Views.SortDialog.textFontColor": "Cor do tipo de letra", "SSE.Views.SortDialog.textLeft": "E<PERSON>rda", "SSE.Views.SortDialog.textLevels": "Levels", "SSE.Views.SortDialog.textMoreCols": "(<PERSON><PERSON> co<PERSON>...)", "SSE.Views.SortDialog.textMoreRows": "(<PERSON><PERSON> lin<PERSON>...)", "SSE.Views.SortDialog.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textOptions": "Opções", "SSE.Views.SortDialog.textOrder": "Ordenar", "SSE.Views.SortDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textRow": "<PERSON><PERSON>", "SSE.Views.SortDialog.textSort": "Ordenar ligado", "SSE.Views.SortDialog.textSortBy": "Ordenar por", "SSE.Views.SortDialog.textThenBy": "<PERSON><PERSON><PERSON> por", "SSE.Views.SortDialog.textTop": "Parte superior", "SSE.Views.SortDialog.textUp": "Mover para nível mais alto", "SSE.Views.SortDialog.textValues": "Valores", "SSE.Views.SortDialog.textZA": "Z -> A ", "SSE.Views.SortDialog.txtInvalidRange": "Intervalo de células inválido.", "SSE.Views.SortDialog.txtTitle": "Classificar", "SSE.Views.SortFilterDialog.textAsc": "Ascendente (A->Z) por", "SSE.Views.SortFilterDialog.textDesc": "Descendente (Z a A) por", "SSE.Views.SortFilterDialog.textNoSort": "No sort", "SSE.Views.SortFilterDialog.txtTitle": "Classificar", "SSE.Views.SortFilterDialog.txtTitleValue": "Sort by value", "SSE.Views.SortOptionsDialog.textCase": "Diferenciar maiús<PERSON>s de minúsculas", "SSE.Views.SortOptionsDialog.textHeaders": "Os meus dados têm cabeçalhos", "SSE.Views.SortOptionsDialog.textLeftRight": "Ordenar da esquerda para a direita", "SSE.Views.SortOptionsDialog.textOrientation": "Orientação", "SSE.Views.SortOptionsDialog.textTitle": "Opções de Ordenação", "SSE.Views.SortOptionsDialog.textTopBottom": "Ordenar de cima para baixo", "SSE.Views.SpecialPasteDialog.textAdd": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textAll": "<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textBlanks": "<PERSON><PERSON><PERSON> c<PERSON> em branco", "SSE.Views.SpecialPasteDialog.textColWidth": "<PERSON><PERSON><PERSON><PERSON> coluna", "SSE.Views.SpecialPasteDialog.textComments": "Comentários", "SSE.Views.SpecialPasteDialog.textDiv": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFFormat": "Fórmulas e Formatação", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formulas e Formatos Numéricos", "SSE.Views.SpecialPasteDialog.textFormats": "Formatos", "SSE.Views.SpecialPasteDialog.textFormulas": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textFWidth": "Largura das Fórmulas e Colunas", "SSE.Views.SpecialPasteDialog.textMult": "Multiplicar", "SSE.Views.SpecialPasteDialog.textNone": "<PERSON><PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textOperation": "Operação", "SSE.Views.SpecialPasteDialog.textPaste": "Colar", "SSE.Views.SpecialPasteDialog.textSub": "Subtrair", "SSE.Views.SpecialPasteDialog.textTitle": "Colar especial", "SSE.Views.SpecialPasteDialog.textTranspose": "Transpor", "SSE.Views.SpecialPasteDialog.textValues": "Valores", "SSE.Views.SpecialPasteDialog.textVFormat": "Valores e formatação", "SSE.Views.SpecialPasteDialog.textVNFormat": "Valores e formatos numéricos", "SSE.Views.SpecialPasteDialog.textWBorders": "<PERSON>do exceto contornos", "SSE.Views.Spellcheck.noSuggestions": "Sem sugestões ortográficas", "SSE.Views.Spellcheck.textChange": "Alterar", "SSE.Views.Spellcheck.textChangeAll": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textIgnore": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textIgnoreAll": "<PERSON><PERSON><PERSON> tudo", "SSE.Views.Spellcheck.txtAddToDictionary": "Adicionar ao dicionário", "SSE.Views.Spellcheck.txtClosePanel": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.txtComplete": "A verificação ortográfica foi concluída", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Idioma do Dicionário", "SSE.Views.Spellcheck.txtNextTip": "Ir para a próxima palavra", "SSE.Views.Spellcheck.txtSpelling": "Ortografia", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Mover para o final)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "Create a copy", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(Create new spreadsheet)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Mover antes da folha", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Spreadsheet", "SSE.Views.Statusbar.filteredRecordsText": "Filtrados {0} de {1} registos", "SSE.Views.Statusbar.filteredText": "<PERSON>do de filtro", "SSE.Views.Statusbar.itemAverage": "Média", "SSE.Views.Statusbar.itemCount": "Contagem", "SSE.Views.Statusbar.itemDelete": "Eliminar", "SSE.Views.Statusbar.itemHidden": "Oculto", "SSE.Views.Statusbar.itemHide": "Ocultar", "SSE.Views.Statusbar.itemInsert": "Inserir", "SSE.Views.Statusbar.itemMaximum": "Máximo", "SSE.Views.Statusbar.itemMinimum": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemMoveOrCopy": "Move or copy", "SSE.Views.Statusbar.itemProtect": "Proteger", "SSE.Views.Statusbar.itemRename": "<PERSON>dar nome", "SSE.Views.Statusbar.itemStatus": "A guardar estado", "SSE.Views.Statusbar.itemSum": "<PERSON><PERSON>", "SSE.Views.Statusbar.itemTabColor": "Cor do separador", "SSE.Views.Statusbar.itemUnProtect": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Planilha com este nome já existe.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Um nome de folha não pode conter os seguintes caracteres: \\/*?[]:", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "<PERSON>me da folha", "SSE.Views.Statusbar.selectAllSheets": "<PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON> de <PERSON>á<PERSON>lo", "SSE.Views.Statusbar.sheetIndexText": "Folha {0} de {1}", "SSE.Views.Statusbar.textAverage": "Média", "SSE.Views.Statusbar.textCount": "Contar", "SSE.Views.Statusbar.textMax": "Máx", "SSE.Views.Statusbar.textMin": "<PERSON>.", "SSE.Views.Statusbar.textNewColor": "Adicionar nova cor personalizada", "SSE.Views.Statusbar.textNoColor": "Sem cor", "SSE.Views.Statusbar.textSum": "SOMA", "SSE.Views.Statusbar.tipAddTab": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipFirst": "<PERSON>r para a primeira folha", "SSE.Views.Statusbar.tipLast": "Ir para a última folha", "SSE.Views.Statusbar.tipListOfSheets": "Lista de folhas", "SSE.Views.Statusbar.tipNext": "Ir para a folha à direita", "SSE.Views.Statusbar.tipPrev": "Ir para a folha à esquerda", "SSE.Views.Statusbar.tipZoomFactor": "Ampliação", "SSE.Views.Statusbar.tipZoomIn": "Ampliar", "SSE.Views.Statusbar.tipZoomOut": "Reduzir", "SSE.Views.Statusbar.ungroupSheets": "Desag<PERSON>ar <PERSON>", "SSE.Views.Statusbar.zoomText": "Ampliação {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Não foi possível concluir a operação para o intervalo de células selecionado.<br>Selecione um intervalo de dados diferente e tente novamente.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Não foi possível completar a operação para o intervalo de células selecionado. <br>Selecionou um intervalo de uma forma que a primeira linha da tabela estava na mesma linha <br>então a tabela que criou sobrepôs-se à atual.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Não foi possível completar a operação para o intervalo de células selecionado.<br>Selecione um intervalo que não inclua outras tabelas.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Intervalo de fórmulas multi-célula não são permitidas em tabelas.", "SSE.Views.TableOptionsDialog.txtEmpty": "Este campo é obrigatório", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON><PERSON> tabela", "SSE.Views.TableOptionsDialog.txtInvalidRange": "ERRO! Intervalo de células inválido", "SSE.Views.TableOptionsDialog.txtNote": "Os cabeçalhos devem permanecer na mesma linha, e o intervalo da tabela resultante deve sobrepor-se ao intervalo da tabela original.", "SSE.Views.TableOptionsDialog.txtTitle": "Titulo", "SSE.Views.TableSettings.deleteColumnText": "Eliminar coluna", "SSE.Views.TableSettings.deleteRowText": "Eliminar linha", "SSE.Views.TableSettings.deleteTableText": "Eliminar tabela", "SSE.Views.TableSettings.insertColumnLeftText": "Inserir coluna à esquerda", "SSE.Views.TableSettings.insertColumnRightText": "Inserir coluna à direita", "SSE.Views.TableSettings.insertRowAboveText": "<PERSON><PERSON><PERSON> linha acima", "SSE.Views.TableSettings.insertRowBelowText": "<PERSON>ser<PERSON> linha a<PERSON>o", "SSE.Views.TableSettings.notcriticalErrorTitle": "Aviso", "SSE.Views.TableSettings.selectColumnText": "Selecionar Dados das Colunas", "SSE.Views.TableSettings.selectDataText": "Selecionar Dados das Colunas", "SSE.Views.TableSettings.selectRowText": "Selecionar linha", "SSE.Views.TableSettings.selectTableText": "Selecionar tabela", "SSE.Views.TableSettings.textActions": "Ações de tabela", "SSE.Views.TableSettings.textAdvanced": "Mostrar definições avançadas", "SSE.Views.TableSettings.textBanded": "Bandas", "SSE.Views.TableSettings.textColumns": "Colunas", "SSE.Views.TableSettings.textConvertRange": "Converter para intervalo", "SSE.Views.TableSettings.textEdit": "Linhas e Colunas", "SSE.Views.TableSettings.textEmptyTemplate": "Sem modelos", "SSE.Views.TableSettings.textExistName": "ERRO! Já existe um intervalo com este nome", "SSE.Views.TableSettings.textFilter": "Botão Filtro", "SSE.Views.TableSettings.textFirst": "<PERSON><PERSON>", "SSE.Views.TableSettings.textHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textInvalidName": "ERRO! Nome inválido", "SSE.Views.TableSettings.textIsLocked": "Este elemento está a ser editado por outro utilizador.", "SSE.Views.TableSettings.textLast": "Última", "SSE.Views.TableSettings.textLongOperation": "Operação longa", "SSE.Views.TableSettings.textPivot": "Inserir tabela dinâ<PERSON>a", "SSE.Views.TableSettings.textRemDuplicates": "Remover duplicados", "SSE.Views.TableSettings.textReservedName": "O nome que está a tentar usar já está referenciado em fórmulas celulares. Por favor, use algum outro nome.", "SSE.Views.TableSettings.textResize": "Redimensionar tabela", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "Selecionar dados", "SSE.Views.TableSettings.textSlicer": "Inserir segmentação de dados", "SSE.Views.TableSettings.textTableName": "<PERSON>me da tabela", "SSE.Views.TableSettings.textTemplate": "Selecionar de um modelo", "SSE.Views.TableSettings.textTotal": "Total", "SSE.Views.TableSettings.txtGroupTable_Custom": "Personalizado", "SSE.Views.TableSettings.txtGroupTable_Dark": "Escuro", "SSE.Views.TableSettings.txtGroupTable_Light": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.txtGroupTable_Medium": "Média", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Estilo de tabela Escuro", "SSE.Views.TableSettings.txtTable_TableStyleLight": "Estilo de tabela Claro", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "Estilo de tabela Médio", "SSE.Views.TableSettings.warnLongOperation": "A operação que está prestes a realizar pode levar muito tempo a concluir.<br>Tem a certeza de que quer continuar?", "SSE.Views.TableSettingsAdvanced.textAlt": "Texto alternativo", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Descrição", "SSE.Views.TableSettingsAdvanced.textAltTip": "A representação alternativa baseada em texto da informação do objeto visual, que será lida às pessoas com deficiências visuais ou cognitivas para as ajudar a compreender melhor a informação que existe na imagem, forma, gráfico ou tabela.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TableSettingsAdvanced.textTitle": "Tabela - Definições avançadas", "SSE.Views.TextArtSettings.strBackground": "Cor de fundo", "SSE.Views.TextArtSettings.strColor": "Cor", "SSE.Views.TextArtSettings.strFill": "Fill", "SSE.Views.TextArtSettings.strForeground": "Cor principal", "SSE.Views.TextArtSettings.strPattern": "Padrão", "SSE.Views.TextArtSettings.strSize": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strStroke": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "Opacidade", "SSE.Views.TextArtSettings.strType": "Tipo", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "O valor inserido não está correto.<br>Introduza um valor entre 0 pt e 1584 pt.", "SSE.Views.TextArtSettings.textColor": "<PERSON><PERSON> <PERSON> pre<PERSON>", "SSE.Views.TextArtSettings.textDirection": "Direção", "SSE.Views.TextArtSettings.textEmptyPattern": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textFromFile": "De um ficheiro", "SSE.Views.TextArtSettings.textFromUrl": "De um URL", "SSE.Views.TextArtSettings.textGradient": "Ponto de gradiente", "SSE.Views.TextArtSettings.textGradientFill": "Preenchimento gradiente", "SSE.Views.TextArtSettings.textImageTexture": "Picture or Texture", "SSE.Views.TextArtSettings.textLinear": "Linear", "SSE.Views.TextArtSettings.textNoFill": "Sem preenchimento", "SSE.Views.TextArtSettings.textPatternFill": "Padrão", "SSE.Views.TextArtSettings.textPosition": "Posição", "SSE.Views.TextArtSettings.textRadial": "Radial", "SSE.Views.TextArtSettings.textSelectTexture": "Selecionar", "SSE.Views.TextArtSettings.textStretch": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textTemplate": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textTexture": "De uma textura", "SSE.Views.TextArtSettings.textTile": "Lado a lado", "SSE.Views.TextArtSettings.textTransform": "Transform", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Adicionar ponto de gradiente", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Remover Ponto de Gradiente", "SSE.Views.TextArtSettings.txtBrownPaper": "Brown Paper", "SSE.Views.TextArtSettings.txtCanvas": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "Tela escura", "SSE.Views.TextArtSettings.txtGrain": "Grain", "SSE.Views.TextArtSettings.txtGranite": "Granite", "SSE.Views.TextArtSettings.txtGreyPaper": "Gray Paper", "SSE.Views.TextArtSettings.txtKnit": "Unir", "SSE.Views.TextArtSettings.txtLeather": "Leather", "SSE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON> linha", "SSE.Views.TextArtSettings.txtPapyrus": "Papyrus", "SSE.Views.TextArtSettings.txtWood": "<PERSON>", "SSE.Views.Toolbar.capBtnAddComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnColorSchemas": "Esquema de cores", "SSE.Views.Toolbar.capBtnComment": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnInsHeader": "Cabeçalho/rodapé", "SSE.Views.Toolbar.capBtnInsSlicer": "Segmentação de dados", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Símbolo", "SSE.Views.Toolbar.capBtnMargins": "Margens", "SSE.Views.Toolbar.capBtnPageBreak": "Breaks", "SSE.Views.Toolbar.capBtnPageOrient": "Orientação", "SSE.Views.Toolbar.capBtnPageSize": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintArea": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capBtnScale": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capImgAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capImgBackward": "Enviar para trás", "SSE.Views.Toolbar.capImgForward": "Trazer para a frente", "SSE.Views.Toolbar.capImgGroup": "Grupo", "SSE.Views.Toolbar.capInsertChart": "Gráfico", "SSE.Views.Toolbar.capInsertChartRecommend": "Recommended Chart", "SSE.Views.Toolbar.capInsertEquation": "Equação", "SSE.Views.Toolbar.capInsertHyperlink": "Hiperligação", "SSE.Views.Toolbar.capInsertImage": "Imagem", "SSE.Views.Toolbar.capInsertShape": "Forma", "SSE.Views.Toolbar.capInsertSpark": "Sparkline", "SSE.Views.Toolbar.capInsertTable": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capInsertText": "Caixa de texto", "SSE.Views.Toolbar.capInsertTextart": "Lágrima", "SSE.Views.Toolbar.capShapesMerge": "<PERSON><PERSON>", "SSE.Views.Toolbar.mniCapitalizeWords": "Capitalize Each Word", "SSE.Views.Toolbar.mniImageFromFile": "Imagem de um ficheiro", "SSE.Views.Toolbar.mniImageFromStorage": "Imagem de um armazenamento", "SSE.Views.Toolbar.mniImageFromUrl": "Imagem de um URL", "SSE.Views.Toolbar.mniLowerCase": "lowercase", "SSE.Views.Toolbar.mniSentenceCase": "Sentence case.", "SSE.Views.Toolbar.mniToggleCase": "tOGGLE cASE", "SSE.Views.Toolbar.mniUpperCase": "UPPERCASE", "SSE.Views.Toolbar.textAddPrintArea": "Adicionar à área de impressão", "SSE.Views.Toolbar.textAlignBottom": "<PERSON><PERSON><PERSON> em baixo", "SSE.Views.Toolbar.textAlignCenter": "Alinhar ao centro", "SSE.Views.Toolbar.textAlignJust": "Justificado", "SSE.Views.Toolbar.textAlignLeft": "Alinhar à esquerda", "SSE.Views.Toolbar.textAlignMiddle": "<PERSON><PERSON>ar ao meio", "SSE.Views.Toolbar.textAlignRight": "Alinhar à direita", "SSE.Views.Toolbar.textAlignTop": "Alinhar em cima", "SSE.Views.Toolbar.textAllBorders": "<PERSON><PERSON> as bordas", "SSE.Views.Toolbar.textAlpha": "Greek Small Letter Alpha", "SSE.Views.Toolbar.textAuto": "Automático", "SSE.Views.Toolbar.textAutoColor": "Automático", "SSE.Views.Toolbar.textBetta": "Greek Small Letter Beta", "SSE.Views.Toolbar.textBlackHeart": "Black Heart Suit", "SSE.Views.Toolbar.textBold": "Negrito", "SSE.Views.Toolbar.textBordersColor": "Cor do contorno", "SSE.Views.Toolbar.textBordersStyle": "Estilo do contorno", "SSE.Views.Toolbar.textBottom": "Baixo: ", "SSE.Views.Toolbar.textBottomBorders": "Contornos inferiores", "SSE.Views.Toolbar.textBullet": "Bullet", "SSE.Views.Toolbar.textCellAlign": "Format cell alignment", "SSE.Views.Toolbar.textCenterBorders": "Bordas verticais interiores", "SSE.Views.Toolbar.textClearPrintArea": "Limpar <PERSON>", "SSE.Views.Toolbar.textClearRule": "<PERSON><PERSON> regras", "SSE.Views.Toolbar.textClockwise": "<PERSON><PERSON><PERSON> para a direita", "SSE.Views.Toolbar.textColorScales": "Escalas de cores", "SSE.Views.Toolbar.textCopyright": "Copyright Sign", "SSE.Views.Toolbar.textCounterCw": "Ângulo para a esquerda", "SSE.Views.Toolbar.textCustom": "Personalizado", "SSE.Views.Toolbar.textDataBars": "Barras de dados", "SSE.Views.Toolbar.textDegree": "Degree Sign", "SSE.Views.Toolbar.textDelLeft": "Deslocar células para a esquerda", "SSE.Views.Toolbar.textDelPageBreak": "Remover quebra de página", "SSE.Views.Toolbar.textDelta": "Greek Small Letter Delta", "SSE.Views.Toolbar.textDelUp": "Deslocar células para cima", "SSE.Views.Toolbar.textDiagDownBorder": "Borda inferior diagonal", "SSE.Views.Toolbar.textDiagUpBorder": "Borda superior diagonal", "SSE.Views.Toolbar.textDivision": "Division Sign", "SSE.Views.Toolbar.textDollar": "Dollar Sign", "SSE.Views.Toolbar.textDone": "Concluido", "SSE.Views.Toolbar.textDown": "Down", "SSE.Views.Toolbar.textEditVA": "Editar <PERSON> v<PERSON>", "SSE.Views.Toolbar.textEntireCol": "Coluna inteira", "SSE.Views.Toolbar.textEntireRow": "<PERSON><PERSON>", "SSE.Views.Toolbar.textEuro": "Euro Sign", "SSE.Views.Toolbar.textFewPages": "p<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textFillLeft": "Left", "SSE.Views.Toolbar.textFillRight": "Right", "SSE.Views.Toolbar.textFormatCellFill": "Format cell fill", "SSE.Views.Toolbar.textGreaterEqual": "Greater Than Or Equal To", "SSE.Views.Toolbar.textHeight": "Altura", "SSE.Views.Toolbar.textHideVA": "Esconder Á<PERSON>", "SSE.Views.Toolbar.textHorizontal": "Texto horizontal", "SSE.Views.Toolbar.textInfinity": "Infinity", "SSE.Views.Toolbar.textInsDown": "Deslocar células para baixo", "SSE.Views.Toolbar.textInsideBorders": "Bordas interiores", "SSE.Views.Toolbar.textInsPageBreak": "Inserir quebra de página", "SSE.Views.Toolbar.textInsRight": "Deslocar células para a direita", "SSE.Views.Toolbar.textItalic": "Itálico", "SSE.Views.Toolbar.textItems": "<PERSON><PERSON>", "SSE.Views.Toolbar.textLandscape": "Horizontal", "SSE.Views.Toolbar.textLeft": "Esquerda: ", "SSE.Views.Toolbar.textLeftBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textLessEqual": "Less Than Or Equal To", "SSE.Views.Toolbar.textLetterPi": "Greek Small Letter Pi", "SSE.Views.Toolbar.textManageRule": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textManyPages": "p<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsLast": "Última personalizada", "SSE.Views.Toolbar.textMarginsNarrow": "Estreita", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "Amplo", "SSE.Views.Toolbar.textMiddleBorders": "Bordas horizontais interiores", "SSE.Views.Toolbar.textMoreBorders": "More borders", "SSE.Views.Toolbar.textMoreFormats": "<PERSON><PERSON> formatos", "SSE.Views.Toolbar.textMorePages": "<PERSON><PERSON>", "SSE.Views.Toolbar.textMoreSymbols": "More symbols", "SSE.Views.Toolbar.textNewColor": "Adicionar nova cor personalizada", "SSE.Views.Toolbar.textNewRule": "Nova Regra", "SSE.Views.Toolbar.textNoBorders": "<PERSON><PERSON> bordas", "SSE.Views.Toolbar.textNotEqualTo": "Not Equal To", "SSE.Views.Toolbar.textOneHalf": "Vulgar Fraction One Half", "SSE.Views.Toolbar.textOnePage": "p<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textOneQuarter": "Vulgar Fraction One Quarter", "SSE.Views.Toolbar.textOutBorders": "<PERSON><PERSON><PERSON> externas", "SSE.Views.Toolbar.textPageMarginsCustom": "Margens personalizadas", "SSE.Views.Toolbar.textPlusMinus": "Plus-Minus Sign", "SSE.Views.Toolbar.textPortrait": "Vertical", "SSE.Views.Toolbar.textPrint": "Imprimir", "SSE.Views.Toolbar.textPrintGridlines": "Imprimir linhas de grade", "SSE.Views.Toolbar.textPrintHeadings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPrintOptions": "Definições de impressão", "SSE.Views.Toolbar.textRegistered": "Registered Sign", "SSE.Views.Toolbar.textResetPageBreak": "<PERSON><PERSON> to<PERSON> as quebra<PERSON> de p<PERSON><PERSON>a", "SSE.Views.Toolbar.textRight": "Direita: ", "SSE.Views.Toolbar.textRightBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textRotateDown": "Girar Texto para Baixo", "SSE.Views.Toolbar.textRotateUp": "Girar Texto para Cima", "SSE.Views.Toolbar.textRtlSheet": "Sheet Right-to-left", "SSE.Views.Toolbar.textScale": "Redimensionar", "SSE.Views.Toolbar.textScaleCustom": "Personalizado", "SSE.Views.Toolbar.textSection": "Section Sign", "SSE.Views.Toolbar.textSelection": "Da seleção atual", "SSE.Views.Toolbar.textSeries": "Series", "SSE.Views.Toolbar.textSetPrintArea": "Definir Área de Impressão", "SSE.Views.Toolbar.textShapesCombine": "Combine", "SSE.Views.Toolbar.textShapesFragment": "Fragment", "SSE.Views.Toolbar.textShapesIntersect": "Intersect", "SSE.Views.Toolbar.textShapesSubstract": "Subtract", "SSE.Views.Toolbar.textShapesUnion": "Union", "SSE.Views.Toolbar.textShowVA": "Mostrar área visível", "SSE.Views.Toolbar.textSmile": "White Smiling Face", "SSE.Views.Toolbar.textSquareRoot": "Square Root", "SSE.Views.Toolbar.textStrikeout": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSubscript": "Subscrito", "SSE.Views.Toolbar.textSubSuperscript": "Subscrito/Sobrescrito", "SSE.Views.Toolbar.textSuperscript": "Sobrescrito", "SSE.Views.Toolbar.textTabCollaboration": "Colaboração", "SSE.Views.Toolbar.textTabData": "Data", "SSE.Views.Toolbar.textTabDraw": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFile": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabHome": "Base", "SSE.Views.Toolbar.textTabInsert": "Inserir", "SSE.Views.Toolbar.textTabLayout": "Disposição", "SSE.Views.Toolbar.textTabProtect": "Proteção", "SSE.Views.Toolbar.textTabView": "<PERSON>er", "SSE.Views.Toolbar.textThisPivot": "A partir deste pivot", "SSE.Views.Toolbar.textThisSheet": "A partir desta folha de cálculo", "SSE.Views.Toolbar.textThisTable": "A partir deste pivot", "SSE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTop": "Parte superior: ", "SSE.Views.Toolbar.textTopBorders": "Bordas superiores", "SSE.Views.Toolbar.textTradeMark": "Trade Mark Sign", "SSE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textUp": "Up", "SSE.Views.Toolbar.textVertical": "Texto vertical", "SSE.Views.Toolbar.textWidth": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textYen": "Yen Sign", "SSE.Views.Toolbar.textZoom": "Ampliação", "SSE.Views.Toolbar.tipAlignBottom": "<PERSON><PERSON><PERSON> em baixo", "SSE.Views.Toolbar.tipAlignCenter": "Alinhar ao centro", "SSE.Views.Toolbar.tipAlignJust": "Justificado", "SSE.Views.Toolbar.tipAlignLeft": "Alinhar à esquerda", "SSE.Views.Toolbar.tipAlignMiddle": "<PERSON><PERSON>ar ao meio", "SSE.Views.Toolbar.tipAlignRight": "Alinhar à direita", "SSE.Views.Toolbar.tipAlignTop": "Alinhar em cima", "SSE.Views.Toolbar.tipAutofilter": "Classificar e Filtrar", "SSE.Views.Toolbar.tipBack": "Voltar", "SSE.Views.Toolbar.tipBorders": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCellStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipChangeCase": "Change case", "SSE.Views.Toolbar.tipChangeChart": "Alterar tipo de gráfico", "SSE.Views.Toolbar.tipClearStyle": "Limpar", "SSE.Views.Toolbar.tipColorSchemas": "Alterar esquema de cor", "SSE.Views.Toolbar.tipCondFormat": "Formatação condicional", "SSE.Views.Toolbar.tipCopy": "Copiar", "SSE.Views.Toolbar.tipCopyStyle": "<PERSON><PERSON><PERSON> est<PERSON>", "SSE.Views.Toolbar.tipCut": "Cortar", "SSE.Views.Toolbar.tipDecDecimal": "<PERSON><PERSON><PERSON>r casas de<PERSON>is", "SSE.Views.Toolbar.tipDecFont": "Di<PERSON><PERSON>r tamanho do tipo de letra", "SSE.Views.Toolbar.tipDeleteOpt": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipDigStyleAccounting": "Estilo contabil<PERSON>", "SSE.Views.Toolbar.tipDigStyleComma": "Comma style", "SSE.Views.Toolbar.tipDigStyleCurrency": "<PERSON><PERSON><PERSON> moe<PERSON>", "SSE.Views.Toolbar.tipDigStylePercent": "Estilo de porcentagem", "SSE.Views.Toolbar.tipEditChart": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipEditChartData": "Selecionar dados", "SSE.Views.Toolbar.tipEditChartType": "Alterar tipo de gráfico", "SSE.Views.Toolbar.tipEditHeader": "Editar cabeçalho e rodapé", "SSE.Views.Toolbar.tipFontColor": "Cor do tipo de letra", "SSE.Views.Toolbar.tipFontName": "<PERSON><PERSON><PERSON> de letra", "SSE.Views.Toolbar.tipFontSize": "Tamanho do tipo de letra", "SSE.Views.Toolbar.tipHAlighOle": "Alinhamento horizontal", "SSE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON><PERSON> ob<PERSON>", "SSE.Views.Toolbar.tipImgGroup": "Agrupar objetos", "SSE.Views.Toolbar.tipIncDecimal": "Aumentar números decimais", "SSE.Views.Toolbar.tipIncFont": "Aumentar tamanho do tipo de letra", "SSE.Views.Toolbar.tipInsertChart": "Inserir g<PERSON>", "SSE.Views.Toolbar.tipInsertChartRecommend": "Insert recommended chart", "SSE.Views.Toolbar.tipInsertChartSpark": "Inserir g<PERSON>", "SSE.Views.Toolbar.tipInsertEquation": "Inserir equação", "SSE.Views.Toolbar.tipInsertHorizontalText": "Inserir caixa de texto horizontal", "SSE.Views.Toolbar.tipInsertHyperlink": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertImage": "Inserir imagem", "SSE.Views.Toolbar.tipInsertOpt": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipInsertShape": "Inserir forma automática", "SSE.Views.Toolbar.tipInsertSlicer": "Inserir segmentação de dados", "SSE.Views.Toolbar.tipInsertSmartArt": "Insert SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Inserir sparkline", "SSE.Views.Toolbar.tipInsertSymbol": "Inserir sí<PERSON>lo", "SSE.Views.Toolbar.tipInsertTable": "<PERSON><PERSON><PERSON> tabela", "SSE.Views.Toolbar.tipInsertText": "Inserir caixa de texto", "SSE.Views.Toolbar.tipInsertTextart": "Inserir arte de texto", "SSE.Views.Toolbar.tipInsertVerticalText": "Inserir caixa de texto vertical", "SSE.Views.Toolbar.tipMerge": "Unir e centrar", "SSE.Views.Toolbar.tipNone": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipNumFormat": "Formato de número", "SSE.Views.Toolbar.tipPageBreak": "Add a break where you want the next page to begin in the printed copy", "SSE.Views.Toolbar.tipPageMargins": "Margens da página", "SSE.Views.Toolbar.tipPageOrient": "Orientação da página", "SSE.Views.Toolbar.tipPageSize": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPaste": "Colar", "SSE.Views.Toolbar.tipPrColor": "<PERSON><PERSON> <PERSON> pre<PERSON>", "SSE.Views.Toolbar.tipPrint": "Imprimir", "SSE.Views.Toolbar.tipPrintArea": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipPrintQuick": "Quick print", "SSE.Views.Toolbar.tipPrintTitles": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipReplace": "Replace", "SSE.Views.Toolbar.tipRtlSheet": "Switch the sheet direction so that the first column is on the right side", "SSE.Views.Toolbar.tipSave": "Guardar", "SSE.Views.Toolbar.tipSaveCoauth": "<PERSON>e as suas alterações para que os outros utilizadores as possam ver.", "SSE.Views.Toolbar.tipScale": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipSelectAll": "Selecionar tudo", "SSE.Views.Toolbar.tipSendBackward": "Enviar para trás", "SSE.Views.Toolbar.tipSendForward": "Trazer para a frente", "SSE.Views.Toolbar.tipShapesMerge": "Merge shapes", "SSE.Views.Toolbar.tipSynchronize": "O documento foi alterado por outro utilizador. Clique para guardar as suas alterações e recarregar o documento.", "SSE.Views.Toolbar.tipTextFormatting": "Mais ferramentas de formatação de texto", "SSE.Views.Toolbar.tipTextOrientation": "Orientação", "SSE.Views.Toolbar.tipUndo": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipVAlighOle": "Alinhamento vertical", "SSE.Views.Toolbar.tipVisibleArea": "<PERSON><PERSON> v<PERSON>", "SSE.Views.Toolbar.tipWrap": "<PERSON><PERSON><PERSON> texto", "SSE.Views.Toolbar.txtAccounting": "Contabilidade", "SSE.Views.Toolbar.txtAdditional": "Adicional", "SSE.Views.Toolbar.txtAscending": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtAutosumTip": "So<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCellStyle": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtClearAll": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtClearComments": "Comentários", "SSE.Views.Toolbar.txtClearFilter": "Limpar filtro", "SSE.Views.Toolbar.txtClearFormat": "Formato", "SSE.Views.Toolbar.txtClearFormula": "Função", "SSE.Views.Toolbar.txtClearHyper": "Hiperligações", "SSE.Views.Toolbar.txtClearText": "Texto", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtCustom": "Personalizar", "SSE.Views.Toolbar.txtDate": "Data", "SSE.Views.Toolbar.txtDateLong": "Data completa", "SSE.Views.Toolbar.txtDateShort": "Data Curta", "SSE.Views.Toolbar.txtDateTime": "Data e hora", "SSE.Views.Toolbar.txtDescending": "Decrescente", "SSE.Views.Toolbar.txtDollar": "$ Dólar", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Exponencial", "SSE.Views.Toolbar.txtFillNum": "Fill", "SSE.Views.Toolbar.txtFilter": "Filtro", "SSE.Views.Toolbar.txtFormula": "Inserir função", "SSE.Views.Toolbar.txtFraction": "Fração", "SSE.Views.Toolbar.txtFranc": "Franco suíço CHF", "SSE.Views.Toolbar.txtGeneral": "G<PERSON>", "SSE.Views.Toolbar.txtInteger": "Inteiro", "SSE.Views.Toolbar.txtManageRange": "Gestor de nome", "SSE.Views.Toolbar.txtMergeAcross": "Mesclar através", "SSE.Views.Toolbar.txtMergeCells": "Mesclar célu<PERSON>", "SSE.Views.Toolbar.txtMergeCenter": "Mesclar e Centrar", "SSE.Views.Toolbar.txtNamedRange": "Intervalos nomeados", "SSE.Views.Toolbar.txtNewRange": "Definir nome", "SSE.Views.Toolbar.txtNoBorders": "<PERSON><PERSON> bordas", "SSE.Views.Toolbar.txtNumber": "Número", "SSE.Views.Toolbar.txtPasteRange": "Colar nome", "SSE.Views.Toolbar.txtPercentage": "Porcentagem", "SSE.Views.Toolbar.txtPound": "£ Libra", "SSE.Views.Toolbar.txtRouble": "₽ <PERSON><PERSON>lo", "SSE.Views.Toolbar.txtScientific": "Científico", "SSE.Views.Toolbar.txtSearch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSort": "Classificar", "SSE.Views.Toolbar.txtSortAZ": "Classificar do menor para o maior", "SSE.Views.Toolbar.txtSortZA": "Classificar do maior para o menor", "SSE.Views.Toolbar.txtSpecial": "Especial", "SSE.Views.Toolbar.txtTableTemplate": "Formatar como modelo de tabela", "SSE.Views.Toolbar.txtText": "Texto", "SSE.Views.Toolbar.txtTime": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtUnmerge": "Desfaz a mesclagem de células", "SSE.Views.Toolbar.txtYen": "¥ Yen", "SSE.Views.Top10FilterDialog.textType": "Mostrar", "SSE.Views.Top10FilterDialog.txtBottom": "Baixo", "SSE.Views.Top10FilterDialog.txtBy": "por", "SSE.Views.Top10FilterDialog.txtItems": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtPercent": "Percentagem", "SSE.Views.Top10FilterDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtTitle": "Filtro automático dos 10 Mais", "SSE.Views.Top10FilterDialog.txtTop": "Parte superior", "SSE.Views.Top10FilterDialog.txtValueTitle": "Filtro Top 10", "SSE.Views.ValueFieldSettingsDialog.textNext": "(se<PERSON><PERSON>)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "Number format", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(anterior)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Definições de campo de valor", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Média", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Campo base", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Item base", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 de %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Contagem", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Contar nú<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Nome personalizado", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "A Diferença em Relação a", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Máx", "SSE.Views.ValueFieldSettingsDialog.txtMin": "<PERSON>.", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "Percentagem de", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Diferença de Percentagem em Relação a", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "Percentagem da Coluna", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% of grand total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% of parent total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% of parent column total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% of parent row total", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% running total in", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Produ<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Rank smallest to largest", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Rank largest to smallest", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Total Corrente Em", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Mostrar valores como", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Nome da origem:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "<PERSON>v.<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "Desv.Padr<PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtSum": "<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Su<PERSON><PERSON>r valor do campo por", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.guestText": "Visitante", "SSE.Views.ViewManagerDlg.lockText": "Bloqueada", "SSE.Views.ViewManagerDlg.textDelete": "Eliminar", "SSE.Views.ViewManagerDlg.textDuplicate": "Duplicar", "SSE.Views.ViewManagerDlg.textEmpty": "Ainda não criou qualquer vista", "SSE.Views.ViewManagerDlg.textGoTo": "Ir para a vista", "SSE.Views.ViewManagerDlg.textLongName": "Introduza um nome com menos de 128 caracteres.", "SSE.Views.ViewManagerDlg.textNew": "Novo", "SSE.Views.ViewManagerDlg.textRename": "<PERSON>dar nome", "SSE.Views.ViewManagerDlg.textRenameError": "O nome da vista não pode estar vazio.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Mudar nome da vista", "SSE.Views.ViewManagerDlg.textViews": "Vistas de folhas", "SSE.Views.ViewManagerDlg.tipIsLocked": "Este elemento está a ser editado por outro utilizador.", "SSE.Views.ViewManagerDlg.txtTitle": "Mostrar gestor de vistas", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Are you sure you want to delete this sheet view?", "SSE.Views.ViewManagerDlg.warnDeleteView": "Está a tentar eliminar a vista atualmente ativa: %1.<br><PERSON><PERSON>r vista e eliminar?", "SSE.Views.ViewTab.capBtnFreeze": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.capBtnSheetView": "Vista de folha", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Mostrar sempre a barra de ferramentas", "SSE.Views.ViewTab.textClose": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "<PERSON><PERSON><PERSON><PERSON> as barras da folha e de estado", "SSE.Views.ViewTab.textCreate": "Novo", "SSE.Views.ViewTab.textDefault": "Padrão", "SSE.Views.ViewTab.textFill": "Fill", "SSE.Views.ViewTab.textFormula": "Barra de fórmulas", "SSE.Views.ViewTab.textFreezeCol": "Fixar a Primeira Coluna", "SSE.Views.ViewTab.textFreezeRow": "Fixar primeira linha", "SSE.Views.ViewTab.textGridlines": "<PERSON><PERSON>", "SSE.Views.ViewTab.textHeadings": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textInterfaceTheme": "<PERSON><PERSON>", "SSE.Views.ViewTab.textLeftMenu": "<PERSON><PERSON>", "SSE.Views.ViewTab.textLine": "Line", "SSE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "Gestor de vistas", "SSE.Views.ViewTab.textRightMenu": "<PERSON><PERSON> dire<PERSON>", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Mostrar sombra dos painéis fixados", "SSE.Views.ViewTab.textTabStyle": "Tab style", "SSE.Views.ViewTab.textUnFreeze": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textZeros": "Mostrar zeros", "SSE.Views.ViewTab.textZoom": "Ampliação", "SSE.Views.ViewTab.tipClose": "Fechar vista de folha", "SSE.Views.ViewTab.tipCreate": "Criar vista de folha", "SSE.Views.ViewTab.tipFreeze": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.tipInterfaceTheme": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipSheetView": "Vista de folha", "SSE.Views.ViewTab.tipViewNormal": "Ver o documento em Vista normal", "SSE.Views.ViewTab.tipViewPageBreak": "<PERSON><PERSON><PERSON>de a<PERSON> as quebra<PERSON> de página quando o documento for impresso", "SSE.Views.ViewTab.txtViewNormal": "Normal", "SSE.Views.ViewTab.txtViewPageBreak": "Pré-visualização da quebra de página", "SSE.Views.WatchDialog.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textAdd": "Adicionar observação", "SSE.Views.WatchDialog.textBook": "<PERSON><PERSON>", "SSE.Views.WatchDialog.textCell": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textDelete": "Eliminar observação", "SSE.Views.WatchDialog.textDeleteAll": "Eliminar tudo", "SSE.Views.WatchDialog.textFormula": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textName": "Nome", "SSE.Views.WatchDialog.textSheet": "Fol<PERSON>", "SSE.Views.WatchDialog.textValue": "Valor", "SSE.Views.WatchDialog.txtTitle": "Janela de observação", "SSE.Views.WBProtection.hintAllowRanges": "<PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.hintProtectRange": "Protect range", "SSE.Views.WBProtection.hintProtectSheet": "<PERSON>te<PERSON> folha", "SSE.Views.WBProtection.hintProtectWB": "Proteger livro", "SSE.Views.WBProtection.txtAllowRanges": "<PERSON><PERSON><PERSON>", "SSE.Views.WBProtection.txtHiddenFormula": "Fórmulas ocultas", "SSE.Views.WBProtection.txtLockedCell": "<PERSON><PERSON><PERSON><PERSON> blo<PERSON>", "SSE.Views.WBProtection.txtLockedShape": "Forma bloqueada", "SSE.Views.WBProtection.txtLockedText": "Bloquear Texto", "SSE.Views.WBProtection.txtProtectRange": "Protect Range", "SSE.Views.WBProtection.txtProtectSheet": "<PERSON>te<PERSON> folha", "SSE.Views.WBProtection.txtProtectWB": "Proteger livro", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Introduza uma palavra-passe para desbloquear a folha", "SSE.Views.WBProtection.txtSheetUnlockTitle": "<PERSON><PERSON><PERSON><PERSON> folha", "SSE.Views.WBProtection.txtWBUnlockDescription": "Introduza uma palavra-passe para desbloquear o livro", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}