{"cancelButtonText": "Annuler", "Common.Controllers.Chat.notcriticalErrorTitle": "Avertissement", "Common.Controllers.Desktop.hintBtnHome": "Afficher la Fenêtre Principale", "Common.Controllers.Desktop.itemCreateFromTemplate": "<PERSON><PERSON>er à partir d'un modèle", "Common.Controllers.History.notcriticalErrorTitle": "Attention", "Common.Controllers.History.txtErrorLoadHistory": "Échec du chargement de l'historique", "Common.Controllers.Plugins.helpUseMacros": "Le bouton Macros se trouve ici", "Common.Controllers.Plugins.helpUseMacrosHeader": "Accès aux macros actualisé", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Les plugins ont été installés avec succès. V<PERSON> pouvez accéder à tous les plugins d'arrière-plan ici.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> est installé avec succès. Vous pouvez accéder à tous les plugins d'arrière-plan ici.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Lancer les plugins installés", "Common.Controllers.Plugins.textRunPlugin": "Lancer le plugin", "Common.define.chartData.textArea": "En aires", "Common.define.chartData.textAreaStacked": "Zone empilée", "Common.define.chartData.textAreaStackedPer": "Aire Empilée 100%", "Common.define.chartData.textBar": "En barre", "Common.define.chartData.textBarNormal": "colonne groupée ", "Common.define.chartData.textBarNormal3d": "Barres groupées en 3D", "Common.define.chartData.textBarNormal3dPerspective": "Colonne 3D", "Common.define.chartData.textBarStacked": "Colonne empi<PERSON>", "Common.define.chartData.textBarStacked3d": "Histogramme empilé en 3D", "Common.define.chartData.textBarStackedPer": "100% colonne empilée", "Common.define.chartData.textBarStackedPer3d": "3-D 100% colonne empilée", "Common.define.chartData.textCharts": "Graphiques", "Common.define.chartData.textColumn": "Colonne", "Common.define.chartData.textColumnSpark": "Histogramme", "Common.define.chartData.textCombo": "Combo", "Common.define.chartData.textComboAreaBar": "Zone empilée - colonne en cluster ", "Common.define.chartData.textComboBarLine": "Colonne - ligne groupée", "Common.define.chartData.textComboBarLineSecondary": "Colonne ligne groupée sur le second axe", "Common.define.chartData.textComboCustom": "Combinaison personnalisée", "Common.define.chartData.textDoughnut": "<PERSON><PERSON>", "Common.define.chartData.textHBarNormal": "barre groupée ", "Common.define.chartData.textHBarNormal3d": "Barres groupées en 3D", "Common.define.chartData.textHBarStacked": "Barre empilée", "Common.define.chartData.textHBarStacked3d": "Barres empilées en 3D", "Common.define.chartData.textHBarStackedPer": "100% barre empilée", "Common.define.chartData.textHBarStackedPer3d": "3-D 100% barre empilée", "Common.define.chartData.textLine": "Graphique en ligne", "Common.define.chartData.textLine3d": "ligne 3D", "Common.define.chartData.textLineMarker": "Ligne avec marqueurs", "Common.define.chartData.textLineSpark": "Ligne", "Common.define.chartData.textLineStacked": "ligne empilée", "Common.define.chartData.textLineStackedMarker": "ligne empilée avec marqueurs", "Common.define.chartData.textLineStackedPer": "100% ligne empilée", "Common.define.chartData.textLineStackedPerMarker": "100% ligne empilée avec marqueurs", "Common.define.chartData.textPie": "Graphiques à secteurs", "Common.define.chartData.textPie3d": "<PERSON>mbert 3D", "Common.define.chartData.textPoint": "Nuages de points (XY)", "Common.define.chartData.textRadar": "Radar", "Common.define.chartData.textRadarFilled": "Radar plein", "Common.define.chartData.textRadarMarker": "Radar avec marqueurs", "Common.define.chartData.textScatter": "Disperser", "Common.define.chartData.textScatterLine": "disperser avec des lignes droites ", "Common.define.chartData.textScatterLineMarker": "disperser avec des lignes droites", "Common.define.chartData.textScatterSmooth": "disperser avec des lignes lisses ", "Common.define.chartData.textScatterSmoothMarker": "disperser avec des lignes lisses et marqueurs", "Common.define.chartData.textSparks": "Graphiques sparkline", "Common.define.chartData.textStock": "<PERSON><PERSON><PERSON>", "Common.define.chartData.textSurface": "Surface", "Common.define.chartData.textWinLossSpark": "Gain/Perte", "Common.define.conditionalData.exampleText": "AaBbCcYyZz", "Common.define.conditionalData.noFormatText": "Aucun format défini", "Common.define.conditionalData.text1Above": "1 écart-type au-dessus de la moyenne", "Common.define.conditionalData.text1Below": "1 écart-type en dessous de la moyenne", "Common.define.conditionalData.text2Above": "2 écarts types au-dessus de la moyenne", "Common.define.conditionalData.text2Below": "2 écarts types en dessous de la moyenne", "Common.define.conditionalData.text3Above": "3 écarts types au-dessus de la moyenne", "Common.define.conditionalData.text3Below": "3 écarts types en dessous de la moyenne", "Common.define.conditionalData.textAbove": "Au-dessus", "Common.define.conditionalData.textAverage": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textBegins": "Commence par", "Common.define.conditionalData.textBelow": "En dessous", "Common.define.conditionalData.textBetween": "<PERSON><PERSON>", "Common.define.conditionalData.textBlank": "Vide", "Common.define.conditionalData.textBlanks": "Contient les vides", "Common.define.conditionalData.textBottom": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textContains": "Contient", "Common.define.conditionalData.textDataBar": "Barre de donn<PERSON>", "Common.define.conditionalData.textDate": "Date", "Common.define.conditionalData.textDuplicate": "Doublon", "Common.define.conditionalData.textEnds": "Se termine par", "Common.define.conditionalData.textEqAbove": "Si égale ou supérieure à", "Common.define.conditionalData.textEqBelow": "<PERSON>gale ou en dessous", "Common.define.conditionalData.textEqual": "Égale à", "Common.define.conditionalData.textError": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textErrors": "Contient les erreurs", "Common.define.conditionalData.textFormula": "Formule", "Common.define.conditionalData.textGreater": "Supérieure à", "Common.define.conditionalData.textGreaterEq": "Sup<PERSON>ure ou égale à", "Common.define.conditionalData.textIconSets": "<PERSON>ux d<PERSON>ic<PERSON>s", "Common.define.conditionalData.textLast7days": "Pendant les 7 derniers jours", "Common.define.conditionalData.textLastMonth": "<PERSON><PERSON>", "Common.define.conditionalData.textLastWeek": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textLess": "Inférieure à", "Common.define.conditionalData.textLessEq": "Inférieure ou égale à", "Common.define.conditionalData.textNextMonth": "<PERSON><PERSON> suivant", "Common.define.conditionalData.textNextWeek": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textNotBetween": "<PERSON><PERSON> entre", "Common.define.conditionalData.textNotBlanks": "Ne contient pas de vides", "Common.define.conditionalData.textNotContains": "Ne contient pas", "Common.define.conditionalData.textNotEqual": "N'est pas égale à", "Common.define.conditionalData.textNotErrors": "Ne contient pas d'erreurs", "Common.define.conditionalData.textText": "Texte", "Common.define.conditionalData.textThisMonth": "<PERSON> mois", "Common.define.conditionalData.textThisWeek": "<PERSON><PERSON> se<PERSON>", "Common.define.conditionalData.textToday": "<PERSON><PERSON><PERSON>'hui", "Common.define.conditionalData.textTomorrow": "<PERSON><PERSON><PERSON>", "Common.define.conditionalData.textTop": "Premiers", "Common.define.conditionalData.textUnique": "Unique", "Common.define.conditionalData.textValue": "Valeur est ", "Common.define.conditionalData.textYesterday": "<PERSON>er", "Common.define.smartArt.textAccentedPicture": "Image accentuée", "Common.define.smartArt.textAccentProcess": "Processus accentué", "Common.define.smartArt.textAlternatingFlow": "Flux interactif", "Common.define.smartArt.textAlternatingHexagons": "Hexagones alternés", "Common.define.smartArt.textAlternatingPictureBlocks": "Blocs d'images alternées", "Common.define.smartArt.textAlternatingPictureCircles": "Cercles d'images alternées", "Common.define.smartArt.textArchitectureLayout": "Disposition architecture", "Common.define.smartArt.textArrowRibbon": "<PERSON><PERSON><PERSON> fl<PERSON>", "Common.define.smartArt.textAscendingPictureAccentProcess": "Processus accentué d'images dans un ordre croissant", "Common.define.smartArt.textBalance": "Balance", "Common.define.smartArt.textBasicBendingProcess": "Processus en lacet simple", "Common.define.smartArt.textBasicBlockList": "Liste de blocs simple", "Common.define.smartArt.textBasicChevronProcess": "Processus simple en chevrons", "Common.define.smartArt.textBasicCycle": "Cycle simple", "Common.define.smartArt.textBasicMatrix": "Matrice simple", "Common.define.smartArt.textBasicPie": "Graphique en secteurs simple", "Common.define.smartArt.textBasicProcess": "Processus simple", "Common.define.smartArt.textBasicPyramid": "Pyramide simple", "Common.define.smartArt.textBasicRadial": "Radial simple", "Common.define.smartArt.textBasicTarget": "Cible simple", "Common.define.smartArt.textBasicTimeline": "Chronologie simple", "Common.define.smartArt.textBasicVenn": "Venn simple", "Common.define.smartArt.textBendingPictureAccentList": "Liste accentuée en lacet avec images", "Common.define.smartArt.textBendingPictureBlocks": "Blocs en lacet avec images", "Common.define.smartArt.textBendingPictureCaption": "Images en lacet avec légendes", "Common.define.smartArt.textBendingPictureCaptionList": "Liste d’images en lacet avec légendes", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Texte semi-transparent en lacet avec images", "Common.define.smartArt.textBlockCycle": "Cycle en blocs", "Common.define.smartArt.textBubblePictureList": "Liste d’images avec bulles", "Common.define.smartArt.textCaptionedPictures": "Images avec légende", "Common.define.smartArt.textChevronAccentProcess": "Processus accentué en chevrons", "Common.define.smartArt.textChevronList": "Liste de chevrons", "Common.define.smartArt.textCircleAccentTimeline": "Barre de planning accentuée avec cercles", "Common.define.smartArt.textCircleArrowProcess": "Processus en flèches circulaires", "Common.define.smartArt.textCirclePictureHierarchy": "Hiérarchie avec images rondes", "Common.define.smartArt.textCircleProcess": "Processus en cercles", "Common.define.smartArt.textCircleRelationship": "Relation circulaire", "Common.define.smartArt.textCircularBendingProcess": "Processus en lacets avec bulles", "Common.define.smartArt.textCircularPictureCallout": "Images circulaires avec légende", "Common.define.smartArt.textClosedChevronProcess": "Processus en chevrons fermés", "Common.define.smartArt.textContinuousArrowProcess": "Processus en flèche continue", "Common.define.smartArt.textContinuousBlockProcess": "Processus en bloc continu", "Common.define.smartArt.textContinuousCycle": "Cycle continu", "Common.define.smartArt.textContinuousPictureList": "Liste continue avec images", "Common.define.smartArt.textConvergingArrows": "Flèches convergentes", "Common.define.smartArt.textConvergingRadial": "Radial convergeant", "Common.define.smartArt.textConvergingText": "Texte convergent", "Common.define.smartArt.textCounterbalanceArrows": "Flèches d’équilibrage", "Common.define.smartArt.textCycle": "Cycle", "Common.define.smartArt.textCycleMatrix": "<PERSON>rice de <PERSON>", "Common.define.smartArt.textDescendingBlockList": "Liste de blocs décroissante", "Common.define.smartArt.textDescendingProcess": "<PERSON><PERSON>", "Common.define.smartArt.textDetailedProcess": "<PERSON><PERSON>", "Common.define.smartArt.textDivergingArrows": "Flèches divergentes", "Common.define.smartArt.textDivergingRadial": "Radial convergeant", "Common.define.smartArt.textEquation": "Équation", "Common.define.smartArt.textFramedTextPicture": "Images avec texte en encadré", "Common.define.smartArt.textFunnel": "Entonnoir", "Common.define.smartArt.textGear": "Engrenage", "Common.define.smartArt.textGridMatrix": "Matrice avec grille", "Common.define.smartArt.textGroupedList": "Liste groupée", "Common.define.smartArt.textHalfCircleOrganizationChart": "Organigramme avec demi-cercles", "Common.define.smartArt.textHexagonCluster": "Groupe d’hexagones", "Common.define.smartArt.textHexagonRadial": "Hexagone radial", "Common.define.smartArt.textHierarchy": "Hiéra<PERSON>ie", "Common.define.smartArt.textHierarchyList": "Liste hiérarchique", "Common.define.smartArt.textHorizontalBulletList": "Liste à puces horizontale", "Common.define.smartArt.textHorizontalHierarchy": "Hiérarchie horizontale", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Hiérarchie horizontale avec étiquettes", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Hiérarchie horizontale à plusieurs niveaux", "Common.define.smartArt.textHorizontalOrganizationChart": "Organigramme horizontal", "Common.define.smartArt.textHorizontalPictureList": "Liste horizontale avec images", "Common.define.smartArt.textIncreasingArrowProcess": "Processus en flèches croissant", "Common.define.smartArt.textIncreasingCircleProcess": "Processus ascendant avec cercles", "Common.define.smartArt.textInterconnectedBlockProcess": "Processus en blocs interconnectés", "Common.define.smartArt.textInterconnectedRings": "Anneaux interconnectés", "Common.define.smartArt.textInvertedPyramid": "Pyramide inversée", "Common.define.smartArt.textLabeledHierarchy": "Hiérarchie libellée", "Common.define.smartArt.textLinearVenn": "Venn lin<PERSON>", "Common.define.smartArt.textLinedList": "Liste alignée", "Common.define.smartArt.textList": "Liste", "Common.define.smartArt.textMatrix": "<PERSON><PERSON>", "Common.define.smartArt.textMultidirectionalCycle": "Cycle multidirectionnel", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Organigramme avec titre et nom", "Common.define.smartArt.textNestedTarget": "Cible imbriquée", "Common.define.smartArt.textNondirectionalCycle": "Cycle non directionnel", "Common.define.smartArt.textOpposingArrows": "Flèches opposées", "Common.define.smartArt.textOpposingIdeas": "Idées opposées", "Common.define.smartArt.textOrganizationChart": "Organigramme", "Common.define.smartArt.textOther": "<PERSON><PERSON>", "Common.define.smartArt.textPhasedProcess": "Processus à phases", "Common.define.smartArt.textPicture": "Image", "Common.define.smartArt.textPictureAccentBlocks": "Blocs accentués avec images", "Common.define.smartArt.textPictureAccentList": "Liste accentuée avec images", "Common.define.smartArt.textPictureAccentProcess": "Processus accentué avec images", "Common.define.smartArt.textPictureCaptionList": "Liste de légendes d'images", "Common.define.smartArt.textPictureFrame": "Cadre de l’image", "Common.define.smartArt.textPictureGrid": "Grille d’images", "Common.define.smartArt.textPictureLineup": "Alignement d’images", "Common.define.smartArt.textPictureOrganizationChart": "Organigramme avec images", "Common.define.smartArt.textPictureStrips": "Bandes avec images", "Common.define.smartArt.textPieProcess": "Processus à secteurs", "Common.define.smartArt.textPlusAndMinus": "Plus et moins", "Common.define.smartArt.textProcess": "Processus", "Common.define.smartArt.textProcessArrows": "Processus en flèches", "Common.define.smartArt.textProcessList": "Liste de processus", "Common.define.smartArt.textPyramid": "Pyramide", "Common.define.smartArt.textPyramidList": "Liste pyramidale", "Common.define.smartArt.textRadialCluster": "Groupe en rayon", "Common.define.smartArt.textRadialCycle": "Cycle radial", "Common.define.smartArt.textRadialList": "Liste radiale", "Common.define.smartArt.textRadialPictureList": "Liste radiale avec images", "Common.define.smartArt.textRadialVenn": "Venn radial", "Common.define.smartArt.textRandomToResultProcess": "Processus d’idées aléatoires avec résultat", "Common.define.smartArt.textRelationship": "Relation", "Common.define.smartArt.textRepeatingBendingProcess": "Processus en lacets", "Common.define.smartArt.textReverseList": "Liste inversée", "Common.define.smartArt.textSegmentedCycle": "Cycle segmenté", "Common.define.smartArt.textSegmentedProcess": "Processus segmenté", "Common.define.smartArt.textSegmentedPyramid": "Pyramide segmentée", "Common.define.smartArt.textSnapshotPictureList": "Liste d’images instantanées", "Common.define.smartArt.textSpiralPicture": "Images en spirale", "Common.define.smartArt.textSquareAccentList": "Liste accentuée avec carrés", "Common.define.smartArt.textStackedList": "Liste empilée", "Common.define.smartArt.textStackedVenn": "<PERSON><PERSON><PERSON>", "Common.define.smartArt.textStaggeredProcess": "<PERSON><PERSON>", "Common.define.smartArt.textStepDownProcess": "Processus descendant", "Common.define.smartArt.textStepUpProcess": "Processus ascendant", "Common.define.smartArt.textSubStepProcess": "Processus avec sous-étapes", "Common.define.smartArt.textTabbedArc": "Arc à onglets", "Common.define.smartArt.textTableHierarchy": "Hiérarchie de tables", "Common.define.smartArt.textTableList": "Liste de tables", "Common.define.smartArt.textTabList": "Liste des onglets", "Common.define.smartArt.textTargetList": "Liste cible", "Common.define.smartArt.textTextCycle": "Cycle de texte", "Common.define.smartArt.textThemePictureAccent": "Images de thème accentué", "Common.define.smartArt.textThemePictureAlternatingAccent": "Images de thème alternées accentué", "Common.define.smartArt.textThemePictureGrid": "Grille d’images de thème", "Common.define.smartArt.textTitledMatrix": "<PERSON><PERSON> l<PERSON>", "Common.define.smartArt.textTitledPictureAccentList": "Liste accentuée avec images et titre", "Common.define.smartArt.textTitledPictureBlocks": "Blocs d’images avec titre", "Common.define.smartArt.textTitlePictureLineup": "Alignement d’images avec titre", "Common.define.smartArt.textTrapezoidList": "Liste trapézo<PERSON>dale", "Common.define.smartArt.textUpwardArrow": "Flèche vers le haut", "Common.define.smartArt.textVaryingWidthList": "Liste à largeur variable", "Common.define.smartArt.textVerticalAccentList": "Liste accentuée verticale", "Common.define.smartArt.textVerticalArrowList": "Liste verticale avec flèches", "Common.define.smartArt.textVerticalBendingProcess": "Processus vertical en lacet", "Common.define.smartArt.textVerticalBlockList": "Liste de blocs verticale", "Common.define.smartArt.textVerticalBoxList": "Liste de zones verticale", "Common.define.smartArt.textVerticalBracketList": "Liste de crochets verticale", "Common.define.smartArt.textVerticalBulletList": "Liste à puces verticale", "Common.define.smartArt.textVerticalChevronList": "Liste de chevrons verticale", "Common.define.smartArt.textVerticalCircleList": "Liste de cercles verticale", "Common.define.smartArt.textVerticalCurvedList": "Liste courbe verticale", "Common.define.smartArt.textVerticalEquation": "Équation verticale", "Common.define.smartArt.textVerticalPictureAccentList": "Liste accentuée verticale avec images", "Common.define.smartArt.textVerticalPictureList": "Liste d’images verticale", "Common.define.smartArt.textVerticalProcess": "Processus vertical", "Common.Translation.textMoreButton": "Plus", "Common.Translation.tipFileLocked": "Le document est verrouillé pour l'édition. Vous pouvez apporter des modifications et l'enregistrer comme copie locale ultérieurement.", "Common.Translation.tipFileReadOnly": "Le fichier est disponible en lecture seule. Pour sauvegarder vos modifications, enregistrez le fichier sous un nouveau nom ou à un autre endroit.", "Common.Translation.warnFileLocked": "Ce fichier a été modifié avec une autre application. Vous pouvez continuer à le modifier et l'enregistrer comme une copie.", "Common.Translation.warnFileLockedBtnEdit": "<PERSON><PERSON>er une copie", "Common.Translation.warnFileLockedBtnView": "Ouvrir pour visualisation", "Common.UI.ButtonColored.textAutoColor": "Automatique", "Common.UI.ButtonColored.textEyedropper": "Pipette", "Common.UI.ButtonColored.textNewColor": "Plus de couleurs", "Common.UI.ComboBorderSize.txtNoBorders": "Pas de bordures", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Pas de bordures", "Common.UI.ComboDataView.emptyComboText": "Aucun style", "Common.UI.ExtendedColorDialog.addButtonText": "Ajouter", "Common.UI.ExtendedColorDialog.textCurrent": "Actuelle", "Common.UI.ExtendedColorDialog.textHexErr": "La valeur saisie est incorrecte. <br>Entrez une valeur de 000000 à FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Nouveau", "Common.UI.ExtendedColorDialog.textRGBErr": "La valeur saisie est incorrecte. <br>Entrez une valeur numérique de 0 à 255.", "Common.UI.HSBColorPicker.textNoColor": "<PERSON><PERSON> de couleur", "Common.UI.InputField.txtEmpty": "Ce champ est obligatoire", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Masquer le mot de passe", "Common.UI.InputFieldBtnPassword.textHintHold": "Appuyez et maintenez pour afficher le mot de passe", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Afficher le mot de passe", "Common.UI.SearchBar.textFind": "<PERSON><PERSON><PERSON>", "Common.UI.SearchBar.tipCloseSearch": "<PERSON><PERSON>er la recherche", "Common.UI.SearchBar.tipNextResult": "Résultat suivant", "Common.UI.SearchBar.tipOpenAdvancedSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON> les paramètres avancés", "Common.UI.SearchBar.tipPreviousResult": "Résultat précédent", "Common.UI.SearchDialog.textHighlight": "Surligner les résultats", "Common.UI.SearchDialog.textMatchCase": "Re<PERSON>er la casse", "Common.UI.SearchDialog.textReplaceDef": "Saisis<PERSON>z le texte de remplacement", "Common.UI.SearchDialog.textSearchStart": "Entrez votre texte ici", "Common.UI.SearchDialog.textTitle": "Re<PERSON><PERSON> et remplacer", "Common.UI.SearchDialog.textTitle2": "<PERSON><PERSON><PERSON>", "Common.UI.SearchDialog.textWholeWords": "Seulement les mots entiers", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "<PERSON><PERSON>lace<PERSON>", "Common.UI.SearchDialog.txtBtnReplaceAll": "Remplacer tout", "Common.UI.SynchronizeTip.textDontShow": "Ne plus afficher ce message", "Common.UI.SynchronizeTip.textGotIt": "OK", "Common.UI.SynchronizeTip.textSynchronize": "Le document a été modifié par un autre utilisateur.<br>Cliquez pour enregistrer vos modifications et recharger les mises à jour.", "Common.UI.ThemeColorPalette.textRecentColors": "Couleurs récentes", "Common.UI.ThemeColorPalette.textStandartColors": "Couleurs standard", "Common.UI.ThemeColorPalette.textThemeColors": "Couleurs thème", "Common.UI.Themes.txtThemeClassicLight": "Classique clair", "Common.UI.Themes.txtThemeContrastDark": "Contraste élevé sombre", "Common.UI.Themes.txtThemeDark": "Sombre", "Common.UI.Themes.txtThemeGray": "<PERSON><PERSON>", "Common.UI.Themes.txtThemeLight": "<PERSON>", "Common.UI.Themes.txtThemeSystem": "Identique à système", "Common.UI.Window.cancelButtonText": "Annuler", "Common.UI.Window.closeButtonText": "<PERSON><PERSON><PERSON>", "Common.UI.Window.noButtonText": "Non", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmation", "Common.UI.Window.textDontShow": "Ne plus afficher ce message", "Common.UI.Window.textError": "<PERSON><PERSON><PERSON>", "Common.UI.Window.textInformation": "Informations", "Common.UI.Window.textWarning": "Avertissement", "Common.UI.Window.yesButtonText": "O<PERSON>", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Accent", "Common.Utils.ThemeColor.txtAqua": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtbackground": "Arrière-plan", "Common.Utils.ThemeColor.txtBlack": "Noir", "Common.Utils.ThemeColor.txtBlue": "Bleu", "Common.Utils.ThemeColor.txtBrightGreen": "Vert brillant", "Common.Utils.ThemeColor.txtBrown": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtDarkBlue": "<PERSON><PERSON>u foncé", "Common.Utils.ThemeColor.txtDarker": "Plus sombre", "Common.Utils.ThemeColor.txtDarkGray": "<PERSON><PERSON> fon<PERSON>", "Common.Utils.ThemeColor.txtDarkGreen": "<PERSON><PERSON> fon<PERSON>", "Common.Utils.ThemeColor.txtDarkPurple": "<PERSON> foncé", "Common.Utils.ThemeColor.txtDarkRed": "Rouge foncé", "Common.Utils.ThemeColor.txtDarkTeal": "Bleu-vert foncé", "Common.Utils.ThemeColor.txtDarkYellow": "Jaune foncé", "Common.Utils.ThemeColor.txtGold": "Or", "Common.Utils.ThemeColor.txtGray": "<PERSON><PERSON>", "Common.Utils.ThemeColor.txtGreen": "<PERSON>ert", "Common.Utils.ThemeColor.txtIndigo": "Indigo", "Common.Utils.ThemeColor.txtLavender": "<PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtLightBlue": "<PERSON><PERSON>u clair", "Common.Utils.ThemeColor.txtLighter": "Plus clair", "Common.Utils.ThemeColor.txtLightGray": "<PERSON><PERSON> clair", "Common.Utils.ThemeColor.txtLightGreen": "Vert clair", "Common.Utils.ThemeColor.txtLightOrange": "Orange clair", "Common.Utils.ThemeColor.txtLightYellow": "<PERSON><PERSON>ne clair", "Common.Utils.ThemeColor.txtOrange": "Orange", "Common.Utils.ThemeColor.txtPink": "<PERSON>", "Common.Utils.ThemeColor.txtPurple": "Violet", "Common.Utils.ThemeColor.txtRed": "Rouge", "Common.Utils.ThemeColor.txtRose": "<PERSON>", "Common.Utils.ThemeColor.txtSkyBlue": "Bleu ciel", "Common.Utils.ThemeColor.txtTeal": "Bleu-vert", "Common.Utils.ThemeColor.txttext": "Texte", "Common.Utils.ThemeColor.txtTurquosie": "Turquoise", "Common.Utils.ThemeColor.txtViolet": "Violet", "Common.Utils.ThemeColor.txtWhite": "<PERSON>", "Common.Utils.ThemeColor.txtYellow": "Jaune", "Common.Views.About.txtAddress": "adresse: ", "Common.Views.About.txtLicensee": "CESSIONNAIRE", "Common.Views.About.txtLicensor": "CONCÉDANT", "Common.Views.About.txtMail": "e-mail: ", "Common.Views.About.txtPoweredBy": "Réalisation", "Common.Views.About.txtTel": "tél.: ", "Common.Views.About.txtVersion": "Version ", "Common.Views.AutoCorrectDialog.textAdd": "Ajouter", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Applique pendant le travail", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Correction automatique", "Common.Views.AutoCorrectDialog.textAutoFormat": "Mise en forme automatique au cours de la frappe", "Common.Views.AutoCorrectDialog.textBy": "Par", "Common.Views.AutoCorrectDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textHyperlink": "Adresses Internet et réseau avec des liens hypertexte", "Common.Views.AutoCorrectDialog.textMathCorrect": "AutoMaths", "Common.Views.AutoCorrectDialog.textNewRowCol": "Inclure nouvelles lignes et colonnes dans le tableau", "Common.Views.AutoCorrectDialog.textRecognized": "Fonctions reconnues", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Les expressions suivantes sont les expressions mathématiques reconnues. Elles ne seront pas mises en italique automatiquement.", "Common.Views.AutoCorrectDialog.textReplace": "<PERSON><PERSON>lace<PERSON>", "Common.Views.AutoCorrectDialog.textReplaceText": "Remplacer pendant la frappe", "Common.Views.AutoCorrectDialog.textReplaceType": "Remp<PERSON><PERSON> le texte au cours de la frappe", "Common.Views.AutoCorrectDialog.textReset": "Réinitialiser", "Common.Views.AutoCorrectDialog.textResetAll": "Rétablir paramètres par défaut", "Common.Views.AutoCorrectDialog.textRestore": "<PERSON><PERSON><PERSON>", "Common.Views.AutoCorrectDialog.textTitle": "Correction automatique", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Fonctions reconnues doivent contenir seulement des lettres de A à Z, majuscules et minuscules.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Toute expression que vous avez ajoutée sera supprimée et toute expression que vous avez supprimée sera restaurée. Voulez-vous continuer ?", "Common.Views.AutoCorrectDialog.warnReplace": "Ce symbol d'autocorrection pour 1% existe déjà. Voulez-vous le remplacer ?", "Common.Views.AutoCorrectDialog.warnReset": "Toutes les autocorrections que vous avez ajouté seront supprimées et celles que vous avez modifiées seront réinitialisées à leurs valeurs originales. Voulez-vous continuer ?", "Common.Views.AutoCorrectDialog.warnRestore": "Ce symbol d'autocorrection pour 1% sera réinitialisé à sa valeur initiale. Voulez-vous continuer ?", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "<PERSON><PERSON><PERSON> le chat", "Common.Views.Chat.textEnterMessage": "Entrez votre message ici", "Common.Views.Chat.textSend": "Envoyer", "Common.Views.Comments.mniAuthorAsc": "Auteur de A à Z", "Common.Views.Comments.mniAuthorDesc": "Auteur de Z à A", "Common.Views.Comments.mniDateAsc": "Plus ancien", "Common.Views.Comments.mniDateDesc": "Plus récent", "Common.Views.Comments.mniFilterGroups": "Filtrer par groupe", "Common.Views.Comments.mniPositionAsc": "Du haut", "Common.Views.Comments.mniPositionDesc": "Du bas", "Common.Views.Comments.textAdd": "Ajouter", "Common.Views.Comments.textAddComment": "Ajouter", "Common.Views.Comments.textAddCommentToDoc": "Ajouter un commentaire au document", "Common.Views.Comments.textAddReply": "Ajouter une réponse", "Common.Views.Comments.textAll": "<PERSON>ut", "Common.Views.Comments.textAnonym": "Invi<PERSON>", "Common.Views.Comments.textCancel": "Annuler", "Common.Views.Comments.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.Comments.textClosePanel": "<PERSON><PERSON><PERSON> les commentaires", "Common.Views.Comments.textComment": "Commentaire", "Common.Views.Comments.textComments": "Commentaires", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Entrez votre commentaire ici", "Common.Views.Comments.textHintAddComment": "Ajouter un commentaire", "Common.Views.Comments.textOpenAgain": "Ouvrir à nouveau", "Common.Views.Comments.textReply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Comments.textResolved": "R<PERSON>ol<PERSON>", "Common.Views.Comments.textSort": "Trier les commentaires", "Common.Views.Comments.textSortFilter": "Tri et filtrage des commentaires", "Common.Views.Comments.textSortFilterMore": "Tri, filtrage et autres", "Common.Views.Comments.textSortMore": "Tri et autres", "Common.Views.Comments.textViewResolved": "Vous n'avez pas la permission de rouvrir le commentaire", "Common.Views.Comments.txtEmpty": "Il n'y a pas de commentaires dans la feuille.", "Common.Views.CopyWarningDialog.textDontShow": "Ne plus afficher ce message", "Common.Views.CopyWarningDialog.textMsg": "Vous pouvez réaliser les actions de copier, couper et coller en utilisant les boutons de la barre d'outils et à l'aide du menu contextuel à partir de cet onglet uniquement.<br><br>Pour copier ou coller de / vers les applications en dehors de l'onglet de l'éditeur, utilisez les combinaisons de touches suivantes :", "Common.Views.CopyWarningDialog.textTitle": "Actions copier, couper et coller", "Common.Views.CopyWarningDialog.textToCopy": "pour <PERSON><PERSON>r", "Common.Views.CopyWarningDialog.textToCut": "pour Couper", "Common.Views.CopyWarningDialog.textToPaste": "pour <PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Télécharger", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Cochez les cases en regard des commandes à afficher sur la barre d'outils Accès rapide", "Common.Views.CustomizeQuickAccessDialog.textPrint": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Impression rapide", "Common.Views.CustomizeQuickAccessDialog.textRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.CustomizeQuickAccessDialog.textSave": "Enregistrer", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Personnaliser l'accès rapide", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Annuler", "Common.Views.DocumentAccessDialog.textLoading": "Chargement en cours...", "Common.Views.DocumentAccessDialog.textTitle": "Paramètres de partage", "Common.Views.DocumentPropertyDialog.errorDate": "Vous pouvez choisir une valeur dans le calendrier pour l'enregistrer en tant que date. Si vous saisissez une valeur manuellement, elle sera enregistrée en tant que texte.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "Non", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "O<PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "La propriété doit avoir un titre", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Titre", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "« Oui » ou »Non »", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Date", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Type", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Fournissez un numéro valide", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Texte", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "La propriété doit avoir une valeur", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "<PERSON><PERSON>", "Common.Views.DocumentPropertyDialog.txtTitle": "Nouvelle propriété de document", "Common.Views.Draw.hintEraser": "<PERSON><PERSON>", "Common.Views.Draw.hintSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Draw.txtEraser": "<PERSON><PERSON>", "Common.Views.Draw.txtHighlighter": "Surligneur", "Common.Views.Draw.txtMM": "mm", "Common.Views.Draw.txtPen": "<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Draw.txtSize": "<PERSON><PERSON>", "Common.Views.EditNameDialog.textLabel": "Étiquette :", "Common.Views.EditNameDialog.textLabelError": "Étiquette ne doit pas être vide", "Common.Views.Header.ariaQuickAccessToolbar": "Barre d'outils d'accès rapide", "Common.Views.Header.labelCoUsersDescr": "Le document est en cours de modification par:", "Common.Views.Header.textAddFavorite": "Marquer en tant que favori", "Common.Views.Header.textAdvSettings": "Paramètres avancés", "Common.Views.Header.textBack": "<PERSON>u<PERSON><PERSON>r l'emplacement du fichier", "Common.Views.Header.textClose": "<PERSON><PERSON><PERSON> le <PERSON>er", "Common.Views.Header.textCompactView": "Masquer la barre d'outils", "Common.Views.Header.textHideLines": "Masquer les règles", "Common.Views.Header.textHideStatusBar": "Combiner la barre de la feuille et la barre d'état", "Common.Views.Header.textPrint": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textReadOnly": "Lecture seule", "Common.Views.Header.textRemoveFavorite": "Enlever des favoris", "Common.Views.Header.textSaveBegin": "Enregistrement en cours...", "Common.Views.Header.textSaveChanged": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.textSaveEnd": "Toutes les modifications ont été enregistrées", "Common.Views.Header.textSaveExpander": "Toutes les modifications ont été enregistrées", "Common.Views.Header.textShare": "Partager", "Common.Views.Header.textZoom": "Grossissement", "Common.Views.Header.tipAccessRights": "<PERSON><PERSON>rer les droits d'accès au document", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Personnaliser la barre d'outils Accès rapide", "Common.Views.Header.tipDownload": "Télécharger le fichier", "Common.Views.Header.tipGoEdit": "Modifier le fichier courant", "Common.Views.Header.tipPrint": "<PERSON><PERSON><PERSON><PERSON> le fi<PERSON>er", "Common.Views.Header.tipPrintQuick": "Impression rapide", "Common.Views.Header.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Header.tipSave": "Enregistrer", "Common.Views.Header.tipSearch": "<PERSON><PERSON><PERSON>", "Common.Views.Header.tipUndo": "Annuler", "Common.Views.Header.tipUndock": "Détacher en fenêtre séparée", "Common.Views.Header.tipUsers": "Afficher les utilisateurs", "Common.Views.Header.tipViewSettings": "Paramètres d'affichage", "Common.Views.Header.tipViewUsers": "Afficher les utilisateurs et gérer les droits d'accès aux documents", "Common.Views.Header.txtAccessRights": "Modifier les droits d'accès", "Common.Views.Header.txtRename": "<PERSON>mmer", "Common.Views.History.textCloseHistory": "Fermer l'historique", "Common.Views.History.textHideAll": "Masquer les modifications détaillées", "Common.Views.History.textHighlightDeleted": "Mettre en évidence la suppression", "Common.Views.History.textMore": "Plus", "Common.Views.History.textRestore": "<PERSON><PERSON><PERSON>", "Common.Views.History.textShowAll": "Afficher les modifications détaillées", "Common.Views.History.textVer": "ver.", "Common.Views.History.textVersionHistory": "Historique des versions", "Common.Views.ImageFromUrlDialog.textUrl": "Coller URL d'image", "Common.Views.ImageFromUrlDialog.txtEmpty": "Ce champ est obligatoire", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Ce champ doit contenir une URL au format \"http://www.example.com\"", "Common.Views.ListSettingsDialog.textBulleted": "À puces", "Common.Views.ListSettingsDialog.textFromFile": "À partir du fichier", "Common.Views.ListSettingsDialog.textFromStorage": "À partir de l'espace de stockage", "Common.Views.ListSettingsDialog.textFromUrl": "À partir d'une URL", "Common.Views.ListSettingsDialog.textNumbering": "Numéroté", "Common.Views.ListSettingsDialog.textSelect": "Sélectionner à partir de", "Common.Views.ListSettingsDialog.tipChange": "Changer de puce", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "<PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtImage": "Image", "Common.Views.ListSettingsDialog.txtImport": "Importer", "Common.Views.ListSettingsDialog.txtNewBullet": "Nouvelle puce", "Common.Views.ListSettingsDialog.txtNewImage": "Nouvelle image", "Common.Views.ListSettingsDialog.txtNone": "Aucun", "Common.Views.ListSettingsDialog.txtOfText": "% de texte", "Common.Views.ListSettingsDialog.txtSize": "<PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtStart": "Commencer par", "Common.Views.ListSettingsDialog.txtSymbol": "Symbole", "Common.Views.ListSettingsDialog.txtTitle": "Paramètres de la liste", "Common.Views.ListSettingsDialog.txtType": "Type", "Common.Views.MacrosDialog.textCopy": "<PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textCustomFunction": "Fonction personnalisée", "Common.Views.MacrosDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.MacrosDialog.textLoading": "Chargement...", "Common.Views.MacrosDialog.textMacros": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textMakeAutostart": "Démarrage automatique", "Common.Views.MacrosDialog.textRename": "<PERSON>mmer", "Common.Views.MacrosDialog.textRun": "Lancer", "Common.Views.MacrosDialog.textSave": "Enregistrer", "Common.Views.MacrosDialog.textTitle": "<PERSON><PERSON>", "Common.Views.MacrosDialog.textUnMakeAutostart": "Annuler le démarrage automatique", "Common.Views.MacrosDialog.tipFunctionAdd": "Ajouter une fonction personnalisée", "Common.Views.MacrosDialog.tipMacrosAdd": "Ajouter des macros", "Common.Views.MacrosDialog.tipMacrosRun": "Lancer", "Common.Views.OpenDialog.closeButtonText": "<PERSON><PERSON><PERSON> le <PERSON>er", "Common.Views.OpenDialog.textInvalidRange": "Plage de cellules non valide", "Common.Views.OpenDialog.textSelectData": "Sélectionner des données", "Common.Views.OpenDialog.txtAdvanced": "<PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtColon": "Deux-points", "Common.Views.OpenDialog.txtComma": "Vir<PERSON>le", "Common.Views.OpenDialog.txtDelimiter": "Délimiteur", "Common.Views.OpenDialog.txtDestData": "Sélectionnez où placer les données", "Common.Views.OpenDialog.txtEmpty": "Ce champ est obligatoire", "Common.Views.OpenDialog.txtEncoding": "Codage ", "Common.Views.OpenDialog.txtIncorrectPwd": "Le mot de passe est incorrect.", "Common.Views.OpenDialog.txtOpenFile": "Entrer le mot de passe pour ouvrir le fichier", "Common.Views.OpenDialog.txtOther": "<PERSON><PERSON>", "Common.Views.OpenDialog.txtPassword": "Mot de passe", "Common.Views.OpenDialog.txtPreview": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.OpenDialog.txtProtected": "Une fois le mot de passe saisi et le fichier ouvert, le mot de passe actuel de fichier sera réinitialisé.", "Common.Views.OpenDialog.txtSemicolon": "Point-virgule", "Common.Views.OpenDialog.txtSpace": "Espace", "Common.Views.OpenDialog.txtTab": "Tabulation", "Common.Views.OpenDialog.txtTitle": "Choisir %1 des options ", "Common.Views.OpenDialog.txtTitleProtected": "Fichier protégé", "Common.Views.PasswordDialog.txtDescription": "Indiquez un mot de passe pour protéger ce document", "Common.Views.PasswordDialog.txtIncorrectPwd": "Le mot de passe de confirmation n'est pas identique", "Common.Views.PasswordDialog.txtPassword": "Mot de passe", "Common.Views.PasswordDialog.txtRepeat": "Confirmer le mot de passe", "Common.Views.PasswordDialog.txtTitle": "Définir un mot de passe", "Common.Views.PasswordDialog.txtWarning": "Attention : si vous oubliez ou perdez votre mot de passe, il sera impossible de le récupérer. Conservez-le en lieu sûr.", "Common.Views.PluginDlg.textLoading": "Chargement", "Common.Views.PluginPanel.textClosePanel": "<PERSON><PERSON><PERSON> le module complémentaire", "Common.Views.PluginPanel.textLoading": "Chargement", "Common.Views.Plugins.groupCaption": "Modules complémentaires", "Common.Views.Plugins.strPlugins": "Plug-ins", "Common.Views.Plugins.textBackgroundPlugins": "Modules complémentaires d'arrière-plan", "Common.Views.Plugins.textSettings": "Paramètres", "Common.Views.Plugins.textStart": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textStop": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "La liste des modules d'arrière-plan", "Common.Views.Plugins.tipMore": "Plus", "Common.Views.Protection.hintAddPwd": "Chiffrer avec mot de passe", "Common.Views.Protection.hintDelPwd": "Supprimer le mot de passe", "Common.Views.Protection.hintPwd": "Modifier ou supprimer", "Common.Views.Protection.hintSignature": "Ajouter une signature numérique ou une ligne de signature", "Common.Views.Protection.txtAddPwd": "Ajouter un mot de passe", "Common.Views.Protection.txtChangePwd": "Modifier le mot de passe", "Common.Views.Protection.txtDeletePwd": "Supprimer le mot de passe", "Common.Views.Protection.txtEncrypt": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.txtInvisibleSignature": "Ajouter une signature numérique", "Common.Views.Protection.txtSignature": "Signature", "Common.Views.Protection.txtSignatureLine": "Ajouter la zone de signature", "Common.Views.RecentFiles.txtOpenRecent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.RenameDialog.textName": "Nom de fi<PERSON>er", "Common.Views.RenameDialog.txtInvalidName": "Un nom de fichier ne peut pas contenir les caractères suivants :", "Common.Views.ReviewChanges.hintNext": "Vers la modification suivante", "Common.Views.ReviewChanges.hintPrev": "Vers la modification précédente", "Common.Views.ReviewChanges.strFast": "Rapide", "Common.Views.ReviewChanges.strFastDesc": "Co-édition en temps réel. Tous les changements sont enregistrés automatiquement.", "Common.Views.ReviewChanges.strStrict": "Strict", "Common.Views.ReviewChanges.strStrictDesc": "Utilise<PERSON> le bouton \"Enregistrer\" pour synchroniser les modifications faites par vous et les autres personnes.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Accepter la modification actuelle", "Common.Views.ReviewChanges.tipCoAuthMode": "Définir le mode de co-édition", "Common.Views.ReviewChanges.tipCommentRem": "Supprimer les commentaires", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Supprimer les commentaires existants", "Common.Views.ReviewChanges.tipCommentResolve": "Résoudre les commentaires", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Résoudre les commentaires actuels", "Common.Views.ReviewChanges.tipHistory": "Afficher versions", "Common.Views.ReviewChanges.tipRejectCurrent": "Rejeter cette modification", "Common.Views.ReviewChanges.tipReview": "Suivi des modifications", "Common.Views.ReviewChanges.tipReviewView": "Sélectionner le mode souhaité.", "Common.Views.ReviewChanges.tipSetDocLang": "Définir la langue du document", "Common.Views.ReviewChanges.tipSetSpelling": "Vérification orthographique", "Common.Views.ReviewChanges.tipSharing": "<PERSON><PERSON>rer les droits d'accès au document", "Common.Views.ReviewChanges.txtAccept": "Accepter", "Common.Views.ReviewChanges.txtAcceptAll": "Accepter toutes les modifications", "Common.Views.ReviewChanges.txtAcceptChanges": "Accepter les modifications", "Common.Views.ReviewChanges.txtAcceptCurrent": "Accepter la modification actuelle", "Common.Views.ReviewChanges.txtChat": "Cha<PERSON>", "Common.Views.ReviewChanges.txtClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCoAuthMode": "Mode de co-édition ", "Common.Views.ReviewChanges.txtCommentRemAll": "Supprimer tous les commentaires", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Supprimer les commentaires actuels", "Common.Views.ReviewChanges.txtCommentRemMy": "Supprimer mes commentaires", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Supprimer mes commentaires actuels", "Common.Views.ReviewChanges.txtCommentRemove": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtCommentResolveAll": "<PERSON><PERSON> tous les commentaires comme résolus", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Marquer les commentaires actuels comme résolus", "Common.Views.ReviewChanges.txtCommentResolveMy": "Marquer mes commentaires comme résolus", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Marquer mes commentaires comme résolus", "Common.Views.ReviewChanges.txtDocLang": "<PERSON><PERSON>", "Common.Views.ReviewChanges.txtFinal": "Toutes les modifications acceptées (aperçu)", "Common.Views.ReviewChanges.txtFinalCap": "Final", "Common.Views.ReviewChanges.txtHistory": "Historique des versions", "Common.Views.ReviewChanges.txtMarkup": "Toutes les modifications (édition)", "Common.Views.ReviewChanges.txtMarkupCap": "Balisage", "Common.Views.ReviewChanges.txtNext": "Suivante", "Common.Views.ReviewChanges.txtOriginal": "Toutes les modifications rejetées (Aperçu)", "Common.Views.ReviewChanges.txtOriginalCap": "Original", "Common.Views.ReviewChanges.txtPrev": "Précédente", "Common.Views.ReviewChanges.txtReject": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewChanges.txtRejectAll": "Rejeter toutes les modifications", "Common.Views.ReviewChanges.txtRejectChanges": "Rejeter les modifications", "Common.Views.ReviewChanges.txtRejectCurrent": "Rejeter cette modification", "Common.Views.ReviewChanges.txtSharing": "Partage", "Common.Views.ReviewChanges.txtSpelling": "Vérification orthographique", "Common.Views.ReviewChanges.txtTurnon": "Suivi des modifications", "Common.Views.ReviewChanges.txtView": "Mode d'affichage", "Common.Views.ReviewPopover.textAdd": "Ajouter", "Common.Views.ReviewPopover.textAddReply": "Ajouter une réponse", "Common.Views.ReviewPopover.textCancel": "Annuler", "Common.Views.ReviewPopover.textClose": "<PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textComment": "Commentaire", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Saisissez votre commentaire ici", "Common.Views.ReviewPopover.textMention": "+mention donne l'accès au document et notifie par courriel  ", "Common.Views.ReviewPopover.textMentionNotify": "+mention notifie l'utilisateur par courriel", "Common.Views.ReviewPopover.textOpenAgain": "Ouvrir à nouveau", "Common.Views.ReviewPopover.textReply": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textResolve": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.textViewResolved": "Vous n'avez pas la permission de rouvrir le commentaire", "Common.Views.ReviewPopover.txtDeleteTip": "<PERSON><PERSON><PERSON><PERSON>", "Common.Views.ReviewPopover.txtEditTip": "Modifier", "Common.Views.SaveAsDlg.textLoading": "Chargement", "Common.Views.SaveAsDlg.textTitle": "Dossier pour enregistrement", "Common.Views.SearchPanel.textByColumns": "Par colonnes", "Common.Views.SearchPanel.textByRows": "Par lignes", "Common.Views.SearchPanel.textCaseSensitive": "Re<PERSON>er la casse", "Common.Views.SearchPanel.textCell": "Cellule", "Common.Views.SearchPanel.textCloseSearch": "<PERSON><PERSON>er la recherche", "Common.Views.SearchPanel.textContentChanged": "Document modifié.", "Common.Views.SearchPanel.textFind": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textFindAndReplace": "Re<PERSON><PERSON> et remplacer", "Common.Views.SearchPanel.textFormula": "Formule", "Common.Views.SearchPanel.textFormulas": "Formules", "Common.Views.SearchPanel.textItemEntireCell": "Totalité du contenu de la cellule", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} éléments remplacés avec succès.", "Common.Views.SearchPanel.textLookIn": "<PERSON><PERSON><PERSON> dans", "Common.Views.SearchPanel.textMatchUsingRegExp": "Correspondance à l'aide d'expressions régulières", "Common.Views.SearchPanel.textName": "Prénom ", "Common.Views.SearchPanel.textNoMatches": "Aucune correspondance", "Common.Views.SearchPanel.textNoSearchResults": "Aucun résultat de recherche", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} éléments remplacés. Les {2} éléments restants sont verrouillés par d'autres utilisateurs.", "Common.Views.SearchPanel.textReplace": "<PERSON><PERSON>lace<PERSON>", "Common.Views.SearchPanel.textReplaceAll": "Remplacer tout", "Common.Views.SearchPanel.textReplaceWith": "Remplacer par", "Common.Views.SearchPanel.textSearch": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSearchAgain": "{0}Effectuer une nouvelle recherche{1} pour obtenir des résultats précis.", "Common.Views.SearchPanel.textSearchHasStopped": "La recherche a été arrêtée", "Common.Views.SearchPanel.textSearchOptions": "Options de recherche", "Common.Views.SearchPanel.textSearchResults": "Résultats de la recherche : {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Résultats de recherche", "Common.Views.SearchPanel.textSelectDataRange": "Sélectionner une plage de données", "Common.Views.SearchPanel.textSheet": "<PERSON><PERSON><PERSON>", "Common.Views.SearchPanel.textSpecificRange": "Plage spécifique", "Common.Views.SearchPanel.textTooManyResults": "Il y a trop de résultats pour les montrer ici", "Common.Views.SearchPanel.textValue": "<PERSON><PERSON>", "Common.Views.SearchPanel.textValues": "Valeurs", "Common.Views.SearchPanel.textWholeWords": "Mots entiers uniquement", "Common.Views.SearchPanel.textWithin": "<PERSON><PERSON>", "Common.Views.SearchPanel.textWorkbook": "Classeur", "Common.Views.SearchPanel.tipNextResult": "Résultat suivant", "Common.Views.SearchPanel.tipPreviousResult": "Résultat précédent", "Common.Views.SelectFileDlg.textLoading": "Chargement", "Common.Views.SelectFileDlg.textTitle": "Sélectionner la source de données", "Common.Views.ShapeShadowDialog.txtAngle": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtDistance": "Distance", "Common.Views.ShapeShadowDialog.txtSize": "<PERSON><PERSON>", "Common.Views.ShapeShadowDialog.txtTitle": "Ajuster l'ombre", "Common.Views.ShapeShadowDialog.txtTransparency": "Transparence", "Common.Views.SignDialog.textBold": "Gras", "Common.Views.SignDialog.textCertificate": "Certificat", "Common.Views.SignDialog.textChange": "Modifier", "Common.Views.SignDialog.textInputName": "Saisir le nom du signataire", "Common.Views.SignDialog.textItalic": "Italique", "Common.Views.SignDialog.textNameError": "Veuillez indiquer le nom du signataire.", "Common.Views.SignDialog.textPurpose": "But de la signature du document", "Common.Views.SignDialog.textSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textSelectImage": "Sélectionner une image", "Common.Views.SignDialog.textSignature": "L'aspect de votre signature", "Common.Views.SignDialog.textTitle": "Signer le document", "Common.Views.SignDialog.textUseImage": "ou cliquer sur \"Sélectionner une image\" afin d'utiliser une image en tant que signature", "Common.Views.SignDialog.textValid": "Valide de %1 à %2", "Common.Views.SignDialog.tipFontName": "Nom de police", "Common.Views.SignDialog.tipFontSize": "Taille de police", "Common.Views.SignSettingsDialog.textAllowComment": "Autoriser le signataire à ajouter un commentaire dans la boîte de dialogue de la signature", "Common.Views.SignSettingsDialog.textDefInstruction": "Avant de signer un document, vérifiez que le contenu que vous signez est correct.", "Common.Views.SignSettingsDialog.textInfoEmail": "E-mail du signataire suggéré", "Common.Views.SignSettingsDialog.textInfoName": "Signataire suggéré", "Common.Views.SignSettingsDialog.textInfoTitle": "Titre du signataire suggéré", "Common.Views.SignSettingsDialog.textInstructions": "Instructions pour les signataires", "Common.Views.SignSettingsDialog.textShowDate": "Afficher la date de signature à côté de la zone de signature", "Common.Views.SignSettingsDialog.textTitle": "Configuration de signature", "Common.Views.SignSettingsDialog.txtEmpty": "Ce champ est obligatoire", "Common.Views.SymbolTableDialog.textCharacter": "<PERSON><PERSON><PERSON>", "Common.Views.SymbolTableDialog.textCode": "Valeur hexadécimale Unicode", "Common.Views.SymbolTableDialog.textCopyright": "Symbole de copyright", "Common.Views.SymbolTableDialog.textDCQuote": "<PERSON><PERSON><PERSON> guil<PERSON>", "Common.Views.SymbolTableDialog.textDOQuote": "Double guillemet ouvrant", "Common.Views.SymbolTableDialog.textEllipsis": "Points de suspension", "Common.Views.SymbolTableDialog.textEmDash": "Tiret cadratin", "Common.Views.SymbolTableDialog.textEmSpace": "Espace cadratin", "Common.Views.SymbolTableDialog.textEnDash": "Tiret demi-cadratin", "Common.Views.SymbolTableDialog.textEnSpace": "Espace demi-cadratin", "Common.Views.SymbolTableDialog.textFont": "Police", "Common.Views.SymbolTableDialog.textNBHyphen": "Trait d’union insécable", "Common.Views.SymbolTableDialog.textNBSpace": "Espace insécable", "Common.Views.SymbolTableDialog.textPilcrow": "Pied-de-mouche", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 Espace cadratin", "Common.Views.SymbolTableDialog.textRange": "Plage", "Common.Views.SymbolTableDialog.textRecent": "Caractères spéciaux récemment utilisés", "Common.Views.SymbolTableDialog.textRegistered": "Symbole de marque déposée", "Common.Views.SymbolTableDialog.textSCQuote": "G<PERSON><PERSON>et simple fermant", "Common.Views.SymbolTableDialog.textSection": "Paragraphe", "Common.Views.SymbolTableDialog.textShortcut": "<PERSON>e <PERSON> r<PERSON>ci", "Common.Views.SymbolTableDialog.textSHyphen": "Trait d'union conditionnel", "Common.Views.SymbolTableDialog.textSOQuote": "G<PERSON><PERSON><PERSON> simple ouvrant", "Common.Views.SymbolTableDialog.textSpecial": "Symboles spé<PERSON>ux", "Common.Views.SymbolTableDialog.textSymbols": "Symboles", "Common.Views.SymbolTableDialog.textTitle": "Symbole", "Common.Views.SymbolTableDialog.textTradeMark": "Symbole de marque", "Common.Views.UserNameDialog.textDontShow": "Ne plus me demander", "Common.Views.UserNameDialog.textLabel": "Étiquette :", "Common.Views.UserNameDialog.textLabelError": "L'étiquette ne peut pas être vide.", "SSE.Controllers.DataTab.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textAddExternalData": "Le lien vers une source externe a été ajouté. Vous pouvez mettre à jour ces liens dans l'onglet Données.", "SSE.Controllers.DataTab.textColumns": "Colonnes ", "SSE.Controllers.DataTab.textContinue": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textDontUpdate": "Ne pas mettre à jour", "SSE.Controllers.DataTab.textEmptyUrl": "Spécifiez l'URL", "SSE.Controllers.DataTab.textRows": "<PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textTurnOff": "Désactiver la mise à jour automatique", "SSE.Controllers.DataTab.textUpdate": "Mettre à jour", "SSE.Controllers.DataTab.textWizard": "Texte en colonnes", "SSE.Controllers.DataTab.txtDataValidation": "Validation des données", "SSE.Controllers.DataTab.txtErrorExternalLink": "Erreur : la mise à jour a écho<PERSON>", "SSE.Controllers.DataTab.txtExpand": "Développer", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Les données situées à côté de la selection ne seront pas déplacées. Voulez-vous étendre la selection pour inclure les données adjacentes ou continuer avec les cellules selcetionnées seulement ?", "SSE.Controllers.DataTab.txtExtendDataValidation": "La sélection contient plusieurs cellules sans les paramètres de validation de données.<br>V<PERSON><PERSON><PERSON>-vous étendre la validation de données pour ces cellules ? ", "SSE.Controllers.DataTab.txtImportWizard": "Assistant Importa<PERSON> de texte", "SSE.Controllers.DataTab.txtRemDuplicates": "Supp<PERSON>er les doublons", "SSE.Controllers.DataTab.txtRemoveDataValidation": "La sélection doit contenir plus d'une type de validation.<br><PERSON><PERSON><PERSON> les paramètres actuels et continuer ?", "SSE.Controllers.DataTab.txtRemSelected": "Déplacer dans sélectionnés", "SSE.Controllers.DataTab.txtUrlTitle": "Coller URL de données", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "Ce livre contient des liens vers des sources externes qui sont mises à jour automatiquement. Cela pourrait être dangereux.<br><br>Si vous leur faites confiance, appuyez sur Continuer.", "SSE.Controllers.DataTab.warnUpdateExternalData": "Ce classeur contient des liens vers une ou plusieurs sources externes qui pourraient être dangereuses.<br>Si vous faites confiance à ces liens, mettez-les à jour pour obtenir les dernières données.", "SSE.Controllers.DocumentHolder.alignmentText": "Alignement", "SSE.Controllers.DocumentHolder.centerText": "Au centre", "SSE.Controllers.DocumentHolder.deleteColumnText": "Supprimer la colonne", "SSE.Controllers.DocumentHolder.deleteRowText": "Supprimer la ligne", "SSE.Controllers.DocumentHolder.deleteText": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Le lien de référence n'existe pas. Veuillez corriger la référence ou la supprimer.", "SSE.Controllers.DocumentHolder.guestText": "Invi<PERSON>", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Colonne à gauche", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Colonne à droite", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Ligne au-dessus", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Ligne en dessous", "SSE.Controllers.DocumentHolder.insertText": "Insertion", "SSE.Controllers.DocumentHolder.leftText": "À gauche", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Avertissement", "SSE.Controllers.DocumentHolder.rightText": "À droite", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Options d'auto-correction", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "Largeur de colonne {0} symboles ({1} pixels)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "<PERSON>ur de ligne {0} points ({1} pixels)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Cliquez sur le lien pour l'ouvrir ou cliquez et maintenez votre souris pour choisir une cellule.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Insérer une colonne à gauche", "SSE.Controllers.DocumentHolder.textInsertTop": "Insérer une ligne au-dessus", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Collage spécial", "SSE.Controllers.DocumentHolder.textStopExpand": "Arrêter automatiquement ", "SSE.Controllers.DocumentHolder.textSym": "sym", "SSE.Controllers.DocumentHolder.tipIsLocked": "Cet élément est en cours de modification par un autre utilisateur.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Au dessus de la moyenne", "SSE.Controllers.DocumentHolder.txtAddBottom": "Ajouter bordure inférieure", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Ajouter barre de fraction", "SSE.Controllers.DocumentHolder.txtAddHor": "Ajouter ligne horizontale", "SSE.Controllers.DocumentHolder.txtAddLB": "Ajouter une ligne en bas à gauche", "SSE.Controllers.DocumentHolder.txtAddLeft": "A<PERSON>ter bordure gauche", "SSE.Controllers.DocumentHolder.txtAddLT": "Ajouter une ligne supérieure gauche", "SSE.Controllers.DocumentHolder.txtAddRight": "Ajouter bordure droite", "SSE.Controllers.DocumentHolder.txtAddTop": "Ajouter bordure supérieure", "SSE.Controllers.DocumentHolder.txtAddVer": "Ajouter une ligne verticale", "SSE.Controllers.DocumentHolder.txtAlignToChar": "<PERSON><PERSON><PERSON> à caractère", "SSE.Controllers.DocumentHolder.txtAll": "(Tous)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Renvoie le contenu complet du tableau ou les colonnes indiquées, y compris les titres des colonnes, les données et les lignes des totaux du tableau", "SSE.Controllers.DocumentHolder.txtAnd": "et", "SSE.Controllers.DocumentHolder.txtBegins": "Commence par", "SSE.Controllers.DocumentHolder.txtBelowAve": "Au dessous de la moyenne", "SSE.Controllers.DocumentHolder.txtBlanks": "(Vides)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Propriétés de la bordure", "SSE.Controllers.DocumentHolder.txtBottom": "En bas", "SSE.Controllers.DocumentHolder.txtByField": "%1 de %2", "SSE.Controllers.DocumentHolder.txtColumn": "Colonne", "SSE.Controllers.DocumentHolder.txtColumnAlign": "L'alignement de la colonne", "SSE.Controllers.DocumentHolder.txtContains": "Contient", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Lien copié dans le presse-papiers", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Renvoie les cellules de données du tableau ou les colonnes indiquées ", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Diminuer la taille de l'argument", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Supprimer l'argument", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "<PERSON><PERSON><PERSON><PERSON> le saut manuel", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Supprimer les caractères englobants", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Supprimer les caractères et les séparateurs englobants", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Supprimer l'équation", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Supprimer caractère", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Supprimer radical", "SSE.Controllers.DocumentHolder.txtEnds": "Se termine par", "SSE.Controllers.DocumentHolder.txtEquals": "Est égal", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Egal à сouleur de cellule", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Egale à couleur de police", "SSE.Controllers.DocumentHolder.txtExpand": "Développer et trier", "SSE.Controllers.DocumentHolder.txtExpandSort": "Les données situées à côté de la sélection ne seront pas triées. Souhaitez-vous développer la sélection pour inclure les données adjacentes ou souhaitez-vous continuer à trier iniquement les cellules sélectionnées ?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Bas", "SSE.Controllers.DocumentHolder.txtFilterTop": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Remplacer par une fraction sur une ligne", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Remplacer par une fraction oblique", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Remplacer par une fraction sur deux lignes", "SSE.Controllers.DocumentHolder.txtGreater": "Sup<PERSON>ur à", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Su<PERSON><PERSON><PERSON> ou égal à", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Char au-dessus du texte", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Char en-dessus du texte", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Renvoie les titres des colonnes du tableau ou les colonnes indiquées ", "SSE.Controllers.DocumentHolder.txtHeight": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtHideBottom": "Masquer bordure inférieure", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Cacher limite inférieure", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "<PERSON><PERSON> le crochet de fermeture", "SSE.Controllers.DocumentHolder.txtHideDegree": "<PERSON><PERSON> de<PERSON>", "SSE.Controllers.DocumentHolder.txtHideHor": "Cacher ligne horizontale", "SSE.Controllers.DocumentHolder.txtHideLB": "Cacher la ligne en bas à gauche", "SSE.Controllers.DocumentHolder.txtHideLeft": "<PERSON><PERSON> bordure gauche", "SSE.Controllers.DocumentHolder.txtHideLT": "Cacher la ligne en haut à gauche", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "<PERSON>acher crochet d'ouverture", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Cacher espace réservé", "SSE.Controllers.DocumentHolder.txtHideRight": "<PERSON><PERSON> bordure droite", "SSE.Controllers.DocumentHolder.txtHideTop": "Cacher bordure supérieure", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Cacher limite supérieure", "SSE.Controllers.DocumentHolder.txtHideVer": "Cacher ligne verticale", "SSE.Controllers.DocumentHolder.txtImportWizard": "Assistant Importa<PERSON> de texte", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Augmenter la taille de l'argument", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Insérez l'argument après", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Insérer argument devant", "SSE.Controllers.DocumentHolder.txtInsertBreak": "In<PERSON><PERSON><PERSON> pause manuelle", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Insérer équation après", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Insérer l'équation avant", "SSE.Controllers.DocumentHolder.txtItems": "éléments", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Conserver uniquement le texte", "SSE.Controllers.DocumentHolder.txtLess": "Inférieur à", "SSE.Controllers.DocumentHolder.txtLessEquals": "Inférieure ou égale à", "SSE.Controllers.DocumentHolder.txtLimitChange": "Modifier l'emplacement des limites", "SSE.Controllers.DocumentHolder.txtLimitOver": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> du texte", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Limite en dessous du texte", "SSE.Controllers.DocumentHolder.txtLockSort": "Des données se trouvent à côté de votre sélection, mais vous n'avez pas les autorisations suffisantes pour modifier ces cellules.<br>Voulez-vous continuer avec la sélection actuelle ?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "<PERSON><PERSON>er crochets à la hauteur de l'argument", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Alignement de la matrice", "SSE.Controllers.DocumentHolder.txtNoChoices": "Il n’y a pas de choix pour le remplissage de la cellule.<br><PERSON><PERSON> des valeurs de texte de la colonne peuvent être sélectionnées pour le remplacement.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Ne commence pas par", "SSE.Controllers.DocumentHolder.txtNotContains": "Ne contient pas", "SSE.Controllers.DocumentHolder.txtNotEnds": "Ne se termine pas par", "SSE.Controllers.DocumentHolder.txtNotEquals": "n'est pas égal", "SSE.Controllers.DocumentHolder.txtOr": "ou", "SSE.Controllers.DocumentHolder.txtOverbar": "<PERSON>e au-dessus d'un texte", "SSE.Controllers.DocumentHolder.txtPaste": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Formule sans bordures", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Formule + largeur de colonne", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Mise en forme de destination", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Coller uniquement la mise en forme", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Formule + format de nombre", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Coller uniquement la formule", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Formule + mise en forme", "SSE.Controllers.DocumentHolder.txtPasteLink": "Coller avec liaison", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Image liée", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Fusionner la mise en forme conditionnelle", "SSE.Controllers.DocumentHolder.txtPastePicture": "Image", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Mise en forme source", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Transposer", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Valeur + toute la mise en forme", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Valeur + format des nombres", "SSE.Controllers.DocumentHolder.txtPasteValues": "Coller uniquement la valeur", "SSE.Controllers.DocumentHolder.txtPercent": "pour cent", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Reprendre l'expansion automatique du tableau", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Supprimer la barre de fraction", "SSE.Controllers.DocumentHolder.txtRemLimit": "Supprimer la limite", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Supp<PERSON>er le caractère d'accent", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Supprimer la barre", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Voulez vous supprimer cette signature?<br>Cette action ne peut pas être annulée.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Supprimer scripts", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Supprimer la souscription", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Supprimer la suscription", "SSE.Controllers.DocumentHolder.txtRowHeight": "<PERSON><PERSON> <PERSON> ligne", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Scripts après le texte", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Scripts avant le texte", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Montrer limite inférieure", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Afficher crochet de fermeture", "SSE.Controllers.DocumentHolder.txtShowDegree": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Afficher crochet d'ouverture", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Afficher espace réservé", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Afficher limite supérieure", "SSE.Controllers.DocumentHolder.txtSorting": "Tri", "SSE.Controllers.DocumentHolder.txtSortSelected": "Trier l'objet sélectionné ", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Allonger des crochets", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Choisissez uniquement cette ligne de la colonne spécifiée", "SSE.Controllers.DocumentHolder.txtTop": "En haut", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Renvoie les lignes des totaux du tableau ou les colonnes indiquées ", "SSE.Controllers.DocumentHolder.txtUnderbar": "Barre en dessous d'un texte", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Annuler l'expansion automatique du tableau", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Utiliser l'assistant d'importation de texte", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Cliquer sur ce lien peut être dangereux pour votre appareil et vos données. <br>Êtes-vous sûr de vouloir continuer ?", "SSE.Controllers.DocumentHolder.txtWidth": "<PERSON><PERSON>", "SSE.Controllers.DocumentHolder.warnFilterError": "Vous avez besoin d'au moins un champs dans la zone des valeurs pour appliquer la filtre de valeur.", "SSE.Controllers.FormulaDialog.sCategoryAll": "<PERSON>ut", "SSE.Controllers.FormulaDialog.sCategoryCube": "C<PERSON>", "SSE.Controllers.FormulaDialog.sCategoryCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Base de données", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Date et heure", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Ingénierie", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Financier", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Information", "SSE.Controllers.FormulaDialog.sCategoryLast10": "10 dernières utilisées", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Logique", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Recherche et référence", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Maths et trigonométrie", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Statistiques", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Texte et données", "SSE.Controllers.LeftMenu.newDocumentTitle": "<PERSON><PERSON><PERSON> de calcul sans nom", "SSE.Controllers.LeftMenu.textByColumns": "Par colonnes", "SSE.Controllers.LeftMenu.textByRows": "Par lignes", "SSE.Controllers.LeftMenu.textFormulas": "Formules", "SSE.Controllers.LeftMenu.textItemEntireCell": "Totalité du contenu de la cellule", "SSE.Controllers.LeftMenu.textLoadHistory": "Chargement de l'historique des versions...", "SSE.Controllers.LeftMenu.textLookin": "Regarder dans", "SSE.Controllers.LeftMenu.textNoTextFound": "Votre recherche n'a donné aucun résultat.S'il vous plaît, modifiez vos critères de recherche.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Le remplacement est fait. {0} occurrences ont été ignorées.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "La recherche est effectuée. Nb d'occurrences remplacées:{0}", "SSE.Controllers.LeftMenu.textSave": "Enregistrer", "SSE.Controllers.LeftMenu.textSearch": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textSelectPath": "Entrez un nouveau nom pour enregistrer la copie du fichier", "SSE.Controllers.LeftMenu.textSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.LeftMenu.textValues": "Valeurs", "SSE.Controllers.LeftMenu.textWarning": "Avertissement", "SSE.Controllers.LeftMenu.textWithin": "A l'intérieur", "SSE.Controllers.LeftMenu.textWorkbook": "Classeur", "SSE.Controllers.LeftMenu.txtUntitled": "Sans titre", "SSE.Controllers.LeftMenu.warnDownloadAs": "Si vous continuez à enregistrer dans ce format toutes les fonctions sauf le texte seront perdues.<br>Êtes-vous sûr de vouloir continuer ?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "Le format CSV ne permet pas d'enregistrer un fichier à plusieurs feuilles.<br>Pour conserver le format sélectionné et enregistrer uniquement la feuille en cours, appuyez sur Enregistrer.<br>Pour enregistrer le livre en cours, cliquez sur Annuler et enregistrez-la dans un format différent.", "SSE.Controllers.Main.confirmAddCellWatches": "Cette action va ajouter {0} espions de cellule. Voulez-vous continuer ?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "Cette action ajoutera seulement {0} espions de cellule par raison de sauvegarde de la mémoire.<br>V<PERSON><PERSON><PERSON>-vous continuer ?", "SSE.Controllers.Main.confirmMaxChangesSize": "La taille des actions dépasse la limitation fixée pour votre serveur.<br><PERSON><PERSON><PERSON><PERSON> sur \"Annuler\" pour annuler votre dernière action ou sur \"Continuer\" pour maintenir l'action en local (vous devez télécharger le fichier ou copier son contenu pour vous assurer que rien n'est perdu).", "SSE.Controllers.Main.confirmMoveCellRange": "La plage de cellules finale peut contenir des données.Voulez-vous continuer?", "SSE.Controllers.Main.confirmPutMergeRange": "Les données source contiennent des cellules fusionnées. <br>La fusion a été supprimée avant le transfert des cellules dans le tableau.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Les formules de la ligne en-tête seront supprimées et transformées en texte statique.<br><PERSON><PERSON><PERSON><PERSON><PERSON>vous continuer?", "SSE.Controllers.Main.confirmReplaceHFPicture": "Seul une image peut être insérée dans chaque section de l'en-tête.<br>Appuyez sur \"Remplacer\" pour remplacer l'image actuelle.<br>Appuyez sur \"Garder\" pour garder l'image actuelle.", "SSE.Controllers.Main.convertationTimeoutText": "<PERSON><PERSON><PERSON> de conversion expiré.", "SSE.Controllers.Main.criticalErrorExtText": "Cliquez sur \"OK\" pour revenir à la liste des documents.", "SSE.Controllers.Main.criticalErrorTitle": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.downloadErrorText": "Échec du téléchargement.", "SSE.Controllers.Main.downloadTextText": "Téléchargement de la feuille de calcul en cours...", "SSE.Controllers.Main.downloadTitleText": "Téléchargement de la feuille de calcul", "SSE.Controllers.Main.errNoDuplicates": "Aucune valeur en double n'est trouvée", "SSE.Controllers.Main.errorAccessDeny": "Vous tentez d'exéсuter une action pour laquelle vous ne disposez pas des droits.<br><PERSON><PERSON><PERSON>z contacter l'administrateur de Document Server.", "SSE.Controllers.Main.errorArgsRange": "Erreur dans la formule entrée.<br>La plage des arguments utilisée est incorrecte.", "SSE.Controllers.Main.errorAutoFilterChange": "L'opération n'est pas autorisée, car elle tente de décaler les cellules d'un tableau sur votre feuille.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Impossible de réaliser l'opération sur les cellules sélectionnées car vous ne pouvez pas déplacer une partie du tableau.<br>Sélectionnez une autre plage de données afin que tout le tableau soit déplacé et essayez à nouveau.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Impossible de réaliser l'opération sur la plage de cellules spécifiée.<br>Sélectionnez une plage de données différente de la plage existante et essayez à nouveau.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "L'opération ne peut pas être effectuée car la zone contient des cellules filtrées.<br><PERSON><PERSON><PERSON>z supprimer les filtres et réessayez.", "SSE.Controllers.Main.errorBadImageUrl": "L'URL d'image est incorrecte", "SSE.Controllers.Main.errorCalculatedItemInPageField": "L'élément ne peut être ni ajouté ni modifié. Le rapport du tableau croisé dynamique contient ce champ dans les filtres.", "SSE.Controllers.Main.errorCannotPasteImg": "Il n'est pas possible de coller cette image à partir du Presse-papiers, mais vous pouvez l'enregistrer sur votre appareil et l'insérer à partir de là, ou vous pouvez copier l'image sans texte et la coller dans la feuille de calcul.", "SSE.Controllers.Main.errorCannotUngroup": "Impossible de dissocier. Pour construire un plan, sélectionnez les lignes ou les colonnes de détail et groupez-les.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Vous ne pouvez pas utiliser cette commande sur une feuille protégée. Pour utiliser cette commande, retirez la protection de la feuille.<br>Il peut vous être demandé de saisir un mot de passe.", "SSE.Controllers.Main.errorChangeArray": "Impossible de modifier une partie de matrice.", "SSE.Controllers.Main.errorChangeFilteredRange": "<PERSON><PERSON> modifiera une plage filtrée sur votre feuille.<br><PERSON>ur terminer cette tâche, veuillez supprimer les filtres automatiques.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "La cellule ou le graphique que vous essayez de modifier se trouve sur une feuille protégée.<br>Pour effectuer une modification, déprotégez la feuille. Il se peut que l'on vous demande de saisir un mot de passe.", "SSE.Controllers.Main.errorCircularReference": "Il existe une ou plusieurs références circulaires où une formule fait référence à sa propre cellule, directement ou indirectement.<br>Essa<PERSON>z de supprimer ou de modifier ces références, ou de déplacer les formules dans des cellules différentes.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Connexion au serveur perdue. Le document ne peut être modifié en ce moment.", "SSE.Controllers.Main.errorConnectToServer": "Impossible d'enregistrer le document. Veuillez vérifier vos paramètres de connexion ou contactez l'administrateur.<br><PERSON><PERSON><PERSON> vous cliquez sur le bouton 'OK', vous serez invité à télécharger le document.", "SSE.Controllers.Main.errorConvertXml": "Le fichier a un format non pris en charge.<br>Seul le format XML Spreadsheet 2003 peut être utilisé.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Impossible d'exécuter cette commande sur des sélections multiples.<br><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> une seule plage et essayez à nouveau.", "SSE.Controllers.Main.errorCountArg": "Une erreur dans la formule entrée.<br>Nombre d'arguments utilisé est incorrect.", "SSE.Controllers.Main.errorCountArgExceed": "Une erreur dans la formule entrée.<br>Nombre d'arguments est dépassé.", "SSE.Controllers.Main.errorCreateDefName": "Actuellement, des plages nommées existantes ne peuvent pas être modifiées et les nouvelles ne peuvent pas être créées,<br> car certaines d'entre eux sont en cours de modification.", "SSE.Controllers.Main.errorCreateRange": "Les plages existantes ne peuvent pas être modifiées et les nouvelles ne peuvent pas être créées<br>pour l'instant car certaines d'entre elles sont en cours de modification.", "SSE.Controllers.Main.errorDatabaseConnection": "Erreur externe.<br>Erreur de connexion à la base de données. Si l'erreur persiste veillez contactez l'assistance technique.", "SSE.Controllers.Main.errorDataEncrypted": "Les modifications chiffrées ont été reçues, elle ne peuvent pas être déchiffrées.", "SSE.Controllers.Main.errorDataRange": "Plage de donn<PERSON>.", "SSE.Controllers.Main.errorDataValidate": "La valeur saisie est incorrecte.<br>Un utilisateur a restreint les valeurs pouvant être saisies dans cette cellule.", "SSE.Controllers.Main.errorDefaultMessage": "Code d'erreur: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Vous essayez de supprimer une colonne qui contient une cellule verrouillée. Les cellules verrouillées ne peuvent pas être supprimées lorsque la feuille est protégée.<br>Pour supprimer une cellule verrouillée, déprotégez la feuille. Il se peut que l'on vous demande d'entrer un mot de passe.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Vous essayez de supprimer une ligne qui contient une cellule verrouillée. Les cellules verrouillées ne peuvent pas être supprimées lorsque la feuille est protégée.<br>Pour supprimer une cellule verrouillée, déprotégez la feuille. Il se peut que l'on vous demande d'entrer un mot de passe.", "SSE.Controllers.Main.errorDependentsNoFormulas": "La commande Tracer les dépendances n'a trouvé aucune formule faisant référence à la cellule active.", "SSE.Controllers.Main.errorDirectUrl": "Veuillez vérifier le lien vers le document.<br>Ce lien doit être un lien direct vers le fichier à télécharger.", "SSE.Controllers.Main.errorEditingDownloadas": "Une erreur s'est produite lors du travail avec le document.<br>Utilisez l'option 'Télécharger comme' pour enregistrer une copie de sauvegarde du fichier sur le disque dur de votre ordinateur.", "SSE.Controllers.Main.errorEditingSaveas": "Une erreur s'est produite lors du travail avec le document.<br>Utilisez l'option 'Télécharger comme...' pour enregistrer une copie de sauvegarde sur le disque dur de votre ordinateur. ", "SSE.Controllers.Main.errorEditView": "Il est impossible de modifier le mode Feuille actuel et de créer de nouveaux modes pour le moment parce que certains modes sont en cours d'édition.", "SSE.Controllers.Main.errorEmailClient": "Pas de client messagerie trouvé", "SSE.Controllers.Main.errorFilePassProtect": "Le fichier est protégé par le mot de passe et ne peut être ouvert.", "SSE.Controllers.Main.errorFileRequest": "Erreur externe.<br>Erreur de demande du fichier. Si l'erreur persiste veillez contactez l'assistance technique.", "SSE.Controllers.Main.errorFileSizeExceed": "La taille du fichier dépasse les limites établies sur votre serveur.<br><PERSON><PERSON><PERSON><PERSON> contacter votre administrateur de Document Server pour obtenir plus d'information.  ", "SSE.Controllers.Main.errorFileVKey": "Erreur externe.<br>Clé de sécurité incorrecte. Si l'erreur persiste veillez contactez l'assistance technique.", "SSE.Controllers.Main.errorFillRange": "Il est impossible de remplir la plage de cellules sélectionnée. <br>Toutes les cellules unies doivent être de la même taille.", "SSE.Controllers.Main.errorForceSave": "Une erreur est survenue lors de l'enregistrement du fichier. Veuillez utiliser l'option \"Télécharger comme\" pour enregistrer le fichier sur le disque dur de votre ordinateur ou réessayer plus tard.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Impossible de saisir une formule pour un nom d'élément ou de champ dans un rapport de tableau croisé dynamique.", "SSE.Controllers.Main.errorFormulaName": "Une erreur dans la formule entrée.<br>Nom de formule utilisé est incorrect.", "SSE.Controllers.Main.errorFormulaParsing": "Une erreur interne lors de l'analyse de la formule.", "SSE.Controllers.Main.errorFrmlMaxLength": "Vous ne pouvez pas ajouter cette formule car sa longueur dépasse le nombre de caractères autorisés.<br>Merci de modifier la formule et d'essayer à nouveau. ", "SSE.Controllers.Main.errorFrmlMaxReference": "Vous ne pouvez pas saisir cette formule parce qu'elle est trop longues, ou contient <br>références de cellules, et/ou noms. ", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Les valeurs de texte dans les formules sont limitées à 255 caractères.<br><PERSON><PERSON><PERSON><PERSON> la fonction CONCATENER ou l’opérateur de concaténation (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "La fonction fait référence à une feuille qui n'existe pas.<br>Veuillez vérifier les données et réessayer.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "L'opération n'a pas pu être achevée pour la plage de cellules sélectionnée. <br> S<PERSON><PERSON>ion<PERSON>z une plage de telle sorte que la première ligne de la table était sur la même ligne <br> et la table résultant chevauché l'actuel.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "L'opération n'a pas pu être achevée pour la plage de cellules sélectionnée. <br> Sélection<PERSON>z une plage qui ne comprend pas d'autres tables.", "SSE.Controllers.Main.errorInconsistentExt": "Une erreur s'est produite lors de l'ouverture du fichier.<br>Le contenu du fichier ne correspond pas à l'extension du fichier.", "SSE.Controllers.Main.errorInconsistentExtDocx": "Une erreur s'est produite lors de l'ouverture du fichier.<br>Le contenu du fichier correspond à des documents texte (par exemple docx), mais le fichier a une extension incohérente : %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "Une erreur s'est produite lors de l'ouverture du fichier.<br>Le contenu du fichier correspond à l'un des formats suivants : pdf/djvu/xps/oxps, mais le fichier a l'extension incohérente : %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "Une erreur s'est produite lors de l'ouverture du fichier.<br>Le contenu du fichier correspond à des présentations (par exemple pptx), mais le fichier a une extension incohérente : %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "Une erreur s'est produite lors de l'ouverture du fichier.<br>Le contenu du fichier correspond à des feuilles de calcul (par exemple xlsx), mais le fichier a une extension incohérente : %1.", "SSE.Controllers.Main.errorInvalidRef": "Entrez un nom correct pour la sélection ou une référence valable à laquelle aller.", "SSE.Controllers.Main.errorKeyEncrypt": "Descripteur de clés inconnu", "SSE.Controllers.Main.errorKeyExpire": "Descripteur de clés expiré", "SSE.Controllers.Main.errorLabledColumnsPivot": "Pour créer un tableau croisé dynamique, utilisez les données organisées sous forme de liste avec des colonnes libellées.", "SSE.Controllers.Main.errorLoadingFont": "Les polices ne sont pas téléchargées.<br><PERSON><PERSON>illez contacter l'administrateur de Document Server.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "La référence de localisation ou la plage de données n'est pas valide.", "SSE.Controllers.Main.errorLockedAll": "L'opération ne peut pas être réalisée car la feuille a été verrouillée par un autre utilisateur.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "L'une des cellules impliquées dans le processus de la recherche d'objectifs a été modifiée par un autre utilisateur.", "SSE.Controllers.Main.errorLockedCellPivot": "Impossible de modifier les données d'un tableau croisé dynamique.", "SSE.Controllers.Main.errorLockedWorksheetRename": "La feuille ne peut pas être renommée pour le moment car elle est en train d'être renommée par un autre utilisateur", "SSE.Controllers.Main.errorMaxPoints": "Maximum de 4096 points en série par graphique.", "SSE.Controllers.Main.errorMoveRange": "Impossible de modifier une partie d'une cellule fusionnée", "SSE.Controllers.Main.errorMoveSlicerError": "Segments de tableau ne peuvent pas être copiés d'un classeur vers un autre.<br><PERSON><PERSON><PERSON><PERSON> réessayer en sélectionnant tout le tableau avec tous les segments. ", "SSE.Controllers.Main.errorMultiCellFormula": "Formules de tableau plusieurs cellules ne sont pas autorisées dans les classeurs.", "SSE.Controllers.Main.errorNoDataToParse": "Aucune donnée à convertir n'a été sélectionnée.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "Si un ou plusieurs tableaux croisés dynamiques comportent des éléments calculés, aucun champ ne peut être utilisé dans la zone de données deux fois ou plus, ou dans la zone de données et une autre zone en même temps.", "SSE.Controllers.Main.errorOpenWarning": "La longueur de l'une des formules dans le fichier a dépassé le nombre de caractères autorisé (8192).<br> La formule a été supprimée.", "SSE.Controllers.Main.errorOperandExpected": "La syntaxe de la saisie est incorrecte. Veuillez vérifier si l'une des parenthèses - '(' ou ')' est manquante.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "Le mot de passe que vous avez fourni n'est pas correct.<br>Vérifiez que la touche CAPS LOCK est désactivée et veillez à utiliser la bonne majuscule.", "SSE.Controllers.Main.errorPasteInPivot": "Nous ne pouvons pas effectuer cette modification pour les cellules sélectionnées, car cela affecterait un tableau croisé dynamique.<br>Utilisez la liste des champs pour modifier le rapport.", "SSE.Controllers.Main.errorPasteMaxRange": "La zone de copie ne correspond pas à la zone de collage.<br>Sélectionnez une zone avec la même taille ou cliquez sur la première cellule d'une ligne pour coller les cellules sélectionnées.", "SSE.Controllers.Main.errorPasteMultiSelect": "Cette action ne peut pas être accomplie sur une sélection de plusieurs plages.<br>électionnez une seule plage, puis réessayez.", "SSE.Controllers.Main.errorPasteSlicerError": "Segments de tableau ne peuvent pas être copiés depuis un classeur vers un autre.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Le nom du champ du tableau croisé dynamique existe déjà.", "SSE.Controllers.Main.errorPivotGroup": "Impossible de grouper cette sélection.", "SSE.Controllers.Main.errorPivotOverlap": "Report du tableau croise dynamique ne peut pas chevaucher un autre tableau.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Le rapport de tableau croisé dynamique a été enregistré sans ses données sous-jacentes.<br><PERSON><PERSON><PERSON><PERSON> la commande « Actualiser » pour mettre à jour le rapport.", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "La commande Tracer les précédents exige que la cellule active contienne une formule qui inclut des références valides.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "Malheureusement, il n’est pas possible d’imprimer plus de 1500 pages à la fois en utilisant avec la version actuelle.<br>Cette restriction sera supprimée dans une version future.", "SSE.Controllers.Main.errorProcessSaveResult": "Échec de l'enregistrement", "SSE.Controllers.Main.errorProtectedRange": "Cette plage ne peut pas être modifiée.", "SSE.Controllers.Main.errorSaveWatermark": "Ce fichier comporte une image en filigrane qui est associée à un autre domaine.<br>Pour la rendre visible dans le fichier PDF, veuillez mettre à jour l'image en filigrane, car l'image doit correspondre au même domaine que le document, ou veuillez télécharger l'image depuis votre ordinateur.", "SSE.Controllers.Main.errorServerVersion": "La version de l'éditeur a été mise à jour. La page sera rechargée pour appliquer les modifications.", "SSE.Controllers.Main.errorSessionAbsolute": "Votre session a expiré. Veuillez recharger la page.", "SSE.Controllers.Main.errorSessionIdle": "Le document n'a pas été modifié depuis trop longtemps. Veuillez recharger la page.", "SSE.Controllers.Main.errorSessionToken": "La connexion au serveur a été interrompue. Veuillez recharger la page.", "SSE.Controllers.Main.errorSetPassword": "Le mot de passe ne peut pas être configuré", "SSE.Controllers.Main.errorSingleColumnOrRowError": "La référence de localisation n'est pas valide puisque les cellules ne sont pas d'une même colonne ou ligne.<br>Sélectionnez les cellules  de la même colonne ou ligne.", "SSE.Controllers.Main.errorStockChart": "Ordre lignes incorrect. <PERSON><PERSON> créer un diagramme boursier, positionnez les données sur la feuille de calcul dans l'ordre suivant :<br>cours à l'ouverture, cours maximal, cours minimal, cours à la clôture.", "SSE.Controllers.Main.errorToken": "Le jeton de sécurité du document n’était pas formé correctement.<br>V<PERSON>illez contacter l'administrateur de Document Server.", "SSE.Controllers.Main.errorTokenExpire": "Le jeton de sécurité du document a expiré.<br>Veuillez contactez l'administrateur de Document Server.", "SSE.Controllers.Main.errorUnexpectedGuid": "Erreur externe.<br>GUID non prévue. Si l'erreur persiste veillez contactez l'assistance technique.", "SSE.Controllers.Main.errorUpdateVersion": "La version du fichier a été changée. La page sera rechargée.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "La connexion a été rétablie et la version du fichier a été modifiée.<br>Avant de pouvoir continuer à travailler, vous devez télécharger le fichier ou copier son contenu pour vous assurer que rien n'est perdu, puis recharger cette page.", "SSE.Controllers.Main.errorUserDrop": "Impossible d'accéder au fichier", "SSE.Controllers.Main.errorUsersExceed": "Le nombre d'utilisateurs autorisés par la licence a été dépassé", "SSE.Controllers.Main.errorViewerDisconnect": "La connexion a été perdue. Vous pouvez toujours afficher le document,<br>mais ne pouvez pas le télécharger jusqu'à ce que la connexion soit rétablie et que la page soit actualisée.", "SSE.Controllers.Main.errorWrongBracketsCount": "Une erreur dans la formule entrée.<br>Nombre utilisé entre parenthèses est incorrect.", "SSE.Controllers.Main.errorWrongOperator": "Une erreur dans la formule entrée. Opérateur utilisé est incorrect.<br><PERSON><PERSON><PERSON><PERSON> corriger l'erreur.", "SSE.Controllers.Main.errorWrongPassword": "Le mot de passe que vous avez fourni n'est pas correct.", "SSE.Controllers.Main.errRemDuplicates": "Valeurs en double qui ont été retrouvées et supprimées: {0}, valeurs uniques restantes: {1}.", "SSE.Controllers.Main.leavePageText": "Vous avez des modifications non enregistrées dans cette feuille de calcul. C<PERSON>z sur 'Rester sur cette page ' ensuite 'Enregistrer' pour les enregistrer. Cliquez sur 'Quitter cette page' pour annuler toutes les modifications non enregistrées.", "SSE.Controllers.Main.leavePageTextOnClose": "Toutes les modifications non-enregistrées seront perdues.<br> <PERSON><PERSON><PERSON> \"Annuler\" et ensuite \"Enregistrer\" pour les sauvegarder. Cliquez \"OK\" pour effacer toutes les modifications non-enregistrées.", "SSE.Controllers.Main.loadFontsTextText": "Chargement des données en cours...", "SSE.Controllers.Main.loadFontsTitleText": "Chargement des données", "SSE.Controllers.Main.loadFontTextText": "Chargement des données en cours...", "SSE.Controllers.Main.loadFontTitleText": "Chargement des données", "SSE.Controllers.Main.loadImagesTextText": "Chargement des images en cours...", "SSE.Controllers.Main.loadImagesTitleText": "Chargement des images", "SSE.Controllers.Main.loadImageTextText": "Chargement d'une image en cours...", "SSE.Controllers.Main.loadImageTitleText": "Chargement d'une image", "SSE.Controllers.Main.loadingDocumentTitleText": "Chargement d'une feuille de calcul", "SSE.Controllers.Main.notcriticalErrorTitle": "Avertissement", "SSE.Controllers.Main.openErrorText": "Une erreur s’est produite lors de l’ouverture du fichier.", "SSE.Controllers.Main.openTextText": "Ouverture de la feuille de calcul en cours...", "SSE.Controllers.Main.openTitleText": "Ouverture de la feuille de calcul", "SSE.Controllers.Main.pastInMergeAreaError": "Impossible de modifier une partie d'une cellule fusionnée", "SSE.Controllers.Main.printTextText": "Impression de la feuille de calcul en cours...", "SSE.Controllers.Main.printTitleText": "Impression de la feuille de calcul", "SSE.Controllers.Main.reloadButtonText": "Recharger la page", "SSE.Controllers.Main.requestEditFailedMessageText": "Quelqu'un est en train de modifier ce document. Veuillez réessayer plus tard.", "SSE.Controllers.Main.requestEditFailedTitleText": "<PERSON><PERSON>ès refusé", "SSE.Controllers.Main.saveErrorText": "Une erreur s'est produite lors de l'enregistrement du fichier.", "SSE.Controllers.Main.saveErrorTextDesktop": "Ce fichier ne peut pas être enregistré ou créé.<br>Les raisons possibles sont : <br>1. Le fichier est en lecture seule. <br>2. Le fichier est en cours d'édition par d'autres utilisateurs. <br>3. le disque est plein ou corrompu.", "SSE.Controllers.Main.saveTextText": "Enregistrement de la feuille de calcul en cours ...", "SSE.Controllers.Main.saveTitleText": "Enregistrement  de la feuille de calcul", "SSE.Controllers.Main.scriptLoadError": "La connexion est trop lente, certains  éléments ne peuvent pas être chargés. Veuillez recharger la page.", "SSE.Controllers.Main.textAnonymous": "Anonyme", "SSE.Controllers.Main.textApplyAll": "Appliquer à toutes les équations", "SSE.Controllers.Main.textBuyNow": "Visiter le site web", "SSE.Controllers.Main.textChangesSaved": "Toutes les modifications ont été enregistrées", "SSE.Controllers.Main.textClose": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textCloseTip": "Cliquez pour fermer la conseil", "SSE.Controllers.Main.textConfirm": "Confirmation", "SSE.Controllers.Main.textConnectionLost": "Essai de connexion. Veuillez vérifier les paramètres de connexion.", "SSE.Controllers.Main.textContactUs": "Contacter l'équipe de ventes", "SSE.Controllers.Main.textContinue": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textConvertEquation": "Cette équation a été créée avec une ancienne version de l'éditeur des équations qui n'est plus disponible. Pour la modifier, convertissez-la au format Office Math ML.<br>Convertir maintenant ?", "SSE.Controllers.Main.textCustomLoader": "Veuillez noter que conformément aux clauses du contrat de licence vous n'êtes pas autorisé à changer le chargeur.<br>Veuillez contacter notre Service des Ventes pour obtenir le devis.", "SSE.Controllers.Main.textDisconnect": "La connexion est perdue", "SSE.Controllers.Main.textFillOtherRows": "Remp<PERSON>r d'autres lignes", "SSE.Controllers.Main.textFormulaFilledAllRows": "La formule a rempli {0} lignes contenant des données. Le remplissage des autres lignes vides peut prendre quelques minutes.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "La formule a rempli les premières {0} lignes. Le remplissage des autres lignes vides peut prendre quelques minutes.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "La formule ne remplit que les premières {0} lignes de données pour des raisons de sauvegarde de la mémoire. Il y a d'autres {1} lignes contenant des données dans cette feuille. Vous pouvez les remplir manuellement.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "La formule ne remplit que les premières {0} lignes par souci de sauvegarde de la mémoire. Les autres lignes de cette feuille ne contiennent pas de données.", "SSE.Controllers.Main.textGuest": "Invi<PERSON>", "SSE.Controllers.Main.textHasMacros": "Le fichier contient des macros automatiques.<br><PERSON><PERSON><PERSON><PERSON><PERSON>vous exécuter les macros ?", "SSE.Controllers.Main.textKeep": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.textLearnMore": "En savoir plus", "SSE.Controllers.Main.textLoadingDocument": "Chargement d'une feuille de calcul", "SSE.Controllers.Main.textLongName": "Entrez un nom qui a moins de 128 caractères.", "SSE.Controllers.Main.textNeedSynchronize": "<PERSON><PERSON> avez des mises à jour", "SSE.Controllers.Main.textNo": "Non", "SSE.Controllers.Main.textNoLicenseTitle": "La limite de la licence est atteinte", "SSE.Controllers.Main.textPaidFeature": "Fonction payante", "SSE.Controllers.Main.textPleaseWait": "L'opération peut prendre plus de temps que prévu. Veuillez patienter...", "SSE.Controllers.Main.textReconnect": "La connexion est restaurée", "SSE.Controllers.Main.textRemember": "Se souvenir de mon choix pour tous les fichiers", "SSE.Controllers.Main.textRememberMacros": "Retenir mes choix pour toutes les macros", "SSE.Controllers.Main.textRenameError": "Le nom d'utilisateur ne peut être vide.", "SSE.Controllers.Main.textRenameLabel": "Entrez un nom à utiliser pour la collaboration", "SSE.Controllers.Main.textReplace": "<PERSON><PERSON>lace<PERSON>", "SSE.Controllers.Main.textRequestMacros": "Une macro fait une demande à l'URL. Voulez-vous autoriser la demande à l'adresse %1 ?", "SSE.Controllers.Main.textShape": "Forme", "SSE.Controllers.Main.textStrict": "Mode strict", "SSE.Controllers.Main.textText": "Texte", "SSE.Controllers.Main.textTryQuickPrint": "Vous avez sélectionné Impression rapide : l'ensemble du document sera imprimé sur la dernière imprimante sélectionnée ou celle par défaut.<br><PERSON><PERSON><PERSON><PERSON>-vous continuer ?", "SSE.Controllers.Main.textTryUndoRedo": "Les fonctions annuler/rétablir sont désactivées pour en mode \"co-édition rapide\".<br><PERSON><PERSON><PERSON> sur le bouton \"Mode strict\" pour changer de mode et pouvoir modifier le fichier sans interférence avec d'autres utilisateurs puis envoyer vos modifications seulement après l'enregistrement. Vous pouvez basculer entre les différents modes de \"co-édition\" à l'aide via les paramètres avancés de l'éditeur.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Les fonctions Annuler/Rétablir sont désactivées pour le mode de co-édition rapide.", "SSE.Controllers.Main.textUndo": "Annuler", "SSE.Controllers.Main.textUpdateVersion": "Le document ne peut pas être édité pour le moment.<br>Tentative de mise à jour du fichier, veuil<PERSON><PERSON> patienter...", "SSE.Controllers.Main.textUpdating": "Mise à jour", "SSE.Controllers.Main.textYes": "O<PERSON>", "SSE.Controllers.Main.titleLicenseExp": "Licence expirée", "SSE.Controllers.Main.titleLicenseNotActive": "Licence non active", "SSE.Controllers.Main.titleServerVersion": "L'éditeur est mis à jour", "SSE.Controllers.Main.titleUpdateVersion": "La version a été modifiée", "SSE.Controllers.Main.txtAccent": "Accentuation", "SSE.Controllers.Main.txtAll": "(Tous)", "SSE.Controllers.Main.txtArt": "Votre texte ici", "SSE.Controllers.Main.txtBasicShapes": "Formes de base", "SSE.Controllers.Main.txtBlank": "(vide)", "SSE.Controllers.Main.txtButtons": "Boutons", "SSE.Controllers.Main.txtByField": "%1 de %2", "SSE.Controllers.Main.txtCallouts": "Légendes", "SSE.Controllers.Main.txtCharts": "Graphiques", "SSE.Controllers.Main.txtClearFilter": "<PERSON><PERSON><PERSON><PERSON> le filtre", "SSE.Controllers.Main.txtColLbls": "Étiquettes de colonnes", "SSE.Controllers.Main.txtColumn": "Colonne", "SSE.Controllers.Main.txtConfidential": "Confidentiel", "SSE.Controllers.Main.txtDate": "Date", "SSE.Controllers.Main.txtDays": "jours", "SSE.Controllers.Main.txtDiagramTitle": "Titre du graphique", "SSE.Controllers.Main.txtEditingMode": "Réglage mode d'édition...", "SSE.Controllers.Main.txtErrorLoadHistory": "Chargement de l'historique a échoué", "SSE.Controllers.Main.txtFiguredArrows": "Flèches figurées", "SSE.Controllers.Main.txtFile": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtGrandTotal": "Total général", "SSE.Controllers.Main.txtGroup": "Grouper", "SSE.Controllers.Main.txtHours": "heures", "SSE.Controllers.Main.txtInfo": "Infos", "SSE.Controllers.Main.txtLines": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtMath": "Maths", "SSE.Controllers.Main.txtMinutes": "minutes", "SSE.Controllers.Main.txtMonths": "mois", "SSE.Controllers.Main.txtMultiSelect": "Sélection multiple", "SSE.Controllers.Main.txtNone": "Aucun", "SSE.Controllers.Main.txtOr": "%1 ou %2", "SSE.Controllers.Main.txtPage": "Page", "SSE.Controllers.Main.txtPageOf": "Page %1 de %2", "SSE.Controllers.Main.txtPages": "Pages", "SSE.Controllers.Main.txtPicture": "Image", "SSE.Controllers.Main.txtPivotTable": "Tableau croisé dynamique", "SSE.Controllers.Main.txtPreparedBy": "Préparé par", "SSE.Controllers.Main.txtPrintArea": "Zone_d'impression", "SSE.Controllers.Main.txtQuarter": "<PERSON><PERSON>", "SSE.Controllers.Main.txtQuarters": "Quartiers", "SSE.Controllers.Main.txtRectangles": "Rectangles", "SSE.Controllers.Main.txtRow": "Ligne", "SSE.Controllers.Main.txtRowLbls": "Etiquettes de ligne", "SSE.Controllers.Main.txtSaveCopyAsComplete": "La copie du fichier a été enregistrée avec succès", "SSE.Controllers.Main.txtScheme_Aspect": "Aspect", "SSE.Controllers.Main.txtScheme_Blue": "Bleu", "SSE.Controllers.Main.txtScheme_Blue_Green": "Bleu vert", "SSE.Controllers.Main.txtScheme_Blue_II": "Bleu II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "<PERSON><PERSON><PERSON> chaud", "SSE.Controllers.Main.txtScheme_Grayscale": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Green": "<PERSON>ert", "SSE.Controllers.Main.txtScheme_Green_Yellow": "<PERSON><PERSON> jaune", "SSE.Controllers.Main.txtScheme_Marquee": "Capitaux", "SSE.Controllers.Main.txtScheme_Median": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Office": "Office", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Orange", "SSE.Controllers.Main.txtScheme_Orange_Red": "Orange rouge", "SSE.Controllers.Main.txtScheme_Paper": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtScheme_Red": "Rouge", "SSE.Controllers.Main.txtScheme_Red_Orange": "Rouge orange", "SSE.Controllers.Main.txtScheme_Red_Violet": "Rouge violet", "SSE.Controllers.Main.txtScheme_Slipstream": "Flux d'air", "SSE.Controllers.Main.txtScheme_Violet": "Violet", "SSE.Controllers.Main.txtScheme_Violet_II": "Violet II", "SSE.Controllers.Main.txtScheme_Yellow": "Jaune", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "Jaune orange", "SSE.Controllers.Main.txtSeconds": "Secondes", "SSE.Controllers.Main.txtSeries": "Série", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Légende encadrée avec une bordure 1", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Légende encadrée avec une bordure 2", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Légende encadrée avec une bordure 3", "SSE.Controllers.Main.txtShape_accentCallout1": "Légende à une bordure 1", "SSE.Controllers.Main.txtShape_accentCallout2": "Légende à une bordure 2", "SSE.Controllers.Main.txtShape_accentCallout3": "Légende à une bordure 3", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Bouton retour ou précédent", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Bouton Au début", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Bouton vide", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Bouton Document", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Bouton Fin", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Bouton Suivant", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Bouton Aide", "SSE.Controllers.Main.txtShape_actionButtonHome": "Bouton Accueil", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Bouton Informations", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Bouton Vidéo", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Bouton Retour", "SSE.Controllers.Main.txtShape_actionButtonSound": "Bouton Son", "SSE.Controllers.Main.txtShape_arc": "Arc", "SSE.Controllers.Main.txtShape_bentArrow": "<PERSON><PERSON><PERSON><PERSON> cour<PERSON>", "SSE.Controllers.Main.txtShape_bentConnector5": "Connecteur en angle", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Connecteur en angle avec flèche\t", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Connecteur en angle avec deux flèches", "SSE.Controllers.Main.txtShape_bentUpArrow": "Flèche courbe vers le haut", "SSE.Controllers.Main.txtShape_bevel": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_blockArc": "Arc plein", "SSE.Controllers.Main.txtShape_borderCallout1": "Légende encadrée 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Légende encadrée 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Légende encadrée 3", "SSE.Controllers.Main.txtShape_bracePair": "Accolades", "SSE.Controllers.Main.txtShape_callout1": "Légende encadrée sans bordure 1", "SSE.Controllers.Main.txtShape_callout2": "Légende encadrée sans bordure 2", "SSE.Controllers.Main.txtShape_callout3": "Légende encadrée sans bordure 3", "SSE.Controllers.Main.txtShape_can": "Cylindre", "SSE.Controllers.Main.txtShape_chevron": "Chevron", "SSE.Controllers.Main.txtShape_chord": "Corde", "SSE.Controllers.Main.txtShape_circularArrow": "Flèche en arc\t", "SSE.Controllers.Main.txtShape_cloud": "Cloud", "SSE.Controllers.Main.txtShape_cloudCallout": "Pensées", "SSE.Controllers.Main.txtShape_corner": "Coin", "SSE.Controllers.Main.txtShape_cube": "C<PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Connecteur en arc ", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Connecteur en arc avec flèche", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Connecteur en arc avec deux flèches", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Flèche courbée vers le bas", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Flèche courbée vers la gauche", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Flèche courbée vers la droite", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Flèche courbée vers le haut", "SSE.Controllers.Main.txtShape_decagon": "Décagone", "SSE.Controllers.Main.txtShape_diagStripe": "Bande diagonale", "SSE.Controllers.Main.txtShape_diamond": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_dodecagon": "Dodécagone", "SSE.Controllers.Main.txtShape_donut": "Bo<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_doubleWave": "Double vague", "SSE.Controllers.Main.txtShape_downArrow": "Flèche vers le bas", "SSE.Controllers.Main.txtShape_downArrowCallout": "Rectangle avec flèche vers le bas", "SSE.Controllers.Main.txtShape_ellipse": "Ellipse", "SSE.Controllers.Main.txtShape_ellipseRibbon": "R<PERSON>n courbé vers le bas", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "R<PERSON>n courbé vers le haut", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Organigramme: Alternative", "SSE.Controllers.Main.txtShape_flowChartCollate": "Organigramme: <PERSON><PERSON><PERSON>\t", "SSE.Controllers.Main.txtShape_flowChartConnector": "Organigramme: Connecteur", "SSE.Controllers.Main.txtShape_flowChartDecision": "Organigramme: Decision", "SSE.Controllers.Main.txtShape_flowChartDelay": "Organigramme: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Organigramme : Affichage", "SSE.Controllers.Main.txtShape_flowChartDocument": "Organigramme: Document", "SSE.Controllers.Main.txtShape_flowChartExtract": "Organigramme: Extraire", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Organigramme: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Organigramme: Stockage interne", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Organigramme: Disque magnétique", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Organigramme: Stockage à accès direct", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Organigramme: Stockage à accès séquentiel\t", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Organigramme: <PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Organigramme: Opération manuelle", "SSE.Controllers.Main.txtShape_flowChartMerge": "Organigramme: Fusion\t", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Organigramme : Multidocument", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Organigramme : Connecteur page suivante", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Organigramme: Donn<PERSON> stockées", "SSE.Controllers.Main.txtShape_flowChartOr": "Organigramme: OU", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Organigramme: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Organigramme: Préparation", "SSE.Controllers.Main.txtShape_flowChartProcess": "Organigramme: <PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Organigramme: <PERSON><PERSON> perfo<PERSON>", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Organigramme: Bande perforée", "SSE.Controllers.Main.txtShape_flowChartSort": "Organigramme : <PERSON><PERSON>", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Organigramme: <PERSON><PERSON> de sommaire", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Organigramme: Terminaison", "SSE.Controllers.Main.txtShape_foldedCorner": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_frame": "Cadre", "SSE.Controllers.Main.txtShape_halfFrame": "Demi-cadre", "SSE.Controllers.Main.txtShape_heart": "Coeur", "SSE.Controllers.Main.txtShape_heptagon": "Heptagone", "SSE.Controllers.Main.txtShape_hexagon": "Hexagon", "SSE.Controllers.Main.txtShape_homePlate": "Pentagone", "SSE.Controllers.Main.txtShape_horizontalScroll": "Parchemin horizontal", "SSE.Controllers.Main.txtShape_irregularSeal1": "Éclatement 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Éclatement 2", "SSE.Controllers.Main.txtShape_leftArrow": "Flèche gauche", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Rectangle avec flèche vers la gauche\t", "SSE.Controllers.Main.txtShape_leftBrace": "Accolade ouvrante", "SSE.Controllers.Main.txtShape_leftBracket": "Parenthèse ouvrante", "SSE.Controllers.Main.txtShape_leftRightArrow": "Flèche bilatérale", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Rectangle horizontal à deux flèches", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Flèche à trois pointes", "SSE.Controllers.Main.txtShape_leftUpArrow": "Double flèche horizontale", "SSE.Controllers.Main.txtShape_lightningBolt": "Éclair", "SSE.Controllers.Main.txtShape_line": "Ligne", "SSE.Controllers.Main.txtShape_lineWithArrow": "Flèche", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Flèche à deux pointes", "SSE.Controllers.Main.txtShape_mathDivide": "Division ", "SSE.Controllers.Main.txtShape_mathEqual": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON>ins", "SSE.Controllers.Main.txtShape_mathMultiply": "Multiplication", "SSE.Controllers.Main.txtShape_mathNotEqual": "<PERSON>ff<PERSON><PERSON> de", "SSE.Controllers.Main.txtShape_mathPlus": "Plus", "SSE.Controllers.Main.txtShape_moon": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_noSmoking": "Signe \"Interdiction\"", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Flèche droite à entaille\t", "SSE.Controllers.Main.txtShape_octagon": "Octogone", "SSE.Controllers.Main.txtShape_parallelogram": "Parallélogramme", "SSE.Controllers.Main.txtShape_pentagon": "Pentagone", "SSE.Controllers.Main.txtShape_pie": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_plaque": "<PERSON>e", "SSE.Controllers.Main.txtShape_plus": "Plus", "SSE.Controllers.Main.txtShape_polyline1": "Dessin à main levée", "SSE.Controllers.Main.txtShape_polyline2": "Forme libre", "SSE.Controllers.Main.txtShape_quadArrow": "Flèche à quatre pointes", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Rectangle à quatre flèches\t", "SSE.Controllers.Main.txtShape_rect": "Rectangle", "SSE.Controllers.Main.txtShape_ribbon": "R<PERSON>n vers le bas", "SSE.Controllers.Main.txtShape_ribbon2": "R<PERSON><PERSON> vers le haut", "SSE.Controllers.Main.txtShape_rightArrow": "Flèche droite", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Rectangle avec flèche vers le droit", "SSE.Controllers.Main.txtShape_rightBrace": "Accolade fermante", "SSE.Controllers.Main.txtShape_rightBracket": "Parenthèse fermante", "SSE.Controllers.Main.txtShape_round1Rect": "Rectangle arrondi à un seul coin", "SSE.Controllers.Main.txtShape_round2DiagRect": "Rectangle avec un coin diagonal rond", "SSE.Controllers.Main.txtShape_round2SameRect": "Rectangle arrondi avec un coin du même côté", "SSE.Controllers.Main.txtShape_roundRect": "Rectangle à coins arrondis", "SSE.Controllers.Main.txtShape_rtTriangle": "Triangle rectangle", "SSE.Controllers.Main.txtShape_smileyFace": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_snip1Rect": "Rogner un rectangle à un seul coin", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Couper un rectangle avec un coin diagonal", "SSE.Controllers.Main.txtShape_snip2SameRect": "Couper un rectangle avec un coin du même côté", "SSE.Controllers.Main.txtShape_snipRoundRect": "<PERSON><PERSON><PERSON> et arrondir un rectangle à un seul coin", "SSE.Controllers.Main.txtShape_spline": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_star10": "Étoile à 10 branches", "SSE.Controllers.Main.txtShape_star12": "Étoile à 12 branches", "SSE.Controllers.Main.txtShape_star16": "Étoile à 16 branches", "SSE.Controllers.Main.txtShape_star24": "Étoile à 24 branches", "SSE.Controllers.Main.txtShape_star32": "Étoile à 32 branches", "SSE.Controllers.Main.txtShape_star4": "Étoile à 4 branches", "SSE.Controllers.Main.txtShape_star5": "Étoile à 5 branches", "SSE.Controllers.Main.txtShape_star6": "Étoile à 6 branches", "SSE.Controllers.Main.txtShape_star7": "Étoile à 7 branches", "SSE.Controllers.Main.txtShape_star8": "Étoile à 8 branches", "SSE.Controllers.Main.txtShape_stripedRightArrow": "flèche vers la droite  rayée", "SSE.Controllers.Main.txtShape_sun": "<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_teardrop": "<PERSON><PERSON><PERSON> ", "SSE.Controllers.Main.txtShape_textRect": "Zone de texte", "SSE.Controllers.Main.txtShape_trapezoid": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_triangle": "Triangle", "SSE.Controllers.Main.txtShape_upArrow": "Flèche vers le haut", "SSE.Controllers.Main.txtShape_upArrowCallout": "Rectangle avec flèche vers le haut", "SSE.Controllers.Main.txtShape_upDownArrow": "Double flèche verticale", "SSE.Controllers.Main.txtShape_uturnArrow": "Demi-tour", "SSE.Controllers.Main.txtShape_verticalScroll": "Défilement vertical", "SSE.Controllers.Main.txtShape_wave": "<PERSON>de", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Bulle ronde", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Rectangle", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Rectangle à coins arrondis", "SSE.Controllers.Main.txtSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSlicer": "Segment", "SSE.Controllers.Main.txtStarsRibbons": "Étoiles et rubans", "SSE.Controllers.Main.txtStyle_Bad": "Incorrect", "SSE.Controllers.Main.txtStyle_Calculation": "Calcul", "SSE.Controllers.Main.txtStyle_Check_Cell": "Vérifier la cellule", "SSE.Controllers.Main.txtStyle_Comma": "Vir<PERSON>le", "SSE.Controllers.Main.txtStyle_Currency": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Texte d'explication", "SSE.Controllers.Main.txtStyle_Good": "Correct", "SSE.Controllers.Main.txtStyle_Heading_1": "Titre 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Titre 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Titre 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Titre 4", "SSE.Controllers.Main.txtStyle_Input": "Entrée", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Cellule liée", "SSE.Controllers.Main.txtStyle_Neutral": "Neutre", "SSE.Controllers.Main.txtStyle_Normal": "Normal", "SSE.Controllers.Main.txtStyle_Note": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Output": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Percent": "Pour cent", "SSE.Controllers.Main.txtStyle_Title": "Titre", "SSE.Controllers.Main.txtStyle_Total": "Total", "SSE.Controllers.Main.txtStyle_Warning_Text": "Texte d'avertissement", "SSE.Controllers.Main.txtTab": "Onglet", "SSE.Controllers.Main.txtTable": "<PERSON><PERSON>", "SSE.Controllers.Main.txtTime": "<PERSON><PERSON>", "SSE.Controllers.Main.txtUnlock": "Déverrouiller", "SSE.Controllers.Main.txtUnlockRange": "Déverouiller la plage", "SSE.Controllers.Main.txtUnlockRangeDescription": "Entrez le mot de passe pour modifier cette plage :", "SSE.Controllers.Main.txtUnlockRangeWarning": "La plage que vous essayez de modifier est protégée par un mot de passe.", "SSE.Controllers.Main.txtValues": "Valeurs", "SSE.Controllers.Main.txtView": "Affichage", "SSE.Controllers.Main.txtXAxis": "Axe X", "SSE.Controllers.Main.txtYAxis": "Axe Y", "SSE.Controllers.Main.txtYears": "ann<PERSON>", "SSE.Controllers.Main.unknownErrorText": "<PERSON><PERSON><PERSON> inconnue.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Votre navigateur n'est pas pris en charge.", "SSE.Controllers.Main.uploadDocExtMessage": "Format de fichier inconnu.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Aucun fichier n'a été chargé.", "SSE.Controllers.Main.uploadDocSizeMessage": "La taille du fichier dépasse la limite autorisée.", "SSE.Controllers.Main.uploadImageExtMessage": "Format d'image inconnu.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Pas d'images chargées.", "SSE.Controllers.Main.uploadImageSizeMessage": "L'image est trop grande. La taille limite est de 25 Mo.", "SSE.Controllers.Main.uploadImageTextText": "Chargement d'une image en cours...", "SSE.Controllers.Main.uploadImageTitleText": "Chargement d'une image", "SSE.Controllers.Main.waitText": "Veuillez patienter...", "SSE.Controllers.Main.warnBrowserIE9": "L'application est peu compatible avec IE9. Utilisez IE10 ou version plus récente", "SSE.Controllers.Main.warnBrowserZoom": "Le paramètre actuel de zoom de votre navigateur n'est pas accepté. Veuillez rétablir le niveau de zoom par défaut en appuyant sur Ctrl+0.", "SSE.Controllers.Main.warnLicenseAnonymous": "Accès refusé aux utilisateurs anonymes.<br>Ce document sera accessible en lecture seule uniquement.", "SSE.Controllers.Main.warnLicenseBefore": "Licence non active.<br>V<PERSON>illez contacter votre administrateur.", "SSE.Controllers.Main.warnLicenseExceeded": "Vous avez dépassé le nombre maximal de connexions simultanées aux éditeurs %1. Ce document sera ouvert à la lecture seulement.<br>Contactez votre administrateur pour en savoir davantage.", "SSE.Controllers.Main.warnLicenseExp": "Votre licence a expiré.<br>Veuillez mettre à jour votre licence et actualisez la page.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "La licence est expirée.<br>V<PERSON> n'avez plus d'accès aux outils d'édition.<br><PERSON><PERSON><PERSON>z contacter votre administrateur.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Il est indispensable de renouveler la licence.<br>Vous avez un accès limité aux outils d'édition des documents.<br><PERSON><PERSON><PERSON>z contacter votre administrateur pour obtenir un accès complet", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Vous avez dépassé le nombre maximal d’utilisateurs des éditeurs %1. Contactez votre administrateur pour en savoir davantage.", "SSE.Controllers.Main.warnNoLicense": "Vous avez dépassé le nombre maximal de connexions simultanées aux éditeurs %1. Ce document sera ouvert à la lecture seulement.<br>Contactez l’équipe des ventes %1 pour mettre à jour les termes de la licence.", "SSE.Controllers.Main.warnNoLicenseUsers": "Vous avez dépassé le nombre maximal d’utilisateurs des éditeurs %1. Contactez l’équipe des ventes %1 pour mettre à jour les termes de la licence.", "SSE.Controllers.Main.warnProcessRightsChange": "Le droit d'édition du fichier vous a été refusé.", "SSE.Controllers.PivotTable.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "L'élément ne peut être ni ajouté ni modifié. Le rapport du tableau croisé dynamique contient ce champ dans les filtres.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "Aucune action avec des éléments calculés n'est autorisée pour cette cellule active.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "Si un ou plusieurs tableaux croisés dynamiques comportent des éléments calculés, aucun champ ne peut être utilisé dans la zone de données deux fois ou plus, ou dans la zone de données et une autre zone en même temps.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Les éléments calculés ne fonctionnent pas avec les sous-totaux personnalisés.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "Le nom de l'élément est introuvable. Veuillez vérifier que vous avez tapé le nom correctement et que l'élément est présent dans le rapport du tableau croisé dynamique.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Les moyennes, les écarts types et les variances ne sont pas pris en charge lorsqu'un rapport du tableau croisé dynamique comporte des éléments calculés.", "SSE.Controllers.Print.strAllSheets": "Toutes les feuilles", "SSE.Controllers.Print.textFirstCol": "Première colonne", "SSE.Controllers.Print.textFirstRow": "Première ligne", "SSE.Controllers.Print.textFrozenCols": "Colonnes verrouillées", "SSE.Controllers.Print.textFrozenRows": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Print.textInvalidRange": "ERREUR ! La plage de cellules n'est pas valide", "SSE.Controllers.Print.textNoRepeat": "Ne pas répéter", "SSE.Controllers.Print.textRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "SSE.Controllers.Print.textSelectRange": "Sélectionner la ligne", "SSE.Controllers.Print.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Print.txtZoomToPage": "Ajuster à la page", "SSE.Controllers.Search.textInvalidRange": "ERREUR ! Plage de cellules invalide", "SSE.Controllers.Search.textNoTextFound": "Les données que vous recherchez n'ont pas pu être trouvées. Veuillez modifier vos options de recherche.", "SSE.Controllers.Search.textReplaceSkipped": "Le remplacement a été effectué. {0} occurrences ont été sautées.", "SSE.Controllers.Search.textReplaceSuccess": "La recherche a été effectuée. {0} occurrences ont été remplacées", "SSE.Controllers.Statusbar.errorLastSheet": "Le classeur doit avoir au moins une feuille visible.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Impossible de supprimer la feuille de calcul.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>La connexion est perdue</b><br>Tentative de connexion. Veuillez vérifier les paramètres de connexion.", "SSE.Controllers.Statusbar.textSheetViewTip": "Vous avez activé le mode Feuille. Les filtres et les tris sont visibles uniquement à vous et à ceux/celles qui ont activé ce mode.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Vous êtes een mode Feuille. Les filtres sont visibles seulement à vous et à ceux qui sont aussi dans ce mode. ", "SSE.Controllers.Statusbar.warnDeleteSheet": "Les feuilles sélectionnées peuvent contenir des données. Êtes-vous sûr de vouloir continuer ?", "SSE.Controllers.Statusbar.zoomText": "Zoom {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "La police que vous allez enregistrer n'est pas disponible sur l'appareil actuel.<br>Le style du texte sera affiché à l'aide de l'une des polices de système, la police sauvée sera utilisée lorsqu'il est disponible.<br><PERSON><PERSON><PERSON><PERSON>-vous continuer?", "SSE.Controllers.Toolbar.errorComboSeries": "Pour créer un graphique combiné, sélectionnez au moins deux séries de données. ", "SSE.Controllers.Toolbar.errorMaxPoints": "Le nombre maximum de points en série par graphique est de 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "ERREUR! Maximum de 255 séries de données par graphique.", "SSE.Controllers.Toolbar.errorStockChart": "Ordre lignes incorrect. <PERSON><PERSON> créer un diagramme boursier, positionnez les données sur la feuille de calcul dans l'ordre suivant :<br>cours à l'ouverture, cours maximal, cours minimal, cours à la clôture.", "SSE.Controllers.Toolbar.helpCalcItems": "Travaillez avec des éléments calculés dans les tableaux croisés dynamiques.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Éléments calculés", "SSE.Controllers.Toolbar.helpFastUndo": "Ann<PERSON>z facilement les modifications lorsque vous collaborez sur des feuilles en mode rapide.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "« Annuler » dans la coédition en temps réel", "SSE.Controllers.Toolbar.helpMergeShapes": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, croisez, soustrayez des formes en quelques secondes pour créer des visuels personnalisés.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Fusionner les formes", "SSE.Controllers.Toolbar.textAccent": "Caractères diacritiques", "SSE.Controllers.Toolbar.textBracket": "Parenthèses", "SSE.Controllers.Toolbar.textDirectional": "Directionnel", "SSE.Controllers.Toolbar.textFontSizeErr": "La valeur entrée est incorrecte.<br>Entrez une valeur numérique entre 1 et 409", "SSE.Controllers.Toolbar.textFraction": "Fractions", "SSE.Controllers.Toolbar.textFunction": "Fonctions", "SSE.Controllers.Toolbar.textIndicator": "Indices", "SSE.Controllers.Toolbar.textInsert": "Insertion", "SSE.Controllers.Toolbar.textIntegral": "Intégrales", "SSE.Controllers.Toolbar.textLargeOperator": "Grands opérateurs", "SSE.Controllers.Toolbar.textLimitAndLog": "Limites et logarithmes ", "SSE.Controllers.Toolbar.textLongOperation": "Opération longue", "SSE.Controllers.Toolbar.textMatrix": "Matrices", "SSE.Controllers.Toolbar.textOperator": "Opérateurs", "SSE.Controllers.Toolbar.textPivot": "Tableau croisé dynamique", "SSE.Controllers.Toolbar.textRadical": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textRating": "Évaluations", "SSE.Controllers.Toolbar.textRecentlyUsed": "Récemment utilisés", "SSE.Controllers.Toolbar.textScript": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.textShapes": "Formes", "SSE.Controllers.Toolbar.textSymbols": "Symboles", "SSE.Controllers.Toolbar.textWarning": "Avertissement", "SSE.Controllers.Toolbar.txtAccent_Accent": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Flèche gauche-droite au-dessus", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Flèche vers la gauche au-dessus", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Flèche vers la droite au-dessus", "SSE.Controllers.Toolbar.txtAccent_Bar": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Barre inférieure", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Barre supérieure", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Formule encadrée (avec espace réservé)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Formule encadrée (exemple)", "SSE.Controllers.Toolbar.txtAccent_Check": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Accolade inférieure", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Accolade supérieure", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Vecteur A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC avec barre supérieure", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y avec barre supérieure", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Point triple", "SSE.Controllers.Toolbar.txtAccent_DDot": "Point double", "SSE.Controllers.Toolbar.txtAccent_Dot": "Point", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Barre supérieure double", "SSE.Controllers.Toolbar.txtAccent_Grave": "Grave", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Regroupement de caractère en dessus", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Regroupement de caractère au-dessus", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Harpon gauche au-dessus", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Harpon droite au-dessus", "SSE.Controllers.Toolbar.txtAccent_Hat": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Smile": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtAccent_Tilde": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_Angle": "<PERSON><PERSON><PERSON><PERSON> pointus", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Crochets pointus avec séparateur", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Crochets pointus avec deux séparateurs", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "C<PERSON>chet angulaire droite", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Crochet angulaire à gauche", "SSE.Controllers.Toolbar.txtBracket_Curve": "Accolades", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Accolades avec séparateur", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Accolade droite", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Accolade gauche", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Cas (deux conditions)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Cas (trois conditions)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Objet empilé", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Objet de pile entre parenthèses", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Exemple de cas", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Coefficient binomial", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Coefficient binomial entre crochets pointus", "SSE.Controllers.Toolbar.txtBracket_Line": "Barres verticales", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Barre verticale droite", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Barre verticale gauche", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "<PERSON><PERSON> verticales doubles", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Double barre verticale droite", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Double barre verticale gauche", "SSE.Controllers.Toolbar.txtBracket_LowLim": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Plancher à droite", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Plancher à gauche", "SSE.Controllers.Toolbar.txtBracket_Round": "Parenthèses", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Parenthèses avec séparateur", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Parenthèse droite", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Parenthèse gauche", "SSE.Controllers.Toolbar.txtBracket_Square": "Crochets", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Placeholder entre deux crochets droits", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Crochets inversés", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Crochet droit", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Crochet gauche", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Placeholder entre deux crochets gauches", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Crochets doubles", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Double crochet droit", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Double crochet gauche", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Plafond", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Plafond à droite", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Plafond à gauche", "SSE.Controllers.Toolbar.txtDeleteCells": "Supprimer les cellules", "SSE.Controllers.Toolbar.txtExpand": "Développer et trier", "SSE.Controllers.Toolbar.txtExpandSort": "Les données situées à côté de la sélection ne seront pas triées. Souhaitez-vous développer la sélection pour inclure les données adjacentes ou souhaitez-vous continuer à trier iniquement les cellules sélectionnées ?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Fraction oblique", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "dx sur dy", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "cap delta y sur cap delta x", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "partielle y sur partielle x", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "y delta sur delta x", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Fraction simple", "SSE.Controllers.Toolbar.txtFractionPi_2": "Pi divisé par 2", "SSE.Controllers.Toolbar.txtFractionSmall": "Petite fraction", "SSE.Controllers.Toolbar.txtFractionVertical": "Fraction sur deux lignes", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Fonction cosinus inverse", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Fonction cosinus inverse hyperbolique", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Fonction cotangente inverse", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Fonction cotangente inverse hyperbolique", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Fonction cosécante inverse", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Fonction cosécante inverse hyperbolique", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Fonction sécante inverse", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Fonction sécante inverse hyperbolique", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Fonction sinus inverse", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Fonction sinus inverse hyperbolique", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Fonction tangente inverse", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Fonction tangente inverse hyperbolique", "SSE.Controllers.Toolbar.txtFunction_Cos": "Fonction cosinus", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Fonction cosinus hyperbolique", "SSE.Controllers.Toolbar.txtFunction_Cot": "Fonction cotangente", "SSE.Controllers.Toolbar.txtFunction_Coth": "Fonction cotangente hyperbolique", "SSE.Controllers.Toolbar.txtFunction_Csc": "Fonction cosécante", "SSE.Controllers.Toolbar.txtFunction_Csch": "Fonction cosécante hyperbolique", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "<PERSON><PERSON> thêta", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cosinus 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Formule de la tangente", "SSE.Controllers.Toolbar.txtFunction_Sec": "Fonction sécante", "SSE.Controllers.Toolbar.txtFunction_Sech": "Fonction sécante hyperbolique", "SSE.Controllers.Toolbar.txtFunction_Sin": "Fonction sinus", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Fonction sinus hyperbolique", "SSE.Controllers.Toolbar.txtFunction_Tan": "Formule de la tangente", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Fonction tangente hyperbolique", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Données et modèle", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "Satisfaisant, insatisfaisant et neutre", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "Sans nom", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Format de nombre", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Styles de cellule à thème", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Titres et en-têtes", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Sombre", "SSE.Controllers.Toolbar.txtGroupTable_Light": "<PERSON>", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtInsertCells": "Insérer les cellules", "SSE.Controllers.Toolbar.txtIntegral": "Intégrale", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>", "SSE.Controllers.Toolbar.txtIntegral_dx": "Différent<PERSON> x", "SSE.Controllers.Toolbar.txtIntegral_dy": "<PERSON><PERSON><PERSON><PERSON><PERSON> y", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Intégrale avec limites empilées", "SSE.Controllers.Toolbar.txtIntegralDouble": "Double intégrale", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Intégrale double avec limites empilées", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Intégrale double avec limites", "SSE.Controllers.Toolbar.txtIntegralOriented": "Intégrale de contour", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Intégrale de contour avec limites empilées", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Intégrale de surface", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Intégrale de surface avec limites empilées", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Intégrale de surface avec limites", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Intégrale de contour avec limites", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Intégrale de volume", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Intégrale de volume avec limites empilées", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Intégrale de volume avec limites", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Intégrale avec limites", "SSE.Controllers.Toolbar.txtIntegralTriple": "Triple intégrale", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Intégrale triple avec limites empilées", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Intégrale triple avec limites", "SSE.Controllers.Toolbar.txtInvalidRange": "ERREUR! Plage de cellules non valable", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Et logique", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Et logique avec limite inférieure", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Et logique avec limites", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Et logique avec limite inférieure en indice", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Et logique avec limites en indice/exposant", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Co-produit", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Coproduit avec limite inférieure", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Coproduit avec limites", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Coproduit avec limite inférieure en indice", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Coproduit avec limites en indice/en exposant", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Somme sur k de n choix k", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Somme de i égal à zéro à n", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Exemple de somme utilisant deux indices", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Exemple de produit", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Exemple d’union", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Ou logique", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Ou logique avec limite inférieure", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Ou logique avec limites", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Ou logique avec limite inférieure en indice", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Ou logique avec limites en indice/exposant", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Intersection", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Intersection avec limite inférieure", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Intersection avec limites", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Intersection avec limite inférieure en indice", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Intersection avec limites en indice/exposant", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Produit", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Produit avec limite inférieure", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Produit avec limites", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Produit avec limite inférieure en indice", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Produit avec limites en indice/exposant", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Somme", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Somme avec limite inférieure", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Somme avec limites", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Somme avec limite inférieure en indice", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Somme avec limites en indice/exposant", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Union", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Union avec limite inférieure", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Union avec limites", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Union avec limite inférieure en indice", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Union avec limites en indice/exposant", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Exemple de limite", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Exemple de maximum", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Limite", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Logarithme naturel", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Logarithme", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Logarithme", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Maximum", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Minimum", "SSE.Controllers.Toolbar.txtLockSort": "Des données se trouvent à côté de votre sélection, mais vous n'avez pas les autorisations suffisantes pour modifier ces cellules.<br>Voulez-vous continuer avec la sélection actuelle ?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "Matrice vide 1x2 ", "SSE.Controllers.Toolbar.txtMatrix_1_3": "Matrice vide 1x3", "SSE.Controllers.Toolbar.txtMatrix_2_1": "Matrice vide 2x1", "SSE.Controllers.Toolbar.txtMatrix_2_2": "Matrice vide 2x2", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Matrice 2x2 vide avec doubles barres verticales", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Déterminant 2x2 vide", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Matrice d’identité 2x2 vide entre parenthèses", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Matrice d’identité 2x2 vide entre crochets", "SSE.Controllers.Toolbar.txtMatrix_2_3": "Matrice vide 2x3", "SSE.Controllers.Toolbar.txtMatrix_3_1": "Matrice vide 3x1", "SSE.Controllers.Toolbar.txtMatrix_3_2": "Matrice vide 3x2", "SSE.Controllers.Toolbar.txtMatrix_3_3": "Matrice vide 3x3", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Points de ligne de base", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Points d'interligne", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Points diagonaux", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Points verticaux", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Matrice avec pointillés entre parenthèses", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Matrice avec pointillés entre crochets", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "Matrice d’identité 2x2 avec zéros", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Matrice d’identité 2x2 avec cellules hors diagonale vides", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "Matrice d’identité 3x3 avec zéros", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Matrice d’identité 3x3 avec cellules hors diagonale vides", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Flèche gauche-droite en dessous", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Flèche gauche-droite au-dessus", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Flèche vers la gauche en dessous", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Flèche vers la gauche au-dessus", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Flèche vers la droite en dessous", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Flèche vers la droite au-dessus", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Deux-points É<PERSON>", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Rendement", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Rendement Delta", "SSE.Controllers.Toolbar.txtOperator_Definition": "Égal par définition à", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Delta égal à", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Double flèche gauche-droite au-dessous", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Double flèche gauche-droite au-dessus", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Flèche vers la gauche en dessous", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Flèche vers la gauche au-dessus", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Flèche vers la droite en dessous", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Flèche vers la droite au-dessus", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Plus Égal", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Unité de mesure", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Côté droit de la formule quadratique", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "<PERSON><PERSON> car<PERSON> de a au carré plus b au carré", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "<PERSON><PERSON> avec degré", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Racine cubique", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Radical avec degré", "SSE.Controllers.Toolbar.txtRadicalSqrt": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtScriptCustom_1": "x indice y au carré", "SSE.Controllers.Toolbar.txtScriptCustom_2": "e au i négatif oméga t", "SSE.Controllers.Toolbar.txtScriptCustom_3": "x au carré", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Y exposant gauche n indice gauche un", "SSE.Controllers.Toolbar.txtScriptSub": "Indice", "SSE.Controllers.Toolbar.txtScriptSubSup": "Indice-Exposant", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Indice-Exposant gauche", "SSE.Controllers.Toolbar.txtScriptSup": "Exposant", "SSE.Controllers.Toolbar.txtSorting": "Tri", "SSE.Controllers.Toolbar.txtSortSelected": "Trier l'objet sélectionné ", "SSE.Controllers.Toolbar.txtSymbol_about": "Approximativement", "SSE.Controllers.Toolbar.txtSymbol_additional": "Complément", "SSE.Controllers.Toolbar.txtSymbol_aleph": "Aleph", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Alpha", "SSE.Controllers.Toolbar.txtSymbol_approx": "Presque égale à", "SSE.Controllers.Toolbar.txtSymbol_ast": "Opérateur astérisque", "SSE.Controllers.Toolbar.txtSymbol_beta": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_beth": "Beth", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Opérateur puce", "SSE.Controllers.Toolbar.txtSymbol_cap": "Intersection", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Racine cubique", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Trois points médians", "SSE.Controllers.Toolbar.txtSymbol_celsius": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_chi": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_cong": "Approximativement égal à", "SSE.Controllers.Toolbar.txtSymbol_cup": "Union", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Trois points diagonaux vers le coin bas à droite", "SSE.Controllers.Toolbar.txtSymbol_degree": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_delta": "Delta", "SSE.Controllers.Toolbar.txtSymbol_div": "Signe de division", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Flèche vers le bas", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Ensemble vide", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Epsilon", "SSE.Controllers.Toolbar.txtSymbol_equals": "<PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Identique à", "SSE.Controllers.Toolbar.txtSymbol_eta": "Êta", "SSE.Controllers.Toolbar.txtSymbol_exists": "Existant", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Factorielle", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Degrés Fahrenheit", "SSE.Controllers.Toolbar.txtSymbol_forall": "Pour tous", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Gamma", "SSE.Controllers.Toolbar.txtSymbol_geq": "Su<PERSON><PERSON><PERSON> ou égal à", "SSE.Controllers.Toolbar.txtSymbol_gg": "Beaucoup plus grande que", "SSE.Controllers.Toolbar.txtSymbol_greater": "Sup<PERSON>ur à", "SSE.Controllers.Toolbar.txtSymbol_in": "Élément de", "SSE.Controllers.Toolbar.txtSymbol_inc": "Incrément", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Infini", "SSE.Controllers.Toolbar.txtSymbol_iota": "Iota", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Kappa", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Lambda", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Flèche gauche", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Double flèche horizontale", "SSE.Controllers.Toolbar.txtSymbol_leq": "Inférieure ou égale à", "SSE.Controllers.Toolbar.txtSymbol_less": "Inférieur à", "SSE.Controllers.Toolbar.txtSymbol_ll": "Beaucoup moins que", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON>ins", "SSE.Controllers.Toolbar.txtSymbol_mp": "Moins plus", "SSE.Controllers.Toolbar.txtSymbol_mu": "Mu", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Nabla", "SSE.Controllers.Toolbar.txtSymbol_neq": "N'est pas égal à", "SSE.Controllers.Toolbar.txtSymbol_ni": "Contient comme élément", "SSE.Controllers.Toolbar.txtSymbol_not": "Signe négation", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Inexistant", "SSE.Controllers.Toolbar.txtSymbol_nu": "<PERSON>u", "SSE.Controllers.Toolbar.txtSymbol_o": "Omicron", "SSE.Controllers.Toolbar.txtSymbol_omega": "Omega", "SSE.Controllers.Toolbar.txtSymbol_partial": "Différentielle partielle", "SSE.Controllers.Toolbar.txtSymbol_percent": "Pourcentage", "SSE.Controllers.Toolbar.txtSymbol_phi": "Phi", "SSE.Controllers.Toolbar.txtSymbol_pi": "Pi", "SSE.Controllers.Toolbar.txtSymbol_plus": "Plus", "SSE.Controllers.Toolbar.txtSymbol_pm": "Plus moins", "SSE.Controllers.Toolbar.txtSymbol_propto": "Proportionnel à", "SSE.Controllers.Toolbar.txtSymbol_psi": "Psi", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "<PERSON><PERSON> quatri<PERSON>", "SSE.Controllers.Toolbar.txtSymbol_qed": "Ce qu'il fallait démontrer", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Trois points diagonaux vers le coin haut à droite", "SSE.Controllers.Toolbar.txtSymbol_rho": "Rho", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Flèche droite", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Sigma", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Symbole de radical", "SSE.Controllers.Toolbar.txtSymbol_tau": "Tau", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Par conséquent", "SSE.Controllers.Toolbar.txtSymbol_theta": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_times": "Signe de multiplication", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Flèche vers le haut", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Upsilon", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "Variante epsilon", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Variante phi", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Variante pi", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Variante rho", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Variante sigma", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "<PERSON><PERSON><PERSON> th<PERSON>ta", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Ellipse verticale", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Xi", "SSE.Controllers.Toolbar.txtSymbol_zeta": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Style de tableau sombre", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Style de tableau clair", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Style de tableau moyen", "SSE.Controllers.Toolbar.warnLongOperation": "L'opération que vous êtes sur le point d'effectuer peut prendre beaucoup de temps.<br>Êtes-vous sûr de vouloir continuer ?", "SSE.Controllers.Toolbar.warnMergeLostData": "Seulement les données de la cellule supérieure gauche seront conservées dans la cellule fusionnée.<br>Êtes-vous sûr de vouloir continuer ?", "SSE.Controllers.Toolbar.warnNoRecommended": "Pour créer un graphique, sélectionnez les cellules qui contiennent les données que vous souhaitez utiliser. Si vous avez des noms pour les lignes et les colonnes et que vous souhaitez les utiliser comme étiquettes, incluez-les dans votre sélection.", "SSE.Controllers.Viewport.textFreezePanes": "Figer les volets", "SSE.Controllers.Viewport.textFreezePanesShadow": "Afficher les ombres des volets verrouillés", "SSE.Controllers.Viewport.textHideFBar": "Masquer la barre de formule", "SSE.Controllers.Viewport.textHideGridlines": "Masquer le quadrillage", "SSE.Controllers.Viewport.textHideHeadings": "Masquer les en-têtes", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Séparateur décimal", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Séparateur de milliers", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Paramètres de reconnaissance du format de nombre", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Qualificateur de texte", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Paramètres avancés", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(aucun)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "<PERSON><PERSON><PERSON> person<PERSON>", "SSE.Views.AutoFilterDialog.textAddSelection": "Ajouter la sélection actuelle pour filtrer", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Vides}", "SSE.Views.AutoFilterDialog.textSelectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Sélectionner tous les résultats de la recherche", "SSE.Views.AutoFilterDialog.textWarning": "Avertissement", "SSE.Views.AutoFilterDialog.txtAboveAve": "Au dessus de la moyenne", "SSE.Views.AutoFilterDialog.txtAfter": "Après...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "Tous les dates dans la période", "SSE.Views.AutoFilterDialog.txtApril": "Avril", "SSE.Views.AutoFilterDialog.txtAugust": "Août", "SSE.Views.AutoFilterDialog.txtBefore": "Avant...", "SSE.Views.AutoFilterDialog.txtBegins": "Commence par...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Au dessous de la moyenne", "SSE.Views.AutoFilterDialog.txtBetween": "Entre...", "SSE.Views.AutoFilterDialog.txtClear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtContains": "Contient ...", "SSE.Views.AutoFilterDialog.txtDateFilter": "Filtre par date", "SSE.Views.AutoFilterDialog.txtDecember": "Décembre", "SSE.Views.AutoFilterDialog.txtEmpty": "Entrez le filtre des cellules", "SSE.Views.AutoFilterDialog.txtEnds": "Se termine par...", "SSE.Views.AutoFilterDialog.txtEquals": "Équivaut à...", "SSE.Views.AutoFilterDialog.txtFebruary": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Filtrer par couleur des cellules", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Filtrer par couleur de police", "SSE.Views.AutoFilterDialog.txtGreater": "Supérieur à...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Sup<PERSON><PERSON> ou égal à...", "SSE.Views.AutoFilterDialog.txtJanuary": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtJuly": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtJune": "Juin", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Filtre étiquette", "SSE.Views.AutoFilterDialog.txtLastMonth": "<PERSON><PERSON> mois", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Dernier trimestre", "SSE.Views.AutoFilterDialog.txtLastWeek": "<PERSON><PERSON><PERSON> se<PERSON>", "SSE.Views.AutoFilterDialog.txtLastYear": "<PERSON><PERSON><PERSON> ann<PERSON>", "SSE.Views.AutoFilterDialog.txtLess": "Moins que...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Moins que ou égal à...", "SSE.Views.AutoFilterDialog.txtMarch": "Mars", "SSE.Views.AutoFilterDialog.txtMay": "<PERSON>", "SSE.Views.AutoFilterDialog.txtNextMonth": "<PERSON><PERSON> suivant", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Trimestre suivant", "SSE.Views.AutoFilterDialog.txtNextWeek": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtNextYear": "<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtNotBegins": "Ne pas commencer par ...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Pas entre ...", "SSE.Views.AutoFilterDialog.txtNotContains": "Ne contient pas...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Ne se termine pas avec ...", "SSE.Views.AutoFilterDialog.txtNotEquals": "N'est pas égal...", "SSE.Views.AutoFilterDialog.txtNovember": "Novembre", "SSE.Views.AutoFilterDialog.txtNumFilter": "Filtre de nombre", "SSE.Views.AutoFilterDialog.txtOctober": "Octobre", "SSE.Views.AutoFilterDialog.txtQuarter1": "Trimestre 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Trimestre 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Trimestre 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Trimestre 1", "SSE.Views.AutoFilterDialog.txtReapply": "Appliquer à nouveau", "SSE.Views.AutoFilterDialog.txtSeptember": "Septembre", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Trier par couleur des cellules", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Trier par couleur de police", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Trier du plus élevé au plus bas", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Trier le plus bas au plus élevé", "SSE.Views.AutoFilterDialog.txtSortOption": "Plus d'options de tri...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Filtre de texte", "SSE.Views.AutoFilterDialog.txtThisMonth": "<PERSON> mois", "SSE.Views.AutoFilterDialog.txtThisQuarter": "Ce trimestre", "SSE.Views.AutoFilterDialog.txtThisWeek": "<PERSON><PERSON> se<PERSON>", "SSE.Views.AutoFilterDialog.txtThisYear": "<PERSON><PERSON> an<PERSON>", "SSE.Views.AutoFilterDialog.txtTitle": "Filtre", "SSE.Views.AutoFilterDialog.txtToday": "<PERSON><PERSON><PERSON>'hui", "SSE.Views.AutoFilterDialog.txtTomorrow": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtTop10": "Les 10 premiers", "SSE.Views.AutoFilterDialog.txtValueFilter": "Filtre de valeur", "SSE.Views.AutoFilterDialog.txtYearToDate": "De<PERSON><PERSON> le début de l'année", "SSE.Views.AutoFilterDialog.txtYesterday": "<PERSON>er", "SSE.Views.AutoFilterDialog.warnFilterError": "Vous avez besoin d'au moins un champs dans la zone des valeurs pour appliquer la filtre de valeur", "SSE.Views.AutoFilterDialog.warnNoSelected": "V<PERSON> devez choisir au moins une valeur", "SSE.Views.CellEditor.textManager": "Gestionnaire de noms ", "SSE.Views.CellEditor.tipFormula": "Insérer une fonction", "SSE.Views.CellRangeDialog.errorMaxRows": "ERREUR! Maximum de 255 séries de données par graphique.", "SSE.Views.CellRangeDialog.errorStockChart": "Ordre lignes incorrect. <PERSON><PERSON> créer un diagramme boursier, positionnez les données sur la feuille de calcul dans l'ordre suivant :<br>cours à l'ouverture, cours maximal, cours minimal, cours à la clôture.", "SSE.Views.CellRangeDialog.txtEmpty": "Ce champ est obligatoire", "SSE.Views.CellRangeDialog.txtInvalidRange": "ERREUR! Plage de cellules non valide", "SSE.Views.CellRangeDialog.txtTitle": "Sélectionner une plage de données", "SSE.Views.CellSettings.strShrink": "Réduire pour ajuster", "SSE.Views.CellSettings.strWrap": "Renvoyer à la ligne", "SSE.Views.CellSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.CellSettings.textBackColor": "Couleur d'arrière-plan", "SSE.Views.CellSettings.textBackground": "Couleur d'arrière-plan", "SSE.Views.CellSettings.textBorderColor": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textBorders": "Style des bordures", "SSE.Views.CellSettings.textClearRule": "Efface<PERSON> les règles", "SSE.Views.CellSettings.textColor": "Remplissage coloré ", "SSE.Views.CellSettings.textColorScales": "<PERSON><PERSON><PERSON> de couleurs", "SSE.Views.CellSettings.textCondFormat": "Mise en forme conditionnelle", "SSE.Views.CellSettings.textControl": "Contrôle de texte", "SSE.Views.CellSettings.textDataBars": "Barres de données", "SSE.Views.CellSettings.textDirection": "Direction", "SSE.Views.CellSettings.textFill": "Remplissage", "SSE.Views.CellSettings.textForeground": "Couleur de premier plan", "SSE.Views.CellSettings.textGradient": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textGradientColor": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textGradientFill": "Remplissage en dégradé", "SSE.Views.CellSettings.textIndent": "Retrait", "SSE.Views.CellSettings.textItems": "éléments", "SSE.Views.CellSettings.textLinear": "linéaire", "SSE.Views.CellSettings.textManageRule": "<PERSON><PERSON><PERSON> les règles", "SSE.Views.CellSettings.textNewRule": "Nouvelle règle", "SSE.Views.CellSettings.textNoFill": "Aucun remplissage", "SSE.Views.CellSettings.textOrientation": "Orientation du texte", "SSE.Views.CellSettings.textPattern": "Style de motif", "SSE.Views.CellSettings.textPatternFill": "<PERSON><PERSON><PERSON>", "SSE.Views.CellSettings.textPosition": "Position", "SSE.Views.CellSettings.textRadial": "Radial", "SSE.Views.CellSettings.textSelectBorders": "Sélectionnez les bordures à modifier en appliquant le style choisi ci-dessus", "SSE.Views.CellSettings.textSelection": "De la sélection actuelle", "SSE.Views.CellSettings.textThisPivot": "À partir d'un tableau croisé dynamique", "SSE.Views.CellSettings.textThisSheet": "À partir de cette feuille", "SSE.Views.CellSettings.textThisTable": "À partir de ce tableau", "SSE.Views.CellSettings.tipAddGradientPoint": "Ajouter un point de dégradé", "SSE.Views.CellSettings.tipAll": "Fixer bordure extérieure et la toutes lignes intérieures", "SSE.Views.CellSettings.tipBottom": "Fixer seulement bordure extérieure inférieure", "SSE.Views.CellSettings.tipDiagD": "Définir une bordure diagonale descendante", "SSE.Views.CellSettings.tipDiagU": "<PERSON><PERSON><PERSON><PERSON> une bordure diagonale ascendante", "SSE.Views.CellSettings.tipInner": "Fixer seulement lignes intérieures", "SSE.Views.CellSettings.tipInnerHor": "Fixer seulement lignes intérieures horizontales", "SSE.Views.CellSettings.tipInnerVert": "Fixer seulement lignes verticales intérieures", "SSE.Views.CellSettings.tipLeft": "Fixer seulement bordure extérieure gauche", "SSE.Views.CellSettings.tipNone": "Fixer pas de bordures", "SSE.Views.CellSettings.tipOuter": "Fixer seulement bordure extérieure", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Supprimer le point de dégradé", "SSE.Views.CellSettings.tipRight": "Fixer seulement bordure extérieure droite", "SSE.Views.CellSettings.tipTop": "Fixer seulement bordure extérieure supérieure", "SSE.Views.ChartDataDialog.errorInFormula": "La formule que vous avez saisi contient une erreure.", "SSE.Views.ChartDataDialog.errorInvalidReference": "La référence n'est pas valide. La référence doit être attachée à une feuille de calcul ouverte.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Maximum de 4096 points en série par graphique.", "SSE.Views.ChartDataDialog.errorMaxRows": "Le nombre maximum de séries de données par graphique est de 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "La référence n'est pas valide. Les références pour titres, valeurs, tailles, ou les étiquettes de données douvent représenter une cellule, une plage de données, ou une colonne.", "SSE.Views.ChartDataDialog.errorNoValues": "Pour créer un graphique, les séries doivent contenir une valeur au minimum.", "SSE.Views.ChartDataDialog.errorStockChart": "Ordre des lignes est incorrect. Pour créer un graphique boursier organisez vos données sur la feuille de calcul dans l'ordre suivant:<br> cours à l'ouverture, cours maximal, cours minimal, cours à la clôture.", "SSE.Views.ChartDataDialog.textAdd": "Ajouter", "SSE.Views.ChartDataDialog.textCategory": "Étiquettes de l'axe horizontal (abscisse)", "SSE.Views.ChartDataDialog.textData": "Plage de données du graphique", "SSE.Views.ChartDataDialog.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textDown": "En bas", "SSE.Views.ChartDataDialog.textEdit": "Modifier", "SSE.Views.ChartDataDialog.textInvalidRange": "Plage de cellules non valide", "SSE.Views.ChartDataDialog.textSelectData": "Sélectionner des données", "SSE.Views.ChartDataDialog.textSeries": "Entrées de légende (série)", "SSE.Views.ChartDataDialog.textSwitch": "Changer de ligne/colonne", "SSE.Views.ChartDataDialog.textTitle": "Données du graphique", "SSE.Views.ChartDataDialog.textUp": "En haut", "SSE.Views.ChartDataRangeDialog.errorInFormula": "La formule que vous avez saisi contient une erreure.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "La référence n'est pas valide. La référence doit être attachée à une feuille ouverte.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Maximum de 4096 points en série par graphique.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Le nombre maximum de séries de données par graphique est de 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "La référence n'est pas valide. Les références pour titres, valeurs, tailles, ou les étiquettes de données douvent représenter une cellule, une plage de données, ou une colonne.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Pour créer un graphique, les séries doivent contenir une valeur au minimum.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Ordre des lignes est incorrect. Pour créer un graphique boursier organisez vos données sur la feuille de calcul dans l'ordre suivant:<br> cours à l'ouverture, cours maximal, cours minimal, cours à la clôture.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Plage de cellules non valide", "SSE.Views.ChartDataRangeDialog.textSelectData": "Sélectionner des données", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Plage de données de l'étiquette de l'axe", "SSE.Views.ChartDataRangeDialog.txtChoose": "Choisir la plage de données", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Nom de la série", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Étiquettes de l'axe", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Modifier la série", "SSE.Views.ChartDataRangeDialog.txtValues": "Valeurs", "SSE.Views.ChartDataRangeDialog.txtXValues": "Valeurs X", "SSE.Views.ChartDataRangeDialog.txtYValues": "Valeurs Y", "SSE.Views.ChartSettings.errorMaxRows": "Le nombre maximum de séries de données par graphique est de 255.", "SSE.Views.ChartSettings.strLineWeight": "Épaisseur de trait", "SSE.Views.ChartSettings.strSparkColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.strTemplate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.text3dDepth": "Profondeur (% de la base)", "SSE.Views.ChartSettings.text3dHeight": "Hauteur (% de la base)", "SSE.Views.ChartSettings.text3dRotation": "Rotation 3D", "SSE.Views.ChartSettings.textAdvanced": "Afficher les paramètres avancés", "SSE.Views.ChartSettings.textAutoscale": "Mise à l'échelle automatique", "SSE.Views.ChartSettings.textBorderSizeErr": "La valeur saisie est incorrecte. <br>En<PERSON>z une valeur de 0 à 1584 points.", "SSE.Views.ChartSettings.textChangeType": "Modifier le type", "SSE.Views.ChartSettings.textChartType": "Modifier le type de graphique", "SSE.Views.ChartSettings.textDefault": "Rotation par défaut", "SSE.Views.ChartSettings.textDown": "En bas", "SSE.Views.ChartSettings.textEditData": "Modifier les données et l'emplacement", "SSE.Views.ChartSettings.textFirstPoint": "Premier point", "SSE.Views.ChartSettings.textHeight": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textHighPoint": "Point élevé", "SSE.Views.ChartSettings.textKeepRatio": "Proportions constantes", "SSE.Views.ChartSettings.textLastPoint": "Dernier point", "SSE.Views.ChartSettings.textLeft": "À gauche", "SSE.Views.ChartSettings.textLowPoint": "Point bas", "SSE.Views.ChartSettings.textMarkers": "Mar<PERSON><PERSON>", "SSE.Views.ChartSettings.textNarrow": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> le champ de vision", "SSE.Views.ChartSettings.textNegativePoint": "Point négatif", "SSE.Views.ChartSettings.textPerspective": "Perspective", "SSE.Views.ChartSettings.textRanges": "Plage de donn<PERSON>", "SSE.Views.ChartSettings.textRight": "À droite", "SSE.Views.ChartSettings.textRightAngle": "Axes à angle droit", "SSE.Views.ChartSettings.textSelectData": "Sélectionner des données", "SSE.Views.ChartSettings.textShow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textSize": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textStyle": "Style", "SSE.Views.ChartSettings.textSwitch": "Changer de ligne/colonne", "SSE.Views.ChartSettings.textType": "Type", "SSE.Views.ChartSettings.textUp": "En haut", "SSE.Views.ChartSettings.textWiden": "<PERSON><PERSON><PERSON><PERSON> le champ de vision", "SSE.Views.ChartSettings.textWidth": "<PERSON><PERSON>", "SSE.Views.ChartSettings.textX": "Rotation X", "SSE.Views.ChartSettings.textY": "Rotation Y", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ERREUR! Maximum de 4096 points en série par graphique.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "ERREUR! Maximum de 255 séries de données par graphique.", "SSE.Views.ChartSettingsDlg.errorStockChart": "Ordre lignes incorrect. <PERSON><PERSON> créer un diagramme boursier, positionnez les données sur la feuille de calcul dans l'ordre suivant :<br>cours à l'ouverture, cours maximal, cours minimal, cours à la clôture.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Ne pas déplacer ou dimensionner avec les cellules", "SSE.Views.ChartSettingsDlg.textAlt": "Texte de remplacement", "SSE.Views.ChartSettingsDlg.textAltDescription": "Description", "SSE.Views.ChartSettingsDlg.textAltTip": "La représentation textuelle des informations sur l’objet visuel, qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre le contenu de l’image, de la forme, du graphique ou du tableau.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Titre", "SSE.Views.ChartSettingsDlg.textAuto": "Auto", "SSE.Views.ChartSettingsDlg.textAutoEach": "Automatique pour chacune", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Croisement de l'axe", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Options de l'axe", "SSE.Views.ChartSettingsDlg.textAxisPos": "Position de l'axe", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Paramètres de l’axe", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Titre", "SSE.Views.ChartSettingsDlg.textBase": "Base", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Entre graduations", "SSE.Views.ChartSettingsDlg.textBillions": "Milliards", "SSE.Views.ChartSettingsDlg.textBottom": "En bas", "SSE.Views.ChartSettingsDlg.textCategoryName": "Nom de catégorie", "SSE.Views.ChartSettingsDlg.textCenter": "Au centre", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Éléments du graphique &<br>Légende du graphique", "SSE.Views.ChartSettingsDlg.textChartTitle": "Titre du graphique", "SSE.Views.ChartSettingsDlg.textCross": "Sur l'axe", "SSE.Views.ChartSettingsDlg.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textDataColumns": "en colonnes", "SSE.Views.ChartSettingsDlg.textDataLabels": "Étiquettes de données", "SSE.Views.ChartSettingsDlg.textDataRows": "en lignes", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Afficher la légende", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Cellules masquées et vides", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Relier les points de données par une courbe", "SSE.Views.ChartSettingsDlg.textFit": "Ajuster à la largeur", "SSE.Views.ChartSettingsDlg.textFixed": "Fixe", "SSE.Views.ChartSettingsDlg.textFormat": "format d'étiquette", "SSE.Views.ChartSettingsDlg.textGaps": "Intervalles", "SSE.Views.ChartSettingsDlg.textGridLines": "Quadrillage", "SSE.Views.ChartSettingsDlg.textGroup": "Groupe de graphiques sparkline", "SSE.Views.ChartSettingsDlg.textHide": "Masquer", "SSE.Views.ChartSettingsDlg.textHideAxis": "Masquer l'axe", "SSE.Views.ChartSettingsDlg.textHigh": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textHorAxis": "Axe horizontal", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Axe horizontal secondaire", "SSE.Views.ChartSettingsDlg.textHorizontal": "Horizontal", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Centaines", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textInnerBottom": "À l'intérieur en bas", "SSE.Views.ChartSettingsDlg.textInnerTop": "À l'intérieur en haut", "SSE.Views.ChartSettingsDlg.textInvalidRange": "ERREUR! Plage de cellules non valide", "SSE.Views.ChartSettingsDlg.textLabelDist": "Distance de l'étiquette de l'axe", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Intervalle entre les étiquettes", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Options d'étiquette", "SSE.Views.ChartSettingsDlg.textLabelPos": "Position d'étiquette", "SSE.Views.ChartSettingsDlg.textLayout": "Mise en page", "SSE.Views.ChartSettingsDlg.textLeft": "À gauche", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Superposition gauche", "SSE.Views.ChartSettingsDlg.textLegendBottom": "En bas", "SSE.Views.ChartSettingsDlg.textLegendLeft": "À gauche", "SSE.Views.ChartSettingsDlg.textLegendPos": "Légende", "SSE.Views.ChartSettingsDlg.textLegendRight": "À droite", "SSE.Views.ChartSettingsDlg.textLegendTop": "En haut", "SSE.Views.ChartSettingsDlg.textLines": "<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLocationRange": "Plage d’emplacements", "SSE.Views.ChartSettingsDlg.textLogScale": "<PERSON><PERSON>le logarithmique", "SSE.Views.ChartSettingsDlg.textLow": "En bas", "SSE.Views.ChartSettingsDlg.textMajor": "Principal", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Principales et secondaires ", "SSE.Views.ChartSettingsDlg.textMajorType": "Type principal", "SSE.Views.ChartSettingsDlg.textManual": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarkers": "Mar<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Intervalle entre les graduations", "SSE.Views.ChartSettingsDlg.textMaxValue": "Valeur maximale", "SSE.Views.ChartSettingsDlg.textMillions": "Millions", "SSE.Views.ChartSettingsDlg.textMinor": "Secondaires", "SSE.Views.ChartSettingsDlg.textMinorType": "Type secondaire", "SSE.Views.ChartSettingsDlg.textMinValue": "Valeur minimale", "SSE.Views.ChartSettingsDlg.textNextToAxis": "À côté de l'axe", "SSE.Views.ChartSettingsDlg.textNone": "Aucun", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Pas de superposition", "SSE.Views.ChartSettingsDlg.textOneCell": "Déplacer sans dimensionner avec les cellules", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Graduations", "SSE.Views.ChartSettingsDlg.textOut": "À l'extérieur", "SSE.Views.ChartSettingsDlg.textOuterTop": "À l'extérieur en haut", "SSE.Views.ChartSettingsDlg.textOverlay": "Superposition", "SSE.Views.ChartSettingsDlg.textReverse": "Valeurs en ordre inverse", "SSE.Views.ChartSettingsDlg.textReverseOrder": "Inverser l’ordre", "SSE.Views.ChartSettingsDlg.textRight": "À droite", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Superposition droite", "SSE.Views.ChartSettingsDlg.textRotated": "Incliné", "SSE.Views.ChartSettingsDlg.textSameAll": "Identique pour tout", "SSE.Views.ChartSettingsDlg.textSelectData": "Sélectionner des données", "SSE.Views.ChartSettingsDlg.textSeparator": "Séparateur des étiquettes de données", "SSE.Views.ChartSettingsDlg.textSeriesName": "Nom de série", "SSE.Views.ChartSettingsDlg.textShow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textShowBorders": "Afficher les bordures du graphique", "SSE.Views.ChartSettingsDlg.textShowData": "Afficher les données des lignes et colonnes masquées", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Affiche<PERSON> les cellules vides comme", "SSE.Views.ChartSettingsDlg.textShowEquation": "Afficher l'équation sur le graphique", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Afficher l’axe", "SSE.Views.ChartSettingsDlg.textShowValues": "Afficher les valeurs du graphique", "SSE.Views.ChartSettingsDlg.textSingle": "Sparkline unique", "SSE.Views.ChartSettingsDlg.textSmooth": "Lisse", "SSE.Views.ChartSettingsDlg.textSnap": "Accrochage à la cellule", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Plage de graphiques sparklines", "SSE.Views.ChartSettingsDlg.textStraight": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textStyle": "Style", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Milliers", "SSE.Views.ChartSettingsDlg.textTickOptions": "Options de graduations", "SSE.Views.ChartSettingsDlg.textTitle": "Graphique - Paramètres avancés", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Graphique sparkline - Paramètres avancés", "SSE.Views.ChartSettingsDlg.textTop": "En haut", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Options de la ligne de tendance", "SSE.Views.ChartSettingsDlg.textTrillions": "Trillions", "SSE.Views.ChartSettingsDlg.textTwoCell": "Déplacer et dimensionner avec des cellules", "SSE.Views.ChartSettingsDlg.textType": "Type", "SSE.Views.ChartSettingsDlg.textTypeData": "Type et données", "SSE.Views.ChartSettingsDlg.textUnits": "Unités d'affichage", "SSE.Views.ChartSettingsDlg.textValue": "<PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textVertAxis": "Axe vertical", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Axe vertical secondaire", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Titre axe X", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Titre axe Y", "SSE.Views.ChartSettingsDlg.textZero": "Valeur nulle", "SSE.Views.ChartSettingsDlg.txtEmpty": "Ce champ est obligatoire", "SSE.Views.ChartTypeDialog.errorComboSeries": "Pour créer un graphique combiné, sélectionnez au moins deux séries de données. ", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Le type de graphique sélectionné nécessite l'axe secondaire utilisé par un graphique existant. Sélectionnez un autre type de graphique.", "SSE.Views.ChartTypeDialog.textSecondary": "Axe secondaire", "SSE.Views.ChartTypeDialog.textSeries": "Séries", "SSE.Views.ChartTypeDialog.textStyle": "Style", "SSE.Views.ChartTypeDialog.textTitle": "Type de graphique", "SSE.Views.ChartTypeDialog.textType": "Type", "SSE.Views.ChartWizardDialog.errorComboSeries": "Pour créer un graphique combiné, sélectionnez au moins deux séries de données. ", "SSE.Views.ChartWizardDialog.errorMaxPoints": "Le nombre maximum de points en série par graphique est de 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "Le nombre maximum de séries de données par graphique est de 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "Le type de graphique sélectionné nécessite l'axe secondaire utilisé par un graphique existant. Sélectionnez un autre type de graphique.", "SSE.Views.ChartWizardDialog.errorStockChart": "Ordre des lignes incorrect. Pour construire un graphique boursier, placez les données sur la feuille dans l'ordre suivant : prix d'ouverture, prix maximum, prix minimum, prix de clôture.", "SSE.Views.ChartWizardDialog.textRecommended": "Recommandations", "SSE.Views.ChartWizardDialog.textSecondary": "Axe secondaire", "SSE.Views.ChartWizardDialog.textSeries": "Série", "SSE.Views.ChartWizardDialog.textTitle": "Insérer un graphique", "SSE.Views.ChartWizardDialog.textTitleChange": "Modifier le type de graphique", "SSE.Views.ChartWizardDialog.textType": "Type", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Choisissez le type de graphique et l'axe pour votre série de données", "SSE.Views.CreatePivotDialog.textDataRange": "Ligne de données de la source", "SSE.Views.CreatePivotDialog.textDestination": "Choisissez l'émplacement du tableau", "SSE.Views.CreatePivotDialog.textExist": "Feuille de calcul existante", "SSE.Views.CreatePivotDialog.textInvalidRange": "Plage de cellules non valide", "SSE.Views.CreatePivotDialog.textNew": "Nouvelle feuille", "SSE.Views.CreatePivotDialog.textSelectData": "Sélectionner des données", "SSE.Views.CreatePivotDialog.textTitle": "<PERSON><PERSON>er un tableau croisé dynamique", "SSE.Views.CreatePivotDialog.txtEmpty": "Ce champ est obligatoire", "SSE.Views.CreateSparklineDialog.textDataRange": "Ligne de données de la source", "SSE.Views.CreateSparklineDialog.textDestination": "Sélectionnez où placer les graphiques sparkline", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Plage de cellules non valide", "SSE.Views.CreateSparklineDialog.textSelectData": "Sélectionner des données", "SSE.Views.CreateSparklineDialog.textTitle": "<PERSON><PERSON>er les graphiques sparklines", "SSE.Views.CreateSparklineDialog.txtEmpty": "Ce champ est obligatoire", "SSE.Views.DataTab.capBtnGroup": "Grouper", "SSE.Views.DataTab.capBtnTextCustomSort": "Tri personnalisé", "SSE.Views.DataTab.capBtnTextDataValidation": "Validation des données", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Supp<PERSON>er les doublons", "SSE.Views.DataTab.capBtnTextToCol": "Texte en colonnes", "SSE.Views.DataTab.capBtnUngroup": "Dissocier", "SSE.Views.DataTab.capDataExternalLinks": "Liens externes", "SSE.Views.DataTab.capDataFromText": "<PERSON><PERSON><PERSON>r les données", "SSE.Views.DataTab.capGoalSeek": "Valeur cible", "SSE.Views.DataTab.mniFromFile": "À partir du fichier local TXT/CSV", "SSE.Views.DataTab.mniFromUrl": "À partir de l'URL du fichier TXT/CSV", "SSE.Views.DataTab.mniFromXMLFile": "À partir du XML local", "SSE.Views.DataTab.textBelow": "Lignes de synthèse sous les lignes de détail", "SSE.Views.DataTab.textClear": "Effacer le plan", "SSE.Views.DataTab.textColumns": "Dissocier les colonnes", "SSE.Views.DataTab.textGroupColumns": "Grouper les colonnes", "SSE.Views.DataTab.textGroupRows": "Grouper les lignes", "SSE.Views.DataTab.textRightOf": "Colonnes de synthèse à droite des colonnes de détail", "SSE.Views.DataTab.textRows": "Dissocier les lignes", "SSE.Views.DataTab.tipCustomSort": "Tri personnalisé", "SSE.Views.DataTab.tipDataFromText": "Obtenir des données à partir d'un fichier", "SSE.Views.DataTab.tipDataValidation": "Validation des données", "SSE.Views.DataTab.tipExternalLinks": "Afficher les autres fichiers auxquels cette feuille de calcul est liée", "SSE.Views.DataTab.tipGoalSeek": "Trouver la bonne entrée pour la valeur souhaitée", "SSE.Views.DataTab.tipGroup": "Groper une plage de cellules", "SSE.Views.DataTab.tipRemDuplicates": "Supprimer les lignes dupliquées d'une feuille de calcul", "SSE.Views.DataTab.tipToColumns": "<PERSON><PERSON><PERSON><PERSON><PERSON> le contenu d'une cellule dans des colonnes adjacentes", "SSE.Views.DataTab.tipUngroup": "Dissocier une plage de cellules", "SSE.Views.DataValidationDialog.errorFormula": "La valeur est actuellement considérée comme une erreur. Voulez-vous continuer ?", "SSE.Views.DataValidationDialog.errorInvalid": "La valeur saisie dans le champ \"{0}\" n'est pas valide.", "SSE.Views.DataValidationDialog.errorInvalidDate": "La date que vous avez saisie dans le champ \"{0}\" n'est pas valide.", "SSE.Views.DataValidationDialog.errorInvalidList": "La liste source doit être une liste séparée ou une référence vers une ligne ou une colonne unique.", "SSE.Views.DataValidationDialog.errorInvalidTime": "Le temps que vous avez saisi pour le champ \"{0}\" n'est pas valide.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "Le champs \"{1}\" doit être supérieur ou égal au champs \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Veuillez saisir une valeur dans les champs \"{0}\" et \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "<PERSON><PERSON><PERSON><PERSON> saisir une valeur dans le champ \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "La plage nommée que vous avez spécifiée est introuvable.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "Les valeurs négatives ne peuvent pas être utilisées dans les conditions \"{0}\".", "SSE.Views.DataValidationDialog.errorNotNumeric": "Le champ \"{0}\" doit contenir une valeur numérique, une expression numérique, ou se référer à une cellule contenant une valeur numérique.", "SSE.Views.DataValidationDialog.strError": "<PERSON><PERSON><PERSON> d'erreur", "SSE.Views.DataValidationDialog.strInput": "Message de saisie", "SSE.Views.DataValidationDialog.strSettings": "Paramètres", "SSE.Views.DataValidationDialog.textAlert": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textAllow": "Autoriser", "SSE.Views.DataValidationDialog.textApply": "Appliquer ces modifications à toutes les cellules ayant les mêmes paramètres", "SSE.Views.DataValidationDialog.textCellSelected": "Afficher ce message de saisie lorsqu'une cellule est sélectionnée.", "SSE.Views.DataValidationDialog.textCompare": "Comparer avec", "SSE.Views.DataValidationDialog.textData": "<PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textEndDate": "Date limite", "SSE.Views.DataValidationDialog.textEndTime": "Heure de fin", "SSE.Views.DataValidationDialog.textError": "Message d'erreur", "SSE.Views.DataValidationDialog.textFormula": "Formule", "SSE.Views.DataValidationDialog.textIgnore": "Ignorer les espaces vides", "SSE.Views.DataValidationDialog.textInput": "Message de saisie", "SSE.Views.DataValidationDialog.textMax": "Maximum", "SSE.Views.DataValidationDialog.textMessage": "Message", "SSE.Views.DataValidationDialog.textMin": "Minimum", "SSE.Views.DataValidationDialog.textSelectData": "Sélectionner des données", "SSE.Views.DataValidationDialog.textShowDropDown": "Afficher une liste déroulante dans une cellule", "SSE.Views.DataValidationDialog.textShowError": "Afficher un avertissement d'erreur lorsque les données invalides sont saisies", "SSE.Views.DataValidationDialog.textShowInput": "Afficher un message de saisie lorsqu'une cellule est sélectionnée", "SSE.Views.DataValidationDialog.textSource": "Source", "SSE.Views.DataValidationDialog.textStartDate": "Date de début", "SSE.Views.DataValidationDialog.textStartTime": "<PERSON><PERSON> d<PERSON>", "SSE.Views.DataValidationDialog.textStop": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DataValidationDialog.textStyle": "Style", "SSE.Views.DataValidationDialog.textTitle": "Titre", "SSE.Views.DataValidationDialog.textUserEnters": "Afficher cet avertissement d'erreur lorsqu'un utilisateur saisit les données invalides.", "SSE.Views.DataValidationDialog.txtAny": "<PERSON><PERSON><PERSON> valeur", "SSE.Views.DataValidationDialog.txtBetween": "entre", "SSE.Views.DataValidationDialog.txtDate": "Date", "SSE.Views.DataValidationDialog.txtDecimal": "Décimales", "SSE.Views.DataValidationDialog.txtElTime": "Temps écoulé", "SSE.Views.DataValidationDialog.txtEndDate": "Date limite", "SSE.Views.DataValidationDialog.txtEndTime": "Heure de fin", "SSE.Views.DataValidationDialog.txtEqual": "est égal", "SSE.Views.DataValidationDialog.txtGreaterThan": "Sup<PERSON>ur à", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "supérieur ou égal à", "SSE.Views.DataValidationDialog.txtLength": "<PERSON><PERSON><PERSON> ", "SSE.Views.DataValidationDialog.txtLessThan": "inférieur à", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "inférieur ou égal à", "SSE.Views.DataValidationDialog.txtList": "Liste", "SSE.Views.DataValidationDialog.txtNotBetween": "pas entre", "SSE.Views.DataValidationDialog.txtNotEqual": "n'est pas égal", "SSE.Views.DataValidationDialog.txtOther": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtStartDate": "Date de début", "SSE.Views.DataValidationDialog.txtStartTime": "<PERSON><PERSON> d<PERSON>", "SSE.Views.DataValidationDialog.txtTextLength": "Longueur du texte", "SSE.Views.DataValidationDialog.txtTime": "<PERSON><PERSON>", "SSE.Views.DataValidationDialog.txtWhole": "Nombre entier", "SSE.Views.DigitalFilterDialog.capAnd": "Et", "SSE.Views.DigitalFilterDialog.capCondition1": "est égal", "SSE.Views.DigitalFilterDialog.capCondition10": "ne se termine pas par", "SSE.Views.DigitalFilterDialog.capCondition11": "contient", "SSE.Views.DigitalFilterDialog.capCondition12": "ne contient pas", "SSE.Views.DigitalFilterDialog.capCondition2": "n'est pas égal", "SSE.Views.DigitalFilterDialog.capCondition3": "est supérieur à", "SSE.Views.DigitalFilterDialog.capCondition30": "est après", "SSE.Views.DigitalFilterDialog.capCondition4": "est supérieur ou égal à", "SSE.Views.DigitalFilterDialog.capCondition40": "est après ou égal à", "SSE.Views.DigitalFilterDialog.capCondition5": "est inférieur à", "SSE.Views.DigitalFilterDialog.capCondition50": "est avant", "SSE.Views.DigitalFilterDialog.capCondition6": "est inférieure ou égale à", "SSE.Views.DigitalFilterDialog.capCondition60": "est avant ou égal à", "SSE.Views.DigitalFilterDialog.capCondition7": "commence par", "SSE.Views.DigitalFilterDialog.capCondition8": "ne commence pas par", "SSE.Views.DigitalFilterDialog.capCondition9": "se termine par", "SSE.Views.DigitalFilterDialog.capOr": "Ou", "SSE.Views.DigitalFilterDialog.textNoFilter": "aucun filtre", "SSE.Views.DigitalFilterDialog.textShowRows": "A<PERSON>iche<PERSON> les lignes où", "SSE.Views.DigitalFilterDialog.textUse1": "Utilisez ? pour représenter un seul caractère", "SSE.Views.DigitalFilterDialog.textUse2": "Utilisez * pour présenter une série de caractères", "SSE.Views.DigitalFilterDialog.txtSelectDate": "Sélectionnez une date", "SSE.Views.DigitalFilterDialog.txtTitle": "<PERSON><PERSON><PERSON> person<PERSON>", "SSE.Views.DocumentHolder.advancedEquationText": "Paramètres d'équations", "SSE.Views.DocumentHolder.advancedImgText": "Paramètres avancés de l'image", "SSE.Views.DocumentHolder.advancedShapeText": "Paramètres avancés de la forme", "SSE.Views.DocumentHolder.advancedSlicerText": "Paramètres avancés du segment", "SSE.Views.DocumentHolder.allLinearText": "Toutes - Linéaire", "SSE.Views.DocumentHolder.allProfText": "Toutes - Professionnel", "SSE.Views.DocumentHolder.bottomCellText": "Aligner en bas", "SSE.Views.DocumentHolder.bulletsText": "Puces et Numéros", "SSE.Views.DocumentHolder.centerCellText": "Aligner au centre", "SSE.Views.DocumentHolder.chartDataText": "Sélectionner les données du graphique", "SSE.Views.DocumentHolder.chartText": "Paramètres du graphique avancés", "SSE.Views.DocumentHolder.chartTypeText": "Modifier le type de graphique", "SSE.Views.DocumentHolder.currLinearText": "Actuelles - Linéaire", "SSE.Views.DocumentHolder.currProfText": "Actuelles - Professionnel", "SSE.Views.DocumentHolder.deleteColumnText": "Colonne", "SSE.Views.DocumentHolder.deleteRowText": "Ligne", "SSE.Views.DocumentHolder.deleteTableText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.direct270Text": "Rotation du texte vers le haut", "SSE.Views.DocumentHolder.direct90Text": "Rotation du texte vers le bas", "SSE.Views.DocumentHolder.directHText": "Horizontal", "SSE.Views.DocumentHolder.directionText": "Orientation du texte", "SSE.Views.DocumentHolder.editChartText": "Modifier les données", "SSE.Views.DocumentHolder.editHyperlinkText": "Modifier le lien hypertexte", "SSE.Views.DocumentHolder.hideEqToolbar": "Masquer la barre d'outils d'équation", "SSE.Views.DocumentHolder.insertColumnLeftText": "Colonne à gauche", "SSE.Views.DocumentHolder.insertColumnRightText": "Colonne à droite", "SSE.Views.DocumentHolder.insertRowAboveText": "Ligne au-dessus", "SSE.Views.DocumentHolder.insertRowBelowText": "Ligne en dessous", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "<PERSON><PERSON> act<PERSON>", "SSE.Views.DocumentHolder.removeHyperlinkText": "Supprimer le lien hypertexte", "SSE.Views.DocumentHolder.selectColumnText": "<PERSON><PERSON>ne <PERSON>", "SSE.Views.DocumentHolder.selectDataText": "Donn<PERSON> de la colonne", "SSE.Views.DocumentHolder.selectRowText": "Ligne", "SSE.Views.DocumentHolder.selectTableText": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.showEqToolbar": "Afficher la barre d'outils Équations", "SSE.Views.DocumentHolder.strDelete": "Supprimer la signature", "SSE.Views.DocumentHolder.strDetails": "<PERSON><PERSON><PERSON> de la signature", "SSE.Views.DocumentHolder.strSetup": "Mise en place de la signature", "SSE.Views.DocumentHolder.strSign": "Signer", "SSE.Views.DocumentHolder.textAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrange": "Organiser", "SSE.Views.DocumentHolder.textArrangeBack": "Mettre en arrière-plan", "SSE.Views.DocumentHolder.textArrangeBackward": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textArrangeForward": "Avancer", "SSE.Views.DocumentHolder.textArrangeFront": "Mettre au premier plan", "SSE.Views.DocumentHolder.textAverage": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textBullets": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCopyCells": "<PERSON><PERSON><PERSON> les cellules", "SSE.Views.DocumentHolder.textCount": "Total", "SSE.Views.DocumentHolder.textCrop": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textCropFill": "Remplissage", "SSE.Views.DocumentHolder.textCropFit": "Ajuster", "SSE.Views.DocumentHolder.textEditPoints": "Modifier les points", "SSE.Views.DocumentHolder.textEntriesList": "Choisir dans la liste déroulante", "SSE.Views.DocumentHolder.textFillDays": "<PERSON><PERSON><PERSON><PERSON> les jours", "SSE.Views.DocumentHolder.textFillFormatOnly": "Remplir uniquement les formats", "SSE.Views.DocumentHolder.textFillMonths": "<PERSON><PERSON><PERSON><PERSON> les mois", "SSE.Views.DocumentHolder.textFillSeries": "Remplir la série", "SSE.Views.DocumentHolder.textFillWeekdays": "Remp<PERSON>r les jours de la semaine", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Remp<PERSON>r sans mise en forme", "SSE.Views.DocumentHolder.textFillYears": "Remplir les années", "SSE.Views.DocumentHolder.textFlashFill": "Remplissage instantané", "SSE.Views.DocumentHolder.textFlipH": "Retourner horizontalement", "SSE.Views.DocumentHolder.textFlipV": "Retourner verticalement", "SSE.Views.DocumentHolder.textFreezePanes": "Figer les volets", "SSE.Views.DocumentHolder.textFromFile": "À partir du fichier", "SSE.Views.DocumentHolder.textFromStorage": "À partir de l'espace de stockage", "SSE.Views.DocumentHolder.textFromUrl": "À partir d'une URL", "SSE.Views.DocumentHolder.textGrowthTrend": "Tendance de croissance", "SSE.Views.DocumentHolder.textLinearTrend": "Tendance linéaire", "SSE.Views.DocumentHolder.textListSettings": "Paramètres de la liste", "SSE.Views.DocumentHolder.textMacro": "Affecter une macro", "SSE.Views.DocumentHolder.textMax": "Max", "SSE.Views.DocumentHolder.textMin": "Min", "SSE.Views.DocumentHolder.textMore": "Plus de fonctions", "SSE.Views.DocumentHolder.textMoreFormats": "Autres formats", "SSE.Views.DocumentHolder.textNone": "Aucun", "SSE.Views.DocumentHolder.textNumbering": "Numérotation", "SSE.Views.DocumentHolder.textReplace": "Remplacer l’image", "SSE.Views.DocumentHolder.textResetCrop": "Réinitialiser le rognage", "SSE.Views.DocumentHolder.textRotate": "Rotation", "SSE.Views.DocumentHolder.textRotate270": "Faire pivoter à gauche de 90°", "SSE.Views.DocumentHolder.textRotate90": "Faire pivoter à droite de 90°", "SSE.Views.DocumentHolder.textSaveAsPicture": "Enregistrer en tant qu’image", "SSE.Views.DocumentHolder.textSeries": "Série", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Aligner en bas", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Aligner au centre", "SSE.Views.DocumentHolder.textShapeAlignLeft": "<PERSON><PERSON><PERSON> à gauche", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Aligner au centre", "SSE.Views.DocumentHolder.textShapeAlignRight": "<PERSON><PERSON><PERSON> d<PERSON>", "SSE.Views.DocumentHolder.textShapeAlignTop": "Aligner en haut", "SSE.Views.DocumentHolder.textShapesMerge": "Combiner les formes", "SSE.Views.DocumentHolder.textStdDev": "Écartype", "SSE.Views.DocumentHolder.textSum": "Somme", "SSE.Views.DocumentHolder.textUndo": "Annuler", "SSE.Views.DocumentHolder.textUnFreezePanes": "Lib<PERSON>rer les volets", "SSE.Views.DocumentHolder.textVar": "Var", "SSE.Views.DocumentHolder.tipMarkersArrow": "<PERSON><PERSON><PERSON> fl<PERSON>", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "<PERSON><PERSON>s coches", "SSE.Views.DocumentHolder.tipMarkersDash": "<PERSON>ire<PERSON>", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "<PERSON><PERSON><PERSON> remplis", "SSE.Views.DocumentHolder.tipMarkersFRound": "<PERSON><PERSON>s arrondies remplies", "SSE.Views.DocumentHolder.tipMarkersFSquare": "<PERSON><PERSON>s car<PERSON> remplies", "SSE.Views.DocumentHolder.tipMarkersHRound": "Puces rondes vides", "SSE.Views.DocumentHolder.tipMarkersStar": "Puces en étoile", "SSE.Views.DocumentHolder.topCellText": "Aligner en haut", "SSE.Views.DocumentHolder.txtAccounting": "Comptabilité", "SSE.Views.DocumentHolder.txtAddComment": "Ajouter un commentaire", "SSE.Views.DocumentHolder.txtAddNamedRange": "Définir le nom", "SSE.Views.DocumentHolder.txtArrange": "Organiser", "SSE.Views.DocumentHolder.txtAscending": "Croissant", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Ajuster automatiquement la largeur des colonnes", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Ajuster automatiquement la hauteur des lignes", "SSE.Views.DocumentHolder.txtAverage": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCellFormat": "Format des cellules", "SSE.Views.DocumentHolder.txtClear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtClearAll": "<PERSON>ut", "SSE.Views.DocumentHolder.txtClearComments": "Commentaires", "SSE.Views.DocumentHolder.txtClearFormat": "Format", "SSE.Views.DocumentHolder.txtClearHyper": "Liens hypertextes", "SSE.Views.DocumentHolder.txtClearPivotField": "Effacer le filtre de {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Effacer les groupes de sparklines sélectionnés", "SSE.Views.DocumentHolder.txtClearSparklines": "Supprimer les graphiques sparklines sélectionnés", "SSE.Views.DocumentHolder.txtClearText": "Texte", "SSE.Views.DocumentHolder.txtCollapse": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCollapseEntire": "Réduire le champ entièrement", "SSE.Views.DocumentHolder.txtColumn": "<PERSON><PERSON>ne <PERSON>", "SSE.Views.DocumentHolder.txtColumnWidth": "Définir la largeur de la colonne", "SSE.Views.DocumentHolder.txtCondFormat": "Mise en forme conditionnelle", "SSE.Views.DocumentHolder.txtCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCount": "Nombre", "SSE.Views.DocumentHolder.txtCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Largeur de colonne personnalis<PERSON>", "SSE.Views.DocumentHolder.txtCustomRowHeight": "<PERSON>ur de ligne personnalis<PERSON>", "SSE.Views.DocumentHolder.txtCustomSort": "Tri personnalisé ", "SSE.Views.DocumentHolder.txtCut": "Couper", "SSE.Views.DocumentHolder.txtDateLong": "Date longue", "SSE.Views.DocumentHolder.txtDateShort": "Date courte", "SSE.Views.DocumentHolder.txtDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDelField": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtDescending": "Décroissant", "SSE.Views.DocumentHolder.txtDifference": "Différence par rapport à", "SSE.Views.DocumentHolder.txtDistribHor": "Distribuer horizontalement", "SSE.Views.DocumentHolder.txtDistribVert": "Distribuer verticalement", "SSE.Views.DocumentHolder.txtEditComment": "Modifier le commentaire", "SSE.Views.DocumentHolder.txtEditObject": "Modifier l'objet", "SSE.Views.DocumentHolder.txtExpand": "Développer", "SSE.Views.DocumentHolder.txtExpandCollapse": "Développer/Réduire", "SSE.Views.DocumentHolder.txtExpandEntire": "Développer le champ entièrement", "SSE.Views.DocumentHolder.txtFieldSettings": "Paramètres de champs", "SSE.Views.DocumentHolder.txtFilter": "Filtre", "SSE.Views.DocumentHolder.txtFilterCellColor": "Filtrer par couleur des cellules", "SSE.Views.DocumentHolder.txtFilterFontColor": "Filtrer par couleur de police", "SSE.Views.DocumentHolder.txtFilterValue": "Filtrer par la valeur de la cellule sélectionnée", "SSE.Views.DocumentHolder.txtFormula": "Insérer une fonction", "SSE.Views.DocumentHolder.txtFraction": "Fraction", "SSE.Views.DocumentHolder.txtGeneral": "Général", "SSE.Views.DocumentHolder.txtGetLink": "Obtenir le lien vers cette plage", "SSE.Views.DocumentHolder.txtGrandTotal": "Total général", "SSE.Views.DocumentHolder.txtGroup": "Grouper", "SSE.Views.DocumentHolder.txtHide": "Masquer", "SSE.Views.DocumentHolder.txtIndex": "Index", "SSE.Views.DocumentHolder.txtInsert": "Insertion", "SSE.Views.DocumentHolder.txtInsHyperlink": "Lien hypertexte", "SSE.Views.DocumentHolder.txtInsImage": "Insérer une image depuis un fichier", "SSE.Views.DocumentHolder.txtInsImageUrl": "Insérer un image depuis un URL", "SSE.Views.DocumentHolder.txtLabelFilter": "Filtres s'appliquant aux étiquettes", "SSE.Views.DocumentHolder.txtMax": "Max", "SSE.Views.DocumentHolder.txtMin": "Min", "SSE.Views.DocumentHolder.txtMoreOptions": "Plus d'options", "SSE.Views.DocumentHolder.txtNormal": "Aucun calcul", "SSE.Views.DocumentHolder.txtNumber": "Numérique", "SSE.Views.DocumentHolder.txtNumFormat": "Format de nombre", "SSE.Views.DocumentHolder.txtPaste": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtPercent": "% de", "SSE.Views.DocumentHolder.txtPercentage": "Pourcentage", "SSE.Views.DocumentHolder.txtPercentDiff": "% de différence avec", "SSE.Views.DocumentHolder.txtPercentOfCol": "% du total de la colonne", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% du total général", "SSE.Views.DocumentHolder.txtPercentOfParent": "% du total du parent", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% du total de la colonne parente", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% du total de la ligne parente", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% résultat cumulé dans", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% du total de la ligne", "SSE.Views.DocumentHolder.txtPivotSettings": "Paramètres du tableau croisé dynamique", "SSE.Views.DocumentHolder.txtProduct": "Produit", "SSE.Views.DocumentHolder.txtRankAscending": "Trier du plus petit au plus grand", "SSE.Views.DocumentHolder.txtRankDescending": "Trier du plus grand au plus petit", "SSE.Views.DocumentHolder.txtReapply": "Appliquer à nouveau", "SSE.Views.DocumentHolder.txtRefresh": "Actualiser", "SSE.Views.DocumentHolder.txtRow": "Ligne entière", "SSE.Views.DocumentHolder.txtRowHeight": "Définir la hauteur de ligne", "SSE.Views.DocumentHolder.txtRunTotal": "Résultat cumulé par", "SSE.Views.DocumentHolder.txtScientific": "Scientifique", "SSE.Views.DocumentHolder.txtSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShiftDown": "<PERSON><PERSON><PERSON><PERSON> les cellules vers le bas", "SSE.Views.DocumentHolder.txtShiftLeft": "<PERSON><PERSON><PERSON>r les cellules vers la gauche", "SSE.Views.DocumentHolder.txtShiftRight": "<PERSON><PERSON><PERSON><PERSON> les cellules vers la droite", "SSE.Views.DocumentHolder.txtShiftUp": "<PERSON><PERSON><PERSON><PERSON> les cellules vers le haut", "SSE.Views.DocumentHolder.txtShow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtShowAs": "Afficher les valeurs comme", "SSE.Views.DocumentHolder.txtShowComment": "<PERSON><PERSON><PERSON><PERSON> le commentaire", "SSE.Views.DocumentHolder.txtShowDetails": "Aff<PERSON>r les détails", "SSE.Views.DocumentHolder.txtSort": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtSortCellColor": "Couleur sélectionnée de cellules sur le dessus", "SSE.Views.DocumentHolder.txtSortFontColor": "Couleur de police sélectionnée sur le dessus", "SSE.Views.DocumentHolder.txtSortOption": "Autres options de tri", "SSE.Views.DocumentHolder.txtSparklines": "Graphiques sparkline", "SSE.Views.DocumentHolder.txtSubtotalField": "Sous-total", "SSE.Views.DocumentHolder.txtSum": "Somme", "SSE.Views.DocumentHolder.txtSummarize": "Synthèse des valeurs par", "SSE.Views.DocumentHolder.txtText": "Texte", "SSE.Views.DocumentHolder.txtTextAdvanced": "Paramètres avancés de paragraphe", "SSE.Views.DocumentHolder.txtTime": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtTop10": "Top 10", "SSE.Views.DocumentHolder.txtUngroup": "Dissocier", "SSE.Views.DocumentHolder.txtValueFieldSettings": "Paramètres des champs de valeurs", "SSE.Views.DocumentHolder.txtValueFilter": "Filtres s'appliquant aux valeurs", "SSE.Views.DocumentHolder.txtWidth": "<PERSON><PERSON>", "SSE.Views.DocumentHolder.unicodeText": "Unicode", "SSE.Views.DocumentHolder.vertAlignText": "Alignement vertical", "SSE.Views.ExternalLinksDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Mise à jour automatique des données provenant des sources liées", "SSE.Views.ExternalLinksDlg.textChange": "Changer la source", "SSE.Views.ExternalLinksDlg.textDelete": "Rom<PERSON><PERSON> les liaisons", "SSE.Views.ExternalLinksDlg.textDeleteAll": "<PERSON><PERSON><PERSON><PERSON> toutes les liaisons", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textOpen": "<PERSON><PERSON><PERSON><PERSON><PERSON> la <PERSON>", "SSE.Views.ExternalLinksDlg.textSource": "Source", "SSE.Views.ExternalLinksDlg.textStatus": "Statut", "SSE.Views.ExternalLinksDlg.textUnknown": "Inconnu", "SSE.Views.ExternalLinksDlg.textUpdate": "Mettre à jour les valeurs", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Tout mettre à jour", "SSE.Views.ExternalLinksDlg.textUpdating": "Mise à jour...", "SSE.Views.ExternalLinksDlg.txtTitle": "Liens externes", "SSE.Views.FieldSettingsDialog.strLayout": "Mise en page", "SSE.Views.FieldSettingsDialog.strSubtotals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.textNumFormat": "Format de nombre", "SSE.Views.FieldSettingsDialog.textReport": "Formulaire de rapport ", "SSE.Views.FieldSettingsDialog.textTitle": "Paramètres de champs", "SSE.Views.FieldSettingsDialog.txtAverage": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtBlank": "Insérer une ligne vide après chaque élément", "SSE.Views.FieldSettingsDialog.txtBottom": "Afficher en bas du groupe", "SSE.Views.FieldSettingsDialog.txtCompact": "Compact", "SSE.Views.FieldSettingsDialog.txtCount": "Total", "SSE.Views.FieldSettingsDialog.txtCountNums": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtCustomName": "Nom", "SSE.Views.FieldSettingsDialog.txtEmpty": "Afficher les éléments sans données", "SSE.Views.FieldSettingsDialog.txtMax": "Max", "SSE.Views.FieldSettingsDialog.txtMin": "Min", "SSE.Views.FieldSettingsDialog.txtOutline": "<PERSON>tour", "SSE.Views.FieldSettingsDialog.txtProduct": "Produit", "SSE.Views.FieldSettingsDialog.txtRepeat": "Ré<PERSON>éter les étiquettes des éléments sur chaque ligne", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Afficher les sous-totaux", "SSE.Views.FieldSettingsDialog.txtSourceName": "Nom de la source :", "SSE.Views.FieldSettingsDialog.txtStdDev": "Écartype", "SSE.Views.FieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.FieldSettingsDialog.txtSum": "Somme", "SSE.Views.FieldSettingsDialog.txtSummarize": "Fonctions pour les sous-totaux", "SSE.Views.FieldSettingsDialog.txtTabular": "Tabulaire", "SSE.Views.FieldSettingsDialog.txtTop": "Afficher en haut du groupe", "SSE.Views.FieldSettingsDialog.txtVar": "Var", "SSE.Views.FieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.ariaFileMenu": "<PERSON><PERSON>", "SSE.Views.FileMenu.btnBackCaption": "<PERSON>u<PERSON><PERSON>r l'emplacement du fichier", "SSE.Views.FileMenu.btnCloseEditor": "<PERSON><PERSON><PERSON> le <PERSON>er", "SSE.Views.FileMenu.btnCloseMenuCaption": "Retour", "SSE.Views.FileMenu.btnCreateNewCaption": "Nouveau classeur", "SSE.Views.FileMenu.btnDownloadCaption": "Télécharger comme", "SSE.Views.FileMenu.btnExitCaption": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnExportToPDFCaption": "Exporter dans le format PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnHelpCaption": "Aide", "SSE.Views.FileMenu.btnHistoryCaption": "Historique des versions", "SSE.Views.FileMenu.btnInfoCaption": "Infos", "SSE.Views.FileMenu.btnPrintCaption": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnProtectCaption": "<PERSON><PERSON>ger", "SSE.Views.FileMenu.btnRecentFilesCaption": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenu.btnRenameCaption": "<PERSON>mmer", "SSE.Views.FileMenu.btnReturnCaption": "Retour à la feuille de calcul", "SSE.Views.FileMenu.btnRightsCaption": "Droits d'accès", "SSE.Views.FileMenu.btnSaveAsCaption": "Enregistrer sous", "SSE.Views.FileMenu.btnSaveCaption": "Enregistrer", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Enregistrer une copie sous", "SSE.Views.FileMenu.btnSettingsCaption": "Paramètres avancés", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Passer en mode mobile", "SSE.Views.FileMenu.btnToEditCaption": "Modifier la feuille de calcul", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "<PERSON><PERSON>le de calcul vide", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Nouveau classeur", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Appliquer", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Ajouter un auteur", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Ajouter une propriété", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "A<PERSON>ter du texte", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Application", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Changer les droits d'accès", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Commentaire", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Communs", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Propriété du document", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Dernière modification par", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Date de dernière modification", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "Non", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Emplacement", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Propriétés", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "La propriété avec ce titre existe déjà", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Personnes qui ont des droits", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Infos sur le classeur", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Sujet", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "<PERSON><PERSON><PERSON> du classeur", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "O<PERSON>", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Droits d'accès", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Changer les droits d'accès", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Personnes qui ont des droits", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Appliquer", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Mode coédition ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Utiliser le calendrier depuis 1904", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Séparateur décimal", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Langue du dictionnaire", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "<PERSON><PERSON> le calcul itératif", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Rapide", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "Hinting de la police", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Langage de formule", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Example: SUM; MIN; MAX; COUNT", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Afficher l'infobulle de la fonction", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Afficher la barre de défilement horizontale", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Ignorer les mots en MAJUSCULES", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Ignorer les mots contenant des chiffres", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Réglages macros", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Variation maximale", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Nombre maximal d'itérations", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Afficher le bouton Options de collage lorsque le contenu est collé ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Style de référence L1C1", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Paramètres régionaux", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Exemple: ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "Interface RTL", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Afficher les commentaires dans la feuille", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Afficher les modifications apportées par d'autres utilisateurs", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Afficher les commentaires résolus", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Accroché à la grille lors du défilement", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Strict", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Style d'onglet", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Thème d’interface", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Séparateur de milliers", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Unité de mesure", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Utiliser des séparateurs basés sur les paramètres régionaux", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Afficher la barre de défilement verticale", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Valeur de zoom par défaut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Toutes les 10 minutes", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Toutes les 30 minutes", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Toutes les 5 minutes", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "<PERSON><PERSON> he<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Récupération automatique", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Enregistrement automatique", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Désactivé", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "Remplissage", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Enregistrer des versions intermédiaires", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "Ligne", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "<PERSON><PERSON> minute", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Style de références", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "Paramètres avancés", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "Apparence", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Options de correction automatique...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Biélorusse", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Bulgare", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Catalan", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Mise en cache par défaut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Calcul en cours", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Collaboration", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Tchèque", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Personnaliser l'accès rapide", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Allemand", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Édition et sauvegarde", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Grec", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Les données introduites ne peuvent pas être utilisées. Un nombre entier ou décimal peut être requis.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Espagnol", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Coédition en temps réel. Toutes les modifications sont enregistrées automatiquement", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "Français", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Hongrois", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Indonésien", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Italien", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Japonais", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Récemment utilisé", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "comme OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Norvégien", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Néerlandais", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Polonais", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Vérification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "Point", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Portugais (Brésil)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "<PERSON><PERSON><PERSON> (Portugal)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Afficher le bouton d'impression rapide dans l'en-tête de l'éditeur", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "Le document sera imprimé sur la dernière imprimante sélectionnée ou par défaut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Région", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Activer tout", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Activer tous les macros sans notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Activer la prise en charge du lecteur d'écran", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Direction de la feuille par défaut", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "Ce paramètre n'affecte que les nouvelles feuilles", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "De gauche à droite", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "De droite à gauche", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Slovaque", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "<PERSON>lov<PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "<PERSON><PERSON><PERSON><PERSON> tout", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Désactiver tous les macros sans notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Utilise<PERSON> le bouton \"Enregistrer\" pour synchroniser les changements apportés par vous et d'autres personnes", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Utiliser la couleur de la barre d'outils comme arrière-plan des onglets", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Utiliser la touche Alt pour naviguer dans l'interface utilisateur à l'aide du clavier", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Utiliser la touche Option pour naviguer dans l'interface utilisateur à l'aide du clavier", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Afficher la notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Désactiver tous les macros avec notification", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "comme Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Espace de travail", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "<PERSON><PERSON>", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Attention", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "Avec mot de passe", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "<PERSON><PERSON><PERSON> le classeur", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "Avec signature", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "Des signatures valides ont été ajoutées à la feuille de calcul.<br>La feuille de calcul est protégée contre toute modification.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Assurer l'intégrité de la feuille de calcul en ajoutant une signature numérique invisible", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Modifier la feuille de calcul", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "Modifier cette feuille de calcul va supprimer les signatures intégrées.<br>Êtes-vous sur de vouloir continuer ?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Ce classeur a été protégé par un mot de passe", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Protéger cette feuille de calcul par un mot de passe", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Il est nécessaire de signer ce classeur.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "Des signatures valables ont été ajoutées au classeur. Le classeur est protégé des modifications.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Certaines signatures numériques sont invalides ou n'ont pu être vérifiées. Le classeur est protégé contre la modification. ", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Voir les signatures", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Télécharger comme", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Enregistrer une copie sous", "SSE.Views.FillSeriesDialog.textAuto": "Remplissage automatique", "SSE.Views.FillSeriesDialog.textCols": "Colonnes", "SSE.Views.FillSeriesDialog.textDate": "Date", "SSE.Views.FillSeriesDialog.textDateUnit": "<PERSON>é de <PERSON>", "SSE.Views.FillSeriesDialog.textDay": "Jour", "SSE.Views.FillSeriesDialog.textGrowth": "Croissance", "SSE.Views.FillSeriesDialog.textLinear": "Linéaire", "SSE.Views.FillSeriesDialog.textMonth": "<PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textSeries": "Série dans", "SSE.Views.FillSeriesDialog.textStep": "Valeur de l'étape", "SSE.Views.FillSeriesDialog.textStop": "Valeur limite", "SSE.Views.FillSeriesDialog.textTitle": "Série", "SSE.Views.FillSeriesDialog.textTrend": "Tendance", "SSE.Views.FillSeriesDialog.textType": "Type", "SSE.Views.FillSeriesDialog.textWeek": "<PERSON>ur ouvrable", "SSE.Views.FillSeriesDialog.textYear": "<PERSON><PERSON>", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Les données introduites ne peuvent pas être utilisées. Un nombre entier ou décimal peut être requis.", "SSE.Views.FormatRulesEditDlg.fillColor": "Couleur de remplissage", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Avertissement", "SSE.Views.FormatRulesEditDlg.text2Scales": "<PERSON><PERSON>le à deux couleurs", "SSE.Views.FormatRulesEditDlg.text3Scales": "<PERSON><PERSON><PERSON> à trois couleurs", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Toutes les bordures", "SSE.Views.FormatRulesEditDlg.textAppearance": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>re", "SSE.Views.FormatRulesEditDlg.textApply": "Appliquer à la plage", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Automatique", "SSE.Views.FormatRulesEditDlg.textAxis": "Axe", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Direction de la barre", "SSE.Views.FormatRulesEditDlg.textBold": "Gras", "SSE.Views.FormatRulesEditDlg.textBorder": "Bordure", "SSE.Views.FormatRulesEditDlg.textBordersColor": "Couleur des bordures", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Style de bordure", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Bordures inférieures", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Impossible d'ajouter la mise en forme conditionnelle", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Milieu de cellule", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Bordures intérieures verticales", "SSE.Views.FormatRulesEditDlg.textClear": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textColor": "Couleur du texte", "SSE.Views.FormatRulesEditDlg.textContext": "Contexte", "SSE.Views.FormatRulesEditDlg.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "<PERSON><PERSON><PERSON> diagonale bas", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "<PERSON><PERSON><PERSON> diagonale haut", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Saissiez la fonction valide.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "La formule entrée ne donne pas un nombre, une date, une heure ou une chaîne.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Sasissez une valeur.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "La valeur entrée n'est pas un nombre, une date, une heure ou une chaîne valide.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "La valeur pour {0} doit être supérieure à la valeur pour {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Saisis<PERSON>z le numéro entre {0} et {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Remplissage", "SSE.Views.FormatRulesEditDlg.textFormat": "Format", "SSE.Views.FormatRulesEditDlg.textFormula": "Formule", "SSE.Views.FormatRulesEditDlg.textGradient": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textIconLabel": "quand {0}{1} et", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "quand  {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "quand la valeur est", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Une ou plusieurs plages de données d'icônes se chevauchent.<br>Modifiez les valeurs des plages de données d'icônes pour corriger ce chevauchement.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Style d'icône", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Bordures intérieures", "SSE.Views.FormatRulesEditDlg.textInvalid": "Plage de données n'est pas valide.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "ERREUR! La plage de cellules n'est pas valide", "SSE.Views.FormatRulesEditDlg.textItalic": "Italique", "SSE.Views.FormatRulesEditDlg.textItem": "Article", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "de gauche à droite", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Bordures gauches", "SSE.Views.FormatRulesEditDlg.textLongBar": "la barre la plus longue", "SSE.Views.FormatRulesEditDlg.textMaximum": "Maximum", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Valeur maximum", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Bordures intérieures horizontales", "SSE.Views.FormatRulesEditDlg.textMidpoint": "valeur médiane", "SSE.Views.FormatRulesEditDlg.textMinimum": "Minimum", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Valeur minimum", "SSE.Views.FormatRulesEditDlg.textNegative": "Négatif", "SSE.Views.FormatRulesEditDlg.textNewColor": "Plus de couleurs", "SSE.Views.FormatRulesEditDlg.textNoBorders": "<PERSON><PERSON><PERSON> bordure", "SSE.Views.FormatRulesEditDlg.textNone": "Aucun", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "Une ou plusieurs valeurs spécifiées ne correspondent pas à un pourcentage valide.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "La valeur spécifiée {0} n'est pas un pourcentage valide.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "Une ou plusieurs valeurs spécifiées ne correspondent pas à une centile valide.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "La valeur spécifiée {0} n'est pas une centile valide.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Bordures extérieures", "SSE.Views.FormatRulesEditDlg.textPercent": "Pour cent", "SSE.Views.FormatRulesEditDlg.textPercentile": "Centile", "SSE.Views.FormatRulesEditDlg.textPosition": "Position", "SSE.Views.FormatRulesEditDlg.textPositive": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textPresets": "Préréglages", "SSE.Views.FormatRulesEditDlg.textPreview": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "Vous ne pouvez pas utiliser les références relatives pour les critères de la mise en forme conditionnelle pour les échelles de couleurs, les barres de données et les jeux d'icônes.", "SSE.Views.FormatRulesEditDlg.textReverse": "Ordre inversé des icônes", "SSE.Views.FormatRulesEditDlg.textRight2Left": "De droite à gauche", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Bordures droites", "SSE.Views.FormatRulesEditDlg.textRule": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSameAs": "Même que positif", "SSE.Views.FormatRulesEditDlg.textSelectData": "Sélectionner des données", "SSE.Views.FormatRulesEditDlg.textShortBar": "la barre la plus courte", "SSE.Views.FormatRulesEditDlg.textShowBar": "Afficher la barre uniquement", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Icônes seules à afficher", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Ce type de référence ne peut pas être utilisé dans une formule de mise en forme conditionnelle.<br>Modifiez la référence à une seule cellule ou utilisez la référence avec une fonction de feuille, telle que =SOMME(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Solide", "SSE.Views.FormatRulesEditDlg.textStrikeout": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textSubscript": "Indice", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Exposant", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Bordures supérieures", "SSE.Views.FormatRulesEditDlg.textUnderline": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipBorders": "Bordures", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Format de nombre", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Comptabilité", "SSE.Views.FormatRulesEditDlg.txtCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtDate": "Date", "SSE.Views.FormatRulesEditDlg.txtDateLong": "Date longue", "SSE.Views.FormatRulesEditDlg.txtDateShort": "Date courte", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Ce champ est obligatoire", "SSE.Views.FormatRulesEditDlg.txtFraction": "Fraction", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Général", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "Aucune icône", "SSE.Views.FormatRulesEditDlg.txtNumber": "Nombre", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Pourcentage", "SSE.Views.FormatRulesEditDlg.txtScientific": "Scientifique", "SSE.Views.FormatRulesEditDlg.txtText": "Texte", "SSE.Views.FormatRulesEditDlg.txtTime": "<PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Modifier la règle de mise en forme", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Nouvelle règle de mise en forme", "SSE.Views.FormatRulesManagerDlg.guestText": "Invi<PERSON>", "SSE.Views.FormatRulesManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.text1Above": "1 écart type au-dessus de la moyenne", "SSE.Views.FormatRulesManagerDlg.text1Below": "1 écart type en dessous de la moyenne", "SSE.Views.FormatRulesManagerDlg.text2Above": "2 écarts types au-dessus de la moyenne", "SSE.Views.FormatRulesManagerDlg.text2Below": "2 écarts types en dessous de la moyenne", "SSE.Views.FormatRulesManagerDlg.text3Above": "3 écarts types au-dessus de la moyenne", "SSE.Views.FormatRulesManagerDlg.text3Below": "3 écarts types en dessous de la moyenne", "SSE.Views.FormatRulesManagerDlg.textAbove": "Au dessus de la moyenne", "SSE.Views.FormatRulesManagerDlg.textApply": "Appliquer à", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "La valeur de la cellule commence par", "SSE.Views.FormatRulesManagerDlg.textBelow": "Au dessous de la moyenne", "SSE.Views.FormatRulesManagerDlg.textBetween": "est entre {0} et {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Valeur de la cellule", "SSE.Views.FormatRulesManagerDlg.textColorScale": "<PERSON><PERSON>le de couleurs en nuances de gris", "SSE.Views.FormatRulesManagerDlg.textContains": "La valeur de la cellule contient", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "La cellule contient une valeur vide", "SSE.Views.FormatRulesManagerDlg.textContainsError": "La cellule contient une erreure", "SSE.Views.FormatRulesManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textDown": "dé<PERSON>r la règle vers le bas", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Doublons", "SSE.Views.FormatRulesManagerDlg.textEdit": "Modifier", "SSE.Views.FormatRulesManagerDlg.textEnds": "La valeur de la cellule se termine par", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "<PERSON><PERSON><PERSON> ou au-dessus de la moyenne", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Égale à ou en dessous de la moyenne", "SSE.Views.FormatRulesManagerDlg.textFormat": "Format", "SSE.Views.FormatRulesManagerDlg.textIconSet": "<PERSON><PERSON> <PERSON>", "SSE.Views.FormatRulesManagerDlg.textNew": "Nouvelle", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "n'est pas entre {0} et {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "La valeur de la cellule ne contient pas", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "La cellule ne contient pas de valeur vide", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "La cellule ne contient pas d'erreurs", "SSE.Views.FormatRulesManagerDlg.textRules": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesManagerDlg.textScope": "Afficher les règles de la mise en forme pour", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Sélectionner des données", "SSE.Views.FormatRulesManagerDlg.textSelection": "Sélection actuelle", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "Ce tableau croisé dynamique", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "<PERSON><PERSON> feuille", "SSE.Views.FormatRulesManagerDlg.textThisTable": "<PERSON> tableau", "SSE.Views.FormatRulesManagerDlg.textUnique": "Valeurs uniques", "SSE.Views.FormatRulesManagerDlg.textUp": "dé<PERSON>r la règle vers le haut", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Cet élément est en cours de modification par un autre utilisateur.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Mise en forme conditionnelle", "SSE.Views.FormatSettingsDialog.textCategory": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.textDecimal": "Décimales", "SSE.Views.FormatSettingsDialog.textFormat": "Format", "SSE.Views.FormatSettingsDialog.textLinked": "<PERSON>é à <PERSON>", "SSE.Views.FormatSettingsDialog.textSeparator": "Utiliser le séparateur de milliers ", "SSE.Views.FormatSettingsDialog.textSymbols": "Symboles", "SSE.Views.FormatSettingsDialog.textTitle": "Format de nombre", "SSE.Views.FormatSettingsDialog.txtAccounting": "Comptabilité", "SSE.Views.FormatSettingsDialog.txtAs10": "Dizièmes (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "Centièmes (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "Seizi<PERSON>s (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "<PERSON><PERSON> (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "Quatrièmes (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Huitièmes (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Veuillez saisir attentivement le format de numéro personnalisé. Pour les formats personnalisés, le tableur ne reconnaît pas les erreurs qui peuvent affecter le fichier xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "Date", "SSE.Views.FormatSettingsDialog.txtFraction": "Fraction", "SSE.Views.FormatSettingsDialog.txtGeneral": "Général", "SSE.Views.FormatSettingsDialog.txtNone": "Aucun", "SSE.Views.FormatSettingsDialog.txtNumber": "Numérique", "SSE.Views.FormatSettingsDialog.txtPercentage": "Pourcentage", "SSE.Views.FormatSettingsDialog.txtSample": "Exemple:", "SSE.Views.FormatSettingsDialog.txtScientific": "Scientifique", "SSE.Views.FormatSettingsDialog.txtText": "Texte", "SSE.Views.FormatSettingsDialog.txtTime": "<PERSON><PERSON>", "SSE.Views.FormatSettingsDialog.txtUpto1": "Un chiffre maximum (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "Deux chiffres maximum (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "Trois chiffres maximum (131/135)", "SSE.Views.FormulaDialog.sDescription": "Description", "SSE.Views.FormulaDialog.textGroupDescription": "Sélectionner un groupe de fonctions", "SSE.Views.FormulaDialog.textListDescription": "Sélectionner une fonction", "SSE.Views.FormulaDialog.txtRecommended": "Recommandations", "SSE.Views.FormulaDialog.txtSearch": "<PERSON><PERSON><PERSON>", "SSE.Views.FormulaDialog.txtTitle": "Insérer une fonction", "SSE.Views.FormulaTab.capBtnRemoveArr": "Supp<PERSON>er les flèches", "SSE.Views.FormulaTab.capBtnTraceDep": "Tracer les dépendants", "SSE.Views.FormulaTab.capBtnTracePrec": "Tracer les précédents", "SSE.Views.FormulaTab.textAutomatic": "Automatique", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Calculer la feuille courante", "SSE.Views.FormulaTab.textCalculateWorkbook": "<PERSON><PERSON><PERSON> le classeur", "SSE.Views.FormulaTab.textManual": "<PERSON><PERSON>", "SSE.Views.FormulaTab.tipCalculate": "Calculer", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "<PERSON><PERSON>r le classeur entier", "SSE.Views.FormulaTab.tipRemoveArr": "Supprimer les flèches crées par", "SSE.Views.FormulaTab.tipShowFormulas": "Afficher la formule dans chaque cellule au lieu du valeur qui en résulte", "SSE.Views.FormulaTab.tipTraceDep": "Afficher des flèches pour indiquer les cellules qui sont affectés par la valeur de la cellule actuelle", "SSE.Views.FormulaTab.tipTracePrec": "Afficher des flèches pour indiquer les cellules qui affectent le valeur de la cellule actuelle", "SSE.Views.FormulaTab.tipWatch": "Ajouter des cellules à la liste de la fenêtre Espion", "SSE.Views.FormulaTab.txtAdditional": "Supplémentaire", "SSE.Views.FormulaTab.txtAutosum": "Somme automatique", "SSE.Views.FormulaTab.txtAutosumTip": "Somme", "SSE.Views.FormulaTab.txtCalculation": "Calcul", "SSE.Views.FormulaTab.txtFormula": "Fonction", "SSE.Views.FormulaTab.txtFormulaTip": "Insérer une fonction", "SSE.Views.FormulaTab.txtMore": "Plus de fonctions", "SSE.Views.FormulaTab.txtRecent": "Récemment utilisés", "SSE.Views.FormulaTab.txtRemDep": "Supprimer les flèches de dépendance", "SSE.Views.FormulaTab.txtRemPrec": "Supprimer les flèches des précédents", "SSE.Views.FormulaTab.txtShowFormulas": "Aff<PERSON><PERSON> les formules", "SSE.Views.FormulaTab.txtWatch": "Fenêtre Espions", "SSE.Views.FormulaWizard.textAny": "N'importe quel", "SSE.Views.FormulaWizard.textArgument": "Argument", "SSE.Views.FormulaWizard.textFunction": "Fonction", "SSE.Views.FormulaWizard.textFunctionRes": "Résultat de fonction", "SSE.Views.FormulaWizard.textHelp": "Aide sur la fonction", "SSE.Views.FormulaWizard.textLogical": "Logique", "SSE.Views.FormulaWizard.textNoArgs": "Cette fonction n'a pas d'arguments", "SSE.Views.FormulaWizard.textNoArgsDesc": "cet argument n'a pas de description", "SSE.Views.FormulaWizard.textNumber": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormulaWizard.textReadMore": "En savoir plus", "SSE.Views.FormulaWizard.textRef": "Référence", "SSE.Views.FormulaWizard.textText": "Texte", "SSE.Views.FormulaWizard.textTitle": "Arguments de la fonction", "SSE.Views.FormulaWizard.textValue": "Résultat d'une formule ", "SSE.Views.GoalSeekDlg.textChangingCell": "En modifiant la cellule", "SSE.Views.GoalSeekDlg.textDataRangeError": "Il manque une plage à la formule", "SSE.Views.GoalSeekDlg.textMustContainFormula": "La cellule doit contenir une formule", "SSE.Views.GoalSeekDlg.textMustContainValue": "La cellule doit contenir une valeur", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "La formule dans la cellule doit aboutir à un nombre", "SSE.Views.GoalSeekDlg.textMustSingleCell": "La référence doit porter sur une seule cellule", "SSE.Views.GoalSeekDlg.textSelectData": "Sélection des données", "SSE.Views.GoalSeekDlg.textSetCell": "Définir la cellule", "SSE.Views.GoalSeekDlg.textTitle": "Valeur cible", "SSE.Views.GoalSeekDlg.textToValue": "Vers la valeur", "SSE.Views.GoalSeekDlg.txtEmpty": "Ce champ est obligatoire", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Les données introduites ne peuvent pas être utilisées. Un nombre entier ou décimal peut être requis.", "SSE.Views.GoalSeekStatusDlg.textContinue": "<PERSON><PERSON><PERSON>", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Valeur actuelle :", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Recherche d'objectif avec la cellule {0} a permis de trouver une solution.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Recherche d'objectif avec la cellule {0} n'a pas pu trouver de solution.", "SSE.Views.GoalSeekStatusDlg.textPause": "Pause", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Recherche d'objectif avec la cellule {0} à l'itération #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "Étape", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Valeur cible :", "SSE.Views.GoalSeekStatusDlg.textTitle": "Statut de la valeur cible", "SSE.Views.HeaderFooterDialog.textAlign": "Aligner sur les marges de page", "SSE.Views.HeaderFooterDialog.textAll": "Toutes les pages", "SSE.Views.HeaderFooterDialog.textBold": "Gras", "SSE.Views.HeaderFooterDialog.textCenter": "Centre", "SSE.Views.HeaderFooterDialog.textColor": "Couleur du texte", "SSE.Views.HeaderFooterDialog.textDate": "Date", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Première page différente", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Pages paires et impaires différentes", "SSE.Views.HeaderFooterDialog.textEven": "Page paire", "SSE.Views.HeaderFooterDialog.textFileName": "Nom de fi<PERSON>er", "SSE.Views.HeaderFooterDialog.textFirst": "Premi<PERSON> Page", "SSE.Views.HeaderFooterDialog.textFooter": "Pied de page", "SSE.Views.HeaderFooterDialog.textHeader": "<PERSON>-tête", "SSE.Views.HeaderFooterDialog.textImage": "Image", "SSE.Views.HeaderFooterDialog.textInsert": "Insertion", "SSE.Views.HeaderFooterDialog.textItalic": "Italique", "SSE.Views.HeaderFooterDialog.textLeft": "À gauche", "SSE.Views.HeaderFooterDialog.textMaxError": "La chaîne de texte que vous avez saisie est trop longue. Réduisez le nombre de caractères.", "SSE.Views.HeaderFooterDialog.textNewColor": "Plus de couleurs", "SSE.Views.HeaderFooterDialog.textOdd": "Page impaire", "SSE.Views.HeaderFooterDialog.textPageCount": "Compteur de page", "SSE.Views.HeaderFooterDialog.textPageNum": "Numéro de <PERSON>", "SSE.Views.HeaderFooterDialog.textPresets": "Préréglages", "SSE.Views.HeaderFooterDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textScale": "Mettre à l'échelle du document", "SSE.Views.HeaderFooterDialog.textSheet": "Nom de feuille", "SSE.Views.HeaderFooterDialog.textStrikeout": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textSubscript": "Indice", "SSE.Views.HeaderFooterDialog.textSuperscript": "Exposant", "SSE.Views.HeaderFooterDialog.textTime": "<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textTitle": "Paramètres de l'en-tête/pied de page", "SSE.Views.HeaderFooterDialog.textUnderline": "<PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.tipFontName": "Police", "SSE.Views.HeaderFooterDialog.tipFontSize": "Taille de police", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Lien vers", "SSE.Views.HyperlinkSettingsDialog.strRange": "Plage", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Plage s<PERSON>", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Entrez une légende ici", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Entrez un lien ici", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Entrez une info-bulle ici", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Lien externe", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Obtenir le lien", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Plage de données internes", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "ERREUR! La plage de cellules non valide", "SSE.Views.HyperlinkSettingsDialog.textNames": "Les noms définis", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Sélectionner des données", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Sélectionner un fichier", "SSE.Views.HyperlinkSettingsDialog.textSheets": "<PERSON><PERSON><PERSON> de cal<PERSON>l", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Texte de l'info-bulle", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Paramètres des liens hypertextes", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Ce champ est obligatoire", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Ce champ doit contenir une URL au format \"http://www.example.com\"", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Ce champ est limité à 2083 caractères.", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Saisissez l'adresse web ou sélectionnez un fichier", "SSE.Views.ImageSettings.strTransparency": "Opacité", "SSE.Views.ImageSettings.textAdvanced": "Afficher les paramètres avancés", "SSE.Views.ImageSettings.textCrop": "<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettings.textCropFill": "Remplissage", "SSE.Views.ImageSettings.textCropFit": "Ajuster", "SSE.Views.ImageSettings.textCropToShape": "<PERSON><PERSON><PERSON> à la forme", "SSE.Views.ImageSettings.textEdit": "Modifier", "SSE.Views.ImageSettings.textEditObject": "Modifier l'objet", "SSE.Views.ImageSettings.textFlip": "Retournement", "SSE.Views.ImageSettings.textFromFile": "À partir du fichier", "SSE.Views.ImageSettings.textFromStorage": "À partir de l'espace de stockage", "SSE.Views.ImageSettings.textFromUrl": "À partir d'une URL", "SSE.Views.ImageSettings.textHeight": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textHint270": "Faire pivoter à gauche de 90°", "SSE.Views.ImageSettings.textHint90": "Faire pivoter à droite de 90°", "SSE.Views.ImageSettings.textHintFlipH": "Retourner horizontalement", "SSE.Views.ImageSettings.textHintFlipV": "Retourner verticalement", "SSE.Views.ImageSettings.textInsert": "Remplacer l’image", "SSE.Views.ImageSettings.textKeepRatio": "Proportions constantes", "SSE.Views.ImageSettings.textOriginalSize": "<PERSON><PERSON> r<PERSON>", "SSE.Views.ImageSettings.textRecentlyUsed": "Récemment utilisés", "SSE.Views.ImageSettings.textResetCrop": "Réinitialiser le rognage", "SSE.Views.ImageSettings.textRotate90": "Faire pivoter de 90°", "SSE.Views.ImageSettings.textRotation": "Rotation", "SSE.Views.ImageSettings.textSize": "<PERSON><PERSON>", "SSE.Views.ImageSettings.textWidth": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Ne pas déplacer ou dimensionner avec les cellules", "SSE.Views.ImageSettingsAdvanced.textAlt": "Texte de remplacement", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Description", "SSE.Views.ImageSettingsAdvanced.textAltTip": "La représentation textuelle des informations sur l’objet visuel, qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre le contenu de l’image, de la forme, du graphique ou du tableau.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Titre", "SSE.Views.ImageSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "Horizontalement", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Déplacer sans dimensionner avec les cellules", "SSE.Views.ImageSettingsAdvanced.textRotation": "Rotation", "SSE.Views.ImageSettingsAdvanced.textSnap": "Accrochage à la cellule", "SSE.Views.ImageSettingsAdvanced.textTitle": "Image - Paramètres avancés", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Déplacer et dimensionner avec des cellules", "SSE.Views.ImageSettingsAdvanced.textVertically": "Verticalement", "SSE.Views.ImportFromXmlDialog.textDestination": "Choisissez où placer les données", "SSE.Views.ImportFromXmlDialog.textExist": "Feuille de calcul existante", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Plage de cellules invalides", "SSE.Views.ImportFromXmlDialog.textNew": "Nouvelle feuille", "SSE.Views.ImportFromXmlDialog.textSelectData": "Sélectionner des données", "SSE.Views.ImportFromXmlDialog.textTitle": "Importer des données", "SSE.Views.ImportFromXmlDialog.txtEmpty": "Ce champ est obligatoire", "SSE.Views.LeftMenu.ariaLeftMenu": "<PERSON><PERSON> de gauche", "SSE.Views.LeftMenu.tipAbout": "À propos", "SSE.Views.LeftMenu.tipChat": "Cha<PERSON>", "SSE.Views.LeftMenu.tipComments": "Commentaires", "SSE.Views.LeftMenu.tipFile": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "Plug-ins", "SSE.Views.LeftMenu.tipSearch": "<PERSON><PERSON><PERSON>", "SSE.Views.LeftMenu.tipSpellcheck": "Vérification orthographique", "SSE.Views.LeftMenu.tipSupport": "Commentaires & assistance", "SSE.Views.LeftMenu.txtDeveloper": "MODE DEVELOPPEUR", "SSE.Views.LeftMenu.txtEditor": "Éditeur de classeurs", "SSE.Views.LeftMenu.txtLimit": "Accès limité", "SSE.Views.LeftMenu.txtTrial": "MODE DEMO", "SSE.Views.LeftMenu.txtTrialDev": "Essai en mode Développeur", "SSE.Views.MacroDialog.textMacro": "Nom de la macro", "SSE.Views.MacroDialog.textTitle": "Affecter une macro", "SSE.Views.MainSettingsPrint.okButtonText": "Enregistrer", "SSE.Views.MainSettingsPrint.strBottom": "En bas", "SSE.Views.MainSettingsPrint.strLandscape": "Paysage", "SSE.Views.MainSettingsPrint.strLeft": "À gauche", "SSE.Views.MainSettingsPrint.strMargins": "Marges", "SSE.Views.MainSettingsPrint.strPortrait": "Portrait", "SSE.Views.MainSettingsPrint.strPrint": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.strPrintTitles": "Imprimer des en-têtes", "SSE.Views.MainSettingsPrint.strRight": "À droite", "SSE.Views.MainSettingsPrint.strTop": "En haut", "SSE.Views.MainSettingsPrint.textActualSize": "<PERSON><PERSON> r<PERSON>", "SSE.Views.MainSettingsPrint.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textCustomOptions": "Options personnalisées", "SSE.Views.MainSettingsPrint.textFitCols": "Ajuster toutes les colonnes à une page", "SSE.Views.MainSettingsPrint.textFitPage": "Ajuster la feuille à une page", "SSE.Views.MainSettingsPrint.textFitRows": "Ajuster toutes les lignes à une page", "SSE.Views.MainSettingsPrint.textPageOrientation": "Orientation de page", "SSE.Views.MainSettingsPrint.textPageScaling": "Mise à l'échelle", "SSE.Views.MainSettingsPrint.textPageSize": "<PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPrintGrid": "Imp<PERSON><PERSON> le quadrillage", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Imprimer des en-têtes de lignes et de colonnes", "SSE.Views.MainSettingsPrint.textRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Colonnes à répéter à gauche", "SSE.Views.MainSettingsPrint.textRepeatTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> les lignes en haut", "SSE.Views.MainSettingsPrint.textSettings": "Paramètres pour", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "Actuellement, des plages nommées existantes ne peuvent pas être modifiées et les nouvelles ne peuvent pas être créées,<br> car certaines d'entre eux sont en cours de modification.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Nom défini", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Avertissement", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Classeur", "SSE.Views.NamedRangeEditDlg.textDataRange": "Plage de donn<PERSON>", "SSE.Views.NamedRangeEditDlg.textExistName": "ERREUR! Gamme avec un tel nom existe déjà", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Le nom doit commencer par une lettre ou un underscore et ne doit pas contenir de caractères non valides.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ERREUR! Plage de cellules non valable", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ERREUR! Cet élément est en cours de modification par un autre utilisateur.", "SSE.Views.NamedRangeEditDlg.textName": "Nom", "SSE.Views.NamedRangeEditDlg.textReservedName": "Le nom que vous essayez d'utiliser est déjà référencé dans des formules de cellules. Veuillez utiliser un autre nom.", "SSE.Views.NamedRangeEditDlg.textScope": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangeEditDlg.textSelectData": "Sélectionner des données", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Ce champ est obligatoire", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Modifier le nom", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Nouveau nom", "SSE.Views.NamedRangePasteDlg.textNames": "<PERSON><PERSON><PERSON>", "SSE.Views.NamedRangePasteDlg.txtTitle": "Coller un nom", "SSE.Views.NameManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.guestText": "Invi<PERSON>", "SSE.Views.NameManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textDataRange": "Plage de donn<PERSON>", "SSE.Views.NameManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textEdit": "Modifier", "SSE.Views.NameManagerDlg.textEmpty": "Aucune plage nommée n’a encore été créée.<br><PERSON><PERSON><PERSON> au moins une plage nommée et elle va apparaître dans ce champ.", "SSE.Views.NameManagerDlg.textFilter": "Filtre", "SSE.Views.NameManagerDlg.textFilterAll": "Tous", "SSE.Views.NameManagerDlg.textFilterDefNames": "Les noms définis", "SSE.Views.NameManagerDlg.textFilterSheet": "Noms liés à la feuille", "SSE.Views.NameManagerDlg.textFilterTableNames": "Titres de tableaux", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Noms liés au classeur", "SSE.Views.NameManagerDlg.textNew": "Nouveau", "SSE.Views.NameManagerDlg.textnoNames": "Pas de plages nommées correspondant à votre filtre n'a pu être trouvée.", "SSE.Views.NameManagerDlg.textRanges": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textScope": "<PERSON><PERSON><PERSON>", "SSE.Views.NameManagerDlg.textWorkbook": "Classeur", "SSE.Views.NameManagerDlg.tipIsLocked": "Cet élément est en cours de modification par un autre utilisateur.", "SSE.Views.NameManagerDlg.txtTitle": "Gestionnaire de noms", "SSE.Views.NameManagerDlg.warnDelete": "Etes-vous certain de vouloir supprimer le nom {0}?", "SSE.Views.PageMarginsDialog.textBottom": "Bas", "SSE.Views.PageMarginsDialog.textCenter": "Centrer sur la page", "SSE.Views.PageMarginsDialog.textHor": "Horizontalement", "SSE.Views.PageMarginsDialog.textLeft": "G<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textRight": "<PERSON><PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textTitle": "Marges", "SSE.Views.PageMarginsDialog.textTop": "<PERSON><PERSON>", "SSE.Views.PageMarginsDialog.textVert": "Verticalement", "SSE.Views.PageMarginsDialog.textWarning": "Attention", "SSE.Views.PageMarginsDialog.warnCheckMargings": "Les marges sont incorrectes", "SSE.Views.ParagraphSettings.strLineHeight": "Espacement des lignes", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Espacement des paragraphes", "SSE.Views.ParagraphSettings.strSpacingAfter": "<PERSON><PERSON>", "SSE.Views.ParagraphSettings.strSpacingBefore": "Avant", "SSE.Views.ParagraphSettings.textAdvanced": "Afficher les paramètres avancés", "SSE.Views.ParagraphSettings.textAt": "à", "SSE.Views.ParagraphSettings.textAtLeast": "Au moins ", "SSE.Views.ParagraphSettings.textAuto": "Multiple ", "SSE.Views.ParagraphSettings.textExact": "Exactement", "SSE.Views.ParagraphSettings.txtAutoText": "Auto", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "Les onglets spécifiés s'affichent dans ce champ", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Tout en majuscules", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Barré double", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Retraits", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "À gauche", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Interligne", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "À droite", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Avant", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Spécial", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "par", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "Police", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Retraits et espacement", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Petites majuscules", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Espacement", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Indice", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Exposant", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Tabulation", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Alignement", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Multiple ", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Espacement des caractères", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "Tabulation par défaut", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "<PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Exactement", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Première ligne", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Suspendu", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(aucun)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Supp<PERSON>er tout", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Spécifier", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "Au centre", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "À gauche", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Position de taquet de tabulation", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "À droite", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Paragraphe - Paramètres avancés", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Auto", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Modifier", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Formule", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Nom des éléments", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "Nouveau", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Éléments calculés dans", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "est égal", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "ne se termine pas par", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "contient", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "ne contient pas", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "entre", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "pas entre", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "n'est pas égal", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "est supérieur à", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "est supérieur ou égal à", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "est inférieur à", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "est inférieure ou égale à", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "commence par", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "ne commence pas par", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "se termine par", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Afficher les éléments avec l'étiquette :", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Afficher les éléments pour lesquels :", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Utilisez ? pour présenter un caractère unique", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Utilisez * pour présenter une série de caractères", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "et", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Filtre des étiquettes", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Filtre de valeurs", "SSE.Views.PivotGroupDialog.textAuto": "Auto", "SSE.Views.PivotGroupDialog.textBy": "par", "SSE.Views.PivotGroupDialog.textDays": "jours", "SSE.Views.PivotGroupDialog.textEnd": "Fin à :", "SSE.Views.PivotGroupDialog.textError": "Ce champ doit être une valeur numérique.", "SSE.Views.PivotGroupDialog.textGreaterError": "Le numéro de fin doit être plus élevé que celui du début", "SSE.Views.PivotGroupDialog.textHour": "heures", "SSE.Views.PivotGroupDialog.textMin": "minutes", "SSE.Views.PivotGroupDialog.textMonth": "mois", "SSE.Views.PivotGroupDialog.textNumDays": "Nombre de jours", "SSE.Views.PivotGroupDialog.textQuart": "Quartiers", "SSE.Views.PivotGroupDialog.textSec": "Secondes", "SSE.Views.PivotGroupDialog.textStart": "Commence à", "SSE.Views.PivotGroupDialog.textYear": "ann<PERSON>", "SSE.Views.PivotGroupDialog.txtTitle": "Grouper", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "Vous pouvez utiliser les éléments calculés pour effectuer des calculs de base entre différents éléments d'un même champ.", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Formule", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Insérer dans la formule", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "Element", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Nom de l'élément", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Éléments", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "En savoir plus", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Insérer un élément calculé dans", "SSE.Views.PivotSettings.textAdvanced": "Afficher les paramètres avancés", "SSE.Views.PivotSettings.textColumns": "Colonnes", "SSE.Views.PivotSettings.textFields": "Sélectionner les champs", "SSE.Views.PivotSettings.textFilters": "Filtres", "SSE.Views.PivotSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotSettings.textValues": "Valeurs", "SSE.Views.PivotSettings.txtAddColumn": "Ajouter aux colonnes", "SSE.Views.PivotSettings.txtAddFilter": "Ajouter aux filtres", "SSE.Views.PivotSettings.txtAddRow": "Ajouter aux lignes", "SSE.Views.PivotSettings.txtAddValues": "Ajouter aux valeurs", "SSE.Views.PivotSettings.txtFieldSettings": "Paramètres de champs", "SSE.Views.PivotSettings.txtMoveBegin": "<PERSON><PERSON><PERSON><PERSON> vers le début", "SSE.Views.PivotSettings.txtMoveColumn": "Déplacer dans les colonnes", "SSE.Views.PivotSettings.txtMoveDown": "<PERSON><PERSON><PERSON><PERSON> vers le bas", "SSE.Views.PivotSettings.txtMoveEnd": "<PERSON><PERSON><PERSON>r vers la fin", "SSE.Views.PivotSettings.txtMoveFilter": "Déplacer aux filtres", "SSE.Views.PivotSettings.txtMoveRow": "Déplacer aux lignes", "SSE.Views.PivotSettings.txtMoveUp": "<PERSON><PERSON><PERSON><PERSON> vers le haut", "SSE.Views.PivotSettings.txtMoveValues": "Déplacer aux valeurs", "SSE.Views.PivotSettings.txtRemove": "<PERSON><PERSON><PERSON><PERSON> le champ", "SSE.Views.PivotSettingsAdvanced.strLayout": "Nom et disposition", "SSE.Views.PivotSettingsAdvanced.textAlt": "Texte de remplacement", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Description", "SSE.Views.PivotSettingsAdvanced.textAltTip": "La représentation textuelle alternative des informations sur l’objet visuel, qui sera lue par les personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre l'information de l’image, de la forme, du graphique ou du tableau.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Titre", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Ajuster automatiquement la largeur de colonne lors de la mise à jour", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Plage de donn<PERSON>", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Source de données", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Afficher les champs dans la zone de filtre du report", "SSE.Views.PivotSettingsAdvanced.textDown": "Vers le bas, puis à droite ", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Totaux généraux", "SSE.Views.PivotSettingsAdvanced.textHeaders": "En-têtes des champs", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "ERREUR ! La plage de cellules n'est pas valide", "SSE.Views.PivotSettingsAdvanced.textOver": "A droite, puis vers le bas", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Sélectionner des données", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Afficher pour les colonnes", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Afficher les en-têtes des champs pour les lignes et les colonnes", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Afficher pour les lignes", "SSE.Views.PivotSettingsAdvanced.textTitle": "Tableau croisé dynamique - Paramètres avancés", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Afficher les champs de filtre de rapport par colonne", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Afficher les champs de filtre de rapport par ligne", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Ce champ est obligatoire", "SSE.Views.PivotSettingsAdvanced.txtName": "Nom", "SSE.Views.PivotShowDetailDialog.textDescription": "Sélectionnez le champ contenant le détail que vous souhaitez afficher :", "SSE.Views.PivotShowDetailDialog.txtTitle": "Aff<PERSON>r les détails", "SSE.Views.PivotTable.capBlankRows": "<PERSON><PERSON><PERSON> vides", "SSE.Views.PivotTable.capGrandTotals": "<PERSON><PERSON> To<PERSON>ux", "SSE.Views.PivotTable.capLayout": "Disposition du rapport", "SSE.Views.PivotTable.capSubtotals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.mniBottomSubtotals": "Afficher tous les sous-totaux au bas du groupe", "SSE.Views.PivotTable.mniInsertBlankLine": "Insérer un saut de ligne après chaque élément", "SSE.Views.PivotTable.mniLayoutCompact": "Afficher sous forme compactée", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Ne pas répéter toutes les étiquettes d'éléments", "SSE.Views.PivotTable.mniLayoutOutline": "Afficher sous forme de plan", "SSE.Views.PivotTable.mniLayoutRepeat": "Ré<PERSON><PERSON>ter les étiquettes de tous les éléments", "SSE.Views.PivotTable.mniLayoutTabular": "Afficher sous forme tabulaire", "SSE.Views.PivotTable.mniNoSubtotals": "Ne pas afficher les sous-totaux", "SSE.Views.PivotTable.mniOffTotals": "Désactivé pour les lignes et les colonnes", "SSE.Views.PivotTable.mniOnColumnsTotals": "Activé pour les colonnes uniquement", "SSE.Views.PivotTable.mniOnRowsTotals": "Activé pour les lignes uniquement", "SSE.Views.PivotTable.mniOnTotals": "Activé pour les lignes et les colonnes", "SSE.Views.PivotTable.mniRemoveBlankLine": "Supp<PERSON>er le saut de ligne après chaque élément", "SSE.Views.PivotTable.mniTopSubtotals": "Afficher tous les sous-totaux en haut du groupe", "SSE.Views.PivotTable.textColBanded": "Colonnes en bandes", "SSE.Views.PivotTable.textColHeader": "En-têtes de colonnes", "SSE.Views.PivotTable.textRowBanded": "Lignes en bandes", "SSE.Views.PivotTable.textRowHeader": "En-<PERSON><PERSON><PERSON> de lignes", "SSE.Views.PivotTable.tipCalculatedItems": "Éléments calculés", "SSE.Views.PivotTable.tipCreatePivot": "Insérer un tableau croisé dynamique", "SSE.Views.PivotTable.tipGrandTotals": "A<PERSON>icher ou masquer les totaux généraux", "SSE.Views.PivotTable.tipRefresh": "Mettre à jour les informations depuis la source de données", "SSE.Views.PivotTable.tipRefreshCurrent": "Mettre à jour les informations de la source de données pour le tableau actuel", "SSE.Views.PivotTable.tipSelect": "Sélectionner tout le tableau croisé dynamique", "SSE.Views.PivotTable.tipSubtotals": "Afficher ou masquer les sous-totaux", "SSE.Views.PivotTable.txtCalculatedItems": "Éléments calculés", "SSE.Views.PivotTable.txtCollapseEntire": "Réduire le champ entièrement", "SSE.Views.PivotTable.txtCreate": "Insérer un tableau", "SSE.Views.PivotTable.txtExpandEntire": "Développer le champ entièrement", "SSE.Views.PivotTable.txtGroupPivot_Custom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Sombre", "SSE.Views.PivotTable.txtGroupPivot_Light": "<PERSON>", "SSE.Views.PivotTable.txtGroupPivot_Medium": "<PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtPivotTable": "Tableau croisé dynamique", "SSE.Views.PivotTable.txtRefresh": "Actualiser", "SSE.Views.PivotTable.txtRefreshAll": "Actualiser tout", "SSE.Views.PivotTable.txtSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Style de tableau croisé dynamique sombre", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Style de tableau croisé dynamique sombre", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Style de tableau croisé dynamique moyen", "SSE.Views.PrintSettings.btnDownload": "Enregistrer et télécharger", "SSE.Views.PrintSettings.btnExport": "Sauvegarder et exporter", "SSE.Views.PrintSettings.btnPrint": "Enregistrer et imprimer", "SSE.Views.PrintSettings.strBottom": "En bas", "SSE.Views.PrintSettings.strLandscape": "Paysage", "SSE.Views.PrintSettings.strLeft": "À gauche", "SSE.Views.PrintSettings.strMargins": "Marges", "SSE.Views.PrintSettings.strPortrait": "Portrait", "SSE.Views.PrintSettings.strPrint": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strPrintTitles": "Imprimer des en-têtes", "SSE.Views.PrintSettings.strRight": "À droite", "SSE.Views.PrintSettings.strShow": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.strTop": "En haut", "SSE.Views.PrintSettings.textActiveSheets": "<PERSON><PERSON><PERSON>s", "SSE.Views.PrintSettings.textActualSize": "<PERSON><PERSON> r<PERSON>", "SSE.Views.PrintSettings.textAllSheets": "Toutes les feuilles", "SSE.Views.PrintSettings.textCurrentSheet": "<PERSON><PERSON><PERSON> actuel<PERSON>", "SSE.Views.PrintSettings.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textCustomOptions": "Options personnalisées", "SSE.Views.PrintSettings.textFitCols": "Ajuster toutes les colonnes à une page", "SSE.Views.PrintSettings.textFitPage": "Ajuster la feuille à une page", "SSE.Views.PrintSettings.textFitRows": "Ajuster toutes les lignes à une page", "SSE.Views.PrintSettings.textHideDetails": "Masquer les détails", "SSE.Views.PrintSettings.textIgnore": "Ignorer la zone d’impression", "SSE.Views.PrintSettings.textLayout": "Mise en page", "SSE.Views.PrintSettings.textMarginsNarrow": "Étroit", "SSE.Views.PrintSettings.textMarginsNormal": "Normal", "SSE.Views.PrintSettings.textMarginsWide": "Larges", "SSE.Views.PrintSettings.textPageOrientation": "Orientation de page", "SSE.Views.PrintSettings.textPages": "Pages :", "SSE.Views.PrintSettings.textPageScaling": "Mise à l'échelle", "SSE.Views.PrintSettings.textPageSize": "<PERSON><PERSON>", "SSE.Views.PrintSettings.textPrintGrid": "Imp<PERSON><PERSON> le quadrillage", "SSE.Views.PrintSettings.textPrintHeadings": "Imprimer les en-têtes de lignes et de colonne", "SSE.Views.PrintSettings.textPrintRange": "Plage d'impression", "SSE.Views.PrintSettings.textRange": "Plage", "SSE.Views.PrintSettings.textRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "SSE.Views.PrintSettings.textRepeatLeft": "Colonnes à répéter à gauche", "SSE.Views.PrintSettings.textRepeatTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> les lignes en haut", "SSE.Views.PrintSettings.textSelection": "Sélection", "SSE.Views.PrintSettings.textSettings": "Paramètres de la feuille", "SSE.Views.PrintSettings.textShowDetails": "Aff<PERSON>r les détails", "SSE.Views.PrintSettings.textShowGrid": "<PERSON><PERSON><PERSON><PERSON> le quadrillage", "SSE.Views.PrintSettings.textShowHeadings": "Afficher les en-têtes des lignes et des colonnes", "SSE.Views.PrintSettings.textTitle": "Paramètres d'impression", "SSE.Views.PrintSettings.textTitlePDF": "Paramètres PDF", "SSE.Views.PrintSettings.textTo": "à", "SSE.Views.PrintSettings.txtMarginsLast": "Derniers personnalisés", "SSE.Views.PrintTitlesDialog.textFirstCol": "Première colonne", "SSE.Views.PrintTitlesDialog.textFirstRow": "Première ligne", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Colonnes verrouillées", "SSE.Views.PrintTitlesDialog.textFrozenRows": "<PERSON><PERSON><PERSON>", "SSE.Views.PrintTitlesDialog.textInvalidRange": "ERREUR ! La plage de cellules n'est pas valide", "SSE.Views.PrintTitlesDialog.textLeft": "Colonnes à répéter à gauche", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Ne pas répéter", "SSE.Views.PrintTitlesDialog.textRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Sélectionner la ligne", "SSE.Views.PrintTitlesDialog.textTitle": "Imprimer des en-têtes", "SSE.Views.PrintTitlesDialog.textTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> les lignes en haut", "SSE.Views.PrintWithPreview.txtActiveSheets": "<PERSON><PERSON><PERSON>s", "SSE.Views.PrintWithPreview.txtActualSize": "<PERSON><PERSON> r<PERSON>", "SSE.Views.PrintWithPreview.txtAllSheets": "Toutes les feuilles", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Appliquer à toutes les feuilles", "SSE.Views.PrintWithPreview.txtBothSides": "Imprimer en recto verso", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "<PERSON><PERSON><PERSON> les pages sur le plus long côté", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "<PERSON><PERSON><PERSON> les pages sur le plus petit côté", "SSE.Views.PrintWithPreview.txtBottom": "Bas", "SSE.Views.PrintWithPreview.txtCopies": "Copies", "SSE.Views.PrintWithPreview.txtCurrentSheet": "<PERSON><PERSON><PERSON> actuel<PERSON>", "SSE.Views.PrintWithPreview.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtCustomOptions": "Options personnalisées", "SSE.Views.PrintWithPreview.txtEmptyTable": "Il n'y a rien à imprimer, car le tableau est vide", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "Num<PERSON><PERSON> de la première page :", "SSE.Views.PrintWithPreview.txtFitCols": "Ajuster toutes les colonnes à une page", "SSE.Views.PrintWithPreview.txtFitPage": "Ajuster la feuille à une page", "SSE.Views.PrintWithPreview.txtFitRows": "Ajuster toutes les lignes à une page", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Quadrillage et titres", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Paramètres de l'en-tête/pied de page", "SSE.Views.PrintWithPreview.txtIgnore": "Ignorer la zone d'impression", "SSE.Views.PrintWithPreview.txtLandscape": "Paysage", "SSE.Views.PrintWithPreview.txtLeft": "A gauche", "SSE.Views.PrintWithPreview.txtMargins": "Marges", "SSE.Views.PrintWithPreview.txtMarginsLast": "Derniers personnalisés", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "Étroit", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Normal", "SSE.Views.PrintWithPreview.txtMarginsWide": "Larges", "SSE.Views.PrintWithPreview.txtOf": "de {0}", "SSE.Views.PrintWithPreview.txtOneSide": "Impression recto", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Imprimer uniquement sur un côté de la page", "SSE.Views.PrintWithPreview.txtPage": "Page", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Numéro de page non valide", "SSE.Views.PrintWithPreview.txtPageOrientation": "Orientation de page", "SSE.Views.PrintWithPreview.txtPages": "Pages :", "SSE.Views.PrintWithPreview.txtPageSize": "<PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPortrait": "Portrait", "SSE.Views.PrintWithPreview.txtPrint": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtPrintGrid": "Imp<PERSON><PERSON> le quadrillage", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Imprimer les titres des lignes et des colonnes", "SSE.Views.PrintWithPreview.txtPrintRange": "Zone d'impression", "SSE.Views.PrintWithPreview.txtPrintSides": "Impression sur les deux côtés", "SSE.Views.PrintWithPreview.txtPrintTitles": "Titres à imprimer", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Imprimer en PDF", "SSE.Views.PrintWithPreview.txtRepeat": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Répéter les colonnes à gauche", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "<PERSON><PERSON><PERSON><PERSON><PERSON> les lignes en haut", "SSE.Views.PrintWithPreview.txtRight": "À droite", "SSE.Views.PrintWithPreview.txtSave": "Enregistrer", "SSE.Views.PrintWithPreview.txtScaling": "Mise à l'échelle", "SSE.Views.PrintWithPreview.txtSelection": "Sélection", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Paramètres de la feuille", "SSE.Views.PrintWithPreview.txtSheet": "<PERSON><PERSON><PERSON> : {0}", "SSE.Views.PrintWithPreview.txtTo": "à", "SSE.Views.PrintWithPreview.txtTop": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.textExistName": "ERREUR ! Une plage avec ce titre existe déjà", "SSE.Views.ProtectDialog.textInvalidName": "Le titre de la plage doit commencer par une lettre et ne peut contenir que des lettres, des chiffres et des espaces.", "SSE.Views.ProtectDialog.textInvalidRange": "ERREUR! La plage de cellules non valide", "SSE.Views.ProtectDialog.textSelectData": "Sélectionner des données", "SSE.Views.ProtectDialog.txtAllow": "Permettre à tous les utilisateurs de cette feuille", "SSE.Views.ProtectDialog.txtAllowDescription": "Vous pouvez débloquer des plages spécifiques pour l'édition.", "SSE.Views.ProtectDialog.txtAllowRanges": "Autoriser la modification des plages", "SSE.Views.ProtectDialog.txtAutofilter": "Utiliser AutoFiltre", "SSE.Views.ProtectDialog.txtDelCols": "Supprimer les colonnes", "SSE.Views.ProtectDialog.txtDelRows": "Supp<PERSON>er les lignes", "SSE.Views.ProtectDialog.txtEmpty": "Ce champ est obligatoire", "SSE.Views.ProtectDialog.txtFormatCells": "Format des cellules", "SSE.Views.ProtectDialog.txtFormatCols": "Modifier les colonnes", "SSE.Views.ProtectDialog.txtFormatRows": "Modifier les lignes", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Le mot de passe de confirmation n'est pas identique", "SSE.Views.ProtectDialog.txtInsCols": "Insérer des colonnes ", "SSE.Views.ProtectDialog.txtInsHyper": "Insérer un lien hypertexte", "SSE.Views.ProtectDialog.txtInsRows": "Insérer des lignes", "SSE.Views.ProtectDialog.txtObjs": "Modifier les objets", "SSE.Views.ProtectDialog.txtOptional": "Optionnel", "SSE.Views.ProtectDialog.txtPassword": "Mot de passe", "SSE.Views.ProtectDialog.txtPivot": "Utiliser tableau et graphique croisés dynamiques", "SSE.Views.ProtectDialog.txtProtect": "<PERSON><PERSON>ger", "SSE.Views.ProtectDialog.txtRange": "Plage", "SSE.Views.ProtectDialog.txtRangeName": "Titre", "SSE.Views.ProtectDialog.txtRepeat": "Confirmer le mot de passe", "SSE.Views.ProtectDialog.txtScen": "Modifier les scénarios", "SSE.Views.ProtectDialog.txtSelLocked": "Sélectionner les cellules verrouillées", "SSE.Views.ProtectDialog.txtSelUnLocked": "Sélectionner les cellules non verrouillées", "SSE.Views.ProtectDialog.txtSheetDescription": "Empêchez les modifications non souhaitées par d'autres personnes en limitant leur capacité de modification.", "SSE.Views.ProtectDialog.txtSheetTitle": "Protéger la feuille", "SSE.Views.ProtectDialog.txtSort": "<PERSON><PERSON>", "SSE.Views.ProtectDialog.txtWarning": "Attention : si vous oubliez ou perdez votre mot de passe, il sera impossible de le récupérer. Conservez-le en lieu sûr.", "SSE.Views.ProtectDialog.txtWBDescription": "Pour empêcher d'autres utilisateurs d'afficher les feuilles masquées, d'a<PERSON><PERSON>, de d<PERSON><PERSON><PERSON>, de supprimer ou de masquer des feuilles et de renommer les feuilles, vous pouvez protéger la structure de votre classeur avec un mot de passe.", "SSE.Views.ProtectDialog.txtWBTitle": "Protéger la structure du classeur", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "Anonyme", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "<PERSON>ut le monde", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Modifier", "SSE.Views.ProtectedRangesEditDlg.textCantView": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.textCanView": "Affichage", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "Le titre de la plage doit commencer par une lettre et ne peut contenir que des lettres, des chiffres et des espaces.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "ERREUR ! Plage de cellules non valide", "SSE.Views.ProtectedRangesEditDlg.textRemove": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "Sélectionner des données", "SSE.Views.ProtectedRangesEditDlg.textYou": "vous", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Accès à la plage", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "Ce champ est obligatoire", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "<PERSON><PERSON>ger", "SSE.Views.ProtectedRangesEditDlg.txtRange": "Plage", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "Titre", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Cette plage ne peut être modifiée que par vous", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Commencer à taper le nom ou l'e-mail", "SSE.Views.ProtectedRangesManagerDlg.guestText": "Invi<PERSON>", "SSE.Views.ProtectedRangesManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "Modifier", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "Aucune plage protégée n'a encore été créée. Créez au moins une plage protégée et elle apparaîtra dans ce champ.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "Filtre", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "Tous", "SSE.Views.ProtectedRangesManagerDlg.textNew": "Nouveau", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Protéger la feuille de calcul", "SSE.Views.ProtectedRangesManagerDlg.textRange": "Plage", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "Vous pouvez limiter les plages d'édition aux personnes sélectionnées.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "Titre", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "Cet élément est en cours de modification par un autre utilisateur.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Accès", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "Modifier", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "Modifier la plage", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "Nouvelle plage", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Plages protégé<PERSON>", "SSE.Views.ProtectedRangesManagerDlg.txtView": "Affichage", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Souhaitez-vous vraiment supprimer la plage protégée {0} ? <br>Toute personne ayant un accès de modification à la feuille de calcul sera en mesure de modifier le contenu de la plage.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Souhaitez-vous vraiment supprimer les plages protégées ? <br>Toute personne ayant un accès de modification à la feuille de calcul sera en mesure de modifier le contenu de ces plages.", "SSE.Views.ProtectRangesDlg.guestText": "Invi<PERSON>", "SSE.Views.ProtectRangesDlg.lockText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ProtectRangesDlg.textEdit": "Modifier", "SSE.Views.ProtectRangesDlg.textEmpty": "Aucune plage n'est autorisée pour la modification.", "SSE.Views.ProtectRangesDlg.textNew": "Nouveau", "SSE.Views.ProtectRangesDlg.textProtect": "Protéger la feuille", "SSE.Views.ProtectRangesDlg.textPwd": "Mot de passe", "SSE.Views.ProtectRangesDlg.textRange": "Plage", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Plages déverrouillées par un mot de passe lorsque la feuille est protégée (cela ne fonctionne que pour les cellules verrouillées).", "SSE.Views.ProtectRangesDlg.textTitle": "Titre", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Cet élément est en cours de modification par un autre utilisateur.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Modifier la plage", "SSE.Views.ProtectRangesDlg.txtNewRange": "Nouvelle plage", "SSE.Views.ProtectRangesDlg.txtNo": "Non", "SSE.Views.ProtectRangesDlg.txtTitle": "Permettre aux utilisateurs de modifier des plages", "SSE.Views.ProtectRangesDlg.txtYes": "O<PERSON>", "SSE.Views.ProtectRangesDlg.warnDelete": "Etes-vous sûr de vouloir supprimer le nom {0} ?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Colonnes", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Pour supprimer les valeurs en double, selectionnez une ou plusieurs colonnes contenant les doublons.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Mes données ont des en-têtes", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Supp<PERSON>er les doublons", "SSE.Views.RightMenu.ariaRightMenu": "<PERSON>u de droite", "SSE.Views.RightMenu.txtCellSettings": "Paramètres de cellule", "SSE.Views.RightMenu.txtChartSettings": "Paramètres du graphique", "SSE.Views.RightMenu.txtImageSettings": "Paramètres de l'image", "SSE.Views.RightMenu.txtParagraphSettings": "Paramètres du paragraphe", "SSE.Views.RightMenu.txtPivotSettings": "Paramètres de tableau croisé", "SSE.Views.RightMenu.txtSettings": "Paramètres communs", "SSE.Views.RightMenu.txtShapeSettings": "Paramètres de forme", "SSE.Views.RightMenu.txtSignatureSettings": "Paramètres de signature", "SSE.Views.RightMenu.txtSlicerSettings": "Paramètres du segment", "SSE.Views.RightMenu.txtSparklineSettings": "Paramètres du graphique sparkline", "SSE.Views.RightMenu.txtTableSettings": "Paramètres du tableau", "SSE.Views.RightMenu.txtTextArtSettings": "Paramètres de texte d'art", "SSE.Views.ScaleDialog.textAuto": "Auto", "SSE.Views.ScaleDialog.textError": "La valeur saisie est incorrecte.", "SSE.Views.ScaleDialog.textFewPages": "pages", "SSE.Views.ScaleDialog.textFitTo": "Ajuster à ", "SSE.Views.ScaleDialog.textHeight": "<PERSON><PERSON>", "SSE.Views.ScaleDialog.textManyPages": "pages", "SSE.Views.ScaleDialog.textOnePage": "Page", "SSE.Views.ScaleDialog.textScaleTo": "Ajuster à", "SSE.Views.ScaleDialog.textTitle": "Paramètres de mise à l'échelle", "SSE.Views.ScaleDialog.textWidth": "<PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "La valeur maximale pour ce champ est {0}", "SSE.Views.SetValueDialog.txtMinText": "La valeur minimale pour ce champ est {0}", "SSE.Views.ShapeSettings.strBackground": "Couleur d'arrière-plan", "SSE.Views.ShapeSettings.strChange": "Modifier la forme", "SSE.Views.ShapeSettings.strColor": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strFill": "Remplissage", "SSE.Views.ShapeSettings.strForeground": "Couleur de premier plan", "SSE.Views.ShapeSettings.strPattern": "Style de motif", "SSE.Views.ShapeSettings.strShadow": "Ajouter une ombre", "SSE.Views.ShapeSettings.strSize": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.strStroke": "Ligne", "SSE.Views.ShapeSettings.strTransparency": "Opacité", "SSE.Views.ShapeSettings.strType": "Type", "SSE.Views.ShapeSettings.textAdjustShadow": "Ajuster l'ombre", "SSE.Views.ShapeSettings.textAdvanced": "Afficher les paramètres avancés", "SSE.Views.ShapeSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.textBorderSizeErr": "La valeur saisie est incorrecte. <br>En<PERSON>z une valeur de 0 à 1584 points.", "SSE.Views.ShapeSettings.textColor": "Remplissage coloré ", "SSE.Views.ShapeSettings.textDirection": "Direction", "SSE.Views.ShapeSettings.textEditPoints": "Modifier les points", "SSE.Views.ShapeSettings.textEditShape": "Modifier la forme", "SSE.Views.ShapeSettings.textEmptyPattern": "Aucun motif", "SSE.Views.ShapeSettings.textEyedropper": "Pipette", "SSE.Views.ShapeSettings.textFlip": "Retournement", "SSE.Views.ShapeSettings.textFromFile": "À partir du fichier", "SSE.Views.ShapeSettings.textFromStorage": "À partir de l'espace de stockage", "SSE.Views.ShapeSettings.textFromUrl": "À partir d'une URL", "SSE.Views.ShapeSettings.textGradient": "Gradient", "SSE.Views.ShapeSettings.textGradientFill": "Remplissage en dégradé", "SSE.Views.ShapeSettings.textHint270": "Faire pivoter à gauche de 90°", "SSE.Views.ShapeSettings.textHint90": "Faire pivoter à droite de 90°", "SSE.Views.ShapeSettings.textHintFlipH": "Retourner horizontalement", "SSE.Views.ShapeSettings.textHintFlipV": "Retourner verticalement", "SSE.Views.ShapeSettings.textImageTexture": "Image ou texture", "SSE.Views.ShapeSettings.textLinear": "Linéaire", "SSE.Views.ShapeSettings.textMoreColors": "Plus de couleurs", "SSE.Views.ShapeSettings.textNoFill": "Aucun remplissage", "SSE.Views.ShapeSettings.textNoShadow": "Aucune ombre", "SSE.Views.ShapeSettings.textOriginalSize": "<PERSON><PERSON> initiale", "SSE.Views.ShapeSettings.textPatternFill": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textPosition": "Position", "SSE.Views.ShapeSettings.textRadial": "Radial", "SSE.Views.ShapeSettings.textRecentlyUsed": "Récemment utilisés", "SSE.Views.ShapeSettings.textRotate90": "Faire pivoter de 90°", "SSE.Views.ShapeSettings.textRotation": "Rotation", "SSE.Views.ShapeSettings.textSelectImage": "Sélectionner l'image", "SSE.Views.ShapeSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.textShadow": "Ombre", "SSE.Views.ShapeSettings.textStretch": "Prolonger", "SSE.Views.ShapeSettings.textStyle": "Style", "SSE.Views.ShapeSettings.textTexture": "Avec texture", "SSE.Views.ShapeSettings.textTile": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Ajouter un point de dégradé", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Supprimer le point de dégradé", "SSE.Views.ShapeSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> marron", "SSE.Views.ShapeSettings.txtCanvas": "Toile", "SSE.Views.ShapeSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.ShapeSettings.txtDarkFabric": "Tissu foncé", "SSE.Views.ShapeSettings.txtGrain": "Grain", "SSE.Views.ShapeSettings.txtGranite": "Granit", "SSE.Views.ShapeSettings.txtGreyPaper": "<PERSON><PERSON>r gris", "SSE.Views.ShapeSettings.txtKnit": "Tricot", "SSE.Views.ShapeSettings.txtLeather": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtNoBorders": "<PERSON><PERSON> de ligne", "SSE.Views.ShapeSettings.txtPapyrus": "Papyrus", "SSE.Views.ShapeSettings.txtWood": "<PERSON>", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Colonnes", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Marges intérieures", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Ne pas déplacer ou dimensionner avec les cellules", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Texte de remplacement", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Description", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "La représentation textuelle des informations sur l’objet visuel, qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre le contenu de l’image, de la forme, du graphique ou du tableau.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Titre", "SSE.Views.ShapeSettingsAdvanced.textAngle": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Flèches", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Ajuster automatiquement", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Style de début", "SSE.Views.ShapeSettingsAdvanced.textBevel": "<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textBottom": "En bas", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Type d'extrémité de la ligne", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Nombre de colonnes", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "<PERSON><PERSON> finale", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Style final", "SSE.Views.ShapeSettingsAdvanced.textFlat": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textHeight": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "Horizontalement", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Type de jointure", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Proportions constantes", "SSE.Views.ShapeSettingsAdvanced.textLeft": "À gauche", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Style de la ligne", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Onglet", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Déplacer sans dimensionner avec les cellules", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Autoriser le texte en débordement", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Redimensionner la forme pour ajuster le texte", "SSE.Views.ShapeSettingsAdvanced.textRight": "À droite", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Rotation", "SSE.Views.ShapeSettingsAdvanced.textRound": "Arrondi", "SSE.Views.ShapeSettingsAdvanced.textSize": "<PERSON><PERSON>", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Accrochage à la cellule", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Espacement entre les colonnes", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Carré", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Zone de texte", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Forme - Paramètres avancés", "SSE.Views.ShapeSettingsAdvanced.textTop": "En haut", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Déplacer et dimensionner avec des cellules", "SSE.Views.ShapeSettingsAdvanced.textVertically": "Verticalement", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Poids et flèches", "SSE.Views.ShapeSettingsAdvanced.textWidth": "<PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Attention", "SSE.Views.SignatureSettings.strDelete": "Supprimer la signature", "SSE.Views.SignatureSettings.strDetails": "<PERSON><PERSON><PERSON> de la signature", "SSE.Views.SignatureSettings.strInvalid": "Signatures non valides", "SSE.Views.SignatureSettings.strRequested": "Signatures requises", "SSE.Views.SignatureSettings.strSetup": "Configuration de signature", "SSE.Views.SignatureSettings.strSign": "Signer", "SSE.Views.SignatureSettings.strSignature": "Signature", "SSE.Views.SignatureSettings.strSigner": "Signataire", "SSE.Views.SignatureSettings.strValid": "Signatures valables", "SSE.Views.SignatureSettings.txtContinueEditing": "Modifier quand même", "SSE.Views.SignatureSettings.txtEditWarning": "Modifier cette feuille de calcul va supprimer les signatures intégrées.<br>Êtes-vous sur de vouloir continuer ?", "SSE.Views.SignatureSettings.txtRemoveWarning": "V<PERSON><PERSON>z vous supprimer cette signature ? <br> <PERSON><PERSON> ne peut pas être r<PERSON>.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Il est nécessaire de signer ce classeur.", "SSE.Views.SignatureSettings.txtSigned": "Des signatures valables ont été ajoutées au classeur. Le classeur est protégé des modifications.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Certaines signatures numériques sont invalides ou n'ont pu être vérifiées. Le classeur est protégé contre la modification. ", "SSE.Views.SlicerAddDialog.textColumns": "Colonnes", "SSE.Views.SlicerAddDialog.txtTitle": "Insérer des segments", "SSE.Views.SlicerSettings.strHideNoData": "Masquer les éléments vides", "SSE.Views.SlicerSettings.strIndNoData": "Indiquer visuellement les éléments sans données", "SSE.Views.SlicerSettings.strShowDel": "Afficher les éléments supprimés de la source de données", "SSE.Views.SlicerSettings.strShowNoData": "Afficher les éléments sans données à la fin", "SSE.Views.SlicerSettings.strSorting": "Trier et filtrer", "SSE.Views.SlicerSettings.textAdvanced": "Afficher les paramètres avancés", "SSE.Views.SlicerSettings.textAsc": "Croissant", "SSE.Views.SlicerSettings.textAZ": "A à Z", "SSE.Views.SlicerSettings.textButtons": "Boutons", "SSE.Views.SlicerSettings.textColumns": "Colonnes", "SSE.Views.SlicerSettings.textDesc": "Décroissant", "SSE.Views.SlicerSettings.textHeight": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textHor": "Horizontal", "SSE.Views.SlicerSettings.textKeepRatio": "Proportions constantes", "SSE.Views.SlicerSettings.textLargeSmall": "Du plus grand au plus petit", "SSE.Views.SlicerSettings.textLock": "Désactiver redimensionnement ou déplacement", "SSE.Views.SlicerSettings.textNewOld": "Du plus récent au plus ancien.", "SSE.Views.SlicerSettings.textOldNew": "Du plus ancien au plus récent", "SSE.Views.SlicerSettings.textPosition": "Position", "SSE.Views.SlicerSettings.textSize": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textSmallLarge": "Du plus petit au plus grand", "SSE.Views.SlicerSettings.textStyle": "Style", "SSE.Views.SlicerSettings.textVert": "Vertical", "SSE.Views.SlicerSettings.textWidth": "<PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "De Z à A", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Boutons", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Colonnes", "SSE.Views.SlicerSettingsAdvanced.strHeight": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Masquer les éléments vides", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Indiquer visuellement les éléments sans données", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Références", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Afficher les éléments supprimés de la source de données", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "<PERSON>ff<PERSON><PERSON> l'en-tête", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Afficher les éléments sans données à la fin", "SSE.Views.SlicerSettingsAdvanced.strSize": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Trier et filtrer", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Style", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Style et taille", "SSE.Views.SlicerSettingsAdvanced.strWidth": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Ne pas déplacer ou dimensionner avec les cellules", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Texte de remplacement", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Description", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "La représentation textuelle alternative des informations sur l’objet visuel, qui sera lue par les personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre l'information de l’image, de la forme, du graphique ou du tableau.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Titre", "SSE.Views.SlicerSettingsAdvanced.textAsc": "Croissant", "SSE.Views.SlicerSettingsAdvanced.textAZ": "A à Z", "SSE.Views.SlicerSettingsAdvanced.textDesc": "Décroissant", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Utiliser le nom dans les formules", "SSE.Views.SlicerSettingsAdvanced.textHeader": "<PERSON>-tête", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Proportions constantes", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "Du plus grand au plus petit", "SSE.Views.SlicerSettingsAdvanced.textName": "Nom", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "Du plus récent au plus ancien.", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "Du plus ancien au plus récent", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Déplacer sans dimensionner avec les cellules", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "Du plus petit au plus grand", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Accrochage à la cellule", "SSE.Views.SlicerSettingsAdvanced.textSort": "<PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Nom de source", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Segment - Paramètres avancés", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Déplacer et dimensionner avec des cellules", "SSE.Views.SlicerSettingsAdvanced.textZA": "De Z à A", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Ce champ est obligatoire", "SSE.Views.SortDialog.errorEmpty": "Une colonne ou une ligne doit être spécifiée pour tous les critères de tri.", "SSE.Views.SortDialog.errorMoreOneCol": "Plusieurs colonnes sont sélectionnées.", "SSE.Views.SortDialog.errorMoreOneRow": "Plusieurs lignes sont sélectionnées.", "SSE.Views.SortDialog.errorNotOriginalCol": "La colonne sélectionnée ne fait pas partie de la plage de sélection d'origine.", "SSE.Views.SortDialog.errorNotOriginalRow": "La ligne sélectionnée ne fait pas partie de la plage de sélection d'origine.", "SSE.Views.SortDialog.errorSameColumnColor": "%1 est trié par la même couleur plusieurs fois. Supprimez le critère de tri en double et réessayez.", "SSE.Views.SortDialog.errorSameColumnValue": "%1 est trié par valeurs plusieurs fois.<br>Supprimez le critère de tri en double et réessayez.", "SSE.Views.SortDialog.textAsc": "Ascendant", "SSE.Views.SortDialog.textAuto": "Automatique", "SSE.Views.SortDialog.textAZ": "de A à Z", "SSE.Views.SortDialog.textBelow": "En dessous", "SSE.Views.SortDialog.textBtnCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textBtnDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.SortDialog.textBtnNew": "Nouveau", "SSE.Views.SortDialog.textCellColor": "Couleur de cellule", "SSE.Views.SortDialog.textColumn": "Colonne", "SSE.Views.SortDialog.textDesc": "Descendant", "SSE.Views.SortDialog.textDown": "Passer au niveau inférieur ", "SSE.Views.SortDialog.textFontColor": "Couleur de police", "SSE.Views.SortDialog.textLeft": "À gauche", "SSE.Views.SortDialog.textLevels": "Niveaux", "SSE.Views.SortDialog.textMoreCols": "(Plus de colonnes...)", "SSE.Views.SortDialog.textMoreRows": "(Plus de lignes...)", "SSE.Views.SortDialog.textNone": "Aucun", "SSE.Views.SortDialog.textOptions": "Options", "SSE.Views.SortDialog.textOrder": "Ordre", "SSE.Views.SortDialog.textRight": "À droite", "SSE.Views.SortDialog.textRow": "Ligne", "SSE.Views.SortDialog.textSort": "Trier sur", "SSE.Views.SortDialog.textSortBy": "Trier par", "SSE.Views.SortDialog.textThenBy": "Puis par", "SSE.Views.SortDialog.textTop": "En haut", "SSE.Views.SortDialog.textUp": "Passer au niveau supérieur", "SSE.Views.SortDialog.textValues": "Valeurs", "SSE.Views.SortDialog.textZA": "De Z à A", "SSE.Views.SortDialog.txtInvalidRange": "Plage de cellules invalide.", "SSE.Views.SortDialog.txtTitle": "<PERSON><PERSON>", "SSE.Views.SortFilterDialog.textAsc": "<PERSON><PERSON> croissant (A à Z)", "SSE.Views.SortFilterDialog.textDesc": "<PERSON><PERSON> (Z à A)", "SSE.Views.SortFilterDialog.textNoSort": "Aucun tri", "SSE.Views.SortFilterDialog.txtTitle": "<PERSON><PERSON>", "SSE.Views.SortFilterDialog.txtTitleValue": "Trier par valeur", "SSE.Views.SortOptionsDialog.textCase": "Re<PERSON>er la casse", "SSE.Views.SortOptionsDialog.textHeaders": "Mes données ont des en-têtes", "SSE.Views.SortOptionsDialog.textLeftRight": "Trier de la gauche vers la droite", "SSE.Views.SortOptionsDialog.textOrientation": "Orientation", "SSE.Views.SortOptionsDialog.textTitle": "Options de tri", "SSE.Views.SortOptionsDialog.textTopBottom": "Trier du haut vers le bas", "SSE.Views.SpecialPasteDialog.textAdd": "Additionner", "SSE.Views.SpecialPasteDialog.textAll": "<PERSON>ut", "SSE.Views.SpecialPasteDialog.textBlanks": "Ignorer les vides", "SSE.Views.SpecialPasteDialog.textColWidth": "<PERSON>ur de colonne", "SSE.Views.SpecialPasteDialog.textComments": "Commentaires", "SSE.Views.SpecialPasteDialog.textDiv": "Diviser", "SSE.Views.SpecialPasteDialog.textFFormat": "Formules et mise en forme", "SSE.Views.SpecialPasteDialog.textFNFormat": "Formules et format des chiffres", "SSE.Views.SpecialPasteDialog.textFormats": "Formats", "SSE.Views.SpecialPasteDialog.textFormulas": "Formules", "SSE.Views.SpecialPasteDialog.textFWidth": "Formules et largeur de colonnes", "SSE.Views.SpecialPasteDialog.textMult": "Multiplier", "SSE.Views.SpecialPasteDialog.textNone": "Aucun", "SSE.Views.SpecialPasteDialog.textOperation": "Opération", "SSE.Views.SpecialPasteDialog.textPaste": "<PERSON><PERSON>", "SSE.Views.SpecialPasteDialog.textSub": "Soustraire", "SSE.Views.SpecialPasteDialog.textTitle": "Collage spécial", "SSE.Views.SpecialPasteDialog.textTranspose": "Transposer", "SSE.Views.SpecialPasteDialog.textValues": "Valeurs", "SSE.Views.SpecialPasteDialog.textVFormat": "Valeurs et mise en forme", "SSE.Views.SpecialPasteDialog.textVNFormat": "Valeurs et formats des nombres", "SSE.Views.SpecialPasteDialog.textWBorders": "Tout sauf la bordure", "SSE.Views.Spellcheck.noSuggestions": "Aucune suggestion orthographique", "SSE.Views.Spellcheck.textChange": "Modification", "SSE.Views.Spellcheck.textChangeAll": "Changer tout", "SSE.Views.Spellcheck.textIgnore": "<PERSON><PERSON><PERSON>", "SSE.Views.Spellcheck.textIgnoreAll": "<PERSON>gno<PERSON> tout", "SSE.Views.Spellcheck.txtAddToDictionary": "Ajouter au dictionnaire", "SSE.Views.Spellcheck.txtClosePanel": "<PERSON><PERSON><PERSON> le correcteur orthographique", "SSE.Views.Spellcheck.txtComplete": "La vérification orthographique est terminée", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Langue du dictionnaire", "SSE.Views.Spellcheck.txtNextTip": "Passer au mot suivant", "SSE.Views.Spellcheck.txtSpelling": "Orthographe", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(<PERSON><PERSON><PERSON><PERSON> vers la fin)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "<PERSON><PERSON>er une copie", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(<PERSON><PERSON><PERSON> une nouvelle feuille de calcul)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Déplacer avant la feuille", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Classeur", "SSE.Views.Statusbar.filteredRecordsText": "Enregistrements filtrés :{0} de {1} ", "SSE.Views.Statusbar.filteredText": "Mode de filtrage", "SSE.Views.Statusbar.itemAverage": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemCount": "Total", "SSE.Views.Statusbar.itemDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.itemHidden": "Masquée", "SSE.Views.Statusbar.itemHide": "Masquer", "SSE.Views.Statusbar.itemInsert": "Insertion", "SSE.Views.Statusbar.itemMaximum": "Maximum", "SSE.Views.Statusbar.itemMinimum": "Minimum", "SSE.Views.Statusbar.itemMoveOrCopy": "<PERSON><PERSON><PERSON><PERSON> ou copier", "SSE.Views.Statusbar.itemProtect": "<PERSON><PERSON>ger", "SSE.Views.Statusbar.itemRename": "<PERSON>mmer", "SSE.Views.Statusbar.itemStatus": "Statut de sauvegarde", "SSE.Views.Statusbar.itemSum": "Somme", "SSE.Views.Statusbar.itemTabColor": "<PERSON><PERSON><PERSON> d'on<PERSON>", "SSE.Views.Statusbar.itemUnProtect": "Déprotéger", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Une feuille portant ce nom existe déjà.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Le nom d'une feuille ne peut pas contenir les caractères suivants : \\/* ?[] : ou le caractère ' comme premier ou dernier caractère", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Nom de la feuille", "SSE.Views.Statusbar.selectAllSheets": "Sélectionner toutes les feuilles", "SSE.Views.Statusbar.sheetIndexText": "<PERSON><PERSON><PERSON> {0} de {1}", "SSE.Views.Statusbar.textAverage": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textCount": "Total", "SSE.Views.Statusbar.textMax": "MAX", "SSE.Views.Statusbar.textMin": "MIN", "SSE.Views.Statusbar.textNewColor": "Plus de couleurs", "SSE.Views.Statusbar.textNoColor": "<PERSON><PERSON> de couleur", "SSE.Views.Statusbar.textSum": "SOMME", "SSE.Views.Statusbar.tipAddTab": "Ajouter feuille de calcul", "SSE.Views.Statusbar.tipFirst": "Faire défiler vers la première feuille de calcul", "SSE.Views.Statusbar.tipLast": "Faire défiler vers la dernière feuille de calcul", "SSE.Views.Statusbar.tipListOfSheets": "Liste des feuilles", "SSE.Views.Statusbar.tipNext": "Faire défiler la liste des feuilles à droite", "SSE.Views.Statusbar.tipPrev": "Faire défiler la liste des feuilles à gauche", "SSE.Views.Statusbar.tipZoomFactor": "Zoom", "SSE.Views.Statusbar.tipZoomIn": "Zoom avant", "SSE.Views.Statusbar.tipZoomOut": "Zoom arri<PERSON>", "SSE.Views.Statusbar.ungroupSheets": "Dissocier les feuilles", "SSE.Views.Statusbar.zoomText": "Zoom {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Impossible de réaliser l'opération sur la plage de cellules spécifiée.<br>Sélectionnez un plage de données différente de la plage existante et essayez à nouveau.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "L'opération n'a pas pu être achevée pour la plage de cellules sélectionnée. <br> S<PERSON><PERSON>ion<PERSON>z une plage de telle sorte que la première ligne de la table était sur la même ligne <br> et la table résultant chevauché l'actuel.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "L'opération n'a pas pu être achevée pour la plage de cellules sélectionnée. <br> Sélection<PERSON>z une plage qui ne comprend pas d'autres tables.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Formules de tableau plusieurs cellules ne sont pas autorisées dans les classeurs.", "SSE.Views.TableOptionsDialog.txtEmpty": "Ce champ est obligatoire", "SSE.Views.TableOptionsDialog.txtFormat": "<PERSON><PERSON><PERSON> un tableau", "SSE.Views.TableOptionsDialog.txtInvalidRange": "ERREUR! La plage de cellules non valide", "SSE.Views.TableOptionsDialog.txtNote": "Les en-têtes doivent rester dans la même ligne, la ligne résultant de la table doit se chevaucher avec une ligne de table originale.", "SSE.Views.TableOptionsDialog.txtTitle": "Titre", "SSE.Views.TableSettings.deleteColumnText": "Supprimer la colonne", "SSE.Views.TableSettings.deleteRowText": "Supprimer la ligne", "SSE.Views.TableSettings.deleteTableText": "Su<PERSON><PERSON><PERSON> le tableau", "SSE.Views.TableSettings.insertColumnLeftText": "Insérer une colonne à gauche", "SSE.Views.TableSettings.insertColumnRightText": "Insérer une colonne à droite", "SSE.Views.TableSettings.insertRowAboveText": "Insérer une ligne au-dessus", "SSE.Views.TableSettings.insertRowBelowText": "Insérer une ligne en dessous", "SSE.Views.TableSettings.notcriticalErrorTitle": "Avertissement", "SSE.Views.TableSettings.selectColumnText": "Sélectionner la colonne entière", "SSE.Views.TableSettings.selectDataText": "Sélectionner les données de la colonne", "SSE.Views.TableSettings.selectRowText": "Sélectionner la ligne", "SSE.Views.TableSettings.selectTableText": "Sélect<PERSON>ner le tableau", "SSE.Views.TableSettings.textActions": "Actions sur le tableau", "SSE.Views.TableSettings.textAdvanced": "Afficher les paramètres avancés", "SSE.Views.TableSettings.textBanded": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textColumns": "Colonnes", "SSE.Views.TableSettings.textConvertRange": "Conversion en plage", "SSE.Views.TableSettings.textEdit": "Lignes et colonnes", "SSE.Views.TableSettings.textEmptyTemplate": "<PERSON><PERSON><PERSON> mod<PERSON>", "SSE.Views.TableSettings.textExistName": "ERREUR! Une gamme avec un tel nom existe déjà", "SSE.Views.TableSettings.textFilter": "Bouton filtre", "SSE.Views.TableSettings.textFirst": "Premier", "SSE.Views.TableSettings.textHeader": "<PERSON>-tête", "SSE.Views.TableSettings.textInvalidName": "ERREUR! Nom de la table non valide", "SSE.Views.TableSettings.textIsLocked": "Cet élément est en cours de modification par un autre utilisateur.", "SSE.Views.TableSettings.textLast": "<PERSON><PERSON>", "SSE.Views.TableSettings.textLongOperation": "Longue opération", "SSE.Views.TableSettings.textPivot": "Insérer un tableau croisé dynamique", "SSE.Views.TableSettings.textRemDuplicates": "Supp<PERSON>er les doublons", "SSE.Views.TableSettings.textReservedName": "Le nom que vous essayez d'utiliser est déjà référencé dans des formules de cellules. Veuillez utiliser un autre nom.", "SSE.Views.TableSettings.textResize": "Redimensionner tableau", "SSE.Views.TableSettings.textRows": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.textSelectData": "Sélectionner des données", "SSE.Views.TableSettings.textSlicer": "Isérer un segment", "SSE.Views.TableSettings.textTableName": "Nom du tableau", "SSE.Views.TableSettings.textTemplate": "Sélectionner à partir d'un modèle", "SSE.Views.TableSettings.textTotal": "Total", "SSE.Views.TableSettings.txtGroupTable_Custom": "Personnalisés", "SSE.Views.TableSettings.txtGroupTable_Dark": "Sombre", "SSE.Views.TableSettings.txtGroupTable_Light": "<PERSON>", "SSE.Views.TableSettings.txtGroupTable_Medium": "<PERSON><PERSON><PERSON>", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Style de tableau sombre", "SSE.Views.TableSettings.txtTable_TableStyleLight": "Style de tableau clair", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "Style de tableau moyen", "SSE.Views.TableSettings.warnLongOperation": "L'opération que vous êtes sur le point d'effectuer peut prendre beaucoup de temps.<br>Êtes-vous sûr de vouloir continuer ?", "SSE.Views.TableSettingsAdvanced.textAlt": "Texte de remplacement", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Description", "SSE.Views.TableSettingsAdvanced.textAltTip": "La représentation textuelle alternative des informations sur l’objet visuel, qui sera lue aux personnes avec des déficiences cognitives ou visuelles pour les aider à mieux comprendre l'information de l’image, de la forme, du graphique ou du tableau.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Titre", "SSE.Views.TableSettingsAdvanced.textTitle": "Tableau - Paramètres avancés", "SSE.Views.TextArtSettings.strBackground": "Couleur d'arrière-plan", "SSE.Views.TextArtSettings.strColor": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strFill": "Remplissage", "SSE.Views.TextArtSettings.strForeground": "Couleur de premier plan", "SSE.Views.TextArtSettings.strPattern": "Style de motif", "SSE.Views.TextArtSettings.strSize": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.strStroke": "Ligne", "SSE.Views.TextArtSettings.strTransparency": "Opacité", "SSE.Views.TextArtSettings.strType": "Type", "SSE.Views.TextArtSettings.textAngle": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textBorderSizeErr": "La valeur saisie est incorrecte. <br>En<PERSON>z une valeur de 0 à 1584 points.", "SSE.Views.TextArtSettings.textColor": "Remplissage coloré ", "SSE.Views.TextArtSettings.textDirection": "Direction", "SSE.Views.TextArtSettings.textEmptyPattern": "Aucun motif", "SSE.Views.TextArtSettings.textFromFile": "À partir du fichier", "SSE.Views.TextArtSettings.textFromUrl": "À partir d'une URL", "SSE.Views.TextArtSettings.textGradient": "Gradient", "SSE.Views.TextArtSettings.textGradientFill": "Remplissage en dégradé", "SSE.Views.TextArtSettings.textImageTexture": "Image ou texture", "SSE.Views.TextArtSettings.textLinear": "Linéaire", "SSE.Views.TextArtSettings.textNoFill": "Aucun remplissage", "SSE.Views.TextArtSettings.textPatternFill": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textPosition": "Position", "SSE.Views.TextArtSettings.textRadial": "Radial", "SSE.Views.TextArtSettings.textSelectTexture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textStretch": "Prolonger", "SSE.Views.TextArtSettings.textStyle": "Style", "SSE.Views.TextArtSettings.textTemplate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.textTexture": "Avec texture", "SSE.Views.TextArtSettings.textTile": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.textTransform": "Transformer", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Ajouter un point de dégradé", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Supprimer le point de dégradé", "SSE.Views.TextArtSettings.txtBrownPaper": "<PERSON><PERSON><PERSON> marron", "SSE.Views.TextArtSettings.txtCanvas": "Toile", "SSE.Views.TextArtSettings.txtCarton": "<PERSON><PERSON>", "SSE.Views.TextArtSettings.txtDarkFabric": "Tissu foncé", "SSE.Views.TextArtSettings.txtGrain": "Grain", "SSE.Views.TextArtSettings.txtGranite": "Granit", "SSE.Views.TextArtSettings.txtGreyPaper": "<PERSON><PERSON>r gris", "SSE.Views.TextArtSettings.txtKnit": "Tricot", "SSE.Views.TextArtSettings.txtLeather": "<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtNoBorders": "<PERSON><PERSON> de ligne", "SSE.Views.TextArtSettings.txtPapyrus": "Papyrus", "SSE.Views.TextArtSettings.txtWood": "<PERSON>", "SSE.Views.Toolbar.capBtnAddComment": "Ajouter un commentaire", "SSE.Views.Toolbar.capBtnColorSchemas": "Couleurs", "SSE.Views.Toolbar.capBtnComment": "Commentaire", "SSE.Views.Toolbar.capBtnInsHeader": "En-tête/Pied de page", "SSE.Views.Toolbar.capBtnInsSlicer": "Segment", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Symbole", "SSE.Views.Toolbar.capBtnMargins": "Marges", "SSE.Views.Toolbar.capBtnPageBreak": "Sauts de page", "SSE.Views.Toolbar.capBtnPageOrient": "Orientation", "SSE.Views.Toolbar.capBtnPageSize": "<PERSON><PERSON>", "SSE.Views.Toolbar.capBtnPrintArea": "Zone d'impression", "SSE.Views.Toolbar.capBtnPrintTitles": "Titres à imprimer", "SSE.Views.Toolbar.capBtnScale": "Mettre à l’échelle", "SSE.Views.Toolbar.capImgAlign": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.capImgBackward": "Envoyer vers l'arrière.", "SSE.Views.Toolbar.capImgForward": "Déplacer vers l'avant", "SSE.Views.Toolbar.capImgGroup": "Groupe", "SSE.Views.Toolbar.capInsertChart": "Graphique", "SSE.Views.Toolbar.capInsertChartRecommend": "Graphique recommandé", "SSE.Views.Toolbar.capInsertEquation": "Équation", "SSE.Views.Toolbar.capInsertHyperlink": "Lien hypertexte", "SSE.Views.Toolbar.capInsertImage": "Image", "SSE.Views.Toolbar.capInsertShape": "Forme", "SSE.Views.Toolbar.capInsertSpark": "Graphique sparkline", "SSE.Views.Toolbar.capInsertTable": "<PERSON><PERSON>", "SSE.Views.Toolbar.capInsertText": "Zone de texte", "SSE.Views.Toolbar.capInsertTextart": "Text Art", "SSE.Views.Toolbar.capShapesMerge": "Combiner les formes", "SSE.Views.Toolbar.mniCapitalizeWords": "Mettre En Majuscules Cha<PERSON>", "SSE.Views.Toolbar.mniImageFromFile": "Image à partir d'un fichier", "SSE.Views.Toolbar.mniImageFromStorage": "Image de stockage", "SSE.Views.Toolbar.mniImageFromUrl": "Image à partir d'une URL", "SSE.Views.Toolbar.mniLowerCase": "minuscules", "SSE.Views.Toolbar.mniSentenceCase": "Majuscule en début de phrase.", "SSE.Views.Toolbar.mniToggleCase": "iNVERSER lA cASSE", "SSE.Views.Toolbar.mniUpperCase": "MAJUSCULES", "SSE.Views.Toolbar.textAddPrintArea": "Ajouter à la zone d'impression", "SSE.Views.Toolbar.textAlignBottom": "Aligner en bas", "SSE.Views.Toolbar.textAlignCenter": "Aligner au centre", "SSE.Views.Toolbar.textAlignJust": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textAlignLeft": "<PERSON><PERSON><PERSON> à gauche", "SSE.Views.Toolbar.textAlignMiddle": "Aligner au centre", "SSE.Views.Toolbar.textAlignRight": "<PERSON><PERSON><PERSON> d<PERSON>", "SSE.Views.Toolbar.textAlignTop": "Aligner en haut", "SSE.Views.Toolbar.textAllBorders": "Toutes les bordures", "SSE.Views.Toolbar.textAlpha": "Lettre miniscule grecque Alpha", "SSE.Views.Toolbar.textAuto": "Auto", "SSE.Views.Toolbar.textAutoColor": "Automatique", "SSE.Views.Toolbar.textBetta": "Lettre miniscule grecque <PERSON>", "SSE.Views.Toolbar.textBlackHeart": "Coeur noir", "SSE.Views.Toolbar.textBold": "Gras", "SSE.Views.Toolbar.textBordersColor": "<PERSON><PERSON><PERSON> de bordure.", "SSE.Views.Toolbar.textBordersStyle": "Style de bordure", "SSE.Views.Toolbar.textBottom": "Bas: ", "SSE.Views.Toolbar.textBottomBorders": "Bordures inférieures", "SSE.Views.Toolbar.textBullet": "<PERSON><PERSON>", "SSE.Views.Toolbar.textCellAlign": "Format de l’alignement de cellule", "SSE.Views.Toolbar.textCenterBorders": "Frontières internes verticales", "SSE.Views.Toolbar.textClearPrintArea": "Effacer la zone d'impression", "SSE.Views.Toolbar.textClearRule": "Efface<PERSON> les règles", "SSE.Views.Toolbar.textClockwise": "Rotation dans le sens des aiguilles d'une montre", "SSE.Views.Toolbar.textColorScales": "Nuances de couleurs", "SSE.Views.Toolbar.textCopyright": "Symbole de copyright", "SSE.Views.Toolbar.textCounterCw": "Rotation dans le sens inverse des aiguilles d'une montre", "SSE.Views.Toolbar.textCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textDataBars": "Barres de données", "SSE.Views.Toolbar.textDegree": "Symbole degré", "SSE.Views.Toolbar.textDelLeft": "<PERSON><PERSON><PERSON>r les cellules vers la gauche", "SSE.Views.Toolbar.textDelPageBreak": "Supp<PERSON><PERSON> le saut de page", "SSE.Views.Toolbar.textDelta": "Lettre miniscule greque Delta", "SSE.Views.Toolbar.textDelUp": "<PERSON><PERSON><PERSON><PERSON> les cellules vers le haut", "SSE.Views.Toolbar.textDiagDownBorder": "Bord<PERSON> diagonale vers le bas", "SSE.Views.Toolbar.textDiagUpBorder": "<PERSON>rd<PERSON> diagonale vers le haut", "SSE.Views.Toolbar.textDivision": "Symbole de division", "SSE.Views.Toolbar.textDollar": "Symbole de dollar", "SSE.Views.Toolbar.textDone": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textDown": "En bas", "SSE.Views.Toolbar.textEditVA": "Modifier la zone visible", "SSE.Views.Toolbar.textEntireCol": "<PERSON><PERSON>ne <PERSON>", "SSE.Views.Toolbar.textEntireRow": "Ligne entière", "SSE.Views.Toolbar.textEuro": "Symbole euro", "SSE.Views.Toolbar.textFewPages": "Pages", "SSE.Views.Toolbar.textFillLeft": "À gauche", "SSE.Views.Toolbar.textFillRight": "À droite", "SSE.Views.Toolbar.textFormatCellFill": "Format du remplissage des cellules", "SSE.Views.Toolbar.textGreaterEqual": "Su<PERSON><PERSON><PERSON> ou égal à", "SSE.Views.Toolbar.textHeight": "<PERSON><PERSON>", "SSE.Views.Toolbar.textHideVA": "Masquer la zone visible", "SSE.Views.Toolbar.textHorizontal": "Texte horizontal", "SSE.Views.Toolbar.textInfinity": "Infini", "SSE.Views.Toolbar.textInsDown": "<PERSON><PERSON><PERSON><PERSON> les cellules vers le bas", "SSE.Views.Toolbar.textInsideBorders": "Bordures intérieures", "SSE.Views.Toolbar.textInsPageBreak": "Insérer un saut de page", "SSE.Views.Toolbar.textInsRight": "<PERSON><PERSON><PERSON><PERSON> les cellules vers la droite", "SSE.Views.Toolbar.textItalic": "Italique", "SSE.Views.Toolbar.textItems": "éléments", "SSE.Views.Toolbar.textLandscape": "Paysage", "SSE.Views.Toolbar.textLeft": "À gauche: ", "SSE.Views.Toolbar.textLeftBorders": "Bordures gauches", "SSE.Views.Toolbar.textLessEqual": "Inférieur ou égal à", "SSE.Views.Toolbar.textLetterPi": "Lettre miniscule grecque Pi", "SSE.Views.Toolbar.textManageRule": "<PERSON><PERSON><PERSON> les règles", "SSE.Views.Toolbar.textManyPages": "Pages", "SSE.Views.Toolbar.textMarginsLast": "<PERSON><PERSON><PERSON> mesure", "SSE.Views.Toolbar.textMarginsNarrow": "Étroit", "SSE.Views.Toolbar.textMarginsNormal": "Normal", "SSE.Views.Toolbar.textMarginsWide": "Large", "SSE.Views.Toolbar.textMiddleBorders": "Frontières internes horizontales", "SSE.Views.Toolbar.textMoreBorders": "Autres bordures", "SSE.Views.Toolbar.textMoreFormats": "Autres formats", "SSE.Views.Toolbar.textMorePages": "Plus de pages", "SSE.Views.Toolbar.textMoreSymbols": "Plus de symboles", "SSE.Views.Toolbar.textNewColor": "Plus de couleurs", "SSE.Views.Toolbar.textNewRule": "Nouvelle règle", "SSE.Views.Toolbar.textNoBorders": "Pas de bordures", "SSE.Views.Toolbar.textNotEqualTo": "N'est pas égal à", "SSE.Views.Toolbar.textOneHalf": "Fraction vulgaire de la moitié", "SSE.Views.Toolbar.textOnePage": "Page", "SSE.Views.Toolbar.textOneQuarter": "Fraction vulgaire un quart", "SSE.Views.Toolbar.textOutBorders": "Bordures extérieures", "SSE.Views.Toolbar.textPageMarginsCustom": "Marges personnalisées", "SSE.Views.Toolbar.textPlusMinus": "Signe plus ou moins", "SSE.Views.Toolbar.textPortrait": "Portrait", "SSE.Views.Toolbar.textPrint": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textPrintGridlines": "Imp<PERSON><PERSON> le quadrillage", "SSE.Views.Toolbar.textPrintHeadings": "Imprimer les en-têtes", "SSE.Views.Toolbar.textPrintOptions": "Paramètres d'impression", "SSE.Views.Toolbar.textRegistered": "Symbole de marque déposée", "SSE.Views.Toolbar.textResetPageBreak": "Réinitialiser tous les sauts de page", "SSE.Views.Toolbar.textRight": "À droite: ", "SSE.Views.Toolbar.textRightBorders": "Bordures droites", "SSE.Views.Toolbar.textRotateDown": "Rotation du texte vers le bas", "SSE.Views.Toolbar.textRotateUp": "Rotation du texte vers le haut", "SSE.Views.Toolbar.textRtlSheet": "<PERSON><PERSON>le de droite à gauche", "SSE.Views.Toolbar.textScale": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textScaleCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textSection": "Signe de paragraphe", "SSE.Views.Toolbar.textSelection": "De la sélection actuelle", "SSE.Views.Toolbar.textSeries": "Série", "SSE.Views.Toolbar.textSetPrintArea": "Selectionner la zone d'impression", "SSE.Views.Toolbar.textShapesCombine": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textShapesFragment": "Fragment", "SSE.Views.Toolbar.textShapesIntersect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textShapesSubstract": "Soustraire", "SSE.Views.Toolbar.textShapesUnion": "Union", "SSE.Views.Toolbar.textShowVA": "Afficher la zone visible", "SSE.Views.Toolbar.textSmile": "Visage souriant", "SSE.Views.Toolbar.textSquareRoot": "<PERSON><PERSON>", "SSE.Views.Toolbar.textStrikeout": "<PERSON><PERSON>", "SSE.Views.Toolbar.textSubscript": "Indice", "SSE.Views.Toolbar.textSubSuperscript": "Indice/Exposant", "SSE.Views.Toolbar.textSuperscript": "Exposant", "SSE.Views.Toolbar.textTabCollaboration": "Collaboration", "SSE.Views.Toolbar.textTabData": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabDraw": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFile": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabFormula": "Formule", "SSE.Views.Toolbar.textTabHome": "Accueil", "SSE.Views.Toolbar.textTabInsert": "Insertion", "SSE.Views.Toolbar.textTabLayout": "Mise en page", "SSE.Views.Toolbar.textTabProtect": "Protection", "SSE.Views.Toolbar.textTabView": "Affichage", "SSE.Views.Toolbar.textThisPivot": "À partir d'un tableau croisé dynamique", "SSE.Views.Toolbar.textThisSheet": "À partir de cette feuille", "SSE.Views.Toolbar.textThisTable": "À partir de ce tableau", "SSE.Views.Toolbar.textTilde": "<PERSON><PERSON>", "SSE.Views.Toolbar.textTop": "En haut: ", "SSE.Views.Toolbar.textTopBorders": "Bordures supérieures", "SSE.Views.Toolbar.textTradeMark": "Signe de marque", "SSE.Views.Toolbar.textUnderline": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textUp": "En haut", "SSE.Views.Toolbar.textVertical": "Texte vertical", "SSE.Views.Toolbar.textWidth": "<PERSON><PERSON>", "SSE.Views.Toolbar.textYen": "Symbole yen", "SSE.Views.Toolbar.textZoom": "Zoom", "SSE.Views.Toolbar.tipAlignBottom": "Aligner en bas", "SSE.Views.Toolbar.tipAlignCenter": "Aligner au centre", "SSE.Views.Toolbar.tipAlignJust": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignLeft": "<PERSON><PERSON><PERSON> à gauche", "SSE.Views.Toolbar.tipAlignMiddle": "Aligner au centre", "SSE.Views.Toolbar.tipAlignRight": "<PERSON><PERSON><PERSON> d<PERSON>", "SSE.Views.Toolbar.tipAlignTop": "Aligner en haut", "SSE.Views.Toolbar.tipAutofilter": "Trier et filtrer", "SSE.Views.Toolbar.tipBack": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipBorders": "Bordures", "SSE.Views.Toolbar.tipCellStyle": "Style de cellule", "SSE.Views.Toolbar.tipChangeCase": "Modifier la casse", "SSE.Views.Toolbar.tipChangeChart": "Modifier le type de graphique", "SSE.Views.Toolbar.tipClearStyle": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipColorSchemas": "Modifier le jeu de couleurs", "SSE.Views.Toolbar.tipCondFormat": "Mise en forme conditionnelle", "SSE.Views.Toolbar.tipCopy": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCopyStyle": "Co<PERSON><PERSON> le style", "SSE.Views.Toolbar.tipCut": "Couper", "SSE.Views.Toolbar.tipDecDecimal": "Réduire les décimales", "SSE.Views.Toolbar.tipDecFont": "Diminuer la taille de police", "SSE.Views.Toolbar.tipDeleteOpt": "Supprimer les cellules", "SSE.Views.Toolbar.tipDigStyleAccounting": "Style comptable", "SSE.Views.Toolbar.tipDigStyleComma": "Style de virgule", "SSE.Views.Toolbar.tipDigStyleCurrency": "Style monétaire", "SSE.Views.Toolbar.tipDigStylePercent": "Style de pourcentage", "SSE.Views.Toolbar.tipEditChart": "Modifier le graphique", "SSE.Views.Toolbar.tipEditChartData": "Sélectionner des données", "SSE.Views.Toolbar.tipEditChartType": "Modifier le type de graphique", "SSE.Views.Toolbar.tipEditHeader": "Modifier l'en-tête ou le pied de page", "SSE.Views.Toolbar.tipFontColor": "Couleur de police", "SSE.Views.Toolbar.tipFontName": "Police", "SSE.Views.Toolbar.tipFontSize": "Taille de police", "SSE.Views.Toolbar.tipHAlighOle": "Alignement horizontal", "SSE.Views.Toolbar.tipImgAlign": "<PERSON><PERSON><PERSON> les objets", "SSE.Views.Toolbar.tipImgGroup": "Grouper les  objets", "SSE.Views.Toolbar.tipIncDecimal": "Ajouter une décimale", "SSE.Views.Toolbar.tipIncFont": "Augmenter la taille de police", "SSE.Views.Toolbar.tipInsertChart": "Insérer un graphique", "SSE.Views.Toolbar.tipInsertChartRecommend": "Insérer le graphique recommandé", "SSE.Views.Toolbar.tipInsertChartSpark": "Insérer un graphique", "SSE.Views.Toolbar.tipInsertEquation": "Insérer une équation", "SSE.Views.Toolbar.tipInsertHorizontalText": "Insérer une zone de texte horizontale", "SSE.Views.Toolbar.tipInsertHyperlink": "Ajouter un lien hypertexte", "SSE.Views.Toolbar.tipInsertImage": "Insérer une image", "SSE.Views.Toolbar.tipInsertOpt": "Insérer les cellules", "SSE.Views.Toolbar.tipInsertShape": "Insérer une forme", "SSE.Views.Toolbar.tipInsertSlicer": "Isérer un segment", "SSE.Views.Toolbar.tipInsertSmartArt": "Insérer un graphique SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Insérer un graphique sparkline", "SSE.Views.Toolbar.tipInsertSymbol": "Insérer un symbole", "SSE.Views.Toolbar.tipInsertTable": "Insérer un tableau", "SSE.Views.Toolbar.tipInsertText": "Insérez zone de texte", "SSE.Views.Toolbar.tipInsertTextart": "Insérer Text Art", "SSE.Views.Toolbar.tipInsertVerticalText": "Insérer une zone de texte verticale", "SSE.Views.Toolbar.tipMerge": "Fusionner et centrer", "SSE.Views.Toolbar.tipNone": "Aucun", "SSE.Views.Toolbar.tipNumFormat": "Format de nombre", "SSE.Views.Toolbar.tipPageBreak": "A<PERSON><PERSON>z un saut de page où vous voulez que le prochain page commence dans la copie imprimée.", "SSE.Views.Toolbar.tipPageMargins": "<PERSON><PERSON> de la page", "SSE.Views.Toolbar.tipPageOrient": "Orientation de la page", "SSE.Views.Toolbar.tipPageSize": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipPaste": "<PERSON><PERSON>", "SSE.Views.Toolbar.tipPrColor": "Couleur de remplissage", "SSE.Views.Toolbar.tipPrint": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipPrintArea": "Zone d'impression", "SSE.Views.Toolbar.tipPrintQuick": "Impression rapide", "SSE.Views.Toolbar.tipPrintTitles": "Titres à imprimer", "SSE.Views.Toolbar.tipRedo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipReplace": "<PERSON><PERSON>lace<PERSON>", "SSE.Views.Toolbar.tipRtlSheet": "Inverser le sens de la feuille de manière à ce que la première colonne se trouve sur le côté droit", "SSE.Views.Toolbar.tipSave": "Enregistrer", "SSE.Views.Toolbar.tipSaveCoauth": "Enregistrez vos modifications pour que les autres utilisateurs puissent les voir.", "SSE.Views.Toolbar.tipScale": "Mettre à l’échelle", "SSE.Views.Toolbar.tipSelectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "SSE.Views.Toolbar.tipSendBackward": "Envoyer vers l'arrière.", "SSE.Views.Toolbar.tipSendForward": "Déplacer vers l'avant", "SSE.Views.Toolbar.tipShapesMerge": "Combiner les formes", "SSE.Views.Toolbar.tipSynchronize": "Le document a été modifié par un autre utilisateur. Cliquez pour enregistrer vos modifications et recharger des mises à jour.", "SSE.Views.Toolbar.tipTextFormatting": "Plus d'outils de formatage de texte", "SSE.Views.Toolbar.tipTextOrientation": "Orientation", "SSE.Views.Toolbar.tipUndo": "Annuler", "SSE.Views.Toolbar.tipVAlighOle": "Alignement vertical", "SSE.Views.Toolbar.tipVisibleArea": "Zone visible", "SSE.Views.Toolbar.tipWrap": "Renvoyer à la ligne", "SSE.Views.Toolbar.txtAccounting": "Comptabilité", "SSE.Views.Toolbar.txtAdditional": "Supplémentaire", "SSE.Views.Toolbar.txtAscending": "Croissant", "SSE.Views.Toolbar.txtAutosumTip": "Somme", "SSE.Views.Toolbar.txtCellStyle": "Style de cellule", "SSE.Views.Toolbar.txtClearAll": "<PERSON>ut", "SSE.Views.Toolbar.txtClearComments": "Commentaires", "SSE.Views.Toolbar.txtClearFilter": "<PERSON><PERSON><PERSON><PERSON> le filtre", "SSE.Views.Toolbar.txtClearFormat": "Format", "SSE.Views.Toolbar.txtClearFormula": "Fonction", "SSE.Views.Toolbar.txtClearHyper": "Liens hypertextes", "SSE.Views.Toolbar.txtClearText": "Texte", "SSE.Views.Toolbar.txtCurrency": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtCustom": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtDate": "Date", "SSE.Views.Toolbar.txtDateLong": "Date longue", "SSE.Views.Toolbar.txtDateShort": "Date courte", "SSE.Views.Toolbar.txtDateTime": "Date et heure", "SSE.Views.Toolbar.txtDescending": "Décroissant", "SSE.Views.Toolbar.txtDollar": "$ Dollar", "SSE.Views.Toolbar.txtEuro": "€ Euro", "SSE.Views.Toolbar.txtExp": "Exponentielle", "SSE.Views.Toolbar.txtFillNum": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtFilter": "Filtre", "SSE.Views.Toolbar.txtFormula": "Insérer une fonction", "SSE.Views.Toolbar.txtFraction": "Fraction", "SSE.Views.Toolbar.txtFranc": "CHF Franc suisse", "SSE.Views.Toolbar.txtGeneral": "Général", "SSE.Views.Toolbar.txtInteger": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtManageRange": "Gestionnaire de noms", "SSE.Views.Toolbar.txtMergeAcross": "Fusionner les cellules sélectionnées", "SSE.Views.Toolbar.txtMergeCells": "Fusionner des cellules", "SSE.Views.Toolbar.txtMergeCenter": "Fusionner et centrer ", "SSE.Views.Toolbar.txtNamedRange": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtNewRange": "Définir le nom", "SSE.Views.Toolbar.txtNoBorders": "Pas de bordures", "SSE.Views.Toolbar.txtNumber": "Nombre", "SSE.Views.Toolbar.txtPasteRange": "Coller un nom", "SSE.Views.Toolbar.txtPercentage": "Pourcentage", "SSE.Views.Toolbar.txtPound": "£ Livre", "SSE.Views.Toolbar.txtRouble": "₽ Rouble", "SSE.Views.Toolbar.txtScientific": "Scientifique", "SSE.Views.Toolbar.txtSearch": "<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.txtSort": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtSortAZ": "Trier par ordre croissant", "SSE.Views.Toolbar.txtSortZA": "Trier par ordre décroissant", "SSE.Views.Toolbar.txtSpecial": "Spécial", "SSE.Views.Toolbar.txtTableTemplate": "Mettre sous forme de modèle de tableau", "SSE.Views.Toolbar.txtText": "Texte", "SSE.Views.Toolbar.txtTime": "<PERSON><PERSON>", "SSE.Views.Toolbar.txtUnmerge": "Annuler la fusion des cellules", "SSE.Views.Toolbar.txtYen": "¥ Yen", "SSE.Views.Top10FilterDialog.textType": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Top10FilterDialog.txtBottom": "En bas", "SSE.Views.Top10FilterDialog.txtBy": "par", "SSE.Views.Top10FilterDialog.txtItems": "Element", "SSE.Views.Top10FilterDialog.txtPercent": "Pour cent", "SSE.Views.Top10FilterDialog.txtSum": "Somme", "SSE.Views.Top10FilterDialog.txtTitle": "Les 10 premiers de filtre automatique", "SSE.Views.Top10FilterDialog.txtTop": "En haut", "SSE.Views.Top10FilterDialog.txtValueTitle": "Le top 10 filtres", "SSE.Views.ValueFieldSettingsDialog.textNext": "(suivant)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "Format de nombre", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(précédent)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Paramètres des champs de valeurs", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Champ de base", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Élément de base", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 de %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Total", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Nom", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Différence par rapport à", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Index", "SSE.Views.ValueFieldSettingsDialog.txtMax": "Max", "SSE.Views.ValueFieldSettingsDialog.txtMin": "Min", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "Aucun calcul", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "% de", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Différence en % par rapport", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "% de la colonne", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% du total général", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% du total du parent", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% du total de la colonne parente", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% du total de la ligne parente", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% résultat cumulé dans", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "% de la ligne", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Produit", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Trier du plus petit au plus grand", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Trier du plus grand au plus petit", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "Résultat cumulé par", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Afficher les valeurs comme", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Nom de la source :", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "Écartype", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "StdDevp", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Somme", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Résumer les valeurs du champs par", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Var", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.guestText": "Invi<PERSON>", "SSE.Views.ViewManagerDlg.lockText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textDelete": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textDuplicate": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewManagerDlg.textEmpty": "Aucun aperçu n'a été créé.", "SSE.Views.ViewManagerDlg.textGoTo": "Passer à l'affichage", "SSE.Views.ViewManagerDlg.textLongName": "Le nom de l'aperçu est limité à 128 symboles.", "SSE.Views.ViewManagerDlg.textNew": "Nouveau", "SSE.Views.ViewManagerDlg.textRename": "<PERSON>mmer", "SSE.Views.ViewManagerDlg.textRenameError": "Le nom d'affichage ne peut pas être vide.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Renommer l'aperçu", "SSE.Views.ViewManagerDlg.textViews": "<PERSON><PERSON>", "SSE.Views.ViewManagerDlg.tipIsLocked": "Cet élément est en cours de modification par un autre utilisateur.", "SSE.Views.ViewManagerDlg.txtTitle": "Gestionnaire du mode Feuille", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Voulez-vous vraiment supprimer ce mode Feuille?", "SSE.Views.ViewManagerDlg.warnDeleteView": "Vous essayez de supprimer l'affichage '%1' qui est maintenant actif.<br><PERSON><PERSON><PERSON> et supprimer cet affichage ?", "SSE.Views.ViewTab.capBtnFreeze": "Figer les volets", "SSE.Views.ViewTab.capBtnSheetView": "Mode Feuille", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Toujours afficher la barre d'outils", "SSE.Views.ViewTab.textClose": "<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Combiner la barre de la feuille et la barre d'état", "SSE.Views.ViewTab.textCreate": "Nouveau", "SSE.Views.ViewTab.textDefault": "<PERSON><PERSON> <PERSON><PERSON>", "SSE.Views.ViewTab.textFill": "Remplissage", "SSE.Views.ViewTab.textFormula": "Barre de formule", "SSE.Views.ViewTab.textFreezeCol": "Figer la première colonne", "SSE.Views.ViewTab.textFreezeRow": "Figer la ligne supérieure", "SSE.Views.ViewTab.textGridlines": "Quadrillage", "SSE.Views.ViewTab.textHeadings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.textInterfaceTheme": "Thème d'interface", "SSE.Views.ViewTab.textLeftMenu": "Panneau gauche", "SSE.Views.ViewTab.textLine": "Ligne", "SSE.Views.ViewTab.textMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.textManager": "Gestionnaire d'affichage", "SSE.Views.ViewTab.textRightMenu": "Panneau droit", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Afficher l'ombre des volets figés", "SSE.Views.ViewTab.textTabStyle": "Style d'onglet", "SSE.Views.ViewTab.textUnFreeze": "Lib<PERSON>rer les volets", "SSE.Views.ViewTab.textZeros": "Afficher les zéros", "SSE.Views.ViewTab.textZoom": "Changer l'<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.tipClose": "<PERSON><PERSON><PERSON> le <PERSON>", "SSE.Views.ViewTab.tipCreate": "<PERSON><PERSON>er un mode Feuille", "SSE.Views.ViewTab.tipFreeze": "Figer les volets", "SSE.Views.ViewTab.tipInterfaceTheme": "Thème d'interface", "SSE.Views.ViewTab.tipMacros": "<PERSON><PERSON>", "SSE.Views.ViewTab.tipSheetView": "Mode Feuille", "SSE.Views.ViewTab.tipViewNormal": "Voir votre document en Affichage normal", "SSE.Views.ViewTab.tipViewPageBreak": "Voir où les sauts de page apparaîtront lors de l'impression du document", "SSE.Views.ViewTab.txtViewNormal": "Normal", "SSE.Views.ViewTab.txtViewPageBreak": "Aperçu des sauts de page", "SSE.Views.WatchDialog.closeButtonText": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textAdd": "Ajouter un espion", "SSE.Views.WatchDialog.textBook": "Classeur", "SSE.Views.WatchDialog.textCell": "Cellule", "SSE.Views.WatchDialog.textDelete": "Supprimer l'espion", "SSE.Views.WatchDialog.textDeleteAll": "Supp<PERSON>er tout", "SSE.Views.WatchDialog.textFormula": "Formule", "SSE.Views.WatchDialog.textName": "Nom", "SSE.Views.WatchDialog.textSheet": "<PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textValue": "<PERSON><PERSON>", "SSE.Views.WatchDialog.txtTitle": "Fenêtre Espions", "SSE.Views.WBProtection.hintAllowRanges": "Autoriser la modification des plages", "SSE.Views.WBProtection.hintProtectRange": "Protéger la plage", "SSE.Views.WBProtection.hintProtectSheet": "Protéger la feuille de calcul", "SSE.Views.WBProtection.hintProtectWB": "<PERSON><PERSON><PERSON> le classeur", "SSE.Views.WBProtection.txtAllowRanges": "Autoriser la modification des plages", "SSE.Views.WBProtection.txtHiddenFormula": "<PERSON>ules masquées", "SSE.Views.WBProtection.txtLockedCell": "Cellule verrouillée", "SSE.Views.WBProtection.txtLockedShape": "Forme verrouillée", "SSE.Views.WBProtection.txtLockedText": "Verouil<PERSON> le texte", "SSE.Views.WBProtection.txtProtectRange": "Protéger la plage", "SSE.Views.WBProtection.txtProtectSheet": "Protéger la feuille de calcul", "SSE.Views.WBProtection.txtProtectWB": "<PERSON><PERSON><PERSON> le classeur", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Entrez un mot de passe pour déprotéger la feuille de calcul", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Déprotéger la feuille de calcul", "SSE.Views.WBProtection.txtWBUnlockDescription": "Entrez un mot de passe pour déprotéger le classeur", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin"}