{"cancelButtonText": "Отмена", "Common.Controllers.Chat.notcriticalErrorTitle": "Предупреждение", "Common.Controllers.Desktop.hintBtnHome": "Показать главное окно", "Common.Controllers.Desktop.itemCreateFromTemplate": "Создать по шаблону", "Common.Controllers.History.notcriticalErrorTitle": "Внимание", "Common.Controllers.History.txtErrorLoadHistory": "Не удалось загрузить историю", "Common.Controllers.Plugins.helpUseMacros": "Кнопку Макросы можно найти здесь", "Common.Controllers.Plugins.helpUseMacrosHeader": "Обновлен доступ к макросам", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Плагины успешно установлены. Здесь вы можете получить доступ ко всем фоновым плагинам.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> успешно установлен. Здесь вы можете получить доступ ко всем фоновым плагинам.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Запустить установленные плагины", "Common.Controllers.Plugins.textRunPlugin": "Запустить плагин", "Common.define.chartData.textArea": "С областями", "Common.define.chartData.textAreaStacked": "Диаграмма с областями с накоплением", "Common.define.chartData.textAreaStackedPer": "Нормированная с областями и накоплением", "Common.define.chartData.textBar": "Лин<PERSON>йчатая", "Common.define.chartData.textBarNormal": "Гистограмма с группировкой", "Common.define.chartData.textBarNormal3d": "Трехмерная гистограмма с группировкой", "Common.define.chartData.textBarNormal3dPerspective": "Трехмерная гистограмма", "Common.define.chartData.textBarStacked": "Гистограмма с накоплением", "Common.define.chartData.textBarStacked3d": "Трехмерная гистограмма с накоплением", "Common.define.chartData.textBarStackedPer": "Нормированная гистограмма с накоплением", "Common.define.chartData.textBarStackedPer3d": "Трехмерная нормированная гистограмма с накоплением", "Common.define.chartData.textCharts": "Диаграммы", "Common.define.chartData.textColumn": "Гистограмма", "Common.define.chartData.textColumnSpark": "Гистограмма", "Common.define.chartData.textCombo": "Комбинированные", "Common.define.chartData.textComboAreaBar": "С областями с накоплением и гистограмма с группировкой", "Common.define.chartData.textComboBarLine": "Гистограмма с группировкой и график", "Common.define.chartData.textComboBarLineSecondary": "Гистограмма с группировкой и график на вспомогательной оси", "Common.define.chartData.textComboCustom": "Пользовательская комбинация", "Common.define.chartData.textDoughnut": "Кольцевая диаграмма", "Common.define.chartData.textHBarNormal": "Линейчатая с группировкой", "Common.define.chartData.textHBarNormal3d": "Трехмерная линейчатая с группировкой", "Common.define.chartData.textHBarStacked": "Линейчатая с накоплением", "Common.define.chartData.textHBarStacked3d": "Трехмерная линейчатая с накоплением", "Common.define.chartData.textHBarStackedPer": "Нормированная линейчатая с накоплением", "Common.define.chartData.textHBarStackedPer3d": "Трехмерная нормированная линейчатая с накоплением", "Common.define.chartData.textLine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textLine3d": "Трехмерный график", "Common.define.chartData.textLineMarker": "График с маркерами", "Common.define.chartData.textLineSpark": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.chartData.textLineStacked": "График с накоплением", "Common.define.chartData.textLineStackedMarker": "График с накоплением с маркерами", "Common.define.chartData.textLineStackedPer": "Нормированный график с накоплением", "Common.define.chartData.textLineStackedPerMarker": "Нормированный график с маркерами и накоплением", "Common.define.chartData.textPie": "Круговая", "Common.define.chartData.textPie3d": "Трехмерная круговая диаграмма", "Common.define.chartData.textPoint": "Точечная", "Common.define.chartData.textRadar": "Лепестковая", "Common.define.chartData.textRadarFilled": "Заполненная лепестковая", "Common.define.chartData.textRadarMarker": "Лепестковая с маркерами", "Common.define.chartData.textScatter": "Точечная диаграмма", "Common.define.chartData.textScatterLine": "Точечная с прямыми отрезками", "Common.define.chartData.textScatterLineMarker": "Точечная с прямыми отрезками и маркерами", "Common.define.chartData.textScatterSmooth": "Точечная с гладкими кривыми", "Common.define.chartData.textScatterSmoothMarker": "Точечная с гладкими кривыми и маркерами", "Common.define.chartData.textSparks": "Спарклайны", "Common.define.chartData.textStock": "Биржевая", "Common.define.chartData.textSurface": "Поверхность", "Common.define.chartData.textWinLossSpark": "Выигрыш/проигрыш", "Common.define.conditionalData.exampleText": "AaBbБбЯя", "Common.define.conditionalData.noFormatText": "Формат не задан", "Common.define.conditionalData.text1Above": "На 1 стандартное отклонение выше", "Common.define.conditionalData.text1Below": "На 1 стандартное отклонение ниже", "Common.define.conditionalData.text2Above": "На 2 стандартных отклонения выше", "Common.define.conditionalData.text2Below": "На 2 стандартных отклонения ниже", "Common.define.conditionalData.text3Above": "На 3 стандартных отклонения выше", "Common.define.conditionalData.text3Below": "На 3 стандартных отклонения ниже", "Common.define.conditionalData.textAbove": "Выше", "Common.define.conditionalData.textAverage": "Среднее", "Common.define.conditionalData.textBegins": "Начинается с", "Common.define.conditionalData.textBelow": "Ниже", "Common.define.conditionalData.textBetween": "Между", "Common.define.conditionalData.textBlank": "Пустая ячейка", "Common.define.conditionalData.textBlanks": "Содержит пустые ячейки", "Common.define.conditionalData.textBottom": "Наименьшее", "Common.define.conditionalData.textContains": "Соде<PERSON><PERSON><PERSON>т", "Common.define.conditionalData.textDataBar": "Гистограмма", "Common.define.conditionalData.textDate": "Дата", "Common.define.conditionalData.textDuplicate": "Повторяющееся", "Common.define.conditionalData.textEnds": "Заканчивается на", "Common.define.conditionalData.textEqAbove": "Равно или больше", "Common.define.conditionalData.textEqBelow": "Равно или меньше", "Common.define.conditionalData.textEqual": "Равно", "Common.define.conditionalData.textError": "Ошибка", "Common.define.conditionalData.textErrors": "Содер<PERSON><PERSON>т ошибки", "Common.define.conditionalData.textFormula": "Формула", "Common.define.conditionalData.textGreater": "Больше", "Common.define.conditionalData.textGreaterEq": "Больше или равно", "Common.define.conditionalData.textIconSets": "Наборы значков", "Common.define.conditionalData.textLast7days": "За последние 7 дней", "Common.define.conditionalData.textLastMonth": "Прошлый месяц", "Common.define.conditionalData.textLastWeek": "Прошлая неделя", "Common.define.conditionalData.textLess": "Меньше", "Common.define.conditionalData.textLessEq": "Меньше или равно", "Common.define.conditionalData.textNextMonth": "Следующий месяц", "Common.define.conditionalData.textNextWeek": "Следующая неделя", "Common.define.conditionalData.textNotBetween": "Не между", "Common.define.conditionalData.textNotBlanks": "Не содержит пустых ячеек", "Common.define.conditionalData.textNotContains": "Не содержит", "Common.define.conditionalData.textNotEqual": "Не равно", "Common.define.conditionalData.textNotErrors": "Не содержит ошибок", "Common.define.conditionalData.textText": "Текст", "Common.define.conditionalData.textThisMonth": "Этот месяц", "Common.define.conditionalData.textThisWeek": "Эта неделя", "Common.define.conditionalData.textToday": "Сегодня", "Common.define.conditionalData.textTomorrow": "Завтра", "Common.define.conditionalData.textTop": "Наибольшее", "Common.define.conditionalData.textUnique": "Уникальное", "Common.define.conditionalData.textValue": "Значение равно", "Common.define.conditionalData.textYesterday": "Вчера", "Common.define.smartArt.textAccentedPicture": "Акцентируемый рисунок", "Common.define.smartArt.textAccentProcess": "Процесс со смещением", "Common.define.smartArt.textAlternatingFlow": "Переменный поток", "Common.define.smartArt.textAlternatingHexagons": "Чередующиеся шестиугольники", "Common.define.smartArt.textAlternatingPictureBlocks": "Чередующиеся блоки рисунков", "Common.define.smartArt.textAlternatingPictureCircles": "Чередующиеся круги рисунков", "Common.define.smartArt.textArchitectureLayout": "Архитектурный макет", "Common.define.smartArt.textArrowRibbon": "Лента со стрелками", "Common.define.smartArt.textAscendingPictureAccentProcess": "Процесс со смещенными по возрастанию рисунками", "Common.define.smartArt.textBalance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.define.smartArt.textBasicBendingProcess": "Простой ломаный процесс", "Common.define.smartArt.textBasicBlockList": "Простой блочный список", "Common.define.smartArt.textBasicChevronProcess": "Простой уголковый процесс", "Common.define.smartArt.textBasicCycle": "Простой цикл", "Common.define.smartArt.textBasicMatrix": "Простая матрица", "Common.define.smartArt.textBasicPie": "Простая круговая", "Common.define.smartArt.textBasicProcess": "Простой процесс", "Common.define.smartArt.textBasicPyramid": "Простая пирамида", "Common.define.smartArt.textBasicRadial": "Простая радиальная", "Common.define.smartArt.textBasicTarget": "Простая целевая", "Common.define.smartArt.textBasicTimeline": "Простая временная шкала", "Common.define.smartArt.textBasicVenn": "Простая Венна", "Common.define.smartArt.textBendingPictureAccentList": "Ломаный список со смещенными рисунками", "Common.define.smartArt.textBendingPictureBlocks": "Нелинейные рисунки с блоками", "Common.define.smartArt.textBendingPictureCaption": "Нелинейные рисунки с подписями", "Common.define.smartArt.textBendingPictureCaptionList": "Ломаный список рисунков с подписями", "Common.define.smartArt.textBendingPictureSemiTranparentText": "Нелинейные рисунки с полупрозрачным текстом", "Common.define.smartArt.textBlockCycle": "Блочный цикл", "Common.define.smartArt.textBubblePictureList": "Список рисунков с выносками", "Common.define.smartArt.textCaptionedPictures": "Подписанные рисунки", "Common.define.smartArt.textChevronAccentProcess": "Уголковый процесс со смещением", "Common.define.smartArt.textChevronList": "Уголковый список", "Common.define.smartArt.textCircleAccentTimeline": "Круглая временная шкала", "Common.define.smartArt.textCircleArrowProcess": "Стрелка процесса с кругами", "Common.define.smartArt.textCirclePictureHierarchy": "Иерархия с круглыми рисунками", "Common.define.smartArt.textCircleProcess": "Процесс с кругами", "Common.define.smartArt.textCircleRelationship": "Круг связей", "Common.define.smartArt.textCircularBendingProcess": "Круглый ломаный процесс", "Common.define.smartArt.textCircularPictureCallout": "Выноска с круглыми рисунками", "Common.define.smartArt.textClosedChevronProcess": "Закрытый уголковый процесс", "Common.define.smartArt.textContinuousArrowProcess": "Стрелка непрерывного процесса", "Common.define.smartArt.textContinuousBlockProcess": "Непрерывный блочный процесс", "Common.define.smartArt.textContinuousCycle": "Непрерывный цикл", "Common.define.smartArt.textContinuousPictureList": "Непрерывный список с рисунками", "Common.define.smartArt.textConvergingArrows": "Сходящиеся стрелки", "Common.define.smartArt.textConvergingRadial": "Сходящаяся радиальная", "Common.define.smartArt.textConvergingText": "Сходящийся текст", "Common.define.smartArt.textCounterbalanceArrows": "Уравновешивающие стрелки", "Common.define.smartArt.textCycle": "<PERSON>и<PERSON><PERSON>", "Common.define.smartArt.textCycleMatrix": "Циклическая матрица", "Common.define.smartArt.textDescendingBlockList": "Нисходящий блочный список", "Common.define.smartArt.textDescendingProcess": "Убывающий процесс", "Common.define.smartArt.textDetailedProcess": "Подробный процесс", "Common.define.smartArt.textDivergingArrows": "Расходящиеся стрелки", "Common.define.smartArt.textDivergingRadial": "Расходящаяся радиальная", "Common.define.smartArt.textEquation": "Уравнение", "Common.define.smartArt.textFramedTextPicture": "Рисунок с текстом в рамке", "Common.define.smartArt.textFunnel": "Воронка", "Common.define.smartArt.textGear": "Шестеренка", "Common.define.smartArt.textGridMatrix": "Сетчатая матрица", "Common.define.smartArt.textGroupedList": "Сгруппированный список", "Common.define.smartArt.textHalfCircleOrganizationChart": "Полукруглая организационная диаграмма", "Common.define.smartArt.textHexagonCluster": "Кластер шестиугольников", "Common.define.smartArt.textHexagonRadial": "Радиальный шестиугольник", "Common.define.smartArt.textHierarchy": "Иерархия", "Common.define.smartArt.textHierarchyList": "Иерархический список", "Common.define.smartArt.textHorizontalBulletList": "Горизонтальный маркированный список", "Common.define.smartArt.textHorizontalHierarchy": "Горизонтальная иерархия", "Common.define.smartArt.textHorizontalLabeledHierarchy": "Горизонтальная иерархия с подписями", "Common.define.smartArt.textHorizontalMultiLevelHierarchy": "Горизонтальная многоуровневая иерархия", "Common.define.smartArt.textHorizontalOrganizationChart": "Горизонтальная организационная диаграмма", "Common.define.smartArt.textHorizontalPictureList": "Горизонтальный список рисунков", "Common.define.smartArt.textIncreasingArrowProcess": "Стрелка нарастающего процесса", "Common.define.smartArt.textIncreasingCircleProcess": "Нарастающий процесс с кругами", "Common.define.smartArt.textInterconnectedBlockProcess": "Процесс со взаимосвязанными блоками", "Common.define.smartArt.textInterconnectedRings": "Взаимосвязанные кольца", "Common.define.smartArt.textInvertedPyramid": "Инвертированная пирамида", "Common.define.smartArt.textLabeledHierarchy": "Иерархия с подписями", "Common.define.smartArt.textLinearVenn": "Линейная Венна", "Common.define.smartArt.textLinedList": "Список с линиями", "Common.define.smartArt.textList": "Список", "Common.define.smartArt.textMatrix": "Матрица", "Common.define.smartArt.textMultidirectionalCycle": "Разнонаправленный цикл", "Common.define.smartArt.textNameAndTitleOrganizationChart": "Организационная диаграмма с именами и должностями", "Common.define.smartArt.textNestedTarget": "Вложенная целевая", "Common.define.smartArt.textNondirectionalCycle": "Ненаправленный цикл", "Common.define.smartArt.textOpposingArrows": "Противостоящие стрелки", "Common.define.smartArt.textOpposingIdeas": "Противоположные идеи", "Common.define.smartArt.textOrganizationChart": "Организационная диаграмма", "Common.define.smartArt.textOther": "Другое", "Common.define.smartArt.textPhasedProcess": "Поэтапный процесс", "Common.define.smartArt.textPicture": "Рисунок", "Common.define.smartArt.textPictureAccentBlocks": "Блоки со смещенными рисунками", "Common.define.smartArt.textPictureAccentList": "Список со смещенными рисунками", "Common.define.smartArt.textPictureAccentProcess": "Процесс со смещенными рисунками", "Common.define.smartArt.textPictureCaptionList": "Список названий рисунков", "Common.define.smartArt.textPictureFrame": "Фоторамка", "Common.define.smartArt.textPictureGrid": "Сетка рисунков", "Common.define.smartArt.textPictureLineup": "Линия рисунков", "Common.define.smartArt.textPictureOrganizationChart": "Организационная диаграмма с рисунками", "Common.define.smartArt.textPictureStrips": "Полосы рисунков", "Common.define.smartArt.textPieProcess": "Процесс с круговой диаграммой", "Common.define.smartArt.textPlusAndMinus": "Плюс и минус", "Common.define.smartArt.textProcess": "Процесс", "Common.define.smartArt.textProcessArrows": "Стрелки процесса", "Common.define.smartArt.textProcessList": "Список процессов", "Common.define.smartArt.textPyramid": "Пирамида", "Common.define.smartArt.textPyramidList": "Пирамидальный список", "Common.define.smartArt.textRadialCluster": "Радиал<PERSON>ный кластер", "Common.define.smartArt.textRadialCycle": "Радиальная циклическая", "Common.define.smartArt.textRadialList": "Радиальный список", "Common.define.smartArt.textRadialPictureList": "Радиальный список рисунков", "Common.define.smartArt.textRadialVenn": "Радиальная Венна", "Common.define.smartArt.textRandomToResultProcess": "Процесс от случайности к результату", "Common.define.smartArt.textRelationship": "Связь", "Common.define.smartArt.textRepeatingBendingProcess": "Повторяющийся ломаный процесс", "Common.define.smartArt.textReverseList": "Обратный список", "Common.define.smartArt.textSegmentedCycle": "Сегментированный цикл", "Common.define.smartArt.textSegmentedProcess": "Сегментированный процесс", "Common.define.smartArt.textSegmentedPyramid": "Сегментированная пирамида", "Common.define.smartArt.textSnapshotPictureList": "Список со снимками", "Common.define.smartArt.textSpiralPicture": "Спираль рисунков", "Common.define.smartArt.textSquareAccentList": "Список с квадратиками", "Common.define.smartArt.textStackedList": "Список в столбик", "Common.define.smartArt.textStackedVenn": "Венна в столбик", "Common.define.smartArt.textStaggeredProcess": "Ступенчатый процесс", "Common.define.smartArt.textStepDownProcess": "Нисходящий процесс", "Common.define.smartArt.textStepUpProcess": "Восходящий процесс", "Common.define.smartArt.textSubStepProcess": "Процесс с вложенными шагами", "Common.define.smartArt.textTabbedArc": "Дуга с вкладками", "Common.define.smartArt.textTableHierarchy": "Табличная иерархия", "Common.define.smartArt.textTableList": "Табличный список", "Common.define.smartArt.textTabList": "Список вкладок", "Common.define.smartArt.textTargetList": "Целевой список", "Common.define.smartArt.textTextCycle": "Текстовый цикл", "Common.define.smartArt.textThemePictureAccent": "Смещенные рисунки темы", "Common.define.smartArt.textThemePictureAlternatingAccent": "Чередующиеся смещенные рисунки темы", "Common.define.smartArt.textThemePictureGrid": "Сетка рисунков темы", "Common.define.smartArt.textTitledMatrix": "Матрица с заголовками", "Common.define.smartArt.textTitledPictureAccentList": "Список со смещенными рисунками и заголовком", "Common.define.smartArt.textTitledPictureBlocks": "Блоки рисунков с названиями", "Common.define.smartArt.textTitlePictureLineup": "Линия рисунков с названиями", "Common.define.smartArt.textTrapezoidList": "Трапециевидный список", "Common.define.smartArt.textUpwardArrow": "Восходящая стрелка", "Common.define.smartArt.textVaryingWidthList": "Список переменной ширины", "Common.define.smartArt.textVerticalAccentList": "Вертикальный список со смещением", "Common.define.smartArt.textVerticalArrowList": "Вертикальный список со стрелкой", "Common.define.smartArt.textVerticalBendingProcess": "Вертикальный ломаный процесс", "Common.define.smartArt.textVerticalBlockList": "Вертикальный блочный список", "Common.define.smartArt.textVerticalBoxList": "Вертикальный список", "Common.define.smartArt.textVerticalBracketList": "Вертикальный список со скобками", "Common.define.smartArt.textVerticalBulletList": "Вертикальный маркированный список", "Common.define.smartArt.textVerticalChevronList": "Вертикальный уголковый список", "Common.define.smartArt.textVerticalCircleList": "Вертикальный список с кругами", "Common.define.smartArt.textVerticalCurvedList": "Вертикальный нелинейный список", "Common.define.smartArt.textVerticalEquation": "Вертикальное уравнение", "Common.define.smartArt.textVerticalPictureAccentList": "Вертикальный список со смещенными рисунками", "Common.define.smartArt.textVerticalPictureList": "Вертикальный список рисунков", "Common.define.smartArt.textVerticalProcess": "Вертикальный процесс", "Common.Translation.textMoreButton": "<PERSON><PERSON><PERSON>", "Common.Translation.tipFileLocked": "Документ заблокирован на редактирование. Вы можете внести изменения и сохранить его как локальную копию позже.", "Common.Translation.tipFileReadOnly": "Файл доступен только для чтения. Чтобы сохранить изменения, сохраните файл с новым названием или в другом месте.", "Common.Translation.warnFileLocked": "Файл редактируется в другом приложении. Вы можете продолжить редактирование и сохранить его как копию.", "Common.Translation.warnFileLockedBtnEdit": "Создать копию", "Common.Translation.warnFileLockedBtnView": "Открыть на просмотр", "Common.UI.ButtonColored.textAutoColor": "Автоматический", "Common.UI.ButtonColored.textEyedropper": "Пипетка", "Common.UI.ButtonColored.textNewColor": "Другие цвета", "Common.UI.ComboBorderSize.txtNoBorders": "Без границ", "Common.UI.ComboBorderSizeEditable.txtNoBorders": "Без границ", "Common.UI.ComboDataView.emptyComboText": "Без стилей", "Common.UI.ExtendedColorDialog.addButtonText": "Добавить", "Common.UI.ExtendedColorDialog.textCurrent": "Текущий", "Common.UI.ExtendedColorDialog.textHexErr": "Введено некорректное значение.<br>Пожалуйста, введите значение от 000000 до FFFFFF.", "Common.UI.ExtendedColorDialog.textNew": "Новый", "Common.UI.ExtendedColorDialog.textRGBErr": "Введено некорректное значение.<br>Пожалуйста, введите числовое значение от 0 до 255.", "Common.UI.HSBColorPicker.textNoColor": "Без цвета", "Common.UI.InputField.txtEmpty": "Это поле необходимо заполнить", "Common.UI.InputFieldBtnPassword.textHintHidePwd": "Скрыть пароль", "Common.UI.InputFieldBtnPassword.textHintHold": "Нажмите и удерживайте, чтобы показать пароль", "Common.UI.InputFieldBtnPassword.textHintShowPwd": "Показать пароль", "Common.UI.SearchBar.textFind": "Поиск", "Common.UI.SearchBar.tipCloseSearch": "Закрыть поиск", "Common.UI.SearchBar.tipNextResult": "Следующий результат", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Открыть дополнительные параметры", "Common.UI.SearchBar.tipPreviousResult": "Предыдущий результат", "Common.UI.SearchDialog.textHighlight": "Выделить результаты", "Common.UI.SearchDialog.textMatchCase": "С учетом регистра", "Common.UI.SearchDialog.textReplaceDef": "Введите текст для замены", "Common.UI.SearchDialog.textSearchStart": "Введите здесь текст", "Common.UI.SearchDialog.textTitle": "Поиск и замена", "Common.UI.SearchDialog.textTitle2": "Поиск", "Common.UI.SearchDialog.textWholeWords": "Только слово целиком", "Common.UI.SearchDialog.txtBtnHideReplace": "Скрыть поле замены", "Common.UI.SearchDialog.txtBtnReplace": "Заменить", "Common.UI.SearchDialog.txtBtnReplaceAll": "Заменить все", "Common.UI.SynchronizeTip.textDontShow": "Больше не показывать это сообщение", "Common.UI.SynchronizeTip.textGotIt": "ОК", "Common.UI.SynchronizeTip.textSynchronize": "Документ изменен другим пользователем.<br/>Нажмите, чтобы сохранить свои изменения и загрузить обновления.", "Common.UI.ThemeColorPalette.textRecentColors": "Недавние цвета", "Common.UI.ThemeColorPalette.textStandartColors": "Стандартные цвета", "Common.UI.ThemeColorPalette.textThemeColors": "Цвета темы", "Common.UI.Themes.txtThemeClassicLight": "Классическая светлая", "Common.UI.Themes.txtThemeContrastDark": "Контрастная темная", "Common.UI.Themes.txtThemeDark": "Темная", "Common.UI.Themes.txtThemeGray": "Серая", "Common.UI.Themes.txtThemeLight": "Светлая", "Common.UI.Themes.txtThemeSystem": "Системная", "Common.UI.Window.cancelButtonText": "Отмена", "Common.UI.Window.closeButtonText": "Закрыть", "Common.UI.Window.noButtonText": "Нет", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Подтверждение", "Common.UI.Window.textDontShow": "Больше не показывать это сообщение", "Common.UI.Window.textError": "Ошибка", "Common.UI.Window.textInformation": "Информация", "Common.UI.Window.textWarning": "Внимание", "Common.UI.Window.yesButtonText": "Да", "Common.Utils.Metric.txtCm": "см", "Common.Utils.Metric.txtPt": "пт", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Utils.ThemeColor.txtaccent": "Акцент", "Common.Utils.ThemeColor.txtAqua": "Темно-бирюзовый", "Common.Utils.ThemeColor.txtbackground": "Фон", "Common.Utils.ThemeColor.txtBlack": "Черный", "Common.Utils.ThemeColor.txtBlue": "Синий", "Common.Utils.ThemeColor.txtBrightGreen": "Ярко-зеленый", "Common.Utils.ThemeColor.txtBrown": "Коричневый", "Common.Utils.ThemeColor.txtDarkBlue": "Темно-синий", "Common.Utils.ThemeColor.txtDarker": "Более темный", "Common.Utils.ThemeColor.txtDarkGray": "Темно-серый", "Common.Utils.ThemeColor.txtDarkGreen": "Тёмно-зеленый", "Common.Utils.ThemeColor.txtDarkPurple": "Темно-лиловый", "Common.Utils.ThemeColor.txtDarkRed": "Темно-красный", "Common.Utils.ThemeColor.txtDarkTeal": "Темно-сизый", "Common.Utils.ThemeColor.txtDarkYellow": "Темно-желтый", "Common.Utils.ThemeColor.txtGold": "Золотистый", "Common.Utils.ThemeColor.txtGray": "Серый", "Common.Utils.ThemeColor.txtGreen": "Зеленый", "Common.Utils.ThemeColor.txtIndigo": "Индиго", "Common.Utils.ThemeColor.txtLavender": "Сиреневый", "Common.Utils.ThemeColor.txtLightBlue": "Светло-синий", "Common.Utils.ThemeColor.txtLighter": "Более светлый", "Common.Utils.ThemeColor.txtLightGray": "Светло-серый", "Common.Utils.ThemeColor.txtLightGreen": "Светло-зеленый", "Common.Utils.ThemeColor.txtLightOrange": "Светло-оранжевый", "Common.Utils.ThemeColor.txtLightYellow": "Светло-желтый", "Common.Utils.ThemeColor.txtOrange": "Оранжевый", "Common.Utils.ThemeColor.txtPink": "Розовый", "Common.Utils.ThemeColor.txtPurple": "Лиловый", "Common.Utils.ThemeColor.txtRed": "Красный", "Common.Utils.ThemeColor.txtRose": "Светло-розовый", "Common.Utils.ThemeColor.txtSkyBlue": "Го<PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Utils.ThemeColor.txtTeal": "Сине-зеленый", "Common.Utils.ThemeColor.txttext": "Текст", "Common.Utils.ThemeColor.txtTurquosie": "Бирюзовый", "Common.Utils.ThemeColor.txtViolet": "Фиолетовый", "Common.Utils.ThemeColor.txtWhite": "Белый", "Common.Utils.ThemeColor.txtYellow": "Желт<PERSON>й", "Common.Views.About.txtAddress": "адрес: ", "Common.Views.About.txtLicensee": "ЛИЦЕНЗИАТ", "Common.Views.About.txtLicensor": "ЛИЦЕНЗИАР", "Common.Views.About.txtMail": "email: ", "Common.Views.About.txtPoweredBy": "Разработано", "Common.Views.About.txtTel": "тел.: ", "Common.Views.About.txtVersion": "Версия ", "Common.Views.AutoCorrectDialog.textAdd": "Добавить", "Common.Views.AutoCorrectDialog.textApplyAsWork": "Выполнять в ходе работы", "Common.Views.AutoCorrectDialog.textAutoCorrect": "Автозамена", "Common.Views.AutoCorrectDialog.textAutoFormat": "Автоформат при вводе", "Common.Views.AutoCorrectDialog.textBy": "На", "Common.Views.AutoCorrectDialog.textDelete": "Удалить", "Common.Views.AutoCorrectDialog.textHyperlink": "Адреса в Интернете и сетевые пути гиперссылками", "Common.Views.AutoCorrectDialog.textMathCorrect": "Автозамена математическими символами", "Common.Views.AutoCorrectDialog.textNewRowCol": "Включать в таблицу новые строки и столбцы", "Common.Views.AutoCorrectDialog.textRecognized": "Распознанные функции", "Common.Views.AutoCorrectDialog.textRecognizedDesc": "Следующие выражения являются распознанными математическими функциями. Они не будут автоматически выделяться курсивом.", "Common.Views.AutoCorrectDialog.textReplace": "Заменить", "Common.Views.AutoCorrectDialog.textReplaceText": "Заменять при вводе", "Common.Views.AutoCorrectDialog.textReplaceType": "Заменять текст при вводе", "Common.Views.AutoCorrectDialog.textReset": "Сброс", "Common.Views.AutoCorrectDialog.textResetAll": "Сбросить настройки", "Common.Views.AutoCorrectDialog.textRestore": "Восстановить", "Common.Views.AutoCorrectDialog.textTitle": "Автозамена", "Common.Views.AutoCorrectDialog.textWarnAddRec": "Распознанные функции должны содержать только прописные или строчные буквы от А до Я.", "Common.Views.AutoCorrectDialog.textWarnResetRec": "Все добавленные вами выражения будут удалены, а удаленные восстановлены. Вы хотите продолжить?", "Common.Views.AutoCorrectDialog.warnReplace": "Элемент автозамены для %1 уже существует. Вы хотите заменить его?", "Common.Views.AutoCorrectDialog.warnReset": "Все добавленные вами автозамены будут удалены, а для измененных будут восстановлены исходные значения. Вы хотите продолжить?", "Common.Views.AutoCorrectDialog.warnRestore": "Элемент автозамены для %1 будет сброшен на исходное значение. Вы хотите продолжить?", "Common.Views.Chat.textChat": "Чат", "Common.Views.Chat.textClosePanel": "Закрыть чат", "Common.Views.Chat.textEnterMessage": "Введите здесь своё сообщение", "Common.Views.Chat.textSend": "Отправить", "Common.Views.Comments.mniAuthorAsc": "По автору от А до Я", "Common.Views.Comments.mniAuthorDesc": "По автору от Я до А", "Common.Views.Comments.mniDateAsc": "От старых к новым", "Common.Views.Comments.mniDateDesc": "От новых к старым", "Common.Views.Comments.mniFilterGroups": "Фильтровать по группе", "Common.Views.Comments.mniPositionAsc": "Сверху вниз", "Common.Views.Comments.mniPositionDesc": "Снизу вверх", "Common.Views.Comments.textAdd": "Добавить", "Common.Views.Comments.textAddComment": "Добавить", "Common.Views.Comments.textAddCommentToDoc": "Добавить комментарий к документу", "Common.Views.Comments.textAddReply": "Добавить ответ", "Common.Views.Comments.textAll": "Все", "Common.Views.Comments.textAnonym": "Гость", "Common.Views.Comments.textCancel": "Отмена", "Common.Views.Comments.textClose": "Закрыть", "Common.Views.Comments.textClosePanel": "Закрыть комментарии", "Common.Views.Comments.textComment": "Комментарий", "Common.Views.Comments.textComments": "Комментарии", "Common.Views.Comments.textEdit": "OK", "Common.Views.Comments.textEnterCommentHint": "Введите здесь свой комментарий", "Common.Views.Comments.textHintAddComment": "Добавить комментарий", "Common.Views.Comments.textOpenAgain": "Открыть снова", "Common.Views.Comments.textReply": "Ответить", "Common.Views.Comments.textResolve": "Решить", "Common.Views.Comments.textResolved": "Решено", "Common.Views.Comments.textSort": "Сортировка комментариев", "Common.Views.Comments.textSortFilter": "Сортировка и фильтрация комментариев", "Common.Views.Comments.textSortFilterMore": "Сортировка, фильтрация и прочее", "Common.Views.Comments.textSortMore": "Сортировка и прочее", "Common.Views.Comments.textViewResolved": "У вас нет прав для повторного открытия комментария", "Common.Views.Comments.txtEmpty": "На листе нет комментариев.", "Common.Views.CopyWarningDialog.textDontShow": "Больше не показывать это сообщение", "Common.Views.CopyWarningDialog.textMsg": "Операции копирования, вырезания и вставки можно выполнить с помощью кнопок на панели инструментов и команд контекстного меню только в этой вкладке редактора.<br><br>Для копирования в другие приложения и вставки из них используйте следующие сочетания клавиш:", "Common.Views.CopyWarningDialog.textTitle": "Операции копирования, вырезания и вставки", "Common.Views.CopyWarningDialog.textToCopy": "для копирования", "Common.Views.CopyWarningDialog.textToCut": "для вырезания", "Common.Views.CopyWarningDialog.textToPaste": "для вставки", "Common.Views.CustomizeQuickAccessDialog.textDownload": "Скачать", "Common.Views.CustomizeQuickAccessDialog.textMsg": "Отметьте команды, которые будут отображаться на панели быстрого доступа.", "Common.Views.CustomizeQuickAccessDialog.textPrint": "Напечатать", "Common.Views.CustomizeQuickAccessDialog.textQuickPrint": "Быстрая печать", "Common.Views.CustomizeQuickAccessDialog.textRedo": "Повторить", "Common.Views.CustomizeQuickAccessDialog.textSave": "Сохранить", "Common.Views.CustomizeQuickAccessDialog.textTitle": "Настроить быстрый доступ", "Common.Views.CustomizeQuickAccessDialog.textUndo": "Отменить", "Common.Views.DocumentAccessDialog.textLoading": "Загрузка...", "Common.Views.DocumentAccessDialog.textTitle": "Настройки совместного доступа", "Common.Views.DocumentPropertyDialog.errorDate": "Вы можете выбрать значение из календаря, чтобы сохранить значение как дату.<br>Если вы введете значение вручную, оно будет сохранено как текст.", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanFalse": "Нет", "Common.Views.DocumentPropertyDialog.txtPropertyBooleanTrue": "Да", "Common.Views.DocumentPropertyDialog.txtPropertyTitleBlankError": "У свойства должно быть название", "Common.Views.DocumentPropertyDialog.txtPropertyTitleLabel": "Название", "Common.Views.DocumentPropertyDialog.txtPropertyTypeBoolean": "\"Да\" или \"Нет\"", "Common.Views.DocumentPropertyDialog.txtPropertyTypeDate": "Дата", "Common.Views.DocumentPropertyDialog.txtPropertyTypeLabel": "Тип", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumber": "Число", "Common.Views.DocumentPropertyDialog.txtPropertyTypeNumberInvalid": "Укажите верное число", "Common.Views.DocumentPropertyDialog.txtPropertyTypeText": "Текст", "Common.Views.DocumentPropertyDialog.txtPropertyValueBlankError": "У свойства должно быть значение", "Common.Views.DocumentPropertyDialog.txtPropertyValueLabel": "Значение", "Common.Views.DocumentPropertyDialog.txtTitle": "Новое свойство документа", "Common.Views.Draw.hintEraser": "Л<PERSON>с<PERSON><PERSON><PERSON>", "Common.Views.Draw.hintSelect": "Выделить", "Common.Views.Draw.txtEraser": "Л<PERSON>с<PERSON><PERSON><PERSON>", "Common.Views.Draw.txtHighlighter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.Draw.txtMM": "мм", "Common.Views.Draw.txtPen": "Ручка", "Common.Views.Draw.txtSelect": "Выделить", "Common.Views.Draw.txtSize": "Толщина", "Common.Views.EditNameDialog.textLabel": "Подпись:", "Common.Views.EditNameDialog.textLabelError": "Подпись не должна быть пустой.", "Common.Views.Header.ariaQuickAccessToolbar": "Панель быстрого доступа", "Common.Views.Header.labelCoUsersDescr": "Пользователи, редактирующие документ:", "Common.Views.Header.textAddFavorite": "Добавить в избранное", "Common.Views.Header.textAdvSettings": "Дополнительные параметры", "Common.Views.Header.textBack": "Открыть расположение файла", "Common.Views.Header.textClose": "Закрыть файл", "Common.Views.Header.textCompactView": "Скрыть панель инструментов", "Common.Views.Header.textHideLines": "Скрыть линейки", "Common.Views.Header.textHideStatusBar": "Объединить строки листов и состояния", "Common.Views.Header.textPrint": "Напечатать", "Common.Views.Header.textReadOnly": "Только чтение", "Common.Views.Header.textRemoveFavorite": "Удалить из избранного", "Common.Views.Header.textSaveBegin": "Сохранение...", "Common.Views.Header.textSaveChanged": "Изменен", "Common.Views.Header.textSaveEnd": "Все изменения сохранены", "Common.Views.Header.textSaveExpander": "Все изменения сохранены", "Common.Views.Header.textShare": "Доступ", "Common.Views.Header.textZoom": "Масш<PERSON><PERSON><PERSON>", "Common.Views.Header.tipAccessRights": "Управление правами доступа к документу", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Настроить панель быстрого доступа", "Common.Views.Header.tipDownload": "Скачать файл", "Common.Views.Header.tipGoEdit": "Редактировать текущий файл", "Common.Views.Header.tipPrint": "Напечатать файл", "Common.Views.Header.tipPrintQuick": "Быстрая печать", "Common.Views.Header.tipRedo": "Повторить", "Common.Views.Header.tipSave": "Сохранить", "Common.Views.Header.tipSearch": "Поиск", "Common.Views.Header.tipUndo": "Отменить", "Common.Views.Header.tipUndock": "Открепить в отдельном окне", "Common.Views.Header.tipUsers": "Просмотр пользователей", "Common.Views.Header.tipViewSettings": "Параметры вида", "Common.Views.Header.tipViewUsers": "Просмотр пользователей и управление правами доступа к документу", "Common.Views.Header.txtAccessRights": "Изменить права доступа", "Common.Views.Header.txtRename": "Переименовать", "Common.Views.History.textCloseHistory": "Закрыть историю", "Common.Views.History.textHideAll": "Скрыть подробные изменения", "Common.Views.History.textHighlightDeleted": "Подсвечивать удаленное", "Common.Views.History.textMore": "<PERSON><PERSON><PERSON>", "Common.Views.History.textRestore": "Восстановить", "Common.Views.History.textShowAll": "Показать подробные изменения", "Common.Views.History.textVer": "вер.", "Common.Views.History.textVersionHistory": "История версий", "Common.Views.ImageFromUrlDialog.textUrl": "Вставьте URL изображения:", "Common.Views.ImageFromUrlDialog.txtEmpty": "Это поле необходимо заполнить", "Common.Views.ImageFromUrlDialog.txtNotUrl": "Это поле должно быть URL-адресом в формате \"http://www.example.com\"", "Common.Views.ListSettingsDialog.textBulleted": "Маркированный", "Common.Views.ListSettingsDialog.textFromFile": "Из файла", "Common.Views.ListSettingsDialog.textFromStorage": "Из хранилища", "Common.Views.ListSettingsDialog.textFromUrl": "По URL", "Common.Views.ListSettingsDialog.textNumbering": "Нумерованный", "Common.Views.ListSettingsDialog.textSelect": "Выбрать из", "Common.Views.ListSettingsDialog.tipChange": "Изменить маркер", "Common.Views.ListSettingsDialog.txtBullet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.ListSettingsDialog.txtColor": "Цвет", "Common.Views.ListSettingsDialog.txtImage": "Изображение", "Common.Views.ListSettingsDialog.txtImport": "Импорт", "Common.Views.ListSettingsDialog.txtNewBullet": "Новый маркер", "Common.Views.ListSettingsDialog.txtNewImage": "Новое изображение", "Common.Views.ListSettingsDialog.txtNone": "Нет", "Common.Views.ListSettingsDialog.txtOfText": "% текста", "Common.Views.ListSettingsDialog.txtSize": "Размер", "Common.Views.ListSettingsDialog.txtStart": "Начать с", "Common.Views.ListSettingsDialog.txtSymbol": "Символ", "Common.Views.ListSettingsDialog.txtTitle": "Параметры списка", "Common.Views.ListSettingsDialog.txtType": "Тип", "Common.Views.MacrosDialog.textCopy": "Копировать", "Common.Views.MacrosDialog.textCustomFunction": "Пользовательская функция", "Common.Views.MacrosDialog.textDelete": "Удалить", "Common.Views.MacrosDialog.textLoading": "Загрузка...", "Common.Views.MacrosDialog.textMacros": "Макросы", "Common.Views.MacrosDialog.textMakeAutostart": "Сделать автозапуск", "Common.Views.MacrosDialog.textRename": "Переименовать", "Common.Views.MacrosDialog.textRun": "Запустить", "Common.Views.MacrosDialog.textSave": "Сохранить", "Common.Views.MacrosDialog.textTitle": "Макросы", "Common.Views.MacrosDialog.textUnMakeAutostart": "Отменить автозапуск", "Common.Views.MacrosDialog.tipFunctionAdd": "Добавить пользовательскую функцию", "Common.Views.MacrosDialog.tipMacrosAdd": "Добавить макросы", "Common.Views.MacrosDialog.tipMacrosRun": "Запустить", "Common.Views.OpenDialog.closeButtonText": "Закрыть файл", "Common.Views.OpenDialog.textInvalidRange": "Недопустимый диапазон ячеек", "Common.Views.OpenDialog.textSelectData": "Выбор данных", "Common.Views.OpenDialog.txtAdvanced": "Дополнительно", "Common.Views.OpenDialog.txtColon": "Двоеточие", "Common.Views.OpenDialog.txtComma": "Запятая", "Common.Views.OpenDialog.txtDelimiter": "Разделитель", "Common.Views.OpenDialog.txtDestData": "Выберите, где поместить данные", "Common.Views.OpenDialog.txtEmpty": "Это поле необходимо заполнить", "Common.Views.OpenDialog.txtEncoding": "Кодировка ", "Common.Views.OpenDialog.txtIncorrectPwd": "Указан неверный пароль.", "Common.Views.OpenDialog.txtOpenFile": "Введите пароль для открытия файла", "Common.Views.OpenDialog.txtOther": "Другое", "Common.Views.OpenDialog.txtPassword": "Пароль", "Common.Views.OpenDialog.txtPreview": "Просмотр", "Common.Views.OpenDialog.txtProtected": "Как только вы введете пароль и откроете файл, текущий пароль к файлу будет сброшен.", "Common.Views.OpenDialog.txtSemicolon": "Точка с запятой", "Common.Views.OpenDialog.txtSpace": "Пробел", "Common.Views.OpenDialog.txtTab": "Табуляция", "Common.Views.OpenDialog.txtTitle": "Выбрать параметры %1", "Common.Views.OpenDialog.txtTitleProtected": "Защищенный файл", "Common.Views.PasswordDialog.txtDescription": "Задайте пароль, чтобы защитить этот документ", "Common.Views.PasswordDialog.txtIncorrectPwd": "Пароль и его подтверждение не совпадают", "Common.Views.PasswordDialog.txtPassword": "Пароль", "Common.Views.PasswordDialog.txtRepeat": "Повторить пароль", "Common.Views.PasswordDialog.txtTitle": "Установка пароля", "Common.Views.PasswordDialog.txtWarning": "Внимание: Если пароль забыт или утерян, его нельзя восстановить. Храните его в надежном месте.", "Common.Views.PluginDlg.textDock": "Закрепить плагин", "Common.Views.PluginDlg.textLoading": "Загрузка", "Common.Views.PluginPanel.textClosePanel": "Закрыть плагин", "Common.Views.PluginPanel.textHidePanel": "Свернуть плагин", "Common.Views.PluginPanel.textLoading": "Загрузка", "Common.Views.PluginPanel.textUndock": "Открепить плагин", "Common.Views.Plugins.groupCaption": "Плагины", "Common.Views.Plugins.strPlugins": "Плагины", "Common.Views.Plugins.textBackgroundPlugins": "Фоновые плагины", "Common.Views.Plugins.textSettings": "Настройки", "Common.Views.Plugins.textStart": "Запустить", "Common.Views.Plugins.textStop": "Остановить", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "Список фоновых плагинов", "Common.Views.Plugins.tipMore": "<PERSON><PERSON><PERSON>", "Common.Views.Protection.hintAddPwd": "Зашифровать с помощью пароля", "Common.Views.Protection.hintDelPwd": "Удалить пароль", "Common.Views.Protection.hintPwd": "Изменить или удалить пароль", "Common.Views.Protection.hintSignature": "Добавить цифровую подпись или строку подписи", "Common.Views.Protection.txtAddPwd": "Добавить пароль", "Common.Views.Protection.txtChangePwd": "Изменить пароль", "Common.Views.Protection.txtDeletePwd": "Удалить пароль", "Common.Views.Protection.txtEncrypt": "Шифровать", "Common.Views.Protection.txtInvisibleSignature": "Добавить цифровую подпись", "Common.Views.Protection.txtSignature": "Подпись", "Common.Views.Protection.txtSignatureLine": "Добавить строку подписи", "Common.Views.RecentFiles.txtOpenRecent": "Открыть последние", "Common.Views.RenameDialog.textName": "Имя файла", "Common.Views.RenameDialog.txtInvalidName": "Имя файла не должно содержать следующих символов: ", "Common.Views.ReviewChanges.hintNext": "К следующему изменению", "Common.Views.ReviewChanges.hintPrev": "К предыдущему изменению", "Common.Views.ReviewChanges.strFast": "Быстрый", "Common.Views.ReviewChanges.strFastDesc": "Совместное редактирование в режиме реального времени. Все изменения сохраняются автоматически.", "Common.Views.ReviewChanges.strStrict": "Строгий", "Common.Views.ReviewChanges.strStrictDesc": "Используйте кнопку 'Сохранить' для синхронизации изменений, вносимых вами и другими пользователями.", "Common.Views.ReviewChanges.tipAcceptCurrent": "Принять текущее изменение", "Common.Views.ReviewChanges.tipCoAuthMode": "Задать режим совместного редактирования", "Common.Views.ReviewChanges.tipCommentRem": "Удалить комментарии", "Common.Views.ReviewChanges.tipCommentRemCurrent": "Удалить текущие комментарии", "Common.Views.ReviewChanges.tipCommentResolve": "Решить комментарии", "Common.Views.ReviewChanges.tipCommentResolveCurrent": "Решить текущие комментарии", "Common.Views.ReviewChanges.tipHistory": "Показать историю версий", "Common.Views.ReviewChanges.tipRejectCurrent": "Отклонить текущее изменение", "Common.Views.ReviewChanges.tipReview": "Отслеживать изменения", "Common.Views.ReviewChanges.tipReviewView": "Выберите режим, в котором вы хотите отображать изменения", "Common.Views.ReviewChanges.tipSetDocLang": "Задать язык документа", "Common.Views.ReviewChanges.tipSetSpelling": "Проверка орфографии", "Common.Views.ReviewChanges.tipSharing": "Управление правами доступа к документу", "Common.Views.ReviewChanges.txtAccept": "Принять", "Common.Views.ReviewChanges.txtAcceptAll": "Принять все изменения", "Common.Views.ReviewChanges.txtAcceptChanges": "Принять изменения", "Common.Views.ReviewChanges.txtAcceptCurrent": "Принять текущее изменение", "Common.Views.ReviewChanges.txtChat": "Чат", "Common.Views.ReviewChanges.txtClose": "Закрыть", "Common.Views.ReviewChanges.txtCoAuthMode": "Режим совместного редактирования", "Common.Views.ReviewChanges.txtCommentRemAll": "Удалить все комментарии", "Common.Views.ReviewChanges.txtCommentRemCurrent": "Удалить текущие комментарии", "Common.Views.ReviewChanges.txtCommentRemMy": "Удалить мои комментарии", "Common.Views.ReviewChanges.txtCommentRemMyCurrent": "Удалить мои текущие комментарии", "Common.Views.ReviewChanges.txtCommentRemove": "Удалить", "Common.Views.ReviewChanges.txtCommentResolve": "Решить", "Common.Views.ReviewChanges.txtCommentResolveAll": "Решить все комментарии", "Common.Views.ReviewChanges.txtCommentResolveCurrent": "Решить текущие комментарии", "Common.Views.ReviewChanges.txtCommentResolveMy": "Решить мои комментарии", "Common.Views.ReviewChanges.txtCommentResolveMyCurrent": "Решить мои текущие комментарии", "Common.Views.ReviewChanges.txtDocLang": "Язык", "Common.Views.ReviewChanges.txtFinal": "Все изменения приняты (просмотр)", "Common.Views.ReviewChanges.txtFinalCap": "Измененный документ", "Common.Views.ReviewChanges.txtHistory": "История версий", "Common.Views.ReviewChanges.txtMarkup": "Все изменения (редактирование)", "Common.Views.ReviewChanges.txtMarkupCap": "Изменения", "Common.Views.ReviewChanges.txtNext": "К следующему", "Common.Views.ReviewChanges.txtOriginal": "Все изменения отклонены (просмотр)", "Common.Views.ReviewChanges.txtOriginalCap": "Исходный документ", "Common.Views.ReviewChanges.txtPrev": "К предыдущему", "Common.Views.ReviewChanges.txtReject": "Отклонить", "Common.Views.ReviewChanges.txtRejectAll": "Отклонить все изменения", "Common.Views.ReviewChanges.txtRejectChanges": "Отклонить изменения", "Common.Views.ReviewChanges.txtRejectCurrent": "Отклонить текущее изменение", "Common.Views.ReviewChanges.txtSharing": "Совместный доступ", "Common.Views.ReviewChanges.txtSpelling": "Проверка орфографии", "Common.Views.ReviewChanges.txtTurnon": "Отслеживание изменений", "Common.Views.ReviewChanges.txtView": "Отображение", "Common.Views.ReviewPopover.textAdd": "Добавить", "Common.Views.ReviewPopover.textAddReply": "Добавить ответ", "Common.Views.ReviewPopover.textCancel": "Отмена", "Common.Views.ReviewPopover.textClose": "Закрыть", "Common.Views.ReviewPopover.textComment": "Комментарий", "Common.Views.ReviewPopover.textEdit": "OK", "Common.Views.ReviewPopover.textEnterComment": "Введите здесь свой комментарий", "Common.Views.ReviewPopover.textMention": "+упоминание предоставит доступ к документу и отправит оповещение по почте", "Common.Views.ReviewPopover.textMentionNotify": "+упоминание отправит пользователю оповещение по почте", "Common.Views.ReviewPopover.textOpenAgain": "Открыть снова", "Common.Views.ReviewPopover.textReply": "Ответить", "Common.Views.ReviewPopover.textResolve": "Решить", "Common.Views.ReviewPopover.textViewResolved": "У вас нет прав для повторного открытия комментария", "Common.Views.ReviewPopover.txtDeleteTip": "Удалить", "Common.Views.ReviewPopover.txtEditTip": "Редактировать", "Common.Views.SaveAsDlg.textLoading": "Загрузка", "Common.Views.SaveAsDlg.textTitle": "Папка для сохранения", "Common.Views.SearchPanel.textByColumns": "По столбцам", "Common.Views.SearchPanel.textByRows": "По строкам", "Common.Views.SearchPanel.textCaseSensitive": "С учетом регистра", "Common.Views.SearchPanel.textCell": "Ячейка", "Common.Views.SearchPanel.textCloseSearch": "Закрыть поиск", "Common.Views.SearchPanel.textContentChanged": "Документ изменен.", "Common.Views.SearchPanel.textFind": "Поиск", "Common.Views.SearchPanel.textFindAndReplace": "Поиск и замена", "Common.Views.SearchPanel.textFormula": "Формула", "Common.Views.SearchPanel.textFormulas": "Формулы", "Common.Views.SearchPanel.textItemEntireCell": "Все содержимое ячеек", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} элементов успешно заменено.", "Common.Views.SearchPanel.textLookIn": "Область поиска", "Common.Views.SearchPanel.textMatchUsingRegExp": "Сопоставление с использованием регулярных выражений", "Common.Views.SearchPanel.textName": "Имя", "Common.Views.SearchPanel.textNoMatches": "Совпадений нет", "Common.Views.SearchPanel.textNoSearchResults": "Ничего не найдено", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} элементов заменено. Оставшиеся {2} элементов заблокированы другими пользователями.", "Common.Views.SearchPanel.textReplace": "Заменить", "Common.Views.SearchPanel.textReplaceAll": "Заменить все", "Common.Views.SearchPanel.textReplaceWith": "Заменить на", "Common.Views.SearchPanel.textSearch": "Поиск", "Common.Views.SearchPanel.textSearchAgain": "{0}Выполнить новый поиск{1} для получения точных результатов.", "Common.Views.SearchPanel.textSearchHasStopped": "Поиск остановлен", "Common.Views.SearchPanel.textSearchOptions": "Параметры поиска", "Common.Views.SearchPanel.textSearchResults": "Результаты поиска: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Результаты поиска", "Common.Views.SearchPanel.textSelectDataRange": "Выбор диапазона данных", "Common.Views.SearchPanel.textSheet": "На листе", "Common.Views.SearchPanel.textSpecificRange": "В указанном диапазоне", "Common.Views.SearchPanel.textTooManyResults": "Слишком много результатов, чтобы отобразить их здесь", "Common.Views.SearchPanel.textValue": "Значение", "Common.Views.SearchPanel.textValues": "Значения", "Common.Views.SearchPanel.textWholeWords": "Только слово целиком", "Common.Views.SearchPanel.textWithin": "Искать", "Common.Views.SearchPanel.textWorkbook": "В книге", "Common.Views.SearchPanel.tipNextResult": "Следующий результат", "Common.Views.SearchPanel.tipPreviousResult": "Предыдущий результат", "Common.Views.SelectFileDlg.textLoading": "Загрузка", "Common.Views.SelectFileDlg.textTitle": "Выбрать источник данных", "Common.Views.ShapeShadowDialog.txtAngle": "Угол", "Common.Views.ShapeShadowDialog.txtDistance": "Расстояние", "Common.Views.ShapeShadowDialog.txtSize": "Размер", "Common.Views.ShapeShadowDialog.txtTitle": "Настроить тень", "Common.Views.ShapeShadowDialog.txtTransparency": "Прозрачность", "Common.Views.SignDialog.textBold": "Полужирный", "Common.Views.SignDialog.textCertificate": "Сертификат", "Common.Views.SignDialog.textChange": "Изменить", "Common.Views.SignDialog.textInputName": "Введите имя подписывающего", "Common.Views.SignDialog.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Common.Views.SignDialog.textNameError": "Имя подписывающего не должно быть пустым.", "Common.Views.SignDialog.textPurpose": "Цель подписания документа", "Common.Views.SignDialog.textSelect": "Выбрать", "Common.Views.SignDialog.textSelectImage": "Выбрать изображение", "Common.Views.SignDialog.textSignature": "Как выглядит подпись:", "Common.Views.SignDialog.textTitle": "Подписание документа", "Common.Views.SignDialog.textUseImage": "или нажмите 'Выбрать изображение', чтобы использовать изображение в качестве подписи", "Common.Views.SignDialog.textValid": "Действителен с %1 по %2", "Common.Views.SignDialog.tipFontName": "<PERSON>ри<PERSON><PERSON>", "Common.Views.SignDialog.tipFontSize": "Размер шрифта", "Common.Views.SignSettingsDialog.textAllowComment": "Разрешить подписывающему добавлять примечания в окне подписи", "Common.Views.SignSettingsDialog.textDefInstruction": "Перед подписанием документа убедитесь, что подписываемое содержимое является правильным.", "Common.Views.SignSettingsDialog.textInfoEmail": "Адрес электронной почты предложенного подписывающего", "Common.Views.SignSettingsDialog.textInfoName": "Предложенный подписывающий", "Common.Views.SignSettingsDialog.textInfoTitle": "Должность предложенного подписывающего", "Common.Views.SignSettingsDialog.textInstructions": "Инструкции для подписывающего", "Common.Views.SignSettingsDialog.textShowDate": "Показывать дату подписи в строке подписи", "Common.Views.SignSettingsDialog.textTitle": "Настройка подписи", "Common.Views.SignSettingsDialog.txtEmpty": "Это поле необходимо заполнить", "Common.Views.SymbolTableDialog.textCharacter": "Символ", "Common.Views.SymbolTableDialog.textCode": "Код знака из Юникод (шестн.)", "Common.Views.SymbolTableDialog.textCopyright": "Знак авторского права", "Common.Views.SymbolTableDialog.textDCQuote": "Закрывающая двойная кавычка", "Common.Views.SymbolTableDialog.textDOQuote": "Открывающая двойная кавычка", "Common.Views.SymbolTableDialog.textEllipsis": "Горизонтальное многоточие", "Common.Views.SymbolTableDialog.textEmDash": "Длинное тире", "Common.Views.SymbolTableDialog.textEmSpace": "Длинный пробел", "Common.Views.SymbolTableDialog.textEnDash": "Короткое тире", "Common.Views.SymbolTableDialog.textEnSpace": "Короткий пробел", "Common.Views.SymbolTableDialog.textFont": "<PERSON>ри<PERSON><PERSON>", "Common.Views.SymbolTableDialog.textNBHyphen": "Неразрывный дефис", "Common.Views.SymbolTableDialog.textNBSpace": "Неразрывный пробел", "Common.Views.SymbolTableDialog.textPilcrow": "Знак абзаца", "Common.Views.SymbolTableDialog.textQEmSpace": "1/4 пробела", "Common.Views.SymbolTableDialog.textRange": "Набор", "Common.Views.SymbolTableDialog.textRecent": "Ранее использовавшиеся символы", "Common.Views.SymbolTableDialog.textRegistered": "Зарегистрированный товарный знак", "Common.Views.SymbolTableDialog.textSCQuote": "Закрывающая одинарная кавычка", "Common.Views.SymbolTableDialog.textSection": "Знак раздела", "Common.Views.SymbolTableDialog.textShortcut": "Сочетание клавиш", "Common.Views.SymbolTableDialog.textSHyphen": "Мягкий дефис", "Common.Views.SymbolTableDialog.textSOQuote": "Открывающая одинарная кавычка", "Common.Views.SymbolTableDialog.textSpecial": "Специальные символы", "Common.Views.SymbolTableDialog.textSymbols": "Символы", "Common.Views.SymbolTableDialog.textTitle": "Символ", "Common.Views.SymbolTableDialog.textTradeMark": "Символ товарного знака", "Common.Views.UserNameDialog.textDontShow": "Больше не спрашивать", "Common.Views.UserNameDialog.textLabel": "Подпись:", "Common.Views.UserNameDialog.textLabelError": "Подпись не должна быть пустой.", "SSE.Controllers.DataTab.strSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.DataTab.textAddExternalData": "Добавлена ссылка на внешний источник. Такие ссылки можно обновить на вкладке Данные.", "SSE.Controllers.DataTab.textColumns": "Столбцы", "SSE.Controllers.DataTab.textContinue": "Продолжить", "SSE.Controllers.DataTab.textDontUpdate": "Не обновлять", "SSE.Controllers.DataTab.textEmptyUrl": "Необходимо указать URL.", "SSE.Controllers.DataTab.textRows": "Строки", "SSE.Controllers.DataTab.textTurnOff": "Отключить автообновление", "SSE.Controllers.DataTab.textUpdate": "Обновить", "SSE.Controllers.DataTab.textWizard": "Текст по столбцам", "SSE.Controllers.DataTab.txtDataValidation": "Проверка данных", "SSE.Controllers.DataTab.txtErrorExternalLink": "Ошибка: не удалось обновить", "SSE.Controllers.DataTab.txtExpand": "Развернуть", "SSE.Controllers.DataTab.txtExpandRemDuplicates": "Данные рядом с выделенным диапазоном не будут удалены. Вы хотите расширить выделенный диапазон, чтобы включить данные из смежных ячеек, или продолжить только с выделенным диапазоном?", "SSE.Controllers.DataTab.txtExtendDataValidation": "Выделенная область содержит ячейки без условий на значения.<br>Вы хотите распространить условия на эти ячейки?", "SSE.Controllers.DataTab.txtImportWizard": "Мастер импорта текста", "SSE.Controllers.DataTab.txtRemDuplicates": "Удалить дубликаты", "SSE.Controllers.DataTab.txtRemoveDataValidation": "Выделенная область содержит более одного условия.<br>Удалить текущие параметры и продолжить?", "SSE.Controllers.DataTab.txtRemSelected": "Удалить в выделенном диапазоне", "SSE.Controllers.DataTab.txtUrlTitle": "Вставьте URL-адрес данных", "SSE.Controllers.DataTab.warnUpdateExternalAutoupdate": "Эта рабочая книга содержит ссылки на внешние источники, которые обновляются автоматически. Это может быть небезопасно.<br><br>Если вы доверяете им, нажмите Продолжить.", "SSE.Controllers.DataTab.warnUpdateExternalData": "Эта книга содержит связи с внешними источниками данных (возможно, небезопасными).<br>Если вы считаете эти связи надежными, обновите их, чтобы получить последние данные.", "SSE.Controllers.DocumentHolder.alignmentText": "Выравнивание", "SSE.Controllers.DocumentHolder.centerText": "По центру", "SSE.Controllers.DocumentHolder.deleteColumnText": "Удалить столбец", "SSE.Controllers.DocumentHolder.deleteRowText": "Удалить строку", "SSE.Controllers.DocumentHolder.deleteText": "Удалить", "SSE.Controllers.DocumentHolder.errorInvalidLink": "Ссылка указывает на несуществующую ячейку. Исправьте или удалите ссылку.", "SSE.Controllers.DocumentHolder.guestText": "Гость", "SSE.Controllers.DocumentHolder.insertColumnLeftText": "Столбец слева", "SSE.Controllers.DocumentHolder.insertColumnRightText": "Столбец справа", "SSE.Controllers.DocumentHolder.insertRowAboveText": "Строку выше", "SSE.Controllers.DocumentHolder.insertRowBelowText": "Строку ниже", "SSE.Controllers.DocumentHolder.insertText": "Добавить", "SSE.Controllers.DocumentHolder.leftText": "По левому краю", "SSE.Controllers.DocumentHolder.notcriticalErrorTitle": "Внимание", "SSE.Controllers.DocumentHolder.rightText": "По правому краю", "SSE.Controllers.DocumentHolder.textAutoCorrectSettings": "Параметры автозамены", "SSE.Controllers.DocumentHolder.textChangeColumnWidth": "<PERSON>и<PERSON><PERSON><PERSON> столбца {0} символов ({1} пикселей)", "SSE.Controllers.DocumentHolder.textChangeRowHeight": "Высота строки {0} пунктов ({1} пикселей)", "SSE.Controllers.DocumentHolder.textCtrlClick": "Щелкните по ссылке, чтобы открыть ее, или щелкните и удерживайте кнопку мыши, чтобы выделить ячейку.", "SSE.Controllers.DocumentHolder.textInsertLeft": "Добавить столбец слева", "SSE.Controllers.DocumentHolder.textInsertTop": "Добавить строку сверху", "SSE.Controllers.DocumentHolder.textPasteSpecial": "Специальная вставка", "SSE.Controllers.DocumentHolder.textStopExpand": "Не развертывать таблицы автоматически", "SSE.Controllers.DocumentHolder.textSym": "симв", "SSE.Controllers.DocumentHolder.tipIsLocked": "Этот элемент редактируется другим пользователем.", "SSE.Controllers.DocumentHolder.txtAboveAve": "Выше среднего", "SSE.Controllers.DocumentHolder.txtAddBottom": "Добавить нижнюю границу", "SSE.Controllers.DocumentHolder.txtAddFractionBar": "Добавить дробную черту", "SSE.Controllers.DocumentHolder.txtAddHor": "Добавить горизонтальную линию", "SSE.Controllers.DocumentHolder.txtAddLB": "Добавить линию из левого нижнего угла", "SSE.Controllers.DocumentHolder.txtAddLeft": "Добавить левую границу", "SSE.Controllers.DocumentHolder.txtAddLT": "Добавить линию из левого верхнего угла", "SSE.Controllers.DocumentHolder.txtAddRight": "Добавить правую границу", "SSE.Controllers.DocumentHolder.txtAddTop": "Добавить верхнюю границу", "SSE.Controllers.DocumentHolder.txtAddVer": "Добавить вертикальную линию", "SSE.Controllers.DocumentHolder.txtAlignToChar": "Выравнивание по символу", "SSE.Controllers.DocumentHolder.txtAll": "(Все)", "SSE.Controllers.DocumentHolder.txtAllTableHint": "Возвращает все содержимое таблицы или указанные столбцы таблицы, включая заголовки столбцов, данные и строки итогов", "SSE.Controllers.DocumentHolder.txtAnd": "и", "SSE.Controllers.DocumentHolder.txtBegins": "Начинается с", "SSE.Controllers.DocumentHolder.txtBelowAve": "Ниже среднего", "SSE.Controllers.DocumentHolder.txtBlanks": "(Пустые)", "SSE.Controllers.DocumentHolder.txtBorderProps": "Свойства границ", "SSE.Controllers.DocumentHolder.txtBottom": "По нижнему краю", "SSE.Controllers.DocumentHolder.txtByField": "%1 из %2", "SSE.Controllers.DocumentHolder.txtColumn": "Столбец", "SSE.Controllers.DocumentHolder.txtColumnAlign": "Выравнивание столбца", "SSE.Controllers.DocumentHolder.txtContains": "Соде<PERSON><PERSON><PERSON>т", "SSE.Controllers.DocumentHolder.txtCopySuccess": "Ссылка скопирована в буфер обмена", "SSE.Controllers.DocumentHolder.txtDataTableHint": "Возвращает ячейки данных из таблицы или указанных столбцов таблицы", "SSE.Controllers.DocumentHolder.txtDecreaseArg": "Уменьшить размер аргумента", "SSE.Controllers.DocumentHolder.txtDeleteArg": "Удалить аргумент", "SSE.Controllers.DocumentHolder.txtDeleteBreak": "Удалить принудительный разрыв", "SSE.Controllers.DocumentHolder.txtDeleteChars": "Удалить вложенные знаки", "SSE.Controllers.DocumentHolder.txtDeleteCharsAndSeparators": "Удалить вложенные знаки и разделители", "SSE.Controllers.DocumentHolder.txtDeleteEq": "Удалить уравнение", "SSE.Controllers.DocumentHolder.txtDeleteGroupChar": "Удалить символ", "SSE.Controllers.DocumentHolder.txtDeleteRadical": "Удалить радикал", "SSE.Controllers.DocumentHolder.txtEnds": "Заканчивается на", "SSE.Controllers.DocumentHolder.txtEquals": "Равно", "SSE.Controllers.DocumentHolder.txtEqualsToCellColor": "Равно цвету ячейки", "SSE.Controllers.DocumentHolder.txtEqualsToFontColor": "Равно цвету шрифта", "SSE.Controllers.DocumentHolder.txtExpand": "Расширить и сортировать", "SSE.Controllers.DocumentHolder.txtExpandSort": "Данные рядом с выделенным диапазоном не будут отсортированы. Вы хотите расширить выделенный диапазон, чтобы включить данные из смежных ячеек, или продолжить сортировку только выделенного диапазона?", "SSE.Controllers.DocumentHolder.txtFilterBottom": "Наименьшие", "SSE.Controllers.DocumentHolder.txtFilterTop": "Наибольшие", "SSE.Controllers.DocumentHolder.txtFractionLinear": "Изменить на горизонтальную простую дробь", "SSE.Controllers.DocumentHolder.txtFractionSkewed": "Изменить на диагональную простую дробь", "SSE.Controllers.DocumentHolder.txtFractionStacked": "Изменить на вертикальную простую дробь", "SSE.Controllers.DocumentHolder.txtGreater": "Больше", "SSE.Controllers.DocumentHolder.txtGreaterEquals": "Больше или равно", "SSE.Controllers.DocumentHolder.txtGroupCharOver": "Символ над текстом", "SSE.Controllers.DocumentHolder.txtGroupCharUnder": "Символ под текстом", "SSE.Controllers.DocumentHolder.txtHeadersTableHint": "Возвращает заголовки столбцов из таблицы или указанных столбцов таблицы", "SSE.Controllers.DocumentHolder.txtHeight": "Высота", "SSE.Controllers.DocumentHolder.txtHideBottom": "Скрыть нижнюю границу", "SSE.Controllers.DocumentHolder.txtHideBottomLimit": "Скрыть нижний предел", "SSE.Controllers.DocumentHolder.txtHideCloseBracket": "Скрыть закрывающую скобку", "SSE.Controllers.DocumentHolder.txtHideDegree": "Скрыть степень", "SSE.Controllers.DocumentHolder.txtHideHor": "Скрыть горизонтальную линию", "SSE.Controllers.DocumentHolder.txtHideLB": "Скрыть линию из левого нижнего угла", "SSE.Controllers.DocumentHolder.txtHideLeft": "Скрыть левую границу", "SSE.Controllers.DocumentHolder.txtHideLT": "Скрыть линию из левого верхнего угла", "SSE.Controllers.DocumentHolder.txtHideOpenBracket": "Скрыть открывающую скобку", "SSE.Controllers.DocumentHolder.txtHidePlaceholder": "Скрыть поля для заполнения", "SSE.Controllers.DocumentHolder.txtHideRight": "Скрыть правую границу", "SSE.Controllers.DocumentHolder.txtHideTop": "Скрыть верхнюю границу", "SSE.Controllers.DocumentHolder.txtHideTopLimit": "Скрыть верхний предел", "SSE.Controllers.DocumentHolder.txtHideVer": "Скрыть вертикальную линию", "SSE.Controllers.DocumentHolder.txtImportWizard": "Мастер импорта текста", "SSE.Controllers.DocumentHolder.txtIncreaseArg": "Увеличить размер аргумента", "SSE.Controllers.DocumentHolder.txtInsertArgAfter": "Вставить аргумент после", "SSE.Controllers.DocumentHolder.txtInsertArgBefore": "Вставить аргумент перед", "SSE.Controllers.DocumentHolder.txtInsertBreak": "Вставить принудительный разрыв", "SSE.Controllers.DocumentHolder.txtInsertEqAfter": "Вставить уравнение после", "SSE.Controllers.DocumentHolder.txtInsertEqBefore": "Вставить уравнение перед", "SSE.Controllers.DocumentHolder.txtItems": "элементов", "SSE.Controllers.DocumentHolder.txtKeepTextOnly": "Сохранить только текст", "SSE.Controllers.DocumentHolder.txtLess": "Меньше", "SSE.Controllers.DocumentHolder.txtLessEquals": "Меньше или равно", "SSE.Controllers.DocumentHolder.txtLimitChange": "Изменить положение пределов", "SSE.Controllers.DocumentHolder.txtLimitOver": "Предел над текстом", "SSE.Controllers.DocumentHolder.txtLimitUnder": "Предел под текстом", "SSE.Controllers.DocumentHolder.txtLockSort": "Обнаружены данные рядом с выделенным диапазоном, но у вас недостаточно прав для изменения этих ячеек.<br>Вы хотите продолжить работу с выделенным диапазоном?", "SSE.Controllers.DocumentHolder.txtMatchBrackets": "Изменить размер скобок в соответствии с высотой аргумента", "SSE.Controllers.DocumentHolder.txtMatrixAlign": "Выравнивание матрицы", "SSE.Controllers.DocumentHolder.txtNoChoices": "Нет вариантов для заполнения ячейки.<br>Для замены можно выбрать только текстовые значения из столбца.", "SSE.Controllers.DocumentHolder.txtNotBegins": "Не начинается с", "SSE.Controllers.DocumentHolder.txtNotContains": "Не содержит", "SSE.Controllers.DocumentHolder.txtNotEnds": "Не заканчивается на", "SSE.Controllers.DocumentHolder.txtNotEquals": "Не равно", "SSE.Controllers.DocumentHolder.txtOr": "или", "SSE.Controllers.DocumentHolder.txtOverbar": "Черта над текстом", "SSE.Controllers.DocumentHolder.txtPaste": "Вставить", "SSE.Controllers.DocumentHolder.txtPasteBorders": "Формула без границ", "SSE.Controllers.DocumentHolder.txtPasteColWidths": "Формула + ширина столбца", "SSE.Controllers.DocumentHolder.txtPasteDestFormat": "Форматирование конечных ячеек", "SSE.Controllers.DocumentHolder.txtPasteFormat": "Вставить только форматирование", "SSE.Controllers.DocumentHolder.txtPasteFormulaNumFormat": "Формула + формат чисел", "SSE.Controllers.DocumentHolder.txtPasteFormulas": "Вставить только формулу", "SSE.Controllers.DocumentHolder.txtPasteKeepSourceFormat": "Формула + все форматирование", "SSE.Controllers.DocumentHolder.txtPasteLink": "Вставить связь", "SSE.Controllers.DocumentHolder.txtPasteLinkPicture": "Связанный рисунок", "SSE.Controllers.DocumentHolder.txtPasteMerge": "Объединить условное форматирование", "SSE.Controllers.DocumentHolder.txtPastePicture": "Рисунок", "SSE.Controllers.DocumentHolder.txtPasteSourceFormat": "Исходное форматирование", "SSE.Controllers.DocumentHolder.txtPasteTranspose": "Транспонировать", "SSE.Controllers.DocumentHolder.txtPasteValFormat": "Значение + все форматирование", "SSE.Controllers.DocumentHolder.txtPasteValNumFormat": "Значение + формат чисел", "SSE.Controllers.DocumentHolder.txtPasteValues": "Вставить только значение", "SSE.Controllers.DocumentHolder.txtPercent": "процентов", "SSE.Controllers.DocumentHolder.txtRedoExpansion": "Повторить авторазвертывание таблицы", "SSE.Controllers.DocumentHolder.txtRemFractionBar": "Удалить дробную черту", "SSE.Controllers.DocumentHolder.txtRemLimit": "Удалить предел", "SSE.Controllers.DocumentHolder.txtRemoveAccentChar": "Удалить диакритический знак", "SSE.Controllers.DocumentHolder.txtRemoveBar": "Удалить черту", "SSE.Controllers.DocumentHolder.txtRemoveWarning": "Вы хотите удалить эту подпись?<br>Это нельзя отменить.", "SSE.Controllers.DocumentHolder.txtRemScripts": "Удалить индексы", "SSE.Controllers.DocumentHolder.txtRemSubscript": "Удалить нижний индекс", "SSE.Controllers.DocumentHolder.txtRemSuperscript": "Удалить верхний индекс", "SSE.Controllers.DocumentHolder.txtRowHeight": "Высота строки", "SSE.Controllers.DocumentHolder.txtScriptsAfter": "Индексы после текста", "SSE.Controllers.DocumentHolder.txtScriptsBefore": "Индексы перед текстом", "SSE.Controllers.DocumentHolder.txtShowBottomLimit": "Показать нижний предел", "SSE.Controllers.DocumentHolder.txtShowCloseBracket": "Показать закрывающую скобку", "SSE.Controllers.DocumentHolder.txtShowDegree": "Показать степень", "SSE.Controllers.DocumentHolder.txtShowOpenBracket": "Показать открывающую скобку", "SSE.Controllers.DocumentHolder.txtShowPlaceholder": "Показать поля для заполнения", "SSE.Controllers.DocumentHolder.txtShowTopLimit": "Показать верхний предел", "SSE.Controllers.DocumentHolder.txtSorting": "Сортировка", "SSE.Controllers.DocumentHolder.txtSortSelected": "Сортировать выделенное", "SSE.Controllers.DocumentHolder.txtStretchBrackets": "Растянуть скобки", "SSE.Controllers.DocumentHolder.txtThisRowHint": "Выбрать только эту строку указанного столбца", "SSE.Controllers.DocumentHolder.txtTop": "По верхнему краю", "SSE.Controllers.DocumentHolder.txtTotalsTableHint": "Возвращает строки итогов из таблицы или указанных столбцов таблицы", "SSE.Controllers.DocumentHolder.txtUnderbar": "Черта под текстом", "SSE.Controllers.DocumentHolder.txtUndoExpansion": "Отменить авторазвертывание таблицы", "SSE.Controllers.DocumentHolder.txtUseTextImport": "Использовать мастер импорта текста", "SSE.Controllers.DocumentHolder.txtWarnUrl": "Переход по этой ссылке может нанести вред вашему устройству и данным.<br>Вы действительно хотите продолжить?", "SSE.Controllers.DocumentHolder.txtWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Controllers.DocumentHolder.warnFilterError": "Чтобы применить фильтр по значению, область значений должна содержать хотя бы одно поле.", "SSE.Controllers.FormulaDialog.sCategoryAll": "Все", "SSE.Controllers.FormulaDialog.sCategoryCube": "Аналитические", "SSE.Controllers.FormulaDialog.sCategoryCustom": "Пользовательские", "SSE.Controllers.FormulaDialog.sCategoryDatabase": "Базы данных", "SSE.Controllers.FormulaDialog.sCategoryDateAndTime": "Дата и время", "SSE.Controllers.FormulaDialog.sCategoryEngineering": "Инженерные", "SSE.Controllers.FormulaDialog.sCategoryFinancial": "Финансовые", "SSE.Controllers.FormulaDialog.sCategoryInformation": "Информационные", "SSE.Controllers.FormulaDialog.sCategoryLast10": "Последние 10 использованных", "SSE.Controllers.FormulaDialog.sCategoryLogical": "Логические", "SSE.Controllers.FormulaDialog.sCategoryLookupAndReference": "Поиск и ссылки", "SSE.Controllers.FormulaDialog.sCategoryMathematic": "Математические", "SSE.Controllers.FormulaDialog.sCategoryStatistical": "Статистические", "SSE.Controllers.FormulaDialog.sCategoryTextAndData": "Текст и данные", "SSE.Controllers.LeftMenu.newDocumentTitle": "Электронная таблица без имени", "SSE.Controllers.LeftMenu.textByColumns": "По столбцам", "SSE.Controllers.LeftMenu.textByRows": "По строкам", "SSE.Controllers.LeftMenu.textFormulas": "Формулы", "SSE.Controllers.LeftMenu.textItemEntireCell": "Все содержимое ячеек", "SSE.Controllers.LeftMenu.textLoadHistory": "Загрузка истории версий...", "SSE.Controllers.LeftMenu.textLookin": "Область поиска", "SSE.Controllers.LeftMenu.textNoTextFound": "Искомые данные не найдены. Пожалуйста, измените параметры поиска.", "SSE.Controllers.LeftMenu.textReplaceSkipped": "Замена выполнена. Пропущено вхождений - {0}.", "SSE.Controllers.LeftMenu.textReplaceSuccess": "Поиск выполнен. Заменено вхождений: {0}", "SSE.Controllers.LeftMenu.textSave": "Сохранить", "SSE.Controllers.LeftMenu.textSearch": "Просматривать", "SSE.Controllers.LeftMenu.textSelectPath": "Введите новое имя для сохранения копии файла", "SSE.Controllers.LeftMenu.textSheet": "На листе", "SSE.Controllers.LeftMenu.textValues": "Значения", "SSE.Controllers.LeftMenu.textWarning": "Предупреждение", "SSE.Controllers.LeftMenu.textWithin": "Искать", "SSE.Controllers.LeftMenu.textWorkbook": "В книге", "SSE.Controllers.LeftMenu.txtUntitled": "Без имени", "SSE.Controllers.LeftMenu.warnDownloadAs": "Если Вы продолжите сохранение в этот формат, вcя функциональность, кроме текста, будет потеряна.<br>Вы действительно хотите продолжить?", "SSE.Controllers.LeftMenu.warnDownloadCsvSheets": "Формат CSV не поддерживает сохранение файла, содержащего несколько листов.<br>Чтобы оставить выбранный формат и сохранить только текущий лист, нажмите кнопку Сохранить.<br>Чтобы сохранить текущую электронную таблицу, нажмите кнопку Отмена и сохраните таблицу в другом формате.", "SSE.Controllers.Main.confirmAddCellWatches": "Эта операция добавит {0} контрольных значений ячеек.<br>Вы хотите продолжить?", "SSE.Controllers.Main.confirmAddCellWatchesMax": "Эта операция добавит только {0} контрольных значений ячеек для экономии памяти.<br>Вы хотите продолжить?", "SSE.Controllers.Main.confirmMaxChangesSize": "Размер внесенных изменений превышает ограничение, установленное для вашего сервера.<br>Нажмите \"Отменить\" для отмены последнего действия или нажмите \"Продолжить\", чтобы сохранить действие локально (потребуется скачать файл или скопировать его содержимое чтобы ничего не потерялось).", "SSE.Controllers.Main.confirmMoveCellRange": "Конечный диапазон ячеек может содержать данные. Продолжить операцию?", "SSE.Controllers.Main.confirmPutMergeRange": "Исходные данные содержали объединенные ячейки.<br>Перед вставкой этих ячеек в таблицу объединение было отменено.", "SSE.Controllers.Main.confirmReplaceFormulaInTable": "Формулы в строке заголовка будут удалены и преобразованы в статический текст.<br>Вы хотите продолжить?", "SSE.Controllers.Main.confirmReplaceHFPicture": "В каждый раздел заголовка можно вставить только одно изображение.<br>Нажмите \"Заменить\", чтобы заменить существующее изображение.<br>Нажмите \"Сохранить\", чтобы сохранить существующее изображение.", "SSE.Controllers.Main.convertationTimeoutText": "Превышено время ожидания конвертации.", "SSE.Controllers.Main.criticalErrorExtText": "Нажмите \"OK\", чтобы вернуться к списку документов.", "SSE.Controllers.Main.criticalErrorTitle": "Ошибка", "SSE.Controllers.Main.downloadErrorText": "Загрузка не удалась.", "SSE.Controllers.Main.downloadTextText": "Загрузка электронной таблицы...", "SSE.Controllers.Main.downloadTitleText": "Загрузка электронной таблицы", "SSE.Controllers.Main.errNoDuplicates": "Повторяющиеся значения не найдены.", "SSE.Controllers.Main.errorAccessDeny": "Вы пытаетесь выполнить действие, на которое у вас нет прав.<br>Пожалуйста, обратитесь к администратору Сервера документов.", "SSE.Controllers.Main.errorArgsRange": "Ошибка во введенной формуле.<br>Использован неверный диапазон аргументов.", "SSE.Controllers.Main.errorAutoFilterChange": "Операция не разрешена, поскольку предпринимается попытка сдвинуть ячейки таблицы на листе.", "SSE.Controllers.Main.errorAutoFilterChangeFormatTable": "Эту операцию нельзя выполнить для выделенных ячеек, поскольку нельзя переместить часть таблицы.<br>Выберите другой диапазон данных, чтобы перемещалась вся таблица, и повторите попытку.", "SSE.Controllers.Main.errorAutoFilterDataRange": "Операция не может быть произведена для выбранного диапазона ячеек.<br>Выделите однородный диапазон данных, отличный от существующего, и повторите попытку.", "SSE.Controllers.Main.errorAutoFilterHiddenRange": "Операция не может быть произведена, так как область содержит отфильтрованные ячейки.<br>Выведите на экран скрытые фильтром элементы и повторите попытку.", "SSE.Controllers.Main.errorBadImageUrl": "Неправильный URL-адрес изображения", "SSE.Controllers.Main.errorCalculatedItemInPageField": "Этот элемент нельзя добавить или изменить. Отчет сводной таблицы содержит это поле в фильтрах.", "SSE.Controllers.Main.errorCannotPasteImg": "Не удается вставить это изображение из буфера обмена, но вы можете сохранить его на устройстве и вставить оттуда или вы можете скопировать изображение без текста и вставить его в таблицу.", "SSE.Controllers.Main.errorCannotUngroup": "Невозможно разгруппировать. Чтобы создать структуру документа, выделите столбцы или строки и сгруппируйте их.", "SSE.Controllers.Main.errorCannotUseCommandProtectedSheet": "Данную команду нельзя использовать на защищенном листе. Необходимо сначала снять защиту листа.<br>Возможно, потребуется ввести пароль.", "SSE.Controllers.Main.errorChangeArray": "Нельзя изменить часть массива.", "SSE.Controllers.Main.errorChangeFilteredRange": "Это приведет к изменению отфильтрованного диапазона листа.<br>Чтобы выполнить эту задачу, необходимо удалить автофильтры.", "SSE.Controllers.Main.errorChangeOnProtectedSheet": "Ячейка или диаграмма, которую вы пытаетесь изменить, находится на защищенном листе.<br>Чтобы внести изменения, снимите защиту листа. Возможно, потребуется ввести пароль.", "SSE.Controllers.Main.errorCircularReference": "Существует одна или несколько циклических ссылок, в которых формула ссылается на свою собственную ячейку прямо или косвенно.<br>Попробуйте удалить или изменить эти ссылки или переместить формулы в другие ячейки.", "SSE.Controllers.Main.errorCoAuthoringDisconnect": "Потеряно соединение с сервером. В данный момент нельзя отредактировать документ.", "SSE.Controllers.Main.errorConnectToServer": "Не удается сохранить документ. Проверьте параметры подключения или обратитесь к вашему администратору.<br>Когда вы нажмете на кнопку 'OK', вам будет предложено скачать документ.", "SSE.Controllers.Main.errorConvertXml": "Файл неподдерживаемого формата.<br>Можно использовать только формат XML Spreadsheet 2003.", "SSE.Controllers.Main.errorCopyMultiselectArea": "Данная команда неприменима для несвязных диапазонов.<br>Выберите один диапазон и повторите попытку.", "SSE.Controllers.Main.errorCountArg": "Ошибка во введенной формуле.<br>Использовано неверное количество аргументов.", "SSE.Controllers.Main.errorCountArgExceed": "Ошибка во введенной формуле.<br>Превышено количество аргументов.", "SSE.Controllers.Main.errorCreateDefName": "В настоящий момент нельзя отредактировать существующие именованные диапазоны и создать новые,<br>так как некоторые из них редактируются.", "SSE.Controllers.Main.errorCreateRange": "Существующие диапазоны нельзя отредактировать, а новые нельзя создать<br>в данный момент, так как некоторые из них редактируются.", "SSE.Controllers.Main.errorDatabaseConnection": "Внешняя ошибка.<br>Ош<PERSON>бка подключения к базе данных. Если ошибка повторяется, пожалуйста, обратитесь в службу поддержки.", "SSE.Controllers.Main.errorDataEncrypted": "Получены зашифрованные изменения, их нельзя расшифровать.", "SSE.Controllers.Main.errorDataRange": "Некорректный диапазон данных.", "SSE.Controllers.Main.errorDataValidate": "Введенное значение недопустимо.<br>Значения, которые можно ввести в эту ячейку, ограничены.", "SSE.Controllers.Main.errorDefaultMessage": "Код ошибки: %1", "SSE.Controllers.Main.errorDeleteColumnContainsLockedCell": "Вы пытаетесь удалить столбец с заблокированной ячейкой. Заблокированные ячейки нельзя удалять, если лист защищен.<br>Чтобы удалить заблокированную ячейку, снимите защиту листа. Возможно, потребуется ввести пароль.", "SSE.Controllers.Main.errorDeleteRowContainsLockedCell": "Вы пытаетесь удалить строку с заблокированной ячейкой. Заблокированные ячейки нельзя удалять, если лист защищен.<br>Чтобы удалить заблокированную ячейку, снимите защиту листа. Возможно, потребуется ввести пароль.", "SSE.Controllers.Main.errorDependentsNoFormulas": "Команда Зависимые ячейки не нашла формул, которые ссылаются на активную ячейку.", "SSE.Controllers.Main.errorDirectUrl": "Проверьте ссылку на документ.<br>Эта ссылка должна быть прямой ссылкой для скачивания файла.", "SSE.Controllers.Main.errorEditingDownloadas": "В ходе работы с документом произошла ошибка.<br>Используйте опцию 'Скачать как', чтобы сохранить резервную копию файла на диск.", "SSE.Controllers.Main.errorEditingSaveas": "В ходе работы с документом произошла ошибка.<br>Используйте опцию 'Сохранить как...', чтобы сохранить резервную копию файла на диск.", "SSE.Controllers.Main.errorEditView": "Сейчас нельзя отредактировать существующее представление листа и нельзя создавать новые, так как некоторые из них редактируются.", "SSE.Controllers.Main.errorEmailClient": "Не найден почтовый клиент", "SSE.Controllers.Main.errorFilePassProtect": "Файл защищен паролем и не может быть открыт.", "SSE.Controllers.Main.errorFileRequest": "Внешняя ошибка.<br>Ош<PERSON>бка запроса файла. Если ошибка повторяется, пожалуйста, обратитесь в службу поддержки.", "SSE.Controllers.Main.errorFileSizeExceed": "Размер файла превышает ограничение, установленное для вашего сервера.<br>Обратитесь к администратору Сервера документов для получения дополнительной информации.", "SSE.Controllers.Main.errorFileVKey": "Внешняя ошибка.<br>Неверный ключ безопасности. Если ошибка повторяется, пожалуйста, обратитесь в службу поддержки.", "SSE.Controllers.Main.errorFillRange": "Не удается заполнить выбранный диапазон ячеек.<br>Все объединенные ячейки должны быть одного размера.", "SSE.Controllers.Main.errorForceSave": "При сохранении файла произошла ошибка. Используйте опцию 'Скачать как', чтобы сохранить файл на диск или повторите попытку позже.", "SSE.Controllers.Main.errorFormulaInPivotFieldName": "Нельзя ввести формулу для имени элемента или поля в отчете сводной таблицы.", "SSE.Controllers.Main.errorFormulaName": "Ошибка во введенной формуле.<br>Использовано неверное имя формулы.", "SSE.Controllers.Main.errorFormulaParsing": "Внутренняя ошибка при синтаксическом анализе формулы.", "SSE.Controllers.Main.errorFrmlMaxLength": "Длина формулы превышает ограничение в 8192 символа.<br>Отредактируйте ее и повторите попытку.", "SSE.Controllers.Main.errorFrmlMaxReference": "Нельзя ввести эту формулу, так как она содержит слишком много значений,<br>ссылок на ячейки и/или имен.", "SSE.Controllers.Main.errorFrmlMaxTextLength": "Длина текстовых значений в формулах не может превышать 255 символов.<br>Используйте функцию СЦЕПИТЬ или оператор сцепления (&).", "SSE.Controllers.Main.errorFrmlWrongReferences": "Функция ссылается на лист, который не существует.<br>Проверьте данные и повторите попытку.", "SSE.Controllers.Main.errorFTChangeTableRangeError": "Не удалось выполнить операцию для выбранного диапазона ячеек.<br>Выделите диапазон так, чтобы первая строка таблицы находилась на той же самой строке,<br>а итоговая таблица перекрывала текущую.", "SSE.Controllers.Main.errorFTRangeIncludedOtherTables": "Не удалось выполнить операцию для выбранного диапазона ячеек.<br>Выберите диапазон, который не содержит других таблиц.", "SSE.Controllers.Main.errorInconsistentExt": "При открытии файла произошла ошибка.<br>Содержимое файла не соответствует расширению файла.", "SSE.Controllers.Main.errorInconsistentExtDocx": "При открытии файла произошла ошибка.<br>Содержимое файла соответствует документам (например, docx), но файл имеет несоответствующее расширение: %1.", "SSE.Controllers.Main.errorInconsistentExtPdf": "При открытии файла произошла ошибка.<br>Содержимое файла соответствует одному из следующих форматов: pdf/djvu/xps/oxps, но файл имеет несоответствующее расширение: %1.", "SSE.Controllers.Main.errorInconsistentExtPptx": "При открытии файла произошла ошибка.<br>Содержимое файла соответствует презентациям (например, pptx), но файл имеет несоответствующее расширение: %1.", "SSE.Controllers.Main.errorInconsistentExtXlsx": "При открытии файла произошла ошибка.<br>Содержимое файла соответствует электронным таблицам (например, xlsx), но файл имеет несоответствующее расширение: %1.", "SSE.Controllers.Main.errorInvalidRef": "Введите корректное имя для выделенного диапазона или допустимую ссылку для перехода.", "SSE.Controllers.Main.errorKeyEncrypt": "Неизвестный дескриптор ключа", "SSE.Controllers.Main.errorKeyExpire": "Срок действия дескриптора ключа истек", "SSE.Controllers.Main.errorLabledColumnsPivot": "Чтобы создать сводную таблицу, используйте данные, организованные в виде списка с заголовками столбцов.", "SSE.Controllers.Main.errorLoadingFont": "Шрифты не загружены.<br>Пож<PERSON><PERSON><PERSON><PERSON>ста, обратитесь к администратору Сервера документов.", "SSE.Controllers.Main.errorLocationOrDataRangeError": "Недействительная ссылка на расположение или диапазон данных.", "SSE.Controllers.Main.errorLockedAll": "Операция не может быть произведена, так как лист заблокирован другим пользователем.", "SSE.Controllers.Main.errorLockedCellGoalSeek": "Одна из ячеек, участвующих в процессе подбора параметра, изменена другим пользователем.", "SSE.Controllers.Main.errorLockedCellPivot": "Нельзя изменить данные в сводной таблице.", "SSE.Controllers.Main.errorLockedWorksheetRename": "В настоящее время лист нельзя переименовать, так как его переименовывает другой пользователь", "SSE.Controllers.Main.errorMaxPoints": "Максимальное число точек в серии для диаграммы составляет 4096.", "SSE.Controllers.Main.errorMoveRange": "Нельзя изменить часть объединенной ячейки", "SSE.Controllers.Main.errorMoveSlicerError": "Срезы таблиц нельзя копировать из одной рабочей книги в другую.<br>Попробуйте еще раз, выделив всю таблицу и срезы.", "SSE.Controllers.Main.errorMultiCellFormula": "Формулы массива с несколькими ячейками не разрешаются в таблицах.", "SSE.Controllers.Main.errorNoDataToParse": "Не выделены данные для разбора.", "SSE.Controllers.Main.errorNotUniqueFieldWithCalculated": "Если одна или несколько сводных таблиц содержат вычисляемые элементы, ни одно поле не может использоваться в области данных два или более раз или в области данных и другой области одновременно.", "SSE.Controllers.Main.errorOpenWarning": "Одна из формул в файле превышает ограничение в 8192 символа.<br>Формула была удалена.", "SSE.Controllers.Main.errorOperandExpected": "Синтаксис введенной функции некорректен. Проверьте, не пропущена ли одна из скобок - '(' или ')'.", "SSE.Controllers.Main.errorPasswordIsNotCorrect": "Неверный пароль.<br>Убедите<PERSON><PERSON>, что отключена клавиша CAPS LOCK и используется правильный регистр.", "SSE.Controllers.Main.errorPasteInPivot": "Нельзя внести это изменение в выбранные ячейки, поскольку это повлияет на сводную таблицу.<br>Используйте список полей для изменения отчета.", "SSE.Controllers.Main.errorPasteMaxRange": "Область копирования не соответствует области вставки.<br>Для вставки скопированных ячеек выделите область такого же размера или щелкните по первой ячейке в строке.", "SSE.Controllers.Main.errorPasteMultiSelect": "Данное действие неприменимо к нескольким выделенным диапазонам.<br>Выделите один диапазон и повторите попытку.", "SSE.Controllers.Main.errorPasteSlicerError": "Срезы таблиц нельзя копировать из одной рабочей книги в другую.", "SSE.Controllers.Main.errorPivotFieldNameExists": "Имя поля сводной таблицы уже существует.", "SSE.Controllers.Main.errorPivotGroup": "Выделенные объекты нельзя объединить в группу.", "SSE.Controllers.Main.errorPivotOverlap": "Не допускается перекрытие отчета сводной таблицы и таблицы.", "SSE.Controllers.Main.errorPivotWithoutUnderlying": "Отчет сводной таблицы был сохранен без данных.<br>Для обновления отчета используйте кнопку 'Обновить'.", "SSE.Controllers.Main.errorPrecedentsNoValidRef": "Команда Влияющие ячейки требует, чтобы активная ячейка содержала формулу с действительными ссылками.", "SSE.Controllers.Main.errorPrintMaxPagesCount": "К сожалению, в текущей версии программы нельзя напечатать более 1500 страниц за один раз.<br>Это ограничение будет устранено в последующих версиях.", "SSE.Controllers.Main.errorProcessSaveResult": "Сбой при сохранении", "SSE.Controllers.Main.errorProtectedRange": "Этот диапазон нельзя редактировать.", "SSE.Controllers.Main.errorSaveWatermark": "Этот файл содержит изображение водяного знака, привязанное к другому домену.<br>Чтобы сделать его видимым в PDF, обновите изображение водяного знака, чтобы оно было связано с тем же доменом, что и документ, или загрузите его со своего компьютера.", "SSE.Controllers.Main.errorServerVersion": "Версия редактора была обновлена. Страница будет перезагружена, чтобы применить изменения.", "SSE.Controllers.Main.errorSessionAbsolute": "Время сеанса редактирования документа истекло. Пожалуйста, обновите страницу.", "SSE.Controllers.Main.errorSessionIdle": "Документ долгое время не редактировался. Пожалуйста, обновите страницу.", "SSE.Controllers.Main.errorSessionToken": "Подключение к серверу было прервано. Пожалуйста, обновите страницу.", "SSE.Controllers.Main.errorSetPassword": "Не удалось задать пароль.", "SSE.Controllers.Main.errorSingleColumnOrRowError": "Ссылка на расположение недопустима, так как ячейки находятся в разных столбцах или строках.<br>Выделите ячейки, расположенные в одном столбце или одной строке.", "SSE.Controllers.Main.errorStockChart": "Неверный порядок строк. Чтобы создать биржевую диаграмму, расположите данные на листе в следующем порядке:<br> цена открытия, максимальная цена, минимальная цена, цена закрытия.", "SSE.Controllers.Main.errorToken": "Токен безопасности документа имеет неправильный формат.<br>Пожалуйста, обратитесь к администратору Сервера документов.", "SSE.Controllers.Main.errorTokenExpire": "Истек срок действия токена безопасности документа.<br>Пожалуйста, обратитесь к администратору Сервера документов.", "SSE.Controllers.Main.errorUnexpectedGuid": "Внешняя ошибка.<br>Непредвиденный идентификатор GUID. Если ошибка повторяется, пожалуйста, обратитесь в службу поддержки.", "SSE.Controllers.Main.errorUpdateVersion": "Версия файла была изменена. Страница будет перезагружена.", "SSE.Controllers.Main.errorUpdateVersionOnDisconnect": "Соединение было восстановлено, и версия файла изменилась.<br>Прежде чем продолжить работу, надо скачать файл или скопировать его содержимое, чтобы обеспечить сохранность данных, а затем перезагрузить страницу.", "SSE.Controllers.Main.errorUserDrop": "В настоящий момент файл недоступен.", "SSE.Controllers.Main.errorUsersExceed": "Превышено количество пользователей, разрешенных согласно тарифному плану", "SSE.Controllers.Main.errorViewerDisconnect": "Подключение прервано. Вы по-прежнему можете просматривать документ,<br>но не сможете скачать или напечатать его до восстановления подключения и обновления страницы.", "SSE.Controllers.Main.errorWrongBracketsCount": "Ошибка во введенной формуле.<br>Использовано неверное количество скобок.", "SSE.Controllers.Main.errorWrongOperator": "Ошибка во введенной формуле. Использован неправильный оператор.<br>Пожалуйста, исправьте ошибку.", "SSE.Controllers.Main.errorWrongPassword": "Неверный пароль.", "SSE.Controllers.Main.errRemDuplicates": "Найдено и удалено повторяющихся значений: {0}, осталось уникальных значений: {1}.", "SSE.Controllers.Main.leavePageText": "Электронная таблица содержит несохраненные изменения. Чтобы сохранить их, нажмите 'Остаться на этой странице', затем 'Сохранить'. Нажмите 'Покинуть эту страницу', чтобы сбросить все несохраненные изменения.", "SSE.Controllers.Main.leavePageTextOnClose": "Все несохраненные изменения в этой электронной таблице будут потеряны.<br> Нажмите кнопку \"Отмена\", а затем нажмите кнопку \"Сохранить\", чтобы сохранить их. Нажмите кнопку \"OK\", чтобы сбросить все несохраненные изменения.", "SSE.Controllers.Main.loadFontsTextText": "Загрузка данных...", "SSE.Controllers.Main.loadFontsTitleText": "Загрузка данных", "SSE.Controllers.Main.loadFontTextText": "Загрузка данных...", "SSE.Controllers.Main.loadFontTitleText": "Загрузка данных", "SSE.Controllers.Main.loadImagesTextText": "Загрузка изображений...", "SSE.Controllers.Main.loadImagesTitleText": "Загрузка изображений", "SSE.Controllers.Main.loadImageTextText": "Загрузка изображения...", "SSE.Controllers.Main.loadImageTitleText": "Загрузка изображения", "SSE.Controllers.Main.loadingDocumentTitleText": "Загрузка таблицы", "SSE.Controllers.Main.notcriticalErrorTitle": "Предупреждение", "SSE.Controllers.Main.openErrorText": "При открытии файла произошла ошибка.", "SSE.Controllers.Main.openTextText": "Открытие электронной таблицы...", "SSE.Controllers.Main.openTitleText": "Открытие электронной таблицы", "SSE.Controllers.Main.pastInMergeAreaError": "Нельзя изменить часть объединенной ячейки", "SSE.Controllers.Main.printTextText": "Печать электронной таблицы...", "SSE.Controllers.Main.printTitleText": "Печать электронной таблицы", "SSE.Controllers.Main.reloadButtonText": "Обновить страницу", "SSE.Controllers.Main.requestEditFailedMessageText": "В настоящее время документ редактируется. Пожалуйста, попробуйте позже.", "SSE.Controllers.Main.requestEditFailedTitleText": "Доступ запрещен", "SSE.Controllers.Main.saveErrorText": "При сохранении файла произошла ошибка.", "SSE.Controllers.Main.saveErrorTextDesktop": "Нельзя сохранить или создать этот файл.<br>Возможные причины: <br>1. Файл доступен только для чтения. <br>2. Файл редактируется другими пользователями. <br>3. Диск заполнен или поврежден.", "SSE.Controllers.Main.saveTextText": "Сохранение электронной таблицы...", "SSE.Controllers.Main.saveTitleText": "Сохранение электронной таблицы", "SSE.Controllers.Main.scriptLoadError": "Слишком медленное подключение, некоторые компоненты не удалось загрузить. Пожалуйста, обновите страницу.", "SSE.Controllers.Main.textAnonymous": "Аноним", "SSE.Controllers.Main.textApplyAll": "Применить ко всем уравнениям", "SSE.Controllers.Main.textBuyNow": "Перейти на сайт", "SSE.Controllers.Main.textChangesSaved": "Все изменения сохранены", "SSE.Controllers.Main.textClose": "Закрыть", "SSE.Controllers.Main.textCloseTip": "Щелкните, чтобы закрыть эту подсказку", "SSE.Controllers.Main.textConfirm": "Подтверждение", "SSE.Controllers.Main.textConnectionLost": "Попытка подключения. Проверьте настройки подключения.", "SSE.Controllers.Main.textContactUs": "Связаться с отделом продаж", "SSE.Controllers.Main.textContinue": "Продолжить", "SSE.Controllers.Main.textConvertEquation": "Это уравнение создано в старой версии редактора уравнений, которая больше не поддерживается. Чтобы изменить это уравнение, его необходимо преобразовать в формат Office Math ML.<br>Преобразовать сейчас?", "SSE.Controllers.Main.textCustomLoader": "Обратите внимание, что по условиям лицензии у вас нет прав изменять экран, отображаемый при загрузке.<br>Пожалуйста, обратитесь в наш отдел продаж, чтобы сделать запрос.", "SSE.Controllers.Main.textDisconnect": "Соединение потеряно", "SSE.Controllers.Main.textFillOtherRows": "Заполнить другие строки", "SSE.Controllers.Main.textFormulaFilledAllRows": "Формула заполнила {0} строк с данными. Заполнение других пустых строк может занять несколько минут.", "SSE.Controllers.Main.textFormulaFilledAllRowsWithEmpty": "Формула заполнила первые {0} строк. Заполнение других пустых строк может занять несколько минут.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherHaveData": "Формула заполнила только первые {0} строк с данными в целях экономии памяти. На этом листе есть еще {1} строк с данными. Вы можете заполнить их вручную.", "SSE.Controllers.Main.textFormulaFilledFirstRowsOtherIsEmpty": "Формула заполнила только первые {0} строк в целях экономии памяти. Остальные строки на этом листе не содержат данных.", "SSE.Controllers.Main.textGuest": "Гость", "SSE.Controllers.Main.textHasMacros": "Файл содержит автозапускаемые макросы.<br>Хотите запустить макросы?", "SSE.Controllers.Main.textKeep": "Сохранить", "SSE.Controllers.Main.textLearnMore": "Подробнее", "SSE.Controllers.Main.textLoadingDocument": "Загрузка таблицы", "SSE.Controllers.Main.textLongName": "Введите имя длиной менее 128 символов.", "SSE.Controllers.Main.textNeedSynchronize": "Есть обновления", "SSE.Controllers.Main.textNo": "Нет", "SSE.Controllers.Main.textNoLicenseTitle": "Лицензионное ограничение", "SSE.Controllers.Main.textPaidFeature": "Платная функция", "SSE.Controllers.Main.textPleaseWait": "Операция может занять больше времени, чем предполагалось. Пожалуйста, подождите...", "SSE.Controllers.Main.textReconnect": "Соединение восстановлено", "SSE.Controllers.Main.textRemember": "Запомнить мой выбор для всех файлов", "SSE.Controllers.Main.textRememberMacros": "Запомнить мой выбор для всех макросов", "SSE.Controllers.Main.textRenameError": "Имя пользователя не должно быть пустым.", "SSE.Controllers.Main.textRenameLabel": "Введите имя, которое будет использоваться для совместной работы", "SSE.Controllers.Main.textReplace": "Заменить", "SSE.Controllers.Main.textRequestMacros": "Макрос делает запрос на URL. Вы хотите разрешить запрос на %1?", "SSE.Controllers.Main.textShape": "Фигура", "SSE.Controllers.Main.textStrict": "Строгий режим", "SSE.Controllers.Main.textText": "Текст", "SSE.Controllers.Main.textTryQuickPrint": "Вы выбрали быструю печать: весь документ будет напечатан на последнем выбранном принтере или на принтере по умолчанию.<br>Вы хотите продолжить?", "SSE.Controllers.Main.textTryUndoRedo": "Функции отмены и повтора действий отключены в Быстром режиме совместного редактирования.<br>Нажмите на кнопку 'Строгий режим' для переключения в Строгий режим совместного редактирования, чтобы редактировать файл без вмешательства других пользователей и отправлять изменения только после того, как вы их сохраните. Переключаться между режимами совместного редактирования можно с помощью Дополнительных параметров редактора.", "SSE.Controllers.Main.textTryUndoRedoWarn": "Функции отмены и повтора действий отключены в Быстром режиме совместного редактирования.", "SSE.Controllers.Main.textUndo": "Отменить", "SSE.Controllers.Main.textUpdateVersion": "Документ нельзя отредактировать прямо сейчас.<br>Попытка обновить файл, пожалуйста, подождите...", "SSE.Controllers.Main.textUpdating": "Обновление", "SSE.Controllers.Main.textYes": "Да", "SSE.Controllers.Main.titleLicenseExp": "Истек срок действия лицензии", "SSE.Controllers.Main.titleLicenseNotActive": "Лицензия неактивна", "SSE.Controllers.Main.titleServerVersion": "Редактор обновлен", "SSE.Controllers.Main.titleUpdateVersion": "Версия изменилась", "SSE.Controllers.Main.txtAccent": "Акцент", "SSE.Controllers.Main.txtAll": "(Все)", "SSE.Controllers.Main.txtArt": "Введите ваш текст", "SSE.Controllers.Main.txtBasicShapes": "Основные фигуры", "SSE.Controllers.Main.txtBlank": "(пусто)", "SSE.Controllers.Main.txtButtons": "Кнопки", "SSE.Controllers.Main.txtByField": "%1 из %2", "SSE.Controllers.Main.txtCallouts": "Выноски", "SSE.Controllers.Main.txtCharts": "Схемы", "SSE.Controllers.Main.txtClearFilter": "Очистить фильтр", "SSE.Controllers.Main.txtColLbls": "Названия столбцов", "SSE.Controllers.Main.txtColumn": "Столбец", "SSE.Controllers.Main.txtConfidential": "Конфиденциально", "SSE.Controllers.Main.txtDate": "Дата", "SSE.Controllers.Main.txtDays": "<PERSON><PERSON>и", "SSE.Controllers.Main.txtDiagramTitle": "Заголовок диаграммы", "SSE.Controllers.Main.txtEditingMode": "Установка режима редактирования...", "SSE.Controllers.Main.txtErrorLoadHistory": "Не удалось загрузить историю", "SSE.Controllers.Main.txtFiguredArrows": "Фигурные стрелки", "SSE.Controllers.Main.txtFile": "<PERSON>а<PERSON><PERSON>", "SSE.Controllers.Main.txtGrandTotal": "Общий итог", "SSE.Controllers.Main.txtGroup": "Группа", "SSE.Controllers.Main.txtHours": "<PERSON>а<PERSON>ы", "SSE.Controllers.Main.txtInfo": "Информация", "SSE.Controllers.Main.txtLines": "Линии", "SSE.Controllers.Main.txtMath": "Математические знаки", "SSE.Controllers.Main.txtMinutes": "Минуты", "SSE.Controllers.Main.txtMonths": "Месяцы", "SSE.Controllers.Main.txtMultiSelect": "Множественный выбор", "SSE.Controllers.Main.txtNone": "Нет", "SSE.Controllers.Main.txtOr": "%1 или %2", "SSE.Controllers.Main.txtPage": "Страница", "SSE.Controllers.Main.txtPageOf": "Страница %1 из %2", "SSE.Controllers.Main.txtPages": "Стр<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtPicture": "Изображение", "SSE.Controllers.Main.txtPivotTable": "СводнаяТаблица", "SSE.Controllers.Main.txtPreparedBy": "Подготовил:", "SSE.Controllers.Main.txtPrintArea": "Область_печати", "SSE.Controllers.Main.txtQuarter": "Кв-л", "SSE.Controllers.Main.txtQuarters": "Кварталы", "SSE.Controllers.Main.txtRectangles": "Прямоугольники", "SSE.Controllers.Main.txtRow": "Строка", "SSE.Controllers.Main.txtRowLbls": "Названия строк", "SSE.Controllers.Main.txtSaveCopyAsComplete": "Копия файла успешно сохранена", "SSE.Controllers.Main.txtScheme_Aspect": "Аспект", "SSE.Controllers.Main.txtScheme_Blue": "Синий", "SSE.Controllers.Main.txtScheme_Blue_Green": "Синий и зеленый", "SSE.Controllers.Main.txtScheme_Blue_II": "Синий II", "SSE.Controllers.Main.txtScheme_Blue_Warm": "Теплый синий", "SSE.Controllers.Main.txtScheme_Grayscale": "Серая", "SSE.Controllers.Main.txtScheme_Green": "Зеленая", "SSE.Controllers.Main.txtScheme_Green_Yellow": "Зеленый и желтый", "SSE.Controllers.Main.txtScheme_Marquee": "Индикатор", "SSE.Controllers.Main.txtScheme_Median": "Обычная", "SSE.Controllers.Main.txtScheme_Office": "Стандартная", "SSE.Controllers.Main.txtScheme_Office_2007___2010": "Office 2007 - 2010", "SSE.Controllers.Main.txtScheme_Office_2013___2022": "Office 2013 - 2022", "SSE.Controllers.Main.txtScheme_Orange": "Оранжевый", "SSE.Controllers.Main.txtScheme_Orange_Red": "Оранжевый и красный", "SSE.Controllers.Main.txtScheme_Paper": "Бумажная", "SSE.Controllers.Main.txtScheme_Red": "Красный", "SSE.Controllers.Main.txtScheme_Red_Orange": "Красный и оранжевый", "SSE.Controllers.Main.txtScheme_Red_Violet": "Красный и фиолетовый", "SSE.Controllers.Main.txtScheme_Slipstream": "Воздушный поток", "SSE.Controllers.Main.txtScheme_Violet": "Фиолетовый", "SSE.Controllers.Main.txtScheme_Violet_II": "Фиолетовый II", "SSE.Controllers.Main.txtScheme_Yellow": "Желт<PERSON>й", "SSE.Controllers.Main.txtScheme_Yellow_Orange": "Желтый и оранжевый", "SSE.Controllers.Main.txtSeconds": "Секунды", "SSE.Controllers.Main.txtSeries": "<PERSON>яд", "SSE.Controllers.Main.txtShape_accentBorderCallout1": "Выноска 1 (граница и черта)", "SSE.Controllers.Main.txtShape_accentBorderCallout2": "Выноска 2 (граница и черта)", "SSE.Controllers.Main.txtShape_accentBorderCallout3": "Выноска 3 (граница и черта)", "SSE.Controllers.Main.txtShape_accentCallout1": "Выноска 1 (черта)", "SSE.Controllers.Main.txtShape_accentCallout2": "Выноска 2 (черта)", "SSE.Controllers.Main.txtShape_accentCallout3": "Выноска 3 (черта)", "SSE.Controllers.Main.txtShape_actionButtonBackPrevious": "Кнопка \"Назад\"", "SSE.Controllers.Main.txtShape_actionButtonBeginning": "Кнопка \"В начало\"", "SSE.Controllers.Main.txtShape_actionButtonBlank": "Пустая кнопка", "SSE.Controllers.Main.txtShape_actionButtonDocument": "Кнопка документа", "SSE.Controllers.Main.txtShape_actionButtonEnd": "Кнопка \"В конец\"", "SSE.Controllers.Main.txtShape_actionButtonForwardNext": "Кнопка \"Вперед\"", "SSE.Controllers.Main.txtShape_actionButtonHelp": "Кнопка \"Справка\"", "SSE.Controllers.Main.txtShape_actionButtonHome": "Кнопка \"Домой\"", "SSE.Controllers.Main.txtShape_actionButtonInformation": "Кнопка сведений", "SSE.Controllers.Main.txtShape_actionButtonMovie": "Кнопка видео", "SSE.Controllers.Main.txtShape_actionButtonReturn": "Кнопка возврата", "SSE.Controllers.Main.txtShape_actionButtonSound": "Кнопка звука", "SSE.Controllers.Main.txtShape_arc": "Дуга", "SSE.Controllers.Main.txtShape_bentArrow": "Угловая стрелка", "SSE.Controllers.Main.txtShape_bentConnector5": "Соединительная линия уступом", "SSE.Controllers.Main.txtShape_bentConnector5WithArrow": "Уступ со стрелкой", "SSE.Controllers.Main.txtShape_bentConnector5WithTwoArrows": "Уступ с двумя стрелками", "SSE.Controllers.Main.txtShape_bentUpArrow": "Угловая стрелка вверх", "SSE.Controllers.Main.txtShape_bevel": "Багетная рамка", "SSE.Controllers.Main.txtShape_blockArc": "Арка", "SSE.Controllers.Main.txtShape_borderCallout1": "Выноска 1", "SSE.Controllers.Main.txtShape_borderCallout2": "Выноска 2", "SSE.Controllers.Main.txtShape_borderCallout3": "Выноска 3", "SSE.Controllers.Main.txtShape_bracePair": "Двойные фигурные скобки", "SSE.Controllers.Main.txtShape_callout1": "Выноска 1 (без границы)", "SSE.Controllers.Main.txtShape_callout2": "Выноска 2 (без границы)", "SSE.Controllers.Main.txtShape_callout3": "Выноска 3 (без границы)", "SSE.Controllers.Main.txtShape_can": "Цилиндр", "SSE.Controllers.Main.txtShape_chevron": "Шевр<PERSON>н", "SSE.Controllers.Main.txtShape_chord": "Сегмент круга", "SSE.Controllers.Main.txtShape_circularArrow": "Круговая стрелка", "SSE.Controllers.Main.txtShape_cloud": "Облако", "SSE.Controllers.Main.txtShape_cloudCallout": "Выноска-облако", "SSE.Controllers.Main.txtShape_corner": "Угол", "SSE.Controllers.Main.txtShape_cube": "<PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_curvedConnector3": "Скругленная соединительная линия", "SSE.Controllers.Main.txtShape_curvedConnector3WithArrow": "Скругленная линия со стрелкой", "SSE.Controllers.Main.txtShape_curvedConnector3WithTwoArrows": "Скругленная линия с двумя стрелками", "SSE.Controllers.Main.txtShape_curvedDownArrow": "Выгнутая вверх стрелка", "SSE.Controllers.Main.txtShape_curvedLeftArrow": "Выгнутая вправо стрелка", "SSE.Controllers.Main.txtShape_curvedRightArrow": "Выгнутая влево стрелка", "SSE.Controllers.Main.txtShape_curvedUpArrow": "Выгнутая вниз стрелка", "SSE.Controllers.Main.txtShape_decagon": "Десятиугольник", "SSE.Controllers.Main.txtShape_diagStripe": "Диагональная полоса", "SSE.Controllers.Main.txtShape_diamond": "Ромб", "SSE.Controllers.Main.txtShape_dodecagon": "Двенадцатиугольник", "SSE.Controllers.Main.txtShape_donut": "Кольцо", "SSE.Controllers.Main.txtShape_doubleWave": "Двойная волна", "SSE.Controllers.Main.txtShape_downArrow": "Стрелка вниз", "SSE.Controllers.Main.txtShape_downArrowCallout": "Выноска со стрелкой вниз", "SSE.Controllers.Main.txtShape_ellipse": "Элли<PERSON><PERSON>", "SSE.Controllers.Main.txtShape_ellipseRibbon": "Круглая лента лицом вниз", "SSE.Controllers.Main.txtShape_ellipseRibbon2": "Круглая лента лицом вверх", "SSE.Controllers.Main.txtShape_flowChartAlternateProcess": "Блок-схема: альтернативный процесс", "SSE.Controllers.Main.txtShape_flowChartCollate": "Блок-схема: сопоставление", "SSE.Controllers.Main.txtShape_flowChartConnector": "Блок-схема: соединительная линия", "SSE.Controllers.Main.txtShape_flowChartDecision": "Блок-схема: решение", "SSE.Controllers.Main.txtShape_flowChartDelay": "Блок-схема: задержка", "SSE.Controllers.Main.txtShape_flowChartDisplay": "Блок-схема: дисплей", "SSE.Controllers.Main.txtShape_flowChartDocument": "Блок-схема: документ", "SSE.Controllers.Main.txtShape_flowChartExtract": "Блок-схема: извлечение", "SSE.Controllers.Main.txtShape_flowChartInputOutput": "Блок-схема: данные", "SSE.Controllers.Main.txtShape_flowChartInternalStorage": "Блок-схема: внутренняя память", "SSE.Controllers.Main.txtShape_flowChartMagneticDisk": "Блок-схема: магнитный диск", "SSE.Controllers.Main.txtShape_flowChartMagneticDrum": "Блок-схема: память с прямым доступом", "SSE.Controllers.Main.txtShape_flowChartMagneticTape": "Блок-схема: память с посл. доступом", "SSE.Controllers.Main.txtShape_flowChartManualInput": "Блок-схема: ручной ввод", "SSE.Controllers.Main.txtShape_flowChartManualOperation": "Блок-схема: ручное управление", "SSE.Controllers.Main.txtShape_flowChartMerge": "Блок-схема: объединение", "SSE.Controllers.Main.txtShape_flowChartMultidocument": "Блок-схема: несколько документов", "SSE.Controllers.Main.txtShape_flowChartOffpageConnector": "Блок-схема: ссылка на другую страницу", "SSE.Controllers.Main.txtShape_flowChartOnlineStorage": "Блок-схема: сохраненные данные", "SSE.Controllers.Main.txtShape_flowChartOr": "Блок-схема: ИЛИ", "SSE.Controllers.Main.txtShape_flowChartPredefinedProcess": "Блок-схема: типовой процесс", "SSE.Controllers.Main.txtShape_flowChartPreparation": "Блок-схема: подготовка", "SSE.Controllers.Main.txtShape_flowChartProcess": "Блок-схема: процесс", "SSE.Controllers.Main.txtShape_flowChartPunchedCard": "Блок-схема: карточка", "SSE.Controllers.Main.txtShape_flowChartPunchedTape": "Блок-схема: перфолента", "SSE.Controllers.Main.txtShape_flowChartSort": "Блок-схема: сортировка", "SSE.Controllers.Main.txtShape_flowChartSummingJunction": "Блок-схема: узел суммирования", "SSE.Controllers.Main.txtShape_flowChartTerminator": "Блок-схема: знак завершения", "SSE.Controllers.Main.txtShape_foldedCorner": "Загнутый угол", "SSE.Controllers.Main.txtShape_frame": "Рамка", "SSE.Controllers.Main.txtShape_halfFrame": "Половина рамки", "SSE.Controllers.Main.txtShape_heart": "Сердце", "SSE.Controllers.Main.txtShape_heptagon": "Семиугольник", "SSE.Controllers.Main.txtShape_hexagon": "Шестиугольник", "SSE.Controllers.Main.txtShape_homePlate": "Пятиугольник", "SSE.Controllers.Main.txtShape_horizontalScroll": "Горизонтальный свиток", "SSE.Controllers.Main.txtShape_irregularSeal1": "Вспышка 1", "SSE.Controllers.Main.txtShape_irregularSeal2": "Вспышка 2", "SSE.Controllers.Main.txtShape_leftArrow": "Стрелка влево", "SSE.Controllers.Main.txtShape_leftArrowCallout": "Выноска со стрелкой влево", "SSE.Controllers.Main.txtShape_leftBrace": "Левая фигурная скобка", "SSE.Controllers.Main.txtShape_leftBracket": "Левая круглая скобка", "SSE.Controllers.Main.txtShape_leftRightArrow": "Двойная стрелка влево-вправо", "SSE.Controllers.Main.txtShape_leftRightArrowCallout": "Выноска со стрелками влево-вправо", "SSE.Controllers.Main.txtShape_leftRightUpArrow": "Тройная стрелка влево-вправо-вверх", "SSE.Controllers.Main.txtShape_leftUpArrow": "Двойная стрелка влево-вверх", "SSE.Controllers.Main.txtShape_lightningBolt": "Молния", "SSE.Controllers.Main.txtShape_line": "Линия", "SSE.Controllers.Main.txtShape_lineWithArrow": "Стрелка", "SSE.Controllers.Main.txtShape_lineWithTwoArrows": "Двойная стрелка", "SSE.Controllers.Main.txtShape_mathDivide": "Деление", "SSE.Controllers.Main.txtShape_mathEqual": "Равно", "SSE.Controllers.Main.txtShape_mathMinus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_mathMultiply": "Умножение", "SSE.Controllers.Main.txtShape_mathNotEqual": "Не равно", "SSE.Controllers.Main.txtShape_mathPlus": "Плюс", "SSE.Controllers.Main.txtShape_moon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtShape_noSmoking": "Запрещено", "SSE.Controllers.Main.txtShape_notchedRightArrow": "Стрелка вправо с вырезом", "SSE.Controllers.Main.txtShape_octagon": "Восьмиугольник", "SSE.Controllers.Main.txtShape_parallelogram": "Параллелограмм", "SSE.Controllers.Main.txtShape_pentagon": "Пятиугольник", "SSE.Controllers.Main.txtShape_pie": "Сектор круга", "SSE.Controllers.Main.txtShape_plaque": "Табличка", "SSE.Controllers.Main.txtShape_plus": "Плюс", "SSE.Controllers.Main.txtShape_polyline1": "Рисованная кривая", "SSE.Controllers.Main.txtShape_polyline2": "Произвольная форма", "SSE.Controllers.Main.txtShape_quadArrow": "Счетверенная стрелка", "SSE.Controllers.Main.txtShape_quadArrowCallout": "Выноска с четырьмя стрелками", "SSE.Controllers.Main.txtShape_rect": "Прямоугольник", "SSE.Controllers.Main.txtShape_ribbon": "Лента лицом вниз", "SSE.Controllers.Main.txtShape_ribbon2": "Лента лицом вверх", "SSE.Controllers.Main.txtShape_rightArrow": "Стрелка вправо", "SSE.Controllers.Main.txtShape_rightArrowCallout": "Выноска со стрелкой вправо", "SSE.Controllers.Main.txtShape_rightBrace": "Правая фигурная скобка", "SSE.Controllers.Main.txtShape_rightBracket": "Правая круглая скобка", "SSE.Controllers.Main.txtShape_round1Rect": "Прямоугольник с одним скругленным углом", "SSE.Controllers.Main.txtShape_round2DiagRect": "Прямоугольник с двумя скругленными противолежащими углами", "SSE.Controllers.Main.txtShape_round2SameRect": "Прямоугольник с двумя скругленными соседними углами", "SSE.Controllers.Main.txtShape_roundRect": "Прямоугольник со скругленными углами", "SSE.Controllers.Main.txtShape_rtTriangle": "Прямоугольный треугольник", "SSE.Controllers.Main.txtShape_smileyFace": "Улыбающееся лицо", "SSE.Controllers.Main.txtShape_snip1Rect": "Прямоугольник с одним вырезанным углом", "SSE.Controllers.Main.txtShape_snip2DiagRect": "Прямоугольник с двумя вырезанными противолежащими углами", "SSE.Controllers.Main.txtShape_snip2SameRect": "Прямоугольник с двумя вырезанными соседними углами", "SSE.Controllers.Main.txtShape_snipRoundRect": "Прямоугольник с одним вырезанным скругленным углом", "SSE.Controllers.Main.txtShape_spline": "Кривая", "SSE.Controllers.Main.txtShape_star10": "10-конечная звезда", "SSE.Controllers.Main.txtShape_star12": "12-конечная звезда", "SSE.Controllers.Main.txtShape_star16": "16-конечная звезда", "SSE.Controllers.Main.txtShape_star24": "24-конечная звезда", "SSE.Controllers.Main.txtShape_star32": "32-конечная звезда", "SSE.Controllers.Main.txtShape_star4": "4-конечная звезда", "SSE.Controllers.Main.txtShape_star5": "5-конечная звезда", "SSE.Controllers.Main.txtShape_star6": "6-конечная звезда", "SSE.Controllers.Main.txtShape_star7": "7-конечная звезда", "SSE.Controllers.Main.txtShape_star8": "8-конечная звезда", "SSE.Controllers.Main.txtShape_stripedRightArrow": "Штриховая стрелка вправо", "SSE.Controllers.Main.txtShape_sun": "Солнце", "SSE.Controllers.Main.txtShape_teardrop": "Капля", "SSE.Controllers.Main.txtShape_textRect": "Надпись", "SSE.Controllers.Main.txtShape_trapezoid": "Трапеция", "SSE.Controllers.Main.txtShape_triangle": "Треугольник", "SSE.Controllers.Main.txtShape_upArrow": "Стрелка вверх", "SSE.Controllers.Main.txtShape_upArrowCallout": "Выноска со стрелкой вверх", "SSE.Controllers.Main.txtShape_upDownArrow": "Двойная стрелка вверх-вниз", "SSE.Controllers.Main.txtShape_uturnArrow": "Развернутая стрелка", "SSE.Controllers.Main.txtShape_verticalScroll": "Вертикальный свиток", "SSE.Controllers.Main.txtShape_wave": "Волна", "SSE.Controllers.Main.txtShape_wedgeEllipseCallout": "Овальная выноска", "SSE.Controllers.Main.txtShape_wedgeRectCallout": "Прямоугольная выноска", "SSE.Controllers.Main.txtShape_wedgeRoundRectCallout": "Скругленная прямоугольная выноска", "SSE.Controllers.Main.txtSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtSlicer": "Срез", "SSE.Controllers.Main.txtStarsRibbons": "Звезды и ленты", "SSE.Controllers.Main.txtStyle_Bad": "Пл<PERSON>х<PERSON>й", "SSE.Controllers.Main.txtStyle_Calculation": "Пересчет", "SSE.Controllers.Main.txtStyle_Check_Cell": "Контрольная ячейка", "SSE.Controllers.Main.txtStyle_Comma": "Финансовый", "SSE.Controllers.Main.txtStyle_Currency": "Денежный", "SSE.Controllers.Main.txtStyle_Explanatory_Text": "Пояснение", "SSE.Controllers.Main.txtStyle_Good": "Хоро<PERSON><PERSON>", "SSE.Controllers.Main.txtStyle_Heading_1": "Заголовок 1", "SSE.Controllers.Main.txtStyle_Heading_2": "Заголовок 2", "SSE.Controllers.Main.txtStyle_Heading_3": "Заголовок 3", "SSE.Controllers.Main.txtStyle_Heading_4": "Заголовок 4", "SSE.Controllers.Main.txtStyle_Input": "Ввод", "SSE.Controllers.Main.txtStyle_Linked_Cell": "Связанная ячейка", "SSE.Controllers.Main.txtStyle_Neutral": "Нейтральный", "SSE.Controllers.Main.txtStyle_Normal": "Обычный", "SSE.Controllers.Main.txtStyle_Note": "Примечание", "SSE.Controllers.Main.txtStyle_Output": "Вывод", "SSE.Controllers.Main.txtStyle_Percent": "Процентный", "SSE.Controllers.Main.txtStyle_Title": "Название", "SSE.Controllers.Main.txtStyle_Total": "Итог", "SSE.Controllers.Main.txtStyle_Warning_Text": "Текст предупреждения", "SSE.Controllers.Main.txtTab": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Main.txtTable": "Таблица", "SSE.Controllers.Main.txtTime": "Время", "SSE.Controllers.Main.txtUnlock": "Разблокировать", "SSE.Controllers.Main.txtUnlockRange": "Разблокировать диапазон", "SSE.Controllers.Main.txtUnlockRangeDescription": "Введите пароль для изменения этого диапазона:", "SSE.Controllers.Main.txtUnlockRangeWarning": "Диа<PERSON>азон, который вы пытаетесь изменить, защищен с помощью пароля.", "SSE.Controllers.Main.txtValues": "Значения", "SSE.Controllers.Main.txtView": "Представление", "SSE.Controllers.Main.txtXAxis": "Ось X", "SSE.Controllers.Main.txtYAxis": "Ось Y", "SSE.Controllers.Main.txtYears": "Годы", "SSE.Controllers.Main.unknownErrorText": "Неизвестная ошибка.", "SSE.Controllers.Main.unsupportedBrowserErrorText": "Ваш браузер не поддерживается.", "SSE.Controllers.Main.uploadDocExtMessage": "Неизвестный формат документа.", "SSE.Controllers.Main.uploadDocFileCountMessage": "Ни одного документа не загружено.", "SSE.Controllers.Main.uploadDocSizeMessage": "Превышен максимальный размер документа.", "SSE.Controllers.Main.uploadImageExtMessage": "Неизвестный формат изображения.", "SSE.Controllers.Main.uploadImageFileCountMessage": "Ни одного изображения не загружено.", "SSE.Controllers.Main.uploadImageSizeMessage": "Слишком большое изображение. Максимальный размер - 25 MB.", "SSE.Controllers.Main.uploadImageTextText": "Загрузка изображения...", "SSE.Controllers.Main.uploadImageTitleText": "Загрузка изображения", "SSE.Controllers.Main.waitText": "Пожалуйста, подождите...", "SSE.Controllers.Main.warnBrowserIE9": "В IE9 приложение имеет низкую производительность. Используйте IE10 или более позднюю версию.", "SSE.Controllers.Main.warnBrowserZoom": "Текущее значение масштаба страницы в браузере поддерживается не полностью. Вернитесь к масштабу по умолчанию, нажав Ctrl+0", "SSE.Controllers.Main.warnLicenseAnonymous": "Доступ запрещен для анонимных пользователей.<br>Этот документ будет открыт только на просмотр.", "SSE.Controllers.Main.warnLicenseBefore": "Лицензия неактивна.<br>По<PERSON><PERSON><PERSON><PERSON><PERSON>ста, обратитесь к администратору.", "SSE.Controllers.Main.warnLicenseExceeded": "Вы достигли лимита на одновременные подключения к редакторам %1. Этот документ будет открыт только на просмотр.<br>Свяжитесь с администратором, чтобы узнать больше.", "SSE.Controllers.Main.warnLicenseExp": "Истек срок действия лицензии.<br>Обновите лицензию, а затем обновите страницу.", "SSE.Controllers.Main.warnLicenseLimitedNoAccess": "Истек срок действия лицензии.<br>Нет доступа к функциональности редактирования документов.<br>Пожалуйста, обратитесь к администратору.", "SSE.Controllers.Main.warnLicenseLimitedRenewed": "Необходимо обновить лицензию.<br>У вас ограниченный доступ к функциональности редактирования документов.<br>Пожалуйста, обратитесь к администратору, чтобы получить полный доступ", "SSE.Controllers.Main.warnLicenseUsersExceeded": "Вы достигли лимита на количество пользователей редакторов %1.<br>Свяжитесь с администратором, чтобы узнать больше.", "SSE.Controllers.Main.warnNoLicense": "Вы достигли лимита на одновременные подключения к редакторам %1. Этот документ будет открыт на просмотр.<br>Напишите в отдел продаж %1, чтобы обсудить индивидуальные условия лицензирования.", "SSE.Controllers.Main.warnNoLicenseUsers": "Вы достигли лимита на одновременные подключения к редакторам %1.<br>Напишите в отдел продаж %1, чтобы обсудить индивидуальные условия лицензирования.", "SSE.Controllers.Main.warnProcessRightsChange": "Вам было отказано в праве на редактирование этого файла.", "SSE.Controllers.PivotTable.strSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.PivotTable.txtCalculatedItemInPageField": "Этот элемент нельзя добавить или изменить. Отчет сводной таблицы содержит это поле в фильтрах.", "SSE.Controllers.PivotTable.txtCalculatedItemWarningDefault": "Для этой активной ячейки не разрешены никакие действия с вычисляемыми элементами.", "SSE.Controllers.PivotTable.txtNotUniqueFieldWithCalculated": "Если одна или несколько сводных таблиц содержат вычисляемые элементы, ни одно поле не может использоваться в области данных два или более раз или в области данных и другой области одновременно.", "SSE.Controllers.PivotTable.txtPivotFieldCustomSubtotalsWithCalculatedItems": "Вычисляемые элементы не работают с пользовательскими промежуточными итогами.", "SSE.Controllers.PivotTable.txtPivotItemNameNotFound": "Имя элемента не найдено. Проверьте, что вы правильно ввели имя и элемент присутствует в отчете сводной таблицы.", "SSE.Controllers.PivotTable.txtWrongDataFieldSubtotalForCalculatedItems": "Средние значения, стандартные отклонения и дисперсии не поддерживаются, если отчет сводной таблицы содержит вычисляемые элементы.", "SSE.Controllers.Print.strAllSheets": "Все листы", "SSE.Controllers.Print.textFirstCol": "Первый столбец", "SSE.Controllers.Print.textFirstRow": "Первая строка", "SSE.Controllers.Print.textFrozenCols": "Закрепленные столбцы", "SSE.Controllers.Print.textFrozenRows": "Закрепленные строки", "SSE.Controllers.Print.textInvalidRange": "ОШИБКА! Недопустимый диапазон ячеек", "SSE.Controllers.Print.textNoRepeat": "Не повторять", "SSE.Controllers.Print.textRepeat": "Повторять...", "SSE.Controllers.Print.textSelectRange": "Выбор диапазона", "SSE.Controllers.Print.txtCustom": "Пользовательская", "SSE.Controllers.Print.txtZoomToPage": "По размеру страницы", "SSE.Controllers.Search.textInvalidRange": "ОШИБКА! Недопустимый диапазон ячеек", "SSE.Controllers.Search.textNoTextFound": "Искомые данные не найдены. Пожалуйста, измените параметры поиска.", "SSE.Controllers.Search.textReplaceSkipped": "Замена выполнена. Пропущено вхождений - {0}.", "SSE.Controllers.Search.textReplaceSuccess": "Поиск выполнен. Заменено совпадений: {0}", "SSE.Controllers.Statusbar.errorLastSheet": "Рабочая книга должна содержать не менее одного видимого рабочего листа.", "SSE.Controllers.Statusbar.errorRemoveSheet": "Не удалось удалить лист.", "SSE.Controllers.Statusbar.strSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Statusbar.textDisconnect": "<b>Соединение потеряно</b><br>Попытка подключения. Проверьте настройки подключения.", "SSE.Controllers.Statusbar.textSheetViewTip": "Вы находитесь в режиме представления листа. Фильтры и сортировка видны только вам и тем, кто также находится в этом представлении.", "SSE.Controllers.Statusbar.textSheetViewTipFilters": "Вы находитесь в режиме представления листа. Фильтры видны только вам и тем, кто также находится в этом представлении.", "SSE.Controllers.Statusbar.warnDeleteSheet": "Выбранный рабочий лист может содержать данные. Вы действительно хотите продолжить?", "SSE.Controllers.Statusbar.zoomText": "Мас<PERSON>т<PERSON>б {0}%", "SSE.Controllers.Toolbar.confirmAddFontName": "Шрифт, который вы хотите сохранить, недоступен на этом устройстве.<br>Стиль текста будет отображаться с помощью одного из системных шрифтов. Сохраненный шрифт будет использоваться, когда он станет доступен.<br>Вы хотите продолжить?", "SSE.Controllers.Toolbar.errorComboSeries": "Для создания комбинированной диаграммы выберите не менее двух рядов данных.", "SSE.Controllers.Toolbar.errorMaxPoints": "Максимальное число точек в серии для диаграммы составляет 4096.", "SSE.Controllers.Toolbar.errorMaxRows": "ОШИБКА! Максимальное число рядов данных для одной диаграммы - 255", "SSE.Controllers.Toolbar.errorStockChart": "Неверный порядок строк. Чтобы создать биржевую диаграмму, расположите данные на листе в следующем порядке:<br> цена открытия, максимальная цена, минимальная цена, цена закрытия.", "SSE.Controllers.Toolbar.helpCalcItems": "Работайте с вычисляемыми элементами в сводных таблицах.", "SSE.Controllers.Toolbar.helpCalcItemsHeader": "Вычисляемые элементы", "SSE.Controllers.Toolbar.helpFastUndo": "Легко отменяйте изменения при совместной работе над электронными таблицами в быстром режиме.", "SSE.Controllers.Toolbar.helpFastUndoHeader": "\"Отмена\" при совместном редактировании в реальном времени", "SSE.Controllers.Toolbar.helpMergeShapes": "Мгновенно объединяйте, фрагментируйте, пересекайте и вычитайте фигуры, создавая оригинальные визуальные эффекты.", "SSE.Controllers.Toolbar.helpMergeShapesHeader": "Объединение фигур", "SSE.Controllers.Toolbar.textAccent": "Диакритические знаки", "SSE.Controllers.Toolbar.textBracket": "Скобки", "SSE.Controllers.Toolbar.textDirectional": "Направления", "SSE.Controllers.Toolbar.textFontSizeErr": "Введенное значение некорректно.<br>Введите числовое значение от 1 до 409", "SSE.Controllers.Toolbar.textFraction": "Дроби", "SSE.Controllers.Toolbar.textFunction": "Функции", "SSE.Controllers.Toolbar.textIndicator": "Индикаторы", "SSE.Controllers.Toolbar.textInsert": "Вставить", "SSE.Controllers.Toolbar.textIntegral": "Интегралы", "SSE.Controllers.Toolbar.textLargeOperator": "Крупные операторы", "SSE.Controllers.Toolbar.textLimitAndLog": "Пределы и логарифмы", "SSE.Controllers.Toolbar.textLongOperation": "Длительная операция", "SSE.Controllers.Toolbar.textMatrix": "Матрицы", "SSE.Controllers.Toolbar.textOperator": "Операторы", "SSE.Controllers.Toolbar.textPivot": "Сводная таблица", "SSE.Controllers.Toolbar.textRadical": "Радикалы", "SSE.Controllers.Toolbar.textRating": "Оценки", "SSE.Controllers.Toolbar.textRecentlyUsed": "Последние использованные", "SSE.Controllers.Toolbar.textScript": "Индексы", "SSE.Controllers.Toolbar.textShapes": "Фигуры", "SSE.Controllers.Toolbar.textSymbols": "Символы", "SSE.Controllers.Toolbar.textWarning": "Предупреждение", "SSE.Controllers.Toolbar.txtAccent_Accent": "Ударение", "SSE.Controllers.Toolbar.txtAccent_ArrowD": "Стрелка вправо-влево сверху", "SSE.Controllers.Toolbar.txtAccent_ArrowL": "Стрелка влево сверху", "SSE.Controllers.Toolbar.txtAccent_ArrowR": "Стрелка вправо сверху", "SSE.Controllers.Toolbar.txtAccent_Bar": "Черта", "SSE.Controllers.Toolbar.txtAccent_BarBot": "Черта снизу", "SSE.Controllers.Toolbar.txtAccent_BarTop": "Черта сверху", "SSE.Controllers.Toolbar.txtAccent_BorderBox": "Формула в рамке (с заполнителем)", "SSE.Controllers.Toolbar.txtAccent_BorderBoxCustom": "Формула в рамке (пример)", "SSE.Controllers.Toolbar.txtAccent_Check": "Галочка", "SSE.Controllers.Toolbar.txtAccent_CurveBracketBot": "Фигурная скобка снизу", "SSE.Controllers.Toolbar.txtAccent_CurveBracketTop": "Фигурная скобка сверху", "SSE.Controllers.Toolbar.txtAccent_Custom_1": "Вектор A", "SSE.Controllers.Toolbar.txtAccent_Custom_2": "ABC с чертой сверху", "SSE.Controllers.Toolbar.txtAccent_Custom_3": "x XOR y с чертой сверху", "SSE.Controllers.Toolbar.txtAccent_DDDot": "Три точки", "SSE.Controllers.Toolbar.txtAccent_DDot": "Две точки", "SSE.Controllers.Toolbar.txtAccent_Dot": "Точка", "SSE.Controllers.Toolbar.txtAccent_DoubleBar": "Двойная черта сверху", "SSE.Controllers.Toolbar.txtAccent_Grave": "Тупое ударение", "SSE.Controllers.Toolbar.txtAccent_GroupBot": "Группир<PERSON><PERSON><PERSON>ий знак снизу", "SSE.Controllers.Toolbar.txtAccent_GroupTop": "Группир<PERSON>ю<PERSON>ий знак сверху", "SSE.Controllers.Toolbar.txtAccent_HarpoonL": "Гар<PERSON>ун влево сверху", "SSE.Controllers.Toolbar.txtAccent_HarpoonR": "Гарпун вправо сверху", "SSE.Controllers.Toolbar.txtAccent_Hat": "Крышка", "SSE.Controllers.Toolbar.txtAccent_Smile": "Значок краткости", "SSE.Controllers.Toolbar.txtAccent_Tilde": "Тильда", "SSE.Controllers.Toolbar.txtBracket_Angle": "Угловые скобки", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_2": "Угловые скобки с разделителем", "SSE.Controllers.Toolbar.txtBracket_Angle_Delimiter_3": "Угловые скобки с двумя разделителями", "SSE.Controllers.Toolbar.txtBracket_Angle_NoneOpen": "Правая угловая скобка", "SSE.Controllers.Toolbar.txtBracket_Angle_OpenNone": "Левая угловая скобка", "SSE.Controllers.Toolbar.txtBracket_Curve": "Фигурные скобки", "SSE.Controllers.Toolbar.txtBracket_Curve_Delimiter_2": "Фигурные скобки с разделителем", "SSE.Controllers.Toolbar.txtBracket_Curve_NoneOpen": "Правая фигурная скобка", "SSE.Controllers.Toolbar.txtBracket_Curve_OpenNone": "Левая фигурная скобка", "SSE.Controllers.Toolbar.txtBracket_Custom_1": "Наборы условий (два условия)", "SSE.Controllers.Toolbar.txtBracket_Custom_2": "Наборы условий (три условия)", "SSE.Controllers.Toolbar.txtBracket_Custom_3": "Стопка объектов", "SSE.Controllers.Toolbar.txtBracket_Custom_4": "Стопка объектов в круглых скобках", "SSE.Controllers.Toolbar.txtBracket_Custom_5": "Наборы условий (пример)", "SSE.Controllers.Toolbar.txtBracket_Custom_6": "Биномиальный коэффициент", "SSE.Controllers.Toolbar.txtBracket_Custom_7": "Биномиальный коэффициент в угловых скобках", "SSE.Controllers.Toolbar.txtBracket_Line": "Вертикальные черты", "SSE.Controllers.Toolbar.txtBracket_Line_NoneOpen": "Правая вертикальная черта", "SSE.Controllers.Toolbar.txtBracket_Line_OpenNone": "Левая вертикальная черта", "SSE.Controllers.Toolbar.txtBracket_LineDouble": "Двойные вертикальные черты", "SSE.Controllers.Toolbar.txtBracket_LineDouble_NoneOpen": "Правая двойная вертикальная черта", "SSE.Controllers.Toolbar.txtBracket_LineDouble_OpenNone": "Левая двойная вертикальная черта", "SSE.Controllers.Toolbar.txtBracket_LowLim": "Закрытые снизу скобки", "SSE.Controllers.Toolbar.txtBracket_LowLim_NoneNone": "Правый предельный уровень снизу", "SSE.Controllers.Toolbar.txtBracket_LowLim_OpenNone": "Левый предельный уровень снизу", "SSE.Controllers.Toolbar.txtBracket_Round": "Круглые скобки", "SSE.Controllers.Toolbar.txtBracket_Round_Delimiter_2": "Круглые скобки с разделителем", "SSE.Controllers.Toolbar.txtBracket_Round_NoneOpen": "Правая круглая скобка", "SSE.Controllers.Toolbar.txtBracket_Round_OpenNone": "Левая круглая скобка", "SSE.Controllers.Toolbar.txtBracket_Square": "Квадратные скобки", "SSE.Controllers.Toolbar.txtBracket_Square_CloseClose": "Заполнитель между двумя правыми квадратными скобками", "SSE.Controllers.Toolbar.txtBracket_Square_CloseOpen": "Перевернутые квадратные скобки", "SSE.Controllers.Toolbar.txtBracket_Square_NoneOpen": "Правая квадратная скобка", "SSE.Controllers.Toolbar.txtBracket_Square_OpenNone": "Левая квадратная скобка", "SSE.Controllers.Toolbar.txtBracket_Square_OpenOpen": "Заполнитель между двумя левыми квадратными скобками", "SSE.Controllers.Toolbar.txtBracket_SquareDouble": "Двойные квадратные скобки", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_NoneOpen": "Правая двойная квадратная скобка", "SSE.Controllers.Toolbar.txtBracket_SquareDouble_OpenNone": "Левая двойная квадратная скобка", "SSE.Controllers.Toolbar.txtBracket_UppLim": "Закрытые сверху скобки", "SSE.Controllers.Toolbar.txtBracket_UppLim_NoneOpen": "Правый предельный уровень сверху", "SSE.Controllers.Toolbar.txtBracket_UppLim_OpenNone": "Левый предельный уровень сверху", "SSE.Controllers.Toolbar.txtDeleteCells": "Удалить ячейки", "SSE.Controllers.Toolbar.txtExpand": "Расширить и сортировать", "SSE.Controllers.Toolbar.txtExpandSort": "Данные рядом с выделенным диапазоном не будут отсортированы. Вы хотите расширить выделенный диапазон, чтобы включить данные из смежных ячеек, или продолжить сортировку только выделенного диапазона?", "SSE.Controllers.Toolbar.txtFractionDiagonal": "Диагональная простая дробь", "SSE.Controllers.Toolbar.txtFractionDifferential_1": "dy над dx", "SSE.Controllers.Toolbar.txtFractionDifferential_2": "пересечение дельты y над пересечением дельты x", "SSE.Controllers.Toolbar.txtFractionDifferential_3": "частичная y по частичной x", "SSE.Controllers.Toolbar.txtFractionDifferential_4": "дельта y через дельта x", "SSE.Controllers.Toolbar.txtFractionHorizontal": "Горизонтальная простая дробь", "SSE.Controllers.Toolbar.txtFractionPi_2": "Пи разделить на два", "SSE.Controllers.Toolbar.txtFractionSmall": "Маленькая простая дробь", "SSE.Controllers.Toolbar.txtFractionVertical": "Вертикальная простая дробь", "SSE.Controllers.Toolbar.txtFunction_1_Cos": "Арккосинус", "SSE.Controllers.Toolbar.txtFunction_1_Cosh": "Гиперболический арккосинус", "SSE.Controllers.Toolbar.txtFunction_1_Cot": "Арккотангенс", "SSE.Controllers.Toolbar.txtFunction_1_Coth": "Гиперболический арккотангенс", "SSE.Controllers.Toolbar.txtFunction_1_Csc": "Арккосеканс", "SSE.Controllers.Toolbar.txtFunction_1_Csch": "Гиперболический арккосеканс", "SSE.Controllers.Toolbar.txtFunction_1_Sec": "Арксеканс", "SSE.Controllers.Toolbar.txtFunction_1_Sech": "Гиперболический арксеканс", "SSE.Controllers.Toolbar.txtFunction_1_Sin": "Аркси<PERSON><PERSON>с", "SSE.Controllers.Toolbar.txtFunction_1_Sinh": "Гиперболический арксинус", "SSE.Controllers.Toolbar.txtFunction_1_Tan": "Арк<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_1_Tanh": "Гиперболический арктангенс", "SSE.Controllers.Toolbar.txtFunction_Cos": "Ко<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Cosh": "Гиперболический косинус", "SSE.Controllers.Toolbar.txtFunction_Cot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Coth": "Гиперболический котангенс", "SSE.Controllers.Toolbar.txtFunction_Csc": "Косеканс", "SSE.Controllers.Toolbar.txtFunction_Csch": "Гиперболический косеканс", "SSE.Controllers.Toolbar.txtFunction_Custom_1": "Sin θ", "SSE.Controllers.Toolbar.txtFunction_Custom_2": "Cos 2x", "SSE.Controllers.Toolbar.txtFunction_Custom_3": "Формула тангенса", "SSE.Controllers.Toolbar.txtFunction_Sec": "Секанс", "SSE.Controllers.Toolbar.txtFunction_Sech": "Гиперболический секанс", "SSE.Controllers.Toolbar.txtFunction_Sin": "Син<PERSON>с", "SSE.Controllers.Toolbar.txtFunction_Sinh": "Гиперболический синус", "SSE.Controllers.Toolbar.txtFunction_Tan": "Та<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtFunction_Tanh": "Гиперболический тангенс", "SSE.Controllers.Toolbar.txtGroupCell_Custom": "Пользовательские", "SSE.Controllers.Toolbar.txtGroupCell_DataAndModel": "Данные и модель", "SSE.Controllers.Toolbar.txtGroupCell_GoodBadAndNeutral": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, плохой и нейтральный", "SSE.Controllers.Toolbar.txtGroupCell_NoName": "Без имени", "SSE.Controllers.Toolbar.txtGroupCell_NumberFormat": "Числовой формат", "SSE.Controllers.Toolbar.txtGroupCell_ThemedCallStyles": "Стили ячеек с темой", "SSE.Controllers.Toolbar.txtGroupCell_TitlesAndHeadings": "Названия и заголовки", "SSE.Controllers.Toolbar.txtGroupTable_Custom": "Пользовательские", "SSE.Controllers.Toolbar.txtGroupTable_Dark": "Темные", "SSE.Controllers.Toolbar.txtGroupTable_Light": "Светлые", "SSE.Controllers.Toolbar.txtGroupTable_Medium": "Средние", "SSE.Controllers.Toolbar.txtInsertCells": "Вставить ячейки", "SSE.Controllers.Toolbar.txtIntegral": "Интеграл", "SSE.Controllers.Toolbar.txtIntegral_dtheta": "Дифференциал dθ", "SSE.Controllers.Toolbar.txtIntegral_dx": "Дифференциал dx", "SSE.Controllers.Toolbar.txtIntegral_dy": "Ди<PERSON><PERSON>еренциал dy", "SSE.Controllers.Toolbar.txtIntegralCenterSubSup": "Интеграл с пределами с накоплением", "SSE.Controllers.Toolbar.txtIntegralDouble": "Двойной интеграл", "SSE.Controllers.Toolbar.txtIntegralDoubleCenterSubSup": "Двойной интеграл с пределами с накоплением", "SSE.Controllers.Toolbar.txtIntegralDoubleSubSup": "Двойной интеграл с пределами", "SSE.Controllers.Toolbar.txtIntegralOriented": "Контурный интеграл", "SSE.Controllers.Toolbar.txtIntegralOrientedCenterSubSup": "Контурный интеграл с пределами с накоплением", "SSE.Controllers.Toolbar.txtIntegralOrientedDouble": "Интеграл по поверхности", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleCenterSubSup": "Интеграл по поверхности с пределами с накоплением", "SSE.Controllers.Toolbar.txtIntegralOrientedDoubleSubSup": "Интеграл по поверхности с пределами", "SSE.Controllers.Toolbar.txtIntegralOrientedSubSup": "Контурный интеграл с пределами", "SSE.Controllers.Toolbar.txtIntegralOrientedTriple": "Интеграл по объему", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleCenterSubSup": "Интеграл по объему с пределами с накоплением", "SSE.Controllers.Toolbar.txtIntegralOrientedTripleSubSup": "Интеграл по объему с пределами", "SSE.Controllers.Toolbar.txtIntegralSubSup": "Интеграл с пределами", "SSE.Controllers.Toolbar.txtIntegralTriple": "Тройной интеграл", "SSE.Controllers.Toolbar.txtIntegralTripleCenterSubSup": "Тройной интеграл с пределами с накоплением", "SSE.Controllers.Toolbar.txtIntegralTripleSubSup": "Тройной интеграл с пределами", "SSE.Controllers.Toolbar.txtInvalidRange": "ОШИБКА! Недопустимый диапазон ячеек", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction": "Логическое И", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSub": "Логическое И с нижним пределом", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_CenterSubSup": "Логическое И с пределами", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_Sub": "Логическое И с нижним пределом подстрочного знака", "SSE.Controllers.Toolbar.txtLargeOperator_Conjunction_SubSup": "Логическое И с пределом подстрочного/надстрочного знака", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd": "Сопроизведение", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSub": "Сопроизведение с нижним пределом", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_CenterSubSup": "Сопроизведение с пределами", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_Sub": "Сопроизведение с нижним пределом подстрочного знака", "SSE.Controllers.Toolbar.txtLargeOperator_CoProd_SubSup": "Сопроизведение с пределами подстрочного/надстрочного знака", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_1": "Суммирование от k от n с выбором k", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_2": "Суммирование от i равно ноль до n", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_3": "Пример суммирования с использованием двух индексов", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_4": "Пример произведения", "SSE.Controllers.Toolbar.txtLargeOperator_Custom_5": "Пример объединения", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction": "Логическое Или", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSub": "Логическое ИЛИ с нижним пределом", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_CenterSubSup": "Логическое ИЛИ с пределами", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_Sub": "Логическое ИЛИ с нижним пределом подстрочного знака", "SSE.Controllers.Toolbar.txtLargeOperator_Disjunction_SubSup": "Логическое ИЛИ с пределами подстрочного/надстрочного знака", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection": "Пересечение", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSub": "Пересечение с нижним пределом", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_CenterSubSup": "Пересечение с пределами", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_Sub": "Пересечение с нижним пределом в виде подстрочного знака", "SSE.Controllers.Toolbar.txtLargeOperator_Intersection_SubSup": "Пересечение с пределами подстрочного/надстрочного знака", "SSE.Controllers.Toolbar.txtLargeOperator_Prod": "Произведение", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSub": "Произведение с нижним пределом", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_CenterSubSup": "Произведение с пределами", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_Sub": "Произведение с нижним пределом подстрочного знака", "SSE.Controllers.Toolbar.txtLargeOperator_Prod_SubSup": "Произведение с пределами подстрочного/надстрочного знака", "SSE.Controllers.Toolbar.txtLargeOperator_Sum": "Сумма", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSub": "Суммирование с нижним пределом", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_CenterSubSup": "Суммирование с пределами", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_Sub": "Суммирование с нижним пределом подстрочного знака", "SSE.Controllers.Toolbar.txtLargeOperator_Sum_SubSup": "Суммирование с пределами подстрочного/надстрочного знака", "SSE.Controllers.Toolbar.txtLargeOperator_Union": "Объединение", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSub": "Объединение с нижним пределом", "SSE.Controllers.Toolbar.txtLargeOperator_Union_CenterSubSup": "Объединение с пределами", "SSE.Controllers.Toolbar.txtLargeOperator_Union_Sub": "Объединение с нижним пределом подстрочного знака", "SSE.Controllers.Toolbar.txtLargeOperator_Union_SubSup": "Объединение с пределами подстрочного/надстрочного знака", "SSE.Controllers.Toolbar.txtLimitLog_Custom_1": "Пример предела", "SSE.Controllers.Toolbar.txtLimitLog_Custom_2": "Пример максимума", "SSE.Controllers.Toolbar.txtLimitLog_Lim": "Предел", "SSE.Controllers.Toolbar.txtLimitLog_Ln": "Натуральный логарифм", "SSE.Controllers.Toolbar.txtLimitLog_Log": "Лог<PERSON><PERSON><PERSON><PERSON>м", "SSE.Controllers.Toolbar.txtLimitLog_LogBase": "Лог<PERSON><PERSON><PERSON><PERSON>м", "SSE.Controllers.Toolbar.txtLimitLog_Max": "Максимум", "SSE.Controllers.Toolbar.txtLimitLog_Min": "Мини<PERSON>ум", "SSE.Controllers.Toolbar.txtLockSort": "Обнаружены данные рядом с выделенным диапазоном, но у вас недостаточно прав для изменения этих ячеек.<br>Вы хотите продолжить работу с выделенным диапазоном?", "SSE.Controllers.Toolbar.txtMatrix_1_2": "Пустая матрица 1 x 2", "SSE.Controllers.Toolbar.txtMatrix_1_3": "Пустая матрица 1 x 3", "SSE.Controllers.Toolbar.txtMatrix_2_1": "Пустая матрица 2 x 1", "SSE.Controllers.Toolbar.txtMatrix_2_2": "Пустая матрица 2 x 2", "SSE.Controllers.Toolbar.txtMatrix_2_2_DLineBracket": "Пустая матрица 2 х 2 в двойных вертикальных чертах", "SSE.Controllers.Toolbar.txtMatrix_2_2_LineBracket": "Пустой определитель 2 x 2", "SSE.Controllers.Toolbar.txtMatrix_2_2_RoundBracket": "Пустая матрица 2 х 2 в круглых скобках", "SSE.Controllers.Toolbar.txtMatrix_2_2_SquareBracket": "Пустая матрица 2 х 2 в скобках", "SSE.Controllers.Toolbar.txtMatrix_2_3": "Пустая матрица 2 x 3", "SSE.Controllers.Toolbar.txtMatrix_3_1": "Пустая матрица 3 x 1", "SSE.Controllers.Toolbar.txtMatrix_3_2": "Пустая матрица 3 x 2", "SSE.Controllers.Toolbar.txtMatrix_3_3": "Пустая матрица 3 x 3", "SSE.Controllers.Toolbar.txtMatrix_Dots_Baseline": "Точки на опорной линии", "SSE.Controllers.Toolbar.txtMatrix_Dots_Center": "Точки посередине", "SSE.Controllers.Toolbar.txtMatrix_Dots_Diagonal": "Точки по диагонали", "SSE.Controllers.Toolbar.txtMatrix_Dots_Vertical": "Точки по вертикали", "SSE.Controllers.Toolbar.txtMatrix_Flat_Round": "Разреженная матрица в круглых скобках", "SSE.Controllers.Toolbar.txtMatrix_Flat_Square": "Разреженная матрица в квадратных скобках", "SSE.Controllers.Toolbar.txtMatrix_Identity_2": "Единичная матрица 2 x 2 с нулями", "SSE.Controllers.Toolbar.txtMatrix_Identity_2_NoZeros": "Единичная матрица 2 x 2 с пустыми ячейками не на диагонали", "SSE.Controllers.Toolbar.txtMatrix_Identity_3": "Единичная матрица 3 x 3 с нулями", "SSE.Controllers.Toolbar.txtMatrix_Identity_3_NoZeros": "Единичная матрица 3 x 3 с пустыми ячейками не на диагонали", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Bot": "Стрелка вправо-влево снизу", "SSE.Controllers.Toolbar.txtOperator_ArrowD_Top": "Стрелка вправо-влево сверху", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Bot": "Стрелка влево снизу", "SSE.Controllers.Toolbar.txtOperator_ArrowL_Top": "Стрелка влево сверху", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Bot": "Стрелка вправо снизу", "SSE.Controllers.Toolbar.txtOperator_ArrowR_Top": "Стрелка вправо сверху", "SSE.Controllers.Toolbar.txtOperator_ColonEquals": "Двоеточие равно", "SSE.Controllers.Toolbar.txtOperator_Custom_1": "Выход", "SSE.Controllers.Toolbar.txtOperator_Custom_2": "Дельта выхода", "SSE.Controllers.Toolbar.txtOperator_Definition": "Равно по определению", "SSE.Controllers.Toolbar.txtOperator_DeltaEquals": "Дельта равна", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Bot": "Двойная стрелка вправо-влево снизу", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowD_Top": "Двойная стрелка вправо-влево сверху", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Bot": "Стрелка влево снизу", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowL_Top": "Стрелка влево сверху", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Bot": "Стрелка вправо снизу", "SSE.Controllers.Toolbar.txtOperator_DoubleArrowR_Top": "Стрелка вправо сверху", "SSE.Controllers.Toolbar.txtOperator_EqualsEquals": "Равно равно", "SSE.Controllers.Toolbar.txtOperator_MinusEquals": "Минус равно", "SSE.Controllers.Toolbar.txtOperator_PlusEquals": "Плюс равно", "SSE.Controllers.Toolbar.txtOperator_UnitOfMeasure": "Единица измерения", "SSE.Controllers.Toolbar.txtRadicalCustom_1": "Правая часть квадратного уравнения", "SSE.Controllers.Toolbar.txtRadicalCustom_2": "Квадратный корень из квадрата плюс b квадрат", "SSE.Controllers.Toolbar.txtRadicalRoot_2": "Квадратный корень со степенью", "SSE.Controllers.Toolbar.txtRadicalRoot_3": "Кубический корень", "SSE.Controllers.Toolbar.txtRadicalRoot_n": "Радикал со степенью", "SSE.Controllers.Toolbar.txtRadicalSqrt": "Квадратный корень", "SSE.Controllers.Toolbar.txtScriptCustom_1": "x в степени квадрата y", "SSE.Controllers.Toolbar.txtScriptCustom_2": "e в степени -iωt", "SSE.Controllers.Toolbar.txtScriptCustom_3": "квадрат x", "SSE.Controllers.Toolbar.txtScriptCustom_4": "Y, надстрочный индекс n слева, подстрочный индекс 1 справа", "SSE.Controllers.Toolbar.txtScriptSub": "Нижний индекс", "SSE.Controllers.Toolbar.txtScriptSubSup": "Нижний и верхний индексы", "SSE.Controllers.Toolbar.txtScriptSubSupLeft": "Нижний и верхний индексы слева", "SSE.Controllers.Toolbar.txtScriptSup": "Верхний индекс", "SSE.Controllers.Toolbar.txtSorting": "Сортировка", "SSE.Controllers.Toolbar.txtSortSelected": "Сортировать выделенное", "SSE.Controllers.Toolbar.txtSymbol_about": "Приблизительно", "SSE.Controllers.Toolbar.txtSymbol_additional": "Дополнение", "SSE.Controllers.Toolbar.txtSymbol_aleph": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_alpha": "Альфа", "SSE.Controllers.Toolbar.txtSymbol_approx": "Почти равно", "SSE.Controllers.Toolbar.txtSymbol_ast": "Оператор-звездочка", "SSE.Controllers.Toolbar.txtSymbol_beta": "Бета", "SSE.Controllers.Toolbar.txtSymbol_beth": "Бет", "SSE.Controllers.Toolbar.txtSymbol_bullet": "Оператор-маркер", "SSE.Controllers.Toolbar.txtSymbol_cap": "Пересечение", "SSE.Controllers.Toolbar.txtSymbol_cbrt": "Кубический корень", "SSE.Controllers.Toolbar.txtSymbol_cdots": "Горизонтальное многоточие посередине", "SSE.Controllers.Toolbar.txtSymbol_celsius": "Градусы Цельсия", "SSE.Controllers.Toolbar.txtSymbol_chi": "Хи", "SSE.Controllers.Toolbar.txtSymbol_cong": "Приблизительно равно", "SSE.Controllers.Toolbar.txtSymbol_cup": "Объединение", "SSE.Controllers.Toolbar.txtSymbol_ddots": "Диагональное многоточие вниз вправо", "SSE.Controllers.Toolbar.txtSymbol_degree": "<PERSON>ра<PERSON><PERSON><PERSON>ы", "SSE.Controllers.Toolbar.txtSymbol_delta": "Дельта", "SSE.Controllers.Toolbar.txtSymbol_div": "Знак деления", "SSE.Controllers.Toolbar.txtSymbol_downarrow": "Стрелка вниз", "SSE.Controllers.Toolbar.txtSymbol_emptyset": "Пустое множество", "SSE.Controllers.Toolbar.txtSymbol_epsilon": "Эп<PERSON>илон", "SSE.Controllers.Toolbar.txtSymbol_equals": "Равно", "SSE.Controllers.Toolbar.txtSymbol_equiv": "Тождественно", "SSE.Controllers.Toolbar.txtSymbol_eta": "Эта", "SSE.Controllers.Toolbar.txtSymbol_exists": "Существует", "SSE.Controllers.Toolbar.txtSymbol_factorial": "Факториал", "SSE.Controllers.Toolbar.txtSymbol_fahrenheit": "Градусы Фаренгейта", "SSE.Controllers.Toolbar.txtSymbol_forall": "Для всех", "SSE.Controllers.Toolbar.txtSymbol_gamma": "Гамма", "SSE.Controllers.Toolbar.txtSymbol_geq": "Больше или равно", "SSE.Controllers.Toolbar.txtSymbol_gg": "Много больше", "SSE.Controllers.Toolbar.txtSymbol_greater": "Больше", "SSE.Controllers.Toolbar.txtSymbol_in": "Является элементом", "SSE.Controllers.Toolbar.txtSymbol_inc": "Приращение", "SSE.Controllers.Toolbar.txtSymbol_infinity": "Бесконечность", "SSE.Controllers.Toolbar.txtSymbol_iota": "Йота", "SSE.Controllers.Toolbar.txtSymbol_kappa": "Каппа", "SSE.Controllers.Toolbar.txtSymbol_lambda": "Лямбда", "SSE.Controllers.Toolbar.txtSymbol_leftarrow": "Стрелка влево", "SSE.Controllers.Toolbar.txtSymbol_leftrightarrow": "Стрелка влево и вправо", "SSE.Controllers.Toolbar.txtSymbol_leq": "Меньше или равно", "SSE.Controllers.Toolbar.txtSymbol_less": "Меньше", "SSE.Controllers.Toolbar.txtSymbol_ll": "Много меньше", "SSE.Controllers.Toolbar.txtSymbol_minus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Controllers.Toolbar.txtSymbol_mp": "Минус и плюс", "SSE.Controllers.Toolbar.txtSymbol_mu": "Мю", "SSE.Controllers.Toolbar.txtSymbol_nabla": "Набла", "SSE.Controllers.Toolbar.txtSymbol_neq": "Не равно", "SSE.Controllers.Toolbar.txtSymbol_ni": "Содер<PERSON><PERSON>т как член", "SSE.Controllers.Toolbar.txtSymbol_not": "Знак отрицания", "SSE.Controllers.Toolbar.txtSymbol_notexists": "Не существует", "SSE.Controllers.Toolbar.txtSymbol_nu": "Ню", "SSE.Controllers.Toolbar.txtSymbol_o": "Омикрон", "SSE.Controllers.Toolbar.txtSymbol_omega": "Омега", "SSE.Controllers.Toolbar.txtSymbol_partial": "Частный дифференциал", "SSE.Controllers.Toolbar.txtSymbol_percent": "Процент", "SSE.Controllers.Toolbar.txtSymbol_phi": "Фи", "SSE.Controllers.Toolbar.txtSymbol_pi": "Пи", "SSE.Controllers.Toolbar.txtSymbol_plus": "Плюс", "SSE.Controllers.Toolbar.txtSymbol_pm": "Плюс и минус", "SSE.Controllers.Toolbar.txtSymbol_propto": "Пропорционально", "SSE.Controllers.Toolbar.txtSymbol_psi": "Пси", "SSE.Controllers.Toolbar.txtSymbol_qdrt": "Корень четвертой степени", "SSE.Controllers.Toolbar.txtSymbol_qed": "Что и требовалось доказать", "SSE.Controllers.Toolbar.txtSymbol_rddots": "Диагональное многоточие вверх вправо", "SSE.Controllers.Toolbar.txtSymbol_rho": "Ро", "SSE.Controllers.Toolbar.txtSymbol_rightarrow": "Стрелка вправо", "SSE.Controllers.Toolbar.txtSymbol_sigma": "Сигма", "SSE.Controllers.Toolbar.txtSymbol_sqrt": "Знак радикала", "SSE.Controllers.Toolbar.txtSymbol_tau": "Тау", "SSE.Controllers.Toolbar.txtSymbol_therefore": "Следовательно", "SSE.Controllers.Toolbar.txtSymbol_theta": "Тета", "SSE.Controllers.Toolbar.txtSymbol_times": "Знак умножения", "SSE.Controllers.Toolbar.txtSymbol_uparrow": "Стрелка вверх", "SSE.Controllers.Toolbar.txtSymbol_upsilon": "Ипсилон", "SSE.Controllers.Toolbar.txtSymbol_varepsilon": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (вариант)", "SSE.Controllers.Toolbar.txtSymbol_varphi": "Фи (вариант)", "SSE.Controllers.Toolbar.txtSymbol_varpi": "Пи (вариант)", "SSE.Controllers.Toolbar.txtSymbol_varrho": "Ро (вариант)", "SSE.Controllers.Toolbar.txtSymbol_varsigma": "Сигма (вариант)", "SSE.Controllers.Toolbar.txtSymbol_vartheta": "Тета (вариант)", "SSE.Controllers.Toolbar.txtSymbol_vdots": "Вертикальное многоточие", "SSE.Controllers.Toolbar.txtSymbol_xsi": "Кси", "SSE.Controllers.Toolbar.txtSymbol_zeta": "Дзета", "SSE.Controllers.Toolbar.txtTable_TableStyleDark": "Стиль таблицы: темный", "SSE.Controllers.Toolbar.txtTable_TableStyleLight": "Стиль таблицы: светлый", "SSE.Controllers.Toolbar.txtTable_TableStyleMedium": "Стиль таблицы: средний", "SSE.Controllers.Toolbar.warnLongOperation": "Для завершения операции, которую вы собираетесь выполнить, может потребоваться довольно много времени.<br>Вы действительно хотите продолжить?", "SSE.Controllers.Toolbar.warnMergeLostData": "В объединенной ячейке останутся только данные из левой верхней ячейки.<br>Вы действительно хотите продолжить?", "SSE.Controllers.Toolbar.warnNoRecommended": "Чтобы создать диаграмму, выделите ячейки с данными, которые вы хотите использовать.<br>Если у строк и столбцов есть заголовки и вы хотите использовать их в качестве меток, включите их в выделение.", "SSE.Controllers.Viewport.textFreezePanes": "Закрепить области", "SSE.Controllers.Viewport.textFreezePanesShadow": "Показывать тень для закрепленных областей", "SSE.Controllers.Viewport.textHideFBar": "Скрыть строку формул", "SSE.Controllers.Viewport.textHideGridlines": "Скрыть линии сетки", "SSE.Controllers.Viewport.textHideHeadings": "Скрыть заголовки", "SSE.Views.AdvancedSeparatorDialog.strDecimalSeparator": "Десятичный разделитель", "SSE.Views.AdvancedSeparatorDialog.strThousandsSeparator": "Разделитель разрядов тысяч", "SSE.Views.AdvancedSeparatorDialog.textLabel": "Настройка определения числовых данных", "SSE.Views.AdvancedSeparatorDialog.textQualifier": "Классификатор текста", "SSE.Views.AdvancedSeparatorDialog.textTitle": "Дополнительные параметры", "SSE.Views.AdvancedSeparatorDialog.txtNone": "(нет)", "SSE.Views.AutoFilterDialog.btnCustomFilter": "Пользовательский", "SSE.Views.AutoFilterDialog.textAddSelection": "Добавить выделенный фрагмент в фильтр", "SSE.Views.AutoFilterDialog.textEmptyItem": "{Пустые}", "SSE.Views.AutoFilterDialog.textSelectAll": "Выделить всё", "SSE.Views.AutoFilterDialog.textSelectAllResults": "Выделить все результаты поиска", "SSE.Views.AutoFilterDialog.textWarning": "Предупреждение", "SSE.Views.AutoFilterDialog.txtAboveAve": "Выше среднего", "SSE.Views.AutoFilterDialog.txtAfter": "После...", "SSE.Views.AutoFilterDialog.txtAllDatesInThePeriod": "Все даты периода", "SSE.Views.AutoFilterDialog.txtApril": "Апрель", "SSE.Views.AutoFilterDialog.txtAugust": "Август", "SSE.Views.AutoFilterDialog.txtBefore": "Перед...", "SSE.Views.AutoFilterDialog.txtBegins": "Начинается с...", "SSE.Views.AutoFilterDialog.txtBelowAve": "Ниже среднего", "SSE.Views.AutoFilterDialog.txtBetween": "Между...", "SSE.Views.AutoFilterDialog.txtClear": "Очистить", "SSE.Views.AutoFilterDialog.txtContains": "Содержит...", "SSE.Views.AutoFilterDialog.txtDateFilter": "Фильтр дат", "SSE.Views.AutoFilterDialog.txtDecember": "Декабрь", "SSE.Views.AutoFilterDialog.txtEmpty": "Введите значение для фильтрации", "SSE.Views.AutoFilterDialog.txtEnds": "Оканчивается на...", "SSE.Views.AutoFilterDialog.txtEquals": "Равно...", "SSE.Views.AutoFilterDialog.txtFebruary": "Февраль", "SSE.Views.AutoFilterDialog.txtFilterCellColor": "Фильтр по цвету ячеек", "SSE.Views.AutoFilterDialog.txtFilterFontColor": "Фильтр по цвету шрифта", "SSE.Views.AutoFilterDialog.txtGreater": "Больше...", "SSE.Views.AutoFilterDialog.txtGreaterEquals": "Больше или равно...", "SSE.Views.AutoFilterDialog.txtJanuary": "Январь", "SSE.Views.AutoFilterDialog.txtJuly": "Июль", "SSE.Views.AutoFilterDialog.txtJune": "Июнь", "SSE.Views.AutoFilterDialog.txtLabelFilter": "Фильтр подписей", "SSE.Views.AutoFilterDialog.txtLastMonth": "Прошлый месяц", "SSE.Views.AutoFilterDialog.txtLastQuarter": "Прошлый квартал", "SSE.Views.AutoFilterDialog.txtLastWeek": "Прошлая неделя", "SSE.Views.AutoFilterDialog.txtLastYear": "Прошлый год", "SSE.Views.AutoFilterDialog.txtLess": "Меньше...", "SSE.Views.AutoFilterDialog.txtLessEquals": "Меньше или равно...", "SSE.Views.AutoFilterDialog.txtMarch": "Ма<PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtMay": "<PERSON><PERSON><PERSON>", "SSE.Views.AutoFilterDialog.txtNextMonth": "Следующий месяц", "SSE.Views.AutoFilterDialog.txtNextQuarter": "Следующий квартал", "SSE.Views.AutoFilterDialog.txtNextWeek": "Следующая неделя", "SSE.Views.AutoFilterDialog.txtNextYear": "Следующий год", "SSE.Views.AutoFilterDialog.txtNotBegins": "Не начинается с...", "SSE.Views.AutoFilterDialog.txtNotBetween": "Не между...", "SSE.Views.AutoFilterDialog.txtNotContains": "Не содержит...", "SSE.Views.AutoFilterDialog.txtNotEnds": "Не оканчивается на...", "SSE.Views.AutoFilterDialog.txtNotEquals": "Не равно...", "SSE.Views.AutoFilterDialog.txtNovember": "Ноябрь", "SSE.Views.AutoFilterDialog.txtNumFilter": "Числовой фильтр", "SSE.Views.AutoFilterDialog.txtOctober": "Октябрь", "SSE.Views.AutoFilterDialog.txtQuarter1": "Квартал 1", "SSE.Views.AutoFilterDialog.txtQuarter2": "Квартал 1", "SSE.Views.AutoFilterDialog.txtQuarter3": "Квартал 1", "SSE.Views.AutoFilterDialog.txtQuarter4": "Квартал 1", "SSE.Views.AutoFilterDialog.txtReapply": "Применить повторно", "SSE.Views.AutoFilterDialog.txtSeptember": "Сентябрь", "SSE.Views.AutoFilterDialog.txtSortCellColor": "Сортировка по цвету ячеек", "SSE.Views.AutoFilterDialog.txtSortFontColor": "Сортировка по цвету шрифта", "SSE.Views.AutoFilterDialog.txtSortHigh2Low": "Сортировка по убыванию", "SSE.Views.AutoFilterDialog.txtSortLow2High": "Сортировка по возрастанию", "SSE.Views.AutoFilterDialog.txtSortOption": "Дополнительные параметры сортировки...", "SSE.Views.AutoFilterDialog.txtTextFilter": "Текстовый фильтр", "SSE.Views.AutoFilterDialog.txtThisMonth": "Этот месяц", "SSE.Views.AutoFilterDialog.txtThisQuarter": "Этот квартал", "SSE.Views.AutoFilterDialog.txtThisWeek": "Эта неделя", "SSE.Views.AutoFilterDialog.txtThisYear": "Этот год", "SSE.Views.AutoFilterDialog.txtTitle": "Фильтр", "SSE.Views.AutoFilterDialog.txtToday": "Сегодня", "SSE.Views.AutoFilterDialog.txtTomorrow": "Завтра", "SSE.Views.AutoFilterDialog.txtTop10": "Первые 10", "SSE.Views.AutoFilterDialog.txtValueFilter": "Фильтр значений", "SSE.Views.AutoFilterDialog.txtYearToDate": "С начала года", "SSE.Views.AutoFilterDialog.txtYesterday": "Вчера", "SSE.Views.AutoFilterDialog.warnFilterError": "Чтобы применить фильтр по значению, область значений должна содержать хотя бы одно поле.", "SSE.Views.AutoFilterDialog.warnNoSelected": "Необходимо выбрать хотя бы одно значение", "SSE.Views.CellEditor.textManager": "Диспетчер имен", "SSE.Views.CellEditor.tipFormula": "Вставить функцию", "SSE.Views.CellRangeDialog.errorMaxRows": "ОШИБКА! Максимальное число рядов данных для одной диаграммы - 255", "SSE.Views.CellRangeDialog.errorStockChart": "Неверный порядок строк. Чтобы создать биржевую диаграмму, расположите данные на листе в следующем порядке:<br> цена открытия, максимальная цена, минимальная цена, цена закрытия.", "SSE.Views.CellRangeDialog.txtEmpty": "Это поле обязательно для заполнения", "SSE.Views.CellRangeDialog.txtInvalidRange": "ОШИБКА! Недопустимый диапазон ячеек", "SSE.Views.CellRangeDialog.txtTitle": "Выбор диапазона данных", "SSE.Views.CellSettings.strShrink": "Автоподбор ширины", "SSE.Views.CellSettings.strWrap": "Перенос текста", "SSE.Views.CellSettings.textAngle": "Угол", "SSE.Views.CellSettings.textBackColor": "Цвет фона", "SSE.Views.CellSettings.textBackground": "Цвет фона", "SSE.Views.CellSettings.textBorderColor": "Цвет", "SSE.Views.CellSettings.textBorders": "Стиль границ", "SSE.Views.CellSettings.textClearRule": "Удалить правила", "SSE.Views.CellSettings.textColor": "Заливка цветом", "SSE.Views.CellSettings.textColorScales": "Цветовые шкалы", "SSE.Views.CellSettings.textCondFormat": "Условное форматирование", "SSE.Views.CellSettings.textControl": "Отображение", "SSE.Views.CellSettings.textDataBars": "Гистограммы", "SSE.Views.CellSettings.textDirection": "Направление", "SSE.Views.CellSettings.textFill": "Заливка", "SSE.Views.CellSettings.textForeground": "Цвет переднего плана", "SSE.Views.CellSettings.textGradient": "Точки градиента", "SSE.Views.CellSettings.textGradientColor": "Цвет", "SSE.Views.CellSettings.textGradientFill": "Градиентная заливка", "SSE.Views.CellSettings.textIndent": "Отступ", "SSE.Views.CellSettings.textItems": "Элементы", "SSE.Views.CellSettings.textLinear": "Линейный", "SSE.Views.CellSettings.textManageRule": "Управление правилами", "SSE.Views.CellSettings.textNewRule": "Новое правило", "SSE.Views.CellSettings.textNoFill": "Без заливки", "SSE.Views.CellSettings.textOrientation": "Ориентация текста", "SSE.Views.CellSettings.textPattern": "Узор", "SSE.Views.CellSettings.textPatternFill": "Узор", "SSE.Views.CellSettings.textPosition": "Положение", "SSE.Views.CellSettings.textRadial": "Радиальный", "SSE.Views.CellSettings.textSelectBorders": "Выберите границы, к которым надо применить выбранный стиль", "SSE.Views.CellSettings.textSelection": "Из текущего выделенного фрагмента", "SSE.Views.CellSettings.textThisPivot": "Из этой сводной таблицы", "SSE.Views.CellSettings.textThisSheet": "Из этого листа", "SSE.Views.CellSettings.textThisTable": "Из этой таблицы", "SSE.Views.CellSettings.tipAddGradientPoint": "Добавить точку градиента", "SSE.Views.CellSettings.tipAll": "Задать внешнюю границу и все внутренние линии", "SSE.Views.CellSettings.tipBottom": "Задать только внешнюю нижнюю границу", "SSE.Views.CellSettings.tipDiagD": "Задать диагональную границу сверху вниз", "SSE.Views.CellSettings.tipDiagU": "Задать диагональную границу снизу вверх", "SSE.Views.CellSettings.tipInner": "Задать только внутренние линии", "SSE.Views.CellSettings.tipInnerHor": "Задать только горизонтальные внутренние линии", "SSE.Views.CellSettings.tipInnerVert": "Задать только вертикальные внутренние линии", "SSE.Views.CellSettings.tipLeft": "Задать только внешнюю левую границу", "SSE.Views.CellSettings.tipNone": "Не задавать границ", "SSE.Views.CellSettings.tipOuter": "Задать только внешнюю границу", "SSE.Views.CellSettings.tipRemoveGradientPoint": "Удалить точку градиента", "SSE.Views.CellSettings.tipRight": "Задать только внешнюю правую границу", "SSE.Views.CellSettings.tipTop": "Задать только внешнюю верхнюю границу", "SSE.Views.ChartDataDialog.errorInFormula": "Ошибка во введенной формуле.", "SSE.Views.ChartDataDialog.errorInvalidReference": "Недопустимая ссылка. Ссылка должна указывать на открытый лист.", "SSE.Views.ChartDataDialog.errorMaxPoints": "Максимальное число точек в серии для диаграммы составляет 4096.", "SSE.Views.ChartDataDialog.errorMaxRows": "Максимальное число рядов данных для одной диаграммы: 255.", "SSE.Views.ChartDataDialog.errorNoSingleRowCol": "Недопустимая ссылка. Ссылка для названий, значений, размеров или меток данных должна указывать на одну ячейку, строку или столбец.", "SSE.Views.ChartDataDialog.errorNoValues": "Для создания диаграммы необходимо, чтобы ряд содержал хотя бы одно значение.", "SSE.Views.ChartDataDialog.errorStockChart": "Неверный порядок строк. Чтобы создать биржевую диаграмму, расположите данные на листе в следующем порядке:<br> цена открытия, максимальная цена, минимальная цена, цена закрытия.", "SSE.Views.ChartDataDialog.textAdd": "Добавить", "SSE.Views.ChartDataDialog.textCategory": "Подписи горизонтальной оси (категории)", "SSE.Views.ChartDataDialog.textData": "Диапазон данных для диаграммы", "SSE.Views.ChartDataDialog.textDelete": "Удалить", "SSE.Views.ChartDataDialog.textDown": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartDataDialog.textEdit": "Редактировать", "SSE.Views.ChartDataDialog.textInvalidRange": "Недопустимый диапазон ячеек", "SSE.Views.ChartDataDialog.textSelectData": "Выбор данных", "SSE.Views.ChartDataDialog.textSeries": "Элементы легенды (ряды)", "SSE.Views.ChartDataDialog.textSwitch": "Переключить строку/столбец", "SSE.Views.ChartDataDialog.textTitle": "Данные диаграммы", "SSE.Views.ChartDataDialog.textUp": "Ввер<PERSON>", "SSE.Views.ChartDataRangeDialog.errorInFormula": "Ошибка во введенной формуле.", "SSE.Views.ChartDataRangeDialog.errorInvalidReference": "Недопустимая ссылка. Ссылка должна указывать на открытый лист.", "SSE.Views.ChartDataRangeDialog.errorMaxPoints": "Максимальное число точек в серии для диаграммы составляет 4096.", "SSE.Views.ChartDataRangeDialog.errorMaxRows": "Максимальное число рядов данных для одной диаграммы: 255.", "SSE.Views.ChartDataRangeDialog.errorNoSingleRowCol": "Недопустимая ссылка. Ссылка для названий, значений, размеров или меток данных должна указывать на одну ячейку, строку или столбец.", "SSE.Views.ChartDataRangeDialog.errorNoValues": "Для создания диаграммы необходимо, чтобы ряд содержал хотя бы одно значение.", "SSE.Views.ChartDataRangeDialog.errorStockChart": "Неверный порядок строк. Чтобы создать биржевую диаграмму, расположите данные на листе в следующем порядке:<br> цена открытия, максимальная цена, минимальная цена, цена закрытия.", "SSE.Views.ChartDataRangeDialog.textInvalidRange": "Недопустимый диапазон ячеек", "SSE.Views.ChartDataRangeDialog.textSelectData": "Выбор данных", "SSE.Views.ChartDataRangeDialog.txtAxisLabel": "Диапазон подписей оси", "SSE.Views.ChartDataRangeDialog.txtChoose": "Выберите диапазон", "SSE.Views.ChartDataRangeDialog.txtSeriesName": "Имя ряда", "SSE.Views.ChartDataRangeDialog.txtTitleCategory": "Подписи оси", "SSE.Views.ChartDataRangeDialog.txtTitleSeries": "Изменить ряд", "SSE.Views.ChartDataRangeDialog.txtValues": "Значения", "SSE.Views.ChartDataRangeDialog.txtXValues": "Значения Х", "SSE.Views.ChartDataRangeDialog.txtYValues": "Значения Y", "SSE.Views.ChartSettings.errorMaxRows": "Максимальное число рядов данных для одной диаграммы: 255.", "SSE.Views.ChartSettings.strLineWeight": "Толщина линии", "SSE.Views.ChartSettings.strSparkColor": "Цвет", "SSE.Views.ChartSettings.strTemplate": "Шабл<PERSON>н", "SSE.Views.ChartSettings.text3dDepth": "Глубина (% от базовой)", "SSE.Views.ChartSettings.text3dHeight": "Высота (% от базовой)", "SSE.Views.ChartSettings.text3dRotation": "Трехмерный поворот", "SSE.Views.ChartSettings.textAdvanced": "Дополнительные параметры", "SSE.Views.ChartSettings.textAutoscale": "Автомасштабирование", "SSE.Views.ChartSettings.textBorderSizeErr": "Введено некорректное значение.<br>Пожалуйста, введите значение от 0 до 1584 пунктов.", "SSE.Views.ChartSettings.textChangeType": "Изменить тип", "SSE.Views.ChartSettings.textChartType": "Изменить тип диаграммы", "SSE.Views.ChartSettings.textDefault": "Поворот по умолчанию", "SSE.Views.ChartSettings.textDown": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textEditData": "Изменить данные и место", "SSE.Views.ChartSettings.textFirstPoint": "Первая точка", "SSE.Views.ChartSettings.textHeight": "Высота", "SSE.Views.ChartSettings.textHighPoint": "Максимальная точка", "SSE.Views.ChartSettings.textKeepRatio": "Сохранять пропорции", "SSE.Views.ChartSettings.textLastPoint": "Последняя точка", "SSE.Views.ChartSettings.textLeft": "Влево", "SSE.Views.ChartSettings.textLowPoint": "Минимальная точка", "SSE.Views.ChartSettings.textMarkers": "Маркеры", "SSE.Views.ChartSettings.textNarrow": "Сузить поле зрения", "SSE.Views.ChartSettings.textNegativePoint": "Отрицательная точка", "SSE.Views.ChartSettings.textPerspective": "Перспектива", "SSE.Views.ChartSettings.textRanges": "Диапазон данных", "SSE.Views.ChartSettings.textRight": "Вправо", "SSE.Views.ChartSettings.textRightAngle": "Оси под прямым углом", "SSE.Views.ChartSettings.textSelectData": "Выбор данных", "SSE.Views.ChartSettings.textShow": "Показать", "SSE.Views.ChartSettings.textSize": "Размер", "SSE.Views.ChartSettings.textStyle": "Стиль", "SSE.Views.ChartSettings.textSwitch": "Переключить строку/столбец", "SSE.Views.ChartSettings.textType": "Тип", "SSE.Views.ChartSettings.textUp": "Ввер<PERSON>", "SSE.Views.ChartSettings.textWiden": "Расширить поле зрения", "SSE.Views.ChartSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettings.textX": "По оси X", "SSE.Views.ChartSettings.textY": "По оси Y", "SSE.Views.ChartSettingsDlg.errorMaxPoints": "ОШИБКА! Максимальное число точек в серии для диаграммы составляет 4096.", "SSE.Views.ChartSettingsDlg.errorMaxRows": "ОШИБКА! Максимальное число рядов данных для одной диаграммы - 255", "SSE.Views.ChartSettingsDlg.errorStockChart": "Неверный порядок строк. Чтобы создать биржевую диаграмму, расположите данные на листе в следующем порядке:<br> цена открытия, максимальная цена, минимальная цена, цена закрытия.", "SSE.Views.ChartSettingsDlg.textAbsolute": "Не перемещать и не изменять размеры вместе с ячейками", "SSE.Views.ChartSettingsDlg.textAlt": "Альтернативный текст", "SSE.Views.ChartSettingsDlg.textAltDescription": "Описание", "SSE.Views.ChartSettingsDlg.textAltTip": "Альтернативное текстовое представление информации о визуальном объекте, которое будет зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит изображение, фигура, диаграмма или таблица.", "SSE.Views.ChartSettingsDlg.textAltTitle": "Заголовок", "SSE.Views.ChartSettingsDlg.textAuto": "Авто", "SSE.Views.ChartSettingsDlg.textAutoEach": "Автоматическое для каждого", "SSE.Views.ChartSettingsDlg.textAxisCrosses": "Пересечение с осью", "SSE.Views.ChartSettingsDlg.textAxisOptions": "Параметры оси", "SSE.Views.ChartSettingsDlg.textAxisPos": "Положение оси", "SSE.Views.ChartSettingsDlg.textAxisSettings": "Параметры оси", "SSE.Views.ChartSettingsDlg.textAxisTitle": "Заголовок", "SSE.Views.ChartSettingsDlg.textBase": "Базовый", "SSE.Views.ChartSettingsDlg.textBetweenTickMarks": "Между делениями", "SSE.Views.ChartSettingsDlg.textBillions": "Милл<PERSON><PERSON><PERSON><PERSON>ы", "SSE.Views.ChartSettingsDlg.textBottom": "Снизу", "SSE.Views.ChartSettingsDlg.textCategoryName": "Название категории", "SSE.Views.ChartSettingsDlg.textCenter": "По центру", "SSE.Views.ChartSettingsDlg.textChartElementsLegend": "Элементы диаграммы и<br/>легенда диаграммы", "SSE.Views.ChartSettingsDlg.textChartTitle": "Заголовок диаграммы", "SSE.Views.ChartSettingsDlg.textCross": "На пересечении", "SSE.Views.ChartSettingsDlg.textCustom": "Пользовательский", "SSE.Views.ChartSettingsDlg.textDataColumns": "в столбцах", "SSE.Views.ChartSettingsDlg.textDataLabels": "Подписи данных", "SSE.Views.ChartSettingsDlg.textDataRows": "в строках", "SSE.Views.ChartSettingsDlg.textDisplayLegend": "Показывать легенду", "SSE.Views.ChartSettingsDlg.textEmptyCells": "Скрытые и пустые ячейки", "SSE.Views.ChartSettingsDlg.textEmptyLine": "Соединять точки данных линиями", "SSE.Views.ChartSettingsDlg.textFit": "По ширине", "SSE.Views.ChartSettingsDlg.textFixed": "Фиксированное", "SSE.Views.ChartSettingsDlg.textFormat": "Формат подписи", "SSE.Views.ChartSettingsDlg.textGaps": "Пустые значения", "SSE.Views.ChartSettingsDlg.textGridLines": "Линии сетки", "SSE.Views.ChartSettingsDlg.textGroup": "Группа спар<PERSON><PERSON><PERSON><PERSON><PERSON>ов", "SSE.Views.ChartSettingsDlg.textHide": "Скрыть", "SSE.Views.ChartSettingsDlg.textHideAxis": "Скрыть ось", "SSE.Views.ChartSettingsDlg.textHigh": "Выше", "SSE.Views.ChartSettingsDlg.textHorAxis": "Горизонтальная ось", "SSE.Views.ChartSettingsDlg.textHorAxisSec": "Вспомогательная горизонтальная ось", "SSE.Views.ChartSettingsDlg.textHorizontal": "По горизонтали", "SSE.Views.ChartSettingsDlg.textHundredMil": "100 000 000", "SSE.Views.ChartSettingsDlg.textHundreds": "Сотни", "SSE.Views.ChartSettingsDlg.textHundredThousands": "100 000", "SSE.Views.ChartSettingsDlg.textIn": "Внутри", "SSE.Views.ChartSettingsDlg.textInnerBottom": "Внутри снизу", "SSE.Views.ChartSettingsDlg.textInnerTop": "Внутри сверху", "SSE.Views.ChartSettingsDlg.textInvalidRange": "ОШИБКА! Недопустимый диапазон ячеек", "SSE.Views.ChartSettingsDlg.textLabelDist": "Расстояние до подписи", "SSE.Views.ChartSettingsDlg.textLabelInterval": "Интервал между подписями", "SSE.Views.ChartSettingsDlg.textLabelOptions": "Параметры подписи", "SSE.Views.ChartSettingsDlg.textLabelPos": "Положение подписи", "SSE.Views.ChartSettingsDlg.textLayout": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.ChartSettingsDlg.textLeft": "Слева", "SSE.Views.ChartSettingsDlg.textLeftOverlay": "Наложение слева", "SSE.Views.ChartSettingsDlg.textLegendBottom": "Снизу", "SSE.Views.ChartSettingsDlg.textLegendLeft": "Слева", "SSE.Views.ChartSettingsDlg.textLegendPos": "Условные обозначения", "SSE.Views.ChartSettingsDlg.textLegendRight": "Справа", "SSE.Views.ChartSettingsDlg.textLegendTop": "Сверху", "SSE.Views.ChartSettingsDlg.textLines": "Линии", "SSE.Views.ChartSettingsDlg.textLocationRange": "Диапазон расположения:", "SSE.Views.ChartSettingsDlg.textLogScale": "Логарифмическая шкала", "SSE.Views.ChartSettingsDlg.textLow": "Ниже", "SSE.Views.ChartSettingsDlg.textMajor": "Основные", "SSE.Views.ChartSettingsDlg.textMajorMinor": "Основные и дополнительные", "SSE.Views.ChartSettingsDlg.textMajorType": "Основной тип", "SSE.Views.ChartSettingsDlg.textManual": "Вручн<PERSON>ю", "SSE.Views.ChartSettingsDlg.textMarkers": "Маркеры", "SSE.Views.ChartSettingsDlg.textMarksInterval": "Интервал между делениями", "SSE.Views.ChartSettingsDlg.textMaxValue": "Максимум", "SSE.Views.ChartSettingsDlg.textMillions": "Миллионы", "SSE.Views.ChartSettingsDlg.textMinor": "Дополнительные", "SSE.Views.ChartSettingsDlg.textMinorType": "Дополнительный тип", "SSE.Views.ChartSettingsDlg.textMinValue": "Мини<PERSON>ум", "SSE.Views.ChartSettingsDlg.textNextToAxis": "Рядом с осью", "SSE.Views.ChartSettingsDlg.textNone": "Нет", "SSE.Views.ChartSettingsDlg.textNoOverlay": "Без наложения", "SSE.Views.ChartSettingsDlg.textOneCell": "Перемещать, но не изменять размеры вместе с ячейками", "SSE.Views.ChartSettingsDlg.textOnTickMarks": "Деления", "SSE.Views.ChartSettingsDlg.textOut": "Снаружи", "SSE.Views.ChartSettingsDlg.textOuterTop": "Снаружи сверху", "SSE.Views.ChartSettingsDlg.textOverlay": "Наложение", "SSE.Views.ChartSettingsDlg.textReverse": "Значения в обратном порядке", "SSE.Views.ChartSettingsDlg.textReverseOrder": "В обратном порядке", "SSE.Views.ChartSettingsDlg.textRight": "Справа", "SSE.Views.ChartSettingsDlg.textRightOverlay": "Наложение справа", "SSE.Views.ChartSettingsDlg.textRotated": "Повернутое", "SSE.Views.ChartSettingsDlg.textSameAll": "Одинаковое для всех", "SSE.Views.ChartSettingsDlg.textSelectData": "Выбор данных", "SSE.Views.ChartSettingsDlg.textSeparator": "Разделитель подписей данных", "SSE.Views.ChartSettingsDlg.textSeriesName": "Имя ряда", "SSE.Views.ChartSettingsDlg.textShow": "Показать", "SSE.Views.ChartSettingsDlg.textShowBorders": "Показывать границы диаграммы", "SSE.Views.ChartSettingsDlg.textShowData": "Показывать данные в скрытых строках и столбцах", "SSE.Views.ChartSettingsDlg.textShowEmptyCells": "Показывать пустые ячейки как", "SSE.Views.ChartSettingsDlg.textShowEquation": "Показывать уравнение на диаграмме", "SSE.Views.ChartSettingsDlg.textShowSparkAxis": "Показывать ось", "SSE.Views.ChartSettingsDlg.textShowValues": "Показывать значения диаграммы", "SSE.Views.ChartSettingsDlg.textSingle": "Отдельный спарклайн", "SSE.Views.ChartSettingsDlg.textSmooth": "Сглаженные", "SSE.Views.ChartSettingsDlg.textSnap": "Привязка к ячейке", "SSE.Views.ChartSettingsDlg.textSparkRanges": "Диапазоны спарклайнов", "SSE.Views.ChartSettingsDlg.textStraight": "Прямые", "SSE.Views.ChartSettingsDlg.textStyle": "Стиль", "SSE.Views.ChartSettingsDlg.textTenMillions": "10 000 000", "SSE.Views.ChartSettingsDlg.textTenThousands": "10 000", "SSE.Views.ChartSettingsDlg.textThousands": "Тыся<PERSON>и", "SSE.Views.ChartSettingsDlg.textTickOptions": "Параметры делений", "SSE.Views.ChartSettingsDlg.textTitle": "Диаграмма - дополнительные параметры", "SSE.Views.ChartSettingsDlg.textTitleSparkline": "Спарклайн - дополнительные параметры", "SSE.Views.ChartSettingsDlg.textTop": "Сверху", "SSE.Views.ChartSettingsDlg.textTrendlineOptions": "Параметры линии тренда", "SSE.Views.ChartSettingsDlg.textTrillions": "Триллионы", "SSE.Views.ChartSettingsDlg.textTwoCell": "Перемещать и изменять размеры вместе с ячейками", "SSE.Views.ChartSettingsDlg.textType": "Тип", "SSE.Views.ChartSettingsDlg.textTypeData": "Тип и данные", "SSE.Views.ChartSettingsDlg.textUnits": "Единицы отображения", "SSE.Views.ChartSettingsDlg.textValue": "Значение", "SSE.Views.ChartSettingsDlg.textVertAxis": "Вертикальная ось", "SSE.Views.ChartSettingsDlg.textVertAxisSec": "Вспомогательная вертикальная ось", "SSE.Views.ChartSettingsDlg.textXAxisTitle": "Название оси X", "SSE.Views.ChartSettingsDlg.textYAxisTitle": "Название оси Y", "SSE.Views.ChartSettingsDlg.textZero": "Нулевые значения", "SSE.Views.ChartSettingsDlg.txtEmpty": "Это поле необходимо заполнить", "SSE.Views.ChartTypeDialog.errorComboSeries": "Для создания комбинированной диаграммы выберите не менее двух рядов данных.", "SSE.Views.ChartTypeDialog.errorSecondaryAxis": "Для выбранного типа диаграммы требуется вспомогательная ось, которая используется в существующей диаграмме. Выберите другой тип диаграммы.", "SSE.Views.ChartTypeDialog.textSecondary": "Вспомогательная ось", "SSE.Views.ChartTypeDialog.textSeries": "<PERSON>яд", "SSE.Views.ChartTypeDialog.textStyle": "Стиль", "SSE.Views.ChartTypeDialog.textTitle": "Тип диаграммы", "SSE.Views.ChartTypeDialog.textType": "Тип", "SSE.Views.ChartWizardDialog.errorComboSeries": "Для создания комбинированной диаграммы выберите не менее двух рядов данных.", "SSE.Views.ChartWizardDialog.errorMaxPoints": "Максимальное число точек в серии для диаграммы составляет 4096.", "SSE.Views.ChartWizardDialog.errorMaxRows": "Максимальное число рядов данных для одной диаграммы: 255.", "SSE.Views.ChartWizardDialog.errorSecondaryAxis": "Для выбранного типа диаграммы требуется вспомогательная ось, которая используется в существующей диаграмме. Выберите другой тип диаграммы.", "SSE.Views.ChartWizardDialog.errorStockChart": "Неверный порядок строк. Чтобы создать биржевую диаграмму, расположите данные на листе в следующем порядке: цена открытия, максимальная цена, минимальная цена, цена закрытия.", "SSE.Views.ChartWizardDialog.textRecommended": "Рекомендованные", "SSE.Views.ChartWizardDialog.textSecondary": "Вспомогательная ось", "SSE.Views.ChartWizardDialog.textSeries": "<PERSON>яд", "SSE.Views.ChartWizardDialog.textTitle": "Вставить диаграмму", "SSE.Views.ChartWizardDialog.textTitleChange": "Изменить тип диаграммы", "SSE.Views.ChartWizardDialog.textType": "Тип", "SSE.Views.ChartWizardDialog.txtSeriesDesc": "Выберите тип диаграммы и ось для ряда данных", "SSE.Views.CreatePivotDialog.textDataRange": "Диапазон исходных данных", "SSE.Views.CreatePivotDialog.textDestination": "Выберите, где разместить таблицу", "SSE.Views.CreatePivotDialog.textExist": "Существующий лист", "SSE.Views.CreatePivotDialog.textInvalidRange": "Недопустимый диапазон ячеек", "SSE.Views.CreatePivotDialog.textNew": "Новый лист", "SSE.Views.CreatePivotDialog.textSelectData": "Выбор данных", "SSE.Views.CreatePivotDialog.textTitle": "Создать сводную таблицу", "SSE.Views.CreatePivotDialog.txtEmpty": "Это поле необходимо заполнить", "SSE.Views.CreateSparklineDialog.textDataRange": "Диапазон исходных данных", "SSE.Views.CreateSparklineDialog.textDestination": "Выберите, где поместить спарклайны", "SSE.Views.CreateSparklineDialog.textInvalidRange": "Недопустимый диапазон ячеек", "SSE.Views.CreateSparklineDialog.textSelectData": "Выбор данных", "SSE.Views.CreateSparklineDialog.textTitle": "Создание спарклайнов", "SSE.Views.CreateSparklineDialog.txtEmpty": "Это поле необходимо заполнить", "SSE.Views.DataTab.capBtnGroup": "Сгруппировать", "SSE.Views.DataTab.capBtnTextCustomSort": "Настраиваемая сортировка", "SSE.Views.DataTab.capBtnTextDataValidation": "Проверка данных", "SSE.Views.DataTab.capBtnTextRemDuplicates": "Удалить дубликаты", "SSE.Views.DataTab.capBtnTextToCol": "Текст по столбцам", "SSE.Views.DataTab.capBtnUngroup": "Разгруппировать", "SSE.Views.DataTab.capDataExternalLinks": "Внешние ссылки", "SSE.Views.DataTab.capDataFromText": "Получить данные", "SSE.Views.DataTab.capGoalSeek": "Подбор параметра", "SSE.Views.DataTab.mniFromFile": "Из локального TXT/CSV файла", "SSE.Views.DataTab.mniFromUrl": "По URL TXT/CSV файла", "SSE.Views.DataTab.mniFromXMLFile": "Из локальной XML", "SSE.Views.DataTab.textBelow": "Итоги в строках под данными", "SSE.Views.DataTab.textClear": "Удалить структуру", "SSE.Views.DataTab.textColumns": "Разгруппировать столбцы", "SSE.Views.DataTab.textGroupColumns": "Сгруппировать столбцы", "SSE.Views.DataTab.textGroupRows": "Сгруппировать строки", "SSE.Views.DataTab.textRightOf": "Итоги в столбцах справа от данных", "SSE.Views.DataTab.textRows": "Разгруппировать строки", "SSE.Views.DataTab.tipCustomSort": "Настраиваемая сортировка", "SSE.Views.DataTab.tipDataFromText": "Получить данные из файла", "SSE.Views.DataTab.tipDataValidation": "Проверка данных", "SSE.Views.DataTab.tipExternalLinks": "Посмотреть другие файлы, с которыми связана эта таблица", "SSE.Views.DataTab.tipGoalSeek": "Поиск правильных входных данных для нужного значения", "SSE.Views.DataTab.tipGroup": "Сгруппировать диапазон ячеек", "SSE.Views.DataTab.tipRemDuplicates": "Удалить повторяющиеся строки с листа", "SSE.Views.DataTab.tipToColumns": "Разделить текст ячейки по столбцам", "SSE.Views.DataTab.tipUngroup": "Разгруппировать диапазон ячеек", "SSE.Views.DataValidationDialog.errorFormula": "При вычислении значения возникает ошибка. Вы хотите продолжить?", "SSE.Views.DataValidationDialog.errorInvalid": "В поле \"{0}\" введено недопустимое значение.", "SSE.Views.DataValidationDialog.errorInvalidDate": "В поле \"{0}\" введена недопустимая дата.", "SSE.Views.DataValidationDialog.errorInvalidList": "Источник списка должен быть списком с разделителями или ссылкой на одну строку или столбец.", "SSE.Views.DataValidationDialog.errorInvalidTime": "В поле \"{0}\" введено недопустимое время.", "SSE.Views.DataValidationDialog.errorMinGreaterMax": "Значение поля \"{1}\" должно быть больше или равно значению поля \"{0}\".", "SSE.Views.DataValidationDialog.errorMustEnterBothValues": "Необходимо ввести значение и в поле \"{0}\", и в поле \"{1}\".", "SSE.Views.DataValidationDialog.errorMustEnterValue": "Необходимо ввести значение в поле \"{0}\".", "SSE.Views.DataValidationDialog.errorNamedRange": "Указанный именованный диапазон не найден.", "SSE.Views.DataValidationDialog.errorNegativeTextLength": "В условиях \"{0}\" нельзя использовать отрицательные значения.", "SSE.Views.DataValidationDialog.errorNotNumeric": "Поле \"{0}\" должно содержать числовое значение, численное выражение или ссылку на ячейку с числовым значением.", "SSE.Views.DataValidationDialog.strError": "Сообщение об ошибке", "SSE.Views.DataValidationDialog.strInput": "Подсказка по вводу", "SSE.Views.DataValidationDialog.strSettings": "Настройки", "SSE.Views.DataValidationDialog.textAlert": "Предупреждение", "SSE.Views.DataValidationDialog.textAllow": "Разрешить", "SSE.Views.DataValidationDialog.textApply": "Распространить изменения на все другие ячейки с тем же условием", "SSE.Views.DataValidationDialog.textCellSelected": "При выборе ячейки отображать следующую подсказку", "SSE.Views.DataValidationDialog.textCompare": "Сравнить с", "SSE.Views.DataValidationDialog.textData": "Данные", "SSE.Views.DataValidationDialog.textEndDate": "Дата окончания", "SSE.Views.DataValidationDialog.textEndTime": "Время окончания", "SSE.Views.DataValidationDialog.textError": "Сообщение об ошибке", "SSE.Views.DataValidationDialog.textFormula": "Формула", "SSE.Views.DataValidationDialog.textIgnore": "Игнорировать пустые ячейки", "SSE.Views.DataValidationDialog.textInput": "Подсказка по вводу", "SSE.Views.DataValidationDialog.textMax": "Максимум", "SSE.Views.DataValidationDialog.textMessage": "Сообщение", "SSE.Views.DataValidationDialog.textMin": "Мини<PERSON>ум", "SSE.Views.DataValidationDialog.textSelectData": "Выбор данных", "SSE.Views.DataValidationDialog.textShowDropDown": "Показывать раскрывающийся список в ячейке", "SSE.Views.DataValidationDialog.textShowError": "Выводить сообщение об ошибке", "SSE.Views.DataValidationDialog.textShowInput": "Отображать подсказку, если ячейка является текущей", "SSE.Views.DataValidationDialog.textSource": "Источник", "SSE.Views.DataValidationDialog.textStartDate": "Дата начала", "SSE.Views.DataValidationDialog.textStartTime": "Время начала", "SSE.Views.DataValidationDialog.textStop": "Стоп", "SSE.Views.DataValidationDialog.textStyle": "Стиль", "SSE.Views.DataValidationDialog.textTitle": "Заголовок", "SSE.Views.DataValidationDialog.textUserEnters": "При попытке ввода недопустимых данных отображать сообщение", "SSE.Views.DataValidationDialog.txtAny": "Любое значение", "SSE.Views.DataValidationDialog.txtBetween": "между", "SSE.Views.DataValidationDialog.txtDate": "Дата", "SSE.Views.DataValidationDialog.txtDecimal": "Десятичное число", "SSE.Views.DataValidationDialog.txtElTime": "Прошло времени", "SSE.Views.DataValidationDialog.txtEndDate": "Дата окончания", "SSE.Views.DataValidationDialog.txtEndTime": "Время окончания", "SSE.Views.DataValidationDialog.txtEqual": "равно", "SSE.Views.DataValidationDialog.txtGreaterThan": "больше", "SSE.Views.DataValidationDialog.txtGreaterThanOrEqual": "больше или равно", "SSE.Views.DataValidationDialog.txtLength": "Длина", "SSE.Views.DataValidationDialog.txtLessThan": "меньше", "SSE.Views.DataValidationDialog.txtLessThanOrEqual": "меньше или равно", "SSE.Views.DataValidationDialog.txtList": "Список", "SSE.Views.DataValidationDialog.txtNotBetween": "не между", "SSE.Views.DataValidationDialog.txtNotEqual": "не равно", "SSE.Views.DataValidationDialog.txtOther": "Другое", "SSE.Views.DataValidationDialog.txtStartDate": "Дата начала", "SSE.Views.DataValidationDialog.txtStartTime": "Время начала", "SSE.Views.DataValidationDialog.txtTextLength": "<PERSON><PERSON><PERSON>на текста", "SSE.Views.DataValidationDialog.txtTime": "Время", "SSE.Views.DataValidationDialog.txtWhole": "Целое число", "SSE.Views.DigitalFilterDialog.capAnd": "И", "SSE.Views.DigitalFilterDialog.capCondition1": "равно", "SSE.Views.DigitalFilterDialog.capCondition10": "не заканчивается на", "SSE.Views.DigitalFilterDialog.capCondition11": "содер<PERSON><PERSON>т", "SSE.Views.DigitalFilterDialog.capCondition12": "не содержит", "SSE.Views.DigitalFilterDialog.capCondition2": "не равно", "SSE.Views.DigitalFilterDialog.capCondition3": "больше чем", "SSE.Views.DigitalFilterDialog.capCondition30": "после", "SSE.Views.DigitalFilterDialog.capCondition4": "больше или равно", "SSE.Views.DigitalFilterDialog.capCondition40": "после или равно", "SSE.Views.DigitalFilterDialog.capCondition5": "меньше чем", "SSE.Views.DigitalFilterDialog.capCondition50": "до", "SSE.Views.DigitalFilterDialog.capCondition6": "меньше или равно", "SSE.Views.DigitalFilterDialog.capCondition60": "до или равно", "SSE.Views.DigitalFilterDialog.capCondition7": "начинается с", "SSE.Views.DigitalFilterDialog.capCondition8": "не начинается с", "SSE.Views.DigitalFilterDialog.capCondition9": "заканчивается на", "SSE.Views.DigitalFilterDialog.capOr": "Или", "SSE.Views.DigitalFilterDialog.textNoFilter": "без фильтра", "SSE.Views.DigitalFilterDialog.textShowRows": "Показать строки, в которых", "SSE.Views.DigitalFilterDialog.textUse1": "Используйте знак ? вместо любого отдельного символа", "SSE.Views.DigitalFilterDialog.textUse2": "Используйте знак * вместо любой последовательности символов", "SSE.Views.DigitalFilterDialog.txtSelectDate": "Выберите дату", "SSE.Views.DigitalFilterDialog.txtTitle": "Пользовательский фильтр", "SSE.Views.DocumentHolder.advancedEquationText": "Параметры уравнений", "SSE.Views.DocumentHolder.advancedImgText": "Дополнительные параметры изображения", "SSE.Views.DocumentHolder.advancedShapeText": "Дополнительные параметры фигуры", "SSE.Views.DocumentHolder.advancedSlicerText": "Дополнительные параметры среза", "SSE.Views.DocumentHolder.allLinearText": "Все - линейный", "SSE.Views.DocumentHolder.allProfText": "Все - профессиональный", "SSE.Views.DocumentHolder.bottomCellText": "По нижнему краю", "SSE.Views.DocumentHolder.bulletsText": "Маркеры и нумерация", "SSE.Views.DocumentHolder.centerCellText": "По середине", "SSE.Views.DocumentHolder.chartDataText": "Выбор данных для построения диаграммы", "SSE.Views.DocumentHolder.chartText": "Дополнительные параметры диаграммы", "SSE.Views.DocumentHolder.chartTypeText": "Изменить тип диаграммы", "SSE.Views.DocumentHolder.currLinearText": "Текущее - линейный", "SSE.Views.DocumentHolder.currProfText": "Текущее - профессиональный", "SSE.Views.DocumentHolder.deleteColumnText": "Столбец", "SSE.Views.DocumentHolder.deleteRowText": "Строку", "SSE.Views.DocumentHolder.deleteTableText": "Табли<PERSON>у", "SSE.Views.DocumentHolder.direct270Text": "Повернуть текст вверх", "SSE.Views.DocumentHolder.direct90Text": "Повернуть текст вниз", "SSE.Views.DocumentHolder.directHText": "Горизонтальное", "SSE.Views.DocumentHolder.directionText": "Направление текста", "SSE.Views.DocumentHolder.editChartText": "Изменить данные", "SSE.Views.DocumentHolder.editHyperlinkText": "Изменить гиперссылку", "SSE.Views.DocumentHolder.hideEqToolbar": "Скрыть панель уравнений", "SSE.Views.DocumentHolder.insertColumnLeftText": "Столбец слева", "SSE.Views.DocumentHolder.insertColumnRightText": "Столбец справа", "SSE.Views.DocumentHolder.insertRowAboveText": "Строку выше", "SSE.Views.DocumentHolder.insertRowBelowText": "Строку ниже", "SSE.Views.DocumentHolder.latexText": "LaTeX", "SSE.Views.DocumentHolder.originalSizeText": "Реальный размер", "SSE.Views.DocumentHolder.removeHyperlinkText": "Удалить гиперссылку", "SSE.Views.DocumentHolder.selectColumnText": "Весь столбец", "SSE.Views.DocumentHolder.selectDataText": "Данные столбцов", "SSE.Views.DocumentHolder.selectRowText": "Строку", "SSE.Views.DocumentHolder.selectTableText": "Табли<PERSON>у", "SSE.Views.DocumentHolder.showEqToolbar": "Показать панель уравнений", "SSE.Views.DocumentHolder.strDelete": "Удалить подпись", "SSE.Views.DocumentHolder.strDetails": "Состав подписи", "SSE.Views.DocumentHolder.strSetup": "Настройка подписи", "SSE.Views.DocumentHolder.strSign": "Подписать", "SSE.Views.DocumentHolder.textAlign": "Выравнивание", "SSE.Views.DocumentHolder.textArrange": "Порядок", "SSE.Views.DocumentHolder.textArrangeBack": "Перенести на задний план", "SSE.Views.DocumentHolder.textArrangeBackward": "Перенести назад", "SSE.Views.DocumentHolder.textArrangeForward": "Перенести вперед", "SSE.Views.DocumentHolder.textArrangeFront": "Перенести на передний план", "SSE.Views.DocumentHolder.textAverage": "Среднее", "SSE.Views.DocumentHolder.textBullets": "Маркеры", "SSE.Views.DocumentHolder.textCopyCells": "Копировать ячейки", "SSE.Views.DocumentHolder.textCount": "Количество", "SSE.Views.DocumentHolder.textCrop": "Обрезать", "SSE.Views.DocumentHolder.textCropFill": "Заливка", "SSE.Views.DocumentHolder.textCropFit": "Вписать", "SSE.Views.DocumentHolder.textEditPoints": "Изменить точки", "SSE.Views.DocumentHolder.textEntriesList": "Выбрать из списка", "SSE.Views.DocumentHolder.textFillDays": "Заполнить по дням", "SSE.Views.DocumentHolder.textFillFormatOnly": "Заполнить только форматы", "SSE.Views.DocumentHolder.textFillMonths": "Заполнить по месяцам", "SSE.Views.DocumentHolder.textFillSeries": "Прогрессия", "SSE.Views.DocumentHolder.textFillWeekdays": "Заполнить по рабочим дням", "SSE.Views.DocumentHolder.textFillWithoutFormat": "Заполнить только значения", "SSE.Views.DocumentHolder.textFillYears": "Заполнить по годам", "SSE.Views.DocumentHolder.textFlashFill": "Мгновенное заполнение", "SSE.Views.DocumentHolder.textFlipH": "Отразить слева направо", "SSE.Views.DocumentHolder.textFlipV": "Отразить сверху вниз", "SSE.Views.DocumentHolder.textFreezePanes": "Закрепить области", "SSE.Views.DocumentHolder.textFromFile": "Из файла", "SSE.Views.DocumentHolder.textFromStorage": "Из хранилища", "SSE.Views.DocumentHolder.textFromUrl": "По URL", "SSE.Views.DocumentHolder.textGrowthTrend": "Экспоненциальное приближение", "SSE.Views.DocumentHolder.textLinearTrend": "Линейное приближение", "SSE.Views.DocumentHolder.textListSettings": "Параметры списка", "SSE.Views.DocumentHolder.textMacro": "Назначить макрос", "SSE.Views.DocumentHolder.textMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.DocumentHolder.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.textMore": "Другие функции", "SSE.Views.DocumentHolder.textMoreFormats": "Другие форматы", "SSE.Views.DocumentHolder.textNone": "Нет", "SSE.Views.DocumentHolder.textNumbering": "Нумерация", "SSE.Views.DocumentHolder.textReplace": "Заменить изображение", "SSE.Views.DocumentHolder.textResetCrop": "Сбросить обрезку", "SSE.Views.DocumentHolder.textRotate": "Поворот", "SSE.Views.DocumentHolder.textRotate270": "Повернуть на 90° против часовой стрелки", "SSE.Views.DocumentHolder.textRotate90": "Повернуть на 90° по часовой стрелке", "SSE.Views.DocumentHolder.textSaveAsPicture": "Сохранить как рисунок", "SSE.Views.DocumentHolder.textSeries": "Прогрессия", "SSE.Views.DocumentHolder.textShapeAlignBottom": "Выровнять по нижнему краю", "SSE.Views.DocumentHolder.textShapeAlignCenter": "Выровнять по центру", "SSE.Views.DocumentHolder.textShapeAlignLeft": "Выровнять по левому краю", "SSE.Views.DocumentHolder.textShapeAlignMiddle": "Выровнять по середине", "SSE.Views.DocumentHolder.textShapeAlignRight": "Выровнять по правому краю", "SSE.Views.DocumentHolder.textShapeAlignTop": "Выровнять по верхнему краю", "SSE.Views.DocumentHolder.textShapesMerge": "Объединить фигуры", "SSE.Views.DocumentHolder.textStdDev": "Стандотклон", "SSE.Views.DocumentHolder.textSum": "Сумма", "SSE.Views.DocumentHolder.textUndo": "Отменить", "SSE.Views.DocumentHolder.textUnFreezePanes": "Снять закрепление областей", "SSE.Views.DocumentHolder.textVar": "Ди<PERSON><PERSON>", "SSE.Views.DocumentHolder.tipMarkersArrow": "Маркеры-стрелки", "SSE.Views.DocumentHolder.tipMarkersCheckmark": "Маркеры-галочки", "SSE.Views.DocumentHolder.tipMarkersDash": "Маркеры-тире", "SSE.Views.DocumentHolder.tipMarkersFRhombus": "Заполненные ромбовидные маркеры", "SSE.Views.DocumentHolder.tipMarkersFRound": "Заполненные круглые маркеры", "SSE.Views.DocumentHolder.tipMarkersFSquare": "Заполненные квадратные маркеры", "SSE.Views.DocumentHolder.tipMarkersHRound": "Пустые круглые маркеры", "SSE.Views.DocumentHolder.tipMarkersStar": "Маркеры-звездочки", "SSE.Views.DocumentHolder.topCellText": "По верхнему краю", "SSE.Views.DocumentHolder.txtAccounting": "Финансовый", "SSE.Views.DocumentHolder.txtAddComment": "Добавить комментарий", "SSE.Views.DocumentHolder.txtAddNamedRange": "Присвоить имя", "SSE.Views.DocumentHolder.txtArrange": "Порядок", "SSE.Views.DocumentHolder.txtAscending": "По возрастанию", "SSE.Views.DocumentHolder.txtAutoColumnWidth": "Автоподбор ширины столбца", "SSE.Views.DocumentHolder.txtAutoRowHeight": "Автоподбор высоты строки", "SSE.Views.DocumentHolder.txtAverage": "Среднее", "SSE.Views.DocumentHolder.txtCellFormat": "Форматировать ячейки", "SSE.Views.DocumentHolder.txtClear": "Очистить", "SSE.Views.DocumentHolder.txtClearAll": "Всё", "SSE.Views.DocumentHolder.txtClearComments": "Комментарии", "SSE.Views.DocumentHolder.txtClearFormat": "Форматирование", "SSE.Views.DocumentHolder.txtClearHyper": "Гиперссылки", "SSE.Views.DocumentHolder.txtClearPivotField": "Снять фильтр со столбца {0}", "SSE.Views.DocumentHolder.txtClearSparklineGroups": "Очистить выбранные группы спарклайнов", "SSE.Views.DocumentHolder.txtClearSparklines": "Очистить выбранные спарклайны", "SSE.Views.DocumentHolder.txtClearText": "Текст", "SSE.Views.DocumentHolder.txtCollapse": "Свернуть", "SSE.Views.DocumentHolder.txtCollapseEntire": "Свернуть все поле", "SSE.Views.DocumentHolder.txtColumn": "Столбец", "SSE.Views.DocumentHolder.txtColumnWidth": "Задать ширину столбца", "SSE.Views.DocumentHolder.txtCondFormat": "Условное форматирование", "SSE.Views.DocumentHolder.txtCopy": "Копировать", "SSE.Views.DocumentHolder.txtCount": "Количество", "SSE.Views.DocumentHolder.txtCurrency": "Денежный", "SSE.Views.DocumentHolder.txtCustomColumnWidth": "Особая ширина столбца", "SSE.Views.DocumentHolder.txtCustomRowHeight": "Особая высота строки", "SSE.Views.DocumentHolder.txtCustomSort": "Настраиваемая сортировка", "SSE.Views.DocumentHolder.txtCut": "Вырезать", "SSE.Views.DocumentHolder.txtDateLong": "Длинный формат даты", "SSE.Views.DocumentHolder.txtDateShort": "Краткий формат даты", "SSE.Views.DocumentHolder.txtDelete": "Удалить", "SSE.Views.DocumentHolder.txtDelField": "Удалить", "SSE.Views.DocumentHolder.txtDescending": "По убыванию", "SSE.Views.DocumentHolder.txtDifference": "Отличие", "SSE.Views.DocumentHolder.txtDistribHor": "Распределить по горизонтали", "SSE.Views.DocumentHolder.txtDistribVert": "Распределить по вертикали", "SSE.Views.DocumentHolder.txtEditComment": "Редактировать комментарий", "SSE.Views.DocumentHolder.txtEditObject": "Редактировать объект", "SSE.Views.DocumentHolder.txtExpand": "Развернуть", "SSE.Views.DocumentHolder.txtExpandCollapse": "Развернуть/Свернуть", "SSE.Views.DocumentHolder.txtExpandEntire": "Развернуть все поле", "SSE.Views.DocumentHolder.txtFieldSettings": "Параметры полей", "SSE.Views.DocumentHolder.txtFilter": "Фильтр", "SSE.Views.DocumentHolder.txtFilterCellColor": "Фильтр по цвету ячейки", "SSE.Views.DocumentHolder.txtFilterFontColor": "Фильтр по цвету шрифта", "SSE.Views.DocumentHolder.txtFilterValue": "Фильтр по значению выбранной ячейки", "SSE.Views.DocumentHolder.txtFormula": "Вставить функцию", "SSE.Views.DocumentHolder.txtFraction": "Дробный", "SSE.Views.DocumentHolder.txtGeneral": "Общий", "SSE.Views.DocumentHolder.txtGetLink": "Получить ссылку на этот диапазон", "SSE.Views.DocumentHolder.txtGrandTotal": "Общий итог", "SSE.Views.DocumentHolder.txtGroup": "Сгруппировать", "SSE.Views.DocumentHolder.txtHide": "Скрыть", "SSE.Views.DocumentHolder.txtIndex": "Ин<PERSON><PERSON><PERSON>с", "SSE.Views.DocumentHolder.txtInsert": "Добавить", "SSE.Views.DocumentHolder.txtInsHyperlink": "Гиперссылка", "SSE.Views.DocumentHolder.txtInsImage": "Вставить изображение из файла", "SSE.Views.DocumentHolder.txtInsImageUrl": "Вставить изображение по URL", "SSE.Views.DocumentHolder.txtLabelFilter": "Фильтры по подписи", "SSE.Views.DocumentHolder.txtMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.DocumentHolder.txtMin": "<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.txtMoreOptions": "Дополнительные параметры", "SSE.Views.DocumentHolder.txtNormal": "Без вычислений", "SSE.Views.DocumentHolder.txtNumber": "Числовой", "SSE.Views.DocumentHolder.txtNumFormat": "Числовой формат", "SSE.Views.DocumentHolder.txtPaste": "Вставить", "SSE.Views.DocumentHolder.txtPercent": "% от", "SSE.Views.DocumentHolder.txtPercentage": "Процентный", "SSE.Views.DocumentHolder.txtPercentDiff": "Разница (%)", "SSE.Views.DocumentHolder.txtPercentOfCol": "% от суммы по столбцу", "SSE.Views.DocumentHolder.txtPercentOfGrand": "% от общего итога", "SSE.Views.DocumentHolder.txtPercentOfParent": "% от родительской суммы", "SSE.Views.DocumentHolder.txtPercentOfParentCol": "% от суммы по родительскому столбцу", "SSE.Views.DocumentHolder.txtPercentOfParentRow": "% от суммы по родительской строке", "SSE.Views.DocumentHolder.txtPercentOfRunTotal": "% от суммы с нарастающим итогом в поле", "SSE.Views.DocumentHolder.txtPercentOfTotal": "% от суммы по строке", "SSE.Views.DocumentHolder.txtPivotSettings": "Параметры сводной таблицы", "SSE.Views.DocumentHolder.txtProduct": "Произведение", "SSE.Views.DocumentHolder.txtRankAscending": "Сортировка от минимального к максимальному", "SSE.Views.DocumentHolder.txtRankDescending": "Сортировка от максимального к минимальному", "SSE.Views.DocumentHolder.txtReapply": "Применить повторно", "SSE.Views.DocumentHolder.txtRefresh": "Обновить", "SSE.Views.DocumentHolder.txtRow": "Строку", "SSE.Views.DocumentHolder.txtRowHeight": "Задать высоту строки", "SSE.Views.DocumentHolder.txtRunTotal": "С нарастающим итогом в поле", "SSE.Views.DocumentHolder.txtScientific": "Научный", "SSE.Views.DocumentHolder.txtSelect": "Выделить", "SSE.Views.DocumentHolder.txtShiftDown": "Ячейки со сдвигом вниз", "SSE.Views.DocumentHolder.txtShiftLeft": "Ячейки со сдвигом влево", "SSE.Views.DocumentHolder.txtShiftRight": "Ячейки со сдвигом вправо", "SSE.Views.DocumentHolder.txtShiftUp": "Ячейки со сдвигом вверх", "SSE.Views.DocumentHolder.txtShow": "Показать", "SSE.Views.DocumentHolder.txtShowAs": "Дополнительные вычисления", "SSE.Views.DocumentHolder.txtShowComment": "Показать комментарий", "SSE.Views.DocumentHolder.txtShowDetails": "Показать детали", "SSE.Views.DocumentHolder.txtSort": "Сортировка", "SSE.Views.DocumentHolder.txtSortCellColor": "Сначала ячейки с выделенным цветом", "SSE.Views.DocumentHolder.txtSortFontColor": "Сначала ячейки с выделенным шрифтом", "SSE.Views.DocumentHolder.txtSortOption": "Дополнительные параметры сортировки", "SSE.Views.DocumentHolder.txtSparklines": "Спарклайны", "SSE.Views.DocumentHolder.txtSubtotalField": "Подытог", "SSE.Views.DocumentHolder.txtSum": "Сумма", "SSE.Views.DocumentHolder.txtSummarize": "Итоги по", "SSE.Views.DocumentHolder.txtText": "Текстовый", "SSE.Views.DocumentHolder.txtTextAdvanced": "Дополнительные параметры абзаца", "SSE.Views.DocumentHolder.txtTime": "Время", "SSE.Views.DocumentHolder.txtTop10": "Первые 10", "SSE.Views.DocumentHolder.txtUngroup": "Разгруппировать", "SSE.Views.DocumentHolder.txtValueFieldSettings": "Параметры поля значений", "SSE.Views.DocumentHolder.txtValueFilter": "Фильтры по значению", "SSE.Views.DocumentHolder.txtWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.DocumentHolder.unicodeText": "Юникод", "SSE.Views.DocumentHolder.vertAlignText": "Вертикальное выравнивание", "SSE.Views.ExternalLinksDlg.closeButtonText": "Закрыть", "SSE.Views.ExternalLinksDlg.textAutoUpdate": "Автоматически обновлять данные из связанных источников", "SSE.Views.ExternalLinksDlg.textChange": "Изменить источник", "SSE.Views.ExternalLinksDlg.textDelete": "Разорвать связи", "SSE.Views.ExternalLinksDlg.textDeleteAll": "Разорвать все связи", "SSE.Views.ExternalLinksDlg.textOk": "OK", "SSE.Views.ExternalLinksDlg.textOpen": "Открыть источник", "SSE.Views.ExternalLinksDlg.textSource": "Источник", "SSE.Views.ExternalLinksDlg.textStatus": "Статус", "SSE.Views.ExternalLinksDlg.textUnknown": "Неизвестно", "SSE.Views.ExternalLinksDlg.textUpdate": "Обновить значения", "SSE.Views.ExternalLinksDlg.textUpdateAll": "Обновить все", "SSE.Views.ExternalLinksDlg.textUpdating": "Обновление...", "SSE.Views.ExternalLinksDlg.txtTitle": "Внешние ссылки", "SSE.Views.FieldSettingsDialog.strLayout": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.strSubtotals": "Промежуточные итоги", "SSE.Views.FieldSettingsDialog.textNumFormat": "Числовой формат", "SSE.Views.FieldSettingsDialog.textReport": "Форма отчета", "SSE.Views.FieldSettingsDialog.textTitle": "Параметры полей", "SSE.Views.FieldSettingsDialog.txtAverage": "Среднее", "SSE.Views.FieldSettingsDialog.txtBlank": "Добавлять пустую строку после каждой записи", "SSE.Views.FieldSettingsDialog.txtBottom": "Показывать в нижней части группы", "SSE.Views.FieldSettingsDialog.txtCompact": "Компактная", "SSE.Views.FieldSettingsDialog.txtCount": "Количество", "SSE.Views.FieldSettingsDialog.txtCountNums": "Количество чисел", "SSE.Views.FieldSettingsDialog.txtCustomName": "Пользовательское имя", "SSE.Views.FieldSettingsDialog.txtEmpty": "Показывать элементы без данных", "SSE.Views.FieldSettingsDialog.txtMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtMin": "<PERSON><PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtOutline": "Структура", "SSE.Views.FieldSettingsDialog.txtProduct": "Произведение", "SSE.Views.FieldSettingsDialog.txtRepeat": "Повторять метки элементов в каждой строке", "SSE.Views.FieldSettingsDialog.txtShowSubtotals": "Показывать промежуточные итоги", "SSE.Views.FieldSettingsDialog.txtSourceName": "Имя источника:", "SSE.Views.FieldSettingsDialog.txtStdDev": "Стандотклон", "SSE.Views.FieldSettingsDialog.txtStdDevp": "Стандотклонп", "SSE.Views.FieldSettingsDialog.txtSum": "Сумма", "SSE.Views.FieldSettingsDialog.txtSummarize": "Функции для промежуточных итогов", "SSE.Views.FieldSettingsDialog.txtTabular": "В виде таблицы", "SSE.Views.FieldSettingsDialog.txtTop": "Показывать в заголовке группы", "SSE.Views.FieldSettingsDialog.txtVar": "Ди<PERSON><PERSON>", "SSE.Views.FieldSettingsDialog.txtVarp": "Дис<PERSON>р", "SSE.Views.FileMenu.ariaFileMenu": "<PERSON><PERSON><PERSON>ю Файл", "SSE.Views.FileMenu.btnBackCaption": "Открыть расположение файла", "SSE.Views.FileMenu.btnCloseEditor": "Закрыть файл", "SSE.Views.FileMenu.btnCloseMenuCaption": "Назад", "SSE.Views.FileMenu.btnCreateNewCaption": "Создать новую", "SSE.Views.FileMenu.btnDownloadCaption": "Скачать как", "SSE.Views.FileMenu.btnExitCaption": "Закрыть", "SSE.Views.FileMenu.btnExportToPDFCaption": "Экспорт в PDF", "SSE.Views.FileMenu.btnFileOpenCaption": "Открыть", "SSE.Views.FileMenu.btnHelpCaption": "Справка", "SSE.Views.FileMenu.btnHistoryCaption": "История версий", "SSE.Views.FileMenu.btnInfoCaption": "Сведения", "SSE.Views.FileMenu.btnPrintCaption": "Печать", "SSE.Views.FileMenu.btnProtectCaption": "Защитить", "SSE.Views.FileMenu.btnRecentFilesCaption": "Открыть последние", "SSE.Views.FileMenu.btnRenameCaption": "Переименовать", "SSE.Views.FileMenu.btnReturnCaption": "Вернуться к таблице", "SSE.Views.FileMenu.btnRightsCaption": "Права доступа", "SSE.Views.FileMenu.btnSaveAsCaption": "Сохранить как", "SSE.Views.FileMenu.btnSaveCaption": "Сохранить", "SSE.Views.FileMenu.btnSaveCopyAsCaption": "Сохранить копию как", "SSE.Views.FileMenu.btnSettingsCaption": "Дополнительные параметры", "SSE.Views.FileMenu.btnSwitchToMobileCaption": "Переключиться в мобильный режим", "SSE.Views.FileMenu.btnToEditCaption": "Редактировать таблицу", "SSE.Views.FileMenuPanels.CreateNew.txtBlank": "Пустая таблица", "SSE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Создать новую", "SSE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Применить", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Добавить автора", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddProperty": "Добавить свойство", "SSE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Добавить текст", "SSE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Приложение", "SSE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Изменить права доступа", "SSE.Views.FileMenuPanels.DocumentInfo.txtComment": "Комментарий", "SSE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Общие", "SSE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Создана", "SSE.Views.FileMenuPanels.DocumentInfo.txtDocumentPropertyUpdateTitle": "Свойство документа", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Автор последнего изменения", "SSE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Последнее изменение", "SSE.Views.FileMenuPanels.DocumentInfo.txtNo": "Нет", "SSE.Views.FileMenuPanels.DocumentInfo.txtOwner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Размещение", "SSE.Views.FileMenuPanels.DocumentInfo.txtProperties": "Свойства", "SSE.Views.FileMenuPanels.DocumentInfo.txtPropertyTitleConflictError": "Свойство с таким названием уже существует", "SSE.Views.FileMenuPanels.DocumentInfo.txtRights": "Люди, имеющие права", "SSE.Views.FileMenuPanels.DocumentInfo.txtSpreadsheetInfo": "Сведения о таблице", "SSE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Тема", "SSE.Views.FileMenuPanels.DocumentInfo.txtTags": "Теги", "SSE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Название", "SSE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Загружена", "SSE.Views.FileMenuPanels.DocumentInfo.txtYes": "Да", "SSE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Права доступа", "SSE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Изменить права доступа", "SSE.Views.FileMenuPanels.DocumentRights.txtRights": "Люди, имеющие права", "SSE.Views.FileMenuPanels.MainSettingsGeneral.okButtonText": "Применить", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strCoAuthMode": "Режим совместного редактирования", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDateFormat1904": "Использовать систему дат 1904", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDecimalSeparator": "Десятичный разделитель", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strDictionaryLanguage": "Язык словаря", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strEnableIterative": "Включить итеративные вычисления", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFast": "Быстрый", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFontRender": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> шрифтов", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocale": "Язык формул", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFuncLocaleEx": "Пример: СУММ; МИН; МАКС; СЧЁТ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strFunctionTooltip": "Показывать подсказки функций", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strHScroll": "Показать горизонтальную полосу прокрутки", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsInUPPERCASE": "Пропускать слова из ПРОПИСНЫХ БУКВ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strIgnoreWordsWithNumbers": "Пропускать слова с цифрами", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMacrosSettings": "Настройки макросов", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxChange": "Относительная погрешность", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strMaxIterations": "Предельное число итераций", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strPasteButton": "Показывать кнопку Параметры вставки при вставке содержимого", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strReferenceStyle": "Стиль ссылок R1C1", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettings": "Региональные параметры", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRegSettingsEx": "Пример:", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strRTLSupport": "RTL-интерфейс", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowComments": "Показывать комментарии на листе", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowOthersChanges": "Показывать изменения других пользователей", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strShowResolvedComments": "Показывать решенные комментарии", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strSmoothScroll": "Привязка к сетке при прокрутке", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strStrict": "Строгий", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTabStyle": "Стиль вкладки", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strTheme": "Тема интерфейса", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strThousandsSeparator": "Разделитель разрядов тысяч", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUnit": "Единица измерения", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strUseSeparatorsBasedOnRegionalSettings": "Использовать разделители на базе региональных настроек", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strVScroll": "Показать вертикальную полосу прокрутки", "SSE.Views.FileMenuPanels.MainSettingsGeneral.strZoom": "Стандартное значение масштаба", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text10Minutes": "Каждые 10 минут", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text30Minutes": "Каждые 30 минут", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text5Minutes": "Каждые 5 минут", "SSE.Views.FileMenuPanels.MainSettingsGeneral.text60Minutes": "Каждый час", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoRecover": "Автовосстановление", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textAutoSave": "Автосохранение", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textDisabled": "Отключено", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textFill": "Заливка", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textForceSave": "Сохранение промежуточных версий", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textLine": "Линия", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textMinute": "Каждую минуту", "SSE.Views.FileMenuPanels.MainSettingsGeneral.textRefStyle": "Стиль ссылок", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAdvancedSettings": "Дополнительные параметры", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAppearance": "Вид", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtAutoCorrect": "Параметры автозамены...", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBe": "Белорусский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtBg": "Болгарский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCa": "Каталонский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCacheMode": "Режим кэширования по умолчанию", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCalculating": "Вычисление", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCm": "Сантиметр", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCollaboration": "Совместная работа", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCs": "Чешский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtCustomizeQuickAccess": "Настроить быстрый доступ", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDa": "Датский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtDe": "Немецкий", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEditingSaving": "Редактирование и сохранение", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEl": "Греческий", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEn": "Английский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtErrorNumber": "Введенные данные нельзя использовать. Возможно, требуется целое или десятичное число.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtEs": "Испанский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFastTip": "Совместное редактирование в режиме реального времени. Все изменения сохраняются автоматически", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFi": "Финский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtFr": "Французский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHu": "Венгерский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtHy": "Армянский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtId": "Индонезийский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtInch": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtIt": "Итальянский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtJa": "Японский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtKo": "Корейский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLastUsed": "Последний использованный", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLo": "Лаосский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtLv": "Латышский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtMac": "как OS X", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNative": "Собственный", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNb": "Норвежский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtNl": "Голландский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPl": "Польский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtProofing": "Правописание", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPt": "<PERSON>у<PERSON><PERSON><PERSON>", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtbr": "Португальский (Бразилия)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtPtlang": "Португальский (Португалия)", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrint": "Показывать кнопку Быстрая печать в шапке редактора", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtQuickPrintTip": "Документ будет напечатан на последнем выбранном принтере или на принтере по умолчанию", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRegion": "Регион", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRo": "Румынский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRu": "Русский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacros": "Включить все", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtRunMacrosDesc": "Включить все макросы без уведомления", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtScreenReader": "Включить поддержку средства чтения с экрана", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDir": "Направление листов по умолчанию", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetDirDesc": "Эта настройка повлияет только на новые листы.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetLtr": "Слева направо", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSheetRtl": "Справа налево", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSk": "Словацкий", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSl": "Словенский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacros": "Отключить все", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStopMacrosDesc": "Отключить все макросы без уведомления", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtStrictTip": "Используйте кнопку \"Сохранить\" для синхронизации изменений, внесенных вами и другими пользователями.", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtSv": "Шведский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTabBack": "Использовать цвет панели инструментов как фон вкладок", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtTr": "Турецкий", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUk": "Украинский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseAltKey": "Использовать клавишу Alt для навигации по интерфейсу с помощью клавиатуры", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtUseOptionKey": "Использовать клавишу Option для навигации по интерфейсу с помощью клавиатуры", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtVi": "Вьетнамский", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacros": "Показывать уведомление", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWarnMacrosDesc": "Отключить все макросы с уведомлением", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWin": "как Windows", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtWorkspace": "Рабочая область", "SSE.Views.FileMenuPanels.MainSettingsGeneral.txtZh": "Китайский", "SSE.Views.FileMenuPanels.ProtectDoc.notcriticalErrorTitle": "Внимание", "SSE.Views.FileMenuPanels.ProtectDoc.strEncrypt": "C помощью пароля", "SSE.Views.FileMenuPanels.ProtectDoc.strProtect": "Защитить электронную таблицу", "SSE.Views.FileMenuPanels.ProtectDoc.strSignature": "С помощью подписи", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddedSignature": "В таблицу добавлены действительные подписи.<br>Таблица защищена от редактирования.", "SSE.Views.FileMenuPanels.ProtectDoc.txtAddSignature": "Обеспечить целостность таблицы, добавив<br>невидимую цифровую подпись", "SSE.Views.FileMenuPanels.ProtectDoc.txtEdit": "Редактировать таблицу", "SSE.Views.FileMenuPanels.ProtectDoc.txtEditWarning": "При редактировании из электронной таблицы будут удалены подписи.<br>Продолжить?", "SSE.Views.FileMenuPanels.ProtectDoc.txtEncrypted": "Эта электронная таблица защищена паролем", "SSE.Views.FileMenuPanels.ProtectDoc.txtProtectSpreadsheet": "Зашифровать эту таблицу с помощью пароля", "SSE.Views.FileMenuPanels.ProtectDoc.txtRequestedSignatures": "Эту таблицу требуется подписать.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSigned": "В электронную таблицу добавлены действительные подписи. Таблица защищена от редактирования.", "SSE.Views.FileMenuPanels.ProtectDoc.txtSignedInvalid": "Некоторые из цифровых подписей в электронной таблице недействительны или их нельзя проверить. Таблица защищена от редактирования.", "SSE.Views.FileMenuPanels.ProtectDoc.txtView": "Просмотр подписей", "SSE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Скачать как", "SSE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Сохранить копию как", "SSE.Views.FillSeriesDialog.textAuto": "Автозаполнение", "SSE.Views.FillSeriesDialog.textCols": "По столбцам", "SSE.Views.FillSeriesDialog.textDate": "Дата", "SSE.Views.FillSeriesDialog.textDateUnit": "Единица", "SSE.Views.FillSeriesDialog.textDay": "День", "SSE.Views.FillSeriesDialog.textGrowth": "Геометрическая", "SSE.Views.FillSeriesDialog.textLinear": "Арифметическая", "SSE.Views.FillSeriesDialog.textMonth": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FillSeriesDialog.textRows": "По строкам", "SSE.Views.FillSeriesDialog.textSeries": "Расположение", "SSE.Views.FillSeriesDialog.textStep": "<PERSON>аг", "SSE.Views.FillSeriesDialog.textStop": "Предельное значение", "SSE.Views.FillSeriesDialog.textTitle": "Прогрессия", "SSE.Views.FillSeriesDialog.textTrend": "Автоматическое определение шага", "SSE.Views.FillSeriesDialog.textType": "Тип", "SSE.Views.FillSeriesDialog.textWeek": "Рабочий день", "SSE.Views.FillSeriesDialog.textYear": "Год", "SSE.Views.FillSeriesDialog.txtErrorNumber": "Введенные данные нельзя использовать. Возможно, требуется целое или десятичное число.", "SSE.Views.FormatRulesEditDlg.fillColor": "Цвет заливки", "SSE.Views.FormatRulesEditDlg.notcriticalErrorTitle": "Внимание", "SSE.Views.FormatRulesEditDlg.text2Scales": "Двухцветная шкала", "SSE.Views.FormatRulesEditDlg.text3Scales": "Трехцветная шкала", "SSE.Views.FormatRulesEditDlg.textAllBorders": "Все границы", "SSE.Views.FormatRulesEditDlg.textAppearance": "Внешний вид столбца", "SSE.Views.FormatRulesEditDlg.textApply": "Применить к диапазону", "SSE.Views.FormatRulesEditDlg.textAutomatic": "Автоматически", "SSE.Views.FormatRulesEditDlg.textAxis": "Оси", "SSE.Views.FormatRulesEditDlg.textBarDirection": "Направление столбца", "SSE.Views.FormatRulesEditDlg.textBold": "Полужирный", "SSE.Views.FormatRulesEditDlg.textBorder": "Гра<PERSON><PERSON><PERSON>а", "SSE.Views.FormatRulesEditDlg.textBordersColor": "Цвет границ", "SSE.Views.FormatRulesEditDlg.textBordersStyle": "Стиль границ", "SSE.Views.FormatRulesEditDlg.textBottomBorders": "Нижние границы", "SSE.Views.FormatRulesEditDlg.textCannotAddCF": "Не удается добавить условное форматирование.", "SSE.Views.FormatRulesEditDlg.textCellMidpoint": "Середина ячейки", "SSE.Views.FormatRulesEditDlg.textCenterBorders": "Внутренние вертикальные границы", "SSE.Views.FormatRulesEditDlg.textClear": "Очистить", "SSE.Views.FormatRulesEditDlg.textColor": "Цвет текста", "SSE.Views.FormatRulesEditDlg.textContext": "Контекст", "SSE.Views.FormatRulesEditDlg.textCustom": "Особый", "SSE.Views.FormatRulesEditDlg.textDiagDownBorder": "Диагональная граница сверху вниз", "SSE.Views.FormatRulesEditDlg.textDiagUpBorder": "Диагональная граница снизу вверх", "SSE.Views.FormatRulesEditDlg.textEmptyFormula": "Введите допустимую формулу.", "SSE.Views.FormatRulesEditDlg.textEmptyFormulaExt": "Значение введенной формулы не является числом, дато<PERSON>, временем или строкой.", "SSE.Views.FormatRulesEditDlg.textEmptyText": "Введите значение.", "SSE.Views.FormatRulesEditDlg.textEmptyValue": "Введенное значение не является допустимыми числом, датой, временем или строкой.", "SSE.Views.FormatRulesEditDlg.textErrorGreater": "Значение для {0} должно быть больше, чем значение для {1}.", "SSE.Views.FormatRulesEditDlg.textErrorTop10Between": "Введите число от {0} до {1}.", "SSE.Views.FormatRulesEditDlg.textFill": "Заливка", "SSE.Views.FormatRulesEditDlg.textFormat": "Формат", "SSE.Views.FormatRulesEditDlg.textFormula": "Формула", "SSE.Views.FormatRulesEditDlg.textGradient": "Град<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textIconLabel": "когда {0} {1} и", "SSE.Views.FormatRulesEditDlg.textIconLabelFirst": "когда {0} {1}", "SSE.Views.FormatRulesEditDlg.textIconLabelLast": "когда значение равно", "SSE.Views.FormatRulesEditDlg.textIconsOverlap": "Один или несколько диапазонов данных значков перекрываются.<br>Скорректируйте значения диапазонов данных значков так, чтобы диапазоны не перекрывались.", "SSE.Views.FormatRulesEditDlg.textIconStyle": "Стиль значка", "SSE.Views.FormatRulesEditDlg.textInsideBorders": "Внутренние границы", "SSE.Views.FormatRulesEditDlg.textInvalid": "Недопустимый диапазон данных.", "SSE.Views.FormatRulesEditDlg.textInvalidRange": "ОШИБКА! Недопустимый диапазон ячеек", "SSE.Views.FormatRulesEditDlg.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.textItem": "Элемент", "SSE.Views.FormatRulesEditDlg.textLeft2Right": "Слева направо", "SSE.Views.FormatRulesEditDlg.textLeftBorders": "Левые границы", "SSE.Views.FormatRulesEditDlg.textLongBar": "Самый длинный столбец", "SSE.Views.FormatRulesEditDlg.textMaximum": "Максимум", "SSE.Views.FormatRulesEditDlg.textMaxpoint": "Макс. точка", "SSE.Views.FormatRulesEditDlg.textMiddleBorders": "Внутренние горизонтальные границы", "SSE.Views.FormatRulesEditDlg.textMidpoint": "Средняя точка", "SSE.Views.FormatRulesEditDlg.textMinimum": "Мини<PERSON>ум", "SSE.Views.FormatRulesEditDlg.textMinpoint": "Мин. точка", "SSE.Views.FormatRulesEditDlg.textNegative": "Отрицательное", "SSE.Views.FormatRulesEditDlg.textNewColor": "Другие цвета", "SSE.Views.FormatRulesEditDlg.textNoBorders": "Без границ", "SSE.Views.FormatRulesEditDlg.textNone": "Нет", "SSE.Views.FormatRulesEditDlg.textNotValidPercentage": "По крайней мере одно из указанных значений не является допустимым процентом.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentageExt": "Указанное значение {0} не является допустимым процентом.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentile": "По крайней мере одно из указанных значений не является допустимым процентилем.", "SSE.Views.FormatRulesEditDlg.textNotValidPercentileExt": "Указанное значение {0} не является допустимым процентилем.", "SSE.Views.FormatRulesEditDlg.textOutBorders": "Внешние границы", "SSE.Views.FormatRulesEditDlg.textPercent": "Процентный", "SSE.Views.FormatRulesEditDlg.textPercentile": "Процентиль", "SSE.Views.FormatRulesEditDlg.textPosition": "Положение", "SSE.Views.FormatRulesEditDlg.textPositive": "Положительное", "SSE.Views.FormatRulesEditDlg.textPresets": "Предустановки", "SSE.Views.FormatRulesEditDlg.textPreview": "Просмотр", "SSE.Views.FormatRulesEditDlg.textRelativeRef": "В условии условного форматирования нельзя использовать относительные ссылки для цветовых шкал, гистограмм и наборов значков.", "SSE.Views.FormatRulesEditDlg.textReverse": "Значки в обратном порядке", "SSE.Views.FormatRulesEditDlg.textRight2Left": "Справа налево", "SSE.Views.FormatRulesEditDlg.textRightBorders": "Правые границы", "SSE.Views.FormatRulesEditDlg.textRule": "Правило", "SSE.Views.FormatRulesEditDlg.textSameAs": "Как положительное", "SSE.Views.FormatRulesEditDlg.textSelectData": "Выбор данных", "SSE.Views.FormatRulesEditDlg.textShortBar": "самый короткий столбец", "SSE.Views.FormatRulesEditDlg.textShowBar": "Показывать только столбец", "SSE.Views.FormatRulesEditDlg.textShowIcon": "Показать только значок", "SSE.Views.FormatRulesEditDlg.textSingleRef": "Такой тип ссылки нельзя использовать в формуле условного форматирования.<br>Измените ссылку так, чтобы она указывала на одну ячейку, или поместите ссылку в функцию. Например: =СУММ(A1:B5).", "SSE.Views.FormatRulesEditDlg.textSolid": "Сплошной", "SSE.Views.FormatRulesEditDlg.textStrikeout": "Зачеркнутый", "SSE.Views.FormatRulesEditDlg.textSubscript": "Подстрочные знаки", "SSE.Views.FormatRulesEditDlg.textSuperscript": "Надстрочные знаки", "SSE.Views.FormatRulesEditDlg.textTopBorders": "Верхние границы", "SSE.Views.FormatRulesEditDlg.textUnderline": "Подчёркнутый", "SSE.Views.FormatRulesEditDlg.tipBorders": "<PERSON>р<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.FormatRulesEditDlg.tipNumFormat": "Числовой формат", "SSE.Views.FormatRulesEditDlg.txtAccounting": "Финансовый", "SSE.Views.FormatRulesEditDlg.txtCurrency": "Денежный", "SSE.Views.FormatRulesEditDlg.txtDate": "Дата", "SSE.Views.FormatRulesEditDlg.txtDateLong": "Длинный формат даты", "SSE.Views.FormatRulesEditDlg.txtDateShort": "Краткий формат даты", "SSE.Views.FormatRulesEditDlg.txtEmpty": "Это поле необходимо заполнить", "SSE.Views.FormatRulesEditDlg.txtFraction": "Дробный", "SSE.Views.FormatRulesEditDlg.txtGeneral": "Общий", "SSE.Views.FormatRulesEditDlg.txtNoCellIcon": "Без значка", "SSE.Views.FormatRulesEditDlg.txtNumber": "Числовой", "SSE.Views.FormatRulesEditDlg.txtPercentage": "Процент", "SSE.Views.FormatRulesEditDlg.txtScientific": "Научный", "SSE.Views.FormatRulesEditDlg.txtText": "Текст", "SSE.Views.FormatRulesEditDlg.txtTime": "Время", "SSE.Views.FormatRulesEditDlg.txtTitleEdit": "Изменение правила форматирования", "SSE.Views.FormatRulesEditDlg.txtTitleNew": "Новое правило форматирования", "SSE.Views.FormatRulesManagerDlg.guestText": "Гость", "SSE.Views.FormatRulesManagerDlg.lockText": "Заблокирован", "SSE.Views.FormatRulesManagerDlg.text1Above": "На 1 стандартное отклонение выше среднего", "SSE.Views.FormatRulesManagerDlg.text1Below": "На 1 стандартное отклонение ниже среднего", "SSE.Views.FormatRulesManagerDlg.text2Above": "На 2 стандартных отклонения выше среднего", "SSE.Views.FormatRulesManagerDlg.text2Below": "На 2 стандартных отклонения ниже среднего", "SSE.Views.FormatRulesManagerDlg.text3Above": "На 3 стандартных отклонения выше среднего", "SSE.Views.FormatRulesManagerDlg.text3Below": "На 3 стандартных отклонения ниже среднего", "SSE.Views.FormatRulesManagerDlg.textAbove": "Выше среднего", "SSE.Views.FormatRulesManagerDlg.textApply": "Применить к", "SSE.Views.FormatRulesManagerDlg.textBeginsWith": "Значение ячейки начинается с", "SSE.Views.FormatRulesManagerDlg.textBelow": "Ниже среднего", "SSE.Views.FormatRulesManagerDlg.textBetween": "находится между {0} и {1}", "SSE.Views.FormatRulesManagerDlg.textCellValue": "Значение ячейки", "SSE.Views.FormatRulesManagerDlg.textColorScale": "Шкала цветов", "SSE.Views.FormatRulesManagerDlg.textContains": "Значение ячейки содержит", "SSE.Views.FormatRulesManagerDlg.textContainsBlank": "Ячейка содержит пустое значение", "SSE.Views.FormatRulesManagerDlg.textContainsError": "Ячейка содержит ошибку", "SSE.Views.FormatRulesManagerDlg.textDelete": "Удалить", "SSE.Views.FormatRulesManagerDlg.textDown": "Переместить правило вниз", "SSE.Views.FormatRulesManagerDlg.textDuplicate": "Повторяющиеся значения", "SSE.Views.FormatRulesManagerDlg.textEdit": "Изменить", "SSE.Views.FormatRulesManagerDlg.textEnds": "Значение ячейки заканчивается на", "SSE.Views.FormatRulesManagerDlg.textEqAbove": "Равно или выше среднего", "SSE.Views.FormatRulesManagerDlg.textEqBelow": "Равно или ниже среднего", "SSE.Views.FormatRulesManagerDlg.textFormat": "Формат", "SSE.Views.FormatRulesManagerDlg.textIconSet": "На<PERSON><PERSON><PERSON> знач<PERSON>ов", "SSE.Views.FormatRulesManagerDlg.textNew": "Новое", "SSE.Views.FormatRulesManagerDlg.textNotBetween": "не находится между {0} и {1}", "SSE.Views.FormatRulesManagerDlg.textNotContains": "Значение ячейки не содержит", "SSE.Views.FormatRulesManagerDlg.textNotContainsBlank": "Ячейка не содержит пустое значение", "SSE.Views.FormatRulesManagerDlg.textNotContainsError": "Ячейка не содержит ошибку", "SSE.Views.FormatRulesManagerDlg.textRules": "Правила", "SSE.Views.FormatRulesManagerDlg.textScope": "Показать правила форматирования для", "SSE.Views.FormatRulesManagerDlg.textSelectData": "Выбор данных", "SSE.Views.FormatRulesManagerDlg.textSelection": "Текущий выделенный фрагмент", "SSE.Views.FormatRulesManagerDlg.textThisPivot": "Эта сводная таблица", "SSE.Views.FormatRulesManagerDlg.textThisSheet": "Этот лист", "SSE.Views.FormatRulesManagerDlg.textThisTable": "Эта таблица", "SSE.Views.FormatRulesManagerDlg.textUnique": "Уникальные значения", "SSE.Views.FormatRulesManagerDlg.textUp": "Переместить правило вверх", "SSE.Views.FormatRulesManagerDlg.tipIsLocked": "Этот элемент редактируется другим пользователем.", "SSE.Views.FormatRulesManagerDlg.txtTitle": "Условное форматирование", "SSE.Views.FormatSettingsDialog.textCategory": "Категория", "SSE.Views.FormatSettingsDialog.textDecimal": "Десятичные знаки", "SSE.Views.FormatSettingsDialog.textFormat": "Формат", "SSE.Views.FormatSettingsDialog.textLinked": "Связь с источником", "SSE.Views.FormatSettingsDialog.textSeparator": "Использовать разделитель разрядов", "SSE.Views.FormatSettingsDialog.textSymbols": "Обозначения", "SSE.Views.FormatSettingsDialog.textTitle": "Числовой формат", "SSE.Views.FormatSettingsDialog.txtAccounting": "Финансовый", "SSE.Views.FormatSettingsDialog.txtAs10": "Десятыми долями (5/10)", "SSE.Views.FormatSettingsDialog.txtAs100": "Сотыми долями (50/100)", "SSE.Views.FormatSettingsDialog.txtAs16": "Шестнадцатыми долями (8/16)", "SSE.Views.FormatSettingsDialog.txtAs2": "Половинными долями (1/2)", "SSE.Views.FormatSettingsDialog.txtAs4": "Четвертыми долями (2/4)", "SSE.Views.FormatSettingsDialog.txtAs8": "Восьмыми долями (4/8)", "SSE.Views.FormatSettingsDialog.txtCurrency": "Денежный", "SSE.Views.FormatSettingsDialog.txtCustom": "Особый", "SSE.Views.FormatSettingsDialog.txtCustomWarning": "Вводите пользовательский числовой формат внимательно. Редактор электронных таблиц не проверяет пользовательские форматы на ошибки, что может повлиять на файл xlsx.", "SSE.Views.FormatSettingsDialog.txtDate": "Дата", "SSE.Views.FormatSettingsDialog.txtFraction": "Дробный", "SSE.Views.FormatSettingsDialog.txtGeneral": "Общий", "SSE.Views.FormatSettingsDialog.txtNone": "Нет", "SSE.Views.FormatSettingsDialog.txtNumber": "Числовой", "SSE.Views.FormatSettingsDialog.txtPercentage": "Процентный", "SSE.Views.FormatSettingsDialog.txtSample": "Пример:", "SSE.Views.FormatSettingsDialog.txtScientific": "Научный", "SSE.Views.FormatSettingsDialog.txtText": "Текстовый", "SSE.Views.FormatSettingsDialog.txtTime": "Время", "SSE.Views.FormatSettingsDialog.txtUpto1": "До одной цифры (1/3)", "SSE.Views.FormatSettingsDialog.txtUpto2": "До двух цифр (12/25)", "SSE.Views.FormatSettingsDialog.txtUpto3": "До трех цифр (131/135)", "SSE.Views.FormulaDialog.sDescription": "Описание", "SSE.Views.FormulaDialog.textGroupDescription": "Выберите группу функций", "SSE.Views.FormulaDialog.textListDescription": "Выберите функцию", "SSE.Views.FormulaDialog.txtRecommended": "Рекомендуемые", "SSE.Views.FormulaDialog.txtSearch": "Поиск", "SSE.Views.FormulaDialog.txtTitle": "Вставить функцию", "SSE.Views.FormulaTab.capBtnRemoveArr": "Убрать стрелки", "SSE.Views.FormulaTab.capBtnTraceDep": "Зависимые ячейки", "SSE.Views.FormulaTab.capBtnTracePrec": "Влияющие ячейки", "SSE.Views.FormulaTab.textAutomatic": "Автоматически", "SSE.Views.FormulaTab.textCalculateCurrentSheet": "Пересчет текущего листа", "SSE.Views.FormulaTab.textCalculateWorkbook": "Пересчет книги", "SSE.Views.FormulaTab.textManual": "Вручн<PERSON>ю", "SSE.Views.FormulaTab.tipCalculate": "Пересчет", "SSE.Views.FormulaTab.tipCalculateTheEntireWorkbook": "Пересчет всей книги", "SSE.Views.FormulaTab.tipRemoveArr": "Убрать стрелки к влияющим или зависимым ячейкам", "SSE.Views.FormulaTab.tipShowFormulas": "Отображать в каждой ячейке формулу, а не значение результата", "SSE.Views.FormulaTab.tipTraceDep": "Отображать стрелки, которые показывают, какие ячейки зависят от значения выделенной ячейки", "SSE.Views.FormulaTab.tipTracePrec": "Отображать стрелки, которые показывают, какие ячейки влияют на значение выделенной ячейки", "SSE.Views.FormulaTab.tipWatch": "Добавить ячейки в список Окно контрольного значения", "SSE.Views.FormulaTab.txtAdditional": "Вставить функцию", "SSE.Views.FormulaTab.txtAutosum": "Автосумма", "SSE.Views.FormulaTab.txtAutosumTip": "Сумма", "SSE.Views.FormulaTab.txtCalculation": "Пересчет", "SSE.Views.FormulaTab.txtFormula": "Функция", "SSE.Views.FormulaTab.txtFormulaTip": "Вставить функцию", "SSE.Views.FormulaTab.txtMore": "Другие функции", "SSE.Views.FormulaTab.txtRecent": "Последние использованные", "SSE.Views.FormulaTab.txtRemDep": "Убрать стрелки к зависимым ячейкам", "SSE.Views.FormulaTab.txtRemPrec": "Убрать стрелки к влияющим ячейкам", "SSE.Views.FormulaTab.txtShowFormulas": "Показать формулы", "SSE.Views.FormulaTab.txtWatch": "Окно контрольных значений", "SSE.Views.FormulaWizard.textAny": "любой", "SSE.Views.FormulaWizard.textArgument": "Аргумент", "SSE.Views.FormulaWizard.textFunction": "Функция", "SSE.Views.FormulaWizard.textFunctionRes": "Результат функции", "SSE.Views.FormulaWizard.textHelp": "Справка по этой функции", "SSE.Views.FormulaWizard.textLogical": "логическое значение", "SSE.Views.FormulaWizard.textNoArgs": "У этой функции нет аргументов", "SSE.Views.FormulaWizard.textNoArgsDesc": "у этого аргумента нет описания", "SSE.Views.FormulaWizard.textNumber": "число", "SSE.Views.FormulaWizard.textReadMore": "Подробнее", "SSE.Views.FormulaWizard.textRef": "ссылка", "SSE.Views.FormulaWizard.textText": "текст", "SSE.Views.FormulaWizard.textTitle": "Аргументы функции", "SSE.Views.FormulaWizard.textValue": "Значение", "SSE.Views.GoalSeekDlg.textChangingCell": "Изменяя значение ячейки", "SSE.Views.GoalSeekDlg.textDataRangeError": "В формуле отсутствует диапазон", "SSE.Views.GoalSeekDlg.textMustContainFormula": "Ячейка должна содержать формулу", "SSE.Views.GoalSeekDlg.textMustContainValue": "Ячейка должна содержать значение", "SSE.Views.GoalSeekDlg.textMustFormulaResultNumber": "Формула в ячейке должна возвращать число.", "SSE.Views.GoalSeekDlg.textMustSingleCell": "Ссылка должна указывать на одну ячейку", "SSE.Views.GoalSeekDlg.textSelectData": "Выбор данных", "SSE.Views.GoalSeekDlg.textSetCell": "Установить в ячейке", "SSE.Views.GoalSeekDlg.textTitle": "Подбор параметра", "SSE.Views.GoalSeekDlg.textToValue": "Значение", "SSE.Views.GoalSeekDlg.txtEmpty": "Это поле необходимо заполнить", "SSE.Views.GoalSeekDlg.txtErrorNumber": "Введенные данные нельзя использовать. Возможно, требуется целое или десятичное число.", "SSE.Views.GoalSeekStatusDlg.textContinue": "Продолжить", "SSE.Views.GoalSeekStatusDlg.textCurrentValue": "Текущее значение:", "SSE.Views.GoalSeekStatusDlg.textFoundSolution": "Подбор параметра для ячейки {0}. Решение найдено.", "SSE.Views.GoalSeekStatusDlg.textNotFoundSolution": "Подбор параметра для ячейки {0}. Решение не найдено.", "SSE.Views.GoalSeekStatusDlg.textPause": "Пауза", "SSE.Views.GoalSeekStatusDlg.textSearchIteration": "Подбор параметра для ячейки {0}. Итерация #{1}.", "SSE.Views.GoalSeekStatusDlg.textStep": "<PERSON>аг", "SSE.Views.GoalSeekStatusDlg.textTargetValue": "Подбираемое значение:", "SSE.Views.GoalSeekStatusDlg.textTitle": "Статус подбора параметра", "SSE.Views.HeaderFooterDialog.textAlign": "Выровнять относительно полей страницы", "SSE.Views.HeaderFooterDialog.textAll": "Все страницы", "SSE.Views.HeaderFooterDialog.textBold": "Полужирный", "SSE.Views.HeaderFooterDialog.textCenter": "В центре", "SSE.Views.HeaderFooterDialog.textColor": "Цвет текста", "SSE.Views.HeaderFooterDialog.textDate": "Дата", "SSE.Views.HeaderFooterDialog.textDiffFirst": "Особый для первой страницы", "SSE.Views.HeaderFooterDialog.textDiffOdd": "Разные для четных и нечетных", "SSE.Views.HeaderFooterDialog.textEven": "Четная страница", "SSE.Views.HeaderFooterDialog.textFileName": "Имя файла", "SSE.Views.HeaderFooterDialog.textFirst": "Первая страница", "SSE.Views.HeaderFooterDialog.textFooter": "Нижний колонтитул", "SSE.Views.HeaderFooterDialog.textHeader": "Верхний колонтитул", "SSE.Views.HeaderFooterDialog.textImage": "Изображение", "SSE.Views.HeaderFooterDialog.textInsert": "Вставить", "SSE.Views.HeaderFooterDialog.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.textLeft": "Слева", "SSE.Views.HeaderFooterDialog.textMaxError": "Введена слишком длинная текстовая строка. Уменьшите число знаков.", "SSE.Views.HeaderFooterDialog.textNewColor": "Другие цвета", "SSE.Views.HeaderFooterDialog.textOdd": "Нечетная страница", "SSE.Views.HeaderFooterDialog.textPageCount": "<PERSON>и<PERSON><PERSON>о страниц", "SSE.Views.HeaderFooterDialog.textPageNum": "Номер страницы", "SSE.Views.HeaderFooterDialog.textPresets": "Предустановки", "SSE.Views.HeaderFooterDialog.textRight": "Справа", "SSE.Views.HeaderFooterDialog.textScale": "Изменять масштаб вместе с документом", "SSE.Views.HeaderFooterDialog.textSheet": "Имя листа", "SSE.Views.HeaderFooterDialog.textStrikeout": "Зачёркнутый", "SSE.Views.HeaderFooterDialog.textSubscript": "Подстрочный", "SSE.Views.HeaderFooterDialog.textSuperscript": "Надстрочный", "SSE.Views.HeaderFooterDialog.textTime": "Время", "SSE.Views.HeaderFooterDialog.textTitle": "Параметры верхнего и нижнего колонтитулов", "SSE.Views.HeaderFooterDialog.textUnderline": "Подчёркнутый", "SSE.Views.HeaderFooterDialog.tipFontName": "<PERSON>ри<PERSON><PERSON>", "SSE.Views.HeaderFooterDialog.tipFontSize": "Размер шрифта", "SSE.Views.HyperlinkSettingsDialog.strDisplay": "Отобража<PERSON>ь", "SSE.Views.HyperlinkSettingsDialog.strLinkTo": "Связать с", "SSE.Views.HyperlinkSettingsDialog.strRange": "Диа<PERSON>азон", "SSE.Views.HyperlinkSettingsDialog.strSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.HyperlinkSettingsDialog.textCopy": "Копировать", "SSE.Views.HyperlinkSettingsDialog.textDefault": "Выбранный диапазон", "SSE.Views.HyperlinkSettingsDialog.textEmptyDesc": "Введите здесь надпись", "SSE.Views.HyperlinkSettingsDialog.textEmptyLink": "Введите здесь ссылку", "SSE.Views.HyperlinkSettingsDialog.textEmptyTooltip": "Введите здесь подсказку", "SSE.Views.HyperlinkSettingsDialog.textExternalLink": "Внешняя ссылка", "SSE.Views.HyperlinkSettingsDialog.textGetLink": "Получить ссылку", "SSE.Views.HyperlinkSettingsDialog.textInternalLink": "Внутренний диапазон данных", "SSE.Views.HyperlinkSettingsDialog.textInvalidRange": "ОШИБКА! Недопустимый диапазон ячеек", "SSE.Views.HyperlinkSettingsDialog.textNames": "Определенные имена", "SSE.Views.HyperlinkSettingsDialog.textSelectData": "Выбор данных", "SSE.Views.HyperlinkSettingsDialog.textSelectFile": "Выбрать файл", "SSE.Views.HyperlinkSettingsDialog.textSheets": "<PERSON>исты", "SSE.Views.HyperlinkSettingsDialog.textTipText": "Текст всплывающей подсказки", "SSE.Views.HyperlinkSettingsDialog.textTitle": "Параметры гиперссылки", "SSE.Views.HyperlinkSettingsDialog.txtEmpty": "Это поле необходимо заполнить", "SSE.Views.HyperlinkSettingsDialog.txtNotUrl": "Это поле должно быть URL-адресом в формате \"http://www.example.com\"", "SSE.Views.HyperlinkSettingsDialog.txtSizeLimit": "Это поле может содержать не более 2083 символов", "SSE.Views.HyperlinkSettingsDialog.txtUrlPlaceholder": "Введите веб-адрес или выберите файл", "SSE.Views.ImageSettings.strTransparency": "Непрозрачность", "SSE.Views.ImageSettings.textAdvanced": "Дополнительные параметры", "SSE.Views.ImageSettings.textCrop": "Обрезать", "SSE.Views.ImageSettings.textCropFill": "Заливка", "SSE.Views.ImageSettings.textCropFit": "Вписать", "SSE.Views.ImageSettings.textCropToShape": "Обрезать по фигуре", "SSE.Views.ImageSettings.textEdit": "Редактировать", "SSE.Views.ImageSettings.textEditObject": "Редактировать объект", "SSE.Views.ImageSettings.textFlip": "Отразить", "SSE.Views.ImageSettings.textFromFile": "Из файла", "SSE.Views.ImageSettings.textFromStorage": "Из хранилища", "SSE.Views.ImageSettings.textFromUrl": "По URL", "SSE.Views.ImageSettings.textHeight": "Высота", "SSE.Views.ImageSettings.textHint270": "Повернуть на 90° против часовой стрелки", "SSE.Views.ImageSettings.textHint90": "Повернуть на 90° по часовой стрелке", "SSE.Views.ImageSettings.textHintFlipH": "Отразить слева направо", "SSE.Views.ImageSettings.textHintFlipV": "Отразить сверху вниз", "SSE.Views.ImageSettings.textInsert": "Заменить изображение", "SSE.Views.ImageSettings.textKeepRatio": "Сохранять пропорции", "SSE.Views.ImageSettings.textOriginalSize": "Реальный размер", "SSE.Views.ImageSettings.textRecentlyUsed": "Последние использованные", "SSE.Views.ImageSettings.textResetCrop": "Сбросить обрезку", "SSE.Views.ImageSettings.textRotate90": "Повернуть на 90°", "SSE.Views.ImageSettings.textRotation": "Поворот", "SSE.Views.ImageSettings.textSize": "Размер", "SSE.Views.ImageSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.ImageSettingsAdvanced.textAbsolute": "Не перемещать и не изменять размеры вместе с ячейками", "SSE.Views.ImageSettingsAdvanced.textAlt": "Альтернативный текст", "SSE.Views.ImageSettingsAdvanced.textAltDescription": "Описание", "SSE.Views.ImageSettingsAdvanced.textAltTip": "Альтернативное текстовое представление информации о визуальном объекте, которое будет зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит изображение, фигура, диаграмма или таблица.", "SSE.Views.ImageSettingsAdvanced.textAltTitle": "Заголовок", "SSE.Views.ImageSettingsAdvanced.textAngle": "Угол", "SSE.Views.ImageSettingsAdvanced.textFlipped": "Отраж<PERSON>но", "SSE.Views.ImageSettingsAdvanced.textHorizontally": "По горизонтали", "SSE.Views.ImageSettingsAdvanced.textOneCell": "Перемещать, но не изменять размеры вместе с ячейками", "SSE.Views.ImageSettingsAdvanced.textRotation": "Поворот", "SSE.Views.ImageSettingsAdvanced.textSnap": "Привязка к ячейке", "SSE.Views.ImageSettingsAdvanced.textTitle": "Изображение - дополнительные параметры", "SSE.Views.ImageSettingsAdvanced.textTwoCell": "Перемещать и изменять размеры вместе с ячейками", "SSE.Views.ImageSettingsAdvanced.textVertically": "По вертикали", "SSE.Views.ImportFromXmlDialog.textDestination": "Выберите, где поместить данные", "SSE.Views.ImportFromXmlDialog.textExist": "Существующий лист", "SSE.Views.ImportFromXmlDialog.textInvalidRange": "Недопустимый диапазон ячеек", "SSE.Views.ImportFromXmlDialog.textNew": "Новый лист", "SSE.Views.ImportFromXmlDialog.textSelectData": "Выбор данных", "SSE.Views.ImportFromXmlDialog.textTitle": "Импорт данных", "SSE.Views.ImportFromXmlDialog.txtEmpty": "Это поле обязательно для заполнения", "SSE.Views.LeftMenu.ariaLeftMenu": "Левое меню", "SSE.Views.LeftMenu.tipAbout": "О программе", "SSE.Views.LeftMenu.tipChat": "Чат", "SSE.Views.LeftMenu.tipComments": "Комментарии", "SSE.Views.LeftMenu.tipFile": "<PERSON>а<PERSON><PERSON>", "SSE.Views.LeftMenu.tipPlugins": "Плагины", "SSE.Views.LeftMenu.tipSearch": "Поиск", "SSE.Views.LeftMenu.tipSpellcheck": "Проверка орфографии", "SSE.Views.LeftMenu.tipSupport": "Обратная связь и поддержка", "SSE.Views.LeftMenu.txtDeveloper": "РЕЖИМ РАЗРАБОТЧИКА", "SSE.Views.LeftMenu.txtEditor": "Редактор таблиц", "SSE.Views.LeftMenu.txtLimit": "Ограниченный доступ", "SSE.Views.LeftMenu.txtTrial": "ПРОБНЫЙ РЕЖИМ", "SSE.Views.LeftMenu.txtTrialDev": "Пробный режим разработчика", "SSE.Views.MacroDialog.textMacro": "Имя макроса", "SSE.Views.MacroDialog.textTitle": "Назначить макрос", "SSE.Views.MainSettingsPrint.okButtonText": "Сохранить", "SSE.Views.MainSettingsPrint.strBottom": "Снизу", "SSE.Views.MainSettingsPrint.strLandscape": "Альбомная", "SSE.Views.MainSettingsPrint.strLeft": "Слева", "SSE.Views.MainSettingsPrint.strMargins": "Поля", "SSE.Views.MainSettingsPrint.strPortrait": "Книжная", "SSE.Views.MainSettingsPrint.strPrint": "Печать", "SSE.Views.MainSettingsPrint.strPrintTitles": "Печатать заголовки", "SSE.Views.MainSettingsPrint.strRight": "Справа", "SSE.Views.MainSettingsPrint.strTop": "Сверху", "SSE.Views.MainSettingsPrint.textActualSize": "Реальный размер", "SSE.Views.MainSettingsPrint.textCustom": "Особый", "SSE.Views.MainSettingsPrint.textCustomOptions": "Настраиваемые параметры", "SSE.Views.MainSettingsPrint.textFitCols": "Вписать все столбцы на одну страницу", "SSE.Views.MainSettingsPrint.textFitPage": "Вписать лист на одну страницу", "SSE.Views.MainSettingsPrint.textFitRows": "Вписать все строки на одну страницу", "SSE.Views.MainSettingsPrint.textPageOrientation": "Ориентация страницы", "SSE.Views.MainSettingsPrint.textPageScaling": "Масш<PERSON><PERSON><PERSON>", "SSE.Views.MainSettingsPrint.textPageSize": "Размер страницы", "SSE.Views.MainSettingsPrint.textPrintGrid": "Печать сетки", "SSE.Views.MainSettingsPrint.textPrintHeadings": "Печать заголовков строк и столбцов", "SSE.Views.MainSettingsPrint.textRepeat": "Повторять...", "SSE.Views.MainSettingsPrint.textRepeatLeft": "Повторять столбцы слева", "SSE.Views.MainSettingsPrint.textRepeatTop": "Повторять строки сверху", "SSE.Views.MainSettingsPrint.textSettings": "Настройки для", "SSE.Views.NamedRangeEditDlg.errorCreateDefName": "В настоящий момент нельзя отредактировать существующие именованные диапазоны и создать новые,<br>так как некоторые из них редактируются.", "SSE.Views.NamedRangeEditDlg.namePlaceholder": "Определенное имя", "SSE.Views.NamedRangeEditDlg.notcriticalErrorTitle": "Внимание", "SSE.Views.NamedRangeEditDlg.strWorkbook": "Книга", "SSE.Views.NamedRangeEditDlg.textDataRange": "Диапазон данных", "SSE.Views.NamedRangeEditDlg.textExistName": "ОШИБКА! Диапазон с таким именем уже существует", "SSE.Views.NamedRangeEditDlg.textInvalidName": "Имя должно начинаться с буквы или нижнего подчеркивания и не должно содержать недопустимых символов.", "SSE.Views.NamedRangeEditDlg.textInvalidRange": "ОШИБКА! Недопустимый диапазон ячеек", "SSE.Views.NamedRangeEditDlg.textIsLocked": "ОШИБКА! Этот элемент редактируется другим пользователем.", "SSE.Views.NamedRangeEditDlg.textName": "Имя", "SSE.Views.NamedRangeEditDlg.textReservedName": "Формулы в ячейках уже содержат ссылки на имя, которое вы пытаетесь использовать. Используйте другое имя.", "SSE.Views.NamedRangeEditDlg.textScope": "Область", "SSE.Views.NamedRangeEditDlg.textSelectData": "Выбор данных", "SSE.Views.NamedRangeEditDlg.txtEmpty": "Это поле обязательно для заполнения", "SSE.Views.NamedRangeEditDlg.txtTitleEdit": "Изменение имени", "SSE.Views.NamedRangeEditDlg.txtTitleNew": "Новое имя", "SSE.Views.NamedRangePasteDlg.textNames": "Именованные диапазоны", "SSE.Views.NamedRangePasteDlg.txtTitle": "Вставка имени", "SSE.Views.NameManagerDlg.closeButtonText": "Закрыть", "SSE.Views.NameManagerDlg.guestText": "Гость", "SSE.Views.NameManagerDlg.lockText": "Заблокирован", "SSE.Views.NameManagerDlg.textDataRange": "Диапазон данных", "SSE.Views.NameManagerDlg.textDelete": "Удалить", "SSE.Views.NameManagerDlg.textEdit": "Изменить", "SSE.Views.NameManagerDlg.textEmpty": "Еще не создано ни одного именованного диапазона.<br>Создайте хотя бы один именованный диапазон, и он появится в этом поле.", "SSE.Views.NameManagerDlg.textFilter": "Фильтр", "SSE.Views.NameManagerDlg.textFilterAll": "Все", "SSE.Views.NameManagerDlg.textFilterDefNames": "Определенные имена", "SSE.Views.NameManagerDlg.textFilterSheet": "Имена на листе", "SSE.Views.NameManagerDlg.textFilterTableNames": "<PERSON><PERSON><PERSON><PERSON> таблиц", "SSE.Views.NameManagerDlg.textFilterWorkbook": "Имена в книге", "SSE.Views.NameManagerDlg.textNew": "Новое", "SSE.Views.NameManagerDlg.textnoNames": "Не найдено именованных диапазонов, соответствующих фильтру.", "SSE.Views.NameManagerDlg.textRanges": "Именованные диапазоны", "SSE.Views.NameManagerDlg.textScope": "Область", "SSE.Views.NameManagerDlg.textWorkbook": "Книга", "SSE.Views.NameManagerDlg.tipIsLocked": "Этот элемент редактируется другим пользователем.", "SSE.Views.NameManagerDlg.txtTitle": "Диспетчер имен", "SSE.Views.NameManagerDlg.warnDelete": "Вы действительно хотите удалить имя {0}?", "SSE.Views.PageMarginsDialog.textBottom": "Нижнее", "SSE.Views.PageMarginsDialog.textCenter": "Центрировать на странице", "SSE.Views.PageMarginsDialog.textHor": "По горизонтали", "SSE.Views.PageMarginsDialog.textLeft": "Левое", "SSE.Views.PageMarginsDialog.textRight": "Правое", "SSE.Views.PageMarginsDialog.textTitle": "Поля", "SSE.Views.PageMarginsDialog.textTop": "Верхнее", "SSE.Views.PageMarginsDialog.textVert": "По вертикали", "SSE.Views.PageMarginsDialog.textWarning": "Внимание", "SSE.Views.PageMarginsDialog.warnCheckMargings": "Неправильные поля", "SSE.Views.ParagraphSettings.strLineHeight": "Междустрочный интервал", "SSE.Views.ParagraphSettings.strParagraphSpacing": "Интервал между абзацами", "SSE.Views.ParagraphSettings.strSpacingAfter": "После", "SSE.Views.ParagraphSettings.strSpacingBefore": "Перед", "SSE.Views.ParagraphSettings.textAdvanced": "Дополнительные параметры", "SSE.Views.ParagraphSettings.textAt": "Значение", "SSE.Views.ParagraphSettings.textAtLeast": "Мини<PERSON>ум", "SSE.Views.ParagraphSettings.textAuto": "Множитель", "SSE.Views.ParagraphSettings.textExact": "Точно", "SSE.Views.ParagraphSettings.txtAutoText": "Авто", "SSE.Views.ParagraphSettingsAdvanced.noTabs": "В этом поле появятся позиции табуляции, которые вы зададите", "SSE.Views.ParagraphSettingsAdvanced.strAllCaps": "Все прописные", "SSE.Views.ParagraphSettingsAdvanced.strDoubleStrike": "Двойное зачёркивание", "SSE.Views.ParagraphSettingsAdvanced.strIndent": "Отступы", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLeftText": "Слева", "SSE.Views.ParagraphSettingsAdvanced.strIndentsLineSpacing": "Междустрочный интервал", "SSE.Views.ParagraphSettingsAdvanced.strIndentsRightText": "Справа", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingAfter": "После", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpacingBefore": "Перед", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecial": "Первая строка", "SSE.Views.ParagraphSettingsAdvanced.strIndentsSpecialBy": "На", "SSE.Views.ParagraphSettingsAdvanced.strParagraphFont": "<PERSON>ри<PERSON><PERSON>", "SSE.Views.ParagraphSettingsAdvanced.strParagraphIndents": "Отступы и интервалы", "SSE.Views.ParagraphSettingsAdvanced.strSmallCaps": "Малые прописные", "SSE.Views.ParagraphSettingsAdvanced.strSpacing": "Интервал между абзацами", "SSE.Views.ParagraphSettingsAdvanced.strStrike": "Зачёркивание", "SSE.Views.ParagraphSettingsAdvanced.strSubscript": "Подстрочные", "SSE.Views.ParagraphSettingsAdvanced.strSuperscript": "Надстрочные", "SSE.Views.ParagraphSettingsAdvanced.strTabs": "Табуляция", "SSE.Views.ParagraphSettingsAdvanced.textAlign": "Выравнивание", "SSE.Views.ParagraphSettingsAdvanced.textAuto": "Множитель", "SSE.Views.ParagraphSettingsAdvanced.textCharacterSpacing": "Межзнаковый интервал", "SSE.Views.ParagraphSettingsAdvanced.textDefault": "По умолчанию", "SSE.Views.ParagraphSettingsAdvanced.textEffects": "Эффекты", "SSE.Views.ParagraphSettingsAdvanced.textExact": "Точно", "SSE.Views.ParagraphSettingsAdvanced.textFirstLine": "Отступ", "SSE.Views.ParagraphSettingsAdvanced.textHanging": "Выступ", "SSE.Views.ParagraphSettingsAdvanced.textJustified": "По ширине", "SSE.Views.ParagraphSettingsAdvanced.textNoneSpecial": "(нет)", "SSE.Views.ParagraphSettingsAdvanced.textRemove": "Удалить", "SSE.Views.ParagraphSettingsAdvanced.textRemoveAll": "Удалить все", "SSE.Views.ParagraphSettingsAdvanced.textSet": "Задать", "SSE.Views.ParagraphSettingsAdvanced.textTabCenter": "По центру", "SSE.Views.ParagraphSettingsAdvanced.textTabLeft": "По левому краю", "SSE.Views.ParagraphSettingsAdvanced.textTabPosition": "Позиция", "SSE.Views.ParagraphSettingsAdvanced.textTabRight": "По правому краю", "SSE.Views.ParagraphSettingsAdvanced.textTitle": "Абзац - дополнительные параметры", "SSE.Views.ParagraphSettingsAdvanced.txtAutoText": "Авто", "SSE.Views.PivotCalculatedItemsDialog.txtDelete": "Удалить", "SSE.Views.PivotCalculatedItemsDialog.txtDuplicate": "Дублировать", "SSE.Views.PivotCalculatedItemsDialog.txtEdit": "Изменить", "SSE.Views.PivotCalculatedItemsDialog.txtFormula": "Формула", "SSE.Views.PivotCalculatedItemsDialog.txtItemsName": "Название элементов", "SSE.Views.PivotCalculatedItemsDialog.txtNew": "Новый", "SSE.Views.PivotCalculatedItemsDialog.txtTitle": "Вычисляемые элементы в", "SSE.Views.PivotDigitalFilterDialog.capCondition1": "равно", "SSE.Views.PivotDigitalFilterDialog.capCondition10": "не заканчивается на", "SSE.Views.PivotDigitalFilterDialog.capCondition11": "содер<PERSON><PERSON>т", "SSE.Views.PivotDigitalFilterDialog.capCondition12": "не содержит", "SSE.Views.PivotDigitalFilterDialog.capCondition13": "между", "SSE.Views.PivotDigitalFilterDialog.capCondition14": "не между", "SSE.Views.PivotDigitalFilterDialog.capCondition2": "не равно", "SSE.Views.PivotDigitalFilterDialog.capCondition3": "больше чем", "SSE.Views.PivotDigitalFilterDialog.capCondition4": "больше или равно", "SSE.Views.PivotDigitalFilterDialog.capCondition5": "меньше чем", "SSE.Views.PivotDigitalFilterDialog.capCondition6": "меньше или равно", "SSE.Views.PivotDigitalFilterDialog.capCondition7": "начинается с", "SSE.Views.PivotDigitalFilterDialog.capCondition8": "не начинается с", "SSE.Views.PivotDigitalFilterDialog.capCondition9": "заканчивается на", "SSE.Views.PivotDigitalFilterDialog.textShowLabel": "Показывать элементы, у которых подпись:", "SSE.Views.PivotDigitalFilterDialog.textShowValue": "Показывать элементы, у которых:", "SSE.Views.PivotDigitalFilterDialog.textUse1": "Используйте знак ? вместо любого отдельного символа", "SSE.Views.PivotDigitalFilterDialog.textUse2": "Используйте знак * вместо любой последовательности символов", "SSE.Views.PivotDigitalFilterDialog.txtAnd": "и", "SSE.Views.PivotDigitalFilterDialog.txtTitleLabel": "Фильтр подписей", "SSE.Views.PivotDigitalFilterDialog.txtTitleValue": "Фильтр значений", "SSE.Views.PivotGroupDialog.textAuto": "Авто", "SSE.Views.PivotGroupDialog.textBy": "По", "SSE.Views.PivotGroupDialog.textDays": "<PERSON><PERSON>и", "SSE.Views.PivotGroupDialog.textEnd": "Заканчивая в", "SSE.Views.PivotGroupDialog.textError": "Это поле должно содержать числовое значение", "SSE.Views.PivotGroupDialog.textGreaterError": "Конечное число должно быть больше начального.", "SSE.Views.PivotGroupDialog.textHour": "<PERSON>а<PERSON>ы", "SSE.Views.PivotGroupDialog.textMin": "Минуты", "SSE.Views.PivotGroupDialog.textMonth": "Месяцы", "SSE.Views.PivotGroupDialog.textNumDays": "Количество дней", "SSE.Views.PivotGroupDialog.textQuart": "Кварталы", "SSE.Views.PivotGroupDialog.textSec": "Секунды", "SSE.Views.PivotGroupDialog.textStart": "Начиная с:", "SSE.Views.PivotGroupDialog.textYear": "Годы", "SSE.Views.PivotGroupDialog.txtTitle": "Группировка", "SSE.Views.PivotInsertCalculatedItemDialog.txtDescription": "Вы можете использовать вычисляемые элементы для базовых вычислений между различными элементами внутри одного поля.", "SSE.Views.PivotInsertCalculatedItemDialog.txtFormula": "Формула", "SSE.Views.PivotInsertCalculatedItemDialog.txtInsertIntoFormula": "Вставить в формулу", "SSE.Views.PivotInsertCalculatedItemDialog.txtItem": "Элемент", "SSE.Views.PivotInsertCalculatedItemDialog.txtItemName": "Название элемента", "SSE.Views.PivotInsertCalculatedItemDialog.txtItems": "Элементы", "SSE.Views.PivotInsertCalculatedItemDialog.txtReadMore": "Подробнее", "SSE.Views.PivotInsertCalculatedItemDialog.txtTitle": "Вставить вычисляемый элемент в", "SSE.Views.PivotSettings.textAdvanced": "Дополнительные параметры", "SSE.Views.PivotSettings.textColumns": "Столбцы", "SSE.Views.PivotSettings.textFields": "Выбрать поля", "SSE.Views.PivotSettings.textFilters": "Фильтры", "SSE.Views.PivotSettings.textRows": "Строки", "SSE.Views.PivotSettings.textValues": "Значения", "SSE.Views.PivotSettings.txtAddColumn": "Добавить в столбцы", "SSE.Views.PivotSettings.txtAddFilter": "Добавить в фильтры", "SSE.Views.PivotSettings.txtAddRow": "Добавить в строки", "SSE.Views.PivotSettings.txtAddValues": "Добавить в значения", "SSE.Views.PivotSettings.txtFieldSettings": "Параметры полей", "SSE.Views.PivotSettings.txtMoveBegin": "Переместить в начало", "SSE.Views.PivotSettings.txtMoveColumn": "Переместить в столбцы", "SSE.Views.PivotSettings.txtMoveDown": "Переместить вниз", "SSE.Views.PivotSettings.txtMoveEnd": "Переместить в конец", "SSE.Views.PivotSettings.txtMoveFilter": "Переместить в фильтры", "SSE.Views.PivotSettings.txtMoveRow": "Переместить в строки", "SSE.Views.PivotSettings.txtMoveUp": "Переместить вверх", "SSE.Views.PivotSettings.txtMoveValues": "Переместить в значения", "SSE.Views.PivotSettings.txtRemove": "Удалить поле", "SSE.Views.PivotSettingsAdvanced.strLayout": "Название и макет", "SSE.Views.PivotSettingsAdvanced.textAlt": "Альтернативный текст", "SSE.Views.PivotSettingsAdvanced.textAltDescription": "Описание", "SSE.Views.PivotSettingsAdvanced.textAltTip": "Альтернативное текстовое представление информации о визуальном объекте, которое будет зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит изображение, фигура, диаграмма или таблица.", "SSE.Views.PivotSettingsAdvanced.textAltTitle": "Заголовок", "SSE.Views.PivotSettingsAdvanced.textAutofitColWidth": "Автоматически изменять ширину столбцов при обновлении", "SSE.Views.PivotSettingsAdvanced.textDataRange": "Диапазон данных", "SSE.Views.PivotSettingsAdvanced.textDataSource": "Источник данных", "SSE.Views.PivotSettingsAdvanced.textDisplayFields": "Отображать поля в области фильтра отчета", "SSE.Views.PivotSettingsAdvanced.textDown": "Вниз, затем вправо", "SSE.Views.PivotSettingsAdvanced.textGrandTotals": "Общие итоги", "SSE.Views.PivotSettingsAdvanced.textHeaders": "Заголовки полей", "SSE.Views.PivotSettingsAdvanced.textInvalidRange": "ОШИБКА! Недопустимый диапазон ячеек", "SSE.Views.PivotSettingsAdvanced.textOver": "Вправо, затем вниз", "SSE.Views.PivotSettingsAdvanced.textSelectData": "Выбор данных", "SSE.Views.PivotSettingsAdvanced.textShowCols": "Показывать для столбцов", "SSE.Views.PivotSettingsAdvanced.textShowHeaders": "Показывать заголовки полей для строк и столбцов", "SSE.Views.PivotSettingsAdvanced.textShowRows": "Показывать для строк", "SSE.Views.PivotSettingsAdvanced.textTitle": "Сводная таблица - дополнительные параметры", "SSE.Views.PivotSettingsAdvanced.textWrapCol": "Число полей фильтра отчета в столбце", "SSE.Views.PivotSettingsAdvanced.textWrapRow": "Число полей фильтра отчета в строке", "SSE.Views.PivotSettingsAdvanced.txtEmpty": "Это поле необходимо заполнить", "SSE.Views.PivotSettingsAdvanced.txtName": "Название", "SSE.Views.PivotShowDetailDialog.textDescription": "Выберите поле, содержащее детали, которые вы хотите отобразить:", "SSE.Views.PivotShowDetailDialog.txtTitle": "Показать детали", "SSE.Views.PivotTable.capBlankRows": "Пустые строки", "SSE.Views.PivotTable.capGrandTotals": "Общие итоги", "SSE.Views.PivotTable.capLayout": "Макет отчета", "SSE.Views.PivotTable.capSubtotals": "Промежуточные итоги", "SSE.Views.PivotTable.mniBottomSubtotals": "Показывать все промежуточные итоги в нижней части группы", "SSE.Views.PivotTable.mniInsertBlankLine": "Вставлять пустую строку после каждого элемента", "SSE.Views.PivotTable.mniLayoutCompact": "Показать в сжатой форме", "SSE.Views.PivotTable.mniLayoutNoRepeat": "Не повторять все метки элементов", "SSE.Views.PivotTable.mniLayoutOutline": "Показать в форме структуры", "SSE.Views.PivotTable.mniLayoutRepeat": "Повторять все метки элементов", "SSE.Views.PivotTable.mniLayoutTabular": "Показать в табличной форме", "SSE.Views.PivotTable.mniNoSubtotals": "Не показывать промежуточные итоги", "SSE.Views.PivotTable.mniOffTotals": "Отключить для строк и столбцов", "SSE.Views.PivotTable.mniOnColumnsTotals": "Включить только для столбцов", "SSE.Views.PivotTable.mniOnRowsTotals": "Включить только для строк", "SSE.Views.PivotTable.mniOnTotals": "Включить для строк и столбцов", "SSE.Views.PivotTable.mniRemoveBlankLine": "Удалить пустую строку после каждого элемента", "SSE.Views.PivotTable.mniTopSubtotals": "Показывать все промежуточные итоги в верхней части группы", "SSE.Views.PivotTable.textColBanded": "Чередовать столбцы", "SSE.Views.PivotTable.textColHeader": "Заголовки столбцов", "SSE.Views.PivotTable.textRowBanded": "Чередовать строки", "SSE.Views.PivotTable.textRowHeader": "Заголовки строк", "SSE.Views.PivotTable.tipCalculatedItems": "Вычисляемые элементы", "SSE.Views.PivotTable.tipCreatePivot": "Вставить сводную таблицу", "SSE.Views.PivotTable.tipGrandTotals": "Показать или скрыть общие итоги", "SSE.Views.PivotTable.tipRefresh": "Обновить информацию из источника данных", "SSE.Views.PivotTable.tipRefreshCurrent": "Обновить информацию из источника данных для текущей таблицы", "SSE.Views.PivotTable.tipSelect": "Выделить всю сводную таблицу", "SSE.Views.PivotTable.tipSubtotals": "Показать или скрыть промежуточные итоги", "SSE.Views.PivotTable.txtCalculatedItems": "Вычисляемые элементы", "SSE.Views.PivotTable.txtCollapseEntire": "Свернуть все поле", "SSE.Views.PivotTable.txtCreate": "Вставить таблицу", "SSE.Views.PivotTable.txtExpandEntire": "Развернуть все поле", "SSE.Views.PivotTable.txtGroupPivot_Custom": "Пользовательские", "SSE.Views.PivotTable.txtGroupPivot_Dark": "Темные", "SSE.Views.PivotTable.txtGroupPivot_Light": "Светлые", "SSE.Views.PivotTable.txtGroupPivot_Medium": "Средние", "SSE.Views.PivotTable.txtPivotTable": "Сводная таблица", "SSE.Views.PivotTable.txtRefresh": "Обновить", "SSE.Views.PivotTable.txtRefreshAll": "Обновить все", "SSE.Views.PivotTable.txtSelect": "Выделить", "SSE.Views.PivotTable.txtTable_PivotStyleDark": "Стиль сводной таблицы: темный", "SSE.Views.PivotTable.txtTable_PivotStyleLight": "Стиль сводной таблицы: светлый", "SSE.Views.PivotTable.txtTable_PivotStyleMedium": "Стиль сводной таблицы: средний", "SSE.Views.PrintSettings.btnDownload": "Сохранить и скачать", "SSE.Views.PrintSettings.btnExport": "Сохранить и экспортировать", "SSE.Views.PrintSettings.btnPrint": "Сохранить и напечатать", "SSE.Views.PrintSettings.strBottom": "Снизу", "SSE.Views.PrintSettings.strLandscape": "Альбомная", "SSE.Views.PrintSettings.strLeft": "Слева", "SSE.Views.PrintSettings.strMargins": "Поля", "SSE.Views.PrintSettings.strPortrait": "Книжная", "SSE.Views.PrintSettings.strPrint": "Печать", "SSE.Views.PrintSettings.strPrintTitles": "Печатать заголовки", "SSE.Views.PrintSettings.strRight": "Справа", "SSE.Views.PrintSettings.strShow": "Показать", "SSE.Views.PrintSettings.strTop": "Сверху", "SSE.Views.PrintSettings.textActiveSheets": "Активные листы", "SSE.Views.PrintSettings.textActualSize": "Реальный размер", "SSE.Views.PrintSettings.textAllSheets": "Все листы", "SSE.Views.PrintSettings.textCurrentSheet": "Текущий лист", "SSE.Views.PrintSettings.textCustom": "Особый", "SSE.Views.PrintSettings.textCustomOptions": "Настраиваемые параметры", "SSE.Views.PrintSettings.textFitCols": "Вписать все столбцы на одну страницу", "SSE.Views.PrintSettings.textFitPage": "Вписать лист на одну страницу", "SSE.Views.PrintSettings.textFitRows": "Вписать все строки на одну страницу", "SSE.Views.PrintSettings.textHideDetails": "Скрыть детали", "SSE.Views.PrintSettings.textIgnore": "Игнорировать область печати", "SSE.Views.PrintSettings.textLayout": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textMarginsNarrow": "Узкие", "SSE.Views.PrintSettings.textMarginsNormal": "Обычные", "SSE.Views.PrintSettings.textMarginsWide": "Широкие", "SSE.Views.PrintSettings.textPageOrientation": "Ориентация страницы", "SSE.Views.PrintSettings.textPages": "Страницы:", "SSE.Views.PrintSettings.textPageScaling": "Масш<PERSON><PERSON><PERSON>", "SSE.Views.PrintSettings.textPageSize": "Размер страницы", "SSE.Views.PrintSettings.textPrintGrid": "Печать сетки", "SSE.Views.PrintSettings.textPrintHeadings": "Печать заголовков строк и столбцов", "SSE.Views.PrintSettings.textPrintRange": "Диапазон печати", "SSE.Views.PrintSettings.textRange": "Диа<PERSON>азон", "SSE.Views.PrintSettings.textRepeat": "Повторять...", "SSE.Views.PrintSettings.textRepeatLeft": "Повторять столбцы слева", "SSE.Views.PrintSettings.textRepeatTop": "Повторять строки сверху", "SSE.Views.PrintSettings.textSelection": "Выделенный фрагмент", "SSE.Views.PrintSettings.textSettings": "Параметры листа", "SSE.Views.PrintSettings.textShowDetails": "Показать детали", "SSE.Views.PrintSettings.textShowGrid": "Показать линии сетки", "SSE.Views.PrintSettings.textShowHeadings": "Показать заголовки строк и столбцов", "SSE.Views.PrintSettings.textTitle": "Параметры печати", "SSE.Views.PrintSettings.textTitlePDF": "Параметры PDF", "SSE.Views.PrintSettings.textTo": "по", "SSE.Views.PrintSettings.txtMarginsLast": "Последние настраиваемые", "SSE.Views.PrintTitlesDialog.textFirstCol": "Первый столбец", "SSE.Views.PrintTitlesDialog.textFirstRow": "Первая строка", "SSE.Views.PrintTitlesDialog.textFrozenCols": "Закрепленные столбцы", "SSE.Views.PrintTitlesDialog.textFrozenRows": "Закрепленные строки", "SSE.Views.PrintTitlesDialog.textInvalidRange": "ОШИБКА! Недопустимый диапазон ячеек", "SSE.Views.PrintTitlesDialog.textLeft": "Повторять столбцы слева", "SSE.Views.PrintTitlesDialog.textNoRepeat": "Не повторять", "SSE.Views.PrintTitlesDialog.textRepeat": "Повторять...", "SSE.Views.PrintTitlesDialog.textSelectRange": "Выбор диапазона", "SSE.Views.PrintTitlesDialog.textTitle": "Печатать заголовки", "SSE.Views.PrintTitlesDialog.textTop": "Повторять строки сверху", "SSE.Views.PrintWithPreview.txtActiveSheets": "Активные листы", "SSE.Views.PrintWithPreview.txtActualSize": "Реальный размер", "SSE.Views.PrintWithPreview.txtAllSheets": "Все листы", "SSE.Views.PrintWithPreview.txtApplyToAllSheets": "Применить ко всем листам", "SSE.Views.PrintWithPreview.txtBothSides": "Двусторонняя печать", "SSE.Views.PrintWithPreview.txtBothSidesLongDesc": "Переворачивать страницы относительно длинного края", "SSE.Views.PrintWithPreview.txtBothSidesShortDesc": "Переворачивать страницы относительно короткого края", "SSE.Views.PrintWithPreview.txtBottom": "Нижнее", "SSE.Views.PrintWithPreview.txtCopies": "Копии", "SSE.Views.PrintWithPreview.txtCurrentSheet": "Текущий лист", "SSE.Views.PrintWithPreview.txtCustom": "Особый", "SSE.Views.PrintWithPreview.txtCustomOptions": "Настраиваемые параметры", "SSE.Views.PrintWithPreview.txtEmptyTable": "Нечего печатать, так как таблица пуста", "SSE.Views.PrintWithPreview.txtFirstPageNumber": "Номер первой страницы:", "SSE.Views.PrintWithPreview.txtFitCols": "Вписать все столбцы на одну страницу", "SSE.Views.PrintWithPreview.txtFitPage": "Вписать лист на одну страницу", "SSE.Views.PrintWithPreview.txtFitRows": "Вписать все строки на одну страницу", "SSE.Views.PrintWithPreview.txtGridlinesAndHeadings": "Линии сетки и заголовки", "SSE.Views.PrintWithPreview.txtHeaderFooterSettings": "Параметры верхнего и нижнего колонтитулов", "SSE.Views.PrintWithPreview.txtIgnore": "Игнорировать область печати", "SSE.Views.PrintWithPreview.txtLandscape": "Альбомная", "SSE.Views.PrintWithPreview.txtLeft": "Левое", "SSE.Views.PrintWithPreview.txtMargins": "Поля", "SSE.Views.PrintWithPreview.txtMarginsLast": "Последние настраиваемые", "SSE.Views.PrintWithPreview.txtMarginsNarrow": "Узкие", "SSE.Views.PrintWithPreview.txtMarginsNormal": "Обычные", "SSE.Views.PrintWithPreview.txtMarginsWide": "Широкие", "SSE.Views.PrintWithPreview.txtOf": "из {0}", "SSE.Views.PrintWithPreview.txtOneSide": "Односторонняя печать", "SSE.Views.PrintWithPreview.txtOneSideDesc": "Печатать только на одной стороне листа", "SSE.Views.PrintWithPreview.txtPage": "Страница", "SSE.Views.PrintWithPreview.txtPageNumInvalid": "Неправильный номер страницы", "SSE.Views.PrintWithPreview.txtPageOrientation": "Ориентация страницы", "SSE.Views.PrintWithPreview.txtPages": "Страницы:", "SSE.Views.PrintWithPreview.txtPageSize": "Размер страницы", "SSE.Views.PrintWithPreview.txtPortrait": "Книжная", "SSE.Views.PrintWithPreview.txtPrint": "Печать", "SSE.Views.PrintWithPreview.txtPrintGrid": "Печать сетки", "SSE.Views.PrintWithPreview.txtPrintHeadings": "Печать заголовков строк и столбцов", "SSE.Views.PrintWithPreview.txtPrintRange": "Диапазон печати", "SSE.Views.PrintWithPreview.txtPrintSides": "Печатать на обеих сторонах", "SSE.Views.PrintWithPreview.txtPrintTitles": "Печатать заголовки", "SSE.Views.PrintWithPreview.txtPrintToPDF": "Печать в PDF", "SSE.Views.PrintWithPreview.txtRepeat": "Повторять...", "SSE.Views.PrintWithPreview.txtRepeatColumnsAtLeft": "Повторять столбцы слева", "SSE.Views.PrintWithPreview.txtRepeatRowsAtTop": "Повторять строки сверху", "SSE.Views.PrintWithPreview.txtRight": "Правое", "SSE.Views.PrintWithPreview.txtSave": "Сохранить", "SSE.Views.PrintWithPreview.txtScaling": "Масш<PERSON><PERSON><PERSON>", "SSE.Views.PrintWithPreview.txtSelection": "Выделенный фрагмент", "SSE.Views.PrintWithPreview.txtSettingsOfSheet": "Параметры листа", "SSE.Views.PrintWithPreview.txtSheet": "Лист: {0}", "SSE.Views.PrintWithPreview.txtTo": "по", "SSE.Views.PrintWithPreview.txtTop": "Верхнее", "SSE.Views.ProtectDialog.textExistName": "ОШИБКА! Диапазон с таким названием уже существует", "SSE.Views.ProtectDialog.textInvalidName": "Название диапазона должно начинаться с буквы и может содержать только буквы, цифры и пробелы.", "SSE.Views.ProtectDialog.textInvalidRange": "ОШИБКА! Недопустимый диапазон ячеек", "SSE.Views.ProtectDialog.textSelectData": "Выбор данных", "SSE.Views.ProtectDialog.txtAllow": "Разрешить всем пользователям этого листа", "SSE.Views.ProtectDialog.txtAllowDescription": "Вы можете разблокировать определенные диапазоны для редактирования.", "SSE.Views.ProtectDialog.txtAllowRanges": "Разрешить редактировать диапазоны", "SSE.Views.ProtectDialog.txtAutofilter": "Использовать автофильтр", "SSE.Views.ProtectDialog.txtDelCols": "Удалять столбцы", "SSE.Views.ProtectDialog.txtDelRows": "Удалять строки", "SSE.Views.ProtectDialog.txtEmpty": "Это поле необходимо заполнить", "SSE.Views.ProtectDialog.txtFormatCells": "Форматировать ячейки", "SSE.Views.ProtectDialog.txtFormatCols": "Форматировать столбцы", "SSE.Views.ProtectDialog.txtFormatRows": "Форматировать строки", "SSE.Views.ProtectDialog.txtIncorrectPwd": "Пароль и его подтверждение не совпадают", "SSE.Views.ProtectDialog.txtInsCols": "Вставлять столбцы", "SSE.Views.ProtectDialog.txtInsHyper": "Вставлять гиперссылку", "SSE.Views.ProtectDialog.txtInsRows": "Вставлять строки", "SSE.Views.ProtectDialog.txtObjs": "Редактировать объекты", "SSE.Views.ProtectDialog.txtOptional": "необязательно", "SSE.Views.ProtectDialog.txtPassword": "Пароль", "SSE.Views.ProtectDialog.txtPivot": "Использовать сводную таблицу и сводную диаграмму", "SSE.Views.ProtectDialog.txtProtect": "Защитить", "SSE.Views.ProtectDialog.txtRange": "Диа<PERSON>азон", "SSE.Views.ProtectDialog.txtRangeName": "Название", "SSE.Views.ProtectDialog.txtRepeat": "Повторить пароль", "SSE.Views.ProtectDialog.txtScen": "Редактировать сценарии", "SSE.Views.ProtectDialog.txtSelLocked": "Выделять заблокированные ячейки", "SSE.Views.ProtectDialog.txtSelUnLocked": "Выделять разблокированные ячейки", "SSE.Views.ProtectDialog.txtSheetDescription": "Запретите внесение нежелательных изменений другими пользователями путем ограничения их права на редактирование.", "SSE.Views.ProtectDialog.txtSheetTitle": "Защитить лист", "SSE.Views.ProtectDialog.txtSort": "Сортировать", "SSE.Views.ProtectDialog.txtWarning": "Внимание: Если пароль забыт или утерян, его нельзя восстановить. Храните его в надежном месте.", "SSE.Views.ProtectDialog.txtWBDescription": "Чтобы запретить другим пользователям просмотр скрытых листов, добавление, перемещение, удаление или скрытие листов и переименование листов, вы можете защитить структуру книги с помощью пароля.", "SSE.Views.ProtectDialog.txtWBTitle": "Защитить структуру книги", "SSE.Views.ProtectedRangesEditDlg.textAnonymous": "Аноним", "SSE.Views.ProtectedRangesEditDlg.textAnyone": "Любой", "SSE.Views.ProtectedRangesEditDlg.textCanEdit": "Редактирование", "SSE.Views.ProtectedRangesEditDlg.textCantView": "Отклонен", "SSE.Views.ProtectedRangesEditDlg.textCanView": "Просмотр", "SSE.Views.ProtectedRangesEditDlg.textInvalidName": "Название диапазона должно начинаться с буквы и может содержать только буквы, цифры и пробелы.", "SSE.Views.ProtectedRangesEditDlg.textInvalidRange": "ОШИБКА! Недопустимый диапазон ячеек", "SSE.Views.ProtectedRangesEditDlg.textRemove": "Удалить", "SSE.Views.ProtectedRangesEditDlg.textSelectData": "Выбор данных", "SSE.Views.ProtectedRangesEditDlg.textYou": "вы", "SSE.Views.ProtectedRangesEditDlg.txtAccess": "Доступ к диапазону", "SSE.Views.ProtectedRangesEditDlg.txtEmpty": "Это поле необходимо заполнить", "SSE.Views.ProtectedRangesEditDlg.txtProtect": "Защитить", "SSE.Views.ProtectedRangesEditDlg.txtRange": "Диа<PERSON>азон", "SSE.Views.ProtectedRangesEditDlg.txtRangeName": "Название", "SSE.Views.ProtectedRangesEditDlg.txtYouCanEdit": "Только вы можете редактировать этот диапазон", "SSE.Views.ProtectedRangesEditDlg.userPlaceholder": "Начните вводить имя или email", "SSE.Views.ProtectedRangesManagerDlg.guestText": "Гость", "SSE.Views.ProtectedRangesManagerDlg.lockText": "Заблокирован", "SSE.Views.ProtectedRangesManagerDlg.textDelete": "Удалить", "SSE.Views.ProtectedRangesManagerDlg.textEdit": "Изменить", "SSE.Views.ProtectedRangesManagerDlg.textEmpty": "Еще не создано ни одного защищенного диапазона.<br>Создайте хотя бы один защищенный диапазон, и он появится в этом поле.", "SSE.Views.ProtectedRangesManagerDlg.textFilter": "Фильтр", "SSE.Views.ProtectedRangesManagerDlg.textFilterAll": "Все", "SSE.Views.ProtectedRangesManagerDlg.textNew": "Новый", "SSE.Views.ProtectedRangesManagerDlg.textProtect": "Защитить лист", "SSE.Views.ProtectedRangesManagerDlg.textRange": "Диа<PERSON>азон", "SSE.Views.ProtectedRangesManagerDlg.textRangesDesc": "Вы можете ограничить редактирование или просмотр диапазонов для выбранных людей.", "SSE.Views.ProtectedRangesManagerDlg.textTitle": "Название", "SSE.Views.ProtectedRangesManagerDlg.tipIsLocked": "Этот элемент редактируется другим пользователем.", "SSE.Views.ProtectedRangesManagerDlg.txtAccess": "Доступ", "SSE.Views.ProtectedRangesManagerDlg.txtDenied": "Отклонен", "SSE.Views.ProtectedRangesManagerDlg.txtEdit": "Изменить", "SSE.Views.ProtectedRangesManagerDlg.txtEditRange": "Редактировать диапазон", "SSE.Views.ProtectedRangesManagerDlg.txtNewRange": "Новый диапазон", "SSE.Views.ProtectedRangesManagerDlg.txtTitle": "Защищенные диапазоны", "SSE.Views.ProtectedRangesManagerDlg.txtView": "Просмотр", "SSE.Views.ProtectedRangesManagerDlg.warnDelete": "Вы действительно хотите удалить защищенный диапазон {0}?<br><PERSON><PERSON><PERSON><PERSON><PERSON>, у кого есть права на редактирование электронной таблицы, сможет редактировать содержимое этого диапазона.", "SSE.Views.ProtectedRangesManagerDlg.warnDeleteRanges": "Вы действительно хотите удалить защищенные диапазоны?<br><PERSON><PERSON><PERSON><PERSON><PERSON>, у кого есть права на редактирование электронной таблицы, сможет редактировать содержимое этих диапазонов.", "SSE.Views.ProtectRangesDlg.guestText": "Гость", "SSE.Views.ProtectRangesDlg.lockText": "Заблокирован", "SSE.Views.ProtectRangesDlg.textDelete": "Удалить", "SSE.Views.ProtectRangesDlg.textEdit": "Редактировать", "SSE.Views.ProtectRangesDlg.textEmpty": "Нет диапазонов, разрешенных для редактирования.", "SSE.Views.ProtectRangesDlg.textNew": "Новый", "SSE.Views.ProtectRangesDlg.textProtect": "Защитить лист", "SSE.Views.ProtectRangesDlg.textPwd": "Пароль", "SSE.Views.ProtectRangesDlg.textRange": "Диа<PERSON>азон", "SSE.Views.ProtectRangesDlg.textRangesDesc": "Диапазоны защищенного листа, разблокируемые паролем (только для заблокированных ячеек)", "SSE.Views.ProtectRangesDlg.textTitle": "Название", "SSE.Views.ProtectRangesDlg.tipIsLocked": "Этот элемент редактируется другим пользователем.", "SSE.Views.ProtectRangesDlg.txtEditRange": "Редактировать диапазон", "SSE.Views.ProtectRangesDlg.txtNewRange": "Новый диапазон", "SSE.Views.ProtectRangesDlg.txtNo": "Нет", "SSE.Views.ProtectRangesDlg.txtTitle": "Разрешить пользователям редактировать диапазоны", "SSE.Views.ProtectRangesDlg.txtYes": "Да", "SSE.Views.ProtectRangesDlg.warnDelete": "Вы действительно хотите удалить имя {0}?", "SSE.Views.RemoveDuplicatesDialog.textColumns": "Столбцы", "SSE.Views.RemoveDuplicatesDialog.textDescription": "Чтобы удалить повторяющиеся значения, выделите один или несколько столбцов, содержащих их.", "SSE.Views.RemoveDuplicatesDialog.textHeaders": "Мои данные содержат заголовки", "SSE.Views.RemoveDuplicatesDialog.textSelectAll": "Выделить всё", "SSE.Views.RemoveDuplicatesDialog.txtTitle": "Удалить дубликаты", "SSE.Views.RightMenu.ariaRightMenu": "Правое меню", "SSE.Views.RightMenu.txtCellSettings": "Параметры ячейки", "SSE.Views.RightMenu.txtChartSettings": "Параметры диаграммы", "SSE.Views.RightMenu.txtImageSettings": "Параметры изображения", "SSE.Views.RightMenu.txtParagraphSettings": "Параметры абзаца", "SSE.Views.RightMenu.txtPivotSettings": "Параметры сводной таблицы", "SSE.Views.RightMenu.txtSettings": "Общие настройки", "SSE.Views.RightMenu.txtShapeSettings": "Параметры фигуры", "SSE.Views.RightMenu.txtSignatureSettings": "Настройка подписи", "SSE.Views.RightMenu.txtSlicerSettings": "Параметры среза", "SSE.Views.RightMenu.txtSparklineSettings": "Параметры спарклайна", "SSE.Views.RightMenu.txtTableSettings": "Параметры таблицы", "SSE.Views.RightMenu.txtTextArtSettings": "Параметры объектов Text Art", "SSE.Views.ScaleDialog.textAuto": "Авто", "SSE.Views.ScaleDialog.textError": "Введено неправильное значение.", "SSE.Views.ScaleDialog.textFewPages": "страницы", "SSE.Views.ScaleDialog.textFitTo": "Разместить не более чем на", "SSE.Views.ScaleDialog.textHeight": "Высота", "SSE.Views.ScaleDialog.textManyPages": "страницы", "SSE.Views.ScaleDialog.textOnePage": "страница", "SSE.Views.ScaleDialog.textScaleTo": "Установить", "SSE.Views.ScaleDialog.textTitle": "Настройки масштаба", "SSE.Views.ScaleDialog.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.SetValueDialog.txtMaxText": "Максимальное значение для этого поля - {0}", "SSE.Views.SetValueDialog.txtMinText": "Минимальное значение для этого поля - {0}", "SSE.Views.ShapeSettings.strBackground": "Цвет фона", "SSE.Views.ShapeSettings.strChange": "Изменить фигуру", "SSE.Views.ShapeSettings.strColor": "Цвет", "SSE.Views.ShapeSettings.strFill": "Заливка", "SSE.Views.ShapeSettings.strForeground": "Цвет переднего плана", "SSE.Views.ShapeSettings.strPattern": "Узор", "SSE.Views.ShapeSettings.strShadow": "Отображать тень", "SSE.Views.ShapeSettings.strSize": "Толщина", "SSE.Views.ShapeSettings.strStroke": "<PERSON>он<PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.strTransparency": "Непрозрачность", "SSE.Views.ShapeSettings.strType": "Тип", "SSE.Views.ShapeSettings.textAdjustShadow": "Настроить тень", "SSE.Views.ShapeSettings.textAdvanced": "Дополнительные параметры", "SSE.Views.ShapeSettings.textAngle": "Угол", "SSE.Views.ShapeSettings.textBorderSizeErr": "Введено некорректное значение.<br>Пожалуйста, введите значение от 0 до 1584 пунктов.", "SSE.Views.ShapeSettings.textColor": "Заливка цветом", "SSE.Views.ShapeSettings.textDirection": "Направление", "SSE.Views.ShapeSettings.textEditPoints": "Изменить точки", "SSE.Views.ShapeSettings.textEditShape": "Изменить фигуру", "SSE.Views.ShapeSettings.textEmptyPattern": "Без узора", "SSE.Views.ShapeSettings.textEyedropper": "Пипетка", "SSE.Views.ShapeSettings.textFlip": "Отразить", "SSE.Views.ShapeSettings.textFromFile": "Из файла", "SSE.Views.ShapeSettings.textFromStorage": "Из хранилища", "SSE.Views.ShapeSettings.textFromUrl": "По URL", "SSE.Views.ShapeSettings.textGradient": "Точки градиента", "SSE.Views.ShapeSettings.textGradientFill": "Градиентная заливка", "SSE.Views.ShapeSettings.textHint270": "Повернуть на 90° против часовой стрелки", "SSE.Views.ShapeSettings.textHint90": "Повернуть на 90° по часовой стрелке", "SSE.Views.ShapeSettings.textHintFlipH": "Отразить слева направо", "SSE.Views.ShapeSettings.textHintFlipV": "Отразить сверху вниз", "SSE.Views.ShapeSettings.textImageTexture": "Изображение или текстура", "SSE.Views.ShapeSettings.textLinear": "Линейный", "SSE.Views.ShapeSettings.textMoreColors": "Другие цвета", "SSE.Views.ShapeSettings.textNoFill": "Без заливки", "SSE.Views.ShapeSettings.textNoShadow": "Без тени", "SSE.Views.ShapeSettings.textOriginalSize": "Исходный размер", "SSE.Views.ShapeSettings.textPatternFill": "Узор", "SSE.Views.ShapeSettings.textPosition": "Положение", "SSE.Views.ShapeSettings.textRadial": "Радиальный", "SSE.Views.ShapeSettings.textRecentlyUsed": "Последние использованные", "SSE.Views.ShapeSettings.textRotate90": "Повернуть на 90°", "SSE.Views.ShapeSettings.textRotation": "Поворот", "SSE.Views.ShapeSettings.textSelectImage": "Выбрать изображение", "SSE.Views.ShapeSettings.textSelectTexture": "Выбрать", "SSE.Views.ShapeSettings.textShadow": "Тень", "SSE.Views.ShapeSettings.textStretch": "Растяжение", "SSE.Views.ShapeSettings.textStyle": "Стиль", "SSE.Views.ShapeSettings.textTexture": "Из текстуры", "SSE.Views.ShapeSettings.textTile": "Плитка", "SSE.Views.ShapeSettings.tipAddGradientPoint": "Добавить точку градиента", "SSE.Views.ShapeSettings.tipRemoveGradientPoint": "Удалить точку градиента", "SSE.Views.ShapeSettings.txtBrownPaper": "Крафт-бумага", "SSE.Views.ShapeSettings.txtCanvas": "Хол<PERSON>т", "SSE.Views.ShapeSettings.txtCarton": "Карт<PERSON>н", "SSE.Views.ShapeSettings.txtDarkFabric": "Темная ткань", "SSE.Views.ShapeSettings.txtGrain": "Песок", "SSE.Views.ShapeSettings.txtGranite": "<PERSON>р<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.ShapeSettings.txtGreyPaper": "Серая бумага", "SSE.Views.ShapeSettings.txtKnit": "Вязание", "SSE.Views.ShapeSettings.txtLeather": "Кожа", "SSE.Views.ShapeSettings.txtNoBorders": "Без контура", "SSE.Views.ShapeSettings.txtPapyrus": "Папир<PERSON>с", "SSE.Views.ShapeSettings.txtWood": "Дерево", "SSE.Views.ShapeSettingsAdvanced.strColumns": "Колонки", "SSE.Views.ShapeSettingsAdvanced.strMargins": "Поля вокруг текста", "SSE.Views.ShapeSettingsAdvanced.textAbsolute": "Не перемещать и не изменять размеры вместе с ячейками", "SSE.Views.ShapeSettingsAdvanced.textAlt": "Альтернативный текст", "SSE.Views.ShapeSettingsAdvanced.textAltDescription": "Описание", "SSE.Views.ShapeSettingsAdvanced.textAltTip": "Альтернативное текстовое представление информации о визуальном объекте, которое будет зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит изображение, фигура, диаграмма или таблица.", "SSE.Views.ShapeSettingsAdvanced.textAltTitle": "Заголовок", "SSE.Views.ShapeSettingsAdvanced.textAngle": "Угол", "SSE.Views.ShapeSettingsAdvanced.textArrows": "Стрелки", "SSE.Views.ShapeSettingsAdvanced.textAutofit": "Автоподбор", "SSE.Views.ShapeSettingsAdvanced.textBeginSize": "Начальный размер", "SSE.Views.ShapeSettingsAdvanced.textBeginStyle": "Начальный стиль", "SSE.Views.ShapeSettingsAdvanced.textBevel": "Скошенный", "SSE.Views.ShapeSettingsAdvanced.textBottom": "Снизу", "SSE.Views.ShapeSettingsAdvanced.textCapType": "Тип окончания", "SSE.Views.ShapeSettingsAdvanced.textColNumber": "Количество колонок", "SSE.Views.ShapeSettingsAdvanced.textEndSize": "Конечный размер", "SSE.Views.ShapeSettingsAdvanced.textEndStyle": "Конечный стиль", "SSE.Views.ShapeSettingsAdvanced.textFlat": "Плоский", "SSE.Views.ShapeSettingsAdvanced.textFlipped": "Отраж<PERSON>но", "SSE.Views.ShapeSettingsAdvanced.textHeight": "Высота", "SSE.Views.ShapeSettingsAdvanced.textHorizontally": "По горизонтали", "SSE.Views.ShapeSettingsAdvanced.textJoinType": "Тип соединения", "SSE.Views.ShapeSettingsAdvanced.textKeepRatio": "Сохранять пропорции", "SSE.Views.ShapeSettingsAdvanced.textLeft": "Слева", "SSE.Views.ShapeSettingsAdvanced.textLineStyle": "Стиль линии", "SSE.Views.ShapeSettingsAdvanced.textMiter": "Прям<PERSON>й", "SSE.Views.ShapeSettingsAdvanced.textOneCell": "Перемещать, но не изменять размеры вместе с ячейками", "SSE.Views.ShapeSettingsAdvanced.textOverflow": "Разрешить переполнение фигуры текстом", "SSE.Views.ShapeSettingsAdvanced.textResizeFit": "Подгонять размер фигуры под текст", "SSE.Views.ShapeSettingsAdvanced.textRight": "Справа", "SSE.Views.ShapeSettingsAdvanced.textRotation": "Поворот", "SSE.Views.ShapeSettingsAdvanced.textRound": "Закругленный", "SSE.Views.ShapeSettingsAdvanced.textSize": "Размер", "SSE.Views.ShapeSettingsAdvanced.textSnap": "Привязка к ячейке", "SSE.Views.ShapeSettingsAdvanced.textSpacing": "Интервал между колонками", "SSE.Views.ShapeSettingsAdvanced.textSquare": "Квадр<PERSON>тный", "SSE.Views.ShapeSettingsAdvanced.textTextBox": "Текстовое поле", "SSE.Views.ShapeSettingsAdvanced.textTitle": "Фигура - дополнительные параметры", "SSE.Views.ShapeSettingsAdvanced.textTop": "Сверху", "SSE.Views.ShapeSettingsAdvanced.textTwoCell": "Перемещать и изменять размеры вместе с ячейками", "SSE.Views.ShapeSettingsAdvanced.textVertically": "По вертикали", "SSE.Views.ShapeSettingsAdvanced.textWeightArrows": "Линии и стрелки", "SSE.Views.ShapeSettingsAdvanced.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.SignatureSettings.notcriticalErrorTitle": "Внимание", "SSE.Views.SignatureSettings.strDelete": "Удалить подпись", "SSE.Views.SignatureSettings.strDetails": "Состав подписи", "SSE.Views.SignatureSettings.strInvalid": "Недействительные подписи", "SSE.Views.SignatureSettings.strRequested": "Запрошенные подписи", "SSE.Views.SignatureSettings.strSetup": "Настройка подписи", "SSE.Views.SignatureSettings.strSign": "Подписать", "SSE.Views.SignatureSettings.strSignature": "Подпись", "SSE.Views.SignatureSettings.strSigner": "Подписывающий", "SSE.Views.SignatureSettings.strValid": "Действительные подписи", "SSE.Views.SignatureSettings.txtContinueEditing": "Все равно редактировать", "SSE.Views.SignatureSettings.txtEditWarning": "При редактировании из электронной таблицы будут удалены подписи.<br>Продолжить?", "SSE.Views.SignatureSettings.txtRemoveWarning": "Вы хотите удалить эту подпись?<br>Это нельзя отменить.", "SSE.Views.SignatureSettings.txtRequestedSignatures": "Эту таблицу требуется подписать.", "SSE.Views.SignatureSettings.txtSigned": "В электронную таблицу добавлены действительные подписи. Таблица защищена от редактирования.", "SSE.Views.SignatureSettings.txtSignedInvalid": "Некоторые из цифровых подписей в электронной таблице недействительны или их нельзя проверить. Таблица защищена от редактирования.", "SSE.Views.SlicerAddDialog.textColumns": "Столбцы", "SSE.Views.SlicerAddDialog.txtTitle": "Вставка срезов", "SSE.Views.SlicerSettings.strHideNoData": "Скрыть элементы без данных", "SSE.Views.SlicerSettings.strIndNoData": "Визуально выделять пустые элементы", "SSE.Views.SlicerSettings.strShowDel": "Показывать элементы, удаленные из источника данных", "SSE.Views.SlicerSettings.strShowNoData": "Показывать пустые элементы последними", "SSE.Views.SlicerSettings.strSorting": "Сортировка и фильтрация", "SSE.Views.SlicerSettings.textAdvanced": "Дополнительные параметры", "SSE.Views.SlicerSettings.textAsc": "По возрастанию", "SSE.Views.SlicerSettings.textAZ": "От А до Я", "SSE.Views.SlicerSettings.textButtons": "Кнопки", "SSE.Views.SlicerSettings.textColumns": "Столбцы", "SSE.Views.SlicerSettings.textDesc": "По убыванию", "SSE.Views.SlicerSettings.textHeight": "Высота", "SSE.Views.SlicerSettings.textHor": "По горизонтали", "SSE.Views.SlicerSettings.textKeepRatio": "Сохранять пропорции", "SSE.Views.SlicerSettings.textLargeSmall": "от большего к меньшему", "SSE.Views.SlicerSettings.textLock": "Отключить изменение размера или перемещение", "SSE.Views.SlicerSettings.textNewOld": "от новых к старым", "SSE.Views.SlicerSettings.textOldNew": "от старых к новым", "SSE.Views.SlicerSettings.textPosition": "Положение", "SSE.Views.SlicerSettings.textSize": "Размер", "SSE.Views.SlicerSettings.textSmallLarge": "от меньшего к большему", "SSE.Views.SlicerSettings.textStyle": "Стиль", "SSE.Views.SlicerSettings.textVert": "По вертикали", "SSE.Views.SlicerSettings.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettings.textZA": "От Я до А", "SSE.Views.SlicerSettingsAdvanced.strButtons": "Кнопки", "SSE.Views.SlicerSettingsAdvanced.strColumns": "Столбцы", "SSE.Views.SlicerSettingsAdvanced.strHeight": "Высота", "SSE.Views.SlicerSettingsAdvanced.strHideNoData": "Скрыть элементы без данных", "SSE.Views.SlicerSettingsAdvanced.strIndNoData": "Визуально выделять пустые элементы", "SSE.Views.SlicerSettingsAdvanced.strReferences": "Ссылки", "SSE.Views.SlicerSettingsAdvanced.strShowDel": "Показывать элементы, удаленные из источника данных", "SSE.Views.SlicerSettingsAdvanced.strShowHeader": "Показывать заголовок", "SSE.Views.SlicerSettingsAdvanced.strShowNoData": "Показывать пустые элементы последними", "SSE.Views.SlicerSettingsAdvanced.strSize": "Размер", "SSE.Views.SlicerSettingsAdvanced.strSorting": "Сортировка и фильтрация", "SSE.Views.SlicerSettingsAdvanced.strStyle": "Стиль", "SSE.Views.SlicerSettingsAdvanced.strStyleSize": "Стиль и размер", "SSE.Views.SlicerSettingsAdvanced.strWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.SlicerSettingsAdvanced.textAbsolute": "Не перемещать и не изменять размеры вместе с ячейками", "SSE.Views.SlicerSettingsAdvanced.textAlt": "Альтернативный текст", "SSE.Views.SlicerSettingsAdvanced.textAltDescription": "Описание", "SSE.Views.SlicerSettingsAdvanced.textAltTip": "Альтернативное текстовое представление информации о визуальном объекте, которое будет зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит изображение, фигура, диаграмма или таблица.", "SSE.Views.SlicerSettingsAdvanced.textAltTitle": "Название", "SSE.Views.SlicerSettingsAdvanced.textAsc": "По возрастанию", "SSE.Views.SlicerSettingsAdvanced.textAZ": "От А до Я", "SSE.Views.SlicerSettingsAdvanced.textDesc": "По убыванию", "SSE.Views.SlicerSettingsAdvanced.textFormulaName": "Имя для использования в формулах", "SSE.Views.SlicerSettingsAdvanced.textHeader": "Заголовок", "SSE.Views.SlicerSettingsAdvanced.textKeepRatio": "Сохранять пропорции", "SSE.Views.SlicerSettingsAdvanced.textLargeSmall": "от большего к меньшему", "SSE.Views.SlicerSettingsAdvanced.textName": "Имя", "SSE.Views.SlicerSettingsAdvanced.textNewOld": "от новых к старым", "SSE.Views.SlicerSettingsAdvanced.textOldNew": "от старых к новым", "SSE.Views.SlicerSettingsAdvanced.textOneCell": "Перемещать, но не изменять размеры вместе с ячейками", "SSE.Views.SlicerSettingsAdvanced.textSmallLarge": "от меньшего к большему", "SSE.Views.SlicerSettingsAdvanced.textSnap": "Привязка к ячейке", "SSE.Views.SlicerSettingsAdvanced.textSort": "Сортировать", "SSE.Views.SlicerSettingsAdvanced.textSourceName": "Имя источника", "SSE.Views.SlicerSettingsAdvanced.textTitle": "Срез - дополнительные параметры", "SSE.Views.SlicerSettingsAdvanced.textTwoCell": "Перемещать и изменять размеры вместе с ячейками", "SSE.Views.SlicerSettingsAdvanced.textZA": "От Я до А", "SSE.Views.SlicerSettingsAdvanced.txtEmpty": "Это поле необходимо заполнить", "SSE.Views.SortDialog.errorEmpty": "Для каждого условия сортировки должен быть указан столбец или строка.", "SSE.Views.SortDialog.errorMoreOneCol": "Выделено несколько столбцов.", "SSE.Views.SortDialog.errorMoreOneRow": "Выделено несколько строк.", "SSE.Views.SortDialog.errorNotOriginalCol": "Выбранный столбец не принадлежит к исходному выделенному диапазону.", "SSE.Views.SortDialog.errorNotOriginalRow": "Выбранная строка не принадлежит к исходному выделенному диапазону.", "SSE.Views.SortDialog.errorSameColumnColor": "Сортировка строки или столбца %1 по одному и тому же цвету выполняется более одного раза.<br>Удалите повторные условия сортировки и повторите попытку.", "SSE.Views.SortDialog.errorSameColumnValue": "Сортировка строки или столбца %1 по значениям выполняется более одного раза.<br>Удалите повторные условия сортировки и повторите попытку.", "SSE.Views.SortDialog.textAsc": "По возрастанию", "SSE.Views.SortDialog.textAuto": "Автоматически", "SSE.Views.SortDialog.textAZ": "От А до Я", "SSE.Views.SortDialog.textBelow": "Снизу", "SSE.Views.SortDialog.textBtnCopy": "Копировать", "SSE.Views.SortDialog.textBtnDelete": "Удалить", "SSE.Views.SortDialog.textBtnNew": "Новый", "SSE.Views.SortDialog.textCellColor": "Цвет ячейки", "SSE.Views.SortDialog.textColumn": "Столбец", "SSE.Views.SortDialog.textDesc": "По убыванию", "SSE.Views.SortDialog.textDown": "Переместить уровень вниз", "SSE.Views.SortDialog.textFontColor": "Цвет шрифта", "SSE.Views.SortDialog.textLeft": "Слева", "SSE.Views.SortDialog.textLevels": "Уровни", "SSE.Views.SortDialog.textMoreCols": "(Другие столбцы...)", "SSE.Views.SortDialog.textMoreRows": "(Другие строки...)", "SSE.Views.SortDialog.textNone": "Нет", "SSE.Views.SortDialog.textOptions": "Параметры", "SSE.Views.SortDialog.textOrder": "Порядок", "SSE.Views.SortDialog.textRight": "Справа", "SSE.Views.SortDialog.textRow": "Строка", "SSE.Views.SortDialog.textSort": "Сортировка", "SSE.Views.SortDialog.textSortBy": "Сортировать по", "SSE.Views.SortDialog.textThenBy": "Затем по", "SSE.Views.SortDialog.textTop": "Сверху", "SSE.Views.SortDialog.textUp": "Переместить уровень вверх", "SSE.Views.SortDialog.textValues": "Значения", "SSE.Views.SortDialog.textZA": "От Я до А", "SSE.Views.SortDialog.txtInvalidRange": "Недопустимый диапазон ячеек.", "SSE.Views.SortDialog.txtTitle": "Сортировка", "SSE.Views.SortFilterDialog.textAsc": "По возрастанию (от А до Я) по", "SSE.Views.SortFilterDialog.textDesc": "По убыванию (от Я до А) по", "SSE.Views.SortFilterDialog.textNoSort": "Без сортировки", "SSE.Views.SortFilterDialog.txtTitle": "Сортировать", "SSE.Views.SortFilterDialog.txtTitleValue": "Сортировка по значению", "SSE.Views.SortOptionsDialog.textCase": "С учетом регистра", "SSE.Views.SortOptionsDialog.textHeaders": "Мои данные содержат заголовки", "SSE.Views.SortOptionsDialog.textLeftRight": "Сортировать слева направо", "SSE.Views.SortOptionsDialog.textOrientation": "Ориентация", "SSE.Views.SortOptionsDialog.textTitle": "Параметры сортировки", "SSE.Views.SortOptionsDialog.textTopBottom": "Сортировать сверху вниз", "SSE.Views.SpecialPasteDialog.textAdd": "Сложение", "SSE.Views.SpecialPasteDialog.textAll": "Все", "SSE.Views.SpecialPasteDialog.textBlanks": "Пропускать пустые ячейки", "SSE.Views.SpecialPasteDialog.textColWidth": "<PERSON><PERSON><PERSON><PERSON><PERSON> столбцов", "SSE.Views.SpecialPasteDialog.textComments": "Комментарии", "SSE.Views.SpecialPasteDialog.textDiv": "Деление", "SSE.Views.SpecialPasteDialog.textFFormat": "Формулы и форматирование", "SSE.Views.SpecialPasteDialog.textFNFormat": "Формулы и форматы чисел", "SSE.Views.SpecialPasteDialog.textFormats": "Форматы", "SSE.Views.SpecialPasteDialog.textFormulas": "Формулы", "SSE.Views.SpecialPasteDialog.textFWidth": "Формулы и ширина столбцов", "SSE.Views.SpecialPasteDialog.textMult": "Умножение", "SSE.Views.SpecialPasteDialog.textNone": "Нет", "SSE.Views.SpecialPasteDialog.textOperation": "Операция", "SSE.Views.SpecialPasteDialog.textPaste": "Вставить", "SSE.Views.SpecialPasteDialog.textSub": "Вычитание", "SSE.Views.SpecialPasteDialog.textTitle": "Специальная вставка", "SSE.Views.SpecialPasteDialog.textTranspose": "Транспонировать", "SSE.Views.SpecialPasteDialog.textValues": "Значения", "SSE.Views.SpecialPasteDialog.textVFormat": "Значения и форматирование", "SSE.Views.SpecialPasteDialog.textVNFormat": "Значения и форматы чисел", "SSE.Views.SpecialPasteDialog.textWBorders": "Без рамки", "SSE.Views.Spellcheck.noSuggestions": "Вариантов не найдено", "SSE.Views.Spellcheck.textChange": "Заменить", "SSE.Views.Spellcheck.textChangeAll": "Заменить все", "SSE.Views.Spellcheck.textIgnore": "Пропустить", "SSE.Views.Spellcheck.textIgnoreAll": "Пропустить все", "SSE.Views.Spellcheck.txtAddToDictionary": "Добавить в словарь", "SSE.Views.Spellcheck.txtClosePanel": "Закрыть проверку правописания", "SSE.Views.Spellcheck.txtComplete": "Проверка орфографии закончена", "SSE.Views.Spellcheck.txtDictionaryLanguage": "Язык словаря", "SSE.Views.Spellcheck.txtNextTip": "Перейти к следующему слову", "SSE.Views.Spellcheck.txtSpelling": "Орфография", "SSE.Views.Statusbar.CopyDialog.itemMoveToEnd": "(Переместить в конец)", "SSE.Views.Statusbar.CopyDialog.textCreateCopy": "Создать копию", "SSE.Views.Statusbar.CopyDialog.textCreateNewSpreadsheet": "(Создать новую электронную таблицу)", "SSE.Views.Statusbar.CopyDialog.textMoveBefore": "Переместить перед листом", "SSE.Views.Statusbar.CopyDialog.textSpreadsheet": "Электронная таблица", "SSE.Views.Statusbar.filteredRecordsText": "Отфильтрованно записей: {0} из {1}", "SSE.Views.Statusbar.filteredText": "Режим фильтрации", "SSE.Views.Statusbar.itemAverage": "Среднее", "SSE.Views.Statusbar.itemCount": "Количество", "SSE.Views.Statusbar.itemDelete": "Удалить", "SSE.Views.Statusbar.itemHidden": "Скрытые", "SSE.Views.Statusbar.itemHide": "Скрыть", "SSE.Views.Statusbar.itemInsert": "Вставить", "SSE.Views.Statusbar.itemMaximum": "Максимум", "SSE.Views.Statusbar.itemMinimum": "Мини<PERSON>ум", "SSE.Views.Statusbar.itemMoveOrCopy": "Переместить или скопировать", "SSE.Views.Statusbar.itemProtect": "Защитить", "SSE.Views.Statusbar.itemRename": "Переименовать", "SSE.Views.Statusbar.itemStatus": "Статус сохранения", "SSE.Views.Statusbar.itemSum": "Сумма", "SSE.Views.Statusbar.itemTabColor": "Цвет ярлычка", "SSE.Views.Statusbar.itemUnProtect": "Снять защиту", "SSE.Views.Statusbar.RenameDialog.errNameExists": "Лист с таким именем уже существует.", "SSE.Views.Statusbar.RenameDialog.errNameWrongChar": "Имя листа не может содержать следующие символы: \\/*?[]: или символ ' в начале или в конце", "SSE.Views.Statusbar.RenameDialog.labelSheetName": "Имя листа", "SSE.Views.Statusbar.selectAllSheets": "Выбрать все листы", "SSE.Views.Statusbar.sheetIndexText": "Лист {0} из {1}", "SSE.Views.Statusbar.textAverage": "Среднее", "SSE.Views.Statusbar.textCount": "Количество", "SSE.Views.Statusbar.textMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.Statusbar.textMin": "<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.textNewColor": "Другие цвета", "SSE.Views.Statusbar.textNoColor": "Без цвета", "SSE.Views.Statusbar.textSum": "Сумма", "SSE.Views.Statusbar.tipAddTab": "Добавить лист", "SSE.Views.Statusbar.tipFirst": "Прокрутить до первого листа", "SSE.Views.Statusbar.tipLast": "Прокрутить до последнего листа", "SSE.Views.Statusbar.tipListOfSheets": "Список листов", "SSE.Views.Statusbar.tipNext": "Прокрутить список листов вправо", "SSE.Views.Statusbar.tipPrev": "Прокрутить список листов влево", "SSE.Views.Statusbar.tipZoomFactor": "Масш<PERSON><PERSON><PERSON>", "SSE.Views.Statusbar.tipZoomIn": "Увеличить", "SSE.Views.Statusbar.tipZoomOut": "Уменьшить", "SSE.Views.Statusbar.ungroupSheets": "Разгруппировать листы", "SSE.Views.Statusbar.zoomText": "Мас<PERSON>т<PERSON>б {0}%", "SSE.Views.TableOptionsDialog.errorAutoFilterDataRange": "Операция не может быть произведена для выбранного диапазона ячеек.<br>Выделите однородный диапазон данных, отличный от существующего, и повторите попытку.", "SSE.Views.TableOptionsDialog.errorFTChangeTableRangeError": "Не удалось выполнить операцию для выбранного диапазона ячеек.<br>Выделите диапазон так, чтобы первая строка таблицы находилась на той же самой строке,<br>а итоговая таблица перекрывала текущую.", "SSE.Views.TableOptionsDialog.errorFTRangeIncludedOtherTables": "Не удалось выполнить операцию для выбранного диапазона ячеек.<br>Выберите диапазон, который не содержит других таблиц.", "SSE.Views.TableOptionsDialog.errorMultiCellFormula": "Формулы массива с несколькими ячейками не разрешаются в таблицах.", "SSE.Views.TableOptionsDialog.txtEmpty": "Это поле необходимо заполнить", "SSE.Views.TableOptionsDialog.txtFormat": "Создать таблицу", "SSE.Views.TableOptionsDialog.txtInvalidRange": "ОШИБКА! Недопустимый диапазон ячеек", "SSE.Views.TableOptionsDialog.txtNote": "Заголовки должны оставаться в той же строке, а результирующий диапазон таблицы - частично перекрываться с исходным диапазоном.", "SSE.Views.TableOptionsDialog.txtTitle": "Заголовок", "SSE.Views.TableSettings.deleteColumnText": "Удалить столбец", "SSE.Views.TableSettings.deleteRowText": "Удалить строку", "SSE.Views.TableSettings.deleteTableText": "Удалить таблицу", "SSE.Views.TableSettings.insertColumnLeftText": "Вставить столбец слева", "SSE.Views.TableSettings.insertColumnRightText": "Вставить столбец справа", "SSE.Views.TableSettings.insertRowAboveText": "Вставить строку выше", "SSE.Views.TableSettings.insertRowBelowText": "Вставить строку ниже", "SSE.Views.TableSettings.notcriticalErrorTitle": "Внимание", "SSE.Views.TableSettings.selectColumnText": "Выделить весь столбец", "SSE.Views.TableSettings.selectDataText": "Выделить данные столбцов", "SSE.Views.TableSettings.selectRowText": "Выделить строку", "SSE.Views.TableSettings.selectTableText": "Выделить таблицу", "SSE.Views.TableSettings.textActions": "Действия над таблицами", "SSE.Views.TableSettings.textAdvanced": "Дополнительные параметры", "SSE.Views.TableSettings.textBanded": "Чередовать", "SSE.Views.TableSettings.textColumns": "Столбцы", "SSE.Views.TableSettings.textConvertRange": "Преобразовать в диапазон", "SSE.Views.TableSettings.textEdit": "Строки и столбцы", "SSE.Views.TableSettings.textEmptyTemplate": "Без ша<PERSON><PERSON><PERSON>нов", "SSE.Views.TableSettings.textExistName": "ОШИБКА! Диапазон с таким именем уже существует", "SSE.Views.TableSettings.textFilter": "Кнопка фильтра", "SSE.Views.TableSettings.textFirst": "Первый", "SSE.Views.TableSettings.textHeader": "Заголовок", "SSE.Views.TableSettings.textInvalidName": "Ошибка! Недопустимое имя таблицы", "SSE.Views.TableSettings.textIsLocked": "Этот элемент редактируется другим пользователем.", "SSE.Views.TableSettings.textLast": "Последний", "SSE.Views.TableSettings.textLongOperation": "Длительная операция", "SSE.Views.TableSettings.textPivot": "Вставить сводную таблицу", "SSE.Views.TableSettings.textRemDuplicates": "Удалить дубликаты", "SSE.Views.TableSettings.textReservedName": "Формулы в ячейках уже содержат ссылки на имя, которое вы пытаетесь использовать. Используйте другое имя.", "SSE.Views.TableSettings.textResize": "Размер таблицы", "SSE.Views.TableSettings.textRows": "Строки", "SSE.Views.TableSettings.textSelectData": "Выбор данных", "SSE.Views.TableSettings.textSlicer": "Вставить срез", "SSE.Views.TableSettings.textTableName": "Имя таблицы", "SSE.Views.TableSettings.textTemplate": "По шаблону", "SSE.Views.TableSettings.textTotal": "Итоговая", "SSE.Views.TableSettings.txtGroupTable_Custom": "Пользовательские", "SSE.Views.TableSettings.txtGroupTable_Dark": "Темные", "SSE.Views.TableSettings.txtGroupTable_Light": "Светлые", "SSE.Views.TableSettings.txtGroupTable_Medium": "Средние", "SSE.Views.TableSettings.txtTable_TableStyleDark": "Стиль таблицы: темный", "SSE.Views.TableSettings.txtTable_TableStyleLight": "Стиль таблицы: светлый", "SSE.Views.TableSettings.txtTable_TableStyleMedium": "Стиль таблицы: средний", "SSE.Views.TableSettings.warnLongOperation": "Для завершения операции, которую вы собираетесь выполнить, может потребоваться довольно много времени.<br>Вы действительно хотите продолжить?", "SSE.Views.TableSettingsAdvanced.textAlt": "Альтернативный текст", "SSE.Views.TableSettingsAdvanced.textAltDescription": "Описание", "SSE.Views.TableSettingsAdvanced.textAltTip": "Альтернативное текстовое представление информации о визуальном объекте, которое будет зачитываться для людей с нарушениями зрения или когнитивными нарушениями, чтобы помочь им лучше понять, какую информацию содержит изображение, фигура, диаграмма или таблица.", "SSE.Views.TableSettingsAdvanced.textAltTitle": "Заголовок", "SSE.Views.TableSettingsAdvanced.textTitle": "Таблица - дополнительные параметры", "SSE.Views.TextArtSettings.strBackground": "Цвет фона", "SSE.Views.TextArtSettings.strColor": "Цвет", "SSE.Views.TextArtSettings.strFill": "Заливка", "SSE.Views.TextArtSettings.strForeground": "Цвет переднего плана", "SSE.Views.TextArtSettings.strPattern": "Узор", "SSE.Views.TextArtSettings.strSize": "Толщина", "SSE.Views.TextArtSettings.strStroke": "<PERSON>он<PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.strTransparency": "Непрозрачность", "SSE.Views.TextArtSettings.strType": "Тип", "SSE.Views.TextArtSettings.textAngle": "Угол", "SSE.Views.TextArtSettings.textBorderSizeErr": "Введено некорректное значение.<br>Пожалуйста, введите значение от 0 до 1584 пунктов.", "SSE.Views.TextArtSettings.textColor": "Заливка цветом", "SSE.Views.TextArtSettings.textDirection": "Направление", "SSE.Views.TextArtSettings.textEmptyPattern": "Без узора", "SSE.Views.TextArtSettings.textFromFile": "Из файла", "SSE.Views.TextArtSettings.textFromUrl": "По URL", "SSE.Views.TextArtSettings.textGradient": "Точки градиента", "SSE.Views.TextArtSettings.textGradientFill": "Градиентная заливка", "SSE.Views.TextArtSettings.textImageTexture": "Изображение или текстура", "SSE.Views.TextArtSettings.textLinear": "Линейный", "SSE.Views.TextArtSettings.textNoFill": "Без заливки", "SSE.Views.TextArtSettings.textPatternFill": "Узор", "SSE.Views.TextArtSettings.textPosition": "Положение", "SSE.Views.TextArtSettings.textRadial": "Радиальный", "SSE.Views.TextArtSettings.textSelectTexture": "Выбрать", "SSE.Views.TextArtSettings.textStretch": "Растяжение", "SSE.Views.TextArtSettings.textStyle": "Стиль", "SSE.Views.TextArtSettings.textTemplate": "Шабл<PERSON>н", "SSE.Views.TextArtSettings.textTexture": "Из текстуры", "SSE.Views.TextArtSettings.textTile": "Плитка", "SSE.Views.TextArtSettings.textTransform": "Трансформация", "SSE.Views.TextArtSettings.tipAddGradientPoint": "Добавить точку градиента", "SSE.Views.TextArtSettings.tipRemoveGradientPoint": "Удалить точку градиента", "SSE.Views.TextArtSettings.txtBrownPaper": "Крафт-бумага", "SSE.Views.TextArtSettings.txtCanvas": "Хол<PERSON>т", "SSE.Views.TextArtSettings.txtCarton": "Карт<PERSON>н", "SSE.Views.TextArtSettings.txtDarkFabric": "Темная ткань", "SSE.Views.TextArtSettings.txtGrain": "Песок", "SSE.Views.TextArtSettings.txtGranite": "<PERSON>р<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.TextArtSettings.txtGreyPaper": "Серая бумага", "SSE.Views.TextArtSettings.txtKnit": "Вязание", "SSE.Views.TextArtSettings.txtLeather": "Кожа", "SSE.Views.TextArtSettings.txtNoBorders": "Без контура", "SSE.Views.TextArtSettings.txtPapyrus": "Папир<PERSON>с", "SSE.Views.TextArtSettings.txtWood": "Дерево", "SSE.Views.Toolbar.capBtnAddComment": "Добавить комментарий", "SSE.Views.Toolbar.capBtnColorSchemas": "Цвета", "SSE.Views.Toolbar.capBtnComment": "Комментарий", "SSE.Views.Toolbar.capBtnInsHeader": "Колонтитулы", "SSE.Views.Toolbar.capBtnInsSlicer": "Срез", "SSE.Views.Toolbar.capBtnInsSmartArt": "SmartArt", "SSE.Views.Toolbar.capBtnInsSymbol": "Символ", "SSE.Views.Toolbar.capBtnMargins": "Поля", "SSE.Views.Toolbar.capBtnPageBreak": "Разрывы", "SSE.Views.Toolbar.capBtnPageOrient": "Ориентация", "SSE.Views.Toolbar.capBtnPageSize": "Размер", "SSE.Views.Toolbar.capBtnPrintArea": "Область печати", "SSE.Views.Toolbar.capBtnPrintTitles": "Печатать заголовки", "SSE.Views.Toolbar.capBtnScale": "Вписать", "SSE.Views.Toolbar.capImgAlign": "Выравнивание", "SSE.Views.Toolbar.capImgBackward": "Перенести назад", "SSE.Views.Toolbar.capImgForward": "Перенести вперед", "SSE.Views.Toolbar.capImgGroup": "Группировка", "SSE.Views.Toolbar.capInsertChart": "Диаграмма", "SSE.Views.Toolbar.capInsertChartRecommend": "Рекомендованная диаграмма", "SSE.Views.Toolbar.capInsertEquation": "Уравнение", "SSE.Views.Toolbar.capInsertHyperlink": "Гиперссылка", "SSE.Views.Toolbar.capInsertImage": "Изображение", "SSE.Views.Toolbar.capInsertShape": "Фигура", "SSE.Views.Toolbar.capInsertSpark": "Спарклайн", "SSE.Views.Toolbar.capInsertTable": "Таблица", "SSE.Views.Toolbar.capInsertText": "Надпись", "SSE.Views.Toolbar.capInsertTextart": "Text Art", "SSE.Views.Toolbar.capShapesMerge": "Объединить фигуры", "SSE.Views.Toolbar.mniCapitalizeWords": "Каждое Слово С Прописной", "SSE.Views.Toolbar.mniImageFromFile": "Изображение из файла", "SSE.Views.Toolbar.mniImageFromStorage": "Изображение из хранилища", "SSE.Views.Toolbar.mniImageFromUrl": "Изображение по URL", "SSE.Views.Toolbar.mniLowerCase": "нижний регистр", "SSE.Views.Toolbar.mniSentenceCase": "Как в предложениях.", "SSE.Views.Toolbar.mniToggleCase": "иЗМЕНИТЬ рЕГИСТР", "SSE.Views.Toolbar.mniUpperCase": "ВЕРХНИЙ РЕГИСТР", "SSE.Views.Toolbar.textAddPrintArea": "Добавить в область печати", "SSE.Views.Toolbar.textAlignBottom": "По нижнему краю", "SSE.Views.Toolbar.textAlignCenter": "По центру", "SSE.Views.Toolbar.textAlignJust": "По ширине", "SSE.Views.Toolbar.textAlignLeft": "По левому краю", "SSE.Views.Toolbar.textAlignMiddle": "По середине", "SSE.Views.Toolbar.textAlignRight": "По правому краю", "SSE.Views.Toolbar.textAlignTop": "По верхнему краю", "SSE.Views.Toolbar.textAllBorders": "Все границы", "SSE.Views.Toolbar.textAlpha": "Греческая строчная буква альфа", "SSE.Views.Toolbar.textAuto": "Авто", "SSE.Views.Toolbar.textAutoColor": "Автоматический", "SSE.Views.Toolbar.textBetta": "Греческая строчная буква бета", "SSE.Views.Toolbar.textBlackHeart": "Черное сердце", "SSE.Views.Toolbar.textBold": "Полужирный", "SSE.Views.Toolbar.textBordersColor": "Цвет границ", "SSE.Views.Toolbar.textBordersStyle": "Стиль границ", "SSE.Views.Toolbar.textBottom": "Нижнее: ", "SSE.Views.Toolbar.textBottomBorders": "Нижние границы", "SSE.Views.Toolbar.textBullet": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textCellAlign": "Форматировать выравнивание ячеек", "SSE.Views.Toolbar.textCenterBorders": "Внутренние вертикальные границы", "SSE.Views.Toolbar.textClearPrintArea": "Очистить область печати", "SSE.Views.Toolbar.textClearRule": "Удалить правила", "SSE.Views.Toolbar.textClockwise": "Текст по часовой стрелке", "SSE.Views.Toolbar.textColorScales": "Цветовые шкалы", "SSE.Views.Toolbar.textCopyright": "Знак авторского права", "SSE.Views.Toolbar.textCounterCw": "Текст против часовой стрелки", "SSE.Views.Toolbar.textCustom": "Пользовательские", "SSE.Views.Toolbar.textDataBars": "Гистограммы", "SSE.Views.Toolbar.textDegree": "Знак градуса", "SSE.Views.Toolbar.textDelLeft": "Ячейки со сдвигом влево", "SSE.Views.Toolbar.textDelPageBreak": "Удалить разрыв страницы", "SSE.Views.Toolbar.textDelta": "Греческая строчная буква дельта", "SSE.Views.Toolbar.textDelUp": "Ячейки со сдвигом вверх", "SSE.Views.Toolbar.textDiagDownBorder": "Диагональная граница сверху вниз", "SSE.Views.Toolbar.textDiagUpBorder": "Диагональная граница снизу вверх", "SSE.Views.Toolbar.textDivision": "Знак деления", "SSE.Views.Toolbar.textDollar": "Знак доллара", "SSE.Views.Toolbar.textDone": "Готово", "SSE.Views.Toolbar.textDown": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textEditVA": "Редактировать видимую область", "SSE.Views.Toolbar.textEntireCol": "Столбец", "SSE.Views.Toolbar.textEntireRow": "Строку", "SSE.Views.Toolbar.textEuro": "Знак евро", "SSE.Views.Toolbar.textFewPages": "стран<PERSON><PERSON>", "SSE.Views.Toolbar.textFillLeft": "Влево", "SSE.Views.Toolbar.textFillRight": "Вправо", "SSE.Views.Toolbar.textFormatCellFill": "Форматировать заливку ячеек", "SSE.Views.Toolbar.textGreaterEqual": "Больше или равно", "SSE.Views.Toolbar.textHeight": "Высота", "SSE.Views.Toolbar.textHideVA": "Скрыть видимую область", "SSE.Views.Toolbar.textHorizontal": "Горизонтальный текст", "SSE.Views.Toolbar.textInfinity": "Бесконечность", "SSE.Views.Toolbar.textInsDown": "Ячейки со сдвигом вниз", "SSE.Views.Toolbar.textInsideBorders": "Внутренние границы", "SSE.Views.Toolbar.textInsPageBreak": "Вставить разрыв страницы", "SSE.Views.Toolbar.textInsRight": "Ячейки со сдвигом вправо", "SSE.Views.Toolbar.textItalic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textItems": "Элементы", "SSE.Views.Toolbar.textLandscape": "Альбомная", "SSE.Views.Toolbar.textLeft": "Левое: ", "SSE.Views.Toolbar.textLeftBorders": "Левые границы", "SSE.Views.Toolbar.textLessEqual": "Мееньше или равно", "SSE.Views.Toolbar.textLetterPi": "Греческая строчная буква пи", "SSE.Views.Toolbar.textManageRule": "Управление правилами", "SSE.Views.Toolbar.textManyPages": "стран<PERSON><PERSON>", "SSE.Views.Toolbar.textMarginsLast": "Последние настраиваемые", "SSE.Views.Toolbar.textMarginsNarrow": "Узкие", "SSE.Views.Toolbar.textMarginsNormal": "Обычные", "SSE.Views.Toolbar.textMarginsWide": "Широкие", "SSE.Views.Toolbar.textMiddleBorders": "Внутренние горизонтальные границы", "SSE.Views.Toolbar.textMoreBorders": "Другие границы", "SSE.Views.Toolbar.textMoreFormats": "Другие форматы", "SSE.Views.Toolbar.textMorePages": "Другие страницы", "SSE.Views.Toolbar.textMoreSymbols": "Другие символы", "SSE.Views.Toolbar.textNewColor": "Другие цвета", "SSE.Views.Toolbar.textNewRule": "Новое правило", "SSE.Views.Toolbar.textNoBorders": "Без границ", "SSE.Views.Toolbar.textNotEqualTo": "Не равно", "SSE.Views.Toolbar.textOneHalf": "Обыкновенная дробь - одна вторая", "SSE.Views.Toolbar.textOnePage": "страница", "SSE.Views.Toolbar.textOneQuarter": "Обыкновенная дробь - одна четвертая", "SSE.Views.Toolbar.textOutBorders": "Внешние границы", "SSE.Views.Toolbar.textPageMarginsCustom": "Настраиваемые поля", "SSE.Views.Toolbar.textPlusMinus": "Знак плюс-минус", "SSE.Views.Toolbar.textPortrait": "Книжная", "SSE.Views.Toolbar.textPrint": "Печать", "SSE.Views.Toolbar.textPrintGridlines": "Печать сетки", "SSE.Views.Toolbar.textPrintHeadings": "Печать заголовков", "SSE.Views.Toolbar.textPrintOptions": "Параметры печати", "SSE.Views.Toolbar.textRegistered": "Зарегистрированный товарный знак", "SSE.Views.Toolbar.textResetPageBreak": "Сбросить все разрывы страниц", "SSE.Views.Toolbar.textRight": "Правое: ", "SSE.Views.Toolbar.textRightBorders": "Правые границы", "SSE.Views.Toolbar.textRotateDown": "Повернуть текст вниз", "SSE.Views.Toolbar.textRotateUp": "Повернуть текст вверх", "SSE.Views.Toolbar.textRtlSheet": "Лист справа налево", "SSE.Views.Toolbar.textScale": "Масш<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textScaleCustom": "Особый", "SSE.Views.Toolbar.textSection": "Знак раздела", "SSE.Views.Toolbar.textSelection": "Из текущего выделенного фрагмента", "SSE.Views.Toolbar.textSeries": "Прогрессия", "SSE.Views.Toolbar.textSetPrintArea": "Задать область печати", "SSE.Views.Toolbar.textShapesCombine": "Группирование", "SSE.Views.Toolbar.textShapesFragment": "Фрагмент", "SSE.Views.Toolbar.textShapesIntersect": "Пересечение", "SSE.Views.Toolbar.textShapesSubstract": "Вычитание", "SSE.Views.Toolbar.textShapesUnion": "Объединение", "SSE.Views.Toolbar.textShowVA": "Показать видимую область", "SSE.Views.Toolbar.textSmile": "Улыбающееся лицо", "SSE.Views.Toolbar.textSquareRoot": "Квадратный корень", "SSE.Views.Toolbar.textStrikeout": "Зачёркнутый", "SSE.Views.Toolbar.textSubscript": "Подстрочные знаки", "SSE.Views.Toolbar.textSubSuperscript": "Подстрочные/надстрочные знаки", "SSE.Views.Toolbar.textSuperscript": "Надстрочные знаки", "SSE.Views.Toolbar.textTabCollaboration": "Совместная работа", "SSE.Views.Toolbar.textTabData": "Данные", "SSE.Views.Toolbar.textTabDraw": "Рисование", "SSE.Views.Toolbar.textTabFile": "<PERSON>а<PERSON><PERSON>", "SSE.Views.Toolbar.textTabFormula": "Формула", "SSE.Views.Toolbar.textTabHome": "Главная", "SSE.Views.Toolbar.textTabInsert": "Вставка", "SSE.Views.Toolbar.textTabLayout": "<PERSON>а<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textTabProtect": "Защита", "SSE.Views.Toolbar.textTabView": "Вид", "SSE.Views.Toolbar.textThisPivot": "Из этой сводной таблицы", "SSE.Views.Toolbar.textThisSheet": "Из этого листа", "SSE.Views.Toolbar.textThisTable": "Из этой таблицы", "SSE.Views.Toolbar.textTilde": "Тильда", "SSE.Views.Toolbar.textTop": "Верхнее: ", "SSE.Views.Toolbar.textTopBorders": "Верхние границы", "SSE.Views.Toolbar.textTradeMark": "Знак торговой марки", "SSE.Views.Toolbar.textUnderline": "Подчеркнутый", "SSE.Views.Toolbar.textUp": "Ввер<PERSON>", "SSE.Views.Toolbar.textVertical": "Вертикальный текст", "SSE.Views.Toolbar.textWidth": "Ши<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.textYen": "Знак йены", "SSE.Views.Toolbar.textZoom": "Масш<PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipAlignBottom": "Выровнять по нижнему краю", "SSE.Views.Toolbar.tipAlignCenter": "Выровнять по центру", "SSE.Views.Toolbar.tipAlignJust": "Выровнять по ширине", "SSE.Views.Toolbar.tipAlignLeft": "Выровнять по левому краю", "SSE.Views.Toolbar.tipAlignMiddle": "Выровнять по середине", "SSE.Views.Toolbar.tipAlignRight": "Выровнять по правому краю", "SSE.Views.Toolbar.tipAlignTop": "Выровнять по верхнему краю", "SSE.Views.Toolbar.tipAutofilter": "Сортировка и фильтр", "SSE.Views.Toolbar.tipBack": "Назад", "SSE.Views.Toolbar.tipBorders": "<PERSON>р<PERSON><PERSON><PERSON><PERSON><PERSON>", "SSE.Views.Toolbar.tipCellStyle": "Стиль ячейки", "SSE.Views.Toolbar.tipChangeCase": "Изменить регистр", "SSE.Views.Toolbar.tipChangeChart": "Изменить тип диаграммы", "SSE.Views.Toolbar.tipClearStyle": "Очистить", "SSE.Views.Toolbar.tipColorSchemas": "Изменение цветовой темы", "SSE.Views.Toolbar.tipCondFormat": "Условное форматирование", "SSE.Views.Toolbar.tipCopy": "Копировать", "SSE.Views.Toolbar.tipCopyStyle": "Копировать стиль", "SSE.Views.Toolbar.tipCut": "Вырезать", "SSE.Views.Toolbar.tipDecDecimal": "Уменьшить разрядность", "SSE.Views.Toolbar.tipDecFont": "Уменьшить размер шрифта", "SSE.Views.Toolbar.tipDeleteOpt": "Удалить ячейки", "SSE.Views.Toolbar.tipDigStyleAccounting": "Финансовый формат", "SSE.Views.Toolbar.tipDigStyleComma": "Формат с разделителями", "SSE.Views.Toolbar.tipDigStyleCurrency": "Денежный формат", "SSE.Views.Toolbar.tipDigStylePercent": "Процентный формат", "SSE.Views.Toolbar.tipEditChart": "Изменить диаграмму", "SSE.Views.Toolbar.tipEditChartData": "Выбор данных", "SSE.Views.Toolbar.tipEditChartType": "Изменить тип диаграммы", "SSE.Views.Toolbar.tipEditHeader": "Изменить колонтитулы", "SSE.Views.Toolbar.tipFontColor": "Цвет шрифта", "SSE.Views.Toolbar.tipFontName": "<PERSON>ри<PERSON><PERSON>", "SSE.Views.Toolbar.tipFontSize": "Размер шрифта", "SSE.Views.Toolbar.tipHAlighOle": "Горизонтальное выравнивание", "SSE.Views.Toolbar.tipImgAlign": "Выровнять объекты", "SSE.Views.Toolbar.tipImgGroup": "Сгруппировать объекты", "SSE.Views.Toolbar.tipIncDecimal": "Увеличить разрядность", "SSE.Views.Toolbar.tipIncFont": "Увеличить размер шрифта", "SSE.Views.Toolbar.tipInsertChart": "Вставить диаграмму", "SSE.Views.Toolbar.tipInsertChartRecommend": "Вставить рекомендованную диаграмму", "SSE.Views.Toolbar.tipInsertChartSpark": "Вставить диаграмму", "SSE.Views.Toolbar.tipInsertEquation": "Вставить уравнение", "SSE.Views.Toolbar.tipInsertHorizontalText": "Вставить горизонтальную надпись", "SSE.Views.Toolbar.tipInsertHyperlink": "Добавить гиперссылку", "SSE.Views.Toolbar.tipInsertImage": "Вставить изображение", "SSE.Views.Toolbar.tipInsertOpt": "Вставить ячейки", "SSE.Views.Toolbar.tipInsertShape": "Вставить фигуру", "SSE.Views.Toolbar.tipInsertSlicer": "Вставить срез", "SSE.Views.Toolbar.tipInsertSmartArt": "Вставить SmartArt", "SSE.Views.Toolbar.tipInsertSpark": "Вставить спарклайн", "SSE.Views.Toolbar.tipInsertSymbol": "Вставить символ", "SSE.Views.Toolbar.tipInsertTable": "Вставить таблицу", "SSE.Views.Toolbar.tipInsertText": "Вставить надпись", "SSE.Views.Toolbar.tipInsertTextart": "Вставить объект Text Art", "SSE.Views.Toolbar.tipInsertVerticalText": "Вставить вертикальную надпись", "SSE.Views.Toolbar.tipMerge": "Объединить и поместить в центре", "SSE.Views.Toolbar.tipNone": "Нет", "SSE.Views.Toolbar.tipNumFormat": "Числовой формат", "SSE.Views.Toolbar.tipPageBreak": "Добавьте разрыв там, где должна начинаться следующая страница в печатной копии.", "SSE.Views.Toolbar.tipPageMargins": "Поля страницы", "SSE.Views.Toolbar.tipPageOrient": "Ориентация страницы", "SSE.Views.Toolbar.tipPageSize": "Размер страницы", "SSE.Views.Toolbar.tipPaste": "Вставить", "SSE.Views.Toolbar.tipPrColor": "Цвет заливки", "SSE.Views.Toolbar.tipPrint": "Печать", "SSE.Views.Toolbar.tipPrintArea": "Область печати", "SSE.Views.Toolbar.tipPrintQuick": "Быстрая печать", "SSE.Views.Toolbar.tipPrintTitles": "Печатать заголовки", "SSE.Views.Toolbar.tipRedo": "Повторить", "SSE.Views.Toolbar.tipReplace": "Заменить", "SSE.Views.Toolbar.tipRtlSheet": "Переключить направление листа, чтобы первый столбец был справа", "SSE.Views.Toolbar.tipSave": "Сохранить", "SSE.Views.Toolbar.tipSaveCoauth": "Сохраните свои изменения, чтобы другие пользователи их увидели.", "SSE.Views.Toolbar.tipScale": "Вписать", "SSE.Views.Toolbar.tipSelectAll": "Выделить всё", "SSE.Views.Toolbar.tipSendBackward": "Перенести назад", "SSE.Views.Toolbar.tipSendForward": "Перенести вперед", "SSE.Views.Toolbar.tipShapesMerge": "Объединить фигуры", "SSE.Views.Toolbar.tipSynchronize": "Документ изменен другим пользователем. Нажмите, чтобы сохранить свои изменения и загрузить обновления.", "SSE.Views.Toolbar.tipTextFormatting": "Больше инструментов форматирования текста", "SSE.Views.Toolbar.tipTextOrientation": "Ориентация", "SSE.Views.Toolbar.tipUndo": "Отменить", "SSE.Views.Toolbar.tipVAlighOle": "Вертикальное выравнивание", "SSE.Views.Toolbar.tipVisibleArea": "Видимая область", "SSE.Views.Toolbar.tipWrap": "Перенос текста", "SSE.Views.Toolbar.txtAccounting": "Финансовый", "SSE.Views.Toolbar.txtAdditional": "Вставить функцию", "SSE.Views.Toolbar.txtAscending": "По возрастанию", "SSE.Views.Toolbar.txtAutosumTip": "Сумма", "SSE.Views.Toolbar.txtCellStyle": "Стиль ячейки", "SSE.Views.Toolbar.txtClearAll": "Всё", "SSE.Views.Toolbar.txtClearComments": "Комментарии", "SSE.Views.Toolbar.txtClearFilter": "Очистить фильтр", "SSE.Views.Toolbar.txtClearFormat": "Форматирование", "SSE.Views.Toolbar.txtClearFormula": "Функцию", "SSE.Views.Toolbar.txtClearHyper": "Гиперссылки", "SSE.Views.Toolbar.txtClearText": "Текст", "SSE.Views.Toolbar.txtCurrency": "Денежный", "SSE.Views.Toolbar.txtCustom": "Особый", "SSE.Views.Toolbar.txtDate": "Дата", "SSE.Views.Toolbar.txtDateLong": "Длинный формат даты", "SSE.Views.Toolbar.txtDateShort": "Краткий формат даты", "SSE.Views.Toolbar.txtDateTime": "Дата и время", "SSE.Views.Toolbar.txtDescending": "По убыванию", "SSE.Views.Toolbar.txtDollar": "$ Доллар", "SSE.Views.Toolbar.txtEuro": "€ Евро", "SSE.Views.Toolbar.txtExp": "Экспоненциальный", "SSE.Views.Toolbar.txtFillNum": "Заполнить", "SSE.Views.Toolbar.txtFilter": "Фильтр", "SSE.Views.Toolbar.txtFormula": "Вставить функцию", "SSE.Views.Toolbar.txtFraction": "Дробный", "SSE.Views.Toolbar.txtFranc": "CHF Франк", "SSE.Views.Toolbar.txtGeneral": "Общий", "SSE.Views.Toolbar.txtInteger": "Целочисленный", "SSE.Views.Toolbar.txtManageRange": "Диспетчер имен", "SSE.Views.Toolbar.txtMergeAcross": "Объединить по строкам", "SSE.Views.Toolbar.txtMergeCells": "Объединить ячейки", "SSE.Views.Toolbar.txtMergeCenter": "Объединить и поместить в центре", "SSE.Views.Toolbar.txtNamedRange": "Именованные диапазоны", "SSE.Views.Toolbar.txtNewRange": "Присвоить имя", "SSE.Views.Toolbar.txtNoBorders": "Без границ", "SSE.Views.Toolbar.txtNumber": "Числовой", "SSE.Views.Toolbar.txtPasteRange": "Вставить имя", "SSE.Views.Toolbar.txtPercentage": "Процентный", "SSE.Views.Toolbar.txtPound": "£ Фунт", "SSE.Views.Toolbar.txtRouble": "₽ Рубль", "SSE.Views.Toolbar.txtScientific": "Научный", "SSE.Views.Toolbar.txtSearch": "Поиск", "SSE.Views.Toolbar.txtSort": "Сортировка", "SSE.Views.Toolbar.txtSortAZ": "Сортировка по возрастанию", "SSE.Views.Toolbar.txtSortZA": "Сортировка по убыванию", "SSE.Views.Toolbar.txtSpecial": "Дополнительный", "SSE.Views.Toolbar.txtTableTemplate": "Форматировать как шаблон таблицы", "SSE.Views.Toolbar.txtText": "Текстовый", "SSE.Views.Toolbar.txtTime": "Время", "SSE.Views.Toolbar.txtUnmerge": "Отменить объединение ячеек", "SSE.Views.Toolbar.txtYen": "¥ Иена", "SSE.Views.Top10FilterDialog.textType": "Показать", "SSE.Views.Top10FilterDialog.txtBottom": "Наименьшие", "SSE.Views.Top10FilterDialog.txtBy": "по", "SSE.Views.Top10FilterDialog.txtItems": "Элемент", "SSE.Views.Top10FilterDialog.txtPercent": "Процент", "SSE.Views.Top10FilterDialog.txtSum": "Сумма", "SSE.Views.Top10FilterDialog.txtTitle": "Наложение условия по списку", "SSE.Views.Top10FilterDialog.txtTop": "Наибольшие", "SSE.Views.Top10FilterDialog.txtValueTitle": "Фильтр \"Первые 10\"", "SSE.Views.ValueFieldSettingsDialog.textNext": "(следующее)", "SSE.Views.ValueFieldSettingsDialog.textNumFormat": "Числовой формат", "SSE.Views.ValueFieldSettingsDialog.textPrev": "(предыдущее)", "SSE.Views.ValueFieldSettingsDialog.textTitle": "Параметры поля значений", "SSE.Views.ValueFieldSettingsDialog.txtAverage": "Среднее", "SSE.Views.ValueFieldSettingsDialog.txtBaseField": "Базовое поле", "SSE.Views.ValueFieldSettingsDialog.txtBaseItem": "Базовый элемент", "SSE.Views.ValueFieldSettingsDialog.txtByField": "%1 из %2", "SSE.Views.ValueFieldSettingsDialog.txtCount": "Количество", "SSE.Views.ValueFieldSettingsDialog.txtCountNums": "Количество чисел", "SSE.Views.ValueFieldSettingsDialog.txtCustomName": "Пользовательское имя", "SSE.Views.ValueFieldSettingsDialog.txtDifference": "Отличие", "SSE.Views.ValueFieldSettingsDialog.txtIndex": "Ин<PERSON><PERSON><PERSON>с", "SSE.Views.ValueFieldSettingsDialog.txtMax": "<PERSON>а<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtMin": "<PERSON><PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtNormal": "Без вычислений", "SSE.Views.ValueFieldSettingsDialog.txtPercent": "% от", "SSE.Views.ValueFieldSettingsDialog.txtPercentDiff": "Разница (%)", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfCol": "% от столбца", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfGrand": "% от общего итога", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParent": "% от родительской суммы", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentCol": "% от суммы по родительскому столбцу", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfParentRow": "% от суммы по родительской строке", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfRunTotal": "% от суммы с нарастающим итогом в поле", "SSE.Views.ValueFieldSettingsDialog.txtPercentOfTotal": "% от строки", "SSE.Views.ValueFieldSettingsDialog.txtProduct": "Произведение", "SSE.Views.ValueFieldSettingsDialog.txtRankAscending": "Сортировка от минимального к максимальному", "SSE.Views.ValueFieldSettingsDialog.txtRankDescending": "Сортировка от максимального к минимальному", "SSE.Views.ValueFieldSettingsDialog.txtRunTotal": "С нарастающим итогом в поле", "SSE.Views.ValueFieldSettingsDialog.txtShowAs": "Дополнительные вычисления", "SSE.Views.ValueFieldSettingsDialog.txtSourceName": "Имя источника:", "SSE.Views.ValueFieldSettingsDialog.txtStdDev": "Стандотклон", "SSE.Views.ValueFieldSettingsDialog.txtStdDevp": "Стандотклонп", "SSE.Views.ValueFieldSettingsDialog.txtSum": "Сумма", "SSE.Views.ValueFieldSettingsDialog.txtSummarize": "Операция", "SSE.Views.ValueFieldSettingsDialog.txtVar": "Ди<PERSON><PERSON>", "SSE.Views.ValueFieldSettingsDialog.txtVarp": "Дис<PERSON>р", "SSE.Views.ViewManagerDlg.closeButtonText": "Закрыть", "SSE.Views.ViewManagerDlg.guestText": "Гость", "SSE.Views.ViewManagerDlg.lockText": "Заблокирован", "SSE.Views.ViewManagerDlg.textDelete": "Удалить", "SSE.Views.ViewManagerDlg.textDuplicate": "Дублировать", "SSE.Views.ViewManagerDlg.textEmpty": "Представления еще не созданы.", "SSE.Views.ViewManagerDlg.textGoTo": "Перейти к представлению", "SSE.Views.ViewManagerDlg.textLongName": "Введите имя длиной менее 128 символов.", "SSE.Views.ViewManagerDlg.textNew": "Новое", "SSE.Views.ViewManagerDlg.textRename": "Переименовать", "SSE.Views.ViewManagerDlg.textRenameError": "Имя представления не должно быть пустым.", "SSE.Views.ViewManagerDlg.textRenameLabel": "Переименовать представление", "SSE.Views.ViewManagerDlg.textViews": "Представления листа", "SSE.Views.ViewManagerDlg.tipIsLocked": "Этот элемент редактируется другим пользователем.", "SSE.Views.ViewManagerDlg.txtTitle": "Диспетчер представлений листа", "SSE.Views.ViewManagerDlg.warnDeleteAnotherView": "Вы действительно хотите удалить это представление листа?", "SSE.Views.ViewManagerDlg.warnDeleteView": "Вы пытаетесь удалить активированное в данный момент представление '%1'.<br>Закрыть это представление и удалить его?", "SSE.Views.ViewTab.capBtnFreeze": "Закрепить области", "SSE.Views.ViewTab.capBtnSheetView": "Представление листа", "SSE.Views.ViewTab.textAlwaysShowToolbar": "Всегда показывать панель инструментов", "SSE.Views.ViewTab.textClose": "Закрыть", "SSE.Views.ViewTab.textCombineSheetAndStatusBars": "Объединить строки листов и состояния", "SSE.Views.ViewTab.textCreate": "Новое", "SSE.Views.ViewTab.textDefault": "По умолчанию", "SSE.Views.ViewTab.textFill": "Заливка", "SSE.Views.ViewTab.textFormula": "Строка формул", "SSE.Views.ViewTab.textFreezeCol": "Закрепить первый столбец", "SSE.Views.ViewTab.textFreezeRow": "Закрепить верхнюю строку", "SSE.Views.ViewTab.textGridlines": "Линии сетки", "SSE.Views.ViewTab.textHeadings": "Заголовки", "SSE.Views.ViewTab.textInterfaceTheme": "Тема интерфейса", "SSE.Views.ViewTab.textLeftMenu": "Левая панель", "SSE.Views.ViewTab.textLine": "Линия", "SSE.Views.ViewTab.textMacros": "Макросы", "SSE.Views.ViewTab.textManager": "Диспетчер представлений", "SSE.Views.ViewTab.textRightMenu": "Правая панель", "SSE.Views.ViewTab.textShowFrozenPanesShadow": "Показывать тень для закрепленных областей", "SSE.Views.ViewTab.textTabStyle": "Стиль вкладки", "SSE.Views.ViewTab.textUnFreeze": "Снять закрепление областей", "SSE.Views.ViewTab.textZeros": "Отображать нули", "SSE.Views.ViewTab.textZoom": "Масш<PERSON><PERSON><PERSON>", "SSE.Views.ViewTab.tipClose": "Закрыть представление листа", "SSE.Views.ViewTab.tipCreate": "Создать представление листа", "SSE.Views.ViewTab.tipFreeze": "Закрепить области", "SSE.Views.ViewTab.tipInterfaceTheme": "Тема интерфейса", "SSE.Views.ViewTab.tipMacros": "Макросы", "SSE.Views.ViewTab.tipSheetView": "Представление листа", "SSE.Views.ViewTab.tipViewNormal": "Просмотр документа в обычном режиме", "SSE.Views.ViewTab.tipViewPageBreak": "Предварительный просмотр разрывов страниц перед печатью документа", "SSE.Views.ViewTab.txtViewNormal": "Обычный", "SSE.Views.ViewTab.txtViewPageBreak": "Страничный режим", "SSE.Views.WatchDialog.closeButtonText": "Закрыть", "SSE.Views.WatchDialog.textAdd": "Добавить контрольное значение", "SSE.Views.WatchDialog.textBook": "Книга", "SSE.Views.WatchDialog.textCell": "Ячейка", "SSE.Views.WatchDialog.textDelete": "Удалить контрольное значение", "SSE.Views.WatchDialog.textDeleteAll": "Удалить все", "SSE.Views.WatchDialog.textFormula": "Формула", "SSE.Views.WatchDialog.textName": "Имя", "SSE.Views.WatchDialog.textSheet": "<PERSON><PERSON><PERSON><PERSON>", "SSE.Views.WatchDialog.textValue": "Значение", "SSE.Views.WatchDialog.txtTitle": "Окно контрольных значений", "SSE.Views.WBProtection.hintAllowRanges": "Разрешить редактировать диапазоны", "SSE.Views.WBProtection.hintProtectRange": "Защитить диапазон", "SSE.Views.WBProtection.hintProtectSheet": "Защитить лист", "SSE.Views.WBProtection.hintProtectWB": "Защитить книгу", "SSE.Views.WBProtection.txtAllowRanges": "Разрешить редактировать диапазоны", "SSE.Views.WBProtection.txtHiddenFormula": "Скрытые формулы", "SSE.Views.WBProtection.txtLockedCell": "Заблокированная ячейка", "SSE.Views.WBProtection.txtLockedShape": "Заблокированная фигура", "SSE.Views.WBProtection.txtLockedText": "Заблокировать текст", "SSE.Views.WBProtection.txtProtectRange": "Защитить диапазон", "SSE.Views.WBProtection.txtProtectSheet": "Защитить лист", "SSE.Views.WBProtection.txtProtectWB": "Защитить книгу", "SSE.Views.WBProtection.txtSheetUnlockDescription": "Введите пароль для отключения защиты листа", "SSE.Views.WBProtection.txtSheetUnlockTitle": "Снять защиту листа", "SSE.Views.WBProtection.txtWBUnlockDescription": "Введите пароль для отключения защиты книги"}