<!DOCTYPE html>
<html>
	<head>
		<title>Функция СЛУЧМЕЖДУ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СЛУЧМЕЖДУ</h1>
			<p>Функция <b>СЛУЧМЕЖДУ</b> - это одна из математических и тригонометрических функций. Возвращает случайное число, большее или равное значению аргумента <b>нижн_граница</b> и меньшее или равное значению аргумента <b>верхн_граница</b>.</p>
			<p>Синтаксис функции <b>СЛУЧМЕЖДУ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СЛУЧМЕЖДУ(нижн_граница;верхн_граница)</em></b></p>
			<p><em>где</em></p>
			<p style="text-indent: 50px;"><b><em>нижн_граница</em></b> - наименьшее целое число.</p>
			<p style="text-indent: 50px;"><b><em>верхн_граница</em></b> - наибольшее целое число.</p>
			<p>Эти числовые значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p> 
			<p class="note"><b>Примечание</b>: если значение аргумента <b><em>нижн_граница</em></b> больше, чем значение аргумента <b><em>верхн_граница</em></b>, функция возвращает ошибку <b>#ЧИСЛО!</b>.</p>
			<p>Чтобы применить функцию <b>СЛУЧМЕЖДУ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Математические</b>,</li>
			<li>щелкните по функции <b>СЛУЧМЕЖДУ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция СЛУЧМЕЖДУ" src="../images/randbetween.png" /></p>
		</div>
	</body>
</html>