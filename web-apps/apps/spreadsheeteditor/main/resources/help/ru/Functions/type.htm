<!DOCTYPE html>
<html>
	<head>
		<title>Функция ТИП</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ТИП</h1>
			<p>Функция <b>ТИП</b> - это одна из информационных функций. Используется для определения типа результирующего или отображаемого значения.</p>
			<p>Синтаксис функции <b>ТИП</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ТИП(значение)</em></b></p> 
			<p>где <b><em>значение</em></b> - это проверяемое значение, введенное вручную или находящееся в ячейке, на которую дается ссылка. Ниже представлены возможные значения и результат, который возвращает функция ТИП:</p>
			<table>
				<tr>
					<td><b>Значение</b></td>
					<td><b>Результат</b></td>
				</tr>
				<tr>
					<td>число</td>
					<td>1</td>
				</tr>
				<tr>
					<td>текст</td>
					<td>2</td>
				</tr>
				<tr>
					<td>логическое значение</td>
					<td>4</td>
				</tr>
				<tr>
					<td>значение ошибки</td>
					<td>16</td>
				</tr>
				<tr>
					<td>массив</td>
					<td>64</td>
				</tr>
			</table>
			<p>Чтобы применить функцию <b>ТИП</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Информационные</b>,</li>
			<li>щелкните по функции <b>ТИП</b>,</li>
			<li>введите требуемый аргумент,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ТИП" src="../images/type.png" /></p>
		</div>
	</body>
</html>