<!DOCTYPE html>
<html>
	<head>
		<title>Функция РАБДЕНЬ.МЕЖД</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция РАБДЕНЬ.МЕЖД</h1>
            <p>Функция <b>РАБДЕНЬ.МЕЖД</b> - это одна из функций даты и времени. Возвращает порядковый номер даты, отстоящей вперед или назад на заданное количество рабочих дней, с указанием настраиваемых параметров выходных, определяющих, сколько в неделе выходных дней и какие дни являются выходными.</p>
            <p>Синтаксис функции <b>РАБДЕНЬ.МЕЖД</b>:</p>
            <p style="text-indent: 150px;"><b><em>РАБДЕНЬ.МЕЖД(нач_дата;количество_дней;[выходной];[праздники])</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>нач_дата</em></b> - первая дата периода, введенная с помощью функции <a href="Date.htm" onclick="onhyperlinkclick(this)">ДАТА</a> или другой функции даты и времени.</p>
            <p style="text-indent: 50px;"><b><em>количество_дней</em></b> - количество рабочих дней до или после заданной <b><em>начальной даты</em></b>. Если аргумент <b><em>количество_дней</em></b> имеет отрицательное значение, функция возвращает дату, идущую перед заданной <b>начальной датой</b>. Если аргумент <b><em>количество_дней</em></b> имеет положительное значение, функция возвращает дату, идущую после заданной <b>начальной даты</b>.</p>
            <p style="text-indent: 50px;"><b><em>выходной</em></b> - необязательный аргумент, заданный номером выходного дня или строкой, определяющей, какие дни являются выходными. Допустимые номера приведены в таблице ниже.</p>
            <table style="width: 40%">
                <tr>
                    <td><b>Номер</b></td>
                    <td><b>Выходные дни</b></td>
                </tr>
                <tr>
                    <td>1 или опущен</td>
                    <td>Суббота, воскресенье</td>
                </tr>
                <tr>
                    <td>2</td>
                    <td>Воскресенье, понедельник</td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>Понедельник, вторник</td>
                </tr>
                <tr>
                    <td>4</td>
                    <td>Вторник, среда</td>
                </tr>
                <tr>
                    <td>5</td>
                    <td>Среда, четверг</td>
                </tr>
                <tr>
                    <td>6</td>
                    <td>Четверг, пятница</td>
                </tr>
                <tr>
                    <td>7</td>
                    <td>Пятница, суббота</td>
                </tr>
                <tr>
                    <td>11</td>
                    <td>Только воскресенье</td>
                </tr>
                <tr>
                    <td>12</td>
                    <td>Только понедельник</td>
                </tr>
                <tr>
                    <td>13</td>
                    <td>Только вторник</td>
                </tr>
                <tr>
                    <td>14</td>
                    <td>Только среда</td>
                </tr>
                <tr>
                    <td>15</td>
                    <td>Только четверг</td>
                </tr>
                <tr>
                    <td>16</td>
                    <td>Только пятница</td>
                </tr>
                <tr>
                    <td>17</td>
                    <td>Только суббота</td>
                </tr>
            </table>
            <p style="text-indent: 50px;">Строка, определяющая выходные дни, должна содержать 7 символов. Каждый символ обозначает день недели, начиная с понедельника. Значение 0 представляет рабочий день, значение 1 представляет нерабочий день. Например, <em>"0000011"</em> означает, что выходными днями являются суббота и воскресенье. Строка <em>"1111111"</em> является недействительной.</p>
            <p style="text-indent: 50px;"><b><em>праздники</em></b> - необязательный аргумент, определяющий, какие даты, помимо заданных аргументом <b><em>выходной</em></b>, являются нерабочими днями. Значения могут быть заданы с помощью функции <a href="Date.htm" onclick="onhyperlinkclick(this)">ДАТА</a> или другой функции даты и времени или являться ссылкой на диапазон ячеек, содержащих даты.</p>
            <p>Чтобы применить функцию <b>РАБДЕНЬ.МЕЖД</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Дата и время</b>,</li>
                <li>щелкните по функции <b>РАБДЕНЬ.МЕЖД</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция РАБДЕНЬ.МЕЖД" src="../images/workday-intl.png" /></p>
        </div>
	</body>
</html>