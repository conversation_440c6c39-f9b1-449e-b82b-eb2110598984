<!DOCTYPE html>
<html>
<head>
    <title>Функция СОРТПО</title>
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
    <link type="text/css" rel="stylesheet" href="../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Функция СОРТПО</h1>
        <p>Функция <b>СОРТПО</b> - это одна из <a href="../UsageInstructions/InsertFunction.htm#lookupreference" onclick="onhyperlinkclick(this)">поисковых функций</a>. Она возвращает отсортированный диапазон данных или массива на основе значений в соответствующем диапазоне или массиве.</p>
        <h3>Синтаксис</h3>
        <p><b><em>СОРТПО(массив, по_массиву, [порядок_сортировки], ...)</em></b></p>
        <p>Функция <b>СОРТПО</b> содержит следующие аргументы:</p>
        <table style="width: 40%">
            <tr>
                <th style="background-color: #f4f4f4"><b>Аргумент</b></th>
                <th style="background-color: #f4f4f4"><b>Описание</b></th>
            </tr>
            <tr>
                <td><b><em>массив</em></b></td>
                <td>Диапазон ячеек для сортировки.</td>
            </tr>
            <tr>
                <td><b><em>по_массиву</em></b></td>
                <td>Диапазон ячеек для сортировки по.</td>
            </tr>
            <tr>
                <td><b><em>порядок_сортировки</em></b></td>
                <td>Это необязательный аргумент. Число, определяющее порядок сортировки. Возможные значения перечислены в таблице ниже.</td>
            </tr>
        </table>
        <p>Аргумент <b><em>порядок_сортировки</em></b> может иметь следующие значения:</p>
        <table style="width: 40%">
            <tr>
                <th style="background-color: #f4f4f4"><b>Числовое значение</b></th>
                <th style="background-color: #f4f4f4"><b>Значение</b></th>
            </tr>
            <tr>
                <td>1 или не указано</td>
                <td>По возрастанию (по умолчанию)</td>
            </tr>
            <tr>
                <td>-1</td>
                <td>По убыванию</td>
            </tr>
        </table>
        <h3>Примечания</h3>
        <p>Обратите внимание, что это формула массива. Чтобы узнать больше, обратитесь к статье <a href="../UsageInstructions/InsertArrayFormulas.htm" onclick="onhyperlinkclick(this)">Вставка формул массива</a>.</p>
        <p><a href="../UsageInstructions/InsertFunction.htm#applyfunction" onclick="onhyperlinkclick(this)">Как использовать</a> функцию <b>СОРТПО</b>.</p>
        <h3>Примеры</h3>
        <p>Изображение ниже содержит результат, возвращаемый функцией <b>СОРТПО</b>.</p>
        <p><img alt="Функция СОРТПО" src="../images/sortby.png" /></p>
    </div>
</body>
</html>