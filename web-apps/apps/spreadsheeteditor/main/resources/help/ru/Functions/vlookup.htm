<!DOCTYPE html>
<html>
	<head>
		<title>Функция ВПР</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ВПР</h1>
			<p>Функция <b>ВПР</b> - это одна из поисковых функций. Она используется для выполнения вертикального поиска значения в крайнем левом столбце таблицы или массива и возвращает значение, которое находится в той же самой строке в столбце с заданным номером.</p>
			<p>Синтаксис функции <b>ВПР</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ВПР(искомое_значение;таблица;номер_столбца;[интервальный_просмотр])</em></b></p> 
			<p>где <b><em>искомое_значение</em></b> - это значение, которое необходимо найти, <b><em>таблица</em></b> - это два или более столбца с данными, отсортированными в порядке возрастания, <b><em>номер_столбца</em></b> - это номер столбца в <b><em>таблице</em></b>, <b><em>интервальный_просмотр</em></b> - это логическое значение ИСТИНА или ЛОЖЬ,</p>
			<p><b><em>интервальный_просмотр</em></b> - необязательный аргумент. Введите значение ЛОЖЬ для поиска точного соответствия. Введите значение ИСТИНА для поиска приблизительного соответствия, в этом случае при отсутствии значения, строго соответствующего <b><em>искомому значению</em></b>, функция выбирает следующее наибольшее значение, которое меньше, чем <b><em>искомое значение</em></b>. Если этот аргумент отсутствует, функция находит приблизительное соответствие.</p>
			<p class="note"><b>Примечание</b>: если значение аргумента <b>номер столбца</b> меньше 1, функция возвращает ошибку <b>#ЗНАЧ!</b>. Если значение аргумента <b>номер столбца</b> больше, чем количество столбцов в <b>таблице</b>, функция возвращает ошибку <b>#ССЫЛКА!</b>. Если аргумент <b><em>интервальный_просмотр</em></b> имеет значение ЛОЖЬ, но точное соответствие не найдено, функция возвращает ошибку <b>#Н/Д</b>.</p>
			<p>Чтобы применить функцию <b>ВПР</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Поиск и ссылки</b>,</li>
			<li>щелкните по функции <b>ВПР</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ВПР" src="../images/vlookup.png" /></p>
		</div>
	</body>
</html>