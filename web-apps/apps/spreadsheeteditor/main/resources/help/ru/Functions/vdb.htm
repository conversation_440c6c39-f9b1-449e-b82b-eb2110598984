<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПУО</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ПУО</h1>
            <p>Функция <b>ПУО</b> - это одна из финансовых функций. Используется для вычисления величины амортизации имущества за указанный отчетный период или его часть методом двойного уменьшения остатка или иным указанным методом.</p>
            <p>Синтаксис функции <b>ПУО</b>:</p>
            <p style="text-indent: 150px;"><b><em>ПУО(нач_стоимость;ост_стоимость;время_эксплуатации;нач_период;кон_период;[коэффициент];[без_переключения])</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>нач_стоимость</em></b> - это стоимость имущества.</p>
            <p style="text-indent: 50px;"><b><em>ост_стоимость</em></b> - это остаточная стоимость имущества на конец срока службы.</p>
            <p style="text-indent: 50px;"><b><em>время_эксплуатации</em></b> - это общее количество периодов в течение срока службы имущества.</p>
            <p style="text-indent: 50px;"><b><em>нач_период</em></b> - это начальный период, за который вы хотите рассчитать сумму амортизационных отчислений. Его значение должно быть выражено в тех же единицах измерения, что и значение аргумента <em>время_эксплуатации</em>.</p>
            <p style="text-indent: 50px;"><b><em>кон_период</em></b> - это конечный период, за который вы хотите рассчитать сумму амортизационных отчислений. Его значение должно быть выражено в тех же единицах измерения, что и значение аргумента <em>время_эксплуатации</em>.</p>
            <p style="text-indent: 50px;"><b><em>коэффициент</em></b> - это коэффициент уменьшения размера амортизации. Это необязательный аргумент. Если он опущен, то аргумент <b><em>коэффициент</em></b> полагается равным 2.</p>
            <p style="text-indent: 50px;"><b><em>без_переключения</em></b> - это необязательный аргумент, который показывает, нужно ли использовать 
            линейный метод амортизации, когда сумма амортизации превышает расчетную величину уменьшающегося остатка. Если он опущен или указано значение ЛОЖЬ, используется
            линейный метод амортизации. Если указано значение ИСТИНА, то используется метод уменьшающегося остатка.
            </p>
            <p class="note"><b>Примечание:</b> все числовые значения должны быть заданы положительными числами.
            </p>
          <p>Значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>
          <p>
            Чтобы применить функцию <b>ПУО</b>,
          </p>
          <ol>
            <li>выделите ячейку, в которой требуется отобразить результат,</li>
            <li>
              щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
              <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
              <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
            </li>
            <li>
              выберите из списка группу функций <b>Финансовые</b>,
            </li>
            <li>
              щелкните по функции <b>ПУО</b>,
            </li>
            <li>введите требуемые аргументы через точку с запятой,</li>
            <li>
              нажмите клавишу <b>Enter</b>.
            </li>
          </ol>
          <p>Результат будет отображен в выделенной ячейке.</p>          
            <p style="text-indent: 150px;"><img alt="Функция ПУО" src="../images/vdb.png" /></p>
        </div>
	</body>
</html>