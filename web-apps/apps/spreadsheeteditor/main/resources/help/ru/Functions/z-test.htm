<!DOCTYPE html>
<html>
	<head>
		<title>Функция Z<PERSON>ТЕСТ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция Z.ТЕСТ</h1>
            <p>Функция <b>Z.ТЕСТ</b> - это одна из статистических функций. Возвращает одностороннее P-значение z-теста. Для заданного гипотетического среднего генеральной совокупности функция <b>Z.ТЕСТ</b> возвращает вероятность того, что среднее по выборке будет больше среднего значения набора рассмотренных данных (массива), то есть среднего значения наблюдаемой выборки.</p>
            <p>Синтаксис функции <b>Z.ТЕСТ</b>:</p>
            <p style="text-indent: 150px;"><b><em>Z.ТЕСТ(массив;x;[сигма])</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>массив</em></b> - диапазон числовых данных, с которыми сравнивается аргумент <b><em>x</em></b>.</p>
            <p style="text-indent: 50px;"><b><em>x</em></b> - проверяемое значение.</p>
            <p style="text-indent: 50px;"><b><em>сигма</em></b> - известное стандартное отклонение генеральной совокупности. Это необязательный аргумент. Если он опущен, используется стандартное отклонение выборки.</p>
            <p>Эти значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
            <p>Чтобы применить функцию <b>Z.ТЕСТ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>Z.ТЕСТ</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция Z.ТЕСТ" src="../images/z-test.png" /></p>
        </div>
	</body>
</html>