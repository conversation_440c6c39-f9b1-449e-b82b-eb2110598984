<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПРОМЕЖУТОЧНЫЕ.ИТОГИ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ПРОМЕЖУТОЧНЫЕ.ИТОГИ</h1>
			<p>Функция <b>ПРОМЕЖУТОЧНЫЕ.ИТОГИ</b> - это одна из математических и тригонометрических функций. Возвращает промежуточный итог в список или базу данных.</p>
			<p>Синтаксис функции <b>ПРОМЕЖУТОЧНЫЕ.ИТОГИ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ПРОМЕЖУТОЧНЫЕ.ИТОГИ(номер_функции;список_аргументов)</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>номер_функции</em></b> - числовое значение, которое обозначает функцию, используемую для расчета промежуточных итогов. Допустимые значения приведены в таблице ниже. При использовании аргументов <em>номер_функции</em> от 1 до 11 функция <b>ПРОМЕЖУТОЧНЫЕ.ИТОГИ</b> учитывает значения строк, которые были скрыты вручную. При использовании аргументов <em>номер_функции</em> от 101 до 111 функция <b>ПРОМЕЖУТОЧНЫЕ.ИТОГИ</b> не учитывает значения строк, которые были скрыты вручную. Значения, скрытые фильтром, исключаются всегда.</p> 
			<p style="text-indent: 50px;"><b><em>список_аргументов</em></b> - ссылка на диапазон ячеек, содержащих значения, для которых требуется вычислить промежуточные итоги.</p>
			<table style="width: 40%">
				<tr>
					<td><b>номер_функции<br />(с учетом скрытых значений)</b></td>
                    <td><b>номер_функции<br />(без учета скрытых значений)</b></td>
					<td><b>Функция</b></td>
				</tr>
				<tr>
					<td>1</td>
                    <td>101</td>
					<td><a href="../Functions/average.htm" onclick="onhyperlinkclick(this)">СРЗНАЧ</a></td>
				</tr>
                <tr>
                    <td>2</td>
                    <td>102</td>
                    <td><a href="../Functions/count.htm" onclick="onhyperlinkclick(this)">СЧЁТ</a></td>
                </tr>
                <tr>
                    <td>3</td>
                    <td>103</td>
                    <td><a href="../Functions/counta.htm" onclick="onhyperlinkclick(this)">СЧЁТЗ</a></td>
                </tr>
                <tr>
                    <td>4</td>
                    <td>104</td>
                    <td><a href="../Functions/max.htm" onclick="onhyperlinkclick(this)">МАКС</a></td>
                </tr>
                <tr>
                    <td>5</td>
                    <td>105</td>
                    <td><a href="../Functions/min.htm" onclick="onhyperlinkclick(this)">МИН</a></td>
                </tr>
                <tr>
                    <td>6</td>
                    <td>106</td>
                    <td><a href="../Functions/product.htm" onclick="onhyperlinkclick(this)">ПРОИЗВЕД</a></td>
                </tr>
                <tr>
                    <td>7</td>
                    <td>107</td>
                    <td><a href="../Functions/stdev.htm" onclick="onhyperlinkclick(this)">СТАНДОТКЛОН</a></td>
                </tr>
                <tr>
                    <td>8</td>
                    <td>108</td>
                    <td><a href="../Functions/stdevp.htm" onclick="onhyperlinkclick(this)">СТАНДОТКЛОНП</a></td>
                </tr>
                <tr>
                    <td>9</td>
                    <td>109</td>
                    <td><a href="../Functions/sum.htm" onclick="onhyperlinkclick(this)">СУММ</a></td>
                </tr>
                <tr>
                    <td>10</td>
                    <td>110</td>
                    <td><a href="../Functions/var.htm" onclick="onhyperlinkclick(this)">ДИСП</a></td>
                </tr>
                <tr>
                    <td>11</td>
                    <td>111</td>
                    <td><a href="../Functions/varp.htm" onclick="onhyperlinkclick(this)">ДИСПР</a></td>
                </tr>
			</table>
			<p>Чтобы применить функцию  <b>ПРОМЕЖУТОЧНЫЕ.ИТОГИ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Математические</b>,</li>
                <li>щелкните по функции <b>ПРОМЕЖУТОЧНЫЕ.ИТОГИ</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ПРОМЕЖУТОЧНЫЕ.ИТОГИ" src="../images/subtotal.png" /></p>
            <p>На следующем изображении показан результат, возвращаемый функцией <b>ПРОМЕЖУТОЧНЫЕ.ИТОГИ</b>, если несколько строк скрыто.</p>
            <p style="text-indent: 150px;"><img alt="Функция ПРОМЕЖУТОЧНЫЕ.ИТОГИ" src="../images/subtotal2.png" /></p>
		</div>
	</body>
</html>