<!DOCTYPE html>
<html>
	<head>
		<title>Функция СУММЕСЛИ</title>
		<meta charset="utf-8" />
        <meta name="description" content="Функция СУММЕСЛИ - это одна из математических и тригонометрических функций. Узнайте, как использовать формулу СУММЕСЛИ в таблицах Excel с помощью ONLYOFFICE." />
        <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <meta name="keywords" content="суммесли, суммесли excel, функция суммесли, excel суммесли, суммесли эксель, формула суммесли, суммесли для текстовых значений, суммесли как работает, как работает функция суммесли, суммесли excel несколько условий, эксель суммесли, excel суммесли несколько условий, excel суммесли сложный критерий, excel функция суммесли, как работает суммесли, как работает формула суммесли, суммесли в excel, суммесли в ексель, суммесли критерий, суммесли несколько условий, суммесли пример, суммесли с несколькими условиями, формула суммесли в excel, формула суммесли в экселе, формула суммесли в эксель, функция в excel суммесли, функция суммесли в excelа">
        <link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СУММЕСЛИ</h1>
			<p>Функция <b>СУММЕСЛИ</b> - это одна из математических и тригонометрических функций. Суммирует все числа в выбранном диапазоне ячеек в соответствии с заданным условием и возвращает результат.</p>
			<p>Синтаксис функции <b>СУММЕСЛИ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СУММЕСЛИ(диапазон;условие;[диапазон_суммирования])</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>диапазон</em></b> - выбранный диапазон ячеек, к которому применяется условие.</p>
			<p style="text-indent: 50px;"><b><em>условие</em></b> - условие, определяющее, какие ячейки требуется просуммировать; значение, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p>
			<p style="text-indent: 50px;"><b><em>диапазон_суммирования</em></b> - диапазон ячеек, который требуется просуммировать. Необязательный аргумент. Если он опущен, суммируются ячейки, указанные в аргументе <b><em>диапазон</em></b>.</p>
            <p class="note"><b>Примечание:</b> при указании условий можно использовать подстановочные знаки. Вопросительный знак "?" может замещать любой отдельный символ, а звездочку "*" можно использовать вместо любого количества символов.</p>
			<h2>Как работает функция СУММЕСЛИ</h2>
            <p>Чтобы применить функцию <b>СУММЕСЛИ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Математические</b>,</li>
			<li>щелкните по функции <b>СУММЕСЛИ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p><img alt="Функция СУММЕСЛИ" src="../images/sumif.gif" /></p>
		</div>
	</body>
</html>