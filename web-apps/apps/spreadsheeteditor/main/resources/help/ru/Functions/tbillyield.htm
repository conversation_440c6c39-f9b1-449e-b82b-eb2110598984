<!DOCTYPE html>
<html>
	<head>
		<title>Функция ДОХОДКЧЕК</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ДОХОДКЧЕК</h1>
            <p>Функция <b>ДОХОДКЧЕК</b> - это одна из финансовых функций. Используется для вычисления доходности по казначейскому векселю.</p>
            <p>Синтаксис функции <b>ДОХОДКЧЕК</b>:</p>
            <p style="text-indent: 150px;"><b><em>ДОХОДКЧЕК(дата_согл;дата_вступл_в_силу;цена)</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>дата_согл</em></b> - это дата покупки казначейского векселя.</p>
            <p style="text-indent: 50px;"><b><em>дата_вступл_в_силу</em></b> - это дата истечения срока действия казначейского векселя (дата погашения). Дата погашения должна наступать не позднее одного года после даты покупки.</p>
            <p style="text-indent: 50px;"><b><em>цена</em></b> - это цена покупки казначейского векселя в расчете на 100 рублей номинальной стоимости.</p>
            <p class="note">
            <b>Примечание:</b> даты должны быть введены с помощью функции ДАТА.
          </p>
          <p>Значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>
          <p>
            Чтобы применить функцию <b>ДОХОДКЧЕК</b>,
          </p>
          <ol>
            <li>выделите ячейку, в которой требуется отобразить результат,</li>
            <li>
              щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
              <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
              <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
            </li>
            <li>
              выберите из списка группу функций <b>Финансовые</b>,
            </li>
            <li>
              щелкните по функции <b>ДОХОДКЧЕК</b>,
            </li>
            <li>введите требуемые аргументы через точку с запятой,</li>
            <li>
              нажмите клавишу <b>Enter</b>.
            </li>
          </ol>
          <p>Результат будет отображен в выделенной ячейке.</p>  
          <p style="text-indent: 150px;"><img alt="Функция ДОХОДКЧЕК" src="../images/tbillyield.png" /></p>
        </div>
	</body>
</html>