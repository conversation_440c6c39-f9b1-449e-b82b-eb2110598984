<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПОИСК/ПОИСКБ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ПОИСК/ПОИСКБ</h1>
			<p>Функция <b>ПОИСК/ПОИСКБ</b> - это одна из функций для работы с текстом и данными. Возвращает местоположение заданной подстроки в строке. Функция <b>ПОИСК</b> предназначена для языков, использующих однобайтовую кодировку (SBCS), в то время как <b>ПОИСКБ</b> - для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.</p>
			<p>Синтаксис функции <b>ПОИСК/ПОИСКБ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ПОИСК(искомый_текст;просматриваемый_текст;[начальная_позиция])</em></b></p> 
			<p style="text-indent: 150px;"><b><em>ПОИСКБ(искомый_текст;просматриваемый_текст;[начальная_позиция])</em></b></p>
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>искомый_текст</em></b> - подстрока, которую требуется найти.</p>
			<p style="text-indent: 50px;"><b><em>просматриваемый_текст</em></b> - строка, в которой выполняется поиск.</p>
			<p style="text-indent: 50px;"><b><em>начальная_позиция</em></b> - позиция, с которой требуется начать поиск. Необязательный аргумент. Если он опущен, поиск выполняется с начала строки <b><em>просматриваемый_текст</em></b>.</p>
			<p>Эти данные можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
			<p class="note"><b>Примечание</b>: если соответствий не найдено, функция ПОИСК/ПОИСКБ возвращает ошибку <b>#ЗНАЧ!</b>.</p>
			<p>Чтобы применить функцию <b>ПОИСК/ПОИСКБ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Текст и данные</b>,</li>
			<li>щелкните по функции <b>ПОИСК/ПОИСКБ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,
			<p class="note"><b>Примечание</b>: функция ПОИСК/ПОИСКБ <b>НЕ</b> учитывает регистр.</p>
			</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ПОИСК" src="../images/search.png" /></p>
		</div>
	</body>
</html>