<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЧИСТНЗ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ЧИСТНЗ</h1>
			<p>Функция <b>ЧИСТНЗ</b> - это одна из финансовых функций. Используется для вычисления чистой приведенной стоимости инвестиции исходя из указанной процентной ставки и нерегулярных выплат.</p>
			<p>Синтаксис функции <b>ЧИСТНЗ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ЧИСТНЗ(ставка;значения;даты)</em></b></p> 
			<p><em>где</em></p> 
				<p style="text-indent: 50px;"><b><em>ставка</em></b> - это ставка дисконтирования инвестиции.</p> 
				<p style="text-indent: 50px;"><b><em>значения</em></b> - это массив, содержащий суммы дохода (положительные значения) или выплат (отрицательные значения). Хотя бы одно из значений должно быть отрицательным, а хотя бы одно - положительным.</p> 
				<p style="text-indent: 50px;"><b><em>даты</em></b> - это массив, содержащий даты поступления или выплаты денежных средств.</p>
      <p>Значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>
      <p>
        Чтобы применить функцию <b>ЧИСТНЗ</b>,
      </p>
      <ol>
        <li>выделите ячейку, в которой требуется отобразить результат,</li>
        <li>
          щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
          <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
          <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
        </li>
        <li>
          выберите из списка группу функций <b>Финансовые</b>,
        </li>
        <li>
          щелкните по функции <b>ЧИСТНЗ</b>,
        </li>
        <li>введите требуемые аргументы через точку с запятой,</li>
        <li>
          нажмите клавишу <b>Enter</b>.
        </li>
      </ol>
      <p>Результат будет отображен в выделенной ячейке.</p>      
			<p style="text-indent: 150px;"><img alt="Функция ЧИСТНЗ" src="../images/xnpv.png" /></p>
		</div>
	</body>
</html>