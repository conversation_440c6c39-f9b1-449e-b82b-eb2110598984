<!DOCTYPE html>
<html>
	<head>
		<title>Функция ТДАТА</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ТДАТА</h1>
			<p>Функция <b>ТДАТА</b> - это одна из функций даты и времени. Используется для добавления в электронную таблицу текущей даты и времени в следующем формате: <em>дд.ММ.гггг чч:мм</em>. Данная функция не требует аргумента.</p>
			<p>Синтаксис функции <b>ТДАТА</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ТДАТА()</em></b></p> 
			<p>Чтобы применить функцию <b>ТДАТА</b>:</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Дата и время</b>,</li>
			<li>щелкните по функции <b>ТДАТА</b>,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ТДАТА" src="../images/now.png" /></p>
		</div>
	</body>
</html>