<!DOCTYPE html>
<html>
	<head>
		<title>Функция СУММПРОИЗВ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СУММПРОИЗВ</h1>
			<p>Функция <b>СУММПРОИЗВ</b> - это одна из математических и тригонометрических функций. Перемножает соответствующие элементы заданных диапазонов ячеек или массивов и возвращает сумму произведений.</p>
			<p>Синтаксис функции <b>СУММПРОИЗВ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СУММПРОИЗВ(список_аргументов)</em></b></p> 
			<p>где <b><em>список_аргументов</em></b> - числовые значения, находящиеся в ячейках, на которые дается ссылка. Можно ввести до 30 диапазонов ячеек или массивов.</p>
			<p class="note"><b>Примечание</b>: если <b><em>список_аргументов</em></b> содержит нечисловые значения, функция трактует их как 0.</p>
			<p>Чтобы применить функцию <b>СУММПРОИЗВ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Математические</b>,</li>
			<li>щелкните по функции <b>СУММПРОИЗВ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция СУММПРОИЗВ" src="../images/sumproduct.png" /></p>
		</div>
	</body>
</html>