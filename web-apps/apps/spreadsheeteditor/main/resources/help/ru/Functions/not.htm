<!DOCTYPE html>
<html>
	<head>
		<title>Функция НЕ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция НЕ</h1>
			<p>Функция <b>НЕ</b> - это одна из логических функций. Она используется для проверки, является ли введенное логическое значение истинным или ложным. Функция возвращает значение ИСТИНА, если аргумент имеет значение ЛОЖЬ, и ЛОЖЬ, если аргумент имеет значение ИСТИНА.</p>
			<p>Синтаксис функции <b>НЕ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>НЕ(логическое_значение)</em></b></p> 
			<p>где <b><em>логическое_значение</em></b> - это значение, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p>
			<p>Чтобы применить функцию <b>НЕ</b>:</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Логические</b>,</li>
			<li>щелкните по функции <b>НЕ</b>,</li>
			<li>введите требуемый аргумент,
			</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p><em>Например:</em></p>
			<p>Здесь один аргумент: <em>логическое_значение</em> = <b>A1&lt;100</b>, где <b>A1</b> имеет значение <b>12</b>. Данное логическое выражение имеет значение <b>ИСТИНА</b>. Следовательно, функция возвращает значение <b>ЛОЖЬ</b>.</p>
			<p style="text-indent: 150px;"><img alt="Функция НЕ: ЛОЖЬ" src="../images/notfalse.png" /></p>
			<p>Если изменить значение <b>A1</b> с <b>12</b> на <b>112</b>, функция возвращает значение <b>ИСТИНА</b>:</p>
			<p style="text-indent: 150px;"><img alt="Функция НЕ: ИСТИНА" src="../images/nottrue.png" /></p>
		</div>
	</body>
</html>