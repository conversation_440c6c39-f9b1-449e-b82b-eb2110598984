<!DOCTYPE html>
<html>
	<head>
		<title>Функция ДИСП.Г</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ДИСП.Г</h1>
            <p>Функция <b>ДИСП.Г</b> - это одна из статистических функций. Вычисляет дисперсию для генеральной совокупности. Логические значения и текст игнорируются.</p>
            <p>Синтаксис функции <b>ДИСП.Г</b>:</p>
            <p style="text-indent: 150px;"><b><em>ДИСП.Г(число1;[число2]; ...)</em></b></p>
            <p>где <b><em>число1(2)</em></b> - это до 254 числовых значений, введенных вручную или находящихся в ячейках, на которые даются ссылки.</p>
            <p class="note"><b>Примечание:</b> пустые ячейки, логические значения, текст и значения ошибок в составе массива игнорируются. Если текстовые представления чисел и логические значения введены непосредственно в функцию, они интерпретируются как числовые значения.</p>
            <p>Чтобы применить функцию <b>ДИСП.Г</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>ДИСП.Г</b>,</li>
                <li>введите требуемые аргументы через точку с запятой или выделите мышью диапазон ячеек,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция ДИСП.Г" src="../images/var-p.png" /></p>
        </div>
	</body>
</html>