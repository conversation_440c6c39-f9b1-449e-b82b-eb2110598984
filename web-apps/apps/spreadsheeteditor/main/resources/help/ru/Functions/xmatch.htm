<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПОИСКПОЗX</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Функция ПОИСКПОЗX</h1>
			<p>Функция <b>ПОИСКПОЗX</b> - это одна из поисковых функций. Она возвращает относительное положение заданного элемента в диапазоне ячеек. По умолчанию требуется точное совпадение.</p>
			<p class="note">Обратите внимание, что это формула массива. Чтобы узнать больше, обратитесь к статье <a href="../UsageInstructions/InsertArrayFormulas.htm" onclick="onhyperlinkclick(this)">Вставка формул массива</a>.</p>
			<p>Синтакс функции <b>ПОИСКПОЗХ</b>:</p>
			<p style="text-indent: 150px;"><b><em>=ПОИСКПОЗХ(искомое_значение, просматриваемый_массив, [режим_сопоставления], [режим_поиска])</em></b></p>
			<p><em>где</em></p>
			<p style="text-indent: 50px;"><b><em>искомое_значение</em></b>  - это искомое значение.</p>
			<p style="text-indent: 50px;"><b><em>просматриваемый_массив</em></b> - это массив или диапазон для поиска.</p>
			<p style="text-indent: 50px;">
				<b><em>[режим_сопоставления]</em></b> - это тип сопоставления. Это необязательный аргумент. Он может принимать одно из следующих числовых значений:
				<ul>
					<li><b><em>0</em></b> - (установлено по умолчанию) возвращает точное совпадение; если точное совпадение не найдено, функция возвращает ошибку #Н/Д.</li>
					<li><b><em>-1</em></b> - возвращает точное совпадение; если точное значение не найдено, функция возвращает наименьшее значение, превышающее <b>искомое_значение</b>.</li>
					<li><b><em>1</em></b> - возвращает точное совпадение; если точное значение не найдено, функция возвращает наибольшее значение, превышающее <b>искомое_значение</b>.</li>
					<li><b><em>2</em></b> - совпадение с использованием подстановочных знаков.</li>
				</ul>
			</p>
			<p style="text-indent: 50px;">
				<b><em>[режим_поиска]</em></b> - это тип поиска. Это необязательный аргумент. Он может принимать одно из следующих числовых значений:
				<ul>
					<li><b><em>1</em></b>  - (установлено по умолчанию) начинает поиск с первого элемента.</li>
					<li><b><em>-1</em></b>  - начинает обратный поиск, т.е. с последнего элемента.</li>
					<li><b><em>2</em></b> - выполняет двоичный поиск в <b><em>искомом_значении</em></b>, отсортированным по возрастанию. Если сортировка не выполнена, будут возвращены недопустимые результаты.</li>
					<li><b><em>-2</em></b> выполняет двоичный поиск в <b><em>искомом_значении</em></b>, отсортированным по убыванию. Если сортировка не выполнена, будут возвращены недопустимые результаты.</li>
				</ul>
			</p>
			<p class="note">
				Аргумент <b>режим_сопоставления</b> может содержать подстановочные знаки — вопросительный знак (?), соответствующий одному символу, и звездочку (*), соответствующую любому количеству символов. Если требуется найти вопросительный знак или звездочку, введите перед этим символом тильду (~).
			</p>
			<p>Чтобы применить функцию <b>ПОИСКПОЗX</b>,</p>
			<ol>
				<li>выделите ячейку, в которой требуется отобразить результат,</li>
				<li>
					щелкните по значку <b>Вставить функцию</b> <div class="icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
					<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
					<br />или щелкните по значку <div class="icon icon-function"></div> перед строкой формул,
				</li>
				<li>выберите из списка группу функций <b>Поиск и ссылки</b>,</li>
				<li>щелкните по функции <b>ПОИСКПОЗX</b>,</li>
				<li>введите требуемые аргументы через точку с запятой или выделите мышью диапазон ячеек,</li>
				<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<!--<p style="text-indent: 150px;"><img alt="XMATCH Function" src="../images/xmatch.png" /></p>-->
		</div>
	</body>
</html>