<!DOCTYPE html>
<html>
	<head>
		<title>Функция ИСКЛИЛИ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ИСКЛИЛИ</h1>
			<p>Функция <b>ИСКЛИЛИ</b> - это одна из логических функций. Возвращает логическое исключающее ИЛИ всех аргументов. Функция возвращает значение ИСТИНА, если число вводов ИСТИНА нечетное, и значение ЛОЖЬ, если число вводов ИСТИНА четное.</p>
			<p>Синтаксис функции <b>ИСКЛИЛИ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ИСКЛИЛИ(логическое_значение1;[логическое значение2]; ...)</em></b></p> 
			<p>где <b><em>логическое_значение1</em></b> - это значение, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p>
			<p>Чтобы применить функцию <b>ИСКЛИЛИ</b>,</p>
			<ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Логические</b>,</li>
                <li>щелкните по функции <b>ИСКЛИЛИ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,
			<p class="note"><b>Примечание</b>: можно ввести до <b>254</b> логических значений.</p>
			</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p><em>Например:</em></p>
			<p>Здесь два аргумента: <em>логическое_значение1</em> = <b>1&gt;0</b>, <em>логическое_значение2</em> = <b>2&gt;0</b>. Число вводов <b>ИСТИНА</b> четное, поэтому функция возвращает значение <b>ЛОЖЬ</b>.</p>
			<p style="text-indent: 150px;"><img alt="Функция ИСКЛИЛИ: ЛОЖЬ" src="../images/xorfalse.png" /></p>
			<p>Здесь два аргумента: <em>логическое_значение1</em> = <b>1&gt;0</b>, <em>логическое_значение2</em> = <b>2&lt;0</b>. Число вводов <b>ИСТИНА</b> нечетное, поэтому функция возвращает значение <b>ИСТИНА</b>.</p>
			<p style="text-indent: 150px;"><img alt="Функция ИСКЛИЛИ: ИСТИНА" src="../images/xortrue.png" /></p>
		</div>
	</body>
</html>