<!DOCTYPE html>
<html>
	<head>
		<title>Функция ОБЪЕДИНИТЬ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ОБЪЕДИНИТЬ</h1>
            <p>Функция <b>ОБЪЕДИНИТЬ</b> - это одна из функций для работы с текстом и данными. Объединяет текст из нескольких диапазонов и (или) строк, вставляя между текстовыми значениями указанный разделитель. Если в качестве разделителя используется пустая текстовая строка, функция эффективно объединит диапазоны. Эта функция аналогична функции <b>СЦЕП</b>, но разница заключается в том, что функция <b>СЦЕП</b> не принимает разделитель.</p>
            <p>Синтаксис функции <b>ОБЪЕДИНИТЬ</b>:</p>
            <p style="text-indent: 150px;"><b><em>ОБЪЕДИНИТЬ(разделитель;игнорировать_пустые;текст1;[текст2];…)</em></b></p>
            <p>где</p>
            <p style="text-indent: 50px;"><b><em>разделитель</em></b> - разделитель, который надо вставить между текстовыми значениями. Может быть задан в виде текстовой строки, заключенной в двойные кавычки (например, <b>","</b> (запятая), <b>" "</b> (пробел), <b>"\"</b> (обратный слэш) и так далее) или в виде ссылки на ячейку или диапазон ячеек.</p>
            <p style="text-indent: 50px;"><b><em>игнорировать_пустые</em></b> - логическое значение, определяющее, должны ли игнорироваться пустые ячейки. Если задано значение ИСТИНА, пустые ячейки игнорируются.</p>
            <p style="text-indent: 50px;"><b><em>текст1(2)</em></b> - это до 252 значений данных. Каждое значение может быть текстовой строкой или ссылкой на диапазон ячеек.</p>
            <p>Чтобы применить функцию <b>ОБЪЕДИНИТЬ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Текст и данные</b>,</li>
                <li>щелкните по функции <b>ОБЪЕДИНИТЬ</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция ОБЪЕДИНИТЬ" src="../images/textjoin.png" /></p>
        </div>
	</body>
</html>