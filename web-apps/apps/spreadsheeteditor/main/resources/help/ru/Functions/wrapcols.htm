<!DOCTYPE html>
<html>
	<head>
		<title>Функция СВЕРНСТОЛБЦ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция СВЕРНСТОЛБЦ</h1>
            <p>Функция <b>СВЕРНСТОЛБЦ</b> - это одна из поисковых функций. Она переносит вектор строки или столбца после указанного числа значений.</p>
            <p class="note">Обратите внимание, что это формула массива. Чтобы узнать больше, обратитесь к статье <a href="../UsageInstructions/InsertArrayFormulas.htm" onclick="onhyperlinkclick(this)">Вставка формул массива</a>.</p>
            <p>Синтаксис функции <b>СВЕРНСТОЛБЦ</b>:</p>
            <p style="text-indent: 150px;"><b><em>СВЕРНСТОЛБЦ(вектор, количество_для_переноса, [заполняющее_значение])</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>вектор</em></b> - задает вектор или ссылку для переноса.</p>
            <p style="text-indent: 50px;"><b><em>количество_для_переноса</em></b> - задает максимальное количество значений для каждого столбца.</p>
            <p style="text-indent: 50px;"><b><em>заполняющее_значение</em></b> - задает значение, используемое для заполнения. Значение по умолчанию: #Н/Д.</p>
            <p>Чтобы применить функцию <b>СВЕРНСТОЛБЦ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class="icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class="icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Поиск и ссылки</b>,</li>
                <li>щелкните по функции <b>СВЕРНСТОЛБЦ</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <!--<p style="text-indent: 150px;"><img alt="Функция СВЕРНСТОЛБЦ" src="../images/wrapcols.png" /></p>-->
        </div>
	</body>
</html>