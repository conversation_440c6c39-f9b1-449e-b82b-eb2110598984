<!DOCTYPE html>
<html>
	<head>
		<title>Функция ОСПЛТ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ОСПЛТ</h1>
			<p>Функция <b>ОСПЛТ</b> - это одна из финансовых функций. Используется для вычисления размера платежа в счет погашения основного долга по инвестиции исходя из заданной процентной ставки и постоянной периодичности платежей.</p>
			<p>Синтаксис функции <b>ОСПЛТ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ОСПЛТ(ставка;период;кпер;пс;[бс];[тип])</em></b></p> 
			<p><em>где</em></p> 
				<p style="text-indent: 50px;"><b><em>ставка</em></b> - процентная ставка.</p>
        <p style="text-indent: 50px;"><b><em>период</em></b> - период, за который требуется рассчитать размер основного платежа. Может принимать значения от 1 до <b><em>кпер</em></b>.</p>
        <p style="text-indent: 50px;"><b><em>кпер</em></b> - количество платежных периодов.</p> 
				<p style="text-indent: 50px;"><b><em>пс</em></b> - текущий размер выплат.</p>
				<p style="text-indent: 50px;"><b><em>бс</em></b> - значение будущей стоимости, то есть остатка средств после последней выплаты. Необязательный аргумент. Если он опущен, аргумент <b><em>бс</em></b> полагается равным 0.</p>
				<p style="text-indent: 50px;"><b><em>тип</em></b> - срок выплаты. Необязательный аргумент. Если его значение равно 0 или он опущен, предполагается, что платеж должен быть произведен в конце периода. Если значение аргумента <b><em>тип</em></b> равно 1, платеж должен быть произведен в начале периода.</p>
				<p class="note"><b>Примечание:</b> выплачиваемые денежные средства (например, сберегательные вклады) представляются отрицательными числами; получаемые денежные средства (например, дивиденды) представляются положительными числами.
                    Единицы измерения аргументов "ставка" и "кпер" должны быть согласованы между собой: используйте N%/12 для аргумента "ставка"
                    и N*12 для аргумента "кпер", если речь идет о ежемесячных платежах, N%/4 для аргумента "ставка" и N*4 для аргумента "кпер",
                    если речь идет о ежеквартальных платежах, N% для аргумента "ставка" и N для аргумента "кпер", если речь идет о ежегодных платежах.
        </p>
			<p>Числовые значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>
			<p>Чтобы применить функцию <b>ОСПЛТ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Финансовые</b>,</li>
			<li>щелкните по функции <b>ОСПЛТ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ОСПЛТ" src="../images/ppmt.png" /></p>
		</div>
	</body>
</html>