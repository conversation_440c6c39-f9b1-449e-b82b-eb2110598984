<!DOCTYPE html>
<html>
<head>
    <title>Функция СОРТ</title>
	<meta charset="utf-8" />
	<meta name="description" content="The SORT function is one of the lookup and reference functions. Learn how to use the SORT function in Excel sheets and compatible files with ONLYOFFICE." />
	<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
	<link type="text/css" rel="stylesheet" href="../../images/sprite.css" />
	<script type="text/javascript" src="../callback.js"></script>
	<script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>Функция СОРТ</h1>
        <p>The <b>СОРТ</b> - это одна из поисковых функций. Она возвращает отсортированное содержимое диапазона данных или массива.</p>
        <p class="note">Обратите внимание, что это формула массива. Чтобы узнать больше, обратитесь к статье <a href="../UsageInstructions/InsertArrayFormulas.htm" onclick="onhyperlinkclick(this)">Вставка формул массива</a>.</p>
        <p>Синтаксис функции <b>СОРТ</b>:</p>
        <p style="text-indent: 150px;"><b><em>=СОРТ(массив,[индекс_сортировки],[порядок_сортировки],[по_столбцу])</em></b></p>
        <p><em>где</em></p>
        <p style="text-indent: 50px;"><b><em>массив</em></b> - диапазон ячеек для фильтрации.</p>
        <p style="text-indent: 50px;"><b><em>[индекс_сортировки]</em></b> - число, определяющее столбец или строку для сортировки. Это необязательный аргумент.</p>
        <p style="text-indent: 50px;">
            <b><em>[порядок_сортировки]</em></b> - число, определяющее порядок сортировки. Это необязательный аргумент. Он может принимать одно из следующих числовых значений:
            <ul>
                <li><em><b>1 или не указано</b></em> - (установлено по умолчанию) порядок по возрастанию,</li>
                <li><em><b>-1</b></em> - порядок по убыванию,</li>
            </ul>
        </p>
        <p style="text-indent: 50px;">
            <b><em>[по_столбцу]</em></b> - это логическое значение. Это необязательный аргумент. Он указывает направление сортировки.  Он может принимать одно из логических числовых значений:
            <ul>
                <li><em><b>ИСТИНА или не указано</b></em> - сортировка по строкам, когда ваши данные расположены вертикально.</li>
                <li><em><b>ЛОЖЬ</b></em> -  сортировка по столбцам, когда ваши данные расположены горизонтально.</li>
            </ul>
        </p>
        <p>Чтобы применить функцию <b>СОРТ</b>,</p>
        <ol>
            <li>выделите ячейку, в которой требуется отобразить результат,</li>
            <li>
                щелкните по значку <b>Вставить функцию</b> <div class="icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                <br />или щелкните по значку <div class="icon icon-function"></div> перед строкой формул,
            </li>
            <li>выберите из списка группу функций <b>Поиск и ссылки</b>,</li>
            <li>щелкните по функции <b>СОРТ</b>,</li>
            <li>введите требуемые аргументы через точку с запятой или выделите мышью диапазон ячеек,</li>
            <li>нажмите клавишу <b>Enter</b>.</li>
        </ol>
        <p>Результат будет отображен в выбранной ячейке.</p>
        <!--<p style="text-indent: 150px;"><img alt="SORT Function" src="../images/sort.png" /></p>-->
    </div>
</body>
</html>