<!DOCTYPE html>
<html>
	<head>
		<title>Функция СЛЧИС</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СЛЧИС</h1>
			<p>Функция <b>СЛЧИС</b> - это одна из математических и тригонометрических функций. Возвращает случайное число, которое больше или равно <b>0</b> и меньше <b>1</b>. Функция <b>не</b> требует аргумента.</p>
			<p>Синтаксис функции <b>СЛЧИС</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СЛЧИС()</em></b></p> 
			<p>Чтобы применить функцию <b>СЛЧИС</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Математические</b>,</li>
			<li>щелкните по функции <b>СЛЧИС</b>,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция СЛЧИС" src="../images/rand.png" /></p>
		</div>
	</body>
</html>