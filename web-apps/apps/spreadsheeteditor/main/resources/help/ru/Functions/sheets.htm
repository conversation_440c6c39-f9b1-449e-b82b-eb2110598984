<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЛИСТЫ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ЛИСТЫ</h1>
            <p>Функция <b>ЛИСТЫ</b> - это одна из информационных функций. Возвращает количество листов в ссылке.</p>
            <p>Синтаксис функции <b>ЛИСТЫ</b>:</p>
            <p style="text-indent: 150px;"><b><em>ЛИСТЫ(ссылка)</em></b></p>
            <p>где <b><em>ссылка</em></b> - это ссылка, для которой необходимо знать количество листов. Если аргумент <b><em>ссылка</em></b> опущен, возвращается количество листов текущей книги.</p>
            <p>Чтобы применить функцию <b>ЛИСТЫ</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Информационные</b>,</li>
                <li>щелкните по функции <b>ЛИСТЫ</b>,</li>
                <li>введите требуемый аргумент,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция ЛИСТЫ" src="../images/sheets.png" /></p>
        </div>
	</body>
</html>