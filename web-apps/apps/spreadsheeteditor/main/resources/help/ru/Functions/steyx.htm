<!DOCTYPE html>
<html>
	<head>
		<title>Функция СТОШYX</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СТОШYX</h1>
			<p>Функция <b>СТОШYX</b> - это одна из статистических функций. Возвращает стандартную ошибку предсказанных значений Y для каждого значения X по регрессивной шкале.</p>
			<p>Синтаксис функции <b>СТОШYX</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СТОШYX(известные_значения_y;известные_значения_x)</em></b></p> 
			<p><em>где</em></p> 
				<p style="text-indent: 50px;"><b><em>известные_значения_y</em></b> - это массив зависимых переменных величин.</p> 
				<p style="text-indent: 50px;"><b><em>известные_значения_x</em></b> - это массив независимых переменных величин.</p>
        <p>Значения могут быть введены вручную или содержаться в ячейках, на которые даются ссылки. Пустые ячейки, логические значения, текст и значения 
        ошибок в составе массива игнорируются. Если текстовые представления чисел и логические значения введены непосредственно в функцию, они интерпретируются как числовые значения.</p>
				<p class="note"><b>Примечание:</b> аргументы <b><em>известные_значения_y</em></b> и <b><em>известные_значения_x</em></b> должны содержать одно и то же
        количество точек данных, иначе функция вернет значение ошибки <b>#Н/Д</b>.</p>
      <p>
        Чтобы применить функцию <b>СТОШYX</b>,
      </p>
      <ol>
        <li>выделите ячейку, в которой требуется отобразить результат,</li>
        <li>
          щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
          <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
          <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
        </li>
        <li>
          выберите из списка группу функций <b>Статистические</b>,
        </li>
        <li>
          щелкните по функции <b>СТОШYX</b>,</li>
			<li>введите требуемые аргументы через точку с запятой или выделите диапазон ячеек мышью,</li>        
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выделенной ячейке.</p>             
			<p style="text-indent: 150px;"><img alt="Функция СТОШYX" src="../images/steyx.png" /></p>
		</div>
	</body>
</html>