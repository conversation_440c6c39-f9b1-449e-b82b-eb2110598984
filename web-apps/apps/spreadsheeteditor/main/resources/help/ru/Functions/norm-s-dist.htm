<!DOCTYPE html>
<html>
	<head>
		<title>Функция НОРМ.СТ.РАСП</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция НОРМ.СТ.РАСП</h1>
            <p>Функция <b>НОРМ.СТ.РАСП</b> - это одна из статистических функций. Возвращает стандартное нормальное интегральное распределение. Это распределение имеет среднее, равное нулю, и стандартное отклонение, равное единице.</p>
            <p>Синтаксис функции <b>НОРМ.СТ.РАСП</b>:</p>
            <p style="text-indent: 150px;"><b><em>НОРМ.СТ.РАСП(z;интегральная)</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>z</em></b> - значение, для которого требуется вычислить распределение; числовое значение, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p>
            <p style="text-indent: 50px;"><b><em>интегральная</em></b> - форма функции; логическое значение: TRUE (ИСТИНА) или FALSE (ЛОЖЬ). Если этот аргумент имеет значение TRUE (ИСТИНА), возвращается интегральная функция распределения. Если этот аргумент имеет значение FALSE (ЛОЖЬ), возвращается весовая функция распределения.</p>
            <p>Чтобы применить функцию <b>НОРМ.СТ.РАСП</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>НОРМ.СТ.РАСП</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция НОРМ.СТ.РАСП" src="../images/norm-s-dist.png" /></p>
        </div>
	</body>
</html>