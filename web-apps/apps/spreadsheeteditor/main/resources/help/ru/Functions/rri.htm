<!DOCTYPE html>
<html>
	<head>
		<title>Функция ЭКВ.СТАВКА</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ЭКВ.СТАВКА</h1>
            <p>Функция <b>ЭКВ.СТАВКА</b> - это одна из финансовых функций. Возвращает эквивалентную процентную ставку для роста инвестиции.</p>
            <p>Синтаксис функции <b>ЭКВ.СТАВКА</b>:</p>
            <p style="text-indent: 150px;"><b><em>ЭКВ.СТАВКА(кпер;пс;бс)</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>кпер</em></b> - количество периодов для инвестиций.</p>
            <p style="text-indent: 50px;"><b><em>пс</em></b> - стоимость инвестиции на текущий момент.</p>
            <p style="text-indent: 50px;"><b><em>бс</em></b> - стоимость инвестиции в будущем.</p>
            <p>Числовые значения могут быть введены вручную или находиться в ячейке, на которую дается ссылка.</p>
            <p>Чтобы применить функцию <b>ЭКВ.СТАВКА</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Финансовые</b>,</li>
                <li>щелкните по функции <b>ЭКВ.СТАВКА</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция ЭКВ.СТАВКА" src="../images/rri.png" /></p>
        </div>
	</body>
</html>