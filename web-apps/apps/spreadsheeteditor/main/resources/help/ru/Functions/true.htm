<!DOCTYPE html>
<html>
	<head>
		<title>Функция ИСТИНА</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ИСТИНА</h1>
			<p>Функция <b>ИСТИНА</b> - это одна из логических функций. Функция возвращает значение ИСТИНА и <b>не</b> требует аргумента.</p>
			<p>Синтаксис функции <b>ИСТИНА</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ИСТИНА()</em></b></p> 
			<p>Чтобы применить функцию <b>ИСТИНА</b>:</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Логические</b>,</li>
			<li>щелкните по функции <b>ИСТИНА</b>,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ИСТИНА" src="../images/true.png" /></p>
		</div>
	</body>
</html>