<!DOCTYPE html>
<html>
	<head>
		<title>Функция ДОЛЯГОДА</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ДОЛЯГОДА</h1>
			<p>Функция <b>ДОЛЯГОДА</b> - это одна из функций даты и времени. Возвращает долю года, представленную числом целых дней между начальной и конечной датами, вычисляемую заданным способом.</p>
			<p>Синтаксис функции <b>ДОЛЯГОДА</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ДОЛЯГОДА(нач_дата;кон_дата;[базис])</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>нач_дата</em></b> - число, представляющее первую дату периода, введенное с помощью функции <a href="Date.htm" onclick="onhyperlinkclick(this)">ДАТА</a> или другой функции даты и времени.</p>
			<p style="text-indent: 50px;"><b><em>кон_дата</em></b> - число, представляющее последнюю дату периода, введенное с помощью функции <a href="Date.htm" onclick="onhyperlinkclick(this)">ДАТА</a> или другой функции даты и времени.</p>
			<p style="text-indent: 50px;"><b><em>базис</em></b> - используемый способ вычисления дня; числовое значение, большее или равное 0, но меньшее или равное 4. Может иметь одно из следующих значений:</p>
			<table style="width: 40%">
				<tr>
					<td><b>Числовое значение</b></td>
					<td><b>Способ вычисления</b></td>
				</tr>
				<tr>
					<td>0</td>
					<td>Американский метод (NASD) 30/360</td>
				</tr>
				<tr>
					<td>1</td>
					<td>Фактический метод</td>
				</tr>
				<tr>
					<td>2</td>
					<td>Фактический/360 метод</td>
				</tr>
				<tr>
					<td>3</td>
					<td>Фактический/365 метод</td>
				</tr>
				<tr>
					<td>4</td>
					<td>Европейский метод 30/360</td>
				</tr>
			</table>
			<p class="note"><b>Примечание</b>: если аргументы <b><em>нач_дата</em></b>, <b><em>кон_дата</em></b> или <b><em>базис</em></b> являются десятичными значениями, функция ДОЛЯГОДА не учитывает числа справа от десятичной точки.
			</p>
			<p>Чтобы применить функцию <b>ДОЛЯГОДА</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Дата и время</b>,</li>
			<li>щелкните по функции <b>ДОЛЯГОДА</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ДОЛЯГОДА" src="../images/yearfrac.png" /></p>
		</div>
	</body>
</html>