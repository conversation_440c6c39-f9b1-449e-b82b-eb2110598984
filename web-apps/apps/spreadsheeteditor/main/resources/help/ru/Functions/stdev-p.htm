<!DOCTYPE html>
<html>
	<head>
		<title>Функция СТАНДОТКЛОН.Г</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция СТАНДОТКЛОН.Г</h1>
            <p>Функция <b>СТАНДОТКЛОН.Г</b> - это одна из статистических функций. Вычисляет стандартное отклонение по генеральной совокупности, заданной аргументами. При этом логические значения и текст игнорируются.</p>
            <p>Синтаксис функции <b>СТАНДОТКЛОН.Г</b>:</p>
            <p style="text-indent: 150px;"><b><em>СТАНДОТКЛОН.Г(число1;[число2]; ...)</em></b></p>
            <p>где <b><em>число1(2)</em></b> - это до 254 числовых значений, введенных вручную или находящихся в ячейках, на которые даются ссылки.</p>
            <p class="note"><b>Примечание:</b> если аргумент, заданный в виде ссылки, содержит текст, логические значения или пустые ячейки, эти значения игнорируются, но ячейки с нулевыми значениями учитываются.</p>
            <p>Чтобы применить функцию <b>СТАНДОТКЛОН.Г</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>СТАНДОТКЛОН.Г</b>,</li>
                <li>введите требуемые аргументы через точку с запятой или выделите мышью диапазон ячеек,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция СТАНДОТКЛОН.Г" src="../images/stdev-p.png" /></p>
        </div>
	</body>
</html>