<!DOCTYPE html>
<html>
	<head>
		<title>Функция СЕКУНДЫ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция СЕКУНДЫ</h1>
			<p>Функция <b>СЕКУНДЫ</b> - это одна из функций даты и времени. Возвращает количество секунд (число от 0 до 59), соответствующее заданному значению времени.</p>
			<p>Синтаксис функции <b>СЕКУНДЫ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>СЕКУНДЫ(время_в_числовом_формате)</em></b></p> 
			<p>где <b><em>время_в_числовом_формате</em></b> - это значение, введенное вручную или находящееся в ячейке, на которую дается ссылка.</p>
			<p class="note"><b>Примечание</b>: аргумент <b><em>время_в_числовом_формате</em></b> может быть выражен строковым значением (например, "13:39:15"), десятичным числом (например, 0.56 соответствует 13:26:24) или результатом какой-либо формулы (например, результатом функции ТДАТА в стандартном формате - 26.09.2012 13:39)</p>
			<p>Чтобы применить функцию <b>СЕКУНДЫ</b>:</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Дата и время</b>,</li>
			<li>щелкните по функции <b>СЕКУНДЫ</b>,</li>
			<li>введите требуемый аргумент,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция СЕКУНДЫ" src="../images/second.png" /></p>
		</div>
	</body>
</html>