<!DOCTYPE html>
<html>
	<head>
		<title>Функция ТЕКСТ</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
			<h1>Функция ТЕКСТ</h1>
			<p>Функция <b>ТЕКСТ</b> - это одна из функций для работы с текстом и данными. Преобразует числовое значение в текст в заданном формате.</p>
			<p>Синтаксис функции <b>ТЕКСТ</b>:</p> 
			<p style="text-indent: 150px;"><b><em>ТЕКСТ(значение;формат)</em></b></p> 
			<p><em>где</em></p> 
			<p style="text-indent: 50px;"><b><em>значение</em></b> - числовое значение, которое требуется преобразовать в текст.</p> 
			<p style="text-indent: 50px;"><b><em>формат</em></b> - формат, в котором требуется отобразить результат.</p> 
			<p>Эти данные можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
			<p>Чтобы применить функцию <b>ТЕКСТ</b>,</p>
			<ol>
			<li>выделите ячейку, в которой требуется отобразить результат,</li>
			<li>щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
				<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
				<br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
			</li>
			<li>выберите из списка группу функций <b>Текст и данные</b>,</li>
			<li>щелкните по функции <b>ТЕКСТ</b>,</li>
			<li>введите требуемые аргументы через точку с запятой,</li>
			<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<p style="text-indent: 150px;"><img alt="Функция ТЕКСТ" src="../images/text.png" /></p>
		</div>
	</body>
</html>