<!DOCTYPE html>
<html>
	<head>
		<title>Функция ПОСЛЕД</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
			<div class="search-field">
				<input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
			</div>
			<h1>Функция ПОСЛЕД</h1>
			<p>Функция <b>ПОСЛЕД</b> - это одна из математических и тригонометрических функций. Она возвращает сгенерированный массив последовательных чисел.</p>
			<p class="note">Обратите внимание, что это формула массива. Чтобы узнать больше, обратитесь к статье <a href="../UsageInstructions/InsertArrayFormulas.htm" onclick="onhyperlinkclick(this)">Вставка формул массива</a>.</p>
			<p>Синтаксис функции <b>ПОСЛЕД</b>:</p>
			<p style="text-indent: 150px;"><b><em>ПОСЛЕД(строки,[столбцы],[начало],[шаг])</em></b></p>
			<p><em>где</em></p>
			<p style="text-indent: 50px;"><b><em>строки</em></b> - это количество возвращаемых строк.</p>
			<p style="text-indent: 50px;"><b><em>столбцы</em></b> - это количества возвращаемых столбцов.</p>
			<p style="text-indent: 50px;"><b><em>начало</em></b> - это первое число в последовательности.</p>
			<p style="text-indent: 50px;"><b><em>шаг</em></b> - это значение приращения для каждого последующего значения в диапазоне ячеек.</p>
			<p>Чтобы применить функцию <b>ПОСЛЕД</b>,</p>
			<ol>
				<li>выделите ячейку, в которой требуется отобразить результат,</li>
				<li>
					щелкните по значку <b>Вставить функцию</b> <div class="icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
					<br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
					<br />или щелкните по значку <div class="icon icon-function"></div> перед строкой формул,
				</li>
				<li>выберите из списка группу функций <b>Математические</b>,</li>
				<li>щелкните по функции <b>ПОСЛЕД</b>,</li>
				<li>введите требуемые аргументы через точку с запятой,</li>
				<li>нажмите клавишу <b>Enter</b>.</li>
			</ol>
			<p>Результат будет отображен в выбранной ячейке.</p>
			<!--<p style="text-indent: 150px;"><img alt="SEQUENCE Function" src="../images/sequence.png" /></p>-->
		</div>
	</body>
</html>