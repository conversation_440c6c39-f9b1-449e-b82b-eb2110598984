<!DOCTYPE html>
<html>
	<head>
		<title>Функция ВЕЙБУЛЛ.РАСП</title>
		<meta charset="utf-8" />
		<meta name="description" content="" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Поиск" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Функция ВЕЙБУЛЛ.РАСП</h1>
            <p>Функция <b>ВЕЙБУЛЛ.РАСП</b> - это одна из статистических функций. Возвращает распределение Вейбулла. Это распределение используется при анализе надежности, например для вычисления среднего времени наработки на отказ какого-либо устройства.</p>
            <p>Синтаксис функции <b>ВЕЙБУЛЛ.РАСП</b>:</p>
            <p style="text-indent: 150px;"><b><em>ВЕЙБУЛЛ.РАСП(x;альфа;бета;интегральная)</em></b></p>
            <p><em>где</em></p>
            <p style="text-indent: 50px;"><b><em>x</em></b> - значение, для которого вычисляется функция; числовое значение большее или равное 0.</p>
            <p style="text-indent: 50px;"><b><em>альфа</em></b> - первый параметр распределения, числовое значение больше 0.</p>
            <p style="text-indent: 50px;"><b><em>бета</em></b> - второй параметр распределения, числовое значение больше 0.</p>
            <p style="text-indent: 50px;"><b><em>интегральная</em></b> - форма функции. Это логическое значение: ИСТИНА или ЛОЖЬ. Если этот аргумент имеет значение ИСТИНА, возвращается интегральная функция распределения. Если этот аргумент имеет значение ЛОЖЬ, возвращается функция плотности распределения.</p>
            <p>Эти значения можно ввести вручную или использовать в качестве аргументов ссылки на ячейки.</p>
            <p>Чтобы применить функцию <b>ВЕЙБУЛЛ.РАСП</b>,</p>
            <ol>
                <li>выделите ячейку, в которой требуется отобразить результат,</li>
                <li>
                    щелкните по значку <b>Вставить функцию</b> <div class = "icon icon-insertfunction"></div>, расположенному на верхней панели инструментов,
                    <br />или щелкните правой кнопкой мыши по выделенной ячейке и выберите в меню команду <b>Вставить функцию</b>,
                    <br />или щелкните по значку <div class = "icon icon-function"></div> перед строкой формул,
                </li>
                <li>выберите из списка группу функций <b>Статистические</b>,</li>
                <li>щелкните по функции <b>ВЕЙБУЛЛ.РАСП</b>,</li>
                <li>введите требуемые аргументы через точку с запятой,</li>
                <li>нажмите клавишу <b>Enter</b>.</li>
            </ol>
            <p>Результат будет отображен в выбранной ячейке.</p>
            <p style="text-indent: 150px;"><img alt="Функция ВЕЙБУЛЛ.РАСП" src="../images/weibull-dist.png" /></p>
        </div>
	</body>
</html>