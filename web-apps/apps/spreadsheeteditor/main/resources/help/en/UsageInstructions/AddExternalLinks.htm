﻿<!DOCTYPE html>
<html>
	<head>
		<title>Add external links to cells</title>
		<meta charset="utf-8" />
        <meta name="description" content="Add an external link to a cell or a range of cells in another workbook" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
			<h1>Add external links to cells</h1>
			<p>In the <a href="https://www.onlyoffice.com/spreadsheet-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Spreadsheet Editor</b></a>, you can create an external link to a cell or a range of cells in another workbook. The external links to cells can be created between files within the current portal (in the online editor) or between local files (in the desktop editor).</p>
            <p class="note"><b>Note</b>: in the online editor, the ability to insert data via an external link must also be implemented on the integrator side (e.g., in a third-party document management system).</p>
            <p>If data in the source workbook changes, you can update values in the destination workbook without copying them manually once again.</p>            
			<p>To add an external link,</p>
            <ol>
				<li>open the source workbook and the destination workbook,</li>                
                <li>in the source workbook, copy a cell or a range of cells (Ctrl+C),</li>
                <li>in the destination workbook, paste the copied data (Ctrl+V),</li>
                <li>click the Paste Special button and choose the <b>Paste link</b> option (or use the <em>Ctrl</em> key to open the <b>Paste Special</b> menu, then press <em>N</em>).</li>				
			</ol>
            <p>The link will be added. If you click the cell which contains an external link, it looks like <code>='[SourceWorkbook.xlsx]Sheet1'!A1</code> in the formula bar.</p>          
            <p>To update the added external links,</p>
            <ol>
                <li>switch to the <b>Data</b> tab,</li>
                <li>
                    click the <b>External Links</b> button,
                    <p><img alt="External Links" src="../images/externallinks.png" /></p>
                </li>
                <li>activate the <b>Automatically update data from the linked sources</b> to keep <em>all data</em> constantly up-to-date,</li>
                <li>to update <em>certain values</em>, select them and click the <b>Update values</b> button.</li>
            </ol>
            <p>The status will be changed to 'OK'.</p>
            <p>In the same <b>External links</b> window, you can click the <b>Open source</b> button to go to the source workbook; or click the <b>Change source</b> button to change the source link.</p>
            <p>When you open the workbook which contains external links, the warning appears:</p>
            <p><img alt="External Links" src="../images/extlinkwarning.png" /></p>
            <p>Click <b>Update</b> to update all external links.</p>
            <p>To break the added external links,</p>
            <ol>
                <li>switch to the <b>Data</b> tab,</li>
                <li>click the <b>External Links</b> button,</li>
                <li>select the necessary link in the list,</li>
                <li>click the <b>Break Links</b> button or click the arrow next to it and choose whether you want to <b>Break links</b> (currently selected) or <b>Break all links</b>.</li>
            </ol>
            <p>The links will be broken, and the values will not be updated.</p>
		</div>
	</body>
</html>
