﻿<!DOCTYPE html>
<html>
	<head>
		<title>Spell-checking</title>
		<meta charset="utf-8" />
		<meta name="description" content="Spell check the text in your language while editing a spreadsheet" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>Spell-checking</h1>
            <p>The <a href="https://www.onlyoffice.com/en/spreadsheet-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Spreadsheet Editor</b></a> allows you to check the spelling of the text in a certain language and correct mistakes while editing. In the <em>desktop version</em>, it's also possible to add words into a custom dictionary which is common for all three editors.</p>
            <p class="note">Starting from <b>version 6.3</b>, the ONLYOFFICE editors support the <b>SharedWorker</b> interface for smoother operation without significant memory consumption. If your browser does not support SharedWorker then just <b>Worker</b> will be active. For more information about SharedWorker please refer to <a href="https://javascript.plainenglish.io/introduction-to-shared-workers-533d9abe9de3">this article</a>.</p>
            <p>Click the <span class="icon icon-spellcheckdeactivated"></span> <b>Spell checking</b> icon on the left sidebar to open the spell checking panel.</p>
            <p><img alt="Spell checking panel" src="../images/spellchecking_panel.png" /></p>
            <p>The upper left cell that contains a misspelled text value will be automatically selected in the current worksheet. The first misspelled word will be displayed in the spell checking field, and the suggested similar words with correct spelling will appear in the field below. </p>
            <p>Use the <span class="icon icon-go_to_the_next_word"></span> <b>Go to the next word</b> button to navigate through misspelled words.</p>
            <h3>Replace misspelled words</h3>
            <p>To <b>replace</b> the currently selected misspelled word with the suggested one, choose one of the suggested similar words spelled correctly and use the <b>Change</b> option:</p>
            <ul>
                <li>click the <b>Change</b> button, or</li>
                <li>click the downward arrow next to the <b>Change</b> button and select the <b>Change</b> option.</li>
            </ul>
            <p>The current word will be replaced and you will proceed to the next misspelled word.</p>
            <p>To quickly replace all the identical words repeated on the worksheet, click the downward arrow next to the <b>Change</b> button and select the <b>Change all</b> option.</p>
            <h3>Ignore words</h3>
            <p>To skip the current word:</p>
            <ul>
                <li>click the <b>Ignore</b> button, or</li>
                <li>click the downward arrow next to the <b>Ignore</b> button and select the <b>Ignore</b> option.</li>
            </ul>
            <p>The current word will be skipped, and you will proceed to the next misspelled word.</p>
            <p>To skip all the identical words repeated in the worksheet, click the downward arrow next to the <b>Ignore</b> button and select the <b>Ignore all</b> option.</p>
            <p>If the current word is missed in the dictionary, you can add it to the custom dictionary using the <b>Add to Dictionary</b> button on the spell checking panel. This word will not be treated as a mistake next time. This option is available in the <em>desktop version</em>.</p>
            <p>The <b>Dictionary Language</b> which is used for spell-checking is displayed in the list below. You can change it, if necessary.</p>
            <p>Once you verify all the words in the worksheet, the <b>Spellcheck has been completed</b> message will appear on the spell-checking panel.</p>
            <p>To close the spell-checking panel, click the <span class="icon icon-spellcheckactivated"></span> <b>Spell checking</b> icon on the left sidebar.</p>
            <h3>Change the spell check settings</h3>
            <p>To change the spell-checking settings, go to the spreadsheet editor advanced settings (<b>File</b> tab -> <b>Advanced Settings</b>) and scroll down to the <b>Proofing</b> section. Here you can adjust the following parameters:</p>
            <p><img alt="Spell checking settings" src="../images/spellchecking_settings.png" /></p>
            <ul>
                <li><b>Dictionary language</b> - select one of the available languages from the list. The <b>Dictionary Language</b> on the spell-checking panel will be changed correspondingly.</li>
                <li><b>Ignore words in UPPERCASE</b> - check this option to ignore words written in capital letters, e.g. acronyms like <em>SMB</em>.</li>
                <li><b>Ignore words with numbers</b> - check this option to ignore words containing numbers, e.g. acronyms like <em>B2B</em>.</li>
                <li><b>AutoCorrect options...</b> - to learn more about the available autocorrect options, please refer to the <a href="../UsageInstructions/MathAutoCorrect.htm">following article</a>.</li>
            </ul>
            <p>To save the changes you made, click the <b>Apply</b> button.</p>
        </div>
	</body>
</html>