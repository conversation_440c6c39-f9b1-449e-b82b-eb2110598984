﻿<!DOCTYPE html>
<html>
	<head>
		<title>Supported Formats of Spreadsheets</title>
		<meta charset="utf-8" />
		<meta name="description" content="The list of spreadsheet formats supported by Spreadsheet Editor" />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
		<div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
		<h1>Supported Formats of Spreadsheets</h1>
			<p>A <b>spreadsheet</b> is a table of data organized in rows and columns. 
			It is most frequently used to store financial information because of its ability to re-calculate the entire sheet automatically after a change to a single cell is made.
			The <a href="https://www.onlyoffice.com/en/spreadsheet-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Spreadsheet Editor</b></a> allows you to open, view and edit the most popular spreadsheet file formats.</p>
            <p class="note">While uploading or opening the file for editing, it will be converted to the Office Open XML (XLSX) format. It's done to speed up the file processing and increase the interoperability.</p>
            <p>The following table contains the formats which can be opened for viewing and/or editing.</p>
            <table>
                <tr>
                    <td><b>Formats</b></td>
                    <td><b>Description</b></td>
                    <td>View natively</td>
                    <td>View after conversion to OOXML</td>
                    <td>Edit natively</td>
                    <td>Edit after conversion to OOXML</td>
                </tr>
                <tr>
                    <td>CSV</td>
                    <td>Comma Separated Values<br />A file format used to store tabular data (numbers and text) in plain-text form.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>ET</td>
                    <td>WPS Spreadsheets Workbook <br />A spreadsheet file format included in the WPS Office suite that supports charts and formulas and stores data in rows and columns of cells.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>ETT</td>
                    <td>WPS Spreadsheets Template <br />A spreadsheet template included in the WPS Office suite. ETT files are similar to ET files, storing rows and columns of data, charts, and graphs, but they are primarily used for duplicating spreadsheets with similar layouts and information.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>FODS</td>
                    <td>OpenDocument Flat XML Spreadsheet <br />An XML-based file format for saving and exchanging spreadsheets.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>ODS</td>
                    <td>A file extension for spreadsheet files used by OpenOffice and StarOffice suites, an open standard for spreadsheets.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>OTS</td>
                    <td>OpenDocument Spreadsheet Template<br />An OpenDocument file format for spreadsheet templates. An OTS template contains formatting settings, styles etc. and can be used to create multiple spreadsheets with the same formatting</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>SXC</td>
                    <td>Sun XML Calc <br />An XML based spreadsheet file format that belongs to OpenOffice suite, supports formulas, functions, macros and charts.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>XLS</td>
                    <td>File extension for spreadsheet files created by Microsoft Excel.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <!--<tr>
        <td>XLSB</td>
        <td>Excel Binary Spreadsheet<br />File extension for spreadsheet files which store information in binary format instead of XML.</td>
        <td></td>
        <td>+</td>
        <td></td>
        <td>+</td>
    </tr>-->
                <tr>
                    <td>XLSX</td>
                    <td>Default file extension for spreadsheet files written in Microsoft Office Excel 2007 (or later versions).</td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                </tr>
                <tr>
                    <td>XLTM</td>
                    <td>An XLTM file is a macro-enabled spreadsheet template created by Microsoft Excel.</td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                </tr>
                <tr>
                    <td>XLTX</td>
                    <td>Excel Open XML Spreadsheet Template<br />Zipped, XML-based file format developed by Microsoft for spreadsheet templates. An XLTX template contains formatting settings, styles etc. and can be used to create multiple spreadsheets with the same formatting.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
                <tr>
                    <td>XML</td>
                    <td> Extensible Markup Language (XML)<br />A file format that is used for structuring, storing and transmitting data.</td>
                    <td></td>
                    <td>+</td>
                    <td></td>
                    <td>+</td>
                </tr>
            </table>
            <p>The following table contains the formats in which you can download a spreadsheet from the <b>File</b> -> <b>Download as</b> menu.</p>
            <table>
                <tr>
                    <td><b>Input format</b></td>
                    <td><b>Can be downloaded as</b></td>
                </tr>
                <tr>
                    <td>CSV</td>
                    <td>JPG, ODS, OTS, PDF, PDF/A, PNG, XLSM, XLSX, XLTM, XLTX</td>
                </tr>
                <tr>
                    <td>ET</td>
                    <td>CSV, JPG, ODS, OTS, PDF, PDF/A, PNG, XLSM, XLSX, XLTM, XLTX</td>
                </tr>
                <tr>
                    <td>ETT</td>
                    <td>CSV, JPG, ODS, OTS, PDF, PDF/A, PNG, XLSM, XLSX, XLTM, XLTX</td>
                </tr>
                <tr>
                    <td>FODS</td>
                    <td>CSV, JPG, ODS, OTS, PDF, PDF/A, PNG, XLSM, XLSX, XLTM, XLTX</td>
                </tr>
                <tr>
                    <td>ODS</td>
                    <td>CSV, JPG, ODS, OTS, PDF, PDF/A, PNG, XLSM, XLSX, XLTM, XLTX</td>
                </tr>
                <tr>
                    <td>SXC</td>
                    <td>CSV, JPG, ODS, OTS, PDF, PDF/A, PNG, XLSM, XLSX, XLTM, XLTX</td>
                </tr>
                <tr>
                    <td>OTS</td>
                    <td>CSV, JPG, ODS, PDF, PDF/A, PNG, XLSM, XLSX, XLTM, XLTX</td>
                </tr>
                <tr>
                    <td>XLS</td>
                    <td>CSV, JPG, ODS, OTS, PDF, PDF/A, PNG, XLSM, XLSX, XLTM, XLTX</td>
                </tr>
                <!--<tr>
        <td>XLSB</td>
        <td>CSV, JPG, ODS, OTS, PDF, PDF/A, PNG, XLSX, XLTX</td>
    </tr>-->
                <tr>
                    <td>XLSM</td>
                    <td>CSV, JPG, ODS, OTS, PDF, PDF/A, PNG, XLSX, XLTM, XLTX</td>
                </tr>
                <tr>
                    <td>XLSX</td>
                    <td>CSV, JPG, ODS, OTS, PDF, PDF/A, PNG, XLSM, XLTM, XLTX</td>
                </tr>
                <tr>
                    <td>XLTM</td>
                    <td>CSV, JPG, ODS, OTS, PDF, PDF/A, PNG, XLSM, XLSX, XLTX</td>
                </tr>
                <tr>
                    <td>XML</td>
                    <td>CSV, JPG, ODS, OTS, PDF, PDF/A, PNG, XLSM, XLSX, XLTM, XLTX</td>
                </tr>
            </table>
            <p>You can also refer to the conversion matrix on <a href="https://api.onlyoffice.com/docs/docs-api/additional-api/conversion-api/conversion-tables/#spreadsheet-file-formats" target="_blank" onclick="onhyperlinkclick(this)"><b>api.onlyoffice.com</b></a> to see possibility of conversion your spreadsheets into the most known file formats.</p>
		</div>
	</body>
</html>