﻿<!DOCTYPE html>
<html>
	<head>
		<title>View Settings and Navigation Tools</title>
		<meta charset="utf-8" />
		<meta name="description" content="The description of view settings and navigation tools: sheet navigation buttons, sheet tabs, scrollbars, zoom." />
		<link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
		<link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
        <script type="text/javascript" src="../callback.js"></script>
        <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
	</head>
	<body>
        <div class="mainpart">
            <div class="search-field">
                <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
            </div>
            <h1>View Settings and Navigation Tools</h1>
            <p>To help you view and select cells in large spreadsheets, the <a href="https://www.onlyoffice.com/en/spreadsheet-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Spreadsheet Editor</b></a> offers several tools: adjustable bars, scrollbars, sheet navigation buttons, sheet tabs and zoom.</p>
            <h3>Adjust the View Settings</h3>
            <p>
                To adjust default view settings and to set the most convenient mode to work with the spreadsheet, go to the <a href="../ProgramInterface/ViewTab.htm" onclick="onhyperlinkclick(this)">View</a> tab.
                You can select the following options:
            </p>
            <ul>
                <li><b>Sheet View</b> - to manage sheet views. To learn more about sheet views, please read <a href="../UsageInstructions/SheetView.htm" onclick="onhyperlinkclick(this)">the following article</a>.</li>
                <li><b>Zoom</b> - to set the required zoom value from 50% to 500% from the drop-down list.</li>
                <li><b>Interface Theme</b> - choose one of the available interface themes from the drop-down menu: <em>Same as system</em>, <em>Light</em>, <em>Classic Light</em>, <em>Dark</em>, <em>Contrast Dark</em>.</li>
                <li><b>Freeze Panes</b> - freezes all the rows above the active cell and all the columns to the left of the active cell so that they remain visible when you scroll the spreadsheet to the right or down. To unfreeze the panes, just click this option once again or right-click anywhere within the worksheet and select the <b>Unfreeze Panes</b> option from the menu.</li>
                <li><b>Formula Bar</b> - when disabled, hides the bar below the top toolbar which is used to enter and review the formulas and their contents. To show the hidden <b>Formula Bar</b>, click this option once again. Dragging formula bar bottom line to expand it toggles <b>Formula Bar</b> height to show one row.</li>
                <li><b>Headings</b> - when disabled, hides the column heading at the top and row heading on the left side of the worksheet. To show the hidden <b>Headings</b>, click this option once again.</li>
                <li><b>Gridlines</b> - when disabled, hides the lines around the cells. To show the hidden <b>Gridlines</b>, click this option once again.</li>
                <li><b>Show Zeros</b> - allows <b>“0”</b> to be visible when entered in a cell. TO disable this option, uncheck the box.</li>
                <li>
                    <b>Always Show Toolbar</b> - when this option is disabled, the top toolbar that contains commands will be hidden while tab names remain visible.
                    <p class="note">Alternatively, you can just double-click any tab to hide the top toolbar or display it again.</p>
                </li>
                <li><b>Combine Sheet and Status Bars</b> - displays all sheet navigation tools and status bar in a single line. By default, this option is active. If you disable it, the status bar will appear in two lines.</li>
                <li><b>Left Panel</b> - when disabled, hides the left panel where <b>Search</b>, <b>Comments</b>, etc. buttons are located. To show the left panel, check this box.</li>
                <li><b>Right Panel</b> - when disabled, hides the right panel where <b>Settings</b> are located. To show the right panel, check this box.</li>
            </ul>
            <p>The right sidebar is minimized by default. To expand it, select any object (e.g., image, chart, shape) and click the icon of the currently activated tab on the right. To minimize the right sidebar, click the icon once again.</p>
            <p>You can also change the size of the opened <b>Comments</b> <span class="onlineDocumentFeatures"> or <b>Chat</b></span> panel using the simple drag-and-drop: move the mouse cursor over the left sidebar border so that it turns into the bidirectional arrow and drag the border to the right to extend the sidebar width. To restore its original width, move the border to the left.</p>
            <h3>Use the Navigation Tools</h3>
            <p>To navigate through your spreadsheet, use the following tools:</p>
            <p>Use the <b>Tab</b> key on your keyboard to move to the cell to the right of the selected one.</p>
            <p>The <b>Scrollbars</b> (at the bottom or on the right side) are used to scroll up/down and left/right the current sheet. To navigate a spreadsheet using the scrollbars:</p>
            <ul>
                <li>click the up/down or right/left arrows on the scrollbars;</li>
                <li>drag the scroll box;</li>
                <li>scroll the mouse wheel to move vertically;</li>
                <li>use the combination of the <b>Shift</b> key and the mouse Scroll wheel to move horizontally;</li>
                <li>click any area to the left/right or above/below the scroll box on the scrollbar.</li>
            </ul>
            <p>You can also use the <b>mouse scroll wheel</b> to scroll your spreadsheet up or down.</p>
            <p>The <b>Sheet Navigation</b> buttons are situated in the left lower corner and are used to scroll the sheet list to the right/left and navigate among the sheet tabs.</p>
            <ul>
                <li>click the <b>Scroll sheet list left</b> <div class = "icon icon-previoussheet"></div> button to scroll the sheet list of the current spreadsheet to the left;</li>
                <li>click the <b>Scroll sheet list right</b> <div class = "icon icon-nextsheet"></div> button to scroll the sheet list of the current spreadsheet to the right;</li>
            </ul>
            <p>Use the <span class="icon icon-addworksheet"></span> button on the status bar to add a new worksheet.</p>
            <p>To select the necessary sheet:</p>
            <ul>
                <li>
                    click <div class="icon icon-list_worksheets"></div> button on the status bar to open the list of all sheets and to select the sheet you need. The list of sheets also displays the sheet status,
                    <p><img alt="Sheet List" src="../images/sheetlist.png" /></p>
                    <p>or</p>
                    <p>click the appropriate <b>Sheet Tab</b> next to <span class="icon icon-list_worksheets"></span> button.</p>
                    <p>
                        The <b>Zoom</b> buttons are situated in the lower right corner and are used to zoom in and out of the current sheet.
                        To change the currently selected zoom value that is displayed in percent, click it and select one of the available zoom options from the list (50% / 75% / 100% / 125% / 150% / 175% / 200% / 300% / 400% / 500%) or use the <b>Zoom in</b> <span class="icon icon-zoomin"></span> or <b>Zoom out</b> <span class="icon icon-zoomout"></span> buttons. The Zoom settings are also available on the <a href="../ProgramInterface/ViewTab.htm" onclick="onhyperlinkclick(this)">View</a> tab. The set scaling is maintained for all files during the current session.
                    </p>
                    <p class="note">You can set a default zoom value. Switch to the <b>File</b> tab of the top toolbar, go to the <a href="../HelpfulHints/AdvancedSettings.htm" onclick="onhyperlinkclick(this)"><b>Advanced Settings</b></a> section, choose the necessary <b>Default Zoom Value</b> from the list, and click the <b>Apply</b> button. To use the previously set scaling, scroll to the top of the drop-down list, and select the <b>Last Used</b> option.</p>
                </li>
            </ul>
        </div>
	</body>
</html>