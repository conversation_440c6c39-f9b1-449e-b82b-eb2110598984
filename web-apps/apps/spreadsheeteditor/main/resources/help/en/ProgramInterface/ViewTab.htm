﻿<!DOCTYPE html>
<html>
<head>
    <title>View tab</title>
    <meta charset="utf-8" />
    <meta name="description" content="Introducing the Spreadsheet Editor user interface - View tab" />
    <link type="text/css" rel="stylesheet" href="../../../../../../common/main/resources/help/editor.css" />
    <link type = "text/css" rel = "stylesheet" href = "../../images/sprite.css" />
    <script type="text/javascript" src="../callback.js"></script>
    <script type="text/javascript" src="../../../../../../common/main/resources/help/search/js/page-search.js"></script>
</head>
<body>
    <div class="mainpart">
        <div class="search-field">
            <input id="search" class="searchBar" placeholder="Search" type="text" onkeypress="doSearch(event)">
        </div>
        <h1>View tab</h1>
        <p>
            The <b>View</b> tab in the <a href="https://www.onlyoffice.com/spreadsheet-editor.aspx" target="_blank" onclick="onhyperlinkclick(this)"><b>Spreadsheet Editor</b></a> allows you to manage sheet view presets based on applied filters view options.
        </p>
        <div class="onlineDocumentFeatures">
            <p>The corresponding window of the Online Spreadsheet Editor:</p>
            <p><img alt="View tab" src="../images/interface/viewtab.png" /></p>
        </div>
        <div class="desktopDocumentFeatures">
            <p>The corresponding window of the Desktop Spreadsheet Editor:</p>
            <p><img alt="View tab" src="../images/interface/desktop_viewtab.png" /></p>
        </div>
        <p>View options available on this tab:</p>
        <ul>
            <li><b>Sheet View</b> allows to manage <a href="../UsageInstructions/SheetView.htm" onclick="onhyperlinkclick(this)">sheet view presets</a>.</li>
            <li><b>Normal</b> allows to see the spreadsheet fully.</li>
            <li><b>Page Break Preview</b> allows to see the printed area of the spreadsheet only.</li>
            <li><b>Zoom</b> allows to zoom in and zoom out your spreadsheet.</li>
            <li><b>Interface Theme</b> allows to change interface theme by choosing a <b>Same as system</b>, <b>Light</b>, <b>Classic Light</b>, <b>Dark</b>, <b>Contrast Dark</b>, or <b>Gray</b> theme.</li>
            <li><b>Freeze Panes</b> allows to <a target="_blank" href="https://helpcenter.onlyoffice.com/ONLYOFFICE-Editors/Editors-User-Guides/SpreadsheetEditor/Freeze-rows-and-columns.aspx" onclick="onhyperlinkclick(this)">freeze and unfreeze</a> certain panes or rows and columns.</li>
        </ul>
        <p>The following options allow you to configure the elements to display or to hide. Check the elements to make them visible:</p>
        <ul>
            <li><b>Formula Bar</b> to show a <a href="../HelpfulHints/Navigation.htm" onclick="onhyperlinkclick(this)">formula bar</a> above the spreadsheet.</li>
            <li><b>Headings</b> to show <a href="../HelpfulHints/Navigation.htm" onclick="onhyperlinkclick(this)">headings</a>, i.e., column headings at the top and row headings on the left.</li>
            <li><b>Gridlines</b> to show <a href="../HelpfulHints/Navigation.htm" onclick="onhyperlinkclick(this)">gridlines</a>, i.e., cell outlines.</li>
            <li><b>Show Zeros</b> to always show "0" when entered in a cell.</li>
            <li><b>Always Show Toolbar</b> to make the top toolbar always visible.</li>
            <li><b>Combine Sheet and Status Bars</b> to display all sheet navigation tools and status bar in a single line. The status bar will appear in two lines when this box is unchecked.</li>
            <li><b>Left Panel</b> to make the left panel visible.</li>
            <li><b>Right Panel</b> to make the right panel visible.</li>
        </ul>
        <p>The <b>Macros</b> button allows you to open the window where you can create and run your own macros. To learn more about macros, please refer to our <a target="_blank" href="https://api.onlyoffice.com/docs/plugin-and-macros/macros/getting-started/" onclick="onhyperlinkclick(this)">API Documentation</a>.</p>
    </div>
</body>
</html>