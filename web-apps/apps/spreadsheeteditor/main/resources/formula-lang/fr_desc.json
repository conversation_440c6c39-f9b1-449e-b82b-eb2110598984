{"DATE": {"a": "(année; mois; jour)", "d": "Renvoie un numéro qui représente la date dans le code de date et d’heure", "ad": "représente un nombre compris entre 1900 ou 9999 (en fonction du calendrier du classeur) et 9999!est un nombre compris entre 1 et 12 représentant le mois de l’année!est un nombre compris entre 1 et 31 représentant le jour du mois"}, "DATEDIF": {"a": "(date_début; date_fin; unité)", "d": "Renvoie la différence entre deux dates (date_début et date_fin) sur la base d'un intervalle (unité) spécifié", "ad": "Date qui représente la première ou la date de début d’une période donnée!Date qui représente la dernière date ou la date de fin de la période!Type d’informations à retourner"}, "DATEVALUE": {"a": "(date_texte)", "d": "Convertit une date donnée sous forme de texte en un numéro représentant la date dans le code de date et d’heure", "ad": "est du texte représentant une date dans un format de date de Spreadsheet Editor, entre le 01/01/1900 ou le 01/01/1904 (en fonction du calendrier du classeur), et le 31/12/9999"}, "DAY": {"a": "(numéro_de_série)", "d": "<PERSON><PERSON> le jour du mois (un nombre entre 1 et 31).", "ad": "est un nombre dans le code date-heure utilisé par Spreadsheet Editor"}, "DAYS": {"a": "(date_fin; date_début)", "d": "Calcule le nombre de jours entre les deux dates", "ad": "date_début et date_fin sont les deux dates délimitant la période dont vous recherchez l'étendue en jours!date_début et date_fin sont les deux dates délimitant la période dont vous recherchez l'étendue en jours"}, "DAYS360": {"a": "(date_début; date_fin; [méthode])", "d": "Calcule le nombre de jours entre deux dates sur la base d'une année de 360 jours (12 mois de 30 jours)", "ad": "date_début et date_fin sont les deux dates délimitant la période dont vous recherchez l'étendue en jours!date_début et date_fin sont les deux dates délimitant la période dont vous recherchez l'étendue en jours!est une valeur logique qui détermine la méthode de calcul: États-Unis (NASD) = FALSE ou omis ; Européen = TRUE."}, "EDATE": {"a": "(date_départ; mois)", "d": "Renvoie le numéro de série de la date située un nombre spécifié de mois dans le passé ou le futur par rapport à une date indiquée", "ad": "est la date de départ, exprimée sous forme de numéro de série!est le nombre de mois avant ou après la date de départ"}, "EOMONTH": {"a": "(date_départ; mois)", "d": "<PERSON><PERSON> le numéro de série du dernier jour du mois situé dans un intervalle exprimé en nombre de mois dans le futur ou dans le passé", "ad": "est la date de départ, exprimée sous forme de numéro de série!est le nombre de mois avant ou après la date de départ"}, "HOUR": {"a": "(numéro_de_série)", "d": "Renvoie l’heure, un nombre entier entre 0 (12:00 A.M.) et 23 (11:00 P.M.).", "ad": "est un nombre dans le code date-heure utilisé par Spreadsheet Editor, ou du texte au format horaire, par exemple 16:48:00 ou 4:48:00 P.M"}, "ISOWEEKNUM": {"a": "(date)", "d": "Renvoie le numéro ISO de la semaine de l’année correspondant à une date donnée", "ad": "est le code de date et d’heure utilisé par Spreadsheet Editor pour le calcul de la date et de l’heure"}, "MINUTE": {"a": "(numéro_de_série)", "d": "Renvoie les minutes, un entier entre 0 et 59.", "ad": "est un nombre dans le code date-heure utilisé par Spreadsheet Editor, ou du texte au format horaire, par exemple 16:48:00 ou 4:48:00 P.M"}, "MONTH": {"a": "(numéro_de_série)", "d": "<PERSON><PERSON> le mois, un nombre de 1 (janvier) à 12 (décembre)", "ad": "est un nombre dans le code date-heure utilisé par Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(date_départ; date_fin; [jours_féri<PERSON>])", "d": "Renvoie le nombre de jours ouvrés compris entre deux dates", "ad": "est la date de départ, exprimée sous forme de numéro de série!est la date de fin, exprimée sous forme de numéro de série!est une matrice contenant un ou des numéro de série représentant des dates à compter comme jours non ouvrés"}, "NETWORKDAYS.INTL": {"a": "(date_départ; date_fin; [week-end]; [jours_fériés])", "d": "Renvoie le nombre de jours ouvrés compris entre deux dates avec des paramètres de week-end personnalisés", "ad": "est la date de départ, exprimée sous forme de numéro de série!est la date de fin, exprimée sous forme de numéro de série!est un numéro ou une chaîne indiquant quand survient le week-end!est une matrice facultative contenant un ou plusieurs numéros de série de date à exclure du calendrier des jours ouvrés (jours de fêtes nationales, etc.)"}, "NOW": {"a": "()", "d": "Renvoie la date du jour et de l'heure du jour, sous la forme d'une date et d'une heure.", "ad": ""}, "SECOND": {"a": "(numéro_de_série)", "d": "<PERSON><PERSON> les secondes, un entier entre 0 et 59. ", "ad": "est un nombre dans le code date-heure utilisé par Spreadsheet Editor, ou du texte au format horaire, par exemple 16:48:23 ou 4:48:47 P.M"}, "TIME": {"a": "(heure; minute; seconde)", "d": "Convertit les heures, minutes et secondes données sous forme de nombres en un numéro de série, selon un format d'heure", "ad": "est un nombre compris entre 0 et 23 qui représente l'heure!est un nombre compris entre 0 et 59 qui représente les minutes!est un nombre compris entre 0 et 59 qui représente les secondes"}, "TIMEVALUE": {"a": "(heure_texte)", "d": "Convertit une heure donnée sous forme de texte en un numéro de série (temps_texte)", "ad": "est une chaîne de texte entre guillemets qui indique une heure dans un des formats d’heure de Spreadsheet Editor (l’information sur la date dans la chaîne de caractères est ignorée)"}, "TODAY": {"a": "()", "d": "Renvoie la date du jour au format de date.", "ad": ""}, "WEEKDAY": {"a": "(numéro_de_série; [type_retour])", "d": "Renvoie un chiffre entre 1 et 7 désignant le jour de la semaine.", "ad": "est un nombre représentant une date!est un nombre : si dimanche=1 et samedi=7, utilisez 1 ; si lundi=1 et dimanche=7, utilisez 2 ; si lundi=0 et dimanche=6, utilisez 3"}, "WEEKNUM": {"a": "(numéro_de_série; [type_renvoi])", "d": "Renvoie le numéro de la semaine de l’année", "ad": "est le code de date et d’heure utilisé par Spreadsheet Editor pour le calcul de la date et de l’heure!est un nombre (1 ou 2) qui détermine le type de la valeur renvoyée"}, "WORKDAY": {"a": "(date_départ; nb_jours; [jours_fériés])", "d": "Renvoie le numéro de série d'une date située un nombre de jours ouvrés avant ou après une date donnée", "ad": "est la date de départ, exprimée sous forme de numéro de série!représente le nombre de jours hors week-ends et hors jours fériés avant ou après la date de départ!est une matrice optionnelle contenant un ou plusieurs numéros de série de date à exclure du calendrier des jours ouvrés (jours de fêtes nationales, etc.)"}, "WORKDAY.INTL": {"a": "(date_départ; nb_jours; [nb_jours_week-end]; [jours_fériés])", "d": "Renvoie le numéro de série d’une date située un nombre de jours ouvrés avant ou après une date donnée avec des paramètres de week-end personnalisés", "ad": "est la date de départ, exprimée sous forme de numéro de série!représente le nombre de jours hors week-ends et hors jours fériés avant ou après la date de départ!représente un nombre ou une chaîne qui spécifie quand les weekends ont lieu!est une matrice facultative contenant un ou plusieurs numéros de série de date à exclure du calendrier des jours ouvrés (jours de fêtes nationales, etc.)"}, "YEAR": {"a": "(numéro_de_série)", "d": "Renvoie l’année, un entier entre 1900 et 9999.", "ad": "est un nombre dans le code date-heure utilisé par Spreadsheet Editor"}, "YEARFRAC": {"a": "(date_début; date_fin; [base])", "d": "Renvoie une fraction correspondant au nombre de jours séparant date_début de date_fin par rapport à une année complète", "ad": "est la date de départ, exprimée sous forme de numéro de série!est la date de fin, exprimée sous forme de numéro de série!est la base annuelle utilisée pour le calcul"}, "BESSELI": {"a": "(x; n)", "d": "Renvoie la fonction de Bessel modifiée In(x)", "ad": "est la valeur à laquelle la fonction est évaluée!est l'ordre de la fonction de Bessel"}, "BESSELJ": {"a": "(x; n)", "d": "Renvoie la fonction de Bessel Jn(x)", "ad": "est la valeur à laquelle la fonction est évaluée!est l'ordre de la fonction de Bessel"}, "BESSELK": {"a": "(x; n)", "d": "Renvoie la fonction de Bessel modifiée Kn(x)", "ad": "est la valeur à laquelle la fonction est évaluée!est l'ordre de la fonction"}, "BESSELY": {"a": "(x; n)", "d": "Renvoie la fonction de Bessel modifiée Yn(x)", "ad": "est la valeur à laquelle la fonction est évaluée!est l'ordre de la fonction"}, "BIN2DEC": {"a": "(nombre)", "d": "Convertit un nombre binaire en nombre décimal", "ad": "est le nombre binaire à convertir"}, "BIN2HEX": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre binaire en nombre hexadécimal", "ad": "est le nombre binaire à convertir!représente le nombre de caractères à utiliser"}, "BIN2OCT": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre binaire en nombre octal", "ad": "est le nombre binaire à convertir!représente le nombre de caractères à utiliser"}, "BITAND": {"a": "(nombre1; nombre2)", "d": "Renvoie le résultat binaire « Et » de deux nombres", "ad": "est la représentation décimale du nombre binaire à calculer!est la représentation décimale du nombre binaire à calculer"}, "BITLSHIFT": {"a": "(nombre; total_décalé)", "d": "Renvoie un nombre décalé vers la gauche de total_décalage bits", "ad": "est la représentation décimale du nombre binaire à calculer!est le nombre de bits par lequel vous voulez décaler le nombre vers la gauche"}, "BITOR": {"a": "(nombre1; nombre2)", "d": "Renvoie le résultat binaire « Ou » de deux nombres", "ad": "est la représentation décimale du nombre binaire à calculer!est la représentation décimale du nombre binaire à calculer"}, "BITRSHIFT": {"a": "(nombre; total_décalé)", "d": "Renvoie un nombre décalé vers la droite de total_décalage bits", "ad": "est la représentation décimale du nombre binaire à calculer!est le nombre de bits par lequel vous voulez décaler le nombre vers la droite"}, "BITXOR": {"a": "(nombre1; nombre2)", "d": "Renvoie Renvoie le résultat binaire « Ou exclusif » de deux nombres", "ad": "est la représentation décimale du nombre binaire à calculer!est la représentation décimale du nombre binaire à calculer"}, "COMPLEX": {"a": "(partie_réelle; partie_imaginaire; [suffixe])", "d": "Renvoie un nombre complexe construit à partir de ses parties réelle et imaginaire", "ad": "est la partie réelle du nombre complexe!est la partie imaginaire du nombre complexe!est le symbole utilisé pour désigner le vecteur imaginaire"}, "CONVERT": {"a": "(nombre; de_unité; à_unité)", "d": "Convertit un nombre d'une unité à une autre unité", "ad": "est le nombre à convertir!est l'unité du nombre à convertir!est l'unité du résultat"}, "DEC2BIN": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre décimal en nombre binaire", "ad": "est le nombre à convertir!représente le nombre de caractères à utiliser"}, "DEC2HEX": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre décimal en nombre hexadécimal", "ad": "est le nombre à convertir!représente le nombre de caractères à utiliser"}, "DEC2OCT": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre décimal en nombre octal", "ad": "est le nombre à convertir!représente le nombre de caractères à utiliser"}, "DELTA": {"a": "(nombre1; [nombre2])", "d": "Teste l'égalité de deux nombres", "ad": "est le premier nombre!est le second nombre"}, "ERF": {"a": "(limite_inf; [limite_sup])", "d": "Renvoie la fonction d'erreur", "ad": "est la borne d'intégration inférieure!est la borne d'intégration supérieure"}, "ERF.PRECISE": {"a": "(X)", "d": "Renvoie la fonction d’erreur", "ad": "est la borne d’intégration de ERF.PRECIS inférieure"}, "ERFC": {"a": "(x)", "d": "Renvoie la fonction d'erreur complémentaire", "ad": "est la borne d'intégration inférieure"}, "ERFC.PRECISE": {"a": "(X)", "d": "Renvoie la fonction d’erreur complémentaire", "ad": "est la borne d’intégration de ERFC.PRECIS inférieure"}, "GESTEP": {"a": "(nombre; [seuil])", "d": "Vérifie si un nombre dépasse une valeur seuil", "ad": "représente la valeur à comparer au seuil!représente la valeur seuil"}, "HEX2BIN": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre hexadécimal en nombre binaire", "ad": "est le nombre à convertir!représente le nombre de caractères à utiliser"}, "HEX2DEC": {"a": "(nombre)", "d": "Convertit un nombre hexadécimal en nombre décimal", "ad": "est le nombre à convertir"}, "HEX2OCT": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre hexadécimal en nombre octal", "ad": "est le nombre hexadécimal à convertir!représente le nombre de caractères à utiliser"}, "IMABS": {"a": "(nombre_complexe)", "d": "Renvoie le module d'un nombre complexe", "ad": "est le nombre complexe dont vous voulez calculer le module"}, "IMAGINARY": {"a": "(nombre_complexe)", "d": "Renvoie la partie imaginaire d'un nombre complexe", "ad": "est le nombre complexe dont vous désirez extraire la partie imaginaire"}, "IMARGUMENT": {"a": "(nombre_complexe)", "d": "Renvoie l'argument thêta, un angle exprimé en radians", "ad": "est le nombre complexe dont vous voulez calculer l'argument"}, "IMCONJUGATE": {"a": "(nombre_complexe)", "d": "Renvoie le conjugué d'un nombre complexe", "ad": "est le nombre complexe dont vous voulez calculer le conjugué"}, "IMCOS": {"a": "(nombre_complexe)", "d": "Ren<PERSON>ie le cosinus d'un nombre complexe", "ad": "est le nombre complexe dont vous voulez calculer le cosinus"}, "IMCOSH": {"a": "(nombre_complexe)", "d": "Renvoie le cosinus hyperbolique d'un nombre complexe", "ad": "est le nombre complexe dont vous voulez obtenir le cosinus hyperbolique"}, "IMCOT": {"a": "(nombre_complexe)", "d": "Renvoie la cotangente d'un nombre complexe", "ad": "est le nombre complexe dont vous voulez obtenir la cotangente"}, "IMCSC": {"a": "(nombre_complexe)", "d": "Renvoie la cosécante d'un nombre complexe", "ad": "est le nombre complexe dont vous voulez obtenir la cosécante"}, "IMCSCH": {"a": "(nombre_complexe)", "d": "Renvoie la cosécante hyperbolique d'un nombre complexe", "ad": "est le nombre complexe dont vous voulez obtenir la cosécante hyperbolique"}, "IMDIV": {"a": "(nombre_complexe1; nombre_complexe2)", "d": "Renvoie le quotient de deux nombres complexes", "ad": "est le numérateur complexe!est le diviseur complexe"}, "IMEXP": {"a": "(nombre_complexe)", "d": "Donne l'exposant d'un nombre complexe", "ad": " est un nombre complexe dont vous voulez calculer l'exposant"}, "IMLN": {"a": "(nombre_complexe)", "d": "Donne le logarithme népérien d'un nombre complexe", "ad": "est le nombre complexe dont vous voulez obtenir le logarithme népérien"}, "IMLOG10": {"a": "(nombre_complexe)", "d": "Calcule le logarithme en base 10 d'un nombre complexe", "ad": "est le nombre complexe dont vous souhaitez obtenir le logarithme en base 10"}, "IMLOG2": {"a": "(nombre_complexe)", "d": "Calcule le logarithme en base 2 d'un nombre complexe", "ad": "est le nombre complexe dont vous voulez calculer le logarithme en base 2"}, "IMPOWER": {"a": "(nombre_complexe; nombre)", "d": "Renvoie la valeur du nombre complexe élevé à une puissance entière", "ad": "est le nombre complexe à élever à la puissance donnée!est la puissance à laquelle le nombre complexe doit être élevé"}, "IMPRODUCT": {"a": "(nb_comp1; [nb_comp2]; ...)", "d": "Renvoie le produit de 1 à 255 nombres complexes", "ad": "nb_comp1, nb_comp2,... représentent de 1 à 255 nombres complexes à multiplier."}, "IMREAL": {"a": "(nombre_complexe)", "d": "Renvoie la partie réelle d'un nombre complexe", "ad": "est le nombre complexe dont vous voulez obtenir la partie réelle"}, "IMSEC": {"a": "(nombre_complexe)", "d": "Renvoie la sécante d'un nombre complexe", "ad": "est le nombre complexe dont vous voulez obtenir la sécante"}, "IMSECH": {"a": "(nombre_complexe)", "d": "Renvoie la sécante hyperbolique d'un nombre complexe", "ad": "est le nombre complexe dont vous voulez obtenir la sécante hyperbolique"}, "IMSIN": {"a": "(nombre_complexe)", "d": "Renvoie le sinus d'un nombre complexe", "ad": "est le nombre complexe dont vous voulez calculer le sinus"}, "IMSINH": {"a": "(nombre_complexe)", "d": "Renvoie le sinus hyperbolique d'un nombre complexe", "ad": "est le nombre complexe dont vous voulez obtenir le sinus hyperbolique"}, "IMSQRT": {"a": "(nombre_complexe)", "d": "Extrait la racine carrée d'un nombre complexe", "ad": "est le nombre complexe dont vous voulez extraire la racine carrée"}, "IMSUB": {"a": "(nombre_complexe1; nombre_complexe2)", "d": "Calcule la différence entre deux nombres complexes", "ad": "est le nombre complexe duquel on veut soustraire le nombre_complexe2!est le nombre complexe à soustraire au nombre_complexe1"}, "IMSUM": {"a": "(nb_comp1; [nb_comp2]; ...)", "d": "Renvoie la somme de nombres complexes", "ad": "représentent de 1 à 255 nombres complexes à ajouter"}, "IMTAN": {"a": "(nombre_complexe)", "d": "Renvoie la tangente d'un nombre complexe", "ad": "est le nombre complexe dont vous voulez obtenir la tangente"}, "OCT2BIN": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre octal en nombre binaire", "ad": "est le nombre octal à convertir!représente le nombre de caractères à utiliser"}, "OCT2DEC": {"a": "(nombre)", "d": "Convertit un nombre octal en nombre décimal", "ad": "est le nombre octal à convertir"}, "OCT2HEX": {"a": "(nombre; [nb_car])", "d": "Convertit un nombre octal en nombre hexadécimal", "ad": "représente le nombre octal à convertir!représente le nombre de caractères à utiliser"}, "DAVERAGE": {"a": "(base_de_données; champ; critères)", "d": "Don<PERSON> la moyenne des valeurs dans la colonne d'une liste ou une base de données qui correspondent aux conditions que vous avez spécifiées", "ad": "est la plage de cellules qui constitue la liste ou la base de données. Une base de données est une liste de données connexes!indique soit l'étiquette de la colonne entre guillemets, soit un nombre représentant la position de la colonne dans la liste!est la plage de cellules qui contient les conditions. Cette plage inclut une étiquette de colonne et une cellule en dessous de l'étiquette de la condition"}, "DCOUNT": {"a": "(base_de_données; champ; critères)", "d": "Compte le nombre de cellules contenant des valeurs numériques satisfaisant les critères spécifiés pour la base de données précisée", "ad": "est la plage de cellules qui constitue la liste ou la base de données. Une base de données est une liste de données connexes!indique soit l'étiquette de la colonne entre guillemets, soit un nombre représentant la position de la colonne dans la liste!est la plage de cellules qui contient les conditions. Cette plage inclut une étiquette de colonne et une cellule en dessous de l'étiquette de la condition"}, "DCOUNTA": {"a": "(base_de_données; champ; critères)", "d": "Compte le nombre de cellules non vides dans le champ (colonne) d'enregistrements dans la base de données correspondant aux conditions spécifiées", "ad": "est la plage de cellules qui constitue la liste ou la base de données. Une base de données est une liste de données connexes!indique soit l'étiquette de la colonne entre guillemets, soit un nombre représentant la position de la colonne dans la liste!est la plage de cellules qui contient les conditions. Cette plage inclut une étiquette de colonne et une cellule en dessous de l'étiquette de la condition"}, "DGET": {"a": "(base_de_données; champ; critères)", "d": "Extrait d'une base de données l'enregistrement qui correspond aux conditions spécifiées", "ad": "est la plage de cellules qui constitue la liste ou la base de données. Une base de données est une liste de données connexes!indique soit l'étiquette de la colonne entre guillemets, soit un nombre représentant la position de la colonne dans la liste!est la plage de cellules qui contient les conditions. Cette plage inclut une étiquette de colonne et une cellule en dessous de l'étiquette de la condition"}, "DMAX": {"a": "(base_de_données; champ; critères)", "d": "Donne la valeur la plus élevée dans le champ (colonne) des enregistrements de la base de données correspondant aux conditions que vous avez spécifiées.", "ad": "est la plage de cellules qui constitue la liste ou la base de données. Une base de données est une liste de données connexes!indique soit l'étiquette de la colonne entre guillemets, soit un nombre représentant la position de la colonne dans la liste!est la plage de cellules qui contient les conditions. Cette plage inclut une étiquette de colonne et une cellule en dessous de l'étiquette de la condition"}, "DMIN": {"a": "(base_de_données; champ; critères)", "d": "Donne la valeur la moins élevée du champ (colonne) d'enregistrements de la base de données correspondant aux conditions définies", "ad": "est la plage de cellules qui constitue la base de données ou la liste. Une base de données est une liste de données connexes!indique soit l'étiquette de la colonne entre guillemets, soit un nombre représentant la position de la colonne dans la liste!est la plage de cellules qui contient les conditions que vous avez spécifiées. Cette plage inclut une étiquette de colonne et une cellule en dessous de l'étiquette de la condition"}, "DPRODUCT": {"a": "(base_de_données; champ; critères)", "d": "Multiplie les valeurs dans le champ (colonne) d'enregistrements de la base de données correspondant aux conditions que vous avez spécifiées", "ad": "est la plage de cellules qui constitue la liste ou la base de données. Une base de données est une liste de données connexes!indique soit l'étiquette de la colonne entre guillemets, soit un nombre représentant la position de la colonne dans la liste!est la plage de cellules qui contient les conditions. Cette plage inclut une étiquette de colonne et une cellule en dessous de l'étiquette de la condition"}, "DSTDEV": {"a": "(base_de_données; champ; critères)", "d": "Évalue l'écart-type à partir d'un échantillon de population représenté par les entrées de base de données sélectionnées", "ad": "est la plage de cellules qui constitue la liste ou la base de données. Une base de données est une liste de données connexes!indique soit l'étiquette de la colonne entre guillemets, soit un nombre représentant la position de la colonne dans la liste!est la plage de cellules qui contient les conditions. Cette plage inclut une étiquette de colonne et une cellule en dessous de l'étiquette de la condition"}, "DSTDEVP": {"a": "(base_de_données; champ; critères)", "d": "Calcule l'écart-type à partir de la population entière représentée par les entrées de base de données sélectionnées", "ad": "est la plage de cellules qui constitue la liste ou la base de données. Une base de données est une liste de données connexes!indique soit l'étiquette de la colonne entre guillemets, soit un nombre représentant la position de la colonne dans la liste!est la plage de cellules qui contient les conditions que vous avez spécifiées. Cette plage inclut une étiquette de colonne et l'étiquette de la condition"}, "DSUM": {"a": "(base_de_données; champ; critères)", "d": "Additionne les nombres se trouvant dans le champ (colonne) d'enregistrements de la base de données correspondant aux conditions que vous avez spécifiées", "ad": "est la plage de cellules qui constitue la liste ou la base de données. Une base de données est une liste de données connexes!indique soit l'étiquette de la colonne entre guillemets, soit un nombre représentant la position de la colonne dans la liste!est la plage de cellules qui contient les conditions. Cette plage inclut une étiquette de colonne et une cellule en dessous de l'étiquette de la condition"}, "DVAR": {"a": "(base_de_données; champ; critères)", "d": "Évalue la variance à partir d'un échantillon de population représenté par des entrées de base de données sélectionnées", "ad": "est la plage de cellules qui constitue la liste ou la base de données. Une base de données est une liste de données connexes!indique soit l'étiquette de la colonne entre guillemets, soit un nombre représentant la position de la colonne dans la liste!est la plage de cellules qui contient les conditions. Cette plage inclut une étiquette de colonne et une cellule en dessous de l'étiquette de la condition"}, "DVARP": {"a": "(base_de_données; champ; critères)", "d": "Calcule la variance à partir de la population entière représentée par les entrées de base de données sélectionnées", "ad": "est la plage de cellules qui constitue la liste ou la base de données. Une base de données est une liste de données connexes!indique soit l'étiquette de la colonne entre guillemets, soit un nombre représentant la position de la colonne dans la liste!est la plage de cellules qui contient les conditions. Cette plage inclut une étiquette de colonne et une cellule en dessous de l'étiquette de la condition"}, "CHAR": {"a": "(nombre)", "d": "Renvoie le caractère spécifié par le code numérique du jeu de caractères de votre ordinateur", "ad": "est un nombre compris entre 1 et 255, indiquant le caractère que vous voulez obtenir"}, "CLEAN": {"a": "(texte)", "d": "Supprime tous les caractères de contrôle du texte", "ad": "représente toute information de feuille de calcul dont vous voulez supprimer les caractères qui ne doivent pas apparaître à l'impression"}, "CODE": {"a": "(texte)", "d": "Renvoie le numéro de code du premier caractère du texte, dans le jeu de caractères utilisé par votre ordinateur", "ad": "est le texte dont vous voulez obtenir le code du premier caractère"}, "CONCATENATE": {"a": "(texte1; [texte2]; ...)", "d": "Assemble plusieurs chaînes de caractères de façon à n'en former qu'une", "ad": "sont de 1 à 255 chaînes de caractères à concaténer. Il peut s'agir de chaînes, nombres ou références à des cellules uniques"}, "CONCAT": {"a": "(texte1; ...)", "d": "Assemble une liste ou une plage de chaînes de caractères", "ad": "représentent 1 à 254 chaînes ou plages de caractères à assembler sous forme de chaîne de caractères unique"}, "DOLLAR": {"a": "(nombre; [décimales])", "d": "Convertit un nombre en texte en utilisant le format monétaire", "ad": "est un nombre, une formule faisant référence à une cellule contenant un nombre ou une formule renvoyant un nombre!indique le nombre de chiffres à droite de la virgule. Le nombre est arrondi si nécessaire; sinon, les décimales = 2"}, "EXACT": {"a": "(texte1; texte2)", "d": "Compare deux chaînes textuelles et renvoie VRAI si elles sont parfaitement identiques et FAUX si elles sont différentes (distinction majuscules/minuscules)", "ad": "est la première chaîne de caractères!est la deuxième chaîne de caractères"}, "FIND": {"a": "(texte_cherché; texte; [no_départ])", "d": "Renvoie la position de départ d'une chaîne de texte à l'intérieur d'une autre chaîne de texte. TROUVE distingue les majuscules des minuscules", "ad": "est le texte que vous recherchez. Utilisez des guillemets (sans texte) pour trouver le premier caractère dans Texte; les caractères génériques ne sont pas autorisés!est le texte contenant le texte recherché!indique le caractère à partir duquel commencer la recherche. Le premier caractère dans l'argument texte est le chiffre 1. S'il est omis, No_départ = 1"}, "FINDB": {"a": "(texte_cherché; texte; [no_départ])", "d": "Recherche la sous-chaîne spécifiée (texte_cherché) dans une chaîne (texte) et destinée aux langues qui utilisent le jeu de caractères à deux octets (DBCS) comme le Japonais, le Chinois, le Coréen etc.", "ad": "est le texte que vous recherchez. Utilisez des guillemets (sans texte) pour trouver le premier caractère dans Texte; les caractères génériques ne sont pas autorisés!est le texte contenant le texte recherché!indique le caractère à partir duquel commencer la recherche. Le premier caractère dans l'argument texte est le chiffre 1. S'il est omis, No_départ = 1"}, "FIXED": {"a": "(nombre; [décimales]; [no_séparateur])", "d": "Arrondit un nombre au nombre de décimales spécifié et renvoie le résultat sous forme de texte avec ou sans virgule", "ad": "est le nombre à arrondir et convertir en texte!est le nombre de chiffres à droite de la virgule. Si omis, 2 décimales!est une valeur logique: ne pas afficher de virgule dans le texte renvoyé = VRAI; afficher des virgules dans le texte renvoyé = FAUX ou omis"}, "LEFT": {"a": "(texte; [no_car])", "d": "Extrait le(s) premier(s) caractère(s) à l'extrême gauche d'une chaîne de texte", "ad": "est la chaîne de texte contenant les caractères à extraire!indique le nombre de caractères que la fonction GAUCHE doit renvoyer; 1 si omis"}, "LEFTB": {"a": "(texte; [no_car])", "d": "Extrait la sous-chaîne d’une chaîne spécifiée à partir du caractère de gauche et destinée aux langues qui utilisent le jeu de caractères à deux octets (DBCS) comme le Japonais, le Chinois, le Coréen etc.", "ad": "est la chaîne de texte contenant les caractères à extraire!indique le nombre de caractères que la fonction GAUCHEB doit renvoyer; 1 si omis"}, "LEN": {"a": "(texte)", "d": "Renvoie le nombre de caractères contenus dans une chaîne de texte", "ad": "est le texte dont vous voulez connaître la longueur. Les espaces sont considérés comme des caractères"}, "LENB": {"a": "(texte)", "d": "Analyse la chaîne spécifiée et renvoyer le nombre de caractères qu’elle contient et destinée aux langues qui utilisent le jeu de caractères à deux octets (DBCS) comme le Japonais, le Chinois, le Coréen etc.", "ad": "est le texte dont vous voulez connaître la longueur. Les espaces sont considérés comme des caractères"}, "LOWER": {"a": "(texte)", "d": "Convertit toutes les lettres majuscules en une chaîne de caractères en minuscules", "ad": "est le texte que vous voulez convertir en caractères minuscules. Les caractères du texte qui ne sont pas des lettres ne sont pas modifiés"}, "MID": {"a": "(texte; no_départ; no_car)", "d": "Renvoie un nombre déterminé de caractères d'une chaîne de texte à partir de la position que vous indiquez", "ad": "est la chaîne de texte qui contient les caractères que vous voulez extraire!est la position du premier caractère à extraire. Le premier caractère du texte porte le numéro 1!indique le nombre de caractères à extraire du texte"}, "MIDB": {"a": "(texte; no_départ; no_car)", "d": "Extrait les caractères d’une chaîne spécifiée à partir de n’importe quelle position et destinée aux langues qui utilisent le jeu de caractères à deux octets (DBCS) comme le Japonais, le Chinois, le Coréen etc.", "ad": "est la chaîne de texte qui contient les caractères que vous voulez extraire!est la position du premier caractère à extraire. Le premier caractère du texte porte le numéro 1!indique le nombre de caractères à extraire du texte"}, "NUMBERVALUE": {"a": "(texte; [séparateur_décimal]; [séparateur_groupe])", "d": "Convertit le texte en nombre quels que soient les paramètres régionaux", "ad": "est la chaîne représentant le nombre à convertir!est le caractère servant de séparateur décimal dans la chaîne!est le caractère servant de séparateur de groupes dans la chaîne"}, "PROPER": {"a": "(texte)", "d": "Met en majuscule la première lettre de chaque mot dans une chaîne de texte et met toutes les autres lettres en minuscules", "ad": "est un texte entre guillemets, une formule qui renvoie du texte ou une référence à une cellule contenant un texte dont vous voulez que certaines lettres soient en capitales"}, "REPLACE": {"a": "(ancien_texte; no_départ; no_car; nouveau_texte)", "d": "Remplace une chaîne de caractères par une autre", "ad": "est le texte dans lequel vous voulez remplacer des caractères!est la position du caractère dans ancien_texte que vous voulez remplacer par nouveau_texte!est le nombre de caractères que vous voulez remplacer dans l'ancien texte!est le texte qui va remplacer des caractères de ancien_texte"}, "REPLACEB": {"a": "(ancien_texte; no_départ; no_car; nouveau_texte)", "d": "Remplace un jeu de caractères, en fonction du nombre de caractères et de la position de départ que vous spécifiez, avec un nouvel ensemble de caractères et destinée aux langues qui utilisent le jeu de caractères à deux octets (DBCS) comme le Japonais, le Chinois, le Coréen etc.", "ad": "est le texte dans lequel vous voulez remplacer des caractères!est la position du caractère dans ancien_texte que vous voulez remplacer par nouveau_texte!est le nombre de caractères que vous voulez remplacer dans l'ancien texte!est le texte qui va remplacer des caractères de ancien_texte"}, "REPT": {"a": "(texte; no_fois)", "d": "Répète un texte un certain nombre de fois. Utilisez REPT pour remplir une cellule avec un certain nombre d'occurrences d'une chaîne de caractères", "ad": "est le texte que vous voulez répé<PERSON>!est un nombre positif indiquant le nombre de fois que texte doit être répété"}, "RIGHT": {"a": "(texte; [no_car])", "d": "Extrait les derniers caractères de la fin d'une chaîne de texte", "ad": "est la chaîne de texte contenant les caractères à extraire!indique le nombre de caractères à extraire, 1 par défaut"}, "RIGHTB": {"a": "(texte; [no_car])", "d": "Extrait une sous-chaîne d'une chaîne à partir du caractère le plus à droite, en fonction du nombre de caractères spécifié et destinée aux langues qui utilisent le jeu de caractères à deux octets (DBCS) comme le Japonais, le Chinois, le Coréen etc.", "ad": "est la chaîne de texte contenant les caractères à extraire!indique le nombre de caractères à extraire, 1 par défaut"}, "SEARCH": {"a": "(texte_cherché; texte; [no_départ])", "d": "Renvoie le numéro du caractère au niveau duquel est trouvé un caractère ou le début d'une chaîne de caractères, en lisant de la gauche à droite (pas de distinction entre majuscules et minuscules)", "ad": "est le texte à trouver. Vous pouvez utiliser les caractères génériques ? et *; utilisez ~? et ~* pour trouver les caractères ? et *.!est le texte comprenant la chaîne de texte à trouver!indique le numéro du caractère dans l'argument texte à partir duquel la recherche doit débuter (en comptant à partir de la gauche). 1 est utilisé par défaut"}, "SEARCHB": {"a": "(texte_cherché; texte; [no_départ])", "d": "Renvoie l'emplacement de la sous-chaîne spécifiée dans une chaîne et destinée aux langues qui utilisent le jeu de caractères à deux octets (DBCS) comme le Japonais, le Chinois, le Coréen etc.", "ad": "est le texte à trouver. Vous pouvez utiliser les caractères génériques ? et *; utilisez ~? et ~* pour trouver les caractères ? et *.!est le texte comprenant la chaîne de texte à trouver!indique le numéro du caractère dans l'argument texte à partir duquel la recherche doit débuter (en comptant à partir de la gauche). 1 est utilisé par défaut"}, "SUBSTITUTE": {"a": "(texte; ancien_texte; nouveau_texte; [no_position])", "d": "Remplace des caractères dans un texte", "ad": "est la chaîne de texte entre guillemets ou la référence de la cellule contenant la chaîne de texte dont vous voulez remplacer des caractères!est le texte qui doit être remplacé. Si la casse de l'ancien texte ne correspond pas à la casse du nouveau, le remplacement sera annulé!est le texte qui doit remplacer l'ancien texte!indique l'occurrence de l'ancien texte que vous voulez remplacer. Par défaut, toutes les occurrence de l'ancien texte seront remplacées"}, "T": {"a": "(valeur)", "d": "Contrôle si une valeur fait référence à du texte et renvoie le texte le cas échéant, ou renvoie des guillemets (texte vide) dans le cas contraire", "ad": "est la valeur à tester"}, "TEXT": {"a": "(valeur; format_text)", "d": "Convertit une valeur en texte en un format de nombre spécifique", "ad": "est un nombre, une formule dont le résultat est une valeur numérique ou une référence à une cellule contenant une valeur numérique!est un format de nombre sous forme de texte issu de la zone catégorie de l’onglet nombre de la boîte de dialogue Format de cellule"}, "TEXTJOIN": {"a": "(dé<PERSON>iteur; ignorer_vide; texte1; ...)", "d": "Assemble une liste ou plage de chaînes de caractères à l’aide d’un délimiteur", "ad": "Caractère ou chaîne à insérer entre chaque élément de texte!si VRAI (valeur par défaut), ignore les cellules vides!représentent 1 à 252 chaînes ou plages de caractères à assembler "}, "TRIM": {"a": "(texte)", "d": "Supprime tous les espaces d'une chaîne de caractères, sauf les espaces simples entre les mots", "ad": "est le texte dont vous voulez supprimer les espaces inutiles"}, "UNICHAR": {"a": "(nombre)", "d": "Renvoie le caractère Unicode référencé par la valeur numérique donnée", "ad": "est le nombre Unicode représentant un caractère"}, "UNICODE": {"a": "(texte)", "d": "Renvoie le nombre (point de code) correspondant au premier caractère du texte", "ad": "représente le caractère dont vous voulez obtenir la valeur Unicode"}, "UPPER": {"a": "(texte)", "d": "Convertit une chaîne de caractères en majuscules", "ad": "est le texte que vous voulez convertir en caractères capitaux, une référence ou une chaîne de caractères"}, "VALUE": {"a": "(texte)", "d": "Convertit une chaîne textuelle représentant un nombre en un nombre", "ad": "est le texte placé entre guillemets ou la référence à une cellule contenant le texte que vous voulez convertir"}, "AVEDEV": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la moyenne des écarts absolus des points de données par rapport à leur moyenne arithmétique. Les arguments peuvent être des nombres, des noms, des matrices ou des références qui contiennent des nombres", "ad": "représentent de 1 à 255 arguments pour lesquels vous voulez calculer la moyenne des écarts absolus"}, "AVERAGE": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la moyenne (espérance arithmétique) des arguments, qui peuvent être des nombres, des noms, des matrices, ou des références contenant des nombres", "ad": "représentent de 1 à 255 arguments numériques dont vous souhaitez obtenir la moyenne"}, "AVERAGEA": {"a": "(valeur1; [valeur2]; ...)", "d": "Renvoie la moyenne (moyenne arithmétique) de ses arguments, en considérant que le texte et la valeur logique FAUX dans les arguments = 0 ; VRAI = 1. Les arguments peuvent être des nombres, des noms, des matrices ou des références", "ad": "représentent de 1 à 255 arguments dont vous voulez obtenir la moyenne"}, "AVERAGEIF": {"a": "(plage; critères; [plage_moyenne])", "d": "Détermine la moyenne (espérance arithmétique) des cellules satisfaisant une condition ou des critères particuliers", "ad": "est la plage de cellules que vous voulez évaluer !est la condition ou le critère sous la forme d'un nombre, d'une expression ou d'un texte qui définit les cellules à utiliser pour trouver la moyenne !sont les cellules réelles à utiliser pour trouver la moyenne. Si elles sont omises, les cellules de la plage sont utilisées "}, "AVERAGEIFS": {"a": "(plage_moyenne; plage_critères; critères; ...)", "d": "Détermine la moyenne (espérance arithmétique) des cellules spécifiées par un ensemble de conditions ou de critères", "ad": "représente les cellules qui seront effectivement utilisées pour effectuer le calcul de la moyenne.!représente la plage de cellules à évaluer d'après une condition particulière!représente la condition ou les critères sous la forme d'un nombre, d'une expression ou de texte qui seront utilisés pour déterminer la moyenne"}, "BETADIST": {"a": "(x; alpha; bêta; [A]; [B])", "d": "Renvoie la probabilité d’une variable aléatoire continue suivant une loi de probabilité Bêta", "ad": "représente la valeur à laquelle la fonction doit être évaluée sur l’intervalle [A, B] comprenant x!représente un paramètre de la distribution et doit être supérieur à 0!représente un paramètre de la distribution et doit être supérieur à 0!est une limite inférieure facultative de l’intervalle comprenant x. Si omis, A = 0!est une limite supérieure facultative de l’intervalle comprenant x. Si omis, B = 1"}, "BETAINV": {"a": "(probabilité; alpha; bêta; [A]; [B])", "d": "Renvoie l’inverse de la fonction de densité de distribution de la probabilité suivant une loi bêta cumulée (LOI.BÊTA)", "ad": "représente une probabilité associée à la distribution Bêta!représente un paramètre de la distribution et doit être supérieur à 0!représente un paramètre de la distribution et doit être supérieur à 0!est une limite inférieure facultative de l’intervalle comprenant x. Si omis, A = 0!est une limite supérieure facultative de l’intervalle comprenant x. Si omis, B = 1"}, "BETA.DIST": {"a": "(x; alpha; bêta; cumulative; [A]; [B])", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi de probabilité Bêta", "ad": "représente la valeur à laquelle la fonction doit être évaluée sur l’intervalle [A, B] comprenant x!représente un paramètre de la distribution et doit être supérieur à 0!représente une valeur logique : pour la fonction distribution cumulative, utilisez VRAI ; pour la fonction densité de la probabilité, utilisez FAUX!représente un paramètre de la distribution et doit être supérieur à 0!est une limite inférieure facultative de l’intervalle comprenant x. Si omis, A = 0!est une limite supérieure facultative de l’intervalle comprenant x. Si omis, B = 1"}, "BETA.INV": {"a": "(probabilité; alpha; bêta; [A]; [B])", "d": "Renvoie l’inverse de la fonction de densité de distribution de la probabilité suivant une loi bêta cumulée (LOI.BETA.N)", "ad": "représente une probabilité associée à la distribution Bêta!représente un paramètre de la distribution et doit être supérieur à 0!représente un paramètre de la distribution et doit être supérieur à 0!est une limite inférieure facultative de l’intervalle comprenant x. Si omis, A = 0!est une limite supérieure facultative de l’intervalle comprenant x. Si omis, B = 1"}, "BINOMDIST": {"a": "(nombre_succès; tirages; probabilité_succès; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire discrète suivant la loi binomiale", "ad": "représente le nombre de succès obtenus lors des tirages!représente le nombre de tirages indépendants!représente la probabilité d’obtenir un succès à chaque tirage!représente une valeur logique : pour la fonction distribution cumulative, utilisez VRAI ; pour la fonction de probabilité de masse, utilisez FAUX"}, "BINOM.DIST": {"a": "(nombre_succès; tirages; probabilité_succès; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire discrète suivant la loi binomiale", "ad": "représente le nombre de succès obtenus lors des tirages!représente le nombre de tirages indépendants!représente la probabilité d’obtenir un succès à chaque tirage!représente une valeur logique : pour la fonction distribution cumulative, utilisez VRAI ; pour la fonction de probabilité de masse, utilisez FAUX"}, "BINOM.DIST.RANGE": {"a": "(tirages; probabilité_succès; nombre_succès; [nombre_succès2])", "d": "Renvoie la probabilité d'un résultat de tirage en suivant une distribution binomiale", "ad": "représente le nombre de tirages indépendants!représente la probabilité d'obtenir un succès à chaque tirage!représente le nombres de succès obtenus lors des tirages!Si cette fonction est fournie, elle renvoie la probabilité que le nombre de tirages réussis se trouve entre nombre_succès et nombre_succès2"}, "BINOM.INV": {"a": "(tirages; probabilité_succès; alpha)", "d": "Renvoie la plus petite valeur pour laquelle la distribution binomiale cumulée est supérieure ou égale à une valeur critère", "ad": "représente le nombre de tirages de <PERSON>lli!représente la probabilité de succès à chaque tirage, un nombre entre 0 et 1 compris!représente la valeur critère, un nombre entre 0 et 1 compris"}, "CHIDIST": {"a": "(x; deg<PERSON>s_liberté)", "d": "Renvoie la probabilité unilatérale à droite d’une variable aléatoire continue suivant une loi du Khi-deux", "ad": "représente la valeur à laquelle vous voulez évaluer la distribution, un nombre positif!représente le nombre de degrés de liberté, un nombre entre 1 et 10^10, 10^10 exclus"}, "CHIINV": {"a": "(probabilité; degrés_liberté)", "d": "Ren<PERSON><PERSON>, pour une probabilité unilatérale à droite donnée, la valeur d’une variable aléatoire suivant une loi du <PERSON>hi-deux", "ad": "représente une probabilité associée à la distribution du Khi-deux, une valeur entre 0 et 1 compris!représente le nombre de degrés de liberté, un nombre entre 1 et 10^10, 10^10 exclus"}, "CHITEST": {"a": "(plage_réelle; plage_attendue)", "d": "Renvoie le test d’indépendance : la valeur pour la statistique suivant la loi du Khi-deux et les degrés de liberté appropriés", "ad": "représente la plage de données contenant les observations à comparer aux valeurs attendues!représente la plage de données contenant le rapport du produit des totaux de lignes et des totaux de colonnes sur le total général"}, "CHISQ.DIST": {"a": "(x; degrés_liberté; cumulative)", "d": "Renvoie la probabilité unilatérale à gauche d’une variable aléatoire continue suivant une loi du Khi-deux", "ad": "représente la valeur à laquelle vous voulez évaluer la distribution, un nombre positif!représente le nombre de degrés de liberté, un nombre entre 1 et 10^10, 10^10 exclus!représente une valeur logique à renvoyer par la fonction : fonction distribution cumulée = VRAI ; fonction densité de la probabilité = FAUX"}, "CHISQ.DIST.RT": {"a": "(x; deg<PERSON>s_liberté)", "d": "Renvoie la probabilité unilatérale à droite d’une variable aléatoire continue suivant une loi du Khi-deux", "ad": "représente la valeur à laquelle vous voulez évaluer la distribution, un nombre non négatif!représente le nombre de degrés de liberté, un nombre entre 1 et 10^10, 10^10 exclus"}, "CHISQ.INV": {"a": "(probabilité; degrés_liberté)", "d": "Ren<PERSON><PERSON>, pour une probabilité unilatérale à gauche donnée, la valeur d’une variable aléatoire suivant une loi du <PERSON>hi-deux", "ad": "représente une probabilité associée à la distribution du Khi-deux, une valeur entre 0 et 1 compris!représente le nombre de degrés de liberté, un nombre entre 1 et 10^10, 10^10 exclus"}, "CHISQ.INV.RT": {"a": "(probabilité; degrés_liberté)", "d": "Ren<PERSON><PERSON>, pour une probabilité unilatérale à droite donnée, la valeur d’une variable aléatoire suivant une loi du <PERSON>hi-deux", "ad": "représente une probabilité associée à la distribution du Khi-deux, une valeur entre 0 et 1 compris!représente le nombre de degrés de liberté, un nombre entre 1 et 10^10, 10^10 exclus"}, "CHISQ.TEST": {"a": "(plage_réelle; plage_prévue)", "d": "Renvoie le test d’indépendance : la valeur de la distribution Khi-deux pour la statistique et les degrés de liberté appropriés", "ad": "représente la plage de données contenant les observations à tester par rapport aux valeurs attendues!représente la plage de données contenant le rapport du produit des totaux de ligne et colonne avec le total général"}, "CONFIDENCE": {"a": "(alpha; écart_type; taille)", "d": "Renvoie l’intervalle de confiance pour la moyenne d’une population à l’aide d’une distribution normale. Consultez l’aide sur l’équation utilisée", "ad": "représente le seuil de probabilité, un nombre supérieur à 0 et inférieur à 1!représente l’écart-type de la population ; cette valeur est supposée connue. L’écart-type doit être supérieur à 0!est la taille de l’échantillon"}, "CONFIDENCE.NORM": {"a": "(alpha; écart_type; taille)", "d": "Renvoie l’intervalle de confiance pour la moyenne d’une population, à l’aide d’une loi normale", "ad": "représente le degré de précision utilisé pour calculer le niveau de confiance, un nombre supérieur à 0 et inférieur à 1!représente l’écart-type de la population pour la plage de données ; cette valeur est supposée connue. L’écart-type doit être supérieur à 0!est la taille de l’échantillon"}, "CONFIDENCE.T": {"a": "(alpha; écart_type; taille)", "d": "Renvoie l’intervalle de confiance pour la moyenne d’une population, à l’aide de la probabilité d’une variable aléatoire suivant une loi T de Student", "ad": "représente le degré de précision utilisé pour calculer le niveau de confiance, un nombre supérieur à 0 et inférieur à 1!représente l’écart-type de la population pour la plage de données ; cette valeur est supposée connue. L’écart-type doit être supérieur à 0!est la taille de l’échantillon"}, "CORREL": {"a": "(matrice1; matrice2)", "d": "Renvoie le coefficient de corrélation entre deux séries de données", "ad": "représente une plage de cellules de valeurs. Les valeurs doivent être un nombre, un nom, une matrice ou une référence qui contient des nombres!représente une seconde plage de cellules de valeurs. Les valeurs doivent être un nombre, un nom, une matrice ou une référence qui contient des nombres"}, "COUNT": {"a": "(valeur1; [valeur2]; ...)", "d": "<PERSON><PERSON><PERSON>mine le nombre de cellules d'une plage contenant des nombres", "ad": "représentant de 1 à 255 arguments qui peuvent contenir ou faire référence à différents types de données, mais dont seuls les nombres sont comptés"}, "COUNTA": {"a": "(valeur1; [valeur2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> le nombre de cellules d'une plage qui ne sont pas vides", "ad": "représentant de 1 à 255 arguments et correspondant aux valeurs et cellules à compter. Les valeurs peuvent être de n'importe quel type"}, "COUNTBLANK": {"a": "(plage)", "d": "Compte le nombre de cellules vides à l'intérieur d'une plage spécifique", "ad": "est la plage dans laquelle compter les cellules vides"}, "COUNTIF": {"a": "(plage; critère)", "d": "Détermine le nombre de cellules non vides répondant à la condition à l'intérieur d'une plage", "ad": "est la plage de cellules dans laquelle compter les cellules non vides.!est la condition, exprimée sous forme de nombre, d'expression ou de texte qui détermine quelles cellules seront comptées"}, "COUNTIFS": {"a": "(plage_critères; critères; ...)", "d": "Compte le nombre de cellules spécifiées par un ensemble de conditions ou de critères", "ad": "représente la plage de cellules à évaluer d'après une condition particulière!représente la condition sous la forme d'un nombre, d'une expression ou de texte définissant quelles cellules doivent être comptées"}, "COVAR": {"a": "(matrice1; matrice2)", "d": "Renvoie la covariance, moyenne du produit des écarts à la moyenne de chaque paire de points de deux séries", "ad": "est la première plage de cellules contenant des entiers et doit être un nombre, un nom, une matrice, ou une référence qui contient des nombres!est la seconde plage de cellules contenant des entiers et doit être un nombre, un nom, une matrice, ou une référence qui contient des nombres"}, "COVARIANCE.P": {"a": "(matrice1; matrice2)", "d": "Renvoie la covariance de population, moyenne du produit des écarts à la moyenne de chaque paire de points de deux séries", "ad": "est la première plage de cellules contenant des entiers et doit être un nombre, une matrice ou une référence qui contient des nombres!est la seconde plage de cellules contenant des entiers et doit être un nombre, une matrice ou une référence qui contient des nombres"}, "COVARIANCE.S": {"a": "(matrice1; matrice2)", "d": "Renvoie la covariance d’échantillon, moyenne du produit des écarts à la moyenne de chaque paire de points de deux séries", "ad": "est la première plage de cellules contenant des entiers et doit être un nombre, une matrice ou une référence qui contient des nombres!est la seconde plage de cellules contenant des entiers et doit être un nombre, une matrice ou une référence qui contient des nombres"}, "CRITBINOM": {"a": "(tirages; probabilité_succès; alpha)", "d": "Renvoie la plus petite valeur pour laquelle la distribution binomiale cumulée est supérieure ou égale à une valeur critère", "ad": "représente le nombre de tirages de <PERSON>lli!représente la probabilité de succès à chaque tirage, un nombre entre 0 et 1 compris!représente la valeur critère, un nombre entre 0 et 1 compris"}, "DEVSQ": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la somme des carrés des écarts entre les points de données et leur moyenne échantillonnée", "ad": "représentent de 1 à 255 arguments, une matrice ou une référence à une matrice, auxquels appliquer une SOMME.CARRES.ECARTS"}, "EXPONDIST": {"a": "(x; lambda; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire continue suivant une loi exponentielle. Consultez l’aide sur l’équation utilisée", "ad": "représente la valeur de la fonction!représente la valeur du paramètre, un nombre positif!représente une valeur logique à renvoyer par la fonction : fonction distribution cumulée = VRAI ; fonction densité de la probabilité = FAUX"}, "EXPON.DIST": {"a": "(x; lambda; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire continue suivant une loi exponentielle. Consultez l’aide sur l’équation utilisée", "ad": "représente la valeur de la fonction!représente la valeur du paramètre, un nombre positif!représente une valeur logique à renvoyer par la fonction : fonction distribution cumulée = VRAI ; fonction densité de la probabilité = FAUX"}, "FDIST": {"a": "(x; degrés_liberté1; degrés_liberté2)", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi F pour deux séries de données", "ad": "représente la valeur à laquelle la fonction doit être évaluée, un nombre positif!représente le nombre de degrés de liberté du numérateur, un nombre entre 1 et 10^10, 10^10 exclus!représente le nombre de degrés de liberté du dénominateur, un nombre entre 1 et 10^10, 10^10 exclus"}, "FINV": {"a": "(probabilité; degrés_liberté1; degrés_liberté2)", "d": "Renvoie l’inverse de la distribution de probabilité (unilatérale à droite) suivant une loi F : si p = LOI.F (x,...), alors INVERSE.LOI.F (p,...) = x", "ad": "représente la probabilité associée à la distribution cumulée F, un nombre entre 0 et 1 compris!représente le nombre de degrés de liberté du numérateur, un nombre entre 1 et 10^10, 10^10 exclus!représente le nombre de degrés de liberté du dénominateur, un nombre entre 1 et 10^10, 10^10 exclus"}, "FTEST": {"a": "(matrice1; matrice2)", "d": "Renvoie le résultat d’un test F, c’est-à-dire la probabilité d’une variable aléatoire continue que les variances dans Matrice1 et Matrice2 ne soient pas différentes de manière significative", "ad": "représente la première matrice ou plage de données et peut être des nombres, des noms, des matrices, ou des références qui contiennent des nombres (les cellules vides sont ignorées)!représente la seconde matrice ou plage de données et peut être des nombres, des noms, des matrices ou des références qui contiennent des nombres (les cellules vides sont ignorées)"}, "F.DIST": {"a": "(x; degrés_liberté1; degrés_liberté2; cumulée)", "d": "Renvoie la probabilité (unilatérale à gauche) d’une variable aléatoire suivant une loi F pour deux séries de données", "ad": "est la valeur à laquelle la fonction doit être évaluée, un nombre positif!représente le nombre de degrés de liberté du numérateur, un nombre entre 1 et 10^10, 10^10 exclus!représente le nombre de degrés de liberté du dénominateur, un nombre entre 1 et 10^10, 10^10 exclus!représente une valeur logique à renvoyer par la fonction : fonction distribution cumulée = VRAI ; fonction densité de la probabilité = FAUX"}, "F.DIST.RT": {"a": "(x; degrés_liberté1; degrés_liberté2)", "d": "Renvoie la probabilité (unilatérale à droite) d’une variable aléatoire suivant une loi F pour deux séries de données", "ad": "est la valeur à laquelle la fonction doit être évaluée, un nombre positif!représente le nombre de degrés de liberté du numérateur, un nombre entre 1 et 10^10, 10^10 exclus!représente le nombre de degrés de liberté du dénominateur, un nombre entre 1 et 10^10, 10^10 exclus"}, "F.INV": {"a": "(probabilité; degrés_liberté1; degrés_liberté2)", "d": "Renvoie l’inverse de la distribution de probabilité (unilatérale à gauche) suivant une loi F : si p = LOI.F (x,...), alors INVERSE.LOI.F.N (p,...) = x", "ad": "représente la probabilité associée à la distribution cumulée F, un nombre entre 0 et 1 compris!représente le nombre de degrés de liberté du numérateur, un nombre entre 1 et 10^10, 10^10 exclus!représente le nombre de degrés de liberté du dénominateur, un nombre entre 1 et 10^10, 10^10 exclus"}, "F.INV.RT": {"a": "(probabilité; degrés_liberté1; degrés_liberté2)", "d": "Renvoie l’inverse de la distribution de probabilité (unilatérale à droite) suivant une loi F : si p = LOI.F.DROITE (x,...), alors INVERSE.LOI.F.DROITE (p,...) = x", "ad": "représente la probabilité associée à la distribution cumulée F, un nombre entre 0 et 1 compris!représente le nombre de degrés de liberté du numérateur, un nombre entre 1 et 10^10, 10^10 exclus!représente le nombre de degrés de liberté du dénominateur, un nombre entre 1 et 10^10, 10^10 exclus"}, "F.TEST": {"a": "(matrice1; matrice2)", "d": "Renvoie le résultat d’un test F, la probabilité bilatérale que les variances des matrice1 et matrice2 ne sont pas très différentes", "ad": "est la première matrice ou plage de données et peut être des numéros, noms, matrices ou références qui contiennent des nombres (les cellules vides sont ignorées)!est la seconde matrice ou plage de données et peut être des numéros, noms, matrices ou références qui contiennent des nombres (les cellules vides sont ignorées)"}, "FISHER": {"a": "(x)", "d": "Renvoie la transformation de Fisher. Consultez l'aide sur l'équation utilisée", "ad": "est une valeur numérique pour laquelle vous voulez obtenir la transformation, un nombre entre -1 et 1, -1 et 1 exclus"}, "FISHERINV": {"a": "(y)", "d": "Renvoie la transformation de Fisher inverse : si y = FISHER(x), alors FISHER.INVERSE(y) = x. <PERSON><PERSON>z l'aide sur l'équation utilisée", "ad": "est la valeur pour laquelle vous voulez effectuer la transformation inverse de Fisher"}, "FORECAST": {"a": "(x; y_connus; x_connus)", "d": "<PERSON><PERSON>, ou prédit, une valeur future suivant une tendance linéaire, en utilisant les valeurs existantes", "ad": "représente le point de données dont vous voulez prévoir la valeur et doit être une valeur numérique!représente la matrice ou la plage de données numériques dépendante!représente la matrice ou la plage de données numériques indépendante. La variance de x_connus doit être différente de zéro"}, "FORECAST.ETS": {"a": "(date_cible; valeurs; chronologie; [caractère_saisonnier]; [achèvement_données]; [agrégation])", "d": "Renvoie la valeur prévue pour une date cible ultérieure spécifiée à l’aide de la méthode de lissage exponentiel.", "ad": "représente le point de données pour lequel Spreadsheet Editor prévoit une valeur. Il doit porter sur le modèle des valeurs de la chronologie.!représente la matrice ou la plage de données numériques pour laquelle vous effectuez une prédiction.!représente la matrice ou plage de données numériques indépendante. Les dates de la chronologie doivent être séparées de manière cohérente et être différentes de zéro.!représente une valeur numérique facultative qui indique la longueur du modèle saisonnier. La valeur par défaut 1 indique que le caractère saisonnier est détecté automatiquement.!représente une valeur facultative pour la gestion des valeurs manquantes. La valeur par défaut 1 remplace les valeurs manquantes par interpolation, et 0 les remplace par des zéros.!est une valeur numérique facultative pour l'agrégation de plusieurs valeurs avec le même horodatage. Si elle est vide, Spreadsheet Editor calcule la moyenne des valeurs."}, "FORECAST.ETS.CONFINT": {"a": "(date_cible; valeurs; chronologie; [niveau_confiance]; [caractère_saisonnier]; [achèvement_données]; [agrégation])", "d": "Renvoie un intervalle de confiance pour la valeur de prévision à la date cible spécifiée.", "ad": "représente le point de données pour lequel Spreadsheet Editor prévoit une valeur. Il doit porter sur le modèle des valeurs de la chronologie.!représente la matrice ou la plage de données numériques pour laquelle vous effectuez une prédiction.!représente la matrice ou plage de données numériques indépendante. Les dates de la chronologie doivent être séparées de manière cohérente et être différentes de zéro.!représente un nombre compris entre 0 et 1 qui indique le niveau de confiance de l’intervalle de confiance calculé. La valeur par défaut est .95.!représente une valeur numérique facultative qui indique la longueur du modèle saisonnier. La valeur par défaut 1 indique que le caractère saisonnier est détecté automatiquement.!représente une valeur facultative pour la gestion des valeurs manquantes. La valeur par défaut 1 remplace les valeurs manquantes par interpolation, et 0 les remplace par des zéros.!est une valeur numérique facultative pour l'agrégation de plusieurs valeurs avec le même horodatage. Si elle est vide, Spreadsheet Editor calcule la moyenne des valeurs."}, "FORECAST.ETS.SEASONALITY": {"a": "(valeurs; chronologie; [achèvement_données]; [agrégation])", "d": "Renvoie la longueur du motif répétitif détecté par l'application pour la série temporelle spécifiée.", "ad": "représente la matrice ou la plage de données numériques que vous prévoyez !représente la matrice ou plage de données numériques indépendante. Les dates de la chronologie doivent être séparées de manière cohérente et être différentes de zéro. !représente une valeur facultative pour la gestion des valeurs manquantes. La valeur par défaut 1 remplace les valeurs manquantes par interpolation, et la valeur 0 les remplace par des zéros.!représente une valeur numérique facultative pour l’agrégation de plusieurs valeurs avec le même horodatage. En l’absence de valeur, Spreadsheet Editor effectue la moyenne des valeurs."}, "FORECAST.ETS.STAT": {"a": "(valeurs; chronologie; type_statistique; [caractère_saisonnier]; [achèvement_données]; [agrégation])", "d": "Renvoie les statistiques demandées pour la prévision.", "ad": "est le tableau ou la plage de données numériques que vous prévoyez.!est le tableau ou la plage de données numériques indépendants. Les dates de la chronologie doivent être séparées par un pas cohérent et ne peuvent pas être nulles.!est un nombre compris entre 1 et 8, indiquant quelle statistique Spreadsheet Editor sera retournée pour la prévision calculée.!est une valeur numérique facultative qui indique la longueur du modèle saisonnier. La valeur par défaut de 1 indique que la saisonnalité est détectée automatiquement.!est une valeur facultative pour le traitement des valeurs manquantes. La valeur par défaut de 1 remplace les valeurs manquantes par interpolation, et 0 les remplace par des zéros.!est une valeur numérique facultative pour l'agrégation de plusieurs valeurs avec le même horodatage. Si elle est vide, Spreadsheet Editor calcule la moyenne des valeurs. "}, "FORECAST.LINEAR": {"a": "(x; y_connus; x_connus)", "d": "<PERSON><PERSON>, ou prédit, une valeur future suivant une tendance linéaire, en utilisant les valeurs existantes", "ad": "représente le point de données dont vous voulez prévoir la valeur et doit être une valeur numérique!représente la matrice ou la plage de données numériques dépendante!représente la matrice ou la plage de données numériques indépendante. La variance de x_connus doit être différente de zéro"}, "FREQUENCY": {"a": "(tableau_données; matrice_classes)", "d": "Calcule la fréquence à laquelle les valeurs apparaissent dans une plage de valeurs, puis renvoie une matrice verticale de nombres ayant un élément de plus que l’argument matrice_classes", "ad": "est un tableau ou une référence comprenant les valeurs parmi lesquelles vous recherchez une fréquence (les cellules vides et le texte sont ignorés)!est une matrice ou une référence aux intervalles permettant de grouper les valeurs de l’argument tableau_données"}, "GAMMA": {"a": "(x)", "d": "Renvoie la valeur de la fonction Gamma", "ad": "est la valeur pour laquelle vous voulez calculer Gamma"}, "GAMMADIST": {"a": "(x; alpha; bêta; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi Gamma. Consultez l’aide sur l’équation utilisée", "ad": "représente la valeur à laquelle vous voulez évaluer la distribution, un nombre positif!représente un paramètre de la distribution, un nombre positif!représente un paramètre de la distribution. Si bêta vaut 1, cette fonction renvoie la probabilité d’une variable aléatoire suivant une loi Gamma centrée réduite!est une valeur logique : renvoie la fonction de distribution cumulée = VRAI ; ou fonction de probabilité de masse = FAUX ou omis"}, "GAMMA.DIST": {"a": "(x; alpha; bêta; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi Gamma", "ad": "représente la valeur à laquelle vous voulez évaluer la distribution, un nombre positif!représente un paramètre de la distribution, un nombre positif! Si bêta vaut 1, LOI.GAMMA.N renvoie la probabilité d’une variable aléatoire suivant une loi Gamma centrée réduite!est une valeur logique : renvoie la fonction de distribution cumulée = VRAI; ou fonction de probabilité de masse = FAUX ou omis"}, "GAMMAINV": {"a": "(probabilité; alpha; bêta)", "d": "Ren<PERSON><PERSON>, pour une probabilité donnée, la valeur d’une variable aléatoire suivant une loi Gamma : si p = LOI.GAMMA(x,...), alors LOI.GAMMA.INVERSE(p,...) = x", "ad": "représente la probabilité associée à la loi Gamma, un nombre entre 0 et 1 compris!représente un paramètre de la distribution, un nombre positif!représente un paramètre de la distribution, un nombre positif. Si bêta = 1, LOI.GAMMA.INVERSE renvoie l’inverse de la valeur d’une variable aléatoire suivant une loi Gamma centrée réduite"}, "GAMMA.INV": {"a": "(probabilité; alpha; bêta)", "d": "<PERSON><PERSON><PERSON>, pour une probabilité donnée, la valeur d’une variable aléatoire suivant une loi Gamma: si p = LOI.GAMMA.N(x,...), alors LOI.GAMMA.INVERSE.N(p,...) = x", "ad": "représente la probabilité associée à la loi Gamma, un nombre entre 0 et 1 compris!représente un paramètre de la distribution, un nombre positif!représente un paramètre de la distribution, un nombre positif. Si bêta = 1, LOI.GAMMA.INVERSE.N renvoie l’inverse de la valeur d’une variable aléatoire suivant une loi Gamma centrée réduite"}, "GAMMALN": {"a": "(x)", "d": "Renvoie le logarithme népérien de la fonction Gamma. Consultez l'aide pour obtenir des informations sur l'équation utilisée", "ad": "représente la valeur pour laquelle vous voulez calculer LNGAMMA, un nombre positif"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Renvoie le logarithme népérien de la fonction Gamma", "ad": "représente la valeur pour laquelle vous voulez calculer LNGAMMA.PRECIS, un nombre positif"}, "GAUSS": {"a": "(x)", "d": "Renvoie 0,5 de moins que la distribution cumulée suivant une loi normale centrée réduite", "ad": "représente la valeur dont vous recherchez la distribution"}, "GEOMEAN": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la moyenne géométrique d'une matrice ou d'une plage de données numériques positives", "ad": "représentent de 1 à 255 nombres, noms, matrices ou références contenant des nombres dont vous recherchez la moyenne géométrique"}, "GROWTH": {"a": "(y_connus; [x_connus]; [x_nouveaux]; [constante])", "d": "Calcule les valeurs de la tendance géométrique exponentielle à partir de valeurs connues", "ad": "représente la série des valeurs y déjà connues par la relation y = b*m ^ x, une matrice ou plage de nombres positifs!représente une série de valeurs x facultatives, éventuellement déjà données par la relation y = b*m ^ x, une matrice ou plage de même taille qu’y_connus!représente la nouvelle série de variables x dont vous voulez que CROISSANCE vous donne les valeurs y correspondantes!représente une valeur logique : la constante b est calculée normalement si Constante = VRAI ; b est égal à 1 si Constante = FAUX ou omis"}, "HARMEAN": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la moyenne harmonique d'une série de données en nombres positifs : la réciproque de la moyenne arithmétique des réciproques", "ad": "représentent de 1 à 255 nombres, noms, matrices ou références qui contiennent des nombres dont vous recherchez la moyenne harmonique"}, "HYPGEOM.DIST": {"a": "(succès_échantillon; nombre_échantillon; succès_population; nombre_population; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire discrète suivant une loi hypergéométrique", "ad": "est le nombre de succès contenu dans l’échantillon!est la taille de l’échantillon!est le nombre de succès dans la population!est la taille de la population!représente une valeur logique : pour la fonction distribution cumulative, utilisez VRAI ; pour la fonction de densité de probabilité, utilisez FAUX"}, "HYPGEOMDIST": {"a": "(succès_échantillon; nombre_échantillon; succès_population; nombre_population)", "d": "Renvoie la probabilité d’une variable aléatoire discrète suivant une loi hypergéométrique", "ad": "est le nombre de succès contenu dans l’échantillon!est la taille de l’échantillon!est le nombre de succès dans la population!est la taille de la population"}, "INTERCEPT": {"a": "(y_connus; x_connus)", "d": "Calcule le point auquel une droite va croiser l’axe des y en traçant une droite de régression linéaire d’après les valeurs connues de x et de y", "ad": "représente la série dépendante d’observations ou de données et peut être un nombre, un nom, une matrice, ou une référence qui contient des nombres!représente la série indépendante d’observations ou de données et peut être un nombre, un nom, une matrice, ou une référence qui contient des nombres"}, "KURT": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie le kurtosis d'une série de données. Consultez l'aide sur l'équation utilisée", "ad": "représentent les 1 à 255 nombres, noms, matrices, ou références qui contiennent des nombres, dont vous voulez calculer le kurtosis"}, "LARGE": {"a": "(matrice; k)", "d": "Renvoie la k-ième plus grande valeur d'une série de données", "ad": "représente la matrice ou la plage de données dans laquelle vous recherchez la k-ième plus grande valeur!représente, dans la matrice ou la plage de cellules, le rang de la donnée à renvoyer, déterminé à partir de la valeur la plus grande"}, "LINEST": {"a": "(y_connus; [x_connus]; [constante]; [statistiques])", "d": "Renvoie une matrice qui décrit une droite de corrélation pour vos données, calculée avec la méthode des moindres carrés", "ad": "représente la série des valeurs y déjà déterminées par la relation y = m x + b!représente une série de valeurs x facultatives, déjà déterminées dans la relation y = m x + b!représente une valeur logique : la constante b est calculée normalement si Constante = VRAI ou omise ; b est égale à 0 si Constante = FAUX!représente une valeur logique : renvoyer les statistiques de régression complémentaires = VRAI ; renvoyer les coefficients m ou la constante b = FAUX ou omis"}, "LOGEST": {"a": "(y_connus; [x_connus]; [constante]; [statistiques])", "d": "Renvoie des statistiques qui décrivent une courbe de corrélation exponentielle à partir de valeurs connues", "ad": "représente la série des valeurs y déjà connues par la relation y = b*m ^ x!représente une série de valeurs x facultatives, éventuellement déjà données par la relation y = b*m ^ x!représente une valeur logique : la constante b est calculée normalement si Constante = VRAI ou omis ; b est égal à 1 si Constante = FAUX!représente une valeur logique : renvoyer les statistiques de régression complémentaires = VRAI ; renvoyer les coefficients m ou la constante b = FAUX ou omis"}, "LOGINV": {"a": "(probabilité; espérance; écart_type)", "d": "Renvoie l’inverse de la fonction de distribution de x suivant une loi lognormale cumulée, où In(x) est normalement distribué avec les paramètres Espérance et Écart_type", "ad": "représente la probabilité associée à la distribution, un nombre entre 0 et 1 compris!représente l’espérance mathématique de ln(x)!représente l’écart-type de ln(x), un nombre positif"}, "LOGNORM.DIST": {"a": "(x; espérance; écart_type; cumulative)", "d": "Renvoie la fonction de distribution de x suivant une loi lognormale, où In(x) est normalement distribué avec les paramètres Espérance et Écart_type", "ad": "représente la valeur à laquelle la fonction doit être évaluée, un nombre positif!représente l’espérance mathématique de ln(x)!représente l’écart-type de ln(x), un nombre positif!représente une valeur logique : pour la fonction distribution cumulative, utilisez VRAI ; pour la fonction densité de probabilité, utilisez FAUX"}, "LOGNORM.INV": {"a": "(probabilité; espérance; écart_type)", "d": "Renvoie l’inverse de la fonction de distribution de x suivant une loi lognormale cumulée, où In(x) est normalement distribué avec les paramètres Espérance et Écart_type", "ad": "représente la probabilité associée à la distribution, un nombre entre 0 et 1 compris!représente l’espérance mathématique de ln(x)!représente l’écart-type de ln(x), un nombre positif"}, "LOGNORMDIST": {"a": "(x; espérance; écart_type)", "d": "Renvoie la distribution de x suivant une loi lognormale cumulée, où ln(x) est normalement distribué avec les paramètres Espérance et Écart_type", "ad": "représente la valeur à laquelle la fonction doit être évaluée, un nombre positif!représente l’espérance mathématique de ln(x)!représente l’écart-type de ln(x), un nombre positif"}, "MAX": {"a": "(nombre1; [nombre2]; ...)", "d": "Donne la valeur la plus grande parmi une liste de valeurs. Ignore les valeurs logiques et le texte", "ad": "représentent de 1 à 255 nombres, cellules vides, valeurs logiques ou nombres au format texte parmi lesquels vous voulez trouver la valeur la plus grande"}, "MAXA": {"a": "(valeur1; [valeur2]; ...)", "d": "Renvoie le plus grand nombre d'un ensemble de valeurs. Prend en compte les valeurs logiques et le texte", "ad": "représentent de 1 à 255 nombres, cellules vides, valeurs logiques ou nombres au format texte parmi lesquels vous voulez trouver la valeur la plus grande"}, "MAXIFS": {"a": "(plage_max; plage_critères; critères; ...)", "d": "Renvoie la valeur maximale parmi les cellules spécifiées par un ensemble de conditions ou de critères donné", "ad": "les cellules dans lesquelles déterminer la valeur maximale!représente la plage de cellules à évaluer pour la condition spécifique!représente la condition ou les critères sous la forme d’un nombre, d’une expression ou d’un texte qui définit les cellules à inclure lors de la détermination de la valeur maximale "}, "MEDIAN": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la valeur médiane ou le nombre qui se trouve au milieu d'une liste de nombres fournie", "ad": "représentent de 1 à 255 nombres, matrices ou références qui contiennent des nombres, dont vous voulez obtenir la médiane"}, "MIN": {"a": "(nombre1; [nombre2]; ...)", "d": "Donne la valeur la plus petite parmi une liste de valeurs. Ignore les valeurs logiques et le texte", "ad": "représentent de 1 à 255 nombres, cellules vides, valeurs logiques ou nombres au format texte parmi lesquels vous voulez trouver la valeur la plus petite"}, "MINA": {"a": "(valeur1; [valeur2]; ...)", "d": "Renvoie la plus petite valeur d'un ensemble de données. Prend en compte des valeurs logiques et le texte", "ad": "représentent de 1 à 255 nombres, cellules vides, valeurs logiques ou nombres sous forme de texte parmi lesquels vous voulez trouver la valeur la plus petite"}, "MINIFS": {"a": "(plage_min; plage_critères; critères; ...)", "d": "Renvoie la valeur minimale parmi les cellules spécifiées par un ensemble de conditions ou de critères donné", "ad": "les cellules dans lesquelles déterminer la valeur minimale!représente la plage de cellules à évaluer pour la condition spécifique!représente la condition ou les critères sous la forme d’un nombre, d’une expression ou d’un texte qui définit les cellules à inclure lors de la détermination de la valeur minimale "}, "MODE": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la valeur la plus fréquente d’une série de données", "ad": "représentent les 1 à 255 nombres, noms, matrices ou références qui contiennent des nombres dont vous recherchez le mode"}, "MODE.MULT": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie une matrice verticale des valeurs les plus fréquentes d’une matrice ou série de données. Pour une matrice horizontale, utilisez =TRANSPOSER(MODE.MULTIPLE(nombre1,nombre2,...))", "ad": "représentent des nombres de 1 à 255  ou des noms, matrices ou références qui contiennent des nombres dont vous recherchez le mode"}, "MODE.SNGL": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la valeur la plus fréquente d’une série de données", "ad": "représentent les 1 à 255 nombres, noms, matrices ou références qui contiennent des nombres dont vous recherchez le mode"}, "NEGBINOM.DIST": {"a": "(nombre_échecs; nombre_succès; probabilité_succès; cumulative)", "d": "Renvoie la distribution négative binomiale, probabilité d’obtenir un nombre d’échecs égal à Nombre_échecs avant le succès numéro Nombre_succès, avec une probabilité égale à Probabilité_succès", "ad": "représente le nombre d’échecs!représente le nombre de succès à obtenir!représente la probabilité d’obtenir un succès ; un nombre entre 0 et 1!représente une valeur logique : pour la fonction distribution cumulative, utilisez VRAI ; pour la fonction densité de probabilité, utilisez FAUX"}, "NEGBINOMDIST": {"a": "(nombre_échecs; nombre_succès; probabilité_succès)", "d": "Renvoie la distribution négative binomiale, probabilité d’obtenir un nombre d’échecs égal à Nombre_échecs avant le succès numéro Nombre_succès, avec une probabilité égale à Probabilité_succès", "ad": "représente le nombre d’échecs!représente le nombre de succès à obtenir!représente la probabilité d’obtenir un succès; un nombre entre 0 et 1"}, "NORM.DIST": {"a": "(x; espérance; écart_type; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire continue suivant une loi normale pour l’espérance arithmétique et l’écart-type spécifiés", "ad": "représente la valeur dont vous recherchez la distribution!représente l’espérance arithmétique de la distribution!représente l’écart-type de la distribution, un nombre positif!représente une valeur logique : pour la fonction distribution cumulative, utilisez VRAI  pour la fonction de probabilité de masse, utilisez FAUX"}, "NORMDIST": {"a": "(x; espérance; écart_type; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire continue suivant une loi normale pour l’espérance arithmétique et l’écart-type spécifiés", "ad": "représente la valeur dont vous recherchez la distribution!représente l’espérance arithmétique de la distribution!représente l’écart-type de la distribution, un nombre positif!représente une valeur logique : pour la fonction distribution cumulative, utilisez VRAI ; pour la fonction de densité de distribution de la probabilité, utilisez FAUX"}, "NORM.INV": {"a": "(probabilité; espérance; écart_type)", "d": "Ren<PERSON><PERSON>, pour une probabilité donnée, la valeur d’une variable aléatoire suivant une loi normale pour la moyenne et l’écart-type spécifiés", "ad": "représente une probabilité correspondant à la distribution normale, un nombre entre 0 et 1 compris!représente l’espérance mathématique de la distribution!représente l’écart-type de la distribution, un nombre positif"}, "NORMINV": {"a": "(probabilité; espérance; écart_type)", "d": "Ren<PERSON><PERSON>, pour une probabilité donnée, la valeur d’une variable aléatoire suivant une loi normale pour la moyenne et l’écart-type spécifiés", "ad": "représente une probabilité correspondant à la distribution normale, un nombre entre 0 et 1 compris!représente l’espérance mathématique de la distribution!représente l’écart-type de la distribution, un nombre positif"}, "NORM.S.DIST": {"a": "(z; cumulative)", "d": "Renvoie la distribution suivant une loi normale centrée réduite (d’espérance nulle et d’écart-type égal à 1)", "ad": "représente la valeur dont vous recherchez la distribution!représente une valeur logique : pour la fonction distribution cumulative, utilisez VRAI ; pour la fonction densité de probabilité, utilisez FAUX"}, "NORMSDIST": {"a": "(z)", "d": "Renvoie la distribution cumulée suivant une loi normale centrée réduite (d’espérance nulle et d’écart-type égal à 1)", "ad": "représente la valeur dont vous recherchez la distribution"}, "NORM.S.INV": {"a": "(probabilité)", "d": "Ren<PERSON><PERSON>, pour une probabilité donnée, la valeur d’une variable aléatoire suivant une loi normale standard (ou centrée réduite), c’est-à-dire ayant une moyenne de zéro et un écart-type de 1", "ad": "représente une probabilité correspondant à la distribution normale, un nombre entre 0 et 1 compris"}, "NORMSINV": {"a": "(probabilité)", "d": "Ren<PERSON><PERSON>, pour une probabilité donnée, la valeur d’une variable aléatoire suivant une loi normale standard (ou centrée réduite), c’est-à-dire ayant une moyenne de zéro et un écart-type de 1", "ad": "représente une probabilité correspondant à la distribution normale, un nombre entre 0 et 1 compris"}, "PEARSON": {"a": "(matrice1; matrice2)", "d": "Renvoie le coefficient de corrélation d'échantillonnage de Pearson, r. <PERSON><PERSON><PERSON> l'aide sur l'équation utilisée", "ad": "représente une série de valeurs indépendantes!représente une série de valeurs dépendantes"}, "PERCENTILE": {"a": "(matrice; k)", "d": "Renvoie le k-ième centile des valeurs d’une plage", "ad": "représente la matrice ou la plage de données définissant l’étendue relative!représente le centile ; celui-ci doit être compris entre 0 et 1 inclus"}, "PERCENTILE.EXC": {"a": "(matrice; k)", "d": "Renvoie le k-ième centile des valeurs d’une plage, où k se trouve dans la plage de 0 à 1, non compris", "ad": "représente la matrice ou la plage de données définissant l’étendue relative!représente le centile qui doit être compris entre 0 et 1 inclus"}, "PERCENTILE.INC": {"a": "(matrice; k)", "d": "Renvoie le k-ième centile des valeurs d’une plage, où k se trouve dans la plage de 0 à 1, compris", "ad": "représente la matrice ou la plage de données définissant l’étendue relative!représente le centile qui doit être compris entre 0 et 1 inclus"}, "PERCENTRANK": {"a": "(matrice; x; [précision])", "d": "Renvoie le rang en pourcentage d’une valeur d’une série de données", "ad": "représente la matrice ou la plage de données de valeurs numériques définissant l’étendue relative!représente la valeur dont vous voulez connaître le rang!représente une valeur facultative indiquant le nombre de décimales du pourcentage renvoyé, 3 chiffres après la décimale si omise (0,xxx %)"}, "PERCENTRANK.EXC": {"a": "(matrice; x; [précision])", "d": "Renvoie le rang en pourcentage (0..1, non compris) d’une valeur d’une série de données", "ad": "est le tableau ou la plage de données avec des valeurs numériques qui définit la position relative!est la valeur dont vous voulez connaître le rang ! est une valeur facultative qui identifie le nombre de chiffres significatifs pour le pourcentage renvoyé, trois chiffres si omis (0,xxx %) "}, "PERCENTRANK.INC": {"a": "(matrice; x; [précision])", "d": "Renvoie le rang en pourcentage (0..1, inclus) d’une valeur d’une série de données", "ad": "est le tableau ou la plage de données avec des valeurs numériques qui définit la position relative!est la valeur dont vous voulez connaître le rang ! est une valeur facultative qui identifie le nombre de chiffres significatifs pour le pourcentage renvoyé, trois chiffres si omis (0,xxx %) "}, "PERMUT": {"a": "(nombre; nombre_choisi)", "d": "Renvoie le nombre de permutations pour un nombre donné d'objets", "ad": "représente un nombre entier correspondant au nombre d'objets!représente le nombre d'objets contenus dans chaque permutation"}, "PERMUTATIONA": {"a": "(nombre; nombre_choisi)", "d": "Renvoie le nombre de permutations pour un nombre donné d'objets (avec répétitions)", "ad": "représente un nombre entier correspondant au nombre d'objets!représente le nombre d'objets contenus dans chaque permutation"}, "PHI": {"a": "(x)", "d": "Renvoie la valeur de la fonction de densité pour une distribution suivant une loi normale centrée réduite", "ad": "représente le nombre pour lequel vous recherchez la densité de la distribution suivant une loi normale centrée réduite"}, "POISSON": {"a": "(x; espérance; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi de Poisson", "ad": "représente le nombre d’événements!représente l’espérance mathématique, un nombre positif!représente une valeur logique : pour la probabilité cumulative de Poisson, utilisez VRAI ; pour la fonction de probabilité de masse en série de Poisson, utilisez FAUX"}, "POISSON.DIST": {"a": "(x; espérance; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi de Poisson", "ad": "représente le nombre d’événements!représente l’espérance mathématique, un nombre positif!représente une valeur logique : pour la probabilité cumulative de Poisson, utilisez VRAI ; pour la fonction de probabilité de masse en série de Poisson, utilisez FAUX"}, "PROB": {"a": "(plage_x; plage_probabilité; limite_inf; [limite_sup])", "d": "Renvoie la probabilité pour les valeurs d'une plage d'être comprises entre deux limites ou égales à une limite inférieure", "ad": "représente la plage des valeurs numériques de x auxquelles sont associées des probabilités!représente une série de probabilités associée aux valeurs de Plage_x, valeurs entre 0 et 1, 0 étant exclu!représente la limite inférieure de la valeur pour laquelle vous recherchez une probabilité!représente la limite supérieure facultative de la valeur. Si omise, PROBABILITE renvoie la probabilité que les valeurs de Plage_x soient égales à Limite_inf"}, "QUARTILE": {"a": "(matrice; quart)", "d": "Renvoie le quartile d’une série de données", "ad": "représente la matrice ou la plage de cellules de valeurs numériques pour laquelle vous recherchez la valeur du quartile!indique quelle valeur à renvoyer: valeur minimale = 0; 1er quartile = 1; quartile moyen = 2; 3e quartile = 3; valeur maximale = 4"}, "QUARTILE.INC": {"a": "(matrice; quart)", "d": "Renvoie le quartile d’une série de données, d’après des valeurs de centile comprises entre 0 et 1 inclus", "ad": "représente la matrice ou la plage de cellules de valeurs numériques pour laquelle vous recherchez la valeur du quartile!indique quelle valeur renvoyer: valeur minimale = 0 ; 1er quartile = 1 ; quartile moyen = 2 ; 3e quartile = 3 ; valeur maximale = 4"}, "QUARTILE.EXC": {"a": "(matrice; quart)", "d": "Renvoie le quartile d’une série de données, d’après des valeurs de centile comprises entre 0 et 1 non compris", "ad": "représente la matrice ou la plage de cellules de valeurs numériques pour laquelle vous recherchez la valeur du quartile!indique quelle valeur renvoyer: valeur minimale = 0 ; 1er quartile = 1 ; quartile moyen = 2 ; 3e quartile = 3 ; valeur maximale = 4"}, "RANK": {"a": "(nombre; référence; [ordre])", "d": "Renvoie le rang d’un nombre dans une liste d’arguments : sa taille est relative aux autres valeurs de la liste", "ad": "est le nombre dont vous voulez connaître le rang!est une matrice, ou une référence à une liste de nombres. Les valeurs non numériques sont ignorées dans la référence!est un numéro : le rang de l’argument dans la liste triée par ordre décroissant = 0 ou omis ; son rang dans la liste triée par ordre croissant = toute valeur différente de zéro"}, "RANK.AVG": {"a": "(nombre; référence; [ordre])", "d": "Renvoie le rang d’un nombre dans une liste d’arguments : sa taille est relative aux autres valeurs de la liste ; si plusieurs valeurs sont associées au même rang, renvoie le rang supérieur de ce jeu de valeurs", "ad": "est le nombre dont vous voulez connaître le rang!est une matrice, ou une référence à une liste de nombres. Les valeurs non numériques sont ignorées dans la référence!est un numéro : le rang de l’argument dans la liste triée par ordre décroissant = 0 ou omis ; son rang dans la liste triée par ordre croissant = toute valeur différente de zéro"}, "RANK.EQ": {"a": "(nombre; référence; [ordre])", "d": "Renvoie le rang d’un nombre dans une liste d’arguments : sa taille est relative aux autres valeurs de la liste ; si plusieurs valeurs sont associées au même rang, renvoie le rang supérieur de ce jeu de valeurs", "ad": "est le nombre dont vous voulez connaître le rang!est une matrice, ou une référence à une liste de nombres. Les valeurs non numériques sont ignorées dans la référence!est un numéro : le rang de l’argument dans la liste triée par ordre décroissant = 0 ou omis ; son rang dans la liste triée par ordre croissant = toute valeur différente de zéro"}, "RSQ": {"a": "(y_connus; x_connus)", "d": "Renvoie la valeur du coefficient de détermination R^2 d’une régression linéaire", "ad": "représente une matrice ou une plage d’observations et peut être un nombre, un nom, une matrice ou une référence qui contient des nombres!représente une matrice ou une plage d’observations et peut être un nombre, un nom, une matrice ou une référence qui contient des nombres"}, "SKEW": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie l'asymétrie d'une distribution : la caractérisation du degré d'asymétrie d'une distribution par rapport à sa moyenne", "ad": "représentent les 1 à 255 nombres, noms, matrices ou références qui contiennent les nombres dont vous voulez calculer l'asymétrie"}, "SKEW.P": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie l'asymétrie d'une distribution basée sur une population : la caractérisation du degré d'asymétrie d'une distribution par rapport à sa moyenne", "ad": "représentent les 1 à 254 nombres, noms, matrices ou références qui contiennent les nombres dont vous voulez calculer l'asymétrie de la population"}, "SLOPE": {"a": "(y_connus; x_connus)", "d": "Renvoie la pente d’une droite de régression linéaire", "ad": "représente une matrice ou une plage de cellules d’observations numériques dépendantes et peut être un nombre, un nom, une matrice ou une références qui contient des nombres!représente la série d’observations indépendantes et peut être un nombre, un nom, une matrice ou une référence qui contient des nombres"}, "SMALL": {"a": "(matrice; k)", "d": "Renvoie la k-ième plus petite valeur d'une série de données", "ad": "représente une matrice ou une plage de données numériques dans laquelle vous recherchez la k-ième plus petite valeur!représente dans la matrice ou la plage, le rang de la donnée à renvoyer, déterminé à partir de la valeur la plus petite"}, "STANDARDIZE": {"a": "(x; espérance; écart_type)", "d": "Renvoie une valeur centrée réduite, depuis une distribution caractérisée par une moyenne et un écart-type", "ad": "représente la valeur à centrer et à réduire!représente l'espérance mathématique de la distribution!représente l'écart-type de la distribution, un nombre positif"}, "STDEV": {"a": "(nombre1; [nombre2]; ...)", "d": "Évalue l’écart-type d’une population en se basant sur un échantillon (ignore les valeurs logiques et le texte de l’échantillon)", "ad": "représentent de 1 à 255 nombres correspondant à un échantillon de population, et peuvent être des nombres ou des références qui contiennent des nombres"}, "STDEV.P": {"a": "(nombre1; [nombre2]; ...)", "d": "Calcule l’écart-type d’une population entière sous forme d’arguments (ignore les valeurs logiques et le texte)", "ad": "représentent de 1 à 255 nombres correspondant à une population et peuvent être des nombres ou des références contenant des nombres"}, "STDEV.S": {"a": "(nombre1; [nombre2]; ...)", "d": "Évalue l’écart-type d’une population en se basant sur un échantillon (ignore les valeurs logiques et le texte de l’échantillon)", "ad": "représentent de 1 à 255 nombres correspondant à un échantillon de population et peuvent être des nombres ou des références qui contiennent des nombres"}, "STDEVA": {"a": "(valeur1; [valeur2]; ...)", "d": "Évalue l'écart-type d'une population en se basant sur un échantillon, incluant les valeurs logiques et le texte. Le texte et les valeurs logiques évalués en tant que FAUX = 0 ; les valeurs logiques évaluées en tant que VRAI = 1", "ad": "représentent de 1 à 255 valeurs correspondant à un échantillon de population et peuvent être des valeurs, des noms, ou des références à des valeurs"}, "STDEVP": {"a": "(nombre1; [nombre2]; ...)", "d": "Calcule l’écart-type d’une population entière sous forme d’arguments (en ignorant les valeurs logiques et le texte)", "ad": "représentent de 1 à 255 nombres correspondant à une population, et peuvent être des nombres ou des références contenant des nombres"}, "STDEVPA": {"a": "(valeur1; [valeur2]; ...)", "d": "Calcule l'écart-type d'une population entière, en incluant les valeurs logiques et le texte. Le texte et les valeurs logiques évalués en tant que FAUX = 0 ; les valeurs logiques évaluées en tant que VRAI = 1", "ad": "représentent les 1 à 255 valeurs correspondant à une population et peuvent être des valeurs, des noms, des matrices ou des références qui contiennent des valeurs"}, "STEYX": {"a": "(y_connus; x_connus)", "d": "Renvoie l’erreur type de la valeur y prévue pour chaque x de la régression", "ad": "représente une matrice ou une plage d’observations dépendantes et peut être un nombre, un nom, une matrice ou une référence qui contient des nombres!représente une matrice ou une plage d’observations indépendantes et peut être un nombre, un nom, une matrice ou une référence qui contient des nombres"}, "TDIST": {"a": "(x; degré<PERSON>_liberté; uni/bilatéral)", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi T de Student", "ad": "représente la valeur numérique à laquelle la distribution doit être évaluée!représente un nombre entier indiquant le nombre de degrés de liberté qui caractérisent la distribution!indique le type de distribution à renvoyer : unilatérale = 1; bilatérale = 2"}, "TINV": {"a": "(probabilité; degrés_liberté)", "d": "Ren<PERSON><PERSON>, pour une probabilité donnée, la valeur inverse bilatérale d’une variable aléatoire suivant une loi T de Student", "ad": "représente la probabilité associée à la loi bilatérale T de Student, c’est-à-dire un nombre entre 0 et 1 inclus!est un entier positif indiquant le nombre de degrés de liberté caractérisant la distribution"}, "T.DIST": {"a": "(x; degrés_liberté; cumulative)", "d": "Renvoie la probabilité unilatérale à gauche d’une variable aléatoire suivant une loi T de Student", "ad": "représente la valeur numérique à laquelle la distribution doit être évaluée!représente un nombre entier indiquant le nombre de degrés de liberté qui caractérisent la distribution!représente une valeur logique : pour la fonction distribution cumulative, utilisez VRAI ; pour la fonction densité de la probabilité, utilisez FAUX"}, "T.DIST.2T": {"a": "(x; deg<PERSON>s_liberté)", "d": "Renvoie la probabilité bilatérale d’une variable aléatoire suivant une loi T de Student", "ad": "représente la valeur numérique à laquelle la distribution doit être évaluée!représente un nombre entier indiquant le nombre de degrés de liberté qui caractérisent la distribution"}, "T.DIST.RT": {"a": "(x; deg<PERSON>s_liberté)", "d": "Renvoie la probabilité unilatérale à droite d’une variable aléatoire suivant une loi T de Student", "ad": "représente la valeur numérique à laquelle la distribution doit être évaluée!représente un nombre entier indiquant le nombre de degrés de liberté qui caractérisent la distribution"}, "T.INV": {"a": "(probabilité; degrés_liberté)", "d": "Ren<PERSON><PERSON>, pour une probabilité donnée, la valeur inverse unilatérale à gauche d’une variable aléatoire suivant une loi T de Student", "ad": "représente la probabilité associée à la loi unilatérale à gauche T de Student, c’est-à-dire un nombre entre 0 et 1 inclus!est un entier positif indiquant le nombre de degrés de liberté caractérisant la distribution"}, "T.INV.2T": {"a": "(probabilité; degrés_liberté)", "d": "Ren<PERSON><PERSON>, pour une probabilité donnée, la valeur inverse bilatérale d’une variable aléatoire suivant une loi T de Student", "ad": "représente la probabilité associée à la loi bilatérale T de Student, c’est-à-dire un nombre entre 0 et 1 inclus!est un entier positif indiquant le nombre de degrés de liberté caractérisant la distribution"}, "T.TEST": {"a": "(matrice1; matrice2; uni/bilatéral; type)", "d": "Renvoie la probabilité associée à un test T de Student", "ad": "représente la première série de données!représente la deuxième série de données!spécifie le nombre de points de distribution à renvoyer : distribution unilatérale = 1; distribution bilatérale = 2!représente le type de test T : observations = 1, variance égale sur deux échantillons (homoscédastique) = 2, variance inégale sur deux échantillons = 3"}, "TREND": {"a": "(y_connus; [x_connus]; [x_nouveaux]; [constante])", "d": "Calcule les valeurs de la courbe de tendance linéaire par la méthode des moindres carrés, appliquée aux valeurs connues", "ad": "représente une plage ou une matrice de la série des valeurs y déjà connues par la relation y = mx + b!représente une plage facultative ou table de valeurs x connues par la relation y = mx + b, une matrice de même dimension que y_connus!représente la plage ou la matrice de la nouvelle série de variables x dont vous voulez que TENDANCE vous donne les valeurs y correspondantes!représente une valeur logique : la constante b est calculée normalement si Constante = VRAI ou omise ; b est égal à 0 si Constante = FAUX"}, "TRIMMEAN": {"a": "(matrice; pourcentage)", "d": "Renvoie la moyenne de la partie intérieure d'une série de valeurs données", "ad": "représente la matrice ou la plage de valeurs à réduire et sur laquelle calculer la moyenne!représente le nombre fractionnaire d'observations à exclure de la série de données"}, "TTEST": {"a": "(matrice1; matrice2; uni/bilatéral; type)", "d": "Renvoie la probabilité associée à un test T de Student", "ad": "représente la première série de données!représente la seconde série de données!indique le type de distribution à renvoyer : unilatérale = 1; bilatérale = 2!représente le type de test t: par paires = 1, 2 exemples, variance égale (homoscédastique) = 2, variance inégale à 2 exemples = 3"}, "VAR": {"a": "(nombre1; [nombre2]; ...)", "d": "Calcule la variance en se basant sur un échantillon (en ignorant les valeurs logiques et le texte de l’échantillon)", "ad": "représentent de 1 à 255 arguments numériques correspondant à un échantillon de population"}, "VAR.P": {"a": "(nombre1; [nombre2]; ...)", "d": "Calcule la variance d’une population entière (ignore les valeurs logiques et le texte de la population)", "ad": "représentent de 1 à 255 arguments numériques qui correspondent à la population"}, "VAR.S": {"a": "(nombre1; [nombre2]; ...)", "d": "Estime la variance en se basant sur un échantillon (ignore les valeurs logiques et le texte de l’échantillon)", "ad": "représentent de 1 à 255 arguments numériques correspondant à un échantillon de population"}, "VARA": {"a": "(valeur1; [valeur2]; ...)", "d": "Estime la variance d'une population en se basant sur un échantillon, texte et valeurs logiques inclus. Le texte et la valeur logique évalués FAUX = 0 ; la valeur logique VRAI = 1", "ad": "représentent de 1 à 255 arguments des valeurs correspondant à un échantillon de population"}, "VARP": {"a": "(nombre1; [nombre2]; ...)", "d": "Calcule la variance d’une population entière (en ignorant les valeurs logiques et le texte de la population)", "ad": "représentent de 1 à 255 arguments numériques qui correspondent à la population"}, "VARPA": {"a": "(valeur1; [valeur2]; ...)", "d": "Calcule la variance d'une population en se basant sur une population entière, en incluant le texte et les valeurs logiques. Le texte et les valeurs logiques FAUX ont la valeur 0 ; la valeur logique VRAI = 1", "ad": "représentent de 1 à 255 arguments numériques correspondant à une population"}, "WEIBULL": {"a": "(x; alpha; bêta; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi de <PERSON>. Consultez l’aide sur l’équation utilisée", "ad": "est la valeur à laquelle la fonction doit être évaluée, un nombre positif!représente un paramètre de la distribution, un nombre positif!représente un paramètre de la distribution, un nombre positif!représente une valeur logique : pour la fonction distribution cumulative, utilisez VRAI ; pour la fonction de probabilité de masse, utilisez FAUX"}, "WEIBULL.DIST": {"a": "(x; alpha; bêta; cumulative)", "d": "Renvoie la probabilité d’une variable aléatoire suivant une loi de <PERSON>", "ad": "est la valeur à laquelle la fonction doit être évaluée, un nombre positif!représente un paramètre de la distribution, un nombre positif!représente un paramètre de la distribution, un nombre positif!représente une valeur logique : pour la fonction distribution cumulative, utilisez VRAI ; pour la fonction de probabilité de masse, utilisez FAUX"}, "Z.TEST": {"a": "(tableau; x; [sigma])", "d": "Renvoie la valeur P unilatérale d’un test Z", "ad": "représente la matrice ou la plage de données par rapport à laquelle tester X!représente la valeur à tester!représente l’écart-type (connu) de population. Si vous ne spécifiez pas, l’écart-type de l’échantillon est utilisé"}, "ZTEST": {"a": "(matrice; x; [sigma])", "d": "Renvoie la valeur unilatérale P du test Z", "ad": "représente la matrice ou la plage de données par rapport à laquelle tester x!représente la valeur à tester!représente l’écart-type (connu) de la population. Si omis, l’écart-type de l’échantillon est utilisé"}, "ACCRINT": {"a": "(émission; prem_coupon; règlement; taux; val_nominale; fréquence; [base]; [méth_calcul])", "d": "Renvoie l'intérêt couru non échu d'un titre dont l'intérêt est perçu périodiquement", "ad": "est la date d'émission, exprimée sous forme de numéro de série!est la date du premier paiement d'intérêt après, exprimée sous forme de numéro de série!est la date de l'escompte exprimée sous forme de numéro de série!est le taux annuel du coupon!est la valeur nominale du titre!est le nombre de coupons payés par an!est la base annuelle utilisée pour le calcul!est une valeur logique: pour l'intérêt couru à partir de la date d'émission = VRAI ou valeur omise ; pour calculer à partir de la date du paiement du dernier coupon = FAUX"}, "ACCRINTM": {"a": "(émission; échéance; taux; val_nominale; [base])", "d": "Renvoie l'intérêt couru non échu d'un titre dont l'intérêt est perçu à l'échéance", "ad": "est la date d'émission, exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est le taux annuel du coupon!est la valeur nominale du titre!est la base annuelle utilisée pour le calcul"}, "AMORDEGRC": {"a": "(coût; date_achat; première_période; val_résiduelle; périodicité; taux; [base])", "d": "Renvoie l'amortissement linéaire proportionnel d'un bien pour chaque période comptable.", "ad": "représente le coût du bien!représente la date d'acquisition du bien!représente la date de fin de la première période!est la valeur résiduelle en fin de vie du bien.!représente la périodicité!représente le taux d'amortissement!base_annuelle : 0 pour une année de 360 jours, 1 pour le nombre de jours réels, 3 pour une année de 365 jours."}, "AMORLINC": {"a": "(coût; date_achat; première_période; val_résiduelle; périodicité; taux; [base])", "d": "Renvoie l'amortissement linéaire proportionnel d'un bien pour chaque période comptable.", "ad": "représente le coût du bien!représente la date d'acquisition du bien!représente la date de fin de la première période!est la valeur résiduelle en fin de vie du bien.!représente la périodicité!représente le taux d'amortissement!base_annuelle : 0 pour une année de 360 jours, 1 pour le nombre de jours réels, 3 pour une année de 365 jours."}, "COUPDAYBS": {"a": "(liquidation; échéance; fréquence; [base])", "d": "Calcule le nombre de jours entre le début de la période de coupon et la date de liquidation", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est le nombre de coupons payés par an!est la base annuelle utilisée pour le calcul"}, "COUPDAYS": {"a": "(liquidation; échéance; fréquence; [base])", "d": "Affiche le nombre de jours pour la période du coupon contenant la date de liquidation", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est le nombre de coupons payés par an!est la base annuelle utilisée pour le calcul"}, "COUPDAYSNC": {"a": "(liquidation; échéance; fréquence; [base])", "d": "Calcule le nombre de jours entre la date de liquidation et la date du coupon suivant la date de liquidation", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est le nombre de coupons payés par an!est la base annuelle utilisée pour le calcul"}, "COUPNCD": {"a": "(liquidation; échéance; fréquence; [base])", "d": "Détermine la date du coupon suivant la date de liquidation", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est le nombre de coupons payés par an!est la base annuelle utilisée pour le calcul"}, "COUPNUM": {"a": "(liquidation; échéance; fréquence; [base])", "d": "Calcule le nombre de coupons entre la date de liquidation et la date d'échéance", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est le nombre de coupons payés par an!est la base annuelle utilisée pour le calcul"}, "COUPPCD": {"a": "(liquidation; échéance; fréquence; [base])", "d": "Calcule la date du coupon précédant la date de liquidation", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est le nombre de coupons payés par an!est la base annuelle utilisée pour le calcul"}, "CUMIPMT": {"a": "(taux; npm; va; période_début; période_fin; type)", "d": "Don<PERSON> le montant cumulé des intérêts payés entre deux périodes données", "ad": "est le taux d'intérêt!est le nombre total de périodes de paiement!est la valeur actuelle!est la première période de paiement à utiliser pour le calcul!est la dernière période du paiement à utiliser pour le calcul!est l'échéancier du paiement"}, "CUMPRINC": {"a": "(taux; npm; va; période_début; période_fin; type)", "d": "Donne le montant cumulé du principal payé entre deux périodes données", "ad": "est le taux d'intérêt!est le nombre total de périodes de paiement!est la valeur actuelle!est la première période de paiement à utiliser pour le calcul!est la dernière période du paiement à utiliser pour le calcul!est l'échéancier du paiement"}, "DB": {"a": "(coût; valeur_rés; du<PERSON>e; période; [mois])", "d": "Renvoie l'amortissement d'un bien durant une période spécifiée en utilisant la méthode de l'amortissement dégressif à taux fixe", "ad": "est le coût initial du bien!est la valeur résiduelle du bien ou valeur du bien à la fin de sa vie!est la durée de vie utile du bien ou le nombre de périodes au cours desquelles le bien est amorti !est la période pour laquelle vous souhaitez calculer l'amortissement. Elle doit être exprimée dans la même unité que la durée de vie du bien!est le nombre de mois de la première année. Si le mois est omis, la valeur par défaut est 12"}, "DDB": {"a": "(co<PERSON>t; valeur_rés; du<PERSON>e; période; [facteur])", "d": "Renvoie l'amortissement d'un bien durant une période spécifiée suivant la méthode de l'amortissement dégressif à taux double ou selon un coefficient à spécifier", "ad": "est le coût initial du bien!est la valeur résiduelle du bien ou valeur du bien à la fin de sa vie!est la vie utile du bien ou le nombre de périodes au cours desquelles le bien est amorti (appelé aussi durée de vie utile du bien)!est la période pour laquelle vous souhaitez calculer l'amortissement. Elle doit être exprimée dans la même unité que la durée de vie du bien!est le taux auquel le solde à amortir décroît. Si cet argument est omis, la valeur par défaut est 2 (amortissement à taux double)"}, "DISC": {"a": "(liquidation; échéance; valeur_nominale; valeur_échéance; [base])", "d": "Calcule le taux d'escompte d'un titre", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est la somme empruntée!est la valeur d'échéance pour 100 € de valeur nominale!est la base annuelle utilisée pour le calcul"}, "DOLLARDE": {"a": "(prix_fraction; fraction)", "d": "Convertit la valeur des cotations boursières de la forme fractionnaire à la forme décimale", "ad": "est un nombre exprimé sous la forme fractionnaire!est un nombre entier utilisé en dénominateur de la fraction"}, "DOLLARFR": {"a": "(prix_décimal; fraction)", "d": "Convertit la valeur des cotations boursières de la forme décimale à la forme fractionnaire", "ad": "est la valeur décimale à convertir!est un nombre entier utilisé en dénominateur de la fraction"}, "DURATION": {"a": "(liquidation; échéance; taux; rendement; fréquence; [base])", "d": "Calcule la durée d'un titre avec des paiements d'intérêts périodiques", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est le taux annuel du coupon!est le taux de rendement du titre!est le nombre de coupons payés par an!est la base annuelle utilisée pour le calcul"}, "EFFECT": {"a": "(taux_nominal; nb_périodes)", "d": "Calcule le taux effectif à partir du taux nominal et du nombre de périodes", "ad": "est le taux d'intérêt nominal!est le nombre de périodes par an"}, "FV": {"a": "(taux; npm; vpm; [va]; [type])", "d": "Calcule la valeur future d'un investissement fondé sur des paiements réguliers et constants, et un taux d'intérêt stable.", "ad": "est le taux d'intérêt par période.Par exemple, utilisez 1/4 de 6 % pour des paiements trimestriels à 6 % d’APR!est le nombre total de remboursements durant l'opération!est le montant du remboursement pour chaque période ; ce montant est inchangé durant toute la durée de l'opération!est la valeur actuelle, ou la somme que représente aujourd'hui une série de paiements futurs. Si omis, Va = 0!est une valeur représentant l'échéancier du paiement : paiement au début de la période = 1 ; paiement à la fin de la période = 0 ou omis"}, "FVSCHEDULE": {"a": "(va; taux)", "d": "Calcule la valeur future d'un investissement en appliquant une série de taux d'intérêt composites", "ad": "est la valeur actuelle!est la matrice des taux d'intérêt à appliquer"}, "INTRATE": {"a": "(liquidation; échéance; investissement; valeur_échéance; [base])", "d": "Affiche le taux d'intérêt d'un titre totalement investi", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est la valeur investie dans le titre!est la somme d'argent reçue à échéance!est la base annuelle utilisée pour le calcul"}, "IPMT": {"a": "(taux; pér; npm; va; [vc]; [type])", "d": "Calcule le montant des intérêts d'un investissement pour une période donnée, fondé sur des paiements périodiques et constants, et un taux d'intérêt stable", "ad": "est le taux d'intérêt par période. Par exemple, utilisez 1/4 de 6 % pour les paiements trimestriels à 6 % d’APR!est la période pour laquelle vous voulez calculer les intérêts, ce nombre doit être compris entre 1 et le nombre total de périodes (Npm)!est le nombre total de remboursements durant l'opération!est la valeur actuelle, ou la somme que représente aujourd'hui une série de paiements futurs!est la valeur future, ou le montant de trésorerie attendu après le dernier paiement. Si omise, Vc = 0!est une valeur logique représentant l'échéancier du paiement : à la fin de la période = 0 ou omis, au début de la période = 1"}, "IRR": {"a": "(valeurs; [estimation])", "d": "Calcule le taux de rentabilité interne d'un investissement pour une succession de trésoreries", "ad": "est une matrice ou une référence à des cellules qui contient des nombres dont vous voulez calculer le taux de rentabilité interne!est le taux que vous estimez être le plus proche du résultat de TRI; 0,1 (10 pourcent) si omis"}, "ISPMT": {"a": "(taux; pér; npm; va)", "d": "Renvoie les intérêts payés pour une période spécifique d'une opération", "ad": "taux d'intérêt par période. Par exemple, utilisez 1/4 de 6 % pour des paiements trimestriels à 6% d’APR! période pour laquelle vous recherchez le montant des intérêts!nombres de périodes de paiement sur la durée totale de l'opération!correspond à la valeur actuelle du total des paiements futurs"}, "MDURATION": {"a": "(liquidation; échéance; taux; rendement; fréquence; [base])", "d": "Renvoie la durée de <PERSON>ley modifiée d'un titre, pour une valeur nominale considérée égale à 100 €", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est le taux annuel du coupon!est le taux de rendement du titre!est le nombre de coupons payés par an!est la base annuelle utilisée pour le calcul"}, "MIRR": {"a": "(valeurs; taux_emprunt; taux_placement)", "d": "Calcule le taux de rentabilité interne pour une série de flux de trésorerie en fonction du coût de l'investissement et de l'intérêt sur le réinvestissement des liquidités", "ad": "est une matrice ou une référence à des cellules qui contiennent des nombres représentant une série de débits (en négatif) et de crédits (en positif) à des dates régulières!est le taux d'intérêt payé pour le financement des besoins de trésorerie!est le taux d'intérêt perçu en cas de placement des excédents de trésorerie"}, "NOMINAL": {"a": "(taux_effectif; nb_périodes)", "d": "Calcule le taux d'intérêt nominal à partir du taux effectif et du nombre de périodes", "ad": "est le taux d'intérêt effectif (composite)!est le nombre de périodes par an"}, "NPER": {"a": "(taux; vpm; va; [vc]; [type])", "d": "Renvoie le nombre de paiements d'un investissement à versements réguliers et taux d'intérêt constant", "ad": "est le taux d'intérêt par période. Par exemple, utilisez 1/4 de 6 % pour des paiements trimestriels à 6 % d’APR!est le montant du remboursement pour chaque période ; ce montant est inchangé durant toute la durée de l'opération!est la valeur actuelle, ou la somme que représente aujourd'hui une série de paiements futurs!est la valeur future, ou le montant de trésorerie attendu après le dernier paiement. Si omis, zéro est utilisé!est une valeur logique : paiement au début de la période = 1 ; paiement à la fin de la période = 0 ou omis"}, "NPV": {"a": "(taux; valeur1; [valeur2]; ...)", "d": "Calcule la valeur actuelle nette d'un investissement s'appuyant sur un taux d'escompte et une série de débits futurs (valeurs négatives) et de crédits (valeurs positives)", "ad": "est le taux d'escompte sur toute la durée de la période!sont de 1 à 254 encaissements et décaissements, répartis de façon égale dans le temps et se produisant à la fin de chaque période"}, "ODDFPRICE": {"a": "(liquidation; échéance; émission; premier_coupon; taux; rendement; valeur_échéance; fréquence; [base])", "d": "Renvoie le prix pour une valeur nominale de 100 € d'un titre dont la première période est irrégulière", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est la date d'émission, exprimée sous forme de numéro de série!est la date du premier coupon du titre, exprimée sous forme de numéro de série!est taux d'intérêt du titre!est le taux de rendement du titre!est la valeur d'échéance!est le nombre de coupons payés par an!est la base annuelle utilisée pour le calcul"}, "ODDFYIELD": {"a": "(liquidation; échéance; émission; premier_coupon; taux; valeur_nominale; valeur_échéance; fréquence; [base])", "d": "Calcule le rendement d'un titre dont la première période est irrégulière", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est la date d'émission, exprimée sous forme de numéro de série!est la date du premier coupon du titre, exprimée sous forme de numéro de série!est taux d'intérêt du titre!est la valeur d'échange du titre!est la valeur d'échéance!est le nombre de coupons payés par an!est la base annuelle utilisée pour le calcul"}, "ODDLPRICE": {"a": "(liquidation; échéance; dernier_coupon; taux; rendement; valeur_échéance; fréquence; [base])", "d": "Renvoie le prix d'un titre d'une valeur nominale de 100 € dont la dernière période est irrégulière", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est la date du dernier coupon du titre, exprimée sous forme de numéro de série!est taux d'intérêt du titre!est le taux de rendement du titre!est la valeur d'échéance!est le nombre de coupons payés par an!est la base annuelle utilisée pour le calcul"}, "ODDLYIELD": {"a": "(liquidation; échéance; dernier_coupon; taux; valeur_nominale; valeur_échéance; fréquence; [base])", "d": "Calcule le rendement d'un titre dont la dernière période est irrégulière", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est la date du dernier coupon du titre, exprimée sous forme de numéro de série!est le taux d'intérêt du titre!est la valeur d'échange du titre!est la valeur d'échéance pour 100 € de valeur nominale!est le nombre de coupons payés par an!est la base annuelle utilisée pour le calcul"}, "PDURATION": {"a": "(taux; va; vc)", "d": "Renvoie le nombre de périodes requises par un investissement pour atteindre une valeur spécifiée", "ad": "est le taux d'intérêt par période.!est la valeur actuelle de l'investissement!est la valeur future souhaitée de l'investissement"}, "PMT": {"a": "(taux; npm; va; [vc]; [type])", "d": "Calcule le montant total de chaque remboursement périodique d'un investissement à remboursements et taux d'intérêt constants", "ad": "est le taux d'intérêt du prêt par période. Par exemple, utilisez 1/4 de 6 % pour des paiements trimestriels à 6 % d’APR!est le nombre total de versements pour rembourser le prêt!est la valeur actuelle, c'est-à-dire la valeur présente du total des remboursements futurs!est la valeur future, ou montant de trésorerie attendu après le dernier paiement, 0 (zéro) si omis!est une valeur logique : paiement au début de la période = 1 ; paiement à la fin de la période = 0 ou omis"}, "PPMT": {"a": "(taux; pér; npm; va; [vc]; [type])", "d": "Calcule la part de remboursement du principal d'un emprunt, fondée sur des remboursements et un taux d'intérêt constants", "ad": "est le taux d'intérêt par période. Par exemple, utilisez 1/4 de 6 % pour les paiements trimestriels à 6 % d’APR!indique la période et doit être compris entre 1 et le nombre total de périodes de paiement durant l'opération!est le nombre total de remboursements durant l'opération!est la valeur actuelle, c'est-à-dire la valeur présente du total des remboursements futurs!est la valeur future, ou le montant de trésorerie attendu après le dernier remboursement!est une valeur logique : paiement au début de la période = 1 ; paiement à la fin de la période = 0 ou omis"}, "PRICE": {"a": "(liquidation; échéance; taux; rendement; valeur_échéance; fréquence; [base])", "d": "Renvoie le prix d'un titre rapportant des intérêts périodiques, pour une valeur nominale de 100 €", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est le taux annuel du coupon!est le taux de rendement du titre!est la valeur d'échéance!est le nombre de coupons payés par an!est la base annuelle utilisée pour le calcul"}, "PRICEDISC": {"a": "(liquidation; échéance; taux; valeur_échéance; [base])", "d": "Renvoie la valeur d'encaissement d'un escompte commercial, pour une valeur nominale de 100 €", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est le taux d'escompte!est la valeur d'échéance!est la base annuelle utilisée pour le calcul"}, "PRICEMAT": {"a": "(liquidation; échéance; émission; taux; rendement; [base])", "d": "Renvoie le prix d'un titre dont la valeur nominale est 100 € et qui rapporte des intérêts à l'échéance", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est la date d'émission, exprimée sous forme de numéro de série!est le taux d'intérêt du titre à la date d'émission!est le taux de rendement du titre!est la base annuelle utilisée pour le calcul"}, "PV": {"a": "(taux; npm; vpm; [vc]; [type])", "d": "Calcule la valeur actuelle d'un investissement : la valeur actuelle du montant total d'une série de remboursements futurs", "ad": "est le taux d'intérêt par période. Par exemple, utiliser 1/4 de 6 % pour des paiements trimestriels à 6 % d’APR!est le nombre total de remboursements durant l'opération!est le montant du remboursement pour chaque période et ce montant ne peut pas être changé durant toute la durée de l'opération!est la valeur future, c'est-à-dire le montant que vous voulez obtenir après le dernier remboursement!est une valeur logique : paiement au début de la période = 1 ; paiement à la fin de la période = 0 ou omis"}, "RATE": {"a": "(npm; vpm; va; [vc]; [type]; [estimation])", "d": "Calcule le taux d'intérêt par période d'un prêt ou d'un investissement. Par exemple, utilisez 1/4 de 6 % pour des paiements trimestriels à 6 % d’APR", "ad": "est le nombre total de périodes de remboursement durant l'opération!est le montant de chaque remboursement périodique et ce montant ne peut pas être changé durant toute la durée de l'opération!est la valeur actuelle, c'est-à-dire la valeur présente du total des remboursements futurs!est la valeur future, c'est-à-dire le montant de la trésorerie attendu après le dernier remboursement. Si omise, utilise VC = 0!est une valeur logique : paiement au début de la période = 1 ; paiement à la fin de la période = 0 ou omis!est votre estimation du taux ; si omise, Estimation = 0,1 (10 %)"}, "RECEIVED": {"a": "(liquidation; échéance; investissement; taux; [base])", "d": "Renvoie la valeur nominale à l'échéance d'un titre entièrement investi", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est la valeur investie dans le titre!est le taux d'escompte!est la base annuelle utilisée pour le calcul"}, "RRI": {"a": "(npm; va; vc)", "d": "Renvoie un taux d'intérêt équivalent pour la croissance d'un investissement", "ad": "est le nombre de périodes pour l'investissement!est la valeur actuelle de l'investissement!est la valeur future de l'investissement"}, "SLN": {"a": "(coût; valeur_rés; durée)", "d": "Calcule l'amortissement linéaire d'un bien pour une période donnée", "ad": "est le coût initial du bien!est la valeur résiduelle du bien ou valeur du bien à la fin de sa vie!est la durée de vie utile du bien ou le nombre de périodes au cours desquelles le bien est amorti"}, "SYD": {"a": "(coût; valeur_rés; du<PERSON>e; période)", "d": "Calcule l'amortissement d'un bien pour une période donnée sur la base de la méthode américaine Sum-of-Years Digits", "ad": "est le coût initial du bien!est la valeur résiduelle du bien ou valeur du bien à la fin de sa vie!est la durée de vie utile du bien ou le nombre de périodes au cours desquelles le bien est amorti !est la période et doit être exprimée dans la même unité que la durée de vie"}, "TBILLEQ": {"a": "(liquidation; échéance; taux_escompte)", "d": "Renvoie le taux d'escompte rationnel d'un bon du trésor", "ad": "est la date de l'emprunt, exprimée sous forme de numéro de série!est la date d'échéance de l'emprunt, exprimée sous forme de numéro de série!est le taux du bon du Trésor"}, "TBILLPRICE": {"a": "(liquidation; échéance; taux_escompte)", "d": "Renvoie le prix d'un bon du trésor d'une valeur nominale de 100 €", "ad": "est la date de l'emprunt, exprimée sous forme de numéro de série!est la date d'échéance de l'emprunt, exprimée sous forme de numéro de série!est le taux du bon du Trésor"}, "TBILLYIELD": {"a": "(liquidation; échéance; valeur_nominale)", "d": "Calcule le taux de rendement d'un bon du trésor", "ad": "est la date de l'emprunt, exprimée sous forme de numéro de série!est la date d'échéance de l'emprunt, exprimée sous forme de numéro de série!est la valeur acquise pour un emprunt de 100 €"}, "VDB": {"a": "(coût; valeur_rés; du<PERSON>e; période_début; période_fin; [facteur]; [valeur_log])", "d": "Calcule l'amortissement d'un bien pour toute période que vous spécifiez, même partielle, en utilisant la méthode américaine Double-declining Balance ou toute autre méthode que vous spécifierez", "ad": "est le coût initial du bien!est la valeur résiduelle du bien ou valeur du bien à la fin de sa vie!est la durée de vie utile du bien ou le nombre de périodes au cours desquelles le bien est amorti !est le début de la période de calcul de l'amortissement, exprimée dans la même unité que la durée de vie!est le terme de la période de calcul de l'amortissement, exprimée dans la même unité que la durée de vie!est le taux auquel l'amortissement décroît, 2 par défaut!basculer vers la méthode de l'amortissement linéaire quand l'amortissement linéaire est supérieur à celui obtenu par la méthode de l'amortissement dégressif = FAUX ou omis; ne pas basculer = VRAI"}, "XIRR": {"a": "(valeurs; dates; [estimation])", "d": "Calcule le taux de rentabilité interne d'un ensemble de paiements", "ad": "est la série des paiements selon un calendrier!est le calendrier de dates des paiements!est le taux que vous estimez être le plus proche du résultat de TRI.PAIEMENTS"}, "XNPV": {"a": "(taux; valeurs; dates)", "d": "Donne la valeur actuelle nette d'un ensemble de paiements planifiés", "ad": "est le taux d'intérêt à appliquer!est la série des paiements selon un calendrier!est le calendrier de dates des paiements"}, "YIELD": {"a": "(liquidation; échéance; taux; valeur_nominale; valeur_rachat; fréquence; [base])", "d": "Calcule le rendement d'un titre rapportant des intérêts périodiquement", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est le taux annuel du coupon!est la somme empruntée!est la valeur d'échéance pour 100 € de valeur nominale!est le nombre de coupons payés par an!est la base annuelle utilisée pour le calcul"}, "YIELDDISC": {"a": "(liquidation; échéance; valeur_nominale; valeur_rachat; [base])", "d": "Calcule le taux de rendement d'un titre escompté, tel qu'un bon du trésor", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est la somme empruntée!est la valeur d'échéance pour 100 € de valeur nominale!est la base annuelle utilisée pour le calcul"}, "YIELDMAT": {"a": "(liquidation; échéance; émission; taux; valeur_nominale; [base])", "d": "Renvoie le rendement annuel d'un titre qui rapporte des intérêts à l'échéance", "ad": "est la date de l'escompte exprimée sous forme de numéro de série!est la date d'échéance, exprimée sous forme de numéro de série!est la date d'émission, exprimée sous forme de numéro de série!est le taux d'intérêt du titre à la date d'émission!est la somme empruntée!est la base annuelle utilisée pour le calcul"}, "ABS": {"a": "(nombre)", "d": "Renvoie la valeur absolue d'un nombre, un nombre sans son signe.", "ad": "est le nombre réel dont vous voulez obtenir la valeur absolue"}, "ACOS": {"a": "(nombre)", "d": "Renvoie l'arccosinus d'un nombre exprimé en radians, de 0 à pi. L'arccosinus est l'angle dont le cosinus est ce nombre", "ad": "est le cosinus de l'angle que vous voulez obtenir et doit être compris entre -1 et 1"}, "ACOSH": {"a": "(nombre)", "d": "Renvoie le cosinus hyperbolique inverse d'un nombre", "ad": "est un nombre réel supérieur ou égal à 1"}, "ACOT": {"a": "(nombre)", "d": "Renvoie l'arccotangente d'un nombre, en radians, dans la plage 0 à Pi", "ad": "est la cotangente de l'angle recherché"}, "ACOTH": {"a": "(nombre)", "d": "Renvoie la cotangente hyperbolique inverse d'un nombre", "ad": "est la cotangente hyperbolique de l'angle recherché"}, "AGGREGATE": {"a": "(no_fonction; options; réf1; ...)", "d": "Renvoie un agrégat dans une liste ou une base de données", "ad": "représente le numéro 1 à 19 qui spécifie la fonction de synthèse pour l’agrégat.!représente le numéro 0 à 7 qui spécifie les valeurs à ignorer pour l’agrégat!représente la matrice ou la plage de données numériques sur laquelle calculer l’agrégat!indique la position dans la matrice ; il s’agit de la k-ième plus grande valeur, k-ième plus petite, k-ième centile ou k-ième quartile.!représente le numéro 1 à 19 qui spécifie la fonction de synthèse pour l’agrégat.!représente le numéro 0 à 7 qui spécifie les valeurs à ignorer pour l’agrégat!représentent 1 à 253 plages ou références dont vous voulez obtenir l’agrégat"}, "ARABIC": {"a": "(texte)", "d": "Convertit un chiffre romain en un chiffre arabe", "ad": "est le chiffre romain à convertir"}, "ASC": {"a": "(texte)", "d": "En ce qui concerne les langues à jeu de caractères codés sur deux octets (DBCS, Double-byte Character Set), la fonction remplace les caractères à pleine chasse (codés sur deux octets) en caractères à demi-chasse (codés sur un octet)", "ad": "est le texte que vous souhaitez modifier"}, "ASIN": {"a": "(nombre)", "d": "Renvoie l'arcsinus d'un nombre en radians, dans la plage -Pi/2 à +Pi/2", "ad": "est le sinus de l'angle souhaité et doit être compris entre -1 et 1"}, "ASINH": {"a": "(nombre)", "d": "Renvoie le sinus hyperbolique inverse d'un nombre", "ad": "est un nombre réel supérieur ou égal à 1"}, "ATAN": {"a": "(nombre)", "d": "Renvoie l'arctangente d'un nombre en radians, dans la plage -Pi/2 à Pi/2", "ad": "est la tangente de l'angle voulu"}, "ATAN2": {"a": "(no_x; no_y)", "d": "Renvoie l'arctangente des coordonnées x et y, en radians entre -Pi et Pi, -Pi étant exclu", "ad": "est la coordonnée x du point!est la coordonnée y du point"}, "ATANH": {"a": "(nombre)", "d": "Renvoie la tangente hyperbolique inverse d'un nombre", "ad": "est un nombre réel compris entre -1 et 1 (-1 et 1 exclus)"}, "BASE": {"a": "(nombre; base; [longueur_mini])", "d": "Convertit un nombre en représentation textuelle avec la base donnée", "ad": "est le nombre à convertir!est la base dans laquelle vous voulez convertir le nombre!est la longueur minimale de la chaîne renvoyée. Sinon, des zéros non significatifs ne sont pas ajoutés"}, "CEILING": {"a": "(nombre; précision)", "d": "Arrondit un nombre au multiple le plus proche de l’argument précision en s’éloignant de zéro", "ad": "représente la valeur que vous voulez arrondir!représente le multiple auquel vous voulez arrondir"}, "CEILING.MATH": {"a": "(nombre; [précision]; [mode])", "d": "Arrondit un nombre à l'entier ou au multiple le plus proche de l'argument précision en s'éloignant de zéro", "ad": "représente la valeur que vous voulez arrondir!représente le multiple auquel vous voulez arrondir!lorsque cette fonction est fournie et différente de zéro, elle arrondira en s'éloignant de zéro"}, "CEILING.PRECISE": {"a": "(x; [précision])", "d": "Arrondit le nombre à l'excès à l'entier ou au multiple significatif le plus proche.", "ad": "représente la valeur que vous voulez arrondir!représente le multiple auquel vous voulez arrondir"}, "COMBIN": {"a": "(nombre_éléments; nb_éléments_choisis)", "d": "Renvoie le nombre de combinaisons que l'on peut former avec un nombre donné d'éléments. Consultez l'aide pour l'équation utilisée", "ad": "représente le nombre total d'éléments!représente le nombre d'éléments de chaque combinaison"}, "COMBINA": {"a": "(nombre; nombre_choisi)", "d": "Renvoie le nombre de combinaisons avec répétitions que l'on peut former avec un nombre donné d'éléments", "ad": "représente le nombre total d'éléments!représente le nombre d'éléments de chaque combinaison"}, "COS": {"a": "(nombre)", "d": "Renvoie le cosinus d'un angle", "ad": "est l'angle exprimé en radians dont vous cherchez le cosinus"}, "COSH": {"a": "(nombre)", "d": "Renvoie le cosinus hyperbolique d'un nombre", "ad": "est un nombre réel"}, "COT": {"a": "(nombre)", "d": "Renvoie la cotangente d'un angle", "ad": "est l'angle exprimé en radians dont vous voulez obtenir la cotangente"}, "COTH": {"a": "(nombre)", "d": "Renvoie la cotangente hyperbolique d'un nombre", "ad": "est l'angle exprimé en radians dont vous voulez obtenir la cotangente hyperbolique"}, "CSC": {"a": "(nombre)", "d": "Renvoie la cosécante d'un angle", "ad": "est l'angle exprimé en radians dont vous voulez obtenir la cosécante"}, "CSCH": {"a": "(nombre)", "d": "Renvoie la cosécante hyperbolique d'un angle", "ad": "est l'angle exprimé en radians dont vous voulez obtenir la cosécante hyperbolique"}, "DECIMAL": {"a": "(nombre; base)", "d": "Convertit la représentation textuelle d'un nombre dans une base donnée en un nombre décimal", "ad": "est le nombre à convertir!est la base du nombre que vous convertissez"}, "DEGREES": {"a": "(angle)", "d": "Convertit des radians en degrés", "ad": "représente l'angle en radians à convertir"}, "ECMA.CEILING": {"a": "(x; précision)", "d": "Arrondit le nombre au multiple le plus proche de l'argument de précision.", "ad": "représente la valeur que vous voulez arrondir!représente le multiple auquel vous voulez arrondir"}, "EVEN": {"a": "(nombre)", "d": "Arrondit un nombre au nombre entier pair le plus proche en s'éloignant de zéro", "ad": "est la valeur à arrondir"}, "EXP": {"a": "(nombre)", "d": "<PERSON><PERSON> e (2,718) élevé à la puissance spécifiée", "ad": "est l'exposant de la base e. La constante e égale 2,71828182845904, la base du logarithme népérien"}, "FACT": {"a": "(nombre)", "d": "Renvoie la factorielle d'un nombre, égale à 1*2*3*...*nombre", "ad": "est un nombre positif dont vous voulez obtenir la factorielle"}, "FACTDOUBLE": {"a": "(nombre)", "d": "Renvoie la factorielle double d'un nombre", "ad": "est un nombre dont vous voulez obtenir la factorielle double"}, "FLOOR": {"a": "(nombre; précision)", "d": "Arrondit un nombre à l’entier ou au multiple le plus proche de l’argument précision", "ad": "est la valeur à arrondir!représente le multiple auquel vous voulez arrondir. Le nombre et le degré de précision doivent être soit positifs tous les deux, soit tous les deux négatifs"}, "FLOOR.PRECISE": {"a": "(x; [précision])", "d": "Arrondit le nombre par défaut à l'entier ou au multiple significatif le plus proche.", "ad": "représente la valeur que vous voulez arrondir!représente le multiple auquel vous voulez arrondir"}, "FLOOR.MATH": {"a": "(nombre; [précision]; [mode])", "d": "Arrondit un nombre à l'entier ou au multiple le plus proche de l'argument précision en tendant vers zéro", "ad": "représente la valeur que vous voulez arrondir!représente le multiple auquel vous voulez arrondir!lorsque cette fonction est fournie et différente de zéro, elle arrondira en tendant vers zéro"}, "GCD": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie le plus grand dénominateur commun", "ad": "représentent de 1 à 255 valeurs"}, "INT": {"a": "(nombre)", "d": "Arrondit un nombre à l'entier immédiatement inférieur", "ad": "est le nombre réel que vous voulez arrondir à l'entier immédiatement inférieur"}, "ISO.CEILING": {"a": "(nombre; [précision])", "d": "Renvoie un nombre arrondi au nombre entier le plus proche ou au multiple le plus proche de l’argument précision en s’éloignant de zéro. Quel que soit son signe, ce nombre est arrondi à l’entier supérieur. <PERSON><PERSON><PERSON><PERSON>, si le nombre ou l’argument précision est égal à zéro, zéro est retourné.", "ad": "représente la valeur que vous voulez arrondir!représente le multiple auquel vous voulez arrondir"}, "LCM": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie le plus petit dénominateur commun", "ad": "représentent de 1 à 255 valeurs dont vous voulez obtenir le plus petit multiple commun "}, "LN": {"a": "(nombre)", "d": "Donne le logarithme népérien d'un nombre", "ad": "est le nombre réel positif dont vous voulez obtenir le logarithme népérien"}, "LOG": {"a": "(nombre; [base])", "d": "Donne le logarithme d'un nombre dans la base spécifiée", "ad": "est le nombre réel positif dont vous voulez obtenir le logarithme!est la base du logarithme; 10 si omis"}, "LOG10": {"a": "(nombre)", "d": "Calcule le logarithme en base 10 d'un nombre", "ad": "est le nombre réel positif dont vous voulez obtenir le logarithme en base 10"}, "MDETERM": {"a": "(matrice)", "d": "Ren<PERSON>ie le déterminant d'une matrice", "ad": "est une matrice carrée numérique, soit une plage de cellules, soit une constante de matrice"}, "MINVERSE": {"a": "(matrice)", "d": "Renvoie la matrice inversée de la matrice enregistrée dans un tableau", "ad": "est une matrice carrée numérique, soit une plage de cellules, soit une constante de matrice"}, "MMULT": {"a": "(matrice1; matrice2)", "d": "Calcule le produit de deux matrices, sous forme d'une matrice avec le même nombre de ligne que la matrice1 et de colonnes que la matrice2", "ad": "est la première matrice de nombres à multiplier, et doit avoir autant de colonnes que Matrice2 a de lignes"}, "MOD": {"a": "(nombre; diviseur)", "d": "Renvoie le reste d'une division", "ad": "est le nombre dont vous voulez obtenir le reste de la division!est le nombre par lequel vous voulez diviser l'argument nombre"}, "MROUND": {"a": "(nombre; multiple)", "d": "Donne l'arrondi d'un nombre au multiple spécifié", "ad": "est la valeur à arrondir!est le multiple auquel le nombre doit être arrondi"}, "MULTINOMIAL": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie le polynôme à plusieurs variables d'un ensemble de nombres", "ad": "représentent de 1 à 255 valeurs dont vous voulez obtenir le polynôme à plusieurs variables"}, "MUNIT": {"a": "(dimension)", "d": "Renvoie la matrice d'unités pour la dimension spécifiée", "ad": "est un nombre entier spécifiant la dimension de la matrice d'unités que vous voulez renvoyer"}, "ODD": {"a": "(nombre)", "d": "Arrondit un nombre au nombre entier impair de valeur absolue immédiatement supérieure", "ad": "est la valeur à arrondir"}, "PI": {"a": "()", "d": "Renvoie la valeur de pi, 3,14159265358979 avec une précision de 15 chiffres", "ad": ""}, "POWER": {"a": "(nombre; puissance)", "d": "Renvoie la valeur du nombre élevé à une puissance", "ad": "est le nombre à élever à la puissance, n'importe quel nombre réel!est la puissance à laquelle le nombre est élevé"}, "PRODUCT": {"a": "(nombre1; [nombre2]; ...)", "d": "Donne le produit de la multiplication de tous les nombres donnés comme arguments", "ad": "représentent de 1 à 255 nombres, valeurs logiques, ou transcriptions textuelles des nombres que vous voulez multiplier"}, "QUOTIENT": {"a": "(numérateur; dénominateur)", "d": "Renvoie la partie entière du résultat d'une division", "ad": "est le dividende!est le diviseur"}, "RADIANS": {"a": "(angle)", "d": "Convertit des degrés en radians", "ad": "désigne l'angle en degrés que vous voulez convertir"}, "RAND": {"a": "()", "d": "Renvoie un nombre aléatoire de distribution normale supérieur ou égal à 0 et inférieur à 1 (différent à chaque calcul)", "ad": ""}, "RANDARRAY": {"a": "([lignes]; [colonnes]; [min]; [max]; [entières])", "d": "Retourne un tableau de nombres aléatoires", "ad": "le nombre de lignes dans le tableau retourné!le nombre de colonnes dans le tableau retourné!le nombre minimal souhaité retourné!retourné par le nombre maximal que vous souhaitez! retourner un entier ou une valeur décimale. TRUE pour un entier, la valeur FALSE pour un nombre décimal"}, "RANDBETWEEN": {"a": "(min; max)", "d": "Renvoie un nombre aléatoire entre les nombres que vous spécifiez", "ad": "est la valeur minimale que peut donner la fonction ALEA.ENTRE.BORNES!est la valeur maximale que peut donner la fonction ALEA.ENTRE.BORNES"}, "ROMAN": {"a": "(nombre; [type])", "d": "Convertit un chiffre arabe en chiffre romain sous forme de texte", "ad": "est le chiffre arabe que vous voulez convertir!est le numéro qui détermine le type de chiffre romain que doit renvoyer la fonction."}, "ROUND": {"a": "(nombre; no_chiffres)", "d": "Arrondit un nombre au nombre de chiffres indiqué", "ad": "est le nombre à arrondir!est le nombre de chiffres auquel vous voulez arrondir l'argument nombre. Arrondis négatifs à la gauche de la décimale; de zéro à l'entier le plus proche"}, "ROUNDDOWN": {"a": "(nombre; no_chiffres)", "d": "Arrondit un nombre en tendant vers zéro", "ad": "est un nombre réel que vous voulez arrondir!est le nombre de chiffres auxquels vous voulez arrondir. Arrondir négativement à la gauche de la décimale; z<PERSON><PERSON> ou omis, arrondis au nombre entier le plus proche"}, "ROUNDUP": {"a": "(nombre; no_chiffres)", "d": "Arrondit un nombre en s'éloignant de zéro", "ad": "est un nombre réel que vous voulez arrondir!est le nombre de chiffres auxquels vous voulez arrondir. Arrondir négativement à la gauche de la décimale; z<PERSON><PERSON> ou omis, arrondis au nombre entier le plus proche"}, "SEC": {"a": "(nombre)", "d": "Renvoie la sécante d'un angle", "ad": "est l'angle exprimé en radians dont vous voulez obtenir la sécante"}, "SECH": {"a": "(nombre)", "d": "Renvoie la sécante hyperbolique d'un angle", "ad": "est l'angle exprimé en radians dont vous voulez obtenir la sécante hyperbolique"}, "SERIESSUM": {"a": "(x; n; m; coefficients)", "d": "Renvoie la somme d'une série géométrique s'appuyant sur la formule", "ad": "est la variable de la série!est la puissance de départ de la variable x de la série!est le pas géométrique de la série!est la liste des coefficients de la série"}, "SIGN": {"a": "(nombre)", "d": "Don<PERSON> le signe d'un nombre\\x00a0: 1 si le nombre est zéro, ou -1 si le nombre est négatif", "ad": "est un nombre réel"}, "SIN": {"a": "(nombre)", "d": "Renvoie le sinus d'un nombre", "ad": "est l'angle exprimé en radians duquel vous voulez connaître le sinus. Degrés * pi()/180 = radians"}, "SINH": {"a": "(nombre)", "d": "Renvoie le sinus hyperbolique d'un nombre", "ad": "est un nombre réel"}, "SQRT": {"a": "(nombre)", "d": "Donne la racine carrée d'un nombre", "ad": "est le nombre dont vous voulez obtenir la racine carrée"}, "SQRTPI": {"a": "(nombre)", "d": "<PERSON><PERSON> la racine carrée du produit (nombre * pi)", "ad": "est le nombre auquel vous voulez multiplier PI"}, "SUBTOTAL": {"a": "(no_fonction; réf1; ...)", "d": "Renvoie un sous-total dans une liste ou une base de données", "ad": "représente un nombre de 1 à 11 déterminant quelle fonction de synthèse utiliser pour calculer le sous-total.!représentent les 1 à 254 plages ou références pour lesquelles vous voulez calculer le sous-total"}, "SUM": {"a": "(nombre1; [nombre2]; ...)", "d": "Calcule la somme des nombres dans une plage de cellules", "ad": "représentent de 1 à 255 arguments dont vous voulez calculer la somme. Les valeurs logiques et le texte sont ignorés dans les cellules, même s'ils sont tapés en tant qu'arguments"}, "SUMIF": {"a": "(plage; critère; [somme_plage])", "d": "Additionne des cellules spécifiées selon un certain critère", "ad": "représente la plage des cellules sur lesquelles vous voulez appliquer la fonction!représente la condition ou le critère, sous forme de nombre, d'expression ou de texte, définissant les cellules à additionner!représente les cellules qui seront effectivement additionnées. Par <PERSON><PERSON><PERSON><PERSON>, les cellules dans la plage seront utilisées"}, "SUMIFS": {"a": "(plage_somme; plage_critères; critères; ...)", "d": "Additionne les cellules indiquées par un ensemble de conditions ou de critères donné", "ad": "cellules à additionner.!représente la plage de cellules servant de base à l'évaluation!représente la condition ou les critères sous la forme d'un nombre, d'une expression ou de texte définissant quelles cellules doivent être additionnées"}, "SUMPRODUCT": {"a": "(matrice1; [matrice2]; [matrice3]; ...)", "d": "Donne la somme des produits des plages ou matrices correspondantes", "ad": "représentent de 2 à 255 matrices dont vous voulez multiplier puis ajouter les composants. Toutes les matrices doivent être de mêmes dimensions"}, "SUMSQ": {"a": "(nombre1; [nombre2]; ...)", "d": "Renvoie la somme des carrés des arguments. Les arguments peuvent être des nombres, des matrices, des noms ou des références à des cellules qui contiennent des nombres", "ad": "représentent de 1 à 255 nombres, matrices, noms, ou références à des matrices dont vous recherchez la somme des carrés"}, "SUMX2MY2": {"a": "(matrice_x; matrice_y)", "d": "Calcule la différence entre les carrés des nombres correspondants dans deux plages ou matrices, puis renvoie la somme des différences. Consultez l'aide sur l'équation utilisée", "ad": "est la première plage ou matrice de valeurs et peut être un nombre, un nom, une matrice, ou une référence qui contient des nombres!est la seconde plage ou matrice de valeurs et peut être un nombre, un nom, une matrice, ou une référence qui contient un nombre"}, "SUMX2PY2": {"a": "(matrice_x; matrice_y)", "d": "Calcule la somme des carrés des nombres correspondants dans deux plages ou matrices, puis renvoie le total de l'addition des sommes. Consultez l'aide sur l'équation utilisée", "ad": "est la première plage ou matrice de valeurs et peut être un nombre, un nom, une matrice, ou une référence qui contient des nombres!est la seconde plage ou matrice de valeurs et peut être un nombre, un nom, une matrice, ou une référence qui contient un nombre"}, "SUMXMY2": {"a": "(matrice_x; matrice_y)", "d": "Renvoie la somme des carrés des différences entre les valeurs correspondantes de deux matrices. Consultez l'aide sur l'équation utilisée", "ad": "est la première matrice ou plage de valeurs et peut être un nombre, un nom, une matrice, ou une référence qui contient des nombres!est la seconde matrice ou plage de valeurs et peut être un nombre, un nom, une matrice, ou une référence qui contient des nombres"}, "TAN": {"a": "(nombre)", "d": "Renvoie la tangente d'un nombre", "ad": "est l'angle exprimé en radians duquel vous voulez connaître la tangente. Degrés * pi()/180 = radians"}, "TANH": {"a": "(nombre)", "d": "Renvoie la tangente hyperbolique d'un nombre", "ad": "est un nombre réel. Consultez l'aide pour l'équation utilisée"}, "TRUNC": {"a": "(nombre; [no_chiffres])", "d": "Renvoie la partie entière d'un nombre en enlevant la partie décimale ou fractionnaire du nombre", "ad": "est le nombre à tronquer!est un nombre qui indique la précision de la troncature, 0 (zéro) par défaut"}, "ADDRESS": {"a": "(no_lig; no_col; [no_abs]; [a1]; [feuille_texte])", "d": "Crée une référence de cellule sous forme de texte, en fonction des numéros de lignes et colonnes spécifiées", "ad": "est le numéro de la ligne à utiliser dans la référence de cellule: numéro_cellule = 1 pour la cellule 1!est le numéro de la colonne à utiliser dans la référence de cellule. Par exemple, numéro_colonne = 4 pour la colonne D!spécifie le type de référence à renvoyer: absolue = 1; ligne absolue/colonne relative = 2; ligne relative/colonne absolue = 3; relative = 4!est une valeur logique qui spécifie le style de référence: style A1 = 1 ou VRAI; style L1C1 = 0 ou FAUX!est le texte donnant le nom de la feuille de calcul à utiliser comme référence externe"}, "CHOOSE": {"a": "(no_index; valeur1; [valeur2]; ...)", "d": "Choisit une valeur ou une action à réaliser dans une liste de valeurs, en fonction d'un numéro d'index", "ad": "spécifie l'argument valeur sélectionné. No_index doit être compris entre 1 et 254, ou bien être une formule ou une référence à un numéro entre 1 et 254!représentent de 1 à 254 nombres, références de cellules, noms définis, formules, fonctions, ou arguments sous forme de texte parmi lesquels la fonction CHOISIR sélectionne"}, "COLUMN": {"a": "([réf<PERSON><PERSON><PERSON>])", "d": "Renvoie le numéro de colonne d'une référence", "ad": "est la cellule ou la plage de cellules dont vous voulez obtenir le numéro de colonne. Sinon, la cellule contenant la fonction COLONNE est utilisée"}, "COLUMNS": {"a": "(tableau)", "d": "Renvoie le nombre de colonnes d'une matrice ou d'une référence", "ad": "est un tableau, une formule matricielle ou la référence d'une plage de cellules dont vous voulez obtenir le nombre de colonnes"}, "FORMULATEXT": {"a": "(référence)", "d": "Renvoie une formule en tant que chaîne", "ad": "est une référence à une formule"}, "HLOOKUP": {"a": "(valeur_cherchée; tableau; no_index_lig; [valeur_proche])", "d": "Cherche une valeur dans la première ligne d'une matrice de valeurs ou d'un tableau et renvoie la valeur de la même colonne à partir d'une ligne spécifiée", "ad": "représente la valeur recherchée dans le premier rang du tableau. Il peut s'agir d'une valeur, d'une référence ou d'un texte!est un tableau de données (texte, nombres, valeurs logiques) dans lequel est exécutée la recherche de la valeur. L'argument table_matrice peut être une référence à une plage ou à un nom de plage de données!est le numéro de la ligne de l'argument table_matrice dont la valeur correspondante est renvoyée. La première ligne des valeurs dans la table est la ligne 1!est une valeur logique: pour trouver la valeur la plus proche dans la ligne du haut (tri par ordre croissant) = VRAI ou omis; pour trouver une valeur exactement identique = FAUX"}, "HYPERLINK": {"a": "(emplacement_lien; [nom_convivial])", "d": "Crée un raccourci pour ouvrir un document enregistré sur votre disque dur, un serveur de réseau, ou sur Internet", "ad": "représente le chemin et le nom du document à ouvrir, un emplacement de disque dur, une adresse UNC ou encore un chemin URL!représente le nombre, le texte ou la fonction qui s'affiche dans la cellule. Si omis, la cellule affiche le texte de l'argument Emplacement_lien"}, "INDEX": {"a": "(matrice; no_lig; [no_col]!réf; no_lig; [no_col]; [no_zone])", "d": "Renvoie une valeur ou la référence de la cellule à l'intersection d'une ligne et d'une colonne particulières, dans une plage données", "ad": "est une plage de cellules ou une constante de matrice.!sélectionne la ligne de la matrice ou de la référence à partir de laquelle la valeur doit être renvoyée. Si cet argument est omis, no_col est requis!sélectionne la colonne de la matrice ou de la référence à partir de laquelle une référence doit être renvoyée. Si cet argument est omis, no_ligne est requis!est une référence à une ou plusieurs plages de cellules!sélectionne la ligne de la matrice ou de la référence à partir de laquelle la valeur doit être renvoyée. Si cet argument est omis, no_col est requis!sélectionne la colonne de la matrice ou de la référence à partir de laquelle une valeur doit être renvoyée. Si cet argument est omis, no_lig est requis!sélectionne une plage de la référence à partir de laquelle une valeur doit être renvoyée. La première zone sélectionnée o"}, "INDIRECT": {"a": "(réf_texte; [a1])", "d": "Donne la référence spécifiée par une chaîne de caractères", "ad": "est une référence à une cellule qui contient une référence de type A1 ou L1C1, un nom défini comme référence, ou une référence à une cellule équivalente à une chaîne de caractères!est une valeur logique qui indique le type de référence contenu dans la cellule de l'argument réf_texte: style L1C1 = FAUX; style A1 = VRAI ou omis"}, "LOOKUP": {"a": "(valeur_cherchée; vecteur_recherche; [vecteur_résultat]!valeur_cherchée; matrice)", "d": "Renvoie une valeur soit à partir d'une plage d'une ligne ou d'une colonne, soit à partir d'une matrice. Fournie pour la compatibilité ascendante", "ad": "est la valeur que la fonction cherche dans un tableau ou un vecteur; cela peut être un nombre, du texte, une valeur logique, un nom ou une référence à une valeur!représente une plage d'une seule ligne ou colonne de texte, de nombres ou de valeurs logiques rangés en ordre croissant!est une plage qui contient une seule ligne ou colonne, de la même taille que vecteur_cherché!est une valeur que la fonction recherche dans un tableau, et cela peut être un nombre, du texte, une valeur logique, un nom ou une référence à une valeur!est une plage de cellules qui contient du texte, des nombres ou des valeurs logiques que vous voulez comparer à l'argument valeur_cherchée"}, "MATCH": {"a": "(valeur_cherchée; tableau_recherche; [type])", "d": "Renvoie la position relative d'un élément dans une matrice qui correspond à une valeur spécifique dans un ordre spécifique", "ad": "est la valeur utilisée pour trouver la valeur voulue dans la matrice, un nombre, du texte, une valeur logique ou la référence à une de ces valeurs!est une plage de cellules adjacentes contenant les valeurs d'équivalence possibles, une matrice de valeurs ou la référence à une matrice!représente le nombre 1, 0 ou -1 indiquant la valeur à renvoyer."}, "OFFSET": {"a": "(réf; lignes; colonnes; [hauteur]; [largeur])", "d": "Donne une référence à une plage dont le nombre de colonnes et de lignes est spécifié dans une cellule ou une plage de cellules", "ad": "est la référence par rapport à laquelle le décalage doit être opéré, une référence à une cellule ou une plage de cellules adjacentes!est le nombre de lignes vers le haut ou vers le bas dont la cellule supérieure gauche de la référence renvoyée doit être décalée!est le nombre de colonnes vers la droite ou vers la gauche dont la cellule supérieure gauche de la référence renvoyée doit être décalée!est la hauteur, en nombre de lignes, attendue pour le résultat. Celle-ci est égale à Réf si omise!est la largeur, en nombre de colonnes, attendue pour le résultat. Celle-ci est égale à Réf si omise"}, "ROW": {"a": "([réf<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON> le numéro de ligne d'une référence", "ad": "est la cellule ou la plage de cellules dont vous voulez obtenir le numéro de ligne; si omis, renvoie la cellule contenant la fonction LIGNE"}, "ROWS": {"a": "(tableau)", "d": "Renvoie le nombre de lignes d'une référence ou d'une matrice.", "ad": "est un tableau, une formule matricielle ou la référence d'une plage de cellules dont vous voulez obtenir le nombre de lignes"}, "TRANSPOSE": {"a": "(tableau)", "d": "Change une plage de cellules verticale en plage horizontale, et vice-versa", "ad": "est un tableau dans une feuille de calcul ou une feuille macro que vous voulez transposer"}, "UNIQUE": {"a": "(matrice; [by_col]; [exactly_once])", "d": " Renvoie les valeurs uniques d’une plage ou d’une matrice. ", "ad": "la plage ou le tableau à partir duquel renvoyer des lignes ou des colonnes uniques!est une valeur logique : comparez les lignes les unes aux autres et renvoyez les lignes uniques = FALSE ou omis; compare les colonnes les unes aux autres et renvoie les colonnes uniques = TRUE!est une valeur logique : renvoie les lignes ou les colonnes qui apparaissent exactement une fois dans le tableau = TRUE ; renvoie toutes les lignes ou colonnes distinctes du tableau = FALSE ou omis "}, "VLOOKUP": {"a": "(valeur_cherchée; table_matrice; no_index_col; [valeur_proche])", "d": "Cherche une valeur dans la première colonne à gauche d'un tableau, puis renvoie une valeur dans la même ligne à partir d'une colonne spécifiée. Pa<PERSON> <PERSON><PERSON><PERSON><PERSON>, le tableau doit être trié par ordre croissant", "ad": "est la valeur à trouver dans la première colonne du tableau, et peut être une valeur, une référence, ou une chaîne textuelle!est un tableau de texte, nombres, valeurs logiques, à partir duquel les données sont récupérées. L'argument table_matrice peut être une plage de cellules ou le nom d'une plage!est le numéro de la colonne de l'argument table_matrice dont la valeur correspondante est renvoyée. La première colonne de valeurs dans le tableau est la colonne 1!est une valeur logique: pour trouver la valeur la plus proche dans la première colonne (triée par ordre croissant) = VRAI ou omis; pour trouver la correspondance exacte = FAUX"}, "XLOOKUP": {"a": "(valeur_cherchée; tableau_recherche; tableau_renvoyé; [si_non_trouvé]; [mode_correspondance]; [mode_recherche])", "d": "Recherche une correspondance dans une plage ou un tableau et renvoie l’élément correspondant dans un deuxième tableau ou plage. <PERSON><PERSON> d<PERSON><PERSON><PERSON>, une correspondance exacte est utilisée", "ad": "représente la valeur recherchée!représente le tableau ou la plage sur lequel effectuer la recherche!représente le tableau ou la plage à renvoyer!renvoyé si aucune correspondance n’est trouvée!spécifie comment comparer la valeur_cherchée aux valeurs dans le tableau_recherche!spécifie le mode de recherche à utiliser. Par <PERSON><PERSON><PERSON><PERSON>, une recherche du premier au dernier est utilisée"}, "CELL": {"a": "(info_type; [reference])", "d": "Renvoie des informations sur la mise en forme, l’emplacement ou le contenu d’une cellule", "ad": "valeur de texte qui spécifie le type d’informations de cellule que vous voulez obtenir!représente la cellule dont vous voulez obtenir des informations"}, "ERROR.TYPE": {"a": "(valeur)", "d": "Renvoie un numéro qui correspond à une valeur d'erreur.", "ad": "est la valeur d'erreur dont vous voulez trouver le numéro d'identification ; il peut s'agir d'une valeur d'erreur véritable ou d'une référence à une cellule contenant une valeur d'erreur"}, "ISBLANK": {"a": "(valeur)", "d": "Contrôle si une référence renvoie à une cellule vide et renvoie VRAI ou FAUX", "ad": "est une cellule ou un nom qui fait référence à la cellule que vous voulez tester"}, "ISERR": {"a": "(valeur)", "d": "Vérifie si l’argument valeur fait référence à une erreur autre que #N/A (valeur non disponible), et renvoie VRAI ou FAUX selon le résultat", "ad": "représente la valeur à tester. Elle peut renvoyer à une cellule, une formule, ou un nom qui fait référence à une cellule, une formule ou une valeur"}, "ISERROR": {"a": "(valeur)", "d": "Vérifie si l’argument valeur fait référence à une erreur, et renvoie VRAI ou FAUX selon le résultat", "ad": "représente la valeur à tester. Elle peut renvoyer à une cellule, une formule, ou un nom qui fait référence à une cellule, une formule ou une valeur"}, "ISEVEN": {"a": "(nombre)", "d": "Renvoie VRAI si le nombre est pair", "ad": "est la valeur dont vous voulez déterminer la parité"}, "ISFORMULA": {"a": "(référence)", "d": "Vérifie si une référence renvoie à une cellule contenant une formule, et renvoie TRUE ou FALSE", "ad": "est une référence à la cellule à tester. La référence peut être une référence de cellule, une formule ou un nom qui fait référence à une cellule"}, "ISLOGICAL": {"a": "(valeur)", "d": "Renvoie VRAI si l'argument valeur fait référence à une valeur logique, que ce soit VRAI ou FAUX", "ad": "est la valeur à tester. Elle peut renvoyer à une cellule, une formule, ou un nom qui fait référence à une cellule, une formule ou une valeur"}, "ISNA": {"a": "(valeur)", "d": "Renvoie VRAI si l'argument valeur fait référence à la valeur d'erreur #N/A (valeur non disponible)", "ad": "est la valeur à tester. Elle peut renvoyer à une cellule, une formule, ou un nom qui fait référence à une cellule, une formule ou une valeur"}, "ISNONTEXT": {"a": "(valeur)", "d": "Renvoie VRAI si l'argument valeur fait référence à autre chose que du texte (les cellules vides ne sont pas du texte)", "ad": "est la valeur que vous voulez tester : une cellule, une formule, ou un nom faisant référence à une cellule, une formule, ou une valeur"}, "ISNUMBER": {"a": "(valeur)", "d": "Contrôle si la valeur est un nombre et renvoie VRAI ou FAUX", "ad": "est la valeur à tester. Elle peut renvoyer à une cellule, une formule, ou un nom qui fait référence à une cellule, une formule ou une valeur"}, "ISODD": {"a": "(nombre)", "d": "Renvoie VRAI si le nombre est impair", "ad": "est la valeur dont vous voulez déterminer la parité"}, "ISREF": {"a": "(valeur)", "d": "Renvoie VRAI ou FAUX si l'argument valeur est une référence", "ad": "est la valeur à tester. Elle peut renvoyer à une cellule, une formule, ou un nom qui fait référence à une cellule, une formule ou une valeur"}, "ISTEXT": {"a": "(valeur)", "d": "Contrôle si une valeur fait référence à du texte et renvoie VRAI ou FAUX ", "ad": "est la valeur à tester. Elle peut renvoyer à une cellule, une formule, ou un nom qui fait référence à une cellule, une formule ou une valeur"}, "N": {"a": "(valeur)", "d": "Renvoie une valeur convertie en nombre. Les nombres sont convertis en nombres, les dates en numéros de série, les VRAI en 1, et tout le reste en 0 (zéro)", "ad": "est la valeur que vous voulez convertir"}, "NA": {"a": "()", "d": "Renvoie la valeur d'erreur #N/A (valeur non disponible)", "ad": ""}, "SHEET": {"a": "([valeur])", "d": "Renvoie le numéro de la feuille référencée", "ad": "est le nom d'une feuille ou d'une référence dont vous voulez obtenir le numéro de feuille. Sinon, le numéro de la feuille contenant la fonction est renvoyé"}, "SHEETS": {"a": "([réf<PERSON><PERSON><PERSON>])", "d": "Renvoie le nombre de feuilles dans une référence", "ad": "est la référence pour laquelle vous voulez obtenir le nombre de feuilles qu'elle contient. Sinon, le nombre de feuilles dans le classeur contenant la fonction est renvoyé"}, "TYPE": {"a": "(valeur)", "d": "Renvoie un nombre indiquant le type de données d’une valeur : nombre = 1 ; texte = 2 ; valeur logique = 4 ; valeur d’erreur = 16 ; tableau = 64 ; donn<PERSON> composites = 128", "ad": "peut être n’importe quelle valeur"}, "AND": {"a": "(valeur_logique1; [valeur_logique2]; ...)", "d": "Vérifie si tous les arguments sont VRAI et renvoie VRAI si tous les arguments sont VRAI", "ad": "représentent de 1 à 255 conditions à tester et qui peuvent être soit VRAI, soit FAUX et représenter aussi bien des valeurs logiques que des matrices ou des références"}, "FALSE": {"a": "()", "d": "Renvoie la valeur logique FAUX", "ad": ""}, "IF": {"a": "(test_logique; [valeur_si_vrai]; [valeur_si_faux])", "d": "Vérifie si la condition est respectée et renvoie une valeur si le résultat d'une condition que vous avez spécifiée est VRAI, et une autre valeur si le résultat est FAUX", "ad": "est toute valeur ou expression dont le résultat peut être VRAI ou FAUX!représente la valeur renvoyée si test_logique est VRAI. Si omis, VRAI est renvoyé. Vous pouvez utiliser jusqu'à sept fonctions SI!représente la valeur renvoyée si test logique est FAUX. Si omis, FAUX est renvoyé"}, "IFS": {"a": "(test_logique; valeur_si_vrai; ...)", "d": "Vérifie si une ou plusieurs conditions sont remplies et renvoie une valeur correspondant à la première condition VRAI", "ad": "représente n’importe quelle valeur ou expression dont le résultat peut être VRAI ou FAUX!représente la valeur renvoyée si testlogique a la valeur VRAI"}, "IFERROR": {"a": "(valeur; valeur_si_erreur)", "d": "Renvoie « valeur_si_erreur » si l'expression est une erreur et la valeur de l'expression dans le cas contaire", "ad": "représente n'importe quelle valeur, expression ou référence!représente n'importe quelle valeur, expression ou référence"}, "IFNA": {"a": "(valeur; valeur_si_na)", "d": "Renvoie la valeur que vous avez spécifiée si le résultat de l'expression est #N/A, sinon renvoie le résultat de l'expression", "ad": "représente n'importe quelle valeur, expression ou référence!représente n'importe quelle valeur, expression ou référence"}, "NOT": {"a": "(valeur_logique)", "d": "Inverse la valeur logique de l'argument: renvoie FAUX pour un argument VRAI et VRAI pour un argument FAUX", "ad": "est une valeur ou une expression dont l'évaluation peut être VRAI ou FAUX"}, "OR": {"a": "(valeur_logique1; [valeur_logique2]; ...)", "d": "Vérifie si un argument est VRAI et renvoie VRAI ou FAUX. Renvoie FAUX uniquement si tous les arguments sont FAUX", "ad": "représentent de 1 à 255 conditions que vous voulez tester, et qui peuvent être soit VRAI, soit FAUX"}, "SWITCH": {"a": "(expression; valeur1; résultat1; [défaut_ou_valeur2]; [résultat2]; ...)", "d": "Compare une expression avec les valeurs d’une liste et renvoie le résultat égal à la première valeur correspondante. Si aucune correspondance n’est trouvée, une valeur facultative par défaut est renvoyée", "ad": "représente l’expression à comparer!représente la valeur avec laquelle l’expression doit être comparée!représente le résultat à renvoyer si la valeur correspondante est égale à l’expression"}, "TRUE": {"a": "()", "d": "Renvoie la valeur logique VRAI", "ad": ""}, "XOR": {"a": "(valeur_logique1; [valeur_logique2]; ...)", "d": "Renvoie une valeur logique « Ou exclusif » de tous les arguments", "ad": "représentent de 1 à 254 conditions à tester qui peuvent avoir la valeur VRAI ou FAUX et être des valeurs logiques, tableaux ou références"}, "TEXTBEFORE": {"a": "(texte, d<PERSON><PERSON><PERSON>ur, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "<PERSON><PERSON><PERSON> le texte qui précède la délimitation des caractères.", "ad": "Texte que vous souhaitez rechercher pour le délimiteur.!<PERSON>ct<PERSON> ou chaîne à utiliser comme délimiteur.!Occurrence souhaitée du délimiteur. La valeur par défaut est 1. Nombre négatif de recherches à partir de la fin.!Recherche dans le texte une correspondance de délimiteur. Par d<PERSON><PERSON><PERSON>, une correspondance respectant la casse est effectuée.!Indique s’il faut faire correspondre le délimiteur à la fin du texte. Par dé<PERSON><PERSON>, ils ne sont pas mis en correspondance.!Renvoyé si aucune correspondance n’est trouvée. Par dé<PERSON><PERSON>, #N/A est renvoyé."}, "TEXTAFTER": {"a": "(texte, d<PERSON><PERSON><PERSON>ur, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Retourne du texte qui succède à la délimitation des caractères.", "ad": "Texte que vous souhaitez rechercher pour le délimiteur.!<PERSON>ct<PERSON> ou chaîne à utiliser comme délimiteur. Occurrence souhaitée du délimiteur.!La valeur par défaut est 1. Nombre négatif de recherches à partir de la fin.!Recherche dans le texte une correspondance de délimiteur. Par d<PERSON><PERSON><PERSON>, une correspondance respectant la casse est effectuée.!Indique s’il faut faire correspondre le délimiteur à la fin du texte. Par dé<PERSON><PERSON>, ils ne sont pas mis en correspondance.!Renvoyé si aucune correspondance n’est trouvée. Par dé<PERSON><PERSON>, #N/A est renvoyé."}, "TEXTSPLIT": {"a": "(texte, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Fractionne le texte en lignes ou colonnes à l’aide de délimiteurs.", "ad": "Texte à fractionner!<PERSON>ct<PERSON> ou chaîne pour fractionner les colonnes par.!<PERSON>ctère ou chaîne pour fractionner les lignes par.!Indique s’il faut ignorer les cellules vides. La valeur par défaut FALSE.!Recherche dans le texte une correspondance de délimiteur. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, une correspondance respectant la casse est effectuée.!Valeur à utiliser pour le remplissage. Par défa<PERSON>, #N/A est utilisé."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Enveloppe un vecteur de ligne ou de colonne après un nombre spécifié de valeurs.", "ad": "Le vecteur ou la référence à envelopper !Le nombre maximum de valeurs par ligne.!La valeur avec laquelle il faut remplir. La valeur par défaut est #N/A."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Empile verticalement les tableaux dans un tableau.", "ad": " Tableau ou référence à empiler."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Empile horizontalement les tableaux dans un tableau.", "ad": " Tableau ou référence à empiler."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Renvoie les lignes d’un tableau ou d’une référence.", "ad": "Le tableau ou la référence contenant les lignes à renvoyer.!Le nombre de lignes à renvoyer."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Renvoie les colonnes d’un tableau ou d’une référence.", "ad": "Le tableau ou la référence contenant les colonnes à renvoyer.!Le nombre de colonnes à renvoyer."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Renvoie le tableau sous la forme d’une colonne.", "ad": "Le tableau ou référence à renvoyer sous forme de colonne.!Indique s’il faut ignorer certains types de valeurs. Par dé<PERSON><PERSON>, aucune valeur n’est ignorée.!Analyse le tableau par colonne. Par d<PERSON><PERSON><PERSON>, le tableau est analysé par ligne."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "<PERSON><PERSON><PERSON> le tableau sous la forme d’une ligne.", "ad": "Tableau ou référence à retourner en tant que ligne.!Indique s’il faut ignorer certains types de valeurs. Par d<PERSON><PERSON><PERSON>, aucune valeur n’est ignorée.!Analyse le tableau par colonne. Pa<PERSON> d<PERSON><PERSON><PERSON>, le tableau est analysé par ligne."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Enveloppe un vecteur de ligne ou de colonne après un nombre spécifié de valeurs.", "ad": "Vecteur ou référence à envelopper.!Nombre maximum de valeurs par colonne.!Valeur avec laquelle remplir. #N/A par défaut."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Renvoie les lignes ou les colonnes du début ou de la fin du tableau.", "ad": "Le tableau à partir duquel prendre des lignes ou des colonnes.!Le nombre de lignes à prendre. Une valeur négative prise à la fin du tableau.!Le nombre de colonnes à prendre. Une valeur négative à prendre à la fin du tableau."}, "DROP": {"a": "(array, rows, [columns])", "d": "Supprime les lignes ou les colonnes du début ou de la fin du tableau.", "ad": "Le tableau à partir duquel supprimer des lignes ou des colonnes.!Le nombre de lignes à supprimer. Une valeur négative supprimée à la fin du tableau.!Le nombre de colonnes à supprimer. Une valeur négative à supprimer à la fin du tableau."}, "SEQUENCE": {"a": "(lignes, [colonnes], [début], [pas])", "d": "Renvoie une séquence de nombres", "ad": "le nombre de lignes à renvoyer!le nombre de colonnes à renvoyer!le premier nombre de la séquence!la quantité dont les valeurs individuelles suivantes doivent être incrémentées dans la séquence"}, "EXPAND": {"a": "(tableau, lignes, [colonnes], [pad_with])", "d": "Développe un tableau aux dimensions spécifiées.", "ad": "Le tableau à développer.!Le nombre de lignes dans le tableau développé. S’il est manquant, les lignes ne seront pas développées.!Le nombre de colonnes dans le tableau développé. S’il est manquant, les colonnes ne seront pas développées.!Valeur a appliquer. La valeur par défaut est #N/A."}, "XMATCH": {"a": "(valeur_cherchée, tableau_recherche, [mode_correspondance], [mode_recherche])", "d": "Renvoie la position relative d’un élément dans un tableau. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, une correspondance exacte est requise", "ad": "représente la valeur à rechercher!représente la plage ou le tableau dans lequel effectuer la recherche!spécifie comment comparer la valeur_cherchée aux valeurs du tableau_recherche!spécifie le mode de recherche à utiliser. <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, une recherche du premier au dernier est utilisée"}, "FILTER": {"a": "(tableau, inclure, [si_vide])", "d": "Filtrer une plage ou un tableau", "ad": "la plage ou le tableau à filtrer!un tableau de booléens où TRUE représente une ligne ou une colonne à conserver!renvoyé si aucun élément n’est conservé"}, "ARRAYTOTEXT": {"a": "(matrice, [format])", "d": "Renvoie une représentation textuelle d’un tableau", "ad": " tableau à représenter sous forme de texte ! le format du texte "}, "SORT": {"a": "(tableau, [index_tri], [ordre_tri], [par_col])", "d": "Trie une plage ou un tableau", "ad": "la plage ou le tableau à trier!un nombre indiquant la ligne ou la colonne sur laquelle effectuer le tri!un nombre indiquant l’ordre de tri souhaité ; 1 pour l’ordre croissant (par défaut), -1 pour l’ordre décroissant!une valeur logique indiquant le sens de tri souhaité : FALSE pour effectuer un tri par ligne (par défaut), TRUE pour effectuer un tri par colonne"}, "SORTBY": {"a": "(tableau, par_tableau, [ordre_tri], ...)", "d": "Trie une plage ou un tableau sur la base des valeurs d’une plage ou d’un tableau correspondant", "ad": "la plage ou le tableau à trier!la plage ou le tableau sur lequel baser le tri!un numéro indiquant l’ordre de tri souhaité ; 1 pour l’ordre croissant (par défaut), -1 pour l’ordre décroissant"}, "GETPIVOTDATA": {"a": "(champ_données; tableau_croisé_dyn; [champ]; [é<PERSON><PERSON>]; ...)", "d": "Extrait des données d'un tableau croisé dynamique.", "ad": "est le nom du champ de données d'où extraire les données!est une référence à une cellule ou à une plage de cellules du tableau croisé dynamique contenant les données à extraire!champ de référence!élément du champ de référence"}, "IMPORTRANGE": {"a": "(url_feuille; chaîne_plage)", "d": "Importe une plage de cellules depuis une feuille de calcul spécifiée.", "ad": "URL de la feuille de calcul contenant les données à importer!la plage à importer"}}