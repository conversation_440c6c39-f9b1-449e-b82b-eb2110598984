{"DATE": {"a": "(year; month; day)", "d": "Mengembalikan angka yang mewakili tanggal dalam kode tanggal-waktu", "ad": "adalah angka dari 1900 atau 1904 (bergantung pada sistem tanggal buku kerja) sampai 9999!adalah angka dari 1 sampai 12 yang mewakili bulan dalam satu tahun!adalah angka dari 1 sampai 31 yang mewakili hari dalam satu bulan"}, "DATEDIF": {"a": "(start-date; end-date; unit)", "d": "Menghitung jumlah hari, bula<PERSON>, atau tahun di antara dua tanggal", "ad": "<PERSON>gal yang menunjukkan tanggal pertama, atau tanggal mulai periode tertentu!Tanggal yang menunjukkan tanggal terakhir, atau tanggal berakhirnya periode!Tipe informasi yang ingin Anda kembalikan"}, "DATEVALUE": {"a": "(date_text)", "d": "Mengonversi tanggal dalam bentuk teks ke angka yang mewakili tanggal dalam kode tanggal-waktu", "ad": "adalah teks yang mewakili tanggal dalam format tanggal Spreadsheet Editor, di antara 1/1/1900 atau 1/1/1904 (tergantung sistem tanggal buku kerja) dan 12/31/9999"}, "DAY": {"a": "(serial_number)", "d": "Mengembalik<PERSON> hari dari bulan, angka dari 1 sampai 31.", "ad": "adalah angka dalam kode tanggal-waktu yang digunakan oleh Spreadsheet Editor"}, "DAYS": {"a": "(end_date; start_date)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> jumlah hari antara dua tanggal.", "ad": "start_date dan end_date adalah dua tanggal yang ingin Anda ketahui jumlah hari di antara keduanya!start_date dan end_date adalah dua tanggal yang ingin Anda ketahui jumlah hari di antara keduanya"}, "DAYS360": {"a": "(start_date; end_date; [method])", "d": "Menampilkan jumlah hari di antara dua tanggal berdasarkan pada 360-hari setahun (dua belas bulan per bulan 30-hari)", "ad": "tanggal_mulai dan tanggal_selesai adalah dua tanggal yang harus Anda ketahui jika ingin menghitung jumlah hari!tanggal_mulai dan tanggal_selesai adalah dua tanggal yang harus Anda ketahui jika ingin menghitung jumlah hari!adalah nilai logis yang menentukan metode perhitungan: U.S. (NASD) = SALAH atau dihilangkan; European = BENAR."}, "EDATE": {"a": "(start_date; months)", "d": "Menampilkan angka seri tanggal yang menunjukkan jumlah bulan sebelum atau sesudah tanggal mulai", "ad": "adalah angka tanggal seri yang mewakili tanggal mulai!adalah jumlah bulan sebelum atau setelah tanggal_mulai"}, "EOMONTH": {"a": "(start_date; months)", "d": "Menampilkan angka seri hari terakhir dari bulan sebelum atau setelah jumlah bulan yang ditentukan", "ad": "adalah angka tanggal seri yang mewakili tanggal mulai!adalah jumlah bulan sebelum atau setelah tanggal_mulai"}, "HOUR": {"a": "(serial_number)", "d": "Mengembalikan jam sebagai angka dari 0 (12:00 A.M.) sampai 23 (11:00 P.M.).", "ad": "adalah angka dalam kode tanggal-waktu yang digunakan oleh Spreadsheet Editor, atau teks dalam format waktu, seperti 16:48:00 atau 4:48:00 PM"}, "ISOWEEKNUM": {"a": "(date)", "d": "Mengembalikan jumlah minggu ISO dalam setahun untuk tanggal tertentu", "ad": "adalah kode tanggal-waktu yang digunakan Spreadsheet Editor untuk penghitungan tanggal dan waktu"}, "MINUTE": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> menit, an<PERSON><PERSON> dari 0 sampai 59.", "ad": "adalah angka dalam kode tanggal-waktu yang digunakan oleh Spreadsheet Editor atau teks dalam format waktu, seperti 16:48:00 atau 4:48:00 PM"}, "MONTH": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> bulan, ang<PERSON> dari 1 (<PERSON><PERSON><PERSON>) sampai 12 (<PERSON><PERSON><PERSON>).", "ad": "adalah angka dalam kode tanggal-waktu yang digunakan oleh Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(start_date; end_date; [holidays])", "d": "Menampilkan jumlah dari seluruh hari kerja antara dua tanggal", "ad": "adalah angka tanggal seri yang mewakili tanggal mulai!adalah angka tanggal seri yang mewakili tanggal selesai!adalah set tambahan dari satu atau lebih angka tanggal seri untuk dieksklusifkan dari kalender kerja, seperti libur umum dan nasional dan liburan tertunda"}, "NETWORKDAYS.INTL": {"a": "(start_date; end_date; [weekend]; [holidays])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> jumlah seluruh hari kerja antara dua tanggal dengan parameter akhir pekan kustom", "ad": "adalah angka tanggal seri yang menunjukkan tanggal mulai!adalah angka tanggal seri yang menunjukkan tanggal akhir!adalah angka atau string yang menentukan saat akhir pekan berlangsung!adalah set opsional dari satu atau beberapa angka tanggal seri untuk dikeluarkan dari kalender kerja, seperti libur umum dan nasional dan liburan tertunda"}, "NOW": {"a": "()", "d": "Menampilkan tanggal dan waktu sekarang terformat sebagai tanggal dan waktu.", "ad": ""}, "SECOND": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, an<PERSON><PERSON> dari 0 sampai 59.", "ad": "adalah angka dalam kode tanggal-waktu yang digunakan oleh Spreadsheet Editor atau teks dalam format waktu, seperti 16:48:23 atau 4:48:47 PM"}, "TIME": {"a": "(hour; minute; second)", "d": "Konversi jam, menit, dan detik yang ditentukan sebagai angka ke nomor seri, diformat dengan format waktu", "ad": "adalah angka dari 0 sampai 23 yang menunjukkan jam!adalah angka dari 0 sampai 59 yang menunjukkan menit!adalah angka dari 0 sampai 59 yang menunjukkan detik"}, "TIMEVALUE": {"a": "(time_text)", "d": "Mengonversi waktu teks ke nomor seri untuk waktuu, angka dari 0 (12:00:00 AM) sampai 0.999988426 (11:59:59 PM). Format nomor dengan format waktu setelah memasukkan rumus", "ad": "adalah string teks yang menampilkan waktu dalam apa pun format waktu Spreadsheet Editor (informasi tanggal dalam string diabaikan)"}, "TODAY": {"a": "()", "d": "Menampilkan format tanggal sekarang sebagai tanggal.", "ad": ""}, "WEEKDAY": {"a": "(serial_number; [return_type])", "d": "Menampilkan angka dari 1 sampai 7 untuk menunjukkan hari dari minggu dari tanggal.", "ad": "adalah angka yang menunjukkan tanggal!adalah angka: untuk Minggu=1 sampai Sabtu=7, gunakan 1; untuk Senin=1 sampai Minggu=7, gunakan 2; untuk Senin=0 sampai Minggu=6, gunakan 3"}, "WEEKNUM": {"a": "(serial_number; [return_type])", "d": "Mengembalikan jumlah minggu dalam setahun", "ad": "adalah kode tanggal-waktu yang digunakan oleh Spreadsheet Editor untuk penghitungan tanggal dan waktu!adalah angka (1 atau 2) yang menentukan tipe nilai pengembalian"}, "WORKDAY": {"a": "(start_date; days; [holidays])", "d": "Menampilkan angka seri tanggal sebelum atau sesudah dari bilangan tertentu dari hari kerja", "ad": " adalah angka seri tanggal yang mewakili tanggal mulai!adalah angka dari hari bukan akhir pekan dan bukan hari libur sebelum atau setelah start_data!merupakan array opsional dari satu atau lebih angka tanggal seri yang dikeluarkan dari kalender kerja, seperti hari libur negara bagian dan federal dan liburan tertunda"}, "WORKDAY.INTL": {"a": "(start_date; days; [weekend]; [holidays])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> jumlah seluruh hari kerja antara dua tanggal dengan parameter akhir pekan kustom", "ad": "adalah angka tanggal seri yang menunjukkan tanggal mulai!adalah jumlah hari non-akhir pekan dan non-liburan sebelum atau setelah tanggal_mulai!adalah angka atau string yang menentukan saat akhir pekan berlangsung!adalah array opsional dari satu atau beberapa angka tanggal seri untuk dikeluarkan dari kalender kerja, seperti libur umum dan nasional serta liburan tertunda"}, "YEAR": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> tahun dari tanggal, bilangan bulat dalam rentang 1900 - 9999.", "ad": "adalah angka dalam kode tanggal-waktu yang digunakan oleh Spreadsheet Editor"}, "YEARFRAC": {"a": "(start_date; end_date; [basis])", "d": "Menampilkan pecahan tahun yang mewakili angka seluruh hari antara tanggal_mulai dan tanggal_akhir", "ad": "adalah angka tanggal seri yang mewakili tanggal mulai!adalah angka tanggal seri yang mewakili tanggal akhir!adalah tipe basis hitungan hari untuk digunakan"}, "BESSELI": {"a": "(x; n)", "d": "Menampilkan fungsi Bessel termodifikasi In(x)", "ad": "adalah nilai untuk mengevaluasi fungsi!adalah urutan fungsi Bessel"}, "BESSELJ": {"a": "(x; n)", "d": "Menampilkan fungsi Bessel Jn(x)", "ad": "adalah nilai untuk mengevaluasi fungsi!adalah urutan fungsi Bessel"}, "BESSELK": {"a": "(x; n)", "d": "Menampilkan fungsi Bessel termodifikasi Kn(x)", "ad": "adalah nilai untuk mengevaluasi fungsi!adalah urutan fungsi"}, "BESSELY": {"a": "(x; n)", "d": "Menampilkan fungsi Bessel Yn(x)", "ad": "adalah nilai untuk mengevaluasi fungsi!adalah urutan fungsi"}, "BIN2DEC": {"a": "(number)", "d": "Mengonversi sebuah bilangan biner ke desimal", "ad": "ad<PERSON>h bilangan biner yang ingin <PERSON>a konversi"}, "BIN2HEX": {"a": "(number; [places])", "d": "Mengonversi sebuah bilangan biner ke hexadesimal", "ad": "adalah bilangan biner yang ingin Anda konversi!adalah jumlah karakter untuk digunakan"}, "BIN2OCT": {"a": "(number; [places])", "d": "Mengonversi sebuah bilangan biner ke oktal", "ad": "adalah bilangan biner yang ingin Anda konversi!adalah jumlah karakter untuk digunakan"}, "BITAND": {"a": "(number1; number2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> bitwise '<PERSON>' dari dua angka", "ad": "adalah representasi desimal dari angka biner yang ingin Anda evaluasi!adalah representasi desimal dari angka biner yang ingin Anda evaluasi"}, "BITLSHIFT": {"a": "(number; shift_amount)", "d": "Menampilkan angka yang dialihkan ke kiri dengan shift_amount bits", "ad": "adalah representasi desimal angka biner yang ingin Anda evaluasi!adalah angka bit yang Angkanya ingin Anda alihkan ke kiri"}, "BITOR": {"a": "(number1; number2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> bitwise '<PERSON><PERSON>' dari dua angka", "ad": "adalah representasi desimal dari angka biner yang ingin Anda evaluasi!adalah representasi desimal dari angka biner yang ingin Anda evaluasi"}, "BITRSHIFT": {"a": "(number; shift_amount)", "d": "Menampilkan angka yang dialihkan ke kanan dengan shift_amount bits", "ad": "adalah representasi desimal angka biner yang ingin Anda evaluasi!adalah angka bit yang Angkanya ingin Anda alihkan ke kanan"}, "BITXOR": {"a": "(number1; number2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> bit<PERSON> '<PERSON><PERSON><PERSON><PERSON><PERSON>' dari dua angka", "ad": "adalah representasi desimal dari angka biner yang ingin Anda evaluasi!adalah representasi desimal dari angka biner yang ingin Anda evaluasi"}, "COMPLEX": {"a": "(real_num; i_num; [suffix])", "d": "Mengonversi koefisien nyata dan bayangan ke dalam bilangan kompleks", "ad": "adalah koefisien nyata dari bilangan kompleks!adalah koefisien bayangan dari bilangan kompleks!adalah sufiks untuk komponen bayangan dari bilangan kompleks"}, "CONVERT": {"a": "(number; from_unit; to_unit)", "d": "Mengubah bilangan dari satu sistem pengukuran ke lainnya", "ad": "adalah nilai dalam dari_unit untuk dikonversi!adalah unit bilangan!adalah unit untuk hasil"}, "DEC2BIN": {"a": "(number; [places])", "d": "Mengonversi sebuah bilangan desimal ke biner", "ad": "adalah bilangan bulat desimal yang ingin Anda konversikan!adalah jumlah karakter untuk digunakan"}, "DEC2HEX": {"a": "(number; [places])", "d": "Mengonversi sebuah bilangan desimal ke hexadesimal", "ad": "adalah bilangan bulat desimal yang ingin Anda konversi!adalah jumlah karakter untuk digunakan"}, "DEC2OCT": {"a": "(number; [places])", "d": "Mengonversi sebuah bilangan desimal ke oktal", "ad": "adalah bilangan bulat desimal yang ingin Anda konversi!adalah jumlah karakter untuk digunakan"}, "DELTA": {"a": "(number1; [number2])", "d": "<PERSON><PERSON><PERSON> a<PERSON>h dua bilangan sama", "ad": "adalah bilangan pertama!adalah bilangan kedua"}, "ERF": {"a": "(lower_limit; [upper_limit])", "d": "Mengembalikan fungsi kesalahan", "ad": "adalah batas terbawah untuk mengintegrasikan ERF!adalah batas teratas untuk mengintegrasikan ERF"}, "ERF.PRECISE": {"a": "(X)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> fungsi kes<PERSON>han", "ad": "adalah batas bawah untuk mengintegrasikan ERF.PRECISE"}, "ERFC": {"a": "(x)", "d": "Mengembalik<PERSON> kes<PERSON>han tambahan fungsi", "ad": "adalah batas terbawah untuk mengintegrasikan ERF"}, "ERFC.PRECISE": {"a": "(X)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> fungsi kes<PERSON>han peleng<PERSON>p", "ad": "adalah batas bawah untuk mengintegrasikan ERFC.PRECISE"}, "GESTEP": {"a": "(number; [step])", "d": "<PERSON><PERSON><PERSON> a<PERSON>h bilangan lebih besar dari nilai ambang", "ad": "adalah nilai untuk menguji langkah!adalah nilai ambang"}, "HEX2BIN": {"a": "(number; [places])", "d": "Mengonversi sebuah bilangan Hexadesimal ke biner", "ad": "adalah bilangan hexadesimal yang ingin Anda konversi!adalah jumlah karakter untuk digunakan"}, "HEX2DEC": {"a": "(number)", "d": "Mengonversi sebuah bilangan hexadesimal ke desimal", "ad": "adalah bilangan hexadesimal yang ingin Anda konversi"}, "HEX2OCT": {"a": "(number; [places])", "d": "Mengonversi sebuah bilangan hexadesimal ke oktal", "ad": "adalah bilangan hexadesimal yang ingin Anda konversi!adalah jumlah karakter untuk digunakan"}, "IMABS": {"a": "(inumber)", "d": "Menampilkan bilangan absolut (modul) dari bilangan kompleks", "ad": "bilangan kompleks yang bilangan absolutnya Anda inginkan"}, "IMAGINARY": {"a": "(inumber)", "d": "Menampilkan koefisien bayangan dari bilangan kompleks", "ad": "ad<PERSON>h bilangan kompleks yang koefisien bayangannya <PERSON>a ing<PERSON>an"}, "IMARGUMENT": {"a": "(inumber)", "d": "Menampilkan argumen q, sebuah sudut terekspresi dalam radian", "ad": "ad<PERSON>h bilangan kompleks yang argumennya <PERSON>a ing<PERSON>an"}, "IMCONJUGATE": {"a": "(inumber)", "d": "Menampilkan konjugasi kompleks dari bilangan kompleks", "ad": "ad<PERSON>h bilangan kompleks yang konju<PERSON><PERSON>ya <PERSON>a ing<PERSON>an"}, "IMCOS": {"a": "(inumber)", "d": "Menampilkan kosinus dari bilangan kompleks", "ad": "ad<PERSON>h bilangan kompleks yang kos<PERSON>nya <PERSON>a inginkan"}, "IMCOSH": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kosinus hiperbolik dari bilangan kompleks", "ad": "ad<PERSON>h bilangan kompleks yang Anda inginkan kosinus hiperbolik<PERSON>"}, "IMCOT": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kotangen dari bilangan kompleks", "ad": "adalah bilangan kompleks yang Anda inginkan kotangennya"}, "IMCSC": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kosekan dari bilangan kompleks", "ad": "adalah bilangan kompleks yang Anda inginkan kosekannya"}, "IMCSCH": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON>an kosekan hiperbolik dari bilangan kompleks", "ad": "adalah bilangan kompleks yang Anda inginkan kosekan hiperboliknya"}, "IMDIV": {"a": "(inumber1; inumber2)", "d": "Menampilkan hasil bagi dari dua bilangan kompleks", "ad": "adalah penghitung atau dividen kompleks!adalah persamaan atau pembagi kompleks"}, "IMEXP": {"a": "(inumber)", "d": "Menampilkan eksponensial dari bilangan kompleks", "ad": "ad<PERSON>h bilangan kompleks yang eksponensialnya Anda inginkan"}, "IMLN": {"a": "(inumber)", "d": "Menampilkan logaritma alami dari bilangan kompleks", "ad": "adalah bilangan kompleks yang logaritma alaminya Anda inginkan"}, "IMLOG10": {"a": "(inumber)", "d": "Menampilkan logaritma dasar 10 dari bilangan kompleks", "ad": "adalah bilangan kompleks yang logaritma umumnya Anda inginkan"}, "IMLOG2": {"a": "(inumber)", "d": "Menampilkan logaritma dasar 2 dari bilangan kompleks", "ad": "ad<PERSON>h bilangan kompleks yang logaritma dasar 2-nya <PERSON><PERSON> ing<PERSON>an"}, "IMPOWER": {"a": "(inumber; number)", "d": "Menampilkan bilangan kompleks yang dinaikkan ke kekuatan bilangan bulat", "ad": "adalah bilangan kompleks yang ingin Anda naikkan hingga ke kekuatan!adalah kekuatan di mana Anda ingin menaikkan bilangan kompleks"}, "IMPRODUCT": {"a": "(inumber1; [inumber2]; ...)", "d": "Menampilkan produk dari 1 sampai 255 bilangan kompleks", "ad": "Inumber1, Inumber2,... adalah dari 1 sampai 255 bilangan kompleks untuk dikalikan."}, "IMREAL": {"a": "(inumber)", "d": "Menampilkan koefisien nyata dari bilangan kompleks", "ad": "ad<PERSON>h bilangan kompleks yang koefisien nyatanya <PERSON>a ing<PERSON>an"}, "IMSEC": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> sekan dari bilangan kompleks", "ad": "adalah bilangan kompleks yang Anda inginkan sekannya"}, "IMSECH": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> sekan hiperbolik dari bilangan kompleks", "ad": "adalah bilangan kompleks yang Anda inginkan sekan hiperboliknya"}, "IMSIN": {"a": "(inumber)", "d": "Menampilkan sinus dari bilangan kompleks", "ad": "adalah bilangan kompleks yang sinusnya Anda inginkan"}, "IMSINH": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON>lkan sinus hiperbolik dari bilangan kompleks", "ad": "adalah bilangan kompleks yang Anda inginkan sinus hiperboliknya"}, "IMSQRT": {"a": "(inumber)", "d": "Menampilkan akar pangkat dua dari bilangan kompleks", "ad": "ad<PERSON>h bilangan kompleks yang akar pangkat duanya <PERSON>a ing<PERSON>an"}, "IMSUB": {"a": "(inumber1; inumber2)", "d": "Menampilkan perbedaan dari dua bilangan kompleks", "ad": "adalah bilangan kompleks untuk mengurangi inumber2!adalah bilangan kompleks untuk mengurangi dari inumber1"}, "IMSUM": {"a": "(inumber1; [inumber2]; ...)", "d": "Menampilkan penjumlahan bilangan kompleks", "ad": "dari 1 sampai 255 bilangan kompleks untuk ditambahkan"}, "IMTAN": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> tangen dari bilangan kompleks", "ad": "adalah bilangan kompleks yang Anda inginkan tangennya"}, "OCT2BIN": {"a": "(number; [places])", "d": "Mengonversi sebuah bilangan oktal ke biner", "ad": "adalah bilangan oktal yang ingin Anda konversi!adalah jumlah karakter untuk digunakan"}, "OCT2DEC": {"a": "(number)", "d": "Mengonversi sebuah bilangan oktal ke desimal", "ad": "ad<PERSON>h bilangan oktal yang ingin <PERSON>a konversi"}, "OCT2HEX": {"a": "(number; [places])", "d": "Mengonversi bilangan oktal ke hexadesimal", "ad": "adalah bilangan oktal yang ingin Anda konversi!adalah jumlah karakter untuk digunakan"}, "DAVERAGE": {"a": "(database; field; criteria)", "d": "Merata-rata nilai dalam kolom pada daftar atau database yang cocok dengan kondisi yang Anda tentukan", "ad": "adalah rentang sel yang membuat daftar atau database. Database adalah daftar dari data terkait!adalah label kolom dalam tanda kutip ganda atau angka yang menunjukkan posisi kolom dalam daftar!adalah rentang sel yang mengandung kondisi yang Anda tentukan. Rentang menyertakan label kolom dan sebuah sel di bawah label sebagai kondisi"}, "DCOUNT": {"a": "(database; field; criteria)", "d": "Menghitung sel yang mengandung angka dalam bidang (kolom) dari catatan dalam database yang sesuai dengan kondisi yang Anda tentukan", "ad": "adalah rentang sel yang membuat daftar atau database. Database adalah daftar dari data yang terkait!adalah label kolom dalam tanda kutip ganda atau angka yang menunjukkan posisi kolom dalam daftar!adalah rentang sel yang mengandung kondisi yang Anda tentukan. Rentang menyertakan label kolom dan satu sel di bawah label sebagai kondisi"}, "DCOUNTA": {"a": "(database; field; criteria)", "d": "Menghitung sel yang tidak kosong dalam bidang (kolom) dari catatan dalam database yang cocok dengan kondisi yang Anda tentukan", "ad": "adalah rentang sel yang membuat daftar atau database. Database adalah daftar dari data yang terkait!adalah label kolom dalam tanda kutip ganda atau angka yang menunjukkan posisi kolom dalam daftar!adalah rentang sel yang mengandung kondisi yang Anda tentukan. Rentang menyertakan label kolom dan satu sel di bawah label sebagai kondisi"}, "DGET": {"a": "(database; field; criteria)", "d": "Mengekstrak dari database rekaman tunggal yang cocok dengan kondisi yang Anda tentukan", "ad": "adalah rentang sel yang membuat daftar atau database. Database adalah daftar dari data yang terkait!adalah label kolom dalam tanda kutip ganda atau angka yang menunjukkan posisi kolom dalam daftar!adalah rentang sel yang mengandung kondisi yang Anda tentukan. Rentang menyertakan label kolom dan satu sel di bawah label sebagai kondisi"}, "DMAX": {"a": "(database; field; criteria)", "d": "Menampilkan angka terbesar dalam bidang (kolom) dari catatan dalam database yang sesuai dengan kondisi yang Anda tentukan", "ad": "adalah rentang dari sel yang membuat daftar atau database. Database adalah daftar dari data terkait!adalah label kolom dalam tanda kutip ganda atau angka yang menunjukkan posisi kolom dalam daftar!adalah rentang sel yang mengandung kondisi yang Anda tentukan. Rentang menyertakan label kolom dan satu sel di bawah label sebagai kondisi"}, "DMIN": {"a": "(database; field; criteria)", "d": "Menampilkan angka terkecil dalam bidang (kolom) dari catatan dalam database yang sesuai dengan kondisi yang Anda tentukan", "ad": "adalah rentang dari sel yang membuat daftar atau database. Database adalah daftar dari data terkait!adalah label kolom dalam tanda kutip ganda atau angka yang menunjukkan posisi kolom dalam daftar!adalah rentang sel yang mengandung kondisi yang Anda tentukan. Rentang menyertakan label kolom dan satu sel di bawah label sebagai kondisi"}, "DPRODUCT": {"a": "(database; field; criteria)", "d": "Mengalikan nilai dalam bidang (kolom) dari rekaman dalam database yang cocok dengan kondisi yang Anda tentukan", "ad": "adalah rentang sel yang membuat daftar atau database. Database adalah daftar dari data yang berhubungan!adalah label kolom dalam tanda petik ganda atau angka yang menunjukkan posisi kolom dalam daftar!adalah rentang sel yang mengandung kondisi yang Anda tentukan. Rentang menyertakan label kolom dan satu sel di bawah label sebagai kondisi"}, "DSTDEV": {"a": "(database; field; criteria)", "d": "<PERSON><PERSON><PERSON>rak<PERSON> standar deviasi berdasarkan pada contoh dari entri database terpilih", "ad": "adalah rentang dari sel yang membuat daftar atau database. Database adalah daftar dari data terkait!adalah label kolom dalam tanda kutip ganda atau angka yang menunjukkan posisi kolom dalam daftar!adalah rentang sel yang mengandung kondisi yang Anda tentukan. Rentang menyertakan label kolom dan satu sel di bawah label sebagai kondisi"}, "DSTDEVP": {"a": "(database; field; criteria)", "d": "Menghitung simpangan baku berdasarkan seluruh populasi entri database terpilih", "ad": "adalah rentang sel yang membuat daftar atau database. Database adalah daftar dari data yang terkait!adalah label kolom dalam tanda kutip ganda atau angka yang menunjukkan posisi kolom dalam daftar!adalah rentang sel yang mengandung kondisi yang Anda tentukan. Rentang menyertakan label kolom dan satu sel di bawah label sebagai kondisi"}, "DSUM": {"a": "(database; field; criteria)", "d": "Menambah angka dalam bidang (kolom) dari rekaman dalam database yang sesuai dengan kondisi yang Anda tentukan", "ad": "adalah rentang sel yang membuat daftar atau database. Database adalah daftar dari data terkait!adalah label kolom dalam tanda kutip ganda atau angka yang menunjukkan posisi kolom dalam daftar!adalah rentang sel yang mengandung kondisi yang Anda tentukan. Rentang menyertakan label kolom dan satu sel di bawah label sebagai kondisi"}, "DVAR": {"a": "(database; field; criteria)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> variansi berdasarkan contoh dari entri database terpilih", "ad": "adalah rentang dari sel yang membuat daftar atau database. Database adalah daftar dari data terkait!adalah label kolom dalam tanda kutip ganda atau angka yang menunjukkan posisi kolom dalam daftar!adalah rentang sel yang mengandung kondisi yang Anda tentukan. Rentang menyertakan label kolom dan satu sel di bawah label sebagai kondisi"}, "DVARP": {"a": "(database; field; criteria)", "d": "Menghitung variansi berdasarkan pada seluruh populasi entri database terpilih", "ad": "adalah rentang sel yang membuat daftar atau database. Database adalah daftar dari data terkait!adalah label kolom dalam tanda kutip ganda atau angka yang menunjukkan posisi kolom dalam daftar!adalah rentang sel yang mengandung kondisi yang Anda tentukan. Rentang menyertakan label kolom dan satu sel di bawah label sebagai kondisi"}, "CHAR": {"a": "(number)", "d": "Menampilkan karakter yang ditentukan oleh nomor kode dari perangkat karakter untuk komputer Anda", "ad": "adalah angka di antara 1 dan 255 yang menentukan karakter yang mana yang <PERSON>a inginkan"}, "CLEAN": {"a": "(text)", "d": "Menghapus semua karakter yang tidak dapat dicetak dari teks", "ad": "adalah informasi lembar kerja mana saja yang karakter tak-tercetaknya ingin <PERSON>a hapus"}, "CODE": {"a": "(text)", "d": "Menampilkan kode numerik untuk karakter pertama pada string teks, pada perangkat karakter yang digunakan oleh komputer Anda", "ad": "adalah teks yang Anda inginkan kodenya dari karakter pertama"}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "Gabungkan beberapa string teks ke dalam satu string teks", "ad": "adalah string teks 1 sampai 255 untuk digabungkan ke dalam string teks tunggal dan dapat berupa string teks, angka, atau referensi sel-tunggal"}, "CONCAT": {"a": "(text1; ...)", "d": "Menggabungkan daftar atau rentang string teks", "ad": "adalah 1 hingga 254 string teks atau rentang yang akan digabungkan menjadi string teks tunggal"}, "DOLLAR": {"a": "(number; [decimals])", "d": "Mengonversi angka ke teks, menggunakan format mata uang", "ad": "adalah angka, referensi ke sel yang mengandung angka, atau rumus yang mengevaluasi ke angka!adalah jumlah digit di sebelah kanan titik desimal. Angka dibulatkan jika perlu; jika dihilangkan, Desimal = 2"}, "EXACT": {"a": "(text1; text2)", "d": "Mengecek apakah kedua string teks tersebut tepat sama, dan men<PERSON><PERSON><PERSON>an BENAR atau SALAH. EXACT membedakan huruf besar dan kecil", "ad": "adalah string teks pertama!adalah string teks kedua"}, "FIND": {"a": "(find_text; within_text; [start_num])", "d": "Menampilkan posisi awal satu string teks dalam string teks lain. FIND membedakan huruf besar dan kecil", "ad": "adalah teks yang ingin Anda temukan. <PERSON><PERSON>n tanda kutip ganda (teks kosong) untuk mencocokkan karakter pertama pada Within_text; karakter bebas tidak diizinkan!adalah teks yang mengandung teks yang ingin Anda temukan!menentukan karakter yang mana untuk memulai pencarian. Karakter pertama pada Dalam_teks adalah karakter angka 1. Jika dihil<PERSON>, nomor_Mulai = 1"}, "FINDB": {"a": "(find_text; within_text; [start_num])", "d": "Menemukan satu string teks ketika string teks kedua, dan men<PERSON>an nomor posisi mulai string teks pertama dari karakter pertama string teks kedua, ditujukan untuk penggunaan dengan bahasa yang menggunakan kumpulan karakter byte ganda (DBCS) - Je<PERSON>g, Tionghoa, dan Korea", "ad": "adalah teks yang ingin Anda temukan. <PERSON><PERSON>n tanda kutip ganda (teks kosong) untuk mencocokkan karakter pertama pada Within_text; karakter bebas tidak diizinkan!adalah teks yang mengandung teks yang ingin Anda temukan!menentukan karakter yang mana untuk memulai pencarian. Karakter pertama pada Dalam_teks adalah karakter angka 1. Jika dihil<PERSON>, nomor_Mulai = 1"}, "FIXED": {"a": "(number; [decimals]; [no_commas])", "d": "Membulatkan angka ke angka desimal tertentu dan mengembalikan hasilnya sebagai teks dengan atau tanpa koma", "ad": "adalah angka yang ingin Anda bulatkan dan ubah ke teks!adalah jumlah digit di sebelah kanan titik desimal. Jika dihilangkan, Desimal = 2!adalah nilai logis: jangan tampilkan koma dalam teks yang dikembalikan = BENAR; tampilkan koma dalam teks yang dikembalikan = SALAH atau hilangkan"}, "LEFT": {"a": "(text; [num_chars])", "d": "Menampilkan jumlah karakter tertentu dari awal string teks", "ad": "adalah string teks yang mengandung karakter yang ingin Anda ekstrak!menentukan berapa banyak karakter yang Anda ingin LEFT ekstrak; 1 jika dihilangkan"}, "LEFTB": {"a": "(text; [num_chars])", "d": "Mengembalikan karakter pertama atau beberapa karakter dalam string teks, berdasarkan jumlah byte yang Anda tentukan, ditujukan untuk penggunaan dengan bahasa yang menggunakan kumpulan karakter byte ganda (DBCS) - <PERSON><PERSON><PERSON>, T<PERSON>ghoa, dan Korea", "ad": "adalah string teks yang mengandung karakter yang ingin Anda ekstrak!menentukan berapa banyak karakter yang Anda ingin LEFTB ekstrak; 1 jika dihilangkan"}, "LEN": {"a": "(text)", "d": "Menampilkan jumlah karakter dalam string teks", "ad": "adalah teks yang panjan<PERSON>a ingin <PERSON>a keta<PERSON>. Spasi dihitung sebagai karakter"}, "LENB": {"a": "(text)", "d": "Mengembalikan jumlah byte yang digunakan untuk menunjukkan karakter dalam string teks, ditujukan untuk penggunaan dengan bahasa yang menggunakan kumpulan karakter byte ganda (DBCS) - <PERSON><PERSON>g, Tionghoa, dan Korea", "ad": "adalah teks yang panjan<PERSON>a ingin <PERSON>a keta<PERSON>. Spasi dihitung sebagai karakter"}, "LOWER": {"a": "(text)", "d": "Mengonversi semua huruf dalam string teks ke huruf kecil", "ad": "adalah teks yang ingin Anda ubah ke huruf kecil. <PERSON><PERSON><PERSON> dalam Teks yang bukan huruf tidak diubah"}, "MID": {"a": "(text; start_num; num_chars)", "d": "Menampilkan karakter dari tengah string teks, memberikan posisi awal dan panjang", "ad": "adalah string teks yang dari sini Anda ingin mengekstrak karakternya!adalah posisi dari karakter pertama yang ingin Anda ekstrak. Karakter pertama dalam Teks adalah 1!tentukan berapa banyak karakter untuk dikembalikan dari Teks"}, "MIDB": {"a": "(text; start_num; num_chars)", "d": "Mengembalikan jumlah karakter tertentu dari sebuah string teks, dimulai dari posisi yang Anda tentukan, berdasarkan jumlah byte yang Anda tentukan, ditujukan untuk penggunaan dengan bahasa yang menggunakan kumpulan karakter byte ganda (DBCS) - Je<PERSON>g, Tionghoa, dan Korea", "ad": "adalah string teks yang dari sini Anda ingin mengekstrak karakternya!adalah posisi dari karakter pertama yang ingin Anda ekstrak. Karakter pertama dalam Teks adalah 1!tentukan berapa banyak karakter untuk dikembalikan dari Teks"}, "NUMBERVALUE": {"a": "(text; [decimal_separator]; [group_separator])", "d": "Mengonversi teks ke angka tanpa tergantung pada lokalnya", "ad": "adalah string yang menunjukkan angka yang ingin Anda konversi!adalah karakter yang digunakan sebagai pemisah desimal di string ini!adalah karakter yang digunakan sebagai pemisah grup di string tersebut"}, "PROPER": {"a": "(text)", "d": "Mengonversi string teks ke huruf judul; huruf pertama pada tiap kata berhuruf besar, dan semua huruf lain berhuruf kecil", "ad": "adalah teks yang tercakup dalam tanda kutip, rumus yang menge<PERSON>likan teks, atau referensi ke sel yang memuat teks untuk diubah ke huruf besar sebagian"}, "REPLACE": {"a": "(old_text; start_num; num_chars; new_text)", "d": "Mengganti bagian dari string teks dengan string teks yang lain", "ad": "adalah teks yang Anda ingin ganti beberapa karakternya!adalah posisi karakter dalam teks_Lama yang ingin Anda ganti dengan teks_Baru!adalah jumlah karakter dalam teks_Lama yang ingin Anda ganti!adalah teks yang akan mengganti karakter dalam teks_Lama"}, "REPLACEB": {"a": "(old_text; start_num; num_chars; new_text)", "d": "Menggantikan bagian dari string teks, berdasarkan jumlah byte yang Anda tentukan, dengan string teks berbeda, ditujukan untuk penggunaan dengan bahasa yang menggunakan kumpulan karakter byte ganda (DBCS) - <PERSON><PERSON><PERSON>, T<PERSON>ghoa, dan Korea", "ad": "adalah teks yang Anda ingin ganti beberapa karakternya!adalah posisi karakter dalam teks_Lama yang ingin Anda ganti dengan teks_Baru!adalah jumlah karakter dalam teks_Lama yang ingin Anda ganti!adalah teks yang akan mengganti karakter dalam teks_Lama"}, "REPT": {"a": "(text; number_times)", "d": "Mengulangi teks sesuai yang ditentukan. Gunakan REPT untuk mengisi sel dengan sejumlah contoh string teks", "ad": "adalah teks yang ingin Anda ulang<PERSON>!adalah angka positif yang menentukan berapa kali teks diulangi"}, "RIGHT": {"a": "(text; [num_chars])", "d": "Menampilkan jumlah karakter tertentu dari bagian akhir string teks", "ad": "adalah string teks yang mengandung karakter yang ingin Anda ekstrak!menentukan berapa banyak karakter yang ingin Anda ekstrak; 1 jika dihilangkan"}, "RIGHTB": {"a": "(text; [num_chars])", "d": "Menampilkan karakter terakhir atau karakter dalam string teks, berdasarkan jumlah byte yang Anda tentukan, ditujukan untuk penggunaan dengan bahasa yang menggunakan kumpulan karakter byte ganda (DBCS) - <PERSON><PERSON><PERSON>, T<PERSON>ghoa, dan Korea", "ad": "adalah string teks yang mengandung karakter yang ingin Anda ekstrak!menentukan berapa banyak karakter yang ingin Anda ekstrak; 1 jika dihilangkan"}, "SEARCH": {"a": "(find_text; within_text; [start_num])", "d": "Menampilkan jumlah dari karakter saat karakter atau string teks tertentu ditemukan pertama kali, membaca dari kiri ke kanan (huruf besar dan kecil tidak dibedakan)", "ad": "adalah teks yang ingin Anda temukan. Anda dapat menggunakan karakter bebas ? dan  *; gunakan ~? dan ~* untuk menemukan ? dan karakter *!adalah teks yang ingin Anda cari Temukan_teks!adalah angka karakter dalam Within_text, dihitung dari kiri, saat Anda ingin memulai pencarian. <PERSON><PERSON>, 1 digunakan"}, "SEARCHB": {"a": "(find_text; within_text; [start_num])", "d": "Menemukan satu string teks dalam string teks kedua, dan men<PERSON>an nomor posisi awal string teks pertama dari karakter pertama string teks kedua, ditujukan untuk penggunaan dengan bahasa yang menggunakan kumpulan karakter byte ganda (DBCS) - <PERSON><PERSON><PERSON>, <PERSON><PERSON>ghoa, dan Korea", "ad": "adalah teks yang ingin Anda temukan. Anda dapat menggunakan karakter bebas ? dan  *; gunakan ~? dan ~* untuk menemukan ? dan karakter *!adalah teks yang ingin Anda cari Temukan_teks!adalah angka karakter dalam Within_text, dihitung dari kiri, saat Anda ingin memulai pencarian. <PERSON><PERSON>, 1 digunakan"}, "SUBSTITUTE": {"a": "(text; old_text; new_text; [instance_num])", "d": "Mengganti teks yang ada dengan teks baru dalam string teks", "ad": "adalah teks atau referensi ke sel yang mengandung teks yang ingin Anda ganti karakternya!adalah teks yang ada yang ingin Anda ganti. <PERSON>ka huruf teks_Lama tidak cocok dengan huruf teks, SUBSTITUTE tidak akan mengganti teks tersebut!adalah teks yang ingin Anda ganti dengan teks_Lama!tentukan kemunculan teks_Lama yang mana yang ingin Anda ganti. <PERSON><PERSON>, tiap contoh dari teks_Lama akan diganti"}, "T": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON> apakah nilai adalah teks, dan menge<PERSON><PERSON>an teksnya jika benar, atau mengembalikan tanda petik ganda (teks kosong) jika tidak benar", "ad": "adalah nilai untuk dites"}, "TEXT": {"a": "(value; format_text)", "d": "Mengonversi nilai ke teks dalam format angka tertentu", "ad": "adalah angka, formula yang mengevaluasi ke nilai numerik, atau referensi ke sel yang mengandung nilai numerik!adalah format angka dalam bentuk teks dari kotak Kategori pada tab Angka di kotak dialog Format Sel"}, "TEXTJOIN": {"a": "(delimiter; ignore_empty; text1; ...)", "d": "Menggabungkan daftar atau rentang string teks menggunakan pembatas", "ad": "Karakter atau string yang akan disisipkan di antara setiap item teks!Jika TRUE(default), mengabaikan sel kosong!adalah string teks 1 hingga 252 atau rentang yang akan digabungkan"}, "TRIM": {"a": "(text)", "d": "Menghapus semua spasi dari string teks kecuali untuk spasi tunggal di antara kata", "ad": "adalah teks yang ingin Anda hapus spasinya"}, "UNICHAR": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON>an karakter Unicode yang diacu oleh nilai numerik te<PERSON>", "ad": "adalah angka Unicode yang menun<PERSON>kkan karakter"}, "UNICODE": {"a": "(text)", "d": "Mengembalikan angka (poin kode) yang sesuai dengan karakter pertama teks", "ad": "adalah karakter yang Anda inginkan nilai Unicode-nya"}, "UPPER": {"a": "(text)", "d": "Mengonversi string teks menjadi huruf besar semua", "ad": "adalah teks yang ingin Anda ubah ke huruf besar, referensi atau string teks"}, "VALUE": {"a": "(text)", "d": "Mengonversi string teks yang menun<PERSON>n angka ke angka", "ad": "adalah teks yang dilampirkan dalam tanda kutip atau referensi ke sel yang mengandung teks yang ingin Anda ubah"}, "AVEDEV": {"a": "(number1; [number2]; ...)", "d": "Menampilkan rata-rata simpangan absolut poin data dari nilai rata-ratanya. Argumen dapat berupa angka atau nama, aray atau referensi yang mengandung angka", "ad": "adalah argumen 1 sampai 255 dimana Anda menginginkan rata-rata dari simpangan absolutnya"}, "AVERAGE": {"a": "(number1; [number2]; ...)", "d": "Menampilkan rata-rata (nilai rata-rata aritmatika) dari argume<PERSON>a, yang dapat berupa angka atau nama, array, atau referensi yang mengandung angka", "ad": "adalah argumen numerik 1 sampai 255 yang Anda inginkan rata-ratanya"}, "AVERAGEA": {"a": "(value1; [value2]; ...)", "d": "Menampilkan rata-rata (nilai rata-rata aritmatika) dari argume<PERSON><PERSON>, mengevaluasi teks dan SALAH dalam argumen sebagai 0; BENAR sebagai 1. Argumen dapat berupa angka atau nama, array, atau referensi", "ad": "adalah argumen 1 sampai 255 yang rata-ratanya <PERSON><PERSON>an"}, "AVERAGEIF": {"a": "(range; criteria; [average_range])", "d": "<PERSON><PERSON><PERSON> rata-rata(rata-rata aritmatik)untuk sel yang ditentukan oleh kondisi atau kriteria yang diberikan", "ad": "adalah rentang sel yang ingin Anda evaluasi!adalah kondisi atau kriteria dalam bentuk bilangan, eks<PERSON><PERSON>i, atau teks yang menetapkan sel yang akan digunakan untuk menemukan rata-rata!adalah sel aktual yang akan digunakan untuk menemukan rata-rata. <PERSON><PERSON>, sel dalam rentang akan digunakan"}, "AVERAGEIFS": {"a": "(average_range; criteria_range; criteria; ...)", "d": "<PERSON><PERSON><PERSON> rata-rata(rata-rata arit<PERSON><PERSON>) untuk sel yang ditentukan dengan diberikan sebuah rangkaian kondisi atau kriteria", "ad": "adalah sel aktual yang digunakan untuk menemukan rata-rata.!adalah rentang sel yang Anda ingin evaluasi untuk kondisi tertentu!adalah kondisi atau kriteria dalam formulir angka, ekspresi, atau teks yang menetapkan sel mana yang akan digunakan untuk menemukan rata-rata"}, "BETADIST": {"a": "(x; alpha; beta; [A]; [B])", "d": "Mengembalikan fungsi kepadatan probabilitas beta kumulatif", "ad": "adalah nilai di antara A dan B untuk mengevaluasi fungsi!adalah parameter untuk distribusi dan harus lebih besar daripada 0.!adalah parameter untuk distribusi dan harus lebih besar daripada 0.!adalah batas bawah opsional untuk interval x. <PERSON><PERSON> dihilangkan, A = 0!adalah batas atas opsional untuk interval x. <PERSON><PERSON> dihilangkan, B = 1"}, "BETAINV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "Mengembalikan inversi dari fungsi kepadatan probabilitas kumulatif beta (BETADIST)", "ad": "adalah probabilitas yang terkait dengan distribusi beta!adalah parameter untuk distribusi dan harus lebih besar dari 0!adalah parameter untuk distribusi dan harus lebih besar dari 0!adalah batas bawah opsional untuk interval x. <PERSON><PERSON> dihilangkan, A = 0!adalah batas atas opsional untuk interval x. <PERSON><PERSON> dihil<PERSON>, B = 1"}, "BETA.DIST": {"a": "(x; alpha; beta; cumulative; [A]; [B])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> fungsi distribusi probabilitas beta", "ad": "adalah nilai di antara A dan B untuk mengevaluasi fungsi!adalah parameter untuk distribusi dan harus lebih besar daripada 0!adalah parameter untuk distribusi dan harus lebih besar daripada 0!adalah nilai logis: untuk fungsi distribusi kumulatif, gunakan TRUE; untuk fungsi kerapatan probabilitas, gunakan FALSE!adalah batas bawah opsional ke interval x. Jika dihilangkan, A = 0!adalah batas atas opsional ke interval x. Jika dihilangkan, B = 1"}, "BETA.INV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> balikan dari fungsi kerapatan probabilitas kumulatif beta (BETA.DIST)", "ad": "adalah probabilitas yang terkait dengan distribusi beta!adalah parameter untuk distribusi dan harus lebih besar daripada 0!adalah parameter untuk distribusi dan harus lebih besar daripada 0!adalah batas bawah opsional ke interval x. <PERSON><PERSON> dihil<PERSON>, A = 0!adalah batas atas opsional ke interval x. <PERSON><PERSON> dihil<PERSON>, B = 1"}, "BINOMDIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "Mengembalikan probabilitas distribusi binomial istilah individu", "ad": "adalah jumlah keberhasilan dalam percobaan!adalah jumlah percobaan independen!adalah probabilitas keberhasilan pada tiap percobaan!adalah nilai logis: untuk fungsi distribusi kumulatif, gunakan TRUE; untuk fungsi massa probabilitas, gunakan FALSE"}, "BINOM.DIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "Menampilkan probabilitas distribusi binomial term individu", "ad": "adalah jumlah kesuksesan dalam percobaan!adalah jumlah percobaan independen!adalah kemungkinan keberhasilan pada tiap percobaan!adalah nilai logis: untuk fungsi distribusi kumula<PERSON><PERSON>, gunakan BENAR; untuk fungsi massa probabilitas, gunakan SALAH"}, "BINOM.DIST.RANGE": {"a": "(trials; probability_s; number_s; [number_s2])", "d": "<PERSON><PERSON><PERSON><PERSON>an probabilitas hasil percobaan dengan distribusi binomial", "ad": "adalah jumlah percobaan independen!adalah probabilitas keberhasilan di setiap percobaan!adalah jumlah keberhasilan dalam percobaan!jika di<PERSON>ikan, fungsi ini mengh<PERSON>lkan probabilitas yang jumlah percobaan berhasilnya harus antara number_s dan number_s2"}, "BINOM.INV": {"a": "(trials; probability_s; alpha)", "d": "Menampilkan nilai terkecil di mana distribusi kumulatif binomial lebih besar dari atau sama dengan nilai standar", "ad": "adalah jumlah perco<PERSON> Bern<PERSON>lli!adalah probabilitas keberhasilan dalam tiap per<PERSON><PERSON>, yaitu angka di antara 0 sampai dengan 1!adalah nilai standar, yaitu angka di antara 0 sampai dengan 1"}, "CHIDIST": {"a": "(x; deg_freedom)", "d": "Mengembalikan probabilitas sisi-kanan dari distribusi khi-kuadrat", "ad": "adalah nilai di mana Anda ingin mengevaluasi distribusi, angka bukan negatif!adalah jumlah derajat kebeb<PERSON>n, angka di antara 1 dan 10^10, tidak termasuk 10^10"}, "CHIINV": {"a": "(probability; deg_freedom)", "d": "Mengembalikan inversi dari probabilitas sisi-kanan dari distribusi khi-kuadrat", "ad": "adalah probabilitas yang terkait dengan distribusi khi-kuadrat, nilai di antara 0 dan 1!adalah jumlah derajat kebeb<PERSON>n, angka di antara 1 dan 10^10, tidak termasuk 10^10"}, "CHITEST": {"a": "(actual_range; expected_range)", "d": "Mengembalikan uji independensi: nilai dari distribusi khi-kuadrat untuk statistik dan derajat kebebasan yang tepat", "ad": "adalah rentang data yang memuat observasi untuk diuji terhadap nilai yang diharapkan!adalah rentang data yang memuat rasio produk dari total baris dan total kolom ke total keseluruhan"}, "CHISQ.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "<PERSON><PERSON><PERSON><PERSON>an probabilitas lemparan-kiri dari distribusi khi-kuadrat", "ad": "adalah nilai yang ingin Anda ingin gunakan untuk mengevaluasi distribusi, bilangan non-negatif!adalah jumlah pangkat, yaitu angka di antara 1 dan 10^10, tidak termasuk 10^10!adalah nilai logis untuk fungsi yang muncul: fungsi distribusi kumulatif = TRUE; fungsi kerapatan probabilitas = FALSE"}, "CHISQ.DIST.RT": {"a": "(x; deg_freedom)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> probabilitas lemparan-kanan dari distribusi khi-kuadrat", "ad": "adalah nilai yang ingin Anda gunakan untuk mengevaluasi distribusi, bilangan non-negatif!adalah jumlah pangkat, yaitu angka di antara 1 dan 10^10, tidak termasuk 10^10"}, "CHISQ.INV": {"a": "(probability; deg_freedom)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> balikan dari probabilitas lemparan-kiri dari distribusi khi-kuadrat", "ad": "adalah probabilitas yang terkait dengan distribusi khi-kuadrat, termasuk nilai di antara 0 sampai 1!adalah jumlah pangkat, yaitu angka di antara 1 dan 10^10, tidak termasuk 10^10"}, "CHISQ.INV.RT": {"a": "(probability; deg_freedom)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> balikan dari probabilitas lemparan-kanan dari distribusi khi-kuadrat", "ad": "adalah probabilitas yang terkait dengan distribusi khi-kuadrat, termasuk nilai di antara 0 sampai 1!adalah jumlah pangkat, yaitu angka di antara 1 dan 10^10, tidak termasuk 10^10"}, "CHISQ.TEST": {"a": "(actual_range; expected_range)", "d": "Menampilkan tes untuk independen: nilai dari distribusi khi-kuadrat untuk statistik dan pangkat yang tepat", "ad": "adalah rentang data yang mengandung observasi untuk dites terhadap nilai yang diharapkan!adalah rentang data yang mengandung rasio produk dari total baris dan kolom ke total seluruhnya"}, "CONFIDENCE": {"a": "(alpha; standard_dev; size)", "d": "Mengembalikan interval kepercayaan untuk mean populasi, menggunakan distribusi normal", "ad": "adalah tingkat kepentingan yang digunakan untuk menghitung tingkat kepercayaan, angka yang lebih besar dari 0 dan lebih kecil dari 1!adalah simpangan baku populasi untuk rentang data dan diasumsikan diketahui. Standar_dev harus lebih besar dari 0!adalah ukuran contoh"}, "CONFIDENCE.NORM": {"a": "(alpha; standard_dev; size)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> interval k<PERSON><PERSON>yaan untuk rata-rata populasi, dengan distribusi normal", "ad": "adalah tingkat signifikansi yang digunakan untuk menghitung tingkat kepercayaan, angka yang lebih besar daripada 0 dan lebih kecil daripada 1!adalah simpangan standar populasi untuk rentang data tersebut dan dianggap diketahui. Standard_dev harus lebih besar daripada 0!adalah ukuran sampel"}, "CONFIDENCE.T": {"a": "(alpha; standard_dev; size)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> interval k<PERSON><PERSON>yaan untuk rata-rata populasi, dengan distribusi T <PERSON>elajar", "ad": "adalah tingkat signifikansi yang digunakan untuk menghitung tingkat kepercayaan, angka yang lebih besar daripada 0 dan lebih kecil daripada 1!adalah simpangan standar populasi untuk rentang data tersebut dan dianggap diketahui. Standard_dev harus lebih besar daripada 0!adalah ukuran sampel"}, "CORREL": {"a": "(array1; array2)", "d": "Menampilkan koefisien korelasi di antara dua perangkat data", "ad": "adalah rentang sel nilai. Nilai harus berupa angka, nama, array atau referensi yang mengandung angka!adalah rentang sel nilai kedua. Nilai harus berupa angka, nama, array, atau referensi yang mengandung angka"}, "COUNT": {"a": "(value1; [value2]; ...)", "d": "Menghitung jumlah sel dalam rentang yang memuat angka", "ad": " adalah 1 sampai 255 argumen yang memuat atau mengacu tipe data yang berva<PERSON>, tapi hanya angka yang dihitung"}, "COUNTA": {"a": "(value1; [value2]; ...)", "d": "Menghitung jumlah sel dalam rentang yang tidak kosong", "ad": "adalah argumen 1 sampai 255 argumen yang menunjukkan nilai dan sel yang ingin Anda hitung. <PERSON><PERSON> dapat berupa tipe informasi apa saja"}, "COUNTBLANK": {"a": "(range)", "d": "Menghitung jumlah sel kosong dalam rentang sel tertentu", "ad": "adalah rentang di mana Anda ingin menghitung sel yang kosong"}, "COUNTIF": {"a": "(range; criteria)", "d": "Menghitung jumlah sel dalam rentang yang sesuai dengan kondisi yang diberikan", "ad": "adalah rentang sel di mana Anda ingin menghitung sel yang berisi!adalah kondisi dalam bentuk angka, ekspresi, atau teks yang menetapkan sel yang ingin dihitung"}, "COUNTIFS": {"a": "(criteria_range; criteria; ...)", "d": "Hitung jumlah sel yang ditentukan oleh pemberian set dari kondisi atau kriteria", "ad": "adalah rentang sel yang Anda ingin evaluasi untuk kondisi tertentu!adalah kondisi dalam formulir bilangan, ekspresi, atau teks yang menetapkan sel mana yang akan dihitung"}, "COVAR": {"a": "(array1; array2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, rata-rata produk dari simpangan untuk tiap pasangan titik data dalam dua perangkat data", "ad": "adalah rentang sel bilangan bulat pertama dan harus berupa angka, array, atau referensi yang memuat angka!adalah rentang sel bilangan bulat kedua dan harus berupa angka, array, atau referensi yang memuat angka"}, "COVARIANCE.P": {"a": "(array1; array2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON>, rata-rata produk dari simpangan untuk tiap pasangan poin data dalam dua set data", "ad": "adalah rentang sel bilangan bulat pertama dan harus berupa angka, array, atau referensi yang berisi angka!adalah rentang sel bilangan bulat kedua dan harus berupa angka, array, atau referensi yang berisi angka"}, "COVARIANCE.S": {"a": "(array1; array2)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> samp<PERSON>, rata-rata produk dari simpangan untuk tiap pasangan poin data dalam dua set data", "ad": "adalah rentang sel bilangan bulat pertama dan harus berupa angka, array, atau referensi yang berisi angka!adalah rentang sel bilangan bulat kedua dan harus berupa angka, array, atau referensi yang berisi angka"}, "CRITBINOM": {"a": "(trials; probability_s; alpha)", "d": "Menampilkan nilai terkecil di mana distribusi binomial kumulatif lebih besar dari atau sama dengan nilai kriteria", "ad": "adalah jumlah per<PERSON>lli!adalah probabilitas keberhasilan dalam tiap per<PERSON>, angka di antara 0 dan 1 (termasuk keduanya)!adalah nilai kriteria, angka di antara 0 dan 1 (termasuk keduanya)"}, "DEVSQ": {"a": "(number1; [number2]; ...)", "d": "Menampilkan penju<PERSON>lahan kuadrat simpangan poin data dari nilai rata-rata contoh poin", "ad": "adalah argumen, atau array atau referensi array 1 sampai 255, di mana Anda ingin DEVSQ menghitungnya"}, "EXPONDIST": {"a": "(x; lambda; cumulative)", "d": "Mengembalikan distribusi eksponensial", "ad": "adalah nilai fungsi, angka bukan negatif!adalah nilai parameter, angka positif!adalah nilai logis yang harus dikembalikan fungsi: fungsi distribusi kumulatif = TRUE; fungsi kepadatan probabilitas = FALSE"}, "EXPON.DIST": {"a": "(x; lambda; cumulative)", "d": "Menampilkan distribusi eksponensial", "ad": "adalah nilai fungsi, yaitu angka bukan negatif!adalah nilai parameter, yaitu angka positif!adalah nilai logika untuk fungsi yang muncul: fungsi distribusi kumulatif = BENAR; fungsi kerapatan probabilitas = SALAH"}, "FDIST": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "Mengembalikan distribusi probabilitas F (sisi-kanan) untuk dua set data", "ad": "adalah nilai untuk mengevaluasi fungsi, angka bukan negatif!adalah derajat kebebasan pembilang, angka di antara 1 dan 10^10, tidak termasuk 10^10!adalah derajat kebebasan penyebut, angka di antara 1 dan 10^10, tidak termasuk 10^10"}, "FINV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Mengembalikan inversi distribusi probabilitas F (sisi-kanan): jika p = FDIST(x,...), maka FINV(p,...) = x", "ad": "adalah probabilitas yang terkait dengan distribusi kumulatif F, angka di antara 0 dan 1 (termasuk keduanya)!adalah derajat kebebasan pembilang, angka di antara 1 dan 10^10, tidak termasuk 10^10!adalah derajat kebebasan penyebut, angka di antara 1 dan 10^10, tidak termasuk 10^10"}, "FTEST": {"a": "(array1; array2)", "d": "Men<PERSON><PERSON><PERSON><PERSON> hasil uji-F, probabilitas dua-sisi di mana varian dalam Array1 dan Array2 tidak memiliki perbedaan yang signifikan", "ad": "adalah array atau rentang data pertama dan dapat berupa angka atau nama, array, atau referensi yang memuat angka (kosong diabaikan)!adalah array atau rentang data kedua dan dapat berupa angka atau nama, array, atau referensi yang memuat angka (kosong diabaikan)"}, "F.DIST": {"a": "(x; deg_freedom1; deg_freedom2; cumulative)", "d": "Menghasilkan distribusi probabilitas F (lemparan-kiri) untuk dua set data", "ad": "adalah nilai untuk mengevaluasi fungsi, yaitu bilangan non-negatif!adalah pangkat pembilang, yaitu angka di antara 1 dan 10^10, tidak termasuk 10^10!adalah pangkat angka penyebut, yaitu angka di antara 1 dan 10^10, tidak termasuk 10^10!adalah nilai logis untuk fungsi yang muncul: fungsi distribusi kumulatif = TRUE; fungsi kerapatan probabilitas = FALSE"}, "F.DIST.RT": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "Men<PERSON><PERSON>lkan distribusi probabilitas F (lemparan-kanan) untuk dua set data", "ad": "adalah nilai untuk mengevaluasi fungsi, yaitu bilangan non-negatif!adalah pangkat pembilang, yaitu angka di antara 1 dan 10^10, tidak termasuk 10^10!adalah pangkat angka penyebut, yaitu angka di antara 1 dan 10^10, tidak termasuk 10^10"}, "F.INV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Men<PERSON><PERSON>lkan balikan distribusi probabilitas F (lemparan-kiri): jika p = F.DIST(x,...), maka F.INV(p,...) = x", "ad": "adalah probabilitas yang terkait dengan distribusi kumulatif F, angka di antara 0 sampai dengan 1!adalah pangkat pembilang, yaitu angka di antara 1 dan 10^10, tidak termasuk 10^10!adalah pangkat angka penyebut, yaitu angka di antara 1 dan 10^10, tidak termasuk 10^10"}, "F.INV.RT": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "<PERSON><PERSON><PERSON><PERSON>an balikan distribusi probabilitas F (lemparan-kanan): jika p = F.DIST(x,...), maka F.INV.RT(p,...) = x", "ad": "adalah probabilitas yang terkait dengan distribusi kumulatif F, angka di antara 0 sampai dengan 1!adalah pangkat pembilang, yaitu angka di antara 1 dan 10^10, tidak termasuk 10^10!adalah pangkat angka penyebut, yaitu angka di antara 1 dan 10^10, tidak termasuk 10^10"}, "F.TEST": {"a": "(array1; array2)", "d": "Menampilkan hasil tes-F, probabilitas dua lemparan di mana variansi dalam Array1 dan Array2 tidak memiliki perbedaan yang berarti", "ad": "adalah array atau rentang data pertama dan dapat berupa angka atau nama, array, atau referensi yang mengandung angka (spasi diabaikan)!adalah array atau rentang data kedua dan dapat berupa angka atau nama, array, atau referensi yang mengandung angka (spasi diabaikan)"}, "FISHER": {"a": "(x)", "d": "Menampilkan transformasi Fisher", "ad": "ad<PERSON>h angka di mana Anda menginginkan transformasi, yaitu angka di antara -1 dan 1, mengecualikan -1 dan 1"}, "FISHERINV": {"a": "(y)", "d": "Menampilkan invers dari transformasi Fisher: Jika y = FISHER(x), maka FISHERINV(y) = x", "ad": "adalah nilai di mana Anda ingin melakukan invers dari transfomasinya"}, "FORECAST": {"a": "(x; known_y's; known_x's)", "d": "<PERSON><PERSON><PERSON><PERSON>, at<PERSON> me<PERSON><PERSON>, nilai mendatang beserta tren linear menggunakan nilai yang ada", "ad": "merupakan titik data yang ingin Anda prediksi nilainya dan harus berupa nilai numerik!merupakan array atau rentang dependen data numerik!merupakan array atau rentang independen data numerik. Variansi Known_x's tidak boleh nol"}, "FORECAST.ETS": {"a": "(tanggal_target; nilai; garis_waktu; [musiman]; [penyelesaian_data]; [agregat])", "d": "Mengembalikan nilai prakiraan untuk tanggal target tertentu di masa depan menggunakan metode pemulusan eksponensial.", "ad": "adalah poin data yang diprediksi nilainya oleh Spreadsheet Editor. Ini harus meneruskan pola nilai dalam garis waktu.!adalah array atau rentang data numerik yang sedang Anda prediksi.!adalah array independen atau rentang data numerik. Tanggal dalam garis waktu harus memiliki langkah yang konsisten di antara tanggal tersebut dan tidak boleh nol.!adalah nilai numerik opsional yang menunjukkan panjang pola musiman. Nilai default 1 menunjukkan musiman terdeteksi secara otomatis.!adalah nilai opsional untuk mengatasi nilai yang hilang. Nilai default 1 menggantikan nilai yang hilang dengan interpolasi, dan 0 menggantikannya dengan nol.!adalah nilai numerik opsional untuk menggabungkan beberapa nilai dengan stempel waktu yang sama. Jika kosong, Spreadsheet Editor merata-ratakan nilai."}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "Mengembalikan interval kepercayaan untuk nilai prakiraan pada tanggal target yang ditentukan.", "ad": "adalah poin data yang diprediksi nilainya oleh Spreadsheet Editor. Ini harus meneruskan pola nilai dalam garis waktu.!adalah array atau rentang data numerik yang sedang Anda prediksi.!adalah array independen atau rentang data numerik. Tanggal dalam garis waktu harus memiliki langkah yang konsisten di antara tanggal tersebut dan tidak boleh nol.!adalah angka antara 0 dan 1, menunjukkan tingkat keyakinan untuk interval kepercayaan yang dihitung. <PERSON><PERSON> default adalah ,95.!adalah nilai numerik opsional yang menunjukkan panjang pola musiman. Nilai default 1 menunjukkan musiman terdeteksi secara otomatis.!adalah nilai opsional untuk mengatasi nilai yang hilang. Nilai default 1 menggantikan nilai yang hilang dengan interpolasi, dan 0 menggantikannya dengan nol.!adalah nilai numerik opsional untuk menggabungkan beberapa nilai dengan stempel waktu yang sama. <PERSON><PERSON> koso<PERSON>, Spreadsheet Editor merata-rat<PERSON><PERSON> nilai."}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "Mengembalikan panjang pola berulang yang dideteksi oleh aplikasi untuk rangkaian waktu tertentu.", "ad": "adalah array atau rentang data numerik yang sedang Anda prediksi.!adalah array independen atau rentang data numerik. Tanggal dalam garis waktu harus memiliki langkah yang konsisten di antara tanggal tersebut dan tidak boleh nol.!adalah nilai optimal untuk mengatasi nilai yang hilang. <PERSON>lai default 1 menggantikan nilai hilang dengan interpolasi, dan 0 menggantikannya dengan nol.!adalah nilai numerik opsional untuk menggabungkan banyak nilai dengan stempel waktu yang sama. <PERSON><PERSON> koso<PERSON>, Spreadsheet Editor merata-ratakan nilainya."}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "Mengembalikan statistik diminta untuk prakiraan.", "ad": "adalah array atau rentang data numerik yang sedang Anda prediksi.!adalah array independen atau rentang data numerik. Tanggal dalam garis waktu harus memiliki langkah yang konsisten di antara tanggal tersebut dan tidak boleh nol.!adalah angka antara 1 dan 8, mengindikasikan statistik Spreadsheet Editor mana yang akan dihasilkan untuk prakiraan yang dihitung.!adalah nilai numerik opsional yang mengindikasikan panjang pola musiman. Nilai default 1 mengindikasikan musiman terdeteksi secara otomatis.!adalah nilai opsional untuk mengatasi nilai yang hilang. Nilai default 1 menggantikan nilai hilang dengan interpolasi, dan 0 menggantikannya dengan nol.!adalah nilai numerik opsional untuk menggabungkan banyak nilai dengan stempel waktu yang sama. <PERSON><PERSON> kosong, Spreadsheet Editor merata-ratakan nilai."}, "FORECAST.LINEAR": {"a": "(x; known_y's; known_x's)", "d": "<PERSON><PERSON><PERSON><PERSON>, at<PERSON> me<PERSON><PERSON>, nilai mendatang beserta tren linear menggunakan nilai yang ada", "ad": "merupakan titik data yang ingin Anda prediksi nilainya dan harus berupa nilai numerik!merupakan array atau rentang dependen data numerik!merupakan array atau rentang independen data numerik. <PERSON><PERSON>i dari Known_x's tidak boleh nol"}, "FREQUENCY": {"a": "(data_array; bins_array)", "d": "Menghitung seberapa sering nilai terdapat dalam rentang nilai kemudian kembalikan array vertikal dari angka yang memiliki lebih dari satu elemen dari array_Bins", "ad": "adalah array dari atau referensi ke perangkat nilai yang ingin Anda hitung frekuensinya (spasi dan teks diabaikan)!adalah array dari atau referensi ke interval yang menjadi dasar pengelompokan nilai dalam array_data"}, "GAMMA": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> nilai fun<PERSON>i <PERSON>", "ad": "adalah nilai di mana Anda ingin menghitung <PERSON>"}, "GAMMADIST": {"a": "(x; alpha; beta; cumulative)", "d": "Mengembalikan distribusi gamma", "ad": "adalah nilai di mana Anda ingin mengevaluasi distribusi, angka bukan negatif!adalah parameter untuk distribusi, angka positif!adalah parameter untuk distribusi, angka positif. Jika beta = 1, GAMMADIST mengembalikan distribusi gamma standar!adalah nilai logis: mengembalikan fungsi distribusi kumulatif = TRUE; mengembalikan fungsi massa probabilitas = FALSE atau dihilangkan"}, "GAMMA.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> distribusi gamma", "ad": "adalah nilai yang ingin Anda gunakan untuk mengevaluasi distribusi, yaitu bilangan non-negatif!adalah parameter untuk distribusi, yaitu bilangan positif!adalah parameter untuk distribusi, yaitu bilangan positif. Jika beta = 1, GAMMADIST menghasilkan distribusi gamma standar!adalah nilai logis: menghasilkan fungsi distribusi kumulatif = TRUE; menghasilkan fungsi massa probabilitas = FALSE atau dihilangkan"}, "GAMMAINV": {"a": "(probability; alpha; beta)", "d": "Mengembalikan inversi distribusi kumulatif gamma: jika p = GAMMADIST(x,...), maka GAMMAINV(p,...) = x", "ad": "adalah probabilitas yang terkait dengan distribusi gamma, angka antara 0 dan 1!adalah parameter untuk distribusi, angka positif!adalah parameter untuk distribusi, angka positif. Jika beta = 1, GAMMAINV mengembalikan inversi distribusi gamma standar"}, "GAMMA.INV": {"a": "(probability; alpha; beta)", "d": "<PERSON><PERSON><PERSON><PERSON>an balikan distribusi kumulatif gamma: jika p = GAMMADIST(x,...), maka GAMMAINV(p,...) = x", "ad": "adalah probabilitas yang terkait dengan distribusi gamma, yaitu bilangan di antara 0 dan 1!adalah parameter untuk distribusi, yaitu bilangan positif!adalah parameter untuk distribusi, yaitu bilangan positif. Jika beta = 1, GAMMAINV menghasilkan balikan distribusi gamma standar"}, "GAMMALN": {"a": "(x)", "d": "Menampilkan logaritma alami fungsi gamma", "ad": "adalah nilai di mana Anda ingin menghitung GAMMALN, angka positif"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> logaritma alami fungsi gamma", "ad": "adalah nilai di mana Anda ingin menghitung GAMMALN.PRECISE, bilangan positif"}, "GAUSS": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> 0,5 kurang dari distribusi kumulatif normal standar", "ad": "adalah nilai yang Anda inginkan distribusinya"}, "GEOMEAN": {"a": "(number1; [number2]; ...)", "d": "Menampilkan nilai rata-rata geometrik array atau rentang data numerik yang positif", "ad": "adalah angka atau nama, array, atau referensi 1 sampai 255 yang mengandung angka di mana Anda inginkan nilai rata-ratanya"}, "GROWTH": {"a": "(known_y's; [known_x's]; [new_x's]; [const])", "d": "Mengembalikan angka dalam tren pertumbuhan eksponensial yang cocok dengan titik data yang diketahui", "ad": "adalah seperangkat nilai-y yang telah Anda ketahui dalam hubungan y = b*m^x, array atau rentang dari angka positif!adalah seperangkat nilai-x opsional yang mungkin telah Anda ketahui dalam hubungan y = b*m^x, array atau rentang berukuran sama seperti Known_y's!adalah nilai-x baru yang GROWTH-nya Anda inginkan untuk mengembalikan nilai-y yang terkait!adalah nilai logika: konstanta b dihitung secara normal jika Const = TRUE; b diatur sama dengan 1 jika Const = FALSE atau dihilangkan"}, "HARMEAN": {"a": "(number1; [number2]; ...)", "d": "Menampilkan nilai rata-rata harmonik perangkat data dari angka positif: resiprokal dari nilai rata-rata aritmatika resiprokal", "ad": "adalah angka atau nama, array, atau referensi 1 sampai 255 yang mengandung angka di mana Anda ingin nilai rata-rata harmoniknya"}, "HYPGEOM.DIST": {"a": "(sample_s; number_sample; population_s; number_pop; cumulative)", "d": "<PERSON><PERSON><PERSON><PERSON>an distribusi hipergeometrik", "ad": "adalah jumlah keberhasilan dalam sampel!adalah ukuran sampel!adalah jumlah keberhasilan dalam populasi!adalah ukuran populasi!adalah nilai logis: untuk fungsi distribusi kumulatif, gunakan TRUE; untuk fungsi kerapatan probabilitas, gunakan FALSE"}, "HYPGEOMDIST": {"a": "(sample_s; number_sample; population_s; number_pop)", "d": "Mengembalikan distribusi hipergeometrik", "ad": "adalah jumlah keberhasilan dalam contoh!adalah ukuran contoh!adalah jumlah keberhasilan dalam populasi!adalah ukuran populasi"}, "INTERCEPT": {"a": "(known_y's; known_x's)", "d": "Menghitung titik tempat garis akan memotong sumbu-y menggunakan garis regresi yang paling cocok yang diplot melalui nilai-x dan nilai-y yang diketahui", "ad": "adalah seperangkat observasi atau data dependen dan dapat berupa angka atau nama, array, atau referensi yang berisi angka!adalah seperangkat observasi atau data independen dan dapat berupa angka atau nama, array, atau referensi yang berisi angka"}, "KURT": {"a": "(number1; [number2]; ...)", "d": "Menampilkan kurtosis dari perangkat data", "ad": "adalah angka atau nama, array, atau referensi  1 sampai 255 yang mengandung angka yang kurtosisnya <PERSON>a inginkan"}, "LARGE": {"a": "(array; k)", "d": "Menampilkan nilai k-th terbesar dalam perangkat data. <PERSON><PERSON><PERSON>, angka terbesar kelima", "ad": "adalah array atau rentang data di mana Anda ingin menentukan nilai k-th terbesar tersebut!adalah posisi (dari yang terbesar) dalam array atau rentang sel dari nilai untuk dikembalikan"}, "LINEST": {"a": "(known_y's; [known_x's]; [const]; [stats])", "d": "Mengembalikan statistik yang menggambarkan tren linear yang cocok dengan titik data yang diketahui, dengan mencocokkan garis lurus menggunakan metode kuadrat terkecil", "ad": "adalah seperangkat nilai-y yang telah Anda ketahui dalam hubungan y = mx + b!adalah seperangkat opsional nilai-x yang mungkin telah Anda ketahui dalam hubungan y = mx + b!adalah nilai logis: konstanta b dihitung secara normal jika Const = TRUE atau dihilangkan; b diatur sama dengan 0 jika Const = FALSE!adalah nilai logis: mengembalikan statistik regresi tambahan = TRUE; mengembalikan koefisien-m dan konstanta b = FALSE atau dihilangkan"}, "LOGEST": {"a": "(known_y's; [known_x's]; [const]; [stats])", "d": "Mengembalikan statistik yang menggambarkan kurva eksponensial yang cocok dengan titik data yang diketahui", "ad": "adalah seperangkat nilai-y yang telah Anda ketahui dalam hubungan y = b*m^x!adalah seperangkat nilai-x opsional yang mungkin telah Anda ketahui dalam hubungan y = b*m^x!adalah nilai logis: konstanta b dihitung secara normal jika Const = TRUE atau dihilangkan; b diatur sama dengan 1 jika Const = FALSE!adalah nilai logika: mengembalikan statistik regresi tambahan = TRUE; mengembalikan koefisien-m dan konstanta b = FALSE atau dihilangkan"}, "LOGINV": {"a": "(probability; mean; standard_dev)", "d": "Mengembalikan inversi fungsi distribusi kumulatif lognormal dari x, di mana ln(x) secara normal terdistribusi dengan parameter Mean dan Standar_dev", "ad": "adalah probabilitas yang terkait dengan distribusi lognormal, angka antara 0 dan 1 (termasuk keduanya)!adalah mean dari ln(x)!adalah simpangan baku dari ln(x), angka positif"}, "LOGNORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "<PERSON><PERSON><PERSON><PERSON>an distribusi lognormal dari x, di mana ln(x) terdistribusi secara normal dengan parameter <PERSON><PERSON> rata-rata dan Standard_dev", "ad": "adalah nilai untuk mengevaluasi fungsi, yaitu bilangan positif!adalah nilai rata-rata dari ln(x)!adalah simpangan standar dari ln(x), bilangan positif!adalah nilai logis: untuk fungsi distribusi kumulatif, gunakan TRUE; untuk fungsi kerapatan probabilitas, gunakan FALSE"}, "LOGNORM.INV": {"a": "(probability; mean; standard_dev)", "d": "Menampilkan invers fungsi distribusi kumulatif lognormal dari x, di mana ln(x) secara normal terdistribusi dengan parameter Nilai rata-rata dan <PERSON>ar_dev", "ad": "adalah probabilitas yang berhubungan dengan distribusi lognormal, angka di antara 0 sampai dengan 1!adalah nilai rata-rata dari ln(x)!adalah simpangan baku dari ln(x), angka positif"}, "LOGNORMDIST": {"a": "(x; mean; standard_dev)", "d": "Mengembalikan distribusi lognormal kumulatif dari x, di mana ln(x) terdistribusi secara normal dengan parameter Mean dan Standar_dev", "ad": "adalah nilai untuk mengevaluasi fungsi, angka positif!adalah mean dari ln(x)!adalah simpangan baku dari ln(x), angka positif"}, "MAX": {"a": "(number1; [number2]; ...)", "d": "Menampilkan nilai terbesar dalam seperangkat nilai. Abaikan nilai dan teks logis", "ad": "ad<PERSON>h angka 1 samp<PERSON> 255, se<PERSON> k<PERSON><PERSON>, nilai logis, atau nomor teks yang Anda ingin nilainya maksimal"}, "MAXA": {"a": "(value1; [value2]; ...)", "d": "Menampilkan nilai terbesar dalam serang<PERSON>an nilai. Tidak mengabaikan nilai logis dan teks", "ad": "ad<PERSON>h angka dari 1 sampai 255, sel koso<PERSON>, nilai logis, atau nomor teks yang maksimalnya <PERSON><PERSON> ing<PERSON>an"}, "MAXIFS": {"a": "(max_range; criteria_range; criteria; ...)", "d": "Mengembalikan nilai maksimum antara sel-sel yang ditentukan oleh seperangkat kondisi atau kriteria tertentu", "ad": "sel untuk menentukan nilai maksimal!adalah rentang sel yang ingin Anda evaluasi untuk kondisi tertentu!adalah kondisi atau kriteria dalam bentuk angka, ekspresi, atau teks yang menjelaskan sel mana yang akan disertakan ketika menentukan nilai maksimal"}, "MEDIAN": {"a": "(number1; [number2]; ...)", "d": "Menampilkan nilai tengah, atau angka di tengah-tengah perangkat angka yang ditentukan", "ad": "adalah angka atau nama, array, atau referensi 1 sampai 255 yang mengandung angka dimana Anda inginkan nilai tengahnya"}, "MIN": {"a": "(number1; [number2]; ...)", "d": "Menampilkan nilai terkecil dalam seperangkat nilai. Abaikan nilai dan teks logis", "ad": "ad<PERSON>h angka 1 samp<PERSON> 255, se<PERSON> k<PERSON><PERSON>, nilai logis, atau angka teks yang Anda ingin nilainya minimal"}, "MINA": {"a": "(value1; [value2]; ...)", "d": "Menampilkan nilai terkecil dalam perangkat nilai. Tidak mengabaikan nilai logika dan teks", "ad": "<PERSON><PERSON><PERSON> an<PERSON>, se<PERSON> k<PERSON><PERSON>, nilai logika atau nomor teks 1 sampai 255 yang <PERSON><PERSON> <PERSON>a ing<PERSON>an"}, "MINIFS": {"a": "(min_range; criteria_range; criteria; ...)", "d": "Mengembalikan nilai minimum antara sel-sel yang ditentukan oleh seperangkat kondisi atau kriteria tertentu", "ad": "sel untuk menentukan nilai minimal!adalah rentang sel yang ingin Anda evaluasi untuk kondisi tertentu!adalah kondisi atau kriteria dalam bentuk angka, ekspresi, atau teks yang menjelaskan sel mana yang akan disertakan ketika menentukan nilai minimal"}, "MODE": {"a": "(number1; [number2]; ...)", "d": "Mengembalikan nilai yang paling sering muncul, atau berulang, dalam arry atau rentang data", "ad": "adalah angka, atau nama, array, atau referensi 1 sampai 255 yang memuat angka yang modusnya <PERSON>a inginkan"}, "MODE.MULT": {"a": "(number1; [number2]; ...)", "d": "Mengh<PERSON>lkan array vertikal dari nilai yang paling sering muncul, atau berulang dalam array atau rentang data.  Untuk array horizontal, gunakan =TRANSPOSE(MODE.MULT(angka1,angka2,...))", "ad": "adalah angka 1 sampai 255, atau nama, array, atau referensi yang berisi angka yang Anda inginkan modenya"}, "MODE.SNGL": {"a": "(number1; [number2]; ...)", "d": "Menampilkan yang paling sering terjadi, atau berulang, nilai dalam array atau rentang data", "ad": "adalah angka, atau nama, array, atau referensi 1 sampai 255 yang mengandung angka dengan mode yang <PERSON>a inginkan"}, "NEGBINOM.DIST": {"a": "(number_f; number_s; probability_s; cumulative)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> distribusi negatif binomial, probabilitas bahwa mungkin ada kegagalan Number_f agar Number_ke-s berhasil, dengan probabilitas keberhasilan Probability_s", "ad": "adalah jumlah kegagalan!adalah jumlah ambang batas dari keberhasilan!adalah probabilitas keberhasilan; angka di antara 0 dan 1!adalah nilai logis: untuk fungsi distribusi kumulatif, gunakan TRUE; untuk fungsi massa probabilitas, gunakan FALSE"}, "NEGBINOMDIST": {"a": "(number_f; number_s; probability_s)", "d": "Menampilkan distribusi negatif binomial, probabilitas yang mungkin terdapat Number_f kegagalan sebelum Number_s-th berhasil, dengan Probability_s probabilitas dari keb<PERSON>n", "ad": "adalah jumlah kegagalan!adalah jumlah ambang batas dari keberhasilan!adalah probabilitas keberhasilan; angka di antara 0 dan 1"}, "NORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "<PERSON><PERSON><PERSON><PERSON>an distribusi normal untuk nilai rata-rata dan simpangan standar tertentu", "ad": "adalah nilai yang Anda inginkan distribusinya!adalah nilai rata-rata aritmatika distribusi!adalah simpangan standar distribusi, bilangan positif!adalah nilai logis: untuk fungsi distribusi kumulatif, gunakan TRUE; untuk fungsi kerapatan probabilitas, gunakan FALSE"}, "NORMDIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Mengembalikan distribusi kumulatif normal untuk mean dan simpangan baku yang ditentukan", "ad": "adalah nilai yang Anda inginkan untuk distribusi!adalah mean aritmatika distribusi!adalah simpangan baku dari distribusi, angka positif!adalah nilai logis: untuk fungsi distribusi kumulatif, gunakan TRUE; untuk fungsi kepadatan probabilitas, gunakan FALSE"}, "NORM.INV": {"a": "(probability; mean; standard_dev)", "d": "Menampilkan invers distribusi kumulatif normal untuk nilai rata-rata dan simpangan baku tertentu", "ad": "adalah probabilitas yang berhubungan dengan distribusi normal, angka di antara 0 sampai dengan 1!adalah nilai rata-rata aritmatika distribusi!adalah simpangan baku distribusi, angka positif"}, "NORMINV": {"a": "(probability; mean; standard_dev)", "d": "Mengembalikan inversi distribusi kumulatif normal untuk mean dan simpangan baku yang ditentukan", "ad": "adalah probabilitas yang terkait dengan distribusi normal, angka antara 0 dan 1 (termasuk keduanya)!adalah mean aritmatika distribusi!adalah simpangan baku distribusi, angka positif"}, "NORM.S.DIST": {"a": "(z; cumulative)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> distribusi kumulatif standar normal (memiliki nilai rata-rata nol dan simpangan standar satu)", "ad": "adalah nilai yang ingin Anda distribusikan!adalah nilai logis bagi fungsi untuk dikembalikan: fungsi distribusi kumulatif = TRUE; fungsi kerapatan probabilitas = FALSE"}, "NORMSDIST": {"a": "(z)", "d": "Mengembalikan distribusi kumulatif normal standar (memiliki mean nol dan simpangan baku satu)", "ad": "adalah nilai yang Anda inginkan untuk distribusi"}, "NORM.S.INV": {"a": "(probability)", "d": "Menampilkan invers distribusi kumulatif standar normal (memiliki nilai rata-rata nol dan simpangan baku satu)", "ad": "adalah probabilitas yang berhubungan dengan distribusi normal, angka di antara 0 sampai dengan 1"}, "NORMSINV": {"a": "(probability)", "d": "Mengembalikan inversi distribusi kumulatif normal standar (memiliki mean nol dan simpangan baku satu)", "ad": "adalah probabilitas yang terkait dengan distribusi normal, angka antara 0 dan 1 (term<PERSON><PERSON>uan<PERSON>)"}, "PEARSON": {"a": "(array1; array2)", "d": "Menampilkan koefisien korelasi momen produk <PERSON>, r", "ad": "adalah seperangkat nilai independen!adalah seperangkat nilai dependen"}, "PERCENTILE": {"a": "(array; k)", "d": "Mengembalikan persentil k-th dari nilai dalam rentang", "ad": "adalah array atau rentang data yang menetapkan kedudukan relatif!adalah nilai persentil antara 0 sampai dengan 1, termasuk keduanya"}, "PERCENTILE.EXC": {"a": "(array; k)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> persentil ke-k dari nilai di suatu rentang, di mana k dalam rentang 0..1, tidak termasuk", "ad": "adalah array atau rentang data yang menentukan posisi relatif!adalah nilai persentil antara 0 hingga 1, term<PERSON>uk"}, "PERCENTILE.INC": {"a": "(array; k)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> persentil ke-k dari nilai di suatu rentang, di mana k dalam rentang 0..1, term<PERSON><PERSON>", "ad": "adalah array atau rentang data yang menentukan kedudukan relatif!adalah nilai persentil antara 0 hingga 1, termasuk"}, "PERCENTRANK": {"a": "(array; x; [significance])", "d": "Mengembalikan peringkat nilai dalam perangkat data sebagai persentase dari perangkat data", "ad": "adalah array atau rentang data dengan nilai numerik yang menetapkan kedudukan relatif!adalah nilai yang ingin Anda ketahui peringkatnya!adalah nilai opsional yang mengidentifikasi angka digit signifikan untuk persentase yang dike<PERSON>, tiga digit jika dihilangkan (0.xxx%)"}, "PERCENTRANK.EXC": {"a": "(array; x; [significance])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> peringkat nilai dalam set data sebagai persentase dari set data sebagai persentase (0..1, tidak termasuk) dari set data", "ad": "adalah array atau rentang data dengan nilai numerik yang menentukan posisi relatif!adalah nilai yang ingin Anda ketahui peringkatnya!adalah nilai opsional yang mengidentifikasi jumlah digit signifikan untuk persentase yang dihasilkan, tiga digit jika dihilangkan (0.xxx%)"}, "PERCENTRANK.INC": {"a": "(array; x; [significance])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> peringkat nilai dalam set data sebagai persentase dari set data sebagai persentase (0..1, termasuk) dari set data", "ad": "adalah array atau rentang data dengan nilai numerik yang menentukan kedudukan relatif!adalah nilai yang ingin Anda ketahui peringkatnya!adalah nilai opsional yang mengidentifikasi jumlah digit signifikan untuk persentase yang dihasilkan, tiga digit jika dihilangkan (0.xxx%)"}, "PERMUT": {"a": "(number; number_chosen)", "d": "Menampilkan jumlah permutasi untuk jumlah objek yang ditentukan yang dapat dipilih dari objek total", "ad": "adalah jumlah total dari objek!adalah jumlah objek dalam tiap permutasi"}, "PERMUTATIONA": {"a": "(number; number_chosen)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> jumlah permutasi untuk sejumlah objek te<PERSON> (dengan pengulangan) yang dapat dipilih dari objek total", "ad": "adalah jumlah objek total!adalah jumlah objek di setiap permutasi"}, "PHI": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> nilai fungsi kerapatan untuk distribusi normal standar", "ad": "adalah angka yang Anda inginkan kerapatan distribusi normal standarnya"}, "POISSON": {"a": "(x; mean; cumulative)", "d": "Mengembalikan distribusi Poisson", "ad": "adalah jumlah kejadian!adalah nilai numerik yang di<PERSON>, angka positif!adalah nilai logis: untuk probabilitas Poisson kumulatif, gunakan TRUE; untuk fungsi massa probabilitas Poisson, gunakan FALSE"}, "POISSON.DIST": {"a": "(x; mean; cumulative)", "d": "Menampilkan distribusi Poisson", "ad": "adalah jumlah kejadian!adalah nilai numerik yang di<PERSON>, angka positif!adalah nilai logis: untuk probabilitas Poisson kumulatif, gunakan BENAR; untuk fungsi massa probabilitas Poisson, gunakan SALAH"}, "PROB": {"a": "(x_range; prob_range; lower_limit; [upper_limit])", "d": "Menampilkan probabilitas bahwa nilai dalam rentang adalah di antara dua limit atau sama dengan limit terendah", "ad": "adalah rentang nilai numerik x di mana terdapat probabilitas terkait!adalah perangkat probabilitas yang berhubungan dengan nilai dalam rentang_ X, nilai di antara 0 dan 1 dan mengeluarkan 0!adalah batas terendah pada nilai yang probabilitasnya Anda inginkan!adalah batas atas opsional nilai. <PERSON><PERSON>, PROB mengembalikan probabilitas bahwa nilai rentang_X adalah sama dengan limit_lebih Rendah"}, "QUARTILE": {"a": "(array; quart)", "d": "Mengembalikan kuartil perangkat data", "ad": "adalah array atau rentang sel dari nilai numerik yang Anda inginkan nilai kuartilnya!adalah angka: nilai minimum = 0; kuartil ke-1 = 1; nilai median = 2; kuartil ke-3 = 3; nilai maksimum = 4"}, "QUARTILE.INC": {"a": "(array; quart)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kuartil set data, berdasarkan nilai persentil dari 0..1, term<PERSON><PERSON>", "ad": "adalah array atau rentang sel dari nilai numerik yang ingin Anda dapatkan nilai kuartilnya!adalah angka: nilai minimal = 0; kuartil ke-1 = 1; nilai median = 2; kuartil ke-3 = 3; nilai maksimal = 4"}, "QUARTILE.EXC": {"a": "(array; quart)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kuartil set data, berdasarkan nilai persentil dari 0..1, tidak termasuk", "ad": "adalah array atau rentang sel dari nilai numerik yang ingin Anda dapatkan nilai kuartilnya!adalah angka: nilai minimal = 0; kuartil ke-1 = 1; nilai median = 2; kuartil ke-3 = 3; nilai maksimal = 4"}, "RANK": {"a": "(number; ref; [order])", "d": "Mengembalikan peringkat angka dalam daftar angka: ukurannya relatif terhadap nilai lain dalam daftar", "ad": "adalah angka yang ingin Anda temukan peringkatnya!adalah array dari, atau referensi ke, daftar angka. Nilai bukan angka diabaikan!adalah angka: peringkat dalam daftar yang diurutkan menurun = 0 atau dihilangkan; peringkat dalam daftar yang diurutkan naik = nilai bukan nol"}, "RANK.AVG": {"a": "(number; ref; [order])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> peringkat angka dalam daftar angka: ukurannya relatif dibandingkan nilai lain dalam daftar; jika lebih dari satu nilai memiliki peringkat yang sama, yang dihas<PERSON>kan adalah peringkat rata-ratanya", "ad": "adalah angka yang ingin Anda ketahui peringkatnya!adalah array, atau acuan ke, daftar angka. Nilai non-numerik diabaikan!adalah angka: peringkat dalam daftar disortir secara menurun = 0 atau dihilangkan; peringkat dalam daftar disortir secara naik = sembarang nilai asal bukan nol"}, "RANK.EQ": {"a": "(number; ref; [order])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> peringkat angka dalam daftar angka: ukurannya relatif dibandingkan nilai lain dalam daftar; jika lebih dari satu nilai memiliki peringkat yang sama, yang dihasilkan adalah peringkat atas dari set nilai tersebut", "ad": "adalah angka yang ingin Anda ketahui peringkatnya!adalah array, atau acuan ke, daftar angka. Nilai non-numerik diabaikan!adalah angka: peringkat dalam daftar disortir secara menurun = 0 atau dihilangkan; peringkat dalam daftar disortir secara naik = sembarang nilai asal bukan nol"}, "RSQ": {"a": "(known_y's; known_x's)", "d": "Mengembalikan kuadrat koefisien korelasi momen produk Pearson melalui titik data yang ditentukan", "ad": "adalah array atau rentang titik data dan dapat berupa angka atau nama, array, atau referensi yang berisi angka!adalah array atau rentang titik data dan dapat berupa angka atau nama, array, atau referensi yang berisi angka"}, "SKEW": {"a": "(number1; [number2]; ...)", "d": "Menampilkan kecondongan distribusi: ka<PERSON><PERSON><PERSON><PERSON> derajat asimetri dari distribusi disekitar nilai rata-ratanya", "ad": "adalah angka atau nama, array, atau referensi 1 sampai 255 yang mengandung angka yang kecondongannya <PERSON>a ing<PERSON>an"}, "SKEW.P": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kecondongan distribusi berdasarkan populasi: karak<PERSON><PERSON><PERSON> derajat asimetri dari distribusi di sekitar nilai rata-ratanya", "ad": "adalah angka 1 sampai 254 atau nama, array, atau referensi yang berisi angka yang kecondongan populasinya <PERSON>a ing<PERSON>an"}, "SLOPE": {"a": "(known_y's; known_x's)", "d": "Mengembalikan kemiringan garis regresi linear melalui titik data yang ditentukan", "ad": "adalah array atau rentang sel titik data dependen numerik dan dapat berupa angka atau nama, array, atau referensi yang berisi angka!adalah perangkat poin data independen dan dapat berupa angka atau nama, array, atau referensi yang berisi angka"}, "SMALL": {"a": "(array; k)", "d": "Menampilkan nilai k-th terkecil dalam perangkat data. <PERSON><PERSON><PERSON>, angka terkecil kelima", "ad": "adalah array atau rentang data numerik di mana Anda ingin menentukan nilai k-th terkecil tersebut!adalah posisi (dari yang terkecil) dalam array atau rentang dari nilai untuk dikembalikan"}, "STANDARDIZE": {"a": "(x; mean; standard_dev)", "d": "Menampilkan nilai yang dinormalisasi dari distribusi terkarakterisasi oleh nilai rata-rata dan simpangan baku", "ad": "adalah nilai yang ingin Anda normalisasi!adalah nilai rata-rata aritmatika distribusi!adalah simpangan baku dari distribusi, angka positif"}, "STDEV": {"a": "(number1; [number2]; ...)", "d": "Memperkirakan simpangan baku berdasarkan contoh (abaikan nilai dan teks logis dalam contoh)", "ad": "adalah angka 1 sampai 255 yang berkaitan dengan contoh dari populasi dan dapat berupa angka atau referensi yang memuat angka"}, "STDEV.P": {"a": "(number1; [number2]; ...)", "d": "Menghitung simpangan baku berdasarkan pada seluruh populasi yang ditentukan sebagai argumen (abaikan nilai dan teks logis)", "ad": "adalah angka 1 sampai 255 yang berhubungan ke populasi dan dapat berupa angka atau referensi yang mengandung angka"}, "STDEV.S": {"a": "(number1; [number2]; ...)", "d": "Memperkirakan simpangan baku berdasarkan pada contoh (mengabaikan nilai dan teks logis dalam contoh)", "ad": "adalah angka 1 sampai 255 yang berhubungan dengan contoh dari populasi dan dapat berupa angka atau referensi yang mengandung angka"}, "STDEVA": {"a": "(value1; [value2]; ...)", "d": "Memperkirakan simpangan baku be<PERSON><PERSON><PERSON> contoh, term<PERSON><PERSON> nilai dan teks logis. Teks dan nilai logis SALAH memiliki nilai 0; nilai logis BENAR memiliki nilai 1", "ad": "adalah nilai 1 sampai 255 yang berhubungan dengan contoh dari populasi dan dapat berupa nilai atau nama atau referensi ke nilai"}, "STDEVP": {"a": "(number1; [number2]; ...)", "d": "Menghitung simpangan baku berdasarkan seluruh populasi yang ditentukan sebagai argumen (abaikan nilai dan teks logis)", "ad": "adalah angka 1 sampai 255 yang berkaitan dengan populasi dan dapat berupa angka atau referensi yang memuat angka"}, "STDEVPA": {"a": "(value1; [value2]; ...)", "d": "Menghitung simpangan baku berdasar<PERSON> selu<PERSON>h populasi, termasuk nilai dan teks logis. Teks dan nilai logis SALAH bernilai 0; nilai logis BENAR bernilai 1", "ad": "adalah nilai 1 sampai 255 yang berhubungan ke populasi dan dapat berupa nilai, nama array atau referensi yang mengandung nilai"}, "STEYX": {"a": "(known_y's; known_x's)", "d": "Mengembalikan kesalahan standar dari nilai-y yang diperkirakan untuk tiap x dalam regresi", "ad": "adalah array atau rentang titik data dependen dan dapat berupa angka atau nama, array, atau referensi yang berisi angka!adalah array atau rentang titik data independen dan dapat berupa angka atau nama, array, atau referensi yang berisi angka"}, "TDIST": {"a": "(x; deg_freedom; tails)", "d": "Mengembalikan distribusi-t Pelajar", "ad": "adalah nilai numerik untuk mengevaluasi distribusi!adalah bilangan bulat yang mengindikasikan jumlah derajat kebebasan yang mengkarakterisasi distribusi!menentukan jumlah sisi distribusi untuk dikembalikan: distribusi satu sisi = 1; distribusi dua sisi = 2"}, "TINV": {"a": "(probability; deg_freedom)", "d": "Mengembalikan inversi dua-sisi distribusi-t <PERSON>elajar", "ad": "adalah probabilitas yang terkait dengan distribusi-t <PERSON><PERSON><PERSON> dua-sisi, angka antara 0 dan 1, termasuk keduanya!adalah bilangan bulat positif yang mengindikasikan jumlah derajat kebebasan untuk mengkarakterisasi distribusi"}, "T.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> distribus<PERSON>-t <PERSON><PERSON>jar le<PERSON>-kiri", "ad": "adalah nilai numerik untuk mengevaluasi distribusi!adalah bilangan bulat yang mengindikasikan jumlah pangkat yang mengarakterisasi distribusi!adalah nilai logis: untuk fungsi distribusi kumulatif, gunakan TRUE; untuk fungsi kerapatan probabilitas, gunakan FALSE"}, "T.DIST.2T": {"a": "(x; deg_freedom)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> distribus<PERSON>-t <PERSON><PERSON><PERSON> dua-le<PERSON><PERSON>", "ad": "adalah nilai numerik untuk mengevaluasi distribusi!adalah bilangan bulat yang mengindikasikan jumlah pangkat yang mengarakterisasi distribusi"}, "T.DIST.RT": {"a": "(x; deg_freedom)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> distribus<PERSON>-t <PERSON><PERSON><PERSON>-kanan", "ad": "adalah nilai numerik untuk mengevaluasi distribusi!adalah bilangan bulat yang mengindikasikan jumlah pangkat yang mengarakterisasi distribusi"}, "T.INV": {"a": "(probability; deg_freedom)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> balikan lemparan-kiri dari distribusi-t <PERSON><PERSON>jar", "ad": "adalah probabilitas yang terkait dengan distribusi-t <PERSON><PERSON>jar dua-lemparan, angka di antara 0 sampai dengan 1!adalah bilangan bulat positif yang mengindikasikan jumlah pangkat untuk mengarakterisasi distribusi"}, "T.INV.2T": {"a": "(probability; deg_freedom)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> balikan dua-lemparan dari distribusi-t <PERSON><PERSON><PERSON>", "ad": "adalah probabilitas yang terkait dengan distribusi-t <PERSON><PERSON>jar dua-lemparan, angka di antara 0 sampai dengan 1!adalah bilangan bulat positif yang mengindikasikan jumlah pangkat untuk mengarakterisasi distribusi"}, "T.TEST": {"a": "(array1; array2; tails; type)", "d": "Menampilkan probabilitas yang berhubungan dengan <PERSON>-t <PERSON>", "ad": "adalah perangkat data pertama!adalah perangkat data kedua!menentukan jumlah arah distribusi untuk dikembalikan: distribusi satu lemparan = 1; distribusi dua lemparan = 2!adalah jenis uji-t: berp<PERSON>ngan = 1, variansi dua-contoh yang sama (homoscedastic) = 2, variansi dua-contoh yang tidak sama = 3"}, "TREND": {"a": "(known_y's; [known_x's]; [new_x's]; [const])", "d": "Mengembalikan angka dalam tren linear yang cocok dengan titik data yang diketahui, menggunakan metode kuadrat terkecil", "ad": "adalah rentang atau array nilai-y yang telah Anda ketahui dalam hubungan y = mx + b!adalah rentang atau array opsional nilai-x yang Anda ketahui dalam hubungan y = mx + b, array berukuran sama seperti Known_y's!adalah rentang atau array nilai-x yang baru yang Anda ingin TREND-nya mengembalikan nilai-y yang terkait!adalah nilai logika: konstanta b dihitung secara normal jika Const = TRUE atau dihilangkan; b diatur sama dengan 0 jika Const = FALSE"}, "TRIMMEAN": {"a": "(array; percent)", "d": "Menampilkan nilai rata-rata bagian interior dari perangkat nilai data", "ad": "adalah rentang atau array nilai untuk dipotong dan dirata-rata!adalah sejumlah kecil poin data untuk dikeluarkan dari bagian atas dan bawah perangkat data"}, "TTEST": {"a": "(array1; array2; tails; type)", "d": "Mengembalikan probabilitas yang terkait dengan U<PERSON>-t <PERSON>jar", "ad": "adalah perangkat data pertama!adalah perangkat data kedua!menentukan jumlah sisi distribusi untuk dikembalikan: distribusi satu sisi = 1; distribusi dua sisi = 2!adalah jenis uji-t: be<PERSON><PERSON>ngan = 1, variansi dua-contoh yang sama (homoscedastic) = 2, variansi dua-contoh yang tidak sama = 3"}, "VAR": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON>rak<PERSON> varian berdasarkan contoh (abaikan nilai dan teks logis dalam contoh)", "ad": "adalah argumen numerik 1 sampai 255 yang berkaitan dengan contoh dari populasi"}, "VAR.P": {"a": "(number1; [number2]; ...)", "d": "Menghitung varians berdasarkan seluruh populasi (mengabaikan nilai dan teks logis dalam populasi)", "ad": "adalah argumen numerik 1 sampai 255 yang berhubungan dengan populasi"}, "VAR.S": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON>rakan variansi berdasarkan contoh (mengabaikan nilai dan teks logis dalam contoh)", "ad": "adalah argumen numerik dari 1 sampai 255 yang berhubungan dengan contoh dari populasi"}, "VARA": {"a": "(value1; [value2]; ...)", "d": "Memperkirakan varians be<PERSON><PERSON><PERSON> con<PERSON>, term<PERSON><PERSON> nilai dan teks logis. Teks dan nilai logis SALAH memiliki nilai 0; nilai logis BENAR memiliki nilai 1", "ad": "adalah argumen nilai 1 sampai 255 yang berhubungan dengan contoh dari populasi"}, "VARP": {"a": "(number1; [number2]; ...)", "d": "Menghitung variansi berdasarkan seluruh populasi (abaikan nilai dan teks logis dalam populasi)", "ad": "adalah argumen angka 1 sampai 255 yang berkaitan dengan populasi"}, "VARPA": {"a": "(value1; [value2]; ...)", "d": "Menghitung varians berdasarkan pada selu<PERSON>h populasi, term<PERSON><PERSON> nilai dan teks logis. Teks dan nilai logis SALAH bernilai 0; nilai logis BENAR bernilai 1", "ad": "adalah argumen nilai 1 sampai 255 yang berhubungan dengan populasi"}, "WEIBULL": {"a": "(x; alpha; beta; cumulative)", "d": "Mengembalikan distribus<PERSON>", "ad": "adalah nilai yang ingin Anda gunakan untuk mengevaluasi fungsi, angka bukan negatif!adalah parameter untuk distribusi, angka positif!adalah parameter untuk distribusi, angka positif!adalah nilai logis: untuk fungsi distribusi kumulatif, gunakan TRUE; untuk fungsi massa probabilitas, gunakan FALSE"}, "WEIBULL.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "Menampilkan distribus<PERSON>", "ad": "adalah nilai yang ingin Anda gunakan untuk mengevaluasi fungsi, angka bukan negatif!adalah parameter untuk distribusi, yaitu angka positif!adalah parameter untuk distribusi, yaitu angka positif!adalah nilai logis: untuk fungsi distribusi kumulatif, gunakan BENAR; untuk fungsi massa probabilitas, gunakan SALAH"}, "Z.TEST": {"a": "(array; x; [sigma])", "d": "Menampilkan nilai-<PERSON> satu-lemparan dari uji-z", "ad": "adalah array atau rentang data yang berlawanan untuk menguji X!adalah nilai untuk diuji!adalah populasi simpangan baku (yang dikenal). <PERSON><PERSON>, contoh simpangan baku digunakan"}, "ZTEST": {"a": "(array; x; [sigma])", "d": "Mengembalikan nilai-<PERSON> satu-sisi dari uji-z", "ad": "adalah array atau rentang data yang digunakan untuk menguji X!adalah nilai untuk diuji!adalah simpangan baku populasi (yang diketahui). <PERSON><PERSON>, contoh simpangan baku digunakan"}, "ACCRINT": {"a": "(issue; first_interest; settlement; rate; par; frequency; [basis]; [calc_method])", "d": "Menampilkan bunga akrual untuk surat berharga yang membayar suku bunga periodik.", "ad": "adalah tanggal penerbitan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal suku bunga surat berharga pertama, yang dinyatakan sebagai angka tanggal seri!adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tingkat kupon tahunan sekuritas!adalah nilai surat berharga!adalah jumlah pembayaran kupon per tahun!adalah jenis basis penghitungan hari untuk digunakan!adalah nilai logis: untuk bunga akrual dari tanggal penerbitan = TRUE atau diabaikan; untuk menghitung dari tanggal pembayaran kupon terakhir = FALSE"}, "ACCRINTM": {"a": "(issue; settlement; rate; par; [basis])", "d": "Menampilkan bunga akrual untuk surat berharga yang membayar bunga pada jatuh tempo", "ad": "adalah tanggal penerbitan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tingkat kupon tahunan surat berharga!adalah nilai par surat berharga!adalah jenis basis penghitungan hari untuk digunakan"}, "AMORDEGRC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Menampilkan depresiasi linear prorata dari aset untuk tiap periode akuntansi.", "ad": "adalah biaya aset!adalah tanggal pembelian aset!adalah tanggal dari akhir periode!adalah nilai sisa pada akhir periode aset.!adalah periode!adalah tingkat depresiasi!basis_tahun : 0 untuk tahun dengan 360 hari, 1 untuk sesungguhnya, 3 untuk tahun dengan 365 hari."}, "AMORLINC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Menampilkan depresiasi linear prorata dari aset untuk tiap periode akuntansi.", "ad": "adalah biaya aset!adalah tanggal pembelian aset!adalah tanggal dari akhir periode!adalah nilai sisa pada akhir periode aset.!adalah periode!adalah tingkat depresiasi!basis_tahun : 0 untuk tahun dengan 360 hari, 1 untuk sesungguhnya, 3 untuk tahun dengan 365 hari."}, "COUPDAYBS": {"a": "(settlement; maturity; frequency; [basis])", "d": "Menampilkan jumlah hari dari awal periode kupon sampai tanggal pelunasan", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah jumlah pembayaran kupon per tahun!adalah tipe basis hitungan hari yang digunakan"}, "COUPDAYS": {"a": "(settlement; maturity; frequency; [basis])", "d": "Menampilkan jumlah angka dalam periode kupon yang berisi tanggal pelunasan", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah jumlah pembayaran kupon per tahun!adalah tipe basis hitungan hari yang digunakan"}, "COUPDAYSNC": {"a": "(settlement; maturity; frequency; [basis])", "d": "Menampilkan jumlah hari dari tanggal pelunasan sampai tanggal kupon selanjutnya", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah angka pembayaran kupon per tahun!adalah tipe basis hitung hari yang digunakan"}, "COUPNCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "Menampilkan tanggal kupon selanjutnya setelah tanggal pelunasan", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah jumlah kupon pembayaran per tahun!adalah tipe basis hitungan hari yang digunakan"}, "COUPNUM": {"a": "(settlement; maturity; frequency; [basis])", "d": "Menampilkan jumlah kupon yang dapat dibayarkan antara tanggal pelunasan dan tanggal jatuh tempo", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah nomor pembayaran kupon per tahun!adalah tipe basis hari yang digunakan"}, "COUPPCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "Menampilkan tanggal kupon sebelumnya sebelum tanggal pelunasan", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah jumlah pembayaran kupon per tahun!adalah basis hitungan hari yang digunakan"}, "CUMIPMT": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Menampilkan bunga kumulatif yang dibayarkan antara dua periode", "ad": "adalah tingkat bunga!adalah jumlah total periode pembayaran!adalah nilai saat ini!adalah periode pertama dalam perhitungan!adalah periode terakhir dalam perhitungan!adalah penentuan waktu pembayaran"}, "CUMPRINC": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Menampilkan pokok kumulatif yang di<PERSON>an pada pinjaman antara dua periode", "ad": "adalah tingkat bunga!adalah jumlah total periode pembayaran!adalah nilai sekarang!adalah periode pertama dalam perhitungan!adalah periode terakhir dalam perhitungan!adalah penentuan waktu pembayaran"}, "DB": {"a": "(cost; salvage; life; period; [month])", "d": "Menampilkan depresiasi aset untuk periode tertentu menggunakan metode pengurangan-tetap saldo", "ad": "adalah nilai awal aset!adalah akumulasi penyusutan nilai pada akhir jangka waktu aset!adalah periode pendepresiasian aset (kadang-kadang disebut nilai jual aset)!adalah periode penghitungan depresiasinya. Periode harus menggunakan unit yang sama dengan Life!adalah jumlah bulan pada tahun pertama. <PERSON><PERSON> bulan dihilangkan, jumlah bulan diasumsikan 12"}, "DDB": {"a": "(cost; salvage; life; period; [factor])", "d": "Menampilkan depresiasi aset untuk periode tertentu menggunakan metode saldo pengu<PERSON>an-ganda atau beberapa metode lain yang Anda tentukan", "ad": "adalah nilai awal aset!adalah akumulasi penyusutan nilai pada akhir jangka waktu aset!adalah jumlah periode saat nilai aset didepresiasikan (kadang-kadang disebut nilai jual aset)!adalah periode yang ingin Anda hitung depresiasinya. Periode harus menggunakan unit yang sama dengan Life!adalah laju penurunan saldo. <PERSON><PERSON> diabaika<PERSON>, diasumsikan menjadi 2 (metode saldo pengurangan-ganda)"}, "DISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "Menampilkan tingkat diskon untuk surat berharga", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo, yang dinyatakan sebagai angka tanggal seri!adalah harga surat berharga per $100 nilai nominal!adalah nilai penukaran surat berharga per $100 per nilai nominal!adalah jenis basis penghitungan hari yang digunakan"}, "DOLLARDE": {"a": "(fractional_dollar; fraction)", "d": "<PERSON><PERSON><PERSON> harga dolar, yang dinyatakan sebagai pecahan, ke dalam harga dolar, yang dinyatakan sebagai angka desimal", "ad": "adalah yang dinyatakan sebagai pecahan!adalah bilangan bulat untuk digunakan dalam penyebut pecahan"}, "DOLLARFR": {"a": "(decimal_dollar; fraction)", "d": "<PERSON><PERSON><PERSON> harga dolar, yang dinyatakan sebagai angka desimal, ke dalam harga dolar, yang dinyatakan sebagai pecahan", "ad": "adalah angka desimal!adalah bilangan bulat untuk digunakan dalam penyebut pecahan"}, "DURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "Menampilkan durasi tahunan surat berharga dengan pembayaran bunga periodik", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tingkat kupon tahunan surat berharga!adalah hasil tahunan surat berharga!adalah jumlah pembayaran kupon per tahun!adalah tipe hitungan basis hari yang digunakan"}, "EFFECT": {"a": "(nominal_rate; npery)", "d": "Menampilkan suku bunga tahunan yang berlaku", "ad": "adalah suku bunga nominal!adalah jumlah periode pemajemukan per tahun"}, "FV": {"a": "(rate; nper; pmt; [pv]; [type])", "d": "Menampilkan nilai masa depan investasi berdasarkan pembayaran periodik yang konstan dan suku bunga konstan", "ad": "adalah suku bunga per periode. <PERSON><PERSON><PERSON>, gunakan 6%/4 untuk pembayaran per kuartal pada APR 6%!adalah total jumlah periode pembayaran dalam investasi!adalah pembayaran yang dilakukan tiap periode dan tidak dapat berubah selama jangka waktu investasi!adalah nilai sekarang, atau jumlah keseluruhan nilai sekarang dari serangkaian pembayaran di masa depan. Jika diabaikan, Pv = 0!adalah nilai yang menunjukkan pemilihan waktu pembayaran: pembayaran di awal periode = 1; pembayaran di akhir periode = 0 atau diabaikan"}, "FVSCHEDULE": {"a": "(principal; schedule)", "d": "Menampilkan nilai akan datang dari nilai pokok awal sesudah menerapkan seri suku bunga majemuk", "ad": "adalah nilai saat ini!adalah array dari tingkat bunga untuk diterapkan"}, "INTRATE": {"a": "(settlement; maturity; investment; redemption; [basis])", "d": "Menampilkan tingkat bunga untuk surat berharga yang diinvestasikan penuh", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah jumlah yang diinvestasikan di surat berharga!adalah jumlah yang akan diterima saat tanggal jatuh tempo!adalah jenis basis hitungan hari yang digunakan"}, "IPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Menampilkan pembayaran bunga untuk periode tertentu untuk investasi, berdasarkan pada pembayaran periodik yang konstan dan suku bunga konstan", "ad": "adalah suku bunga tiap periode. <PERSON><PERSON><PERSON>, gunakan 6%/4 untuk pembayaran tiap kuartal pada APR 6%!adalah periode yang ingin Anda hitung bunga dan harus dalam rentang 1 sampai Nper!adalah total jumlah periode pembayaran dalam investasi!adalah nilai sekarang, atau jumlah keseluruhan nilai sekarang dari serangkaian pembayaran di masa depan!adalah nilai masa depan, atau saldo tunai yang ingin Anda capai setelah pembayaran terakhir dilakukan. Jika diabaikan, Fv = 0!adalah nilai logis yang menunjukkan pemilihan waktu pembayaran: pembayaran di akhir periode = 0 atau dihilangkan; pembayaran di awal periode = 1"}, "IRR": {"a": "(values; [guess])", "d": "Menampilkan angka internal pengembalian untuk seri aliran kas", "ad": "adalah array atau referensi ke sel yang mengandung angka yang pengembalian angka internalnya ingin Anda hitung!adalah angka yang Anda tebak mendekati hasil dari IRR; 0,1 (10 persen) jika dihilangkan"}, "ISPMT": {"a": "(rate; per; nper; pv)", "d": "Menampilkan bunga yang dibayarkan selama periode investasi tertentu", "ad": "suku bunga tiap periode. <PERSON><PERSON><PERSON>, gunakan 6%/4 untuk pembayaran tiap kuartal pada APR 6%!periode yang ingin Anda cari bunganya!jumlah periode pembayaran dalam investasi!jumlah keseluruhan nilai sekarang dari serangkaian pembayarn di masa depan"}, "MDURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "Menampilkan durasi yang disesuaikan Macauley untuk surat berharga dengan nilai par asumsi $100", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tingkat kupon tahunan surat berharga!adalah hasil tahunan surat berharga!adalah jumlah kupon pembayaran per tahun!adalah tipe basis hitungan hari yang digunakan"}, "MIRR": {"a": "(values; finance_rate; reinvest_rate)", "d": "Menampilkan angka internal dari pengembalian serangkaikan aliran kas secara periodik, mempertimbangkan biaya investasi dan bunga pada investasi kembali uang", "ad": "adalah array atau referensi ke sel yang mengandung angka yang menunjukkan serangkaian pembayaran (negatif) dan pendapatan (positif) pada periode reguler!adalah suku bunga yang Anda bayarkan atas uang yang digunakan dalam aliran kas!adalah suku bunga yang Anda terima pada aliran kas saat Anda menginvestasikan kembali aliran kas"}, "NOMINAL": {"a": "(effect_rate; npery)", "d": "Menampilkan suku bunga tahunan", "ad": "adalah suku bunga yang berlaku!adalah jumlah dari periode penyusunan per tahun"}, "NPER": {"a": "(rate; pmt; pv; [fv]; [type])", "d": "Menampilkan jumlah periode untuk investasi berdasarkan pembayaran periodik yang konstan dan suku bunga konstan", "ad": "adalah suku bunga per periode. <PERSON><PERSON><PERSON>, gunakan 6%/4 untuk pembayaran per kuartal pada APR 6%!adalah pembayaran yang dilakukan setiap periode dan tidak dapat berubah selama jangka waktu investasi!adalah nilai sekarang, atau jumlah keseluruhan nilai sekarang dari serangkaikan pembayaran di masa depan!adalah nilai masa depan, atau saldo tunai yang ingin Anda capai setelah pembayaran terakhir dilakukan. Jika diabaikan, nol digunakan!adalah nilai logis: pembayaran pada awal periode = 1; pembayaran pada akhir periode = 0 atau diabaikan"}, "NPV": {"a": "(rate; value1; [value2]; ...)", "d": "Menampilkan nilai bersih sekarang dari investasi berdasar pada tingkat diskon dan serangkaikan pembayaran di masa depan (nilai negatif) dan pendapatan (nilai positif)", "ad": "adalah harga diskon sepanjang satu periode!adalah pembayaran dan pendapatan 1 sampai 254, yang ditempatkan dalam waktu teratur dan terjadi pada akhir dari tiap periode"}, "ODDFPRICE": {"a": "(settlement; maturity; issue; first_coupon; rate; yld; redemption; frequency; [basis])", "d": "Menampilkan harga per $100 nilai nominal surat berharga dengan periode ganjil pertama", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal penerbitan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal kupon pertama surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tingkat bunga surat berharga!adalah hasil tahunan surat berharga!adalah nilai penukaran surat berharga per $100 nilai nominal!adalah jumlah pembayaran kupon per tahun!adalah tipe basis hitungan hari yang digunakan"}, "ODDFYIELD": {"a": "(settlement; maturity; issue; first_coupon; rate; pr; redemption; frequency; [basis])", "d": "Menampilkan hasil surat berharga dengan periode ganjil pertama", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal tanggal penerbitan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal kupon pertama surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tingkat bunga surat berharga!adalah harga surat berharga!adalah nilai penukaran surat berharga per $100 nilai nominal!adalah jumlah pembayaran kupon per tahun!adalah jenis basis hitungan hari yang digunakan"}, "ODDLPRICE": {"a": "(settlement; maturity; last_interest; rate; yld; redemption; frequency; [basis])", "d": "Menampilkan harga per $100 nilai nominal surat berharga dengan periode terakhir ganjil", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal kupon terakhir surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah suku bunga surat berharga!adalah hasil tahunan surat berharga!adalah nilai penukaran surat berharga per $100 nilai nominal! adalah jumlah pembayaran kupon per tahun!tipe basis hitungan hari yang digunakan"}, "ODDLYIELD": {"a": "(settlement; maturity; last_interest; rate; pr; redemption; frequency; [basis])", "d": "Menampilkan hasil surat berharga dengan periode terakhir ganjil", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal kupon surat berharga terakhir, yang dinyatakan sebagai angka tanggal seri!adalah tingkat bunga surat berharga!adalah harga surat berharga!adalah nilai penukaran surat berharga per $100 nilai nominal!adalah jumlah pembayaran kupon per tahun!adalah tipe basis hitung hari yang digunakan"}, "PDURATION": {"a": "(rate; pv; fv)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> seju<PERSON>lah periode yang diperlukan oleh investasi untuk mencapai nilai yang ditentukan", "ad": "adalah suku bunga per periode.!adalah nilai investasi saat ini!adalah nilai investasi yang diinginkan di masa mendatang"}, "PMT": {"a": "(rate; nper; pv; [fv]; [type])", "d": "Menghitung pembayaran untuk pinjaman berdasarkan pembayaran yang konstan pembayaran dan suku bunga konstan", "ad": "adalah suku bunga per periode untuk pinjaman. <PERSON><PERSON><PERSON>, gunakan 6%/4 untuk pembayaran tiap kuartal APR 6%!adalah total jumlah pembayaran untuk pinjaman tersebut!adalah nilai sekarang: jumlah total nilai sekarang dari serangkaian pembayaran di masa mendatang!adalah nilai masa depan, atau saldo tunai yang ingin Anda capai setelah pembayaran terakhir dilakukan. 0 (nol) jika diabaikan!adalah nilai logis: pembayaran pada awal periode = 1; pembayaran pada akhir periode = 0 atau diabaikan"}, "PPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Menampilkan pembayaran pada uang pokok untuk investasi tertentu berdasarkan pada pembayaran periodik yang konstan dan suku bunga konstan", "ad": "adalah suku bunga tiap periode. <PERSON><PERSON><PERSON>, gunakan 6%/4 untuk pembayaran tiap kuartal pada APR 6%!menentukan periode dan harus ada dalam rentang 1 sampai nper!adalah total jumlah periode pembayaran dalam investasi!adalah nilai sekarang: jumlah total nilai sekarang dari serangkaian pembayaran di masa depan!adalah nilai masa depan, atau saldo kas yang ingin Anda capai setelah pembayaran terakhir dilakukan!adalah nilai logis: pembayaran di awal periode = 1; pembayaran di akhir periode = 0 atau diabaikan"}, "PRICE": {"a": "(settlement; maturity; rate; yld; redemption; frequency; [basis])", "d": "Menampilkan harga per $100 nilai nominal surat berharga yang membayarkan bunga periodik", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tingkat kupon tahunan surat berharga!adalah hasil tahunan surat berharga!adalah nilai penukaran per $100 nilai nominal!adalah jumlah pembayaran kupon per tahun!adalah tipe basis hitungan hari yang digunakan"}, "PRICEDISC": {"a": "(settlement; maturity; discount; redemption; [basis])", "d": "Menampilkan harga per $100 nilai nominal dari surat berharga yang didiskon", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tingkat diskon surat berharga!adalah nilai penukaran sekuritas per $100 nilai nominal!adalah jenis basis penghitungan hari untuk digunakan"}, "PRICEMAT": {"a": "(settlement; maturity; issue; rate; yld; [basis])", "d": "Menampilkan harga per nilai nominal $100 dari surat berharga yang membayar tingkat bunga pada saat jatuh tempo", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal penerbitan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tingkat suku bunga pada tanggal penerbitan!adalah hasil tahunan surat berharga!adalah jenis basis hitungan hari yang digunakan"}, "PV": {"a": "(rate; nper; pmt; [fv]; [type])", "d": "Menampilkan nilai investasi sekarang: jumlah total nilai sekarang dari serangkaian pembayaran di masa depan", "ad": "adalah suku bunga tiap periode. <PERSON><PERSON><PERSON>, gunakan 6%/4 untuk pembayaran tiap kuartal pada APR 6%!adalah total jumlah periode pembayaran dalam investasi!adalah pembayaran yang dilakukan tiap periode dan tidak dapat berubah selama jangka waktu investasi!adalah nilai masa depan, atau saldo kas yang ingin Anda capai setelah pembayaran terakhir dilakukan!adalah nilai logis: pembayaran di awal periode = 1; pembayaran di akhir periode = 0 atau diabaikan"}, "RATE": {"a": "(nper; pmt; pv; [fv]; [type]; [guess])", "d": "Menampilkan suku bunga per periode pinjaman atau investasi. <PERSON><PERSON><PERSON>, gunakan 6%/4 untuk pembayaran tiap kuartal pada APR 6%", "ad": "adalah jumlah total periode pembayaran untuk pinjaman atau investasi!adalah pembayaran yang dilakukan tiap periode dan tidak dapat berubah selama jangka waktu pinjaman atau investasi!adalah nilai sekarang: jumlah keseluruhan nilai sekarang dari serangkaikan pembayaran di masa depan!adalah nilai masa depan, atau saldo kas yang ingin Anda capai setelah pembayaran terakhir dilakukan. Jika diabaikan, gunakan Fv = 0!adalah nilai logika: pembayaran di awal periode = 1; pembayaran di akhir periode = 0 atau dihilangkan!adalah perkiraan Anda untuk harga akan menjadi berapa; jika diabaikan, Guess = 0,1 (10 persen)"}, "RECEIVED": {"a": "(settlement; maturity; investment; discount; [basis])", "d": "Menampilkan jumlah yang diterima saat tanggal jatuh tempo untuk surat berharga yang diinvestasikan penuh", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah jumlah yang diinvestasikan dalam surat berharga!adalah tingkat diskon surat berharga!adalah jenis basis hitungan hari yang digunakan"}, "RRI": {"a": "(nper; pv; fv)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> suku bunga setara untuk pertumbuhan investasi", "ad": "adalah jumlah periode investasi!adalah nilai investasi saat ini!adalah nilai investasi di masa depan"}, "SLN": {"a": "(cost; salvage; life)", "d": "Menampilkan depresiasi garis-lurus modal untuk satu periode", "ad": "adalah nilai awal aset!adalah nilai penyusutan pada nilai akhir aset!adalah jumlah periode saat aset didepresiasikan (kadang-kadang disebut nilai jual aset)"}, "SYD": {"a": "(cost; salvage; life; per)", "d": "Menampilkan jumlah depresiasi digit tahun dari aset untuk periode tertentu", "ad": "adalah nilai awal aset!adalah nilai penyusutan pada nilai akhir aset!adalah jumlah periode saat nilai aset didepresiasikan (kadang-kadang disebut nilai jual aset)!adalah periode dan harus menggunakan unit yang sama dengan Life"}, "TBILLEQ": {"a": "(settlement; maturity; discount)", "d": "Menampilkan hasil setara obligasi untuk surat utang", "ad": " adalah tanggal pelunasan surat utang, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo Surat utang, yang dinyatakan sebagai angka tanggal seri!adalah tingkat diskon surat utang"}, "TBILLPRICE": {"a": "(settlement; maturity; discount)", "d": "Menampilkan harga per nilai awal $100 untuk surat utang", "ad": "adalah tanggal pelunasan Surat utang, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo Surat Utang, yang dinyatakan sebagai angka tanggal seri!adalah tingkat diskon Surat utang"}, "TBILLYIELD": {"a": "(settlement; maturity; pr)", "d": "Menampilkan hasil untuk Surat utang", "ad": "adalah tanggal pelunasan surat utang, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo Surat Utang, yang dinyatakan sebagai angka tanggal seri!adalah harga Surat Utang per nilai awal $100"}, "VDB": {"a": "(cost; salvage; life; start_period; end_period; [factor]; [no_switch])", "d": "Menampilkan depresiasi aset untuk periode yang Anda tentukan, termasuk periode parsial, menggunakan metode saldo pengu<PERSON>an-ganda atau beberapa metode lain yang Anda tentukan", "ad": "adalah nilai awal aset!adalah akumulasi penyusutan nilai pada akhir jangka waktu aset!adalah jumlah periode pendepresiasian aset (kadang-kadang disebut nilai jual aset)!adalah periode awal penghitungan depresiasi, dalam unit yang sama dengan Life!adalah periode akhir penghitungan depresiasi, dalam unit yang sama dengan Life!adalah laju pengurangan saldo, 2 (saldo pengurangan-ganda) jika dihilangkan!ganti ke depresiasi garis-lurus ketika depresiasi lebih besar dari saldo pengurangan = SALAH atau dihilangkan; jangan ganti  = BENAR"}, "XIRR": {"a": "(values; dates; [guess])", "d": "Menampilkan tingkat internal pengembalian untuk jadwal arus kas", "ad": "adalah rangkaian arus kas yang terkait dengan jadwal pembayaran dalam tanggal!adalah jadwal tanggal pembayaran yang berhubungan dengan arus kas pembayaran!adalah bilangan yang Anda perkirakan mendekati hasil XIRR"}, "XNPV": {"a": "(rate; values; dates)", "d": "Menampilkan nilai bersih saat ini untuk jadwal arus kas", "ad": "adalah tingkat diskon untuk diterapkan pada arus kas!adalah rangkaian arus kas yang berhubungan dengan jadwal pembayaran dalam tanggal!adalah jadwal tanggal pembayaran yang berhubungan dengan pembayaran arus kas"}, "YIELD": {"a": "(settlement; maturity; rate; pr; redemption; frequency; [basis])", "d": "Menampilkan hasil surat berharga yang membayar bunga periodik", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tingkat kupon tahunan surat berharga!adalah harga surat berharga per $100 nilai nominall!adalah nilai penukaran surat berharga per $100 nilai nominal!adalah jumlah pembayaran kupon per tahun!adalah tipe basis hitungan hari yang digunakan"}, "YIELDDISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "Menampilkan hasil tahunan untuk surat berharga yang didiskon. Sebagai contoh, surat utang negara", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah harga surat berharga per $100 nilai nominal!adalah nilai penukaran surat berharga per $100 nilai nominal!adalah jenis basis penghitungan hari yang digunakan"}, "YIELDMAT": {"a": "(settlement; maturity; issue; rate; pr; [basis])", "d": "Menampilkan hasil tahunan surat berharga yang membayar bunga pada tanggal jatuh tempo", "ad": "adalah tanggal pelunasan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal jatuh tempo surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tanggal penerbitan surat berharga, yang dinyatakan sebagai angka tanggal seri!adalah tingkat bunga surat berharga pada tanggal penerbitan!adalah harga surat berharga per $100 nilai nominal!adalah jenis dari basis hitungan hari yang digunakan"}, "ABS": {"a": "(number)", "d": "Menampilkan nilai absolut dari angka, angka tanpa tanda tersebut", "ad": "adalah angka riil yang nilai absolutnya <PERSON>a inginkan"}, "ACOS": {"a": "(number)", "d": "Menampilkan kurva kosinus dari ang<PERSON>, dalam radian pada rentang 0 sampai Pi. <PERSON><PERSON> kosinus adalah sudut yang kosinus<PERSON> berupa <PERSON>", "ad": "ad<PERSON><PERSON> kosinus dari sudut yang <PERSON>a inginkan dan harus dari -1 sampai 1"}, "ACOSH": {"a": "(number)", "d": "Menampilkan kosinus hiperbolik invers dari angka", "ad": "adalah angka riil yang sama dengan atau lebih besar dari 1"}, "ACOT": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON>an kurva kotangen dari angka dalam radian pada rentang 0 hingga Pi.", "ad": "adalah kotangen dari sudut yang <PERSON>a inginkan"}, "ACOTH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON>an kotangen balikan hiperbolik dari angka", "ad": "adalah kotangen hiperbolik dari sudut yang Anda inginkan"}, "AGGREGATE": {"a": "(function_num; options; ref1; ...)", "d": "Menampilkan agregasi dalam sebuah daftar atau database", "ad": "adalah Angka 1 sampai 19 yang menunjukkan ringkasan fungsi dari agregasi.!adalah angka 0 sampai 7 yang menunjukkan nilai yang diabaikan dalam agregasi!adalah array atau rentang data numerik yang akan menghitung agregasi!mengindikasi posisi di array; adalah terbesar ke-k, terkecil ke-k, k perseratus, atau k perempat.!adalah angka 1 sampai 19 yang menunjukkan fungsi ringkasan dari agregasi.!adalah angka 0 sampai 7 menunjukkan nilai yang diabaikan dalam agregasi!adalah rentang 1 sampai 253 atau acuan yang agregasinya Anda inginkan"}, "ARABIC": {"a": "(text)", "d": "Mengonversi angka Romawi ke angka Arab", "ad": "adalah angka <PERSON> yang ingin <PERSON>a konversi"}, "ASC": {"a": "(text)", "d": "<PERSON>tuk bahasa Perangkat karakter bit ganda (DBCS, Double-byte character set), fungsi tersebut mengubah karakter lebar penuh (bit ganda) menjadi lebar setengah (bit tunggal)", "ad": "Teks atau referensi ke suatu sel yang berisi teks yang ingin <PERSON>a ubah."}, "ASIN": {"a": "(number)", "d": "Menampilkan kurva sinus angka dalam radian, dalam rentang -Pi/2 sampai Pi/2", "ad": "adalah sinus dari sudut yang Anda inginkan dan harus dari -1 sampai 1"}, "ASINH": {"a": "(number)", "d": "Menampilkan sinus hiperbolik invers dari angka", "ad": "ad<PERSON>h tiap angka riil sama dengan atau lebih besar dari 1"}, "ATAN": {"a": "(number)", "d": "Menampilkan kurva tangen dari angka dalam radian, pada rentang -Pi/2 ke Pi/2", "ad": "adalah tangen dari sudut yang <PERSON>a inginkan"}, "ATAN2": {"a": "(x_num; y_num)", "d": "Menampilkan kurva tangen dari koordinat -x dan -y yang ditentu<PERSON>, dalam radian antara -<PERSON> dan <PERSON>, tidak term<PERSON>uk -<PERSON>", "ad": "adalah koordinat-x dari titik!adalah koordinat-y dari titik"}, "ATANH": {"a": "(number)", "d": "Menampilkan invers tangen hiperbolik dari angka", "ad": "adalah angka riil di antara -1 dan 1 mengeluarkan -1 dan 1"}, "BASE": {"a": "(number; radix; [min_length])", "d": "Mengonversi angka menjadi representasi teks dengan radiks tertentu (dasar)", "ad": "adalah angka yang ingin Anda konversi!adalah Radiks dasar yang ingin Anda jadikan hasil konversi angka!adalah panjang minimal string yang dihasilkan.  <PERSON><PERSON> di<PERSON>, nol di awal tidak ditambahkan"}, "CEILING": {"a": "(number; significance)", "d": "Membulatkan angka ke atas, ke bilangan bulat terdekat atau ke multipel terdekat dari signifikansi", "ad": "adalah nilai yang ingin Anda bulatkan!adalah multipel yang ingin Anda bulatkan"}, "CEILING.MATH": {"a": "(number; [significance]; [mode])", "d": "Membulatkan angka ke atas, ke bilangan bulat terdekat atau ke kelipatan signifikansi terdekat", "ad": "adalah nilai yang ingin Anda bulatkan!adalah kelipatan opsional yang ingin Anda bulatkan!jika diberikan dan selain nol, fungsi ini akan membulatkan jauh dari nol"}, "CEILING.PRECISE": {"a": "(number; [significance])", "d": "Mengembalikan angka yang dibulatkan ke atas ke bilangan bulat terdekat atau ke kelipatan signifikansi terdekat", "ad": "adalah nilai yang ingin Anda bulatkan!adalah multipel yang ingin Anda bulatkan"}, "COMBIN": {"a": "(number; number_chosen)", "d": "Menampilkan jumlah kombinasi untuk jumlah item yang ditentukan", "ad": "adalah jumlah total item!adalah jumlah item dalam tiap kombinasi"}, "COMBINA": {"a": "(number; number_chosen)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> jumlah kombinasi dengan pengulangan untuk sejumlah objek tertentu", "ad": "adalah total jumlah item!adalah jumlah item di setiap kombinasi"}, "COS": {"a": "(number)", "d": "Menampilkan kosinus dari sudut", "ad": "<PERSON><PERSON> sudut dalam radian kosinus yang <PERSON>a inginkan"}, "COSH": {"a": "(number)", "d": "Menampilkan kosinus hiperbolik dari angka", "ad": "adalah angka real apa saja"}, "COT": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kotangen sudut", "ad": "adalah sudut dalam radian yang Anda inginkan kotangennya"}, "COTH": {"a": "(number)", "d": "Mengh<PERSON>lkan kotangen hiperbolik angka", "ad": "adalah sudut dalam radian yang Anda inginkan kotangen hiperboliknya"}, "CSC": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kosekan sudut", "ad": "adalah sudut dalam radian yang <PERSON>a inginkan kosekannya"}, "CSCH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> kosekan hiperbolik angka", "ad": "adalah sudut dalam radian yang Anda inginkan kosekan hiperboliknya"}, "DECIMAL": {"a": "(number; radix)", "d": "Mengonversi representasi teks suatu angka dalam dasar tertentu ke angka desimal", "ad": "adalah angka yang ingin Anda konversi!adalah Radiks dasar angka yang Anda konversi"}, "DEGREES": {"a": "(angle)", "d": "Mengonversi radian ke derajat", "ad": "adalah sudut dalam radian yang ingin Anda ubah"}, "ECMA.CEILING": {"a": "(number; significance)", "d": "Membulatkan angka ke atas, ke bilangan bulat terdekat atau ke multipel terdekat dari signifikansi", "ad": "adalah nilai yang ingin Anda bulatkan!adalah multipel yang ingin Anda bulatkan"}, "EVEN": {"a": "(number)", "d": "Membulatkan angka positif ke atas dan angka negatif ke bawah ke bilangan bulat genap terdekat", "ad": "adalah nilai untuk dibulatkan"}, "EXP": {"a": "(number)", "d": "Menampilkan ke e pangkat dari angka yang ditentukan", "ad": "adalah eksponen yang diterapkan ke basis e. Konstanta e sama dengan 2,71828182845904, basis dari logaritma alami"}, "FACT": {"a": "(number)", "d": "Menampilkan faktorial dari angka, sama dengan 1*2*3*...* Angka", "ad": "adalah angka yang bukan negatif yang ingin <PERSON> fak<PERSON>an"}, "FACTDOUBLE": {"a": "(number)", "d": "Menampilkan faktorial ganda dari bilangan", "ad": "adalah nilai untuk mengembalikan faktorial ganda"}, "FLOOR": {"a": "(number; significance)", "d": "Membulatkan angka ke bawah, mendekati multipel terdekat dari signifikansi", "ad": "adalah nilai angka yang ingin Anda bulatkan!adalah multipel yang ingin Anda bulatkan. Angka dan Signifikansi harus keduanya positif atau keduanya negatif"}, "FLOOR.PRECISE": {"a": "(number; [significance])", "d": "Mengembalikan angka yang dibulatkan ke bawah ke bilangan bulat terdekat atau ke kelipatan signifikansi terdekat", "ad": "adalah nilai yang ingin Anda bulatkan!adalah multipel yang ingin Anda bulatkan"}, "FLOOR.MATH": {"a": "(number; [significance]; [mode])", "d": "Membulatkan angka ke atas, ke bilangan bulat terdekat atau ke kelipatan signifikansi terdekat", "ad": "adalah nilai yang ingin Anda bulatkan!adalah kelipatan opsional yang ingin Anda bulatkan!jika diberikan dan selain nol, fungsi ini akan membulatkan ke nol"}, "GCD": {"a": "(number1; [number2]; ...)", "d": "Menampilkan pembagi terbesar yang umum", "ad": "adalah 1 sampai 255 nilai"}, "INT": {"a": "(number)", "d": "Membulatkan angka ke bawah ke bilangan bulat terdekat", "ad": "adalah angka riil yang ingin Anda bulatkan ke bawah ke bilangan bulat"}, "ISO.CEILING": {"a": "(number; [significance])", "d": "Mengembalikan angka yang dibulatkan ke atas ke bilangan bulat terdekat atau ke kelipatan signifikansi terdekat. Tanpa memperhatikan lambang angkanya, bilangan itu dibulatkan ke atas. <PERSON><PERSON> tetapi, jika angka signifikansinya nol, maka hasilnya nol.", "ad": "adalah nilai yang ingin Anda bulatkan!adalah multipel yang ingin Anda bulatkan"}, "LCM": {"a": "(number1; [number2]; ...)", "d": "Menampilkan multipel umum paling sedikit", "ad": "adalah 1 sampai 255 yang multipel umum paling sedikitnya <PERSON>a inginkan"}, "LN": {"a": "(number)", "d": "Menampilkan logaritma alami dari angka", "ad": "adalah angka riil positif yang logaritma alaminya Anda inginkan"}, "LOG": {"a": "(number; [base])", "d": "Menampilkan logarima sebuah angka ke basis yang Anda tentukan", "ad": "adalah angka riil positif yang logaritmanya Anda inginkan!adalah basis dari logaritma; 10 jika dihilangkan"}, "LOG10": {"a": "(number)", "d": "Menampilkan logaritma basis-10 dari angka", "ad": "adalah angka riil positif yang logaritma basis-10-nya <PERSON><PERSON>an"}, "MDETERM": {"a": "(array)", "d": "Menampilkan determinan matrik dari array", "ad": "adalah array numerik dengan jumlah baris dan kolom yang sama, baik rentang sel maupun konstanta array"}, "MINVERSE": {"a": "(array)", "d": "Menampilkan matrik invers untuk matrik yang disimpan dalam array", "ad": "adalah array numerik dengan jumlah baris dan kolom yang sama, baik rentang sel maupun konstanta array"}, "MMULT": {"a": "(array1; array2)", "d": "Menampilkan produk matrik dari dua array, array dengan jumlah baris yang sama dengan Array1 dan jumlah kolom yang sama dengan Array2", "ad": "adalah array pertama dari angka untuk dikalikan dan harus memiliki jumlah kolom yang sama dengan Array2 yang memiliki baris"}, "MOD": {"a": "(number; divisor)", "d": "Menampilkan sisanya setelah angka dibagi dengan pembagi", "ad": "adalah angka yang ingin Anda temukan sisanya setelah pembagian dilakukan!adalah angka yang Angkanya ingin Anda bagi"}, "MROUND": {"a": "(number; multiple)", "d": "Menampilkan bilangan yang dibulatkan ke multipel yang diinginkan", "ad": "adalah nilai yang akan dibulatkan!adalah multipel untuk membulatkan bilangan"}, "MULTINOMIAL": {"a": "(number1; [number2]; ...)", "d": "Menampilkan multinomial dari set bilangan", "ad": "adalah 1 sampai 255 yang multinomialnya Anda inginkan"}, "MUNIT": {"a": "(dimension)", "d": "<PERSON><PERSON><PERSON><PERSON>an matriks satuan untuk dimensi yang ditentukan", "ad": "adalah bilangan bulat yang menentukan dimensi matriks satuan yang ingin <PERSON>a has<PERSON>kan"}, "ODD": {"a": "(number)", "d": "Membulatkan angka positif ke atas dan angka negatif ke bawah ke bilangan bulat ganjil terdekat", "ad": "adalah nilai untuk dibulatkan"}, "PI": {"a": "()", "d": "Menampilkan nilai dari <PERSON>, 3,14159265358979, a<PERSON><PERSON> untuk 15 digit", "ad": ""}, "POWER": {"a": "(number; power)", "d": "Menampilkan hasil dari nilai yang dipang<PERSON>kan", "ad": "adalah angka basis, angka riil apa saja!adalah eksponen, tempat angka basis dipangkatkan"}, "PRODUCT": {"a": "(number1; [number2]; ...)", "d": "Mengalikan semua angka yang ditentukan sebagai argumen", "ad": "<PERSON><PERSON><PERSON> angka, nilai logis, atau teks 1 sampai 255 representasi dari angka yang ingin <PERSON>a kalikan"}, "QUOTIENT": {"a": "(numerator; denominator)", "d": "Menampilkan porsi bilangan bulat dari pembagian", "ad": "adalah yang dibagi!adalah pembagi"}, "RADIANS": {"a": "(angle)", "d": "Mengonversi derajat ke radian", "ad": "adalah sudut dalam derajat yang ingin Anda ubah"}, "RAND": {"a": "()", "d": "Menampilkan angka acak lebih besar dari 0 dan kurang dari 1, terdistribusi secara merata (perubahan pada perhitungan ulang)", "ad": ""}, "RANDARRAY": {"a": "([rows]; [columns]; [min]; [max]; [integer])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> larik berisi nomor acak", "ad": "jumlah baris dalam larik yang dihasilkan!jumlah kolom dalam larik yang dihasilkan!jumlah minimum yang ingin dihasilkan!jumlah maksimum yang ingin dihasilkan!hasilkan nilai bilangan bulat atau desimal. TRUE untuk bilangan bulat, FALSE untuk bilangan desimal"}, "RANDBETWEEN": {"a": "(bottom; top)", "d": "Menampilkan bilangan acak antara bilangan yang anda tentukan", "ad": "adalah bilangan bulat terkecil RANDBETWEEN akan muncul!adalah bilangan bulat terbesar RANDBETWEEN akan muncul"}, "ROMAN": {"a": "(number; [form])", "d": "Mengonversi angka Arab ke Romawi, sebagai teks", "ad": "adalah angka Arab yang ingin Anda ubah!adalah angka yang menentukan tipe angka Romawi yang Anda inginkan."}, "ROUND": {"a": "(number; num_digits)", "d": "Membulatkan angka ke jumlah digit yang ditentukan", "ad": "adalah angka yang ingin Anda bulatkan!adalah jumlah digit yang ingin Anda bulatkan. Negatif dibulatkan ke kiri dari titik desimal; nol ke bilangan bulat terdekat"}, "ROUNDDOWN": {"a": "(number; num_digits)", "d": "Membulatkan angka ke bawah, terhadap nol", "ad": "adalah angka riil yang ingin Anda bulatkan ke bawah!adalah jumlah digit yang ingin Anda bulatkan. Negatif dibulatkan ke kiri dari titik desimal; nol atau dihilangkan, ke bilangan bulat terdekat"}, "ROUNDUP": {"a": "(number; num_digits)", "d": "Membulatkan angka ke atas, menjauhi nol", "ad": "adalah angka riil yang ingin Anda bulatkan ke atas!adalah jumlah digit ke mana Anda ingin bulatkan. Negatif dibulatkan ke kiri dari titik desimal; nol atau dihilangkan, ke bilangan bulat terdekat"}, "SEC": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> sekan sudut", "ad": "adalah sudut dalam radian yang Anda inginkan sekannya"}, "SECH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> sekan hiperbolik angka", "ad": "adalah sudut dalam radian yang Anda inginkan sekan hiperboliknya"}, "SERIESSUM": {"a": "(x; n; m; coefficients)", "d": "Menampilkan jumlah dari seri kekuatan berdasarkan pada rumus", "ad": "adalah masukan nilai ke seri kekuatan!adalah kekuatan awal untuk menaikkan x!adalah langkah guna meningkatkan n untuk tiap ketentuan dalam seri!adalah set koefisien yang dikalikan dengan tiap kekuatan berurutan x"}, "SIGN": {"a": "(number)", "d": "Menampilkan tanda dari angka: 1 jika angka adalah positif, nol jika angka adalah nol, atau -1 jika angka adalah negatif", "ad": "adalah angka riil apa saja"}, "SIN": {"a": "(number)", "d": "Menampilkan sinus dari sudut", "ad": "adalah sudut dalam radian sinus yang Anda inginkan. Radian = derajat * PI()/180"}, "SINH": {"a": "(number)", "d": "Menampilkan sinus hiperbolik dari angka", "ad": "adalah angka riil apa saja"}, "SQRT": {"a": "(number)", "d": "Menampilkan akar pangkat dua dari angka", "ad": "ad<PERSON>h angka akar kuadrat yang <PERSON>a ing<PERSON>an"}, "SQRTPI": {"a": "(number)", "d": "Menampilkan akar pangkat dua dari (bilangan * Pi)", "ad": "ad<PERSON>h bilangan yang dikalikan p"}, "SUBTOTAL": {"a": "(function_num; ref1; ...)", "d": "Menampilkan subtotal dalam daftar atau database", "ad": "adalah angka 1 sampai 11 yang menentukan fungsi ringkasan untuk subtotal.!adalah rentang atau referensi 1 sampai 254 yang subtotalnya Anda inginkan"}, "SUM": {"a": "(number1; [number2]; ...)", "d": "Menambah semua angka dalam rentang sel", "ad": "adalah angka 1 sampai 255 untuk dijumlahkan. <PERSON>lai dan teks logis diabaikan dalam sel, termasuk jika diketikkan sebagai argumen"}, "SUMIF": {"a": "(range; criteria; [sum_range])", "d": "Menambah sel yang ditentukan oleh kondisi atau kriteria tertentu", "ad": "adalah rentang sel yang ingin Anda evaluasi!adalah kondisi atau kriteria dalam bentuk angka, eksp<PERSON>i, atau teks yang menetapkan sel mana yang akan ditambahkan!adalah sel sebenarnya untuk dijumlahkan. <PERSON><PERSON> dihil<PERSON>, sel dalam rentang akan digunakan"}, "SUMIFS": {"a": "(sum_range; criteria_range; criteria; ...)", "d": "Tambahkan sel yang ditentukan oleh pemberian set aturan atau kriteria", "ad": "adalah sel sesungguhnya untuk dijumlahkan.!adalah rentang sel yang Anda ingin evaluasi untuk kondisi tertentu!adalah kondisi atau kriteria dalam formulir dari bilangan, eks<PERSON><PERSON><PERSON>, atau teks yang menetapkan sel mana yang akan ditambah"}, "SUMPRODUCT": {"a": "(array1; [array2]; [array3]; ...)", "d": "Menampilkan penjumlahan produk dari rentang atau array terkait", "ad": "adalah array 2 sampai 255 yang ingin Anda kalikan dan tambahkan komponen. Semua array harus berdimensi sama"}, "SUMSQ": {"a": "(number1; [number2]; ...)", "d": "Menampilkan jumlah kuadrat argumen. Argumen dapat berupa angka, array, nama, atau referensi ke sel yang mengandung angka", "ad": "adalah angka, array, nama, atau referensi ke array 1 sampai 255 yang jumlah kuadratnya Anda inginkan"}, "SUMX2MY2": {"a": "(array_x; array_y)", "d": "Menjumlahkan perbedaan di antara kuadrat dua rentang array yang berhubungan", "ad": "adalah rentang atau array angka pertama dan dapat berupa angka atau nama, array, atau referensi yang mengandung angka!adalah rentang atau array angka kedua dan dapat berupa angka atau nama, array, atau referensi yang mengandung angka"}, "SUMX2PY2": {"a": "(array_x; array_y)", "d": "Menampilkan total jumlah penjumlahan kuadrat angka dalam dua rentang array berhubungan", "ad": "adalah rentang atau array angka pertama dan dapat berupa angka atau nama, array, atau referensi yang mengandung angka!adalah rentang atau array angka kedua dan dapat berupa angka atau nama, array, atau referensi yang mengandung angka"}, "SUMXMY2": {"a": "(array_x; array_y)", "d": "Menjumlahkan kuadrat perbedaan dalam dua rentang array yang berhubungan", "ad": "adalah rentang atau array nilai pertama dan dapat berupa angka atau nama, array, atau referensi yang mengandung angka!adalah rentang atau array nilai kedua dan dapat berupa angka atau nama, array, atau referensi yang mengandung angka"}, "TAN": {"a": "(number)", "d": "Menampilkan tangen dari sudut", "ad": "<PERSON><PERSON> sudut dalam radian tangen yang Anda inginkan. Radian = derajat * PI()/180"}, "TANH": {"a": "(number)", "d": "Menampilkan tangen hiperbolik dari angka", "ad": "adalah angka riil apa saja"}, "TRUNC": {"a": "(number; [num_digits])", "d": "Memotong angka ke bilangan bulat dengan menghapus desimalnya, atau pecahan, bagian dari angka tersebut", "ad": "adalah angka yang ingin Anda potong!adalah angka yang menentukan presisi pemotongan, 0 (nol) jika diabaikan"}, "ADDRESS": {"a": "(row_num; column_num; [abs_num]; [a1]; [sheet_text])", "d": "Membuat referensi sel sebagai teks, memberikan nomor baris dan kolom tertentu", "ad": "adalah nomor baris yang digunakan dalam referensi sel: nomor_Baris = 1 untuk baris 1!adalah nomor kolom yang digunakan dalam referensi sel. <PERSON><PERSON><PERSON>, nomor_Kolom, = 4 untuk kolom D!tentukan tipe referensi: absolut = 1; baris absolut/kolom relatif = 2; baris relatif/kolom absolut = 3; relatif = 4!adalah nilai logis yang menentukan gaya referensi: gaya A1 = 1 atau BENAR; gaya R1C1 = 0 atau SALAH!adalah teks yang menentukan nama lembar-kerja yang digunakan sebagai referensi eksternal"}, "CHOOSE": {"a": "(index_num; value1; [value2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> nilai atau tindakan yang dilakukan dari daftar nilai, berda<PERSON><PERSON> nomor indeks", "ad": "menentukan argumen nilai yang mana yang terpilih. Indeks_num harus di antara 1 dan 254, atau rumus atau referensi ke angka di antara 1 dan 254!adalah angka, refer<PERSON>i sel, nama yang diten<PERSON>, rumus, fungsi, atau argumen teks 1 sampai 254 dari mana CHOOSE memilih"}, "COLUMN": {"a": "([reference])", "d": "Menampilkan nomor kolom dari referensi", "ad": "adalah sel atau rentang dari sel yang berdekatan yang Anda ingin kolomnya dinomori. <PERSON><PERSON>, sel yang mengandung fungsi KOLOM digunakan"}, "COLUMNS": {"a": "(array)", "d": "Menampilkan jumlah kolom dalam array atau referensi", "ad": "adalah array atau rumus array, atau referensi ke rentang sel yang jumlah kolomnya Anda inginkan"}, "FORMULATEXT": {"a": "(reference)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> rumus sebagai string", "ad": "adalah referensi ke rumus"}, "HLOOKUP": {"a": "(lookup_value; table_array; row_index_num; [range_lookup])", "d": "Mencari nilai dalam baris teratas dari tabel atau array nilai dan mengembalikan nilai dalam kolom yang sama dari baris yang Anda tentukan", "ad": "adalah nilai yang ditemukan dalam baris pertama dalam tabel dan dapat berupa nilai, referensi atau string teks!adalah tabel teks, angka, atau nilai logis tempat data dicari. Tabel_array dapat direferensikan ke rentang atau nama rentang!adalah nomor baris dalam tabel_array yang jika nilainya cocok harus dikembalikan. Baris pertama dari nilai dalam tabel adalah baris 1!adalah nilai logika: untuk menemukan yang paling mendekati kecocokannya pada baris teratas (urutkan dalam urutan naik) = BENAR atau dihilangkan; temukan yang benar-benar cocok = SALAH"}, "HYPERLINK": {"a": "(link_location; [friendly_name])", "d": "Membuat pintasan atau loncatan yang akan membuka dokumen yang disimpan dalam hard drive Anda, server jaringan, atau pada Internet", "ad": "adalah teks yang memberikan jalur dan nama file ke dokumen untuk membukanya, lokasi hard drive, alamat UNC, atau jalur URL!adalah teks atau angka yang ditampilkan dalam sel. <PERSON><PERSON> di<PERSON>, sel tersebut menampilkan teks lokasi_Tautan"}, "INDEX": {"a": "(array; row_num; [column_num]!reference; row_num; [column_num]; [area_num])", "d": "Menampilkan nilai atau referensi sel pada persimpangan baris atau kolom tertentu, dalam rentang yang ditentukan", "ad": "adalah rentang sel atau konstanta array.!pilih baris dalam Array atau Referensi yang dari sini sebuah nilai dikembalikan. Jika dihilangkan, nomor_Kolom dibutuhkan!pilih kolom dalam Array atau Referensi yang dari sini sebuah nilai dikembalikan. Jika dihilangkan, nomor_Baris dibutuhkan!adalah referensi ke satu atau lebih rentang sel!pilih baris dalam Array atau Referensi yang dari sini sebuah nilai dikembalikan. Jika dihilangkan, nomor_Kolom dibutuhkan!pilih kolom dalam Array atau Referensi yang dari sini sebuah nilai dikembalikan. Jika dihilangkan, nomor_Baris dibutuhkan!pilih rentang dalam Referensi yang dari sini sebuah nilai dikembalikan. Area yang terpilih atau dimasukkan pertama kali adalah area 1, area yang kedua adalah area 2, dan seterusnya"}, "INDIRECT": {"a": "(ref_text; [a1])", "d": "Menampilkan referensi yang ditentukan dengan string teks", "ad": "adalah referensi ke sel yang mengandung gaya referensi- A1 atau -R1C1, nama yang ditentukan sebagai referensi, atau referensi ke sel sebagai string teks!adalah nilai logis yang menentukan tipe referensi dalam teks_Ref: gaya-R1C1 = SALAH; gaya-A1 = BENAR atau dihilangkan"}, "LOOKUP": {"a": "(lookup_value; lookup_vector; [result_vector]!lookup_value; array)", "d": "Mencari nilai dari rentang satu-baris atau satu-kolom atau dari array. Tersedia untuk kompatibilitas terbalik.", "ad": "adalah nilai yang LOOKUP-nya mencari ke dalam Vektor_Lookup dan dapat berupa angka, teks, nilai logis, atau nama atau referensi ke suatu nilai!adalah rentang yang mengandung satu baris atau satu kolom teks, angka, atau nilai logis saja, tempatkan dalam urutan naik!adalah rentang yang mengandung satu baris atau kolom saja, berukuran sama seperti vektor_Lookup!adalah nilai yang LOOKUP-nya mencari ke dalam array dan dapat berupa angka, teks, nilai logis, atau nama atau referensi ke suatu nilai!adalah rentang sel yang mengandung teks, angka, atau nilai logis yang ingin Anda bandingkan dengan nilai_Lookup"}, "MATCH": {"a": "(lookup_value; lookup_array; [match_type])", "d": "Menampilkan posisi relatif item dalam array yang cocok dengan nilai tertentu dalam urutan tertentu", "ad": "adalah nilai yang Anda gunakan untuk mencari nilai yang Anda inginkan dalam array, angka, teks, atau nilai logika, atau referensi ke salah satunya!adalah rentang sel berdekatan yang mengandung nilai pencarian, array dari nilai, atau referensi ke array yang mungkin!adalah angka 1, 0, atau -1 menunjukkan angka yang mana untuk dikembalikan."}, "OFFSET": {"a": "(reference; rows; cols; [height]; [width])", "d": "Menampilkan referensi ke rentang yang adalah jumlah baris dan kolom yang ditentukan dari referensi yang ditentukan", "ad": "adalah referensi di mana Anda ingin memusatkan offset, referensi ke sel atau rentang sel yang berdekatan!adalah jumlah baris, ke atas atau ke bawah, yang Anda inginkan sel kiri-atas hasil merujuk ke!adalah jumlah kolom, ke kiri atau kanan, yang Anda inginkan sel kiri-atas hasil merujuk ke!adalah tinggi, dalam jumlah baris, di mana Anda ingin hasilnya menjadi, memiliki tinggi yang sama seperti Referensi jika dihilangkan!adalah lebar, dalam jumlah kolom, yang hasil<PERSON>a ingink<PERSON>, memiliki lebar yang sama dengan Referensi jika dihilangkan"}, "ROW": {"a": "([reference])", "d": "Menampilkan nomor baris dari referensi", "ad": "adalah sel atau rentang sel tunggal yang Anda ingin barisnya dinomori; jika <PERSON>, kembalikan sel yang mengandung fungsi BARIS"}, "ROWS": {"a": "(array)", "d": "Menampilkan jumlah baris dalam referensi atau array", "ad": "adalah array, rumus array, atau referensi ke rentang sel yang jumlah barisnya <PERSON>a inginkan"}, "TRANSPOSE": {"a": "(array)", "d": "Mengonversi rentang sel vertikal ke rentang horizontal, atau sebaliknya", "ad": "adalah rentang sel pada lembar kerja atau array nilai yang ingin Anda ubah urutannya"}, "UNIQUE": {"a": "(array; [by_col]; [exactly_once])", "d": "Mengembalikan nilai unik dari rentang atau larik.", "ad": "rentang atau larik untuk menghasilkan baris atau kolom unik!adalah nilai logis: membandingkan baris satu sama lain dan menghasilkan baris unik = FALSE atau dihilangkan; membandingkan kolom satu sama lain dan menghasilkan kolom unik = TRUE!adalah nilai logis: menghasilkan baris atau kolom yang muncul tepat satu kali dari larik = TRUE; menghasilkan semua baris atau kolom yang berbeda atau kolom dari larik = FALSE atau dihilangkan"}, "VLOOKUP": {"a": "(lookup_value; table_array; col_index_num; [range_lookup])", "d": "Mencari nilai dalam kolom terkiri dari tabel, k<PERSON><PERSON>an mengembalikan nilai dalam baris yang sama dengan kolom yang Anda tentukan. Secara default, tabel harus diurutkan dalam urutan naik", "ad": "adalah nilai yang dapat ditemukan dalam kolom pertama dari tabel, dan dapat berupa nilai, referensi, atau string teks!adalah tabel teks, angka, atau nilai logis tempat data diambil. Tabel_array dapat direferensikan ke rentang atau nama rentang!adalah nomor kolom dalam tabel_array yang jika nilainya cocok harus dikembalikan. Kolom pertama dari nilai dalam tabel adalah kolom 1!adalah nilai logis: untuk menemukan yang paling mendekati kecocokannya pada kolom pertama (urutkan dalam urutan naik) = BENAR atau dihilangkan; temukan yang benar-benar cocok = SALAH"}, "XLOOKUP": {"a": "(lookup_value; lookup_array; return_array; [if_not_found]; [match_mode]; [search_mode])", "d": "Mencari rentang atau larik untuk dicocokkan dan mengh<PERSON>lkan item yang sesuai dari rentang atau larik kedua. Secara default, kecocokan yang sama persis akan digunakan", "ad": "adalah nilai yang akan dicari!adalah larik atau rentang yang akan dicari!adalah larik atau rentang yang akan dihasilkan!dihasilkan jika tidak ditemukan kecocokan!tentukan cara mencocokkan lookup_value dengan nilai dalam lookup_array!tentukan mode pencarian yang akan digunakan. Secara default, pencarian pertama hingga terakhir akan digunakan"}, "CELL": {"a": "(info_type; [reference])", "d": "Mengembalikan informasi tentang pemformatan, lokasi, atau konten sel", "ad": "nilai teks yang menentukan tipe informasi sel apa yang ingin Anda hasilkan!sel yang Anda inginkan informasinya"}, "ERROR.TYPE": {"a": "(error_val)", "d": "Mengembalikan nilai yang sesuai dengan nilai kes<PERSON>han.", "ad": "adalah nilai kesalahan dimana Anda ingin mengidentif<PERSON>si angka, dan dapat berupa nilai kesalahan aktual atau referensi ke sel yang mengandung nilai kesalahan"}, "ISBLANK": {"a": "(value)", "d": "Mengecek apakah referensi ke sel kosong, dan men<PERSON><PERSON><PERSON>an BENAR atau SALAH", "ad": "adalah sel atau nama yang menu<PERSON>k ke sel yang ingin Anda tes"}, "ISERR": {"a": "(value)", "d": "Memerik<PERSON> apakah nilai adalah kesalahan selain #N/A, dan men<PERSON><PERSON>an TRUE atau FALSE", "ad": "adalah nilai yang ingin diuji. <PERSON><PERSON> dapat mengacu pada sel, rumus, atau nama yang mengacu pada sel, rumus atau nilai"}, "ISERROR": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON> apa<PERSON>h nilai adalah k<PERSON>, dan <PERSON><PERSON><PERSON><PERSON> TRUE atau FALSE", "ad": "adalah nilai yang ingin diuji. <PERSON><PERSON> dapat mengacu pada sel, rumus, atau nama yang mengacu pada sel, rumus atau nilai"}, "ISEVEN": {"a": "(number)", "d": "Menampilkan BENAR jika bilangan genap", "ad": "adalah nilau untuk diuji"}, "ISFORMULA": {"a": "(reference)", "d": "Memeriksa apakah referensi menuju ke sel yang berisi rumus, dan <PERSON><PERSON><PERSON><PERSON>an TRUE atau FALSE", "ad": "adalah referensi ke sel yang ingin Anda uji.  Referensi bisa berupa referensi sel, rumus, atau nama yang mengacu ke sel"}, "ISLOGICAL": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON> apakah nilai adalah nilai logika (BENAR atau SALAH), dan men<PERSON><PERSON><PERSON><PERSON> BENAR atau SALAH", "ad": "adalah nilai yang ingin Anda tes. <PERSON>lai dapat menunjuk ke sel, rumus, atau nama yang menunjuk ke sel, rumus, atau nilai"}, "ISNA": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON> apakah nilai adalah #N/A, dan men<PERSON><PERSON><PERSON><PERSON> BENAR atau SALAH", "ad": "adalah nilai yang ingin Anda tes. <PERSON><PERSON> dapat mengacu pada sel, rumus, atau nama yang mengacu pada sel, rumus atau nilai"}, "ISNONTEXT": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON> apakah nilai bukan teks (sel kosong bukan teks), dan men<PERSON><PERSON><PERSON><PERSON> BENAR atau SALAH", "ad": "adalah nilai yang ingin Anda tes: sel; rumus; atau nama yang menu<PERSON>k ke sel, rumus, atau nilai"}, "ISNUMBER": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON> apakah nilai adalah se<PERSON> an<PERSON>, dan men<PERSON><PERSON><PERSON><PERSON> BENAR atau SALAH", "ad": "adalah nilai yang ingin Anda tes. <PERSON>lai dapat merujuk ke sebuah sel, rumus, atau nama yang merujuk ke sel, rumus atau nilai"}, "ISODD": {"a": "(number)", "d": "Menampilkan BENAR jika bilangan ganjil", "ad": "adalah nilau untuk diuji"}, "ISREF": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON> apakah nilai ad<PERSON>h <PERSON>, dan men<PERSON><PERSON><PERSON><PERSON> BENAR atau SALAH", "ad": "adalah nilai yang ingin Anda tes. <PERSON>lai dapat menunjuk ke sel, rumus, atau nama yang menunjuk ke sel, rumus, atau nilai"}, "ISTEXT": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON> apakah nilai adalah te<PERSON>, dan men<PERSON><PERSON><PERSON><PERSON> BENAR atau SALAH", "ad": "adalah nilai yang ingin Anda tes. <PERSON>lai dapat merujuk ke sebuah sel, rumus, atau nama yang merujuk ke sel, rumus atau nilai"}, "N": {"a": "(value)", "d": "Mengonversi nilai non-angka ke angka, tangg<PERSON> ke nomor seri, BENAR ke 1, yang lain ke 0 (nol)", "ad": "adalah nilai yang ingin <PERSON> ubah"}, "NA": {"a": "()", "d": "Mengembalikan nilai kes<PERSON>han #N/A (nilai tidak tersedia)", "ad": ""}, "SHEET": {"a": "([value])", "d": "<PERSON><PERSON><PERSON><PERSON>an nomor lembar dari lembar yang diacu", "ad": "adalah nama lembar atau referensi yang Anda inginkan nomor lembarnya.  <PERSON><PERSON>, nomor lembar yang berisi fungsi tersebut dikembalikan"}, "SHEETS": {"a": "([reference])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> jumlah lembar di referensi", "ad": "adalah refer<PERSON>i yang ingin Anda ketahui jumlah lembar yang dikandu<PERSON>.  <PERSON><PERSON>, jumlah lembar di buku kerja yang berisi fungsi tersebut dikembalikan"}, "TYPE": {"a": "(value)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> bilangan bulat yang mewakili jenis data dari nilai: angka = 1; teks = 2; nilai logis = 4; nilai k<PERSON> = 16; larik = 64; data gabungan = 128", "ad": "dapat berupa sembarang nilai"}, "AND": {"a": "(logical1; [logical2]; ...)", "d": "Memerik<PERSON> apakah semua argumen adalah BENAR, dan men<PERSON><PERSON><PERSON>an BENAR jika semua argumen adalah BENAR", "ad": "adalah kondisi 1 sampai 255 yang ingin Anda tes yang dapat bernilai BENAR atau SALAH dan dapat berupa nilai, array, atau referensi logis"}, "FALSE": {"a": "()", "d": "Menampilkan nilai logika SALAH", "ad": ""}, "IF": {"a": "(logical_test; [value_if_true]; [value_if_false])", "d": "Memerik<PERSON> apakah kond<PERSON>, dan men<PERSON><PERSON><PERSON>an satu nilai jika BENAR, dan nilai lain jika SALAH", "ad": "adalah nilai atau ekspresi apa saja yang dapat dievalusi ke BENAR atau SALAH!adalah nilai yang dikembalikan jika tes_Logika adalah BENAR. <PERSON><PERSON>, BENAR dikembalikan. Anda dapat menyarangkan hingga tujuh fungsi IF!adalah nilai yang dikembalikan jika tes_Logis adalah SALAH. <PERSON><PERSON> di<PERSON>, SALAH dikembalikan"}, "IFS": {"a": "(logical_test; value_if_true; ...)", "d": "Memeriksa apakah satu atau beberapa syarat terpenuhi dan mengembalikan nilai sesuai dengan syarat TRUE pertama", "ad": "adalah nilai atau ekspresi yang dapat dievaluasi untuk TRUE atau FALSE!adalah nilai yang dikembalikan jika Logical_test TRUE"}, "IFERROR": {"a": "(value; value_if_error)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> nilai_jika_kesalahan jika ekspresi merupakan kesalahan dan nilai dari ekspresi itu sendiri jika tidak", "ad": "adalah nilai atau ekspresi atau referensi apasaja!adalah semua nilai atau ekspresi atau referensi"}, "IFNA": {"a": "(value; value_if_na)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> nilai yang Anda tentukan jika ekspresi terpisah ke #N/A, jika tidak, hasil ekspresi akan diberikan", "ad": "adalah sembarang nilai atau ekspresi atau referensi!adalah nilai atau ekspresi atau referensi"}, "NOT": {"a": "(logical)", "d": "Mengubah SALAH ke BENAR, atau BENAR ke SALAH", "ad": "adalah nilai atau eksperesi yang dapat dievaluasi ke BENAR atau SALAH"}, "OR": {"a": "(logical1; [logical2]; ...)", "d": "Memerik<PERSON> apakah tiap argumen adalah BENAR, dan men<PERSON><PERSON><PERSON> BENAR atau SALAH. Menampilkan SALAH hanya jika semua argumen adalah SALAH", "ad": "ad<PERSON>h kond<PERSON> 1 sampai 255 yang ingin Anda tes yang dapat bernilai BENAR atau SALAH"}, "SWITCH": {"a": "(expression; value1; result1; [default_or_value2]; [result2]; ...)", "d": "Mengevaluasi ekspresi terhadap daftar nilai-nilai dan mengembalikan hasil yang sesuai dengan nilai pertama yang cocok. Jika tidak ada yang cocok, nilai default opsional dikembalikan", "ad": "adalah ekspresi untuk dievaluasi!adalah nilai yang akan dibandingkan dengan ekspresi!adalah hasil yang akan dikembalikan jika nilai yang terkait cocok dengan ekspresi"}, "TRUE": {"a": "()", "d": "Menampilkan nilai logika BENAR", "ad": ""}, "XOR": {"a": "(logical1; [logical2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> logis dari semua argumen", "ad": "adalah 1 hingga 254 syarat yang ingin Anda uji yang boleh saja TRUE atau FALSE dan bisa saja berupa nilai logis, array, atau referensi"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Mengembalikan teks sebelum karakter pemisah.", "ad": "Teks yang ingin Anda cari pemisahnya.!<PERSON><PERSON><PERSON> atau string yang akan digunakan sebagai pemisah.!Kemunculan pemisah yang diinginkan. Defaultnya adalah 1. Angka negatif mencari dari akhir.!Mencari pemisah yang cocok di teks. Secara default, kecocokan peka huruf besar/kecil telah dilakukan.!Apakah akan mencocokkan pemisah dengan akhir teks. Secara default, keduanya tidak cocok.!Dikembalikan jika tidak ditemukan yang cocok. Secara default, #N/A dikembalikan."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Mengembalikan teks setelah karakter pemisah.", "ad": "Teks yang ingin Anda cari pemisahnya.!<PERSON><PERSON><PERSON> atau string yang akan digunakan sebagai pemisah.!Kemunculan pemisah yang diinginkan. Defaultnya adalah 1. Angka negatif mencari dari akhir.!Mencari pemisah yang cocok di teks. Secara default, kecocokan peka huruf besar/kecil telah dilakukan.!Apakah akan mencocokkan pemisah dengan akhir teks. Secara default, keduanya tidak cocok.!Dikembalikan jika tidak ditemukan kecocokan. Secara default, #N/A dikembalikan."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Membagi teks menjadi baris atau kolom menggunakan pemisah.", "ad": "Teks yang akan dipisahkan!<PERSON><PERSON><PERSON> atau string yang digunakan untuk memisahkan kolom.!<PERSON><PERSON>er atau string yang digunakan untuk memisahkan baris.!Apakah akan mengabaikan sel kosong. Defaultnya adalah FALSE.!Mencari pemisah yang cocok di teks. Secara default, kecocokan peka huruf besar/kecil telah dilakukan.!<PERSON>lai yang akan digunakan untuk pengisi. Secara default, #N/A adalah digunakan."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Membungkus vektor baris atau kolom setelah jumlah nilai yang ditentukan.", "ad": "Vektor atau referensi untuk membungkus.!Jumlah nilai maksimal per baris.!<PERSON><PERSON> yang akan di-pad. Defaultnya adalah #N/A."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "<PERSON><PERSON><PERSON> array secara vertikal menjadi satu array.", "ad": "Array atau referensi yang akan di<PERSON>k."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Menumpuk array secara horizontal menjadi satu array.", "ad": "Array atau referensi yang akan di<PERSON>k."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Mengembalikan baris dari array atau referensi.", "ad": "Array atau referensi yang berisi baris yang akan dikembalikan.!Jumlah baris yang akan dikembalikan."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Mengembalikan kolom dari array atau referensi.", "ad": "Array atau referensi yang berisi kolom yang akan dikembalikan.!Ju<PERSON>lah kolom yang akan dikembalikan."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Mengembalikan array sebagai satu kolom.", "ad": "Array atau referensi yang akan dikembalikan sebagai kolom.!Apakah akan mengabaikan tipe nilai tertentu. Secara default, tidak ada nilai yang diabaikan.!Memindai array menurut kolom. Secara default, array dipindai berdasarkan baris."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> array sebagai satu baris.", "ad": "Array atau referensi yang akan dihasilkan sebagai baris.!Apakah akan mengabaikan tipe nilai tertentu. Secara default, tidak ada nilai yang diabaikan.!Memindai array menurut kolom. Secara default, array dipindai berdasarkan baris."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Membungkus vektor baris atau kolom setelah jumlah nilai yang ditentukan.", "ad": "Vektor atau referensi untuk membungkus.!Jumlah nilai maksimal per kolom.!<PERSON><PERSON> yang akan di-pad. Defaultnya adalah #N/A."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Mengembalikan baris atau kolom dari awal atau akhir array.", "ad": "Array tempat baris atau kolom diambil.!Jumlah baris yang akan diambil. Nilai negatif diambil dari akhir array.!<PERSON><PERSON><PERSON> kolom yang akan diambil. Nilai negatif diambil dari akhir array."}, "DROP": {"a": "(array, rows, [columns])", "d": "Menghapus baris atau kolom dari awal atau akhir array.", "ad": "Array tempat baris atau kolom dihapus.!Jumlah baris yang akan dihapus. <PERSON>lai negatif dihapus dari akhir array.!<PERSON><PERSON><PERSON> kolom yang akan dihapus. Nilai negatif dihapus dari akhir array."}, "SEQUENCE": {"a": "(rows, [columns], [start], [step])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> urutan nomor", "ad": "jumlah baris untuk dihasilkan!jumlah kolom untuk dihasilkan!angka pertama dalam urutan!jumlah yang ditambahkan ke setiap nilai berikutnya dalam urutan"}, "EXPAND": {"a": "(array, rows, [columns], [pad_with])", "d": "Memperluas array ke dimensi yang ditentukan.", "ad": "Array yang akan diperluas.!<PERSON><PERSON>lah baris dalam array yang diperluas. <PERSON><PERSON> tidak ada, baris tidak akan diperluas.!<PERSON><PERSON><PERSON> kolom dalam array yang diperluas. <PERSON><PERSON> tidak ada, kolom tidak akan diperluas.!<PERSON><PERSON> yang akan diisi. Defaultnya adalah #N/A."}, "XMATCH": {"a": "(lookup_value, lookup_array, [match_mode], [search_mode])", "d": "<PERSON><PERSON><PERSON><PERSON>an posisi relatif item dalam array. Secara default, kecocokan yang sama persis diperlukan", "ad": "adalah nilai yang akan dicari!adalah array atau rentang untuk dicari!tentukan cara mencocokkan lookup_value dengan nilai-nilai dalam lookup_array!tentukan mode pencarian yang akan digunakan. Secara default, pencarian pertama hingga terakhir akan digunakan"}, "FILTER": {"a": "(array, include, [if_empty])", "d": "Filter rentang atau larik", "ad": "rentang atau larik yang difilter!larik boolean dengan nilai TRUE yang mewakili baris atau kolom yang dipertahankan!dikembalikan jika tidak ada item yang dipertahankan"}, "ARRAYTOTEXT": {"a": "(array, [format])", "d": "Mengembalikan representasi teks larik", "ad": "larik di<PERSON>pilkan sebagai teks!format teks"}, "SORT": {"a": "(array, [sort_index], [sort_order], [by_col])", "d": "Mengurut<PERSON> rentang atau larik", "ad": "rentang atau larik untuk diurutkan!angka yang menandakan baris atau kolom untuk diurutkan!angka yang menandakan urutan sortir yang diinginkan; 1 untuk urutan naik (default), -1 untuk urutan menurun!nilai logika yang menandakan arah urutan yang diinginkan: FALSE untuk mengurutkan berdasarkan baris (default), TRUE untuk mengurutkan berdasarkan kolom"}, "SORTBY": {"a": "(array, by_array, [sort_order], ...)", "d": "Mengurutkan rentang atau larik berdasarkan nilai dalam rentang atau larik yang sesuai", "ad": "rentang atau larik untuk diurutkan!rentang atau larik untuk diurutkan!angka yang menunjukkan urutan sortir yang diinginkan; 1 untuk urutan naik (default), -1 untuk urutan turun"}, "GETPIVOTDATA": {"a": "(data_field; pivot_table; [field]; [item]; ...)", "d": "Mengekstrak data yang tersimpan dalam sebuah PivotTable.", "ad": "adalah nama dari bidang data untuk ekstrak data!adalah referensi ke sebuah sel atau rentang sel dalam PivotTable yang berisikan data yang ingin Anda ambil!bidang acuan!item bidang untuk dirujuk"}, "IMPORTRANGE": {"a": "(url_spreadsheet; string_rentang)", "d": "Mengimpor rentang sel dari spreadsheet tertentu.", "ad": "URL spreadsheet tempat asal data yang akan diimpor!String yang menentukan rentang yang akan diimpor"}}