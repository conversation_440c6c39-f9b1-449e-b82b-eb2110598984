{"DATE": "FECHA", "DATEDIF": "SIFECHA", "DATEVALUE": "FECHANUMERO", "DAY": "DIA", "DAYS": "DIAS", "DAYS360": "DIAS360", "EDATE": "FECHA.MES", "EOMONTH": "FIN.MES", "HOUR": "HORA", "ISOWEEKNUM": "ISO.NUM.DE.SEMANA", "MINUTE": "MINUTO", "MONTH": "MES", "NETWORKDAYS": "DIAS.LAB", "NETWORKDAYS.INTL": "DIAS.LAB.INTL", "NOW": "AHORA", "SECOND": "SEGUNDO", "TIME": "NSHORA", "TIMEVALUE": "HORANUMERO", "TODAY": "HOY", "WEEKDAY": "DIASEM", "WEEKNUM": "NUM.DE.SEMANA", "WORKDAY": "DIA.LAB", "WORKDAY.INTL": "DIA.LAB.INTL", "YEAR": "AÑO", "YEARFRAC": "FRAC.AÑO", "BESSELI": "BESSELI", "BESSELJ": "BESSELJ", "BESSELK": "BESSELK", "BESSELY": "BESSELY", "BIN2DEC": "BIN.A.DEC", "BIN2HEX": "BIN.A.HEX", "BIN2OCT": "BIN.A.OCT", "BITAND": "BIT.Y", "BITLSHIFT": "BIT.DESPLIZQDA", "BITOR": "BIT.O", "BITRSHIFT": "BIT.DESPLDCHA", "BITXOR": "BIT.XO", "COMPLEX": "COMPLEJO", "CONVERT": "CONVERTIR", "DEC2BIN": "DEC.A.BIN", "DEC2HEX": "DEC.A.HEX", "DEC2OCT": "DEC.A.OCT", "DELTA": "DELTA", "ERF": "FUN.ERROR", "ERF.PRECISE": "FUN.ERROR.EXACTO", "ERFC": "FUN.ERROR.COMPL", "ERFC.PRECISE": "FUN.ERROR.COMPL.EXACTO", "GESTEP": "MAYOR.O.IGUAL", "HEX2BIN": "HEX.A.BIN", "HEX2DEC": "HEX.A.DEC", "HEX2OCT": "HEX.A.OCT", "IMABS": "IM.ABS", "IMAGINARY": "IMAGINARIO", "IMARGUMENT": "IM.ANGULO", "IMCONJUGATE": "IM.CONJUGADA", "IMCOS": "IM.COS", "IMCOSH": "IM.COSH", "IMCOT": "IM.COT", "IMCSC": "IM.CSC", "IMCSCH": "IM.CSCH", "IMDIV": "IM.DIV", "IMEXP": "IM.EXP", "IMLN": "IM.LN", "IMLOG10": "IM.LOG10", "IMLOG2": "IM.LOG2", "IMPOWER": "IM.POT", "IMPRODUCT": "IM.PRODUCT", "IMREAL": "IM.REAL", "IMSEC": "IM.SEC", "IMSECH": "IM.SECH", "IMSIN": "IM.SENO", "IMSINH": "IM.SENOH", "IMSQRT": "IM.RAIZ2", "IMSUB": "IM.SUSTR", "IMSUM": "IM.SUM", "IMTAN": "IM.TAN", "OCT2BIN": "OCT.A.BIN", "OCT2DEC": "OCT.A.DEC", "OCT2HEX": "OCT.A.HEX", "DAVERAGE": "BDPROMEDIO", "DCOUNT": "BDCONTAR", "DCOUNTA": "BDCONTARA", "DGET": "BDEXTRAER", "DMAX": "BDMAX", "DMIN": "BDMIN", "DPRODUCT": "BDPRODUCTO", "DSTDEV": "BDDESVEST", "DSTDEVP": "BDDESVESTP", "DSUM": "BDSUMA", "DVAR": "BDVAR", "DVARP": "BDVARP", "CHAR": "CARACTER", "CLEAN": "LIMPIAR", "CODE": "CODIGO", "CONCATENATE": "CONCATENAR", "CONCAT": "CONCAT", "DOLLAR": "MONEDA", "EXACT": "IGUAL", "FIND": "ENCONTRAR", "FINDB": "ENCONTRARB", "FIXED": "DECIMAL", "LEFT": "IZQUIERDA", "LEFTB": "IZQUIERDAB", "LEN": "LARGO", "LENB": "LARGOB", "LOWER": "MINUSC", "MID": "EXTRAE", "MIDB": "EXTRAEB", "NUMBERVALUE": "VALOR.NUMERO", "PROPER": "NOMPROPIO", "REPLACE": "REEMPLAZAR", "REPLACEB": "REEMPLAZARB", "REPT": "REPETIR", "RIGHT": "DERECHA", "RIGHTB": "DERECHAB", "SEARCH": "HALLAR", "SEARCHB": "HALLARB", "SUBSTITUTE": "SUSTITUIR", "T": "T", "T.TEST": "PRUEBA.T.N", "TEXT": "TEXTO", "TEXTJOIN": "UNIRCADENAS", "TREND": "TENDENCIA", "TRIM": "ESPACIOS", "TRIMMEAN": "MEDIA.ACOTADA", "TTEST": "PRUEBA.T", "UNICHAR": "UNICHAR", "UNICODE": "UNICODE", "UPPER": "MAYUSC", "VALUE": "VALOR", "AVEDEV": "DESVPROM", "AVERAGE": "PROMEDIO", "AVERAGEA": "PROMEDIOA", "AVERAGEIF": "PROMEDIO.SI", "AVERAGEIFS": "PROMEDIO.SI.CONJUNTO", "BETADIST": "DISTR.BETA", "BETAINV": "DISTR.BETA.INV", "BETA.DIST": "DISTR.BETA.N", "BETA.INV": "INV.BETA.N", "BINOMDIST": "DISTR.BINOM", "BINOM.DIST": "DISTR.BINOM.N", "BINOM.DIST.RANGE": "DISTR.BINOM.SERIE", "BINOM.INV": "INV.BINOM", "CHIDIST": "DISTR.CHI", "CHIINV": "PRUEBA.CHI.INV", "CHITEST": "PRUEBA.CHI", "CHISQ.DIST": "DISTR.CHICUAD", "CHISQ.DIST.RT": "DISTR.CHICUAD.CD", "CHISQ.INV": "INV.CHICUAD", "CHISQ.INV.RT": "INV.CHICUAD.CD", "CHISQ.TEST": "PRUEBA.CHICUAD", "CONFIDENCE": "INTERVALO.CONFIANZA", "CONFIDENCE.NORM": "INTERVALO.CONFIANZA.NORM", "CONFIDENCE.T": "INTERVALO.CONFIANZA.T", "CORREL": "COEF.DE.CORREL", "COUNT": "CONTAR", "COUNTA": "CONTARA", "COUNTBLANK": "CONTAR.BLANCO", "COUNTIF": "CONTAR.SI", "COUNTIFS": "CONTAR.SI.CONJUNTO", "COVAR": "COVAR", "COVARIANCE.P": "COVARIANZA.P", "COVARIANCE.S": "COVARIANZA.M", "CRITBINOM": "BINOM.CRIT", "DEVSQ": "DESVIA2", "EXPON.DIST": "DISTR.EXP.N", "EXPONDIST": "DISTR.EXP", "FDIST": "DISTR.F", "FINV": "DISTR.F.INV", "FTEST": "PRUEBA.F", "F.DIST": "DISTR.F.N", "F.DIST.RT": "DISTR.F.CD", "F.INV": "INV.F", "F.INV.RT": "INV.F.CD", "F.TEST": "PRUEBA.F.N", "FISHER": "FISHER", "FISHERINV": "PRUEBA.FISHER.INV", "FORECAST": "PRONOSTICO", "FORECAST.ETS": "PRONOSTICO.ETS", "FORECAST.ETS.CONFINT": "PRONOSTICO.ETS.CONFINT", "FORECAST.ETS.SEASONALITY": "PRONOSTICO.ETS.ESTACIONALIDAD", "FORECAST.ETS.STAT": "PRONOSTICO.ETS.ESTADISTICA", "FORECAST.LINEAR": "PRONOSTICO.LINEAL", "FREQUENCY": "FRECUENCIA", "GAMMA": "GAMMA", "GAMMADIST": "DISTR.GAMMA", "GAMMA.DIST": "DISTR.GAMMA.N", "GAMMAINV": "DISTR.GAMMA.INV", "GAMMA.INV": "INV.GAMMA", "GAMMALN": "GAMMA.LN", "GAMMALN.PRECISE": "GAMMA.LN.EXACTO", "GAUSS": "GAUSS", "GEOMEAN": "MEDIA.GEOM", "GROWTH": "CRECIMIENTO", "HARMEAN": "MEDIA.ARMO", "HYPGEOM.DIST": "DISTR.HIPERGEOM.N", "HYPGEOMDIST": "DISTR.HIPERGEOM", "INTERCEPT": "INTERSECCION.EJE", "KURT": "CURTOSIS", "LARGE": "K.ESIMO.MAYOR", "LINEST": "ESTIMACION.LINEAL", "LOGEST": "ESTIMACION.LOGARITMICA", "LOGINV": "DISTR.LOG.INV", "LOGNORM.DIST": "DISTR.LOGNORM", "LOGNORM.INV": "INV.LOGNORM", "LOGNORMDIST": "DISTR.LOG.NORM", "MAX": "MAX", "MAXA": "MAXA", "MAXIFS": "MAX.SI.CONJUNTO", "MEDIAN": "MEDIANA", "MIN": "MIN", "MINA": "MINA", "MINIFS": "MIN.SI.CONJUNTO", "MODE": "MODA", "MODE.MULT": "MODA.VARIOS", "MODE.SNGL": "MODA.UNO", "NEGBINOM.DIST": "NEGBINOM.DIST", "NEGBINOMDIST": "NEGBINOMDIST", "NORM.DIST": "DISTR.NORM.N", "NORM.INV": "INV.NORM", "NORM.S.DIST": "DISTR.NORM.ESTAND.N", "NORM.S.INV": "INV.NORM.ESTAND", "NORMDIST": "DISTR.NORM", "NORMINV": "DISTR.NORM.INV", "NORMSDIST": "DISTR.NORM.ESTAND", "NORMSINV": "DISTR.NORM.ESTAND.INV", "PEARSON": "PEARSON", "PERCENTILE": "PERCENTIL", "PERCENTILE.EXC": "PERCENTIL.EXC", "PERCENTILE.INC": "PERCENTIL.INC", "PERCENTRANK": "RANGO.PERCENTIL", "PERCENTRANK.EXC": "RANGO.PERCENTIL.EXC", "PERCENTRANK.INC": "RANGO.PERCENTIL.INC", "PERMUT": "PERMUTACIONES", "PERMUTATIONA": "PERMUTACIONES.A", "PHI": "FI", "POISSON": "POISSON", "POISSON.DIST": "POISSON.DIST", "PROB": "PROBABILIDAD", "QUARTILE": "CUARTIL", "QUARTILE.INC": "CUARTIL.INC", "QUARTILE.EXC": "CUARTIL.EXC", "RANK.AVG": "JERARQUIA.MEDIA", "RANK.EQ": "JERARQUIA.EQV", "RANK": "JERARQUIA", "RSQ": "COEFICIENTE.R2", "SKEW": "COEFICIENTE.ASIMETRIA", "SKEW.P": "COEFICIENTE.ASIMETRIA.P", "SLOPE": "PENDIENTE", "SMALL": "K.ESIMO.MENOR", "STANDARDIZE": "NORMALIZACION", "STDEV": "DESVEST", "STDEV.P": "DESVEST.P", "STDEV.S": "DESVEST.M", "STDEVA": "DESVESTA", "STDEVP": "DESVESTP", "STDEVPA": "DESVESTPA", "STEYX": "ERROR.TIPICO.XY", "TDIST": "DISTR.T", "TINV": "DISTR.T.INV", "T.DIST": "DISTR.T.N", "T.DIST.2T": "DISTR.T.2C", "T.DIST.RT": "DISTR.T.CD", "T.INV": "INV.T", "T.INV.2T": "INV.T.2C", "VAR": "VAR", "VAR.P": "VAR.P", "VAR.S": "VAR.S", "VARA": "VARA", "VARP": "VARP", "VARPA": "VARPA", "WEIBULL": "DIST.WEIBULL", "WEIBULL.DIST": "DISTR.WEIBULL", "Z.TEST": "PRUEBA.Z.N", "ZTEST": "PRUEBA.Z", "ACCRINT": "INT.ACUM", "ACCRINTM": "INT.ACUM.V", "AMORDEGRC": "AMORTIZ.PROGRE", "AMORLINC": "AMORTIZ.LIN", "COUPDAYBS": "CUPON.DIAS.L1", "COUPDAYS": "CUPON.DIAS", "COUPDAYSNC": "CUPON.DIAS.L2", "COUPNCD": "CUPON.FECHA.L2", "COUPNUM": "CUPON.NUM", "COUPPCD": "CUPON.FECHA.L1", "CUMIPMT": "PAGO.INT.ENTRE", "CUMPRINC": "PAGO.PRINC.ENTRE", "DB": "DB", "DDB": "DDB", "DISC": "TASA.DESC", "DOLLARDE": "MONEDA.DEC", "DOLLARFR": "MONEDA.FRAC", "DURATION": "DURACION", "EFFECT": "INT.EFECTIVO", "FV": "VF", "FVSCHEDULE": "VF.PLAN", "INTRATE": "TASA.INT", "IPMT": "PAGOINT", "IRR": "TIR", "ISPMT": "INT.PAGO.DIR", "MDURATION": "DURACION.MODIF", "MIRR": "TIRM", "NOMINAL": "TASA.NOMINAL", "NPER": "NPER", "NPV": "VNA", "ODDFPRICE": "PRECIO.PER.IRREGULAR.1", "ODDFYIELD": "RENDTO.PER.IRREGULAR.1", "ODDLPRICE": "PRECIO.PER.IRREGULAR.2", "ODDLYIELD": "RENDTO.PER.IRREGULAR.2", "PDURATION": "P.DURACION", "PMT": "PAGO", "PPMT": "PAGOPRIN", "PRICE": "PRECIO", "PRICEDISC": "PRECIO.DESCUENTO", "PRICEMAT": "PRECIO.VENCIMIENTO", "PV": "VA", "RATE": "TASA", "RECEIVED": "CANTIDAD.RECIBIDA", "RRI": "RRI", "SLN": "SLN", "SYD": "SYD", "TBILLEQ": "LETRA.DE.TES.EQV.A.BONO", "TBILLPRICE": "LETRA.DE.TES.PRECIO", "TBILLYIELD": "LETRA.DE.TES.RENDTO", "VDB": "DVS", "XIRR": "TIR.NO.PER", "XNPV": "VNA.NO.PER", "YIELD": "RENDTO", "YIELDDISC": "RENDTO.DESC", "YIELDMAT": "RENDTO.VENCTO", "ABS": "ABS", "ACOS": "ACOS", "ACOSH": "ACOSH", "ACOT": "ACOT", "ACOTH": "ACOTH", "AGGREGATE": "AGREGAR", "ARABIC": "NUMERO.ARABE", "ASC": "ASC", "ASIN": "ASENO", "ASINH": "ASENOH", "ATAN": "ATAN", "ATAN2": "ATAN2", "ATANH": "ATANH", "BASE": "BASE", "CEILING": "MULTIPLO.SUPERIOR", "CEILING.MATH": "MULTIPLO.SUPERIOR.MAT", "CEILING.PRECISE": "MULTIPLO.SUPERIOR.EXACTO", "COMBIN": "COMBINAT", "COMBINA": "COMBINA", "COS": "COS", "COSH": "COSH", "COT": "COT", "COTH": "COTH", "CSC": "CSC", "CSCH": "CSCH", "DECIMAL": "CONV.DECIMAL", "DEGREES": "GRADOS", "ECMA.CEILING": "MULTIPLO.SUPERIOR.ECMA", "EVEN": "REDONDEA.PAR", "EXP": "EXP", "FACT": "FACT", "FACTDOUBLE": "FACT.DOBLE", "FLOOR": "MULTIPLO.INFERIOR", "FLOOR.PRECISE": "MULTIPLO.INFERIOR.EXACTO", "FLOOR.MATH": "MULTIPLO.INFERIOR.MAT", "GCD": "M.C.D", "INT": "ENTERO", "ISO.CEILING": "MULTIPLO.SUPERIOR.ISO", "LCM": "M.C.M", "LN": "LN", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "MDETERM", "MINVERSE": "MINVERSA", "MMULT": "MMULT", "MOD": "RESIDUO", "MROUND": "REDOND.MULT", "MULTINOMIAL": "MULTINOMIAL", "MUNIT": "M.UNIDAD", "ODD": "REDONDEA.IMPAR", "PI": "PI", "POWER": "POTENCIA", "PRODUCT": "PRODUCTO", "QUOTIENT": "COCIENTE", "RADIANS": "RADIANES", "RAND": "ALEATORIO", "RANDARRAY": "MATRIZALEAT", "RANDBETWEEN": "ALEATORIO.ENTRE", "ROMAN": "NUMERO.ROMANO", "ROUND": "REDONDEAR", "ROUNDDOWN": "REDONDEAR.MENOS", "ROUNDUP": "REDONDEAR.MAS", "SEC": "SEC", "SECH": "SECH", "SERIESSUM": "SUMA.SERIES", "SIGN": "SIGNO", "SIN": "SENO", "SINH": "SENOH", "SQRT": "RAIZ", "SQRTPI": "RAIZ2PI", "SUBTOTAL": "SUBTOTALES", "SUM": "SUMA", "SUMIF": "SUMAR.SI", "SUMIFS": "SUMAR.SI.CONJUNTO", "SUMPRODUCT": "SUMAPRODUCTO", "SUMSQ": "SUMA.CUADRADOS", "SUMX2MY2": "SUMAX2MENOSY2", "SUMX2PY2": "SUMAX2MASY2", "SUMXMY2": "SUMAXMENOSY2", "TAN": "TAN", "TANH": "TANH", "TRUNC": "TRUNCAR", "ADDRESS": "DIRECCION", "CHOOSE": "ELEGIR", "COLUMN": "COLUMNA", "COLUMNS": "COLUMNAS", "FORMULATEXT": "FORMULATEXTO", "HLOOKUP": "BUSCARH", "HYPERLINK": "HIPERVINCULO", "INDEX": "INDICE", "INDIRECT": "INDIRECTO", "LOOKUP": "BUSCAR", "MATCH": "COINCIDIR", "OFFSET": "DESREF", "ROW": "FILA", "ROWS": "FILAS", "TRANSPOSE": "TRANSPONER", "UNIQUE": "UNIQUE", "VLOOKUP": "BUSCARV", "XLOOKUP": "BUSCARX", "CELL": "CELDA", "ERROR.TYPE": "TIPO.DE.ERROR", "ISBLANK": "ESBLANCO", "ISERR": "ESERR", "ISERROR": "ESERROR", "ISEVEN": "ES.PAR", "ISFORMULA": "ESFORMULA", "ISLOGICAL": "ESLOGICO", "ISNA": "ESNOD", "ISNONTEXT": "ESNOTEXTO", "ISNUMBER": "ESNUMERO", "ISODD": "ES.IMPAR", "ISREF": "ESREF", "ISTEXT": "ESTEXTO", "N": "N", "NA": "NOD", "SHEET": "HOJA", "SHEETS": "HOJAS", "TYPE": "TIPO", "AND": "Y", "FALSE": "FALSO", "IF": "SI", "IFS": "SI.CONJUNTO", "IFERROR": "SI.ERROR", "IFNA": "SI.ND", "NOT": "NO", "OR": "O", "SWITCH": "CAMBIAR", "TRUE": "VERDADERO", "XOR": "XO", "TEXTBEFORE": "TEXTOANTES", "TEXTAFTER": "TEXTODESPUES", "TEXTSPLIT": "DIVIDIRTEXTO", "WRAPROWS": "AJUSTARFILAS", "VSTACK": "APILARV", "HSTACK": "APILARH", "CHOOSEROWS": "ELEGIRFILAS", "CHOOSECOLS": "ELEGIRCOLS", "TOCOL": "ENCOL", "TOROW": "ENFILA", "WRAPCOLS": "AJUSTARCOLS", "TAKE": "TOMAR", "DROP": "EXCLUIR", "SEQUENCE": "SECUENCIA", "EXPAND": "EXPANDIR", "XMATCH": "COINCIDIRX", "FILTER": "FILTRAR", "ARRAYTOTEXT": "MATRIZATEXTO", "SORT": "ORDENAR", "SORTBY": "ORDENARPOR", "GETPIVOTDATA": "IMPORTARDATOSDINAMICOS", "IMPORTRANGE": "IMPORTRANGE", "LocalFormulaOperands": {"StructureTables": {"h": "Encabezados", "d": "Datos", "a": "Todo", "tr": "Esta file", "t": "Totales"}, "CONST_TRUE_FALSE": {"t": "VERDADERO", "f": "FALSO"}, "CONST_ERROR": {"nil": "#NULL!", "div": "#DIV/0!", "value": "#VALUE!", "ref": "#REF!", "name": "#NAME\\?", "num": "#NUM!", "na": "#N/A", "getdata": "#GETTING_DATA", "uf": "#UNSUPPORTED_FUNCTION!", "calc": "#CALC!"}, "CELL_FUNCTION_INFO_TYPE": {"address": "DIRECCIÓN", "col": "COLUMNA", "color": "COLOR", "contents": "CONTENIDO", "filename": "ARCHIVO", "format": "FORMATO", "parentheses": "PARÉNTESIS", "prefix": "PREFIJO", "protect": "PROTEGER", "row": "FILA", "type": "TIPO", "width": "ANCHO"}}}