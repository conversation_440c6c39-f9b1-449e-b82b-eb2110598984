{"DATE": {"a": "(year; month; day)", "d": "<PERSON><PERSON><PERSON> về số thể hiện ngày tháng theo mã ngày-giờ", "ad": "là một số từ 1900 hoặc 1904 (tù<PERSON> và<PERSON> hệ thống ngày tháng của sổ làm việc) đến 9999 trong!là một số từ 1 đến 12 thể hiện tháng trong năm!là một số từ 1 đến 31 thể hiện ngày trong tháng"}, "DATEDIF": {"a": "(start-date; end-date; unit)", "d": "<PERSON><PERSON><PERSON> to<PERSON> s<PERSON> ng<PERSON>, tháng hoặc năm giữa hai ngày", "ad": "Ngày đại diện cho ngày đầu tiên hoặc ngày bắt đầu của một khoảng thời gian đã cho!Ngày đại diện cho ngày cuối cùng hoặc ngày kết thúc khoảng thời gian!Kiểu thông tin mà bạn muốn được trả về"}, "DATEVALUE": {"a": "(date_text)", "d": "Chuyển đổi ngày tháng dưới dạng văn bản sang số thể hiện ngày tháng theo mã ngày-giờ", "ad": "là văn thể hiện ngày tháng theo định dạng ngày tháng Spreadsheet Editor, giữa  1/1/1900 hoặc 1/1/1904 (tùy vào hệ thống ngày tháng của sổ làm việc) và 12/31/9999"}, "DAY": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON> về ngà<PERSON> trong tháng, một số từ 1 đến 31.", "ad": "là một số trong mã ngày-giờ được sử dụng bởi Spreadsheet Editor"}, "DAYS": {"a": "(end_date; start_date)", "d": "Trả về số lượng ngày giữa hai ngày", "ad": "Bạn muốn biết có bao nhiêu ngày ở giữa start_date và end_date!Bạn muốn biết có bao nhiêu ngày ở giữa start_date và end_date"}, "DAYS360": {"a": "(start_date; end_date; [method])", "d": "<PERSON><PERSON><PERSON> về số ngày giữa hai ngày trên cơ sở năm 360-ngày (12 tháng 30-ngày)", "ad": "ngày_bắt_đầu và ngày_kết_thúc là hai ngày mà bạn muốn biết số ngày có trong khoảng thời gian đó!ngày_bắt_đầu và ngày_kết_thúc là hai ngày mà bạn muốn biết số ngày có trong khoảng thời gian đó!là giá trị lô-gic chỉ định phương pháp tính toán: U.S. (NASD) = SAI hoặc không có; European = ĐÚNG."}, "EDATE": {"a": "(start_date; months)", "d": "Tr<PERSON> lại số tuần tự của ngày tháng là số chỉ báo các tháng trước hay sau ngày bắt đầu", "ad": "là số tuần tự ngày tháng thể hiện ngày bắt đầu!là số tháng trước hay sau start_date"}, "EOMONTH": {"a": "(start_date; months)", "d": "Tr<PERSON> lại số tuần tự của ngày cuối cùng của tháng trước hay sau số tháng chỉ định", "ad": "là số tuần tự ngày tháng thể hiện ngày bắt đầu!là số tháng trước hay sau start_date"}, "HOUR": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON> về giờ dưới dạng số từ 0 (12:00 SA) đến 23 (11:00 CH).", "ad": "là một số theo mã ngày-giờ được sử dụng bởi Spreadsheet Editor hoặc văn bản ở định dạng thời gian, n<PERSON><PERSON> 16:48:00 hoặc 4:48:00 CH"}, "ISOWEEKNUM": {"a": "(date)", "d": "Trả về số tuần ISO trong năm đối với ngày cho trước", "ad": "là mã ngày-giờ sử dụng bởi Spreadsheet Editor <PERSON><PERSON> t<PERSON>h toán ngày và giờ"}, "MINUTE": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON> v<PERSON>, m<PERSON><PERSON> số từ 0 đế<PERSON> 59.", "ad": "là một số theo mã ngày-giờ được sử dụng bởi Spreadsheet Editor hoặc văn bản ở định dạng thời gian, n<PERSON><PERSON> 16:48:00 hoặc 4:48:00 CH"}, "MONTH": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON> về tháng, m<PERSON>t số từ 1 (<PERSON><PERSON><PERSON>) tới 12 (<PERSON><PERSON><PERSON><PERSON>).", "ad": "là một số trong mã ngày-tháng được sử dụng bởi Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(start_date; end_date; [holidays])", "d": "<PERSON><PERSON><PERSON> lại số ngày làm việc đầy đủ giữa hai mốc ngày", "ad": "là số ngày tuần tự thể hiện ngày bắt đầu!là số ngày tuần tự thể hiện ngày kết thúc!là tập tùy chọn của một hay nhiều số ngày tuần tự để loại khỏi lịch ngày làm việc như ngày lễ của tỉnh hay quốc gia và các ngày nghỉ lễ bù"}, "NETWORKDAYS.INTL": {"a": "(start_date; end_date; [weekend]; [holidays])", "d": "Trả về số ngày làm việc cả ngày giữa hai ngày với tham số ngày cuối tuần tùy chỉnh", "ad": "là số ngày tuần tự thể hiện ngày bắt đầu!là số ngày tuần tự thể hiện ngày kết thúc!là số hoặc chuỗi chỉ định thời điểm diễn ra ngày cuối tuần!là tập hợp tùy chọn một hoặc nhiều số ngày tuần tự để loại trừ khỏi lịch làm việc, như ngày lễ tiểu bang và liên bang và ngày nghỉ lễ bù"}, "NOW": {"a": "()", "d": "<PERSON><PERSON><PERSON> về ngày tháng hiện tại theo dạng thức ngày tháng và thời gian.", "ad": ""}, "SECOND": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON> về <PERSON>, m<PERSON><PERSON> số từ 0 đế<PERSON> 59.", "ad": "là một số theo mã ngày-giờ được sử dụng bởi Spreadsheet Editor hoặc văn bản ở định dạng thời gian, <PERSON><PERSON><PERSON> 16:48:23 hoặc 4:48:47 CH"}, "TIME": {"a": "(hour; minute; second)", "d": "Chuyển đổi giờ, <PERSON><PERSON><PERSON><PERSON>, gi<PERSON><PERSON> đã cho sang số tuần tự, đ<PERSON><PERSON><PERSON> định dạng theo dạng thức thời gian", "ad": "là một số từ 0 đến 23 để biểu diễn giờ!là một số từ 0 đến 59 để biểu diễn phút!là một số từ 0 đến 59 để biểu diễn giây"}, "TIMEVALUE": {"a": "(time_text)", "d": "Chuyển đổi thời gian vă<PERSON> b<PERSON><PERSON> sang số tuần tự cho thời gian, một số từ 0 (12:00:00 SA) thành 0.999988426 (11:59:59 CH). <PERSON><PERSON><PERSON> dạng số bằng định dạng thời gian sau khi nhập công thức", "ad": "là chuỗi văn bản chỉ thời gian theo bất kỳ định dạng thời gian nào trong Spreadsheet Editor (bỏ qua thông tin ngày tháng trong chuỗi)"}, "TODAY": {"a": "()", "d": "<PERSON><PERSON><PERSON> về ngày hiện thời theo dạng thức ngày tháng.", "ad": ""}, "WEEKDAY": {"a": "(serial_number; [return_type])", "d": "<PERSON><PERSON><PERSON> về một số từ 1 đến 7 thể hiện ngày trong tuần.", "ad": "là một số biểu diễn ngày tháng!là một số: dùng 1, nếu <PERSON> nhật=1 tới thứ <PERSON>=7; dùng 2, nế<PERSON> thứ <PERSON>=1 tới <PERSON>ủ nhật=7; dùng 3, nế<PERSON>ứ <PERSON>=0 tới Chủ nhật=6"}, "WEEKNUM": {"a": "(serial_number; [return_type])", "d": "<PERSON><PERSON><PERSON> lại số tuần trong năm", "ad": "là mã ngày-gi<PERSON> được sử dụng bởi Spreadsheet Editor đ<PERSON> tính toán ngày giờ!là số (1 hoặc 2) xác định loại giá trị trả lại"}, "WORKDAY": {"a": "(start_date; days; [holidays])", "d": "Trả lại số tuần tự của ngày trước hay sau một số ngày làm việc được chỉ ra", "ad": "là số ngày tuần tự thể hiện ngày bắt đầu!là số ngày không phải cuối tuần hay ngày lễ trước hay sau start_date!là mảng tùy chọn của một hay nhiều số ngày tuần tự để loại khỏi lịch ngày làm việc như ngày lễ của tỉnh hay quốc gia và các ngày nghỉ lễ bù"}, "WORKDAY.INTL": {"a": "(start_date; days; [weekend]; [holidays])", "d": "Tr<PERSON> về số ngày tuần tự trước hoặc sau số ngày làm việc được chỉ định với tham số ngày cuối tuần tùy chỉnh", "ad": "là số ngày tuần tự thể hiện ngày bắt đầu!là số ngày không phải cuối tuần và ngày nghỉ trước hoặc sau start_date!là số hoặc chuỗi chỉ định thời điểm diễn ra ngày cuối tuần!là một mảng tùy chọn một hoặc nhiều số ngày tuần tự để loại trừ khỏi lịch làm việc, như ngày lễ tiểu bang và liên bang và ngày nghỉ lễ bù"}, "YEAR": {"a": "(serial_number)", "d": "<PERSON><PERSON><PERSON> về năm củ<PERSON>, một số nguyên trong k<PERSON> 1900 - 9999.", "ad": "là một số trong mã ngày-giờ được sử dụng bởi Spreadsheet Editor"}, "YEARFRAC": {"a": "(start_date; end_date; [basis])", "d": "<PERSON><PERSON><PERSON> lại phân số năm thể hiện số ngày nguyên giữa start_date và end_date", "ad": "là số tuần tự ngày tháng thể hiện ngày bắt đầu!là số tuần tự ngày tháng thể hiện ngày kết thúc!là loại cơ sở tính ngày được dùng"}, "BESSELI": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON> về hàm <PERSON> biến đổi In(x)", "ad": "là giá trị cần tính giá trị của hàm!là thứ tự hàm Bessel"}, "BESSELJ": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON> v<PERSON> hàm <PERSON>(x)", "ad": "là giá trị cần tính giá trị của hàm!ilà thứ tự hàm Bessel"}, "BESSELK": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON> về hàm <PERSON> biến đổi Kn(x)", "ad": "là giá trị cần tính giá trị của hàm!là thứ tự hàm Bessel"}, "BESSELY": {"a": "(x; n)", "d": "<PERSON><PERSON><PERSON> v<PERSON> hàm <PERSON>(x)", "ad": "là giá trị cần tính giá trị của hàm!là thứ tự hàm"}, "BIN2DEC": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON> số nhị phân thành thập phân", "ad": "là số nhị phân mà bạn muốn chuyển"}, "BIN2HEX": {"a": "(number; [places])", "d": "<PERSON><PERSON><PERSON><PERSON> số nhị phân thành thập lục phân", "ad": "là số nhị phân mà bạn muốn chuyển!là số ký tự sử dụng"}, "BIN2OCT": {"a": "(number; [places])", "d": "Chuyển số nhị phân thành bát phân", "ad": "là số nhị phân mà bạn muốn chuyển!là số ký tự sử dụng"}, "BITAND": {"a": "(number1; number2)", "d": "Trả về \"And\" theo bit của hai số", "ad": "là biểu diễn theo hệ thập phân của hai số nhị phân bạn muốn định trị!là biểu diễn theo hệ thập phân của số nhị phân bạn muốn định trị"}, "BITLSHIFT": {"a": "(number; shift_amount)", "d": "Trả về một số được dịch trái bởi các bit shift_amount", "ad": "là biểu thị theo hệ thập phân của số nhị phân bạn muốn tính giá trị!là số lượng bit mà bạn muốn dịch Số sang trái"}, "BITOR": {"a": "(number1; number2)", "d": "Trả về \"Or\" theo bit của hai số", "ad": "là biểu diễn theo hệ thập phân của hai số nhị phân bạn muốn định trị!là biểu diễn theo hệ thập phân của số nhị phân bạn muốn định trị"}, "BITRSHIFT": {"a": "(number; shift_amount)", "d": "Trả về một số được dịch phải bởi các bit shift_amount ", "ad": "là biểu thị theo hệ thập phân của số nhị phân bạn muốn tính giá trị!là số lượng bit mà bạn muốn dịch Số sang phải"}, "BITXOR": {"a": "(number1; number2)", "d": "Trả về \"Exclusive Or\" theo bit của hai số", "ad": "là biểu diễn theo hệ thập phân của hai số nhị phân bạn muốn định trị!là biểu diễn theo hệ thập phân của số nhị phân bạn muốn định trị"}, "COMPLEX": {"a": "(real_num; i_num; [suffix])", "d": "<PERSON>y<PERSON><PERSON> đổi hệ số thực và ảo thành số phức", "ad": "là hệ số thực của số phức!là hệ số ảocủa số phức!là phần tiếp sau phần ảo của số phức"}, "CONVERT": {"a": "(number; from_unit; to_unit)", "d": "Chuyển đổi một số từ hệ đo này sang hệ khác", "ad": "là giá trị trong from_units cần chuyển đổi!là đơn vị cho số!là đơn vị cho kết quả"}, "DEC2BIN": {"a": "(number; [places])", "d": "<PERSON><PERSON><PERSON><PERSON> số thập phân thành nhị phân", "ad": "là số nguyên thập phân mà bạn muốn chuyển!là số ký tự sử dụng"}, "DEC2HEX": {"a": "(number; [places])", "d": "<PERSON><PERSON><PERSON><PERSON> số thập phân thành thập lục phân", "ad": "là số nguyên thập phân mà bạn muốn chuyển!là số ký tự sử dụng"}, "DEC2OCT": {"a": "(number; [places])", "d": "<PERSON>y<PERSON><PERSON> số thập phân thành bát phân", "ad": "là số nguyên thập phân mà bạn muốn chuyển!là số ký tự sử dụng"}, "DELTA": {"a": "(number1; [number2])", "d": "<PERSON><PERSON>m tra hai số có bằng nhau không", "ad": "là số thứ nhất!là số thứ hai"}, "ERF": {"a": "(lower_limit; [upper_limit])", "d": "<PERSON><PERSON><PERSON> về hàm lỗi", "ad": "là biên dư<PERSON>i của tích hợp ERF!là biên trên của tích hợp ERF"}, "ERF.PRECISE": {"a": "(X)", "d": "Trả về hàm lỗi", "ad": "là cận dưới cho tích phân ERF.PRECISE"}, "ERFC": {"a": "(x)", "d": "<PERSON><PERSON><PERSON> về hàm lỗi bổ sung", "ad": "là biên d<PERSON><PERSON><PERSON> của tích hợp ERF"}, "ERFC.PRECISE": {"a": "(X)", "d": "Trả về hàm lỗi bù", "ad": "là cận dưới cho tích phân ERFC.PRECISE"}, "GESTEP": {"a": "(number; [step])", "d": "<PERSON><PERSON><PERSON> tra số có lớn hơn giá trị ngưỡng không", "ad": "là giá trị cần kiểm tra lại theo bước!là giá trị ngưỡng"}, "HEX2BIN": {"a": "(number; [places])", "d": "<PERSON><PERSON><PERSON><PERSON> số thập lục phân thành nhị phân", "ad": "là số thập lục phân mà bạn muốn chuyển!là số ký tự sử dụng"}, "HEX2DEC": {"a": "(number)", "d": "<PERSON><PERSON><PERSON><PERSON> số thập lục phân thành thập phân", "ad": "là số thập lục phân mà bạn muốn chuyển"}, "HEX2OCT": {"a": "(number; [places])", "d": "<PERSON><PERSON><PERSON><PERSON> số thập lục phân thành bát phân", "ad": "là số thập lục phân mà bạn muốn chuyển!là số ký tự sử dụng"}, "IMABS": {"a": "(inumber)", "d": "Trả về giá trị tuyệt đối của số phức", "ad": "là số phức cần lấy giá trị tuyệt đối"}, "IMAGINARY": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON> về hệ số ảo của số phức", "ad": "là số ph<PERSON>c cần l<PERSON><PERSON> hệ số ảo"}, "IMARGUMENT": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON> về tham đối q, g<PERSON><PERSON> theo r<PERSON>ian", "ad": "là số ph<PERSON><PERSON> cần lấy tham đối "}, "IMCONJUGATE": {"a": "(inumber)", "d": "<PERSON><PERSON>ả về số liên hợp của số phức", "ad": "là số ph<PERSON>c cần lấy số liên hợp"}, "IMCOS": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON> về c<PERSON>sin của số phức", "ad": "l<PERSON> số ph<PERSON><PERSON> c<PERSON>n l<PERSON><PERSON> c<PERSON>sin"}, "IMCOSH": {"a": "(inumber)", "d": "Trả về hàm cos hyperbol của số phức", "ad": "là số phức bạn muốn tìm cos hyperbol"}, "IMCOT": {"a": "(number)", "d": "Trả về hàm cotang của một số phức", "ad": "là số phức mà bạn muốn tìm cotang"}, "IMCSC": {"a": "(number)", "d": "Trả về hàm cosec của một số phức", "ad": "là số phức mà bạn muốn tìm cosec"}, "IMCSCH": {"a": "(number)", "d": "Trả về hàm cosec hyperbol của một số phức", "ad": "là số phức mà bạn muốn tìm cosec hyperbol"}, "IMDIV": {"a": "(inumber1; inumber2)", "d": "<PERSON><PERSON><PERSON> về thương số của hai số phức", "ad": "là tử số phức hay số bị chia!là mẫu số phức hay số bị chia"}, "IMEXP": {"a": "(inumber)", "d": "<PERSON><PERSON>ả về số mũ của số phức", "ad": "là số ph<PERSON>c cần lấy số mũ"}, "IMLN": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON> về lô-ga-rít tự nhiên của số phức", "ad": "là số phức cần lấy lô-ga-rít tự nhiên"}, "IMLOG10": {"a": "(inumber)", "d": "<PERSON><PERSON>ả về lô-ga-r<PERSON><PERSON> c<PERSON> số 10 của số phức", "ad": "là số ph<PERSON><PERSON> c<PERSON>n l<PERSON>y lô-ga-r<PERSON><PERSON> c<PERSON> số 10"}, "IMLOG2": {"a": "(inumber)", "d": "<PERSON><PERSON>ả về lô-ga-r<PERSON><PERSON> c<PERSON> số 2 của số phức", "ad": "là số ph<PERSON><PERSON> c<PERSON>n l<PERSON>y lô-ga-r<PERSON><PERSON> c<PERSON> số 2"}, "IMPOWER": {"a": "(inumber; number)", "d": "<PERSON><PERSON><PERSON> về số phức lũy thừa nguyên", "ad": "là số phức muốn tính lũy thừa!là lũy thừa của số phức"}, "IMPRODUCT": {"a": "(inumber1; [inumber2]; ...)", "d": "<PERSON><PERSON><PERSON> về tích của 1 đến 255 số phức", "ad": "Inumber1, Inumber2,... từ 1 đến 255 số <PERSON>o cần nhân."}, "IMREAL": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON> về hệ số thực của số phức", "ad": "là số phức cần l<PERSON>y hệ số thực"}, "IMSEC": {"a": "(number)", "d": "Trả về hàm sec của một số phức", "ad": "là số phức mà bạn muốn tìm sec"}, "IMSECH": {"a": "(number)", "d": "Trả về hàm sec hyperbol của một số phức", "ad": "là số phức mà bạn muốn tìm sec hyperbol"}, "IMSIN": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON> về sin của số phức", "ad": "l<PERSON> số ph<PERSON><PERSON> cần l<PERSON>y sin"}, "IMSINH": {"a": "(inumber)", "d": "Trả về hàm sin hyperbol của số phức", "ad": "là số phức bạn muốn tìm sim hyperbol"}, "IMSQRT": {"a": "(inumber)", "d": "<PERSON><PERSON><PERSON> về căn bậc hai của số phức", "ad": "là số ph<PERSON><PERSON> cần l<PERSON>y căn bậc hai"}, "IMSUB": {"a": "(inumber1; inumber2)", "d": "<PERSON><PERSON><PERSON> về độ chênh lệch giữa hai số phức", "ad": "là số phức để trừ đi inumber2!ilà số phức để trừ từ inumber1"}, "IMSUM": {"a": "(inumber1; [inumber2]; ...)", "d": "<PERSON><PERSON>ả về tổng của các số phức", "ad": "từ 1 đến 255 số <PERSON><PERSON> cần cộng"}, "IMTAN": {"a": "(number)", "d": "Trả về hàm tang của một số phức", "ad": "là số phức mà bạn muốn tìm tang"}, "OCT2BIN": {"a": "(number; [places])", "d": "Chuyển số bát phân thành nhị phân", "ad": "là số bát phân mà bạn muốn chuyển!là số ký tự sử dụng"}, "OCT2DEC": {"a": "(number)", "d": "Chuyển số bát phân thành thập phân", "ad": "là số bát phân mà bạn muốn chuyển"}, "OCT2HEX": {"a": "(number; [places])", "d": "Chuyển số bát phân thành thập lục phân", "ad": "là số bát phân mà bạn muốn chuyển!là số ký tự sử dụng"}, "DAVERAGE": {"a": "(database; field; criteria)", "d": "Trung bình giá trị của cột trong danh sách hoặc trong cơ sở dữ liệu thoả mãn điều kiện chỉ định", "ad": "là vùng ô tạo thành danh sách hoặc cơ sở dữ liệu. Cơ sở dữ liệu là danh sách các dữ liệu có liên quan!Hoặc là nhãn của cột trong cặp dấu nháy kép hoặc là số thứ tự của cột trong danh sách!là vùng ô chứa điều kiện chỉ định. Vùng này gồm nhãn cột và một ô bên dưới chỉ điều kiện"}, "DCOUNT": {"a": "(database; field; criteria)", "d": "<PERSON><PERSON><PERSON> số ô chứa giá trị số trong trường (cột) của các bản ghi thuộc cơ sở dữ liệu thoả mãn các điều kiện chỉ định", "ad": "là vùng ô tạo thành danh sách hoặc cơ sở dữ liệu. Cơ sở dữ liệu là danh sách các dữ liệu có liên quan!Hoặc là nhãn của cột trong cặp dấu nháy kép hoặc là số thứ tự của cột trong danh sách!là vùng ô chứa điều kiện chỉ định. Vùng này gồm nhãn cột và một ô bên dưới chỉ điều kiện"}, "DCOUNTA": {"a": "(database; field; criteria)", "d": "<PERSON><PERSON><PERSON> số ô không trắng trong trường (cột) của các bản ghi trong cơ sở dữ liệu trùng khớp với điều kiện chỉ định", "ad": "là một khoảng các ô tạo thành danh sách hoặc cơ sở dữ liệu. Cơ sở dữ liệu là một danh sách dữ liệu có liên quan!hoặc là nhãn cột trong cặp dấu nháy kép hoặc là số thể hiện thứ tự cột trong danh sách!là khoảng các ô chứa điều kiện chỉ định. Khoảng bao gồm nhãn cột và môt ô bên dưới chỉ điều kiện"}, "DGET": {"a": "(database; field; criteria)", "d": "<PERSON><PERSON><PERSON><PERSON> xuất từ cơ sở dữ liệu một bản ghi trùng khớp với các điều kiện chỉ định", "ad": "là vùng ô tạo thành danh sách hoặc cơ sở dữ liệu. Cơ sở dữ liệu là danh sách các dữ liệu có liên quan!Hoặc là nhãn của cột trong cặp dấu nháy kép hoặc là số thứ tự của cột trong danh sách!là khoảng các ô chứa điều kiện chỉ định. Khoảng bao gồm nhãn cột và môt ô bên dưới nhãn chỉ điều kiện"}, "DMAX": {"a": "(database; field; criteria)", "d": "Tr<PERSON> về giá trị lớn nhất trong trườ<PERSON> (cột) của các bản ghi thuộc cơ sở dữ liệu thoả mãn điều kiện chỉ định", "ad": "là vùng ô tạo thành danh sách hoặc cơ sở dữ liệu. Cơ sở dữ liệu là danh sách các dữ liệu có liên quan!Hoặc là nhãn của cột trong cặp dấu nháy kép hoặc là số thứ tự của cột trong danh sách!là vùng ô chứa điều kiện chỉ định. Vùng này gồm nhãn cột và một ô bên dưới chỉ điều kiện"}, "DMIN": {"a": "(database; field; criteria)", "d": "Tr<PERSON> về giá trị nhỏ nhất trong trường (cột) của các bản ghi thuộc cơ sở dữ liệu thoả mãn điều kiện chỉ định", "ad": "là vùng ô tạo thành danh sách hoặc cơ sở dữ liệu. Cơ sở dữ liệu là danh sách các dữ liệu có liên quan!Hoặc là nhãn của cột trong cặp dấu nháy kép hoặc là số thứ tự của cột trong danh sách!là vùng ô chứa điều kiện chỉ định. Vùng này gồm nhãn cột và một ô bên dưới chỉ điều kiện"}, "DPRODUCT": {"a": "(database; field; criteria)", "d": "Nhân giá trị trong trường (cột) của các bản ghi trong cơ sở dữ liệu trùng khớp với điều kiện chỉ định", "ad": "là một khoảng các ô tạo thành danh sách hoặc cơ sở dữ liệu. Cơ sở dữ liệu là một danh sách dữ liệu có liên quan!hoặc là nhãn cột trong cặp dấu nháy kép hoặc là số thể hiện thứ tự cột trong danh sách!là khoảng các ô chứa điều kiện chỉ định. Khoảng bao gồm nhãn cột và môt ô bên dưới chỉ điều kiện"}, "DSTDEV": {"a": "(database; field; criteria)", "d": "Ước lượng độ lệch tiêu chuẩn dựa trên mẫu từ các mục trong cơ sở dữ liệu", "ad": "là vùng ô tạo thành danh sách hoặc cơ sở dữ liệu. Cơ sở dữ liệu là danh sách các dữ liệu có liên quan!Hoặc là nhãn của cột trong cặp dấu nháy kép hoặc là số thứ tự của cột trong danh sách!là vùng ô chứa điều kiện chỉ định. Vùng này gồm nhãn cột và một ô bên dưới chỉ điều kiện"}, "DSTDEVP": {"a": "(database; field; criteria)", "d": "<PERSON><PERSON><PERSON> toán độ lệch tiêu chuẩn dựa trên tập toàn bộ của các mục trong cơ sở dữ liệu đã chọn", "ad": "là một khoảng các ô tạo thành danh sách hoặc cơ sở dữ liệu. Cơ sở dữ liệu là một danh sách dữ liệu có liên quan!hoặc là nhãn cột trong cặp dấu nháy kép hoặc là số thể hiện thứ tự cột trong danh sách!là khoảng các ô chứa điều kiện chỉ định. Khoảng bao gồm nhãn cột và môt ô bên dưới chỉ điều kiện"}, "DSUM": {"a": "(database; field; criteria)", "d": "<PERSON>hê<PERSON> số vào trườ<PERSON> (cột) của các bản ghi thuộc cơ sở dữ liệu thoả mãn điều kiện chỉ định", "ad": "là vùng ô tạo thành danh sách hoặc cơ sở dữ liệu. Cơ sở dữ liệu là danh sách các dữ liệu có liên quan!Hoặc là nhãn của cột trong cặp dấu nháy kép hoặc là số thứ tự của cột trong danh sách!là vùng ô chứa điều kiện chỉ định. Vùng này gồm nhãn cột và một ô bên dưới chỉ điều kiện"}, "DVAR": {"a": "(database; field; criteria)", "d": "Ước lượng phương sai dựa trên mẫu từ các mục của cơ sở dữ liệu lựa chọn", "ad": "là vùng ô tạo thành danh sách hoặc cơ sở dữ liệu. Cơ sở dữ liệu là danh sách các dữ liệu có liên quan!Hoặc là nhãn của cột trong cặp dấu nháy kép hoặc là số thứ tự của cột trong danh sách!là vùng ô chứa điều kiện chỉ định. Vùng này gồm nhãn cột và một ô bên dưới chỉ điều kiện"}, "DVARP": {"a": "(database; field; criteria)", "d": "<PERSON><PERSON>h toán dung sai dựa trên tập toàn bộ của các mục trong cơ sở dữ liệu đã chọn", "ad": "là một khoảng các ô tạo thành danh sách hoặc cơ sở dữ liệu. Cơ sở dữ liệu là một danh sách dữ liệu có liên quan!hoặc là nhãn cột trong cặp dấu nháy kép hoặc là số thể hiện thứ tự cột trong danh sách!là khoảng các ô chứa điều kiện chỉ định. Khoảng bao gồm nhãn cột và môt ô bên dưới chỉ điều kiện"}, "CHAR": {"a": "(number)", "d": "Trả về ký tự xác định bởi số hiệu mã từ tập ký tự trên máy tính", "ad": "là số từ 1 đến 255 xác định ký tự mong muốn"}, "CLEAN": {"a": "(text)", "d": "Loại bỏ mọi ký tự không in trong văn bản", "ad": "là bất kỳ trong tin trang tính cần loại bỏ các ký tự không in"}, "CODE": {"a": "(text)", "d": "Tr<PERSON> về mã số của ký tự đầu tiên trong văn bản, theo tập ký tự sử dụng trong máy", "ad": "là văn bản cần mã của ký tự đầu tiên"}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "<PERSON><PERSON><PERSON> kết vài xâu văn bản vào một xâu văn bản", "ad": "từ 1 đến 255 xâu văn bản đư<PERSON><PERSON> nối vào một xâu văn bản và có thể là xâu văn bản, s<PERSON>, hoặc tham chiếu ô đơn"}, "CONCAT": {"a": "(text1; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> danh sách hoặc phạm vi các chuỗi văn bản", "ad": "là chuỗi hoặc phạm vi văn bản từ 1 đến 254 để nối thành chuỗi văn bản đơn"}, "DOLLAR": {"a": "(number; [decimals])", "d": "Chuyển đổi số sang văn bản, sử dụng định dạng tiền tệ", "ad": "là một số, tham chiếu tới ô chứa số, hoặc một công thức tính giá trị là số!là số chữ số bên phải dấu thập phân. Số được làm tròn nếu cần; nếu không có, phần thập phân = 2"}, "EXACT": {"a": "(text1; text2)", "d": "Ki<PERSON>m tra hai xâu văn bản có chính xác giố<PERSON>u, và trả về ĐÚNG hoặc SAI. Hàm EXACT có phân biệt chữ hoa, chữ thường", "ad": "Là xâu văn bản đầu tiên!Là xâu văn bản thứ hai"}, "FIND": {"a": "(find_text; within_text; [start_num])", "d": "Trả về vị trí bắt đầu của một xâu văn bản nằm trong xâu văn bản khác. Hàm FIND có phân biệt chữ hoa, chữ thường", "ad": "là văn bản cần tìm. Sử dụng cặp dấu nh<PERSON><PERSON> k<PERSON> (văn bản rỗng) để so khớp ký tự đầu tiên trong Văn_bản_chứa; không cho phép ký tự đại diện!là văn bản chứa văn bản cần tìm!Chỉ định ký tự từ đó bắt đầu tìm. Ký tự đầu tiên trong Văn_bản_chứa có số hiệu là 1. Nếu không có, Số_bắt_đầu = 1"}, "FINDB": {"a": "(find_text; within_text; [start_num])", "d": "Định vị một chuỗi văn bản nằm trong chuỗi văn bản thứ hai và trả về số của vị trí bắt đầu của chuỗi văn bản thứ nhất tính từ ký tự thứ nhất của chuỗi văn bản thứ hai, nhằm để dùng với những ngôn ngữ sử dụng bộ ký tự byte kép (DBCS) - Tiếng Nhật, Tiếng Trung Quốc và Tiếng Hàn Quốc", "ad": "là văn bản cần tìm. Sử dụng cặp dấu nh<PERSON><PERSON> k<PERSON> (văn bản rỗng) để so khớp ký tự đầu tiên trong Văn_bản_chứa; không cho phép ký tự đại diện!là văn bản chứa văn bản cần tìm!Chỉ định ký tự từ đó bắt đầu tìm. Ký tự đầu tiên trong Văn_bản_chứa có số hiệu là 1. Nếu không có, Số_bắt_đầu = 1"}, "FIXED": {"a": "(number; [decimals]; [no_commas])", "d": "<PERSON><PERSON><PERSON> tròn số theo phần thập phân chỉ định và trả về kết quả dạng văn bản có dấu hàng đơn vị hoặc không", "ad": "là số cần làm tròn và chuyển đổi sang dạng văn bản!là số chữ số bên phải dấu thập phân. Nếu không có, phần thập phân = 2!là giá trị lô-gic:  = ĐÚNG sẽ không hiển thị dấu ZZ; = FALSE hoặc không có sẽ hiển thị dấu ZZ trong văn bản trả về"}, "LEFT": {"a": "(text; [num_chars])", "d": "Tr<PERSON> về số ký tự xác định từ vị trí bắt đầu của xâu văn bản", "ad": "là xâu văn bản chứa các ký tự cần trích xuất!Xác định số ký tự hàm LEFT cần trích xuất; là 1 nếu không có"}, "LEFTB": {"a": "(text; [num_chars])", "d": "Trả về một hoặc nhiều ký tự đầu tiên trong một chuỗi, dựa vào số byte mà bạn chỉ định, nhằm để dùng với những ngôn ngữ sử dụng bộ ký tự byte kép (DBCS) - Tiếng Nhật, Tiếng Trung Quốc và Tiếng Hàn Quốc", "ad": "là xâu văn bản chứa các ký tự cần trích xuất!Xác định số ký tự hàm LEFT cần trích xuất; là 1 nếu không có"}, "LEN": {"a": "(text)", "d": "Trả về số lượng ký tự trong xâu văn bản", "ad": "là phần văn bản cần tìm độ dài. <PERSON><PERSON><PERSON> cách đư<PERSON> coi như ký tự"}, "LENB": {"a": "(text)", "d": "Trả về số byte dùng để biểu thị các ký tự trong một chuỗi văn bản, nhằm để dùng với những ngôn ngữ sử dụng bộ ký tự byte kép (DBCS) - Tiếng Nhật, Tiếng Trung Quốc và Tiếng Hàn Quốc", "ad": "là phần văn bản cần tìm độ dài. <PERSON><PERSON><PERSON> cách đư<PERSON> coi như ký tự"}, "LOWER": {"a": "(text)", "d": "Chuyển đổi mọi chữ cái trong xâu văn bản sang chữ thường", "ad": "là văn bản cần chuyển đổi sang chữ thường. <PERSON><PERSON><PERSON> ký tự trong Văn bản không phải là chữ cái sẽ không đổi"}, "MID": {"a": "(text; start_num; num_chars)", "d": "Tr<PERSON> về các ký tự ở giữa xâu văn bản, với vị trí bắt đầu và độ dài chỉ định", "ad": "là xâu văn bản cần trích xuất các ký tự!là vị trí ký tự đầu tiên cần trích xuất. Vị trí ký tự đầu tiên trong Văn bản là 1!<PERSON><PERSON><PERSON> định số lượng ký tự được trả về từ Văn bản"}, "MIDB": {"a": "(text; start_num; num_chars)", "d": "Trả về một số lượng ký tự cụ thể từ một chuỗi văn bản, bắt đầu từ vị trí do bạn chỉ định, dựa vào số lượng byte do bạn chỉ định, nhằm để dùng với những ngôn ngữ sử dụng bộ ký tự byte kép (DBCS) - Tiếng Nhật, Tiếng Trung Quốc và Tiếng Hàn Quốc", "ad": "là xâu văn bản cần trích xuất các ký tự!là vị trí ký tự đầu tiên cần trích xuất. Vị trí ký tự đầu tiên trong Văn bản là 1!<PERSON><PERSON><PERSON> định số lượng ký tự được trả về từ Văn bản"}, "NUMBERVALUE": {"a": "(text; [decimal_separator]; [group_separator])", "d": "Chuyển đổi văn bản sang con số độc lập miền địa phương", "ad": "là xâu thể hiện con số bạn muốn chuyển đổi!là ký tự sử dụng như là dấu phân cách thập phân trong xâu!là ký tự sử dụng như là dấu phân cách nhóm trong xâu"}, "PROPER": {"a": "(text)", "d": "Chuyển đổi chuỗi văn bản sang dạng chữ thích hợp; chữ cái đầu tiên của từ là chữ hoa, mọi chữ cái khác là chữ thường", "ad": "là văn bản bao trong dấu ngoặc kép, công thức trả về văn bản hoặc tham chiếu tới ô chứa văn bản để viết hoa một phần"}, "REPLACE": {"a": "(old_text; start_num; num_chars; new_text)", "d": "Thay thế một phần của xâu văn bản bằng xâu văn bản khác", "ad": "là văn bản cần thay thế một số ký tự!là vị trí của ký tự trong Văn_bản_cũ cần thay thế bằng Văn_bản_mới!là số ký tự trong Văn_bản_cũ cần thay thế!là văn bản sẽ thay thế các ký tự trong Văn_bản_cũ"}, "REPLACEB": {"a": "(old_text; start_num; num_chars; new_text)", "d": "Thay thế một phần của chuỗi văn bản, dựa vào số byte do bạn chỉ định, bằng một chuỗi văn bản khác, nhằm để dùng với những ngôn ngữ sử dụng bộ ký tự byte kép (DBCS) - Tiếng Nhật, Tiếng Trung Quốc và Tiếng Hàn Quốc", "ad": "là văn bản cần thay thế một số ký tự!là vị trí của ký tự trong Văn_bản_cũ cần thay thế bằng Văn_bản_mới!là số ký tự trong Văn_bản_cũ cần thay thế!là văn bản sẽ thay thế các ký tự trong Văn_bản_cũ"}, "REPT": {"a": "(text; number_times)", "d": "Lặp lại văn bản theo số lần chỉ định. Sử dụng hàm REPT để điền xâu văn bản nhiều lần vào ô", "ad": "là văn bản cần lặp lại!là một số dương chỉ định số lần lặp lại văn bản"}, "RIGHT": {"a": "(text; [num_chars])", "d": "Trả về số ký tự xác định từ vị trí cuối của xâu văn bản", "ad": "là xâu văn bản chứa các ký tự cần trích xuất!Xác định số ký tự cần trích xuất; là 1 nếu không có"}, "RIGHTB": {"a": "(text; [num_chars])", "d": "Trả về một hoặc nhiều ký tự cuối cùng trong một chuỗi, dựa vào số byte mà bạn chỉ định, nhằm để dùng với những ngôn ngữ sử dụng bộ ký tự byte kép (DBCS) - Tiếng Nhật, Tiếng Trung Quốc và Tiếng Hàn Quốc", "ad": "là xâu văn bản chứa các ký tự cần trích xuất!Xác định số ký tự cần trích xuất; là 1 nếu không có"}, "SEARCH": {"a": "(find_text; within_text; [start_num])", "d": "Tr<PERSON> về số ký tự tại vị trí xuất hiện đầu tiên của ký tự hoặc xâu văn bản cho trướ<PERSON>, t<PERSON>h từ trái qua phải (không phân biệt chữ hoa, chữ thường)", "ad": "là văn bản cần tìm. <PERSON><PERSON> thể sử dụng ký tự đại diện ? và *; sử dụng ~? và ~* để tìm ký tự ? và *!là văn bản, trong đó cần tìm Văn_bản_tìm!là số thứ tự ký tự trong Văn_bản_ch<PERSON><PERSON>, t<PERSON><PERSON> từ tr<PERSON><PERSON> sang, cần bắt đầu tìm kiếm. N<PERSON><PERSON> không có, bắt đầu từ 1"}, "SEARCHB": {"a": "(find_text; within_text; [start_num])", "d": "Trả về một chuỗi văn bản bên trong chuỗi văn bản thứ hai và trả về số vị trí bắt đầu của chuỗi văn bản thứ nhất từ ký tự đầu tiên của chuỗi văn bản thứ hai, nhằm để dùng với những ngôn ngữ sử dụng bộ ký tự byte kép (DBCS) - Tiếng Nhật, Tiếng Trung Quốc và Tiếng Hàn Quốc", "ad": "là văn bản cần tìm. <PERSON><PERSON> thể sử dụng ký tự đại diện ? và *; sử dụng ~? và ~* để tìm ký tự ? và *!là văn bản, trong đó cần tìm Văn_bản_tìm!là số thứ tự ký tự trong Văn_bản_ch<PERSON><PERSON>, t<PERSON><PERSON> từ tr<PERSON><PERSON> sang, cần bắt đầu tìm kiếm. N<PERSON><PERSON> không có, bắt đầu từ 1"}, "SUBSTITUTE": {"a": "(text; old_text; new_text; [instance_num])", "d": "Thay thế văn bản hiện thời bằng văn bản mới trong xâu văn bản", "ad": "là văn bản hoặc tham chiếu tới ô chứa văn bản cần thay thế các ký tự!là văn bản hiện thời cần thay thế. Nếu dạng chữ của Văn_bản_cũ không đúng với dạng chữ của văn bản, hàm SUBSTITUTE sẽ không thay thế văn bản!là văn bản thay thế cho Văn_bản_cũ!xác định xuất hiện nào của Văn_bản_cũ sẽ được thay thế. <PERSON><PERSON><PERSON> không có, mọi lần xuất hiện của Văn_bản_cũ sẽ được thay thế"}, "T": {"a": "(value)", "d": "<PERSON><PERSON><PERSON> tra một giá trị có phải là văn bản, và trả về văn bản nếu đúng, hoặc cặp dấu nh<PERSON><PERSON> k<PERSON> (văn bản rỗng) nếu sai", "ad": "là giá trị cần thử"}, "TEXT": {"a": "(gi<PERSON>_trị; định_dạng_văn_bản)", "d": "Chuyển đổi một giá trị sang văn bản theo một định dạng số cụ thể", "ad": "là một số, một công thức đánh giá một giá trị số, hoặc một tham chiếu tới ô chứa một giá trị số!là một định dạng số ở dạng văn bản từ hộp Thể loại trên tap <PERSON><PERSON> trong hộp thoại Định dạng Ô"}, "TEXTJOIN": {"a": "(delimiter; ignore_empty; text1; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> danh sách hoặc phạm vi các chuỗi văn bản bằng dấu tách", "ad": "Ký tự hoặc chuỗi để chèn giữa mỗi mục văn bản!nếu TRUE(mặc định), bỏ qua các ô trống!là 1 đến 252 chuỗi văn bản hoặc các phạm vi được tham gia"}, "TRIM": {"a": "(text)", "d": "Loại bỏ mọi dấu cách trong xâu văn bản ngoại trừ dấu cách đơn gi<PERSON>a các từ", "ad": "là văn bản cần loại bỏ dấu cách"}, "UNICHAR": {"a": "(number)", "d": "Trả về ký tự Unicode được tham chiếu bởi giá trị số cho trước", "ad": "là con số Unicode thể hiện một ký tự"}, "UNICODE": {"a": "(text)", "d": "Trả về con số (điểm mã) tương ứng với ký tự đầu tiên của đoạn văn bản", "ad": "là ký tự bạn muốn tìm giá trị Unicode của nó"}, "UPPER": {"a": "(text)", "d": "Chuyển đổi xâu văn bản sang chữ hoa", "ad": "là văn bản cần đổi sang chữ hoa, một tham chiếu hoặc xâu văn bản"}, "VALUE": {"a": "(text)", "d": "Chuyển đổi xâu văn bản thể hiện số thành số", "ad": "là văn bản nằm trong dấu ngoặc kép hoặc một tham chiếu tới ô chứa văn bản cần chuyển đổi"}, "AVEDEV": {"a": "(number1; [number2]; ...)", "d": "Trả về trung bình độ lệch tuyệt đối giữa các điểm dữ liệu với giá trị trung bình của chúng. Tham đối có thể là số hoặc tên, m<PERSON><PERSON>, tham chiếu chứa số", "ad": "từ 1 đến 255 tham đối cần tính trung bình độ lệch tuyệt đối"}, "AVERAGE": {"a": "(number1; [number2]; ...)", "d": "Tr<PERSON> về giá trị trung bình (trung bình số học) của các tham đối, chún<PERSON> có thể là các số, t<PERSON><PERSON>, mảng hoặc các tham chiếu có chứa số", "ad": "từ 1 đến 255 tham đối dạng số bạn cần tính trung bình"}, "AVERAGEA": {"a": "(value1; [value2]; ...)", "d": "Tr<PERSON> về trung bình (trung bình số học) các tham đối của nó, văn bản và SAI được coi là 0; Đ<PERSON><PERSON> được coi là 1. Tham đối có thể là số, t<PERSON><PERSON>, m<PERSON><PERSON>, hoặc tham chiếu", "ad": "từ 1 đến 255 tham đối cần tính trung bình"}, "AVERAGEIF": {"a": "(range; criteria; [average_range])", "d": "Tìm trung bình(trung bình số học) cho ô được chỉ ra bởi điều kiện hay chỉ tiêu cho trước", "ad": "là phạm vi ô bạn muốn được đánh giá!là điều kiện hoặc tiêu chí ở dạng số, biểu thức hoặc văn bản xác định ô nào sẽ được sử dụng để tìm giá trị trung bình!là các ô thực tế sẽ được sử dụng để tìm giá trị trung bình. Nếu bị bỏ qua, các ô trong phạm vi sẽ được sử dụng"}, "AVERAGEIFS": {"a": "(average_range; criteria_range; criteria; ...)", "d": "Tìm trung bình(trung bình số học) cho các ô được xác định bởi tập cho trước các điều kiện hoặc tiêu chí", "ad": "là các ô thực sự sẽ được dùng để tìm trung bình!là dải ô cần tính giá trị theo điều kiện cụ thể!là điều kiện ở dạng số, bi<PERSON><PERSON> thức, hay văn bản xác định ô nào sẽ được dùng để tìm trung bình"}, "BETADIST": {"a": "(x; alpha; beta; [A]; [B])", "d": "<PERSON><PERSON><PERSON> về hàm mật độ xác suất bê-ta lũy tích", "ad": "là giá trị định trị của hàm trong khoảng giữa A và B!là một tham số dùng để phân bố và phải lớn hơn 0!là một tham số dùng để phân bố và phải lớn hơn 0!là giới hạn biến thiên dưới tùy chọn của x. Nếu không có, A = 0!là giới hạn biến thiên trên tùy chọn của x. Nếu không có, B = 1"}, "BETAINV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "Tr<PERSON> về giá trị đảo của hàm phân bố tích lũy bê-ta (BETADIST)", "ad": "là xác suất gắn với phân bố bêta!là một tham số cho phân bố và phải lớn hơn 0!là một tham số cho phân bố và phải lớn hơn 0!là giới hạn biến thiên dưới tùy chọn của x. Nếu không có, A = 0!là giới hạn biến thiên trên tùy chọn của x. Nếu không có, B = 1"}, "BETA.DIST": {"a": "(x; alpha; beta; cumulative; [A]; [B])", "d": "<PERSON><PERSON>ả về hàm phân phối xác su<PERSON> beta", "ad": "là giá trị từ A đến B để tính giá trị của hàm!là tham số cho phân phối và phải lớn hơn 0!là tham số cho phân phối và phải lớn hơn 0!là giá trị lôgic: đối với hàm phân phối tí<PERSON> lũ<PERSON>, sử dụng TRUE; đối với hàm mật độ xác suất, sử dụng FALSE!là giới hạn dưới tùy chọn cho khoảng x. Nếu bị bỏ qua, A = 0!là giới hạn trên tùy chọn cho khoảng x. Nếu bị bỏ qua, B = 1"}, "BETA.INV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "<PERSON><PERSON><PERSON> về nghịch đ<PERSON>o của hàm mật độ phân phối beta tích lũy (BETA.DIST)", "ad": "là xác suất liên quan đến phân phối beta!là tham số cho phân phối và phải lớn hơn 0! là tham số cho phân phối và phải lớn hơn 0!là giới hạn dưới tùy chọn cho khoảng x. Nếu bị bỏ qua, A = 0!là giới hạn trên tùy chọn cho khoảng x. Nếu bị bỏ qua, B = 1"}, "BINOMDIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "<PERSON><PERSON><PERSON> về thời hạn riêng của nhị thức phân bố xác suất", "ad": "là số lần thành công trong khi thử!là số phép thử độc lập!là xác suất thành công của mỗi lần thử!là giá trị logic: dành cho hàm phân bố tích lũ<PERSON>, dùng TRUE; hàm xác suất số đông, dùng FALSE"}, "BINOM.DIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "<PERSON><PERSON><PERSON> về thời hạn riêng của nhị thức phân bố xác suất", "ad": "là số lần thành công trong khi thử!là số phép thử độc lập!là xác suất thành công của mỗi lần thử!là giá trị lô gíc: dành cho hàm phân bố tích l<PERSON>, dùng ĐÚNG; hàm xác suất số đông, dùng SAI"}, "BINOM.DIST.RANGE": {"a": "(trials; probability_s; number_s; [number_s2])", "d": "Trả về xác suất của kết quả thử sử dụng phân phối nhị thức", "ad": "là số lần thử độc lập!là xác suất thành công của mỗi lần thử!là số lần thành công của mỗi lần thử!nếu được cung cấp, hàm này trả về xác suất mà số lần thử thành công nằm giữa number_s và number_s2"}, "BINOM.INV": {"a": "(trials; probability_s; alpha)", "d": "Tr<PERSON> về giá trị nhỏ nhất có nhị thức phân bố tích lũy lớn hơn hoặc bằng giá trị tiêu chuẩn", "ad": "là số phép thử <PERSON>lli!là xác suất thành công của mỗi lần thử, một số từ 0 đến 1!là gi<PERSON> trị ti<PERSON><PERSON> chu<PERSON>, một số từ 0 đến 1"}, "CHIDIST": {"a": "(x; deg_freedom)", "d": "<PERSON><PERSON>ả về xác suất một phía của phân bố chi-bình-phương", "ad": "là giá trị cần t<PERSON>h phân bố, là số âm!là số hiệu bậc tự do, là số trong khoảng từ 1 đến 10^10, lo<PERSON>i trừ 10^10"}, "CHIINV": {"a": "(probability; deg_freedom)", "d": "Trả về giá trị đảo xác suất một phía của phân bố chi-bình phương", "ad": "là x<PERSON>c suất gắn với chi-b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, là gi<PERSON> trị từ 0 đến 1!là số bậc tự do, là số trong khoảng từ 1 đến 10^10, lo<PERSON><PERSON> trừ 10^10"}, "CHITEST": {"a": "(actual_range; expected_range)", "d": "Tr<PERSON> về kiểm tra tính độc lập: gi<PERSON> trị từ phân bố thống kê chi-bình-phương và bậc tự do tương ứng", "ad": "là khoảng dữ liệu chứa giá trị quan sát để kiểm chứng lại với các giá trị mong đợi!là khoảng dữ liệu chứa tỷ lệ giữa tích của tổng theo hàng và tổng theo cột với toàn tổng"}, "CHISQ.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "<PERSON><PERSON><PERSON> về xác suất phần dư bên trái của phân phối chi-bình phư<PERSON>ng", "ad": "là giá trị cần đánh giá phân phối, một số không âm!là số bậc tự do, một số từ 1 đến 10^10, loại trừ 10^10!là giá trị lôgic cho hàm để trả về: hàm phân phối tích lũy = TRUE; hàm mật độ xác suất = FALSE"}, "CHISQ.DIST.RT": {"a": "(x; deg_freedom)", "d": "Trả về xác suất nhánh phải của phân bố chi bình phương", "ad": "là giá trị mà tại đó bạn muốn đánh giá phân bố, một số không âm!là số bậc tự do, một số nằm giữa 1 và 10^10, ngoài 10^10"}, "CHISQ.INV": {"a": "(probability; deg_freedom)", "d": "<PERSON><PERSON><PERSON> về nghịch đ<PERSON>o của x<PERSON>c suất phần dư bên trái của phân phối chi-bình phương", "ad": "là xác suất liên quan đến phân phối chi-bình phư<PERSON>, một giá trị từ 0 đến 1 và bao gồ<PERSON> cả 0 và 1!là số bậc tự do, một số từ 1 đến 10^10, lo<PERSON>i trừ 10^10"}, "CHISQ.INV.RT": {"a": "(probability; deg_freedom)", "d": "<PERSON><PERSON><PERSON> về nghịch đ<PERSON>o của x<PERSON>c suất phần dư bên phải của phân phối chi-bình phương", "ad": "là xác suất liên quan đến phân phối chi-bình phư<PERSON>, một giá trị từ 0 đến 1 và bao gồ<PERSON> cả 0 và 1!là số bậc tự do, một số từ 1 đến 10^10, lo<PERSON>i trừ 10^10"}, "CHISQ.TEST": {"a": "(actual_range; expected_range)", "d": "Tr<PERSON> về <PERSON>ể<PERSON> định tính độc lập: gi<PERSON> trị từ phân bố thống kê chi-bình-phương và bậc tự do tương <PERSON>ng", "ad": "là khoảng dữ liệu chứa giá trị quan sát để kiểm chứn lại với các giá trị mong đợi!là khoảng dữ liệu chứa tỷ lệ giữa tích của tổng theo hàng và tổng theo cột với toàn tổng"}, "CONFIDENCE": {"a": "(alpha; standard_dev; size)", "d": "<PERSON>r<PERSON> về khoảng tin cậy của trung bình tổng thể, sử dụng phân bố bình thường", "ad": "là mức quan trọng dùng để tính mức tin cậy, một số lớn hơn 0 và nhỏ hơn 1!là độ lệch tiêu chuẩn toàn bộ của khoảng dữ liệu được coi như đã xác định. Standard_dev phải lớn hơn 0!là kích cỡ mẫu"}, "CONFIDENCE.NORM": {"a": "(alpha; standard_dev; size)", "d": "<PERSON><PERSON><PERSON> về khoảng tin cậy cho trung bình của tập hợp, sử dụng phân ph<PERSON>i thông thường", "ad": "là mức quan trọng dùng để tính mức tin cậy, một số lớn hơn 0 và nhỏ hơn 1!là độ lệch chuẩn của tập hợp cho vùng dữ liệu và được coi là xác định. Độ lệch chuẩn phải lớn hơn 0!là kích thước mẫu"}, "CONFIDENCE.T": {"a": "(alpha; standard_dev; size)", "d": "<PERSON><PERSON><PERSON> về khoảng tin cậy cho trung bình của tập hợp, sử dụng phân phối Student T", "ad": "là mức quan trọng dùng để tính mức tin cậy, một số lớn hơn 0 và nhỏ hơn 1!là độ lệch chuẩn của tập hợp cho vùng dữ liệu và được coi là xác định. Độ lệch chuẩn phải lớn hơn 0!là kích thước mẫu"}, "CORREL": {"a": "(array1; array2)", "d": "<PERSON><PERSON><PERSON> về hệ số tương quan giữa hai tập dữ liệu", "ad": "là khoảng ô các giá trị. <PERSON><PERSON><PERSON> trị phải là số, tê<PERSON>, m<PERSON><PERSON>, hoặc tên chứa số!là khoảng ô các giá trị thứ hai. <PERSON><PERSON><PERSON> trị phải là số, tê<PERSON>, m<PERSON><PERSON>, hoặc tên chứa số"}, "COUNT": {"a": "(value1; [value2]; ...)", "d": "<PERSON><PERSON><PERSON> c<PERSON>c ô trong phạm vi chứa giá trị số", "ad": "từ 1 đến 255 tham đối chứa hoặc tham chiếu tới các loại dữ liệu khác nhau, nhưng chỉ tính kiểu số"}, "COUNTA": {"a": "(value1; [value2]; ...)", "d": "<PERSON><PERSON><PERSON> số ô không rỗng trong phạm vi và các giá trị nằm trong danh sách tham đối", "ad": "từ 1 đến 30 tham đối thể hiện giá trị và ô cần đếm. Giá trị có thể là dạng thông tin bất kỳ"}, "COUNTBLANK": {"a": "(range)", "d": "<PERSON><PERSON><PERSON> số ô rỗng trong khoảng các ô xác định", "ad": "là k<PERSON>ng cần đếm số ô rỗng"}, "COUNTIF": {"a": "(range; criteria)", "d": "<PERSON><PERSON><PERSON> số ô trong khoảng thoả mãn điều kiện cho trước", "ad": "là khoảng các ô cần đếm số ô không trắng!là điều kiện dưới dạng số, bi<PERSON><PERSON> thức, hoặc văn bản xác định các ô nào được đếm"}, "COUNTIFS": {"a": "(criteria_range; criteria; ...)", "d": "<PERSON><PERSON><PERSON> số ô được xác định bởi tập cho trước các điều kiện hoặc tiêu chí", "ad": "là dải các ô muốn tính giá trị theo điều kiện cụ thể!là điều kiện ở dạng số, bi<PERSON><PERSON> thức, hay văn bản xác định ô nào sẽ được tính"}, "COVAR": {"a": "(array1; array2)", "d": "<PERSON><PERSON><PERSON> về hiệp ph<PERSON><PERSON><PERSON>i, là trung bình tích độ lệch của mỗi cặp điểm dữ liệu trong hai tập dữ liệu", "ad": "là khoảng ô số nguyên thứ nhất và có thể là số, mảng hoặc tham chiếu chứa số!là khoảng ô số nguyên thứ hai và có thể là số, mảng hoặc tham chiếu chứa số"}, "COVARIANCE.P": {"a": "(array1; array2)", "d": "Tr<PERSON> về hiệp phương sai mẫu, trung bình của các tích độ lệch cho mỗi cặp điểm dữ liệu trong hai tập hợp dữ liệu", "ad": "là khoảng ô số nguyên thứ nhất và phải là số, mảng hoặc tham chiếu chứa số!là khoảng ô số nguyên thứ hai và phải là số, mảng hoặc tham chiếu chứa số"}, "COVARIANCE.S": {"a": "(array1; array2)", "d": "Tr<PERSON> về hiệp phương sai mẫu, trung bình của các tích độ lệch cho mỗi cặp điểm dữ liệu trong hai tập hợp dữ liệu", "ad": "là khoảng ô số nguyên thứ nhất và phải là số, mảng hoặc tham chiếu chứa số!là khoảng ô số nguyên thứ hai và phải là số, mảng hoặc tham chiếu chứa số"}, "CRITBINOM": {"a": "(trials; probability_s; alpha)", "d": "Tr<PERSON> về giá trị nhỏ nhất có phân bố nhị thức tích lũy lớn hơn hoặc bằng giá trị tiêu chuẩn", "ad": "là số phép thử <PERSON>lli!là xác suất thành công của mỗi lần thử, một số từ 0 đến 1!là gi<PERSON> trị ti<PERSON><PERSON> chu<PERSON>, một số từ 0 đến 1"}, "DEVSQ": {"a": "(number1; [number2]; ...)", "d": "Tr<PERSON> về tổng bình phương độ lệch của các điểm dữ liệu so với trung bình mẫu của chúng", "ad": "từ 1 đến 255 đối số, hoặc một mảng hoặc tham chiếu mảng, mà bạn cần DEVS<PERSON> t<PERSON>h toán trên đó"}, "EXPONDIST": {"a": "(x; lambda; cumulative)", "d": "<PERSON><PERSON><PERSON> về phân bố hàm mũ", "ad": "là giá trị của hàm, một số không âm!là giá trị của tham biến, một số dương!là giá trị logic cho hàm trả về: = TRUE là hàm phân bố tích lũy; =FALSE là hàm mật độ xác suất"}, "EXPON.DIST": {"a": "(x; lambda; cumulative)", "d": "<PERSON><PERSON><PERSON> về phân bố hàm mũ", "ad": "là giá trị của hàm, một số không âm!là giá trị của tham biến, một số dương!là giá trị lô gíc cho hàm trả về: = ĐÚNG là hàm phân bố tích lũy; =SAI là hàm mật độ xác suất"}, "FDIST": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "Tr<PERSON> về phân bố xác suất F (độ đa dạng) của hai tập dữ liệu", "ad": "là giá trị định trị của hàm, một số không âm!là tử số bậc tự do, một số từ 1 đến 10^10, loại trừ 10^10!là mẫu số bậc tự do, một số từ 1 đến 10^10, loại trừ 10^10"}, "FINV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Tr<PERSON> về giá trị đảo của phân bố xác suất F: nếu p = FDIST(x,...), thì FINV(p,...) = x", "ad": "là xác suất gắn với phân bố tích lũy <PERSON>, một số từ 0 đến 1!là tử số bậc tự do, một số từ 1 đến 10^10, loại trừ 10^10!là mẫu số bậc tự do, một số từ 1 đến 10^10, loạ<PERSON> trừ 10^10"}, "FTEST": {"a": "(array1; array2)", "d": "Tr<PERSON> về kết quả của kiểm tra-F, là xác suất hai phía có phương sai trong Mảng1 và Mảng2 không quá khác nhau", "ad": "là mảng hoặc khoảng dữ liệu đầu tiên và có thể là số hoặc tên, mảng hoặc tham chiếu chứa số (không tính giá trị trắng)!là mảng hoặc khoảng dữ liệu thứ hai và có thể là số hoặc tên, mảng hoặc tham chiếu chứa số (không tính giá trị trắng)"}, "F.DIST": {"a": "(x; deg_freedom1; deg_freedom2; cumulative)", "d": "Tr<PERSON> về phân phối xác suất F (phần dư bên trái) (độ đa dạng) cho hai tập dữ liệu", "ad": "là giá trị để tính giá trị của hàm, một số không âm!là bậc tự do tử số, một số từ 1 đến 10^10, loại trừ 10^10!là bậc tự do mẫu số, một số từ 1 đến 10^10, loại trừ 10^10!là giá trị lôgic cho hàm để trả về: hàm phân phối tích lũy = TRUE; hàm mật độ xác suất = FALSE"}, "F.DIST.RT": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "Tr<PERSON> về phân phối xác suất <PERSON> (phần dư bên phải) (độ đa dạng) cho hai tập dữ liệu", "ad": "là giá trị để tính giá trị của hàm, một số không âm!là bậc tự do tử số, một số từ 1 đến 10^10, loại trừ 10^10!là bậc tự do mẫu số, một số từ 1 đến 10^10, loại trừ 10^10"}, "F.INV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Tr<PERSON> về nghịch đảo của phân phối xác suất F (phần dư bên trái): nếu p = F.DIST(x,...), thì F.INV(p,...) = x", "ad": "là xác suất liên quan đến phân phối tích lũy <PERSON>, một số từ 0 đến 1 và bao gồm cả 0 và 1!là bậc tự do tử số, một số từ 1 đến 10^10, loại trừ 10^10!là bậc tự do mẫu số, một số từ 1 đến 10^10, loại trừ 10^10"}, "F.INV.RT": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Tr<PERSON> về nghịch đảo của phân phối xác suất F (phần dư bên trái): nếu p = F.DIST(x,...), thì F.INV(p,...) = x", "ad": "là xác suất liên quan đến phân phối tích lũy <PERSON>, một số từ 0 đến 1 và bao gồm cả 0 và 1!là bậc tự do tử số, một số từ 1 đến 10^10, loại trừ 10^10!là bậc tự do mẫu số, một số từ 1 đến 10^10, loại trừ 10^10"}, "F.TEST": {"a": "(array1; array2)", "d": "Tr<PERSON> về kết quả của kiểm-chứng-<PERSON>, là xác suất hai phía có phương sai trong Mảng1 và Mảng2 không quá khác nhau", "ad": "là mảng hoặc khoảng dữ liệu đầu tiên và có thể là số hoặc tên, m<PERSON><PERSON>, hoặc tham chiếu chứa số (không tính giá trị trắng)!là mảng hoặc khoảng dữ liệu thứ hai và có thể là số hoặc tên, m<PERSON><PERSON>, hoặc tham chiếu chứa số (không tính giá trị trắng)"}, "FISHER": {"a": "(x)", "d": "<PERSON><PERSON><PERSON> về biến đổi <PERSON>ơ", "ad": "là giá trị cần biến đổi, một số từ -1 đến 1, loại trừ -1 và 1"}, "FISHERINV": {"a": "(y)", "d": "Tr<PERSON> về giá trị đảo của biến đổi Fi-sơ:nếu y = FISHER(x), thì FISHERINV(y) = x", "ad": "là giá trị cần thực hiện đảo biến đổi"}, "FORECAST": {"a": "(x; known_ys; known_xs)", "d": "<PERSON><PERSON><PERSON> toán hoặc dự đoán giá trị tương lai theo xu hướng tuyến tính bằng cách dùng các giá trị hiện có.", "ad": "là điểm dữ liệu bạn muốn dự đoán giá trị và phải là giá trị số!là mảng hoặc dải dữ liệu số phụ thuộc!là mảng hoặc dải dữ liệu số độc lập. Phương sai của Known_x's không được bằng không"}, "FORECAST.ETS": {"a": "(target_date; values; timeline; [seasonality]; [data_completion]; [aggregation])", "d": "Tr<PERSON> về giá trị dự báo cho một ngày mục tiêu xác định trong tương lai bằng phư<PERSON><PERSON> thứ<PERSON> sang bằng hàm số mũ.", "ad": "là điểm dữ liệu mà Spreadsheet Editor dự đoán một giá trị. Đối số này phải thể hiện mẫu hình giá trị trong đường thời gian.!là mảng hoặc dải dữ liệu số bạn đang dự đoán.!là mảng hoặc dải dữ liệu số độc lập. Ngày tháng trong đường thời gian phải có khoảng cách đồng nhất và không được bằng không.!là giá trị số tùy chọn cho biết độ dài của mẫu hình thời vụ. Giá trị mặc định là 1 cho biết tính thời vụ được tự động phát hiện.!là một giá trị tùy chọn để xử lý giá trị thiếu. G<PERSON><PERSON> trị mặc định là 1 thay thế giá trị thiếu bằng phép nội suy và 0 thay thế giá trị thiếu bằng số không.!là giá trị số tùy chọn để tập hợp nhiều giá trị có cùng nhãn thời gian. Nếu để trống thì Spreadsheet Editor lấy trung bình các giá trị."}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "Tr<PERSON> về một khoảng tin cậy cho giá trị dự báo vào ngày mục tiêu được chỉ định.", "ad": "là điểm dữ liệu mà Spreadsheet Editor dự đoán một giá trị. Đối số này phải thể hiện mẫu hình giá trị trong đường thời gian.!là mảng hoặc dải dữ liệu số bạn đang dự đoán.!là mảng hoặc dải dữ liệu số độc lập. Ngày tháng trong đường thời gian phải có khoảng cách đồng nhất và không được bằng không.!là một số giữa 0 và 1, cho biết mức tin cậy cho khoảng tin cậy được tính. Giá trị mặc định là .95!là giá trị số tùy chọn cho biết độ dài của mẫu hình thời vụ. Giá trị mặc định là 1 cho biết tính thời vụ được tự động phát hiện.!là một giá trị tùy chọn để xử lý giá trị thiếu. Gi<PERSON> trị mặc định là 1 thay thế giá trị thiếu bằng phép nội suy và 0 thay thế giá trị thiếu bằng số không.!là giá trị số tùy chọn để tập hợp nhiều giá trị có cùng nhãn thời gian. Nếu để trống thì Spreadsheet Editor lấy trung bình các giá trị."}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "Tr<PERSON> về độ dài của một mô hình lặp mà ứng dụng phát hiện cho một chuỗi thời gian được chỉ định.", "ad": "là mảng hoặc dải dữ liệu số bạn đang dự đoán.!là mảng hoặc dải dữ liệu số độc lập. <PERSON><PERSON><PERSON> tháng trong đường thời gian phải có khoảng cách đồng nhất và không được bằng không.!là một giá trị tùy chọn để xử lý giá trị thiếu. Giá trị mặc định là 1 thay thế giá trị thiếu bằng phép nội suy và 0 thay thế giá trị thiếu bằng số không.!là giá trị số tùy chọn để tập hợp nhiều giá trị có cùng nhãn thời gian. Nếu để trống thì Spreadsheet Editor lấy trung bình các giá trị."}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "T<PERSON><PERSON> về số liệu thống kê yêu cầu cho dự báo.", "ad": "là mảng hoặc phạm vi dữ liệu số mà bạn đang dự đoán.! là mảng hoặc phạm vi dữ liệu số độc lập. Các ngày trong dòng thời gian phải có bước nhất quán giữa chúng và không được bằng 0.!là một số từ 1 đến 8, cho biết thống kê nào Spreadsheet Editor sẽ trả về cho dự báo được tính toán.!là một giá trị số tùy chọn cho biết độ dài của mẫu theo mùa. Giá trị mặc định của 1 cho biết tính thời vụ được phát hiện tự động.!là giá trị tùy chọn để xử lý các giá trị bị thiếu. Giá trị mặc định của 1 thay thế các giá trị bị thiếu bằng phép nội suy và 0 thay thế chúng bằng số không.!là giá trị số tùy chọn để tổng hợp nhiều giá trị có cùng dấu thời gian. Nếu để trống, Spreadsheet Editor tính trung bình các giá trị."}, "FORECAST.LINEAR": {"a": "(x; known_ys; known_xs)", "d": "<PERSON><PERSON><PERSON> toán hoặc dự đoán một giá trị trong tương lai theo xu hướng tuyến tính bằng cách dùng các giá trị hiện có.", "ad": "là điểm dữ liệu bạn muốn dự đoán giá trị và phải là giá trị số!là mảng hoặc dải dữ liệu số phụ thuộc!là mảng hoặc dải dữ liệu số độc lập. Phương sai của Known_x's không được bằng không"}, "FREQUENCY": {"a": "(data_array; bins_array)", "d": "<PERSON><PERSON>h số lần xuất hiện của một giá trị trong khoảng các giá trị và trả về một mảng dọc các số có nhiều phần tử hơn Bảng_đựng", "ad": "là mảng hoặc tham chiếu tới tập các giá trị cần đếm tần suất (khô<PERSON> tính ô trắng và văn bản)!là mảng hoặc tham chiếu tới các khẩu độ cần nhóm giá trị trong Mảng_dữ_liệu"}, "GAMMA": {"a": "(x)", "d": "Trả về giá trị hàm Gamma", "ad": "là giá trị mà bạn muốn tính Gamma"}, "GAMMADIST": {"a": "(x; alpha; beta; cumulative)", "d": "<PERSON><PERSON>ả về phân bố Gamma", "ad": "Là giá trị cần tính giá trị cho phân bố, một số không âm!là một tham biến cho phân bố, một số dương!là một tham biến cho phân bố, một số dương. Nếu beta = 1, GAMMADIST trả về phân bố gamma chuẩn!là giá trị logic: dành cho hàm phân bố tích lũy, dùng TRUE; hàm xác suất số đông, dùng FALSE"}, "GAMMA.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "Trả về phân phối gamma", "ad": "là giá trị bạn cần tính giá trị phân phối, một số không âm!là tham số cho phân phối, một số dương!là tham số cho phân phối, một số dương. Nếu beta = 1, GAMMA.DIST trả về phân phối gamma tiêu chuẩn!là giá trị lôgic: trả về hàm phân phối tích lũy = TRUE; trả về hàm xác suất số đông = FALSE hoặc bị bỏ qua"}, "GAMMAINV": {"a": "(probability; alpha; beta)", "d": "Tr<PERSON> về giá trị đảo của phân bố tích lũy gamma: nếu p = GAMMADIST(x,...) thì GAMMAINV(p,...) = x", "ad": "là xác suất gắn với phân bố gamma, một số từ 0 đến 1!là một tham biến cho phân bố, một số dương!là một tham biến cho phân bố, một số dương. Nếu beta = 1, GAMMAINV trả về giá trị đảo của phân bố gamma chuẩn"}, "GAMMA.INV": {"a": "(probability; alpha; beta)", "d": "<PERSON><PERSON><PERSON> về nghịch đ<PERSON>o của hàm tích lũy gamma: nếu p = GAMMA.DIST(x,...), thì GAMMA.INV(p,...) = x", "ad": "là xác suất liên quan đến phân phối gamma, một số từ 0 đến 1, bao g<PERSON><PERSON> cả 0 và 1!là tham số cho phân phối, một số dương!là tham số cho phân phối, một số dương. Nếu beta = 1, GAMMA.INV trả về nghịch đảo của phân phối gamma chuẩn"}, "GAMMALN": {"a": "(x)", "d": "<PERSON><PERSON>ả về lô-ga-rít tự nhiên của hàm gamma", "ad": "là giá trị cần t<PERSON>h GAMMALN, là số dươ<PERSON>"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Trả về loga tự nhiên của hàm gamma", "ad": "là giá trị cho bạn tính GAMMALN.PRECISE, một số dương"}, "GAUSS": {"a": "(x)", "d": "Trả về 0.5 ít hơn hàm phân phối tích lũy chuẩn chuẩn hóa", "ad": "là giá trị bạn muốn tìm phân phối"}, "GEOMEAN": {"a": "(number1; [number2]; ...)", "d": "Tr<PERSON> về trung bình hình học của một mảng hoặc khoảng dữ liệu số dương", "ad": "là các số từ 1 đến 255 hoặc các tên, c<PERSON><PERSON> m<PERSON>, hoặc các tham chiếu có chứa số cần tính trung bình"}, "GROWTH": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "<PERSON><PERSON><PERSON> về các số tăng theo hướng hàm mũ phù hợp với các điểm dữ liệu", "ad": "là tập hợp các giá trị y xác định theo quan hệ y = b*m^x, một mảng hoặc khoảng các số dương!là tập hợp tùy chọn các giá trị x xác định theo quan hệ = b*m^x, một mảng hoặc khoảng có cùng kích cỡ như y-xác định!là giá trị x mới cần cho hàm GROWTH trả về giá trị y tương ứng!là giá trị lô gíc: hằng số b được tính toán bình thường nếu Hằng số=ĐÚNG; b đặt bằng 1 nếu Hằng số=SAI hoặc không có"}, "HARMEAN": {"a": "(number1; [number2]; ...)", "d": "Trả về trung bình điều hoà của tập dữ liệu số dương: số nghịch đảo của trung bình nghịch đảo số học", "ad": "từ 1 đến 255 số hoặc tên, <PERSON><PERSON><PERSON>, hoặc tham chiếu chứa số cần tính trung bình điều hoà"}, "HYPGEOM.DIST": {"a": "(sample_s; number_sample; population_s; number_pop; cumulative)", "d": "<PERSON><PERSON>ả về phân phối siêu hình học", "ad": "là số lần thành công trong mẫu!là kích cỡ của mẫu!là số lần thành công trong tập hợp!là kích cỡ tập hợp!là giá trị lôgic: đối với hàm phân phối tích lũ<PERSON>, sử dụng TRUE; đối với hàm mật độ xác suất, sử dụng FALSE"}, "HYPGEOMDIST": {"a": "(sample_s; number_sample; population_s; number_pop)", "d": "<PERSON><PERSON>ả về phân bố siêu hình học", "ad": "là số lần thành công trong mẫu!là kích cỡ của mẫu!là số lần thành công trong tập toàn bộ!là kích cỡ tập"}, "INTERCEPT": {"a": "(known_ys; known_xs)", "d": "<PERSON><PERSON>h toán giao điểm của đường thẳng với trục y dùng đường hồi quy khớp nhất vẽ qua các giá trị x và giá trị y đã biết", "ad": "là tập các giá trị quan sát hoặc dữ liệu phụ thuộc và có thể là số hoặc tên, mảng, hoặc tham chiếu chứa số!là tập các giá trị quan sát hoặc dữ liệu độc lập và có thể là số hoặc tên, mảng, hoặc tham chiếu chứa số"}, "KURT": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON> về độ nhọn của tập dữ liệu", "ad": "từ 1 đến 255 số hoặc tên, <PERSON><PERSON><PERSON>, hoặc tham chiếu chứa số cần tính độ nhọn"}, "LARGE": {"a": "(array; k)", "d": "Tr<PERSON> về giá trị lớn thứ k trong tập dữ liệu. <PERSON><PERSON>, số lớn thứ năm", "ad": "là mảng hoặc khoảng dữ liệu cần xác định giá trị lớn thứ k!là vị trí (từ giá trị lớn nhất) của một giá trị trong mảng hoặc khoảng ô trả về"}, "LINEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "<PERSON><PERSON><PERSON> về thống kê mô tả xu hướng tuyến tính phù hợp với các điểm dữ liệu, bằng cách khớp đường thẳng dùng phương pháp bình quân nhỏ nhất", "ad": "là tập hợp các giá trị y xác định theo quan hệ y = mx + b!là tập hợp tùy chọn các giá trị x xác định theo quan hệ y = mx + b!là giá trị lô gíc: hằng số b đư<PERSON><PERSON> tính toán bình thường nếu Hằng số=ĐÚNG hoặc không có; b đặt bằng 0 nếu Hằng số=SAI!là giá trị lô gíc: sẽ trả về thống kê hồi quy bổ sung nếu = ĐÚNG; trả về hệ số m và hằng số b nếu = SAI hoặc không có"}, "LOGEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "Tr<PERSON> về thống kê mô tả đường hàm mũ phù hợp với các điểm dữ liệu", "ad": "là tập hợp các giá trị y xác định theo quan hệ y = b*m^x!là tập hợp tùy chọn các giá trị x xác định theo quan hệ y = b*m^x!là giá trị lô gíc: hằng số b đư<PERSON><PERSON> tính toán bình thường nếu Hằng số = ĐÚNG hoặc không có; b đặt bằng 1 nếu Hằng số = SAI!là giá trị lô gíc: sẽ trả về thống kê hồi quy bổ sung nếu = ĐÚNG; trả về hệ số m và hằng số b nếu = SAI hoặc không có"}, "LOGINV": {"a": "(probability; mean; standard_dev)", "d": "Trả về giá trị đảo của hàm phân bố tích lũy loga chuẩn của x, mà ln(x) đ<PERSON><PERSON><PERSON> phân phối chuẩn với tham số Mean và Standard_dev", "ad": "là xác suất gắn với phân bố loga chuẩn, một số từ 0 đến 1, k<PERSON> cả 0 và 1!là trung bình của ln(x)!là độ lệch tiêu chuẩn của ln(x), một số dương"}, "LOGNORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Trả về phân phối lôga bình thường của x, trong đó ln(x) được phân phối chuẩn với tham số Trung bình và Độ lệch chuẩn", "ad": "là giá trị để tính giá trị của hàm, một số dương!là trung bình của ln(x)!là độ lệch chuẩn của ln(x), một số dương!là giá trị lôgic: đối với hàm phân phối tí<PERSON> l<PERSON>, sử dụng TRUE; đối với hàm mật đ<PERSON> x<PERSON><PERSON> suấ<PERSON>, sử dụng FALSE"}, "LOGNORM.INV": {"a": "(probability; mean; standard_dev)", "d": "Tr<PERSON> về giá trị đảo của hàm phân bố lô-ga-rít chuẩn tích lũy, trong đó ln(x) <PERSON><PERSON><PERSON><PERSON> phân bố chuẩn với tham số Trung_bình và Độ_lệch_tiêu _chuẩn", "ad": "là xác suất gắn với phân bố lô-ga-r<PERSON><PERSON>, một số từ 0 đến 1!là trung bình của ln(x)!là độ lệch tiêu chuẩn của ln(x), một số dương"}, "LOGNORMDIST": {"a": "(x; mean; standard_dev)", "d": "Tr<PERSON> về phân bố lô-ga-rít chuẩn tích lũ<PERSON>, trong đó ln(x) đ<PERSON><PERSON><PERSON> phân bố chuẩn với tham số Mean và Standard_dev", "ad": "là giá trị định trị của hàm, một số dương!là trung bình của ln(x)!là độ lệch tiêu chuẩn của ln(x), một số dương"}, "MAX": {"a": "(number1; [number2]; ...)", "d": "Trả về giá trị lớn nhất trong tập hợp giá trị. Không tính giá trị lô gíc và văn bản", "ad": "từ 1 đến 255 số, ô rỗng, gi<PERSON> trị lô-gic, hoặc văn bản số bạn cần tính giá trị lớn nhất"}, "MAXA": {"a": "(value1; [value2]; ...)", "d": "Trả về giá trị lớn nhất trong tập các giá trị. Không loại trừ giá trị lô gíc và văn bản", "ad": "từ 1 đến 255 số, ô rỗng, gi<PERSON> trị lô gíc, hoặc văn bản số cần giá trị tối đa"}, "MAXIFS": {"a": "(max_range; criteria_range; criteria; ...)", "d": "Tr<PERSON> về giá trị tối đa trong số các ô được xác định bởi bộ các điều kiện hoặc tiêu chí đã cho", "ad": "các ô trong đó để xác định giá trị tối đa!là phạm vi các ô bạn muốn đánh giá đối với các điều kiện cụ thể!là điều kiện hoặc tiêu chí dưới dạng số, biể<PERSON> thức hoặc văn bản xác định các ô nào sẽ được bao gồm khi xác định giá trị tối đa"}, "MEDIAN": {"a": "(number1; [number2]; ...)", "d": "Trả lại số trung bình hoặc số ở khoảng giữa tập hợp các số đã cho", "ad": "là các số từ 1 đến 255 hoặc các tên, các mảng hoặc các tham chiếu có chứa các số mà bạn muốn số trung bình"}, "MIN": {"a": "(number1; [number2]; ...)", "d": "Tr<PERSON> về số nhỏ nhất trong tập hợp giá trị. Không tính giá trị lô gíc và văn bản", "ad": "từ 1 đến 255 số, ô rỗng, gi<PERSON> trị lô-gic, hoặc văn bản số bạn cần tính giá trị nhỏ nhất"}, "MINA": {"a": "(value1; [value2]; ...)", "d": "Trả về giá trị nhỏ nhất trong tập các giá trị. Không loại trừ giá trị lô gíc và văn bản", "ad": "từ 1 đến 255 số, ô rỗng, gi<PERSON> trị lô gíc, hoặc văn bản số cần giá trị tối thiểu"}, "MINIFS": {"a": "(min_range; criteria_range; criteria; ...)", "d": "Tr<PERSON> về giá trị tối thiểu trong số các ô được xác định bởi bộ các điều kiện hoặc tiêu chí đã cho", "ad": "các ô trong đó để xác định giá trị tối thiểu!là phạm vi các ô bạn muốn đánh giá các điều kiện cụ thể!là điều kiện hoặc tiêu chí dưới dạng số, biểu thức hoặc văn bản xác định các ô nào sẽ được bao gồm khi xác định giá trị tối thiểu"}, "MODE": {"a": "(number1; [number2]; ...)", "d": "Tr<PERSON> về mức xuất hiện thường xuyên hoặc mức lặp của một giá trị trong mảng hoặc khoảng dữ liệu", "ad": "từ 1 đến 255 số hoặc tên, mảng hoặc tham chiếu chứa số cần t<PERSON>h cách thức"}, "MODE.MULT": {"a": "(number1; [number2]; ...)", "d": "Tr<PERSON> về mảng dọc các giá trị xuất hiện thường xuyên nhất, hoặc lặp lại trong mảng hoặc vùng dữ liệu.  Đối với mảng ngang, sử dụng =TRANSPOSE(MODE.MULT(number1,number2,...))", "ad": "là 1 đến 255 số, hoặc tên, mảng hoặc tham chiếu có chứa các số cần lập mẫu"}, "MODE.SNGL": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON> về mức xuất hiện thư<PERSON><PERSON> xuyê<PERSON>, hoặc mức lặp của một giá trị trong mảng hoặc khoảng dữ liệu", "ad": "từ 1 đến 255 số, hoặc tê<PERSON>, <PERSON><PERSON><PERSON>, hoặc tham chiếu chứa số cần t<PERSON>h cách thức"}, "NEGBINOM.DIST": {"a": "(number_f; number_s; probability_s; cumulative)", "d": "Tr<PERSON> về xác suất nh<PERSON> thức âm, x<PERSON><PERSON> suất sẽ là Number_f lần thất bại trước lần thành công thứ Number_s, với xác suất thành công Probability_s", "ad": "là số lần thất bại!là ngưỡng số lần thành công!là xác suất thành công; là số từ 0 đến 1!là giá trị lôgic: đối với hàm phân phố<PERSON> t<PERSON><PERSON> l<PERSON>, sử dụng TRUE; đối với hàm xác suất số đông, sử dụng FALSE"}, "NEGBINOMDIST": {"a": "(number_f; number_s; probability_s)", "d": "Trả về phân bố nhị thức âm, là xác suất sẽ có Number_f lần thất bại trước thành công thứ Number_s-th, với xác suất của thành công là Probability_s", "ad": "là số lần thất bại!là số ngưỡng của thành công!là xác suất của thành công; một số từ 0 đến 1"}, "NORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Tr<PERSON> về phân phối bình thường cho độ lệch chuẩn và trung bình đã chỉ định", "ad": "là giá trị cần tính phân phối!là giá trị trung bình cộng của phân phối!là độ lệch chuẩn của phân phối, một số dương!là giá trị lôgic: đối với hàm phân phối tí<PERSON> l<PERSON>, sử dụng TRUE; đối với hàm mật đ<PERSON> x<PERSON><PERSON> suấ<PERSON>, sử dụng FALSE"}, "NORMDIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Trả về phân bố tích lũy chuẩn của trung bình và độ lệch tiêu chuẩn cho trước", "ad": "là giá trị cần tính phân bố!là trung bình số học của phân bố!là độ lệch tiêu chuẩn của phân bố, một số dương!là giá trị logic: dành cho hàm phân bố tích lũ<PERSON>, dùng TRUE; hàm xác suất số đông, dùng FALSE"}, "NORM.INV": {"a": "(probability; mean; standard_dev)", "d": "Trả về giá trị đảo của phân bố tích lũy chuẩn của trung bình và độ lệch tiêu chuẩn cho trước", "ad": "là xác suất gắn với phân bố chuẩn, một số từ 0 đến 1!là trung bình số học của phân bố!là độ lệch tiêu chuẩn của phân bố, một số dương"}, "NORMINV": {"a": "(probability; mean; standard_dev)", "d": "Trả về giá trị đảo của phân bố tích lũy chuẩn cho độ lệch trung bình và tiêu chuẩn cho trước", "ad": "là xác suất gắn với phân bố chuẩn, một số từ 0 đến 1, k<PERSON> cả 0 và 1!là trung bình số học của phân bố!là độ lệch tiêu chuẩn của phân bố, một số dương"}, "NORM.S.DIST": {"a": "(z; cumulative)", "d": "Tr<PERSON> về phân phối bình thường chuẩn (có trung bình là không và độ lệch chuẩn là một)", "ad": "là giá trị cần tính phân phối!là giá trị lôgic cho hàm để trả về: hàm phân phối tích lũy = TRUE; hàm mật độ xác suất = FALSE"}, "NORMSDIST": {"a": "(z)", "d": "<PERSON><PERSON><PERSON> về phân bố tích lũy chuẩn (có trung bình là không và độ lệch tiêu chuẩn là 1)", "ad": "là giá trị cần t<PERSON>h phân bố"}, "NORM.S.INV": {"a": "(probability)", "d": "Tr<PERSON> về giá trị đảo của phân bố tích lũy chuẩn (có trung bình là không và độ lêch tiêu chuẩn là 1)", "ad": "<PERSON><PERSON> x<PERSON><PERSON> suất gắn với phân bố chuẩn, một số từ 0 đến 1"}, "NORMSINV": {"a": "(probability)", "d": "Tr<PERSON> về giá trị đảo của phân bố tích lũy chuẩn (có trung bình là không và độ lêch tiêu chuẩn là 1)", "ad": "<PERSON><PERSON> x<PERSON><PERSON> suất gắn với phân bố chuẩn, một số từ 0 đến 1"}, "PEARSON": {"a": "(array1; array2)", "d": "<PERSON><PERSON><PERSON> về hệ số tương quan mômen tích <PERSON><PERSON>, r", "ad": "là tập các giá trị độc lập!là tập các giá trị phụ thuộc"}, "PERCENTILE": {"a": "(array; k)", "d": "Trả về phân vị thứ k của các giá trị trong khoảng", "ad": "là mảng hoặc khoảng dữ liệu xác định thế tương quan!là giá trị phân vị trong khoảng từ 0 đến 1"}, "PERCENTILE.EXC": {"a": "(array; k)", "d": "Tr<PERSON> về phân vị thứ k của giá trị trong một kho<PERSON>ng, trong đó k nằm trong khoảng 0..1, bao g<PERSON>m", "ad": "là cả mảng hoặc vùng dữ liệu xác định vị trí tương đối!là giá trị phân vị từ 0 đến 1, bao g<PERSON><PERSON> c<PERSON> 0 và 1"}, "PERCENTILE.INC": {"a": "(array; k)", "d": "Tr<PERSON> về phân vị thứ k của giá trị trong một kho<PERSON>ng, trong đó k nằm trong khoảng 0..1, bao g<PERSON>m", "ad": "là cả mảng hoặc vùng dữ liệu xác định vị trí tương đối!là giá trị phân vị từ 0 đến 1, bao g<PERSON><PERSON> c<PERSON> 0 và 1"}, "PERCENTRANK": {"a": "(array; x; [significance])", "d": "Tr<PERSON> về cấp của giá trị trong tập dữ liệu đ<PERSON><PERSON><PERSON> gán như số phần trăm của tập dữ liệu", "ad": "là mảng hoặc khoảng dữ liệu với các giá trị số xác định thế tương quan!là giá trị cần biết cấp!là giá trị tùy chọn chỉ định số chữ số có nghĩa của số phần trăm trả về, là 3 chữ số nếu không có (0.xxx%)"}, "PERCENTRANK.EXC": {"a": "(array; x; [significance])", "d": "<PERSON><PERSON><PERSON> về thứ hạng của một giá trị trong tập hợp dữ liệu dưới dạng phần trăm của tập hợp dữ liệu dưới dạng phần trăm (0..1, bao gồ<PERSON> cả 0 và 1) của tập hợp dữ liệu", "ad": "là mảng hoặc phạm vi dữ liệu có giá trị số xác định vị trí tương đối!là giá trị mà bạn muốn biết thứ hạng! là một giá trị tùy chọn xác định số chữ số có nghĩa cho tỷ lệ phần trăm được trả về, ba chữ số nếu bị bỏ qua (0.xxx%)"}, "PERCENTRANK.INC": {"a": "(array; x; [significance])", "d": "<PERSON><PERSON><PERSON> về thứ hạng của một giá trị trong tập hợp dữ liệu dưới dạng phần trăm của tập hợp dữ liệu dưới dạng phần trăm (0..1, bao gồ<PERSON> cả 0 và 1) của tập hợp dữ liệu", "ad": "là mảng hoặc phạm vi dữ liệu có giá trị số xác định vị trí tương đối!là giá trị mà bạn muốn biết thứ hạng!là một giá trị tùy chọn xác định số chữ số có nghĩa cho tỷ lệ phần trăm được trả về, ba chữ số nếu bị bỏ qua (0.xxx%)"}, "PERMUT": {"a": "(number; number_chosen)", "d": "Trả về số hoán vị của số đối tượng đư<PERSON><PERSON> chọn từ tổng số đối tượng", "ad": "là tổng số đối tượng!là số đối tượng trong mỗi hoán vị"}, "PERMUTATIONA": {"a": "(number; number_chosen)", "d": "Trả về số lượng hoán vị của một số lượng đối tượng cho trước (có trùng lặp) mà có thể được chọn từ tổng số đối tượng", "ad": "là tổng số lượng các đối tượng!là số lượng đối tượng của mỗi hoán vị"}, "PHI": {"a": "(x)", "d": "Trả về hàm mật độ của phân phối chuẩn chuẩn hóa", "ad": "là con số bạn muốn tìm mật độ của phân phối chuẩn chuẩn hóa"}, "POISSON": {"a": "(x; mean; cumulative)", "d": "<PERSON><PERSON><PERSON> về phân b<PERSON>", "ad": "là số sự kiện!là giá trị số mong muốn, một số dương!là giá trị lô gíc: dành cho xác suất Poisson tích l<PERSON>, dùng TRUE; hàm xác suất số đông Poisson, dùng FALSE"}, "POISSON.DIST": {"a": "(x; mean; cumulative)", "d": "<PERSON><PERSON><PERSON> về phân b<PERSON>ơn", "ad": "là số sự kiện!là giá trị số mong muốn, một số dương!là giá trị lô gíc: dành cho xác suất <PERSON>uat-sơn tích l<PERSON>, dùng ĐÚNG; hàm xác suất số đông Puat-sơn, dùng SAI"}, "PROB": {"a": "(x_range; prob_range; lower_limit; [upper_limit])", "d": "Trả về xác suất của các giá trị trong khoảng hai giới hạn hoặc bằng giới hạn dưới", "ad": "là khoảng giá trị số của x cùng với xác suất của chúng!là tập hợp xác suất gắn với các giá trị trong Khoảng_X, là giá trị từ 0 đến 1 không tính 0!là giới hạn dưới của giá trị cần lấy xác suất!là giới hạn trên tùy chọn của giá trị. Nếu không có, hàm PROB trả về xác suất của các giá trị Khoảng_X bằng Giới_hạn_dưới"}, "QUARTILE": {"a": "(array; quart)", "d": "<PERSON><PERSON><PERSON> về tứ phân vị của tập dữ liệu", "ad": "là mảng hoặc khoảng giá trị số cần xác định giá trị tứ phân vị!là một số: giá trị tối thiểu = 0; tứ phân vị đầu tiên= 1; giá trị giữa = 2; tứ phân vị thứ ba= 3; giá trị tối đa = 4"}, "QUARTILE.INC": {"a": "(array; quart)", "d": "<PERSON>r<PERSON> về tứ phân vị của tập hợp dữ liệ<PERSON>, d<PERSON><PERSON> trên giá trị phần trăm từ 0..1, bao g<PERSON><PERSON> cả 0 và 1", "ad": "là mảng hoặc khoảng ô có giá trị số cần tính giá trị tứ phân vị!là số: giá trị tối thiểu = 0; tứ phân vị thứ 1 = 1; giá trị trung bình = 2; tứ phân vị thứ 3 = 3; giá trị tối đa = 4"}, "QUARTILE.EXC": {"a": "(array; quart)", "d": "<PERSON>r<PERSON> về tứ phân vị của tập hợp dữ liệ<PERSON>, d<PERSON><PERSON> trên giá trị phần trăm từ 0..1, bao g<PERSON><PERSON> cả 0 và 1", "ad": "là mảng hoặc khoảng ô có giá trị số cần tính giá trị tứ phân vị!là số: giá trị tối thiểu = 0; tứ phân vị thứ 1 = 1; giá trị trung bình = 2; tứ phân vị thứ 3 = 3; giá trị tối đa = 4"}, "RANK": {"a": "(number; ref; [order])", "d": "Tr<PERSON> về cấp của số trong danh sách: là độ lớn tương đối của nó so với các giá trị khác trong danh sách", "ad": "là số cần tìm cấp!là một mảng hoặc tham chiếu tới danh sách các số. Không tính các giá trị khác số!là một số: =0 hoặc không có sẽ tính cấp số trong danh sách giảm dần; = giá trị bất kỳ khác không sẽ tính cấp số trong danh sách tăng dần"}, "RANK.AVG": {"a": "(number; ref; [order])", "d": "Trả về xếp hạng của số trong danh sách các số: kích cỡ của nó là tương đối với các giá trị khác trong danh sách; nếu có nhiều hơn một giá trị với cùng xếp hạng, sẽ trả về xếp hạng trung bình", "ad": "là số bạn muốn tìm xếp hạng!là mảng của, hoặc tham chiếu tới, danh sách các số. Bỏ qua các giá trị không phải là số!là một số: xếp hạng trong danh sách sắp xếp giảm dần = 0 hoặc bỏ qua; xếp hạng trong danh sách sắp xếp tăng dần = giá trị khác không bất kỳ"}, "RANK.EQ": {"a": "(number; ref; [order])", "d": "Trả về xếp hạng của số trong danh sách các số: kích cỡ của nó là tương đối với các giá trị khác trong danh sách; nếu có nhiều hơn một giá trị với cùng xếp hạng, sẽ trả về xếp hạng trung bình", "ad": "là số bạn muốn tìm xếp hạng!là mảng của, hoặc tham chiếu tới, danh sách các số. Bỏ qua các giá trị không phải là số!là một số: xếp hạng trong danh sách sắp xếp giảm dần = 0 hoặc bỏ qua; xếp hạng trong danh sách sắp xếp tăng dần = giá trị khác không bất kỳ"}, "RSQ": {"a": "(known_ys; known_xs)", "d": "<PERSON><PERSON><PERSON> về bình phương hệ số tương quan mômen tích <PERSON>-sơn từ các điểm dữ liệu đã cho", "ad": "là mảng hoặc khoảng các điểm dữ liệu và có thể là số hoặc tên, m<PERSON><PERSON>, hoặc tham chiếu chứa số!là mảng hoặc khoảng các điểm dữ liệu và có thể là số hoặc tên, m<PERSON><PERSON>, hoặc tham chiếu chứa số"}, "SKEW": {"a": "(number1; [number2]; ...)", "d": "Tr<PERSON> về độ xiên của phân bố: đặc trưng mức độ mất đối xứng của phân bố xung quanh trung bình của nó", "ad": "từ 1 đến 255 số hoặc tên, <PERSON><PERSON><PERSON>, hoặc tham chiếu chứa số cần tính độ xiên"}, "SKEW.P": {"a": "(number1; [number2]; ...)", "d": "Trả về độ xiên của phân phối dựa trên một tập mẫu: đặc tính của mức độ bất đối xứng của một phân phối xoay quanh giá trị trung bình", "ad": "là từ 1 đến 254 số lượng tên, mảng, hoặc tham chiếu mà có chứa con số bạn muốn tìm độ xiên của tập mẫu"}, "SLOPE": {"a": "(known_ys; known_xs)", "d": "T<PERSON><PERSON> về độ dốc của đường hồi quy tuyến tính từ các điểm dữ liệu đã cho", "ad": "là mảng hoặc khoảng ô các điểm dữ liệu số phụ thuộc và có thể là số hoặc tên, m<PERSON><PERSON>, hoặc tham chiếu chứa số!là tập hợp các điểm dữ liệu độc lập và có thể là số hoặc tên, m<PERSON><PERSON>, hoặc tham chiếu chứa số"}, "SMALL": {"a": "(array; k)", "d": "Tr<PERSON> về giá trị lớn thứ k trong tập dữ liệu. <PERSON><PERSON>, số lớn thứ năm", "ad": "là mảng hoặc khoảng dữ liệu số cần xác định giá trị lớn thứ k!là vị trí (từ giá trị nhỏ nhất) của một giá trị trong mảng hoặc khoảng ô trả về"}, "STANDARDIZE": {"a": "(x; mean; standard_dev)", "d": "Tr<PERSON> về giá trị chuẩn hóa từ phân bố đặc trưng bởi trung bình và độ lệch tiêu chuẩn", "ad": "là giá trị cần chuẩn hoá!là trung bình số học của phân bố!là độ lệch tiêu chuẩn của phân bố, một số dương"}, "STDEV": {"a": "(number1; [number2]; ...)", "d": "Ước lượng độ lệch tiêu chuẩn dựa trên mẫu (không tính giá trị logic và văn bản trong mẫu)", "ad": "từ 1 đến 255 số tương ứng với mẫu tập toàn bộ, có thể là số hoặc tham chiếu chứa số"}, "STDEV.P": {"a": "(number1; [number2]; ...)", "d": "T<PERSON>h toán độ lệch tiêu chuẩn dựa trên tập toàn bộ là các tham đối (không tính giá trị lô gíc và văn bản)", "ad": "từ 1 đến 255 số tương ứng với tập toàn bộ và có thể là số hoặc tham chiếu chứa số"}, "STDEV.S": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON> nhận độ lệch tiêu chuẩn dựa trên mẫu (không tính giá trị lô gíc và văn bản trong mẫu)", "ad": "từ 1 đến 255 số tương ứng với mẫu tập toàn bộ, có thể là số hoặc tham chiếu chứa số"}, "STDEVA": {"a": "(value1; [value2]; ...)", "d": "Ước đoán độ lệch tiêu chuẩn dựa trên mẫu, t<PERSON>h cả giá trị lô gíc và văn bản. Văn bản và giá trị lô gíc SAI có giá trị 0; giá trị lô gíc ĐÚNG có giá trị 1", "ad": "từ 1 đến 255 giá trị tương ứng với một mẫu của tập toàn bộ và có thể là giá trị, tên hoặc tham chiếu tới giá trị"}, "STDEVP": {"a": "(number1; [number2]; ...)", "d": "T<PERSON>h toán độ lệch tiêu chuẩn dựa trên tập toàn bộ là các đối số (không tính giá trị logic và văn bản)", "ad": "từ 1 đến 255 số tương ứng với tập toàn bộ và có thể là số hoặc tham chiếu chứa số"}, "STDEVPA": {"a": "(value1; [value2]; ...)", "d": "T<PERSON>h toán độ lệch tiêu chuẩn dựa trên tập toàn bộ, tính cả giá trị lô gíc và văn bản. Văn bản và giá trị lô gíc SAI có giá trị 0; giá trị lô gíc ĐÚNG có giá trị 1", "ad": "từ 1 đến 255 giá trị tương ứng với một tập toàn bộ và có thể là giá trị, t<PERSON><PERSON>, m<PERSON><PERSON>, hoặc tham chiếu chứa giá trị"}, "STEYX": {"a": "(known_ys; known_xs)", "d": "Tr<PERSON> về độ sai chuẩn của giá trị y ước đoán cho mỗi giá trị x trong hồi quy", "ad": "là mảng hoặc khoảng các điểm dữ liệu phụ thuộc và có thể là số hoặc tên, m<PERSON><PERSON>, hoặc tham chiếu chứa số!là mảng hoặc khoảng các điểm dữ liệu độc lập và có thể là số hoặc tên, m<PERSON><PERSON>, hoặc tham chiếu chứa số"}, "TDIST": {"a": "(x; deg_freedom; tails)", "d": "<PERSON><PERSON><PERSON> về phân bố t Student", "ad": "là giá trị số cần tính phân bố!là số nguyên chỉ số bậc tự do đặc trưng của phân bố!chỉ định số phần dư của phân bố được trả về: phân bố một phần dư = 1; phân bố hai phần dư = 2"}, "TINV": {"a": "(probability; deg_freedom)", "d": "Trả về giá trị đảo của phân bố t Student", "ad": "là xác suất gắn với phân bố t Student hai phần dư, một số trong khoảng từ 0 đến 1!là số nguyên dương chỉ thị số bậc tự do đặc trưng cho phân bố"}, "T.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "Tr<PERSON> về phân phối Student-t có phần dư bên trái", "ad": "là giá trị số để đánh giá phân phối!là số nguyên cho biết số bậc tự do đặc trưng cho phân phối!là giá trị lôgic: đối với hàm phân phối tí<PERSON> l<PERSON>, sử dụng TRUE; đối với hàm mật độ x<PERSON>c suất, sử dụng FALSE"}, "T.DIST.2T": {"a": "(x; deg_freedom)", "d": "Trả về phân bố t Student hai nhánh", "ad": "là giá trị số tại đó đánh giá phân bố!là một số nguyên chỉ ra số bậc tự do để phân loại phân bố"}, "T.DIST.RT": {"a": "(x; deg_freedom)", "d": "Trả về phân bố t Student nhánh phải", "ad": "là giá trị số tại đó đánh giá phân bố!là một số nguyên chỉ ra số bậc tự do để phân loại phân bố"}, "T.INV": {"a": "(probability; deg_freedom)", "d": "<PERSON><PERSON><PERSON> về nghịch đ<PERSON>o phần dư bên trái của phân phối Student t", "ad": "là xác suất liên quan đến phân phối Student t hai phần dư, một số từ 0 đến 1 và bao gồm cả 0 và 1!là số nguyên dương cho biết số bậc tự do đặc trưng cho phân phối"}, "T.INV.2T": {"a": "(probability; deg_freedom)", "d": "<PERSON><PERSON><PERSON> về nghịch đ<PERSON>o phần dư bên trái của phân phối Student t", "ad": "là xác suất liên quan đến phân phối Student t hai phần dư, một số từ 0 đến 1 và bao gồm cả 0 và 1!là số nguyên dương cho biết số bậc tự do đặc trưng cho phân phối"}, "T.TEST": {"a": "(array1; array2; tails; type)", "d": "<PERSON><PERSON><PERSON> về xác suất gắn với kiểm định t Student", "ad": "là tập dữ liệu đầu tiên!là tập hợp dữ liệu thứ hai!chỉ định số phần dư của phân bố được trả về: phân bố một phần dư = 1; phân bố hai phần dư = 2!là kiểu của kiểm định t: theo cặp = 1, phương sai ngang nhau cho hai mẫu (phương sai có điều kiện không đổi) = 2, phương sai không ngang nhau cho hai mẫu = 3"}, "TREND": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "<PERSON><PERSON><PERSON> về các số trong xu hướng tuyến tính phù hợp với các điểm dữ liệu, dùng phương pháp bình phương nhỏ nhất", "ad": "là một khoảng hoặc mảng các giá trị y xác định theo quan hệ y = mx + b!là một khoảng hoặc mảng tùy chọn các giá trị x xác định theo quan hệ y = mx + b, mảng có cùng kích cỡ như y-xác định!là một khoảng hoặc mảng các giá trị x mới cần cho hàm TREND trả về các giá trị y tương ứng!là giá trị lô gíc: hằng số b được tính toán bình thường nếu Hằng số=ĐÚNG hoặc không có; b đặt bằng 0 nếu Hằng số=SAI"}, "TRIMMEAN": {"a": "(array; percent)", "d": "Tr<PERSON> về trung bình phần bên trong của tập giá trị dữ liệu", "ad": "là khoảng hoặc mảng các giá trị cần thu gọn và tính trung bình!là phân đoạn các điểm dữ liệu cần loại trừ khỏi phần trên và dưới của tập dữ liệu"}, "TTEST": {"a": "(array1; array2; tails; type)", "d": "<PERSON><PERSON><PERSON> về xác suất gắn với kiểm tra t Student", "ad": "là tập dữ liệu đầu tiên!là tập dữ liệu thứ hai!chỉ định số phần dư của phân bố được trả về: phân bố một phần dư = 1; phân bố hai phần dư = 2!là kiểu của kiểm tra t: theo cặp = 1, phương sai ngang nhau cho hai mẫu (phương sai có điều kiện không đổi) = 2, phương sai không ngang nhau cho hai mẫu = 3"}, "VAR": {"a": "(number1; [number2]; ...)", "d": "Ước lượng phương sai dựa trên mẫu (không tính giá trị logic và văn bản trong mẫu)", "ad": "từ 1 đến 255 đối số dạng số tương ứng với mẫu tập toàn bộ"}, "VAR.P": {"a": "(number1; [number2]; ...)", "d": "Tính toán dung sai trên cơ sở tập toàn bộ (không tính các giá trị lô gíc và văn bản trong tập toàn bộ)", "ad": "từ 1 đến 255 tham đối dạng số tương ứng với tập toàn bộ"}, "VAR.S": {"a": "(number1; [number2]; ...)", "d": "Ước lượng phương sai dựa trên mẫu (không tính giá trị lô gíc và văn bản trong mẫu)", "ad": "từ 1 đến 255 tham đối dạng số tương ứng với mẫu tập toàn bộ"}, "VARA": {"a": "(value1; [value2]; ...)", "d": "Ước đoán độ lệch tiêu chuẩn dựa trên mẫu, t<PERSON>h cả giá trị lô gíc và văn bản. Văn bản và giá trị lô gíc SAI có giá trị 0; giá trị lô gíc ĐÚNG có giá trị 1", "ad": "từ 1 đến 255 giá trị tham đối tương ứng với một mẫu của tập toàn bộ"}, "VARP": {"a": "(number1; [number2]; ...)", "d": "T<PERSON>h toán dung sai trên cơ sở tập toàn bộ (không tính các giá trị logic và văn bản trong tập toàn bộ)", "ad": "từ 1 đến 255 tham đối dạng số tương ứng với tập toàn bộ"}, "VARPA": {"a": "(value1; [value2]; ...)", "d": "<PERSON><PERSON>h toán phương sai dựa trên tập toàn bộ, t<PERSON>h cả giá trị lô gíc và văn bản. V<PERSON>n bản và giá trị lô gíc SAI có giá trị 0; giá trị lô gíc ĐÚNG có giá trị 1", "ad": "từ 1 đến 255 giá trị tham đối tương <PERSON>ng với một tập toàn bộ"}, "WEIBULL": {"a": "(x; alpha; beta; cumulative)", "d": "<PERSON><PERSON><PERSON> về ph<PERSON> b<PERSON>", "ad": "là giá trị cần tính giá trị của hàm, một số không âm!là tham biến của phân bố, một số dương!là tham biến của phân bố, một số dương!là giá trị lô gíc: dành cho hàm phân bố tích lũy, dùng TRUE; hàm xác suất số đông, dùng FALSE"}, "WEIBULL.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "<PERSON><PERSON><PERSON> về ph<PERSON> b<PERSON>", "ad": "là giá trị cần tính giá trị của hàm, một số không âm!là tham biến của phân bố, một số dương!là tham biến của phân bố, một số dương!là giá trị lô gíc: dà<PERSON> cho hàm phân bố tích lũ<PERSON>, dùng ĐÚNG; hàm xác suất số đông, dùng SAI"}, "Z.TEST": {"a": "(array; x; [sigma])", "d": "<PERSON>r<PERSON> về giá trị P một phần dư của kiểm định z", "ad": "là mảng hoặc khoảng dữ liệu cần kiểm định X!là giá trị cần thử!là tập toàn bộ (đã biết) độ lệch tiêu chuẩn. <PERSON><PERSON><PERSON> có, độ lệch tiêu chuẩn mẫum sẽ được sử dụng"}, "ZTEST": {"a": "(array; x; [sigma])", "d": "Trả về giá trị P một phần dư của kiểm tra z", "ad": "là mảng hoặc khoảng dữ liệu cần kiểm tra X!là giá trị cần thử!là tập toàn bộ (đã biết) độ lệch tiêu chuẩn. <PERSON><PERSON><PERSON> c<PERSON>, độ lệch tiêu chuẩn mẫu sẽ được sử dụng"}, "ACCRINT": {"a": "(issue; first_interest; settlement; rate; par; frequency; [basis]; [calc_method])", "d": "Trả về lãi suất cộng dồn cho chứng khoán trả lãi định kỳ.", "ad": "là ngày phát hành chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tính lãi đầu tiên của chứ<PERSON> kho<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày thanh khoản chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là tỷ suất cu-pôn hàng năm của chứng khoán!là mệnh giá của chứng khoán!là số lần thanh toán cu-pôn hàng năm!là loại cơ sở tính ngày được dùng!là giá trị lô-gic: cộng dồn lợi nhuận từ ngày phát hành = TRUE hay bỏ qua; tính từ ngày trả cu-pôn cuối cùng = FALSE"}, "ACCRINTM": {"a": "(issue; settlement; rate; par; [basis])", "d": "Trả về lãi suất cộng dồn cho chứng khoán trả lãi khi tới hạn", "ad": "là ngày phát hành chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng kho<PERSON>, thể hiện dưới dạng số ngày tuần tự!là tỷ suất cu-pôn hàng năm của chứng khoán!là mệnh giá của chứng khoán!là loại cơ sở tính ngày được dùng"}, "AMORDEGRC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Trả về khấu hao theo tỷ lệ của tài nguyên theo mỗi chu kỳ kế toán.", "ad": "là giá vốn của tài nguyên!là ngày mua tài nguyên!là ngày kết thúc chu kỳ đầu tiên!là giá trị còn lại vào cuối vòng đời của tài nguyên.!là chu kỳ!là tỷ lệ khấu hao!year_basis : 0 cho năm có 360 ngày, 1 cho số ngày thực tế của năm, 3 cho năm có 365 ngày."}, "AMORLINC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Trả về khấu hao theo tỷ lệ của tài nguyên theo mỗi chu kỳ kế toán.", "ad": "là giá vốn của tài nguyên!là ngày mua tài nguyên!là ngày kết thúc chu kỳ đầu tiên!là giá trị còn lại vào cuối vòng đời của tài nguyên.!là chu kỳ!là tỷ lệ khấu hao!year_basis : 0 cho năm có 360 ngày, 1 cho số ngày thực tế của năm, 3 cho năm có 365 ngày."}, "COUPDAYBS": {"a": "(settlement; maturity; frequency; [basis])", "d": "Trả về số ngày từ đầu chu kỳ cu-pôn đến ngày thanh kho<PERSON>n", "ad": "là ngày thanh kho<PERSON><PERSON> chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng kho<PERSON>, thể hiện dưới dạng số ngày tuần tự!là số lần thanh toán cu-pôn hàng năm!là loại cơ sở tính ngày được dùng"}, "COUPDAYS": {"a": "(settlement; maturity; frequency; [basis])", "d": "<PERSON><PERSON><PERSON> về số ngày trong chu kỳ cu-pôn chứa ngày thanh k<PERSON>n", "ad": "là ngày thanh kho<PERSON><PERSON> chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng kho<PERSON>, thể hiện dưới dạng số ngày tuần tự!là số lần thanh toán cu-pôn hàng năm!là loại cơ sở tính ngày được dùng"}, "COUPDAYSNC": {"a": "(settlement; maturity; frequency; [basis])", "d": "<PERSON><PERSON><PERSON> về số ngày từ ngày thanh khoản đến ngày cu-pôn tiếp theo", "ad": "là ngày thanh kho<PERSON><PERSON> chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng kho<PERSON>, thể hiện dưới dạng số ngày tuần tự!là số lần thanh toán cu-pôn hàng năm!là loại cơ sở tính ngày được dùng"}, "COUPNCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "<PERSON><PERSON><PERSON> về ngày cu-pôn tiếp theo sau ngày thanh kho<PERSON>n chứng khoán", "ad": "là ngày thanh kho<PERSON><PERSON> chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng kho<PERSON>, thể hiện dưới dạng số ngày tuần tự!là số lần thanh toán cu-pôn hàng năm!là loại cơ sở tính ngày được dùng"}, "COUPNUM": {"a": "(settlement; maturity; frequency; [basis])", "d": "Trả về số lượng cu-pôn phải trả giữa ngày thanh khoản chứng khoán và ngày tới hạn", "ad": "là ngày thanh kho<PERSON><PERSON> chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng kho<PERSON>, thể hiện dưới dạng số ngày tuần tự!là số lần thanh toán cu-pôn hàng năm!là loại cơ sở tính ngày được dùng"}, "COUPPCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "Trả về ngày cu-pôn đã qua trước ngày thanh k<PERSON>n", "ad": "là ngày thanh kho<PERSON><PERSON> chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng kho<PERSON>, thể hiện dưới dạng số ngày tuần tự!là số lần thanh toán cu-pôn hàng năm!là loại cơ sở tính ngày được dùng"}, "CUMIPMT": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Tr<PERSON> lại tiền trả lợi nhuận cộng dồn giữa hai chu kỳ", "ad": "là lãi suất!là tổng số chu kỳ thanh toán!là giá trị hiện thời!là chu kỳ đầu trong tính toán!là chu kỳ cuối trong tính toán!là khoảng thời gian thanh toán"}, "CUMPRINC": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Tr<PERSON> lại tiền gốc cộng dồn phải trả cho khoản vay giữa hai chu kỳ", "ad": "là lãi suất!là tổng số chu kỳ thanh toán!là giá trị hiện thời!là chu kỳ đầu trong tính toán!là chu kỳ cuối trong tính toán!là khoảng thời gian thanh toán"}, "DB": {"a": "(cost; salvage; life; period; [month])", "d": "<PERSON><PERSON><PERSON> về khấu hao của tài sản cho một chu kỳ xác định sử dụng phương pháp giảm dần cố định", "ad": "là chi phí ban đầu của tài sản!là giá trị còn lại vào cuối vòng đời của tài sản!là số chu kỳ khấu hao của tài sản (đôi khi được gọi là vòng đời của tài sản)!là chu kỳ cần tính khấu hao. Chu kỳ phải dùng cùng đơn vị tính như Vòng đời!là số tháng trong năm đầu tiên. Nế<PERSON> không có, tháng được coi bằng 12"}, "DDB": {"a": "(cost; salvage; life; period; [factor])", "d": "<PERSON><PERSON><PERSON> về khấu hao của tài sản cho một chu kỳ xác định dùng phương pháp giảm dần kép hoặc một số phương pháp chỉ định khác", "ad": "là chi phí ban đầu của tài sản!là giá trị còn lại vào cuối vòng đời của tài sản!là số chu kỳ khấu hao của tài sản (đôi khi được gọi là vòng đời của tài sản)!là chu kỳ cần tính khấu hao. Chu kỳ phải dùng cùng đơn vị tính như Vòng đời!là tốc độ giảm dần. Nếu không có <PERSON> số, nó được gán bằng 2 (phương pháp giảm dần kép)"}, "DISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "<PERSON><PERSON><PERSON> về hệ số quy đổi của chứ<PERSON> k<PERSON>", "ad": "là ngày thanh khoản chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng khoán, thể hiện dưới dạng số ngày tuần tự!là giá của chứng khoán trên $100 mệnh giá!là giá trị hoàn lại chứng khoán trên $100 mệnh giá!là loại cơ sở tính ngày được dùng"}, "DOLLARDE": {"a": "(fractional_dollar; fraction)", "d": "Chuyển đổi giá đô-la, thể hiện như phân số, sang giá đô-la, thể hiện như số thập phân", "ad": "là số thể hiện như phân số!là số nguyên dùng trong mẫu số của phân số"}, "DOLLARFR": {"a": "(decimal_dollar; fraction)", "d": "<PERSON>y<PERSON>n đổi giá đô-la, thể hiện như số thập phân, sang giá đô-la, thể hiện như phân số", "ad": "là số thập phân!là số nguyên dùng trong mẫu số của phân số"}, "DURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "Tr<PERSON> về thời đoạn hàng năm của chứng khoán trả lãi định kỳ", "ad": "là ngày thanh khoản chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng khoán, thể hiện dưới dạng số ngày tuần tự!là tỷ suất cu-pôn hàng năm của chứng khoán!là lợi nhuận hàng năm của chứng khoán!là số lần thanh toán cu-pôn hàng năm!là loại cơ sở tính ngày được dùng"}, "EFFECT": {"a": "(nominal_rate; npery)", "d": "<PERSON>r<PERSON> lại tỷ suất lợi nhuận có hiệu lực hàng năm", "ad": "là tỷ suất lợi nhuận danh nghĩa!là số chu kỳ tổng gộp hàng năm"}, "FV": {"a": "(rate; nper; pmt; [pv]; [type])", "d": "Tr<PERSON> về giá trị tương lai của khoản đầu tư trên cơ sở các khoản thanh toán, lãi suất không đổi theo chu kỳ", "ad": "là lãi suất theo chu kỳ. <PERSON><PERSON> dụ, khoản thanh toán hàng quý là 6% APR sẽ dùng 6%/4!là tổng số chu kỳ thanh toán trong khoản đầu tư!là khoản thanh toán cho mỗi chu kỳ và không thay đổi trong suốt thời gian đầu tư!là giá trị hiện thời, hoặc lượng tiền cả gói có tính đến một chuỗi các khoản thanh toán tương lai. Nếu không có, Pv = 0!là giá trị thể hiện thời điểm thanh toán:=1 nếu là khoản thanh toán đầu kỳ; =0 hoặc không có nếu là khoản thanh toán cuối kỳ"}, "FVSCHEDULE": {"a": "(principal; schedule)", "d": "Tr<PERSON> lại giá trị tương lai của vốn ban đầu sau khi áp dụng chuỗi tỷ suất lợi nhuận tổng gộp", "ad": "là giá trị hiện thời!là mảng tỷ suất lợi nhuận để áp dụng"}, "INTRATE": {"a": "(settlement; maturity; investment; redemption; [basis])", "d": "Tr<PERSON> về lãi suất cho chứng khoán đầu tư đầy đủ", "ad": "là ngày thanh kho<PERSON><PERSON> chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng khoán, thể hiện dưới dạng số ngày tuần tự!là số tiền đầu tư vào chứng khoán!là số tiền sẽ nhận được lúc tới hạn!là loại cơ sở tính ngày được dùng"}, "IPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Trả về lãi suất thanh toán cho một chu kỳ định trước của một khoản đầu tư trên cơ sở thanh toán không đổi và lãi suất không đổi theo chu kỳ", "ad": "là lãi suất theo chu kỳ. <PERSON><PERSON> dụ, khoản thanh toán hàng quý là 6% APR sẽ dùng 6%/4!là chu kỳ cần xác định lãi và phải nằm trong khoảng từ 1 đến SốChukỳ!là tổng số chu kỳ thanh toán cho một khoản đầu tư!là giá trị hiện thời, hoặc lượng tiền cả gói có tính đến một chuỗi các khoản thanh toán tương lai!là giá trị tương lai, hoặc số dư tiền mặt cần thu được sau khi thực hiện khoản thanh toán cuối cùng. Nếu không có, Fv=0!là giá trị thể hiện thời điểm thanh toán:=0 hoặc không có nếu là khoản thanh toán cuối kỳ; =1 nếu là khoản thanh toán đầu kỳ"}, "IRR": {"a": "(values; [guess])", "d": "Tr<PERSON> về tỷ lệ thu hồi nội tại của một chuỗi các lưu chuyển tiền tệ", "ad": "là một mảng hoặc tham chiếu tới ô chứa các số cần tính tỷ lệ thu hồi nội tại!là số đoán trước sát với kết quả của tỷ lệ thu hồi nội tại; 0.1 (10 phần trăm) nếu không có"}, "ISPMT": {"a": "(rate; per; nper; pv)", "d": "Trả về lãi phải trả trong chu kỳ xác định của khoản đầu tư", "ad": "lãi suất theo chu kỳ. <PERSON><PERSON> d<PERSON>, khoản thanh toán hàng quý là 6% APR sẽ dùng 6%/4!chu kỳ cần tìm lãi suất!số chu kỳ thanh toán của khoản đầu tư!lượng tiền cả gói có tính ngay một chuỗi các khoản thanh toán tương lai"}, "MDURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "<PERSON><PERSON><PERSON> về thời đoạn sửa đổi theo <PERSON> cho chứng khoán với mệnh giá giả định là $100", "ad": "là ngày thanh khoản chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng khoán, thể hiện dưới dạng số ngày tuần tự!là tỷ suất cu-pôn hàng năm của chứng khoán!là thu hồi hàng năm của chứng khoán!là số lần thanh toán cu-pôn hàng năm!là loại cơ sở tính ngày được dùng"}, "MIRR": {"a": "(values; finance_rate; reinvest_rate)", "d": "Tr<PERSON> về tỷ lệ thu hồi nội tại của một chuỗi các lưu chuyển tiền tệ định kỳ, có tính đến cả chi phí đầu tư và lợi tức của việc tái đầu tư tiền mặt", "ad": "là một mảng hoặc tham chiếu tới ô thể hiện một chuỗi các khoản thanh toán (số âm) và thu nhập (số dương) trong các chu kỳ đều nhau!là lãi suất phải trả cho khoản tiền sử dụng trong lưu chuyển tiền tệ!là lãi suất nhận được trong lưu chuyển tiền tệ do tái đầu tư chúng"}, "NOMINAL": {"a": "(effect_rate; npery)", "d": "<PERSON><PERSON><PERSON> lại tỷ suất lợi nhuận danh nghĩa hàng năm", "ad": "là tỷ suất lợi nhuận có hiệu lực!là số chu kỳ tổng gộp hàng năm"}, "NPER": {"a": "(rate; pmt; pv; [fv]; [type])", "d": "Tr<PERSON> về số chu kỳ của khoản đầu tư trên cơ sở các khoản thanh toán, lãi suất không đổi theo chu kỳ", "ad": "là lãi suất theo chu kỳ. <PERSON><PERSON> d<PERSON>, khoản thanh toán hàng quý là 6% APR sẽ dùng 6%/4!là khoản thanh toán cho mỗi chu kỳ và không thay đổi trong suốt thời gian đầu tư!là giá trị hiện thời, hoặc lượng tiền cả gói có tính đến một chuỗi các khoản thanh toán tương lai!là giá trị tương lai, hoặc số dư tiền mặt cần thu được sau khi thực hiện khoản thanh toán cuối cùng. Nếu không có, sẽ bằng 0!là một giá trị logíc: = 1 nếu trả vào đầu kỳ ; = 0 hoặc được bỏ qua nếu trả vào cuối kỳ"}, "NPV": {"a": "(rate; value1; [value2]; ...)", "d": "Tr<PERSON> lại giá trị thực hiện tại của khoản đầu tư dựa trên tỷ lệ khấu trừ và chuỗi các khoản thanh toán (gi<PERSON> trị âm) và thu nhập (gi<PERSON> trị dươ<PERSON>) tương lai", "ad": "là tỷ lệ khấu trừ qua suốt một chu kỳ!từ 1 đến 254 khoản thanh toán và thu nh<PERSON>p, theo thời khoảng đều nhau và thực hiện vào cuối mỗi chu kỳ"}, "ODDFPRICE": {"a": "(settlement; maturity; issue; first_coupon; rate; yld; redemption; frequency; [basis])", "d": "Trả về giá trên $100 mệnh giá của chứng khoán với một chu kỳ đầu lẻ", "ad": "là ngày thanh kho<PERSON><PERSON> chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứ<PERSON> kho<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày phát hành chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày cu-pôn đầu của chứng khoán, thể hiện dưới dạng số ngày tuần tự!là tỷ suất lợi nhuận của chứng khoán!là thu hồi hàng năm của chứng khoán!là giá trị hoàn lại của chứng khoán trên $100 mệnh giá!là số lần thanh toán cu-pôn hàng năm!là loại cơ sở tính ngày được dùng"}, "ODDFYIELD": {"a": "(settlement; maturity; issue; first_coupon; rate; pr; redemption; frequency; [basis])", "d": "<PERSON><PERSON><PERSON> về lợi nhuận của chứng khoán với một chu kỳ đầu lẻ", "ad": "là ngày thanh kho<PERSON><PERSON> chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứ<PERSON> kho<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày phát hành chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày cu-pôn đầu của chứng khoán, thể hiện dưới dạng số ngày tuần tự!là tỷ suất lợi nhuận của chứng khoán!là giá của chứng khoán!là giá trị hoàn lại của chứng khoán trên $100 mệnh giá!là số lần thanh toán cu-pôn hàng năm!là loại cơ sở tính ngày được dùng"}, "ODDLPRICE": {"a": "(settlement; maturity; last_interest; rate; yld; redemption; frequency; [basis])", "d": "Trả về giá trên $100 mệnh giá của chứng khoán với một chu kỳ cuối lẻ", "ad": "là ngày thanh khoản chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứ<PERSON> kho<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày cu-pôn cuối của chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là tỷ suất lợi nhuận của chứng khoán!là thu hồi hàng năm của chứng khoán!là giá trị hoàn lại của chứng khoán trên $100 mệnh giá!là số lần thanh toán cu-pôn hàng năm!là loại cơ sở tính ngày được dùng"}, "ODDLYIELD": {"a": "(settlement; maturity; last_interest; rate; pr; redemption; frequency; [basis])", "d": "<PERSON><PERSON><PERSON> về lợi nhuận của chứng khoán với một chu kỳ cuối lẻ", "ad": "là ngày thanh kho<PERSON><PERSON> chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng khoán, thể hiện dưới dạng số ngày tuần tự!là ngày cu-pôn cuối của chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là tỷ suất lợi nhuận của chứng khoán!là giá của chứng khoán!là giá trị hoàn lại của chứng khoán trên $100 mệnh giá!là số lần thanh toán cu-pôn hàng năm!là loại cơ sở tính ngày được dùng"}, "PDURATION": {"a": "(rate; pv; fv)", "d": "Trả về số lượng chu kỳ để khoản đầu tư đạt đến giá trị được chỉ định", "ad": "là lãi suất của mỗi chu kỳ.!là giá trị hiện tại của khoản đầu tư!là giá trị tương lai mong muốn của khoản đầu tư"}, "PMT": {"a": "(rate; nper; pv; [fv]; [type])", "d": "<PERSON><PERSON>h toán phần phải thanh toán cho khoản vay trên cơ sở các khoản thanh toán, lãi suất không đổi", "ad": "là lãi suất theo chu kỳ của khoản vay. <PERSON><PERSON>, khoản thanh toán hàng quý là 6% APR sẽ dùng 6%/4!là tổng số phần phải thanh toán cho khoản vay!là giá trị hiện thời: có tính luôn tổng của một chuỗi các khoản thanh toán tương lai!là giá trị tương lai, hoặc số dư tiền mặt cần thu được sau khi thực hiện khoản thanh toán cuối cùng. Nếu không có, sẽ bằng 0!là một giá trị logíc: = 1 nếu trả vào đầu kỳ ; = 0 hoặc được bỏ qua nếu trả vào cuối kỳ"}, "PPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Tr<PERSON> về khoản thanh toán vốn của một khoản đầu tư trên cơ sở thanh toán không đổi và lãi suất không đổi theo chu kỳ", "ad": "là lãi suất theo chu kỳ. <PERSON><PERSON> dụ, khoản thanh toán hàng quý là 6% APR sẽ dùng 6%/4!Chỉ định chu kỳ và phải nằm trong khoảng từ 1 tới SốChukỳ!là tổng số chu kỳ thanh toàn cho một khoản đầu tư!Trả về giá trị hiện thời: có tính luôn tổng của một chuỗi các khoản thanh toán tương lai!là giá trị tương lai, hoặc số dư tiền mặt cần thu được sau khi thực hiện khoản thanh toán cuối cùng!là giá trị lô gíc:=1 nếu là khoản thanh toán đầu kỳ; =0 hoặc không có nếu là khoản thanh toán cuối kỳ"}, "PRICE": {"a": "(settlement; maturity; rate; yld; redemption; frequency; [basis])", "d": "Trả về giá trên $100 mệnh giá cho chứng khoán trả lãi định kỳ", "ad": "là ngày thanh khoản chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng khoán, thể hiện dưới dạng số ngày tuần tự!là tỷ suất cu-pôn hàng năm của chứng khoán!là thu hồi hàng năm của chứng khoán!là giá trị hoàn lại của chứng khoán trên $100 mệnh giá!là số lần thanh toán cu-pôn hàng năm!là loại cơ sở tính ngày được dùng"}, "PRICEDISC": {"a": "(settlement; maturity; discount; redemption; [basis])", "d": "Trả về giá trên $100 mệnh giá của chứng khoán giảm giá", "ad": "là ngày thanh khoản chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng khoán, thể hiện dưới dạng số ngày tuần tự!là tỷ suất giảm giá chứng khoán!là giá trị chuộc chứng khoán trên $100 mệnh giá!là loại cơ sở tính ngày được dùng"}, "PRICEMAT": {"a": "(settlement; maturity; issue; rate; yld; [basis])", "d": "Trả về giá cho mỗi $100 mệnh giá chứng khoán trả lãi lúc tới hạn", "ad": "là ngày thanh toán của chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là kỳ hạn của chứng kho<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày phát hành chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là tỷ lệ lợi nhuận của chứng khoán vào ngày phát hành!là lợi tức hàng năm của chứng khoán!là loại cơ sở tính ngày đượ<PERSON> sử dụng"}, "PV": {"a": "(rate; nper; pmt; [fv]; [type])", "d": "Tr<PERSON> về giá trị hiện thời của khoản đầu tư: có tính luôn tổng của một chuỗi các khoản thanh toán tương lai", "ad": "là lãi suất theo chu kỳ. <PERSON><PERSON> d<PERSON>, khoản thanh toán hàng quý là 6% APR sẽ dùng 6%/4!là tổng số chu kỳ thanh toán trong khoản đầu tư!là khoản thanh toán cho mỗi chu kỳ và không thay đổi trong suốt thời gian đầu tư!là giá trị tương lai, hoặc số dư tiền mặt cần thu được sau khi thực hiện khoản thanh toán cuối cùng!là giá trị lô gíc: =1 nếu là khoản thanh toán đầu kỳ; =0 hoặc không có nếu là khoản thanh toán cuối kỳ"}, "RATE": {"a": "(nper; pmt; pv; [fv]; [type]; [guess])", "d": "là lãi suất theo chu kỳ của khoản vay hoặc khoản đầu tư. <PERSON><PERSON> dụ, khoản thanh toán hàng quý là 6% APR sẽ dùng 6%/4", "ad": "là tổng số chu kỳ thanh toán cho khoản vay hoặc khoản đầu tư!là khoản thanh toán cho mỗi chu kỳ và không thay đổi trong suốt thời gian cho vay hoặc đầu tư!là giá trị hiện thời: có tính luôn tổng của một chuỗi các khoản thanh toán tương lai!là giá trị tương lai, hoặc số dư tiền mặt cần thu được sau khi thực hiện khoản thanh toán cuối cùng. Nếu không có, dùng Fv = 0!là giá trị lô gíc: =1 nếu là khoản thanh toán đầu kỳ; =0 hoặc không có nếu là khoản thanh toán cuối kỳ!là tỷ lệ được đoán trước; nế<PERSON> không có, <PERSON><PERSON><PERSON> trước= 0.1 (10 phần trăm)"}, "RECEIVED": {"a": "(settlement; maturity; investment; discount; [basis])", "d": "T<PERSON><PERSON> về số tiền sẽ nhận được lúc tới hạn cho chứng khoán đầu tư đầy đủ", "ad": "là ngày thanh khoản chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng khoán, thể hiện dưới dạng số ngày tuần tự!là số tiền đầu tư vào chứng khoán!là tỷ suất giảm giá của chứng khoán!là loại cơ sở tính ngày được dùng"}, "RRI": {"a": "(nper; pv; fv)", "d": "Trả về lãi suất tương ứng cho sự tăng trưởng của khoản đầu tư", "ad": "là số lượng chu kỳ của khoản đầu tư!là giá trị hiện tại của khoản đầu tư!là giá trị tương lai của khoản đầu tư"}, "SLN": {"a": "(cost; salvage; life)", "d": "Trả về khấu hao đều của tài sản cho một chu kỳ", "ad": "là chi phí ban đầu của tài sản!là giá trị còn lại ở cuối vòng đời tài sản!là số chu kỳ khấu hao của tài sản (đôi khi được gọi là vòng đời của tài sản)"}, "SYD": {"a": "(cost; salvage; life; per)", "d": "Trả về số khấu hao tổng cả năm của tài sản cho một chu kỳ xác đ<PERSON>nh", "ad": "là chi phí ban đầu của tài sản!là giá trị còn lại ở cuối vòng đời tài sản!là số chu kỳ khấu hao của tài sản (đôi khi được gọi là vòng đời của tài sản)!là chu kỳ và phải dùng cùng đơn vị tính như Vòng đời"}, "TBILLEQ": {"a": "(settlement; maturity; discount)", "d": "<PERSON><PERSON><PERSON> lại thu hồi trái phiếu đổi ngang cho trái phiếu kho bạc", "ad": "là ngày thanh khoản trái phiếu kho bạc, thể hiện như số ngày tuần tự!là ngày tới hạn trái phiếu kho bạc, thể hiện như số ngày tuần tự!là tỷ suất giảm giá trái phiếu kho bạc"}, "TBILLPRICE": {"a": "(settlement; maturity; discount)", "d": "Trả lại giá trên $100 mệnh giá cho trái phiếu kho bạc", "ad": "là ngày thanh khoản trái phiếu kho bạc, thể hiện như số ngày tuần tự!là ngày tới hạn trái phiếu kho bạc, thể hiện như số ngày tuần tự!là tỷ suất giảm giá trái phiếu kho bạc"}, "TBILLYIELD": {"a": "(settlement; maturity; pr)", "d": "<PERSON><PERSON><PERSON> lại thu hồi cho trái phiếu kho bạc", "ad": "là ngày thanh khoản trái phiếu kho bạc, thể hiện như số ngày tuần tự!là ngày tới hạn trái phiếu kho bạc, thể hiện như số ngày tuần tự!là giá trái phiếu kho bạc trên $100 mệnh giá"}, "VDB": {"a": "(cost; salvage; life; start_period; end_period; [factor]; [no_switch])", "d": "T<PERSON><PERSON> về khấu hao của tài sản cho bất kỳ chu kỳ được chỉ định, t<PERSON><PERSON> cả một phần chu kỳ, sử dụng phương pháp giảm dần kép hoặc một số phương pháp xác định khác", "ad": "là chi phí ban đầu của tài sản!là giá trị còn lại vào cuối vòng đời của tài sản!là số chu kỳ khấu hao của tài sản (đôi khi được gọi là vòng đời của tài sản)!là chu kỳ bắt đầu tính khấu hao, và phải dùng cùng đơn vị tính như Vòng đời!là chu kỳ cuối cùng tính khấu hao, và phải dùng cùng đơn vị tính như Vòng đời!là tốc độ giảm dần, nếu không có được gán bằng 2 (phương pháp giảm dần kép)!chuy<PERSON><PERSON> sang khấu hao đều khi khấu hao lớn hơn khấu hao giảm dần nếu = SAI hoặc không có; không chuyển nếu = ĐÚNG"}, "XIRR": {"a": "(values; dates; [guess])", "d": "Tr<PERSON> về tỷ lệ thu hồi nội bộ của lịch trình lưu chuyển tiền tệ", "ad": "là chuỗi lưu chuyển tiền tệ tương ứng với lịch trình thanh toán theo ngày!là lịch trình ngày thanh toán tương ứng với thanh toán lưu chuyển tiền tệ!là số dự đoán sát với kết quả XIRR"}, "XNPV": {"a": "(rate; values; dates)", "d": "<PERSON>r<PERSON> về giá trị thực hiện tại cho lịch trình lưu chuyển tiền tệ", "ad": "là tỷ lệ mất giá áp dụng cho lưu chuyển tiền tệ!là chuỗi lưu chuyển tiền tệ tương ứng với lịch trình thanh toán theo ngày!là lịch trình ngày thanh toán tương ứng với thanh toán lưu chuyển tiền tệ"}, "YIELD": {"a": "(settlement; maturity; rate; pr; redemption; frequency; [basis])", "d": "Tr<PERSON> về lợi nhuận cho chứng khoán trả lãi định kỳ", "ad": "là ngày thanh khoản chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng kho<PERSON>, thể hiện dưới dạng số ngày tuần tự!là tỷ suất cu-pôn hàng năm của chứng khoán!là giá chứng khoán trên $100 mệnh giá!là giá trị hoàn lại của chứng khoán trên $100 mệnh giá!là số lần thanh toán cu-pôn hàng năm!là loại cơ sở tính ngày được dùng"}, "YIELDDISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "Tr<PERSON> về lợi nhuận hàng năm cho chứng khoán giảm giá. <PERSON><PERSON> dụ, tr<PERSON>i phiếu kho bạc", "ad": "là ngày thanh khoản chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng khoán, thể hiện dưới dạng số ngày tuần tự!là giá của chứng khoán trên $100 mệnh giá!là giá trị hoàn lại chứng khoán trên $100 mệnh giá!là loại cơ sở tính ngày được dùng"}, "YIELDMAT": {"a": "(settlement; maturity; issue; rate; pr; [basis])", "d": "Tr<PERSON> về lợi nhuận hàng năm của chứng khoán trả lãi lúc tới hạn", "ad": "là ngày thanh kho<PERSON><PERSON> chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là ngày tới hạn chứng khoán, thể hiện dưới dạng số ngày tuần tự!là ngày phát hành chứ<PERSON>, thể hiện dưới dạng số ngày tuần tự!là tỷ suất lợi tức của chứng khoán ở lúc phát hành!là giá của chứng khoán trên $100 mệnh giá!là loại cơ sở tính ngày được dùng"}, "ABS": {"a": "(number)", "d": "Tr<PERSON> về giá trị tuyệt đối một số, giá trị số không dấu", "ad": "là phần số thực cần lấy giá trị tuyệt đối"}, "ACOS": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về ArcCosin của một số, theo radian trong khoảng từ 0 đến Pi. ArcCosin là góc có <PERSON> bằng Số", "ad": "<PERSON><PERSON> của góc mong muốn và phải từ -1 đến 1"}, "ACOSH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về Cosin hi-péc-bôn đ<PERSON><PERSON> của một số", "ad": "là số thực bất kỳ lớn hơn hoặc bằng 1"}, "ACOT": {"a": "(number)", "d": "Trả về hàm arccotang của con số, theo đơn vị đo góc từ 0 đến Pi.", "ad": "là hàm cotang của góc bạn muốn"}, "ACOTH": {"a": "(number)", "d": "Trả về hàm cotang hyperbol ngược của con số", "ad": "là hàm cotang hyperbol của góc bạn muốn"}, "AGGREGATE": {"a": "(function_num; options; ref1; ...)", "d": "<PERSON><PERSON><PERSON> về tập hợp trong danh sách hoặc cơ sở dữ liệu", "ad": "là số từ 1 đến 19 chỉ định hàm tóm tắt cho tập hợp.!là số từ 0 đến 7 chỉ định giá trị để bỏ qua cho tập hợp!là mảng hoặc vùng dữ liệu số để tính toán tập hợp!cho biết vị trí trong mảng; là giá trị lớn nhất thứ k, giá trị nhỏ nhất thứ k, phân vị thứ k hay tứ phân vị thứ k.!là số từ 1 đến 19 chỉ định hàm tóm tắt cho tập hợp.!là số từ 0 đến 7 chỉ định giá trị để bỏ qua cho tập hợp!là 1 đến 253 vùng hoặc tham chiếu cần tính tập hợp"}, "ARABIC": {"a": "(text)", "d": "Chuyển đổi chữ số La Ma<PERSON> sang chữ số Ả Rập", "ad": "là chữ số La Mã bạn muốn chuyển đổi"}, "ASC": {"a": "(text)", "d": "<PERSON><PERSON><PERSON> với những ngôn ngữ dùng bộ ký tự byte kép (DBCS), hàm này gi<PERSON><PERSON> thay đổi các ký tự có độ rộng toàn phần (byte kép) thành các ký tự có độ rộng b<PERSON> phần (byte đơn)", "ad": "<PERSON><PERSON>n bản hoặc tham chiếu tới một ô có chứa văn bản mà bạn muốn thay đổi. "}, "ASIN": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về Arc<PERSON> của một số theo radian, trong kho<PERSON>ng từ -Pi/2 đến Pi/2", "ad": "là Sin của góc mong muốn và phải từ -1 đến 1"}, "ASINH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về Sin hi-péc-bôn đ<PERSON><PERSON> của một số", "ad": "là số thực bất kỳ lớn hơn hoặc bằng 1"}, "ATAN": {"a": "(number)", "d": "Trở về Arc<PERSON>ang của một số theo <PERSON>, trong kho<PERSON>ng từ -Pi/2 đến Pi/2", "ad": "l<PERSON> <PERSON> của góc mong muốn"}, "ATAN2": {"a": "(x_num; y_num)", "d": "Tr<PERSON> về ArcTang của cặp toạ độ x và y, theo radian trong khoảng từ -<PERSON> đến Pi, kh<PERSON>ng tính -Pi", "ad": "là toạ độ x của điểm!là toạ độ y của điểm"}, "ATANH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về Tang hi-péc-bôn đ<PERSON><PERSON> của một số", "ad": "là số thực b<PERSON><PERSON> kỳ trong khoảng từ -1 đến 1 ngoại trừ -1 và 1"}, "BASE": {"a": "(number; radix; [min_length])", "d": "Chuyển một con số thành biểu diễn bằng văn bản với cơ số (gốc) cho trước", "ad": "là con số bạn muốn chuyển!là Cơ số gốc bạn muốn chuyển con số về!là chiều dài tối thiểu của xâu trả về. Nếu bỏ qua thì các số 0 ở đâu sẽ không được thêm vào"}, "CEILING": {"a": "(number; significance)", "d": "Làm tròn số lên, tới số nguyên hoặc bội gần nhất của số có nghĩa", "ad": "là giá trị cần làm tròn!là số có bội được làm tròn tới"}, "CEILING.MATH": {"a": "(number; [significance]; [mode])", "d": "Làm tròn số lên tới số nguyên gần nhất hoặc lên bội số có nghĩa gần nhất", "ad": "là giá trị bạn muốn làm tròn!là bội số bạn muốn làm tròn lên!nếu được cung cấp và không bằng không thì hàm này sẽ làm tròn về xa con số không"}, "CEILING.PRECISE": {"a": "(number; [significance])", "d": "Tr<PERSON> về một số được làm tròn lên tới số nguyên gần nhất hoặc tới bội số có nghĩa gần nhất", "ad": "là giá trị cần làm tròn!là số có bội được làm tròn tới"}, "COMBIN": {"a": "(number; number_chosen)", "d": "Trả về số tổ hợp của một số phần tử cho trước", "ad": "là tổng số phần tử!là số phần tử trong mỗi tổ hợp"}, "COMBINA": {"a": "(number; number_chosen)", "d": "Trả về số lượng tổ hợp có lặp lại của một số lượng khoản mục cho trước", "ad": "là tổng số lượng các khoản mục!là số lượng khoản mục của mỗi tổ hợp"}, "COS": {"a": "(number)", "d": "Trả về Cosin của góc", "ad": "là giá trị Cosin của góc theo <PERSON>"}, "COSH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về Cosin hi-péc-bôn của một số", "ad": "là số thực b<PERSON><PERSON> k<PERSON>"}, "COT": {"a": "(number)", "d": "Trả về hàm cotang của một góc", "ad": "là số đo góc theo rađian mà bạn muốn tìm cotang"}, "COTH": {"a": "(number)", "d": "Trả về hàm cotang hyperbol của một con số", "ad": "là số đo góc theo rađian mà bạn muốn tìm cotang hyperbol"}, "CSC": {"a": "(number)", "d": "Trả về hàm cosec của một góc", "ad": "là số đo góc theo rađian mà bạn muốn tìm cosec"}, "CSCH": {"a": "(number)", "d": "Trả về hàm cosec hyperbol của một góc", "ad": "là số đo góc theo rađian mà bạn muốn tìm cosec hyperbol"}, "DECIMAL": {"a": "(number; radix)", "d": "Chuyển dạng biểu diễn bằng văn bản của một con số với cơ số cho trước sang con số ở hệ thập phân", "ad": "là con số bạn muốn chuyển!là cơ số gốc của con số bạn đang chuyển"}, "DEGREES": {"a": "(angle)", "d": "Chuyển đổi từ radian sang độ", "ad": "là g<PERSON><PERSON> t<PERSON>h theo radian cần chuyển đổi"}, "ECMA.CEILING": {"a": "(number; significance)", "d": "Làm tròn số lên, tới số nguyên hoặc bội gần nhất của số có nghĩa", "ad": "là giá trị cần làm tròn!là số có bội được làm tròn tới"}, "EVEN": {"a": "(number)", "d": "Làm tròn số dương lên và số âm xuống số nguyên chẵn gần nhất", "ad": "là giá trị cần làm tròn"}, "EXP": {"a": "(number)", "d": "Trả về giá trị e mũ số chỉ định", "ad": "là số mũ của e. Hằng số e là cơ số lô-ga-r<PERSON><PERSON> tự nhiên, bằng 2.71828182845904"}, "FACT": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về giai thừa của một số, bằng 1*2*3*...* Số", "ad": "là một số không âm cần tính giai thừa"}, "FACTDOUBLE": {"a": "(number)", "d": "<PERSON><PERSON>ả về giai thừa kép của một số", "ad": "là giá trị để trả về giai thừa kép"}, "FLOOR": {"a": "(number; significance)", "d": " làm tròn number xuống bội số gần nhất của significance", "ad": "is giá trị số bạn muốn round!is là bội số của cái bạn muốn làm tròn. Number và Signification cả hai phải cùng dương hay cùng âm"}, "FLOOR.PRECISE": {"a": "(number; [significance])", "d": "Tr<PERSON> về một số được làm tròn xuống tới số nguyên gần nhất hoặc tới bội số có nghĩa gần nhất", "ad": "là giá trị cần làm tròn!là số có bội được làm tròn tới"}, "FLOOR.MATH": {"a": "(number; [significance]; [mode])", "d": "Làm tròn số xuống về số nguyên gần nhất hoặc về bội số có nghĩa gần nhất", "ad": "là giá trị bạn muốn làm tròn!là bội số bạn muốn làm tròn về!nếu được cung cấp và không bằng không thì hàm này sẽ làm tròn về phía con số không"}, "GCD": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON>ả lại ước số chung lớn nhất", "ad": "là các giá trị 1 tới 255"}, "INT": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> tròn số xuống giá trị nguyên gần nhất", "ad": "là phần số thực cần làm tròn số xuống giá trị nguyên"}, "ISO.CEILING": {"a": "(number; [significance])", "d": "Tr<PERSON> về một số được làm tròn lên tới số nguyên gần nhất hoặc tới bội số có nghĩa gần nhất. Bất chấp dấu của số, số sẽ được làm tròn lên. <PERSON><PERSON>, nếu đối số số hoặc đối số số có nghĩa là không, thì kết quả là không.", "ad": "là giá trị cần làm tròn!là số có bội được làm tròn tới"}, "LCM": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON> lại mẫu số chung nhỏ nhất", "ad": "là các giá trị 1 tới 255 mà bạn muốn có mẫu số chung nhỏ nhất"}, "LN": {"a": "(number)", "d": "Tr<PERSON> về lô-ga-rít tự nhiên của một số", "ad": "là phần số thực dư<PERSON>ng cần lấy lô-ga-rít"}, "LOG": {"a": "(number; [base])", "d": "trả về lô-ga-rít của một số theo cơ số chỉ định", "ad": "là phần số thực dương cần tính lô-ga-rít!là cơ số của lô-ga-rít; 10 nếu không có"}, "LOG10": {"a": "(number)", "d": "Trả về lô-ga-r<PERSON>t cơ số 10 của một số", "ad": "là phần số thực dư<PERSON>ng cần l<PERSON>y lô-ga-r<PERSON><PERSON> c<PERSON> số 10"}, "MDETERM": {"a": "(array)", "d": "<PERSON>r<PERSON> về ma trận xác đ<PERSON>nh mảng", "ad": "là một mảng số có cùng số lượng hàng và cột, của một khoảng các ô hoặc một hằng số mảng"}, "MINVERSE": {"a": "(array)", "d": "Trả về ma trận đảo của ma trận trong mảng", "ad": "là một mảng số có cùng số lượng hàng và cột, của một khoảng các ô hoặc một hằng số mảng"}, "MMULT": {"a": "(array1; array2)", "d": "Tr<PERSON> về ma trận tích của hai mảng, là một mảng có cùng số hàng như Mảng1 và số cột như Mảng2", "ad": "là mảng các số đầu tiên được nhân và phải có số cột bằng với số hàng của Mảng2"}, "MOD": {"a": "(number; divisor)", "d": "<PERSON><PERSON><PERSON> về phần dư của một số sau khi bị chia", "ad": "là số cần tìm phần dư sau khi thực hiện phép chia!là số cần chia cho Số"}, "MROUND": {"a": "(number; multiple)", "d": "Trả về giá trị đã làm tròn theo bội số", "ad": "là giá trị cần làm tròn!là bội số cần làm tròn tới"}, "MULTINOMIAL": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON> lại đa thức từ một tập số", "ad": "là các giá trị 1 tới 255 mà bạn muốn có đa thức"}, "MUNIT": {"a": "(dimension)", "d": "Trả về ma trận đơn vị của chiều được chỉ định", "ad": "là số nguyên xác định chiều của ma trận đơn vị bạn muốn trả về"}, "ODD": {"a": "(number)", "d": "Là tròn số dương lên và số âm xuống số lẻ gần nhất", "ad": "là giá trị cần làm tròn"}, "PI": {"a": "()", "d": "<PERSON><PERSON><PERSON> về giá trị số <PERSON>, 3.14159265358979, <PERSON><PERSON><PERSON><PERSON><PERSON> x<PERSON><PERSON> tới 15 chữ số", "ad": ""}, "POWER": {"a": "(number; power)", "d": "Trả về giá trị hàm mũ của một số", "ad": "là số cơ số, bất kỳ số thực!là số mũ của cơ số"}, "PRODUCT": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON> tất cả các số là tham đối", "ad": "từ 1 đến 255 số, g<PERSON><PERSON> trị lô-gic, hoặc văn bản thể hiện các số mà bạn muốn nhân"}, "QUOTIENT": {"a": "(numerator; denominator)", "d": "<PERSON><PERSON><PERSON> về phần nguyên của phép chia", "ad": "là số bị chia!là số chia"}, "RADIANS": {"a": "(angle)", "d": "Chuyển đổi độ sang radian", "ad": "là g<PERSON><PERSON> t<PERSON>h theo độ cần chuyển đổi"}, "RAND": {"a": "()", "d": "Trả về số ngẫu nhiên phân bổ đều trong khoảng lớn hơn hoặc bằng 0 và nhỏ hơn 1 (thay đổi khi tính lại)", "ad": ""}, "RANDARRAY": {"a": "([hàng]; [cột]; [nhỏ_nhất]; [lớn_nhất]; [số_nguyên])", "d": "<PERSON><PERSON><PERSON> về một mảng số ngẫu nhiên", "ad": "số hàng trong mảng được trả về!số cột trong mảng được trả về!số lượng tối thiểu mà bạn muốn được trả về!số lượng tối đa mà bạn muốn được trả về!trả về một số nguyên hoặc giá trị thập phân. TRUE cho số nguyên, FALSE cho số thập phân"}, "RANDBETWEEN": {"a": "(bottom; top)", "d": "Trả lại số ngẫu nhiên giữa các số được chỉ định", "ad": "là số nguyên nhỏ nhất mà RANDBETWEEN sẽ trả lại!là số nguyên lớn nhất mà RANDBETWEEN sẽ trả lại"}, "ROMAN": {"a": "(number; [form])", "d": "<PERSON><PERSON><PERSON>n đổi chữ số <PERSON><PERSON><PERSON><PERSON><PERSON> sang La mã, n<PERSON><PERSON> v<PERSON><PERSON> bản", "ad": "là chữ số A-rập cần chuyển đổi!là số chỉ định kiểu chữ số La mã mong muốn."}, "ROUND": {"a": "(number; num_digits)", "d": "<PERSON><PERSON><PERSON> tròn số theo số chữ số được chỉ định", "ad": "là số cần làm tròn!là số chữ số cần làm tròn. Nếu là số âm làm tròn phần bên trái dấu thập phân, là số 0 làm tròn đến giá trị nguyên gần nhất"}, "ROUNDDOWN": {"a": "(number; num_digits)", "d": "<PERSON><PERSON><PERSON> tròn số xuố<PERSON>, tiến tới không", "ad": "là số thực cần làm tròn xuống!là số chữ số cần làm tròn. Nếu là số âm làm tròn phần bên trái dấu thập phân, là số 0 hoặc không có làm tròn đến giá trị nguyên gần nhất"}, "ROUNDUP": {"a": "(number; num_digits)", "d": "<PERSON><PERSON><PERSON> tròn số lên, xa khỏi không", "ad": "là số thực cần làm tròn lên!là số chữ số cần làm tròn. Nếu là số âm làm tròn phần bên trái dấu thập phân, là số 0 hoặc không có làm tròn đến giá trị nguyên gần nhất"}, "SEC": {"a": "(number)", "d": "Trả về hàm sec của một góc", "ad": "là số đo góc theo rađian mà bạn muốn tìm sec"}, "SECH": {"a": "(number)", "d": "Trả về hàm sec hyperbol của một góc", "ad": "là số đo góc theo rađian mà bạn muốn tìm sec hyperbol"}, "SERIESSUM": {"a": "(x; n; m; coefficients)", "d": "<PERSON><PERSON><PERSON> về tổng chuỗi lũy thừa theo công thức", "ad": "là giá trị  nhập vào cho chuỗi lũy thừa!là lũy thừa khởi tạo để tăng tới x!là bước tăng cho mỗi phần trong chuỗi!là tập hợp hệ số sẽ được nhân với mỗi lũy thừa của x"}, "SIGN": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về dấu của số: 1 nếu số dươ<PERSON>, 0 nếu là số 0, hoặc -1 nếu số âm", "ad": "là số thực b<PERSON><PERSON> k<PERSON>"}, "SIN": {"a": "(number)", "d": "Trả về Sin của góc", "ad": "là giá trị Sin của góc theo <PERSON>. Độ * PI()/180 = Radian"}, "SINH": {"a": "(number)", "d": "<PERSON><PERSON>ả về Sin hi-péc-bôn của một số", "ad": "là số thực b<PERSON><PERSON> k<PERSON>"}, "SQRT": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về căn bậc hai của một số", "ad": "là số cần l<PERSON>y căn b<PERSON>c hai"}, "SQRTPI": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về căn bậ<PERSON>i (số * Pi)", "ad": "là số nhân với p"}, "SUBTOTAL": {"a": "(function_num; ref1; ...)", "d": "<PERSON><PERSON><PERSON> về tổng con trong danh sách hoặc cơ sở dữ liệu", "ad": "là số từ 1 đến 11 chỉ định hàm tổng hợp cho tổng con!từ 1 đến 254 khoảng hoặc tham chiếu cần tính tổng con"}, "SUM": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON> tất cả các số trong phạm vi ô", "ad": "từ 1 đến 255 số để tính tổng. <PERSON>h<PERSON>ng tính giá trị lô-gic và văn bản, trừ phi nó là tham đối"}, "SUMIF": {"a": "(range; criteria; [sum_range])", "d": "<PERSON><PERSON><PERSON> các ô chỉ định theo điều kiện hoặc tiêu chí cho trước", "ad": "là khoảng các ô cần tính giá trị!là điều kiện hoặc tiêu chí dưới dạng số, bi<PERSON><PERSON> thức, hoặc văn bản xác định ô nào được thêm vào!là các ô thật sự tính tổng. <PERSON><PERSON><PERSON>hô<PERSON> có, sử dụng các ô trong khoảng"}, "SUMIFS": {"a": "(sum_range; criteria_range; criteria; ...)", "d": "<PERSON>h<PERSON><PERSON> ô được chỉ ra bởi tập cho trước các điều kiện hay chỉ tiêu", "ad": "là các ô thực tế cần cộng.!là dải các ô muốn tính giá trị theo điều kiện cụ thể!là điều kiện ở dạng số, bi<PERSON><PERSON> thức, hay văn bản xác định ô nào sẽ được thêm vào"}, "SUMPRODUCT": {"a": "(array1; [array2]; [array3]; ...)", "d": "Trả về tổng tích của các khoảng hoặc mảng tương ứng", "ad": "từ 2 đến 255 mảng cần nhân sau đó cộng các thành phần. Tất cả các mảng phải có cùng kích cỡ"}, "SUMSQ": {"a": "(number1; [number2]; ...)", "d": "<PERSON><PERSON><PERSON> về tổng bình phương các tham đối. Tham đối có thể là số, m<PERSON><PERSON>, tên hoặc tham chiếu tới ô chứa số", "ad": "từ 1 đến 255 số, m<PERSON><PERSON>, tên hoặc tham chiếu tới mảng cần tính tổng bình phương"}, "SUMX2MY2": {"a": "(array_x; array_y)", "d": "Tổng độ lệch gi<PERSON>a bình phương trong hai khoảng hoặc mảng tương ứng", "ad": "là khoảng hay mảng số thứ nhất và có thể là số hoặc tên, m<PERSON><PERSON>, hoặc tham chiếu chứa số!là khoảng hay mảng số thứ hai và có thể là số hoặc tên, <PERSON><PERSON><PERSON>, hoặc tham chiếu chứa số"}, "SUMX2PY2": {"a": "(array_x; array_y)", "d": "T<PERSON>ả về tổng cộng của tổng bình phương các số trong hai khoảng hoặc mảng tương ứng", "ad": "là khoảng hay mảng số thứ nhất và có thể là số hoặc tên, m<PERSON><PERSON>, hoặc tham chiếu chứa số!là khoảng hay mảng số thứ hai và có thể là số hoặc tên, <PERSON><PERSON><PERSON>, hoặc tham chiếu chứa số"}, "SUMXMY2": {"a": "(array_x; array_y)", "d": "Tổng bình phương độ lệch trong hai khoảng hoặc mảng tương ứng", "ad": "là khoảng hay mảng giá trị thứ nhất và có thể là số, m<PERSON><PERSON>, hoặc tham chiếu chứa số!là khoảng hay mảng giá trị thứ hai và có thể là số, <PERSON><PERSON><PERSON>, hoặc tham chiếu chứa số"}, "TAN": {"a": "(number)", "d": "<PERSON><PERSON>ả về <PERSON> của góc", "ad": "là giá trị Tang của góc theo <PERSON>. Độ * PI()/180 = Radian"}, "TANH": {"a": "(number)", "d": "<PERSON><PERSON><PERSON> về Tang hi-péc-bôn của một số", "ad": "là số thực b<PERSON><PERSON> k<PERSON>"}, "TRUNC": {"a": "(number; [num_digits])", "d": "<PERSON><PERSON><PERSON> ngắn số thành số nguyên bằng cách loại bỏ phần thập phân, hoặc phần phân số của nó", "ad": "là số cần rút ngắn!là số chỉ độ chính xác của việc rút ngắn, bằng 0 (không) nếu không có"}, "ADDRESS": {"a": "(row_num; column_num; [abs_num]; [a1]; [sheet_text])", "d": "T<PERSON>o tham chiếu ô dạng văn bản, c<PERSON> số hàng và cột xác định", "ad": "là số hiệu hàng trong tham chiếu ô: Số_hiệu_hàng = 1 cho hàng 1!là số hiệu cột trong tham chiếu ô.Ví dụ, Số_hiệu_cột = 1 cho cột D!chỉ định loại tham chiếu: tuyệt đối nếu = 1; hàng tuyệt đối/cột tương đối nếu = 2; hàng tương đối/cột tuyệt đối nếu = 3; tương đối nếu = 4!là giá trị lô-gic thể hiện kiểu tham chiếu: kiểu A1 = 1 hoặc ĐÚNG; kiểu R1C1 = 0 hoặc SAI!là văn bản chỉ định tên của trang tính sử dụng như tham chiếu ngoài"}, "CHOOSE": {"a": "(index_num; value1; [value2]; ...)", "d": "Chọn giá trị hoặc thao tác thực hiện từ danh sách các giá trị, dựa trên số hiệu chỉ mục", "ad": "chỉ định tham đối giá trị được chọn. Index_num phải từ 1 đến 254, công thức hoặc tham chiếu tới một số từ 1 đến 254!từ 1 đến 254 số, tham chi<PERSON><PERSON> ô, tê<PERSON> đ<PERSON><PERSON> ngh<PERSON>, c<PERSON><PERSON> thứ<PERSON>, h<PERSON><PERSON>, hoặc tham đối văn bản do hàm CHOOSE chọn"}, "COLUMN": {"a": "([reference])", "d": "<PERSON><PERSON><PERSON> về số hiệu cột của tham chiếu", "ad": "là một ô hoặc một vùng ô liên tiếp bạn cần có số hiệu cột; <PERSON><PERSON><PERSON> không có, sử dụng ô trong hàm CỘT"}, "COLUMNS": {"a": "(array)", "d": "<PERSON><PERSON><PERSON> về số cột trong một mảng hoặc tham chiếu", "ad": "<PERSON>à một mảng, c<PERSON><PERSON> th<PERSON><PERSON> mả<PERSON>, hoặc tham chiếu tới một khoảng các ô cần số cột"}, "FORMULATEXT": {"a": "(reference)", "d": "Trả về công thức như là một xâu", "ad": "là tham chiếu đến công thức"}, "HLOOKUP": {"a": "(lookup_value; table_array; row_index_num; [range_lookup])", "d": "Tìm giá trị trong hàng trên cùng của bảng hoặc mảng các giá trị và trả về giá trị cùng cột từ hàng chỉ định", "ad": "là giá trị tìm thấy trong hàng đầu tiên của bảng và có thể là giá trị, tham chi<PERSON><PERSON>, hoặc xâu văn bản!là một bảng các văn b<PERSON>, s<PERSON>, hoặc giá trị lô-gic chứa dữ liệu cần tìm. Bảng_mảng có thể là tham chiếu tới khoảng hoặc tên khoảng!là số hiệu hàng của giá trị trả về phù hợp trong bảng_mảng. Hàng giá trị đầu tiên trong bảng là hàng 1!là giá trị lô-gic: =ĐÚNG hoặc không có nếu cần tìm phù hợp nhất trong hàng trên cùng (sắp xếp theo trật tự tăng dần); =SAI nếu cần tìm khớp"}, "HYPERLINK": {"a": "(link_location; [friendly_name])", "d": "T<PERSON>o T<PERSON><PERSON> lối tắt hoặc bước nhảy để mở tài liệu lưu trên ổ cứng, máy chủ mạng hoặc trên internet", "ad": "là văn bản đưa ra đường dẫn và tên tệp của tài liệu được mở, một vị trí trên đĩa cứ<PERSON>, địa chỉ UNC, hoặc đường dẫn URL!là văn bản hoặc số được hiển thị trong ô. <PERSON><PERSON><PERSON> không có, sẽ hiển thị văn bản Vị_trí_Liên_kết"}, "INDEX": {"a": "(array; row_num; [column_num]!reference; row_num; [column_num]; [area_num])", "d": "Tr<PERSON> về giá trị hoặc tham chiếu tới ô giao của hàng và cột trong vùng chỉ định", "ad": "là một vùng ô hoặc một hằng số dạng mảng.!chọn hàng trong Mảng hoặc Tham chiếu trả về giá trị. <PERSON><PERSON><PERSON>, cần có Số hiệu-cột!chọn cột trong Mảng hoặc Tham chiếu trả về giá trị. <PERSON><PERSON><PERSON>, cần có Số hiệu-hàng!là tham chiếu tới một hoặc nhiều vùng ô!chọn hàng trong Mảng hoặc Tham chiếu trả về giá trị. <PERSON><PERSON><PERSON>, cần có Số hiệu-cột!chọn cột trong Mảng hoặc Tham chiếu trả về giá trị. <PERSON><PERSON><PERSON>h<PERSON>, cần có Số hiệu-hàng!chọn vùng trong Tham chiếu trả về giá trị. Vùng đầu tiên đư<PERSON><PERSON> chọn hoặc nhập vào là vùng 1, tiế<PERSON> the<PERSON> <PERSON> vù<PERSON> 2, c<PERSON> <PERSON>h<PERSON> vậy"}, "INDIRECT": {"a": "(ref_text; [a1])", "d": "<PERSON><PERSON><PERSON> về tham chiếu chỉ định bởi xâu văn bản", "ad": "là tham chiếu tới ô chứa một tham chiếu kiểu A1 hoặc R1C1, tên xác định của tham chiếu, hoặc một tham chiếu tới ô là xâu văn bản!là giá trị lô-gic chỉ định loại tham chiếu trong Văn_bản_tham chiếu: = SAI nếu là kiểu R1C1; =ĐÚNG hoặc không có nếu là kiểu A1"}, "LOOKUP": {"a": "(lookup_value; lookup_vector; [result_vector]!lookup_value; array)", "d": "Tìm trong phạm vi một hàng, mộ<PERSON> cột, hoặc trong mảng.  <PERSON><PERSON><PERSON><PERSON> đưa ra để đảm bảo tương thích ngược", "ad": "là giá trị hàm LOOKUP tìm trong Vector_tìm, c<PERSON> thể là số, vă<PERSON> bả<PERSON>, gi<PERSON> trị lô-gic hoặc tên, tham chiếu tới một giá trị!là vùng chỉ chứa một hàng, một cột giá trị văn bản, s<PERSON>, lô-gic theo trật tự tăng dần!là vùng chỉ chứa một hàng hoặc một cột, cùng kích cỡ với Vector_tìm!là giá trị hàm LOOKUP tìm trong mảng, c<PERSON> thể là số, v<PERSON><PERSON> b<PERSON>, giá trị lô-gic hoặc tên, tham chiếu tới một giá trị!là vùng ô chứa giá trị số, văn bản, lô-gic c<PERSON>n so sánh với Giá trị_tìm"}, "MATCH": {"a": "(lookup_value; lookup_array; [match_type])", "d": "Tr<PERSON> về vị trí tương đối của một phần tử trong mảng khớp với giá trị cho trước theo trật tự nhất định", "ad": "là giá trị dùng để tìm giá trị mong muốn trong mảng, c<PERSON> thể là số, v<PERSON><PERSON> b<PERSON>, gi<PERSON> trị lô<PERSON>gic, hoặc tham chiếu tới các giá trị đó!là một khoảng các ô liên tục chứa giá trị, mảng các giá trị, hoặc tham chiếu tới mảng tìm kiếm!là số 1, 0, hoặc -1 chỉ thị giá trị trả về."}, "OFFSET": {"a": "(reference; rows; cols; [height]; [width])", "d": "Tr<PERSON> về tham chiếu tới khoảng từ số hàng và số cột của một khoảng cho trước", "ad": "là tham chiếu mà khoảng chênh dựa vào, một tham chiếu tới ô hoặc khoảng các ô lân cận!là số hàng, trên hoặc dưới, ô trên-trái trong kết quả tham chiếu tới!là số cột, tr<PERSON><PERSON> hoặc phải, ô trên-trái trong kết quả tham chiếu tới!là độ cao, tính bằng số hàng, trong kết quả mong muốn, bằng độ cao của Tham chiếu nếu không có!là độ rộng, t<PERSON><PERSON> bằng số cột, trong kết quả mong muốn, bằng độ rộng của Tham chiếu nếu không có"}, "ROW": {"a": "([reference])", "d": "<PERSON><PERSON><PERSON> về số hiệu hàng của tham chiếu", "ad": "là một ô hoặc một vùng ô đơn bạn cần có số hiệu hàng; <PERSON><PERSON><PERSON> không có, trả về ô trong hàm HÀNG"}, "ROWS": {"a": "(array)", "d": "<PERSON><PERSON><PERSON> về số hàng trong một tham chiếu hoặc mảng", "ad": "<PERSON>à một mảng, c<PERSON><PERSON> th<PERSON><PERSON> mả<PERSON>, hoặc tham chiếu tới một khoảng các ô cần số hàng"}, "TRANSPOSE": {"a": "(array)", "d": "Chuyển một dãy dọc các ô sang dãy ngang, hoặc ngược lại,", "ad": "là một dãy ô trên một trang tính hoặc một mảng giá trị mà bạn muốn hoán đổi"}, "UNIQUE": {"a": "(array; [by_col]; [exactly_once])", "d": " Tr<PERSON> lại giá trị duy nhất từ một phạm vi hoặc mảng.", "ad": "phạm vi hoặc mảng mà từ đó trả về các hàng hoặc cột duy nhất!là một giá trị logic: so sánh các hàng với nhau và trả về các hàng duy nhất = FALSE hoặc bỏ qua; so sánh các cột với nhau và trả về các cột duy nhất = TRUE!là một giá trị logic: trả về các hàng hoặc cột xuất hiện chính xác một lần từ mảng = TRUE; trả về tất cả các hàng hoặc cột riêng biệt từ mảng = FALSE hoặc bị bỏ qua"}, "VLOOKUP": {"a": "(lookup_value; table_array; col_index_num; [range_lookup])", "d": "Tìm giá trị trong cột bên trái nhất củ<PERSON> bả<PERSON>, và trả về giá trị cùng hàng từ cột chỉ định. Mặc định, bảng đư<PERSON><PERSON> sắp xếp theo trật tự tăng dần", "ad": "là giá trị tìm thấy trong cột đầu tiên của bảng và có thể là giá trị, tham chi<PERSON><PERSON>,hoặc xâu văn bản!là một bảng các văn b<PERSON>, s<PERSON>, hoặc giá trị lô-gic chứa dữ liệu cần truy xuất. Bảng_mảng có thể là tham chiếu tới khoảng hoặc tên khoảng!là số hiệu cột của giá trị trả về phù hợp trong bảng_mảng. Cột giá trị đầu tiên trong bảng là cột 1!là giá trị lô-gic: =ĐÚNG hoặc không có nếu cần tìm phù hợp nhất trong cột đầu tiên (sắp xếp theo trật tự tăng dần); =SAI nếu cần tìm khớp"}, "XLOOKUP": {"a": "(giá_trị_tra_cứu; mảng_tra_cứu; mảng_trả_về; [nếu_không_tìm_thấy]; [chế_độ_khớp]; [chế_độ_tìm_kiếm])", "d": "T<PERSON><PERSON> kiếm giá trị trùng khớp trong một dải ô hoặc một mảng và trả về mục tương ứng từ dải ô hoặc mảng thứ hai. <PERSON> mặc <PERSON>, sẽ sử dụng giá trị khớp hoàn toàn", "ad": "là giá trị cần tìm kiếm!là mảng hoặc dải ô cần tìm kiếm!là mảng hoặc dải ô cần trả về!trả về nếu không tìm thấy giá trị trùng khớp!chỉ định cách khớp giá_trị_tra_cứu với giá trị trong mảng_tra_cứu!chỉ định chế độ tìm kiếm để sử dụng. <PERSON> mặc định, sẽ sử dụng tìm kiếm từ đầu đến cuối"}, "CELL": {"a": "(info_type; [reference])", "d": "Tr<PERSON> về thông tin về định dạng, vị trí hay nội dung của một ô", "ad": "giá trị văn bản xác định bạn muốn trả về kiểu thông tin ô nào!ô mà bạn muốn có thông tin"}, "ERROR.TYPE": {"a": "(error_val)", "d": "Tr<PERSON> về một số khớp với giá trị lỗi.", "ad": "là giá trị lỗi cần nhận biết bởi số, và có thể là giá trị lỗi thật sự hoặc tham chiếu tới ô chứa giá trị lỗi"}, "ISBLANK": {"a": "(value)", "d": "<PERSON><PERSON>m tra tham chiếu có phải tới một ô rỗng, và trả về ĐÚNG hoặc SAI", "ad": "là ô hoặc tên tham chiếu tới ô cần thử"}, "ISERR": {"a": "(giá_trị)", "d": "<PERSON><PERSON>m tra xem giá trị có phải là lỗi khác ngoài #N/A hay không và trả về TRUE hoặc FALSE", "ad": "là giá trị cần kiểm tra. Gi<PERSON> trị có thể tham chiếu tới ô, công thức hoặc một tên có tham chiếu tới ô, công thức hoặc giá trị"}, "ISERROR": {"a": "(giá_trị)", "d": "<PERSON><PERSON><PERSON> tra xem giá trị có phải là lỗi hay không và trả về TRUE hoặc FALSE", "ad": "là giá trị cần kiểm tra. Gi<PERSON> trị có thể tham chiếu tới ô, công thức hoặc một tên có tham chiếu tới ô, công thức hoặc giá trị"}, "ISEVEN": {"a": "(number)", "d": "Trả về ĐÚNG nếu là số chẵn", "ad": "là giá trị cần kiểm tra"}, "ISFORMULA": {"a": "(reference)", "d": "Kiểm tra xem tham chiếu tới một ô có bao gồm công thức không, và trả về TRUE (ĐÚNG) hoặc FALSE (SAI)", "ad": "là tham chiếu tới ô bạn muốn kiểm tra. Tham chiếu có tể là tham chiếu ô, công thức, hoặc tên mà tham chiếu tới ô"}, "ISLOGICAL": {"a": "(value)", "d": "<PERSON><PERSON><PERSON> tra giá trị có phải là giá trị lô-gic (ĐÚNG hoặc SAI), và tr<PERSON> về ĐÚNG hoặc SAI", "ad": "là giá trị cần thử. <PERSON><PERSON><PERSON> trị có thể tham chiếu tới ô, cô<PERSON> thức, hoặc tên tham chiếu tới ô, cô<PERSON> thức, hoặc một giá trị"}, "ISNA": {"a": "(value)", "d": "<PERSON><PERSON><PERSON> tra giá trị có phải là #N/A, trả về ĐÚNG hoặc SAI", "ad": "là giá trị cần kiểm tra. Gi<PERSON> trị có thể tham chiếu tới ô, công thức hoặc là tên tham chiếu tới ô, công thức hoặc là một giá trị"}, "ISNONTEXT": {"a": "(value)", "d": "<PERSON><PERSON><PERSON> tra giá trị có phải là văn bản (ô trắng không phải là văn bản), và trả về ĐÚNG hoặc SAI", "ad": "là giá trị cần thử: một ô; công thức; hoặc tên tham chiếu tới ô, công thức, hoặc giá trị"}, "ISNUMBER": {"a": "(value)", "d": "<PERSON><PERSON><PERSON> tra một giá trị có phải là số, và trả về ĐÚNG hoặc SAI", "ad": "là giá trị cần thử. <PERSON><PERSON><PERSON> trị có thể tham chiếu tới ô, cô<PERSON> thức,hoặc tên tham chiếu tới ô, cô<PERSON> thức,hoặc một giá trị"}, "ISODD": {"a": "(number)", "d": "Trả về ĐÚNG nếu là số lẻ", "ad": "là giá trị cần kiểm tra"}, "ISREF": {"a": "(value)", "d": "<PERSON><PERSON><PERSON> tra giá trị có phải là tham chiếu, trả về ĐÚNG hoặc SAI", "ad": "là giá trị cần thử. <PERSON><PERSON><PERSON> trị có thể tham chiếu tới ô, công thức, hoặc tên tham chiếu tới ô, cô<PERSON> thức, hoặc giá trị"}, "ISTEXT": {"a": "(value)", "d": "<PERSON><PERSON><PERSON> tra một giá trị có phải là văn bản, và trả về ĐÚNG hoặc SAI", "ad": "là giá trị cần thử. <PERSON><PERSON><PERSON> trị có thể tham chiếu tới ô, cô<PERSON> thức,hoặc tên tham chiếu tới ô, cô<PERSON> thức,hoặc một giá trị"}, "N": {"a": "(value)", "d": "<PERSON>yển đổi giá trị khác số thành số, ng<PERSON><PERSON> tháng thành số tuần tự, ĐÚNG thành 1, c<PERSON><PERSON> gi<PERSON> trị khác thành 0 (kh<PERSON><PERSON>)", "ad": "là giá trị cần chuyển đổi"}, "NA": {"a": "()", "d": "Tr<PERSON> về giá trị lỗi #N/A (giá trị không áp dụng)", "ad": ""}, "SHEET": {"a": "([value])", "d": "Trả về số của trang tính của trang tính được tham chiếu", "ad": "là tên của trang tính hoặc tham chiếu bạn đang muốn tìm số của trang tính. Nếu bỏ qua, sẽ trả về số của trang tính có chứa hàm này"}, "SHEETS": {"a": "([reference])", "d": "Trả về số lượng trang tính trong một tham chiếu", "ad": "là tham chiếu mà bạn muốn biết nó bao gồm bao nhiêu trang tính. Nếu bỏ qua, sẽ trả về số lượng trang tính có trong sổ làm việc"}, "TYPE": {"a": "(giá_trị)", "d": "Tr<PERSON> về một số nguyên đại diện cho kiểu dữ liệu của một giá trị: số = 1; văn bản = 2; giá trị lô-gic = 4; gi<PERSON> trị lỗi = 16; mảng = 64; dữ liệu phức hợp = 128", "ad": "có thể là bất kỳ giá trị nào"}, "AND": {"a": "(logical1; [logical2]; ...)", "d": "<PERSON><PERSON><PERSON> tra nếu tất cả các tham đối là TRUE, trả về giá trị TRUE nếu tất cả tham đối là TRUE", "ad": "từ 1 đến 255 điều kiện cần kiểm tra, có thể là ĐÚNG hoặc SAI, có thể là giá trị lô-gic, m<PERSON><PERSON>, hoặc tham chiếu"}, "FALSE": {"a": "()", "d": "Trả về giá trị lô-gic SAI", "ad": ""}, "IF": {"a": "(logical_test; [value_if_true]; [value_if_false])", "d": "<PERSON><PERSON><PERSON> tra điều kiện có đáp ứng không và trả về một giá trị nếu ĐÚNG, trả về một giá trị khác nếu SAI", "ad": "là bất kỳ giá trị hoặc biểu thức nào có thể được tính giá trị ĐÚNG hoặc SAI!là giá trị được trả về nếu Logical_test là ĐÚNG. <PERSON><PERSON><PERSON> không có, Đ<PERSON><PERSON> được trả về. Bạn có thể lồng tới bảy hàm IF!là giá trị được trả về nếu kiểm tra lô gíc là SAI. N<PERSON><PERSON> không có, SAI được trả về"}, "IFS": {"a": "(logical_test; value_if_true; ...)", "d": "<PERSON><PERSON>m tra xem một hay nhiều điều kiện được đáp ứng và trả về giá trị tương ứng với điều kiện TRUE đầu tiên", "ad": "là bất kỳ giá trị hoặc biểu thức nào có thể được tính giá trị là TRUE hoặc FALSE!là giá trị được trả về nếu Logical_test là TRUE"}, "IFERROR": {"a": "(value; value_if_error)", "d": "Trả lại value_if_error nếu biểu thức có lỗi và giá trị của biểu thức nếu không", "ad": "là giá trị của biểu thức hay tham chiếu!là bất kỳ giá trị hay biểu thức hay tham chiếu"}, "IFNA": {"a": "(value; value_if_na)", "d": "Trả về giá trị bạn chỉ định nếu biểu thức cho ra kết quả #N/A, nếu không thì sẽ trả về giá trị của biểu thức", "ad": "là bất kỳ giá trị hoặc biểu thức hoặc tham chiếu nào!là bất kỳ giá trị hoặc biểu thức hoặc tham chiếu nào"}, "NOT": {"a": "(logical)", "d": "Đổi SAI thành ĐÚNG, ĐÚNG thành SAI", "ad": "là giá trị hoặc biểu thức có thể tính giá trị là ĐÚNG hoặc SAI"}, "OR": {"a": "(logical1; [logical2]; ...)", "d": "<PERSON><PERSON><PERSON> tra nếu tất cả các tham đối là SAI, trả về giá trị SAI", "ad": "từ 1 đền 255 điều kiện cần kiểm tra, có thể là ĐÚNG hoặc SAI"}, "SWITCH": {"a": "(expression; value1; result1; [default_or_value2]; [result2]; ...)", "d": "T<PERSON>h giá trị biểu thức dựa vào danh sách các giá trị và trả về kết quả tương ứng với giá trị khớp đầu tiên. <PERSON>ếu không có giá trị khớp, giá trị mặc định tùy chọn đượ<PERSON> trả về", "ad": "là biểu thức để tính giá trị!là giá trị để so sánh với biểu thức!là kết quả để trả về nếu giá trị tương ứng khớp với biểu thức"}, "TRUE": {"a": "()", "d": "Trả về giá trị lô-gic ĐÚNG", "ad": ""}, "XOR": {"a": "(logical1; [logical2]; ...)", "d": "Trả về hàm \"Exclusive Or\" lô-gic của tất cả tham đối", "ad": "là từ 1 đến 254 điều kiện bạn muốn kiểm tra mà có thể là TRUE (ĐÚNG) hoặc FALSE (SAI) và có thể là giá trị lô-gic, mảng, hoặc tham chiếu"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Trả về văn bản trư<PERSON><PERSON> khi phân tách ký tự.", "ad": "Văn bản bạn muốn tìm kiếm dấu phân tách.!Ký tự hoặc chuỗi dùng làm dấu phân tách.!Lần xuất hiện dấu phân tách mong muốn. Mặc định là 1. Số âm sẽ tìm kiếm từ cuối.!Tìm kiếm văn bản để tìm so khớp dấu phân tách. Theo mặc định, so khớp phân biệt chữ hoa/thường đã hoàn tất.!C<PERSON> so khớp dấu phân tách với phần cuối văn bản không. <PERSON> mặc định, chúng không khớp.!Tr<PERSON> về nếu không tìm thấy so khớp. <PERSON> mặc định, kết quả trả về là #N/A."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Trả về văn bản sau khi phân tách các ký tự.", "ad": "Văn bản bạn muốn tìm kiếm dấu phân tách.!Ký tự hoặc chuỗi dùng làm dấu phân tách.!Lần xuất hiện dấu phân tách mong muốn. Mặc định là 1. Số âm sẽ tìm kiếm từ cuối.!Tìm kiếm văn bản để tìm so khớp dấu phân tách. Theo mặc định, so khớp phân biệt chữ hoa/thường đã hoàn tất.!C<PERSON> so khớp dấu phân tách với phần cuối văn bản không. <PERSON> mặc định, chúng không khớp.!Tr<PERSON> về nếu không tìm thấy so khớp. <PERSON> mặc định, kết quả trả về là #N/A."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "<PERSON><PERSON><PERSON> văn bản thành các hàng hoặc cột bằng dấu phân tách.", "ad": "Văn bản để tách!Ký tự hoặc chuỗi để tách các cột theo.!Ký tự hoặc chuỗi để tách các hàng theo.!Có bỏ qua các ô trống hay không. Mặc định là FALSE.!Tìm kiếm văn bản cho một dấu phân cách phù hợp. Theo mặc định, kết quả khớp phân biệt chữ hoa/thường đã hoàn tất.!Giá trị được sử dụng cho phần đệm. <PERSON> mặc định, #N/A được sử dụng."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": " <PERSON><PERSON> b<PERSON><PERSON> một véc-tơ hàng hoặc cột sau một số giá trị được chỉ định.", "ad": " Vec-tơ hoặc tham chiếu để bọc.! Số lượng giá trị tối đa trên mỗi cột.! Giá trị cần thêm vào. Mặc định là #N/A."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "<PERSON><PERSON><PERSON> chồng theo chiều dọc các mảng thành một mảng.", "ad": "<PERSON><PERSON>ng hoặc tham chiếu cần đư<PERSON><PERSON> xếp chồng."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "<PERSON><PERSON><PERSON> chồng theo chiều ngang các mảng thành một mảng.", "ad": "<PERSON><PERSON>ng hoặc tham chiếu cần đư<PERSON><PERSON> xếp chồng."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "<PERSON><PERSON><PERSON> về các hàng từ mảng hoặc tham chiếu.", "ad": "Mảng hoặc tham chiếu chứa các hàng được trả về.!Số lượng hàng cần trả về."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "<PERSON><PERSON><PERSON> về các cột từ mảng hoặc tham chiếu.", "ad": "Mảng hoặc tham chiếu chứa các cột được trả về.!Số lượng cột cần trả về."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "<PERSON><PERSON><PERSON> về mảng dưới dạng một cột.", "ad": "Mảng hoặc tham chiếu cần trả về dưới dạng cột.!Có bỏ qua các loại giá trị nhất định hay không. <PERSON> mặc định, không giá trị nào bị bỏ qua.!Quét mảng theo cột. <PERSON> mặc định, mảng đư<PERSON><PERSON> quét theo hàng."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "<PERSON><PERSON><PERSON> về mảng dưới dạng một hàng.", "ad": "Mảng hoặc tham chiếu để trả về dưới dạng một hàng.!Có bỏ qua các loại giá trị nhất định hay không. <PERSON> mặc định, không giá trị nào bị bỏ qua.!Quét mảng theo cột. <PERSON> mặc định, mảng đư<PERSON><PERSON> quét theo hàng."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "<PERSON><PERSON> b<PERSON><PERSON> một véc-tơ hàng hoặc cột sau một số Giá trị được chỉ định.", "ad": " Vec-tơ hoặc tham chiếu để bọc.! Số lượng giá trị tối đa trên mỗi cột.! Giá trị cần thêm vào. Mặc định là #N/A."}, "TAKE": {"a": "(array, rows, [columns])", "d": "<PERSON><PERSON><PERSON> về hàng hoặc cột từ đầu hoặc cuối mảng.", "ad": "Vị trí trong mảng để lấy hàng hoặc cột.!Số lượng hàng cần lấy. Gi<PERSON> trị âm lấy từ cuối mảng.!Số lượng cột cần lấy. Giá trị âm lấy từ cuối mảng."}, "DROP": {"a": "(array, rows, [columns])", "d": "<PERSON><PERSON><PERSON> hàng hoặc cột từ đầu hoặc cuối mảng.", "ad": "Vị trí trong mảng để thả hàng hoặc cột.!Số lượng hàng cần thả. Giá trị âm thả từ cuối mảng.!Số lượng cột cần thả. Giá trị âm thả từ cuối mảng."}, "SEQUENCE": {"a": "(hàng, [cột], [bắt_đầu], [bước])", "d": "<PERSON><PERSON><PERSON> về một chuỗi số", "ad": "số hàng trả về!số cột trả về!số đầu tiên trong chuỗi!số lượng tăng dần của từng giá trị tiếp theo trong chuỗi"}, "EXPAND": {"a": "(array, rows, [columns], [pad_with])", "d": "Mở rộng mảng theo kích thước đã chỉ định.", "ad": "Mảng để mở rộng.!Số hàng trong mảng được mở rộng. <PERSON><PERSON><PERSON> thiếu, các hàng sẽ không được mở rộng.!Số cột trong mảng được mở rộng. <PERSON><PERSON><PERSON> thiế<PERSON>, các cột sẽ không được mở rộng.!Giá trị có phần cần đệm. Giá trị mặc định là #N/A."}, "XMATCH": {"a": "(lookup_value, lookup_array, [match_mode], [search_mode])", "d": "<PERSON>r<PERSON> về vị trí tương đối của một mục trong mảng. <PERSON> mặc định, yêu cầu phải khớp hoàn toàn", "ad": "là giá trị để tìm kiếm!là mảng hoặc phạm vi ô để tìm kiếm!chỉ định cách khớp giá trị_tìm với các giá trị trong mảng_tìm!chỉ định chế độ tìm kiếm để sử dụng. <PERSON> mặc định, tìm kiếm từ đầu đến cuối sẽ được sử dụng"}, "FILTER": {"a": "(m<PERSON><PERSON>, bao_gồm, [nếu_trống])", "d": "<PERSON><PERSON><PERSON> d<PERSON>i ô hoặc mảng", "ad": "dải ô hoặc mảng cần lọc!mảng boolean mà TRUE đại diện cho hàng hoặc cột cần giữ lại!trả về nếu không mục nào được giữ lại"}, "ARRAYTOTEXT": {"a": "(mảng, [định_dạng])", "d": "Tr<PERSON> về biểu diễn bằng văn bản của một mảng", "ad": "mảng cần biểu diễn dưới dạng văn bản!định dạng của văn bản"}, "SORT": {"a": "(mảng, [chỉ_mục_sắp_xếp], [thứ_tự_sắp_xếp], [theo_cột])", "d": "<PERSON><PERSON><PERSON> xếp dải ô hoặc mảng", "ad": "dải ô hay mảng cần sắp xếp!số cho biết hàng hoặc cột cần sắp xếp theo!số cho biết thứ tự sắp xếp mong muốn; 1 cho thứ tự tăng dần (mặc định), -1 cho thứ tự giảm dần!giá trị lô-gic cho biết hướng sắp xếp bạn muốn: FALSE để sắp xếp theo hà<PERSON> (mặc định), TRUE để sắp xếp theo cột"}, "SORTBY": {"a": "(m<PERSON><PERSON>, theo_mảng, [thứ_tự_sắp_xếp], ...)", "d": "Sắp xếp một dải ô hoặc mảng dựa trên các giá trị trong dải ô hoặc mảng tương ứng", "ad": "d<PERSON><PERSON> ô hoặc mảng cần sắp xếp!d<PERSON><PERSON> ô hoặc mảng để sắp xếp theo!số cho biết thứ tự sắp xếp mong muốn; 1 cho biết thứ tự tăng dần (mặc định), -1 cho biết thứ tự giảm dần"}, "GETPIVOTDATA": {"a": "(data_field; pivot_table; [field]; [item]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> xuất dữ liệu lưu trong PivotTable.", "ad": "là tên của trường dữ liệu cần trích xuất dữ liệu!là tham chiếu tới ô hoặc khoảng ô trong PivotTable chứa dữ liệu cần trích xuất!trường và mục được tham chiếu tới!trường và mục được tham chiếu tới"}, "IMPORTRANGE": {"a": "(url_bảng_tính; chuỗi_dải_ô)", "d": "<PERSON><PERSON><PERSON><PERSON> một dải ô từ một bảng tính được chỉ định.", "ad": "URL của bảng tính mà dữ liệu sẽ được nhập từ bảng tính đó!dải ô để nhập"}}