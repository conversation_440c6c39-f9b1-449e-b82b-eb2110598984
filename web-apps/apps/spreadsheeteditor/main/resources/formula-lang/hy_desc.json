{"DATE": {"a": "(year; month; day)", "d": "Վերադարձնում է այն համարը, որը ներկայացնում է ամսաթիվը ամսաթիվ-ժամանակի կոդի մեջ", "ad": "1900-ից կամ 1904-ից (կախված աշխատանքային գրքույկի ամսաթվերի համակարգից) մինչև 9999-ը!1-ից 12-ը տարվա ամիսը ներկայացնող թիվ է!1-ից 31-ը ամսվա օրը ներկայացնող թիվ է"}, "DATEDIF": {"a": "(start-date; end-date; unit)", "d": "Վերադարձնում է երկու ամսաթվի արժեքների տարբերությունը (մեկնարկի ամսաթիվ և ավարտի ամսաթիվ)՝ հիմնված նշված միջակայքի (միավորի) վրա", "ad": "ամսաթիվ է, որը ներկայացնում է տվյալ ժամանակաշրջանի առաջին կամ մեկնարկային ամսաթիվը!Ամսաթիվ է, որը ներկայացնում է ժամանակաշրջանի վերջին կամ ավարտի ամսաթիվը!Տեղեկատվության տեսակն է, որը ցանկանում եք վերադարձնել"}, "DATEVALUE": {"a": "(date_text)", "d": "Փոխակերպում է ամսաթիվը տեքստի տեսքով թվի, որը ներկայացնում է ամսաթիվ-ժամանակի կոդը", "ad": "Տեքստ է, որը ներկայացնում է ամսաթիվ Աղյուսակի խմբագրման ամսաթվի ձևաչափով, 1/1/1900 կամ 1/1/1904 (կախված աշխատանքային գրքի ամսաթվերի համակարգից) և 12/31/9999-ի միջև"}, "DAY": {"a": "(serial_number)", "d": "Վերադարձնում է ամսվա օրը՝ 1-ից մինչև 31 թիվը։", "ad": "թիվն է date-time-ի ծածկագրի, որն օգտագործվում է Աղյուսակների խմբագրի կողմից"}, "DAYS": {"a": "(end_date; start_date)", "d": "Վերադարձնում է երկու ամսաթվերի միջև եղած օրերի քանակը:", "ad": "start_date և end_date-ը երկու ամսաթվերն են, որոնց միջև դուք ցանկանում եք իմանալ օրերի քանակը: start_date և end_date-ը երկու ամսաթվերն են, որոնց միջև դուք ցանկանում եք իմանալ օրերի քանակը"}, "DAYS360": {"a": "(start_date; end_date; [method])", "d": "Վերադարձնում է օրերի քանակը երկու ամսաթվերի միջև՝ հիմնված 360-օրյա տարվա վրա (տասներկու 30-օրյա ամիսներ)", "ad": "start_date և end_date-ը երկու ամսաթվերն են, որոնց միջև դուք ցանկանում եք իմանալ օրերի քանակը: start_date և end_date-ը երկու ամսաթվերն են, որոնց միջև դուք ցանկանում եք իմանալ օրերի քանակը՝ տրամաբանական արժեք է, որը նշում է հաշվարկման եղանակը՝ ԱՄՆ (NASD) = FALSE կամ բաց թողնված եվրոպական = TRUE:"}, "EDATE": {"a": "(start_date; months)", "d": "Վերադարձնում է ամսաթվի սերիական համարը, որը մեկնարկի ամսաթվից առաջ կամ հետո ամիսների նշված թիվն է", "ad": "հերթական ամսաթվի համարն է, որը ներկայացնում է start date-ը։ start date-ից առաջ կամ հետո ամիսների թիվն է"}, "EOMONTH": {"a": "(start_date; months)", "d": "Վերադարձնում է ամսվա վերջին օրվա հերթական համարը ամիսներից առաջ կամ հետո", "ad": "հերթական ամսաթվի համար է, որը ներկայացնում է start date-ը: start date-ից առաջ կամ հետո ամիսների թիվն է"}, "HOUR": {"a": "(serial_number)", "d": "Ժամը վերադարձնում է որպես թիվ 0-ից (12:00-ից մինչև 23:00):", "ad": "թիվն է աղյուսակի խմբագրի կողմից օգտագործվող ամսաթվի-ժամանակի կոդի կամ տեքստի ժամանակի ձևաչափով, օրինակ՝ 16:48:00 կամ 4:48:00 PM"}, "ISOWEEKNUM": {"a": "(date)", "d": "Վերադարձնում է տարվա ISO շաբաթվա համարը տվյալ ամսաթվի համար", "ad": "date-time-ի ծածկագիրն է, որն օգտագործվում է Աղյուսակների խմբագրի կողմից ամսաթվի և ժամի հաշվարկի համար"}, "MINUTE": {"a": "(serial_number)", "d": "Վերադարձնում է րոպեը՝ 0-ից մինչև 59 թիվը։", "ad": "թիվն է date-time-ի ծածկագրի, որն օգտագործվում է Աղյուսակների խմբագրի կամ տեքստի ժամանակի ձևաչափով, օրինակ՝ 16:48:00 կամ 4:48:00 PM"}, "MONTH": {"a": "(serial_number)", "d": "Վերադարձնում է ամիսը, թիվ 1-ից (հունվար) մինչև 12 (դեկտեմբեր):", "ad": "թիվն է date-time-ի ծածկագրի, որն օգտագործվում է Աղյուսակների խմբագրի կողմից"}, "NETWORKDAYS": {"a": "(start_date; end_date; [holidays])", "d": "Վերադարձնում է երկու ամսաթվերի միջև եղած ամբողջ աշխատանքային օրերի քանակը", "ad": "սերիական ամսաթվի համար է, որը ներկայացնում է start date-ը!սերիական ամսաթվի համար է, որը ներկայացնում է end date-ը!այն մեկ կամ մի քանի սերիական ամսաթվերի ընտրովի հավաքածու է, որոնք պետք է բացառվեն աշխատանքային օրացույցից, օրինակ՝ նահանգային և դաշնային, տոներ և լողացող արձակուրդներ"}, "NETWORKDAYS.INTL": {"a": "(start_date; end_date; [weekend]; [holidays])", "d": "Վերադարձնում է ամբողջ աշխատանքային օրերի թիվը երկու ամսաթվերի միջև՝ հատուկ հանգստյան օրերի պարամետրերով", "ad": "հերթական ամսաթվի համար է, որը ներկայացնում է start date-ը!Սերիական ամսաթվի համար է, որը ներկայացնում է end date-ը!Թիվ կամ տող է, որը նշում է, թե երբ են տեղի ունենում հանգստյան օրերը!մեկ կամ մի քանի սերիական ամսաթվերի կամընտիր հավաքածու է, որը պետք է բացառվի աշխատանքային օրացույցից, ինչպիսիք են նահանգային և դաշնային արձակուրդները և լողացող արձակուրդները"}, "NOW": {"a": "()", "d": "Վերադարձնում է ընթացիկ ամսաթիվը և ժամը՝ ձևաչափված որպես ամսաթիվ և ժամ:", "ad": ""}, "SECOND": {"a": "(serial_number)", "d": "Վերադարձնում է երկրորդը՝ 0-ից մինչև 59 թիվը։", "ad": "թիվն է date-time-ի ծածկագրի, որն օգտագործվում է Աղյուսակների խմբագրի կամ տեքստի ժամանակի ձևաչափով, օրինակ՝ 16:48:23 կամ 4:48:47 PM"}, "TIME": {"a": "(hour; minute; second)", "d": "Փոխակերպում է որպես թվեր տրված ժամերը, րոպեները և վայրկյանները ժամանակի ձևաչափով ձևաչափված սերիական համարի", "ad": "0-ից մինչև 23 թիվը, որը ներկայացնում է ժամը!0-ից 59-ը ներկայացնում է րոպեն!0-ից մինչև 59-ը երկրորդը ներկայացնող թիվ է"}, "TIMEVALUE": {"a": "(time_text)", "d": "Տեքստային ժամանակը որոշ ժամանակով փոխակերպում է սերիական համարի, թիվը 0-ից (12:00:00) մինչև 0,999988426 (23:59:59): Բանաձևը մուտքագրելուց հետո համարը ձևաչափեք ժամանակի ձևաչափով", "ad": "տեքստային տող է, որը ժամանակ է տալիս աղյուսակների խմբագրի ժամանակի ձևաչափերից որևէ մեկում (տողի ամսաթվի տեղեկատվությունը անտեսվում է)"}, "TODAY": {"a": "()", "d": "Վերադարձնում է ընթացիկ ամսաթիվը, որը ձևաչափված է որպես ամսաթիվ:", "ad": ""}, "WEEKDAY": {"a": "(serial_number; [return_type])", "d": "Վերադարձնում է 1-ից մինչև 7 թիվը, որը նույնացնում է ամսաթվի շաբաթվա օրը:", "ad": "թիվ է, որը ներկայացնում է ամսաթիվ!թիվ է՝ կիրակի օրվա համար=1 մինչև շաբաթ=7, օգտագործեք 1, երկուշաբթի օրվա համար=1 մինչև կիրակի=7, օգտագործեք 2, երկուշաբթի օրվա համար=0 մինչև կիրակի=6, օգտագործել 3"}, "WEEKNUM": {"a": "(serial_number; [return_type])", "d": "Վերադարձնում է տարվա համարը", "ad": "date-time-ի ծածկագիրն է, որն օգտագործվում է Աղյուսակային խմբագրի կողմից ամսաթվի և ժամի հաշվարկման համար!այն մի թիվ է (1 կամ 2), որը որոշում է վերադարձվող արժեքի տեսակը"}, "WORKDAY": {"a": "(start_date; days; [holidays])", "d": "Վերադարձնում է օրվա հերթական համարը որոշակի աշխատանքային օրերից առաջ կամ հետո", "ad": "հերթական ամսաթվի համար է, որը ներկայացնում է start date-ը!ոչ շաբաթվա վերջին և ոչ արձակուրդային օրերի թիվն է start_date-ից առաջ կամ հետո!մեկ կամ մի քանի սերիական ամսաթվերի կամընտիր զանգված է, որը պետք է բացառվի աշխատանքային օրացույցից, ինչպիսիք են՝ որպես պետական և դաշնային տոներ և լողացող տոներ"}, "WORKDAY.INTL": {"a": "(start_date; days; [weekend]; [holidays])", "d": "Վերադարձնում է օրվա սերիական համարը որոշակի աշխատանքային օրերից առաջ կամ հետո՝ հատուկ հանգստյան օրերի պարամետրերով", "ad": "հերթական ամսաթվի համարն է, որը ներկայացնում է start date-ը!ոչ հանգստյան օրերի և ոչ արձակուրդային օրերի թիվն է start_date-ից առաջ կամ հետո!այն թիվ կամ տող է, որը նշում է, թե երբ են տեղի ունենում հանգստյան օրերը!մեկ կամ մի քանի սերիայի կամընտիր զանգված է՝ աշխատանքային օրացույցից բացառելու ամսաթվերը, ինչպիսիք են նահանգային և դաշնային արձակուրդները և լողացող արձակուրդները"}, "YEAR": {"a": "(serial_number)", "d": "Վերադարձնում է ամսաթվի տարին, ամբողջ թիվ 1900-9999 միջակայքում:", "ad": "թիվն է ամսաթվի-ժամանակի կոդը, որն օգտագործվում է Աղյուսակների խմբագրի կողմից"}, "YEARFRAC": {"a": "(start_date; end_date; [basis])", "d": "Վերադարձնում է տարվա կոտորակը, որը ներկայացնում է start_date-ի և end_date-ի միջև ընկած ամբողջ օրերի թիվը", "ad": "հերթական ամսաթվի համարն է, որը ներկայացնում է start date-ը!սերիական ամսաթվի համար է, որը ներկայացնում է ավարտի ամսաթիվը!օրվա հաշվարկի հիմքն է օգտագործելու համար"}, "BESSELI": {"a": "(x; n)", "d": "Վերադարձնում է փոփոխված Bessel ֆունկցիան In(x)-ում", "ad": "արժեքն է, որով պետք է գնահատել ֆունկցիան!Բեսելի ֆունկցիայի կարգն է"}, "BESSELJ": {"a": "(x; n)", "d": "Վերադարձնում է փոփոխված Bessel ֆունկցիան In(x)-ում", "ad": "արժեքն է, որով պետք է գնահատել ֆունկցիան!Բեսելի ֆունկցիայի կարգն է"}, "BESSELK": {"a": "(x; n)", "d": "Վերադարձնում է փոփոխված Bessel ֆունկցիան Kn(x)", "ad": "արժեքն է, որով պետք է գնահատել ֆունկցիան!ֆունկցիայի հերթականությունն է"}, "BESSELY": {"a": "(x; n)", "d": "Վերադարձնում է Բեսելի Yn(x) ֆունկցիան", "ad": "արժեքն է, որով պետք է գնահատել ֆունկցիան!ֆունկցիայի հերթականությունն է"}, "BIN2DEC": {"a": "(number)", "d": "Երկուական թիվը փոխակերպում է տասնորդականի", "ad": "այն երկուական համարն է, որը ցանկանում եք փոխարկել"}, "BIN2HEX": {"a": "(number; [places])", "d": "Երկուական թիվը փոխակերպում է տասնվեցականի", "ad": "այն երկուական թիվն է, որը ցանկանում եք փոխարկել!օգտագործվող նիշերի թիվն է"}, "BIN2OCT": {"a": "(number; [places])", "d": "Երկուական թիվը փոխակերպում է օկտալի", "ad": "այն երկուական թիվն է, որը ցանկանում եք փոխարկել!օգտագործվող նիշերի թիվն է"}, "BITAND": {"a": "(number1; number2)", "d": "Վերադարձնում է երկու թվերի մի փոքր «Եվ»", "ad": "այն երկուական թվի տասնորդական ներկայացումն է, որը ցանկանում եք գնահատել!երկուական թվի տասնորդական ներկայացումն է, որը ցանկանում եք գնահատել"}, "BITLSHIFT": {"a": "(number; shift_amount)", "d": "Վերադարձնում է հերթափոխի_գումարը բիթներով տեղափոխված թիվը", "ad": "այն երկուական թվի տասնորդական ներկայացումն է, որը ցանկանում եք գնահատել!այն բիթերի թիվն է, որով ցանկանում եք տեղափոխել թողած թիվը"}, "BITOR": {"a": "(number1; number2)", "d": "Վերադարձնում է երկու թվերի մի փոքր «Կամ»:", "ad": "այն երկուական թվի տասնորդական ներկայացումն է, որը ցանկանում եք գնահատել!երկուական թվի տասնորդական ներկայացումն է, որը ցանկանում եք գնահատել"}, "BITRSHIFT": {"a": "(number; shift_amount)", "d": "Վերադարձնում է հերթափոխի_գումարը բիթերով աջ տեղափոխված թիվը", "ad": "այն երկուական թվի տասնորդական ներկայացումն է, որը ցանկանում եք գնահատել!բիթերի քանակն է, որոնցով ցանկանում եք տեղափոխել Համարը դեպի աջ"}, "BITXOR": {"a": "(number1; number2)", "d": "Վերադարձնում է երկու թվերի մի փոքր «Բացառիկ կամ»:", "ad": "այն երկուական թվի տասնորդական ներկայացումն է, որը ցանկանում եք գնահատել!երկուական թվի տասնորդական ներկայացումն է, որը ցանկանում եք գնահատել"}, "COMPLEX": {"a": "(real_num; i_num; [suffix])", "d": "Իրական և երևակայական գործակիցները վերածում է կոմպլեքս թվի", "ad": "համալիր թվի իրական գործակիցն է! համալիր թվի երևակայական գործակիցն է! համալիր թվի երևակայական բաղադրիչի վերջածանցն է"}, "CONVERT": {"a": "(number; from_unit; to_unit)", "d": "Փոխակերպում է թիվը մի չափման համակարգից մյուսը", "ad": "արժույթն է from_units-ում, որը պետք է փոխարկվի!թվի միավորներն են!արդյունքի միավորներն են"}, "DEC2BIN": {"a": "(number; [places])", "d": "Տասնորդական թիվը վերածում է երկուականի", "ad": "տասնորդական ամբողջ թիվն է, որը ցանկանում եք փոխարկել!օգտագործվող նիշերի թիվն է"}, "DEC2HEX": {"a": "(number; [places])", "d": "Տասնորդական թիվը վերածում է տասնվեցականի", "ad": "տասնորդական ամբողջ թիվն է, որը ցանկանում եք փոխարկել!օգտագործվող նիշերի թիվն է"}, "DEC2OCT": {"a": "(number; [places])", "d": "Տասնորդական թիվը վերածում է ութնյակի", "ad": "տասնորդական ամբողջ թիվն է, որը անհրաժեշտ է փոխարկել!օգտագործվող նիշերի թիվն է"}, "DELTA": {"a": "(number1; [number2])", "d": "Ստուգում է արդյոք երկու թվերը հավասար են", "ad": "առաջին համարն է!երկրորդ համարն է"}, "ERF": {"a": "(lower_limit; [upper_limit])", "d": "Վերադարձնում է սխալի գործառույթը", "ad": "ERF -ի ինտեգրման ստորին սահմանն է!ERF-ի ինտեգրման վերին սահմանն է"}, "ERF.PRECISE": {"a": "(X)", "d": "Վերադարձնում է սխալի գործառույթը", "ad": "ERF.PRECISE-ի ինտեգրման ստորին սահմանն է"}, "ERFC": {"a": "(x)", "d": "Վերադարձնում է լրացուցիչ սխալի գործառույթը", "ad": "ERF-ի ինտեգրման ստորին սահմանն է"}, "ERFC.PRECISE": {"a": "(X)", "d": "Վերադարձնում է լրացուցիչ սխալի գործառույթը", "ad": "ERFC.PRECISE-ի ինտեգրման ստորին սահմանն է"}, "GESTEP": {"a": "(number; [step])", "d": "Ստուգում է արդյոք թիվն ավելի մեծ է քան եռանիշ արժեքը", "ad": "այն քայլի համար ստուգելու արժեքն է! շեմային արժեքն է"}, "HEX2BIN": {"a": "(number; [places])", "d": "Փոխակերպում է տասնվեցական թիվը երկուականի", "ad": "այն տասնվեցական թիվն է, որը անհրաժեշտ է փոխարկել!օգտագործվող նիշերի թիվն է"}, "HEX2DEC": {"a": "(number)", "d": "Տասնվեցական թիվը վերածում է տասնորդականի", "ad": "այն տասնվեցական թիվն է, որը անհրաժեշտ է փոխարկել"}, "HEX2OCT": {"a": "(number; [places])", "d": "Տասնվեցական թիվը վերածում է ութնյակի", "ad": "այն տասնվեցական թիվն է, որը անհրաժեշտ է փոխարկել!օգտագործվող նիշերի թիվն է"}, "IMABS": {"a": "(inumber)", "d": "Վերադարձնում է կոմպլեքս թվի բացարձակ արժեքը (մոդուլը):", "ad": "համալիր թիվ է, որի համար անհրաժեշտ է  բացարձակ արժեք"}, "IMAGINARY": {"a": "(inumber)", "d": "Վերադարձնում է կոմպլեքս թվի երևակայական գործակիցը", "ad": "այն այն համալիր թիվ է, որի համար անհրաժեշտ է երևակայական գործակից"}, "IMARGUMENT": {"a": "(inumber)", "d": "Վերադարձնում է q արգումենտը՝ ռադիաններով արտահայտված անկյուն", "ad": "այն այն համալիր թիվ է, որի համար անհրաժեշտ է փաստարկ"}, "IMCONJUGATE": {"a": "(inumber)", "d": "Վերադարձնում է կոմպլեքս թվի բարդ խոնարհումը", "ad": "համալիր թիվ է, որի համար անհրաժեշտ է խոնարհում"}, "IMCOS": {"a": "(inumber)", "d": "Վերադարձնում է կոմպլեքս թվի կոսինուսը", "ad": "համալիր թիվ է, որն անհրաժեշտ է կոսինուսի համար"}, "IMCOSH": {"a": "(inumber)", "d": "Վերադարձնում է կոմպլեքս թվի հիպերբոլիկ կոսինուսը", "ad": "համալիր թիվ է, որի համար անհրաժեշտ է հիպերբոլիկ կոսինուս"}, "IMCOT": {"a": "(inumber)", "d": "Վերադարձնում է կոմպլեքս թվի կոտանգենսը", "ad": "համալիր թիվ է, որի համար անհրաժեշտ է կոտանգենս"}, "IMCSC": {"a": "(inumber)", "d": "Վերադարձնում է կոմպլեքս թվի կոսեկանտը", "ad": "համալիր թիվ է, որի համար անհրաժեշտ է կոսեկանտ"}, "IMCSCH": {"a": "(inumber)", "d": "Վերադարձնում է կոմպլեքս թվի հիպերբոլիկ կոսեկանտը", "ad": "կոմպլեքս թիվ է, որի համար անհրաժեշտ է կոսեկանտ"}, "IMDIV": {"a": "(inumber1; inumber2)", "d": "Վերադարձնում է երկու բարդ թվերի քանորդը", "ad": "համալիր համարիչն է կամ դիվիդենտը! համալիր հայտարարն է կամ բաժանարարը"}, "IMEXP": {"a": "(inumber)", "d": "Վերադարձնում է կոմպլեքս թվի էքսպոնենցիալը", "ad": "համալիր թիվ է, որի համար անհրաժեշտ է էքսպոնենցիալ"}, "IMLN": {"a": "(inumber)", "d": "Վերադարձնում է բարդ թվի բնական լոգարիթմը", "ad": "համալիր թիվ է, որի համար անհրաժեշտ է բնական լոգարիթմ"}, "IMLOG10": {"a": "(inumber)", "d": "Վերադարձնում է կոմպլեքս թվի հիմք-10 լոգարիթմը", "ad": "համալիր թիվ է, որի համար անհրաժեշտ է ընդհանուր լոգարիթմ"}, "IMLOG2": {"a": "(inumber)", "d": "Վերադարձնում է բարդ թվի հիմք-2 լոգարիթմը", "ad": "համալիր թիվ է, որի համար անհրաժեշտ է բազային-2 լոգարիթմ"}, "IMPOWER": {"a": "(inumber; number)", "d": "Վերադարձնում է կոմպլեքս թիվը, որը հասցված է ամբողջ թվի", "ad": "համալիր թիվ է, որի համար անհրաժեշտ է բարձրացնել մինչև հզորության!այն ուժն է, որի համար անհրաժեշտ է բարձրացնել կոմպլեքս թիվը"}, "IMPRODUCT": {"a": "(inumber1; [inumber2]; ...)", "d": "Վերադարձնում է 1-ից մինչև 255 կոմպլեքս թվերի արտադրյալը", "ad": "Inumber1, Inumber2,... 1-ից 255 համալիր թվեր են բազմապատկելու համար:"}, "IMREAL": {"a": "(inumber)", "d": "Վերադարձնում է բարդ թվի իրական գործակիցը", "ad": "համալիր թիվ է, որի համար անհրաժեշտ է իրական գործակից"}, "IMSEC": {"a": "(inumber)", "d": "Վերադարձնում է կոմպլեքս թվի սեկանտը", "ad": "համալիր թիվ է, որի համար անհրաժեշտ է սեկանտ"}, "IMSECH": {"a": "(inumber)", "d": "Վերադարձնում է կոմպլեքս թվի հիպերբոլիկ սեկանտը", "ad": "համալիր թիվ է, որի համար անհրաժեշտ է հիպերբոլիկ սեկանտ"}, "IMSIN": {"a": "(inumber)", "d": "Վերադարձնում է կոմպլեքս թվի սինուսը", "ad": "համալիր թիվ է, որի համար անհրաժեշտ է սինուս"}, "IMSINH": {"a": "(inumber)", "d": "Վերադարձնում է կոմպլեքս թվի հիպերբոլիկ սինուսը", "ad": "համալիր թիվ է, որի համար ցանկանանհրաժեշտ է հիպերբոլիկ սինուս"}, "IMSQRT": {"a": "(inumber)", "d": "Վերադարձնում է կոմպլեքս թվի քառակուսի արմատը", "ad": "համալիր թիվ է, որի համար անհրաժեշտ է քառակուսի արմատ"}, "IMSUB": {"a": "(inumber1; inumber2)", "d": "Վերադարձնում է երկու կոմպլեքս թվերի տարբերությունը", "ad": "համալիր թիվն է, որից պետք է հանել innuber2-ը։! Այն համալիր թիվն է, որը պետք է հանել innumber1-ից"}, "IMSUM": {"a": "(inumber1; [inumber2]; ...)", "d": "Վերադարձնում է կոմպլեքս թվերի գումարը", "ad": "1-ից 255 համալիր թվեր են ավելացնելու համար"}, "IMTAN": {"a": "(inumber)", "d": "Վերադարձնում է կոմպլեքս թվի շոշափողը", "ad": "համալիր թիվ է, որի համար անհրաժեշտ է թանգենտ"}, "OCT2BIN": {"a": "(number; [places])", "d": "Օկտալ թիվը վերածում է երկուականի", "ad": "օկտալային թիվն է, որը անհրաժեշտ է փոխարկել!օգտագործվող նիշերի թիվն է"}, "OCT2DEC": {"a": "(number)", "d": "Օկտալ թիվը վերածում է տասնորդականի", "ad": "օկտալ թիվն է, որը անհրաժեշտ է փոխարկել"}, "OCT2HEX": {"a": "(number; [places])", "d": "Օկտալ թիվը վերածում է տասնվեցականի", "ad": "օկտալային թիվն է, որը անհրաժեշտ է փոխարկել!օգտագործվող նիշերի թիվն է"}, "DAVERAGE": {"a": "(database; field; criteria)", "d": "Ցանկում կամ տվյալների բազայում սյունակի արժեքները միջինում են, որոնք համապատասխանում են ձեր նշած պայմաններին", "ad": "այն վանդկների տիրույթն է, որը կազմում է ցուցակը կամ տվյալների բազան! տվյալների բազան հարակից տվյալների ցանկն է: Այն կամ սյունակի պիտակն է կրկնակի չակերտներով, կամ թիվ, որը ներկայացնում է սյունակի դիրքը ցուցակում!վանդակների այն տիրույթն է, որը պարունակում է ձեր նշած պայմանները"}, "DCOUNT": {"a": "(database; field; criteria)", "d": "Հաշվում է տվյալների բազայի գրառումների դաշտում (սյունակում) թվեր պարունակող բջիջները, որոնք համապատասխանում են ձեր նշած պայմաններին", "ad": "այն վանդակների տիրույթն է, որը կազմում է ցուցակը կամ տվյալների բազան! տվյալների բազան հարակից տվյալների ցանկն է!այն կամ սյունակի պիտակն է կրկնակի չակերտներով, կամ թիվ, որը ներկայացնում է սյունակի դիրքը ցուցակում: Վանդակների այն տիրույթն է, որը պարունակում է ձեր նշած պայմանները"}, "DCOUNTA": {"a": "(database; field; criteria)", "d": "Հաշվում է ոչ դատարկ բջիջները տվյալների բազայի գրառումների դաշտում (սյունակում), որոնք համապատասխանում են ձեր նշած պայմաններին", "ad": "վանդակների շրջանակն է, որը կազմում է ցուցակը կամ տվյալների բազան: Տվյալների բազան հարակից տվյալների ցանկն է! կամ սյունակի պիտակն է կրկնակի չակերտների մեջ, կամ թիվ, որը ներկայացնում է սյունակի դիրքը ցանկում!վանդակների այն տիրույթն է, որը պարունակում է ձեր նշած պայմանները: Շրջանակը ներառում է սյունակի պիտակ և պայմանի համար պիտակի տակ գտնվող մեկ վանդակ"}, "DGET": {"a": "(database; field; criteria)", "d": "Տվյալների բազայից քաղում է մեկ գրառում, որը համապատասխանում է ձեր նշած պայմաններին", "ad": "վանդակների շրջանակն է, որը կազմում է ցուցակը կամ տվյալների բազան: Տվյալների բազան հարակից տվյալների ցանկն է!այն կամ սյունակի պիտակն է կրկնակի չակերտների մեջ, կամ թիվ, որը ներկայացնում է սյունակի դիրքը ցանկում!վանդակների այն տիրույթն է, որը պարունակում է ձեր նշած պայմանները: Շրջանակը ներառում է սյունակի պիտակ և պայմանի համար պիտակի տակ գտնվող մեկ վանդակ"}, "DMAX": {"a": "(database; field; criteria)", "d": "Վերադարձնում է տվյալների բազայի դաշտում (սյունակում) գրառումների ամենամեծ թիվը, որոնք համապատասխանում են ձեր նշած պայմաններին", "ad": "վանդակների շրջանակն է, որը կազմում է ցուցակը կամ տվյալների բազան: Տվյալների բազան հարակից տվյալների ցանկն է!կամ սյունակի պիտակն է կրկնակի չակերտների մեջ, կամ թիվ, որը ներկայացնում է սյունակի դիրքը ցանկում!վանդակների այն տիրույթն է, որը պարունակում է ձեր նշած պայմանները: Շրջանակը ներառում է սյունակի պիտակ և պայմանի համար պիտակի տակ գտնվող մեկ վանդակ"}, "DMIN": {"a": "(database; field; criteria)", "d": "Վերադարձնում է տվյալների բազայի դաշտում (սյունակում) ամենափոքր թիվը, որոնք համապատասխանում են ձեր նշած պայմաններին", "ad": "վանդակների շրջանակն է, որը կազմում է ցուցակը կամ տվյալների բազան: Տվյալների բազան հարակից տվյալների ցանկն է! կամ սյունակի պիտակն է կրկնակի չակերտների մեջ, կամ թիվ, որը ներկայացնում է սյունակի դիրքը ցանկում!վանդակների այն տիրույթն է, որը պարունակում է ձեր նշած պայմանները: Շրջանակը ներառում է սյունակի պիտակ և պայմանի համար պիտակի տակ գտնվող մեկ վանդակ"}, "DPRODUCT": {"a": "(database; field; criteria)", "d": "Բազմապատկում է տվյալների բազայի գրառումների դաշտի (սյունակի) արժեքները, որոնք համապատասխանում են ձեր նշած պայմաններին", "ad": "վանդակների շրջանակն է, որը կազմում է ցուցակը կամ տվյալների բազան: Տվյալների բազան հարակից տվյալների ցանկն է! կամ սյունակի պիտակն է կրկնակի չակերտների մեջ, կամ թիվ, որը ներկայացնում է սյունակի դիրքը ցանկում!վանդակների այն տիրույթն է, որը պարունակում է ձեր նշած պայմանները: Շրջանակը ներառում է սյունակի պիտակ և պիտակի տակ գտնվող մեկ վանդակ՝ պայմանի համար"}, "DSTDEV": {"a": "(database; field; criteria)", "d": "Գնահատում է ստանդարտ շեղումը տվյալների բազայի ընտրված գրառումներից նմուշի հիման վրա", "ad": "վանդակների շրջանակն է, որը կազմում է ցուցակը կամ տվյալների բազան: Տվյալների բազան հարակից տվյալների ցանկն է!կամ սյունակի պիտակն է կրկնակի չակերտների մեջ, կամ թիվ, որը ներկայացնում է սյունակի դիրքը ցանկում!վանդակների այն տիրույթն է, որը պարունակում է ձեր նշած պայմանները: Շրջանակը ներառում է սյունակի պիտակ և պայմանի համար պիտակի տակ գտնվող մեկ վանդակ"}, "DSTDEVP": {"a": "(database; field; criteria)", "d": "Հաշվում է ստանդարտ շեղումը` հիմնվելով տվյալների բազայի ընտրված գրառումների ամբողջ բնակչության վրա", "ad": "վանդակների շրջանակն է, որը կազմում է ցուցակը կամ տվյալների բազան: Տվյալների բազան հարակից տվյալների ցանկն է!կամ սյունակի պիտակն է կրկնակի չակերտների մեջ, կամ թիվ, որը ներկայացնում է սյունակի դիրքը ցանկում!վանդակների այն տիրույթն է, որը պարունակում է ձեր նշած պայմանները: Շրջանակը ներառում է սյունակի պիտակ և պայմանի համար պիտակի տակ գտնվող մեկ վանդակ"}, "DSUM": {"a": "(database; field; criteria)", "d": "Տվյալների բազայի գրառումների դաշտում (սյունակում) ավելացնում է այն թվերը, որոնք համապատասխանում են ձեր նշած պայմաններին", "ad": "վանդակների շրջանակն է, որը կազմում է ցուցակը կամ տվյալների բազան: Տվյալների բազան հարակից տվյալների ցանկն է!կամ սյունակի պիտակն է կրկնակի չակերտների մեջ, կամ թիվ, որը ներկայացնում է սյունակի դիրքը ցանկում!վանդակների այն տիրույթն է, որը պարունակում է ձեր նշած պայմանները: Շրջանակը ներառում է սյունակի պիտակ և պայմանի համար պիտակի տակ գտնվող մեկ վանդակ"}, "DVAR": {"a": "(database; field; criteria)", "d": "Գնահատում է շեղումը տվյալների բազայի ընտրված գրառումներից նմուշի հիման վրա", "ad": "վանդակների շրջանակն է, որը կազմում է ցուցակը կամ տվյալների բազան: Տվյալների բազան հարակից տվյալների ցանկն է!կամ սյունակի պիտակն է կրկնակի չակերտների մեջ, կամ թիվ, որը ներկայացնում է սյունակի դիրքը ցանկում!վանդակների այն տիրույթն է, որը պարունակում է ձեր նշած պայմանները: Շրջանակը ներառում է սյունակի պիտակ և պայմանի համար պիտակի տակ գտնվող մեկ վանդակ"}, "DVARP": {"a": "(database; field; criteria)", "d": "Հաշվում է շեղումները՝ հիմնվելով տվյալների բազայի ընտրված գրառումների ամբողջ պոպուլյացիայի վրա", "ad": "վանդակներ շրջանակն է, որը կազմում է ցուցակը կամ տվյալների բազան: Տվյալների բազան հարակից տվյալների ցանկն է!կամ սյունակի պիտակն է կրկնակի չակերտների մեջ, կամ թիվ, որը ներկայացնում է սյունակի դիրքը ցանկում!վանդակների այն տիրույթն է, որը պարունակում է ձեր նշած պայմանները: Շրջանակը ներառում է սյունակի պիտակ և պայմանի համար պիտակի տակ գտնվող մեկ վանդակ"}, "CHAR": {"a": "(number)", "d": "Վերադարձնում է կոդի համարով նշված նիշը ձեր համակարգչի համար նախատեսված նիշերից", "ad": "1-ից 255-ի միջև ընկած թիվ է, որը նշում է, թե որ նիշն եք ցանկանում"}, "CLEAN": {"a": "(text)", "d": "Հեռացնում է բոլոր չտպվող նիշերը տեքստից", "ad": "աշխատաթերթի ցանկացած տեղեկատվություն է, որից ցանկանում եք հեռացնել տպագրվող չտպվող նիշերը"}, "CODE": {"a": "(text)", "d": "Վերադարձնում է տեքստային տողի առաջին նիշի թվային կոդը՝ ձեր համակարգչի կողմից օգտագործվող նիշերի հավաքածուում", "ad": "այն տեքստն է, որի համար ցանկանում եք առաջին նիշի կոդը"}, "CONCATENATE": {"a": "(text1; [text2] ...)", "d": "Միացնում է մի քանի տեքստային տողեր մեկ տեքստային տողի մեջ", "ad": "1-ից 255 տեքստային տողեր են, որոնք պետք է միացվեն մեկ տեքստային տողի մեջ և կարող են լինել տեքստային տողեր, թվեր կամ մեկ վանդակի հղումներ"}, "CONCAT": {"a": "(text1; ...)", "d": "Միացնում է տեքստային տողերի ցանկը կամ տիրույթը", "ad": "1-ից 254 տեքստային տողեր կամ տիրույթներ են, որոնք պետք է միացվեն մեկ տեքստային տողի"}, "DOLLAR": {"a": "(number; [decimals])", "d": "Թիվը փոխակերպում է տեքստի՝ օգտագործելով արժույթի ձևաչափը", "ad": "թիվ է, թվանշան պարունակող վանդակի հղում կամ թվին գնահատող բանաձև! տասնորդական կետի աջ կողմում գտնվող թվանշանների թիվն է: Թիվը կլորացվում է ըստ անհրաժեշտության. եթե բաց թողնվի, տասնորդական = 2"}, "EXACT": {"a": "(text1; text2)", "d": "Ստուգում է՝ արդյոք երկու տեքստային տողերը միանգամայն նույնն են, և վերադարձնում է TRUE կամ FALSE: EXACT-ը մեծատառերի նկատմամբ զգայուն է", "ad": "տեքստի առաջին տողն է: Երկրորդ տեքստային տողն է"}, "FIND": {"a": "(find_text; within_text; [start_num])", "d": "Վերադարձնում է մեկ տեքստային տողի մեկնարկային դիրքը մեկ այլ տեքստային տողի մեջ: FIND-ը մեծատառերի նկատմամբ զգայուն է", "ad": "այն տեքստն է, որը ցանկանում եք գտնել: Օգտագործեք կրկնակի չակերտներ (դատարկ տեքստ)՝ Within_text-ի առաջին նիշին համապատասխանելու համար; նիշերը թույլատրված չեն! այն տեքստն է, որը պարունակում է այն տեքստը, որը ցանկանում եք գտնել! նշում է նիշը, որով պետք է սկսել որոնումը: Within_text-ի առաջին նիշը նիշ թիվ 1 է: Եթե բաց թողնվի, Start_num = 1"}, "FINDB": {"a": "(string-1; string-2; [start-pos])", "d": "Գտնում է նշված ենթատողը (string-1) տողի մեջ (string-2) և նախատեսված է երկբայթանոց նիշերի հավաքածուի (DBCS) լեզուների համար, ինչպիսիք են ճապոներենը, չինարենը, կորեերենը և այլն:", "ad": "այն տեքստն է, որը ցանկանում եք գտնել: Օգտագործեք կրկնակի չակերտներ (դատարկ տեքստ)՝ տող-2-ի առաջին նիշին համապատասխանելու համար; նիշերը թույլատրված չեն! այն տեքստն է, որը պարունակում է այն տեքստը, որը ցանկանում եք գտնել! նշում է նիշը, որով պետք է սկսել որոնումը: Տող-2-ի առաջին նիշը նիշ թիվ 1 է: Եթե բաց թողնվի, start-pos = 1"}, "FIXED": {"a": "(number; [decimals]; [no_commas])", "d": "Կլորացնում է թիվը մինչև սահմանված տասնորդական թիվը և արդյունքը վերադարձնում է որպես տեքստ՝ ստորակետերով կամ առանց ստորակետներով", "ad": "այն թիվն է, որը ցանկանում եք կլորացնել և վերածել տեքստի! տասնորդական կետի աջ կողմում գտնվող թվանշանների թիվն է: Եթե բաց թողնված է, տասնորդական = 2!-ը տրամաբանական արժեք է. վերադարձված տեքստում ստորակետներ մի ցուցադրեք = TRUE; ցուցադրեք ստորակետները վերադարձված տեքստում = FALSE կամ բաց թողնված"}, "LEFT": {"a": "(text; [num_chars])", "d": "Վերադարձնում է տեքստային տողի սկզբից նիշերի նշված թիվը", "ad": "այն տեքստային տողն է, որը պարունակում է այն նիշերը, որոնք ցանկանում եք հանել!նշում է, թե քանի նիշ եք ուզում հանել LEFT-ը; 1, եթե բաց թողնվի"}, "LEFTB": {"a": "(string; [number-chars])", "d": "Հանում է ենթատողը նշված տողից՝ սկսած ձախ գրանշանից և նախատեսված է լեզուների համար, որոնք օգտագործում են կրկնակի բայթ նիշերի հավաքածու (DBCS), ինչպիսիք են ճապոներենը, չինարենը, կորեերենը և այլն:", "ad": "տեքստային տող է, որը պարունակում է այն նիշերը, որոնք ցանկանում եք հանել! նշում է, թե քանի նիշ եք ուզում հանել LEFTB-ը; 1, եթե բաց թողնվի"}, "LEN": {"a": "(text)", "d": "Վերադարձնում է տեքստային տողի նիշերի քանակը", "ad": "այն տեքստն է, որի երկարությունը ցանկանում եք գտնել:Բացատները հաշվվում են որպես նիշ"}, "LENB": {"a": "(string)", "d": "Վերլուծում է նշված տողը և վերադարձնում դրա պարունակած նիշերի քանակը և նախատեսված է լեզուների համար, որոնք օգտագործում են կրկնակի բայթ նիշերի հավաքածու (DBCS), ինչպիսիք են ճապոներենը, չինարենը, կորեերենը և այլն:", "ad": "այն տեքստն է, որի երկարությունը ցանկանում եք գտնել: Բացատները հաշվվում են որպես նիշ"}, "LOWER": {"a": "(text)", "d": "Փոխակերպում է տեքստային տողի բոլոր տառերը փոքրատառերի", "ad": "այն տեքստն է, որը ցանկանում եք վերածել փոքրատառի: Տեքստի նիշերը, որոնք տառեր չեն, չեն փոխվում"}, "MID": {"a": "(text; start_num; num_chars)", "d": "Վերադարձնում է նիշերը տեքստային տողի կեսից՝ հաշվի առնելով մեկնարկային դիրքը և երկարությունը", "ad": "տեքստային տող է, որից ցանկանում եք հանել նիշերը!այն առաջին նիշի դիրքն է, որը ցանկանում եք հանել: Տեքստի առաջին նիշը 1 է!նշում է, թե քանի նիշ պետք է վերադարձվի Տեքստից"}, "MIDB": {"a": "(string; start-pos; number-chars)", "d": "Հանում է նիշերը նշված տողից սկսած ցանկացած դիրքից և նախատեսված է լեզուների համար, որոնք օգտագործում են կրկնակի բայթ նիշերի հավաքածու (DBCS), ինչպիսիք են ճապոներենը, չինարենը, կորեերենը և այլն:0", "ad": "տեքստային տող է, որից ցանկանում եք հանել նիշերը!այն առաջին նիշի դիրքն է, որը ցանկանում եք հանել: String-ի առաջին նիշը 1 է! նշում է, թե քանի նիշ պետք է վերադարձվի String-ից"}, "NUMBERVALUE": {"a": "(text; [decimal_separator]; [group_separator])", "d": "Փոխակերպում է տեքստը թվի տեղականից անկախ եղանակով", "ad": "այն տողը, որը ներկայացնում է այն թիվը, որը ցանկանում եք փոխարկել!նիշն է, որն օգտագործվում է որպես տողի տասնորդական բաժանարար!նիշն է, որն օգտագործվում է որպես խմբի բաժանարար տողի մեջ"}, "PROPER": {"a": "(text)", "d": "Փոխակերպում է տեքստային տողը պատշաճ գործի; յուրաքանչյուր բառի առաջին տառը մեծատառ, իսկ մնացած բոլոր տառերը՝ փոքրատառ", "ad": "տեքստ է, որը փակցված է չակերտների մեջ, բանաձև, որը վերադարձնում է տեքստը կամ հղում վանդակին, որը պարունակում է տեքստ՝ մասամբ մեծատառելու համար"}, "REPLACE": {"a": "(old_text; start_num; num_chars; new_text)", "d": "Փոխարինում է տեքստային տողի մի մասը այլ տեքստային տողով", "ad": "տեքստ է, որում ցանկանում եք փոխարինել որոշ նիշեր!Old_text-ի նիշի դիրքն է, որը ցանկանում եք փոխարինել New_text-ով!Old_text-ի նիշերի թիվն է, որը ցանկանում եք փոխարինել! այն տեքստն է, որը փոխարինելու է Old_text-ի նիշերին"}, "REPLACEB": {"a": "(string-1; start-pos; number-chars; string-2)", "d": "Նիշերի շարքը, որը հիմնված է ձեր նշած նիշերի քանակի և մեկնարկային դիրքի վրա, փոխարինում է նոր նիշերով և նախատեսված է այն լեզուների համար, որոնք օգտագործում են կրկնակի բայթ նիշերի հավաքածու (DBCS), ինչպիսիք են ճապոներենը, չինարենը, կորեերենը և այլն:", "ad": "տեքստ է, որում ցանկանում եք փոխարինել որոշ նիշեր! String-1-ի կերպարի դիրքն է, որը ցանկանում եք փոխարինել String-2-ով! String-1-ի նիշերի թիվն է, որը ցանկանում եք փոխարինել! այն տեքստն է, որը կփոխարինի String-1-ի նիշերին"}, "REPT": {"a": "(text; number_times)", "d": "Կրկնում է տեքստը որոշակի քանակությամբ անգամ: Օգտագործեք REPT՝ բջիջը տեքստային տողի մի շարք օրինակներով լցնելու համար", "ad": "այն տեքստն է, որը ցանկանում եք կրկնել!դրական թիվ է, որը նշում է տեքստը կրկնելու կրկնությունների քանակը"}, "RIGHT": {"a": "(text; [num_chars])", "d": "Վերադարձնում է նշված թվով նիշերը տեքստային տողի վերջից", "ad": "տեքստային տող է, որը պարունակում է այն նիշերը, որոնք ցանկանում եք հանել! նշում է, թե քանի նիշ եք ուզում ցանկանում, 1, եթե բաց թողնվի"}, "RIGHTB": {"a": "(string; [number-chars])", "d": "Տողից հանում է ենթատողը՝ սկսած ամենաաջ նիշից՝ հիմնվելով նիշերի նշված քանակի վրա և նախատեսված է լեզուների համար, որոնք օգտագործում են կրկնակի բայթ նիշերի հավաքածու (DBCS), ինչպիսիք են ճապոներենը, չինարենը, կորեերենը և այլն:", "ad": "տեքստային տող է, որը պարունակում է այն նիշերը, որոնք ցանկանում եք հանել!նշում է, թե քանի նիշ եք ուզում հանել, 1, եթե բաց թողնվի"}, "SEARCH": {"a": "(find_text; within_text; [start_num])", "d": "Վերադարձնում է այն նիշի համարը, որով առաջին անգամ գտնվել է կոնկրետ նիշ կամ տեքստային տող՝ կարդալով ձախից աջ (չզգայուն մեծատառերը)", "ad": "այն տեքստն է, որը ցանկանում եք գտնել: Դուք կարող եք օգտագործել ? և * wildcard նիշեր; օգտագործել ~? և ~* գտնել ? և * նիշերը! այն տեքստն է, որում ցանկանում եք փնտրել Find_text!-ը նիշերի թիվն է Within_text-ում, ձախից հաշված, որտեղից ցանկանում եք սկսել որոնումը: Բաց թողնելու դեպքում օգտագործվում է 1"}, "SEARCHB": {"a": "(string-1; string-2; [start-pos])", "d": "Վերադարձնում է նշված ենթատողի գտնվելու վայրը տողի մեջ և նախատեսված է լեզուների համար, որոնք օգտագործում են կրկնակի բայթ նիշերի հավաքածու (DBCS), ինչպիսիք են ճապոներենը, չինարենը, կորեերենը և այլն:", "ad": "այն տեքստն է, որը ցանկանում եք գտնել: Դուք կարող եք օգտագործել ? և * wildcard նիշեր; օգտագործել ~? և ~* գտնել ? և * նիշերը! այն տեքստն է, որում ցանկանում եք փնտրել String-1! String-2-ում ձախից հաշվվող նիշերի թիվն է, որից ցանկանում եք սկսել որոնումը. Բաց թողնելու դեպքում օգտագործվում է 1"}, "SUBSTITUTE": {"a": "(text; old_text; new_text; [instance_num])", "d": "Փոխարինում է գոյություն ունեցող տեքստը նոր տեքստով տեքստային տողի մեջ", "ad": "տեքստն է կամ հղումը տեքստ պարունակող վանդակին, որտեղ ցանկանում եք փոխարինել նիշերը! գոյություն ունեցող տեքստն է, որը ցանկանում եք փոխարինել: Եթե Old_text-ի մեծատառը չի համընկնում տեքստի մեծատառի հետ, SUBSTITUTE-ը չի փոխարինի տեքստը! այն տեքստն է, որով ցանկանում եք փոխարինել Old_text-ը! նշում է Old_text-ի որ տարբերակն եք ցանկանում փոխարինել: Եթե բաց թողնվի, Old_text-ի յուրաքանչյուր օրինակ փոխարինվում է"}, "T": {"a": "(value)", "d": "Ստուգում է արդյոք արժեքը տեքստ է, և վերադարձնում է տեքստը, եթե դա է, կամ վերադարձնում է կրկնակի չակերտներ (դատարկ տեքստ), եթե դա չէ:", "ad": "փորձարկման արժեքն է"}, "TEXT": {"a": "(value; format_text)", "d": "Փոխակերպում է արժեքը տեքստի որոշակի թվային ձևաչափով", "ad": "թիվ է, բանաձև, որը գնահատվում է թվային արժեքի կամ թվային արժեք պարունակող վանդակի հղում! Թվերի ձևաչափ է տեքստային ձևով՝ Format Cells dialog box-ի Number ներդիրի Category box-ից"}, "TEXTJOIN": {"a": "(delimiter; ignore_empty; text1; ...)", "d": "Միացնում է տեքստային տողերի ցանկը կամ տիրույթը՝ օգտագործելով սահմանազատիչ", "ad": "Նիշ կամ տող՝ յուրաքանչյուր տեքստի տարրի միջև տեղադրելու համար! եթե TRUE (լռելյայն), անտեսում է դատարկ վանդակները: 1-ից 252 տեքստային տողեր կամ տիրույթներ պետք է միանան"}, "TRIM": {"a": "(text)", "d": "Հեռացնում է բոլոր բացատները տեքստային տողից, բացառությամբ բառերի միջև եղած մեկ բացատների", "ad": "այն տեքստն է, որից ցանկանում եք հեռացնել բացատները"}, "UNICHAR": {"a": "(number)", "d": "Վերադարձնում է Յունիկոդի նիշը, որը վկայակոչվում է տվյալ թվային արժեքով", "ad": "Յունիկոդի թիվն է, որը ներկայացնում է նիշ"}, "UNICODE": {"a": "(text)", "d": "Վերադարձնում է տեքստի առաջին նիշին համապատասխան համարը (կոդային կետը):", "ad": "այն նիշն է, որի արժեքը ցանկանում եք ստանալ Unicode-ից"}, "UPPER": {"a": "(text)", "d": "Փոխակերպում է տեքստային տողը բոլոր մեծատառերի", "ad": "այն տեքստն է, որը ցանկանում եք վերածել մեծատառի, հղում կամ տեքստային տողի"}, "VALUE": {"a": "(text)", "d": "Թիվը ներկայացնող տեքստային տողը վերածում է թվի", "ad": "չակերտների մեջ փակցված տեքստ է կամ հղում վանդակին, որը պարունակում է այն տեքստը, որը ցանկանում եք փոխարկել"}, "AVEDEV": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է տվյալների կետերի բացարձակ շեղումների միջինը դրանց միջինից: Փաստարկները կարող են լինել թվեր կամ անուններ, զանգվածներ կամ թվեր պարունակող հղումներ", "ad": "1-ից 255 փաստարկներ են, որոնց համար ցանկանում եք բացարձակ շեղումների միջի"}, "AVERAGE": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է իր արգումենտների միջինը (թվաբանական միջինը), որոնք կարող են լինել թվեր կամ անուններ, զանգվածներ կամ թվեր պարունակող հղումներ։", "ad": "1-ից 255 թվային փաստարկներ են, որոնց համար ցանկանում եք միջինը"}, "AVERAGEA": {"a": "(value1; [value2]; ...)", "d": "Վերադարձնում է իր արգումենտների միջինը (միջին թվաբանականը)՝ տեքստը և FALSE-ը արգումենտներում գնահատելով 0; TRUE գնահատվում է որպես 1: Փաստարկները կարող են լինել թվեր, անուններ, զանգվածներ կամ հղումներ", "ad": "1-ից 255 փաստարկներ են, որոնց համար ցանկանում եք միջինը"}, "AVERAGEIF": {"a": "(range; criteria; [average_range])", "d": "Գտնում է միջինը (թվաբանական միջինը) տվյալ պայմանով կամ չափանիշներով նշված բջիջների համար", "ad": "այն վանդակների տիրույթն է, որը ցանկանում եք գնահատել!այն պայմանն է կամ չափանիշը թվի, արտահայտության կամ տեքստի տեսքով, որը սահմանում է, թե որ վանդակներն են օգտագործվելու միջինը գտնելու համար!իրական վանդակներն են, որոնք պետք է օգտագործվեն վանդակներն գտնելու համար: Բաց թողնելու դեպքում օգտագործվում են միջակայքի վանդակները"}, "AVERAGEIFS": {"a": "(average_range; criteria_range; criteria; ...)", "d": "Գտնում է միջինը (թվաբանական միջինը) տվյալ պայմանների կամ չափանիշների կողմից նշված բջիջների համար", "ad": "այն իրական վանդակներն են, որոնք պետք է օգտագործվեն միջինը գտնելու համար!այն վանդակների շրջանակն է, որը ցանկանում եք գնահատել տվյալ պայմանի համար!պայման կամ չափանիշ է թվի, արտահայտության կամ տեքստի տեսքով, որը սահմանում է, թե որ վանդակներն են օգտագործվելու: գտնել միջինը"}, "BETADIST": {"a": "(x; alpha; beta; [A]; [B])", "d": "Վերադարձնում է կուտակային բետա հավանականության խտության ֆունկցիան", "ad": "A-ի և B-ի միջև եղած արժեքն է, որով պետք է գնահատել ֆունկցիան!բաշխման պարամետր է և պետք է լինի 0-ից մեծ!բաշխման պարամետր է և պետք է լինի 0-ից մեծ! կամընտիր ստորին սահման է x-ի միջակայքի համար: Եթե բաց թողնվի, A = 0!կամընտիր վերին սահման է x-ի միջակայքին: Եթե բաց թողնվի, B = 1"}, "BETAINV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "Վերադարձնում է կուտակային բետա հավանականության խտության ֆունկցիայի հակադարձը (BETADIST)", "ad": "բետա բաշխման հետ կապված հավանականություն է!բաշխման պարամետր է և պետք է լինի 0-ից մեծ!բաշխման պարամետր է և պետք է լինի 0-ից մեծ! կամընտիր ստորին սահման է x-ի միջակայքի համար: Եթե բաց թողնվի, A = 0!կամընտիր վերին սահման է x-ի միջակայքին: Եթե բաց թողնվի, B = 1"}, "BETA.DIST": {"a": "(x; alpha; beta; cumulative; [A]; [B])", "d": "Վերադարձնում է բետա հավանականության բաշխման ֆունկցիան", "ad": "A-ի և B-ի միջև եղած արժեքն է, որով կարելի է գնահատել ֆունկցիան! բաշխման պարամետր է և պետք է լինի 0-ից մեծ! բաշխման պարամետր է և պետք է լինի 0-ից մեծ!տրամաբանական արժեք է՝ կուտակային բաշխման ֆունկցիայի համար օգտագործեք TRUE; հավանականության խտության ֆունկցիայի համար օգտագործեք FALSE!կամընտիր ստորին սահման է x-ի միջակայքին: Եթե բաց թողնվի, A = 0! կամընտիր վերին սահման է x-ի միջակայքին: Եթե բաց թողնվի, B = 1"}, "BETA.INV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "Վերադարձնում է կուտակային բետա հավանականության խտության ֆունկցիայի հակադարձը (BETA.DIST)", "ad": "բետա բաշխման հետ կապված հավանականություն է! բաշխման պարամետր է և պետք է լինի 0-ից մեծ, բաշխման պարամետր է և պետք է լինի 0ից մեծ!կամընտիր ստորին սահման է x-ի միջակայքին: Եթե բաց թողնվի, A = 0! կամընտիր վերին սահման է x-ի միջակայքին: Եթե բաց թողնվի, B = 1"}, "BINOMDIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "Վերադարձնում է անհատական ​​տերմինի երկանդամ բաշխման հավանականությունը", "ad": "փորձությունների մեջ հաջողությունների թիվն է!անկախ փորձությունների թիվն է! յուրաքանչյուր փորձարկման ժամանակ հաջողության հասնելու հավանականությունը! տրամաբանական արժեք է՝կուտակային բաշխման ֆունկցիայի համար օգտագործեք TRUE; հավանականության զանգվածի ֆունկցիայի համար օգտագործեք FALSE"}, "BINOM.DIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "Վերադարձնում է անհատական ​​տերմինի երկանդամ բաշխման հավանականությունը", "ad": "փորձարկումներում հաջողությունների թիվն է!անկախ փորձարկումների թիվն է! յուրաքանչյուր փորձարկման հաջողության հավանականությունը!տրամաբանական արժեք է՝կուտակային բաշխման ֆունկցիայի համար օգտագործեք TRUE; հավանականության զանգվածի ֆունկցիայի համար օգտագործեք FALSE"}, "BINOM.DIST.RANGE": {"a": "(trials; probability_s; number_s; [number_s2])", "d": "Վերադարձնում է փորձարկման արդյունքի հավանականությունը՝ օգտագործելով երկանդամ բաշխումը", "ad": "անկախ փորձարկումների թիվն է! յուրաքանչյուր փորձարկման հաջողության հավանականությունն է!փորձարկումների հաջողության թիվն է!եթե այս ֆունկցիան վերադարձնում է հավանականությունը, որ հաջող փորձարկումների թիվը պետք է լինի թվերի_s և_s2-ի միջև"}, "BINOM.INV": {"a": "(trials; probability_s; alpha)", "d": "Վերադարձնում է ամենափոքր արժեքը, որի համար կուտակային երկանդամ բաշխումը մեծ է կամ հավասար է չափանիշի արժեքին", "ad": "Բեռնուլիի փորձարկումների թիվն է! յուրաքանչյուր փորձարկումում հաջողության հասնելու հավանականությունն է, 0-ից 1-ի միջև ներառյալ թիվը!չափանիշի արժեքն է, 0-ից 1-ի միջև ներառյալ թիվն է"}, "CHIDIST": {"a": "(x; deg_freedom)", "d": "Վերադարձնում է խի-քառակուսի բաշխման աջակողմյան հավանականությունը", "ad": "այն արժեքն է, որով ցանկանում եք գնահատել բաշխումը, ոչ բացասական թիվն է!ազատության աստիճանների թիվն է, թիվ 1-ից 10^10-ի միջակայքում՝ բացառելով 10^10-ը"}, "CHIINV": {"a": "(probability; deg_freedom)", "d": "Վերադարձնում է խի-քառակուսի բաշխման աջակողմյան հավանականության հակադարձը", "ad": "հավանականություն է, որը կապված է խի-քառակուսի բաշխման հետ, արժեքը 0-ի և 1-ի միջև ներառյալ:!ազատության աստիճանների թիվն է, թիվ 1-ից մինչև 10^10, բացառությամբ 10^10-ի"}, "CHITEST": {"a": "(actual_range; expected_range)", "d": "Վերադարձնում է անկախության թեստը. վիճակագրության ք-քառակուսի բաշխման արժեքը և ազատության համապատասխան աստիճանները", "ad": "տվյալների տիրույթն է, որը պարունակում է դիտարկումներ՝ ակնկալվող արժեքների դեմ փորձարկելու համար! այն տվյալների տիրույթն է, որը պարունակում է տողերի և սյունակների հանրագումարների արտադրյալի հարաբերակցությունը մեծ ընդհանուրին"}, "CHISQ.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "Վերադարձնում է խի-քառակուսի բաշխման ձախակողմյան հավանականությունը", "ad": "այն արժեքն է, որով ցանկանում եք գնահատել բաշխումը, ոչ բացասական թիվ! Ազատության աստիճանների թիվն է, թիվ 1-ի և 10^10-ի միջև, բացառելով 10^10!-ը վերադարձվող ֆունկցիայի տրամաբանական արժեքն է՝ կուտակային բաշխման ֆունկցիան = TRUE; հավանականության խտության ֆունկցիան = FALSE"}, "CHISQ.DIST.RT": {"a": "(x; deg_freedom)", "d": "Վերադարձնում է խի-քառակուսի բաշխման աջակողմյան հավանականությունը", "ad": "այն արժեքն է, որով ցանկանում եք գնահատել բաշխումը, ոչ բացասական թիվ! ազատության աստիճանների թիվն է՝ 1-ից 10^10-ի միջակայքում գտնվող թիվ՝ չհաշված 10^10-ը"}, "CHISQ.INV": {"a": "(probability; deg_freedom)", "d": "Վերադարձնում է խի-քառակուսի բաշխման ձախակողմյան հավանականության հակառակը", "ad": "հավանականություն է, որը կապված է խի-քառակուսի բաշխման հետ, արժեք 0-ից 1-ը ներառյալ! ազատության աստիճանների թիվն է՝ 1-ից 10^10-ի միջակայքում գտնվող թիվ՝ չհաշված 10^10-ը"}, "CHISQ.INV.RT": {"a": "(probability; deg_freedom)", "d": "Վերադարձնում է խի-քառակուսի բաշխման աջակողմյան հավանականության հակադարձը", "ad": "հավանականություն է, որը կապված է chi-squared բաշխման հետ, արժեք 0-ից 1-ը ներառյալ!ազատության աստիճանների թիվն է՝ 1-ից 10^10-ի միջակայքում գտնվող թիվ՝ չհաշված 10^10-ը"}, "CHISQ.TEST": {"a": "(actual_range; expected_range)", "d": "Վերադարձնում է անկախության թեստը. վիճակագրության ք-քառակուսի բաշխման արժեքը և ազատության համապատասխան աստիճանները", "ad": "տվյալների տիրույթն է, որը պարունակում է դիտարկումներ՝ ակնկալվող արժեքների դեմ փորձարկելու համար! այն տվյալների տիրույթն է, որը պարունակում է տողերի և սյունակների հանրագումարների արտադրյալի հարաբերակցությունը մեծ ընդհանուրին"}, "CONFIDENCE": {"a": "(alpha; standard_dev; size)", "d": "Վերադարձնում է վստահության միջակայքը պոպուլյացիայի միջինի համար՝ օգտագործելով նորմալ բաշխում", "ad": "նշանակալիության մակարդակն է, որն օգտագործվում է վստահության մակարդակը հաշվարկելու համար՝ 0-ից մեծ և 1-ից փոքր թիվ! տվյալների տիրույթի ամբողջության ստանդարտ շեղումն է և ենթադրվում է, որ հայտնի է: Standard_dev-ը պետք է լինի 0-ից մեծ! նմուշի չափն է"}, "CONFIDENCE.NORM": {"a": "(alpha; standard_dev; size)", "d": "Վերադարձնում է վստահության միջակայքը պոպուլյացիայի միջինի համար՝ օգտագործելով նորմալ բաշխում", "ad": "նշանակալիության մակարդակն է, որն օգտագործվում է վստահության մակարդակը հաշվարկելու համար՝ 0-ից մեծ և 1-ից փոքր թիվ! տվյալների տիրույթի ամբողջության ստանդարտ շեղումն է և ենթադրվում է, որ հայտնի է: Standard_dev-ը պետք է լինի 0-ից մեծ! նմուշի չափն է"}, "CONFIDENCE.T": {"a": "(alpha; standard_dev; size)", "d": "Վերադարձնում է վստահության միջակայքը պոպուլյացիայի միջինի համար՝ օգտագործելով Student's T բաշխումը", "ad": "նշանակալիության մակարդակն է, որն օգտագործվում է վստահության մակարդակը հաշվարկելու համար՝ 0-ից մեծ և 1-ից փոքր թիվ! տվյալների տիրույթի բնակչության ստանդարտ շեղումն է և ենթադրվում է, որ հայտնի է: Standard_dev-ը պետք է լինի 0-ից մեծ! նմուշի չափն է"}, "CORREL": {"a": "(array1; array2)", "d": "Վերադարձնում է երկու տվյալների հավաքածուների հարաբերակցության գործակիցը", "ad": "արժեքների վանդակների միջակայք է: Արժեքները պետք է լինեն թվեր, անուններ, զանգվածներ կամ թվեր պարունակող հղումներ! արժեքների երկրորդ վանդակների միջակայքն է: Արժեքները պետք է լինեն թվեր, անուններ, զանգվածներ կամ թվեր պարունակող հղումներ"}, "COUNT": {"a": "(value1; [value2]; ...)", "d": "Հաշվում է թվեր պարունակող տիրույթի բջիջների քանակը", "ad": "1-ից 255 արգումենտներ են, որոնք կարող են պարունակել կամ վերաբերել տարբեր տեսակի տվյալների, բայց հաշվվում են միայն թվեր"}, "COUNTA": {"a": "(value1; [value2]; ...)", "d": "Հաշվում է տիրույթի բջիջների թիվը, որոնք դատարկ չեն", "ad": "1-ից 255 արգումենտներ են, որոնք ներկայացնում են այն արժեքներն ու վանդակները, որոնք ցանկանում եք հաշվել: Արժեքները կարող են լինել ցանկացած տեսակի տեղեկատվություն"}, "COUNTBLANK": {"a": "(range)", "d": "Հաշվում է դատարկ բջիջների թիվը բջիջների որոշակի տիրույթում", "ad": "այն միջակայքն է, որտեղից ցանկանում եք հաշվել դատարկ վանդակները"}, "COUNTIF": {"a": "(range; criteria)", "d": "Հաշվում է տիրույթի բջիջների թիվը, որոնք համապատասխանում են տվյալ պայմանին", "ad": "վանդակների այն տիրույթն է, որտեղից ցանկանում եք հաշվել ոչ դատարկ վանդակները! պայման է թվի, արտահայտության կամ տեքստի տեսքով, որը սահմանում է, թե որ վանդակները պետք է հաշվվեն"}, "COUNTIFS": {"a": "(criteria_range; criteria; ...)", "d": "Հաշվում է բջիջների քանակը, որոնք նշված են որոշակի պայմանների կամ չափանիշների համաձայն", "ad": "այն վանդակների շրջանակն է, որը ցանկանում եք գնահատել տվյալ պայմանի համար! պայման է թվի, արտահայտության կամ տեքստի տեսքով, որը սահմանում է, թե որ վանդակները պետք է հաշվվեն"}, "COVAR": {"a": "(array1; array2)", "d": "Վերադարձնում է կովարիանսը, շեղումների արտադրյալների միջինը տվյալների կետերի յուրաքանչյուր զույգի համար երկու տվյալների հավաքածուներում", "ad": "ամբողջ թվերի առաջին վանդակների միջակայքն է և պետք է լինի թվեր, զանգվածներ կամ թվեր պարունակող հղումներ! ամբողջ թվերի երկրորդ վանդակների տիրույթն է և պետք է լինի թվեր, զանգվածներ կամ թվեր պարունակող հղումներ"}, "COVARIANCE.P": {"a": "(array1; array2)", "d": "Վերադարձնում է բնակչության կովարիանսը, շեղումների արտադրյալների միջինը տվյալների յուրաքանչյուր զույգի համար երկու տվյալների հավաքածուներում", "ad": "ամբողջ թվերի առաջին վանդակների տիրույթն է և պետք է լինի թվեր, զանգվածներ կամ թվեր պարունակող հղումներ!Ամբողջ թվերի երկրորդ վանդակների տիրույթն է և պետք է լինի թվեր, զանգվածներ կամ թվեր պարունակող հղումներ"}, "COVARIANCE.S": {"a": "(array1; array2)", "d": "Վերադարձնում է նմուշի կովարիանսը, շեղումների արտադրյալների միջինը տվյալների կետերի յուրաքանչյուր զույգի համար երկու տվյալների հավաքածուներում", "ad": "ամբողջ թվերի առաջին վանդակների տիրույթն է և պետք է լինի թվեր, զանգվածներ կամ թվեր պարունակող հղումներ!Ամբողջ թվերի երկրորդ վանդակների տիրույթն է և պետք է լինի թվեր, զանգվածներ կամ թվեր պարունակող հղումներ"}, "CRITBINOM": {"a": "(trials; probability_s; alpha)", "d": "Վերադարձնում է ամենափոքր արժեքը, որի համար կուտակային երկանդամ բաշխումը մեծ է կամ հավասար է չափանիշի արժեքին", "ad": "Բեռնուլիի փորձարկումների թիվն է! Յուրաքանչյուր փորձարկման հաջողության հավանականությունն է՝ 0-ից 1-ի միջև ներառյալ!Չափանիշի արժեքն է, 0-ից 1-ը ներառյալ թիվ"}, "DEVSQ": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է տվյալների կետերի շեղումների քառակուսիների գումարը դրանց ընտրանքի միջինից", "ad": "1-ից 255 արգումենտներ են, կամ զանգված կամ զանգվածի հղում, որի վրա ցանկանում եք հաշվարկել DEVSQ-ը"}, "EXPONDIST": {"a": "(x; lambda; cumulative)", "d": "Վերադարձնում է էքսպոնենցիալ բաշխումը", "ad": "ֆունկցիայի արժեքն է, ոչ բացասական թիվ!պարամետրի արժեքն է, դրական թիվ!ֆունկցիայի վերադարձվող տրամաբանական արժեք է՝ կուտակային բաշխման ֆունկցիա = TRUE; հավանականության խտության ֆունկցիան = FALSE"}, "EXPON.DIST": {"a": "(x; lambda; cumulative)", "d": "Վերադարձնում է էքսպոնենցիալ բաշխումը", "ad": "ֆունկցիայի արժեքն է, ոչ բացասական թիվ!պարամետրի արժեքն է, դրական թիվ! ֆունկցիայի վերադարձվող տրամաբանական արժեք է՝ կուտակային բաշխման ֆունկցիա = TRUE; հավանականության խտության ֆունկցիան = FALSE"}, "FDIST": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "Վերադարձնում է (աջակողմյան) F հավանականության բաշխումը (բազմազանության աստիճանը) երկու տվյալների հավաքածուների համար", "ad": "այն արժեքն է, որով կարելի է գնահատել ֆունկցիան, ոչ բացասական թիվ!ազատության համարիչի աստիճանն է, թիվ 1-ի և 10^10-ի միջև, բացառությամբ 10^10-ի!ազատության աստիճանների հայտարարն է, թիվ 1-ի և 10^10-ի միջև, բացառությամբ 10^10-ի"}, "FINV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Վերադարձնում է (աջակողմյան) F հավանականության բաշխման հակառակը. եթե p = FDIST(x,...), ապա FINV(p,...) = x", "ad": "հավանականություն է, որը կապված է F կուտակային բաշխման հետ՝ 0-ի և 1-ի միջև ներառյալ!ազատության համարիչի աստիճանն է, թիվ 1-ի և 10^10-ի միջև, բացառությամբ 10^10-ի! ազատության աստիճանների հայտարարն է, թիվ 1-ի և 10^10-ի միջև, բացառությամբ 10^10-ի"}, "FTEST": {"a": "(array1; array2)", "d": "Վերադարձնում է F-թեստի արդյունքը՝ երկակի հավանականությունը, որ Զանգված1-ում և Զանգված2-ում շեղումները էականորեն չեն տարբերվում", "ad": "առաջին զանգվածն է կամ տվյալների տիրույթը և կարող է լինել թվեր կամ անուններ, զանգվածներ կամ հղումներ, որոնք պարունակում են թվեր (դատարկներն անտեսվում են)! երկրորդ զանգվածն է կամ տվյալների տիրույթը և կարող է լինել թվեր կամ անուններ, զանգվածներ կամ թվեր պարունակող հղումներ (դատարկներն անտեսվում են)"}, "F.DIST": {"a": "(x; deg_freedom1; deg_freedom2; cumulative)", "d": "Վերադարձնում է (ձախ պոչով) F հավանականության բաշխումը (բազմազանության աստիճանը) երկու տվյալների հավաքածուների համար", "ad": "այն արժեքն է, որով կարելի է գնահատել ֆունկցիան, ոչ բացասական թիվ!ազատության համարիչի աստիճանն է, թիվ 1-ի և 10^10-ի միջև, բացառությամբ 10^10-ի!ազատության աստիճանների հայտարարն է, թիվ 1-ի և 10^10-ի միջև, բացառությամբ 10^10-ի!is ֆունկցիայի վերադարձվող տրամաբանական արժեք՝ կուտակային բաշխման ֆունկցիա = TRUE; ; հավանականության խտության ֆունկցիա = FALSE"}, "F.DIST.RT": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "Վերադարձնում է (աջակողմյան) F հավանականության բաշխումը (բազմազանության աստիճանը) երկու տվյալների հավաքածուների համար", "ad": "այն արժեքն է, որով կարելի է գնահատել ֆունկցիան, ոչ բացասական թիվ!ազատության համարիչի աստիճանն է, թիվ 1-ի և 10^10-ի միջև, բացառությամբ 10^10-ի!ազատության աստիճանների հայտարարն է, թիվ 1-ի և 10^10-ի միջև, բացառությամբ 10^10-ի"}, "F.INV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Վերադարձնում է (ձախ պոչով) F հավանականության բաշխման հակառակը. եթե p = F.DIST(x,...), ապա F.INV(p,...) = x", "ad": "հավանականություն է, որը կապված է F կուտակային բաշխման հետ՝ 0-ի և 1-ի միջև ներառյալ!ազատության համարիչի աստիճանն է, թիվ 1-ի և 10^10-ի միջև, բացառությամբ 10^10-ի!ազատության աստիճանների հայտարարն է, թիվ 1-ի և 10^10-ի միջև, բացառությամբ 10^10-ի"}, "F.INV.RT": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Վերադարձնում է (աջակողմյան) F հավանականության բաշխման հակադարձը՝ եթե p = F.DIST.RT(x,...), ապա F.INV.RT(p,...) = x", "ad": "հավանականություն է, որը կապված է F կուտակային բաշխման հետ՝ 0-ի և 1-ի միջև ներառյալ!ազատության համարիչի աստիճանն է, թիվ 1-ի և 10^10-ի միջև, բացառությամբ 10^10-ի!ազատության աստիճանների հայտարարն է, թիվ 1-ի և 10^10-ի միջև, բացառությամբ 10^10-ի"}, "F.TEST": {"a": "(array1; array2)", "d": "Վերադարձնում է F-թեստի արդյունքը՝ երկակի հավանականությունը, որ Զանգված1-ում և Զանգված2-ում շեղումները էականորեն չեն տարբերվում", "ad": "առաջին զանգվածն է կամ տվյալների տիրույթը և կարող է լինել թվեր կամ անուններ, զանգվածներ կամ հղումներ, որոնք պարունակում են թվեր (դատարկներն անտեսվում են)!երկրորդ զանգվածն է կամ տվյալների տիրույթը և կարող է լինել թվեր կամ անուններ, զանգվածներ կամ թվեր պարունակող հղումներ (դատարկներն անտեսվում են)"}, "FISHER": {"a": "(x)", "d": "Վերադարձնում է Ֆիշերի փոխակերպումը", "ad": "այն արժեքն է, որի համար ցանկանում եք փոխակերպումը, մի թիվ -1-ի և 1-ի միջև, բացառելով -1-ը և 1-ը"}, "FISHERINV": {"a": "(y)", "d": "Վերադարձնում է Ֆիշերի փոխակերպման հակադարձությունը. եթե y = FISHER(x), ապա FISHERINV(y) = x", "ad": "այն արժեքն է, որի համար ցանկանում եք կատարել փոխակերպման հակառակը"}, "FORECAST": {"a": "(x; known_y's; known_x's)", "d": "Հաշվում կամ կանխատեսում է ապագա արժեքը գծային միտման երկայնքով՝ օգտագործելով գոյություն ունեցող արժեքները", "ad": "այն տվյալների կետն է, որի համար ցանկանում եք կանխագուշակել արժեք և պետք է լինի թվային արժեք!կախված զանգվածն է կամ թվային տվյալների տիրույթը:! թվային տվյալների անկախ զանգված կամ տիրույթ է։ Known_x-ի շեղումը չպետք է զրո լինի"}, "FORECAST.ETS": {"a": "(target_date; values; timeline; [seasonality]; [data_completion]; [aggregation])", "d": "Վերադարձնում է կանխատեսված արժեքը որոշակի ապագա նպատակային ամսաթվի համար՝ օգտագործելով էքսպոնենցիալ հարթեցման մեթոդը:", "ad": "այն տվյալների կետն է, որի համար Աղյուսակների խմբագիրը արժեք է կանխատեսում: Այն պետք է պահպանի ժամանակացույցի արժեքների օրինակը։! ձեր կանխատեսած թվային տվյալների զանգվածն է կամ տիրույթը:! թվային տվյալների անկախ զանգված կամ տիրույթ է։ Ժամանակագրության ամսաթվերը պետք է ունենան հետևողական քայլ դրանց միջև և չեն կարող լինել զրո:! կամընտիր թվային արժեք է, որը ցույց է տալիս սեզոնային օրինաչափության երկարությունը: 1-ի լռելյայն արժեքը ցույց է տալիս, որ սեզոնայնությունը ինքնաբերաբար հայտնաբերվում է:! կամընտիր արժեք է բացակայող արժեքների հետ աշխատելու համար: 1-ի լռելյայն արժեքը փոխարինում է բացակայող արժեքներին ինտերպոլացիայով, իսկ 0-ը փոխարինում է զրոներով:! կամընտիր թվային արժեք է միևնույն ժամանակի դրոշմակնիքով մի քանի արժեքներ հավաքելու համար: Եթե դատարկ է, Աղյուսակների խմբագիրը միջինացնում է արժեքները:"}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "Վերադարձնում է կանխատեսված արժեքի վստահության միջակայքը նշված նպատակային ամսաթվի դրությամբ.", "ad": "այն տվյալների կետն է, որի համար Աղյուսակների խմբագիրը արժեք է կանխատեսում: Այն պետք է պահպանի ժամանակացույցի արժեքների օրինակը:! ձեր կանխատեսած թվային տվյալների զանգվածն է կամ տիրույթը:!թվային տվյալների անկախ զանգված կամ տիրույթ է։ Ժամանակագրության ամսաթվերը պետք է ունենան հետևողական քայլ դրանց միջև և չեն կարող լինել զրո:! 0-ի և 1-ի միջև ընկած թիվ է, որը ցույց է տալիս վստահության մակարդակը հաշվարկված վստահության միջակայքի համար: Նախնական արժեքը .95 է:!կամընտիր թվային արժեք է, որը ցույց է տալիս սեզոնային օրինաչափության երկարությունը: 1-ի լռելյայն արժեքը ցույց է տալիս, որ սեզոնայնությունը ինքնաբերաբար հայտնաբերվում է:! կամընտիր արժեք է բացակայող արժեքների հետ աշխատելու համար: 1-ի լռելյայն արժեքը փոխարինում է բացակայող արժեքներին ինտերպոլացիայով, իսկ 0-ը փոխարինում է զրոներով:! կամընտիր թվային արժեք է միևնույն ժամանակի դրոշմակնիքով մի քանի արժեքներ հավաքելու համար: Եթե դատարկ է, Աղյուսակի խմբագիրը միջինացնում է արժեքները:"}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "Վերադարձնում է կրկնվող օրինաչափության երկարությունը, որը հայտնաբերում է հավելվածը նշված ժամանակային շարքի համար.", "ad": "ձեր կանխատեսած թվային տվյալների զանգվածն է կամ տիրույթը:! թվային տվյալների անկախ զանգված կամ տիրույթ է։ Ժամանակագրության ամսաթվերը պետք է ունենան հետևողական քայլ դրանց միջև և չեն կարող լինել զրո:!կամընտիր արժեք է բացակայող արժեքների հետ աշխատելու համար: 1-ի լռելյայն արժեքը փոխարինում է բացակայող արժեքներին ինտերպոլացիայով, իսկ 0-ը փոխարինում է զրոներով:! կամընտիր թվային արժեք է միևնույն ժամանակի դրոշմակնիքով մի քանի արժեքներ հավաքելու համար: Եթե դատարկ է, Աղյուսակի խմբագիրը միջինացնում է արժեքները:"}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "Վերադարձնում է կանխատեսման համար պահանջված վիճակագրությունը.", "ad": "ձեր կանխատեսած թվային տվյալների զանգվածն է կամ տիրույթը:! թվային տվյալների անկախ զանգված կամ տիրույթ է։ Ժամանակագրության ամսաթվերը պետք է ունենան հետևողական քայլ դրանց միջև և չեն կարող լինել զրո:!1-ից 8-ի միջև ընկած թիվ է, որը ցույց է տալիս, թե որ վիճակագրական աղյուսակի խմբագրիչը կվերադարձնի հաշվարկված կանխատեսմանը:!կամընտիր թվային արժեք է, որը ցույց է տալիս սեզոնային օրինաչափության երկարությունը: 1-ի լռելյայն արժեքը ցույց է տալիս, որ սեզոնայնությունը ինքնաբերաբար հայտնաբերվում է:! կամընտիր արժեք է բացակայող արժեքների հետ աշխատելու համար: 1-ի լռելյայն արժեքը փոխարինում է բացակայող արժեքներին ինտերպոլացիայով, իսկ 0-ը փոխարինում է զրոներով:! կամընտիր թվային արժեք է միևնույն ժամանակի դրոշմակնիքով մի քանի արժեքներ հավաքելու համար: Եթե դատարկ է, Աղյուսակների խմբագիրը միջինացնում է արժեքները:"}, "FORECAST.LINEAR": {"a": "(x; known_y's; known_x's)", "d": "Հաշվում կամ կանխատեսում է ապագա արժեքը գծային միտման երկայնքով՝ օգտագործելով գոյություն ունեցող արժեքները", "ad": "այն տվյալների կետն է, որի համար ցանկանում եք կանխագուշակել արժեքը և պետք է լինի թվային արժեք!թվային տվյալների կախյալ զանգված կամ տիրույթ է! թվային տվյալների անկախ զանգված կամ տիրույթ է։ Known_x-ի շեղումը չպետք է զրո լինի"}, "FREQUENCY": {"a": "(data_array; bins_array)", "d": "Հաշվում է, թե որքան հաճախ են արժեքները հայտնվում արժեքների միջակայքում և այնուհետև վերադարձնում է թվերի ուղղահայաց զանգված, որոնք ունեն մեկ տարր ավելի, քան աղբամանների_զանգված-ը", "ad": "մի զանգված կամ հղում է մի շարք արժեքների, որոնց համար ցանկանում եք հաշվել հաճախականությունները (դատարկներն ու տեքստը անտեսվում են)! զանգված կամ հղում է ընդմիջումներին, որոնց մեջ ցանկանում եք խմբավորել արժեքները data_array-ում"}, "GAMMA": {"a": "(x)", "d": "Վերադարձնում է Գամմա ֆունկցիայի արժեքը", "ad": "այն արժեքն է, որի համար ցանկանում եք հաշվարկել գամմա"}, "GAMMADIST": {"a": "(x; alpha; beta; cumulative)", "d": "Վերադարձնում է գամմա բաշխումը", "ad": "այն արժեքն է, որով ցանկանում եք գնահատել բաշխումը, ոչ բացասական թիվ!բաշխման պարամետր է, դրական թիվ! բաշխման պարամետր է, դրական թիվ: Եթե բետա = 1, GAMMADIST-ը վերադարձնում է ստանդարտ գամմա բաշխումը! տրամաբանական արժեք է՝ վերադարձնել կուտակային բաշխման ֆունկցիան = TRUE; վերադարձնել հավանականության զանգվածի ֆունկցիան = FALSE կամ բաց թողնված"}, "GAMMA.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "Վերադարձնում է գամմա բաշխումը", "ad": "այն արժեքն է, որով ցանկանում եք գնահատել բաշխումը, ոչ բացասական թիվ!բաշխման պարամետր է, դրական թիվ! բաշխման պարամետր է, դրական թիվ: Եթե բետա = 1, GAMMA.DIST-ը վերադարձնում է ստանդարտ գամմա բաշխումը! տրամաբանական արժեք է՝ վերադարձնել կուտակային բաշխման ֆունկցիան = TRUE; վերադարձնել հավանականության զանգվածի ֆունկցիան = FALSE կամ բաց թողնված"}, "GAMMAINV": {"a": "(probability; alpha; beta)", "d": "Վերադարձնում է գամմա կուտակային բաշխման հակադարձը՝ եթե p = GAMMADIST(x,...), ապա GAMMAINV(p,...) = x", "ad": "գամմա բաշխման հետ կապված հավանականությունն է՝ 0-ից 1-ի միջև ներառյալ!բաշխման պարամետր է, դրական թիվ! բաշխման պարամետր է, դրական թիվ: Եթե բետա = 1, GAMMAINV-ը վերադարձնում է ստանդարտ գամմա բաշխման հակադարձը"}, "GAMMA.INV": {"a": "(probability; alpha; beta)", "d": "Վերադարձնում է գամմա կուտակային բաշխման հակադարձը՝ եթե p = GAMMA.DIST(x,...), ապա GAMMA.INV(p,...) = x", "ad": "գամմա բաշխման հետ հավանականությունն է՝ 0-ից 1-ի միջև ներառյալ!բաշխման պարամետր է, դրական թիվ! բաշխման պարամետր է, դրական թիվ: Եթե բետա = 1, GAMMA.INV-ը վերադարձնում է ստանդարտ գամմա բաշխման հակառակը"}, "GAMMALN": {"a": "(x)", "d": "Վերադարձնում է գամմա ֆունկցիայի բնական լոգարիթմը", "ad": "այն արժեքն է, որի համար ցանկանում եք հաշվարկել GAMMALN-ը, դրական թիվ"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Վերադարձնում է գամմա ֆունկցիայի բնական լոգարիթմը", "ad": "այն արժեքն է, որի համար ցանկանում եք հաշվարկել GAMMALN.PRECISE, դրական թիվ"}, "GAUSS": {"a": "(x)", "d": "Վերադարձնում է 0,5-ով պակաս, քան ստանդարտ նորմալ կուտակային բաշխումը", "ad": "այն արժեքն է, որի համար ցանկանում եք բաշխումը"}, "GEOMEAN": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է զանգվածի կամ դրական թվային տվյալների երկրաչափական միջինը", "ad": "1-ից 255 թվեր կամ անուններ, զանգվածներ կամ հղումներ են, որոնք պարունակում են թվեր, որոնց համար ցանկանում եք միջինը"}, "GROWTH": {"a": "(known_y's; [known_x's]; [new_x's]; [const])", "d": "Վերադարձնում է թվերը էքսպոնենցիալ աճի միտումով, որը համապատասխանում է հայտնի տվյալների կետերին", "ad": "y արժեքների բազմությունն է, որը դուք արդեն գիտեք y = b*m^x հարաբերություններում, զանգված կամ դրական թվերի տիրույթ!x-արժեքների կամընտիր հավաքածու է, որը դուք կարող եք արդեն իմանալ y = b*m^x հարաբերություններում, զանգված կամ տիրույթ նույն չափի, ինչ Known_y-ը!նոր x արժեքներ են, որոնց համար ցանկանում եք, որ GROWTH-ը վերադարձնի համապատասխան y արժեքներ! տրամաբանական արժեք է. b հաստատունը սովորաբար հաշվարկվում է, եթե Const = TRUE; b-ը հավասար է 1-ի, եթե Const = FALSE կամ բաց թողնված է"}, "HARMEAN": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է դրական թվերի տվյալների բազմության ներդաշնակ միջինը՝ փոխադարձ թվային միջինի փոխադարձը", "ad": "1-ից 255 թվեր կամ անուններ, զանգվածներ կամ հղումներ են, որոնք պարունակում են թվեր, որոնց համար ցանկանում եք ներդաշնակ միջինը"}, "HYPGEOM.DIST": {"a": "(sample_s; number_sample; population_s; number_pop; cumulative)", "d": "Վերադարձնում է հիպերերկրաչափական բաշխումը", "ad": "նմուշի հաջողությունների քանակն է: նմուշի չափն է!բնակչության հաջողությունների թիվն է!բնակչության թվաքանակն է! տրամաբանական արժեք է՝ կուտակային բաշխման ֆունկցիայի համար օգտագործեք TRUE; հավանականության խտության ֆունկցիայի համար օգտագործեք FALSEE"}, "HYPGEOMDIST": {"a": "(sample_s; number_sample; population_s; number_pop)", "d": "Վերադարձնում է հիպերերկրաչափական բաշխումը", "ad": "նմուշի հաջողությունների թիվն է! նմուշի չափն է! բնակչության հաջողությունների թիվն է!բնակչության թվաքանակն է"}, "INTERCEPT": {"a": "(known_ys; known_xs)", "d": "Հաշվում է այն կետը, երբ ուղիղը հատում է y առանցքը՝ օգտագործելով հայտնի x-արժեքների և y-արժեքների միջով գծագրված լավագույն ռեգրեսիոն գիծը:", "ad": "դիտարկումների կամ տվյալների կախվածություն է և կարող է լինել թվեր կամ անուններ, զանգվածներ կամ հղումներ, որոնք պարունակում են թվեր! դիտարկումների կամ տվյալների անկախ հավաքածու է և կարող է լինել թվեր կամ անուններ, զանգվածներ կամ հղումներ, որոնք պարունակում են թվեր"}, "KURT": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է տվյալների հավաքածուի կարճությունը", "ad": "1-ից մինչև 255 թվեր և անուններ, զանգվածներ կամ հղումներ, որոնք պարունակում են թվեր, որոնց համար ցանկանում եք որոշել ավելցուկը"}, "LARGE": {"a": "(array; k)", "d": "Վերադարձնում է տվյալների հավաքածուի k-րդ ամենամեծ արժեքը: Օրինակ՝ հինգերորդ ամենամեծ numberը", "ad": "այն տվյալների զանգվածն է կամ տիրույթը, որի համար ցանկանում եք որոշել k-րդ ամենամեծ արժեքը! վերադարձվող արժեքի զանգվածում կամ վանդակների տիրույթում (ամենամեծից) դիրքն է"}, "LINEST": {"a": "(known_y's; [known_x's]; [const]; [stats])", "d": "Վերադարձնում է վիճակագրություն, որը նկարագրում է գծային միտում, որը համապատասխանում է հայտնի տվյալների կետերին, ուղիղ գիծ տեղադրելով նվազագույն քառակուսիների մեթոդով", "ad": "y արժեքների բազմությունն է, որը դուք արդեն գիտեք y = mx + b հարաբերություններում!x-արժեքների ընտրովի հավաքածու է, որը դուք կարող եք արդեն իմանալ y = mx + b հարաբերություններում!տրամաբանական արժեք է՝ b հաստատունը սովորաբար հաշվարկվում է, եթե Const = TRUE կամ բաց թողնված; b-ը հավասար է 0-ի, եթե Const = FALSE! տրամաբանական արժեք է՝ վերադարձնել լրացուցիչ ռեգրեսիոն վիճակագրություն = TRUE; վերադարձնել m-գործակիցները և հաստատունը b = FALSE կամ բաց թողնված"}, "LOGEST": {"a": "(known_y's; [known_x's]; [const]; [stats])", "d": "Վերադարձնում է վիճակագրություն, որը նկարագրում է հայտնի տվյալների կետերին համապատասխանող էքսպոնենցիալ կորը", "ad": "y արժեքների բազմությունն է, որը դուք արդեն գիտեք y = b*m^x հարաբերություններում!x-արժեքների ընտրովի հավաքածու է, որը դուք կարող եք արդեն իմանալ y = b*m^x հարաբերություններում!տրամաբանական արժեք է՝ b հաստատունը սովորաբար հաշվարկվում է, եթե Const = TRUE կամ բաց թողնված; b-ը հավասար է 1-ի, եթե Const = FALSE! տրամաբանական արժեք է՝ վերադարձնել լրացուցիչ ռեգրեսիոն վիճակագրություն = TRUE; վերադարձնել m-գործակիցները և հաստատունը b = FALSE կամ բաց թողնված"}, "LOGINV": {"a": "(probability; mean; standard_dev)", "d": "Վերադարձնում է x-ի լոգնորմալ կուտակային բաշխման ֆունկցիայի հակադարձը, որտեղ ln(x)-ը սովորաբար բաշխվում է Միջին և Ստանդարտ_dev պարամետրերով:", "ad": "հավանականություն է, որը կապված է լոգոնորմալ բաշխման հետ՝ 0-ի և 1-ի միջև ներառյալ թիվ! ln(x)-ի միջինն է! ln(x-ի ստանդարտ շեղումն է), դրական թիվ"}, "LOGNORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Վերադարձնում է x-ի լոգնորմալ բաշխումը, որտեղ ln(x)-ը սովորաբար բաշխվում է Միջին և Ստանդարտ_dev պարամետրերով", "ad": "այն արժեքն է, որով կարելի է գնահատել ֆունկցիան, դրական թիվ!ln(x)-ի միջինն է! ln(x-ի ստանդարտ շեղումն է), դրական թիվ! տրամաբանական արժեք է. կուտակային բաշխման ֆունկցիայի համար օգտագործեք TRUE; հավանականության խտության ֆունկցիայի համար օգտագործեք FALSE"}, "LOGNORM.INV": {"a": "(probability; mean; standard_dev)", "d": "Վերադարձնում է x-ի լոգնորմալ կուտակային բաշխման ֆունկցիայի հակադարձը, որտեղ ln(x)-ը սովորաբար բաշխվում է Միջին և Ստանդարտ_dev պարամետրերով:", "ad": "հավանականություն է, որը կապված է լոգոնորմալ բաշխման հետ՝ 0-ի և 1-ի միջև ներառյալ թիվ! ln(x)-ի միջինն է! ln(x-ի ստանդարտ շեղումն է), դրական թիվ"}, "LOGNORMDIST": {"a": "(x; mean; standard_dev)", "d": "Վերադարձնում է x-ի կուտակային լոգնորմալ բաշխումը, որտեղ ln(x) սովորաբար բաշխվում է Միջին և Ստանդարտ_dev պարամետրերով", "ad": "այն արժեքն է, որով կարելի է գնահատել ֆունկցիան, դրական թիվ! ln(x)-ի միջինն է! ln(x-ի ստանդարտ շեղումն է), դրական թիվ"}, "MAX": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է արժեքների հավաքածուի ամենամեծ արժեքը: Անտեսում է տրամաբանական արժեքները և տեքստը", "ad": "1-ից 255 թվեր են, դատարկ վանդակներ, տրամաբանական արժեքներ կամ տեքստային համարներ, որոնց համար առավելագույնն եք ուզում"}, "MAXA": {"a": "(value1; [value2]; ...)", "d": "Վերադարձնում է արժեքների հավաքածուի ամենամեծ արժեքը: Չի անտեսում տրամաբանական արժեքները և տեքստը", "ad": "1-ից 255 թվեր են, դատարկ վանդակների, տրամաբանական արժեքներ կամ տեքստային համարներ, որոնց համար առավելագույնն եք ցանկանում"}, "MAXIFS": {"a": "(max_range; criteria_range; criteria; ...)", "d": "Վերադարձնում է առավելագույն արժեքը բջիջների միջև, որոնք նշված են որոշակի պայմանների կամ չափանիշների կողմից", "ad": "վանդակները, որոնցում պետք է որոշվի առավելագույն արժեքը!վանդակների շրջանակն է, որը ցանկանում եք գնահատել տվյալ պայմանի համար!պայման կամ չափանիշ է թվի, արտահայտության կամ տեքստի տեսքով, որը սահմանում է, թե որ վանդակներն են ներառվելու առավելագույն արժեքը որոշելիս"}, "MEDIAN": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է միջինը կամ տրված թվերի բազմության մեջտեղում գտնվող թիվը", "ad": "1-ից 255 թվեր կամ անուններ, զանգվածներ կամ հղումներ են, որոնք պարունակում են թվեր, որոնց համար ցանկանում եք միջինը"}, "MIN": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է արժեքների հավաքածուի ամենափոքր թիվը: Անտեսում է տրամաբանական արժեքները և տեքստը", "ad": "1-ից 255 թվեր են, դատարկ վանդակներ, տրամաբանական արժեքներ կամ տեքստային համարներ, որոնց համար ցանկանում եք նվազագույնը"}, "MINA": {"a": "(value1; [value2]; ...)", "d": "Վերադարձնում է արժեքների մի շարքի ամենափոքր արժեքը: Չի անտեսում տրամաբանական արժեքները և տեքստը", "ad": "1-ից 255 թվեր են, դատարկ վանդակներ, տրամաբանական արժեքներ կամ տեքստային համարներ, որոնց համար ցանկանում եք նվազագույնը"}, "MINIFS": {"a": "(min_range; criteria_range; criteria; ...)", "d": "Վերադարձնում է նվազագույն արժեքը բջիջների միջև, որոնք նշված են որոշակի պայմանների կամ չափանիշների կողմից", "ad": "վանդակները, որոնցում պետք է որոշվի նվազագույն արժեքը!վանդակների շրջանակն է, որը ցանկանում եք գնահատել տվյալ պայմանի համար! պայման կամ չափանիշ է թվի, արտահայտության կամ տեքստի տեսքով, որը սահմանում է, թե որ վանդակներն են ներառվելու նվազագույն արժեքը որոշելիս"}, "MODE": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է զանգվածի կամ տվյալների տիրույթում ամենահաճախ հանդիպող կամ կրկնվող արժեքը", "ad": "1-ից 255 թվեր են, կամ անուններ, զանգվածներ կամ հղումներ, որոնք պարունակում են թվեր, որոնց համար ցանկանում եք ռեժիմը"}, "MODE.MULT": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է զանգվածի կամ տվյալների տիրույթում ամենահաճախ հանդիպող կամ կրկնվող արժեքների ուղղահայաց զանգվածը: Հորիզոնական զանգվածի համար օգտագործեք =TRANSPOSE(MODE.MULT(համար1,համար2,...))", "ad": "1-ից 255 թվեր են, կամ անուններ, զանգվածներ կամ հղումներ, որոնք պարունակում են թվեր, որոնց համար ցանկանում եք ռեժիմը"}, "MODE.SNGL": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է զանգվածի կամ տվյալների տիրույթում ամենահաճախ հանդիպող կամ կրկնվող արժեքը", "ad": "1-ից 255 թվեր են, կամ անուններ, զանգվածներ կամ հղումներ, որոնք պարունակում են թվեր, որոնց համար ցանկանում եք ռեժիմը"}, "NEGBINOM.DIST": {"a": "(number_f; number_s; probability_s; cumulative)", "d": "Վերադարձնում է բացասական երկանդամ բաշխումը, հավանականությունը, որ կլինեն Թիվ_f ձախողումներ մինչև Թիվ_s-րդ հաջողությունը, Հավանականություն_s հաջողության հավանականությամբ", "ad": "ձախողումների թիվն է! հաջողության շեմն է! հաջողության հավանականությունն է; 0-ի և 1-ի միջև եղած թիվը տրամաբանական արժեք է!կուտակային բաշխման ֆունկցիայի համար օգտագործեք TRUE; հավանականության զանգվածի ֆունկցիայի համար օգտագործեք FALSE"}, "NEGBINOMDIST": {"a": "(number_f; number_s; probability_s)", "d": "Վերադարձնում է բացասական երկանդամ բաշխումը, հավանականությունը, որ կլինեն Թիվ_f ձախողումներ մինչև Թիվ_s-րդ հաջողությունը, Հավանականություն_s հաջողության հավանականությամբ", "ad": "ձախողումների թիվն է! հաջողության շեմն է! հաջողության հավանականությունն է; 0-ի և 1-ի միջև եղած թիվը"}, "NORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Վերադարձնում է նորմալ բաշխումը նշված միջին և ստանդարտ շեղման համար", "ad": "այն արժեքն է, որի համար ցանկանում եք բաշխումը! բաշխման միջին թվաբանականն է!բաշխման ստանդարտ շեղումն է, դրական թիվ! տրամաբանական արժեք է. կուտակային բաշխման ֆունկցիայի համար օգտագործեք TRUE; հավանականության խտության ֆունկցիայի համար օգտագործեք FALSE"}, "NORMDIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Վերադարձնում է նորմալ կուտակային բաշխումը նշված միջին և ստանդարտ շեղման համար", "ad": "այն արժեքն է, որի համար ցանկանում եք բաշխումը!բաշխման միջին թվաբանականն է!բաշխման ստանդարտ շեղումն է, դրական թիվ!տրամաբանական արժեք է՝կուտակային բաշխման ֆունկցիայի համար օգտագործեք TRUE; հավանականության խտության ֆունկցիայի համար օգտագործեք FALSE"}, "NORM.INV": {"a": "(probability; mean; standard_dev)", "d": "Վերադարձնում է նորմալ կուտակային բաշխման հակադարձը նշված միջին և ստանդարտ շեղման համար", "ad": "նորմալ բաշխմանը համապատասխանող հավանականություն է՝ 0-ից 1-ի միջև ներառյալ! բաշխման միջին թվաբանականն է! բաշխման ստանդարտ շեղումն է, դրական թիվ"}, "NORMINV": {"a": "(probability; mean; standard_dev)", "d": "Վերադարձնում է նորմալ կուտակային բաշխման հակադարձը նշված միջին և ստանդարտ շեղման համար", "ad": "նորմալ բաշխմանը համապատասխանող հավանականություն է՝ 0-ից 1-ի միջև ներառյալ! բաշխման միջին թվաբանականն է! բաշխման ստանդարտ շեղումն է, դրական թիվ"}, "NORM.S.DIST": {"a": "(z; cumulative)", "d": "Վերադարձնում է ստանդարտ նորմալ բաշխումը (ունի զրոյի միջինը և մեկ ստանդարտ շեղումը)", "ad": "այն արժեքն է, որի համար ցանկանում եք բաշխումը! ֆունկցիայի վերադարձվող տրամաբանական արժեք է՝ կուտակային բաշխման ֆունկցիա = TRUE; հավանականության խտության ֆունկցիան = FALSE"}, "NORMSDIST": {"a": "(z)", "d": "Վերադարձնում է ստանդարտ նորմալ կուտակային բաշխումը (ունի զրոյի միջինը և մեկ ստանդարտ շեղումը)", "ad": "այն արժեքն է, որի համար ցանկանում եք բաշխումը"}, "NORM.S.INV": {"a": "(probability)", "d": "Վերադարձնում է ստանդարտ նորմալ կուտակային բաշխման հակադարձը (ունի զրո միջին և մեկ ստանդարտ շեղում)", "ad": "նորմալ բաշխմանը համապատասխանող հավանականություն է՝ 0-ից 1-ի միջև ներառյալ"}, "NORMSINV": {"a": "(probability)", "d": "Վերադարձնում է ստանդարտ նորմալ կուտակային բաշխման հակադարձը (ունի զրո միջին և մեկ ստանդարտ շեղում)", "ad": "նորմալ բաշխմանը համապատասխանող հավանականություն է՝ 0-ից 1-ի միջև ներառյալ"}, "PEARSON": {"a": "(array1; array2)", "d": "Վերադարձնում է Պիրսոնի արտադրյալ պահի հարաբերակցության գործակիցը, r", "ad": "անկախ արժեքների բազմություն է! Կախյալ արժեքների բազմություն է"}, "PERCENTILE": {"a": "(array; k)", "d": "Վերադարձնում է միջակայքի արժեքների k-րդ տոկոսը", "ad": "տվյալների զանգված կամ տիրույթ է, որը սահմանում է հարաբերական դիրքը! տոկոսային արժեքն է, որը գտնվում է 0-ից 1-ի միջև՝ ներառյալ"}, "PERCENTILE.EXC": {"a": "(array; k)", "d": "Վերադարձնում է արժեքների k-րդ տոկոսը տիրույթում, որտեղ k-ը 0..1 միջակայքում է, բացառապես", "ad": "տվյալների զանգված կամ տիրույթ է, որը սահմանում է հարաբերական դիրքը! տոկոսային արժեքն է, որը գտնվում է 0-ից 1-ի միջև՝ ներառյալ"}, "PERCENTILE.INC": {"a": "(array; k)", "d": "Վերադարձնում է արժեքների k-րդ տոկոսը տիրույթում, որտեղ k-ը գտնվում է 0..1 միջակայքում՝ ներառյալ", "ad": "տվյալների զանգված կամ տիրույթ է, որը սահմանում է հարաբերական դիրքը! տոկոսային արժեքն է, որը գտնվում է 0-ից 1-ի միջև՝ ներառյալ"}, "PERCENTRANK": {"a": "(array; x; [significance])", "d": "Վերադարձնում է տվյալների հավաքածուի արժեքի վարկանիշը՝ որպես տվյալների հավաքածուի տոկոս", "ad": "թվային արժեքներով տվյալների զանգված կամ տիրույթ է, որը սահմանում է հարաբերական դիրքը!այն արժեքն է, որի համար ցանկանում եք իմանալ վարկանիշը! կամընտիր արժեք է, որը նույնականացնում է վերադարձված տոկոսի նշանակալի թվանշանների քանակը, երեք նիշ, եթե բաց թողնվի(0.xxx%)"}, "PERCENTRANK.EXC": {"a": "(array; x; [significance])", "d": "Վերադարձնում է տվյալների հավաքածուի արժեքի դասակարգումը որպես տվյալների հավաքածուի տոկոս՝ որպես տվյալների հավաքածուի տոկոս (0..1, բացառիկ):", "ad": "թվային արժեքներով տվյալների զանգված կամ տիրույթ է, որը սահմանում է հարաբերական դիրքը!այն արժեքն է, որի համար ցանկանում եք իմանալ վարկանիշը! կամընտիր արժեք է, որը նույնականացնում է վերադարձված տոկոսի նշանակալի թվանշանների քանակը, երեք նիշ, եթե բաց թողնվի (0.xxx%)"}, "PERCENTRANK.INC": {"a": "(array; x; [significance])", "d": "Վերադարձնում է տվյալների հավաքածուի արժեքի դասակարգումը որպես տվյալների հավաքածուի տոկոս՝ որպես տվյալների հավաքածուի տոկոս (0..1, ներառյալ):", "ad": "թվային արժեքներով տվյալների զանգված կամ տիրույթ է, որը սահմանում է հարաբերական դիրքը!այն արժեքն է, որի համար ցանկանում եք իմանալ վարկանիշը! կամընտիր արժեք է, որը նույնացնում է վերադարձված տոկոսի նշանակալի թվանշանների քանակը, երեք նիշ, եթե բաց թողնվի (0.xxx%)"}, "PERMUT": {"a": "(number; number_chosen)", "d": "Վերադարձնում է փոխակերպումների քանակը տվյալ թվով օբյեկտների համար, որոնք կարող են ընտրվել ընդհանուր օբյեկտներից", "ad": "օբյեկտների ընդհանուր թիվն է! յուրաքանչյուր փոխակերպման մեջ գտնվող օբյեկտների քանակն է"}, "PERMUTATIONA": {"a": "(number; number_chosen)", "d": "Վերադարձնում է փոխակերպումների քանակը տվյալ քանակի օբյեկտների համար (կրկնություններով), որոնք կարող են ընտրվել ընդհանուր օբյեկտներից", "ad": "օբյեկտների ընդհանուր թիվն է! Յուրաքանչյուր փոխակերպման մեջ գտնվող օբյեկտների թիվն է"}, "PHI": {"a": "(x)", "d": "Վերադարձնում է խտության ֆունկցիայի արժեքը ստանդարտ նորմալ բաշխման համար", "ad": "այն թիվն է, որի համար ցանկանում եք ստանդարտ նորմալ բաշխման խտությունը"}, "POISSON": {"a": "(x; mean; cumulative)", "d": "Վերադարձնում է Պուասոնի բաշխումը", "ad": "իրադարձությունների քանակն է! ակնկալվող թվային արժեքն է, դրական թիվ! տրամաբանական արժեք է. Poisson-ի կուտակային հավանականության համար օգտագործեք TRUE; Պուասոնի հավանականության զանգվածի ֆունկցիայի համար օգտագործեք FALSE"}, "POISSON.DIST": {"a": "(x; mean; cumulative)", "d": "Վերադարձնում է Պուասոնի բաշխումը", "ad": "իրադարձությունների քանակն է! ակնկալվող թվային արժեքն է, դրական թիվ! տրամաբանական արժեք է՝Poisson-ի կուտակային հավանականության համար օգտագործեք TRUE; Պուասոնի հավանականության զանգվածի ֆունկցիայի համար օգտագործեք FALSE"}, "PROB": {"a": "(x_range; prob_range; lower_limit; [upper_limit])", "d": "Վերադարձնում է հավանականությունը, այն տիրույթում արժեքները, որոնք գտնվում են երկու սահմանների միջև կամ հավասար են ստորին սահմանին", "ad": "x-ի թվային արժեքների միջակայքն է, որի հետ կապված են հավանականությունները! X_range-ի արժեքների հետ կապված հավանականությունների բազմությունն է՝ 0-ից 1-ի միջև ընկած արժեքները և բացառելով 0-ը! այն արժեքի ստորին սահմանն է, որի համար ցանկանում եք հավանականություն! արժեքի կամընտիր վերին սահմանն է: Եթե բաց թողնվի, PROB-ը վերադարձնում է հավանականությունը, որ X_range արժեքները հավասար են Lower_limit-ին"}, "QUARTILE": {"a": "(array; quart)", "d": "Վերադարձնում է տվյալների հավաքածուի քառորդը", "ad": "թվային արժեքների զանգված կամ վանդակների միջակայք է, որի համար ցանկանում եք քառորդ արժեքը! մի թիվ է՝ նվազագույն արժեքը = 0; 1-ին քառորդ = 1; միջին արժեքը = 2; 3-րդ քառորդ = 3; առավելագույն արժեքը = 4"}, "QUARTILE.INC": {"a": "(array; quart)", "d": "Վերադարձնում է տվյալների հավաքածուի քառորդը՝ հիմնված 0..1-ից ներառյալ տոկոսային արժեքների վրա", "ad": "թվային արժեքների զանգված կամ վանդակների միջակայք է, որի համար ցանկանում եք քառորդ արժեքը! մի թիվ է՝ նվազագույն արժեքը = 0; 1-ին քառորդ = 1; միջին արժեքը = 2; 3-րդ քառորդ = 3; առավելագույն արժեքը = 4"}, "QUARTILE.EXC": {"a": "(array; quart)", "d": "Վերադարձնում է տվյալների հավաքածուի քառորդը՝ հիմնված 0..1-ի տոկոսային արժեքների վրա՝ բացառապես", "ad": "արժեքների զանգված կամ վանդակների միջակայք է, որի համար ցանկանում եք քառորդ արժեքը!մի թիվ է՝ նվազագույն արժեքը = 0; 1-ին քառորդ = 1; միջին արժեքը = 2; 3-րդ քառորդ = 3; առավելագույն արժեքը = 4"}, "RANK": {"a": "(number; ref; [order])", "d": "Վերադարձնում է թվի դասակարգումը թվերի ցանկում՝ նրա չափը ցուցակի այլ արժեքների համեմատ", "ad": "այն թիվն է, որի համար ցանկանում եք գտնել վարկանիշը! թվերի ցանկի զանգված է կամ հղում։ Ոչ թվային արժեքներն անտեսվում են! մի թիվ է. վարկանիշը ցուցակում դասավորված է նվազման = 0 կամ բաց թողնված; դասակարգումը ցուցակում դասավորված աճման = ցանկացած ոչ զրոյական արժեք"}, "RANK.AVG": {"a": "(number; ref; [order])", "d": "Վերադարձնում է թվերի դասակարգումը թվերի ցանկում՝ նրա չափը ցուցակի այլ արժեքների համեմատ. եթե մեկից ավելի արժեքներ ունեն նույն աստիճանը, միջին վարկանիշը վերադարձվում է", "ad": "այն թիվն է, որի համար ցանկանում եք գտնել վարկանիշը! թվերի ցանկի զանգված է կամ հղում։ Ոչ թվային արժեքներն անտեսվում են! մի թիվ է. վարկանիշը ցուցակում դասավորված է նվազման = 0 կամ բաց թողնված; դասակարգումը ցուցակում դասավորված աճման = ցանկացած ոչ զրոյական արժեք"}, "RANK.EQ": {"a": "(number; ref; [order])", "d": "Վերադարձնում է թվերի դասակարգումը թվերի ցանկում՝ նրա չափը ցուցակի այլ արժեքների համեմատ. եթե մեկից ավելի արժեքներ ունեն նույն վարկանիշը, այդ արժեքների հավաքածուի վերին աստիճանը վերադարձվում է", "ad": "այն թիվն է, որի համար ցանկանում եք գտնել վարկանիշը!թվերի ցանկի զանգված է կամ հղում։ Ոչ թվային արժեքներն անտեսվում են! մի թիվ է. վարկանիշը ցուցակում դասավորված է նվազման = 0 կամ բաց թողնված; դասակարգումը ցուցակում դասավորված աճման = ցանկացած ոչ զրոյական արժեք"}, "RSQ": {"a": "(known_y's; known_x's)", "d": "Վերադարձնում է Պիրսոնի արտադրյալի մոմենտների հարաբերակցության գործակցի քառակուսին տվյալ տվյալների կետերի միջոցով", "ad": "տվյալների միավորների զանգված կամ տիրույթ է և կարող է լինել թվեր կամ անուններ, զանգվածներ կամ թվեր պարունակող հղումներ! տվյալների միավորների զանգված կամ տիրույթ է և կարող է լինել թվեր կամ անուններ, զանգվածներ կամ թվեր պարունակող հղումներ"}, "SKEW": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է բաշխման թեքությունը. բաշխման անհամաչափության աստիճանի բնութագրում միջինի շուրջ", "ad": "1-ից 255 թվեր կամ անուններ, զանգվածներ կամ հղումներ են, որոնք պարունակում են թվեր, որոնց համար ցանկանում եք թեքություն"}, "SKEW.P": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է պոպուլյացիայի վրա հիմնված բաշխման թեքությունը. բաշխման անհամաչափության աստիճանի բնութագրում միջինի շուրջ", "ad": "1-ից 254 թվեր կամ անուններ, զանգվածներ կամ հղումներ են, որոնք պարունակում են թվեր, որոնց համար դուք ցանկանում եք ամբողջության անհավասարակշռությունը"}, "SLOPE": {"a": "(known_ys; known_xs)", "d": "Վերադարձնում է գծային ռեգրեսիայի գծի թեքությունը տվյալ տվյալների կետերի միջով", "ad": "թվային կախված տվյալների կետերի զանգված կամ վանդակային տիրույթ է և կարող է լինել թվեր կամ անուններ, զանգվածներ կամ հղումներ, որոնք պարունակում են թվեր!անկախ տվյալների կետերի բազմություն է և կարող է լինել թվեր կամ անուններ, զանգվածներ կամ թվեր պարունակող հղումներ"}, "SMALL": {"a": "(array; k)", "d": "Վերադարձնում է տվյալների հավաքածուի k-րդ ամենափոքր արժեքը: Օրինակ՝ հինգերորդ ամենափոքր թիվը", "ad": "թվային տվյալների զանգված կամ տիրույթ է, որի համար ցանկանում եք որոշել k-րդ ամենափոքր արժեքը! վերադարձվող արժեքի զանգվածում կամ միջակայքում (ամենափոքրից) դիրքն է"}, "STANDARDIZE": {"a": "(x; mean; standard_dev)", "d": "Վերադարձնում է նորմալացված արժեք բաշխումից, որը բնութագրվում է միջին և ստանդարտ շեղումներով", "ad": "այն արժեքն է, որը ցանկանում եք նորմալացնել!բաշխման միջին թվաբանականն է!բաշխման ստանդարտ շեղումն է, դրական թիվ"}, "STDEV": {"a": "(number1; [number2]; ...)", "d": "Գնահատում է ստանդարտ շեղումը նմուշի հիման վրա (անտեսում է նմուշի տրամաբանական արժեքները և տեքստը)", "ad": "1-ից 255 թվեր են, որոնք համապատասխանում են ամբողջության ընտրանքին և կարող են լինել թվեր կամ հղումներ, որոնք պարունակում են թվեր"}, "STDEV.P": {"a": "(number1; [number2]; ...)", "d": "Հաշվում է ստանդարտ շեղումը` հիմնվելով որպես փաստարկ տրված ամբողջ բնակչության վրա (անտեսում է տրամաբանական արժեքները և տեքստը)", "ad": "1-ից 255 թվեր են, որոնք համապատասխանում են ամբողջությունը և կարող են լինել թվեր կամ թվեր պարունակող հղումներ"}, "STDEV.S": {"a": "(number1; [number2]; ...)", "d": "Գնահատում է ստանդարտ շեղումը նմուշի հիման վրա (անտեսում է նմուշի տրամաբանական արժեքները և տեքստը)", "ad": "1-ից 255 թվեր են, որոնք համապատասխանում են ամբողջության ընտրանքին և կարող են լինել թվեր կամ հղումներ, որոնք պարունակում են թվեր"}, "STDEVA": {"a": "(value1; [value2]; ...)", "d": "Գնահատում է ստանդարտ շեղումը նմուշի հիման վրա՝ ներառյալ տրամաբանական արժեքները և տեքստը: Տեքստը և ՍՈՒՏ տրամաբանական արժեքը ունեն 0 արժեքը; ՃՇՄԱՐԻՏ տրամաբանական արժեքը ունի 1 արժեքը", "ad": "1-ից 255 արժեքներ են, որոնք համապատասխանում են ամբողջության ընտրանքին և կարող են լինել արժեքներ կամ անուններ կամ հղումներ արժեքներին"}, "STDEVP": {"a": "(number1; [number2]; ...)", "d": "Հաշվում է ստանդարտ շեղումը` հիմնվելով որպես փաստարկ տրված ամբողջ բնակչության վրա (անտեսում է տրամաբանական արժեքները և տեքստը)", "ad": "1-ից 255 թվեր են, որոնք համապատասխանում են ամբողջությանը և կարող են լինել թվեր կամ թվեր պարունակող հղումներ"}, "STDEVPA": {"a": "(value1; [value2]; ...)", "d": "Հաշվում է ստանդարտ շեղումը` հիմնվելով ամբողջ բնակչության վրա, ներառյալ տրամաբանական արժեքները և տեքստը: Տեքստը և ՍՈՒՏ տրամաբանական արժեքը ունեն 0 արժեքը; ՃՇՄԱՐԻՏ տրամաբանական արժեքը ունի 1 արժեքը", "ad": "1-ից 255 արժեքներ են, որոնք համապատասխանում են ամբողջությանը և կարող են լինել արժեքներ, անուններ, զանգվածներ կամ հղումներ, որոնք պարունակում են արժեքներ"}, "STEYX": {"a": "(known_y's; known_x's)", "d": "Վերադարձնում է ռեգրեսիայի յուրաքանչյուր x-ի համար կանխատեսված y արժեքի ստանդարտ սխալը", "ad": "Կախված տվյալների կետերի զանգված կամ տիրույթ է և կարող է լինել թվեր կամ անուններ, զանգվածներ կամ հղումներ, որոնք պարունակում են թվեր! անկախ տվյալների միավորների զանգված կամ տիրույթ է և կարող է լինել թվեր կամ անուններ, զանգվածներ կամ հղումներ, որոնք պարունակում են թվեր"}, "TDIST": {"a": "(x; deg_freedom; tails)", "d": "Վերադարձնում է Ուսանողի t-բաշխումը", "ad": "այն թվային արժեքն է, որով գնահատվում է բաշխումը!ամբողջ թիվ է, որը ցույց է տալիս բաշխումը բնութագրող ազատության աստիճանների թիվը! նշում է վերադարձվող բաշխման վերրամասերի քանակը՝ միակողմանի վերրամասի բաշխում = 1; երկկողմանի վերրամասի բաշխում = 2"}, "TINV": {"a": "(probability; deg_freedom)", "d": "Վերադարձնում է Ուսանողի t-բաշխման երկու պոչով հակադարձ", "ad": "0-ից 1-ի միջ ներառյալ թիվ երկու պոչ ուսանողի t-բաշխման հետ կապված հավանականությունն է!դրական ամբողջ թիվ է, որը ցույց է տալիս բաշխումը բնութագրելու ազատության աստիճանների թիվը"}, "T.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "Վերադարձնում է ձախակողմյան Ուսանողի t-բաշխումը", "ad": "այն թվային արժեքն է, որով գնահատվում է բաշխումը!ամբողջ թիվ է, որը ցույց է տալիս բաշխումը բնութագրող ազատության աստիճանների թիվը! տրամաբանական արժեք է՝կուտակային բաշխման ֆունկցիայի համար օգտագործեք TRUE; հավանականության խտության ֆունկցիայի համար օգտագործեք FALSE"}, "T.DIST.2T": {"a": "(x; deg_freedom)", "d": "Վերադարձնում է երկու պոչ ուսանողի t-բաշխումը", "ad": "այն թվային արժեքն է, որով գնահատվում է բաշխումը! ամբողջ թիվ է, որը ցույց է տալիս բաշխումը բնութագրող ազատության աստիճանների թիվը"}, "T.DIST.RT": {"a": "(x; deg_freedom)", "d": "Վերադարձնում է աջակողմյան Ուսանողի t-բաշխումը", "ad": "այն թվային արժեքն է, որով գնահատվում է բաշխումը! ամբողջ թիվ է, որը ցույց է տալիս բաշխումը բնութագրող ազատության աստիճանների թիվը"}, "T.INV": {"a": "(probability; deg_freedom)", "d": "Վերադարձնում է Ուսանողի t-բաշխման ձախակողմյան հակադարձը", "ad": "0-ից 1-ի միջև ներառյալ թիվ երկու վերջամաս Student's t-բաշխման հետ կապված հավանականությունն! դրական ամբողջ թիվ է, որը ցույց է տալիս բաշխումը բնութագրելու ազատության աստիճանների թիվը"}, "T.INV.2T": {"a": "(probability; deg_freedom)", "d": "Վերադարձնում է Ուսանողի t-բաշխման երկու պոչով հակադարձ", "ad": "0-ից 1-ի միջև ներառյալ թիվ երկու վերջամաս Student's t-բաշխման հետ կապված հավանականությունն է! դրական ամբողջ թիվ է, որը ցույց է տալիս բաշխումը բնութագրելու ազատության աստիճանների թիվը"}, "T.TEST": {"a": "(array1; array2; tails; type)", "d": "Վերադարձնում է Ուսանողի թեստի-ի հետ կապված հավանականությունը", "ad": "առաջին տվյալների հավաքածուն է! Երկրորդ տվյալների հավաքածուն է!նշում է վերադարձվող բաշխման վերջամասերի քանակը՝ միակողմանի բաշխում = 1; երկկողմանի բաշխում = 2!t-թեստի տեսակն է՝ զուգավորված = 1, երկու նմուշի հավասար շեղում (հոմոսկեդաստիկ) = 2, երկու ընտրանքային անհավասար շեղում = 3"}, "TREND": {"a": "(known_y's; [known_x's]; [new_x's]; [const])", "d": "Վերադարձնում է թվերը գծային միտումով, որոնք համապատասխանում են հայտնի տվյալների կետերին՝ օգտագործելով նվազագույն քառակուսիների մեթոդը", "ad": "y արժեքների միջակայք կամ զանգված է, որը դուք արդեն գիտեք y = mx + b հարաբերություններում! կամընտիր տիրույթ է կամ x արժեքների զանգված, որը դուք գիտեք y = mx + b հարաբերության մեջ, զանգված նույն չափի, ինչ Known_y-ը:! նոր x արժեքների միջակայք կամ զանգված է, որի համար ցանկանում եք, որ TREND-ը վերադարձնի համապատասխան y արժեքներ! տրամաբանական արժեք է՝b հաստատունը սովորաբար հաշվարկվում է, եթե Const = TRUE կամ բաց թողնված; b-ը հավասար է 0-ի, եթե Const = FALSE"}, "TRIMMEAN": {"a": "(array; percent)", "d": "Վերադարձնում է տվյալների արժեքների հավաքածուի ներքին մասի միջինը", "ad": "կտրվածքի և միջինացման արժեքների միջակայքն է կամ զանգվածը! տվյալների հավաքածուի վերևից և ներքևից բացառվող տվյալների կետերի կոտորակային թիվն է"}, "TTEST": {"a": "(array1; array2; tails; type)", "d": "Վերադարձնում է Ուսանողի t- թեստ-ի հետ կապված հավանականությունը", "ad": "առաջին տվյալների հավաքածուն է! տվյալների երկրորդ հավաքածուն է!նշում է վերադարձվող բաշխման վերջամասերի քանակը՝ միակողմանի վերջամասերի բաշխում = 1; երկկողմանի վերջամասերի  բաշխում = 2!t-թեստի տեսակն է՝ զուգավորված = 1, երկու նմուշի հավասար շեղում (հոմոսկեդաստիկ) = 2, երկու ընտրանքային անհավասար շեղում = 3"}, "VAR": {"a": "(number1; [number2]; ...)", "d": "Գնահատում է շեղումը նմուշի հիման վրա (անտեսում է նմուշի տրամաբանական արժեքները և տեքստը)", "ad": "1-ից 255 թվային փաստարկներ են, որոնք համապատասխանում են ամբողջության ընտրանքին"}, "VAR.P": {"a": "(number1; [number2]; ...)", "d": "Հաշվում է դիսպերսիան՝ հիմնվելով ամբողջ բնակչության վրա (անտեսում է տրամաբանական արժեքները և տեքստը պոպուլյացիայի մեջ)", "ad": "1-ից 255 թվային փաստարկներ են, որոնք համապատասխանում են ամբողջությանը"}, "VAR.S": {"a": "(number1; [number2]; ...)", "d": "Գնահատում է շեղումը նմուշի հիման վրա (անտեսում է նմուշի տրամաբանական արժեքները և տեքստը)", "ad": "1-ից 255 թվային փաստարկներ են, որոնք համապատասխանում են ամբողջության ընտրանքին"}, "VARA": {"a": "(value1; [value2]; ...)", "d": "Գնահատում է տարբերությունը նմուշի հիման վրա՝ ներառյալ տրամաբանական արժեքները և տեքստը: Տեքստը և ՍՈՒՏ տրամաբանական արժեքը ունեն 0 արժեքը; ՃՇՄԱՐԻՏ տրամաբանական արժեքը ունի 1 արժեքը", "ad": "1-ից 255 արժեքային արգումենտներ են, որոնք համապատասխանում են ամբողջության ընտրանքին"}, "VARP": {"a": "(number1; [number2]; ...)", "d": "Հաշվում է դիսպերսիան՝ հիմնվելով ամբողջ բնակչության վրա (անտեսում է տրամաբանական արժեքները և տեքստը պոպուլյացիայի մեջ)", "ad": "1-ից 255 թվային փաստարկներ են, որոնք համապատասխանում են ամբողջությանը"}, "VARPA": {"a": "(value1; [value2]; ...)", "d": "Հաշվում է շեղումները՝ հիմնվելով ամբողջ բնակչության վրա, ներառյալ տրամաբանական արժեքները և տեքստը: Տեքստը և ՍՈՒՏ տրամաբանական արժեքը ունեն 0 արժեքը; ՃՇՄԱՐԻՏ տրամաբանական արժեքը ունի 1 արժեքը", "ad": "1-ից 255 արժեքային արգումենտներ են, որոնք համապատասխանում են ամբողջությանը"}, "WEIBULL": {"a": "(x; alpha; beta; cumulative)", "d": "Վերադարձնում է Վեյբուլի բաշխումը", "ad": "այն արժեքն է, որով կարելի է գնահատել ֆունկցիան, ոչ բացասական թիվ!բաշխման պարամետր է, դրական թիվ! բաշխման պարամետր է, դրական թիվ! տրամաբանական արժեք է!կուտակային բաշխման ֆունկցիայի համար օգտագործեք TRUE; հավանականության զանգվածի ֆունկցիայի համար օգտագործեք FALSE"}, "WEIBULL.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "Վերադարձնում է Վեյբուլի բաշխումը", "ad": "այն արժեքն է, որով կարելի է գնահատել ֆունկցիան, ոչ բացասական թիվ! բաշխման պարամետր է, դրական թիվ!բաշխման պարամետր է, դրական թիվ! տրամաբանական արժեք է!կուտակային բաշխման ֆունկցիայի համար օգտագործեք TRUE; հավանականության զանգվածի ֆունկցիայի համար օգտագործեք FALSE"}, "Z.TEST": {"a": "(array; x; [sigma])", "d": "Վերադարձնում է z թեստի միակողմանի P արժեքը", "ad": "այն տվյալների զանգվածն է կամ տիրույթը, որի նկատմամբ պետք է փորձարկվի X-ը!փորձարկման արժեքն է! ամբողջության (հայտնի) ստանդարտ շեղումն է: Բաց թողնելու դեպքում օգտագործվում է նմուշի ստանդարտ շեղումը"}, "ZTEST": {"a": "(array; x; [sigma])", "d": "Վերադարձնում է z թեստի միակողմանի P արժեքը", "ad": "այն տվյալների զանգվածն է կամ տիրույթը, որի նկատմամբ պետք է փորձարկվի X-ը!փորձարկման արժեքն է! ամբողջության (հայտնի) ստանդարտ շեղումն է: Բաց թողնելու դեպքում օգտագործվում է նմուշի ստանդարտ շեղումը"}, "ACCRINT": {"a": "(issue; first_interest; settlement; rate; par; frequency; [basis]; [calc_method])", "d": "Վերադարձնում է պարբերական տոկոսներ վճարող արժեթղթի հաշվեգրված տոկոսները.", "ad": "արժեթղթի թողարկման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի առաջին տոկոսադրույքն է, արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար! արժեթղթի տարեկան արժեկտրոնային դրույքաչափն է!արժեթղթի անվանական արժեքն է!տարեկան կտրոնային վճարումների քանակն է!օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է! տրամաբանական արժեք է՝ մինչև թողարկման ամսաթվից հաշվեգրված տոկոսները = TRUE կամ բաց թողնված; վերջին արժեկտրոնի վճարման ամսաթվից հաշվարկելու համար = FALSE"}, "ACCRINTM": {"a": "(issue; settlement; rate; par; [basis])", "d": "Վերադարձնում է հաշվեգրված տոկոսն արժեթղթի համար, որը տոկոս է վճարում մարման ժամկետում", "ad": "արժեթղթի թողարկման ամսաթիվն է՝ արտահայտված որպես հերթականության համար! արժեթղթի մարման ժամկետն է՝ արտահայտված որպես հերթական ամսաթվի համար! արժեթղթի տարեկան արժեկտրոնային դրույքաչափն է! արժեթղթի անվանական արժեքն է: Օրվա հաշվարկի հիմքն է, որն օգտագործվում է"}, "AMORDEGRC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Վերադարձնում է ակտիվի համաչափ գծային մաշվածությունը յուրաքանչյուր հաշվետու ժամանակաշրջանի համար:", "ad": "ակտիվի արժեքն է!ակտիվի գնման ամսաթիվն է! առաջին շրջանի ավարտի ամսաթիվն է! ակտիվի շահագործման վերջում փրկված արժեքն է:! ժամանակաշրջանն է! արժեզրկման ցուցանիշն է! year_basis. 0 360 օրվա տարվա համար, 1 փաստացի, 3 365 օրվա համար:"}, "AMORLINC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Վերադարձնում է ակտիվի համաչափ գծային մաշվածությունը յուրաքանչյուր հաշվետու ժամանակաշրջանի համար.", "ad": "ակտիվի ինքնարժեքն է!ակտիվի գնման ամսաթիվն է!այն ակտիվի գործունեության ավարտի ավարտին լուծարման արժեքն է!ակտիվի շահագործման վերջում փրկված արժեքն է!ժամանակաշրջանն է!year_basis. 0 360 օրվա տարվա համար, 1 փաստացի, 3 365 օրվա համար:"}, "COUPDAYBS": {"a": "(settlement; maturity; frequency; [basis])", "d": "Վերադարձնում է արժեկտրոնային ժամանակաշրջանի սկզբից մինչև վճարման ամսաթիվը օրերի քանակը", "ad": "արժեթղթի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի մարման ժամկետն է, արտահայտված որպես հերթական ամսաթվի համար!տարեկան արժեկտրոնային վճարումների թիվն է!օրերի հաշվարկի հիմքի տեսակն է, որն օգտագործվում է:"}, "COUPDAYS": {"a": "(settlement; maturity; frequency; [basis])", "d": "Վերադարձնում է արժեկտրոնային ժամանակաշրջանի օրերի քանակը, որը պարունակում է վճարման ամսաթիվը", "ad": "արժեթղթի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի մարման ժամկետն է՝ արտահայտված որպես հերթական ամսաթվի համար! տարեկան կտրոնային վճարումների քանակն է! օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "COUPDAYSNC": {"a": "(settlement; maturity; frequency; [basis])", "d": "Վերադարձնում է հաշվարկի ամսաթվից մինչև հաջորդ արժեկտրոնի ամսաթիվը օրերի քանակը", "ad": "արժեթղթի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար! արժեթղթի մարման ժամկետն է՝ արտահայտված որպես հերթական ամսաթվի համար!տարեկան արժեկտրոնային վճարումների թիվն է! օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "COUPNCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "Վերադարձնում է հաջորդ արժեկտրոնի ամսաթիվը հաշվարկի ամսաթվից հետո", "ad": "արժեթղթի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի մարման ժամկետն է՝ արտահայտված որպես հերթական ամսաթվի համար! տարեկան կտրոնային վճարումների քանակն է!օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "COUPNUM": {"a": "(settlement; maturity; frequency; [basis])", "d": "Վերադարձնում է վճարման ենթակա արժեկտրոնների քանակը մարման ամսաթվի և մարման ամսաթվի միջև", "ad": "արժեթղթի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի մարման ժամկետն է՝ արտահայտված որպես հերթական ամսաթվի համար!տարեկան կտրոնային վճարումների քանակն է! օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "COUPPCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "Վերադարձնում է նախորդ արժեկտրոնի ամսաթիվը մինչև հաշվարկի ամսաթիվը", "ad": "արժեթղթի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի մարման ժամկետն է՝ արտահայտված որպես հերթական ամսաթվի համար! տարեկան կտրոնների վճարումների քանակն է!օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "CUMIPMT": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Վերադարձնում է երկու ժամանակաշրջանների միջև վճարված կուտակային տոկոսները", "ad": "տոկոսադրույքն է!վճարման ժամկետների ընդհանուր թիվն է! ներկա արժեքն է!հաշվարկի առաջին շրջանն է! հաշվարկի վերջին շրջանն է!վճարման ժամկետն է"}, "CUMPRINC": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Վերադարձնում է երկու ժամանակաշրջանների միջև փոխառության դիմաց վճարված կուտակային մայր գումարը", "ad": "տոկոսադրույքն է!վճարման ժամկետների ընդհանուր թիվն է!ներկա արժեքն է! հաշվարկի առաջին պարբերաշրջանն է!հաշվարկի վերջին շրջանն է! վճարման ժամկետն է"}, "DB": {"a": "(cost; salvage; life; period; [month])", "d": "Վերադարձնում է ակտիվի մաշվածությունը որոշակի ժամանակահատվածի համար՝ օգտագործելով ֆիքսված-նվազող մնացորդի մեթոդը", "ad": "ակտիվի սկզբնական արժեքն է! այն ակտիվի գործունեության ավարտի ավարտին լուծարման արժեքն է! այն ժամանակաշրջանների քանակն է, որոնց ընթացքում ակտիվը մաշվում է (երբեմն կոչվում է ակտիվի օգտակար ծառայության ժամկետ)! այն ժամանակահատվածն է, որի համար ցանկանում եք հաշվարկել մաշվածությունը: Ժամանակահատվածը պետք է օգտագործի նույն միավորները, ինչպես Life! առաջին տարվա ամիսների թիվն է: Եթե ամիսը բաց է թողնվում, ապա ենթադրվում է, որ այն 12 է"}, "DDB": {"a": "(cost; salvage; life; period; [factor])", "d": "Վերադարձնում է ակտիվի մաշվածությունը որոշակի ժամանակահատվածի համար՝ օգտագործելով կրկնակի նվազող մնացորդի մեթոդը կամ ձեր նշած որևէ այլ մեթոդ", "ad": "ակտիվի սկզբնական արժեքն է!այն ակտիվի գործունեության ավարտի ավարտին լուծարման արժեքն է! այն ժամանակաշրջանների քանակն է, որոնց ընթացքում ակտիվը մաշվում է (երբեմն կոչվում է ակտիվի օգտակար ծառայության ժամկետ)! այն ժամանակահատվածն է, որի համար ցանկանում եք հաշվարկել մաշվածությունը: Ժամանակահատվածը պետք է օգտագործի նույն միավորները, ինչպես Life! այն արագությունն է, որով մնացորդը նվազում է: Եթե Գործակիցը բաց է թողնված, ապա ենթադրվում է, որ այն 2 է (կրկնակի նվազող մնացորդի մեթոդ)"}, "DISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "Վերադարձնում է արժեթղթի զեղչի դրույքաչափը", "ad": "արժեթղթի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար! արժեթղթի մարման ժամկետն է՝ արտահայտված որպես հերթական ամսաթվի համար! արժեթղթի գինը $100 անվանական արժեքն է! արժեթղթի մարման արժեքն է $100 անվանական արժեքի դիմաց! օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "DOLLARDE": {"a": "(fractional_dollar; fraction)", "d": "Դոլարի գինը՝ արտահայտված կոտորակի տեսքով, փոխակերպում է դոլարի գնի՝ արտահայտված տասնորդական թվի տեսքով", "ad": "կոտորակով արտահայտված թիվ է! կոտորակի հայտարարի մեջ օգտագործվող ամբողջ թիվն է"}, "DOLLARFR": {"a": "(decimal_dollar; fraction)", "d": "Դոլարի գինը՝ արտահայտված տասնորդական թվով, փոխակերպում է դոլարի գնի՝ արտահայտված կոտորակի տեսքով", "ad": "տասնորդական թիվ է! ամբողջ թիվն է, որն օգտագործվում է կոտորակի հայտարարում"}, "DURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "Վերադարձնում է արժեթղթի տարեկան տևողությունը՝ պարբերական տոկոսավճարներով", "ad": "արժեթղթի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար! արժեթղթի մարման ժամկետն է՝ արտահայտված որպես հերթական ամսաթվի համար! արժեթղթի տարեկան արժեկտրոնային դրույքաչափն է! արժեթղթի տարեկան եկամտաբերությունն է! տարեկան կտրոնային վճարումների քանակն է!օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "EFFECT": {"a": "(nominal_rate; npery)", "d": "Վերադարձնում է տարեկան արդյունավետ տոկոսադրույքը", "ad": "անվանական տոկոսադրույքն է! Տարեկան բարդացման ժամանակաշրջանների թիվն է"}, "FV": {"a": "(rate; nper; pmt; [pv]; [type])", "d": "Վերադարձնում է ներդրումների ապագա արժեքը՝ հիմնված պարբերական, մշտական ​​վճարումների և մշտական ​​տոկոսադրույքի վրա", "ad": "ժամանակաշրջանի տոկոսադրույքն է: Օրինակ, օգտագործեք 6%/4 եռամսյակային վճարումների համար 6% APR!ներդրումների վճարման ժամկետների ընդհանուր թիվն է! յուրաքանչյուր ժամանակահատվածում կատարված վճարումն է. այն չի կարող փոխվել ներդրման ողջ ընթացքում! ներկա արժեքն է կամ միանվագ գումարը, որն այժմ արժե մի շարք ապագա վճարումներ: Եթե բաց թողնված է, Pv = 0-ը! արժեք է, որը ներկայացնում է վճարման ժամանակը. վճարում ժամանակաշրջանի սկզբում = 1; վճարումը ժամանակաշրջանի վերջում = 0 կամ բաց թողնված"}, "FVSCHEDULE": {"a": "(principal; schedule)", "d": "Վերադարձնում է սկզբնական մայր գումարի ապագա արժեքը բարդ տոկոսադրույքների մի շարք կիրառելուց հետո", "ad": "ներկա արժեքն է. կիրառելի տոկոսադրույքների զանգված է"}, "INTRATE": {"a": "(settlement; maturity; investment; redemption; [basis])", "d": "Վերադարձնում է ամբողջությամբ ներդրված արժեթղթի տոկոսադրույքը", "ad": "արժեթղթի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար! արժեթղթի մարման ժամկետն է՝ արտահայտված որպես հերթական ամսաթվի համար! արժեթղթում ներդրված գումարն է! մարման պահին ստացվելիք գումարն է! օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "IPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Վերադարձնում է ներդրման համար տվյալ ժամանակահատվածի տոկոսավճարը՝ հիմնված պարբերական, մշտական ​​վճարումների և հաստատուն տոկոսադրույքի վրա", "ad": "ժամանակաշրջանի տոկոսադրույքն է: Օրինակ, օգտագործեք 6%/4 եռամսյակային վճարումների համար 6% APR! այն ժամանակահատվածն է, որի համար ցանկանում եք գտնել հետաքրքրությունը և պետք է լինի 1-ից մինչև Nper միջակայքում! ներդրումների վճարման ժամկետների ընդհանուր թիվն է! ներկա արժեքն է կամ միանվագ գումարը, որն այժմ արժե մի շարք ապագա վճարումներ! ապագա արժեքն է կամ կանխիկի մնացորդը, որը ցանկանում եք ձեռք բերել վերջին վճարումը կատարելուց հետո: Եթե բաց թողնվի, Fv = 0! Վճարման ժամկետը ներկայացնող տրամաբանական արժեք է՝ ժամանակաշրջանի վերջում = 0 կամ բաց թողնված, ժամանակաշրջանի սկզբում = 1"}, "IRR": {"a": "(values; [guess])", "d": "Վերադարձնում է մի շարք դրամական հոսքերի ներքին եկամտաբերությունը", "ad": "զանգված կամ հղում է վանդակներին, որոնք պարունակում են թվեր, որոնց համար ցանկանում եք հաշվարկել վերադարձի ներքին դրույքաչափը! մի թիվ է, որը դուք կռահում եք, որ մոտ է IRR-ի արդյունքին. 0.1 (10 տոկոս), եթե բաց թողնվի"}, "ISPMT": {"a": "(rate; per; nper; pv)", "d": "Վերադարձնում է ներդրման որոշակի ժամանակահատվածում վճարված տոկոսները", "ad": "տոկոսադրույքը մեկ ժամանակաշրջանի համար: Օրինակ, օգտագործեք 6%/4 եռամսյակային վճարումների համար 6% APR! ժամանակահատված, որի համար ցանկանում եք գտնել հետաքրքրությունը! ներդրումների վճարման ժամկետների քանակը! միանվագ գումարը, որը մի շարք ապագա վճարումների հենց հիմա է"}, "MDURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "Վերադարձնում է Macauley-ի փոփոխված տևողությունը $100 ենթադրյալ անվանական արժեքով արժեթղթի համար", "ad": "արժեթղթի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար! արժեթղթի մարման ժամկետն է՝ արտահայտված որպես հերթական ամսաթվի համար! արժեթղթի տարեկան արժեկտրոնային դրույքաչափն է!արժեթղթի տարեկան եկամտաբերությունն է! տարեկան կտրոնային վճարումների քանակն է! օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "MIRR": {"a": "(values; finance_rate; reinvest_rate)", "d": "Վերադարձնում է եկամտաբերության ներքին դրույքաչափը մի շարք պարբերական դրամական հոսքերի համար՝ հաշվի առնելով ինչպես ներդրումների արժեքը, այնպես էլ դրամական միջոցների վերաներդրման տոկոսները", "ad": "զանգված կամ հղում վանդակներին, որոնք պարունակում են թվեր, որոնք ներկայացնում են մի շարք վճարումներ (բացասական) և եկամուտ (դրական) կանոնավոր ժամանակաշրջաններում! այն տոկոսադրույքն է, որը դուք վճարում եք դրամական միջոցների հոսքերում օգտագործված գումարների համար! այն տոկոսադրույքն է, որը դուք ստանում եք դրամական միջոցների հոսքերի վրա, երբ դրանք վերաներդրեք"}, "NOMINAL": {"a": "(effect_rate; npery)", "d": "Վերադարձնում է տարեկան անվանական տոկոսադրույքը", "ad": "Արդյունավետ տոկոսադրույքն է: Տարեկան բարդացման ժամանակաշրջանների թիվն է"}, "NPER": {"a": "(rate; pmt; pv; [fv]; [type])", "d": "Վերադարձնում է ներդրումների համար ժամանակաշրջանների քանակը՝ հիմնված պարբերական, մշտական ​​վճարումների և մշտական ​​տոկոսադրույքի վրա", "ad": "ժամանակաշրջանի տոկոսադրույքն է: Օրինակ, օգտագործեք 6%/4 եռամսյակային վճարումների համար 6%APR! յուրաքանչյուր ժամանակահատվածում կատարված վճարումն է! ներկա արժեքն է կամ միանվագ գումարը, որն այժմ արժե մի շարք ապագա վճարումներ! ապագա արժեքն է կամ կանխիկի մնացորդը, որը ցանկանում եք ձեռք բերել վերջին վճարումը կատարելուց հետո: Բաց թողնելու դեպքում օգտագործվում է զրո! տրամաբանական արժեք է՝ վճարում ժամանակաշրջանի սկզբում = 1; վճարումը ժամանակաշրջանի վերջում = 0 կամ բաց թողնված"}, "NPV": {"a": "(rate; value1; [value2]; ...)", "d": "Վերադարձնում է ներդրման զուտ ներկա արժեքը՝ հիմնված զեղչման դրույքաչափի և մի շարք ապագա վճարումների (բացասական արժեքներ) և եկամուտների (դրական արժեքների) վրա:", "ad": "մեկ ժամանակահատվածի ընթացքում զեղչի դրույքաչափն է!1-ից 254 վճարումներ և եկամուտներ են, որոնք ժամանակի մեջ հավասարապես բաժանված են և տեղի են ունենում յուրաքանչյուր ժամանակաշրջանի վերջում"}, "ODDFPRICE": {"a": "(settlement; maturity; issue; first_coupon; rate; yld; redemption; frequency; [basis])", "d": "Վերադարձնում է արժեթղթի 100 ԱՄՆ դոլար անվանական արժեքի արժեքը կենտ առաջին շրջանով", "ad": "արժեթղթի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար! արժեթղթի մարման ժամկետն է՝ արտահայտված որպես հերթական ամսաթվի համար! արժեթղթի թողարկման ամսաթիվն է՝ արտահայտված որպես հերթականության համար! արժեթղթի առաջին արժեկտրոնի ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար! արժեթղթի տոկոսադրույքն է! արժեթղթի տարեկան եկամտաբերությունն է! արժեթղթի մարման արժեքն է $100 անվանական արժեքի դիմաց! տարեկան կտրոնային վճարումների քանակն է! օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "ODDFYIELD": {"a": "(settlement; maturity; issue; first_coupon; rate; pr; redemption; frequency; [basis])", "d": "Վերադարձնում է կենտ առաջին շրջանով արժեթղթի եկամտաբերությունը", "ad": "արժեթղթի մարման ամսաթիվն է` արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի մարման ամսաթիվն է,  արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի թողարկման ամսաթիվն է, արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի առաջին արժեկտրոնի ամսաթիվն է՝ արտահայտված որպես սերիական!ամսաթվի համարը. արժեթղթի տոկոսադրույքն է. արժեթղթի արժեքն է! արժեթղթի մարման արժեքն է $100 անվանական արժեքի համար!տարեկան արժեկտրոնային վճարումների թիվն է!օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "ODDLPRICE": {"a": "(settlement; maturity; last_interest; rate; yld; redemption; frequency; [basis])", "d": "Վերադարձնում է արժեթղթի 100 ԱՄՆ դոլար անվանական արժեքի գինը տարօրինակ վերջին ժամանակաշրջանով", "ad": "արժեթղթի մարման ամսաթիվն է, արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի մարման ժամկետն է, արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի վերջին արժեկտրոնի ամսաթիվն է, արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի տոկոսադրույքն է!արժեթղթի տարեկան  եկամտաբերությունը!արժեթղթի մարման արժեքն է $100 անվանական արժեքի դիմաց! տարեկան կտրոնների վճարումների թիվն է! օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "ODDLYIELD": {"a": "(settlement; maturity; last_interest; rate; pr; redemption; frequency; [basis])", "d": "Վերադարձնում է կենտ վերջին շրջանով արժեթղթի եկամտաբերությունը", "ad": "արժեթղթի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար! արժեթղթի մարման ժամկետն է՝ արտահայտված որպես հերթական ամսաթվի համար! արժեթղթի վերջին արժեկտրոնի ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի տոկոսադրույքն է!արժեթղթի գինն է! արժեթղթի մարման արժեքն է $100 անվանական արժեքի դիմաց! օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "PDURATION": {"a": "(rate; pv; fv)", "d": "Վերադարձնում է որոշակի արժեքին հասնելու համար ներդրման համար պահանջվող ժամանակահատվածների քանակը", "ad": "ժամանակաշրջանի տոկոսադրույքն է! ներդրման ներկա արժեքն է!ներդրման ապագա ապագա արժեքն է "}, "PMT": {"a": "(rate; nper; pv; [fv]; [type])", "d": "Հաշվարկում է վարկի դիմաց վճարումը` հիմնված մշտական ​​վճարումների և հաստատուն տոկոսադրույքի վրա", "ad": "վարկի մեկ ժամանակահատվածի տոկոսադրույքն է: Օրինակ, օգտագործեք 6%/4 եռամսյակային վճարումների համար 6% APR-ով!վարկի համար վճարումների ընդհանուր թիվն է!ներկա արժեքն է՝ այն ընդհանուր գումարը, որն այժմ արժե մի շարք ապագա վճարումներ: ապագա արժեքն է, կամ կանխիկի մնացորդը, որը ցանկանում եք ձեռք բերել վերջին վճարումը կատարելուց հետո, 0 (զրո), եթե բաց թողնվի!տրամաբանական արժեք է՝վճարում ժամանակաշրջանի սկզբում = 1; վճարումը ժամանակաշրջանի վերջում = 0 կամ բաց թողնված"}, "PPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Վերադարձնում է մայր գումարի վճարումը տվյալ ներդրման համար՝ հիմնված պարբերական, մշտական ​​վճարումների և հաստատուն տոկոսադրույքի վրա", "ad": "ժամանակաշրջանի տոկոսադրույքն է: Օրինակ, օգտագործեք 6%/4 եռամսյակային վճարումների համար 6% APR-ով!սահմանում է ժամանակաշրջանը և պետք է լինի 1-ից մինչև nper միջակայքում! ներդրումների վճարման ժամկետների ընդհանուր թիվն է! ներկա արժեքն է`ընդհանուր գումարը, որը  ապագա վճարումների շարքն արժե հիմա!ապագա արժեքն է կամ կանխիկի մնացորդը, որը ցանկանում եք ձեռք բերել վերջին վճարումը կատարելուց հետո:!տրամաբանական արժեք է՝վճարում ժամանակաշրջանի սկզբում = 1; վճարումը ժամանակաշրջանի վերջում = 0 կամ բաց թողնված"}, "PRICE": {"a": "(settlement; maturity; rate; yld; redemption; frequency; [basis])", "d": "Վերադարձնում է արժեթղթի 100 ԱՄՆ դոլար անվանական արժեքի գինը, որը պարբերաբար տոկոսներ է վճարում", "ad": "արժեթղթի մարման ամսաթիվն է, արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի մարման ամսաթիվն է, արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի տարեկան արժեկտրոնի տոկոսադրույքն է!արժեթղթի տարեկան եկամտաբերությունն է!արժեթղթի մարման արժեքն է $100 անվանական արժեքի համար! տարեկան կտրոնների վճարումների քանակը! օրվա հաշվարկի հիմքն է"}, "PRICEDISC": {"a": "(settlement; maturity; discount; redemption; [basis])", "d": "Վերադարձնում է զեղչված արժեթղթի 100 ԱՄՆ դոլար անվանական արժեքի գինը", "ad": "արժեթղթի մարման ամսաթիվն է` արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի մարման ամսաթիվն է, արտահայտված որպես հերթական ամսաթվի համար! արժեթղթի մարման արժեքն է $100 անվանական արժեքի դիմաց! օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "PRICEMAT": {"a": "(settlement; maturity; issue; rate; yld; [basis])", "d": "Վերադարձնում է արժեթղթի 100 ԱՄՆ դոլար անվանական արժեքի գինը, որը տոկոսներ է վճարում մարման ժամանակ", "ad": "արժեթղթի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի մարման ժամկետն է, արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի թողարկման ամսաթիվն է, արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի տոկոսադրույքն է թողարկման ամսաթվի դրությամբ!Արժեթղթի տարեկան եկամտաբերությունը!օրական հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "PV": {"a": "(rate; nper; pmt; [fv]; [type])", "d": "Վերադարձնում է ներդրման ներկա արժեքը. այն ընդհանուր գումարը, որն այժմ արժե մի շարք ապագա վճարումներ", "ad": "ժամանակաշրջանի տոկոսադրույքն է: Օրինակ, օգտագործեք 6%/4 եռամսյակային վճարումների համար 6% APR-ով!սա ներդրման վճարման ժամանակաշրջանների ընդհանուր թիվն է!վճարումն է, որը կատարվել է յուրաքանչյուր ժամանակահատվածում և չի կարող փոխվել ներդրման ողջ ընթացքում! ապագա արժեքն է, կամ Կանխիկի մնացորդը, որը ցանկանում եք ձեռք բերել վերջին վճարումը կատարելուց հետո!տրամաբանական արժեք է՝ վճարում ժամանակաշրջանի սկզբում = 1; վճարումը ժամանակաշրջանի վերջում = 0 կամ բաց թողնված"}, "RATE": {"a": "(nper; pmt; pv; [fv]; [type]; [guess])", "d": "Վերադարձնում է տոկոսադրույքը վարկի կամ ներդրման ժամանակաշրջանի համար: Օրինակ, օգտագործեք 6%/4 եռամսյակային վճարումների համար 6% ապրիլին", "ad": "վարկի կամ ներդրման համար վճարման ժամկետների ընդհանուր թիվն է!վճարումն է, որը կատարվել է յուրաքանչյուր ժամանակահատվածում և չի կարող փոխվել վարկի կամ ներդրման ժամկետի ընթացքում !ներկա արժեքն է!այն ընդհանուր գումարն է, որն այժմ արժե մի շարք ապագա վճարումներ: ապագա արժեքը կամ կանխիկի մնացորդը, որը ցանկանում եք ձեռք բերել վերջին վճարումը կատարելուց հետո: Եթե բաց թողնված է, օգտագործում է Fv = 0-ը! տրամաբանական արժեք է՝ վճարում ժամանակաշրջանի սկզբում = 1; վճարումը ժամանակաշրջանի վերջում = 0 կամ բաց թողնված!ձեր ենթադրությունն է, թե որն է լինելու դրույքաչափը. եթե բաց թողնված է, Գուշակիր = 0,1 (10 տոկոս)"}, "RECEIVED": {"a": "(settlement; maturity; investment; discount; [basis])", "d": "Վերադարձնում է մարման պահին ստացված գումարը ամբողջությամբ ներդրված արժեթղթի համար", "ad": "արժեթղթի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի մարման ժամկետն է, արտահայտված որպես հերթական ամսաթվի համար!արժեթղթում ներդրված գումարն է! արժեթղթի զեղչի դրույքաչափն է!րվա հաշվարկի հիմքն է"}, "RRI": {"a": "(nper; pv; fv)", "d": "Վերադարձնում է համարժեք տոկոսադրույք ներդրումների աճի համար", "ad": "ներդրումների ժամանակաշրջանների քանակն է!ներդրման ներկա արժեքն է! ներդրումների ապագա արժեքն է"}, "SLN": {"a": "(cost; salvage; life)", "d": "Վերադարձնում է ակտիվի գծային մաշվածությունը մեկ ժամանակաշրջանի համար", "ad": "ակտիվի սկզբնական արժեքն է!այն ակտիվի կյանքի վերջում փրկված արժեքն է! այն ժամանակաշրջանների քանակն է, որոնց ընթացքում ակտիվը մաշվում է (երբեմն կոչվում է ակտիվի օգտակար ծառայության ժամկետ)"}, "SYD": {"a": "(cost; salvage; life; per)", "d": "Վերադարձնում է ակտիվի ամորտիզացիան տարիների գումարի թվանշանները որոշակի ժամանակահատվածի համար", "ad": "ակտիվի սկզբնական ինքնարժեքն է! փրկարար արժեքն է ակտիվի ժամկետի վերջում!այն ժամանակաշրջանների քանակն է, որոնց ընթացքում ակտիվը մաշվում է (երբեմն կոչվում է ակտիվի օգտակար ծառայության ժամկետ)!ժամանակաշրջան է և պետք է օգտագործի նույն միավորները, ինչ Life"}, "TBILLEQ": {"a": "(settlement; maturity; discount)", "d": "Վերադարձնում է պարտատոմսերի համարժեք եկամտաբերությունը գանձապետական ​​մուրհակի համար", "ad": "Treasury հաշվի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար!Treasury հաշվի մարման ամսաթիվն է, արտահայտված որպես հերթական ամսաթվի համար!Treasury հաշվի զեղչման տոկոսադրույքն է"}, "TBILLPRICE": {"a": "(settlement; maturity; discount)", "d": "Վերադարձնում է գանձապետական ​​մուրհակի 100 դոլար անվանական արժեքի գինը", "ad": "Treasury հաշվի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար! Treasury հաշվի մարման ժամկետն է՝ արտահայտված որպես հերթական ամսաթվի համար!Treasury հաշվի զեղչման տոկոսադրույքն է"}, "TBILLYIELD": {"a": "(settlement; maturity; pr)", "d": "Վերադարձնում է գանձապետական ​​մուրհակի եկամտաբերությունը", "ad": "Treasury հաշվի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար!Treasury պարտատոմսերի մարման ժամկետն է՝ արտահայտված որպես հերթական ամսաթվի համար!Treasury հաշվի արժեքն է $100 անվանական արժեքի դիմաց"}, "VDB": {"a": "(cost; salvage; life; start_period; end_period; [factor]; [no_switch])", "d": "Վերադարձնում է ակտիվի մաշվածությունը ձեր նշած ցանկացած ժամանակաշրջանի համար, ներառյալ մասնակի ժամանակաշրջանները, օգտագործելով կրկնակի նվազող մնացորդի մեթոդը կամ ձեր նշած որևէ այլ մեթոդ", "ad": "ակտիվի սկզբնական արժեքն է: Այն  ակտիվի շահագործման վերջում փրկարար արժեքն է!Այն ժամանակաշրջանների քանակն է, որոնց ընթացքում ակտիվը մաշվում է (երբեմն կոչվում է ակտիվի օգտակար ծառայության ժամկետ)! որով ցանկանում եք հաշվարկել մաշվածությունը, նույն միավորներով, ինչ Life-ը!վերջնաժամկետն է, որի համար ցանկանում եք հաշվարկել մաշվածությունը, նույն միավորներով, ինչպես Life-ը! մնացորդի անկման տեմպն է, 2 (կրկնակի նվազող մնացորդը ) եթե բաց թողնված է! անցեք ուղիղ գծով մաշվածության, երբ մաշվածությունը ավելի մեծ է, քան նվազող մնացորդը = FALSE կամ բաց թողնված; մի փոխիր = TRUE"}, "XIRR": {"a": "(values; dates; [guess])", "d": "Վերադարձնում է եկամտաբերության ներքին դրույքաչափը դրամական միջոցների հոսքերի ժամանակացույցի համար", "ad": "դրամական հոսքերի շարք է, որը համապատասխանում է ամսաթվերով վճարումների ժամանակացույցին! Վճարման ամսաթվերի ժամանակացույց է, որը համապատասխանում է դրամական միջոցների հոսքերի վճարումներին: Թիվ է, որը, ըստ Ձեզ, մոտ է XIRR-ի արդյունքին"}, "XNPV": {"a": "(rate; values; dates)", "d": "Վերադարձնում է զուտ ներկա արժեքը դրամական միջոցների հոսքերի ժամանակացույցի համար", "ad": "դրամական միջոցների հոսքերի նկատմամբ կիրառվող զեղչի դրույքաչափն է!դրամական հոսքերի մի շարք է, որը համապատասխանում է ամսաթվերով վճարումների ժամանակացույցին!վճարման ամսաթվերի ժամանակացույց է, որը համապատասխանում է դրամական հոսքերի վճարումներին"}, "YIELD": {"a": "(settlement; maturity; rate; pr; redemption; frequency; [basis])", "d": "Վերադարձնում է պարբերական տոկոսներ վճարող արժեթղթի եկամտաբերությունը", "ad": "արժեթղթի մարման ամսաթիվն է, արտահայտված որպես հերթական ամսաթվի համար: Արժեթղթի մարման ամսաթիվն է, արտահայտված որպես հերթական ամսաթվի համար: Արժեթղթի տարեկան արժեկտրոնային տոկոսադրույքն է! արժեթղթի արժեքը $100 անվանական արժեքի համար! արժեքը! տարեկան կտրոնների վճարումների քանակն է: Օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "YIELDDISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "Վերադարձնում է զեղչված արժեթղթի տարեկան եկամտաբերությունը: Օրինակ՝ գանձապետական ​​մուրհակ", "ad": "արժեթղթի մարման ամսաթիվն է, արտահայտված որպես հերթական ամսաթվի համար: Արժեթղթի մարման ամսաթիվն է, արտահայտված որպես հերթական ամսաթվի համար: Արժեթղթի արժեքը $100 անվանական արժեքի դիմաց! Օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "YIELDMAT": {"a": "(settlement; maturity; issue; rate; pr; [basis])", "d": "Վերադարձնում է արժեթղթի տարեկան եկամտաբերությունը, որը տոկոս է վճարում մարման ժամանակ", "ad": "արժեթղթի մարման ամսաթիվն է՝ արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի մարման ժամկետն է, արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի թողարկման ամսաթիվն է, արտահայտված որպես հերթական ամսաթվի համար!արժեթղթի տոկոսադրույքն է թողարկման ամսաթվի դրությամբ!արժեթղթի արժեքը $100 անվանական արժեքի համար: Օրվա հաշվարկի հիմքի տեսակն է, որն օգտագործվում է"}, "ABS": {"a": "(number)", "d": "Վերադարձնում է թվի բացարձակ արժեքը՝ առանց իր նշանի", "ad": "այն իրական թիվն է, որի համար ցանկանում եք բացարձակ արժեքը"}, "ACOS": {"a": "(number)", "d": "Վերադարձնում է թվի արկկոսինը՝ ռադիաններով 0-ից Pi միջակայքում: Արկկոսինն այն անկյունն է, որի կոսինուսը թիվ է", "ad": "ձեր ցանկացած անկյան կոսինուսն է և պետք է լինի -1-ից մինչև 1"}, "ACOSH": {"a": "(number)", "d": "Վերադարձնում է թվի հակադարձ հիպերբոլիկ կոսինուսը", "ad": "ցանկացած իրական թիվ է 1-ին հավասար կամ մեծ"}, "ACOT": {"a": "(number)", "d": "Վերադարձնում է թվի արկոտանգենսը ռադիաններով 0-ից Pi միջակայքում.", "ad": "ձեր ցանկացած անկյան կոտանգենսն է"}, "ACOTH": {"a": "(number)", "d": "Վերադարձնում է թվի հակադարձ հիպերբոլիկ կոտանգենսը", "ad": "ձեր ցանկացած անկյան հիպերբոլիկ կոտանգենսն է"}, "AGGREGATE": {"a": "(function_num; options; ref1; ...)", "d": "Վերադարձնում է ագրեգատը ցուցակում կամ տվյալների բազայում", "ad": "1-ից 19 թիվը, որը սահմանում է ագրեգատի ամփոփ գործառույթը.! 0-ից 7 թիվը, որը սահմանում է ագրեգատի համար անտեսման ենթակա արժեքները!Այն թվային տվյալների զանգվածն է կամ միջակայքը!որի վրա պետք է հաշվարկվի ագրեգատը․ ցույց է տալիս դիրքը: զանգվածում; դա k--րդ ամենամեծն է, k-րդ ամենափոքրը, k-րդ տոկոսը կամ k-րդ քառորդը!1-ից 19 թիվը, որը սահմանում է ագրեգատի ամփոփ գործառույթը!0-ից 7 թիվը, որը սահմանում է արժեքները: Ագրեգատի համար անտեսել!1-ից 253 միջակայքներ կամ հղումներ են, որոնց համար ցանկանում եք ագրեգատը"}, "ARABIC": {"a": "(text)", "d": "Հռոմեական թիվը վերածում է արաբերենի", "ad": "այն հռոմեական թիվն է, որը ցանկանում եք փոխարկել"}, "ASC": {"a": "(text)", "d": "Կրկնակի բայթ նիշերի հավաքածուի (DBCS) լեզուների համար ֆունկցիան փոխում է ամբողջ լայնության (կրկնակի բայթ) նիշերը կիսալայնության (մեկ բայթ) նիշերի:", "ad": "այն տեքստն է, որը ցանկանում եք փոխել: Եթե տեքստը չի պարունակում ամբողջական լայնությամբ տառեր, տեքստը չի փոխվում։"}, "ASIN": {"a": "(number)", "d": "Վերադարձնում է մի թվի աղեղնաշարը ռադիաններով՝ -Pi/2-ից Pi/2 միջակայքում", "ad": "ձեր ցանլացած անկյան սինուսն է և պետք է լինի -1-ից մինչև 1"}, "ASINH": {"a": "(number)", "d": "Վերադարձնում է թվի հակադարձ հիպերբոլիկ սինուսը", "ad": "ցանկացած իրական թիվ է 1-ին հավասար կամ մեծ"}, "ATAN": {"a": "(number)", "d": "Վերադարձնում է թվի արկտանգենսը ռադիաններով՝ -Pi/2-ից Pi/2 միջակայքում", "ad": "ձեր ցանկացած անկյան շոշափողն է"}, "ATAN2": {"a": "(x_num; y_num)", "d": "Վերադարձնում է նշված x և y կոորդինատների արկտանգենսը, ռադիաններով -Pi-ի և Pi-ի միջև, բացառելով -Pi-ն:", "ad": "կետի x կոորդինատն է! կետի y կոորդինատն է"}, "ATANH": {"a": "(number)", "d": "վերադարձնում է թվի հակադարձ հիպերբոլիկ շոշափողը", "ad": "ցանկացած իրական թիվ է -1-ի և 1-ի միջև՝ բացառելով -1-ը և 1-ը"}, "BASE": {"a": "(number; radix; [min_length])", "d": "Թիվը վերածում է տեքստային ներկայացման՝ տրված արմատով (հիմք)", "ad": "այն թիվն է, որը դուք ցանկանում եք փոխարկել!այն բազային Radix-ն է, որին ցանկանում եք փոխարկել թիվը!վերադարձված տողի նվազագույն երկարությունն է: Եթե բաց թողնված սկզբնական զրոները չեն ավելացվում"}, "CEILING": {"a": "(number; significance)", "d": "Կլորացնում է թիվը դեպի վեր, մինչև նշանակության մոտակա բազմապատիկը", "ad": "այն արժեքն է, որը ցանկանում եք կլորացնել!այն բազմապատիկը, որին ցանկանում եք կլորացնել"}, "CEILING.MATH": {"a": "(number; [significance]; [mode])", "d": "Կլորացնում է թիվը մինչև մոտակա ամբողջ թիվը կամ նշանակության մոտակա բազմապատիկը", "ad": "այն արժեքն է, որը ցանկանում եք կլորացնել!այն բազմապատիկն է, որի վրա ցանկանում եք կլորացնել!երբ տրված է և ոչ զրոյական, այս ֆունկցիան կկլորացվի զրոյից"}, "CEILING.PRECISE": {"a": "(x; [significance])", "d": "Վերադարձնում է մի թիվը, որը կլորացված է մինչև մոտակա ամբողջ թիվը կամ նշանակության մոտակա բազմապատիկը", "ad": "այն արժեքն է, որը ցանկանում եք կլորացնել!այն բազմապատիկը, որին ցանկանում եք կլորացնել"}, "COMBIN": {"a": "(number; number_chosen)", "d": "Վերադարձնում է միավորների քանակը տվյալ քանակի տարրերի համար", "ad": "տարրերի ընդհանուր թիվն է։յուրաքանչյուր համակցության տարրերի քանակն է"}, "COMBINA": {"a": "(number; number_chosen)", "d": "Վերադարձնում է տվյալ քանակի տարրերի համար կրկնվող համակցությունների քանակը", "ad": "ապրանքների ընդհանուր թիվն է!յուրաքանչյուր համակցության տարրերի քանակն է"}, "COS": {"a": "(number)", "d": "Վերադարձնում է անկյան կոսինուսը", "ad": "այն անկյունն է ռադիաններով, որի համար ցանկանում եք կոսինուսը"}, "COSH": {"a": "(number)", "d": "Վերադարձնում է թվի հիպերբոլիկ կոսինուսը", "ad": "ցանկացած իրական թիվ է"}, "COT": {"a": "(number)", "d": "Վերադարձնում է անկյան կոտանգենսը", "ad": "այն անկյունն է ռադիաններով, որի համար ցանկանում եք կոտանգենսը"}, "COTH": {"a": "(number)", "d": "Վերադարձնում է թվի հիպերբոլիկ կոտանգենսը", "ad": "այն անկյունն է ռադիաններով, որի համար ցանկանում եք հիպերբոլիկ կոտանգենսը"}, "CSC": {"a": "(number)", "d": "Վերադարձնում է անկյան կոսեկանտը", "ad": "այն անկյունն է ռադիաններով, որի համար ցանկանում եք կոսեկանտը"}, "CSCH": {"a": "(number)", "d": "Վերադարձնում է անկյան հիպերբոլիկ կոսեկանտը", "ad": "այն անկյունն է ռադիաններով, որի համար ցանկանում եք հիպերբոլիկ կոսեկանտը"}, "DECIMAL": {"a": "(number; radix)", "d": "Տրված հիմքում թվի տեքստային ներկայացումը փոխակերպում է տասնորդական թվի", "ad": "այն թիվն է, որը դուք ցանկանում եք փոխարկել!այն թվի բազային Radix-ն է, որը դուք փոխակերպում եք"}, "DEGREES": {"a": "(angle)", "d": "Փոխակերպում է ռադիանները աստիճանների", "ad": "այն անկյունն է ռադիաններով, որը ցանկանում եք փոխարկել"}, "ECMA.CEILING": {"a": "(x; significance)", "d": "Կլորացնում է թիվը մինչև նշանակության մոտակա բազմապատիկը", "ad": "այն արժեքն է, որը ցանկանում եք կլորացնել!այն բազմապատիկը, որին ցանկանում եք կլորացնել"}, "EVEN": {"a": "(number)", "d": "Դրական թիվը կլորացնում է վերև, իսկ բացասական թիվը՝ մինչև մոտակա զույգ ամբողջ թիվը", "ad": "կլորացման արժեքն է"}, "EXP": {"a": "(number)", "d": "Վերադարձնում է e-ը բարձրացված՝ տրված թվի ուժին", "ad": "e հիմքի վրա կիրառվող ցուցիչն է։ e հաստատունը հավասար է 2.71828182845904, բնական լոգարիթմի հիմքի"}, "FACT": {"a": "(number)", "d": "Վերադարձնում է թվի գործակիցը, որը հավասար է 1*2*3*...* թվի", "ad": "այն ոչ բացասական թիվն է, որի ֆակտորիալը ցանկանում եք"}, "FACTDOUBLE": {"a": "(number)", "d": "Վերադարձնում է թվի կրկնակի գործակիցը", "ad": "այն արժեքն է, որի համար պետք է վերադարձնել կրկնակի ֆակտորիալը"}, "FLOOR": {"a": "(number; significance)", "d": "Կլորացնում է թիվը մինչև նշանակության մոտակա բազմապատիկը", "ad": "այն թվային արժեքն է, որը ցանկանում եք կլորացնել!այն բազմապատիկը, որի վրա ցանկանում եք կլորացնել: Համարը և նշանակությունը պետք է կամ դրական լինեն, կամ երկուսն էլ բացասական"}, "FLOOR.PRECISE": {"a": "(x; [significance])", "d": "Վերադարձնում է մի թիվը, որը կլորացված է դեպի ներքև մինչև մոտակա ամբողջ թիվը կամ մինչև նշանակության մոտակա բազմապատիկը", "ad": "այն արժեքն է, որը ցանկանում եք կլորացնել!այն բազմապատիկը, որին ցանկանում եք կլորացնել"}, "FLOOR.MATH": {"a": "(number; [significance]; [mode])", "d": "Կլորացնում է թիվը դեպի ներքև՝ մինչև մոտակա ամբողջ թիվը կամ մինչև նշանակության մոտակա բազմապատիկը", "ad": "այն արժեքն է, որը ցանկանում եք կլորացնել!այն բազմապատիկն է, որի վրա ցանկանում եք կլորացնել!երբ տրված է և ոչ զրոյական, այս ֆունկցիան կկլորացվի զրոյի ուղղությամբ"}, "GCD": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է ամենամեծ ընդհանուր բաժանարարը", "ad": "1-ից 255 արժեքներն են"}, "INT": {"a": "(number)", "d": "Կլորացնում է թիվը մինչև մոտակա ամբողջ թիվը", "ad": "այն իրական թիվն է, որը ցանկանում եք կլորացնել մինչև ամբողջ թիվ"}, "ISO.CEILING": {"a": "(number; [significance])", "d": "Վերադարձնում է այն թիվը, որը կլորացվում է մինչև մոտակա ամբողջ թիվը կամ նշանակության մոտակա բազմապատիկը, անկախ թվի նշանից: Այնուամենայնիվ, եթե թիվը կամ նշանակությունը զրո է, զրո է վերադարձվում.", "ad": "այն արժեքն է, որը ցանկանում եք կլորացնել!այն բազմապատիկը, որին ցանկանում եք կլորացնել"}, "LCM": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է ամենափոքր ընդհանուր բազմապատիկը", "ad": "1-ից 255 արժեքներ են, որոնց համար ցանկանում եք նվազագույն ընդհանուր բազմապատիկը"}, "LN": {"a": "(number)", "d": "Վերադարձնում է թվի բնական լոգարիթմը", "ad": "այն դրական իրական թիվն է, որի համար ցանկանում եք բնական լոգարիթմը"}, "LOG": {"a": "(number; [base])", "d": "Վերադարձնում է թվի լոգարիթմը ձեր նշած հիմքին", "ad": "այն դրական իրական թիվն է, որի համար ցանկանում եք լոգարիթմը!լոգարիթմի հիմքն է. 10, եթե բաց թողնվի"}, "LOG10": {"a": "(number)", "d": "Վերադարձնում է թվի 10 հիմքի լոգարիթմը", "ad": "այն դրական իրական թիվն է, որի համար ցանկանում եք բազային-10 լոգարիթմը"}, "MDETERM": {"a": "(array)", "d": "Վերադարձնում է զանգվածի մատրիցային որոշիչը", "ad": "թվային զանգված է՝ հավասար թվով տողերով և սյունակներով՝ կա՛մ վանդակների տիրույթ, կա՛մ զանգվածի հաստատուն"}, "MINVERSE": {"a": "(array)", "d": "Վերադարձնում է հակադարձ մատրիցը զանգվածում պահվող մատրիցի համար", "ad": "թվային զանգված է՝ հավասար թվով տողերով և սյունակներով՝ կա՛մ վանդակների տիրույթ, կա՛մ զանգվածի հաստատուն"}, "MMULT": {"a": "(array1; array2)", "d": "Վերադարձնում է երկու զանգվածների մատրիցային արտադրյալը՝ զանգվածի նույն թվով տողեր, ինչ զանգված 1 և սյունակներ, ինչպես զանգված 2։", "ad": "թվերի առաջին զանգվածն է, որը պետք է բազմապատկվի և պետք է ունենա նույն թվով սյունակներ, քանի որ Array2-ն ունի տողեր"}, "MOD": {"a": "(number; divisor)", "d": "Վերադարձնում է մնացորդը այն բանից հետո, երբ թիվը բաժանվում է բաժանարարով", "ad": "այն թիվն է, որի համար ցանկանում եք գտնել մնացորդը բաժանումը կատարելուց հետո!այն թիվն է, որով ցանկանում եք բաժանել Number-ը"}, "MROUND": {"a": "(number; multiple)", "d": "Վերադարձնում է ցանկալի բազմակի կլորացված թիվը", "ad": "կլորացման արժեքն է!այն բազմապատիկը, որի վրա ցանկանում եք կլորացնել թիվը"}, "MULTINOMIAL": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է թվերի բազմության բազմանդամը", "ad": "1-ից 255 արժեքներ են, որոնց համար ցանկանում եք բազմանդամը"}, "MUNIT": {"a": "(dimension)", "d": "Վերադարձնում է միավորի մատրիցը նշված չափման համար", "ad": "ամբողջ թիվ է, որը նշում է միավորի մատրիցայի չափը, որը ցանկանում եք վերադարձնել"}, "ODD": {"a": "(number)", "d": "Դրական թիվը կլորացնում է վերև, իսկ բացասական թիվը մինչև մոտակա կենտ ամբողջ թիվը", "ad": "կլորացման արժեքն է"}, "PI": {"a": "()", "d": "Վերադարձնում է Pi-ի արժեքը՝ 3.14159265358979, ճշգրիտ մինչև 15 նիշ", "ad": ""}, "POWER": {"a": "(number; power)", "d": "Վերադարձնում է հզորության բարձրացված թվի արդյունքը", "ad": "բազային թիվն է, ցանկացած իրական թիվ!այն ցուցանիշ է, որի վրա բարձրացվում է թվի հիմքը"}, "PRODUCT": {"a": "(number1; [number2]; ...)", "d": "Բազմապատկում է որպես արգումենտ տրված բոլոր թվերը", "ad": "1-ից 255 թվեր, տրամաբանական արժեքներ կամ թվերի տեքստային ներկայացումներ են, որոնք ցանկանում եք բազմապատկել"}, "QUOTIENT": {"a": "(numerator; denominator)", "d": "Վերադարձնում է բաժանման ամբողջ թվային մասը", "ad": "դա դիվիդենտն է!բաժանարարն է"}, "RADIANS": {"a": "(angle)", "d": "Փոխակերպում է աստիճանները ռադիանի", "ad": "աստիճաններով անկյուն է, որը ցանկանում եք փոխարկել"}, "RAND": {"a": "()", "d": "Վերադարձնում է 0-ից մեծ կամ հավասար և 1-ից փոքր պատահական թիվը՝ հավասարաչափ բաշխված (վերահաշվարկի փոփոխություններ)", "ad": ""}, "RANDARRAY": {"a": "([rows]; [columns]; [min]; [max]; [integer])", "d": "Վերադարձնում է պատահական թվերի զանգված", "ad": "վերադարձված զանգվածի տողերի քանակը!վերադարձված զանգվածի սյունակների քանակը!նվազագույն թիվը, որը դուք կցանկանայիք վերադարձնել!առավելագույն թիվը, որը դուք կցանկանայիք վերադարձնել, վերադարձրեք ամբողջ թիվ կամ տասնորդական արժեք: TRUE ամբողջ թվի համար, FALSE տասնորդական թվի համար"}, "RANDBETWEEN": {"a": "(bottom; top)", "d": "Վերադարձնում է պատահական թիվ ձեր նշած թվերի միջև", "ad": "ամենափոքր ամբողջ թիվն է RANDBETWEEN-ը կվերադառնա!ամենամեծ ամբողջ թիվն է RANDBETWEEN-ը կվերադառնա"}, "ROMAN": {"a": "(number; [form])", "d": "Արաբական թիվը վերածում է հռոմեականի՝ որպես տեքստ", "ad": "այն Արաբական թիվն է, որը ցանկանում եք փոխարկել!այն համարն է, որը նշում է ձեր ցանկացած Հռոմեական թվի տեսակը:"}, "ROUND": {"a": "(number; num_digits)", "d": "Կլորացնում է թիվը մինչև նշված թվանշանները", "ad": "այն թիվն է, որը ցանկանում եք կլորացնել!այն թվանշանների թիվն է, որով ցանկանում եք կլորացնել: Բացասական շրջաններ տասնորդական կետի ձախ կողմում; զրո մոտակա ամբողջ թվին"}, "ROUNDDOWN": {"a": "(number; num_digits)", "d": "Կլորացնում է թիվը դեպի ներքև՝ դեպի զրոյ", "ad": "ցանկացած իրական թիվ, որը ցանկանում եք, կլորացվի ներքև!սա այն թվանշանների թիվն է, որով ցանկանում եք կլորացնել: Բացասական շրջաններ տասնորդական կետի ձախ կողմում; զրո կամ բաց թողնված, մոտակա ամբողջ թվով"}, "ROUNDUP": {"a": "(number; num_digits)", "d": "Կլորացնում է թիվը վերև՝ զրոյից հեռու", "ad": "ցանկացած իրական թիվ, որը ցանկանում եք, կլորացվի դեպի վեր!սա այն թվանշանների թիվն է, որով ցանկանում եք կլորացնել: Բացասական շրջաններ տասնորդական կետի ձախ կողմում; զրո կամ բաց թողնված, մոտակա ամբողջ թվով"}, "SEC": {"a": "(number)", "d": "Վերադարձնում է անկյան հատվածը", "ad": "այն անկյունն է ռադիաններով, որի համար ցանկանում եք սեկանտը"}, "SECH": {"a": "(number)", "d": "Վերադարձնում է անկյան հիպերբոլիկ հատվածը", "ad": "այն անկյունն է ռադիաններով, որի համար ցանկանում եք հիպերբոլիկ սեկանտը"}, "SERIESSUM": {"a": "(x; n; m; coefficients)", "d": "Վերադարձնում է բանաձևի հիման վրա հզորության շարքի գումարը", "ad": "հզորության շարքի մուտքային արժեքն է!սկզբնական հզորությունն է, որին ցանկանում եք բարձրացնել x-ը, քայլն է, որով մեծացնել n-ը շարքի յուրաքանչյուր անդամի համար!գործակիցների մի շարք է, որով x-ի յուրաքանչյուր հաջորդական հզորությունը բազմապատկվում է"}, "SIGN": {"a": "(number)", "d": "Վերադարձնում է թվի նշանը՝ 1, եթե թիվը դրական է, զրո, եթե թիվը զրո է, կամ -1, եթե թիվը բացասական է։", "ad": "ցանկացած իրական թիվ է"}, "SIN": {"a": "(number)", "d": "Վերադարձնում է անկյան սինուսը", "ad": "այն անկյունն է ռադիաններով, որի համար ցանկանում եք սինուսը: Աստիճաններ * PI()/180 = ռադիաններ"}, "SINH": {"a": "(number)", "d": "Վերադարձնում է թվի հիպերբոլիկ սինուսը", "ad": "ցանկացած իրական թիվ է"}, "SQRT": {"a": "(number)", "d": "Վերադարձնում է թվի քառակուսի արմատը", "ad": "այն թիվն է, որի համար ցանկանում եք քառակուսի արմատ"}, "SQRTPI": {"a": "(number)", "d": "Վերադարձնում է քառակուսի արմատը (թիվ * Pi)", "ad": "այն թիվն է, որով բազմապատկվում է p"}, "SUBTOTAL": {"a": "(function_num; ref1; ...)", "d": "Վերադարձնում է ենթագումարը ցուցակում կամ տվյալների բազայում", "ad": "1-ից 11 թիվն է, որը սահմանում է ենթագումարի ամփոփման ֆունկցիան!1-ից 254 միջակայքներ կամ հղումներ են, որոնց համար ցանկանում եք ենթագումարը"}, "SUM": {"a": "(number1; [number2]; ...)", "d": "Ավելացնում է բոլոր թվերը մի շարք բջիջներում", "ad": "գումարելու համար 1-ից 255 թվեր են: Տրամաբանական արժեքները և տեքստը անտեսվում են վանդակներում և ներառվում են որպես փաստարկներ մուտքագրելու դեպքում"}, "SUMIF": {"a": "(range; criteria; [sum_range])", "d": "Ավելացնում է տվյալ պայմանով կամ չափանիշներով սահմանված բջիջները", "ad": "այն վանդակների տիրույթն է, որը ցանկանում եք գնահատել!այն պայմանն է կամ չափանիշը թվի, արտահայտության կամ տեքստի տեսքով, որը սահմանում է, թե որ վանդակներն են ավելացվելու!իրական վանդակներն են, որոնք պետք է գումարվեն: Բաց թողնելու դեպքում օգտագործվում են միջակայքի վանդակները"}, "SUMIFS": {"a": "(sum_range; criteria_range; criteria; ...)", "d": "Ավելացնում է բջիջները, որոնք նշված են որոշակի պայմանների կամ չափանիշների համաձայն", "ad": "փաստացի վանդակներն են, որոնք պետք է sum.!:այն վանդակների տիրույթն է, որը ցանկանում եք գնահատել տվյալ պայմանի համար!պայման կամ չափանիշ է թվի, արտահայտության կամ տեքստի տեսքով, որը սահմանում է, կամ որ վանդակները կավելացվեն"}, "SUMPRODUCT": {"a": "(array1; [array2]; [array3]; ...)", "d": "Վերադարձնում է համապատասխան տիրույթների կամ զանգվածների արտադրյալների գումարը", "ad": "2-ից 255 զանգված են, որոնց համար ցանկանում եք բազմապատկել, ապա ավելացնել բաղադրիչներ: Բոլոր զանգվածները պետք է ունենան նույն չափերը"}, "SUMSQ": {"a": "(number1; [number2]; ...)", "d": "Վերադարձնում է արգումենտների քառակուսիների գումարը: Փաստարկները կարող են լինել թվեր, զանգվածներ, անուններ կամ թվեր պարունակող բջիջների հղումներ", "ad": "1-ից 255 թվեր, զանգվածներ, անվանումներ կամ հղումներ են այն զանգվածներին, որոնց համար ցանկանում եք քառակուսիների գումարը"}, "SUMX2MY2": {"a": "(array_x; array_y)", "d": "Գումարում է երկու համապատասխան միջակայքերի կամ զանգվածների քառակուսիների տարբերությունները", "ad": "թվերի առաջին տիրույթն է կամ զանգվածը և կարող է լինել թիվ կամ անվանում, զանգված կամ հղում, որը պարունակում է թվեր!թվերի երկրորդ տիրույթն է կամ զանգվածը և կարող է լինել թվեր կամ անվանում, զանգված կամ հղում, որը պարունակում է թվեր"}, "SUMX2PY2": {"a": "(array_x; array_y)", "d": "Վերադարձնում է երկու համապատասխան միջակայքերի կամ զանգվածների թվերի քառակուսիների գումարների գումարը", "ad": "թվերի առաջին տիրույթն է կամ զանգվածը և կարող է լինել թիվ կամ անվանում, զանգված կամ հղում, որը պարունակում է թվեր!թվերի երկրորդ տիրույթն է կամ զանգվածը և կարող է լինել թվեր կամ անվանում, զանգված կամ հղում, որը պարունակում է թվեր"}, "SUMXMY2": {"a": "(array_x; array_y)", "d": "Գումարում է տարբերությունների քառակուսիները երկու համապատասխան տիրույթներում կամ զանգվածներում", "ad": "Արժեքների առաջին միջակայքն է կամ զանգվածը և կարող է լինել թվեր կամ անվանում, զանգված կամ հղում, որը պարունակում է թվեր!երկրորդ միջակայքն է կամ արժեքների զանգվածը և կարող է լինել թվեր կամ անվանում, զանգված կամ հղում, որը պարունակում է թվեր"}, "TAN": {"a": "(number)", "d": "Վերադարձնում է անկյան շոշափողը", "ad": "այն անկյունն է ռադիաններով, որի համար ցանկանում եք շոշափող: Աստիճաններ * PI()/180 = ռադիաններ"}, "TANH": {"a": "(number)", "d": "Վերադարձնում է թվի հիպերբոլիկ շոշափողը", "ad": "ցանկացած իրական թիվ է"}, "TRUNC": {"a": "(number; [num_digits])", "d": "Թիվը կտրում է ամբողջ թվի՝ հեռացնելով թվի տասնորդական կամ կոտորակային մասը", "ad": "այն թիվն է, որը ցանկանում եք կրճատել!դա մի թիվ է, որը նշում է կրճատման ճշգրտությունը, 0 (զրո), եթե բաց թողնվի"}, "ADDRESS": {"a": "(row_num; column_num; [abs_num]; [a1]; [sheet_text])", "d": "Ստեղծում է բջիջի հղում՝ որպես տեքստ՝ հաշվի առնելով տողերի և սյունակների նշված համարները", "ad": "տողի համարն է, որը պետք է օգտագործվի վանդակային հղումում. Row_number = 1 1-ին տողի համար․ սյունակի համարն է, որը պետք է օգտագործվի վանդակային հղումում: Օրինակ, Column_number = 4 D! սյունակի համար նշում է հղման տեսակը՝ բացարձակ = 1; բացարձակ տող/հարաբերական սյունակ = 2; հարաբերական տող / բացարձակ սյունակ = 3; հարաբերական = 4․ տրամաբանական արժեք է, որը սահմանում է հղման ոճը. A1 ոճ = 1 կամ TRUE; R1C1 ոճ = 0 կամ FALSE-ը!տեքստ է, որը նշում է աշխատաթերթի անվանումը, որը պետք է օգտագործվի որպես արտաքին հղում"}, "CHOOSE": {"a": "(index_num; value1; [value2]; ...)", "d": "Ընտրում է արժեք կամ գործողություն, որը պետք է կատարվի արժեքների ցանկից՝ հիմնվելով ինդեքսի համարի վրա", "ad": "նշում է, թե որ արժեքի փաստարկն է ընտրված: Index_num-ը պետք է լինի 1-ից 254-ի միջև, կամ բանաձևը կամ հղումը 1-ից 254-ի միջև ընկած թվին: 1-ից 254 թվեր են, վանդակների հղումներ, սահմանված անուններ, բանաձևեր, գործառույթներ կամ տեքստային արգումենտներ, որոնցից ընտրում է CHOOSE-ը"}, "COLUMN": {"a": "([reference])", "d": "Վերադարձնում է հղումի սյունակի համարը", "ad": "այն վանդակն է կամ հարակից վանդակների տիրույթը, որի համար ցանկանում եք սյունակի համարը: Բաց թողնելու դեպքում օգտագործվում է COLUMN ֆունկցիան պարունակող վանդակը"}, "COLUMNS": {"a": "(array)", "d": "Վերադարձնում է զանգվածի կամ հղումի սյունակների քանակը", "ad": "զանգված կամ զանգվածի բանաձև է, կամ հղում վանդակների մի շարք, որոնց համար ցանկանում եք սյունակների քանակը"}, "FORMULATEXT": {"a": "(reference)", "d": "Վերադարձնում է բանաձևը որպես տող", "ad": "Բանաձևի հղումն է"}, "HLOOKUP": {"a": "(lookup_value; table_array; row_index_num; [range_lookup])", "d": "Աղյուսակի կամ արժեքների զանգվածի վերին տողում արժեք է փնտրում և ձեր նշած տողից վերադարձնում է արժեքը նույն սյունակում", "ad": "այն աժեքն է, որը կարելի է գտնել աղյուսակի առաջին շարքում և կարող է լինել արժեք, հղում կամ տեքստային տող!տեքստի, թվերի կամ տրամաբանական արժեքների աղյուսակ է, որում որոնվում են տվյալները: Table_array-ը կարող է հղում լինել տիրույթին կամ տիրույթի անվանմանը!այն տողի համարն է table_array-ում, որտեղից պետք է վերադարձվի համապատասխան արժեքը: Աղյուսակի արժեքների առաջին շարքը տող 1-ն է!Տրամաբանական արժեք է. վերևի շարքում ամենամոտ համընկնումը գտնելու համար (տեսակավորված աճման կարգով) = TRUE կամ բաց թողնված; գտնել ճշգրիտ համընկնում = FALSE"}, "HYPERLINK": {"a": "(link_location; [friendly_name])", "d": "Ստեղծում է դյուրանցում կամ թռիչք, որը բացում է փաստաթուղթ, որը պահվում է ձեր կոշտ սկավառակի վրա, ցանցային սերվերում կամ ինտերնետում", "ad": "Այն տեքստն է, որը տալիս է բացվող փաստաթղթի ուղին և ֆայլի անվանումը, կոշտ սկավառակի գտնվելու վայրը, UNC հասցեն կամ URL-ի ուղին: տեքստ է կամ մի թիվ, որը ցուցադրվում է վանդակում: Եթե բաց թողնված է, վանդակը ցուցադրում է Link_location տեքստը"}, "INDEX": {"a": "(array; row_num; [column_num]!reference; row_num; [column_num]; [area_num])", "d": "Վերադարձնում է բջիջի արժեքը կամ հղումը որոշակի տողի և սյունակի հատման կետում՝ տվյալ տիրույթում", "ad": "վանդակների տիրույթ է կամ հաստատուն զանգվածի:! ընտրում է Array-ի կամ Reference-ի այն տողը, որտեղից պետք է վերադարձվի արժեքը: Եթե բաց թողնվի, Column_num-ը պահանջվում է! ընտրում է զանգվածի կամ հղումի սյունակը, որտեղից պետք է վերադարձվի արժեքը: Եթե բաց թողնված է, Row_num-ը պահանջվում է! հղում է մեկ կամ մի քանի վանդակների տիրույթներին: ընտրում է զանգվածի կամ հղումի տողը, որտեղից պետք է վերադարձվի արժեքը: Եթե բաց թողնվի, Column_num-ը պահանջվում է! ընտրում է զանգվածի կամ հղումի սյունակը, որտեղից պետք է վերադարձվի արժեքը: Եթե բաց թողնված է, Row_num-ը պահանջվում է: Տեղեկանքում ընտրում է մի տիրույթ, որտեղից պետք է վերադարձնի արժեքը: Առաջին ընտրված կամ մուտքագրված տարածքը 1 տարածքն է, երկրորդը՝ 2 տարածքը և այլն"}, "INDIRECT": {"a": "(ref_text; [a1])", "d": "Վերադարձնում է տեքստային տողով նշված հղումը", "ad": "հղում է վանդակին, որը պարունակում է A1- կամ R1C1 ոճի հղում, անվանեւմ, որը սահմանված է որպես հղում, կամ հղում վանդակին որպես տեքստային տող!տրամաբանական արժեք է, որը սահմանում է Ref_text-ում հղման տեսակը՝ R1C1-: ոճ = FALSE; A1 ոճ = TRUE կամ բաց թողնված"}, "LOOKUP": {"a": "(lookup_value; lookup_vector; [result_vector]!lookup_value; array)", "d": "Որոնում է արժեքը կամ մեկ տողով կամ մեկ սյունակով կամ զանգվածից: Ապահովված է հետընթաց համատեղելիության համար", "ad": "այն արժեքն է, որը փնտրում է LOOKUP-ը Lookup_vector-ում և կարող է լինել թիվ, տեքստ, տրամաբանական արժեք, կամ արժեքի անվանում կամ հղում!որը պարունակում է միայն մեկ տող կամ մեկ սյունակ տեքստի, թվերի կամ տրամաբանական արժեքների, տեղադրված է աճման կարգով!ընդգրկույթ է, որը պարունակում է միայն մեկ տող կամ սյունակ, նույն չափը, ինչ Lookup_vector-ը: արժեք է, որը փնտրում է LOOKUP-ը Array-ում և կարող է լինել թիվ, տեքստ, տրամաբանական արժեք կամ արժեքի անվանում կամ հղում!վանդակների մի շարք է, որը պարունակում է տեքստ, թիվ կամ տրամաբանական արժեքներ, որոնք դուք ցանկանում եք համեմատել Lookup_value-ի հետ"}, "MATCH": {"a": "(lookup_value; lookup_array; [match_type])", "d": "Վերադարձնում է նյութի հարաբերական դիրքը զանգվածում, որը համապատասխանում է նշված արժեքին սահմանված կարգով", "ad": "այն արժեքն է, որը դուք օգտագործում եք զանգվածում ձեր նախնտրելի արժեքը գտնելու համար, թվեր, տեքստեր կամ տրամաբանական արժեքներ կամ դրանցից մեկի հղումը!այն վանդակների հարակից տիրույթ է, որը պարունակում է հնարավոր որոնման արժեքներ, արժեքների զանգված կամ զանգվածին հղումը!1, 0 կամ -1 թիվ է, որը ցույց է տալիս, թե որ արժեքը պետք է վերադարձվի:"}, "OFFSET": {"a": "(reference; rows; cols; [height]; [width])", "d": "Վերադարձնում է հղում մի տիրույթի, որը տվյալ հղումից տողերի և սյունակների որոշակի քանակ է", "ad": "այն հղումն է, որից ցանկանում եք հիմնել օֆսեթը, հղումը վանդակին կամ հարակից վանդակների տիրույթին!այն տողերի թիվն է, վերև կամ վար, որին ցանկանում եք հղում կատարել արդյունքի վերին ձախ վանդակը!ձախ կամ աջ սյունակների թիվը, որին ցանկանում եք հղում կատարել արդյունքի վերին ձախ վանդակը!այն բարձրությունն է՝ ըստ տողերի, որը դուք ցանկանում եք, որ լինի արդյունքը, նույն բարձրությունը, ինչ Reference-ը, եթե բաց թողնվի!այն լայնությունն է, սյունակների քանակով, որը դուք ցանկանում եք, որ լինի նույն լայնությունը, ինչ Reference-ը, եթե բաց թողնվի"}, "ROW": {"a": "([reference])", "d": "Վերադարձնում է հղումի տողի համարը", "ad": "այն վանդակն է կամ վանդակների մեկ տիրույթ, որի համար ցանկանում եք տողի համարը լինի. եթե բաց թողնվի, վերադարձնում է ROW ֆունկցիան պարունակող վանդակը"}, "ROWS": {"a": "(array)", "d": "Վերադարձնում է հղման կամ զանգվածի տողերի քանակը", "ad": "զանգված է, զանգվածի բանաձև կամ հղում վանդակների մի շարք, որոնց համար ցանկանում եք տողերի քանակը"}, "TRANSPOSE": {"a": "(array)", "d": "Փոխակերպում է բջիջների ուղղահայաց տիրույթը հորիզոնական տիրույթի կամ հակառակը", "ad": "աշխատանքային թերթիկի վանդակների տիրույթ է կամ արժեքների զանգված, որը ցանկանում եք փոխադրել"}, "UNIQUE": {"a": "(array; [by_col]; [exactly_once])", "d": "Վերադարձնում է եզակի արժեքները տիրույթից կամ զանգվածից.", "ad": "տիրույթը կամ զանգվածը, որից վերադարձվում են եզակի տողեր կամ սյունակներ!տրամաբանական արժեք է. համեմատել տողերը միմյանց հետ և վերադարձնել եզակի տողերը = FALSE կամ բաց թողնված; համեմատեք սյունակները միմյանց դեմ և վերադարձրեք եզակի սյունակները = TRUE-ն! տրամաբանական արժեք է.վերադարձնել տողեր կամ սյունակներ, որոնք առաջանում են ուղիղ մեկ անգամ զանգվածից = TRUE; վերադարձնել բոլոր տարբեր տողերը կամ սյունակները զանգվածից = FALSE կամ բաց թողնված"}, "VLOOKUP": {"a": "(lookup_value; table_array; col_index_num; [range_lookup])", "d": "Աղյուսակի ամենաձախ սյունակում որոնում է արժեք և այնուհետև վերադարձնում է ձեր նշած սյունակից նույն տողում գտնվող արժեքը: Լռելյայնորեն աղյուսակը պետք է դասավորված լինի աճման կարգով", "ad": "այն արժեքն է, որը կարելի է գտնել աղյուսակի առաջին սյունակում և կարող է լինել արժեք, հղում կամ տեքստային տող! տեքստի, թվերի կամ տրամաբանական արժեքների աղյուսակ, որում վերցվում են տվյալները: Table_array-ը կարող է հղում լինել տիրույթին կամ տիրույթի անվանմանը!այն սյունակի համարն է table_array-ում, որտեղից պետք է վերադարձվի համապատասխան արժեքը: Աղյուսակի արժեքների առաջին սյունակը սյունակ 1-ն է!տրամաբանական արժեք է. գտնել ամենամոտիկ համընկնումն առաջին սյունակում (տեսակավորված աճման կարգով) = TRUE կամ բաց թողնված; գտնել ճշգրիտ համընկնում = FALSE"}, "XLOOKUP": {"a": "(lookup_value; lookup_array; return_array; [if_not_found]; [match_mode]; [search_mode])", "d": "Որոնում է համընկնման ընդգրկույթ կամ զանգված և վերադարձնում է համապատասխան տարրը երկրորդ տիրույթից կամ զանգվածից: Լռելյայնորեն օգտագործվում է ճշգրիտ համընկնում", "ad": "այն արժեքն է, որը պետք է որոնվի!որոնվող զանգվածն է կամ տիրույթը! այն զանգվածն է կամ տիրույթը, որը պետք է վերադարձվի: Վերադարձվում է, եթե համընկնում չի գտնվել!նշեք, թե ինչպես համընկնել lookup_value-ի արժեքներին lookup_array-ում!նշեք որոնման ռեժիմը, որն օգտագործվում է: Լռելյայնորեն կօգտագործվի առաջինից վերջին որոնումը"}, "CELL": {"a": "(info_type; [reference])", "d": "Վերադարձնում է տեղեկատվություն բջիջի ձևաչափման, գտնվելու վայրի կամ բովանդակության մասին", "ad": "տեքստային արժեք է, որը սահմանում է, թե ինչ տեսակի վանդակային տեղեկատվություն եք ցանկանում վերադարձնել!վանդակը, որի մասին տեղեկատվություն եք ցանկանում"}, "ERROR.TYPE": {"a": "(error_val)", "d": "Վերադարձնում է սխալի արժեքին համապատասխանող թիվ.", "ad": "այն սխալի արժեքն է, որի համար ցանկանում եք նույնականացման համարը, և կարող է լինել իրական սխալի արժեք կամ հղում սխալի արժեք պարունակող վանդակին"}, "ISBLANK": {"a": "(value)", "d": "Ստուգում է՝ արդյոք հղումը դատարկ բջիջ է, և վերադարձնում է ՃՇՄԱՐԻՏ կամ ԿԵՂԾ", "ad": "այն վանդակն է կամ անվանումը, որը վերաբերում է այն վանդակին, որը ցանկանում եք փորձարկել"}, "ISERR": {"a": "(value)", "d": "Ստուգում է՝ արդյոք արժեքը այլ սխալ է, քան #N/A, և վերադարձնում է ՃՇՄԱՐԻՏ կամ ԿԵՂԾ", "ad": "այն արժեքն է, որը ցանկանում եք ստուգել: Արժեքը կարող է վերաբերել վանդակին, բանաձևին կամ անվանմանը, որը վերաբերում է վանդակին, բանաձևին կամ արժեքին"}, "ISERROR": {"a": "(value)", "d": "Ստուգում է արդյոք արժեքը սխալ է, և վերադարձնում է ՃՇՄԱՐԻՏ կամ ԿԵՂԾ", "ad": "այն արժեքն է, որը ցանկանում եք ստուգել: Արժեքը կարող է վերաբերել վանդակին, բանաձևին կամ անվանմանը, որը վերաբերում է վանդակին, բանաձևին կամ արժեքին"}, "ISEVEN": {"a": "(number)", "d": "Վերադարձնում է ՃՇՄԱՐԻՏ, եթե թիվը զույգ է", "ad": "փորձարկման արժեքն է"}, "ISFORMULA": {"a": "(reference)", "d": "Ստուգում է՝ արդյոք հղումը բանաձև պարունակող բջիջին է, և վերադարձնում է ՃՇՄԱՐԻՏ կամ ԿԵՂԾ", "ad": "հղում է այն վանդակին, որը ցանկանում եք փորձարկել: Հղումը կարող է լինել վանդակային հղում, բանաձև կամ անուն, որը վերաբերում է վանդակին "}, "ISLOGICAL": {"a": "(value)", "d": "Ստուգում է՝ արդյոք արժեքը տրամաբանական արժեք է (ՃՇՄԱՐԻՏ կամ ԿԵՂԾ), և վերադարձնում է ՃՇՄԱՐԻՏ կամ ԿԵՂԾ", "ad": "այն արժեքն է, որը ցանկանում եք ստուգել: Արժեքը կարող է վերաբերել վանդակին, բանաձևին կամ անվանմանը, որը վերաբերում է վանդակին, բանաձևին կամ արժեքին"}, "ISNA": {"a": "(value)", "d": "Ստուգում է՝ արդյոք արժեքը #N/A է, և վերադարձնում է ՃՇՄԱՐԻՏ կամ ԿԵՂԾ", "ad": "այն արժեքն է, որը ցանկանում եք ստուգել: Արժեքը կարող է վերաբերել վանդակին, բանաձևին կամ անվանմանը, որը վերաբերում է վանդակին, բանաձևին կամ արժեքին"}, "ISNONTEXT": {"a": "(value)", "d": "Ստուգում է՝ արդյոք արժեքը տեքստ չէ (դատարկ բջիջները տեքստ չեն), և վերադարձնում է ՃՇՄԱՐԻՏ կամ ԿԵՂԾ", "ad": "այն արժեքն է, որը ցանկանում եք ստուգել՝ վանդակ; բանաձև; կամ անվանում, որը վերաբերում է վանդակին, բանաձևին կամ արժեքին"}, "ISNUMBER": {"a": "(value)", "d": "Ստուգում է՝ արդյոք արժեքը թիվ է, և վերադարձնում է ՃՇՄԱՐԻՏ կամ ԿԵՂԾ", "ad": "այն արժեքն է, որը ցանկանում եք ստուգել: Արժեքը կարող է վերաբերել վանդակին, բանաձևին կամ անվանմանը, որը վերաբերում է վանդակին, բանաձևին կամ արժեքին"}, "ISODD": {"a": "(number)", "d": "Վերադարձնում է ՃՇՄԱՐԻՏ, եթե թիվը կենտ է", "ad": "փորձարկման արժեքն է"}, "ISREF": {"a": "(value)", "d": "Ստուգում է՝ արդյոք արժեքը հղում է, և վերադարձնում է ՃՇՄԱՐԻՏ կամ ԿԵՂԾ", "ad": "այն արժեքն է, որը ցանկանում եք ստուգել: Արժեքը կարող է վերաբերել վանդակին, բանաձևին կամ անվանմանը, որը վերաբերում է վանդակին, բանաձևին կամ արժեքին"}, "ISTEXT": {"a": "(value)", "d": "Ստուգում է՝ արդյոք արժեքը տեքստ է, և վերադարձնում է ՃՇՄԱՐԻՏ կամ ԿԵՂԾ", "ad": "այն արժեքն է, որը ցանկանում եք ստուգել: Արժեքը կարող է վերաբերել վանդակին, բանաձևին կամ անվանմանը, որը վերաբերում է վանդակին, բանաձևին կամ արժեքին"}, "N": {"a": "(value)", "d": "Փոխակերպում է ոչ թվային արժեքը թվի, ամսաթվերը սերիական համարների, ՃՇՄԱՐԻՏ-ի 1-ի, ցանկացած այլ բանի 0-ի (զրո)", "ad": "այն արժեքն է, որը ցանկանում եք փոխարկե"}, "NA": {"a": "()", "d": "Վերադարձնում է #N/A սխալի արժեքը (արժեքը հասանելի չէ)", "ad": ""}, "SHEET": {"a": "([value])", "d": "Վերադարձնում է մատնանշված թերթի համարը", "ad": "աղյուսակաթերթի կամ տեղեկանքի անվանում է, որի համարը ցանկանում եք: Բաց թողնելու դեպքում ֆունկցիան պարունակող թերթիկի համարը վերադարձվում է"}, "SHEETS": {"a": "([reference])", "d": "Վերադարձնում է տեղեկանքի թերթերի քանակը", "ad": "տեղեկանք է, որի համար ցանկանում եք իմանալ պարունակած թերթերի քանակը: Բաց թողնելու դեպքում ֆունկցիան պարունակող աշխատանքային գրքում թերթերի քանակը վերադարձվում է"}, "TYPE": {"a": "(value)", "d": "Վերադարձնում է արժեքի տվյալների տեսակը ներկայացնող ամբողջ թիվ՝ թիվ = 1; տեքստ = 2; տրամաբանական արժեքը = 4; սխալի արժեքը = 16; զանգված = 64; բարդ տվյալներ = 128", "ad": "կարող է լինել ցանկացած արժեք"}, "AND": {"a": "(logical1; [logical2]; ...)", "d": "Ստուգում է արդյոք բոլոր արգումենտները ճշմարիտ են, և վերադարձնում է ՃՇՄԱՐԻՏ, եթե բոլոր արգումենտները ճշմարիտ են", "ad": "1-ից 255 պայմաններ են, որոնք ցանկանում եք ստուգել, որոնք կարող են լինել TRUE կամ FALSE և կարող են լինել տրամաբանական արժեքներ, զանգվածներ կամ հղումներ"}, "FALSE": {"a": "()", "d": "Վերադարձնում է ՃՇՄԱՐԻՏ տրամաբանական արժեքը", "ad": ""}, "IF": {"a": "(logical_test; [value_if_true]; [value_if_false])", "d": "Ստուգում է՝ արդյոք պայմանը բավարարված է, և վերադարձնում է մի արժեք, եթե ՃՇՄԱՐԻՏ, և մեկ այլ արժեք, եթե ԿԵՂԾ", "ad": "ցանկացած արժեք կամ արտահայտություն է, որը կարող է գնահատվել TRUE կամ FALSE!ա յն արժեքն է, որը վերադարձվում է, եթե Logical_test-ը TRUE է: Եթե բաց թողնվի, TRUE-ն վերադարձվում է: Դուք կարող եք տեղադրել մինչև յոթ IF ֆունկցիաներ!այն արժեքն է, որը վերադարձվում է, եթե Logical_test-ը FALSE է: Բաց թողնելու դեպքում FALSE-ը վերադարձվում է"}, "IFS": {"a": "(logical_test; value_if_true; ...)", "d": "Ստուգում է, թե արդյոք մեկ կամ մի քանի պայմաններ են բավարարված և վերադարձնում է առաջին ճՇՄԱՐԻՏ պայմանին համապատասխանող արժեք", "ad": "ցանկացած արժեք կամ արտահայտություն է, որը կարող է գնահատվել TRUE կամ FALSE!արժեքը վերադարձվում է, եթե Logical_test-ը TRUE է"}, "IFERROR": {"a": "(value; value_if_error)", "d": "Վերադարձնում է արժեք_եթե_սխալ, եթե արտահայտությունը սխալ է, իսկ ինքն արտահայտության արժեքը հակառակ դեպքում", "ad": "ցանկացած արժեք կամ արտահայտություն կամ հղում է!ցանկացած արժեք կամ արտահայտություն կամ հղում"}, "IFNA": {"a": "(value; value_if_na)", "d": "Վերադարձնում է ձեր նշած արժեքը, եթե արտահայտությունը դառնում է #N/A, հակառակ դեպքում վերադարձնում է արտահայտության արդյունքը", "ad": "ցանկացած արժեք կամ արտահայտություն կամ հղում է!ցանկացած արժեք կամ արտահայտություն կամ հղում"}, "NOT": {"a": "(logical)", "d": "Փոխում է ԿԵՂԾ-ը ՃՇՄԱՐԻՏ-ի կամ ՃՇՄԱՐԻՏ-ը ԿԵՂԾ-ի", "ad": "արժեք կամ արտահայտություն է, որը կարող է գնահատվել TRUE կամ FALSE"}, "OR": {"a": "(logical1; [logical2])", "d": "Ստուգում է արդյոք արգումենտներից որևէ մեկը ճՇՄԱՐԻՏ է, և վերադարձնում է ճՇՄԱՐԻՏ կամ ԿԵՂԾ: Վերադարձնում է ԿԵՂԾ միայն այն դեպքում, եթե բոլոր արգումենտները ԿԵՂԾ են", "ad": "1-ից 255 պայմաններ են, որոնք ցանկանում եք ստուգել, որոնք կարող են լինել TRUE կամ FALSE"}, "SWITCH": {"a": "(expression; value1; result1; [default_or_value2]; [result2]; ...)", "d": "Գնահատում է արտահայտությունը արժեքների ցանկի համեմատ և վերադարձնում է առաջին համապատասխան արժեքին համապատասխանող արդյունքը: Եթե ​​համընկնում չկա, վերադարձվում է կամընտիր լռելյայն արժեք", "ad": "արտահայտություն է, որը պետք է գնահատվի!արժեք է, որը պետք է համեմատվի արտահայտության հետ!արդյունք է, որը պետք է վերադարձվի, եթե համապատասխան արժեքը համընկնում է արտահայտության հետ"}, "TRUE": {"a": "()", "d": "Վերադարձնում է ՃՇՄԱՐԻՏ տրամաբանական արժեքը", "ad": ""}, "XOR": {"a": "(logical1; [logical2]; ...)", "d": "Վերադարձնում է բոլոր արգումենտների տրամաբանական «Բացառիկ Կամ»:", "ad": "1-ից 254 պայմաններ են, որոնք ցանկանում եք ստուգել, որոնք կարող են լինել TRUE կամ FALSE և կարող են լինել տրամաբանական արժեքներ, զանգվածներ կամ հղումներ"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Վերադարձնում է այն տեքստը, որը նախքան տառերը սահմանելն է.", "ad": "Տեքստը, որը ցանկանում եք որոնել սահմանազատողի համար․!Նիշը կամ տողը որպես սահմանազատիչ օգտագործելու համար!Սահմանազատողի ցանկալի առաջացումը: Լռելյայն 1 է: Բացասական թիվը որոնում է վերջից!Փնտրում է տեքստում սահմանազատող համընկնում: Լռելյայնորեն, կատարվում է մեծատառերի համընկնումը!Լռելյայնորեն դրանք չեն համընկնում!Վերադարձվում է, եթե համընկնում չգտնվի: Լռելյայնորեն, #N/A վերադարձվում է:"}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Վերադարձնում է այն տեքստը, որը տառերը սահմանելուց հետո է.", "ad": "Տեքստը, որը ցանկանում եք որոնել սահմանազատողի համար!Նիշը կամ տողը որպես սահմանազատիչ օգտագործելու համար!Սահմանազատողի ցանկալի առաջացումը: Լռելյայն 1 է: Բացասական թիվը որոնում է վերջից!Փնտրում է տեքստում սահմանազատող համընկնում: Լռելյայնորեն, կատարվում է մեծատառերի համընկնումը!Լռելյայնորեն դրանք չեն համընկնում: Վերադարձվում է, եթե համընկնում չգտնվի: Լռելյայնորեն, #N/A վերադարձվում է:"}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Տարածել տեքստը շարքերում կամ սյունակներում սահմանանշիչներով.", "ad": "Տեքստը, որը պետք է բաժանվի!Նիշ կամ տող՝ սյունակները բաժանելու համար!Նիշ կամ տող՝ տողերը ըստ բաժանելու!Անտեսել դատարկ վանդակները: Լռելյայն է FALSE․!Տեքստում որոնում է սահմանազատող համընկնում: Լռելյայնորեն, մեծատառերի համընկնումն արված է!Լրացման համար օգտագործվող արժեքը: Լռելյայնորեն օգտագործվում է #N/A:"}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Ծալում է գծի կամ սյունակի վեկտորը որոշակի թվով արժեքներից հետո:", "ad": "Վեկտորը կամ հղումը ծալելու համար! Արժեքների առավելագույն քանակը մեկ տողում․!Արժեքը, որով պետք է լրացվի: Լռելյայն #N/A է:"}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "ՈՒղղահայաց շարում է զանգվածները մեկ զանգվածի մեջ.", "ad": "Զանգված կամ հղում, որը պետք է կուտակվի:"}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Հորիզոնական կերպով շարում է զանգվածները մեկ զանգվածի մեջ:", "ad": "Զանգված կամ հղում, որը պետք է կուտակվի:"}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Վերադարձնում է տողերը զանգվածից կամ հղումից․", "ad": "Վերադարձվող տողերը պարունակող զանգվածը կամ հղումը!Վերադարձվող տողի թիվը։"}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Վերադարձնում է սյունակները զանգվածից կամ հղումից.", "ad": "Վերադարձվող սյունակները պարունակող զանգվածը կամ հղումը! Վերադարձվող սյունակի թիվը։"}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Վերադարձնում է զանգվածը որպես մեկ սյունակ.", "ad": "Զանգվածը կամ հղումը, որը պետք է վերադարձվի որպես սյունակ!Անտեսել որոշակի տեսակի արժեքներ: Լռելյայնորեն, ոչ մի արժեք չի անտեսվում!Սկանավորեք զանգվածը սյունակով: Լռելյայնորեն, զանգվածը սկանավորվում է ըստ տողերի:"}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "Վերադարձնում է զանգվածը որպես մեկ տող.", "ad": "Զանգվածը կամ հղումը, որը պետք է վերադարձվի որպես տող!Անտեսել որոշակի տեսակի արժեքներ: Լռելյայնորեն, ոչ մի արժեք չի անտեսվում!Սկանավորեք զանգվածը սյունակով: Լռելյայնորեն, զանգվածը սկանավորվում է ըստ տողերի:"}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Ծալում է տող կամ սյունակ վեկտորը որոշակի թվով արժեքներից հետո.", "ad": "Ծալման վեկտորը կամ հղումը!Արժեքների առավելագույն քանակը մեկ սյունակում!Արժեքը, որով պետք է լրացվի։ Լռելյայն #N/A է:"}, "TAKE": {"a": "(array, rows, [columns])", "d": "Վերադարձնում է տողեր կամ սյունակներ զանգվածի սկզբից կամ ավարտից.", "ad": "Զանգվածը, որից պետք է վերցնել տողեր կամ սյունակներ!Վերցնելու տողերի քանակը: Բացասական արժեք է վերցվում զանգվածի վերջից!Վերցվող սյունակների քանակը: Բացասական արժեքը վերցնում է զանգվածի վերջից:"}, "DROP": {"a": "(array, rows, [columns])", "d": "Ցուցադրում է տողերը կամ սյունակները զանգվածի սկզբից կամ ավարտից.", "ad": "Զանգվածը, որից պետք է թողնել տողեր կամ սյունակներ!Բացասական արժեքն ընկնում է զանգվածի վերջից!Սյունակների քանակը, որոնք պետք է իջնեն: Բացասական արժեքն ընկնում է զանգվածի վերջից:"}, "SEQUENCE": {"a": "(rows, [columns], [start], [step])", "d": "Վերադարձնում է թվերի հաջորդականություն", "ad": "վերադարձվող տողերի թիվը! վերադարձվող սյունակների քանակը, հաջորդականության առաջին թիվը!հաջորդականության յուրաքանչյուր հաջորդ արժեքն ավելացնելու գումարը"}, "EXPAND": {"a": "(array, rows, [columns], [pad_with])", "d": "Ընդլայնում է զանգվածը մինչև նշված չափերը:", "ad": "Ընդլայնվող զանգված!Ընդլայնված զանգվածի տողերի թիվը: Եթե բացակայում է, տողերը չեն ընդլայնվի!Ընդլայնված զանգվածի սյունակների թիվը: Եթե բացակայում է, սյունակները չեն ընդլայնվի!Արժեքը, որով պետք է լրացվի: Լռելյայն #N/A է:"}, "XMATCH": {"a": "(lookup_value, lookup_array, [match_mode], [search_mode])", "d": "Վերադարձնում է միավորի հարաբերական դիրքը զանգվածում: Սկզբնադիր պահանջվում է ճշգրիտ համընկնում", "ad": "այն արժեքն է, որը պետք է որոնվի!որոնման զանգվածն է կամ տիրույթը: !նշեք, թե ինչպես պետք է համապատասխանի lookup_ value lookup_array-ի արժեքներին!նշեք որոնման ռեժիմը, որն օգտագործվում է: Լռելյայնորեն կօգտագործվի առաջինից վերջին որոնումը"}, "FILTER": {"a": "(array, include, [if_empty])", "d": "Զտել ընդգրկույթ կամ զանգված", "ad": "տիրույթը կամ զանգվածը զտելու համար!բուլյանների զանգված, որտեղ TRUE-ն ներկայացնում է տող կամ սյունակ, որը պետք է պահպանվի!վերադարձվում է, եթե որևէ տարր չի պահպանվում:"}, "ARRAYTOTEXT": {"a": "(array, [format])", "d": "Վերադարձնում է զանգվածի գրվածքային ներկայացումը", "ad": "զանգվածը՝ որպես տեքստ!ներկայացնելու համար, տեքստի ձևաչափը"}, "SORT": {"a": "(array, [sort_index], [sort_order], [by_col])", "d": "Տեսակավորում է ընդգրկույթը կամ զանգվածը", "ad": "տիրույթը կամ զանգվածը՝ տեսակավորելու համար!մի թիվ, որը ցույց է տալիս ըստ դասակարգման տող կամ սյունակ. մի թիվ, որը ցույց է տալիս ցանկալի տեսակավորման կարգը; 1 աճման կարգի համար (կանխադրված), -1 նվազման կարգի համար!տրամաբանական արժեք, որը ցույց է տալիս տեսակավորման ցանկալի ուղղությունը. FALSE՝ ըստ տողերի (լռելյայն), TRUE՝ ըստ սյունակի տեսակավորման"}, "SORTBY": {"a": "(array, by_array, [sort_order], ...)", "d": "Տեսակավորում է ընդգրկույթը կամ զանգվածը՝ ըստ համապատասխան ընդգրկույթում կամ զանգվածում առկա արժեքների", "ad": "տեսակավորվող միջակայքը կամ զանգվածը!տեսակավորելու համար նախատեսված տիրույթը կամ զանգվածը!, ցանկալի տեսակավորման կարգը ցույց տվող թիվ; 1 աճման կարգի համար (լռելյայն), -1 նվազման համար"}, "GETPIVOTDATA": {"a": "(data_field; pivot_table; [field]; [item]; ...)", "d": "Արտահանում է PivotTable-ում պահեստավորված տվյալները", "ad": "Տվյալների դաշտի անվանումն է, որտեղից տվյալները հանվում են!հղում է առանցքային աղյուսակի վանդակի կամ վանդակների տիրույթի!որը պարունակում է այն տվյալները!որոնք ցանկանում եք արբերել"}, "IMPORTRANGE": {"a": "(աղյուսակի_url, ընդգրկույթի_տող)", "d": "Որոշակի աղյուսակից ներմուծում է բջիջների ընդգրկույթ:", "ad": "աղյուսակի URL-ն է, որտեղից կներմուծվեն տվյալները!Ներմուծվող միջակայքն է"}}