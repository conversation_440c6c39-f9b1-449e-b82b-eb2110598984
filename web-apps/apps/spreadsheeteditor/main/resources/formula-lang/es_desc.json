{"DATE": {"a": "(año; mes; día)", "d": "Devuelve el número que representa la fecha en código de fecha y hora", "ad": "es un número entre 1900 o 1904 (en función del sistema de fecha del libro) y 9999!es un número de 1 a 12 que representa el mes del año!es un número de 1 a 31 que representa el día del mes"}, "DATEDIF": {"a": "( fecha-inicio; fecha-final; unidad )", "d": "Devuelve la diferencia entre dos valores de fecha (fecha de inicio y fecha de fin), según el intervalo (unidad) especificado", "ad": "Es una fecha que representa la primera o la fecha inicial de un período determinado!Una fecha que representa la última del período o al fecha de finalización!El tipo de información que desea que se devuelva"}, "DATEVALUE": {"a": "(texto_de_fecha)", "d": "Convierte una fecha en forma de texto en un número que representa la fecha en código de fecha y hora", "ad": "es el texto que representa una fecha en formato de fecha de Spreadsheet Editor, entre 1/1/1900 o 1/1/1904 (en función del sistema de fecha del libro) y 31/12/9999"}, "DAY": {"a": "(núm_de_serie)", "d": "Devuelve el día del mes (un número de 1 a 31).", "ad": "es un número en el código de fecha y hora usado por Spreadsheet Editor"}, "DAYS": {"a": "(fecha_inicial; fecha_final)", "d": "Devuelve la cantidad de días entre las dos fechas.", "ad": "Fecha de inicio y fecha de finalización son las dos fechas entre las que quieres saber los días que hay!Fecha de inicio y fecha de finalización son las dos fechas entre las que quieres saber los días que hay"}, "DAYS360": {"a": "(fecha_inicial; fecha_final; [método])", "d": "Calcula el número de días entre dos fechas basándose en un año de 360 días (doce meses de 30 días)", "ad": "fecha_inicial y fecha_final son las dos fechas entre las que se desea saber el número de días!fecha_inicial y fecha_final son las dos fechas entre las que se desea saber el número de días!es un valor lógico que especifica el método de cálculo: para usar EE.UU. (NASD) = FALSO u omitido; para usar Europeo = VERDADERO."}, "EDATE": {"a": "(fecha_inicial; meses)", "d": "Devuelve el número de serie de la fecha que es el número indicado de meses antes o después de la fecha inicial", "ad": "es un número de fecha de serie que representa la fecha inicial!es el número de meses antes o después de la fecha_inicial"}, "EOMONTH": {"a": "(fecha_inicial; meses)", "d": "Devuelve el número de serie del último día del mes antes o después del número especificado de meses", "ad": "es el número de fecha de serie que representa la fecha inicial!es el número de meses antes o después de la fecha_inicial"}, "HOUR": {"a": "(núm_de_serie)", "d": "Devuelve la hora como un número de 0 (12:00 a.m.) a 23 (11:00 p.m.).", "ad": "es un número en el código de fecha y hora usado por Spreadsheet Editor, o texto en formato de hora, como por ejemplo 16:48:00 o 4:48:00 p.m."}, "ISOWEEKNUM": {"a": "(fecha)", "d": "Devuelve el número de semana ISO del año para una fecha determinada", "ad": "Es el código de fecha y hora que usa Spreadsheet Editor para calcular la fecha y la hora"}, "MINUTE": {"a": "(núm_de_serie)", "d": "Devuelve el minuto, un número de 0 a 59.", "ad": "es un número en el código de fecha y hora usado por Spreadsheet Editor, o texto en formato de hora, como por ejemplo 16:48:00 o 4:48:00 p.m."}, "MONTH": {"a": "(núm_de_serie)", "d": "Devuelve el mes, un número entero de 1 (enero) a 12 (diciembre).", "ad": "es un número en el código de fecha y hora usado por Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(fecha_inicial; fecha_final; [vacaciones])", "d": "Devuelve el número total de días laborables entre dos fechas", "ad": "es un número de fecha de serie que representa la fecha inicial!es un número de fecha de serie que representa la fecha final!es un conjunto opcional de uno o varios números de fecha de serie para excluir del calendario laboral, como las vacaciones estatales y federales y los días libres"}, "NETWORKDAYS.INTL": {"a": "(fecha_inicial; fecha_final; [fin_de_semana]; [días_no_laborables])", "d": "Devuelve el número de días laborables completos entre dos fechas con parámetros de fin de semana personalizados", "ad": "es un número de fecha de serie que representa la fecha inicial!es un número de fecha de serie que representa la fecha final!es un número o una cadena que especifica cuándo tienen lugar los fines de semana!es un conjunto opcional de uno o más números de fecha de serie que se deben excluir del calendario laboral, como los días no laborables estatales y federales, y los días libres"}, "NOW": {"a": "()", "d": "Devuelve la fecha y hora actuales con formato de fecha y hora.", "ad": ""}, "SECOND": {"a": "(núm_de_serie)", "d": "Devuelve el segundo, un número de 0 a 59.", "ad": "es un número en el código de fecha y hora usado por Spreadsheet Editor, o texto en formato de hora, como por ejemplo 16:48:23 o 4:48:47 p.m."}, "TIME": {"a": "(hora; minuto; segundo)", "d": "Convierte horas, minutos y segundos dados como números en un número de serie, con formato de hora", "ad": "es un número entre 0 y 23 que representa la hora!es un número entre 0 y 59 que representa los minutos!es un número entre 0 y 59 que representa los segundos"}, "TIMEVALUE": {"a": "(texto_de_hora)", "d": "Convierte una hora de texto en un número de serie para una hora, un número de 0 (12:00:00 a.m.) a 0.999988426 (11:59:59 p.m.). Da formato al número con un formato de hora después de introducir la fórmula", "ad": "es una cadena de texto que indica la hora en cualquiera de los formatos de hora de Spreadsheet Editor (se omite la información de fecha en la cadena)"}, "TODAY": {"a": "()", "d": "Devuelve la fecha actual con formato de fecha.", "ad": ""}, "WEEKDAY": {"a": "(núm_de_serie; [tipo])", "d": "Devuelve un número de 1 a 7 que identifica el día de la semana.", "ad": "es un número que representa una fecha!es un número: para domingo=1 a sábado=7, use 1; para lunes=1 a domingo=7, use 2; para lunes=0 a domingo=6, use 3"}, "WEEKNUM": {"a": "(número_serie; [tipo_devuelto])", "d": "Devuelve el número de semanas en el año", "ad": "es el código de fecha-hora utilizado por Spreadsheet Editor para los cálculos de fecha y hora!es un número (1 o 2) que determina el tipo de valor devuelto"}, "WORKDAY": {"a": "(fecha_inicial; días; [vacaciones])", "d": "Devuelve el número de serie de la fecha antes o después de un número especificado de días laborables", "ad": "es el número de fecha de serie que representa la fecha inicial!es el número de días laborables antes o después de la fecha_inicial!es una matriz opcional de uno o varios números de fecha de serie que se excluyen del calendario laboral, como las vacaciones estatales, federales y días libres"}, "WORKDAY.INTL": {"a": "(fecha_inicial; días; [fin_de_semana]; [días_no_laborables])", "d": "Devuelve el número de serie de la fecha anterior o posterior a un número especificado de días laborables con parámetros de fin de semana personalizados", "ad": "es un número de fecha de serie que representa la fecha de comienzo!es el número de días laborables antes o después de fecha_inicial!es un número o cadena que especifica cuándo tienen lugar los fines de semana!es una matriz opcional de uno o más números de fecha de serie que se deben excluir del calendario laboral, como los días no laborables estatales y federales y los días libres"}, "YEAR": {"a": "(núm_de_serie)", "d": "Devuelve el año, un número entero en el rango 1900-9999.", "ad": "es un número en el código de fecha y hora usado por Spreadsheet Editor"}, "YEARFRAC": {"a": "(fecha_inicial; fecha_final; [base])", "d": "Devuelve la fracción del año que representa el número de días completos entre la fecha_inicial y la fecha_final", "ad": "es el número de fecha de serie que representa la fecha inicial!es el número de fecha de serie que representa la fecha final!determina en qué tipo de base deben ser contados los días"}, "BESSELI": {"a": "(x; n)", "d": "Devuelve la función Bessel In(x) modificada", "ad": "es el valor en el que se evalúa la función!es el orden de la función Bessel"}, "BESSELJ": {"a": "(x; n)", "d": "Devuelve la función Bessel Jn(x)", "ad": "es el valor en el que se evalúa la función!es el orden de la función Bessel"}, "BESSELK": {"a": "(x; n)", "d": "Devuelve la función Bessel Kn(x) modificada", "ad": "es el valor en el que se evalúa la función!es el orden de la función"}, "BESSELY": {"a": "(x; n)", "d": "Devuelve la función Bessel Yn(x)", "ad": "es el valor en el que se evalúa la función!es el orden de la función"}, "BIN2DEC": {"a": "(número)", "d": "Convierte un número binario en decimal", "ad": "es el número binario que desea convertir"}, "BIN2HEX": {"a": "(número; [posiciones])", "d": "Convierte un número binario en hexadecimal", "ad": "es el número binario que desea convertir!es el número de caracteres que se deben usar"}, "BIN2OCT": {"a": "(número; [posiciones])", "d": "Convierte un número binario en octal", "ad": "es el número binario que desea convertir!es el número de caracteres que se deben usar"}, "BITAND": {"a": "(número1; número2)", "d": "Devuelve un bit a bit 'And' de dos números", "ad": "Es la representación decimal del número binario que quieres evaluar!Es la representación decimal del número binario que quieres evaluar"}, "BITLSHIFT": {"a": "(número; cambio_cantidad)", "d": "Devuelve un número desplazado a la izquierda por bits de desplazamiento", "ad": "Es la representación decimal del número binario que quiere evaluar!Es la cantidad de bits que quiere desplazar el número hacia la izquierda"}, "BITOR": {"a": "(número1; número2)", "d": "Devuelve un bit a bit 'Or' de dos números", "ad": "Es la representación decimal del número binario que quieres evaluar!Es la representación decimal del número binario que quieres evaluar"}, "BITRSHIFT": {"a": "(número; cambio_cantidad)", "d": "Devuelve un número desplazado a la derecha por bits de desplazamiento", "ad": "Es la representación decimal del número binario que quiere evaluar!Es la cantidad de bits que quiere desplazar el número hacia la derecha"}, "BITXOR": {"a": "(número1; número2)", "d": "Devuelve un bit a bit 'Exclusive Or' de dos números", "ad": "Es la representación decimal del número binario que quieres evaluar!Es la representación decimal del número binario que quieres evaluar"}, "COMPLEX": {"a": "(núm_real; i_núm; [sufijo])", "d": "Convierte el coeficiente real e imaginario en un número complejo", "ad": "es el coeficiente real de un número complejo!es el coeficiente imaginario del número complejo!es el sufijo para el componente imaginario del número complejo"}, "CONVERT": {"a": "(número; desde_unidad; a_unidad)", "d": "Convierte un número de un sistema decimal a otro", "ad": "es el valor desde_unidades para convertir!son las unidades para el número!son las unidades para el resultado"}, "DEC2BIN": {"a": "(número; [posiciones])", "d": "Convierte un número decimal en binario", "ad": "es el entero decimal que desea convertir!es el número de caracteres que se deben usar"}, "DEC2HEX": {"a": "(número; [posiciones])", "d": "Convierte un número decimal en hexadecimal", "ad": "es el entero decimal que desea convertir!es el número de caracteres que se deben usar"}, "DEC2OCT": {"a": "(número; [posiciones])", "d": "Convierte un número decimal en octal", "ad": "es el entero decimal que desea convertir!es el número de caracteres que se deben usar"}, "DELTA": {"a": "(número1; [número2])", "d": "<PERSON><PERSON><PERSON> si los dos números son iguales", "ad": "es el primer número!es el segundo número"}, "ERF": {"a": "(límite_inferior; [límite_superior])", "d": "Devuelve la función de error", "ad": "es el límite inferior para integrar FUN.ERR!es el límite superior para integrar FUN.ERR"}, "ERF.PRECISE": {"a": "(X)", "d": "Devuelve la función de error", "ad": "es el límite inferior para integrar FUN.ERROR.EXACTO"}, "ERFC": {"a": "(x)", "d": "Devuelve la función de error complementaria", "ad": "es el límite inferior para integrar FUN.ERR"}, "ERFC.PRECISE": {"a": "(X)", "d": "Devuelve la función de error complementaria", "ad": "es el límite inferior para integrar FUN.ERROR.EXACTO"}, "GESTEP": {"a": "(número; [paso])", "d": "Prue<PERSON> si un número es mayor que el valor de referencia", "ad": "es el valor a comparar con el argumento valor_red!es el valor de referencia"}, "HEX2BIN": {"a": "(número; [posiciones])", "d": "Convierte un número hexadecimal en binario", "ad": "es el número hexadecimal que desea convertir!es el número de caracteres que se deben usar"}, "HEX2DEC": {"a": "(número)", "d": "Convierte un número hexadecimal en decimal", "ad": "es el número hexadecimal que desea convertir"}, "HEX2OCT": {"a": "(número; [posiciones])", "d": "Convierte un número hexadecimal en octal", "ad": "es el número hexadecimal que desea convertir!es el número de caracteres que se deben usar"}, "IMABS": {"a": "(inúmero)", "d": "Devuelve el valor absoluto (módulo) de un número complejo", "ad": "es el número complejo cuyo valor absoluto desea calcular"}, "IMAGINARY": {"a": "(inúmero)", "d": "Devuelve el coeficiente imaginario de un número complejo", "ad": "es el número complejo cuyo coeficiente imaginario desea calcular"}, "IMARGUMENT": {"a": "(inúmero)", "d": "Devuelve el argumento q, un ángulo expresado en radianes", "ad": "es el número complejo cuyo argumento desea calcular"}, "IMCONJUGATE": {"a": "(inúmero)", "d": "Devuelve el conjugado complejo de un número complejo", "ad": "es el número complejo cuyo conjugado desea calcular"}, "IMCOS": {"a": "(inúmero)", "d": "Devuelve el coseno de un número complejo", "ad": "es el número complejo cuyo coseno desea calcular"}, "IMCOSH": {"a": "(númeroi)", "d": "Devuelve el coseno hiperbólico de un número complejo", "ad": "Es un número complejo del que quieres saber el coseno hiperbólico"}, "IMCOT": {"a": "(númeroi)", "d": "Devuelve la cotangente de un número complejo", "ad": "Es un número complejo del que quieres saber la cotangente"}, "IMCSC": {"a": "(númeroi)", "d": "Devuelve la cosecante de un número complejo", "ad": "Es un número complejo del que quieres saber la cosecante"}, "IMCSCH": {"a": "(númeroi)", "d": "Devuelve la cosecante hiperbólica de un número complejo", "ad": "Es un número complejo del que quieres saber la cosecante hiperbólica"}, "IMDIV": {"a": "(inúmero1; inúmero2)", "d": "Devuelve el cociente de dos números complejos", "ad": "es el numerador o dividendo complejo!es el denominador o divisor complejo"}, "IMEXP": {"a": "(inúmero)", "d": "Devuelve el valor exponencial de un número complejo", "ad": "es el número complejo cuyo valor exponencial desea calcular"}, "IMLN": {"a": "(inúmero)", "d": "Devuelve el logaritmo natural de un número complejo", "ad": "es el número complejo cuyo logaritmo natural desea calcular"}, "IMLOG10": {"a": "(inúmero)", "d": "Devuelve el logaritmo de base 10 de un número complejo", "ad": "es el número complejo cuyo logaritmo común desea calcular"}, "IMLOG2": {"a": "(inúmero)", "d": "Devuelve el logaritmo de base 2 de un número complejo", "ad": "es el número complejo cuyo logaritmo de base 2 desea calcular"}, "IMPOWER": {"a": "(inúmero; número)", "d": "Devuelve un número complejo elevado a la potencia del entero", "ad": "es un número complejo que desea elevar a una potencia!es la potencia a la cual desea elevar un número complejo"}, "IMPRODUCT": {"a": "(inúmero1; [inúmero2]; ...)", "d": "Devuelve el producto de 1 a 255 números complejos", "ad": "Inúmero1, Inúmero2,... son de 1 a 255 números que desea multiplicar."}, "IMREAL": {"a": "(inúmero)", "d": "Devuelve el coeficiente real de un número complejo", "ad": "es el número complejo cuyo coeficiente real desea calcular"}, "IMSEC": {"a": "(númeroi)", "d": "Devuelve la secante de un número complejo", "ad": "Es un número complejo del que quieres saber la secante"}, "IMSECH": {"a": "(númeroi)", "d": "Devuelve la secante hiperbólica de un número complejo ", "ad": "Es un número complejo del que quieres saber la secante hiperbólica"}, "IMSIN": {"a": "(inúmero)", "d": "Devuelve el seno de un número complejo", "ad": "es el número complejo cuyo seno desea calcular"}, "IMSINH": {"a": "(númeroi)", "d": "Devuelve el seno hiperbólico de un número complejo", "ad": "Es un número complejo del que quieres saber el seno hiperbólico"}, "IMSQRT": {"a": "(inúmero)", "d": "Devuelve la raíz cuadrada de un número complejo", "ad": "es el número complejo cuya raíz cuadrada desea calcular"}, "IMSUB": {"a": "(inúmero1; inúmero2)", "d": "Devuelve la diferencia de dos números complejos", "ad": "es el número complejo del que debe restar inúmero2!es el número complejo para restar del inúmero1"}, "IMSUM": {"a": "(inúmero1; [inúmero2]; ...)", "d": "Devuelve la suma de números complejos", "ad": "son de 1 a 255 números que desea sumar"}, "IMTAN": {"a": "(númeroi)", "d": "Devuelve la tangente de un número complejo", "ad": "Es un número complejo del que quiere saber la tangente"}, "OCT2BIN": {"a": "(número; [posiciones])", "d": "Convierte un número octal en binario", "ad": "es el número octal que desea convertir!es el número de caracteres que se deben usar"}, "OCT2DEC": {"a": "(número)", "d": "Convierte un número octal en decimal", "ad": "es el número octal que desea convertir"}, "OCT2HEX": {"a": "(número; [posiciones])", "d": "Convierte un número octal en hexadecimal", "ad": "es el número octal que desea convertir!es el número de caracteres que se deben usar"}, "DAVERAGE": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Obtiene el promedio de los valores de una columna, lista o base de datos que cumplen las condiciones especificadas", "ad": "es el rango de celdas que compone la lista o base de datos. Una base de datos es una lista de datos relacionados!es el rótulo entre comillas dobles de la columna o un número que representa la posición de la columna en la lista!es el rango de celdas que contiene las condiciones especificadas. El rango incluye un rótulo de columna y una celda bajo el rótulo para una condición"}, "DCOUNT": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Cuenta las celdas que contienen números en el campo (columna) de registros de la base de datos que cumplen las condiciones especificadas", "ad": "es el rango de celdas que compone la lista o base de datos. Una base de datos es una lista de datos relacionados!es el rótulo entre comillas dobles de la columna o un número que representa la posición de la columna en la lista!es el rango de celdas que contiene las condiciones especificadas. El rango incluye un rótulo de columna y una celda bajo el rótulo para una condición"}, "DCOUNTA": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Cuenta el número de celdas que no están en blanco en el campo (columna) de los registros de la base de datos que cumplen las condiciones especificadas", "ad": "es el rango de celdas que compone la lista o base de datos. Una base de datos es una lista de datos relacionados!es el rótulo entre comillas dobles de la columna o un número que representa la posición de la columna en la lista!es el rango de celdas que contiene las condiciones especificadas. El rango incluye un rótulo de columna y una celda bajo el rótulo para una condición"}, "DGET": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Extrae de una base de datos un único registro que coincide con las condiciones especificadas", "ad": "es el rango de celdas que compone la lista o base de datos. Una base de datos es una lista de datos relacionados!es el rótulo entre comillas dobles de la columna o un número que representa la posición de la columna en la lista!es el rango de celdas que contiene las condiciones especificadas. El rango incluye un rótulo de columna y una celda bajo el rótulo para una condición"}, "DMAX": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Devuelve el número máximo en el campo (columna) de registros de la base de datos que coinciden con las condiciones especificadas", "ad": "es el rango de celdas que compone la lista o base de datos. Una base de datos es una lista de datos relacionados!es el rótulo entre comillas dobles de la columna o un número que representa la posición de la columna en la lista!es el rango de celdas que contiene las condiciones especificadas. El rango incluye un rótulo de columna y una celda bajo el rótulo para una condición"}, "DMIN": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Devuelve el número menor del campo (columna) de registros de la base de datos que coincide con las condiciones especificadas", "ad": "es el rango de celdas que compone la lista o base de datos. Una base de datos es una lista de datos relacionados!es el rótulo entre comillas dobles de la columna o un número que representa la posición de la columna en la lista!es el rango de celdas que contiene las condiciones especificadas. El rango incluye un rótulo de columna y una celda bajo el rótulo para una condición"}, "DPRODUCT": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Multiplica los valores del campo (columna) de registros en la base de datos que coinciden con las condiciones especificadas", "ad": "es el rango de celdas que compone la lista o base de datos. Una base de datos es una lista de datos relacionados!es el rótulo entre comillas dobles de la columna o un número que representa la posición de la columna en la lista!es el rango de celdas que contiene las condiciones especificadas. El rango incluye un rótulo de columna y una celda bajo el rótulo para una condición"}, "DSTDEV": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Calcula la desviación estándar basándose en una muestra de las entradas seleccionadas de una base de datos", "ad": "es el rango de celdas que compone la lista o base de datos. Una base de datos es una lista de datos relacionados!es el rótulo entre comillas dobles de la columna o un número que representa la posición de la columna en la lista!es el rango de celdas que contiene las condiciones especificadas. El rango incluye un rótulo de columna y una celda bajo el rótulo para una condición"}, "DSTDEVP": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Calcula la desviación estándar basándose en la población total de las entradas seleccionadas de una base de datos", "ad": "es el rango de celdas que compone la lista o base de datos. Una base de datos es una lista de datos relacionados!es el rótulo entre comillas dobles de la columna o un número que representa la posición de la columna en la lista!es el rango de celdas que contiene las condiciones especificadas. El rango incluye un rótulo de columna y una celda bajo el rótulo para una condición"}, "DSUM": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Suma los números en el campo (columna) de los registros que coinciden con las condiciones especificadas", "ad": "es el rango de celdas que compone la lista o base de datos. Una base de datos es una lista de datos relacionados!es el rótulo entre comillas dobles de la columna o un número que representa la posición de la columna en la lista!es el rango de celdas que contiene las condiciones especificadas. El rango incluye un rótulo de columna y una celda bajo el rótulo para una condición"}, "DVAR": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Calcula la varianza basándose en una muestra de las entradas seleccionadas de una base de datos", "ad": "es el rango de celdas que compone la lista o base de datos. Una base de datos es una lista de datos relacionados!es el rótulo entre comillas dobles de la columna o un número que representa la posición de la columna en la lista!es el rango de celdas que contiene las condiciones especificadas. El rango incluye un rótulo de columna y una celda bajo el rótulo para una condición"}, "DVARP": {"a": "(base_de_datos; nombre_de_campo; criterios)", "d": "Calcula la varianza basándose en la población total de las entradas seleccionadas de una base de datos", "ad": "es el rango de celdas que compone la lista o base de datos. Una base de datos es una lista de datos relacionados!es el rótulo entre comillas dobles de la columna o un número que representa la posición de la columna en la lista!es el rango de celdas que contiene las condiciones especificadas. El rango incluye un rótulo de columna y una celda bajo el rótulo para una condición"}, "CHAR": {"a": "(número)", "d": "Devuelve el carácter especificado por el número de código a partir del juego de caracteres establecido en su PC", "ad": "es un número entre 1 y 255 que especifica el carácter deseado"}, "CLEAN": {"a": "(texto)", "d": "Quita todos los caracteres no imprimibles del texto", "ad": "es cualquier información de hoja de cálculo de la cual se desea quitar los caracteres no imprimibles"}, "CODE": {"a": "(texto)", "d": "Devuelve el número de código del primer carácter del texto del juego de caracteres usados por su PC", "ad": "es el texto del que se desea obtener el código del primer carácter"}, "CONCATENATE": {"a": "(texto1; [texto2]; ...)", "d": "Une varios elementos de texto en uno solo", "ad": "son entre 1 y 255 elementos de texto que se unirán en un solo elemento y que pueden ser texto, cadenas, números o referencias simples de celdas"}, "CONCAT": {"a": "(texto1; ...)", "d": "Concatena una lista o rango de cadenas de texto", "ad": "son de 1 a 254 cadenas o rangos de texto que se unirán a una sola cadena de texto"}, "DOLLAR": {"a": "(número; [núm_de_decimales])", "d": "Convierte un número en texto usando formato de moneda", "ad": "es un número, una referencia a una celda que contiene un número o una fórmula que evalúa un número!es el número de dígitos a la derecha del separador decimal. El número se redondea si es necesario. Si se omite se establecerá: Decimales = 2"}, "EXACT": {"a": "(texto1; texto2)", "d": "Comprueba si dos cadenas de texto son exactamente iguales y devuelve VERDADERO o FALSO. IGUAL distingue mayúsculas de minúsculas", "ad": "es la primera cadena de texto!es la segunda cadena de texto"}, "FIND": {"a": "(texto_buscado; dentro_del_texto; [núm_inicial])", "d": "Devuelve la posición inicial de una cadena de texto dentro de otra cadena de texto. ENCONTRAR distingue mayúsculas de minúsculas", "ad": "es el texto que se desea encontrar. Use comillas dobles (sin texto) para que coincida con  el primer carácter en Dentro_texto. No se permiten caracteres comodín!es el texto que a su vez contiene el texto que se desea encontrar!especifica el carácter a partir del cual se iniciará la búsqueda. El primer carácter en Dentro_texto es 1. Si se omite, Núm_inicial = 1"}, "FINDB": {"a": "(cadena-1; cadena-2; [posición-inicio])", "d": "Encuentra la subcadena especificada (cadena-1) dentro de una cadena (cadena-2) y está destinada a los idiomas del conjunto de caracteres de doble bit (DBCS) como el japonés, chino, coreano, etc.", "ad": "es el texto que se desea encontrar. Use comillas dobles (sin texto) para que coincida con  el primer carácter en cadena-2. No se permiten caracteres comodín!es el texto que a su vez contiene el texto que se desea encontrar!especifica el carácter a partir del cual se iniciará la búsqueda. El primer carácter en cadena-2 es 1. Si se omite, Núm_inicial = 1"}, "FIXED": {"a": "(número; [decimales]; [no_separar_millares])", "d": "Redondea un número al número especificado de decimales y devuelve el resultado como texto con o sin comas", "ad": "es el número que se desea redondear y convertir en texto!es el número de dígitos a la derecha del separador decimal. Si se omite se establecerá: Decimales = 2!es un valor lógico: para no presentar comas en el texto devuelto  = VERDADERO; para presentar comas en el texto devuelto = FALSO u omitido"}, "LEFT": {"a": "(texto; [núm_de_caracteres])", "d": "Devuelve el número especificado de caracteres del principio de una cadena de texto", "ad": "es la cadena de texto que contiene los caracteres que desea extraer!especifica el número de caracteres que se desea que IZQUIERDA extraiga. Si se omite, se asume 1"}, "LEFTB": {"a": "(cadena; [número-caracteres])", "d": "Extrae la subcadena de la cadena especificada a partir del carácter izquierdo y está destinada a idiomas que utilizan el juego de caracteres de doble bit (DBCS) como el japonés, chino, coreano, etc.", "ad": "es la cadena de texto que contiene los caracteres que desea extraer!especifica el número de caracteres que se desea que IZQUIERDAB extraiga. Si se omite, se asume 1"}, "LEN": {"a": "(texto)", "d": "Devuelve el número de caracteres de una cadena de texto", "ad": "es el texto cuya longitud desea conocer. Los espacios cuentan como caracteres"}, "LENB": {"a": "( cadena )", "d": "Analiza la cadena especificada y devolver el número de caracteres que contiene y está destinada a idiomas que utilizan el juego de caracteres de doble bit (DBCS) como el japonés, chino, coreano, etc.", "ad": "es el texto cuya longitud desea conocer. Los espacios cuentan como caracteres"}, "LOWER": {"a": "(texto)", "d": "Convierte todas las letras de una cadena de texto en minúsculas", "ad": "es el texto que se desea convertir en minúsculas. Los caracteres en Texto que no sean letras no cambiarán"}, "MID": {"a": "(texto; posición_inicial; núm_de_caracteres)", "d": "Devuelve los caracteres del centro de una cadena de texto, dada una posición y longitud iniciales", "ad": "es la cadena de texto de la cual se desea extraer los caracteres!es la posición del primer carácter que se desea extraer del argumento texto. El primer carácter en Texto es 1!especifica el número de caracteres de Texto que se debe devolver"}, "MIDB": {"a": "(cadena; posición-empiece; número-caracteres)", "d": "Extrae los caracteres de la cadena especificada a partir de cualquier posición y está destinada a idiomas que utilizan el juego de caracteres de doble bit (DBCS) como el japonés, chino, coreano, etc.", "ad": "es la cadena de texto de la cual se desea extraer los caracteres!es la posición del primer carácter que se desea extraer del argumento texto. El primer carácter en Cadena es 1!especifica el número de caracteres de Cadena que se debe devolver"}, "NUMBERVALUE": {"a": "(texto; [separador_decimal]; [separador_grupo])", "d": "Convierte texto a número de manera independiente a la configuración regional", "ad": "Es la cadena que representa el número que quieres convertir!Es el carácter que se usa como separador decimal en la cadena!Es el carácter que se usa como el separador de grupos en la cadena"}, "PROPER": {"a": "(texto)", "d": "Convierte una cadena de texto en mayúsculas o minúsculas, según corresponda; la primera letra de cada palabra en mayúscula y las demás letras en minúscula", "ad": "es el texto entre comillas, una fórmula que devuelve texto o una referencia a una celda que contiene el texto al que se desea agregar mayúsculas"}, "REPLACE": {"a": "(texto_original; núm_inicial; núm_de_caracteres; texto_nuevo)", "d": "Reemplaza parte de una cadena de texto por otra", "ad": "es el texto en el que se desea reemplazar ciertos caracteres!es la posición del carácter dentro de texto_original que se desea reemplazar con texto_nuevo!es el número de caracteres en texto_original que se desea reemplazar!es el texto que reemplaza caracteres en texto_original"}, "REPLACEB": {"a": "(cadena-1; pos-inicio; número-caracteres; cadena-2)", "d": "Reemplaza un conjunto de caracteres, basado en el número de caracteres y la posición inicial que especifique, por un nuevo conjunto de caracteres y está destinada a idiomas que utilizan el conjunto de caracteres de doble bit (DBCS) como el japonés, chino, coreano, etc.", "ad": "es el texto en el que se desea reemplazar ciertos caracteres!es la posición del carácter dentro de cadena-1 que se desea reemplazar con cadena-2!es el número de caracteres en cadena-1 que se desea reemplazar!es el texto que reemplaza caracteres en cadena-1"}, "REPT": {"a": "(texto; núm_de_veces)", "d": "Repite el texto un número determinado de veces. Use REPETIR para rellenar una celda con el número de repeticiones de una cadena de texto", "ad": "es el texto que se desea repetir!es un número positivo que especifica el número de veces que el argumento texto se repetirá"}, "RIGHT": {"a": "(texto; [núm_de_caracteres])", "d": "Devuelve el número especificado de caracteres del final de una cadena de texto", "ad": "es la cadena de texto que contiene los caracteres que se desea extraer!especifica el número de caracteres que se desea extraer; si se omite, se asume 1"}, "RIGHTB": {"a": "(cadena; [número-caracteres])", "d": "Extrae una subcadena de una cadena a partir del carácter más a la derecha, basada en el número especificado de caracteres y está destinada a idiomas que utilizan el juego de caracteres de doble bit (DBCS) como el japonés, chino, coreano, etc.", "ad": "es la cadena de texto que contiene los caracteres que se desea extraer!especifica el número de caracteres que se desea extraer; si se omite, se asume 1"}, "SEARCH": {"a": "(texto_buscado; dentro_del_texto; [núm_inicial])", "d": "Devuelve el número de carácter en el que se encuentra un carácter o una cadena de texto en particular, leyendo de izquierda a derecha (no distingue mayúsculas de minúsculas).", "ad": "es el texto que se desea encontrar. Puede usar ? y * como caracteres comodín; puede usar ~? y ~* para encontrar los caracteres ? y *!es el texto en el que se desea encontrar texto_buscado!es, contando desde la izquierda, el número del carácter en dentro_del_texto desde donde se desea iniciar la búsqueda. Si se omite, se usa 1"}, "SEARCHB": {"a": "(cadena-1; cadena-2; [posición-inicio])", "d": "Devuelve la ubicación de la subcadena especificada en una cadena y está destinada a idiomas que utilizan el juego de caracteres de doble bit (DBCS) como el japonés, chino, coreano, etc.", "ad": "es el texto que se desea encontrar. Puede usar ? y * como caracteres comodín; puede usar ~? y ~* para encontrar los caracteres ? y *!es el texto en el que se desea encontrar cadena-1!es, contando desde la izquierda, el número del carácter en cadena-2 desde donde se desea iniciar la búsqueda. Si se omite, se usa 1"}, "SUBSTITUTE": {"a": "(texto; texto_original; texto_nuevo; [núm_de_repeticiones])", "d": "Reemplaza el texto existente con texto nuevo en una cadena", "ad": "es el texto o la referencia a una celda que contiene texto en el que se desea cambiar caracteres!es el texto que se desea sustituir. Si las mayúsculas y minúsculas del texto_original y el texto a sustituir no coinciden, SUSTITUIR no reemplazará el texto!es el texto con el que se desea reemplazar texto_original!especifica la aparición de texto_original que se desea reemplazar. Si se omite, se reemplazará texto_original en todos los sitios donde aparezca"}, "T": {"a": "(valor)", "d": "Comprueba si un valor es texto y devuelve texto si lo es o comillas dobles (sin texto) si no lo es", "ad": "es el valor que desea comprobar"}, "TEXT": {"a": "(valor; formato)", "d": "Convierte un valor en texto, con un formato de número específico", "ad": "es un valor numérico, una fórmula que evalúa un valor numérico o una referencia a una celda que contiene un valor numérico!es un número en forma de texto del cuadro Categoría, pestaña Número del cuadro de diálogo Formato de celdas"}, "TEXTJOIN": {"a": "(delimitador; ignorar_vacías; texto1; ...)", "d": "Concatena una lista o rango de cadenas de texto mediante una cadena o carácter delimitador", "ad": "Carácter o cadena que se inserta entre cada elemento de texto!Si es VERDADERO (predeterminado), ignora las celdas vacías!son de 1 a 252 cadenas de texto o rangos que se unirán"}, "TRIM": {"a": "(texto)", "d": "Quita todos los espacios del texto excepto los espacios individuales entre palabras", "ad": "es el texto del cual se desea quitar espacios"}, "UNICHAR": {"a": "(número)", "d": "Devuelve el carácter Unicode al que hace referencia el valor numérico dado", "ad": "Es el número Unicode que representa un carácter"}, "UNICODE": {"a": "(texto)", "d": "Devuelve el número (punto de código) que corresponde al primer carácter del texto", "ad": "Es el carácter del que quieres saber el valor Unicode"}, "UPPER": {"a": "(texto)", "d": "Convierte una cadena de texto en letras mayúsculas", "ad": "es el texto que se desea convertir en mayúsculas, una referencia o una cadena de texto"}, "VALUE": {"a": "(texto)", "d": "Convierte un argumento de texto que representa un número en un número", "ad": "es el texto entre comillas o una referencia a una celda que contiene el texto que se desea convertir"}, "AVEDEV": {"a": "(número1; [número2]; ...)", "d": "Devuelve el promedio de las desviaciones absolutas de la media de los puntos de datos. Los argumentos pueden ser números, nombres, matrices o referencias que contienen números", "ad": "son de 1 a 255 argumentos cuyo promedio de las desviaciones absolutas desea calcular"}, "AVERAGE": {"a": "(número1; [número2]; ...)", "d": "Devuelve el promedio (media aritmética) de los argumentos, los cuales pueden ser números, nombres, matrices o referencias que contengan números", "ad": "son entre 1 y 255 argumentos numéricos de los que se desea obtener el promedio"}, "AVERAGEA": {"a": "(valor1; [valor2]; ...)", "d": "Devuelve el promedio (media aritmética) de los argumentos; 0 evalúa el texto como FALSO; 1 como VERDADERO. Los argumentos pueden ser números, nombres, matrices o referencias", "ad": "son de 1 a 255 argumentos de los que desea obtener la media"}, "AVERAGEIF": {"a": "(rango; criterio; [rango_promedio])", "d": "Busca el promedio (media aritmética) de las celdas que cumplen un determinado criterio o condición", "ad": "es el rango de celdas que desea evaluar!es la condición o el criterio en forma de número, expresión o texto que determina qué celdas se utilizarán para buscar el promedio!son las celdas que se van a utilizar para buscar el promedio. Si se omite, se usarán las celdas en el rango"}, "AVERAGEIFS": {"a": "(rango_promedio; rango_criterios; criterio; ...)", "d": "Busca el promedio (media aritmética) de las celdas que cumplen un determinado conjunto de condiciones o criterios", "ad": "son las celdas que se van a utilizar para buscar el promedio.!es el rango de celdas que desea evaluar para la condición determinada!es la condición o el criterio en forma de número, expresión o texto que determina qué celdas se utilizarán para buscar el promedio"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Devuelve la función de densidad de probabilidad beta acumulativa", "ad": "es el valor dentro del intervalo [A, B] con el que se evaluará la función!es un parámetro de la distribución y debe ser mayor que 0!es un parámetro de la distribución y debe ser mayor que 0!es un límite inferior opcional del intervalo de x. Si se omite, se presupone A = 0!es un límite superior opcional del intervalo de x. Si se omite, se presupone B = 1"}, "BETAINV": {"a": "(probabilidad; alfa; beta; [A]; [B])", "d": "Devuelve el inverso de la función de densidad de probabilidad beta acumulativa (DISTR.BETA)", "ad": "es una probabilidad asociada con la distribución beta!es un parámetro de la distribución y debe ser mayor que 0!es un parámetro de la distribución y debe ser mayor que 0!es un límite inferior opcional del intervalo de x. Si se omite, se presupone A = 0!es un límite superior opcional del intervalo de x. Si se omite, B = 1"}, "BETA.DIST": {"a": "(x; alfa; beta; acumulado; [A]; [B])", "d": "Devuelve la función de distribución de probabilidad beta", "ad": "es el valor entre A y B para evaluar la función!es un parámetro de la distribución y debe ser mayor que 0!es un parámetro de la distribución y debe ser mayor que 0!es un valor lógico: para usar la función de distribución acumulativa = VERDADERO; para usar la función de densidad de probabilidad = FALSO!es un límite inferior opcional del intervalo de x. Si se omite, A = 0!es un límite superior opcional del intervalo de x. Si se omite, B = 1"}, "BETA.INV": {"a": "(probabilidad; alfa; beta; [A]; [B])", "d": "Devuelve el inverso de la función de densidad de probabilidad beta acumulativa (DISTR.BETA.N)", "ad": "es una probabilidad asociada a la distribución beta!es un parámetro para la distribución y debe ser mayor que 0!es un parámetro para la distribución y debe ser mayor que 0!es un límite inferior opcional del intervalo de x. Si se omite, A = 0!es un límite superior opcional del intervalo de x. Si se omite, B = 1"}, "BINOMDIST": {"a": "(núm_éxito; ensayos; prob_éxito; acumulado)", "d": "Devuelve la probabilidad de la distribución binomial del término individual", "ad": "es el número de éxitos en los ensayos!es el número de ensayos independientes!es la probabilidad de éxito en cada ensayo!es un valor lógico: para usar la función de distribución acumulativa = VERDADERO; para usar la función de probabilidad bruta = FALSO"}, "BINOM.DIST": {"a": "(núm_éxito; ensayos; prob_éxito; acumulado)", "d": "Devuelve la probabilidad de una variable aleatoria discreta siguiendo una distribución binomial", "ad": "es el número de éxitos en los ensayos!es el número de ensayos independientes!es la probabilidad de éxito en cada ensayo!es un valor lógico: para usar la función de distribución acumulativa = VERDADERO; para usar la función de probabilidad bruta = FALSO"}, "BINOM.DIST.RANGE": {"a": "(ensayos; probabilidad_s; número_s; [número_s2])", "d": "Devuelve la probabilidad de un resultado de prueba que usa una distribución binomial", "ad": "Es la cantidad de pruebas independientes!Es la probabilidad de éxito en cada prueba!Es la cantidad de resultados satisfactorios en las pruebas!Si existe, esta función devuelve la probabilidad de que la cantidad de pruebas satisfactorias se encuentre entre number_s y number_s2"}, "BINOM.INV": {"a": "(ensayos; prob_éxito; alfa)", "d": "Devuelve el menor valor cuya distribución binomial acumulativa es mayor o igual que un valor de criterio", "ad": "es el número de ensayos de Bernoulli!es la probabilidad de éxito en cada ensayo, un número entre 0 y 1 inclusive!es el valor del criterio, un número entre 0 y 1 inclusive"}, "CHIDIST": {"a": "(x; grado<PERSON>_<PERSON>_libertad)", "d": "Devuelve la probabilidad de cola derecha de la distribución chi cuadrado", "ad": "es el valor al que desea evaluar la distribución, un número no negativo!es el número de grados de libertad, un número entre 1 y 10^10, excluido 10^10"}, "CHIINV": {"a": "(probabilidad; grados_de_libertad)", "d": "Devuelve el inverso de una probabilidad dada, de una cola derecha, en una distribución chi cuadrado", "ad": "es una probabilidad asociada con la distribución chi cuadrado, un valor entre 0 y 1 inclusive!es el número de grados de libertad, un número entre 1 y 10^10, excluido 10^10"}, "CHITEST": {"a": "(rango_real; rango_esperado)", "d": "Devuelve la prueba de independencia: el valor de distribución chi cuadrado para la estadística y los grados de libertad apropiados", "ad": "es el rango de datos que contiene observaciones para contrastar frente a los valores esperados!es el rango de datos que contiene el resultado del producto de los totales de filas y columnas con el total general"}, "CHISQ.DIST": {"a": "(x; grados_de_libertad; acumulado)", "d": "Devuelve la probabilidad de cola izquierda de la distribución chi cuadrado", "ad": "es el valor en el que se desea evaluar la distribución, un número no negativo!es el número de grados de libertad, un número entre 1 y 10^10, excluyendo 10^10!es un valor lógico que devuelve la función: función de distribución acumulativa = VERDADERO; función de densidad de probabilidad = FALSO"}, "CHISQ.DIST.RT": {"a": "(x; grados_de_probabilidad)", "d": "Devuelve la probabilidad de cola derecha de la distribución chi cuadrado", "ad": "es el valor en el que se desea evaluar la distribución, un número no negativo!es el número de grados de libertad, un número entre 1 y 10^10, excluyendo 10^10"}, "CHISQ.INV": {"a": "(probabilidad; grados_de_libertad)", "d": "Devuelve el inverso de la probabilidad de cola izquierda de la distribución chi cuadrado", "ad": "es una probabilidad asociada a la distribución chi cuadrado, un valor entre 0 y 1 inclusive!es el número de grados de libertad, un número entre 1 y 10^10, excluyendo 10^10"}, "CHISQ.INV.RT": {"a": "(probabilidad; grados_de_libertad)", "d": "Devuelve el inverso de la probabilidad de cola derecha de la distribución chi cuadrado", "ad": "es una probabilidad asociada a la distribución chi cuadrado, un valor entre 0 y 1 inclusive!es el número de grados de libertad, un número entre 1 y 10^10, excluyendo 10^10"}, "CHISQ.TEST": {"a": "(rango_real; rango_esperado)", "d": "Devuelve la prueba de independencia: el valor de la distribución chi cuadrado para la estadística y los grados adecuados de libertad", "ad": "es el rango de datos que contiene observaciones para contrastar frente a los valores esperados!es el rango de datos que contiene el resultado del producto de los totales de filas y columnas con el total general"}, "CONFIDENCE": {"a": "(alfa; desv_estándar; tama<PERSON>)", "d": "Devuelve el rango de confianza para la media de una población, con una distribución normal", "ad": "es el nivel de significancia empleado para calcular el nivel de confianza, un número mayor que 0 y menor que 1!es la desviación estándar de la población para el rango de datos y se presupone que es conocida. Desv_estándar debe ser mayor que 0!es el tamaño de la muestra"}, "CONFIDENCE.NORM": {"a": "(alfa; desv_estándar; tama<PERSON>)", "d": "Devuelve el rango de confianza para una media de población con una distribución normal", "ad": "es el nivel de significación utilizado para calcular el nivel de confianza, un número mayor que 0 y menor que 1!es la desviación de estándar de población para el rango de datos y se presupone que se conoce el valor. Desv_estándar debe ser mayor que 0!es el tamaño de la muestra"}, "CONFIDENCE.T": {"a": "(alfa; desv_estándar; tama<PERSON>)", "d": "Devuelve el rango de confianza para una media de población con distribución de T de Student", "ad": "es el nivel de significación utilizado para calcular el nivel de confianza, un número mayor que 0 y menor que 1!es la desviación de estándar de población para el rango de datos y se presupone que se conoce el valor. Desv_estándar debe ser mayor que 0!es el tamaño de la muestra"}, "CORREL": {"a": "(matriz1; matriz2)", "d": "Devuelve el coeficiente de correlación de dos conjuntos de datos", "ad": "es un rango de celdas de valores. Los valores deben ser números, nombres, matrices o referencias que contengan números!es un segundo rango de celdas de valores. Los valores deben ser números, nombres, matrices o referencias que contengan números"}, "COUNT": {"a": "(valor1; [valor2]; ...)", "d": "Cuenta el número de celdas de un rango que contienen números", "ad": "son de 1 a 255 argumentos que pueden contener o hacer referencia a distintos tipos de datos, pero solo se cuentan los números"}, "COUNTA": {"a": "(valor1; [valor2]; ...)", "d": "Cuenta el número de celdas no vacías de un rango", "ad": "son de 1 a 255 argumentos que representan los valores y las celdas que desea contar. Los valores pueden ser cualquier tipo de información"}, "COUNTBLANK": {"a": "(rango)", "d": "Cuenta el número de celdas en blanco dentro de un rango especificado", "ad": "es el rango del que se desea contar el número de celdas en blanco"}, "COUNTIF": {"a": "(rango; criterio)", "d": "Cuenta las celdas en el rango que coinciden con la condición dada", "ad": "es el rango del que se desea contar el número de celdas que no están en blanco!es la condición en forma de número, expresión o texto que determina qué celdas deben contarse"}, "COUNTIFS": {"a": "(rango_criterios; criterio; ...)", "d": "Cuenta el número de celdas que cumplen un determinado conjunto de condiciones o criterios", "ad": "es el rango de celdas que desea evaluar para la condición determinada!es la condición en forma de número, expresión o texto que determina qué celdas deben contarse"}, "COVAR": {"a": "(matriz1; matriz2)", "d": "Devuelve la covarianza, que es el promedio de los productos de las desviaciones de los pares de puntos de datos en dos conjuntos de datos", "ad": "es el primer rango de celdas de números enteros formado por números, matrices o referencias que contengan números!es el segundo rango de celdas de números enteros formado por números, matrices o referencias que contengan números"}, "COVARIANCE.P": {"a": "(matriz1; matriz2)", "d": "Devuelve la covarianza de población, el promedio de los productos de las desviaciones para cada pareja de puntos de datos en dos conjuntos de datos", "ad": "es el primer rango de celdas de números enteros y debe ser números, matrices o referencias que contengan números!es el segundo rango de celdas de números enteros y debe ser números, matrices o referencias que contengan números"}, "COVARIANCE.S": {"a": "(matriz1; matriz2)", "d": "Devuelve la covarianza, el promedio de los productos de las desviaciones para cada pareja de puntos de datos en dos conjuntos de datos", "ad": "es el primer rango de celdas de números enteros y debe ser números, matrices o referencias que contengan números!es el segundo rango de celdas de números enteros y debe ser números, matrices o referencias que contengan números"}, "CRITBINOM": {"a": "(ensayos; prob_éxito; alfa)", "d": "Devuelve el menor valor cuya distribución binomial acumulativa es mayor o igual que un valor de criterio", "ad": "es el número de ensayos de Bernoulli!es la probabilidad de éxito en cada ensayo, un número entre 0 y 1 inclusive!es el valor de criterio, un número entre 0 y 1 inclusive"}, "DEVSQ": {"a": "(número1; [número2]; ...)", "d": "Devuelve la suma de los cuadrados de las desviaciones de los puntos de datos con respecto al promedio de la muestra", "ad": "son de 1 a 255 argumentos, una matriz o una referencia matricial para los cuales se desea calcular la DESVIA2"}, "EXPONDIST": {"a": "(x; lambda; acum)", "d": "Devuelve la distribución exponencial", "ad": "es el valor de la función, un número no negativo!es el valor del parámetro, un número positivo!es un valor lógico que devuelve la función: función de distribución acumulativa = VERDADERO; función de densidad de probabilidad = FALSO"}, "EXPON.DIST": {"a": "(x; lambda; acum)", "d": "Devuelve la distribución exponencial", "ad": "es el valor de la función, un número no negativo!es el valor del parámetro, un número positivo!es un valor lógico que devuelve la función: función de distribución acumulativa = VERDADERO; función de densidad de probabilidad = FALSO"}, "FDIST": {"a": "(x; grados_de_libertad1; grados_de_libertad2)", "d": "Devuelve la distribución de probabilidad F (grado de diversidad) (de cola derecha) de dos conjuntos de datos", "ad": "es el valor al que desea evaluar la función, un número no negativo!es el número de grados de libertad del numerador, un número entre 1 y 10^10, excluido 10^10!es el número de grados de libertad del denominador, un número entre 1 y 10^10, excluido 10^10"}, "FINV": {"a": "(probabilidad; grados_de_libertad1; grados_de_libertad2)", "d": "Devuelve el inverso de la distribución de probabilidad F (cola derecha): si p = DISTR.F (x,...), entonces INV.F(p,...) = x", "ad": "es una probabilidad asociada con la función de distribución acumulativa F, un número entre 0 y 1 inclusive!es el número de grados de libertad del numerador, un número entre 1 y 10^10, excluido 10^10!es el número de grados de libertad del denominador, un número entre 1 y 10^10, excluido 10^10"}, "FTEST": {"a": "(matriz1; matriz2)", "d": "Devuelve el resultado de una prueba F, la probabilidad de dos colas de que las varianzas en Matriz1 y Matriz2 no sean significativamente diferentes", "ad": "es la primera matriz o rango de datos formado por números, nombres, matrices o referencias que contengan números (se omiten los que estén en blanco)!es la segunda matriz o rango de datos formado por números, nombres, matrices o referencias que contengan números (se omiten los que estén en blanco)"}, "F.DIST": {"a": "(x; grados_de_libertad1; grados_de_libertad2; acumulado)", "d": "Devuelve la distribución (de cola izquierda) de probabilidad F (grado de diversidad) para dos conjuntos de datos", "ad": "es el valor para evaluar la función, un número no negativo!es el número de grados de libertad del numerador, un número entre 1 y 10^10, excluyendo 10^10!es el número de grados de libertad del denominador, un número entre 1 y 10^10, excluyendo 10^10!es un valor lógico que devuelve la función: función de distribución acumulativa = VERDADERO; función de densidad de probabilidad = FALSO"}, "F.DIST.RT": {"a": "(x; grados_de_libertad1; grados_de_libertad2)", "d": "Devuelve la distribución (de cola derecha) de probabilidad F (grado de diversidad) para dos conjuntos de datos", "ad": "es el valor para evaluar la función, un número no negativo!es el número de grados de libertad del numerador, un número entre 1 y 10^10, excluyendo 10^10!es el número de grados de libertad del denominador, un número entre 1 y 10^10, excluyendo 10^10"}, "F.INV": {"a": "(probabilidad; grados_de_libertad1; grados_de_libertad2)", "d": "Devuelve el inverso de la distribución de probabilidad F (de cola izquierda): si p = DISTR.F(x,...), entonces INV.F(p,...) = x", "ad": "es una probabilidad asociada a la distribución acumulativa F, un número entre 0 y 1 inclusive!es el número de grados de libertad del numerador, un número entre 1 y 10^10, excluido 10^10!es el número de grados de libertad del denominador, un número entre 1 y 10^10, excluido 10^10"}, "F.INV.RT": {"a": "(probabilidad; grados_de_libertad1; grados_de_libertad2)", "d": "Devuelve el inverso de la distribución de probabilidad F (de cola derecha): si p = DISTR.F.CD(x,...), entonces INV.F.CD(p,...) = x", "ad": "es una probabilidad asociada a la distribución acumulativa F, un número entre 0 y 1 inclusive!es el número de grados de libertad del numerador, un número entre 1 y 10^10, excluyendo 10^10!es el número de grados de libertad del denominador, un número entre 1 y 10^10, excluyendo 10^10"}, "F.TEST": {"a": "(matriz1; matriz2)", "d": "Devuelve el resultado de una prueba F, la probabilidad de dos colas de que las varianzas en Matriz 1 y Matriz 2 no sean significativamente diferentes", "ad": "es la primera matriz o rango de datos y puede ser números o nombres, matrices o referencias que contienen números (los blancos no se tienen en cuenta)!es la segunda matriz o rango de datos y puede ser números o nombres, matrices o referencias que contienen números (los blancos no se tienen en cuenta)"}, "FISHER": {"a": "(x)", "d": "Devuelve la transformación Fisher o coeficiente Z", "ad": "es un valor numérico para el que se desea calcular la transformación, un número entre -1 y 1, excluyendo -1 y 1"}, "FISHERINV": {"a": "(y)", "d": "Devuelve la función inversa de la transformación Fisher o coeficiente Z: si y = FISHER (x), entonces PRUEBA.FISHER.INV (y) = x", "ad": "es el valor al que se realizará la transformación inversa"}, "FORECAST": {"a": "(x; conocido_y; conocido_x)", "d": "Calcula o predice un valor futuro en una tendencia lineal usando valores existentes", "ad": "es el punto de datos para el cual desea predecir un valor y debe ser un valor numérico!es la matriz o el rango de datos numéricos dependientes!Es la matriz o el rango de datos numéricos independientes. La varianza de conocido_x no debe ser cero"}, "FORECAST.ETS": {"a": "(fecha_objetivo; valores; escaladetiempo; [estacionalidad]; [finalización_datos]; [agregación])", "d": "Devuelve el valor previsto para una fecha objetivo usando el método de suavizado exponencial.", "ad": "es el punto de datos para el que Spreadsheet Editor predice un valor. Se debe llevar a cabo en el patrón de valores de la escala de tiempo.!es la matriz o el rango de datos numéricos que está prediciendo.!es la matriz o el rango de datos numéricos independiente. Las fechas de la escala de tiempo deben tener un paso coherente entre ellas, que no puede ser cero.!es un valor numérico opcional que indica la longitud del patrón estacional. El valor predeterminado de 1 indica que la estacionalidad se detecta automáticamente.!es un valor opcional para el tratamiento de los valores que faltan. El valor predeterminado 1 reemplaza los valores por interpolación y 0 los reemplaza con ceros.!es un valor numérico opcional para agregar varios valores con la misma marca temporal. Si está en blanco, Spreadsheet Editor calcula la media de los valores"}, "FORECAST.ETS.CONFINT": {"a": "(fecha_objetivo; valores; escaladetiempo; [nivel_confianza]; [estacionalidad]; [finalización_datos]; [agregación])", "d": "Devuelve un rango de confianza para el valor previsto en la fecha objetivo especificada.", "ad": "es el punto de datos para el que Spreadsheet Editor predice un valor. Se debe llevar a cabo en el patrón de valores de la escala de tiempo.!es la matriz o el rango de datos numéricos que está prediciendo.!es la matriz o el rango de datos numéricos independiente. Las fechas de la escala de tiempo deben tener un paso coherente entre ellas, que no puede ser cero.!es un número entre 0 y 1, que indica el nivel de confianza para el rango de confianza calculado. El valor predeterminado es 0,95.!es un valor numérico opcional que indica la longitud del patrón estacional. El valor predeterminado de 1 indica que la estacionalidad se detecta automáticamente.!es un valor opcional para el tratamiento de los valores que faltan. El valor predeterminado 1 reemplaza los valores por interpolación y 0 los reemplaza con ceros.!es un valor numérico opcional para agregar varios valores con la misma marca temporal. Si está en blanco, Spreadsheet Editor calcula la media de los valores"}, "FORECAST.ETS.SEASONALITY": {"a": "(valores; escaladetiempo; [finalización_datos]; [agregación])", "d": "Devuelve la longitud del patrón repetitivo que la aplicación detecta para la serie de tiempo especificado.", "ad": "es la matriz o el rango de datos numéricos que está prediciendo.!es la matriz o el rango de datos numéricos independiente. Las fechas de la escala de tiempo deben tener un paso coherente entre ellas, que no puede ser cero.!es un valor opcional para el tratamiento de los valores que faltan. El valor predeterminado 1 reemplaza los valores que faltan por interpolación y 0 los reemplaza con ceros.!es un valor numérico opcional para agregar varios valores con la misma marca temporal. Si está en blanco, Spreadsheet Editor calcula la media de los valores."}, "FORECAST.ETS.STAT": {"a": "(valores; escaladetiempo; tipo_estadística; [estacionalidad]; [completación_datos]; [agregación])", "d": "Devuelve la estadística requerida de la previsión.", "ad": "es la matriz o el rango de datos numéricos que está prediciendo.!es la matriz o el rango de datos numéricos independiente. Las fechas de la escala de tiempo deben tener un paso coherente entre ellas, que no puede ser cero.!es un número entre 1 y 8 que indica qué estadística Spreadsheet Editor devolverá para la previsión calculada.!es un valor numérico opcional que indica la longitud del patrón estacional. El valor predeterminado 1 indica que la estacionalidad se detecta automáticamente.!es un valor opcional para el tratamiento de los valores que faltan. El valor predeterminado 1 reemplaza los valores por interpolación y 0 los reemplaza con ceros.!es un valor numérico opcional para agregar varios valores con la misma marca temporal. Si está en blanco, Spreadsheet Editor calcula la media de los valores."}, "FORECAST.LINEAR": {"a": "(x; conocido_y; conocido_x)", "d": "Calcula o predice un valor futuro en una tendencia lineal usando valores existentes", "ad": "es el punto de datos para el cual desea predecir un valor y debe ser un valor numérico!es la matriz dependiente o rango de datos numéricos!es el rango de datos numéricos o matriz independiente. La varianza de conocido_x no debe ser cero"}, "FREQUENCY": {"a": "(datos; grupos)", "d": "Calcula la frecuencia con la que ocurre un valor dentro de un rango de valores y devuelve una matriz vertical de números con más de un elemento que grupos", "ad": "es una matriz, o una referencia, de un conjunto de valores de los cuales se desea contar frecuencias. Se omiten espacios en blanco y texto.!es una matriz, o una referencia, a rangos dentro de los cuales se desea agrupar los valores de datos"}, "GAMMA": {"a": "(x)", "d": "Devuelve los valores de la función gamma", "ad": "Es el valor para el que quieres calcular gamma"}, "GAMMADIST": {"a": "(x; alfa; beta; acumulado)", "d": "Devuelve la distribución gamma", "ad": "es el valor al que desea evaluar la distribución, un número no negativo!es un parámetro de la distribución, un número positivo!es un parámetro de la distribución, un número positivo. Si beta = 1, DISTR.GAMMA devuelve la distribución gamma estándar!es un valor lógico: para que devuelva la función de distribución acumulativa = VERDADERO; para que devuelva la función de probabilidad bruta = FALSO u omitido"}, "GAMMA.DIST": {"a": "(x; alfa; beta; acumulado)", "d": "Devuelve la distribución gamma", "ad": "es el valor en el que desea evaluar la distribución, un número no negativo!es un parámetro de la distribución, un número positivo!es un parámetro de la distribución, un número positivo. Si beta = 1, DISTR.GAMMA.N devuelve la distribución gamma estándar!es un valor lógico: para que devuelva la función de distribución acumulativa = VERDADERO; para que devuelva la función de probabilidad bruta = FALSO u omitido"}, "GAMMAINV": {"a": "(prob; alfa; beta)", "d": "Devuelve el inverso de la distribución gamma acumulativa: si p = DISTR.GAMMA(x,...), entonces INV.GAMMA(p,...) = x", "ad": "es la probabilidad asociada con la distribución gamma, un número entre 0 y 1 inclusive!es un parámetro de la distribución, un número positivo!es un parámetro de la distribución, un número positivo. Si beta = 1, INV.GAMMA devuelve el valor inverso de la distribución gamma estándar"}, "GAMMA.INV": {"a": "(probabilidad; alfa; beta)", "d": "Devuelve el inverso de la distribución gamma acumulativa: si p = DISTR.GAMMA.N(x,...), entonces INV.GAMMA(p,...) = x", "ad": "es la probabilidad asociada con la distribución gamma, un número entre 0 y 1 inclusive!es un parámetro de la distribución, un número positivo!es un parámetro de la distribución, un número positivo. Si beta = 1, INV.GAMMA devuelve el inverso de la distribución gamma estándar"}, "GAMMALN": {"a": "(x)", "d": "Devuelve el logaritmo natural de la función gamma", "ad": "es el valor cuya función GAMMA.LN desea calcular, un número positivo"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Devuelve el logaritmo natural de la función gamma", "ad": "es el valor cuya función GAMMA.LN.EXACTO desea calcular, un número positivo"}, "GAUSS": {"a": "(x)", "d": "Devuelve un 0,5 menos que la distribución acumulativa normal estándar", "ad": "Es el valor para el que quieres saber la distribución"}, "GEOMEAN": {"a": "(número1; [número2]; ...)", "d": "Devuelve la media geométrica de una matriz o rango de datos numéricos positivos", "ad": "son de 1 a 255 números, argumentos, matrices o referencias que contienen números cuya media se desea calcular"}, "GROWTH": {"a": "(conocido_y; [conocido_x]; [nueva_matriz_x]; [constante])", "d": "Devuelve números en una tendencia de crecimiento exponencial coincidente con puntos de datos conocidos", "ad": "es el conjunto de valores de Y conocidos en la relación y = b*m^x, una matriz o rango de números positivos!es un conjunto de valores de X opcionales (puede que conocidos) de la relación y = b*m^x, una matriz o rango con el mismo tamaño que los valores conocido_y!son nuevos valores de X de los que se desea que la función CRECIMIENTO devuelva los valores de Y correspondientes!es un valor lógico: la constante b se calcula normalmente si Const = VERDADERO; se establece b = 1 si Const= FALSO u omitida"}, "HARMEAN": {"a": "(número1; [número2]; ...)", "d": "Devuelve la media armónica de un conjunto de números positivos: el recíproco de la media aritmética de los recíprocos", "ad": "son de 1 a 255 números, argumentos, matrices o referencias que contienen números cuya media armónica desea calcular"}, "HYPGEOM.DIST": {"a": "(muestra_éxito; núm_de_muestra; población_éxito; núm_de_población; acumulado)", "d": "Devuelve la distribución hipergeométrica", "ad": "es el número de éxitos en la muestra!es el tamaño de la muestra!es el número de éxitos en la población!es el tamaño de la población!es un valor lógico: para usar la función de distribución acumulativa = VERDADERO; para usar la función de densidad de probabilidad = FALSO"}, "HYPGEOMDIST": {"a": "(muestra_éxito; núm_de_muestra; población_éxito; núm_de_población)", "d": "Devuelve la distribución hipergeométrica", "ad": "es el número de éxitos en la muestra!es el tamaño de la muestra!es el número de éxitos en la población!es el tamaño de la población"}, "INTERCEPT": {"a": "(conocido_y; conocido_x)", "d": "Calcula el punto en el cual una línea formará intersección con el eje Y usando una línea de regresión optimizada trazada a través de los valores conocidos de X e Y", "ad": "es el conjunto de observaciones o datos dependientes y puede ser: números, nombres, matrices o referencias que contengan números!es el conjunto de observaciones o datos independientes formado por: números, nombres, matrices o referencias que contengan números"}, "KURT": {"a": "(número1; [número2]; ...)", "d": "Devuelve la curtosis de un conjunto de datos.", "ad": "son entre 1 y 255 números o nombres, matrices o referencias que contengan números cuya curtosis desea calcular"}, "LARGE": {"a": "(matriz; k)", "d": "Devuelve el valor k-ésimo mayor de un conjunto de datos. <PERSON><PERSON> e<PERSON><PERSON><PERSON>, el trigésimo número más grande", "ad": "es la matriz o rango de datos cuyo valor k-ésimo mayor desea determinar!representa dentro de la matriz o rango de datos la posición, a partir del valor más alto, del dato a devolver"}, "LINEST": {"a": "(conocido_y; [conocido_x]; [const]; [stats])", "d": "Devuelve estadísticas que describen una tendencia lineal que coincide con puntos de datos conocidos, mediante una línea recta usando el método de mínimos cuadrados", "ad": "es el conjunto de valores de Y conocidos en la relación y = mx + b!es un conjunto de valores de X opcionales (puede que conocidos) de la relación y = mx + b!es un valor lógico: la constante b se calcula de forma normal si Const = VERDADERO u omitida; b será igual a 0 si Const = FALSO!es un valor lógico: para que devuelva estadísticas de regresión adicionales = VERDADERO; para que devuelva coeficientes m y la constante b = FALSO u omitida"}, "LOGEST": {"a": "(conocido_y; [conocido_x]; [constante]; [estadística])", "d": "Devuelve estadísticas que describen una curva exponencial, coincidente con puntos de datos conocidos", "ad": "es el conjunto de valores de Y conocidos en la relación y = b*m^x!es un conjunto de valores de X opcionales (puede que conocidos) en la relación y = b*m^x!es un valor lógico: la constante b se calcula normalmente si Const = VERDADERO u omitida; la constante b se considera 1 si Const = FALSO!es un valor lógico: para que devuelva estadísticas de regresión adicionales = VERDADERO; para que devuelva coeficientes m y la constante b = FALSO u omitida"}, "LOGINV": {"a": "(probabilidad; media; desv_estándar)", "d": "Devuelve el inverso de la función de distribución logarítmico-normal acumulativa de x, donde ln(x) se distribuye de forma normal con los parámetros Media y desv_estándar", "ad": "es una probabilidad asociada con la distribución logarítmico-normal, un número entre 0 y 1 inclusive!es la media de ln(x)!es la desviación estándar de In(x), un número positivo"}, "LOGNORM.DIST": {"a": "(x; media; desv_estándar; acumulado)", "d": "Devuelve la distribución logarítmico-normal de x, donde ln(x) se distribuye normalmente con los parámetros media y desv_estándar", "ad": "es el valor para evaluar la función, un número positivo!es la media de ln(x)!es la desviación estándar de ln(x), un número positivo!es un valor lógico: para usar la función de distribución acumulativa = VERDADERO; para usar la función de densidad de probabilidad = FALSO"}, "LOGNORM.INV": {"a": "(probabilidad; media; desv_estándar)", "d": "Devuelve el inverso de la distribución logarítmico-normal de x, donde ln(x) se distribuye de forma normal con los parámetros Media y desv_estándar", "ad": "es una probabilidad asociada con la distribución logarítmico-normal, un número entre 0 y 1 inclusive!es la media de ln(x)!es la desviación estándar de In(x), un número positivo"}, "LOGNORMDIST": {"a": "(x; media; desv_estándar)", "d": "Devuelve la distribución logarítmico-normal acumulativa de x, donde In(x) se distribuye de forma normal con los parámetros Media y desv_estándar", "ad": "es el valor al que desea evaluar la función, un número positivo!es la media de ln(x)!es la desviación estándar de ln(x), un número positivo"}, "MAX": {"a": "(número1; [número2]; ...)", "d": "Devuelve el valor máximo de una lista de valores. Omite los valores lógicos y el texto", "ad": "son de 1 a 255 números, celdas vacías, valores lógicos o números en forma de texto para los cuales desea encontrar el máximo"}, "MAXA": {"a": "(valor1; [valor2]; ...)", "d": "Devuelve el valor máximo de un conjunto de valores. Incluye valores lógicos y texto", "ad": "son de 1 a 255 números, celdas vacías, valores lógicos o números en forma de texto cuyo máximo desea calcular"}, "MAXIFS": {"a": "(rango_max; rango_criterios; criterios; ...)", "d": "Devuelve el valor máximo entre las celdas de un determinado conjunto de condiciones o criterios", "ad": "las celdas en las que se va a determinar el valor máximo!es el rango de celdas que desea evaluar para la condición determinada!es la condición o el criterio en forma de número, expresión o texto, que determina las celdas que se incluirán al determinar el valor máximo"}, "MEDIAN": {"a": "(número1; [número2]; ...)", "d": "Devuelve la mediana o el número central de un conjunto de números", "ad": "son de 1 a 255 números, nombres, matrices o referencias que contienen números, para los cuales desea obtener la mediana"}, "MIN": {"a": "(número1; [número2]; ...)", "d": "Devuelve el valor mínimo de una lista de valores. Omite los valores lógicos y el texto", "ad": "son de 1 a 255 números, celdas vacías, valores lógicos o números en forma de texto, para los cuales desea obtener el mínimo"}, "MINA": {"a": "(valor1; [valor2]; ...)", "d": "Devuelve el valor mínimo de un conjunto de valores. Incluye valores lógicos y texto", "ad": "son de 1 a 255 números, celdas vacías, valores lógicos o números en forma de texto cuyo mínimo desea calcular"}, "MINIFS": {"a": "(rango_min; rango_criterios; criterios; ...)", "d": "Devuelve el valor mínimo entre las celdas de un determinado conjunto de condiciones o criterios", "ad": "las celdas en las que se determina el valor mínimo!es el rango de celdas que desea evaluar para la condición determinada!es la condición o el criterio en forma de número, expresión o texto, que determina las celdas que se incluirán al determinar el valor mínimo"}, "MODE": {"a": "(número1; [número2]; ...)", "d": "Devuelve el valor más frecuente o que más se repite en una matriz o rango de datos", "ad": "son de 1 a 255 números, nombres, matrices o referencias que contienen números cuya moda desea calcular"}, "MODE.MULT": {"a": "(número1; [número2]; ...)", "d": "Devuelve una matriz vertical de los valores más frecuente o repetitivos de una matriz o rango de datos. Para una matriz horizontal, use =TRANSPONER(MODA.VARIOS(número1,número2,...))", "ad": "son de 1 a 255 números, nombres, matrices o referencias que contienen números para los que desea la moda"}, "MODE.SNGL": {"a": "(número1; [número2]; ...)", "d": "Devuelve el valor más frecuente o repetitivo de una matriz o rango de datos", "ad": "son de 1 a 255 números, nombres, matrices o referencias que contienen números para los que desea la moda"}, "NEGBINOM.DIST": {"a": "(núm_fracasos; núm_éxitos; prob_éxito; acumulado)", "d": "Devuelve la distribución binomial negativa, la probabilidad de encontrar núm_fracasos antes que núm_éxito, con probabilidad probabilidad_s de éxito", "ad": "es el número de fracasos!es el número de umbral de éxitos!es la probabilidad de éxito; un número entre 0 y 1!es un valor lógico: para usar la función de distribución acumulativa = VERDADERO; para usar la función de densidad de probabilidad = FALSO"}, "NEGBINOMDIST": {"a": "(núm_fracasos; núm_éxitos; prob_éxito)", "d": "Devuelve la distribución binomial negativa, la probabilidad de encontrar núm_fracasos antes que núm_éxito, con la probabilidad probabilidad_éxito de éxito", "ad": "es el número de fracasos!es el número de umbral de éxitos!es la probabilidad de éxito; un número entre 0 y 1"}, "NORM.DIST": {"a": "(x; media; desv_estándar; acumulado)", "d": "Devuelve la distribución normal para la media  y la desviación estándar especificadas", "ad": "es el valor para el que desea la distribución!es la media aritmética de la distribución!es la desviación estándar de la distribución, un número positivo!es un valor lógico: para usar la función de distribución acumulativa = VERDADERO; para usar la función de densidad de probabilidad = FALSO"}, "NORMDIST": {"a": "(x; media; desv_estándar; acum)", "d": "Devuelve la distribución acumulativa normal para la media y desviación estándar especificadas", "ad": "es el valor cuya distribución desea obtener!es la media aritmética de la distribución!es la desviación estándar de la distribución, un número positivo!es un valor lógico: para usar la función distribución acumulativa = VERDADERO; para usar la función de densidad de probabilidad = FALSO"}, "NORM.INV": {"a": "(probabilidad; media; desv_estándar)", "d": "Devuelve el inverso de la distribución acumulativa normal para la media y desviación estándar especificadas", "ad": "es una probabilidad asociada a la distribución normal, un número entre 0 y 1 inclusive!es la media aritmética de la distribución!es la desviación estándar de la distribución, un número positivo"}, "NORMINV": {"a": "(probabilidad; media; desv_estándar)", "d": "Devuelve el inverso de la distribución acumulativa normal para la media y desviación estándar especificadas", "ad": "es una probabilidad que corresponde a la distribución normal, un número entre 0 y 1 inclusive!es la media aritmética de la distribución!es la desviación estándar de la distribución, un número positivo"}, "NORM.S.DIST": {"a": "(z; acumulado)", "d": "Devuelve la distribución normal estándar (tiene una media de cero y una desviación estándar de uno)", "ad": "es el valor para el que se desea la distribución!es un valor lógico que devuelve la función: función de distribución acumulativa = VERDADERO; función de densidad de probabilidad = FALSO"}, "NORMSDIST": {"a": "(z)", "d": "Devuelve la distribución normal estándar acumulativa. Tiene una media de cero y una desviación estándar de uno", "ad": "es el valor cuya distribución desea obtener"}, "NORM.S.INV": {"a": "(probabilidad)", "d": "Devuelve el inverso de la distribución normal estándar acumulativa. Tiene una media de cero y una desviación estándar de uno", "ad": "es una probabilidad asociada a la distribución normal, un número entre 0 y 1 inclusive"}, "NORMSINV": {"a": "(probabilidad)", "d": "Devuelve el inverso de la distribución normal estándar acumulativa. Tiene una media de cero y una desviación estándar de uno", "ad": "es una probabilidad que corresponde a la distribución normal, un número entre 0 y 1 inclusive"}, "PEARSON": {"a": "(matriz1; matriz2)", "d": "Devuelve el coeficiente de correlación producto o momento r de <PERSON>, r", "ad": "es un conjunto de valores independientes!es un conjunto de valores dependientes"}, "PERCENTILE": {"a": "(matriz; k)", "d": "Devuelve el percentil k-ésimo de los valores de un rango", "ad": "es la matriz o rango de datos que define la posición relativa!es el valor del percentil entre 0 y 1 inclusive"}, "PERCENTILE.EXC": {"a": "(matriz; k)", "d": "Devuelve el percentil k-ésimo de los valores de un rango, donde k está en el rango 0..1, exclusivo", "ad": "es la matriz o rango de datos que define la posición relativa!es el valor del percentil entre 0 y 1, inclusive"}, "PERCENTILE.INC": {"a": "(matriz; k)", "d": "Devuelve el percentil k-ésimo de los valores de un rango, donde k está en el rango 0..1, inclusive", "ad": "es la matriz o rango de datos que define la posición relativa!es el valor del percentil entre 0 y 1, inclusive"}, "PERCENTRANK": {"a": "(matriz; x; [cifra_significativa])", "d": "Devuelve el rango de un valor en un conjunto de datos como porcentaje del conjunto", "ad": "es la matriz o rango de datos con valores numéricos que define la posición relativa!es el valor cuyo rango desea conocer!es un valor opcional que identifica el número de dígitos significativos para el porcentaje devuelto. Si se omite, se usarán tres dígitos (0,xxx%)"}, "PERCENTRANK.EXC": {"a": "(matriz; x; [cifra_significativa])", "d": "Devuelve la jerarquía de un valor en un conjunto de datos como un porcentaje del conjunto de datos como un porcentaje (0..1, exclusivo) del conjunto de datos", "ad": "es la matriz o rango de datos con valores numéricos que define la posición relativa!es el valor del que se desea conocer la jerarquía!es un valor opcional que identifica el número de decimales significativos para el porcentaje devuelto. Si se omite, se usarán tres decimales (0.xxx%)"}, "PERCENTRANK.INC": {"a": "(matriz; x; [cifra_significativa])", "d": "Devuelve la jerarquía de un valor en un conjunto de datos como un porcentaje del conjunto de datos como un porcentaje (0..1, inclusive) del conjunto de datos", "ad": "es la matriz o rango de datos con valores numéricos que define la posición relativa!es el valor del que se desea conocer la jerarquía!es un valor opcional que identifica el número de decimales significativos para el porcentaje devuelto. Si se omite, se usarán tres decimales (0.xxx%)"}, "PERMUT": {"a": "(número; tamaño)", "d": "Devuelve el número de permutaciones para un número determinado de objetos que pueden ser seleccionados de los objetos totales", "ad": "es un número total de objetos!es un número de objetos en cada permutación"}, "PERMUTATIONA": {"a": "(número; número_elegido)", "d": "Devuelve la cantidad de permutaciones de una cantidad determinada de objetos (con repeticiones) que pueden seleccionarse del total de objetos", "ad": "Es la cantidad total de objetos!Es la cantidad de objetos en cada permutación"}, "PHI": {"a": "(x)", "d": "Devuelve el valor de la función de densidad para una distribución normal estándar", "ad": "Es el número para el que quieres saber la densidad de la distribución normal estándar"}, "POISSON": {"a": "(x; media; acumulado)", "d": "Devuelve la distribución de Poisson", "ad": "es el número de eventos!es el valor numérico esperado, un número positivo!es un valor lógico: para usar la probabilidad acumulativa de Poisson VERDADERO; para usar la función de probabilidad bruta de Poisson FALSO"}, "POISSON.DIST": {"a": "(x; media; acumulado)", "d": "Devuelve la distribución de Poisson", "ad": "es el número de eventos!es el valor numérico esperado, un número positivo!es un valor lógico: para usar la probabilidad acumulativa de Poisson = VERDADERO; para usar la función de probabilidad bruta de Poisson = FALSO"}, "PROB": {"a": "(rango_x; rango_probabilidad; límite_inf; [límite_sup])", "d": "Devuelve la probabilidad de que los valores de un rango se encuentren entre dos límites o sean iguales a un límite inferior", "ad": "es el rango de valores numéricos de X con que el que hay probabilidades asociadas!es un conjunto de probabilidades asociado a valores del rango_x, valores entre 0 y 1, excluyendo 0!es el límite inferior del valor para el que desea una probabilidad!es el límite superior opcional del valor para el que desea una probabilidad. Si se omite, PROB devuelve la probabilidad de que los valores rango_x sean iguales a límite_inf"}, "QUARTILE": {"a": "(matriz; cuartil)", "d": "Devuelve el cuartil de un conjunto de datos", "ad": "es la matriz o rango de celdas de valores numéricos cuyo cuartil desea obtener!es un número: valor mínimo = 0; primer cuartil = 1; valor de la mediana = 2; tercer cuartil = 3; valor máximo = 4"}, "QUARTILE.INC": {"a": "(matriz; cuartil)", "d": "Devuelve el cuartil de un conjunto de datos en función de los valores del percentil de 0..1, inclusive", "ad": "es la matriz o rango de celdas de los valores numéricos para el que desea el valor del cuartil!es un número: valor mínimo = 0; primer cuartil = 1; valor de la mediana = 2; tercer cuartil = 3; valor máximo = 4"}, "QUARTILE.EXC": {"a": "(matriz; cuartil)", "d": "Devuelve el cuartil de un conjunto de datos en función de los valores del percentil de 0..1, exclusivo", "ad": "es la matriz o rango de celdas de valores numéricos para el que desea el valor del cuartil!es un número: valor mínimo = 0; primer cuartil = 1; valor de la mediana = 2; tercer cuartil = 3; valor máximo = 4"}, "RANK": {"a": "(número; referencia; [orden])", "d": "Devuelve la jerarquía de un número dentro de una lista: su tamaño depende de los otros valores de la lista", "ad": "es el número cuya jerarquía desea conocer!es una matriz de una lista de números o una referencia a la misma. Omite los valores no numéricos!es un número: si la jerarquía en la lista se ordena de forma descendente = 0 u omitido; si la jerarquía en la lista se ordena de forma ascendente = cualquier valor distinto de cero"}, "RANK.AVG": {"a": "(número; referencia; [orden])", "d": "Devuelve la jerarquía de un número dentro de una lista de números: su tamaño en relación con otros valores de la lista; si más de un valor tiene la misma jerarquía, se devuelve el promedio de jerarquía", "ad": "es el número del que desea encontrar la jerarquía!es una matriz de, o una referencia a, una lista de números. Se omiten los valores no numéricos!es un número: jerarquía en la lista en orden descendente = 0 o se omite; jerarquía en la lista en orden ascendente = cualquier valor distinto de cero"}, "RANK.EQ": {"a": "(número; referencia; [orden])", "d": "Devuelve la jerarquía de un número dentro de una lista de números: su tamaño en relación con otros valores de la lista; si más de un valor tiene la misma jerarquía, se devuelve la jerarquía superior de ese conjunto de valores", "ad": "es el número del que desea encontrar la jerarquía!es una matriz de, o una referencia a, una lista de números. Se omiten los valores no numéricos!es un número: jerarquía en la lista en orden descendente = 0 o se omite; jerarquía en la lista en orden ascendente = cualquier valor distinto de cero"}, "RSQ": {"a": "(conocido_y; conocido_x)", "d": "Devuelve el cuadrado del coeficiente del momento de correlación del producto Pearson de los puntos dados", "ad": "es una matriz o rango de puntos de datos formada por: números, nombres, matrices o referencias que contengan números!es una matriz o rango de puntos de datos formada por: números, nombres, matrices o referencias que contengan números"}, "SKEW": {"a": "(número1; [número2]; ...)", "d": "Devuelve el sesgo de una distribución: una caracterización del grado de asimetría de una distribución alrededor de su media", "ad": "son de 1 a 255 números o nombres, matrices o referencias que contienen números para los cuales desea conocer el sesgo"}, "SKEW.P": {"a": "(número1; [número2]; ...)", "d": "Devuelve el sesgo de una distribución basado en una población: una caracterización del grado de asimetría de una distribución alrededor de su media", "ad": "Son los números del 1 al 254 o nombres, matrices o referencias que contienen números para los que quieres saber el sesgo de población"}, "SLOPE": {"a": "(conocido_y; conocido_x)", "d": "Devuelve la pendiente de una línea de regresión lineal de los puntos dados", "ad": "es una matriz, o rango de celdas de puntos de datos numéricos dependientes, formada por: números, nombres, matrices o referencias que contengan números!es el conjunto de datos independientes que puede estar compuesto por: números, nombres, matrices o referencias que contengan números"}, "SMALL": {"a": "(matriz; k)", "d": "Devuelve el valor k-ésimo menor de un conjunto de datos. <PERSON>r eje<PERSON>lo, el trigésimo número menor", "ad": "es una matriz o rango de datos numéricos cuyo valor k-ésimo menor desea determinar!representa dentro de la matriz o rango de datos la posición, a partir de valor más bajo, del dato a devolver"}, "STANDARDIZE": {"a": "(x; media; desv_estándar)", "d": "Devuelve un valor normalizado de una distribución caracterizada por una media y desviación estándar", "ad": "es el valor que desea normalizar!es la media aritmética de la distribución!es la desviación estándar de la distribución, un número positivo"}, "STDEV": {"a": "(número1; [número2]; ...)", "d": "Calcula la desviación estándar de una muestra (se omiten los valores lógicos y el texto de la muestra)", "ad": "son de 1 a 255 números que corresponden a una muestra de una población y pueden ser números o referencias que contienen números"}, "STDEV.P": {"a": "(número1; [número2]; ...)", "d": "Calcula la desviación estándar en función de la población total proporcionada como argumentos (omite los valores lógicos y el texto)", "ad": "son de 1 a 255 argumentos numéricos que se corresponden con una población y que pueden ser números o referencias que contienen números"}, "STDEV.S": {"a": "(número1; [número2]; ...)", "d": "Calcula la desviación estándar en función de una muestra (omite los valores lógicos y el texto)", "ad": "son de 1 a 255 argumentos numéricos que se corresponden con una muestra de una población y que pueden ser números o referencias que contienen números"}, "STDEVA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula la desviación estándar de una muestra, incluidos valores lógicos y texto. El texto y el valor lógico FALSO tienen el valor 0. El valor lógico VERDADERO tiene el valor 1.", "ad": "son de 1 a 255 argumentos de valores correspondientes a una muestra de una población y pueden ser valores, nombres o referencias a valores"}, "STDEVP": {"a": "(número1; [número2]; ...)", "d": "Calcula la desviación estándar de la población total proporcionada como argumentos (se omiten los valores lógicos y el texto)", "ad": "son de 1 a 255 números que corresponden a una población y pueden ser números o referencias que contienen números"}, "STDEVPA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula la desviación estándar a partir de toda una población, incluidos valores lógicos y texto. El texto y el valor lógico FALSO tienen el valor 0. El valor lógico VERDADERO tiene el valor 1.", "ad": "son de 1 a 255 argumentos de valores correspondientes a una población y pueden ser valores, nombres, matrices o referencias que contengan valores"}, "STEYX": {"a": "(conocido_y; conocido_x)", "d": "Devuelve el error típico del valor de Y previsto para cada X de la regresión", "ad": "es una matriz o rango de puntos de datos dependientes formada por: números, nombres, matrices o referencias que contengan números!es una matriz o rango de puntos de datos independientes formada por: números, nombres, matrices o referencias que contengan números"}, "TDIST": {"a": "(x; grados_de_libertad; colas)", "d": "Devuelve la distribución t de Student ", "ad": "es el valor numérico al que se va a evaluar la distribución!es un entero que indica el número de grados de libertad que caracteriza la distribución!especifica el número de colas de la distribución que se debe devolver: distribución de una cola = 1; distribución de dos colas = 2"}, "TINV": {"a": "(probabilidad; grados_de_libertad)", "d": "Devuelve el inverso de dos colas de la distribución t de Student", "ad": "es la probabilidad asociada con la distribución t de Student de dos colas, un número entre 0 y 1 inclusive!es un número entero positivo que indica el número de grados de libertad que caracteriza la distribución"}, "T.DIST": {"a": "(x; grados_de_libertad; acumulado)", "d": "Devuelve la distribución t de Student de cola izquierda", "ad": "es el valor numérico para evaluar la distribución!es un entero que indica el número de grados de libertad que caracterizan la distribución!es un valor lógico: para usar la función de distribución acumulativa = VERDADERO; para usar la función de densidad de probabilidad = FALSO"}, "T.DIST.2T": {"a": "(x; grado<PERSON>_<PERSON>_libertad)", "d": "Devuelve la distribución t de Student de dos colas", "ad": "es el valor numérico para evaluar la distribución!es un entero que indica el número de grados de libertad que caracterizan la distribución"}, "T.DIST.RT": {"a": "(x; grado<PERSON>_<PERSON>_libertad)", "d": "Devuelve la distribución t de Student de cola derecha", "ad": "es el valor numérico para evaluar la distribución!es un entero que indica el número de grados de libertad que caracterizan la distribución"}, "T.INV": {"a": "(probabilidad; grados_de_libertad)", "d": "Devuelve el inverso de cola izquierda de la distribución t de Student", "ad": "es la probabilidad asociada a la distribución t de Student de dos colas, un número entre 0 y 1 inclusive!es un entero positivo que indica el número de grados de libertad que caracteriza la distribución"}, "T.INV.2T": {"a": "(probabilidad; grados_de_libertad)", "d": "Devuelve el inverso de dos colas de la distribución t de Student", "ad": "es la probabilidad asociada a la distribución t Student de dos colas, un número entre 0 y 1 inclusive!es un entero positivo que indica el número de grados de libertad que caracteriza la distribución"}, "T.TEST": {"a": "(matriz1; matriz2; colas; tipo)", "d": "Devuelve la probabilidad asociada con la prueba t de Student", "ad": "es el primer conjunto de datos!es el segundo conjunto de datos!especifica el número de colas de distribución para devolver: una cola de distribución = 1; dos colas de distribución = 2!es el tipo de prueba t: pareado = 1, dos muestras de igual varianza = 2, dos muestras de varianza distinta = 3"}, "TREND": {"a": "(conocido_y; [conocido_x]; [nueva_matriz_x]; [constante])", "d": "Devuelve números en una tendencia lineal que coincide con puntos de datos conocidos, usando el método de mínimos cuadrados", "ad": "es el conjunto de valores de Y conocidos en la relación y = mx + b!es un conjunto de valores de X opcionales (puede que conocidos) de la relación y = mx + b, una matriz del mismo tamaño que conocido_y!son nuevos valores de X para los cuales se desea que TENDENCIA devuelva los valores de Y correspondientes!es un valor lógico: la constante b se calcula normalmente si Const = VERDADERO u omitido; se establece b igual a 0 si Const = FALSO"}, "TRIMMEAN": {"a": "(matriz; porcentaje)", "d": "Devuelve la media de la porción interior de un conjunto de valores de datos", "ad": "es la matriz o rango de valores que desea acotar y calcular su media!es el número fraccionario de puntos de datos que se excluyen del extremo superior e inferior del conjunto de datos"}, "TTEST": {"a": "(matriz1; matriz2; colas; tipo)", "d": "Devuelve la probabilidad asociada con la prueba t de Student", "ad": "es el primer conjunto de datos!es el segundo conjunto de datos!especifica el número de colas de distribución que se va a devolver: distribución de una cola = 1; distribución de dos colas = 2!es el tipo de prueba t: pareada = 1, varianza igual de dos muestras (homoscedástica) = 2, varianza desigual de dos muestras = 3"}, "VAR": {"a": "(número1; [número2]; ...)", "d": "Calcula la varianza de una muestra (se omiten los valores lógicos y el texto de la muestra)", "ad": "son de 1 a 255 argumentos numéricos que corresponden a una muestra de una población"}, "VAR.P": {"a": "(número1; [número2]; ...)", "d": "Calcula la varianza en función de la población total (omite los valores lógicos y el texto)", "ad": "son de 1 a 255 argumentos numéricos que se corresponden con una población"}, "VAR.S": {"a": "(número1; [número2]; ...)", "d": "Calcula la varianza en función de una muestra (omite los valores lógicos y el texto)", "ad": "son de 1 a 255 argumentos numéricos que se corresponden con una muestra de una población"}, "VARA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula la varianza de una muestra, incluyendo valores lógicos y texto. Los valores lógicos y el texto con valor FALSO tiene valor asignado 0, los de valor lógico VERDADERO tienen valor 1", "ad": "son de 1 a 255 argumentos de valores correspondientes a una muestra de una población"}, "VARP": {"a": "(número1; [número2]; ...)", "d": "Calcula la varianza de la población total (se omiten los valores lógicos y el texto de la población)", "ad": "son de 1 a 255 argumentos numéricos que corresponden a una población"}, "VARPA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula la varianza de la población total, incluyendo valores lógicos y texto. Los valores lógicos y el texto con valor FALSO tienen el valor asignado 0, los de valor lógico VERDADERO tienen valor 1", "ad": "son de 1 a 255 argumentos de valores correspondientes a una población"}, "WEIBULL": {"a": "(x; alfa; beta; acumulado)", "d": "Devuelve la probabilidad de <PERSON>", "ad": "es el valor al que desea evaluar la función, un número no negativo!es un parámetro de la distribución, un número positivo!es un parámetro de la distribución, un número positivo!es un valor lógico: para usar la función de distribución acumulativa = VERDADERO; para usar la función de probabilidad bruta = FALSO"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; acumulado)", "d": "Devuelve la probabilidad de una variable aleatoria siguiendo una distribución de Weibull", "ad": "es el valor al que desea evaluar la función, un número no negativo!es un parámetro de la distribución, un número positivo!es un parámetro de la distribución, un número positivo!es un valor lógico: para usar la función de distribución acumulativa = VERDADERO; para usar la función de probabilidad bruta = FALSO"}, "Z.TEST": {"a": "(matriz; x; [sigma])", "d": "Devuelve el valor P de una cola de una prueba z", "ad": "es la matriz o rango de datos con los que se ha de contrastar X!es el valor para comprobar!es la desviación estándar (conocida) de la población. Si se omite, se usará la desviación estándar de muestra"}, "ZTEST": {"a": "(matriz; x; [sigma])", "d": "Devuelve el valor P de una cola de una prueba z", "ad": "es la matriz o rango de datos frente a los que se probará X!es el valor que se probará!es la desviación estándar (conocida) de la población. Si se omite, se usará la desviación estándar de muestra"}, "ACCRINT": {"a": "(emisión; primer_interés; liquidación; tasa; par; frecuencia; [base]; [método_calc])", "d": "Devuelve el interés devengado de un valor bursátil que paga intereses periódicos.", "ad": "es la fecha de emisión del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la fecha de primer interés del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la fecha de liquidación del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la tasa de cupón anual del valor bursátil!es el valor de paridad del valor bursátil!es el número de cupones pagaderos por año!determina en qué tipo de base deben ser contados los días!es un valor lógico: para el interés acumulado desde la fecha de emisión = VERDADERO u omitido; para calcular desde la fecha de pago de cupones más reciente = FALSO"}, "ACCRINTM": {"a": "(emisión; liquidación; tasa; par; [base])", "d": "Devuelve el interés devengado para un valor bursátil que paga intereses al vencimiento", "ad": "es la fecha de emisión del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es la tasa del cupón anual del valor burs<PERSON>til!es el valor de paridad del valor burs<PERSON>til!determina en qué tipo de base deben ser contados los días"}, "AMORDEGRC": {"a": "(costo; fecha_compra; primer_período; valor_residual; período; tasa; [base])", "d": "Devuelve la depreciación lineal prorrateada de un activo para cada período contable especificado.", "ad": "es el costo de un activo!es la fecha de compra de un activo!es la fecha del final del primer período!es el valor residual al final de la vida de un activo.!es el período!es la tasa de depreciación!base_año: 0 para un año de 360 días, 1 para real, 3 para años de 365 días."}, "AMORLINC": {"a": "(costo; fecha_compra; primer_período; valor_residual; período; tasa; [base])", "d": "Devuelve la depreciación lineal prorrateada de un activo para cada período contable especificado.", "ad": "es el costo de un activo!es la fecha de compra de un activo!es la fecha del final del primer período!es el valor residual al final de la vida de un activo.!es el período!es la tasa de depreciación!base_año: 0 para un año de 360 días, 1 para real, 3 para años de 365 días."}, "COUPDAYBS": {"a": "(liquidación; vencimiento; frecuencia; [base])", "d": "Devuelve el número de días del inicio del período nominal hasta la fecha de liquidación", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es el número de cupones pagaderos por año!determina en qué tipo de base deben ser contados los días"}, "COUPDAYS": {"a": "(liquidación; vencimiento; frecuencia; [base])", "d": "Devuelve el número de días en el período nominal que contiene la fecha de liquidación", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es el número de pagos nominales por año!determina en qué tipo de base deben ser contados los días"}, "COUPDAYSNC": {"a": "(liquidación; vencimiento; frecuencia; [base])", "d": "Devuelve el número de días de la fecha de liquidación hasta la siguiente fecha nominal", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es el número de cupones pagaderos por año!determina en qué tipo de base deben ser contados los días"}, "COUPNCD": {"a": "(liquidación; vencimiento; frecuencia; [base])", "d": "Devuelve la próxima fecha nominal después de la fecha de liquidación", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es el número de pagos nominales por año!determina en qué tipo de base deben ser contados los días"}, "COUPNUM": {"a": "(liquidación; vencimiento; frecuencia; [base])", "d": "Devuelve el número de cupones pagables entre la fecha de liquidación y la fecha de vencimiento", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es el número de cupones pagaderos por año!determina en qué tipo de base deben ser contados los días"}, "COUPPCD": {"a": "(liquidación; vencimiento; frecuencia; [base])", "d": "Devuelve la fecha de cupón anterior antes de la fecha de liquidación", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresado como un número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es el número de pagos de cupón por año!determina en qué tipo de base deben ser contados los días"}, "CUMIPMT": {"a": "(tasa; nper; va; período_inicial; período_final; tipo)", "d": "Devuelve el pago de intereses acumulativo entre dos períodos", "ad": "es la tasa de interés!es el número total de períodos de pago!es el valor actual!es el primer período del cálculo!es el último período del cálculo!es cuando vencen los pagos"}, "CUMPRINC": {"a": "(tasa; nper; va; período_inicial; período_final; tipo)", "d": "Devuelve el principal acumulado pagado de un préstamo entre dos períodos", "ad": "es la tasa de interés!es el número total de períodos de pago!es el valor actual!es el primer período del cálculo!es el último período del cálculo!es cuando vencen los pagos"}, "DB": {"a": "(costo; valor_residual; vida; período; [mes])", "d": "Devuelve la depreciación de un activo durante un período específico usando el método de depreciación de saldo fijo", "ad": "es el costo inicial del activo!es el valor residual al final de la vida de un activo!es el número de períodos durante los que se produce la depreciación del activo (también conocido como vida útil del activo)!es el período del que se desea calcular la depreciación. El período debe usar las mismas unidades que las usadas en Vida!es el número de meses del primer año; si se omite, se asume que es 12"}, "DDB": {"a": "(costo; valor_residual; vida; período; [factor])", "d": "Devuelve la depreciación de un activo en un período específico mediante el método de depreciación por doble disminución de saldo u otro método que se especifique", "ad": "es el costo inicial del activo!es el valor residual al final de la vida de un bien!es el número de períodos durante los que se produce la depreciación del activo (algunas veces se conoce como vida útil del activo)!es el período para el que se desea calcular la depreciación. El Período debe usar las mismas unidades que las utilizadas en Vida!es la tasa a la que disminuye el saldo. Si se omite un factor, se asumirá el  valor 2 (método de disminución del saldo doble)"}, "DISC": {"a": "(liquidación; vencimiento; pr; amortización; [base])", "d": "Devuelve la tasa de descuento del valor bursátil", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es el precio del valor burs<PERSON>til por un valor nominal de 100 $!es el rendimiento de amortización por cada 100 $ de valor nominal!determina en qué tipo de base deben ser contados los días"}, "DOLLARDE": {"a": "(dólar_fraccional; fracción)", "d": "Convierte un precio en dólares expresado como fracción en un precio en dólares expresado como número decimal", "ad": "es un número expresado como fracción!es el entero que se debe usar en el denominador de la fracción"}, "DOLLARFR": {"a": "(dólar_decimal; fracción)", "d": "Convierte un precio en dólares expresado como número decimal en un precio en dólares expresado como fracción", "ad": "es un número decimal!es el entero que debe utilizar en el denominador de una fracción"}, "DURATION": {"a": "(liquidación; vencimiento; cupón; rdto; frecuencia; [base])", "d": "Devuelve la duración anual de un valor bursátil con pagos de interés periódicos", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresado como un número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresado como número de fecha de serie!es la tasa de cupón anual del valor burs<PERSON>til!es el rendimiento anual del valor burs<PERSON>til!es el número de cupones pagaderos por año!determina en qué tipo de base deben ser contados los días"}, "EFFECT": {"a": "(tasa_nominal; núm_per_año)", "d": "Devuelve la tasa de interés anual efectiva", "ad": "es la tasa de interés nominal!es el número de períodos por año"}, "FV": {"a": "(tasa; nper; pago; [va]; [tipo])", "d": "Devuelve el valor futuro de una inversión basado en pagos periódicos y constantes, y una tasa de interés también constante.", "ad": "es la tasa de interés por período. Por ejemplo, use 6%/4 para pagos trimestrales al 6% de TPA!es el número total de pagos de una inversión!es el pago efectuado cada período; no puede cambiar durante la vigencia de la inversión!es el valor actual o la suma total del valor de una serie de pagos futuros. Si se omite, VA = 0!es el número 0 o 1 e indica cuándo vencen los pagos: pago al comienzo del período =1; pago al final del período = 0 u omitido"}, "FVSCHEDULE": {"a": "(principal; programación)", "d": "Devuelve el valor futuro de un principal inicial después de aplicar una serie de tasas de interés compuestas", "ad": "es el valor presente!es una matriz de tasas de interés para aplicar"}, "INTRATE": {"a": "(liquidación; vencimiento; inversión; amortización; [base])", "d": "Devuelve la tasa de interés para la inversión total en un valor bursátil", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresado como un número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresado como un número de fecha de serie!es la cantidad invertida en el valor burs<PERSON><PERSON>!es la cantidad recibida al vencimiento!determina en qué tipo de base deben contarse los días"}, "IPMT": {"a": "(tasa; período; nper; va; [vf]; [tipo])", "d": "Devuelve el interés pagado por una inversión durante un período determinado, basado en pagos periódicos y constantes y una tasa de interés constante", "ad": "es la tasa de interés por período. Por ejemplo, use 6%/4 para pagos trimestrales al 6% de TPA!es el período para el que se desea encontrar el interés, que deberá estar en el rango de 1 a Nper!es el número total de períodos de pago en una inversión!es el valor actual o la suma total de una serie de pagos futuros!es el valor futuro o saldo en efectivo que se desea obtener después de efectuar el último pago. Si se omite, se asume VA = 0!es un valor lógico que representa cuándo vencen los pagos: si se omite o está al final del período = 0, al comienzo del período = 1"}, "IRR": {"a": "(valores; [estimar])", "d": "Devuelve la tasa interna de retorno de una inversión para una serie de valores en efectivo", "ad": "es una matriz o referencia a celdas que contengan los números para los cuales se desea calcular la tasa interna de retorno!es un número que el usuario estima que se aproximará al resultado de TIR; se asume 0,1 (10%) si se omite"}, "ISPMT": {"a": "(tasa; período; nper; va)", "d": "Devuelve el interés de un préstamo de pagos directos", "ad": "es la tasa de interés por período. Por ejemplo, use 6%/4 para pagos trimestrales al 6% TPA!es el período para el que desea averiguar el interés!es el número total de períodos de pago en una anualidad!es la suma total del valor de una serie de pagos futuros"}, "MDURATION": {"a": "(liquidación; vencimiento; cupón; rdto; frecuencia; [base])", "d": "Devuelve la duración modificada de Macauley para un valor bursátil con un valor nominal asumido de 100 $", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la tasa de cupón anual del valor burs<PERSON>til!es el rendimiento anual del valor burs<PERSON>til!es el número de cupones pagaderos por año!determina en qué tipo de base deben ser contados los días"}, "MIRR": {"a": "(valores; tasa_financiamiento; tasa_reinversión)", "d": "Devuelve la tasa interna de retorno para una serie de flujos de efectivo periódicos, considerando costo de la inversión e interés al volver a invertir el efectivo", "ad": "es una matriz o referencia a celdas que contienen números que representan una serie de pagos (negativos) y entradas (positivas) realizados en períodos constantes!es la tasa de interés que se paga del dinero utilizado en flujos de efectivo!es la tasa de interés que se recibe de los flujos de efectivo a medida que se vuelven a invertir"}, "NOMINAL": {"a": "(tasa_efect; núm_per_año)", "d": "Devuelve la tasa de interés nominal anual", "ad": "es la tasa de interés efectiva!es el número de períodos compuestos por año"}, "NPER": {"a": "(tasa; pago; va; [vf]; [tipo])", "d": "Devuelve el número de pagos de una inversión, basado en pagos constantes y periódicos y una tasa de interés constante", "ad": "es la tasa de interés por período. Por ejemplo, use 6%/4 para pagos trimestrales al 6% TPA!es el pago efectuado en cada período; no puede cambiar durante la vigencia de la inversión!es el valor actual o el valor de la suma total de una serie de pagos futuros!es el valor futuro o saldo en efectivo que se desea lograr después de efectuar el último pago. Si se omite, se usa cero!es un valor lógico: para pago al comienzo del período = 1; para pago al final del período = 0 u omitido"}, "NPV": {"a": "(tasa; valor1; [valor2]; ...)", "d": "Devuelve el valor neto presente de una inversión a partir de una tasa de descuento y una serie de pagos futuros (valores negativos) y entradas (valores positivos)", "ad": "es la tasa de descuento durante un período!son de 1 a 254 pagos e ingresos, igualmente espaciados y que tienen lugar al final de cada período"}, "ODDFPRICE": {"a": "(liquidación; vencimiento; emisión; primer_cupón; tasa; rdto; amortización; frecuencia; [base])", "d": "Devuelve el precio de un valor nominal de 100 $ de un valor bursátil con un período inicial impar", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la fecha de emisión del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la fecha del primer cupón del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la tasa de interés del valor bursátil!es el rendimiento anual del valor bursátil!es el valor de amortización del valor bursátil de un valor nominal de 100 $!es el número de pagos de cupón por año!determina en qué tipo de base deben ser contados los días"}, "ODDFYIELD": {"a": "(liquidación; vencimiento; emisión; primer_cupón; tasa; pr; amortización; frecuencia; [base])", "d": "Devuelve el rendimiento de un valor bursátil con un primer período impar", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la fecha de emisión del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la fecha del primer cupón del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la tasa de interés del valor bursátil!es el precio del valor bursátil!es el valor de amortización del valor bursátil de un valor nominal de 100 $!es el número de cupones pagaderos por año!determina en qué tipo de base deben ser contados los días"}, "ODDLPRICE": {"a": "(liquidación; vencimiento; último_interés; tasa; rdto; amortización; frecuencia; [base])", "d": "Devuelve el precio por un valor nominal de 100 $ de un valor bursátil con un período final impar", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la fecha de cupón del último valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es la tasa de interés del valor bursátil!es el rendimiento anual del valor bursátil!es el valor de amortización del valor bursátil de un valor nominal de 100 $!es el número de cupones pagaderos por año!determina en qué tipo de base deben ser contados los días"}, "ODDLYIELD": {"a": "(liquidación; vencimiento; último_interés; tasa; pr; amortización; frecuencia; [base])", "d": "Devuelve la amortización de un valor bursátil con un período final impar", "ad": "es la fecha de liquidación de seguridad, expresada como un número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es la fecha de cupón del último valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es la tasa de interés del valor bursátil!es el precio del valor bursátil!es el valor de amortización del valor bursátil de un valor nominal de 100 $!es el número de cupones pagaderos por año!determina en qué tipo de base deben ser contados los días"}, "PDURATION": {"a": "(tasa; va; vf)", "d": "Devuelve la cantidad de períodos necesarios para que una inversión alcance un valor especificado", "ad": "Es la tasa de interés por período.!Es el valor actual de la inversión!Es el valor futuro deseado de la inversión"}, "PMT": {"a": "(tasa; nper; va; [vf]; [tipo])", "d": "Calcula el pago de un préstamo basado en pagos y tasa de interés constantes", "ad": "es la tasa de interés por período del préstamo. Por ejemplo, use 6%/4 para pagos trimestrales al 6% TPA!es el número total de pagos del préstamo!es el valor actual: la cantidad total de una serie de pagos futuros!es el valor futuro o saldo en efectivo que se desea lograr después de efectuar el último pago y que se asume 0 (cero) si se omite!es un valor lógico: para pago al comienzo del período = 1; para pago al final del período = 0 u omitido"}, "PPMT": {"a": "(tasa; período; nper; va; [vf]; [tipo])", "d": "Devuelve el pago del capital de una inversión determinada, basado en pagos constantes y periódicos, y una tasa de interés constante", "ad": "es la tasa de interés por período. Por ejemplo, use 6%/4 para pagos trimestrales al 6% de TPA!especifica el período y deberá encontrarse en el rango comprendido entre 1 y nper!es el número total de períodos de pago en una inversión!es el valor actual: la cantidad total de una serie de pagos futuros!es el valor futuro o saldo en efectivo que se desea lograr después de efectuar el último pago!es un valor lógico: para pago al comienzo del período = 1; para pago al final del período = 0 u omitido"}, "PRICE": {"a": "(liquidación; vencimiento; tasa; rdto; amortización; frecuencia; [base])", "d": "Devuelve el precio por 100 $ de valor nominal de un valor bursátil que paga una tasa de interés periódica", "ad": "es la fecha liquidación del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es la tasa del interés nominal del valor burs<PERSON>til!es el rendimiento anual del valor burs<PERSON>til!es la amortización del valor bursátil por cada 100 $ de valor nominal!es el número de pagos contra presentación de cupón por año!determina en qué tipo de base deben ser contados los días"}, "PRICEDISC": {"a": "(liquidación; vencimiento; descuento; amortización; [base])", "d": "Devuelve el precio por 100 $ de un valor nominal de un valor bursátil con descuento", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es el tipo de descuento del valor burs<PERSON><PERSON>!es el valor de amortización por cada 100 $ de valor nominal!determina en qué tipo de base deben contarse los días"}, "PRICEMAT": {"a": "(liquidación; vencimiento; emisión; tasa; rdto; [base])", "d": "Devuelve el precio por 100 $ de un valor nominal que genera intereses al vencimiento", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es la fecha de emisión del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es la tasa de interés del valor bursátil de emisión!es el rendimiento anual del valor bursátil!determina en qué tipo de base deben contarse los días"}, "PV": {"a": "(tasa; nper; pago; [vf]; [tipo])", "d": "Devuelve el valor presente de una inversión: la suma total del valor actual de una serie de pagos futuros", "ad": "es la tasa de interés por período. Por ejemplo, use 6%/4 para pagos trimestrales al 6% TPA!es el número total de períodos de pago en una inversión!es el pago efectuado en cada período y no puede cambiar durante la vigencia de la inversión!es el valor futuro o saldo en efectivo que se desea lograr después de efectuar el último pago!es un valor lógico: para pago al comienzo del período = 1; para pago al final del período = 0 u omitido"}, "RATE": {"a": "(nper; pago; va; [vf]; [tipo]; [estimar])", "d": "Devuelve la tasa de interés por período de un préstamo o una inversión. Por ejemplo, use 6%/4 para pagos trimestrales al 6% TPA", "ad": "es el número total de períodos de pago de un préstamo o una inversión!es el pago efectuado en cada período y no puede cambiar durante la vigencia del préstamo o la inversión!es el valor actual: la cantidad total de una serie de pagos futuros!es el valor futuro o saldo en efectivo que se desea lograr después de efectuar el último pago. Si se omite, se usa VA = 0!es un valor lógico: para pago al comienzo del período = 1; para pago al final del período = 0 u omitido!es su estimación de la tasa de interés; si se omite Estimar = 0,1 (10%)"}, "RECEIVED": {"a": "(liquidación; vencimiento; inversión; descuento; [base])", "d": "Devuelve la cantidad recibida al vencimiento para un valor bursátil completamente invertido", "ad": "es la fecha de vencimiento del valor burs<PERSON>til expresada como número de serie!es la fecha de liquidación del valor burs<PERSON><PERSON>, expresado como número de fecha de serie!es la cantidad invertida en el valor burs<PERSON>til!es la tasa de descuento del valor burs<PERSON>til!determina en qué tipo de base deben contarse los días"}, "RRI": {"a": "(nper; va; vf)", "d": "Devuelve una tasa de interés equivalente para el crecimiento de una inversión", "ad": "Es la cantidad de períodos de la inversión!Es el valor actual de la inversión!Es el valor futuro de la inversión"}, "SLN": {"a": "(costo; valor_residual; vida)", "d": "Devuelve la depreciación por método directo de un activo en un período dado", "ad": "es el costo inicial del bien!es el valor remanente al final de la vida de un activo!es el número de períodos durante los que se produce la depreciación del activo (algunas veces se conoce como vida útil del activo)"}, "SYD": {"a": "(costo; valor_residual; vida; período)", "d": "Devuelve la depreciación por método de anualidades de un activo durante un período específico", "ad": "es el costo inicial del bien!es el valor remanente al final de la vida de un activo!es el número de períodos durante los que se produce la depreciación del activo (algunas veces se conoce como vida útil del activo)!es el período y se deben utilizar las mismas unidades que Vida"}, "TBILLEQ": {"a": "(liquidación; vencimiento; descuento)", "d": "Devuelve el rendimiento para un bono equivalente a una letra de tesorería", "ad": "es la fecha de liquidación de la letra de tesorería, expresada como un número de fecha de serie!es la fecha de vencimiento de la letra de tesorería, expresada como un número de fecha de serie!es la tasa de descuento de la letra de tesorería"}, "TBILLPRICE": {"a": "(liquidación; vencimiento; descuento)", "d": "Devuelve el precio de un valor nominal de 100 $ para una letra de tesorería", "ad": "es la fecha de liquidación de la letra de tesorería, expresada como un número de fecha de serie!es la fecha de vencimiento de la letra de tesorería, expresada con un número de fecha de serie!es la tasa de descuento de la letra de tesorería"}, "TBILLYIELD": {"a": "(liquidación; vencimiento; pr)", "d": "Devuelve el rendimiento de una letra de tesorería", "ad": "es la fecha de liquidación de la letra de tesorería, expresada como un número de fecha de serie!es la fecha de vencimiento de la letra de tesorería, expresada como un número de fecha de serie!es el precio de la letra de tesorería de un valor nominal de 100 $"}, "VDB": {"a": "(costo; valor_residual; vida; período_inicial; período_final; [factor]; [sin_cambios])", "d": "Devuelve la depreciación de un activo para cualquier período especificado, incluyendo períodos parciales, usando el método de depreciación por doble disminución del saldo u otro método que especifique", "ad": "es el costo inicial del activo!es el valor remanente al final de la vida de un activo!es el número de períodos durante los que se produce la depreciación del activo (algunas veces se conoce como vida útil del bien)!es el período inicial para el se desea calcular la depreciación, en las mismas unidades que Vida!es el período final para el se desea calcular la depreciación, en las mismas unidades que Vida!es la tasa a la que disminuye el saldo; si se omite, se asume 2 (depreciación por doble disminución)!cambia al método directo de depreciación cuando la depreciación es mayor que el saldo en disminución = FALSO o bien se omite; si no se desea que cambie = VERDADERO"}, "XIRR": {"a": "(valores; fechas; [estimar])", "d": "Devuelve la tasa interna de retorno para un flujo de caja que no es necesariamente periódico", "ad": "es un flujo de caja, no necesariamente periódico, que corresponde al plan de fechas de pagos!son las flechas del plan de pagos que corresponde al flujo de caja, no necesariamente periódico!es un número que estima que es aproximado al resultado de TIR.NO.PER"}, "XNPV": {"a": "(tasa; valores; fechas)", "d": "Devuelve el valor neto actual para un flujo de caja que no es necesariamente periódico", "ad": "es la tasa de descuento para aplicar el efectivo!es un flujo de caja, no necesariamente periódico, que corresponde al plan de fechas de pagos!son las fechas del plan de pago que corresponde al flujo de caja, no necesariamente periódico"}, "YIELD": {"a": "(liquidación; vencimiento; tasa; pr; amortización; frecuencia; [base])", "d": "Devuelve el rendimiento de un valor bursátil que obtiene intereses periódicos", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es el interés nominal anual del valor burs<PERSON>til!es el precio del valor bursátil por un valor nominal de 100 $!es la amortización del valor bursátil por cada 100 $ de valor nominal!es el número de cupones pagaderos por año!determina en qué tipo de base deben ser contados los días"}, "YIELDDISC": {"a": "(liquidación; vencimiento; pr; amortización; [base])", "d": "Devuelve el rendimiento anual para el valor bursátil con descuento. Por ejemplo, una letra de tesorería", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresada como número de fecha de serie!es el precio del valor bursátil por un valor nominal de 100 $!es la amortización del valor bursátil por cada 100 $ de valor nominal!determina en qué tipo de base deben ser contados los días"}, "YIELDMAT": {"a": "(liquidación; vencimiento; emisión; tasa; pr; [base])", "d": "Devuelve el interés anual de un valor que genera intereses al vencimiento", "ad": "es la fecha de liquidación del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es la fecha de vencimiento del valor burs<PERSON><PERSON>, expresado como un número de fecha de serie!es la fecha de emisión del valor burs<PERSON><PERSON>, expresada como un número de fecha de serie!es la tasa de interés del valor bursátil en la fecha de emisión!es el precio del valor bursátil por un valor nominal de 100 $!determina en qué tipo de base deben ser contados los días"}, "ABS": {"a": "(número)", "d": "Devuelve el valor absoluto de un número, es decir, un número sin signo", "ad": "es el número real del que se desea obtener el valor absoluto"}, "ACOS": {"a": "(número)", "d": "Devuelve el arcoseno de un número, en radianes, dentro del rango de 0 a Pi. El arcoseno es el ángulo cuyo coseno es Número", "ad": "es el coseno del ángulo deseado y debe estar entre -1 y 1"}, "ACOSH": {"a": "(número)", "d": "Devuelve el coseno hiperbólico inverso de un número", "ad": "es un número real y debe ser mayor o igual que 1"}, "ACOT": {"a": "(número)", "d": "Devuelve el arco tangente de un número en radianes dentro del rango de 0 a Pi.", "ad": "es la cotangente del ángulo que quieres"}, "ACOTH": {"a": "(número)", "d": "Devuelve la cotangente hiperbólica inversa de un número", "ad": "Es la cotangente hiperbólica del ángulo que quieres"}, "AGGREGATE": {"a": "(núm_función; opciones; ref1; ...)", "d": "Devuelve un agregado de una lista o base de datos", "ad": "es el número del 1 al 19 que especifica la función de resumen para el agregado.!es el número del 0 al 7 que especifica los valores que se omitirán para el agregado!es la matriz o el rango de datos numéricos en que se calculará el agregado!indica la posición en la matriz; es el mayor valor k-ésimo, el menor valor k-ésimo, el percentil k-ésimo o el cuartil k-ésimo.!es el número del 1 al 19 que especifica la función de resumen para el agregado.!es el número del 0 al 7 que especifica los valores que se omitirán para el agregado!son las referencias o rangos del 1 al 253 para los que se necesita el agregado"}, "ARABIC": {"a": "(texto)", "d": "Convierte un número romano en arábigo", "ad": "Es el número romano que quieres convertir"}, "ASC": {"a": "(texto)", "d": "Para los idiomas que empleen juegos de caracteres de dos bytes (DBCS), convierte los caracteres de ancho completo (de dos bytes) en caracteres de ancho medio (de un byte)", "ad": "El texto que se desea reemplazar"}, "ASIN": {"a": "(número)", "d": "Devuelve el arcoseno de un número en radianes, dentro del rango -Pi/2 a Pi/2", "ad": "es el seno del ángulo deseado y debe estar entre -1 y 1"}, "ASINH": {"a": "(número)", "d": "Devuelve el seno hiperbólico inverso de un número", "ad": "es un número real y debe ser mayor o igual que 1"}, "ATAN": {"a": "(número)", "d": "Devuelve el arco tangente de un número en radianes, dentro del rango -Pi/2 a Pi/2", "ad": "es la tangente del ángulo deseado"}, "ATAN2": {"a": "(coord_x; coord_y)", "d": "Devuelve el arco tangente de las coordenadas X e Y especificadas, en un valor en radianes comprendido entre -Pi y Pi, excluyendo -Pi", "ad": "es la coordenada X del punto!es la coordenada Y del punto"}, "ATANH": {"a": "(número)", "d": "Devuelve la tangente hiperbólica inversa de un número", "ad": "es un número real que debe ser mayor que -1 y menor que 1"}, "BASE": {"a": "(númer<PERSON>; raíz; [longitud_mín])", "d": "Convierte un número en una representación de texto con la base dada", "ad": "Es el número que quieres convertir!Es la base en la que quieres que se convierta el número!Es la longitud mínima de la cadena devuelta. Si se omite, no se agregan ceros iniciales"}, "CEILING": {"a": "(número; cifra_significativa)", "d": "Redondea un número hacia arriba, hasta el múltiplo significativo más cercano", "ad": "es el valor que se desea redondear!es el múltiplo hacia el que se desea redondear"}, "CEILING.MATH": {"a": "(número; [cifra_significativa]; [moda])", "d": "Redondea un número hacia arriba, al entero más cercano o al múltiplo significativo más cercano", "ad": "Es el valor que quieres redondear!Es el múltiplo al que quieres redondear!Cuando está dada y no es nula, esta función se redondea a un valor alejado del cero"}, "CEILING.PRECISE": {"a": "( x; [significado])", "d": "Devuelve un número que se redondea hacia arriba al entero más cercano o al múltiplo de significación más cercano.", "ad": "es el valor que se desea redondear!es el múltiplo hacia el que se desea redondear"}, "COMBIN": {"a": "(número; tamaño)", "d": "Devuelve el número de combinaciones para un número determinado de elementos", "ad": "es el número total de elementos!es el número de elementos en cada combinación"}, "COMBINA": {"a": "(número; número_elegido)", "d": "Devuelve la cantidad de combinaciones con repeticiones de una cantidad determinada de elementos", "ad": "Es la cantidad total de elementos!Es la cantidad de elementos en cada combinación"}, "COS": {"a": "(número)", "d": "Devuelve el coseno de un ángulo", "ad": "es el ángulo en radianes del que se desea obtener el coseno"}, "COSH": {"a": "(número)", "d": "Devuelve el coseno hiperbólico de un número", "ad": "es cualquier número real"}, "COT": {"a": "(número)", "d": "Devuelve la cotangente de un ángulo", "ad": "Es el ángulo en radianes del que quieres saber la cotangente"}, "COTH": {"a": "(número)", "d": "Devuelve la cotangente hiperbólica de un número", "ad": "Es el ángulo en radianes del que quieres saber la cotangente hiperbólica"}, "CSC": {"a": "(número)", "d": "Devuelve la cosecante de un ángulo", "ad": "Es el ángulo en radianes del que quieres saber la cosecante"}, "CSCH": {"a": "(número)", "d": "Devuelve la cosecante hiperbólica de un ángulo", "ad": "Es el ángulo en radianes del que quieres saber la cosecante hiperbólica"}, "DECIMAL": {"a": "(número; raíz)", "d": "Convierte una representación de texto de un número en una base dada en un número decimal", "ad": "Es el número que quieres convertir!Es la base del número que estás convirtiendo"}, "DEGREES": {"a": "(á<PERSON><PERSON>)", "d": "Convierte radianes en grados", "ad": "es el ángulo en radianes que se desea convertir"}, "ECMA.CEILING": {"a": "( x; significado)", "d": "Redondea el número hasta el múltiplo de significación más cercano", "ad": "es el valor que se desea redondear!es el múltiplo hacia el que se desea redondear"}, "EVEN": {"a": "(número)", "d": "Redondea un número positivo hacia arriba y un número negativo hacia abajo hasta el próximo entero par. Los números negativos se ajustan alejándolos de cero", "ad": "es el valor que se redondea"}, "EXP": {"a": "(número)", "d": "Devuelve e elevado a la potencia de un número determinado", "ad": "es el exponente aplicado a la base e. La constante e es igual a 2.71828182845904, la base del logaritmo natural"}, "FACT": {"a": "(número)", "d": "Devuelve el factorial de un número, igual a 1*2*3*...*Número", "ad": "es el número no negativo del que desea obtener su factorial"}, "FACTDOUBLE": {"a": "(número)", "d": "Devuelve el factorial doble de un número", "ad": "es el número cuyo factorial doble desea calcular"}, "FLOOR": {"a": "(número; cifra_significativa)", "d": "Redondea un número hacia abajo, hasta el múltiplo significativo más cercano", "ad": "es el valor numérico que se desea redondear!es el múltiplo hacia el que se desea redondear. Número y Cifra significativa deben ser ambos positivos o negativos"}, "FLOOR.PRECISE": {"a": "( x; [significado])", "d": "Devuelve un número que se redondea hacia abajo al entero más cercano o al múltiplo de significación más cercano.", "ad": "es el valor que se desea redondear!es el múltiplo hacia el que se desea redondear"}, "FLOOR.MATH": {"a": "(número; [cifra_significativa]; [moda])", "d": "Redondea un número hacia abajo, al entero más cercano o al múltiplo significativo más cercano", "ad": "Es el valor que quieres redondear!Es el múltiplo al que quieres redondear!Cuando está dada y no es nula, esta función se redondea hacia cero"}, "GCD": {"a": "(número1; [número2]; ...)", "d": "Devuelve el máximo común divisor", "ad": "son valores del 1 al 255"}, "INT": {"a": "(número)", "d": "Redondea un número hasta el entero inferior más próximo", "ad": "es el número real que se desea redondear a entero"}, "ISO.CEILING": {"a": "(número; [significado])", "d": "Devuelve un número que se redondea hacia arriba al entero más cercano o al múltiplo de significación más cercano, independientemente del signo del número. Sin embargo, si el número o el significado es cero, se devuelve cero.", "ad": "es el valor que se desea redondear!es el múltiplo hacia el que se desea redondear"}, "LCM": {"a": "(número1; [número2]; ...)", "d": "Devuelve el mínimo común múltiplo", "ad": "son valores del 1 al 255 de los que desea el múltiplo menos común"}, "LN": {"a": "(número)", "d": "Devuelve el logaritmo natural de un número", "ad": "es el número real positivo para el que se desea obtener el logaritmo natural"}, "LOG": {"a": "(número; [base])", "d": "Devuelve el logaritmo de un número en la base especificada", "ad": "es el número real positivo para el que se desea obtener el logaritmo!es la base del logaritmo. Si se omite, se asume 10"}, "LOG10": {"a": "(número)", "d": "Devuelve el logaritmo en base 10 de un número", "ad": "es el número real positivo para el cual se desea el logaritmo en base 10"}, "MDETERM": {"a": "(matriz)", "d": "Devuelve el determinante matricial de una matriz", "ad": "es una matriz numérica con el mismo número de filas y columnas y puede ser un rango de celdas o una constante matricial"}, "MINVERSE": {"a": "(matriz)", "d": "Devuelve la matriz inversa de una matriz dentro de una matriz", "ad": "es una matriz numérica con el mismo número de filas y columnas, y puede ser un rango de celdas o una constante matricial"}, "MMULT": {"a": "(matriz1; matriz2)", "d": "Devuelve el producto matricial de dos matrices, una matriz con el mismo número de filas que Matriz1 y columnas que Matriz2", "ad": "son las matrices que se desea multiplicar y debe tener el mismo número de columnas que filas hay en Matriz2"}, "MOD": {"a": "(número; núm_divisor)", "d": "Proporciona el residuo después de dividir un número por un divisor", "ad": "es el número para el que se desea encontrar el residuo después de realizar la división!es el número por el cual se desea dividir Número"}, "MROUND": {"a": "(n<PERSON><PERSON><PERSON>; múltiplo)", "d": "Devuelve un número redondeado al múltiplo deseado", "ad": "es el valor para redondear!es el múltiplo al cual desea redondear el argumento"}, "MULTINOMIAL": {"a": "(número1; [número2]; ...)", "d": "Devuelve el polinomio de un conjunto de números", "ad": "valores de 1 a 255 para los que desea el polinomio"}, "MUNIT": {"a": "(dimension)", "d": "Devuelve la matriz de la unidad para la dimensión especificada", "ad": "Es un entero que especifica la dimensión de la matriz de la unidad que quieres devolver"}, "ODD": {"a": "(número)", "d": "Redondea un número positivo hacia arriba y un número negativo hacia abajo hasta el próximo entero impar", "ad": "es el valor que se redondea"}, "PI": {"a": "()", "d": "Devuelve el valor Pi, 3,14159265358979, con precisión de 15 dígitos", "ad": ""}, "POWER": {"a": "(número; potencia)", "d": "Devuelve el resultado de elevar el número a una potencia", "ad": "es el número base; cualquier número real!es el exponente al que desea elevar la base"}, "PRODUCT": {"a": "(número1; [número2]; ...)", "d": "Multiplica todos los números especificados como argumentos", "ad": "son entre 1 y 255 números, valores lógicos o texto que representa números que desea multiplicar"}, "QUOTIENT": {"a": "(numerador; denominador)", "d": "Devuelve la parte entera de una división", "ad": "es el dividendo!es el divisor"}, "RADIANS": {"a": "(á<PERSON><PERSON>)", "d": "Convierte grados en radianes", "ad": "es el ángulo en grados que se desea convertir"}, "RAND": {"a": "()", "d": "Devuelve un número aleatorio mayor o igual que 0 y menor que 1, distribuido (cambia al actualizarse)", "ad": ""}, "RANDARRAY": {"a": "([filas]; [columnas]; [min]; [max]; [entero])", "d": "Devuelve una matriz de números aleatorios", "ad": "el número de filas de la matriz devuelta!el número de columnas de la matriz devuelta!el número mínimo que desea que se devuelva!el número máximo que desea que se devuelva!devuelve un entero o un valor decimal. VERDADERO para un entero, FALSO para un número decimal"}, "RANDBETWEEN": {"a": "(inferior; superior)", "d": "Devuelve el número aleatorio entre los números que especifique", "ad": "es el entero más pequeño que devolverá ALEATORIO.ENTRE!es el entero más grande que devolverá ALEATORIO.ENTRE"}, "ROMAN": {"a": "(número; [forma])", "d": "Convierte un número arábigo en romano, en formato de texto", "ad": "es el número arábigo que desea convertir!es el número que especifica el tipo de número romano que desea."}, "ROUND": {"a": "(número; núm_decimales)", "d": "Redondea un número al número de decimales especificado", "ad": "es el número que se desea redondear!especifica el número de decimales al que se desea redondear. Los números negativos se redondean a la izquierda de la coma decimal; cero se redondea al entero más cercano"}, "ROUNDDOWN": {"a": "(número; núm_decimales)", "d": "Redondea un número hacia abajo, hacia cero", "ad": "es cualquier número real que desee redondear hacia abajo!es el número de decimales a los cuales desea redondear. Para números negativos se redondea a la izquierda de la coma decimal; si se omite o el valor es cero, se redondea al entero más cercano"}, "ROUNDUP": {"a": "(número; núm_decimales)", "d": "Redondea un número hacia arriba, en dirección contraria a cero", "ad": "es cualquier número real que desee redondear!es el número de decimales a los cuales desea redondear. Para números negativos se redondea a la izquierda de la coma decimal; si se omite o el valor es cero, se redondea al entero más cercano"}, "SEC": {"a": "(número)", "d": "Devuelve la secante de un ángulo", "ad": "Es el ángulo en radianes del que quieres saber la secante"}, "SECH": {"a": "(número)", "d": "Devuelve la secante hiperbólica de un ángulo", "ad": "Es el ángulo en radianes del que quieres saber la secante hiperbólica"}, "SERIESSUM": {"a": "(x; n; m; coeficientes)", "d": "Devuelve la suma de una serie de potencias basándose en la fórmula", "ad": "es el valor de entrada para la serie de potencias!es la potencia inicial a la que desea elevar x!es el paso por el que se incrementa n para cada término de la serie!es un conjunto de coeficientes por el que cada potencia sucesiva de x se multiplica"}, "SIGN": {"a": "(número)", "d": "Devuelve el signo de un número: 1, si el número es positivo; cero, si el número es cero y -1, si el número es negativo", "ad": "es un número real"}, "SIN": {"a": "(número)", "d": "Devuelve el seno de un ángulo determinado", "ad": "es el ángulo en radianes del que se desea obtener el seno. Grados * PI()/180 = radianes"}, "SINH": {"a": "(número)", "d": "Devuelve el seno hiperbólico de un número", "ad": "es un número real"}, "SQRT": {"a": "(número)", "d": "Devuelve la raíz cuadrada de un número", "ad": "es el número del que se desea obtener la raíz cuadrada"}, "SQRTPI": {"a": "(número)", "d": "Devuelve la raíz cuadrada de (número * Pi)", "ad": "es el número por el que será multiplicado p"}, "SUBTOTAL": {"a": "(núm_función; ref1; ...)", "d": "Devuelve un subtotal dentro de una lista o una base de datos", "ad": "es un número del 1 al 11 que indica qué función debe ser usada en el cálculo de los subtotales dentro de una lista.!son de 1 a 254 rangos o referencias, de los cuales desea calcular el subtotal"}, "SUM": {"a": "(número1; [número2]; ...)", "d": "Suma todos los números en un rango de celdas", "ad": "son de 1 a 255 números que se desea sumar. Los valores lógicos y el texto se omiten en las celdas, incluso si están escritos como argumentos"}, "SUMIF": {"a": "(rango; criterio; [rango_suma])", "d": "Suma las celdas que cumplen determinado criterio o condición", "ad": "es el rango de celdas que desea evaluar!es el criterio o condición que determina qué celdas deben sumarse. Puede estar en forma de número, texto o expresión!son las celdas que se van a sumar. Si se omite, se usarán las celdas en el rango"}, "SUMIFS": {"a": "(rango_suma; rango_criterios; criterio; ...)", "d": "Suma las celdas que cumplen un determinado conjunto de condiciones o criterios", "ad": "son las celdas que se van a sumar!es el rango de celdas que desea evaluar para la condición determinada!es el criterio o condición que determina qué celdas deben sumarse. Puede estar en forma de número, texto o expresión"}, "SUMPRODUCT": {"a": "(matriz1; [matriz2]; [matriz3]; ...)", "d": "Devuelve la suma de los productos de rangos o matrices correspondientes", "ad": "son de 2 a 255 matrices cuyos componentes se desea multiplicar y después sumar. Todas las matrices deben tener las mismas dimensiones"}, "SUMSQ": {"a": "(número1; [número2]; ...)", "d": "Devuelve la suma de los cuadrados de los argumentos. Los argumentos pueden ser números, matrices, nombres o referencias a celdas que contengan números", "ad": "son de 1 a 225 números, matrices, nombres o referencias a matrices cuya suma de cuadrados desea calcular"}, "SUMX2MY2": {"a": "(matriz_x; matriz_y)", "d": "Suma las diferencias entre cuadrados de dos rangos o matrices correspondientes", "ad": "es la primera matriz o rango de valores y puede ser un número, nombre, matriz o referencia que contenga números!es la segunda matriz o rango de valores y puede ser un número, nombre, matriz o referencia que contenga números"}, "SUMX2PY2": {"a": "(matriz_x; matriz_y)", "d": "Devuelve la suma de total de las sumas de cuadrados de números en dos rangos o matrices correspondientes", "ad": "es la primera matriz o rango de valores y puede ser un número, nombre, matriz o referencia que contenga números!es la segunda matriz o rango de valores y puede ser un número, nombre, matriz o referencia que contenga números"}, "SUMXMY2": {"a": "(matriz_x; matriz_y)", "d": "Suma los cuadrados de las diferencias en dos rangos correspondientes de matrices", "ad": "es la primera matriz o rango de valores y puede ser un número, nombre, matriz o referencia que contiene números!es el segundo rango o matriz de valores y puede ser un número, nombre, matriz o referencia que contenga números"}, "TAN": {"a": "(número)", "d": "Devuelve la tangente de un ángulo", "ad": "es el ángulo en radianes del que se desea obtener la tangente. Grados * PI()/180 = radianes"}, "TANH": {"a": "(número)", "d": "Devuelve la tangente hiperbólica de un número", "ad": "es cualquier número real"}, "TRUNC": {"a": "(número; [núm_decimales])", "d": "Convierte un número decimal a uno entero al quitar la parte decimal o de fracción", "ad": "es el número que se desea truncar!es un número que especifica la precisión de truncado; si se omite se asume 0 (cero)"}, "ADDRESS": {"a": "(fila; columna; [abs]; [a1]; [hoja])", "d": "Crea una referencia de celda en forma de texto una vez especificados los números de fila y columna", "ad": "es el número de fila que se usa en la referencia de celda: Núm_fila = 1 para la fila 1!es el número de columna que se usa en la referencia de celda. Por ejemplo, Núm_columna = 4 para la columna D!especifica el tipo de referencia: absoluta = 1; fila absoluta y columna relativa = 2; fila relativa y columna absoluta = 3; relativa = 4!es el valor lógico que especifica el estilo de referencia: para estilo A1 = 1 o VERDADERO; para estilo F1C1 = 0 o FALSO!es el nombre de la hoja de cálculo que se usará como referencia externa"}, "CHOOSE": {"a": "(núm_índice; valor1; [valor2]; ...)", "d": "Elige un valor o una acción de una lista de valores a partir de un número de índice", "ad": "especifica el argumento de valor que se selecciona. El núm_índice debe estar entre 1 y 254 o ser una fórmula, o una referencia a un número, entre 1 y 254!son de 1 a 254 argumentos de valores, referencias de celda, nombres definidos, fórmulas, funciones o argumentos de texto entre los cuales ELEGIR selecciona un valor"}, "COLUMN": {"a": "([ref])", "d": "Devuelve el número de columna de una referencia", "ad": "es la celda o rango de celdas contiguas de las cuales se desea obtener el número de columna. Si esta referencia se omite, se usará la celda con la función COLUMNA"}, "COLUMNS": {"a": "(matriz)", "d": "Devuelve el número de columnas en una matriz o referencia", "ad": "es una matriz, fórmula matricial o una referencia a un rango de celdas de las que se desea obtener el número de columnas"}, "FORMULATEXT": {"a": "(referencia)", "d": "Devuelve una fórmula como una cadena", "ad": "Es una referencia a una fórmula"}, "HLOOKUP": {"a": "(valor_buscado; matriz_buscar_en; indicador_filas; [ordenado])", "d": "Busca en la primera fila de una tabla o matriz de valores y devuelve el valor en la misma columna desde una fila especificada", "ad": "es el valor que se busca en la primera fila de la tabla y puede ser un valor, una referencia o una cadena de texto!es una tabla de texto, números o valores lógicos en los que se buscan los datos. Tabla_matriz puede ser una referencia a un rango o un nombre de rango!es el número de fila en tabla_matriz desde el cual se deberá devolver el valor coincidente. La primera fila de valores en la tabla es la fila 1!es un valor lógico: para encontrar la coincidencia más cercana en la fila superior (ordenada de forma ascendente) = VERDADERO u omitido; para encontrar coincidencia exacta = FALSO"}, "HYPERLINK": {"a": "(ubicación_del_vínculo; [nombre_descriptivo])", "d": "Crea un acceso directo o salto que abre un documento guardado en el disco duro, en un servidor de red o en Internet", "ad": "es el texto con la ruta de acceso y el nombre de archivo que se abrirá en el disco duro, en una dirección UNC o en una ruta URL!es el número, texto o función que aparece en la celda. Si se omite, la celda presentará el texto de ubicación_del_vínculo"}, "INDEX": {"a": "(matriz; núm_fila; [núm_columna]!ref; núm_fila; [núm_columna]; [núm_área])", "d": "Devuelve un valor o referencia de la celda en la intersección de una fila y columna en particular, en un rango especificado", "ad": "es un rango de celdas o una constante de matriz.!sele<PERSON><PERSON>, en Matriz o Referencia, la fila desde la cual se devolverá un valor. Si se omite, se requerirá núm_columna!selecciona, en Matriz o Referencia, la columna desde la cual se devolverá un valor. Si se omite, se requerirá núm_fila!es una referencia a una o más celdas!selecciona, en Matriz o Referencia, la fila desde la cual se devolverá un valor. Si se omite, se requerirá núm_columna!selecciona, en Matriz o Referencia, la columna desde la cual se devolverá un valor. Si se omite, se requerirá núm_fila!selecciona un rango en Referencia desde el cual se devolverá un valor. La primera área seleccionada o introducida es el área 1, la segunda es área 2 y así sucesivamente"}, "INDIRECT": {"a": "(ref; [a1])", "d": "Devuelve una referencia especificada por un valor de texto", "ad": "es una referencia a una celda que contiene una referencia de tipo A1, de tipo F1C1, un nombre definido como referencia o una referencia a una celda como cadena de texto!es un valor lógico que especifica el tipo de referencia en ref_texto: estilo F1C1 = FALSO; estilo A1 = VERDADERO u omitido"}, "LOOKUP": {"a": "(valor_buscado; vector_de_comparación; [vector_resultado]!valor_buscado; matriz)", "d": "Busca valores de un rango de una columna o una fila o desde una matriz. Proporcionado para compatibilidad con versiones anteriores", "ad": "es un valor que busca BUSCAR en el vector_de_comparación y puede ser un número, texto, un valor lógico o un nombre o referencia a un valor!es un rango que solo contiene una columna o una fila de texto, números o valores lógicos, ubicados en orden ascendente!es un rango que solo contiene una columna o una fila del mismo tamaño que el vector_de_comparación!es un valor que busca BUSCAR en la Matriz y que puede ser un número, texto, un valor lógico, un nombre o una referencia a un valor!es un rango de celdas que contiene el texto, los números o los valores lógicos que se desean comparar con valor_buscado"}, "MATCH": {"a": "(valor_buscado; matriz_buscada; [tipo_de_coincidencia])", "d": "Devuelve la posición relativa de un elemento en una matriz, que coincide con un valor dado en un orden especificado", "ad": "es el valor que se usa para encontrar el valor deseado en la matriz y puede ser un número, texto, valor lógico o una referencia a uno de ellos!es un rango contiguo de celdas que contienen posibles valores de búsqueda, una matriz de valores o una referencia a una matriz!es un número 1, 0, -1 que indica el valor que se devolverá."}, "OFFSET": {"a": "(ref; filas; columnas; [alto]; [ancho])", "d": "Devuelve una referencia a un rango que es un número especificado de filas y columnas de una referencia dada", "ad": "es la referencia a partir de la cual se desea basar la desviación, una referencia a una celda o rango de celdas adyacentes!es el número de filas, hacia arriba o hacia abajo, al que se desea que haga referencia el resultado de la celda superior izquierda!es el número de columnas, hacia la derecha o izquierda, al que se desea que haga referencia el resultado de la celda superior izquierda!es el alto, en número de filas, que se desea que tenga el resultado y, que si se omite, tiene el mismo alto que Referencia!es el ancho, en número de columnas, que se desea que tenga el resultado y, que si se omite, tiene el mismo ancho que Referencia"}, "ROW": {"a": "([ref])", "d": "Devuelve el número de fila de una referencia", "ad": "es la celda, o rango único de celdas, de la que se desea obtener el número de fila; si se omite, devuelve la celda con la función FILA"}, "ROWS": {"a": "(matriz)", "d": "Devuelve el número de filas de una referencia o matriz", "ad": "es una matriz, fórmula matricial o referencia a un rango de celdas de las que se desea obtener el número de filas"}, "TRANSPOSE": {"a": "(matriz)", "d": "Devuelve un rango vertical de celdas como un rango horizontal, o viceversa", "ad": "es un rango de celdas en una hoja de cálculo o una matriz de valores que se desea transponer"}, "UNIQUE": {"a": "(matriz; [by_col]; [exactly_once])", "d": " Devuelve los valores únicos de un rango o matriz.", "ad": "el rango o matriz desde el que se van a devolver filas o columnas únicas!es un valor lógico: comparar filas entre sí y devolver las filas únicas = FALSE u omitidas; comparar columnas entre sí y devolver las columnas únicas = TRUE! es un valor lógico: devolver filas o columnas que se producen exactamente una vez desde la matriz = TRUE; devuelve todas las filas o columnas distintas de la matriz = FALSE u omitidas"}, "VLOOKUP": {"a": "(valor_buscado; matriz_buscar_en; indicador_columnas; [ordenado])", "d": "Busca un valor en la primera columna de la izquierda de una tabla y luego devuelve un valor en la misma fila desde una columna especificada. De forma predeterminada, la tabla se ordena de forma ascendente", "ad": "es el valor buscado en la primera columna de la tabla y puede ser un valor, referencia o una cadena de texto!es una tabla de texto, números o valores lógicos en los cuales se recuperan datos. Matriz_buscar_en puede ser una referencia a un rango o un nombre de rango!es el número de columna de matriz_buscar_en desde la cual debe devolverse el valor que coincida. La primera columna de valores en la tabla es la columna 1!es un valor lógico: para encontrar la coincidencia más cercana en la primera columna (ordenada de forma ascendente) = VERDADERO u omitido; para encontrar la coincidencia exacta = FALSO"}, "XLOOKUP": {"a": "(valor_buscado; matriz_buscada; matriz_devuelta; [si_no_se_encuentra]; [modo_de_coincidencia]; [modo_de_búsqueda])", "d": "Busca una coincidencia en un rango o una matriz y devuelve el elemento correspondiente de un segundo rango o matriz. De forma predeterminada, se usa una coincidencia exacta", "ad": "es el valor que se buscará!es la matriz o rango donde se buscará!es la matriz o rango donde se devolverá!valor devuelto si no se encuentra ninguna coincidencia!especifica cómo comparar el valor_buscado con los valores de la matriz_buscada!especifica el modo de búsqueda que se usará. De forma predeterminada, se usará una búsqueda de primera a última"}, "CELL": {"a": "(info_type; [reference])", "d": "Devuelve información sobre el formato, la ubicación o el contenido de una celda", "ad": "es un valor de texto que especifica el tipo de información de la celda que se desea obtener!la celda sobre la que desea información"}, "ERROR.TYPE": {"a": "(valor_de_error)", "d": "Devuelve un número que coincide con un valor de error.", "ad": "es el valor de error cuyo número identificador desea buscar y puede ser un valor de error actual o una referencia a una celda que contiene un valor de error"}, "ISBLANK": {"a": "(valor)", "d": "Comprueba si se refiere a una celda vacía y devuelve VERDADERO o FALSO", "ad": "es la celda o un nombre que se refiere a la celda que desea comprobar"}, "ISERR": {"a": "(valor)", "d": "Comprueba si un valor es un error diferente de #N/D y devuelve VERDADERO o FALSO", "ad": "es el valor que desea probar. <PERSON><PERSON> puede referirse a una celda, una fórmula o un nombre que se refiere a una celda, una fórmula o un valor"}, "ISERROR": {"a": "(valor)", "d": "Comprueba si un valor es un error y devuelve VERDADERO o FALSO", "ad": "es el valor que desea probar. <PERSON><PERSON> puede referirse a una celda, una fórmula o un nombre que se refiere a una celda, una fórmula o un valor"}, "ISEVEN": {"a": "(número)", "d": "Devuelve verdadero si el número es par", "ad": "es el valor a comprobar"}, "ISFORMULA": {"a": "(referencia)", "d": "Comprueba si la referencia es a una celda que contiene una fórmula y devuelve VERDADERO o FALSO", "ad": "Es una referencia a la celda que quieres probar. La referencia puede ser una referencia de celda, una fórmula o un nombre que hace referencia a una celda"}, "ISLOGICAL": {"a": "(valor)", "d": "Comprueba si un valor es un valor lógico (VERDADERO o FALSO), y devuelve VERDADERO o FALSO", "ad": "es el valor que desea probar. <PERSON><PERSON> puede referirse a una celda, una fórmula o un nombre que se refiere a una celda, fórmula o valor"}, "ISNA": {"a": "(valor)", "d": "Comprueba si un valor de error es #N/A (valor no aplicable) y devuelve VERDADERO o FALSO", "ad": "es el valor que desea probar. <PERSON><PERSON> puede referirse a una celda, una fórmula o un nombre que se refiere a una celda, fórmula o valor"}, "ISNONTEXT": {"a": "(valor)", "d": "Comprueba si un valor no es texto (las celdas en blanco no son texto), y devuelve VERDADERO o FALSO", "ad": "es el valor que desea probar: una celda, una fórmula o un nombre que se refiere a una celda, fórmula o valor"}, "ISNUMBER": {"a": "(valor)", "d": "Comprueba si un valor es un número y devuelve VERDADERO o FALSO", "ad": "es el valor que desea probar. <PERSON><PERSON> puede referirse a una celda, una fórmula o un nombre que se refiere a una celda, fórmula o valor"}, "ISODD": {"a": "(número)", "d": "Devuelve VERDADERO si el número es impar", "ad": "es el valor que se va a comprobar"}, "ISREF": {"a": "(valor)", "d": "Comprueba si valor es una referencia y devuelve VERDADERO o FALSO", "ad": "es el valor que desea probar. <PERSON><PERSON> puede referirse a una celda, una fórmula o un nombre que se refiere a una celda, fórmula o valor"}, "ISTEXT": {"a": "(valor)", "d": "Comprueba si un valor es texto y devuelve VERDADERO o FALSO", "ad": "es el valor de texto que se desea comprobar. <PERSON><PERSON> puede referirse a una celda, una fórmula o un nombre que se refiere a una celda, fórmula o valor"}, "N": {"a": "(valor)", "d": "Convierte valores no numéricos en números, fechas en números de serie, VERDADERO en 1 y cualquier otro en 0 (cero)", "ad": "es el valor que se desea convertir"}, "NA": {"a": "()", "d": "Devuelve el valor de error #N/A (valor no disponible)", "ad": ""}, "SHEET": {"a": "([valor])", "d": "Devuelve el número de la hoja a la que se hace referencia", "ad": "Es el nombre de una hoja o una referencia de la que quieres el número de hoja. Si se omite, se devuelve el número de la hoja que contiene la función"}, "SHEETS": {"a": "([referencia])", "d": "Devuelve la cantidad de hojas de una referencia", "ad": "Es una referencia de la que quieres saber la cantidad de hojas que contiene. Si se omite, se devuelve la cantidad de hojas del libro que contienen la función"}, "TYPE": {"a": "(valor)", "d": "Devuelve un entero que representa el tipo de datos de un valor: número = 1; texto = 2; valor lógico = 4; valor de error = 16; matriz = 64; datos compuestos = 128", "ad": "puede ser cualquier valor"}, "AND": {"a": "(valor_lógico1; [valor_lógico2]; ...)", "d": "Comprueba si todos los argumentos son VERDADEROS, y devuelve VERDADERO si todos los argumentos son VERDADEROS", "ad": "son entre 1 y 255 condiciones que desea comprobar, que pueden ser VERDADERO o FALSO y que pueden ser valores lógicos, matrices o referencias"}, "FALSE": {"a": "()", "d": "Devuelve el valor lógico FALSO", "ad": ""}, "IF": {"a": "(prue<PERSON>_lógica; [valor_si_verdadero]; [valor_si_falso])", "d": "Comprueba si se cumple una condición y devuelve una valor si se evalúa como VERDADERO y otro valor si se evalúa como FALSO", "ad": "es cualquier valor o expresión que pueda evaluarse como VERDADERO o FALSO!es el valor que se devolverá si prueba_lógica es VERDADERO. Si se omite, devolverá VERDADERO. Puede anidar hasta siete funciones SI!es el valor que se devolverá si prueba_lógica es FALSO. Si se omite, devolverá FALSO"}, "IFS": {"a": "(prue<PERSON>_lógica; valor_si_verdadero; ...)", "d": "Comprueba si se cumplen una o más condiciones y devuelve un valor correspondiente a la primera condición verdadera", "ad": "es cualquier valor o expresión que pueda evaluarse como VERDADERO o FALSO!es el valor devuelto si prueba_lógica es VERDADERO"}, "IFERROR": {"a": "(valor; valor_si_error)", "d": "Devuelve valor_si_error si la expresión es un error y el valor de la expresión no lo es", "ad": "es cualquier valor, expresión o referencia!es cualquier valor, expresión o referencia"}, "IFNA": {"a": "(valor; valor_si_nd)", "d": "Devuelve el valor que especificas, si la expresión se convierte en #N/A. De lo contrario, devuelve el resultado de la expresión", "ad": "Es cualquier valor, expresión o referencia!Es cualquier valor, expresión o referencia"}, "NOT": {"a": "(valor_lógico)", "d": "Cambia FALSO por VERDADERO y VERDADERO por FALSO", "ad": "es un valor lógico o expresión que se puede evaluar como VERDADERO o FALSO"}, "OR": {"a": "(valor_lógico1; [valor_lógico2]; ...)", "d": "Comprueba si alguno de los argumentos es VERDADERO, y devuelve VERDADERO o FALSO. Devuelve FALSO si todos los argumentos son FALSOS", "ad": "son entre 1 y 255 condiciones que se desea comprobar y que pueden ser VERDADERO o FALSO"}, "SWITCH": {"a": "(expresión; valor1; resultado1; [predeterminado_o_valor2]; [resultado2]; ...)", "d": "Evalúa una expresión con una lista de valores y devuelve el resultado correspondiente al primer valor coincidente. Si no hay ninguna coincidencia, se devuelve un valor predeterminado opcional", "ad": "es la expresión que se va a evaluar!es un valor que se va a comparar con la expresión!es el resultado que se devuelve si el valor correspondiente coincide con expresión"}, "TRUE": {"a": "()", "d": "Devuelve el valor lógico VERDADERO", "ad": ""}, "XOR": {"a": "(lógico1; [lógico2]; ...)", "d": "Devuelve una 'Exclusive Or' lógica de todos los argumentos", "ad": "Son las condiciones de 1 a 254 que quieres probar, que pueden ser VERDADERAS o FALSAS, así como valores lógicos, matrices o referencias"}, "TEXTBEFORE": {"a": "(texto, delimitador, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Devuelve el texto que está antes de delimitar caracteres.", "ad": "El texto que desea buscar en el delimitador.!El carácter o cadena que se va a usar como delimitador.!Repetición deseada del delimitador. El valor predeterminado es 1. Un número negativo busca desde el final.!Busca una coincidencia delimitadora en el texto. De forma predeterminada, se realiza una coincidencia que distingue mayúsculas de minúsculas.!Indica si se debe hacer coincidir el delimitador con el final del texto. De forma predeterminada, no coinciden.!Se devuelve si no se encuentra ninguna coincidencia. De forma predeterminada, se devuelve #N/A."}, "TEXTAFTER": {"a": "(texto, delimitador, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Devuelve el texto que está después de delimitar caracteres.", "ad": "El texto que desea buscar en el delimitador.!El carácter o cadena que se va a usar como delimitador.!Repetición deseada del delimitador. El valor predeterminado es 1. Un número negativo busca desde el final.!Busca una coincidencia delimitadora en el texto. De forma predeterminada, se realiza una coincidencia que distingue mayúsculas de minúsculas.!Indica si se debe hacer coincidir el delimitador con el final del texto. De forma predeterminada, no coinciden.!Se devuelve si no se encuentra ninguna coincidencia. De forma predeterminada, se devuelve #N/A."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Divide el texto en filas o columnas con delimitadores.", "ad": "El texto para dividir!Carácter o cadena que se usará para dividir las columnas.!Carácter o cadena que se usará para dividir las filas.!Ignorar o no las celdas vacías. El valor predeterminado es FALSO.!Busca el texto para una coincidencia de delimitador.!De forma predeterminada, se realiza una coincidencia que distingue mayúsculas de minúsculas Valor que se usará para el espaciado entre borde y texto. El valor predeterminado es #N/A."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Ajusta un vector de fila o columna después de un número especificado de valores.", "ad": "El vector o referencia para ajustar.!El número máximo de valores por fila.!El valor con el que rellenar. El valor predeterminado es #N/A."}, "VSTACK": {"a": "(matriz1, [matriz2], ...)", "d": "Apilar verticalmente matrices en una matriz.", "ad": "Una matriz o referencia que se va a apilar."}, "HSTACK": {"a": "(matriz1, [matriz2], ...)", "d": "Apilar horizontalmente matrices en una matriz.", "ad": "Una matriz o referencia que se va a apilar."}, "CHOOSEROWS": {"a": "(matriz, row_num1, [row_num2], ...)", "d": "Devuelve filas de una matriz o referencia.", "ad": "Matriz o referencia que contiene las filas que se devolverán.!Número de la fila que se va a devolver."}, "CHOOSECOLS": {"a": "(matriz, col_num1, [col_num2], ...)", "d": "Devuelve columnas de una matriz o referencia.", "ad": "Matriz o referencia que contiene las columnas que se devolverán.!Número de la columna que se va a devolver."}, "TOCOL": {"a": "(matriz, [ignorar], [scan_by_column])", "d": "Devuelve la matriz como una columna.", "ad": "Matriz o referencia que se devolverá como columna.!Indica si se deben omitir determinados tipos de valores. De forma predeterminada, no se omite ningún valor.!Examina la matriz por columna. De forma predeterminada, la matriz se examina por fila."}, "TOROW": {"a": "(matriz, [ignorar], [scan_by_column])", "d": "Devuelve la matriz como una fila.", "ad": "Matriz o referencia que se devolverá como fila.!Indica si se deben omitir determinados tipos de valores. De forma predeterminada, no se omite ningún valor.!Examina la matriz por columna. De forma predeterminada, la matriz se examina por fila."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Envuelve un vector de fila o columna después de un número especificado de valores.", "ad": "El vector o referencia para ajustar.!El número máximo de valores por columna.!El valor con el que rellenar. El valor predeterminado es #N/A."}, "TAKE": {"a": "(matriz, filas, [columnas])", "d": "Devuelve filas o columnas desde el inicio o el final de la matriz.", "ad": "Matriz de la que se van a tomar filas o columnas.!Número de filas que se van a tomar. Un valor negativo toma desde el final de la matriz.!Número de columnas que se van a tomar. Un valor negativo toma desde el final de la matriz."}, "DROP": {"a": "(matriz, filas, [columnas])", "d": "Quita filas o columnas desde el inicio o el final de la matriz.", "ad": "Matriz de la que se van a tomar filas o columnas.!Número de filas que se van a quitar. Un valor negativo quita desde el final de la matriz.!Número de columnas que se van a quitar. Un valor negativo quita desde el final de la matriz."}, "SEQUENCE": {"a": "(filas, [columnas], [inicio], [paso])", "d": "Devuelve una secuencia de números", "ad": "El número de filas que se va a devolver!El número de columnas que se va a devolver!El primer número de la secuencia!La cantidad en que se incrementa cada valor subsiguiente de la secuencia"}, "EXPAND": {"a": "(matriz, filas, [columnas], [pad_with])", "d": "Expande una matriz a las dimensiones especificadas.", "ad": "Matriz que se va a expandir. Número de filas de la matriz expandida. Si falta, no se expandirán las filas.!Número de columnas de la matriz expandida.!Si falta, las columnas no se expandirán.!Valor con el que se va a rellenar. El valor predeterminado es #N/A."}, "XMATCH": {"a": "(valor_buscado, matriz_buscada, [modo_de_coincidencia], [modo_de_búsqueda])", "d": "Devuelve la posición relativa de un elemento en una matriz. De forma predeterminada, se requiere una coincidencia exacta", "ad": "Es el valor que se busca!Es la matriz o el rango en el que se busca!Especifica cómo se busca el valor_buscado comparándolo con los valores de la matriz_buscada!Especifica el modo de búsqueda que se va a utilizar. De forma predeterminada, se usará una búsqueda del primero al último"}, "FILTER": {"a": "(array, include, [if_empty])", "d": "Filtrar un rango o matriz", "ad": "el rango o matriz que se va a filtrar!una matriz de valores booleanos donde TRUE representa una fila o columna que se va a conservar!se devuelve si no se conserva ningún elemento"}, "ARRAYTOTEXT": {"a": "(array, [format])", "d": "Devuelve una representación de texto de una array", "ad": "the array para representar como el formato text!the del text"}, "SORT": {"a": "(matriz, [ordenar_índice], [criterio_ordenación], [por_col])", "d": "Ordena un rango o matriz", "ad": "el rango o matriz para ordenar!un número que indica la fila o columna para ordenar por!un número que indica el orden deseado; 1 para orden ascendente (valor predeterminado), -1 para orden descendente!un valor lógico que indica la dirección de ordenación deseada: FALSE para ordenar por fila (valor predeterminado), TRUE para ordenar por columna"}, "SORTBY": {"a": "(matriz, por_matriz, [orden], ...)", "d": "Ordena un rango o una matriz basándose en los valores de una matriz o un rango correspondiente", "ad": "El rango o la matriz que se va a ordenar!El rango o la matriz en los que se basa el orden!Número que indica el orden deseado; 1 para orden ascendente (valor predeterminado), -1 para orden descendente"}, "GETPIVOTDATA": {"a": "(camp_datos; tabla_dinámica; [campo]; [elemento]; ...)", "d": "Extrae datos almacenados en una tabla dinámica", "ad": "es el nombre del campo de datos del que extraer los datos!es una referencia a una celda o rango de celdas en la tabla dinámica que contiene los datos que desea recuperar!campo al que hacer referencia!elemento de campo al que hacer referencia"}, "IMPORTRANGE": {"a": "(url_hoja_cálculo; cadena_intervalo)", "d": "Importa un intervalo de celdas de una hoja de cálculo específica.", "ad": "URL de la hoja de la que se van a importar los datos!el intervalo que se desea importar"}}