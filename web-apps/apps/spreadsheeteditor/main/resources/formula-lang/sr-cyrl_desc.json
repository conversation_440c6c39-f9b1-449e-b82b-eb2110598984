{"DATE": {"a": "(година; месец; дан)", "d": "Враћа број који представља датум у коду датум-време", "ad": "је број од 1900 или 1904 (у зависности од датумског система радне свеске) до 9999!је број од 1 до 12 који представља месец у години!је број од 1 до 31 који представља дан у месецу"}, "DATEDIF": {"a": "(почетни-датум; крајњи-датум; јединица)", "d": "Враћа разлику између два датума (почетни датум и крајњи датум), на основу специфицираног интервала (јединице)", "ad": "је датум који представља први, или почетни датум датог периода!је датум који представља последњи, или завршни, датум периода!је врста информација које желите да се врати"}, "DATEVALUE": {"a": "(датум_текст)", "d": "Претвара датум у облику текста у број који представља датум у коду датум-време", "ad": "је текст који представља датум у Spreadsheet Editor-у формату датума, између 1/1/1900 или 1/1/1904 (у зависности од датумског система радне свеске) и 12/31/9999"}, "DAY": {"a": "(серијски_број)", "d": "Враћа дан у месецу, број од 1 до 31.", "ad": "је број у датумско-временском коду који користи Spreadsheet Editor"}, "DAYS": {"a": "(крајњи_датум; почетни_датум)", "d": "Враћа број дана између два датума.", "ad": "почетни_датум и крајњи_датум су два датума између којих желите да знате број дана!почетни_датум и крајњи_датуме су два датума између којих желите да знате број дана"}, "DAYS360": {"a": "(почетни_датум; крајњи_датум; [метод])", "d": "Враћа број дана између два датума на основу 360-дневног календара (дванаест 30-дневних месеци)", "ad": "почетни_датум и крајњи_датум су два датума између којих желите да знате број дана!почетни_датум а крајњи_датум су два датума између којих желите да знате број дана!је логичка вредност која одређује метод обрачуна: U.S. (NASD) = НЕТАЧНО или изостављено; Европски = ТАЧНО."}, "EDATE": {"a": "(почетни_датум; месеци)", "d": "Враћа серијски број датума који је наведени број месеци пре или после почетног датума", "ad": "је серијски број датума који представља датум почетка!је број месеци пре или после почетног_датума"}, "EOMONTH": {"a": "(почетни_датум; месеци)", "d": "Враћа серијски број последњег дана месеца пре или после наведеног броја месеци", "ad": "је серијски број датума који представља датум почетка!је број месеци пре или после почетног_датума"}, "HOUR": {"a": "(серијски_број)", "d": "Враћа сат као број од 0 (12:00 AM) до 23 (11:00 PM).", "ad": "је број у коду датума и времена који користи Spreadsheet Editor, или текст у временском формату, као што је 16:48:00 или 4:48:00 PM"}, "ISOWEEKNUM": {"a": "(датум)", "d": "Враћа ISO број недеље у години за дати датум", "ad": "је датумско-временски код који користи Spreadsheet Editor за израчунавање датума и времена"}, "MINUTE": {"a": "(серијски_број)", "d": "Вра<PERSON>а минут, број од 0 до 59.", "ad": "је број у датумско-временском коду који користи Spreadsheet Editor или текст у временском формату, као што је 16:48:00 или 4:48:00 PM"}, "MONTH": {"a": "(серијски_број)", "d": "Враћа месец, број од 1 (ја<PERSON><PERSON><PERSON><PERSON>) до 12 (деце<PERSON>б<PERSON><PERSON>).", "ad": "је број у датумско-временском коду који користи Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(почетни_датум; крајњи_датум; [празници])", "d": "Враћа број целих радних дана између два датума", "ad": "је серијски број датума који представља датум почетка!је серијски број датума који представља крајњи датум!је опциони скуп једног или више серијских бројева датума да се искључе из радног календара, као што су државни и савезни празници и плутајући празници"}, "NETWORKDAYS.INTL": {"a": "(почетни_датум; крајњи_датум; [викенд]; [празници])", "d": "Враћа број целих радних дана између два датума са прилагођеним параметрима викенда", "ad": "је серијски број датума који представља датум почетка!је серијски број датума који представља крајњи датум!је број или низ који одређује када се викенди јављају!је опциони скуп једног или више серијских бројева датума да се искључе из радног календара, као што су државни и савезни празници и плутајући празници"}, "NOW": {"a": "()", "d": "Враћа тренутни датум и време форматирано као датум и време.", "ad": ""}, "SECOND": {"a": "(серијски_број)", "d": "Враћа секунду, број од 0 до 59.", "ad": "је број у датумско-временском коду који користи Spreadsheet Editor или текст у временском формату, као што је 16:48:23 или 4:48:47 PM"}, "TIME": {"a": "(сат; минута; секунда)", "d": "Претвара сате, минуте и секунде дате као бројеви у серијски број, форматиран са форматом времена", "ad": "је број од 0 до 23 који представља сат!је број од 0 до 59 који представља минут!је број од 0 до 59 који представља секунду"}, "TIMEVALUE": {"a": "(текст_времена)", "d": "Претвара текстуално време у серијски број за време, број од 0 (12:00:00 AM) до 0.999988426 (11:59:59 PM). Форматирајте број са форматом времена након уноса формуле", "ad": "је текстуални низ који даје време у било ком од Spreadsheet Editor временских формата (информације о датуму у низу се игноришу)"}, "TODAY": {"a": "()", "d": "Враћа тренутни датум форматиран као датум.", "ad": ""}, "WEEKDAY": {"a": "(серијски_број; [тип_повратка])", "d": "Враћа број од 1 до 7 који идентификује дан у недељи за дати датум.", "ad": "је број који представља датум!је број: за недељу=1 до суботе=7, користите 1; за понедељак=1 до недеље=7, користите 2; за понедељак=0 до недеље=6, користите 3"}, "WEEKNUM": {"a": "(серијски_број; [тип_повратка])", "d": "Враћа број недеље у години", "ad": "је датум-време код који користи Spreadsheet Editor за израчунавање датума и времена!је број (1 или 2) који одређује врсту повратне вредности"}, "WORKDAY": {"a": "(почетни_датум; дани; [празници])", "d": "Враћа серијски број датума пре или после наведеног броја радних дана", "ad": "је серијски број датума који представља датум почетка!је број дана који нису викенд и нису празник пре или после start_date!је опциони низ једног или више серијских бројева датума да се искључе из радног календара, као што су државни и савезни празници и плутајући празници"}, "WORKDAY.INTL": {"a": "(почетни_датум; дани; [викенд]; [празници])", "d": "Враћа серијски број датума пре или после наведеног броја радних дана са прилагођеним параметрима викенда", "ad": "је серијски број датума који представља датум почетка!је број дана који нису викенд и нису празник пре или после start_date!је број или низ који одређује када се викенди јављају!је опциони низ једног или више серијских бројева датума који се искључују из радног календара, као што су државни и савезни празници и плутајући празници"}, "YEAR": {"a": "(серијски_број)", "d": "Враћа годину датума, цео број у опсегу од 1900-9999.", "ad": "је број у датумско-временском коду који користи Spreadsheet Editor"}, "YEARFRAC": {"a": "(почетни_датум; крајњи_датум; [основа])", "d": "Враћа годишњи удео који представља број целих дана између почетни_датум и крајњи_датум", "ad": "је серијски број датума који представља датум почетка!је серијски датум број који представља крајњи датум!је врста основе за бројање дана коју треба користити"}, "BESSELI": {"a": "(x; n)", "d": "Враћа модификовану Bessel функцију In(x)", "ad": "је вредност на којој се процењује функција!је редослед Bessel функције"}, "BESSELJ": {"a": "(x; n)", "d": "Враћа Bessel функцију Jn(x)", "ad": "jе вредност на којој се процењује функција!је редослед Bessel функције"}, "BESSELK": {"a": "(x; n)", "d": "Враћа модификовану Bessel функцију Kn(x)", "ad": "је вредност на којој се процењује функција!је редослед функције"}, "BESSELY": {"a": "(x; n)", "d": "Враћа модификовану Bessel функцију Yn(x)", "ad": "је вредност на којој се процењује функција!је редослед функције"}, "BIN2DEC": {"a": "(број)", "d": "Конвертује бинарни број у децимални", "ad": "је бинарни број који желите да конвертујете"}, "BIN2HEX": {"a": "(број; [места])", "d": "Конвертује бинарни број у хексадецимални", "ad": "је бинарни број који желите да конвертујете!је број знакова за коришћење"}, "BIN2OCT": {"a": "(број; [места])", "d": "Конвертује бинарни број у октални", "ad": "је бинарни број који желите да конвертујете!је број знакова за коришћење"}, "BITAND": {"a": "(број1; број2)", "d": "Враћа битовски 'I' два броја", "ad": "је децимални приказ бинарног броја који желите да процените!је децимални приказ бинарног броја који желите да процените"}, "BITLSHIFT": {"a": "(број; помак)", "d": "Враћа број померан улево за помак битова", "ad": "је децимални приказ бинарног броја који желите да процените!је број битова за који желите да померите број улево"}, "BITOR": {"a": "(број1; број2)", "d": "Враћа битовски 'ИЛИ' два броја", "ad": "је децимални приказ бинарног броја који желите да процените!је децимални приказ бинарног броја који желите да процените"}, "BITRSHIFT": {"a": "(број; помак)", "d": "Враћа број померан удесно за помак битова", "ad": "је децимални приказ бинарног броја који желите да процените!је број битова за који желите да померите број удесно"}, "BITXOR": {"a": "(број1; број2)", "d": "Враћа битовски 'Ексклузивно ИЛИ' два броја", "ad": "је децимални приказ бинарног броја који желите да процените!је децимални приказ бинарног броја који желите да процените"}, "COMPLEX": {"a": "(реалан_број; и_број; [суфикс])", "d": "Конвертује реалне и имагинарне коефицијенте у комплексан број", "ad": "је реални коефицијент комплексног броја!је имагинарни коефицијент комплексног броја!је суфикс за имагинарну компоненту комплексног броја"}, "CONVERT": {"a": "(број; из_јединица; у_јединице)", "d": "Конвертује број из једног мерног система у други", "ad": "је вредност у фром_унитс да конвертујете!је јединица за број!је јединица за резултат"}, "DEC2BIN": {"a": "(број; [места])", "d": "Конвертује децимални број у бинарни", "ad": "је децимални цео број који желите да конвертујете!је број знакова који се користе"}, "DEC2HEX": {"a": "(број; [места])", "d": "Конвертује децимални број у хексадецимални", "ad": "је децимални цео број који желите да конвертујете!је број знакова који се користе"}, "DEC2OCT": {"a": "(број; [места])", "d": "Конвертује децимални број у октални", "ad": "је децимални цео број који желите да конвертујете!је број знакова који се користе"}, "DELTA": {"a": "(број1; [број2])", "d": "Тестира да ли су два броја једнака", "ad": "је први број!је други број"}, "ERF": {"a": "(доња_граница; [горња_граница])", "d": "Враћа грешку функције", "ad": "је доња граница за интеграцију ERF!је горња граница за интеграцију ERF"}, "ERF.PRECISE": {"a": "(X)", "d": "Враћа грешку функције", "ad": "је доња граница за интеграцију ERF.ТАЧНА"}, "ERFC": {"a": "(x)", "d": "Враћа комплементарну грешку функције", "ad": "је доња граница за интеграцију ERF"}, "ERFC.PRECISE": {"a": "(X)", "d": "Враћа комплементарну грешку функције", "ad": "је доња граница за интеграцију ERF.КОМПЛ.ТАЧНА"}, "GESTEP": {"a": "(број; [корак])", "d": "Тестира да ли је број већи од прага вредности", "ad": "је вредност за тестирање у односу на корак!је вредност прага"}, "HEX2BIN": {"a": "(број; [места])", "d": "Конвертује хексадецимални број у бинарни", "ad": "је хексадецимални број који желите да конвертујете!је број знакова који се користе"}, "HEX2DEC": {"a": "(број)", "d": "Конвертује хексадецимални број у децимални", "ad": "је хексадецимални број који желите да конвертујете"}, "HEX2OCT": {"a": "(број; [места])", "d": "Конвертује хексадецимални број у октални", "ad": "је хексадецимални број који желите да конвертујете!је број знакова који се користе"}, "IMABS": {"a": "(комплексни_број)", "d": "Враћа апсолутну вредност (модул) комплексног броја", "ad": "је комплексан број за који желите апсолутну вредност"}, "IMAGINARY": {"a": "(комплексни_број)", "d": "Враћа имагинарни коефицијент комплексног броја", "ad": "је комплексни број за који желите имагинарни коефицијент"}, "IMARGUMENT": {"a": "(комплексни_број)", "d": "Враћа аргумент q, угао изражен у радијанима", "ad": "је комплексни број за који желите аргумент"}, "IMCONJUGATE": {"a": "(комплексни_број)", "d": "Враћа комплексно коњуговање комплексног броја", "ad": "је комплексни број за који желите коњугат"}, "IMCOS": {"a": "(комплексни_број)", "d": "Враћа косинус комплексног броја", "ad": "је комплексни број за који желите косинус"}, "IMCOSH": {"a": "(комплексни_број)", "d": "Враћа хиперболични косинус комплексног броја", "ad": "је комплексни број за који желите хиперболички косинус"}, "IMCOT": {"a": "(комплексни_број)", "d": "Враћа котангенс комплексног броја", "ad": "је комплексни број за који желите котангенс"}, "IMCSC": {"a": "(комплексни_број)", "d": "Враћа косеканс комплексног броја", "ad": "је комплексни број за који желите косеканс"}, "IMCSCH": {"a": "(комплексни_број)", "d": "Враћа хиперболични косеканс комплексног броја", "ad": "је комплексни број за који желите хиперболички косеканс"}, "IMDIV": {"a": "(комплексни_broj1; комплексни_broj2)", "d": "Враћа количник два комплексна броја", "ad": "је сложени бројник или дивиденда!је сложени именилац или делилац"}, "IMEXP": {"a": "(комплексни_број)", "d": "Враћа експоненцијални комплексног броја", "ad": "је комплексни број за који желите експоненцијални"}, "IMLN": {"a": "(комплексни_број)", "d": "Враћа природни логаритам комплексног броја", "ad": "је комплексни број за који желите природни логаритам"}, "IMLOG10": {"a": "(комплексни_број)", "d": "Враћа логаритам по основи 10 комплексног броја", "ad": "је комплексни број за који желите заједнички логаритам"}, "IMLOG2": {"a": "(комплексни_број)", "d": "Враћа логаритам по основи 2 комплексног броја", "ad": "је комплексни број за који желите логаритам базе-2"}, "IMPOWER": {"a": "(комплексни_број; број)", "d": "Враћа комплексан број на целобројну потенцију", "ad": "је комплексни број који желите да подигнете на степен!је степен на коју желите да подигнете комплексни број"}, "IMPRODUCT": {"a": "(комплексни_broj1; [комплексни_broj2]; ...)", "d": "Враћа производ од 1 до 255 комплексних бројева", "ad": "Inumber1, Inumber2,... су од 1 до 255 комплексних бројева за множење."}, "IMREAL": {"a": "(комплексни_број)", "d": "Враћа реални коефицијент комплексног броја", "ad": "је комплексни број за који желите реални коефицијент"}, "IMSEC": {"a": "(комплексни_број)", "d": "Враћа секанс комплексног броја", "ad": "је комплексни број за који желите секант"}, "IMSECH": {"a": "(комплексни_број)", "d": "Враћа хиперболични секанс комплексног броја", "ad": "је комплексни број за који желите хиперболички секанс"}, "IMSIN": {"a": "(комплексни_број)", "d": "Враћа синус комплексног броја", "ad": "је комплексни број за који желите синус"}, "IMSINH": {"a": "(комплексни_број)", "d": "Враћа хиперболични синус комплексног броја", "ad": "је комплексни број за који желите хиперболички синус"}, "IMSQRT": {"a": "(комплексни_број)", "d": "Враћа квадратни корен комплексног броја", "ad": "је комплексни број за који желите квадратни корен"}, "IMSUB": {"a": "(комплексни_broj1; комплексни_broj2)", "d": "Враћа разлику два комплексна броја", "ad": "је комплексни број од којег треба одузети inumber2!је комплексни број који треба одузети од inumber1"}, "IMSUM": {"a": "(комплексни_broj1; [комплексни_broj2]; ...)", "d": "Враћа збир комплексних бројева", "ad": "су од 1 до 255 сложених бројева за додавање"}, "IMTAN": {"a": "(комплексни_број)", "d": "Враћа тангенс комплексног броја", "ad": "је комплексни број за који желите тангенту"}, "OCT2BIN": {"a": "(број; [места])", "d": "Претвара октални број у бинарни", "ad": "је октални број који желите да конвертујете!је број знакова за коришћење"}, "OCT2DEC": {"a": "(број)", "d": "Претвара октални број у децимални", "ad": "је октални број који желите да конвертујете"}, "OCT2HEX": {"a": "(број; [места])", "d": "Претвара октални број у хексадецимални", "ad": "је октални број који желите да конвертујете!је број знакова за коришћење"}, "DAVERAGE": {"a": "(база_података; поље; критеријум)", "d": "Израчунава просечне вредности у колони у листи или бази података које задовољавају наведене услове", "ad": "је опсег ћелија које чине листу или базу података. База података је листа сродних података!је или ознака колоне у двоструким наводницима или број који представља позицију колоне на листи!је опсег ћелија који садржи услове које одредите. Опсег укључује ознаку колоне и једну ћелију испод ознаке за услов"}, "DCOUNT": {"a": "(база_података; поље; критеријум)", "d": "Броји ћелије које садрже бројеве у пољу (колони) записа у бази података које задовољавају наведене услове", "ad": "је опсег ћелија које чине листу или базу података. База података је листа сродних података! је или ознака колоне у двоструким наводницима или број који представља позицију колоне на листи!је опсег ћелија који садржи услове које одредите. Опсег укључује ознаку колоне и једну ћелију испод ознаке за услов"}, "DCOUNTA": {"a": "(база_података; поље; критеријум)", "d": "Броји не-празне ћелије у пољу (колони) записа у бази података које задовољавају наведене услове", "ad": "је опсег ћелија које чине листу или базу података. База података је листа сродних података!је или ознака колоне у двоструким наводницима или број који представља позицију колоне на листи!је опсег ћелија који садржи услове које одредите. Опсег укључује ознаку колоне и једну ћелију испод ознаке за услов"}, "DGET": {"a": "(база_података; поље; критеријум)", "d": "Извлачи из базе података један запис који задовољава наведене услове", "ad": "је опсег ћелија које чине листу или базу података. База података је листа сродних података!је или ознака колоне у двоструким наводницима или број који представља позицију колоне на листи!је опсег ћелија који садржи услове које одредите. Опсег укључује ознаку колоне и једну ћелију испод ознаке за услов"}, "DMAX": {"a": "(база_података; поље; критеријум)", "d": "Враћа највећи број у пољу (колони) записа у бази података који задовољава наведене услове", "ad": "је опсег ћелија које чине листу или базу података. База података је листа сродних података!је или ознака колоне у двоструким наводницима или број који представља позицију колоне на листи!је опсег ћелија који садржи услове које одредите. Опсег укључује ознаку колоне и једну ћелију испод ознаке за услов"}, "DMIN": {"a": "(база_података; поље; критеријум)", "d": "Враћа најмањи број у пољу (колони) записа у бази података који задовољава наведене услове", "ad": "је опсег ћелија које чине листу или базу података. База података је листа сродних података!је или ознака колоне у двоструким наводницима или број који представља позицију колоне на листи!је опсег ћелија који садржи услове које одредите. Опсег укључује ознаку колоне и једну ћелију испод ознаке за услов"}, "DPRODUCT": {"a": "(база_података; поље; критеријум)", "d": "Множи вредности у пољу (колони) записа у бази података које задовољавају наведене услове", "ad": "је опсег ћелија које чине листу или базу података. База података је листа сродних података!је или ознака колоне у двоструким наводницима или број који представља позицију колоне на листи!је опсег ћелија који садржи услове које одредите. Опсег укључује ознаку колоне и једну ћелију испод ознаке за услов"}, "DSTDEV": {"a": "(база_података; поље; критеријум)", "d": "Процена стандардне девијације на основу узорка изабраних уноса у бази података", "ad": "је опсег ћелија које чине листу или базу података. База података је листа сродних података!је или ознака колоне у двоструким наводницима или број који представља позицију колоне на листи!је опсег ћелија који садржи услове које одредите. Опсег укључује ознаку колоне и једну ћелију испод ознаке за услов"}, "DSTDEVP": {"a": "(база_података; поље; критеријум)", "d": "Израчунава стандардну девијацију на основу целе популације изабраних уноса у бази података", "ad": "је опсег ћелија које чине листу или базу података. База података је листа сродних података!је или ознака колоне у двоструким наводницима или број који представља позицију колоне на листи!је опсег ћелија који садржи услове које одредите. Опсег укључује ознаку колоне и једну ћелију испод ознаке за услов"}, "DSUM": {"a": "(база_података; поље; критеријум)", "d": "Сабира бројеве у пољу (колони) записа у бази података које задовољавају наведене услове", "ad": "је опсег ћелија које чине листу или базу података. База података је листа сродних података!је или ознака колоне у двоструким наводницима или број који представља позицију колоне на листи!је опсег ћелија који садржи услове које одредите. Опсег укључује ознаку колоне и једну ћелију испод ознаке за услов"}, "DVAR": {"a": "(база_података; поље; критеријум)", "d": "Процена варијансе на основу узорка изабраних уноса у бази података", "ad": "је опсег ћелија које чине листу или базу података. База података је листа сродних података!је или ознака колоне у двоструким наводницима или број који представља позицију колоне на листи!је опсег ћелија који садржи услове које одредите. Опсег укључује ознаку колоне и једну ћелију испод ознаке за услов"}, "DVARP": {"a": "(база_података; поље; критеријум)", "d": "Израчунава варијансу на основу целе популације изабраних уноса у бази података", "ad": "је опсег ћелија које чине листу или базу података. База података је листа сродних података!је или ознака колоне у двоструким наводницима или број који представља позицију колоне на листи!је опсег ћелија који садржи услове које одредите. Опсег укључује ознаку колоне и једну ћелију испод ознаке за услов"}, "CHAR": {"a": "(број)", "d": "Враћа карактер специфициран бројем кода из скупа карактера вашег рачунара", "ad": "је број између 1 и 255 који одређује који знак желите"}, "CLEAN": {"a": "(текст)", "d": "Уклања све нештампане карактере из текста", "ad": "је било која информација о радном листу из које желите уклонити знакове који се не могу штампати"}, "CODE": {"a": "(текст)", "d": "Враћа нумерички код за први карактер у текстуалном низу, у скупу карактера који користи ваш рачунар", "ad": "је текст за који желите код првог знака"}, "CONCATENATE": {"a": "(текст1; [текст2]; ...)", "d": "Спаја више текстуалних низова у један текстуални низ", "ad": "су од 1 до 125 текстуалних низова који се спајају у један текстуални низ и могу бити текстуални низови, бројеви или референце са једном ћелијом"}, "CONCAT": {"a": "(текст1; ...)", "d": "Конкатенира листу или опсег текстуалних низова", "ad": "су од 1 до 254 текстуалних низова или опсега који се придружују једном текстуалном низу"}, "DOLLAR": {"a": "(број; [децимале])", "d": "Претвара број у текст користећи формат валуте", "ad": "је број, референца на ћелију која садржи број, или формула која се процењује на број!је број цифара десно од децималне тачке. Број се заокружује по потреби; ако је изостављен, Децимале = 2"}, "EXACT": {"a": "(текст1; текст2)", "d": "Проверава да ли су два текстуална низа идентична и враћа TRUE (ТАЧНО) или FALSE (НЕТАЧНО). EXACT разликује велика и мала слова", "ad": "је први текстуални низ!је други текстуални низ"}, "FIND": {"a": "(тражи_текст; унутар_текста; [почетни_број])", "d": "Враћа почетну позицију једног текстуалног низа унутар другог текстуалног низа. FIND разликује велика и мала слова", "ad": "је текст који желите да пронађете. Користите двоструке наводнике (празан текст) за подударање са првим знаком у Унутар_текста; џокер знакови нису дозвољени!је текст који садржи текст који желите да пронађете!спецификује знак на коме треба да започне претрагу. Први знак Унутар_текста је знак број 1. Ако је изостављен, Почетни_број = 1"}, "FINDB": {"a": "(низ-1; низ-2; [почетна_позиција])", "d": "Проналази наведени подниз (низ-1) унутар низа (низ-2) и намењен је за језике са скупом двоструких бајтова (DBCS) као што су јапански, кинески, корејски итд.", "ad": "је текст који желите да пронађете. Користите двоструке наводнике (празан текст) да се подударају са првим знаком у string-2; џокер знакови нису дозвољени!је текст који садржи текст који желите да пронађете!спецификује карактер на коме треба да започне претрагу. Први знак у string-2 је знак број 1. Ако је изостављен, start-pos = 1"}, "FIXED": {"a": "(број; [децимале]; [без_зареза])", "d": "Заокружује број на наведени број децимала и враћа резултат као текст са или без зареза", "ad": "је број који желите да заокружите и претворите у текст!је број цифара десно од децималне тачке. Ако је изостављен, Decimale = 2!е логичка вредност: не приказују зарезе у враћеном тексту = ТАЧНО; прикажи зарезе у враћеном тексту = НЕТАЧНО или изостављен"}, "LEFT": {"a": "(текст; [број_карактера])", "d": "Враћа наведени број карактера с почетка текстуалног низа", "ad": "је текстуални низ који садржи знакове које желите да издвојите!одређује колико знакова желите да ЛЕВО издвојите; 1 ако је изостављен"}, "LEFTB": {"a": "(низ; [број_карактера])", "d": "Извлачи подниз из наведеног низа почевши од левог карактера и намењен је за језике који користе скуп двоструких бајтова (DBCS) као што су јапански, кинески, корејски итд.", "ad": "је текстуални низ који садржи знакове које желите да издвојите!специфицира колико знакова желите да ЛЕВОB издвоји; 1 ако је изостављен"}, "LEN": {"a": "(текст)", "d": "Враћа број карактера у текстуалном низу", "ad": "је текст чију дужину желите да пронађете. Размаци се рачунају као знакови"}, "LENB": {"a": "(низ)", "d": "Анализира наведени низ и враћа број карактера које садржи, а намењен је за језике који користе скуп двоструких бајтова (DBCS) као што су јапански, кинески, корејски итд.", "ad": "је текст чију дужину желите да пронађете. Размаци се рачунају као знакови"}, "LOWER": {"a": "(текст)", "d": "Претвара сва слова у текстуалном низу у мала слова", "ad": "је текст који желите да конвертујете у мала слова. Знакови у тексту који нису слова се не мењају"}, "MID": {"a": "(текст; почетни_број; број_карактера)", "d": "Враћа карактере из средине текстуалног низа, према почетној позицији и дужини", "ad": "је текстуални низ из којег желите да извучете знакове!је положај првог знака који желите да извучете. Први знак у тексту је 1!одређује колико знакова да се врати из текста"}, "MIDB": {"a": "(низ; почетна_позиција; број_карактера)", "d": "Извлачи карактере из наведеног низа почевши од било које позиције и намењен је за језике који користе скуп двоструких бајтова (DBCS) као што су јапански, кинески, корејски итд.", "ad": "је текстуални низ из којег желите да извучете знакове!је положај првог знака који желите да извучете. Први знак у Низу је 1!одређује колико знакова да се врати из Низа"}, "NUMBERVALUE": {"a": "(текст; [децимални_раздвојник]; [раздвојник_група])", "d": "Претвара текст у број на независни начин од регионалних поставки", "ad": "је низ који представља број који желите да конвертујете!је знак који се користи као децимални низ!је знак који се користи као групни сепаратор у низу"}, "PROPER": {"a": "(текст)", "d": "Претвара текстуални низ у правилан облик; прво слово у свакој речи у велико, а сва остала слова у мала", "ad": "је текст стављен у наводнике, формула која враћа текст, или референца на ћелију која садржи текст за делимично писање великих слова"}, "REPLACE": {"a": "(стари_текст; почетни_број; број_карактера; нови_текст)", "d": "Замењује део текстуалног низа другим текстуалним низом", "ad": "је текст у којем желите да замените неке знакове!је положај карактера у Стари_текст који желите да замените са Нови_текст!је број знакова у Стари_текст који желите да замените!је текст који ће заменити знакове у Стари_текст"}, "REPLACEB": {"a": "(низ-1; почетна_позиција; број_карактера; низ-2)", "d": "Замењује скуп карактера, на основу броја карактера и почетне позиције коју наведете, новим скупом карактера и намењен је за језике који користе скуп двоструких бајтова (DBCS) као што су јапански, кинески, корејски итд.", "ad": "је текст у коме желите да замените неке знакове!је положај карактера у Низ-1 који желите да замените са Низ-2!је број знакова у Низ-1 који желите да замените!је текст који ће заменити знакове у Низ-1"}, "REPT": {"a": "(текст; број_понављања)", "d": "Понавља текст одређени број пута. Користите ПОНОВИ за попуњавање ћелије одређеним бројем инстанци текстуалног низа", "ad": "је текст који желите да поновите!је позитиван број који одређује колико пута се понавља текст"}, "RIGHT": {"a": "(текст; [број_карактера])", "d": "Враћа наведени број карактера с краја текстуалног низа", "ad": "је текстуални низ који садржи знакове које желите да издвојите!спецификује колико знакова желите да издвојите, 1 ако је изостављен"}, "RIGHTB": {"a": "(низ; [број_карактера])", "d": "Извлачи подниз из низа почевши од десног карактера, на основу наведеног броја карактера и намењен је за језике који користе скуп двоструких бајтова (DBCS) као што су јапански, кинески, корејски итд.", "ad": "је текстуални низ који садржи знакове које желите да издвојите!спецификује колико знакова желите да издвојите, 1 ако је изостављен"}, "SEARCH": {"a": "(тражи_текст; унутар_текста; [почетни_број])", "d": "Враћа број карактера на којем се први пут налази одређени карактер или текстуални низ, читајући с лева на десно (није осетљиво на велика и мала слова)", "ad": "је текст који желите да пронађете. Можете користити ? и * џокер знакове; Користите ~? i ~* да пронађете ? i * знакове!је текст у којем желите да тражите Тражи_текст!је број карактера у Унутар_текста, рачунајући са леве стране, на којој желите да почнете претрагу. Ако се изостави, користи се 1"}, "SEARCHB": {"a": "(низ-1; низ-2; [почетна_позиција])", "d": "Враћа локацију наведеног подниза у низу и намењен је за језике који користе скуп двоструких бајтова (DBCS) као што су јапански, кинески, корејски итд.", "ad": "је текст који желите да пронађете. Можете користити ? и * џокер знакове; Користите ~? и ~* да пронађете ? и * знакове!је текст у којем желите да тражите Низ-1! је број карактера у Низ-2, рачунајући са леве стране, на којој желите да почнете претрагу. Ако се изостави, користи се 1"}, "SUBSTITUTE": {"a": "(текст; стари_текст; нови_текст; [број_инстанце])", "d": "Замењује постојећи текст новим текстом у текстуалном низу", "ad": "је текст или референца на ћелију која садржи текст у којем желите да замените карактере!је постојећи текст који желите да замените. Ако се случај Стари_текст не поклапа са случајем текста, ЗАМЕНА неће заменити текст!је текст којим желите да замените Стари_текст!одређује коју појаву Стари_текст желите да замените. Ако се изостави, свака инстанца Стари_текст се замењује"}, "T": {"a": "(вредност)", "d": "Проверава да ли је вредност текст и враћа текст ако јесте, или враћа двоструке наводнике (празан текст) ако није", "ad": "је вредност за тестирање"}, "TEXT": {"a": "(вредност; формат_текста)", "d": "Претвара вредност у текст у одређеном бројевном формату", "ad": "је број, формула која се процењује на нумеричку вредност или референца на ћелију која садржи нумеричку вредност!је формат броја у текстуалном облику из Категорије поља на картици Број у дијалогу Форматирање ћелија"}, "TEXTJOIN": {"a": "(раздвојник; игнориши_празно; текст1; ...)", "d": "Конкатенира листу или опсег текстуалних низова користећи раздвојник", "ad": "знак или низ за уметање измедју сваке текстуалне ставке!ако ТАЧНО(подразумевано), игнорише празне ћелије!су од 1 до 252 текстуалних низова или опсега који се придружују"}, "TRIM": {"a": "(текст)", "d": "Уклања све размаке из текстуалног низа осим једноструких размака између речи", "ad": "је текст из којег желите да се уклоне размаци"}, "UNICHAR": {"a": "(број)", "d": "Враћа Unicode карактер референциран датом нумеричком вредношћу", "ad": "je Unicode број који представља знак"}, "UNICODE": {"a": "(текст)", "d": "Враћа број (кодну тачку) која одговара првом карактеру текста", "ad": "је знак за који желите Unicode вредност"}, "UPPER": {"a": "(текст)", "d": "Претвара текстуални низ у сва велика слова", "ad": "је текст који желите претворити у велика слова, референцу или текстуални низ"}, "VALUE": {"a": "(текст)", "d": "Претвара текстуални низ који представља број у број", "ad": "је текст стављен под наводнике или референца на ћелију која садржи текст који желите да конвертујете"}, "AVEDEV": {"a": "(број1; [број2]; ...)", "d": "Враћа просечну вредност апсолутних девијација тачака података од њихове средине. Аргументи могу бити бројеви или имена, низови или референце које садрже бројеве", "ad": "су од 1 до 255 аргумената за које желите просек апсолутних одступања"}, "AVERAGE": {"a": "(број1; [број2]; ...)", "d": "Враћа просечну (аритметичку средину) својих аргумената, који могу бити бројеви или имена, низови или референце које садрже бројеве", "ad": "су од 1 до 255 нумеричких аргумената за које желите просек"}, "AVERAGEA": {"a": "(вредност1; [вредност2]; ...)", "d": "Враћа просечну (аритметичку средину) својих аргумената, евалуирајући текст и FALSE (НЕТАЧНО) у аргументима као 0; TRUE (ТАЧНО) se evaluira kao 1. се евалуира као 1. Аргументи могу бити бројеви, имена, низови или референце", "ad": "су од 1 до 255 аргумената за које желите просек"}, "AVERAGEIF": {"a": "(опсег; критеријум; [просечни_опсег])", "d": "Проналази просек (аритметичку средину) за ћелије специфициране одређеним условом или критеријумом", "ad": "је опсег ћелија које желите проценити!је услов или критеријум у облику броја, израза или текста који дефинише које ћелије ће се користити за проналажење просека!су стварне ћелије које ће се користити за проналажење просека. Ако се изостави, користе се ћелије у опсегу"}, "AVERAGEIFS": {"a": "(просечни_опсег; опсег_критеријума; критеријум; ...)", "d": "Проналази просек (аритметичку средину) за ћелије специфициране одређеним скупом услова или критеријума", "ad": "су стварне ћелије које ће се користити за проналажење просека.! је опсег ћелија које желите проценити за одређени услов!је услов или критеријум у облику броја, израза или текста који дефинише које ћелије ће се користити за проналажење просека"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Враћа кумулативну бета функцију вероватноће", "ad": "је вредност између A и B на којој се процењује функција!је параметар за дистрибуцију и мора бити већи од 0!је параметар за дистрибуцију и мора бити већи од 0!је опциона доња граница за интервал од x. Ако је изостављен, А = 0!је опциона горња граница интервала од x. Ако је изостављен, B = 1"}, "BETAINV": {"a": "(вероватноћа; alfa; beta; [A]; [B])", "d": "Враћа инверзну вредност кумулативне бета функције вероватноће (BETADIST)", "ad": "је вероватноћа повезана са бета дистрибуцијом!је параметар за дистрибуцију и мора бити већи од 0!је параметар за дистрибуцију и мора бити већи од 0!је опциона доња граница интервала x. Ако је изостављен, А = 0!је опциона горња граница интервала од x. Ако је изостављен, B = 1"}, "BETA.DIST": {"a": "(x; alfa; beta; кумулативно; [A]; [B])", "d": "Враћа бета функцију расподеле вероватноће", "ad": "је вредност између A и B на којој се процењује функција!је параметар за дистрибуцију и мора бити већи од 0!је параметар за дистрибуцију и мора бити већи од 0!је логичка вредност: за кумулативну функцију дистрибуције, користити ТАЧНО; за функцију густине вероватноће, користите НЕТАЧНО!jе опциона доња граница интервала x. Ако је изостављен, А = 0! је опциона горња граница интервала од x. Ако је изостављен, B = 1"}, "BETA.INV": {"a": "(вероватноћа; alfa; beta; [A]; [B])", "d": "Враћа инверзну вредност кумулативне бета густине вероватноће (BETA.DIST)", "ad": "је вероватноћа повезана са бета дистрибуцијом!је параметар за дистрибуцију и мора бити већи од 0!је параметар за дистрибуцију и мора бити већи од 0!је опциона доња граница интервалаa x. Ако је изостављен, A = 0!je опциона горња граница интервала од x. Ако је изостављен, B = 1"}, "BINOMDIST": {"a": "(број_с; покушаји; вероватноћа_с; кумулативно)", "d": "Враћа појединачну биномну расподелу вероватноће", "ad": "је број успеха у суђењима!је број независних суђења!да ли је вероватноћа успеха на сваком суђењу!је логичка вредност: за функцију кумулативне дистрибуције, користите ТАЧНО; за функцију масе вероватноће, користите НЕТАЧНО"}, "BINOM.DIST": {"a": "(број_с; покушаји; вероватноћа_с; кумулативно)", "d": "Враћа појединачну биномну расподелу вероватноће", "ad": "је број успеха у суђењима!је број независних суђења!да ли је вероватноћа успеха на сваком суђењу!је логичка вредност: за функцију кумулативне дистрибуције, користите ТАЧНО; за функцију масе вероватноће, користите НЕТАЧНО"}, "BINOM.DIST.RANGE": {"a": "(покушаји; вероватноћа_с; број_с; [број_c2])", "d": "Враћа вероватноћу резултата покушаја користећи биномну расподелу", "ad": "је број независних суђења!је вероватноћа успеха на сваком суђењу!је број успеха у суђењима!Ако је под условом да ова функција враћа вероватноћу да ће број успешних суђења бити између број_с и број_с2"}, "BINOM.INV": {"a": "(покушаји; вероватноћа_с; alfa)", "d": "Враћа најмању вредност за коју је кумулативна биномна расподела већа или једнака критеријуму", "ad": "је број Берноуллијевих суђења!је вероватноћа успеха на сваком суђењу, број између 0 и 1 укључујући!је вредност критеријума, број између 0 и 1"}, "CHIDIST": {"a": "(x; степени_слободе)", "d": "Враћа вероватноћу хи-квадрат расподеле са десним репом", "ad": "је вредност на којој желите да процените дистрибуцију, ненегативан број!је број степени слободе, број између 1 и 10^10, искључујући 10^10"}, "CHIINV": {"a": "(вероватноћа; степени_слободе)", "d": "Враћа инверзну вредност хи-квадрат расподеле са десним репом", "ad": "је вероватноћа повезана са хи-квадратном дистрибуцијом, вредност између 0 и 1 укључујући!је број степени слободе, број између 1 и 10^10, искључујући 10^10"}, "CHITEST": {"a": "(стварни_опсег; очекивани_опсег)", "d": "Враћа тест независности: вредност из хи-квадрат расподеле за статистику и одговарајуће степене слободе", "ad": "је опсег података који садржи запажања за тестирање у односу на очекиване вредности!је опсег података који садржи однос производа редова и колона укупног износа"}, "CHISQ.DIST": {"a": "(x; степени_слободе; кумулативно)", "d": "Враћа вероватноћу хи-квадрат расподеле са левим репом", "ad": "је вредност на којој желите да процените дистрибуцију, ненегативан број!је број степени слободе, број између 1 и 10^10, искључујући 10^10!je логичка вредност за функцију да врати: функција кумулативне дистрибуције = ТАЧНО; функција густине вероватноће = НЕТАЧНО"}, "CHISQ.DIST.RT": {"a": "(x; степени_слободе)", "d": "Враћа вероватноћу хи-квадрат расподеле са десним репом", "ad": "је вредност на којој желите да процените дистрибуцију, ненегативан број!је број степени слободе, број између 1 и 10^10, искључујући 10^10"}, "CHISQ.INV": {"a": "(вероватноћа; степени_слободе)", "d": "Враћа инверзну вредност хи-квадрат расподеле са левим репом", "ad": "је вероватноћа повезана са хи-квадратном дистрибуцијом, вредност између 0 и 1 укључујући!је број степени слободе, број између 1 и 10^10, искључујући 10^10"}, "CHISQ.INV.RT": {"a": "(вероватноћа; степени_слободе)", "d": "Враћа инверзну вредност хи-квадрат расподеле са десним репом", "ad": "је вероватноћа повезана са хи-квадратном дистрибуцијом, вредност између 0 и 1 укључујући!је број степени слободе, број између 1 и 10^10, искључујући 10^10"}, "CHISQ.TEST": {"a": "(стварни_опсег; очекивани_опсег)", "d": "Враћа тест независности: вредност из хи-квадрат расподеле за статистику и одговарајуће степене слободе", "ad": "је опсег података који садржи запажања за тестирање у односу на очекиване вредности!је опсег података који садржи однос производа редова и колона укупног износа"}, "CONFIDENCE": {"a": "(alfa; стандардна_девијација; величина)", "d": "Враћа интервал поверења за средњу вредност популације, користећи нормалну расподелу", "ad": "је ниво значајности који се користи за израчунавање нивоа поузданости, број већи од 0 и мањи од 1!је стандардна девијација популације за опсег података и претпоставља се да је познат. Standard_dev мора бити већа од 0!је величина узорка"}, "CONFIDENCE.NORM": {"a": "(alfa; стандардна_девијација; величина)", "d": "Враћа интервал поверења за средњу вредност популације, користећи нормалну расподелу", "ad": "је ниво значајности који се користи за израчунавање нивоа поузданости, број већи од 0 и мањи од 1!је стандардна девијација популације за опсег података и претпоставља се да је познат. Standard_dev мора бити већа од 0!је величина узорка"}, "CONFIDENCE.T": {"a": "(alfa; стандардна_девијација; величина)", "d": "Враћа интервал поверења за средњу вредност популације, користећи Студентову Т расподелу", "ad": "је ниво значајности који се користи за израчунавање нивоа поузданости, број већи од 0 и мањи од 1!је стандардна девијација популације за опсег података и претпоставља се да је познат. Standard_dev мора бити већа од 0!је величина узорка"}, "CORREL": {"a": "(низ1; низ2)", "d": "Враћа коефицијент корелације између два скупа података", "ad": "је ћелијски опсег вредности. Вредности треба да буду бројеви, име<PERSON>, низови или референце које садрже бројеве!је други опсег ћелија вредности. Вредности треба да буду бројеви, имена, низови или референце које садрже бројеве"}, "COUNT": {"a": "(вредност1; [вредност2]; ...)", "d": "Броји број ћелија у опсегу које садрже бројеве", "ad": "су од 1 до 255 аргумената који могу да садрже или се односе на различите врсте података, али се рачунају само бројеви"}, "COUNTA": {"a": "(вредност1; [вредност2]; ...)", "d": "Броји број ћелија у опсегу које нису празне", "ad": "су од 1 до 255 аргумената који представљају вредности и ћелије које желите да бројите. Вредности могу бити било која врста информација"}, "COUNTBLANK": {"a": "(опсег)", "d": "Броји број празних ћелија у одређеном опсегу ћелија", "ad": "је опсег из којег желите да бројите празне ћелије"}, "COUNTIF": {"a": "(опсег; критеријум)", "d": "Броји број ћелија унутар опсега које испуњавају дати услов", "ad": "је опсег ћелија из којих желите да бројите непразне ћелије!је услов у облику броја, израза или текста који дефинише које ћелије ће се бројати"}, "COUNTIFS": {"a": "(опсег_критеријума; критеријум; ...)", "d": "Броји број ћелија одређених скупом услова или критеријума", "ad": "је опсег ћелија које желите проценити за одређено стање!је услов у облику броја, израза или текста који дефинише које ћелије ће се бројати"}, "COVAR": {"a": "(низ1; низ2)", "d": "Враћа коваријансу, просечну вредност производа одступања за сваки пар података у два скупа података", "ad": "је први опсег ћелија целих бројева и морају бити бројеви, низови или референце које садрже бројеве!је други опсег ћелија целих бројева и мора бити бројеви, низови или референце које садрже бројеве"}, "COVARIANCE.P": {"a": "(низ1; низ2)", "d": "Враћа популациону коваријансу, просечну вредност производа одступања за сваки пар података у два скупа података", "ad": "је први опсег ћелија целих бројева и морају бити бројеви, низови или референце које садрже бројеве!је други опсег ћелија целих бројева и мора бити бројеви, низови или референце које садрже бројеве"}, "COVARIANCE.S": {"a": "(низ1; низ2)", "d": "Враћа узорковану коваријансу, просечну вредност производа одступања за сваки пар података у два скупа података", "ad": "је први опсег ћелија целих бројева и морају бити бројеви, низови или референце које садрже бројеве!је други опсег ћелија целих бројева и мора бити бројеви, низови или референце које садрже бројеве"}, "CRITBINOM": {"a": "(покушаји; вероватноћа_с; alfa)", "d": "Враћа најмању вредност за коју је кумулативна биномна расподела већа или једнака критеријуму", "ad": "је број Берноуллијевих суђења!је вероватноћа успеха на сваком суђењу, број између 0 и 1 укључујући!је вредност критеријума, број између 0 и 1"}, "DEVSQ": {"a": "(број1; [број2]; ...)", "d": "Враћа збир квадрата одступања тачака података од њихове узорковане средње вредности", "ad": "су од 1 до 255 аргумената, или низ или референца низа, на којој желите да ODSTUPANJEKV израчуна"}, "EXPONDIST": {"a": "(x; ламбда; кумулативно)", "d": "Враћа експоненцијалну расподелу", "ad": "је вредност функције, ненегативан број!је вредност параметра, позитиван број!је логичка вредност за функцију да се врати: кумулативна функција дистрибуције = ТАЧНО; функција густине вероватноће = НЕТАЧНО"}, "EXPON.DIST": {"a": "(x; ламбда; кумулативно)", "d": "Враћа експоненцијалну расподелу", "ad": "је вредност функције, ненегативан број!је вредност параметра, позитиван број!је логичка вредност за функцију да се врати: кумулативна функција дистрибуције = ТАЧНО; функција густине вероватноће = НЕТАЧНО"}, "FDIST": {"a": "(x; степени_слободе1; степени_слободе2)", "d": "Враћа (десно-репну) F вероватноћу расподеле (степен различитости) за два скупа података", "ad": "је вредност на којој се процењује функција, ненегативан број!је бројилац степени слободе, број између 1 и 10^10, искључујући 10^10!је именилац степени слободе, број између 1 и 10^10, искључујући 10^10"}, "FINV": {"a": "(вероватноћа; степени_слободе1; степени_слободе2)", "d": "Враћа инверзну вредност (десно-репне) F вероватноће расподеле: ако је p = FDIST(x,...), тада је FINV(p,...) = x", "ad": "је вероватноћа повезана са Ф кумулативном дистрибуцијом, број између 0 и 1 искључујући!je бројилац степени слободе, број између 1 и 10^10, искључујући 10^10!je именилац степени слободе, број између 1 и 10^10, искључујући 10^10"}, "FTEST": {"a": "(низ1; низ2)", "d": "Враћа резултат F-теста, двоструку вероватноћу да варијансе у Низ1 i Низ2 нису значајно различите", "ad": "је први низ или опсег података и могу бити бројеви или имена, низови или референце које садрже бројеве (празнине се занемарују)!је други низ или опсег података и могу бити бројеви или имена, низови или референце које садрже бројеве (празнине се занемарују)"}, "F.DIST": {"a": "(x; степени_слободе1; степени_слободе2; кумулативно)", "d": "Враћа (лево-репну) F вероватноћу расподеле (степен различитости) за два скупа података", "ad": "је вредност на којој се процењује функција, ненегативан број!је бројилац степени слободе, број између 1 и 10^10, искључујући 10^10!је именилац степени слободе, број између 1 и 10^10, искључујући 10^10!је логичка вредност за функцију да врати: функција кумулативне дистрибуције = ТАЧНО; функција густине вероватноће = НЕТАЧНО"}, "F.DIST.RT": {"a": "(x; степени_слободе1; степени_слободе2)", "d": "Враћа (десно-репну) F вероватноћу расподеле (степен различитости) за два скупа података", "ad": "је вредност на којој се процењује функција, ненегативан број!је бројилац степени слободе, број између 1 и 10^10, искључујући 10^10!је именилац степени слободе, број између 1 и 10^10, искључујући 10^10"}, "F.INV": {"a": "(вероватноћа; степени_слободе1; степени_слободе2)", "d": "Враћа инверзну вредност (лево-репне) F вероватноће расподеле: ако је p = F.DIST(x,...), тада је F.INV(p,...) = x", "ad": "је вероватноћа повезана са F кумулативном дистрибуцијом, број између 0 и 1 укључујући!je бројилац степени слободе, број између 1 и 10^10, искључујући 10^10!je именитељ степени слободе, број између 1 и 10^10, искључујући 10^10"}, "F.INV.RT": {"a": "(вероватноћа; степени_слободе1; степени_слободе2)", "d": "Враћа инверзну вредност (десно-репне) F вероватноће расподеле: ако је p = F.DIST.RT(x,...), тада је F.INV.RT(p,...) = x", "ad": "је вероватноћа повезана са F кумулативном дистрибуцијом, број између 0 и 1 искључујући!je бројилац степени слободе, број између 1 и 10^10, искључујући 10^10!je именилац степени слободе, број између 1 и 10^10, искључујући 10^10"}, "F.TEST": {"a": "(низ1; низ2)", "d": "Враћа резултат F-теста, двоструку вероватноћу да варијансе у Низ1 и Низ2 нису значајно различите", "ad": "је први низ или опсег података и могу бити бројеви или имена, низови или референце које садрже бројеве (празнине се занемарују)!је други низ или опсег података и могу бити бројеви или имена, низови или референце које садрже бројеве (празнине се занемарују)"}, "FISHER": {"a": "(x)", "d": "Враћа Фишерову трансформацију", "ad": "је вредност за коју желите трансформацију, број између -1 и 1, искључујући -1 и 1"}, "FISHERINV": {"a": "(y)", "d": "Враћа инверзну вредност Фишерове трансформације: ако је y = ФИШЕР(x), тада je ФИШЕРINV(y) = x", "ad": "је вредност за коју желите да извршите обрнуто од трансформације"}, "FORECAST": {"a": "(x; познате_y; познате_x)", "d": "Израчунава или предвиђа будућу вредност дуж линеарне тенденције користећи постојеће вредности", "ad": "је тачка података за коју желите да предвидите вредност и мора бити нумеричка вредност!је зависни низ или опсег нумеричких података!је независни низ или опсег нумеричких података. Варијанса Known_x-a не сме бити нула"}, "FORECAST.ETS": {"a": "(циљни_датум; вредности; временска_линија; [сезоналност]; [довршење_података]; [агрегација])", "d": "Враћа предвиђену вредност за одређени будући циљни датум користећи методу експоненцијалног изглађивања.", "ad": "је тачка података за коју Spreadsheet Editor предвиђа вредност. Требало би да настави са обрасцем вредности у временској линији.! је низ или опсег нумеричких података које предвиђате.!је независни низ или опсег нумеричких података. Датуми у временској линији морају имати доследан корак између њих и не могу бити нула.!је опциона нумеричка вредност која означава дужину сезонског обрасца. Подразумевана вредност 1 означава сезоналност која се аутоматски детектује.!је опциона вредност за руковање вредностима које недостају. Подразумевана вредност 1 замењује вредности које недостају интерполацијом, а 0 их замењује нулама.!је опциона нумеричка вредност за агрегирање више вредности са истом временском ознаком. Ако је празно, Spreadsheet Editor упросечује вредности"}, "FORECAST.ETS.CONFINT": {"a": "(циљни_датум; вредности; временска_линија; [ниво_поузданости]; [сезоналност]; [довршење_података]; [агрегација])", "d": "Враћа интервал поузданости за предвиђену вредност на одређеном циљног датуму.", "ad": "је тачка података за коју Spreadsheet Editor предвиђа вредност. Требало би да настави образац вредности у временској линији.! је низ или опсег нумеричких података које предвиђате.!је независни низ или опсег нумеричких података. Датуми у временској линији морају имати доследан корак између њих и не могу бити нула.!је број између 0 и 1 који показује ниво поузданости за израчунати интервал поузданости. Основна вредност је .95.!је опциона нумеричка вредност која означава дужину сезонског обрасца. Подразумевана вредност 1 означава сезоналност која се аутоматски детектује.!je opciona vrednost za rukovanje vrednostima koje nedostaju. Подразумевана вредност 1 замењује вредности које недостају интерполацијом, а 0 их замењује нулама.!је опциона нумеричка вредност за агрегирање више вредности са истом временском ознаком. Ако је празно, Spreadsheet Editor у упросечује вредности"}, "FORECAST.ETS.SEASONALITY": {"a": "(вредности; временска_линија; [довршење_података]; [агрегација])", "d": "Враћа дужину понављајућег обрасца који апликација детектује за одређени низ временских података.", "ad": "је низ или опсег нумеричких података које предвиђате.! је независни низ или опсег нумеричких података. Датуми у временској линији морају имати доследан корак између њих и не могу бити нула.! је опциона вредност за руковање вредностима које недостају. Подразумевана вредност 1 замењује вредности које недостају интерполацијом, а 0 их замењује нулама.!је опциона нумеричка вредност за агрегирање више вредности са истом временском ознаком. Ако је празно, Spreadsheet Editor упросечује вредности"}, "FORECAST.ETS.STAT": {"a": "(вредности; временска_линија; тип_статистике; [сезоналност]; [довршење_података]; [агрегација])", "d": "Враћа захтевану статистику за прогнозу.", "ad": "је низ или опсег нумеричких података које предвиђате.! је независни низ или опсег нумеричких података. Датуми у временској линији морају имати доследан корак између њих и не могу бити нула.!је број између 1 и 8, што указује на то која статистика Spreadsheet Editor ће се вратити за израчунату прогнозу.!је опциона нумеричка вредност која означава дужину сезонског обрасца. Подразумевана вредност 1 означава сезоналност која се аутоматски детектује.!је опциона вредност за руковање недостајућим вредностима. Подразумевана вредност 1 замењује вредности које недостају интерполацијом, а 0 их замењује нулама.!је опциона нумеричка вредност за агрегирање више вредности са истим временским печатом. Ако је празно, Spreadsheet Editor у упросечује вредности"}, "FORECAST.LINEAR": {"a": "(x; познате_y; познате_x)", "d": "Израчунава или предвиђа будућу вредност дуж линеарне тенденције користећи постојеће вредности", "ad": "је тачка података за коју желите да предвидите вредност и мора бити нумеричка вредност!је зависни низ или опсег нумеричких података!је независни низ или опсег нумеричких података. Варијанса Known_x не сме бити нула"}, "FREQUENCY": {"a": "(низ_података; низ_интервала)", "d": "Израчунава колико често се вредности појављују унутар опсега вредности и затим враћа вертикални низ бројева који има један елемент више од низа интервала", "ad": "је низ или референца на скуп вредности за које желите да бројите фреквенције (празнине и текст се игноришу)!је низ или референца на интервале у које желите да групишете вредности у низ_података"}, "GAMMA": {"a": "(x)", "d": "Враћа вредност Гама функције", "ad": "је вредност за коју желите да израчунате Гама"}, "GAMMADIST": {"a": "(x; alfa; beta; кумулативно)", "d": "Враћа гамма расподелу", "ad": "је вредност на којој желите да процените дистрибуцију, ненегативан број!је параметар за дистрибуцију, позитиван број!је параметар за дистрибуцију, позитиван број. Ако је бета = 1, ГАМАDIST враћа стандардну гама дистрибуцију!је логичка вредност: вратити кумулативну функцију дистрибуције = ТАЧНО; вратити функцију масе вероватноће = НЕТАЧНО или изостављено"}, "GAMMA.DIST": {"a": "(x; alfa; beta; кумулативно)", "d": "Враћа гамма расподелу", "ad": "је вредност на којој желите да процените дистрибуцију, ненегативан број!је параметар за дистрибуцију, позитиван број!је параметар за дистрибуцију, позитиван број. Ако је бета = 1, ГАМА.DIST враћа стандардну гама дистрибуцију!је логичка вредност: вратити кумулативну функцију дистрибуције = ТАЧНО; вратити функцију масе вероватноће = НЕТАЧНО или изостављено"}, "GAMMAINV": {"a": "(вероватноћа; alfa; beta)", "d": "Враћа инверзну вредност кумулативне гамма расподеле: ако је p = ГАМАDIST(x,...), tada je ГАМАINV(p,...) = x", "ad": "је вероватноћа повезана са гама дистрибуцијом, број између 0 и 1, укључујући!је параметар за дистрибуцију, позитиван број!је параметар за дистрибуцију, позитиван број. Ако је бета = 1, ГАМАINV враћа инверзну од стандардне гама дистрибуције"}, "GAMMA.INV": {"a": "(вероватноћа; alfa; beta)", "d": "Враћа инверзну вредност кумулативне гамма расподеле: ако је p = ГАМА.DIST(x,...), тада је ГАМА.INV(p,...) = x", "ad": "је вероватноћа повезана са гама дистрибуцијом, број између 0 и 1, укључујући!је параметар за дистрибуцију, позитиван број!је параметар за дистрибуцију, позитиван број. Ако је бета = 1, ГАМА.INV враћа инверзну од стандардне гама дистрибуције"}, "GAMMALN": {"a": "(x)", "d": "Враћа природни логаритам гамма функције", "ad": "је вредност за коју желите да израчунате ГАМАLN, позитиван број"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Враћа природни логаритам гамма функције", "ad": "је вредност за коју желите да израчунате ГАМАЛН.ТАЧНО, позитиван број"}, "GAUSS": {"a": "(x)", "d": "Враћа 0,5 мање од стандардне нормалне кумулативне расподеле", "ad": "је вредност за коју желите дистрибуцију"}, "GEOMEAN": {"a": "(број1; [број2]; ...)", "d": "Враћа геометријску средину низа или опсега позитивних нумеричких података", "ad": "су од 1 до 255 бројева или имена, низова или референци које садрже бројеве за које желите средњу вредност"}, "GROWTH": {"a": "(познате_y; [познате_x]; [нове_x]; [константа])", "d": "Враћа бројеве у експоненцијалном тренду раста који одговарају познатим тачкама података", "ad": "је скуп y-вредности које већ знате у односу y = b*m^x, низ или опсег позитивних бројева!је опциони скуп x-вредности које можда већ знате у односу y = b*m^x, низ или опсег исте величине као Познате_y!су нове x-вредности за које желите да РАСТ врати одговарајуће y-вредности!је логичка вредност: константа б се израчунава нормално ако је Конст = ТАЧНО; б је подешен једнак 1 ако је Конст = НЕТАЧНО или изостављено"}, "HARMEAN": {"a": "(број1; [број2]; ...)", "d": "Враћа хармонијску средину скупа позитивних бројева: реципрочну вредност аритметичке средине реципрочних вредности", "ad": "су од 1 до 255 бројева или имена, низова или референци које садрже бројеве за које желите хармоничну средину"}, "HYPGEOM.DIST": {"a": "(узорак_с; број_узорака; популација_с; број_популације; кумулативно)", "d": "Враћа хипергеометријску расподелу", "ad": "је број успеха у узорку!је величина узорка!је број успеха у популацији!је величина популације!је логичка вредност: за кумулативну функцију дистрибуције, користите ТАЧНО; за функцију густине вероватноће, користите НЕТАЧНО"}, "HYPGEOMDIST": {"a": "(узорак_с; број_узорака; популација_с; број_популације)", "d": "Враћа хипергеометријску расподелу", "ad": "је број успеха у узорку!је величина узорка!је број успеха у популацији!је величина популације"}, "INTERCEPT": {"a": "(познате_y; познате_x)", "d": "Израчунава тачку на којој ће се линија пресећи са y-осом користећи линију најбоље прилагођене регресије нацртане кроз познате x-вредности и y-вредности", "ad": "је зависни скуп запажања или података и могу бити бројеви или имена, низови или референце које садрже бројеве!је независан скуп запажања или података и могу бити бројеви или имена, низови или референце које садрже бројеве"}, "KURT": {"a": "(број1; [број2]; ...)", "d": "Враћа куртосис скупа података", "ad": "су од 1 до 255 бројева или имена, низова или референци које садрже бројеве за које желите куртозис"}, "LARGE": {"a": "(низ; k)", "d": "Враћа к-ту највећу вредност у скупу података. На пример, пети највећи број", "ad": "је низ или опсег података за које желите да одредите к-ту највећу вредност!је позиција (од највећег) у низу или опсегу ћелија вредности за повратак"}, "LINEST": {"a": "(познате_y; [познате_x]; [константа]; [статистике]])", "d": "Враћа статистике које описују линеарни тренд који одговара познатим тачкама података, помоћу цртања праве линије користећи методу најмањих квадрата", "ad": "је скуп y-вредности које већ знате у односу y = b*m^x, низ или опсег позитивних бројева!је опциони скуп x-вредности које можда већ знате у односу y = b*m^x, низ или опсег исте величине као Позната_y!су нове x-вредности за које желите да РАСТ врати одговарајуће y-вредности!је логичка вредност: константа б се израчунава нормално ако је Конст = ТАЧНО; б је подешен једнак 1 ако је Конст = НЕТАЧНО или изостављено"}, "LOGEST": {"a": "(познате_y; [познате_x]; [константа]; [статистике])", "d": "Враћа статистике које описују експоненцијалну криву која одговара познатим тачкама података", "ad": "је скуп y-вредности које већ знате у односу y = b*m^x!је опциони скуп x-вредности које можда већ знате у односу y = b*m^x!је логичка вредност: константа б се израчунава нормално ако је Конст = ТАЧНО или изостављена; б је подешен једнак 1 ако је Конст = НЕТАЧНО!је логичка вредност: вратити додатну регресијску статистику = ТАЧНО; повратак м-коефицијенти и константа б = НЕТАЧНО или изостављено"}, "LOGINV": {"a": "(вероватноћа; средња; стандардна_девијација)", "d": "Враћа инверзну вредност логнормалне кумулативне функције расподеле x, где је ln(x) нормално дистрибуирана са параметрима Средња и Стандардна_девијација", "ad": "је вероватноћа повезана са логнормалном дистрибуцијом, број између 0 и 1, укључујући!је средња вредност ln(x)!је стандардна девијација ln(x), позитиван број"}, "LOGNORM.DIST": {"a": "(x; средња; стандардна_девијација; кумулативно)", "d": "Враћа лог-нормалну расподелу за x, где је ln(x) нормално дистрибуирано са параметрима Средња и Стандардна_девијација", "ad": "је вредност на којој се процењује функција, позитиван број!је средња вредност ln(x)!је стандардна девијација ln(x), позитиван број!је логичка вредност: за кумулативну функцију дистрибуције, користите ТАЧНО; за функцију густине вероватноће, користите НЕТАЧНО"}, "LOGNORM.INV": {"a": "(вероватноћа; средња; стандардна_девијација)", "d": "Враћа инверзну вредност кумулативне лог-нормалне функције расподеле за x, где је ln(x) нормално дистрибуирано са параметрима Средња и Стандардна_девијација", "ad": "је вероватноћа повезана са логнормалном дистрибуцијом, број између 0 i 1, укључујући!је средња вредност ln(x)!је стандардна девијација ln(x), позитиван број"}, "LOGNORMDIST": {"a": "(x; средња; стандардна_девијација)", "d": "Враћа кумулативну лог-нормалну расподелу за x, где је ln(x) нормално дистрибуирано са параметрима Средња и Стандардна_девијација", "ad": "је вредност на којој се процењује функција, позитиван број!је средња вредност ln(x)!је стандардна девијација ln(x), позитиван број"}, "MAX": {"a": "(број1; [број2]; ...)", "d": "Враћа највећу вредност у скупу вредности. Занемарује логичке вредности и текст", "ad": "су од 1 до 255 бројеви, празне ћелије, логичке вредности или текстуални бројеви за које желите максимум"}, "MAXA": {"a": "(вредност1; [вредност2]; ...)", "d": "Враћа највећу вредност у скупу вредности. Не занемарује логичке вредности и текст", "ad": "су од 1 до 255 бројеви, празне ћелије, логичке вредности или текстуални бројеви за које желите максимум"}, "MAXIFS": {"a": "(max_опсег; опсег_критеријума; критеријум; ...)", "d": "Враћа максималну вредност међу ћелијама одређеним задатим скупом услова или критеријума", "ad": "ћелије у којима се одређује максимална вредност!је опсег ћелија које желите да процените за одређено стање!је услов или критеријум у облику броја, израза или текста који дефинише које ћелије ће бити укључене приликом одређивања максималне вредности"}, "MEDIAN": {"a": "(број1; [број2]; ...)", "d": "Враћа медијану, или број у средини скупа датих бројева", "ad": "су од 1 до 255 бројева или имена, низова или референци које садрже бројеве за које желите медијану"}, "MIN": {"a": "(број1; [број2]; ...)", "d": "Враћа најмањи број у скупу вредности. Занемарује логичке вредности и текст", "ad": "су од 1 до 255 бројеви, празне ћелије, логичке вредности или текстуални бројеви за које желите минимум"}, "MINA": {"a": "(вредност1; [вредност2]; ...)", "d": "Враћа најмању вредност у скупу вредности. Не занемарује логичке вредности и текст", "ad": "су од 1 до 255 бројеви, празне ћелије, логичке вредности или текстуални бројеви за које желите минимум"}, "MINIFS": {"a": "(min_опсег; опсег_критеријума; критеријум; ...)", "d": "Враћа минималну вредност међу ћелијама одређеним задатим скупом услова или критеријума", "ad": "ћелије у којима се одређује минимална вредност!је опсег ћелија које желите да процените за одређено стање!је услов или критеријуми у облику броја, израза или текста који дефинише које ћелије ће бити укључене приликом одређивања минималне вредности"}, "MODE": {"a": "(број1; [број2]; ...)", "d": "Враћа најчешће понављану вредност у низу или опсегу података", "ad": "су од 1 до 255 бројеви, или име<PERSON>, низови или референце које садрже бројеве за које желите режим"}, "MODE.MULT": {"a": "(број1; [број2]; ...)", "d": "Враћа вертикални низ најчешће понављаних вредности у низу или опсегу података. За хоризонтални низ, користите =ТРАНСПОНУЈ(MODE.MULT(број1,број2,...))", "ad": "су од 1 до 255 бројеви, или име<PERSON>, низови или референце које садрже бројеве за које желите режим"}, "MODE.SNGL": {"a": "(број1; [број2]; ...)", "d": "Враћа најчешће понављану вредност у низу или опсегу података", "ad": "су од 1 до 255 бројеви, или име<PERSON>, низови или референце које садрже бројеве за које желите режим"}, "NEGBINOM.DIST": {"a": "(број_неуспеха; број_успеха; вероватноћа_успеха; кумулативно)", "d": "Враћа негативну биномну расподелу, вероватноћу да ће бити број_неуспеха пре броја_успеха, са вероватноћом_успеха за успех", "ad": "је број неуспеха!је праг броја успеха!је вероватноћа успеха; број између 0 и 1!је логичка вредност: за кумулативну функцију дистрибуције, користите ТАЧНО; за функцију масе вероватноће, користите НЕТАЧНО"}, "NEGBINOMDIST": {"a": "(број_неуспеха; број_успеха; вероватноћа_успеха)", "d": "Враћа негативну биномну расподелу, вероватноћу да ће бити број_неуспеха пре броја_успеха, са вероватноћом_успеха за успех", "ad": "је број неуспеха!је праг броја успеха!је вероватноћа успеха; број између 0 и 1"}, "NORM.DIST": {"a": "(x; средња; стандардна_девијација; кумулативно)", "d": "Враћа нормалну расподелу за одређену средњу вредност и стандардну девијацију", "ad": "је вредност за коју желите дистрибуцију!је аритметичка средина расподеле!је стандардна девијација дистрибуције, позитиван број!је логичка вредност: за функцију кумулативне расподеле користите ТАЧНО; за функцију густине вероватноће, користите НЕТАЧНО"}, "NORMDIST": {"a": "(x; средња; стандардна_девијација; кумулативно)", "d": "Враћа нормалну кумулативну расподелу за одређену средњу вредност и стандардну девијацију", "ad": "је вредност за коју желите дистрибуцију!је аритметичка средина расподеле!је стандардна девијација дистрибуције, позитиван број!је логичка вредност: за функцију кумулативне расподеле користите ТАЧНО; за функцију густине вероватноће, користите НЕТАЧНО"}, "NORM.INV": {"a": "(вероватноћа; средња; стандардна_девијација)", "d": "Враћа инверзну вредност нормалне кумулативне расподеле за одређену средњу вредност и стандардну девијацију", "ad": "је вероватноћа која одговара нормалној дистрибуцији, број између 0 и 1 укључујући!је аритметичка средина дистрибуције!је стандардна девијација дистрибуције, позитиван број"}, "NORMINV": {"a": "(вероватноћа; средња; стандардна_девијација)", "d": "Враћа инверзну вредност нормалне кумулативне расподеле за одређену средњу вредност и стандардну девијацију", "ad": "је вероватноћа која одговара нормалној дистрибуцији, број између 0 и 1 укључујући!је аритметичка средина дистрибуције!је стандардна девијација дистрибуције, позитиван број"}, "NORM.S.DIST": {"a": "(z; кумулативно)", "d": "Враћа стандардну нормалну расподелу (има средњу вредност нула и стандардну девијацију један)", "ad": "је вредност за коју желите дистрибуцију!је логичка вредност за функцију да врати: кумулативна функција дистрибуције = ТАЧНО; функција густине вероватноће = НЕТАЧНО"}, "NORMSDIST": {"a": "(z)", "d": "Враћа стандардну нормалну кумулативну расподелу (има средњу вредност нула и стандардну девијацију један)", "ad": "је вредност за коју желите дистрибуцију"}, "NORM.S.INV": {"a": "(вероватноћа)", "d": "Враћа инверзну вредност стандардне нормалне кумулативне расподеле (има средњу вредност нула и стандардну девијацију један)", "ad": "је вероватноћа која одговара нормалној дистрибуцији, број између 0 и 1 укључујући"}, "NORMSINV": {"a": "(вероватноћа)", "d": "Враћа инверзну вредност стандардне нормалне кумулативне расподеле (има средњу вредност нула и стандардну девијацију један)", "ad": "је вероватноћа која одговара нормалној дистрибуцији, број између 0 и 1 укључујући"}, "PEARSON": {"a": "(низ1; низ2)", "d": "Враћа Пирсонов коефицијент корелације производа момената, r", "ad": "је скуп независних вредности!је скуп зависних вредности"}, "PERCENTILE": {"a": "(низ; k)", "d": "Враћа к-ти перцентил вредности у опсегу", "ad": "је низ или опсег података који дефинише релативни положај!је перцентилна вредност која је између 0 до 1, укључујући"}, "PERCENTILE.EXC": {"a": "(низ; k)", "d": "Враћа к-ти перцентил вредности у опсегу, где је к у опсегу од 0 до 1, искључујући границе", "ad": "је низ или опсег података који дефинише релативни положај!је перцентилна вредност која је између 0 до 1, укључујући"}, "PERCENTILE.INC": {"a": "(низ; k)", "d": "Враћа к-ти перцентил вредности у опсегу, где је к у опсегу од 0 до 1, укључујући границе", "ad": "је низ или опсег података који дефинише релативни положај!је перцентилна вредност која је између 0 до 1, укључујући"}, "PERCENTRANK": {"a": "(низ; x; [значајност])", "d": "Враћа ранг вредности у скупу података као проценат скупа података", "ad": "је низ или опсег података са нумеричким вредностима који дефинише релативни положај!је вредност за коју желите да знате ранг!је опциона вредност која идентификује број значајних цифара за враћени проценат, три цифре ако су изостављене (0.xxx%)"}, "PERCENTRANK.EXC": {"a": "(низ; x; [значајност])", "d": "Враћа ранг вредности у скупу података као проценат скупа података (0 до 1, искључујући границе)", "ad": "је низ или опсег података са нумеричким вредностима који дефинише релативни положај!је вредност за коју желите да знате ранг!је опциона вредност која идентификује број значајних цифара за враћени проценат, три цифре ако су изостављене (0.xxx%)"}, "PERCENTRANK.INC": {"a": "(низ; x; [значајност])", "d": "Враћа ранг вредности у скупу података као проценат скупа података (0 до 1, укључујући границе)", "ad": "је низ или опсег података са нумеричким вредностима који дефинише релативни положај!је вредност за коју желите да знате ранг!је опциона вредност која идентификује број значајних цифара за враћени проценат, три цифре ако су изостављене (0.xxx%)"}, "PERMUT": {"a": "(број; број_изабраних)", "d": "Враћа број пермутација за дати број објеката који се могу изабрати из укупног броја објеката", "ad": "је укупан број објеката!је број објеката у свакој пермутацији"}, "PERMUTATIONA": {"a": "(број; број_изабраних)", "d": "Враћа број пермутација за дати број објеката (са понављањем) који се могу изабрати из укупног броја објеката", "ad": "је укупан број објеката!је број објеката у свакој пермутацији"}, "PHI": {"a": "(x)", "d": "Враћа вредност функције густине за стандардну нормалну расподелу", "ad": "је број за који желите густину стандардне нормалне дистрибуције"}, "POISSON": {"a": "(x; средња; кумулативно)", "d": "Враћа Поасонову расподелу", "ad": "је број догађаја!је очекивана нумеричка вредност, позитиван број!је логичка вредност: за кумулативну Поасонову вероватноћу, користите ТАЧНО; за Поасонову функцију масе вероватноће, користите НЕТАЧНО"}, "POISSON.DIST": {"a": "(x; средња; кумулативно)", "d": "Враћа Поасонову расподелу", "ad": "је број догађаја!је очекивана нумеричка вредност, позитиван број!је логичка вредност: за кумулативну Поасонову вероватноћу, користите ТАЧНО; за Поасонову функцију масе вероватноће, користите НЕТАЧНО"}, "PROB": {"a": "(опсег_x; опсег_вероватноћа; доња_граница; [горња_граница])", "d": "Враћа вероватноћу да вредности у опсегу буду између две границе или једнаке доњој граници", "ad": "је опсег нумеричких вредности од x са којима постоје повезане вероватноће!је скуп вероватноћа повезаних са вредностима у X_опсегу, вредности између 0 и 1 и искључујући 0!је доња граница вредности за коју желите вероватноћу!је опциона горња граница на вредности. Ако је изостављен, ВЕРОВАТНОЋА враћа вероватноћу да су X_опсег вредности једнаке доња_граница"}, "QUARTILE": {"a": "(низ; квартил)", "d": "Враћа квартил скупа података", "ad": "је низ или опсег ћелија нумеричких вредности за које желите квартилну вредност!је број: минимална вредност = 0; 1. квартил = 1; средња вредност = 2; 3. квартил = 3; максимална вредност = 4"}, "QUARTILE.INC": {"a": "(низ; квартил)", "d": "Враћа квартил скупа података, базирано на перцентилним вредностима од 0 до 1, укључујући границе", "ad": "је низ или опсег ћелија нумеричких вредности за које желите квартилну вредност!је број: минимална вредност = 0; 1. квартил = 1; средња вредност = 2; 3. квартил = 3; максимална вредност = 4"}, "QUARTILE.EXC": {"a": "(низ; квартил)", "d": "Враћа квартил скупа података, базирано на перцентилним вредностима од 0 до 1, искључујући границе", "ad": "је низ или опсег ћелија нумеричких вредности за које желите квартилну вредност!је број: минимална вредност = 0; 1. квартил = 1; средња вредност = 2; 3. квартил = 3; максимална вредност = 4"}, "RANK": {"a": "(број; реф; [редослед])", "d": "Враћа ранг броја у листи бројева: његова величина у односу на друге вредности у листи", "ad": "је број за који желите да пронађете ранг!је низ или референца на листу бројева. Ненумеричке вредности се занемарују!је број: ранг на листи сортиран опадајуће = 0 или изостављен; ранг на листи сортиран растуће = било која вредност различита од нуле"}, "RANK.AVG": {"a": "(број; реф; [редослед])", "d": "Враћа ранг броја у листи бројева: његова величина у односу на друге вредности у листи; ако више вредности има исти ранг, враћа се просечан ранг", "ad": "је број за који желите да пронађете ранг!је низ или референца на листу бројева. Ненумеричке вредности се занемарују!је број: ранг на листи сортиран опадајуће = 0 или изостављен; ранг на листи сортиран растуће = било која вредност различита од нуле"}, "RANK.EQ": {"a": "(број; реф; [редослед])", "d": "Враћа ранг броја у листи бројева: његова величина у односу на друге вредности у листи; ако више вредности има исти ранг, враћа се највиши ранг тог скупа вредности", "ad": "је број за који желите да пронађете ранг!је низ или референца на листу бројева. Ненумеричке вредности се занемарују!је број: ранг на листи сортиран опадајуће = 0 или изостављен; ранг на листи сортиран растуће = било која вредност различита од нуле"}, "RSQ": {"a": "(познати_y; познати_x)", "d": "Враћа квадрат Пирсоновог коефицијента корелације производа момената кроз дате тачке података", "ad": "је низ или опсег тачака података и могу бити бројеви или имена, низови или референце које садрже бројеве!је низ или опсег тачака података и могу бити бројеви или имена, низови или референце које садрже бројеве"}, "SKEW": {"a": "(број1; [број2]; ...)", "d": "Враћа асиметрију расподеле: карактеризацију степена асиметрије расподеле око њене средње вредности", "ad": "су од 1 до 255 бројева или имена, низова или референци које садрже бројеве за које желите искривљеност"}, "SKEW.P": {"a": "(број1; [број2]; ...)", "d": "Враћа асиметрију расподеле базирану на популацији: карактеризацију степена асиметрије расподеле око њене средње вредности", "ad": "су од 1 до 254 бројева или имена, низова или референци које садрже бројеве за које желите искривљеност популације"}, "SLOPE": {"a": "(познати_y; познати_x)", "d": "Враћа нагиб линије линеарне регресије кроз дате тачке података", "ad": "је низ или опсег ћелија нумеричких зависних тачака података и могу бити бројеви или имена, низови или референце које садрже бројеве!је скуп независних тачака података и могу бити бројеви или имена, низови или референце које садрже бројеве"}, "SMALL": {"a": "(низ; k)", "d": "Враћа к-ту најмању вредност у скупу података. На пример, пети најмањи број", "ad": "је низ или опсег нумеричких података за које желите да одредите к-ту најмању вредност!је позиција (од најмање) у низу или опсегу вредности која се враћа"}, "STANDARDIZE": {"a": "(x; средња; стандардна_девијација)", "d": "Враћа нормализовану вредност из расподеле карактерисане средњом вредношћу и стандардном девијацијом", "ad": "је вредност коју желите да нормализујете!је аритметичка средина дистрибуције!је стандардна девијација дистрибуције, позитиван број"}, "STDEV": {"a": "(број1; [број2]; ...)", "d": "Процена стандардне девијације базирана на узорку (занемарује логичке вредности и текст у узорку)", "ad": "су од 1 до 255 бројева који одговарају узорку популације и могу бити бројеви или референце које садрже бројеве"}, "STDEV.P": {"a": "(број1; [број2]; ...)", "d": "Израчунава стандардну девијацију базирану на целокупној популацији датој као аргументи (занемарује логичке вредности и текст)", "ad": "су од 1 до 255 бројева који одговарају популацији и могу бити бројеви или референце које садрже бројеве"}, "STDEV.S": {"a": "(број1; [број2]; ...)", "d": "Процена стандардне девијације базирана на узорку (занемарује логичке вредности и текст у узорку)", "ad": "су од 1 до 255 бројева који одговарају узорку популације и могу бити бројеви или референце које садрже бројеве"}, "STDEVA": {"a": "(вредност1; [вредност2]; ...)", "d": "роцена стандардне девијације базирана на узорку, укључујући логичке вредности и текст. Текст и логичка вредност FALSE (НЕТАЧНО) имају вредност 0; логичка вредност TRUE (ТАЧНО) има вредност 1", "ad": "су од 1 до 255 вредности које одговарају узорку популације и могу бити вредности или имена или референце на вредности"}, "STDEVP": {"a": "(број1; [број2]; ...)", "d": "Израчунава стандардну девијацију базирану на целокупној популацији датој као аргументи (занемарује логичке вредности и текст)", "ad": "су од 1 до 255 бројева који одговарају популацији и могу бити бројеви или референце које садрже бројеве"}, "STDEVPA": {"a": "(вредност1; [вредност2]; ...)", "d": "Израчунава стандардну девијацију базирану на целокупној популацији, укључујући логичке вредности и текст. Текст и логичка вредност FALSE (НЕТАЧНО) имају вредност 0; логичка вредност TRUE (ТАЧНО) има вредност 1", "ad": "су од 1 до 255 вредности које одговарају популацији и могу бити вредности, имена, низови или референце које садрже вредности"}, "STEYX": {"a": "(poznati_y; poznati_x)", "d": "Враћа стандардну грешку предвиђене y-вредности за сваки x у регресији", "ad": "је низ или опсег зависних тачака података и могу бити бројеви или имена, низови или референце које садрже бројеве!је низ или опсег независних тачака података и могу бити бројеви или имена, низови или референце које садрже бројеве"}, "TDIST": {"a": "(x; степени_слободе; репови)", "d": "Враћа Студентову т-расподелу", "ad": "је нумеричка вредност на којој се процењује дистрибуција!је цео број који указује на број степени слободе који карактеришу дистрибуцију!спецификује број репова дистрибуције које треба вратити: једнострана дистрибуција = 1; двострана дистрибуција = 2"}, "TINV": {"a": "(вероватноћа; степени_слободе)", "d": "Враћа двострано инверзну вредност Студентове т-расподеле", "ad": "је вероватноћа повезана са двостраном Студентовом т-дистрибуцијом, број између 0 и 1 укључујући!је позитиван цео број који указује на број степени слободе за карактеризацију дистрибуције"}, "T.DIST": {"a": "(x; степени_слободе; кумулативно)", "d": "Враћа лево-репну Студентову т-расподелу", "ad": "је нумеричка вредност на којој се процењује дистрибуција!је цео број који указује на број степени слободе који карактеришу дистрибуцију!је логичка вредност: за кумулативну функцију дистрибуције, користите ТАЧНО; за функцију густине вероватноће, користите НЕТАЧНО"}, "T.DIST.2T": {"a": "(x; степени_слободе)", "d": "Враћа двострано Студентову т-расподелу", "ad": "је нумеричка вредност на којој се процењује дистрибуција!је цео број који указује на број степени слободе који карактеришу дистрибуцију"}, "T.DIST.RT": {"a": "(x; степени_слободе)", "d": "Враћа десно-репну Студентову т-расподелу", "ad": "је нумеричка вредност на којој се процењује дистрибуција!је цео број који указује на број степени слободе који карактеришу дистрибуцију"}, "T.INV": {"a": "(вероватноћа; степени_слободе)", "d": "Враћа лево-репну инверзну вредност Студентове т-расподеле", "ad": "је вероватноћа повезана са двокраком Студентовом т-дистрибуцијом, број између 0 и 1 укључујући!је позитиван цео број који указује на број степени слободе за карактеризацију дистрибуције"}, "T.INV.2T": {"a": "(вероватноћа; степени_слободе)", "d": "Враћа двострано инверзну вредност Студентове т-расподеле", "ad": "је вероватноћа повезана са двокраком Студентовом т-дистрибуцијом, број између 0 и 1 укључујући!је позитиван цео број који указује на број степени слободе за карактеризацију дистрибуције"}, "T.TEST": {"a": "(низ1; низ2; репови; тип)", "d": "Враћа вероватноћу повезану са Студентовим т-тестом", "ad": "је први скуп података!је други скуп података!одређује број репова дистрибуције за повратак: једнострана дистрибуција = 1; Двострана дистрибуција = 2! Је врста Т-теста: упарен = 1, два узорка једнака варијанса (хомоскедастично) = 2, два узорка неједнака варијанса = 3"}, "TREND": {"a": "(познати_y; [познати_x]; [нови_x]; [конст])", "d": "Враћа бројеве у линеарном тренду који одговара познатим подацима, користећи методу најмањих квадрата", "ad": "је опсег или низ y-вредности које већ знате у односу y = мx + б!је опциони опсег или низ x-вредности које знате у односу y = mx + b, низ исте величине као Познати_y!је опсег или низ нових x-вредности за које желите да TREND врати одговарајуће y-вредности!је логичка вредност: константа б се израчунава нормално ако је Konst = TAČNO ili izostavljena; b je postavljen jednak 0 ako je Konst = NETAČNO"}, "TRIMMEAN": {"a": "(низ; проценат)", "d": "Враћа средњу вредност унутрашњег дела скупа података", "ad": "је опсег или низ вредности за скраћивање и просек!је фракциони број тачака података које треба искључити са врха и дна скупа података"}, "TTEST": {"a": "(низ1; низ2; репови; тип)", "d": "Враћа вероватноћу повезану са Студентовим т-тестом", "ad": "је први скуп података!је други скуп података!одређује број репова дистрибуције за повратак: једнострана дистрибуција = 1; двострана дистрибуција = 2! Је врста Т-теста: упарен = 1, два узорка једнака варијанса (хомоскедастично) = 2, два узорка неједнака варијанса = 3"}, "VAR": {"a": "(број1; [број2]; ...)", "d": "Процена варијансе базирана на узорку (занемарује логичке вредности и текст у узорку)", "ad": "су од 1 до 255 нумеричких аргумената који одговарају узорку популације"}, "VAR.P": {"a": "(број1; [број2]; ...)", "d": "Израчунава варијансу базирану на целокупној популацији (занемарује логичке вредности и текст у популацији)", "ad": "су од 1 до 255 нумеричких аргумената који одговарају популацији"}, "VAR.S": {"a": "(број1; [број2]; ...)", "d": "Процена варијансе базирана на узорку (занемарује логичке вредности и текст у узорку)", "ad": "су од 1 до 255 нумеричких аргумената који одговарају узорку популације"}, "VARA": {"a": "(вредност1; [вредност2]; ...)", "d": "Процена варијансе базирана на узорку, укључујући логичке вредности и текст. Текст и логичка вредност FALSE (НЕТАЧНО) имају вредност 0; логичка вредност TRUE (ТАЧНО) има вредност 1", "ad": "су од 1 до 255 вредносних аргумената који одговарају узорку популације"}, "VARP": {"a": "(број1; [број2]; ...)", "d": "Израчунава варијансу базирану на целокупној популацији (занемарује логичке вредности и текст у популацији)", "ad": "су од 1 до 255 нумеричких аргумената који одговарају популацији"}, "VARPA": {"a": "(вредност1; [вредност2]; ...)", "d": "Израчунава варијансу базирану на целокупној популацији, укључујући логичке вредности и текст. Текст и логичка вредност FALSE (НЕТАЧНО) имају вредност 0; логичка вредност TRUE (ТАЧНО) има вредност 1", "ad": "су од 1 до 255 вредносних аргумената који одговарају популацији"}, "WEIBULL": {"a": "(x; alfa; beta; кумулативно)", "d": "<PERSON><PERSON>а<PERSON><PERSON>-ову расподелу", "ad": "је вредност на којој се процењује функција, ненегативан број!је параметар за дистрибуцију, позитиван број!је параметар за дистрибуцију, позитиван број!је логичка вредност: за кумулативну функцију дистрибуције, користите ТАЧНО; за функцију масе вероватноће, користите НЕТАЧНО"}, "WEIBULL.DIST": {"a": "(x; alpha; beta; кумулативно)", "d": "<PERSON><PERSON>а<PERSON><PERSON>-ову расподелу", "ad": "је вредност на којој се процењује функција, ненегативан број!је параметар за дистрибуцију, позитиван број!је параметар за дистрибуцију, позитиван број!је логичка вредност: за кумулативну функцију дистрибуције, користите ТАЧНО; за функцију масе вероватноће, користите НЕТАЧНО"}, "Z.TEST": {"a": "(низ; x; [сигма])", "d": "Враћа једнострану P-вредност z-теста", "ad": "је низ или опсег података према којима се тестира X!је вредност за тестирање!је популација (позната) стандардна девијација. Ако се изостави, користи се стандардна девијација узорка"}, "ZTEST": {"a": "(низ; x; [сигма])", "d": "Враћа једнострану P-вредност z-теста", "ad": "је низ или опсег података према којима се тестира X!је вредност за тестирање!је популација (позната) стандардна девијација. Ако се изостави, користи се стандардна девијација узорка"}, "ACCRINT": {"a": "(издавање; први_интерес; намирење; стопа; номинална_вредност; учесталост; [основа]; [метод_израчунавања])", "d": "Враћа обрачунате камате за хартију од вредности која плаћа периодичне камате.", "ad": "је датум издавања хартије од вредности, изражен као серијски број датума!је први датум интереса хартије од вредности, изражен као број серијског датума!је годишња стопа купона хартије од вредности!је номинална вредност хартије од вредности!је број исплата купона годишње!је врста дана бројања основа за коришћење!је логична вредност: обрачуната камата од датума издавања = ТАЧНО или изостављена; да израчунате од последњег датума исплате купона = НЕТАЧНО"}, "ACCRINTM": {"a": "(издавање; намирење; стопа; номинална_вредност; [основа])", "d": "Враћа обрачунате камате за хартију од вредности која плаћа камату на доспелост.", "ad": "је датум издавања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је годишња стопа купона хартије од вредности!је номинална вредност хартије од вредности!је врста дана рачунања основа за коришћење"}, "AMORDEGRC": {"a": "(трошак; датум_куповине; први_период; остатак_вредности; период; стопа; [основа])", "d": "Враћа прорачунату линеарну амортизацију средства за сваки рачуноводствени период.", "ad": "је цена средства!је датум куповине средства!је датум завршетка првог периода!је вредност спасавања на крају живота средства.! је период!је стопа амортизације!година_основа : 0 за годину од 360 дана, 1 за стварни, 3 за годину од 365 дана."}, "AMORLINC": {"a": "(трошак; датум_куповине; први_период; остатак_вредности; период; стопа; [основа])", "d": "Враћа прорачунату линеарну амортизацију средства за сваки рачуноводствени период.", "ad": "је цена средства!је датум куповине средства!је датум завршетка првог периода!је вредност спасавања на крају живота средства.! је период!је стопа амортизације!година_основа : 0 за годину од 360 дана, 1 за стварну, 3 за годину од 365 дана."}, "COUPDAYBS": {"a": "(намирење; доспелост; учесталост; [основа])", "d": "Враћа број дана од почетка купонског периода до датума намирења.", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је број исплата купона годишње!је врста основе за бројање дана коју треба искористити"}, "COUPDAYS": {"a": "(намирење; доспелост; учесталост; [основа])", "d": "Враћа број дана у купонском периоду који садржи датум намирења.", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је број исплата купона годишње!је врста основе за бројање дана коју треба искористити"}, "COUPDAYSNC": {"a": "(намирење; доспелост; учесталост; [основа])", "d": "Враћа број дана од датума намирења до следећег датума купона.", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је број исплата купона годишње!је врста основе за бројање дана коју треба искористити"}, "COUPNCD": {"a": "(намирење; доспелост; учесталост; [основа])", "d": "Враћа следећи датум купона након датума намирења.", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је број исплата купона годишње!је врста основе за бројање дана коју треба искористити"}, "COUPNUM": {"a": "(намирење; доспелост; учесталост; [основа])", "d": "Враћа број купона који се плаћају између датума намирења и датума доспелости.", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је број исплата купона годишње!је врста основе за бројање дана коју треба искористити"}, "COUPPCD": {"a": "(намирење; доспелост; учесталост; [основа])", "d": "Враћа претходни датум купона пре датума намирења.", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је број исплата купона годишње!је врста основе за бројање дана коју треба искористити"}, "CUMIPMT": {"a": "(стопа; бр_периода; садашња_вредност; почетни_период; крајњи_период; тип)", "d": "Враћа кумулативну исплаћену камату између два периода.", "ad": "је каматна стопа!је укупан број периода плаћања!је садашња вредност!је први период у обрачуну!је последњи период у обрачуну!је време плаћања"}, "CUMPRINC": {"a": "(стопа; бр_периода; садашња_вредност; почетни_период; крајњи_период; тип)", "d": "Враћа кумулативно исплаћени главницу зајма између два периода.", "ad": "је каматна стопа!је укупан број периода плаћања!је садашња вредност!је први период у обрачуну!је последњи период у обрачуну!је време плаћања"}, "DB": {"a": "(трошак; остатак_вредности; век; период; [месец])", "d": "Враћа амортизацију средства за одређени период користећи методу фиксног опадајућег салда.", "ad": "је почетни трошак средства!је преостала вредност на крају животног века средства!је број периода током којих се средство амортизује (понекад се назива и корисни век средства)!је период за који желите да израчунате амортизацију. Период мора да користи исте јединице као Живот!је број месеци у првој години. Ако је месец изостављен, претпоставља се да је 12"}, "DDB": {"a": "(трошак; остатак_вредности; век; период; [фактор])", "d": "Враћа амортизацију средства за одређени период користећи методу двоструко опадајућег салда или неки други метод који наведете.", "ad": "је почетни трошак средства!је преостала вредност на крају животног века средства!је број периода током којих се средство амортизује (понекад се назива и корисни век средства)!је период за који желите да израчунате амортизацију. Период мора да користи исте јединице као Живот!је стопа којом се салдо смањује. Ако је фактор изостављен, претпоставља се да је 2 (метода двоструко опадајућег салда)"}, "DISC": {"a": "(намирење; доспелост; цена; откуп; [основа])", "d": "Враћа дисконтну стопу за хартију од вредности.", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је цена хартије од вредности по $ 100 номинална вредност!је откупна вредност хартије од вредности по $100 номинална вредност!је тип осове за бројање дана које треба користити"}, "DOLLARDE": {"a": "(цен_фракциона; фракција)", "d": "Претвара цену долара, изражену као фракцију, у цену долара, изражену као децимални број.", "ad": "је број изражен као разломак! је цео број који се користи у имениоцу разломка"}, "DOLLARFR": {"a": "(цен_децимална; фракција)", "d": "Претвара цену долара, изражену као децимални број, у цену долара, изражену као фракцију.", "ad": "је децимални број! је цео број који се користи у имениоцу разломка"}, "DURATION": {"a": "(намирење; доспелост; купон; принос; учесталост; [основа])", "d": "Враћа годишње трајање хартије од вредности са периодичним каматним исплатама.", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је годишња стопа купона хартије од вредности!је годишњи принос хартије од вредности!је тип основе за бројање дана које треба користити"}, "EFFECT": {"a": "(номина<PERSON>на_стопа; бр_периода_год)", "d": "Враћа ефективну годишњу каматну стопу.", "ad": "је номинална каматна стопа!је број сложених периода годишње"}, "FV": {"a": "(стопа; бр_периода; уплата; [садашња_вредност]; [тип])", "d": "Враћа будућу вредност инвестиције на основу периодичних, константних уплата и константне каматне стопе.", "ad": "је каматна стопа по периоду. На пример, користите 6%/4 за кварталне исплате на 6% APR!је укупан број периода плаћања у инвестицији!је плаћање извршено у сваком периоду; Не може се променити током живота инвестиције!је садашња вредност, или паушални износ који сада вреди низ будућих исплата. Ако је изостављен, Pv = 0!је вредност која представља време плаћања: плаћање на почетку периода = 1; плаћање на крају периода = 0 или изостављено"}, "FVSCHEDULE": {"a": "(главница; распоред)", "d": "Враћа будућу вредност почетне главнице након примене низа сложених каматних стопа.", "ad": "је садашња вредност! је низ каматних стопа које се примењују"}, "INTRATE": {"a": "(намирење; доспелост; инвестиција; откуп; [основа])", "d": "Враћа каматну стопу за потпуно инвестирану хартију од вредности.", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је износ уложен у хартију од вредности!је износ који се прима по доспећу!је тип основе за бројање дана које треба користити"}, "IPMT": {"a": "(стопа; период; бр_периода; садашња_вредност; [будућа_вредност]; [тип])", "d": "Враћа каматну уплату за дати период инвестиције, на основу периодичних, константних уплата и константне каматне стопе.", "ad": "је каматна стопа по периоду. На пример, користите 6%/4 за кварталне исплате на 6% APR!је период за који желите да пронађете камату и мора бити у опсегу од 1 до Nper!је укупан број периода плаћања у инвестицији!је садашња вредност, или паушални износ који серија будућих плаћања вреди сада!је будућа вредност, или готовински салдо који желите да постигнете након последње уплате. Ако је изостављен, Fv = 0!је логичка вредност која представља време плаћања: на крају периода = 0 или изостављена, на почетку периода = 1"}, "IRR": {"a": "(вредности; [претпоставка])", "d": "Враћа унутрашњу стопу поврата за низ новчаних токова.", "ad": "је низ или референца на ћелије које садрже бројеве за које желите да израчунате унутрашњу стопу повратка!је број за који претпостављате да је близу резултата IRR-a; 0,1 (10 процената) ако се изостави"}, "ISPMT": {"a": "(стопа; период; бр_периода; садашња_вредност)", "d": "Враћа исплаћену камату током одређеног периода инвестиције.", "ad": "каматна стопа по периоду. На пример, користите 6%/4 за кварталне исплате на 6% APR!период за који желите да пронађете камату!број периода плаћања у инвестицији!паушални износ који је серија будућих исплата је управо сада"}, "MDURATION": {"a": "(намирење; доспелост; купон; принос; учесталост; [основа])", "d": "Враћа Маколи модификовано трајање за хартију од вредности са претпостављеном номиналном вредношћу од $100.", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је годишња стопа купона хартије од вредности!је годишњи принос хартије од вредности!је тип основе за бројање дана које треба искористити"}, "MIRR": {"a": "(вредности; стопа_финансирања; стопа_реинвестирања)", "d": "Враћа унутрашњу стопу поврата за низ периодичних новчаних токова, узимајући у обзир трошкове инвестиције и камату на реинвестирање новца.", "ad": "је низ или референца на ћелије које садрже бројеве који представљају низ плаћања (негативних) и прихода (позитивних) у редовним периодима!је каматна стопа коју плаћате на новац који се користи у новчаним токовима!је каматна стопа коју добијате на новчане токове док их реинвестирате"}, "NOMINAL": {"a": "(ефективна_стопа; бр_периода_год)", "d": "Враћа номиналну годишњу каматну стопу.", "ad": "је ефективна каматна стопа!је број сложених периода годишње"}, "NPER": {"a": "(стопа; уплата; садашња_вредност; [будућа_вредност]; [тип])", "d": "Враћа број периода за инвестицију на основу периодичних, константних уплата и константне каматне стопе.", "ad": "је каматна стопа по периоду. На пример, користите 6%/4 за кварталне исплате на 6% APR!је плаћање извршено у сваком периоду; то не може да се промени током живота инвестиције!је садашња вредност, или паушални износ који серија будућих плаћања вреди сада!је будућа вредност, или готовински салдо који желите да постигнете након што је извршена последња уплата. Ако се изостави, користи се нула!је логичка вредност: плаћање на почетку периода = 1; плаћање на крају периода = 0 или изостављено"}, "NPV": {"a": "(стопа; вредност1; [вредност2]; ...)", "d": "Враћа нето садашњу вредност инвестиције на основу дисконтне стопе и низа будућих уплата (негативне вредности) и прихода (позитивне вредности).", "ad": "је стопа попуста у дужини од једног периода!су од 1 до 254 исплате и приходи, подједнако распоређени у времену и јављају се на крају сваког периода"}, "ODDFPRICE": {"a": "(намирење; доспелост; емисија; први_купон; стопа; принос; откуп; учесталост; [основа])", "d": "Враћа цену по $100 номиналне вредности хартије од вредности са неправилним првим периодом.", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као број доспећа хартије од вредности!је први датум купона хартије од вредности, изражен као серијски број датума!је каматна стопа хартије од вредности!је годишњи принос хартије од вредности!!је вредност откупа хартије од вредности по $100 номинална вредност!је број исплата купона годишње!је тип основе за бројање дана које треба искористити"}, "ODDFYIELD": {"a": "(намирење; доспелост; емисија; први_купон; стопа; цена; откуп; учесталост; [основа])", "d": "Враћа принос хартије од вредности са неправилним првим периодом.", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски датум датума!је први датум купона хартије од вредности, изражен као серијски број датума!је каматна стопа хартије од вредности!је цена хартије од вредности!је вредност откупа хартије од вредности по $100 номинална вредност!је број исплата купона годишње!је тип основе за бројање дана које треба искористити"}, "ODDLPRICE": {"a": "(намирење; доспелост; последња_камата; стопа; принос; откуп; учесталост; [основа])", "d": "Враћа цену по $100 номиналне вредности хартије од вредности са неправилним последњим периодом.", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски датум датума!је каматна стопа хартије од вредности!је вредност откупа хартије од вредности по $100 номинална вредност!је број исплата купона годишње!је тип основе за бројање дана које треба искористити"}, "ODDLYIELD": {"a": "(намирење; доспелост; последња_камата; стопа; цена; откуп; учесталост; [основа])", "d": "Враћа принос хартије од вредности са неправилним последњим периодом.", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски датум датума!је каматна стопа хартије од вредности!је цена хартије од вредности!је вредност откупа хартије од вредности по $100 номинална вредност!је број исплата купона годишње!је тип основе за бројање дана које треба искористити"}, "PDURATION": {"a": "(стопа; садашња_вредност; будућа_вредност)", "d": "Враћа број периода потребних да инвестиција достигне одређену вредност.", "ad": "је каматна стопа по периоду.! је садашња вредност инвестиције!је жељена будућа вредност инвестиције"}, "PMT": {"a": "(стопа; бр_периода; садашња_вредност; [будућа_вредност]; [тип])", "d": "Израчунава уплату за зајам на основу константних уплата и константне каматне стопе.", "ad": "је каматна стопа по периоду за кредит. На пример, користите 6%/4 за кварталне исплате на 6% APR!је укупан број уплата за кредит!је садашња вредност: укупан износ који серија будућих плаћања вреди сада!је будућа вредност, или готовински салдо који желите да постигнете након што је извршена последња уплата, 0 (нула) ако је изостављена!је логична вредност: плаћање на почетку периода = 1; плаћање на крају периода = 0 или изостављено"}, "PPMT": {"a": "(стопа; период; бр_периода; садашња_вредност; [будућа_вредност]; [тип])", "d": "Враћа уплату на главницу за одређену инвестицију на основу периодичних, константних уплата и константне каматне стопе.", "ad": "је каматна стопа по периоду. На пример, користите 6%/4 за кварталне исплате на 6% APR!одређује период и мора бити у опсегу од 1 до нпер!је укупан број периода плаћања у инвестицији!је садашња вредност: укупан износ који серија будућих плаћања вреди сада!је будућа вредност, или готовински салдо који желите да постигнете након што је извршена последња уплата!је логична вредност:  плаћање на почетку периода = 1; плаћање на крају периода = 0 или изостављено"}, "PRICE": {"a": "(поравнање; доспеће; стопа; принос; откуп; учесталост; [основа])", "d": "Враћа цену по номиналној вредности од 100 долара за хартију од вредности која плаћа периодичне камате", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је годишња стопа купона хартије од вредности!је вредност откупа хартије од вредности по $100 номинална вредност!је број исплата купона годишње!је тип основе за бројање дана које треба искорисити"}, "PRICEDISC": {"a": "(поравнање; доспеће; дисконт; откуп; [основа])", "d": "Враћа цену по номиналној вредности од 100 долара за дисконтовану хартију од вредности", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је дисконтна стопа хартије од вредности!је откупна вредност хартије од вредности по $100 номиналне вредности!је тип основе за бројање дана које треба искористити"}, "PRICEMAT": {"a": "(поравнање; доспеће; емисија; стопа; принос; [основа])", "d": "Враћа цену по номиналној вредности од 100 долара за хартију од вредности која плаћа камату на доспеће", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је датум издавања хартије од вредности, изражен као серијски број датума!је каматна стопа хартије од вредности на дан издавања!је тип основе за бројање дана које треба искористити"}, "PV": {"a": "(стопа; бр_периода; рата; [будућа_вредност]; [тип])", "d": "Враћа садашњу вредност инвестиције: укупан износ који је низ будућих плаћања вредан сада", "ad": "је каматна стопа по периоду. На пример, користите 6%/4 за кварталне исплате на 6% APR!је укупан број периода плаћања у инвестицији!је уплата извршена у сваком периоду и не може се мењати током животног века инвестиције!је будућа вредност, или готовински салдо који желите да постигнете након што је извршена последња уплата!је логична вредност: плаћање на почетку периода = 1; плаћање на крају периода = 0 или изостављено"}, "RATE": {"a": "(бр_периода; рата; садашња_вредност; [будућа_вредност]; [тип]; [претпоставка])", "d": "Враћа каматну стопу по периоду зајма или инвестиције. На пример, користите 6%/4 за квартална плаћања по годишњој стопи од 6%", "ad": "је укупан број периода плаћања за кредит или инвестицију!је уплата извршена у сваком периоду и не може се мењати током трајања кредита или инвестиције!је садашња вредност: укупан износ који серија будућих плаћања вреди сада!је будућа вредност, или готовински салдо који желите да постигнете након последње уплате. Ако је изостављен, користи Fv = 0!је логичка вредност: плаћање на почетку периода = 1; плаћање на крају периода = 0 или изостављено!је ваша претпоставка за шта ће стопа бити; ако је изостављен, Претпоставка = 0,1 (10 процената)"}, "RECEIVED": {"a": "(поравнање; доспеће; инвестиција; дисконт; [основа])", "d": "Враћа износ примљен на доспеће за потпуно инвестирану хартију од вредности", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је износ уложен у хартији од вредности!је дисконтна стопа хартије од вредности! је тип осове за бројање дана које треба искористити"}, "RRI": {"a": "(бр_периода; садашња_вредност; будућа_вредност)", "d": "Враћа еквивалентну каматну стопу за раст инвестиције", "ad": "је број периода за инвестицију!је садашња вредност инвестиције!је будућа вредност инвестиције"}, "SLN": {"a": "(трошак; откуп; век)", "d": "Враћа линеарну амортизацију средства за један период", "ad": "је почетни трошак средства!је преостала вредност на крају животног века средства!је број периода током којих се средство амортизује (понекад се назива корисни век средства)"}, "SYD": {"a": "(трошак; откуп; век; период)", "d": "Враћа амортизацију средства по методологији цифара година за одређени период", "ad": "је почетни трошак средства!је преостала вредност на крају животног века средства!је број периода током којих се средство амортизује (понекад се назива корисни век средства)!је период и мора да користи исте јединице као живот"}, "TBILLEQ": {"a": "(поравнање; доспеће; дисконт)", "d": "Враћа принос еквивалентан обвезници за трезорски запис", "ad": "је датум поравнања трезорског записа, изражен као серијски број датума!је датум доспећа трезорског записа, изражен као серијски број датума!је дисконтна стопа трезорског записа"}, "TBILLPRICE": {"a": "(поравнање; доспеће; дисконт)", "d": "Враћа цену по номиналној вредности од 100 долара за трезорски запис", "ad": "је датум поравнања трезорског записа, изражен као серијски број датума!је датум доспећа трезорског записа, изражен као серијски број датума!је дисконтна стопа трезорског записа"}, "TBILLYIELD": {"a": "(поравнање; доспеће; цена)", "d": "Враћа принос за трезорски запис", "ad": "је датум поравнања трезорског записа, изражен као серијски број датума!је датум доспећа трезорског записа, изражен као серијски број датума!је дисконтна стопа трезорског записа"}, "VDB": {"a": "(трошак; откуп; век; почетни_период; крајњи_период; [фактор]; [без_преклопа])", "d": "Враћа амортизацију средства за било који период који наведете, укључујући делимичне периоде, користећи методу двоструке опадајуће вредности или неку другу методу коју наведете", "ad": "је почетни трошак средства!је преостала вредност на крају животног века средства!је број периода током којих се средство амортизира (понекад се назива и корисни век средства)!је почетни период за који желите да израчунате амортизацију, у истим јединицама као и живот!је завршни период за који желите да израчунате амортизацију, у истим јединицама као Живот!је стопа по којој се стање смањује, 2 (двоструко опадајући биланс) ако је изостављена!пребаците се на праволинијску амортизацију када је амортизација већа од опадајућег биланса = НЕТАЧНО или изостављена; немој пребацити = ТАЧНО"}, "XIRR": {"a": "(вредности; датуми; [претпоставка])", "d": "Враћа интерну стопу поврата за распоред новчаних токова", "ad": "је низ новчаних токова који одговарају распореду плаћања у датумима!је распоред датума плаћања који одговара исплатама новчаног тока!је број који претпостављате да је близу резултата XIRR"}, "XNPV": {"a": "(стопа; вредности; датуми)", "d": "Враћа нето садашњу вредност за распоред новчаних токова", "ad": "је дисконтна стопа која се примењује на новчане токове!је низ новчаних токова који одговарају распореду плаћања у датумима!је распоред датума плаћања који одговара исплатама новчаног тока"}, "YIELD": {"a": "(поравнање; доспеће; стопа; цена; откуп; учесталост; [основа])", "d": "Враћа принос на хартију од вредности која плаћа периодичне камате", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је годишња стопа купона хартије од вредности!је цена хартије од вредности по $100 номиналне вредности!је број исплата купона годишње!је врста основе за бројање дана коју треба користити"}, "YIELDDISC": {"a": "(поравнање; доспеће; цена; откуп; [основа])", "d": "Враћа годишњи принос за дисконтовану хартију од вредности. На пример, трезорски запис", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је цена хартије од вредности по $100 номиналне вредности!је откупна вредност хартије од вредности по $100 номиналне вредности!је врста основе за бројање дана коју треба користити"}, "YIELDMAT": {"a": "(поравнање; доспеће; емисија; стопа; цена; [основа])", "d": "Враћа годишњи принос хартије од вредности која плаћа камату на доспеће", "ad": "је датум поравнања хартије од вредности, изражен као серијски број датума!је датум доспећа хартије од вредности, изражен као серијски број датума!је датум издавања хартије од вредности, изражен као серијски број датума!је каматна стопа хартије од вредности на дан издавања!је цена хартије од вредности по $100 номиналне вредности!је врста основе за бројање дана коју треба користити"}, "ABS": {"a": "(број)", "d": "Враћа апсолутну вредност броја, број без његовог знака", "ad": "је реалан број за који желите апсолутну вредност"}, "ACOS": {"a": "(број)", "d": "Враћа аркус косинус броја, у радијанима у распону од 0 до Pi. Аркус косинус је угао чији је косинус Број", "ad": "је косинус жељеног угла и мора бити од -1 до 1"}, "ACOSH": {"a": "(број)", "d": "Враћа инверзни хиперболички косинус броја", "ad": "је било који реални број једнак или већи од 1"}, "ACOT": {"a": "(број)", "d": "Враћа аркус котангенс броја, у радијанима у распону од 0 до Pi", "ad": "је котангенс угла који желите"}, "ACOTH": {"a": "(број)", "d": "Враћа инверзни хиперболички котангенс броја", "ad": "је хиперболички котангенс угла који желите"}, "AGGREGATE": {"a": "(број_функције; опције; референца1; ...)", "d": "Враћа агрегат у листи или бази података", "ad": "је број од 1 до 19 који одређује функцију сажетка за агрегат.!је број од 0 до 7 који одређује вредности које треба занемарити за агрегат!је низ или опсег нумеричких података на којима се израчунава агрегат!означава позицију у низу; То је к-ти највећи, к-ти најмањи, к-ти перцентил, или к-ти квартил.!је број од 1 до 19 који одређује функцију сажетка за агрегат.!је број од 0 до 7 који специфицира вредности које треба занемарити за агрегат!су од 1 до 253 опсега или референце за које желите агрегат"}, "ARABIC": {"a": "(текст)", "d": "Претвара римски број у арапски", "ad": "је римски број који желите да конвертујете"}, "ASC": {"a": "(текст)", "d": "За језике са двоструким бајтом (DBCS), функција мења пуне (двобајтне) карактере у половичне (једнобајтне) карактере", "ad": "је текст који желите да промените. Ако текст не садржи слова пуне ширине, текст се не мења."}, "ASIN": {"a": "(број)", "d": "Враћа аркус синус броја у радијанима, у распону од -Pi/2 до Pi/2", "ad": "је синус жељеног угла и мора бити од -1 до 1"}, "ASINH": {"a": "(број)", "d": "Враћа инверзни хиперболички синус броја", "ad": "је било који реални број једнак или већи од 1"}, "ATAN": {"a": "(број)", "d": "Враћа аркус тангенс броја у радијанима, у распону од -Pi/2 до Pi/2", "ad": "је тангента угла који желите"}, "ATAN2": {"a": "(x_број; y_број)", "d": "Враћа аркус тангенс за наведене x и y координате, у радијанима између -Pi и Pi, искључујући -Pi", "ad": "је x-координата тачке!је y-координата тачке"}, "ATANH": {"a": "(број)", "d": "Враћа инверзни хиперболички тангенс броја", "ad": "је било који реални број између -1 и 1 искључујући -1 и 1"}, "BASE": {"a": "(број; база; [минимална_дужина])", "d": "Претвара број у текстуалну репрезентацију са датом базом (основом)", "ad": "је број који желите да конвертујете!је основа Radix у који желите да конвертујете број!је минимална дужина враћеног низа. Ако се не додају изостављене водеће нуле"}, "CEILING": {"a": "(број; значајност)", "d": "Заокружује број навише, до најближег вишекратника значајности", "ad": "је вредност коју желите да заокружите!је вишеструки на који желите да заокружите"}, "CEILING.MATH": {"a": "(број; [значајност]; [начин])", "d": "Заокружује број навише, до најближег целог броја или до најближег вишекратника значајности", "ad": "је вредност коју желите да заокружите!је вишеструки на који желите да заокружите!када је дат и није нула, ова функција ће заокружити даље од нуле"}, "CEILING.PRECISE": {"a": "(x; [значајност])", "d": "Враћа број заокружен навише до најближег целог броја или до најближег вишекратника значајности", "ad": "је вредност коју желите да заокружите!је вишеструки на који желите да заокружите"}, "COMBIN": {"a": "(број; изабрано)", "d": "Враћа број комбинација за дати број ставки", "ad": "је укупан број ставки!је број ставки у свакој комбинацији"}, "COMBINA": {"a": "(број; изабрано)", "d": "Враћа број комбинација са понављањима за дати број ставки", "ad": "је укупан број ставки!је број ставки у свакој комбинацији"}, "COS": {"a": "(број)", "d": "Враћа косинус угла", "ad": "је угао у радијанима за који желите косинус"}, "COSH": {"a": "(број)", "d": "Враћа хиперболички косинус броја", "ad": "је било који реални број"}, "COT": {"a": "(број)", "d": "Враћа котангенс угла", "ad": "је угао у радијанима за који желите котангенс"}, "COTH": {"a": "(број)", "d": "Враћа хиперболички котангенс броја", "ad": "је угао у радијанима за који желите хиперболички котангенс"}, "CSC": {"a": "(број)", "d": "Враћа косеканс угла", "ad": "је угао у радијанима за који желите косеканс"}, "CSCH": {"a": "(број)", "d": "Враћа хиперболички косеканс угла", "ad": "је угао у радијанима за који желите хиперболички косеканс"}, "DECIMAL": {"a": "(број; база)", "d": "Претвара текстуалну репрезентацију броја у датој бази у децимални број", "ad": "је број који желите да конвертујете!је основа радиx броја који се претвара"}, "DEGREES": {"a": "(угао)", "d": "Претвара радијане у степене", "ad": "је угао у радијанцима који желите да конвертујете"}, "ECMA.CEILING": {"a": "(x; значајност)", "d": "Заокружује број навише до најближег вишекратника значајности", "ad": "је вредност коју желите да заокружите!је вишеструки на који желите да заокружите"}, "EVEN": {"a": "(број)", "d": "Заокружује позитиван број навише и негативан број наниже до најближег парног целог броја", "ad": "је вредност коју треба заокружити"}, "EXP": {"a": "(број)", "d": "Враћа е подигнуто на степен заданог броја", "ad": "је експонент који се примењује на базу e. Константа е једнака је 2.71828182845904, основа природног логаритма"}, "FACT": {"a": "(број)", "d": "Враћа факторијел броја, једнак 1*2*3*...* Број", "ad": "је ненегативан број за који желите факторијел"}, "FACTDOUBLE": {"a": "(број)", "d": "Враћа двоструки факторијел броја", "ad": "је вредност за коју се враћа двоструки факторијал"}, "FLOOR": {"a": "(број; значајност)", "d": "Заокружује број наниже до најближег вишекратника значајности", "ad": "је нумеричка вредност коју желите да заокружите!је вишеструки на који желите да заокружите. Број и Значајност морају бити или оба позитивна или оба негативна"}, "FLOOR.PRECISE": {"a": "(x; [значајност])", "d": "Враћа број заокружен наниже до најближег целог броја или до најближег вишекратника значајности", "ad": "је вредност коју желите да заокружите!је вишеструки на који желите да заокружите"}, "FLOOR.MATH": {"a": "(број; [значајност]; [начин])", "d": "Заокружује број наниже, до најближег целог броја или до најближег вишекратника значајности", "ad": "је вредност коју желите да заокружите!је вишеструки на који желите да заокружите! Када је дата и није нула, ова функција ће се заокружити према нули"}, "GCD": {"a": "(број1; [број2]; ...)", "d": "Враћа највећи заједнички делилац", "ad": "су од 1 до 255 вредности"}, "INT": {"a": "(број)", "d": "Заокружује број наниже до најближег целог броја", "ad": "је стварни број који желите да заокружите на цео број"}, "ISO.CEILING": {"a": "(број; [значајност])", "d": "Враћа број заокружен навише до најближег целог броја или до најближег вишекратника значајности без обзира на знак броја. Међутим, ако је број или значајност нула, враћа се нула.", "ad": "је вредност коју желите да заокружите!је вишеструки на који желите да заокружите"}, "LCM": {"a": "(број1; [број2]; ...)", "d": "Враћа најмањи заједнички садржалац", "ad": "су од 1 до 255 вредности за које желите најмањи заједнички садржалац"}, "LN": {"a": "(број)", "d": "Враћа природни логаритам броја", "ad": "је позитиван реални број за који желите природни логаритам"}, "LOG": {"a": "(број; [база])", "d": "Враћа логаритам броја у задатој бази", "ad": "је позитиван реални број за који желите логаритам!је основа логаритма; 10 ако је изостављен"}, "LOG10": {"a": "(број)", "d": "Враћа логаритам броја у бази 10", "ad": "је позитиван реални број за који желите логаритам базе-10"}, "MDETERM": {"a": "(низ)", "d": "Враћа детерминанту матрице низа", "ad": "је нумерички низ са једнаким бројем редова и колона, било опсег ћелија или константа низа"}, "MINVERSE": {"a": "(низ)", "d": "Враћа инверзну матрицу за матрицу похрањену у низу", "ad": "је нумерички низ са једнаким бројем редова и колона, било опсег ћелија или константа низа"}, "MMULT": {"a": "(низ1; низ2)", "d": "Враћа матрични производ два низа, низ са истим бројем редова као низ1 и колона као низ2", "ad": "је први низ бројева за множење и мора имати исти број колона као што Низ2 има редова"}, "MOD": {"a": "(број; делилац)", "d": "Враћа остатак након што се број подели делиоцем", "ad": "је број за који желите да пронађете остатак након што се изврши подела!је број којим желите да поделите Број"}, "MROUND": {"a": "(број; вишекратник)", "d": "Враћа број заокружен на жељени вишекратник", "ad": "је вредност коју треба заокружити!је вишеструки на који желите да заокружите број"}, "MULTINOMIAL": {"a": "(број1; [број2]; ...)", "d": "Враћа мултиномијал скупа бројева", "ad": "су од 1 до 255 вредности за које желите мултиномијал"}, "MUNIT": {"a": "(димензија)", "d": "Враћа јединичну матрицу за задату димензију", "ad": "је цео број који одређује димензију матрице јединица коју желите да вратите"}, "ODD": {"a": "(број)", "d": "Заокружује позитиван број навише и негативан број наниже до најближег непарног целог броја", "ad": "је вредност коју треба заокружити"}, "PI": {"a": "()", "d": "Враћа вредност Pi, 3.14159265358979, тачно до 15 цифара", "ad": ""}, "POWER": {"a": "(број; експонент)", "d": "Враћа резултат броја подигнутог на задати степен", "ad": "је основни број, било који реални број!је експонент, на који је подигнут основни број"}, "PRODUCT": {"a": "(број1; [број2]; ...)", "d": "Множи све задате бројеве", "ad": "су од 1 до 255 бројева, логичке вредности или текстуалне репрезентације бројева које желите да помножите"}, "QUOTIENT": {"a": "(дељеник; делилац)", "d": "Враћа целобројни део дељења", "ad": "је дељеник!је делилац"}, "RADIANS": {"a": "(угао)", "d": "Претвара степене у радијане", "ad": "је угао у степенима који желите да конвертујете"}, "RAND": {"a": "()", "d": "Враћа случајан број већи или једнак 0 и мањи од 1, равномерно дистрибуиран (мења се при поновном израчунавању)", "ad": ""}, "RANDARRAY": {"a": "([редови]; [колоне]; [мин]; [маx]; [цео_број])", "d": "Враћа низ случајних бројева", "ad": "број редова у враћеном низу!број колона у враћеном низу!минимални број који желите вратити!максимални број који желите вратити!вратите цео број или децималну вредност. ТАЧНО за цео број, НЕТАЧНО за децимални број"}, "RANDBETWEEN": {"a": "(доле; горе)", "d": "Враћа насумичан број између специфицираних бројева", "ad": "је најмањи цео број који ће РАНДИЗМЕЂУ вратити!је највећи цео број који ће РАНДИЗМЕЂУ вратити"}, "ROMAN": {"a": "(број; [форма])", "d": "Претвара арапски број у римски, као текст", "ad": "је арапски број који желите да конвертујете!је број који одређује врсту римског броја који желите."}, "ROUND": {"a": "(број; број_цифара)", "d": "Заокружује број на одређени број цифара", "ad": "је број који желите да заокружите!је број цифара на које желите да заокружите. Негативни број заокружује лево од децималне тачке; нула до најближег целог броја"}, "ROUNDDOWN": {"a": "(број; број_цифара)", "d": "Заокружује број наниже, према нули", "ad": "је било који реалан број који желите заокружити надоле!је број цифара на које желите да заокружите. Негативни број заокружује лево од децималне тачке; нула или изостављено, на најближи цео број"}, "ROUNDUP": {"a": "(број; број_цифара)", "d": "Заокружује број навише, од нуле", "ad": "је било који реалан број који желите заокружити навише!је број цифара на које желите да заокружите. Негативни број заокружује лево од децималне тачке; нула или изостављено, на најближи цео број"}, "SEC": {"a": "(број)", "d": "Враћа секанс угла", "ad": "је угао у радијанима за који желите секант"}, "SECH": {"a": "(број)", "d": "Враћа хиперболички секанс угла", "ad": "је угао у радијанима за који желите хиперболички секант"}, "SERIESSUM": {"a": "(x; n; m; коефицијенти)", "d": "Враћа збир редова снага на основу формуле", "ad": "је улазна вредност у степенасти низ!је почетни степен на коју желите да подигнете x!је корак којим се повећава н за сваки члан у низу!је скуп коефицијената по којима се множи сваки узастопна степен x"}, "SIGN": {"a": "(број)", "d": "Враћа знак броја: 1 ако је број позитиван, нула ако је број нула, или -1 ако је број негативан", "ad": "је било који реални број"}, "SIN": {"a": "(број)", "d": "Враћа синус угла", "ad": "је угао у радијанима за који желите синус. Степени * PI()/180 = радијани"}, "SINH": {"a": "(број)", "d": "Враћа хиперболички синус броја", "ad": "је било који реални број"}, "SQRT": {"a": "(број)", "d": "Враћа квадратни корен броја", "ad": "је број за који желите квадратни корен"}, "SQRTPI": {"a": "(број)", "d": "Враћа квадратни корен (број * Pi)", "ad": "је број којим се p множи"}, "SUBTOTAL": {"a": "(број_функције; реф1; ...)", "d": "Враћа међузбир у листи или бази података", "ad": "је број од 1 до 11 који одређује функцију резимеа за међузбир.! су од 1 до 254 опсега или референци за које желите међузбир"}, "SUM": {"a": "(број1; [број2]; ...)", "d": "Сабира све бројеве у опсегу ћелија", "ad": "су од 1 до 255 бројева за сабирање. Логичке вредности и текст се игноришу у ћелијама, укључене ако су уписане као аргументи"}, "SUMIF": {"a": "(опсег; услов; [опсег_суме])", "d": "Сабира ћелије које задовољавају одређени услов или критеријум", "ad": "је опсег ћелија које желите проценити!је услов или критеријум у облику броја, израза или текста који дефинише које ћелије ће бити додате!су стварне ћелије за сумирање. Ако се изостави, користе се ћелије у опсегу"}, "SUMIFS": {"a": "(опсег_суме; опсег_критеријума; услов; ...)", "d": "Сабира ћелије које задовољавају задати скуп услова или критеријума", "ad": "су реалне ћелије које треба сумирати.! је опсег ћелија које желите проценити за одређени услов!је услов или критеријум у облику броја, израза или текста који дефинише које ћелије ће бити додате"}, "SUMPRODUCT": {"a": "(низ1; [низ2]; [низ3]; ...)", "d": "Враћа збир производа одговарајућих опсега или низова", "ad": "су од 2 до 255 низова за које желите да помножите, а затим додате компоненте. Сви низови морају имати исте димензије"}, "SUMSQ": {"a": "(број1; [број2]; ...)", "d": "Враћа збир квадрата аргумената. Аргументи могу бити бројеви, низови, имена или референце на ћелије које садрже бројеве", "ad": "су од 1 до 255 бројева, низова, имена или референци на низове за које желите збир квадрата"}, "SUMX2MY2": {"a": "(низ_x; низ_y)", "d": "Сабира разлике између квадрата два одговарајућа низа или опсега", "ad": "је први опсег или низ бројева и може бити број или име, низ или референца која садржи бројеве!је други опсег или низ бројева и може бити број или име, низ или референца која садржи бројеве"}, "SUMX2PY2": {"a": "(низ_x; низ_y)", "d": "Враћа укупну суму квадрата бројева у два одговарајућа низа или опсега", "ad": "је први опсег или низ бројева и може бити број или име, низ или референца која садржи бројеве!је други опсег или низ бројева и може бити број или име, низ или референца која садржи бројеве"}, "SUMXMY2": {"a": "(низ_x; низ_y)", "d": "Сабира квадрате разлика у два одговарајућа низа или опсега", "ad": "је први опсег или низ вредности и може бити број или име, низ или референца која садржи бројеве!је други опсег или низ вредности и може бити број или име, низ или референца која садржи бројеве"}, "TAN": {"a": "(број)", "d": "Враћа тангенс угла", "ad": "је угао у радијанима за који желите тангенту. Степени * PI()/180 = радијани"}, "TANH": {"a": "(број)", "d": "Враћа хиперболички тангенс броја", "ad": "је било који реални број"}, "TRUNC": {"a": "(број; [број_цифара])", "d": "Скраћује број на цео број уклањањем децималног, или фракцијског, дела броја", "ad": "је број који желите да скратите!је број који одређује прецизност скраћивања, 0 (нула) ако је изостављен"}, "ADDRESS": {"a": "(број_реда; број_колоне; [апсолутни_број]; [a1]; [име_листа])", "d": "Креира референцу на ћелију као текст, дато према специфицираним бројевима реда и колоне", "ad": "је број реда који се користи у референци ћелије: број_реда = 1 за ред 1!је број колоне који се користи у референци ћелије. На пример, број_колоне = 4 за колону D!одређује тип референце: апсолутни = 1; апсолутни ред / релативна колона = 2; релативни ред / апсолутна колона = 3; релативна = 4!је логичка вредност која одређује стил референце: A1 стил = 1 или ТАЧНО; R1C1 стил = 0 или НЕТАЧНО! је текст који одређује име радног листа који ће се користити као спољна референца"}, "CHOOSE": {"a": "(индексни_број; vrednost1; [вредност2]; ...)", "d": "Бира вредност или радњу коју треба извршити из листе вредности, на основу индексног броја", "ad": "специфицира који аргумент вредности је изабран. Индеx _нум мора бити између 1 и 254, или формула или референца на број између 1 и 254!су 1 до 254 бројева, референци ћелија, дефини<PERSON><PERSON>на имена, формуле, функције или текстуални аргументи из којих ИЗАБЕРИ бира"}, "COLUMN": {"a": "([референца])", "d": "Враћа број колоне референце", "ad": "је ћелија или опсег суседних ћелија за које желите број колоне. Ако је изостављена, користи се ћелија која садржи функцију КОЛОНА"}, "COLUMNS": {"a": "(низ)", "d": "Враћа број колона у низу или референци", "ad": "је низ или формула низа, или референца на низ ћелија за које желите број колона"}, "FORMULATEXT": {"a": "(референца)", "d": "Враћа формулу као стринг", "ad": "је референца на формулу"}, "HLOOKUP": {"a": "(вредност_претраге; низ_табеле; број_реда; [опсег_претраге])", "d": "Тражи вредност у горњем реду табеле или низа вредности и враћа вредност у истој колони из реда који ви наведете", "ad": "је вредност која се налази у првом реду табеле и може бити вредност, референца или текстуални низ!је табела текста, бројева или логичких вредности у којима се подаци претражују. Низ _табеле може бити референца на опсег или име опсега!је број реда у низу_табеле из којег треба да се врати одговарајућа вредност. Први ред вредности у табели је ред 1!је логичка вредност: да пронађете најближе подударање у горњем реду (сортирано у узлазном редоследу) = ТАЧНО или изостављено; пронађи тачно подударање = НЕТАЧНО"}, "HYPERLINK": {"a": "(локација_везе; [пријатељско_име])", "d": "Креира пречицу или скок који отвара документ сачуван на вашем хард диску, мрежном серверу или на Интернету", "ad": "је текст који даје путању и име датотеке документу који се отвара, локација чврстог диска, UNC адреса или URL путања!је текст или број који се приказује у ћелији. Ако је изостављен, ћелија приказује Линк_локацију текста"}, "INDEX": {"a": "(низ; број_реда; [број_колоне]; референца; број_реда; [број_колоне]; [број_подручја])", "d": "Враћа вредност или референцу ћелије на пресеку одређеног реда и колоне у датом опсегу", "ad": "је низ ћелија или константа низа.! бира ред у низу или референцу из које ће вратити вредност. Ако је изостављен, број_колоне је обавезан!бира колону у низу или референцу из које се враћа вредност. Ако је изостављен, број_реда је обавезно!је референца на један или више опсега ћелија!бира ред у низу или референци из које се враћа вредност. Ако је изостављен, број_колонр је обавезан!бира колону у низу или референцу из које се враћа вредност. Ако је изостављен, број_реда је обавезан!бира опсег у Референци из које се враћа вредност. Прва област изабрана или унесена је област 1, друга област је област 2, и тако даље"}, "INDIRECT": {"a": "(текст_референца; [a1])", "d": "Враћа референцу специфицирану текстуалним стрингом", "ad": "је референца на ћелију која садржи референцу у стилу A1- или R1C1, име дефинисано као референца, или референца на ћелију као текстуални низ!је логичка вредност која одређује врсту референце у Реф_текст: R1C1-стил = НЕТАЧНО; A1 -стил = ТАЧНО или изостављен"}, "LOOKUP": {"a": "(вредност_претраге; вектор_претраге; [вектор_резултата]; вредност_претраге; низ)", "d": "Тражи вредност или из једног реда или опсега једне колоне или из низа. Обезбеђено за компатибилност уназад", "ad": "је вредност коју ПРОНАЂИ тражи у Вектор_преетраге и може бити број, текст, логичка вредност или име или референца на вредност!је опсег који садржи само један ред или једну колону текста, бројева или логичких вредности, постављених у растућем редоследу!је опсег који садржи само један ред или колону, иста величина као Вектор_претераге!је вредност коју ПРОНАЂИ тражи у Низу и може бити број, текст, логичка вредност или име или референца на вредност!је низ ћелија које садрже текст, број или логичке вредности које желите да упоредите са вредност_претраге"}, "MATCH": {"a": "(вредност_претраге; низ_претраге; [тип_подударања])", "d": "Враћа релативну позицију ставке у низу која одговара специфицираној вредности у одређеном редоследу", "ad": "је вредност коју користите да бисте пронашли вредност коју желите у низу, број, текст или логичка вредност, или референца на једну од ових!је суседни опсег ћелија које садрже могуће вредности претраге, низ вредности или референцу на низ! је број 1, 0 или -1 који означава коју вредност треба вратити."}, "OFFSET": {"a": "(референца; редови; колоне; [висина]; [ширина])", "d": "Враћа референцу на опсег који је одређен број редова и колона удаљен од дате референце", "ad": "је референца на коју желите да се заснива померање, референца на ћелију или опсег суседних ћелија!је број редова, горе или доле, на који желите да се горња лева ћелија резултата односи!је број колона, лево или десно, на које желите да се горња лева ћелија резултата односи!је висина, у броју редова, да желите да резултат буде, исте висине као Референца ако је изостављена!је ширина, у броју колона, да желите да резултат буде, исте ширине као Референца ако је изостављен"}, "ROW": {"a": "([референца])", "d": "Враћа број реда референце", "ad": "је ћелија или један опсег ћелија за које желите број реда; ако је изостављена, враћа ћелију која садржи функцију РЕД"}, "ROWS": {"a": "(низ)", "d": "Враћа број редова у референци или низу", "ad": "је низ, формула низа или референца на низ ћелија за које желите број редова"}, "TRANSPOSE": {"a": "(низ)", "d": "Претвара вертикални опсег ћелија у хоризонтални опсег, или обрнуто", "ad": "је опсег ћелија на радном листу или низ вредности које желите да пренесете"}, "UNIQUE": {"a": "(низ; [по_колони]; [тачно_једном])", "d": "Враћа јединствене вредности из опсега или низа.", "ad": "опсег или низ из којег се враћају јединствени редови или колоне!је логичка вредност: упоредите редове једне против других и вратите јединствене редове = ТАЧНО или изостављене; упоредите колоне једне против других и вратите јединствене колоне = ТАЧНО!је логичка вредност: вратите редове или колоне које се јављају тачно једном из низа = ТАЧНО; вратите све различите редове или колоне из низа = НЕТАЧНО или изостављено"}, "VLOOKUP": {"a": "(вредност_за_претрагу; табела_низ; број_колоне; [опсег_претраге])", "d": "Тражи вредност у крајњој левој колони табеле и затим враћа вредност у истом реду из колоне коју наведете. По подразумевању, табела мора бити сортирана у растућем редоследу", "ad": "је вредност која се налази у првој колони табеле, а може бити вредност, референца или текстуални низ!је табела текста, бројева или логичких вредности, у којој се подаци преузимају. Табела _низа може бити референца на опсег или име опсега!је број колоне у табели_низа из које треба да се врати одговарајућа вредност. Прва колона вредности у табели је колона 1!је логичка вредност: да пронађете најближе подударање у првој колони (сортирано у узлазном редоследу) = ТАЧНО или изостављено; пронађи тачно подударање = НЕТАЧНО"}, "XLOOKUP": {"a": "(вредност_за_претрагу; низ_за_претрагу; повратни_низ; [ако_није_пронађено]; [начин_подударања]; [начин_претраге])", "d": "Претражује опсег или низ ради проналажења подударања и враћа одговарајућу ставку из другог опсега или низа. По подразумевању се користи тачно подударање", "ad": "је вредност за претрагу!је низ или опсег за претрагу!је низ или опсег који се враћа!враћено ако се не нађе поклапање!навести како да се вредност_за_претрагу упоређује са вредностима у низ_за_претрагу!навести режим претраге који ће се користити. Подразумевано , прва до последња претрага ће се користити"}, "CELL": {"a": "(тип_информације; [референца])", "d": "Враћа информације о форматирању, локацији или садржају ћелије", "ad": "је текстуална вредност која одређује коју врсту информација о ћелији желите да вратите!ћелија о којој желите информације"}, "ERROR.TYPE": {"a": "(вредност_грешке)", "d": "Враћа број који одговара вредности грешке.", "ad": "је вредност грешке за коју желите идентификациони број, и може бити стварна вредност грешке или референца на ћелију која садржи вредност грешке"}, "ISBLANK": {"a": "(вредност)", "d": "Проверава да ли је референца на празну ћелију и враћа TRUE (ТАЧНО) или FALSE (НЕТАЧНО)", "ad": "је ћелија или име које се односи на ћелију коју желите тестирати"}, "ISERR": {"a": "(вредност)", "d": "Проверава да ли је вредност грешка осим #N/A, и враћа TRUE (ТАЧНО) или FALSE (НЕТАЧНО)", "ad": "је вредност коју желите да тестирате. Вредност се може односити на ћелију, формулу или име које се односи на ћелију, формулу или вредност"}, "ISERROR": {"a": "(вредност)", "d": "Проверава да ли је вредност грешка и враћа TRUE (ТАЧНО) или FALSE (НЕТАЧНО)", "ad": "је вредност коју желите да тестирате. Вредност се може односити на ћелију, формулу или име које се односи на ћелију, формулу или вредност"}, "ISEVEN": {"a": "(број)", "d": "Враћа TRUE (ТАЧНО) ако је број паран", "ad": "је вредност за тестирање"}, "ISFORMULA": {"a": "(референца)", "d": "Проверава да ли је референца на ћелију која садржи формулу и враћа TRUE (ТАЧНО) или FALSE (НЕТАЧНО)", "ad": "је референца на ћелију коју желите да тестирате. Референца може бити референца ћелије, формула или име које се односи на ћелију"}, "ISLOGICAL": {"a": "(вредност)", "d": "Проверава да ли је вредност логичка вредност TRUE (ТАЧНА) или FALSE (НЕТАЧНА)) и враћа TRUE (ТАЧНО) или FALSE (НЕТАЧНО)", "ad": "jје вредност коју желите да тестирате. Вредност се може односити на ћелију, формулу или име које се односи на ћелију, формулу или вредност"}, "ISNA": {"a": "(вредност)", "d": "Проверава да ли је вредност #Н/А и враћа TRUE (ТАЧНО) или FALSE (НЕТАЧНО)", "ad": "је вредност коју желите да тестирате. Вредност се може односити на ћелију, формулу или име које се односи на ћелију, формулу или вредност"}, "ISNONTEXT": {"a": "(вредност)", "d": "Проверава да ли вредност није текст (празне ћелије нису текст) и враћа TRUE (ТАЧНО) или FALSE (НЕТАЧНО)", "ad": "је вредност коју желите тестирати: ћелија; формула; или име које се односи на ћелију, формулу или вредност"}, "ISNUMBER": {"a": "(вредност)", "d": "Проверава да ли је вредност број и враћа TRUE (ТАЧНО) или FALSE (НЕТАЧНО)", "ad": "је вредност коју желите да тестирате. Вредност се може односити на ћелију, формулу или име које се односи на ћелију, формулу или вредност"}, "ISODD": {"a": "(број)", "d": "Враћа TRUE (ТАЧНО) ако је број непаран", "ad": "је вредност за тестирање"}, "ISREF": {"a": "(вредност)", "d": "Проверава да ли је вредност референца и враћа TRUE (ТАЧНО) или FALSE (НЕТАЧНО)", "ad": "је вредност коју желите да тестирате. Вредност се може односити на ћелију, формулу или име које се односи на ћелију, формулу или вредност"}, "ISTEXT": {"a": "(вредност)", "d": "Проверава да ли је вредност текст и враћа TRUE (ТАЧНО) или FALSE (НЕТАЧНО)", "ad": "је вредност коју желите да тестирате. Вредност се може односити на ћелију, формулу или име које се односи на ћелију, формулу или вредност"}, "N": {"a": "(вредност)", "d": "Претвара вредност која није број у број, датуме у серијске бројеве, TRUE (ТАЧНО) у 1, било шта друго у 0 (нула)", "ad": "је вредност коју желите конвертовати"}, "NA": {"a": "()", "d": "Враћа вредност грешке #N/A (вредност није доступна)", "ad": ""}, "SHEET": {"a": "([вредност])", "d": "Враћа број листа референцираног листа", "ad": "је име листа или референце за коју желите број листа. Ако је изостављен, број листа који садржи функцију се враћа"}, "SHEETS": {"a": "([референца])", "d": "Враћа број листова у референци", "ad": "је референца за коју желите да знате број листова које садржи. Ако је изостављен, број листова у радној свесци која садржи функцију се враћа"}, "TYPE": {"a": "(вредност)", "d": "Враћа цео број који представља тип података вредности: број = 1; текст = 2; логичка вредност = 4; вредност грешке = 16; низ = 64; сложени подаци = 128", "ad": "може бити било која вредност"}, "AND": {"a": "(логички1; [логички2]; ...)", "d": "Проверава да ли су сви аргументи TRUE (ТАЧНИ) и враћа TRUE (ТАЧНО) ако су сви аргументи TRUE (ТАЧНИ)", "ad": "су од 1 до 255 услова које желите да тестирате који могу бити ТАЧНО или НЕТАЧНО и могу бити логичке вредности, низови или референце"}, "FALSE": {"a": "()", "d": "Враћа логичку вредност FALSE (НЕТАЧНО)", "ad": ""}, "IF": {"a": "(логички_тест; [вредност_ако_true]; [вредност_ако_false])", "d": "Проверава да ли је услов испуњен и враћа једну вредност ако је TRUE (ТАЧНО), а другу вредност ако је FALSE (НЕТАЧНО)", "ad": "је било која вредност или израз који се може проценити на ТАЧНО или НЕТАЧНО!је вредност која се враћа ако је Логички_тест ТАЧНО. Ако се изостави, враћа се ТАЧНО. Можете угнездити до седам IF функција!је вредност која се враћа ако је Логички_тест НЕТАЧНО. Ако је изостављен, НЕТАЧНО се враћа"}, "IFS": {"a": "(логички_тест; вредност_ако_true; ...)", "d": "Проверава да ли је један или више услова испуњено и враћа вредност која одговара првом TRUE (ТАЧНОМ) услову", "ad": "је било која вредност или израз који се може проценити на ТАЧНО или НЕТАЧНО!је вредност враћена ако је Логички_тест ТАЧНО"}, "IFERROR": {"a": "(вредност; вредност_ако_грешка)", "d": "Враћа вредност_ако_грешка ако је израз грешка, а вредност израза у супротном", "ad": "је било која вредност или израз или референца!је било која вредност или израз или референца"}, "IFNA": {"a": "(вредност; вредност_ако_на)", "d": "Враћа вредност коју наведете ако израз резултира са #N/A, у супротном враћа резултат израза", "ad": "је било која вредност или израз или референца!је било која вредност или израз или референца"}, "NOT": {"a": "(логички)", "d": "Мења FALSE (НЕТАЧНО) у TRUE (ТАЧНО), или TRUE (ТАЧНО) у FALSE (НЕТАЧНО)", "ad": "је вредност или израз који се може проценити на ТАЧНО или НЕТАЧНО"}, "OR": {"a": "(логички1; [логички2]; ...)", "d": "Проверава да ли је било који од аргумената TRUE (ТАЧАН) и враћа TRUE (ТАЧНО) или FALSE (НЕТАЧНО). Враћа FALSE (НЕТАЧНО) само ако су сви аргументи FALSE (НЕТАЧНИ)", "ad": "су од 1 до 255 услова које желите да тестирате који могу бити ТАЧНО или НЕТАЧНО"}, "SWITCH": {"a": "(израз; вредност1; резултат1; [подразумевана_вредност_или_вредност2]; [резултат2]; ...)", "d": "Процењује израз према листи вредности и враћа резултат који одговара првој вредности која се поклапа. Ако нема поклапања, враћа се опционална подразумевана вредност", "ad": "је израз који треба проценити!је вредност која се упоређује са изразом!је резултат који се враћа ако одговарајућа вредност одговара изразу"}, "TRUE": {"a": "()", "d": "Враћа логичку вредност TRUE (ТАЧНО)", "ad": ""}, "XOR": {"a": "(логички1; [логички2]; ...)", "d": "Враћа логичку вредност 'Ексклузивно Или' свих аргумената", "ad": "су од 1 до 254 услова које желите да тестирате који могу бити ТАЧНО или НЕТАЧНО и могу бити логичке вредности, низови или референце"}, "TEXTBEFORE": {"a": "(текст, делимитер, [број_инстанци], [начин_поклапања], [поклапање_краја], [ако_није_пронађено])", "d": "Враћа текст који се налази пре делимитирајућих карактера", "ad": "Текст који желите да претражујете за раздвајачем.! Карактер или стринг који ће се користити као раздвајач.! Жељена појава раздвајача. Подразумевано је 1. Негативан број тражи од краја.!Претражује текст за поклапање раздвајача. По дефаулту, подударање осетљиво на велика и мала слова је урађено.!се поклапа са раздвајачем на крају текста. По дефаулту, они се не поклапају.!Враћа се ако се не пронађе подударање. Подразумевано, #N/A се враћа."}, "TEXTAFTER": {"a": "(текст, делимитер, [број_инстанци], [начин_поклапања], [поклапање_краја], [ако_није_пронађено])", "d": "Враћа текст који се налази после делимитирајућих карактера", "ad": "Текст који желите да претражујете за раздвајачем.!Карактер или стринг који ће се користити као раздјелник.! Жељена појава раздвајача. Подразумевано је 1. Негативан број тражи од краја.! Претражује текст за поклапање раздвајача. По дефаулту, подударање осетљиво на велика и мала слова је урађено.!се поклапа са раздвајачем на крају текста. По дефаулту, они се не поклапају.! Враћа се ако се не пронађе подударање. Подразумевано, #N/A се враћа."}, "TEXTSPLIT": {"a": "(текст, кол_делимитер, [ред_делимитер], [игнориши_празне], [начин_поклапања], [допуни_са])", "d": "Раздваја текст у редове или колоне користећи делимитере", "ad": "Текст за поделу! Карактер или низ за поделу колона.! Карактер или низ за поделу редова по.! да игноришете празне ћелије. Подразумевано је НЕТАЧНО.!Претражује текст за поклапање раздвајача. По дефаулту, подударање осетљиво на велика и мала слова је урађено.!Вредност која се користи за подлогу. Подразумевано се користи #N/A."}, "WRAPROWS": {"a": "(вектор, број_завијања, [допуни_са]])", "d": "Завија ред или колону вектора након одређеног броја вредности", "ad": "Вектор или референца за завијање.! Максималан број вредности по реду.! Вредност којом ће се попунити. Подразумевано је #N/A."}, "VSTACK": {"a": "(низ1, [низ2], ...)", "d": "Вертикално слаже низове у један низ", "ad": "Низ или референца која се слаже."}, "HSTACK": {"a": "(низ1, [низ2], ...)", "d": "Хоризонтално слаже низове у један низ", "ad": "Низ или референца која се слаже."}, "CHOOSEROWS": {"a": "(низ, ред_број1, [ред_број2], ...)", "d": "Враћа редове из низа или референце", "ad": "Низ или референца која садржи редове који се враћају.! Број реда који се враћа."}, "CHOOSECOLS": {"a": "(низ, кол_број1, [кол_број2], ...)", "d": "Враћа редове из низа или референце", "ad": "Низ или референца која садржи редове који се враћају.! Број реда који се враћа."}, "TOCOL": {"a": "(низ, [игнориши], [сканирај_по_колонама])", "d": "Враћа низ као једну колону", "ad": "Низ или референца да се врати као колона.! Да ли да игноришемо одређене врсте вредности. По дефаулту, ниједна вредност се не игнорише.! Скенирајте низ по колони. По дефаулту, низ се скенира по редовима."}, "TOROW": {"a": "(низ, [игнориши], [сканирај_по_колонама])", "d": "Враћа низ као један ред", "ad": "Низ или референца да се врати као ред.! Да ли да игноришемо одређене врсте вредности. По дефаулту, ниједна вредност се не игнорише.! Скенирајте низ по колони. По дефаулту, низ се скенира по редовима."}, "WRAPCOLS": {"a": "(вектор, број_завијања, [допуни_са])", "d": "Завија ред или колону вектора након одређеног броја вредности", "ad": "Вектор или референца за завијање.! Максималан број вредности по колони.! Вредност са којом ће се попунити. Подразумевано је #N/A."}, "TAKE": {"a": "(низ, редови, [колоне])", "d": "Враћа редове или колоне са почетка или краја низа", "ad": "Низ из којег се узимају редови или колоне.! Број редова које треба узети. Негативна вредност узима од краја низа.! Број колона које треба узети. Негативна вредност узима од краја низа."}, "DROP": {"a": "(низ, редови, [колоне])", "d": "Избацује редове или колоне са почетка или краја низа", "ad": "Низ из којег се испуштају редови или колоне.! Број редова за испуштање. Негативна вредност пада са краја низа.! Број колона које треба испустити. Негативна вредност пада са краја низа."}, "SEQUENCE": {"a": "(редови, [колоне], [почетак], [корак])", "d": "Враћа секвенцу бројева", "ad": "број редова за повратак!број колона за повратак!први број у низу!износ за повећање сваке наредне вредности у низу"}, "EXPAND": {"a": "(низ, редови, [колоне], [допуни_са])", "d": "Проширује низ на одређене димензије", "ad": "Низ за проширење.! Број редова у проширеном низу. Ако недостаје, редови неће бити проширени.! Број колона у проширеном низу. Ако недостаје, колоне неће бити проширене.! Вредност са којом ће се попунити. Подразумевано је #N/A."}, "XMATCH": {"a": "(вредност_претраге, низ_претраге, [начин_поклапања], [начин_претраге])", "d": "Враћа релативну позицију ставке у низу. Подразумевано се користи тачно поклапање", "ad": "је вредност за претрагу!је низ или опсег за претрагу!навести како да се поклапа вредност_претраге са вредностима у низу_претраге!навести режим претраге да се користи. Подразумевано, прва до последња претрага ће се користити"}, "FILTER": {"a": "(низ, укључити, [ако_је_празно])", "d": "Филтрира опсег или низ", "ad": "опсег или низ за филтрирање!низ логичких вредности где ТАЧНО представља ред или колону за задржавање!враћа се ако се не задрже ставке"}, "ARRAYTOTEXT": {"a": "(низ, [формат])", "d": "Враћа текстуалну репрезентацију низа", "ad": "низ који ће се представити као текст!формат текста"}, "SORT": {"a": "(низ, [индекс_сортирања], [редослед_сортирања], [по_колонама])", "d": "Сортира опсег или низ", "ad": "опсег или низ за сортирање!број који означава ред или колону за сортирање!број који означава жељени редослед сортирања; 1 за узлазни редослед (подразумевано), -1 за силазни редослед!логичка вредност која указује на жељени правац сортирања: НЕТАЧНО за сортирање по реду (подразумевано), ТАЧНО за сортирање по колони"}, "SORTBY": {"a": "(низ, по_низу, [редослед_сортирања], ...)", "d": "Сортира опсег или низ на основу вредности у одговарајућем опсегу или низу", "ad": "опсег или низ за сортирање!опсег или низ за сортирање!број који означава жељени редослед сортирања; 1 за узлазни редослед (подразумевано), -1 за опадајући редослед"}, "GETPIVOTDATA": {"a": "(поље_података; пивот_табела; [поље]; [ставка]; ...)", "d": "Екстрахује податке из PivotTable табеле", "ad": "је име поља података из којег треба извући податке!је референца на ћелију или опсег ћелија у PivotTable који садржи податке које желите да преузмете!поље на које се односи!поље на које се односи"}, "IMPORTRANGE": {"a": "(url_табеле, опсег_стринга)", "d": "Увози опсег ћелија из одређене табеле", "ad": "je URL табеле одакле ће подаци бити увезени!је опсег за увоз"}}