{"DATE": "DATUM", "DATEDIF": "DATEDIF", "DATEVALUE": "DATUMWAARDE", "DAY": "DAG", "DAYS": "DAGEN", "DAYS360": "DAGEN360", "EDATE": "ZELFDE.DAG", "EOMONTH": "LAATSTE.DAG", "HOUR": "UUR", "ISOWEEKNUM": "ISO.WEEKNUMMER", "MINUTE": "MINUUT", "MONTH": "MAAND", "NETWORKDAYS": "NETTO.WERKDAGEN", "NETWORKDAYS.INTL": "NETWERKDAGEN.INTL", "NOW": "NU", "SECOND": "SECONDE", "TIME": "TIJD", "TIMEVALUE": "TIJDWAARDE", "TODAY": "VANDAAG", "WEEKDAY": "WEEKDAG", "WEEKNUM": "WEEKNUMMER", "WORKDAY": "WERKDAG", "WORKDAY.INTL": "WERKDAG.INTL", "YEAR": "JAAR", "YEARFRAC": "JAAR.DEEL", "BESSELI": "BESSEL.I", "BESSELJ": "BESSEL.J", "BESSELK": "BESSEL.K", "BESSELY": "BESSEL.Y", "BIN2DEC": "BIN.N.DEC", "BIN2HEX": "BIN.N.HEX", "BIN2OCT": "BIN.N.OCT", "BITAND": "BIT.EN", "BITLSHIFT": "BIT.VERSCHUIF.LINKS", "BITOR": "BIT.OF", "BITRSHIFT": "BIT.VERSCHUIF.RECHTS", "BITXOR": "BIT.EX.OF", "COMPLEX": "COMPLEX", "CONVERT": "CONVERTEREN", "DEC2BIN": "DEC.N.BIN", "DEC2HEX": "DEC.N.HEX", "DEC2OCT": "DEC.N.OCT", "DELTA": "DELTA", "ERF": "FOUTFUNCTIE", "ERF.PRECISE": "FOUTFUNCTIE.NAUWKEURIG", "ERFC": "FOUT.COMPLEMENT", "ERFC.PRECISE": "FOUT.COMPLEMENT.NAUWKEURIG", "GESTEP": "GROTER.DAN", "HEX2BIN": "HEX.N.BIN", "HEX2DEC": "HEX.N.DEC", "HEX2OCT": "HEX.N.OCT", "IMABS": "C.ABS", "IMAGINARY": "C.IM.DEEL", "IMARGUMENT": "C.ARGUMENT", "IMCONJUGATE": "C.<PERSON>EGD", "IMCOS": "C.COS", "IMCOSH": "C.COSH", "IMCOT": "C.COT", "IMCSC": "C.COSEC", "IMCSCH": "C.COSECH", "IMDIV": "C<PERSON>U<PERSON>IE<PERSON>", "IMEXP": "C.EXP", "IMLN": "C.LN", "IMLOG10": "C.LOG10", "IMLOG2": "C.LOG2", "IMPOWER": "C.<PERSON>", "IMPRODUCT": "C.PRODUCT", "IMREAL": "C.REEEL.DEEL", "IMSEC": "C.SEC", "IMSECH": "C.SECH", "IMSIN": "C.SIN", "IMSINH": "C.SINH", "IMSQRT": "C.WORTEL", "IMSUB": "C.VERSCHIL", "IMSUM": "C.SOM", "IMTAN": "C.TAN", "OCT2BIN": "OCT.N.BIN", "OCT2DEC": "OCT.N.DEC", "OCT2HEX": "OCT.N.HEX", "DAVERAGE": "DBGEMIDDELDE", "DCOUNT": "DBAANTAL", "DCOUNTA": "DBAANTALC", "DGET": "DBLEZEN", "DMAX": "DBMAX", "DMIN": "DBMIN", "DPRODUCT": "DBPRODUCT", "DSTDEV": "DBSTDEV", "DSTDEVP": "DBSTDEVP", "DSUM": "DBSOM", "DVAR": "DBVAR", "DVARP": "DBVARP", "CHAR": "TEKEN", "CLEAN": "WISSEN.CONTROL", "CODE": "CODE", "CONCATENATE": "TEKST.SAMENVOEGEN", "CONCAT": "TEKST.SAMENV", "DOLLAR": "EURO", "EXACT": "GELIJK", "FIND": "VIND.ALLES", "FINDB": "FINDB", "FIXED": "VAST", "LEFT": "LINKS", "LEFTB": "LEFTB", "LEN": "LENGTE", "LENB": "LENB", "LOWER": "KLEINE.LETTERS", "MID": "DEEL", "MIDB": "MIDB", "NUMBERVALUE": "NUMERIEKE.WAARDE", "PROPER": "BEGINLETTERS", "REPLACE": "VERVANGEN", "REPLACEB": "REPLACEB", "REPT": "HERHALING", "RIGHT": "RECHTS", "RIGHTB": "RIGHTB", "SEARCH": "VIND.SPEC", "SEARCHB": "SEARCHB", "SUBSTITUTE": "SUBSTITUEREN", "T": "T", "T.TEST": "T.TEST", "TEXT": "TEKST", "TEXTJOIN": "TEKST.COMBINEREN", "TREND": "TREND", "TRIM": "SPATIES.WISSEN", "TRIMMEAN": "GETRIMD.GEM", "TTEST": "<PERSON><PERSON>", "UNICHAR": "UNITEKEN", "UNICODE": "UNICODE", "UPPER": "HOOFDLETTERS", "VALUE": "WAARDE", "AVEDEV": "GEM.DEVIATIE", "AVERAGE": "GEMIDDELDE", "AVERAGEA": "GEMIDDELDEA", "AVERAGEIF": "GEMIDDELDE.ALS", "AVERAGEIFS": "GEMIDDELDEN.ALS", "BETADIST": "BETAVERD", "BETAINV": "BETAINV", "BETA.DIST": "BETA.VERD", "BETA.INV": "BETA.INV", "BINOMDIST": "BINOMIALE.VERD", "BINOM.DIST": "BINOM.VERD", "BINOM.DIST.RANGE": "BINOM.VERD.BEREIK", "BINOM.INV": "BINOMIALE.INV", "CHIDIST": "CHI.KWADRAAT", "CHIINV": "CHI.KWADRAAT.INV", "CHITEST": "CHI.TOETS", "CHISQ.DIST": "CHIKW.VERD", "CHISQ.DIST.RT": "CHIKW.VERD.RECHTS", "CHISQ.INV": "CHIKW.INV", "CHISQ.INV.RT": "CHIKW.INV.RECHTS", "CHISQ.TEST": "CHIKW.TEST", "CONFIDENCE": "BETROUWBAARHEID", "CONFIDENCE.NORM": "VERTROUWELIJKHEID.NORM", "CONFIDENCE.T": "VERTROUWELIJKHEID.T", "CORREL": "CORRELATIE", "COUNT": "AANTAL", "COUNTA": "AANTALARG", "COUNTBLANK": "AANTAL.LEGE.CELLEN", "COUNTIF": "AANTAL.ALS", "COUNTIFS": "AANTALLEN.ALS", "COVAR": "COVARIANTIE", "COVARIANCE.P": "COVARIANTIE.P", "COVARIANCE.S": "COVARIANTIE.S", "CRITBINOM": "CRIT.BINOM", "DEVSQ": "DEV.KWAD", "EXPON.DIST": "EXPON.VERD.N", "EXPONDIST": "EXPON.VERD", "FDIST": "F.VERDELING", "FINV": "F.INVERSE", "FTEST": "<PERSON><PERSON>", "F.DIST": "F.<PERSON>", "F.DIST.RT": "F.VERD.RECHTS", "F.INV": "F.INV", "F.INV.RT": "F.INV.RECHTS", "F.TEST": "F.TEST", "FISHER": "FISHER", "FISHERINV": "FISHER.INV", "FORECAST": "VOORSPELLEN", "FORECAST.ETS": "VOORSPELLEN.ETS", "FORECAST.ETS.CONFINT": "VOORSPELLEN.ETS.CONFINT", "FORECAST.ETS.SEASONALITY": "VOORSPELLEN.ETS.SEASONALITY", "FORECAST.ETS.STAT": "FORECAST.ETS.STAT", "FORECAST.LINEAR": "VOORSPELLEN.LINEAR", "FREQUENCY": "INTERVAL", "GAMMA": "GAMMA", "GAMMADIST": "GAMMA.VERD", "GAMMA.DIST": "GAMMA.VERD.N", "GAMMAINV": "GAMMA.INV", "GAMMA.INV": "GAMMA.INV.N", "GAMMALN": "GAMMA.LN", "GAMMALN.PRECISE": "GAMMA.LN.NAUWKEURIG", "GAUSS": "GAUSS", "GEOMEAN": "MEETK.GEM", "GROWTH": "GROEI", "HARMEAN": "HARM.GEM", "HYPGEOM.DIST": "HYPGEOM.VERD", "HYPGEOMDIST": "HYPERGEO.VERD", "INTERCEPT": "SNIJPUNT", "KURT": "KURTOSIS", "LARGE": "GROOTSTE", "LINEST": "LIJNSCH", "LOGEST": "LOGSCH", "LOGINV": "LOG.NORM.INV", "LOGNORM.DIST": "LOGNORM.VERD", "LOGNORM.INV": "LOGNORM.INV", "LOGNORMDIST": "LOG.NORM.VERD", "MAX": "MAX", "MAXA": "MAXA", "MAXIFS": "MAX.ALS.VOORWAARDEN", "MEDIAN": "MEDIAAN", "MIN": "MIN", "MINA": "MINA", "MINIFS": "MIN.ALS.VOORWAARDEN", "MODE": "MODUS", "MODE.MULT": "MODUS.MEERV", "MODE.SNGL": "MODUS.ENKELV", "NEGBINOM.DIST": "NEGBINOM.VERD", "NEGBINOMDIST": "NEG.BINOM.VERD", "NORM.DIST": "NORM.VERD.N", "NORM.INV": "NORM.INV.N", "NORM.S.DIST": "NORM.S.VERD", "NORM.S.INV": "NORM.S.INV", "NORMDIST": "NORM.VERD", "NORMINV": "NORM.INV", "NORMSDIST": "STAND.NORM.VERD", "NORMSINV": "STAND.NORM.INV", "PEARSON": "PEARSON", "PERCENTILE": "PERCENTIEL", "PERCENTILE.EXC": "PERCENTIEL.EXC", "PERCENTILE.INC": "PERCENTIEL.INC", "PERCENTRANK": "PERCENT.RANG", "PERCENTRANK.EXC": "PROCENTRANG.EXC", "PERCENTRANK.INC": "PROCENTRANG.INC", "PERMUT": "PERMUTATIES", "PERMUTATIONA": "PERMUTATIE.A", "PHI": "PHI", "POISSON": "POISSON", "POISSON.DIST": "POISSON.VERD", "PROB": "KANS", "QUARTILE": "KWARTIEL", "QUARTILE.INC": "KWARTIEL.INC", "QUARTILE.EXC": "KWARTIEL.EXC", "RANK.AVG": "RANG.GEMIDDELDE", "RANK.EQ": "RANG.GELIJK", "RANK": "RANG", "RSQ": "R.<PERSON>", "SKEW": "SCHEEFHEID", "SKEW.P": "SCHEEFHEID.P", "SLOPE": "RICHTING", "SMALL": "KLEINSTE", "STANDARDIZE": "NORMALISEREN", "STDEV": "STDEV", "STDEV.P": "STDEV.P", "STDEV.S": "STDEV.S", "STDEVA": "STDEVA", "STDEVP": "STDEVP", "STDEVPA": "STDEVPA", "STEYX": "STAND.FOUT.YX", "TDIST": "T.VERD", "TINV": "TINV", "T.DIST": "T.DIST", "T.DIST.2T": "T.VERD.2T", "T.DIST.RT": "T.VERD.RECHTS", "T.INV": "T.INV", "T.INV.2T": "T.INV.2T", "VAR": "VAR", "VAR.P": "VAR.P", "VAR.S": "VAR.S", "VARA": "VARA", "VARP": "VARP", "VARPA": "VARPA", "WEIBULL": "WEIBULL", "WEIBULL.DIST": "WEIBULL.VERD", "Z.TEST": "Z.TEST", "ZTEST": "Z.<PERSON>", "ACCRINT": "SAMENG.RENTE", "ACCRINTM": "SAMENG.RENTE.V", "AMORDEGRC": "AMORDEGRC", "AMORLINC": "AMORLINC", "COUPDAYBS": "COUP.DAGEN.BB", "COUPDAYS": "COUP.DAGEN", "COUPDAYSNC": "COUP.DAGEN.VV", "COUPNCD": "COUP.DATUM.NB", "COUPNUM": "COUP.AANTAL", "COUPPCD": "COUP.DATUM.VB", "CUMIPMT": "CUM.RENTE", "CUMPRINC": "CUM.HOOFDSOM", "DB": "DB", "DDB": "DDB", "DISC": "DISCONTO", "DOLLARDE": "EURO.DE", "DOLLARFR": "EURO.BR", "DURATION": "DUUR", "EFFECT": "EFFECT.RENTE", "FV": "TW", "FVSCHEDULE": "TOEK.WAARDE2", "INTRATE": "RENTEPERCENTAGE", "IPMT": "IBET", "IRR": "IR", "ISPMT": "ISBET", "MDURATION": "AANG.DUUR", "MIRR": "GIR", "NOMINAL": "NOMINALE.RENTE", "NPER": "NPER", "NPV": "NHW", "ODDFPRICE": "AFW.ET.PRIJS", "ODDFYIELD": "AFW.ET.REND", "ODDLPRICE": "AFW.LT.PRIJS", "ODDLYIELD": "AFW.LT.REND", "PDURATION": "PDUUR", "PMT": "BET", "PPMT": "PBET", "PRICE": "PRIJS.NOM", "PRICEDISC": "PRIJS.DISCONTO", "PRICEMAT": "PRIJS.VERVALDAG", "PV": "HW", "RATE": "RENTE", "RECEIVED": "OPBRENGST", "RRI": "RRI", "SLN": "LIN.AFSCHR", "SYD": "SYD", "TBILLEQ": "SCHATK.OBL", "TBILLPRICE": "SCHATK.PRIJS", "TBILLYIELD": "SCHATK.REND", "VDB": "VDB", "XIRR": "IR.SCHEMA", "XNPV": "NHW2", "YIELD": "RENDEMENT", "YIELDDISC": "REND.DISCONTO", "YIELDMAT": "REND.VERVAL", "ABS": "ABS", "ACOS": "BOOGCOS", "ACOSH": "BOOGCOSH", "ACOT": "BOOGCOT", "ACOTH": "BOOGCOTH", "AGGREGATE": "AGGREGAAT", "ARABIC": "ARABISCH", "ASC": "ASC", "ASIN": "BOOGSIN", "ASINH": "BOOGSINH", "ATAN": "BOOGTAN", "ATAN2": "BOOGTAN2", "ATANH": "BOOGTANH", "BASE": "BASIS", "CEILING": "AFRONDEN.BOVEN", "CEILING.MATH": "AFRONDEN.BOVEN.WISK", "CEILING.PRECISE": "CEILING.PRESIZE", "COMBIN": "COMBINATIES", "COMBINA": "COMBIN.A", "COS": "COS", "COSH": "COSH", "COT": "COT", "COTH": "COTH", "CSC": "COSEC", "CSCH": "COSECH", "DECIMAL": "DECIMAAL", "DEGREES": "GRADEN", "ECMA.CEILING": "ECMA.CEILING", "EVEN": "EVEN", "EXP": "EXP", "FACT": "FACULTEIT", "FACTDOUBLE": "DUBBELE.FACULTEIT", "FLOOR": "AFRONDEN.BENEDEN", "FLOOR.PRECISE": "FLOOR.PRECISE", "FLOOR.MATH": "AFRONDEN.BENEDEN.WISK", "GCD": "GGD", "INT": "INTEGER", "ISO.CEILING": "ISO.CEILING", "LCM": "KGV", "LN": "LN", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "DETERMINANTMAT", "MINVERSE": "INVERSEMAT", "MMULT": "PRODUCTMAT", "MOD": "REST", "MROUND": "AFRONDEN.N.VEELVOUD", "MULTINOMIAL": "MULTINOMIAAL", "MUNIT": "EENHEIDMAT", "ODD": "ONEVEN", "PI": "PI", "POWER": "MACHT", "PRODUCT": "PRODUCT", "QUOTIENT": "QUOTIENT", "RADIANS": "RADIALEN", "RAND": "ASELECT", "RANDARRAY": "ASELECT.MATRIX", "RANDBETWEEN": "ASELECTTUSSEN", "ROMAN": "ROMEINS", "ROUND": "AFRONDEN", "ROUNDDOWN": "AFRONDEN.NAAR.BENEDEN", "ROUNDUP": "AFRONDEN.NAAR.BOVEN", "SEC": "SEC", "SECH": "SECH", "SERIESSUM": "SOM.MACHTREEKS", "SIGN": "POS.NEG", "SIN": "SIN", "SINH": "SINH", "SQRT": "WORTEL", "SQRTPI": "WORTEL.PI", "SUBTOTAL": "SUBTOTAAL", "SUM": "SOM", "SUMIF": "SOM.ALS", "SUMIFS": "SOMMEN.ALS", "SUMPRODUCT": "SOMPRODUCT", "SUMSQ": "KWADRATENSOM", "SUMX2MY2": "SOM.X2MINY2", "SUMX2PY2": "SOM.X2PLUSY2", "SUMXMY2": "SOM.XMINY.2", "TAN": "TAN", "TANH": "TANH", "TRUNC": "GEHEEL", "ADDRESS": "ADRES", "CHOOSE": "KIEZEN", "COLUMN": "KOLOM", "COLUMNS": "KOLOMMEN", "FORMULATEXT": "FORMULETEKST", "HLOOKUP": "HORIZ.ZOEKEN", "HYPERLINK": "HYPERLINK", "INDEX": "INDEX", "INDIRECT": "INDIRECT", "LOOKUP": "ZOEKEN", "MATCH": "VERGELIJKEN", "OFFSET": "VERSCHUIVING", "ROW": "RIJ", "ROWS": "RIJEN", "TRANSPOSE": "TRANSPONEREN", "UNIQUE": "UNIEK", "VLOOKUP": "VERT.ZOEKEN", "XLOOKUP": "X.ZOEKEN", "CELL": "CELL", "ERROR.TYPE": "TYPE.FOUT", "ISBLANK": "ISLEEG", "ISERR": "ISFOUT2", "ISERROR": "ISFOUT", "ISEVEN": "IS.EVEN", "ISFORMULA": "ISFORMULE", "ISLOGICAL": "ISLOGISCH", "ISNA": "ISNB", "ISNONTEXT": "ISGEENTEKST", "ISNUMBER": "ISGETAL", "ISODD": "IS.ONEVEN", "ISREF": "ISVERWIJZING", "ISTEXT": "ISTEKST", "N": "N", "NA": "NB", "SHEET": "BLAD", "SHEETS": "BLADEN", "TYPE": "TYPE", "AND": "EN", "FALSE": "ONWAAR", "IF": "ALS", "IFS": "ALS.VOORWAARDEN", "IFERROR": "ALS.FOUT", "IFNA": "ALS.NB", "NOT": "NIET", "OR": "OF", "SWITCH": "SCHAKELEN", "TRUE": "WAAR", "XOR": "EX.OF", "TEXTBEFORE": "TEKST.VOOR", "TEXTAFTER": "TEKST.NA", "TEXTSPLIT": "TEKST.SPLITSEN", "WRAPROWS": "OMLOOP.RIJEN", "VSTACK": "VERT.STAPELEN", "HSTACK": "HOR.STAPELEN", "CHOOSEROWS": "KIES.RIJEN", "CHOOSECOLS": "KIES.KOLOMMEN", "TOCOL": "NAAR.KOLOM", "TOROW": "NAAR.RIJ", "WRAPCOLS": "OMLOOP.KOLOMMEN", "TAKE": "NEMEN", "DROP": "WEGLATEN", "SEQUENCE": "REEKS", "EXPAND": "UITBREIDEN", "XMATCH": "X.VERGELIJKEN", "FILTER": "FILTER", "ARRAYTOTEXT": "ARRAYTOTEXT", "SORT": "SORTEREN", "SORTBY": "SORTEREN.OP", "GETPIVOTDATA": "DRAAITABEL.OPHALEN", "IMPORTRANGE": "IMPORTRANGE", "LocalFormulaOperands": {"StructureTables": {"h": "Headers", "d": "Data", "a": "All", "tr": "This row", "t": "Totals"}, "CONST_TRUE_FALSE": {"t": "TRUE", "f": "FALSE"}, "CONST_ERROR": {"nil": "#NULL!", "div": "#DIV/0!", "value": "#VALUE!", "ref": "#REF!", "name": "#NAME\\?", "num": "#NUM!", "na": "#N/A", "getdata": "#GETTING_DATA", "uf": "#UNSUPPORTED_FUNCTION!", "calc": "#CALC!"}, "CELL_FUNCTION_INFO_TYPE": {"address": "adres", "col": "kolom", "color": "kleur", "contents": "inhoud", "filename": "bestandsnaam", "format": "notatie", "parentheses": "<PERSON><PERSON><PERSON><PERSON>", "prefix": "voorvoegsel", "protect": "bescherming", "row": "rij", "type": "type", "width": "breedte"}}}