{"DATE": {"a": "(<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>)", "d": "Gibt die Zahl zurück, die das Datum im Datums-/Uhrzeitcode darstellt", "ad": "ist eine Zahl zwischen 1900 oder 1904 (je nach dem Datumssystem der Arbeitsmappe) und 9999!ist eine Zahl zwischen 1 und 12, die für den Monat des Jahrs steht!ist eine Zahl zwischen 1 und 31, die für den Tag des Monats steht"}, "DATEDIF": {"a": "(Ausgangsdatum;Enddatum;Einheit)", "d": "Berechnet der Differenz zwischen zwei Datumsangaben (Start- und Enddatum), basierend auf der angegebenen Einheit", "ad": "Ein Datum, das das erste Datum oder das Anfangsdatum eines bestimmten Zeitraums darstellt!Ein Datum, das das letzte Datum des Zeitraums darstellt!Der Typ der Informationen, die zurückgegeben werden sollen"}, "DATEVALUE": {"a": "(Datumstext)", "d": "Wandelt ein als Text vorliegendes Datum in eine Zahl um, die das Datum im Datums-/Uhrzeitcode darstellt", "ad": "ist Text, der ein Datum in einem Spreadsheet Editor-Datumsformat darstellt (zwischen 1.1.1900 oder 1.1.1904 (je nach dem Datumssystem der Arbeitsmappe) und dem 31.12.9999)"}, "DAY": {"a": "(<PERSON><PERSON>)", "d": "Wandelt eine fortlaufende Zahl in eine Zahl von 1 bis 31 für den Tag des Monats um", "ad": "ist der Code für Datum und Zeit, den Spreadsheet Editor für Datums- und Zeitberechnungen verwendet"}, "DAYS": {"a": "(Zieldatum; Ausgangsdatum)", "d": "Gibt die Anzahl der Tage zwischen den beiden Datumswerten zurück.", "ad": "Ausgangsdatum und Zieldatum sind die beiden Datumswerte, für die die Anzahl der dazwischen liegenden Tage ermittelt werden soll!Ausgangsdatum und Zieldatum sind die beiden Datumswerte, für die die Anzahl der dazwischen liegenden Tage ermittelt werden soll"}, "DAYS360": {"a": "(Ausgangsdatum; Enddatum; [Methode])", "d": "<PERSON><PERSON><PERSON><PERSON>, aus<PERSON><PERSON> von <PERSON>, das 360 (12 Monate mit je 30 Tagen)<PERSON><PERSON> umfasst, die Anzahl der zwischen zwei Tagesdaten liegenden Tage", "ad": "ist das Datum des ersten Tages der Zeitperiode, die Sie berechnen möchten!ist das Datum des letzten Tages der Zeitperiode, die Sie berechnen möchten!ist ein Wahrheitswert, der die europäische Berechnungsmethode bestimmt: FALSCH oder fehlt = U.S. (NASD); WAHR = Europäisch."}, "EDATE": {"a": "(Ausgangsdatum; Monate)", "d": "Gibt die fortlaufende Zahl des Datums zurück, das eine bestimmte Anzahl von <PERSON>ten vor bzw. nach dem Ausgangsdatum liegt", "ad": "ist die fortlaufende Zahl, die das Ausgangsdatum darstellt!ist die Anzahl der Monate vor oder nach Ausgangsdatum"}, "EOMONTH": {"a": "(Ausgangsdatum; Monate)", "d": "Gibt die fortlaufende Zahl des letzten Tags des Monats vor oder nach einer bestimmten Anzahl von Monaten zurück.", "ad": "ist eine fortlaufende Zahl, die das Ausgangsdatum darstellt.!ist die Anzahl der Monate vor oder nach dem Ausgangsdatum."}, "HOUR": {"a": "(<PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON> den Wert für die Stunde von 0 (00:00:00 Uhr) bis 23 (23:00:00 Uhr) zurück.", "ad": "ist der Code für Datum und Zeit, den Spreadsheet Editor für Datums- und Zeitberechnungen verwendet"}, "ISOWEEKNUM": {"a": "(Datum)", "d": "Gibt die ISO-Wochennummer des Jahres für ein angegebenes Datum zurück", "ad": "ist der von Spreadsheet Editor für die Datums- und Uhrzeitberechnung verwendete Datums-Uhrzeitcode"}, "MINUTE": {"a": "(<PERSON><PERSON>)", "d": "Wandelt eine fortlaufende Zahl in einen Wert zwischen 0 und 59 für Minuten um", "ad": "ist der Code für Datum und Zeit, den Spreadsheet Editor für Datums- und Zeitberechnungen verwendet"}, "MONTH": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t eine Zahl von 1 (<PERSON><PERSON><PERSON>) bis 12 (Dezember) für den Monat zurück.", "ad": "ist der Code für Datum und Zeit, den Spreadsheet Editor für Datums- und Zeitberechnungen verwendet"}, "NETWORKDAYS": {"a": "(Ausgangsdatum; Enddatum; [<PERSON><PERSON><PERSON>_Tage])", "d": "Gibt die Anzahl der Arbeitstage in einem Zeitintervall zurück", "ad": "ist die fortlaufende Zahl, die das Ausgangsdatum repräsentiert!ist die fortlaufende Zahl, die Enddatum repräsentiert!ist eine optionale Gruppe von ein oder mehreren fortlaufenden Zahlen, die alle Arten von arbeitsfreien Tagen (Feiertage, Freischichten, etc.) repräsentieren"}, "NETWORKDAYS.INTL": {"a": "(Ausgangsdatum; Enddatum; [<PERSON><PERSON><PERSON><PERSON>]; [<PERSON><PERSON><PERSON>_Tage])", "d": "Gibt die Anzahl der vollständigen Arbeitstage zwischen zwei Daten mit benutzerdefinierten Wochenendparametern zurück", "ad": "ist die fortlaufende Zahl, die das Ausgangsdatum repräsentiert!ist die fortlaufende Zahl, die Enddatum repräsentiert!ist eine Zahl oder eine Zeichenfolge, die den Fall von Wochenenden angibt!ist eine optionale Gruppe von ein oder mehreren fortlaufenden Zahlen, die alle Arten von arbeitsfreien Tagen (Feiertage, Freischichten, usw.) darstellen"}, "NOW": {"a": "()", "d": "Gibt die fortlaufende Zahl des aktuellen Datums und der aktuellen Uhrzeit zurück.", "ad": ""}, "SECOND": {"a": "(<PERSON><PERSON>)", "d": "Wandelt eine fortlaufende Zahl in einen Wert von 0 bis 59 für die Sekunde um.", "ad": "ist der Code für Datum und Zeit, den Spreadsheet Editor für Datums- und Zeitberechnungen verwendet"}, "TIME": {"a": "(Stunde; Minute; Sekunde)", "d": "Gibt die fortlaufende Zahl einer bestimmten Uhrzeit zurück", "ad": "ist eine Zahl von 0 bis 23, die für die Stunde steht!ist eine Zahl von 0 bis 59, die für die Minute steht!ist eine Zahl von 0 bis 59, die für die Sekunde steht"}, "TIMEVALUE": {"a": "(Zeit)", "d": "Wandelt eine als Text vorliegende Zeitangabe in eine fortlaufende Zahl von 0 (00:00:00) bis 0.999988426 (23:59:59) um. Formatieren Sie die Zahl nach Eingabe der Formel mit einem Zeitformat.", "ad": "ist die Zeichenfolge einer Zeitangabe in einem gültigen Spreadsheet Editor-Zeitformat (der Datumsteil der Zeichenfolge wird ignoriert.)"}, "TODAY": {"a": "()", "d": "Gibt die fortlaufende Zahl des heutigen Datums zurück.", "ad": ""}, "WEEKDAY": {"a": "(<PERSON><PERSON>; [Typ])", "d": "Wandelt eine fortlaufende Zahl in einen Wochentag um.", "ad": "ist der Code für Datum und Zeit, den Spreadsheet Editor für Datums- und Zeitberechnungen verwendet!legt den Rückgabewert-Typ fest: 1 für Sonntag = 1 bis Samstag = 7;  2 für Montag = 1 bis  Sonntag =7;  3 für Montag = 0 bis Sonntag= 6"}, "WEEKNUM": {"a": "(<PERSON><PERSON><PERSON><PERSON>_Zahl; [<PERSON><PERSON>_<PERSON><PERSON>])", "d": "Gibt die Wochennummer für das Jahr zurück", "ad": "ist der von Spreadsheet Editor für Datums- und Uhrzeitberechnungen verwendete Code für Datum und Uhrzeit!ist eine Zahl (1 oder 2), die den Typ des Rückgabewertes bestimmt"}, "WORKDAY": {"a": "(Ausgangsdatum; Tage; [<PERSON><PERSON><PERSON>_Tage])", "d": "Gibt die fortlaufende Zahl des Datums zurück, vor oder nach einer bestimmten Anzahl von Arbeitstagen", "ad": "ist die fortlaufende Zahl, die Ausgangsdatum repräsentiert!ist die Anzahl der Arbeitstage vor oder nach Ausgangsdatum!ist eine optionale Matrix von ein oder mehreren fortlaufenden Zahlen, die alle Arten von arbeitsfreien Tagen (Feiertage, etc.) repräsentieren"}, "WORKDAY.INTL": {"a": "(Ausgangsdatum; Tage; [<PERSON><PERSON><PERSON>nde]; [<PERSON><PERSON><PERSON>_Tage])", "d": "Gibt die fortlaufende Nummer des Datums vor oder nach einer angegebenen Anzahl von Arbeitstagen mit benutzerdefinierten Wochenendparametern zurück", "ad": "ist eine fortlaufende Datumsnummer, die das Ausgangsdatum darstellt!ist die Anzahl der nicht am Wochenende liegenden und nicht freien Tage vor oder nach dem Ausgangsdatum!ist eine Zahl oder Zeichenfolge, die das Auftreten von Wochenenden angibt!ist ein optionales Array aus einer oder mehreren fortlaufenden Datumsnummern, die aus dem Arbeitskalender ausgeschlossen werden sollen, wie etwa Bundes-/Landes- und bewegliche Feiertage"}, "YEAR": {"a": "(<PERSON><PERSON>)", "d": "Wandelt eine fortlaufende Zahl im Bereich von 1900 - 9999 in eine Jahreszahl um.", "ad": "ist der Code für Datum und Zeit, den Spreadsheet Editor für Datums- und Zeitberechnungen verwendet"}, "YEARFRAC": {"a": "(Ausgangsdatum; Enddatum; [Basis])", "d": "Wandelt die Anzahl der ganzen Tage zwischen Ausgangsdatum und Enddatum in Bruchteile von Jahren um", "ad": "ist die fortlaufende Zahl, die das Ausgangsdatum angibt!ist die fortlaufende Zahl, die das Enddatum angibt!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "BESSELI": {"a": "(x; n)", "d": "Gibt die geänderte Besselfunktion In(x) zurück", "ad": "ist der Wert, für den die Funktion ausgewertet werden soll!ist die Ordnung der Besselfunktion"}, "BESSELJ": {"a": "(x; n)", "d": "Gibt die Besselfunktion Jn(x) zurück", "ad": "ist der Wert, für den die Funktion ausgewertet werden soll!ist die Ordnung der Besselfunktion"}, "BESSELK": {"a": "(x; n)", "d": "Gibt die modifizierte Besselfunktion Kn(x) zurück", "ad": "ist der Wert, für den die Funktion ausgewertet werden soll!ist die Ordnung der Besselfunktion"}, "BESSELY": {"a": "(x; n)", "d": "Gibt die Besselfunktion Yn(x) zurück", "ad": "ist der Wert, für den die Funktion ausgewertet werden soll!ist die Ordnung der Besselfunktion"}, "BIN2DEC": {"a": "(<PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON> eine binäre <PERSON> (Dualzahl) in eine dezimale Zahl um", "ad": "ist die bin<PERSON>re Zahl, die <PERSON> umwandeln möchten"}, "BIN2HEX": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> eine binä<PERSON> (Dualzahl) in eine hexadezimale <PERSON> um", "ad": "ist die bin<PERSON>re Zahl, die <PERSON> umwandeln möchten!ist die Zahl der verwendeten Stellen"}, "BIN2OCT": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON> eine binäre <PERSON> (Dualzahl) in eine oktale Zahl um", "ad": "ist die bin<PERSON>re Zahl, die <PERSON> umwandeln möchten!ist die Zahl der verwendeten Stellen"}, "BITAND": {"a": "(<PERSON><PERSON>1; <PERSON><PERSON>2)", "d": "Gibt ein bitweises 'Und' zweier Zahlen zurück", "ad": "ist die dezimale Darstellung der auszuwertenden Binärzahl!ist die dezimale Darstellung der auszuwertenden Binärzahl"}, "BITLSHIFT": {"a": "(Zahl; Verschiebebetrag)", "d": "Gibt eine Zahl zurück, der um Verschiebebetrag Bits nach links verschoben ist", "ad": "ist die dezimale Darstellung der auszuwertenden Binärzahl!ist die Anzahl der Bits, um die Zahl nach links verschoben werden soll"}, "BITOR": {"a": "(<PERSON><PERSON>1; <PERSON><PERSON>2)", "d": "Gibt ein bitweises 'Oder' zweier Zahlen zurück", "ad": "ist die dezimale Darstellung der auszuwertenden Binärzahl!ist die dezimale Darstellung der auszuwertenden Binärzahl"}, "BITRSHIFT": {"a": "(Zahl; Verschiebebetrag)", "d": "Gibt eine Zahl zurück, die um Verschiebebetrag Bits nach rechts verschoben ist", "ad": "ist die dezimale Darstellung der auszuwertenden Binärzahl!ist die Anzahl der Bits, um die Zahl nach rechts verschoben werden soll"}, "BITXOR": {"a": "(<PERSON><PERSON>1; <PERSON><PERSON>2)", "d": "Gibt ein bitweises 'Ausschließliches Oder' zweier Zahlen zurück", "ad": "ist die dezimale Darstellung der auszuwertenden Binärzahl!ist die dezimale Darstellung der auszuwertenden Binärzahl"}, "COMPLEX": {"a": "(Realteil; Imaginärteil; [Suffix])", "d": "Wandelt den Real- und Imaginärteil in eine komplexe Zahl um", "ad": "ist der Realteil der komplexen Zahl!ist der Imaginärteil der komplexen Zahl!ist der Buchstabe, der als imaginäre Einheit der komplexen Zahl verwendet werden soll."}, "CONVERT": {"a": "(<PERSON><PERSON>; Von_Maßeinheit; In_Maßeinheit)", "d": "Wandelt eine Zahl von einem Maßsystem in ein anderes um", "ad": "ist der Wert in Von_Maßeinheit, der umgewandelt werden soll!ist die Maßeinheit von Zahl!ist die Maßeinheit des Ergebnisses"}, "DEC2BIN": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "Wandelt eine dezimale Zahl in eine binäre Zahl (Dualzahl) um", "ad": "ist die dezimale ganze <PERSON>, die <PERSON> umwandeln möchten!ist die Zahl der verwendeten Stellen"}, "DEC2HEX": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "Wandelt eine dezimale Zahl in eine hexadezimale Zahl um", "ad": "ist die dezimale ganze <PERSON>, die <PERSON> umwandeln möchten!ist die Zahl der verwendeten Stellen"}, "DEC2OCT": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "Wandelt eine dezimale Zahl in eine oktale Zahl um", "ad": "ist die dezimale ganze <PERSON>, die <PERSON> umwandeln möchten!ist die Zahl der verwendeten Stellen"}, "DELTA": {"a": "(<PERSON>ahl1; [<PERSON>ahl2])", "d": "Überprü<PERSON>, ob zwei Werte gleich sind", "ad": "ist die erste Zahl!ist die zweite Zahl"}, "ERF": {"a": "(<PERSON><PERSON><PERSON>_Grenze; [<PERSON><PERSON><PERSON>_<PERSON><PERSON>])", "d": "Gibt die Gauss'sche Fehlerfunktion zurück", "ad": "ist die untere Grenze für die Integration von GAUSSFEHLER!ist die obere Grenze für die Integration von GAUSSFEHLER"}, "ERF.PRECISE": {"a": "(X)", "d": "Gibt die Gauss'sche Fehlerfunktion zurück", "ad": "ist die untere Grenze für die Integration von GAUSSF.GENAU"}, "ERFC": {"a": "(Untere_Grenze)", "d": "Gibt das Komplement zur Gauss'schen Fehlerfunktion zurück", "ad": "ist die untere Grenze für die Integration von GAUSSFEHLER"}, "ERFC.PRECISE": {"a": "(X)", "d": "Gibt das Komplement zur Gauss'schen Fehlerfunktion zurück", "ad": "ist die untere Grenze für die Integration von GAUSSFKOMPL.GENAU"}, "GESTEP": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ob eine Zahl größer als ein gegebener Schwellenwert ist", "ad": "ist der an Schritt zu überprüfende Wert!ist der Schwellenwert"}, "HEX2BIN": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON>t eine hexadezimale Zahl in eine Binärzahl um", "ad": "ist die hexadezimale Zahl, die Si<PERSON> umwandeln möchten!ist die Anzahl der zu verwendenden Zeichen"}, "HEX2DEC": {"a": "(<PERSON><PERSON>)", "d": "Wandelt eine hexadezimale <PERSON> in eine dezimale Zahl um", "ad": "ist die hexadezimale <PERSON>, die Si<PERSON> umwandeln möchten"}, "HEX2OCT": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "Wandelt eine hexadezimale Zahl in eine oktale Zahl um", "ad": "ist die hexadezimale <PERSON>ahl, die <PERSON> umwandeln möchten!ist die Zahl der verwendeten Stellen"}, "IMABS": {"a": "(Komplexe_Zahl)", "d": "Gibt den Absolutwert einer komplexen Zahl zurück", "ad": "ist eine komplexe <PERSON>ahl, deren Absolutwert Sie bestimmen möchten"}, "IMAGINARY": {"a": "(Komplexe_Zahl)", "d": "Gibt den Imaginärteil einer komplexen Zahl zurück", "ad": "ist die komplexe Zahl, deren Imaginärteil Sie bestimmen möchten"}, "IMARGUMENT": {"a": "(Komplexe_Zahl)", "d": "G<PERSON>t den Winkel im Bogenmaß zur Darstellung der komplexen Zahl in trigonometrischer Schreibweise zurück", "ad": "ist die komplexe Zahl, deren Argument Sie bestimmen möchten"}, "IMCONJUGATE": {"a": "(Komplexe_Zahl)", "d": "Gibt die konjugiert komplexe Zahl zu einer komplexen Zahl zurück", "ad": "ist die komplexe Zahl, deren Konjugierte Sie bestimmen möchten"}, "IMCOS": {"a": "(Komplexe_Zahl)", "d": "G<PERSON>t den Kosinus einer komplexen Zahl zurück", "ad": "ist die komplexe Zahl, deren Kosinus Sie bestimmen möchten"}, "IMCOSH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Kosinus einer komplexen Zahl zurück", "ad": "ist eine komplexe <PERSON>, deren hyperbolischen Kosinus Sie bestimmen möchten"}, "IMCOT": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Kotangens einer komplexen Zahl zurück", "ad": "ist eine komplexe <PERSON>, deren Kotangens Sie bestimmen möchten"}, "IMCSC": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Kosekans einer komplexen Zahl zurück", "ad": "ist eine komplexe <PERSON>, deren Kosekans Sie bestimmen möchten"}, "IMCSCH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Kosekans einer komplexen Zahl zurück", "ad": "ist eine komplexe <PERSON>, deren hyperbolischen Kosekans Sie bestimmen möchten"}, "IMDIV": {"a": "(Komplexe_Zahl1; Komplexe_Zahl2)", "d": "Gibt den Quotient zweier komplexer Zahlen zurück", "ad": "ist der komplexe Zähler bzw. Dividend!ist der komplexe Nenner bzw. Divisor"}, "IMEXP": {"a": "(Komplexe_Zahl)", "d": "Gibt die algebraische Form einer in exponentieller Schreibweise vorliegenden komplexen Zahl zurück", "ad": "ist die komplexe Zahl, deren exponentielle Schreibweise Sie bestimmen möchten"}, "IMLN": {"a": "(Komplexe_Zahl)", "d": "Gibt den natürlichen Logarithmus einer komplexen Zahl zurück", "ad": "ist die komplexe Zahl, deren natürlichen Logarithmus Sie bestimmen möchten"}, "IMLOG10": {"a": "(Komplexe_Zahl)", "d": "Gibt den Logarithmus einer komplexen Zahl zur Basis 10 zurück", "ad": "ist die komplexe Zahl, deren Logarithmus zur Basis 10 Sie bestimmen möchten"}, "IMLOG2": {"a": "(Komplexe_Zahl)", "d": "Gibt den Logarithmus einer komplexen Zahl zur Basis 2 zurück", "ad": "ist die komplexe Zahl, deren Logarithmus zur Basis 2 Sie bestimmen möchten"}, "IMPOWER": {"a": "(Komplexe_Zahl; Potenz)", "d": "Potenziert eine komplexe Zahl mit einer ganzen Zahl", "ad": "ist die komplexe Zahl, die Sie mit dem Exponenten potenzieren möchten!ist der Exponent, mit dem Sie die komplexe Zahl potenzieren möchten"}, "IMPRODUCT": {"a": "(Komplexe_Zahl1; [Komplexe_Zahl2]; ...)", "d": "Gibt das Produkt von 1 bis 255 komplexen Zahlen zurück", "ad": "Komplexe_Zahl1, <PERSON><PERSON>xe_Zahl2,... sind von 1 bis 255 komplexe Zahlen, die multipliziert werden können."}, "IMREAL": {"a": "(Komplexe_Zahl)", "d": "Gibt den Realteil einer komplexen Zahl zurück", "ad": "ist die komplexe Zahl, deren Realteil Sie bestimmen möchten"}, "IMSEC": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Sekans einer komplexen Zahl zurück", "ad": "ist eine komplexe <PERSON>, deren Sekans Si<PERSON> bestimmen möchten"}, "IMSECH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Sekans einer komplexen Zahl zurück", "ad": "ist eine komplexe <PERSON>, deren hyperbolischen Sekans Sie bestimmen möchten"}, "IMSIN": {"a": "(Komplexe_Zahl)", "d": "G<PERSON>t den Sinus einer komplexen Zahl zurück", "ad": "ist die komplexe Zahl, deren Sinus Sie bestimmen möchten"}, "IMSINH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Sinus einer komplexen Zahl zurück", "ad": "ist eine komplexe <PERSON>, deren hyperbolischen Sinus Sie bestimmen möchten"}, "IMSQRT": {"a": "(Komplexe_Zahl)", "d": "Gibt die Quadratwurzel einer komplexen Zahl zurück", "ad": "ist die komplexe Zahle, deren Quadratwurzel Sie bestimmen möchten"}, "IMSUB": {"a": "(Komplexe_Zahl1; Komplexe_Zahl2)", "d": "Gibt die Differenz zweier komplexer Zahlen zurück", "ad": "ist die komplexe <PERSON>ahl, von der Komplexe_Zahl2 subtrahiert werden soll!ist die komplexe <PERSON>ahl, die von Komplexe_Zahl1 subtrahiert werden soll"}, "IMSUM": {"a": "(Komplexe_Zahl1; [Komplexe_Zahl2]; ...)", "d": "Gibt die Summe von komplexen Zahlen zurück", "ad": "sind 1 bis 255 komplexe Zahlen, die addiert werden können"}, "IMTAN": {"a": "(<PERSON><PERSON>)", "d": "Gibt den Tangens einer komplexen Zahl zurück", "ad": "ist eine komplexe <PERSON>, deren Tangens Sie bestimmen möchten"}, "OCT2BIN": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "Wandelt eine oktale Zahl in eine binäre Zahl (Dualzahl) um", "ad": "ist die oktale Zahl, die <PERSON> umwandeln möchten!ist die Zahl der verwendeten Stellen"}, "OCT2DEC": {"a": "(<PERSON><PERSON>)", "d": "Wandelt eine oktale Zahl in eine dezimale Zahl um", "ad": "ist die oktale Zahl, die Si<PERSON> umwandeln möchten"}, "OCT2HEX": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>])", "d": "Wandelt eine oktale Zahl in eine hexadezimale Zahl um", "ad": "ist die oktale Zahl, die <PERSON> umwandlen möchten!ist die Zahl der verwendeten Stellen"}, "DAVERAGE": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Gibt den Mittelwert aus den ausgewählten Datenbankeinträgen zurück", "ad": "ist der Zellbereich, aus dem sich die Datenbank zusammensetzt!gibt an, welches Datenbankfeld in der Funktion benutzt wird!ist der Zellbereich, der die Suchkriterien enthält. Der Bereich besteht aus der Spaltenbeschriftung und der Zelle darunter, in der sich die Bedingung befindet"}, "DCOUNT": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Zä<PERSON><PERSON> die Zellen in einem Feld (Spalte) einer Datenbank, deren Inhalte mit den Suchkriterien übereinstimmen", "ad": "ist der Zellbereich, der die Datenbank bestimmt!gibt an, welches Datenbankfeld in der Funktion benutzt wird!ist der Zellbereich, der die Suchkriterien enthält. Der Bereich besteht aus der Spaltenbeschriftung und der Zelle darunter, in der sich die Bedingung befindet"}, "DCOUNTA": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Zä<PERSON>t die Zellen einer Datenbank, deren Inhalte mit den Suchkriterien übereinstimmen und die nicht leer sind", "ad": "ist der Zellbereich, der die Datenbank bestimmt!gibt an, welches Datenbankfeld in der Funktion benutzt wird!ist der Zellbereich, der die Suchkriterien enthält. Der Bereich besteht aus der Spaltenbeschriftung und der Zelle darunter, in der sich die Bedingung befindet"}, "DGET": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "G<PERSON>t den Datensatz in einer Datenbank zurück, der mit den angegebenen Suchkriterien übereinstimmt", "ad": "ist der Zellbereich, aus dem die Datenbank besteht!gibt an, welches Datenbankfeld in der Funktion benutzt wird!ist der Zellbereich, der die Suchkriterien enthält. Der Bereich besteht aus der Spaltenbeschriftung und der Zelle darunter, in der sich die Bedingung befindet"}, "DMAX": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Gibt den größten Wert aus den ausgewählten Datenbankeinträgen zurück", "ad": "ist der Zellbereich, aus dem die Datenbank besteht!gibt an, welches Datenbankfeld in der Funktion benutzt wird!ist der Zellbereich, der die Suchkriterien enthält. Der Bereich besteht aus der Spaltenbeschriftung und der Zelle darunter, in der sich die Bedingung befindet"}, "DMIN": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Gibt den kleinsten Wert aus den ausgewählten Datenbankeinträgen zurück", "ad": "ist der Zellbereich, aus dem die Datenbank besteht!gibt an, welches Datenbankfeld in der Funktion benutzt wird!ist der Zellbereich, der die Suchkriterien enthält. Der Bereich besteht aus der Spaltenbeschriftung und der Zelle darunter, in der sich die Bedingung befindet"}, "DPRODUCT": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Multipliziert die Werte eines bestimmten Felds der Datensätze, die innerhalb einer Datenbank mit den Suchkriterien übereinstimmen", "ad": "ist der Zellbereich, aus dem die Datenbank besteht!gibt an, welches Datenbankfeld in der Funktion benutzt wird!ist der Zellbereich, der die Suchkriterien enthält. Der Bereich besteht aus der Spaltenbeschriftung und der Zelle darunter, in der sich die Bedingung befindet"}, "DSTDEV": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Schätzt die Standardabweichung, ausge<PERSON> von einer Stichprobe aus bestimmten Datenbankeinträgen", "ad": "ist der Zellbereich, aus dem die Datenbank besteht!gibt an, welches Datenbankfeld in der Funktion benutzt wird!ist der Zellbereich, der die Suchkriterien enthält. Der Bereich besteht aus der Spaltenbeschriftung und der Zelle darunter, in der sich die Bedingung befindet"}, "DSTDEVP": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Berechnet die Standardabweichung, ausgehend von der Grundgesamtheit aus bestimmten Datenbankeinträgen", "ad": "ist der Zellbereich, aus dem die Datenbank besteht!gibt an, welches Datenbankfeld in der Funktion benutzt wird!ist der Zellbereich, der die Suchkriterien enthält. Der Bereich besteht aus der Spaltenbeschriftung und der Zelle darunter, in der sich die Bedingung befindet"}, "DSUM": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "<PERSON><PERSON><PERSON><PERSON>, die in einer Datenbank abgelegt sind", "ad": "ist der Zellbereich, aus dem die Datenbank besteht!gibt an, welches Datenbankfeld in der Funktion benutzt wird!ist der Zellbereich, der die Suchkriterien enthält. Der Bereich besteht aus der Spaltenbeschriftung und der Zelle darunter, in der sich die Bedingung befindet"}, "DVAR": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Schät<PERSON><PERSON> die Varianz, ausge<PERSON> von einer Stichprobe aus bestimmten Datenbankeinträgen", "ad": "ist der Zellbereich, aus dem die Datenbank besteht!gibt an, welches Datenbankfeld in der Funktion benutzt wird!ist der Zellbereich, der die Suchkriterien enthält. Der Bereich besteht aus der Spaltenbeschriftung und der Zelle darunter, in der sich die Bedingung befindet"}, "DVARP": {"a": "(Datenbank; Datenbankfeld; Suchkriterien)", "d": "Berech<PERSON> die Varianz, ausgehend von der Grundgesamtheit aus bestimmten Datenbankeinträgen", "ad": "ist der Zellbereich, aus dem die Datenbank besteht!gibt an, welches Datenbankfeld in der Funktion benutzt wird!ist der Zellbereich, der die Suchkriterien enthält. Der Bereich besteht aus der Spaltenbeschriftung und der Zelle darunter, in der sich die Bedingung befindet"}, "CHAR": {"a": "(<PERSON><PERSON>)", "d": "Gibt das der Codezahl entsprechende Zeichen zurück", "ad": "ist eine Zahl zwischen 1 und 255, die das gewünschte Zeichen entspricht"}, "CLEAN": {"a": "(Text)", "d": "Lö<PERSON>t alle nicht druckbaren Zeichen aus einem Text", "ad": "ist jede beliebige Arbeitsblattinformation, aus der Si<PERSON> die nicht druckbaren Zeichen entfernen möchten"}, "CODE": {"a": "(Text)", "d": "Gibt die Codezahl des ersten Zeichens in einem Text zurück (abhä<PERSON><PERSON> von Zeichensatz, der auf Ihrem Computer eingestellt ist)", "ad": "ist der Text, für den Sie die Codezahl des ersten Zeichens bestimmen möchten"}, "CONCATENATE": {"a": "(Text1; [Text2]; ...)", "d": "Verknüpft mehrere Zeichenfolgen zu einer Zeichenfolge", "ad": "sind 1 bis 255 Zeichenfolgen, die Sie zu einer Zeichenfolge verketten möchten und können Zeichenfolgen, Zahlen oder Bezüge einer Zelle sein"}, "CONCAT": {"a": "(Text1; ...)", "d": "Verkettet eine Liste oder einen Bereich aus Textzeichenfolgen.", "ad": "sind 1 bis 254 Textzeichenfolgen oder Bereiche, die zu einer einzigen Textzeichenfolge verknüpft werden sollen."}, "DOLLAR": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> eine Zahl in einen Text im Währungsformat um", "ad": "ist eine <PERSON>ahl, ein Bezug auf eine <PERSON>elle, die eine Zahl enthält, oder eine Formel, die zu einer Zahl auswertet!ist die Anzahl der Ziffern rechts vom Dezimalkomma. <PERSON><PERSON>, wird die Zahl gerundet. Wen<PERSON> der Parameter fehlt, werden 2 Dezimalstellen zurückgegeben"}, "EXACT": {"a": "(Text1; Text2)", "d": "<PERSON><PERSON><PERSON><PERSON>, ob zwei Zeichenfolgen identisch sind und gibt WAHR oder FALSCH zurück. IDENTISCH unterscheidet Groß- und Kleinschreibung", "ad": "ist die erste Textzeichenfolge!ist die zweite Textzeichenfolge"}, "FIND": {"a": "(Suchtext; Text; [<PERSON><PERSON><PERSON>_<PERSON>])", "d": "Sucht eine Zeichenfolge innerhalb einer anderen (Groß-/Kleinschreibung wird beachtet)", "ad": "ist der Text, den Sie finden wollen. Verwenden Sie zwei Anführungszeichen (leerer Text), um nach dem ersten Zeichen im Suchtext zu suchen. Stellvertreterzeichen sind nicht zulässig!ist der Text, in dem Suchtext gesucht werden soll!gibt an, bei welchem Zeichen die Suche begonnen werden soll. Wenn der Parameter fehlt, wird 1 angenommen"}, "FINDB": {"a": "(Suchtext;Text;[<PERSON><PERSON><PERSON>_<PERSON>])", "d": "Sucht eine Zeichenfolge (Suchtext) innerhalb einer anderen (Text) und gibt die Position der gesuchten Zeichenfolge ab dem ersten Zeichen der anderen Zeichenfolge an, für Sprachen die den Double-Byte Zeichensatz (DBCS) verwenden, wie japanisch, chinesisch, koreanisch etc.", "ad": "ist der Text, den Sie finden wollen. Verwenden Sie zwei Anführungszeichen (leerer Text), um nach dem ersten Zeichen im Suchtext zu suchen. Stellvertreterzeichen sind nicht zulässig!ist der Text, in dem Suchtext gesucht werden soll!gibt an, bei welchem Zeichen die Suche begonnen werden soll. Wenn der Parameter fehlt, wird 1 angenommen"}, "FIXED": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON><PERSON><PERSON>]; [<PERSON><PERSON>_<PERSON><PERSON>])", "d": "Formatiert eine Zahl als Text mit einer festen Anzahl an Nachkommastellen", "ad": "ist die Zahl, die Sie auf- oder abrunden und in Text umwandeln möchten!ist die Anzahl der Ziffern rechts vom Dezimalkomma. Wen<PERSON> der Parameter fehlt, werden 2 Dezimalstellen zurückgegeben!ist ein Wahrheitswert, der wenn WAHR, die Funktion FEST daran hindert, <PERSON><PERSON> (1.000er-Trennzeichen) bei der Wiedergabe des Textes zu benutzen"}, "LEFT": {"a": "(Text; [<PERSON><PERSON><PERSON>_<PERSON>])", "d": "Gibt das erste oder die ersten Zeichen einer Zeichenfolge zurück", "ad": "ist die Zeichenfolge mit den Zeichen, die Si<PERSON> kopieren wollen!gibt an, wie viele Zeichen LINKS zurückgeben soll, 1 wenn nicht angegeben"}, "LEFTB": {"a": "(Text;[<PERSON><PERSON><PERSON>_<PERSON><PERSON>])", "d": "Gibt auf der Grundlage der Anzahl von Bytes, die Si<PERSON> angeben, das oder die erste(n) Zeichen in einer Textzeichenfolge zurück, für Sprachen die den Double-Byte Zeichensatz (DBCS) verwenden, wie japanisch, chinesisch, koreanisch etc.", "ad": "ist die Zeichenfolge mit den Zeichen, die Si<PERSON> kopieren wollen!gibt an, wie viele Zeichen LINKSB zurückgeben soll, 1 wenn nicht angegeben"}, "LEN": {"a": "(Text)", "d": "Gibt die Anzahl der Zeichen einer Zeichenfolge zurück", "ad": "ist der Text, dessen Länge Sie bestimmen wollen"}, "LENB": {"a": "(Text)", "d": "Gibt die Anzahl von Bytes zurück, die zum Darstellen der Zeichen in einer Zeichenfolge verwendet werden, für Sprachen die den Double-Byte Zeichensatz (DBCS) verwenden, wie japanisch, chinesisch, koreanisch etc.", "ad": "ist der Text, dessen Länge Sie bestimmen wollen"}, "LOWER": {"a": "(Text)", "d": "Wandelt einen Text in Kleinbuchstaben um", "ad": "ist der Text, dessen Großbuchstaben in Kleinbuchstaben umgewandelt werden sollen"}, "MID": {"a": "(Text; <PERSON><PERSON><PERSON>_<PERSON>; <PERSON><PERSON><PERSON>_<PERSON>ei<PERSON>)", "d": "Gibt eine bestimmte Anzahl Zeichen einer Zeichenfolge ab der von Ihnen bestimmten Stelle zurück", "ad": "ist die Zeichenfolge mit den Zeichen, die Sie kopieren wollen!ist die Position des ersten Zeichens, das Sie aus Text kopieren möchten!gibt an, wie viele Zeichen aus Text zurückgegeben werden sollen"}, "MIDB": {"a": "(Text;<PERSON><PERSON><PERSON>_<PERSON>;<PERSON><PERSON><PERSON>_Byte)", "d": "Gibt auf der Grundlage der angegebenen Anzahl von Bytes eine bestimmte Anzahl von Zeichen einer Zeichenfolge ab der von Ihnen angegebenen Position zurück, für Sprachen die den Double-Byte Zeichensatz (DBCS) verwenden, wie japanisch, chinesisch, koreanisch etc.", "ad": "ist die Zeichenfolge mit den Zeichen, die Sie kopieren wollen!ist die Position des ersten Zeichens, das Sie aus Text kopieren möchten!gibt an, wie viele Zeichen aus Text zurückgegeben werden sollen"}, "NUMBERVALUE": {"a": "(Text; [Dezimaltrennzeichen]; [Gruppentrennzeichen])", "d": "Konvertiert auf vom Gebietsschema unabhängige Weise Text in Zahlen", "ad": "ist die Zeichenfolge, die die zu konvertierende Zahl darstellt!ist das als Dezimaltrennzeichen in der Zeichenfolge verwendete Zeichen!ist das als Gruppentrennzeichen in der Zeichenfolge verwendete Zeichen"}, "PROPER": {"a": "(Text)", "d": "Wandelt eine Textzeichenfolge in die geeignete Groß-/Kleinschreibung um: den ersten Buchstaben jedes Worts in Großbuchstaben, alle anderen Buchstaben in Kleinbuchstaben", "ad": "ist ein in Anführungszeichen eingeschlossener Text, eine Formel, die einen Text zurückgibt, oder ein Bezug auf eine Zelle, die den Text enthält, der teilweise großgeschrieben werden soll"}, "REPLACE": {"a": "(<PERSON><PERSON>_Text; <PERSON><PERSON><PERSON>_<PERSON>ei<PERSON>; <PERSON><PERSON><PERSON>_Zeichen; Neuer_Text)", "d": "Ersetzt eine bestimmte Anzahl Zeichen ab einer bestimmten Stelle innerhalb eines Textes", "ad": "ist der Text, in dem Sie eine Anzahl von Zeichen ersetzen möchten!ist die Position des Zeichens in Alter_Text, an der mit dem Ersetzen durch Neuer_Text begonnen werden soll!ist die Anzahl der Zeichen in Alter_Text, angefangen mit Beginn, die Sie durch Neuer_Text ersetzen wollen!ist der Text, mit dem Sie Anzahl_Zeichen in Alter_Text ersetzen wollen"}, "REPLACEB": {"a": "(<PERSON><PERSON>_Text;<PERSON><PERSON><PERSON>_<PERSON>;<PERSON><PERSON><PERSON>_Bytes;Neuer_Text)", "d": "Ersetzt eine Zeichenfolge, basierend auf der Anzahl der Zeichen und der angegebenen Startposition, durch eine neue Zeichengruppe, für Sprachen die den Double-Byte Zeichensatz (DBCS) verwenden, wie japanisch, chinesisch, koreanisch etc.", "ad": "ist der Text, in dem Sie eine Anzahl von Zeichen ersetzen möchten!ist die Position des Zeichens in Alter_Text, an der mit dem Ersetzen durch Neuer_Text begonnen werden soll!ist die Anzahl der Zeichen in Alter_Text, angefangen mit Beginn, die Sie durch Neuer_Text ersetzen wollen!ist der Text, mit dem Sie Anzahl_Bytes in Alter_Text ersetzen wollen"}, "REPT": {"a": "(Text; Multiplikator)", "d": "<PERSON><PERSON><PERSON><PERSON>t einen Text so oft wie angegeben", "ad": "ist der Text, den Si<PERSON> wiederholen wollen!ist eine positive Zahl, die die Anzahl der Wiederholungen von Text festlegt"}, "RIGHT": {"a": "(Text; [<PERSON><PERSON><PERSON>_<PERSON>])", "d": "Gibt das letzte oder die letzten Zeichen einer Zeichenfolge zurück", "ad": "ist die Zeichenfolge mit den Zeichen, die Sie kopieren wollen!gibt an, wie viele Zeichen kopiert werden sollen, 1 wenn nicht angegeben"}, "RIGHTB": {"a": "(Text;[<PERSON><PERSON><PERSON>_<PERSON><PERSON>])", "d": "Gibt auf der Grundlage der angegebenen Anzahl von Bytes das bzw. die letzten Zeichen einer Zeichenfolge zurück, für Sprachen die den Double-Byte Zeichensatz (DBCS) verwenden, wie japanisch, chinesisch, koreanisch etc.", "ad": "ist die Zeichenfolge mit den Zeichen, die Sie kopieren wollen!gibt an, wie viele Zeichen kopiert werden sollen, 1 wenn nicht angegeben"}, "SEARCH": {"a": "(Suchtext; Text; [<PERSON><PERSON><PERSON>_<PERSON>])", "d": "Sucht eine Zeichenfolge innerhalb einer anderen (Groß-/Kleinschreibung wird nicht beachtet)", "ad": "ist der Text, den Sie finden wollen. Sie können Stellvertreterzeichen verwenden (* und ?); verwenden Sie ~? Und ~*, um die Zeichen ? Und * selber zu suchen!ist der Text, in dem Sie nach Suchtext suchen wollen!ist die Nummer des Zeichens in Text (von links nach rechts), ab der Sie mit der Suche beginnen wollen"}, "SEARCHB": {"a": "(Suchtext;Text;[<PERSON><PERSON><PERSON>_<PERSON>])", "d": "Gibt die Position der angegebenen Teilzeichenfolge in einer Zeichenfolge zurück, für Sprachen die den Double-Byte Zeichensatz (DBCS) verwenden, wie japanisch, chinesisch, koreanisch etc.", "ad": "ist der Text, den Sie finden wollen. Sie können Stellvertreterzeichen verwenden (* und ?); verwenden Sie ~? Und ~*, um die Zeichen ? Und * selber zu suchen!ist der Text, in dem Sie nach Suchtext suchen wollen!ist die Nummer des Zeichens in Text (von links nach rechts), ab der Sie mit der Suche beginnen wollen"}, "SUBSTITUTE": {"a": "(Text; Alter_Text; Neuer_Text; [ntes_Auftreten])", "d": "Tauscht einen alten Text durch einen neuen Text in einer Zeichenfolge aus", "ad": "ist der in Anführungszeichen gesetzte Text oder der Bezug auf eine Zelle, die den Text enthält, in dem Zeichen ausgetauscht werden sollen!ist der Text, den Sie ersetzen wollen.  Wenn die Groß-/Kleinschreibung nicht übereinstimmt, ersetzt die Funktion den Text nicht!ist der Text, mit dem Sie Alter_Text ersetzen wollen!gibt an, wo Alter_Text durch Neuer_Text ersetzt werden soll"}, "T": {"a": "(Wert)", "d": "<PERSON><PERSON><PERSON><PERSON>, ob ein Wert ein Text ist, und gibt den Text zurück, falls dies zutrifft. Wenn der Wert kein Text ist, werden doppelte Anführungsstriche (leerer Text) zurückgegeben.", "ad": "ist der Wert, den Sie überprüfen wollen."}, "TEXT": {"a": "(Wert; Textformat)", "d": "Formatiert eine Zahl und wandelt sie in einen Text um", "ad": "ist ein numerischer Wert, eine <PERSON>el, die einen numerischen Wert berechnet, oder ein Bezug auf eine Zelle, die einen numerischen Wert enthält!ist ein Zahlenformat in Textform aus dem Kategoriefeld auf der Registerkarte Nummer im Dialogfeld Zellen formatieren"}, "TEXTJOIN": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>_<PERSON>; Text1; ...)", "d": "Verkettet eine Liste oder einen Bereich aus Textzeichenfolgen unter Verwendung eines Trennzeichens.", "ad": "Zwischen jedem Textelement einzufügende/s Zeichen oder Zeichenfolge.!Wenn \"WAHR\" (Standard), werden leere Zellen ignoriert.!sind 1 bis 252 Textzeichenfolgen oder Bereiche, die verknüpft werden sollen."}, "TRIM": {"a": "(Text)", "d": "<PERSON><PERSON>scht Leerzeichen in einem Text", "ad": "ist der Text, aus dem Sie Leerzeichen entfernen wollen"}, "UNICHAR": {"a": "(<PERSON><PERSON>)", "d": "Gibt das Unicode-Zeichen zurück, das durch den angegebenen Zahlenwert bezeichnet wird", "ad": "ist die Unicode-Zahl, die ein Zeichen darstellt"}, "UNICODE": {"a": "(Text)", "d": "<PERSON><PERSON><PERSON> die Zahl (Codepoint) zurück, die dem ersten Zeichen des Texts entspricht", "ad": "ist das Zeichen, dessen Unicode-Wert Sie bestimmen möchten"}, "UPPER": {"a": "(Text)", "d": "Wandelt einen Text in Großbuchstaben um", "ad": "ist der Text, der in Großbuchstaben umgewandelt werden soll"}, "VALUE": {"a": "(Text)", "d": "Wandelt ein als Text angegebenes Argument in eine Zahl um", "ad": "ist der in Anführungszeichen gesetzte Text oder der Bezug auf eine Zelle, die den Text enthält, der umgewandelt werden soll"}, "AVEDEV": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt die durchschnittliche absolute Abweichung von Datenpunkten von ihrem Mittelwert zurück. Die Argumente können Zahlen, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder Bezüge sein, die Zahlen enthalten", "ad": "sind 1 bis 255 Argumente, deren durchschnittliche absolute Abweichung Sie berechnen möchten"}, "AVERAGE": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "G<PERSON>t den Mittelwert (arithmetisches Mittel) der Argumente zurück, bei denen es sich um Zahlen oder Namen, Arrays oder Bezüge handeln kann, die Zahlen enthalten", "ad": "sind 1 bis 255 numerische Argumente, deren Mittelwert Sie berechnen möchten"}, "AVERAGEA": {"a": "(Wert1; [Wert2]; ...)", "d": "Gibt den Mittelwert der Argumente zurück. Dabei werden Text und FALSCH als 0 interpretiert, WAHR wird als 1 interpretiert. Argumente können <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder Bezüge sein.", "ad": "sind 1 bis 255 Argumente, für die der Mittelwert berechnet werden soll"}, "AVERAGEIF": {"a": "(Bereich; Kriterien; [<PERSON><PERSON><PERSON><PERSON><PERSON>_Bereich])", "d": "Sucht den Mittelwert (arithmetisches Mittel) für die von einer bestimmten Bedingung oder bestimmten Kriterien festgelegten Zellen", "ad": "ist der Zellbereich, den Si<PERSON> bewerten möchten!ist die Bedingung oder die Kriterien in Form einer Zahl, eines Ausdrucks oder Texts, der definiert, welche Zellen zum Ermitteln des Mittelwerts verwendet werden!sind die tatsächlichen Zellen, die zum Ermitteln des Mittelwerts verwendet werden sollen. Wenn dies nicht angegeben wird, werden die Zellen im Bereich verwendet"}, "AVERAGEIFS": {"a": "(Mittelwert_Bereich; Kriterien_Bereich; Kriterien; ...)", "d": "Sucht den Mittelwert (arithmetisches Mittel) für die Zellen, die durch bestimmte Bedingungen oder Kriterien angegeben sind", "ad": "sind die tatsächlichen Zellen, die zum Suchen des Mittelwerts verwendet werden sollen.!ist der Zellenbereich, der für eine bestimmte Bedingung ausgewertet werden soll!ist die Bedingung oder sind die Kriterien in Form einer Zahl, eines Ausdrucks oder Texts, der definiert, welche Zellen zum Suchen des Mittelwerts verwendet werden sollen"}, "BETADIST": {"a": "(x; Alpha; Beta; [A]; [B])", "d": "Gibt Werte der Verteilungsfunktion einer betaverteilten Zufallsvariablen zurück", "ad": "ist der Wert, an dem die Funktion über dem Intervall A bis B ausgewertet werden soll!ist ein Parameter der Verteilung und muss größer als 0 sein!ist ein Parameter der Verteilung und muss größer als 0 sein!ist eine optionale untere Begrenzung des Intervalls für x. Wenn der Parameter fehlt, dann ist A = 0!ist eine optionale obere Begrenzung des Intervalls für x. Wenn der Parameter fehlt, dann ist A = 1"}, "BETAINV": {"a": "(<PERSON><PERSON><PERSON>; Alpha; Beta; [A]; [B])", "d": "Gibt Perzentile der Betaverteilung zurück", "ad": "ist die zur Betaverteilung gehörige Wahrscheinlichkeit!ist ein Parameter der Verteilung und muss größer als 0 sein!ist ein Parameter der Verteilung und muss größer als 0 sein!ist eine optionale Untergrenze des Intervalls für x. Wenn der Parameter fehlt, dann ist A = 0!ist eine optionale Obergrenze des Intervalls für x. Wenn der Parameter fehlt, dann ist B = 1"}, "BETA.DIST": {"a": "(x; Alpha; Beta; kumuliert; [A]; [B])", "d": "Gibt Werte der Verteilungsfunktion einer betaverteilten Zufallsvariablen zurück", "ad": "ist der Wert, an dem die Funktion über dem Intervall A bis B ausgewertet werden soll!ist ein Parameter der Verteilung und muss größer als 0 sein!ist ein Parameter der Verteilung und muss größer als 0 sein!ist ein Wahrheitswert: für die kumulierte Verteilungsfunktion WAHR; für die Wahrscheinlichkeitsdichtefunktion FALSCH!ist eine optionale untere Begrenzung des Intervalls für x. Wenn der Parameter fehlt, dann ist A = 0!ist eine optionale obere Begrenzung des Intervalls für x. Wenn der Parameter fehlt, dann ist B = 1"}, "BETA.INV": {"a": "(<PERSON><PERSON><PERSON>; Alpha; Beta; [A]; [B])", "d": "Gibt Perzentile der Betaverteilung (BETA.VERT) zurück", "ad": "ist die zur Betaverteilung gehörige Wahrscheinlichkeit!ist ein Parameter der Verteilung und muss größer als 0 sein!ist ein Parameter der Verteilung und muss größer als 0 sein!ist eine optionale Untergrenze des Intervalls für x. Wenn der Parameter fehlt, dann ist A = 0!ist eine optionale Obergrenze des Intervalls für x. Wenn der Parameter fehlt, dann ist B = 1"}, "BINOMDIST": {"a": "(Zahl_Erfolge; Versuche; Erfolgswahrsch; Kumuliert)", "d": "G<PERSON>t Wahrscheinlichkeiten einer binomialverteilten Zufallsvariablen zurück.", "ad": "ist die Zahl der günstigen Ereignisse der Experimente!ist die Zahl der unabhängigen Zufallsexperimente!ist die Wahrscheinlichkeit für den günstigen Ausgang des Experiments!ist der Wahrheitswert, der den Typ der Funktion bestimmt"}, "BINOM.DIST": {"a": "(Zahl_Erfolge; Versuche; Erfolgswahrsch; Kumuliert)", "d": "G<PERSON>t Wahrscheinlichkeiten einer binomialverteilten Zufallsvariablen zurück.", "ad": "ist die Zahl der günstigen Ereignisse der Experimente!ist die Zahl der unabhängigen Zufallsexperimente!ist die Wahrscheinlichkeit für den günstigen Ausgang des Experiments!ist der Wahrheitswert, der den Typ der Funktion bestimmt"}, "BINOM.DIST.RANGE": {"a": "(Versuche; Erfolgswahrscheinlichkeit; Zahl_Erfolge; [Zahl2_Erfolge])", "d": "Gibt die Erfolgswahrscheinlichkeit eines Versuchsergebnisses als Binomialverteilung zurück", "ad": "ist die Anzahl der unabhängigen Versuche!ist die Erfolgswahrscheinlichkeit jedes Einzelversuchs!ist die Anzahl der Erfolge bei den Versuchen!sofern angegeben gibt diese Funktion die Wahrscheinlichkeit zurück, dass die Anzahl der erfolgreichen Versuche zwischen Zahl_Erfolge und Zahl2_Erfolge liegt"}, "BINOM.INV": {"a": "(V<PERSON><PERSON>; Erfolgswahrsch; Alpha)", "d": "Gibt den kleinsten Wert zurück, für den die kumulierten Wahrscheinlichkeiten der Binomialverteilung größer oder gleich einer Grenzwahrscheinlichkeit sind", "ad": "ist die Zahl der Bernoulliexperimente!ist die Wahrscheinlichkeit für den günstigen Ausgang des Experiments, eine <PERSON>ahl von einschließlich 0 bis einschließlich 1!ist die Grenzwahrscheinlichkeit, eine Zahl von einschließlich 0 bis einschließlich 1"}, "CHIDIST": {"a": "(x; Freiheits<PERSON>)", "d": "Gibt Werte der Verteilungsfunktion (1-Alpha) einer Chi-Quadrat-verteilten Zufallsgröße zurück.", "ad": "ist der Wert der Verteilung (Perzentil), ein nicht-negativer Wert, dessen Wahrscheinlichkeit Si<PERSON> berechnen möchten!ist die Anzahl der Freiheitsgrade, eine Zahl größer oder gleich 1 und kleiner als 10^10"}, "CHIINV": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade)", "d": "G<PERSON>t Perzentile der Chi-Quadrat-Verteilung zurück, eine Zahl von einschließlich 0 bis einschließlich 1", "ad": "ist die zur Chi-Quadrat-Verteilung gehörige Wahrscheinlichkeit!ist die Anzahl der Freiheitsgrade, eine Zahl größer oder gleich 1 und kleiner als 10^10"}, "CHITEST": {"a": "(<PERSON><PERSON>_<PERSON>; <PERSON><PERSON><PERSON>_<PERSON>)", "d": "Gibt die Teststatistik eines Chi-Quadrat-Unabhängigkeitstests zurück", "ad": "ist der Bereich beobachteter Daten, den Si<PERSON> gegen die erwarteten Werte testen möchten!ist der Bereich erwarteter Beobachtungen, die sich aus der Division der miteinander multiplizierten Rangsummen und der Gesamtsumme berechnen"}, "CHISQ.DIST": {"a": "(x; <PERSON><PERSON>heits<PERSON>; kumuli<PERSON>)", "d": "Gibt Werte der linksseitigen Verteilungsfunktion (1-Alpha) einer Chi-Quadrat-verteilten Zufallsgröße zurück.", "ad": "ist der Wert der Verteilung (Perzentil), ein nicht-negativer <PERSON>rt, dessen Wahrscheinlichkeit Si<PERSON> berechnen möchten!ist die Anzahl der Freiheitsgrade, eine Zahl größer oder gleich 1 und kleiner als 10^10!ist ein Wahrheitswert, den die Funktion zurückgeben soll: die kumulative Verteilungsfunktion = WAHR; die Wahrscheinlichkeitsmassenfunktion = FALSCH"}, "CHISQ.DIST.RT": {"a": "(x; Freiheits<PERSON>)", "d": "Gibt Werte der rechtsseitigen Verteilungsfunktion (1-Alpha) einer Chi-Quadrat-verteilten Zufallsgröße zurück.", "ad": "ist der Wert der Verteilung (Perzentil), ein nicht-negativer Wert, dessen Wahrscheinlichkeit Si<PERSON> berechnen möchten!ist die Anzahl der Freiheitsgrade, eine Zahl größer oder gleich 1 und kleiner als 10^10"}, "CHISQ.INV": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade)", "d": "Gibt Perzentile der linksseitigen Chi-Quadrat-Verteilung zurück, eine Zahl von einschließlich 0 bis einschließlich 1", "ad": "ist die zur Chi-Quadrat-Verteilung gehörige Wahrscheinlichkeit!ist die Anzahl der Freiheitsgrade, eine Zahl größer oder gleich 1 und kleiner als 10^10"}, "CHISQ.INV.RT": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade)", "d": "Gibt Perzentile der rechtsseitigen Chi-Quadrat-Verteilung zurück, eine Zahl von einschließlich 0 bis einschließlich 1", "ad": "ist die zur Chi-Quadrat-Verteilung gehörige Wahrscheinlichkeit!ist die Anzahl der Freiheitsgrade, eine Zahl größer oder gleich 1 und kleiner als 10^10"}, "CHISQ.TEST": {"a": "(<PERSON><PERSON>_<PERSON>; <PERSON><PERSON><PERSON>_<PERSON>)", "d": "Gibt die Teststatistik eines Chi-Quadrat-Unabhängigkeitstests zurück", "ad": "ist der Bereich beobachteter Daten, den Si<PERSON> gegen die erwarteten Werte testen möchten!ist der Bereich erwarteter Beobachtungen, die sich aus der Division der miteinander multiplizierten Rangsummen und der Gesamtsumme berechnen"}, "CONFIDENCE": {"a": "(Alpha; Standabwn; Umfang_S)", "d": "Ermöglicht die Berechnung des 1-Alpha Konfidenzintervalls für den Erwartungswert einer Zufallsvariablen und verwendet dazu die Normalverteilung", "ad": "ist die Irrtumswahrscheinlichkeit Alpha, zur Berechnung des 1- Alpha Konfidenzniveaus, eine <PERSON> größer als 0 und kleiner als 1!ist die als bekannt angenommene Standardabweichung der Grundgesamtheit, eine <PERSON>, die größer als 1 sein muss!ist die Größe der Stichprobe"}, "CONFIDENCE.NORM": {"a": "(Alpha; Standardabwn; Umfang)", "d": "Gibt das Konfidenzintervall für den Erwartungswert einer Zufallsvariablen zurück", "ad": "ist die Irrtumswahrscheinlichkeit Alpha zur Berechnung des 1-Alpha-Konfidenzniveaus, eine Zahl größer als 0 und kleiner als 1!ist die Standardabweichung der Grundgesamtheit für den Datenbereich, die als bekannt angenommen wird. Standardabwn muss größer als 0 sein!ist die Größe der Stichprobe"}, "CONFIDENCE.T": {"a": "(Alpha; Standardabwn; Umfang)", "d": "Gibt das Konfidenzintervall für den Erwartungswert einer (Student) t-verteilten Zufallsvariablen zurück", "ad": "ist das Signifikanzniveau, das zur Berechnung des Konfidenzniveaus verwendet wird, eine Zahl größer als 0 und kleiner als 1!ist die Standardabweichung der Grundgesamtheit für den Datenbereich, die als bekannt angenommen wird. Standardabwn muss größer als 0 sein!ist die Größe der Stichprobe"}, "CORREL": {"a": "(Matrix1; Matrix2)", "d": "Gibt den Korrelationskoeffizient zweier Reihen von Merkmalsausprägungen zurück.", "ad": "ist der mit Werten belegte Zellbereich, Arg<PERSON>nte können Zahlen, <PERSON><PERSON>, <PERSON><PERSON>ys oder Bezüge sein!ist ein zweiter mit Werten belegter Zellbereich, Argumente können Zahlen, <PERSON><PERSON>, <PERSON><PERSON>ys oder Bezüge sein"}, "COUNT": {"a": "(Wert1; [Wert2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, wie viele Zellen in einem Bereich Zahlen enthalten", "ad": "sind 1 bis 255 Argumente, die unterschiedliche Datentypen beinhalten können oder sich auf solche beziehen können, wobei nur Zahlen in die Berechnung eingehen"}, "COUNTA": {"a": "(Wert1; [Wert2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> die Anzahl nicht leerer Zellen in einem Bereich", "ad": "sind 1 bis 255 Argumente, die zu zählende Werte und Zellen darstellen. Werte können beliebige Informationen sein"}, "COUNTBLANK": {"a": "(<PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON>t die leeren Zellen in einem Zellbereich", "ad": "ist der Bereich, von dem <PERSON> anfangen wollen, die leeren Z<PERSON>n zu z<PERSON>hlen"}, "COUNTIF": {"a": "(Bereich; Suchkriterien)", "d": "<PERSON><PERSON><PERSON>t die nichtleeren Zellen eines Bereichs, deren Inhalte mit dem Suchkriterium übereinstimmen", "ad": "ist der Bereich, von dem <PERSON> anfangen wollen, die nichtleeren Zellen zu z<PERSON>hlen!ist das Suchkriterium als Zahl, Formel oder Text, das festlegt, welche Zellen gezählt werden"}, "COUNTIFS": {"a": "(Kriterienberei<PERSON>; Kriterien; ...)", "d": "<PERSON><PERSON><PERSON>t die Anzahl der Zellen, die durch eine bestimmte Menge von Bedingungen oder Kriterien festgelegt ist", "ad": "ist der Zellenbereich, der für eine bestimmte Bedingung ausgewertet werden soll!ist die Bedingung in Form einer Zahl, eines Ausdrucks oder Texts, der definiert, welche Zellen gezählt werden sollen"}, "COVAR": {"a": "(Matrix1; Matrix2)", "d": "Gibt die Kovarianz, den Mittelwert der für alle Datenpunktpaare gebildeten Produkte der Abweichungen zurück.", "ad": "ist der erste mit ganzen Zahlen belegte Zellbereich, <PERSON>rg<PERSON>nte können Zahlen, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder Bezüge sein!ist der zweite mit ganzen Zahlen belegte Zellbereich, <PERSON>rg<PERSON>nte können Zahlen, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder Bezüge sein"}, "COVARIANCE.P": {"a": "(Array1; Array2)", "d": "Gibt die Kovarianz einer Grundgesamtheit, den Mittelwert der für alle Datenpunktpaare gebildeten Produkte der Abweichungen zurück.", "ad": "ist der erste mit ganzen Zahlen belegte Zellbereich, <PERSON>rg<PERSON>nte können Zahlen, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder Bezüge sein!ist der zweite mit ganzen Zahlen belegte Zellbereich, <PERSON>rg<PERSON>nte können Zahlen, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder Bezüge sein"}, "COVARIANCE.S": {"a": "(Array1; Array2)", "d": "Gibt die Kovarianz einer Stichprobe, den Mittelwert der für alle Datenpunktpaare gebildeten Produkte der Abweichungen zurück.", "ad": "ist der erste mit ganzen Zahlen belegte Zellbereich, <PERSON>rg<PERSON>nte können Zahlen, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder Bezüge sein!ist der zweite mit ganzen Zahlen belegte Zellbereich, <PERSON>rg<PERSON>nte können Zahlen, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder Bezüge sein"}, "CRITBINOM": {"a": "(V<PERSON><PERSON>; Erfolgswahrsch; Alpha)", "d": "G<PERSON><PERSON> den kleinsten Wert, für den die kumulierten Wahrscheinlichkeiten der Binomialverteilung größer oder gleich einer Grenzwahrscheinlichkeit sind zurück", "ad": "ist die Zahl der Bernoulliexperimente!ist die Wahrscheinlichkeit für den günstigen Ausgang des Experiments, eine <PERSON>ahl von einschließlich 0 bis einschließlich 1!ist die Grenzwahrscheinlichkeit, eine Zahl von einschließlich 0 bis einschließlich 1"}, "DEVSQ": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt die Summe der quadrierten Abweichungen der Datenpunkte von ihrem Stichprobenmittelwert zurück.", "ad": "sind 1 bis 255 Argumente bzw. ein Array oder Arraybezug, deren quadratische Abweichungen Sie berechnen möchten"}, "EXPONDIST": {"a": "(x; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>)", "d": "G<PERSON>t Wahrscheinlichkeiten einer exponentialverteilten Zufallsvariablen zurück", "ad": "ist der Wert der Verteilung (Perzentil), dessen Wahrscheinlichkeit Sie berechnen möchten!ist der Parameter der Verteilung!ist der Wahrheitswert, der den Typ der Funktion bestimmt"}, "EXPON.DIST": {"a": "(x; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>)", "d": "G<PERSON>t Wahrscheinlichkeiten einer exponentialverteilten Zufallsvariablen zurück", "ad": "ist der Wert der Verteilung (Perzentil), dessen Wahrscheinlichkeit Sie berechnen möchten!ist der Parameter der Verteilung!ist der Wahrheitswert, der den Typ der Funktion bestimmt"}, "FDIST": {"a": "(x; Freiheitsgrade1; Freiheitsgrade2)", "d": "Gibt Werte der Verteilungsfunktion (1-Alpha) einer rechtsseitigen F-verteilten Zufallsvariablen zurück", "ad": "ist der Wert der Verteilung (Perzentil), des<PERSON> Wahrscheinlichkeit Sie berechnen möchten, eine nicht-negative <PERSON>ahl!ist die Anzahl der Freiheitsgrade im Zähler, eine Zahl größer oder gleich 1 und kleiner als 10^10!ist die Anzahl der Freiheitsgrade im Nenner, eine Zahl größer oder gleich 1 und kleiner als 10^10"}, "FINV": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade1; Freiheitsgrade2)", "d": "Gibt Perzentile der rechtsseitigen F-Verteilung zurück", "ad": "ist die zur F-Verteilung gehörige Wahrscheinlichkeit, eine Zahl von einschließlich 0 bis einschließlich 1!ist die Anzahl der Freiheitsgrade im Zähler, eine Zahl größer oder gleich 1 und kleiner als 10^10!ist die Anzahl der Freiheitsgrade im Nenner, eine Zahl größer oder gleich 1 und kleiner als 10^10"}, "FTEST": {"a": "(Matrix1; Matrix2)", "d": "Gibt die Teststatistik eines F-Tests zurück, die zweiseitige Wahrscheinlichkeit darstellt, dass sich die Varianzen in Matrix1 und Matrix2 nicht signifikant unterscheiden", "ad": "ist die erste Matrix oder der erste Wertebereich, Argumente können Zahlen, <PERSON><PERSON>, <PERSON><PERSON>ys oder Bezüge sein (Leerzellen werden ignoriert)!ist die zweite Matrix oder der zweite Wertebereich, Argumente können Zahlen, <PERSON><PERSON>, <PERSON><PERSON>ys oder Bezüge sein (Leerzellen werden ignoriert)"}, "F.DIST": {"a": "(x; Freiheitsgrade1; Freiheitsgrade2; kumuli<PERSON>)", "d": "Gibt Werte der Verteilungsfunktion (1-Alpha) einer (linksseitigen) F-verteilten Zufallsvariablen zurück", "ad": "ist der Wert der Verteilung (Perzentil), dessen Wahrscheinlichkeit Sie berechnen möchten, eine nicht-negative <PERSON>ahl!ist die Anzahl der Freiheitsgrade im Zähler, eine Zahl größer oder gleich 1 und kleiner als 10^10!ist die Anzahl der Freiheitsgrade im Nenner, eine Zahl größer oder gleich 1 und kleiner als 10^10!ist ein Wahrheitswert, den die Funktion zurückgeben soll: die kumulative Verteilungsfunktion = WAHR; die Wahrscheinlichkeitsmassenfunktion = FALSCH"}, "F.DIST.RT": {"a": "(x; Freiheitsgrade1; Freiheitsgrade2)", "d": "Gibt Werte der Verteilungsfunktion (1-Alpha) einer (rechtsseitigen) F-verteilten Zufallsvariablen zurück", "ad": "ist der Wert der Verteilung (Perzentil), des<PERSON> Wahrscheinlichkeit Sie berechnen möchten, eine nicht-negative <PERSON>ahl!ist die Anzahl der Freiheitsgrade im Zähler, eine Zahl größer oder gleich 1 und kleiner als 10^10!ist die Anzahl der Freiheitsgrade im Nenner, eine Zahl größer oder gleich 1 und kleiner als 10^10"}, "F.INV": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade1; Freiheitsgrade2)", "d": "Gibt Perzentile der (linksseitigen) F-Verteilung zurück: wenn p = F.VERT(x,...), dann FINV(p,...) = x", "ad": "ist eine der kumulierten F-Verteilung zugeordnete Wahrscheinlichkeit, eine Zahl zwischen 0 und 1 einsch<PERSON>ßlich!ist die Anzahl der Freiheitsgrade im Zähler, eine Zahl größer oder gleich 1 und kleiner als 10^10!ist die Anzahl der Freiheitsgrade im Nenner, eine Zahl größer als oder gleich 1 und kleiner als 10^10"}, "F.INV.RT": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade1; Freiheitsgrade2)", "d": "Gibt Perzentile der (rechtsseitigen) F-Verteilung zurück: wenn p = F.VERT.RE(x,...), dann F.INV.RE(p,...) = x", "ad": "ist eine der kumulierten F-Verteilung zugeordnete Wahrscheinlichkeit, eine Zahl zwischen 0 und 1 einsch<PERSON>ßlich!ist die Anzahl der Freiheitsgrade im Zähler, eine Zahl größer oder gleich 1 und kleiner als 10^10!ist die Anzahl der Freiheitsgrade im Nenner, eine Zahl größer als oder gleich 1 und kleiner als 10^10"}, "F.TEST": {"a": "(Matrix1; Matrix2)", "d": "Gibt die Teststatistik eines F-Tests zurück, die zweiseitige Wahrscheinlichkeit darstellt, dass sich die Varianzen in Matrix1 und Matrix2 nicht signifikant unterscheiden", "ad": "ist die erste Matrix oder der erste Wertebereich, Argumente können Zahlen, <PERSON><PERSON>, <PERSON><PERSON>ys oder Bezüge sein (Leerzellen werden ignoriert)!ist die zweite Matrix oder der zweite Wertebereich, Argumente können Zahlen, <PERSON><PERSON>, <PERSON><PERSON>ys oder Bezüge sein (Leerzellen werden ignoriert)"}, "FISHER": {"a": "(x)", "d": "Gibt die Fisher-Transformation zurück", "ad": "ist der numerische Wert, grösser als -1 und kleiner als 1, für den Sie die Transformation durchführen möchten"}, "FISHERINV": {"a": "(y)", "d": "Gibt die Umkehrung der Fisher-Transformation zurück", "ad": "ist der Wert, dessen Transformation Sie umkehren möchten"}, "FORECAST": {"a": "(x; <PERSON>_<PERSON><PERSON>; <PERSON>_<PERSON>rte)", "d": "Berechnet oder prognostiziert einen Schätzwert für einen linearen Trend unter Verwendung vorhandener Werte", "ad": "ist der Datenpunkt, dessen Wert Si<PERSON> vorhersagen möchten; dieser muss ein numerischer Wert sein!ist die abhängige Matrix oder der abhängige Datenbereich aus numerischen Daten!ist die unabhängige Matrix oder der unabhängige Datenbereich aus numerischen Daten. Die Varianz von X_Werte darf nicht null sein"}, "FORECAST.ETS": {"a": "(<PERSON><PERSON>_<PERSON>; Werte; Zeitachse; [Saisonalität]; [Daten_Vollständigkeit]; [Aggregation])", "d": "G<PERSON><PERSON> den Schätzwert für ein bestimmtes Zieldatum zurück, wobei eine exponentielle Glättungsmethode verwendet wird.", "ad": "ist der Datenpunkt, für den Spreadsheet Editor einen Wert vorhersagt. Er sollte das Muster der Werte auf der Zeitachse fortsetzen.!ist die Matrix oder der Bereich aus numerischen Daten, die vorhergesagt werden.!ist die unabhängige Matrix oder der unabhängige Bereich aus numerischen Daten. Die Daten auf der Zeitachse müssen konsistente Abstände haben und dürfen nicht null sein.!ist ein optionaler numerischer Wert, der die Länge des saisonalen Musters angibt. Der Standardwert 1 gibt an, dass die Saisonalität automatisch erkannt wird.!ist ein optionaler Wert für die Behandlung fehlender Werte. Beim Standardwert 1 werden fehlende Werte mittels Interpolation ersetzt, und bei 0 werden diese durch Nullen ersetzt.!ist ein optionaler numerischer Wert zum Aggregieren mehrerer Werte mit demselben Zeitstempel. <PERSON><PERSON> le<PERSON>, gibt Spreadsheet Editor den Mittelwert der Werte an."}, "FORECAST.ETS.CONFINT": {"a": "(<PERSON><PERSON>_<PERSON>; Werte; Zeitachse; [Konfidenz_Niveau]; [Saisonalität]; [Daten_Vollständigkeit]; [Aggregation])", "d": "Gibt ein Konfidenzintervall für den Schätzwert zum angegebenen Zieldatum zurück.", "ad": "ist der Datenpunkt, für den Spreadsheet Editor einen Wert vorhersagt. Er sollte das Muster der Werte auf der Zeitachse fortsetzen.!ist die Matrix oder der Bereich aus numerischen Daten, die vorhergesagt werden.!ist die unabhängige Matrix oder der unabhängige Bereich aus numerischen Daten. Die Daten auf der Zeitachse müssen konsistente Abstände haben und dürfen nicht null sein.!ist eine Zahl zwischen 0 und 1, die das Konfidenzniveau für das berechnete Konfidenzintervall anzeigt. Der Standardwert ist 0,95.!ist ein optionaler numerischer Wert, der die Länge des saisonalen Musters angibt. Der Standardwert 1 gibt an, dass die Saisonalität automatisch erkannt wird.!ist ein optionaler Wert für die Behandlung fehlender Werte. Beim Standardwert 1 werden fehlende Werte mittels Interpolation ersetzt, und bei 0 werden diese durch Nullen ersetzt.!ist ein optionaler numerischer Wert zum Aggregieren mehrerer Werte mit demselben Zeitstempel. <PERSON><PERSON>, gibt Spreadsheet Editor den Mittelwert der Werte an."}, "FORECAST.ETS.SEASONALITY": {"a": "(Werte; Zeitachse; [Daten_Vollständigkeit]; [Aggregation])", "d": "Gibt die Länge des Wiederholungsmusters zurück, das von eine Anwendung für die angegebene Zeitserie erkannt wird.", "ad": "ist die Matrix oder der Bereich aus numerischen Daten, die vorhergesagt werden.!ist die unabhängige Matrix oder der unabhängige Bereich numerischer Daten. Die Daten auf der Zeitachse müssen konsistente Abstände haben und dürfen nicht null sein.!ist ein optionaler Wert für die Behandlung fehlender Werte. Beim Standardwert 1 werden fehlende Werte mittels Interpolation ersetzt, und bei 0 werden diese durch Nullen ersetzt.!ist ein optionaler numerischer Wert für die Aggregation mehrerer Werte mit identischem Zeitstempel. Wenn er nicht angegeben wird, mittelt Spreadsheet Editor die Werte."}, "FORECAST.ETS.STAT": {"a": "(Werte; Zeitachse; Statistik_Typ; [Saisonalität]; [Daten_Vollständigkeit]; [Aggregation])", "d": "Gibt die angeforderte Statistik für die Vorhersage zurück.", "ad": "ist das Array oder der Bereich numerischer Daten, die Sie prognostizieren!.ist das unabhängige Array oder der Bereich numerischer Daten. Die Datumsangaben in der Zeitachse müssen einen konsistenten Schritt zwischen ihnen aufweisen und dürfen nicht null sein.!ist eine Zahl zwischen 1 und 8, die angibt, welche Statistik Spreadsheet Editor für die berechnete Prognose zurückgegeben wird.!ist ein optionaler numerischer Wert, der die Länge des saisonalen Musters angibt. Der Standardwert 1 gibt an, dass die Saisonalität automatisch erkannt wird.!ist ein optionaler Wert für die Behandlung fehlender Werte. Der Standardwert 1 ersetzt fehlende Werte durch Interpolation, und 0 ersetzt sie durch Nullen.!ist ein optionaler numerischer Wert zum Aggregieren mehrerer Werte mit demselben Zeitstempel. <PERSON><PERSON> le<PERSON>, gibt Spreadsheet Editor den Mittelwert der Werte an."}, "FORECAST.LINEAR": {"a": "(x; <PERSON>_<PERSON><PERSON>; <PERSON>_<PERSON>rte)", "d": "Berechnet oder prognostiziert einen Schätzwert für einen linearen Trend unter Verwendung vorhandener Werte", "ad": "ist der Datenpunkt, dessen Wert Si<PERSON> vorhersagen möchten; dieser muss ein numerischer Wert sein!ist die abhängige Matrix oder der abhängige Datenbereich aus numerischen Daten!ist die unabhängige Matrix oder der unabhängige Datenbereich aus numerischen Daten. Die Varianz von X_Werte darf nicht null sein"}, "FREQUENCY": {"a": "(Daten; Klassen)", "d": "Gibt eine Häufigkeitsverteilung als einspaltige Matrix zurück", "ad": "entspricht einer Matrix von oder einem Bezug auf eine Wertemenge, deren Häufigkeiten Si<PERSON> zählen möchten!sind die als Matrix oder Bezug auf einen Zellbereich eingegebenen Intervallgrenzen, nach denen Sie die in Daten befindlichen Werte einordnen möchten"}, "GAMMA": {"a": "(x)", "d": "Gibt den Wert der Gammafunktion zurück", "ad": "ist der Wert, für den Gamma berechnet werden soll"}, "GAMMADIST": {"a": "(x; Alpha; Beta; <PERSON><PERSON><PERSON><PERSON>)", "d": "G<PERSON>t Wahrscheinlichkeiten einer gammaverteilten Zufallsvariablen zurück", "ad": "ist der <PERSON>rt (Perzentil), dessen Wahrscheinlichkeit (1-Alpha) Sie berechnen wollen!ist ein Parameter der Verteilung!ist ein Parameter der Verteilung. Wenn Beta = 1, liefert GAMMAVERT die Standard-Gammaverteilung!ist der Wahrheitswert, der den Typ der Funktion bestimmt"}, "GAMMA.DIST": {"a": "(x; Alpha; Beta; <PERSON><PERSON><PERSON><PERSON>)", "d": "G<PERSON>t Wahrscheinlichkeiten einer gammaverteilten Zufallsvariablen zurück", "ad": "ist der Wert, dessen Wahrscheinlichkeit berechnet werden soll, eine nicht negative Zahl!ist ein Parameter der Verteilung, eine positive Zahl!ist ein Parameter der Verteilung, eine positive Zahl. Wenn Beta = 1, liefert GAMMA.VERT die Standard-Gammaverteilung!ist ein Wahrheitswert: kumulierte Verteilungsfunktion zurückgeben = WAHR; Wahrscheinlichkeits-Mengenfunktion = FALSCH oder auslassen"}, "GAMMAINV": {"a": "(<PERSON><PERSON><PERSON>; Alpha; Beta)", "d": "Gibt Perzentile der Gammaverteilung zurück", "ad": "ist die zur Gamma-Verteilung gehörige Wahrscheinlichkeit, eine <PERSON>ahl von einschließlich 0 bis einschließlich 1!ist ein Parameter der Verteilung, eine positive Zahl!ist ein Parameter der Verteilung, eine positive Zahl. Wenn Beta = 1, liefert GAMMAINV die Standard-Gammaverteilung"}, "GAMMA.INV": {"a": "(<PERSON><PERSON><PERSON>; Alpha; Beta)", "d": "Gibt den Kehrwert der kumulierten Gammaverteilung zurück: wenn p = GAMMA.VERT(x,...), dann GAMMA.INV(p,...) = x", "ad": "ist die der Gammaverteilung zugeordnete Wahrscheinlichkeit, eine Zahl zwischen 0 und 1, einsch<PERSON>ß<PERSON>!ist ein Parameter für die Verteilung, eine positive Zahl!ist ein Parameter für die Verteilung, eine positive Zahl. Wenn Beta = 1, gibt GAMMA.INV den Kehrwert der Gammanormalverteilung zurück"}, "GAMMALN": {"a": "(x)", "d": "Gibt den natürlichen Logarithmus der Gammafunktion zurück", "ad": "ist der Wert, für den Sie GAMMALN berechnen möchten"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Gibt den natürlichen Logarithmus der Gammafunktion zurück", "ad": "ist der Wert, für den Sie GAMMALN.GENAU berechnen möchten"}, "GAUSS": {"a": "(x)", "d": "Gibt 0,5 weniger als die kumulierte Normalverteilung zurück", "ad": "ist der Wert, für den Sie die Verteilung ermitteln möchten"}, "GEOMEAN": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt das geometrische Mittel eines Arrays oder Bereichs von positiven numerischen Daten zurück.", "ad": "sind 1 bis 255 Zahlen oder Namen, <PERSON><PERSON><PERSON> oder Bezüge, die Zahlen enthalten, deren Mittel Sie berechnen möchten"}, "GROWTH": {"a": "(<PERSON>_<PERSON><PERSON>; [<PERSON>_<PERSON><PERSON>]; [<PERSON><PERSON><PERSON>_x_Werte]; [<PERSON><PERSON><PERSON>])", "d": "G<PERSON>t Werte aus einem exponentiellen Wachstumstrend zurück, die bekannten Datenpunkten entsprechen", "ad": "sind die Y-Werte, die Ihnen bereits aus der Beziehung y = b*m^x bekannt sind, einer Matrix oder einem Bereich aus positiven Zahlen!sind optionale X-Werte, die Ihnen eventuell bereits aus der Beziehung y = b*m^x bekannt sind, einer Matrix oder einem Bereich derselben Größe wie Y_Werte!sind neue x-Werte, für die die Funktion VARIATION die entsprechenden Y-Werte zurückgeben soll!ist ein Wahrheitswert, der angibt: die Konstante b wird normal berechnet, wenn Konstante = WAHR, und b erhält den Wert 1, wenn Konstante = FALSCH oder nicht angegeben ist"}, "HARMEAN": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt das harmonische Mittel eines Datensatzes mit positiven Zahlen zurück: den Umkehrwert des arithmetischen Mittels der Umkehrwerte", "ad": "sind 1 bis 255 <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, die Zahlen enthalten, deren harmonisches Mittel Si<PERSON> berechnen möchten"}, "HYPGEOM.DIST": {"a": "(Erfolge_S; Umfang_S; Erfolge_G; Umfang_G; kumuliert)", "d": "<PERSON><PERSON>t Wahrscheinlichkeiten einer hypergeometrisch-verteilten Zufallsvariablen zurück.", "ad": "ist die Anzahl der in der Stichprobe erzielten Erfolge!ist der Umfang (Größe) der Stichprobe!ist die Anzahl der in der Grundgesamtheit möglichen Erfolge!ist der Umfang (Größe) der Grundgesamtheit!ist ein Wahrheitswert: für die kumulierte Verteilungsfunktion WAHR; für die Wahrscheinlichkeitsdichtefunktion FALSCH"}, "HYPGEOMDIST": {"a": "(Erfolge_S; Umfang_S; Erfolge_G; Umfang_G)", "d": "<PERSON><PERSON>t Wahrscheinlichkeiten einer hypergeometrisch-verteilten Zufallsvariablen zurück.", "ad": "ist die Anzahl der in der Stichprobe erzielten Erfolge!ist der Umfang (Größe) der Stichprobe!ist die Anzahl der in der Grundgesamtheit möglichen Erfolge!ist der Umfang (Größe) der Grundgesamtheit"}, "INTERCEPT": {"a": "(<PERSON>_<PERSON><PERSON>; <PERSON>_<PERSON><PERSON>)", "d": "Berechnet den Schnittpunkt einer Geraden mit der Y-Achse unter Verwendung einer Ausgleichsregressionsgeraden, die durch die X- und Y-Werte gezeichnet wird", "ad": "ist die abhängige Datengruppe oder Reihe von <PERSON>gen, <PERSON><PERSON><PERSON><PERSON> können Zahlen, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder <PERSON>ü<PERSON>, die Zahlen enthalten, sein!ist die unabhängige Datengruppe oder Reihe von Be<PERSON>achtungen, <PERSON>rg<PERSON><PERSON> können <PERSON>ahl<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder Bezüge, die Zahlen enthalten, sein"}, "KURT": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt die Kurtosis (Exzess) einer Datengruppe zurück", "ad": "sind 1 bis 255 <PERSON>, der<PERSON> (Exzess) Sie berechnen möchten"}, "LARGE": {"a": "(Matrix; k)", "d": "Gibt den k-größten Wert einer Datengruppe zurück", "ad": "ist die Matrix oder der Datenbereich, deren k-größten Wert Si<PERSON> bestimmen möchten!ist der Rang des Elementes einer Matrix oder eines Zellbereiches, dessen Wert zurückgegeben werden soll"}, "LINEST": {"a": "(Y_<PERSON>rte; [<PERSON>_<PERSON><PERSON>]; [<PERSON>nst<PERSON>]; [Stats])", "d": "Gibt statistische Werte zurück, die einen linearen Trend beschreiben, der bekannten Datenpunkten entspricht, indem eine Gerade mithilfe der Methode der kleinesten Quadrate ausgeglichen wird", "ad": "sind die Y-Werte, die Ihnen bereits aus der Beziehung y = mx + b bekannt sind!sind optionale X-Werte, die Ihnen eventuell bereits aus der Beziehung y = mx + b bekannt sind!ist ein Wahrheitswert, der angibt: die Konstante b wird normal berechnet, wenn Konstante = WAHR oder nicht angegeben ist, und b erhält den Wert 0, wenn Konstante = FALSCH ist!ist ein Wahrheitswert, der angibt: wenn WAHR, werden weitere Regressionskenngrößen zurückgegeben, wenn FALSCH oder nicht angegeben, werden der m-Koeffizient und die Konstante b zurückgegeben"}, "LOGEST": {"a": "(Y_<PERSON>rte; [<PERSON>_<PERSON><PERSON>]; [<PERSON>nst<PERSON>]; [Stats])", "d": "Gibt statistische Werte zurück, die eine exponentielle Kurve beschreiben, die bekannten Datenpunkten entspricht", "ad": "sind die Y-Werte, die Ihnen bereits aus der Beziehung y = b*m^x bekannt sind!sind optionale X-Werte, die Ihnen eventuell bereits aus der Beziehung y = b*m^x bekannt sind!ist ein Wahrheitswert, der angibt: die Konstante b wird normal berechnet, wenn Konstante = WAHR oder nicht angegeben ist, und b erhält den Wert 1, wenn Konstante = FALSCH!ist ein Wahrheitswert, der angibt: wenn WAHR, werden weitere Regressionskenngrößen zurückgegeben, wenn FALSCH oder nicht angegeben, werden der m-Koeffizient und die Konstante b zurückgegeben"}, "LOGINV": {"a": "(<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Standab<PERSON>)", "d": "Gibt Perzentile der Lognormalverteilung zurück", "ad": "ist die zur Lognormalverteilung gehörige Wahrscheinlichkeit, eine Zahl von einschließlich 0 bis einschließlich 1!ist das Mittel der Lognormalverteilung!ist die Standardabweichung der Lognormalverteilung, eine positive Zahl"}, "LOGNORM.DIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; kumuli<PERSON>)", "d": "Gibt Werte der Verteilungsfunktion einer lognormalverteilten Zufallsvariablen zurück, wobei ln(x) mit den Parametern Mittelwert und Standabwn normalverteilt ist.", "ad": "ist der Wert der Verteilung (Perzentil), dessen Wahrscheinlichkeit Sie berechnen möchten, eine positive Zahl!ist der Mittelwert der Lognormalverteilung!ist die Standardabweichung der Lognormalverteilung, eine positive Zahl!ist ein Wahrheitswert: für die kumulierte Verteilungsfunktion WAHR; für die Wahrscheinlichkeitsdichtefunktion FALSCH"}, "LOGNORM.INV": {"a": "(<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Standab<PERSON>)", "d": "Gibt Perzentile der Lognormalverteilung zurück", "ad": "ist die zur Lognormalverteilung gehörige Wahrscheinlichkeit, eine Zahl von einschließlich 0 bis einschließlich 1!ist das Mittel der Lognormalverteilung!ist die Standardabweichung der Lognormalverteilung, eine positive Zahl"}, "LOGNORMDIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON>; Standabwn)", "d": "Gibt Werte der Verteilungsfunktion einer lognormalverteilten Zufallsvariablen zurück.", "ad": "ist der Wert der Verteilung (Perzentil), dessen Wahrscheinlichkeit Sie berechnen möchten, eine positive Zahl!ist der Mittelwert der Lognormalverteilung!ist die Standardabweichung der Lognormalverteilung, eine positive Zahl"}, "MAX": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt den größten Wert innerhalb einer Wertemenge zurück. Logische Werte und Textwerte werden ignoriert", "ad": "sind 1 bis 255 <PERSON>, <PERSON><PERSON>, logische Werte oder Textzahlen, deren größte Zahl Sie berechnen möchten"}, "MAXA": {"a": "(Wert1; [Wert2]; ...)", "d": "Gibt den größten Wert innerhalb einer Wertemenge zurück. Logische Werte und Text werden nicht ignoriert", "ad": "sind 1 bis 255 <PERSON>ahlen, <PERSON><PERSON> Z<PERSON>n, logische Werte oder Textwerte, in denen der größte Wert gefunden werden soll"}, "MAXIFS": {"a": "(<PERSON><PERSON>; <PERSON><PERSON><PERSON>_<PERSON>; Kriterien; ...)", "d": "Gibt den in Zellen vorhandenen Maximalwert zurück, der durch einen angegebenen Satz von Bedingungen oder Kriterien festgelegt ist.", "ad": "die Zellen, in denen der Höchstwert bestimmt werden soll!ist der Zellbereich, den Sie für die bestimmte Bedingung auswerten möchten!ist die Bedingung oder die Kriterien in Form einer Zahl, eines Ausdrucks oder Texts, der definiert, welche Zellen beim Bestimmen des Maximalwerts einbezogen werden sollen"}, "MEDIAN": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt den Median bzw. die Zahl in der Mitte der Menge von angegebenen Zahlen zurück", "ad": "sind 1 bis 255 Zahlen oder Namen, <PERSON><PERSON><PERSON> oder Bezüge, die Zahlen enthalten, deren Median Sie berechnen möchten"}, "MIN": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt den kleinsten Wert innerhalb einer Wertemenge zurück. Logische Werte und Text werden ignoriert", "ad": "sind 1 bis 255 <PERSON>, <PERSON><PERSON>, logische Werte oder Textzahlen, deren kleinste Zahl Sie berechnen möchten"}, "MINA": {"a": "(Wert1; [Wert2]; ...)", "d": "Gibt den kleinsten Wert innerhalb einer Wertemenge zurück. Logische Werte und Text werden nicht ignoriert", "ad": "sind 1 bis 255 Zahlen, <PERSON><PERSON> Zellen, logische Werte und Textwerte, in denen der kleinste Wert gefunden werden soll"}, "MINIFS": {"a": "(<PERSON><PERSON>; <PERSON><PERSON><PERSON>_<PERSON>; Kriterien; ...)", "d": "Gibt den in Zellen vorhandenen Minimalwert zurück, der durch einen angegebenen Satz von Bedingungen oder Kriterien festgelegt ist.", "ad": "die Zellen, in denen der Mindestwert bestimmt werden soll!ist der Zellbereich, den Sie für die bestimmte Bedingung auswerten möchten!ist die Bedingung oder Kriterien in Form einer Zahl, eines Ausdrucks oder Texts, der definiert, welche Zellen beim Bestimmen des Minimalwerts einbezogen werden sollen"}, "MODE": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt den häufigsten Wert in einem Array oder einer Datengruppe zurück", "ad": "sind 1 bis 255 Zahlen oder Namen, <PERSON><PERSON><PERSON> oder Bezüge, die Zahlen enthalten, deren <PERSON>wert (Modus) Sie berechnen möchten"}, "MODE.MULT": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt ein vertikales Array der am häufigsten vorkommenden oder wiederholten Werte in einem Array oder einem Datenbereich zurück. Verwenden Sie für ein horizontales Array =MTRANS(MODUS.VIELF(Zahl1,Zahl2,...))", "ad": "sind 1 bis 255 Zahlen oder Namen, <PERSON><PERSON><PERSON> oder Bezüge, die Zahlen enthalten, für die der Modus angewendet werden soll"}, "MODE.SNGL": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt den häufigsten Wert in einem Array oder einer Datengruppe zurück", "ad": "sind 1 bis 255 Zahlen oder Namen, <PERSON><PERSON><PERSON> oder Bezüge, die Zahlen enthalten, deren <PERSON>wert (Modus) Sie berechnen möchten"}, "NEGBINOM.DIST": {"a": "(Zahl_Mißerfolge; Zahl_Erfolge; Erfolgswahrsch; kumuliert)", "d": "<PERSON><PERSON><PERSON> Wahrscheinlichkeiten einer negativen, binomial verteilten Zufallsvariablen zurück", "ad": "ist die Zahl der ungünstigen Ereignisse.!ist die Zahl der günstigen Ereignisse!ist die Wahrscheinlichkeit für den günstigen Ausgang des Experiments, eine Zahl zwischen 0 und 1!ist ein Wahrheitswert: für die kumulierte Verteilungsfunktion WAHR; für die Wahrscheinlichkeitsmengenfunktion FALSCH"}, "NEGBINOMDIST": {"a": "(Zahl_Mißerfolge; Zahl_Erfolge; Erfolgswahrsch)", "d": "<PERSON><PERSON><PERSON> Wahrscheinlichkeiten einer negativen, binomial verteilten Zufallsvariablen zurück", "ad": "ist die Zahl der ungünstigen Ereignisse.!ist die Zahl der günstigen Ereignisse!ist die Wahrscheinlichkeit für den günstigen Ausgang des Experiments, eine Zahl zwischen 0 und 1"}, "NORM.DIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON>; Standab<PERSON>; <PERSON><PERSON><PERSON>t)", "d": "G<PERSON>t Wahrscheinlichkeiten einer normal verteilten Zufallsvariablen zurück.", "ad": "ist der Wert der Verteilung (Perzentil), dessen Wahrscheinlichkeit Sie berechnen möchten!ist das arithmetische Mittel der Verteilung, eine positive Zahl!ist die Standardabweichung der Verteilung!ist ein Wahrheitswert: für die kumulierte Verteilungsfunktion WAHR; für die Wahrscheinlichkeitsdichtefunktion FALSCH"}, "NORMDIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON>; Standab<PERSON>; <PERSON><PERSON><PERSON>t)", "d": "G<PERSON>t Wahrscheinlichkeiten einer normal verteilten Zufallsvariablen zurück.", "ad": "ist der Wert der Verteilung (Perzentil), dessen Wahrscheinlichkeit Sie berechnen möchten!ist das arithmetische Mittel der Verteilung, eine positive Zahl!ist die Standardabweichung der Verteilung!ist der Wahrheitswert, der den Typ der Funktion bestimmt"}, "NORM.INV": {"a": "(<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Standab<PERSON>)", "d": "Gibt Perzentile der Normalverteilung zurück", "ad": "ist die zur Normalverteilung gehörige Wahrscheinlichkeit, eine <PERSON>ahl von einschließlich 0 bis einsch<PERSON>ßlich 1!ist das arithmetische Mittel der Verteilung!ist die Standardabweichung der Verteilung, eine <PERSON>ahl"}, "NORMINV": {"a": "(<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; Standab<PERSON>)", "d": "Gibt Perzentile der Normalverteilung zurück", "ad": "ist die zur Normalverteilung gehörige Wahrscheinlichkeit, eine <PERSON>ahl von einschließlich 0 bis einsch<PERSON>ßlich 1!ist das arithmetische Mittel der Verteilung!ist die Standardabweichung der Verteilung, eine <PERSON>ahl"}, "NORM.S.DIST": {"a": "(z; kumuliert)", "d": "Gibt Werte der Verteilungsfunktion standardmäßigen, normal verteilten Zufallsvariablen zurück.", "ad": "ist der Wert der Verteilung (Perzentil), dessen Wahrscheinlichkeit Sie berechnen möchten!ist ein Wahrheitswert, den die Funktion zurückgeben soll: die kumulative Verteilungsfunktion = WAHR; die Wahrscheinlichkeitsmassenfunktion = FALSCH"}, "NORMSDIST": {"a": "(z)", "d": "Gibt Werte der Verteilungsfunktion standardmäßigen, normal verteilten Zufallsvariablen zurück.", "ad": "ist der Wert der Verteilung (Perzentil), dessen Wahrscheinlichkeit Sie berechnen möchten"}, "NORM.S.INV": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Gibt Perzentile der Standardnormalverteilung zurück", "ad": "ist die zur Standardnormalverteilung gehörige Wahrscheinlichkeit, eine Zahl von einschließlich 0 bis einschließlich 1"}, "NORMSINV": {"a": "(<PERSON><PERSON><PERSON>)", "d": "Gibt Perzentile der Standardnormalverteilung zurück", "ad": "ist die zur Standardnormalverteilung gehörige Wahrscheinlichkeit, eine Zahl von einschließlich 0 bis einschließlich 1"}, "PEARSON": {"a": "(Matrix1; Matrix2)", "d": "Gibt den Pearsonschen Korrelationskoeffizienten zurück", "ad": "ist eine Reihe von unabhängigen Werten!ist eine Reihe von abhängigen Werten"}, "PERCENTILE": {"a": "(Matrix; Alpha)", "d": "Gibt das Alphaquantil einer Gruppe von Daten zurück", "ad": "ist eine Matrix oder ein Datenbereich, die/der die relative Lage der Daten beschreibt!ist der Prozentwert im geschlossenen Intervall von 0 bis 1"}, "PERCENTILE.EXC": {"a": "(Array; k)", "d": "Gibt das k-Quantil von Werten in einem Bereich zurück, wobei k im Bereich von 0..1 ausschließlich liegt", "ad": "ist ein Array oder ein Datenbereich, die/der die relative Lage der Daten beschreibt!ist der Prozentwert im geschlossenen Intervall von 0 bis 1"}, "PERCENTILE.INC": {"a": "(Array; k)", "d": "Gibt das k-Quantil von Werten in einem Bereich zurück, wobei k im Bereich von 0..1 einsch<PERSON>ß<PERSON> liegt", "ad": "ist ein Array oder ein Datenbereich, die/der die relative Lage der Daten beschreibt!ist der Prozentwert im geschlossenen Intervall von 0 bis 1"}, "PERCENTRANK": {"a": "(Matrix; x; [Genauigke<PERSON>])", "d": "Gibt den prozentualen Rang (Alpha) eines Wertes zurück", "ad": "ist die Matrix oder der Bereich numerischer Daten, die/der die relative Lage der Daten beschreibt!ist der Wert, dessen Rang Si<PERSON> bestimmen möchten!ist ein optionaler Wert, der die Anzahl der Nachkommastellen des zurückgegebenen Perzentilrangs festlegt, drei Stellen bei fehlender Angabe (0,xxx%)"}, "PERCENTRANK.EXC": {"a": "(<PERSON><PERSON>y; x; [<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Gibt den prozentualen Rang (Alpha) eines Werts in einem Dataset als Prozentsatz des Datasets (0..1 ausschließlich) zurück", "ad": "ist das Array oder der Bereich numerischer Daten, das/der die relative Lage der Daten beschreibt!ist der Wert, dessen Rang Si<PERSON> bestimmen möchten!ist ein optionaler Wert, der die Anzahl der Nachkommastellen des zurückgegebenen Prozentsatz festlegt, d<PERSON><PERSON>, wenn ausgelassen (0,xxx%)"}, "PERCENTRANK.INC": {"a": "(<PERSON><PERSON>y; x; [<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Gibt den prozentualen Rang (Alpha) eines Wertes in einem Dataset als Prozentsatz des Datasets (0..1 einschließlich) zurück", "ad": "ist das Array oder der Bereich von Daten mit numerischen Werten, die die relative Position definieren!ist der Wert, für den Si<PERSON> den Rang kennen möchten!ist ein optionaler Wert, der die Anzahl signifikanter Ziffern für den zurückgegebenen Prozentsatz angibt, d<PERSON><PERSON>, wenn ausgelassen (0.xxx%)"}, "PERMUT": {"a": "(n; k)", "d": "Gibt die Anzahl der Möglichkeiten zurück, um k Elemente aus einer Menge von n Elementen ohne Zurücklegen zu ziehen.", "ad": "ist die Anzahl aller Elemente!gibt an, aus wie vielen Elementen jede Variationsmöglichkeit bestehen soll"}, "PERMUTATIONA": {"a": "(Zahl; gewählte_Zahl)", "d": "Gibt die Anzahl der Permutationen für eine angegebene Anzahl von Objekten zurück (mit Wiederholungen), die aus der Gesamtmenge der Objekte ausgewählt werden können", "ad": "ist die Gesamtzahl der Objekte!ist die Anzahl der Objekte in jeder Permutation"}, "PHI": {"a": "(x)", "d": "Gibt den Wert der Dichtefunktion für eine Standardnormalverteilung zurück", "ad": "ist die Zahl, für die Sie die Dichte der Standardnormalverteilung ermitteln möchten"}, "POISSON": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>)", "d": "Gibt Wahrscheinlichkeiten einer Poisson-verteilten Zufallsvariablen zurück. (Die verwendete Gleichung ist in der Hilfe genauer beschrieben.)", "ad": "ist die Zahl der Fälle!ist der erwartete Zahlenwert!ist der Wahrheitswert, der den Typ der Funktion bestimmt"}, "POISSON.DIST": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>)", "d": "Gibt Wahrscheinlichkeiten einer Poisson-verteilten Zufallsvariablen zurück. (Die verwendete Gleichung ist in der Hilfe genauer beschrieben.)", "ad": "ist die Zahl der Fälle!ist der erwartete Zahlenwert!ist der Wahrheitswert, der den Typ der Funktion bestimmt"}, "PROB": {"a": "(<PERSON><PERSON>_<PERSON>; <PERSON><PERSON>_<PERSON>; <PERSON><PERSON>ze; [<PERSON><PERSON><PERSON><PERSON>])", "d": "Gibt die Wahrscheinlichkeit für ein von zwei Werten eingeschlossenes Intervall zurück", "ad": "ist ein Bereich von Realisationen der Zufallsvariablen, denen Wahrscheinlichkeiten zugeordnet sind!sind die Wahrscheinlichkeiten zu den beobachteten Werten, eine Zahl gleich oder größer als 0 und kleiner als 1!ist die untere Grenze der Werte, deren Wahrscheinlichkeit berechnet werden soll!ist die optionale obere Grenze der Werte, deren Wahrscheinlichkeit berechnet werden soll"}, "QUARTILE": {"a": "(Matrix; Quartile)", "d": "Gibt die Quartile der Datengruppe zurück", "ad": "ist eine Matrix oder ein Zellbereich numerischer Werte, deren Quartile Si<PERSON> bestimmen möchten!gibt an, welches Quartil berechnet werden soll"}, "QUARTILE.INC": {"a": "(Array; Quartile)", "d": "Gibt die Quartile eines Datasets zurück, basierend auf Perzentilwerten von 0..1 einsch<PERSON><PERSON>lich", "ad": "ist ein Array oder ein Zellbereich numerischer Werte, deren Quartile Sie bestimmen möchten!ist eine Zahl: Minimalwert = 0; 1. Quartil = 1; Medianwert = 2; 3. Quartil = 3; Maximalwert = 4"}, "QUARTILE.EXC": {"a": "(Array; Quartile)", "d": "Gibt die Quartile eines Datasets zurück, basierend auf Perzentilwerten von 0..1 ausschließlich", "ad": "ist ein Array oder ein Zellbereich numerischer Werte, deren Quartile Sie bestimmen möchten!ist eine Zahl: Minimalwert = 0; 1. Quartil = 1; Medianwert = 2; 3. Quartil = 3; Maximalwert = 4"}, "RANK": {"a": "(<PERSON><PERSON>; <PERSON><PERSON><PERSON>; [<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> den <PERSON>ng, den eine Zahl innerhalb einer Liste von Zahlen einnimmt zurück", "ad": "ist die Zahl, deren Rang<PERSON>hl Si<PERSON> bestimmen möchten!ist eine Matrix mit Zahlen oder ein Bezug auf eine Liste von Zahlen. Nichtnumerische Werte im Bezug werden ignoriert!ist eine <PERSON>ahl, die angibt, wie der Rang von Zahl bestimmt werden soll; 0 = Rang in absteigend sortierter Liste; alle anderen Werte = Rang in aufsteigend sortierter Liste"}, "RANK.AVG": {"a": "(<PERSON><PERSON>; <PERSON><PERSON><PERSON>; [<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> den <PERSON>ng, den eine Zahl innerhalb einer Liste von Zahlen einnimmt, zurück: die Größe relativ zu anderen Werten in der Liste; wenn mehrere Werte die gleiche Rangzahl aufweisen, wird die durchschnittliche Rangzahl zurückgegeben", "ad": "ist die Zahl, für die der Rang ermittelt werden soll!ist ein Array von oder ein Bezug auf eine Liste mit Zahlen. Nicht numerische Werte werden ignoriert!ist eine Zahl: Rang in der absteigend sortierten Liste = 0 oder ohne Angabe; Rang in der aufsteigend sortierten Liste = jeder Wert ungleich Null"}, "RANK.EQ": {"a": "(<PERSON><PERSON>; <PERSON><PERSON><PERSON>; [<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> den Rang, den eine Zahl innerhalb einer Liste von Zahlen einnimmt, zurück: die Größe relativ zu anderen Werten in der Liste; wenn mehrere Werte die gleiche Rangzahl aufweisen, wird der oberste Rang dieser Gruppe von Werten zurückgegeben", "ad": "ist die Zahl, für die der Rang ermittelt werden soll!ist ein Array von oder ein Bezug auf eine Liste mit Zahlen. Nicht numerische Werte werden ignoriert!ist eine Zahl: Rang in der absteigend sortierten Liste = 0 oder ohne Angabe; Rang in der aufsteigend sortierten Liste = jeder Wert ungleich Null"}, "RSQ": {"a": "(<PERSON>_<PERSON><PERSON>; <PERSON>_<PERSON><PERSON>)", "d": "Gibt das Quadrat des Pearsonschen Korrelationskoeffizienten durch die gegebenen Datenpunkte zurück", "ad": "ist eine Matrix oder ein <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> können Zahlen, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder <PERSON>ü<PERSON>, die Zahlen enthalten, sein!ist eine Matrix oder ein <PERSON><PERSON><PERSON> von Datenpunkten, Argumente kö<PERSON><PERSON>ahl<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder <PERSON>, die Zahlen enthalten, sein"}, "SKEW": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt die Schiefe einer Verteilung zurück: eine Charakterisierung des Assymmetriegrads einer Verteilung um seinen Mittelwert", "ad": "sind 1 bis 255 Zahlen oder Namen, <PERSON><PERSON><PERSON> oder Bezüge, die Zahlen enthalten, deren Schiefe Sie berechnen möchten"}, "SKEW.P": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt die Schiefe einer Verteilung auf der Basis einer Grundgesamtheit zurück: eine Charakterisierung des Asymmetriegrads einer Verteilung um ihren Mittelwert", "ad": "sind 1 bis 254 Zahlen oder Namen, Matrizen oder Bezüge, die Zahlen enthalten, deren Schiefe bezogen auf die Grundgesamtheit Sie berechnen möchten"}, "SLOPE": {"a": "(<PERSON>_<PERSON><PERSON>; <PERSON>_<PERSON><PERSON>)", "d": "Gibt die Steigung der linearen Regressionsgeraden durch die gegebenen Datenpunkte zurück", "ad": "ist eine Matrix oder ein Zellbereich von numerisch abhängigen Datenpunkten, <PERSON>rg<PERSON>nte können Zahlen, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder <PERSON>züge, die Zahlen enthalten, sein!ist eine Reihe von unabhängigen Datenpunkten, <PERSON>rg<PERSON><PERSON> können Zahlen, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder Bezüge, die Zahlen enthalten, sein"}, "SMALL": {"a": "(Matrix; k)", "d": "Gibt den k-kleinsten Wert einer Datengruppe zurück", "ad": "ist eine Matrix oder ein Bereich von numerischen Daten, deren k-kleinsten Wert Sie bestimmen möchten!ist der Rang des Elementes einer Matrix oder eines Zellbereiches, dessen Wert zurückgegeben werden soll"}, "STANDARDIZE": {"a": "(x; <PERSON><PERSON><PERSON><PERSON><PERSON>; Standabwn)", "d": "Gibt den standardisierten Wert zurück.", "ad": "ist der Wert, den Si<PERSON> standardisieren möchten!ist das arithmetische Mittel der Verteilung!ist die Standardabweichung der Verteilung, eine positive Zahl"}, "STDEV": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Schätzt die Standardabweichung ausgehend von einer Stichprobe (logische Werte und Text werden im Beispiel ignoriert)", "ad": "sind 1 bis 255 Zahlen, die einer Stichprobe einer Grundgesamtheit entsprechen und können Zahlen oder Bezüge darstellen, die Zahlen enthalten"}, "STDEV.P": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Berechnet die Standardabweichung, ausgehend von der Grundgesamtheit angegeben als Argumente (logische Werte und Text werden ignoriert)", "ad": " sind 1 bis 255 einer Grundgesamtheit entsprechende Zahlen und können Zahlen oder Bezüge sein, die Zahlen enthalten"}, "STDEV.S": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Schätzt die Standardabweichung ausgehend von einer Stichprobe (logische Werte und Text werden im Beispiel ignoriert)", "ad": "sind 1 bis 255 Zahlen, die einer Stichprobe einer Grundgesamtheit entsprechen und können Zahlen oder Bezüge darstellen, die Zahlen enthalten"}, "STDEVA": {"a": "(Wert1; [Wert2]; ...)", "d": "Schätzt die Standardabweichung, ausge<PERSON> von einer Stichprobe, einschließlich logischer Werte und Text. Dabei werden Text und FALSCH als 0 interpretiert, WAHR wird als 1 interpretiert.", "ad": "sind 1 bis 255 Werte, die zu einer Stichprobe einer Grundgesamtheit gehören und können Zahlen, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder Bezüge sein"}, "STDEVP": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Berechnet die Standardabweichung, ausgehend von der Grundgesamtheit angegeben als Argumente (logische Werte und Text werden ignoriert)", "ad": " sind 1 bis 255 einer Grundgesamtheit entsprechende Zahlen und können Zahlen oder Bezüge sein, die Zahlen enthalten"}, "STDEVPA": {"a": "(Wert1; [Wert2]; ...)", "d": "Berechnet die Standardabweichung ausgehend von der Grundgesamtheit, einschließlich logischer Werte und Text. Dabei werden Text und FALSCH als 0 interpretiert, WAHR wird als 1 interpretiert.", "ad": "sind 1 bis 255 Werte, die zu einer Grundgesamtheit gehören, sie kö<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder Bezü<PERSON> sein, die Werte enthalten"}, "STEYX": {"a": "(<PERSON>_<PERSON><PERSON>; <PERSON>_<PERSON><PERSON>)", "d": "Gibt den Standardfehler des vorhergesagten Y-Werts für alle x-Werte in einer Regression zurück", "ad": "ist eine Matrix oder ein Bereich von abhängigen Datenpunkten, <PERSON><PERSON><PERSON><PERSON> können Zahlen, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder <PERSON>zü<PERSON>, die Zahlen enthalten, sein!ist eine Matrix oder ein Bereich von unabhängigen Datenpunkten, <PERSON><PERSON><PERSON><PERSON> können Zahlen, <PERSON><PERSON>, <PERSON><PERSON><PERSON> oder <PERSON>ü<PERSON>, die Zahlen enthalten, sein"}, "TDIST": {"a": "(x; Freiheitsgrade; Seiten)", "d": "Gibt Werte der Verteilungsfunktion (1-Alpha) e<PERSON> (Student) t-verteilten Zufallsvariablen zurück", "ad": "ist der numerische Wert, bei dem die Verteilung zu bewerten ist!ist eine ganze Zahl, die die Anzahl der Freiheitsgrade angibt, die die Verteilung charakterisieren!gibt die Anzahl der Verteilungsspitzen an, die zurückgegeben werden sollen: einseitige Verteilung = 1; zweiseitige Verteilung = 2"}, "TINV": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade)", "d": "Gibt Perzentile der zweiseitigen t-Verteilung zurück", "ad": "ist die mit der zweiseitigen Studenschen t-Verteilung verbundene Wahrscheinlichkeit, eine Zahl zwischen 0 und 1 einschließlich!ist eine positive ganze <PERSON>, die die Anzahl der Freiheitsgrade angibt, die die Verteilung charakterisieren"}, "T.DIST": {"a": "(x; <PERSON><PERSON>heits<PERSON>; kumuli<PERSON>)", "d": "<PERSON><PERSON><PERSON> die (Student) t-Verteilung der linken Endfläche zurück", "ad": "ist der numerische Wert, für den die Verteilung ausgewertet werden soll!ist eine <PERSON>an<PERSON>hl, die die Anzahl der Freiheitsgrade angibt, die die Verteilung charakterisieren!ist ein logischer Wert: für die kumulierte Verteilungsfunktion WAHR; für die Wahrscheinlichkeitsdichtefunktion FALSCH"}, "T.DIST.2T": {"a": "(x; Freiheits<PERSON>)", "d": "<PERSON><PERSON><PERSON> die (Student) t-Verteilung für zwei Endflächen zurück", "ad": "ist der numerische Wert, für den die Verteilung ausgewertet werden soll!ist eine Ganzzahl, die die Anzahl der Freiheitsgrade angibt, die die Verteilung charakterisieren"}, "T.DIST.RT": {"a": "(x; Freiheits<PERSON>)", "d": "<PERSON><PERSON><PERSON> die (Student) t-Verteilung für die rechte Endfläche zurück", "ad": "ist der numerische Wert, für den die Verteilung ausgewertet werden soll!ist eine Ganzzahl, die die Anzahl der Freiheitsgrade angibt, die die Verteilung charakterisieren"}, "T.INV": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade)", "d": "Gibt linksseitige Quantile der (Student) t-Verteilung zurück", "ad": "ist die mit der zweiseitigen Studenschen t-Verteilung verbundene Wahrscheinlichkeit, eine Zahl zwischen 0 und 1 einschließlich!ist eine positive ganze <PERSON>, die die Anzahl der Freiheitsgrade angibt, die die Verteilung charakterisieren"}, "T.INV.2T": {"a": "(<PERSON><PERSON><PERSON>; Freiheitsgrade)", "d": "Gibt zweiseitige Quantile der (Student) t-Verteilung zurück", "ad": "ist die mit der zweiseitigen Studenschen t-Verteilung verbundene Wahrscheinlichkeit, eine Zahl zwischen 0 und 1 einschließlich!ist eine positive ganze <PERSON>, die die Anzahl der Freiheitsgrade angibt, die die Verteilung charakterisieren"}, "T.TEST": {"a": "(Matrix1; Matrix2; Seiten; Typ)", "d": "Gibt die Teststatistik eines Studentschen t-Tests zurück", "ad": "ist der erste Datensatz!ist der zweite Datensatz!gibt die Anzahl der zurückzugebenden Verteilungsspitzen an: einseitige Verteilung = 1; zweiseitige Verteilung = 2!ist die Art des t-Tests: gepaart = 1, gleiche Varianz bei zwei Stichproben (homoskedastisch) = 2, ungleiche Varianz bei zwei Stichproben = 3"}, "TREND": {"a": "(<PERSON>_<PERSON><PERSON>; [<PERSON>_<PERSON><PERSON>]; [<PERSON><PERSON><PERSON>_x_Werte]; [<PERSON><PERSON><PERSON>])", "d": "G<PERSON>t Werte aus einem linearen Trend zurück, die bekannten Datenpunkten entsprechen, unter Verwendung der Methode der kleinsten Quadrate", "ad": "ist ein Bereich oder eine Matrix aus Y-Werten, die Ihnen bereits aus der Beziehung y = mx + b bekannt sind!ist ein optionaler Bereich oder eine optionale Matrix aus X-Werten, die Ihnen bereits aus der Beziehung y = mx + b bekannt sind, einer Matrix derselben Größe wie Y_Werte!ist ein Bereich oder eine Matrix aus den neuen X-Werten, für die die Funktion TREND die entsprechenden Y-Werte zurückgeben soll!ist ein Wahrheitswert, der angibt: die Konstante b wird normal berechnet, wenn Konstante = WAHR oder nicht angegeben ist, und b erhält den Wert 0, wenn Konstante = FALSCH"}, "TRIMMEAN": {"a": "(Matrix; Prozent)", "d": "G<PERSON>t den Mittelwert einer Datengruppe, ohne seine Werte an den Rändern zurück", "ad": "ist eine Matrix oder Gruppe von <PERSON>rten, die ohne ihre Ausreißer gemittelt wird!ist der Prozentsatz der Datenpunkte, die nicht in die Bewertung e<PERSON>hen sollen"}, "TTEST": {"a": "(Matrix1; Matrix2; Seiten; Typ)", "d": "Gibt die Teststatistik eines Studentschen t-Tests zurück", "ad": "ist die erste Datengruppe!ist die zweite Datengruppe!bestimmt die Anzahl der Endflächen!bestimmt die Form des durchzuführenden t-Tests"}, "VAR": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "<PERSON>hät<PERSON><PERSON> die Varianz, ausgehend von einer Stichprobe (logische Werte und Text werden in der Stichprobe ignoriert)", "ad": "sind 1 bis 255 numerische Argumente, die eine, aus einer Grundgesamtheit gezogene Stichprobe darstellen"}, "VAR.P": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Berech<PERSON> die Varianz, ausgehend von der Grundgesamtheit. Logische Werte und Text werden ignoriert", "ad": "sind 1 bis 255 numerische Argumente, die einer Grundgesamtheit entsprechen"}, "VAR.S": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "<PERSON>hät<PERSON><PERSON> die Varianz, ausgehend von einer Stichprobe (logische Werte und Text werden in der Stichprobe ignoriert)", "ad": "sind 1 bis 255 numerische Argumente, die eine, aus einer Grundgesamtheit gezogene Stichprobe darstellen"}, "VARA": {"a": "(Wert1; [Wert2]; ...)", "d": "Schät<PERSON><PERSON> die Varianz, ausge<PERSON> von einer Stichprobe, einschließlich logischer Werte und Text. Dabei werden Text und FALSCH als 0 interpretiert, WAHR wird als 1 interpretiert.", "ad": "sind 1 bis 255 Werte, die zu einer Stichprobe einer Grundgesamtheit gehören"}, "VARP": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Berech<PERSON> die Varianz, ausgehend von der Grundgesamtheit. Logische Werte und Text werden ignoriert", "ad": "sind 1 bis 255 numerische Argumente, die einer Grundgesamtheit entsprechen"}, "VARPA": {"a": "(Wert1; [Wert2]; ...)", "d": "Berech<PERSON> die Varianz, aus<PERSON><PERSON> von der Grundgesamtheit, einschließlich logischer Werte und Text. Dabei werden Text und FALSCH als 0 interpretiert, WAHR wird als 1 interpretiert.", "ad": "sind 1 bis 255 Argumente, die zu einer Grundgesamtheit gehören"}, "WEIBULL": {"a": "(x; Alpha; Beta; <PERSON><PERSON><PERSON><PERSON>)", "d": "G<PERSON>t Wahrscheinlichkeiten einer Weibull-verteilten Zufallsvariablen zurück", "ad": "ist der Wert der Verteilung (Perzentil), dessen Wahrscheinlichkeit Sie berechnen möchten, eine nicht-negative Zahl!ist ein Parameter der Verteilung, eine positive Zahl!ist ein Parameter der Verteilung, eine positive Zahl!ist der Wahrheitswert, der den Typ der Funktion bestimmt"}, "WEIBULL.DIST": {"a": "(x; Alpha; Beta; <PERSON><PERSON><PERSON><PERSON>)", "d": "G<PERSON>t Wahrscheinlichkeiten einer Weibull-verteilten Zufallsvariablen zurück", "ad": "ist der Wert der Verteilung (Perzentil), dessen Wahrscheinlichkeit Sie berechnen möchten, eine nicht-negative Zahl!ist ein Parameter der Verteilung, eine positive Zahl!ist ein Parameter der Verteilung, eine positive Zahl!ist der Wahrheitswert, der den Typ der Funktion bestimmt"}, "Z.TEST": {"a": "(Matrix; x; [Sigma])", "d": "Gibt die einseitige Prüfstatistik für einen Gaußtest (Normalverteilung) zurück.", "ad": "ist die Matrix oder der Datenbereich, gegen die/den Sie x testen möchten!ist der zu testende Wert!ist die bekannte Standardabweichung der Grundgesamtheit. Ohne Angabe wird die Beispielstandardabweichung verwendet"}, "ZTEST": {"a": "(Matrix; x; [Sigma])", "d": "Gibt die einseitige Prüfstatistik für einen Gaußtest (Normalverteilung) zurück.", "ad": "ist die Matrix oder der Datenbereich, gegen die/den Sie x testen möchten!ist der zu testende Wert!ist die bekannte Standardabweichung der Grundgesamtheit. Ohne Angabe wird die Beispielstandardabweichung verwendet"}, "ACCRINT": {"a": "(Emission; <PERSON><PERSON><PERSON>_<PERSON>min; Abre<PERSON>nung; Satz; Nennwert; Häufigkeit; [Basis]; [Berechnungsmethode])", "d": "Gibt die aufgelaufenen Zinsen für ein Wertpapier zurück, das regelmäßig Zinsen abwirft.", "ad": "ist das Emissionsdatum des Wertpapiers, als fortlaufende Zahl angegeben!ist der erste Zinstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist das Abrechnungsdatum des Wertpapiers, als fortlaufende Zahl angegeben!ist der jährliche Kuponzinssatz des Wertpapiers!ist der Nennwert des Wertpapiers!ist die Anzahl der Zinszahlungen pro Jahr!gibt an, auf welcher Basis die Zinstage gezählt werden!ist ein logischer Wert: für abgelaufene Zinsen ab Emissionsdatum = WAHR oder leer; für Berechnungen aus letztem Datum der Zinszahlungen = FALSCH"}, "ACCRINTM": {"a": "(Emission; Abrechnung; Nominalzins; Nennwert; [Basis])", "d": "Gibt die aufgelaufenen Zinsen (Stückzinsen) eines Wertpapiers zurück, die bei Fälligkeit ausgezahlt werden", "ad": "ist das Datum der Wertpapieremission, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist der jährliche Nominalzins (Kuponzinssatz) des Wertpapiers!ist der Nennwert des Wertpapiers!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "AMORDEGRC": {"a": "(<PERSON><PERSON>_<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>_Zinstermin; Restwert; <PERSON>rmin; Satz; [<PERSON><PERSON>])", "d": "Gibt eine anteilige lineare Abschreibung eines Wirtschaftsguts für jeden Abrechnungszeitraum zurück.", "ad": "ist der Anschaffungswert des Wirtschaftsguts!ist das Kaufdatum des Wirtschaftsguts!ist das Enddatum des ersten Zinstermins!ist der Restwert am Ende der Lebensdauer eines Wirtschaftsguts.!ist der Zeitraum!ist der Abschreibungssatz.!Jahr_Basis : 0 für das Jahr mit 360 Tagen, 1 für tatsächlich, 3 für das Jahr mit 365 Tagen."}, "AMORLINC": {"a": "(<PERSON><PERSON>_<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>_Zinstermin; Restwert; <PERSON>rmin; Satz; [<PERSON><PERSON>])", "d": "Gibt eine anteilige lineare Abschreibung eines Wirtschaftsguts für jeden Abrechnungszeitraum zurück.", "ad": "ist der Anschaffungswert des Wirtschaftsguts!ist das Kaufdatum des Wirtschaftsguts!ist das Enddatum des ersten Zinstermins!ist der Restwert am Ende der Lebensdauer eines Wirtschaftsguts.!ist der Zeitraum!ist der Abschreibungssatz.!Jahr_Basis : 0 für das Jahr mit 360 Tagen, 1 für tatsächlich, 3 für das Jahr mit 365 Tagen."}, "COUPDAYBS": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Häufigkeit; [Basis])", "d": "Gibt die Anzahl der Tage vom Anfang des Zinstermins bis zum Abrechnungstermin zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist die Anzahl der Zinszahlungen pro Jahr!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "COUPDAYS": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Häufigkeit; [Basis])", "d": "Gibt die Anzahl der Tage der Zinsperiode zurück, die den Abrechnungstermin einschließt", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist die Anzahl der Zinszahlungen pro Jahr!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "COUPDAYSNC": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Häufigkeit; [Basis])", "d": "Gibt die Anzahl der Tage vom Abrechnungstermin bis zum nächsten Zinstermin zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist die Anzahl der Zinszahlungen pro Jahr!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "COUPNCD": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Häufigkeit; [Basis])", "d": "Gibt das Datum des ersten Zinstermins nach dem Abrechnungstermin zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist die Anzahl der Zinszahlungen pro Jahr!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "COUPNUM": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Häufigkeit; [Basis])", "d": "Gibt die Anzahl der Zinstermine zwischen Abrechnungs- und Fälligkeitdatum zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist die Anzahl der Zinszahlungen pro Jahr!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "COUPPCD": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Häufigkeit; [Basis])", "d": "Gibt das Datum des letzten Zinstermins vor dem Abrechnungstermin zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist die Anzahl der Zinszahlungen pro Jahr!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "CUMIPMT": {"a": "(<PERSON><PERSON>; Zzr; Bw; Zeitraum_Anfang; Zeitraum_Ende; F)", "d": "Berechnet die kumulierten Zinsen, die zwischen zwei Perioden zu zahlen sind", "ad": "ist der Zinssatz!ist die Anzahl aller Zahlungsperioden!ist der Gegenwartswert!ist die erste in die Berechnung einfließende Periode!ist die letzte in die Berechnung einfließende Periode!gibt an, zu welchem Zeitpunkt einer Periode jeweils eine Zahlung fällig ist"}, "CUMPRINC": {"a": "(<PERSON><PERSON>; Zzr; Bw; Zeitraum_Anfang; Zeitraum_Ende; F)", "d": "Berechnet die aufgelaufene Tilgung eines Darlehens, die zwischen zwei Perioden zu zahlen ist", "ad": "ist der Zinssatz!ist die Anzahl aller Zahlungsperioden!ist der Gegenwartswert!ist die erste in die Berechnung einfließende Periode!ist die letzte in die Berechnung einfließende Periode!gibt an, zu welchem Zeitpunkt einer Periode jeweils eine Zahlung fällig ist"}, "DB": {"a": "(Ansch_<PERSON>; Restwert; Nutzungsdauer; Periode; [<PERSON><PERSON>])", "d": "Gibt die geometrisch-degressive Abschreibung eines Wirtschaftsguts für eine bestimmte Periode zurück.", "ad": "sind die Anschaffungskosten eines Wirtschaftsguts!ist der Restwert am Ende der Nutzungsdauer (wird häufig auch als Schrottwert bezeichnet)!ist die Anzahl der Perioden, über die das Wirtschaftsgut abgeschrieben wird (auch als Nutzungsdauer bezeichnet)!ist die Periode, deren Abschreibungsbetrag Sie berechnen möchten. Für das Argument Periode muss dieselbe Zeiteinheit wie für die Nutzungsdauer verwendet werden!ist die Anzahl der Monate im ersten Jahr. Fehlt das Argument Monate, wird 12 vorausgesetzt"}, "DDB": {"a": "(Ansch_<PERSON>; Restwert; Nutzungsdauer; Periode; [<PERSON><PERSON><PERSON>])", "d": "Gibt die degressive Doppelraten-Abschreibung eines Wirtschaftsguts für eine bestimmte Periode zurück", "ad": "sind die Anschaffungskosten eines Wirtschaftsguts!ist der Restwert am Ende der Nutzungsdauer (wird häufig auch als Schrottwert bezeichnet)!ist die Anzahl der Perioden, über die das Wirtschaftsgut abgeschrieben wird (auch als Nutzungsdauer bezeichnet)!ist die Periode, deren Abschreibungsbetrag Sie berechnen möchten. Für das Argument Periode muss dieselbe Zeiteinheit wie für die Nutzungsdauer verwendet werden!ist die Rate, um die der Restbuchwert abnimmt ( Faktor steht für Faktor * 100 % / Nutzungsdauer)"}, "DISC": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; Fälligkeit; <PERSON>; Rückzahlung; [Ba<PERSON>])", "d": "Gibt den in Prozent ausgedrückten Abschlag (Disagio) eines Wertpapiers zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist der Kurs des Wertpapiers pro 100 EUR Nennwert!ist der Rückzahlungswert des Wertpapiers pro 100 EUR Nennwert!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "DOLLARDE": {"a": "(<PERSON><PERSON>; <PERSON>)", "d": "Konvertiert eine Notierung, die als Dezimalbruch ausgedrückt wurde, in eine Dezimalzahl", "ad": "ist eine als Dezimalbruch ausgedrückte Zahl!ist eine ganze Zahl, die als Nenner des Dezimalbruchs verwendet wird"}, "DOLLARFR": {"a": "(<PERSON><PERSON>; <PERSON>)", "d": "Konvertiert eine Notierung in dezimaler Schreibweise in einen gemischten Dezimalbruch", "ad": "ist eine Dezimalzahl!ist eine ganze <PERSON>ahl, die als Nenner des Dezimalbruchs verwendet wird"}, "DURATION": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Nominalzins; Rendite; Häufigkeit; [Basis])", "d": "Gibt die jährliche Duration eines Wertpapiers mit periodischen Zinszahlungen zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist der jährliche Nominalzins (Kuponzinssatz) des Wertpapiers!ist die jährliche Rendite des Wertpapiers!ist die Anzahl der Zinszahlungen pro Jahr!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "EFFECT": {"a": "(Nominalzins; Perioden)", "d": "Gibt die jährliche Effektivverzinsung zurück", "ad": "ist die Nominalverzinsung!ist die Anzahl der Zinszahlungen pro Jahr"}, "FV": {"a": "(<PERSON><PERSON>; Zzr; Rmz; [Bw]; [F])", "d": "Gibt den zukünftigen Wert (Endwert) einer Investition auf der Basis periodischer, konstanter Zahlungen und einem konstanten Zinssatz zurück", "ad": "ist der Zinssatz pro Periode (Zahlungszeitraum). Verwenden Sie z. B. 6%/4 für Quartalszahlungen mit einem Zinssatz von 6%!gibt an, über wie viele Perioden die jeweilige Annuität (Rente) gezahlt wird!ist der Betrag (Annuität), der in jeder Periode gezahlt wird. Dieser Betrag bleibt während der Laufzeit konstant!ist der Barwert oder der heutige Gesamtwert einer Reihe zukünftiger Zahlungen. Ist nichts angegeben, ist der Barwert = 0!ist ein Wert zur Angabe, wann Zahlungen fällig sind (Fälligkeit): 1 = Zahlung am Anfang der Periode, 0 oder keine Angabe = Zahlung am Ende der Periode"}, "FVSCHEDULE": {"a": "(<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>)", "d": "Gibt den aufgezinsten Wert des Anfangskapitals für eine Reihe periodisch unterschiedlicher Zinssätze zurück", "ad": "ist der Gegenwartswert!ist eine Reihe von Zinssätzen, als Matrix eingegeben"}, "INTRATE": {"a": "(Abre<PERSON>nung; Fälligkeit; Anlage; Rückzahlung; [Basis])", "d": "Gibt den Zinssatz eines voll investierten Wertpapiers zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist der Betrag, der in dem Wertpapier angelegt werden soll!ist der Betrag, der bei Fälligkeit zu erwarten ist!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "IPMT": {"a": "(<PERSON><PERSON>; Zr; Zzr; Bw; [Zw]; [F])", "d": "Gibt die Zinszahlung einer Investition für eine angegebene Periode auf der Basis von periodischen, konstanten Zahlungen und einem konstanten Zinssatz zurück", "ad": "ist der Zinssatz pro Periode (Zahlungszeitraum). Verwenden Sie z. B. 6%/4 für Quartalszahlungen von 6%!ist die Periode, für die Sie den Zinsbetrag berechnen möchten. Muss zwischen 1 und Zr liegen!gibt an, über wie viele Perioden die jeweilige Annuität (Rente) gezahlt wird!ist der Barwert oder der heutige Gesamtwert einer Reihe zukünftiger Zahlungen!ist der zukünftige Wert (Endwert) oder der Kassenbestand, den Sie nach der letzten Zahlung erreicht haben möchten. Bei fehlender Angabe ist der Barwert = 0!kann den Wert 0 oder 1 annehmen und gibt an, wann Zahlungen fällig sind (Fälligkeit): 1 = Zahlung am Anfang der Periode, 0 oder keine Angabe = Zahlung am Ende der Periode"}, "IRR": {"a": "(<PERSON>rte; [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Gibt den internen Zinsfuß einer Investition ohne Finanzierungskosten oder Reinvestitionsgewinne zurück", "ad": "ist eine Matrix von Zellen oder ein Bezug auf Zellen, in denen die Zahlen stehen, für die Sie den internen Zinsfuß berechnen möchten!ist eine <PERSON>ahl, von der <PERSON> annehm<PERSON>, dass sie dem Ergebnis der Funktion nahe kommt. Wen<PERSON> der Parameter fehlt, wird 0,1 (10 Prozent) angenommen"}, "ISPMT": {"a": "(rate; per; nper; pv)", "d": "Gibt die während eines bestimmten Zeitraums gezahlten Zinsen einer Investition zurück", "ad": "Zinssatz pro Periode. Verwenden Sie z. B. 6%/4 für Quartalszahlungen mit einem Zinssatz von 6%!Zeitraum, für den die Zinsen ermittelt werden sollen!Anzahl der Zahlungsperioden in einer Investition!der heutige Gesamtwert einer Reihe zukünftiger Zahlungen"}, "MDURATION": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Coupon; Rendite; Häufigkeit; [Basis])", "d": "Gibt die geänderte Dauer für ein Wertpapier mit einem angenommenen Nennwert von 100 EUR zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, angegeben als fortlaufende Zahl!ist der Fälligkeitstermin des Wertpapiers, angegeben als fortlaufende Zahl!ist der jährliche Kuponzinssatz des Wertpapiers!ist die jährliche Rendite des Wertpapiers!ist die Anzahl der Zinszahlungen pro Jahr!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "MIRR": {"a": "(Werte; Investition; Reinvestition)", "d": "Gibt die interne Rendite für eine Reihe nicht periodisch anfallender Zahlungen zurück, wobei sowohl die Investitionkosten als auch die Zinssätze auf Wiederanlagen berücksichtigt werden", "ad": "ist eine Matrix von oder ein Bezug auf <PERSON>n, die Zahlen enthalten, die eine Reihe von periodischen Zahlungen (negativ) und Erträgen (positiv) darstellen!ist der Zinssatz, den Sie für das Geld für die Cashflows berücksichtigen müssen!ist der Zinssatz für die Wiederanlage von Cashflows"}, "NOMINAL": {"a": "(Effektiver_Zins; Perioden)", "d": "Gibt die jährliche Nominalverzinsung zurück", "ad": "ist die Effektivverzinsung!ist die Anzahl der Zinszahlungen pro Jahr"}, "NPER": {"a": "(Z<PERSON>; Rmz; Bw; [Zw]; [F])", "d": "Gibt die Anzahl der Zahlungsperioden einer Investition auf der Basis periodischer, konstanter Zahlungen einem konstanten Zinssatz zurück", "ad": "ist der Zinssatz pro Periode (Zahlungszeitraum). Verwenden Sie z. B. 6%/4 für Quartalszahlungen von 6%!ist der Betrag (Annuität), der in jeder Periode gezahlt wird. Dieser Betrag bleibt während der Laufzeit konstant!ist der Barwert oder der heutige Gesamtwert einer Reihe zukünftiger Zahlungen!ist der zukünftige Wert (Endwert) oder der Kassenbestand, den Sie nach der letzten Zahlung erreicht haben möchten. Bei fehlender <PERSON>abe, wird 0 (Null) verwendet!kann den Wert 0 oder 1 annehmen und gibt an, wann Zahlungen fällig sind (Fälligkeit): 1 = Zahlung am Anfang der Periode, 0 oder keine Angabe = Zahlung am Ende der Periode"}, "NPV": {"a": "(Z<PERSON>; Wert1; [Wert2]; ...)", "d": "G<PERSON>t den Nettobarwert einer Investition auf Basis eines Abzinsungsfaktors für eine Reihe zukünftiger Zahlungen (negative Werte) und Erträge (positive Werte) zurück", "ad": "ist der Abzinsungssatz für die Dauer einer Periode!sind 1 bis 254 Zahlungen und Erträge, die gleichmäßig über die Zeit verteilt sind und am Ende jeder Periode stattfinden"}, "ODDFPRICE": {"a": "(Abrechnung; Fälligkeit; Emission; <PERSON><PERSON><PERSON>_Zinstermin; Zins; Rendite; Rückzahlung; Häufigkeit; [Basis])", "d": "G<PERSON>t den Kurs pro 100 EUR Nennwert eines Wertpapiers mit einem unregelmäßigen ersten Zinstermin zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist das Datum der Wertpapieremission, als fortlaufende Zahl angegeben!ist der erste Zinstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist der Zinssatz des Wertpapiers!ist die jährliche Rendite des Wertpapiers!ist der Rückzahlungswert des Wertpapiers pro 100 EUR Nennwert!ist die Anzahl der Zinszahlungen pro Jahr!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "ODDFYIELD": {"a": "(Abrechnung; Fälligkeit; Emission; <PERSON><PERSON><PERSON>_Zinstermin; <PERSON><PERSON>; <PERSON>; Rückzahlung; Häufigkeit; [Basis])", "d": "Gibt die Rendite eines Wertpapiers mit einem unregelmäßigen ersten Zinstermin zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist der Emissionstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist der erste Zinstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist der Zinssatz des Wertpapiers!ist der Kurs des Wertpapiers!ist der Rückzahlungswert pro 100 EUR Nennwert!ist die Anzahl der Zinszahlungen pro Jahr!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "ODDLPRICE": {"a": "(Abrechnung; Fälligkeit; Letzter_Zinstermin; Zins; Rendite; Rückzahlung; Häufigkeit; [Basis])", "d": "G<PERSON>t den Kurs pro 100 EUR Nennwert eines Wertpapiers mit einem unregelmäßigen letzten Zinstermin zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist der letzte Zinstermin des Wertpapiers vor dem Abrechnungstermin, als fortlaufende Zahl angegeben!ist der Zinssatz des Wertpapiers!ist die jährliche Rendite des Wertpapiers!ist der Rückzahlungswert des Wertpapiers pro 100 EUR Nennwert!ist die Anzahl der Zinszahlungen pro Jahr!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "ODDLYIELD": {"a": "(Abrechnung; Fälligkeit; Letzter_Zinstermin; <PERSON><PERSON>; <PERSON>; Rückzahlung; Häufigkeit; [Basis])", "d": "Gibt die Rendite eines Wertpapiers mit einem unregelmäßigen letzten Zinstermin zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist der letzte Zinstermin des Wertpapiers vor dem Abrechnungstermin, als fortlaufende Zahl angegeben!ist der Zinssatz des Wertpapiers!ist der Kurs des Wertpapiers!ist der Rückzahlungswert des Wertpapiers pro 100 EUR Nennwert!ist die Anzahl der Zinszahlungen pro Jahr!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "PDURATION": {"a": "(Z<PERSON>; Bw; Zw)", "d": "Gibt die Anzahl der Zahlungsperioden zurück, die eine Investition zum Erreichen eines angegebenen Werts benötigt.", "ad": "ist der Zinssatz pro Zahlungsperiode.!ist der aktuelle Wert der Investition!ist der gewünschte zukünftige Wert der Investition"}, "PMT": {"a": "(<PERSON><PERSON>; Zzr; Bw; [Zw]; [F])", "d": "Berechnet die Zahlung für einen Kredit auf der Basis konstanzer Zahlungen und einem konstanten Zinssatz", "ad": "ist der Zinssatz pro Periode (Zahlungszeitraum) für den Kredit. Verwenden Sie z. B. 6%/4 für Quartalszahlungen mit einem Zinssatz von 6%!gibt an, über wie viele Perioden die jeweilige Annuität gezahlt wird!ist der Barwert: Der Gesamtbetrag, den eine Reihe zukünftiger Zahlungen zum gegenwärtigen Zeitpunkt wert ist!ist der zukünftige Wert (Endwert) oder der Kassenbestand, den Sie nach der letzten Zahlung erreicht haben möchten. 0 (Null) bei fehlender Angabe!kann den Wert 0 oder 1 annehmen und gibt an, wann Zahlungen fällig sind (Fälligkeit): 1 = Zahlung am Anfang der Periode, 0 oder keine Angabe = Zahlung am Ende der Periode"}, "PPMT": {"a": "(<PERSON><PERSON>; Zr; Zzr; Bw; [Zw]; [F])", "d": "Gibt die Kapitalrückzahlung einer Investition für eine angegebene Periode auf der Basis periodischer, konstanter Zahlungen und einem konstanten Zinssatz zurück", "ad": "ist der Zinssatz pro Periode (Zahlungszeitraum). Verwenden Sie z. B. 6%/4 für Quartalszahlungen von 6%!gibt die Periode an und muss zwischen 1 und Zr liegen!gibt an, über wie viele Perioden die jeweilige Annuität (Rente) gezahlt wird!ist der Barwert: der Gesamtbetrag, den eine Reihe zukünftiger Zahlungen zum gegenwärtigen Zeitpunkt wert ist!ist der zukünftige Wert (Endwert) oder der Kassenbestand, den Sie nach der letzten Zahlung erreicht haben möchten!kann den Wert 0 oder 1 annehmen und gibt an, wann Zahlungen fällig sind (Fälligkeit): 1 = Zahlung am Anfang der Periode, 0 oder keine Angabe = Zahlung am Ende der Periode"}, "PRICE": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; Fälligkeit; Zins; Ren<PERSON>e; Rückzahlung; Häufigkeit; [Basis])", "d": "<PERSON><PERSON><PERSON> den Kurs pro 100 EUR Nennwert eines Wertpapiers zurück, das periodisch Zinsen auszahlt", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist der jährliche Nominalzins (Kuponzinssatz) des Wertpapiers!ist die jährliche Rendite des Wertpapiers!ist der Rückzahlungswert des Wertpapiers pro 100 EUR Nennwert!ist die Anzahl der Zinszahlungen pro Jahr!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "PRICEDISC": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; Fälligkeit; Disagio; Rückzahlung; [Basis])", "d": "G<PERSON>t den Kurs pro 100 EUR Nennwert eines unverzinslichen Wertpapiers zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist der in Prozent ausgedrückte Abschlag (Disagio) des Wertpapiers!ist der Rückzahlungswert des Wertpapiers pro 100 EUR Nennwert!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "PRICEMAT": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Emission; Zins; Rendite; [<PERSON><PERSON>])", "d": "G<PERSON><PERSON> den Kurs pro 100 EUR Nennwert eines Wertpapiers zurück, das Zinsen am Fälligkeitsdatum auszahlt", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist das Datum der Wertpapieremission, als fortlaufende Zahl angegeben!ist der Zinssatz des Wertpapiers zum Emissionstermin!ist die jährliche Rendite des Wertpapiers!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "PV": {"a": "(<PERSON><PERSON>; Zzr; Rmz; [Zw]; [F])", "d": "Gibt den Barwert einer Investition zurück: den heutigen Gesamtwert einer Reihe zukünftiger Zahlungen", "ad": "ist der Zinssatz pro Periode (Zahlungszeitraum). Verwenden Sie z. B. 6%/4 für Quartalszahlungen mit einem Zinssatz von 6%!gibt an, über wie viele Perioden die jeweilige Annuität (Rente) gezahlt wird!ist der Betrag (Annuität), der in jeder Periode gezahlt wird. Dieser Betrag bleibt während der Laufzeit konstant!ist der zukünftige Wert (Endwert) oder der Kassenbestand, den Sie nach der letzten Zahlung erreicht haben möchten!kann den Wert 0 oder 1 annehmen und gibt an, wann Zahlungen fällig sind (Fälligkeit): 1 = Zahlungen am Anfang einer Periode; 0 oder keine Angabe = Zahlungen am Ende einer Periode"}, "RATE": {"a": "(Zzr; Rmz; Bw; [Zw]; [F]; [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Gibt den Zinssatz einer Annuität pro Periode zurück. Verwenden Sie z. B. 6%/4 für Quartalszahlungen von 6%", "ad": "ist die Anzahl der Zahlungsperioden!ist der Betrag (Annuität), der in jeder Periode gezahlt wird. Dieser Betrag bleibt während der Laufzeit konstant.!ist der Barwert: der Gesamtbetrag, den eine Reihe zukünftiger Zahlungen zum gegenwärtigen Zeitpunkt wert ist!ist der zukünftige Wert (Endwert) oder der Kassenbestand, den Si<PERSON> nach der letzten Zahlung erreicht haben möchten!kann den Wert 0 oder 1 annehmen und gibt an, wann Zahlungen fällig sind (Fälligkeit): 1 = Zahlung am Anfang der Periode, 0 = Zahlung am Ende der Periode!entspricht Ihrer Schätzung, wie hoch der Zinssatz sein wird. Wenn der Parameter fehlt, wird 0,1 (10 Prozent) angenommen"}, "RECEIVED": {"a": "(<PERSON><PERSON><PERSON><PERSON>ng; Fälligkeit; Anlage; Disagio; [Ba<PERSON>])", "d": "Gibt den Auszahlungsbetrag eines voll investierten Wertpapiers am Fälligkeitstermin zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist der Betrag, der in dem Wertpapier angelegt werden soll!ist der in Prozent ausgedrückte Abschlag (Disagio) des Wertpapiers!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "RRI": {"a": "(Zzr; Bw; Zw)", "d": "Gibt den effektiven Jahreszins für den Wertzuwachs einer Investition zurück", "ad": "ist die Anzahl der Perioden für die Investition!ist der aktuelle Wert der Investition!ist der zukünftige Wert der Investition"}, "SLN": {"a": "(Ansch_Wert; Restwert; Nutzungsdauer)", "d": "Gibt die lineare Abschreibung eines Wirtschaftsguts pro Periode zurück", "ad": "sind die Anschaffungskosten eines Wirtschaftsguts!ist der Restwert am Ende der Nutzungsdauer (wird häufig auch als Schrottwert bezeichnet)!ist die Anzahl der Perioden, über die das Wirtschaftsgut abgeschrieben wird (auch als Nutzungsdauer bezeichnet)"}, "SYD": {"a": "(Ansch_Wert; Restwert; Nutzungsdauer; Zr)", "d": "Gibt die arithmetisch-degressive Abschreibung eines Wirtschaftsguts für eine bestimmte Periode zurück", "ad": "sind die Anschaffungskosten eines Wirtschaftsguts!ist der Restwert am Ende der Nutzungsdauer (wird häufig auch als Schrottwert bezeichnet)!ist die Anzahl der Perioden, über die das Wirtschaftsgut abgeschrieben wird (auch als Nutzungsdauer bezeichnet)!ist die Periode und muss dieselbe Zeiteinheit verwenden wie die Nutzungsdauer"}, "TBILLEQ": {"a": "(Abre<PERSON>nung; Fälligkeit; Abzinsungssatz)", "d": "Gibt die Rendite eines Wertpapiers zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, angegeben als fortlaufende Zahl!ist das Fälligkeitsdatum des Wertpapiers, angegeben als fortlaufende Zahl!ist der Abzinsungssatz des Wertpapiers"}, "TBILLPRICE": {"a": "(Abre<PERSON>nung; Fälligkeit; Abzinsungssatz)", "d": "G<PERSON>t den Kurs pro 100 EUR Nennwert eines Wertpapiers zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, angegeben als fortlaufende Zahl!ist der Fälligkeitstermin des Wertpapiers, angegeben als fortlaufende Zahl!ist der Abzinsungssatz des Wertpapiers"}, "TBILLYIELD": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; Fälligkeit; pr)", "d": "Gibt die Rendite eines Wertpapiers zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, angegeben als fortlaufende Zahl!ist der Fälligkeitstermin des Wertpapiers, angegeben als fortlaufende Zahl!ist der Kurs des Wertpapiers pro 100 EUR Nennwert"}, "VDB": {"a": "(<PERSON><PERSON>_<PERSON>; Restwert; Nutzungsdauer; Anfang; Ende; [<PERSON><PERSON><PERSON>]; [<PERSON><PERSON>_wechs<PERSON>n])", "d": "Gibt die degressive Doppelraten-Abschreibung eines Wirtschaftsguts für eine bestimmte Periode oder Teilperiode zurück", "ad": "sind die Anschaffungskosten eines Wirtschaftsguts!ist der Wert am Ende des Abschreibungszeitraums (auch Restwert des Anlageobjekts genannt)!ist die Anzahl der Perioden, über die das Wirtschaftsgut abgeschrieben wird (auch als Nutzungsdauer bezeichnet)!ist der Anfangszeitraum, für den Sie die Abschreibung berechnen wollen!ist der Endzeitraum, für den Sie die Abschreibung berechnen wollen!ist das Maß, in welchem die Abschreibung abnimmt!ist ein Wahrheitswert, der angibt, ob zur linearen Abschreibung gewechselt werden soll, wenn diese höhere jährliche Abschreibungen zurückgibt"}, "XIRR": {"a": "(<PERSON><PERSON>; Zeitpkte; [Schätzwer<PERSON>])", "d": "Gibt den internen Zinsfuß einer Reihe nicht periodisch anfallender Zahlungen zurück", "ad": "ist eine Reihe von nicht periodisch anfallenden Zahlungen, die den Zeitpunkten des Zahlungsplans entsprechen!sind die Zeitpunkte im Zahlungsplan, die den nicht periodisch anfallenden Zahlungen entsprechen!ist eine <PERSON>ahl, von der Si<PERSON> annehmen, dass sie dem Ergebnis der Funktion XINTZINSFUSS nahekommt"}, "XNPV": {"a": "(<PERSON><PERSON>; <PERSON><PERSON>; Zeitpkte)", "d": "G<PERSON>t den Nettobarwert (Kapitalwert) einer Reihe nicht periodisch anfallender Zahlungen zurück", "ad": "ist der Diskontsatz, der für die Zahlungen zu berücksichtigen ist!ist eine Reihe von nicht periodisch anfallenden Zahlungen, die den Zeitpunkten des Zahlungsplans entsprechen!sind die Zeitpunkte im Zahlungsplan, die den nicht periodisch anfallenden Zahlungen entsprechen"}, "YIELD": {"a": "(A<PERSON><PERSON><PERSON>ng; Fälligkeit; Zins; <PERSON>; Rückzahlung; Häufigkeit; [Basis])", "d": "Gibt die Rendite eines Wertpapiers zurück, das periodisch Zinsen auszahlt", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist der jährliche Nominalzins (Kuponzinssatz) des Wertpapiers!ist der Kurs des Wertpapiers pro 100 EUR Nennwert!ist der Rückzahlungswert des Wertpapiers pro 100 EUR Nennwert!ist die Anzahl der Zinszahlungen pro Jahr!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "YIELDDISC": {"a": "(<PERSON><PERSON><PERSON><PERSON><PERSON>; Fälligkeit; <PERSON>; Rückzahlung; [Ba<PERSON>])", "d": "Gibt die jährliche Rendite eines unverzinslichen Wertpapiers zurück", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist der Kurs des Wertpapiers pro 100 EUR Nennwert!ist der Rückzahlungswert des Wertpapiers pro 100 EUR Nennwert!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "YIELDMAT": {"a": "(A<PERSON><PERSON>nung; Fälligkeit; Emission; Zins; Kurs; [<PERSON><PERSON>])", "d": "Gibt die jährliche Rendite eines Wertpapiers zurück, das Zinsen am Fälligkeitsdatum auszahlt", "ad": "ist der Abrechnungstermin des Wertpapierkaufs, als fortlaufende Zahl angegeben!ist der Fälligkeitstermin des Wertpapiers, als fortlaufende Zahl angegeben!ist das Datum der Wertpapieremission, als fortlaufende Zahl angegeben!ist der Zinssatz des Wertpapiers am Emissionsdatum!ist der Kurs des Wertpapiers pro 100 EUR Nennwert!gibt an, auf welcher Basis die Zinstage gezählt werden"}, "ABS": {"a": "(<PERSON><PERSON>)", "d": "Gibt den Absolutwert einer Zahl zurück", "ad": "ist die reelle Zahl, deren Absolutwert Sie ermitteln möchten"}, "ACOS": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Arkuskosinus einer Zahl im Bereich von 0 bis Pi zurück", "ad": "ist der Kosinus des Winkels, den Si<PERSON> berechnen wollen, und liegt zwischen -1 und 1"}, "ACOSH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den umgekehrten hyperbolischen Kosinus einer Zahl zurück.", "ad": "ist eine reelle Zahl größer oder gleich 1 zurück"}, "ACOT": {"a": "(<PERSON><PERSON>)", "d": "Gibt den Arkuskotangens einer Zahl als Bogenmaß im Bereich 0 bis Pi zurück.", "ad": "ist der Kotangens des gewünschten Winkels"}, "ACOTH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den umgekehrten hyperbolischen Kotangens einer Zahl zurück", "ad": "ist der hyperbolische Kotangens des gewünschten Winkels"}, "AGGREGATE": {"a": "(Funk<PERSON>; Optionen; Bezug1; ...)", "d": "Gibt ein Aggregat in einer Liste oder Datenbank zurück", "ad": "ist die Zahl von 1 bis 19, die die Zusammenfassungsfunktion für das Aggregat angibt.!ist die Zahl von 0 bis 7, mit der die für das Aggregat zu ignorierenden Werte angegeben werden!ist das Array oder der Bereich der numerischen Daten, für die das Aggregat berechnet werden soll!zeigt die Position im Array an; ist der k-größte, k-kleinste, das k-Quantil oder k-Quartil.!ist die Zahl von 1 bis 19, die die Zusammenfassungsfunktion für das Aggregat angibt.!ist die Zahl von 0 bis 7, mit der die für das Aggregat zu ignorierenden Werte angegeben werden!sind 1 bis 253 Bereiche oder Bezüge, für die das Aggregat gewünscht wird"}, "ARABIC": {"a": "(Text)", "d": "Konvertiert eine römische Zahl in eine arabische Zahl", "ad": "ist die zu konvertierende römische Zahl"}, "ASC": {"a": "(Text)", "d": "<PERSON><PERSON><PERSON> mit einem Double-Byte-Z<PERSON>satz (DBCS) werden in dieser Funktion Zeichen normaler Breite (Double-Byte-Zeichen) in Zeichen halber Breite (Single-Byte-Zeichen) umgewandelt", "ad": "ist der Text, den Si<PERSON> ändern möchten"}, "ASIN": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Arkussinus einer Zahl im Bereich von -Pi/2 bis Pi/2 zurück", "ad": "ist der Sinus des Winkels, den Si<PERSON> berechnen wollen, und liegt zwischen -1 und 1"}, "ASINH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den umgekehrten hyperbolischen Sinus einer Zahl zurück.", "ad": "ist eine reelle Zahl größer oder gleich 1"}, "ATAN": {"a": "(<PERSON><PERSON>)", "d": "Gibt den Arkustangens einer Zahl in RAD in einem Bereich von -Pi/2 bis Pi/2 zurück", "ad": "ist der Tangens des Winkels, den Si<PERSON> berechnen wollen"}, "ATAN2": {"a": "(x_Koordinate; y_Koordinate)", "d": "Gibt den Arkustangens ausgehend von einer x- und einer y-Koordinate zurück in RAD von -Pi bis Pi (ohne -Pi selbst)", "ad": "ist die x-Koordinate des Punktes!ist die y-Koordinate des Punktes"}, "ATANH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den umgekehrten hyperbolischen Tangens einer Zahl zurück.", "ad": "ist eine reelle Zahl zwischen -1 und 1 (-1 und 1 ausgeschlossen)"}, "BASE": {"a": "(<PERSON><PERSON>; <PERSON><PERSON>; [<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Konvertiert eine Zahl in eine Textdarstellung mit der angegebenen Basis", "ad": "ist die zu konvertierende Zahl!ist die Basis, in die die Zahl konvertiert werden soll!ist die Mindestlänge der zurückgegebenen Zeichenfolge. Ohne Angabe werden keine führenden Nullen hinzugefügt"}, "CEILING": {"a": "(<PERSON><PERSON>; <PERSON>)", "d": "Rundet eine Zahl betragsmäßig auf das kleinste Vielfache von <PERSON>hr<PERSON> auf", "ad": "ist der Wert, den Si<PERSON> runden möchten!ist der Wert, auf dessen Vielfaches Sie runden möchten"}, "CEILING.MATH": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>]; [<PERSON><PERSON>])", "d": "Rundet eine Zahl betragsmäßig auf das kleinste Vielfache von <PERSON>hr<PERSON> auf", "ad": "ist der Wert, den Sie runden möchten!ist der Wert, auf dessen Vielfaches Sie runden möchten!sofern angegeben und ungleich Null rundet diese Funktion von <PERSON>ull weg"}, "CEILING.PRECISE": {"a": "(<PERSON><PERSON>;[<PERSON><PERSON><PERSON>])", "d": "G<PERSON>t eine Zahl zurück, die auf die nächste Ganzzahl oder auf das kleinste Vielfache von „Schritt“ gerundet wurde", "ad": "ist der Wert, den Si<PERSON> runden möchten!ist der Wert, auf dessen Vielfaches Sie runden möchten"}, "COMBIN": {"a": "(n; k)", "d": "Gibt die Anzahl der Kombinationen ohne Wiederholung von k Elementen aus einer Menge von n Elementen zurück", "ad": "ist die Anzahl aller Elemente!gibt an, aus wie vielen Elementen jede Kombinationsmöglichkeit bestehen soll"}, "COMBINA": {"a": "(Zahl; gewählte_Zahl)", "d": "Gibt die Anzahl der Kombinationen mit Wiederholung für eine angegebene Anzahl von Elementen zurück", "ad": "ist die Gesamtzahl der Elemente!ist die Anzahl der Elemente in jeder Kombination"}, "COS": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Kosinus einer Zahl zurück", "ad": "ist ein im Bogenmaß (Radiant) g<PERSON><PERSON><PERSON>, dessen Ko<PERSON>us Si<PERSON> berechnen möchten"}, "COSH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Kosinus einer Zahl zurück. ", "ad": "ist eine reelle Zahl"}, "COT": {"a": "(<PERSON><PERSON>)", "d": "Gibt den Kotangens eines Winkels zurück", "ad": "ist der im Bogenmaß angegebene Win<PERSON>, dessen Kotangens Sie berechnen möchten"}, "COTH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Kotangens einer Zahl zurück", "ad": "ist der im Bogenmaß angegebene <PERSON>, dessen hyperbolischen Kotangens Sie berechnen möchten"}, "CSC": {"a": "(<PERSON><PERSON>)", "d": "Gibt den Kosekans eines Winkels zurück", "ad": "ist der im Bogenmaß angegebene <PERSON>, dessen Kosekans Si<PERSON> berechnen möchten"}, "CSCH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Kosekans eines Winkels zurück", "ad": "ist der im Bogenmaß angegebene <PERSON>, dessen hyperbolischen Kosekans Si<PERSON> berechnen möchten"}, "DECIMAL": {"a": "(<PERSON><PERSON>; <PERSON>)", "d": "Konvertiert eine Textdarstellung einer Zahl mit einer angegebenen Basis in eine Dezimalzahl", "ad": "ist die zu konvertierende Zahl!ist die Basis der zu konvertierenden Zahl"}, "DEGREES": {"a": "(<PERSON><PERSON>)", "d": "Wandelt Bogenmaß (Radiant) in Grad um", "ad": "ist ein in Bogenmaß (Radiant) g<PERSON>bener Winkel, den Si<PERSON> umwandeln möchten"}, "ECMA.CEILING": {"a": "(<PERSON><PERSON>;<PERSON>)", "d": "Rundet eine Zahl auf die nächste Ganzzahl oder auf das kleinste Vielfache des angegebenen Schritts", "ad": "ist der Wert, den Si<PERSON> runden möchten!ist der Wert, auf dessen Vielfaches Sie runden möchten"}, "EVEN": {"a": "(<PERSON><PERSON>)", "d": "Rundet eine positive Zahl auf die nächste gerade ganze Zahl auf und eine negative Zahl auf die nächste gerade ganze Zahl ab", "ad": "ist der Wert, den Si<PERSON> runden möchten"}, "EXP": {"a": "(<PERSON><PERSON>)", "d": "Potenziert die Basis e mit der als Argument angegebenen Zahl", "ad": "ist der Exponent zur Basis e. Die Konstante e entspricht 2.71828182845904, der Basis der natürlichen Logarithmen"}, "FACT": {"a": "(<PERSON><PERSON>)", "d": "Gibt die Fakultät einer Zahl zurück (Fakultät n = 1*2*3...*n)", "ad": "ist eine nicht negative <PERSON>ahl, deren Fakultät Sie berechnen wollen"}, "FACTDOUBLE": {"a": "(<PERSON><PERSON>)", "d": "Gibt die Fakultät zu Zahl mit Schrittlänge 2 zurück", "ad": "ist der Wert, für den die Fakultät mit Schrittlänge 2 berechnet werden soll"}, "FLOOR": {"a": "(<PERSON><PERSON>; <PERSON>)", "d": "Rundet eine Zahl auf das nächstliegende Vielfache von Schritt ab", "ad": "ist der Wert, den Si<PERSON> runden möchten!ist das Vielfache, auf das Si<PERSON> runden möchten. Die zu rundende Zahl und das Vielfachen müssen beide entweder positiv oder negativ sein"}, "FLOOR.PRECISE": {"a": "(<PERSON><PERSON>;[<PERSON><PERSON><PERSON>])", "d": "G<PERSON>t eine Zahl zurück, die auf die nächste Ganzzahl oder auf das kleinste Vielfache von „Schritt“ gerundet wurde", "ad": "ist der Wert, den Si<PERSON> runden möchten!ist der Wert, auf dessen Vielfaches Sie runden möchten"}, "FLOOR.MATH": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>]; [<PERSON><PERSON>])", "d": "Rundet eine Zahl betragsmäßig auf das kleinste Vielfache von <PERSON>hr<PERSON> ab", "ad": "ist der Wert, den Si<PERSON> runden möchten!ist der Wert, auf dessen Vielfaches Sie runden möchten!sofern angegeben und ungleich Null rundet diese Funktion Richtung Null"}, "GCD": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt den größten gemeinsamen Teiler zurück", "ad": "sind 1 bis 255 Werte"}, "INT": {"a": "(<PERSON><PERSON>)", "d": "Rundet eine Zahl auf die nächstkleinere ganze Zahl ab", "ad": "ist die reelle Zahl, die Si<PERSON> zur nächsten ganzen Zahl abrunden möchten"}, "ISO.CEILING": {"a": "(<PERSON><PERSON>; <PERSON>)", "d": "Gibt eine Zahl zurück, die auf die nächste Ganzzahl oder auf das kleinste Vielfache von „Schritt“ gerundet wurde. Die Zahl wird unabhängig von ihrem Vorzeichen aufgerundet. Ist „Zahl“ oder „Schritt“ gleich 0, wird 0 zurückgegeben.", "ad": "ist der Wert, den Si<PERSON> runden möchten!ist der Wert, auf dessen Vielfaches Sie runden möchten"}, "LCM": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt das kleinste gemeinsame Vielfache zurück", "ad": "sind 1 bis 255 Werte, für die Sie das kleinste gemeinsame Vielfache berechnen möchten"}, "LN": {"a": "(<PERSON><PERSON>)", "d": "Gibt den natürlichen Logarithmus einer Zahl zurück", "ad": "ist die positive reelle Zahl, deren natürlichen Logarithmus Sie berechnen möchten"}, "LOG": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON>])", "d": "Gibt den Logarithmus einer Zahl zu der angegebenen Basis zurück", "ad": "ist die positive reelle Zahl, deren Logarithmus Sie berechnen möchten!ist die Basis des Logarithmus. Wenn der Parameter fehlt, wird 10 angenommen"}, "LOG10": {"a": "(<PERSON><PERSON>)", "d": "Gibt den Logarithmus einer Zahl zur Basis 10 zurück", "ad": "ist die positive reelle Zahl, deren Logarithmus zur Basis 10 Sie berechnen möchten"}, "MDETERM": {"a": "(Matrix)", "d": "Gibt die Determinante einer Matrix zurück", "ad": "ist eine quadratische Matrix (die Anzahl der Zeilen und Spalten ist identisch)"}, "MINVERSE": {"a": "(Matrix)", "d": "Gibt die Inverse einer Matrix (die zu einer Matrix gehörende Kehrmatrix) zurück", "ad": "ist eine quadratische Matrix (die Anzahl der Zeilen und Spalten ist identisch)"}, "MMULT": {"a": "(Array1; Array2)", "d": "Gibt das Produkt von zwei Arrays zurück, ein Array mit der gleichen Anzahl von Zeilen wie Array1 und Spalten wie Array2", "ad": "ist das erste Array zu multiplizierender Zahlen und muss die gleiche Anzahl von Spalten aufweisen, wie Array2 Zeilen hat"}, "MOD": {"a": "(<PERSON><PERSON>; Divisor)", "d": "Gibt den Rest einer Division zurück", "ad": "ist die Zahl, deren Rest aus einer Division Sie wissen möchten!ist die Zahl, durch die Zahl dividiert werden soll"}, "MROUND": {"a": "(Zahl; Vielfaches)", "d": "Gibt eine auf das gewünschte Vielfache gerundete Zahl", "ad": "ist die zu rundende Zahl zurück!ist der Wert auf dessen Vielfaches Sie Zahl aufrunden möchten"}, "MULTINOMIAL": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt den Polynomialkoeffizienten einer Gruppe von <PERSON>en", "ad": "sind 1 bis 255 Werte zurück, für die Sie den Polynomialkoeffizienten berechnen möchten"}, "MUNIT": {"a": "(Größe)", "d": "Gibt die Einheitsmatrix für die angegebene Größe zurück", "ad": "ist eine ganze Zahl zur Angabe der Größe der Einheitsmatrix, die zurückgegeben werden soll"}, "ODD": {"a": "(<PERSON><PERSON>)", "d": "Rundet eine positive Zahl auf die nächste ungerade ganze Zahl auf und eine negative Zahl auf die nächste ungerade genaue Zahl ab", "ad": "ist der Wert, den Si<PERSON> runden möchten"}, "PI": {"a": "()", "d": "<PERSON><PERSON><PERSON> den Wert PI, 3.14159265358979, mit 15 Stellen Genauigkeit zurück", "ad": ""}, "POWER": {"a": "(<PERSON><PERSON>; <PERSON>z)", "d": "Gibt als Ergebnis eine potenzierte Zahl zurück", "ad": "ist die Zahl, die Sie mit dem Exponenten potenzieren möchten!ist der Exponent, mit dem Sie die Zahl potenzieren möchten"}, "PRODUCT": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Multipliziert alle als Argumente angegebenen Zahlen", "ad": "sind 1 bis 255 <PERSON>, logische Werte oder Textdarstellungen von <PERSON>, die Sie multiplizieren möchten"}, "QUOTIENT": {"a": "(<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>)", "d": "Gibt den ganzzahligen Anteil einer Division zurück", "ad": "ist der Dividend!ist der Divisor"}, "RADIANS": {"a": "(<PERSON><PERSON>)", "d": "Wandelt Grad in Bogenmaß (Radiant) um", "ad": "ist ein in Grad gegebener Winkel, den Si<PERSON> umwandeln möchten"}, "RAND": {"a": "()", "d": "Gibt eine Zufallszahl gleichmässig zwischen 0 und 1 verteilt zurück. Das Ergebnis ändert sich bei jeder Neuberechnung", "ad": ""}, "RANDARRAY": {"a": "([<PERSON><PERSON><PERSON>]; [<PERSON><PERSON>]; [min]; [max]; [gan<PERSON>_<PERSON>])", "d": "G<PERSON><PERSON> ein A<PERSON><PERSON> von Zufallszahlen zurück.", "ad": "die Anzahl der Zeilen im zurückgegebenen Array.!die Anzahl der Spalten im zurückgegebenen Array.!die kleinste Zahl, die zurückgegeben werden soll.!die größte Zahl, die zurückgegeben werden soll.!ganze Zahl oder Dezimalwert zurückgeben. WAHR für eine ganze <PERSON>ahl, FALSCH für eine Dezimalzahl."}, "RANDBETWEEN": {"a": "(<PERSON><PERSON><PERSON>_<PERSON>; <PERSON><PERSON><PERSON>_<PERSON>ahl)", "d": "Gibt eine ganze Zufallszahl aus dem festgelegten Bereich zurück", "ad": "ist die kleinste ganze <PERSON>, die ZUFALLSBEREICH zurückgebenn kann!ist die größte ganze <PERSON>, die ZUFALLSBEREICH zurückgeben kann"}, "ROMAN": {"a": "(<PERSON><PERSON>; [Typ])", "d": "Wandelt eine arabische Zahl in eine römische Zahl als Text um", "ad": "ist die arabische Zahl, die <PERSON> umwandeln wollen!ist eine Zahl, die den Typ der römischen Zahl festlegt"}, "ROUND": {"a": "(<PERSON><PERSON>; <PERSON><PERSON><PERSON>_<PERSON>)", "d": "Rundet eine Zahl auf eine bestimmte Anzahl an Dezimalstellen", "ad": "ist die Zahl, die Sie auf- oder abrunden möchten!gibt an, auf wie viele Dezimalstellen Sie die Zahl auf- oder abrunden möchten. Negative Werte runden auf ganze Zehnerpotenzen: RUNDEN(225;-2) ergibt 200. 0 rundet auf die nächste Ganzzahl"}, "ROUNDDOWN": {"a": "(<PERSON><PERSON>; <PERSON><PERSON><PERSON>_<PERSON>)", "d": "Rundet die Zahl auf Anzahl_Stellen ab", "ad": "ist eine reelle Zahl, die Si<PERSON> abrunden wollen!legt die Anzahl der Dezimalstellen fest, auf die Sie die Zahl abrunden wollen. Negative Werte runden auf ganze Zehnerpotenzen: RUNDEN(225;-2) ergibt 200. 0 rundet auf die nächste Ganzzahl"}, "ROUNDUP": {"a": "(<PERSON><PERSON>; <PERSON><PERSON><PERSON>_<PERSON>)", "d": "Rundet die Zahl auf Anzahl_Stellen auf", "ad": "ist eine reelle Zahl, die Sie aufrunden wollen!legt die Anzahl der Dezimalstellen fest, auf die Sie die Zahl aufrunden wollen"}, "SEC": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Sekans eines Winkels zurück", "ad": "ist der im Bogenmaß angegebene <PERSON>, dessen Sekans Si<PERSON> berechnen möchten"}, "SECH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Sekans eines Winkels zurück", "ad": "ist der im Bogenmaß angegebene <PERSON>, dessen hyperbolischen Sekans Si<PERSON> berechnen möchten"}, "SERIESSUM": {"a": "(x; n; m; Koeffizienten)", "d": "Gibt die Summe von Potenzen (zur Berechnung von Potenzreihen und dichotomen Wahrscheinlichkeiten) zurück", "ad": "ist der Wert der unabhängigen Variablen der Potenzreihe!ist die Anfangspotenz, in die Sie x erheben möchten!ist das Inkrement, um das Sie n in jedem Glied der Reihe vergrößern möchten!ist eine Gruppe von Koeffizienten, mit denen die aufeinanderfolgenden Potenzen der Variablen x multipliziert werden"}, "SIGN": {"a": "(<PERSON><PERSON>)", "d": "Gibt das Vorzeichen einer Zahl zurück: 1 wenn die Zahl positiv ist, 0 wenn die Zahl 0 ist, -1 wenn die Zahl negativ ist", "ad": "ist eine beliebige reelle Zahl"}, "SIN": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Sinus einer Zahl zurück", "ad": "ist der Winkel im Bogenmaß, für den Sie den Sinus berechnen wollen. Grad * PI()/180 = RAD"}, "SINH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Sinus einer Zahl zurück. (Die verwendete Gleichung ist in der Hilfe genauer beschrieben.)", "ad": "ist eine beliebige reelle Zahl"}, "SQRT": {"a": "(<PERSON><PERSON>)", "d": "Gibt die Quadratwurzel einer Zahl", "ad": "ist die Zahl, deren Quadratwurzel Sie berechnen möchten zurück"}, "SQRTPI": {"a": "(<PERSON><PERSON>)", "d": "Gibt die Wurzel aus der mit Pi multiplizierten Zahl zurück", "ad": "ist die Zahl, die mit Pi multipliziert wird"}, "SUBTOTAL": {"a": "(<PERSON><PERSON>; Bezug1; ...)", "d": "Gibt ein Teilergebnis in einer Liste oder Datenbank zurück", "ad": "ist eine <PERSON>ahl (1 bis 11), die festleg<PERSON>, welche Funktion in der Berechnung des Teilergebnisses verwendet werden soll!ist 1 bis 254 Bereiche oder Bezüge, zu denen Si<PERSON> ein Teilergebnis berechnen möchten"}, "SUM": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Summier<PERSON> die Zahlen in einem Zellenbereich", "ad": "sind 1 bis 255 Zahlen, deren Summe Sie berechnen möchten. Logische Werte und Text werden in <PERSON><PERSON><PERSON> igno<PERSON>, j<PERSON><PERSON>, wenn sie als Argumente eingegeben werden"}, "SUMIF": {"a": "(Bereich; Suchkriterien; [<PERSON><PERSON>_Bereich])", "d": "<PERSON><PERSON><PERSON>, die mit dem Suchkriterium übereinstimmen", "ad": "ist der Zellbereich, den Sie auswerten wollen!ist das Suchkriterium als Zahl, Formel, oder Text, das festlegt, welche Zellen addiert werden!sind die Zellen, die Sie summieren wollen"}, "SUMIFS": {"a": "(<PERSON><PERSON>_<PERSON>; Kriterien_Be<PERSON>ich; Kriterien; ...)", "d": "Addiert die Zellen, die von einer bestimmten Gruppe von Bedingungen oder Kriterien angegeben sind", "ad": "sind die tatsächlich zu addierenden Zellen.!ist der Bereich von <PERSON>n, die für die jeweiligen Bedingungen ausgewertet werden sollen!ist die Bedingung oder sind die Kriterien in Form einer Zahl, eines Ausdrucks oder Texts, die definieren, welche Zellen addiert werden"}, "SUMPRODUCT": {"a": "(Array1; [Array2]; [Array3]; ...)", "d": "Gibt die Summe der Produkte der entsprechenden Bereiche und Arrays zurück", "ad": "sind 2 bis 255 Arrays, deren Komponenten Sie zunächst multiplizieren und anschließend addieren möchten"}, "SUMSQ": {"a": "(<PERSON>ahl1; [<PERSON>ahl2]; ...)", "d": "Gibt die Summe der quadrierten Argumente zurück. Die Argumente können Zahlen, <PERSON><PERSON><PERSON>, <PERSON>n oder Bezüge auf Z<PERSON>n sein, die Zahlen enthalten", "ad": "sind 1 bis 255 <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>n oder Bezüge auf Arrays, deren Summe der Quadrate Sie berechnen möchten"}, "SUMX2MY2": {"a": "(Matrix_x; Matrix_y)", "d": "Summiert für zusammengehörige Komponenten zweier Matrizen die Differenzen der Quadrate", "ad": "ist die erste Matrix oder der erste Wertebereich, Argumente können Zahlen, <PERSON><PERSON>, Arrays oder Bezüge sein!ist die zweite Matrix oder der zweite Wertebereich, Argumente können Zahlen, <PERSON><PERSON>, Arrays oder Bezüge sein"}, "SUMX2PY2": {"a": "(Matrix_x; Matrix_y)", "d": "Summiert für zusammengehörige Komponenten zweier Matrizen die Summen der Quadrate", "ad": "ist die erste Matrix oder der erste Wertebereich, Argumente können Zahlen, <PERSON><PERSON>, Arrays oder Bezüge sein!ist die zweite Matrix oder der zweite Wertebereich, Argumente können Zahlen, <PERSON><PERSON>, Arrays oder Bezüge sein"}, "SUMXMY2": {"a": "(Matrix_x; Matrix_y)", "d": "Summiert für zusammengehörige Komponenten zweier Matrizen die quadrierten Differenzen.", "ad": "ist die erste Matrix oder der erste Wertebereich, Argumente können Zahlen, <PERSON><PERSON>, Arrays oder Bezüge sein!ist die zweite Matrix oder der zweite Wertebereich, Argumente können Zahlen, <PERSON><PERSON>, Arrays oder Bezüge sein"}, "TAN": {"a": "(<PERSON><PERSON>)", "d": "G<PERSON>t den Tangens einer Zahl zurück", "ad": "ist der Winkel im Bogenmaß, für den Sie den Tangens ermitteln wollen. Grad * PI()/180 = RAD"}, "TANH": {"a": "(<PERSON><PERSON>)", "d": "Gibt den hyperbolischen Tangens einer Zahl zurück.", "ad": "ist eine reelle Zahl"}, "TRUNC": {"a": "(<PERSON><PERSON>; [<PERSON><PERSON><PERSON>_<PERSON>])", "d": "Schneidet die Kommastellen der Zahl ab und gibt als Ergebnis eine ganze Zahl zurück", "ad": "ist die Zahl, deren Stellen Si<PERSON> abschneiden wollen!ist eine <PERSON>ahl, die angibt, wie viele Nachkommastellen erhalten bleiben sollen (0, wenn ausgelassen)"}, "ADDRESS": {"a": "(<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; [Abs]; [A1]; [Tabellenname])", "d": "Gibt einen Verweis auf eine Zelle einer Tabelle als Text zurück", "ad": "ist die Zeilennummer, die für den Zellbezug verwendet werden soll!ist die Spaltennummer, die für den Zellbezug verwendet werden soll!gibt an, welcher Bezugstyp zurückgegeben werden soll: absolut = 1; absolute Zeile/relative Spalte = 2; relative Zeile/absolute Spalte = 3; relative = 4!ist ein Wahrheitswert, der angibt, ob Bezüge in der A1- oder Z1S1-Schreibweise ausgegeben werden sollen: 1 oder WAHR = A1-Bezug; 0 oder FALSCH = Z1S1-Bezug!ist der Text, der den Tabellennamen angibt, der als externer Bezug verwendet werden soll"}, "CHOOSE": {"a": "(Index; Wert1; [Wert2]; ...)", "d": "W<PERSON><PERSON>t einen Wert aus einer Liste von Werten", "ad": "gibt an, welcher Argumentwert gewählt ist. Der Wert muss zwischen 1 und 254 liegen, oder eine Formel oder ein Bezug zu einem Wert von 1 bis 254 sein!sind 1 bis 254 Argumente, aus denen WAHL je nach angegebenem Index einen Wert oder eine Anweisung wählt"}, "COLUMN": {"a": "([Bezug])", "d": "Gibt die Spaltennummer eines Bezuges zurück", "ad": "ist die Zelle oder der Zellbereich, deren bzw. dessen Spaltennummer Si<PERSON> ermitteln möchten. Wenn der Parameter fehlt, wird die Zelle zurückgegeben, die die Funktion enthält"}, "COLUMNS": {"a": "(Matrix)", "d": "Gibt die Anzahl der Spalten eines Bezuges zurück", "ad": "ist eine Matrix, eine Matrixformel oder ein Bezug auf einen Zellbereich, dessen Spaltenanzahl Sie abfragen möchten"}, "FORMULATEXT": {"a": "(Bezug)", "d": "Gibt eine Formel als Zeichenfolge zurück", "ad": "ist ein Bezug auf eine Formel"}, "HLOOKUP": {"a": "(Suchkriterium; Matrix; Zeilenindex; [<PERSON><PERSON>ich_Verweis])", "d": "Durchsucht die erste Zeile einer Matrix und durchläuft die Spalte nach unten, um den Wert einer Zelle zurückzugeben", "ad": "ist der Wert, nach dem Si<PERSON> in der ersten Zeile der Matrix suchen. Suchkriterium kann ein Wert, ein Bezug oder eine Zeichenfolge in Anführungszeichen sein!ist eine Informationstabelle, in der Daten gesucht werden!ist die Nummer der Zeile in der Mehrfachoperationsmatrix, aus der der übereinstimmende Wert zurückgegeben werden soll!ist ein logischer Wert: WAHR oder k. A. = in der obersten Zeile wird nach einer möglichst genauen Übereinstimmung gesucht (aufsteigend sortiert); FALSCH = es wird eine genaue Übereinstimmung gesucht"}, "HYPERLINK": {"a": "(Hyperlink_Adresse; [Freundlicher_Name])", "d": "Erstellt eine Verknüpfung, die zur Hyperlink_Adresse verbindet", "ad": "ist der gesamte Pfad und Dateiname, UNC-Pfad oder Internet-URL zu dem Dokument, dass beim Klicken auf die Zelle geöffnet wird!ist die Zahl, Text oder Funktion, die in der Zelle angezeigt werden soll"}, "INDEX": {"a": "(<PERSON>; <PERSON><PERSON><PERSON>; [<PERSON><PERSON><PERSON>]!<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; [<PERSON><PERSON><PERSON>]; [<PERSON><PERSON><PERSON>])", "d": "Verwendet einen Index, um aus einem Bezug oder einer Matrix einen Wert zu wählen", "ad": "ist ein als Matrix eingegebener Zellbereich!markiert die Zeile in der Matrix, aus der ein Wert zurückgegeben werden soll!markiert die Spalte in der Matrix, aus der ein Wert zurückgegeben werden soll!ist der Bezug auf einen oder mehrere Zellbereiche!ist die Nummer der Zeile im Bereich Bezug, aus dem der Bezug zurückgegeben werden soll!ist die Nummer der Spalte im Bereich Bezug, aus dem der Bezug zurückgegeben werden soll!bestimmt den Zellbereich im Bezug, dessen Schnittpunkt von Zeile und Spalte zurückgegeben werden soll"}, "INDIRECT": {"a": "(<PERSON>zug; [A1])", "d": "G<PERSON>t den Bezug eines Textwertes zurück", "ad": "ist der Bezug auf eine Zelle, die einen Bezug in der A1-Schreibweise, einen Bezug in der Z1S1-Schreibweise oder einen definierten Namen als Bezug enthält!ist ein Wahrheitswert, der angibt, welche <PERSON> von Bezug in der Zelle enthalten ist: FALSCH = Z1S1-Bezüge; WAHR oder fehlt = A1-Bezüge"}, "LOOKUP": {"a": "(Suchkriterium; Suchvektor; [Ergebnisvektor]!Suchkriterium; Matrix)", "d": "Durchsucht die Werte eines Vektors oder einer Matrix", "ad": "ist ein Wert, nach dem VERWEIS im ersten Vektor sucht. Wird für Abwärtskompatibilität unterstützt.!ist ein Bereich, der nur eine Zeile oder Spalte enthält!ist ein Bereich, der nur eine Zeile oder Spalte enthält!ist ein Wert, nach dem VERWEIS in der Matrix sucht!ist ein Zellbereich, der entweder Text, Zahlen oder Wahrheitswerte enthält, die Sie mit den Suchkriterien vergleichen möchten"}, "MATCH": {"a": "(Suchkriterium; Suchmatrix; [Vergleichstyp])", "d": "Sucht Werte innerhalb eines Bezuges oder einer Matrix", "ad": "ist der Wert, aufgrund dessen der gewünschte Wert in einer Tabelle gesucht wird!ist ein zusammenhängender Zellbereich mit möglichen Vergleichskriterien!ist die Zahl 1, 0 oder -1, die angibt, welcher Wert zurückgegeben werden soll."}, "OFFSET": {"a": "(Bezug; Zeilen; Spalten; [<PERSON><PERSON><PERSON>]; [<PERSON><PERSON><PERSON>])", "d": "Gibt einen Bezug zurück, der gegenüber dem angegebenen Bezug versetzt ist", "ad": "ist der Bezug, der als Ausgangspunkt des Verschiebevorgangs dienen soll!ist die Anzahl der Zeilen, um die Sie die obere linke Eckzelle des Bereiches nach oben oder nach unten verschieben wollen!ist die Anzahl der Spalten, um die Sie die obere linke Eckzelle des Bereiches nach links oder nach rechts verschieben wollen!ist die Höhe des neuen Bezuges in Zeilen. Wenn der Parameter fehlt, wird die selbe Höhe wie beim Ursprungsbezug angenommen!ist die Breite des neuen Bezuges in Spalten. Wenn der Parameter fehlt, wird die selbe Breite wie beim Ursprungsbezug angenommen"}, "ROW": {"a": "([Bezug])", "d": "Gibt die Zeilennummer eines Bezuges zurück", "ad": "ist die Zelle oder der Zellbereich, deren bzw. dessen Zeilennummer Si<PERSON> ermitteln möchten. Wenn der Parameter fehlt, wird die Zelle zurückgegeben, die die Funktion enthält"}, "ROWS": {"a": "(Matrix)", "d": "Gibt die Anzahl der Zeilen eines Bezuges zurück", "ad": "ist eine Matrix, eine Matrixformel oder ein Bezug auf einen Zellbereich, dessen Zeilenanzahl Si<PERSON> abfragen möchten"}, "TRANSPOSE": {"a": "(Matrix)", "d": "Gibt die transponierte Matrix der angegebenen Matrix zurück", "ad": "ist eine Matrix in einem Arbeitsblatt oder einer Makrovorlage, die Sie transponieren möchten"}, "UNIQUE": {"a": "(<PERSON>; [nach_<PERSON>lte]; [gena<PERSON>_e<PERSON><PERSON>])", "d": "Gibt die eindeutigen Werte eines Bereichs oder Arrays zurück.", "ad": "Der Bereich oder das Array, aus dem eindeutige Zeilen oder Spalten zurückgegeben werden sollen.!Ist ein logischer Wert: Vergleicht Zeilen miteinander, und gibt die eindeutigen Zeilen zurück = FALSCH oder ausgelassen. Vergleicht Spalten miteinander und gibt die eindeutigen Spalten zurück = WAHR.!Ist ein logischer Wert: Gibt Zeilen oder Spalten, die genau einmal vorkommen, aus dem Array zurück = WAHR. Gibt alle eindeutigen Zeilen oder Spalten aus dem Array zurück = FALSCH oder ausgelassen."}, "VLOOKUP": {"a": "(Suchkriterium; Matrix; Spaltenindex; [Bereich_Verweis])", "d": "Durchsucht die erste Spalte einer Matrix und durchläuft die Zeile nach rechts, um den Wert einer Zelle zurückzugeben", "ad": "ist der Wert, nach dem Si<PERSON> in der ersten Spalte der Matrix suchen!ist die Informationstabelle, in der Daten gesucht werden!ist die Nummer der Spalte in der Mehrfachoperationsmatrix, aus der der übereinstimmende Wert  zurückgegeben werden soll!ist ein logischer Wert: WAHR oder k. A. = in der ersten Spalte wird nach einer möglichst genauen Übereinstimmung gesucht (aufsteigend sortiert); FALSCH = es wird eine genaue Übereinstimmung gesucht"}, "XLOOKUP": {"a": "(Suchkriterium; Suchmatrix; Rückgabematrix; [wenn_nicht_gefunden]; [Vergleichsmodus]; [Suchmodus])", "d": "Durchsu<PERSON> einen Bereich oder eine Matrix auf eine Übereinstimmung und gibt das entsprechende Element aus einem zweiten Bereich oder einer zweiten Matrix zurück. Standardmäßig wird eine exakte Übereinstimmung verwendet.", "ad": "ist der zu suchende Wert.!ist die Matrix oder der Bereich, die/der durchsucht werden soll.!ist die Matrix oder der Bereich, die/der zurückgegeben werden soll.!wird zurückgegeben, wenn keine Übereinstimmung gefunden wird.!gibt an, wie das Suchkriterium mit den Werten in der Suchmatrix verglichen werden soll.!gibt den zu verwendenden Suchmodus an. Standardmäßig wird eine Suche vom ersten zum letzten Element verwendet."}, "CELL": {"a": "(info_type; [reference])", "d": "Werden Informationen zur Formatierung, zur Position oder zum Inhalt einer Zelle zurückgegeben", "ad": "ein <PERSON>, der angibt, welcher <PERSON><PERSON> von Zellinformationen zurückgegeben werden soll!die Zelle, zu der Sie Informationen wünschen"}, "ERROR.TYPE": {"a": "(Fehlerwert)", "d": "Gibt eine Zahl entsprechend dem vorliegenden Fehlerwert zurück.", "ad": "ist der Fehlerwert, dessen Kennummer Sie finden möchten"}, "ISBLANK": {"a": "(Wert)", "d": "Gibt WAHR zurück, wenn der Wert eine leere Zelle ist", "ad": "ist der Wert, der geprüft werden soll"}, "ISERR": {"a": "(Wert)", "d": "<PERSON><PERSON><PERSON><PERSON>, ob ein Wert ein anderer Fehler als #NV ist, und gibt WAHR oder FALSCH zurück", "ad": "ist der Wert, der geprüft werden soll. Der Wert kann eine Zelle sein, eine Formel oder ein Name, der sich auf eine Zelle, Formel oder einen Wert bezieht"}, "ISERROR": {"a": "(Wert)", "d": "<PERSON><PERSON><PERSON><PERSON>, ob ein Wert ein Fehler ist, und gibt WAHR oder FALSCH zurück", "ad": "ist der Wert, der geprüft werden soll. Der Wert kann eine Zelle sein, eine Formel oder ein Name, der sich auf eine Zelle, Formel oder einen Wert bezieht"}, "ISEVEN": {"a": "(<PERSON><PERSON>)", "d": "Gibt WAHR zurück, wenn es sich um eine gerade Zahl handelt", "ad": "ist der zu prüfende Wert"}, "ISFORMULA": {"a": "(Bezug)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ob ein Bezug auf eine Zelle verweist, die eine Formel enthält, und gibt WAHR oder FALSCH zurück", "ad": "ist ein Bezug auf die zu prüfende Zelle. Der Bezug kann ein Zellbezug, eine Formel oder ein Name, der auf eine Zelle verweist, sein"}, "ISLOGICAL": {"a": "(Wert)", "d": "Gibt WAHR zurück, wenn der Wert ein Wahrheitswert ist", "ad": "ist der Wert, der geprüft werden soll, eine <PERSON>, eine Formel oder ein Name, der sich auf eine <PERSON>, eine Formel oder einen Wert bezieht"}, "ISNA": {"a": "(Wert)", "d": "<PERSON><PERSON><PERSON><PERSON>, ob ein Wert #NV ist und gibt WAHR oder FALSCH zurück", "ad": "ist der Wert, der geprüft werden soll. Der Wert kann eine Zelle sein, eine Formel oder eine Name, der sich auf eine Zelle, Formel oder einen Wert bezieht"}, "ISNONTEXT": {"a": "(Wert)", "d": "Gibt WAHR zurück, wenn der Wert ein Element ist, das keinen Text enthält", "ad": "ist der Wert, der geprüft werden soll, eine <PERSON>, eine Formel oder ein Name, der ich auf eine Zelle, eine Formel oder einen Wert bezieht"}, "ISNUMBER": {"a": "(Wert)", "d": "Gibt WAHR zurück, wenn der Wert eine Zahl ist", "ad": "ist der Wert, der geprüft werden soll"}, "ISODD": {"a": "(<PERSON><PERSON>)", "d": "Gibt WAHR zurück, wenn es sich um eine ungerade Zahl handelt", "ad": "ist der zu prüfende Wert"}, "ISREF": {"a": "(Wert)", "d": "Gibt WAHR zurück, wenn der Wert ein Bezug ist", "ad": "ist der Wert, der geprüft werden soll"}, "ISTEXT": {"a": "(Wert)", "d": "<PERSON><PERSON><PERSON><PERSON>, ob ein Wert ein Text ist und gibt WAHR zurück, wenn dies zutrifft, bzw. FALSCH, wenn dies nicht zutrifft.", "ad": "ist der Wert, den Sie überprüfen wollen. Bei dem Wert kann es sich um eine Zelle, eine Formel oder einen Namen handeln."}, "N": {"a": "(Wert)", "d": "Wandelt einen nicht-numerischen Wert in eine Zahl, ein <PERSON> in eine serielle Zahl, und WAHR in die Zahl 1 um. Alle anderen Werte werden in die Zahl 0 umgewandelt", "ad": "ist der Wert, den Si<PERSON> in eine Zahl umwandeln möchten"}, "NA": {"a": "()", "d": "Gibt den Fehlerwert #NV (Wert nicht verfügbar) zurück", "ad": ""}, "SHEET": {"a": "([Wert])", "d": "Gibt die Blattnummer des Blatts zurück, auf das verwiesen wird", "ad": "ist der Name eines Blatts oder Bezugs, dessen Blattnummer zurückgegeben werden soll. Ohne Parameter wird die Nummer des Blatts, das die Funktion enthält, zurückgegeben"}, "SHEETS": {"a": "([Bezug])", "d": "Gibt die Anzahl der Blätter in einem Bezug zurück", "ad": "ist ein Bezug, für den die Anzahl der enthaltenen Blätter zurückgegeben werden soll. Ohne Parameter wird die Anzahl der Blätter in der Arbeitsmappe, die die Funktion enthält, zurückgegeben"}, "TYPE": {"a": "(Wert)", "d": "Gibt eine ganze Zahl zurück, die den Datentyp des angegebenen Wertes darstellt: Zahl = 1; Text = 2; Logischer Wert = 4; Fehlerwert = 16; Matrix = 64; Zusammengesetzte Daten = 128", "ad": "kann ein beliebiger Wert sein."}, "AND": {"a": "(Wahrheitswert1; [Wahrheitswert2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, ob alle Argumente WAHR sind; gibt WAHR zurück, wenn alle Argumente WAHR sind", "ad": "sind 1 bis 255 Bedingungen, die Sie überprüfen möchten und die jeweils entweder WAHR oder FALSCH sein können. Übergeben werden können logische Werte, Arrays oder Bezüge"}, "FALSE": {"a": "()", "d": "Gibt den Wahrheitswert FALSCH zurück", "ad": ""}, "IF": {"a": "(Wahrheitstest; [Wert_wenn_wahr]; [Wert_wenn_falsch])", "d": "Gibt einen Wahrheitstest an, der durchgeführt werden soll", "ad": "ist ein beliebiger Wert oder Ausdruck, der WAHR oder FALSCH sein kann!ist das Resultat der Funktion, wenn der Wahrheitstest WAHR ergibt. Wenn der Parameter nicht angegeben wird, wird WAHR zurückgegeben.!ist das Resultat der Funktion, wenn der Wahrheitstest FALSCH ergibt. Wenn der Parameter nicht angegeben wird, wird FALSCH zurückgegeben"}, "IFS": {"a": "(Wahrheitstest; Wert_wenn_wahr; ...)", "d": "<PERSON>ber<PERSON><PERSON><PERSON><PERSON>, ob mindestens eine Bedingung erfüllt ist, und gibt einen Wert entsprechend der ersten erfüllten Bedingung (WAHR) zurück.", "ad": "ist ein beliebiger Wert oder Ausdruck, der zu WAHR oder FALSCH ausgewertet werden kann.!ist der zurückgegebene Wert, wenn Wahrheitstest WAHR ist."}, "IFERROR": {"a": "(Wert; Wert_falls_<PERSON>hler)", "d": "Gibt einen Wert_falls_<PERSON>hler aus, falls es sich bei dem Ausdruck um einen Fehler handelt, und anderenfalls den Wert des Ausdrucks selbst", "ad": "ist jeder Wert oder Audruck oder Bezug!ist jeder Wert oder Ausdruck oder Bezug"}, "IFNA": {"a": "(Wert; Wert_bei_NV)", "d": "Gibt den Wert zurück, den Si<PERSON> angeben, wenn der Ausdruck zu \"#NV\" ausgewertet wird, gibt andern<PERSON> das Ergebnis des Ausdrucks zurück", "ad": "ist ein beliebiger Wert, Ausdruck oder Bezug!ist ein beliebiger Wert, Ausdruck oder Bezug"}, "NOT": {"a": "(Wahrheitswert)", "d": "Kehrt den Wert ihres Argumentes um", "ad": "ist ein Wert oder Ausdruck, der WAHR oder FALSCH annehmen kann"}, "OR": {"a": "(Wahrheitswert1; [Wahrheitswert2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, ob eines der Argumente WAHR ist und gibt WAHR oder FALSCH zurück. Nur wenn alle Argumente FALSCH sind, wird FALSCH zurückgegeben", "ad": "sind 1 bis 255 Bedingungen, die Sie überprüfen möchten und die jeweils entweder WAHR oder FALSCH sein können"}, "SWITCH": {"a": "(Ausdruck; Wert1; <PERSON>rgebnis1; [Standard_oder_Wert2]; [Ergebnis2]; ...)", "d": "Wertet einen Ausdruck anhand einer Wertliste aus und gibt das Ergebnis zurück, das dem ersten übereinstimmenden Wert entspricht. Wenn es keine Übereinstimmung gibt, wird ein optionaler Standardwert zurückgegeben", "ad": "ist ein auszuwertender Ausdruck!ist ein mit dem Ausdruck zu vergleichender Wert!ist ein zurückzugebendes Ergebnis, wenn der entsprechende Wert mit dem Ausdruck übereinstimmt"}, "TRUE": {"a": "()", "d": "Gibt den Wahrheitswert WAHR zurück", "ad": ""}, "XOR": {"a": "(Wahrheitswert1; [Wahrheitswert2]; ...)", "d": "Gibt ein logisches \"Ausschließliches Oder\" aller Argumente zurück", "ad": "sind 1 bis 254 zu prüfende Bedingungen, die entweder WAHR oder FALSCH sein können und logische Werte, Matrizen oder Bezüge darstellen können"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Gibt Text zurück, der vor Trennzeichen steht.", "ad": "Der Text, den Sie nach für das Trennzeichen suchen möchten.!Das Zeichen oder die Zeichenfolge, das bzw. die als Trennzeichen verwendet werden soll.!Das gewünschte Vorkommen des Trennzeichens. Der Standardwert ist 1. Eine negative Zahl sucht vom Ende.!Durchsucht den Text nach einer Trennzeichenübereinstimmung. Standardmäßig erfolgt eine Übereinstimmung mit Beachtung der Groß-/Kleinschreibung.!Gibt an, ob das Trennzeichen mit dem Textende übereinstimmt. Standardmäßig werden sie nicht abgeglichen.!Wird zurückgegeben, wenn keine Übereinstimmung gefunden wird. Standardmäßig wird #N/V zurückgegeben."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Gibt Text zurück, der nach Trennzeichen steht.", "ad": "Der Text, in dem Sie nach dem Trennzeichen suchen möchten.!Das Zeichen oder die Zeichenfolge, die als Trennzeichen verwendet werden soll.!Das gewünschte Vorkommen des Trennzeichens. Der Standardwert ist 1. Bei einer negativen Zahl wird am Ende gesucht.!Durchsucht den Text nach einem übereinstimmenden Trennzeichen. Standardmäßig wird zwischen Groß- und Kleinschreibung unterschieden.!Ob das Trennzeichen mit dem Textende abgeglichen werden soll. Standardmäßig werden sie nicht abgeglichen.!Wird zurückgegeben, wenn keine Übereinstimmung gefunden wird. Standardmäßig wird #NV zurückgegeben."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Teilt Text mithil<PERSON> von Trennzeichen in Zeilen oder Spalten auf.", "ad": "Der zu teilende Text!Zeichen oder Zeichenfolge zum Teilen von Spalten.!Zeichen oder Zeichenfolge zum Teilen von Zeilen.!Ignorieren leere Zellen. Der Standardwert ist FALSE.!Durchsucht den Text nach einem übereinstimmenden Trennzeichen. Standardmäßig wird zwischen Groß- und Kleinschreibung unterschieden.!Der zum Auffüllen zu verwendende Wert. Standardmäßig wird #N/A verwendet."}, "WRAPROWS": {"a": "(<PERSON><PERSON><PERSON>, wrap_count, [pad_with])", "d": " Umschließt einen Zeilen- oder Spaltenvektor nach einer angegebenen Anzahl von <PERSON>rten.", "ad": "Der Vektor oder Verweis, der umbrochen werden soll.!Die maximale Anzahl von Werten pro Zeile.!Der Wert, mit dem aufgefüllt werden soll. Der Standardwert ist #N/A."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Stapelt Matrizes vertikal in eine Matrix.", "ad": "Eine Matrix oder ein Verweis, die bzw. der gestapelt werden soll."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Stapelt Matrizes horizontal in eine Matrix.", "ad": "Eine Matrix oder ein Verweis, die bzw. der gestapelt werden soll."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "G<PERSON>t Zeilen aus einer Matrix oder einem Verweis zurück.", "ad": "Die Matrix oder der Verweis, welche bzw. welcher die zurückzugebenden Zeilen enthält.!Die Nummer der zurückzugebenden Zeile."}, "CHOOSECOLS": {"a": "(array, row_num1, [row_num2], ...)", "d": "G<PERSON>t Spalten aus einer Matrix oder einem Verweis zurück.", "ad": "Die Matrix oder der Verweis, welche bzw. welcher die zurückzugebenden Spalten enthält.!Die Nummer der zurückzugebenden Spalte."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Gibt die Matrix als eine Spalte zurück.", "ad": "Das Array oder der Verweis, das bzw. der als eine Spalte zurückgegeben werden soll.!Gib<PERSON> an, ob bestimmte Typen von Werten ignoriert werden sollen. Standardmäßig werden keine Werte ignoriert.!Scannt das Array nach Spalte. Standardmäßig wird das Array zeilenweise gescannt."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "G<PERSON>t das Array als eine Zeile zurück.", "ad": "Das Array oder der Verweis, das bzw. der als Zeile zurückgegeben werden soll.!Gib<PERSON> an, ob bestimmte Typen von Werten ignoriert werden sollen. Standardmäßig werden keine Werte ignoriert.!Scannt das Array nach Spalte. Standardmäßig wird das Array zeilenweise gescannt."}, "WRAPCOLS": {"a": "(<PERSON><PERSON><PERSON>, wrap_count, [pad_with])", "d": " Umschließt einen Zeilen- oder Spaltenvektor nach einer angegebenen Anzahl von <PERSON>rten.", "ad": "Der Vektor oder Verweis, der umbrochen werden soll.!Die maximale Anzahl von Werten pro Spalte.!Der Wert, mit dem aufgefüllt werden soll. Der Standardwert ist #N/A."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Gibt Zeilen oder Spalten vom Anfang oder Ende der Matrix zurück.", "ad": "<PERSON> Array, dem Zeilen oder Spalten entnommen werden sollen.!Die Anzahl der zu verwendenden Zeilen. Mit einem negativen Wert werden Zeilen vom Ende des Arrays entnommen.!Die Anzahl der zu verwendenden Spalten. Mit einem negativen Wert werden Spalten vom Ende des Arrays entnommen."}, "DROP": {"a": "(array, rows, [columns])", "d": "Löscht Zeilen oder Spalten vom Anfang oder Ende der Matrix.", "ad": "Die Matrix, aus der Zeilen oder Spalten gelöscht werden sollen.!Die Anzahl der zu löschenden Zeilen. Ein negativer Wert löscht Zeilen vom Ende der Matrix.!Die Anzahl der zu löschenden Spalten. Ein negativer Wert löscht Spalten vom Ende der Matrix."}, "SEQUENCE": {"a": "(<PERSON><PERSON><PERSON>, [<PERSON><PERSON>], [<PERSON><PERSON><PERSON>], [<PERSON><PERSON><PERSON>])", "d": "Gibt eine Abfolge von Zahlen zurück.", "ad": "die Anzahl der zurückzugebenden Zeilen.!die Anzahl der zurückzugebenden Spalten.!die erste Zahl in der Abfolge.!die Menge, um die jeder nachfolgende Wert in der Abfolge erhöht werden soll."}, "EXPAND": {"a": "(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, [<PERSON><PERSON>], [füllen_mit])", "d": "<PERSON><PERSON><PERSON><PERSON>t eine Matrix auf die angegebenen Dimensionen.", "ad": "Die zu erweiternde Matrix!Die Anzahl der Zeilen in der erweiterten Matrix. Wenn diese fehlt, werden die Zeilen nicht erweitert.!Die Anzahl der Spalten in der erweiterten Matrix. Wenn diese fehlt, werden die Spalten nicht erweitert.!Der Wert, mit dem aufgefüllt werden soll"}, "XMATCH": {"a": "(lookup_value, lookup_array, [match_mode], [search_mode])", "d": "Gibt die relative Position eines Elements in einem Array zurück. Standardmäßig ist eine genaue Übereinstimmung erforderlich", "ad": "ist der Wert, nach dem gesucht werden soll!ist das Array oder der Bereich, nach dem gesucht werden soll!Geben Sie an, wie der lookup_value mit den Werten in lookup_array übereinstimmen soll!Geben Sie den zu verwendenden Suchmodus an. Standardmäßig wird eine erste bis letzte Suche verwendet"}, "FILTER": {"a": "(<PERSON>, e<PERSON><PERSON><PERSON>ßen, [wenn_leer])", "d": "<PERSON>ltert einen Bereich oder eine Matrix.", "ad": "Der zu filternde Bereich bzw. die zu filternde Matrix.!Eine Matrix aus booleschen Werten, wobei WAHR eine zu erhaltende Zeile oder Spalte darstellt.!wird zurückgegeben, wenn keine Elemente erhalten werden."}, "ARRAYTOTEXT": {"a": "(Matrix, [Format])", "d": "Gibt eine Textdarstellung einer Matrix zurück.", "ad": "das als Text darzustellende Array.!das Format des Texts."}, "SORT": {"a": "(Matrix, [Sortierindex], [Sortierreihenfolge], [nach_<PERSON>lte])", "d": "Sortiert einen Bereich oder eine Matrix.", "ad": "der zu sortierende Bereich bzw. die zu sortierende Matrix.!eine <PERSON>, die die Zeilen- oder Spaltennummer angibt, nach der sortiert werden soll.!eine <PERSON>, die die gewünschte Sortierreihenfolge angibt. 1 für aufsteigende Reihenfolge (Standard), -1 für absteigende Reihenfolge.!ein logischer Wert, der die gewünschte Sortierrichtung angibt: FALSCH, um nach Zeilen zu sortieren (Standard), WAHR, um nach Spalten zu sortieren."}, "SORTBY": {"a": "(<PERSON>, nach_Matrix, [Sortierreihenfolge], ...)", "d": "Sortiert einen Bereich oder eine Matrix, basierend auf den Werten in einem entsprechenden Bereich oder einer entsprechenden Matrix.", "ad": "der zu sortierende Bereich bzw. die zu sortierende Matrix.!der Bereich bzw. die Matrix, nach dem/der sortiert werden soll.!eine <PERSON>, die die gewünschte Sortierreihenfolge angibt. 1 für aufsteigende Reihenfolge (Standard), -1 für absteigende Reihenfolge."}, "GETPIVOTDATA": {"a": "(Datenfeld; PivotTable; [Feld]; [Element]; ...)", "d": "<PERSON><PERSON><PERSON> Daten, die in einer PivotTable gespeichert sind", "ad": "ist der Name des Datenfeldes, aus dem Daten extrahiert werden sollen!ist ein Bezug auf eine Zelle oder einen Zellenbereich, in der PivotTable, der die Daten enthält die Sie abrufen möchten!Feld für den Verweis!Feldelement für den Verweis"}, "IMPORTRANGE": {"a": "(URL_<PERSON>_<PERSON>, Bereichszeichenfolge)", "d": "Importiert einen Zellenbereich aus einer angegebenen Tabelle.", "ad": "Die URL der Tabelle, aus der Daten importiert werden!eine Zeichenfolge die den zu importierenden Bereich definiert."}}