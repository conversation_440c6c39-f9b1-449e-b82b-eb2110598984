{"DATE": {"a": "(år; måned; dag)", "d": "Returnerer tallet som svarer til datoen i koden for dato og tid", "ad": "er et tall fra 1900 eller 1904 (avhengig av arbeidsbokens datosystem) til 9999!er et tall fra 1 til 12 som representerer måneden i året!er et tall fra 1 til 31 som representerer dagen i måneden"}, "DATEDIF": {"a": "(startdato; sluttdato; enhet)", "d": "<PERSON><PERSON><PERSON><PERSON> antall <PERSON>, m<PERSON><PERSON><PERSON> eller år mellom to datoer", "ad": "En dato som representerer den første eller første datoen for en gitt periode!En dato som representerer den siste, eller avsluttende, datoen for perioden!Informasjonstypen du vil returnere"}, "DATEVALUE": {"a": "(dato_tekst)", "d": "Konverterer en dato med tekstformat til et tall som representerer datoen i koden for dato og tid", "ad": "er tekst som representerer en dato i et av datoformatene i Spreadsheet Editor, mellom 01.01.1900 eller 01.01.1904 (avhengig av arbeidsbokens datosystem) og 31.12.9999"}, "DAY": {"a": "(serienummer)", "d": "Returnerer en dag i måneden, et tall fra 1 til 31.", "ad": "er et tall som representerer en dato eller et klokkeslett som brukes av Spreadsheet Editor"}, "DAYS": {"a": "(sluttdato; startdato)", "d": "Returnerer antallet dager mellom to datoer.", "ad": "startdato og sluttdato er de to datoene du vil vite antallet dager mellom!startdato og sluttdato er de to datoene du vil vite antallet dager mellom"}, "DAYS360": {"a": "(startdato; sluttdato; [metode])", "d": "<PERSON><PERSON><PERSON><PERSON> antall dager mellom to datoer basert på et år med 360 dager (12 måneder à 30 dager)", "ad": "startdato og sluttdato er de to datoene du vil vite antall dager mellom!startdato og sluttdato er de to datoene du vil vite antall dager mellom!er en logisk verdi som angir beregningsmetoden. U.S. (NASD) = USANN eller utelatt, Europeisk = SANN."}, "EDATE": {"a": "(start_dato; m<PERSON><PERSON><PERSON>)", "d": "Returnerer serienum<PERSON>t for datoen som er det viste antallet måneder før eller etter startdatoen", "ad": "er et serienummer for datoen som representerer startdatoen!er antall måneder før eller etter start_dato"}, "EOMONTH": {"a": "(start_dato; m<PERSON><PERSON><PERSON>)", "d": "Returnerer serienummeret for den siste dagen i måneden før eller etter et angitt antall måneder", "ad": "er et serienummer for datoen som representerer startdatoen!er antall måneder før eller etter start_dato"}, "HOUR": {"a": "(serienummer)", "d": "Returnerer time på dagen som et tall fra 0 (12:00 AM) til 23 (11:00 PM).", "ad": "er et tall som representerer en dato eller et klokkeslett som brukes av Spreadsheet Editor, eller tekst i klokkeslettformat, for eksempel 16:48:00 eller 4:48:00 PM"}, "ISOWEEKNUM": {"a": "(dato)", "d": "Returnerer ISO-ukenummeret i året for en gitt dato", "ad": "er dato-klokkeslett-koden som brukes av Spreadsheet Editor til beregning av dato og klokkeslett"}, "MINUTE": {"a": "(serienummer)", "d": "Returnerer minuttet, et tall fra 0 til 59.", "ad": "er et tall som representerer en dato eller et klokkeslett som brukes av Spreadsheet Editor, eller tekst i klokkeslettformat, for eksempel 16:48:00 eller 4:48:00 PM"}, "MONTH": {"a": "(serienummer)", "d": "Returnerer m<PERSON><PERSON><PERSON>, et tall fra 1 (januar) til 12 (desember).", "ad": "er et tall som representerer en dato eller et klokkeslett som brukes av Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(<PERSON>da<PERSON>; sluttdato; [helligdager])", "d": "Returnerer antallet hele arbeidsdager mellom to datoer", "ad": "er et serielt datonummer som representerer startdatoen!er et serielt datonummer som representerer sluttdatoen!er en valgfri liste med én eller flere datoer som skal utelates fra arbeidskalenderen, for eksempel nasjonale helligdager og flytende helligdager"}, "NETWORKDAYS.INTL": {"a": "(startdato; sluttdato; [helg]; [helligdager])", "d": "Returnerer antall hele arbeidsdager mellom to datoer med egendefinerte helgeparametere", "ad": "er et seriedatonummer som representerer startdatoen!er et seriedatonummer som representerer sluttdatoen!er et tall eller en streng som angir når helger forekommer!er et valgfritt sett med ett eller flere seriedatonumre som du kan utelate fra arbeidskalenderen, for eks<PERSON>pel faste og flytende helligdager"}, "NOW": {"a": "()", "d": "Returnerer gjeldende dato og klokkeslett formatert som dato og klokkeslett.", "ad": ""}, "SECOND": {"a": "(serienummer)", "d": "Returnerer sekundet, et tall fra 0 til 59.", "ad": "er et tall som representerer en dato eller et klokkeslett som brukes av Spreadsheet Editor, eller tekst i klokkeslettformat, for eksempel 16:48:23 eller 4:48:47 PM"}, "TIME": {"a": "(time; minutt; sekund)", "d": "<PERSON><PERSON><PERSON><PERSON> timer, minutter og sekunder som er gitt som tall, til et serienummer, formatert etter et klokkeslettsformat", "ad": "er et tall fra 0 til 23 som representerer timen!er et tall fra 0 til 59 som representerer minuttet!er et tall fra 0 til 59 som representerer sekundet"}, "TIMEVALUE": {"a": "(tidstekst)", "d": "Konverterer teksttid til et serienummer for et klokkeslett, et tall fra 0 (12:00:00 AM) til 0.999988426 (11:59:59 PM). Formaterer tallet med et klokkeslettformat etter å ha innført formelen", "ad": "er en tekststreng som angir klokkeslettet i et av klokkeslettformatene i Spreadsheet Editor (datoinformasjon i strengen ignoreres)"}, "TODAY": {"a": "()", "d": "Returnerer gjeldende dato formatert som dato.", "ad": ""}, "WEEKDAY": {"a": "(serienummer; [retur_type])", "d": "Returnerer et tall fra 1 til 7 som representerer ukedagen.", "ad": "er et tall som representerer en dato!er et tall. For søndag = 1 til lørdag = 7 bruker du 1, for mandag = 1 til søndag = 7 bruker du 2, for mandag = 0 til søndag = 6 bruker du 3"}, "WEEKNUM": {"a": "(tall; [returtype])", "d": "Returnerer ukenummeret i et år", "ad": "er koden som brukes til å beregne dato og klokkeslett i Spreadsheet Editor!er et tall (1 eller 2) som fastsetter typen returverdi"}, "WORKDAY": {"a": "(startdato; dager; [ekstra_feriedager])", "d": "returnerer serienummeret for da<PERSON><PERSON> før eller etter et angitt antall arbeidsdager", "ad": "er et serielt datonummer som representerer startdatoen!er antall dager som ikke er helge- eller helligdager før eller etter startdato!er en valgfri liste med én eller flere datoer som skal utelates fra arbeidskalenderen, for eksempel nasjonale helligdager og flytende helligdager"}, "WORKDAY.INTL": {"a": "(startdato; sluttdato; [helg]; [helligdager])", "d": "Returnerer serienummeret for da<PERSON><PERSON> før eller etter et angitt antall arbeidsdager med egendefinerte helgeparametere", "ad": "er et serielt datonummer som representerer startdatoen!er antall ikke-helgedager og ikke-helligdager før eller etter startdatoen!er et tall eller en streng som angir når det er helg!er en valgfri matrise med ett eller flere seriedatonumre som du kan utelate fra arbeidskalenderen, for eksempel faste og flytende helligdager"}, "YEAR": {"a": "(serienummer)", "d": "Returnerer årstallet, et heltall i intervallet 1900 - 9999.", "ad": "er et tall som representerer en dato eller et klokkeslett som brukes av Spreadsheet Editor"}, "YEARFRAC": {"a": "(startdato; sluttdato; [basis])", "d": "Returnerer delen av året som representeres av antall hele dager mellom startdato og sluttdato", "ad": "er serienummeret for en dato som representerer startdatoen!er serienummeret som representerer sluttdatoen!er typen datosystem som skal brukes"}, "BESSELI": {"a": "(x; n)", "d": "Returnerer Besselfunksjonen In(x)", "ad": "er verdien funksjonen skal evalueres etter!er ordenen til Besselfunksjonen"}, "BESSELJ": {"a": "(x; n)", "d": "Returnerer Besselfunksjonen Jn(x)", "ad": "er verdien du evaluerer funksjonen etter!er ordenen til Besselfunksjonen"}, "BESSELK": {"a": "(x; n)", "d": "Returnerer den modifiserte Besselfunksjonen Kn(x)", "ad": "er verdien funksjonen skal evalueres etter!er ordenen til Besselfunksjonen"}, "BESSELY": {"a": "(x; n)", "d": "Returnerer Besselfunksjonen Yn(x)", "ad": "er verdien funksjonen skal evalueres etter!er ordenen til Besselfunksjonen"}, "BIN2DEC": {"a": "(tall)", "d": "Konverterer et binærtall til et heltall i 10-tallsystemet", "ad": "er binærtallet du vil konvertere"}, "BIN2HEX": {"a": "(tall; [plasser])", "d": "Konverterer et binærtall til et heksadesimalt tall", "ad": "er binærtallet du vil konvertere!er antallet tegn du skal bruke"}, "BIN2OCT": {"a": "(tall; [plasser])", "d": "Konverterer et binærtall til et oktaltall", "ad": "er binærtallet du vil konvertere!er antallet tegn du skal bruke"}, "BITAND": {"a": "(tall1; tall2)", "d": "Returnerer et bitvis \"Og\" av to tall", "ad": "er desimalformen av det binære tallet du vil evaluere!er desimalformen av det binære tallet du vil evaluere"}, "BITLSHIFT": {"a": "(tall; stø<PERSON><PERSON>_forskyvning)", "d": "Returnerer et tall som forskyves mot venstre med størrelse_forskyvning biter", "ad": "er desimalformen av det binære tallet du vil evaluere!er antallet biter du vil forskyve tallet mot venstre med"}, "BITOR": {"a": "(tall1; tall2)", "d": "Returnerer et bitvis \"Eller\" av to tall", "ad": "er desimalformen av det binære tallet du vil evaluere!er desimalformen av det binære tallet du vil evaluere"}, "BITRSHIFT": {"a": "(tall; stø<PERSON><PERSON>_forskyvning)", "d": "Returnerer et tall som forskyves mot høyre med størrelse_forskyvning biter", "ad": "er desimalformen av det binære tallet du vil evaluere!er antallet biter du vil forskyve tallet mot høyre med"}, "BITXOR": {"a": "(tall1; tall2)", "d": "Returnerer et bitvis \"Utelukkende eller\" av to tall", "ad": "er desimalformen av det binære tallet du vil evaluere!er desimalformen av det binære tallet du vil evaluere"}, "COMPLEX": {"a": "(reelt_tall; imaginært_tall; [suffiks])", "d": "Konverterer reelle og imaginære koeffisienter til et komplekst tall", "ad": "er den reelle koeffisienten i det komplekse tallet!er den imaginære koeffisienten i det komplekse tallet!er suffikset for den imaginære komponenten i det komplekse tallet"}, "CONVERT": {"a": "(tall; fra_enhet; til_enhet)", "d": "Konverterer et tall fra ett målesystem til et annet", "ad": "er verdien i fra_enhet som skal konverteres!er enheten for tall!er enheten for resultatet"}, "DEC2BIN": {"a": "(tall; [plasser])", "d": "Konverterer et heltall i 10-tallsystemet til et binærtall", "ad": "er heltallet du vil konvertere!er antallet tegn du skal bruke"}, "DEC2HEX": {"a": "(tall; [plasser])", "d": "Konverterer et heltall i 10-tallsystemet til et heksadesimaltall", "ad": "er heltallet du vil konvertere!er antallet tegn du skal bruke"}, "DEC2OCT": {"a": "(tall; [plasser])", "d": "Konverterer et heltall i 10-tallsystemet til et oktaltall", "ad": "er heltallet du vil konvertere!er antallet tegn du skal bruke"}, "DELTA": {"a": "(tall1; [tall2])", "d": "<PERSON><PERSON><PERSON><PERSON> om to tall er like", "ad": "er det første tallet!er det andre tallet"}, "ERF": {"a": "(nedre_grense; [øvre_grense])", "d": "Returnerer feilfunksjonen", "ad": "er den nedre grensen for å integrere FEILF!er den øvre grensen for å integrere FEILF"}, "ERF.PRECISE": {"a": "(X)", "d": "Returnerer feilfunksjonen", "ad": "er den nedre grensen for å integrere FEILF.PRESIS"}, "ERFC": {"a": "(x)", "d": "Returnerer den komplementære feilfunksjonen", "ad": "er den nedre grensen for å integrere FEILF"}, "ERFC.PRECISE": {"a": "(x)", "d": "Returnerer den komplementære feilfunksjonen", "ad": "er den nedre grensen for å integrere FEILF.PRESIS"}, "GESTEP": {"a": "(tall; [steg])", "d": "<PERSON><PERSON><PERSON><PERSON> om et tall er større enn en terskelverdi", "ad": "er verdien du vil teste mot steg!er terskelverdien"}, "HEX2BIN": {"a": "(tall; [plasser])", "d": "Konverterer et heksadesimalt tall til et binærtall", "ad": "er det heksadesimale tallet du vil konvertere!er antallet tegn du skal bruke"}, "HEX2DEC": {"a": "(tall)", "d": "Konverterer et heksadesimalt tall til et heltall i 10-tallsystemet", "ad": "er det heksades<PERSON>le tallet du vil konvertere"}, "HEX2OCT": {"a": "(tall; [plasser])", "d": "Konverterer et heksadesimaltall til et oktaltall", "ad": "er det heksadesimale tallet du vil konvertere!er antallet tegn du skal bruke"}, "IMABS": {"a": "(imtall)", "d": "Returnerer absoluttverdien (modulus) til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne absoluttverdien til"}, "IMAGINARY": {"a": "(imtall)", "d": "Returnerer den imaginære koeffisienten til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne den imaginære koeffisienten til"}, "IMARGUMENT": {"a": "(imtall)", "d": "Returnerer argumentet q, en vinkel uttrykt i radianer", "ad": "er det komplekse tallet du ønsker å finne argumentet til"}, "IMCONJUGATE": {"a": "(imtall)", "d": "Returnerer den komplekskonjugerte til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne den konjugerte til"}, "IMCOS": {"a": "(imtall)", "d": "Returnerer kosinusen til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne kosinusen til"}, "IMCOSH": {"a": "(imtall)", "d": "Returnerer hyperbolsk cosinus til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne hyperbolsk cosinus til"}, "IMCOT": {"a": "(imtall)", "d": "Returnerer cotangens til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne cotangens til"}, "IMCSC": {"a": "(imtall)", "d": "Returnerer cosekans til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne cosekans til"}, "IMCSCH": {"a": "(imtall)", "d": "Returnerer hyperbolsk cosekans til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne hyperbolsk cosekans til"}, "IMDIV": {"a": "(imtall1; imtall2)", "d": "Returnerer kvotienten av to komplekse tall", "ad": "er den komplekse telleren eller dividenden!er den komplekse nevneren eller divisoren"}, "IMEXP": {"a": "(imtall)", "d": "Returnerer eksponenten til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne eksponenten til"}, "IMLN": {"a": "(imtall)", "d": "Returnerer den naturlige logaritmen til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne den naturlige logaritmen til"}, "IMLOG10": {"a": "(imtall)", "d": "Returnerer den briggske logaritmen (med grunntall 10) til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne den briggske logaritmen til"}, "IMLOG2": {"a": "(imtall)", "d": "Returnerer logaritmen med grunntall 2 til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne logaritmen med grunntall 2  til"}, "IMPOWER": {"a": "(imtall; tall)", "d": "Returnerer et komplekst tall opphøyd i en heltallspotens", "ad": "er det komplekse tallet du vil opphøye i en potens!er potensen du vil opphøye det komplekse tallet i"}, "IMPRODUCT": {"a": "(imtall1; [imtall2]; ...)", "d": "Returnerer produktet av 1 til 255 komplekse tall", "ad": "imtall1,imtall2,... er fra 1 til 255 komplekse tall som skal multipliseres"}, "IMREAL": {"a": "(imtall)", "d": "Returnerer den reelle koeffisienten til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne den reelle koeffisienten til"}, "IMSEC": {"a": "(imtall)", "d": "Returnerer sekans til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne sekans til"}, "IMSECH": {"a": "(imtall)", "d": "Returnerer hyperbolsk sekans til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne hyperbolsk sekans til"}, "IMSIN": {"a": "(imtall)", "d": "Returnerer sinusen til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne sinusen til"}, "IMSINH": {"a": "(imtall)", "d": "Returnerer hyperbolsk sinus til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne hyperbolsk sinus til"}, "IMSQRT": {"a": "(imtall)", "d": "Returnerer kvadratroten til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne kvadratroten til"}, "IMSUB": {"a": "(imtall1; imtall2)", "d": "Returnerer differansen mellom to komplekse tall", "ad": "er det komplekse tallet imtall2 skal subtraheres fra!er det komplekse tallet som skal subtraheres fra imtall1"}, "IMSUM": {"a": "(imtall1; [imtall2]; ...)", "d": "Returnerer summen av to eller flere komplekse tall", "ad": "er 1 til 255 komplekse tall som skal legges sammen"}, "IMTAN": {"a": "(imtall)", "d": "Returnerer tangens til et komplekst tall", "ad": "er det komplekse tallet du ønsker å finne tangens til"}, "OCT2BIN": {"a": "(tall; [plasser])", "d": "Konverterer et oktaltall til et binærtall", "ad": "er oktaltallet du vil konvertere!er antallet tegn du skal bruke"}, "OCT2DEC": {"a": "(tall)", "d": "Konverterer et oktaltall til et heltall i 10-tallsystemet", "ad": "er oktaltallet du vil konvertere"}, "OCT2HEX": {"a": "(tall; [plasser])", "d": "Konverterer et oktaltall til et heksadesimalt tall", "ad": "er oktaltallet du vil konvertere!er antallet tegn du skal bruke"}, "DAVERAGE": {"a": "(database; felt; vilkår)", "d": "Returnerer gjennomsnittet av verdiene i en kolonne i en liste eller database som oppfyller vilkårene du angir", "ad": "er celleområdet som utgjør listen eller databasen. En database er en liste over data som hører sammen!er enten etiketten til kolonnen i doble anførselstegn eller et tall som representerer kolonnens plass i listen!er celleområdet som inneholder vilkårene du angir. Området inneholder en kolonneetikett og en celle under etiketten for et vilkår"}, "DCOUNT": {"a": "(database; felt; vilkår)", "d": "Teller cellene som inneholder tall, i feltet (kolonnen) med poster i databasen som oppfyller vilkårene du angir", "ad": "er celleområdet som utgjør listen eller databasen. En database er en liste over data som hører sammen!er enten etiketten til kolonnen i doble anførselstegn eller et tall som representerer kolonnens plass i listen!er celleområdet som inneholder vilkårene du angir. Området inneholder en kolonneetikett og en celle under etiketten for et vilkår"}, "DCOUNTA": {"a": "(database; felt; vilkår)", "d": "Teller utfylte celler i feltet (kolonnen) med poster i databasen som oppfyller vilkårene du angir", "ad": "er celleområdet som utgjør listen eller databasen. En database er en liste med data som hører sammen.!er enten etiketten til kolonnen i doble anførselstegn eller et tall som representerer kolonnens plass i listen!er celleområdet som inneholder vilkårene du angir. Området inneholder en kolonneetikett og en celle under etiketten for et vilkår"}, "DGET": {"a": "(database; felt; vilkår)", "d": "Trekker ut en post som oppfyller vilkårene du angir, fra en database", "ad": "er celleområdet som utgjør listen eller databasen. En database er en liste over data som hører sammen!er enten etiketten til kolonnen i doble anførselstegn eller et tall som representerer kolonnens plass i listen!er celleområdet som inneholder vilkårene du angir. Området inneholder en kolonneetikett og en celle under etiketten for et vilkår"}, "DMAX": {"a": "(database; felt; vilkår)", "d": "Returnerer det høyeste tallet i feltet (kolonnen) med poster i databasen som oppfyller vilkårene du angir", "ad": "er celleområdet som utgjør listen eller databasen. En database er en liste over data som hører sammen!er enten etiketten til kolonnen i doble anførselstegn eller et tall som representerer kolonnens plass i listen!er celleområdet som inneholder vilkårene du angir. Området inneholder en kolonneetikett og en celle under etiketten for et vilkår"}, "DMIN": {"a": "(database; felt; vilkår)", "d": "Returnerer det laveste tallet i feltet (kolonnen) med poster i databasen som oppfyller vilkårene du angir", "ad": "er celleområdet som utgjør listen eller databasen. En database er en liste over data som hører sammen.!er enten etiketten til kolonnen i doble anførselstegn eller et tall som representerer kolonnens plass i listen!er celleområdet som inneholder vilkårene du angir. Området inneholder en kolonneetikett og en celle under etiketten for et vilkår"}, "DPRODUCT": {"a": "(database; felt; vilkår)", "d": "Multipliserer verdiene i et bestemt felt (kolonne) med poster i databasen som oppfyller vilkårene du angir", "ad": "er celleområdet som utgjør listen eller databasen. En database er en liste over data som hører sammen.!er enten etiketten til kolonnen i doble anførselstegn eller et tall som representerer kolonnens plass i listen!er celleområdet som inneholder vilkårene du angir. Området inneholder en kolonneetikett og en celle under etiketten for et vilkår"}, "DSTDEV": {"a": "(database; felt; vilkår)", "d": "Estimerer standardavviket på grunnlag av et utvalg i form av merkede databaseposter", "ad": "er celleområdet som utgjør listen eller databasen. En database er en liste over data som hører sammen.!er enten etiketten til kolonnen i doble anførselstegn eller et tall som representerer kolonnens plass i listen!er celleområdet som inneholder vilkårene du angir. Området inneholder en kolonneetikett og en celle under etiketten for et vilkår"}, "DSTDEVP": {"a": "(database; felt; vilkår)", "d": "Be<PERSON>gner standardavviket basert på at merkede databaseposter utgjør hele populasjonen", "ad": "er celleområdet som utgjør listen eller databasen. En database er en liste over data som hører sammen.!er enten etiketten til kolonnen i doble anførselstegn eller et tall som representerer kolonnens plass i listen!er celleområdet som inneholder vilkårene du angir. Området inneholder en kolonneetikett og en celle under etiketten for et vilkår"}, "DSUM": {"a": "(database; felt; vilkår)", "d": "Legger til tallene i feltet (kolonnen) med poster i databasen som oppfyller vilkårene du angir", "ad": "er celleområdet som utgjør listen eller databasen. En database er en liste over data som hører sammen.!er enten etiketten til kolonnen i doble anførselstegn eller et tall som representerer kolonnens plass i listen!er celleområdet som inneholder vilkårene du angir. Området inneholder en kolonneetikett og en celle under etiketten for et vilkår"}, "DVAR": {"a": "(database; felt; vilkår)", "d": "<PERSON><PERSON><PERSON><PERSON> variansen basert på at merkede databaseposter utgjør et utvalg", "ad": "er celleområdet som utgjør listen eller databasen. En database er en liste over data som hører sammen.!er enten etiketten til kolonnen i doble anførselstegn eller et tall som representerer kolonnens plass i listen!er celleområdet som inneholder vilkårene du angir. Området inneholder en kolonneetikett og en celle under etiketten for et vilkår"}, "DVARP": {"a": "(database; felt; vilkår)", "d": "<PERSON><PERSON><PERSON><PERSON> var<PERSON>en basert på at merkede databaseposter utgjør hele populasjonen", "ad": "er celleområdet som utgjør listen eller databasen. En database er en liste over data som hører sammen.!er enten etiketten til kolonnen i doble anførselstegn eller et tall som representerer kolonnens plass i listen!er celleområdet som inneholder vilkårene du angir. Området inneholder en kolonneetikett og en celle under etiketten for et vilkår"}, "CHAR": {"a": "(tall)", "d": "Returnerer tegnet som svarer til kodenummeret fra tegnsettet på datamaskinen", "ad": "er et tall mellom 1 og 255 som svarer til tegnet du ønsker"}, "CLEAN": {"a": "(tekst)", "d": "<PERSON><PERSON><PERSON> alle tegn som ikke kan skrives ut, fra teksten", "ad": "er all regnearkinformasjon der du vil fjerne tegn som ikke kan skrives ut"}, "CODE": {"a": "(tekst)", "d": "Returnerer en numerisk kode for det første tegnet i en tekststreng, i tegnsettet som datamaskinen bruker", "ad": "er teksten der du vil finne koden for det første tegnet"}, "CONCATENATE": {"a": "(tekst1; [tekst2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> sammen flere tekststrenger til én tekststreng", "ad": "er 1 til 255 tekststrenger som skal slås sammen til en tekststreng, og kan være tekststrenger, tall eller referanser til enkelte celler"}, "CONCAT": {"a": "(tekst1; ...)", "d": "<PERSON><PERSON><PERSON> sammen en liste eller et område med tekststrenger", "ad": "er 1 til 254 tekststrenger eller områder som skal kjedes sammen til én tekststreng"}, "DOLLAR": {"a": "(tall; [desimaler])", "d": "Konverterer et tall til tekst i valutaformat", "ad": "er et tall, en referanse til en celle som inneholder et tall, eller en formel som evalueres til et tall!er antall sifre til høyre for desimaltegnet. Tallet blir avrundet etter behov. Hvis argumentet utelates, settes Desimaler til 2"}, "EXACT": {"a": "(tekst1; tekst2)", "d": "Kontrollerer om to tekststrenger er helt like, og returnerer SANN eller USANN. EKSAKT skiller mellom små og store bokstaver", "ad": "er den første tekststrengen!er den andre tekststrengen"}, "FIND": {"a": "(finn; innen_tekst; [startpos])", "d": "Returnerer nummeret for posisjonen for det første tegnet i en tekststreng inne i en annen tekststreng. FINN skiller mellom store og små bokstaver", "ad": "er teksten du vil søke etter. Bruk doble anførselstegn (tom tekst) for å søke etter det første tegnet i innen_tekst. Du kan ikke bruke jokertegn!er teksten som inneholder teksten du vil finne!angir tegnet du vil starte søket fra. Det første tegnet i innen_tekst er tegn nummer 1. <PERSON><PERSON> den utelates, blir startpos satt til 1"}, "FINDB": {"a": "(finn; innen_tekst; [startpos])", "d": "Finner en tekststreng inni en annen tekststreng og returnerer nummeret som svarer til startposisjonen for den første tekststrengen, regnet fra det første tegnet i den andre tekststrengen, er beregnet for språk som bruker dobbeltbyte-tegnsett (DBCS) - japansk, kinesisk og koreansk", "ad": "er teksten du vil søke etter. Bruk doble anførselstegn (tom tekst) for å søke etter det første tegnet i innen_tekst. Du kan ikke bruke jokertegn!er teksten som inneholder teksten du vil finne!angir tegnet du vil starte søket fra. Det første tegnet i innen_tekst er tegn nummer 1. <PERSON><PERSON> den utelates, blir startpos satt til 1"}, "FIXED": {"a": "(tall; [desimaler]; [ingen_tusenskille])", "d": "Runder av et tall til et angitt antall desimaler og konverter det til tekst med eller uten kommaer", "ad": "er tallet du vil runde av og konvertere til tekst!er antall sifre til høyre for desimaltegnet. Hvis argumentet utelates, blir desimaler satt til 2!er en logisk verdi. Ikke vis kommaer i den returnerte teksten = SANN, vis kommaer i den returnerte teksten = USANN eller utelatt"}, "LEFT": {"a": "(tekst; [antall_tegn])", "d": "Returnerer det angitte antall tegn fra begynnelsen av en tekststreng", "ad": "er tekststrengen som inneholder tegnene du vil trekke ut!angir hvor mange tegn du vil at funksjonen VENSTRE skal trekke ut. Settes til 1 hvis argumentet utelates"}, "LEFTB": {"a": "(tekst; [antall_tegn])", "d": "Returnerer det første tegnet eller de første tegnene i en tekststreng, basert på antallet byte du angir, er beregnet for språk som bruker dobbeltbyte-tegnsett (DBCS) - japansk, kinesisk og koreansk", "ad": "er tekststrengen som inneholder tegnene du vil trekke ut!angir hvor mange tegn du vil at funksjonen LEFTB skal trekke ut. Settes til 1 hvis argumentet utelates"}, "LEN": {"a": "(tekst)", "d": "Returnerer antall tegn i en tekststreng", "ad": "er teksten du vil finne lengden på. Mellomrom telles som tegn"}, "LENB": {"a": "(tekst)", "d": "Returnerer antallet byte som brukes til å representere tegnene i en tekststreng, er beregnet for språk som bruker dobbeltbyte-tegnsett (DBCS) - japansk, kinesisk og koreansk", "ad": "er teksten du vil finne lengden på. Mellomrom telles som tegn"}, "LOWER": {"a": "(tekst)", "d": "Konverterer alle bokstaver i en tekststreng til små bokstaver", "ad": "er teksten du vil konvertere til små bokstaver. Tegn i teksten som ikke er bokstaver, endres ikke"}, "MID": {"a": "(tekst; startpos; antall_tegn)", "d": "Returnerer tegnene fra midten av en tekststreng, hvis posisjonen for det første tegnet og lengden er oppgitt", "ad": "er tekststrengen som inneholder tegnene du vil trekke ut!er posisjonen til det første tegnet du vil trekke ut. Det første tegnet i tekst er 1!angir hvor mange tegn som skal returneres fra teksten"}, "MIDB": {"a": "(tekst; startpos; antall_tegn)", "d": "Returnerer et angitt antall tegn fra en tekststreng, regnet fra den posisjonen du angir og basert på antall tegn du angir, er beregnet for språk som bruker dobbeltbyte-tegnsett (DBCS) - japansk, kinesisk og koreansk", "ad": "er tekststrengen som inneholder tegnene du vil trekke ut!er posisjonen til det første tegnet du vil trekke ut. Det første tegnet i tekst er 1!angir hvor mange tegn som skal returneres fra teksten"}, "NUMBERVALUE": {"a": "(tekst; [desimalskilletegn]; [gruppeskilletegn])", "d": "Konverterer tekst til tall på en måte som er uavhengig av nasjonal innstilling", "ad": "er strengen som representerer tallet du vil konvertere!er tegnet som brukes som desimalskilletegn i strengen!er tegnet som brukes som skilletegn for gruppe i en streng"}, "PROPER": {"a": "(tekst)", "d": "Konverterer en tekststreng med hensyn til små og store bokstaver. Den første bokstaven i hvert ord får stor bokstav, og alle andre bokstaver konverteres til små bokstaver", "ad": "er tekst som står mellom anførselstegn, en formel som returnerer tekst, eller en referanse til en celle som inneholder tekst du vil skrive med store forbokstaver"}, "REPLACE": {"a": "(gammel_tekst; startpos; antall_tegn; ny_tekst)", "d": "Erstatter en del av en tekststreng med en annen tekststreng", "ad": "er tekst der du vil erstatte enkelte tegn!er posisjonen til tegnet i gammel_tekst som du vil erstatte med ny_tekst!er antall tegn i gammel_tekst som du vil erstatte!er teksten som skal erstatte tegn i gammel_tekst"}, "REPLACEB": {"a": "(gammel_tekst; startpos; antall_tegn; ny_tekst)", "d": "Bytter ut en del av en tekststreng med en annen tekststreng. Hvilken del som byttes ut, er basert på antall byte du angir, er beregnet for språk som bruker dobbeltbyte-tegnsett (DBCS) - japansk, kinesisk og koreansk", "ad": "er tekst der du vil erstatte enkelte tegn!er posisjonen til tegnet i gammel_tekst som du vil erstatte med ny_tekst!er antall tegn i gammel_tekst som du vil erstatte!er teksten som skal erstatte tegn i gammel_tekst"}, "REPT": {"a": "(tekst; antall_ganger)", "d": "Gjentar tekst et angitt antall ganger. Bruk GJENTA for å fylle en celle med et antall forekomster av en tekststreng", "ad": "er teksten du vil gjenta!er et positivt tall som angir hvor mange ganger teksten skal gjentas"}, "RIGHT": {"a": "(tekst; [antall_tegn])", "d": "Returnerer det angitte antall tegn fra slutten av en tekststreng", "ad": "er tekststrengen som inneholder tegnene du vil trekke ut!angir hvor mange tegn du vil trekke ut. Settes til 1 hvis argumentet utelates"}, "RIGHTB": {"a": "(tekst; [antall_tegn])", "d": "Returnerer det siste tegnet eller de siste tegnene i en tekststreng, basert på antallet byte du angir, er beregnet for språk som bruker dobbeltbyte-tegnsett (DBCS) - japansk, kinesisk og koreansk", "ad": "er tekststrengen som inneholder tegnene du vil trekke ut!angir hvor mange tegn du vil trekke ut. Settes til 1 hvis argumentet utelates"}, "SEARCH": {"a": "(finn; innen_tekst; [startpos])", "d": "Returnerer tallet for tegnet hvor et bestemt tegn eller en tekststreng først blir funnet, lest fra venstre mot høyre (skiller ikke mellom små og store bokstaver)", "ad": "er teksten du vil søke etter. Du kan bruke jokertegnene ? og *. Bruk ~? og ~* når du vil finne tegnene ? og *!er teksten der du vil søke etter argumentet finn!er nummeret til det tegnet i innen_tekst der du vil begynne søket, lest fra venstre mot høyre. Hvis argumentet utelates, brukes 1"}, "SEARCHB": {"a": "(finn; innen_tekst; [startpos])", "d": "Finner én tekststreng inni en annen tekststreng og returnerer tallet som svarer til startposisjonen for den første tekststrengen, regnet fra det første tegnet i den andre tekststrengen, er beregnet for språk som bruker dobbeltbyte-tegnsett (DBCS) - japansk, kinesisk og koreansk", "ad": "er teksten du vil søke etter. Du kan bruke jokertegnene ? og *. Bruk ~? og ~* når du vil finne tegnene ? og *!er teksten der du vil søke etter argumentet finn!er nummeret til det tegnet i innen_tekst der du vil begynne søket, lest fra venstre mot høyre. Hvis argumentet utelates, brukes 1"}, "SUBSTITUTE": {"a": "(tekst; gammel_tekst; ny_tekst; [forekomst_nr])", "d": "Erstatter eksisterende tekst med ny tekst i en tekststreng", "ad": "er teksten eller referansen til en celle som inneholder teksten der du vil bytte ut tegn!er den eksisterende teksten du vil erstatte. Hvis gammel_tekst ikke er lik med hensyn til store og små bokstaver, vil BYTT.UT ikke erstatte teksten!er teksten du vil erstatte gammel_tekst med!angir hvilken forekomst av gammel_tekst du vil erstatte. Hvis argumentet utelates, erstattes alle forekomster av gammel_tekst"}, "T": {"a": "(verdi)", "d": "Kontrollerer om en verdi er tekst, og returnerer i så fall teksten, hvis ikke returnerer den doble anførselstegn (tom tekst)", "ad": "er verdien du vil teste"}, "TEXT": {"a": "(verdi; format_text)", "d": "Konverterer en verdi til tekst i et bestemt nummer format", "ad": "et et tall, en formel som returnerer en numerisk verdi, eller en referanse til en celle som inneholder en numerisk verdi!er et tall format i tekst form under kategori i kategorien tall i dialog boksen Formater celler"}, "TEXTJOIN": {"a": "(skilletegn; ignorer_tom; tekst1; ...)", "d": "<PERSON><PERSON><PERSON> sammen en liste eller et område med tekststrenger ved hjelp av et skilletegn", "ad": "Tegn eller streng som skal settes inn mellom hvert tekstelement!ignorerer tomme celler hvis SANN(standard)!er 1 til 252 tekststrenger eller områder som skal kjedes sammen"}, "TRIM": {"a": "(tekst)", "d": "<PERSON><PERSON><PERSON> alle mellomrom fra tekst unntatt enkle mellomrom mellom ord", "ad": "er teksten du vil fjerne mellomrom fra"}, "UNICHAR": {"a": "(nummer)", "d": "Returnerer Unicode-tegnet som den gitte numeriske verdien refererer til", "ad": "er Unicode-nummeret som representerer et tegn"}, "UNICODE": {"a": "(tekst)", "d": "Returnerer tallet (kodepunktet) som tilsvarer det første tegnet i teksten", "ad": "er tegnet du vil finne Unicode-verdien for"}, "UPPER": {"a": "(tekst)", "d": "Konverterer en tekststreng til store bokstaver", "ad": "er teksten du vil konvertere til store bokstaver, en referanse eller en tekststreng"}, "VALUE": {"a": "(tekst)", "d": "Konverterer en tekststreng som representerer et tall, til et tall", "ad": "er tekst som står mellom anførselstegn, eller en referanse til en celle som inneholder teksten du vil konvertere"}, "AVEDEV": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer datapunktenes gjennomsnittlige absoluttavvik fra middelverdien. Argumentene kan være tall eller navn, matriser eller referanser som inneholder tall", "ad": "er 1 til 255 argumenter du vil finne de gjennomsnittlige absoluttavvikene for"}, "AVERAGE": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer gjenn<PERSON><PERSON><PERSON><PERSON> av argumentene, som kan være tall, navn, matriser eller referanser som inneholder tall", "ad": "er 1 til 255 numeriske argumenter du vil finne gjennomsnittet av"}, "AVERAGEA": {"a": "(verdi1; [verdi2]; ...)", "d": "Returnerer gjennomsnittet (aritmetisk middelverdi) av argumentene. Evaluerer tekst og USANN i argumenter som 0 og SANN som 1. Argumentene kan være tall, navn, matriser eller referanser", "ad": "er 1 til 255 argumenter du vil finne gjennomsnittet av"}, "AVERAGEIF": {"a": "(område; kriterium; [gjenn<PERSON>nitt_område])", "d": "<PERSON>er gjennomsnittet (det aritmetiske gjennomsnittet) for cellene som angis av et gitt vilkår eller kriterium", "ad": "er celleområdet du vil evaluere!er betingelsen eller vilkåret i form av et tall, et uttrykk eller tekst som definerer hvilke celler som skal brukes til å finne gjennomsnittet!er de faktiske cellene som skal brukes til å finne gjennomsnittet. Hvis utelatt, brukes cellene i området"}, "AVERAGEIFS": {"a": "(gjennomsnitt_område; kriterieområde; kriterium; ...)", "d": "Finner gjennomsnittet (aritmetisk middelverdi) for cellene som angis av et gitt sett med vilkår eller kriterier", "ad": "er de faktiske cellene det skal finnes gjennomsnitt av!er området med celler du vil evaluere i det spesifikke vilkåret!er vilkåret eller kriteriet i form av tall, uttrykk eller tekst som definerer hvilke celler som skal brukes til å finne gjennomsnittet"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Returnerer den kumulative betafordelingsfunksjonen for sannsynlig tetthet", "ad": "er en verdi mellom A og B du vil evaluere funksjonen for!er en parameter for fordelingen og må være større enn 0!er en parameter for fordelingen og må være større enn 0!er en valgfri nedre grense for x-intervallet. Hvis argumentet utelates, settes A til 0!er en valgfri øvre grense for x-intervallet. Hvis argumentet utelates, settes B til 1"}, "BETAINV": {"a": "(sannsynlighet; alfa; beta; [A]; [B])", "d": "Returnerer den inverse til den kumulative betafordelingsfunksjonen for sannsynlig tetthet (BETA.FORDELING)", "ad": "er en sannsynlighet for betafordelingen!er en parameter for fordelingen og må være større enn 0!er en parameter for fordelingen og må være større enn 0!er en valgfri nedre grense for x-intervallet. Hvis argumentet utelates, settes A til 0!er en valgfri øvre grense for x-intervallet. Hvis argumentet utelates, settes B til 1"}, "BETA.DIST": {"a": "(x; alfa; beta; kumulativ; [A]; [B])", "d": "Returnerer betafunksjonen for sannsynlig fordeling", "ad": "er en verdi mellom A og B som funksjonen skal evalueres for!er en parameter for fordelingen og må være større enn 0!er en parameter for fordelingen og må være større enn 0!er en logisk verdi: Bruk SANN for kumulativ fordeling, bruk USANN for sannsynlig tetthet!er en valgfri nedre grense for x-intervallet. Hvis argumentet utelates, settes A lik 0!er en valgfri øvre grense for x-intervallet. Hvis argumentet utelates, settes B lik 1"}, "BETA.INV": {"a": "(sannsynlighet; alfa; beta; [A]; [B])", "d": "Returnerer den inverse av den kumulative betafunksjonen for sannsynlig tetthet (BETA.FORDELING.N)", "ad": "er en sannsynlighet knyttet til betafordelingen!er en parameter for fordelingen og må være større enn 0!er en parameter for fordelingen og må være større enn 0!er en valgfri nedre grense for x-intervallet. Hvis argumentet utelates, settes A lik 0! er en valgfri øvre grense for x-intervallet. Hvis argumentet utelates, settes B lik 1"}, "BINOMDIST": {"a": "(tall_s; forsøk; sannsynlighet_s; kumulativ)", "d": "Returnerer punktsannsynlighet eller kumulativ sannsynlighet", "ad": "er antallet vellykkede forsøk!er antallet uavhengige forsøk!er sannsynligheten for å lykkes i hvert forsøk!er en logisk verdi. For den kumulative fordelingsfunksjonen bruker du SANN. For punktsannsynlighet bruker du USANN"}, "BINOM.DIST": {"a": "(antall_s; forsøk; sannsynlighet_s; kumulativ)", "d": "Returnerer den individuelle binomiske sannsynlighetsfordelingen", "ad": "er antall vellykkede forsøk!er antall uavhengige forsøk!er sannsynligheten for å lykkes i hvert forsøk!er en logisk verdi. For den kumulative fordelingsfunksjonen bruker du SANN, for punktsannsynlighet bruker du USANN"}, "BINOM.DIST.RANGE": {"a": "(forsøk; sannsynlighet_s; antall_s; [antall_s2])", "d": "Returnerer sannsynligheten for et forsøksresultat ved hjelp av en binomisk fordeling", "ad": "er antallet uavhengige forsøk!er sannsynligheten for å lykkes ved hvert forsøk!er antallet forsøk med vellykket utfall!Hvis angitt returnerer denne funksjonen sannsynligheten for at antallet vellykkede forsøk skal ligge mellom antall_s og antall_s2"}, "BINOM.INV": {"a": "(forsøk; sannsynlighet_s; alfa)", "d": "Returnerer den minste verdien der den kumulative binomiske fordelingen er større enn eller lik en vilkårsverdi", "ad": "er antallet <PERSON>lli-forsøk!er sannsynligheten for å lykkes i hvert forsøk, et tall fra og med 0 til og med 1!er vilkårsverdien, et tall fra og med 0 til og med 1"}, "CHIDIST": {"a": "(x; frihetsgrader)", "d": "Returnerer den høyre sannsynligheten til kjikvadratfordelingen", "ad": "er verdien du vil evaluere fordelingen for, et ikke-negativt tall!er antallet frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10"}, "CHIINV": {"a": "(sannsynlighet; frihetsgrader)", "d": "Returnerer den inverse av den høyre sannsynligheten til kjikvadratfordelingen", "ad": "er en sannsynlighet knyttet til kjikvadratfordelingen, en verdi fra og med 0 til og med 1!er antallet frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10"}, "CHITEST": {"a": "(faktisk; forventet)", "d": "Returnerer testen for uavhengighet: verdien fra kjikvadratfordelingen for observatoren og gjeldende frihetsgrader", "ad": "er dataområdet som inneholder observasjonene som skal testes mot forventede verdier!er dataområdet som inneholder forholdet i produktet mellom rad- og kolonnesummer og hovedsummen"}, "CHISQ.DIST": {"a": "(x; frihetsgrader; kumulative)", "d": "Returnerer venstre sannsynlighet for den kjikvadrerte fordelingen", "ad": "er verdien der du vil evaluere fordelingen, et ikke-negativt tall!er antall frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10!er en logisk verdi som funksjonen skal returnere: SANN gir den kumulative fordelingsfunksjonen; USANN gir funksjonen for sannsynlig tetthet"}, "CHISQ.DIST.RT": {"a": "(x; frihetsgrader)", "d": "Returnerer den høyre sannsynligheten for den kjikvadrerte fordelingen", "ad": "er verdien der du vil evaluere fordelingen, et ikke-negativt tall!er antall frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10"}, "CHISQ.INV": {"a": "(sannsynlighet; frihetsgrader)", "d": "Returnerer den inverse av den venstre sannsynligheten for den kjikvadrerte fordelingen", "ad": "er en sannsynlighet knyttet til den kjikvadrerte fordelingen, en verdi fra 0 til og med 1!er antall frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10"}, "CHISQ.INV.RT": {"a": "(sannsynlighet; frihetsgrader)", "d": "Returnerer den inverse av den høyre sannsynligheten for den kjikvadrerte fordelingen", "ad": "er en sannsynlighet knyttet til den kjikvadrerte fordelingen, en verdi fra 0 til og med 1!er antall frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10"}, "CHISQ.TEST": {"a": "(faktisk; forventet)", "d": "Returnerer testen for uavhengighet: verdien fra den kjikvadrerte fordelingen for observatoren og gjeldende frihetsgrader", "ad": "er dataområdet som inneholder observasjonene som skal testes mot forventede verdier!er dataområdet som inneholder forholdet mellom produktet av rad- og kolonnesummene og totalsummen"}, "CONFIDENCE": {"a": "(alfa; standardavvik; størrelse)", "d": "Returnerer konfidensintervallet til populasjonens gjennomsnitt ved å bruke en normalfordeling", "ad": "er signifikansnivået som brukes ved beregningen av konfidensnivået, et tall større enn 0 og mindre enn 1!er populasjonens standardavvik for dataområdet og antas å være kjent. Standardavvik må være større enn 0!er størrelsen på utvalget"}, "CONFIDENCE.NORM": {"a": "(alfa; standardavvik; størrelse)", "d": "Returnerer konfidensintervallet til populasjonens middelverdi", "ad": "er signifikansnivået som brukes ved beregningen av konfidensnivået, et tall som er større enn 0 og mindre enn 1!er populasjonens standardavvik for dataområdet, og blir antatt å være kjent. Standardavvik må være større enn 0!er størrelsen på utvalget"}, "CONFIDENCE.T": {"a": "(alfa; standardavvik; størrelse)", "d": "Returnerer konfidensintervallet til populasjonens middelverdi ved hjelp av en Student T-fordeling", "ad": "er signifikansnivået som brukes ved beregningen av konfidensnivået, et tall større enn 0 og mindre enn 1!er populasjonens standardavvik for dataområdet, og antas å være kjent. Standardavvik må være større enn 0!er størrelsen på utvalget"}, "CORREL": {"a": "(matrise1; matrise2)", "d": "Returnerer korrelasjonskoeffisienten mellom to datasett", "ad": "er et celleområde med verdier. Verdiene må være tall eller navn, matriser eller referanser som inneholder tall!er et annet celleområde med verdier. Verdiene må være tall, navn, matriser eller referanser som inneholder tall"}, "COUNT": {"a": "(verdi1; [verdi2]; ...)", "d": "Teller antall celler i et område som inneholder tall", "ad": "er 1 til 255 argumenter som kan inneholde eller referere til forskjellige datatyper, men bare tall telles"}, "COUNTA": {"a": "(verdi1; [verdi2]; ...)", "d": "Teller hvor mange celler i et intervall som ikke er tomme", "ad": "er 1 til 255 argumenter som representerer verdiene og cellene du vil telle. Verdiene kan være av en hvilken som helst type"}, "COUNTBLANK": {"a": "(område)", "d": "Teller antall tomme celler innenfor et område", "ad": "er området du vil telle tomme celler i"}, "COUNTIF": {"a": "(områ<PERSON>; vilkår)", "d": "Teller antall celler som oppfyller det gitte vilkåret, i et område", "ad": "er området du vil telle antall utfylte celler i!er vilkåret i form av et tall, uttrykk eller en tekst som definerer hvilke celler som telles"}, "COUNTIFS": {"a": "(kriterieområde; kriterium; ...)", "d": "Teller antall celler som angis av et gitt sett med vilkår eller kriterier", "ad": "er celleområdet du vil evaluere for det spesifikke vilkåret!er kriteriet i form av tall, uttrykk eller tekst som angir hvilke celler som skal telles"}, "COVAR": {"a": "(matrise1; matrise2)", "d": "Returnerer kova<PERSON><PERSON>, gjennomsnittet av produktene av avvikene for hvert datapunktpar i to datasett", "ad": "er det første celleområdet med heltall og må være tall, matriser eller referanser som inneholder tall!er det andre celleområdet med heltall og må være tall, matriser eller referanser som inneholder tall"}, "COVARIANCE.P": {"a": "(matrise1; matrise2)", "d": "Returnerer populas<PERSON>ens kovarians, som er gjennomsnittet av produktene av avvikene for hvert datapunktpar i to datasett", "ad": "er det første celleområdet med heltall, og må være tall, matriser eller referanser som inneholder tall!er det andre celleområdet med heltall, og må være tall, matriser eller referanser som inneholder tall"}, "COVARIANCE.S": {"a": "(matrise1; matrise2)", "d": "Returnerer utval<PERSON> kovarians, som er gjennomsnittet av produktene av avvikene for hvert datapunktpar i to datasett", "ad": "er det første celleområdet med heltall, og må være tall, matriser eller referanser som inneholder tall!er det andre celleområdet med heltall, og må være tall, matriser eller referanser som inneholder tall"}, "CRITBINOM": {"a": "(forsøk; sannsynlighet_s; alfa)", "d": "Returnerer den minste verdien der den kumulative binomiske fordelingen er større enn eller lik en vilkårsverdi", "ad": "er antallet <PERSON>lli-forsøk!er sannsynligheten for å lykkes i hvert forsøk, et tall fra og med 0 til og med 1!er vilkårsverdien, et tall fra og med 0 til og med 1"}, "DEVSQ": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer summen av datapunkters kvadrerte avvik fra utvalgsgjennomsnittet", "ad": "er 1 til 255 argumenter, eller en matrise eller matrisereferanse, som du vil beregne med AVVIK.KVADRERT"}, "EXPONDIST": {"a": "(x; lambda; kumulativ)", "d": "Returnerer eksponentialfordelingen", "ad": "er verdien av funksjonen, et ikke-negativt tall!er parameterverdien, et positivt tall!er en logisk verdi som funksjonen skal returnere. For kumulativ fordeling bruker du SANN. For sannsynlighetstetthet bruker du USANN"}, "EXPON.DIST": {"a": "(x; lambda; kumulativ)", "d": "Returnerer eksponentialfordelingen", "ad": "er verdien av funksjonen, et ikke-negativt tall!er parameterverdien, et positivt tall!er en logisk verdi for hva funksjonen skal returnere: SANN gir den kumulative fordelingsfunksjonen, USANN gir funksjonen for sannsynlig tetthet"}, "FDIST": {"a": "(x; frihetsgrader1; frihetsgrader2)", "d": "Returnerer den høyresidige Fisher-fordelingen (spredningsgraden) for to datasett", "ad": "er verdien du vil evaluere funksjonen for, et ikke-negativt tall!er tellerens frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10!er nevnerens frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10"}, "FINV": {"a": "(sannsynlighet; frihetsgrader1; frihetsgrader2)", "d": "Returnerer den høyresidige inverse av Fisher-fordelingen. Hvis p = FFORDELING(x...), er FFORDELING.INVERS(p...) = x", "ad": "er en sannsynlighet knyttet til den kumulative F-fordelingen, et tall fra og med 0 til og med 1!er tellerens frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10!er nevnerens frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10"}, "FTEST": {"a": "(matrise1; matrise2)", "d": "Returnerer resultatet av en F-test, den tosidige sannsynligheten for at variansene i matrise1 og matrise2 ikke er signifikant forskjellige", "ad": "er den første matrisen eller det første området med data og kan være tall eller navn, matriser eller referanser som inneholder tall (tomme ignoreres)!er den andre matrisen eller det andre området med data og kan være tall eller navn, matriser eller referanser som inneholder tall (tomme ignoreres)"}, "F.DIST": {"a": "(x; frihetsgrader1; frihetsgrader2; kumulative)", "d": "Returnerer (den venstresidige) F-sannsynlighetsfordelingen (spredningsgraden) for to datasett", "ad": "er verdien du vil evaluere funksjonen for, et ikke-negativt tall!er tellerens frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10!er nevnerens frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10!er en logisk verdi som funksjonen skal returnere: SANN gir den kumulative fordelingsfunksjonen; USANN gir funksjonen for sannsynlig tetthet"}, "F.DIST.RT": {"a": "(x; frihetsgrader1; frihetsgrader2)", "d": "Returnerer (den høyresidige) F-sannsynlighetsfordelingen (spredningsgraden) for to datasett", "ad": "er verdien du vil evaluere funksjonen for, et ikke-negativt tall!er tellerens frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10!er nevnerens frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10"}, "F.INV": {"a": "(x; frihetsgrader1; frihetsgrader2)", "d": "Returnerer den inverse av (den venstresidige) F-sannsynlighetsfordelingen. hvis p = F.FORDELING(x,...), sål F.INV(p,...) = x", "ad": "er en sannsynlighet knyttet til den kumulative F-fordelingen, et tall fra 0 til og med 1!er tellerens frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10!er nevnerens frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10"}, "F.INV.RT": {"a": "(sannsynlighet; frihetsgrader1; frihetsgrader2)", "d": "Returnerer den inverse av (den høyresidige) F-sannsynlighetsfordelingen. hvis p = F.FORDELING.H(x,...), så F.INV.H(p,...) = x", "ad": "er en sannsynlighet knyttet til den kumulative F-fordelingen, et tall fra 0 til og med 1!er tellerens frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10!er nevnerens frihetsgrader, et tall mellom 1 og 10^10, unntatt 10^10"}, "F.TEST": {"a": "(matrise1; matrise2)", "d": "Returnerer resultatet av en F-test, den tosidige sannsynligheten for at variansene i matrise1 og matrise2 ikke er signifikant forskjellige", "ad": "er den første matrisen eller det første området med data og kan være tall eller navn, matriser eller referanser som inneholder tall (tomme ignoreres)!er den andre matrisen eller det andre dataområdet og kan være tall eller navn, matriser eller referanser som inneholder tall (tomme ignoreres)"}, "FISHER": {"a": "(x)", "d": "Returnerer Fisher-<PERSON><PERSON><PERSON><PERSON>", "ad": "er verdien du vil finne transformasjonen for, et tall mellom -1 og 1, unntatt -1 og 1"}, "FISHERINV": {"a": "(y)", "d": "Returnerer den inverse av Fisher-transformasjonen. Hvis y = FISHER(x), er FISHERINV(y) = x", "ad": "er verdien du vil utføre den inverse av transformasjonen på"}, "FORECAST": {"a": "(x; kjente_y; kjente_x)", "d": "<PERSON><PERSON><PERSON><PERSON> el<PERSON> forutsier en fremtidig verdi langs en lineær trend på grunnlag av eksisterende verdier", "ad": "er datapunktet du vil forutsi en verdi for og må være en numerisk verdi!er den avhengige matrisen eller det avhengige området med numeriske data!er den uavhengige matrisen eller det uavhengige området med numeriske data. Variansen av kjente_x kan ikke være null"}, "FORECAST.ETS": {"a": "(m<PERSON><PERSON><PERSON>; verdier; tidslinje; [sesongavhengighet]; [data<PERSON><PERSON><PERSON>ring]; [aggregasjon])", "d": "Returnerer den prognostiserte verdien for en bestemt fremtidig måldato ved hjelp av eksponentiell glatting.", "ad": "er datapunktet som Spreadsheet Editor beregner en verdi for. Verdien skal ha samme mønster som verdier på tidslinjen.!er matrisen eller området med numeriske data som du beregner.!er den uavhengige matrisen eller området med numeriske data. Datoene på tidslinjen må ha et konsekvent trinn mellom seg og kan ikke være null.!er en numerisk verdi som indikerer lengden på det sesongbetonte mønsteret. Standardverdien 1 indikerer at sesongavhengighet oppdages automatisk.!er en valgfri verdi for håndtering av manglende verdier. Standardverdien 1 erstatter manglende verdier ved interpolasjon, og 0 erstatter dem med nuller.!er en valgfri numerisk verdi for å akkumulere manglende verdier med samme tidsstempel. Hvis den er tom, bruker Spreadsheet Editor gjennomsnittet av verdiene."}, "FORECAST.ETS.CONFINT": {"a": "(m<PERSON><PERSON><PERSON>; verdier; tidslinje; [konfidensnivå]; [sesongavhengighet]; [data<PERSON><PERSON><PERSON>ring]; [aggregasjon])", "d": "Returnerer kon<PERSON><PERSON><PERSON><PERSON><PERSON>let for prognoseverdien for den angitte måldatoen.", "ad": "er datapunktet som Spreadsheet Editor beregner en verdi for. Verdien skal ha samme mønster som verdier på tidslinjen.!er matrisen eller området med numeriske data som du beregner.!er den uavhengige matrisen eller området med numeriske data. Datoene på tidslinjen må ha et konsekvent trinn mellom seg og kan ikke være null.!er et tall mellom 0 og 1, som viser konfidensnivået for det beregnede konfidensintervallet. Standardverdien er .95.!er en numerisk verdi som indikerer lengden på det sesongbetonte mønsteret. Standardverdien 1 indikerer at sesongavhengighet oppdages automatisk.!er en valgfri verdi for håndtering av manglende verdier. Standardverdien 1 erstatter manglende verdier ved interpolasjon, og 0 erstatter dem med nuller.!er en valgfri numerisk verdi for å akkumulere manglende verdier med samme tidsstempel. Hvis den er tom, bruker Spreadsheet Editor gjennomsnittet av verdiene."}, "FORECAST.ETS.SEASONALITY": {"a": "(verdier; tidslinje; [data<PERSON><PERSON><PERSON><PERSON>]; [aggregasjon])", "d": "Returnerer hele det gjentakende mønsteret som applikasjon oppdager for den angitte tidsserien.", "ad": "er matrisen eller området med numeriske data som du forutsier.!er den uavhengige matrisen eller området med numeriske data. Datoene i tidslinjen må ha et konsekvent trinn mellom dem og kan ikke være null.!er en valgfri verdi for håndtering av manglende verdier. Standardverdien 1 erstatter manglende verdier ved interpolasjon, og 0 erstatter dem nuller.!er en valgfri numerisk verdi for å akkumulere flere verdier med samme tidsstempel. Hvis den er tom, bruker Spreadsheet Editor gjennomsnittet av verdiene."}, "FORECAST.ETS.STAT": {"a": "(verdier; tidslinje; statistikktype; [seasongavhengighet]; [datafullf<PERSON>ring]; [aggregasjon])", "d": "Returnerer den forespurte statistikken for prognosen.", "ad": "er matrisen eller området med numeriske data du forutser.!er den uavhengige matrisen eller området med numeriske data. Datoene på tidslinjen må ha et konsekvent trinn mellom dem og kan ikke være null.!er et tall mellom 1 og 8, som angir hvilken statistikk Spreadsheet Editor som skal returneres for den beregnede prognosen.!er en valgfri numerisk verdi som angir lengden på sesongmønsteret. Standardverdien 1 indikerer at sesongavhengighet oppdages automatisk.!er en valgfri verdi for håndtering av manglende verdier. Standardverdien på 1 erstatter manglende verdier med interpolering, og 0 erstatter dem med nuller.!er en valgfri numerisk verdi for aggregering av flere verdier med samme tidsangivelse. Hvis tom, vil Spreadsheet Editor beregne gjennomsnittet av verdiene."}, "FORECAST.LINEAR": {"a": "(x; kjente_y; kjente_x)", "d": "<PERSON><PERSON><PERSON><PERSON> el<PERSON> forutsier en fremtidig verdi langs en lineær trend på grunnlag av eksisterende verdier", "ad": "er datapunktet du vil forutsi en verdi for, og må være en numerisk verdi!er den avhengige matrisen eller det avhengige området med numeriske data!er den uavhengige matrisen eller det uavhengige området med numeriske data. Variansen av kjente_x kan ikke være null"}, "FREQUENCY": {"a": "(datamatrise; klassematrise)", "d": "<PERSON><PERSON><PERSON><PERSON> hvor ofte verdier forekommer i et område med verdier, og returnerer en loddrett matrise med tall med ett element mer enn klassematrise", "ad": "er en matrise eller en referanse til et sett verdier du vil beregne frekvensene i (tomme celler og tekst ignoreres)!er en matrise av eller referanse til intervaller du vil gruppere verdiene i datamatrisen i"}, "GAMMA": {"a": "(x)", "d": "Returnerer gammafunksjonsverdien", "ad": "er verdien du vil beregne gamma for"}, "GAMMADIST": {"a": "(x; alfa; beta; kumulativ)", "d": "Returnerer gammafordelingen", "ad": "er verdien du vil evaluere fordelingen for, et ikke-negativt tall!er en parameter for fordelingen, et positivt tall!er en parameter for fordelingen, et positivt tall. Hvis beta = 1, returnerer GAMMAFORDELING standard gammafordeling!er en logisk verdi. SANN returnerer den kumulative fordelingsfunksjonen. USANN eller utelatt returnerer punktsannsynligheten"}, "GAMMA.DIST": {"a": "(x; alfa; beta; kumulativ)", "d": "Returnerer gammafordelingen", "ad": "er verdien der du vil evaluere fordelingen, et ikke-negativt tall!er en parameter for fordelingen, et positivt tall!er en parameter for fordelingen, et positivt tall. Hvis beta = 1, returnerer GAMMA.FORDELING standard gammafordeling!er en logisk verdi: SANN returnerer den kumulative fordelingsfunksjonen, USANN eller utelatt returnerer punktsannsynlighet"}, "GAMMAINV": {"a": "(sannsynlighet; alfa; beta)", "d": "Returnerer den inverse av den kumulative gammafordelingen. Hvis p = GAMMAFORDELING(x...), er GAMMAINV(p,...) = x", "ad": "er sannsynligheten assosiert med gammafordelingen, et tall fra og med 0 til og med 1!er en parameter for fordelingen, et positivt tall!er en parameter for fordelingen, et positivt tall. Hvis beta = 1, returnerer funksjonen GAMMAINV den inverse av standard gammafordeling"}, "GAMMA.INV": {"a": "(sannsynlighet; alfa; beta)", "d": "Returnerer den inverse av den kumulative gammafordelingen: hvis p = GAMMA.FORDELING(x,...), så GAMMA.INV(p,...) = x", "ad": "er sannsynligheten som er knyttet til gammafordelingen, et tall fra 0 til og med 1!er en parameter for fordelingen, et positivt tall!er en parameter for fordelingen, et positivt tall. Hvis beta = 1, returnerer GAMMA.INV den inverse av standard gammafordeling"}, "GAMMALN": {"a": "(x)", "d": "Returnerer den naturlige logaritmen til gammafunksjonen", "ad": "er verdien du vil beregne GAMMALN for, et positivt tall"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Returnerer den naturlige logaritmen til gammafunksjonen", "ad": "er verdien (et positivt tall) du vil beregne GAMMALN.PRESIS for"}, "GAUSS": {"a": "(x)", "d": "Returnerer 0,5 mindre enn standard kumulativ normalfordeling", "ad": "er verdien du ønsker fordelingen for"}, "GEOMEAN": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer den geometriske middelverdien for en matrise eller et område med positive numeriske data", "ad": "er 1 til 255 tall eller navn, matriser eller referanser som inneholder tall som du vil finne gjennomsnittet av"}, "GROWTH": {"a": "(kjente_y; [kjente_x]; [nye_x]; [konst])", "d": "Returnerer tall i en eksponentiell veksttrend som samsvarer med kjente datapunkter", "ad": "er et sett y-verdier som du allerede kjenner i forholdet y = b*m^x, en matrise eller et intervall av positive tall!er et valgfritt sett med x-verdier som du kanskje allerede kjenner i forholdet y = b*m^x, en matrise eller et intervall av samme størrelse som kjente_y!er nye x-verdier som du vil at funksjonen VEKST skal returnere de tilsvarende y-verdiene for!er en logisk verdi. Konstanten b beregnes normalt hvis konst = SANN, b settes til 1 hvis konst = USANN eller utelatt"}, "HARMEAN": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer den harmoniske middelverdien for et datasett med positive tall, det vil si den resiproke verdien av den aritmetiske middelverdien av de resiproke verdiene", "ad": "er 1 til 255 tall eller navn, matriser eller referanser som inneholder tall som du vil finne det harmoniske gjennomsnittet for"}, "HYPGEOM.DIST": {"a": "(utvalg_s; utvalgsstørrelse; suksesser; populasjonsstørrelse; kumulative)", "d": "Returnerer den hypergeometriske fordelingen", "ad": "er antallet suksesser i utvalget!er størrelsen på utvalget!er antallet vellykkede forsøk i populasjonen!er populasjonens størrelse!er en logisk verdi: Bruk SANN for kumulativ fordeling, bruk USANN for sannsynlig tetthet"}, "HYPGEOMDIST": {"a": "(utvalg_s; utvalgsstørrelse; suksesser; populasjonsstørrelse)", "d": "Returnerer den hypergeometriske fordelingen", "ad": "er antallet suksesser i utvalget!er størrelsen på utvalget!er antallet vellykkede forsøk i populasjonen!er populasjonens størrelse"}, "INTERCEPT": {"a": "(kjente_y; kjente_x)", "d": "Be<PERSON>gner punktet hvor en linje skjærer y-aksen ved å bruke en regresjonslinje for beste tilpasning som tegnes gjennom de kjente x- og y-verdiene", "ad": "er det avhengige settet observasjoner eller data og kan være tall eller navn, matriser eller referanser som inneholder tall!er det uavhengige settet observasjoner eller data og kan være tall eller navn, matriser eller referanser som inneholder tall"}, "KURT": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer kurtosen til et datasett", "ad": "er 1 til 255 tall eller navn, matriser eller referanser som inneholder tall som du vil finne kurtosen for"}, "LARGE": {"a": "(matrise; n)", "d": "Returnerer den n-te høyeste verdien i et datasett, for eks<PERSON>pel det femte høyeste tallet", "ad": "er matrisen eller området med numeriske data du vil finne den n-te høyeste verdien for!er posisjonen (fra den høyeste verdien) i matrisen eller celleområdet som inneholder verdien som skal returneres"}, "LINEST": {"a": "(kjente_y; [kjente_x]; [konst]; [statistikk])", "d": "Returnerer statistikk som beskriver en lineær trend som samsvarer med kjente datapunkter, ved å tilpasse en rett linje beregnet med minste kvadraters metode", "ad": "er et sett y-verdier som du allerede kjenner i forholdet y = mx + b!er et valgfritt sett med x-verdier som du kanskje allerede kjenner i forholdet y = mx + b!er en logisk verdi. Konstanten b beregnes normalt hvis konst = SANN eller utelatt, b settes til 0 hvis konst = USANN!er en logisk verdi. Returner flere regresjonsdata = SANN, returner m-koeffisienter og konstanten b = USANN eller utelatt"}, "LOGEST": {"a": "(kjente_y; [kjente_x]; [konst]; [statistikk])", "d": "Returnerer statistikk som beskriver en eksponentiell kurve som samsvarer med kjente datapunkter", "ad": "er et sett y-verdier som du allerede kjenner i forholdet y = b*m^x!er et valgfritt sett med x-verdier som det kan hende du allerede kjenner i forholdet y = b*m^x!er en logisk verdi. Konstanten b beregnes normalt hvis konst = SANN eller utelatt. B settes til 1 hvis konst = USANN!er en logisk verdi. Returner flere regresjonsdata = SANN, returner m-koeffisienter og konstanten b = USANN eller utelatt"}, "LOGINV": {"a": "(sannsynlighet; median; standardavvik)", "d": "Returnerer den inverse av den lognormale fordelingsfunksjonen til x, hvor In(x) har normalfordeling med parameterne median og standardavvik", "ad": "er en sannsynlighet knyttet til den lognormale fordelingen, et tall fra og med 0 til og med 1!er gjennomsnittet for ln(x)!er standardavviket for ln(x), et positivt tall"}, "LOGNORM.DIST": {"a": "(x; gje<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; kumulativ)", "d": "Returnerer den lognormale fordelingen av x, der ln(x) er normalfordelt med parameterne Gjennomsnitt og Standardavvik", "ad": "er verdien du vil evaluere funksjonen for, et positivt tall!er gjennomsnittet til ln (x)!er standardavviket for ln(x), et positivt tall!er en logisk verdi: Bruk SANN for kumulativ fordeling, bruk USANN for sannsynlig tetthet"}, "LOGNORM.INV": {"a": "(sannsynlighet; middelverdi; standardavvik)", "d": "Returnerer den inverse av den lognormale fordelingsfunksjonen, hvor In(x) har normalfordeling med parameterne middelverdi og standardavvik", "ad": "er en sannsynlighet knyttet til den lognormale fordelingen, et tall fra og med 0 til og med 1!er middelverdien for ln(x)!er standardavviket for ln(x), et positivt tall"}, "LOGNORMDIST": {"a": "(x; median; standardavvik)", "d": "Returnerer den kumulative lognormale fordelingen for x, hvor In(x) har normalfordeling med parameterne median og standardavvik", "ad": "er verdien funksjonen skal evalueres for, et positivt tall!er gjennomsnittet for ln(x)!er standardavviket for ln(x), et positivt tall"}, "MAX": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer maksimumsverdien i et verdisett. Ignorerer logiske verdier og tekst", "ad": "er 1 til 255 tall, tomme celler, logiske verdier eller teksttall du vil finne maksimumsverdien for"}, "MAXA": {"a": "(verdi1; [verdi2]; ...)", "d": "Returnerer den høyeste verdien i et verdisett. Ignorerer ikke logiske verdier og tekst", "ad": "er 1 til 255 tall, tomme celler, logiske verdier eller teksttall du vil finne maksimumsverdien for"}, "MAXIFS": {"a": "(maks_områ<PERSON>; vilkår_område; vilk<PERSON>r; ...)", "d": "Returnerer maksimumsverdien mellom cellene som angis av et gitt sett med betingelser eller vilkår", "ad": "cellene der du finner maksimumsverdien!er celleområdet du vil evaluere for den bestemte betingelsen!er betingelsen eller vilkåret i form av et tall, et uttrykk eller en tekst som definerer hvilke celler som skal inkluderes når maksimumsverdien fastslås"}, "MEDIAN": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer medianen for settet av angitte verdier, altså den midterste verdien i rekken (eller gjennomsnittet av de to midterste) når verdiene er ordnet i stigende rekkefølge", "ad": "er 1 til 255 tall eller navn, matriser eller referanser som inneholder tall du vil finne medianen for"}, "MIN": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer det laveste tallet i et verdisett. Ignorerer logiske verdier og tekst", "ad": "er 1 til 255 tall, tomme celler, logiske verdier eller teksttall du vil finne minimumsverdien for"}, "MINA": {"a": "(verdi1; [verdi2]; ...)", "d": "Returnerer den laveste verdien i et verdisett. Ignorerer ikke logiske verdier og tekst", "ad": "er 1 til 255 tall, tomme celler, logiske verdier eller teksttall du vil finne minimumsverdien for"}, "MINIFS": {"a": "(min_områ<PERSON>; vilk<PERSON>r_område; vilk<PERSON><PERSON>; ...)", "d": "Returnerer minimumssverdien mellom cellene som angis av et gitt sett med betingelser eller vilkår", "ad": "cellene der du finner minimumssverdien!er celleområdet du vil evaluere for den bestemte betingelsen!er betingelsen eller vilkåret i form av et tall, et uttrykk eller tekst som definerer hvilke celler som skal inkluderes når minimumsverdien fastslås"}, "MODE": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer den hyppigst forekommende verdien i en matrise eller et dataområde", "ad": "er 1 til 255 tall eller navn, matriser eller referanser som inneholder tall, som du vil finne modus for"}, "MODE.MULT": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer en loddrett matrise av de hyppigste verdiene, eller de som gjentas oftest, i en matrise eller dataområde. For en vannrett matrise bruker du =TRANSPONER(MODUS.MULT(tall1,tall2,...))", "ad": "er 1 til 255 tall eller navn, matriser eller referanser som inneholder tall du vil bruke modusen for"}, "MODE.SNGL": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer den hyppigst forekommende, eller mest repeterte, verdien i en matrise eller et dataområde", "ad": "er 1 til 255 tall eller navn, matriser eller referanser som inneholder tall som du vil finne modus for"}, "NEGBINOM.DIST": {"a": "(tall_f; tall_s; sannsynlighet_s; kumulativ)", "d": "Returnerer den negative binomiske fordelingen, sannsy<PERSON><PERSON><PERSON><PERSON>n for at det vil være tall_f fiaskoer før tall_s-te suksess, der sannsynlighet_s er sannsynligheten for suksess", "ad": "er antall mislykkede forsøk!er terskeltallet for vellykkede forsøk!er sannsynligheten for et vellykket forsøket, et tall mellom 0 og 1!er en logisk verdi: Bruk SANN for kumulativ fordelingsfunksjon, bruk USANN for punktsannsynlighet"}, "NEGBINOMDIST": {"a": "(tall_f; tall_s; sannsynlighet_s)", "d": "Returnerer negativ binomisk fordeling, s<PERSON><PERSON> for at det vil bli tall_f fiaskoer før tall_s-te suksess, med sannsynlighet_s sjanse for suksess, hvor sannsynlighet_s er sannsynligheten for suksess", "ad": "er antallet fiaskoer!er terskeltallet for vellykkede forsøk!er sannsynligheten for at forsøket lykkes, et tall mellom 0 og 1"}, "NORM.DIST": {"a": "(x; median; standardavvik; kumulativ)", "d": "Returnerer normalfordelingen for angitt middelverdi og standardavvik", "ad": "er verdien du vil bruke fordelingen for!er den aritmetiske middelverdien for fordelingen!er standardavviket for fordelingen, et positivt tall!er en logisk verdi: Bruk SANN for den kumulative fordelingsfunksjonen, bruk USANN for funksjonen for sannsynlig tetthet"}, "NORMDIST": {"a": "(x; median; standardavvik; kumulativ)", "d": "Returnerer den kumulative normalfordelingen for angitt median og standardavvik", "ad": "er verdien du vil finne fordelingen for!er medianen (den aritmetiske middelverdien) av fordelingen!er fordelingens standardavvik, et positivt tall!er en logisk verdi. For kumulativ fordeling bruker du SANN. For punktsannsynlighet bruker du USANN"}, "NORM.INV": {"a": "(sannsynlighet; median; standardavvik)", "d": "Returnerer den inverse av den kumulative normalfordelingen for angitt gjennomsnitt og standardavvik", "ad": "er en sannsynlighet for normalfordelingen, et tall fra og med 0 til og med 1!er gjennomsnittet (den aritmetiske middelverdien) av fordelingen!er standardavviket for fordelingen, et positivt tall"}, "NORMINV": {"a": "(sannsynlighet; median; standardavvik)", "d": "Returnerer den inverse av den kumulative normalfordelingen for angitt gjennomsnitt og standardavvik", "ad": "er en sannsynlighet for normalfordelingen, et tall fra og med 0 til og med 1!er gjennomsnittet (den aritmetiske middelverdien) av fordelingen!er standardavviket for fordelingen, et positivt tall"}, "NORM.S.DIST": {"a": "(z; kumulativ)", "d": "Returnerer standard normalfordeling (har en middelverdi lik null og et standardavvik lik én)", "ad": "er verdien du ønsker fordelingen for!er en logisk verdi som funksjonen skal returnere: SANN gir funksjonen for kumulativ fordeling; USANN gir funksjonen for sannsynlig tetthet"}, "NORMSDIST": {"a": "(z)", "d": "Returnerer standard kumulativ normalfordeling (der gjennomsnittet er lik null og standardavviket er én)", "ad": "er verdien du vil finne fordelingen for"}, "NORM.S.INV": {"a": "(sannsynlighet)", "d": "Returnerer den inverse av standard kumulativ normalfordeling (har null som middelverdi og én som standardavvik)", "ad": "er en sannsynlighet som tilsvarer normalfordelingen, et tall fra og med 0 til og med 1"}, "NORMSINV": {"a": "(sannsynlighet)", "d": "Returnerer den inverse av standard kumulativ normalfordeling (har et gjennomsnitt på null og standardavvik på én)", "ad": "er en sannsynlighet for normalfordelingen, et tall fra og med 0 til og med 1"}, "PEARSON": {"a": "(matrise1; matrise2)", "d": "Returnerer produkt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> r", "ad": "er et uavhengig verdisett!er et avhengig verdisett"}, "PERCENTILE": {"a": "(matrise; n)", "d": "Returnerer det n-te persentilet av verdiene i et område", "ad": "er matrisen eller dataområdet som definerer relativ fordeling!er persentilverdien som ligger i området fra og med 0 til og med 1"}, "PERCENTILE.EXC": {"a": "(matrise; k)", "d": "Returnerer den k-te persentilen av verdiene i et område, der k er i området 0..1, eksklusive", "ad": "er matrisen eller dataområdet som definerer relativ fordeling!er persentilverdien som er mellom 0 og 1, inklusive"}, "PERCENTILE.INC": {"a": "(matrise; k)", "d": "Returnerer den k-te persentilen av verdiene i et område, der k er i området 0..1, inklusive", "ad": "er matrisen eller dataområdet som definerer relativ fordeling!er persentilverdien som er mellom 0 og 1, inklusive"}, "PERCENTRANK": {"a": "(matrise; x; [gje<PERSON><PERSON>_sifre])", "d": "Returnerer rangeringen av en verdi i et datasett som en prosent av datasettet", "ad": "er matrisen eller dataområdet med numeriske verdier som definerer relativ fordeling!er verdien du vil vite rangeringen for!er en valgfri verdi som identifiserer antallet signifikante sifre i den prosentandelen som returneres. Hvis argumentet utelates, brukes tre sifre (0,xxx %)"}, "PERCENTRANK.EXC": {"a": "(matrise; x; [signifikans])", "d": "Returnerer rangeringen av en verdi i et datasett som en prosentdel av datasettet som en prosentdel (0..1, eksklusive) av datasettet", "ad": "er matrisen eller dataområdet med numeriske verdier som definerer relativ fordeling!er verdien du vil finne rangeringen for!er en valgfri verdi som identifiserer antallet signifikante sifre for returnert prosentandel, tre sifre hvis utelatt (0,xxx %)"}, "PERCENTRANK.INC": {"a": "(matrise; x; [signifikans])", "d": "Returnerer rangeringen av en verdi i et datasett som en prosentdel av datasettet som en prosentdel (0..1, inklusive) av datasettet", "ad": "er matrisen eller dataområdet med numeriske verdier som definerer relativ fordeling!er verdien du vil finne rangeringen for!er en valgfri verdi som identifiserer antallet signifikante sifre for returnert prosentandel, tre sifre hvis utelatt (0,xxx %)"}, "PERMUT": {"a": "(antall; valgt_antall)", "d": "Returnerer antall permutasjoner for et gitt antall objekter som kan velges fra det totale antall objekter", "ad": "er det totale antall objekter!er antall objekter i hver permutasjon"}, "PERMUTATIONA": {"a": "(antall; valgt_antall)", "d": "Returnerer antallet permutasjoner for et gitt antall objekter (med repetisjoner) som kan velges fra det totale antallet objekter", "ad": "er det totale antallet objekter!er antall objekter i hver permutasjon"}, "PHI": {"a": "(x)", "d": "Returnerer verdien av tetthetsfunksjonen for en standard normalfordeling", "ad": "er tallet du vil finne tetthet for standard normalfordeling for"}, "POISSON": {"a": "(x; median; kumulativ)", "d": "Returnerer Poisson-fordelingen", "ad": "er antallet hendelser!er den forventede numeriske verdien, et positivt tall!er en logisk verdi. For kumulativ Poisson-sannsynlighet bruker du SANN. For Poisson-punktsannsynlighet bruker du USANN"}, "POISSON.DIST": {"a": "(x; median; kumulativ)", "d": "Returnerer Poisson-fordelingen", "ad": "er antallet hendelser!er den forventede numeriske verdien, et positivt tall!er en logisk verdi. For kumulativ Poisson-sannsynlighet bruker du SANN, for Poisson-punktsannsynlighet bruker du USANN"}, "PROB": {"a": "(x_omr<PERSON><PERSON>; utfallsområ<PERSON>; nedre_grense; [øvre_grense])", "d": "Returnerer sann<PERSON><PERSON><PERSON><PERSON><PERSON>n for at en verdi ligger mellom to ytterpunkter i et område eller er lik et nedre ytterpunkt", "ad": "er området med numeriske verdier av x som det er knyttet sannsynligheter til!er et et sett sannsynligheter knyttet til verdiene i x_område, verdier mellom 0 og 1, unntatt 0!er den nedre grensen for verdien du vil finne en sannsynlighet for!er den valgfrie øvre grensen for verdien. Hvis argumentet utelates, returnerer SANNSYNLIG sannsynligheten for at verdiene i x_område er lik nedre_grense"}, "QUARTILE": {"a": "(matrise; kvar<PERSON>)", "d": "Returnerer kvartilen for et datasett", "ad": "er matrisen eller celleområdet med numeriske verdier du vil finne kvartilverdien for!er et tall. Minimumsverdi = 0, fø<PERSON><PERSON> kvartil = 1, medianver<PERSON> = 2, tred<PERSON> kvartil = 3, maks<PERSON><PERSON>verdi = 4"}, "QUARTILE.INC": {"a": "(matrise; kvart)", "d": "Returnerer kvartilen til et datasett, basert på persentilverdier fra 0..1, inklusive", "ad": "er matrisen eller celleområdet med numeriske verdier du vil finne kvartilverdien for!er et tall: minimumsverdi = 0; fø<PERSON><PERSON> kvartil = 1; medianverdi = 2; tred<PERSON> kvartil = 3; maks<PERSON><PERSON>verdi = 4"}, "QUARTILE.EXC": {"a": "(matrise; kvart)", "d": "Returnerer kvartilen til et datasett basert på persentilverdier fra 0..1, eksklusive", "ad": "er matrisen eller celleområdet med numeriske verdier du vil finne kvartilverdien for!er et tall: minimumsverdi = 0; fø<PERSON><PERSON> kvartil = 1; medianverdi = 2; tred<PERSON> kvartil = 3; maks<PERSON><PERSON>verdi = 4"}, "RANK": {"a": "(tall; ref; [rekke<PERSON><PERSON><PERSON>])", "d": "Returnerer rangeringen av et tall i en liste over tall. Tallet rangeres etter størrelsen i forhold til andre verdier i listen", "ad": "er tallet du vil finne rangeringen til!er en matrise med, eller en referanse til, en liste over tall. Ikke-numeriske verdier ignoreres!er et tall. Rangering i listen sortert synkende = 0 eller utelatt. Rangering i listen sortert stigende = en hvilken som helst annen verdi enn null"}, "RANK.AVG": {"a": "(tall; ref; [rekke<PERSON><PERSON><PERSON>])", "d": "Returnerer rangeringen for et tall i en liste med tall: st<PERSON><PERSON><PERSON> i forhold til andre verdier i listen, hvis mer enn én verdi har samme rangering, returneres gjennomsnittlig rangering", "ad": "er tallet du vil finne rangeringen for!er en matrise av, eller referanse til, en liste med tall. Ikke-numeriske verdier ignoreres!er et tall: rangert i listen som er sortert synkende = 0 eller utelatt, rangert i listen som er sortert stigende = en annen verdi enn null"}, "RANK.EQ": {"a": "(tall; ref; [rekke<PERSON><PERSON><PERSON>])", "d": "Returnerer rangeringen for et tall i en liste med tall: st<PERSON><PERSON><PERSON> i forhold til andre verdier i listen. Hvis mer enn én verdi har samme rangering, returneres den høyeste rangeringen for det verdisettet", "ad": "er tallet du vil finne rangeringen for!er en matrise av, eller en referanse til, en liste med tall. Ikke-numeriske verdier ignoreres!er et tall: rangert i listen som er sortert synkende = 0 eller utelatt, rangert i listen som er sortert stigende = en annen verdi enn null"}, "RSQ": {"a": "(kjente_y; kjente_x)", "d": "Returnerer kvadratet av produktmomentkorrelasjonskoeffisienten Pearsons r gjennom de gitte datapunktene", "ad": "er en matrise eller et område som inneholder datapunkter, og kan være tall eller navn, matriser eller referanser som inneholder tall!er en matrise eller et område som inneholder datapunkter, og kan være tall eller navn, matriser eller referanser som inneholder tall"}, "SKEW": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer skjevheten for en fordeling, et mål for en fordelings asymmetri i forhold til gjennomsnittet", "ad": "er 1 til 255 tall eller navn, matriser eller referanser som inneholder tall som du vil finne skjevheten for"}, "SKEW.P": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer skje<PERSON><PERSON><PERSON> for en fordeling basert på en populasjon, et mål for en fordelingsasymmetri i forhold til gjennomsnittet", "ad": "er 1 til 254 tall eller navn, matriser eller referanser som inneholder tall som du vil finne populasjonsskjevheten for"}, "SLOPE": {"a": "(kjente_y; kjente_x)", "d": "Returnerer stigningstallet for den lineære regresjonslinjen gjennom de gitte datapunktene", "ad": "er en matrise eller et celleområde som inneholder numerisk avhengige datapunkter, og kan være tall eller navn, matriser eller referanser som inneholder tall!er settet med uavhengige datapunkter og kan være tall eller navn, matriser eller referanser som inneholder tall"}, "SMALL": {"a": "(matrise; n)", "d": "Returnerer den n-te laveste verdien i et datasett, for eks<PERSON><PERSON> det femte laveste tallet", "ad": "er matrisen eller området med numeriske data du vil finne den n-te laveste verdien for!er posisjonen (fra den laveste verdien) i matrisen eller celleområdet som inneholder verdien som skal returneres"}, "STANDARDIZE": {"a": "(x; median; standardavvik)", "d": "Returnerer en normalisert verdi fra en fordeling spesifisert ved gjennomsnitt og standardavvik", "ad": "er verdien du vil normalisere!er gjennomsnittet (den aritmetiske middelverdien) av fordelingen!er standardavviket for fordelingen, et positivt tall"}, "STDEV": {"a": "(tall1; [tall2]; ...)", "d": "Beregner standardavvik basert på et utvalg (ignorerer logiske verdier og tekst i utvalget)", "ad": "er 1 til 255 tall som svarer til et utvalg fra en populasjon, og kan være tall eller referanser som inneholder tall"}, "STDEV.P": {"a": "(tall1; [tall2]; ...)", "d": "Beregner standardavvik basert på hele populasjonen (ignorerer logiske verdier og tekst)", "ad": "er 1 til 255 tall som utgjør en populasjon, og kan være tall eller referanser som inneholder tall"}, "STDEV.S": {"a": "(tall1; [tall2]; ...)", "d": "Beregner standardavvik basert på et utvalg (ignorerer logiske verdier og tekst i utvalget)", "ad": "er 1 til 255 tall som svarer til et utvalg fra en populasjon, og kan være tall eller referanser som inneholder tall"}, "STDEVA": {"a": "(verdi1; [verdi2]; ...)", "d": "Estimerer standardavvik basert på et utvalg, inkludert logiske verdier og tekst. Tekst og den logiske verdien USANN har verdien 0, den logiske verdien SANN har verdien 1", "ad": "er 1 til 255 verdier som utgjør et utvalg fra en populasjon og kan være verdier eller navn eller referanser til verdier"}, "STDEVP": {"a": "(tall1; [tall2]; ...)", "d": "Beregner standardavvik basert på hele populasjonen gitt som argumenter (ignorerer logiske verdier og tekst)", "ad": "er 1 til 255 tall som tilsvarer en populasjon, og kan være tall eller referanser som inneholder tall"}, "STDEVPA": {"a": "(verdi1; [verdi2]; ...)", "d": "Beregner standardavvik basert på at argumentene beskriver hele populasjonen, inkludert logiske verdier og tekst. Tekst og den logiske verdien USANN har verdien 0, den logiske verdien SANN har verdien 1", "ad": "er 1 til 255 verdier som utgjør en populasjon, og kan være verdier eller navn, matriser eller referanser som inneholder verdier"}, "STEYX": {"a": "(kjente_y; kjente_x)", "d": "Returnerer standardfeilen til den forventede y-verdien for hver x i en regresjon", "ad": "er en matrise eller et område som inneholder avhengige datapunkter, og kan være tall eller navn, matriser eller referanser som inneholder tall!er en matrise eller et område som inneholder uavhengige datapunkter, og kan være tall eller navn, matriser eller referanser som inneholder tall"}, "TDIST": {"a": "(x; frihetsgrader; sider)", "d": "Returnerer Students t-fordeling", "ad": "er den numeriske verdien du vil evaluere fordelingen for!er et heltall som angir antallet frihetsgrader som karakteriserer fordelingen!angir hvor mange fordelingssider som skal returneres. Ensidig fordeling = 1. Tosidig fordeling = 2"}, "TINV": {"a": "(sannsynlighet; frihetsgrader)", "d": "Returnerer den tosidige inverse av Students t-fordelingen", "ad": "er sannsynligheten knyttet til den tosidige Students t-fordelingen, et tall fra og med 0 til og med 1!er et positivt heltall som indikerer antallet frihetsgrader som karakteriserer fordelingen"}, "T.DIST": {"a": "(x; frihetsgrader; kumulative)", "d": "Returnerer den venstre Students t-fordelingen", "ad": "er den numeriske verdien du vil evaluere fordelingen for!er et heltall som angir antall frihetsgrader som kjennetegner fordelingen!er en logisk verdi: Bruk SANN for kumulativ fordelingsfunksjon, bruk USANN for funksjon for sannsynlig tetthet"}, "T.DIST.2T": {"a": "(x; frihetsgrader)", "d": "Returnerer den tosidige Students t-fordelingen", "ad": "er den numeriske verdien du vil evaluere fordelingen for!er et heltall som angir antall frihetsgrader som karakteriserer fordelingen"}, "T.DIST.RT": {"a": "(x; frihetsgrader)", "d": "Returnerer den høyre Students t-fordelingen", "ad": "er den numeriske verdien du vil evaluere fordelingen for!er et heltall som angir antall frihetsgrader som karakteriserer fordelingen"}, "T.INV": {"a": "(sannsynlighet; frihetsgrader)", "d": "Returnerer den venstre inverse av Students t-fordelingen", "ad": "er sannsynligheten som er knyttet til den tosidige Students t-fordelingen, et tall mellom 0 og 1, inklusive!er et positivt heltall som angir antallet frihetsgrader som karakteriserer fordelingen"}, "T.INV.2T": {"a": "(sannsynlighet; frihetsgrader)", "d": "Returnerer den tosidige inverse av Students t-fordelingen", "ad": "er sannsynligheten som er knyttet til den tosidige Students t-fordelingen, et tall fra og med 0 til og med 1!er et positivt heltall som angir antall frihetsgrader som karakteriserer fordelingen"}, "T.TEST": {"a": "(matrise1; matrise2; sider; type)", "d": "Returnerer sannsynligheten knyttet til en Students t-test", "ad": "er det første datasettet!er det andre datasettet!angir hvor mange fordelingssider som skal returneres. Ensidig fordeling = 1, tosidig fordeling = 2!er typen t-test som skal utføres. Parvis = 1, to utvalg med lik varians (homoskedastisk) = 2, to utvalg med ulik varians = 3"}, "TREND": {"a": "(kjente_y; [kjente_x]; [nye_x]; [konst])", "d": "Returnerer tall i en lineær trend som samsvarer med kjente datapunkter, ved hjelp av minste kvadraters metode", "ad": "er et område eller en matrise med y-verdier du allerede kjenner i forholdet y = mx + b!er et valgfritt sett med x-verdier du kanskje allerede kjenner i forholdet y = mx + b, en matrise av samme størrelse som kjente_y!er et område eller en matrise med nye x-verdier som du vil at funksjonen TREND skal returnere de tilsvarende y-verdiene for!er en logisk verdi. Konstanten b beregnes normalt hvis konst = SANN eller utelatt, b settes til 0 hvis konst = USANN"}, "TRIMMEAN": {"a": "(matrise; prosent)", "d": "Returnerer det trimmede gjennomsnittet av et sett dataverdier", "ad": "er området eller matrisen som inneholder verdiene du vil trimme og beregne gjennomsnittet for!er andelen datapunkter som skal utelates fra toppen og bunnen av datasettet"}, "TTEST": {"a": "(matrise1; matrise2; sider; type)", "d": "Returnerer sannsynligheten knyttet til en Students t-test", "ad": "er det første datasettet!er det andre datasettet!angir hvor mange fordelingssider som skal returneres: ensidig fordeling = 1, tosidig fordeling = 2!er den typen t-test som skal utføres: partest = 1, to utvalg med lik varians (homoskedastisk) = 2, to utvalg med ulik varians = 3"}, "VAR": {"a": "(tall1; [tall2]; ...)", "d": "Beregner varians basert på et utvalg (ignorerer logiske verdier og tekst i utvalget)", "ad": "er 1 til 255 tallargumenter som svarer til et utvalg fra en populasjon"}, "VAR.P": {"a": "(tall1; [tall2]; ...)", "d": "Beregner varians basert på hele populasjonen (ignorerer logiske verdier og tekst i populasjonen)", "ad": "er 1 til 255 tallargumenter som svarer til en populasjon"}, "VAR.S": {"a": "(tall1; [tall2]; ...)", "d": "Beregner varians basert på et utvalg (ignorerer logiske verdier og tekst i utvalget)", "ad": "er 1 til 255 tallargumenter som tilsvarer et utvalg fra en populasjon"}, "VARA": {"a": "(verdi1; [verdi2]; ...)", "d": "Anslår varians basert på et utvalg, inkludert logiske verdier og tekst. Tekst og den logiske verdien USANN har verdi 0, den logiske verdien SANN har verdi 1", "ad": "er 1 til 255 verdiargumenter som tilsvarer et utvalg fra en populasjon"}, "VARP": {"a": "(tall1; [tall2]; ...)", "d": "Beregner varians basert på hele populasjonen (ignorerer logiske verdier og tekst i populasjonen)", "ad": "er 1 til 255 tallargumenter som tilsvarer en populasjon"}, "VARPA": {"a": "(verdi1; [verdi2]; ...)", "d": "Beregner varians basert på hele populasjonen, inkludert logiske verdier og tekst. Tekst og den logiske verdien USANN har verdien 0, den logiske verdien SANN har verdien 1", "ad": "er 1 til 255 verdiargumenter som utgjør en populasjon"}, "WEIBULL": {"a": "(x; alfa; beta; kumulativ)", "d": "Returnerer <PERSON><PERSON>-fordelingen", "ad": "er verdien du vil evaluere funksjonen for, et ikke-negativt tall!er en parameter for fordelingen, et positivt tall!er en parameter for fordelingen, et positivt tall!er en logisk verdi. For kumulativ fordeling bruker du SANN. For punktsannsynlighet bruker du USANN"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; kumulativ)", "d": "Returnerer <PERSON><PERSON>-fordelingen", "ad": "er verdien du vil evaluere funksjonen for, et ikke-negativt tall!er en parameter for fordelingen, et positivt tall!er en parameter for fordelingen, et positivt tall!er en logisk verdi. For den kumulative fordelingsfunksjonen bruker du SANN, for punktsannsynlighet bruker du USANN"}, "Z.TEST": {"a": "(matrise; x; [sigma])", "d": "Returnerer den ensidige P-verdien i en z-test", "ad": "er matrisen eller dataområdet du vil teste X mot!er verdien som skal testes!er populasjonens (kjente) standardavvik. Hvis argumentet utelates, brukes standardavviket for utvalget"}, "ZTEST": {"a": "(matrise; x; [sigma])", "d": "Returnerer den ensidige P-verdien for en z-test", "ad": "er matrisen eller dataområdet du vil teste X mot!er verdien som skal testes!er populasjonens (kjente) standardavvik. Hvis argumentet utelates, brukes standardavviket for utvalget"}, "ACCRINT": {"a": "(utstedt_dato; første_renteforfall; betalingsdato; rente; pari; frekvens; [basis]; [beregningsmetode])", "d": "Returnerer påløpte renter for et verdipapir som betaler periodisk rente.", "ad": "er verdipapirets utstedelsesdato uttrykt som et serienummer!er datoen for verdipapirets første renteforfall uttrykt som et serienummer!er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets årlige rente!er verdipapirets pariverdi!er antall rentebetalinger i året!er typen datosystem som skal brukes!er en logisk verdi: for akkumulert rente fra utstedelsesdato = SANN eller utelatt; for beregning fra siste rentebetalingsdato = USANN"}, "ACCRINTM": {"a": "(utstedt_dato; forfallsdato; rente; pari; [basis])", "d": "Returnerer den påløpte renten for et verdipapir som betaler rente ved forfall", "ad": "er verdipapirets utstedelsesdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er verdipapirets årlige rente!er verdipapirets pariverdi!er typen datosystem som skal brukes"}, "AMORDEGRC": {"a": "(kostnad; innkjøpsdato; første_periode; skrapverdi; periode; rente; [basis])", "d": "Returnerer forhold<PERSON><PERSON><PERSON> lineær avskrivning for et aktivum for hver regnskapsperiode.", "ad": "er aktivumets kostnad!er aktivumets innkjøpsdato!er sluttdatoen til den første perioden!er skrapverdien ved slutten av aktivumets levetid!er perioden!er avskrivingsrenten!årsbasis: 0 for år med 360 dager, 1 for faktisk, 3 for år med 365 dager."}, "AMORLINC": {"a": "(kostnad; innkjøpsdato; første_periode; skrapverdi; periode; rente; [basis])", "d": "Returnerer forhold<PERSON><PERSON><PERSON> lineær avskrivning for et aktivum for hver regnskapsperiode.", "ad": "er aktivumets kostnad!er aktivumets innkjøpsdato!er sluttdatoen til den første perioden!er skrapverdien ved slutten av aktivumets levetid!er perioden!er avskrivingsrenten!årsbasis: 0 for år med 360 dager, 1 for faktisk, 3 for år med 365 dager."}, "COUPDAYBS": {"a": "(betalingsdato; forfallsdato; frekvens; [basis])", "d": "Returnerer antall dager fra begynnelsen av den rentebærende perioden til betalingsdatoen", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er antallet rentebetalinger i året!er typen datosystem som skal brukes"}, "COUPDAYS": {"a": "(betalingsdato; forfallsdato; frekvens; [basis])", "d": "Returnerer antall dager i den rentebærende perioden som inneholder betalingsdatoen", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er antallet rentebetalinger i året!angir typen datosystem som skal brukes"}, "COUPDAYSNC": {"a": "(betalingsdato; forfallsdato; frekvens; [basis])", "d": "Returnerer antall dager fra betalingsdatoen til neste rentebetalingsdato", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er antallet rentebetalinger i året!er typen datosystem som skal brukes"}, "COUPNCD": {"a": "(betalingsdato; forfallsdato; frekvens; [basis])", "d": "Returnerer neste <PERSON>ato etter beta<PERSON>", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er antallet rentebetalinger i året!er typen datosystem som skal brukes"}, "COUPNUM": {"a": "(betalingsdato; forfallsdato; frekvens; [basis])", "d": "Returnerer antallet rentebetalinger mellom betalingsdatoen og forfallsdatoen", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er antallet rentebetalinger i året!er typen datosystem som skal brukes"}, "COUPPCD": {"a": "(betalingsdato; forfallsdato; frekvens; [basis])", "d": "Returnerer si<PERSON> <PERSON><PERSON> før <PERSON>", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er antallet rentebetalinger i året!er typen datosystem som skal brukes"}, "CUMIPMT": {"a": "(rente; antall_utbet; nåverdi; startperiode; sluttperiode; type)", "d": "Returnerer den kumulative renten på et lån mellom to perioder", "ad": "er rentesatsen!er det totale antallet utbetalinger!er nåverdien!er den første perioden i beregningen!er den siste perioden i beregningen!er en verdi som angir når utbetalingen forfaller"}, "CUMPRINC": {"a": "(rente; antall_utbet; nåverdi; startperiode; sluttperiode; type)", "d": "Returnerer den kumulative hovedstolen som er betalt på et lån mellom to perioder", "ad": "er rentesatsen!er det totale antallet utbetalinger!er nåverdien!er den første perioden i beregningen!er den siste perioden i beregningen!er en verdi som angir når utbetalingen forfaller"}, "DB": {"a": "(kostnad; restverdi; levetid; periode; [måned])", "d": "Returnerer avskrivningen for et aktivum for en angitt periode, ved hjelp av fast degressiv avskrivning", "ad": "er den opprinnelige kostnaden til aktivumet!er verdien ved slutten av avskrivningen!er antall perioder et aktivum blir avskrevet over (ofte kalt aktivumets økonomiske levetid)!er perioden du vil beregne avskrivningen for. Periode må angis med samme tidsenhet som Levetid!er antall måneder i det første året. Hvis argumentet måneder er utelatt, blir det satt til 12"}, "DDB": {"a": "(kostnad; restverdi; levetid; periode; [faktor])", "d": "Returnerer avskrivningen for et aktivum for en gitt periode, ved hjelp av dobbel degressiv avskrivning eller en annen metode du selv angir", "ad": "er den opprinnelige kostnaden til aktivumet!er verdien ved slutten av avskrivningen!er antall perioder et aktivum blir avskrevet over (ofte kalt aktivumets økonomiske levetid)!er perioden du vil beregne avskrivningen for. Periode må angis med samme tidsenhet som Levetid!er faktoren verdien avtar med. Hvis argumentet faktor utelates, blir det satt til 2 (dobbel degressiv avskrivning)"}, "DISC": {"a": "(betalingsdato; forfallsdato; pris; innløsningsverdi; [basis])", "d": "Returnerer diskontosa<PERSON>en for et verdipapir", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er verdipapirets pris per pålydende kr 100!er verdipapirets innløsningsverdi per pålydende kr 100!angir typen datosystem som brukes"}, "DOLLARDE": {"a": "(brøk_valuta; brøk)", "d": "Konverterer en valutapris uttrykt som en brøk til en valutapris uttrykt som et desimaltall", "ad": "er et tall uttrykt som en brøk!er heltallet som skal brukes i brøkens nevner"}, "DOLLARFR": {"a": "(desimal_valuta; brøk)", "d": "Konverterer en valutapris som et desimaltall til en valutapris uttrykt som en brøk", "ad": "er et desimaltall!er heltallet som skal brukes i brøkens nevner"}, "DURATION": {"a": "(betalingsdato; forfallsdato; rente; avkastning; frekvens; [basis])", "d": "Returnerer den årlige varigheten for et verdipapir med periodisk rentebetaling", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er verdipapirets årlige rente!er verdipapirets årlige avkastning!er antallet rentebetalinger per år!angir typen datosystem som brukes"}, "EFFECT": {"a": "(nominell_rente; perioder)", "d": "Returnerer den effektive årlige renten", "ad": "er den nominelle rentefoten!er antall sammensatte perioder per år"}, "FV": {"a": "(rente; antall_utbet; utbetaling; [nåverdi]; [type])", "d": "Returnerer den fremtidige verdien av en investering basert på periodiske, konstante utbetalinger og en fast rente", "ad": "er rentesatsen per periode. Bruk for eksempel 6 %/4 for kvartalsvise utbetalinger ved 6 % årlig rente!er det totale antallet utbetalingsperioder i en investering!er utbetalingen som foretas i hver periode, og kan ikke endres i investeringens løpetid!er nåverdien, eller det totale beløpet som en serie fremtidige utbetalinger er verdt i dag. Hvis argumentet utelates, blir nåverdi satt til 0!er en verdi som angir når utbetalingen forfaller. Utbetaling ved begynnelsen av perioden = 1, utbetaling ved slutten av perioden = 0 eller utelatt"}, "FVSCHEDULE": {"a": "(hovedstol; plan)", "d": "Returnerer sluttverdien av en inngående hovedstol etter å ha brukt en serie med sammensatte rentesatser", "ad": "er nåverdien!er en matrise av rentesatser som skal brukes"}, "INTRATE": {"a": "(betalingsdato; forfallsdato; investering; innløsningsverdi; [basis])", "d": "Returnerer rentefoten av et fullfinansiert verdipapir", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er beløpet som er investert i verdipapiret!er beløpet som mottas ved forfallsdato!angir typen datosystem som brukes"}, "IPMT": {"a": "(rente; periode; antall_utbet; nåverdi; [sluttverdi]; [type])", "d": "Returnerer betalte renter på en investering for en gitt periode basert på periodiske, konstante utbetalinger og en fast rentesats", "ad": "er rentesatsen per periode. Bruk for eksempel 6 %/4 for kvartalsvise utbetalinger ved 6 % årlig rente!er perioden du vil finne renten for, og må være et tall mellom 1 og antall_utbet!er det totale antallet betalingsperioder i en investering!er nåverdien, eller det totale beløpet som en serie fremtidige utbetalinger er verdt i dag!er fremtidig verdi, eller kontantbalansen du vil oppnå etter at siste utbetaling er foretatt. Hvis argumentet utelates, blir sluttverdi satt til 0!er en logisk verdi som angir når utbetalingen forfaller. Slutten av perioden = 0 eller utelatt, begynnelsen av perioden = 1"}, "IRR": {"a": "(verdi; [antatt])", "d": "Returnerer internrenten for en serie kontantstrømmer", "ad": "er en matrise eller en referanse til celler som inneholder tall du vil beregne internrenten for!er et tall du antar er i nærheten av resultatet av funksjonen IR. Settes til 0,1 (10 prosent) hvis argumentet utelates"}, "ISPMT": {"a": "(rente; periode; antall_utbet; nåverdi)", "d": "Returnerer renten som er betalt i løpet av en angitt investeringsperiode", "ad": "rente per periode. Bruk for eksempel 6 %/4 for kvartalsvise utbetalinger ved 6 % årlig rente!perioden du vil finne renten i!antallet betalingsperioder i en investering!nåverdien av en serie fremtidige utbetalinger"}, "MDURATION": {"a": "(betalingsdato; forfallsdato; rente; avkastning; frekvens; [basis])", "d": "Returnerer <PERSON>leys modifiserte varighet for et verdipapir med en antatt pariverdi på kr 100", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er verdipapirets årlige rente!er verdipapirets årlige avkastning!er antallet rentebetalinger per år!angir typen datosystem som brukes"}, "MIRR": {"a": "(verdier; kapital<PERSON>e; reinvestering<PERSON>rente)", "d": "Returnerer internrenten for en serie periodiske kontantstrømmer og tar hensyn til både investeringskostnad og rente på reinvestering av kontanter", "ad": "er en matrise eller en referanse til celler som inneholder tall som representerer en serie utbetalinger (negative) og inntekter (positive) med regelmessige mellomrom!er rentesatsen du betaler for penger som brukes i kontantstrømmene!er rentesatsen du mottar for kontantstrømmene når du reinvesterer dem"}, "NOMINAL": {"a": "(effektiv_rente; perioder)", "d": "Returnerer den årlige nominelle rentefoten", "ad": "er den effektive rentefoten!er antall sammensatte perioder per år"}, "NPER": {"a": "(rente; utbetaling; nåverdi; [sluttverdi]; [type])", "d": "Returnerer antallet perioder for en investering basert på periodiske, konstante utbetalinger og en fast rentesats", "ad": "er rentesatsen per periode. Bruk for eksempel 6 %/4 for kvartalsvise utbetalinger ved 6 % årlig rente!er utbetalingen som foretas i hver periode, og kan ikke endres i utbetalingens løpetid!er nåverdien, eller det totale beløpet som en serie fremtidige utbetalinger er verdt i dag!er fremtidig verdi, eller kontantbalansen du vil oppnå etter at siste utbetaling er foretatt. Hvis argumentet utelates, brukes null!er en logisk verdi. Utbetaling ved begynnelsen av perioden = 1, utbetaling ved slutten av perioden = 0 eller utelatt"}, "NPV": {"a": "(rente; verdi1; [verdi2]; ...)", "d": "Returnerer netto nåverdi for en investering basert på en rentesats og en serie fremtidige utbetalinger (negative verdier) og inntekter (positive verdier)", "ad": "er diskontosatsen over en periode!er 1 til 254 argumenter som representerer utbetalingene og inntektene med like mellomrom i tid. Alle inntreffer ved slutten av hver periode"}, "ODDFPRICE": {"a": "(betalingsdato; forfallsdato; utstedelsesdato; første_forfallsdato; rente; avkastning; innløsningsverdi; frekvens; [basis])", "d": "Returnerer prisen per pålydende kr 100 for et verdipapir som har en odde første periode", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er verdipapirets utstedelsesdato uttrykt som et serienummer!er verdipapirets første forfallsdato uttrykt som et serienummer!er verdipapirets årlige rente!er verdipapirets årlige avkastning!er verdipapirets innløsningsverdi per pålydende kr 100!er antallet rentebetalinger per år!angir typen datosystem som brukes"}, "ODDFYIELD": {"a": "(betalingsdato; forfallsdato; utstedelsesdato; første_forfallsdato; rente; pris; innløsningsverdi; frekvens; [basis])", "d": "Returnerer avkastningen av et verdipapir som har en odde første periode", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er verdipapirets utstedelsesdato uttrykt som et serienummer!er verdipapirets første forfallsdato uttrykt som et serienummer!er verdipapirets årlige rente!er verdipapirets pris!er verdipapirets innløsningsverdi per pålydende kr 100!er antallet rentebetalinger per år!angir typen datosystem som brukes"}, "ODDLPRICE": {"a": "(betalingsdato; forfallsdato; siste_forfallsdato; rente; avkastning; innløsningsverdi; frekvens; [basis])", "d": "Returnerer prisen per pålydende kr 100 for et verdipapir som har en odde siste periode", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er verdipapirets siste rentedato før betalingsdatoen uttrykt som et serienummer!er verdipapirets årlige rente!er verdipapirets årlige avkastning!er verdipapirets innløsningsverdi per pålydende kr 100!er antallet rentebetalinger per år!angir typen datosystem som brukes"}, "ODDLYIELD": {"a": "(betalingsdato; forfallsdato; siste_forfallsdato; rente; pris; innløsningsverdi; frekvens; [basis])", "d": "Returnerer avkastningen av et verdipapir som har en odde siste periode", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er verdipapirets siste rentedato før betalingsdatoen uttrykt som et serienummer!er verdipapirets årlige rente!er verdipapirets pris!er verdipapirets innløsningsverdi per pålydende kr 100!er antallet rentebetalinger per år!angir typen datosystem som brukes"}, "PDURATION": {"a": "(rente; nåverdi; sluttverdi)", "d": "Returnerer antallet perioder som kreves av en investering for å nå en angitt verdi", "ad": "er rentesatsen per periode.!er nåverdien til investeringen!er den ønskede fremtidige verdien av investeringen"}, "PMT": {"a": "(rente; antall_utbet; nåverdi; [sluttverdi]; [type])", "d": "<PERSON><PERSON><PERSON><PERSON> utbetalinger for et lån basert på konstante utbetalinger og en fast rentesats", "ad": "er rentesatsen per periode for lånet. Bruk for eksempel 6 %/4 for kvartalsvise utbetalinger ved 6 % årlig rente!er det totale antallet utbetalinger for lånet!er nåverdien, eller det totale beløpet som en serie fremtidige utbetalinger er verdt i dag!er fremtidig verdi, eller kontantbalansen du vil oppnå etter at siste utbetaling er foretatt. Settes til 0 (null) hvis argumentet utelates!er en logisk verdi. Utbetaling ved begynnelsen av perioden = 1, utbetaling ved slutten av perioden = 0 eller utelatt"}, "PPMT": {"a": "(rente; periode; antall_utbet; nåverdi; [sluttverdi]; [type])", "d": "Returnerer utbetaling på hovedstolen for en gitt investering basert på periodiske, konstante utbetalinger og en fast rentesats", "ad": "er rentesatsen per periode. Bruk for eksempel 6 %/4 for kvartalsvise utbetalinger ved 6 % årlig rente!angir perioden og må ligge mellom 1 og antall_utbet!er det totale antallet betalingsperioder i en investering!er nåverdien, eller det totale beløpet som en serie fremtidige utbetalinger er verdt i dag!er fremtidig verdi, eller kontantbalansen du vil oppnå etter at siste utbetaling er foretatt!er en logisk verdi. Utbetaling ved begynnelsen av perioden = 1, utbetaling ved slutten av perioden = 0 eller utelatt"}, "PRICE": {"a": "(betalingsdato; forfallsdato; rente; avkastning; innløsningsverdi; frekvens; [basis])", "d": "Returnerer prisen per pålydende kr 100 for et verdipapir som betaler periodisk rente", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er verdipapirets årlige rente!er verdipapirets årlige avkastning!er verdipapirets innløsningsverdi per pålydende kr 100!er antallet rentebetalinger per år!angir typen datosystem som brukes"}, "PRICEDISC": {"a": "(betalingsdato; forfallsdato; diskonto; innløsningsverdi; [basis])", "d": "Returnerer prisen per pålydende kr 100 for et diskontert verdipapir", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er verdipapirets diskontosats!er verdipapirets innløsningsverdi per pålydende kr 100!angir typen datosystem som brukes"}, "PRICEMAT": {"a": "(betalingsdato; forfallsdato; utstedelsesdato; rente; avkastning; [basis])", "d": "Returnerer prisen per pålydende kr 100 av et verdipapir som betaler rente ved forfall", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er verdipapirets utstedelsesdato uttrykt som et serienummer!er verdipapirets rente ved utsedelsesdatoen!er verdipapirets årlige avkastning!er typen datosystem som skal brukes"}, "PV": {"a": "(rente; antall_utbet; utbetaling; [sluttverdi]; [type])", "d": "Returnerer nåverdien for en investering, det totale beløpet som en serie fremtidige utbetalinger er verdt i dag", "ad": "er rentesatsen per periode. Bruk for eksempel 6 %/4 for kvartalsvise utbetalinger ved 6 % årlig rente!er det totale antallet utbetalinger i en investering!er utbetalingen som foretas i hver periode, og kan ikke endres i investeringens løpetid!er fremtidig verdi, eller kontantbalansen du vil oppnå etter at siste utbetaling er foretatt!er en logisk verdi. Utbetaling ved begynnelsen av perioden = 1, utbetaling ved slutten av perioden = 0 eller utelatt"}, "RATE": {"a": "(antall_innbet; betaling; nåverdi; [sluttverdi]; [type]; [anslag])", "d": "Returnerer rentesatsen per periode for et lån eller en investering. Bruk for eksempel 6 %/4 for kvartalsvise innbetalinger ved 6 % årlig rente", "ad": "er det totale antallet innbetalingsperioder for lånet eller investeringen!er innbetalingen som foretas i hver periode, og kan ikke endres i lånets eller investeringens løpetid!er nåverdien, eller det totale beløpet som en serie fremtidige innbetalinger er verdt i dag!er fremtidig verdi, eller kontantbalansen du vil oppnå etter at siste innbetaling er foretatt. Hvis argumentet utelates, brukes sluttverdi = 0!er en logisk verdi. Innbetaling ved begynnelsen av perioden = 1, innbetaling ved slutten av perioden = 0 eller utelatt!er ditt anslag for hva rentesatsen vil være. Hvis argumentet utelates, settes anslag til 0,1 (10 prosent)"}, "RECEIVED": {"a": "(betalingsdato; forfallsdato; investering; diskonto; [basis])", "d": "Returnerer belø<PERSON> som mottas ved forfallsdato for et fullinvestert verdipapir", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er beløpet som er investert i verdipapiret!er verdipapirets diskontosats!angir typen datosystem som brukes"}, "RRI": {"a": "(antall_innbet; nåverdi; sluttverdi)", "d": "Returnerer en ekvivalent rentesats for vekst for en investering", "ad": "er antallet perioder for investeringen!er nåverdien til investeringen!er den fremtidige verdien av investeringen"}, "SLN": {"a": "(kostnad; restverdi; levetid)", "d": "Returnerer den lineære verdiavskrivningen for et aktivum i en gitt periode", "ad": "er den opprinnelige kostnaden til aktivumet!er verdien ved slutten av avskrivningen!er antall perioder et aktivum blir avskrevet over (ofte kalt aktivumets økonomiske levetid)"}, "SYD": {"a": "(kostnad; restverdi; levetid; periode)", "d": "Returnerer årsavskrivningen for et aktivum i en angitt periode", "ad": "er den opprinnelige kostnaden til aktivumet!er verdien ved slutten av avskrivningen!er antall perioder et aktivum blir avskrevet over (ofte kalt aktivumets økonomiske levetid)!er perioden, og må oppgis i samme enhet som levetid"}, "TBILLEQ": {"a": "(betalingsdato; forfallsdato; diskonto)", "d": "Returnerer verdipapirekvivalenten til en statsobligasjon", "ad": "er statsobligasjonens betalingsdato uttrykt som et serienummer!er statsobligasjonens forfallsdato uttrykt som et serienummer!er statsobligasjonens diskontosats"}, "TBILLPRICE": {"a": "(betalingsdato; forfallsdato; diskonto)", "d": "Returnerer prisen per pålydende kr 100 for en statsobligasjon", "ad": "er statsobligasjonens betalingsdato uttrykt som et serienummer !er statsobligasjonens forfallsdato uttrykt som et serienummer !er statsobligasjonens diskontosats "}, "TBILLYIELD": {"a": "(betalingsdato; forfallsdato; diskonto)", "d": "Returnerer avkastningen for en statsobligasjon", "ad": "er statsobligasjonens betalingsdato uttrykt som et serienummer !er statsobligasjonens forfallsdato uttrykt som et serienummer !er statsobligasjonens diskontosats "}, "VDB": {"a": "(kostnad; restverdi; levetid; start_periode; slutt_periode; [faktor]; [skift])", "d": "Returnerer avskrivningen på et aktivum for en periode du angir, medre<PERSON><PERSON>, ved hjelp av dobbel degressiv avskrivning eller en annen metode du angir", "ad": "er den opprinnelige kostnaden til aktivumet!er verdien ved slutten av avskrivningen!er antall perioder et aktivum blir avskrevet over (ofte kalt aktivumets økonomiske levetid)!er startperioden du vil beregne avskrivningen fra, med samme enheter som levetid!er sluttperioden du vil beregne avskrivningen til, med samme enheter som levetid!er satsen verdien avskrives med. Settes til 2 (dobbel degressiv avskrivning) hvis argumentet utelates!bytt til lineær avskriving når avskrivingen er større enn verdiforringelsen = USANN eller utelatt, ikke bytt = SANN"}, "XIRR": {"a": "(verdier; datoer; [anslag])", "d": "Returnerer internrenten for en serie kontantstrømmer", "ad": "er en serie kontantstrømmer som tilsvarer en innbetalingsplan i datoer!er en plan over betalingsdatoer som tilsvarer kontantstrømbetalingene!er et tall du antar er i nærheten av resultatet av XIR"}, "XNPV": {"a": "(rente; verdier; datoer)", "d": "Returnerer netto nåverdi av planlagte kontantstrømmer", "ad": "er diskontosats for kontantstrømmene!er en serie kontantstrømmer som svarer til en betalingsplan i datoer!er en betalingsdatoplan som svarer til kontantstrømbetalingene"}, "YIELD": {"a": "(betalingsdato; forfallsdato; rente; pris; innløsningsverdi; frekvens; [basis])", "d": "Returnerer avkastningen på et verdipapir som betaler periodisk rente", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er verdipapirets årlige rente!er verdipapirets pris per pålydende kr 100!er verdipapirets innløsningsverdi per pålydende kr 100!er antallet rentebetalinger per år!angir typen datosystem som brukes"}, "YIELDDISC": {"a": "(betalingsdato; forfallsdato; pris; innløsningsverdi; [basis])", "d": "Returnerer den årlige avkastningen for et diskontert verdipapir, for eksempel en statsobligasjon", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er verdipapirets pris per pålydende kr 100!er verdipapirets innløsningsverdi per pålydende kr 100!angir typen datosystem som brukes"}, "YIELDMAT": {"a": "(betalingsdato; forfallsdato; utstedelsesdato; rente; pris; [basis])", "d": "Returnerer den årlige avkastningen av et verdipapir som betaler rente ved forfallsdatoen", "ad": "er verdipapirets betalingsdato uttrykt som et serienummer!er verdipapirets forfallsdato uttrykt som et serienummer!er verdipapirets utstedelsesdato uttrykt som et serienummer!er verdipapirets rente ved utstedelsesdatoen!er verdipapirets pris per pålydende kr 100!er typen datosystem som skal brukes"}, "ABS": {"a": "(tall)", "d": "Returnerer absoluttverdien til et tall, et tall uten fortegn", "ad": "er det reelle tallet du vil ha absoluttverdien til"}, "ACOS": {"a": "(tall)", "d": "Returnerer arccosinus til et tall, i radianer i intervallet 0 til pi. Arccosinus er vinkelen som har tall-variabelen som cosinus", "ad": "er cosinus til vinkelen du ønsker, og må ligge mellom -1 og 1"}, "ACOSH": {"a": "(tall)", "d": "Returnerer den inverse hyperbolske cosinus til et tall", "ad": "er et hvilket som helst reelt tall som er større enn eller lik 1"}, "ACOT": {"a": "(tall)", "d": "Returnerer arccotangens for et tall i radianer i området 0 til pi.", "ad": "er cotangens til vinkelen du ønsker"}, "ACOTH": {"a": "(tall)", "d": "Returnerer invers hyperbolsk cotangens for et tall", "ad": "er hyperbolsk cotangens til vinkelen du ønsker"}, "AGGREGATE": {"a": "(funksjonsnummer; alternativer; ref1; ...)", "d": "Returnerer en mengde i en liste eller database", "ad": "er et tall fra 1 til 19 som angir sammendragsfunksjonen for mengdeverdien.!er tallet 0 til 7 som angir verdiene som skal ignoreres for mengdeverdien!er matrisen eller området med numeriske data mengdeverdien skal beregnes for!angir plasseringen i matrisen, den er k-te størst, k-te lavest, k-te persentil eller k-te kvartil.!er tallet mellom 1 og 19 som angir sammendragsfunksjonen for mengdeverdien.!er tallet 0 til 7 som angir verdiene som skal ignoreres for mengdeverdien!er 1 til 253 områder eller referanser du vil finne mengdeverdien for"}, "ARABIC": {"a": "(tekst)", "d": "Konverterer et romertall til et arabisk tall", "ad": "er romertallet du vil konvertere"}, "ASC": {"a": "(tekst)", "d": "For språk med dobbeltbyte-te<PERSON>sett (DBCS) endrer funksjonen tegn med full bredde (dobbeltbyte) til tegn med halv bredde (enkeltbyte)", "ad": "er teksten du vil endre"}, "ASIN": {"a": "(tall)", "d": "Returnerer arcsinus til et tall i radianer, i området ‑Pi/2 til Pi/2", "ad": "er sinus til vinkelen, og må være mellom -1 og 1"}, "ASINH": {"a": "(tall)", "d": "Returnerer den inverse hyperbolske sinus til et tall", "ad": "er et hvilket som helst reelt tall som er større enn eller lik 1"}, "ATAN": {"a": "(tall)", "d": "Returnerer arctangens til et tall i radianer, i området -Pi/2 til Pi/2", "ad": "er tangens til den vinkelen du ønsker"}, "ATAN2": {"a": "(x; y)", "d": "Returnerer arctangens til x- og y-koordinatene i radianer, i området fra -Pi til Pi, unntatt -Pi", "ad": "er x-koordinatet til punktet!er y-koordinatet til punktet"}, "ATANH": {"a": "(tall)", "d": "Returnerer den inverse hyperbolske tangens til et tall", "ad": "er ethvert reelt tall mellom -1 og 1 unntatt -1 og 1"}, "BASE": {"a": "(tall; basis; [minste_lengde])", "d": "Konverterer et tall til en tekstrepresentasjon med den gitte basisen", "ad": "er tallet du vil konvertere!er den basisen som du vil konvertere tallet til!er den minste lengden på den returnerte strengen. Hvis dette er utelatt, blir foranstilte nuller ikke lagt til"}, "CEILING": {"a": "(tall; gjeldende_multiplum)", "d": "Runder av et tall oppover til nærmeste multiplum av en faktor", "ad": "er verdien du vil runde av!er multiplumet du vil runde av tall til et multiplum av"}, "CEILING.MATH": {"a": "(tall; [gjeldende_multiplum]; [modus])", "d": "Runder av et tall oppover til nærmeste heltall eller til nærmeste signifikante multiplum av en faktor", "ad": "er verdien du vil runde av!er multiplumet du vil runde av tall til et multiplum av!når dette argumentet er gitt og ikke null, avrunder denne funksjonen bort fra null"}, "CEILING.PRECISE": {"a": "(tall; [gjeldende_multiplum])", "d": "Returnerer et tall som er avrundet opp til nærmeste heltall, eller til nærmeste gjeldende multiplum", "ad": "er verdien du vil runde av!er multiplumet du vil runde av tall til et multiplum av"}, "COMBIN": {"a": "(antall; valgt_antall)", "d": "Returnerer antall komb<PERSON>er for et gitt antall elementer", "ad": "er totalt antall elementer!er antall elementer i hver kombinasjon"}, "COMBINA": {"a": "(antall; valgt_antall)", "d": "Returnerer antallet kombinasjoner med repetisjoner for et gitt antall elementer", "ad": "er totalt antall elementer!er antallet elementer i hver kombinasjon"}, "COS": {"a": "(tall)", "d": "Returnerer cosinus for en vinkel", "ad": "er vinkelen du vil finne cosinus til, i radianer"}, "COSH": {"a": "(tall)", "d": "Returnerer den hyperbolske cosinus til et tall", "ad": "er ethvert reelt tall"}, "COT": {"a": "(tall)", "d": "Returnerer cotangens for en vinkel", "ad": "er vinkelen du vil finne cotangensen til, i radianer"}, "COTH": {"a": "(tall)", "d": "Returnerer hyperbolsk cotangens for et tall", "ad": "er vinkelen du vil finne hyperbolsk cotangens til, i radianer"}, "CSC": {"a": "(tall)", "d": "Returnerer cosekans for en vinkel", "ad": "er vinkelen du vil finne cosekans til, i radianer"}, "CSCH": {"a": "(tall)", "d": "Returnerer hyperbolsk cosekans for en vinkel", "ad": "er vinkelen du vil finne hyperbolsk cosekans til, i radianer"}, "DECIMAL": {"a": "(tall; basis)", "d": "Konverterer en tekstrepresentasjon av et tall i en gitt basis til et desimaltall", "ad": "er tallet du vil konvertere!er basisen til tallet du konverterer"}, "DEGREES": {"a": "(vinkel)", "d": "Konverterer radianer til grader", "ad": "er vinkelen du vil konvertere, angitt i radianer"}, "ECMA.CEILING": {"a": "(tall; gjeldende_multiplum)", "d": "Runder av et tall oppover til nærmeste multiplum av en faktor", "ad": "er verdien du vil runde av!er multiplumet du vil runde av tall til et multiplum av"}, "EVEN": {"a": "(tall)", "d": "Runder av et positivt tall oppover og et negativt tall nedover til nærmeste heltall som er et partall", "ad": "er verdien som skal rundes av"}, "EXP": {"a": "(tall)", "d": "Returnerer e opphøyd i en potens du angir", "ad": "er eksponenten som brukes på grunntallet e. Konstanten e = 2,71828182845904, grunntal<PERSON> for den naturlige logaritmen"}, "FACT": {"a": "(tall)", "d": "Returnerer fakultet til et tall, med andre ord produktet av 1*2*3*...* Tall", "ad": "er det ikke-negative tallet du vil finne fakultet til"}, "FACTDOUBLE": {"a": "(tall)", "d": "Returnerer et talls doble fakultet", "ad": "er verdien som den doble fakultetsverdien skal returneres for"}, "FLOOR": {"a": "(tall; gjeldende_multiplum)", "d": "Runder av et tall nedover til nærmeste signifikante multiplum av en faktor", "ad": "er den numeriske verdien du vil runde av!er faktoren du vil runde av tall ned til et multiplum av. Tall og faktor må enten begge være positive eller begge være negative"}, "FLOOR.PRECISE": {"a": "(tall; [gjeldende_multiplum])", "d": "Returnerer et tall som er avrundet ned til nærmeste heltall eller til nærmeste multiplum av gjeldende_multiplum", "ad": "er verdien du vil runde av!er multiplumet du vil runde av tall til et multiplum av"}, "FLOOR.MATH": {"a": "(tall; [gjeldende_multiplum]; [modus])", "d": "Runder av et tall nedover til nærmeste heltall eller til nærmeste signifikante multiplum av en faktor", "ad": "er verdien du vil runde av!er multiplumet du vil runde av tall til et multiplum av!når dette argumentet er gitt og ikke null, avrunder denne funk<PERSON>jonen mot null"}, "GCD": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer den største felles divisor", "ad": "er 1 til 255 verdier"}, "INT": {"a": "(tall)", "d": "Runder av et tall nedover til nærmeste heltall", "ad": "er det reelle tallet du vil runde av nedover til et heltall"}, "ISO.CEILING": {"a": "(tall; [gjeldende_multiplum])", "d": "Returnerer et tall som er avrundet opp til nærmeste heltall, eller til nærmeste gjeldende multiplum. <PERSON><PERSON><PERSON> tallets fortegn, blir tallet avrundet opp. Men hvis tallet eller gjeldende multiplum er null, returneres null.", "ad": "er verdien du vil runde av!er multiplumet du vil runde av tall til et multiplum av"}, "LCM": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer minste felles multiplum", "ad": "er 1 til 255 verdier du ønsker å finne minste felles multiplum for"}, "LN": {"a": "(tall)", "d": "Returnerer den naturlige logaritmen for et tall", "ad": "er det positive reelle tallet du vil finne den naturlige logaritmen til"}, "LOG": {"a": "(tall; [grun<PERSON><PERSON>])", "d": "Returnerer logaritmen til et tall med det grunntallet du angir", "ad": "er det positive reelle tallet du vil finne logaritmen til!er grunntallet i logaritmen. Settes til 10 hvis utelatt"}, "LOG10": {"a": "(tall)", "d": "Returnerer logaritmen med grunntall 10 for et tall", "ad": "er det positive reelle tallet du vil finne logaritmen med grunntall 10 til"}, "MDETERM": {"a": "(matrise)", "d": "Returnerer matrisedeterminanten til en matrise", "ad": "er en tallmatrise med likt antall rader og kolonner, enten et celleområde eller en matrisekonstant"}, "MINVERSE": {"a": "(matrise)", "d": "Returnerer den inverse matrisen til matrisen som er lagret i en matrise", "ad": "er en tallmatrise med likt antall rader og kolonner, enten et celleområde eller en matrisekonstant"}, "MMULT": {"a": "(matrise1; matrise2)", "d": "Returnerer matriseproduktet av to matriser, en matrise med samme antall rader som matrise1 og kolonner som matrise2", "ad": "er den første matrisen med tall som skal multipliseres. <PERSON><PERSON> ha samme antall kolonner som matrise2 har rader"}, "MOD": {"a": "(tall; divisor)", "d": "Returnerer resten når et tall divideres med en divisor", "ad": "er tallet du vil finne resten for etter at divisjonen er utført!er det tallet som tall divideres med"}, "MROUND": {"a": "(tall; multiplum)", "d": "Returnerer et tall avrundet til det ønskede multiplum", "ad": "er verdien du vil runde av!er det multiplum du vil runde av tallet til"}, "MULTINOMIAL": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer polynomet til et sett med tall", "ad": "er 1 til 255 verdier du ønsker å finne polynomet til"}, "MUNIT": {"a": "(dimensjon)", "d": "Returnerer enhetsmatrisen for den angitte dimensjonen", "ad": "er et heltall som angir dimensjonen til enhetsmatrisen som du vil returnere"}, "ODD": {"a": "(tall)", "d": "Runder av et positivt tall oppover og et negativt tall nedover til nærmeste heltall som er et oddetall", "ad": "er verdien som skal rundes av"}, "PI": {"a": "()", "d": "Returnerer verdien av <PERSON>, 3,14159265358979, med 15 desimalers nøyaktighet", "ad": ""}, "POWER": {"a": "(tall; potens)", "d": "Returnerer resultatet av et tall opphøyd i en potens", "ad": "er grunntallet, et reelt tall!er eksponenten grunntallet blir opphøyd i"}, "PRODUCT": {"a": "(tall1; [tall2]; ...)", "d": "Multiplis<PERSON> alle tall som gis som argumenter", "ad": "er 1 til 255 tall, logiske verdier eller tall i tekstformat du vil multiplisere"}, "QUOTIENT": {"a": "(teller; nevner)", "d": "Returnerer heltallsdelen av en divisjon", "ad": "er dividenden!er divisoren"}, "RADIANS": {"a": "(vinkel_i_grader)", "d": "Konverterer grader til radianer", "ad": "er en vinkel, angitt i grader, som du vil konvertere"}, "RAND": {"a": "()", "d": "Returnerer et tilfeldig tall som er lik eller større enn 0 og mindre enn 1 (endres ved omberegning)", "ad": ""}, "RANDARRAY": {"a": "([rader]; [kolonner]; [minimum]; [maksimum]; [heltall])", "d": "Returnerer en matrise med tilfeldige tall", "ad": "antall rader i den returnerte matrisen!antall kolonner i den returnerte matrisen!det minste tallet du ønsker å returnere!det maksimale tallet du ønsker å returnere!returner et heltall eller en desimalverdi. SANN for et heltall, USANN for et desimaltall"}, "RANDBETWEEN": {"a": "(bunn; topp)", "d": "Returnerer et tilfeldig tall mellom tallene du angir", "ad": "er det minste heltallet TILFELDIGMELLOM returnerer!er det største heltallet TILFELDIGMELLOM returnerer"}, "ROMAN": {"a": "(tall; [form])", "d": "Konverterer et arabertall til et romertall, som tekst", "ad": "er det arabertallet du vil konvertere!er tallet som angir hvilken type romertall du vil bruke."}, "ROUND": {"a": "(tall; antall_sifre)", "d": "Runder av et tall til et angitt antall sifre", "ad": "er tallet du vil runde av!er antall sifre du vil runde av tallet til. Et negativt tall runder av til venstre for desimalkommaet, null til nærmeste heltall"}, "ROUNDDOWN": {"a": "(tall; antall_sifre)", "d": "Runder av et tall nedover mot null", "ad": "er et reelt tall du vil runde av nedover!er antall sifre du vil runde av tallet til. En negativ verdi runder av til venstre for desimalkommaet, null eller utelatt til nærmeste heltall"}, "ROUNDUP": {"a": "(tall; antall_sifre)", "d": "Runder av et tall oppover, bort fra null", "ad": "er et reelt tall du vil runde av oppover!er antall sifre du vil runde av tallet til. En negativ verdi runder av til venstre for desimalkommaet, null eller utelatt til nærmeste heltall"}, "SEC": {"a": "(tall)", "d": "Returnerer sekans for en vinkel", "ad": "er vinkelen du vil finne sekans til, i radianer"}, "SECH": {"a": "(tall)", "d": "Returnerer hyperbolsk sekans for en vinkel", "ad": "er vinkelen du vil finne hyperbolsk sekans til, i radianer"}, "SERIESSUM": {"a": "(x; n; m; koeffisienter)", "d": "Returnerer summen av en geometrisk rekke, basert på formelen", "ad": "er innsettingsverdien til potensrekken!er den første eksponenten du vil opphøye x i!er verdien du vil øke n med for hvert ledd i rekken!er et sett av koeffisienter som hver påfølgende potens av x skal multipliseres med"}, "SIGN": {"a": "(tall)", "d": "Returnerer fortegnet for et tall: 1 hvis tallet er er positivt, 0 hvis tallet er null, og -1 hvis tallet er negativt", "ad": "er ethvert reelt tall"}, "SIN": {"a": "(tall)", "d": "Returnerer sinus for en vinkel", "ad": "er vinkelen du vil finne sinus til, i radianer. Grader * PI()/180 = radianer"}, "SINH": {"a": "(tall)", "d": "Returnerer den hyperbolske sinus til et tall", "ad": "er ethvert reelt tall"}, "SQRT": {"a": "(tall)", "d": "Returnerer kvadratroten av et tall", "ad": "er tallet du vil finne kvadratroten av"}, "SQRTPI": {"a": "(tall)", "d": "Returnerer k<PERSON><PERSON><PERSON><PERSON> av (tall * pi)", "ad": "er tallet som pi skal multipliseres med"}, "SUBTOTAL": {"a": "(<PERSON><PERSON><PERSON>; ref1; ...)", "d": "Returnerer en delsum fra en liste eller database", "ad": "er et tall mellom 1 og 11 som angir hvilken sammendragsfunksjon som skal brukes i beregningen av delsummen.!er 1 til 254 områder eller referanser du vil beregne delsummen for"}, "SUM": {"a": "(tall1; [tall2]; ...)", "d": "<PERSON>er alle tallene i et celleområde", "ad": "er 1 til 255 argumenter som skal summeres. Logiske verdier og tekst ignoreres i celler, men tas med hvis de skrives inn som argumenter"}, "SUMIF": {"a": "(områ<PERSON>; vilkår; [summeringsområde])", "d": "<PERSON>er cellene som tilfredsstiller en gitt betingelse eller et gitt vilkår", "ad": "er celleområdet du vil evaluere!er betingelsen eller vilkåret i form av et tall, et uttrykk eller en tekst, som definerer hvilke celler som skal summeres!er cellene som skal summeres. Hvis argumentet utelates, brukes cellene i området"}, "SUMIFS": {"a": "(summeringsområde; kriterieområde; kriterium; ...)", "d": "<PERSON><PERSON> sammen cellene som angis av et gitt sett med vilkår eller kriterier", "ad": "er cellene som skal summeres!er celleområdet du vil evaluere for det spesifikke vilkåret!er kriteriet i form av tall, uttrykk eller tekst som definerer hvilke celler som skal summeres"}, "SUMPRODUCT": {"a": "(matrise1; [matrise2]; [matrise3]; ...)", "d": "Returnerer summen av produktene til tilsvarende områder eller matriser", "ad": "er 2 til 255 matriser du vil multiplisere og så summere komponentene til. Alle matrisene må ha samme dimensjoner"}, "SUMSQ": {"a": "(tall1; [tall2]; ...)", "d": "Returnerer summen av de kvadrerte argumentene. Argumentene kan være tall eller matriser, navn eller referanser til celler som inneholder tall", "ad": "er 1 til 255 tall eller navn, matriser eller referanser som inneholder tall som du vil finne summen av kvadratene for"}, "SUMX2MY2": {"a": "(matrise_x; matrise_y)", "d": "<PERSON>er differansen mellom kvadratene av to tilsvarende områder eller matriser", "ad": "er det første området eller den første matrisen med tall, og kan være et tall eller et navn, en matrise eller en referanse som inneholder tall!er det andre området eller den andre matrisen med tall, og kan være et tall eller et navn, en matrise eller en referanse som inneholder tall"}, "SUMX2PY2": {"a": "(matrise_x; matrise_y)", "d": "Returnerer totalsummen av summene av kvadratene for tall i to tilsvarende områder eller matriser", "ad": "er det første området eller den første matrisen med tall, og kan være et tall eller et navn, en matrise eller en referanse som inneholder tall!er det andre området eller den andre matrisen med tall, og kan være et tall eller et navn, en matrise eller en referanse som inneholder tall"}, "SUMXMY2": {"a": "(matrise_x; matrise_y)", "d": "Summerer kvadratene av differansene mellom to tilsvarende områder eller matriser", "ad": "er det første området eller den første matrisen med verdier, og kan være et tall eller et navn, en matrise eller en referanse som inneholder tall!er det andre området eller den andre matrisen med verdier, og kan være et tall eller et navn, en matrise eller en referanse som inneholder tall"}, "TAN": {"a": "(tall)", "d": "Returnerer tangens for en vinkel", "ad": "er vinkelen du vil finne tangens til, i radianer. Grader * PI()/180 = radianer"}, "TANH": {"a": "(tall)", "d": "Returnerer den hyperbolske tangens til et tall", "ad": "er ethvert reelt tall"}, "TRUNC": {"a": "(tall; [antall_sifre])", "d": "Avrunder et tall nedover ved å fjerne alle desimaler over et angitt antall (et positivt tall) eller runder et angitt antall sifre til venstre for kommaet ned til null (et negativt tall)", "ad": "er tallet du vil avkorte (avrun<PERSON> nedover)!er et tall som angir antallet desimalplasser som skal beholdes. Settes til 0 (null) hvis argumentet utelates"}, "ADDRESS": {"a": "(rad_nr; kolonne_nr; [abs]; [a1]; [regneark])", "d": "Lager en cellereferanse som tekst, når det angis rad- og kolonnenumre", "ad": "er radnummeret som skal brukes i cellereferansen: Rad_nummer = 1 for rad 1!er kolonnenummeret du vil bruke i cellereferansen, for eksempel 4 for kolonne D!angir referansetypen: Absolutt = 1, absolutt rad/relativ kolonne = 2, relativ rad/absolutt kolonne = 3, relativ = 4!er en logisk funksjon som angir referansestil: A1-stil = 1 eller SANN, R1C1-stil = 0 eller USANN!er tekst som angir navnet på regnearket som skal brukes som ekstern referanse"}, "CHOOSE": {"a": "(indeks; verdi1; [verdi2]; ...)", "d": "<PERSON>elger en verdi eller en handling fra en liste med verdier, basert på et indekstall", "ad": "angir hvilket verdiargument som er valgt. Indeks må være mellom 1 og 254, eller en formel eller en referanse til et tall mellom 1 og 254.!er 1 til 254 tall, cell<PERSON><PERSON><PERSON><PERSON>, definerte navn, formler, funksjoner eller tekstargumenter som funksjonen VELG velger fra"}, "COLUMN": {"a": "([ref])", "d": "Returnerer kolonnenummeret for en referanse", "ad": "er cellen eller celleområdet du vil vite kolonnenummeret til. <PERSON><PERSON> dette utelates, brukes cellen som inneholder KOLONNE-funksjonen"}, "COLUMNS": {"a": "(matrise)", "d": "Returnerer antall kolonner i en matrise eller referanse", "ad": "er en matrise eller en matriseformel, eller en referanse til et celleområde, som du vil finne antall kolonner i"}, "FORMULATEXT": {"a": "(referanse)", "d": "Returnerer en formel som en streng", "ad": "er en referanse til en formel"}, "HLOOKUP": {"a": "(s<PERSON><PERSON><PERSON><PERSON>; matrise; radinde<PERSON>; [omr<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> etter en verdi i den øverste raden i en tabell eller matrise, og returnerer verdien i samme kolonne fra en rad du angir", "ad": "er den verdien du vil søke etter i den første raden i tabellen, og kan være en verdi, en referanse eller en tekststreng!er en tabell med tekst, tall eller logiske verdier som du kan søke etter data i. Matrise kan være en referanse til et område eller et områdenavn!er det radnummeret i matrise sammenligningsverdien skal returneres fra. Den første raden med verdier i tabellen er rad 1!er en logisk verdi. Hvis du vil finne den verdien som har størst samsvar i den øverste raden (sortert stigende) = SANN eller utelatt, hvis du vil finne en verdi som er eksakt lik = USANN"}, "HYPERLINK": {"a": "(kobling; [egendefinert_navn])", "d": "Lager en snarvei eller et hopp som åpner et dokument som er lagret på harddisken, på en server på nettverket eller på Internett", "ad": "er teksten som gir banen til og filnavnet på dokumentet som skal åpnes, en plassering på harddisken, en UNC-adresse eller en URL-bane!er teksten eller tallet som vises i cellen. Hvis argumentet utelates, viser cellen koblingsteksten"}, "INDEX": {"a": "(matrise; rad_nr; [kolonne_nr]!ref; rad; [kolonne]; [omr<PERSON><PERSON>])", "d": "Returnerer en verdi eller referanse for cellen i skjæringspunktet av en bestemt rad eller kolonne, i et gitt celleområde", "ad": "er et celleområde eller en matrisekonstant.!merker raden i matrisen eller referansen du vil returnere en verdi fra. Hvis argumentet utelates, må du bruke kolonne_nr!merker området i matrisen eller referansen du vil returnere en verdi fra. Hvis argumentet utelates, må du bruke rad_nr!er en referanse til ett eller flere celleområder!merker raden i matrisen eller referansen du vil returnere en verdi fra. Hvis argumentet utelates, må du bruke kolonne_nr!merker kolonnen i matrisen eller referansen du vil returnere en verdi fra. Hvis argumentet utelates, må du bruke rad_nr!merker et område i referansen du vil returnere en verdi fra. Det første området som merkes eller angis, blir område 1, det neste området blir område 2, og så videre"}, "INDIRECT": {"a": "(ref; [a1])", "d": "Returnerer en referanse angitt av en tekststreng", "ad": "er en referanse til en celle som inneholder en referanse i A1-stil eller R1C1-stil, et navn som er definert som en referanse, eller en referanse til en celle som en tekststreng!er en logisk verdi som angir typen referanse i reftekst. R1C1-stil = USANN, A1-stil = SANN eller utelatt"}, "LOOKUP": {"a": "(søkever<PERSON>; søkematrise; [resultatvektor]!søkeverdi; matrise)", "d": "<PERSON><PERSON><PERSON><PERSON> opp en verdi enten fra et enrads- eller enkolonnes-område eller fra en matrise. Angitt for bakoverkompatibilitet", "ad": "er en verdi som funksjonen SLÅ.OPP søker etter i søkematrisen. Kan være et tall, tekst, en logisk verdi eller et navn eller en referanse til en verdi!er et celleområde som bare inneholder én rad eller én kolonne med tekst, tall eller logiske verdier, ordnet i stigende rekkefølge!er et celleområde som bare inneholder én rad eller én kolonne, av samme størrelse som søkematrisen!er en verdi som funksjonen SLÅ.OPP søker etter i en matrise. Kan være et tall, tekst, en logisk verdi eller et navn eller en referanse til en verdi!er et celleområde som inneholder tekst, tall eller logiske verdier du vil sammenligne med søkeverdi"}, "MATCH": {"a": "(søke<PERSON><PERSON>; søkematrise; [type])", "d": "Returnerer den relative posisjonen til et element i en matrise som tilsvarer en angitt verdi i en angitt rekkefølge", "ad": "er verdien du bruker for å finne ønsket verdi i matrisen: et tall, tekst, en logisk verdi eller en referanse til en av disse!er et sammenhengende celleområde som inneholder mulige oppslagsverdier, en matrise med verdier eller en referanse til en matrise!er tallet 1, 0 eller -1, som bestemmer hvilken verdi som skal returneres."}, "OFFSET": {"a": "(ref; rader; kolonner; [hø<PERSON><PERSON>]; [bredde])", "d": "Returnerer en referanse til et område som er et gitt antall rader og kolonner fra en gitt referanse", "ad": "er referansen som skal brukes som utgangspunkt for forskyvningen, en referanse til en celle eller et sammenhengende celleområde!er antall rader, opp eller ned, som du vil at cellen øverst til venstre i resultatet skal vise til!er antall kolonner, til venstre eller høyre, som du vil at cellen øverst til venstre i resultatet skal vise til!er høyden, i antall rader, som du vil at resultatet skal ha. Får den samme høyden som referansen hvis argumentet utelates!er bredden, i antall kolonner, som du vil at resultatet skal ha. Får samme bredde som referansen hvis argumentet utelates"}, "ROW": {"a": "([ref])", "d": "Returnerer radnummeret for en referanse", "ad": "er cellen eller celleområdet du vil vite radnummeret til. Hvis argumentet utelates, returneres cellen som inneholder RAD-funksjonen"}, "ROWS": {"a": "(matrise)", "d": "Returnerer antall rader i en referanse eller en matrise", "ad": "er en matrise, en matriseformel eller en referanse til et celleområde du vil vite antall rader i"}, "TRANSPOSE": {"a": "(matrise)", "d": "Konverterer et vertikalt celleområde til et horisontalt celleområde, eller omvendt", "ad": "er et celleområde i et regneark eller i en matrise der du vil bytte om rader og kolonner"}, "UNIQUE": {"a": "(array; [by_col]; [exactly_once])", "d": "Returnerer de unike verdiene fra et område eller en matrise.", "ad": "området eller matrisen som unike rader eller kolonner skal returneres fra!er en logisk verdi: sammenligne rader med hverandre og returnere de unike radene = USANN eller utelatt. sammenligne kolonner med hverandre og returnere de unike kolonnene = SANN! er en logisk verdi: returnere rader eller kolonner som forekommer nøyaktig én gang fra matrisen = SANN. returnere alle distinkte rader eller kolonner fra matrisen = USANN eller utelatt"}, "VLOOKUP": {"a": "(sø<PERSON><PERSON><PERSON>; matrise; kolonneind<PERSON>s; [sø<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> etter en verdi i kolonnen lengst til venstre i en tabell, og returnerer en verdi i samme rad fra en kolonne du angir. Standardinnstilling er at tabellen må være sortert i stigende rekkefølge", "ad": "er verdien du vil søke etter i den første kolonnen i tabellen, og kan være en verdi, en referanse eller en tekststreng!er en tabell med tekst, tall eller logiske verdier som data hentes fra. Tabell_matrise kan være en referanse til et område eller et områdenavn!er kolonnenummeret i tabell_matrise som den samsvarende verdien returneres fra. Den første kolonnen med verdier i tabellen er kolonne 1!er en logisk verdi. Hvis du vil finne verdien i den første kolonnen (sortert i stigende rekkefølge) som er mest lik søkeverdien = SANN eller utelatt. Hvis du vil finne en verdi som er helt lik søkeverdien = USANN"}, "XLOOKUP": {"a": "(søkeverdi; søkematrise; returneringsmatrise; [om_ikke_funnet]; [samsvarsmodus]; [søke<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON> i et område eller en matrise etter et treff og returnerer det tilsvarende elementet fra et annet område eller en annen matrise. Som standard brukes et eksakt treff", "ad": "er verdien du vil søke etter!er matrisen eller området du vil søke i!er matrisen eller området som skal returneres!hvis ingen treff finnes!angi hvordan søkeverdi skal samsvare med verdiene i søkematrise!angi søkemodusen du vil bruke. Første-til-siste-søk blir brukt som standard"}, "CELL": {"a": "(infotype; [ref])", "d": "Returnerer informasjon om formatering, plassering eller innholdet til en celle", "ad": "en tekstverdi som angir hvilken type celleinformasjon du vil returnere!cellen som du vil ha informasjon om"}, "ERROR.TYPE": {"a": "(feil<PERSON><PERSON>)", "d": "Returnerer et tall som svarer til en feilverdi.", "ad": "er feilverdien du vil finne identifikasjonsnummeret til, og kan være selve feilverdien eller en referanse til en celle som inneholder en feilverdi"}, "ISBLANK": {"a": "(verdi)", "d": "Kontrollerer om en referanse er til en tom celle, og returnerer SANN eller USANN", "ad": "er cellen eller et navn som refererer til cellen du vil teste"}, "ISERR": {"a": "(verdi)", "d": "Kontrollerer om verdien er en annen feil enn #I/T (ikke tilg<PERSON>), og returnerer SANN eller USANN", "ad": "er verdien du vil teste. <PERSON>en kan referere til en celle, en formel eller et navn som refererer til en celle, en formel eller en verdi"}, "ISERROR": {"a": "(verdi)", "d": "Kontrollerer om verdien er en feil, og returnerer SANN eller USANN", "ad": "er verdien du vil teste. <PERSON>en kan referere til en celle, en formel eller et navn som refererer til en celle, en formel eller en verdi"}, "ISEVEN": {"a": "(tall)", "d": "Returnerer SANN hvis tallet er et partall", "ad": "er verdien du ønsker å teste"}, "ISFORMULA": {"a": "(referanse)", "d": "Kontrollerer om en referanse er til en celle som inneholder en formel, og returnerer SANN eller USANN", "ad": "er en referanse til cellen du vil teste. Referansen kan være en cellereferanse, en formel eller et navn som refererer til en celle"}, "ISLOGICAL": {"a": "(verdi)", "d": "Kontrollerer om en verdi er en logisk verdi (SANN eller USANN), og returnerer SANN eller USANN", "ad": "er verdien du vil teste. <PERSON>en kan referere til en celle, en formel eller et navn som refererer til en celle, en formel eller en verdi"}, "ISNA": {"a": "(verdi)", "d": "Kontrollerer om verdien er #I/T (ikke tilg<PERSON>g) og returnerer SANN eller USANN", "ad": "er verdien du vil teste. <PERSON>en kan referere til en celle, en formel eller et navn som refererer til en celle, en formel eller en verdi"}, "ISNONTEXT": {"a": "(verdi)", "d": "Kontrollerer om en verdi ikke er tekst (tomme celler er ikke tekst), og returnerer SANN eller USANN", "ad": "er verdien du vil teste: en celle, en formel, eller et navn som refererer til en celle, en formel eller en verdi"}, "ISNUMBER": {"a": "(verdi)", "d": "Kontrollerer om en verdi er et tall, og returnerer SANN eller USANN", "ad": "er verdien du vil teste. <PERSON>en kan referere til en celle, en formel eller et navn som refererer til en celle, en formel eller en verdi"}, "ISODD": {"a": "(tall)", "d": "Returnerer SANN hvis tallet er et oddetall", "ad": "er verdien du ønsker å teste"}, "ISREF": {"a": "(verdi)", "d": "Kontrollerer om verdien er en referanse, og returnerer SANN eller USANN", "ad": "er verdien du vil teste. <PERSON>en kan referere til en celle, en formel eller et navn som refererer til en celle, en formel eller en verdi"}, "ISTEXT": {"a": "(verdi)", "d": "Kontrollerer om en verdi er tekst, og returnerer SANN eller USANN", "ad": "er verdien du vil teste. <PERSON>en kan referere til en celle, en formel eller et navn som refererer til en celle, en formel eller en verdi"}, "N": {"a": "(verdi)", "d": "Konverterer verdier som ikke er tall, til tall, da<PERSON><PERSON> til serienumre, SANN til 1 og alt annet til 0 (null)", "ad": "er verdien du vil konvertere"}, "NA": {"a": "()", "d": "Returnerer feilverdien #I/T (ik<PERSON>)", "ad": ""}, "SHEET": {"a": "([verdi])", "d": "Returnerer arknummeret for arket det refereres til", "ad": "er navnet på et ark eller en referanse som du ønsker arknummeret til. Hvis argumentet utelates, returneres nummeret til arket som inneholder funksjonen"}, "SHEETS": {"a": "([referanse])", "d": "Returnerer antall ark i en referanse", "ad": "er en referanse som du vil vite antall ark i. Hvis argumentet utelates, returneres antall ark i arbeidsboken som inneholder funksjonen"}, "TYPE": {"a": "(verdi)", "d": "Returnerer et heltall som representerer datatypen til en verdi: tall = 1, tekst = 2, logisk verdi =4, feilverdi = 16, matrise = 64, sammensatte data = 128", "ad": "kan være en hvilken som helst verdi"}, "AND": {"a": "(logisk1; [logisk2]; ...)", "d": "Kontrollerer om alle argumenter er lik SANN, og returnerer SANN hvis alle argumentene er lik SANN", "ad": "er 1 til 255 tilstander du vil teste, som hver kan være enten SANN eller USANN, og som kan være logiske verdier, matriser eller referanser"}, "FALSE": {"a": "()", "d": "Returnerer den logiske verdien USANN", "ad": ""}, "IF": {"a": "(logisk_test; [sann]; [usann])", "d": "Kontrollerer om en betingelse er oppfylt, og returnerer en verdi hvis SANN, og en annen verdi hvis USANN", "ad": "er enhver verdi eller ethvert uttrykk som kan evalueres til SANN eller USANN!er verdien som returneres hvis logisk_test er SANN. Hvis argumentet utelates, returneres SANN. Du kan neste opptil sju HVIS-funksjoner!er verdien som returneres hvis logisk_test er USANN. Hvis argumentet utelates, returneres USANN"}, "IFS": {"a": "(logisk_test; verdi_hvis_sann; ...)", "d": "Kontrollerer om én eller flere betingelser er oppfylt, og returnerer en verdi som tilsvarer den første betingelsen som er SANN", "ad": "er enhver verdi eller ethvert uttrykk som kan evalueres til SANN eller USANN!er verdien som returneres hvis logisk_test er SANN"}, "IFERROR": {"a": "(verdi; verdi_hvis_feil)", "d": "Returnerer verdi_hvis_feil hvis uttrykket er en feil, ellers returneres verdien til selve uttrykket", "ad": "en hvilken som helst verdi, referanse eller uttrykk!en hvilken som helst verdi, referanse eller uttrykk"}, "IFNA": {"a": "(verdi; verdi_hvis_it)", "d": "Returnerer verdien du angir hvis uttrykket løses til #I/T, og ellers returneres resultatet av uttrykket", "ad": "er enhver verdi eller et uttrykk eller en referanse!er enhver verdi eller et uttrykk eller en referanse"}, "NOT": {"a": "(logisk)", "d": "<PERSON>rer USANN til SANN eller SANN til USANN", "ad": "er en verdi eller et uttrykk som kan evalueres til SANN eller USANN"}, "OR": {"a": "(logisk1; [logisk2]; ...)", "d": "Kontrollerer om noen av argumentene er lik SANN, og returnerer SANN eller USANN. Returnerer USANN bare hvis alle argumentene er lik USANN", "ad": "er 1 til 255 tilstander du vil teste, og som hver kan være enten SANN eller USANN"}, "SWITCH": {"a": "(uttrykk; verdi1; resultat1; [standard_eller_verdi2]; [resultat2]; ...)", "d": "Evaluerer et uttrykk mot en liste med verdier og returnerer resultatet som tilsvarer den første samsvarende verdien. Hvis det ikke er noen samsvarende verdier, returneres en valgfri standardverdi", "ad": "er et uttrykk som skal evalueres!er en verdi som skal sammenlignes med uttrykket!er et resultat som skal returneres hvis den tilsvarende verdien samsvarer med uttrykket"}, "TRUE": {"a": "()", "d": "Returnerer den logiske verdien SANN", "ad": ""}, "XOR": {"a": "(logisk1; [logisk2]; ...)", "d": "Returnerer et \"Utelukkende eller\" av alle argumenter", "ad": "er 1 til 254 tilstander du vil teste, som hver kan være enten SANN eller USANN, og som kan være logiske verdier, matriser eller referanser"}, "TEXTBEFORE": {"a": "(tekst, skilletegn, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " Returnerer tekst som er før tegnskilletegn.", "ad": "Teksten du vil søke i for skilletegnet.!Tegnet eller strengen som skal brukes som skilletegn.!Ønsket forekomst av skilletegnet. Standard er 1. Et negativt tall søker fra slutten.!Søker i teksten etter et skilletegn som samsvarer. Som standard samsvares det mellom små og store bokstaver.!Om skilletegnet skal samsvare med slutten av teksten. Som standard samsvarer de ikke.!Returneres hvis ingen treff blir funnet. Som standard returneres #N/A."}, "TEXTAFTER": {"a": "(tekst, skilletegn, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " Returnerer tekst som er etter skilletegn.", "ad": "Teksten du vil søke i for skilletegnet.!Tegnet eller strengen som skal brukes som skilletegn.!Ønsket forekomst av skilletegnet. Standard er 1. Et negativt tall søker fra slutten.!Søker i teksten etter et skilletegn som samsvarer. Som standard samsvares det mellom små og store bokstaver.!Om skilletegnet skal samsvare med slutten av teksten. Som standard samsvarer de ikke.!Returneres hvis ingen treff blir funnet. Som standard returneres #N/A."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Deler tekst inn i rader eller kolonner ved hjelp av skilletegn.", "ad": "Teksten som skal deles!Tegn eller streng som kolonnene skal deles med.!Tegn eller streng som radene skal deles med.!Om tomme celler skal ignoreres. Standard er USANN.!Søker i teksten etter skilletegnsamsvar. Som standard utføres et samsvar som skiller mellom store og små bokstaver.!Verdien som skal brukes for utfylling. Som standard brukes #N/A."}, "WRAPROWS": {"a": "(vektor, wrap_count, [pad_with])", "d": "B<PERSON>ter en rad- eller kolonnevektor etter et bestemt antall verdier.", "ad": "Vektoren eller referansen som skal brytes.!Maksimalt antall verdier per rad.!Verdien som skal utfylles med. Standard er #N/A."}, "VSTACK": {"a": "(matrise1, [matrise2], ...)", "d": "<PERSON><PERSON><PERSON> matriser loddrett i én matrise.", "ad": "En matrise eller referanse som skal stables."}, "HSTACK": {"a": "(matrise1, [matrise2], ...)", "d": "<PERSON><PERSON><PERSON> matriser van<PERSON>t i én matrise.", "ad": "En matrise eller referanse som skal stables."}, "CHOOSEROWS": {"a": "(matrise, row_num1, [row_num2], ...)", "d": "Returnerer rader fra en matrise eller referanse.", "ad": "<PERSON><PERSON><PERSON> eller referansen som inneholder radene som skal returneres.!Nummeret på raden som skal returneres."}, "CHOOSECOLS": {"a": "(matrise, col_num1, [col_num2], ...)", "d": "Returnerer kolonner fra en matrise eller referanse.", "ad": "Mat<PERSON>n eller referansen som inneholder kolonnene som skal returneres.!Nummeret på kolonnen som skal returneres."}, "TOCOL": {"a": "(matrise, [ignorer], [scan_by_column])", "d": " Returnerer matrisen som én kolonne.", "ad": " Matrisen eller referansen som skal returneres som en kolonne.!Om bestemte typer verdier skal ignoreres. Som standard ignoreres ingen verdier.!Skann matrisen etter kolonne. Matrisen skannes som standard etter rad."}, "TOROW": {"a": "(matrise, [ignorer], [skann_etter_kolonne])", "d": "Returnerer matrisen som én rad.", "ad": "Matrisen eller referansen som skal returneres som en rad.!Om bestemte typer verdier skal ignoreres. Som standard ignoreres ingen verdier.!Skann matrisen etter kolonne. Matrisen skannes som standard etter rad."}, "WRAPCOLS": {"a": "(vektor, wrap_count, [pad_with])", "d": "B<PERSON>ter en rad- eller kolonnevektor etter et bestemt antall verdier.", "ad": "Vektoren eller referansen som skal brytes.!Maksimalt antall verdier per kolonne.!Verdien som skal utfylles med. Standard er #N/A."}, "TAKE": {"a": "(matrise, rader, [kolonner])", "d": "Returnerer rader eller kolonner fra matrisestart eller -slutt.", "ad": "Matrisen som rader eller kolonner skal hentes fra.!<PERSON><PERSON><PERSON> rader som skal tas. En negativ verdi tar fra slutten av matrisen.!Antall kolonner som skal tas. En negativ verdi tar fra slutten av matrisen."}, "DROP": {"a": "(matrise, rader, [kolonner])", "d": "Sletter rader eller kolonner fra matrisestart eller -slutt.", "ad": "Matrisen som rader eller kolonner skal slettes fra.!<PERSON>tal<PERSON> rader som skal slettes. En negativ verdi faller fra slutten av matrisen.!Antall kolonner som skal slettes. En negativ verdi faller fra slutten av matrisen."}, "SEQUENCE": {"a": "(rader, [kolonner], [start], [trinn])", "d": "Returnerer en tallsekvens", "ad": "antall rader som skal returneres!antall kolonner som skal returneres!det første tallet i sekvensen!antallet hver etterfølgende verdi i sekvensen skal økes med"}, "EXPAND": {"a": "(matrise, rader, [kolonner], [fyll_med])", "d": "Utvider en matrise til de angitte dimensjonene.", "ad": "Matrisen som skal utvides.!Antal<PERSON> rader i den utvidede matrisen. <PERSON><PERSON> det mangler, utvides ikke radene.!Antall kolonner i den utvidede matrisen. <PERSON><PERSON> det mangler, utvides ikke kolonnene.!Verdien du vil tastatur med. Standard er #N/A."}, "XMATCH": {"a": "(oppslag_verdi, oppslag_matrise, [treff_modus], [søk_modus])", "d": "Returnerer den relative plasseringen av et element i en matrise. Som standard er et eksakt treff nødvendig", "ad": "er verdien du vil søke etter!er matrisen eller området du vil søke i!angi hvordan oppslag_verdi skal samsvare med verdiene i oppslag_matrise!angi søkemodusen du vil bruke. Første-til-siste-søk blir brukt som standard"}, "FILTER": {"a": "(matrise, inkluder, [hvis_tom])", "d": "<PERSON>lt<PERSON><PERSON> et område eller en matrise", "ad": "området eller matrisen som skal filtreres!en matrise med boolske verdier der SANN representerer en rad eller kolonne som skal beholdes!returneres hvis ingen elementer beholdes"}, "ARRAYTOTEXT": {"a": "(matrise, [format])", "d": "Returnerer en tekstrepresentasjon av en matrise", "ad": "matrisen som skal representeres som tekst!formatet på teksten"}, "SORT": {"a": "(matrise, [sortering<PERSON><PERSON><PERSON>], [sorteringsrek<PERSON>fø<PERSON>], [etter_kolonne])", "d": "So<PERSON><PERSON> et område eller en matrise", "ad": "området eller matrisen som skal sorteres!et tall som angir raden eller kolonnen det skal sorteres etter!et tall som angir den ønskede sorteringsrekkefølgen; 1 for stigende rekkefølge (standard), -1 for synkende rekkefølge!en logisk verdi som angir den ønskede sorteringsretningen: USANN hvis du vil sortere etter rad (standard), SANN hvis du vil sortere etter kolonne"}, "SORTBY": {"a": "(matrise, etter_matrise, [sorteringsrekkefølge], ...)", "d": "Sorter<PERSON> et område eller en matrise basert på verdiene i et tilsvarende område eller en tilsvarende matrise", "ad": "området eller matrisen som skal sorteres!området eller matrisen det skal sorteres etter!et tall som angir den ønskede sorteringsrekkefølgen, 1 for stigende rekkefølge (standard), -1 for synkende rekkefølge"}, "GETPIVOTDATA": {"a": "(data_felt; pivottabell; [felt]; [element]; ...)", "d": "Trekker ut data som er lagret i en pivottabell.", "ad": "er navnet på datafeltet du vil trekke ut data fra!er en referanse til en celle eller et celleområde i pivottabellen som inneholder dataene du vil hente!felt som det refereres til!feltelement det refereres til"}, "IMPORTRANGE": {"a": "(nettadresse_til_regneark; områdestreng)", "d": "Importerer et celleområde fra et angitt regneark.", "ad": "nettadressen til regnearket dataene skal importeres fra!området som skal importeres"}}