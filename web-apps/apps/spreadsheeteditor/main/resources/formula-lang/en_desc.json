{"DATE": {"a": "(year; month; day)", "d": "Returns the number that represents the date in the date-time code", "ad": "is a number from 1900 or 1904 (depending on the workbook's date system) to 9999!is a number from 1 to 12 representing the month of the year!is a number from 1 to 31 representing the day of the month"}, "DATEDIF": {"a": "( start-date; end-date; unit )", "d": "Returns the difference between two date values (start date and end date), based on the interval (unit) specified", "ad": "is a date that represents the first, or starting date of a given period!is a date that represents the last, or ending, date of the period!is the type of information that you want returned"}, "DATEVALUE": {"a": "(date_text)", "d": "Converts a date in the form of text to a number that represents the date in the date-time code", "ad": "is text that represents a date in a Spreadsheet Editor date format, between 1/1/1900 or 1/1/1904 (depending on the workbook's date system) and 12/31/9999"}, "DAY": {"a": "(serial_number)", "d": "Returns the day of the month, a number from 1 to 31.", "ad": "is a number in the date-time code used by Spreadsheet Editor"}, "DAYS": {"a": "(end_date; start_date)", "d": "Returns the number of days between the two dates.", "ad": "start_date and end_date are the two dates between which you want to know the number of days!start_date and end_date are the two dates between which you want to know the number of days"}, "DAYS360": {"a": "(start_date; end_date; [method])", "d": "Returns the number of days between two dates based on a 360-day year (twelve 30-day months)", "ad": "start_date and end_date are the two dates between which you want to know the number of days!start_date and end_date are the two dates between which you want to know the number of days!is a logical value specifying the calculation method: U.S. (NASD) = FALSE or omitted; European = TRUE."}, "EDATE": {"a": "(start_date; months)", "d": "Returns the serial number of the date that is the indicated number of months before or after the start date", "ad": "is a serial date number that represents the start date!is the number of months before or after start_date"}, "EOMONTH": {"a": "(start_date; months)", "d": "Returns the serial number of the last day of the month before or after a specified number of months", "ad": "is a serial date number that represents the start date!is the number of months before or after the start_date"}, "HOUR": {"a": "(serial_number)", "d": "Returns the hour as a number from 0 (12:00 A.M.) to 23 (11:00 P.M.).", "ad": "is a number in the date-time code used by Spreadsheet Editor, or text in time format, such as 16:48:00 or 4:48:00 PM"}, "ISOWEEKNUM": {"a": "(date)", "d": "Returns the ISO week number in the year for a given date", "ad": "is the date-time code used by Spreadsheet Editor for date and time calculation"}, "MINUTE": {"a": "(serial_number)", "d": "Returns the minute, a number from 0 to 59.", "ad": "is a number in the date-time code used by Spreadsheet Editor or text in time format, such as 16:48:00 or 4:48:00 PM"}, "MONTH": {"a": "(serial_number)", "d": "Returns the month, a number from 1 (January) to 12 (December).", "ad": "is a number in the date-time code used by Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(start_date; end_date; [holidays])", "d": "Returns the number of whole workdays between two dates", "ad": "is a serial date number that represents the start date!is a serial date number that represents the end date!is an optional set of one or more serial date numbers to exclude from the working calendar, such as state and federal holidays and floating holidays"}, "NETWORKDAYS.INTL": {"a": "(start_date; end_date; [weekend]; [holidays])", "d": "Returns the number of whole workdays between two dates with custom weekend parameters", "ad": "is a serial date number that represents the start date!is a serial date number that represents the end date!is a number or string specifying when weekends occur!is an optional set of one or more serial date numbers to exclude from the working calendar, such as state and federal holidays and floating holidays"}, "NOW": {"a": "()", "d": "Returns the current date and time formatted as a date and time.", "ad": ""}, "SECOND": {"a": "(serial_number)", "d": "Returns the second, a number from 0 to 59.", "ad": "is a number in the date-time code used by Spreadsheet Editor or text in time format, such as 16:48:23 or 4:48:47 PM"}, "TIME": {"a": "(hour; minute; second)", "d": "Converts hours, minutes and seconds given as numbers to a serial number, formatted with a time format", "ad": "is a number from 0 to 23 representing the hour!is a number from 0 to 59 representing the minute!is a number from 0 to 59 representing the second"}, "TIMEVALUE": {"a": "(time_text)", "d": "Converts a text time to a serial number for a time, a number from 0 (12:00:00 AM) to 0.999988426 (11:59:59 PM). Format the number with a time format after entering the formula", "ad": "is a text string that gives a time in any one of the Spreadsheet Editor time formats (date information in the string is ignored)"}, "TODAY": {"a": "()", "d": "Returns the current date formatted as a date.", "ad": ""}, "WEEKDAY": {"a": "(serial_number; [return_type])", "d": "Returns a number from 1 to 7 identifying the day of the week of a date.", "ad": "is a number that represents a date!is a number: for Sunday=1 through Saturday=7, use 1; for Monday=1 through Sunday=7, use 2; for Monday=0 through Sunday=6, use 3"}, "WEEKNUM": {"a": "(serial_number; [return_type])", "d": "Returns the week number in the year", "ad": "is the date-time code used by Spreadsheet Editor for date and time calculation!is a number (1 or 2) that determines the type of the return value"}, "WORKDAY": {"a": "(start_date; days; [holidays])", "d": "Returns the serial number of the date before or after a specified number of workdays", "ad": "is a serial date number that represents the start date!is the number of nonweekend and non-holiday days before or after start_date!is an optional array of one or more serial date numbers to exclude from the working calendar, such as state and federal holidays and floating holidays"}, "WORKDAY.INTL": {"a": "(start_date; days; [weekend]; [holidays])", "d": "Returns the serial number of the date before or after a specified number of workdays with custom weekend parameters", "ad": "is a serial date number that represents the start date!is the number of nonweekend and non-holiday days before or after start_date!is a number or string specifying when weekends occur!is an optional array of one or more serial date numbers to exclude from the working calendar, such as state and federal holidays and floating holidays"}, "YEAR": {"a": "(serial_number)", "d": "Returns the year of a date, an integer in the range 1900-9999.", "ad": "is a number in the date-time code used by Spreadsheet Editor"}, "YEARFRAC": {"a": "(start_date; end_date; [basis])", "d": "Returns the year fraction representing the number of whole days between start_date and end_date", "ad": "is a serial date number that represents the start date!is a serial date number that represents the end date!is the type of day count basis to use"}, "BESSELI": {"a": "(x; n)", "d": "Returns the modified <PERSON>ssel function In(x)", "ad": "is the value at which to evaluate the function!is the order of the <PERSON>ssel function"}, "BESSELJ": {"a": "(x; n)", "d": "Returns the <PERSON>ssel function Jn(x)", "ad": "is the value at which to evaluate the function!is the order of the <PERSON>ssel function"}, "BESSELK": {"a": "(x; n)", "d": "Returns the modified <PERSON>ssel function Kn(x)", "ad": "is the value at which to evaluate the function!is the order of the function"}, "BESSELY": {"a": "(x; n)", "d": "Returns the Bessel function Yn(x)", "ad": "is the value at which to evaluate the function!is the order of the function"}, "BIN2DEC": {"a": "(number)", "d": "Converts a binary number to decimal", "ad": "is the binary number you want to convert"}, "BIN2HEX": {"a": "(number; [places])", "d": "Converts a binary number to hexadecimal", "ad": "is the binary number you want to convert!is the number of characters to use"}, "BIN2OCT": {"a": "(number; [places])", "d": "Converts a binary number to octal", "ad": "is the binary number you want to convert!is the number of characters to use"}, "BITAND": {"a": "(number1; number2)", "d": "Returns a bitwise 'And' of two numbers", "ad": "is the decimal representation of the binary number you want to evaluate!is the decimal representation of the binary number you want to evaluate"}, "BITLSHIFT": {"a": "(number; shift_amount)", "d": "Returns a number shifted left by shift_amount bits", "ad": "is the decimal representation of the binary number you want to evaluate!is the number of bits that you want to shift Number left by"}, "BITOR": {"a": "(number1; number2)", "d": "Returns a bitwise 'Or' of two numbers", "ad": "is the decimal representation of the binary number you want to evaluate!is the decimal representation of the binary number you want to evaluate"}, "BITRSHIFT": {"a": "(number; shift_amount)", "d": "Returns a number shifted right by shift_amount bits", "ad": "is the decimal representation of the binary number you want to evaluate!is the number of bits that you want to shift Number right by"}, "BITXOR": {"a": "(number1; number2)", "d": "Returns a bitwise 'Exclusive Or' of two numbers", "ad": "is the decimal representation of the binary number you want to evaluate!is the decimal representation of the binary number you want to evaluate"}, "COMPLEX": {"a": "(real_num; i_num; [suffix])", "d": "Converts real and imaginary coefficients into a complex number", "ad": "is the real coefficient of the complex number!is the imaginary coefficient of the complex number!is the suffix for the imaginary component of the complex number"}, "CONVERT": {"a": "(number; from_unit; to_unit)", "d": "Converts a number from one measurement system to another", "ad": "is the value in from_units to convert!is the units for number!is the units for the result"}, "DEC2BIN": {"a": "(number; [places])", "d": "Converts a decimal number to binary", "ad": "is the decimal integer you want to convert!is the number of characters to use"}, "DEC2HEX": {"a": "(number; [places])", "d": "Converts a decimal number to hexadecimal", "ad": "is the decimal integer you want to convert!is the number of characters to use"}, "DEC2OCT": {"a": "(number; [places])", "d": "Converts a decimal number to octal", "ad": "is the decimal integer you want to convert!is the number of characters to use"}, "DELTA": {"a": "(number1; [number2])", "d": "Tests whether two numbers are equal", "ad": "is the first number!is the second number"}, "ERF": {"a": "(lower_limit; [upper_limit])", "d": "Returns the error function", "ad": "is the lower bound for integrating ERF!is the upper bound for integrating ERF"}, "ERF.PRECISE": {"a": "(X)", "d": "Returns the error function", "ad": "is the lower bound for integrating ERF.PRECISE"}, "ERFC": {"a": "(x)", "d": "Returns the complementary error function", "ad": "is the lower bound for integrating ERF"}, "ERFC.PRECISE": {"a": "(X)", "d": "Returns the complementary error function", "ad": "is the lower bound for integrating ERFC.PRECISE"}, "GESTEP": {"a": "(number; [step])", "d": "Tests whether a number is greater than a threshold value", "ad": "is the value to test against step!is the threshold value"}, "HEX2BIN": {"a": "(number; [places])", "d": "Converts a Hexadecimal number to binary", "ad": "is the hexadecimal number you want to convert!is the number of characters to use"}, "HEX2DEC": {"a": "(number)", "d": "Converts a hexadecimal number to decimal", "ad": "is the hexadecimal number you want to convert"}, "HEX2OCT": {"a": "(number; [places])", "d": "Converts a hexadecimal number to octal", "ad": "is the hexadecimal number you want to convert!is the number of characters to use"}, "IMABS": {"a": "(inumber)", "d": "Returns the absolute value (modulus) of a complex number", "ad": "is a complex number for which you want the absolute value"}, "IMAGINARY": {"a": "(inumber)", "d": "Returns the imaginary coefficient of a complex number", "ad": "is a complex number for which you want the imaginary coefficient"}, "IMARGUMENT": {"a": "(inumber)", "d": "Returns the argument q, an angle expressed in radians", "ad": "is a complex number for which you want the argument"}, "IMCONJUGATE": {"a": "(inumber)", "d": "Returns the complex conjugate of a complex number", "ad": "is a complex number for which you want the conjugate"}, "IMCOS": {"a": "(inumber)", "d": "Returns the cosine of a complex number", "ad": "is a complex number for which you want the cosine"}, "IMCOSH": {"a": "(inumber)", "d": "Returns the hyperbolic cosine of a complex number", "ad": "is a complex number for which you want the hyperbolic cosine"}, "IMCOT": {"a": "(inumber)", "d": "Returns the cotangent of a complex number", "ad": "is a complex number for which you want the cotangent"}, "IMCSC": {"a": "(inumber)", "d": "Returns the cosecant of a complex number", "ad": "is a complex number for which you want the cosecant"}, "IMCSCH": {"a": "(inumber)", "d": "Returns the hyperbolic cosecant of a complex number", "ad": "is a complex number for which you want the hyperbolic cosecant"}, "IMDIV": {"a": "(inumber1; inumber2)", "d": "Returns the quotient of two complex numbers", "ad": "is the complex numerator or dividend!is the complex denominator or divisor"}, "IMEXP": {"a": "(inumber)", "d": "Returns the exponential of a complex number", "ad": "is a complex number for which you want the exponential"}, "IMLN": {"a": "(inumber)", "d": "Returns the natural logarithm of a complex number", "ad": "is a complex number for which you want the natural logarithm"}, "IMLOG10": {"a": "(inumber)", "d": "Returns the base-10 logarithm of a complex number", "ad": "is a complex number for which you want the common logarithm"}, "IMLOG2": {"a": "(inumber)", "d": "Returns the base-2 logarithm of a complex number", "ad": "is a complex number for which you want the base-2 logarithm"}, "IMPOWER": {"a": "(inumber; number)", "d": "Returns a complex number raised to an integer power", "ad": "is a complex number you want to raise to a power!is the power to which you want to raise the complex number"}, "IMPRODUCT": {"a": "(inumber1; [inumber2]; ...)", "d": "Returns the product of 1 to 255 complex numbers", "ad": "Inumber1, Inumber2,... are from 1 to 255 complex numbers to multiply."}, "IMREAL": {"a": "(inumber)", "d": "Returns the real coefficient of a complex number", "ad": "is a complex number for which you want the real coefficient"}, "IMSEC": {"a": "(inumber)", "d": "Returns the secant of a complex number", "ad": "is a complex number for which you want the secant"}, "IMSECH": {"a": "(inumber)", "d": "Returns the hyperbolic secant of a complex number", "ad": "is a complex number for which you want the hyperbolic secant"}, "IMSIN": {"a": "(inumber)", "d": "Returns the sine of a complex number", "ad": "is a complex number for which you want the sine"}, "IMSINH": {"a": "(inumber)", "d": "Returns the hyperbolic sine of a complex number", "ad": "is a complex number for which you want the hyperbolic sine"}, "IMSQRT": {"a": "(inumber)", "d": "Returns the square root of a complex number", "ad": "is a complex number for which you want the square root"}, "IMSUB": {"a": "(inumber1; inumber2)", "d": "Returns the difference of two complex numbers", "ad": "is the complex number from which to subtract inumber2!is the complex number to subtract from inumber1"}, "IMSUM": {"a": "(inumber1; [inumber2]; ...)", "d": "Returns the sum of complex numbers", "ad": "are from 1 to 255 complex numbers to add"}, "IMTAN": {"a": "(inumber)", "d": "Returns the tangent of a complex number", "ad": "is a complex number for which you want the tangent"}, "OCT2BIN": {"a": "(number; [places])", "d": "Converts an octal number to binary", "ad": "is the octal number you want to convert!is the number of characters to use"}, "OCT2DEC": {"a": "(number)", "d": "Converts an octal number to decimal", "ad": "is the octal number you want to convert"}, "OCT2HEX": {"a": "(number; [places])", "d": "Converts an octal number to hexadecimal", "ad": "is the octal number you want to convert!is the number of characters to use"}, "DAVERAGE": {"a": "(database; field; criteria)", "d": "Averages the values in a column in a list or database that match conditions you specify", "ad": "is the range of cells that makes up the list or database. A database is a list of related data!is either the label of the column in double quotation marks or a number that represents the column's position in the list!is the range of cells that contains the conditions you specify. The range includes a column label and one cell below the label for a condition"}, "DCOUNT": {"a": "(database; field; criteria)", "d": "Counts the cells containing numbers in the field (column) of records in the database that match the conditions you specify", "ad": "is the range of cells that makes up the list or database. A database is a list of related data!is either the label of the column in double quotation marks or a number that represents the column's position in the list!is the range of cells that contains the conditions you specify. The range includes a column label and one cell below the label for a condition"}, "DCOUNTA": {"a": "(database; field; criteria)", "d": "Counts nonblank cells in the field (column) of records in the database that match the conditions you specify", "ad": "is the range of cells that makes up the list or database. A database is a list of related data!is either the label of the column in double quotation marks or a number that represents the column's position in the list!is the range of cells that contains the conditions you specify. The range includes a column label and one cell below the label for a condition"}, "DGET": {"a": "(database; field; criteria)", "d": "Extracts from a database a single record that matches the conditions you specify", "ad": "is the range of cells that makes up the list or database. A database is a list of related data!is either the label of the column in double quotation marks or a number that represents the column's position in the list!is the range of cells that contains the conditions you specify. The range includes a column label and one cell below the label for a condition"}, "DMAX": {"a": "(database; field; criteria)", "d": "Returns the largest number in the field (column) of records in the database that match the conditions you specify", "ad": "is the range of cells that makes up the list or database. A database is a list of related data!is either the label of the column in double quotation marks or a number that represents the column's position in the list!is the range of cells that contains the conditions you specify. The range includes a column label and one cell below the label for a condition"}, "DMIN": {"a": "(database; field; criteria)", "d": "Returns the smallest number in the field (column) of records in the database that match the conditions you specify", "ad": "is the range of cells that makes up the list or database. A database is a list of related data!is either the label of the column in double quotation marks or a number that represents the column's position in the list!is the range of cells that contains the conditions you specify. The range includes a column label and one cell below the label for a condition"}, "DPRODUCT": {"a": "(database; field; criteria)", "d": "Multiplies the values in the field (column) of records in the database that match the conditions you specify", "ad": "is the range of cells that makes up the list or database. A database is a list of related data!is either the label of the column in double quotation marks or a number that represents the column's position in the list!is the range of cells that contains the conditions you specify. The range includes a column label and one cell below the label for a condition"}, "DSTDEV": {"a": "(database; field; criteria)", "d": "Estimates the standard deviation based on a sample from selected database entries", "ad": "is the range of cells that makes up the list or database. A database is a list of related data!is either the label of the column in double quotation marks or a number that represents the column's position in the list!is the range of cells that contains the conditions you specify. The range includes a column label and one cell below the label for a condition"}, "DSTDEVP": {"a": "(database; field; criteria)", "d": "Calculates the standard deviation based on the entire population of selected database entries", "ad": "is the range of cells that makes up the list or database. A database is a list of related data!is either the label of the column in double quotation marks or a number that represents the column's position in the list!is the range of cells that contains the conditions you specify. The range includes a column label and one cell below the label for a condition"}, "DSUM": {"a": "(database; field; criteria)", "d": "Adds the numbers in the field (column) of records in the database that match the conditions you specify", "ad": "is the range of cells that makes up the list or database. A database is a list of related data!is either the label of the column in double quotation marks or a number that represents the column's position in the list!is the range of cells that contains the conditions you specify. The range includes a column label and one cell below the label for a condition"}, "DVAR": {"a": "(database; field; criteria)", "d": "Estimates variance based on a sample from selected database entries", "ad": "is the range of cells that makes up the list or database. A database is a list of related data!is either the label of the column in double quotation marks or a number that represents the column's position in the list!is the range of cells that contains the conditions you specify. The range includes a column label and one cell below the label for a condition"}, "DVARP": {"a": "(database; field; criteria)", "d": "Calculates variance based on the entire population of selected database entries", "ad": "is the range of cells that makes up the list or database. A database is a list of related data!is either the label of the column in double quotation marks or a number that represents the column's position in the list!is the range of cells that contains the conditions you specify. The range includes a column label and one cell below the label for a condition"}, "CHAR": {"a": "(number)", "d": "Returns the character specified by the code number from the character set for your computer", "ad": "is a number between 1 and 255 specifying which character you want"}, "CLEAN": {"a": "(text)", "d": "Removes all nonprintable characters from text", "ad": "is any worksheet information from which you want to remove nonprintable characters"}, "CODE": {"a": "(text)", "d": "Returns a numeric code for the first character in a text string, in the character set used by your computer", "ad": "is the text for which you want the code of the first character"}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "Joins several text strings into one text string", "ad": "are 1 to 255 text strings to be joined into a single text string and can be text strings, numbers, or single-cell references"}, "CONCAT": {"a": "(text1; ...)", "d": "Concatenates a list or range of text strings", "ad": "are 1 to 254 text strings or ranges to be joined to a single text string"}, "DOLLAR": {"a": "(number; [decimals])", "d": "Converts a number to text, using currency format", "ad": "is a number, a reference to a cell containing a number, or a formula that evaluates to a number!is the number of digits to the right of the decimal point. The number is rounded as necessary; if omitted, Decimals = 2"}, "EXACT": {"a": "(text1; text2)", "d": "Checks whether two text strings are exactly the same, and returns TRUE or FALSE. EXACT is case-sensitive", "ad": "is the first text string!is the second text string"}, "FIND": {"a": "(find_text; within_text; [start_num])", "d": "Returns the starting position of one text string within another text string. FIND is case-sensitive", "ad": "is the text you want to find. Use double quotes (empty text) to match the first character in Within_text; wildcard characters not allowed!is the text containing the text you want to find!specifies the character at which to start the search. The first character in Within_text is character number 1. If omitted, Start_num = 1"}, "FINDB": {"a": "( string-1; string-2; [start-pos] )", "d": "Finds the specified substring (string-1) within a string (string-2) and is intended for languages the double-byte character set (DBCS) like Japanese, Chinese, Korean etc.", "ad": "is the text you want to find. Use double quotes (empty text) to match the first character in string-2; wildcard characters not allowed!is the text containing the text you want to find!specifies the character at which to start the search. The first character in string-2 is character number 1. If omitted, start-pos = 1"}, "FIXED": {"a": "(number; [decimals]; [no_commas])", "d": "Rounds a number to the specified number of decimals and returns the result as text with or without commas", "ad": "is the number you want to round and convert to text!is the number of digits to the right of the decimal point. If omitted, Decimals = 2!is a logical value: do not display commas in the returned text = TRUE; do display commas in the returned text = FALSE or omitted"}, "LEFT": {"a": "(text; [num_chars])", "d": "Returns the specified number of characters from the start of a text string", "ad": "is the text string containing the characters you want to extract!specifies how many characters you want LEFT to extract; 1 if omitted"}, "LEFTB": {"a": "( string; [number-chars] )", "d": "Extracts the substring from the specified string starting from the left character and is intended for languages that use the double-byte character set (DBCS) like Japanese, Chinese, Korean etc.", "ad": "is the text string containing the characters you want to extract!specifies how many characters you want LEFTB to extract; 1 if omitted"}, "LEN": {"a": "(text)", "d": "Returns the number of characters in a text string", "ad": "is the text whose length you want to find. Spaces count as characters"}, "LENB": {"a": "( string )", "d": "Analyses the specified string and returns the number of characters it contains and is intended for languages that use the double-byte character set (DBCS) like Japanese, Chinese, Korean etc.", "ad": "is the text whose length you want to find. Spaces count as characters"}, "LOWER": {"a": "(text)", "d": "Converts all letters in a text string to lowercase", "ad": "is the text you want to convert to lowercase. Characters in Text that are not letters are not changed"}, "MID": {"a": "(text; start_num; num_chars)", "d": "Returns the characters from the middle of a text string, given a starting position and length", "ad": "is the text string from which you want to extract the characters!is the position of the first character you want to extract. The first character in Text is 1!specifies how many characters to return from Text"}, "MIDB": {"a": "( string; start-pos; number-chars )", "d": "Extracts the characters from the specified string starting from any position and is intended for languages that use the double-byte character set (DBCS) like Japanese, Chinese, Korean etc.", "ad": "is the text string from which you want to extract the characters!is the position of the first character you want to extract. The first character in String is 1!specifies how many characters to return from String"}, "NUMBERVALUE": {"a": "(text; [decimal_separator]; [group_separator])", "d": "Converts text to number in a locale-independent manner", "ad": "is the string representing the number you want to convert!is the character used as the decimal separator in the string!is the character used as the group separator in the string"}, "PROPER": {"a": "(text)", "d": "Converts a text string to proper case; the first letter in each word to uppercase, and all other letters to lowercase", "ad": "is text enclosed in quotation marks, a formula that returns text, or a reference to a cell containing text to partially capitalize"}, "REPLACE": {"a": "(old_text; start_num; num_chars; new_text)", "d": "Replaces part of a text string with a different text string", "ad": "is text in which you want to replace some characters!is the position of the character in Old_text that you want to replace with New_text!is the number of characters in Old_text that you want to replace!is the text that will replace characters in Old_text"}, "REPLACEB": {"a": "( string-1; start-pos; number-chars; string-2 )", "d": "Replaces a set of characters, based on the number of characters and the start position you specify, with a new set of characters and is intended for languages that use the double-byte character set (DBCS) like Japanese, Chinese, Korean etc.", "ad": "is text in which you want to replace some characters!is the position of the character in String-1 that you want to replace with String-2!is the number of characters in String-1 that you want to replace!is the text that will replace characters in String-1"}, "REPT": {"a": "(text; number_times)", "d": "Repeats text a given number of times. Use REPT to fill a cell with a number of instances of a text string", "ad": "is the text you want to repeat!is a positive number specifying the number of times to repeat text"}, "RIGHT": {"a": "(text; [num_chars])", "d": "Returns the specified number of characters from the end of a text string", "ad": "is the text string that contains the characters you want to extract!specifies how many characters you want to extract, 1 if omitted"}, "RIGHTB": {"a": "( string; [number-chars] )", "d": "Extracts a substring from a string starting from the right-most character, based on the specified number of characters and is intended for languages that use the double-byte character set (DBCS) like Japanese, Chinese, Korean etc.", "ad": "is the text string that contains the characters you want to extract!specifies how many characters you want to extract, 1 if omitted"}, "SEARCH": {"a": "(find_text; within_text; [start_num])", "d": "Returns the number of the character at which a specific character or text string is first found, reading left to right (not case-sensitive)", "ad": "is the text you want to find. You can use the ? and * wildcard characters; use ~? and ~* to find the ? and * characters!is the text in which you want to search for Find_text!is the character number in Within_text, counting from the left, at which you want to start searching. If omitted, 1 is used"}, "SEARCHB": {"a": "( string-1; string-2; [start-pos] )", "d": "Returns the location of the specified substring in a string and is intended for languages that use the double-byte character set (DBCS) like Japanese, Chinese, Korean etc.", "ad": "is the text you want to find. You can use the ? and * wildcard characters; use ~? and ~* to find the ? and * characters!is the text in which you want to search for String-1!is the character number in String-2, counting from the left, at which you want to start searching. If omitted, 1 is used"}, "SUBSTITUTE": {"a": "(text; old_text; new_text; [instance_num])", "d": "Replaces existing text with new text in a text string", "ad": "is the text or the reference to a cell containing text in which you want to substitute characters!is the existing text you want to replace. If the case of Old_text does not match the case of text, SUBSTITUTE will not replace the text!is the text you want to replace Old_text with!specifies which occurrence of Old_text you want to replace. If omitted, every instance of Old_text is replaced"}, "T": {"a": "(value)", "d": "Checks whether a value is text, and returns the text if it is, or returns double quotes (empty text) if it is not", "ad": "is the value to test"}, "TEXT": {"a": "(value; format_text)", "d": "Converts a value to text in a specific number format", "ad": "is a number, a formula that evaluates to a numeric value, or a reference to a cell containing a numeric value!is a number format in text form from the Category box on the Number tab in the Format Cells dialog box"}, "TEXTJOIN": {"a": "(delimiter; ignore_empty; text1; ...)", "d": "Concatenates a list or range of text strings using a delimiter", "ad": "Character or string to insert between each text item!if TRUE(default), ignores empty cells!are 1 to 252 text strings or ranges to be joined"}, "TRIM": {"a": "(text)", "d": "Removes all spaces from a text string except for single spaces between words", "ad": "is the text from which you want spaces removed"}, "UNICHAR": {"a": "(number)", "d": "Returns the Unicode character referenced by the given numeric value", "ad": "is the Unicode number representing a character"}, "UNICODE": {"a": "(text)", "d": "Returns the number (code point) corresponding to the first character of the text", "ad": "is the character that you want the Unicode value of"}, "UPPER": {"a": "(text)", "d": "Converts a text string to all uppercase letters", "ad": "is the text you want converted to uppercase, a reference or a text string"}, "VALUE": {"a": "(text)", "d": "Converts a text string that represents a number to a number", "ad": "is the text enclosed in quotation marks or a reference to a cell containing the text you want to convert"}, "AVEDEV": {"a": "(number1; [number2]; ...)", "d": "Returns the average of the absolute deviations of data points from their mean. Arguments can be numbers or names, arrays or references that contain numbers", "ad": "are 1 to 255 arguments for which you want the average of the absolute deviations"}, "AVERAGE": {"a": "(number1; [number2]; ...)", "d": "Returns the average (arithmetic mean) of its arguments, which can be numbers or names, arrays or references that contain numbers", "ad": "are 1 to 255 numeric arguments for which you want the average"}, "AVERAGEA": {"a": "(value1; [value2]; ...)", "d": "Returns the average (arithmetic mean) of its arguments, evaluating text and FALSE in arguments as 0; TRUE evaluates as 1. Arguments can be numbers, names, arrays or references", "ad": "are 1 to 255 arguments for which you want the average"}, "AVERAGEIF": {"a": "(range; criteria; [average_range])", "d": "Finds average (arithmetic mean) for the cells specified by a given condition or criteria", "ad": "is the range of cells you want evaluated!is the condition or criteria in the form of a number, expression, or text that defines which cells will be used to find the average!are the actual cells to be used to find the average. If omitted, the cells in range are used"}, "AVERAGEIFS": {"a": "(average_range; criteria_range; criteria; ...)", "d": "Finds average (arithmetic mean) for the cells specified by a given set of conditions or criteria", "ad": "are the actual cells to be used to find the average.!is the range of cells you want evaluated for the particular condition!is the condition or criteria in the form of a number, expression, or text that defines which cells will be used to find the average"}, "BETADIST": {"a": "(x; alpha; beta; [A]; [B])", "d": "Returns the cumulative beta probability density function", "ad": "is the value between A and B at which to evaluate the function!is a parameter to the distribution and must be greater than 0!is a parameter to the distribution and must be greater than 0!is an optional lower bound to the interval of x. If omitted, A = 0!is an optional upper bound to the interval of x. If omitted, B = 1"}, "BETAINV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "Returns the inverse of the cumulative beta probability density function (BETADIST)", "ad": "is a probability associated with the beta distribution!is a parameter to the distribution and must be greater than 0!is a parameter to the distribution and must be greater than 0!is an optional lower bound to the interval of x. If omitted, A = 0!is an optional upper bound to the interval of x. If omitted, B = 1"}, "BETA.DIST": {"a": "(x; alpha; beta; cumulative; [A]; [B])", "d": "Returns the beta probability distribution function", "ad": "is the value between A and B at which to evaluate the function!is a parameter to the distribution and must be greater than 0!is a parameter to the distribution and must be greater than 0!is a logical value: for the cumulative distribution function, use TRUE; for the probability density function, use FALSE!is an optional lower bound to the interval of x. If omitted, A = 0!is an optional upper bound to the interval of x. If omitted, B = 1"}, "BETA.INV": {"a": "(probability; alpha; beta; [A]; [B])", "d": "Returns the inverse of the cumulative beta probability density function (BETA.DIST)", "ad": "is a probability associated with the beta distribution!is a parameter to the distribution and must be greater than 0!is a parameter to the distribution and must be greater than 0!is an optional lower bound to the interval of x. If omitted, A = 0!is an optional upper bound to the interval of x. If omitted, B = 1"}, "BINOMDIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "Returns the individual term binomial distribution probability", "ad": "is the number of successes in trials!is the number of independent trials!is the probability of success on each trial!is a logical value: for the cumulative distribution function, use TRUE; for the probability mass function, use FALSE"}, "BINOM.DIST": {"a": "(number_s; trials; probability_s; cumulative)", "d": "Returns the individual term binomial distribution probability", "ad": "is the number of successes in trials!is the number of independent trials!is the probability of success on each trial!is a logical value: for the cumulative distribution function, use TRUE; for the probability mass function, use FALSE"}, "BINOM.DIST.RANGE": {"a": "(trials; probability_s; number_s; [number_s2])", "d": "Returns the probability of a trial result using a binomial distribution", "ad": "is the number of independent trials!is the probability of success on each trial!is the number of successes in trials!if provided this function returns the probability that the number of successful trials shall lie between number_s and number_s2"}, "BINOM.INV": {"a": "(trials; probability_s; alpha)", "d": "Returns the smallest value for which the cumulative binomial distribution is greater than or equal to a criterion value", "ad": "is the number of <PERSON><PERSON><PERSON> trials!is the probability of success on each trial, a number between 0 and 1 inclusive!is the criterion value, a number between 0 and 1 inclusive"}, "CHIDIST": {"a": "(x; deg_freedom)", "d": "Returns the right-tailed probability of the chi-squared distribution", "ad": "is the value at which you want to evaluate the distribution, a nonnegative number!is the number of degrees of freedom, a number between 1 and 10^10, excluding 10^10"}, "CHIINV": {"a": "(probability; deg_freedom)", "d": "Returns the inverse of the right-tailed probability of the chi-squared distribution", "ad": "is a probability associated with the chi-squared distribution, a value between 0 and 1 inclusive!is the number of degrees of freedom, a number between 1 and 10^10, excluding 10^10"}, "CHITEST": {"a": "(actual_range; expected_range)", "d": "Returns the test for independence: the value from the chi-squared distribution for the statistic and the appropriate degrees of freedom", "ad": "is the range of data that contains observations to test against expected values!is the range of data that contains the ratio of the product of row totals and column totals to the grand total"}, "CHISQ.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "Returns the left-tailed probability of the chi-squared distribution", "ad": "is the value at which you want to evaluate the distribution, a nonnegative number!is the number of degrees of freedom, a number between 1 and 10^10, excluding 10^10!is a logical value for the function to return: the cumulative distribution function = TRUE; the probability density function = FALSE"}, "CHISQ.DIST.RT": {"a": "(x; deg_freedom)", "d": "Returns the right-tailed probability of the chi-squared distribution", "ad": "is the value at which you want to evaluate the distribution, a nonnegative number!is the number of degrees of freedom, a number between 1 and 10^10, excluding 10^10"}, "CHISQ.INV": {"a": "(probability; deg_freedom)", "d": "Returns the inverse of the left-tailed probability of the chi-squared distribution", "ad": "is a probability associated with the chi-squared distribution, a value between 0 and 1 inclusive!is the number of degrees of freedom, a number between 1 and 10^10, excluding 10^10"}, "CHISQ.INV.RT": {"a": "(probability; deg_freedom)", "d": "Returns the inverse of the right-tailed probability of the chi-squared distribution", "ad": "is a probability associated with the chi-squared distribution, a value between 0 and 1 inclusive!is the number of degrees of freedom, a number between 1 and 10^10, excluding 10^10"}, "CHISQ.TEST": {"a": "(actual_range; expected_range)", "d": "Returns the test for independence: the value from the chi-squared distribution for the statistic and the appropriate degrees of freedom", "ad": "is the range of data that contains observations to test against expected values!is the range of data that contains the ratio of the product of row totals and column totals to the grand total"}, "CONFIDENCE": {"a": "(alpha; standard_dev; size)", "d": "Returns the confidence interval for a population mean, using a normal distribution", "ad": "is the significance level used to compute the confidence level, a number greater than 0 and less than 1!is the population standard deviation for the data range and is assumed to be known. Standard_dev must be greater than 0!is the sample size"}, "CONFIDENCE.NORM": {"a": "(alpha; standard_dev; size)", "d": "Returns the confidence interval for a population mean, using a normal distribution", "ad": "is the significance level used to compute the confidence level, a number greater than 0 and less than 1!is the population standard deviation for the data range and is assumed to be known. Standard_dev must be greater than 0!is the sample size"}, "CONFIDENCE.T": {"a": "(alpha; standard_dev; size)", "d": "Returns the confidence interval for a population mean, using a Student's T distribution", "ad": "is the significance level used to compute the confidence level, a number greater than 0 and less than 1!is the population standard deviation for the data range and is assumed to be known. Standard_dev must be greater than 0!is the sample size"}, "CORREL": {"a": "(array1; array2)", "d": "Returns the correlation coefficient between two data sets", "ad": "is a cell range of values. The values should be numbers, names, arrays, or references that contain numbers!is a second cell range of values. The values should be numbers, names, arrays, or references that contain numbers"}, "COUNT": {"a": "(value1; [value2]; ...)", "d": "Counts the number of cells in a range that contain numbers", "ad": "are 1 to 255 arguments that can contain or refer to a variety of different types of data, but only numbers are counted"}, "COUNTA": {"a": "(value1; [value2]; ...)", "d": "Counts the number of cells in a range that are not empty", "ad": "are 1 to 255 arguments representing the values and cells you want to count. Values can be any type of information"}, "COUNTBLANK": {"a": "(range)", "d": "Counts the number of empty cells in a specified range of cells", "ad": "is the range from which you want to count the empty cells"}, "COUNTIF": {"a": "(range; criteria)", "d": "Counts the number of cells within a range that meet the given condition", "ad": "is the range of cells from which you want to count nonblank cells!is the condition in the form of a number, expression, or text that defines which cells will be counted"}, "COUNTIFS": {"a": "(criteria_range; criteria; ...)", "d": "Counts the number of cells specified by a given set of conditions or criteria", "ad": "is the range of cells you want evaluated for the particular condition!is the condition in the form of a number, expression, or text that defines which cells will be counted"}, "COVAR": {"a": "(array1; array2)", "d": "Returns covariance, the average of the products of deviations for each data point pair in two data sets", "ad": "is the first cell range of integers and must be numbers, arrays, or references that contain numbers!is the second cell range of integers and must be numbers, arrays, or references that contain numbers"}, "COVARIANCE.P": {"a": "(array1; array2)", "d": "Returns population covariance, the average of the products of deviations for each data point pair in two data sets", "ad": "is the first cell range of integers and must be numbers, arrays, or references that contain numbers!is the second cell range of integers and must be numbers, arrays, or references that contain numbers"}, "COVARIANCE.S": {"a": "(array1; array2)", "d": "Returns sample covariance, the average of the products of deviations for each data point pair in two data sets", "ad": "is the first cell range of integers and must be numbers, arrays, or references that contain numbers!is the second cell range of integers and must be numbers, arrays, or references that contain numbers"}, "CRITBINOM": {"a": "(trials; probability_s; alpha)", "d": "Returns the smallest value for which the cumulative binomial distribution is greater than or equal to a criterion value", "ad": "is the number of <PERSON><PERSON><PERSON> trials!is the probability of success on each trial, a number between 0 and 1 inclusive!is the criterion value, a number between 0 and 1 inclusive"}, "DEVSQ": {"a": "(number1; [number2]; ...)", "d": "Returns the sum of squares of deviations of data points from their sample mean", "ad": "are 1 to 255 arguments, or an array or array reference, on which you want DEVSQ to calculate"}, "EXPONDIST": {"a": "(x; lambda; cumulative)", "d": "Returns the exponential distribution", "ad": "is the value of the function, a nonnegative number!is the parameter value, a positive number!is a logical value for the function to return: the cumulative distribution function = TRUE; the probability density function = FALSE"}, "EXPON.DIST": {"a": "(x; lambda; cumulative)", "d": "Returns the exponential distribution", "ad": "is the value of the function, a nonnegative number!is the parameter value, a positive number!is a logical value for the function to return: the cumulative distribution function = TRUE; the probability density function = FALSE"}, "FDIST": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "Returns the (right-tailed) F probability distribution (degree of diversity) for two data sets", "ad": "is the value at which to evaluate the function, a nonnegative number!is the numerator degrees of freedom, a number between 1 and 10^10, excluding 10^10!is the denominator degrees of freedom, a number between 1 and 10^10, excluding 10^10"}, "FINV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Returns the inverse of the (right-tailed) F probability distribution: if p = FDIST(x,...), then FINV(p,...) = x", "ad": "is a probability associated with the F cumulative distribution, a number between 0 and 1 inclusive!is the numerator degrees of freedom, a number between 1 and 10^10, excluding 10^10!is the denominator degrees of freedom, a number between 1 and 10^10, excluding 10^10"}, "FTEST": {"a": "(array1; array2)", "d": "Returns the result of an F-test, the two-tailed probability that the variances in Array1 and Array2 are not significantly different", "ad": "is the first array or range of data and can be numbers or names, arrays, or references that contain numbers (blanks are ignored)!is the second array or range of data and can be numbers or names, arrays, or references that contain numbers (blanks are ignored)"}, "F.DIST": {"a": "(x; deg_freedom1; deg_freedom2; cumulative)", "d": "Returns the (left-tailed) F probability distribution (degree of diversity) for two data sets", "ad": "is the value at which to evaluate the function, a nonnegative number!is the numerator degrees of freedom, a number between 1 and 10^10, excluding 10^10!is the denominator degrees of freedom, a number between 1 and 10^10, excluding 10^10!is a logical value for the function to return: the cumulative distribution function = TRUE; the probability density function = FALSE"}, "F.DIST.RT": {"a": "(x; deg_freedom1; deg_freedom2)", "d": "Returns the (right-tailed) F probability distribution (degree of diversity) for two data sets", "ad": "is the value at which to evaluate the function, a nonnegative number!is the numerator degrees of freedom, a number between 1 and 10^10, excluding 10^10!is the denominator degrees of freedom, a number between 1 and 10^10, excluding 10^10"}, "F.INV": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Returns the inverse of the (left-tailed) F probability distribution: if p = F.DIST(x,...), then F.INV(p,...) = x", "ad": "is a probability associated with the F cumulative distribution, a number between 0 and 1 inclusive!is the numerator degrees of freedom, a number between 1 and 10^10, excluding 10^10!is the denominator degrees of freedom, a number between 1 and 10^10, excluding 10^10"}, "F.INV.RT": {"a": "(probability; deg_freedom1; deg_freedom2)", "d": "Returns the inverse of the (right-tailed) F probability distribution: if p = F.DIST.RT(x,...), then F.INV.RT(p,...) = x", "ad": "is a probability associated with the F cumulative distribution, a number between 0 and 1 inclusive!is the numerator degrees of freedom, a number between 1 and 10^10, excluding 10^10!is the denominator degrees of freedom, a number between 1 and 10^10, excluding 10^10"}, "F.TEST": {"a": "(array1; array2)", "d": "Returns the result of an F-test, the two-tailed probability that the variances in Array1 and Array2 are not significantly different", "ad": "is the first array or range of data and can be numbers or names, arrays, or references that contain numbers (blanks are ignored)!is the second array or range of data and can be numbers or names, arrays, or references that contain numbers (blanks are ignored)"}, "FISHER": {"a": "(x)", "d": "Returns the Fisher transformation", "ad": "is the value for which you want the transformation, a number between -1 and 1, excluding -1 and 1"}, "FISHERINV": {"a": "(y)", "d": "Returns the inverse of the Fisher transformation: if y = FISHER(x), then FISHERINV(y) = x", "ad": "is the value for which you want to perform the inverse of the transformation"}, "FORECAST": {"a": "(x; known_y's; known_x's)", "d": "Calculates, or predicts, a future value along a linear trend by using existing values", "ad": "is the data point for which you want to predict a value and must be a numeric value!is the dependent array or range of numeric data!is the independent array or range of numeric data. The variance of Known_x's must not be zero"}, "FORECAST.ETS": {"a": "(target_date; values; timeline; [seasonality]; [data_completion]; [aggregation])", "d": "Returns the forecasted value for a specific future target date using exponential smoothing method.", "ad": "is the data point for which Spreadsheet Editor predicts a value. It should carry on the pattern of values in the timeline.!is the array or range of numeric data you're predicting.!is the independent array or range of numeric data. The dates in the timeline must have a consistent step between them and can't be zero.!is an optional numeric value that indicates the length of the seasonal pattern. The default value of 1 indicates seasonality is detected automatically.!is an optional value for handling missing values. The default value of 1 replaces missing values by interpolation, and 0 replaces them with zeros.!is an optional numeric value for aggregating multiple values with the same time stamp. If blank, Spreadsheet Editor averages the values."}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "Returns a confidence interval for the forecast value at the specified target date.", "ad": "is the data point for which Spreadsheet Editor predicts a value. It should carry on the pattern of values in the timeline.!is the array or range of numeric data you're predicting.!is the independent array or range of numeric data. The dates in the timeline must have a consistent step between them and can't be zero.!is a number between 0 and 1 that shows the confidence level for the calculated confidence interval. The default value is .95.!is an optional numeric value that indicates the length of the seasonal pattern. The default value of 1 indicates seasonality is detected automatically.!is an optional value for handling missing values. The default value of 1 replaces missing values by interpolation, and 0 replaces them with zeros.!is an optional numeric value for aggregating multiple values with the same time stamp. If blank, Spreadsheet Editor averages the values."}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "Returns the length of the repetitive pattern an application detects for the specified time series.", "ad": "is the array or range of numeric data you're predicting.!is the independent array or range of numeric data. The dates in the timeline must have a consistent step between them and can't be zero.!is an optional value for handling missing values. The default value of 1 replaces missing values by interpolation, and 0 replaces them with zeros.!is an optional numeric value for aggregating multiple values with the same time stamp. If blank, Spreadsheet Editor averages the values."}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "Returns the requested statistic for the forecast.", "ad": "is the array or range of numeric data you're predicting.!is the independent array or range of numeric data. The dates in the timeline must have a consistent step between them and can't be zero.!is a number between 1 and 8, indicating which statistic Spreadsheet Editor will return for the calculated forecast.!is an optional numeric value that indicates the length of the seasonal pattern. The default value of 1 indicates seasonality is detected automatically.!is an optional value for handling missing values. The default value of 1 replaces missing values by interpolation, and 0 replaces them with zeros.!is an optional numeric value for aggregating multiple values with the same time stamp. If blank, Spreadsheet Editor averages the values."}, "FORECAST.LINEAR": {"a": "(x; known_y's; known_x's)", "d": "Calculates, or predicts, a future value along a linear trend by using existing values", "ad": "is the data point for which you want to predict a value and must be a numeric value!is the dependent array or range of numeric data!is the independent array or range of numeric data. The variance of Known_x's must not be zero"}, "FREQUENCY": {"a": "(data_array; bins_array)", "d": "Calculates how often values occur within a range of values and then returns a vertical array of numbers that have one more element than Bins_array", "ad": "is an array of or reference to a set of values for which you want to count frequencies (blanks and text are ignored)!is an array of or reference to intervals into which you want to group the values in data_array"}, "GAMMA": {"a": "(x)", "d": "Returns the Gamma function value", "ad": "is the value for which you want to calculate Gamma"}, "GAMMADIST": {"a": "(x; alpha; beta; cumulative)", "d": "Returns the gamma distribution", "ad": "is the value at which you want to evaluate the distribution, a nonnegative number!is a parameter to the distribution, a positive number!is a parameter to the distribution, a positive number. If beta = 1, GAMMADIST returns the standard gamma distribution!is a logical value: return the cumulative distribution function = TRUE; return the probability mass function = FALSE or omitted"}, "GAMMA.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "Returns the gamma distribution", "ad": "is the value at which you want to evaluate the distribution, a nonnegative number!is a parameter to the distribution, a positive number!is a parameter to the distribution, a positive number. If beta = 1, GAMMA.DIST returns the standard gamma distribution!is a logical value: return the cumulative distribution function = TRUE; return the probability mass function = FALSE or omitted"}, "GAMMAINV": {"a": "(probability; alpha; beta)", "d": "Returns the inverse of the gamma cumulative distribution: if p = GAMMADIST(x,...), then GAMMAINV(p,...) = x", "ad": "is the probability associated with the gamma distribution, a number between 0 and 1, inclusive!is a parameter to the distribution, a positive number!is a parameter to the distribution, a positive number. If beta = 1, GAMMAINV returns the inverse of the standard gamma distribution"}, "GAMMA.INV": {"a": "(probability; alpha; beta)", "d": "Returns the inverse of the gamma cumulative distribution: if p = GAMMA.DIST(x,...), then GAMMA.INV(p,...) = x", "ad": "is the probability associated with the gamma distribution, a number between 0 and 1, inclusive!is a parameter to the distribution, a positive number!is a parameter to the distribution, a positive number. If beta = 1, GAMMA.INV returns the inverse of the standard gamma distribution"}, "GAMMALN": {"a": "(x)", "d": "Returns the natural logarithm of the gamma function", "ad": "is the value for which you want to calculate GAMMALN, a positive number"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Returns the natural logarithm of the gamma function", "ad": "is the value for which you want to calculate GAMMALN.PRECISE, a positive number"}, "GAUSS": {"a": "(x)", "d": "Returns 0.5 less than the standard normal cumulative distribution", "ad": "is the value for which you want the distribution"}, "GEOMEAN": {"a": "(number1; [number2]; ...)", "d": "Returns the geometric mean of an array or range of positive numeric data", "ad": "are 1 to 255 numbers or names, arrays, or references that contain numbers for which you want the mean"}, "GROWTH": {"a": "(known_y's; [known_x's]; [new_x's]; [const])", "d": "Returns numbers in an exponential growth trend matching known data points", "ad": "is the set of y-values you already know in the relationship y = b*m^x, an array or range of positive numbers!is an optional set of x-values that you may already know in the relationship y = b*m^x, an array or range the same size as Known_y's!are new x-values for which you want GROWTH to return corresponding y-values!is a logical value: the constant b is calculated normally if Const = TRUE; b is set equal to 1 if Const = FALSE or omitted"}, "HARMEAN": {"a": "(number1; [number2]; ...)", "d": "Returns the harmonic mean of a data set of positive numbers: the reciprocal of the arithmetic mean of reciprocals", "ad": "are 1 to 255 numbers or names, arrays, or references that contain numbers for which you want the harmonic mean"}, "HYPGEOM.DIST": {"a": "(sample_s; number_sample; population_s; number_pop; cumulative)", "d": "Returns the hypergeometric distribution", "ad": "is the number of successes in the sample!is the size of the sample!is the number of successes in the population!is the population size!is a logical value: for the cumulative distribution function, use TRUE; for the probability density function, use FALSE"}, "HYPGEOMDIST": {"a": "(sample_s; number_sample; population_s; number_pop)", "d": "Returns the hypergeometric distribution", "ad": "is the number of successes in the sample!is the size of the sample!is the number of successes in the population!is the population size"}, "INTERCEPT": {"a": "(known_ys; known_xs)", "d": "Calculates the point at which a line will intersect the y-axis by using a best-fit regression line plotted through the known x-values and y-values", "ad": "is the dependent set of observations or data and can be numbers or names, arrays, or references that contain numbers!is the independent set of observations or data and can be numbers or names, arrays, or references that contain numbers"}, "KURT": {"a": "(number1; [number2]; ...)", "d": "Returns the kurtosis of a data set", "ad": "are 1 to 255 numbers or names, arrays, or references that contain numbers for which you want the kurtosis"}, "LARGE": {"a": "(array; k)", "d": "Returns the k-th largest value in a data set. For example, the fifth largest number", "ad": "is the array or range of data for which you want to determine the k-th largest value!is the position (from the largest) in the array or cell range of the value to return"}, "LINEST": {"a": "(known_y's; [known_x's]; [const]; [stats])", "d": "Returns statistics that describe a linear trend matching known data points, by fitting a straight line using the least squares method", "ad": "is the set of y-values you already know in the relationship y = mx + b!is an optional set of x-values that you may already know in the relationship y = mx + b!is a logical value: the constant b is calculated normally if Const = TRUE or omitted; b is set equal to 0 if Const = FALSE!is a logical value: return additional regression statistics = TRUE; return m-coefficients and the constant b = FALSE or omitted"}, "LOGEST": {"a": "(known_y's; [known_x's]; [const]; [stats])", "d": "Returns statistics that describe an exponential curve matching known data points", "ad": "is the set of y-values you already know in the relationship y = b*m^x!is an optional set of x-values that you may already know in the relationship y = b*m^x!is a logical value: the constant b is calculated normally if Const = TRUE or omitted; b is set equal to 1 if Const = FALSE!is a logical value: return additional regression statistics = TRUE; return m-coefficients and the constant b = FALSE or omitted"}, "LOGINV": {"a": "(probability; mean; standard_dev)", "d": "Returns the inverse of the lognormal cumulative distribution function of x, where ln(x) is normally distributed with parameters Mean and Standard_dev", "ad": "is a probability associated with the lognormal distribution, a number between 0 and 1, inclusive!is the mean of ln(x)!is the standard deviation of ln(x), a positive number"}, "LOGNORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Returns the lognormal distribution of x, where ln(x) is normally distributed with parameters Mean and Standard_dev", "ad": "is the value at which to evaluate the function, a positive number!is the mean of ln(x)!is the standard deviation of ln(x), a positive number!is a logical value: for the cumulative distribution function, use TRUE; for the probability density function, use FALSE"}, "LOGNORM.INV": {"a": "(probability; mean; standard_dev)", "d": "Returns the inverse of the lognormal cumulative distribution function of x, where ln(x) is normally distributed with parameters Mean and Standard_dev", "ad": "is a probability associated with the lognormal distribution, a number between 0 and 1, inclusive!is the mean of ln(x)!is the standard deviation of ln(x), a positive number"}, "LOGNORMDIST": {"a": "(x; mean; standard_dev)", "d": "Returns the cumulative lognormal distribution of x, where ln(x) is normally distributed with parameters Mean and Standard_dev", "ad": "is the value at which to evaluate the function, a positive number!is the mean of ln(x)!is the standard deviation of ln(x), a positive number"}, "MAX": {"a": "(number1; [number2]; ...)", "d": "Returns the largest value in a set of values. Ignores logical values and text", "ad": "are 1 to 255 numbers, empty cells, logical values, or text numbers for which you want the maximum"}, "MAXA": {"a": "(value1; [value2]; ...)", "d": "Returns the largest value in a set of values. Does not ignore logical values and text", "ad": "are 1 to 255 numbers, empty cells, logical values, or text numbers for which you want the maximum"}, "MAXIFS": {"a": "(max_range; criteria_range; criteria; ...)", "d": "Returns the maximum value among cells specified by a given set of conditions or criteria", "ad": "the cells in which to determine the maximum value!is the range of cells you want to evaluate for the particular condition!is the condition or criteria in the form of a number, expression, or text that defines which cells will be included when determining the maximum value"}, "MEDIAN": {"a": "(number1; [number2]; ...)", "d": "Returns the median, or the number in the middle of the set of given numbers", "ad": "are 1 to 255 numbers or names, arrays, or references that contain numbers for which you want the median"}, "MIN": {"a": "(number1; [number2]; ...)", "d": "Returns the smallest number in a set of values. Ignores logical values and text", "ad": "are 1 to 255 numbers, empty cells, logical values, or text numbers for which you want the minimum"}, "MINA": {"a": "(value1; [value2]; ...)", "d": "Returns the smallest value in a set of values. Does not ignore logical values and text", "ad": "are 1 to 255 numbers, empty cells, logical values, or text numbers for which you want the minimum"}, "MINIFS": {"a": "(min_range; criteria_range; criteria; ...)", "d": "Returns the minimum value among cells specified by a given set of conditions or criteria", "ad": "the cells in which to determine the minimum value!is the range of cells you want to evaluate for the particular condition!is the condition or criteria in the form of a number, expression, or text that defines which cells will be included when determining the minimum value"}, "MODE": {"a": "(number1; [number2]; ...)", "d": "Returns the most frequently occurring, or repetitive, value in an array or range of data", "ad": "are 1 to 255 numbers, or names, arrays, or references that contain numbers for which you want the mode"}, "MODE.MULT": {"a": "(number1; [number2]; ...)", "d": "Returns a vertical array of the most frequently occurring, or repetitive, values in an array or range of data. For a horizontal array, use =TRANSPOSE(MODE.MULT(number1,number2,...))", "ad": "are 1 to 255 numbers, or names, arrays, or references that contain numbers for which you want the mode"}, "MODE.SNGL": {"a": "(number1; [number2]; ...)", "d": "Returns the most frequently occurring, or repetitive, value in an array or range of data", "ad": "are 1 to 255 numbers, or names, arrays, or references that contain numbers for which you want the mode"}, "NEGBINOM.DIST": {"a": "(number_f; number_s; probability_s; cumulative)", "d": "Returns the negative binomial distribution, the probability that there will be Number_f failures before the Number_s-th success, with Probability_s probability of a success", "ad": "is the number of failures!is the threshold number of successes!is the probability of a success; a number between 0 and 1!is a logical value: for the cumulative distribution function, use TRUE; for the probability mass function, use FALSE"}, "NEGBINOMDIST": {"a": "(number_f; number_s; probability_s)", "d": "Returns the negative binomial distribution, the probability that there will be Number_f failures before the Number_s-th success, with Probability_s probability of a success", "ad": "is the number of failures!is the threshold number of successes!is the probability of a success; a number between 0 and 1"}, "NORM.DIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Returns the normal distribution for the specified mean and standard deviation", "ad": "is the value for which you want the distribution!is the arithmetic mean of the distribution!is the standard deviation of the distribution, a positive number!is a logical value: for the cumulative distribution function, use TRUE; for the probability density function, use FALSE"}, "NORMDIST": {"a": "(x; mean; standard_dev; cumulative)", "d": "Returns the normal cumulative distribution for the specified mean and standard deviation", "ad": "is the value for which you want the distribution!is the arithmetic mean of the distribution!is the standard deviation of the distribution, a positive number!is a logical value: for the cumulative distribution function, use TRUE; for the probability density function, use FALSE"}, "NORM.INV": {"a": "(probability; mean; standard_dev)", "d": "Returns the inverse of the normal cumulative distribution for the specified mean and standard deviation", "ad": "is a probability corresponding to the normal distribution, a number between 0 and 1 inclusive!is the arithmetic mean of the distribution!is the standard deviation of the distribution, a positive number"}, "NORMINV": {"a": "(probability; mean; standard_dev)", "d": "Returns the inverse of the normal cumulative distribution for the specified mean and standard deviation", "ad": "is a probability corresponding to the normal distribution, a number between 0 and 1 inclusive!is the arithmetic mean of the distribution!is the standard deviation of the distribution, a positive number"}, "NORM.S.DIST": {"a": "(z; cumulative)", "d": "Returns the standard normal distribution (has a mean of zero and a standard deviation of one)", "ad": "is the value for which you want the distribution!is a logical value for the function to return: the cumulative distribution function = TRUE; the probability density function = FALSE"}, "NORMSDIST": {"a": "(z)", "d": "Returns the standard normal cumulative distribution (has a mean of zero and a standard deviation of one)", "ad": "is the value for which you want the distribution"}, "NORM.S.INV": {"a": "(probability)", "d": "Returns the inverse of the standard normal cumulative distribution (has a mean of zero and a standard deviation of one)", "ad": "is a probability corresponding to the normal distribution, a number between 0 and 1 inclusive"}, "NORMSINV": {"a": "(probability)", "d": "Returns the inverse of the standard normal cumulative distribution (has a mean of zero and a standard deviation of one)", "ad": "is a probability corresponding to the normal distribution, a number between 0 and 1 inclusive"}, "PEARSON": {"a": "(array1; array2)", "d": "Returns the Pearson product moment correlation coefficient, r", "ad": "is a set of independent values!is a set of dependent values"}, "PERCENTILE": {"a": "(array; k)", "d": "Returns the k-th percentile of values in a range", "ad": "is the array or range of data that defines relative standing!is the percentile value that is between 0 through 1, inclusive"}, "PERCENTILE.EXC": {"a": "(array; k)", "d": "Returns the k-th percentile of values in a range, where k is in the range 0..1, exclusive", "ad": "is the array or range of data that defines relative standing!is the percentile value that is between 0 through 1, inclusive"}, "PERCENTILE.INC": {"a": "(array; k)", "d": "Returns the k-th percentile of values in a range, where k is in the range 0..1, inclusive", "ad": "is the array or range of data that defines relative standing!is the percentile value that is between 0 through 1, inclusive"}, "PERCENTRANK": {"a": "(array; x; [significance])", "d": "Returns the rank of a value in a data set as a percentage of the data set", "ad": "is the array or range of data with numeric values that defines relative standing!is the value for which you want to know the rank!is an optional value that identifies the number of significant digits for the returned percentage, three digits if omitted (0.xxx%)"}, "PERCENTRANK.EXC": {"a": "(array; x; [significance])", "d": "Returns the rank of a value in a data set as a percentage of the data set as a percentage (0..1, exclusive) of the data set", "ad": "is the array or range of data with numeric values that defines relative standing!is the value for which you want to know the rank!is an optional value that identifies the number of significant digits for the returned percentage, three digits if omitted (0.xxx%)"}, "PERCENTRANK.INC": {"a": "(array; x; [significance])", "d": "Returns the rank of a value in a data set as a percentage of the data set as a percentage (0..1, inclusive) of the data set", "ad": "is the array or range of data with numeric values that defines relative standing!is the value for which you want to know the rank!is an optional value that identifies the number of significant digits for the returned percentage, three digits if omitted (0.xxx%)"}, "PERMUT": {"a": "(number; number_chosen)", "d": "Returns the number of permutations for a given number of objects that can be selected from the total objects", "ad": "is the total number of objects!is the number of objects in each permutation"}, "PERMUTATIONA": {"a": "(number; number_chosen)", "d": "Returns the number of permutations for a given number of objects (with repetitions) that can be selected from the total objects", "ad": "is the total number of objects!is the number of objects in each permutation"}, "PHI": {"a": "(x)", "d": "Returns the value of the density function for a standard normal distribution", "ad": "is the number for which you want the density of the standard normal distribution"}, "POISSON": {"a": "(x; mean; cumulative)", "d": "Returns the <PERSON><PERSON><PERSON> distribution", "ad": "is the number of events!is the expected numeric value, a positive number!is a logical value: for the cumulative Poisson probability, use TRUE; for the Poisson probability mass function, use FALSE"}, "POISSON.DIST": {"a": "(x; mean; cumulative)", "d": "Returns the <PERSON><PERSON><PERSON> distribution", "ad": "is the number of events!is the expected numeric value, a positive number!is a logical value: for the cumulative Poisson probability, use TRUE; for the Poisson probability mass function, use FALSE"}, "PROB": {"a": "(x_range; prob_range; lower_limit; [upper_limit])", "d": "Returns the probability that values in a range are between two limits or equal to a lower limit", "ad": "is the range of numeric values of x with which there are associated probabilities!is the set of probabilities associated with values in X_range, values between 0 and 1 and excluding 0!is the lower bound on the value for which you want a probability!is the optional upper bound on the value. If omitted, PROB returns the probability that X_range values are equal to Lower_limit"}, "QUARTILE": {"a": "(array; quart)", "d": "Returns the quartile of a data set", "ad": "is the array or cell range of numeric values for which you want the quartile value!is a number: minimum value = 0; 1st quartile = 1; median value = 2; 3rd quartile = 3; maximum value = 4"}, "QUARTILE.INC": {"a": "(array; quart)", "d": "Returns the quartile of a data set, based on percentile values from 0..1, inclusive", "ad": "is the array or cell range of numeric values for which you want the quartile value!is a number: minimum value = 0; 1st quartile = 1; median value = 2; 3rd quartile = 3; maximum value = 4"}, "QUARTILE.EXC": {"a": "(array; quart)", "d": "Returns the quartile of a data set, based on percentile values from 0..1, exclusive", "ad": "is the array or cell range of numeric values for which you want the quartile value!is a number: minimum value = 0; 1st quartile = 1; median value = 2; 3rd quartile = 3; maximum value = 4"}, "RANK": {"a": "(number; ref; [order])", "d": "Returns the rank of a number in a list of numbers: its size relative to other values in the list", "ad": "is the number for which you want to find the rank!is an array of, or a reference to, a list of numbers. Nonnumeric values are ignored!is a number: rank in the list sorted descending = 0 or omitted; rank in the list sorted ascending = any nonzero value"}, "RANK.AVG": {"a": "(number; ref; [order])", "d": "Returns the rank of a number in a list of numbers: its size relative to other values in the list; if more than one value has the same rank, the average rank is returned", "ad": "is the number for which you want to find the rank!is an array of, or a reference to, a list of numbers. Nonnumeric values are ignored!is a number: rank in the list sorted descending = 0 or omitted; rank in the list sorted ascending = any nonzero value"}, "RANK.EQ": {"a": "(number; ref; [order])", "d": "Returns the rank of a number in a list of numbers: its size relative to other values in the list; if more than one value has the same rank, the top rank of that set of values is returned", "ad": "is the number for which you want to find the rank!is an array of, or a reference to, a list of numbers. Nonnumeric values are ignored!is a number: rank in the list sorted descending = 0 or omitted; rank in the list sorted ascending = any nonzero value"}, "RSQ": {"a": "(known_y's; known_x's)", "d": "Returns the square of the Pearson product moment correlation coefficient through the given data points", "ad": "is an array or range of data points and can be numbers or names, arrays, or references that contain numbers!is an array or range of data points and can be numbers or names, arrays, or references that contain numbers"}, "SKEW": {"a": "(number1; [number2]; ...)", "d": "Returns the skewness of a distribution: a characterisation of the degree of asymmetry of a distribution around its mean", "ad": "are 1 to 255 numbers or names, arrays, or references that contain numbers for which you want the skewness"}, "SKEW.P": {"a": "(number1; [number2]; ...)", "d": "Returns the skewness of a distribution based on a population: a characterisation of the degree of asymmetry of a distribution around its mean", "ad": "are 1 to 254 numbers or names, arrays, or references that contain numbers for which you want the population skewness"}, "SLOPE": {"a": "(known_ys; known_xs)", "d": "Returns the slope of the linear regression line through the given data points", "ad": "is an array or cell range of numeric dependent data points and can be numbers or names, arrays, or references that contain numbers!is the set of independent data points and can be numbers or names, arrays, or references that contain numbers"}, "SMALL": {"a": "(array; k)", "d": "Returns the k-th smallest value in a data set. For example, the fifth smallest number", "ad": "is an array or range of numerical data for which you want to determine the k-th smallest value!is the position (from the smallest) in the array or range of the value to return"}, "STANDARDIZE": {"a": "(x; mean; standard_dev)", "d": "Returns a normalised value from a distribution characterised by a mean and standard deviation", "ad": "is the value you want to normalize!is the arithmetic mean of the distribution!is the standard deviation of the distribution, a positive number"}, "STDEV": {"a": "(number1; [number2]; ...)", "d": "Estimates standard deviation based on a sample (ignores logical values and text in the sample)", "ad": "are 1 to 255 numbers corresponding to a sample of a population and can be numbers or references that contain numbers"}, "STDEV.P": {"a": "(number1; [number2]; ...)", "d": "Calculates standard deviation based on the entire population given as arguments (ignores logical values and text)", "ad": "are 1 to 255 numbers corresponding to a population and can be numbers or references that contain numbers"}, "STDEV.S": {"a": "(number1; [number2]; ...)", "d": "Estimates standard deviation based on a sample (ignores logical values and text in the sample)", "ad": "are 1 to 255 numbers corresponding to a sample of a population and can be numbers or references that contain numbers"}, "STDEVA": {"a": "(value1; [value2]; ...)", "d": "Estimates standard deviation based on a sample, including logical values and text. Text and the logical value FALSE have the value 0; the logical value TRUE has the value 1", "ad": "are 1 to 255 values corresponding to a sample of a population and can be values or names or references to values"}, "STDEVP": {"a": "(number1; [number2]; ...)", "d": "Calculates standard deviation based on the entire population given as arguments (ignores logical values and text)", "ad": "are 1 to 255 numbers corresponding to a population and can be numbers or references that contain numbers"}, "STDEVPA": {"a": "(value1; [value2]; ...)", "d": "Calculates standard deviation based on an entire population, including logical values and text. Text and the logical value FALSE have the value 0; the logical value TRUE has the value 1", "ad": "are 1 to 255 values corresponding to a population and can be values, names, arrays, or references that contain values"}, "STEYX": {"a": "(known_y's; known_x's)", "d": "Returns the standard error of the predicted y-value for each x in a regression", "ad": "is an array or range of dependent data points and can be numbers or names, arrays, or references that contain numbers!is an array or range of independent data points and can be numbers or names, arrays, or references that contain numbers"}, "TDIST": {"a": "(x; deg_freedom; tails)", "d": "Returns the Student's t-distribution", "ad": "is the numeric value at which to evaluate the distribution!is an integer indicating the number of degrees of freedom that characterize the distribution!specifies the number of distribution tails to return: one-tailed distribution = 1; two-tailed distribution = 2"}, "TINV": {"a": "(probability; deg_freedom)", "d": "Returns the two-tailed inverse of the Student's t-distribution", "ad": "is the probability associated with the two-tailed Student's t-distribution, a number between 0 and 1 inclusive!is a positive integer indicating the number of degrees of freedom to characterize the distribution"}, "T.DIST": {"a": "(x; deg_freedom; cumulative)", "d": "Returns the left-tailed Student's t-distribution", "ad": "is the numeric value at which to evaluate the distribution!is an integer indicating the number of degrees of freedom that characterize the distribution!is a logical value: for the cumulative distribution function, use TRUE; for the probability density function, use FALSE"}, "T.DIST.2T": {"a": "(x; deg_freedom)", "d": "Returns the two-tailed Student's t-distribution", "ad": "is the numeric value at which to evaluate the distribution!is an integer indicating the number of degrees of freedom that characterize the distribution"}, "T.DIST.RT": {"a": "(x; deg_freedom)", "d": "Returns the right-tailed Student's t-distribution", "ad": "is the numeric value at which to evaluate the distribution!is an integer indicating the number of degrees of freedom that characterize the distribution"}, "T.INV": {"a": "(probability; deg_freedom)", "d": "Returns the left-tailed inverse of the Student's t-distribution", "ad": "is the probability associated with the two-tailed Student's t-distribution, a number between 0 and 1 inclusive!is a positive integer indicating the number of degrees of freedom to characterize the distribution"}, "T.INV.2T": {"a": "(probability; deg_freedom)", "d": "Returns the two-tailed inverse of the Student's t-distribution", "ad": "is the probability associated with the two-tailed Student's t-distribution, a number between 0 and 1 inclusive!is a positive integer indicating the number of degrees of freedom to characterize the distribution"}, "T.TEST": {"a": "(array1; array2; tails; type)", "d": "Returns the probability associated with a Student's t-Test", "ad": "is the first data set!is the second data set!specifies the number of distribution tails to return: one-tailed distribution = 1; two-tailed distribution = 2!is the kind of t-test: paired = 1, two-sample equal variance (homoscedastic) = 2, two-sample unequal variance = 3"}, "TREND": {"a": "(known_y's; [known_x's]; [new_x's]; [const])", "d": "Returns numbers in a linear trend matching known data points, using the least squares method", "ad": "is a range or array of y-values you already know in the relationship y = mx + b!is an optional range or array of x-values that you know in the relationship y = mx + b, an array the same size as Known_y's!is a range or array of new x-values for which you want TREND to return corresponding y-values!is a logical value: the constant b is calculated normally if Const = TRUE or omitted; b is set equal to 0 if Const = FALSE"}, "TRIMMEAN": {"a": "(array; percent)", "d": "Returns the mean of the interior portion of a set of data values", "ad": "is the range or array of values to trim and average!is the fractional number of data points to exclude from the top and bottom of the data set"}, "TTEST": {"a": "(array1; array2; tails; type)", "d": "Returns the probability associated with a Student's t-Test", "ad": "is the first data set!is the second data set!specifies the number of distribution tails to return: one-tailed distribution = 1; two-tailed distribution = 2!is the kind of t-test: paired = 1, two-sample equal variance (homoscedastic) = 2, two-sample unequal variance = 3"}, "VAR": {"a": "(number1; [number2]; ...)", "d": "Estimates variance based on a sample (ignores logical values and text in the sample)", "ad": "are 1 to 255 numeric arguments corresponding to a sample of a population"}, "VAR.P": {"a": "(number1; [number2]; ...)", "d": "Calculates variance based on the entire population (ignores logical values and text in the population)", "ad": "are 1 to 255 numeric arguments corresponding to a population"}, "VAR.S": {"a": "(number1; [number2]; ...)", "d": "Estimates variance based on a sample (ignores logical values and text in the sample)", "ad": "are 1 to 255 numeric arguments corresponding to a sample of a population"}, "VARA": {"a": "(value1; [value2]; ...)", "d": "Estimates variance based on a sample, including logical values and text. Text and the logical value FALSE have the value 0; the logical value TRUE has the value 1", "ad": "are 1 to 255 value arguments corresponding to a sample of a population"}, "VARP": {"a": "(number1; [number2]; ...)", "d": "Calculates variance based on the entire population (ignores logical values and text in the population)", "ad": "are 1 to 255 numeric arguments corresponding to a population"}, "VARPA": {"a": "(value1; [value2]; ...)", "d": "Calculates variance based on the entire population, including logical values and text. Text and the logical value FALSE have the value 0; the logical value TRUE has the value 1", "ad": "are 1 to 255 value arguments corresponding to a population"}, "WEIBULL": {"a": "(x; alpha; beta; cumulative)", "d": "Returns the <PERSON><PERSON> distribution", "ad": "is the value at which to evaluate the function, a nonnegative number!is a parameter to the distribution, a positive number!is a parameter to the distribution, a positive number!is a logical value: for the cumulative distribution function, use TRUE; for the probability mass function, use FALSE"}, "WEIBULL.DIST": {"a": "(x; alpha; beta; cumulative)", "d": "Returns the <PERSON><PERSON> distribution", "ad": "is the value at which to evaluate the function, a nonnegative number!is a parameter to the distribution, a positive number!is a parameter to the distribution, a positive number!is a logical value: for the cumulative distribution function, use TRUE; for the probability mass function, use FALSE"}, "Z.TEST": {"a": "(array; x; [sigma])", "d": "Returns the one-tailed P-value of a z-test", "ad": "is the array or range of data against which to test X!is the value to test!is the population (known) standard deviation. If omitted, the sample standard deviation is used"}, "ZTEST": {"a": "(array; x; [sigma])", "d": "Returns the one-tailed P-value of a z-test", "ad": "is the array or range of data against which to test X!is the value to test!is the population (known) standard deviation. If omitted, the sample standard deviation is used"}, "ACCRINT": {"a": "(issue; first_interest; settlement; rate; par; frequency; [basis]; [calc_method])", "d": "Returns the accrued interest for a security that pays periodic interest.", "ad": "is the security's issue date, expressed as a serial date number!is the security's first interest date, expressed as a serial date number!is the security's settlement date, expressed as a serial date number!is the security's annual coupon rate!is the security's par value!is the number of coupon payments per year!is the type of day count basis to use!is a logical value: to accrued interest from issue date = TRUE or omitted; to calculate from last coupon payment date = FALSE"}, "ACCRINTM": {"a": "(issue; settlement; rate; par; [basis])", "d": "Returns the accrued interest for a security that pays interest at maturity", "ad": "is the security's issue date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the security's annual coupon rate!is the security's par value!is the type of day count basis to use"}, "AMORDEGRC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Returns the prorated linear depreciation of an asset for each accounting period.", "ad": "is the cost of the asset!is the date the asset is purchased!is the date of the end of the first period!is the salvage value at the end of life of the asset.!is the period!is the rate of depreciation!year_basis : 0 for year of 360 days, 1 for actual, 3 for year of 365 days."}, "AMORLINC": {"a": "(cost; date_purchased; first_period; salvage; period; rate; [basis])", "d": "Returns the prorated linear depreciation of an asset for each accounting period.", "ad": "is the cost of the asset!is the date the asset is purchased!is the date of the end of the first period!is the salvage value at the end of life of the asset.!is the period!is the rate of depreciation!year_basis : 0 for year of 360 days, 1 for actual, 3 for year of 365 days."}, "COUPDAYBS": {"a": "(settlement; maturity; frequency; [basis])", "d": "Returns the number of days from the beginning of the coupon period to the settlement date", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the number of coupon payments per year!is the type of day count basis to use"}, "COUPDAYS": {"a": "(settlement; maturity; frequency; [basis])", "d": "Returns the number of days in the coupon period that contains the settlement date", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the number of coupon payments per year!is the type of day count basis to use"}, "COUPDAYSNC": {"a": "(settlement; maturity; frequency; [basis])", "d": "Returns the number of days from the settlement date to the next coupon date", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the number of coupon payments per year!is the type of day count basis to use"}, "COUPNCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "Returns the next coupon date after the settlement date", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the number of coupon payments per year!is the type of day count basis to use"}, "COUPNUM": {"a": "(settlement; maturity; frequency; [basis])", "d": "Returns the number of coupons payable between the settlement date and maturity date", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the number of coupon payments per year!is the type of day count basis to use"}, "COUPPCD": {"a": "(settlement; maturity; frequency; [basis])", "d": "Returns the previous coupon date before the settlement date", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the number of coupon payments per year!is the type of day count basis to use"}, "CUMIPMT": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Returns the cumulative interest paid between two periods", "ad": "is the interest rate!is the total number of payment periods!is the present value!is the first period in the calculation!is the last period in the calculation!is the timing of the payment"}, "CUMPRINC": {"a": "(rate; nper; pv; start_period; end_period; type)", "d": "Returns the cumulative principal paid on a loan between two periods", "ad": "is the interest rate!is the total number of payment periods!is the present value!is the first period in the calculation!is the last period in the calculation!is the timing of the payment"}, "DB": {"a": "(cost; salvage; life; period; [month])", "d": "Returns the depreciation of an asset for a specified period using the fixed-declining balance method", "ad": "is the initial cost of the asset!is the salvage value at the end of the life of the asset!is the number of periods over which the asset is being depreciated (sometimes called the useful life of the asset)!is the period for which you want to calculate the depreciation. Period must use the same units as Life!is the number of months in the first year. If month is omitted, it is assumed to be 12"}, "DDB": {"a": "(cost; salvage; life; period; [factor])", "d": "Returns the depreciation of an asset for a specified period using the double-declining balance method or some other method you specify", "ad": "is the initial cost of the asset!is the salvage value at the end of the life of the asset!is the number of periods over which the asset is being depreciated (sometimes called the useful life of the asset)!is the period for which you want to calculate the depreciation. Period must use the same units as Life!is the rate at which the balance declines. If Factor is omitted, it is assumed to be 2 (the double-declining balance method)"}, "DISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "Returns the discount rate for a security", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the security's price per $100 face value!is the security's redemption value per $100 face value!is the type of day count basis to use"}, "DOLLARDE": {"a": "(fractional_dollar; fraction)", "d": "Converts a dollar price, expressed as a fraction, into a dollar price, expressed as a decimal number", "ad": "is a number expressed as a fraction!is the integer to use in the denominator of the fraction"}, "DOLLARFR": {"a": "(decimal_dollar; fraction)", "d": "Converts a dollar price, expressed as a decimal number, into a dollar price, expressed as a fraction", "ad": "is a decimal number!is the integer to use in the denominator of a fraction"}, "DURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "Returns the annual duration of a security with periodic interest payments", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the security's annual coupon rate!is the security's annual yield!is the number of coupon payments per year!is the type of day count basis to use"}, "EFFECT": {"a": "(nominal_rate; npery)", "d": "Returns the effective annual interest rate", "ad": "is the nominal interest rate!is the number of compounding periods per year"}, "FV": {"a": "(rate; nper; pmt; [pv]; [type])", "d": "Returns the future value of an investment based on periodic, constant payments and a constant interest rate", "ad": "is the interest rate per period. For example, use 6%/4 for quarterly payments at 6% APR!is the total number of payment periods in the investment!is the payment made each period; it cannot change over the life of the investment!is the present value, or the lump-sum amount that a series of future payments is worth now. If omitted, Pv = 0!is a value representing the timing of payment: payment at the beginning of the period = 1; payment at the end of the period = 0 or omitted"}, "FVSCHEDULE": {"a": "(principal; schedule)", "d": "Returns the future value of an initial principal after applying a series of compound interest rates", "ad": "is the present value!is an array of interest rates to apply"}, "INTRATE": {"a": "(settlement; maturity; investment; redemption; [basis])", "d": "Returns the interest rate for a fully invested security", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the amount invested in the security!is the amount to be received at maturity!is the type of day count basis to use"}, "IPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Returns the interest payment for a given period for an investment, based on periodic, constant payments and a constant interest rate", "ad": "is the interest rate per period. For example, use 6%/4 for quarterly payments at 6% APR!is the period for which you want to find the interest and must be in the range 1 to Nper!is the total number of payment periods in an investment!is the present value, or the lump-sum amount that a series of future payments is worth now!is the future value, or a cash balance you want to attain after the last payment is made. If omitted, Fv = 0!is a logical value representing the timing of payment: at the end of the period = 0 or omitted, at the beginning of the period = 1"}, "IRR": {"a": "(values; [guess])", "d": "Returns the internal rate of return for a series of cash flows", "ad": "is an array or a reference to cells that contain numbers for which you want to calculate the internal rate of return!is a number that you guess is close to the result of IRR; 0.1 (10 percent) if omitted"}, "ISPMT": {"a": "(rate; per; nper; pv)", "d": "Returns the interest paid during a specific period of an investment", "ad": "interest rate per period. For example, use 6%/4 for quarterly payments at 6% APR!period for which you want to find the interest!number of payment periods in an investment!lump sum amount that a series of future payments is right now"}, "MDURATION": {"a": "(settlement; maturity; coupon; yld; frequency; [basis])", "d": "Returns the <PERSON>ley modified duration for a security with an assumed par value of $100", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the security's annual coupon rate!is the security's annual yield!is the number of coupon payments per year!is the type of day count basis to use"}, "MIRR": {"a": "(values; finance_rate; reinvest_rate)", "d": "Returns the internal rate of return for a series of periodic cash flows, considering both cost of investment and interest on reinvestment of cash", "ad": "is an array or a reference to cells that contain numbers that represent a series of payments (negative) and income (positive) at regular periods!is the interest rate you pay on the money used in the cash flows!is the interest rate you receive on the cash flows as you reinvest them"}, "NOMINAL": {"a": "(effect_rate; npery)", "d": "Returns the annual nominal interest rate", "ad": "is the effective interest rate!is the number of compounding periods per year"}, "NPER": {"a": "(rate; pmt; pv; [fv]; [type])", "d": "Returns the number of periods for an investment based on periodic, constant payments and a constant interest rate", "ad": "is the interest rate per period. For example, use 6%/4 for quarterly payments at 6% APR!is the payment made each period; it cannot change over the life of the investment!is the present value, or the lump-sum amount that a series of future payments is worth now!is the future value, or a cash balance you want to attain after the last payment is made. If omitted, zero is used!is a logical value: payment at the beginning of the period = 1; payment at the end of the period = 0 or omitted"}, "NPV": {"a": "(rate; value1; [value2]; ...)", "d": "Returns the net present value of an investment based on a discount rate and a series of future payments (negative values) and income (positive values)", "ad": "is the rate of discount over the length of one period!are 1 to 254 payments and income, equally spaced in time and occurring at the end of each period"}, "ODDFPRICE": {"a": "(settlement; maturity; issue; first_coupon; rate; yld; redemption; frequency; [basis])", "d": "Returns the price per $100 face value of a security with an odd first period", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the security's issue date, expressed as a serial date number!is the security's first coupon date, expressed as a serial date number!is the security's interest rate!is the security's annual yield!is the security's redemption value per $100 face value!is the number of coupon payments per year!is the type of day count basis to use"}, "ODDFYIELD": {"a": "(settlement; maturity; issue; first_coupon; rate; pr; redemption; frequency; [basis])", "d": "Returns the yield of a security with an odd first period", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the security's issue date, expressed as a serial date number!is the security's first coupon date, expressed as a serial date number!is the security's interest rate!is the security's price!is the security's redemption value per $100 face value!is the number of coupon payments per year!is the type of day count basis to use"}, "ODDLPRICE": {"a": "(settlement; maturity; last_interest; rate; yld; redemption; frequency; [basis])", "d": "Returns the price per $100 face value of a security with an odd last period", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the security's last coupon date, expressed as a serial date number!is the security's interest rate!is the security's annual yield!is the security's redemption value per $100 face value!is the number of coupon payments per year!is the type of day count basis to use"}, "ODDLYIELD": {"a": "(settlement; maturity; last_interest; rate; pr; redemption; frequency; [basis])", "d": "Returns the yield of a security with an odd last period", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the security's last coupon date, expressed as a serial date number!is the security's interest rate!is the security's price!is the security's redemption value per $100 face value!is the number of coupon payments per year!is the type of day count basis to use"}, "PDURATION": {"a": "(rate; pv; fv)", "d": "Returns the number of periods required by an investment to reach a specified value", "ad": "is the interest rate per period.!is the present value of the investment!is the desired future value of the investment"}, "PMT": {"a": "(rate; nper; pv; [fv]; [type])", "d": "Calculates the payment for a loan based on constant payments and a constant interest rate", "ad": "is the interest rate per period for the loan. For example, use 6%/4 for quarterly payments at 6% APR!is the total number of payments for the loan!is the present value: the total amount that a series of future payments is worth now!is the future value, or a cash balance you want to attain after the last payment is made, 0 (zero) if omitted!is a logical value: payment at the beginning of the period = 1; payment at the end of the period = 0 or omitted"}, "PPMT": {"a": "(rate; per; nper; pv; [fv]; [type])", "d": "Returns the payment on the principal for a given investment based on periodic, constant payments and a constant interest rate", "ad": "is the interest rate per period. For example, use 6%/4 for quarterly payments at 6% APR!specifies the period and must be in the range 1 to nper!is the total number of payment periods in an investment!is the present value: the total amount that a series of future payments is worth now!is the future value, or cash balance you want to attain after the last payment is made!is a logical value: payment at the beginning of the period = 1; payment at the end of the period = 0 or omitted"}, "PRICE": {"a": "(settlement; maturity; rate; yld; redemption; frequency; [basis])", "d": "Returns the price per $100 face value of a security that pays periodic interest", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the security's annual coupon rate!is the security's annual yield!is the security's redemption value per $100 face value!is the number of coupon payments per year!is the type of day count basis to use"}, "PRICEDISC": {"a": "(settlement; maturity; discount; redemption; [basis])", "d": "Returns the price per $100 face value of a discounted security", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the security's discount rate!is the security's redemption value per $100 face value!is the type of day count basis to use"}, "PRICEMAT": {"a": "(settlement; maturity; issue; rate; yld; [basis])", "d": "Returns the price per $100 face value of a security that pays interest at maturity", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the security's issue date, expressed as a serial date number!is the security's interest rate at date of issue!is the security's annual yield!is the type of day count basis to use"}, "PV": {"a": "(rate; nper; pmt; [fv]; [type])", "d": "Returns the present value of an investment: the total amount that a series of future payments is worth now", "ad": "is the interest rate per period. For example, use 6%/4 for quarterly payments at 6% APR!is the total number of payment periods in an investment!is the payment made each period and cannot change over the life of the investment!is the future value, or a cash balance you want to attain after the last payment is made!is a logical value: payment at the beginning of the period = 1; payment at the end of the period = 0 or omitted"}, "RATE": {"a": "(nper; pmt; pv; [fv]; [type]; [guess])", "d": "Returns the interest rate per period of a loan or an investment. For example, use 6%/4 for quarterly payments at 6% APR", "ad": "is the total number of payment periods for the loan or investment!is the payment made each period and cannot change over the life of the loan or investment!is the present value: the total amount that a series of future payments is worth now!is the future value, or a cash balance you want to attain after the last payment is made. If omitted, uses Fv = 0!is a logical value: payment at the beginning of the period = 1; payment at the end of the period = 0 or omitted!is your guess for what the rate will be; if omitted, Guess = 0.1 (10 percent)"}, "RECEIVED": {"a": "(settlement; maturity; investment; discount; [basis])", "d": "Returns the amount received at maturity for a fully invested security", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the amount invested in the security!is the security's discount rate!is the type of day count basis to use"}, "RRI": {"a": "(nper; pv; fv)", "d": "Returns an equivalent interest rate for the growth of an investment", "ad": "is the number of periods for the investment!is the present value of the investment!is the future value of the investment"}, "SLN": {"a": "(cost; salvage; life)", "d": "Returns the straight-line depreciation of an asset for one period", "ad": "is the initial cost of the asset!is the salvage value at the end of the life of the asset!is the number of periods over which the asset is being depreciated (sometimes called the useful life of the asset)"}, "SYD": {"a": "(cost; salvage; life; per)", "d": "Returns the sum-of-years' digits depreciation of an asset for a specified period", "ad": "is the initial cost of the asset!is the salvage value at the end of the life of the asset!is the number of periods over which the asset is being depreciated (sometimes called the useful life of the asset)!is the period and must use the same units as Life"}, "TBILLEQ": {"a": "(settlement; maturity; discount)", "d": "Returns the bond-equivalent yield for a treasury bill", "ad": "is the Treasury bill's settlement date, expressed as a serial date number!is the Treasury bill's maturity date, expressed as a serial date number!is the Treasury bill's discount rate"}, "TBILLPRICE": {"a": "(settlement; maturity; discount)", "d": "Returns the price per $100 face value for a treasury bill", "ad": "is the Treasury bill's settlement date, expressed as a serial date number!is the Treasury bill's maturity date, expressed as a serial date number!is the Treasury bill's discount rate"}, "TBILLYIELD": {"a": "(settlement; maturity; pr)", "d": "Returns the yield for a treasury bill", "ad": "is the Treasury bill's settlement date, expressed as a serial date number!is the Treasury bill's maturity date, expressed as a serial date number!is the Treasury Bill's price per $100 face value"}, "VDB": {"a": "(cost; salvage; life; start_period; end_period; [factor]; [no_switch])", "d": "Returns the depreciation of an asset for any period you specify, including partial periods, using the double-declining balance method or some other method you specify", "ad": "is the initial cost of the asset!is the salvage value at the end of the life of the asset!is the number of periods over which the asset is being depreciated (sometimes called the useful life of the asset)!is the starting period for which you want to calculate the depreciation, in the same units as Life!is the ending period for which you want to calculate the depreciation, in the same units as Life!is the rate at which the balance declines, 2 (double-declining balance) if omitted!switch to straight-line depreciation when depreciation is greater than the declining balance = FALSE or omitted; do not switch = TRUE"}, "XIRR": {"a": "(values; dates; [guess])", "d": "Returns the internal rate of return for a schedule of cash flows", "ad": "is a series of cash flows that correspond to a schedule of payments in dates!is a schedule of payment dates that corresponds to the cash flow payments!is a number that you guess is close to the result of XIRR"}, "XNPV": {"a": "(rate; values; dates)", "d": "Returns the net present value for a schedule of cash flows", "ad": "is the discount rate to apply to the cash flows!is a series of cash flows that correspond to a schedule of payments in dates!is a schedule of payment dates that corresponds to the cash flow payments"}, "YIELD": {"a": "(settlement; maturity; rate; pr; redemption; frequency; [basis])", "d": "Returns the yield on a security that pays periodic interest", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the security's annual coupon rate!is the security's price per $100 face value!is the security's redemption value per $100 face value!is the number of coupon payments per year!is the type of day count basis to use"}, "YIELDDISC": {"a": "(settlement; maturity; pr; redemption; [basis])", "d": "Returns the annual yield for a discounted security. For example, a treasury bill", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the security's price per $100 face value!is the security's redemption value per $100 face value!is the type of day count basis to use"}, "YIELDMAT": {"a": "(settlement; maturity; issue; rate; pr; [basis])", "d": "Returns the annual yield of a security that pays interest at maturity", "ad": "is the security's settlement date, expressed as a serial date number!is the security's maturity date, expressed as a serial date number!is the security's issue date, expressed as a serial date number!is the security's interest rate at date of issue!is the security's price per $100 face value!is the type of day count basis to use"}, "ABS": {"a": "(number)", "d": "Returns the absolute value of a number, a number without its sign", "ad": "is the real number for which you want the absolute value"}, "ACOS": {"a": "(number)", "d": "Returns the arccosine of a number, in radians in the range 0 to Pi. The arccosine is the angle whose cosine is Number", "ad": "is the cosine of the angle you want and must be from -1 to 1"}, "ACOSH": {"a": "(number)", "d": "Returns the inverse hyperbolic cosine of a number", "ad": "is any real number equal to or greater than 1"}, "ACOT": {"a": "(number)", "d": "Returns the arccotangent of a number, in radians in the range 0 to Pi.", "ad": "is the cotangent of the angle you want"}, "ACOTH": {"a": "(number)", "d": "Returns the inverse hyperbolic cotangent of a number", "ad": "is the hyperbolic cotangent of the angle that you want"}, "AGGREGATE": {"a": "(function_num; options; ref1; ...)", "d": "Returns an aggregate in a list or database", "ad": "is the number 1 to 19 that specifies the summary function for the aggregate.!is the number 0 to 7 that specifies the values to ignore for the aggregate!is the array or range of numerical data on which to calculate the aggregate!indicates the position in the array; it is k-th largest, k-th smallest, k-th percentile, or k-th quartile.!is the number 1 to 19 that specifies the summary function for the aggregate.!is the number 0 to 7 that specifies the values to ignore for the aggregate!are 1 to 253 ranges or references for which you want the aggregate"}, "ARABIC": {"a": "(text)", "d": "Converts a Roman numeral to Arabic", "ad": "is the Roman numeral you want to convert"}, "ASC": {"a": "( text )", "d": "For Double-byte character set (DBCS) languages, the function changes full-width (double-byte) characters to half-width (single-byte) characters", "ad": "is the text you want to change. If text does not contain any full-width letters, text is not changed."}, "ASIN": {"a": "(number)", "d": "Returns the arcsine of a number in radians, in the range -Pi/2 to Pi/2", "ad": "is the sine of the angle you want and must be from -1 to 1"}, "ASINH": {"a": "(number)", "d": "Returns the inverse hyperbolic sine of a number", "ad": "is any real number equal to or greater than 1"}, "ATAN": {"a": "(number)", "d": "Returns the arctangent of a number in radians, in the range -Pi/2 to Pi/2", "ad": "is the tangent of the angle you want"}, "ATAN2": {"a": "(x_num; y_num)", "d": "Returns the arctangent of the specified x and y coordinates, in radians between -Pi and Pi, excluding -Pi", "ad": "is the x-coordinate of the point!is the y-coordinate of the point"}, "ATANH": {"a": "(number)", "d": "Returns the inverse hyperbolic tangent of a number", "ad": "is any real number between -1 and 1 excluding -1 and 1"}, "BASE": {"a": "(number; radix; [min_length])", "d": "Converts a number into a text representation with the given radix (base)", "ad": "is the number that you want to convert!is the base Radix that you want to convert the number into!is the minimum length of the returned string.  If omitted leading zeros are not added"}, "CEILING": {"a": "(number; significance)", "d": "Rounds a number up, to the nearest multiple of significance", "ad": "is the value you want to round!is the multiple to which you want to round"}, "CEILING.MATH": {"a": "(number; [significance]; [mode])", "d": "Rounds a number up, to the nearest integer or to the nearest multiple of significance", "ad": "is the value you want to round!is the multiple to which you want to round!when given and nonzero this function will round away from zero"}, "CEILING.PRECISE": {"a": "( x; [significance] )", "d": "Returns a number that is rounded up to the nearest integer or to the nearest multiple of significance", "ad": "is the value you want to round!is the multiple to which you want to round"}, "COMBIN": {"a": "(number; number_chosen)", "d": "Returns the number of combinations for a given number of items", "ad": "is the total number of items!is the number of items in each combination"}, "COMBINA": {"a": "(number; number_chosen)", "d": "Returns the number of combinations with repetitions for a given number of items", "ad": "is the total number of items!is the number of items in each combination"}, "COS": {"a": "(number)", "d": "Returns the cosine of an angle", "ad": "is the angle in radians for which you want the cosine"}, "COSH": {"a": "(number)", "d": "Returns the hyperbolic cosine of a number", "ad": "is any real number"}, "COT": {"a": "(number)", "d": "Returns the cotangent of an angle", "ad": "is the angle in radians for which you want the cotangent"}, "COTH": {"a": "(number)", "d": "Returns the hyperbolic cotangent of a number", "ad": "is the angle in radians for which you want the hyperbolic cotangent"}, "CSC": {"a": "(number)", "d": "Returns the cosecant of an angle", "ad": "is the angle in radians for which you want the cosecant"}, "CSCH": {"a": "(number)", "d": "Returns the hyperbolic cosecant of an angle", "ad": "is the angle in radians for which you want the hyperbolic cosecant"}, "DECIMAL": {"a": "(number; radix)", "d": "Converts a text representation of a number in a given base into a decimal number", "ad": "is the number that you want to convert!is the base Radix of the number you are converting"}, "DEGREES": {"a": "(angle)", "d": "Converts radians to degrees", "ad": "is the angle in radians that you want to convert"}, "ECMA.CEILING": {"a": "( x; significance )", "d": "Rounds the number up to the nearest multiple of significance", "ad": "is the value you want to round!is the multiple to which you want to round"}, "EVEN": {"a": "(number)", "d": "Rounds a positive number up and negative number down to the nearest even integer", "ad": "is the value to round"}, "EXP": {"a": "(number)", "d": "Returns e raised to the power of a given number", "ad": "is the exponent applied to the base e. The constant e equals 2.71828182845904, the base of the natural logarithm"}, "FACT": {"a": "(number)", "d": "Returns the factorial of a number, equal to 1*2*3*...* Number", "ad": "is the nonnegative number you want the factorial of"}, "FACTDOUBLE": {"a": "(number)", "d": "Returns the double factorial of a number", "ad": "is the value for which to return the double factorial"}, "FLOOR": {"a": "(number; significance)", "d": "Rounds a number down to the nearest multiple of significance", "ad": "is the numeric value you want to round!is the multiple to which you want to round. Number and Significance must either both be positive or both be negative"}, "FLOOR.PRECISE": {"a": "( x; [significance] )", "d": "Returns a number that is rounded down to the nearest integer or to the nearest multiple of significance", "ad": "is the value you want to round!is the multiple to which you want to round"}, "FLOOR.MATH": {"a": "(number; [significance]; [mode])", "d": "Rounds a number down, to the nearest integer or to the nearest multiple of significance", "ad": "is the value you want to round!is the multiple to which you want to round!when given and nonzero this function will round towards zero"}, "GCD": {"a": "(number1; [number2]; ...)", "d": "Returns the greatest common divisor", "ad": "are 1 to 255 values"}, "INT": {"a": "(number)", "d": "Rounds a number down to the nearest integer", "ad": "is the real number you want to round down to an integer"}, "ISO.CEILING": {"a": "( number; [significance] )", "d": "Returns a number that is rounded up to the nearest integer or to the nearest multiple of significance regardless of the sign of the number. However, if the number or the significance is zero, zero is returned.", "ad": "is the value you want to round!is the multiple to which you want to round"}, "LCM": {"a": "(number1; [number2]; ...)", "d": "Returns the least common multiple", "ad": "are 1 to 255 values for which you want the least common multiple"}, "LN": {"a": "(number)", "d": "Returns the natural logarithm of a number", "ad": "is the positive real number for which you want the natural logarithm"}, "LOG": {"a": "(number; [base])", "d": "Returns the logarithm of a number to the base you specify", "ad": "is the positive real number for which you want the logarithm!is the base of the logarithm; 10 if omitted"}, "LOG10": {"a": "(number)", "d": "Returns the base-10 logarithm of a number", "ad": "is the positive real number for which you want the base-10 logarithm"}, "MDETERM": {"a": "(array)", "d": "Returns the matrix determinant of an array", "ad": "is a numeric array with an equal number of rows and columns, either a cell range or an array constant"}, "MINVERSE": {"a": "(array)", "d": "Returns the inverse matrix for the matrix stored in an array", "ad": "is a numeric array with an equal number of rows and columns, either a cell range or an array constant"}, "MMULT": {"a": "(array1; array2)", "d": "Returns the matrix product of two arrays, an array with the same number of rows as array1 and columns as array2", "ad": "is the first array of numbers to multiply and must have the same number of columns as Array2 has rows"}, "MOD": {"a": "(number; divisor)", "d": "Returns the remainder after a number is divided by a divisor", "ad": "is the number for which you want to find the remainder after the division is performed!is the number by which you want to divide Number"}, "MROUND": {"a": "(number; multiple)", "d": "Returns a number rounded to the desired multiple", "ad": "is the value to round!is the multiple to which you want to round number"}, "MULTINOMIAL": {"a": "(number1; [number2]; ...)", "d": "Returns the multinomial of a set of numbers", "ad": "are 1 to 255 values for which you want the multinomial"}, "MUNIT": {"a": "(dimension)", "d": "Returns the unit matrix for the specified dimension", "ad": "is an integer specifying the dimension of the unit matrix that you want to return"}, "ODD": {"a": "(number)", "d": "Rounds a positive number up and negative number down to the nearest odd integer", "ad": "is the value to round"}, "PI": {"a": "()", "d": "Returns the value of Pi, 3.14159265358979, accurate to 15 digits", "ad": ""}, "POWER": {"a": "(number; power)", "d": "Returns the result of a number raised to a power", "ad": "is the base number, any real number!is the exponent, to which the base number is raised"}, "PRODUCT": {"a": "(number1; [number2]; ...)", "d": "Multiplies all the numbers given as arguments", "ad": "are 1 to 255 numbers, logical values, or text representations of numbers that you want to multiply"}, "QUOTIENT": {"a": "(numerator; denominator)", "d": "Returns the integer portion of a division", "ad": "is the dividend!is the divisor"}, "RADIANS": {"a": "(angle)", "d": "Converts degrees to radians", "ad": "is an angle in degrees that you want to convert"}, "RAND": {"a": "()", "d": "Returns a random number greater than or equal to 0 and less than 1, evenly distributed (changes on recalculation)", "ad": ""}, "RANDARRAY": {"a": "([rows]; [columns]; [min]; [max]; [integer])", "d": "Returns an array of random numbers", "ad": "the number of rows in the returned array!the number of columns in the returned array!the minimum number you would like returned!the maximum number you would like returned!return an integer or a decimal value. TRUE for an integer, FALSE for a decimal number"}, "RANDBETWEEN": {"a": "(bottom; top)", "d": "Returns a random number between the numbers you specify", "ad": "is the smallest integer RANDBETWEEN will return!is the largest integer RANDBETWEEN will return"}, "ROMAN": {"a": "(number; [form])", "d": "Converts an Arabic numeral to Roman, as text", "ad": "is the Arabic numeral you want to convert!is the number specifying the type of Roman numeral you want."}, "ROUND": {"a": "(number; num_digits)", "d": "Rounds a number to a specified number of digits", "ad": "is the number you want to round!is the number of digits to which you want to round. Negative rounds to the left of the decimal point; zero to the nearest integer"}, "ROUNDDOWN": {"a": "(number; num_digits)", "d": "Rounds a number down, towards zero", "ad": "is any real number that you want rounded down!is the number of digits to which you want to round. Negative rounds to the left of the decimal point; zero or omitted, to the nearest integer"}, "ROUNDUP": {"a": "(number; num_digits)", "d": "Rounds a number up, away from zero", "ad": "is any real number that you want rounded up!is the number of digits to which you want to round. Negative rounds to the left of the decimal point; zero or omitted, to the nearest integer"}, "SEC": {"a": "(number)", "d": "Returns the secant of an angle", "ad": "is the angle in radians for which you want the secant"}, "SECH": {"a": "(number)", "d": "Returns the hyperbolic secant of an angle", "ad": "is the angle in radians for which you want the hyperbolic secant"}, "SERIESSUM": {"a": "(x; n; m; coefficients)", "d": "Returns the sum of a power series based on the formula", "ad": "is the input value to the power series!is the initial power to which you want to raise x!is the step by which to increase n for each term in the series!is a set of coefficients by which each successive power of x is multiplied"}, "SIGN": {"a": "(number)", "d": "Returns the sign of a number: 1 if the number is positive, zero if the number is zero, or -1 if the number is negative", "ad": "is any real number"}, "SIN": {"a": "(number)", "d": "Returns the sine of an angle", "ad": "is the angle in radians for which you want the sine. Degrees * PI()/180 = radians"}, "SINH": {"a": "(number)", "d": "Returns the hyperbolic sine of a number", "ad": "is any real number"}, "SQRT": {"a": "(number)", "d": "Returns the square root of a number", "ad": "is the number for which you want the square root"}, "SQRTPI": {"a": "(number)", "d": "Returns the square root of (number * Pi)", "ad": "is the number by which p is multiplied"}, "SUBTOTAL": {"a": "(function_num; ref1; ...)", "d": "Returns a subtotal in a list or database", "ad": "is the number 1 to 11 that specifies the summary function for the subtotal.!are 1 to 254 ranges or references for which you want the subtotal"}, "SUM": {"a": "(number1; [number2]; ...)", "d": "Adds all the numbers in a range of cells", "ad": "are 1 to 255 numbers to sum. Logical values and text are ignored in cells, included if typed as arguments"}, "SUMIF": {"a": "(range; criteria; [sum_range])", "d": "Adds the cells specified by a given condition or criteria", "ad": "is the range of cells you want evaluated!is the condition or criteria in the form of a number, expression, or text that defines which cells will be added!are the actual cells to sum. If omitted, the cells in range are used"}, "SUMIFS": {"a": "(sum_range; criteria_range; criteria; ...)", "d": "Adds the cells specified by a given set of conditions or criteria", "ad": "are the actual cells to sum.!is the range of cells you want evaluated for the particular condition!is the condition or criteria in the form of a number, expression, or text that defines which cells will be added"}, "SUMPRODUCT": {"a": "(array1; [array2]; [array3]; ...)", "d": "Returns the sum of the products of corresponding ranges or arrays", "ad": "are 2 to 255 arrays for which you want to multiply and then add components. All arrays must have the same dimensions"}, "SUMSQ": {"a": "(number1; [number2]; ...)", "d": "Returns the sum of the squares of the arguments. The arguments can be numbers, arrays, names or references to cells that contain numbers", "ad": "are 1 to 255 numbers, arrays, names, or references to arrays for which you want the sum of the squares"}, "SUMX2MY2": {"a": "(array_x; array_y)", "d": "Sums the differences between the squares of two corresponding ranges or arrays", "ad": "is the first range or array of numbers and can be a number or name, array, or reference that contains numbers!is the second range or array of numbers and can be a number or name, array, or reference that contains numbers"}, "SUMX2PY2": {"a": "(array_x; array_y)", "d": "Returns the sum total of the sums of squares of numbers in two corresponding ranges or arrays", "ad": "is the first range or array of numbers and can be a number or name, array, or reference that contains numbers!is the second range or array of numbers and can be a number or name, array, or reference that contains numbers"}, "SUMXMY2": {"a": "(array_x; array_y)", "d": "Sums the squares of the differences in two corresponding ranges or arrays", "ad": "is the first range or array of values and can be a number or name, array, or reference that contains numbers!is the second range or array of values and can be a number or name, array, or reference that contains numbers"}, "TAN": {"a": "(number)", "d": "Returns the tangent of an angle", "ad": "is the angle in radians for which you want the tangent. Degrees * PI()/180 = radians"}, "TANH": {"a": "(number)", "d": "Returns the hyperbolic tangent of a number", "ad": "is any real number"}, "TRUNC": {"a": "(number; [num_digits])", "d": "Truncates a number to an integer by removing the decimal, or fractional, part of the number", "ad": "is the number you want to truncate!is a number specifying the precision of the truncation, 0 (zero) if omitted"}, "ADDRESS": {"a": "(row_num; column_num; [abs_num]; [a1]; [sheet_text])", "d": "Creates a cell reference as text, given specified row and column numbers", "ad": "is the row number to use in the cell reference: Row_number = 1 for row 1!is the column number to use in the cell reference. For example, Column_number = 4 for column D!specifies the reference type: absolute = 1; absolute row/relative column = 2; relative row/absolute column = 3; relative = 4!is a logical value that specifies the reference style: A1 style = 1 or TRUE; R1C1 style = 0 or FALSE!is text specifying the name of the worksheet to be used as the external reference"}, "CHOOSE": {"a": "(index_num; value1; [value2]; ...)", "d": "Chooses a value or action to perform from a list of values, based on an index number", "ad": "specifies which value argument is selected. Index_num must be between 1 and 254, or a formula or a reference to a number between 1 and 254!are 1 to 254 numbers, cell references, defined names, formulas, functions, or text arguments from which CHOOSE selects"}, "COLUMN": {"a": "([reference])", "d": "Returns the column number of a reference", "ad": "is the cell or range of contiguous cells for which you want the column number. If omitted, the cell containing the COLUMN function is used"}, "COLUMNS": {"a": "(array)", "d": "Returns the number of columns in an array or reference", "ad": "is an array or array formula, or a reference to a range of cells for which you want the number of columns"}, "FORMULATEXT": {"a": "(reference)", "d": "Returns a formula as a string", "ad": "is a reference to a formula"}, "HLOOKUP": {"a": "(lookup_value; table_array; row_index_num; [range_lookup])", "d": "Looks for a value in the top row of a table or array of values and returns the value in the same column from a row you specify", "ad": "is the value to be found in the first row of the table and can be a value, a reference, or a text string!is a table of text, numbers, or logical values in which data is looked up. Table_array can be a reference to a range or a range name!is the row number in table_array from which the matching value should be returned. The first row of values in the table is row 1!is a logical value: to find the closest match in the top row (sorted in ascending order) = TRUE or omitted; find an exact match = FALSE"}, "HYPERLINK": {"a": "(link_location; [friendly_name])", "d": "Creates a shortcut or jump that opens a document stored on your hard drive, a network server, or on the Internet", "ad": "is the text giving the path and file name to the document to be opened, a hard drive location, UNC address, or URL path!is text or a number that is displayed in the cell. If omitted, the cell displays the Link_location text"}, "INDEX": {"a": "(array; row_num; [column_num]!reference; row_num; [column_num]; [area_num])", "d": "Returns a value or reference of the cell at the intersection of a particular row and column, in a given range", "ad": "is a range of cells or an array constant.!selects the row in Array or Reference from which to return a value. If omitted, Column_num is required!selects the column in Array or Reference from which to return a value. If omitted, Row_num is required!is a reference to one or more cell ranges!selects the row in Array or Reference from which to return a value. If omitted, Column_num is required!selects the column in Array or Reference from which to return a value. If omitted, Row_num is required!selects a range in Reference from which to return a value. The first area selected or entered is area 1, the second area is area 2, and so on"}, "INDIRECT": {"a": "(ref_text; [a1])", "d": "Returns the reference specified by a text string", "ad": "is a reference to a cell that contains an A1- or R1C1-style reference, a name defined as a reference, or a reference to a cell as a text string!is a logical value that specifies the type of reference in Ref_text: R1C1-style = FALSE; A1-style = TRUE or omitted"}, "LOOKUP": {"a": "(lookup_value; lookup_vector; [result_vector]!lookup_value; array)", "d": "Looks up a value either from a one-row or one-column range or from an array. Provided for backwards compatibility", "ad": "is a value that LOOKUP searches for in Lookup_vector and can be a number, text, a logical value, or a name or reference to a value!is a range that contains only one row or one column of text, numbers, or logical values, placed in ascending order!is a range that contains only one row or column, the same size as Lookup_vector!is a value that LOOKUP searches for in Array and can be a number, text, a logical value, or a name or reference to a value!is a range of cells that contain text, number, or logical values that you want to compare with Lookup_value"}, "MATCH": {"a": "(lookup_value; lookup_array; [match_type])", "d": "Returns the relative position of an item in an array that matches a specified value in a specified order", "ad": "is the value you use to find the value you want in the array, a number, text, or logical value, or a reference to one of these!is a contiguous range of cells containing possible lookup values, an array of values, or a reference to an array!is a number 1, 0, or -1 indicating which value to return."}, "OFFSET": {"a": "(reference; rows; cols; [height]; [width])", "d": "Returns a reference to a range that is a given number of rows and columns from a given reference", "ad": "is the reference from which you want to base the offset, a reference to a cell or range of adjacent cells!is the number of rows, up or down, that you want the upper-left cell of the result to refer to!is the number of columns, to the left or right, that you want the upper-left cell of the result to refer to!is the height, in number of rows, that you want the result to be, the same height as Reference if omitted!is the width, in number of columns, that you want the result to be, the same width as Reference if omitted"}, "ROW": {"a": "([reference])", "d": "Returns the row number of a reference", "ad": "is the cell or a single range of cells for which you want the row number; if omitted, returns the cell containing the ROW function"}, "ROWS": {"a": "(array)", "d": "Returns the number of rows in a reference or array", "ad": "is an array, an array formula, or a reference to a range of cells for which you want the number of rows"}, "TRANSPOSE": {"a": "(array)", "d": "Converts a vertical range of cells to a horizontal range, or vice versa", "ad": "is a range of cells on a worksheet or an array of values that you want to transpose"}, "UNIQUE": {"a": "(array; [by_col]; [exactly_once])", "d": "Returns the unique values from a range or array.", "ad": "the range or array from which to return unique rows or columns!is a logical value: compare rows against each other and return the unique rows = FALSE or omitted; compare columns against each other and return the unique columns = TRUE!is a logical value: return rows or columns that occur exactly once from the array = TRUE; return all distinct rows or columns from the array = FALSE or omitted"}, "VLOOKUP": {"a": "(lookup_value; table_array; col_index_num; [range_lookup])", "d": "Looks for a value in the leftmost column of a table and then returns a value in the same row from a column that you specify. By default, the table must be sorted in an ascending order", "ad": "is the value to be found in the first column of the table, and can be a value, a reference, or a text string!is a table of text, numbers, or logical values, in which data is retrieved. Table_array can be a reference to a range or a range name!is the column number in table_array from which the matching value should be returned. The first column of values in the table is column 1!is a logical value: to find the closest match in the first column (sorted in ascending order) = TRUE or omitted; find an exact match = FALSE"}, "XLOOKUP": {"a": "(lookup_value; lookup_array; return_array; [if_not_found]; [match_mode]; [search_mode])", "d": "Searches a range or an array for a match and returns the corresponding item from a second range or array. By default, an exact match is used", "ad": "is the value to search for!is the array or range to search!is the array or range to return!returned if no match is found!specify how to match lookup_value against the values in lookup_array!specify the search mode to use. By default, a first to last search will be used"}, "CELL": {"a": "(info_type; [reference])", "d": "Returns information about the formatting, location, or contents of a cell", "ad": "is a text value that specifies what type of cell information you want to return!the cell that you want information about"}, "ERROR.TYPE": {"a": "(error_val)", "d": "Returns a number matching an error value.", "ad": "is the error value for which you want the identifying number, and can be an actual error value or a reference to a cell containing an error value"}, "ISBLANK": {"a": "(value)", "d": "Checks whether a reference is to an empty cell, and returns TRUE or FALSE", "ad": "is the cell or a name that refers to the cell you want to test"}, "ISERR": {"a": "(value)", "d": "Checks whether a value is an error other than #N/A, and returns TRUE or FALSE", "ad": "is the value you want to test. Value can refer to a cell, a formula, or a name that refers to a cell, formula, or value"}, "ISERROR": {"a": "(value)", "d": "Checks whether a value is an error, and returns TRUE or FALSE", "ad": "is the value you want to test. Value can refer to a cell, a formula, or a name that refers to a cell, formula, or value"}, "ISEVEN": {"a": "(number)", "d": "Returns TRUE if the number is even", "ad": "is the value to test"}, "ISFORMULA": {"a": "(reference)", "d": "Checks whether a reference is to a cell containing a formula, and returns TRUE or FALSE", "ad": "is a reference to the cell you want to test.  Reference can be a cell reference, a formula, or name that refers to a cell"}, "ISLOGICAL": {"a": "(value)", "d": "Checks whether a value is a logical value (TRUE or FALSE), and returns TRUE or FALSE", "ad": "is the value you want to test. Value can refer to a cell, a formula, or a name that refers to a cell, formula, or value"}, "ISNA": {"a": "(value)", "d": "Checks whether a value is #N/A, and returns TRUE or FALSE", "ad": "is the value you want to test. Value can refer to a cell, a formula, or a name that refers to a cell, formula, or value"}, "ISNONTEXT": {"a": "(value)", "d": "Checks whether a value is not text (blank cells are not text), and returns TRUE or FALSE", "ad": "is the value you want tested: a cell; a formula; or a name referring to a cell, formula, or value"}, "ISNUMBER": {"a": "(value)", "d": "Checks whether a value is a number, and returns TRUE or FALSE", "ad": "is the value you want to test. Value can refer to a cell, a formula, or a name that refers to a cell, formula, or value"}, "ISODD": {"a": "(number)", "d": "Returns TRUE if the number is odd", "ad": "is the value to test"}, "ISREF": {"a": "(value)", "d": "Checks whether a value is a reference, and returns TRUE or FALSE", "ad": "is the value you want to test. Value can refer to a cell, a formula, or a name that refers to a cell, formula, or value"}, "ISTEXT": {"a": "(value)", "d": "Checks whether a value is text, and returns TRUE or FALSE", "ad": "is the value you want to test. Value can refer to a cell, a formula, or a name that refers to a cell, formula, or value"}, "N": {"a": "(value)", "d": "Converts non-number value to a number, dates to serial numbers, TRUE to 1, anything else to 0 (zero)", "ad": "is the value you want converted"}, "NA": {"a": "()", "d": "Returns the error value #N/A (value not available)", "ad": ""}, "SHEET": {"a": "([value])", "d": "Returns the sheet number of the referenced sheet", "ad": "is the name of a sheet or a reference that you want the sheet number of.  If omitted the number of the sheet containing the function is returned"}, "SHEETS": {"a": "([reference])", "d": "Returns the number of sheets in a reference", "ad": "is a reference for which you want to know the number of sheets it contains.  If omitted the number of sheets in the workbook containing the function is returned"}, "TYPE": {"a": "(value)", "d": "Returns an integer representing the data type of a value: number = 1; text = 2; logical value = 4; error value = 16; array = 64; compound data = 128", "ad": "can be any value"}, "AND": {"a": "(logical1; [logical2]; ...)", "d": "Checks whether all arguments are TRUE, and returns TRUE if all arguments are TRUE", "ad": "are 1 to 255 conditions you want to test that can be either TRUE or FALSE and can be logical values, arrays, or references"}, "FALSE": {"a": "()", "d": "Returns the logical value FALSE", "ad": ""}, "IF": {"a": "(logical_test; [value_if_true]; [value_if_false])", "d": "Checks whether a condition is met, and returns one value if TRUE, and another value if FALSE", "ad": "is any value or expression that can be evaluated to TRUE or FALSE!is the value that is returned if Logical_test is TRUE. If omitted, TRUE is returned. You can nest up to seven IF functions!is the value that is returned if Logical_test is FALSE. If omitted, FALSE is returned"}, "IFS": {"a": "(logical_test; value_if_true; ...)", "d": "Checks whether one or more conditions are met and returns a value corresponding to the first TRUE condition", "ad": "is any value or expression that can be evaluated to TRUE or FALSE!is the value returned if Logical_test is TRUE"}, "IFERROR": {"a": "(value; value_if_error)", "d": "Returns value_if_error if expression is an error and the value of the expression itself otherwise", "ad": "is any value or expression or reference!is any value or expression or reference"}, "IFNA": {"a": "(value; value_if_na)", "d": "Returns the value you specify if the expression resolves to #N/A, otherwise returns the result of the expression", "ad": "is any value or expression or reference!is any value or expression or reference"}, "NOT": {"a": "(logical)", "d": "Changes FALSE to TRUE, or TRUE to FALSE", "ad": "is a value or expression that can be evaluated to TRUE or FALSE"}, "OR": {"a": "(logical1; [logical2]; ...)", "d": "Checks whether any of the arguments are TRUE, and returns TRUE or FALSE. Returns FALSE only if all arguments are FALSE", "ad": "are 1 to 255 conditions that you want to test that can be either TRUE or FALSE"}, "SWITCH": {"a": "(expression; value1; result1; [default_or_value2]; [result2]; ...)", "d": "Evaluates an expression against a list of values and returns the result corresponding to the first matching value. If there is no match, an optional default value is returned", "ad": "is an expression to be evaluated!is a value to be compared with expression!is a result to be returned if the corresponding value matches expression"}, "TRUE": {"a": "()", "d": "Returns the logical value TRUE", "ad": ""}, "XOR": {"a": "(logical1; [logical2]; ...)", "d": "Returns a logical 'Exclusive Or' of all arguments", "ad": "are 1 to 254 conditions you want to test that can be either TRUE or FALSE and can be logical values, arrays, or references"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Returns text that’s before delimiting characters.", "ad": "The text you want to search for the delimiter.!The character or string to use as a delimiter.!The desired occurrence of delimiter. The default is 1. A negative number searches from the end.!Searches the text for a delimiter match. By default, a case-sensitive match is done.!Whether to match the delimiter against the end of text. By default, they're not matched.!Returned if no match is found. By default, #N/A is returned."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Returns text that’s after delimiting characters.", "ad": "The text you want to search for the delimiter.!The character or string to use as a delimiter.!The desired occurrence of delimiter. The default is 1. A negative number searches from the end.!Searches the text for a delimiter match. By default, a case-sensitive match is done.!Whether to match the delimiter against the end of text. By default, they're not matched.!Returned if no match is found. By default, #N/A is returned."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Splits text into rows or columns using delimiters.", "ad": "The text to split!Character or string to split columns by.!Character or string to split rows by.!Whether to ignore empty cells. Defaults to FALSE.!Searches the text for a delimiter match. By default, a case-sensitive match is done.!The value to use for padding. By default, #N/A is used."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Wraps a row or column vector after a specified number of values.", "ad": "The vector or reference to wrap.!The maximum number of values per row.!The value with which to pad. The default is #N/A."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Vertically stacks arrays into one array.", "ad": "An array or reference to be stacked."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Horizontally stacks arrays into one array.", "ad": "An array or reference to be stacked."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Returns rows from an array or reference.", "ad": "The array or reference containing the rows to be returned.!The number of the row to be returned."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Returns columns from an array or reference.", "ad": "The array or reference containing the columns to be returned.!The number of the column to be returned."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Returns the array as one column.", "ad": "The array or reference to return as a column.!Whether to ignore certain types of values. By default, no values are ignored.!Scan the array by column. By default, the array is scanned by row."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "Returns the array as one row.", "ad": "The array or reference to return as a row.!Whether to ignore certain types of values. By default, no values are ignored.!Scan the array by column. By default, the array is scanned by row."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Wraps a row or column vector after a specified number of values.", "ad": "The vector or reference to wrap.!The maximum number of values per column.!The value with which to pad. The default is #N/A."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Returns rows or columns from array start or end.", "ad": "The array from which to take rows or columns.!The number of rows to take. A negative value takes from the end of the array.!The number of columns to take. A negative value takes from the end of the array."}, "DROP": {"a": "(array, rows, [columns])", "d": "Drops rows or columns from array start or end.", "ad": "The array from which to drop rows or columns.!The number of rows to drop. A negative value drops from the end of the array.!The number of columns to drop. A negative value drops from the end of the array."}, "SEQUENCE": {"a": "(rows, [columns], [start], [step])", "d": "Returns a sequence of numbers", "ad": "the number of rows to return!the number of columns to return!the first number in the sequence!the amount to increment each subsequent value in the sequence"}, "EXPAND": {"a": "(array, rows, [columns], [pad_with])", "d": "Expands an array to the specified dimensions.", "ad": "The array to expand.!The number of rows in the expanded array. If missing, rows will not be expanded.!The number of columns in the expanded array. If missing, columns will not be expanded.!The value with which to pad. The default is #N/A."}, "XMATCH": {"a": "(lookup_value, lookup_array, [match_mode], [search_mode])", "d": "Returns the relative position of an item in an array. By default, an exact match is required", "ad": "is the value to search for!is the array or range to search!specify how to match the lookup_value against the values in lookup_array!specify the search mode to use. By default, a first to last search will be used"}, "FILTER": {"a": "(array, include, [if_empty])", "d": "Filter a range or array", "ad": "the range or array to filter!an array of booleans where TRUE represents a row or column to retain!returned if no items are retained"}, "ARRAYTOTEXT": {"a": "(array, [format])", "d": "Returns a text representation of an array", "ad": "the array to represent as text!the format of the text"}, "SORT": {"a": "(array, [sort_index], [sort_order], [by_col])", "d": "Sorts a range or array", "ad": "the range or array to sort!a number indicating the row or column to sort by!a number indicating the desired sort order; 1 for ascending order (default), -1 for descending order!a logical value indicating the desired sort direction: FALSE to sort by row (default), TRUE to sort by column"}, "SORTBY": {"a": "(array, by_array, [sort_order], ...)", "d": "Sorts a range or array based on the values in a corresponding range or array", "ad": "the range or array to sort!the range or array to sort on!a number indicating the desired sort order; 1 for ascending order (default), -1 for descending order"}, "GETPIVOTDATA": {"a": "(data_field; pivot_table; [field]; [item]; ...)", "d": "Extracts data stored in a PivotTable.", "ad": "is the name of the data field to extract data from!is a reference to a cell or range of cells in the PivotTable that contains the data you want to retrieve!field to refer to!field item to refer to"}, "IMPORTRANGE": {"a": "(spreadsheet_url, range_string)", "d": "Imports a range of cells from a specified spreadsheet.", "ad": "is the URL of the spreadsheet from where data will be imported!is the range to import"}}