{"DATE": {"a": "(jaar; maand; dag)", "d": "Geeft het getal als resultaat dat de datum aangeeft in code voor de datum/tijd", "ad": "is een getal tussen 1900 of 1904 (afhankeli<PERSON> van het datumsysteem van de werkmap) en 9999!is een getal van 1 tot 12 dat de maand van het jaar aangeeft!is een getal van 1 tot 31 dat de dag van de maand aangeeft"}, "DATEDIF": {"a": "(begindatum; einddatum; eenheid)", "d": "<PERSON><PERSON>ent het aantal dagen, maanden of jaren tussen twee datums", "ad": "Een datum die de eerste datum, of begindatum, van de periode aangeeft!Een datum die de laatste datum, of einddatum, van de periode aangeeft!Het type informatie dat u wilt laten retourneren"}, "DATEVALUE": {"a": "(datum_tekst)", "d": "Zet de opgegeven datum in de vorm van tekst om in de code voor de datum/tijd", "ad": "is tekst die overeenkomt met een datum in een Spreadsheet Editor-datumnotatie, tussen 1-1-1900 of 1-1-1904 (a<PERSON><PERSON><PERSON><PERSON><PERSON> van het datumsysteem van de werkmap) en 31-12-9999"}, "DAY": {"a": "(serieel-getal)", "d": "<PERSON>ft als resultaat de dag van de <PERSON>, een getal tussen 1 en 31.", "ad": "is een getal in het systeem dat in Spreadsheet Editor wordt gebruikt voor datumberekeningen"}, "DAYS": {"a": "(einddatum; begindatum)", "d": "Geeft als resultaat het aantal dagen tussen twee datums", "ad": "startdatum en einddatum zijn de twee datums waartussen u het aantal dagen wilt weten!startdatum en einddatum zijn de twee datums waartussen u het aantal dagen wilt weten"}, "DAYS360": {"a": "(begindatum; einddatum; [methode])", "d": "Berekent het aantal dagen tussen twee datums op basis van een jaar met 360 dagen (12 maanden van 30 dagen)", "ad": "startdatum en einddatum zijn de twee datums waartussen u het aantal dagen wilt bepalen!startdatum en einddatum zijn de twee datums waartussen u het aantal dagen wilt bepalen!is een logische waarde die de methode bepaalt die u in de berekening wilt gebruiken: V.S. (NASD) = ONWAAR of leeg, Europees = WAAR."}, "EDATE": {"a": "(begindatum; aantal_maanden)", "d": "Zet de datum die het opgegeven aantal maanden voor of na de begindatum ligt, om in een serieel getal", "ad": "is een serieel getal dat de begindatum aangeeft!is het aantal maanden voor of na de begindatum"}, "EOMONTH": {"a": "(begindatum; aantal_maanden)", "d": "<PERSON>et de laatste dag van de maand die een opgegeven aantal maanden voor of na de begindatum ligt, om in een serieel getal", "ad": "is een serieel getal dat de begindatum aangeeft!is het aantal maanden voor of na de begindatum"}, "HOUR": {"a": "(serieel-getal)", "d": "Geeft als resultaat het aantal uren als een getal van 0 (00:00) tot 23 (23:00).", "ad": "is een getal dat een dag of tijd a<PERSON><PERSON><PERSON> in het systeem dat in Spreadsheet Editor wordt gebruikt voor datum- en tijdberekeningen of tekst in tijdnotatie, zoals 16:48:00 of 4:48:00 PM"}, "ISOWEEKNUM": {"a": "(datum)", "d": "Geeft als resultaat het ISO-weeknummer van het jaar voor een bepaalde datum", "ad": "is de datum-tijdcode die door Spreadsheet Editor wordt gebruikt voor de berekening van de datum en tijd"}, "MINUTE": {"a": "(serieel-getal)", "d": "Geeft als resultaat het aantal minuten (een getal van 0 tot en met 59).", "ad": "is een getal dat een dag of tijd a<PERSON><PERSON><PERSON> in het systeem dat in Spreadsheet Editor wordt gebruikt voor datum- en tijdberekeningen of tekst in tijdopmaak, zoals 16:48:00 of 4:48:00 PM"}, "MONTH": {"a": "(serieel-getal)", "d": "<PERSON>ft als resultaat de ma<PERSON>, een getal van 1 (januari) tot en met 12 (december).", "ad": "is een getal in het systeem dat in Spreadsheet Editor wordt gebruikt voor datumberekeningen"}, "NETWORKDAYS": {"a": "(begindatum; einddatum; [vakantiedagen])", "d": "Geeft het aantal volledige werkdagen tussen twee datums", "ad": "is een serieel getal dat de startdatum aangeeft!is een serieel getal dat de einddatum aangeeft!is een optionele verzameling met een of meer seriële getallen die de datums aangeven waarop niet wordt gewerkt, zoals nationale feestdagen en vakantiedagen"}, "NETWORKDAYS.INTL": {"a": "(begindatum; einddatum; [weekend]; [vakantiedagen])", "d": "Geeft het aantal volledige werkdagen tussen twee datums met aangepaste weekendparameters", "ad": "is een serieel getal dat de startdatum aangeeft!is een serieel getal dat de einddatum aangeeft!is een getal of tekenreeks waarmee weekends worden aangegeven!is een optionele verzameling met een of meer seriële getallen die de datums aangeven waarop niet wordt gewerkt, zoals nationale feestdagen en vakantiedagen"}, "NOW": {"a": "()", "d": "Geeft als resultaat de huidige datum en tijd in de datum- en tijdnotatie.", "ad": ""}, "SECOND": {"a": "(serieel-getal)", "d": "Geeft als resultaat het aantal seconden (een getal van 0 tot en met 59).", "ad": "is het seri<PERSON>e getal dat een dag of tijd a<PERSON><PERSON><PERSON> in het systeem dat in Spreadsheet Editor wordt gebruikt voor datum- en tijdberekeningen of een tekst in tijdopmaak, zoals 16:48:23 of 4:48:23 PM"}, "TIME": {"a": "(uur; minuut; seconde)", "d": "Converteert u<PERSON>, minuten en seconden die als getallen zijn opgegeven naar seriële getallen in de tijdnotatie", "ad": "is een getal tussen 0 en 23 dat het uur aangeeft!is een getal tussen 0 en 59 dat de minuut aangeeft!is een getal tussen 0 en 59 dat de seconde aangeeft"}, "TIMEVALUE": {"a": "(tijd_tekst)", "d": "Converteert een tijd in tekstnotatie naar een serieel getal voor tijd, een getal van 0 (00:00:00) tot 0,999988426 (23:59:59). Pas na het opgeven van de formule een tijdnotatie toe op het getal", "ad": "is een tekenreeks die een tijd aangeeft in een van de tijdnotaties van Spreadsheet Editor (datuminformatie in de reeks wordt genegeerd)"}, "TODAY": {"a": "()", "d": "Geeft als resultaat de huidige datum in de datumnotatie.", "ad": ""}, "WEEKDAY": {"a": "(serieel-getal; [type_getal])", "d": "Geeft als resultaat een getal van 1 tot 7 dat de dag van de week van een datum aangeeft.", "ad": "is het seriële getal dat een datum aangeeft!is een getal: 1 = zondag is 1 tot en met zaterdag is 7, 2 = maandag is 1 tot en met zondag is 7, 3 = maandag is 0 tot en met zondag is 6"}, "WEEKNUM": {"a": "(serieel_getal; [type_resultaat])", "d": "Zet een serieel getal om in een weeknummer", "ad": "is het seriële getal dat een dag of tijd aang<PERSON>ft in het systeem dat Spreadsheet Editor gebruikt voor datum- en tijdberekeningen!is een getal (1 of 2) waarmee u het type waarde bepaalt dat u als resultaat wilt hebben"}, "WORKDAY": {"a": "(begindatum; aantal_dagen; [vakantiedagen])", "d": "Geeft het seriële getal van de datum voor of na een opgegeven aantal werkdagen", "ad": "is een serieel getal dat de begindatum aangeeft!is het aantal werkdagen voor of na de begindatum!is een optionele matrix met een of meer seriële getallen die de datums aangeven waarop niet wordt gewerkt, zoals nationale feestdagen en vakantiedagen"}, "WORKDAY.INTL": {"a": "(begindatum; dagen; [weekend]; [vakantiedagen])", "d": "Geeft het seriële getal van de datum voor of na een opgegeven aantal werkdagen, met aangep<PERSON>e weekendpar<PERSON><PERSON>", "ad": "is een serieel getal dat de begindatum aangeeft!is het aantal werkdagen voor of na de begindatum!is een getal of tekenreeks waarmee weekends worden aangegeven!is een optionele matrix van een of meer seriële getallen die de datums aangeven waarop niet wordt gewerkt, zoals nationale feestdagen en vakantiedagen"}, "YEAR": {"a": "(serieel-getal)", "d": "<PERSON>ft als resultaat het jaar van een datum, een geheel getal in het bereik 1900 - 9999.", "ad": "is een getal in het systeem dat in Spreadsheet Editor wordt gebruikt voor datumberekeningen"}, "YEARFRAC": {"a": "(begindatum; einddatum; [soort_jaar])", "d": "Berekent het gede<PERSON>te van het jaar, uitgedrukt in het aantal dagen tussen begindatum en einddatum", "ad": "is een serieel getal dat de startdatum aangeeft!is een serieel getal dat de einddatum aangeeft!is het soort jaar waarop de berekening is gebaseerd"}, "BESSELI": {"a": "(x; n)", "d": "Berekent de gemodificeerde i-functie van Bessel In(x)", "ad": "is de waarde waarop de functie moet worden geëvalueerd!is de volgorde waarin de <PERSON>ssel-functie wordt uitgevoerd"}, "BESSELJ": {"a": "(x; n)", "d": "Berekent de gemodificeerde j-functie van Bessel Jn(x)", "ad": "is de waarde waarop de functie moet worden geëvalueerd!is de volgorde waarin de <PERSON>ssel-functie wordt uitgevoerd"}, "BESSELK": {"a": "(x; n)", "d": "Berekent de gemodificeerde k-functie van Bessel Kn(x)", "ad": "is de waarde waarop de functie moet worden geëvalueerd!is de volgorde waarin de functie wordt uitgevoerd"}, "BESSELY": {"a": "(x; n)", "d": "Berekent de gemodificeerde y-functie van Bessel Yn(x)", "ad": "is de waarde waarop de functie moet worden geëvalueerd!is de volgorde waarin de functie wordt uitgevoerd"}, "BIN2DEC": {"a": "(getal)", "d": "Converteert een binair getal naar een decimaal getal", "ad": "is het binaire getal dat u wilt converteren"}, "BIN2HEX": {"a": "(getal; [aantal_tekens])", "d": "Converteert een binair getal naar een hexadecimaal getal", "ad": "is het binaire getal dat u wilt converteren!is het aantal tekens dat u wilt gebruiken"}, "BIN2OCT": {"a": "(getal; [aantal_tekens])", "d": "Converteert een binair getal naar een octaal getal", "ad": "is het binaire getal dat u wilt converteren!is het aantal tekens dat u wilt gebruiken"}, "BITAND": {"a": "(getal1; getal2)", "d": "Geeft als resultaat een bitsgewijze 'En' van twee getallen", "ad": "is de decimale weergave van het binaire getal dat u wilt evalueren!is de decimale weergave van het binaire getal dat u wilt evalueren"}, "BITLSHIFT": {"a": "(getal; verschuivingsaantal)", "d": "Geeft als resultaat een getal dat naar links is verschoven met <verschuivingsaantal> bits", "ad": "is de decimale weergave van het binaire getal dat u wilt evalueren!is het aantal bits waarmee u het getal naar links wilt verschuiven"}, "BITOR": {"a": "(getal1; getal2)", "d": "Geeft als resultaat een bitsgewijze 'Of' van twee getallen", "ad": "is de decimale weergave van het binaire getal dat u wilt evalueren!is de decimale weergave van het binaire getal dat u wilt evalueren"}, "BITRSHIFT": {"a": "(getal; verschuivingsaantal)", "d": "Geeft als resultaat een getal dat naar rechts is verschoven met <verschuivingsaantal> bits", "ad": "is de decimale weergave van het binaire getal dat u wilt evalueren!is het aantal bits waarmee u het getal naar rechts wilt verschuiven"}, "BITXOR": {"a": "(getal1; getal2)", "d": "Geeft als resultaat een bitsgewij<PERSON> 'Exclusieve of' van twee getallen", "ad": "is de decimale weergave van het binaire getal dat u wilt evalueren!is de decimale weergave van het binaire getal dat u wilt evalueren"}, "COMPLEX": {"a": "(re<PERSON><PERSON>_deel; imaginair_deel; [achter<PERSON><PERSON><PERSON>])", "d": "Converteert reële en imaginaire coëfficiënten naar complexe getallen", "ad": "is de reële coëfficiënt van een complex getal!is de imaginaire coëfficiënt van het complexe getal!is het achter<PERSON><PERSON><PERSON> voor het imaginaire deel van het complexe getal"}, "CONVERT": {"a": "(getal; van_eenheid; naar_eenheid)", "d": "Converteert een getal in de ene maateenheid naar een getal in een andere maateenheid", "ad": "is de waarde in van_eenheid die u wilt converteren!is de eenheid die bij getal wordt gebruikt!is de eenheid die voor het resultaat wordt gebruikt"}, "DEC2BIN": {"a": "(getal; [aantal_tekens])", "d": "Converteert een decimaal getal naar een binair getal", "ad": "is het decimale gehele getal dat u wilt converteren!is het aantal tekens dat u wilt gebruiken"}, "DEC2HEX": {"a": "(getal; [aantal_tekens])", "d": "Converteert een decimaal getal naar een hexadecimaal getal", "ad": "is het decimale gehele getal dat u wilt converteren!is het aantal tekens dat u wilt gebruiken"}, "DEC2OCT": {"a": "(getal; [aantal_tekens])", "d": "Converteert een decimaal getal naar een octaal getal", "ad": "is het decimale gehele getal dat u wilt converteren!is het aantal tekens dat u wilt gebruiken"}, "DELTA": {"a": "(getal1; [getal2])", "d": "<PERSON><PERSON><PERSON> of twee getallen gelijk zijn", "ad": "is het eerste getal!is het tweede getal"}, "ERF": {"a": "(on<PERSON><PERSON><PERSON>; [boveng<PERSON>s])", "d": "Geeft de foutfunctie weer", "ad": "is de ondergrens voor FOUTFUNCTIE!is de bovengrens voor FOUTFUNCTIE"}, "ERF.PRECISE": {"a": "(X)", "d": "Geeft de foutfunctie als resultaat", "ad": "is de ondergrens voor FOUTFUNCTIE.NAUWKEURIG"}, "ERFC": {"a": "(x)", "d": "Geeft de bijbehorende foutfunctie", "ad": "is de ondergrens voor FOUTFUNCTIE"}, "ERFC.PRECISE": {"a": "(X)", "d": "Geeft de bijbehorende foutfunctie als resultaat", "ad": "is de ondergrens voor FOUT.COMPLEMENT.NAUWKEURIG"}, "GESTEP": {"a": "(getal; [drempel<PERSON><PERSON>e])", "d": "To<PERSON>t of een getal groter is dan de drem<PERSON>e", "ad": "is de waarde die u wilt vergelijken met drempelwaarde!is de drempelwaarde"}, "HEX2BIN": {"a": "(getal; [aantal_tekens])", "d": "Converteert een hexadecimaal getal naar een binair getal", "ad": "is het hexadecimale getal dat u wilt converteren!is het aantal tekens dat u wilt gebruiken"}, "HEX2DEC": {"a": "(getal)", "d": "Converteert een hexadecimaal getal naar een decimaal getal", "ad": "is het hexadecimale getal dat u wilt converteren"}, "HEX2OCT": {"a": "(getal; [aantal_tekens])", "d": "Converteert een hexadecimaal getal naar een octaal getal", "ad": "is het hexadecimale getal dat u wilt omzetten in een octaal getal!is het aantal tekens dat u wilt gebruiken"}, "IMABS": {"a": "(complex_getal)", "d": "Geeft de absolute waarde van een complex getal", "ad": "is een complex getal waarvan u de absolute waarde wilt berekenen"}, "IMAGINARY": {"a": "(complex_getal)", "d": "Berekent de imaginaire coëfficiënt van een complex getal", "ad": "is een complex getal waarvan u de imaginaire coëfficiënt wilt berekenen"}, "IMARGUMENT": {"a": "(complex_getal)", "d": "Berekent het argument theta, een hoek uitgedrukt in radialen", "ad": "is een complex getal waarvan u het argument theta wilt berekenen"}, "IMCONJUGATE": {"a": "(complex_getal)", "d": "Berekent de complex toegevoegde van een complex getal", "ad": "is een complex getal waarvan u de complex toegevoegde wilt berekenen"}, "IMCOS": {"a": "(complex_getal)", "d": "<PERSON><PERSON>ent de cosinus van een complex getal", "ad": "is een complex getal waarvan u de cosinus wilt berekenen"}, "IMCOSH": {"a": "(igetal)", "d": "Geeft als resultaat de cosinus hyperbolicus van een complex getal", "ad": "is een complex getal waarvoor u de cosinus hyperbolicus wilt berekenen"}, "IMCOT": {"a": "(igetal)", "d": "Geeft als resultaat de cotangens van een complex getal", "ad": "is een complex getal waarvoor u de cotangens wilt berekenen"}, "IMCSC": {"a": "(igetal)", "d": "Geeft als resultaat de cosecans van een complex getal", "ad": "is een complex getal waarvoor u de cosecans wilt berekenen"}, "IMCSCH": {"a": "(igetal)", "d": "Geeft als resultaat de cosecans hyperbolicus van een complex getal", "ad": "is een complex getal waarvoor u de cosecans hyperbolicus wilt berekenen"}, "IMDIV": {"a": "(complex_getal1; complex_getal2)", "d": "<PERSON><PERSON><PERSON> het quotiënt van twee complexe getallen", "ad": "is de complexe teller of het complexe deeltal!is de complexe noemer of de complexe deler"}, "IMEXP": {"a": "(complex_getal)", "d": "Berekent de exponentiële waarde van een complex getal", "ad": "is een complex getal waarvan u de exponent wilt berekenen"}, "IMLN": {"a": "(complex_getal)", "d": "Berekent de natuurlijke logaritme van een complex getal", "ad": "is een complex getal waarvan u de natuurlijke logaritme wilt berekenen"}, "IMLOG10": {"a": "(complex_getal)", "d": "Be<PERSON>ent de logaritme met grondtal 10 van een complex getal", "ad": "is een complex getal waarvan u de logaritme met het grondtal 10 wilt berekenen"}, "IMLOG2": {"a": "(complex_getal)", "d": "Berekent de logaritme met grondtal 2 van een complex getal", "ad": "is een complex getal waarvan u de logaritme met het grondtal 2 wilt berekenen"}, "IMPOWER": {"a": "(complex_getal; getal)", "d": "Verheft een complex getal tot een hele macht", "ad": "is een complex getal dat u tot een bepaalde macht wilt verheffen!is de macht waartoe u het complexe getal wilt verheffen"}, "IMPRODUCT": {"a": "(complex_getal1; [complex_getal2]; ...)", "d": "Berekent het product van 1 tot 255 complexe getallen", "ad": "complex_getal1, complex_getal,... zijn van 1 tot 255 complexe getallen die worden vermenigvuldigd."}, "IMREAL": {"a": "(complex_getal)", "d": "Bepaalt de reële coëfficiënt van een complex getal", "ad": "is een complex getal waarvan u de reële coëfficiënt wilt berekenen"}, "IMSEC": {"a": "(igetal)", "d": "Geeft als resultaat de secans van een complex getal", "ad": "is een complex getal waarvoor u de secans wilt berekenen"}, "IMSECH": {"a": "(igetal)", "d": "Geeft als resultaat de secans hyperbolicus van een complex getal", "ad": "is een complex getal waarvoor u de secans hyperbolicus wilt berekenen"}, "IMSIN": {"a": "(complex_getal)", "d": "Berekent de sinus van een complex getal", "ad": "is een complex getal waarvan u de sinus wilt berekenen"}, "IMSINH": {"a": "(igetal)", "d": "Geeft als resultaat de sinus hyperbolicus van een complex getal", "ad": "is een complex getal waarvoor u de sinus hyperbolicus wilt berekenen"}, "IMSQRT": {"a": "(complex_getal)", "d": "Berekent de vierkantswortel van een complex getal", "ad": "is een complex getal waarvan u de vierkantswortel wilt berekenen"}, "IMSUB": {"a": "(complex_getal1; complex_getal2)", "d": "Berekent het verschil tussen twee complexe getallen", "ad": "is het complexe getal waarvan u complex_getal2 wilt aftrekken!is het complexe getal dat u van complex_getal1 wilt aftrekken"}, "IMSUM": {"a": "(complex_getal1; [complex_getal2]; ...)", "d": "Geeft als resultaat de som van complexe getallen", "ad": "zijn van 1 tot 255 complexe getallen die worden toegevoegd"}, "IMTAN": {"a": "(igetal)", "d": "Geeft als resultaat de tangens van een complex getal", "ad": "is een complex getal waarvoor u de tangens wilt berekenen"}, "OCT2BIN": {"a": "(getal; [aantal_tekens])", "d": "Converteert een octaal getal naar een binair getal", "ad": "is het octale getal dat u wilt converteren!is het aantal tekens dat u wilt gebruiken"}, "OCT2DEC": {"a": "(getal)", "d": "Converteert een octaal getal naar een decimaal getal", "ad": "is het octale getal dat u wilt converteren"}, "OCT2HEX": {"a": "(getal; [aantal_tekens])", "d": "Converteert een octaal getal naar een hexadecimaal getal", "ad": "is het octale getal dat u wilt converteren!is het aantal tekens dat u wilt gebruiken"}, "DAVERAGE": {"a": "(database; veld; criteria)", "d": "Berekent het gemid<PERSON><PERSON> van de waarden in een kolom of database die voldoen aan de opgegeven voorwaarden", "ad": "is het celbereik waaruit de lijst of database bestaat. Een database is een lijst met verwante gege<PERSON>!is het label van de kolom tussen dubbele aanhalingstekens of een getal dat overeenkomt met de positie van de kolom in de lijst!is het celbereik dat de opgegeven voorwaarden bevat. Het bereik bevat een kolomlabel en een cel onder het label met een voorwaarde"}, "DCOUNT": {"a": "(database; veld; criteria)", "d": "<PERSON><PERSON> de cellen in de database die getallen bevatten in het veld (kolom) met records die voldoen aan de opgegeven voorwaarden", "ad": "is het celbereik waaruit de lijst of database bestaat. Een database is een lijst met verwante gege<PERSON>!is het label van de kolom tussen dubbele aanhalingstekens of een getal dat overeenkomt met de positie van de kolom in de lijst!is het celbereik dat de opgegeven voorwaarden bevat. Het bereik bevat een kolomlabel en een cel onder het label met een voorwaarde"}, "DCOUNTA": {"a": "(database; veld; criteria)", "d": "Telt in de database de niet-lege cellen in het veld (kolom) met records die overeenkomen met de opgegeven voorwaarden", "ad": "is het celbereik dat de database omvat. Een database is een lijst met verwante gegeven<PERSON>!is het label van de kolom tussen dubbele aanhalingstekens of een getal dat overeenkomt met de positie van de kolom in de lijst!is het celbereik dat de opgegeven voorwaarden bevat. Het bereik bevat een kolomlabel en een cel onder het label met een voorwaarde"}, "DGET": {"a": "(database; veld; criteria)", "d": "Haalt één record op uit een database dat voldoet aan de gespecificeerde criteria", "ad": "is het celbereik dat als database is gedefinieerd. Een database is een lijst met verwante gegevens!is het label van de kolom tussen dubbele aanhalingstekens of een getal dat overeenkomt met de positie van de kolom in de lijst!is het celbereik dat de opgegeven voorwaarden bevat. Het bereik bevat een kolomlabel en een cel onder het label met een voorwaarde"}, "DMAX": {"a": "(database; veld; criteria)", "d": "Geeft als resultaat de maximumwaarde in het veld (kolom) met records in de database die overeenkomen met de opgegeven voorwaarden", "ad": "is het celbereik dat als database is gedefinieerd. Een database is een lijst met verwante gegevens!is het label van de kolom tussen dubbele aanhalingstekens of een getal dat overeenkomt met de positie van de kolom in de lijst!is het celbereik dat de opgegeven voorwaarden bevat. Het bereik bevat een kolomlabel en een cel onder het label met een voorwaarde"}, "DMIN": {"a": "(database; veld; criteria)", "d": "Geeft als resultaat de minimumwaarde in het veld (kolom) met records in de database die overeenkomen met de opgegeven voorwaarden", "ad": "is het celbereik dat als database is gedefinieerd. Een database is een lijst met verwante gegevens!is het label van de kolom tussen dubbele aanhalingstekens of een getal dat overeenkomt met de positie van de kolom in de lijst!is het celbereik dat de opgegeven voorwaarden bevat. Het bereik bevat een kolomlabel en een cel onder het label met een voorwaarde"}, "DPRODUCT": {"a": "(database; veld; criteria)", "d": "Vermenigvuldigt de waarden in het veld (kolom) met records in de database die voldoen aan de opgegeven voorwaarden", "ad": "is het celbereik dat als database is gedefinieerd. Een database is een lijst met verwante gegevens!is het label van de kolom tussen dubbele aanhalingstekens of een getal dat overeenkomt met de positie van de kolom in de lijst!is het celbereik dat de opgegeven voorwaarden bevat. Het bereik bevat een kolomlabel en een cel onder het label met een voorwaarde"}, "DSTDEV": {"a": "(database; veld; criteria)", "d": "Maakt een schatting van de standaarddeviatie die is gebaseerd op een steekproef onder geselecteerde databasegegevens", "ad": "is het celbereik dat als database is gedefinieerd. Een database is een lijst met verwante gegevens!is het label van de kolom tussen dubbele aanhalingstekens of een getal dat overeenkomt met de positie van de kolom in de lijst!is het celbereik dat de opgegeven voorwaarden bevat. Het bereik bevat een kolomlabel en een cel onder het label met een voorwaarde"}, "DSTDEVP": {"a": "(database; veld; criteria)", "d": "<PERSON><PERSON>ent de standaarddeviatie die is gebaseerd op de hele populatie van geselecteerde databasegegevens", "ad": "is het celbereik dat als database is gedefinieerd. Een database is een lijst met verwante gegevens!is het label van de kolom tussen dubbele aanhalingstekens of een getal dat overeenkomt met de positie van de kolom in de lijst!is het celbereik dat de opgegeven voorwaarden bevat. Het bereik bevat een kolomlabel en een cel onder het label met een voorwaarde"}, "DSUM": {"a": "(database; veld; criteria)", "d": "Telt de getallen op in het veld (kolom) met records in de database die voldoen aan de opgegeven voorwaarden", "ad": "is het celbereik dat als database is gedefinieerd. Een database is een lijst met verwante gegevens!is het label van de kolom tussen dubbele aanhalingstekens of een getal dat overeenkomt met de positie van de kolom in de lijst!is het celbereik dat de opgegeven voorwaarden bevat. Het bereik bevat een kolomlabel en een cel onder het label met een voorwaarde"}, "DVAR": {"a": "(database; veld; criteria)", "d": "Maakt een schatting van de variantie die is gebaseerd op een steekproef onder geselecteerde databasegegevens", "ad": "is het celbereik dat als database is gedefinieerd. Een database is een lijst met verwante gegevens!is het label van de kolom tussen dubbele aanhalingstekens of een getal dat overeenkomt met de positie van de kolom in de lijst!is het celbereik dat de opgegeven voorwaarden bevat. Het bereik bevat een kolomlabel en een cel onder het label met een voorwaarde"}, "DVARP": {"a": "(database; veld; criteria)", "d": "Berekent de variantie die is gebaseerd op de hele populatie van geselecteerde databasegegevens", "ad": "is het celbereik dat als database is gedefinieerd. Een database is een lijst met verwante gegevens!is het label van de kolom tussen dubbele aanhalingstekens of een getal dat overeenkomt met de positie van de kolom in de lijst!is het celbereik dat de opgegeven voorwaarden bevat. Het bereik bevat een kolomlabel en een cel onder het label met een voorwaarde"}, "CHAR": {"a": "(getal)", "d": "Geeft als resultaat het teken dat hoort bij de opgegeven code voor de tekenset van uw computer", "ad": "is een getal tussen 1 en 255 dat aangeeft welk teken u wilt gebruiken"}, "CLEAN": {"a": "(tekst)", "d": "Verwijdert alle niet-afdrukbare tekens uit een tekst", "ad": "is de werkbladinformatie waaruit u niet-afdrukbare tekens wilt verwijderen"}, "CODE": {"a": "(tekst)", "d": "Geeft als resultaat de numerieke code voor het eerste teken in een tekenreeks voor de tekenset die door uw computer wordt gebruikt", "ad": "is de tekst waarvan u de numerieke code voor het eerste teken wilt weten"}, "CONCATENATE": {"a": "(tekst1; [tekst2]; ...)", "d": "Voegt verschillende tekenreeksen samen tot één tekenreeks", "ad": "zijn 1 tot 255 tekenreeksen die u wilt samenvoegen tot één tekenreeks. Dit kunnen tekenreeksen, getallen of verwijzingen naar één cel zijn"}, "CONCAT": {"a": "(tekst1; ...)", "d": "Voegt een lijst of bereik met teksttekenreeksen samen", "ad": "zijn tussen de 1 en 254 teksttekenreeksen of bereiken die moeten worden samengevoegd tot een enkele teksttekenreeks"}, "DOLLAR": {"a": "(getal; [decimalen])", "d": "Converteert een getal naar tekst op basis van de valutanotatie", "ad": "is een getal, een verwijzing naar een cel met een getal of een formule die resulteert in een getal!is het aantal decimalen rechts van de decimale komma. Indien noodzakelijk wordt het getal afgerond. Als dit wordt weggelaten, wordt uitgegaan van Decimalen = 2"}, "EXACT": {"a": "(tekst1; tekst2)", "d": "Controleert of twee tekenreeksen identiek zijn en geeft als resultaat WAAR of ONWAAR. Er wordt verschil gemaakt tussen hoofdletters en kleine letters", "ad": "is de eerste tekenreeks!is de tweede tekenreeks"}, "FIND": {"a": "(zoeken_tekst; in_tekst; [begin_getal])", "d": "Geeft als resultaat de beginpositie van een tekenreeks binnen een andere tekenreeks (er wordt onderscheid gemaakt tussen hoofdletters en kleine letters)", "ad": "is de tekst die u zoekt. Gebruik dubbele aanhalingstekens (een lege tekenreeks) om het eerste teken in In_tekst te zoeken. Jokertekens zijn niet toegestaan!is de tekst die de door u gezochte tekenreeks bevat!geeft de positie aan van het teken waar u wilt beginnen met zoeken. Het eerste teken in In_tekst is teken nummer 1. Als u dit weglaat, is Begin_getal = 1"}, "FINDB": {"a": "(zoeken_tekst; in_tekst; [begin_getal])", "d": "Wordt naar een tekenreeks binnen een andere tekenreeks gezocht en wordt als resultaat het nummer van de beginpositie van de eerste tekenreeks gegeven, is bedoeld voor talen met DBCS-tekenset (Double-Byte Character Set) - Japans, Chinees en Koreaans.", "ad": "is de tekst die u zoekt. Gebruik dubbele aanhalingstekens (een lege tekenreeks) om het eerste teken in In_tekst te zoeken. Jokertekens zijn niet toegestaan!is de tekst die de door u gezochte tekenreeks bevat!geeft de positie aan van het teken waar u wilt beginnen met zoeken. Het eerste teken in In_tekst is teken nummer 1. Als u dit weglaat, is Begin_getal = 1"}, "FIXED": {"a": "(getal; [decimalen]; [geen-punten])", "d": "Rondt een getal af op het opgegeven aantal decimalen en geeft het resultaat weer als tekst met of zonder komma's", "ad": "is het getal dat u wilt afronden en converteren naar tekst!is het aantal decimalen rechts van de decimale komma. Als u dit weglaat, is Decimalen = 2!is een logische waarde. Geen punten weergeven in de resulterende tekst = WAAR. Punten weergeven in de resulterende tekst = ONWAAR of weggelaten"}, "LEFT": {"a": "(tekst; [aantal-tekens])", "d": "Geeft als resultaat het aantal tekens vanaf het begin van een tekenreeks", "ad": "is de tekenreeks met de tekens die u wilt ophalen!geeft aan hoeveel tekens LINKS moet ophalen. Als dit wordt weggelaten, wordt uitgegaan van 1"}, "LEFTB": {"a": "(tekst; [aantal-tekens])", "d": "Geeft het eerste teken of de eerste tekens in een tekenreeks als resultaat, op basis van het aantal bytes dat u opgeeft, is bedoeld voor talen met DBCS-tekenset (Double-Byte Character Set) - Japans, Chinees en Koreaans", "ad": "is de tekenreeks met de tekens die u wilt ophalen!geeft aan hoeveel tekens LEFTB moet ophalen. Als dit wordt weggelaten, wordt uitgegaan van 1"}, "LEN": {"a": "(tekst)", "d": "Geeft als resultaat het aantal tekens in een tekenreeks", "ad": "is de tekst waarvan u de lengte wilt bepalen. Spaties tellen mee als tekens"}, "LENB": {"a": "(tekst)", "d": "Geeft als resultaat het aantal bytes dat is gebru<PERSON>t voor de tekens in een tekenreeks, is bedoeld voor talen met DBCS-tekenset (Double-Byte Character Set) - Japans, Chinees en Koreaans", "ad": "is de tekst waarvan u de lengte wilt bepalen. Spaties tellen mee als tekens"}, "LOWER": {"a": "(tekst)", "d": "Zet alle letters in een tekenreeks om in kleine letters", "ad": "is de tekst die u wilt omzetten in kleine letters. Tekens in Tekst die geen letters zijn, worden niet gewij<PERSON>d"}, "MID": {"a": "(tekst; begin_getal; aantal-tekens)", "d": "Geeft als resultaat het aantal tekens in het midden van een tekenreeks, beginnend op een opgegeven positie en met een opgegeven lengte", "ad": "is de tekenreeks waaruit u de tekens wilt ophalen!is de positie van het eerste teken dat u wilt ophalen uit de tekenreeks. Het eerste teken in Tekst is 1!geeft aan hoeveel tekens u wilt ophalen uit Tekst"}, "MIDB": {"a": "(tekst; begin_getal; aantal-tekens)", "d": "Geeft als resultaat een bepaald aantal tekens uit een tekenreeks, gerekend vanaf de opgegeven positie en op basis van het aantal opgegeven bytes, is bedoeld voor talen met DBCS-tekenset (Double-Byte Character Set) - Japans, Chinees en Koreaans", "ad": "is de tekenreeks waaruit u de tekens wilt ophalen!is de positie van het eerste teken dat u wilt ophalen uit de tekenreeks. Het eerste teken in Tekst is 1!geeft aan hoeveel tekens u wilt ophalen uit Tekst"}, "NUMBERVALUE": {"a": "(tekst; [decimaal_scheidingsteken]; [groep_scheidingsteken])", "d": "Converteert tekst naar getal, onafhan<PERSON><PERSON><PERSON> van landinstellingen", "ad": "is de tekenreeks voor het getal dat u wilt converteren!is het teken dat wordt gebruikt als decimaal scheidingsteken in de tekenreeks!is het teken dat wordt gebruikt als groepsscheidingsteken in de tekenreeks"}, "PROPER": {"a": "(tekst)", "d": "<PERSON>et de eerste letter van een tekenreeks om in een hoofdletter en converteert alle andere letters naar kleine letters", "ad": "is tekst tussen aanhalingste<PERSON>s, een formule die tekst als resultaat geeft of een verwijzing naar een cel met tekst die u gedeeltelijk wilt omzetten in hoofdletters"}, "REPLACE": {"a": "(oud_tekst; begin_getal; aantal-tekens; nieuw_tekst)", "d": "Vervangt een deel van een tekenreeks door een andere tekenreeks", "ad": "is tekst waarvan u een aantal tekens wilt vervangen!is de positie van het teken in oud_tekst dat u door nieuw_tekst wilt vervangen!is het aantal tekens in oud_tekst die u wilt vervangen!is de tekst waardoor u tekens in oud_tekst wilt vervangen"}, "REPLACEB": {"a": "(oud_tekst; begin_getal; aantal-tekens; nieuw_tekst)", "d": "Wordt een deel van een tekenreeks vervangen door een andere tekenreeks, op basis van het aantal bytes dat u opgeeft, is bedoeld voor talen met DBCS-tekenset (Double-Byte Character Set) - Japans, Chinees en Koreaans", "ad": "is tekst waarvan u een aantal tekens wilt vervangen!is de positie van het teken in oud_tekst dat u door nieuw_tekst wilt vervangen!is het aantal tekens in oud_tekst die u wilt vervangen!is de tekst waardoor u tekens in oud_tekst wilt vervangen"}, "REPT": {"a": "(tekst; aantal-malen)", "d": "<PERSON><PERSON><PERSON>t een tekst een aantal malen. Gebruik HERHALING om een cel een aantal keren te vullen met een tekenreeks", "ad": "is de tekst die u wilt herhalen!is een positief getal dat aangeeft hoe vaak u een tekst wilt herhalen"}, "RIGHT": {"a": "(tekst; [aantal-tekens])", "d": "Geeft als resultaat het opgegeven aantal tekens vanaf het einde van een tekenreeks", "ad": "is de tekenreeks met de tekens die u wilt ophalen!geeft aan hoeveel tekens u wilt ophalen. Als dit wordt weggelaten, wordt uitgegaan van 1"}, "RIGHTB": {"a": "(tekst; [aantal-tekens])", "d": "Geeft het laatste teken of de laatste tekens in een tekenreeks als resultaat, op basis van het aantal bytes dat u opgeeft, is bedoeld voor talen met DBCS-tekenset (Double-Byte Character Set) - Japans, Chinees en Koreaans", "ad": "is de tekenreeks met de tekens die u wilt ophalen!geeft aan hoeveel tekens u wilt ophalen. Als dit wordt weggelaten, wordt uitgegaan van 1"}, "SEARCH": {"a": "(zoeken_tekst; in_tekst; [begin_getal])", "d": "Geeft als resultaat de positie van het teken, lezend van links naar rechts, waar een bepaald teken of een bepaalde tekenreeks de eerste keer wordt gevonden (zonder onderscheid tussen hoofdletters en kleine letters)", "ad": "is de tekenreeks die u zoekt. U kunt de jokertekens ? en * gebruiken. Gebruik ~? en ~* om de tekens ? en * te zoeken!is de tekst die de door u gezochte tekenreeks bevat!geeft de positie, tellend van links naar rechts, aan in in_tekst van het teken waar u wilt beginnen met zoeken. Als dit wordt weggelaten, wordt 1 gebruikt"}, "SEARCHB": {"a": "(zoeken_tekst; in_tekst; [begin_getal])", "d": "Wordt naar een tekenreeks gezocht binnen een andere tekenreeks en wordt het nummer van de beginpositie van de eerste tekenreeks als resultaat gegeven, berekend vanaf het eerste teken van de tweede tekenreeks, is bedoeld voor talen met DBCS-tekenset (Double-Byte Character Set) - Japans, Chinees en Koreaans", "ad": "is de tekenreeks die u zoekt. U kunt de jokertekens ? en * gebruiken. Gebruik ~? en ~* om de tekens ? en * te zoeken!is de tekst die de door u gezochte tekenreeks bevat!geeft de positie, tellend van links naar rechts, aan in in_tekst van het teken waar u wilt beginnen met zoeken. Als dit wordt weggelaten, wordt 1 gebruikt"}, "SUBSTITUTE": {"a": "(tekst; oud_tekst; nieuw_tekst; [rang_getal])", "d": "<PERSON><PERSON><PERSON><PERSON> bestaande tekst door nieuwe tekst in een tekenreeks", "ad": "is de tekst of een verwijzing naar de cel met de tekst waarin u een aantal tekens wilt vervangen!is de tekst die u wilt vervangen. Als de hoofdletters en kleine letters in oud_tekst niet overeenkomt met die in tekst, wordt de tekst niet vervangen door SUBSTITUEREN!is de tekst waardoor u oud_tekst wilt vervangen!geeft aan welke oud_tekst u wilt vervangen door nieuw_tekst. Als dit wordt weggelaten, wordt elke oud_tekst vervangen"}, "T": {"a": "(waarde)", "d": "Controleert of een waarde tekst is. Als dit het geval is, wordt de tekst als resultaat gegeven. Als dit niet het geval is, worden er dubbele aanhalingstekens (lege tekst) als resultaat gegeven", "ad": "is de waarde die u wilt testen"}, "TEXT": {"a": "(waarde; notatie_tekst)", "d": "Converteert een waarde naar tekst in een specifieke getalnotatie", "ad": "is een getal, een formule die kan worden geëvalueerd als een getal of een verwijzing naar een cel die een getal bevat!is een getalnotatie in de vorm van tekst uit het vak Categorie op het tabblad Getal in het dialoogvenster Celeigenschappen"}, "TEXTJOIN": {"a": "(scheidingsteken; leeg_negeren; tekst1; ...)", "d": "Voegt een lijst of bereik met teksttekenreeks<PERSON> samen met be<PERSON><PERSON> van een scheidingsteken", "ad": "In te voegen teken of tekenreeks tussen elk tekstitem!indien WAAR(standaard): negeert lege cellen!zijn tussen de 1 en 252 teksttekenreeksen of bereiken die moeten worden samengevoegd"}, "TRIM": {"a": "(tekst)", "d": "Verwijdert de spaties uit een tekst, behalve de enkele spaties tussen woorden", "ad": "is de tekst waaruit u de spaties wilt verwijderen"}, "UNICHAR": {"a": "(getal)", "d": "Geeft als resultaat het Unicode-teken waarnaar wordt verwezen door de opgegeven numerieke waarde", "ad": "is het Unicode-getal waarmee een teken wordt aangeduid"}, "UNICODE": {"a": "(tekst)", "d": "Geeft als resultaat het getal (codepunt) dat overeen<PERSON><PERSON>t met het eerste teken van de tekst", "ad": "is het teken waarvoor u de Unicode-waarde wilt weten"}, "UPPER": {"a": "(tekst)", "d": "Zet een tekenreeks om in hoofdletters", "ad": "is de tekst die u wilt omzetten in hoofdletters. Dit is een verwijzing of een tekenreeks"}, "VALUE": {"a": "(tekst)", "d": "Converteert een tekenreeks die overeenkomt met een getal naar een getal", "ad": "is de tekst tussen aanhalingstekens of een verwijzing naar een cel met de tekst die u wilt converteren"}, "AVEDEV": {"a": "(getal1; [getal2]; ...)", "d": "Berekent het gemiddelde van de absolute deviaties van gegevenspunten ten opzichte van hun gemiddelde waarde. De argumenten kunnen getallen zijn of namen, matrices of verwij<PERSON>en die getallen bevatten", "ad": "zijn maximaal 255 argumenten waarvoor u het gemiddelde van de absolute deviatie wilt bepalen"}, "AVERAGE": {"a": "(getal1; [getal2]; ...)", "d": "Berekent het (rekenkundig) gemiddel<PERSON> van de argumenten. De argumenten kunnen getallen zijn of namen, matrices of verwi<PERSON><PERSON>en die getallen bevatten", "ad": "zijn maximaal 255 argumenten waarvan u het gemiddelde wilt berekenen"}, "AVERAGEA": {"a": "(waarde1; [waarde2]; ...)", "d": "Berekent het (meetkundige) gemiddelde van de argumenten. Tekst en ONWAAR worden geëvalueerd als 0, WAAR wordt geëvalueerd als 1. Argumenten kunnen getallen, namen, matrices en verwijzingen zijn", "ad": "zijn 1 tot 255 argumenten waarvan u het gemiddelde wilt bepalen"}, "AVERAGEIF": {"a": "(bereik; criteria; [gemiddelde_bereik])", "d": "Zoe<PERSON> het (rekenkundige) gemiddelde voor de cellen die worden gespecificeerd door een gegeven voorwaarde of criterium", "ad": "is het celbereik dat u wilt evalueren!is de voorwaarde of het criterium in de vorm van een getal, expressie of tekst waarmee wordt aangegeven welke cellen worden gebruikt om het gemiddelde te zoeken!zijn de feitelijke cellen die moeten worden gebruikt om het gemiddelde te bepalen. Indien weggelaten worden alle cellen in het bereik gebruikt"}, "AVERAGEIFS": {"a": "(gemiddelde_bereik; criteriumbereik; criteria; ...)", "d": "Zoe<PERSON> het (rekenkundige) gemiddelde voor de cellen die worden gespecificeerd door een gegeven set voorwaarden of criteria", "ad": "is het werkelijke aantal cellen dat wordt gebruikt om het gemiddelde te zoeken!is het celbereik dat u wilt evalueren voor de voorwaarde in kwestie!is de voorwaarde of het criterium in de vorm van een getal, expressie of tekst waarmee wordt aangegeven welke cellen worden gebruikt om het gemiddelde te zoeken"}, "BETADIST": {"a": "(x; alfa; bèta; [A]; [B])", "d": "Berekent de cumulatieve bèta-kansdichtheidsfunctie", "ad": "is de waarde tussen A en B waarop de functie geëvalueerd moet worden!is een parameter voor de verdeling. De parameter moet groter zijn dan 0!is een parameter voor de verdeling. De parameter moet groter zijn dan 0!is een optionele ondergrens voor het interval van x. Als dit wordt weggelaten, wordt uitgegaan van A = 0!is een optionele bovengrens voor het interval van x. Als deze waarde wordt weggelaten, wordt uitgegaan van B = 1"}, "BETAINV": {"a": "(kans; alfa; beta; [A]; [B])", "d": "Berekent de inverse van de cumulatieve bèta-kansdichtheidsfunctie (BETAVERD)", "ad": "is een kans die samenhangt met de bèta-verdeling!is een parameter voor de verdeling. De parameter moet groter zijn dan 0!is een parameter voor de verdeling. De parameter moet groter zijn dan 0!is een optionele ondergrens voor het interval van x. Als dit wordt weggelaten, wordt uitgegaan van A = 0!is een optionele bovengrens voor het interval van x. Als deze waarde wordt weggelaten, wordt uitgegaan van B = 1"}, "BETA.DIST": {"a": "(x; alfa; beta; cumulatief; [A]; [B])", "d": "Berekent de bètakansverdelingsfunctie", "ad": "is de waarde tussen A en B waarvoor de functie wordt geëvalueerd!is een parameter voor de verdeling en moet groter zijn dan 0!is een parameter voor de verdeling en moet groter zijn dan 0!is een logische waarde: gebruik WAAR voor de cumulatieve verdelingsfunctie en gebruik ONWAAR voor de kansdichtheidsfunctie!is een optionele ondergrens voor het interval van x. Als dit wordt weggelaten, wordt uitgegaan van A = 0!is een optionele bovengrens voor het interval van x. Als dit wordt weggelaten, wordt uitgegaan van B = 1"}, "BETA.INV": {"a": "(kans; alfa; beta; [A]; [B])", "d": "Berekent de inverse van de cumulatieve bètakansdichtheidsfunctie (BETA.VERD)", "ad": "is een kans die samenhangt met de bètaverdeling!is een parameter voor de verdeling en moet groter zijn dan 0!is een parameter voor de verdeling en moet groter zijn dan 0!is een optionele ondergrens voor het interval van x. Als dit wordt weggelaten, wordt uitgegaan van A = 0!is een optionele bovengrens voor het interval van x. Als dit wordt weggelaten, wordt uitgegaan van B = 1"}, "BINOMDIST": {"a": "(aantal-gunstig; experimenten; kans-gunstig; cumulatief)", "d": "Geeft als resultaat de binomiale verdeling", "ad": "is het aantal gunstige uitkomsten in een experiment!is het aantal onafhankelijke experimenten!is de kans op een gunstige uitkomst bij elk experiment!is een logische waarde. Gebruik WAAR voor de cumulatieve verdelingsfunctie en ONWAAR voor de kansdichtheidsfunctie"}, "BINOM.DIST": {"a": "(aantal-gunstig; experimenten; kans-gunstig; cumulatief)", "d": "Geeft als resultaat de binomiale verdeling", "ad": "is het aantal gunstige uitkomsten in een experiment!is het aantal onafhankelijke experimenten!is de kans op een gunstige uitkomst bij elk experiment!is een logische waarde. Gebruik WAAR voor de cumulatieve verdelingsfunctie en ONWAAR voor de kansdichtheidsfunctie"}, "BINOM.DIST.RANGE": {"a": "(proeven; kans_succes; getal_succes; [getal_succes2])", "d": "Geeft als resultaat de kans van een proefresultaat waarvoor een binomiale verdeling wordt gebruikt", "ad": "is het aantal onafhankelijke proeven!is de kans op succes voor elke proef!is het aantal successen in proeven!als dit is opgegeven, wordt met deze functie de kans geretourneerd dat het aantal geslaagde proeven tussen getal_succes en getal_succes2 ligt"}, "BINOM.INV": {"a": "(experimenten; kans-gunstig; alfa)", "d": "Berekent de kleinste waarde waarvoor de cumulatieve binomiale verdeling kleiner is dan of gelijk aan een criteriumwaarde", "ad": "is het aantal Bern<PERSON>lli-experimenten!is de kans op een gunstige uitkomst bij elk experiment, een getal van 0 tot en met 1!is de waarde van het criterium, een getal van 0 tot en met 1"}, "CHIDIST": {"a": "(x; vri<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Be<PERSON><PERSON> de rechtszijdige kans van de chi-kwadraatverdeling", "ad": "is de waarde waarop u de verdeling wilt evalueren. Dit is een niet-negatief getal!is het aantal vrijhe<PERSON>graden. Dit is een getal tussen 1 en 10^10, met 10^10 uitgesloten"}, "CHIINV": {"a": "(kans; vrijhe<PERSON>graden)", "d": "Berek<PERSON> de inverse van de rechtszijdige kans van de chi-kwadraatverdeling", "ad": "is een kans die samenhangt met de chi-kwadraatverdeling. Dit is een waarde van 0 tot en met 1!is het aantal vrijheidsgraden. Dit is een getal tussen 1 en 10^10, met 10^10 uitgesloten"}, "CHITEST": {"a": "(waarnemingen; verwacht)", "d": "Geeft het resultaat van de onafhankelijkheidstoets: de waarde van de chi-kwadraatverdeling voor de toetsingsgrootheid en de ingestelde vrijheidsgraden", "ad": "is het g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met de waarnemingen die u aan de hand van de verwachte waarden wilt toetsen!is het gege<PERSON><PERSON><PERSON><PERSON><PERSON> met de verhouding tussen enerzijds het product van de rijtotalen en kolomtotalen en anderzijds de eindtotalen"}, "CHISQ.DIST": {"a": "(x; vri<PERSON><PERSON><PERSON>n; cumulatief)", "d": "Berekent de linkszijdige kans van de chi-kwadraatverdeling", "ad": "is de waarde waarvoor u de verdeling wilt evalueren. Dit is een niet-negatief getal!is het aantal vrijheidsgraden. Dit is een getal tussen 1 en 10^10, exclusief 10^10!is een logische waarde die door de functie moet worden geretourneerd: de cumulatieve verdelingsfunctie = WAAR; de kansdichtheidsfunctie = ONWAAR"}, "CHISQ.DIST.RT": {"a": "(x; vri<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Be<PERSON><PERSON> de rechtszijdige kans van de chi-kwadraatverdeling", "ad": "is de waarde waarvoor u de verdeling wilt evalueren. Dit is een niet-negatief getal,!is het aantal vrijheidsgraden. Dit is een getal tussen 1 en 10^10, exclusief 10^10"}, "CHISQ.INV": {"a": "(kans; vrijhe<PERSON>graden)", "d": "Berekent de inverse van de linkszijdige kans van de chi-kwadraatverdeling", "ad": "is een kans die met de chi-kwadraatverdeling samenhangt. Dit is een waarde tussen 0 en 1 inclusief!is het aantal vrijheidsgraden. Dit is een getal tussen 1 en 10^10, exclusief 10^10"}, "CHISQ.INV.RT": {"a": "(kans; vrijhe<PERSON>graden)", "d": "Berek<PERSON> de inverse van de rechtszijdige kans van de chi-kwadraatverdeling", "ad": "is een kans die met de chi-kwadraatverdeling samenhangt. Dit is een waarde tussen 0 en 1 inclusief!is het aantal vrijheidsgraden. Dit is een getal tussen 1 en 10^10, exclusief 10^10"}, "CHISQ.TEST": {"a": "(waarnemingen; verwacht)", "d": "Geeft het resultaat van de onafhankelijkheidstoets: de waarde van de chi-kwadraatverdeling voor de toetsingsgrootheid en de ingestelde vrijheidsgraden", "ad": "is het g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> met de waarnemingen die u aan de hand van de verwachte waarden wilt toetsen!is het gege<PERSON><PERSON><PERSON><PERSON><PERSON> met de verhouding tussen enerzijds het product van de rijtotalen en kolomtotalen en anderzijds de eindtotalen"}, "CONFIDENCE": {"a": "(alfa; stand<PERSON><PERSON><PERSON>; grootte)", "d": "<PERSON><PERSON><PERSON> de betrouwbaarhe<PERSON>interval van een gemiddelde waarde voor de elementen van een populatie, met een normale verdeling", "ad": "is het significantieniveau op basis waar<PERSON> de betrouwbaarheidswaarde wordt berekend. Dit is een getal groter dan 0 en kleiner dan 1!is de standaarddeviatie voor het gegevensbereik binnen de populatie. Deze wordt verondersteld bekend te zijn. Standaarddev moet groter dan 0 zijn!is de groot<PERSON> van de steekproef"}, "CONFIDENCE.NORM": {"a": "(alfa; stand<PERSON><PERSON><PERSON>; grootte)", "d": "Berekent het betrouwbaarheidsinterval van een gemiddelde waarde voor de elementen van een populatie met een normale verdeling", "ad": "is het significantieniveau op basis waar<PERSON> de betrouwbaarheidswaarde wordt berekend. Dit is een getal groter dan 0 en kleiner dan 1!is de standaarddeviatie voor het gegevensbereik binnen de populatie. Deze wordt verondersteld bekend te zijn. Standaarddev moet groter dan 0 zijn!is de groot<PERSON> van de steekproef"}, "CONFIDENCE.T": {"a": "(alfa; stand<PERSON><PERSON><PERSON>; grootte)", "d": "<PERSON><PERSON><PERSON> de betrouwbaar<PERSON><PERSON>interval van een gemiddelde waarde voor de elementen van een populatie, met be<PERSON><PERSON> van een Student T-verdeling", "ad": "is het significantieniveau op basis waar<PERSON> de betrouwbaarheidswaarde wordt berekend. Dit is een getal groter dan 0 en kleiner dan 1!is de standaarddeviatie voor het gegevensbereik binnen de populatie. Deze wordt verondersteld bekend te zijn. Standaarddev moet groter dan 0 zijn!is de groot<PERSON> van de steekproef"}, "CORREL": {"a": "(matrix1; matrix2)", "d": "Berekent de correlatiecoëfficiënt van twee gegevensverzamelingen", "ad": "is een cel<PERSON><PERSON><PERSON> met waarden. Dit kunnen getallen zijn of namen, matrices of verwijzingen die getallen bevatten!is een tweede celber<PERSON><PERSON> met waarden. Dit kunnen getallen zijn of namen, matrices of verwijzingen die getallen bevatten"}, "COUNT": {"a": "(waarde1; [waarde2]; ...)", "d": "Telt het aantal cellen in een bereik dat getallen bevat", "ad": "zijn maximaal 255 argumenten die verschillende typen gegevens kunnen bevatten of ernaar verwijzen. <PERSON><PERSON> de getallen worden geteld"}, "COUNTA": {"a": "(waarde1; [waarde2]; ...)", "d": "Telt het aantal niet-lege cellen in een bereik", "ad": "zijn maximaal 255 argumenten met de waarden en cellen die u wilt tellen. Waarden kunnen van elk type informatie zijn"}, "COUNTBLANK": {"a": "(bereik)", "d": "Telt het aantal lege cellen in een bereik", "ad": "is het bereik waarin u de lege cellen wilt tellen"}, "COUNTIF": {"a": "(bereik; criterium)", "d": "Telt het aantal niet-lege cellen in een bereik die voldoen aan het opgegeven criterium", "ad": "is het celber<PERSON>k waarin u wilt tellen hoeveel niet-lege cellen voldoen aan het criterium!is de voorwaarde die bepaalt welke cellen worden geteld. Dit kan een getal, een expressie of tekst zijn"}, "COUNTIFS": {"a": "(criteriumbereik; criteria; ...)", "d": "Telt het aantal cellen dat wordt gespecificeerd door een gegeven set voorwaarden of criteria", "ad": "is het celbereik dat u wilt evalueren voor de voorwaarde in kwestie!is de voorwaarde in de vorm van een getal, expressie of tekst waarmee wordt aangegeven welke cellen worden geteld"}, "COVAR": {"a": "(matrix1; matrix2)", "d": "<PERSON><PERSON>ent de covariantie, het gemid<PERSON><PERSON> van de producten van deviaties voor ieder paar gegevenspunten in twee gegevenssets", "ad": "is het e<PERSON><PERSON> c<PERSON><PERSON><PERSON><PERSON> met gehele getallen. Dit moeten getallen zijn of namen, matrices of verwijzingen die getallen bevatten!is het tweede c<PERSON><PERSON><PERSON><PERSON> met gehele getallen. Dit moeten getallen zijn of matrices of verwijzingen die getallen bevatten"}, "COVARIANCE.P": {"a": "(matrix1; matrix2)", "d": "Berekent de covariantie van de populatie, het gemid<PERSON><PERSON> van de producten van deviaties voor ieder paar gegevenspunten in twee gegevenssets", "ad": "is het e<PERSON><PERSON> c<PERSON><PERSON><PERSON><PERSON> met gehele getallen. Dit kunnen getallen zijn of matrices of verwijzingen die getallen bevatten!is het tweede cel<PERSON><PERSON><PERSON> met gehele getallen. Dit kunnen getallen zijn of matrices of verwijzingen die getallen bevatten"}, "COVARIANCE.S": {"a": "(matrix1; matrix2)", "d": "Berekent de covariantie voor een steekproef, het gemid<PERSON><PERSON> van de producten van deviaties voor ieder paar gegevenspunten in twee gegevenssets", "ad": "is het e<PERSON><PERSON> c<PERSON><PERSON><PERSON><PERSON> met gehele getallen. Dit kunnen getallen zijn of namen, matrices of verwijzingen die getallen bevatten!is het tweede cel<PERSON><PERSON><PERSON> met gehele getallen. Dit kunnen getallen zijn of matrices of verwijzingen die getallen bevatten"}, "CRITBINOM": {"a": "(experimenten; kans-gunstig; alfa)", "d": "Berekent de kleinste waarde waarvoor de cumulatieve binomiale verdeling kleiner is dan of gelijk aan een criteriumwaarde", "ad": "is het aantal Bern<PERSON>lli-experimenten!is de kans op een gunstige uitkomst bij elk experiment, een getal van 0 tot en met 1!is de waarde van het criterium, een getal van 0 tot en met 1"}, "DEVSQ": {"a": "(getal1; [getal2]; ...)", "d": "Berek<PERSON> de som van de kwadraten van de deviaties van gegevenspunten ten opzichte van het gemiddelde van de steekproef", "ad": "zijn 1 tot 255 argumenten of een matrix of een verwijzing naar een matrix met getallen die u wilt gebruiken in de berekening met DEV.KWAD"}, "EXPONDIST": {"a": "(x; lambda; cumulatief)", "d": "Geeft als resultaat de exponentiële verdeling", "ad": "is de waarde van de functie. Dit is een niet-negatief getal!is de waarde van de parameter. Dit is een positief getal!is een logische waarde die bepaalt welke functie als resultaat wordt gegeven: WAAR = de cumulatieve verdelingsfunctie, ONWAAR = de kansdichtheidsfunctie"}, "EXPON.DIST": {"a": "(x; lambda; cumulatief)", "d": "Geeft als resultaat de exponentiële verdeling", "ad": "is de waarde van de functie. Dit is een niet-negatief getal!is de waarde van de parameter. Dit is een positief getal!is een logische waarde die bepaalt welke functie als resultaat wordt gegeven: WAAR = de cumulatieve verdelingsfunctie, ONWAAR = de kansdichtheidsfunctie"}, "FDIST": {"a": "(x; vrijheidsgraden1; vrijheidsgraden2)", "d": "Geeft als resultaat de (rechtszijdige) F-verdeling (de graad van verscheidenheid) voor twee gegevensverzamelingen", "ad": "is de waarde waarop de functie geëvalueerd moet worden. Dit is een niet-negatief getal!is het aantal vrijheidsgraden van de teller. Dit is een getal tussen 1 en 10^10, met 10^10 uitgesloten!is het aantal vrijheidsgraden van de noemer. Dit is een getal tussen 1 en 10^10, met 10^10 uitgesloten"}, "FINV": {"a": "(kans; vrijheidsgraden1; vrijheidsgraden2)", "d": "Berekent de inverse van de (rechtszijdige) F-verdeling: als p = F.VERDELING(x,...), is F.INVERSE(p,...) = x", "ad": "is de kans die samenhangt met de cumulatieve F-verdeling. Dit is een getal van 0 tot en met 1!is het aantal vrijheidsgraden van de teller. Dit is een getal tussen 1 en 10^10, met 10^10 uitgesloten!is het aantal vrijheidsgraden van de noemer. Dit is een getal tussen 1 en 10^10, met 10^10 uitgesloten"}, "FTEST": {"a": "(matrix1; matrix2)", "d": "Geeft het resultaat van een F-toets, de tweezijdige kans dat de varianties in matrix1 en matrix2 niet significant verschillen", "ad": "is de eerste matrix of het eerste gegevensbereik. Dit kunnen getallen zijn of namen, matrices of verwijzingen die getallen bevatten (lege cellen worden genegeerd)!is de tweede matrix of het tweede gegevensbereik. Dit kunnen getallen zijn of namen, matrices of verwijzingen die getallen bevatten (lege cellen worden genegeerd)"}, "F.DIST": {"a": "(x; vrijheidsgraden1; vrijheidsgraden2; cumulatief)", "d": "Geeft als resultaat de (linkszijdige) F-kansverdeling (de graad van verscheidenheid) voor twee gegevenssets", "ad": "is de waarde waarvoor de functie moet worden geëvalueerd. Dit is een niet-negatief getal!is het aantal vrijheidsgraden van de teller. Dit is een getal tussen 1 en 10^10, exclusief 10^10!is het aantal vrijheidsgraden van de noemer. Dit is een getal tussen 1 en 10^10, exclusief 10^10!is een logische waarde die door de functie moet worden geretourneerd: de cumulatieve verdelingsfunctie = WAAR; de kansdichtheidsfunctie = ONWAAR"}, "F.DIST.RT": {"a": "(x; vrijheidsgraden1; vrijheidsgraden2)", "d": "Geeft als resultaat de (rechtszijdige) F-kansverdeling (de graad van verscheidenheid) voor twee gegevenssets", "ad": "is de waarde waarvoor de functie moet worden geëvalueerd. Dit is een niet-negatief getal!is het aantal vrijheidsgraden van de teller. Dit is een getal tussen 1 en 10^10, exclusief 10^10!is het aantal vrijheidsgraden van de noemer. Dit is een getal tussen 1 en 10^10, exclusief 10^10"}, "F.INV": {"a": "(kans; vrijheidsgraden1; vrijheidsgraden2)", "d": "Berekent de inverse van de (linkszijdige) F-kansverdeling: als p = F.VERDELING(x,...), dan F.INVERSE(p,...) = x", "ad": "is een kans die samenhangt met de cumulatieve F-verdeling. Dit is een getal tussen 0 en 1 inclusief!is het aantal vrijheidsgraden van de teller. Dit is een getal tussen 1 en 10^10, exclusief 10^10!is het aantal vrijheidsgraden van de noemer. Dit is een getal tussen 1 en 10^10, exclusief 10^10"}, "F.INV.RT": {"a": "(kans; vrijheidsgraden1; vrijheidsgraden2)", "d": "Berekent de inverse van de (rechtszijdige) F-kansverdeling: als p = F.VERD.RECHTS(x,...), dan F.INV.RECHTS(p,...) = x", "ad": "is een kans die samenhangt met de cumulatieve F-verdeling. Dit is een getal tussen 0 en 1 inclusief!is het aantal vrijheidsgraden van de teller. Dit is een getal tussen 1 en 10^10, exclusief 10^10!is het aantal vrijheidsgraden van de noemer. Dit is een getal tussen 1 en 10^10, exclusief 10^10"}, "F.TEST": {"a": "(matrix1; matrix2)", "d": "Geeft het resultaat van een F-toets, de tweezijdige kans dat de varianties in matrix1 en matrix2 niet significant verschillen", "ad": "is de eerste matrix of het eerste gegevensbereik. Dit kunnen getallen zijn of namen, matrices of verwijzingen die getallen bevatten (lege cellen worden genegeerd)!is de tweede matrix of het tweede gegevensbereik. Dit kunnen getallen zijn of namen, matrices of verwijzingen die getallen bevatten (lege cellen worden genegeerd)"}, "FISHER": {"a": "(x)", "d": "<PERSON><PERSON><PERSON> de Fisher-transformatie", "ad": "is het getal waarvoor u de Fisher-transformatie wilt berekenen. Dit is een getal tussen -1 en 1, met -1 en 1 uitgesloten"}, "FISHERINV": {"a": "(y)", "d": "Berekent de inverse van de Fisher-transformatie: als y=FISHER(x), is FISHER.INV(y) = x", "ad": "is de waarde waarop u de inverse van de transformatie wilt uitvoeren"}, "FORECAST": {"a": "(x; known_ys; known_xs)", "d": "Berekent of voorspelt een toekomstige waarde langs een lineaire trend op basis van bestaande waarden", "ad": "is de numerieke waarde voor het gegevenspunt waarvan u de waarde wilt voorspellen!is de afhankelijke matrix of het afhankelijke bereik met numerieke gegevens!is de onafhankelijke matrix of het onafhankelijke bereik met numerieke gegevens. De afwijking van Bekende_x's mag niet nul zijn"}, "FORECAST.ETS": {"a": "(target_date; values; timeline; [seasonality]; [data_completion]; [aggregation])", "d": "Retourneert de voorspelde waarde voor een specifieke doeldatum in de toekomst met de methode voor exponentieel vloeiend maken.", "ad": "is het gege<PERSON><PERSON>unt waarvoor in Spreadsheet Editor een waarde wordt voorspeld. Hiermee moet het waardepatroon op de tijdlijn worden doorgevoerd.!is de matrix of het bereik met numerieke gegevens die u voorspelt.!is de onafhankelijke matrix of het onafhankelijke bereik met numerieke gegevens. De datums op de tijdlijn moeten een consistent interval hebben en mogen niet nul zijn.!is een optionele numerieke waarde die de lengte van het seizoensgebonden patroon aangeeft. De standaardwaarde 1 geeft aan dat seizoensgebondenheid automatisch wordt gedetecteerd.!is een optionele waarde voor het verwerken van ontbrekende waarden. De standaardwaarde 1 vervangt ontbrekende waarden door nabijgelegen waarden en 0 vervangt ontbrekende waarden door nullen.!is een optionele numerieke waarde die meerdere waarden met de<PERSON><PERSON><PERSON> tijdstempel samenvoegt. Indien leeg, dan wordt in Spreadsheet Editor de gemiddelde waarde berekend."}, "FORECAST.ETS.CONFINT": {"a": "(target_date; values; timeline; [confidence_level]; [seasonality]; [data_completion]; [aggregation])", "d": "Hier<PERSON> wordt een betrouwbaarheidsinterval geretourneerd voor de voorspelde waarde op de opgegeven doeldatum.", "ad": "is het gegevenspunt waarvoor in Spreadsheet Editor een waarde wordt voorspeld. Hiermee moet het waardepatroon op de tijdlijn worden doorgevoerd.!is de matrix of het bereik met numerieke gegevens die u voorspelt.!is de onafhankelijke matrix of het onafhankelijke bereik met numerieke gegevens. De datums op de tijdlijn moeten een consistent interval hebben en mogen niet nul zijn.!is een getal tussen 0 en 1 dat de betrouwbaarheidswaarde van het berekende betrouwbaarheidsinterval aangeeft. De standaardwaarde is .95.!is een optionele numerieke waarde die de lengte van het seizoensgebonden patroon aangeeft. De standaardwaarde 1 geeft aan dat seizoensgebondenheid automatisch wordt gedetecteerd.!is een optionele waarde voor het verwerken van ontbrekende waarden. De standaardwaarde 1 vervangt ontbrekende waarden door nabijgelegen waarden en 0 vervangt ontbrekende waarden door nullen.!is een optionele numerieke waarde die meerdere waarden met dezelfde tijdstempel samenvoegt. Indien leeg, dan wordt in Spreadsheet Editor de gemiddelde waarde berekend."}, "FORECAST.ETS.SEASONALITY": {"a": "(values; timeline; [data_completion]; [aggregation])", "d": "Retourneert de lengte van het herhaalde patroon dat in applicatie wordt gedetecteerd voor de opgegeven tijdreeks.", "ad": "is de matrix of het bereik met numerieke gegevens die u voorspelt.!is de onafhankelijke matrix of het onafhankelijke bereik met numerieke gegevens. De datums op de tijdlijn moeten een consistent interval hebben en mogen niet nul zijn.!is een optionele waarde voor het verwerken van ontbrekende waarden. De standaardwaarde 1 vervangt ontbrekende waarden door nabijgelegen waarden en 0 vervangt ontbrekende waarden door nullen!.is een optionele numerieke waarde die meerdere waarden met dezelfde tijdstempel samenvoegt. Indien leeg, dan wordt in Spreadsheet Editor de gemiddelde waarde berekend."}, "FORECAST.ETS.STAT": {"a": "(values; timeline; statistic_type; [seasonality]; [data_completion]; [aggregation])", "d": "Retourneert de aangevraagde statistische gegevens voor de voorspelling.", "ad": "is de matrix of het bere<PERSON> met numerieke gegevens die u voorspelt.!is de onafhankelijke matrix of het onafhankelijke bereik met numerieke gegevens. De datums op de tijdlijn moeten een consistent interval hebben en mogen niet nul zijn.!is een getal tussen 1 en 8, dat aangeeft welke statistische gegevens in Spreadsheet Editor worden geretourneerd voor de berekende voorspelling.!is een optionele numerieke waarde die de lengte van het seizoensgebonden patroon aangeeft. De standaardwaarde 1 geeft aan dat seizoensgebondenheid automatisch wordt gedetecteerd.!is een optionele waarde voor het verwerken van ontbrekende waarden. De standaardwaarde 1 vervangt ontbrekende waarden door nabijgelegen waarden en 0 vervangt ontbrekende waarden door nullen.!is een optionele numerieke waarde die meerdere waarden met de<PERSON><PERSON>de tijdstempel samenvoegt. Indien leeg, dan wordt in Spreadsheet Editor de gemiddelde waarde berekend."}, "FORECAST.LINEAR": {"a": "(x; known_ys; known_xs)", "d": "Berekent of voorspelt aan de hand van bestaande waarden een toekomstige waarde volgens een lineaire trend", "ad": "is de numerieke waarde voor het gegevenspunt waarvan u de waarde wilt voorspellen!is de afhankelijke matrix of het afhankelijke bereik met numerieke gegevens!is de onafhankelijke matrix of het onafhankelijke bereik met numerieke gegevens. De variantie van waarden voor x-bekend mag niet nul zijn"}, "FREQUENCY": {"a": "(gegevensmatrix; interval_verw)", "d": "<PERSON><PERSON><PERSON> hoe vaak waarden voorkomen in een waardebereik en geeft als resultaat een verticale matrix met getallen met een element meer dan interval_verw", "ad": "is een matrix of een verwijzing naar een verzameling waarden waarvan u de frequentie wilt berekenen (lege cellen en tekst worden genegeerd)!is een matrix of een verwijzing naar de intervallen waarin u de waarden uit de gegevensmatrix wilt verdelen"}, "GAMMA": {"a": "(x)", "d": "Geeft als resultaat de waarde van de functie Gamma", "ad": "is de waarde waarvoor u Gamma wilt berekenen"}, "GAMMADIST": {"a": "(x; alfa; bèta; cumulatief)", "d": "Geeft als resultaat de gamma-verdeling", "ad": "is de waarde waarop u de verdeling wilt evalueren. Dit is een niet-negatief getal!is een parameter voor de verdeling. Dit is een positief getal!is een parameter voor de verdeling. Dit is een positief getal. Als bèta 1 is, geeft GAMMA.VERD als resultaat de standaard-gamma-verdeling!is een logische waarde. Gebruik WAAR voor de cumulatieve verdelingsfunctie en ONWAAR voor de kansdichtheidsfunctie"}, "GAMMA.DIST": {"a": "(x; alfa; beta; cumulatief)", "d": "Geeft als resultaat de gamma-verdeling", "ad": "is de waarde waarop u de verdeling wilt evalueren. Dit is een niet-negatief getal!is een parameter voor de verdeling. Dit is een positief getal!is een parameter voor de verdeling. Dit is een positief getal. Als beta 1 is, geeft GAMMA.VERD.N als resultaat de standaard-gamma-verdeling!is een logische waarde. Gebruik WAAR voor de cumulatieve verdelingsfunctie en ONWAAR voor de kansdichtheidsfunctie"}, "GAMMAINV": {"a": "(kans; alfa; bèta)", "d": "Berekent de inverse van de cumulatieve gamma-verdeling: als p = GAMMA.VERD(x,...), is GAMMA.INV(p,...) = x", "ad": "is de kans die samenhangt met een gamma-verdeling. Dit is een getal tussen 0 en 1, met 0 en 1 ingesloten!is een parameter voor de verdeling. Dit is een positief getal!is een parameter voor de verdeling. Dit is een positief getal. Als bèta 1 is, geeft GAMMA.INV als resultaat de inverse van de standaard gamma-verdeling"}, "GAMMA.INV": {"a": "(kans; alfa; beta)", "d": "Berekent de inverse van de cumulatieve gamma-verdeling: als p = GAMMA.VERD.N(x,...), is GAMMA.INV.N(p,...) = x", "ad": "is de kans die samenhangt met een gamma-verdeling. Dit is een getal tussen 0 en 1, met 0 en 1 ingesloten!is een parameter voor de verdeling. Dit is een positief getal!is een parameter voor de verdeling. Dit is een positief getal. Als beta 1 is, geeft GAMMA.INV als resultaat de inverse van de standaard gamma-verdeling"}, "GAMMALN": {"a": "(x)", "d": "Berekent de natuurlijke logaritme van de gamma-functie", "ad": "is de waarde waarvoor u GAMMA.LN wilt berekenen. Dit is een positief getal"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Berekent de natuurlijke logaritme van de gammafunctie", "ad": "is de waarde waarvoor u GAMMA.LN.NAUWKEURIG wilt berekenen. Dit is een positief getal"}, "GAUSS": {"a": "(x)", "d": "Geeft als resultaat 0,5 minder dan de normale cumulatieve standaardverdeling", "ad": "is de waarde waarvoor u de verdeling wilt berekenen"}, "GEOMEAN": {"a": "(getal1; [getal2]; ...)", "d": "Berekent het meetkundige gemiddelde van positieve numerieke gegevens in een matrix of bereik", "ad": "zijn 1 tot 255 getallen of namen, matrices of verwi<PERSON><PERSON>en die getallen bevatten waarvan u het meetkundige gemiddelde wilt berekenen"}, "GROWTH": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "Geeft als resultaat getallen in een exponentiële groeitrend die overeenkomen met bekende gegevenspunten", "ad": "is de verzameling y-waarden die al bekend zijn uit y = b*m^x. Dit is een matrix of bereik met positieve getallen!is een optionele verzameling x-waarden die wellicht al bekend zijn uit y = b*m^x. Dit is een matrix of bereik met dezelfde grootte als y-bekend!zijn de nieuwe x-waarden waarvoor GROEI als resultaat de bijbehorende y-waarden moet geven!is een logische waarde. Als Const = WAAR, wordt de constante b normaal berekend. Als Const = ONWAAR of wordt weggelaten, moet b gelijk zijn aan 1"}, "HARMEAN": {"a": "(getal1; [getal2]; ...)", "d": "Berekent het harmonische gemiddelde van een gegevensverzameling met positieve getallen: de reciproque waarde van het meetkundige gemiddelde van reciproque waarden", "ad": "zijn 1 tot 255 getallen of namen, matrices of verwi<PERSON><PERSON>en die getallen bevatten waarvan u het harmonische gemiddelde wilt berekenen"}, "HYPGEOM.DIST": {"a": "(steekproef-gunstig; grootte-steekproef; populatie-gunstig; grootte-populatie; cumulatief)", "d": "Geeft als resultaat de hypergeometrische verdeling", "ad": "is het aantal gunstige uitkomsten in de steekproef!is de grootte van de steekproef!is het aantal gunstige uitkomsten in de populatie!is de grootte van de populatie!is een logische waarde: gebruik WAAR voor de cumulatieve verdelingsfunctie en gebruik ONWAAR voor de kansdichtheidsfunctie"}, "HYPGEOMDIST": {"a": "(steekproef-gunstig; grootte-steekproef; populatie-gunstig; grootte-populatie)", "d": "Geeft als resultaat de hypergeometrische verdeling", "ad": "Is het aantal gunstige uitkomsten in de steekproef!Is de grootte van de steekproef!Is het aantal gunstige uitkomsten in de populatie!Is de grootte van de populatie"}, "INTERCEPT": {"a": "(known_ys; known_xs)", "d": "<PERSON><PERSON><PERSON> het snijpunt van een lijn met de y-as aan de hand van een optimale regressielijn die wordt getrokken door de bekende x-waarden en y-waarden", "ad": "is de afhankelijke verzameling van waarnemingen of gegevens. Dit kunnen getallen zijn of namen, matrices of verwijzingen die getallen bevatten!is de onafhankelijke verzameling van waarnemingen of gegevens. Dit kunnen getallen zijn of namen, matrices of verwijzingen die getallen bevatten"}, "KURT": {"a": "(getal1; [getal2]; ...)", "d": "Berekent de kurtosis van een gegevensverzameling", "ad": "zijn 1 tot 255 getallen of namen, matrices of verwi<PERSON><PERSON>en die getallen bevatten waarvoor u de kurtosis wilt berekenen"}, "LARGE": {"a": "(matrix; k)", "d": "Berekent de op k-1 na grootste waarde in een gegevensbereik, bijvoorbeeld het vijfde grootste getal", "ad": "is de matrix of het gegevensbereik waarvoor u de op k-1 na grootste waarde wilt bepalen!is de positie (vanaf grootste waarde geteld) in de matrix die of het celbereik met gegevens dat als resultaat moet worden gegeven"}, "LINEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "Geeft als resultaat statistieken die een lineaire trend beschrijven en overeenkomen met bekende gegevenspunten. De lijn wordt berekend met de kleinste-kwadratenmethode", "ad": "is de verzameling y-waarden die u al bekend is uit y = mx + b!is een optionele verzameling van x-waarden die wellicht al bekend is uit y = mx + b!is een logische waarde. Als Const = WAAR of wordt weggelaten, wordt de constante b normaal berekend. Als Const = ONWAAR, moet b gelijk zijn aan 0!is een logische waarde: WAAR = geef als resultaat aanvullende regressiegrootheden, ONWAAR of weggelaten = geef als resultaat m-coëfficiënten en de constante b"}, "LOGEST": {"a": "(known_ys; [known_xs]; [const]; [stats])", "d": "Geeft als resultaat statistieken die een exponentiële curve beschrijven die past bij de gegevenspunten", "ad": "is de verzameling y-waarden die al bekend is uit y = b*m^x!is een optionele verzameling x-waarden die wellicht al bekend is uit y = b*m^x!is een logische waarde. Als Const = WAAR of wordt weggelaten, wordt de constante b normaal berekend, als Const = ONWAAR, is de constante b gelijk aan 1!is een logische waarde: WAAR = geef als resultaat aanvullende regressiestatistieken, ONWAAR of weggelaten = geef als resultaat m-coëfficiënten en de constante b"}, "LOGINV": {"a": "(kans; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>)", "d": "Berekent de inverse van de logaritmische normale verdeling van x, waar<PERSON>j ln(x) normaal wordt verdeeld met de parameters Gemiddelde en Standaarddev", "ad": "is een getal van 0 tot en met 1 voor de kans die samenhangt met de logaritmische normale verdeling!is de gemiddelde ln(x)!is de standaarddeviatie van ln(x). Dit is een positief getal"}, "LOGNORM.DIST": {"a": "(x; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>; cumulatief)", "d": "Geeft als resultaat de logaritmische normale verdeling van x, waarbij ln(x) normaal is verdeeld met de parameters Gemiddelde en Standaarddev", "ad": "is de waarde waarvoor de functie moet worden geëvalueerd. Dit is een positief getal!is het gemid<PERSON><PERSON> van ln(x)!is de standaarddeviatie van ln(x). Dit is een positief getal!is een logische waarde: gebruik WAAR voor de cumulatieve verdelingsfunctie en gebruik ONWAAR voor de kansdichtheidsfunctie"}, "LOGNORM.INV": {"a": "(kans; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>)", "d": "Berekent de inverse van de logaritmische normale verdeling van x, waar<PERSON>j ln(x) normaal wordt verdeeld met de parameters Gemiddelde en Standaarddev", "ad": "is een getal van 0 tot en met 1 voor de kans die samenhangt met de logaritmische normale verdeling!is de gemiddelde ln(x)!is de standaarddeviatie van ln(x). Dit is een positief getal."}, "LOGNORMDIST": {"a": "(x; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>)", "d": "Geeft als resultaat de logaritmische normale verdeling van x, waar<PERSON>j ln(x) normaal wordt verdeeld met de parameters Gemiddelde en Standaarddev", "ad": "is de waarde waarop de functie geëvalueerd moet worden. Dit is een positief getal.!is de gemiddelde ln(x)!is de standaarddeviatie van ln(x). Dit is een positief getal"}, "MAX": {"a": "(getal1; [getal2]; ...)", "d": "Geeft als resultaat de grootste waarde in een lijst met argumenten. Logische waarden en tekst worden genegeerd", "ad": "zijn maximaal 255 getallen, lege cellen, logische waarden of tekstgetallen waarvan u het maximum wilt zoeken"}, "MAXA": {"a": "(waarde1; [waarde2]; ...)", "d": "Geeft als resultaat de grootste waarde in een verzameling waarden. Logische waarden en tekst worden niet genegeerd", "ad": "zijn 1 tot 255 getallen, lege cellen, logische waarden of getallen met een tekstindeling waarvan u de grootste waarde wilt bepalen"}, "MAXIFS": {"a": "(maximumbereik; criteriabereik; criteria; ...)", "d": "Retourneert de maximumwaarde tussen cellen die wordt bepaald door een gegeven set voorwaarden of criteria", "ad": "de cellen waarin de maximumwaarde wordt bepaald!is het cellenbereik dat u wilt evalueren voor de specifieke voorwaarde!is de voorwaarde of het criterium waarmee wordt bepaald welke cellen worden opgenomen bij het bepalen van de maximumwaarde. Dit kan een getal, een expressie of tekst zijn"}, "MEDIAN": {"a": "(getal1; [getal2]; ...)", "d": "<PERSON><PERSON><PERSON> de mediaan (het getal in het midden van een set) van de gegeven getallen", "ad": "zijn maximaal 255 getallen of namen, matrices of verwi<PERSON><PERSON>en die getallen bevatten waarvan u de mediaan wilt bepalen"}, "MIN": {"a": "(getal1; [getal2]; ...)", "d": "Geeft als resultaat het kleinste getal in een lijst met waarden. Logische waarden en tekst worden genegeerd", "ad": "zijn maximaal 255 getallen, lege cellen, logische waarden of tekstgetallen waarvan u het minimum wilt zoeken"}, "MINA": {"a": "(waarde1; [waarde2]; ...)", "d": "Geeft als resultaat de kleinste waarde in een lijst met argumenten. Logische waarden en tekst worden niet genegeerd", "ad": "zijn 1 tot 255 getallen, lege cellen, logische waarden of getallen met een tekstindeling waarvan u de kleinste waarde wilt bepalen"}, "MINIFS": {"a": "(minimumbereik; criteriabereik; criteria; ...)", "d": "Retourneert de minimumwaarde tussen cellen die wordt bepaald door een gegeven set voorwaarden of criteria", "ad": "de cellen waarin de minimumwaarde wordt bepaald!is het cellenbereik dat u wilt evalueren voor de specifieke voorwaarde!is de voorwaarde of het criterium waarmee wordt bepaald welke cellen worden opgenomen bij het bepalen van de minimumwaarde. Dit kan een getal, een expressie of tekst zijn"}, "MODE": {"a": "(getal1; [getal2]; ...)", "d": "Geeft als resultaat de meest voorkomende (repeterende) waarde in een matrix of bereik met gegevens", "ad": "zijn 1 tot 255 getallen of namen, matrices of verwi<PERSON><PERSON>en die getallen bevatten waarvan u de modus wilt bepalen"}, "MODE.MULT": {"a": "(getal1; [getal2]; ...)", "d": "Berekent een verticale matrix van de vaakst voorkomende, of herhaalde waarden in een matrix of gegevensbereik. Voor een horizontale matrix gebruikt u =TRANSPONEREN(MODUS.VERM(getal1,getal2,...))", "ad": "zijn 1 tot 255 getallen of namen, matrices of verwi<PERSON><PERSON>en die getallen bevatten waarvoor u de modus wilt berekenen"}, "MODE.SNGL": {"a": "(getal1; [getal2]; ...)", "d": "Geeft als resultaat de meest voorkomende (repeterende) waarde in een matrix of bereik met gegevens", "ad": "zijn 1 tot 255 getallen of namen, matrices of verwi<PERSON><PERSON>en die getallen bevatten waarvan u de modus wilt bepalen"}, "NEGBINOM.DIST": {"a": "(aantal-ongunstig; aantal-gunstig; kans-gunstig; cumulatief)", "d": "Geeft als resultaat de negatieve binomiaalverdeling, de kans dat er Aantal-ongunstig ongunstige uitkomsten zijn voor Aantal-gunstig gunstige uitkomsten, met een kans van Kans-gunstig op een gunstige uitkomst", "ad": "is het aantal ongunstige uitkomsten!is het minimum aantal gunstige uitkomsten!is een getal tussen 0 en 1 voor de kans op een gunstige uitkomst!is een logische waarde: gebruik WAAR voor de cumulatieve verdelingsfunctie en gebruik ONWAAR voor de kansdichtheidsfunctie"}, "NEGBINOMDIST": {"a": "(aantal-ongunstig; aantal-gunstig; kans-gunstig)", "d": "Geeft als resultaat de negatieve binomiaalverdeling, de kans dat er Aantal-ongunstig ongunstige uitkomsten zijn voor Aantal-gunstig gunstige uitkomsten, met een kans van Kans-gunstig op een gunstige uitkomst", "ad": "is het aantal ongunstige uitkomsten!is het minimum aantal gunstige uitkomsten!is een getal van 0 tot en met 1 voor de kans op een gunstige uitkomst"}, "NORM.DIST": {"a": "(x; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>; cumulatieve)", "d": "Resulteert in de normale verdeling voor het opgegeven gemiddelde en de standaarddeviatie", "ad": "is de waarde waarvoor u de verdeling wilt berekenen!is het rekenkundige gemiddelde van de verdeling!is de standaarddeviatie van de verdeling, een positief getal!is een logische waarde. Gebruik WAAR voor de cumulatieve verdelingsfunctie en gebruik ONWAAR voor de kansdichtheidsfunctie"}, "NORMDIST": {"a": "(x; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>; cumulatief)", "d": "Geeft als resultaat de cumulatieve normale verdeling van het opgegeven gemiddelde en de standaarddeviatie", "ad": "is de waarde waarvoor u de verdeling wilt bepalen!is het rekenkundige gemiddelde van de verdeling!is de standaarddeviatie van de verdeling. Dit is een positief getal.!is een logische waarde. Gebruik WAAR voor de cumulatieve verdelingsfunctie en ONWAAR voor de kansdichtheidsfunctie"}, "NORM.INV": {"a": "(kans; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>)", "d": "Berekent de inverse van de cumulatieve normale verdeling voor het gemiddelde en de standaarddeviatie die u hebt opgegeven", "ad": "is een getal van 0 tot en met 1 voor de kans die overeenkomt met een normale verdeling!is het rekenkundige gemiddelde van de verdeling!is de standaarddeviatie van de verdeling. Dit is een positief getal"}, "NORMINV": {"a": "(kans; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>)", "d": "Berekent de inverse van de cumulatieve normale verdeling voor het gemiddelde en de standaarddeviatie die u hebt opgegeven", "ad": "is een getal van 0 tot en met 1 voor de kans die overeenkomt met een normale verdeling!is het rekenkundige gemiddelde van de verdeling!is de standaarddeviatie van de verdeling. Dit is een positief getal"}, "NORM.S.DIST": {"a": "(z; cumulatief)", "d": "Geeft als resultaat de normale standaardverdeling (heeft een gemiddelde van nul en een standaarddeviatie van één)", "ad": "is de waarde waarvoor u de verdeling wilt weten!is een logische waarde die door de functie moet worden geretourneerd: de cumulatieve verdelingsfunctie = WAAR; de kansdichtheidsfunctie = ONWAAR"}, "NORMSDIST": {"a": "(z)", "d": "Geeft als resultaat de cumulatieve normale standaardverdeling (met een gemiddelde nul en een standaarddeviatie één)", "ad": "is de waarde waarvoor u de verdeling wilt bepalen"}, "NORM.S.INV": {"a": "(kans)", "d": "Berekent de inverse van de cumulatieve normale standaardverdeling (met een gemiddelde nul en een standaarddeviatie één)", "ad": "is een getal van 0 tot en met 1 voor de kans die overeenkomt met een normale verdeling"}, "NORMSINV": {"a": "(kans)", "d": "Berekent de inverse van de cumulatieve normale standaardverdeling (met een gemiddelde nul en een standaarddeviatie één)", "ad": "is een getal van 0 tot en met 1 voor de kans die overeenkomt met een normale verdeling"}, "PEARSON": {"a": "(matrix1; matrix2)", "d": "Berekent de correlatiecoëfficië<PERSON> <PERSON>", "ad": "is een verzameling onafhankelijke waarden!is een verzameling afhankelijke waarden"}, "PERCENTILE": {"a": "(matrix; k)", "d": "Berekent het k-percentiel van waarden in een bereik", "ad": "is de matrix of het gegevensbereik waarin u de relatieve positie van waarde k wilt bepalen!is de percentielwaarde in het bereik van 0 tot en met 1"}, "PERCENTILE.EXC": {"a": "(matrix; k)", "d": "Geeft als resultaat het k-percentiel van waarden in een bereik, waar<PERSON>j k zich in het bereik 0..1, exclusief bevindt", "ad": "is de matrix of het bere<PERSON> van gege<PERSON>s die de relatieve positie bepaalt!is de percentiele waarde tussen 0 en 1, inclusief"}, "PERCENTILE.INC": {"a": "(matrix; k)", "d": "Geeft als resultaat het k-percentiel van waarden in een bereik, waar<PERSON>j k zich in het bereik 0..1, inclusief bevindt", "ad": "is de matrix of het bere<PERSON> van gege<PERSON>s die de relatieve positie bepaalt!is de percentiele waarde tussen 0 en 1, inclusief"}, "PERCENTRANK": {"a": "(matrix; x; [significantie])", "d": "Geeft als resultaat de positie, in procenten uitgedrukt, van een waarde in de rangorde van een gegevensbereik", "ad": "is de matrix of het bereik met numerieke gegevens waarin u de relatieve positie van waarde x wilt bepalen!is de waarde waarvan u de positie wilt weten!is een optionele waarde die het aantal significante cijfers van het resulterende percentage aangeeft. Als u niets opgeeft, worden drie cijfers weergegeven (0,xxx%)"}, "PERCENTRANK.EXC": {"a": "(matrix; x; [significantie])", "d": "Bepaalt de positie van een waarde in een gegevensset als een percentage van de gegevensset als een percentage (0..1, exclusief) van de gegevensset", "ad": "is de matrix of het gegevensbereik met numerieke waarden die de relatieve positie bepaalt!is de waarde waarvoor u de positie wilt weten!is een optionele waarde die het aantal significante cijfers voor het resulterende percentage identificeert. Als u niets opgeeft, worden drie cijfers weergegeven (0,xxx%)"}, "PERCENTRANK.INC": {"a": "(matrix; x; [significantie])", "d": "Bepaalt de positie van een waarde in een gegevensset als een percentage van de gegevensset als een percentage (0..1, inclusief) van de gegevensset", "ad": "is de matrix of het gegevensbereik met numerieke waarden die de relatieve positie bepaalt!is de waarde waarvoor u de positie wilt weten!is een optionele waarde die het aantal significante cijfers voor het resulterende percentage identificeert. Als u niets opgeeft, worden drie cijfers weergegeven (0,xxx%)"}, "PERMUT": {"a": "(getal; aantal-gekozen)", "d": "Berekent het aantal permutaties voor een gegeven aantal objecten dat uit het totale aantal objecten geselecteerd kan worden", "ad": "is het totale aantal objecten!is het aantal objecten in elke permutatie"}, "PERMUTATIONA": {"a": "(getal; aantal_gekozen)", "d": "Geeft als resultaat het aantal permutaties voor een opgegeven aantal objecten (met her<PERSON><PERSON>) dat kan worden geselecteerd in het totale aantal objecten", "ad": "is het totale aantal objecten!is het aantal objecten in elke permutatie"}, "PHI": {"a": "(x)", "d": "Geeft als resultaat de waarde van de dichtheidsfunctie voor de normale standaardverdeling", "ad": "is het getal waarvoor u de dichtheid van de normale standaardverdeling wilt berekenen"}, "POISSON": {"a": "(x; gemid<PERSON><PERSON>; cumulatief)", "d": "Geeft als resultaat de Poisson-verdeling", "ad": "is het aantal gebeurtenissen!is de verwachte numerieke waarde. Dit is een positief getal.!is een logische waarde. Gebruik WAAR voor de cumulatieve Poisson-kans en ONWAAR voor de Poisson-kansdichtheidsfunctie"}, "POISSON.DIST": {"a": "(x; gemid<PERSON><PERSON>; cumulatief)", "d": "Geeft als resultaat de Poisson-verdeling", "ad": "is het aantal gebeurtenissen!is de verwachte numerieke waarde. Dit is een positief getal.!is een logische waarde. Gebruik WAAR voor de cumulatieve Poisson-kans en ONWAAR voor de Poisson-kansdichtheidsfunctie"}, "PROB": {"a": "(x-bereik; kansbereik; ondergrens; [boveng<PERSON>s])", "d": "<PERSON><PERSON><PERSON> de kans dat waarden zich tussen twee grenzen bevinden of gelijk zijn aan een onderlimiet", "ad": "is het bere<PERSON> met de numerieke waarden voor x waarvoor bijbehorende kansen bestaan!is een verzameling kansen die betrekking hebben op de waarden in het X-bereik, waarbij de waarden tussen 0 en 1 liggen en niet gelijk zijn aan 0!is de ondergrens voor een waarde waarvan u een kans wilt vaststellen!is de optionele bovengrens voor een waarde waarvan u een kans wilt vaststellen. Zonder deze parameter geeft KANS als resultaat de kans dat de waarden in het X-bereik gelijk zijn aan Ondergrens"}, "QUARTILE": {"a": "(matrix; kwartiel)", "d": "Berekent het kwartiel van een gegevensverzameling", "ad": "is de matrix of het celbereik met de numerieke waarden waarvoor u het kwartiel wilt berekenen!is een getal: minimumwaarde = 0; 1e kwartiel = 1; middenwaarde = 2; 3e kwartiel - 3; maximumwaarde = 4"}, "QUARTILE.INC": {"a": "(matrix; kwartiel)", "d": "Bepaalt het kwartiel van een gegevensset op basis van percentiele waarden van 0..1, inclusief", "ad": "is de matrix of het celbereik met numerieke waarden waarvoor u het kwartiel wilt weten!is een getal: minimale waarde = 0; eerste kwartiel = 1; gemiddelde waarde = 2; derde kwartiel = 3; maximale waarde = 4"}, "QUARTILE.EXC": {"a": "(matrix; kwartiel)", "d": "Bepaalt het kwartiel van een gegevensset op basis van percentiele waarden van 0..1, exclusief", "ad": "is de matrix of het celbereik met numerieke waarden waarvoor u het kwartiel wilt weten!is een getal: minimale waarde = 0; eerste kwartiel = 1; gemiddelde waarde = 2; derde kwartiel = 3; maximale waarde = 4"}, "RANK": {"a": "(getal; verw; [volgorde])", "d": "Berekent de rang van een getal in een lijst getallen: de grootte ten opzichte van andere waarden in de lijst", "ad": "is het getal waarvan u de rang wilt bepalen!is een matrix van of een verwijzing naar een lijst met getallen. Niet-numerieke waarden worden genegeerd!is een getal: 0 of weggelaten = rang in de aflopend gesorteerde lijst, een waarde ongelijk aan nul = rang in de oplopend gesorteerde lijst"}, "RANK.AVG": {"a": "(getal; verw; [volgorde])", "d": "Berekent de rang van een getal in een lijst getallen: de grootte ten opzichte van andere waarden in de lijst; als meer dan één waarde de<PERSON>fde rang heeft, wordt de gemiddelde rang geretourneerd", "ad": "is het getal waarvan u de rang wilt bepalen!is een matrix van of een verwijzing naar een lijst met getallen. Niet-numerieke waarden worden genegeerd!is een getal: 0 of weggelaten = rang in de aflopend gesorteerde lijst, een waarde ongelijk aan nul = rang in de oplopend gesorteerde lijst"}, "RANK.EQ": {"a": "(getal; verw; [volgorde])", "d": "Berekent de rang van een getal in een lijst getallen: de grootte ten opzichte van andere waarden in de lijst; als meerdere waarden dezelfde rang hebben, wordt de bovenste rang van die set met waarden geretourneerd", "ad": "is het getal waarvan u de rang wilt bepalen!is een matrix van of een verwijzing naar een lijst met getallen. Niet-numerieke waarden worden genegeerd!is een getal: 0 of weggelaten = rang in de aflopend gesorteerde lijst, een waarde ongelijk aan nul = rang in de oplopend gesorteerde lijst"}, "RSQ": {"a": "(known_ys; known_xs)", "d": "Berekent R-kwadraat van een lineaire regressielijn door de ingevoerde gegevenspunten", "ad": "is een matrix of een bereik met gegevenspunten. Dit kunnen getallen zijn of namen, matrices of verwijzingen die getallen bevatten!is een matrix of een bereik met gegevenspunten. Dit kunnen getallen zijn of namen, matrices of verwijzingen die getallen bevatten"}, "SKEW": {"a": "(getal1; [getal2]; ...)", "d": "Berek<PERSON> de mate van asymmetrie van een verdeling: een a<PERSON><PERSON>ing van de mate van asymmetrie van een verdeling rond het gemiddelde", "ad": "zijn 1 tot 255 getallen of namen, matrices of verwi<PERSON><PERSON>en die getallen bevatten waarvoor u de scheefheid wilt berekenen"}, "SKEW.P": {"a": "(getal1; [getal2]; ...)", "d": "Geeft als resultaat de scheefheid van een verdeling op basis van een populatie: een ken<PERSON><PERSON> van de mate van asymmetrie van een verdeling rondom het gemiddelde", "ad": "zijn maximaal 254 getallen of namen, matrices of verwi<PERSON><PERSON>en die getallen bevatten waarvoor u de populatiescheefheid wilt berekenen"}, "SLOPE": {"a": "(known_ys; known_xs)", "d": "Berekent de richtingscoëfficiënt van een lineaire regressielijn door de ingevoerde gegevenspunten", "ad": "is een matrix of celbereik met afhankelijke numerieke gegevenspunten. Dit kunnen getallen zijn of namen, matrices of verwijzingen die getallen bevatten!is een verzameling onafhankelijke gegevenspunten. Dit kunnen getallen zijn of namen, matrices of verwijzingen die getallen bevatten"}, "SMALL": {"a": "(matrix; k)", "d": "Geeft als resultaat de op k-1 na kleinste waarde in een gegevensbereik, bijvoorbeeld het vijfde kleinste getal", "ad": "is de matrix of het bereik met numerieke gegevens waarvoor u de op k-1 na kleinste waarde wilt bepalen!is de positie (vanaf de kleinste waarde geteld) in de resulterende matrix of het resulterende celbereik met gegevens"}, "STANDARDIZE": {"a": "(x; gem<PERSON><PERSON><PERSON>; stand<PERSON><PERSON><PERSON>)", "d": "Berekent een genormaliseerde waarde uit een verdeling die wordt gekenmerkt door een gemiddelde en standaarddeviatie", "ad": "is de waarde die u wilt normaliseren!is het rekenkundige gemiddelde van de verdeling!is de standaarddeviatie van de verdeling. Dit is een positief getal."}, "STDEV": {"a": "(getal1; [getal2]; ...)", "d": "Maakt een schatting van de standaarddeviatie op basis van een steekproef (logische waarden en tekst in de steekproef worden genegeerd)", "ad": "zijn maximaal 255 getallen die resulteren uit een steekproef onder een populatie. Dit kunnen getallen zijn of verwijzingen die getallen bevatten"}, "STDEV.P": {"a": "(getal1; [getal2]; ...)", "d": "Berekent de standaarddeviatie op basis van de volledige populatie die als argumenten wordt gegeven (logische waarden en tekst worden genegeerd)", "ad": "zijn maximaal 255 getallen die betrekking hebben op een populatie. Dit kunnen getallen zijn of verwijzingen die getallen bevatten"}, "STDEV.S": {"a": "(getal1; [getal2]; ...)", "d": "Maakt een schatting van de standaarddeviatie op basis van een steekproef (logische waarden en tekst in de steekproef worden genegeerd)", "ad": "zijn maximaal 255 getallen die resulteren uit een steekproef onder een populatie. Dit kunnen getallen zijn of verwijzingen die getallen bevatten"}, "STDEVA": {"a": "(waarde1; [waarde2]; ...)", "d": "Maakt een schatting van de standaarddeviatie op basis van een steekproef, met inbegrip van logische waarden en tekst. Tekst en de logische waarde ONWAAR krijgen de waarde 0, de logische waarde WAAR krijgt de waarde 1", "ad": "zijn 1 tot 255 waarden die resulteren uit een steekproef onder een populatie. Dit kunnen waarden zijn of namen of verwijzingen voor cellen die waarden bevatten"}, "STDEVP": {"a": "(getal1; [getal2]; ...)", "d": "Berekent de standaarddeviatie op basis van de volledige populatie die als argumenten worden gegeven (logische waarden en tekst worden genegeerd)", "ad": "zijn maximaal 255 getallen die betrekking hebben op een populatie. Dit kunnen getallen zijn of verwijzingen die getallen bevatten"}, "STDEVPA": {"a": "(waarde1; [waarde2]; ...)", "d": "Berekent de standaarddeviatie op basis van de volledige populatie, inclusief logische waarden en tekst. Tekst en de logische waarde ONWAAR krijgen de waarde 0, de logische waarde WAAR krijgt de waarde 1", "ad": "zijn 1 tot 255 waarden die betrekking hebben op een populatie. Dit kunnen waarden zijn of namen, matrices of verwijzingen voor cellen die waarden bevatten"}, "STEYX": {"a": "(known_ys; known_xs)", "d": "<PERSON><PERSON><PERSON> de standaardfout in de voorspelde y-waarde voor elke x in een regressie", "ad": "is een matrix of een bereik met afhankelijke gegevenspunten. Dit kunnen getallen zijn of namen, matrices of verwijzingen die getallen bevatten!is een matrix of een bereik met onafhankelijke gegevenspunten. Dit kunnen getallen zijn of namen, matrices of verwijzingen die getallen bevatten"}, "TDIST": {"a": "(x; vri<PERSON><PERSON><PERSON><PERSON><PERSON>; zij<PERSON>)", "d": "<PERSON><PERSON><PERSON> de Student T-verdeling", "ad": "is de numerieke waarde waarop u de verdeling wilt evalueren!is een geheel getal voor het aantal vrijheidsgraden dat de verdeling kenmerkt!geeft de verdelingstoets aan waarvan u uitgaat: eenzijdige toets = 1, tweezijdige toets = 2"}, "TINV": {"a": "(kans; vrijhe<PERSON>graden)", "d": "Berekent de tweezijdige inverse van de Student T-verdeling", "ad": "is een getal van 0 tot en met 1 voor de kans die samenhangt met de tweezijdige Student T-verdeling!is een positief geheel getal voor het aantal vrijheidsgraden van de verdeling"}, "T.DIST": {"a": "(x; vri<PERSON><PERSON><PERSON>n; cumulatief)", "d": "Deze eigenschap retourneert de linkszijdige Student T-verdeling", "ad": "is de numerieke waarde waarop u de verdeling wilt evalueren!is een geheel getal voor het aantal vrijheidsgraden dat de verdeling kenmerkt!is een logische waarde die bepaalt welke functie als resultaat wordt gegeven: WAAR = de cumulatieve verdelingsfunctie, ONWAAR = de kansdichtheidsfunctie"}, "T.DIST.2T": {"a": "(x; vri<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Berekent de tweezijdige Student T-verdeling", "ad": "is de numerieke waarde waarvoor u de verdeling wilt evalueren!is een geheel getal dat het aantal vrijheidsgraden aangeeft dat de verdeling kenmerkt"}, "T.DIST.RT": {"a": "(x; vri<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Berekent de rechtszijdige Student T-verdeling", "ad": "is de numerieke waarde waarvoor u de verdeling wilt evalueren!is een geheel getal dat het aantal vrijheidsgraden aangeeft dat de verdeling kenmerkt"}, "T.INV": {"a": "(kans; vrijhe<PERSON>graden)", "d": "Berekent de linkszijdige inverse van de Student T-verdeling", "ad": "is een getal van 0 tot en met 1 voor de kans die samenhangt met de tweezijdige Student T-verdeling!is een positief geheel getal voor het aantal vrijheidsgraden van de verdeling"}, "T.INV.2T": {"a": "(kans; vrijhe<PERSON>graden)", "d": "Berekent de tweezijdige inverse van de Student T-verdeling", "ad": "is een getal van 0 tot en met 1 voor de kans die samenhangt met de tweezijdige Student T-verdeling!is een positief geheel getal voor het aantal vrijheidsgraden van de verdeling"}, "T.TEST": {"a": "(matrix1; matrix2; zijden; type_getal)", "d": "<PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON> T-toets", "ad": "is de eerste gegevensverzameling!is de tweede gegevensverzameling!geeft de verdelingstoets aan waarvan u uitgaat: eenzijdige toets = 1, tweezijdige toets = 2!is het type T-toets dat u wilt uitvoeren: gepaard = 1, twee steekproeven met gelijke varianties = 2, twee steekproeven met ongelijke varianties = 3"}, "TREND": {"a": "(known_ys; [known_xs]; [new_xs]; [const])", "d": "Geeft als resultaat getallen in een lineaire trend die overeenkomen met bekende gegevenspunten, be<PERSON><PERSON> met de kleinste-kwadratenmethode", "ad": "is een bereik of matrix met y-waarden die al bekend is uit de relatie y = mx + b!is het optionele bereik of de optionele matrix x-waarden die al bekend is uit de relatie y = mx + b, een matrix van dezelfde grootte als y_bekend!is een bereik of matrix met de nieuwe x-waarden waarvoor TREND als resultaat de bijbehorende y-waarde moet geven!is een logische waarde. Als Const = WAAR of wordt weggelaten, wordt de constante b normaal berekend. Als Const = ONWAAR, moet b gelijk zijn aan 0"}, "TRIMMEAN": {"a": "(matrix; percentage)", "d": "Berekent het gemiddelde van waarden in een gegevensverzameling, waarbij de extreme waarden in de berekening worden uitgesloten", "ad": "is de matrix of het bere<PERSON> met waarden waaruit u extreme waarden wilt verwijderen om vervolgens het gemiddelde te berekenen!is het percentage van de gegevenspunten dat aan de boven- en onderzijde van de gegevensverzameling uitgesloten moet worden"}, "TTEST": {"a": "(matrix1; matrix2; zijden; type_getal)", "d": "<PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON> T-toets", "ad": "is de eerste gegevensverzameling!is de tweede gegevensverzameling!geeft de verdelingstoets aan waarvan u uitgaat: eenzijdige toets = 1, tweezijdige toets = 2!is het type T-toets dat u wilt uitvoeren: gepaard = 1, twee steekproeven met gelijke varianties = 2, twee steekproeven met ongelijke varianties = 3"}, "VAR": {"a": "(getal1; [getal2]; ...)", "d": "Maakt een schatting van de variantie op basis van een steekproef (logische waarden en tekst in de steekproef worden genegeerd)", "ad": "zijn maximaal 255 numerieke argumenten die resulteren uit een steekproef onder een populatie"}, "VAR.P": {"a": "(getal1; [getal2]; ...)", "d": "Berekent de variantie op basis van de volledige populatie (logische waarden en tekst in de populatie worden genegeerd)", "ad": "zijn maximaal 255 numerieke argumenten die betrekking hebben op een populatie"}, "VAR.S": {"a": "(getal1; [getal2]; ...)", "d": "Maakt een schatting van de variantie op basis van een steekproef (logische waarden en tekst in de steekproef worden genegeerd)", "ad": "zijn maximaal 255 numerieke argumenten die resulteren uit een steekproef onder een populatie"}, "VARA": {"a": "(waarde1; [waarde2]; ...)", "d": "Maakt een schatting van de variantie op basis van een steekproef, inclusief logische waarden en tekst. Tekst en de logische waarde ONWAAR krijgen de waarde 0, de logische waarde WAAR krijgt de waarde 1", "ad": "zijn 1 tot 255 numerieke argumenten die resulteren uit een steekproef onder een populatie"}, "VARP": {"a": "(getal1; [getal2]; ...)", "d": "Berekent de variantie op basis van de volledige populatie (logische waarden en tekst in de populatie worden genegeerd)", "ad": "zijn maximaal 255 numerieke argumenten die betrekking hebben op een populatie"}, "VARPA": {"a": "(waarde1; [waarde2]; ...)", "d": "Berekent de variantie op basis van de volledige populatie, inclusief logische waarden en tekst. Tekst en de logische waarde ONWAAR krijgen de waarde 0, de logische waarde WAAR krijgt de waarde 1", "ad": "zijn 1 tot 255 numerieke argumenten die betrekking hebben op een populatie"}, "WEIBULL": {"a": "(x; alfa; bèta; cumulatief)", "d": "Geeft als resultaa<PERSON> de <PERSON>-verdeling", "ad": "is een niet-negatief getal voor de waarde waarop de functie geëvalueerd moet worden!is de parameter voor de verdeling. Dit is een positief getal.!is de parameter voor de verdeling. Dit is een positief getal.!is een logische waarde. Gebruik WAAR voor de cumulatieve verdelingsfunctie en ONWAAR voor de kansdichtsheidsfunctie"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; cumulatief)", "d": "Geeft als resultaa<PERSON> de <PERSON>-verdeling", "ad": "is een niet-negatief getal voor de waarde waarop de functie geëvalueerd moet worden!is de parameter voor de verdeling. Dit is een positief getal.!is de parameter voor de verdeling. Dit is een positief getal.!is een logische waarde. Gebruik WAAR voor de cumulatieve verdelingsfunctie en ONWAAR voor de kansdichtsheidsfunctie"}, "Z.TEST": {"a": "(matrix; x; [sigma])", "d": "Berekent de eenzijdige P-waarde voor een Z-toets", "ad": "is de matrix of het gegevensbereik waartegen u x wilt testen!is de waarde die u wilt testen!is de (al bekende) standaarddeviatie voor de hele populatie. Als u deze niet opgeeft, wordt de standaarddeviatie voor de steekproef gebruikt"}, "ZTEST": {"a": "(matrix; x; [sigma])", "d": "Berekent de eenzijdige P-waarde voor een Z-toets", "ad": "is de matrix of het gegevensbereik waartegen u x wilt testen!is de waarde die u wilt testen!is de (al bekende) standaarddeviatie voor de hele populatie. Als u deze niet opgeeft, wordt de standaarddeviatie voor de steekproef gebruikt"}, "ACCRINT": {"a": "(uitgifte; eerste_rente; vervaldatum; rente; nominale_waarde; frequentie; [soort_jaar]; [berek.methode])", "d": "Berekent de samengestelde rente voor een waardepapier waarvan de rente periodiek wordt uitgekeerd", "ad": "is de uitgiftedatum van het waardepapier, uitgedrukt in een serieel getal!is de eerste rentedatum van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is het jaarlijkse rentepercentage van het waardepapier!is de nominale waarde van het waardepapier!is het aantal couponuitbetalingen per jaar!is het soort jaar waarop de berekening is gebaseerd!is een logische waarde: rente samenstellen vanaf uitgiftedatum = WAAR of weggelaten; berekenen vanaf datum van laatste couponbetaling = ONWAAR"}, "ACCRINTM": {"a": "(uitgifte; vervaldatum; rente; nominale_waarde; [soort_jaar])", "d": "Berekent de samengestelde rente voor een waardepapier waarvan de rente op de vervaldatum wordt uitgekeerd", "ad": "is de uitgiftedatum van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is het jaarlijkse rentepercentage van het waardepapier!is de nominale waarde van het waardepapier!is het soort jaar waarop de berekening is gebaseerd"}, "AMORDEGRC": {"a": "(kosten; aankoopdatum; eerste_termijn; restwaarde; termijn; snelheid; [basis])", "d": "<PERSON><PERSON>ent de evenredig verdeelde lineaire afschrijving van activa over el<PERSON> boekhoudperiode.", "ad": "zijn de kosten van activa!is de datum waarop activa zijn aangeschaft!is de einddatum van de eerste termijn!is de restwaarde aan het eind van de levensduur van activa.!is de termijn!is de s<PERSON>he<PERSON> van de afschrijving !jaar_basis : 0 voor een jaar van 360 dagen, 1 voor een werkelijk jaar, 3 voor een jaar van 365 dagen."}, "AMORLINC": {"a": "(kosten; aankoopdatum; eerste_termijn; restwaarde; termijn; snelheid; [basis])", "d": "<PERSON><PERSON>ent de evenredig verdeelde lineaire afschrijving van activa over el<PERSON> boekhoudperiode.", "ad": "zijn de kosten van activa!is de datum waarop activa zijn aangeschaft!is de einddatum van de eerste termijn!is de restwaarde aan het eind van de levensduur van activa.!is de termijn!is de s<PERSON>he<PERSON> van de afschrijving !jaar_basis : 0 voor een jaar van 360 dagen, 1 voor een werkelijk jaar, 3 voor een jaar van 365 dagen."}, "COUPDAYBS": {"a": "(stortingsdatum; vervaldatum; frequentie; [soort_jaar])", "d": "Berekent het aantal dagen vanaf het begin van de couponperiode tot de stortingsdatum", "ad": "is de stortingsdatum van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is het aantal couponuitbetalingen per jaar!is het soort jaar waarop de berekening is gebaseerd"}, "COUPDAYS": {"a": "(stortingsdatum; vervaldatum; frequentie; [soort_jaar])", "d": "Bepaalt het aantal dagen in de couponperiode waarin de stortingsdatum valt", "ad": "is de stortingsdatum van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van een waardepapier, uitgedrukt in een serieel getal!is het aantal couponuitbetalingen per jaar!is het soort jaar waarop de berekening is gebaseerd"}, "COUPDAYSNC": {"a": "(stortingsdatum; vervaldatum; frequentie; [soort_jaar])", "d": "Berekent het aantal dagen vanaf de stortingsdatum tot de volgende coupondatum", "ad": "is de stortingsdatum van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is het aantal couponuitbetalingen per jaar!is het soort jaar waarop de berekening is gebaseerd"}, "COUPNCD": {"a": "(stortingsdatum; vervaldatum; frequentie; [soort_jaar])", "d": "Bepaalt de volgende coupondatum na de stortingsdatum", "ad": "is de stortingsdatum van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is het aantal couponuitbetalingen per jaar!is het soort jaar waarop de berekening is gebaseerd"}, "COUPNUM": {"a": "(stortingsdatum; vervaldatum; frequentie; [soort_jaar])", "d": "Berekent het aantal coupons dat uitbetaald moet worden tussen de stortingsdatum en de vervaldatum", "ad": "is de stortingsdatum van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is het aantal couponuitbetalingen per jaar!is het soort jaar waarop de berekening is gebaseerd"}, "COUPPCD": {"a": "(stortingsdatum; vervaldatum; frequentie; [soort_jaar])", "d": "Berekent de laatste coupondatum voor de stortingsdatum", "ad": "is de stortingsdatum van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is het aantal couponuitbetalingen per jaar!is het soort jaar waarop de berekening is gebaseerd"}, "CUMIPMT": {"a": "(rente; aantal_termijnen; hw; begin_periode; einde_periode; type_getal)", "d": "Berekent de cumulatieve rente over een bepaalde periode", "ad": "is het rentepercentage!is het totale aantal betalingstermijnen!is de huidige waarde!is de eerste termijn in de periode waarover u de cumulatieve rente wilt berekenen!is de laatste termijn in de periode waarover u de cumulatieve rente wilt berekenen!is het tijds<PERSON> van de betaling"}, "CUMPRINC": {"a": "(rente; aantal_termijnen; hw; begin_periode; einde_periode; type_getal)", "d": "Berekent de cumulatieve terugbetaalde hoofdsom voor een lening over een bepaalde periode", "ad": "is het rentepercentage!is het totale aantal betalingstermijnen!is de huidige waarde!is de eerste termijn in de periode waarover u de cumulatieve terugbetaalde hoofdsom wilt berekenen!is de laatste termijn in de periode waarover u de cumulatieve terugbetaalde hoofdsom wilt berekenen!is het tij<PERSON><PERSON> van de betaling"}, "DB": {"a": "(kosten; restwaarde; duur; termijn; [maand])", "d": "<PERSON><PERSON><PERSON> de afschrijving van activa over een op<PERSON><PERSON><PERSON> termijn, met be<PERSON><PERSON> van de 'fixed declining balance'-methode", "ad": "zijn de aanschafkosten van de activa!is de resterende waarde van de activa aan het einde van de afschrijving!is het aantal termijnen waarin de activa worden afgeschreven (ook wel levensduur van de activa genoemd)!is de termijn waarvoor u de afschrijving wilt berekenen. Termijn moet in dezelfde eenheden worden opgegeven als duur!is het aantal maanden in het eerste jaar. Als maand wordt weggelaten, wordt uitgegaan van 12"}, "DDB": {"a": "(kosten; restwaarde; duur; termijn; [factor])", "d": "<PERSON><PERSON><PERSON> de afschrijving van activa over een opgegeven termijn met de 'double declining balance'-methode of met een methode die u zelf bepaalt", "ad": "zijn de aanschaf<PERSON>ten van de activa!is de resterende waarde van de activa aan het einde van de afschrijving!is het aantal termijnen waarin de activa worden afgeschreven (ook wel levensduur van de activa genoemd)!is de termijn waarvoor u de afschrijving wilt berekenen. Termijn moet in dezelfde eenheden worden opgegeven als duur!is de snelheid waarmee wordt afgeschreven. Als u de factor weglaat, wordt uitgegaan van de waarde 2 ('double declining balance'-methode)"}, "DISC": {"a": "(stortingsdatum; vervaldatum; prijs; aflossingsprijs; [soort_jaar])", "d": "Berekent het discontopercentage voor waardepapier", "ad": "is de stortings<PERSON><PERSON> van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is de prijs van het waardepapier per 100 euro nominale waarde!is de aflossingsprijs van het waardepapier per 100 euro nominale waarde!is het soort jaar waarop de berekening is gebaseerd"}, "DOLLARDE": {"a": "(breuk; noemer)", "d": "Converteert een prijs in euro's, uitgedrukt in een breuk, naar een prijs in euro's, uitgedrukt in een decimaal getal", "ad": "is een getal uitgedrukt in een breuk!is het gehele getal dat in de noemer van de breuk wordt gebruikt"}, "DOLLARFR": {"a": "(decimaal; noemer)", "d": "Converteert een prijs in euro's, uitgedrukt in een decimaal getal, naar een prijs in euro's, uitgedrukt in een breuk", "ad": "is een decimaal getal!is het gehele getal dat in de noemer van de breuk wordt gebruikt"}, "DURATION": {"a": "(stortingsdatum; vervaldatum; coupon; rendem; frequentie; [soort_jaar])", "d": "<PERSON><PERSON><PERSON>-<PERSON><PERSON> van een waardepap<PERSON> met periodieke rentebetalingen", "ad": "is de stortingsdatum van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is het jaarlijkse rentepercentage van het waardepapier!is het jaarlijkse rendement van het waardepapier!is het aantal couponuitbetalingen per jaar!is het soort jaar waarop de berekening is gebaseerd"}, "EFFECT": {"a": "(nominale_rente; termijnen_per_jaar)", "d": "Berekent het jaarlijkse effectieve rentepercentage", "ad": "is het nominale rentepercentage!is het aantal termijnen per jaar waarover de samengestelde rente wordt berekend"}, "FV": {"a": "(rente; aantal-termijnen; bet; [hw]; [type_getal])", "d": "<PERSON><PERSON><PERSON> de toekomstige waarde van een investering, geb<PERSON><PERSON> op periodieke, constante betalingen en een constant rentepercentage", "ad": "is het rentepercentage per termijn. Gebruik bijvoorbeeld 6%/4 voor kwartaalbetalingen met een rentepercentage van 6%!is het totale aantal betalingstermijnen van een investering!is de betaling die elke termijn wordt verricht. Dit bedrag kan gedurende de looptijd van de investering niet worden gewijzigd!is de huidige waarde of het totaalbedrag dat een reeks toekomstige betalingen op dit moment waard is. Als dit wordt weggelaten, wordt uitgegaan van Hw = 0!is een waarde die aangeeft wanneer de betalingen voldaan moeten worden. 1 = betaling aan het begin van de periode. 0 of weggelaten = betaling aan het einde van de periode"}, "FVSCHEDULE": {"a": "(hoofdsom; rente_waarden)", "d": "Berekent de toekomstige waarde van een aanvangshoofdsom nadat de samengestelde rente eraan is toegevoegd", "ad": "is de huidige waarde!is een matrix met de rentepercentages die moeten worden toegepast"}, "INTRATE": {"a": "(stortingsdatum; vervaldatum; investering; aflossingsprijs; [soort_jaar])", "d": "Berekent het rentepercentage voor een volgestort waardepapier", "ad": "is de stortingsdatum van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is het bedrag dat in het waardepapier is geïnvesteerd!is het bedrag dat op de vervaldatum wordt uitgekeerd!is het soort jaar waarop de berekening is gebaseerd"}, "IPMT": {"a": "(rente; termijn; aantal-termijnen; hw; [tw]; [type_getal])", "d": "Berekent de te betalen rente voor een investering over een bepaalde termijn, op basis van periodieke, constante betalingen en een constant rentepercentage", "ad": "is het rentepercentage per termijn. Gebruik bijvoorbeeld 6%/4 voor kwartaalbetalingen met een rentepercentage van 6%!is de termijn waarover u het rentepercentage wilt berekenen. Dit argument moet liggen tussen 1 en aantal-termijnen!is het totale aantal betalingstermijnen van een investering!is de huidige waarde of het totaalbedrag dat een reeks toekomstige betalingen op dit moment waard is!is de toekomstige waarde of het kassaldo waarover u wilt beschikken als de laatste betaling is gedaan. Als dit wordt weggelaten, wordt uitgegaan van TW = 0!is een logische waarde die aangeeft wanneer de betalingen voldaan moeten worden: 0 of weggelaten = aan het begin van de periode, 1 = aan het einde van de periode"}, "IRR": {"a": "(waarden; [schatting])", "d": "Berekent de interne rentabiliteit voor een reeks cashflows", "ad": "is een matrix of verwi<PERSON>zing naar cellen die getallen bevatten waarvoor u de interne rentabiliteit wilt berekenen!is een getal waarvan u denkt dat het in de buurt komt van het resultaat van IR. Als dit wordt weggelaten, wordt uitgegaan van 0,1 (10 procent)"}, "ISPMT": {"a": "(rente; termijn; aantal-termijnen; hw)", "d": "Berekent de betaalde rente voor een bepaalde termijn van een investering", "ad": "Is het rentepercentage per termijn. Gebruik bijvoorbeeld 6%/4 voor kwartaalbetalingen met een rentepercentage van 6%!is de termijn waarvoor u de rente wilt berekenen!is het aantal betalingstermijnen van een investering!is de huidige waarde of het totaalbedrag dat een reeks toekomstige betalingen op dit moment waard is"}, "MDURATION": {"a": "(stortingsdatum; vervaldatum; coupon; rendem; frequentie; [soort_jaar])", "d": "<PERSON><PERSON><PERSON> de aangepaste <PERSON>ley-duur van een waardepapier, aangenomen dat de nominale waarde 100 euro bedraagt", "ad": "is de stortingsdatum van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is het jaarlijkse rentepercentage van het waardepapier!is het jaarlijkse rendement van het waardepapier!is het aantal couponuitbetalingen per jaar!is het soort jaar waarop de berekening is gebaseerd"}, "MIRR": {"a": "(waarden; financieringsrente; herinvesteringsrente)", "d": "Berekent de interne rentabiliteit voor een serie periodieke cashflows, rekening ho<PERSON><PERSON> met zow<PERSON> beleggingskosten als de rente op het herinvesteren van geld", "ad": "is een matrix of verwijzing naar cellen die getallen bevatten die overeenkomen met een reeks betalingen (negatief) en inkomsten (positief) met regelmatige tussenperioden!is het rentepercentage dat u betaalt over het in de cashflows gebruikte bedrag!is het rentepercentage dat u ontvangt door de cashflows te herinvesteren"}, "NOMINAL": {"a": "(effectieve_rente; termijnen_per_jaar)", "d": "Berekent de jaarlijkse nominale rente", "ad": "is het effectieve rentepercentage!is het aantal termijnen per jaar waarover de samengestelde rente wordt berekend"}, "NPER": {"a": "(rente; bet; hw; [tw]; [type_getal])", "d": "Berekent het aantal termijnen van een investering, op <PERSON> van periodieke, constante betalingen en een constant rentepercentage", "ad": "is het rentepercentage per termijn. Gebruik bijvoorbeeld 6%/4 voor kwartaalbetalingen met een rentepercentage van 6%!is de betaling die iedere termijn wordt verricht. Dit bedrag kan gedurende de looptijd van de investering niet worden gewijzigd!is de huidige waarde of het totaalbedrag dat een reeks toekomstige betalingen op dit moment waard is!is de toekomstige waarde of het kassaldo waarover u wilt beschikken als de laatste betaling is gedaan. Als dit wordt weggelaten, wordt nul gebruikt!is een logische waarde: 1 = betaling aan het begin van de periode, 0 of weggelaten = betaling aan het einde van de periode"}, "NPV": {"a": "(rente; waarde1; [waarde2]; ...)", "d": "Berekent de netto huidige waarde van een investering op basis van een discontopercentage en een reeks toekomstige betalingen (negatieve waarden) en inkomsten (positieve waarden)", "ad": "is het discontopercentage over <PERSON><PERSON> termijn!zijn maximaal 254 betalingen en inkomsten, met gelijke tussenperioden, die aan het einde van elke periode optreden"}, "ODDFPRICE": {"a": "(stortingsdatum; vervaldatum; uitgifte; eerste_coupon; rente; rendem; aflossingsprijs; frequentie; [soort_jaar])", "d": "Bepaalt de prijs per 100 euro nominale waarde voor een waardepapier met een afwijkende eerste termijn", "ad": "is de stortings<PERSON>tum van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is de dag van uitgifte van het waardepapier, uitgedrukt in een serieel getal!is de eerste coupondatum voor het waardepapier, uitgedrukt in een serieel getal!Is het jaarlijkse rentepercentage van het waardepapier!is het jaarlijkse rendement van het waardepapier!is de aflossingsprijs van het waardepapier per 100 euro nominale waarde!Is het aantal couponuitbetalingen per jaar!is het soort jaar waarop de berekening is gebaseerd"}, "ODDFYIELD": {"a": "(stortingsdatum; vervaldatum; uitgifte; eerste_coupon; rente; prijs; aflossingsprijs; frequentie; [soort_jaar])", "d": "<PERSON><PERSON><PERSON> het rendement voor een waardepapier met een afwijkende eerste termijn", "ad": "is de stortings<PERSON><PERSON> van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is de dag van uitgifte van het waardepapier, uitgedrukt in een serieel getal!is de eerste coupondatum voor het waardepapier, uitgedrukt in een serieel getal!is het jaarlijkse rentepercentage van het waardepapier!is de prijs van het waardepapier!is de aflossingsprijs van het waardepapier per 100 euro nominale waarde!is het aantal couponuitbetalingen per jaar!is het soort jaar waarop de berekening is gebaseerd"}, "ODDLPRICE": {"a": "(stortingsdatum; vervaldatum; laatste_rente; rente; rendem; aflossingsprijs; frequentie; [soort_jaar])", "d": "Bepaalt de prijs per 100 euro nominale waarde voor een waardepapier met een afwijkende laatste termijn", "ad": "is de stortingsdatum van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is de laatste coupondatum voor het waardepapier, uitgedrukt in een serieel getal!is het jaarlijkse rentepercentage van het waardepapier!is het jaarlijkse rendement van het waardepapier!is de aflossingsprijs van het waardepapier per 100 euro nominale waarde!is het aantal couponuitbetalingen per jaar!is het soort jaar waarop de berekening is gebaseerd"}, "ODDLYIELD": {"a": "(stortingsdatum; vervaldatum; laatste_rente; rente; prijs; aflossingsprijs; frequentie; [soort_jaar])", "d": "<PERSON><PERSON><PERSON> het rendement van een waardepapier met een afwijkende laatste termijn", "ad": "is de stortings<PERSON>tum van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van een waardepapier, uitgedrukt in een serieel getal!is de laatste coupondatum voor het waardepapier, uitgedrukt in een serieel getal!is het jaarlijkse rentepercentage van het waardepapier!is de prijs van het waardepapier!is de aflossingsprijs van het waardepapier per 100 euro nominale waarde!is het aantal couponuitbetalingen per jaar!is het soort jaar waarop de berekening is gebaseerd"}, "PDURATION": {"a": "(percentage; hw; tw)", "d": "Geeft als resultaat het aantal perioden dat is vereist voor een investering om een opgegeven waarde te bereiken", "ad": "is het rentepercentage per periode.!is de huidige waarde van de investering!is de gewenste toekomstige waarde van de investering"}, "PMT": {"a": "(rente; aantal-termijnen; hw; [tw]; [type_getal])", "d": "Berekent de periodieke betaling voor een lening op basis van constante betalingen en een constant rentepercentage", "ad": "is het rentepercentage per termijn voor de lening. Gebruik bijvoorbeeld 6%/4 voor kwartaalbetalingen met een rentepercentage van 6%!is het totale aantal betalingstermijnen van een lening!is de huidige waarde: het totaalbedrag dat een reeks toekomstige betalingen op dit moment waard is!is de toekomstige waarde of het kassaldo waarover u wilt beschikken als de laatste betaling is gedaan. Als dit wordt weggelaten, wordt uitgegaan van 0 (nul)!is een logische waarde: 1 = betaling aan het begin van de periode, 0 of weggelaten = betaling aan het einde van de periode"}, "PPMT": {"a": "(rente; termijn; aantal-termijnen; hw; [tw]; [type_getal])", "d": "Berekent de afbetaling op de hoofdsom voor een gegeven investering, op basis van constante betalingen en een constant rentepercentage", "ad": "is het rentepercentage per termijn. Gebruik bijvoorbeeld 6%/4 voor kwartaalbetalingen met een rentepercentage van 6%!geeft de termijn aan en moet een getal zijn tussen 1 en aantal-termijnen!is het totale aantal betalingstermijnen van een investering!is de huidige waarde: het totaalbedrag dat een reeks toekomstige betalingen op dit moment waard is!is de toekomstige waarde of het kassaldo waarover u wilt beschikken als de laatste betaling is gedaan!is een logische waarde: 1 = betaling aan het begin van de periode, 0 of weggelaten = betaling aan het einde van de periode"}, "PRICE": {"a": "(stortingsdatum; vervaldatum; rente; rendem; aflossingsprijs; frequentie; [soort_jaar])", "d": "Bepaalt de prijs per 100 euro nominale waarde voor een waardepapier waarvan de rente periodiek wordt uitgekeerd", "ad": "is de stortingsda<PERSON> van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is het jaarlijkse rentepercentage van het waardepapier!is het jaarlijkse rendement van het waardepapier!is de aflossingsprijs van het waardepapier per 100 euro nominale waarde!Is het aantal couponuitbetalingen per jaar!is het soort jaar waarop de berekening is gebaseerd"}, "PRICEDISC": {"a": "(stortingsdatum; vervaldatum; disc; aflossingsprijs; [soort_jaar])", "d": "Bepaalt de prijs per 100 euro nominale waarde voor een verdisconteerd waardepapier", "ad": "is de stortingsdatum van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is het jaarlijkse discontopercentage van het waardepapier!is de aflossingsprijs van het waardepapier per 100 euro nominale waarde!is het soort jaar waarop de berekening is gebaseerd"}, "PRICEMAT": {"a": "(stortingsdatum; vervaldatum; uitgifte; rente; rendem; [soort_jaar])", "d": "Bepaalt de prijs per 100 euro nominale waarde voor een waardepapier waarvan de rente op de vervaldatum wordt uitgekeerd", "ad": "is de stortings<PERSON><PERSON> van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is de dag van uitgifte van het waardepapier, uitgedrukt in een serieel getal!is het rentepercentage van het waardepapier op de dag van uitgifte!is het jaarlijkse rendement van het waardepapier!is het soort jaar waarop de berekening is gebaseerd"}, "PV": {"a": "(rente; aantal-termijnen; bet; [tw]; [type_getal])", "d": "Berekent de huidige waarde van een investering: het totale bedrag dat een reeks toekomstige betalingen momenteel waard is", "ad": "is het rentepercentage per termijn. Gebruik bijvoorbeeld 6%/4 voor kwartaalbetalingen met een rentepercentage van 6%!is het totale aantal betalingstermijnen van een investering!is de betaling die iedere termijn wordt verricht. Dit bedrag kan gedurende de looptijd van de investering niet worden gewijzigd!is de toekomstige waarde of het kassaldo waarover u wilt beschikken als de laatste betaling is gedaan!is een logische waarde: 1 = betaling aan het begin van de periode, 0 of weggelaten = betaling aan het einde van de periode"}, "RATE": {"a": "(aantal-termijnen; bet; hw; [tw]; [type_getal]; [schatting])", "d": "Berekent het periodieke rentepercentage voor een lening of een investering. Gebruik bijvoorbeeld 6%/4 voor kwartaalbetalingen met een rentepercentage van 6%", "ad": "is het totale aantal betalingstermijnen voor de lening of de investering!is de betaling die iedere termijn wordt verricht. Dit bedrag kan gedurende de looptijd van de investering niet worden gewijzigd!is de huidige waarde: het totaalbedrag dat een reeks toekomstige betalingen op dit moment waard is!is de toekomstige waarde of het kassaldo waarover u wilt beschikken als de laatste betaling is gedaan. Als dit wordt weggelaten, wordt Tw = 0 gebruikt!is een logische waarde: betaling aan het begin van de termijn = 1; betaling aan het eind van de termijn = 0 of weggelaten!is uw schatting voor het rentepercentage. Als dit wordt weggelaten, wordt uitgegaan van Schatting = 0,1 (10 procent)"}, "RECEIVED": {"a": "(stortingsdatum; vervaldatum; investering; disc; [soort_jaar])", "d": "Berekent het bedrag dat op de vervaldatum wordt uitgekeerd voor volgestort waardepapier", "ad": "is de stortingsdatum van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is het bedrag dat in het waardepapier is geïnvesteerd!is het discontopercentage van het waardepapier!is het soort jaar waarop de berekening is gebaseerd"}, "RRI": {"a": "(perioden; hw; tw)", "d": "Geeft als resultaat een equivalent rentepercentage voor de groei van een investering", "ad": "is het aantal perioden voor de investering!is de huidige waarde van de investering!is de toekomstige waarde van de investering"}, "SLN": {"a": "(kosten; restwaarde; duur)", "d": "Berekent de lineaire afschrijving van activa over <PERSON><PERSON> bepaalde termijn", "ad": "zijn de aanschafkosten van de activa!is de resterende waarde van de activa aan het einde van de levensduur van de activa!is het aantal termijnen waarover de activa worden afgeschreven (ook wel levensduur van de activa genoemd)"}, "SYD": {"a": "(kosten; restwaarde; duur; termijn)", "d": "<PERSON><PERSON><PERSON> de afschrijving van activa over een bepa<PERSON>e termijn met be<PERSON><PERSON>-Of-The-Years-Digit'-methode", "ad": "zijn de aanschafkosten voor de activa!is de resterende waarde van de activa aan het einde van de levensduur van de activa!is het aantal termijnen waarover de activa worden afgeschreven (ook wel levensduur van de activa genoemd)!is de termijn. Termijn moet in dezelfde eenheden worden opgegeven als duur"}, "TBILLEQ": {"a": "(stortingsdatum; vervaldatum; disc)", "d": "Berekent het rendement op schatkistpapier op dezelfde manier waarop het rendement op obligaties wordt berekend", "ad": "is de stortingsdatum van het schatkistpapier, uitgedrukt in een serieel getal!is de vervaldatum van het schatkistpapier, uitgedrukt in een serieel getal!is het discontopercentage van het schatkistpapier"}, "TBILLPRICE": {"a": "(stortingsdatum; vervaldatum; disc)", "d": "Bepaalt de prijs per 100 euro nominale waarde voor schatkistpapier", "ad": "is de stortingsdatum van het schatkistpapier, uitgedrukt in een serieel getal!is de vervaldatum van het schatkistpapier, uitgedrukt in een serieel getal!is het discontopercentage van het schatkistpapier"}, "TBILLYIELD": {"a": "(stortingsdatum; vervaldatum; prijs)", "d": "<PERSON><PERSON><PERSON> het rendement van schatkistpapier", "ad": "is de stortingsdatum van het schatkistpapier, uitgedrukt in een serieel getal!is de vervaldatum van het schatkistpapier, uitgedrukt in een serieel getal!is de prijs van het schatkistpapier per 100 euro nominale waarde"}, "VDB": {"a": "(kosten; restwaarde; duur; begin-periode; einde-periode; [factor]; [geen-omschakeling])", "d": "<PERSON><PERSON><PERSON> de afschrijving van activa over een opgegeven periode, ook delen van perioden, met be<PERSON><PERSON> van de 'variable declining balance'-methode of met een andere methode die u opgeeft", "ad": "zijn de aanschafkosten van de activa!is de resterende waarde van de activa aan het einde van de levensduur van de activa!is het aantal termijnen waarover de activa worden afgeschreven (ook wel levensduur van de activa genoemd)!is het begin van een periode waarvoor u de afschrijving wilt berekenen. Dit moet in dezelfde eenheden zijn als Duur!is het einde van een periode waarvoor u de afschrijving wilt berekenen. Dit moet in dezelfde eenheden zijn als Duur!is de snelheid waarmee wordt afgeschreven. Als dit wordt weggelaten, wordt uitgegaan van 2 ('double declining balance'-methode)!ONWAAR of weggelaten = overschakelen op een lineaire afschrijving wanneer deze afschrijving groter is dan de afschrijving volgens de Declining Balance-methode, WAAR = niet overs"}, "XIRR": {"a": "(waarden; datums; [schatting])", "d": "Berekent de interne rentabiliteit voor een geplande serie van cashflows", "ad": "is een reeks cashflows die correspondeert met een geplande serie betalingsdatums!is een geplande serie betalingsdatums die correspondeert met de cashflowbetalingen!is een getal waarvan u denkt dat het in de buurt komt van het resultaat van IR2"}, "XNPV": {"a": "(rente; waarden; datums)", "d": "Berekent de netto huidige waarde van een geplande serie cashflows", "ad": "is het discontopercentage dat moet worden toegepast op de cashflows!is een reeks cashflows die correspondeert met een geplande serie betalingsdatums!is een schema van betalingsdatums die correspondeert met de geplande cashflowbetalingen"}, "YIELD": {"a": "(stortingsdatum; vervaldatum; rente; prijs; aflossingsprijs; frequentie; [soort_jaar])", "d": "Berekent het rendement van een waardepapier waarvan de rente periodiek wordt uitgekeerd", "ad": "is de stortings<PERSON><PERSON> van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is het jaarlijkse rentepercentage van het waardepapier!is de prijs van het waardepapier per 100 euro nominale waarde!is de aflossingsprijs van het waardepapier per 100 euro nominale waarde!is het aantal couponuitbetalingen per jaar!is het soort jaar waarop de berekening is gebaseerd"}, "YIELDDISC": {"a": "(stortingsdatum; vervaldatum; prijs; aflossingsprijs; [soort_jaar])", "d": "Berekent het jaarlijkse rendement van verdisconteerd waardepapier, bij<PERSON><PERSON><PERSON><PERSON> schatkistpapier", "ad": "is de stortings<PERSON><PERSON> van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van een waardepapier, uitgedrukt in een serieel getal!is de prijs van het waardepapier per 100 euro nominale waarde!is de aflossingsprijs van het waardepapier per 100 euro nominale waarde!is het soort jaar waarop de berekening is gebaseerd"}, "YIELDMAT": {"a": "(stortingsdatum; vervaldatum; uitgifte; rente; prijs; [soort_jaar])", "d": "Berekent het jaarlijkse rendement van waardepapier waarvan de rente op de vervaldatum wordt uitgekeerd", "ad": "is de stortings<PERSON><PERSON> van het waardepapier, uitgedrukt in een serieel getal!is de vervaldatum van het waardepapier, uitgedrukt in een serieel getal!is de dag van uitgifte van het waardepapier, uitgedrukt in een serieel getal!is het rentepercentage van het waardepapier op de dag van uitgifte!is de prijs van het waardepapier per 100 euro nominale waarde!Is het soort jaar waarop de berekening is gebaseerd"}, "ABS": {"a": "(getal)", "d": "Geeft als resultaat de absolute waarde van een getal. Dit is het getal zonder het teken", "ad": "is het reële getal waarvan u de absolute waarde wilt weten"}, "ACOS": {"a": "(getal)", "d": "Geeft als resultaat de boogcosinus van een getal in radialen, in het bereik 0 tot pi. De boogcosinus is de hoek waarvan de cosinus Getal is", "ad": "is de cosinus waarvan u de hoek wilt weten. Het argument moet een waarde zijn tussen -1 en 1"}, "ACOSH": {"a": "(getal)", "d": "Berekent de inverse cosinus hyperbolicus van een getal", "ad": "is een re<PERSON><PERSON> getal dat groter is dan of gelijk is aan 1"}, "ACOT": {"a": "(getal)", "d": "Geeft als resultaat de boogcotangens van een getal in het bereik 0 tot Pi radialen.", "ad": "is de cotangens van de gewenste hoek"}, "ACOTH": {"a": "(getal)", "d": "Geeft als resultaat de inverse cotangens hyperbolicus van een getal", "ad": "is de cotangens hyperbolicus van de gewenste hoek"}, "AGGREGATE": {"a": "(functie_getal; opties; verw1; ...)", "d": "Geeft als resultaat een statistische waarde in een lijst of database", "ad": "is een getal tussen 1 en 19 dat de samenvattingsfunctie voor de statistische waarde aangeeft.!is het getal 0 tot en met 7 waarmee de waarden worden opgegeven die moeten worden genegeerd voor de statistische waarde!is de matrix of een bereik met numerieke gegevens op basis waarvan u de statistische waarde wilt berekenen!geeft de positie in de matrix aan; op k-1 na grootste, op k-1 na kleinste, k-percentiel of k-kwartiel.!is het getal tussen 1 en 19 dat de samenvattingsfunctie voor de statistische waarde aangeeft.!is de optie (0=niets negeren,1=verborgen rijen negeren,2=foutwaarden negeren,3=foutwaarden en verborgen rijen negeren)!zijn 1 tot 253 bereiken of verwijzingen waarvoor u de statistische waarde wilt berekenen"}, "ARABIC": {"a": "(tekst)", "d": "Converteert een Romeins cijfer naar een Arabisch cijfer", "ad": "is het <PERSON><PERSON>e cijfer dat u wilt converteren"}, "ASC": {"a": "(tekst)", "d": "Voor talen met DBCS-tekensets (Double Byte Character Set). W<PERSON><PERSON>zigt de functie tekens met volledige breedte (dubbel-byte) in tekens met halve breedte (enkel-byte)", "ad": "is de tekst waarvan u een deel wilt wijzigen"}, "ASIN": {"a": "(getal)", "d": "<PERSON><PERSON><PERSON> de boogs<PERSON> van een getal, in het bereik -pi/2 tot pi/2", "ad": "is de sinus waarvan u de hoek wilt berekenen. Het argument moet een getal tussen -1 en 1 zijn"}, "ASINH": {"a": "(getal)", "d": "Berekent de inverse sinus hyperbolicus van een getal", "ad": "is een re<PERSON><PERSON> getal dat groter is dan of gelijk is aan 1"}, "ATAN": {"a": "(getal)", "d": "<PERSON><PERSON><PERSON> de boogtangens van een getal, in het bereik -pi/2 tot pi/2", "ad": "is de tangens waarvan u de hoek wilt berekenen"}, "ATAN2": {"a": "(x_getal; y_getal)", "d": "Berekent de boogtangens van de x- en y-coördinaten in radialen, tussen -pi en pi, met -pi uitgesloten", "ad": "is de x-coördinaat van het punt!is de y-coördinaat van het punt"}, "ATANH": {"a": "(getal)", "d": "Berekent de inverse tangens hyperbolicus van een getal", "ad": "is een re<PERSON><PERSON> getal tussen -1 en 1"}, "BASE": {"a": "(getal; grondtal; [min_lengte])", "d": "Converteert een getal in een tekstweergave met het opgegeven grondtal (basis)", "ad": "is het getal dat u wilt converteren!is het basisgrondtal waarnaar u het getal wilt converteren!is de minimale lengte van de geretourneerde tekenreeks. Als weggelaten voorloopnullen niet worden toegevoegd"}, "CEILING": {"a": "(getal; significantie)", "d": "<PERSON>dt een getal naar boven af op het dichtstbijzijnde significante veelvoud", "ad": "is de waarde die u wilt afronden!is het veelvoud waarop u wilt afronden"}, "CEILING.MATH": {"a": "(getal; [significantie]; [modus])", "d": "<PERSON>dt een getal naar boven af tot op het dichtstbijzijnde gehele getal of op het dichtstbijzijnde veelvoud van significantie", "ad": "is de waarde die u wilt afronden!is het veelvoud waarop u wilt afronden!wanneer deze functie is opgegeven en niet nul is, wordt er naar boven afgerond"}, "CEILING.PRECISE": {"a": "(getal; [significantie])", "d": "Geeft als resultaat een getal dat is afgerond naar boven op het dichtstbijzijnde gehele getal of het dichtstbijzijnde meervoud van <PERSON> significantie", "ad": "is de waarde die u wilt afronden!is het veelvoud waarop u wilt afronden"}, "COMBIN": {"a": "(getal; aantal-gekozen)", "d": "Geeft als resultaat het aantal combinaties voor een gegeven aantal objecten", "ad": "is het aantal objecten!is het aantal objecten in elke combinatie"}, "COMBINA": {"a": "(getal; aantal_gekozen)", "d": "Geeft als resultaat het aantal combinaties met herhalingen voor een opgegeven aantal items", "ad": "is het totale aantal items!is het aantal items in elke combinatie"}, "COS": {"a": "(getal)", "d": "<PERSON><PERSON><PERSON> de cosinus van een getal", "ad": "is de hoek in radialen waarvan u de cosinus wilt berekenen"}, "COSH": {"a": "(getal)", "d": "<PERSON><PERSON><PERSON> de cosinus hyperbolicus van een getal", "ad": "is een re<PERSON><PERSON> getal"}, "COT": {"a": "(getal)", "d": "Geeft als resultaat de cotangens van een hoek", "ad": "is de hoek in radialen waarvoor u de cotangens wilt berekenen"}, "COTH": {"a": "(getal)", "d": "Geeft als resultaat de cotangens hyperbolicus van een getal", "ad": "is de hoek in radialen waarvan u de cotangens hyperbolicus wilt berekenen"}, "CSC": {"a": "(getal)", "d": "Geeft als resultaat de cosecans van een hoek", "ad": "is de hoek in radialen waarvoor u de cosecans wilt berekenen"}, "CSCH": {"a": "(getal)", "d": "Geeft als resultaat de cosecans hyperbolicus van een hoek", "ad": "is de hoek in radialen waarvoor u de cosecans hyperbolicus wilt berekenen"}, "DECIMAL": {"a": "(getal; grondtal)", "d": "Converteert een tekstweergave van een getal in een bepaalde basis naar een decimaal getal", "ad": "is het getal dat u wilt converteren!is het basisgrondtal van het getal dat u converteert"}, "DEGREES": {"a": "(hoek)", "d": "Converteert radialen naar graden", "ad": "is de hoek in radialen die u wilt converteren"}, "ECMA.CEILING": {"a": "(getal; significantie)", "d": "<PERSON>dt een getal naar boven af op het dichtstbijzijnde significante veelvoud", "ad": "is de waarde die u wilt afronden!is het veelvoud waarop u wilt afronden"}, "EVEN": {"a": "(getal)", "d": "Rondt een positief getal naar boven af, en een negatief getal naar beneden, op het dichtstbijzijnde gehele even getal", "ad": "is de waarde die moet worden afgerond"}, "EXP": {"a": "(getal)", "d": "Verheft e tot de macht van het gegeven getal", "ad": "is de exponent die wordt toegepast op het grondtal e. De constante e is gelijk aan 2,71828182845904, de basis van de natuurlijke logaritme"}, "FACT": {"a": "(getal)", "d": "Berekent de faculteit van een getal. Dit is gelijk aan 1*2*3*...*Getal", "ad": "is het niet-negatieve getal waarvan u de faculteit wilt bepalen"}, "FACTDOUBLE": {"a": "(getal)", "d": "Berekent de dubbele faculteit van een getal", "ad": "is de waarde waarvoor u de dubbele faculteit wilt bepalen"}, "FLOOR": {"a": "(getal; significantie)", "d": "<PERSON>dt een getal naar beneden af op het dichtstbijzijnde significante veelvoud", "ad": "is de numerieke waarde die u wilt afronden!is het veelvoud waarop u wilt afronden. <PERSON><PERSON><PERSON> Getal als Significantie moeten beide positief of beide negatief zijn"}, "FLOOR.PRECISE": {"a": "(getal; [significantie])", "d": "Geeft als resultaat een getal dat naar beneden is afgerond op het dichtstbijzijnde gehele getal of het dichtstbijzijnde significante veelvoud", "ad": "is de waarde die u wilt afronden!is het veelvoud waarop u wilt afronden"}, "FLOOR.MATH": {"a": "(getal; [significantie]; [modus])", "d": "<PERSON>dt het getal naar beneden af op het dichtstbijzijnde gehele getal of op het dichtstbijzijnde veelvoud van significantie", "ad": "is de waarde die u wilt afronden!is het veelvoud waarop u wilt afronden!wanneer deze functie is opgegeven en niet nul is, wordt er naar beneden afgerond"}, "GCD": {"a": "(complex_getal1; [complex_getal2]; ...)", "d": "Geeft als resultaat de grootste algemene deler", "ad": "zijn 1 tot 255 waarden"}, "INT": {"a": "(getal)", "d": "<PERSON>dt een getal naar beneden af op het dichtstbijzijnde gehele getal", "ad": "is het reële getal dat u naar beneden wilt afronden op een geheel getal"}, "ISO.CEILING": {"a": "(getal; significantie)", "d": "Geeft als resultaat een getal dat is afgerond naar boven op het dichtstbijzijnde gehele getal of het dichtstbijzijnde meervoud van de significantie. Het getal wordt afgerond ongeacht het teken van het getal. Als het getal of de significantie echter nul is, is nul het resultaat.", "ad": "is de waarde die u wilt afronden!is het veelvoud waarop u wilt afronden"}, "LCM": {"a": "(complex_getal1; [complex_getal2]; ...)", "d": "Berekent het kleinste gemene veelvoud", "ad": "zijn 1 tot 255 waarden waarvoor u het kleinste gemene veelvoud wilt berekenen"}, "LN": {"a": "(getal)", "d": "Berekent de natuurlijke logaritme van een getal", "ad": "is het positieve reële getal waarvan u de natuurlijke logaritme wilt berekenen"}, "LOG": {"a": "(getal; [grondtal])", "d": "<PERSON><PERSON><PERSON> de logaritme van een getal met het door u opgegeven grondtal", "ad": "is het positieve reële getal waarvan u de logaritme wilt berekenen!is het grondtal van de logaritme. Als dit wordt weggelaten, wordt uitgegaan van 10"}, "LOG10": {"a": "(getal)", "d": "<PERSON><PERSON><PERSON> de logaritme met grondtal 10 van een getal", "ad": "is het positieve reële getal waarvan u de logaritme met grondtal 10 wilt berekenen"}, "MDETERM": {"a": "(matrix)", "d": "Berekent de determinant van een matrix", "ad": "is een numerieke matrix met een gelijk aantal rijen en kolommen. Dit is een cel<PERSON><PERSON>k of een matrixconstante"}, "MINVERSE": {"a": "(matrix)", "d": "Berekent de inverse van een matrix die is opgeslagen in een matrix", "ad": "is een numerieke matrix met een gelijk aantal rijen en kolommen. Dit is een cel<PERSON><PERSON>k of een matrixconstante"}, "MMULT": {"a": "(matrix1; matrix2)", "d": "Berekent het product van twee matrices, een matrix met hetzelfde aantal rijen als matrix1 en hetzelfde aantal kolommen als matrix2", "ad": "is de eerste matrix met getallen die u wilt vermenigvuldigen. Deze matrix moet hetzelfde aantal kolommen hebben als het aantal rijen in matrix2"}, "MOD": {"a": "(getal; deler)", "d": "Geeft als resultaat het restgetal bij de deling van een getal door een deler", "ad": "is het getal waarvan u na deling het restgetal wilt bepalen!is het getal waardoor u Getal wilt delen"}, "MROUND": {"a": "(getal; veelvoud)", "d": "Geeft een getal dat is afgerond op het gewenste veelvoud", "ad": "is de waarde die moet worden afgerond!is het veelvoud waarop u het getal wilt afronden"}, "MULTINOMIAL": {"a": "(complex_getal1; [complex_getal2]; ...)", "d": "Berekent de multinomiaal van een set getallen", "ad": "zijn 1 tot 255 waarden waarvoor u de multinomiaal wilt berekenen"}, "MUNIT": {"a": "(afmeting)", "d": "Geeft als resultaat een eenheidmatrix van de opgegeven afmeting", "ad": "is een geheel getal waarmee de afmeting wordt opgegeven van de eenheidmatrix die u wilt retourneren"}, "ODD": {"a": "(getal)", "d": "Rondt de absolute waarde van een positief getal naar boven af, en een negatief getal naar beneden, op het dichtstbijzijnde oneven gehele getal", "ad": "is de waarde die moet worden afgerond"}, "PI": {"a": "()", "d": "Geeft als resultaat de waarde van de rekenkundige constante pi (3,14159265358979), nauw<PERSON><PERSON><PERSON> tot op 15 cijfers", "ad": ""}, "POWER": {"a": "(getal; macht)", "d": "Geeft als resultaat een getal verheven tot een macht", "ad": "is een re<PERSON>el getal dat u tot een bepaalde macht wilt verheffen!is de exponent van de machtsverheffing"}, "PRODUCT": {"a": "(getal1; [getal2]; ...)", "d": "Vermenigvuldigt de getallen die zijn opgegeven als argumenten met <PERSON><PERSON><PERSON>", "ad": "zijn maximaal 255 getallen, logische waarden of tekstgetallen van getallen die u wilt vermenigvuldigen"}, "QUOTIENT": {"a": "(teller; noemer)", "d": "Geeft de uitkomst van een deling in gehele getallen", "ad": "is de teller of het deeltal!is de noemer of deler"}, "RADIANS": {"a": "(hoek)", "d": "Converteert graden naar radialen", "ad": "is een hoek in graden die u wilt converteren"}, "RAND": {"a": "()", "d": "Geeft als resultaat een will<PERSON> getal, geli<PERSON><PERSON><PERSON> verdeeld, dat groter is dan of gelijk is aan 0 en kleiner is dan 1 (wijzigt bij herberekening)", "ad": ""}, "RANDARRAY": {"a": "([rijen]; [kolommen]; [min]; [max]; [geheel])", "d": "Retour<PERSON>t een matrix met will<PERSON><PERSON><PERSON> getallen", "ad": "het aantal rijen in de geretourneerde matrix!het aantal kolommen in de geretourneerde matrix!het minimumaantal dat u geretourneerd wilt!het maximumaantal dat u geretourneerd wilt!retourneert een geheel of een decimale waarde. WAAR voor een geheel getal, ONWAAR voor een decimaal getal"}, "RANDBETWEEN": {"a": "(laagst; hoogst)", "d": "Geeft een willekeurig getal tussen de getallen die u hebt opgegeven", "ad": "is het kleinste gehele getal dat ASELECTTUSSEN als resultaat geeft!is het grootste gehele getal dat ASELECTTUSSEN als resultaat geeft"}, "ROMAN": {"a": "(getal; [type_getal])", "d": "Converteert Arabische cijfers naar Romeinse cijfers, als tekst", "ad": "is het getal met Arabische cijfers dat u wilt converteren!is een getal waarmee u het type Romeinse cijfers kiest dat u wilt gebruiken."}, "ROUND": {"a": "(getal; aantal-decimalen)", "d": "Rondt een getal af op het opgegeven aantal decimalen", "ad": "is het getal dat u wilt afronden!is het aantal decimalen waarop u wilt afronden. Negatieve waarden ronden links van de komma af, nul rondt af op het dichtstbijzijnde gehele getal"}, "ROUNDDOWN": {"a": "(getal; aantal-decimalen)", "d": "<PERSON>dt de absolute waarde van een getal naar beneden af", "ad": "is een re<PERSON>el getal dat u naar beneden wilt afronden!is het aantal decimalen waarop u het getal wilt afronden. Negatieve waarden ronden links van de komma af, nul of weggelaten rondt af op het dichtstbijzijnde gehele getal"}, "ROUNDUP": {"a": "(getal; aantal-decimalen)", "d": "<PERSON>dt de absolute waarde van een getal naar boven af", "ad": "is een re<PERSON>el getal dat u naar boven wilt afronden!is het aantal decimalen waarop u het getal wilt afronden. Negatieve waarden ronden links van de komma af, nul of weggelaten rondt af op het dichtstbijzijnde gehele getal"}, "SEC": {"a": "(getal)", "d": "Geeft als resultaat de secans van een hoek", "ad": "is de hoek in radialen waarvoor u de secans wilt berekenen"}, "SECH": {"a": "(getal)", "d": "Geeft als resultaat de secans hyperbolicus van een hoek", "ad": "is de hoek in radialen waarvoor u de secans hyperbolicus wilt berekenen"}, "SERIESSUM": {"a": "(x; n; m; coëff<PERSON>en)", "d": "Be<PERSON><PERSON> de som van een machtreeks die is gebaseerd op de formule", "ad": "is de invoerwaarde voor de machtreeks!is de macht tot waartoe u de eerste x-waarde in de reeks wilt verheffen!is de stap waarmee n wordt verhoogd in elke term van de reeks!is een verzameling coëfficiënten waarmee elke opeenvolgende macht van x wordt vermenigvuldigd"}, "SIGN": {"a": "(getal)", "d": "Geeft als resultaat het teken van een getal: 1 als het getal positief is, nul als het getal nul is en -1 als het getal negatief is", "ad": "is een re<PERSON><PERSON> getal"}, "SIN": {"a": "(getal)", "d": "Berekent de sinus van de opgegeven hoek", "ad": "is de hoek in radialen waarvan u de sinus wilt berekenen. Graden * PI()/180 = radialen"}, "SINH": {"a": "(getal)", "d": "<PERSON><PERSON>ent de sinus hyperbolicus van een getal", "ad": "is een re<PERSON><PERSON> getal"}, "SQRT": {"a": "(getal)", "d": "Be<PERSON>ent de vierkantswortel van een getal", "ad": "is het getal waarvan u de vierkantswortel wilt berekenen"}, "SQRTPI": {"a": "(getal)", "d": "Berekent de vierkantswortel van (getal * pi)", "ad": "is het getal waarmee pi wordt vermenigvuldigd"}, "SUBTOTAL": {"a": "(functie_getal; verw1; ...)", "d": "Be<PERSON>ent een subtotaal in een lijst of database", "ad": "is een getal van 1 tot 11 waarmee u bepaalt welke samenvattingsfunctie u wilt gebruiken voor de berekening van het subtotaal.!zijn maximaal 254 bereiken of verwijzingen waarvoor u een subtotaal wilt berekenen."}, "SUM": {"a": "(getal1; [getal2]; ...)", "d": "Telt de getallen in een celbereik op", "ad": "zijn maximaal 255 getallen die u wilt optellen. Logische waarden en tekst in cellen worden genegeerd, behalve als deze als argumenten worden opgegeven"}, "SUMIF": {"a": "(bereik; criterium; [optelbereik])", "d": "Telt de cellen bij elkaar op die voldoen aan het criterium dat of de voorwaarde die u hebt ingesteld", "ad": "is het celbereik dat u wilt evalueren!is de voorwa<PERSON>e of het criterium in de vorm van een getal, een expressie of tekst met behulp waarvan de cellen voor de optelsom worden geselecteerd!zijn de werkelijke cellen waarop u de bewerking wilt uitvoeren. Als dit wordt weggelaten, worden de cellen in het bereik gebruikt"}, "SUMIFS": {"a": "(optelbereik; criteriumbereik; criteria; ...)", "d": "Telt het aantal cellen op dat wordt gespecificeerd door een gegeven set voorwaarden of criteria", "ad": "is het werkelijke aantal cellen dat wordt opgeteld.!is het celbereik dat u wilt evalueren voor de voorwaarde in kwestie!is de voorwaarde of het criterium in de vorm van een getal, expressie of tekst waarmee wordt aangegeven welke cellen worden opgeteld"}, "SUMPRODUCT": {"a": "(matrix1; [matrix2]; [matrix3]; ...)", "d": "Geeft als resultaat de som van de producten van corresponderende bereiken of matrices", "ad": "zijn minimaal 2 en maximaal 255 matrices waarvan u de elementen wilt vermenigvuldigen en vervolgens optellen. Alle matrices moeten dezelfde afmetingen hebben"}, "SUMSQ": {"a": "(getal1; [getal2]; ...)", "d": "<PERSON><PERSON><PERSON> de som van de kwadraten van de argumenten. Dit kunnen getallen zijn of namen, matrices of verwi<PERSON>zingen naar cellen die getallen bevatten", "ad": "zijn 1 tot 255 getallen, matrices, namen of matrixverwijzingen waarvan u de som van de kwadraten wilt bepalen"}, "SUMX2MY2": {"a": "(x-matrix; y-matrix)", "d": "Telt de verschillen tussen de kwadraten van twee corresponderende bereiken of matrices op", "ad": "is de eerste matrix of het eerste gegevensbereik. Dit kan een getal zijn of een naam, matrix of verwijzing die getallen bevat!is de tweede matrix of het tweede gegevensbereik. Dit kan een getal zijn of een naam, matrix of verwijzing die getallen bevat"}, "SUMX2PY2": {"a": "(x-matrix; y-matrix)", "d": "Geeft het somtotaal van de som van de kwadraten van waarden in twee corresponderende bereiken of matrices als resultaat", "ad": "is de eerste matrix of het eerste gegevensbereik. Dit kan een getal zijn of een naam, matrix of verwijzing die getallen bevat!is de tweede matrix of het tweede gegevensbereik. Dit kan een getal zijn of een naam, matrix of verwijzing die getallen bevat"}, "SUMXMY2": {"a": "(x-matrix; y-matrix)", "d": "Telt de kwadraten van de verschillen tussen twee corresponderende bereiken of matrices op", "ad": "is de eerste matrix of het eerste gegevensbereik. Dit kan een getal zijn of een naam, matrix of verwijzing die getallen bevat!is de tweede matrix of het tweede gegevensbereik. Dit kan een getal zijn of een naam, matrix of verwijzing die getallen bevat"}, "TAN": {"a": "(getal)", "d": "<PERSON><PERSON><PERSON> de tangens van een getal", "ad": "is de hoek in radialen waarvan u de tangens wilt berekenen. Graden * PI()/180 = radialen"}, "TANH": {"a": "(getal)", "d": "<PERSON><PERSON>ent de tangens hyperbolicus van een getal", "ad": "is een re<PERSON><PERSON> getal"}, "TRUNC": {"a": "(getal; [aantal-decimalen])", "d": "Kapt een getal af tot een geheel getal door het decimale (of gebroken) gede<PERSON>te van het getal te verwijderen", "ad": "is het getal dat u wilt afkappen!is een getal dat de precisie van de afkapping aangeeft. Als dit wordt weggelaten, wordt uitgegaan van 0 (nul)"}, "ADDRESS": {"a": "(rij_getal; kolom_getal; [abs_getal]; [A1]; [blad_tekst])", "d": "Geeft als resultaat een celverwi<PERSON>zing, in de vorm van tekst, gegeven bepaalde rij- en kolomnummers", "ad": "is het rijnummer voor de celverwijzing. Rij_nummer = 1 voor rij 1!is het kolomnummer voor de celverwijzing. Bij<PERSON>orbeeld, Kolom_nummer = 4 voor kolom D!geeft aan welk type verwijzing als resultaat moet worden gegeven: absoluut = 1, absolute rij/relatieve kolom = 2, relatieve rij/absolute kolom = 3, relatief = 4!is een logische waarde die aangeeft welk verwijzingstype wordt gebruikt: A1 = 1 of WAAR, R1K1 = 0 of ONWAAR!is de tekst die de naam aangeeft van het werkblad of macroblad dat moet worden gebruikt als externe verwijzing"}, "CHOOSE": {"a": "(index_getal; waarde1; [waarde2]; ...)", "d": "Kiest een waarde uit de lijst met waarden op basis van een indexnummer", "ad": "geeft aan welke waarde geselecteerd is. Index_getal moet een getal zijn tussen 1 en 254 of een formule of een verwijzing naar een getal tussen 1 en 254!zijn maximaal 254 getallen, cel<PERSON><PERSON><PERSON><PERSON><PERSON>, gede<PERSON><PERSON><PERSON>e namen, formules, functies of tekst waaruit de functie KIEZEN kiest"}, "COLUMN": {"a": "([verw])", "d": "Geeft als resultaat het kolomnummer van een verwijzing", "ad": "is de cel of de reeks aaneengesloten cellen waarvan u het kolomnummer wilt weten. Als dit wordt weggelaten, wordt de cel gebruikt die de functie KOLOM bevat"}, "COLUMNS": {"a": "(matrix)", "d": "Geeft als resultaat het aantal kolommen in een matrix of een verwijzing", "ad": "is een matrix, een matrixformule of een verwijzing naar een celbereik waarvan u het aantal kolommen wilt weten"}, "FORMULATEXT": {"a": "(ver<PERSON><PERSON><PERSON>)", "d": "Geeft als resultaat een formule als tekenreeks", "ad": "is een verwijzing naar een formule"}, "HLOOKUP": {"a": "(zoekwaarde; tabelmatrix; rij-index_getal; [bereik])", "d": "<PERSON><PERSON> in de bovenste rij van een tabel of matrix met waarden naar waarden en geeft als resultaat de waarde in dezelfde kolom uit een opgegeven kolom", "ad": "is de waarde in de eerste rij van de tabel en kan een waarde, een verwi<PERSON><PERSON>, of een tekenreeks zijn!is een tabel met tekst,getallen, of logische waarden waarin u naar gegevens zoekt. Tabelmatrix kan een referentie zijn naar een bereik of de naam van een bereik!is het rijnummer in de opgegeven tabelmatrix waaruit u de overeenkomende waarde wilt halen. De eerste waarderij in de tabel is rij 1!is een logische waarde: voor de beste overeenkomst in de bovenste rij (in oplopende volgorde gesorteerd) = WAAR of weggelaten; voor een exacte overeenkomst = ONWAAR"}, "HYPERLINK": {"a": "(locatie_link; [mak<PERSON><PERSON><PERSON><PERSON>_naam])", "d": "Maakt een snelkoppeling of sprong die een document opent dat is opgeslagen op de harde schijf, op een netwerkserver of op internet", "ad": "is de tekst met het volledige pad en de volledige bestandsnaam van het bestand dat moet worden geopend, de locatie van een harde schijf, een UNC-adres of een URL-pad!is het getal, de tekst of de functie die in de cel wordt weergegeven. Als dit wordt weggelaten, geeft de cel de tekst van de Locatie_koppeling weer"}, "INDEX": {"a": "(matrix; rij_getal; [kolom_getal]!verw; rij_getal; [kolom_getal]; [bereik_getal])", "d": "Geeft als resultaat een waarde of verwi<PERSON><PERSON> van de cel op het snijpunt van een bepaalde rij en kolom in een opgegeven bereik", "ad": "is een cel<PERSON><PERSON>k of een matrixconstante.!selecteert de rij in de opgegeven matrix waaruit een waarde moet worden opgehaald. Als dit wordt weggelaten, moet Kolom_getal worden opgegeven!selecteert de kolom in de opgegeven matrix waaruit u een waarde wilt ophalen. Als dit wordt weggelaten, moet Rij_getal worden opgegeven!is een verwijzing naar een of meer celbereiken!selecteert de rij in de matrix of verwijzing waaruit een waarde moet worden opgehaald. Als dit wordt weggelaten, moet Kolom_getal worden opgegeven!selecteert de kolom in de matrix of verwijzing waaruit u een waarde wilt ophalen. Als dit wordt weggelaten, moet Rij_getal worden opgegeven!selecteert een bereik in verw waaruit u een waarde wilt ophalen. Het eerste gebied dat u selecteert of invoert, is gebied 1, het tweede gebied is gebied 2, enzovoort"}, "INDIRECT": {"a": "(verw_tekst; [A1])", "d": "Geeft als resultaat een verwijzing aangegeven door een tekstwaarde", "ad": "is een verwijzing naar een cel die een A1- of een R1K1-verwijzing bevat, een naam die is gedefinieerd als een verwijzing of een verwijzing naar een cel in de vorm van een tekenreeks!is een logische waarde die aangeeft welk type verwijzing de cel in verw_tekst bevat: ONWAAR = R1K1, WAAR of weggelaten = A1"}, "LOOKUP": {"a": "(zoekwa<PERSON>e; zoekvector; [resultaatvector]!zoekwaarde; matrix)", "d": "Zoekt een waarde uit een bereik met <PERSON><PERSON> rij of <PERSON><PERSON> kolom of uit een matrix. De functie is achterwaarts compatibel", "ad": "is de waarde die ZOEKEN in Zoekvector moet zoeken. Dit kan een getal zijn, tekst, een logische waarde of een naam of een verwijzing naar een waarde!is een celbereik dat slechts é<PERSON> rij of <PERSON>én kolom bevat met teks<PERSON>, getallen of logische waarden, g<PERSON><PERSON><PERSON><PERSON> in oplopende volgorde!is een celbereik dat slechts één rij of één kolom bevat van dezelfde grootte als Zoekvector!is een waarde die ZOEKEN in Matrix moet zoeken. Dit kan een getal zijn, tekst, een logische waarde of een naam of een verwijzing naar een waarde!is een celbereik met tekst, getallen of logische waarden die u wilt vergelijken met Zoekwaarde"}, "MATCH": {"a": "(zoekwaarde; zoeken-matrix; [criteriumtype_getal])", "d": "Geeft als resultaat de relatieve positie in een matrix van een item dat overeenkomt met een opgegeven waarde in een opgegeven volgorde", "ad": "is de waarde die u gebruikt om een waarde in de matrix te zoeken. De waarde kan een getal zijn, tekst, een logische waarde of een verwijzing naar een van deze typen!is een aaneengesloten celbereik met mogelijke zoekwaarden. Dit kunnen matrices met waarden zijn of verwijzingen naar een matrix!is het getal 1, 0 of -1 dat aangeeft welke waarde het resultaat moet hebben."}, "OFFSET": {"a": "(verw; rijen; kolommen; [hoogte]; [breedte])", "d": "Geeft als resultaat een verwijzing naar een bereik dat een opgegeven aantal rijen en kolommen van een opgegeven verwijzing is", "ad": "is de verwijzing waarop u de verschuiving wilt baseren. Dit is een verwijzing naar een cel of een bereik a<PERSON>eng<PERSON>loten cellen!is het aantal rijen, omhoog of omlaag, waarnaar u de cel in de linkerbovenhoek van het resultaat wilt laten verwijzen!is het aantal kolommen, naar links of naar rechts, waarnaar u de cel in de linkerbovenhoek van het resultaat wilt laten verwijzen!is het aantal rijen dat u wilt dat het resultaat hoog is. Als dit wordt weggelaten, wordt uitgegaan van dezelfde hoogte als Verw!is het aantal kolommen dat u wilt dat het resultaat breed is. Als dit wordt weggelaten, wordt uitgegaan van dezelfde breedte als Verw"}, "ROW": {"a": "([verw])", "d": "Geeft als resultaat het rijnummer van een verwijzing", "ad": "is de cel of een enkel celbereik waarvan u het rijnummer wilt weten. Als dit wordt weggelaten, is het resultaat de cel die de functie RIJ bevat"}, "ROWS": {"a": "(matrix)", "d": "Geeft als resultaat het aantal rijen in een verwijzing of matrix", "ad": "is een matrix, een matrixformule of een verwijzing naar een celbereik waarvan u het aantal rijen wilt weten"}, "TRANSPOSE": {"a": "(matrix)", "d": "Converteert een verticaal celbereik naar een horizontaal bereik en omgekeerd", "ad": "is een celbereik op een werkblad of een matrix met waarden die u wilt transponeren"}, "UNIQUE": {"a": "(matrix; [per_kolom]; [exact_eenmaal])", "d": "Retourneert de unieke waarden uit een bereik of matrix.", "ad": "het bereik of de matrix waaruit unieke rijen of kolommen moeten worden geretourneerd!is een logische waarde: vergelijk rijen met elkaar en retourneer de unieke rijen = FALSE of weggelaten; vergelijk kolommen met elkaar en retourneer de unieke kolommen = TRUE!is een logische waarde: retourneer rijen of waarden uit de matrix die exact één keer voorkomen = TRUE; retourneer unieke rijen of kolommen uit de matrix = FALSE of weggelaten"}, "VLOOKUP": {"a": "(zoekwaarde; tabelmatrix; kolomindex_getal; [ben<PERSON><PERSON>])", "d": "Zoe<PERSON> in de meest linkse kolom van een matrix naar een bepaalde waarde en geeft als resultaat de waarde uit dezelfde rij in een opgegeven kolom. Standaard moet de tabel in oplopende volgorde worden gesorteerd", "ad": "is de waarde die u wilt zoeken in de eerste kolom van de matrix. Dit kan een waarde zijn, een verwijzing of een tekenreeks!is een tabel met tekst, getallen of logische waarden, waarin u naar gegevens zoekt. Tabelmatrix kan een verwijzing zijn naar een bereik of de naam van een bereik!is het nummer van de kolom in tabelmatrix waaruit u de waarde wilt ophalen. De eerste waardekolom in de tabel is kolom 1!is een logische waarde: WAAR of weggelaten = zoek de best mogelijke waarde in de eerste kolom (gesorteerd in oplopende volgorde), ONWAAR = de gevonden waarde moet exact overeenkomen"}, "XLOOKUP": {"a": "(zoekwaarde; zoeken-matrix; matrix_retourneren; [indien_niet_gevonden]; [overeenkomstmodus]; [zoekmodus])", "d": "Zoekt in een bereik of matrix naar een overeenkomst en retourneert het bijbehorende item uit een tweede bereik of matrix. Standaard wordt een exacte overeenkomst gebruikt", "ad": "is de waarde waarnaar moet worden gezocht!is de matrix of het bereik waarin moet worden gezocht!is de matrix of het bereik dat moet worden geretourneerd!wordt geretourneerd als er geen overeenkomst is gevonden!geef op hoe de zoekwaarde moet overeenko<PERSON> met de waarden in de zoeken-matrix!geef de zoek<PERSON> op die moet worden gebruikt. Standaard wordt een zoekopdracht van eerste naar laatste gebruikt"}, "CELL": {"a": "(infotype; [verwij<PERSON>])", "d": "Geeft als resultaat informatie over de op<PERSON>, locatie of inhoud van een cel", "ad": "dit is een tekstwaarde waarmee u opgeeft welk informatietype u voor de cel wilt ophalen!de cel waarover u informatie wilt ophalen"}, "ERROR.TYPE": {"a": "(foutwaarde)", "d": "<PERSON>ft als resultaat een nummer dat overeen<PERSON>t met een fout<PERSON>e.", "ad": "is de foutwaarde waarvan u het identificatienummer wilt weten. Dit kan een werkelijke foutwaarde zijn of een verwijzing naar een cel die een foutwaarde bevat"}, "ISBLANK": {"a": "(waarde)", "d": "Controleert of een verwijzing naar een lege cel verwijst en geeft als resultaat WAAR of ONWAAR", "ad": "is de cel of de naam die verwijst naar de cel die u wilt testen"}, "ISERR": {"a": "(waarde)", "d": "Controleert of een waarde een fout anders dan #N/B is en geeft als resultaat WAAR of ONWAAR", "ad": "is de waarde die u wilt testen. Waarde kan verwijzen naar een cel, een formule of een naam die verwijst naar een cel, formule of waarde"}, "ISERROR": {"a": "(waarde)", "d": "Controleert of een waarde een fout is en geeft als resultaat WAAR of ONWAAR", "ad": "is de waarde die u wilt testen. Waarde kan verwijzen naar een cel, een formule of een naam die verwijst naar een cel, formule of waarde"}, "ISEVEN": {"a": "(getal)", "d": "Resulteert in WAAR als het getal even is", "ad": "is de waarde die u wilt testen"}, "ISFORMULA": {"a": "(ver<PERSON><PERSON><PERSON>)", "d": "Controleert of een verwijzing verwijst naar een cel die een formule bevat en geeft WAAR of ONWAAR als resultaat", "ad": "is een verwijzing naar de cel die u wilt testen. Verwijzing kan een celverwijzing, formule of naam zijn die naar een cel verwijst"}, "ISLOGICAL": {"a": "(waarde)", "d": "Controleert of een waarde een logische waarde is (WAAR of ONWAAR), en geeft vervolgens WAAR of ONWAAR als resultaat", "ad": "is de waarde die u wilt testen. Waarde kan verwijzen naar een cel, een formule of een naam die verwijst naar een cel, formule of waarde"}, "ISNA": {"a": "(waarde)", "d": "Controleert of een waarde #N/B is en geeft als resultaat WAAR of ONWAAR", "ad": "is de waarde die u wilt testen. Waarde kan verwijzen naar een cel, een formule of een naam die verwijst naar een cel, formule of waarde"}, "ISNONTEXT": {"a": "(waarde)", "d": "Controleert of een waarde geen tekst is (lege cellen zijn geen tekst), en geeft WAAR of ONWAAR als resultaat", "ad": "is de waarde die u wilt testen: een cel, een formule of een naam die verwijst naar een cel, formule of waarde"}, "ISNUMBER": {"a": "(waarde)", "d": "Controleert of een waarde een getal is en geeft als resultaat WAAR of ONWAAR", "ad": "is de waarde die u wilt testen. Waarde kan verwijzen naar een cel, een formule of een naam die verwijst naar een cel, formule of waarde"}, "ISODD": {"a": "(getal)", "d": "Resulteert in WAAR als het getal oneven is", "ad": "is de waarde die u wilt testen"}, "ISREF": {"a": "(waarde)", "d": "Controleert of een waarde een verwijzing is en geeft als resultaat WAAR of ONWAAR", "ad": "is de waarde die u wilt testen. Waarde kan verwijzen naar een cel, een formule of een naam die verwijst naar een cel, formule of waarde"}, "ISTEXT": {"a": "(waarde)", "d": "Controleert of een waarde tekst is en geeft als resultaat WAAR of ONWAAR", "ad": "is de waarde die u wilt testen. Waarde kan verwijzen naar een cel, een formule of een naam die verwijst naar een cel, formule of waarde"}, "N": {"a": "(waarde)", "d": "Converteert een waarde die geen getal is naar een getal, datums naar seri<PERSON><PERSON> getallen, WAAR naar 1 en overige waarden naar 0 (nul)", "ad": "is de waarde die u wilt converteren"}, "NA": {"a": "()", "d": "Geeft als resultaat de foutwaarde #N/B (waarde niet be<PERSON>)", "ad": ""}, "SHEET": {"a": "([waarde])", "d": "Geeft als resultaat het bladnummer van het werkblad waarnaar wordt verwezen", "ad": "is de naam van een blad of een verwijzing waarvoor u het bladnummer wilt weten. Als dit wordt weggelaten, wordt het nummer van het blad met de functie geretourneerd."}, "SHEETS": {"a": "([verwi<PERSON><PERSON>])", "d": "Geeft als resultaat het aantal bladen in een verwijzing", "ad": "is een verwijzing waarvoor u wilt weten hoeveel bladen deze bevat. Als dit wordt weggelaten, wordt het aantal bladen in de werkmap met de functie geretourneerd."}, "TYPE": {"a": "(waarde)", "d": "Geeft als resultaat een geheel getal dat het gegevenstype van de waarde aangeeft: getal = 1; tekst = 2; logische waarde = 4, formule = 8; foutwaarde = 16; matrix = 64; samengestelde gegevens =128", "ad": "kan elke waarde zijn"}, "AND": {"a": "(logisch1; [logisch2]; ...)", "d": "Controleert of alle argumenten WAAR zijn. Als dit het geval is, wordt als resultaat WAAR gegeven", "ad": "zijn maximaal 255 voorwaarden waarvan u wilt testen of ze WAAR of ONWAAR zijn. De voorwaarden kunnen logische waarden, matrices of verwi<PERSON>zingen zijn"}, "FALSE": {"a": "()", "d": "Geeft als resultaat de logische waarde ONWAAR", "ad": ""}, "IF": {"a": "(logische-test; [waarde-als-waar]; [waarde-als-onwaar])", "d": "Controleert of er aan een voorwaarde is voldaan. Geeft een bepaalde waarde als resultaat als de opgegeven voorwaarde WAAR is en een andere waarde als deze ONWAAR is", "ad": "is een waarde of expressie die kan worden geëvalueerd als WAAR of ONWAAR!is de waarde die als resultaat wordt gegeven als logische-test WAAR is. Als dit wordt weggelaten, wordt als resultaat WAAR gegeven. U kunt maximaal zeven ALS-functies nesten!is de waarde die als resultaat wordt gegeven als logische-test ONWAAR is. Als dit wordt weggelaten, wordt als resultaat ONWAAR gegeven"}, "IFS": {"a": "(logische_test; waarde_indien_waar; ...)", "d": "Controleert of is voldaan aan een of meer voorwaarden en retourneert een waarde die overeenkomt met de eerste WAAR-voorwaarde", "ad": "is elke waarde of expressie die kan worden geëvalueerd als WAAR of ONWAAR!is de waarde die wordt geretourneerd indien de logische test WAAR is"}, "IFERROR": {"a": "(waarde; waarde_indien_fout)", "d": "Geeft als resultaat de waarde_indien_fout als de expressie een fout is en anders de waarde van de expressie zelf", "ad": "is een willekeurige waarde of expressie of verwijzing!is een willekeurige waarde of expressie of verwijzing"}, "IFNA": {"a": "(waarde; waarde_als_nvt)", "d": "Geeft als resultaat de waarde die u opgeeft als de expressie wordt omgezet in #N.v.t, anders wordt het resultaat van de expressie geretourneerd", "ad": "is een waarde of expressie of verwijzing!is een waarde of expressie of verwijzing"}, "NOT": {"a": "(logisch)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON> de waarde ONWAAR in WAAR, of WAAR in ONWAAR", "ad": "is een waarde of expressie die kan worden geëvalueerd als WAAR of ONWAAR"}, "OR": {"a": "(logisch1; [logisch2]; ...)", "d": "Controleert of een van de argumenten WAAR is, en geeft als resultaat WAAR of ONWAAR. Geeft alleen ONWAAR als resultaat als alle argumenten ONWAAR zijn", "ad": "zijn maximaal 255 voorwaarden die u wilt testen, die WAAR of ONWAAR kunnen zijn"}, "SWITCH": {"a": "(expressie; waarde1; resultaat1; [standaard_of_waarde2]; [resultaat2]; ...)", "d": "Evalueert een expressie met een lij<PERSON> met waarden en retourneert het resultaat dat overeenkomt met de eerste overeenkomende waarde. Als er geen overeenkomst is, wordt er een optionele standaardwaarde geretourneerd", "ad": "is een expressie die moet worden geëvalueerd!is een waarde die moet worden vergeleken met de expressie!is het resultaat dat moet worden geretourneerd als de overeenkomende waarde overeenkomt met de expressie"}, "TRUE": {"a": "()", "d": "Geeft als resultaat de logische waarde WAAR", "ad": ""}, "XOR": {"a": "(logisch1; [logisch2]; ...)", "d": "Geeft als resultaat een logische 'Exclusieve of' van alle argumenten", "ad": "zijn maximaal 254 voorwaarden waarvan u wilt testen of ze WAAR of ONWAAR zijn en kunnen logische waarden, matrices of ver<PERSON><PERSON>zing<PERSON> zijn"}, "TEXTBEFORE": {"a": "(tekst, scheiding<PERSON>ken, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Retourneert tekst voor scheidingstekens.", "ad": "De tekst waarin u wilt zoeken naar het scheidingsteken.!Het teken of de tekenreeks die als scheidingsteken moet worden gebruikt.!Het gewenste voorkomen van het scheidingsteken. De standaardwaarde is 1. Een negatief getal zoekt vanaf het einde.!Zoekt in de tekst naar een overeenstemmend scheidingsteken. Standaard wordt een hoofdlettergevoelige overeenkomst uitgevoerd.!Of het scheidingsteken moet overeenkomen met het einde van de tekst. Standaard komen ze niet overeen.!Geretourneerd als er geen overeenkomst is gevonden. Standaard wordt #n.v.t. geretourneerd."}, "TEXTAFTER": {"a": "(tekst, scheiding<PERSON>ken, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Retourneert tekst na scheidingstekens.", "ad": "De tekst waarin u wilt zoeken naar het scheidingsteken.!Het teken of de tekenreeks die als scheidingsteken moet worden gebruikt.!Het gewenste voorkomen van het scheidingsteken. De standaardwaarde is 1. Een negatief getal zoekt vanaf het einde.!Zoekt in de tekst naar een overeenstemmend scheidingsteken. Standaard wordt een hoofdlettergevoelige overeenkomst uitgevoerd.!Of het scheidingsteken moet overeenkomen met het einde van de tekst. Standaard komen ze niet overeen.!Geretourneerd als er geen overeenkomst is gevonden. Standaard wordt #n.v.t. geretourneerd."}, "TEXTSPLIT": {"a": "(tekst, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Hi<PERSON><PERSON> wordt tekst gesplitst in rijen of kolommen met scheidingstekens.", "ad": "De tekst die moet worden gesplitst.!Teken of tekenreeks om kolommen op te splitsen.!Teken of tekenreeks om rijen op te splitsen.!Of lege cellen moeten worden genegeerd. Standaard ingesteld op FALSE.!Zoekt in de tekst naar een scheidingstekenovereenkomst. Standaard wordt een hoofdlettergevoelige overeenkomst uitgevoerd.!De waarde die moet worden gebruikt voor opvulling. Standaard wordt #N/A gebruikt."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": " Hier<PERSON> wordt een rij- of kolomvector achter een opgegeven aantal waarden verpakt.", "ad": "De vector of verwijzing die moet worden verpakt.!Het maximum aantal waarden per rij.!De waarde waarmee moet worden opgevuld. De standaardwaarde is #N/A."}, "VSTACK": {"a": "(matrix1, [matrix2], ...)", "d": "Stapelt matrices verticaal in één matrix.", "ad": " Een matrix of verwijzing die moet worden gestapeld."}, "HSTACK": {"a": "(matrix1, [matrix2], ...)", "d": "Stapelt matrices horizontaal in één matrix.", "ad": "Een matrix of verwijzing die moet worden gestapeld."}, "CHOOSEROWS": {"a": "(matrix, row_num1, [row_num2], ...)", "d": "Retourneert rijen uit een matrix of verwijzing.", "ad": "De matrix of verwijzing die de rijen bevat die moeten worden geretourneerd.!Het nummer van de rij die moet worden geretourneerd."}, "CHOOSECOLS": {"a": "(matrix, col_num1, [col_num2], ...)", "d": "Retourneert kolommen uit een matrix of verwi<PERSON>zing.", "ad": "De matrix of verwijzing die de kolommen bevat die moeten worden geretourneerd.!Het nummer van de kolom die moet worden geretourneerd."}, "TOCOL": {"a": "(matrix, [negeren], [scan_by_column])", "d": "Retourneert de matrix als één kolom.", "ad": "De matrix of verwijzing die moet worden geretourneerd als een kolom.!Hiermee wordt aangegeven of bepaalde typen waarden moeten worden genegeerd. Standaard worden er geen waarden genegeerd.!Scan de matrix op kolom. Standaard wordt de matrix gescand op rij."}, "TOROW": {"a": "(matrix, [negeren], [scan_by_column])", "d": "Retourneert de matrix als één rij.", "ad": "De matrix of verwijzing die moet worden geretourneerd als een rij.!Hier<PERSON> wordt aangegeven of bepaalde typen waarden moeten worden genegeerd. Standaard worden er geen waarden genegeerd.!Scan de matrix op kolom. Standaard wordt de matrix gescand op rij."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": " Hier<PERSON> wordt een rij- of kolomvector achter een opgegeven aantal waarden verpakt.", "ad": "De vector of verwijzing die moet worden verpakt.!Het maximum aantal waarden per kolom.!De waarde waarmee moet worden opgevuld. De standaardwaarde is #N/A."}, "TAKE": {"a": "(matrix, rijen, [kolommen])", "d": "Hier<PERSON> worden rijen of kolommen geretourneerd vanaf het begin of einde van de matrix.", "ad": "De matrix van waaruit rijen of kolommen moeten worden gemaakt.!Het aantal rijen dat in beslag genomen worden. Een negatieve waarde is afkomstig van het einde van de matrix.!Het aantal kolommen dat moet worden opgehaald. Een negatieve waarde is afkomstig van het einde van de matrix."}, "DROP": {"a": "(matrix, rijen, [kolommen])", "d": "<PERSON><PERSON><PERSON><PERSON> of kolommen worden verwijderd uit het begin of einde van de matrix.", "ad": "De matrix waaruit rijen of kolommen moeten worden verwijderd.!Het aantal rijen dat moet worden verwijderd. Een negatieve waarde daalt van het einde van de matrix.!Het aantal kolommen dat moet dalen. Een negatieve waarde daalt vanaf het einde van de matrix."}, "SEQUENCE": {"a": "(rijen, [kolommen], [beginnen], [stap])", "d": "Retour<PERSON>t een reeks getallen", "ad": "het aantal rijen dat moet worden geretourneerd!het aantal kolommen dat moet worden geretourneerd!het eerste getal in de reeks!de hoeveelheid waarmee elke volgende waarde in de reeks moet worden verhoogd"}, "EXPAND": {"a": "(matrix, rijen, [kolommen], [opvullen_met])", "d": "Hi<PERSON><PERSON> wordt een matrix uitgevouwen naar de opgegeven dimensies.", "ad": "De matrix die moet worden uitgevouwen.!Het aantal rijen in de uitgevouwen matrix. Als deze ontbreekt, worden rijen niet uitgevouwen.!Het aantal kolommen in de uitgevouwen matrix. Als deze ontbreekt, worden kolommen niet uitgevouwen.!De waarde waarmee moet worden opgevuld. De standaardwaarde is #N/A."}, "XMATCH": {"a": "(zoek<PERSON><PERSON>e, zoeken-matrix, [overeenkomstmodus], [zoekmodus])", "d": "Retourneert de relatieve positie van een item in een matrix. Standaard is een exacte overeenkomst vereist", "ad": "is de waarde waarnaar moet worden gezocht!is de matrix of het bereik waarin moet worden gezocht!geeft op hoe de zoekwaarde moet overeenkomen met de waarden in de zoeken-matrix !geeft de zoekmodus op die moet worden gebruikt. Standaard wordt een zoekopdracht van eerste naar laatste gebruikt"}, "FILTER": {"a": "(matrix, opnemen, [als_leeg])", "d": "Een matrix of bereik filteren", "ad": "het bereik of de matrix filteren!een bereik of booleaanse waarden waarbij WAAR een rij of kolom vertegenwoordigt die moet worden behouden!wordt geretourneerd als er geen items zijn behouden"}, "ARRAYTOTEXT": {"a": "(matrix, [Format])", "d": "Retourneert een tekstweergave van een matrix", "ad": " de matrix die als tekst moet worden weergegeven! de opmaak van de tekst"}, "SORT": {"a": "(matrix, [sorteerindex], [sorteervolgorde], [op_kol])", "d": "<PERSON><PERSON><PERSON> een bereik of matrix", "ad": "het bereik of de matrix om te sorteren!een getal dat de te sorteren rij of kolom vertegenwoordigt!een getal dat de gewenste sorteervolgorde vertegenwoordigt; 1 voor oplopende volgorde (standaard), -1 voor aflopende volgorde!een logische waarde die de gewenste sorteerrichting vertegenwoordigt: ONWAAR om te sorteren op rij (standaard), WAAR om te sorteren op kolom"}, "SORTBY": {"a": "(matrix, op_matrix, [sorteervolgorde], ...)", "d": "So<PERSON><PERSON> een bereik of matrix op basis van de waarden in een bijbehorend bereik of een bijbehorende matrix", "ad": "het bereik of de matrix die moet worden gesorteerd!het bereik of de matrix op basis waarvan moet worden gesorteerd!een getal dat de gewenste sorteervolgorde aangeeft; 1 voor oplopend (standaard), -1 voor aflopend"}, "GETPIVOTDATA": {"a": "(g<PERSON><PERSON>sveld; dra<PERSON><PERSON>l; [veld]; [item]; ...)", "d": "<PERSON><PERSON>t gegevens op uit een dra<PERSON>l", "ad": "is de naam van het gegevensveld waaruit gegevens moeten worden opgehaald!is een verwijzing naar een cel of celbereik in de draaitabel met de gegevens die u wilt ophalen!veld waarnaar wordt verwezen!velditem waarnaar wordt verwezen"}, "IMPORTRANGE": {"a": "(spreadsheet_url; bereik_tekenreeks)", "d": "Importeert een bereik van cellen uit een bepaalde spreadsheet.", "ad": "de URL van de spreadsheet waaruit gegevens worden geïmporteerd!te importeren bereik"}}