{"DATE": {"a": "(vuosi; kuukausi; pä<PERSON><PERSON>)", "d": "Palauttaa annetun päivämäärän järjestysnumeron päivämäärä-aika-koodissa", "ad": "on luku 1900–9999 tai 1904–9999 laskentataulukon päivämääräjärjestelmästä riippuen!on kuukauden järjestysnumero välillä 1–12!on päivän järjestysnumero välillä 1–31"}, "DATEDIF": {"a": "(aloituspäivä; lopetuspäivä; yksikkö)", "d": "Laskee kahden päivämäärän välillä olevien päivien, kuukausien tai vuosien määrän", "ad": "Päivämäärä, joka vastaa tietyn kauden ensimmäistä tai alkamispäivää!Päivämäärä, joka kuvaa ajan<PERSON> viimeistä päivämäärää eli lopetuspäivää!Palautettavien tietojen tyyppi"}, "DATEVALUE": {"a": "(päivämäärä_teksti)", "d": "Muuntaa päivämäärän tekstistä järjestysnumeroksi, joka vastaa päivämäärää päivämäärä-aika-koodauksessa", "ad": "on teksti, joka vastaa Spreadsheet Editorin päivämäärämuotoa. Arvot voivat olla 1.1.1900–31.12.9999 tai 1.1.1904–31.12.9999 laskentataulukon päivämääräjärjestelmästä riippuen"}, "DAY": {"a": "(järjestysnro)", "d": "<PERSON><PERSON><PERSON><PERSON> kuukauden pä<PERSON>än, luvun väliltä 1–31.", "ad": "on luku Spreadsheet Editorin käyttämässä päivämäärä- ja kellonaikamuodossa"}, "DAYS": {"a": "(lopetuspäivä; aloituspäivä)", "d": "Palauttaa päivän luvun kahden päivämäärän väliltä.", "ad": "aloituspäivä ja lopetuspäivä ovat kaksi päivämäärää, joiden välillä olevien päivien määrän haluat tietää!aloituspäivä ja lopetuspäivä ovat kaksi päivämäärää, joiden välillä olevien päivien määrän haluat tietää"}, "DAYS360": {"a": "(aloituspäiv<PERSON>; lopetuspäivä; [menetelmä])", "d": "Palauttaa kahden päivämäärän välisen päivien lukumäärän käyttämällä 360-päiväistä vuotta (12 kuukautta, joissa on 30 päivää)", "ad": "aloituspäivä ja lopetuspäivä ovat päivämäärät, joiden välisten päivien määrän haluat laskea!aloituspäivä ja lopetuspäivä ovat päivämäärät, joiden välisten päivien määrän haluat laskea!määrittää käytettävän laskentamenetelmän: EPÄTOSI tai jätetty pois = amerikkalainen (NASD) menetelmä; TOSI = eurooppalainen menetelmä"}, "EDATE": {"a": "(aloituspäivä; ku<PERSON><PERSON>t)", "d": "Palauttaa sen päivän järjestysnumeron, joka on määrätyn kuukausimäärän verran ennen tai jälkeen alkupäivää.", "ad": "on aloituspäivän järjestysnumero!on kuukausien lukumäärä ennen tai jälkeen aloituspäivän"}, "EOMONTH": {"a": "(aloituspäivä; ku<PERSON><PERSON>t)", "d": "Palauttaa kuukauden viimeisen päivän järjestysnumeron, joka on määrätyn kuukausimäärän päässä ennen tai jälkeen aloituspäivämäärästä laskien.", "ad": "on aloituspäivän järjestysnumero!on kuukausien lukumäärä ennen tai jälkeen aloituspäivän"}, "HOUR": {"a": "(järjestysnro)", "d": "Palauttaa tunnin kokonaislukumuodossa. Arvo on välillä 0 (0:00)–23 (23:00).", "ad": "on luku Spreadsheet Editorin käyttämässä päivämäärä- ja kellonaikamuodossa tai teksti aikamuodossa, esimerkiksi 16:48:00"}, "ISOWEEKNUM": {"a": "(päivämäärä)", "d": "Palauttaa annetun päivämäärän mukaisen vuoden ISO-standardin mukaisen viikon numeron", "ad": "on ajan ja päivämää<PERSON><PERSON><PERSON> kood<PERSON>, jota Spreadsheet Editor käyttää päivämäärien ja aikojen las<PERSON>"}, "MINUTE": {"a": "(järjestysnro)", "d": "Palauttaa minuutin lukuna väliltä 0–59.", "ad": "on luku Spreadsheet Editorin käyttämässä päivämäärä- ja kellonaikamuodossa tai teksti aikamuodossa, esimerkiksi 16:48:00"}, "MONTH": {"a": "(järjestysnro)", "d": "Palauttaa kuukauden järjestysnumeron 1 (tammikuu)–12 (jou<PERSON><PERSON><PERSON>).", "ad": "on luku Spreadsheet Editorin käyttämässä päivämäärä- ja kellonaikamuodossa"}, "NETWORKDAYS": {"a": "(aloituspäiv<PERSON>; lopetuspäivä; [loma])", "d": "Palauttaa työpäivien lukumäärän kahden päivämäärän väliltä.", "ad": "on aloituspäivän järjestysnumero!on lopetuspäivän järjestysnumero!on vaihtoehtoinen joukko päivän järjestysnumeroita, jotka kuvaavat yhtä tai useampaa päivää, jotka eivät ole työpäiviä. Tällaisiä päiviä ovat esim. yleiset vapaapäivät, eivät kuitenkaan normaalit viikonloput"}, "NETWORKDAYS.INTL": {"a": "(aloituspäiv<PERSON>; lopetuspäivä; [viikon<PERSON><PERSON>]; [loma])", "d": "Palauttaa työpäivien lukumäärän kahden päivämäärän väliltä mukautettava viikonloppuparametri huomioiden", "ad": "on aloituspäivän järjestysnumero!on lopetuspäivän järjestysnumero!on numero tai merkkijono, joka il<PERSON>, milloin viikonloput ovat!on vaihtoehtoinen joukko päivän järjestysnumeroita, jotka kuvaavat yhtä tai useaa päivää, joka ei ole työpäivä. Tällaisia päiviä ovat esimerkiksi yleiset vapaapäivät"}, "NOW": {"a": "()", "d": "Palau<PERSON><PERSON> n<PERSON>sen päivän ja ajan päivämäärän ja ajan muodos<PERSON>.", "ad": ""}, "SECOND": {"a": "(järjestysnro)", "d": "Palauttaa sekunnin lukuna väliltä 0–59.", "ad": "on luku Spreadsheet Editorin käyttämässä päivämäärä- ja kellonaikamuodossa tai teksti aikamuodossa, esimerkiksi 16:48:23"}, "TIME": {"a": "(tunnit; minuutit; sekunnit)", "d": "<PERSON><PERSON><PERSON> lukuina annetut tunnit, minuutit ja sekunnit aikamuotoilluksi järjestysnumeroksi", "ad": "on luku 0 - 23, joka vastaa tunteja!on luku 0 - 59, joka vastaa minuutteja!on luku 0 - 59, joka vastaa sekunteja"}, "TIMEVALUE": {"a": "(a<PERSON>_teksti)", "d": "Muuntaa tekstimuotoisen ajan aikaa ilmaisevaksi järjestysnumeroksi. Numero 0 (0:00:00) muunnetaan muotoon 0,999988426 (23:59:59). Muotoile numero aikamuotoon kaavan kirjoittamisen jälkeen", "ad": "on teksti, joka määrittää ajan jossakin Spreadsheet Editorin tunnistamista ajan esitysmuodoista (tekstissä olevia päivämäärätietoja ei huomioida)"}, "TODAY": {"a": "()", "d": "Palauttaa nykyisen päivämäärän päivämäärämuodossa.", "ad": ""}, "WEEKDAY": {"a": "(järjestysn<PERSON>; [palauta_tyyppi])", "d": "Palauttaa viikonpäivän määrittävän numeron välillä 1 - 7.", "ad": "on päivämäärää kuvaava luku!argumentti määrittää, miten viikonpäivä muutetaan luvuksi. <PERSON><PERSON> arvo on 1, sunnuntai = 1 ... la<PERSON><PERSON> = 7. <PERSON><PERSON> arvo on 2, maanantai = 1 ... sunnuntai = 7. <PERSON><PERSON> arvo on 3, maananta<PERSON> = 0 ... sunnuntai = 6"}, "WEEKNUM": {"a": "(järjestysn<PERSON>; [palauta_tyyppi])", "d": "Palauttaa viikon numeron.", "ad": "on päivämäärä- ja kello<PERSON>, jota Spreadsheet Editor käyttää päivämäärän ja kellonajan laskemisessa!on luku (1 tai 2), joka määrittää palautettavan arvon tyypin"}, "WORKDAY": {"a": "(aloitusp<PERSON><PERSON><PERSON>; päiv<PERSON>; [loma])", "d": "Palauttaa sen päivän järjestysnumeron, joka määritettyjen työpäivien päässä aloituspäivästä.", "ad": "on aloituspäivän järjestysnumero!on työpäivien lukumäärä ennen tai jälkeen aloituspäivän!on vaihtoehtoinen matriisi päivien järjestysnumeroita, jotka poistetaan työpäivistä. Tällaisia päiviä ovat esim. yleiset vapaapäivät, eivät kuitenkaan tavalliset viikonloput"}, "WORKDAY.INTL": {"a": "(al<PERSON>usp<PERSON>iv<PERSON>; päiv<PERSON>; [viikon<PERSON><PERSON>]; [loma])", "d": "Palauttaa sen päivän järjestysnumeron, joka on määritettyjen työpäivien päässä aloituspäivästä, mukautettava viikonloppuparametri huomioiden", "ad": "on aloituspäivän järjestysnumero!on muiden kuin viikonlopun ja lomapäivien määrä ennen aloituspäivää tai sen jälkeen!on luku tai merk<PERSON>jono, joka il<PERSON>, milloin viikonloput ovat!on vaihtoehtoinen matriisi sellaisten päivien järjestysnumeroita, jotka poistetaan työpäivistä. Tällaisia päiviä ovat esimerkiksi yleiset vapaapäivät"}, "YEAR": {"a": "(järjestysnro)", "d": "Palauttaa vuoden kokonaislukuna välillä 1900–9999.", "ad": "on luku Spreadsheet Editorin käyttämässä päivämäärä- ja kellonaikamuodossa"}, "YEARFRAC": {"a": "(aloituspäiv<PERSON>; lopetuspäivä; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON>, joka ilmoittaa kuinka suuri osa vuodesta kuuluu aloituspäivän ja lopetuspäivän väliseen a<PERSON>an.", "ad": "on aloituspäivän järjestysnumero!on lopetuspäivän järjestysnumero!on käytettävä päivien laskentaperuste"}, "BESSELI": {"a": "(x; n)", "d": "Palauttaa muutetun Besselin funktion ln(x).", "ad": "on arvo, jossa funktion arvo lasketaan!on Besselin funktion kertaluokka"}, "BESSELJ": {"a": "(x; n)", "d": "Palauttaa Besselin funktion Jn(x).", "ad": "on arvo, jossa funktion arvo lasketaan!on Besselin funktion kertaluokka"}, "BESSELK": {"a": "(x; n)", "d": "Palauttaa muutetun Besselin funktion Kn(x).", "ad": "on arvo, jossa funktion arvo lasketaan!on funktion kertaluokka"}, "BESSELY": {"a": "(x; n)", "d": "Palauttaa Besselin funktion Yn(x).", "ad": "on arvo, jossa funktion arvo lasketaan!on funktion kertaluokka"}, "BIN2DEC": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON> des<PERSON>.", "ad": "on binaar<PERSON><PERSON>, jonka haluat muuntaa"}, "BIN2HEX": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON><PERSON> he<PERSON>.", "ad": "on binaar<PERSON><PERSON>, jonka haluat muuntaa!on käytettävien merkkien lukumäärä"}, "BIN2OCT": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON>.", "ad": "on binaar<PERSON><PERSON>, jonka haluat muuntaa!on käytettävien merkkien lukumäärä"}, "BITAND": {"a": "(luku1; luku2)", "d": "<PERSON><PERSON><PERSON><PERSON> kahden luvun bitti<PERSON>on 'Ja'", "ad": "on desimaalimuoto bin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jonka haluat laskea!on desimaalimuoto bin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jonka haluat laskea"}, "BITLSHIFT": {"a": "(luku; siirrettävä_määrä)", "d": "<PERSON><PERSON><PERSON><PERSON> siirrettävän_määrän bittimäärän verran vasemmalle siirretyn luvun", "ad": "on desima<PERSON><PERSON><PERSON> bin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jonka haluat laskea!on bittil<PERSON>, jonka verran haluat siirtää Lukua vasemmalle"}, "BITOR": {"a": "(luku1; luku2)", "d": "<PERSON><PERSON><PERSON><PERSON> kahden luvun bittitason 'Tai'", "ad": "on desimaalimuoto bin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jonka haluat laskea!on desimaalimuoto bin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jonka haluat laskea"}, "BITRSHIFT": {"a": "(luku; siirrettävä_määrä)", "d": "Palauttaa siirrettävän_määrän bittimäärän verran oikealle siirretyn luvun", "ad": "on desima<PERSON><PERSON><PERSON> bin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jonka haluat laskea!on bittil<PERSON>, jonka verran haluat siirtää Lu<PERSON> o<PERSON>alle"}, "BITXOR": {"a": "(luku1; luku2)", "d": "<PERSON><PERSON><PERSON><PERSON> kahden luvun bittitason 'Poissulkeva Tai'", "ad": "on desimaalimuoto bin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jonka haluat laskea!on desimaalimuoto bin<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jonka haluat laskea"}, "COMPLEX": {"a": "(re<PERSON><PERSON><PERSON>; imag_osa; [suffiksi])", "d": "<PERSON><PERSON><PERSON>- ja imaginaari<PERSON><PERSON><PERSON>t kompleksiluvuksi.", "ad": "on kompleksiluvun reaalikerroin!on kompleksiluvun imaginaarikerroin!on kompleksiluvun imaginaariosan loppuliite"}, "CONVERT": {"a": "(luku; yks<PERSON><PERSON><PERSON><PERSON>; yks<PERSON><PERSON><PERSON><PERSON>)", "d": "Muuttaa luvun toiseen järjestelmään.", "ad": "on muunnettava arvo!on muutettavan luvun yksikkö!on muunnoksen yksikkö"}, "DEC2BIN": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON> k<PERSON>rjestelmän luvun binaariluvuksi.", "ad": "on kymmenjärjestelmän luku, jonka haluat muuntaa!on käytettävien merkkien lukumäärä"}, "DEC2HEX": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON> k<PERSON>rjestelmän luvun heksades<PERSON>luvuks<PERSON>.", "ad": "on kymmenjärjestelmän luku, jonka haluat muuntaa!on käytettävien merkkien lukumäärä"}, "DEC2OCT": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON> k<PERSON>j<PERSON>rjestelmän luvun oktaaliluvuksi.", "ad": "on kymmenjärjestelmän luku, jonka haluat muuntaa!on käytettävien merkkien lukumäärä"}, "DELTA": {"a": "(luku1; [luku2])", "d": "Testaa ovatko kaksi lukua y<PERSON>t.", "ad": "on ensimmäinen luku!on toinen luku"}, "ERF": {"a": "(alaraja; [yl<PERSON>raja])", "d": "Palauttaa virhefunktion.", "ad": "on alaraja virhefunktion integraatiolle!on yläräja virhefunktion integraatiolle"}, "ERF.PRECISE": {"a": "(X)", "d": "Palauttaa virhefunktion", "ad": "on alaraja se<PERSON><PERSON> kohteen integraatiolle: ERF.PRECISE"}, "ERFC": {"a": "(x)", "d": "Palauttaa virhefunktion komplementin.", "ad": "on alaraja virhefunktion integraatiolle"}, "ERFC.PRECISE": {"a": "(X)", "d": "Palauttaa virhefunktion komplementin", "ad": "on alaraja se<PERSON><PERSON> kohteen integraatiolle: VIRHEFUNKTIO.KOMPLEMENTTI.TARKKA"}, "GESTEP": {"a": "(luku; [raja_arvo])", "d": "Testaa onko luku suurempi kuin raja-arvo.", "ad": "on arvo, jota testataan raja-arvoa vastaan!on raja-arvo"}, "HEX2BIN": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON> bin<PERSON>.", "ad": "on he<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jonka haluat muuntaa!on käytettävien merkkien lukumäärä"}, "HEX2DEC": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON> he<PERSON> desima<PERSON>luvu<PERSON>i.", "ad": "on he<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jonka haluat muuntaa"}, "HEX2OCT": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON> ok<PERSON>lu<PERSON>.", "ad": "on he<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jonka haluat muuntaa!on käytettävien merkkien lukumäärä"}, "IMABS": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun itseisarvon.", "ad": "on kompleksiluku, jolle haluat laskea itseisarvon"}, "IMAGINARY": {"a": "(kompleksiluku)", "d": "<PERSON><PERSON><PERSON>a kompleksiluvun imaginaariosan kertoimen.", "ad": "on kompleks<PERSON><PERSON>, jolle haluat laskea imaginaarikertoimen"}, "IMARGUMENT": {"a": "(kompleksiluku)", "d": "<PERSON><PERSON><PERSON><PERSON> argumentin q, joka on kulma radiaaneina.", "ad": "on kompleks<PERSON><PERSON>, jolle haluat laskea argumentin theeta"}, "IMCONJUGATE": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun konjugaattiluvun.", "ad": "on kompleksil<PERSON>, jolle haluat laskea konjugaattiluvun"}, "IMCOS": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun kosinin.", "ad": "on kompleks<PERSON><PERSON>, jolle haluat laskea kosinin"}, "IMCOSH": {"a": "(luku)", "d": "Palauttaa kompleksiluvun hyperbolisen kosinin", "ad": "on kompleksil<PERSON>, jolle haluat laskea hyperbolisen kosinin"}, "IMCOT": {"a": "(luku)", "d": "Palauttaa kompleksiluvun kotangentin", "ad": "on kompleks<PERSON><PERSON>, jolle haluat laskea kotangentin"}, "IMCSC": {"a": "(luku)", "d": "Pa<PERSON><PERSON>a kompleksiluvun kosekantin", "ad": "on kompleks<PERSON><PERSON>, jolle haluat laskea kosekantin"}, "IMCSCH": {"a": "(luku)", "d": "Palauttaa kompleksiluvun hyperbolisen kosekantin", "ad": "on kompleksil<PERSON>, jolle haluat laskea hyperbolisen kosekantin"}, "IMDIV": {"a": "(kompleksiluku1; kompleksiluku2)", "d": "<PERSON><PERSON><PERSON><PERSON> kahden kompleksiluvun osamäärän.", "ad": "on jae<PERSON>va kompleksiluku!on j<PERSON><PERSON> oleva kompleksiluku"}, "IMEXP": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun eksponentin.", "ad": "on kompleksiluku, jolle haluat laskea eksponentin"}, "IMLN": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun luonnollisen logaritmin.", "ad": "on kompleks<PERSON><PERSON>, jolle haluat laskea luonnollisen logaritmin"}, "IMLOG10": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun kymmenkantaisen logaritmin.", "ad": "on kompleksil<PERSON>, jolle haluat laskea kymmenkantaisen logaritmin"}, "IMLOG2": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun kaksikantaisen logaritmin.", "ad": "on kompleks<PERSON><PERSON>, jolle haluat laskea kaksikantaisen logaritmin"}, "IMPOWER": {"a": "(kompleksiluku; luku)", "d": "Palauttaa kompleksiluvun korotettuna kokonaislukupotenssiin.", "ad": "on kompleksil<PERSON>, jonka haluat korottaa potenssiin!on potens<PERSON>, johon haluat korottaa kompleksiluvun"}, "IMPRODUCT": {"a": "(iluku1; [iluku2]; ...)", "d": "Palauttaa 1 - 255 kompleksiluvun tulon", "ad": "<PERSON><PERSON>1, <PERSON><PERSON>2,... ovat 1 - 255 kom<PERSON><PERSON><PERSON><PERSON><PERSON>, jotka haluat kertoa."}, "IMREAL": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun reaaliosan kertoimen.", "ad": "on kompleksil<PERSON>, josta haluat laskea reaalikertoimen"}, "IMSEC": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> kompleksilu<PERSON>n se<PERSON>tin", "ad": "on kompleks<PERSON><PERSON>, jolle haluat laskea sekantin"}, "IMSECH": {"a": "(luku)", "d": "Palauttaa kompleksiluvun hyperbolisen sekantin", "ad": "on kompleksil<PERSON>, jolle haluat laskea hyperbolisen sekantin"}, "IMSIN": {"a": "(kompleksiluku)", "d": "<PERSON><PERSON><PERSON><PERSON> kompleks<PERSON>vun sinin.", "ad": "on kompleks<PERSON><PERSON>, jolle haluat laskea sinin"}, "IMSINH": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON>a kompleksiluvun hyperbolisen sinin", "ad": "on kompleks<PERSON><PERSON>, jolle haluat laskea hyperbolisen sinin"}, "IMSQRT": {"a": "(kompleksiluku)", "d": "Palauttaa kompleksiluvun neliöjuuren.", "ad": "on kompleksil<PERSON>, jolle haluat laskea ne<PERSON>ö<PERSON>"}, "IMSUB": {"a": "(kompleksiluku1; kompleksiluku2)", "d": "<PERSON><PERSON><PERSON><PERSON> kahden kompleksiluvun erotuksen.", "ad": "on kompleksiluku, josta haluat vähentää kompleksiluku2:n!on kompleksiluku, jonka haluat vähentää kompleksi1:stä"}, "IMSUM": {"a": "(iluku1; [iluku2]; ...)", "d": "Pa<PERSON><PERSON>a kompleksil<PERSON><PERSON>n summan", "ad": "ovat 1 - 255 komple<PERSON><PERSON><PERSON><PERSON>, jotka haluat lisätä"}, "IMTAN": {"a": "(iluku)", "d": "Pa<PERSON><PERSON>a kompleksiluvun tangentin", "ad": "on kompleks<PERSON><PERSON>, jolle haluat laskea tangentin"}, "OCT2BIN": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON>.", "ad": "on ok<PERSON>aliluku, jonka haluat muuntaa!on käytettävien merkkien lukumäärä"}, "OCT2DEC": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON> desima<PERSON>.", "ad": "on ok<PERSON><PERSON>luku, jonka haluat muuntaa"}, "OCT2HEX": {"a": "(luku; [merkit])", "d": "<PERSON><PERSON><PERSON> he<PERSON>.", "ad": "on ok<PERSON>aliluku, jonka haluat muuntaa!on käytettävien merkkien lukumäärä"}, "DAVERAGE": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Palauttaa valittujen tietok<PERSON>akenttien arvojen kes<PERSON>von", "ad": "on solualue, joka muodostaa luettelon tai tietokannan. Tietokanta on joukko toisiinsa liittyviä tietoja!on joko lainausmerkeissä oleva sarakkeen otsikko tai luku, joka määrittää sarakkeen sijainnin luettelossa!on solualue, joka sisältää määrittämäsi ehdot. Alue sisältää sarakeotsikon ja otsikon alapuolella olevan solun, joka sisältää ehdot"}, "DCOUNT": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "<PERSON><PERSON><PERSON>, mon<PERSON><PERSON>o annetun tietokannan solussa on ehdot täyttävä luku", "ad": "on solualue, joka muodostaa luettelon tai tietokannan. Tietokanta on joukko toisiinsa liittyviä tietoja!on joko lainausmerkeissä oleva sarakkeen otsikko tai luku, joka määrittää sarakkeen sijainnin luettelossa!on solualue, joka sisältää määrittämäsi ehdot. Alue sisältää sarakeotsikon ja otsikon alapuolella olevan solun, joka sisältää ehdot"}, "DCOUNTA": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "<PERSON><PERSON><PERSON>, moniko tietokannan tietoja sisältävä solu vastaa määrittämiäsi ehtoja", "ad": "on solualue, joka muodostaa luettelon tai tietokannan. Tietokanta on joukko toisiinsa liittyviä tietoja!on joko lainausmerkeissä oleva sarakkeen otsikko tai luku, joka määrittää sarakkeen sijainnin luettelossa!on solualue, joka sisältää määrittämäsi ehdot. Alue sisältää sarakeotsikon ja otsikon alapuolella olevan solun, joka sisältää ehdot"}, "DGET": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Poimii yksittäisiä ehdot täyttäviä tietueita tietokannasta", "ad": "on solualue, joka muodostaa luettelon tai tietokannan. Tietokanta on joukko toisiinsa liittyviä tietoja!on joko lainausmerkeissä oleva sarakkeen otsikko tai luku, joka määrittää sarakkeen sijainnin luettelossa.!on solualue, joka sisältää määrittämäsi ehdot. Alue sisältää sarakeotsikon ja otsikon alapuolella olevan solun, joka sisältää ehdot"}, "DMAX": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "<PERSON><PERSON><PERSON><PERSON> valittujen tie<PERSON>n kent<PERSON>, määritetty<PERSON><PERSON> <PERSON><PERSON> arvon", "ad": "on solualue, joka muodostaa luettelon tai tietokannan. Tietokanta on joukko toisiinsa liittyviä tietoja!on joko lainausmerkeissä oleva sarakkeen otsikko tai luku, joka määrittää sarakkeen sijainnin luettelossa!on solualue, joka sisältää määrittämäsi ehdot. Alue sisältää sarakeotsikon ja otsikon alapuolella olevan solun, joka sisältää ehdot"}, "DMIN": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Palauttaa valittujen tietokannan kent<PERSON>, määritetyt ehdot täyttävän arvon", "ad": "on solualue, joka muodostaa luettelon tai tietokannan. Tietokanta on joukko toisiinsa liittyviä tietoja!on joko lainausmerkeissä oleva sarakkeen otsikko tai luku, joka määrittää sarakkeen sijainnin luettelossa!on solualue, joka sisältää määrittämäsi ehdot. Alue sisältää sarakeotsikon ja otsikon alapuolella olevan solun, joka sisältää ehdot"}, "DPRODUCT": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Laskee määritetyt ehdot täyttävien tietokantakenttien tulon", "ad": "on solualue, joka muodostaa luettelon tai tietokannan. Tietokanta on joukko toisiinsa liittyviä tietoja!on joko lainausmerkeissä oleva sarakkeen otsikko tai luku, joka määrittää sarakkeen sijainnin luettelossa!on solualue, joka sisältää määrittämäsi ehdot. Alue sisältää sarakeotsikon ja otsikon alapuolella olevan solun, joka sisältää ehdot"}, "DSTDEV": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Laskee populaation keskipoikkeaman otoksen perusteella käyttäen ehdon täyttävissä tietokantakentissä olevia arvoja", "ad": "on solualue, joka muodostaa luettelon tai tietokannan. Tietokanta on joukko toisiinsa liittyviä tietoja!on joko lainausmerkeissä oleva sarakkeen otsikko tai luku, joka määrittää sarakkeen sijainnin luettelossa!on solualue, joka sisältää määrittämäsi ehdot. Alue sisältää sarakeotsikon ja otsikon alapuolella olevan solun, joka sisältää ehdot"}, "DSTDEVP": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Laskee keskihajonnan koko populaatiosta käyttäen ehdon täyttävissä tietokantakentissä olevia arvoja", "ad": "on solualue, joka muodostaa luettelon tai tietokannan. Tietokanta on joukko toisiinsa liittyviä tietoja!on joko lainausmerkeissä oleva sarakkeen otsikko tai luku, joka määrittää sarakkeen sijainnin luettelossa!on solualue, joka sisältää määrittämäsi ehdot. Alue sisältää sarakeotsikon ja otsikon alapuolella olevan solun, joka sisältää ehdot"}, "DSUM": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Laskee ehdon tä<PERSON>tävissä tietokantakentissä olevien arvojen summan", "ad": "on solualue, joka muodostaa luettelon tai tietokannan. Tietokanta on joukko toisiinsa liittyviä tietoja!on joko lainausmerkeissä oleva sarakkeen otsikko tai luku, joka määrittää sarakkeen sijainnin luettelossa!on solualue, joka sisältää määrittämäsi ehdot. Alue sisältää sarakeotsikon ja otsikon alapuolella olevan solun, joka sisältää ehdot"}, "DVAR": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Laskee populaation varianssin otoksen perusteella käyttäen ehdot täyttävissä tietokantakentissä olevia arvoja", "ad": "on solualue, joka muodostaa luettelon tai tietokannan. Tietokanta on joukko toisiinsa liittyviä tietoja!on joko lainausmerkeissä oleva sarakkeen otsikko tai luku, joka määrittää sarakkeen sijainnin luettelossa!on solualue, joka sisältää määrittämäsi ehdot. Alue sisältää sarakeotsikon ja otsikon alapuolella olevan solun, joka sisältää ehdot"}, "DVARP": {"a": "(tie<PERSON>ant<PERSON>; kentt<PERSON>; ehdot)", "d": "Laskee populaation varianssin koko populaation perusteella käyttäen ehdot täyttävissä tietokantakentissä olevia arvoja", "ad": "on solualue, joka muodostaa luettelon tai tietokannan. Tietokanta on joukko toisiinsa liittyviä tietoja!on joko lainausmerkeissä oleva sarakkeen otsikko tai luku, joka määrittää sarakkeen sijainnin luettelossa!on solualue, joka sisältää määrittämäsi ehdot. Alue sisältää sarakeotsikon ja otsikon alapuolella olevan solun, joka sisältää ehdot"}, "CHAR": {"a": "(luku)", "d": "Palauttaa tietokoneen merkistössä annettua lukua vastaavan merkin", "ad": "on luku väliltä 1 - 255, joka määrittä<PERSON> halutun merkin"}, "CLEAN": {"a": "(teks<PERSON>)", "d": "Poistaa tekstistä kaikki merkit, jotka eivät tulostu", "ad": "on mik<PERSON> ta<PERSON> tieto, josta haluat poistaa tulostumattomat merkit"}, "CODE": {"a": "(teks<PERSON>)", "d": "Palauttaa tekstijonon ensimmäisen merkin numerokoodin tietokoneen käyttämässä merkistössä", "ad": "on teksti, jonka ensimmäisen merkin koodin haluat saada selville"}, "CONCATENATE": {"a": "(teksti1; [teksti2]; ...)", "d": "Yhdistää erilliset merkkijonot yhdeksi merkkijonoksi", "ad": "ovat 1 - 255 yhteenliitettävää merkkijonoa. Argumentit voivat olla merk<PERSON>oja, lukuja tai viittauksia yksitt<PERSON>isiin soluihin"}, "CONCAT": {"a": "(teksti1; ...)", "d": "Yhdistää luettelon tai alueen tekstimerkkijonot", "ad": "ovat 1-254 tekstimerkkijonoa tai <PERSON>, jotka yhdistetään yhdeksi tekstimerkkijonoksi"}, "DOLLAR": {"a": "(luku; [desimaalit])", "d": "<PERSON><PERSON><PERSON> luvun valuutt<PERSON>uo<PERSON>iseksi tekstiksi", "ad": "on luku, viittaus luvun sisältävään soluun tai kaava, joka palauttaa tulokseksi luvun!on desimaalipilkun oikealla puolella olevien numeroiden määrä. Lu<PERSON> pyöristetään tarvittaessa. <PERSON><PERSON> lukua ei anneta, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on luku 2"}, "EXACT": {"a": "(teksti1; teksti2)", "d": "<PERSON><PERSON><PERSON><PERSON>, o<PERSON><PERSON> ka<PERSON>i <PERSON>, ja palauttaa arvon TOSI tai EPÄTOSI. VERTAA-funktio ottaa huo<PERSON> kir<PERSON>", "ad": "on ensimmäinen tekstijono!on toinen tekstijono"}, "FIND": {"a": "(etsittävä_teksti; tekstissä; [aloit<PERSON><PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>, josta toisen merkkijonon sisällä oleva merkkijono alkaa. FIND-arvo ottaa huomioon kirjainkoon", "ad": "on etsittävä teksti. Käytä lainausmerkkejä (tyhjä teksti) ensimmäisen merkin tunnistamiseen. Yleismerkkejä (?, *) ei voi käyttää!on teksti, josta haluat etsiä merkkijonon!määrittää sen merkin järjestysnumeron, josta haluat aloittaa etsinnän. <PERSON><PERSON><PERSON> ensimmäisen merkin numero on 1. Jo<PERSON> tämä argumentti jätetään pois, etsintä aloitetaan ensimmäisestä merkistä"}, "FINDB": {"a": "(etsittävä_teksti; tekstissä; [aloit<PERSON><PERSON><PERSON>])", "d": "Etsivät merkkijonon toisen merkkijonon sisältä ja palauttavat luvun, joka ilmaisee etsittävän merkkijonon ensimmäisen merkin sijainnin toisen merkkijonon sisällä, k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kun kirjoituskielen merkistön merkeissä on kaksi tavu<PERSON> (DBCS) - japani, kiina ja korea.", "ad": "on etsittävä teksti. Käytä lainausmerkkejä (tyhjä teksti) ensimmäisen merkin tunnistamiseen. Yleismerkkejä (?, *) ei voi käyttää!on teksti, josta haluat etsiä merkkijonon!määrittää sen merkin järjestysnumeron, josta haluat aloittaa etsinnän. <PERSON><PERSON><PERSON> ensimmäisen merkin numero on 1. Jo<PERSON> tämä argumentti jätetään pois, etsintä aloitetaan ensimmäisestä merkistä"}, "FIXED": {"a": "(luku; [desimaalit]; [ei_erotinta])", "d": "Muotoilee luvun tekstiksi, jossa on kiinteä määrä desimaaleja ja palauttaa tuloksen tekstinä", "ad": "on luku, jonka haluat pyöristää ja muuntaa tekstiksi!on desimaalipilkun oikealla puolella olevien numeroiden määrä. Jos arvoa ei ole määritetty, desimaalit = 2!on totu<PERSON>r<PERSON>, joka mä<PERSON>rittää, näytetäänkö erottimet luvussa. <PERSON><PERSON> arvo on TOSI, erottimia ei näytetä palautettavassa tekstissä. Jos arvo puuttuu tai on EPÄTOSI, erottimet näytetään"}, "LEFT": {"a": "(teksti; [merkit_luku])", "d": "Palauttaa määritetyn määrän merkkejä tekstimerkkijonon alusta lukien", "ad": "on tekstimerkkijono, joka sisältää poimittavat merkit!määrittää, montako merkkiä haluat VASEN-funktion poimivan. Funktio poimii vain yhden merkin, jos tätä arvoa ei määritetä"}, "LEFTB": {"a": "(teksti; [merkit_luku])", "d": "Palauttaa merkkijonon ensimmäisen merkin tai ensimmäiset merkit määritetyn tavumäärän per<PERSON>, k<PERSON>ytetään, kun kirjoituskielen merkistön merkeissä on kaksi tavua (DBCS) - japani, kiina ja korea", "ad": "on tekstimerkkijono, joka sisältää poimittavat merkit!määrittää, montako merkkiä haluat LEFTB-funktion poimivan. Funktio poimii vain yhden merkin, jos tätä arvoa ei määritetä"}, "LEN": {"a": "(teks<PERSON>)", "d": "Palauttaa tekstimerkkijonon merkkien määrän", "ad": "on teksti, jonka pituuden haluat selvittää. Välilyönnit lasketaan merkeiksi"}, "LENB": {"a": "(teks<PERSON>)", "d": "Palauttaa tekstimerkkijonossa olevien tavujen määrän, k<PERSON>ytetään, kun kirjoituskielen merkistön merkeissä on kaksi tavua (DBCS) - japani, kiina ja korea", "ad": "on teksti, jonka pituuden haluat selvittää. Välilyönnit lasketaan merkeiksi"}, "LOWER": {"a": "(teks<PERSON>)", "d": "Muuntaa kaikki tekstissä olevat isot kirjaimet pieniksi", "ad": "on te<PERSON><PERSON>, jonka haluat muuttaa pieniksi kirjaimiksi. Merkkejä, jotka eivät ole kirjaim<PERSON>, ei muunneta"}, "MID": {"a": "(teksti; aloitusnro; merkit_luku)", "d": "Palauttaa tekstin keskeltä määritetyn määrän merkkejä aloittaen määrittämästäsi kohdasta", "ad": "on tekstimerkkijono, joka sisältää poimittavat merkit!on ensimmäisen tekstistä poimittavan merkin sijainti. <PERSON><PERSON><PERSON> ensimmäinen merkki on 1!määrittää, montako merkkiä funktio poimii tekstistä"}, "MIDB": {"a": "(teksti; aloitusnro; merkit_luku)", "d": "Poimii merkkijonosta määrittämääsi tavumäärään perustuvan määrän merkkejä alkaen määrittämästäsi paikasta, käytetään, kun kirjoituskielen merkistön merkeissä on kaksi tavua (DBCS) - japani, kiina ja korea", "ad": "on tekstimerkkijono, joka sisältää poimittavat merkit!on ensimmäisen tekstistä poimittavan merkin sijainti. <PERSON><PERSON><PERSON> ensimmäinen merkki on 1!määrittää, montako merkkiä funktio poimii tekstistä"}, "NUMBERVALUE": {"a": "(teksti; [desima<PERSON><PERSON>tin]; [ry<PERSON><PERSON><PERSON>tin])", "d": "<PERSON><PERSON><PERSON> tekstin luvuksi maa-asetuksen itsenäisellä tavalla", "ad": "on merk<PERSON>jono, joka esittää muunnettavaa lukua!on merkki, jota käytetään merkkijonossa desimaalierottimena!on merkki, jota käytetään merkkijonossa ryhmäerottimena"}, "PROPER": {"a": "(teks<PERSON>)", "d": "Muuntaa jokaisen tekstimuotoisen sanan ensimmäisen kirjaimen isoksi kirjaimeksi ja kaikki muut kirjaimet pieniksi", "ad": "on lainausmerkein rajattu teksti, kaava, joka palauttaa tekstiä, tai viittaus soluun, jossa on tekstiä, jonka alkukirja<PERSON>t haluat muuttaa isoiksi"}, "REPLACE": {"a": "(van<PERSON>_teksti; aloit<PERSON><PERSON><PERSON>; merkit_luku; uusi_teksti)", "d": "Korvaa merkkejä tekstissä", "ad": "on teksti, josta haluat korvata tietyn määrän merkkejä!on tekstin vanha_teksti merkin järjestysnumero, josta merkkien korvaus tekstin uusi_teksti merkeillä alkaa!on niiden vanha_teksti merkkien määrä, jotka haluat korvata tekstillä uusi_teksti!on teksti, joka korvaa tekstin vanha_teksti"}, "REPLACEB": {"a": "(van<PERSON>_teksti; aloit<PERSON><PERSON><PERSON>; merkit_luku; uusi_teksti)", "d": "<PERSON>rvaa tekstimerkkijonon osan toisella tekstimerkkijonolla määritettyjen merkkien tavujen määrän perust<PERSON>, with a new set of characters, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kun kirjoituskielen merkistön merkeissä on kaksi tavua (DBCS) - japani, kiina ja korea", "ad": "on teksti, josta haluat korvata tietyn määrän merkkejä!on tekstin vanha_teksti merkin järjestysnumero, josta merkkien korvaus tekstin uusi_teksti merkeillä alkaa!on niiden vanha_teksti merkkien määrä, jotka haluat korvata tekstillä uusi_teksti!on teksti, joka korvaa tekstin vanha_teksti"}, "REPT": {"a": "(teksti; kerrat_luku)", "d": "Toistaa tekstin antamasi määrän kertoja. Voit käyttää funktiota saman tekstin kirjoittamiseen soluun useita kertoja", "ad": "on teks<PERSON>, jonka haluat to<PERSON>a!on positiivinen luku, joka määrittää tekstin toist<PERSON><PERSON>n määrän"}, "RIGHT": {"a": "(teksti; [merkit_luku])", "d": "Palauttaa määritetyn määrän merkkejä tekstimerkkijonon lopusta lukien", "ad": "on tekstimerkkijono, joka sisältää poimittavat merkit!määrittää poimittavien merkkien määrän. Jos arvoa ei määritetä, oh<PERSON><PERSON> k<PERSON>ää arvoa 1"}, "RIGHTB": {"a": "(teksti; [merkit_luku])", "d": "Palauttaa merkkijonon viimeisen merkin tai viimeiset merkit annetun merkkien tavumäärän perust<PERSON>, k<PERSON>ytetään, kun kirjoituskielen merkistön merkeissä on kaksi tavua (DBCS) - japani, kiina ja korea", "ad": "on tekstimerkkijono, joka sisältää poimittavat merkit!määrittää poimittavien merkkien määrän. Jos arvoa ei määritetä, oh<PERSON><PERSON> k<PERSON>ää arvoa 1"}, "SEARCH": {"a": "(etsittävä_teksti; tekstissä; [aloit<PERSON><PERSON><PERSON>])", "d": "Palauttaa sen merkin numeron, jossa etsittävä merkki tai merkkijono esiintyy ensimmäisen kerran. Merkkiä tai merkkijonoa etsitään vasemmalta o<PERSON>alle, eik<PERSON> kir<PERSON>a oteta huomioon", "ad": "on etsittävä teksti. Voit käyttää yleismerkkejä ? ja *. Jos haluat etsiä merkkejä ? tai *, käytä ~? tai ~* merkkejä!on teksti, josta haluat etsiä etsittävä_teksti argumentin arvoa!on sen argumentin tekstissä määrittämän merkin järjestysnumero (vasemmalta oikealle), josta haluat aloittaa etsinnän. Jos arvoa ei määritetä, ohjelma käyttää arvoa 1"}, "SEARCHB": {"a": "(etsittävä_teksti; tekstissä; [aloit<PERSON><PERSON><PERSON>])", "d": "Paikantavat yhden merkkijonon toisen merkkijonon sisältä ja ne palauttavat luvun, joka vastaa ensimmäisen merkkijonon aloituskohtaa toisen merkkijojon ensimmäisestä kirjaimesta laskettuna, k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kun kirjoituskielen merkistön merkeissä on kaksi tavua (DBCS) - japani, kiina ja korea", "ad": "on etsittävä teksti. Voit käyttää yleismerkkejä ? ja *. Jos haluat etsiä merkkejä ? tai *, käytä ~? tai ~* merkkejä!on teksti, josta haluat etsiä etsittävä_teksti argumentin arvoa!on sen argumentin tekstissä määrittämän merkin järjestysnumero (vasemmalta oikealle), josta haluat aloittaa etsinnän. Jos arvoa ei määritetä, ohjelma käyttää arvoa 1"}, "SUBSTITUTE": {"a": "(teksti; vanha_teksti; uusi_teksti; [esiintym<PERSON>_nro])", "d": "<PERSON><PERSON><PERSON> tekstissä olevan vanhan tekstin uudella tekstillä", "ad": "on teksti tai viittaus soluun, joka sisältää muutettavan tekstin!on teksti, jonka haluat vaihtaa. Jo<PERSON> korvattavan tekstin kirjainkoko ei vastaa korvaavan tekstin kirjainkokoa, funktio ei korvaa tekstiä!on teksti, jonka haluat vaihtaa vanha_teksti-arvon tilalle!määrittää, minkä vanha_teksti-argumentin esiintymän haluat korvata. Jos argumenttia ei määritetä, kaikki esiintymät korvataan"}, "T": {"a": "(arvo)", "d": "Tark<PERSON>a, onko arvo tekstiä. Jos arvo on tekstiä, funktio palauttaa tekstin. Jos arvo ei ole tekstiä, funktio palauttaa tyhjät lainausmerkit", "ad": "on testattava arvo"}, "TEXT": {"a": "(arvo; muoto_teksti)", "d": "Muotoilee luvun ja muuntaa sen tekstiksi", "ad": "on luku, luvun palauttava kaava tai viittaus luvun sisältävään soluun!on tekstimuotoinen luvun esitysmuoto Mu<PERSON> luku -valintaikkunasta"}, "TEXTJOIN": {"a": "(erotin; ohita_tyhjä; teksti1; ...)", "d": "Yhdistää luettelon tai alueen tekstimerkkijonot erottimella", "ad": "Tekstikohteiden väliin lisättävä merkki tai merkkijono!jos TOSI(oletus), ohittaa tyhjät solut!ovat 1-252 tekstimerkkijonoa tai al<PERSON>tta, jotka yhdistetään"}, "TRIM": {"a": "(teks<PERSON>)", "d": "Poistaa välit tekstimerkkijonosta paitsi yksittäiset sanojen välissä olevat välit", "ad": "on teksti, josta haluat poistaa välit"}, "UNICHAR": {"a": "(luku)", "d": "Palauttaa Unicode-<PERSON><PERSON>, johon annettu luku<PERSON> viittaa", "ad": "on Unicode-numero, jota esittää merkki"}, "UNICODE": {"a": "(teks<PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> (koodipiste), joka vastaa tekstin ensimmäistä merk<PERSON>", "ad": "on merk<PERSON>, josta haluat Unicode-arvon"}, "UPPER": {"a": "(teks<PERSON>)", "d": "<PERSON><PERSON><PERSON> te<PERSON><PERSON> isoin kir<PERSON><PERSON><PERSON> kir<PERSON>i", "ad": "on isoin kir<PERSON><PERSON><PERSON> kirjoitetuksi muutettava teksti (joko teksti- tai viittaus merkkijonoon)"}, "VALUE": {"a": "(teks<PERSON>)", "d": "<PERSON><PERSON><PERSON> luku<PERSON>a kuvaavan merkkijonon luvuksi", "ad": "on lainausmerkeissä oleva teksti tai viittaus muunnettavan tekstin sisältävään soluun"}, "AVEDEV": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa hajontojen itseisarvojen keskiarvon. Argumentit voivat olla lukuja, ni<PERSON><PERSON>, matri<PERSON>ja tai viitta<PERSON>sia lukuihin", "ad": "ovat 1 - 255 <PERSON><PERSON>, joista haluat laskea keskipoik<PERSON>amien keski<PERSON>von"}, "AVERAGE": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa argumenttien <PERSON><PERSON><PERSON> k<PERSON>. Argumentit voivat olla lukuja, <PERSON><PERSON><PERSON>, matri<PERSON><PERSON> tai vii<PERSON><PERSON><PERSON>, jotka kohdistuvat lukuihin", "ad": "ovat 1 - 255 nume<PERSON><PERSON>, joista haluat laskea a<PERSON><PERSON><PERSON><PERSON> keski<PERSON>von"}, "AVERAGEA": {"a": "(arvo1; [arvo2]; ...)", "d": "Palauttaa argumenttien (<PERSON><PERSON><PERSON><PERSON><PERSON>) keskiarvon. Argumentin teksti ja arvo EPÄTOSI lasketaan arvona 0. Arvo TOSI lasketaan arvona 1. Argumentit voivat olla lukuja, ni<PERSON><PERSON>, matriiseja tai viittauksia", "ad": "ovat 1 - 255 a<PERSON><PERSON><PERSON>, joista keski<PERSON><PERSON> lasket<PERSON>"}, "AVERAGEIF": {"a": "(alue; ehdot; [keski<PERSON>vo<PERSON>ue])", "d": "Määrittää tiettyjen ehtojen määrittämille soluille aritmeettisen keskiarvon", "ad": "on solualue, jonka haluat laskea!on ehto, joka voi olla keskiarvon laskemisessa käytettävät solut määrittävä luku, lauseke tai teksti!ovat todelliset solut, joiden avulla keskiarvo lasketaan. <PERSON><PERSON> arvo puuttuu, käytetään alueen soluja"}, "AVERAGEIFS": {"a": "(keskiar<PERSON>alue; ehtoalue; ehdot; ...)", "d": "Määrittää tiettyjen ehtojen määrittämille soluille aritmeettisen keskiarvon", "ad": "ovat todelliset solut, joiden avulla keskiarvo lasketaan.!ion solualue, jonka haluat laskea tietyllä ehdolla!on ehto, joka voi olla keskiarvon laskemiseen käytettävät solut määrittävä luku, lauseke tai teksti"}, "BETADIST": {"a": "(x; alfa; beeta; [A]; [B])", "d": "Palauttaa kumulatiivisen beeta-todennäköisyystiheysfunktion arvon", "ad": "on arvo väliltä A - B, jossa funktion arvo lasketaan!on jakauman parametri. Arvon täytyy olla positiivinen!on jakauman parametri. Arvon täytyy olla positiivinen!on valinnainen alaraja x:n arvoille. Jos arvoa ei määritetä, ohjelma käyttää arvoa A = 0!on valinnainen yläraja x:n arvoille. Jos arvoa ei määritetä, ohjelma käyttää arvoa B = 1"}, "BETAINV": {"a": "(todennäköisyys; alfa; beeta; [A]; [B])", "d": "Palauttaa kumulatiivisen beeta-todennäköisyystiheysfunktion (BEETAJAKAUMA) käänteisarvon", "ad": "on bee<PERSON>jakaumaan liittyvä todennäköisyys!on jakauman parametri. <PERSON><PERSON>von täytyy olla positiivinen!on jakauman parametri. <PERSON><PERSON>von täytyy olla positiivinen!on valinnainen alaraja x:n arvoille. Jos arvoa ei määritetä, ohjelma käyttää arvoa A = 0!on valinnainen yläraja x:n arvoille. Jos arvoa ei määritetä, ohjelma käyttää arvoa B = 1"}, "BETA.DIST": {"a": "(x; alfa; beeta; kertymä; [A]; [B])", "d": "Palauttaa beeta-todennäköisyysjakaumafunktion arvon", "ad": "on arvo väliltä A - B, jossa funktion arvo lasketaan!on jakauman parametri. <PERSON><PERSON>von täytyy olla positiivinen!on jakauman parametri. <PERSON>rvon täytyy olla positiivinen!on looginen arvo. <PERSON><PERSON> arvo on TOSI, funktio laskee todennäköisyyden kertymäfunktion. Jo<PERSON> arvo on EPÄTOSI, funktio laskee todennäköisyystiheysfunktion!on valinnainen alaraja x:n arvoille. Jos arvoa ei määritetä, ohjelma käyttää arvoa A = 0!on valinnainen yläraja x:n arvoille. Jos arvoa ei määritetä, ohjelma käyttää arvoa B = 1"}, "BETA.INV": {"a": "(todennäköisyys; alfa; beeta; [A]; [B])", "d": "Palauttaa kumulatiivisen beeta-todennäköisyystiheysfunktion (BEETA.JAKAUMA) käänteisarvon", "ad": "on bee<PERSON>jakaumaan liittyvä todennäköisyys!on jakauman parametri. <PERSON><PERSON>von täytyy olla positiivinen!on jakauman parametri. <PERSON><PERSON>von täytyy olla positiivinen!on valinnainen alaraja x:n arvoille. Jos arvoa ei määritetä, ohjelma käyttää arvoa A = 0!on valinnainen yläraja x:n arvoille. Jos arvoa ei määritetä, ohjelma käyttää arvoa B = 1"}, "BINOMDIST": {"a": "(luku_tot; yritykset; todennäköisyys_tot; kumulatiivinen)", "d": "Palauttaa yksittäisen termin binomijakauman todennäköisyyden", "ad": "on onnistuneiden yritysten määrä!on toisistaan riippumattomien yritysten määrä!on yrityksen onnistumisen todennäköisyys!on totuusarvo. <PERSON><PERSON> arvo on TOSI, jakaumalle lasketaan kertymäfunktio. <PERSON><PERSON> arvo on EPÄTOSI, jakaumalle lasketaan todennäköisyysmassafunktio"}, "BINOM.DIST": {"a": "(luku_tot; yritykset; todennäköisyys_tot; kumulatiivinen)", "d": "Palauttaa yksittäisen termin binomijakauman todennäköisyyden", "ad": "on onnistuneiden yritysten määrä!on toisistaan riippumattomien yritysten määrä!on yrityksen onnistumisen todennäköisyys!on totuusarvo. <PERSON><PERSON> arvo on TOSI, jakaumalle lasketaan  kertymäfunktio. <PERSON><PERSON> arvo on EPÄTOSI, jakaumalle lasketaan todennäköisyysmassafunktio"}, "BINOM.DIST.RANGE": {"a": "(kokeet; todennäköisyys_s; luku_s; [luku_s2])", "d": "Palauttaa kokeen tuloksen todennäköisyyden binomijakaumaa käyttämällä", "ad": "on itsenäisten kokeiden luku!on onnistumisen todennäköisyys jokaisessa kokeessa!on onnistumisen luku kokeissa!jos funktio on käytettävissä, se palauttaa todennäköisyyden, että onnistuneiden kokeiden luku on lukujen luku_s ja luku_s2 välillä"}, "BINOM.INV": {"a": "(yritykset; todennäköisyys_tot; alfa)", "d": "<PERSON><PERSON><PERSON><PERSON>, jossa binomijak<PERSON><PERSON> kertymäfunktion arvo on pienempi tai yhtä suuri kuin eh<PERSON>vo", "ad": "on Bernoulli-<PERSON><PERSON><PERSON> määrä!on y<PERSON>ksen onnist<PERSON>sen todennäköisyyttä osoittava luku välillä 0 - 1, päätepisteet mukaan lukien!on ehtoarvoa osoittava luku, joka on välillä 0 - 1, päätepisteet mukaan lukien"}, "CHIDIST": {"a": "(x; vapausasteet)", "d": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> chi-ne<PERSON><PERSON>n jakauman to<PERSON>äköisyyden", "ad": "on ei-negatiivinen arvo, jossa haluat laskea jakauman!on vapausasteiden määrää osoittava luku välillä 1 ja 10^10 (pois lukien 10^10)"}, "CHIINV": {"a": "(todennäköisyys; vapausasteet)", "d": "<PERSON><PERSON><PERSON><PERSON> chi-<PERSON><PERSON><PERSON>n o<PERSON>ole<PERSON>n jakauman k<PERSON>isa<PERSON>von", "ad": "on chi-<PERSON><PERSON><PERSON><PERSON> jaka<PERSON>an liittyvä todennäköisyys, jonka arvo on välillä 0 - 1 päätepisteet mukaan lukien!on vapausasteiden määrää osoittava luku välillä 1 ja 10^10 (pois lukien 10^10)"}, "CHITEST": {"a": "(todellinen_alue; odotettu_alue)", "d": "<PERSON><PERSON><PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eli chi-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> arvon ja vapausasteiden määrän", "ad": "on tietoalue, joka sisältää havaitut arvot, joita verrataan odotettuihin arvoihin!on tietoalue, joka sisältää rivi- ja sarakesummien tulon suhteen loppusummaan"}, "CHISQ.DIST": {"a": "(x; vapausa<PERSON>et; kertym<PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> chi-ne<PERSON><PERSON>n vasenhäntä<PERSON>n jakauman todennäköisyyden", "ad": "on ei-negatiivinen arvo, jossa haluat laskea jakauman!on vapausasteiden määrää osoittava luku välillä 1 - 10^10 (pois lukien 10^10)!on looginen arvo. <PERSON><PERSON> arvo on TOSI, jakaumalle lasketaan kertymäfunktio. <PERSON><PERSON> arvo on EPÄTOSI, jakaumalle lasketaan todennäköisyystiheysfunktio"}, "CHISQ.DIST.RT": {"a": "(x; vapausasteet)", "d": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>ntäisen chi-ne<PERSON><PERSON>n jakauman todennäköisyyden", "ad": "on ei-negatiivinen arvo, jossa haluat laskea jakauman!on vapausasteiden määrää osoittava luku välillä 1 - 10^10 (pois lukien 10^10)"}, "CHISQ.INV": {"a": "(todennäköisyys; vapausasteet)", "d": "<PERSON><PERSON><PERSON><PERSON> chi-<PERSON><PERSON><PERSON>n vasenhäntäisen jakauman käänteisarvon", "ad": "on chi-<PERSON><PERSON><PERSON><PERSON> jaka<PERSON>an liittyvä todennäköisyys, jonka arvo on välillä 0 - 1 päätepisteet mukaan lukien!on vapausasteiden määrää osoittava luku välillä 1 - 10^10 (pois lukien 10^10)"}, "CHISQ.INV.RT": {"a": "(todennäköisyys; vapausasteet)", "d": "<PERSON><PERSON><PERSON><PERSON> chi-<PERSON><PERSON><PERSON>n vasenhäntäisen jakauman käänteisarvon", "ad": "on chi-<PERSON><PERSON><PERSON><PERSON> jaka<PERSON>an liittyvä todennäköisyys, jonka arvo on välillä 0 - 1 päätepisteet mukaan lukien!on vapausasteiden määrää osoittava luku välillä 1 - 10^10 (pois lukien 10^10)"}, "CHISQ.TEST": {"a": "(todellinen_alue; odotettu_alue)", "d": "<PERSON><PERSON><PERSON><PERSON> r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, eli chi-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> arvo ja vapausasteiden määrän", "ad": "on tietoalue, joka sisältää havaitut arvot, joita verrataan odotettuihin arvoihin!on tietoalue, joka sisältää rivi- ja sarakesummien tulon suhteen loppusummaan"}, "CONFIDENCE": {"a": "(alfa; keskihajonta; koko)", "d": "Palauttaa populaation keskiarvon luottamusvälin normaalijakaumaa käyttäen", "ad": "on luottamustasoa laskettaessa käytettävää merkitsevyystasoa osoittava luku. Arvo on suurempi kuin 0 ja pienempi kuin 1!on populaation keskihajonta tietoalueella ja sen oletetaan olevan tunnettu. <PERSON>rg<PERSON><PERSON> täytyy olla positiivinen!on otoksen koko"}, "CONFIDENCE.NORM": {"a": "(alfa; keskihajonta; koko)", "d": "Palauttaa populaation keskiarvon luottamusvälin normaalijakaumaa käyttäen", "ad": "on luottamustasoa laskettaessa käytettävää merkitsevyystasoa osoittava luku. Arvo on suurempi kuin 0 ja pienempi kuin 1!on populaation keskihajonta tietoalueella ja sen oletetaan olevan tunnettu. Keskihajonta-argumentin täytyy olla positiivinen!on otoksen koko"}, "CONFIDENCE.T": {"a": "(alfa; keskihajonta; koko)", "d": "Palauttaa populaation keskiarvon luottamusvälin Studentin T-jakaumaa käyttäen", "ad": "on luottamustasoa laskettaessa käytettävää merkitsevyystasoa osoittava luku. Arvo on suurempi kuin 0 ja pienempi kuin 1!on populaation keskihajonta tietoalueella ja sen oletetaan olevan tunnettu. Keskihajonta-argumentin täytyy olla positiivinen!on otoksen koko"}, "CORREL": {"a": "(matriisi1; matriisi2)", "d": "Palauttaa kahden tietoalueen välisen korrelaatiokertoimen", "ad": "on arvojen solualue. Arvot voivat olla lukuja, ni<PERSON><PERSON>, matriiseja tai viittauksia lukuihin!on toinen arvojen solualue. Arvot voivat olla lukuja, ni<PERSON><PERSON>, matriiseja tai viittauksia lukuihin"}, "COUNT": {"a": "(arvo1; [arvo2]; ...)", "d": "Laskee alueen lukuja sisältävien solujen määrän", "ad": "ovat 1 - 255 <PERSON><PERSON>, jotka voivat sisältää useita erilaisia tietolajeja tai viitata niihin. <PERSON><PERSON><PERSON><PERSON> laskee kuitenkin vain luvut"}, "COUNTA": {"a": "(arvo1; [arvo2]; ...)", "d": "Laskee alueen tietoja sisältävien solujen määrän", "ad": "ovat 1 - 255 <PERSON><PERSON>, jotka vastaavat laskettavia arvoja ja soluja. Argumentit voivat olla mitä tahansa tie<PERSON>ä"}, "COUNTBLANK": {"a": "(alue)", "d": "Las<PERSON><PERSON> alueella olevien tyhjien solujen määrän", "ad": "on alue, jolta tyhjät solut lasketaan"}, "COUNTIF": {"a": "(alue; ehdot)", "d": "Las<PERSON><PERSON> annetun alueen solut, jotka täyttävät annetut ehdot", "ad": "on alue, jolta lasketaan tietoja sisältävät solut!on luvun, lausekkeen tai tekstin muodos<PERSON> oleva ehto, joka määrittää laskettavat solut"}, "COUNTIFS": {"a": "(eh<PERSON><PERSON><PERSON>; ehdot; ...)", "d": "Laskee määritettyjen ehtojen palauttamien solujen määrän", "ad": "on solualue, jonka haluat laskea tietyllä ehdolla!on ehto, joka voi olla laskettavat solut määrittävä luku, lauseke tai teksti"}, "COVAR": {"a": "(matriisi1; matriisi2)", "d": "<PERSON><PERSON><PERSON><PERSON>, eli kahden arvojoukon kaikkien arvopisteparien hajontojen tulojen keski<PERSON>von", "ad": "ensimmäinen kokonaislukualue. Arvojen täytyy olla lukuja, matriiseja tai viittauksia lukuihin!toinen kokonaislukualue. Arvojen täytyy olla lukuja, matriiseja tai viittauksia lukuihin"}, "COVARIANCE.P": {"a": "(matriisi1; matriisi2)", "d": "Palauttaa popula<PERSON> k<PERSON><PERSON><PERSON>, eli kahden arvojoukon kaikkien arvopisteparien hajontojen tulojen keskiarvon", "ad": "on ensimmäinen kokonaislukualue. Arvojen täytyy olla lukuja, matriiseja tai viittauksia lukuihin!on toinen kokonaislukualue. Arvojen täytyy olla lukuja, matriiseja tai viittauksia lukuihin"}, "COVARIANCE.S": {"a": "(matriisi1; matriisi2)", "d": "<PERSON><PERSON><PERSON><PERSON>, eli kahden arvojoukon kaikkien arvopisteparien hajontojen tulojen keskiarvon", "ad": "on ensimmäinen kokonaislukualue. Arvojen täytyy olla lukuja, matriiseja tai viittauksia lukuihin!on toinen kokonaislukualue. Arvojen täytyy olla lukuja, matriiseja tai viittauksia lukuihin"}, "CRITBINOM": {"a": "(yritykset; todennäköisyys_tot; alfa)", "d": "<PERSON><PERSON><PERSON><PERSON>, jossa binomijak<PERSON><PERSON> kertymäfunktion arvo on pienempi tai yhtä suuri kuin eh<PERSON>vo", "ad": "on Bernoulli-<PERSON><PERSON><PERSON> määrä!on y<PERSON>ksen onnist<PERSON>sen todennäköisyyttä osoittava luku välillä 0 - 1, päätepisteet mukaan lukien!on ehtoarvoa osoittava luku, joka on välillä 0 - 1, päätepisteet mukaan lukien"}, "DEVSQ": {"a": "(luku1; [luku2]; ...)", "d": "Pa<PERSON>ttaa keskipoikkeamien neliösumman", "ad": "ovat 1 - 255 <PERSON><PERSON>, matri<PERSON>a tai matri<PERSON><PERSON><PERSON><PERSON><PERSON>, joista haluat laskea keskipoikkeamien neliö<PERSON>mman"}, "EXPONDIST": {"a": "(x; lambda; kum<PERSON><PERSON><PERSON><PERSON>)", "d": "Palauttaa eksponent<PERSON>", "ad": "on funktion arvo ei-negatiivisena lukuna!on parametrin arvo. Argumentin täytyy olla positiivinen!määrittää palautettavan eksponentiaalifunktion lajin: TOSI = j<PERSON><PERSON> k<PERSON>mäfunktio; EPÄTOSI =  todennäköisyystiheysfunktio"}, "EXPON.DIST": {"a": "(x; lambda; kum<PERSON><PERSON><PERSON><PERSON>)", "d": "Palauttaa eksponent<PERSON>", "ad": "on funktion arvo ei-negatiivisena lukuna!on parametrin arvo. Argumentin täytyy olla positiivinen!määrittää palautettavan eksponentiaalifunktion lajin: TOSI = j<PERSON><PERSON> k<PERSON>mäfunktio; EPÄTOSI =  todennäköisyystiheysfunktio"}, "FDIST": {"a": "(x; vapausaste1; vapausaste2)", "d": "Palauttaa F-todennäkö<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (hajonnan aste) kahdesta tietosarjasta", "ad": "on arvo<PERSON>te, jossa funktion arvo lasketaan. <PERSON><PERSON><PERSON><PERSON> tä<PERSON>y olla positiivinen luku!on osoittajan vapausasteita osoittava luku, joka on välillä 1 - 10^10, pois lukien 10^10!on nimittäjän vapausasteita osoittava luku välillä 1 - 10^10, pois lukien 10^10"}, "FINV": {"a": "(todennäköisyys; vapausaste1; vapausaste2)", "d": "Palauttaa k<PERSON>änteisen (oikeanpuoleisen) F-todennäköisyysjakauman. Jos p = FJAKAUMA(x,...), niin <PERSON>.KÄÄNT(p,...) = x", "ad": "on <PERSON>-<PERSON><PERSON><PERSON> kertymäfunktioon liittyvää todennäköisyyttä osoittava luku välillä 0-1 päätepisteet mukaan lukien!on osoittajan vapausasteita osoittava luku välillä 1 - 10^10 pois lukien 10^10!on nimittäjän vapausasteita osoittava luku välillä 1 - 10^10 pois lukien 10^10"}, "FTEST": {"a": "(matriisi1; matriisi2)", "d": "Palauttaa F-testin tuloksen eli kaksisuuntaisen todennäköisyyden sille, että matriisien 1 ja 2 varianssit eivät ole merkittävästi erilaisia", "ad": "on ensimmäinen matriisi tai tietoalue. Arvot voivat olla lukuja tai lukuja sisältäviä nimiä, matriiseja tai viittauksia (tyhjiä soluja ei huomioida)!on toinen matriisi tai tietoalue. Arvot voivat olla lukuja tai lukuja sisältäviä nimiä, matriiseja tai viittauksia (tyhjiä soluja ei huomioida)"}, "F.DIST": {"a": "(x; vapausaste1; vapausaste2; kertymäfunktio)", "d": "Pa<PERSON><PERSON>a (vasenhäntäisen) F-todennäköisyysjakauman (hajonnan aste) kahdesta tietosarjasta", "ad": "on arvopiste, jossa funktion arvo lasketaan. <PERSON><PERSON><PERSON><PERSON> täyt<PERSON>y olla positiivinen luku!on osoittajan vapausasteita osoittava luku, joka on välillä 1 - 10^10, pois lukien 10^10!on nimittäjän vapausasteita osoittava luku välillä 1 - 10^10, pois lukien 10^10!on looginen arvo. <PERSON><PERSON> arvo on TOSI, funktion palauttaa jakauman kertymäfunktion. <PERSON><PERSON> arvo on EPÄTOSI, funktio palauttaa  todennäköisyystiheysfunktion"}, "F.DIST.RT": {"a": "(x; vapausaste1; vapausaste2)", "d": "Palauttaa (oikeahäntäisen) F-todennäköisyysjakauman (hajonnan aste) kahdesta tietosarjasta", "ad": "on arvo<PERSON>te, jossa funktion arvo lasketaan. <PERSON><PERSON><PERSON><PERSON> tä<PERSON>y olla positiivinen luku!on osoittajan vapausasteita osoittava luku, joka on välillä 1 - 10^10, pois lukien 10^10!on nimittäjän vapausasteita osoittava luku välillä 1 - 10^10, pois lukien 10^10"}, "F.INV": {"a": "(todennäköisyys; vapausaste1; vapausaste2)", "d": "Palauttaa k<PERSON>änteisen (vasenhäntäisen) F-todennäköisyysjakauman. Jos p = F.JAKAUMA(x,...), niin <PERSON>(p,...) = x", "ad": "on <PERSON>-<PERSON><PERSON><PERSON> kertymäfunktioon liittyvää todennäköisyyttä osoittava luku välillä 0 - 1 päätepisteet mukaan lukien!on osoittajan vapausasteita osoittava luku välillä 1 - 10^10 pois lukien 10^10!on nimittäjän vapausasteita osoittava luku välillä 1 - 10^10 pois lukien 10^10"}, "F.INV.RT": {"a": "(todennäköisyys; vapausaste1; vapausaste2)", "d": "Palauttaa käänteisen (oikeahäntäisen) F-todennäköisyysjakauman. Jos p = F.JAKAUMA.OH(x,...), niin F.KÄÄNT.OH(p,...) = x", "ad": "on <PERSON>-<PERSON><PERSON><PERSON> kertymäfunktioon liittyvää todennäköisyyttä osoittava luku välillä 0-1 päätepisteet mukaan lukien!on osoittajan vapausasteita osoittava luku välillä 1 - 10^10 pois lukien 10^10!on nimittäjän vapausasteita osoittava luku välillä 1 - 10^10 pois lukien 10^10"}, "F.TEST": {"a": "(matriisi1; matriisi2)", "d": "Palauttaa F-testin tuloksen eli kaksisuuntaisen todennäköisyyden sille, että matriisien 1 ja 2 varianssit eivät ole merkittävästi erilaisia", "ad": "on ensimmäinen matriisi tai tietoalue. Arvot voivat olla lukuja tai lukuja sisältäviä nimiä, matriiseja tai viittauksia (tyhjiä soluja ei huomioida)!on toinen matriisi tai tietoalue. Arvot voivat olla lukuja tai lukuja sisältäviä nimiä, matriiseja tai viittauksia (tyhjiä soluja ei huomioida)"}, "FISHER": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "ad": "on -1 ja 1 välissä (päätepisteet poislukien) o<PERSON>a luku, jolle haluat suorittaa muunnoksen"}, "FISHERINV": {"a": "(y)", "d": "Palauttaa käänteisen Fisherin muunnoksen. Jos y = FISHER(x), niin FISHER.KÄÄNT(y) = x", "ad": "on arvo, jolle haluat suorittaa käänteismuunnoksen"}, "FORECAST": {"a": "(x; tunnettu_y; tunnettu_x)", "d": "Laskee tai ennustaa arvojen lineaarisen trendin aiempien arvojen perusteella", "ad": "on arvo<PERSON>te, jolle haluat ennustaa arvon. <PERSON><PERSON><PERSON><PERSON> tä<PERSON>y olla numeerinen arvo!on riippuva matriisi tai tietoalue!on riippumaton matriisi tai tietoalue. Tunnettujen arvojen varianssi ei saa olla nolla"}, "FORECAST.ETS": {"a": "(mä<PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON>; arvo<PERSON>; aika<PERSON>; [kausivaihtelu]; [tieto<PERSON><PERSON>_viimeistely]; [koon<PERSON>])", "d": "Palauttaa tietyn tulevan määräpäivän ennustearvon eksponentiaalisella tasoitusmenetelmällä.", "ad": "on arvopiste, jolle Spreadsheet Editor ennus<PERSON>a arvon. <PERSON><PERSON><PERSON> tulee noudattaa aikajanan arvomallia.!on matriisi tai tietojoukko, jota ennuste koskee.!on riippumaton matriisi tai tietojoukko. Aikajanan päivämäärien tulee olla tasavälein. Arvo ei saa olla nolla.!on valinnainen numeerinen arvo., joka ilmoittaa kausivaihtelumallin pituuden. Oletusarvo 1 tarkoittaa, että kausivaihtelu on havaittu automaattisesti.!on valinnainen arvo, jota käytetään puuttuvien arvojen käsittelyssä. Oletusarvo 1 korvaa puuttuvat arvot interpoloimalla, arvo 0 korvaa ne nollilla.!on valinnainen numeerinen arvo, jota käytetään saman aikaleiman arvojen kokoamiseen. <PERSON><PERSON>, Spreadsheet Editor laskee arvojen keskiarvot."}, "FORECAST.ETS.CONFINT": {"a": "(määr<PERSON><PERSON><PERSON><PERSON><PERSON>; arvo<PERSON>; aika<PERSON>; [luotta<PERSON><PERSON><PERSON><PERSON>]; [kausivaihtelu]; [tieto<PERSON><PERSON>_viimeistely]; [koon<PERSON>])", "d": "Palauttaa tietyn tulevan määräpäivän ennustearvon luottamusvälin.", "ad": "on arvopiste, jolle Spreadsheet Editor ennus<PERSON>a arvon. <PERSON><PERSON><PERSON> tulee noudattaa aikajanan arvomallia.!on matriisi tai tietojoukko, jota ennuste koskee.!on riippumaton matriisi tai tietojoukko. Aikajanan päivämäärien tulee olla tasavälein. Arvo ei saa olla nolla.!on numeerinen arvo välillä 0-1, joka ilmaisee laskennallisen luottamusvälin luottamustason. Oletusarvo on 95 prosenttia.!on valinnainen numeerinen arvo., joka ilmoittaa kausivaihtelumallin pituuden. Oletusarvo 1 tarkoittaa, että kausivaihtelu on havaittu automaattisesti.!on valinnainen arvo, jota käytetään puuttuvien arvojen käsittelyssä. Oletusarvo 1 korvaa puuttuvat arvot interpoloimalla, arvo 0 korvaa ne nollilla.!on valinnainen numeerinen arvo, jota käytetään saman aikaleiman arvojen kokoamiseen. <PERSON><PERSON>, Spreadsheet Editor laskee arvojen keskiarvot."}, "FORECAST.ETS.SEASONALITY": {"a": "(arvot; aikajana; [tie<PERSON><PERSON>n_viimeistely]; [koon<PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> to<PERSON> mallin <PERSON>, jonka sovellus tunnistaa annet<PERSON>a a<PERSON>.", "ad": "on matriisi tai tietojoukko, jota ennuste koskee.!on riippumaton matriisi tai tietojoukko. Aikajanan päivämäärien tulee olla tasav<PERSON>lein. Arvo ei saa olla nolla.!on valinnainen arvo, jota käytetään puuttuvien arvojen käsittelyssä. Oletusarvo 1 korvaa puuttuvat arvot interpoloimalla, ja 0 korvaa arvot nollilla.!on valinnainen numeerinen arvo, jota käytetään saman aikaleiman arvojen kokoamisessa. <PERSON><PERSON>, Spreadsheet Editor laskee arvojen keskiarvon."}, "FORECAST.ETS.STAT": {"a": "(arvo<PERSON>; aikajana; tilastotyyppi; [kausivaihtelu]; [tie<PERSON><PERSON><PERSON>_viimeistely]; [koon<PERSON>])", "d": "<PERSON><PERSON><PERSON>a ennusteen pyydetyn tilastotiedon.", "ad": "on matriisi tai tietojoukko, jota ennuste koskee.!on riippuva matriisi tai tietojoukko. Aikajanan päivämäärien tulee olla tasavälein. Arvo ei saa olla nolla.!on numero välillä 1-8, joka ilmaisee minkä tilastotiedon Spreadsheet Editor palauttaa lasketulle ennusteelle. !on valinnainen numeerinen arvo, joka ilmaisee kausivaihtelumallin pituuden. Oletusarvo 1 ilmaisee, että kausivaihtelu on havaittu automaattisesti.!on valinnainen arvo puuttuvien arvojen käsittelyyn. Oletusarvo 1 korvaa puuttuvat arvot interpoloimalla, ja 0 korvaa arvot nollilla.!on valinnainen numeerinen arvo, jota käytetään saman aikaleiman arvojen kokoamisessa. <PERSON><PERSON>, Spreadsheet Editor laskee arvojen keskiarvon."}, "FORECAST.LINEAR": {"a": "(x; tunnettu_y; tunnettu_x)", "d": "Laskee tai ennustaa arvojen lineaarisen trendin aiempien arvojen perusteella", "ad": "on arvo<PERSON>te, jolle haluat ennustaa arvon. <PERSON><PERSON><PERSON><PERSON> tä<PERSON>y olla numeerinen arvo!on riippuva matriisi tai tietoalue!riippumaton matriisi tai tietoalue. Tunnettujen arvojen varianssi ei saa olla nolla"}, "FREQUENCY": {"a": "(tieto_matriisi; lohko_matriisi)", "d": "<PERSON><PERSON><PERSON>, kuinka usein arvot esiintyvät arvoalueessa ja palauttaa pystymatriisin, jossa on yksi elementti enemmän kuin Bins_matriisissa", "ad": "on arvosarjan matriisi tai viittaus ar<PERSON>, jonka ta<PERSON><PERSON><PERSON><PERSON> haluat laskea (tyhj<PERSON> alueet ja teksti ohiteta<PERSON>)!on välien matriisi tai viittaus vä<PERSON>, joihin haluat ryhmitellä tieto_matriisin arvot"}, "GAMMA": {"a": "(x)", "d": "Palauttaa Gamma-funktion arvon", "ad": "on arvo, jolle haluat laskea gamman"}, "GAMMADIST": {"a": "(x; alfa; beeta; kum<PERSON><PERSON><PERSON><PERSON>)", "d": "Palauttaa gamma-jakauman", "ad": "on ei-negatiivinen arvo, jossa haluat laskea jakauman!on jakauman parametri. <PERSON><PERSON><PERSON> tä<PERSON>y olla positiivinen!on positiivinen jakauman parametri. <PERSON><PERSON> beeta = 1, funktio palauttaa vakiomuotoisen gamma-jakauman!on totu<PERSON><PERSON><PERSON>, joka määrittää, kumpi jakauma palautetaan. <PERSON><PERSON> arvo on TOSI, ohjelma palauttaa jakauman kertymäfunktion. Jo<PERSON> arvo on EPÄTOSI tai puuttuu, ohjelma palauttaa todennäköisyysmassafunktion"}, "GAMMA.DIST": {"a": "(x; alfa; beeta; kum<PERSON><PERSON><PERSON><PERSON>)", "d": "Palauttaa gamma-jakauman", "ad": "on ei-negatiivinen arvo, jossa haluat laskea jakauman!on jakauman parametri. <PERSON><PERSON><PERSON> tä<PERSON>y olla positiivinen!on positiivinen jakauman parametri. <PERSON><PERSON> beeta = 1, funktio palauttaa vakiomuotoisen gamma-jakauman!on totu<PERSON><PERSON><PERSON>, joka määrittää, kumpi jakauma palautetaan. <PERSON><PERSON> arvo on TOSI, ohjelma palauttaa jakauman kertymäfunktion. Jo<PERSON> arvo on EPÄTOSI tai puuttuu, ohjelma palauttaa todennäköisyysmassafunktion"}, "GAMMAINV": {"a": "(todennäköisyys; alfa; beeta)", "d": "Palauttaa käänteisen gamma-jakauman kertymäfunktion: jos p = GAMMAJAKAUMA(x,...), niin GAMMAJAKAUMA.KÄÄNT(p,...) = x", "ad": "on gamma-jakaumaan liittyvää todennäköisyyttä osoittava luku. Luku on välillä 0 ja 1 (p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mukaan lukien)!on jakauman parametri. Parametrin on oltava positiivinen luku!on jakauman parametria osoittava positiivinen luku. <PERSON><PERSON> bee<PERSON> = 1, funktio palauttaa käänteisen vakiomuotoisen gamma-jakauman"}, "GAMMA.INV": {"a": "(todennäköisyys; alfa; beeta)", "d": "Palauttaa käänteisen gamma-jakauman kertymäfunktion: jos p = GAMMA.JAKAUMA(x,...), niin GAMMA.JAKAUMA.KÄÄNT(p,...) = x", "ad": "on gamma-jakaumaan liittyvää todennäköisyyttä osoittava luku. Luku on välillä 0 - 1 (p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mukaan lukien)!on jakauman parametri. Parametrin on oltava positiivinen luku!on jakauman parametria osoittava positiivinen luku. <PERSON><PERSON> bee<PERSON> = 1, GAMMA.JAKAUMA.KÄÄNT-funktio palauttaa käänteisen vakiomuotoisen gamma-jakauman"}, "GAMMALN": {"a": "(x)", "d": "Palauttaa gamma-funktion luonnollisen logaritmin", "ad": "on funktion GAMMALN argumentti. <PERSON><PERSON><PERSON> t<PERSON> olla positiivinen"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Palauttaa gamma-funktion luonnollisen logaritmin", "ad": "on arvo, jolle haluat laskea kohteen GAMMALN.TARKKA, positiivisen luvun"}, "GAUSS": {"a": "(x)", "d": "Palauttaa 0,5 vähemmän kuin normaali kumulatiivinen vakiojakauma", "ad": "on arvo, jolle haluat laskea jakauman"}, "GEOMEAN": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa matriisin tai positiivisten lukuarvojen geometrisen keskiarvon", "ad": "ovat 1 - 255 lukua, <PERSON><PERSON><PERSON>, matri<PERSON>a tai vii<PERSON><PERSON>a luku<PERSON>, joille haluat laskea geometrisen keskiarvon"}, "GROWTH": {"a": "(tunnettu_y; [tunnettu_x]; [uusi_x]; [vakio])", "d": "Palauttaa eksponentiaalisen kasvutrendin luvut, jotka vastaavat tunnettuja tietopisteitä", "ad": "on joukko y-arvoja, jotka tunnetaan yhtälöstä y = b*m^x. Arvot ovat matriisi tai joukko positiivisia lukuja!on valinnainen joukko x-arvoja, jotka tunnetaan yhtälöstä y = b*m^x. Arvot ovat samankokoinen matriisi tai arvojoukko kuin tunnetut y-arvot!ovat uudet x:n arvot, joille KASVU-funktion halutaan palauttavan vastaavat y:n arvot!on totuusarvo, joka määrittää, mikä tulee b:n arvoksi. <PERSON><PERSON> arvo on TOSI, vakio b lasketaan normaalisti. Jos arvo puuttuu tai on EPÄTOSI, b:n arvoksi tulee 1"}, "HARMEAN": {"a": "(luku1; [luku2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> harmonisen keskiarvon positiivisesta lukujou<PERSON>ta", "ad": "ovat 1 - 255 lukua, <PERSON><PERSON><PERSON>, matri<PERSON>a tai vii<PERSON><PERSON><PERSON> luku<PERSON>, joille haluat laskea harmonisen keskiarvon"}, "HYPGEOM.DIST": {"a": "(otos_tot; luku_otos; populaatio_tot; populaatio_luku; kertymä)", "d": "<PERSON><PERSON><PERSON><PERSON> hyperge<PERSON><PERSON> j<PERSON>", "ad": "on onnistumisten määrä otoksessa!on otoksen koko!on onnistumisten määrä populaatiossa!on populaation koko!on looginen arvo. <PERSON><PERSON> arvo on TOSI, jakaumalle lasketaan kertymäfunktio. <PERSON><PERSON> arvo on EPÄTOSI, jakaumalle lasketaan todennäköisyystiheysfunktio"}, "HYPGEOMDIST": {"a": "(otos_tot; luku_otos; populaatio_tot; populaatio_luku)", "d": "<PERSON><PERSON><PERSON><PERSON> hyperge<PERSON><PERSON> j<PERSON>", "ad": "on onnistumisten määrä otoksessa!on otoksen koko!on onnistumisten määrä populaatiossa!on populaation koko"}, "INTERCEPT": {"a": "(tunnettu_y; tunnettu_x)", "d": "Palauttaa lineaarisen regressiosuoran ja  y-a<PERSON><PERSON>. Regressiosuora piirretään tunnettujen x-arvojen ja y-arvojen avulla", "ad": "on riippuva joukko havaintoja tai tietoja. Arvot voivat olla nimiä, matriiseja tai viittauksia lukuihin (tyhjiä soluja ei huomioida)!on riippumaton joukko havaintoja tai tietoja. Arvot voivat olla nimiä, matriiseja tai viittauksia lukuihin (tyhjiä soluja ei huomioida)"}, "KURT": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa tietojoukon kurtosis-arvon", "ad": "ovat 1 - 255 lukua, <PERSON><PERSON><PERSON>, matri<PERSON>a tai vii<PERSON><PERSON><PERSON> luku<PERSON>, joista haluat laskea kurtosis-arvon"}, "LARGE": {"a": "(matriisi; k)", "d": "Palauttaa tietoalueen k:nneksi suurimman arvon. Esimerkiksi viidenneksi suurimman arvon", "ad": "on matriisi tai tietoalue, josta haluat määrittää k:nneksi suurimman arvon!on palautettavan arvon sijainti (suurimmasta pienimpään) matriisissa tai solualueessa"}, "LINEST": {"a": "(tunnettu_y; [tunnettu_x]; [vakio]; [tilasto])", "d": "<PERSON><PERSON><PERSON><PERSON>, jotka kuva<PERSON>t tietopisteisiin sovitettua lineaarista trendiä. <PERSON><PERSON> on laskettu neliösummamenetelmällä", "ad": "on joukko y-arvoja, jotka tunnetaan yhtälöstä y = mx + b!on vali<PERSON>inen joukko x-arvoja, jotka ehkä tunnetaan yhtälöstä y = mx + b!on totuusarvo, joka mä<PERSON><PERSON>, miten vakion b arvo lasketaan. <PERSON><PERSON> arvo on TOSI tai jätetty pois, b:n arvo lasketaan normaalisti. <PERSON><PERSON> arvo on EPÄTOSI, b = 0!on totuusarvo. <PERSON><PERSON> arvo on TOSI, funktio palauttaa lisää regressiotilastoja. Jos arvo puuttuu tai on EPÄTOSI, funktio palauttaa m-kertoimen ja b:n arvon"}, "LOGEST": {"a": "(tunnettu_y; [tunnettu_x]; [vakio]; [tilasto])", "d": "<PERSON><PERSON><PERSON><PERSON> an<PERSON><PERSON><PERSON> tietopisteisiin sovitetun eksponentiaalisen käyrän tilastotiedot", "ad": "on joukko y-arvoja, jotka tunnetaan yhtälöstä y = b*m^x!on vali<PERSON>inen joukko x-arvoja, jotka ehkä tunnetaan yhtälöstä y = b*m^x!on totuusarvo, joka mä<PERSON><PERSON>, miten vakion b arvo lasketaan. <PERSON><PERSON> arvo on TOSI tai jätetty pois, b:n arvo lasketaan normaalisti. <PERSON><PERSON> arvo on EPÄTOSI, b = 1!on totuusarvo. <PERSON><PERSON> arvo on TOSI, funktio palauttaa lisää regressiotilastoja. Jos arvo puuttuu tai on EPÄTOSI, funktio palauttaa m-kertoimen ja b:n arvon"}, "LOGINV": {"a": "(todennäköisyys; keskiarvo; keskihajonta)", "d": "Palauttaa x:n käänteisjakauman kumulatiivisesta ja logaritmisesta normaalijakaumasta, jossa ln(x) jakautuu normaalijakauman mukaisesti parametrien Keskiarvo ja Keskipoikkeama osoittamalla tavalla", "ad": "on logaritmiseen normaalijakaumaan liittyvää todennäköisyyttä osoittava luku väliltä 0 - 1 välin päätepisteet mukaan lukien!on ln(x):n keskiarvo!on ln(x):n keskihajonta. <PERSON><PERSON> on positiivinen luku"}, "LOGNORM.DIST": {"a": "(x; keski<PERSON><PERSON>; keski<PERSON><PERSON><PERSON>; kertym<PERSON>)", "d": "Palauttaa x:n logaritmisen norm<PERSON><PERSON><PERSON><PERSON>, jossa ln(x) jakautuu normaal<PERSON> mukaisesti parametrien Ke<PERSON>arvo ja Keskihajonta osoittama<PERSON> ta<PERSON>la", "ad": "on positiivinen lukuarvo, jossa funktion arvo lasketaan!on ln(x):n keskiarvo!on ln(x):n keski<PERSON><PERSON>ta, positiivinen luku!on looginen arvo. <PERSON><PERSON> arvo on TOSI, jakaumalle lasketaan kertymäfunktio. <PERSON><PERSON> arvo on EPÄTOSI, jakaumalle lasketaan todennäköisyystiheysfunktio"}, "LOGNORM.INV": {"a": "(todennäköisyys; keskiarvo; keskihajonta)", "d": "Palauttaa x:n käänteisjakauman kumulatiivisesta ja logaritmisesta normaalijakaumasta, jossa ln(x) jakautuu normaalijakauman mukaisesti parametrien Keskiarvo ja Keskihajonta osoittamalla tavalla", "ad": "on logaritmiseen normaalijakaumaan liittyvää todennäköisyyttä osoittava luku väliltä 0 - 1 v<PERSON>lin päätepisteet mukaan lukien!on ln(x):n keskiarvo!on ln(x):n kes<PERSON><PERSON><PERSON><PERSON>, positiivinen luku"}, "LOGNORMDIST": {"a": "(x; keski<PERSON><PERSON>; keskihajon<PERSON>)", "d": "Palauttaa x:n kumulatiivisen ja logaritmisen normaal<PERSON>, jossa ln(x) jakautuu normaal<PERSON> mukaisesti parametrien Keskiarvo ja Keskipoikkeama osoittamalla tavalla", "ad": "on positiivinen lukuarvo, jossa funktion arvo lasketaan!on ln(x):n keskiarvo!on ln(x):n keskihajonta. <PERSON><PERSON> on positiivinen luku"}, "MAX": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa suurimman luvun arvojoukosta. Totuusarvot ja merkkijonot jätetään huomioimatta", "ad": "ovat 1 - 255 lukua, t<PERSON><PERSON><PERSON><PERSON><PERSON> solua, totuusarvoa tai tekstimuotoista lukua, joiden joukosta haluat löytää suurimman arvon"}, "MAXA": {"a": "(arvo1; [arvo2]; ...)", "d": "Palauttaa arvojoukon suurimman arvon. Funktio ottaa my<PERSON> huo<PERSON>on loogiset arvot ja tekstin", "ad": "ovat 1 - 255 lukua, t<PERSON><PERSON><PERSON><PERSON><PERSON> solua, totuusarvoa tai tekstimuotoista lukua, joista et<PERSON>t<PERSON><PERSON>n suurinta arvoa"}, "MAXIFS": {"a": "(ma<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; krite<PERSON>; ...)", "d": "Palauttaa suurimman arvon annetut ehdot täyttävistä soluista", "ad": "solut, joita käytetään enimmäisarvon määrittämiseen!on tietyn ehdon mukaan arvioitava solualue!on numerona, lausekkeena tai tekstinä ilmaistu ehto, joka määrittää suurimman arvon määrittämiseen käytettävät solut"}, "MEDIAN": {"a": "(luku1; [luku2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> annettujen lukujen mediaanin eli annetun lukujoukon keskimmäisen arvon", "ad": "ovat 1 - 255 lukua, <PERSON><PERSON><PERSON>, matri<PERSON>a tai lukuihin koh<PERSON> viittausta, joista haluat et<PERSON><PERSON> mediaanin"}, "MIN": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa pienimmän luvun arvojoukosta. Jättää  totuusarvot ja tekstin huomiotta", "ad": "ovat 1  - 255 lukua, t<PERSON><PERSON><PERSON><PERSON><PERSON> solua, to<PERSON><PERSON>rvoa tai tekstimuotoista lukua, joista haluat etsi<PERSON> pienimmän"}, "MINA": {"a": "(arvo1; [arvo2]; ...)", "d": "Palauttaa arvojoukon pienimmän arvon. Funktio ottaa my<PERSON> huo<PERSON>on loogiset arvot ja tekstin", "ad": "ovat 1 - 255 lukua, t<PERSON><PERSON><PERSON><PERSON><PERSON> solua, totuusarvoa tai tekstimuotoista lukua, joista etsit<PERSON>än pienintä arvoa"}, "MINIFS": {"a": "(v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; eh<PERSON><PERSON><PERSON>; ehdo<PERSON>; ...)", "d": "Palauttaa pienimmän arvon annetut ehdot täyttävistä soluista", "ad": "solut, joista pienin arvo määritetään!on tietyn ehdon mukaan arvioitava solualue!on numerona, lausekkeena tai tekstinä ilmaistu ehto, joka määrittää pienimmän arvon määrittämiseen käytettävät solut"}, "MODE": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa matriisissa tai tietoalueella useimmin tai toistu<PERSON>ti esiint<PERSON>vän arvon", "ad": "ovat 1 - 255 lukua, <PERSON><PERSON><PERSON>, matri<PERSON>a tai vii<PERSON><PERSON><PERSON> luku<PERSON>, joista haluat laskea moodin"}, "MODE.MULT": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa pystymatriisin matriisissa tai tietoalueella useimmin tai toistuvasti esiintyvistä arvoista. Jo<PERSON> haluat vaakamatriisin, käytä =TRANSPONOI(MOODI.USEA(luku1,luku2),...)-funktiota", "ad": "ovat 1 - 255 lukua, <PERSON><PERSON><PERSON>, matri<PERSON>a tai vii<PERSON><PERSON><PERSON> luku<PERSON>, joista haluat laskea moodin"}, "MODE.SNGL": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa matriisissa tai tietoalueella useimmin tai toistu<PERSON>ti esiint<PERSON>vän arvon", "ad": "ovat 1 - 255 lukua, <PERSON><PERSON><PERSON>, matri<PERSON>a tai vii<PERSON><PERSON><PERSON> luku<PERSON>, joista haluat laskea moodin"}, "NEGBINOM.DIST": {"a": "(luku_epäon; luku_tot; todennäköisyys_tot; kertymä)", "d": "Palauttaa negatiivisen binomijakauman eli todennäköisyyden, että ennen luku_tot:ttä onnistumista tapahtuu luku_epäon epäonnistumista onnistumistodennäköisyydellä todennäköisyys_tot", "ad": "on epäonnistumisten määrä!on onnistumisten kynnysarvo!on onnistumisen todennäköisyyttä osoittava luku välillä 0 - 1!on looginen arvo. <PERSON><PERSON> arvo on TOSI, jakaumalle lasketaan kertymäfunktio. <PERSON><PERSON> arvo on EPÄTOSI, jakaumalle lasketaan todennäköisyysmassafunktio"}, "NEGBINOMDIST": {"a": "(luku_epäon; luku_tot; todennäköisyys_tot)", "d": "Palauttaa negatiivisen binomijakauman eli todennäköisyyden, että ennen luku_tot:ttä onnistumista tapahtuu luku_epäon epäonnistumista onnistumistodennäköisyydellä todennäköisyys_tot", "ad": "on epäonnistumisten määrä!on onnistumisten kynnysarvo!on onnistumisen todennäköisyyttä osoittava luku välillä 0 - 1"}, "NORM.DIST": {"a": "(x; kes<PERSON><PERSON><PERSON>; kes<PERSON><PERSON><PERSON><PERSON>; k<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> määritetylle keskiarvolle ja -hajonnalle", "ad": "on arvo, jolle haluat jakauman!on jakauman aritmeettinen keskiarvo!on jakauman keskihajonta. Arvo on positiivinen luku!on totuusarvo. <PERSON><PERSON> arvo on TOSI, jakaumalle lasketaan kertymäfunktio. <PERSON><PERSON> arvo on EPÄTOSI, jakaumalle lasketaan todennäköisyystiheysfunktio"}, "NORMDIST": {"a": "(x; kes<PERSON><PERSON><PERSON>; kes<PERSON><PERSON><PERSON><PERSON>; k<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Pa<PERSON><PERSON><PERSON> kertymäfunktion määritetylle keskiarvolle ja -hajonnalle", "ad": "on arvo, jolle haluat jakauman!on jakauman aritmeettinen keskiarvo!on jakauman keskihajonta. Arvo on positiivisena luku!on totuusarvo. <PERSON><PERSON> arvo on TOSI, jakaumalle lasketaan  kertymäfunktio. <PERSON><PERSON> arvo on EPÄTOSI, jakaumalle lasketaan todennäköisyyden tiheysfunktio"}, "NORM.INV": {"a": "(todennäköisyys; keskiarvo; keskihajonta)", "d": "Palauttaa käänteisen normaalijakauman kertymäfunktion määritetylle keskiarvolle ja -hajonnalle", "ad": "on normaalijakaumaan liittyvä todennäköisyyttä osoittava luku välillä 0 - 1 päätepisteet mukaan lukien!on jakauman aritmeettinen keskiarvo!on jakauman keskihajontaa osoittava positiivinen luku"}, "NORMINV": {"a": "(todennäköisyys; keskiarvo; keskihajonta)", "d": "Palauttaa käänteisen normaalijakauman kertymäfunktion määritetylle keskiarvolle ja -hajonnalle", "ad": "on normaalijakaumaan liittyvä todennäköisyyttä osoittava luku välillä 0 - 1 päätepisteet mukaan lukien!on jakauman aritmeettinen keskiarvo!on jakauman keskihajontaa osoittava positiivinen luku"}, "NORM.S.DIST": {"a": "(z; kertymä)", "d": "Palauttaa normitetun normaalija<PERSON>uman (keskiarvo 0 ja keskihajonta 1)", "ad": "on arvo, jolle haluat jakauman!on looginen arvo. <PERSON><PERSON> arvo on TOSI, funktio palauttaa kertymäfunktion. <PERSON><PERSON> arvo on EPÄTOSI, funktio palauttaa todennäköisyystiheysfunktion"}, "NORMSDIST": {"a": "(z)", "d": "Palauttaa normitetun normaal<PERSON>uman kertymäfunktion, jonka keskiarvo on 0 ja keskihajonta 1", "ad": "on arvo, jolle haluat jakauman"}, "NORM.S.INV": {"a": "(todennäköisyys)", "d": "Palauttaa käänteisen normitetun normaalijakauman kertymäfunktion, jonka keskiar<PERSON> on nolla ja keskihajonta 1", "ad": "on normaalijakaumaan liittyvää todennäköisyyttä osoittava luku välillä 0 - 1 päätepisteet mukaan lukien"}, "NORMSINV": {"a": "(todennäköisyys)", "d": "Palauttaa käänteisen normitetun normaalijakauman kertymäfunktion, jonka keskiar<PERSON> on nolla ja keskihajonta 1", "ad": "on normaalijakaumaan liittyvää todennäköisyyttä osoittava luku välillä 0 - 1 päätepisteet mukaan lukien"}, "PEARSON": {"a": "(matriisi1; matriisi2)", "d": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>aati<PERSON>n", "ad": "on joukko riippumattomia arvoja!on joukko riippuvia arvoja"}, "PERCENTILE": {"a": "(matriisi; k)", "d": "Palauttaa k:nnen prosentti<PERSON>uuden alueen arvoista", "ad": "on matriisi tai tietoalue, joka määrittää suhteellisen sijainnin!on prosenttipistearvo väliltä 0 - 1 (0 ja 1 mukaan lukien)"}, "PERCENTILE.EXC": {"a": "(matriisi; k)", "d": "Palauttaa k:nnen prosentti<PERSON>uden alueen arvoista, jossa k on välillä 0 - 1 päätepisteet pois lukien", "ad": "on matriisi tai tietoalue, joka määrittää suhteel<PERSON>en sijainnin!on prosenttipistearvo väliltä 0 - 1 päätepisteet mukaan lukien"}, "PERCENTILE.INC": {"a": "(matriisi; k)", "d": "Palauttaa k:nnen prosentti<PERSON>uden alueen arvoista, jossa k on välillä 0 - 1 päätepisteet mukaan lukien", "ad": "on matriisi tai tietoalue, joka määrittää suhteel<PERSON>en sijainnin!on prosenttipistearvo väliltä 0 - 1 päätepisteet mukaan lukien"}, "PERCENTRANK": {"a": "(matriisi; x; [tark<PERSON>us])", "d": "Palautta<PERSON> a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ad": "on numeerinen matriisi tai tietoalue, joka määrittää suhteellisen sijainnin!on arvo, jolle haluat löytää sijoituksen!on valinnainen arvo, joka määrittää, montako merkitsevää numeroa palautettavassa prosenttiluvussa on. Jos arvoa ei määritetä, ohjelma käyttää kolmea numeroa (0,xxx%)"}, "PERCENTRANK.EXC": {"a": "(matriisi; x; [tark<PERSON>us])", "d": "Palauttaa arvon prosenttijärjestyksen tietojoukossa prosenttijärjestyksenä (0 - 1, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pois lukien) tietojoukossa", "ad": "on numeerinen matriisi tai tietoalue, joka määrittää suhteellisen sijainnin!on arvo, jolle haluat löytää sijoituksen!on valinnainen arvo, joka määrittää, montako merkitsevää numeroa palautettavassa prosenttiluvussa on. Jos arvoa ei määritetä, ohjelma käyttää kolmea numeroa (0,xxx%)"}, "PERCENTRANK.INC": {"a": "(matriisi; x; [tark<PERSON>us])", "d": "Palauttaa arvon prosenttijärjestyksen tietojoukossa prosenttijärjestyksenä (0 - 1, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mukaan lukien) tietojoukossa", "ad": "on numeerinen matriisi tai tietoalue, joka määrittää suhteellisen sijainnin!on arvo, jolle haluat löytää sijoituksen!on valinnainen arvo, joka määrittää, montako merkitsevää numeroa palautettavassa prosenttiluvussa on. Jos arvoa ei määritetä, ohjelma käyttää kolmea numeroa (0,xxx%)"}, "PERMUT": {"a": "(luku; valittu_luku)", "d": "Palauttaa permutaatioiden määrän kaikista valittavissa olevista objekteista valituille objekteille", "ad": "on objektien määrä!on objektien määrä jokaisessa permutaatiossa"}, "PERMUTATIONA": {"a": "(luku; valittu_luku)", "d": "Palauttaa permutaatioiden määrän kaikista valittavissa olevista objekteista valituille objekteille (toistojen kanssa)", "ad": "on objektien kokonaismäärä!on objektien määrä jokaisessa permutaatiossa"}, "PHI": {"a": "(x)", "d": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>funktion arvon norm<PERSON> v<PERSON>", "ad": "on luku, jolle haluat laskea normaalin v<PERSON><PERSON> tiheyden"}, "POISSON": {"a": "(x; kes<PERSON><PERSON><PERSON>; k<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "ad": "on tapahtumien lukumäärä!on odotettua numeerista arvoa osoittava positiivinen luku!on totuusarvo. <PERSON><PERSON> arvo on TOSI, lasketaan Poissonin todennäköisyyden kertymäfunktio. Jo<PERSON> arvo on EPÄTOSI, lasketaan Poissonin todennäköisyysmassafunktio"}, "POISSON.DIST": {"a": "(x; kes<PERSON><PERSON><PERSON>; k<PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "ad": "on tapahtumien lukumäärä!on odotettua numeerista arvoa osoittava positiivinen luku!on totuusarvo. <PERSON><PERSON> arvo on TOSI, lasketaan Poissonin todennäköisyyden kertymäfunktio. Jo<PERSON> arvo on EPÄTOSI, lasketaan Poissonin todennäköisyysmassafunktio"}, "PROB": {"a": "(x_alue; todnäk_alue; alaraja; [yl<PERSON><PERSON><PERSON>])", "d": "Palauttaa todennäköisyyden sille, että alueen arvot ovat kahden rajan välissä tai yhtä suuria kuin alaraja", "ad": "on niiden numeeristen x-arvojen alue, joille on olemassa todennäköisyys!on niiden todennäköisyyksien alue, jonka arvot liittyvät x_alueen arvoihin. Arvot ovat välillä 0 - 1, p<PERSON><PERSON><PERSON> 0!on alaraja arvolle, jolle haluat todennäköisyyden!on valinnainen yläraja arvolle, jolle haluat todennäköisyyden. Jos arvoa ei määritetä, funktio palauttaa todennäköisyyden sille, x_alueen arvot ovat yhtä suuria kuin alarajan arvot"}, "QUARTILE": {"a": "(matriisi; pal_nelj<PERSON><PERSON>)", "d": "Palauttaa tietoalueen neljänneksen", "ad": "on lukumatriisi tai lukuja sisältävä solualue, jolle haluat laskea neljännesarvon!on luku: 0 = minimiarvo; 1 = 1. nelj<PERSON><PERSON>; 2 = mediaani; 3 = 3. nelj<PERSON>nnes; 4 = maksimiar<PERSON>"}, "QUARTILE.INC": {"a": "(matriisi; pal_nelj<PERSON><PERSON>)", "d": "Palauttaa tietoalueen neljänneksen perustuen prosenttiarvoihin välillä 0 - 1, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pois lukien", "ad": "on lukumatriisi tai lukuja sisältävä solualue, jolle haluat laskea neljännesarvon!on luku: 0 = minimiarvo; 1 = 1. nelj<PERSON><PERSON>; 2 = mediaani; 3 = 3. nelj<PERSON>nnes; 4 = maksimiar<PERSON>"}, "QUARTILE.EXC": {"a": "(matriisi; pal_nelj<PERSON><PERSON>)", "d": "Palauttaa tietoalueen neljänneksen perustuen prosenttiarvoihin välillä 0 - 1, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pois lukien", "ad": "on lukumatriisi tai lukuja sisältävä solualue, jolle haluat laskea neljännesarvon!on luku: 0 = minimiarvo; 1 = 1. nelj<PERSON><PERSON>; 2 = mediaani; 3 = 3. nelj<PERSON>nnes; 4 = maksimiar<PERSON>"}, "RANK": {"a": "(luku; viittaus; [järje<PERSON><PERSON>])", "d": "Palau<PERSON>a luvun sija<PERSON>in lukuarvoluettelossa. <PERSON><PERSON><PERSON> arvon su<PERSON><PERSON><PERSON> muihin luette<PERSON> lukuihin", "ad": "on luku, jonka järjestyksen suhteessa muihin haluat tietää!on lukuarvomatriisi tai viittaus lukuarvoluetteloon. Funktio ohittaa tässä argumentissa määritetyt arvot, jotka eivät ole lukuarvoja!on luku, joka määrittää funktion käyttämän lajittelujärjestyksen. Jos arvo on 0 tai puuttuu, luette<PERSON> lajitellaan laskevaan järjestykseen. Jo<PERSON> arvo poikkeaa nollasta, luettelo lajitellaan nousevaan järjestykseen"}, "RANK.AVG": {"a": "(luku; viittaus; [järje<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> luvun sijainnin luku<PERSON><PERSON><PERSON><PERSON><PERSON>, eli sen arvon su<PERSON><PERSON><PERSON> muihin luette<PERSON> lukuihin. <PERSON><PERSON> use<PERSON><PERSON><PERSON> kuin yhdellä arvolla on sama sijainti, funktio palauttaa keskimääräisen sijainnin", "ad": "on luku, jonka järjestyksen suhteessa muihin haluat tietää!on lukuarvomatriisi tai viittaus lukuarvoluetteloon. Funktio ohittaa tässä argumentissa määritetyt arvot, jotka eivät ole lukuarvoja!on luku, joka määrittää funktion käyttämän lajittelujärjestyksen. Jos arvo on 0 tai puuttuu, luette<PERSON> lajitellaan laskevaan järjestykseen. Jo<PERSON> arvo poikkeaa nollasta, luettelo lajitellaan nousevaan järjestykseen"}, "RANK.EQ": {"a": "(luku; viittaus; [järje<PERSON><PERSON>])", "d": "<PERSON><PERSON><PERSON><PERSON> luvun sijainnin luku<PERSON><PERSON><PERSON><PERSON>, eli sen arvon su<PERSON><PERSON><PERSON> muihin luette<PERSON> lukuihin. <PERSON><PERSON> use<PERSON><PERSON><PERSON> kuin yhdell<PERSON> arvolla on sama sijainti, funktio palauttaa arvojoukon arvojen korke<PERSON> sijainnin", "ad": "on luku, jonka järjestyksen suhteessa muihin haluat tietää!on lukuarvomatriisi tai viittaus lukuarvoluetteloon. Funktio ohittaa tässä argumentissa määritetyt arvot, jotka eivät ole lukuarvoja!on luku, joka määrittää funktion käyttämän lajittelujärjestyksen. Jos arvo on 0 tai puuttuu, luette<PERSON> lajitellaan laskevaan järjestykseen. Jo<PERSON> arvo poikkeaa nollasta, luettelo lajitellaan nousevaan järjestykseen"}, "RSQ": {"a": "(tunnettu_y; tunnettu_x)", "d": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>relaatiokerto<PERSON>n ne<PERSON>ö<PERSON>, joka on laskettu annettujen arvopisteiden pohjalta", "ad": "on matriisi tai arvopisteiden alue. Arvot voivat olla nimiä, matriiseja tai viittauksia lukuihin!on matriisi tai arvopisteiden alue. Arvot voivat olla nimiä, matriiseja tai viittauksia lukuihin"}, "SKEW": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa jakauman vinouden. <PERSON><PERSON>vo kuvaa jakauman keskittymistä keskiarvon ympärille", "ad": "ovat 1 - 255 lukua, <PERSON><PERSON><PERSON>, matri<PERSON>a tai vii<PERSON><PERSON><PERSON> luku<PERSON>, joista haluat laskea vinouden"}, "SKEW.P": {"a": "(luku1; [luku2]; ...)", "d": "Palauttaa jakauman vinouden populaatioon perustuen: arvo kuvaa jakauman keskittymistä keskiarvon ympärille", "ad": "ovat 1 - 254 lukua, <PERSON><PERSON><PERSON>, matri<PERSON>a tai vii<PERSON><PERSON><PERSON> luku<PERSON>, joista haluat laskea populaation vinouden"}, "SLOPE": {"a": "(tunnettu_y; tunnettu_x)", "d": "Palauttaa lineaarisen regressiosuoran kulmakertoimen, joka on laskettu annettujen arvopisteiden pohjalta", "ad": "on matriisi tai solualue, jossa on numeerisia riippuvia arvopisteitä. Arvot voivat olla nimiä, matriiseja tai viittauksia lukuihin!on joukko riippumattomia arvopisteitä. Arvot voivat olla nimiä, matriiseja tai viittauksia lukuihin"}, "SMALL": {"a": "(matriisi; k)", "d": "Palauttaa k:nneksi pienimmän arvon tie<PERSON><PERSON>ue<PERSON>. Esimerkiksi viidenneksi pienimmän arvon", "ad": "on numeerista tietoa sisältävä matriisi tai alue, josta haluat löytää k:nneksi pienimmän arvon!on palautettavan arvon sijainti (pienimmästä lähtien) matriisissa tai tietoalueella"}, "STANDARDIZE": {"a": "(x; keski<PERSON><PERSON>; keskihajon<PERSON>)", "d": "Palauttaa normitetun arvon kes<PERSON>von ja -hajonnan määrittämästä jakaumasta", "ad": "on arvo, jonka haluat normittaa!on jakauman aritmeettinen keskiarvo!on jakauman keskihajontaa osoittava positiivinen luku"}, "STDEV": {"a": "(luku1; [luku2]; ...)", "d": "Arvioi populaation keskihajonnan otoksen perusteella (funktio ei huomioi näytteessä olevia totuusarvoja tai tekstiä)", "ad": "ovat 1 - 255 lukua, jotka muodostavat näytteen populaatiosta. Arvot voivat olla lukuja tai viitta<PERSON><PERSON> lukuihin"}, "STDEV.P": {"a": "(luku1; [luku2]; ...)", "d": "Laskee populaation keskihajonnan koko argumentteina annetun populaation perusteella (funktio ei huomioi totuusarvoja tai tekstiä)", "ad": "ovat 1 - 255 lukua, jotka vastaavat populaatiota. Arvot voivat olla lukuja tai viitta<PERSON>sia lukuihin"}, "STDEV.S": {"a": "(luku1; [luku2]; ...)", "d": "Arvioi populaation keskihajonnan otoksen perusteella (funktio ei huomioi näytteessä olevia totuusarvoja tai tekstiä)", "ad": "ovat 1 - 255 lukua, jotka muodostavat näytteen populaatiosta. Arvot voivat olla lukuja tai viitta<PERSON><PERSON> lukuihin"}, "STDEVA": {"a": "(arvo1; [arvo2]; ...)", "d": "Arvioi keskipoikkeamaa näytteen pohjalta. Funktio huomioi myös totuusarvot ja tekstin. Teksti ja totuusarvo EPÄTOSI lasketaan arvona 0. Totuusarvo TOSI lasketaan arvona 1", "ad": "ovat 1 - 255 populaation näytettä. Argumentit voivat olla lukuja, nimi<PERSON> tai vii<PERSON><PERSON>sia lukuihin"}, "STDEVP": {"a": "(luku1; [luku2]; ...)", "d": "Laskee populaation keskihajonnan koko populaation perusteella (funktio ei huomioi totuusarvoja tai tekstiä)", "ad": "ovat 1 - 255 lukua, jotka vastaavat populaatiota. Arvot voivat olla lukuja tai viitta<PERSON>sia lukuihin"}, "STDEVPA": {"a": "(arvo1; [arvo2]; ...)", "d": "Laskee koko populaation keskipoikkeaman. Funktio ottaa myö<PERSON> huo<PERSON>on totuusarvot ja tekstin. Teksti ja totuusarvo EPÄTOSI lasketaan arvona 0. Totuusarvo TOSI lasketaan arvona 1", "ad": "ovat 1 - 255  populaatioon liittyvää arvoa. Argumentit voivat olla lukuja, ni<PERSON><PERSON>, matri<PERSON><PERSON> tai vii<PERSON><PERSON><PERSON> lukuihin"}, "STEYX": {"a": "(tunnettu_y; tunnettu_x)", "d": "<PERSON><PERSON><PERSON><PERSON> j<PERSON> x-ar<PERSON>a vast<PERSON> ennus<PERSON> y-arvon k<PERSON>n", "ad": "on riippuva matriisi tai arvopisteiden alue. Arvot voivat olla nimiä, matriiseja tai viittauksia lukuihin!on riippumaton matriisi tai arvopisteiden alue. Arvot voivat olla nimiä, matriiseja tai viittauksia lukuihin"}, "TDIST": {"a": "(x; vapausa<PERSON><PERSON>; suunta)", "d": "<PERSON><PERSON><PERSON><PERSON> t-jaka<PERSON>", "ad": "on numeerinen arvo, jossa haluat laskea jakauman!on kokonaisluku, joka määrittää jakaumaa kuvaavat vapausasteet!määrittää jakauman suuntien lukumäärän. Yksisuuntainen jakauma = 1; kaksisuuntainen jakauma = 2"}, "TINV": {"a": "(todennäköisyys; vapausasteet)", "d": "Palauttaa käänteisen t-jakauman", "ad": "on kaksisuuntaiseen t-jakauma<PERSON> liitetty todennäköisyys. Todennäköisyys on luku välillä 0 - 1 (1 mukaan lukien)!on jakaumaa kuvaava vapausasteiden määrä. <PERSON><PERSON><PERSON> t<PERSON> olla positiivinen kokonaisluku"}, "T.DIST": {"a": "(x; vapausa<PERSON>et; kertym<PERSON>)", "d": "Palauttaa vasenhäntäisen Studentin t-jakauman", "ad": "on numeerinen arvo, jossa haluat laskea jakauman!on kokonaisluku, joka määrittää jakaumaa kuvaavat vapausasteet!On looginen arvo. <PERSON><PERSON> arvo on TOSI, funktio palauttaa jakauman kertymäfunktion. <PERSON><PERSON> arvo on EPÄTOSI, funktio palauttaa todennäköisyystiheysfunktion"}, "T.DIST.2T": {"a": "(x; vapausasteet)", "d": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON><PERSON> t-jakauman", "ad": "on numeerinen arvo, jossa haluat laskea jakauman!on kokonaisluku, joka määrittää jakaumaa kuvaavat vapausasteet"}, "T.DIST.RT": {"a": "(x; vapausasteet)", "d": "Pa<PERSON>ttaa oikeahäntäisen Studentin t-jakauman", "ad": "on numeerinen arvo, jossa haluat laskea jakauman!on kokonaisluku, joka määrittää jakaumaa kuvaavat vapausasteet"}, "T.INV": {"a": "(todennäköisyys; vapausasteet)", "d": "Palauttaa käänteisen vasenhäntäisen t-jakauman", "ad": "on kaksisuuntaiseen t-jakauma<PERSON> liitetty todennäköisyys. Todennäköisyys on luku välillä 0 - 1 (1 mukaan lukien)!on jakaumaa kuvaava vapausasteiden määrä. <PERSON><PERSON><PERSON> t<PERSON> olla positiivinen kokonaisluku"}, "T.INV.2T": {"a": "(todennäköisyys; vapausasteet)", "d": "Palauttaa käänteisen kaksisuuntaisen t-jakauman", "ad": "on kaksisuuntaiseen t-jakauma<PERSON> liitetty todennäköisyys. Todennäköisyys on luku välillä 0 - 1 (1 mukaan lukien)!on jakaumaa kuvaava vapausasteiden määrä. <PERSON><PERSON><PERSON> t<PERSON> olla positiivinen kokonaisluku"}, "T.TEST": {"a": "(matriisi1; matriisi2; suunta; laji)", "d": "Palauttaa t-testiin liittyvän todennäköisyyden", "ad": "on ensimmäinen arvojoukko!on toinen arvojoukko!määrittää onko jakauma yksi, vai kaksis<PERSON>. Jos arvo = 1, jakauma on yks<PERSON>uuntainen. Jos arvo = 2, jakauma on kaksisuuntainen!on suoritettavan t-testin tyyppi. Jos arvo = 1, testi on parittainen. Jos arvo = 2, kahden näytteen varianssit ovat samat (homoskedastiset). Jos arvo = 3, kahden näytteen varianssit ovat erilaiset"}, "TREND": {"a": "(tunnettu_y; [tunnettu_x]; [uusi_x]; [vakio])", "d": "Palauttaa lineaarisen trendin numerot sovittamalla tunnetut tietopisteet ja käyttäen pienemmän neliön menetelmää", "ad": "on yhtälöstä y = mx + b jo tunnettujen y-arvojen alue tai matriisi!on valinnainen x-arvojen alue tai matriisi, jotka tunnetaan yhtälöstä y = mx + b. <PERSON><PERSON> kokoinen matriisi kuin tunnettu_y!on niiden uusien x-arvojen alue tai matriisi, joille haluat SUUNTAUS-funktion palauttavan vastaavat y-arvot!on totuusarvo. <PERSON><PERSON><PERSON> b lasketaan normaalisti, jos vakio = TOSI tai jos sitä ei ole. Vakion b arvo on 0, jos vakio = EPÄTOSI"}, "TRIMMEAN": {"a": "(matri<PERSON>; prosentti)", "d": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON>un keski<PERSON>", "ad": "on matriisi tai arvoalue, jonka haluat tasata ja jonka keskiarvon haluat sitten laskea!on murtoluku, joka määrittää, kuinka suuri osa arvopisteistä jätetään laskennan ulkopuolelle arvojoukon alusta ja lopusta"}, "TTEST": {"a": "(matriisi1; matriisi2; suunta; laji)", "d": "Palauttaa t-testiin liittyvän todennäköisyyden", "ad": "on ensimmäinen arvojoukko!on toinen arvojoukko!määrittää onko jakauma yksi, vai kaksis<PERSON>. Jos arvo = 1, jakauma on yksisuuntainen. Jos arvo = 2, jakauma on kaksisuuntainen!on suoritettavan t-testin tyyppi. Jos arvo = 1, testi on parittainen. Jos arvo = 2, kahden näytteen varianssit ovat samat. Jos arvo = 3, kahden näytteen varianssit ovat erilaiset"}, "VAR": {"a": "(luku1; [luku2]; ...)", "d": "Arvioi populaation varians<PERSON> o<PERSON>sen perusteella (jättää huomiotta otoksessa olevat totuusarvot ja tekstit)", "ad": "ovat 1 - 255 lukua, jotka vastaavat populaation otosta"}, "VAR.P": {"a": "(luku1; [luku2]; ...)", "d": "Laskee varianssin koko populaation perusteella (jättää huomiotta populaatiossa olevat totuusarvot ja tekstit)", "ad": "ovat 1 - 255 lukua, jotka muodostavat populaation"}, "VAR.S": {"a": "(luku1; [luku2]; ...)", "d": "Arvioi populaation varians<PERSON> o<PERSON>sen perusteella (jättää huomiotta otoksessa olevat totuusarvot ja tekstit)", "ad": "ovat 1 - 255 lukua, jotka vastaavat populaation otosta"}, "VARA": {"a": "(arvo1; [arvo2]; ...)", "d": "Arvioi varianssia näytteen pohjalta. Funktio ottaa myö<PERSON> huomioon  totuusarvot ja tekstin. Teksti ja totuusarvo EPÄTOSI lasketaan arvona 0. Totuusarvo TOSI lasketaan arvona 1", "ad": "ovat 1 - 255 populaation näytteeseen liittyvää argumenttia"}, "VARP": {"a": "(luku1; [luku2]; ...)", "d": "Laskee varianssin koko populaation perusteella (jättää huomiotta populaatiossa olevat totuusarvot ja tekstit)", "ad": "ovat 1 - 255 lukua, jotka muodostavat populaation"}, "VARPA": {"a": "(arvo1; [arvo2]; ...)", "d": "Laskee koko populaation varianssin. Funktio ottaa myös huo<PERSON>on totuusarvot ja tekstin. Teksti ja totuusarvo EPÄTOSI lasketaan arvona 0. Totuusarvo TOSI lasketaan arvona 1", "ad": "ovat 1 - 255 populaatioon liittyvää <PERSON>tia"}, "WEIBULL": {"a": "(x; alfa; beeta; kum<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "ad": "on ei-negatii<PERSON>en lukuarvo, jossa funktion arvo lasketaan!on jakauman parametri. Arvon täytyy olla positiivinen!on jakauman parametri. Arvon täytyy olla positiivinen!on totuusarvo. <PERSON><PERSON> arvo on TOSI, lasketaan jakauman kertymäfunktio. <PERSON><PERSON> arvo on EPÄTOSI, lasketaan todennäköisyysmassafunktio"}, "WEIBULL.DIST": {"a": "(x; alfa; beeta; kum<PERSON><PERSON><PERSON><PERSON>)", "d": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "ad": "on ei-negatii<PERSON>en lukuarvo, jossa funktion arvo lasketaan!on jakauman parametri. Arvon täytyy olla positiivinen!on jakauman parametri. Arvon täytyy olla positiivinen!on totuusarvo. <PERSON><PERSON> arvo on TOSI, lasketaan jakauman kertymäfunktio. <PERSON><PERSON> arvo on EPÄTOSI, lasketaan todennäköisyysmassafunktio"}, "Z.TEST": {"a": "(matriisi; x; [sigma])", "d": "Palauttaa yksisuuntaisen z-testin P-arvon", "ad": "on matriisi tai tietoalue jota vastaan arvoa x testataan!on testattava arvo!on populaation (tunnettu) keskihajonta. Jos arvoa ei määritetä, oh<PERSON><PERSON> käyttää näytteen keskihajontaa"}, "ZTEST": {"a": "(matriisi; x; [sigma])", "d": "Palauttaa yksisuuntaisen z-testin P-arvon", "ad": "on matriisi tai tietoalue jota vastaan arvoa x testataan!on testattava arvo!on populaation (tunnettu) keskihajonta. Jos arvoa ei määritetä, oh<PERSON><PERSON> käyttää näytteen keskihajontaa"}, "ACCRINT": {"a": "(asettamispvm; alkukorko; tilityspvm; korko; nimellisarvo; korkojakso; [peruste]; [laskentamenetelmä])", "d": "<PERSON><PERSON><PERSON><PERSON> kausitt<PERSON>ta korkoa maksavalle arvo<PERSON> kertyneen koron.", "ad": "on arvopaperin liikkeellelaskupäivämäärä järjestysnumerona!on arvopaperin ensimmäinen korkopäivämäärä järjestysnumerona!on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin vuosittainen korkokanta!on arvopaperin nimellisarvo!on koronmaksukausien lukumäärä vuodessa!on käytettävä päivien laskentaperuste!on totuusarvo: kertynyt korko asettamispäivämääränä = TOSI tai arvo puuttuu; laskeminen viimeisestä koronmaksukauden päivämäärästä = EPÄTOSI"}, "ACCRINTM": {"a": "(asettamispvm; tilityspvm; korko; nimellisarvo; [peruste])", "d": "Palauttaa eräpäivänä korkoa maksavalle arvo<PERSON> kertyneen koron.", "ad": "on arvopaperin liikkeellelaskupäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on arvopaperin vuosittainen korkokanta!on arvopaperin nimellisarvo!on käytettävä päivien laskentaperuste"}, "AMORDEGRC": {"a": "(kustannus; ostopäivämäär<PERSON>; ensimmäinen_kausi; loppuarvo; kausi; korko; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> kunkin til<PERSON>n valmiskorkoisen suoran poiston.", "ad": "on sijoituksen kustannus!on sijoituksen hankintapäivämäärä!on ensimmäisen kauden viimeisen päivän päivämäärä!on sijoituksen käyttöajan loppuarvo.!on kausi!on poiston korko!vuosiperuste: 0, kun vuodessa on 360 päivää, 1 todellista kohden, 3, kun vuodessa on 365 päivää."}, "AMORLINC": {"a": "(kustannus; ostopäivämäär<PERSON>; ensimmäinen_kausi; loppuarvo; kausi; korko; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> kunkin til<PERSON>n valmiskorkoisen suoran poiston.", "ad": "on sijoituksen kustannus!on sijoituksen hankintapäivämäärä!on ensimmäisen kauden viimeisen päivän päivämäärä!on sijoituksen käyttöajan loppuarvo.!on kausi!on poiston korko!vuosiperuste: 0, kun vuodessa on 360 päivää, 1 todellista kohden, 3, kun vuodessa on 365 päivää."}, "COUPDAYBS": {"a": "(tilityspvm; erääntymispvm; korkojakso; [peruste])", "d": "Palauttaa koronmaksupäivien lukumäärän korkokauden alusta tilityspäivämäärään asti.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on koronmaksukausien lukumäärä vuodessa!on käytettävä päivien laskentaperuste"}, "COUPDAYS": {"a": "(tilityspvm; erääntymispvm; korkojakso; [peruste])", "d": "Palau<PERSON>a koronmaksukauden päivien lukumäärän, joka sisältää tilityspäivän.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on koronmaksukausien lukumäärä vuodessa!on käytettävä päivien laskentaperuste"}, "COUPDAYSNC": {"a": "(tilityspvm; erääntymispvm; korkojakso; [peruste])", "d": "Palauttaa tilityspäivän ja se<PERSON>avan koronmaksupäivän välisen a<PERSON>jakson päivien lukumäärän.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on koronmaksukausien lukumäärä vuodessa!on käytettävä päivien laskentaperuste"}, "COUPNCD": {"a": "(tilityspvm; erääntymispvm; korkojakso; [peruste])", "d": "Palauttaa tilityspäivämäärää seuraavan koronmaksupäivän.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on koronmaksukausien lukumäärä vuodessa!on käytettävä päivien laskentaperuste"}, "COUPNUM": {"a": "(tilityspvm; erääntymispvm; korkojakso; [peruste])", "d": "Palauttaa maksettavien korkosuoritusten lukumäärän tilitys- ja erääntymispäivämäärän välillä.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on koronmaksukausien lukumäärä vuodessa!on käytettävä päivien laskentaperuste"}, "COUPPCD": {"a": "(tilityspvm; erääntymispvm; korkojakso; [peruste])", "d": "Palauttaa tilityspäivää edeltävän korkopäivän.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on koronmaksukausien lukumäärä vuodessa!on käytettävä päivien laskentaperuste"}, "CUMIPMT": {"a": "(korko; kaudet_yht; nykyarvo; ens_kausi; viim_kausi; laji)", "d": "<PERSON><PERSON><PERSON><PERSON> kahden kauden välillä maksetun kumulatiivisen koron.", "ad": "on kauden korkokanta!on maksuerien kokonaismäärä!on nykyarvo!on ensimmäinen laskentakausi!on viimeinen laskentakausi!on maksun a<PERSON>"}, "CUMPRINC": {"a": "(korko; kaudet_yht; nykyarvo; ens_kausi; viim_kausi; laji)", "d": "Palauttaa kumulatiivisen lyhennyksen kahden jakson v<PERSON>llä.", "ad": "on kauden korkokanta!on maksukausien kokonaismäärä!on nykyarvo!on ensimmäinen laskentakausi!on viimeinen laskentakausi!on maksun a<PERSON>"}, "DB": {"a": "(kustannus; loppuarvo; aika; kausi; [kuukausi])", "d": "<PERSON><PERSON><PERSON><PERSON> kauden kirjan<PERSON>ollisen poiston am<PERSON><PERSON><PERSON><PERSON>-men<PERSON>lm<PERSON>n (Fixed-declining balance) mukaan", "ad": "on sijoituksen alkuperäinen hankintahinta!on sijoituksen arvo käyttöajan lopussa (jäännösarvo)!on kausien määrä, joiden aikana sijoitus poistetaan (kuts<PERSON><PERSON> myös sijoituksen käyttöiäksi)!on kausi, jolta poisto halutaan laskea. Argumenteilla kausi ja aika on oltava sama yksikkö!on ensimmäisen vuoden kuukausien lukumäärä. Jos et määritä tätä argumenttia, funktio käyttää oletusarvoa 12"}, "DDB": {"a": "(kustannus; loppuarvo; aika; kausi; [kerroin])", "d": "<PERSON><PERSON><PERSON><PERSON> kauden kirjanpidollisen poiston amerikka<PERSON>sen DDB-menetemän (Double-Declining Balance) tai jonkun muun määrittämäsi menetelmän mukaan", "ad": "on sijoituksen alkuperäinen hankintahinta!on sijoituksen arvo käyttöajan lopussa (jäännösarvo)!on kausien määr<PERSON>, joiden aikana sijoitus poistetaan (kuts<PERSON><PERSON> my<PERSON><PERSON> sijoituksen käyttöiäksi)!on kausi, jolta poisto halutaan laskea. Argumenteilla kausi ja aika on oltava sama yksikkö!on poistonopeus. Jos et määritä argumenttia kerroin, funktio käyttää oletusarvoa 2 (Double-Declining Balance)"}, "DISC": {"a": "(tilityspvm; erääntymispvm; hinta; lunastushinta; [peruste])", "d": "Palauttaa arvopaperin diskonttokoron.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on arvopaperin hinta (100 euron nimellisarvo)!on arvopaperin lunastushinta (100 euron nimellisarvo)!on käytettävä päivien laskentaperuste"}, "DOLLARDE": {"a": "(valuutta_murtoluku; nimitt<PERSON>j<PERSON>)", "d": "<PERSON><PERSON><PERSON> murtolukuna esitetyn luvun kymmenjärjestelmän luvuksi.", "ad": "on murtolukuna esitetty luku!on kokonaisluku, jota käytetään muunnoks<PERSON>a ni<PERSON>ä"}, "DOLLARFR": {"a": "(valuutta_des; nimitt<PERSON>j<PERSON>)", "d": "<PERSON><PERSON><PERSON> kym<PERSON>rjestelmän luvun murtoluvuksi.", "ad": "on desimaaliluku!on kokonaisluku, jota käytetään muunnoksessa nimittäjänä"}, "DURATION": {"a": "(tilityspvm; erääntymispvm; korko; tuotto; korko<PERSON><PERSON>; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> kausitt<PERSON>ta korkoa maksavan arvo<PERSON>in keston vuosina.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on arvopaperin vuosittainen korkokanta!on arvopaperin vuotuinen tuotto!on koronmaksukausien lukumäärä vuodessa!on käytettävä päivien laskentaperuste"}, "EFFECT": {"a": "(nimelliskorko; korkojaksot)", "d": "Palauttaa efektiivisen vuosikorkokannan.", "ad": "on nimelliskorkokanta!on korkojaksojen lukumäärä vuodessa"}, "FV": {"a": "(korko; kaudet_yht; erä; [nykyarvo]; [laji])", "d": "Palauttaa tasavälisiin vakiomaksueriin ja kiinteään korkoon perustuvan lainan tai sijoituksen tulevan arvon", "ad": "on kauden korkokanta. Käytä esimerkiksi neljännesvuosittaisina maksukausina arvoa 6 %/4, kun vuosik<PERSON><PERSON> on 6 %!on sijoituksen maksukausien kokonaismäärä!on kullakin kaudella maksettava maksuerä. <PERSON><PERSON><PERSON> on vakio sijoituksen aikana!on nykyarvo eli tulevien maksujen yhteisarvo tällä hetkellä. Jos arvo jätetään pois, nykyarvo = 0!on luku 0 tai 1, joka oso<PERSON>aa, milloin maksuerät erääntyvät. Jos maksuerä erääntyy kauden alussa, arvo = 1. <PERSON><PERSON> arvo on nolla tai puuttuu, maksuer<PERSON> erääntyy kauden lopussa"}, "FVSCHEDULE": {"a": "(nykyarvo; korot)", "d": "Pa<PERSON><PERSON><PERSON> tulevan arvon, joka on saatu käyttämällä erilaisia korkokantoja.", "ad": "on nykyarvo!on käytettävien korkokantojen matriisi"}, "INTRATE": {"a": "(tilityspvm; erääntymispvm; sijoitus; lunastushinta; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON>.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on arvopaperiin sijoitettu rahamäärä!on erääntymispäivänä saatava rahamäärä!on käytettävä päivien laskentaperuste"}, "IPMT": {"a": "(korko; kausi; kaudet_yht; nykyarvo; [ta]; [laji])", "d": "Palauttaa sijoitukselle tiettynä ajan<PERSON>a k<PERSON>vän koron, joka pohjautuu säännöllisiin vakioeriin ja kiinteään korkoprosenttiin", "ad": "on kauden korkokanta. Käytä esimerkiksi neljännesvuosittaisina maksukausina arvoa 6 %/4, kun vuosik<PERSON><PERSON> on 6 %!on kausi, jolta haluat tietää koron. <PERSON><PERSON><PERSON><PERSON> arvon on oltava välillä 1 - kaudet_yht!on sijoituksen maksukausien kokonaismäärä!on nykyarvo eli tulevien maksujen yhteisarvo tällä hetkellä!on tuleva arvo eli arvo, jonka haluat saavuttaa, kun viimeinen maksuerä on hoidettu. Jos argumenttia ei määritetä, ta = 0!on totuusarvo, joka osoittaa, milloin maksuerät erääntyvät. Jos arvo on = 0 tai jätetty pois, maksut erääntyvät kauden lopussa. Jos arvo = 1, maksut erääntyvät kauden alussa"}, "IRR": {"a": "(arvot; [arvaus])", "d": "Palauttaa sisäisen korkokannan toistuvista kassavirroista muodostuvalle sar<PERSON>", "ad": "on matriisi tai soluvi<PERSON>, jossa oleville luvuille funktio laskee sisäisen korkokannan!on luku, jonka arvaat olevan lähellä funktion SISÄINEN.KORKO antamaa tulosta. Jos argumenttia ei anneta, arvo on 0,1 (10 prosenttia)"}, "ISPMT": {"a": "(korko; kausi; kaudet_yht; nykyar<PERSON>)", "d": "Palauttaa tietyn sijoituskauden lainanmaksukoron", "ad": "kauden korko. Käytä esimerkiksi neljännesvuosittaisina maksukausina arvoa 6 %/4, kun vuosikorko on 6 %!<PERSON><PERSON><PERSON>, jolle etsit korkoa!Maksukausien kokonaismäärä!Summa, joka vastaa maksettavien erien arvoa nyt"}, "MDURATION": {"a": "(tilityspvm; erääntymispvm; korko; tuotto; korko<PERSON><PERSON>; [peruste])", "d": "Palauttaa muunnetun Macauleyn keston arvopaperille (100 euron nimellisarvo).", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on arvopaperin vuosittainen korkokanta!on arvopaperin vuotuinen tuotto!on koronmaksukausien lukumäärä vuodessa!on käytettävä päivien laskentaperuste"}, "MIRR": {"a": "(arvot; pääoma_korko; uudinvest_korko)", "d": "Palauttaa sisäisen korkokannan sarjalle jat<PERSON><PERSON> ka<PERSON>, j<PERSON><PERSON> huo<PERSON> my<PERSON> sijo<PERSON> arvo ja uudelleen sijoittamisen korko", "ad": "on matriisi tai viittaus soluihin, jotka sisältävät tasaisin väliajoin toistuvat maksut (negatiiviset) ja tulot (positiiviset)!on kassavirroissa käytettävästä rahasta maksettava korko!on uudelleen sijoitetuista kassavirroista saatava korko"}, "NOMINAL": {"a": "(tod_korko; korkojaksot)", "d": "Palauttaa vuosittaisen nimellisk<PERSON>n.", "ad": "on efektiivinen korkokanta!on korkojaksojen lukumäärä vuodessa"}, "NPER": {"a": "(korko; erä; nykyarvo; [ta]; [laji])", "d": "<PERSON><PERSON><PERSON><PERSON> kausien mä<PERSON><PERSON><PERSON><PERSON>, joka perustuu ta<PERSON>, kii<PERSON><PERSON><PERSON> maksuihin ja kiinte<PERSON>än kor<PERSON>p<PERSON>in", "ad": "on kauden korkokanta. Käytä esimerkiksi neljännesvuosittaisina maksukausina arvoa 6 %/4, kun vuo<PERSON><PERSON><PERSON> on 6 %!on kunkin kauden maksuerä. Maks<PERSON><PERSON>n suuruus on kiinteä!on nykyarvo eli tulevien maksujen yhteisarvo tällä hetkellä!on tuleva arvo eli arvo, jonka haluat saavuttaa, kun viimeinen maksuerä on hoidettu. Jos arvoa ei määritetä, käytetään arvoa 0!on luku 0 tai 1, joka osoittaa, milloin maksuerät erääntyvät. Jos maksut erääntyvät kauden alussa, arvo = 1. Jos maksut erääntyvät kauden lopussa, arvo = 0 tai jätetty pois"}, "NPV": {"a": "(korko; arvo1; [arvo2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, joka perustuu toistuvista kassavirroista muodos<PERSON><PERSON> sar<PERSON> (ma<PERSON><PERSON><PERSON> ja tulo<PERSON>)  ja kor<PERSON>", "ad": "on diskonttokorko yhden kauden ajalta!ovat 1 - 254 maksua tai tuloa, jotka jakaantuvat tasaisesti ja sijoittuvat aina kauden loppuun"}, "ODDFPRICE": {"a": "(tilityspvm; erääntymispvm; asettamispvm; ens_korko; korko; tuotto; lunastushinta; korkojak<PERSON>; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON> (100 euron nimellisarvo) <PERSON><PERSON><PERSON><PERSON>, j<PERSON> en<PERSON><PERSON><PERSON><PERSON> kaus<PERSON> on normaalista poikkeava.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on arvopaperin liikkeellelaskupäivämäärä järjestysnumerona!on arvopaperin ensimmäisen korkopäivän järjestysnumero!on arvopaperin korkokanta!on arvopaperin vuotuinen tuotto!on arvopaperin lunastushinta (100 euron nimellisarvo)!on koronmaksukausien lukumäärä vuodessa!on käytettävä päivien laskentaperuste"}, "ODDFYIELD": {"a": "(tilityspvm; erääntymispvm; asettamispvm; ens_korko; korko; hinta; lunastushinta; korkojakso; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON> tuoton <PERSON>, jossa en<PERSON><PERSON><PERSON><PERSON> kaus<PERSON> on normaalista poikkeava.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on arvopaperin liikkeellelaskupäivämäärä järjestysnumerona!on arvopaperin ensimmäisen korkopäivän järjestysnumero!on arvopaperin korkokanta!on arvopaperin hinta!on arvopaperin lunastushinta (100 euron nimellisarvo)!on koronmaksukausien lukumäärä vuodessa!on käytettävä päivien laskentaperuste"}, "ODDLPRICE": {"a": "(tilityspvm; erääntymispvm; viim_korko; korko; tuotto; lunast<PERSON><PERSON>a; kor<PERSON><PERSON><PERSON>; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> arvo<PERSON><PERSON> hi<PERSON> (100 euron nimellisarvo) <PERSON><PERSON><PERSON><PERSON>, jossa vii<PERSON><PERSON> kausi on normaalista poikkeava.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on arvopaperin viimeinen korkopäivä järjestysnumerona!on arvopaperin korkokanta!on arvopaperin vuotuinen tuotto!on arvopaperin lunastushinta (100 euron nimellisarvo)!on koronmaksukausien lukumäärä vuodessa!on käytettävä päivien laskentaperuste"}, "ODDLYIELD": {"a": "(tilityspvm; erääntymispvm; viim_korko; korko; hinta; lunastushinta; korkojakso; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> ar<PERSON><PERSON><PERSON> tuoton til<PERSON>, jossa vii<PERSON><PERSON> ka<PERSON> on normaalista poikkeava.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on arvopaperin viimeinen korkopäivä järjestysnumerona!on arvopaperin korkokanta!on arvopaperin hinta!on arvopaperin lunastushinta (100 euron nimellisarvo)!on koronmaksukausien lukumäärä vuodessa!on käytettävä päivien laskentaperuste"}, "PDURATION": {"a": "(korko; nykyarvo; tuleva_arvo)", "d": "<PERSON><PERSON><PERSON>a sijoituk<PERSON>sa tarvittavien jaksojen mä<PERSON><PERSON><PERSON>, jotta määritetty arvo <PERSON> saavuttaa", "ad": "on kauden korkokanta.!on sijoituksen nykyarvo!on haluttu sijoituksen tuleva arvo"}, "PMT": {"a": "(korko; kaudet_yht; nykyarvo; [ta]; [laji])", "d": "Palau<PERSON><PERSON> lainan kausittaisen maksun. <PERSON>na perustuu tasa<PERSON>in ja kiinte<PERSON>än korkoon", "ad": "on lainakauden korkokanta. Käytä esimerkiksi neljännesvuosittaisina maksukausina arvoa 6 %/4, kun vuo<PERSON><PERSON><PERSON> on 6 %!on lainan maksuerien kokonaismäärä!on nykyarvo eli tulevien maksujen yhteisarvo tällä hetkellä!on tuleva arvo eli arvo, jonka haluat saavuttaa, kun viimeinen maksuerä on hoidettu. Jos arvoa ei määritetä, ohjelma käyttää arvoa 0 (nolla)!on luku 0 tai 1, joka osoittaa, milloin maksuerät erääntyvät. Jos maksut erääntyvät kauden alussa, arvo = 1. Jos maksut erääntyvät kauden lopussa, arvo = 0 tai jätetty pois"}, "PPMT": {"a": "(korko; kausi; kaudet_yht; nykyarvo; [ta]; [laji])", "d": "Palauttaa pääoman lyhennyksen annetulla kaude<PERSON>, kun käytetään tasaeriä ja kiinteää korkoa", "ad": "on kauden korkokanta. Käytä esimerkiksi neljännesvuosittaisina maksukausina arvoa 6 %/4, kun vuosik<PERSON><PERSON> on 6 %!määrittää maksukauden, jonka on oltava välillä 1 - kaudet_yht!on sijoituksen maksukausien kokonaismäärä!on nykyarvo eli tulevien maksujen yhteisarvo tällä hetkellä!on tuleva arvo eli arvo, jonka haluat saavuttaa, kun viimeinen maksuerä on hoidettu!on luku 0 tai 1, joka osoittaa, milloin maksuerät erääntyvät. Jos maksut erääntyvät kauden alussa, arvo = 1. Jos maksut erääntyvät kauden lopussa, arvo = 0 tai jätetty pois"}, "PRICE": {"a": "(tilityspvm; erä<PERSON>ntymispvm; korko; tuotto; luna<PERSON><PERSON><PERSON>a; kor<PERSON><PERSON><PERSON>; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> kausitt<PERSON>ta korkoa maksavan a<PERSON>vo<PERSON> hinnan (100 euron nimellisarvo)", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on arvopaperin vuosittainen korkokanta!on arvopaperin vuotuinen tuotto!on arvopaperin lunastushinta (100 euron nimellisarvo)!on koronmaksukausien lukumäärä vuodessa!on käytettävä päivien laskentaperuste"}, "PRICEDISC": {"a": "(tilityspvm; erääntymispvm; diskonttokorko; lunast<PERSON>inta; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> arvo<PERSON><PERSON> hinnan (100 euron nimellisarvo)", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on arvopaperin diskonttokorko!on arvopaperin lunastushinta (100 euron nimellisarvo)!on käytettävä päivien laskentaperuste"}, "PRICEMAT": {"a": "(tilityspvm; erääntymispvm; asettamispvm; korko; tuotto; [peruste])", "d": "Palauttaa erääntymispäivänä korkoa maksavan arvo<PERSON> hinnan (100 euron nimellisarvo)", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on arvopaperin liikkeellelaskupäivämäärä järjestysnumerona!on arvopaperin korkokanta liikkeellelaskupäivänä!on arvopaperin vuotuinen tuotto!on käytettävä päivien laskentaperuste"}, "PV": {"a": "(korko; kaudet_yht; erä; [ta]; [laji])", "d": "<PERSON><PERSON><PERSON><PERSON>", "ad": "on kauden korkokanta. Käytä esimerkiksi neljännesvuosittaisina maksukausina arvoa 6 %/4, kun vuo<PERSON><PERSON><PERSON> on 6 %!on sijoituksen maksukausien kokonaismäärä!on kunkin kauden maksuerä, joka ei voi muuttua sijoituskauden aikana!on tuleva arvo eli arvo, jonka haluat saavuttaa, kun viimeinen maksuerä on hoidettu!on luku 0 tai 1, joka osoittaa, milloin maksuerät erääntyvät. Jos maksut erääntyvät kauden alussa, arvo = 1. Jo<PERSON> maksut erääntyvät kauden lopussa, arvo = 0 tai jätetty pois"}, "RATE": {"a": "(kaudet_yht; erä; nykyarvo; [ta]; [laji]; [arvaus])", "d": "Palauttaa sijoituksen tai lainan kausittaisen korkokannan. Käytä esimerkiksi neljännesvuosittaisina maksukausina arvoa 6 %/4, kun vuosik<PERSON><PERSON> on 6 %", "ad": "on sijoituksen tai lainan maksukausien kokonaismäärä!on kunkin kauden maksuerä, joka ei voi muuttua sijoitus- tai lainakauden aikana!on nykyarvo eli tulevien maksujen yhteisarvo tällä hetkellä!on tuleva arvo eli arvo, jonka haluat saavuttaa, kun viimeinen maksuerä on hoidettu. Jos arvoa ei määritetä, ta = 0!on luku 0 tai 1, joka osoittaa, milloin maksuerät erääntyvät. Jos maksut erääntyvät kauden alussa, arvo = 1. Jos maksut erääntyvät kauden lopussa, arvo = 0 tai jätetty pois!on arvioimasi korkokanta. Jos arvoa ei määritetä, ohjelma k<PERSON>yttää arvoa 0,1 (10 prosenttia)"}, "RECEIVED": {"a": "(tilityspvm; erääntymispvm; sijoitus; disk<PERSON><PERSON><PERSON><PERSON>; [peruste])", "d": "Palauttaa arvopaperista erääntymispäivänä saatavan rahasumman.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on arvopaperiin sijoitettu rahamäärä!on arvopaperin diskonttokorko!on käytettävä päivien laskentaperuste"}, "RRI": {"a": "(nper; pv; fv)", "d": "<PERSON><PERSON><PERSON><PERSON> si<PERSON> kasvun vast<PERSON> k<PERSON>", "ad": "on sijoituksen kausien määrä!on sijoituksen nykyarvo!on sijoituksen tuleva arvo"}, "SLN": {"a": "(kustannus; loppuarvo; aika)", "d": "<PERSON><PERSON><PERSON><PERSON> si<PERSON><PERSON> tasapoiston yhdeltä kaudelta", "ad": "on sijoituksen alkuperäinen hankintahinta!on sijoituksen arvo käyttöajan lopussa (jäännösarvo)!on kausien määrä, joiden aikana sijoitus pois<PERSON> (kutsutaan myös sijoituksen käyttöiäksi)"}, "SYD": {"a": "(kustannus; loppuarvo; aika; kausi)", "d": "<PERSON><PERSON><PERSON><PERSON> si<PERSON><PERSON> vuosipoiston annettuna kautena käyttäen amerikkalaista SYD-menetelmää (Sum-of-Year's Digits)", "ad": "on sijoituksen alkuperäinen hankintahinta!on sijoituksen arvo käyttöajan lopussa (jäännösarvo)!on kausien määrä, joiden aikana sijoitus poiste<PERSON> (kuts<PERSON><PERSON> myös sijoituksen käyttöiäksi)!on laskentakausi, jonka yks<PERSON> on oltava sama kuin argumentin aikayksikön"}, "TBILLEQ": {"a": "(tilityspvm; erääntymispvm; diskonttokorko)", "d": "Palauttaa obligaation tuoton.", "ad": "on obligaation tilityspäivämäärä järjestysnumerona!on obligaation erääntymispäivämäärä järjestysnumerona!on obligaation diskonttokorko"}, "TBILLPRICE": {"a": "(tilityspvm; erääntymispvm; diskonttokorko)", "d": "Palauttaa obligaation hinnan (100 euron nimellisarvo)", "ad": "on obligaation tilityspäivämäärä järjestysnumerona!on obligaation erääntymispäivämäärä järjestysnumerona!on obligaation diskonttokorko"}, "TBILLYIELD": {"a": "(tilityspvm; erääntymispvm; hinta)", "d": "Palauttaa obligaation tuoton.", "ad": "on obligaation tilityspäivämäärä järjestysnumerona!on obligaation erääntymispäivämäärä järjestysnumerona!on obligaation hinta (100 euron nimellisarvo)"}, "VDB": {"a": "(kustannus; loppuarvo; aika; ens_kausi; viim_kausi; [kerroin]; [ei_siirtoa])", "d": "<PERSON><PERSON><PERSON><PERSON> sijoit<PERSON>sen kaksinkertaisen kirjanpidon tai muun määritetyn menetelmän mukaisen poiston millä hyvänsä annet<PERSON>a kaudella, muka<PERSON><PERSON><PERSON> osittaiset kaudet", "ad": "on sijoituksen alkuperäinen hankintahinta!on jäännösarvo sijoituksen käyttöiän lopussa!on kausien määrä, joiden aikana sijoitus poistetaan (kuts<PERSON><PERSON> my<PERSON>s sijoituksen käyttöiäksi)!on ensimm<PERSON><PERSON> kausi, jolle haluat laskea poiston. Aikayksikön täytyy olla sama kuin argumentissa Aika!on viimeinen kausi, jolle haluat laskea poiston. Aikayksikön täytyy olla sama kuin argumentissa Aika!on poiston laskennassa käytettävä kerroin. Jos arvoa ei määritetä, ohjelma käyttää arvoa 2 (kaksinkert<PERSON><PERSON> kirjanpidon saldo)!Jos arvo puuttuu tai on EPÄTOSI, ohjelma siirtyy käyttämään tasapoistoa, kun poisto on suurempi kuin DDB-poistotavalla laskettu poisto. Jos argumentin arvo on TOSI, ohjelma ei siirry käyttämään tasapoistoa"}, "XIRR": {"a": "(arvot; päivät; [arvaus])", "d": "Pa<PERSON>ttaa rahasuoritusten sarjan si<PERSON>n kor<PERSON>kan<PERSON>.", "ad": "on sarja rahasuorituksia, jotka tapahtuvat argumentin päivät määrittäminä päivinä!on sarja maksupäiviä, joina argumentin arvot maksusuoritukset tapahtuvat!on arvo, jonka arvaat olevan lähellä funktion SISÄINEN.KORKO.JAKSOTON laskemaa sisäistä korkokantaa"}, "XNPV": {"a": "(korko; arvot; päivät)", "d": "<PERSON><PERSON><PERSON><PERSON> maksus<PERSON><PERSON><PERSON>en sar<PERSON>.", "ad": "on diskonttokorko, jota käytetään rahasuoritusten diskonttaamiseen!on sarja rahasuorituksia, jotka tapahtuvat argumentin päivät määrittäminä päivinä!on sarja maksupäiviä, joina argumentin arvot maksusuoritukset tapahtuvat"}, "YIELD": {"a": "(tilityspvm; erääntymispvm; korko; hinta; lunastushinta; korkojakso; [peruste])", "d": "<PERSON><PERSON><PERSON><PERSON> jaksott<PERSON>ta korkoa maksavan arvo<PERSON>in tuoton.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on arvopaperin vuosittainen korkokanta!on arvopaperin hinta (100 euron nimellisarvo)!on arvopaperin lunastushinta (100 euron nimellisarvo)!on koronmaksukausien lukumäärä vuodessa!on käytettävä päivien laskentaperuste"}, "YIELDDISC": {"a": "(tilityspvm; erääntymispvm; hinta; lunastushinta; [peruste])", "d": "Palauttaa vuosittaisen tuoton diskontatulle arvo<PERSON>ille.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on arvopaperin hinta (100 euron nimellisarvo)!on arvopaperin lunastushinta (100 euron nimellisarvo)!on käytettävä päivien laskentaperuste"}, "YIELDMAT": {"a": "(tilityspvm; erääntymispvm; asettamispvm; korko; hinta; [peruste])", "d": "<PERSON><PERSON><PERSON>a vuosittaisen tuo<PERSON><PERSON>, joka maksaa korkoa erääntymispäivänä.", "ad": "on arvopaperin tilityspäivämäärä järjestysnumerona!on arvopaperin erääntymispäivämäärä järjestysnumerona!on arvopaperin liikkeellelaskupäivämäärä järjestysnumerona!on arvopaperin korkokanta liikkeellelaskupäivänä!on arvopaperin hinta (100 euron nimellisarvo)!on käytettävä päivien laskentaperuste"}, "ABS": {"a": "(luku)", "d": "Palauttaa luvun itseisarvon eli luvun ilman etumerkkiä.", "ad": "on re<PERSON><PERSON><PERSON>, jonka itsei<PERSON>von haluat laskea."}, "ACOS": {"a": "(luku)", "d": "Palauttaa luvun arcuskosinin radiaaneina väliltä 0 - pii. <PERSON><PERSON><PERSON><PERSON> on kulma, jonka kosini on luku", "ad": "on las<PERSON><PERSON><PERSON> kulman kosini ja sen arvon on oltava välillä -1 - 1"}, "ACOSH": {"a": "(luku)", "d": "Palauttaa luvun hyperbolisen kosinin käänteisfunktion arvon", "ad": "on mik<PERSON> ta<PERSON> re<PERSON>, joka on suurempi tai yhtä suuri kuin 1"}, "ACOT": {"a": "(luku)", "d": "Palauttaa luvun arkuskotangentin radiaaneina 0 - pii.", "ad": "on ha<PERSON><PERSON><PERSON> kulman kotangentti"}, "ACOTH": {"a": "(luku)", "d": "Palauttaa luvun käänteisen hyperbolisen kotangentin", "ad": "on haluamasi hyperbolisen kotangentin kulma"}, "AGGREGATE": {"a": "(funktion_nro; asetukset; viittaus1; ...)", "d": "Palauttaa koosteen luettelona tai tietok<PERSON>ana", "ad": "on luku 1–19, joka määrittää koosteen yhteenvetofunktion.!on luku 0–7, joka määrittää arvot, jotka ohitetaan matriisissa!on matriisi tai numeeristen tietojen alue, josta kooste lasketaan!ilmaisee sijainnin matriisissa; se on k. suuri<PERSON>, k. <PERSON>, k. prosenttipiste tai k. nelj<PERSON>.!on luku 1–19, joka määrittää koosteen yhteenvetofunktion.!on luku 0–7, joka mä<PERSON>rittää arvot, jotka ohitetaan matriisissa!ovat 1–253 aluetta tai viittausta, joista haluat koosteen"}, "ARABIC": {"a": "(teks<PERSON>)", "d": "<PERSON><PERSON><PERSON>alaiset numerot arabialaisiksi numeroiksi", "ad": "on muunnettava roomalainen numero"}, "ASC": {"a": "(teks<PERSON>)", "d": "Funktio muuttaa DBCS (Double-byte character set) -merkit SBCS (Single-byte character set) -merkist<PERSON>n merkeiksi DBCS-merkistöä edellyttäviä kieliä käytettäessä", "ad": "Muutettava teksti tai viittaus muutettavan tekstin sisältävään soluun."}, "ASIN": {"a": "(luku)", "d": "Palauttaa luvun arcussinin radiaaneina välillä -pii/2 - pii/2", "ad": "on halutun kulman sini, ja sen arvo on välillä -1 - 1"}, "ASINH": {"a": "(luku)", "d": "Palauttaa luvun hyperbolisen sinin käänteisfunktion arvon", "ad": "on mik<PERSON> ta<PERSON> re<PERSON>, joka on suurempi tai yhtä suuri kuin 1"}, "ATAN": {"a": "(luku)", "d": "Palauttaa luvun arcustangentin radiaaneina. <PERSON><PERSON> on välillä -pii/2 - pii/2", "ad": "on halutun kulman tangentti"}, "ATAN2": {"a": "(x_luku; y_luku)", "d": "<PERSON><PERSON><PERSON><PERSON> pisteen (x,y) arcustangentin radiaaneina. <PERSON><PERSON> on välillä -pii - pii, pois<PERSON>ien -pii", "ad": "on pisteen x-koordinaatti!on pisteen y-koordinaatti"}, "ATANH": {"a": "(luku)", "d": "Palauttaa luvun hyperbolisen tangentin käänteisfunktion arvon", "ad": "on mik<PERSON> ta<PERSON><PERSON>, joka on suurempi kuin -1 tai pienempi kuin 1"}, "BASE": {"a": "(luku; kantaluku; [vähimmäispituus])", "d": "<PERSON><PERSON><PERSON> luvun teksti<PERSON><PERSON><PERSON> annetulla kantal<PERSON> (kanta)", "ad": "on luku, jonka haluat muuntaa!on kantal<PERSON>, joksi haluat muuntaa luvun!on palautettavan merkkijonon vähimmäispituus.  <PERSON><PERSON> pois jätettyjä etunollia ei ole lis<PERSON>ty"}, "CEILING": {"a": "(luku; tarkkuus)", "d": "Pyöristää luvun ylöspäin lähimpään tarkkuuden kerrannaiseen", "ad": "on pyöristettävä luku!on kerrannainen, johon haluat pyöristää luvun"}, "CEILING.MATH": {"a": "(luku; [tarkkuus]; [tila])", "d": "Pyöristää luvun ylöspäin seuraavaan kokonaislukuun tai seuraavaan tarkkuuden <PERSON>taan", "ad": "on pyöristettävä arvo!on <PERSON><PERSON>, johon luku halutaan pyöristää!kun se on annettu ja poikkeaa nollasta, funktio pyöristää luvun nollasta poispäin"}, "CEILING.PRECISE": {"a": "(luku; [tarkkuus])", "d": "Palauttaa luvun pyöristettynä lähimpään kokonaislukuun tai tarkkuuden kerrannaiseen", "ad": "on pyöristettävä luku!on kerrannainen, johon haluat pyöristää luvun"}, "COMBIN": {"a": "(luku; valittu_luku)", "d": "<PERSON><PERSON><PERSON><PERSON> annettujen objektien kombinaatioiden määrän", "ad": "on objektien määrä!on objektien määrä jokaisessa yhdistelmässä"}, "COMBINA": {"a": "(luku; valittu_luku)", "d": "Palauttaa yhdistelmien määrän toistoineen tietylle määrälle kohteita", "ad": "on kohteiden kokonaismäärä!on kohteiden määrä jokaisessa yhdistelmässä"}, "COS": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> annetun kulman kosinin", "ad": "on radia<PERSON><PERSON> an<PERSON>u k<PERSON>, jonka kosini halutaan laskea"}, "COSH": {"a": "(luku)", "d": "Palauttaa luvun hyperbolisen kosinin", "ad": "on mik<PERSON> ta<PERSON> re<PERSON>"}, "COT": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> kulman kotangentin", "ad": "on radia<PERSON><PERSON> kulma, jolle haluat laskea kotangentin"}, "COTH": {"a": "(luku)", "d": "Palauttaa luvun hyperbolisen kotangentin", "ad": "on radia<PERSON><PERSON> kulma, jolle haluat laskea hyperbolisen kotangentin"}, "CSC": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> kulman k<PERSON>", "ad": "on r<PERSON><PERSON><PERSON> kulma, jolle haluat laskea kosekantin"}, "CSCH": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> kulman hyperbolisen kose<PERSON>tin", "ad": "on radia<PERSON><PERSON> kulma, jolle haluat laskea hyperbolisen kosekantin"}, "DECIMAL": {"a": "(luku; kantaluku)", "d": "<PERSON><PERSON><PERSON> annetun kannan luvun tekstimuodon desima<PERSON>luvuksi", "ad": "on luku, jonka haluat muuntaa!on muun<PERSON><PERSON> luvun kantaluku"}, "DEGREES": {"a": "(kulma)", "d": "<PERSON><PERSON><PERSON> r<PERSON><PERSON>", "ad": "on muunnettava kulma radiaaneina"}, "ECMA.CEILING": {"a": "(luku; tarkkuus)", "d": "Pyöristää luvun ylöspäin lähimpään tarkkuuden kerrannaiseen", "ad": "on pyöristettävä luku!on kerrannainen, johon haluat pyöristää luvun"}, "EVEN": {"a": "(luku)", "d": "Pyöristää positiivisen luvun ylöspäin ja negatiivisen luvun alaspäin lähimpään parilliseen kokonaislukuun", "ad": "on pyöristettävä arvo"}, "EXP": {"a": "(luku)", "d": "Palauttaa e:n korotettuna annettuun potenssiin", "ad": "on e:n eksponentti. Kantaluvun e arvo on 2.71828182845904, mik<PERSON> on luonnollisen logaritmin kantaluku"}, "FACT": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> luvun k<PERSON>, eli 1*2*3*...*luku", "ad": "on positiivinen luku, josta haluat laskea kertoman"}, "FACTDOUBLE": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> luvun o<PERSON>.", "ad": "on arvo, jolle haluat laskea osa<PERSON>an"}, "FLOOR": {"a": "(luku; tarkkuus)", "d": "Pyöristää luvun alaspäin lähimpään tarkkuuden kerrannaiseen", "ad": "on numeerinen arvo, jonka haluat pyöristää!on kerrannainen, johon haluat pyöristää luvun. Luvun ja tarkkuuden tulee olla molempien joko positiivisia tai negatiivisia"}, "FLOOR.PRECISE": {"a": "(luku; [tarkkuus])", "d": "Palauttaa luvun pyöristettynä alaspäin lähimpään kokonaislukuun tai tarkkuuden kerrannaiseen", "ad": "on pyöristettävä luku!on kerrannainen, johon haluat pyöristää luvun"}, "FLOOR.MATH": {"a": "(luku; [tarkkuus]; [tila])", "d": "Pyöristää luvun alaspäin seuraavaan kokonaislukuun tai seuraavaan tarkkuuden monikertaan", "ad": "on pyöristettävä arvo!on moniker<PERSON>, johon luku halutaan pyöristää!kun se on annettu ja poikkeaa nollasta, funktio pyöristää luvun nollaa kohti"}, "GCD": {"a": "(luku1; [luku2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> suurimman y<PERSON><PERSON><PERSON> j<PERSON>", "ad": "ovat 1 - 255 arvoa"}, "INT": {"a": "(luku)", "d": "Pyöristää luvun alaspäin lähimpään kokonai<PERSON>lukuun", "ad": "on re<PERSON><PERSON><PERSON>, jonka haluat pyöristää alaspäin kokonaisluvuksi"}, "ISO.CEILING": {"a": "(luku; [tarkkuus])", "d": "Palauttaa luvun pyöristettynä lähimpään kokonaislukuun tai tarkkuuden kerrannaiseen. Luvun etumerkistä huolimatta arvo pyöristetään aina ylöspäin. <PERSON><PERSON> luku tai tarkkuus on pariton kokonaisluku, sitä ei pyöristetä.", "ad": "on pyöristettävä luku!on kerrannainen, johon haluat pyöristää luvun"}, "LCM": {"a": "(luku1; [luku2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> pie<PERSON>än yhtei<PERSON> kertojan", "ad": "ovat 1 - 255 ar<PERSON><PERSON>, joille haluat pienimmän yhteisen kertojan"}, "LN": {"a": "(luku)", "d": "Palauttaa luvun luonnollisen logaritmin", "ad": "on positiivinen reaaliluku, jolle haluat laskea luonnollisen logaritmin"}, "LOG": {"a": "(luku; [kanta])", "d": "Palauttaa luvun logaritmin käyttämällä annettua kantal<PERSON>a", "ad": "on positiivinen reaaliluku, jolle haluat laskea logaritmin!on logaritmin kantaluku. Jos arvoa ei määritetä, käytetään arvoa 10"}, "LOG10": {"a": "(luku)", "d": "<PERSON>lautta<PERSON> luvun 10-kanta<PERSON>n logaritmin", "ad": "on positiiv<PERSON> reaaliluku, jolle haluat laskea 10-kantaisen logaritmin"}, "MDETERM": {"a": "(matriisi)", "d": "Palauttaa matriisin matriisideterminantin", "ad": "on luku<PERSON>ri<PERSON>, jossa on yhtä monta riviä ja saraketta. Arvo voi olla solualue tai matriisivakio"}, "MINVERSE": {"a": "(matriisi)", "d": "Palauttaa matriisin käänteismatriisin", "ad": "on luku<PERSON>ri<PERSON>, jossa on yhtä monta riviä ja saraketta. Tämä voi olla solualue tai matriisivakio"}, "MMULT": {"a": "(matriisi1; matriisi2)", "d": "Palauttaa kahden matriisin tulon. <PERSON><PERSON><PERSON><PERSON>a on yhtä monta riviä kuin matriisissa 1 ja yhtä monta saraketta kuin matriisissa 2", "ad": "on ensimmäinen matriisi, jonka haluat kertoa. Matriisissa täytyy olla yhtä monta saraketta kuin matriisissa 2 on rivejä"}, "MOD": {"a": "(luku; jakaja)", "d": "Palauttaa jakoj<PERSON><PERSON><PERSON><PERSON><PERSON>en", "ad": "on luku, jonka jakojäännöksen haluat laskea!on arvo, jolla haluat jakaa luvun"}, "MROUND": {"a": "(luku; kerran<PERSON><PERSON>)", "d": "Palauttaa luvun pyöristettynä haluttuun kerrannaiseen.", "ad": "on pyöristettävä arvo!on kerrannainen, johon haluat pyöristää luvun"}, "MULTINOMIAL": {"a": "(luku1; [luku2]; ...)", "d": "<PERSON><PERSON><PERSON>a lukujoukon multinomin", "ad": "ovat 1 - 255 arvoa, joille haluat multinomin"}, "MUNIT": {"a": "(ulottuvuus)", "d": "<PERSON><PERSON><PERSON><PERSON> valitun ul<PERSON>n yksikön matriisin", "ad": "on kokonaisluku, joka määrittää palautettavan yksikön matriisin ulottuvuuden"}, "ODD": {"a": "(luku)", "d": "Pyöristää positiivisen luvun ylöspäin ja negatiivisen luvun alaspäin lähimpään parittomaan kokonai<PERSON>uun", "ad": "on pyöristettävä arvo"}, "PI": {"a": "()", "d": "Palauttaa piin likiarvon 15 numeron tarkkuudella (3,14159265358979)", "ad": ""}, "POWER": {"a": "(luku; potenssi)", "d": "Palauttaa luvun korotettuna haluttuun potenssiin", "ad": "on kantaluku, joka voi olla mikä tahansa reaaliluku!on ekspo<PERSON><PERSON>, johon kantaluku korotetaan"}, "PRODUCT": {"a": "(luku1; [luku2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> tulon", "ad": "ovat 1 - 255 lukua, totuusarvoa tai tekstimuotoista lukua, jotka haluat kertoa keskenään"}, "QUOTIENT": {"a": "(oso<PERSON><PERSON>; nimittäjä)", "d": "<PERSON>lau<PERSON><PERSON> o<PERSON>ää<PERSON>ä<PERSON>.", "ad": "on jaettava!on jakaja"}, "RADIANS": {"a": "(kulma)", "d": "<PERSON><PERSON><PERSON> asteet r<PERSON><PERSON><PERSON>", "ad": "on muun<PERSON><PERSON> kulma asteina"}, "RAND": {"a": "()", "d": "Palauttaa tasaisesti jakautuneen satunnaisluvun, joka on yhtä suuri tai suurempi kuin 0 ja pienempi kuin 1", "ad": ""}, "RANDARRAY": {"a": "([rivit]; [sarak<PERSON>et]; [minimi]; [maksimi]; [koko<PERSON><PERSON><PERSON>u])", "d": "<PERSON><PERSON><PERSON><PERSON> satun<PERSON><PERSON><PERSON>n matri<PERSON>n", "ad": "palautettavan matriisin rivien lukumäärä!palautettavan matriisin sarakkeiden lukumäärä!pienin arvo, jonka haluat palauttaa!suurin arvo, jonka haluat palauttaa!palauttaa kokonaisluvun tai desimaaliluvun. TOSI jos koko<PERSON>, EPÄTOSI jos desimaaliluku"}, "RANDBETWEEN": {"a": "(ala; ylä)", "d": "Palauttaa satunnaisluvun määritettyjen arvojen väliltä.", "ad": "on <PERSON><PERSON> kokonai<PERSON>luku, jonka SATUNNAISLUKU.VÄLILTÄ-funktio palauttaa!on su<PERSON><PERSON> kokonaisluku, jonka SATUNNAISLUKU.VÄLILTÄ-funktio palauttaa"}, "ROMAN": {"a": "(luku; [muoto])", "d": "Muuntaa arabialaiset numerot roomalaisiksi numeroiksi", "ad": "on arabialainen luku, jonka haluat muuntaa!on luku, joka mä<PERSON>, minkätyyppisen roomalaisen luvun haluat."}, "ROUND": {"a": "(luku; numerot)", "d": "Pyöristää luvun annettuun määrään desimaaleja", "ad": "on pyöristettävä luku!on käytettävien desimaalien määrä. Negatiivinen arvo pyöristää desimaalipilkun vasemmalle puolelle, nolla lähimpään kokonaislukuun"}, "ROUNDDOWN": {"a": "(luku; numerot)", "d": "Pyöristä<PERSON> luvun alaspäin (no<PERSON><PERSON> kohti)", "ad": "on re<PERSON><PERSON><PERSON>, jonka haluat pyöristää alaspäin!on numeroiden määrä, johon haluat pyöristää luvun. Negatiivisella arvolla pyöristäminen tapahtuu desimaalipilkun vasemmalle puolelle. <PERSON><PERSON> arvo on nolla tai se puuttuu, ohjelma pyöristää lähimpään kokonaislukuun"}, "ROUNDUP": {"a": "(luku; numerot)", "d": "Pyöristää luvun y<PERSON> (nollasta pois<PERSON>)", "ad": "on re<PERSON><PERSON><PERSON>, jonka haluat pyöristää ylöspäin!on numeroiden määrä, johon haluat pyöristää luvun. Negatiivisella arvolla pyöristäminen tapahtuu desimaalipilkun vasemmalle puolelle. <PERSON><PERSON> arvo on nolla tai se puuttuu, ohjelma pyöristää lähimpään kokonaislukuun"}, "SEC": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> kulman se<PERSON>", "ad": "on r<PERSON><PERSON><PERSON> kul<PERSON>, jolle haluat laskea sekantin"}, "SECH": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> kulman hyperbolisen sekantin", "ad": "on radia<PERSON><PERSON> kulma, jolle haluat laskea hyperbolisen sekantin"}, "SERIESSUM": {"a": "(x; n; m; kertoimet)", "d": "<PERSON><PERSON><PERSON><PERSON> summan.", "ad": "on potenssisarjan syöttöarvo!on ensimmäinen potenssi, johon haluat korottaa argumentin x!on askel, jonka verran potenssi kasvaa tekijästä toiseen!on jouk<PERSON> kertoi<PERSON>, j<PERSON><PERSON> jokainen peräkkäinen argumentin x arvo kerrotaan"}, "SIGN": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> luvun etumerkin. <PERSON><PERSON> luku on positiivinen, arvo on 1. <PERSON><PERSON> luku on nolla, funktio palauttaa arvon 0. <PERSON><PERSON> luku on negatii<PERSON><PERSON>, funktio palauttaa arvon -1.", "ad": "on mik<PERSON> ta<PERSON> re<PERSON>"}, "SIN": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> annetun kulman sinin", "ad": "on kulma radiaaneina, josta haluat laskea sinin. Asteet*(pii/180)=radiaanit"}, "SINH": {"a": "(luku)", "d": "<PERSON>lauttaa luvun hyperbolisen sinin", "ad": "on mik<PERSON> ta<PERSON> re<PERSON>"}, "SQRT": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> luvun ne<PERSON>", "ad": "on luku, josta haluat laskea ne<PERSON>"}, "SQRTPI": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> (luku*pii) neliöjuuren", "ad": "on luku, jolla pii kerrotaan"}, "SUBTOTAL": {"a": "(funktio_nro; viittaus1; ...)", "d": "Laskee välisumman luettelosta tai tietokannasta", "ad": "on välisummien laskennassa käytettävän funktion määrittävä luku välillä 1 - 11.!ovat 1 - 254 aluetta tai viittausta, joista vä<PERSON>umma lasketaan"}, "SUM": {"a": "(luku1; [luku2]; ...)", "d": "<PERSON><PERSON><PERSON> solualueessa olevien lukujen summan", "ad": "ovat 1 - 255 yhteenlaskettavaa lukua. <PERSON><PERSON><PERSON> olevat totuusarvot ja teksti j<PERSON>etään huomioimatta. Totuusarvot ja teksti huomioidaan vain, jos ne kirjo<PERSON>ta<PERSON> argumentteina"}, "SUMIF": {"a": "(alue; ehdot; [summa_alue])", "d": "Laskee ehdot täyttävien solujen summan", "ad": "on solualue, jonka haluat testata!on ehto, joka määrittä<PERSON>, mitk<PERSON> solut lasketaan yhteen. <PERSON>hto voi olla luku, lauseke tai merkkijono!ovat solut, jotka lasketaan yhteen. Jos argumenttia ei määritetä, kaikki solualueen solut lasketaan yhteen"}, "SUMIFS": {"a": "(summa-alue; eh<PERSON><PERSON>ue; ehdot; ...)", "d": "Lisää tiettyjen ehtojen määrittämät solut", "ad": "ovat todelliset yhteenlaskettavat solut.!on solualue, jonka haluat laskea tietyllä ehdolla!on ehto, joka voi olla lisättävät solut määrittävä luku, lauseke tai teksti"}, "SUMPRODUCT": {"a": "(matriisi1; [matriisi2]; [matriisi3]; ...)", "d": "Palauttaa toisiaan vastaavien alueiden tai matriisin osien tulojen summan", "ad": "ovat 2 - 255 matriisia, j<PERSON>en osat haluat ensin kertoa keskenään ja sen jälkeen laskea yhteen. Kaikkien matriisien on oltava samankokoisia"}, "SUMSQ": {"a": "(luku1; [luku2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> argumenttien ne<PERSON>. Arvot voivat olla lukuja, <PERSON><PERSON><PERSON>, matri<PERSON><PERSON> tai vii<PERSON><PERSON><PERSON> lukuihin", "ad": "ovat 1 - 255 lukua, mat<PERSON><PERSON><PERSON>, ni<PERSON><PERSON> tai vii<PERSON><PERSON><PERSON> luku<PERSON>, joista haluat laskea ne<PERSON>n"}, "SUMX2MY2": {"a": "(matriisi_x; matriisi_y)", "d": "<PERSON><PERSON><PERSON> kahden alueen tai matriisin toisiaan vastaavien arvojen neliösummien erotuksen", "ad": "on ensimmäinen matriisi tai arvoalue. Argumentti voi olla luku, nimi, matriisi tai viittaus, jossa on lukuja!on toinen matriisi tai arvoalue. Argumentti voi olla luku, nimi, matriisi tai viittaus, jossa on lukuja"}, "SUMX2PY2": {"a": "(matriisi_x; matriisi_y)", "d": "<PERSON><PERSON><PERSON><PERSON> kahden alueen tai matriisin toisiaan vastaavien arvojen neliösummien summan", "ad": "on ensimmäinen matriisi tai arvoalue. Argumentti voi olla luku, nimi, matriisi tai viittaus, jossa on lukuja!on toinen matriisi tai arvoalue. Argumentti voi olla luku, nimi, matriisi tai viittaus, jossa on lukuja"}, "SUMXMY2": {"a": "(matriisi_x; matriisi_y)", "d": "<PERSON><PERSON><PERSON> kahden alueen tai matriisin toisiaan vastaavien arvojen erotusten neliösumman", "ad": "on ensimmäinen matriisi tai arvoalue. Argumentti voi olla luku, nimi, matriisi tai viittaus, jossa on lukuja!on toinen matriisi tai arvoalue. Argumentti voi olla luku, nimi, matriisi tai viittaus, jossa on lukuja"}, "TAN": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> kulman tangentin", "ad": "on radiaaneina se kulma, josta haluat laskea tangentin. Asteet*(pii/180)=radiaanit"}, "TANH": {"a": "(luku)", "d": "Palauttaa luvun hyperbolisen tangentin", "ad": "on mik<PERSON> ta<PERSON> re<PERSON>"}, "TRUNC": {"a": "(luku; [numerot])", "d": "Katkaisee luvun kokonai<PERSON>luvuksi poistamalla desimaali- tai murto-osan", "ad": "on katkaistava luku!on katkaisutarkkuuden määrittävä luku. Jos arvoa ei määritetä, oh<PERSON><PERSON> k<PERSON>tää arvoa 0 (nolla)"}, "ADDRESS": {"a": "(rivi_nro; sarake_nro; [vii<PERSON><PERSON><PERSON><PERSON>]; [a1]; [tauluk<PERSON>_teksti])", "d": "<PERSON><PERSON><PERSON><PERSON> solu<PERSON><PERSON><PERSON><PERSON> te<PERSON>, kun <PERSON><PERSON>a annetaan rivien ja sarakkeiden numerot", "ad": "on soluviittauksessa käytettävän rivin numero: Rivinumero=1 riville 1!on soluviittauksessa käytettävän sarakkeen numero. Esimerkiksi sarakkeelle D Sarakenumero=4!määrittää viittauksen lajin:  suora = 1; suora viittaus riviin, suht<PERSON>linen viittaus sarakkeeseen = 2, su<PERSON><PERSON><PERSON>n viittaus riviin, suora viittaus sarakkeeseen = 3, suhteellinen viittaus = 4!on totuusarvo, joka määrittää käytetäänkö suoraa vai suhteellista viittausta: A1 (suora) viittaus = 1 tai TOSI; R1C1 (suhteellinen) viittaus = 0 tai EPÄTOSI!on ulkoisena viittauksena käytettävän laskentataulukon nimi"}, "CHOOSE": {"a": "(indeksi_luku; arvo1; [arvo2]; ...)", "d": "Valitsee arvon tai suoritettavan toimen indeksiluetteloon perustuvasta arvoluettelosta", "ad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mi<PERSON><PERSON> ar<PERSON>argum<PERSON><PERSON> valitaan. Indeksiluvun täytyy olla välillä 1 - 254 tai kaava tai viittaus soluun, jonka arvo on välillä 1 - 254!ovat 1 - 254 lukua, so<PERSON><PERSON><PERSON><PERSON><PERSON>, määrite<PERSON><PERSON> nime<PERSON>, ka<PERSON><PERSON>, funktiota tai teks<PERSON>, jotka määrittävät, mistä VALITSE.INDEKSI-funktio valitsee arvon tai toim<PERSON>, jonka se suorittaa"}, "COLUMN": {"a": "([viittaus])", "d": "Palauttaa annettua viittausta vastaavan sarakkeen numeron", "ad": "on solu tai yhten<PERSON>inen solualue, jonka sarakenumeron haluat saada selville. Jos tätä arvoa ei anneta, k<PERSON>ytetä<PERSON><PERSON> solua, joka sisältää SARAKE-funktion"}, "COLUMNS": {"a": "(matriisi)", "d": "Palauttaa viittauksessa tai matriisissa olevien sarakkeiden määrän", "ad": "on matriisi, matri<PERSON><PERSON><PERSON> tai viittaus solu<PERSON>, jonka sa<PERSON><PERSON> määrän haluat laskea"}, "FORMULATEXT": {"a": "(viittaus)", "d": "Pa<PERSON><PERSON><PERSON> ka<PERSON> me<PERSON>jon<PERSON>", "ad": "on vii<PERSON>us kaavaan"}, "HLOOKUP": {"a": "(haku<PERSON><PERSON>; tauluk<PERSON>_matriisi; rivi_indeksi_nro; [alue_haku])", "d": "Hakee annettua arvoa matriisin tai taulukon ylimmältä riviltä ja palauttaa samassa sarakkeessa ja määrittämälläsi rivillä olevan arvon", "ad": "on arvo, jonka haluat hakea taulukon ensimmäiseltä riviltä. Hakuarvo voi olla arvo, viittaus tai lainausmerkeissä oleva merkkijono!on tekstiä, lukuja tai totuusarvoja sisältävä taulukko, josta ohjelma hakee tietoja. Taulukko_matriisi voi olla viittaus alueeseen tai alueen nimi!on taulukko_matriisin rivinumero, josta ohjelma palauttaa hakuarvoa vastaavan arvon. Taulukon ensimmäinen tietoja sisältävä rivi on rivi 1!määrittää, miten arvo etsitään: TOSI tai ei arvoa = etsitään 1. riviltä lähin vastaava (nousevasti lajiteltu); EPÄTOSI = täsmällinen vastine"}, "HYPERLINK": {"a": "(<PERSON>in_kuvaus; [<PERSON><PERSON>_tiedot])", "d": "<PERSON><PERSON>, joka a<PERSON><PERSON> k<PERSON>, verk<PERSON>palvelimella tai Internetissä olevan asiaki<PERSON>", "ad": "on teksti, joka määrittä<PERSON> linkin kautta avattavan tiedoston tiedostonimen ja sijainnin kiintolevyllä, UNC-osoitteen tai URL-polun!on numero tai teksti, joka tulee näkyviin linkin solussa. <PERSON><PERSON> täm<PERSON> j<PERSON>et<PERSON>än pois, solussa näkyy linkin_sijainti-argumentin teksti"}, "INDEX": {"a": "(matriisi; rivi_nro; [sarake_nro]!viittaus; rivi_nro; [sarake_nro]; [alue_nro])", "d": "Palauttaa solun arvon tai viitta<PERSON>sen tietyn alueen rivin ja sarakkeen yhtymäkohdasta", "ad": "on solualue tai matriisivakio.!valitsee matriisista tai viittauksesta rivin, josta funktio palauttaa arvon. Jos argumenttia ei määritetä, tarvitaan argumentti sarake_nro!valitsee matriisista tai viittauk<PERSON><PERSON> sarak<PERSON>en, josta funktio palauttaa arvon. Jos argumenttia ei määritetä, argumentille rivi_nro täytyy määrittää arvo!on viittaus yhteen tai useaan solualueeseen!valitsee matriisista tai viittauksesta rivin, josta funktio palauttaa arvon. <PERSON><PERSON> tämä jätetään pois, argumentille sarake_nro täytyy määrittää arvo!valitsee matriisista tai viittauksesta sarakkeen, josta funktio palauttaa arvon. Jo<PERSON> tämä jätetään pois, argumentille rivi_nro täytyy määrittää arvo!valitsee viitta<PERSON><PERSON><PERSON> alue<PERSON>, josta palautetaan arvo. Ensimmäinen valittu tai kirjoitettu alue on alue1, toinen alue on alue2 ja niin edelleen"}, "INDIRECT": {"a": "(viittaus_teksti; [a1])", "d": "Palauttaa merkkijonon määrittämän viittauksen", "ad": "on viittaus soluun, joka sisältää A1-tyyppisen viitta<PERSON>sen, R1C1-tyyppisen viitta<PERSON>sen tai nimen, joka on määritetty viittaukseksi, tai merkkijonomuotoisen viittauksen soluun!on to<PERSON><PERSON><PERSON><PERSON>, joka määrittää, mink<PERSON> tyyppinen viittaus on argumentissa viittaus_teksti. R1C1 =  EPÄTOSI, A1 = TOSI tai jätetty pois"}, "LOOKUP": {"a": "(hakuarvo; hakuvektori; [tulosvektori]!hakuarvo; matriisi)", "d": "Etsii arvoja yhden rivin tai sarakkeen kokoisesta alueesta tai matriisista", "ad": "on arvo, jota HAKU-funktio etsii ensimmäisestä vektorista. Arvo voi olla luku, merk<PERSON><PERSON>o, totu<PERSON>r<PERSON>, nimi tai viittaus!on alue, jossa on vain yksi rivi tai sarake tekstiä, lukuja tai loogisia arvoja, jotka ovat nousevassa järjestyksessä!on vain yhden rivin tai sarakkeen sisältävä alue!on arvo, jota HAKU-funktio hakee matriisista. Arvo voi olla luku, merkkijono, totuusarvo, nimi tai viittaus!on solualue, jossa on tekstiä, lukuja tai totuusarvoja, joita haluat verrata hakuarvoon"}, "MATCH": {"a": "(hakuarvo; haku_matriisi; [vastine_laji])", "d": "<PERSON><PERSON><PERSON><PERSON> sen matriisin osan <PERSON> si<PERSON>, joka vastaa määritettyj<PERSON>", "ad": "on arvo, jota vastaavan arvon haluat etsiä taulukosta. Arvo voi olla luku, merk<PERSON>jono, totuusarvo tai viittaus!on jatkuva solualue, joka sisältää mahdolliset etsittävät arvot, matriisin tai viittauksen matriisiin!on luku 1, 0 tai -1, joka ilmoittaa palautettavan arvon."}, "OFFSET": {"a": "(viittaus; rivit; sarakkeet; [korkeus]; [leveys])", "d": "Palautta<PERSON> vii<PERSON><PERSON><PERSON>, joka on annetun etäisyyden (sarakkeina ja riveinä) päässä annetusta viittauksesta", "ad": "on viittaus, johon siirtymä perustuu. V<PERSON><PERSON><PERSON><PERSON> täytyy kohdistua soluun tai yhtenäiseen solualueeseen!on rivien lukumäärä, joka osoittaa, kuinka monta riviä ylös- tai alaspäin vasemmassa yläkulmassa oleva solu viittaa!on sarakkeiden lukumäärä, joka osoittaa, kuinka monta saraketta vasemmalle tai oikealle vasemmassa yläkulmassa oleva solu viittaa!on luku, joka osoittaa palautettavan viittauksen korkeuden riveinä. Jos arvoa ei määritetä, ohjelma käyttää samaa arvoa kuin viittauksessa!on luku, joka osoittaa palautettavan viittauksen leveyden. Jos arvoa ei määritetä, ohjelma käyttää samaa arvoa kuin viittauksessa"}, "ROW": {"a": "([viittaus])", "d": "Palauttaa viittauksen rivinumeron", "ad": "on solu tai solualue, jonka rivinumeron haluat saada. Jos arvoa ei määritetä, funktio palauttaa viitta<PERSON>sen siihen soluun, joka sisältää RIVI-funktion"}, "ROWS": {"a": "(matriisi)", "d": "Palauttaa viittauksessa tai matriisissa olevien rivien määrän", "ad": "on matriisi, matri<PERSON>kaava tai viittaus solu<PERSON>, jonka rivimä<PERSON><PERSON><PERSON>n haluat tietää"}, "TRANSPOSE": {"a": "(matriisi)", "d": "<PERSON>untaa vertikaalisen solualueen horisontaaliseksi ja päinvas<PERSON>in", "ad": "on laskentataulukossa oleva solualue tai ar<PERSON>matriisi, jonka haluat transponoida"}, "UNIQUE": {"a": "(matriisi; [by_col]; [exactly_once])", "d": " Palauttaa alueen tai matriisin ainutkertaiset arvot.", "ad": "aluetta tai matriisia, josta palautetaan yksilöllisiä rivejä tai sarakkeita!on totuusarvo: vertaa rivejä toisiinsa ja palauttaa uniikit rivit = EPÄTOSI tai puuttuu; vertaa sarakkeita toisiinsa ja palauttaa uniikit sarakkeet = TOSI! on totuusarvo: palauttavat rivit tai sarakkeet, jotka esiintyvät täsmälleen kerran matriisista = TOSI; palauttaa kaikki erilliset rivit tai sarakkeet matriisista = EPÄTOSI tai puuttuu"}, "VLOOKUP": {"a": "(haku<PERSON><PERSON>; taulukko_matriisi; sar_indeksi_nro; [alue_haku])", "d": "Hakee solun arvoa taulukon vasemmanpuoleisimmasta sarakkeesta ja palauttaa arvon samalla rivillä määritetystä sarakkeesta. Oletusarvoisesti taulukon tulee olla lajiteltu nousevassa järjestyksessä", "ad": "on arvo, jonka haluat hakea taulukon ensimmäisestä sarakkeesta. Argumentti voi olla arvo, viittaus tai merkkijono!on teksti-, luku- tai totuusar<PERSON><PERSON><PERSON><PERSON><PERSON>, josta ohjelma hakee tietoa. Taulukko_matriisi voi olla viittaus alueeseen tai alueen nimeen!on ensimmäinen sarake taulukko_matriisissa, josta ohjelma palauttaa hakuarvoa vastaavan luvun. Taulukon 1. arvosarake on sarake 1!mä<PERSON>rittää, miten arvo etsitään: TOSI = etsitään 1. sarakkeesta (nousevasti lajiteltu) lähin vastine; EPÄTOSI = etsitään täsmällinen vastine"}, "XLOOKUP": {"a": "(haku<PERSON><PERSON>; hakumatri<PERSON>; pala<PERSON><PERSON><PERSON><PERSON><PERSON>; [jos_ei_lö<PERSON><PERSON>]; [vastaa<PERSON>ust<PERSON>]; [haku<PERSON>a])", "d": "<PERSON><PERSON><PERSON> vastaavaa aluetta tai matriisia ja palauttaa vastaavan kohteen toisesta alueesta tai matriisista. Oletusarvoisesti tarkkaa vastaavuutta k<PERSON>ytetään", "ad": "on haettava arvo!on matriisi tai alue, jolta haetaan!on matriisi tai alue, joka palauteta<PERSON>!pala<PERSON><PERSON><PERSON>, jos vastaa<PERSON>a kohdetta ei löydy!määritä, miten hakuarvo yhdistetään hakumatriisin arvoihin!määritä käytettävä hakutila. Oletusarvoisesti käytetään ensimmäisestä viimeiseen -hakua"}, "CELL": {"a": "(kuvaus_laji; [viittaus])", "d": "Palauttaa tietoja solun muoto<PERSON>, si<PERSON><PERSON><PERSON> ja sis<PERSON>llöst<PERSON>", "ad": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, j<PERSON>, millaisia tietoja solusta halutaan tulokseksi!solu, josta halutaan tietoja"}, "ERROR.TYPE": {"a": "(virhearvo)", "d": "<PERSON><PERSON><PERSON><PERSON> vir<PERSON><PERSON><PERSON>a vastaavan luvun.", "ad": "on virhearvo, jonka tunnistenumeron haluat löytää.Virhearvo voi olla todellinen virhearvo tai viittaus soluun, joka sis<PERSON>lt<PERSON> virhearvon"}, "ISBLANK": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko vii<PERSON> kohteena tyhjä solu, ja palauttaa arvon TOSI tai EPÄTOSI", "ad": "on solu tai nimi, joka viittaa testattavaan soluun"}, "ISERR": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko arvo virhe, joka on muu kuin #PUUTTUU, ja palauttaa TOSI- tai EPÄTOSI-arvon", "ad": "on arvo, jonka haluat testata. Arvo voi olla viittaus soluun tai kaavaan tai nimeen, joka viittaa soluun, kaavaan tai arvoon"}, "ISERROR": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko arvo virhe, ja palauttaa TOSI- tai EPÄTOSI-arvon", "ad": "on arvo, jonka haluat testata. Arvo voi olla viittaus soluun tai kaavaan tai nimeen, joka viittaa soluun, kaavaan tai arvoon"}, "ISEVEN": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>von <PERSON>, jos luku on parill<PERSON>", "ad": "on testattava arvo"}, "ISFORMULA": {"a": "(viittaus)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko viittaus kaavan sisältävä solu, ja palauttaa arvon TOSI tai EPÄTOSI", "ad": "on viittaus testattavaan soluun.  Viittaus voi olla soluviittaus, kaava tai soluun viittaava nimi"}, "ISLOGICAL": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko arvo totuusarvo (TOSI tai EPÄTOSI), ja palauttaa arvon TOSI tai EPÄTOSI", "ad": "on arvo, jonka haluat testata. Arvo voi olla viittaus soluun tai kaavaan tai nimeen, joka viittaa soluun, kaavaan tai arvoon"}, "ISNA": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko arvo #PUUTTUU, ja palauttaa TOSI- tai EPÄTOSI-arvon", "ad": "on arvo, jonka haluat testata. Arvo voi olla viittaus soluun tai kaavaan tai nimeen, joka viittaa soluun, kaavaan tai arvoon"}, "ISNONTEXT": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko arvo muuta kuin tekstiä (tyhjät solut eivät ole tekstiä), ja palauttaa arvon TOSI tai EPÄTOSI", "ad": "on arvo, jonka haluat testata. Arvo voi olla solu tai kaava tai nimi, joka vii<PERSON>a soluun, kaavaan tai arvoon"}, "ISNUMBER": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko arvo luku, ja palauttaa arvon TOSI tai EPÄTOSI", "ad": "on arvo, jonka haluat testata. Arvo voi olla viittaus soluun tai kaavaan tai nimeen, joka viittaa soluun, kaavaan tai arvoon"}, "ISODD": {"a": "(luku)", "d": "<PERSON><PERSON><PERSON><PERSON> arvon <PERSON>, jos luku on pariton", "ad": "on testattava arvo"}, "ISREF": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko arvo viittaus, ja palauttaa arvon <PERSON>SI tai EPÄTOSI", "ad": "on arvo, jonka haluat testata. Arvo voi olla viittaus soluun tai kaavaan tai nimeen, joka viittaa soluun, kaavaan tai arvoon"}, "ISTEXT": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko arvo te<PERSON>, ja palauttaa arvon <PERSON>SI tai EPÄTOSI", "ad": "on arvo, jonka haluat testata. Arvo voi olla viittaus soluun tai kaavaan tai nimeen, joka viittaa soluun, kaavaan tai arvoon"}, "N": {"a": "(arvo)", "d": "Muuntaa muun kuin numeroarvon numeroksi, päivämäärät järjestysnumeroksi, TOSI-arvon arvoksi 1 ja kaikki muut arvot arvoksi 0 (nolla)", "ad": "on muunnettava arvo"}, "NA": {"a": "()", "d": "Palauttaa virhearvon #PUUTTUU (arvo ei ole käytettävissä)", "ad": ""}, "SHEET": {"a": "([arvo])", "d": "Palauttaa viitattavan taulukon numeron", "ad": "on taulukon nimi tai viittaus, josta haluat taulukon luvun. <PERSON><PERSON> se j<PERSON>än pois, funktion sisältävän taulukon luku palautetaan"}, "SHEETS": {"a": "([viittaus])", "d": "Palauttaa viittauksessa olevien taulukoiden määrän", "ad": "on viittaus, josta haluat tietää sen sisältämien taulukoiden määrän. <PERSON><PERSON> j<PERSON> pois, ty<PERSON><PERSON><PERSON><PERSON> funktion sisältävien taulukoiden määrä palautetaan"}, "TYPE": {"a": "(arvo)", "d": "<PERSON><PERSON><PERSON><PERSON>, joka vastaa arvon tie<PERSON>: numero = 1, teksti = 2, totuusarvo = 4, virhearvo = 16, matri<PERSON> = 64; yhdistelmätiedot = 128", "ad": "voi olla mikä tahansa arvo"}, "AND": {"a": "(totuus1; [totuus2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko kaikkien argumenttien totuusarvo TOSI, ja palauttaa totuusarvon TOSI, jos n<PERSON>in on", "ad": "ovat 1 - 255 eh<PERSON><PERSON>, jotka voivat testattaessa saada arvon TOSI tai EPÄTOSI. Ehdot voivat olla loogisia arvoja, matriiseja tai viittauksia"}, "FALSE": {"a": "()", "d": "Palauttaa totuusarvon EPÄTOSI", "ad": ""}, "IF": {"a": "(logiikka_testi; [arvo_jos_tosi]; [arvo_jos_epätosi])", "d": "Ta<PERSON><PERSON>a, täyttyykö määrittämäsi ehto. Palauttaa y<PERSON><PERSON> arvon, jos ehto on TOSI ja toisen arvon, jos ehto on EPÄTOSI", "ad": "on mikä tahansa arvo tai lauseke, joka voi saada arvoksi TOSI tai EPÄTOSI!on palautettava arvo, jos logiikka_testi on TOSI. Jos arvoa ei määritetä, funktio palauttaa arvon TOSI. Voit määrittää 7 sisäkkäistä JOS-funktiota!on palautettava arvo, jos logiikka_testi on EPÄTOSI. Jos arvoa ei määritetä, funktio palauttaa arvon EPÄTOSI"}, "IFS": {"a": "(logiikka_testi; arvo_jos_tosi; ...)", "d": "Tarkistaa, täyttyykö vähintään yksi eh<PERSON>, ja palauttaa ensimmäistä TOSI-ehtoa vastaavan arvon", "ad": "on mik<PERSON> tahansa arvo tai lauseke, joka voi saada arvoksi TOSI tai EPÄTOSI!on arvo, joka palaute<PERSON>, kun logicaltest-arvo on TOSI"}, "IFERROR": {"a": "(arvo; arvo_jos_virhe)", "d": "<PERSON><PERSON><PERSON><PERSON> arvo_jos_virheen, jos lauseke on virhe ja lausekkeen arvo jokin muu", "ad": "on mikä tahansa arvo tai lauseke tai viittaus!on mikä tahansa arvo tai lauseke tai viittaus"}, "IFNA": {"a": "(arvo; arvo_jos_ei_mit<PERSON>än)", "d": "Pa<PERSON>ttaa mää<PERSON>, jos lauseke antaa ratkaisuksi #Ei mitään, muutoin se palauttaa lausekkeen tuloksen", "ad": "on mikä tahansa arvo, lauseke tai viittaus!on mikä tahansa arvo, lauseke tai viittaus"}, "NOT": {"a": "(totuus)", "d": "Kääntää EPÄTOSI-arvon TOSI-arvoksi tai TOSI-arvon EPÄTOSI-arvoksi", "ad": "on arvo tai lauseke, joka voi saada arvoksi TOSI tai EPÄTOSI"}, "OR": {"a": "(totuus1; [totuus2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON>, onko <PERSON>n argumentin totuusarvo TOSI, ja palauttaa TOSI- tai EPÄTOSI-arvon. <PERSON><PERSON> kaikkien argumenttien arvo on EPÄTOSI, funktio palauttaa arvon EPÄTOSI", "ad": "ovat 1 - 255 eh<PERSON>a, jotka voivat testattaessa saada arvon TOSI tai EPÄTOSI"}, "SWITCH": {"a": "(la<PERSON><PERSON>; arvo1; tulos1; [oletus_tai_arvo2]; [tulos2]; ...)", "d": "<PERSON><PERSON><PERSON> lausekkeen arvon luettelon arvojen per<PERSON> ja palauttaa tuloksena ensimmäisen vastaavan arvon. <PERSON><PERSON> vastaavu<PERSON> ei ole, palauttaa valinnaisen oletusarvon", "ad": "on laskettava lauseke!on lausekkeeseen verrattava arvo!on tulos, joka pala<PERSON>, jos lauseketta vastaava arvo l<PERSON>y"}, "TRUE": {"a": "()", "d": "Palauttaa totuusarvon TOSI", "ad": ""}, "XOR": {"a": "(totuus1; [totuus2]; ...)", "d": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> to<PERSON>uden 'Poissulkeva Tai'", "ad": "ovat 1 - 254 <PERSON><PERSON><PERSON>, jot<PERSON> halu<PERSON> testata, ja ne voivat olla joko TOSI tai EPÄTOSI ja totuusarvoja, matriiseja tai viittauksia"}, "TEXTBEFORE": {"a": "(te<PERSON><PERSON>, erotin, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "<PERSON><PERSON><PERSON><PERSON>, joka on ennen erotinmerk<PERSON>j<PERSON>.", "ad": "<PERSON><PERSON><PERSON>, jota haluat etsiä erottimelle.!Erotinmerkkinä käytettävä merkki tai merkkijono.!Erottimen haluttu esiintymä. Oletusarvo on 1. Negatiivinen luku etsii lopusta.!Etsii erottimen vastineen tekstistä. <PERSON><PERSON><PERSON><PERSON><PERSON> on oletusarvoisesti merkitsevä.!Määrittää, täsmätäänkö erotin tekstin loppuun. Oletusarvoisesti niitä ei täsmätä.!<PERSON><PERSON><PERSON><PERSON><PERSON>, jos vastinetta ei löydy. Oletusarvoisesti palautetaan #N/A."}, "TEXTAFTER": {"a": "(te<PERSON><PERSON>, erotin, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "<PERSON><PERSON><PERSON><PERSON>, j<PERSON> on erotinmerkkien jälkeen.", "ad": "<PERSON><PERSON><PERSON>, jota haluat etsiä erottimelle.!Erotinmerkkinä käytettävä merkki tai merkkijono.!Erottimen haluttu esiintymä. Oletusarvo on 1. Negatiivinen luku etsii lopusta.!Etsii erottimen vastineen tekstistä. <PERSON><PERSON><PERSON><PERSON><PERSON> on oletusarvoisesti merkitsevä.!Määrittää, täsmätäänkö erotin tekstin loppuun. Oletusarvoisesti niitä ei täsmätä.!<PERSON><PERSON><PERSON><PERSON><PERSON>, jos vastinetta ei löydy. Oletusarvoisesti palautetaan #N/A."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "<PERSON><PERSON><PERSON> te<PERSON><PERSON> rive<PERSON> tai sarak<PERSON>si erott<PERSON> avulla.", "ad": "Jaettava teksti!Merkki tai merk<PERSON>, jonka mukaan sarak<PERSON>et jaetaan.!Merkki tai merkki<PERSON>o, jonka mukaan rivit jaetaan.!Ohitetaanko tyhjät solut. Oletusarvo on FALSE.!Etsii erottimen vastineen tekstin. <PERSON><PERSON><PERSON><PERSON><PERSON> on oletusarvoisesti merkitsevä.!Ar<PERSON>, jota käytetään täyttämiseen. Oletusarvo on #N/A."}, "WRAPROWS": {"a": "(vektori, wrap_count, [pad_with])", "d": " Rivittää rivi- tai sarakevektorin määritetyn arvomäärän jälk<PERSON>.", "ad": " Rivitettävä vektori tai viittaus.! Arvojen enimmäismäärä riviä kohden.! <PERSON><PERSON><PERSON>, jolla alustaa. Oletusarvo on #N/A."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Pinoaa taulukot pystysuunnassa yhteen matriisiin.", "ad": "Pinottava matriisi tai viittaus."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Pinoaa taulukot vaakasuunnassa yhteen matriisiin.", "ad": "Pinottava matriisi tai viittaus."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Palauttaa matriisista tai viittauksesta vain määritetyt rivit", "ad": "Matriisi tai viittaus, joka sisältää palautettavat rivit!Palautettavan rivin numero."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Palauttaa matriisista tai viittauksesta vain mä<PERSON>tyt sarak<PERSON>et.", "ad": "Matriisi tai viittaus, joka sisältää palautettavat sarakkeet.!Palautettavan sarakkeen numero."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Palauttaa matriisin yhtenä sarakkeena.", "ad": "Sarakkeena palautettava matriisi tai viittaus.!Ohitetaanko tietyt arvotyypit. Oletusarvoisesti mitään arvoja ei ohiteta.!Tarkista taulukko sarakkeen mukaan. Oletusarvoisesti taulukko tarkistetaan rivin mukaan."}, "TOROW": {"a": "(matriisi, [ignore], [scan_by_column])", "d": "Palauttaa matriisin yhtenä rivinä. ", "ad": "Rivinä palautettava matriisi tai viittaus.!Ohitetaanko tietyt arvotyypit. Oletusarvoisesti mitään arvoja ei ohiteta.!Tarkista taulukko sarakkeen mukaan. Oletusarvoisesti taulukko tarkistetaan rivin mukaan."}, "WRAPCOLS": {"a": "(vektori, wrap_count, [pad_with])", "d": " Rivittää rivi- tai sarakevektorin määritetyn arvomäärän jälk<PERSON>.", "ad": " Rivitettävä vektori tai viittaus.! Arvojen enimmäismäärä riviä kohden.! <PERSON><PERSON><PERSON>, jolla alustaa. Oletusarvo on #N/A."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Palauttaa rivit tai sarakkeet matriisin alusta tai lopusta.", "ad": "<PERSON><PERSON><PERSON>, josta rivit tai sarakkeet otetaan.!Otettavien rivien määrä. Negatiivinen arvo on peräisin matriisin lopusta.!Otettavien sarakkeiden määrä. Negatiivinen arvo on peräisin matriisin lopusta."}, "DROP": {"a": "(array, rows, [columns])", "d": "Poistaa rivit tai sarakkeet matriisin alusta tai lopusta.", "ad": "<PERSON><PERSON><PERSON>, josta rivit tai sarak<PERSON>et poistetaan.!Poistettavien rivien määrä. Negatiivinen arvo poistaa matriisin lopusta.!Poistettavien sarakkeiden määrä. Negatiivinen arvo poistaa matriisin lopusta."}, "SEQUENCE": {"a": "(rivit, [sarakkeet], [alku], [vaihe])", "d": "Palauttaa numerosarjan", "ad": "palautettavien rivien määrä!palautettavien sarakkeiden määrä!sarjan ensimmäinen numero!määrä, jolla sarjan kukin seuraava arvo kasvaa"}, "EXPAND": {"a": "(matriisi, rivit, [sarakkeet], [pad_with])", "d": "Laajentaa matriisin määritettyyn kokoon.", "ad": "Laajennettava matriisi.!Laajennetusta matriisista löytyvien rivien lukumäärä. Jos tämä luku puuttuu, rivejä ei laajenneta.!Laajennetusta matriisista löytyvien sarakkeiden lukumäärä. Jos tämä luku puuttuu, sarak<PERSON>ita ei laajenneta.!<PERSON><PERSON><PERSON>, jonka perust<PERSON> täyttäminen tapahtuu. Oletusarvo on #N/A."}, "XMATCH": {"a": "(haku<PERSON><PERSON>, haku_matriisi, [vastaa<PERSON>ust<PERSON>], [hakutila])", "d": "Palauttaa matriisissa olevan koh<PERSON> su<PERSON> sija<PERSON>. Oletusarvoisesti tarkka vastaavuus vaaditaan", "ad": "on haettava arvo!on matriisi tai alue, jolta haetaan!määrit<PERSON>, miten hakuarvo yhdistetään hakumatriisin arvoihin!määritä käytettävä hakutila. Oletusarvoisesti käytetään ensimmäisestä viimeiseen -hakua"}, "FILTER": {"a": "(matri<PERSON>, sis<PERSON><PERSON><PERSON><PERSON>, [jos_tyhjä])", "d": "Suodata alue tai matriisi", "ad": "suodatettava alue tai matriisi!totuusarvojen matriisi, jossa TOSI tarkoittaa säilytettävää riviä tai saraketta!palautetaan jos mitään kohteita ei säilytetä"}, "ARRAYTOTEXT": {"a": "(matri<PERSON>, [muoto])", "d": "Palauttaa matriisin tekstin.", "ad": " ta<PERSON><PERSON><PERSON>, joka edustaa tekstinä! tekstin muoto"}, "SORT": {"a": "(matri<PERSON>, [lajit<PERSON><PERSON><PERSON><PERSON>], [lajitteluj<PERSON>r<PERSON><PERSON><PERSON>], [sarak<PERSON><PERSON>_mukaanl])", "d": "Lajittelee alueen tai matriisin", "ad": "lajiteltava alue tai matriisi!luku, joka ilmaisee sen rivin tai sarakkeen, jonka mukaan lajitellaan!luku, joka osoittaa halutun lajittelujärjestyksen; 1 = nouseva järjestys (oletus), -1 = laskeva järjestys!looginen arvo, joka osoittaa halutun lajittelujärjestyksen: EPÄTOSI = lajittelu rivin mukaan (oletus), TOSI = lajittelu sarakkeen mukaan"}, "SORTBY": {"a": "(matri<PERSON>, matri<PERSON>n_mukaan, [lajittelujärjestys], ...)", "d": "Lajittelee alueen tai matriisin vastaavan alueen tai matriisin arvojen per<PERSON>", "ad": "lajiteltava alue tai matriisi!alue tai matriisi, jonka mukaan lajit<PERSON>!numero, joka osoittaa halutun lajittelujärjestyksen; 1 tarkoittaa nousevaa järjestystä (oletusarvo), -1 tarkoittaa laskevaa järjestystä"}, "GETPIVOTDATA": {"a": "(tietokenttä; pivot_taulukko; [kenttä]; [osa]; ...)", "d": "Poimii Pivot-taulukkoon tallennettuja tietoja.", "ad": "on sen kentän nimi, josta tiedot poimitaan!on viittaus Pivot-taulukon soluun tai solual<PERSON>en, joka sisältää poimittavat tiedot!kenttä, johon viitataan!kentän osa, johon viitataan"}, "IMPORTRANGE": {"a": "(laskentataulukon_url, al<PERSON>en_merkkijono)", "d": "<PERSON><PERSON> tiedot määritetyn laskentataulukon soluväliltä.", "ad": "Sen laskentataulukon URL-<PERSON><PERSON><PERSON>, josta tiedot tuodaan!Merkkijono muodossa, joka määrittää tuotavan alueen"}}