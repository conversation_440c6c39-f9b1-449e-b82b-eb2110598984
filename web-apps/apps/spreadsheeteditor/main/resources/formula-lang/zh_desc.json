{"DATE": {"a": "(年; 月; 日)", "d": "返回在日期时间代码中代表日期的数字", "ad": "是介于 1900 或 1904(取决于工作簿的日期系统)到 9999 之间的数字!代表一年中月份的数字，其值在 1 到 12 之间!代表一个月中第几天的数字，其值在 1 到 31 之间"}, "DATEDIF": {"a": "(起始日期; 结束日期; 信息类型)", "d": "计算两个日期之间相隔的天数、月数或年数。", "ad": "表示给定时间段的第一个或开始日期的日期!用于表示时间段的最后一个（即结束）日期的日期!要返回的信息类型"}, "DATEVALUE": {"a": "(日期字符串)", "d": "将日期值从字符串转化为序列数，表示日期-时间代码的日期", "ad": "按电子表格编辑器日期格式表示的字符串，应在 1900/1/1 或 1904/1/1 (取决于工作簿的日期系统) 到 9999/12/31 之间"}, "DAY": {"a": "(序列号)", "d": "返回一个月中的第几天的数值，介于 1 到 31 之间。", "ad": "电子表格编辑器进行日期及时间计算的日期-时间代码"}, "DAYS": {"a": "(结束日期; 起始日期)", "d": "返回两个日期之间的天数。", "ad": "起始日期和结束日期是您想要知道期间天数的两个日期 !起始日期和结束日期是您想要知道期间天数的两个日期"}, "DAYS360": {"a": "(起始日期; 结束日期; [方法])", "d": "按每年 360 天返回两个日期间相差的天数(每月 30 天)", "ad": "起始日期和结束日期是要计算天数差值的起止日期!起始日期和结束日期是要计算天数差值的起止日期!是一个指定计算方法的逻辑值: FALSE 或忽略，使用美国(NASD)方法；TRUE，使用欧洲方法。"}, "EDATE": {"a": "(起始日期; 月数)", "d": "返回一串日期，指示起始日期之前/之后的月数", "ad": "是一串代表起始日期的日期!是起始日期之前/之后的月数"}, "EOMONTH": {"a": "(起始日期; 月数)", "d": "返回一串日期，表示指定月数之前或之后的月份的最后一天", "ad": "是一串代表起始日期的日期!是 起始日期之前/之后的月数"}, "HOUR": {"a": "(序列号)", "d": "返回小时数值，是一个 0 (12:00 A.M.) 到 23 (11:00 P.M.) 之间的整数。", "ad": "电子表格编辑器进行日期及时间计算的日期-时间代码，或以时间格式表示的文本，如 16:48:00 或 4:48:00 PM"}, "ISOWEEKNUM": {"a": "(日期)", "d": "返回给定日期所在年份的 ISO 周数目", "ad": " 是由 电子表格编辑器用于日期和时间计算的日期时间代码"}, "MINUTE": {"a": "(序列号)", "d": "返回分钟数值，是一个 0 到 59 之间的整数。", "ad": "电子表格编辑器进行日期及时间计算的日期-时间代码，或以时间格式表示的文本，如 16:48:00 或 4:48:00 PM"}, "MONTH": {"a": "(序列号)", "d": "返回月份值，是一个 1 (一月)到 12 (十二月)之间的数字。", "ad": "电子表格编辑器进行日期及时间计算的日期-时间代码"}, "NETWORKDAYS": {"a": "(起始日期; 结束日期; [假日])", "d": "返回两个日期之间的完整工作日数", "ad": "是一串表示起始日期的数字!是一串表示结束日期的数字!是要从工作日历中去除的一个或多个日期(一串数字)的可选组合，如传统假日、国家法定假日及非固定假日"}, "NETWORKDAYS.INTL": {"a": "(起始日期; 结束日期; [周末]; [假日])", "d": "使用自定义周末参数返回两个日期之间的完整工作日数", "ad": "是一串表示起始日期的数字!是一串表示结束日期的数字!是一个用于指定周末个数的数字或字符串!是要从工作日历中去除的一个或多个日期(一串数字)的可选组合，如传统假日、国家法定假日及非固定假日"}, "NOW": {"a": "()", "d": "返回日期时间格式的当前日期和时间。", "ad": ""}, "SECOND": {"a": "(序列号)", "d": "返回秒数值，是一个 0 到 59 之间的整数。", "ad": "电子表格编辑器进行日期及时间计算的日期-时间代码，或以时间格式表示的文本，如 16:48:23 或 4:48:47 PM"}, "TIME": {"a": "(时; 分; 秒)", "d": "返回特定时间的序列数", "ad": "介于 0 到 23 之间的数字，代表小时数!介于 0 到 59 之间的数字，代表分钟数!介于 0 到 59 之间的数字，代表秒数"}, "TIMEVALUE": {"a": "(时间文本)", "d": "将文本形式表示的时间转换成序列数，是一个从 0 (12:00:00 AM) 到 0.999988426 (11:59:59 PM) 的数。在输入公式后将数字设置为时间格式", "ad": "按任何一种电子表格编辑器时间格式表示的时间(其中的日期信息将忽略)"}, "TODAY": {"a": "()", "d": "返回日期格式的的当前日期。", "ad": ""}, "WEEKDAY": {"a": "(序列号; [返回类型])", "d": "返回代表一周中的第几天的数值，是一个 1 到 7 之间的整数。", "ad": "一个表示返回值类型的数字: !从 星期日=1 到 星期六=7，用 1；从 星期一=1 到 星期日=7，用 2；从 星期一=0 到 星期日=6 时，用 3"}, "WEEKNUM": {"a": "(序列号; [返回类型])", "d": "返回一年中的周数", "ad": "是 电子表格编辑器用于日期和时间计算的日期时间代码!是一个确定返回值类型的数值(1 或 2)"}, "WORKDAY": {"a": "(起始日期; 天数; [假日])", "d": "返回在指定的若干个工作日之前/之后的日期(一串数字)", "ad": "是一串表示起始日期的数字!是起始日期之前/之后非周末和非假日的天数!是要从工作日历中去除的一个或多个日期(一串数字)的可选组合，如传统假日、国家法定假日及非固定假日"}, "WORKDAY.INTL": {"a": "(起始日期; 天数; [周末]; [假日])", "d": "使用自定义周末参数返回在指定的若干个工作日之前/之后的日期(一串数字)", "ad": "是一串表示起始日期的数字!起始日期之前/之后非周末和非假日的天数!是一个用于指定周末个数的数字或字符串!是要从工作日历中去除的一个或多个日期(一串数字)的可选组合，如传统假日、国家法定假日及非固定假日"}, "YEAR": {"a": "(序列号)", "d": "返回日期的年份值，一个 1900-9999 之间的数字。", "ad": "电子表格编辑器进行日期及时间计算的日期-时间代码"}, "YEARFRAC": {"a": "(起始日期; 结束日期; [日算类型])", "d": "返回一个年分数，表示起始日期和结束日期之间的整天天数", "ad": "是一串代表起始日期的日期!是一串代表结束日期的日期!是所采用的日算类型"}, "BESSELI": {"a": "(x; n)", "d": "返回修正的贝赛耳函数 In(x)", "ad": "是求值函数的值!是贝赛耳函数的次序"}, "BESSELJ": {"a": "(x; n)", "d": "返回贝赛耳函数 Jn(x)", "ad": "是求值函数的值!是贝赛耳函数的次序"}, "BESSELK": {"a": "(x; n)", "d": "返回修正的贝赛耳函数 Kn(x)", "ad": "是求值函数的值!是函数的次序"}, "BESSELY": {"a": "(x; n)", "d": "返回贝赛耳函数 Yn(x)", "ad": "是求值函数的值!是函数的次序"}, "BIN2DEC": {"a": "(数字)", "d": "将二进制数转换为十进制", "ad": "是要转换的二进制数"}, "BIN2HEX": {"a": "(数字; [字符数])", "d": "将二进制数转换为十六进制", "ad": "是要转换的二进制数!是要使用的字符数"}, "BIN2OCT": {"a": "(数字; [字符数])", "d": "将二进制数转换为八进制", "ad": "是要转换的二进制数!是要使用的字符数"}, "BITAND": {"a": "(数字1; 数字2)", "d": "返回两个数字的按位\"与\"值", "ad": " 是您要计算的二进制数的十进制表示形式! 是您要计算的二进制数的十进制表示形式"}, "BITLSHIFT": {"a": "(数字; 左移位数)", "d": "返回按左移位数位左移的值数字", "ad": " 是您要计算的二进制数的十进制表示形式! 是您要将数字 1 左移的位数"}, "BITOR": {"a": "(数字1; 数字2)", "d": "返回两个数字的按位“或”值", "ad": " 是您要计算的二进制数的十进制表示形式! 是您要计算的二进制数的十进制表示形式"}, "BITRSHIFT": {"a": "(数字; 左移位数)", "d": "返回按左移位数位右移的值数字", "ad": " 是您要计算的二进制数的十进制表示形式! 是您要将数字 1 右移的位数"}, "BITXOR": {"a": "(数字1; 数字2)", "d": "返回两个数字的按位“异或”值", "ad": " 是您要计算的二进制数的十进制表示形式! 是您要计算的二进制数的十进制表示形式"}, "COMPLEX": {"a": "(实部系数; 虚部系数; [虚部后缀])", "d": "将实部系数和虚部系数转换为复数", "ad": "是复数的实部系数!是复数的虚部系数!是复数虚部的后缀"}, "CONVERT": {"a": "(数字; 数字单位; 结果单位)", "d": "将数字从一种度量体系转换为另一种度量体系", "ad": "是数字单位中要转换的值!是数字的单位!是结果的单位"}, "DEC2BIN": {"a": "(数字; [字符数])", "d": "将十进制数转换为二进制", "ad": "是要转换的十进制整数!是要使用的字符数"}, "DEC2HEX": {"a": "(数字; [字符数])", "d": "将十进制数转换为十六进制", "ad": "是要转换的十进制整数!是要使用的字符数"}, "DEC2OCT": {"a": "(数字; [字符数])", "d": "将十进制数转换为八进制", "ad": "是要转换的十进制整数!是要使用的字符数"}, "DELTA": {"a": "(数字1; [数字2])", "d": "测试两个数字是否相等", "ad": "是第一个数!是第二个数"}, "ERF": {"a": "(下界; [上界])", "d": "返回误差函数", "ad": "是整合 ERF 的下界!是整合 ERF 的上界"}, "ERF.PRECISE": {"a": "(X)", "d": "返回误差函数", "ad": "是整合 ERF.PRECISE 的下界"}, "ERFC": {"a": "(x)", "d": "返回补余误差函数", "ad": "是整合 ERF 的下界"}, "ERFC.PRECISE": {"a": "(X)", "d": "返回补余误差函数", "ad": "是整合 ERFC.PRECISE 的下界"}, "GESTEP": {"a": "(数字; [步长])", "d": "测试某个数字是否大于阈值", "ad": "是按步长测试的值!是阈值"}, "HEX2BIN": {"a": "(数字; [字符数])", "d": "将十六进制数转换为二进制", "ad": "是要转换的十六进制数!是要使用的字符数"}, "HEX2DEC": {"a": "(数字)", "d": "将十六进制数转换为十进制", "ad": "是要转换的十六进制数"}, "HEX2OCT": {"a": "(数字; [字符数])", "d": "将十六进制数转换为八进制", "ad": "是要转换的十六进制数!是要使用的字符数"}, "IMABS": {"a": "(复数)", "d": "返回复数的绝对值(模数)", "ad": "是求解绝对值的复数"}, "IMAGINARY": {"a": "(复数)", "d": "返回复数的虚部系数", "ad": "是求解虚部系数的复数"}, "IMARGUMENT": {"a": "(复数)", "d": "返回辐角 q (以弧度表示的角度)", "ad": "是求解辐角的复数"}, "IMCONJUGATE": {"a": "(复数)", "d": "返回复数的共轭复数", "ad": "是求解共轭复数的复数"}, "IMCOS": {"a": "(复数)", "d": "返回复数的余弦值", "ad": "是求解余弦值的复数"}, "IMCOSH": {"a": "(复数)", "d": "返回复数的双曲余弦值", "ad": " 准备求其双曲余弦值的复数"}, "IMCOT": {"a": "(复数)", "d": "返回复数的余切值", "ad": " 准备求其余切值的复数"}, "IMCSC": {"a": "(复数)", "d": "返回复数的余割值", "ad": " 准备求其余割值的复数"}, "IMCSCH": {"a": "(复数)", "d": "返回复数的双曲余割值", "ad": " 准备求其双曲余割值的复数"}, "IMDIV": {"a": "(复数1; 复数2)", "d": "返回两个复数之商", "ad": "是复数分子(被除数)!是复数分母(除数)"}, "IMEXP": {"a": "(复数)", "d": "返回复数的指数值", "ad": "是求解指数值的复数"}, "IMLN": {"a": "(复数)", "d": "返回复数的自然对数", "ad": "是求解自然对数的复数"}, "IMLOG10": {"a": "(复数)", "d": "返回以 10 为底的复数的对数", "ad": "是求解常用对数的复数"}, "IMLOG2": {"a": "(复数)", "d": "返回以 2 为底的复数的对数", "ad": "是求解以 2 为底的对数的复数"}, "IMPOWER": {"a": "(复数; 数字)", "d": "返回复数的整数幂", "ad": "是要进行幂运算的复数!是幂次"}, "IMPRODUCT": {"a": "(复数1; [复数2]; ...)", "d": "返回 1 到 255 个复数的乘积", "ad": "复数1, 复数2,... 是要相乘的 1 到 255 个复数"}, "IMREAL": {"a": "(复数)", "d": "返回复数的实部系数", "ad": "是求解实部系数的复数"}, "IMSEC": {"a": "(复数)", "d": "返回复数的正割值", "ad": " 准备求其正割值的复数"}, "IMSECH": {"a": "(复数)", "d": "返回复数的双曲正割值", "ad": " 准备求其双曲正割值的复数"}, "IMSIN": {"a": "(复数)", "d": "返回复数的正弦值", "ad": "是求解正弦值的复数"}, "IMSINH": {"a": "(复数)", "d": " 返回复数的双曲正弦值", "ad": " 准备求其双曲正弦值的复数"}, "IMSQRT": {"a": "(复数)", "d": "返回复数的平方根", "ad": "是求解平方根的复数"}, "IMSUB": {"a": "(复数1; 复数2)", "d": "返回两个复数的差值", "ad": "是从中减去 复数2 的复数!是从 复数1 上减去的复数"}, "IMSUM": {"a": "(复数1; [复数2]; ...)", "d": "返回复数的和", "ad": "是要相加的 1 到 255 个复数"}, "IMTAN": {"a": "(复数)", "d": "返回复数的正切值", "ad": " 准备求其正切值的复数"}, "OCT2BIN": {"a": "(数字; [字符数])", "d": "将八进制数转换为二进制", "ad": "是要转换的八进制数!是要使用的字符数"}, "OCT2DEC": {"a": "(数字)", "d": "将八进制数转换为十进制", "ad": "是要转换的八进制数"}, "OCT2HEX": {"a": "(数字; [字符数])", "d": "将八进制数转换为十六进制", "ad": "是要转换的八进制数!是要使用的字符数"}, "DAVERAGE": {"a": "(数据库; 字段; 条件)", "d": "计算满足给定条件的列表或数据库的列中数值的平均值。请查看“帮助”", "ad": "构成列表或数据库的单元格区域。数据库是一系列相关的数据的列表!用双引号括住的列标签，或是表示该列在列表中位置的数值!包含给定条件的单元格区域。该区域包括列标签及列标签下满足条件的单元格"}, "DCOUNT": {"a": "(数据库; 字段; 条件)", "d": "从满足给定条件的数据库记录的字段(列)中，计算数值单元格数目", "ad": "构成列表或数据库的单元格区域。数据库是一系列相关的数据!用双引号括住的列标签，或是表示该列在列表中位置的数值!包含指定条件的单元格区域。区域包括列标签及列标签下满足某个条件的单元格"}, "DCOUNTA": {"a": "(数据库; 字段; 条件)", "d": "对满足指定条件的数据库中记录字段(列)的非空单元格进行记数", "ad": "是构成列表或数据库的单元格区域。数据库是相关数据的列表!或是用双引号括住的列标签，或是表示该列在列表中位置的数字!是包含指定条件的单元格区域。区域包括列标签及列标签下满足某个条件的单元格"}, "DGET": {"a": "(数据库; 字段; 条件)", "d": "从数据库中提取符合指定条件且唯一存在的记录", "ad": "是构成列表或数据库的单元格区域。数据库是相关数据的列表!或是用双引号括住的列标签，或是表示该列在列表中位置的数字!是包含指定条件的单元格区域。区域包括列标签及列标签下满足某个条件的单元格"}, "DMAX": {"a": "(数据库; 字段; 条件)", "d": "返回满足给定条件的数据库中记录的字段(列)中数据的最大值", "ad": "构成列表或数据库的单元格区域。数据库是一系列相关的数据列表!用双引号括住的列标签，或是表示该列在列表中位置的数值!包含数据库条件的单元格区域。该区域包括列标签及列标签下满足条件的单元格"}, "DMIN": {"a": "(数据库; 字段; 条件)", "d": "返回满足给定条件的数据库中记录的字段(列)中数据的最小值", "ad": "构成列表或数据库的单元格区域。数据库是一系列相关的数据列表!用双引号括住的列标签，或是表示该列在列表中位置的数值!包含数据库条件的单元格区域。该区域包括列标签及列标签下满足条件的单元格"}, "DPRODUCT": {"a": "(数据库; 字段; 条件)", "d": "与满足指定条件的数据库中记录字段(列)的值相乘", "ad": "是构成列表或数据库的单元格区域。数据库是相关数据的列表!或是用双引号括住的列标签，或是表示该列在列表中位置的数字!是包含指定条件的单元格区域。区域包括列标签及列标签下满足某个条件的单元格"}, "DSTDEV": {"a": "(数据库; 字段; 条件)", "d": "根据所选数据库条目中的样本估算数据的标准偏差", "ad": "构成列表或数据库的单元格区域。数据库是一系列相关的数据列表!用双引号括住的列标签，或是表示该列在列表中位置的数值!包含数据库条件的单元格区域。该区域包括列标签及列标签下满足条件的单元格"}, "DSTDEVP": {"a": "(数据库; 字段; 条件)", "d": "以数据库选定项作为样本总体，计算数据的标准偏差", "ad": "是构成列表或数据库的单元格区域。数据库是相关数据的列表!或是用双引号括住的列标签，或是表示该列在列表中位置的数字!是包含指定条件的单元格区域。区域包括列标签及列标签下满足某个条件的单元格"}, "DSUM": {"a": "(数据库; 字段; 条件)", "d": "求满足给定条件的数据库中记录的字段(列)数据的和", "ad": "构成列表或数据库的单元格区域。数据库是一系列相关的数据!用双引号括住的列标签，或是表示该列在列表中位置的数值!包含指定条件的单元格区域。区域包括列标签及列标签下满足某个条件的单元格"}, "DVAR": {"a": "(数据库; 字段; 条件)", "d": "根据所选数据库条目中的样本估算数据的方差", "ad": "构成列表或数据库的单元格区域。数据库是一系列相关的数据!用双引号括住的列标签，或是表示该列在列表中位置的数值!包含数据库条件的单元格区域。该区域包括列标签及列标签下满足条件的单元格"}, "DVARP": {"a": "(数据库; 字段; 条件)", "d": "以数据库选定项作为样本总体，计算数据的总体方差", "ad": "是构成列表或数据库的单元格区域。数据库是相关数据的列表!或是用双引号括住的列标签，或是表示该列在列表中位置的数字!是包含指定条件的单元格区域。区域包括列标签及列标签下满足某个条件的单元格"}, "CHAR": {"a": "(数字)", "d": "根据本机中的字符集，返回由代码数字指定的字符", "ad": "介于 1 到 255 之间的任一数字，该数字对应着您要返回的字符"}, "CLEAN": {"a": "(文本)", "d": "删除文本中的所有非打印字符", "ad": "任何想要从中删除非打印字符的工作表信息"}, "CODE": {"a": "(文本)", "d": "返回文本字符串第一个字符在本机所用字符集中的数字代码", "ad": "要取第一个字符代码的字符串"}, "CONCATENATE": {"a": "(文本1; [文本2]; ...)", "d": "将多个文本字符串合并成一个", "ad": "是 1 到 255 个要合并的文本字符串。可以是字符串、数字或对单个单元格的引用"}, "CONCAT": {"a": "(文本1; ...)", "d": "连接列表或文本字符串区域", "ad": "是要与单个文本字符串联接的 1 到 254 个文本字符串或区域"}, "DOLLAR": {"a": "(数字; [小数位数])", "d": "用货币格式将数值转换成文本字符", "ad": "一个数值、一个对含有数值的单元格的引用、或一个可导出数值的公式!指定小数点右边的位数。如果必要，数字将四舍五入；如果忽略，小数位数= 2"}, "EXACT": {"a": "(文本1; 文本2)", "d": "比较两个字符串是否完全相同(区分大小写)。返回 TRUE 或 FALSE", "ad": "第一个字符串!第二个字符串"}, "FIND": {"a": "(查找文本; 目标文本; [起始位置])", "d": "返回一个字符串在另一个字符串中出现的起始位置(区分大小写)", "ad": "要查找的字符串。用双引号(表示空串)可匹配目标文本中的第一个字符，不能使用通配符!要在其中进行搜索的字符串!起始搜索位置，目标文本 中第一个字符的位置为 1。如果忽略，起始位置 = 1"}, "FINDB": {"a": "(查找文本; 目标文本; [起始位置])", "d": "用于在第二个文本串中定位第一个文本串，并返回第一个文本串的起始位置的值，该值从第二个文本串的第一个字符算起。适用于使用双字节字符集 (DBCS) 的语言。支持 DBCS 的语言包括日语、中文、以及朝鲜语。", "ad": "要查找的字符串。用双引号(表示空串)可匹配目标文本中的第一个字符，不能使用通配符!要在其中进行搜索的字符串!起始搜索位置，目标文本 中第一个字符的位置为 1。如果忽略，起始位置 = 1"}, "FIXED": {"a": "(数字; [小数位数]; [不显示逗号])", "d": "用定点小数格式将数值舍入成特定位数并返回带或不带逗号的文本", "ad": "准备舍入并转化为文本字符的数值!指小数点右边的位数。如果忽略，小数位数 = 2!一个逻辑值，指定在返回文本中是否显示逗号。为 TRUE 时显示逗号；忽略或为 FALSE 时则不显示逗号"}, "LEFT": {"a": "(文本; [字符数])", "d": "从一个文本字符串的第一个字符开始返回指定个数的字符", "ad": "要提取字符的字符串!要 LEFT 提取的字符数；如果忽略，为 1"}, "LEFTB": {"a": "(文本; [字符数])", "d": "基于所指定的字节数返回文本字符串中的第一个或前几个字符。适用于使用双字节字符集 (DBCS) 的语言。支持 DBCS 的语言包括日语、中文、以及朝鲜语。", "ad": "要提取字符的字符串!要 LEFTB 提取的字符数；如果忽略，为 1"}, "LEN": {"a": "(文本)", "d": "返回文本字符串中的字符个数", "ad": "要计算长度的文本字符串；包括空格"}, "LENB": {"a": "(文本)", "d": "返回文本字符串中用于代表字符的字节数。适用于使用双字节字符集 (DBCS) 的语言。支持 DBCS 的语言包括日语、中文、以及朝鲜语。", "ad": "要计算长度的文本字符串；包括空格"}, "LOWER": {"a": "(文本)", "d": "将一个文本字符串的所有字母转换为小写形式", "ad": "要对其进行转换的字符串。其中不是英文字母的字符不变"}, "MID": {"a": "(文本; 起始位置; 字符数)", "d": "从文本字符串中指定的起始位置起返回指定长度的字符", "ad": "准备从中提取字符串的文本字符串!准备提取的第一个字符的位置。文本 中第一个字符为 1!指定所要提取的字符串长度"}, "MIDB": {"a": "(文本; 起始位置; 字符数)", "d": "根据您指定的字节数，返回文本字符串中从指定位置开始的特定数目的字符。适用于使用双字节字符集 (DBCS) 的语言。支持 DBCS 的语言包括日语、中文、以及朝鲜语。", "ad": "准备从中提取字符串的文本字符串!准备提取的第一个字符的位置。文本 中第一个字符为 1!指定所要提取的字符串长度"}, "NUMBERVALUE": {"a": "(文本; [小数点分隔符]; [组分隔符])", "d": "按独立于区域设置的方式将文本转换为数字", "ad": " 代表要转换的数字的字符串! 用作字符串的小数点分隔符的字符! 用作字符串的组分隔符的字符"}, "PROPER": {"a": "(文本)", "d": "将一个文本字符串中各英文单词的第一个字母转换成大写，将其他字符转换成小写", "ad": "所要转换的字符串数据，可以是包含在一对双引号中的字符串、能够返回字符串的公式，或者是对文本单元格的引用"}, "REPLACE": {"a": "(旧文本; 起始位置; 字符数; 新文本)", "d": "将一个字符串中的部分字符用另一个字符串替换", "ad": "要进行字符替换的文本!要替换为新文本的字符在旧文本中的位置!要从旧文本中替换的字符个数!用来对旧文本中指定字符串进行替换的字符串"}, "REPLACEB": {"a": "(旧文本; 起始位置; 字符数; 新文本)", "d": "使用其他文本字符串并根据所指定的字节数替换某文本字符串中的部分文本。适用于使用双字节字符集 (DBCS) 的语言。支持 DBCS 的语言包括日语、中文、以及朝鲜语。", "ad": "要进行字符替换的文本!要替换为新文本的字符在旧文本中的位置!要从旧文本中替换的字符个数!用来对旧文本中指定字符串进行替换的字符串"}, "REPT": {"a": "(文本; 重复次数)", "d": "根据指定次数重复文本。可用 RPET 在一个单元格中重复填写一个文本字符串", "ad": "要重复文本!文本的重复次数(正数)"}, "RIGHT": {"a": "(文本; [字符数])", "d": "从一个文本字符串的最后一个字符开始返回指定个数的字符", "ad": "要提取字符的字符串!要提取的字符数；如果忽略，为 1"}, "RIGHTB": {"a": "(文本; [字符数])", "d": "根据所指定的字节数返回文本字符串中最后一个或多个字符。适用于使用双字节字符集 (DBCS) 的语言。支持 DBCS 的语言包括日语、中文、以及朝鲜语。", "ad": "要提取字符的字符串!要提取的字符数；如果忽略，为 1"}, "SEARCH": {"a": "(查找文本; 目标文本; [起始位置])", "d": "返回一个指定字符或文本字符串在字符串中第一次出现的位置，从左到右查找(忽略大小写)", "ad": "要查找的字符串。可以使用 ? 和 * 作为通配符；如果要查找 ? 和 * 字符，可使用 ~? 和 ~*!用来搜索查找文本的父字符串!数字值，用以指定从被搜索字符串左侧第几个字符开始查找。如果忽略，则为 1"}, "SEARCHB": {"a": "(查找文本; 目标文本; [起始位置])", "d": "函数可在第二个文本字符串中查找第一个文本字符串，并返回第一个文本字符串的起始位置的编号，该编号从第二个文本字符串的第一个字符算起。适用于使用双字节字符集 (DBCS) 的语言。支持 DBCS 的语言包括日语、中文、以及朝鲜语。", "ad": "要查找的字符串。可以使用 ? 和 * 作为通配符；如果要查找 ? 和 * 字符，可使用 ~? 和 ~*!用来搜索 查找文本 的父字符串!数字值，用以指定从被搜索字符串左侧第几个字符开始查找。如果忽略，则为 1"}, "SUBSTITUTE": {"a": "(文本; 旧文本; 新文本; [替换实例编号])", "d": "将字符串中的部分字符串以新字符串替换", "ad": "包含有要替换字符的字符串或文本单元格引用!要被替换的字符串。如果原有字符串中的大小写与新字符串中的大小写不匹配的话，将不进行替换!用于替换旧文本的新字符串!若指定的字符串旧文本在父字符串中出现多次，则用本参数指定要替换第几个。如果省略，则全部替换"}, "T": {"a": "(值)", "d": "检测给定值是否为文本，如果是文本按原样返回，如果不是文本则返回双引号(空文本)", "ad": "要检测的值"}, "TEXT": {"a": "(值; 文字形式)", "d": "根据指定的数字格式将数值转成文本", "ad": "数字、能够求值数值的公式，或者对数值单元格的引用!文字形式的数字格式，文字形式来自于“单元格格式”对话框“数字”选项卡的“分类”框"}, "TEXTJOIN": {"a": "(分隔符; 忽略空单元格; 文本1; ...)", "d": "使用分隔符连接列表或文本字符串区域", "ad": "要在每个文本项之间插入的字符或字符串!如果为 TRUE (默认)，则忽略空单元格!为要联接的 1 到 252 个文本字符串或区域"}, "TRIM": {"a": "(文本)", "d": "删除字符串中多余的空格，但会在英文字符串中保留一个作为词与词之间分隔的空格", "ad": "要删除空格的字符串"}, "UNICHAR": {"a": "(数字)", "d": "返回由给定数值引用的 Unicode 字符", "ad": " 是代表字符的 Unicode 数字"}, "UNICODE": {"a": "(文本)", "d": "返回对应于文本的第一个字符的数字(代码点)", "ad": " 是需要其 Unicode 值的字符"}, "UPPER": {"a": "(文本)", "d": "将文本字符串转换成字母全部大写形式", "ad": "要转换成大写的文本字符串"}, "VALUE": {"a": "(文本)", "d": "将一个代表数值的文本字符串转换成数值", "ad": "带双引号的文本，或是一个单元格引用。该单元格中有要被转换的文本"}, "AVEDEV": {"a": "(数字1; [数字2]; ...)", "d": "返回一组数据点到其算术平均值的绝对偏差的平均值。参数可以是数字、名称、数组或包含数字的引用", "ad": "是要计算绝对偏差平均值的 1 到 255 个数值"}, "AVERAGE": {"a": "(数字1; [数字2]; ...)", "d": "返回其参数的算术平均值；参数可以是数值或包含数值的名称、数组或引用", "ad": "是用于计算平均值的 1 到 255 个数值参数"}, "AVERAGEA": {"a": "(值1; [值2]; ...)", "d": "返回所有参数的算术平均值。字符串和 FALSE 相当于 0；TRUE 相当于 1。参数可以是数值、名称、数组或引用", "ad": "是要求平均值的 1 到 255 个参数"}, "AVERAGEIF": {"a": "(范围; 条件; [平均值单元格范围])", "d": "查找给定条件指定的单元格的平均值(算术平均值)", "ad": "是要进行计算的单元格区域!是数字、表达式或文本形式的条件，它定义了用于查找平均值的单元格范围!是用于查找平均值的实际单元格。如果省略，则使用区域中的单元格"}, "AVERAGEIFS": {"a": "(平均值单元格范围; 条件范围; 条件; ...)", "d": "查找一组给定条件指定的单元格的平均值(算术平均值)", "ad": "是用于查找平均值的实际单元格!是要为特定条件计算的单元格区域!是数字、表达式或文本形式的条件，它定义了用于查找平均值的单元格范围"}, "BETADIST": {"a": "(x; alpha; beta; [A]; [B])", "d": "返回累积 beta 分布的概率密度函数", "ad": "是函数计算点 A 和 B 之间的值!是此分布的一个参数，必须大于 0!是此分布的一个参数，必须大于 0!是间隔 x 的可选下界。如果忽略，A=0!是间隔 x 的可选上界。如果忽略，B=1"}, "BETAINV": {"a": "(概率; alpha; beta; [A]; [B])", "d": "返回累积 beta 分布的概率密度函数区间点 (BETADIST)", "ad": "是与 beta 分布相关的概率!是分布的参数，必须大于 0!是分布的参数，必须大于 0!是间隔 x 的可选下界。如果忽略，A=0!是间隔 x 的可选上界，如果忽略，B=1"}, "BETA.DIST": {"a": "(x; alpha; beta; 累积; [A]; [B])", "d": "返回 beta 概率分布函数", "ad": "用来进行概率分布计算的值，须居于可选性上下界(A 和 B)之间!此分布的一个参数，必须大于 0!此分布的一个参数，必须大于 0!逻辑值，决定函数的形式。累积分布函数，使用 TRUE；概率密度函数，使用 FALSE!数值 x 的可选下界。如果忽略，A=0!数值 x 的可选上界。如果忽略，B=1"}, "BETA.INV": {"a": "(概率; alpha; beta; [A]; [B])", "d": "返回具有给定概率的累积 beta 分布的区间点", "ad": "Beta 分布的概率!Beta 分布的参数，必须大于 0!Beta 分布的参数，必须大于 0!数值 x 的可选下界。如果忽略，A=0!数值 x 的可选上界。如果忽略，B=1"}, "BINOMDIST": {"a": "(成功次数; 独立实验次数; 成功概率; 累积)", "d": "返回一元二项式分布的概率", "ad": "是实验成功次数!是独立实验次数!是一次实验中成功的概率!是逻辑值，决定函数的形式。累积分布函数，使用 TRUE；概率密度函数，使用 FALSE"}, "BINOM.DIST": {"a": "(成功次数; 独立实验次数; 成功概率; 累积)", "d": "返回一元二项式分布的概率", "ad": "实验成功次数!独立实验次数!一次实验中成功的概率!逻辑值，决定函数的形式。累积分布函数，使用 TRUE；概率密度函数，使用 FALSE"}, "BINOM.DIST.RANGE": {"a": "(独立实验次数; 成功概率; 试验成功次数; [数字_s2])", "d": "使用二项式分布返回试验结果的概率", "ad": " 是独立试验次数! 是每次试验成功的概率! 是试验成功次数! 如果提供此函数，则返回成功试验次数介于试验成功次数和数字_s2之间的概率"}, "BINOM.INV": {"a": "(贝努利试验次数; 成功概率; alpha)", "d": "返回一个数值，它是使得累积二项式分布的函数值大于或等于临界值 α 的最小整数", "ad": "贝努利试验次数!一次试验中成功的概率，介于 0 与 1 之间，含 0 与 1!临界值，介于 0 与 1 之间，含 0 与 1"}, "CHIDIST": {"a": "(x; 自由度)", "d": "返回 χ2 分布的右尾概率", "ad": "是用来计算 χ2 分布右尾概率的数值，非负数!是自由度的数值，介于 1 与 10^10 之间，不含 10^10"}, "CHIINV": {"a": "(概率; 自由度)", "d": "返回具有给定概率的右尾 χ2 分布的区间点", "ad": "χ2 分布的概率，介于 0 与 1 之间，含 0 与 1!自由度，介于 1 与 10^10 之间，不含 10^10"}, "CHITEST": {"a": "(实际范围; 预期范围)", "d": "返回独立性检验的结果: 针对统计和相应的自由度返回卡方分布值", "ad": "为包含观察值的数据范围，用于检验预期值!为一数据范围，其内容为各行总和乘以各列总和后的值，再除以全部值总和的比率"}, "CHISQ.DIST": {"a": "(x; 自由度; 累积)", "d": "返回 χ2 分布的左尾概率", "ad": "用来计算 χ2 分布左尾概率的数值，非负数值!自由度，介于 1 与 10^10 之间，不含 10^10!逻辑值，当函数为累积分布函数时，返回值为 TRUE；当为概率密度函数时，返回值为 FALSE"}, "CHISQ.DIST.RT": {"a": "(x; 自由度)", "d": "返回 χ2 分布的右尾概率", "ad": "用来计算 χ2 分布右尾概率的数值，非负数值!自由度，介于 1 与 10^10 之间，不含 10^10"}, "CHISQ.INV": {"a": "(概率; 自由度)", "d": "返回具有给定概率的左尾 χ2 分布的区间点", "ad": "χ2 分布的概率，介于 0 与 1 之间，含 0 与 1!自由度，介于 1 与 10^10 之间，不含 10^10"}, "CHISQ.INV.RT": {"a": "(概率; 自由度)", "d": "返回具有给定概率的右尾 χ2 分布的区间点", "ad": "χ2 分布的概率，介于 0 与 1 之间，含 0 与 1!自由度，介于 1 与 10^10 之间，不含 10^10"}, "CHISQ.TEST": {"a": "(实际范围; 预期范围)", "d": "返回独立性检验的结果: 针对统计和相应的自由度返回卡方分布值", "ad": "为包含观察值的数据范围，用于检验预期值!为一数据范围，其内容为各行总和乘以各列总和后的值，再除以全部值总和的比率"}, "CONFIDENCE": {"a": "(alpha; 标准偏差; 尺寸)", "d": "使用正态分布，返回总体平均值的置信区间", "ad": "是用来计算可信度的显著性水平,一个大于 0 小于 1 的数值!是假设为已知的数据范围的总体标准方差。标准偏差必须大于 0!是样本容量"}, "CONFIDENCE.NORM": {"a": "(alpha; 标准偏差; 尺寸)", "d": "使用正态分布，返回总体平均值的置信区间", "ad": "用来计算可信度的显著性水平，一个大于 0 小于 1 的数值!假设为已知的总体标准方差。标准偏差必须大于 0!样本容量"}, "CONFIDENCE.T": {"a": "(alpha; 标准偏差; 尺寸)", "d": "使用学生 T 分布，返回总体平均值的置信区间", "ad": "用来计算可信度的显著性水平，一个大于 0 小于 1 的数值!假设为已知的总体标准方差。标准偏差必须大于 0!样本容量"}, "CORREL": {"a": "(数组1; 数组2)", "d": "返回两组数值的相关系数", "ad": "第一组数值单元格区域。值应为数值、名称、数组或包含数值的引用!第二组数值单元格区域。值应为数值、名称、数组或包含数值的引用"}, "COUNT": {"a": "(值1; [值2]; ...)", "d": "计算区域中包含数字的单元格的个数", "ad": "是 1 到 255 个参数，可以包含或引用各种不同类型的数据，但只对数字型数据进行计数"}, "COUNTA": {"a": "(值1; [值2]; ...)", "d": "计算区域中非空单元格的个数", "ad": "是 1 到 255 个参数，代表要进行计数的值和单元格。值可以是任意类型的信息"}, "COUNTBLANK": {"a": "(范围)", "d": "计算某个区域中空单元格的数目", "ad": "指要计算空单元格数目的区域"}, "COUNTIF": {"a": "(范围; 条件)", "d": "计算某个区域中满足给定条件的单元格数目", "ad": "要计算其中非空单元格数目的区域!以数字、表达式或文本形式定义的条件"}, "COUNTIFS": {"a": "(条件范围; 条件; ...)", "d": "统计一组给定条件所指定的单元格数", "ad": "是要为特定条件计算的单元格区域!是数字、表达式或文本形式的条件，它定义了单元格统计的范围"}, "COVAR": {"a": "(数组1; 数组2)", "d": "返回协方差，即每对变量的偏差乘积的均值", "ad": "第一组整数单元格区域，必须为数值、数组或包含数值的引用!第二组整数单元格区域，必须为数值、数组或包含数值的引用"}, "COVARIANCE.P": {"a": "(数组1; 数组2)", "d": "返回总体协方差，即两组数值中每对变量的偏差乘积的平均值", "ad": "第一组整数单元格区域，必须为数值、数组或包含数值的引用!第二组整数单元格区域，必须为数值、数组或包含数值的引用"}, "COVARIANCE.S": {"a": "(数组1; 数组2)", "d": "返回样本协方差，即两组数值中每对变量的偏差乘积的平均值", "ad": "第一组整数单元格区域，必须为数值、数组或包含数值的引用!第二组整数单元格区域，必须为数值、数组或包含数值的引用"}, "CRITBINOM": {"a": "(贝努利试验次数; 成功概率; alpha)", "d": "返回一个数值，它是使得累积二项式分布的函数值大于等于临界值 α 的最小整数", "ad": "是贝努利试验次数!是一次试验中成功的概率，介于 0 与 1 之间，含 0 与 1!是临界值，介于 0 与 1 之间，含 0 与 1"}, "DEVSQ": {"a": "(数字1; [数字2]; ...)", "d": "返回各数据点与数据均值点之差(数据偏差)的平方和", "ad": "是用于计算偏差平方和的 1 到 255 个参数，或者是一个数组或数组引用"}, "EXPONDIST": {"a": "(x; lambda; 累积)", "d": "返回指数分布", "ad": "是函数的值，非负数值!是参数值，正数!是逻辑值，用于指定返回的函数形式: 当函数为累积分布函数时，返回值为 TRUE；当为概率密度函数时，返回值为 FALSE"}, "EXPON.DIST": {"a": "(x; lambda; 累积)", "d": "返回指数分布", "ad": "用于指数分布函数计算的区间点，非负数值!指数分布函数的参数，正数!逻辑值，当函数为累积分布函数时，返回值为 TRUE；当为概率密度函数时，返回值为 FALSE，指定使用何种形式的指数函数"}, "FDIST": {"a": "(x; 自由度1; 自由度2)", "d": "返回两组数据的(右尾) F 概率分布(散布程度)", "ad": "是用来计算函数的值，非负值!是分子的自由度，大小介于 1 和 10^10 之间，不包含 10^10!是分母的自由度，大小介于 1 和 10^10 之间，不包含 10^10"}, "FINV": {"a": "(概率; 自由度1; 自由度2)", "d": "返回(右尾) F 概率分布的逆函数值，如果 p = FDIST(x,...)，那么 FINV(p,...) = x", "ad": "F 累积分布的概率值，介于 0 与 1 之间，含 0 与 1!分子的自由度，介于 1 与 10^10 之间，不包含 10^10!分母的自由度，介于 1 与 10^10 之间，不包含 10^10"}, "FTEST": {"a": "(数组1; 数组2)", "d": "返回 F 检验的结果，F 检验返回的是当 数组1 和 数组2 的方差无明显差异时的双尾概率", "ad": "是第一个数组或数据区域，可以是数值、名称、数组或者是包含数值的引用(将忽略空白对象)!是第二个数组或数据区域，可以是数值、名称、数组或者是包含数值的引用(将忽略空白对象)"}, "F.DIST": {"a": "(x; 自由度1; 自由度2; 累积)", "d": "返回两组数据的(左尾) F 概率分布", "ad": "用来计算概率分布的区间点，非负数值!分子的自由度，大小介于 1 和 10^10 之间，不含 10^10!分母的自由度，大小介于 1 和 10^10 之间，不含 10^10!逻辑值，当函数为累积分布函数时，返回值为 TRUE；当为概率密度函数时，返回值为 FALSE"}, "F.DIST.RT": {"a": "(x; 自由度1; 自由度2)", "d": "返回两组数据的(右尾) F 概率分布", "ad": "用来计算概率分布的区间点，非负数值!分子的自由度，大小介于 1 和 10^10 之间，不含 10^10!分母的自由度，大小介于 1 和 10^10 之间，不含 10^10"}, "F.INV": {"a": "(概率; 自由度1; 自由度2)", "d": "返回(左尾) F 概率分布的逆函数值，如果 p = F.DIST(x,...)，那么 FINV(p,...) = x", "ad": "F 累积分布的概率值，介于 0 与 1 之间，含 0 与 1!分子的自由度，大小介于 1 和 10^10 之间，不包括 10^10!分母的自由度，大小介于 1 和 10^10 之间，不包括 10^10"}, "F.INV.RT": {"a": "(概率; 自由度1; 自由度2)", "d": "返回(右尾) F 概率分布的逆函数值，如果 p = F.DIST.RT(x,...)，那么 F.INV.RT(p,...) = x", "ad": "F 累积分布的概率值，介于 0 与 1 之间，含 0 与 1!分子的自由度，大小介于 1 和 10^10 之间，不含 10^10!分母的自由度，大小介于 1 和 10^10 之间，不含 10^10"}, "F.TEST": {"a": "(数组1; 数组2)", "d": "返回 F 检验的结果，F 检验返回的是当数组1和数组2 的方差无明显差异时的双尾概率", "ad": "是第一个数组或数据区域，可以是数值、名称、数组或者是包含数值的引用(将忽略空白对象)!是第二个数组或数据区域，可以是数值、名称、数组或者是包含数值的引用(将忽略空白对象)"}, "FISHER": {"a": "(x)", "d": "返回 Fisher 变换值", "ad": "需要变换的数值，介于 -1 与 1 之间，不含 -1 与 1"}, "FISHERINV": {"a": "(y)", "d": "返回 Fisher 逆变换值，如果 y = FISHER(x)，那么 FISHERINV(y) = x", "ad": "需要进行逆变换的数值"}, "FORECAST": {"a": "(x; Known_y's; Known_x's)", "d": "根据现有的值所产生出的等差序列来计算或预测未来值", "ad": "为所要预测的值的数据点，且必须是数值!是相依的数组或数值数据范围!是独立的数组或数值数据范围。Known_x's 的方差不得为零"}, "FORECAST.ETS": {"a": "(目标日期; 值; 时间线; [季节性]; [数据完整]; [聚合])", "d": "使用指数平滑方法返回特定未来目标日期的预测值。", "ad": "是 电子表格编辑器预测值的数据点。它应该执行日程表中的值模式。!是您正在预测的数组或数值数据范围。!是独立的数组或数值数据范围。日程表中的日期之间必须有一致的间隔，不能为零。!是指明季节性模式长度的可选数值。默认值 1 表示自动检测季节性。!是用于处理缺失值的可选值。默认值 1 将缺失值替换为插值，0 将它们替换为零。!是用于聚合时间戳相同的多个值的可选数值。如果为空，电子表格编辑器将聚合值。"}, "FORECAST.ETS.CONFINT": {"a": "(目标日期; 值; 时间线; [可信度]; [季节性]; [数据完整]; [数据完整])", "d": "返回指定目标日期预测值的置信区间。", "ad": "是 电子表格编辑器预测值的数据点。它应该执行日程表中的值模式。!是您正在预测的数组或数值数据范围。!是独立的数组或数值数据范围。日程表中的日期之间必须有一致的间隔，不能为零。!是介于 0 和 1 之间的数字，显示所计算的置信区间的可信度。默认值为 0.95。!是指明季节性模式长度的可选数值。默认值 1 表示自动检测季节性。!是用于处理缺失值的可选值。默认值 1 将缺失值替换为插值，0 将它们替换为零。!是用于聚合时间戳相同的多个值的可选数值。如果为空，电子表格编辑器将聚合值。"}, "FORECAST.ETS.SEASONALITY": {"a": "(值; 时间线; [数据完整]; [数据完整])", "d": "返回应用针对指定时间序列检测到的重复模式的长度。", "ad": "是正在预测的数组或数值数据范围!是独立的数组或数值数据范围。日程表中的日期之间必须有一致的间隔，不能为零。!是用于处理缺失值的可选值。默认值 1 将缺失值替换为插值，0 将它们替换为零。!是用于聚合时间戳相同的多个值的可选数值。如果为空，电子表格编辑器将聚合值。"}, "FORECAST.ETS.STAT": {"a": "(值; 时间线; 数据类型; [季节性]; [数据完整]; [数据完整])", "d": "对预测返回请求的统计信息。", "ad": "是正在预测的数组或数值数据范围!是独立的数组或数值数据范围。日程表中的日期之间必须有一致的间隔，不能为零。!是介于 1 和 8 之间的数字，指明 电子表格编辑器将对计算的预测返回哪些统计信息。!是指明季节性模式长度的可选数值。默认值 1 表示自动检测季节性。!是用于处理缺失值的可选值。默认值 1 将缺失值替换为插值，0 将它们替换为零。!是用于聚合时间戳相同的多个值的可选数值。如果为空，电子表格编辑器将聚合值。"}, "FORECAST.LINEAR": {"a": "(x; known_y's; known_x's)", "d": "根据现有的值所产生出的等差序列来计算或预测未来值", "ad": "为所要预测的值的数据点，且必须是数值!是相依的数组或数值数据范围!是独立的数组或数值数据范围。Known_x's 的方差不得为零"}, "FREQUENCY": {"a": "(数据数组; 数据接收区间)", "d": "以一列垂直数组返回一组数据的频率分布", "ad": "用来计算频率的数组，或对数组单元区域的引用(空格及字符串忽略)!数据接收区间，为一数组或对数组区域的引用，设定对数据数组进行频率计算的分段点"}, "GAMMA": {"a": "(x)", "d": "返回伽玛函数值", "ad": " 是您要为其计算伽玛的值"}, "GAMMADIST": {"a": "(x; alpha; beta; 累积)", "d": "返回 γ 分布函数", "ad": "是用来计算 γ 分布的值，非负数值!是 γ 分布的一个参数，正数!是 γ 分布的一个参数，正数。当 beta=1 时，GAMMADIST 返回标准 γ 分布函数!决定函数形式的逻辑值: 返回累积分布函数时，等于 TRUE；返回概率密度函数时，等于 FALSE 或忽略"}, "GAMMA.DIST": {"a": "(x; alpha; beta; 累积)", "d": "返回 γ 分布函数", "ad": "用来计算 γ 分布的区间点，非负数值!γ 分布的一个参数，正数!γ 分布的一个参数，正数。如果 beta=1，GAMMADIST 返回标准 γ 分布函数!决定函数形式的逻辑值: 返回累积分布函数时，等于 TRUE；返回概率密度函数时，等于 FALSE 或忽略"}, "GAMMAINV": {"a": "(概率; alpha; beta)", "d": "返回具有给定概率的 γ 累积分布的区间点: 如果 p = GAMMADIST(x,...) 则 GAMMAINV(p,...) = x", "ad": "是 γ 分布的概率值，介于 0 与 1 之间，含 0 与 1!是 γ 分布的一个参数，正数!是 γ 分布的一个参数，为一个正数。当 beta= 1 时，GAMMAINV 返回标准 γ 分布的区间点"}, "GAMMA.INV": {"a": "(概率; alpha; beta)", "d": "返回具有给定概率的 γ 累积分布的区间点", "ad": "γ 分布的概率值，介于 0 与 1 之间，含 0 与 1!γ 分布的一个参数，正数!γ 分布的一个参数，为一个正数。当 beta= 1 时，GAMMAINV 返回标准 γ 分布的逆"}, "GAMMALN": {"a": "(x)", "d": "返回 γ 函数的自然对数", "ad": "一个要计算 GAMMALN 的正数"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "返回 γ 函数的自然对数", "ad": "一个要计算 GAMMALN.PRECISE 的正数"}, "GAUSS": {"a": "(x)", "d": "返回比标准正态累积分布小 0.5 的值", "ad": " 是要计算其分布的值"}, "GEOMEAN": {"a": "(数字1; [数字2]; ...)", "d": "返回一正数数组或数值区域的几何平均数", "ad": "是用于计算几何平均数的 1 到 255 个数值、名称、数组，或者是对数值的引用"}, "GROWTH": {"a": "(known_y's; [known_x's]; [new_x's]; [const])", "d": "返回指数回归拟合曲线的一组纵坐标值(y 值)", "ad": "满足指数回归拟合曲线 y=b*m^x 的一组已知的 y 值!满足指数回归拟合曲线 y=b*m^x 的一组已知的 x 值，个数与 y 值相同。为可选参数!一组新 x 值，通过 GROWTH 函数返回各自对应的 y 值!逻辑值，指定是否将系数 b 强制设为 1。如果为 FALSE 或忽略，b 等于 1；如果为 TRUE，b 值按普通方法计算"}, "HARMEAN": {"a": "(数字1; [数字2]; ...)", "d": "返回一组正数的调和平均数: 所有参数倒数平均值的倒数", "ad": "是用于计算调和平均数的 1 到 255 个数值、名称、数组，或者是数值的引用"}, "HYPGEOM.DIST": {"a": "(样本成功数目; 样本容量; 样本总体成功数目; 样本总体容量; 累积)", "d": "返回超几何分布", "ad": "样本中成功的数目!样本容量!样本总体中成功的数目!样本总体的容量!逻辑值，决定函数的形式。累积分布函数，使用 TRUE；概率密度函数，使用 FALSE"}, "HYPGEOMDIST": {"a": "(样本成功数目; 样本容量; 样本总体成功数目; 样本总体容量)", "d": "返回超几何分布", "ad": "是样本中成功的数目!是样本容量!是样本总体中成功的数目!是样本总体的容量"}, "INTERCEPT": {"a": "(known_y's; known_x's)", "d": "根据已知的 x 值及 y 值所绘制出来的最佳回归线，计算出直线将于 y 轴交汇的点", "ad": "为所观测的因变量集或数据。可以是数值、名称、数组，或者是数值的引用!为所观测的自变量集合或数据。可以是数值、名称、数组，或者是数值的引用"}, "KURT": {"a": "(数字1; [数字2]; ...)", "d": "返回一组数据的峰值", "ad": "是用于峰值计算的 1 到 255 个参数，可以是数值、名称、数组，或者是数值的引用"}, "LARGE": {"a": "(数组; k)", "d": "返回数据组中第 k 个最大值。例如，第五个最大值", "ad": "用来计算第 k 个最大值点的数值数组或数值区域!所要返回的最大值点在数组或数据区中的位置(从最大值开始)"}, "LINEST": {"a": "(known_y's; [known_x's]; [const]; [stats])", "d": "返回线性回归方程的参数", "ad": "满足线性拟合直线 y=mx+b 的一组已知的 y 值!满足线性拟合直线 y=mx+b 的一组已知的 x 值，为可选的参数!逻辑值，用以指定是否要强制常数 b 为 0。如果 Const=TRUE 或忽略，b 取正常值；如果 Const=FALSE，b 等于 0!逻辑值，如果返回附加的回归统计值，返回 TRUE；如果返回系数 m 和常数 b，返回 FALSE 或忽略"}, "LOGEST": {"a": "(known_y's; [known_x's]; [const]; [stats])", "d": "返回指数回归拟合曲线方程的参数", "ad": "一组满足指数回归曲线 y=b*m^x 的 y 值!一组满足指数回归曲线 y=b*m^x 的 x 值，为可选参数!逻辑值: 如果 Const=TRUE 或省略，常数 b 正常计算；如果 Const=FALSE，常数 b 为 1!逻辑值，如果返回附加的回归统计值，返回 TRUE；如果返回系数 m 和常数 b，返回 FALSE 或忽略"}, "LOGINV": {"a": "(概率; 平均值; 标准偏差)", "d": "返回 x 的对数正态累积分布函数的区间点，其中 ln(x) 是平均数和标准方差参数的正态分布", "ad": "是对数正态分布的概率，介于 0 与 1 之间，含 0 与 1!是 ln(x) 的平均数!是 ln(x) 的标准方差，正数"}, "LOGNORM.DIST": {"a": "(x; 平均值; 标准偏差; 累积)", "d": "返回对数正态分布", "ad": "用来进行函数计算的参数，正数!ln(x) 的平均数!ln(x) 的标准方差，正数!逻辑值，决定函数的形式。累积分布函数，使用 TRUE；概率密度函数，使用 FALSE"}, "LOGNORM.INV": {"a": "(概率; 平均值; 标准偏差)", "d": "返回具有给定概率的对数正态分布函数的区间点", "ad": "对数正态分布的概率，介于 0 与 1 之间，含 0 与 1!ln(x) 的平均数!ln(x) 的标准方差，正数"}, "LOGNORMDIST": {"a": "(x; 平均值; 标准偏差)", "d": "返回 x 的累积正态分布，其中 ln(x) 是平均数和标准方差参数的正态分布", "ad": "是用来进行函数计算的值，正数!是 ln(x) 的平均数!是 ln(x) 的标准方差，正数"}, "MAX": {"a": "(数字1; [数字2]; ...)", "d": "返回一组数值中的最大值，忽略逻辑值及文本", "ad": "是准备从中求取最大值的 1 到 255 个数值、空单元格、逻辑值或文本数值"}, "MAXA": {"a": "(值1; [值2]; ...)", "d": "返回一组参数中的最大值(不忽略逻辑值和字符串)", "ad": "是要求最大值的 1 到 255 个参数，可以是数值、空单元格、逻辑值或文本型数值"}, "MAXIFS": {"a": "(最大值单元格; 条件范围; 条件; ...)", "d": "返回一组给定条件所指定的单元格的最大值", "ad": "要确定最大值的单元格!是要为特定条件计算的单元格区域!是数字、表达式或文本形式的条件，它定义了在确定最大值时将包括的单元格"}, "MEDIAN": {"a": "(数字1; [数字2]; ...)", "d": "返回一组数的中值", "ad": "是用于中值计算的 1 到 255 个数字、名称、数组，或者是数值引用"}, "MIN": {"a": "(数字1; [数字2]; ...)", "d": "返回一组数值中的最小值，忽略逻辑值及文本", "ad": "是准备从中求取最小值的 1 到 255 个数值、空单元格、逻辑值或文本数值"}, "MINA": {"a": "(值1; [值2]; ...)", "d": "返回一组参数中的最小值(不忽略逻辑值和字符串)", "ad": "是要求最小值的 1 到 255 个参数，可以是数值、空单元格、逻辑值或文本型数值"}, "MINIFS": {"a": "(最小值单元格; 条件范围; 条件; ...)", "d": "返回一组给定条件所指定的单元格的最小值", "ad": "要确定最小值的单元格!是要为特定条件计算的单元格区域!是数字、表达式或文本形式的条件，它定义了在确定最小值时将包括的单元格"}, "MODE": {"a": "(数字1; [数字2]; ...)", "d": "返回一组数据或数据区域中的众数(出现频率最高的数)", "ad": "是 1 到 255 个数值、名称、数组或对数值的引用，用以求算众数"}, "MODE.MULT": {"a": "(数字1; [数字2]; ...)", "d": "返回一组数据或数据区域中出现频率最高或重复出现的数值的垂直数组。对于水平数组，可使用 =TRANSPOSE(MODE.MULT(数字1,数字2,...))", "ad": "是用于模式的 1 到 255 个数值、名称、数组或包含数值的引用"}, "MODE.SNGL": {"a": "(数字1; [数字2]; ...)", "d": "返回一组数据或数据区域中出现频率最高或重复出现数值", "ad": "是用于模式的 1 到 255 个数值、名称、数组或包含数值的引用"}, "NEGBINOM.DIST": {"a": "(失败次数; 成功次数阀值; 成功概率; 累积)", "d": "返回负二项式分布函数值", "ad": "失败的次数!成功次数阀值!一次试验中成功的概率,介于 0 与 1 之间!逻辑值，决定函数的形式。累积分布函数，使用 TRUE；概率密度函数，使用 FALSE"}, "NEGBINOMDIST": {"a": "(失败次数; 成功次数阀值; 成功概率)", "d": "返回负二项式分布函数，第 数字_s 次成功之前将有 数字_f 次失败的概率，具有 Probability_s 成功概率", "ad": "是失败的次数!是成功次数阀值!是一次试验中成功的概率，介于 0 与 1 之间"}, "NORM.DIST": {"a": "(x; 平均值; 标准偏差; 累积)", "d": "返回正态分布函数值", "ad": "用于计算正态分布函数值的区间点!分布的算术平均!分布的标准方差，正数!逻辑值，决定函数的形式。累积分布函数，使用 TRUE；概率密度函数，使用 FALSE"}, "NORMDIST": {"a": "(x; 平均值; 标准偏差; 累积)", "d": "返回指定平均值和标准方差的正态累积分布函数值", "ad": "是用于计算正态分布函数的值!是分布的算术平均!分布的标准方差，正数!是逻辑值，决定函数的形式。累积分布函数，使用 TRUE；概率密度函数，使用 FALSE"}, "NORM.INV": {"a": "(概率; 平均值; 标准偏差)", "d": "返回具有给定概率正态分布的区间点", "ad": "正态分布的概率,介于 0 与 1 之间，含 0 与 1!分布的算术平均!分布的标准方差，正数"}, "NORMINV": {"a": "(概率; 平均值; 标准偏差)", "d": "返回指定平均值和标准方差的正态累积分布函数的区间点", "ad": "是正态分布对应的概率，介于 0 与 1 之间，含 0 与 1!是分布的算术平均!是分布的标准方差，正数"}, "NORM.S.DIST": {"a": "(z; 累积)", "d": "返回标准正态分布函数值", "ad": "用于计算标准正态分布函数的区间点!逻辑值，当函数为累积分布函数时，返回值为 TRUE；当为概率密度函数时，返回值为 FALSE"}, "NORMSDIST": {"a": "(z)", "d": "返回标准正态累积分布函数值(具有零平均值和一标准方差)", "ad": "是要计算其分布的值"}, "NORM.S.INV": {"a": "(概率)", "d": "返回标准正态分布的区间点", "ad": "正态分布概率，介于 0 与 1 之间，含 0 与 1"}, "NORMSINV": {"a": "(概率)", "d": "返回标准正态累积分布的区间点(具有零平均值和一标准方差)", "ad": "是正态分布对应的概率，介于 0 与 1 之间，含 0 与 1"}, "PEARSON": {"a": "(数组1; 数组2)", "d": "求皮尔生(Pearson)积矩法的相关系数 r", "ad": "一组自变量!一组因变量"}, "PERCENTILE": {"a": "(数组; k)", "d": "返回数组的 K 百分点值", "ad": "为元素间关系确定的数值数组或数值区域!0 到 1 之间(含 0 与 1)的百分点值"}, "PERCENTILE.EXC": {"a": "(数组; k)", "d": "返回数组的 K 百分点值，K 介于 0 与 1 之间，不含 0 与 1", "ad": "为元素间关系确定的数值数组或数值区域!0 到 1 之间(含 0 与 1)的百分点值"}, "PERCENTILE.INC": {"a": "(数组; k)", "d": "返回数组的 K 百分点值，K 介于 0 与 1 之间，含 0 与 1", "ad": "为元素间关系确定的数值数组或数值区域!0 到 1 之间(含 0 与 1)的百分点值"}, "PERCENTRANK": {"a": "(数组; x; [有效位数])", "d": "返回特定数值在一组数中的百分比排名", "ad": "为元素间关系确定的数值数组或数值区域!为数组中需要得到其排名的某一个元素的数值!待返回百分比值的有效位数。此参数是可选的。如果忽略，则默认为 3 位(0.XXX%)"}, "PERCENTRANK.EXC": {"a": "(数组; x; [有效位数])", "d": "返回特定数值在一组数中的百分比排名(介于 0 与 1 之间，不含 0 与 1)", "ad": "是定义相对排名的带数值数据的数组或区域!是希望借以了解排名的值!是确定已返回百分比的有效数字数量的可选值,如果忽略将保留 3 位数字(0.xxx%)"}, "PERCENTRANK.INC": {"a": "(数组; x; [有效位数])", "d": "返回特定数值在一组数中的百分比排名(介于 0 与 1 之间，含 0 与 1)", "ad": "是定义相对排名的带数值数据的数组或区域!是希望借以了解排名的值!是确定已返回百分比的有效数字数量的可选值,如果忽略将保留 3 位数字(0.xxx%)"}, "PERMUT": {"a": "(数字; 元素数目)", "d": "返回从给定元素数目的集合中选取若干元素的排列数", "ad": "元素总数!每个排列中的元素数目"}, "PERMUTATIONA": {"a": "(数字; 对象数目)", "d": "返回可以从对象总数中选取的给定数目对象(包含重复项)的排列数", "ad": " 对象总数! 每个排列中的对象数目"}, "PHI": {"a": "(x)", "d": "返回标准正态分布的密度函数值", "ad": " 是计算其标准正态分布密度的数字"}, "POISSON": {"a": "(x; 平均值; 累积)", "d": "返回泊松(POISSON)分布", "ad": "是事件出现的次数!是期望值(正数)!是逻辑值，指定概率分布的返回形式，累积泊松概率，使用 TRUE；泊松概率密度函数，使用 FALSE"}, "POISSON.DIST": {"a": "(x; 平均值; 累积)", "d": "返回泊松(POISSON)分布", "ad": "事件出现的次数!期望值(正数)!逻辑值，指定概率分布的返回形式"}, "PROB": {"a": "(x_range; prob_range; 下界; [上界])", "d": "返回一概率事件组中符合指定条件的事件集所对应的概率之和", "ad": "具有各自不同的概率值的一组数值 x!与 x_range 中数值相对应的一组概率值，取值区间为 0(不含) 到 1!用于概率求和计算的数值的下界!用于概率求和计算的数值的可选上界。如果省略，PROB 返回当 X_range 等于 Lower_limit 时的概率"}, "QUARTILE": {"a": "(数组; 四分位数)", "d": "返回一组数据的四分位点", "ad": "用来计算其四分位点的数值数组或数值区域!数字，按四分位从小到大依次为 0 到 4"}, "QUARTILE.INC": {"a": "(数组; 四分位数)", "d": "基于从 0 到 1 之间(含 0 与 1)的百分点值，返回一组数据的四分位点", "ad": "用来计算其四分位点的数值数组或数值区域!数字，按四分位从小到大依次为 0 到 4"}, "QUARTILE.EXC": {"a": "(数组; 四分位数)", "d": "基于从 0 到 1 之间(不含 0 与 1)的百分点值，返回一组数据的四分位点", "ad": "用来计算其四分位点的数值数组或数值区域!数字，按四分位从小到大依次为 0 到 4"}, "RANK": {"a": "(数字; 引用; [次序])", "d": "返回某数字在一列数字中相对于其他数值的大小排名", "ad": "是要查找排名的数字!是一组数或对一个数据列表的引用。非数字值将被忽略!是在列表中排名的数字。如果为 0 或忽略，降序；非零值，升序"}, "RANK.AVG": {"a": "(数字; 引用; [次序])", "d": "返回某数字在一列数字中相对于其他数值的大小排名；如果多个数值排名相同，则返回平均值排名", "ad": "指定的数字!一组数或对一个数据列表的引用。非数字值将被忽略!指定排名的方式。如果为 0 或忽略，降序；非零值，升序"}, "RANK.EQ": {"a": "(数字; 引用; [次序])", "d": "返回某数字在一列数字中相对于其他数值的大小排名；如果多个数值排名相同，则返回该组数值的最佳排名", "ad": "指定的数字!一组数或对一个数据列表的引用。非数字值将被忽略!指定排名的方式。如果为 0 或忽略，降序；非零值，升序"}, "RSQ": {"a": "(known_y's; known_x's)", "d": "返回给定数据点的 Pearson 积矩法相关系数的平方", "ad": "一个数值数组或数值区域，可以是数值、名称、数组，或者是数值的引用!一个数值数组或数值区域，可以是数值、名称、数组，或者是数值的引用"}, "SKEW": {"a": "(数字1; [数字2]; ...)", "d": "返回一个分布的不对称度: 用来体现某一分布相对其平均值的不对称程度", "ad": "是要计算不对称度的 1 到 255 个参数，可以是数值、名称、数组，或者是数值的引用"}, "SKEW.P": {"a": "(数字1; [数字2]; ...)", "d": "基于总体返回分布的不对称度: 用来体现某一分布相对其平均值的不对称程度", "ad": " 是要计算总体不对称度的 1 到 255 个参数，可以是数值、名称、数组或者是数值的引用"}, "SLOPE": {"a": "(known_y's; known_x's)", "d": "返回经过给定数据点的线性回归拟合线方程的斜率", "ad": "为因变量数组或数值区域，可以是数值、名称、数组，或者是数值的引用!自变量数组或数值区域，可以是数值、名称、数组，或者是数值的引用"}, "SMALL": {"a": "(数组; k)", "d": "返回数据组中第 k 个最小值", "ad": "要求第 K 个最小值点的数值数组或数值区域!要返回的最小值点在数组或数据区中的位次"}, "STANDARDIZE": {"a": "(x; 平均值; 标准偏差)", "d": "通过平均值和标准方差返回正态分布概率值", "ad": "用于求解正态分布概率的区间点!分布的算术平均值!分布的标准方差"}, "STDEV": {"a": "(数字1; [数字2]; ...)", "d": "估算基于给定样本的标准偏差(忽略样本中的逻辑值及文本)", "ad": "是与总体抽样样本相应的 1 到 255 个数值，可以是数值，也可以是包含数值的引用"}, "STDEV.P": {"a": "(数字1; [数字2]; ...)", "d": "计算基于给定的样本总体的标准偏差(忽略逻辑值及文本)", "ad": "是与总体抽样样本相应的 1 到 255 个数值，可以是数值，也可以是包含数值的引用"}, "STDEV.S": {"a": "(数字1; [数字2]; ...)", "d": "估算基于给定样本的标准偏差(忽略样本中的逻辑值及文本)", "ad": "是与总体抽样样本相应的 1 到 255 个数值，可以是数值，也可以是包含数值的引用"}, "STDEVA": {"a": "(值1; [值2]; ...)", "d": "估算基于给定样本(包括逻辑值和字符串)的标准偏差。字符串和逻辑值 FALSE 数值为 0；逻辑值 TRUE 为 1", "ad": "是构成总体抽样样本的 1 到 255 个数值参数，可以是数字、名称或对数字的引用"}, "STDEVP": {"a": "(数字1; [数字2]; ...)", "d": "计算基于给定的样本总体的标准偏差(忽略逻辑值及文本)", "ad": "是与总体抽样样本相应的 1 到 255 个数值，可以是数值，也可以是包含数值的引用"}, "STDEVPA": {"a": "(值1; [值2]; ...)", "d": "计算样本(包括逻辑值和字符串)总体的标准偏差。字符串和逻辑值 FALSE 数值为 0；逻辑值 TRUE 为 1", "ad": "是构成样本总体的 1 到 255 个数值参数，这些参数可以是数值、名称、数组或对一个值的引用"}, "STEYX": {"a": "(known_y's; known_x's)", "d": "返回通过线性回归法计算纵坐标预测值所产生的标准误差", "ad": "因变量数组或数值区域，可以是数值、名称、数组，或者是数值的引用!自变量数组或数值区域，可以是数值、名称、数组，或者是数值的引用"}, "TDIST": {"a": "(x; 自由度; 尾数)", "d": "返回学生 t-分布", "ad": "用来计算 t 分布的数值!是一个表示自由度的整数值，用于定义分布!指定要返回的分布尾数的个数: 1 表示单尾分布；2 表示双尾分布"}, "TINV": {"a": "(概率; 自由度)", "d": "返回学生 t-分布的双尾区间点", "ad": "是双尾学生 t-分布的概率值，位于区间 0 到 1之间，含 0 与 1!为一正整数，表示分布的自由度"}, "T.DIST": {"a": "(x; 自由度; 累积)", "d": "返回左尾学生 t-分布", "ad": "用来计算 t-分布的数值!一个整数，用于定义分布的自由度!逻辑值，决定函数的形式。累积分布函数，使用 TRUE；概率密度函数，使用 FALSE"}, "T.DIST.2T": {"a": "(x; 自由度)", "d": "返回双尾学生 t-分布", "ad": "用来计算 t-分布的数值!一个整数，用于定义分布的自由度"}, "T.DIST.RT": {"a": "(x; 自由度)", "d": "返回右尾学生 t-分布", "ad": "用来计算 t-分布的数值!一个整数，用于定义分布的自由度"}, "T.INV": {"a": "(概率; 自由度)", "d": "返回学生 t-分布的左尾区间点", "ad": "双尾学生 t-分布的概率值，介于 0 与 1 之间，含 0 与 1!为一个正整数，用于定义分布的自由度"}, "T.INV.2T": {"a": "(概率; 自由度)", "d": "返回学生 t-分布的双尾区间点", "ad": "双尾学生 t-分布的概率值，介于 0 与 1 之间，含 0 与 1!为一个正整数，用于定义分布的自由度"}, "T.TEST": {"a": "(数组1; 数组2; 尾数; 类型)", "d": "返回学生 t-检验的概率值", "ad": "第一组数据集!第二组数据集!用于定义所返回的分布的尾数: 1 代表单尾；2 代表双尾!用于定义 t-检验的类型: 1 代表成对检验；2 代表双样本等方差假设；3 代表双样本异方差假设"}, "TREND": {"a": "(known_y's; [known_x's]; [new_x's]; [const])", "d": "返回线性回归拟合线的一组纵坐标值(y 值)", "ad": "满足线性拟合直线 y=mx+b 的一组已知的 y 值!满足线性拟合直线 y = mx + b 的一组已知的 x 值，为可选项!一组新 x 值，希望通过 TREND 函数推出相应的 y 值!逻辑值，用以指定是否强制常数 b 为 0，如果 Const=TRUE 或忽略，b 按通常方式计算；如果 Const=FALSE，b 强制为 0"}, "TRIMMEAN": {"a": "(数组; 百分比)", "d": "返回一组数据的修剪平均值", "ad": "用于截去极值后求取均值的数值数组或数值区域!为一分数，用于指定数据点集中所要消除的极值比例"}, "TTEST": {"a": "(数组1; 数组2; 尾数; 类型)", "d": "返回学生 t-检验的概率值", "ad": "第一组数据集!第二组数据集!用于定义所返回的分布的尾数: 1 代表单尾；2 代表双尾!用于定义 t-检验的类型: 1 代表成对检验；2 代表双样本等方差假设；3 代表双样本异方差假设"}, "VAR": {"a": "(数字1; [数字2]; ...)", "d": "估算基于给定样本的方差(忽略样本中的逻辑值及文本)", "ad": "是与总体抽样样本相应的 1 到 255 个数值参数"}, "VAR.P": {"a": "(数字1; [数字2]; ...)", "d": "计算基于给定的样本总体的方差(忽略样本中的逻辑值及文本)", "ad": "是与总体抽样样本相应的 1 到 255 个数值参数"}, "VAR.S": {"a": "(数字1; [数字2]; ...)", "d": "估算基于给定样本的方差(忽略样本中的逻辑值及文本)", "ad": "是与总体抽样样本相应的 1 到 255 个数值参数"}, "VARA": {"a": "(值1; [值2]; ...)", "d": "估算基于给定样本(包括逻辑值和字符串)的方差。字符串和逻辑值 FALSE 数值为 0；逻辑值 TRUE 为 1", "ad": "是构成总体抽样样本的 1 到 255 个数值参数"}, "VARP": {"a": "(数字1; [数字2]; ...)", "d": "计算基于给定的样本总体的方差(忽略样本总体中的逻辑值及文本)", "ad": "是与总体抽样样本相应的 1 到 255 个数值参数"}, "VARPA": {"a": "(值1; [值2]; ...)", "d": "计算样本(包括逻辑值和字符串)总体的方差。字符串和逻辑值 FALSE 数值为 0；逻辑值 TRUE 为 1", "ad": "是构成样本总体的 1 到 255 个数值参数"}, "WEIBULL": {"a": "(x; alpha; beta; 累积)", "d": "返回 Weibull 分布(概率密度)", "ad": "用来计算函数的数值，非负!是分布的参数值，为正!是分布的参数值，为正!逻辑值，决定函数的形式。累积分布函数，使用 TRUE；概率密度函数，使用 FALSE"}, "WEIBULL.DIST": {"a": "(x; alpha; beta; 累积)", "d": "返回 Weibull 分布(概率密度)", "ad": "用来计算分布的数值，非负数值!是分布的参数值，正数!是分布的参数值，正数!逻辑值，决定函数的形式。累积分布函数，使用 TRUE；概率密度函数，使用 FALSE"}, "Z.TEST": {"a": "(数组; x; [sigma])", "d": " 返回 z 测试的单尾 P 值。", "ad": " 是用于 z 测试的数值数组或数值区域。X! 是要测试的值。! 是总体标准偏差(已知)。如果忽略，则使用样本标准偏差"}, "ZTEST": {"a": "(数组; x; [sigma])", "d": "返回 z 测试的单尾 P 值", "ad": "是用于 X 测试的数值数组或数值区域!是要测试的值!是总体标准偏差(已知)。如果忽略，则使用样本标准偏差"}, "ACCRINT": {"a": "(发行日; 首次计息日; 结算日; 年息票利率; 票面值; 年付息次数; [日计数基准类型]; [计算方法])", "d": "返回定期支付利息的债券的应计利息", "ad": "是债券的发行日期，以一串日期表示!是债券的首次计息日，以一串日期表示!是债券的结算日，以一串日期表示!是债券的年票息率!是债券的票面值!是每年支付票息的次数!是所采用的日算类型!是一个逻辑值: 从发行日期开始的应计利息 = TRUE 或忽略；从最后票息支付日期开始计算 = FALSE"}, "ACCRINTM": {"a": "(发行日; 到期日; 年票息率; 票面值; [日计数基准类型])", "d": "返回在到期日支付利息的债券的应计利息", "ad": "是债券的发行日期，以一串日期表示!是债券的到期日，以一串日期表示!是债券的年票息率!是债券的票面值!是所采用的日算类型"}, "AMORDEGRC": {"a": "(资产原值; 购入资产日期; 第一个期间结束日期; 残值; 期间; 折旧率; [年基准])", "d": "返回每个记帐期内资产分配的线性折旧。", "ad": "是资产成本!是资产购买日期!是第一期的结束日期!是资产报废时的折余值!是记帐期!是折旧率!年基准: 0 代表一年 360 天，1 代表实际天数，3 代表一年 365 天"}, "AMORLINC": {"a": "(资产原值; 购入资产日期; 第一个期间结束日期; 残值; 期间; 折旧率; [年基准])", "d": "返回每个记帐期内资产分配的线性折旧。", "ad": "是资产成本!是资产购买日期!是第一期的结束日期!是资产报废时的折余值!是记帐期!是折旧率!年基准: 0 代表一年 360 天，1 代表实际天数，3 代表一年 365 天"}, "COUPDAYBS": {"a": "(结算日; 到期日; 年付息次数; [日计数基准类型])", "d": "返回从票息期开始到结算日之间的天数", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是每年支付票息的次数!是所采用的日算类型"}, "COUPDAYS": {"a": "(结算日; 到期日; 年付息次数; [日计数基准类型])", "d": "返回包含结算日的票息期的天数", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是每年支付票息的次数!是所采用的日算类型"}, "COUPDAYSNC": {"a": "(结算日; 到期日; 年付息次数; [日计数基准类型])", "d": "返回从结算日到下一票息支付日之间的天数", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是每年支付票息的次数!是所采用的日算类型"}, "COUPNCD": {"a": "(结算日; 到期日; 年付息次数; [日计数基准类型])", "d": "返回结算日后的下一票息支付日", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是每年支付票息的次数!是所采用的日算类型"}, "COUPNUM": {"a": "(结算日; 到期日; 年付息次数; [日计数基准类型])", "d": "返回结算日与到期日之间可支付的票息数", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是每年支付票息的次数!是所采用的日算类型"}, "COUPPCD": {"a": "(结算日; 到期日; 年付息次数; [日计数基准类型])", "d": "返回结算日前的上一票息支付日", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是每年支付票息的次数!是所采用的日算类型"}, "CUMIPMT": {"a": "(利率; 总付款期数; 现值; 第一期; 最后一期; 付款时间类型)", "d": "返回两个付款期之间为贷款累积支付的利息", "ad": "是利率!是总付款期数!是现值!是计算的第一期!是计算的最后一期!是付款的计时"}, "CUMPRINC": {"a": "(利率; 总付款期数; 现值; 第一期; 最后一期; 付款时间类型)", "d": "返回两个付款期之间为贷款累积支付的本金", "ad": "是利率!是总付款期数!是现值!是计算的第一期!是计算的最后一期!是付款的计时"}, "DB": {"a": "(固定资产原值; 估计残值; 周期总数; 生命周期; [月数])", "d": "用固定余额递减法，返回指定期间内某项固定资产的折旧值", "ad": "固定资产原值!资产使用年限结束时的估计残值!进行折旧计算的周期总数，也称固定资产的生命周期!进行折旧计算的期次，它必须与前者使用相同的单位!第一年的月份数，默认值为 12"}, "DDB": {"a": "(固定资产原值; 估计残值; 生命周期; 折旧期次; [余额递减速率])", "d": "用双倍余额递减法或其他指定方法，返回指定期间内某项固定资产的折旧值", "ad": "固定资产原值!固定资产使用年限终了时的估计残值!固定资产进行折旧计算的周期总数，也称固定资产的生命周期!进行折旧计算的期次，它必须与前者使用相同的单位!余额递减速率，若省略，则采用默认值 2(双倍余额递减)"}, "DISC": {"a": "(结算日; 到期日; 现价; 赎回值; [日计数基准类型])", "d": "返回债券的贴现率", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是每张票面为 100 元的债券的现价!是每张票面为 100 元的债券的赎回值!是所采用的日算类型"}, "DOLLARDE": {"a": "(货币值; 分母的整数)", "d": "将以分数表示的货币值转换为以小数表示的货币值", "ad": "是以分数表示的货币值!是分数的分母中使用的整数"}, "DOLLARFR": {"a": "(小数值; 分母的整数)", "d": "将以小数表示的货币值转换为以分数表示的货币值", "ad": "是小数值!是分数的分母中使用的整数"}, "DURATION": {"a": "(结算日; 到期日; 年票息率; 年收益; 付息次数; [日计数基准类型])", "d": "返回定期支付利息的债券的年持续时间", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是债券的年票息率!是债券的年收益!是每年支付票息的次数!是所采用的日算类型"}, "EFFECT": {"a": "(单利; 复利计算期数)", "d": "返回年有效利率", "ad": "是单利!是每年的复利计算期数"}, "FV": {"a": "(各期利率; 总付款期数; 各期支出金额; [已产生的付款或入账累计值]; [付款时间类型])", "d": "基于固定利率和等额分期付款方式，返回某项投资的未来值", "ad": "各期利率。例如，当利率为 6% 时，使用 6%/4 计算一个季度的还款额!总投资期，即该项投资总的付款期数!各期支出金额，在整个投资期内不变!从该项投资开始计算时已经入账的款项；或一系列未来付款当前值的累积和。如果忽略，Pv=0!数值 0 或 1,指定付款时间是期初还是期末。1 = 期初；0 或忽略 = 期末"}, "FVSCHEDULE": {"a": "(现值; 利率组合)", "d": "返回在应用一系列复利后，初始本金的终值", "ad": "是现值!是要应用的利率组合"}, "INTRATE": {"a": "(结算日; 到期日; 投资金额; 到期日收回金额; [日计数基准类型])", "d": "返回完全投资型债券的利率", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是投资债券的金额!是在到期日收回的金额!是所采用的日算类型"}, "IPMT": {"a": "(各期利率; 计算利息的期次; 付款期总数; 已产生的付款或入账累计值; [未来值]; [付款时间类型])", "d": "返回在定期偿还、固定利率条件下给定期次内某项投资回报(或贷款偿还)的利息部分", "ad": "各期利率。例如，当利率为 6% 时，使用 6%/4 计算一个季度的还款额!用于计算利息的期次，它必须介于 1 和付息总次数 Nper 之间!总投资(或贷款)期，即该项投资(或贷款)的付款期总数!从该项投资(或贷款)开始计算时已经入账的款项，或一系列未来付款当前值的累积和!未来值，或在最后一次付款后获得的现金余额。如果忽略，Fv = 0!数值 0 或 1 ，用以指定付息方式在期初还是在期末。如果为 0 或忽略，在期末；如果为 1，在期初"}, "IRR": {"a": "(值; [猜测值])", "d": "返回一系列现金流的内部报酬率", "ad": "一个数组，或对包含用来计算返回内部报酬率的数字的单元格的引用!内部报酬率的猜测值。如果忽略，则为 0.1(百分之十)"}, "ISPMT": {"a": "(各期利率; 一个季度的还款额; 偿还总期数; 未来付款当前值的累积和)", "d": "返回普通(无担保)贷款的利息偿还", "ad": "各期利率。例如，当利率为 6% 时，使用 6%/4 计算一个季度的还款额!用于计算利息的期次!总贷款期，即该笔贷款的偿还总期数!一系列未来付款当前值的累积和"}, "MDURATION": {"a": "(结算日; 到期日; 年票息率; 年收益; 付息次数; [付款时间类型])", "d": "为假定票面值为 100 元的债券返回麦考利修正持续时间", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是债券的年票息率!是债券的年收益!是每年支付票息的次数!是所采用的日算类型"}, "MIRR": {"a": "(值; 融资利率; 再投资报酬率)", "d": "返回在考虑投资成本以及现金再投资利率下一系列分期现金流的内部报酬率", "ad": "一个数组，或对数字单元格区的引用。代表固定期间内一系列支出(负数)及收入(正数)值!现金流中投入资金的融资利率!将各期收入净额再投资的报酬率"}, "NOMINAL": {"a": "(有效利率; 年复利计算期数)", "d": "返回年度的单利", "ad": "是有效利率!是每年的复利计算期数"}, "NPER": {"a": "(各期利率; 各期还款额; 已产生的付款或入账累计值; [未来值]; [付款时间类型])", "d": "基于固定利率和等额分期付款方式，返回某项投资或贷款的期数", "ad": "各期利率。例如，当利率为 6% 时，使用 6%/4 计算一个季度的还款额!各期还款额!从该项投资或贷款开始计算时已经入账的款项，或一系列未来付款当前值的累积和!未来值，或在最后一次付款后可以获得的现金余额!数值 0 或 1 ，用来指定付款时间是期初还是期末"}, "NPV": {"a": "(贴现率; 值1; [值2]; ...)", "d": "基于一系列将来的收(正值)支(负值)现金流和一贴现率，返回一项投资的净现值", "ad": "是一期的整个阶段的贴现率!代表支出和收入的 1 到 254 个参数，时间均匀分布并出现在每期末尾"}, "ODDFPRICE": {"a": "(结算日; 到期日; 发行日; 第一个票息日; 利率; 年收益; 赎回值; 付息次数; [日计数基准类型])", "d": "返回每张票面为 100 元且第一期为奇数的债券的现价", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是债券的发行日期，以一串日期表示!是债券的第一个票息日，以一串日期表示!是债券的利率!是债券的年收益!是每张票面为 100 元的债券的赎回值!是每年支付票息的次数!是所采用的日算类型"}, "ODDFYIELD": {"a": "(结算日; 到期日; 发行日; 第一个票息日; 利率; 现价; 赎回值; 付息次数; [日计数基准类型])", "d": "返回第一期为奇数的债券的收益", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是债券的发行日期，以一串日期表示!是债券的第一个票息日，以一串日期表示!是债券的利率!是债券的现价!是每张票面为 100 元的债券的赎回值!是每年支付票息的次数!是所采用的日算类型"}, "ODDLPRICE": {"a": "(结算日; 到期日; 最后一个票息日; 利率; 年收益; 赎回值; 付息次数; [日计数基准类型])", "d": "返回每张票面为 100 元且最后一期为奇数的债券的现价", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是债券的最后一个票息日，以一串日期表示!是债券的利率!是债券的年收益!是每张票面为 100 元的债券的赎回值!是每年支付票息的次数!是所采用的日算类型"}, "ODDLYIELD": {"a": "(结算日; 到期日; 最后一个票息日; 利率; 现价; 赎回值; 付息次数; [日计数基准类型])", "d": "返回最后一期为奇数的债券的收益", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是债券的最后一个票息日，以一串日期表示!是债券的利率!是债券的现价!是每张票面为 100 元的债券的赎回值!是每年支付票息的次数!是所采用的日算类型"}, "PDURATION": {"a": "(每期利率; 现值; 所需未来值)", "d": "返回投资达到指定的值所需的期数", "ad": " 是每期利率! 是投资的现值! 是投资的所需未来值"}, "PMT": {"a": "(各期利率; 付款期总数; 已产生的付款或入账累计值; [未来值]; [付款时间类型])", "d": "计算在固定利率下，贷款的等额分期偿还额", "ad": "各期利率。例如，当利率为 6% 时，使用 6%/4 计算一个季度的还款额!总投资期或贷款期，即该项投资或贷款的付款期总数!从该项投资(或贷款)开始计算时已经入账的款项，或一系列未来付款当前值的累积和!未来值，或在最后一次付款后可以获得的现金余额。如果忽略，则认为此值为 0!逻辑值 0 或 1，用以指定付款时间在期初还是在期末。如果为 1，付款在期初；如果为 0 或忽略，付款在期末"}, "PPMT": {"a": "(各期利率; 期次; 付款期总数; 已产生的付款或入账累计值; [未来值]; [付款时间类型])", "d": "返回在定期偿还、固定利率条件下给定期次内某项投资回报(或贷款偿还)的本金部分", "ad": "各期利率。例如，当利率为 6% 时，使用 6%/4 计算一个季度的还款额!用于计算其本金数额的期次，它必须介于 1 和付款总次数 nper 之间!总投资(或贷款)期，即该项投资(或贷款)的付款期总数!从该项投资(或贷款)开始计算时已经入账的款项，或一系列未来付款当前值的累积和!未来值，或在最后一次付款后可以获得的现金余额!数值 0 或 1 ，用以指定付款方式在期末还是期初。如果为 0 或忽略，在期末；如果为 1，在期初"}, "PRICE": {"a": "(结算日; 到期日; 年票息率; 年收益; 赎回值; 付息次数; [日计数基准类型])", "d": "返回每张票面为 100 元且定期支付利息的债券的现价", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是债券的年票息率!是债券的年收益!是每张票面为 100 元的债券的赎回值!是债券每年支付票息的次数!是所采用的日算类型"}, "PRICEDISC": {"a": "(结算日; 到期日; 贴现率; 赎回值; [日计数基准类型])", "d": "返回每张票面为 100 元的已贴现债券的现价", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是债券的贴现率!是每张票面为 100 元的债券的赎回值!是所采用的日算类型"}, "PRICEMAT": {"a": "(结算日; 到期日; 发行日; 发行日的利率; 年收益; [日计数基准类型])", "d": "返回每张票面为 100 元且在到期日支付利息的债券的现价", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是债券发行日期，以一串日期表示!是债券发行日的利率!是债券的年收益!是所采用的日算类型"}, "PV": {"a": "(各期利率; 偿款期总数; 各期所获得的金额; [未来值]; [付款时间类型])", "d": "返回某项投资的一系列将来偿还额的当前总值(或一次性偿还额的现值)", "ad": "各期利率。例如，当利率为 6% 时，使用 6%/4 计算一个季度的还款额!总投资期，即该项投资的偿款期总数!是各期所获得的金额，在整个投资期内不变!未来值，或在最后一次付款期后获得的一次性偿还额!逻辑值 0 或 1，用以指定付款时间在期初还是在期末。如果为 1，付款在期初；如果为 0 或忽略，付款在期末"}, "RATE": {"a": "(付款期总数; 各期所应收取(或支付)的金额; 未来付款的现值总额; [未来值]; [付款时间类型]; [利率猜测值])", "d": "返回投资或贷款的每期实际利率。例如，当利率为 6% 时，使用 6%/4 计算一个季度的还款额", "ad": "总投资期或贷款期，即该项投资或贷款的付款期总数!各期所应收取(或支付)的金额，在整个投资期或付款期不能改变!一系列未来付款的现值总额!未来值，或在最后一次付款后可以获得的现金余额。如果忽略，Fv=0!数值 0 或1，用以指定付款时间在期初还是在期末。如果为 1，付款在期初；如果为 0 或忽略，付款在期末!给定的利率猜测值。如果忽略，猜测值为 0.1(10%)"}, "RECEIVED": {"a": "(结算日; 到期日; 投资债券的金额; 贴现率; [日计数基准类型])", "d": "返回完全投资型债券在到期日收回的金额", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是投资债券的金额!是债券的贴现率!是所采用的日算类型"}, "RRI": {"a": "(期数; 现值; 未来值)", "d": "返回某项投资增长的等效利率", "ad": " 是投资的期数! 是投资的现值! 是投资的未来值"}, "SLN": {"a": "(固定资产原值; 估计残值; 生命周期)", "d": "返回固定资产的每期线性折旧费", "ad": "固定资产原值!固定资产使用年限终了时的估计残值!固定资产进行折旧计算的周期总数，也称固定资产的生命周期"}, "SYD": {"a": "(固定资产原值; 估计残值; 生命周期; 折旧期次)", "d": "返回某项固定资产按年限总和折旧法计算的每期折旧金额", "ad": "固定资产原值!固定资产使用年限终了时的估计残值!固定资产进行折旧计算的周期总数，也称固定资产的生命周期!进行折旧计算的期次，它必须与前者使用相同的单位"}, "TBILLEQ": {"a": "(结算日; 到期日; 贴现率)", "d": "返回短期国库券的等价债券收益", "ad": "是短期国库券的结算日，以一串日期表示!是短期国库券的到期日，以一串日期表示!是短期国库券的贴现率"}, "TBILLPRICE": {"a": "(结算日; 到期日; 贴现率)", "d": "返回每张票面为 100 元的短期国库券的现价", "ad": "是短期国库券的结算日，以一串日期表示!是短期国库券的到期日，以一串日期表示!是短期国库券的贴现率"}, "TBILLYIELD": {"a": "(结算日; 到期日; 现价)", "d": "返回短期国库券的收益", "ad": "是短期国库券的结算日，以一串日期表示!是短期国库券的到期日，以一串日期表示!是每张票面为 100 元的短期国库券的现价"}, "VDB": {"a": "(固定资产原值; 估计残值; 生命周期; 开始期次; 结束期次; [余额递减速率]; [不转换])", "d": "返回某项固定资产用余额递减法或其他指定方法计算的特定或部分时期的折旧额", "ad": "固定资产原值!固定资产使用年限终了时的估计残值!固定资产进行折旧计算的周期总数，也称固定资产的生命周期!进行折旧计算的开始期次，与生命周期的单位相同!进行折旧计算的结束期次，上述三者必须保持同样的单位(年，月等)!指余额递减速率!逻辑值，指定当折旧额超出用余额递减法计算的水平时，是否转换成直线折旧法"}, "XIRR": {"a": "(现金流; 付款日期计划; [猜测值])", "d": "返回现金流计划的内部回报率", "ad": "是一系列按日期对应付款计划的现金流!是对应现金流付款的付款日期计划!是一个认为接近 XIRR 结果的数字"}, "XNPV": {"a": "(贴现率; 现金流; 付款日期计划)", "d": "返回现金流计划的净现值", "ad": "是应用于现金流的贴现率!是一系列按日期对应付款计划的现金流!是对应现金流付款的付款日期计划"}, "YIELD": {"a": "(结算日; 到期日; 年票息率; 现价; 赎回值; 付息次数; [日计数基准类型])", "d": "返回定期支付利息的债券的收益", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是债券的年票息率!是每张票面为 100 元的债券的现价!是每张票面为 100 元的债券的赎回值!是债券每年支付票息的次数!是所采用的日算类型"}, "YIELDDISC": {"a": "(结算日; 到期日; 现价; 赎回值; [日计数基准类型])", "d": "返回已贴现债券的年收益，如短期国库券", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是每张票面为 100 元的债券的现价!是每张票面为 100 元的债券的赎回值!是所采用的日算类型"}, "YIELDMAT": {"a": "(结算日; 到期日; 发行日; 利率; 现价; [日计数基准类型])", "d": "返回在到期日支付利息的债券的年收益", "ad": "是债券的结算日，以一串日期表示!是债券的到期日，以一串日期表示!是债券发行日期，以一串日期表示!是债券发行日的利率!是每张票面为 100 元的债券的现价!是所采用的日算类型"}, "ABS": {"a": "(实数)", "d": "返回给定数值的绝对值，即不带符号的数值", "ad": "要对其求绝对值的实数"}, "ACOS": {"a": "(余弦值)", "d": "返回一个弧度的反余弦。弧度值在 0 到 Pi 之间。反余弦值是指余弦值为 数字 的角度", "ad": "余弦值，必须在 -1 和 1 之间"}, "ACOSH": {"a": "(实数)", "d": "返回反双曲余弦值", "ad": "大于或等于 1 的任何实数"}, "ACOT": {"a": "(余切值)", "d": "返回一个数字的反余切值。弧度值在 0 到 Pi 之间", "ad": " 所需角度的的余切值"}, "ACOTH": {"a": "(双曲余切值)", "d": "返回一个数字的反双曲余切值", "ad": " 所需角度的双曲余切值"}, "AGGREGATE": {"a": "(汇总函数; 合计要忽略的值; 引用1; ...)", "d": "返回一个数据列表或数据库的合计", "ad": "是从 1 到 19 的数字，用来指定合计所采用的汇总函数。!是从 1 到 7 的数字，用来指定合计要忽略的值!要计算合计的数值数组或数值区域!指示在数组中的位置；它是第 K 个最大值、第 K 个最小值、第 K 个百分点值或第 K 个四分位点。!是从 1 到 19 的数字，用来指定合计所采用的汇总函数。!是从 1 到 7 的数字，用来指定合计要忽略的值!为 1 到 253 个要进行合计的区域或引用"}, "ARABIC": {"a": "(罗马数字)", "d": "将罗马数字转换为阿拉伯数字", "ad": " 是要转换的罗马数字"}, "ASC": {"a": "(文本)", "d": "对于双字节字符集 (DBCS) 语言，该函数将全角（双字节）字符转换成半角（单字节）字符。", "ad": "文本或对包含要更改文本的单元格的引用。"}, "ASIN": {"a": "(正弦值)", "d": "返回一个弧度的反正弦。弧度值在 -Pi/2 到 Pi/2 之间", "ad": "正弦值，必须在 -1 和 1 之间"}, "ASINH": {"a": "(实数)", "d": "返回反双曲正弦值", "ad": "大于等于 1 的任何实数"}, "ATAN": {"a": "(正切值)", "d": "返回反正切值。以弧度表示，大小在 -Pi/2 到 Pi/2 之间", "ad": "角度的正切值"}, "ATAN2": {"a": "(X 轴坐标值; Y 轴坐标值)", "d": "根据给定的 X 轴及 Y 轴坐标值，返回反正切值。返回值在 -Pi 到 Pi 之间(不包括 -Pi)", "ad": "某点的 X 轴坐标值!某点的 Y 轴坐标值"}, "ATANH": {"a": "(实数)", "d": "返回反双曲正切值", "ad": "介于 -1 和 1 之间(不包括 -1 和 1)的任何实数"}, "BASE": {"a": "(数字; 基数; [最小长度])", "d": "将数字转换成具有给定基数的文本表示形式", "ad": " 是您要转换的数字! 是您希望将数字转换成的基数! 是返回的字符串的最小长度。如果省略，则不添加前导零"}, "CEILING": {"a": "(参数; 基数)", "d": "将参数向上舍入为最接近的指定基数的倍数", "ad": "需要进行舍入的参数!用于向上舍入的基数"}, "CEILING.MATH": {"a": "(值; [倍数]; [模式])", "d": "将数字向上舍入到最接近的整数或最接近的指定基数的倍数", "ad": " 是要进行舍入的值! 是要舍入到的倍数! 当为给定和非零值时，此函数将远离零的方向舍入"}, "CEILING.PRECISE": {"a": "(参数; [舍入倍数])", "d": "返回一个数字，该数字向上舍入为最接近的整数或最接近的有效位的倍数。", "ad": "需要进行舍入的参数!用于向上舍入的基数"}, "COMBIN": {"a": "(元素总数; 元素数目)", "d": "返回从给定元素数目的集合中提取若干元素的组合数", "ad": "元素总数!每个组合包含的元素数目"}, "COMBINA": {"a": "(项目总数; 项目数目)", "d": "返回给定数目的项目的组合数(包含重复项)", "ad": " 项目总数! 每个组合包含的项目数目"}, "COS": {"a": "(角度)", "d": "返回给定角度的余弦值", "ad": "以弧度表示的，准备求其余弦值的角度"}, "COSH": {"a": "(实数)", "d": "返回双曲余弦值", "ad": "任意实数"}, "COT": {"a": "(角度)", "d": "返回一个角度的余切值", "ad": " 以弧度表示，准备求其余切值的角度"}, "COTH": {"a": "(角度)", "d": "返回一个数字的双曲余切值", "ad": " 以弧度表示，准备求其双曲余切值的角度"}, "CSC": {"a": "(角度)", "d": "返回一个角度的余割值", "ad": " 以弧度表示，准备求其余切值的角度"}, "CSCH": {"a": "(角度)", "d": "返回一个角度的双曲余割值", "ad": " 以弧度表示，准备求其双曲余割值的角度"}, "DECIMAL": {"a": "(数字; 基数)", "d": "按给定基数将数字的文本表示形式转换成十进制数", "ad": " 是您要转换的数字! 是正在转换的数字的基数"}, "DEGREES": {"a": "(角)", "d": "将弧度转换成角度", "ad": "以弧度表示的角"}, "ECMA.CEILING": {"a": "(参数; 基数)", "d": "将参数向上舍入为最接近的指定基数的倍数", "ad": "需要进行舍入的参数!用于向上舍入的基数"}, "EVEN": {"a": "(数字)", "d": "将正数向上舍入到最近的偶数，负数向下舍入到最近的偶数", "ad": "需要取偶的数值"}, "EXP": {"a": "(指数)", "d": "返回 e 的 n 次方", "ad": "指数。常数 e 等于 2.71828182845904，是自然对数的底"}, "FACT": {"a": "(数字)", "d": "返回某数的阶乘，等于 1*2*...*数字", "ad": "要进行阶乘计算的非负数"}, "FACTDOUBLE": {"a": "(数字)", "d": "返回数字的双阶乘", "ad": "是求解双阶乘的值"}, "FLOOR": {"a": "(数字; 倍数)", "d": "将参数向下舍入为最接近的指定基数的倍数", "ad": "需要进行舍入运算的数值!用以进行舍入计算的倍数。数字和倍数两个参数必须同时为正或同时为负"}, "FLOOR.PRECISE": {"a": "(数字; [舍入倍数])", "d": "返回一个数字，该数字向下舍入为最接近的整数或最接近的有效位数的倍数。"}, "FLOOR.MATH": {"a": "(数字; [舍入倍数]; [模式])", "d": "将数字向下舍入到最接近的整数或最接近的指定基数的倍数", "ad": " 是要进行舍入的值! 是要舍入到的倍数! 当为给定和非零值时，此函数将向零方向舍入"}, "GCD": {"a": "(数字1; [数字2]; ...)", "d": "返回最大公约数", "ad": "是 1 到 255 个值"}, "INT": {"a": "(实数)", "d": "将数值向下取整为最接近的整数", "ad": "要取整的实数"}, "ISO.CEILING": {"a": "(参数; [舍入倍数])", "d": "返回一个数字，该数字向上舍入为最接近的整数或最接近的有效位的倍数。无论该数字的符号如何，该数字都向上舍入。但是，如果该数字或有效位为 0，则返回 0。", "ad": "需要进行舍入的参数!用于向上舍入的基数"}, "LCM": {"a": "(数字1; [数字2]; ...)", "d": "返回最小公倍数", "ad": "是 1 到 255 个求解最小其公倍数的值"}, "LN": {"a": "(数字)", "d": "返回给定数值的自然对数", "ad": "要对其求自然对数的正实数"}, "LOG": {"a": "(数字; [base])", "d": "根据给定底数返回数字的对数", "ad": "要取其对数的正实数!计算对数时所使用的底数，如果省略，则以 10 为底"}, "LOG10": {"a": "(数字)", "d": "返回给定数值以 10 为底的对数", "ad": "要对其求以 10 为底的对数的正实数"}, "MDETERM": {"a": "(数组)", "d": "返回一数组所代表的矩阵行列式的值", "ad": "行数和列数相等的数值数组，或是单元格区域，或是数组常量"}, "MINVERSE": {"a": "(数组)", "d": "返回一数组所代表的矩阵的逆矩阵", "ad": "行数和列数相等的数值数组，或是单元格区域，或是数组常量"}, "MMULT": {"a": "(数组1; 数组2)", "d": "返回两数组的矩阵积，结果矩阵的行数与 数组1 相等，列数与 数组2 相等", "ad": "是用于乘积计算的第一个数组数值，数组1 的列数应该与 数组2 的行数相等"}, "MOD": {"a": "(数字; divisor)", "d": "返回两数相除的余数", "ad": "被除数!除数"}, "MROUND": {"a": "(数字; multiple)", "d": "返回一个舍入到所需倍数的数字", "ad": "是要舍入的值!是要舍入到的倍数"}, "MULTINOMIAL": {"a": "(数字1; [数字2]; ...)", "d": "返回一组数字的多项式", "ad": "是 1 到 255 个用于计算多项式的值"}, "MUNIT": {"a": "(维度)", "d": "返回指定维度的单位矩阵", "ad": " 是一个整数，指定要返回的单位矩阵的维度"}, "ODD": {"a": "(数字)", "d": "将正(负)数向上(下)舍入到最接近的奇数", "ad": "要舍入的数值"}, "PI": {"a": "()", "d": "返回圆周率 Pi 的值，3.14159265358979，精确到 15 位", "ad": ""}, "POWER": {"a": "(底数; 幂值)", "d": "返回某数的乘幂", "ad": "底数，任何实数!幂值"}, "PRODUCT": {"a": "(数字1; [数字2]; ...)", "d": "计算所有参数的乘积", "ad": "是要计算乘积的 1 到 255 个数值、逻辑值或者代表数值的字符串"}, "QUOTIENT": {"a": "(被除数; 除数)", "d": "返回除法的整数部分", "ad": "是被除数!是除数"}, "RADIANS": {"a": "(角度)", "d": "将角度转为弧度", "ad": "要转成弧度的角度值"}, "RAND": {"a": "()", "d": "返回大于或等于 0 且小于 1 的平均分布随机数(依重新计算而变)", "ad": ""}, "RANDARRAY": {"a": "([行数]; [列数]; [最小值]; [最大值]; [整数])", "d": "返回随机数数组", "ad": "返回的数组中的行数!返回的数组中的列数!返回的想要的最小数目!返回的想要的最大数目!返回一个整数或小数值。整数为 TRUE，小数为 FALSE"}, "RANDBETWEEN": {"a": "(最小整数; 最大整数)", "d": "返回一个介于指定的数字之间的随机数", "ad": "是 RANDBETWEEN 能返回的最小整数!是 RANDBETWEEN 能返回的最大整数"}, "ROMAN": {"a": "(阿拉伯数字; [罗马数字类型])", "d": "将阿拉伯数字转换成文本式罗马数字", "ad": "要转换的阿拉伯数字!一个用于指定罗马数字类型的数字"}, "ROUND": {"a": "(数字; 位数)", "d": "按指定的位数对数值进行四舍五入", "ad": "要四舍五入的数值!执行四舍五入时采用的位数。如果此参数为负数，则圆整到小数点的左边；如果此参数为零，则圆整到最接近的整数"}, "ROUNDDOWN": {"a": "(数字; 位数)", "d": "向下舍入数字", "ad": "需要向下舍入的任意实数!舍入后的数字位数。如果此参数为负数，则将小数舍入到小数点左边一位；如果参数为零，则将小数转换为最接近的整数"}, "ROUNDUP": {"a": "(数字; 位数)", "d": "向上舍入数字", "ad": "需要向上舍入的任意实数!舍入后的数字位数。如果此参数为负数，则将小数舍入到小数点左边一位；如果参数为零，则将小数转换为最接近的整数"}, "SEC": {"a": "(数字)", "d": "返回角度的正割值", "ad": " 以弧度表示、准备求正割值的角度"}, "SECH": {"a": "(数字)", "d": "返回角度的双曲正割值", "ad": " 以弧度表示，准备求其双曲正割值的角度"}, "SERIESSUM": {"a": "(x; n; m; 系数)", "d": "返回基于公式的幂级数的和", "ad": "是幂级数的输入值!是 x 的初始幂次!是每级对 n 增加的步长!是 x 的每个继接幂次所乘的一组系数"}, "SIGN": {"a": "(实数)", "d": "返回数字的正负号: 为正时，返回 1；为零时，返回 0；为负时，返回 -1", "ad": "任意实数"}, "SIN": {"a": "(角度)", "d": "返回给定角度的正弦值", "ad": "以弧度表示的，准备求其正弦值的角度。Degrees * PI()/180 = radians"}, "SINH": {"a": "(实数)", "d": "返回双曲正弦值", "ad": "任意实数"}, "SQRT": {"a": "(数字)", "d": "返回数值的平方根", "ad": "要对其求平方根的数值"}, "SQRTPI": {"a": "(数字)", "d": "返回(数字 * Pi)的平方根", "ad": "是 pi 的乘数"}, "SUBTOTAL": {"a": "(汇总函数; 引用1; ...)", "d": "返回一个数据列表或数据库的分类汇总", "ad": "是从 1 到 11 的数字，用来指定分类汇总所采用的汇总函数!为 1 到 254 个要进行分类汇总的区域或引用"}, "SUM": {"a": "(数字1; [数字2]; ...)", "d": "计算单元格区域中所有数值的和", "ad": " 1 到 255 个待求和的数值。单元格中的逻辑值和文本将被忽略。但当作为参数键入时，逻辑值和文本有效"}, "SUMIF": {"a": "(范围; 条件; [求和单元格])", "d": "对满足条件的单元格求和", "ad": "要进行计算的单元格区域!以数字、表达式或文本形式定义的条件!用于求和计算的实际单元格。如果省略，将使用区域中的单元格"}, "SUMIFS": {"a": "(求和单元格; 条件范围; 条件; ...)", "d": "对一组给定条件指定的单元格求和", "ad": "是求和的实际单元格!是要为特定条件计算的单元格区域!是数字、表达式或文本形式的条件，它定义了单元格求和的范围"}, "SUMPRODUCT": {"a": "(数组1; [数组2]; [数组3]; ...)", "d": "返回相应的数组或区域乘积的和", "ad": "是 2 到 255 个数组。所有数组的维数必须一样"}, "SUMSQ": {"a": "(数字1; [数字2]; ...)", "d": "返回所有参数的平方和。参数可以是数值、数组、名称，或者是对数值单元格的引用", "ad": "是用于平方和计算的 1 至 255 个参数，可以是数值、数组、名称，或者是数组的引用"}, "SUMX2MY2": {"a": "(数组_x; 数组_y)", "d": "计算两数组中对应数值平方差的和", "ad": "第一个数组或数值区域，可以是数值、名称、数组，或者是对数值的引用!第二个数组或数值区域，可以是数值、名称、数组，或者是对数值的引用"}, "SUMX2PY2": {"a": "(数组_x; 数组_y)", "d": "计算两数组中对应数值平方和的和", "ad": "第一个数组或数值区域!第二个数组或数值区域"}, "SUMXMY2": {"a": "(数组_x; 数组_y)", "d": "求两数组中对应数值差的平方和", "ad": "第一个数组或数值区域，可以是数值、名称、数组，或者是对数值的引用!第二个数组或数值区域，可以是数值、名称、数组，或者是对数值的引用"}, "TAN": {"a": "(数字)", "d": "返回给定角度的正切值", "ad": "以弧度表示的，准备求其正切值的角度。Degrees * PI()/180 = radians"}, "TANH": {"a": "(数字)", "d": "返回双曲正切值", "ad": "任意实数。"}, "TRUNC": {"a": "(数字; [位数])", "d": "将数字截为整数或保留指定位数的小数", "ad": "要进行截尾操作的数字!用于指定截尾精度的数字。如果忽略，为 0"}, "ADDRESS": {"a": "(行号; 列标; [引用类型]; [引用样式]; [工作表的名称])", "d": "创建一个以文本方式对工作簿中某一单元格的引用", "ad": "指定引用单元格的行号。例如，行号 = 1 代表第 1 行!指定引用单元格的列标。例如，列标 = 4 代表 D 列!指定引用类型: 绝对引用 = 1；绝对行/相对列 = 2；相对行/绝对列 = 3；相对引用 = 4!用逻辑值指定引用样式: A1 样式 = 1 或 TRUE；R1C1 样式 = 0 或 FALSE!字符串，指定用作外部引用的工作表的名称"}, "CHOOSE": {"a": "(索引值; 值1; [值2]; ...)", "d": "根据给定的索引值，从参数串中选出相应值或操作", "ad": "指出所选参数值在参数表中的位置。索引值必须是介于 1 到 254 之间的数值，或者是返回值介于 1 到 254 之间的引用或公式!是 1 到 254 个数值参数、单元格引用、已定义名称、公式、函数，或者是 CHOOSE 从中选定的文本参数"}, "COLUMN": {"a": "([引用])", "d": "返回一引用的列号", "ad": "准备求取其列号的单元格或连续的单元格区域；如果忽略，则使用包含 COLUMN 函数的单元格"}, "COLUMNS": {"a": "(数组)", "d": "返回某一引用或数组的列数", "ad": "要计算列数的数组、数组公式或是对单元格区域的引用"}, "FORMULA文本": {"a": "(引用)", "d": "作为字符串返回公式", "ad": " 是对公式的引用"}, "HLOOKUP": {"a": "(搜索值; 信息表; 行索引值; [近似匹配值])", "d": "搜索数组区域首行满足条件的元素，确定待检索单元格在区域中的列序号，再进一步返回选定单元格的值", "ad": "需要在数据表首行进行搜索的值，可以是数值、引用或字符串!需要在其中搜索数据的文本、数据或逻辑值表。信息表可为区域或区域名的引用!满足条件的单元格在数组区域 table_数组 中的行序号。表中第一行序号为 1!逻辑值: 如果为 TRUE 或忽略，在第一行中查找最近似的匹配；如果为 FALSE，查找时精确匹配"}, "HYPERLINK": {"a": "(路径; [名称])", "d": "创建一个快捷方式或链接，以便打开一个存储在硬盘、网络服务器或  Internet 上的文档", "ad": "要打开的文件名称及完整路径。可以是本地硬盘、UNC 路径或 URL 路径!要显示在单元格中的数字或字符串。如果忽略此参数，单元格中显示路径的文本"}, "INDEX": {"a": "(数组; 行号; [列号]!引用; 行号; [列号]; [区域号])", "d": "在给定的单元格区域中，返回特定行列交叉处单元格的值或引用", "ad": "单元格区域或数组常量!数组或引用中要返回值的行序号。如果忽略，则必须有列号参数!数组或引用中要返回值的列序号。如果忽略，则必须有行号参数!对一个或多个单元格区域的引用!目标单元格在引用区域中的行序号，如果忽略，则必须有列号参数!目标单元格在引用区域中的列序号。如果忽略，则必须有行号参数!指定所要返回的行列交叉点位于引用区域组中的第几个区域。第一个区域为 1，第二个区域为 2，依次类推"}, "INDIRECT": {"a": "(引用文本; [引用方式])", "d": "返回文本字符串所指定的引用", "ad": "单元格引用，该引用所指向的单元格中存放有对另一单元格的引用，引用的形式为 A1、 R1C1 或是名称!逻辑值，用以指明引用文本单元格中包含的引用方式。R1C1 格式 = FALSE；A1 格式 = TRUE 或忽略"}, "LOOKUP": {"a": "(查找值; 查找向量; [结果向量]!查找值; 数组)", "d": "从单行或单列或从数组中查找一个值。条件是向后兼容性", "ad": "LOOKUP 要在查找值中查找的值，可以是数值、文本、逻辑值，也可以是数值的的名称或引用!只包含单行或单列的单元格区域，其值为文本，数值或者逻辑值且以升序排序!只包含单行或单列的单元格区域，与查找向量大小相同!LOOKUP 要在数组中查找的值，可以是数值、文本、逻辑值，也可以是数值的名称或引用!包含文本、数值或逻辑值的单元格区域，用来同查找值相比较"}, "MATCH": {"a": "(查找值; 查找数组; [匹配类型])", "d": "返回符合特定值特定顺序的项在数组中的相对位置", "ad": "在数组中所要查找匹配的值，可以是数值、文本或逻辑值，或者对上述类型的引用!含有要查找的值的连续单元格区域，一个数组，或是对某数组的引用!数字 -1、 0 或 1。 匹配类型指定了电子表格编辑器将查找值与查找数组中数值进行匹配的方式"}, "OFFSET": {"a": "(引用; 偏移行数; 偏移列数; [行数]; [列数])", "d": "以指定的引用为参照系，通过给定偏移量返回新的引用", "ad": "作为参照系的引用区域，其左上角单元格是偏移量的起始位置!相对于引用参照系的左上角单元格，上(下)偏移的行数!相对于引用参照系的左上角单元格，左(右)偏移的列数!新引用区域的行数!新引用区域的列数"}, "ROW": {"a": "([引用])", "d": "返回一个引用的行号", "ad": "准备求取其行号的单元格或单元格区域；如果忽略，则返回包含 ROW 函数的单元格"}, "ROWS": {"a": "(数组)", "d": "返回某一引用或数组的行数", "ad": "要计算行数的数组、数组公式或是对单元格区域的引用"}, "TRANSPOSE": {"a": "(数组)", "d": "转置单元格区域", "ad": "工作表中的单元格区域或数组"}, "UNIQUE": {"a": "(数组; [按列]; [仅一次])", "d": "从一个范围或数组返回唯一值。", "ad": "从中返回唯一行或列的范围或数组!是一个逻辑值: 将行彼此比较并返回唯一值 = FALSE，或已省略；将列彼此比较并返回唯一列 = TRUE!是一个逻辑值: 从数组中返回只出现一次的行或列 = TRUE；从数组中返回所有不同的行或列 = FALSE，或已省略"}, "VLOOKUP": {"a": "(查找值; 表数组; 列索引值; [大致匹配])", "d": "搜索表区域首列满足条件的元素，确定待检索单元格在区域中的行序号，再进一步返回选定单元格的值。默认情况下，表是以升序排序的", "ad": "需要在数据表首列进行搜索的值，可以是数值、引用或字符串!要在其中搜索数据的文字、数字或逻辑值表。表数组可以是对区域或区域名称的引用!应返回其中匹配值的表数组中的列序号。表中首个值列的序号为 1!逻辑值: 若要在第一列中查找大致匹配，请使用 TRUE 或省略；若要查找精确匹配，请使用 FALSE"}, "XLOOKUP": {"a": "(查找值; 查找数组; 返回数组; [如未找到]; [匹配模式]; [搜索模式])", "d": "在某个范围或数组中搜索匹配项，并通过第二个范围或数组返回相应的项。默认情况下使用精确匹配", "ad": "是要搜索的值!是要在其中进行搜索的数组或范围!是要返回的数组或范围!如果找不到匹配项，则返回!指定如何匹配查找值与查找数组中的值!指定要使用的搜索模式。默认情况下，将使用从第一项到最后一项的搜索模式"}, "CELL": {"a": "(信息类型; [引用])", "d": "返回有关单元格的格式、位置或内容的信息。", "ad": "一个文本值，指定要返回的单元格信息的类型。!需要其相关信息的单元格。"}, "ERROR.TYPE": {"a": "(错误值)", "d": "返回与错误值对应的数字。", "ad": "需要辨认其类型的错误值，可为实际错误值或对包含错误值的单元格的引用"}, "ISBLANK": {"a": "(值)", "d": "检查是否引用了空单元格，返回 TRUE 或 FALSE", "ad": "要检查的单元格或单元格名称"}, "ISERR": {"a": "(值)", "d": "检测一个值是否为 #N/A 以外的错误，返回 TRUE 或 FALSE", "ad": "是要测试的值。值可以是单元格、公式或指代单元格、公式或值的名称"}, "ISERROR": {"a": "(值)", "d": "检测一个值是否为错误，返回 TRUE 或 FALSE", "ad": "是要测试的值。值可以是单元格、公式或指代单元格、公式或值的名称"}, "ISEVEN": {"a": "(数字)", "d": "如果数字为偶数则返回 TRUE", "ad": "是要测试的值"}, "ISFORMULA": {"a": "(引用)", "d": "检查引用是否指向包含公式的单元格，并返回 TRUE 或 FALSE", "ad": " 是对要测试的单元格的引用。引用可以是单元格引用、公式或引用单元格的名称"}, "ISLOGICAL": {"a": "(值)", "d": "检测一个值是否是逻辑值(TRUE 或 FALSE)，返回 TRUE 或 FALSE", "ad": "检测值。该值可以是一个单元格、公式，或者是一个单元格、公式，或数值的名称"}, "ISNA": {"a": "(值)", "d": "检测一个值是否为 #N/A，返回 TRUE 或 FALSE", "ad": "检测值。检测值可以是一个单元格、公式，或者是一个单元格、公式，或数值的名称"}, "ISNON文本": {"a": "(值)", "d": "检测一个值是否不是文本(空单元格不是文本)，返回 TRUE 或 FALSE", "ad": "要检测的值，可以是单元格；公式；或者是单元格、公式或数值的引用"}, "IS数字": {"a": "(值)", "d": "检测一个值是否是数值，返回 TRUE 或 FALSE", "ad": "检测值。可以是一个单元格、公式，或者是一个单元格、公式，或数值的名称"}, "ISODD": {"a": "(数字)", "d": "如果数字为奇数则返回 TRUE", "ad": "是要测试的值"}, "IS引用": {"a": "(值)", "d": "检测一个值是否为引用，返回 TRUE 或 FALSE", "ad": "检测值,可以是一个单元格、公式，或者是一个单元格、公式，或数值的名称"}, "IS文本": {"a": "(值)", "d": "检测一个值是否为文本，返回 TRUE 或 FALSE", "ad": "检测值，可以是一个单元格、公式，或者是一个单元格、公式，或数值的名称"}, "N": {"a": "(值)", "d": "将不是数值形式的值转换为数值形式。日期转换成序列值，TRUE 转换成 1，其他值转换成 0", "ad": "要进行转换的值"}, "NA": {"a": "()", "d": "返回错误值 #N/A (无法计算出数值)", "ad": ""}, "SHEET": {"a": "([值])", "d": "返回引用的工作表的工作表编号", "ad": " 是需要工作表编号的工作表或引用的名称。如果省略，则返回包含函数的工作表的编号"}, "SHEETS": {"a": "([引用])", "d": "返回引用中的工作表数目", "ad": " 是要知道它包含的工作表数目的引用。如果省略，则返回工作簿中包含该函数的工作表数目"}, "TYPE": {"a": "(值)", "d": "以整数形式返回值的数据类型: 数值 = 1；文字 = 2；逻辑值 = 4；错误值 = 16；数组 = 64；复合数据 = 128", "ad": "可以是任何值"}, "AND": {"a": "(逻辑值1; [逻辑值2]; ...)", "d": "检查是否所有参数均为 TRUE，如果所有参数值均为 TRUE，则返回 TRUE", "ad": "是 1 到 255 个结果为 TRUE 或 FALSE 的检测条件，检测内容可以是逻辑值、数组或引用"}, "FALSE": {"a": "()", "d": "返回逻辑值 FALSE", "ad": ""}, "IF": {"a": "(逻辑测试; [如值为true]; [如值为false])", "d": "判断是否满足某个条件，如果满足返回一个值，如果不满足则返回另一个值。", "ad": " 是任何可能被计算为 TRUE 或 FALSE 的数值或表达式。! 是逻辑测试为 TRUE 时的返回值。如果忽略，则返回 TRUE。IF 函数最多可嵌套七层。! 是当逻辑测试为 FALSE 时的返回值。如果忽略，则返回 FALSE"}, "IFS": {"a": "(逻辑测试; 如值为true; ...)", "d": " 检查是否满足一个或多个条件并返回与第一个 TRUE 条件对应的值", "ad": " 是任何可被计算为 TRUE 或 FALSE 的数值或表达式! 如果逻辑测试为 TRUE，是否返回该值"}, "IFERROR": {"a": "(值; 如值为error)", "d": "如果表达式是一个错误，则返回如值为error，否则返回表达式自身的值", "ad": "是任意值、表达式或引用!是任意值、表达式或引用"}, "IFNA": {"a": "(值; 如值为na)", "d": "如果表达式解析为 #N/A，则返回您指定的值,否则返回表达式的结果", "ad": " 是任何值或表达式或引用! 是任何值或表达式或引用"}, "NOT": {"a": "(逻辑值)", "d": "对参数的逻辑值求反: 参数为 TRUE 时返回 FALSE；参数为 FALSE 时返回TRUE", "ad": "可以对其进行真(TRUE)假(FALSE) 判断的任何值或表达式"}, "OR": {"a": "(逻辑值1; [逻辑值2]; ...)", "d": "如果任一参数值为 TRUE，即返回 TRUE；只有当所有参数值均为 FALSE 时才返回 FALSE", "ad": "1 到 225 个结果是 TRUE 或 FALSE 的检测条件"}, "SWITCH": {"a": "(表达式; 值1; 结果1; [默认或值2]; [结果2]; ...)", "d": "根据值列表计算表达式并返回与第一个匹配值对应的结果。如果没有匹配项，则返回可选默认值", "ad": "是要计算的表达式!是要与表达式进行比较的值!是在对应值与表达式匹配时要返回的结果"}, "TRUE": {"a": "()", "d": "返回逻辑值 TRUE", "ad": ""}, "XOR": {"a": "(逻辑值1; [逻辑值2]; ...)", "d": "返回所有参数的逻辑“异或”值", "ad": " 您想要测试的 1 到 254 个条件，可以为 TURE 或 FALSE，可以是逻辑值、数组或引用"}, "TEXTBEFORE": {"a": "(文本, 分隔符, [需要出现的分隔符], [匹配模式], [结尾匹配], [如未找到])", "d": " 返回分隔字符之前的文本。", "ad": " 要搜索分隔符的文本。! 要用作分隔符的字符或字符串。! 需要出现的分隔符。默认值为 1。从末尾搜索负数。! 在文本中搜索分隔符匹配项。默认情况下，将完成区分大小写的匹配。! 是否将分隔符与文本结尾匹配。默认情况下，不进行匹配。! 如果未找到匹配项，则返回。默认情况下，将返回 #N/A。"}, "TEXTAFTER": {"a": "(文本, 分隔符, [需要出现的分隔符], [匹配模式], [结尾匹配], [如未找到])", "d": " 返回分隔字符之后的文本。", "ad": " 要搜索分隔符的文本。! 要用作分隔符的字符或字符串。! 需要出现的分隔符。默认值为 1。从末尾搜索负数。! 在文本中搜索分隔符匹配项。默认情况下，将完成区分大小写的匹配。! 是否将分隔符与文本结尾匹配。默认情况下，不进行匹配。! 如果未找到匹配项，则返回。默认情况下，将返回 #N/A。"}, "TEXTSPLIT": {"a": "(文本, 列分隔符, [行分隔符], [忽略空], [匹配模式], [填充值])", "d": " 使用分隔符将文本拆分为行或列。", "ad": " 要拆分的文本! 要拆分列依据的字符或字符串。! 要拆分行依据的字符或字符串。! 是否忽略空单元格。默认为 FALSE。! 搜索文本中的分隔符匹配。默认情况下，会进行区分大小写的匹配。! 用于填充的值。默认情况下，使用 #N/A。"}, "WRAPROWS": {"a": "(矢量, 最大值数, [填充值])", "d": "在指定数目的值后将行或列向量换行。", "ad": "包装的矢量或引用。!每行的最大值数。!要填充的值。默认值为 #N/A。"}, "VSTACK": {"a": "(数组1, [数组2], ...)", "d": "将数组垂直堆叠到一个数组中。", "ad": "要堆叠的数组或引用。"}, "HSTACK": {"a": "(数组1, [数组2], ...)", "d": "将数组水平堆叠到一个数组中。", "ad": "要堆叠的数组或引用。"}, "CHOOSEROWS": {"a": "(数组, 行数1, [行数2], ...)", "d": "返回数组或引用中的行。", "ad": "包含要返回的行的数组或引用。!要返回的行数。"}, "CHOOSECOLS": {"a": "(数组, 列数1, [列数2], ...)", "d": "返回数组或引用中的列。", "ad": "包含要返回的列的数组或引用。!要返回的列数。"}, "TOCOL": {"a": "(数组, [忽略], [按行扫描])", "d": "以一列形式返回数组。", "ad": "作为列返回的数组或引用。!是否忽略某些类型的值。默认情况下，不会忽略任何值。!按列扫描数组。默认情况下，按行扫描数组。"}, "TOROW": {"a": "(数组, [忽略], [按列扫描])", "d": "以一行形式返回数组。", "ad": "作为行返回的数组或引用。!是否忽略某些类型的值。默认情况下，不会忽略任何值。!按列扫描数组。默认情况下，按行扫描数组。"}, "WRAPCOLS": {"a": "(矢量, 最大值数, [填充值])", "d": "在指定数目的值后将行或列向量换行。", "ad": "包装的矢量或引用。!每列的最大值数。!要填充的值。默认值为 #N/A。"}, "TAKE": {"a": "(数组, 行数, [列数])", "d": "从数组开头或结尾返回行或列。", "ad": "从中获取行或列的数组。!要获取的行数。负值表示从数组末尾开始获取。!要获取的列数。负值表示从数组末尾开始获取。"}, "DROP": {"a": "(数组, 行数, [列数])", "d": "从数组开头或结尾删除行或列。", "ad": "从中删除行或列的数组。!要删除的行数。负值表示从数组末尾开始删除。!要删除的列数。负值表示从数组末尾开始删除。"}, "SEQUENCE": {"a": "(行数, [列数], [第一个数字], [增量])", "d": "返回一个数字序列", "ad": "要返回的行数!要返回的列数!序列中的第一个数字!序列中每个序列值的增量"}, "EXPAND": {"a": "(数组, 行数, [列数], [填充值])", "d": "将数组扩展到指定维度。", "ad": "要扩展的数组。!扩展数组中的行数。如果缺失，则不会扩展行。!扩展数组中的列数。如果缺失，则不会扩展列。!要填充的值。默认值为 #N/A。"}, "XMATCH": {"a": "(查找值, 查找数组, [匹配模式], [搜索模式])", "d": "返回项目在数组中的相对位置。默认情况下，需要精确匹配", "ad": "是要搜索的值!是要在其中进行搜索的数组或范围!指定如何匹配查找值与查找数组中的值!指定要使用的搜索模式。默认情况下，将使用从第一项到最后一项的搜索模式"}, "FILTER": {"a": "(数组, 保留, [保留为空])", "d": "筛选区域或数组", "ad": "要筛选的区域或数组!布尔值数组，其中 TRUE 表示要保留的一行或一列!如果未保留任何项，则返回"}, "ARRAYTO文本": {"a": "(数组, [格式])", "d": "返回数组的文本表示形式", "ad": "要表示为文本的数组!文本的格式"}, "SORT": {"a": "(数组, [排序依据], [排序顺序], [按列])", "d": "对范围或数组进行排序", "ad": "要排序的范围或数组!表示排序依据(按行或按列)的数字!表示所需排序顺序的数字；1 表示升序(默认)，-1 表示降序!表示所需排序方向的逻辑值: FALSE 指按行排序(默认)，TRUE 指按列排序"}, "SORTBY": {"a": "(数组, 按数组, [排序顺序], ...)", "d": "根据相应范围或数组中的值对范围或数组排序", "ad": "要排序的范围或数组!要基于其进行排序的范围或数组!指示所需排序顺序的数字；1 表示升序(默认值)，-1 表示降序"}, "GETPIVOTDATA": {"a": "(数据字段; 数据透视表; [字段]; [字段项]; ...)", "d": "提取存储在数据透视表中的数据", "ad": "是要从中提取数据的数据字段的名称!是到包含要检索数据的数据透视表中某单元格或单元格区域的引用!是要引用的字段!是要引用的字段项"}, "IMPORTRANGE": {"a": "(电子表格网址, 范围字符串)", "d": "从指定的电子表格中导入相应范围的单元格。", "ad": "数据导入来源电子表格的网址。!指定数据导入来源范围的字符串"}}