{"DATE": "DATE", "DATEDIF": "DATEDIF", "DATEVALUE": "DATEVALUE", "DAY": "DAY", "DAYS": "DAYS", "DAYS360": "DAYS360", "EDATE": "EDATE", "EOMONTH": "EOMONTH", "HOUR": "HOUR", "ISOWEEKNUM": "ISOWEEKNUM", "MINUTE": "MINUTE", "MONTH": "MONTH", "NETWORKDAYS": "NETWORKDAYS", "NETWORKDAYS.INTL": "NETWORKDAYS.INTL", "NOW": "NOW", "SECOND": "SECOND", "TIME": "TIME", "TIMEVALUE": "TIMEVALUE", "TODAY": "TODAY", "WEEKDAY": "WEEKDAY", "WEEKNUM": "WEEKNUM", "WORKDAY": "WORKDAY", "WORKDAY.INTL": "WORKDAY.INTL", "YEAR": "YEAR", "YEARFRAC": "YEARFRAC", "BESSELI": "BESSELI", "BESSELJ": "BESSELJ", "BESSELK": "BESSELK", "BESSELY": "BESSELY", "BIN2DEC": "BIN2DEC", "BIN2HEX": "BIN2HEX", "BIN2OCT": "BIN2OCT", "BITAND": "BITAND", "BITLSHIFT": "BITLSHIFT", "BITOR": "BITOR", "BITRSHIFT": "BITRSHIFT", "BITXOR": "BITXOR", "COMPLEX": "COMPLEX", "CONVERT": "CONVERT", "DEC2BIN": "DEC2BIN", "DEC2HEX": "DEC2HEX", "DEC2OCT": "DEC2OCT", "DELTA": "DELTA", "ERF": "ERF", "ERF.PRECISE": "ERF.PRECISE", "ERFC": "ERFC", "ERFC.PRECISE": "ERFC.PRECISE", "GESTEP": "GESTEP", "HEX2BIN": "HEX2BIN", "HEX2DEC": "HEX2DEC", "HEX2OCT": "HEX2OCT", "IMABS": "IMABS", "IMAGINARY": "IMAGINARY", "IMARGUMENT": "IMARGUMENT", "IMCONJUGATE": "IMCONJUGATE", "IMCOS": "IMCOS", "IMCOSH": "IMCOSH", "IMCOT": "IMCOT", "IMCSC": "IMCSC", "IMCSCH": "IMCSCH", "IMDIV": "IMDIV", "IMEXP": "IMEXP", "IMLN": "IMLN", "IMLOG10": "IMLOG10", "IMLOG2": "IMLOG2", "IMPOWER": "IMPOWER", "IMPRODUCT": "IMPRODUCT", "IMREAL": "IMREAL", "IMSEC": "IMSEC", "IMSECH": "IMSECH", "IMSIN": "IMSIN", "IMSINH": "IMSINH", "IMSQRT": "IMSQRT", "IMSUB": "IMSUB", "IMSUM": "IMSUM", "IMTAN": "IMTAN", "OCT2BIN": "OCT2BIN", "OCT2DEC": "OCT2DEC", "OCT2HEX": "OCT2HEX", "DAVERAGE": "DAVERAGE", "DCOUNT": "DCOUNT", "DCOUNTA": "DCOUNTA", "DGET": "DGET", "DMAX": "DMAX", "DMIN": "DMIN", "DPRODUCT": "DPRODUCT", "DSTDEV": "DSTDEV", "DSTDEVP": "DSTDEVP", "DSUM": "DSUM", "DVAR": "DVAR", "DVARP": "DVARP", "CHAR": "CHAR", "CLEAN": "CLEAN", "CODE": "CODE", "CONCATENATE": "CONCATENATE", "CONCAT": "CONCAT", "DOLLAR": "DOLLAR", "EXACT": "EXACT", "FIND": "FIND", "FINDB": "FINDB", "FIXED": "FIXED", "LEFT": "LEFT", "LEFTB": "LEFTB", "LEN": "LEN", "LENB": "LENB", "LOWER": "LOWER", "MID": "MID", "MIDB": "MIDB", "NUMBERVALUE": "NUMBERVALUE", "PROPER": "PROPER", "REPLACE": "REPLACE", "REPLACEB": "REPLACEB", "REPT": "REPT", "RIGHT": "RIGHT", "RIGHTB": "RIGHTB", "SEARCH": "SEARCH", "SEARCHB": "SEARCHB", "SUBSTITUTE": "SUBSTITUTE", "T": "T", "T.TEST": "T.TEST", "TEXT": "TEXT", "TEXTJOIN": "TEXTJOIN", "TREND": "TREND", "TRIM": "TRIM", "TRIMMEAN": "TRIMMEAN", "TTEST": "TTEST", "UNICHAR": "UNICHAR", "UNICODE": "UNICODE", "UPPER": "UPPER", "VALUE": "VALUE", "AVEDEV": "AVEDEV", "AVERAGE": "AVERAGE", "AVERAGEA": "AVERAGEA", "AVERAGEIF": "AVERAGEIF", "AVERAGEIFS": "AVERAGEIFS", "BETADIST": "BETADIST", "BETAINV": "BETAINV", "BETA.DIST": "BETA.DIST", "BETA.INV": "BETA.INV", "BINOMDIST": "BINOMDIST", "BINOM.DIST": "BINOM.DIST", "BINOM.DIST.RANGE": "BINOM.DIST.RANGE", "BINOM.INV": "BINOM.INV", "CHIDIST": "CHIDIST", "CHIINV": "CHIINV", "CHITEST": "CHITEST", "CHISQ.DIST": "CHISQ.DIST", "CHISQ.DIST.RT": "CHISQ.DIST.RT", "CHISQ.INV": "CHISQ.INV", "CHISQ.INV.RT": "CHISQ.INV.RT", "CHISQ.TEST": "CHISQ.TEST", "CONFIDENCE": "CONFIDENCE", "CONFIDENCE.NORM": "CONFIDENCE.NORM", "CONFIDENCE.T": "CONFIDENCE.T", "CORREL": "CORREL", "COUNT": "COUNT", "COUNTA": "COUNTA", "COUNTBLANK": "COUNTBLANK", "COUNTIF": "COUNTIF", "COUNTIFS": "COUNTIFS", "COVAR": "COVAR", "COVARIANCE.P": "COVARIANCE.P", "COVARIANCE.S": "COVARIANCE.S", "CRITBINOM": "CRITBINOM", "DEVSQ": "DEVSQ", "EXPON.DIST": "EXPON.DIST", "EXPONDIST": "EXPONDIST", "FDIST": "FDIST", "FINV": "FINV", "FTEST": "FTEST", "F.DIST": "F.DIST", "F.DIST.RT": "F.DIST.RT", "F.INV": "F.INV", "F.INV.RT": "F.INV.RT", "F.TEST": "F.TEST", "FISHER": "FISHER", "FISHERINV": "FISHERINV", "FORECAST": "FORECAST", "FORECAST.ETS": "FORECAST.ETS", "FORECAST.ETS.CONFINT": "FORECAST.ETS.CONFINT", "FORECAST.ETS.SEASONALITY": "FORECAST.ETS.SEASONALITY", "FORECAST.ETS.STAT": "FORECAST.ETS.STAT", "FORECAST.LINEAR": "FORECAST.LINEAR", "FREQUENCY": "FREQUENCY", "GAMMA": "GAMMA", "GAMMADIST": "GAMMADIST", "GAMMA.DIST": "GAMMA.DIST", "GAMMAINV": "GAMMAINV", "GAMMA.INV": "GAMMA.INV", "GAMMALN": "GAMMALN", "GAMMALN.PRECISE": "GAMMALN.PRECISE", "GAUSS": "GAUSS", "GEOMEAN": "GEOMEAN", "GROWTH": "GROWTH", "HARMEAN": "HARMEAN", "HYPGEOM.DIST": "HYPGEOM.DIST", "HYPGEOMDIST": "HYPGEOMDIST", "INTERCEPT": "INTERCEPT", "KURT": "KURT", "LARGE": "LARGE", "LINEST": "LINEST", "LOGEST": "LOGEST", "LOGINV": "LOGINV", "LOGNORM.DIST": "LOGNORM.DIST", "LOGNORM.INV": "LOGNORM.INV", "LOGNORMDIST": "LOGNORMDIST", "MAX": "MAX", "MAXA": "MAXA", "MAXIFS": "MAXIFS", "MEDIAN": "MEDIAN", "MIN": "MIN", "MINA": "MINA", "MINIFS": "MINIFS", "MODE": "MODE", "MODE.MULT": "MODE.MULT", "MODE.SNGL": "MODE.SNGL", "NEGBINOM.DIST": "NEGBINOM.DIST", "NEGBINOMDIST": "NEGBINOMDIST", "NORM.DIST": "NORM.DIST", "NORM.INV": "NORM.INV", "NORM.S.DIST": "NORM.S.DIST", "NORM.S.INV": "NORM.S.INV", "NORMDIST": "NORMDIST", "NORMINV": "NORMINV", "NORMSDIST": "NORMSDIST", "NORMSINV": "NORMSINV", "PEARSON": "PEARSON", "PERCENTILE": "PERCENTILE", "PERCENTILE.EXC": "PERCENTILE.EXC", "PERCENTILE.INC": "PERCENTILE.INC", "PERCENTRANK": "PERCENTRANK", "PERCENTRANK.EXC": "PERCENTRANK.EXC", "PERCENTRANK.INC": "PERCENTRANK.INC", "PERMUT": "PERMUT", "PERMUTATIONA": "PERMUTATIONA", "PHI": "PHI", "POISSON": "POISSON", "POISSON.DIST": "POISSON.DIST", "PROB": "PROB", "QUARTILE": "QUARTILE", "QUARTILE.INC": "QUARTILE.INC", "QUARTILE.EXC": "QUARTILE.EXC", "RANK.AVG": "RANK.AVG", "RANK.EQ": "RANK.EQ", "RANK": "RANK", "RSQ": "RSQ", "SKEW": "SKEW", "SKEW.P": "SKEW.P", "SLOPE": "SLOPE", "SMALL": "SMALL", "STANDARDIZE": "STANDARDIZE", "STDEV": "STDEV", "STDEV.P": "STDEV.P", "STDEV.S": "STDEV.S", "STDEVA": "STDEVA", "STDEVP": "STDEVP", "STDEVPA": "STDEVPA", "STEYX": "STEYX", "TDIST": "TDIST", "TINV": "TINV", "T.DIST": "T.DIST", "T.DIST.2T": "T.DIST.2T", "T.DIST.RT": "T.DIST.RT", "T.INV": "T.INV", "T.INV.2T": "T.INV.2T", "VAR": "VAR", "VAR.P": "VAR.P", "VAR.S": "VAR.S", "VARA": "VARA", "VARP": "VARP", "VARPA": "VARPA", "WEIBULL": "WEIBULL", "WEIBULL.DIST": "WEIBULL.DIST", "Z.TEST": "Z.TEST", "ZTEST": "ZTEST", "ACCRINT": "ACCRINT", "ACCRINTM": "ACCRINTM", "AMORDEGRC": "AMORDEGRC", "AMORLINC": "AMORLINC", "COUPDAYBS": "COUPDAYBS", "COUPDAYS": "COUPDAYS", "COUPDAYSNC": "COUPDAYSNC", "COUPNCD": "COUPNCD", "COUPNUM": "COUPNUM", "COUPPCD": "COUPPCD", "CUMIPMT": "CUMIPMT", "CUMPRINC": "CUMPRINC", "DB": "DB", "DDB": "DDB", "DISC": "DISC", "DOLLARDE": "DOLLARDE", "DOLLARFR": "DOLLARFR", "DURATION": "DURATION", "EFFECT": "EFFECT", "FV": "FV", "FVSCHEDULE": "FVSCHEDULE", "INTRATE": "INTRATE", "IPMT": "IPMT", "IRR": "IRR", "ISPMT": "ISPMT", "MDURATION": "MDURATION", "MIRR": "MIRR", "NOMINAL": "NOMINAL", "NPER": "NPER", "NPV": "NPV", "ODDFPRICE": "ODDFPRICE", "ODDFYIELD": "ODDFYIELD", "ODDLPRICE": "ODDLPRICE", "ODDLYIELD": "ODDLYIELD", "PDURATION": "PDURATION", "PMT": "PMT", "PPMT": "PPMT", "PRICE": "PRICE", "PRICEDISC": "PRICEDISC", "PRICEMAT": "PRICEMAT", "PV": "PV", "RATE": "RATE", "RECEIVED": "RECEIVED", "RRI": "RRI", "SLN": "SLN", "SYD": "SYD", "TBILLEQ": "TBILLEQ", "TBILLPRICE": "TBILLPRICE", "TBILLYIELD": "TBILLYIELD", "VDB": "VDB", "XIRR": "XIRR", "XNPV": "XNPV", "YIELD": "YIELD", "YIELDDISC": "YIELDDISC", "YIELDMAT": "YIELDMAT", "ABS": "ABS", "ACOS": "ACOS", "ACOSH": "ACOSH", "ACOT": "ACOT", "ACOTH": "ACOTH", "AGGREGATE": "AGGREGATE", "ARABIC": "ARABIC", "ASC": "ASC", "ASIN": "ASIN", "ASINH": "ASINH", "ATAN": "ATAN", "ATAN2": "ATAN2", "ATANH": "ATANH", "BASE": "BASE", "CEILING": "CEILING", "CEILING.MATH": "CEILING.MATH", "CEILING.PRECISE": "CEILING.PRESIZE", "COMBIN": "COMBIN", "COMBINA": "COMBINA", "COS": "COS", "COSH": "COSH", "COT": "COT", "COTH": "COTH", "CSC": "CSC", "CSCH": "CSCH", "DECIMAL": "DECIMAL", "DEGREES": "DEGREES", "ECMA.CEILING": "ECMA.CEILING", "EVEN": "EVEN", "EXP": "EXP", "FACT": "FACT", "FACTDOUBLE": "FACTDOUBLE", "FLOOR": "FLOOR", "FLOOR.PRECISE": "FLOOR.PRECISE", "FLOOR.MATH": "FLOOR.MATH", "GCD": "GCD", "INT": "INT", "ISO.CEILING": "ISO.CEILING", "LCM": "LCM", "LN": "LN", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "MDETERM", "MINVERSE": "MINVERSE", "MMULT": "MMULT", "MOD": "MOD", "MROUND": "MROUND", "MULTINOMIAL": "MULTINOMIAL", "MUNIT": "MUNIT", "ODD": "ODD", "PI": "PI", "POWER": "POWER", "PRODUCT": "PRODUCT", "QUOTIENT": "QUOTIENT", "RADIANS": "RADIANS", "RAND": "RAND", "RANDARRAY": "RANDARRAY", "RANDBETWEEN": "RANDBETWEEN", "ROMAN": "ROMAN", "ROUND": "ROUND", "ROUNDDOWN": "ROUNDDOWN", "ROUNDUP": "ROUNDUP", "SEC": "SEC", "SECH": "SECH", "SERIESSUM": "SERIESSUM", "SIGN": "SIGN", "SIN": "SIN", "SINH": "SINH", "SQRT": "SQRT", "SQRTPI": "SQRTPI", "SUBTOTAL": "SUBTOTAL", "SUM": "SUM", "SUMIF": "SUMIF", "SUMIFS": "SUMIFS", "SUMPRODUCT": "SUMPRODUCT", "SUMSQ": "SUMSQ", "SUMX2MY2": "SUMX2MY2", "SUMX2PY2": "SUMX2PY2", "SUMXMY2": "SUMXMY2", "TAN": "TAN", "TANH": "TANH", "TRUNC": "TRUNC", "ADDRESS": "ADDRESS", "CHOOSE": "CHOOSE", "COLUMN": "COLUMN", "COLUMNS": "COLUMNS", "FORMULATEXT": "FORMULATEXT", "HLOOKUP": "HLOOKUP", "HYPERLINK": "HYPERLINK", "INDEX": "INDEX", "INDIRECT": "INDIRECT", "LOOKUP": "LOOKUP", "MATCH": "MATCH", "OFFSET": "OFFSET", "ROW": "ROW", "ROWS": "ROWS", "TRANSPOSE": "TRANSPOSE", "UNIQUE": "UNIQUE", "VLOOKUP": "VLOOKUP", "XLOOKUP": "XLOOKUP", "CELL": "CELL", "ERROR.TYPE": "ERROR.TYPE", "ISBLANK": "ISBLANK", "ISERR": "ISERR", "ISERROR": "ISERROR", "ISEVEN": "ISEVEN", "ISFORMULA": "ISFORMULA", "ISLOGICAL": "ISLOGICAL", "ISNA": "ISNA", "ISNONTEXT": "ISNONTEXT", "ISNUMBER": "ISNUMBER", "ISODD": "ISODD", "ISREF": "ISREF", "ISTEXT": "ISTEXT", "N": "N", "NA": "NA", "SHEET": "SHEET", "SHEETS": "SHEETS", "TYPE": "TYPE", "AND": "AND", "FALSE": "FALSE", "IF": "IF", "IFS": "IFS", "IFERROR": "IFERROR", "IFNA": "IFNA", "NOT": "NOT", "OR": "OR", "SWITCH": "SWITCH", "TRUE": "TRUE", "XOR": "XOR", "TEXTBEFORE": "TEXTBEFORE", "TEXTAFTER": "TEXTAFTER", "TEXTSPLIT": "TEXTSPLIT", "WRAPROWS": "WRAPROWS", "VSTACK": "VSTACK", "HSTACK": "HSTACK", "CHOOSEROWS": "CHOOSEROWS", "CHOOSECOLS": "CHOOSECOLS", "TOCOL": "TOCOL", "TOROW": "TOROW", "WRAPCOLS": "WRAPCOLS", "TAKE": "TAKE", "DROP": "DROP", "SEQUENCE": "SEQUENCE", "EXPAND": "EXPAND", "XMATCH": "XMATCH", "FILTER": "FILTER", "ARRAYTOTEXT": "ARRAYTOTEXT", "SORT": "SORT", "SORTBY": "SORTBY", "GETPIVOTDATA": "GETPIVOTDATA", "IMPORTRANGE": "IMPORTRANGE", "LocalFormulaOperands": {"StructureTables": {"h": "Headers", "d": "Data", "a": "All", "tr": "This row", "t": "Totals"}, "CONST_TRUE_FALSE": {"t": "TRUE", "f": "FALSE"}, "CONST_ERROR": {"nil": "#NULL!", "div": "#DIV/0!", "value": "#VALUE!", "ref": "#REF!", "name": "#NAME\\?", "num": "#NUM!", "na": "#N/A", "getdata": "#GETTING_DATA", "uf": "#UNSUPPORTED_FUNCTION!", "calc": "#CALC!"}, "CELL_FUNCTION_INFO_TYPE": {"address": "<PERSON><PERSON><PERSON>", "col": "coloană", "color": "culoare", "contents": "conținut", "filename": "numele fișierului", "format": "format", "parentheses": "paranteze", "prefix": "prefix", "protect": "protejare", "row": "rând", "type": "tip", "width": "lățime"}}}