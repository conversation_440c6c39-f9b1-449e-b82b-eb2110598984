{"DATE": "DATA", "DATEDIF": "DATEDIF", "DATEVALUE": "DATE.VALORE", "DAY": "GIORNO", "DAYS": "GIORNI", "DAYS360": "GIORNO360", "EDATE": "DATA.MESE", "EOMONTH": "FINE.MESE", "HOUR": "ORA", "ISOWEEKNUM": "NUM.SETTIMANA.ISO", "MINUTE": "MINUTO", "MONTH": "MESI", "NETWORKDAYS": "GIORNI.LAVORATIVI.TOT", "NETWORKDAYS.INTL": "GIORNI.LAVORATIVI.TOT.INTL", "NOW": "ORA", "SECOND": "SECONDO", "TIME": "ORARIO", "TIMEVALUE": "ORARIO.VALORE", "TODAY": "OGGI", "WEEKDAY": "GIORNO.SETTIMANA", "WEEKNUM": "NUM.SETTIMANA", "WORKDAY": "GIORNO.LAVORATIVO", "WORKDAY.INTL": "GIORNO.LAVORATIVO.INTL", "YEAR": "ANNO", "YEARFRAC": "FRAZIONE.ANNO", "BESSELI": "BESSELI", "BESSELJ": "BESSELJ", "BESSELK": "BESSELK", "BESSELY": "BESSELY", "BIN2DEC": "BINARIO.DECIMALE", "BIN2HEX": "BINARIO.HEX", "BIN2OCT": "BINARIO.OCT", "BITAND": "BITAND", "BITLSHIFT": "BIT.SPOSTA.SX", "BITOR": "BITOR", "BITRSHIFT": "BIT.SPOSTA.DX", "BITXOR": "BITXOR", "COMPLEX": "COMPLESSO", "DEC2BIN": "DECIMALE.BINARIO", "DEC2HEX": "DECIMALE.HEX", "DEC2OCT": "DECIMALE.OCT", "DELTA": "DELTA", "ERF": "FUNZ.ERRORE", "ERF.PRECISE": "FUNZ.ERRORE.PRECISA", "ERFC": "FUNZ.ERRORE.COMP", "ERFC.PRECISE": "FUNZ.ERRORE.COMP.PRECISA", "GESTEP": "SOGLIA", "HEX2BIN": "HEX.BINARIO", "HEX2DEC": "HEX.DECIMALE", "HEX2OCT": "HEX.OCT", "IMABS": "COMP.MODULO", "IMAGINARY": "COMP.IMMAGINARIO", "IMARGUMENT": "COMP.ARGOMENTO", "IMCONJUGATE": "COMP.CONIUGATO", "IMCOS": "COMP.COS", "IMCOSH": "COMP.COSH", "IMCOT": "COMP.COT", "IMCSC": "COMP.CSC", "IMCSCH": "COMP.CSCH", "IMDIV": "COMP.DIV", "IMEXP": "COMP.EXP", "IMLN": "COMP.LN", "IMLOG10": "COMP.LOG10", "IMLOG2": "COMP.LOG2", "IMPOWER": "COMP.POTENZA", "IMPRODUCT": "COMP.PRODOTTO", "IMREAL": "COMP.PARTE.REALE", "IMSEC": "COMP.SEC", "IMSECH": "COMP.SECH", "IMSIN": "COMP.SEN", "IMSINH": "COMP.SENH", "IMSQRT": "COMP.RADQ", "IMSUB": "COMP.DIFF", "IMSUM": "COMP.SOMMA", "IMTAN": "COMP.TAN", "OCT2BIN": "OCT.BINARIO", "OCT2DEC": "OCT.DECIMALE", "OCT2HEX": "OCT.HEX", "DAVERAGE": "DB.MEDIA", "DCOUNT": "DB.CONTA.NUMERI", "DCOUNTA": "DB.CONTA.VALORI", "DGET": "DB.VALORI", "DMAX": "DB.MAX", "DMIN": "DB.MIN", "DPRODUCT": "DB.PRODOTTO", "DSTDEV": "DB.DEV.ST", "DSTDEVP": "DB.DEV.ST.POP", "DSUM": "DB.SOMMA", "DVAR": "DB.VAR", "DVARP": "DB.VAR.POP", "CHAR": "CODICE.CARATT", "CLEAN": "LIBERA", "CODE": "CODICE", "CONCATENATE": "CONCATENA", "CONCAT": "CONCATENA", "DOLLAR": "VALUTA", "EXACT": "IDENTICO", "FIND": "TROVA", "FINDB": "FINDB", "FIXED": "FISSO", "LEFT": "SINISTRA", "LEFTB": "LEFTB", "LEN": "LUNGHEZZA", "LENB": "LENB", "LOWER": "MINUSC", "MID": "STRINGA.ESTRAI", "MIDB": "MIDB", "NUMBERVALUE": "NUMERO.VALORE", "PROPER": "MAIUSC.INIZ", "REPLACE": "RIMPIAZZA", "REPLACEB": "REPLACEB", "REPT": "RIPETI", "RIGHT": "DESTRA", "RIGHTB": "RIGHTB", "SEARCH": "RICERCA", "SEARCHB": "SEARCHB", "SUBSTITUTE": "SOSTITUISCI", "T": "T", "T.TEST": "TESTT", "TEXT": "TESTO", "TEXTJOIN": "TEXTJOIN", "TREND": "TENDENZA", "TRIM": "ANNULLA.SPAZI", "TRIMMEAN": "MEDIA.TRONCATA", "TTEST": "TEST.T", "UNICHAR": "CARATT.UNI", "UNICODE": "UNICODE", "UPPER": "MAIUSCOL", "VALUE": "VALORE", "AVEDEV": "MEDIA.DEV", "AVERAGE": "MEDIA", "AVERAGEA": "MEDIA.VALORI", "AVERAGEIF": "MEDIA.SE", "AVERAGEIFS": "MEDIA.PIÙ.SE", "BETADIST": "DISTRIB.BETA", "BETAINV": "INV.BETA", "BETA.DIST": "DISTRIB.BETA.N", "BETA.INV": "INV.BETA.N", "BINOMDIST": "DISTRIB.BINOM", "BINOM.DIST": "DISTRIB.BINOM.N", "BINOM.DIST.RANGE": "INTERVALLO.DISTRIB.BINOM.N.", "BINOM.INV": "INV.BINOM", "CHIDIST": "DISTRIB.CHI", "CHIINV": "INV.CHI", "CHITEST": "TEST.CHI", "CHISQ.DIST": "DISTRIB.CHI.QUAD", "CHISQ.DIST.RT": "DISTRIB.CHI.QUAD.DS", "CHISQ.INV": "INV.CHI.QUAD", "CHISQ.INV.RT": "INV.CHI.QUAD.DS", "CHISQ.TEST": "TEST.CHI.QUAD", "CONFIDENCE": "CONFIDENZA", "CONFIDENCE.NORM": "CONFIDENZA.NORM", "CONFIDENCE.T": "CONFIDENZA.T", "CORREL": "CORRELAZIONE", "COUNT": "CONTA.NUMERI", "COUNTA": "COUNTA", "COUNTBLANK": "CONTA.VUOTE", "COUNTIF": "CONTA.SE", "COUNTIFS": "CONTA.PIÙ.SE", "COVAR": "COVARIANZA", "COVARIANCE.P": "COVARIANZA.P", "COVARIANCE.S": "COVARIANZA.C", "CRITBINOM": "CRIT.BINOM", "DEVSQ": "DEV.Q", "EXPON.DIST": "DISTRIB.EXP.N", "EXPONDIST": "EXPONDIST", "FDIST": "DISTRIB.F", "FINV": "INV.F", "F.DIST": "DISTRIBF", "F.DIST.RT": "DISTRIB.F.DS", "F.INV": "INVF", "F.INV.RT": "INV.F.DS", "FISHER": "FISHER", "FISHERINV": "INV.FISHER", "FORECAST": "PREVISIONE", "FORECAST.LINEAR": "PREVISIONE.LINEARE", "FREQUENCY": "FREQUENZA", "GAMMA": "GAMMA", "GAMMADIST": "DISTRIB.GAMMA", "GAMMA.DIST": "DISTRIB.GAMMA.N", "GAMMAINV": "INV.GAMMA", "GAMMA.INV": "INV.GAMMA.N", "GAMMALN": "LN.GAMMA", "GAMMALN.PRECISE": "LN.GAMMA.PRECISA", "GAUSS": "GAUSS", "GEOMEAN": "MEDIA.GEOMETRICA", "GROWTH": "CRESCITA", "HARMEAN": "MEDIA.ARMONICA", "HYPGEOM.DIST": "DISTRIB.IPERGEOM.N", "HYPGEOMDIST": "DISTRIB.IPERGEOM", "INTERCEPT": "INTERCETTA", "KURT": "CURTOSI", "LARGE": "GRANDE", "LINEST": "REGR.LIN", "LOGEST": "REGR.LOG", "LOGINV": "INV.LOGNORM", "LOGNORM.DIST": "DISTRIB.LOGNORM.N", "LOGNORM.INV": "INV.LOGNORM.N", "LOGNORMDIST": "DISTRIB.LOGNORM", "MAX": "MAX", "MAXA": "MAX.VALORI", "MAXIFS": "MAXIFS", "MEDIAN": "MEDIANA", "MIN": "MIN", "MINA": "MIN.VALORI", "MINIFS": "MINIFS", "MODE": "MODA", "MODE.MULT": "MODA.MULT", "MODE.SNGL": "MODA.SNGL", "NEGBINOM.DIST": "DISTRIB.BINOM.NEG.N", "NEGBINOMDIST": "DISTRIB.BINOM.NEG", "NORM.DIST": "DISTRIB.NORM.N", "NORM.INV": "INV.NORM.N", "NORM.S.DIST": "DISTRIB.NORM.ST.N", "NORM.S.INV": "INV.NORM.S", "NORMDIST": "DISTRIB.NORM.N", "NORMINV": "INV.NORM.N", "NORMSDIST": "DISTRIB.NORM.ST", "NORMSINV": "INV.NORM.ST", "PEARSON": "PEARSON", "PERCENTILE": "PERCENTILE", "PERCENTILE.EXC": "ESC.PERCENTILE", "PERCENTILE.INC": "INC.PERCENTILE", "PERCENTRANK": "PERCENT.RANGO", "PERCENTRANK.EXC": "ESC.PERCENT.RANGO", "PERCENTRANK.INC": "INC.PERCENT.RANGO", "PERMUT": "PERMUTAZIONE", "PERMUTATIONA": "PERMUTAZIONE.VALORI", "PHI": "PHI", "POISSON": "POISSON", "POISSON.DIST": "DISTRIB.POISSON", "PROB": "PROBABILITÀ", "QUARTILE": "QUARTILE", "QUARTILE.INC": "INC.QUARTILE", "QUARTILE.EXC": "ESC.QUARTILE", "RANK.AVG": "RANGO.MEDIA", "RANK.EQ": "RANGO.UG", "RANK": "RANGO", "RSQ": "RQ", "SKEW": "ASIMMETRIA", "SKEW.P": "ASSIMETRIA.P", "SLOPE": "PENDENZA", "SMALL": "PICCOLO", "STANDARDIZE": "NORMALIZZA", "STDEV": "DEV.ST", "STDEV.P": "DEV.ST.P", "STDEV.S": "DEV.ST.C", "STDEVA": "DEV.ST.VALORI", "STDEVP": "DEV.ST.P", "STDEVPA": "DEV.ST.POP.VALORI", "STEYX": "ERR.STD.YX", "TDIST": "DISTRIB.T", "TINV": "INV.T", "T.DIST": "DISTRIB.T.N", "T.DIST.2T": "DISTRIB.T.2T", "T.DIST.RT": "DISTRIB.T.DS", "T.INV": "INVT", "T.INV.2T": "INV.T.2T", "VAR": "VAR", "VAR.P": "VAR.P", "VAR.S": "VAR.C", "VARA": "VAR.VALORI", "VARP": "VAR.POP", "VARPA": "VAR.POP.VALORI", "WEIBULL": "WEIBULL", "WEIBULL.DIST": "DISTRIB.WEIBULL", "Z.TEST": "TESTZ", "ZTEST": "TEST.Z", "ACCRINT": "INT.MATUTRATO.PER", "ACCRINTM": "INT.MATUTRATO.SCAD", "AMORDEGRC": "AMMORT.DEGR", "AMORLINC": "AMMORT.PER", "COUPDAYBS": "GIORNI.CED.INIZ.LIQ", "COUPDAYS": "GIORNI.CED", "COUPDAYSNC": "GIORNI.CED.NUOVA", "COUPNCD": "DATA.CED.SUCC", "COUPNUM": "NUM.CED", "COUPPCD": "DATA.CED.PREC", "CUMIPMT": "INT.CUMUL", "CUMPRINC": "CAP.CUM", "DB": "AMMORT.FISSO", "DDB": "AMMORT", "DISC": "TASSO.SCONTO", "DOLLARDE": "VALUTA.DEC", "DOLLARFR": "VALUTA.FRAZ", "DURATION": "DURATA", "EFFECT": "EFFETTIVO", "FV": "VAL.FUT", "FVSCHEDULE": "VAL.FUT.CAPITALE", "INTRATE": "TASSO.INT", "IPMT": "INTERESSI", "IRR": "TIR.COST", "ISPMT": "INTERESSE,RATA", "MDURATION": "DURATA.M", "MIRR": "TIR.VAR", "NOMINAL": "NOMINALE", "NPER": "NUM.RATE", "NPV": "VAN", "ODDFPRICE": "PREZZO.PRIMO.IRR", "ODDFYIELD": "REND.PRIMO.IRR", "ODDLPRICE": "PREZZO.ULTIMO.IRR", "ODDLYIELD": "REND.ULTIMO.IRR", "PMT": "RATA", "PPMT": "P.RATA", "PRICE": "PREZZO", "PRICEDISC": "PREZZO.SCONT", "PRICEMAT": "PREZZO.SCAD", "PV": "VA", "RATE": "TASSO", "RECEIVED": "RICEV.SCAD", "RRI": "RIT.INVEST.EFFETT", "SLN": "AMMORT.COST", "SYD": "AMMORT.ANNUO", "TBILLEQ": "BOT.EQUIV", "TBILLPRICE": "BOT.PREZZO", "TBILLYIELD": "BOT.REND", "VDB": "AMMORT.VAR", "XIRR": "TIR.X", "XNPV": "VAN.X", "YIELD": "REND", "YIELDDISC": "REND.TITOLI.SOCNTI", "YIELDMAT": "REND.SCAD", "ABS": "ASS", "ACOS": "ARCCOS", "ACOSH": "ARCCOSH", "ACOT": "ARCCOT", "ACOTH": "ARCCOTH", "AGGREGATE": "AGGREGA", "ARABIC": "ARABO", "ASC": "ASC", "ASIN": "ARCSEN", "ASINH": "ARCSENH", "ATAN": "ARCTAN", "ATAN2": "ARCTAN.2", "ATANH": "ARCTANH", "BASE": "BASE", "CEILING": "ARROTONDA.ECCESSO", "CEILING.MATH": "ARROTONDA.ECCESSO.MAT", "CEILING.PRECISE": "ARROTONDA.ECCESSO.PRECISA", "COMBIN": "COMBINAZIONE", "COMBINA": "COMBINAZIONE.VALORI", "COS": "COS", "COSH": "COSH", "COT": "COT", "COTH": "COTH", "CSC": "CSC", "CSCH": "CSCH", "DECIMAL": "DECIMALE", "DEGREES": "GRADI", "ECMA.CEILING": "ECMA.CEILING", "EVEN": "PARI", "EXP": "EXP", "FACT": "FATTORIALE", "FACTDOUBLE": "FATT.DOPPIO", "FLOOR": "ARROTONDA.DIFETTO", "FLOOR.PRECISE": "ARROTONDA.DIFETTO.PRECISA", "FLOOR.MATH": "ARROTONDA.DIFETTO.MAT", "GCD": "MCD", "INT": "INT", "ISO.CEILING": "ISO.ARROTONDA.ECCESSO", "LCM": "MCM", "LN": "LN", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "MATR.DETERM", "MINVERSE": "MATR.INVERSA", "MMULT": "MATR.PRODOTTO", "MOD": "RESTO", "MROUND": "ARROTONDA.MULTIPLO", "MULTINOMIAL": "MULTINOMIALE", "MUNIT": "MATR.UNIT", "ODD": "DISPARI", "PI": "PI.GRECO", "POWER": "POTENZA", "PRODUCT": "PRODOTTO", "QUOTIENT": "QUOZIENTE", "RADIANS": "RADIANTI", "RAND": "CASUALE", "RANDARRAY": "MATR.CASUALE", "RANDBETWEEN": "CASUALE.TRA", "ROMAN": "ROMANO", "ROUND": "ARROTONDA", "ROUNDDOWN": "ARROTONDA.PER.DIF", "ROUNDUP": "ARROTONDA.PER.ECC", "SEC": "SEC", "SECH": "SECH", "SERIESSUM": "SOMMA.SERIE", "SIGN": "SEGNO", "SIN": "SEN", "SINH": "SENH", "SQRT": "RADQ", "SQRTPI": "RADQ.PI.GRECO", "SUBTOTAL": "SUBTOTAL", "SUM": "SOMMA", "SUMIF": "SOMMA.SE", "SUMIFS": "SOMMA.PIÙ.SE", "SUMPRODUCT": "MATR.SOMMA.PRODOTTO", "SUMSQ": "SOMMA.Q", "SUMX2MY2": "SOMMA.DIFF.Q", "SUMX2PY2": "SOMMA.SOMMA.Q", "SUMXMY2": "SOMMA.Q.DIFF", "TAN": "TAN", "TANH": "TANH", "TRUNC": "TRONCA", "ADDRESS": "INDIRIZZO", "CHOOSE": "SCEGLI", "COLUMN": "RIF.COLONNA", "COLUMNS": "COLONNE", "HLOOKUP": "CERCA.ORIZZ", "HYPERLINK": "COLLEG.IPERTESTUALE", "INDEX": "INDICE", "INDIRECT": "INDIRETTO", "LOOKUP": "CERCA", "MATCH": "CONFRONTA", "OFFSET": "SCARTO", "ROW": "RIF.RIGA", "ROWS": "RIGHE", "TRANSPOSE": "MATR.TRASPOSTA", "UNIQUE": "UNICI", "VLOOKUP": "CERCA.VERT", "XLOOKUP": "CERCA.X", "CELL": "CELLA", "ERROR.TYPE": "ERRORE.TIPO", "ISBLANK": "VAL.VUOTO", "ISERR": "VAL.ERR", "ISERROR": "VAL.ERRORE", "ISEVEN": "VAL.PARI", "ISFORMULA": "VAL.FORMULA", "ISLOGICAL": "VAL.LOGICO", "ISNA": "ISNA", "ISNONTEXT": "VAL.NON.TESTO", "ISNUMBER": "VAL.NUMERO", "ISODD": "VAL.DISPARI", "ISREF": "VAL.RIF", "ISTEXT": "VAL.TESTO", "N": "N", "NA": "NA", "SHEET": "FOGLIO", "SHEETS": "FOGLI", "TYPE": "TYPE", "AND": "E", "FALSE": "FALSO", "IF": "SE", "IFERROR": "SE.ERRORE", "IFNA": "SE.NON.DISP.", "NOT": "NON", "OR": "O", "SWITCH": "SWITCH", "TRUE": "VERO", "XOR": "XOR", "TEXTBEFORE": "TESTO.PRECEDENTE", "TEXTAFTER": "TESTO.SUCCESSIVO", "TEXTSPLIT": "DIVIDI.TESTO", "WRAPROWS": "A.CAPO.RIGA", "VSTACK": "STACK.VERT", "HSTACK": "STACK.ORIZ", "CHOOSEROWS": "SCEGLI.RIGA", "CHOOSECOLS": "SCEGLI.COL", "TOCOL": "A.COL", "TOROW": "A.RIGA", "WRAPCOLS": "A.CAPO.COL", "TAKE": "INCLUDI", "DROP": "ESCLUDI", "SEQUENCE": "SEQUENZA", "EXPAND": "ESPANDI", "XMATCH": "CONFRONTA.X", "FILTER": "FILTRO", "ARRAYTOTEXT": "MATRICE.A.TESTO", "SORT": "DATI.ORDINA", "SORTBY": "DATI.ORDINA.PER", "GETPIVOTDATA": "INFO.DATI.TAB.PIVOT", "IMPORTRANGE": "IMPORTRANGE", "LocalFormulaOperands": {"StructureTables": {"h": "Headers", "d": "Data", "a": "All", "tr": "This row", "t": "Totals"}, "CONST_TRUE_FALSE": {"t": "VERO", "f": "FALSO"}, "CONST_ERROR": {"nil": "#NULLO!", "div": "#DIV/0!", "value": "#VALORE!", "ref": "#RIF!", "name": "#NOME\\?", "num": "#NUM!", "na": "#N/D", "getdata": "#ESTRAZIONE_DATI_IN_CORSO", "uf": "#UNSUPPORTED_FUNCTION!", "calc": "#CALC!"}, "CELL_FUNCTION_INFO_TYPE": {"address": "<PERSON><PERSON><PERSON><PERSON>", "col": "col", "color": "colore", "contents": "contenuto", "filename": "nomefile", "format": "formato", "parentheses": "parentesi", "prefix": "prefisso", "protect": "proteggi", "row": "riga", "type": "tipo", "width": "<PERSON><PERSON><PERSON><PERSON>"}}}