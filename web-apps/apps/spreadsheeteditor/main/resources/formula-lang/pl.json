{"DATE": "DATA", "DATEDIF": "DATA.JEŻELI", "DATEVALUE": "DATA.WARTOŚĆ", "DAY": "DZIEŃ", "DAYS": "DNI", "DAYS360": "DNI.360", "EDATE": "NR.SER.DATY", "EOMONTH": "NR.SER.OST.DN.MIES", "HOUR": "GODZINA", "ISOWEEKNUM": "ISO.NUM.TYG", "MINUTE": "MINUTA", "MONTH": "MIESIĄC", "NETWORKDAYS": "DNI.ROBOCZE", "NETWORKDAYS.INTL": "DNI.ROBOCZE.NIESTAND", "NOW": "TERAZ", "SECOND": "SEKUNDA", "TIME": "CZAS", "TIMEVALUE": "CZAS.WARTOŚĆ", "TODAY": "DZIŚ", "WEEKDAY": "DZIEŃ.TYG", "WEEKNUM": "NUM.TYG", "WORKDAY": "DZIEŃ.ROBOCZY", "WORKDAY.INTL": "DZIEŃ.ROBOCZY.NIESTAND", "YEAR": "ROK", "YEARFRAC": "CZĘŚĆ.ROKU", "BESSELI": "BESSEL.I", "BESSELJ": "BESSEL.J", "BESSELK": "BESSEL.K", "BESSELY": "BESSEL.Y", "BIN2DEC": "DWÓJK.NA.DZIES", "BIN2HEX": "DWÓJK.NA.SZESN", "BIN2OCT": "DWÓJK.NA.ÓSM", "BITAND": "BITAND", "BITLSHIFT": "BIT.PRZESUNIĘCIE.W.LEWO", "BITOR": "BITOR", "BITRSHIFT": "BIT.PRZESUNIĘCIE.W.PRAWO", "BITXOR": "BITXOR", "COMPLEX": "LICZBA.ZESP", "CONVERT": "KONWERTUJ", "DEC2BIN": "DZIES.NA.DWÓJK", "DEC2HEX": "DZIES.NA.SZESN", "DEC2OCT": "DZIES.NA.ÓSM", "DELTA": "CZY.RÓWNE", "ERF": "FUNKCJA.BŁ", "ERF.PRECISE": "FUNKCJA.BŁ.DOKŁ", "ERFC": "KOMP.FUNKCJA.BŁ", "ERFC.PRECISE": "KOMP.FUNKCJA.BŁ.DOKŁ", "GESTEP": "SPRAWDŹ.PRÓG", "HEX2BIN": "SZESN.NA.DWÓJK", "HEX2DEC": "SZESN.NA.DZIES", "HEX2OCT": "SZESN.NA.ÓSM", "IMABS": "MODUŁ.LICZBY.ZESP", "IMAGINARY": "CZ.UROJ.LICZBY.ZESP", "IMARGUMENT": "ARG.LICZBY.ZESP", "IMCONJUGATE": "SPRZĘŻ.LICZBY.ZESP", "IMCOS": "COS.LICZBY.ZESP", "IMCOSH": "COSH.LICZBY.ZESP", "IMCOT": "COT.LICZBY.ZESP", "IMCSC": "CSC.LICZBY.ZESP", "IMCSCH": "CSCH.LICZBY.ZESP", "IMDIV": "ILORAZ.LICZB.ZESP", "IMEXP": "EXP.LICZBY.ZESP", "IMLN": "LN.LICZBY.ZESP", "IMLOG10": "LOG10.LICZBY.ZESP", "IMLOG2": "LOG2.LICZBY.ZESP", "IMPOWER": "POTĘGA.LICZBY.ZESP", "IMPRODUCT": "ILOCZYN.LICZB.ZESP", "IMREAL": "CZ.RZECZ.LICZBY.ZESP", "IMSEC": "SEC.LICZBY.ZESP", "IMSECH": "SECH.LICZBY.ZESP", "IMSIN": "SIN.LICZBY.ZESP", "IMSINH": "SINH.LICZBY.ZESP", "IMSQRT": "PIERWIASTEK.LICZBY.ZESP", "IMSUB": "RÓŻN.LICZB.ZESP", "IMSUM": "SUMA.LICZB.ZESP", "IMTAN": "TAN.LICZBY.ZESP", "OCT2BIN": "ÓSM.NA.DWÓJK", "OCT2DEC": "ÓSM.NA.DZIES", "OCT2HEX": "ÓSM.NA.SZESN", "DAVERAGE": "BD.ŚREDNIA", "DCOUNT": "BD.ILE.REKORDÓW", "DCOUNTA": "BD.ILE.REKORDÓW.A", "DGET": "BD.POLE", "DMAX": "BD.MAX", "DMIN": "BD.MIN", "DPRODUCT": "BD.ILOCZYN", "DSTDEV": "BD.ODCH.STANDARD", "DSTDEVP": "BD.ODCH.STANDARD.POPUL", "DSUM": "BD.SUMA", "DVAR": "BD.WARIANCJA", "DVARP": "BD.WARIANCJA.POPUL", "CHAR": "ZNAK", "CLEAN": "OCZYŚĆ", "CODE": "KOD", "CONCATENATE": "ZŁĄCZ.TEKSTY", "CONCAT": "ZŁĄCZ.TEKST", "DOLLAR": "KWOTA", "EXACT": "PORÓWNAJ", "FIND": "ZNAJDŹ", "FINDB": "ZNAJDŹB", "FIXED": "ZAOKR.DO.TEKST", "LEFT": "LEWY", "LEFTB": "LEWYB", "LEN": "DŁ", "LENB": "DŁ.B", "LOWER": "LITERY.MAŁE", "MID": "FRAGMENT.TEKSTU", "MIDB": "FRAGMENT.TEKSTU.B", "NUMBERVALUE": "WARTOŚĆ.LICZBOWA", "PROPER": "Z.<PERSON>LKIEJ.LITERY", "REPLACE": "ZASTĄP", "REPLACEB": "ZASTĄP.B", "REPT": "POWT", "RIGHT": "PRAWY", "RIGHTB": "PRAWY.B", "SEARCH": "SZUKAJ.TEKST", "SEARCHB": "SZUKAJ.TEKST.B", "SUBSTITUTE": "PODSTAW", "T": "T", "T.TEST": "T.TEST", "TEXT": "TEKST", "TEXTJOIN": "POŁĄCZ.TEKSTY", "TREND": "REGLINW", "TRIM": "USUŃ.ZBĘDNE.ODSTĘPY", "TRIMMEAN": "ŚREDNIA.WEWN", "TTEST": "TEST.T", "UNICHAR": "ZNAK.UNICODE", "UNICODE": "UNICODE", "UPPER": "LITERY.WIELKIE", "VALUE": "WARTOŚĆ", "AVEDEV": "ODCH.ŚREDNIE", "AVERAGE": "ŚREDNIA", "AVERAGEA": "ŚREDNIA.A", "AVERAGEIF": "ŚREDNIA.JEŻELI", "AVERAGEIFS": "ŚREDNIA.WARUNKÓW", "BETADIST": "ROZKŁAD.BETA", "BETAINV": "ROZKŁAD.BETA.ODW", "BETA.DIST": "ROZKŁ.BETA", "BETA.INV": "ROZKŁ.BETA.ODWR", "BINOMDIST": "ROZKŁAD.DWUM", "BINOM.DIST": "ROZKŁ.DWUM", "BINOM.DIST.RANGE": "ROZKŁ.DWUM.ZAKRES", "BINOM.INV": "ROZKŁ.DWUM.ODWR", "CHIDIST": "ROZKŁAD.CHI", "CHIINV": "ROZKŁAD.CHI.ODW", "CHITEST": "TEST.CHI", "CHISQ.DIST": "ROZKŁ.CHI", "CHISQ.DIST.RT": "ROZKŁ.CHI.PS", "CHISQ.INV": "ROZKŁ.CHI.ODWR", "CHISQ.INV.RT": "ROZKŁ.CHI.ODWR.PS", "CHISQ.TEST": "CHI.TEST", "CONFIDENCE": "UFNOŚĆ", "CONFIDENCE.NORM": "UFNOŚĆ.NORM", "CONFIDENCE.T": "UFNOŚĆ.T", "CORREL": "WSP.KORELACJI", "COUNT": "ILE.LICZB", "COUNTA": "ILE.NIEPUSTYCH", "COUNTBLANK": "LICZ.PUSTE", "COUNTIF": "LICZ.JEŻELI", "COUNTIFS": "LICZ.WARUNKI", "COVAR": "KOWARIANCJA", "COVARIANCE.P": "KOWARIANCJA.POPUL", "COVARIANCE.S": "KOWARIANCJA.PRÓBKI", "CRITBINOM": "PRÓG.ROZKŁAD.DWUM", "DEVSQ": "ODCH.KWADRATOWE", "EXPON.DIST": "ROZKŁ.EXP", "EXPONDIST": "ROZKŁAD.EXP", "FDIST": "ROZKŁAD.F", "FINV": "ROZKŁAD.F.ODW", "FTEST": "TEST.F", "F.DIST": "ROZKŁ.F", "F.DIST.RT": "ROZKŁ.F.PS", "F.INV": "ROZKŁ.F.ODWR", "F.INV.RT": "ROZKŁ.F.ODWR.PS", "F.TEST": "F.TEST", "FISHER": "ROZKŁAD.FISHER", "FISHERINV": "ROZKŁAD.FISHER.ODW", "FORECAST": "REGLINX", "FORECAST.ETS": "REGLINX.ETS", "FORECAST.ETS.CONFINT": "REGLINX.ETS.CONFINT", "FORECAST.ETS.SEASONALITY": "REGLINX.ETS.SEZONOWOŚĆ", "FORECAST.ETS.STAT": "REGLINX.ETS.STATYSTYKA", "FORECAST.LINEAR": "REGLINX.LINIOWA", "FREQUENCY": "CZĘSTOŚĆ", "GAMMA": "GAMMA", "GAMMADIST": "ROZKŁAD.GAMMA", "GAMMA.DIST": "ROZKŁ.GAMMA", "GAMMAINV": "ROZKŁAD.GAMMA.ODW", "GAMMA.INV": "ROZKŁ.GAMMA.ODWR", "GAMMALN": "ROZKŁAD.LIN.GAMMA", "GAMMALN.PRECISE": "ROZKŁAD.LIN.GAMMA.DOKŁ", "GAUSS": "GAUSS", "GEOMEAN": "ŚREDNIA.GEOMETRYCZNA", "GROWTH": "REGEXPW", "HARMEAN": "ŚREDNIA.HARMONICZNA", "HYPGEOM.DIST": "ROZKŁ.HIPERGEOM", "HYPGEOMDIST": "ROZKŁAD.HIPERGEOM", "INTERCEPT": "ODCIĘTA", "KURT": "KURTOZA", "LARGE": "MAX.K", "LINEST": "REGLINP", "LOGEST": "REGEXPP", "LOGINV": "ROZKŁAD.LOG.ODW", "LOGNORM.DIST": "ROZKŁ.LOG", "LOGNORM.INV": "ROZKŁ.LOG.ODWR", "LOGNORMDIST": "ROZKŁAD.LOG", "MAX": "MAX", "MAXA": "MAX.A", "MAXIFS": "MAKS.WARUNKÓW", "MEDIAN": "MEDIANA", "MIN": "MIN", "MINA": "MIN.A", "MINIFS": "MIN.WARUNKÓW", "MODE": "WYST.NAJCZĘŚCIEJ", "MODE.MULT": "WYST.NAJCZĘŚCIEJ.TABL", "MODE.SNGL": "WYST.NAJCZĘŚCIEJ.WART", "NEGBINOM.DIST": "ROZKŁ.DWUM.PRZEC", "NEGBINOMDIST": "ROZKŁAD.DWUM.PRZEC", "NORM.DIST": "ROZKŁ.NORMALNY", "NORM.INV": "ROZKŁ.NORMALNY.ODWR", "NORM.S.DIST": "ROZKŁ.NORMALNY.S", "NORM.S.INV": "ROZKŁ.NORMALNY.S.ODWR", "NORMDIST": "ROZKŁAD.NORMALNY.S", "NORMINV": "ROZKŁAD.NORMALNY.ODW", "NORMSDIST": "ROZKŁAD.NORMALNY", "NORMSINV": "ROZKŁAD.NORMALNY.S.ODW", "PEARSON": "PEARSON", "PERCENTILE": "PERCENTYL", "PERCENTILE.EXC": "PERCENTYL.PRZEDZ.OTW", "PERCENTILE.INC": "PERCENTYL.PRZEDZ.ZAMK", "PERCENTRANK": "PROCENT.POZYCJA", "PERCENTRANK.EXC": "PROC.POZ.PRZEDZ.OTW", "PERCENTRANK.INC": "PROC.POZ.PRZEDZ.ZAMK", "PERMUT": "PERMUTACJE", "PERMUTATIONA": "PERMUTACJE.A", "PHI": "PHI", "POISSON": "ROZKŁAD.POISSON", "POISSON.DIST": "ROZKŁ.POISSON", "PROB": "PRAWDPD", "QUARTILE": "KWARTYL", "QUARTILE.INC": "KWARTYL.PRZEDZ.ZAMK", "QUARTILE.EXC": "KWARTYL.PRZEDZ.OTW", "RANK.AVG": "POZYCJA.ŚR", "RANK.EQ": "POZYCJA.NAJW", "RANK": "POZYCJA", "RSQ": "R.<PERSON>", "SKEW": "SKOŚNOŚĆ", "SKEW.P": "SKOŚNOŚĆ.P", "SLOPE": "NACHYLENIE", "SMALL": "MIN.K", "STANDARDIZE": "NORMALIZUJ", "STDEV": "ODCH.STANDARDOWE", "STDEV.P": "ODCH.STAND.POPUL", "STDEV.S": "ODCH.STANDARD.PRÓBKI", "STDEVA": "ODCH.STANDARDOWE.A", "STDEVP": "ODCH.STANDARD.POPUL", "STDEVPA": "ODCH.STANDARD.POPUL.A", "STEYX": "REGBŁSTD", "TDIST": "ROZKŁAD.T", "TINV": "ROZKŁAD.T.ODW", "T.DIST": "ROZKŁ.T", "T.DIST.2T": "ROZKŁ.T.DS", "T.DIST.RT": "ROZKŁ.T.PS", "T.INV": "ROZKŁ.T.ODWR", "T.INV.2T": "ROZKŁ.T.ODWR.DS", "VAR": "WARIANCJA", "VAR.P": "WARIANCJA.POP", "VAR.S": "WARIANCJA.PRÓBKI", "VARA": "WARIANCJA.A", "VARP": "WARIANCJA.POPUL", "VARPA": "WARIANCJA.POPUL.A", "WEIBULL": "ROZKŁAD.WEIBULL", "WEIBULL.DIST": "ROZKŁ.WEIBULL", "Z.TEST": "Z.TEST", "ZTEST": "TEST.Z", "ACCRINT": "NAL.ODS", "ACCRINTM": "NAL.ODS.WYKUP", "AMORDEGRC": "AMORT.NIELIN", "AMORLINC": "AMORT.LIN", "COUPDAYBS": "WYPŁ.DNI.OD.POCZ", "COUPDAYS": "WYPŁ.DNI", "COUPDAYSNC": "WYPŁ.DNI.NAST", "COUPNCD": "WYPŁ.DATA.NAST", "COUPNUM": "WYPŁ.LICZBA", "COUPPCD": "WYPŁ.DATA.POPRZ", "CUMIPMT": "SPŁAC.ODS", "CUMPRINC": "SPŁAC.KAPIT", "DB": "DB", "DDB": "DDB", "DISC": "STOPA.DYSK", "DOLLARDE": "CENA.DZIES", "DOLLARFR": "CENA.UŁAM", "DURATION": "ROCZ.PRZYCH", "EFFECT": "EFEKTYWNA", "FV": "FV", "FVSCHEDULE": "WART.PRZYSZŁ.KAP", "INTRATE": "STOPA.PROC", "IPMT": "IPMT", "IRR": "IRR", "ISPMT": "ISPMT", "MDURATION": "ROCZ.PRZYCH.M", "MIRR": "MIRR", "NOMINAL": "NOMINALNA", "NPER": "NPER", "NPV": "NPV", "ODDFPRICE": "CENA.PIERW.OKR", "ODDFYIELD": "RENT.PIERW.OKR", "ODDLPRICE": "CENA.OST.OKR", "ODDLYIELD": "RENT.OST.OKR", "PDURATION": "O.CZAS.TRWANIA", "PMT": "PMT", "PPMT": "PPMT", "PRICE": "CENA", "PRICEDISC": "CENA.DYSK", "PRICEMAT": "CENA.WYKUP", "PV": "PV", "RATE": "RATE", "RECEIVED": "KWOTA.WYKUP", "RRI": "RÓWNOW.STOPA.PROC", "SLN": "SLN", "SYD": "SYD", "TBILLEQ": "RENT.EKW.BS", "TBILLPRICE": "CENA.BS", "TBILLYIELD": "RENT.BS", "VDB": "VDB", "XIRR": "XIRR", "XNPV": "XNPV", "YIELD": "RENTOWNOŚĆ", "YIELDDISC": "RENT.DYSK", "YIELDMAT": "RENT.WYKUP", "ABS": "MODUŁ.LICZBY", "ACOS": "ACOS", "ACOSH": "ACOSH", "ACOT": "ACOT", "ACOTH": "ACOTH", "AGGREGATE": "AGREGUJ", "ARABIC": "ARABSKIE", "ASC": "ASC", "ASIN": "ASIN", "ASINH": "ASINH", "ATAN": "ATAN", "ATAN2": "ATAN2", "ATANH": "ATANH", "BASE": "PODSTAWA", "CEILING": "ZAOKR.W.GÓRĘ", "CEILING.MATH": "ZAOKR.W.GÓRĘ.MATEMATYCZNE", "CEILING.PRESIZE": "ZAOKR.W.GÓRĘ.DOKŁ", "COMBIN": "KOMBINACJE", "COMBINA": "KOMBINACJE.A", "COS": "COS", "COSH": "COSH", "COT": "COT", "COTH": "COTH", "CSC": "CSC", "CSCH": "CSCH", "DECIMAL": "DZIESIĘTNA", "DEGREES": "STOPNIE", "ECMA.CEILING": "ECMA.ZAOKR.W.GÓRĘ", "EVEN": "ZAOKR.DO.PARZ", "EXP": "EXP", "FACT": "SILNIA", "FACTDOUBLE": "SILNIA.DWUKR", "FLOOR": "ZAOKR.W.<PERSON>", "FLOOR.PRECISE": "ZAOKR.W.DÓŁ.DOKŁ", "FLOOR.MATH": "ZAOKR.<PERSON><PERSON>.MATEMATYCZNE", "GCD": "NAJW.WSP.DZIEL", "INT": "ZAOKR.DO.CAŁK", "ISO.CEILING": "ISO.ZAOKR.W.GÓRĘ", "LCM": "NAJMN.WSP.WIEL", "LN": "LN", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "WYZNACZNIK.MACIERZY", "MINVERSE": "MACIERZ.ODW", "MMULT": "MACIERZ.ILOCZYN", "MOD": "MOD", "MROUND": "ZAOKR.DO.WIELOKR", "MULTINOMIAL": "WIELOMIAN", "MUNIT": "MACIERZ.JEDNOSTKOWA", "ODD": "ZAOKR.DO.NPARZ", "PI": "PI", "POWER": "POTĘGA", "PRODUCT": "ILOCZYN", "QUOTIENT": "CZ.CAŁK.DZIELENIA", "RADIANS": "RADIANY", "RAND": "LOS", "RANDARRAY": "LOSOWA.TABLICA", "RANDBETWEEN": "LOS.ZAKR", "ROMAN": "RZYMSKIE", "ROUND": "ZAOKR", "ROUNDDOWN": "ZAOKR.DÓŁ", "ROUNDUP": "ZAOKR.GÓRA", "SEC": "SEC", "SECH": "SECH", "SERIESSUM": "SUMA.SZER.POT", "SIGN": "ZNAK.LICZBY", "SIN": "SIN", "SINH": "SINH", "SQRT": "PIERWIASTEK", "SQRTPI": "PIERW.PI", "SUBTOTAL": "SUMY.CZĘŚCIOWE", "SUM": "SUMA", "SUMIF": "SUMA.JEŻELI", "SUMIFS": "SUMA.WARUNKÓW", "SUMPRODUCT": "SUMA.ILOCZYNÓW", "SUMSQ": "SUMA.KWADRATÓW", "SUMX2MY2": "SUMA.X2.M.Y2", "SUMX2PY2": "SUMA.X2.P.Y2", "SUMXMY2": "SUMA.XMY.2", "TAN": "TAN", "TANH": "TANH", "TRUNC": "LICZBA.CAŁK", "ADDRESS": "ADRES", "CHOOSE": "WYBIERZ", "COLUMN": "NR.KOLUMNY", "COLUMNS": "LICZBA.KOLUMN", "FORMULATEXT": "FORMUŁA.TEKST", "HLOOKUP": "WYSZUKAJ.POZIOMO", "HYPERLINK": "HIPERŁĄCZE", "INDEX": "INDEKS", "INDIRECT": "ADR.POŚR", "LOOKUP": "WYSZUKAJ", "MATCH": "PODAJ.POZYCJĘ", "OFFSET": "PRZESUNIĘCIE", "ROW": "WIERSZ", "ROWS": "ILE.WIERSZY", "TRANSPOSE": "TRANSPONUJ", "UNIQUE": "UNIKATOWE", "VLOOKUP": "WYSZUKAJ.PIONOWO", "XLOOKUP": "XLOOKUP", "CELL": "KOMÓRKA", "ERROR.TYPE": "NR.BŁĘDU", "ISBLANK": "CZY.PUSTA", "ISERR": "CZY.BŁ", "ISERROR": "CZY.BŁĄD", "ISEVEN": "CZY.PARZYSTE", "ISFORMULA": "CZY.FORMUŁA", "ISLOGICAL": "CZY.LOGICZNA", "ISNA": "CZY.BRAK", "ISNONTEXT": "CZY.NIE.TEKST", "ISNUMBER": "CZY.LICZBA", "ISODD": "CZY.NIEPARZYSTE", "ISREF": "CZY.ADR", "ISTEXT": "CZY.TEKST", "N": "N", "NA": "BRAK", "SHEET": "ARKUSZ", "SHEETS": "ARKUSZE", "TYPE": "TYP", "AND": "ORAZ", "FALSE": "FAŁSZ", "IF": "JEŻELI", "IFS": "WARUNKI", "IFERROR": "JEŻELI.BŁĄD", "IFNA": "JEŻELI.ND", "NOT": "NIE", "OR": "LUB", "SWITCH": "SWITCH", "TRUE": "PRAWDA", "XOR": "XOR", "TEXTBEFORE": "TEKST.PRZED", "TEXTAFTER": "TEKST.PO", "TEXTSPLIT": "PODZIEL.TEKST", "WRAPROWS": "ZAWIŃ.WIERSZE", "VSTACK": "STOS.PION", "HSTACK": "STOS.POZ", "CHOOSEROWS": "WYBIERZ.WIERSZE", "CHOOSECOLS": "WYBIERZ.KOLUMNY", "TOCOL": "DO.KOLUMNY", "TOROW": "DO.WIERSZA", "WRAPCOLS": "ZAWIŃ.KOLUMNY", "TAKE": "WYCINEK", "DROP": "POMIŃ", "SEQUENCE": "SEKWENCJA", "EXPAND": "ROZSZERZ", "XMATCH": "X.DOPASUJ", "FILTER": "FILTRUJ", "ARRAYTOTEXT": "TABLICA.NA.TEKST", "SORT": "SORTUJ", "SORTBY": "SORTUJ.WEDŁUG", "GETPIVOTDATA": "WEŹDANETABELI", "IMPORTRANGE": "IMPORTRANGE", "LocalFormulaOperands": {"StructureTables": {"h": "Wszystkie", "d": "<PERSON>", "a": "Nagłówki", "tr": "Ten wiersz", "t": "<PERSON><PERSON>"}, "CONST_TRUE_FALSE": {"t": "PRAWDA", "f": "FAŁSZ"}, "CONST_ERROR": {"nil": "#ZERO!", "div": "#DZIEL/0!", "value": "#ARG!", "ref": "#ADR!", "name": "#NAZWA?", "num": "#LICZBA!", "na": "#N/D", "getdata": "#GETTING_DATA", "uf": "#NIEOBSŁUGIWANE_FUNKCJA!", "calc": "#OBL!"}, "CELL_FUNCTION_INFO_TYPE": {"address": "adres", "col": "kolumna", "color": "kolor", "contents": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "filename": "nazwa_pliku", "format": "format", "parentheses": "na<PERSON><PERSON>y", "prefix": "prefiks", "protect": "ochrona", "row": "w<PERSON>z", "type": "typ", "width": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}