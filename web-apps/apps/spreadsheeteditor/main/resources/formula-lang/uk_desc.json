{"DATE": {"a": "(рік; місяць; день)", "d": "Повертає число, що відповідає коду дати-часу", "ad": "число від 1900 або 1904 (залежно від системи дат у книзі) до 9999!число від 1 до 12, що відповідає номеру місяця в році!число від 1 до 31, що відповідає номеру дня в місяці"}, "DATEDIF": {"a": "(дата_початку; дата_завершення; одиниця)", "d": "Обчислює кількість днів, місяців або років між двома датами", "ad": "Дата, яка представляє першу або початкову дату заданого періоду!Остання дата (або дата завершення періоду)!Тип даних, які потрібно повернути"}, "DATEVALUE": {"a": "(дата_в_текстовому_форматі)", "d": "Перетворює дату в текстовому форматі на число, що відповідає даті в коді дати-часу", "ad": "текстовий рядок із датою у форматі дати Spreadsheet Editor у діапазоні від 1.1.1900 або 1.1.1904 (залежно від системи дат у книзі) до 31.12.9999"}, "DAY": {"a": "(дата_в_числовому_форматі)", "d": "Повертає день місяця (число від 1 до 31).", "ad": "число в коді дати-часу, який використовується у програмі Spreadsheet Editor"}, "DAYS": {"a": "(кінц_дата; поч_дата)", "d": "Повертає кількість днів між двома датами.", "ad": "\"початк_дата\" і \"кінц_дата\" – це дві дати, між якими потрібно обчислити кількість днів!\"початк_дата\" і \"кінц_дата\" – це дві дати, між якими потрібно обчислити кількість днів"}, "DAYS360": {"a": "(поч_дата; кін_дата; [метод])", "d": "Повертає кількість днів між двома датами на основі 360-денного року (12 місяців по 30 днів)", "ad": "початк_дата і кінц_дата - дати, кількість днів між якими необхідно обчислити!початк_дата і кінц_дата - дати, кількість днів між якими необхідно обчислити!логічне значення, яке визначає використовуваний метод: європейський (TRUE) або американський (FALSE або не вказано)."}, "EDATE": {"a": "(поч_дата; кількість_місяців)", "d": "Повертає порядковий номер дати, яка передує початковій даті на вказану кількість місяців або йде через указану кількість місяців після початкової дати", "ad": "порядковий номер дати, яка представляє початкову дату!кількість місяців до або після початк_дати"}, "EOMONTH": {"a": "(поч_дата; кількість_місяців)", "d": "Повертає порядковий номер останнього дня місяця до або після вказаної кількості місяців", "ad": "порядковий номер, що представляє початкову дату!кількість місяців до або після початк_дати"}, "HOUR": {"a": "(час_у_числовому_форматі)", "d": "Повертає години як число від 0 до 23.", "ad": "число в коді дати-часу, який використовується у програмі Spreadsheet Editor, або текст у форматі часу, наприклад 16:48:00"}, "ISOWEEKNUM": {"a": "(дата)", "d": "Повертає номер тижня в році за системою ISO для вказаної дати", "ad": "код дати-часу, що використовується в програмі Spreadsheet Editor, щоб обчислювати дату й час"}, "MINUTE": {"a": "(час_у_числовому_форматі)", "d": "Повертає хвилини як число від 0 до 59.", "ad": "число в коді дати-часу, який використовується у програмі Spreadsheet Editor, або текст у форматі часу, наприклад 16:48:00"}, "MONTH": {"a": "(дата_в_числовому_форматі)", "d": "Повертає місяць (число від 1 (січень) до 12 (грудень)).", "ad": "число в коді дати-часу, який використовується у програмі Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(поч_дата; кінц_дата; [свята])", "d": "Повертає кількість цілих робочих днів між двома датами", "ad": "порядковий номер початкової дати!порядковий номер кінцевої дати!необов’язковий набір порядкових номерів дат, які потрібно виключити з робочого календаря (як-от державні, місцеві свята або свята зі змінною датою)"}, "NETWORKDAYS.INTL": {"a": "(дата_початку; дата_завершення; [вихідний]; [свята])", "d": "Повертає кількість цілих робочих днів між двома датами зі спеціальними параметрами вихідного дня", "ad": "порядковий номер дати початку!порядковий номер дати завершення!число або рядок, що вказує, який день вихідний!необов’язковий набір порядкових номерів дат, які потрібно виключити з робочого календаря (як-от державні, місцеві свята або свята зі змінною датою)"}, "NOW": {"a": "()", "d": "Повертає поточну дату й час у форматі дати й часу.", "ad": ""}, "SECOND": {"a": "(час_у_числовому_форматі)", "d": "Повертає секунди як число від 0 до 59.", "ad": "число в коді дати-часу, який використовується у програмі Spreadsheet Editor, або текст у форматі часу, наприклад 16:48:23"}, "TIME": {"a": "(години; хвилини; секунди)", "d": "Перетворює години, хвилини та секунди, задані як числа, на число в коді часу", "ad": "число від 0 до 23, яке представляє години!число від 0 до 59, яке представляє хвилини!число від 0 до 59, яке представляє секунди"}, "TIMEVALUE": {"a": "(час_у_текстовому_форматі)", "d": "Перетворює час із текстового формату на число в коді часу (число в інтервалі від 0 (0:00:00) до 0,999988426 (23:59:59)).", "ad": "текстовий рядок, який містить час у будь-якому з форматів часу Spreadsheet Editor (відомості про дату ігноруються)"}, "TODAY": {"a": "()", "d": "Повертає поточну дату у форматі дати.", "ad": ""}, "WEEKDAY": {"a": "(дата_в_числовому_форматі; [тип])", "d": "Повертає число від 1 до 7, яке відповідає дню тижня для вказаної дати.", "ad": "дата в числовому форматі!чис<PERSON><PERSON> (1,2 або 3), яке визначає тип відліку тижня: 1 - з Нд=1 по Сб=7, 2 - з Пн=1 по Нд=7, 3 - з Пн=0 по Нд=6"}, "WEEKNUM": {"a": "(порядковий_номер; [тип_повернення])", "d": "Повертає номер тижня в році", "ad": "Дата в коді дати-часу, який використовується в Spreadsheet Editor для обчислення дати та часу!число (1 або 2), яке визначає тип отриманого значення"}, "WORKDAY": {"a": "(поч_дата; дні; [свята])", "d": "Повертає порядковий номер дати, віддаленої в минулому або майбутньому на вказану кількість робочих днів", "ad": "порядковий номер початкової дати!кількість невихідних і несвяткових днів до або після початкової дати!необов’язковий масив порядкових номерів дат, які потрібно виключити з робочого календаря (як-от державні, місцеві свята або свята зі змінною датою)"}, "WORKDAY.INTL": {"a": "(дата_початку; дні; [вихідний]; [свята])", "d": "Повертає порядковий номер дати, віддаленої в минулому або майбутньому на вказану кількість робочих днів зі спеціальними параметрами вихідного дня", "ad": "порядковий номер дати початку!кількість невихідних і несвяткових днів до або після початк_дати!число або рядок, що вказує, який день вихідний!необов’язковий масив порядкових номерів дат, які потрібно виключити з робочого календаря (як-от державні, місцеві свята або свята зі змінною датою)"}, "YEAR": {"a": "(дата_в_числовому_форматі)", "d": "Повертає рік (ціле число від 1900 до 9999).", "ad": "число в коді дати-часу, який використовується у програмі Spreadsheet Editor"}, "YEARFRAC": {"a": "(поч_дата; кінц_дата; [базис])", "d": "Повертає частину року, яка складається з кількості повних днів між початковою та кінцевою датами", "ad": "порядковий номер, що представляє початкову дату!порядковий номер, що представляє кінцеву дату!використовуваний спосіб обчислення дня"}, "BESSELI": {"a": "(x; n)", "d": "Повертає модифіковану функцію Бесселя Іn(x)", "ad": "значення, для якого обчислюється функція!порядок функції Бесселя"}, "BESSELJ": {"a": "(x; n)", "d": "Повертає функцію Бесселя Jn(x)", "ad": "значення, для якого обчислюється функція!порядок функції Бесселя"}, "BESSELK": {"a": "(x; n)", "d": "Повертає модифіковану функцію Бесселя Кn(x)", "ad": "значення, для якого обчислюється функція!порядок функції"}, "BESSELY": {"a": "(x; n)", "d": "Повертає функцію Бесселя Yn(x)", "ad": "значення, для якого обчислюється функція!порядок функції"}, "BIN2DEC": {"a": "(число)", "d": "Перетворює двійкове число на десяткове", "ad": "двійкове число, яке слід перетворити"}, "BIN2HEX": {"a": "(число; [розряди])", "d": "Перетворює двійкове число на шістнадцяткове", "ad": "двійкове число, яке слід перетворити!кількість символів, які використовуються"}, "BIN2OCT": {"a": "(число; [розряди])", "d": "Перетворює двійкове число на вісімкове", "ad": "двійкове число, яке слід перетворити!кількість символів, які використовуються"}, "BITAND": {"a": "(число1; число2)", "d": "Повертає побітове \"ТА\" двох чисел", "ad": "являє собою десяткове вираження двійкового числа, яке потрібно обчислити!являє собою десяткове вираження двійкового числа, яке потрібно обчислити"}, "BITLSHIFT": {"a": "(число; велич_зсуву)", "d": "Повертає число, зсунуте ліворуч на велич_зсуву в бітах", "ad": "являє собою десяткове вираження двійкового числа, яке потрібно обчислити!являє собою кількість бітів, на яку число потрібно зсунути ліворуч"}, "BITOR": {"a": "(число1; число2)", "d": "Повертає побітове \"АБО\" двох чисел", "ad": "являє собою десяткове вираження двійкового числа, яке потрібно обчислити!являє собою десяткове вираження двійкового числа, яке потрібно обчислити"}, "BITRSHIFT": {"a": "(число; велич_зсуву)", "d": "Повертає число, зсунуте праворуч на велич_зсуву в бітах", "ad": "являє собою десяткове вираження двійкового числа, яке потрібно обчислити!являє собою кількість бітів, на яку число потрібно зсунути праворуч"}, "BITXOR": {"a": "(число1; число2)", "d": "Повертає побітове \"виключне АБО\" двох чисел", "ad": "являє собою десяткове вираження двійкового числа, яке потрібно обчислити!являє собою десяткове вираження двійкового числа, яке потрібно обчислити"}, "COMPLEX": {"a": "(дійсна_частина; уявна_частина; [суфікс])", "d": "Перетворює коефіцієнти дійсної й уявної частин на комплексне число", "ad": "коефіцієнт дійсної частини комплексного числа!коефіцієнт уявної частини комплексного числа!суфікс уявної частини комплексного числа"}, "CONVERT": {"a": "(число; стара_одиниця; нова_одиниця)", "d": "Перетворює число з однієї системи вимірювання в іншу", "ad": "значення в стара_одиниця, яке потрібно перетворити!одиниці вимірювання для числа!одиниці вимірювання для результату"}, "DEC2BIN": {"a": "(число; [розряди])", "d": "Перетворює десяткове число на двійкове", "ad": "десяткове ціле число, яке слід перетворити!кількість символів, які використовуються"}, "DEC2HEX": {"a": "(число; [розряди])", "d": "Перетворює десяткове число на шістнадцяткове", "ad": "десяткове ціле число, яке слід перетворити!кількість символів, які використовуються"}, "DEC2OCT": {"a": "(число; [розряди])", "d": "Перетворює десяткове число на вісімкове", "ad": "десяткове ціле число, яке слід перетворити!кількість символів, які використовуються"}, "DELTA": {"a": "(число1; [число2])", "d": "Перевіряє, чи рівні два числа", "ad": "перше число!друге число"}, "ERF": {"a": "(нижня_межа; [верхня_межа])", "d": "Повертає функцію помилки", "ad": "нижня межа інтегрування функції помилки!верхня межа інтегрування функції помилки"}, "ERF.PRECISE": {"a": "(X)", "d": "Повертає функцію помилки", "ad": "нижня межа інтегрування ERF.PRECISE"}, "ERFC": {"a": "(x)", "d": "Повертає додаткову функцію помилки", "ad": "нижня межа інтегрування функції помилки"}, "ERFC.PRECISE": {"a": "(X)", "d": "Повертає додаткову функцію помилки", "ad": "нижня межа інтегрування ERFC.PRECISE"}, "GESTEP": {"a": "(число; [поріг])", "d": "Перевіряє, чи є число більшим за граничне значення", "ad": "значення, яке порівнюється із граничним значенням!граничне значення"}, "HEX2BIN": {"a": "(число; [розряди])", "d": "Перетворює шістнадцяткове число на двійкове", "ad": "шістнадцяткове число, яке слід перетворити!кількість символів, які використовуються"}, "HEX2DEC": {"a": "(число)", "d": "Перетворює шістнадцяткове число на десяткове", "ad": "шістнадцяткове число, яке слід перетворити"}, "HEX2OCT": {"a": "(число; [розряди])", "d": "Перетворює шістнадцяткове число на вісімкове", "ad": "шістнадцяткове число, яке слід перетворити!кількість символів, які використовуються"}, "IMABS": {"a": "(компл_число)", "d": "Повертає абсолютне значення (модуль) комплексного числа", "ad": "комплексне число, для якого слід отримати абсолютне значення"}, "IMAGINARY": {"a": "(компл_число)", "d": "Повертає коефіцієнт уявної частини комплексного числа", "ad": "комплексне число, для якого слід обчислити коефіцієнт уявної частини"}, "IMARGUMENT": {"a": "(компл_число)", "d": "Повертає аргумент q, кут, виражений у радіанах", "ad": "комплексне число, для якого слід обчислити аргумент"}, "IMCONJUGATE": {"a": "(компл_число)", "d": "Повертає комплексне спряжене комплексного числа", "ad": "комплексне число, для якого слід обчислити спряжене"}, "IMCOS": {"a": "(компл_число)", "d": "Повертає косинус комплексного числа", "ad": "комплексне число, для якого слід обчислити косинус"}, "IMCOSH": {"a": "(компл_число)", "d": "Повертає гіперболічний косинус комплексного числа", "ad": "являє собою комплексне число, для якого потрібно обчислити гіперболічний косинус"}, "IMCOT": {"a": "(компл_число)", "d": "Повертає котангенс комплексного числа", "ad": "являє собою комплексне число, для якого потрібно обчислити котангенс"}, "IMCSC": {"a": "(компл_число)", "d": "Повертає косеканс комплексного числа", "ad": "являє собою комплексне число, для якого потрібно обчислити косеканс"}, "IMCSCH": {"a": "(компл_число)", "d": "Повертає гіперболічний косеканс комплексного числа", "ad": "являє собою комплексне число, для якого потрібно обчислити гіперболічний косеканс"}, "IMDIV": {"a": "(компл_число1; компл_число2)", "d": "Повертає частку двох комплексних чисел", "ad": "комплексний чисельник або ділене!комплексний знаменник або дільник"}, "IMEXP": {"a": "(компл_число)", "d": "Повертає експоненту комплексного числа", "ad": "комплексне число, для якого слід обчислити експоненту"}, "IMLN": {"a": "(компл_число)", "d": "Повертає натуральний логарифм комплексного числа", "ad": "комплексне число, для якого слід обчислити натуральний логарифм"}, "IMLOG10": {"a": "(компл_число)", "d": "Повертає десятковий логарифм комплексного числа", "ad": "комплексне число, для якого слід обчислити десятковий логарифм"}, "IMLOG2": {"a": "(компл_число)", "d": "Повертає логарифм комплексного числа за основою 2", "ad": "комплексне число, для якого слід обчислити логарифм за основою 2"}, "IMPOWER": {"a": "(компл_число; число)", "d": "Повертає комплексне число, піднесене до цілого степеня", "ad": "комплексне число, яке слід піднести до степеня!степінь, до якого слід піднести комплексне число"}, "IMPRODUCT": {"a": "(компл_число1; [компл_число2]; ...)", "d": "Повертає добуток від 1 до 255 комплексних чисел", "ad": "компл_число1, компл_число2,... можна помножити від 1 до 255 комплексних чисел."}, "IMREAL": {"a": "(компл_число)", "d": "Повертає коефіцієнт дійсної частини комплексного числа", "ad": "комплексне число, для якого слід обчислити коефіцієнт дійсної частини"}, "IMSEC": {"a": "(компл_число)", "d": "Повертає секанс комплексного числа", "ad": "являє собою комплексне число, для якого потрібно обчислити секанс"}, "IMSECH": {"a": "(компл_число)", "d": "Повертає гіперболічний секанс комплексного числа", "ad": "являє собою комплексне число, для якого потрібно обчислити гіперболічний секанс"}, "IMSIN": {"a": "(компл_число)", "d": "Повертає синус комплексного числа", "ad": "комплексне число, для якого слід обчислити синус"}, "IMSINH": {"a": "(компл_число)", "d": "Повертає гіперболічний синус комплексного числа", "ad": "являє собою комплексне число, для якого потрібно обчислити гіперболічний синус"}, "IMSQRT": {"a": "(компл_число)", "d": "Повертає квадратний корінь комплексного числа", "ad": "комплексне число, з якого слід обчислити квадратний корінь"}, "IMSUB": {"a": "(компл_число1; компл_число2)", "d": "Повертає різницю двох комплексних чисел", "ad": "комплексне число, від якого віднімається компл_число2!комплексне число, яке віднімається від компл_числа1"}, "IMSUM": {"a": "(компл_число1; [компл_число2]; ...)", "d": "Повертає суму комплексних чисел", "ad": "можна складати від 1 до 255 комплексних чисел"}, "IMTAN": {"a": "(компл_число)", "d": "Повертає тангенс комплексного числа", "ad": "являє собою комплексне число, для якого потрібно обчислити тангенс"}, "OCT2BIN": {"a": "(число; [розряди])", "d": "Перетворює вісімкове число на двійкове", "ad": "вісімкове число, яке слід перетворити!кількість символів, які використовуються"}, "OCT2DEC": {"a": "(число)", "d": "Перетворює вісімкове число на десяткове", "ad": "вісімкове число, яке слід перетворити"}, "OCT2HEX": {"a": "(число; [розряди])", "d": "Перетворює вісімкове число на шістнадцяткове", "ad": "вісімкове число, яке слід перетворити!кількість символів, які використовуються"}, "DAVERAGE": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Обчислює середнє всіх значень стовпця списку або бази даних, які відповідають указаним умовам", "ad": "діапазон клітинок, які утворюють список або базу даних. База даних - це список споріднених даних!заголовок стовпця у подвійних лапках або номер стовпця у списку!діапазон клітинок, які містять указані умови. До діапазону входять заголовок стовпця та одна клітинка з умовою"}, "DCOUNT": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Підраховує кількість клітинок з числами у стовпці записів бази даних, які відповідають указаним умовам", "ad": "діапазон клітинок, які утворюють список або базу даних. База даних - це список споріднених даних!заголовок стовпця у подвійних лапках або номер стовпця у списку!діапазон клітинок, які містять указані умови. До діапазону входять заголовок стовпця та одна клітинка з умовою"}, "DCOUNTA": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Підраховує кількість непустих клітинок у стовпці записів бази даних, які відповідають указаним умовам", "ad": "діапазон клітинок, які утворюють список або базу даних. База даних - це список споріднених даних!заголовок стовпця у подвійних лапках або номер стовпця у списку!діапазон клітинок, які містять указані умови. До діапазону входять заголовок стовпця та одна клітинка з умовою"}, "DGET": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Витягає з бази даних один запис, який відповідає вказаним умовам", "ad": "діапазон клітинок, які утворюють список або базу даних. База даних - це список споріднених даних!заголовок стовпця у подвійних лапках або номер стовпця у списку!діапазон клітинок, які містять указані умови. До діапазону входять заголовок стовпця та одна клітинка з умовою"}, "DMAX": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Повертає найбільше значення у стовпці записів бази даних, які відповідають указаним умовам", "ad": "діапазон клітинок, які утворюють список або базу даних. База даних - це список споріднених даних!заголовок стовпця у подвійних лапках або номер стовпця у списку!діапазон клітинок, які містять указані умови. До діапазону входять заголовок стовпця та одна клітинка з умовою"}, "DMIN": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Повертає найменше значення у стовпці записів бази даних, які відповідають указаним умовам", "ad": "діапазон клітинок, які утворюють список або базу даних. База даних - це список споріднених даних!заголовок стовпця у подвійних лапках або номер стовпця у списку!діапазон клітинок, які містять указані умови. До діапазону входять заголовок стовпця та одна клітинка з умовою"}, "DPRODUCT": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Перемножує значення у стовпці записів бази даних, які відповідають указаним умовам", "ad": "діапазон клітинок, які утворюють список або базу даних. База даних - це список споріднених даних!заголовок стовпця у подвійних лапках або номер стовпця у списку!діапазон клітинок, які містять указані умови. До діапазону входять заголовок стовпця та одна клітинка з умовою"}, "DSTDEV": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Обчислює стандартне відхилення на основі вибірки з виділених записів бази даних", "ad": "діапазон клітинок, які утворюють список або базу даних. База даних - це список споріднених даних!заголовок стовпця в подвійних лапках або номер стовпця у списку!діапазон клітинок, які містять указані умови. До діапазону входять заголовок стовпця та одна клітинка з умовою"}, "DSTDEVP": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Обчислює стандартне відхилення на основі генеральної сукупності з виділених записів бази даних", "ad": "діапазон клітинок, які утворюють список або базу даних. База даних - це список споріднених даних!заголовок стовпця у подвійних лапках або номер стовпця у списку!діапазон клітинок, які містять указані умови. До діапазону входять заголовок стовпця та одна клітинка з умовою"}, "DSUM": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Складає числа у стовпці записів бази даних, які відповідають указаним умовам", "ad": "діапазон клітинок, які утворюють список або базу даних. База даних - це список споріднених даних!заголовок стовпця у подвійних лапках або номер стовпця у списку!діапазон клітинок, які містять указані умови. До діапазону входять заголовок стовпця та одна клітинка з умовою"}, "DVAR": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Обчислює дисперсію на основі вибірки з виділених записів бази даних", "ad": "діапазон клітинок, які утворюють список або базу даних. База даних - це список споріднених даних!заголовок стовпця в подвійних лапках або номер стовпця у списку!діапазон клітинок, які містять указані умови. До діапазону входять заголовок стовпця та одна клітинка з умовою"}, "DVARP": {"a": "(база_даних; поле; критер<PERSON>й)", "d": "Обчислює дисперсію на основі генеральної сукупності з виділених записів бази даних", "ad": "діапазон клітинок, які утворюють список або базу даних. База даних - це список споріднених даних!заголовок стовпця у подвійних лапках або номер стовпця у списку!діапазон клітинок, які містять указані умови. До діапазону входять заголовок стовпця та одна клітинка з умовою"}, "CHAR": {"a": "(число)", "d": "Повертає символ з указаним кодом із набору символів, установленого для вашого комп'ютера", "ad": "число від 1 до 255, яке вказує потрібний символ"}, "CLEAN": {"a": "(текст)", "d": "Видаляє з тексту всі недруковані символи", "ad": "будь-яка інформація на аркуші, з якої необхідно видалити недруковані символи"}, "CODE": {"a": "(текст)", "d": "Повертає числовий код першого символу в текстовому рядку для набору символів, що використовується у вашому комп'ютері", "ad": "текст, у якому необхідно знайти код першого символу"}, "CONCATENATE": {"a": "(текст1; [текст2]; ...)", "d": "Поєднує кілька текстових рядків у один", "ad": "від 1 до 255 текстових рядків, які необхідно поєднати; можуть бути текстовими рядками, числами або посиланнями на окремі клітинки"}, "CONCAT": {"a": "(текст1; ...)", "d": "Об’єднання списку або діапазону текстових рядків", "ad": "від 1 до 254 текстових рядків або діапазонів, які потрібно об’єднати в єдиний текстовий рядок"}, "DOLLAR": {"a": "(число; [десяткові_знаки])", "d": "Перетворює число на текст, використовуючи грошовий формат", "ad": "число, посилання на клітинку з числом або формула, яка обчислюється в число!кількість цифр справа від десяткової коми. За потреби число округлюється; якщо пропущено, приймається кількість_знаків = 2"}, "EXACT": {"a": "(текст1; текст2)", "d": "Перевіряє, чи збігаються два текстові рядки, і повертає значення TRUE або FALSE. Великі й малі букви розрізняються", "ad": "перший текстовий рядок!другий текстовий рядок"}, "FIND": {"a": "(шуканий_текст; текст_перегляду; [поч_позиція])", "d": "Повертає позицію початку шуканого текстового рядка в тексті перегляду, який його містить. Великі й малі букви розрізняються", "ad": "текст, який необхідно знайти. Щоб знайти перший символ у тексті перегляду, укажіть пустий рядок (дві подвійних лапки). Символи узагальнення використовувати неможливо!текст, який містить шуканий текст!позиція, з якої починається пошук. Перший символ у тексті_перегляду має позицію 1. Якщо не вказано, приймається позиція початку пошуку 1"}, "FINDB": {"a": "(шуканий_текст; текст_перегляду; [поч_позиція])", "d": "Знаходить один рядок тексту у другому рядку тексту та повертає позицію початку першого рядка тексту від першого символу другого рядка тексту, передбачена для використання з мовами, що мають набір двобайтних символів (DBCS) - японська, китайська і корейська", "ad": "текст, який необхідно знайти. Щоб знайти перший символ у тексті перегляду, укажіть пустий рядок (дві подвійних лапки). Символи узагальнення використовувати неможливо!текст, який містить шуканий текст!позиція, з якої починається пошук. Перший символ у тексті_перегляду має позицію 1. Якщо не вказано, приймається позиція початку пошуку 1"}, "FIXED": {"a": "(число; [кількість_знаків]; [без_розділювачів])", "d": "Округлює число до заданої кількості десяткових знаків і перетворює його на текст", "ad": "число, яке слід округлити та перетворити на текст!кількість цифр справа від десяткової коми. Якщо не вказано, вважається, що кількість_знаків = 0!логічне значення: TRUE – використовувати розділювачі розрядів у повернутому тексті; FALSE або пропущено – не використовувати"}, "LEFT": {"a": "(текст; [кількість_символів])", "d": "Повертає задану кількість символів, вибрану з початку текстового рядка", "ad": "текстовий рядок, з якого необхідно вибрати символи!кількість символів, яку необхідно вибрати; якщо не вказано, приймається 1"}, "LEFTB": {"a": "(текст; [кількість_символів])", "d": "Повертає перший символ або символи в текстовому рядку, залежно від заданої кількості байтів, передбачена для використання з мовами, що мають набір двобайтних символів (DBCS) - японська, китайська і корейська", "ad": "текстовий рядок, з якого необхідно вибрати символи!кількість символів, яку необхідно вибрати; якщо не вказано, приймається 1"}, "LEN": {"a": "(текст)", "d": "Повертає кількість символів у текстовому рядку", "ad": "текст, довжину якого необхідно знайти. Пробіли вважаються символами"}, "LENB": {"a": "(текст)", "d": "Повертає кількість байтів, використаних для відображення символів у текстовому рядку, передбачена для використання з мовами, що мають набір двобайтних символів (DBCS) - японська, китайська і корейська", "ad": "текст, довжину якого необхідно знайти. Пробіли вважаються символами"}, "LOWER": {"a": "(текст)", "d": "Перетворює всі букви в текстовому рядку на малі", "ad": "текст, букви якого необхідно перетворити на малі. Символи, які не є буквами, не змінюються"}, "MID": {"a": "(текст; поч_позиція; кількість_символів)", "d": "Повертає задану кількість символів, вибрану з рядка тексту, починаючи з указаної позиції", "ad": "текстовий рядок, з якого необхідно вибрати символи!позиція, починаючи з якої необхідно вибрати символи. Перший символ у тексті має позицію 1!кількість символів, яку необхідно вибрати з тексту"}, "MIDB": {"a": "(текст; поч_позиція; кількість_символів)", "d": "Повертає задану кількість символів із рядка тексту, починаючи з указаної позиції, на основі заданої кількості байтів, передбачена для використання з мовами, що мають набір двобайтних символів (DBCS) - японська, китайська і корейська", "ad": "текстовий рядок, з якого необхідно вибрати символи!позиція, починаючи з якої необхідно вибрати символи. Перший символ у тексті має позицію 1!кількість символів, яку необхідно вибрати з тексту"}, "NUMBERVALUE": {"a": "(текст; [десятковий_роздільник]; [груповий_роздільник])", "d": "Перетворює текст на число незалежно від локалізації", "ad": "являє собою рядок вираження числа, яке потрібно перетворити!являє собою символ, який використовується як десятковий роздільник у рядку!являє собою символ, який використовується як груповий роздільник у рядку"}, "PROPER": {"a": "(текст)", "d": "Перетворює регістр текстового рядка; регістр першої букви в кожному слові перетворено на верхній, а регістр решти букв у слові – на нижній", "ad": "текст у лапках, формула, що повертає текст, або посилання на клітинку, що містить текст, регістр частини якого слід перетворити на верхній"}, "REPLACE": {"a": "(старий_текст; поч_поз; кількість_символів; новий_текст)", "d": "Замінює частину текстового рядка на інший текст", "ad": "текст, в якому необхідно зробити заміну!позиція символу у старому_тексті, починаючи з якої необхідно замінити на новий_текст!кількість символів у старому_тексті, яку необхідно замінити!рядок, на який буде замінено частину старого_тексту"}, "REPLACEB": {"a": "(старий_текст; поч_поз; кількість_символів; новий_текст)", "d": "Замінює частину текстового рядка на інший текст, виходячи з кількості вказаних байтів, передбачена для використання з мовами, що мають набір двобайтних символів (DBCS) - японська, китайська і корейська", "ad": "текст, в якому необхідно зробити заміну!позиція символу у старому_тексті, починаючи з якої необхідно замінити на новий_текст!кількість символів у старому_тексті, яку необхідно замінити!рядок, на який буде замінено частину старого_тексту"}, "REPT": {"a": "(текст; кількість_повторів)", "d": "Повторює текст задану кількість разів. Функція REPT використовується для заповнення клітинки повторюваними текстовими рядками", "ad": "повторюваний текст!додатне число, яке вказує кількість повторів тексту"}, "RIGHT": {"a": "(текст; [кількість_символів])", "d": "Повертає задану кількість символів, вибрану з кінця текстового рядка", "ad": "текстовий рядок, з якого необхідно вибрати символи!кількість символів, яку необхідно вибрати; якщо не вказано, приймається 1"}, "RIGHTB": {"a": "(текст; [кількість_символів])", "d": "Повертає останній символ або символи в текстовому рядку, залежно від указаної кількості байтів, передбачена для використання з мовами, що мають набір двобайтних символів (DBCS) - японська, китайська і корейська", "ad": "текстовий рядок, з якого необхідно вибрати символи!кількість символів, яку необхідно вибрати; якщо не вказано, приймається 1"}, "SEARCH": {"a": "(шуканий_текст; текст_перегляду; [поч_позиція])", "d": "Повертає позицію першого входження символу або рядка тексту (без урахування регістру), якщо читати зліва направо", "ad": "текст, який необхідно знайти. Можна використовувати символи узагальнення ? і *; для пошуку символів ? і * використовуються послідовності ~? і ~*!текст, в якому необхідно знайти шуканий_текст!позиція в тексті_для_пошуку, рахуючи з лівого краю, з якої починається пошук. Якщо не вказано, приймається 1"}, "SEARCHB": {"a": "(шуканий_текст; текст_перегляду; [поч_позиція])", "d": "Знаходить один текстовий рядок у межах другого рядка та повертає число стартової позиції першого текстового рядка з першого символу другого текстового рядка, передбачена для використання з мовами, що мають набір двобайтних символів (DBCS) - японська, китайська і корейська", "ad": "текст, який необхідно знайти. Можна використовувати символи узагальнення ? і *; для пошуку символів ? і * використовуються послідовності ~? і ~*!текст, в якому необхідно знайти шуканий_текст!позиція в тексті_для_пошуку, рахуючи з лівого краю, з якої починається пошук. Якщо не вказано, приймається 1"}, "SUBSTITUTE": {"a": "(текст; стар_текст; нов_текст; [номер_входження])", "d": "Замінює в текстовому рядку старий текст на новий", "ad": "текст або посилання на клітинку з текстом, в якому необхідно зробити заміну.!Наявний текст, в якому необхідно зробити заміну. Якщо регістри символів старого та нового текстів не збігаються, текст не буде замінено!текст, на який замінюється старий текст!номер входження старого тексту, для якого необхідно виконати заміну. Якщо не вказано, замінюються всі входження старого тексту"}, "T": {"a": "(значення)", "d": "Перевіряє, чи є значення текстом, і повертає цей текст, якщо так, або дві лапки (пустий рядок), якщо ні", "ad": "значення, яке перевіряють"}, "TEXT": {"a": "(значення; формат)", "d": "Перетворює значення на текст у певному форматі числа", "ad": "число, формулу, яка Повертає числове значення, або посилання на клітинку, яка містить числове значення!формат числа в діалоговому вікні \"Категорія\" на вкладці \"число\" у розділі \"Формат клітинок\""}, "TEXTJOIN": {"a": "(роздільник; пропускати_пусті; текст1; ...)", "d": "Об’єднання списку або діапазону текстових рядків за допомогою роздільника", "ad": "Символ або рядок, який буде вставлено між усіма елементами текстового рядка!якщо вказано значення TRUE (за замовчуванням), пусті рядки буде пропущено!текстові рядки або діапазони (1–252), які потрібно об’єднати"}, "TRIM": {"a": "(текст)", "d": "Видаляє з тексту всі пробіли, крім одиночних пробілів між словами", "ad": "текст, із якого необхідно видалити пробіли"}, "UNICHAR": {"a": "(число)", "d": "Повертає символ Юнікод, на який посилається задане числове значення", "ad": "являє собою число Юнікод, яке виражає символ"}, "UNICODE": {"a": "(текст)", "d": "Повертає число (кодову точку), відповідне першому символу тексту", "ad": "являє собою символ, для якого потрібно обчислити значення Юнікод"}, "UPPER": {"a": "(текст)", "d": "Перетворює всі букви в текстовому рядку на великі", "ad": "текст, букви якого необхідно перетворити на великі; може бути посиланням або текстовим рядком"}, "VALUE": {"a": "(текст)", "d": "Перетворює текстовий рядок, який представляє число, на число", "ad": "текст, взятий у лапки, або посилання на клітинку з текстом, який необхідно перетворити"}, "AVEDEV": {"a": "(число1; [число2]; ...)", "d": "Повертає середнє абсолютних значень відхилень точок даних від середнього. Аргументи можуть бути числами або іменами, масивами або посиланнями на клітинки з числами", "ad": "від 1 до 255 аргумен<PERSON>ів, для яких необхідно обчислити середнє абсолютних відхилень"}, "AVERAGE": {"a": "(число1; [число2]; ...)", "d": "Повертає середнє (арифметичне) аргу<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, які можуть бути числами або іменами, масивами або посиланнями на клітинки з числами", "ad": "від 1 до 255 аргументів, для яких необхідно обчислити середнє"}, "AVERAGEA": {"a": "(значення1; [значення2]; ...)", "d": "Повертає середнє (арифметичне) аргу<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, причому текст і логічні значення FALSE приймаються за 0, а логічні значення TRUE - за 1. Аргументи можуть бути числами, іменами, масивами або посиланнями", "ad": "від 1 до 255 аргументів, для яких необхідно обчислити середнє"}, "AVERAGEIF": {"a": "(діа<PERSON><PERSON>з<PERSON><PERSON>; крите<PERSON><PERSON><PERSON>; [діапазон_середн])", "d": "Повертає середнє (арифметичне) для клітинок, визначених цією умовою або критерієм", "ad": "діапазон обчислюваних клітинок!умова або критерій у вигляді числа, виразу або тексту, що визначає клітинки для обчислення середнього значення!дійсні клітинки для обчислення середнього значення. Якщо не вказано, використовуються клітинки, задані параметром \"діапазон\""}, "AVERAGEIFS": {"a": "(діапазон_середн; діапазон_критерію; критер<PERSON>й; ...)", "d": "Обчислює середнє (арифметичне) для клітинок, визначених певним набором умов або критеріїв", "ad": "дійсні клітинки для обчислення середнього значення!діапазон клітинок, які слід обчислити з використанням певної умови!умова або критерій у вигляді числа, виразу або тексту, що визначає клітинки для обчислення середнього значення"}, "BETADIST": {"a": "(x; альфа; бета; [A]; [Б])", "d": "Повертає інтегральну функцію щільності бета-ймовірності", "ad": "значення в інтервалі від A до B, для якого обчислюється функція!параметр розподілу має бути більшим за 0!параметр розподілу має бути більшим за 0!необов'язкова нижня межа інтервалу зміни x. Якщо не вказано, приймається A = 0!необов'язкова верхня межа інтервалу зміни x. Якщо не вказано, приймається B = 1"}, "BETAINV": {"a": "(імовірність; альфа; бета; [A]; [Б])", "d": "Повертає обернену функцію до інтегральної функції щільності бета-ймовірності (BETADIST)", "ad": "імовірність, пов'язана з бета-розподілом!параметр розподілу має бути більшим за 0!параметр розподілу має бути більшим за 0!необов'язкова нижня межа інтервалу зміни x. Якщо не вказано, приймається A = 0!необов'язкова верхня межа інтервалу зміни x. Якщо не вказано, приймається B = 1"}, "BETA.DIST": {"a": "(x; альфа; бета; сукупне; [A]; [B])", "d": "Повертає функцію бета-ймовірності розподілу", "ad": "значення в інтервалі від A до B, для якого слід обчислити функцію!параметр розподілу, має бути більший за 0!параметр розподілу, має бути більший за 0!логічне значення, яке визначає повернуту функцію: TRUE – інтегральна функція розподілу, FALSE – функція щільності ймовірності!необов'язкова нижня межа інтервалу зміни x. Якщо не вказано, вважається, що A = 0!необов'язкова верхня межа інтервалу зміни x. Якщо не вказано, вважається, що B = 1"}, "BETA.INV": {"a": "(імовірність; альфа; бета; [A]; [B])", "d": "Повертає обернену функцію до інтегральної функції щільності бета-ймовірності (BETA.DIST)", "ad": "імовірність, пов'язана з бета-розподілом!параметр розподілу, має бути більшим за 0!параметр розподілу, має бути більшим за 0!необов'язкова нижня межа інтервалу зміни x. Якщо не вказано, приймається A = 0!необов'язкова верхня межа інтервалу зміни x. Якщо не вказано, приймається B = 1"}, "BINOMDIST": {"a": "(кількість_успіхів; кількість_випробувань; імовірність_успіху; функція)", "d": "Повертає окреме значення біноміального розподілу", "ad": "кількість успішних випробувань!кількість незалежних випробувань!імовірність успіху кожного випробування!логічне значення, яке визначає вид функції: TRUE – інтегральна функція розподілу, FALSE – вагова функція розподілу"}, "BINOM.DIST": {"a": "(кількість_успіхів; кількість_випробувань; імовірність_успіху; функція)", "d": "Повертає окреме значення біноміального розподілу", "ad": "кількість успішних випробувань!кількість незалежних випробувань!імовірність успіху кожного випробування!логічне значення, яке визначає вид функції: TRUE - інтегральна функція розподілу, FALSE - вагова функція розподілу"}, "BINOM.DIST.RANGE": {"a": "(кількість_випробувань; імовірність_успіху; кількість_успіхів; [кількість_успіхів2])", "d": "Повертає імовірність результатів випробувань за допомогою біномного розподілу", "ad": "являє собою кількість незалежних випробувань!являє собою імовірність успіху кожного випробування!являє собою число успішних випробувань!якщо задати цю умову, функція повертає дані про імовірність кількості успішних випробувань у діапазоні \"кількість_успіхів\" – \"кількість_успіхів2\""}, "BINOM.INV": {"a": "(кількість_випробувань; імовірність_успіху; альфа)", "d": "Повертає найменше значення, для якого інтегральна біноміальна функція розподілу більша або дорівнює значенню критерію", "ad": "кількість випробувань Бернуллі!імовірність успіху кожного випробування, число в інтервалі від 0 до 1 включно!значення критерію, число в інтервалі від 0 до 1 включно"}, "CHIDIST": {"a": "(x; ступені_вільності)", "d": "Повертає правобічну ймовірність розподілу хі-квадрат", "ad": "значення, для якого потрібно обчислити розподіл, невід'ємне число!кількість ступенів вільності, число від 1 до 10^10, виключно з 10^10"}, "CHIINV": {"a": "(імовірність; ступені_вільності)", "d": "Повертає обернену величину правобічної ймовірності розподілу хі-квадрат", "ad": "імовірність, пов'язана з розподілом хі-квадрат, значення в інтервалі від 0 до 1 включно!кількість ступенів вільності, число від 1 до 10^10, виключаючи 10^10"}, "CHITEST": {"a": "(фактичний_діапазон; очікуваний_діапазон)", "d": "Повертає тест на незалежність: значення розподілу хі-квадрат для статистики та відповідних ступенів вільності", "ad": "діапазон даних із спостереженнями, які потрібно порівняти з очікуваними значеннями!діапазон даних із відношенням добутків підсумків рядків та стовпців до загального підсумка"}, "CHISQ.DIST": {"a": "(x; ступінь_свободи; сукупне)", "d": "Повертає лівобічну ймовірність розподілу хі-квадрат", "ad": "значення, для якого необхідно обчислити розподіл, невід'ємне число!ступінь свободи, число від 1 до 10^10, крім 10^10!логічне значення, яке визначає повернуту функцію: TRUE – інтегральна функція розподілу, FALSE – функція щільності ймовірності"}, "CHISQ.DIST.RT": {"a": "(x; ступінь_свободи)", "d": "Повертає лівобічну ймовірність розподілу хі-квадрат", "ad": "значення, для якого необхідно обчислити розподіл, невід'ємне число!ступінь свободи, число від 1 до 10^10, виключаючи 10^10"}, "CHISQ.INV": {"a": "(імовірність; ступінь_свободи)", "d": "Повертає обернене значення лівобічної ймовірності розподілу хі-квадрат", "ad": "імовірність, пов'язана з розподілом хі-квадрат, значення в діапазоні від 0 до 1 включно!кількість ступенів вільності, число від 1 до 10^10, виключаючи 10^10"}, "CHISQ.INV.RT": {"a": "(імовірність; ступінь_свободи)", "d": "Повертає обернене значення правобічної ймовірності розподілу хі-квадрат", "ad": "імовірність, пов'язана з розподілом хі-квадрат, значення в діапазоні від 0 до 1 включно!кількість ступенів вільності, число від 1 до 10^10, виключаючи 10^10"}, "CHISQ.TEST": {"a": "(фактичний_інтервал; очікуваний_інтервал)", "d": "Повертає тест на незалежність: значення розподілу хі-квадрат для статистичного розподілу та відповідної кількості ступенів вільності", "ad": "діапазон зі спостереженнями, які потрібно порівняти з очікуваними значеннями!діапазон з відношенням добутків підсумків рядків і стовпців до загального підсумка"}, "CONFIDENCE": {"a": "(альфа; станд_відхил; розмір)", "d": "Повертає довірчий інтервал для середнього генеральної сукупності, застосовуючи нормальний розподіл", "ad": "рівень значущості, який використовується для обчислення довірчого рівня; число, більше за 0 і менше за 1!стандартне відхилення генеральної сукупності для діапазону даних, вважається відомим. Стандартне відхилення має бути більшим за 0!розмір вибірки"}, "CONFIDENCE.NORM": {"a": "(альфа; станд_відхилення; розмір)", "d": "Повертає довірчий інтервал для середнього генеральної сукупності за допомогою нормального розподілу", "ad": "рівень значущості, який використовується для обчислення довірчого рівня; число, більше за 0 і менше за 1!стандартне відхилення генеральної сукупності для інтервалу даних; вважається відомим. Стандартне відхилення має бути більшим за 0!розмір вибірки"}, "CONFIDENCE.T": {"a": "(альфа; станд_відхилення; розмір)", "d": "Повертає довірчий інтервал для середнього сукупності за допомогою Т-розподілу Ст'юдента", "ad": "рівень значущості, який використовується для обчислення довірчого рівня; число, більше за 0 і менше за 1!стандартне відхилення генеральної сукупності для інтервалу даних; вважається відомим. Стандартне відхилення має бути більшим за 0!розмір вибірки"}, "CORREL": {"a": "(масив1; масив2)", "d": "Повертає коефіцієнт кореляції між двома сукупностями даних", "ad": "перший діапазон значень. Значення можуть бути числами, іменами, масивами або посиланнями на числа!другий діапазон значень. Значення можуть бути числами, іменами, масивами або посиланнями на числа"}, "COUNT": {"a": "(значення1; [значення2]; ...)", "d": "Підраховує кількість клітинок у діапазоні з числами", "ad": " від 1 до 255 аргум<PERSON><PERSON><PERSON><PERSON><PERSON>, які можуть містити або посилатися на різні типи даних, але підраховуються лише числа"}, "COUNTA": {"a": "(значення1; [значення2]; ...)", "d": "Підраховує кількість непустих клітинок у діапазоні", "ad": "від 1 до 255 аргументів, які представляють значення або клітинки і які необхідно підрахувати. Значення можуть бути довільного типу"}, "COUNTBLANK": {"a": "(діапазон)", "d": "Підраховує кількість пустих клітинок у діапазоні", "ad": "діапазон, в якому необхідно підрахувати пусті клітинки"}, "COUNTIF": {"a": "(діа<PERSON><PERSON><PERSON><PERSON><PERSON>; критер<PERSON><PERSON>)", "d": "Підраховує в діапазоні кількість непустих клітинок, які відповідають заданій умові", "ad": "діапазон, в якому підраховують непусті клітинки!умова у вигляді числа, виразу або тексту, яка визначає клітинки для підрахування"}, "COUNTIFS": {"a": "(діапазон_критерію; критер<PERSON><PERSON>; ...)", "d": "Перелічує кількість клітинок, визначених наявним набором умов", "ad": "діапазон клітинок, які слід перевірити з використанням певної умови!умова у вигляді числа, виразу або тексту, що визначає клітинки для перелічення"}, "COVAR": {"a": "(масив1; масив2)", "d": "Повертає коваріацію – середнє добутків відхилень для кожної пари точок даних двох наборів даних", "ad": "перший діапазон клітинок цілих чисел – числа, масиви або посилання, які містять числа!другий діапазон клітинок цілих чисел – числа, масиви або посилання, які містять числа"}, "COVARIANCE.P": {"a": "(масив1; масив2)", "d": "Повертає коваріацію сукупності — середнє попарних добутків відхилень", "ad": "перший діапазон цілих чисел - числа, масиви або посилання, які містять числа!другий діапазон цілих чисел — числа, масиви або посилання, які містять числа"}, "COVARIANCE.S": {"a": "(масив1; масив2)", "d": "Повертає коваріацію зразка — середнє попарне добутків відхилень", "ad": "перший діапазон цілих чисел - числа, масиви або посилання, які містять числа!другий діапазон цілих чисел — числа, масиви або посилання, які містять числа"}, "CRITBINOM": {"a": "(кількість_випробувань; імовірність_успіху; альфа)", "d": "Повертає найменше значення, для якого інтегральна біноміальна функція розподілу більша або дорівнює значенню критерію", "ad": "кількість випробувань Бернуллі!імовірність успіху кожного випробування, число в інтервалі від 0 до 1 включно!значення критерію, число в інтервалі від 0 до 1 включно"}, "DEVSQ": {"a": "(число1; [число2]; ...)", "d": "Повертає суму квадратів відхилень точок даних від середнього з вибірки", "ad": "від 1 до 255 аргу<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, масив або посилання на масив, для елементів якого обчислюється функція DEVSQ"}, "EXPONDIST": {"a": "(x; лямбда; інтегральна)", "d": "Повертає експоненційний розподіл", "ad": "значення функції, невід'ємне число!значення параметра, додатне число!логічне значення, яке визначає повернуту функцію: TRUE – інтегральна функція розподілу, FALSE – функція щільності ймовірності"}, "EXPON.DIST": {"a": "(x; лямбда; сукупне)", "d": "Повертає експоненційний розподіл", "ad": "значення функції, невід'ємне число!значення параметра, невід'ємне число!логічне значення, яке визначає повернуту функцію: TRUE - інтегральна функція розподілу, FALSE - функція щільності ймовірності"}, "FDIST": {"a": "(x; ступені_вільності1; ступені_вільності2)", "d": "Повертає (правобічний) F-розподіл імовірності (ступінь відхилення) для двох наборів даних", "ad": "значення, для якого потрібно обчислити функцію, невід'ємне число!чисельник ступенів вільності, число від 1 до 10^10, виключно з 10^10!знаменник ступенів вільності, число від 1 до 10^10, виключно з 10^10"}, "FINV": {"a": "(імовірність; ступені_вільності1; ступені_вільності2)", "d": "Повертає обернене значення для (правобічного) F-розподілу ймовірностей: якщо p = FDIST(x,...), то FINV(p,...) = x", "ad": "імовірність, пов'язана з F-інтегральним розподілом, число в інтервалі від 0 до 1 включно!чисельник ступенів вільності, число від 1 до 10^10, виключно з 10^10!знаменник ступенів вільності, число від 1 до 10^10, виключно з 10^10"}, "FTEST": {"a": "(масив1; масив2)", "d": "Повертає результат F-тесту – двобічну імовірність того, що відхилення двох масивів різняться незначно", "ad": "перший масив або діапазон даних – числа, іме<PERSON>, масиви або посилання, що містять числа (пробіли ігноруються)!другий масив або діапазон даних – числа, імена, масиви або посилання, які містять числа (пробіли ігноруються)"}, "F.DIST": {"a": "(x; ступінь_свободи1; ступінь_свободи2; сукупне)", "d": "Повертає (лівобічний) F-розподіл імовірності (ступінь відхилення) для двох наборів даних", "ad": "значення, для якого слід обчислити функцію, невід'ємне число!чисельник ступенів вільності, число від 1 до 10^10, крім 10^10!знаменник ступенів вільності, число від 1 до 10^10, крім 10^10!логічне значення, яке визначає повернуту функцію: TRUE — інтегральна функція розподілу, FALSE — функція щільності ймовірності"}, "F.DIST.RT": {"a": "(x; ступінь_свободи1; ступінь_свободи2)", "d": "Повертає (правобічний) F-розподіл імовірності (ступінь відхилення) для двох наборів даних", "ad": "значення, для якого слід обчислити функцію, невід'ємне число!чисельник ступенів вільності, число від 1 до 10^10, виключаючи 10^10!знаменник ступенів вільності, число від 1 до 10^10, виключаючи 10^10"}, "F.INV": {"a": "(імовірність; ступінь_свободи1; ступінь_свободи2)", "d": "Повертає обернене значення (лівобічного) F-розподілу ймовірності: якщо p = F.DIST(x,...), то F.INV(p,...) = x", "ad": "імовірність, пов'язана з F-інтегральним розподілом, число в інтервалі від 0 до 1 включно!чисельник ступенів вільності, число від 1 до 10^10, виключаючи 10^10!знаменник ступенів вільності, число від 1 до 10^10, виключаючи 10^10"}, "F.INV.RT": {"a": "(імовірність; ступінь_свободи1; ступінь_свободи2)", "d": "Повертає обернене значення (правобічного) F-розподілу ймовірності: якщо p = F.DIST.RT(x,...), то F.INV.RT(p,...) = x", "ad": "імовірність, пов'язана з F-інтегральним розподілом, число в інтервалі від 0 до 1 включно!чисельник ступенів вільності, число від 1 до 10^10, виключаючи 10^10!знаменник ступенів вільності, число від 1 до 10^10, виключаючи 10^10"}, "F.TEST": {"a": "(масив1; масив2)", "d": "Повертає результат F-тесту - двоб<PERSON>чну ймовірність того, що дисперсії двох масивів різняться незначно", "ad": "перший масив або діапазон - числа, масиви або посилання на числа (пробіли ігноруються)!другий масив або діапазон - числа, масиви або посилання на числа (пробіли ігноруються)"}, "FISHER": {"a": "(x)", "d": "Повертає перетворення Фішера", "ad": "значення, яке необхідно перетворити, число в інтервалі від -1 до 1, виключаючи -1 і 1"}, "FISHERINV": {"a": "(y)", "d": "Повертає обернене перетворення Фішера: якщо y = FISHER(x), то FISHERINV(y) = x", "ad": "значення, для якого необхідно виконати обернене перетворювання"}, "FORECAST": {"a": "(x; відомі_значення_y; відомі_значення_x)", "d": "Прогнозує значення відповідно до лінійного наближення, обчисленого за відомими значеннями", "ad": "точка даних (числова), для якої необхідно прогнозувати значення!залежний масив або діапазон числових даних!незалежний масив або діапазон числових даних. Дисперсія відомих значень x не має дорівнювати нулю"}, "FORECAST.ETS": {"a": "(цільова_дата; значення; часова_шкала; [сезонний_фактор]; [доповнення_даних]; [агрегація])", "d": "Повертає передбачене значення для вказаної цільової дати в майбутньому за допомогою методу експоненційного згладжування.", "ad": "точка даних, для якої програма Spreadsheet Editor передбачає значення. Має доповнювати закономірність решти значень на часовій шкалі.!масив або діапазон чисельних даних, для яких виконується передбачення.!незалежний масив або діапазон чисельних даних. Інтервал між датами часової шкали має бути узгоджений. Дати не мають бути нульові.!необов’язкове чисельне значення, що вказує тривалість сезонної закономірності. Значення 1 за замовчуванням указує, що сезонний фактор визначається автоматично.!необов’язкове значення для оброблення відсутніх значень. Якщо вказати 1 (значення за замовчуванням), відсутні значення буде інтерпольовано. Якщо вказати 0, відсутні значення буде замінено на нульові.!необов’язкове чисельне значення для агрегування кількох значень з однаковими позначками часу. Якщо значення не вказано, програма Spreadsheet Editor замінює значення на середні"}, "FORECAST.ETS.CONFINT": {"a": "(цільова_дата; значення; часова_шкала; [довірчий_рівень]; [сезонний_фактор]; [доповнення_даних]; [агрегація])", "d": "Повертає довірчий інтервал для передбаченого значення та вказаної цільової дати.", "ad": "точка даних, для якої програма Spreadsheet Editor передбачає значення. Має доповнювати закономірність решти значень на часовій шкалі.!масив або діапазон чисельних даних, для яких виконується передбачення.!незалежний масив або діапазон чисельних даних. Інтервал між датами часової шкали має бути узгоджений. Дати не мають бути нульові.!число від 0 до 1, що вказує довірчий рівень для довірчого інтервалу, який буде обчислено. Значення за замовчуванням – 0,95.!необов’язкове чисельне значення, що вказує тривалість сезонної закономірності. Значення 1 за замовчуванням указує, що сезонний фактор визначається автоматично.!необов’язкове значення для оброблення відсутніх значень. Якщо вказати 1 (значення за замовчуванням), відсутні значення буде інтерпольовано. Якщо вказати 0, відсутні значення буде замінено на нульові.!необов’язкове чисельне значення для агрегування кількох значень з однаковими позначками часу. Якщо значення не вказано, програма Spreadsheet Editor замінює значення на середні"}, "FORECAST.ETS.SEASONALITY": {"a": "(значення; часова_шкала; [доповнення_даних]; [агрегація])", "d": "Повертає тривалість повторюваної закономірності, яку програма виявила для вказаного часового ряду.", "ad": "масив або діапазон чисельних даних, для якого виконується передбачення.!незалежний масив або діапазон чисельних даних. Інтервал між датами часової шкали має бути узгоджений. Дати не мають бути нульові.!необов’язкове значення для обробки відсутніх значень. Якщо вказати 1, відсутні значення буде вставлено за інтерполяцією. Якщо вказати 0, відсутні значення буде замінено на нульові.!необов’язкове чисельне значення для агрегування кількох значень з однаковими позначками часу. Якщо значення не вказано, програма Spreadsheet Editor замінює значення на середні."}, "FORECAST.ETS.STAT": {"a": "(значення; часова_шкала; тип_статистики; [сезонність]; [доповнення_даних]; [агрегація])", "d": "Повертає запитану статистику для передбачення.", "ad": "масив або діапазон числових даних, для якого виконується прогнозування.!незалежний масив або діапазон числових даних. Інтервал між датами часової шкали має бути узгоджений. Дати не мають бути нульові.!число від 1 до 8, що вказує, яку статистику для розрахованого прогнозу поверне програма Spreadsheet Editor.!необов’язкове числове значення, що вказує тривалість сезонної закономірності. Значення 1 за замовчуванням указує, що сезонний фактор визначається автоматично.!необов’язкове значення для обробки відсутніх значень. Якщо вказати 1, відсутні значення буде вставлено за інтерполяцією. Якщо вказати 0, відсутні значення буде замінено на нульові.!необов’язкове числове значення для агрегування кількох значень з однаковими позначками часу. Якщо значення не вказано, програма Spreadsheet Editor замінює значення на середні."}, "FORECAST.LINEAR": {"a": "(x; відомі_значення_y; відомі_значення_x)", "d": "Прогнозує значення відповідно до лінійного наближення, обчисленого за відомими значеннями", "ad": "точка даних (числова), для якої необхідно прогнозувати значення!залежний масив або діапазон числових даних!незалежний масив або діапазон числових даних. Дисперсія відомих значень x не має дорівнювати нулю"}, "FREQUENCY": {"a": "(масив_даних; масив_секцій)", "d": "Обчислює частоту появи значень в іншому діапазоні значень і повертає вертикальний масив кількості екземплярів, що містить на один елемент більше ніж масив_секцій", "ad": "масив або посилання на сукупність значень, для яких необхідно обчислити частоту (пусті клітинки та текст ігноруються)!масив або посилання на інтервали, за якими потрібно розподілити значення з масиву_даних"}, "GAMMA": {"a": "(x)", "d": "Повертає значення гамма-функції", "ad": "являє собою значення, для якого потрібно обчислити гамма-функцію"}, "GAMMADIST": {"a": "(x; альфа; бета; функція)", "d": "Повертає гамма-розподіл", "ad": "значення, для якого необхідно обчислити розподіл, невід'ємне число!параметр розподілу, додатне число!параметр розподілу, додатне число. Якщо бета=1, GAMMADIST повертає стандартний гамма-розподіл!логічне значення, яке визначає вид повернутої функції: TRUE – інтегральна функція розподілу, FALSE або не вказано – вагова функція розподілу"}, "GAMMA.DIST": {"a": "(x; альфа; бета; сукупне)", "d": "Повертає гамма-розподіл", "ad": "значення, для якого слід обчислити розподіл, невід'ємне число!параметр розподілу, додатне число!параметр розподілу, додатне число. Якщо бета = 1, GAMMA.DIST повертає стандартний гамма-розподіл!логічне значення, яке визначає вид повернутої функції: TRUE – інтегральна функція розподілу; FALSE або не вказано – вагова функція ймовірності"}, "GAMMAINV": {"a": "(імовірність; альфа; бета)", "d": "Повертає обернений інтегральний гамма-розподіл: якщо p = GAMMADIST(x,...), то GAMMAINV(p,...) = x", "ad": "імовірність, пов'язана з гамма-розподілом, число в інтервалі від 0 до 1 включно!параметр розподілу, додатне число!параметр розподілу, додатне число. Якщо бета = 1, GAMMAINV повертає обернений стандартний гамма-розподіл"}, "GAMMA.INV": {"a": "(імовірність; альфа; бета)", "d": "Повертає обернений гамма-розподіл: якщо p = GAMMA.DIST(x,...), то GAMMA.INV(p,...) = x", "ad": "імовірність, пов’язана з гамма-розподілом, число в інтервалі від 0 до 1 включно!параметр розподілу, додатне число!параметр розподілу, додатне число. Якщо бета = 1, GAMMA.INV повертає обернений стандартний гамма-розподіл"}, "GAMMALN": {"a": "(x)", "d": "Повертає натуральний логарифм гамма-функції", "ad": "значення, для якого обчислюють GAMMALN, додатне число"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Повертає натуральний логарифм гамма-функції", "ad": "значення, для якого обчислюється GAMMALN.PRECISE, додатне число"}, "GAUSS": {"a": "(x)", "d": "Повертає значення на 0,5 менше, ніж стандартний нормальний інтегральний розподіл", "ad": "являє собою значення, для якого потрібно обчислити розподіл"}, "GEOMEAN": {"a": "(число1; [число2]; ...)", "d": "Повертає середнє геометричне елементів масиву або діапазону з додатних чисел", "ad": "від 1 до 255 чисел або імен, масивів або посилань на числа, для яких обчислюється середнє геометричне"}, "GROWTH": {"a": "(відомі_значення_y; [відомі_значення_x]; [нові_значення_x]; [конст])", "d": "Повертає значення відповідно до експоненційного наближення, обчисленого на основі відомих даних", "ad": "сукупність значень y, уже відомих у відношенні y = b*m^x, масив або діапазон додатних чисел!необов’язкова сукупність значень x, уже, можливо, відомих у відношенні y = b*m^x, масив або діапазон того самого розміру, що й значення y!нові значення x, для яких функція GROWTH повертає відповідні значення y!логічне значення: якщо TRUE, константа b обчислюється звичайним чином; якщо FALSE або не вказано, вважається, що b = 1"}, "HARMEAN": {"a": "(число1; [число2]; ...)", "d": "Повертає середнє гармонічне сукупності додатних чисел: обернену величину середнього арифметичного обернених величин", "ad": "від 1 до 255 чисе<PERSON>, м<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, імен або посилань на числа, для яких необхідно обчислити середнє гармонічне"}, "HYPGEOM.DIST": {"a": "(кількість_успіхів_у_вибірці; розмір_виборки; кількість_успіхів_у_сукупності; розмір_сукупності; сукупне)", "d": "Повертає гіпергеометричний розподіл", "ad": "кількість успішних спроб у вибірці!розмір вибірки!кількість успішних спроб у генеральній сукупності!розмір генеральної сукупності!логічне значення: TRUE – інтегральна функція розподілу, FALSE – функція щільності ймовірності"}, "HYPGEOMDIST": {"a": "(кількість_успіхів_у_вибірці; кількість_вибірок; кількість_успіхів_у_сукупності; кількість_сукупностей)", "d": "Повертає гіпергеометричний розподіл", "ad": "кількість успіхів у вибірці!розмір вибірки!кількість успіхів у генеральній сукупності!розмір генеральної сукупності"}, "INTERCEPT": {"a": "(відомі_значення_y; відомі_значення_x)", "d": "Знаходить точку перетину осі Y із лінією лінійної регресії, обчисленої на основі відомих значень x і y", "ad": "залежна множина спостережень або даних – числа, імена, масиви або посилання на числа!незалежна множина спостережень або даних – числа, імена, масиви або посилання на числа"}, "KURT": {"a": "(число1; [число2]; ...)", "d": "Повертає ексцес сукупності даних", "ad": "від 1 до 255 чисел або імен, масивів або посилань на клітинки з числами, для яких необхідно обчислити ексцес"}, "LARGE": {"a": "(масив; k)", "d": "Повертає k-е найбільше значення у сукупності даних (наприклад, п'яте за величиною)", "ad": "масив або діапазон, для якого необхідно обчислити k-е найбільше значення!місце за величиною (починаючи з найбільшого значення) в масиві або діапазоні"}, "LINEST": {"a": "(відомі_значення_y; [відомі_значення_x]; [конст]; [статистика])", "d": "Повертає параметри лінійного наближення, обчислені за методом найменших квадратів", "ad": "сукупність значень y, уже відомих у відношенні y = mx + b!необов’язкова сукупність значень x, уже, можливо, відомих у відношенні y = mx + b!логічне значення: якщо TRUE або не вказано, константа b обчислюється звичайним чином; якщо FALSE, вважається, що b = 0!логічне значення: якщо TRUE, повертається додаткова статистика регресії; якщо FALSE або не вказано, повертаються лише коефіцієнти m і константа b"}, "LOGEST": {"a": "(відомі_значення_y; [відомі_значення_x]; [конст]; [статистика])", "d": "Повертає параметри експоненційного наближення", "ad": "сукупність значень y, уже відомих у відношенні y = b*m^x!необов’язкова сукупність значень x, уже, можливо, відомих у відношенні y = b*m^x!логічне значення: якщо TRUE або не вказано, константа b обчислюється звичайним чином; якщо FALSE, приймається b = 1!логічне значення: якщо TRUE, повертається додаткова статистика регресії; якщо FALSE або не вказано, повертаються лише коефіцієнти m і константа b"}, "LOGINV": {"a": "(імовірність; середнє; станд_відхил)", "d": "Повертає обернену функцію інтегрального логарифмічно-стандартного розподілу x, де ln(x) стандартно розподілено з параметрами ''середнє'' та ''станд_відхил''", "ad": "імовірність, пов'язана з логарифмічно-нормальним розподілом, число в інтервалі від 0 до 1 включно!середнє ln(x)!стандартне відхилення ln(x), додатне число"}, "LOGNORM.DIST": {"a": "(x; середнє; станд_відхилення; сукупне)", "d": "Повертає логарифмічно-нормальний розподіл x, де ln(x) – нормальний розподіл із параметрами \"середнє\" та \"станд_відхилення\"", "ad": "значення, для якого слід обчислити функцію, додатне число!середнє ln(x)!стандартне відхилення ln(x), додатне число!логічне значення: TRUE – інтегральна функція розподілу, FALSE – функція щільності ймовірності"}, "LOGNORM.INV": {"a": "(імовірність; середнє; станд_відхил)", "d": "Повертає обернений інтегральний логарифмічно-нормальний розподіл x, де ln(x) - нормальний розподіл із параметрами ''середнє'' та ''станд_відхил''", "ad": "імовірність, пов'язана з логарифмічно-нормальним розподілом, число в інтервалі від 0 до 1 включно!середнє ln(x)!стандартне відхилення ln(x), додатне число"}, "LOGNORMDIST": {"a": "(x; середнє; стандартне_відхил)", "d": "Повертає інтегральний логарифмічно-нормальний розподіл x, де ln(x) стандартно розподілено з параметрами ''середнє'' та ''стандартне_відхил''", "ad": "значення, для якого потрібно обчислити функцію, додатне число!середнє ln(x)!стандартне відхилення ln(x), додатне число"}, "MAX": {"a": "(число1; [число2]; ...)", "d": "Повертає найбільше значення зі списку аргументів. Логічні значення та текст ігноруються", "ad": "від 1 до 255 чисел, пустих клітинок, логічних значень або чисел у текстовому форматі, серед яких необхідно відшукати максимальне значення"}, "MAXA": {"a": "(значення1; [значення2]; ...)", "d": "Повертає найбільше значення з набору значень. Логічні значення та текст не ігноруються", "ad": "від 1 до 255 чисел, пустих клітинок, логічних значень або чисел у текстовому форматі, серед яких необхідно відшукати максимальне значення"}, "MAXIFS": {"a": "(діапазон_максимумів; діапазон_критеріїв; критерії; ...)", "d": "Повертає максимальне значення серед клітинок, що визначаються за допомогою набору умов або критеріїв", "ad": "клітинки, у яких потрібно визначити максимальне значення!діапазон клітинок, які потрібно обчислити на основі певної умови!умова або критерій у вигляді числа, виразу або тексту, які визначають, які клітинки буде додано під час визначення максимального значення"}, "MEDIAN": {"a": "(значення1; [значення2]; ...)", "d": "Повертає медіану вказаних чисел (тобто число, яке міститься посередині їх списку)", "ad": "від 1 до 255 чисел або числових імен, масивів або посилань, для яких необхідно знайти медіану"}, "MIN": {"a": "(число1; [число2]; ...)", "d": "Повертає найменше значення зі списку аргументів. Логічні значення та текст ігноруються", "ad": "від 1 до 255 чисел, пустих клітинок, логічних значень або чисел у текстовому форматі, серед яких необхідно відшукати мінімальне значення"}, "MINA": {"a": "(значення1; [значення2]; ...)", "d": "Повертає найменше значення з набору значень. Логічні значення та текст не ігноруються", "ad": "від 1 до 255 чисел, пустих клітинок, логічних значень або чисел у текстовому форматі, серед яких необхідно відшукати мінімальне значення"}, "MINIFS": {"a": "(діапазон_мінімумів; діапазон_критеріїв; критерії; ...)", "d": "Повертає мінімальне значення серед клітинок, що визначаються за допомогою набору умов або критеріїв", "ad": "клітинки, у яких потрібно визначити мінімальне значення!діапазон клітинок, які потрібно обчислити на основі певної умови!умова або критерій у вигляді числа, виразу або тексту, які визначають, які клітинки буде додано під час визначення мінімального значення"}, "MODE": {"a": "(число1; [число2]; ...)", "d": "Повертає моду (найчастіше повторюване значення) масиву або діапазону даних", "ad": "від 1 до 255 чисел, <PERSON><PERSON><PERSON><PERSON>, масив<PERSON>в або посилань, які містять числа, для яких потрібно обчислити моду"}, "MODE.MULT": {"a": "(число1; [число2]; ...)", "d": "Повертає вертикальний масив найчастіше повторюваних значень у масиві або діапазоні даних. Для горизонтального масиву використовуйте =TRANSPOSE(MODE.MULT(число1,число2,...))", "ad": "числа в інтервалі від 1 до 255 або імена, масиви або посилання, для яких слід обчислити моду"}, "MODE.SNGL": {"a": "(число1; [число2]; ...)", "d": "Повертає моду (найчастіше повторюване значення) сукупності даних", "ad": "від 1 до 255 чисел, <PERSON><PERSON><PERSON><PERSON>, масивів або посилань на числа, для яких потрібно обчислити моду"}, "NEGBINOM.DIST": {"a": "(кількість_невдач; кількість_успіхів; імовірність_успіхів; сукупне)", "d": "Повертає від'ємний біноміальний розподіл – імовірність виникнення певної кількості невдач до досягнення певної кількості успіхів із певною ймовірністю успіху", "ad": "кількість невдалих спроб!граничне значення кількості успішних спроб!імовірність успіху, число в інтервалі від 0 до 1!логічне значення: TRUE – інтегральна функція розподілу, FALSE – вагова функція ймовірності"}, "NEGBINOMDIST": {"a": "(кількість_невдач; кількість_успіхів; імовірність_успіху)", "d": "Повертає від'ємний біноміальний розподіл – імовірність певної кількості невдач до досягнення певної кількості успіхів, з певною імовірністю успіху", "ad": "кількість відмов!граничне значення кількості успіхів!імовірність успіху, число в інтервалі від 0 до 1"}, "NORM.DIST": {"a": "(x; середнє; станд_відхилення; сукупне)", "d": "Повертає нормальний розподіл з указаними параметрами", "ad": "значення, для якого слід обчислити розподіл!арифметичне середнє розподілу!стандартне відхилення розподілу, додатне число!логічне значення, яке визначає вид повернутої функції: TRUE – інтегральна функція розподілу, FALSE – функція щільності ймовірності"}, "NORMDIST": {"a": "(x; середнє; стандартне_відхил; інтегральна)", "d": "Повертає нормальний інтегральний розподіл для указаного середнього та стандартного відхилення", "ad": "значення, для якого обчислюється розподіл!арифметичне середнє розподілу!стандартне відхилення розподілу, додатне число!логічне значення, яке визначає вид повернутої функції TRUE – інтегральна функція розподілу, FALSE – функція щільності імовірності"}, "NORM.INV": {"a": "(імовірність; середнє; станд_відхил)", "d": "Повертає обернений нормальний розподіл з указаними параметрами", "ad": "імовірність, яка відповідає нормальному розподілу, число в інтервалі від 0 до 1 включно!арифметичне середнє розподілу!стандартне відхилення розподілу, додатне число"}, "NORMINV": {"a": "(імовірність; середнє; станд_відхил)", "d": "Повертає обернений нормальний розподіл для указаного середнього та стандартного відхилення", "ad": "імовірність, яка відповідає нормальному розподілу, число в інтервалі від 0 до 1 включно!арифметичне середнє розподілу!стандартне відхилення розподілу, додатне число"}, "NORM.S.DIST": {"a": "(z; сукупне)", "d": "Повертає значення стандартного нормального розподілу (з нульовим середнім і одиничним стандартним відхиленням)", "ad": "значення, для якого необхідно обчислити розподіл!логічне значення, яке визначає повернуту функцію: TRUE – інтегральна функція розподілу, FALSE – функція щільності ймовірності"}, "NORMSDIST": {"a": "(z)", "d": "Повертає стандартний нормальний інтегральний розподіл (з нульовим середнім і одиничним стандартним відхиленням)", "ad": "значення, для якого обчислюється розподіл"}, "NORM.S.INV": {"a": "(імовірність)", "d": "Повертає обернене значення стандартного нормального розподілу (з нульовим середнім і одиничним стандартним відхиленням)", "ad": "імовірність, яка відповідає нормальному розподілу, число в інтервалі від 0 до 1 включно"}, "NORMSINV": {"a": "(імовірність)", "d": "Повертає обернене значення стандартного нормального розподілу (з нульовим середнім і одиничним стандартним відхиленням)", "ad": "імовірність, яка відповідає нормальному розподілу, число в інтервалі від 0 до 1 включно"}, "PEARSON": {"a": "(масив1; масив2)", "d": "Повертає коефіцієнт кореля<PERSON><PERSON><PERSON> Пірсона, r", "ad": "множина незалежних значень!множина залежних значень"}, "PERCENTILE": {"a": "(масив; k)", "d": "Повертає k-й процентиль значень діапазону", "ad": "масив або діапазон даних, який визначає відносне положення!значення процентилю від 0 до 1 включно"}, "PERCENTILE.EXC": {"a": "(масив; k)", "d": "Повертає k-й процентиль для значень діапазону, де k в інтервалі від 0 до 1, виключаючи", "ad": "масив або діапазон даних, який визначає відносне положення!значення процентилю в інтервалі від 0 до 1 включно"}, "PERCENTILE.INC": {"a": "(масив; k)", "d": "Повертає k-й процентиль для значень в діапазоні, де k в інтервалі від 0 до 1, виключаючи", "ad": "масив або діапазон даних, який визначає відносне положення!значення процентилю в інтервалі від 0 до 1 включно"}, "PERCENTRANK": {"a": "(масив; x; [точність])", "d": "Повертає ранг значення в наборі даних як відсоток набору даних", "ad": "масив або діапазон числових значень, який визначає відносне положення!значення, для якого потрібно обчислити ранг!необов'язкове значення, яке вказує кількість значущих цифр у повернутому відсотку, 3 цифри пропущено (0.xxx%)"}, "PERCENTRANK.EXC": {"a": "(масив; x; [точність])", "d": "Повертає ранг (відсоткову норму) значення в сукупності даних (від 0 до 1, виключаючи)", "ad": "масив або діапазон числових значень, який визначає відносне положення!значення, для якого потрібно обчислити ранг!необов’язкове значення, яке вказує кількість значущих цифр у повернутому відсотку, за замовчуванням приймається 3 (0.xxx%)"}, "PERCENTRANK.INC": {"a": "(масив; x; [точність])", "d": "Повертає ранг (відсоткову норму) значення в сукупності даних (від 0 до 1 включно)", "ad": "масив або діапазон числових значень, який визначає відносне положення!значення, для якого потрібно обчислити ранг!необов’язкове значення, яке вказує кількість значущих цифр у повернутому відсотку, за замовчуванням приймається 3 (0.xxx%)"}, "PERMUT": {"a": "(число; кількість_вибраних)", "d": "Повертає кількість перестановок для заданої кількості об'єктів, які можна вибрати з загального числа об'єктів", "ad": "загальне число об'єктів!кількість об'єктів у кожній перестановці"}, "PERMUTATIONA": {"a": "(число; кількість_вибраних)", "d": "Повертає кількість перестановок для заданої кількості об’єктів (з повторами), які можна вибрати із загального числа об’єктів", "ad": "являє собою загальне число об’єктів!являє собою число об’єктів у кожній перестановці"}, "PHI": {"a": "(x)", "d": "Повертає значення функції щільності ймовірності для стандартного нормального розподілу", "ad": "являє собою число, для якого потрібно обчислити щільність імовірності стандартного нормального розподілу"}, "POISSON": {"a": "(x; середнє; інтегральна)", "d": "Повертає розподіл Пуассона", "ad": "кількість подій!очікуване числове значення, додатне число!логічне значення, яке визначає повернуту функцію: TRUE – сукупна імовірність Пуассона, FALSE – функція вагової імовірності Пуассона"}, "POISSON.DIST": {"a": "(x; середнє; сукупне)", "d": "Повертає розподіл Пуассона", "ad": "кількість подій!очікуване числове значення, додатне число!логічне значення, яке визначає повернуту функцію: TRUE - інтегральна функція розподілу, FALSE - вагова функція розподілу"}, "PROB": {"a": "(інтервал_x; інтервал_імовірностей; нижня_межа; [верхня_межа])", "d": "Повертає ймовірність того, що значення діапазону знаходяться в указаних межах або на нижній межі", "ad": "інтервал числових значень x, з якими пов'язані ймовірності!сукупність імовірностей, які відповідають значенням в інтервалі_x; значення знаходяться між 0 і 1, виключаючи 0!нижня межа значення, для якого обчислюється ймовірність!необов'язкова верхня межа значення, для якого обчислюється ймовірність. Якщо не вказано, функція повертає ймовірність того, що всі значення в інтервалі_x дорівнюють нижній_межі"}, "QUARTILE": {"a": "(масив; частка)", "d": "Повертає квартиль набору даних", "ad": "масив або діапазон клітинок із числовими значеннями, для яких потрібно обчислити квартиль!значення: 0 – мін<PERSON>мальне, 1 – перший квартиль, 2 – середнє, 3 – третій квартиль, 4 – максимальне значення"}, "QUARTILE.INC": {"a": "(масив; частка)", "d": "Повертає квартиль сукупності даних на основі значень процентилю в інтервалі від 0 до 1 включно", "ad": "масив або діапазон числових значень, для якого слід обчислити квартиль!значення: 0 — мінімальне; 1 — перший квартиль; 2 — медіана; 3 — третій квартиль; 4 — максимальне значення"}, "QUARTILE.EXC": {"a": "(масив; частка)", "d": "Повертає квартиль сукупності даних на основі значень процентилю в інтервалі від 0 до 1, виключаючи", "ad": "масив або діапазон числових значень, для якого слід обчислити квартиль!значення: 0 — мінімальне; 1 — перший квартиль; 2 — медіана; 3 — третій квартиль; 4 — максимальне значення"}, "RANK": {"a": "(число; посилання; [порядок])", "d": "Повертає ранг числа у списку чисел: його місце за величиною відносно інших значень у списку", "ad": "число, для якого потрібно знайти ранг!масив або посилання на список чисел. Нечислові значення ігноруються!число: ранг у списку, відсортованому за спаданням = 0 або не вказано; ранг у списку, відсортованому за зростанням = будь-яке ненульове значення"}, "RANK.AVG": {"a": "(число; посилання; [порядок])", "d": "Повертає ранг числа у списку чисел: його місце за величиною серед інших значень у списку; якщо кілька значень мають такий самий ранг, повертається середній ранг", "ad": "число, для якого слід обчислити ранг!масив або посилання на список чисел. Нечислові значення ігноруються!порядок відліку: 0 або не вказано — ранг у списку, відсортованому за спаданням; будь-яке ненульове значення - ранг у списку, відсортованому за зростанням"}, "RANK.EQ": {"a": "(число; посилання; [порядок])", "d": "Повертає ранг числа у списку чисел: його місце за величиною серед інших значень у списку; якщо кілька значень мають такий самий ранг, повертається найбільший ранг", "ad": "число, для якого слід обчислити ранг!масив або посилання на список чисел. Нечислові значення ігноруються!порядок відліку: 0 або не вказано — ранг у списку, відсортованому за спаданням; будь-яке ненульове значення - ранг у списку, відсортованому за зростанням"}, "RSQ": {"a": "(відомі_значення_y; відомі_значення_x)", "d": "Повертає квадрат коефіцієнта кореляції Пірсона, обчисленого за вказаними точками", "ad": "масив або діапазон точок даних – числа, іме<PERSON>, масиви або посилання на числа!масив або діапазон точок даних – числа, імена, масиви або посилання на числа"}, "SKEW": {"a": "(число1; [число2]; ...)", "d": "Повертає асиметрію розподілу: характеристика ступеня асиметрії розподілу за середнім значенням", "ad": "від 1 до 255 чисел або імен, масивів або посилань на клітинки з числами, для яких необхідно обчислити асиметрію"}, "SKEW.P": {"a": "(число1; [число2]; ...)", "d": "Повертає асиметрію розподілу на основі сукупності: характеристика ступеня асиметрії розподілу навколо середнього значення", "ad": "від 1 до 254 чисел або імен, масивів чи посилань із числами, для яких потрібно обчислити асиметрію сукупності"}, "SLOPE": {"a": "(відомі_значення_y; відомі_значення_x)", "d": "Повертає нахил лінії лінійної регресії за вказаними точками даних", "ad": "масив або діапазон залежних точок даних – числа, імена, масиви або посилання на числа!масив або діапазон незалежних точок даних – числа, імена, масиви або посилання на числа"}, "SMALL": {"a": "(масив; k)", "d": "Повертає k-е найменше значення у сукупності даних (наприклад, п'яте з кінця за величиною)", "ad": "масив або діапазон, для якого необхідно обчислити k-е найменше значення!місце за величиною (починаючи з найменшого значення) в масиві або діапазоні"}, "STANDARDIZE": {"a": "(x; середнє; стандартне_відхил)", "d": "Повертає нормалізоване значення розподілу з указаними параметрами", "ad": "значення, яке необхідно нормалізувати!арифметичне середнє розподілу, додатне число!стандартне відхилення розподілу, додатне число"}, "STDEV": {"a": "(число1; [число2]; ...)", "d": "Обчислює стандартне відхилення на основі вибірки (ігноруючи логічні значення й текст)", "ad": "числа від 1 до 255, які відповідають вибірці з генеральної сукупності. Це можуть бути числа або посилання, які містять числа"}, "STDEV.P": {"a": "(число1; [число2]; ...)", "d": "Обчислює стандартне відхилення на основі генеральної сукупності (ігноруючи логічні значення й текст)", "ad": "від 1 до 255 аргументів, які відповідають генеральній сукупності. Це можуть бути числа або посилання на числа"}, "STDEV.S": {"a": "(число1; [число2]; ...)", "d": "Обчислює стандартне відхилення на основі вибірки (ігноруючи логічні значення й текст)", "ad": "від 1 до 255 аргументів, які відповідають вибірці з генеральної сукупності. Це можуть бути числа або посилання на числа"}, "STDEVA": {"a": "(значення1; [значення2]; ...)", "d": "Обчислює стандартне відхилення на основі вибірки, враховуючи логічні значення й текст. Текст і логічне значення FALSE мають значення 0, а логічне значення TRUE – 1", "ad": "від 1 до 255 значень, які відповідають вибірці з генеральної сукупності. Це можуть бути значення, імена, масиви або посилання на значення"}, "STDEVP": {"a": "(число1; [число2]; ...)", "d": "Обчислює стандартне відхилення на основі генеральної сукупності (ігноруючи логічні значення та текст)", "ad": "числа від 1 до 255, які відповідають генеральній сукупності. Це можуть бути числа або посилання, які містять числа"}, "STDEVPA": {"a": "(значення1; [значення2]; ...)", "d": "Обчислює стандартне відхилення на основі генеральної сукупності, враховуючи логічні значення й текст. Текст і логічне значення FALSE мають значення 0, а логічне значення TRUE – 1", "ad": "від 1 до 255 значень, які відповідають генеральній сукупності. Це можуть бути значення, імена, масиви або посилання на значення"}, "STEYX": {"a": "(відомі_значення_y; відомі_значення_x)", "d": "Повертає стандартну помилку прогнозованих значень y для кожного значення y в регресії", "ad": "масив або діапазон залежних точок даних – числа, імена, масиви або посилання на числа!масив або діапазон незалежних точок даних – числа, імена, масиви або посилання на числа"}, "TDIST": {"a": "(x; ступені_вільності; боки)", "d": "Повертає t-розподіл студента", "ad": "числове значення, для якого потрібно обчислити розподіл!ціле число — кількість ступенів вільності, які характеризують розподіл!кількість повернутих боків розподілу: 1 — однобічний розподіл, 2 — двобічний"}, "TINV": {"a": "(імовірність; ступені_вільності)", "d": "Повертає обернений двобічний t-розподіл студента", "ad": "імовірність, пов'язана із двобічним t-розподілом студента, число від 0 до 1 включно!додатне число – кількість ступенів вільності, які характеризують розподіл"}, "T.DIST": {"a": "(x; ступінь_свободи; сукупне)", "d": "Повертає лівобічний t-розподіл Ст'юдента", "ad": "числове значення, для якого слід обчислити розподіл!ціле число – кількість ступенів свободи, які характеризують розподіл!логічне значення, яке визначає вид повернутої функції: TRUE – інтегральна функція розподілу, FALSE – функція щільності ймовірності"}, "T.DIST.2T": {"a": "(x; ступінь_свободи)", "d": "Повертає двобічний t-розподіл Ст'юдента", "ad": "числове значення, для якого слід обчислити розподіл!ціле число — кількість ступенів свободи, які характеризують розподіл"}, "T.DIST.RT": {"a": "(x; ступінь_свободи)", "d": "Повертає правобічний t-розподіл Ст'юдента", "ad": "числове значення, для якого слід обчислити розподіл!ціле число — кількість ступенів свободи, які характеризують розподіл"}, "T.INV": {"a": "(імовірність; ступінь_свободи)", "d": "Повертає обернений лівобічний t-розподіл Ст'юдента", "ad": "імовірність, пов'язана із двобічним t-розподілом Ст'юдента, число в діапазоні від 0 до 1 включно!додатне число — кількість ступенів свободи, які характеризують розподіл"}, "T.INV.2T": {"a": "(імовірність; ступінь_свободи)", "d": "Повертає обернений двобічний t-розподіл Ст'юдента", "ad": "імовірність пов'язана із двобічним t-розподілом Ст'юдента, число в діапазоні від 0 до 1 включно!додатне число — кількість ступенів свободи, які характеризують розподіл"}, "T.TEST": {"a": "(масив1; масив2; боки; тип)", "d": "Повертає ймовірність, яка відповідає t-тесту Ст'юдента", "ad": "перша множина даних!друга множина даних!кількість повернутих боків розподілу: 1 - однобічний розподіл, 2 - двоб<PERSON>чний!вид t-тесту: 1 - парний, 2 - двопарний із рівною дисперсією (гомоскедастичний), 3 - двопарний із нерівною дисперсією"}, "TREND": {"a": "(відомі_значення_y; [відомі_значення_x]; [нові_значення_x]; [конст])", "d": "Повертає значення відповідно до лінійного наближення, обчисленого методом найменших квадратів на основі відомих даних", "ad": "сукупність значень y, уже відомих у відношенні y = mx + b!необов’язкова сукупність значень x, уже, можливо, відомих у відношенні y = mx + b, масив або діапазон того самого розміру, що й значення y!діапазон або масив нових значень x, для яких функція TREND повертає відповідні значення y!логічне значення: якщо TRUE або не вказано, константа b обчислюється звичайним чином; якщо FALSE, вважається, що b = 0"}, "TRIMMEAN": {"a": "(масив; частка)", "d": "Повертає середнє внутрішньої частки набору даних", "ad": "масив або діапазон, для якого обчислюють результат!відносна кількість точок даних, які виключаються з початку та з кінця набору"}, "TTEST": {"a": "(масив1; масив2; боки; тип)", "d": "Повертає імовірність, яка пов'язана із t-тестом студента", "ad": "перший набір даних!другий набір даних!кількість повернутих боків розподілу: 1 – однобічний розподіл, 2 – двобічний розподіл!вид t-тесту: 1 – парний, 2 – двухвибірковий з однаковим відхиленням (гомоскедастичний), 3 – двухвибірковий з неоднаковим відхиленням"}, "VAR": {"a": "(число1; [число2]; ...)", "d": "Обчислює відхилення на основі вибірки (ігноруючи логічні значення та текст)", "ad": "числові аргументи від 1 до 255, які відповідають вибірці з генеральної сукупності"}, "VAR.P": {"a": "(число1; [число2]; ...)", "d": "Обчислює дисперсію на основі генеральної сукупності (ігноруючи логічні значення й текст)", "ad": "від 1 до 255 числових аргументів, які відповідають генеральній сукупності"}, "VAR.S": {"a": "(число1; [число2]; ...)", "d": "Обчислює дисперсію на основі вибірки (ігноруючи логічні значення й текст)", "ad": "від 1 до 255 числових аргументів, які відповідають вибірці з генеральної сукупності"}, "VARA": {"a": "(значення1; [значення2]; ...)", "d": "Обчислює дисперсію на основі вибірки, враховуючи логічні значення й текст. Текст і логічне значення FALSE мають значення 0, а логічне значення TRUE – 1", "ad": "від 1 до 255 числових аргументів, які відповідають вибірці з генеральної сукупності"}, "VARP": {"a": "(число1; [число2]; ...)", "d": "Обчислює відхилення на основі генеральної сукупності (ігноруючи логічні значення та текст)", "ad": "числові аргументи від 1 до 255, які відповідають генеральній сукупності"}, "VARPA": {"a": "(значення1; [значення2]; ...)", "d": "Обчислює дисперсію на основі генеральної сукупності, враховуючи логічні значення й текст. Текст і логічне значення FALSE мають значення 0, а логічне значення TRUE – 1", "ad": "від 1 до 255 числових аргументів, які відповідають генеральній сукупності"}, "WEIBULL": {"a": "(x; альфа; бета; інтегральна)", "d": "Повертає розподіл Вейбулла", "ad": "значення, для якого необхідно обчислити розподіл, невід'ємне число!параметр розподілу, додатне число!параметр розподілу, додатне число!логічне значення, яке визначає вид повернутої функції: TRUE – інтегральна функція розподілу, FALSE – вагова функція імовірності"}, "WEIBULL.DIST": {"a": "(x; альфа; бета; сукупне)", "d": "Повертає розподіл Вейбулла", "ad": "значення, для якого потрібно обчислити розподіл, невід'ємне число!параметр розподілу, додатне число!параметр розподілу, додатне число!логічне значення, яке визначає вид повернутої функції: TRUE - інтегральна функція розподілу, FALSE - вагова функція розподілу"}, "Z.TEST": {"a": "(масив; x; [сигма])", "d": "Повертає двобічне P-значення z-тесту", "ad": "масив або діапазон даних, з якими порівнюють x!значення, яке перевіряється!відоме стандартне відхилення генеральної сукупності. Якщо не вказано, використовується стандартне відхилення вибірки"}, "ZTEST": {"a": "(масив; x; [сигма])", "d": "Повертає двобічне P-значення z-тесту", "ad": "масив або діапазон даних, з якими порівнюють x!значення, яке перевіряється!відоме стандартне відхилення генеральної сукупності. Якщо не вказано, використовується стандартне відхилення вибірки"}, "ACCRINT": {"a": "(дата_випуску; перша_виплата; дата_угоди; ставка; номінал; частота; [базис]; [метод_обчисл])", "d": "Повертає накопичений відсоток за цінними паперами з періодичною виплатою відсотків.", "ad": "дата випуску цінних паперів, виражена порядковим номером!дата першої виплати відсотків за цінними паперами, виражена порядковим номером!дата розрахунку за цінними паперами,виражена порядковим номером!річна відсоткова ставка для купонів за цінними паперами!номінальна вартість цінних паперів!кількість виплат за купонами протягом року!застосований спосіб обчислення дня!логічне значення: для накопиченого відсотку з дати випуску = TRUE або пропущено, для обчислення з останньої дати купонної виплати = FALSE"}, "ACCRINTM": {"a": "(дата_випуску; дата_угоди; ставка; номінал; [базис])", "d": "Повертає накопичений відсоток для цінних паперів із виплатою відсотків у момент погашення", "ad": "дата випуску цінних паперів, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!річна відсоткова ставка для купонів за цінними паперами!номінальна вартість цінних паперів!застосований спосіб обчислення дня"}, "AMORDEGRC": {"a": "(поч_вартість; дата_придбан; перш_період; зал_вартість; період; ставка; [базис])", "d": "Повертає пропорційну лінійну амортизацію активів для кожного звітного періоду", "ad": "вартість активів!дата придбання активів!дата завершення першого періоду!залишкова вартість активів наприкінці терміну експлуатації.!період!ставка амортизації!рік_базис : 0 для року з 360 днів, 1 для фактичного року, 3 для року з 365 днів."}, "AMORLINC": {"a": "(поч_вартість; дата_придбан; перш_період; зал_вартість; період; ставка; [базис])", "d": "Повертає пропорційну лінійну амортизацію активів для кожного звітного періоду", "ad": "вартість активів!дата придбання активів!дата завершення першого періоду!залишкова вартість активів наприкінці терміну експлуатації.!період!ставка амортизації!рік_базис : 0 для року з 360 днів, 1 для фактичного року, 3 для року з 365 днів."}, "COUPDAYBS": {"a": "(дата_угоди; дата_погаш; частота; [базис])", "d": "Повертає кількість днів від початку купонного періоду до дня розрахунку", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!кількість виплат за купонами протягом року! застосований спосіб обчислення дня"}, "COUPDAYS": {"a": "(дата_угоди; дата_погаш; частота; [базис])", "d": "Повертає кількість днів у купонному періоді, який містить дату розрахунку", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!кількість виплат за купонами протягом року! застосований спосіб обчислення дня"}, "COUPDAYSNC": {"a": "(дата_угоди; дата_погаш; частота; [базис])", "d": "Повертає кількість днів від дати розрахунку до наступної купонної дати", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!кількість виплат за купонами протягом року! застосований спосіб обчислення дня"}, "COUPNCD": {"a": "(дата_угоди; дата_погаш; частота; [базис])", "d": "Повертає наступну купонну дату після дати розрахунку", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!кількість виплат за купонами протягом року! застосований спосіб обчислення дня"}, "COUPNUM": {"a": "(дата_угоди; дата_погаш; частота; [базис])", "d": "Повертає кількість купонів, які можна оплатити між датою розрахунку та датою погашення", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!кількість виплат за купонами протягом року! застосований спосіб обчислення дня"}, "COUPPCD": {"a": "(дата_угоди; дата_погаш; частота; [базис])", "d": "Повертає попередню купонну дату перед датою розрахунку", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!кількість виплат за купонами протягом року! застосований спосіб обчислення дня"}, "CUMIPMT": {"a": "(ставка; кількість_періодів; поточна_сума; поч_період; кінц_період; тип)", "d": "Повертає сукупну суму відсотків, що виплачується між двома періодами", "ad": "відсоткова ставка!загальна кількість періодів виплат!поточна сума!перший період в обчисленні!останній період в обчисленні!час виплати"}, "CUMPRINC": {"a": "(ставка; кількість_періодів; поточна_сума; поч_період; кінц_період; тип)", "d": "Повертає сукупну суму, виплачувану за позикою між двома періодами", "ad": "відсоткова ставка!загальна кількість періодів виплат!поточна сума!перший період в обчисленні!останній період в обчисленні!час виплати"}, "DB": {"a": "(поч_вартість; зал_вартість; термін_експлуатації; період; [місяці])", "d": "Повертає величину амортизації активу за вказаний період із використанням методу фіксованого зменшення залишку", "ad": "початкова вартість активу!залишкова вартість активу наприкінці терміну експлуатації!кількість періодів амортизації активу (іноді зветься періодом нормальної експлуатації активу)!період, для якого необхідно обчислити амортизацію; слід указувати в тих самих одиницях, що й термін_експлуатації!кількість місяців у першому році. Якщо не вказано, приймається значення 12"}, "DDB": {"a": "(поч_вартість; зал_вартість; термін_експлуатації; період; [коефіцієнт])", "d": "Повертає величину амортизації активу за вказаний період із використанням методу подвійного зменшення залишку або іншого вказаного методу", "ad": "початкова вартість активу!залишкова вартість активу наприкінці терміну експлуатації!кількість періодів амортизації активу (іноді зветься періодом нормальної експлуатації активу)!період, для якого необхідно обчислити амортизацію; слід указувати в тих самих одиницях, що й термін_експлуатації!коефіцієнт зменшення залишку. Якщо коефіцієнт не вказано, приймається значення 2 (зменшення залишку вдвічі)"}, "DISC": {"a": "(дата_угоди; дата_погаш; ціна; погашення; [базис])", "d": "Повертає дисконтну ставку для цінних паперів", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!ціна цінних паперів за 100 грн. номінальної вартості!ціна дострокового погашення цінних паперів за 100 грн. номінальної вартості!застосований спосіб обчислення дня"}, "DOLLARDE": {"a": "(дріб_грн; дріб)", "d": "Перетворює ціну в гривнях, виражену як дріб, на ціну в гривнях, виражену як десяткове число", "ad": "число, виражене як дріб!ціле число, яке використовується як знаменник дробу"}, "DOLLARFR": {"a": "(дес_грн; дріб)", "d": "Перетворює ціну в гривнях, виражену як десяткове число, на ціну в гривнях, виражену як дріб", "ad": "десяткове число!ціле число, яке використовується як знаменник дробу"}, "DURATION": {"a": "(дата_угоди; дата_погаш; купон; прибуток; частота; [базис])", "d": "Повертає річну дюрацію для цінних паперів із періодичною виплатою відсотків", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!річна відсоткова ставка для купонів за цінними паперами!річний прибуток за цінними паперами!кількість виплат за купонами протягом року!застосований спосіб обчислення дня"}, "EFFECT": {"a": "(номін_ставка; кількість_пер)", "d": "Повертає річну ефективну відсоткову ставку", "ad": "номінальна відсоткова ставка!кількість періодів нарощення в році"}, "FV": {"a": "(ставка; кількість_періодів; виплата; [поточна_сума]; [тип])", "d": "Повертає майбутню вартість інвестиції на основі постійних періодичних виплат і постійної відсоткової ставки", "ad": "відсоткова ставка за період (наприклад, використовується значення квартальної відсоткової ставки 6%/4, якщо річна відсоткова ставка становить 6%)!загальна кількість періодів сплати за інвестицією!сума, яка сплачується за кожний період і не змінюється протягом усього часу виплат!поточна вартість або загальна сума, яка на цей час дорівнює сукупності майбутніх виплат; якщо не вказано, використовується нульове значення!значення, яке визначає час сплати: 0 або не вказано – наприкінці періоду, 1 – на початку періоду"}, "FVSCHEDULE": {"a": "(сума; розклад)", "d": "Повертає майбутнє значення початкової суми після застосування ряду складних процентних ставок", "ad": "поточне значення!масив застосованих процентних ставок"}, "INTRATE": {"a": "(дата_угоди; дата_погаш; інвестиція; погашення; [базис])", "d": "Повертає відсоткову ставку для повністю інвестованих цінних паперів", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!обсяг інвестицій у цінні папери!сума, яка отримується на момент погашення цінних паперів!застосований спосіб обчислення дня"}, "IPMT": {"a": "(ставка; період; кількість_періодів; поточна_сума; [майбутня_сума]; [тип])", "d": "Повертає суму сплати відсотків за інвестицією за вказаний період на основі постійних періодичних виплат і постійної відсоткової ставки", "ad": "відсоткова ставка за період (наприклад, використовуйте значення квартальної відсоткової ставки 6%/4, якщо річна відсоткова ставка становить 6%)!період, для якого необхідно обчислити суму сплати; має бути в діапазоні від 1 до значення аргументу \"кількість_періодів\"!загальна кількість періодів сплати за інвестицією!поточна вартість або загальна сума, яка на цей час дорівнює сукупності майбутніх виплат!майбутня вартість або касовий залишок, якого слід досягти після останньої виплати; якщо не вказано, використовується нульове значення!логічне значення, яке визначає час сплати: 0 або не вказано – наприкінці періоду, 1 – на початку періоду"}, "IRR": {"a": "(значення; [припущення])", "d": "Повертає внутрішню ставку прибутковості для ряду періодичних грошових переміщень", "ad": "масив або посилання на клітинки з числами, для яких необхідно обчислити внутрішню ставку прибутковості!передбачувана величина, наближена до результату функції; якщо не вказано, приймається значення 0,1 (10 відсотків)"}, "ISPMT": {"a": "(ставка; період; кількість_періодів; поточна_сума)", "d": "Повертає відсотки, що сплачуються за певний період інвестиції", "ad": "відсоткова ставка за період (наприклад, використовуйте значення квартальної відсоткової ставки 6%/4, якщо річна відсоткова ставка становить 6%)!період, для якого необхідно знайти відсоток!кількість періодів сплати за інвестицією!загальна сума, яка на цей час дорівнює сукупності майбутніх виплат"}, "MDURATION": {"a": "(дата_угоди; дата_погаш; купон; прибуток; частота; [базис])", "d": "Повертає модифіковану дюрацію Маколея для цінних паперів із очікуваною номінальною вартістю 100 грн.", "ad": "дата розрахунку за цінними паперамивиражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!річна відсоткова ставка для купонів за цінними паперами!річний прибуток за цінними паперами!кількість виплат за купонами протягом року!застосований спосіб обчислення дня"}, "MIRR": {"a": "(значення; ставка_фінанс; ставка_реінвест)", "d": "Повертає внутрішню ставку прибутковості від постійного руху грошових коштів з урахуванням як витрат на інвестування, так і відсотка реінвестування для надходжень", "ad": "масив або посилання на клітинки з числами, які представляють ряд регулярних виплат (від'ємні числа) і надходжень (додатні числа)!відсоткова ставка, що сплачується з коштів в обороті!відсоткова ставка, що отримується з коштів в обороті, у разі реінвестування"}, "NOMINAL": {"a": "(ефект_ставка; кількість_пер)", "d": "Повертає річну номінальну відсоткову ставку", "ad": "ефективна відсоткова ставка!кількість періодів нарощення на рік"}, "NPER": {"a": "(ставка; виплата; поточна_сума; [майбутня_сума]; [тип])", "d": "Повертає кількість періодів сплати за інвестицією на основі постійних періодичних виплат і постійної відсоткової ставки", "ad": "відсоткова ставка за період (наприклад, використовується значення квартальної відсоткової ставки 6%/4, якщо річна відсоткова ставка становить 6%)!сума, яка сплачується за кожний період і не змінюється протягом усього часу сплати інвестиції!поточна вартість або загальна сума, яка на цей час дорівнює сукупності майбутніх виплат!майбутня вартість або касовий залишок, якого слід досягти після останньої виплати; якщо не вказано, використовується нульове значення!логічне значення, яке визначає час сплати: 0 або не вказано – наприкінці періоду, 1 – на початку періоду"}, "NPV": {"a": "(ставка; значення1; [значення2]; ...)", "d": "Повертає чисту поточну вартість інвестиції на основі дисконтної ставки та вартості майбутніх виплат (від'ємні значення) і надходжень (додатні значення)", "ad": "дисконтна ставка на один період!від 1 до 254 виплат і надходжень, рівновіддалених у часі та здійснених наприкінці кожного періоду"}, "ODDFPRICE": {"a": "(дата_угоди; дата_погаш; дата_випуску; перша_виплата; ставка; прибуток; погашення; частота; [базис])", "d": "Повертає ціну за 100 грн. номінальної вартості цінних паперів із нерегулярним першим періодом", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!дата випуску цінних паперів, виражена порядковим номером!дата першого купона для цінних паперів, виражена порядковим номером!відсоткова ставка для цінних паперів!річний прибуток за цінними паперами!ціна дострокового погашення цінних паперів за 100 грн. номінальної вартості!кількість виплат за купонами протягом року!застосований спосіб обчислення дня"}, "ODDFYIELD": {"a": "(дата_угоди; дата_погаш; дата_випуску; перша_виплата; ставка; ціна; погашення; частота; [базис])", "d": "Повертає прибуток для цінних паперів із нерегулярним першим періодом", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!дата випуску цінних паперів, виражена порядковим номером!дата першого купона для цінних паперів, виражена порядковим номером!відсоткова ставка для цінних паперів!вартість цінних паперів!ціна дострокового погашення цінних паперів за 100 грн. номінальної вартості!кількість виплат за купонами протягом року!застосований спосіб обчислення дня"}, "ODDLPRICE": {"a": "(дата_угоди; дата_погаш; ост_виплата; ставка; прибуток; погашення; частота; [базис])", "d": "Повертає ціну за 100 грн. номінальної вартості цінних паперів із нерегулярним останнім періодом", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!дата останньої виплати за купонами для цінних паперів, виражена порядковим номером!відсоткова ставка для цінних паперів!річний прибуток за цінними паперами!ціна дострокового погашення цінних паперів за 100 грн. номінальної вартості!кількість виплат за купонами протягом року!застосований спосіб обчислення дня"}, "ODDLYIELD": {"a": "(дата_угоди; дата_погаш; ост_виплата; ставка; ціна; погашення; частота; [базис])", "d": "Повертає прибуток для цінних паперів із нерегулярним останнім періодом", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!дата останньої виплати за купонами для цінних паперів, виражена порядковим номером!відсоткова ставка для цінних паперів!вартість цінних паперів!ціна дострокового погашення цінних паперів за 100 грн. номінальної вартості!кількість виплат за купонами протягом року!застосований спосіб обчислення дня"}, "PDURATION": {"a": "(ставка; поточне_значення; майбутнє_значення)", "d": "Повертає число період<PERSON>в, потр<PERSON>бних, щоб інвестиція досягла вказаного значення", "ad": "являє собою відсоткову ставку за період.!являє собою поточне значення інвестиції!являє собою бажане значення інвестиції в майбутньому"}, "PMT": {"a": "(ставка; кількість_періодів; поточна_сума; [майбутня_сума]; [тип])", "d": "Повертає суму чергової виплати за позичкою на основі постійних періодичних виплат і постійної відсоткової ставки", "ad": "відсоткова ставка за період позички (наприклад, використовується значення квартальної відсоткової ставки 6%/4, якщо річна відсоткова ставка становить 6%)!загальна кількість виплат за позичкою!поточна вартість або загальна сума, яка на цей час дорівнює сукупності майбутніх виплат!майбутня вартість або касовий залишок, якого слід досягти після останньої виплати; якщо не вказано, використовується нульове значення!логічне значення, яке визначає час сплати: 0 або не вказано – наприкінці періоду, 1 – на початку періоду"}, "PPMT": {"a": "(ставка; період; кількість_періодів; поточна_сума; [майбутня_сума]; [тип])", "d": "Повертає величину плати на погашення основної суми за інвестицією на основі постійних періодичних виплат і незмінної відсоткової ставки", "ad": "відсоткова ставка за період (наприклад, використовується значення квартальної відсоткової ставки 6%/4, якщо річна відсоткова ставка становить 6%)!період; має бути в діапазоні від 1 до значення аргумента \"кількість_періодів\"!загальна кількість періодів сплати за інвестицією!поточна вартість або загальна сума, яка на цей час дорівнює сукупності майбутніх виплат!майбутня вартість або касовий залишок, якого слід досягти після останньої виплати!логічне значення, яке визначає час виплати: 0 або не вказано – наприкінці періоду, 1 – на початку періоду"}, "PRICE": {"a": "(дата_угоди; дата_погаш; ставка; прибуток; погашення; частота; [базис])", "d": "Повертає ціну за 100 грн. номінальної вартості цінних паперів із періодичною виплатою відсотків", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!річна купонна ставка для цінних паперів!річний прибуток за цінними паперами!ціна дострокового погашення цінних паперів за 100 грн. номінальної вартості!кількість виплат за купонами протягом року!застосований спосіб обчислення дня"}, "PRICEDISC": {"a": "(дата_угоди; дата_погаш; знижка; погашення; [базис])", "d": "Повертає ціну за 100 грн. номінальної вартості дисконтованих цінних паперів", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!дисконтна ставка для цінних паперів!ціна дострокового погашення цінних паперів за 100 грн. номінальної вартості!застосований спосіб обчислення дня"}, "PRICEMAT": {"a": "(дата_угоди; дата_погаш; дата_випуску; ставка; прибуток; [базис])", "d": "Повертає ціну за 100 грн. номінальної вартості цінних паперів із виплатою відсотків у момент погашення", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!дата випуску цінних паперів, виражена порядковим номером!відсоткова ставка цінних паперів у день випуску!річний прибуток за цінними паперами!застосований спосіб обчислення дня"}, "PV": {"a": "(ставка; кількість_періодів; виплата; [майбутня_сума]; [тип])", "d": "Повертає поточну вартість інвестиції – загальну суму, яка на цей час дорівнює сукупності майбутніх виплат", "ad": "відсоткова ставка за період (наприклад, використовується значення квартальної відсоткової ставки 6%/4, якщо річна відсоткова ставка становить 6%)!загальна кількість періодів сплати за інвестицією!сума, яка сплачується за кожний період і не змінюється протягом усього часу сплати!майбутня вартість або касовий залишок, якого слід досягти після останньої виплати!логічне значення, яке визначає час сплати: 0 або не вказано – наприкінці періоду; 1 - на початку періоду"}, "RATE": {"a": "(кількість_періодів; виплата; поточна_сума; [майбутня_сума]; [тип]; [прогноз])", "d": "Повертає відсоткову ставку за позичкою або інвестицією за один період (наприклад, використовується значення квартальної відсоткової ставки 6%/4, якщо річна відсоткова ставка становить 6%)", "ad": "загальна кількість періодів сплати за позичкою або інвестицією!сума, яка сплачується за кожний період і не змінюється протягом усього часу сплати позички або інвестиції!поточна вартість або загальна сума, яка на цей час дорівнює сукупності майбутніх виплат!майбутня вартість або касовий залишок, якого слід досягти після останньої виплати; якщо не вказано, використовується нульове значення!логічне значення, яке визначає час сплати: 1 – на початку періоду, 0 або не вказано – наприкінці періоду!орієнтовна величина ставки; якщо не вказано, використовується прогнозоване значення 0,1 (10%)"}, "RECEIVED": {"a": "(дата_угоди; дата_погаш; інвестиція; знижка; [базис])", "d": "Повертає суму, отриману на момент погашення повністю інвестованих цінних паперів", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!обсяг інвестицій у цінні папери!дисконтна ставка для цінних паперів!застосований спосіб обчислення дня"}, "RRI": {"a": "(кількість_періодів; поточне_значення; майбутнє_значення)", "d": "Повертає еквівалентне значення відсоткової ставки для приросту інвестиції", "ad": "являє собою кількість періодів для інвестиції!являє собою поточне значення інвестиції!являє собою майбутнє значення інвестиції"}, "SLN": {"a": "(поч_вартість; зал_вартість; термін_експлуатації)", "d": "Повертає величину амортизації активу за один період із використанням лінійного методу", "ad": "початкова вартість активу!залишкова вартість активу наприкінці терміну експлуатації!кількість періодів амортизації активу (іноді зветься періодом нормальної експлуатації активу)"}, "SYD": {"a": "(поч_вартість; зал_вартість; термін_експлуатації; період)", "d": "Повертає величину амортизації активу за вказаний період із використанням методу підсумовування річних чисел", "ad": "початкова вартість активу!залишкова вартість активу наприкінці терміну експлуатації!кількість періодів амортизації активу (іноді зветься періодом нормальної експлуатації активу)!період; слід указувати в тих самих одиницях, що й термін_експлуатації"}, "TBILLEQ": {"a": "(дата_угоди; дата_погаш; знижка)", "d": "Повертає еквівалентний облігації прибуток за казначейським векселем", "ad": "дата розрахунку за казначейським векселем, виражена порядковим номером!дата погашення казначейського векселя, виражена порядковим номером!дисконтна ставка для казначейського векселя"}, "TBILLPRICE": {"a": "(дата_угоди; дата_погаш; знижка)", "d": "Повертає ціну за 100 грн. номінальної вартості для казначейського векселя", "ad": "дата розрахунку за казначейським векселем, виражена порядковим номером!дата погашення казначейського векселя, виражена порядковим номером!дисконтна ставка для казначейського векселя"}, "TBILLYIELD": {"a": "(дата_угоди; дата_погаш; ціна)", "d": "Повертає прибуток за казначейським векселем", "ad": "дата розрахунку за казначейським векселем, виражена  порядковим номером!дата погашення казначейського векселя, виражена порядковим номером!ціна за 100 грн. номінальної вартості казначейського векселя"}, "VDB": {"a": "(поч_вартість; зал_вартість; термін_експлуатації; поч_період; кін_період; [коефіцієнт]; [не_переходити])", "d": "Повертає величину амортизації активу за будь-який указаний період, зокрема за часткові періоди, з використанням методу подвійного зменшення залишку або іншого вказаного методу", "ad": "початкова вартість активу!залишкова вартість активу наприкінці терміну експлуатації!кількість періодів амортизації активу (іноді зветься періодом нормальної експлуатації активу)!початковий період, для якого необхідно обчислити амортизацію, у тих самих одиницях, що й термін_експлуатації!кінцевий період, для якого необхідно обчислити амортизацію, у тих самих одиницях, що й термін_експлуатації!коефіцієнт зменшення залишку; якщо його не вказано, приймається значення 2 (зменшення залишку вдвічі)!чи необхідно переходити до застосування лінійної амортизації, коли амортизація більша за зменшуваний залишок: FALSE або не вказано – переходити, TRUE – не переходити"}, "XIRR": {"a": "(значення; дати; [прогноз])", "d": "Повертає внутрішнє значення прибутковості для запланованого руху грошових коштів", "ad": "рух грошових коштів, який відповідає запланованим платежам за датами!графік дат платежів, який відповідає руху грошових коштів!прогнозований результат функції XIRR"}, "XNPV": {"a": "(ставка; значення; дати)", "d": "Повертає чисту вартість для графіку руху грошових коштів", "ad": "дисконтна ставка, яка застосовується до руху грошових коштів!рух грошових коштів, який відповідає запланованим платежам за датами!графік дат платежів, який відповідає руху грошових коштів"}, "YIELD": {"a": "(дата_угоди; дата_погаш; ставка; ціна; погашення; частота; [базис])", "d": "Повертає прибуток за цінними паперами з періодичною виплатою відсотків", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!річна купонна ставка за цінними паперами!ціна за 100 грн. номінальної вартості цінних паперів!ціна дострокового погашення цінних паперів за 100 грн. номінальної вартості!кількість виплат за купонами протягом року!застосований спосіб обчислення дня"}, "YIELDDISC": {"a": "(дата_угоди; дата_погаш; ціна; погашення; [базис])", "d": "Повертає річний прибуток для дисконтних цінних паперів (наприклад, казначейський вексель)", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!ціна цінних паперів за 100 грн. номінальної вартості!ціна дострокового погашення цінних паперів за 100 грн. номінальної вартості!застосований спосіб обчислення дня"}, "YIELDMAT": {"a": "(дата_угоди; дата_погаш; дата_випуску; ставка; ціна; [базис])", "d": "Повертає річний прибуток за цінними паперами з виплатою відсотків у момент погашення", "ad": "дата розрахунку за цінними паперами, виражена порядковим номером!дата погашення цінних паперів, виражена порядковим номером!дата випуску цінних паперів, виражена порядковим номером!відсоткова ставка цінних паперів у день випуску!ціна цінних паперів за 100 грн. номінальної вартості!застосований спосіб обчислення дня"}, "ABS": {"a": "(число)", "d": "Повертає модуль (абсолютне значення) числа, тобто число без знака", "ad": "число, абсолютне значення якого необхідно знайти"}, "ACOS": {"a": "(число)", "d": "Повертає арккосинус числа в радіанах, у діапазоні від 0 до Пі. Арккосинус числа – це кут, косинус якого дорівнює числу", "ad": "косинус шуканого кута, значення в діапазоні від -1 до 1"}, "ACOSH": {"a": "(число)", "d": "Повертає обернений гіперболічний косинус (ареа-косинус) числа", "ad": "будь-яке дійсне число, яке більше або дорівнює 1"}, "ACOT": {"a": "(число)", "d": "Повертає арккотангенс числа в радіанах у діапазоні від 0 до Пі", "ad": "являє собою котангенс потрібного кута"}, "ACOTH": {"a": "(число)", "d": "Повертає обернений гіперболічний котангенс числа", "ad": "являє собою гіперболічний котангенс потрібного кута"}, "AGGREGATE": {"a": "(номер_функції; параметри; посилання1; ...)", "d": "Повертає сукупність для списку або бази даних", "ad": "число від 1 до 19, яке вказує функцію зведення для сукупності.!число від 0 до 7, яке вказує значення, що пропускаються під час обчислення сукупності!масив або діапазон числових даних, для якого слід обчислити сукупність!означає розташування в масиві; значення k-е найбільше, k-е найменше, k-е процентиль або k-е квартиль.!число від 1 до 19, яке вказує функцію зведення для сукупності.!число від 0 до 7, яке вказує значення, що пропускаються під час обчислення сукупності!від 1 до 253 діапазонів або посилань, для яких слід обчислити сукупність"}, "ARABIC": {"a": "(текст)", "d": "Перетворює римську цифру на арабську", "ad": "являє собою римську цифру, яку потрібно перетворити"}, "ASC": {"a": "(текст)", "d": "Для мов із двобайтними наборами символів (DBCS) ця функція перетворює двобайтні символи на однобайтні", "ad": "являє собою текст, який потрібно перетворити"}, "ASIN": {"a": "(число)", "d": "Повертає арксинус числа в радіанах, у діапазоні від -Пі/2 до Пі/2", "ad": "синус шуканого кута, значення в діапазоні від -1 до 1"}, "ASINH": {"a": "(число)", "d": "Повертає обернений гіперболічний синус (ареа-синус) числа", "ad": "будь-яке дійсне число, яке більше або дорівнює 1"}, "ATAN": {"a": "(число)", "d": "Повертає арктангенс числа в радіанах, у діапазоні від -Пі/2 до Пі/2", "ad": "тангенс шуканого кута"}, "ATAN2": {"a": "(x; y)", "d": "Повертає арктангенс для вказаних координат x та y, в радіанах від -Пі до Пі, виключаючи -Пі", "ad": "координата X точки!координата Y точки"}, "ATANH": {"a": "(число)", "d": "Повертає обернений гіперболічний тангенс (ареа-тангенс) числа", "ad": "будь-яке дійсне число в інтервалі від -1 до 1, виключаючи -1 і 1"}, "BASE": {"a": "(число; система_числення; [мін_довжина])", "d": "Перетворює число на текстовий вираз за заданою системою (основою) числення", "ad": "являє собою число, яке потрібно перетворити!являє собою основу системи числення, за якою потрібно перетворити число!являє собою мінімальну довжину рядка відповіді. Пропущені нулі на початку не додаються"}, "CEILING": {"a": "(число; точність)", "d": "Округлює число найближчого більшого кратного точності", "ad": "числове значення, яке необхідно округлити!кратне, до якого необхідно округлити"}, "CEILING.MATH": {"a": "(число; [точність]; [модуль])", "d": "Округлює число до найближчого більшого за модулем цілого числа або кратного значенню точності", "ad": "являє собою значення, яке потрібно округлити!являє собою кратне число, до якого потрібно округлити!якщо задати ненульове значення, ця функція округлюватиме в напрямку від нуля"}, "CEILING.PRECISE": {"a": "(число; [точність])", "d": "Повертає число, округлене до найближчого більшого цілого або до кратного значенню точності", "ad": "числове значення, яке необхідно округлити!кратне, до якого необхідно округлити"}, "COMBIN": {"a": "(число; кількість_вибраних)", "d": "Повертає кількість комбінацій для заданого числа елементів", "ad": "загальне число елементів!кількість елементів у кожній комбінації"}, "COMBINA": {"a": "(число; кількість_вибраних)", "d": "Повертає кількість комбінацій (з повторами) для заданої кількості елементів", "ad": "являє собою загальне число елементів!являє собою число елементів у кожній комбінації"}, "COS": {"a": "(число)", "d": "Повертає косинус кута", "ad": "кут у радіанах, косинус якого необхідно знайти"}, "COSH": {"a": "(число)", "d": "Повертає гіперболічний косинус числа", "ad": "будь-яке дійсне число"}, "COT": {"a": "(число)", "d": "Повертає котангенс кута", "ad": "являє собою кут у радіанах, для якого потрібно обчислити котангенс"}, "COTH": {"a": "(число)", "d": "Повертає гіперболічний котангенс числа", "ad": "являє собою кут у радіанах, для якого потрібно обчислити гіперболічний котангенс"}, "CSC": {"a": "(число)", "d": "Повертає косеканс кута", "ad": "являє собою кут у радіанах, для якого потрібно обчислити косеканс"}, "CSCH": {"a": "(число)", "d": "Повертає гіперболічний косеканс кута", "ad": "являє собою кут у радіанах, для якого потрібно обчислити гіперболічний косеканс"}, "DECIMAL": {"a": "(число; система_числення)", "d": "Перетворює текстове вираження числа за заданою основою на десяткове число", "ad": "являє собою число, яке потрібно перетворити!являє собою основу системи числення для числа, яке потрібно перетворити"}, "DEGREES": {"a": "(кут)", "d": "Перетворює радіани на градуси", "ad": "кут у радіанах, який необхідно перетворити"}, "ECMA.CEILING": {"a": "(число; точність)", "d": "Округлює число найближчого більшого кратного точності", "ad": "числове значення, яке необхідно округлити!кратне, до якого необхідно округлити"}, "EVEN": {"a": "(число)", "d": "Округлює число до найближчого парного цілого, більшого (додатні числа) або меншого (від’ємні числа)", "ad": "значення, яке необхідно округлити"}, "EXP": {"a": "(число)", "d": "Повертає експоненту заданого числа", "ad": "степінь, до якого підноситься основа e. Стала e, основа натурального логарифма, приблизно дорівнює 2,71828182845904"}, "FACT": {"a": "(число)", "d": "Повертає факторіал числа, який дорівнює 1*2*3*...*число", "ad": "невід'ємне число, факторіал якого необхідно обчислити"}, "FACTDOUBLE": {"a": "(число)", "d": "Повертає подвійний факторіал числа", "ad": "значення, для якого слід обчислити подвійний факторіал"}, "FLOOR": {"a": "(число; точність)", "d": "Округлює число до найближчого меншого кратного точності", "ad": "числове значення, яке необхідно округлити!кратне, до якого необхідно округлити. В обох параметрів має бути однаковий знак"}, "FLOOR.PRECISE": {"a": "(число; [точність])", "d": "Повертає число, округлене до найближчого меншого цілого або до кратного значенню точності", "ad": "числове значення, яке необхідно округлити!кратне, до якого необхідно округлити"}, "FLOOR.MATH": {"a": "(число; [точність]; [модуль])", "d": "Округлює число до найближчого меншого за модулем цілого числа або кратного значенню точності", "ad": "являє собою значення, яке потрібно округлити!являє собою кратне число, до якого потрібно округлити!якщо задати ненульове значення, ця функція округлюватиме в напрямку до нуля"}, "GCD": {"a": "(число1; [число2]; ...)", "d": "Повертає найбільший спільний дільник", "ad": "значення від 1 до 255"}, "INT": {"a": "(число)", "d": "Округлює число до найближчого меншого цілого", "ad": "дійсне число, яке необхідно округлити до найближчого меншого цілого"}, "ISO.CEILING": {"a": "(число; [точність])", "d": "Повертає число, округлене до найближчого більшого цілого або до кратного значенню точності. Число округлюється незалежно від його знака. Однак, якщо число – нуль або його значення точності дорівнює нулю, буде повернуто нуль.", "ad": "числове значення, яке необхідно округлити!кратне, до якого необхідно округлити"}, "LCM": {"a": "(число1; [число2]; ...)", "d": "Повертає найменше спільне кратне", "ad": "значення від 1 до 255, для яких слід обчислити найменше спільне кратне"}, "LN": {"a": "(число)", "d": "Повертає натуральний логарифм числа", "ad": "додатне дійсне число, для якого необхідно обчислити натуральний логарифм"}, "LOG": {"a": "(число; [основа])", "d": "Повертає логарифм числа за вказаною основою", "ad": "додатне дійсне число, для якого необхідно обчислити логарифм!основа логарифму; за промовчанням приймається 10"}, "LOG10": {"a": "(число)", "d": "Повертає десятковий логарифм числа", "ad": "додатне дійсне число, для якого необхідно обчислити десятковий логарифм"}, "MDETERM": {"a": "(масив)", "d": "Повертає визначник матриці, яка зберігається в масиві", "ad": "числовий масив з рівною кількістю рядків і стовпців - діапазон клітинок або константа-масив"}, "MINVERSE": {"a": "(масив)", "d": "Повертає обернену матрицю для матриці, яка зберігається в масиві", "ad": "числовий масив з рівною кількістю рядків і стовпців - діапазон клітинок або константа-масив"}, "MMULT": {"a": "(масив1; масив2)", "d": "Повертає добуток матриць, які зберігаються у двох масивах, – масив із кількістю рядків першого масиву і кількістю стовпців другого масиву", "ad": "перший з перемножуваних масивів; кількість його стовпців має дорівнювати кількості рядків другого масиву"}, "MOD": {"a": "(число; дільник)", "d": "Повертає залишок від ділення", "ad": "число, залишок від ділення якого необхідно знайти!число, на яке ділять (дільник)"}, "MROUND": {"a": "(число; точність)", "d": "Повертає число, округлене з потрібною точністю", "ad": "значення, яке слід округлити!точність, з якою слід округлити число"}, "MULTINOMIAL": {"a": "(число1; [число2]; ...)", "d": "Повертає багаточлен набору чисел", "ad": "значення від 1 до 255, для яких слід обчислити багаточлен"}, "MUNIT": {"a": "(вимір)", "d": "Повертає матрицю одиниці для вказаного виміру", "ad": "являє собою ціле число, яке вказує вимір для потрібної матриці одиниці"}, "ODD": {"a": "(число)", "d": "Округлює число до найближчого непарного цілого, більшого (додатні числа) або меншого (від’ємні числа)", "ad": "значення, яке необхідно округлити"}, "PI": {"a": "()", "d": "Повертає число Пі, округлене до 15 знаків після коми (значення 3,14159265358979)", "ad": ""}, "POWER": {"a": "(число; степінь)", "d": "Повертає результат піднесення числа до степеня", "ad": "основа - будь-яке дійсне число!степінь, до якого підносять основу"}, "PRODUCT": {"a": "(число1; [число2]; ...)", "d": "Повертає добуток усіх аргументів", "ad": "від 1 до 255 чисел, логічних значень або чисел у вигляді тексту, які необхідно перемножити"}, "QUOTIENT": {"a": "(чисельник; знаменник)", "d": "Повертає цілу частину частки", "ad": "ділене!дільник"}, "RADIANS": {"a": "(кут)", "d": "Перетворює градуси на радіани", "ad": "кут у градусах, який необхідно перетворити"}, "RAND": {"a": "()", "d": "Повертає рівномірно розподілене випадкове число, яке більше або дорівнює 0 і менше за 1 (змінюється в разі переобчислення)", "ad": ""}, "RANDARRAY": {"a": "([rows]; [columns]; [min]; [max]; [integer])", "d": "Повертає масив випадкових чисел", "ad": "кількість рядків у масиві!кількість стовпців у масиві!мінімальне число, яке має повернутися!максимальне число, яке має повернутися!повернення цілих або десяткових чисел. TRUE — для цілого, FALSE — для десяткового числа"}, "RANDBETWEEN": {"a": "(нижн_межа; верх_межа)", "d": "Повертає випадкове число між двома вказаними числами", "ad": "найменше ціле число, яке повертається функцією RANDBETWEEN!найбільше ціле число, яке повертається функцією RANDBETWEEN"}, "ROMAN": {"a": "(число; [форма])", "d": "Перетворює арабські числа на римські, у текстовому форматі", "ad": "число в арабській нотації, яке необхідно перетворити!число, яке вказує бажаний тип числа в римській нотації."}, "ROUND": {"a": "(число; кількість_розрядів)", "d": "Округлює число до заданої кількості десяткових знаків", "ad": "число, яке необхідно округлити!кількість десяткових розрядів, до якої необхідно округлити число. Від'ємні значення викликають округлення цілої частини, нуль - округлення до найближчого цілого"}, "ROUNDDOWN": {"a": "(число; кількість_розрядів)", "d": "Округлює число до найближчого меншого за модулем", "ad": "будь-яке дійсне число, яке необхідно округлити!кількість десяткових розрядів, до якої необхідно округлити число. Від'ємні значення викликають округлення цілої частини, нуль або відсутність значення - округлення до найближчого цілого"}, "ROUNDUP": {"a": "(число; кількість_розрядів)", "d": "Округлює число до найближчого більшого за модулем", "ad": "будь-яке дійсне число, яке необхідно округлити!кількість десяткових розрядів, до якої необхідно округлити число. Від'ємні значення викликають округлення цілої частини, нуль або відсутність значення - округлення до найближчого цілого"}, "SEC": {"a": "(число)", "d": "Повертає секанс кута", "ad": "являє собою кут у радіанах, для якого потрібно обчислити секанс"}, "SECH": {"a": "(число)", "d": "Повертає гіперболічний секанс кута", "ad": "являє собою кут у радіанах, для якого потрібно обчислити гіперболічний секанс"}, "SERIESSUM": {"a": "(x; n; m; коефіцієнти)", "d": "Повертає суму степеневого ряду на основі формули", "ad": "вхідне значення степеневого ряду!початковий степінь, до якого потрібно піднести число х!величина, на яку слід збільшувати n для кожного етапу в ряді!набір коефіцієнтів, на які множиться степеневий ряд х"}, "SIGN": {"a": "(число)", "d": "Повертає знак числа: 1 – якщо число додатне, 0 – якщо воно дорівнює нулю, –1 – якщо число від'ємне", "ad": "будь-яке дійсне число"}, "SIN": {"a": "(число)", "d": "Повертає синус кута", "ad": "кут у радіанах, синус якого необхідно знайти. Градуси * ПІ()/180 = радіани"}, "SINH": {"a": "(число)", "d": "Повертає гіперболічний синус числа", "ad": "будь-яке дійсне число"}, "SQRT": {"a": "(число)", "d": "Повертає квадратний корінь числа", "ad": "число, для якого необхідно обчислити квадратний корінь"}, "SQRTPI": {"a": "(число)", "d": "Повертає квадратний корінь виразу (число * пі)", "ad": "число, на яке множиться число пі"}, "SUBTOTAL": {"a": "(номер_функції; посилання1; ...)", "d": "Повертає проміжні підсумки списку або бази даних", "ad": "число від 1 до 11, яке вказує функцію для обчислення підсумків.!від 1 до 254 діапазонів або посилань, для яких необхідно підбити проміжні підсумки"}, "SUM": {"a": "(число1; [число2]; ...)", "d": "Підсумовує всі числа в діапазоні клітинок", "ad": "від 1 до 255 чисел, які підсумовують. Логічні та текстові значення ігноруються"}, "SUMIF": {"a": "(діа<PERSON><PERSON><PERSON><PERSON><PERSON>; крите<PERSON><PERSON><PERSON>; [діапазон_для_суми])", "d": "Підсумовує клітинки, задані вказаним критерієм", "ad": "діапазон клітинок для обчислення!умова у вигляді числа, виразу або тексту, що визначає клітинки для підсумовування!фактичні клітинки для підсумовування. Якщо не вказано, використовуються клітинки, задані параметром ''діапазон''"}, "SUMIFS": {"a": "(діапазон_суми; діапазон_критерію; критер<PERSON>й; ...)", "d": "Додає клітинки, визначені наявним набором умов або критеріїв", "ad": "дійсні клітинки для складання!діапазон клітинок, які слід обчислити з використанням певної умови!умова або критерій у вигляді числа, виразу або тексту, що визначає, які клітинки буде додано"}, "SUMPRODUCT": {"a": "(масив1; [масив2]; [масив3]; ...)", "d": "Повертає суму добутків елементів відповідних масивів або діапазонів", "ad": "від 2 до 255 масивів, елементи яких спочатку перемножуються, а отримані добутки підсумовуються. Всі масиви повинні мати однакову розмірність"}, "SUMSQ": {"a": "(число1; [число2]; ...)", "d": "Повертає суму квадра<PERSON><PERSON>в аргументів. Аргументи можуть бути числами, масивами, іменами або посиланнями на клітинки з числами", "ad": "від 1 до 255 чисел, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, імен або посилань на масиви, для яких необхідно обчислити суму квадратів"}, "SUMX2MY2": {"a": "(масив_x; масив_y)", "d": "Повертає суму різниць квадратів відповідних значень двох масивів", "ad": "перший діапазон або масив - число, ім'я, масив або посилання на числові значення!другий діапазон або масив - число, ім'я, масив або посилання на числові значення"}, "SUMX2PY2": {"a": "(масив_x; масив_y)", "d": "Повертає суму сум квадратів відповідних значень двох масивів", "ad": "перший діапазон або масив - число, ім'я, масив або посилання на числові значення!другий діапазон або масив - число, ім'я, масив або посилання на числові значення"}, "SUMXMY2": {"a": "(масив_x; масив_y)", "d": "Повертає суму квадратів різниць відповідних значень двох масивів", "ad": "перший діапазон або масив - число, ім'я, масив або посилання на числові значення!другий діапазон або масив - число, ім'я, масив або посилання на числові значення"}, "TAN": {"a": "(число)", "d": "Повертає тангенс кута", "ad": "кут у радіанах, тангенс якого необхідно знайти. Градуси * ПІ()/180 = радіани"}, "TANH": {"a": "(число)", "d": "Повертає гіперболічний тангенс числа", "ad": "будь-яке дійсне число"}, "TRUNC": {"a": "(число; [кількість_розрядів])", "d": "Відтинає дробову частину числа, залишаючи цілу частину", "ad": "число, яке необхідно скоротити!число, яке вказує точність відтинання, за промовчанням приймається 0"}, "ADDRESS": {"a": "(номер_рядка; номер_стовпця; [тип_посилання]; [a1]; [ім'я_аркуша])", "d": "Повертає в текстовому вигляді посилання на клітинку з указаними номерами рядка й стовпця", "ad": "номер рядка для посилання на клітинку; для першого рядка номер_рядка = 1!номер стовпця для посилання на клітинку; для стовпця D номер_стовпця = 4!тип повернутого посилання: абсолютне – 1; абсолютне на рядок/відносне на стовпець – 2; відносне на рядок/абсолютне на стовпець – 3; відносне – 4!логічне значення, яке вказує стиль посилання: 1 або TRUE – стиль A1, 0 або FALSE – стиль R1C1!текст, який указує ім'я аркуша, що слід використати як зовнішнє посилання"}, "CHOOSE": {"a": "(індекс; значення1; [значення2]; ...)", "d": "Вибирає значення або виконувану дію зі списку значень за індексом", "ad": "номер аргументу-значення, який необхідно вибрати. Число від 1 до 254, формула або посилання на число від 1 до 254!від 1 до 254 чисел, посилань на клітинки, визначених імен, формул, функцій або текстових аргументів, з яких вибирає CHOOSE"}, "COLUMN": {"a": "([посилання])", "d": "Повертає номер стовпця, визначеного посиланням", "ad": "клітинка або діапазон, для яких необхідно відшукати номер стовпця. Якщо не вказано, використовується клітинка з функцією COLUMN"}, "COLUMNS": {"a": "(масив)", "d": "Повертає кількість стовпців у посиланні або масиві", "ad": "ма<PERSON><PERSON><PERSON>, формула масиву або посилання на діапазон, для яких необхідно визначити кількість стовпців"}, "FORMULATEXT": {"a": "(посилання)", "d": "Повертає формулу як рядок", "ad": "являє собою посилання на формулу"}, "HLOOKUP": {"a": "(значення_підстановки; масив_таблиці; номер_рядка; [точність_пошуку])", "d": "Шукає значення у верхньому рядку таблиці або масиву значень і повертає значення з того ж стовпця та вказаного рядка", "ad": "значення, яке потрібно знайти в першому рядку таблиці (значення, посилання або текстовий рядок)!таблиця з текстом, числами або логічними значеннями, де шукаються дані. Масив_таблиці може бути посиланням на діапазон або ім’ям діапазону!номер рядка в масиві таблиці, звідки потрібно взяти значення. Перший рядок значень таблиці має номер 1!логічне значення. TRUE або не вказано: шукати у верхньому рядку (відсортованому за зростанням) найближче значення; FALSE: шукати точний збіг"}, "HYPERLINK": {"a": "(адреса; [ім'я])", "d": "Створює посилання, яке відкриває документ на жорсткому диску, на сервері мережі або в Інтернеті", "ad": "шлях та ім'я файлу документа (розташування на диску, адреса UNC або URL)!текст або число, яке відображається у клітинці. Якщо не вказано, у клітинці відображується значення параметра ''адреса''"}, "INDEX": {"a": "(масив; номер_рядка; [номер_стовпця]!посилання; номер_рядка; [номер_стовпця]; [номер_області])", "d": "Повертає значення або посилання на клітинку на перетині певних рядка й стовпця в указаному діапазоні", "ad": "діапазон клітинок або константа-масив.!рядок у масиві або посиланні, з якого необхідно повернути значення. Якщо він пропущений, необхідно вказати номер_стовпця!стовпець у масиві або посиланні, з якого необхідно повернути значення. Якщо він пропущений, необхідно вказати номер_рядка!посилання на один або кілька діапазонів!рядок у масиві або посиланні, з якого необхідно повернути значення. Якщо він пропущений, необхідно вказати номер_стовпця!стовпець у масиві або посиланні, з якого необхідно повернути значення. Якщо він пропущений, необхідно вказати номер_рядка!діапазон у посиланні, з якого необхідно повернути значення. Перша виділена або введена область - область 1, друга - область 2 і т.д."}, "INDIRECT": {"a": "(посилання_на_клітинку; [a1])", "d": "Повертає посилання, указане текстовим рядком", "ad": "посилання на клітинку, яка містить посилання у стилі A1 або R1C1, або ім'я, визначене як посилання, або посилання на клітинку у вигляді текстового рядка!логічне значення, яке визначає тип посилання, указаного в аргументі: FALSE – стиль R1C1; TRUE або не вказано – стиль A1"}, "LOOKUP": {"a": "(шукане_значення; вектор_перегляду; [вектор_результатів]!шукане_значення; масив)", "d": "Шукає значення в одному рядку, в одному стовпці або в масиві. Забезпечує зворотну сумісність", "ad": "значення, яке LOOKUP шукає у векторі_перегляду; може бути числом, текстом, логічним значенням, ім'ям або посиланням на значення!діапазон, який містить лише один рядок або один стовпець тексту, чисел або логічних значень, розташованих за зростанням!діапазон, який містить лише один рядок або один стовпець із таким самим розміром, що й вектор_перегляду!значення, яке LOOKUP шукає в масиві; може бути числом, текстом, логічним значенням, ім'ям або посиланням на значення!діапазон клітинок із текстом, числами або логічними значеннями, які необхідно порівнювати із шуканим_значенням"}, "MATCH": {"a": "(шукане_значення; масив_який_переглядається; [тип_зіставлення])", "d": "Повертає відносну позицію в масиві елемента, який відповідає вказаному значенню з урахуванням указаного порядку", "ad": "значення, яке використовується під час пошуку потрібного значення в масиві; може бути числом, текстом, логічним значенням або посиланням на такі дані!суцільний діапазон клітинок, який переглядається під час пошуку; може бути діапазоном значень або посиланням на діапазон!число (1, 0 або -1), що визначає, яке значення потрібно повернути."}, "OFFSET": {"a": "(посилання; рядки; стовпці; [висота]; [ширина])", "d": "Повертає посилання на діапазон, віддалений від указаного посилання на вказану кількість рядків і стовпців", "ad": "посилання, від якого відраховується віддалення (посилання на клітинку або діапазон суміжних клітинок)!кількість рядків (вгору або вниз), на яку результат зсунуто щодо діапазону вихідного посилання!кількість стовпців (праворуч або ліворуч), на яку результат зсунуто щодо діапазону вихідного посилання!висота в рядках діапазону посилання результату; якщо не вказано, використовується висота діапазону вихідного посилання!ширина в стовпцях діапазону посилання результату; якщо не вказано, використовується ширина діапазону вихідного посилання"}, "ROW": {"a": "([посилання])", "d": "Повертає номер рядка, визначеного посиланням", "ad": "клітинка або діапазон, для яких необхідно відшукати номер рядка; якщо пропущено, повертає клітинку з функцією ROW"}, "ROWS": {"a": "(масив)", "d": "Повертає кількість рядків у посиланні або масиві", "ad": "ма<PERSON><PERSON><PERSON>, формула масиву або посилання на діапазон, для яких необхідно визначити кількість рядків"}, "TRANSPOSE": {"a": "(масив)", "d": "Перетворює вертикальний діапазон клітинок на горизонтальний або навпаки", "ad": "діапазон клітинок на аркуші або масив значень, який необхідно транспонувати"}, "UNIQUE": {"a": "(масив; [за_стовпцем]; [рівно_один_раз])", "d": "Повертає унікальні значення з діапазону або масиву.", "ad": "діапазон або масив, із якого потрібно повернути унікальні рядки або стовпці!значення логічного типу: порівняння рядків один із одним і повернення унікальних рядків, якщо вказано значення FALSE або параметр пропущено; порівняння стовпців один із одним і повернення унікальних стовпців, якщо вказано значення TRUE!значення логічного типу: повернення рядків або стовпців масиву, які трапляються рівно один раз, якщо вказано значення TRUE; повернення всіх окремих рядків або стовпців із масиву, якщо вказано значення FALSE або параметр пропущено"}, "VLOOKUP": {"a": "(значення_підстановки; масив_таблиці; номер_стовпця; [точність_пошуку])", "d": "Шукає значення в найлівішому стовпці таблиці та повертає значення з того ж рядка та вказаного стовпця. За замовчуванням таблиця має бути відсортована за зростанням", "ad": "значення, яке потрібно знайти в першому стовпці таблиці (значення, посилання або текстовий рядок)!таблиця з текстом, числами або логічними значеннями, де шукаються дані. Масив_таблиці може бути посиланням на діапазон або ім’ям діапазону!номер стовпця в масиві_таблиці, з якого потрібно взяти значення. Перший стовпець значень таблиці має номер 1!логічне значення. TRUE або не вказано: шукати в першому стовпці (відсортованому за зростанням) найближче значення; FALSE: шукати точний збіг"}, "XLOOKUP": {"a": "(значення_для_пошуку; масив_для_пошуку; масив_для_повернення; [якщо_не_знайдено]; [режим_зіставлення]; [режим_пошуку])", "d": "Пошук збігів у масиві або діапазоні та повернення відповідного елемента з другого масиву або діапазону. За замовчуванням ведеться пошук точних збігів", "ad": "значення, яке потрібно знайти!масив або діапазон для пошуку!масив або діапазон, з якого буде повернуто значення!повертається, якщо збігів не знайдено!указує, як значення_для_пошуку зіставляється з масивом масив_для_пошуку!указує режим пошуку. За замовчуванням пошук ведеться від першого елемента до останнього"}, "CELL": {"a": "(тип_відомостей; [посилання])", "d": "Повертає інформацію про форматування, розташування або вміст клітинки", "ad": "текстове значення, яке вказує потрібний тип інформації про клітинку!клітинка, відомості про яку потрібно повернути"}, "ERROR.TYPE": {"a": "(значення_помилки)", "d": "Повертає код, який відповідає значенню помилки.", "ad": "значення помилки, для якої потрібно знайти код; може бути фактичним значенням помилки або посиланням на клітинку, яка містить значення помилки"}, "ISBLANK": {"a": "(значення)", "d": "Перевіряє, чи вказує посилання на пусту клітинку, і повертає значення TRUE або FALSE", "ad": "клітинка або ім'я, що посилаються на клітинку, яку слід перевірити"}, "ISERR": {"a": "(значення)", "d": "Перевіряє, чи значення є помилкою, відмінною від #N/A, і повертає значення TRUE або FALSE", "ad": "значення, яке потрібно перевірити. Це може бути посилання на клітинку, формулу або ім’я клітинки, формули чи значення"}, "ISERROR": {"a": "(значення)", "d": "Перевіряє, чи значення є помилкою, і повертає значення TRUE або FALSE", "ad": "значення, яке потрібно перевірити. Це може бути посилання на клітинку, формулу або ім’я клітинки, формули чи значення"}, "ISEVEN": {"a": "(число)", "d": "Повертає значення TRUE, якщо число парне", "ad": "значення, яке перевіряється"}, "ISFORMULA": {"a": "(посилання)", "d": "Перевіряє, чи посилання надано на клітинку з формулою, і повертає значення ІСТИНА або ХИБНІСТЬ", "ad": "являє собою посилання на клітинку, яку потрібно перевірити. Це може бути посилання на клітинку, формулу, або ім’я клітинки"}, "ISLOGICAL": {"a": "(значення)", "d": "Перевіряє, чи значення логічне (TRUE або FALSE), і повертає TRUE або FALSE", "ad": "значення, яке перевіряють. Значення може бути посиланням на клітинку, формулу або ім'я клітинки, формули або значення"}, "ISNA": {"a": "(значення)", "d": "Перевіряє, чи значення недоступне (#N/A), і повертає значення TRUE або FALSE", "ad": "значення, яке перевіряють. Значення може бути посиланням на клітинку, формулу або ім'я клітинки, формули або значення"}, "ISNONTEXT": {"a": "(значення)", "d": "Повертає TRUE, якщо значення не текст, і FALSE, якщо так (пусті клітинки не вважаються текстом)", "ad": "значення, яке перевіряють: клітинка, формула або ім'я клітинки, формули або значення"}, "ISNUMBER": {"a": "(значення)", "d": "Перевіряє, чи значення число, і повертає значення TRUE або FALSE", "ad": "значення, яке перевіряють. Значення може бути посиланням на клітинку, формулу або ім'я клітинки, формули або значення"}, "ISODD": {"a": "(число)", "d": "Повертає значення TRUE, якщо число непарне", "ad": "значення, яке перевіряється"}, "ISREF": {"a": "(значення)", "d": "Перевіряє, чи значення посилання, і повертає значення TRUE або FALSE", "ad": "значення, яке перевіряють. Значення може бути посиланням на клітинку, формулу або ім'я клітинки, формули або значення"}, "ISTEXT": {"a": "(значення)", "d": "Перевіряє, чи значення текст, і повертає значення TRUE або FALSE", "ad": "значення, яке перевіряють. Значення може бути посиланням на клітинку, формулу або ім'я клітинки, формули або значення"}, "N": {"a": "(значення)", "d": "Перетворює нечислові значення на числа, дати – на дати у вигляді чисел, значення TRUE на 1, решту значень – на 0 (нуль)", "ad": "значення, яке необхідно перетворити"}, "NA": {"a": "()", "d": "Повертає значення помилки #N/A (значення недоступне)", "ad": ""}, "SHEET": {"a": "([значення])", "d": "Повертає номер аркуша для аркуша, на який надано посилання", "ad": "являє собою назву аркуша або посилання, для якого потрібно дізнатися номер аркуша. Якщо пропустити цю умову, повертається номер аркуша з цією функцією"}, "SHEETS": {"a": "([посилання])", "d": "Повертає кількість аркушів у посиланні", "ad": "являє собою посилання, кількість аркушів у якому потрібно дізнатися. Якщо пропустити цю умову, повертається кількість аркушів у книзі з цією функцією"}, "TYPE": {"a": "(значення)", "d": "Повертає ціле число, яке позначає тип даних указаного значення: число = 1; текст = 2; логічне значення = 4; помилка = 16; масив = 64; комбінований тип = 128", "ad": "будь-яке значення"}, "AND": {"a": "(лог_значення1; [лог_значення2]; ...)", "d": "Перевіряє, чи всі аргументи істинні, і повертає значення TRUE, якщо це так", "ad": "від 1 до 255 умов, які слід перевірити і які можуть мати значення TRUE або FALSE. Це можуть бути логічні значення, масиви або посилання"}, "FALSE": {"a": "()", "d": "Повертає логічне значення FALSE", "ad": ""}, "IF": {"a": "(лог_вираз; [значення_якщо_істина]; [значення_якщо_хибність])", "d": "Перевіряє, чи виконується умова, і повертає одне значення, якщо вона виконується, та інше значення, якщо ні", "ad": "будь-яке значення або вираз, що може визначитися як TRUE або FALSE!значення, яке повертається, якщо лог_вираз має значення TRUE. Якщо не вказано, повертається TRUE. Дозволяється глибина вкладення до 7 рівнів!значення, яке повертається, якщо лог_вираз має значення FALSE. Якщо не вказано, повертається FALSE"}, "IFS": {"a": "(логічна_перевірка; значення_якщо_істина; ...)", "d": "Перевірка однієї або кількох умов і повернення значення, що відповідає першій умові зі значенням ІСТИНА", "ad": "будь-яке значення або вираз, які можуть визначатися як ІСТИНА або ХИБНІСТЬ!значення, яке повертається, якщо умова \"логічна_перевірка\" має значення ІСТИНА"}, "IFERROR": {"a": "(значення; значення_якщо_помилка)", "d": "Повертає значення значення_якщо_помилка, якщо вираз містить помилку, або значення виразу, якщо помилки немає", "ad": "значення, вираз або посилання!значення, вираз або посилання"}, "IFNA": {"a": "(значення; значення_якщо_немає_даних)", "d": "Повертає значення, яке вказується, якщо значення виразу – \"#N/A\"; в іншому випадку повертає результат виразу", "ad": "являє собою будь-яке значення, вираз або посилання!являє собою будь-яке значення, вираз або посилання"}, "NOT": {"a": "(лог_значення)", "d": "Змінює значення TRUE на FALSE, а FALSE на TRUE", "ad": "значення або вираз, що може визначатися як TRUE або FALSE"}, "OR": {"a": "(лог_значення1; [лог_значення2]; ...)", "d": "Перевіряє, чи має принаймні один аргумент значення TRUE, і повертає значення TRUE або FALSE. Значення FALSE повертається, лише якщо всі аргументи мають значення FALSE", "ad": "від 1 до 255 умов, які слід перевірити і які можуть мати значення TRUE або FALSE"}, "SWITCH": {"a": "(вираз; значення1; результат1; [за_замовчуванням_або_значення2]; [результат2]; ...)", "d": "Порівняння виразу зі списком значень і повернення першого збігу як результату. Якщо збігів немає, повертається необов’язкове значення за замовчуванням", "ad": "вираз, який потрібно оцінити!значення, яке потрібно порівняти з виразом!результат, який потрібно повернути, якщо наявний збіг відповідного значення з виразом"}, "TRUE": {"a": "()", "d": "Повертає логічне значення TRUE", "ad": ""}, "XOR": {"a": "(лог_значення1; [лог_значення2]; ...)", "d": "Повертає логічний об’єкт \"виключне АБО\" для всіх аргументів", "ad": "являють собою умови (від 1 до 254) для перевірки; можуть мати значення ІСТИНА або ХИБНІСТЬ і бути логічними значеннями, масивами чи посиланнями"}, "TEXTBEFORE": {"a": "(текст, роздільник, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Повертає текст, який перед розділенням символів.", "ad": "Текст, який потрібно знайти за роздільником.!Символ або рядок, який потрібно використовувати як роздільник.!Потрібний екземпляр роздільника. Стандартне значення = 1. Від’ємне число шукає з кінця.!Шукає в тексті відповідність роздільнику. За замовчуванням виконується зіставлення з урахуванням регістра.!Указує, чи потрібно зіставити роздільник із кінцем тексту. За замовчуванням вони не зіставляються.!Повертається, якщо збігів не знайдено. За замовчуванням повертається #N/A."}, "TEXTAFTER": {"a": "(текст, роздільник, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " Повертає текст після розділення символів.", "ad": " Текст, який потрібно знайти за роздільником.!Символ або рядок, який потрібно використовувати як роздільник.!Потрібний екземпляр роздільника. Стандартне значення = 1. Від’ємне число шукає з кінця.!Шукає в тексті відповідність роздільнику. За замовчуванням виконується зіставлення з урахуванням регістра.!Указує, чи потрібно зіставити роздільник із кінцем тексту. За замовчуванням вони не зіставляються.!Повертається, якщо збігів не знайдено. За замовчуванням повертається #N/A."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Розділяє текст на рядки або стовпці за допомогою роздільників.", "ad": "Текст, який потрібно розділити!Символ або рядок, за якими потрібно розділити стовпці.!Символ або рядок, за якими потрібно розділити рядки.!Визначає, чи слід ігнорувати пусті клітинки. Значення за замовчуванням – FALSE.! Шукає в тексті збіг роздільників. За замовчуванням збіг з урахуванням регістру виконується.!Значення, яке використовується для заповнення. За замовчуванням використовується значення #N/A."}, "WRAPROWS": {"a": "(вектор, wrap_count, [pad_with])", "d": "Переносить вектор рядка або стовпця після вказаної кількості значень.", "ad": "Вектор або посилання для перенесення.!Максимальна кількість значень у рядку.!Значення, за яким потрібно заповнити. Стандартне значення: #N/A."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Вертикально укладає масиви в один масив.", "ad": "Масив або посилання, які потрібно скласти."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Горизонтально укладає масиви в один масив.", "ad": "Масив або посилання, які потрібно скласти."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Повертає рядки з масиву або посилання.", "ad": "Масив або посилання, що містить рядки, які потрібно повернути.!Номер рядка, який потрібно повернути."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Повертає стовпці з масиву або посилання.", "ad": "Масив або посилання, що містить стовпці, які потрібно повернути.!Номер стовпця, який потрібно повернути."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Повертає масив як один стовпець.", "ad": "Масив або посилання, які потрібно повернути як стовпець.!Визначає, чи слід ігнорувати певні типи значень. За замовчуванням жодні значення не ігноруються.!Сканувати масив за стовпцем. За замовчуванням масив сканується за рядком."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "Повертає масив як один рядок. ", "ad": "Масив або посилання, які потрібно повернути як рядок.!Визначає, чи слід ігнорувати певні типи значень. За замовчуванням жодні значення не ігноруються.!Відскануйте масив за стовпцем. За замовчуванням масив сканується за рядком."}, "WRAPCOLS": {"a": "(вектор, wrap_count, [pad_with])", "d": "Переносить вектор рядка або стовпця після вказаної кількості значень.", "ad": "Вектор або посилання для перенесення.!Максимальна кількість значень у стовпці.!Значення, за яким потрібно заповнити. Стандартне значення: #N/A."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Повертає рядки або стовпці з початку або кінця масиву.", "ad": "Ма<PERSON><PERSON><PERSON>, з якого потрібно взяти рядки або стовпці.!Кількість рядків, які потрібно взяти. Від’ємне значення береться з кінця масиву.!Кількість стовпців, які потрібно взяти. Від’ємне значення береться з кінця масиву."}, "DROP": {"a": "(array, rows, [columns])", "d": "Видаляє рядки або стовпці з початку або кінця масиву.", "ad": "<PERSON><PERSON><PERSON><PERSON><PERSON>, з якого потрібно видалити рядки або стовпці.!Кількість рядків, які потрібно видалити. Від’ємне значення видаляється з кінця масиву.!Кількість стовпців, які потрібно видалити. Від’ємне значення видаляється з кінця масиву."}, "SEQUENCE": {"a": "(рядки, [стовпці], [початок], [інкремент])", "d": "Повертає послідовність чисел", "ad": "кількість рядків, які потрібно повернути!кількість стовпців, які потрібно повернути!перше число в послідовності!число, яке слід додавати до кожного наступного значення в послідовності"}, "EXPAND": {"a": "(масив, рядки, [стовпці], [pad_with])", "d": "Розгортає масив до вказаних розмірів.", "ad": "Ма<PERSON>ив, який потрібно розгорнути.!Кількість рядків у розгорнутому масиві. Якщо їх немає, рядки не буде розгорнуто.!Кількість стовпців у розгорнутому масиві. Якщо їх немає, стовпці не буде розгорнуто.!Значення, яке потрібно заповнити. Значення за замовчуванням – #N/A."}, "XMATCH": {"a": "(lookup_value, lookup_array, [match_mode], [search_mode])", "d": "Повертає відносне розташування елемента в масиві. За замовчуванням ведеться пошук точних збігів", "ad": "значення, яке потрібно знайти!масив або діапазон для пошуку!указати, як значення_підстановки зіставляється з масивом масив_підстановки!указати режим пошуку. За замовчуванням пошук ведеться від першого елемента до останнього"}, "FILTER": {"a": "(масив, включити, [якщо_пустий])", "d": "Фільтрування діапазону або масиву", "ad": "діапазон або масив, який потрібно відфільтрувати!масив логічних значень, де значення TRUE вказує, який рядок або стовпець потрібно зберегти!повертається, якщо не збережено жодних елементів"}, "ARRAYTOTEXT": {"a": "(масив, [формат])", "d": "Повертає текстове подання масиву", "ad": "маси<PERSON>, який потрібно відобразити як текст!формат тексту"}, "SORT": {"a": "(масив, [індекс_сортування], [порядок_сортування], [за_стовпцем])", "d": "Сортування діапазону або масиву", "ad": "діапазон або масив, який потрібно відсортувати!число, яке вказує на рядок або стовпець, за яким потрібно сортувати!число, яке вказує потрібний порядок сортування; значення 1 (використовується за замовчуванням) указує сортування за зростанням, -1 указує сортування за спаданням!логічне значення, яке вказує бажаний напрямок сортування: FALSE указує сортування за рядком (використовується за замовчуванням), TRUE указує сортування за стовпцем"}, "SORTBY": {"a": "(ма<PERSON>и<PERSON>, ключовий_масив, [порядок_сортування], ...)", "d": "Сортування діапазону або масиву на основі значень у зіставленому діапазоні або масиві", "ad": "діапазон або масив для сортування!діапазон або масив, за яким виконується сортування!число, яке вказує потрібний порядок сортування: 1 – за зростанням (стандартне значення), -1 – за спаданням"}, "GETPIVOTDATA": {"a": "(поле_даних; зведена_таблиця; [поле]; [елем]; ...)", "d": "Витягає дані, які зберігаються у зведеній таблиці", "ad": "ім'я поля даних, з якого необхідно витягти дані!посилання на клітинку або діапазон клітинок зведеної таблиці, які містять потрібні дані!поле!елемент поля"}, "IMPORTRANGE": {"a": "(url-адреса_таблиці, діапазон_рядок)", "d": "Імпортує діапазон клітинок з указаної електронної таблиці."}}