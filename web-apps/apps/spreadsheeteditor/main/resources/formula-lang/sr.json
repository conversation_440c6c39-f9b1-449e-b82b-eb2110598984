{"DATE": "DATUM", "DATEDIF": "RAZLIKADATUMA", "DATEVALUE": "DATUMVREDNOST", "DAY": "DAN", "DAYS": "DANI", "DAYS360": "DAN360", "EDATE": "EDATUM", "EOMONTH": "EOMESEC", "HOUR": "SAT", "ISOWEEKNUM": "ISO.BR.NED", "MINUTE": "MINUTA", "MONTH": "MESEC", "NETWORKDAYS": "MREŽNIRADNIDANI", "NETWORKDAYS.INTL": "MREŽNIRADNIDANI.INTL", "NOW": "SADA", "SECOND": "SEKUNDA", "TIME": "VREME", "TIMEVALUE": "VREMEVREDNOST", "TODAY": "DANAS", "WEEKDAY": "VIKEND", "WEEKNUM": "BRNEDELJE", "WORKDAY": "RADNI_DAN", "WORKDAY.INTL": "RADNI_DAN.INTL", "YEAR": "GODINA", "YEARFRAC": "GODINAFRAKCIJA", "BESSELI": "BESSELI", "BESSELJ": "BESSELJ", "BESSELK": "BESSELK", "BESSELY": "BESSELY", "BIN2DEC": "BIN2DEC", "BIN2HEX": "BIN2HEX", "BIN2OCT": "BIN2OCT", "BITAND": "BITAND", "BITLSHIFT": "BITLSHIFT", "BITOR": "BITOR", "BITRSHIFT": "BITRSHIFT", "BITXOR": "BITXOR", "COMPLEX": "KOMPLEKSNO", "CONVERT": "KONVERTUJ", "DEC2BIN": "DECIMALNI_U_BINARNI", "DEC2HEX": "DECIMALNI_U_HEKSADECIMALNI", "DEC2OCT": "DECIMALNI_U_OKTALNI", "DELTA": "DELTA", "ERF": "ERF", "ERF.PRECISE": "ERF.TAČNA", "ERFC": "ERF.KOMPL", "ERFC.PRECISE": "ERF.KOMPL.TAČNA", "GESTEP": "GESTEP", "HEX2BIN": "HEKSADECIMALNI_U_BINARNI", "HEX2DEC": "EKSADECIMALNI_U_DECIMALNI", "HEX2OCT": "EKSADECIMALNI_U_OKTALNI", "IMABS": "IMABS", "IMAGINARY": "IMAGINARNO", "IMARGUMENT": "IMARGUMENT", "IMCONJUGATE": "KONJUGAT", "IMCOS": "IMCOS", "IMCOSH": "IMCOSH", "IMCOT": "IMCOT", "IMCSC": "IMCSC", "IMCSCH": "IMCSCH", "IMDIV": "IMDIV", "IMEXP": "IMEXP", "IMLN": "IMLN", "IMLOG10": "IMLOG10", "IMLOG2": "IMLOG2", "IMPOWER": "IMPOWER", "IMPRODUCT": "IMPROIZVOD", "IMREAL": "IMREAL", "IMSEC": "IMSEC", "IMSECH": "IMSECH", "IMSIN": "IMSIN", "IMSINH": "IMSINH", "IMSQRT": "IMSQRT", "IMSUB": "IMSUB", "IMSUM": "IMSUMA", "IMTAN": "IMTAN", "OCT2BIN": "OKTALNI_U_BINARNI", "OCT2DEC": "OKTALNI_U_DECIMALNI", "OCT2HEX": "OKTALNI_U_HEKSADECIMALNI", "DAVERAGE": "DAVERAGE", "DCOUNT": "DCOUNT", "DCOUNTA": "DCOUNTA", "DGET": "DGET", "DMAX": "DMAX", "DMIN": "DMIN", "DPRODUCT": "DPROIZVOD", "DSTDEV": "DSTDEV", "DSTDEVP": "DSTDEVP", "DSUM": "DSUMA", "DVAR": "DVAR", "DVARP": "DVARP", "CHAR": "KARAKTER", "CLEAN": "OČISTI", "CODE": "KOD", "CONCATENATE": "KONKATENACIJA", "CONCAT": "KONKAT", "DOLLAR": "DOLAR", "EXACT": "EXACT", "FIND": "PRONAĐI", "FINDB": "PRONAĐIB", "FIXED": "FIKSNI", "LEFT": "LEVO", "LEFTB": "LEVOB", "LEN": "LEN", "LENB": "LENB", "LOWER": "LOWER", "MID": "MID", "MIDB": "MIDB", "NUMBERVALUE": "BROJVREDNOST", "PROPER": "PROPER", "REPLACE": "ZAMENI", "REPLACEB": "ZAMENIB", "REPT": "PONOVI", "RIGHT": "DESNO", "RIGHTB": "DESNOB", "SEARCH": "PRETRAGA", "SEARCHB": "PRETRAGAB", "SUBSTITUTE": "ZAMENA", "T": "T", "T.TEST": "T.TEST", "TEXT": "TEXT", "TEXTJOIN": "TEXTJOIN", "TREND": "TREND", "TRIM": "TRIM", "TRIMMEAN": "TRIMMEAN", "TTEST": "TTEST", "UNICHAR": "UNICHAR", "UNICODE": "UNICODE", "UPPER": "UPPER", "VALUE": "VALUE", "AVEDEV": "AVEDEV", "AVERAGE": "PROSEK", "AVERAGEA": "PROSEK.SVU", "AVERAGEIF": "PROSEK.AKO", "AVERAGEIFS": "PROSEK.AKO.USLOVI", "BETADIST": "BETADIST", "BETAINV": "BETAINV", "BETA.DIST": "BETA.DIST", "BETA.INV": "BETAINV", "BINOMDIST": "BINOMDIST", "BINOM.DIST": "BINOM.DIST", "BINOM.DIST.RANGE": "BINOM.DIST.RANGE", "BINOM.INV": "BINOM.INV", "CHIDIST": "HIKVADRAT.DIST", "CHIINV": "HIKVADRAT.INV", "CHITEST": "CHITEST", "CHISQ.DIST": "CHISQ.DIST", "CHISQ.DIST.RT": "CHISQ.DIST.RT", "CHISQ.INV": "CHISQ.INV", "CHISQ.INV.RT": "CHISQ.INV.RT", "CHISQ.TEST": "CHISQ.TEST", "CONFIDENCE": "KONFIDENCIJALNOST", "CONFIDENCE.NORM": "KONFIDENCIJALNOST.NORM", "CONFIDENCE.T": "KONFIDENCIJALNOST.T", "CORREL": "CORREL", "COUNT": "BROJANJE", "COUNTA": "BROJ.SVE", "COUNTBLANK": "BROJ.PRAZNO", "COUNTIF": "BROJ.AKO", "COUNTIFS": "BROJ.AKO.SVI", "COVAR": "KOVARIJANSA", "COVARIANCE.P": "KOVARIJANSA.P", "COVARIANCE.S": "KOVARIJANSA.S", "CRITBINOM": "KRITBINOM", "DEVSQ": "ODSTUPANJEKV", "EXPON.DIST": "EKSPON.DIST", "EXPONDIST": "EKSPONDIST", "FDIST": "FDIST", "FINV": "FINV", "FTEST": "FTEST", "F.DIST": "F.DIST", "F.DIST.RT": "FDIST.RT", "F.INV": "F.INV", "F.INV.RT": "F.INV.RT", "F.TEST": "F.TEST", "FISHER": "FIŠER", "FISHERINV": "FIŠERINV", "FORECAST": "PROGNOZA", "FORECAST.ETS": "PROGNOZA.ETS", "FORECAST.ETS.CONFINT": "PROGNOZA.ETS.POVERENJE", "FORECAST.ETS.SEASONALITY": "PROGNOZA.ETS.SEZONALNOST", "FORECAST.ETS.STAT": "PROGNOZA.ETS.STAT", "FORECAST.LINEAR": "PROGNOZA.LINEARNA", "FREQUENCY": "FREKVENCIJA", "GAMMA": "GAMA", "GAMMADIST": "GAMADIST", "GAMMA.DIST": "GAMA.DIST", "GAMMAINV": "GAMAINV", "GAMMA.INV": "GAMA.INV", "GAMMALN": "GAMALN", "GAMMALN.PRECISE": "GAMALN.TAČNO", "GAUSS": "GAUS", "GEOMEAN": "GEOMEAN", "GROWTH": "RAST", "HARMEAN": "HARM.SREDINA", "HYPGEOM.DIST": "HIPERGEOM.DIST", "HYPGEOMDIST": "HIPERGEOMDIST", "INTERCEPT": "PRESEČNA", "KURT": "KURT", "LARGE": "LARGE", "LINEST": "LINEST", "LOGEST": "LOGEST", "LOGINV": "LOGINV", "LOGNORM.DIST": "LOGNORM.DIST", "LOGNORM.INV": "LOGNORM.INV", "LOGNORMDIST": "LOGNORMDIST", "MAX": "MAKS", "MAXA": "MAKS.SVE", "MAXIFS": "MAKS.AKO", "MEDIAN": "MEDIJANA", "MIN": "MIN", "MINA": "MIN.SVE", "MINIFS": "MIN.AKO", "MODE": "MOD", "MODE.MULT": "MOD.VIŠE", "MODE.SNGL": "MOD.JEDAN", "NEGBINOM.DIST": "NEGBINOM.DIST", "NEGBINOMDIST": "NEGBINOMDIST", "NORM.DIST": "NORM.DIST", "NORM.INV": "NORM.INV", "NORM.S.DIST": "NORM.S.DIST", "NORM.S.INV": "NORM.S.INV", "NORMDIST": "NORMDIST", "NORMINV": "NORMINV", "NORMSDIST": "NORMSDIST", "NORMSINV": "NORMSINV", "PEARSON": "PIRSON", "PERCENTILE": "PROCENAT", "PERCENTILE.EXC": "PROCENAT.IZUZ", "PERCENTILE.INC": "PROCENAT.UKLJ", "PERCENTRANK": "PROCENATRANG", "PERCENTRANK.EXC": "PROCENATRANG.IZUZ", "PERCENTRANK.INC": "PROCENATRANG.UKLJ", "PERMUT": "PERMUT", "PERMUTATIONA": "PERMUTACIJASVE", "PHI": "FI", "POISSON": "POASON", "POISSON.DIST": "POASON.DIST", "PROB": "VEROVATNOĆA", "QUARTILE": "KVARTIL", "QUARTILE.INC": "KVARTIL.UKLJ", "QUARTILE.EXC": "KVARTIL.IZUZ", "RANK.AVG": "RANG.PROSEK", "RANK.EQ": "RANG.JEDNAK", "RANK": "RANG", "RSQ": "RKV", "SKEW": "ASIMETRIJA", "SKEW.P": "ASIMETRIJA.P", "SLOPE": "NAGIB", "SMALL": "MALI", "STANDARDIZE": "STANDARDIZUJ", "STDEV": "STDEV", "STDEV.P": "STDEV.P", "STDEV.S": "STDEV.S", "STDEVA": "STDEVA", "STDEVP": "STDEVP", "STDEVPA": "STDEVPA", "STEYX": "STEYX", "TDIST": "TDIST", "TINV": "TINV", "T.DIST": "T.DIST", "T.DIST.2T": "T.DIST.2T", "T.DIST.RT": "T.DIST.RT", "T.INV": "T.INV", "T.INV.2T": "T.INV.2T", "VAR": "VAR", "VAR.P": "VAR.P", "VAR.S": "VAR.S", "VARA": "VARA", "VARP": "VARP", "VARPA": "VARPA", "WEIBULL": "VEJBUL", "WEIBULL.DIST": "VEJBUL.DIST", "Z.TEST": "Z.TEST", "ZTEST": "ZTEST", "ACCRINT": "AKUMKAMATA", "ACCRINTM": "AKUMKAMATAZA", "AMORDEGRC": "AMORDEGRC", "AMORLINC": "AMORLINC", "COUPDAYBS": "DANI.OD.POČ.KUP", "COUPDAYS": "KUPDANI", "COUPDAYSNC": "DANI.DO.SLED.KUP", "COUPNCD": "KUP.SLED.DATUM", "COUPNUM": "KUPBR", "COUPPCD": "KUP.PRET.DATUM", "CUMIPMT": "KUMKAMATA", "CUMPRINC": "KUMGLAVNICA", "DB": "OPADAJUĆA", "DDB": "DVOSTR.OPADAJUĆA", "DISC": "DISC", "DOLLARDE": "DOLARDE", "DOLLARFR": "DOLARFR", "DURATION": "TRAJANJE", "EFFECT": "EF.NOMINALNA", "FV": "BUDUĆA.VR", "FVSCHEDULE": "BUDUĆA.VR.PLANA", "INTRATE": "KAMATNA.STOPA", "IPMT": "IPMT", "IRR": "IRR", "ISPMT": "ISPMT", "MDURATION": "MOD.TRAJANJE", "MIRR": "MIRR", "NOMINAL": "NOMINAL", "NPER": "NPER", "NPV": "NPV", "ODDFPRICE": "ODDFCENA", "ODDFYIELD": "ODDFPRINOS", "ODDLPRICE": "ODDLCENA", "ODDLYIELD": "ODDLPRINOS", "PDURATION": "TRAJANJE.PROJEKTA", "PMT": "PMT", "PPMT": "PPMT", "PRICE": "CENA", "PRICEDISC": "CENADISKONT", "PRICEMAT": "CENADOSPEĆE", "PV": "SADAŠNJA.VR", "RATE": "STOPA", "RECEIVED": "PRIMLJENO", "RRI": "RRI", "SLN": "SLN", "SYD": "SYD", "TBILLEQ": "TBILLEQ", "TBILLPRICE": "TBILLCENA", "TBILLYIELD": "TBILLPRINOS", "VDB": "VDB", "XIRR": "XIRR", "XNPV": "XNPV", "YIELD": "PRINOS", "YIELDDISC": "PRINOSDISKONT", "YIELDMAT": "PRINOSDOSPEĆE", "ABS": "APS", "ACOS": "ARKCOS", "ACOSH": "ARKCOSH", "ACOT": "ARKCOT", "ACOTH": "ARKCOTH", "AGGREGATE": "AGREGAT", "ARABIC": "ARAPSKI", "ASC": "ASC", "ASIN": "ARKSIN", "ASINH": "ARKSINH", "ATAN": "ARKTAN", "ATAN2": "ARKTAN2", "ATANH": "ARKTANH", "BASE": "BAZA", "CEILING": "PLAFON", "CEILING.MATH": "PLAFON.MAT", "CEILING.PRECISE": "PLAFON.TAČAN", "COMBIN": "KOMBINACIJA", "COMBINA": "KOMBINACIJA.SVE", "COS": "KOS", "COSH": "KOSH", "COT": "KOT", "COTH": "KOTH", "CSC": "KSC", "CSCH": "KSCH", "DECIMAL": "DECIMAL", "DEGREES": "STEPENI", "ECMA.CEILING": "ECMA.PLAFON", "EVEN": "PARNO", "EXP": "EXP", "FACT": "FAKTORIJAL", "FACTDOUBLE": "DUPLIFAKTORIJAL", "FLOOR": "POD", "FLOOR.PRECISE": "POD.TAČAN", "FLOOR.MATH": "POD.MAT", "GCD": "NZD", "INT": "CELI", "ISO.CEILING": "ISO.PLAFON", "LCM": "NZS", "LN": "LN", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "MATDETERM", "MINVERSE": "MATINVERSE", "MMULT": "MATMNOŽENJE", "MOD": "MOD", "MROUND": "MROUND", "MULTINOMIAL": "MULTINOMIJAL", "MUNIT": "JEDMATRICA", "ODD": "NEPARAN", "PI": "PI", "POWER": "STEPEN", "PRODUCT": "PROIZVOD", "QUOTIENT": "KOLIČNIK", "RADIANS": "RADIJANI", "RAND": "RAND", "RANDARRAY": "RANDNIZ", "RANDBETWEEN": "RANDIZMEĐU", "ROMAN": "RIMSKI", "ROUND": "ZAOKRUŽI", "ROUNDDOWN": "ZAOKRUŽI.DOLE", "ROUNDUP": "ZAOKRUŽI.GORE", "SEC": "SEC", "SECH": "SECH", "SERIESSUM": "SUMASERIJE", "SIGN": "ZNAK", "SIN": "SIN", "SINH": "SINH", "SQRT": "KOREN", "SQRTPI": "KOREN.PI", "SUBTOTAL": "DELIMIČANZBIR", "SUM": "SUMA", "SUMIF": "SUMA.AKO", "SUMIFS": "SUMA.AKO.VIŠE", "SUMPRODUCT": "SUMPROIZVOD", "SUMSQ": "SUMAKVADRATA", "SUMX2MY2": "ZBIRX2MINUSY2", "SUMX2PY2": "ZBIRX2PLUSY2", "SUMXMY2": "ZBIRXMINUSY2", "TAN": "TAN", "TANH": "TANH", "TRUNC": "SKRATI", "ADDRESS": "ADRESA", "CHOOSE": "IZABERI", "COLUMN": "KOLONA", "COLUMNS": "KOLONE", "FORMULATEXT": "FORMULATEXT", "HLOOKUP": "HPRONAĐI", "HYPERLINK": "HIPERVEZA", "INDEX": "INDEKS", "INDIRECT": "INDIREKTNO", "LOOKUP": "PRONAĐI", "MATCH": "POKLAPANJE", "OFFSET": "POMERAJ", "ROW": "RED", "ROWS": "REDOVI", "TRANSPOSE": "TRANSPONUJ", "UNIQUE": "JEDINSTVEN", "VLOOKUP": "VPRONAĐI", "XLOOKUP": "XPRONAĐI", "CELL": "ĆELIJA", "ERROR.TYPE": "GREŠKA.TIP", "ISBLANK": "JE.PRAZNO", "ISERR": "J<PERSON><PERSON>", "ISERROR": "JE.GREŠKA", "ISEVEN": "JE.PARAN", "ISFORMULA": "JE.FORMULA", "ISLOGICAL": "JE.LOGIČNO", "ISNA": "JE.NA", "ISNONTEXT": "JE.NE.TEKST", "ISNUMBER": "JE.<PERSON>O<PERSON>", "ISODD": "JE.NEPARAN", "ISREF": "JE.REF", "ISTEXT": "JE.TEKST", "N": "N", "NA": "NA", "SHEET": "LIST", "SHEETS": "LISTOVI", "TYPE": "TIP", "AND": "I", "FALSE": "NETAČNO", "IF": "AKO", "IFS": "AKOVIŠE", "IFERROR": "AKOGREŠKA", "IFNA": "AKO.NA", "NOT": "NIJE", "OR": "ILI", "SWITCH": "PREBACI", "TRUE": "TAČNO", "XOR": "XOR", "TEXTBEFORE": "TEKSTPRE", "TEXTAFTER": "TEKSTPOSLE", "TEXTSPLIT": "RAZDELITITEKST", "WRAPROWS": "OBAVIJREDOVE", "VSTACK": "VERTIKALNISLOJ", "HSTACK": "HORIZONTALNISLOJ", "CHOOSEROWS": "IZABERIREDOVE", "CHOOSECOLS": "IZABERIKOLONE", "TOCOL": "UKOLONU", "TOROW": "URED", "WRAPCOLS": "OBAVIJKOLONE", "TAKE": "UZMI", "DROP": "SPUSTI", "SEQUENCE": "SEKVENCA", "EXPAND": "PROŠIRI", "XMATCH": "XPOKLAPANJE", "FILTER": "FILTER", "ARRAYTOTEXT": "NIZUTEKST", "SORT": "SORTIRAJ", "SORTBY": "SORTIRAJPO", "GETPIVOTDATA": "DOBIJPIVOTPODATKE", "IMPORTRANGE": "UVEZIOPSEG", "LocalFormulaOperands": {"StructureTables": {"h": "Zaglavlja", "d": "<PERSON><PERSON><PERSON>", "a": "Sve", "tr": "<PERSON><PERSON>j red", "t": "Zbir"}, "CONST_TRUE_FALSE": {"t": "TAČNO", "f": "NETAČNO"}, "CONST_ERROR": {"nil": "#NULA!", "div": "#DEL/0!", "value": "#VREDNOST!", "ref": "#REF!", "name": "#NAZIV\\?", "num": "#BROJ!", "na": "#N/A", "getdata": "#DOBIJANJE_PODATAKA", "uf": "#NEPODRŽANA_FUNKCIJA!", "calc": "#RAČUNANJE!"}, "CELL_FUNCTION_INFO_TYPE": {"address": "ad<PERSON>a", "col": "kolona", "color": "boja", "contents": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filename": "nazivfajla", "format": "format", "parentheses": "zagrade", "prefix": "prefiks", "protect": "z<PERSON>š<PERSON><PERSON>", "row": "red", "type": "tip", "width": "<PERSON><PERSON><PERSON>"}}}