{"DATE": {"a": "(an; lună; zi)", "d": "Returnează numărul care reprezintă data în cod dată-oră.", "ad": "este un număr de la 1900 sau 1904 (în funcție de sistemul de date al registrului de lucru) la 9999!este un număr de la 1 la 12 care reprezintă luna din an!este un număr de la 1 la 31 care reprezintă ziua din lună"}, "DATEDIF": {"a": "(dată_început; dată_sfârșit; unitate)", "d": "Calculează numărul de zile, luni sau ani dintre două date calendaristice", "ad": "O dată care reprezintă prima sau data de început a unei perioade date!O dată care reprezintă ultima zi sau data de sfârșit a perioadei!Tipul de informații care doriți să fie returnate"}, "DATEVALUE": {"a": "(text_dată)", "d": "Transformă o dată sub formă de text într-un număr care reprezintă data în cod dată-oră", "ad": "este textul care reprezintă o dată într-un format de dată Spreadsheet Editor, între 1.01.1900 sau 1.01.1904 (în funcție de sistemul de date al registrului de lucru) și 31.12.9999"}, "DAY": {"a": "(număr_serie)", "d": "Returnează ziua din lună, un număr de la 1 la 31.", "ad": "este un număr în cod dată-oră utilizat de Spreadsheet Editor"}, "DAYS": {"a": "(dată_sfârșit; dată_început)", "d": "Returnează numărul de zile dintre cele două date.", "ad": "dată_început și dată_sfârșit sunt cele două date între care doriți să cunoașteți numărul de zile!dată_început și dată_sfârșit sunt cele două date între care doriți să cunoașteți numărul de zile"}, "DAYS360": {"a": "(dată_start; dată_sfârșit; [metodă])", "d": "Returnează numărul de zile dintre două date bazat pe un an de 360 de zile (12 luni de 30 de zile)", "ad": "dată_start și dată_sfârșit sunt cele două date între care se calculează numărul de zile!dată_start și dată_sfârșit sunt cele două date între care se calculează numărul de zile!este o valoare logică și specifică metoda de calcul: S.U.A. (NASD) = FALSE sau omisă; European = TRUE."}, "EDATE": {"a": "(dată_start; luni)", "d": "Returnează numărul serial al datei care este numărul indicat de luni înainte sau după data de început", "ad": "este numărul serial care reprezintă data de început!este numărul de luni înainte sau după data_de început"}, "EOMONTH": {"a": "(dată_start; luni)", "d": "Returnează numărul serial al ultimei zile din ultima lună înainte sau după un anumit număr de luni specificate", "ad": "este numărul serial care reprezintă data de început!este numărul de luni înainte sau după data_de început"}, "HOUR": {"a": "(număr_serie)", "d": "Returnează ora ca număr de la 0 (12:00 A.M.) la 23 (11:00 P.M.).", "ad": "este un număr în cod dată-oră utilizat de Spreadsheet Editor sau text în format de oră, cum ar fi 16:48:00 sau 4:48:00 PM"}, "ISOWEEKNUM": {"a": "(dată)", "d": "Returnează numărul de săptămână ISO din an pentru o anumită dată", "ad": "este codul dată-oră utilizat de Spreadsheet Editor pentru calculul datei și orei"}, "MINUTE": {"a": "(număr_serie)", "d": "Returnează minutul ca număr de la 0 la 59.", "ad": "este un număr în cod dată-oră utilizat de Spreadsheet Editor sau text în format de oră, cum ar fi 16:48:00 sau 4:48:00 PM"}, "MONTH": {"a": "(număr_serie)", "d": "<PERSON><PERSON><PERSON><PERSON> luna, un număr de la 1 (ianuarie) la 12 (dece<PERSON><PERSON>).", "ad": "este un număr în cod dată-oră utilizat de Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(dată_start; dată_sfârșit; [s<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Returnează numărul total de zile lucrătoare între două date", "ad": "este numărul de dată serial care reprezintă ziua de început!este un număr de dată serial care reprezintă ziua de final!este un set opțional de una sau mai multe numere de date seriale care se vor exclude din calendarul de lucru, cum ar fi sărbătorile legale"}, "NETWORKDAYS.INTL": {"a": "(dată_start; dată_sfârșit; [weekend]; [s<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Returnează numărul total de zile lucrătoare între două date cu parametri sfârșit de săptămână particularizați", "ad": "este numărul de dată serial care reprezintă ziua de început!este un număr de dată serial care reprezintă ziua de final!este un număr sau un șir care specifică data sfârșiturilor de săptămână!este un set opțional de una sau mai multe numere de date seriale care se vor exclude din calendarul de lucru, cum ar fi sărbătorile legale"}, "NOW": {"a": "()", "d": "Returnează data și ora curente formatate ca dată și oră.", "ad": ""}, "SECOND": {"a": "(număr_serie)", "d": "Returnează secunda ca număr în intervalul de la 0 la 59.", "ad": "este un număr în cod dată-oră utilizat de Spreadsheet Editor sau text în format de oră, cum ar fi 16:48:23 sau 4:48:47 PM"}, "TIME": {"a": "(oră; minut; secundă)", "d": "Transformă ore, minut și secunde date ca numere în numere seriale, formatate cu un format de oră", "ad": "este un număr de la 0 la 23 reprezentând ora!este un număr de la 0 la 59 reprezentând minutul!este un număr de la 0 la 59 reprezentând secunda"}, "TIMEVALUE": {"a": "(text_oră)", "d": "Transformă o oră din format text într-un număr serial pentru oră, un număr de la 0 (12:00:00 AM) la 0,999988426 (11:59:59 PM). Formatați numărul cu format de oră după introducerea formulei", "ad": "este un șir text care dă o oră în orice format de oră Spreadsheet Editor (informațiile despre dată din șir se ignoră)"}, "TODAY": {"a": "()", "d": "Returnează data curentă formatată ca dată.", "ad": ""}, "WEEKDAY": {"a": "(număr_serie; [tip_returnare])", "d": "Returnează un număr de la 1 la 7 care identifică ziua din săptămână a unei date calendaristice.", "ad": "este un număr care reprezintă o dată!este un număr: pentru duminică=1 până la sâmbătă=7, utilizați 1; pentru luni=1 până la duminică=7, utilizați 2; pentru luni=0 până la duminică=6, utilizați 3"}, "WEEKNUM": {"a": "(număr_serie; [tip_returnare])", "d": "Returnează numărul de săptămâni dintr-un an", "ad": "este codul zi-oră utilizat de Spreadsheet Editor pentru calcularea datelor și duratelor!este un număr (1 sau 2) care determină tipul de date care se returnează"}, "WORKDAY": {"a": "(dată_start; zile; [s<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Returnează numărul serial al datei înainte sau după un număr specificat de zile lucrătoare", "ad": "este un număr serial de dată care reprezintă data de început!este numărul de zile care nu sunt în weekend și nu sunt sărbători înainte sau după data_de început!este un index de început pentru una sau mai multe nume de date seriale care se vor exclude din calendarul de lucru, cum ar fi zilele de sărbătoare legale"}, "WORKDAY.INTL": {"a": "(dată_start; zile; [weekend]; [s<PERSON><PERSON><PERSON><PERSON><PERSON>])", "d": "Returnează numărul serial al datei înainte sau după un număr specificat de zile lucrătoare cu parametri sfârșit de săptămână particularizați", "ad": "este un număr serial de dată care reprezintă data de început!este numărul de zile care nu sunt în weekend și nu sunt sărbători înainte sau după data_de început!indică zilele care cad la sfârșit de săptămână. FIX ME!este un index de început pentru una sau mai multe nume de date seriale care se vor exclude din calendarul de lucru, cum ar fi zilele de sărbătoare legale"}, "YEAR": {"a": "(număr_serie)", "d": "<PERSON><PERSON><PERSON><PERSON> anul, un întreg în intervalul 1900 - 9999.", "ad": "este un număr în cod dată-oră utilizat de Spreadsheet Editor"}, "YEARFRAC": {"a": "(dată_start; dată_sfârșit; [bază])", "d": "Returnează fracțiunea de an care reprezintă numărul de zile întregi între data_de început și data_de sfârșit", "ad": "este numărul serial care reprezintă data de început!este numărul serial care reprezintă data de sfârșit!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "BESSELI": {"a": "(x; n)", "d": "Returnează funcția Bessel modificată In(x)", "ad": "este valoarea la care se evaluează funcția!este ordinea funcției Bessel"}, "BESSELJ": {"a": "(x; n)", "d": "Returnează funcția Bessel Jn(x)", "ad": "este valoarea la care se evaluează funcția!este ordinea funcției Bessel"}, "BESSELK": {"a": "(x; n)", "d": "Returnează funcția Bessel modificată Kn(x)", "ad": "este valoarea la care se evaluează funcția!este ordinea funcției"}, "BESSELY": {"a": "(x; n)", "d": "Returnează funcția Bessel  Yn(x)", "ad": "este valoarea la care se evaluează funcția!este ordinea funcției"}, "BIN2DEC": {"a": "(număr)", "d": "Se efectuează conversia unui număr binar într-un număr zec<PERSON>l", "ad": "este numărul binar care va suferi conversia"}, "BIN2HEX": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr binar într-un număr hexazecimal", "ad": "este numărul binar care va suferi conversia!ieste numărul de caractere de utilizat"}, "BIN2OCT": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr binar într-un octal", "ad": "este numărul binar care va suferi conversia!este numărul de caractere de utilizat"}, "BITAND": {"a": "(număr1; număr2)", "d": "Returnează un „And” de nivel de bit din două numere", "ad": "este reprezentarea zecimalei numărului binar pe care doriți să-l evaluați!este reprezentarea zecimalei numărului binar pe care doriți să-l evaluați"}, "BITLSHIFT": {"a": "(număr; volum_deplasare)", "d": "Returnează un număr deplasat la stânga la volum_deplasare biți", "ad": "este reprezentarea zecimalei numărului binar pe care doriți să-l evaluați!este numărul de biți la care doriți să deplasați numărul la stânga"}, "BITOR": {"a": "(număr1; număr2)", "d": "Returnează un „Or” de nivel de bit din două numere", "ad": "este reprezentarea zecimalei numărului binar pe care doriți să-l evaluați!este reprezentarea zecimalei numărului binar pe care doriți să-l evaluați"}, "BITRSHIFT": {"a": "(număr; volum_deplasare)", "d": "Returnează un număr deplasat la dreapta la volum_deplasare biți", "ad": "este reprezentarea zecimalei numărului binar pe care doriți să-l evaluați!este numărul de biți la care doriți să deplasați numărul la dreapta"}, "BITXOR": {"a": "(număr1; număr2)", "d": "Returnează un „Sau exclusiv” la nivel de bit din două numere", "ad": "este reprezentarea zecimală a numărului binar pe care doriți să-l evaluați!este reprezentarea zecimală a numărului binar pe care doriți să-l evaluați"}, "COMPLEX": {"a": "(num_real; num_i; [sufix])", "d": "Se efectuează conversia coeficienților reali și imaginari într-un număr complex", "ad": "este coeficientul real al numărului complex!este coeficientul imaginar al numărului complex!este sufixul componentei imaginare a numărului complex"}, "CONVERT": {"a": "(număr; din_unit; la_unit)", "d": "Se efectuează conversia unui număr de la un sistem de măsurare la altul", "ad": "este valoarea în din_unități asupra cărora se va efectua conversia!unitățile pentru număr!unitățile pentru rezultat"}, "DEC2BIN": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr zecimal într-un număr binar", "ad": "este numărul întreg zecimal care va suferi conversia!este numărul de caractere de utilizat"}, "DEC2HEX": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr zecimal într-un număr hexazecimal", "ad": "este numărul întreg zecimal care va suferi conversia!este numărul de caractere de utilizat"}, "DEC2OCT": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr zecimal într-un octal", "ad": "este numărul întreg zecimal care va suferi conversia!este numărul de caractere de utilizat"}, "DELTA": {"a": "(număr1; [număr2])", "d": "Se testează dacă două numere sunt egale", "ad": "este primul număr!este al doilea număr"}, "ERF": {"a": "(limită_inf; [limită_sup])", "d": "Returnează funcția de eroare", "ad": "este limita inferioară pentru integrarea ERF!este limita superioară pentru integrarea ERF"}, "ERF.PRECISE": {"a": "(X)", "d": "Returnează funcția de eroare", "ad": "este o legătură inferioară pentru integrarea ERF.PRECISE"}, "ERFC": {"a": "(x)", "d": "Returnează funcția de eroare complementară", "ad": "este limita inferioară pentru integrarea ERF"}, "ERFC.PRECISE": {"a": "(X)", "d": "Returnează funcția complementară de eroare", "ad": "este o legătură inferioară pentru integrarea ERFC.PRECISE"}, "GESTEP": {"a": "(număr; [prag])", "d": "Se testează dacă numărul este mai mare decât valoarea de prag", "ad": "este valoarea de testat relativ la prag!este valoarea de prag"}, "HEX2BIN": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr hexazecimal într-un număr binar", "ad": "este numărul hexazecimal care va suferi conversia!este numărul de caractere de utilizat"}, "HEX2DEC": {"a": "(număr)", "d": "Se efectuează conversia unui număr hexazecimal într-un număr zecimal", "ad": "ieste numărul hexazecimal care realizează conversie"}, "HEX2OCT": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr hexazecimal într-un octal", "ad": "este numărul hexazecimal care va suferi conversia!este numărul de caractere de utilizat"}, "IMABS": {"a": "(număr_i)", "d": "Returnează valoarea absolută (modulul) a unui număr complex", "ad": "este un număr complex căruia pentru care doriți valoarea absolută"}, "IMAGINARY": {"a": "(număr_i)", "d": "Returnează coeficientul imaginar al unui număr complex", "ad": "este numărul complex pentru care doriți coeficientul imaginar"}, "IMARGUMENT": {"a": "(număr_i)", "d": "Returnează argumentul q, un unghi exprimat în radiani", "ad": "este numărul complex pentru care doriți argumentul"}, "IMCONJUGATE": {"a": "(număr_i)", "d": "Returnează conjugata complexă a unui număr complex", "ad": "este un număr complex pentru care doriți conjugata"}, "IMCOS": {"a": "(număr_i)", "d": "Returnează cosinusul unui număr complex", "ad": "este numărul complex pentru care doriți cosinusul"}, "IMCOSH": {"a": "(număr)", "d": "Returnează cosinusul hiperbolic al unui număr complex", "ad": "este un număr complex pentru care doriți cosinusul hiperbolic"}, "IMCOT": {"a": "(număr)", "d": "Returnează cotangenta unui număr complex", "ad": "este numărul complex pentru care doriți cotangenta"}, "IMCSC": {"a": "(număr)", "d": "Returnează cosecanta unui număr complex", "ad": "este numărul complex pentru care doriți cosecanta"}, "IMCSCH": {"a": "(număr)", "d": "Returnează cosecanta hiperbolică a unui număr complex", "ad": "este numărul complex pentru care doriți cosecanta hiperbolică"}, "IMDIV": {"a": "(număr_i1; număr_i2)", "d": "Returnează câtul a două numere complexe", "ad": "este numărătorul sau de împărțitul complex!este numitorul sau divizorul complex"}, "IMEXP": {"a": "(număr_i)", "d": "Returnează exponentul unui număr complex", "ad": "este numărul complex pentru care doriți exponentul"}, "IMLN": {"a": "(număr_i)", "d": "Returnează logaritmul natural al unui număr complex", "ad": "este un număr complex pentru care doriți logaritmul natural"}, "IMLOG10": {"a": "(număr_i)", "d": "Returnează un logaritm în baza 10 al unui număr complex", "ad": "este un număr complex pentru doriți logaritmul comun"}, "IMLOG2": {"a": "(număr_i)", "d": "Returnează un logaritm în baza 2 al unui număr complex", "ad": "este un număr complex pentru care doriți logaritmul în baza 2"}, "IMPOWER": {"a": "(număr_i; număr)", "d": "Returnează un număr complex ridicat la o putere întreagă", "ad": "este un număr complex care trebuie ridicat la putere!este puterea la care se ridică numărul complex"}, "IMPRODUCT": {"a": "(număr_i1; [număr_i2]; ...)", "d": "Returnează produsul numerelor complexe de la 1 la 255", "ad": "Inum<PERSON>r1, <PERSON><PERSON><PERSON>r2,... sunt de la 1 la 255 numere complexe de multiplicat."}, "IMREAL": {"a": "(număr_i)", "d": "Returnează coeficientul real al unui număr complex", "ad": "este un număr complex pentru care doriți coeficientul real"}, "IMSEC": {"a": "(număr)", "d": "Returnează secanta unui număr complex", "ad": "este numărul complex pentru care doriți secanta"}, "IMSECH": {"a": "(număr)", "d": "Returnează secanta hiperbolică a unui număr complex", "ad": "este numărul complex pentru care doriți secanta hiperbolică"}, "IMSIN": {"a": "(număr_i)", "d": "Returnează sinusul unui număr complex", "ad": "este un număr complex pentru care doriți sinusul"}, "IMSINH": {"a": "(număr)", "d": "Returnează sinusul hiperbolic al unui număr complex", "ad": "este un număr complex pentru care doriți sinusul hiperbolic"}, "IMSQRT": {"a": "(număr_i)", "d": "Returnează rădăcina pătrată a numărului complex", "ad": "este un număr complex pentru care doriți rădăcina pătrată"}, "IMSUB": {"a": "(număr_i1; număr_i2)", "d": "Returnează diferența dintre două numere complexe", "ad": "este numărul complex din care se scade număr_i2!este numărul complex care se scade din număr_i1"}, "IMSUM": {"a": "(număr_i1; [număr_i2]; ...)", "d": "Returnează suma numerelor complexe", "ad": "sunt de adăugat numere complexe de la 1 la 255"}, "IMTAN": {"a": "(număr)", "d": "Returnează tangenta unui număr complex", "ad": "este numărul complex pentru care doriți tangenta"}, "OCT2BIN": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr octal într-un număr binar", "ad": "este numărul octal care va suferi conversia!este numărul de caractere de utilizat"}, "OCT2DEC": {"a": "(număr)", "d": "Se efectuează conversia unui număr octal într-un număr zecimal", "ad": "este numărul octal care va suferi conversia"}, "OCT2HEX": {"a": "(număr; [locuri])", "d": "Se efectuează conversia unui număr octal într-un număr hexazecimal", "ad": "este numărul octal care va suferi conversia!este numărul de caractere de utilizat"}, "DAVERAGE": {"a": "(bază_de_date; câmp; criterii)", "d": "Face media valorilor dintr-o coloană dintr-o listă sau bază de date care corespund condițiilor precizate", "ad": "este zona de celule care compun lista sau baza de date. O bază de date este o listă de înregistrări înrudite!este fie eticheta unei coloane în ghilimele duble, fie un număr care reprezintă poziția coloanei în listă!este zona de celule care conține condițiile  precizate. Zona include o etichetă de coloană și o celulă sub etichetă pentru o condiție"}, "DCOUNT": {"a": "(bază_de_date; câmp; criterii)", "d": "Numără celulele câmpului (coloanei) care conțin numere. Numărarea se face pentru înregistrările bazei de date care corespund condițiilor specificate", "ad": "este zona de celule care compune lista sau baza de date. O bază de date este o listă de date înrudite!este fie eticheta unei coloane în ghilimele duble fie un număr care reprezintă poziția coloanei în listă!este zona de celule care conține condițiile specificate. Zona include o etichetă de coloană și o celulă sub etichetă pentru o condiție"}, "DCOUNTA": {"a": "(bază_de_date; câmp; criterii)", "d": "Numără celulele completate în câmp (coloană) pentru înregistrările din baza de date care corespund condițiilor specificate", "ad": "este zona de celule care compun lista sau baza de date. O bază de date este o listă de date înrudite!este fie eticheta coloanei în ghilimele duble sau un număr care reprezintă poziția coloanei în listă!este zona de celule care conține condițiile specificate. Zona include o etichetă de coloană și o celulă sub etichetă pentru o condiție"}, "DGET": {"a": "(bază_de_date; câmp; criterii)", "d": "Extrage dintr-o bază de date o singură înregistrare care se potrivește condițiilor specificate", "ad": "este zona de celule care compun lista sau baza de date. O bază de date este o listă de date înrudite!este fie eticheta unei coloane în ghilimele duble sau un număr care reprezintă poziția coloanei în listă!este zona de celule care conțin condițiile specificate. Zona include o etichetă de coloană și o celulă sub etichetă pentru o condiție"}, "DMAX": {"a": "(bază_de_date; câmp; criterii)", "d": "Returnează cel mai mare număr din câmpul (coloana) de înregistrări din baza de date care corespund condițiilor precizate", "ad": "este zona de celule care compun lista sau baza de date. O bază de date este o listă de date înrudite!este fie eticheta coloanei în ghilimele duble, fie un număr care reprezintă poziția coloanei în listă!este zona de celule care conține condițiile precizate. Zona include o etichetă de coloană și o celulă sub etichetă pentru o condiție"}, "DMIN": {"a": "(bază_de_date; câmp; criterii)", "d": "Returnează în câmp (coloană) cel mai mic număr din înregistrările din baza de date care corespund condițiilor specificate", "ad": "este zona de celule care compun lista sau baza de date. O bază de date este o listă de date înrudite!este fie eticheta coloanei în ghilimele duble fie un număr care reprezintă poziția coloanei în listă!este zona de celule care conține condițiile specificate. Zona include o etichetă de coloană și o celulă sub etichetă pentru o condiție"}, "DPRODUCT": {"a": "(bază_de_date; câmp; criterii)", "d": "Înmulțește valorile câmpului (coloanei) pentru înregistrările bazei de date care corespund condițiilor specificate", "ad": "este zona de celule care compun lista sau baza de date. O bază de date este o listă de date înrudite!este fie eticheta coloanei în ghilimele duble fie un număr care reprezintă poziția coloanei în listă!este zona de celule care conține condiția specificată. Zona include o etichetă de coloană și o celulă sub etichetă pentru o condiție"}, "DSTDEV": {"a": "(bază_de_date; câmp; criterii)", "d": "Estimează deviația standard bazată pe un eșantion din intrările selectate din baza de date", "ad": "este zona de celule care compun lista sau baza de date. O bază de date este o listă de date înrudite!este fie eticheta unei coloane în ghilimele duble fie un număr care reprezintă poziția coloanei în listă!este zona de celule care conține condițiile specificate. Zona include o etichetă de coloană și o celulă sub etichetă pentru o condiție"}, "DSTDEVP": {"a": "(bază_de_date; câmp; criterii)", "d": "Calculează deviația standard bazată pe întreaga populație a intrărilor selectate din baza de date", "ad": "este zona de celule care compun lista sau baza de date. O bază de date este o listă de date înrudite!este fie eticheta unei coloane în ghilimele duble fie un număr care reprezintă poziția coloanei în listă!este zona de celule care conține condițiile specificate. Zona include o etichetă de coloană și o celulă sub etichetă pentru o condiție"}, "DSUM": {"a": "(bază_de_date; câmp; criterii)", "d": "Adună în câmp (coloană) numerele din înregistrările din baza de date care corespund condițiilor specificate", "ad": "este zona de celule care compun lista sau baza de date. O bază de date este o listă de date înrudite!este fie eticheta unei coloane în ghilimele duble fie un număr care reprezintă poziția coloanei în listă!este zona de celule care conține condițiile specificate. Zona include o etichetă de coloană și o celulă sub etichetă pentru o condiție"}, "DVAR": {"a": "(bază_de_date; câmp; criterii)", "d": "Estimează varianța bazată pe un eșantion din intrările selectate ale bazei de date", "ad": "este zona de celule care compun lista sau baza de date. O bază de date este o listă de date înrudite!este fie eticheta coloanei în ghilimele duble fie un număr care reprezintă poziția coloanei în listă!este zona de celule care conține condițiile specificate. Zona include o etichetă de coloană și o celulă sub etichetă pentru o condiție"}, "DVARP": {"a": "(bază_de_date; câmp; criterii)", "d": "Calculează varianța bazată pe întreaga populație a intrărilor selectate din baza de date", "ad": "este zona de celule care compun lista sau baza de date. O bază de date este o listă de date înrudite!este fie eticheta unei coloane în ghilimele duble fie un număr care reprezintă poziția coloanei în listă!este zona de celule care conține condițiile specificate. Zona include o etichetă de coloană și o celulă sub etichetă pentru o condiție"}, "CHAR": {"a": "(număr)", "d": "Returnează caracterul specificat de numărul codului din setul de caractere al computerului", "ad": "este un număr între 1 și 255 specificând caracterul"}, "CLEAN": {"a": "(text)", "d": "Elimină toate caracterele neimprimabile din text", "ad": "este orice informație privind foaia de lucru din care se elimină caracterele neimprimabile"}, "CODE": {"a": "(text)", "d": "Returnează un cod numeric pentru primul caracter dintr-un șir text, în setul de caractere utilizat de computer", "ad": "este textul pentru care rezultă codul primului caracter"}, "CONCATENATE": {"a": "(text1; [text2]; ...)", "d": "Unește mai multe șiruri text într-un singur șir text", "ad": "sunt de la 1 la 255 de șiruri text care se unesc într-un singur șir text și care pot fi șiruri text, numere sau referințe la o singură celulă"}, "CONCAT": {"a": "(text1; ...)", "d": "Concatenează o listă sau o zonă de șiruri de text", "ad": "între 1 și 254 de șiruri de text sau zone care se unesc într-un singur șir de text"}, "DOLLAR": {"a": "(număr; [zec<PERSON><PERSON>])", "d": "Transformă un număr în text, utilizând formatul monedă", "ad": "este un număr, o referință la o celulă care conține un număr, sau o formulă care evaluează numere!este numărul de cifre de la dreapta separatorului zecimal. Numărul este rotunjit dacă este necesar; dacă este omis, zecimale = 2"}, "EXACT": {"a": "(text1; text2)", "d": "Verifică dacă două șiruri text sunt identice și returnează TRUE sau FALSE. EXACT diferențiază literele mari de literele mici", "ad": "este primul șir text!este al doilea șir text"}, "FIND": {"a": "(text_de_căutat; în_text; [num_start])", "d": "Returnează numărul poziției de început a unui șir text găsit în cadrul altui șir text. FIND diferențiază literele mari de literele mici", "ad": "este textul de găsit. Utilizați ghil<PERSON> dub<PERSON> (text gol) pentru a corespunde primului caracter din în_text; metacaracterele nu se admit!este textul care conține textul de găsit!precizează de la care caracter începe căutarea. Primul caracter din în_text este caracterul numărul 1. <PERSON><PERSON><PERSON> se omite, Start_num = 1"}, "FINDB": {"a": "(text_de_căutat; în_text; [num_start])", "d": "Găsesc un șir text într-un al doilea șir text, apoi returnează numărul poziției de început a primului șir text începând cu primul caracter al celui de-al doilea șir text, are ca scop utilizarea cu limbi care întrebuințează setul de caractere dublu-octet (DBCS) -  limba japoneză, limba chineză și limba coreeană", "ad": "este textul de găsit. Utilizați ghil<PERSON> dub<PERSON> (text gol) pentru a corespunde primului caracter din în_text; metacaracterele nu se admit!este textul care conține textul de găsit!precizează de la care caracter începe căutarea. Primul caracter din în_text este caracterul numărul 1. <PERSON><PERSON><PERSON> se omite, Start_num = 1"}, "FIXED": {"a": "(număr; [zecimale]; [nr_virgule])", "d": "Rotunjește un număr la numărul specificat de  zecimale și returnează rezultatul ca text cu sau fără virgule", "ad": "este numărul de rotunjit și de transformat în text!este numărul de cifre de la dreapta separatorului zecimal. Dacă este omis, zecimale = 2!este o valoare logică: nu afișează virgule în textul întors = TRUE; afișează virgule în textul întors = FALSE sau omisă"}, "LEFT": {"a": "(text; [car_num])", "d": "Returnează numărul precizat de caractere de la începutul unui șir text", "ad": "este șirul text care conține caracterele de extras!precizează câte caractere să extragă LEFT; 1 dacă se omite"}, "LEFTB": {"a": "(text; [car_num])", "d": "Returnează primul caracter sau primele caractere dintr-un șir text, în funcție de numărul de byți specificat, are ca scop utilizarea cu limbi care întrebuințează setul de caractere dublu-octet (DBCS) -  limba japoneză, limba chineză și limba coreeană", "ad": "este șirul text care conține caracterele de extras!precizează câte caractere să extragă LEFTB; 1 dacă se omite"}, "LEN": {"a": "(text)", "d": "Returnează numărul de caractere într-un șir text", "ad": "este textul a cărui lungime se calculează. Spațiile sunt numărate drept caractere"}, "LENB": {"a": "(text)", "d": "Returnează numărul de byți utilizați pentru reprezentarea caracterelor dintr-un șir text, are ca scop utilizarea cu limbi care întrebuințează setul de caractere dublu-octet (DBCS) -  limba japoneză, limba chineză și limba coreeană", "ad": "este textul a cărui lungime se calculează. Spațiile sunt numărate drept caractere"}, "LOWER": {"a": "(text)", "d": "Transformă toate literele dintr-un șir text în litere mici", "ad": "este textul de transformat în litere mici. Caracterele din Text care nu sunt litere nu se modifică"}, "MID": {"a": "(text; num_start; car_num)", "d": "Returnează caracterele din mijlocul unui șir text fiind date poziția de început și lungimea", "ad": "este șirul text din care se extrag caracterele!este poziția primului caracter de extras. Primul caracter din Text este 1!specifică câte caractere se întorc din Text"}, "MIDB": {"a": "(text; num_start; car_num)", "d": "Returnează un anumit număr de caractere dintr-un șir de text, începând din poziția specificată, pe baza numărului de byți specificat, are ca scop utilizarea cu limbi care întrebuințează setul de caractere dublu-octet (DBCS) -  limba japoneză, limba chineză și limba coreeană", "ad": "este șirul text din care se extrag caracterele!este poziția primului caracter de extras. Primul caracter din Text este 1!specifică câte caractere se întorc din Text"}, "NUMBERVALUE": {"a": "(text; [separator_zecimale]; [separator_grup])", "d": "Efectuează conversia textului în număr într-o manieră independentă de setările regionale", "ad": "este șirul care reprezintă numărul căruia îi efectuați conversia!este caracterul utilizat ca zecimală în șir!este caracterul utilizat ca separator de grup în șir"}, "PROPER": {"a": "(text)", "d": "Transformă un șir text astfel: scrie cu majusculă prima literă din fiecare cuvânt și transformă toate celelalte litere în litere mici", "ad": "este text încadrat în ghilimele, o formulă care returnează text sau o referință la o celulă care conține textul care va fi transformat"}, "REPLACE": {"a": "(text_vechi; num_start; car_num; text_nou)", "d": "Înlocuiește o parte a unui șir text cu un șir text diferit", "ad": "este textul în care se înlocuiesc unele caractere!este poziția caracterului din text_vechi care se înlocuiește cu text_nou!este numărul de caractere din text_vechi care se înlocuiește!este textul care va înlocui caracterele din text_vechi"}, "REPLACEB": {"a": "(text_vechi; num_start; car_num; text_nou)", "d": "Înlocuiește o parte dintr-un șir de text, pe baza numărului de byți specificat, cu un alt șir de text, are ca scop utilizarea cu limbi care întrebuințează setul de caractere dublu-octet (DBCS) -  limba japoneză, limba chineză și limba coreeană", "ad": "este textul în care se înlocuiesc unele caractere!este poziția caracterului din text_vechi care se înlocuiește cu text_nou!este numărul de caractere din text_vechi care se înlocuiește!este textul care va înlocui caracterele din text_vechi"}, "REPT": {"a": "(text; număr_ori)", "d": "Repetă un text de un număr de ori dat. Utilizați REPT pentru a umple o celulă cu un număr de instanțe ale unui șir text", "ad": "este textul de repetat!este un număr pozitiv care precizează de câte ori se repetă textul"}, "RIGHT": {"a": "(text; [car_num])", "d": "Returnează numărul precizat de caractere de la sfârșitul unui șir text", "ad": "este șirul text care conține caracterele de extras!precizează numărul de caractere de extras, 1 dacă se omite"}, "RIGHTB": {"a": "(text; [car_num])", "d": "Întoarce ultimul caracter sau caractere dintr-un șir de text, pe baza unui număr de byți specificat, are ca scop utilizarea cu limbi care întrebuințează setul de caractere dublu-octet (DBCS) -  limba japoneză, limba chineză și limba coreeană", "ad": "este șirul text care conține caracterele de extras!precizează numărul de caractere de extras, 1 dacă se omite"}, "SEARCH": {"a": "(text_de_căutat; în_text; [num_start])", "d": "Returnează numărul caracterului de la care este găsit prima dată un caracter sau un șir text precizat, citind de la stânga la dreapta (nu se diferențiază literele mari și mici)", "ad": "este textul de găsit. Utilizați metacaracterele ? și *; utilizați ~? și ~* pentru a găsi caracterele ? și *!este textul în care se caută text_de_căutat!este numărul caracterului din în_text, num<PERSON><PERSON><PERSON><PERSON> de la stânga, de la care se începe căutarea. Dacă este omis, se utilizează 1"}, "SEARCHB": {"a": "(text_de_căutat; în_text; [num_start])", "d": "Găsesc un șir text într-un al doilea șir text, apoi returnează numărul poziției de început a primului șir text începând cu primul caracter al celui de-al doilea șir text, are ca scop utilizarea cu limbi care întrebuințează setul de caractere dublu-octet (DBCS) -  limba japoneză, limba chineză și limba coreeană", "ad": "este textul de găsit. Utilizați metacaracterele ? și *; utilizați ~? și ~* pentru a găsi caracterele ? și *!este textul în care se caută text_de_căutat!este numărul caracterului din în_text, num<PERSON><PERSON><PERSON><PERSON> de la stânga, de la care se începe căutarea. Dacă este omis, se utilizează 1"}, "SUBSTITUTE": {"a": "(text; text_vechi; text_nou; [num_instanță])", "d": "Înlocuiește textul existent cu text nou într-un șir text", "ad": "este textul sau referința la o celulă care conține textul în care se substituie caracterele!este textul existent de înlocuit. Dacă literele mari și mici din text_vechi nu se regăsesc în text, SUBSTITUTE nu va înlocui textul!este textul care înlocuiește text_vechi!specifică ce instanță a text_vechi se înlocuiește. Dacă este omis, fiecare instanță a text_vechi este înlocuită"}, "T": {"a": "(valoare)", "d": "Verifică dacă o valoare este text și, în caz că este, returnează text, iar în caz contrar, returneaz<PERSON> ghil<PERSON><PERSON> dub<PERSON> (text gol)", "ad": "este valoarea de testat"}, "TEXT": {"a": "(valoare; format_text)", "d": "Transformă o valoare în text cu un format de număr precizat", "ad": "este un număr, o formulă care evaluează o valoare numerică sau o referință la o celulă care conține o valoare numerică!este un format de număr în formă de text din caseta Categorie de pe fila Număr din caseta de dialog Formatare celule"}, "TEXTJOIN": {"a": "(delimitator; ignorare_gol; text1; ...)", "d": "Concatenează o listă sau o zonă de șiruri de text folosind un delimitator", "ad": "Caracter sau șir de inserat între fiecare element text!dacă TRUE(default), ignoră celulele necompletate!între 1 și 252 de șiruri de text sau zone care se unesc"}, "TRIM": {"a": "(text)", "d": "<PERSON><PERSON><PERSON> toate spațiile dintr-un șir text except<PERSON>d spațiile simple dintre cuvinte", "ad": "este textul din care se extrag spațiile"}, "UNICHAR": {"a": "(număr)", "d": "Returnează caracterul Unicode menționat de o valoare numerică dată", "ad": "este numărul Unicode reprezentând un caracter"}, "UNICODE": {"a": "(text)", "d": "Returnează numărul (punctul de cod) corespunzător primului caracter al textului", "ad": "este caracterul al cărui valoare Unicode o doriți"}, "UPPER": {"a": "(text)", "d": "Transformă un șir text în majuscule", "ad": "este textul de transformat în majuscule, o referință sau un șir text"}, "VALUE": {"a": "(text)", "d": "Transformă un șir text care reprezintă un număr într-un număr", "ad": "este textul în ghilimele sau o referință la o celulă care conține textul de transformat"}, "AVEDEV": {"a": "(număr1; [număr2]; ...)", "d": "Returnează media deviațiilor absolute ale punctelor de date față de media lor. Argumentele pot fi numere sau nume, matrice sau referințe care conțin numere", "ad": "sunt de la 1 la 255 de argumente pentru care se calculează media deviațiilor absolute"}, "AVERAGE": {"a": "(număr1; [număr2]; ...)", "d": "Returnează media (aritmetică) a argumentelor sale, care pot fi numere sau nume, matrice sau referințe care conțin numere", "ad": "sunt de la 1 la 255 de argumente numerice pentru care se calculează media"}, "AVERAGEA": {"a": "(valoare1; [valoare2]; ...)", "d": "Returnează media (aritmetică) a argumentelor sale, evaluând textul și valorile FALSE din argumente ca 0; TRUE se evaluează ca 1. Argumentele pot fi numere, nume, matrice sau referințe", "ad": "sunt de la 1 la 255 de argumente pentru care se calculează media"}, "AVERAGEIF": {"a": "(zon<PERSON>; criterii; [zonă_medie])", "d": "Găsește media(aritmetică) pentru celulele specificate printr-o condiție sau criteriu date", "ad": "este intervalul de celule de evaluat!este condiția sau criteriul sub forma unui număr, a unei expresii sau a unui text care definește care celule se vor utiliza pentru a găsi media!sunt chiar celulele care se vor utiliza pentru a găsi media. Dacă se omit, se vor utiliza celulele din interval"}, "AVERAGEIFS": {"a": "(zon<PERSON>_medie; zonă_criterii; criterii; ...)", "d": "Găsește media(aritmetică) pentru celulele specificate printr-un set de condiții sau criterii", "ad": "sunt chiar celulele care se vor utiliza pentru a găsi media.!este intervalul de celule de evaluat pentru o anumită stare!este condiția sau criteriul sub forma unui număr, a unei expresii sau a unui text care definește care celule se vor utiliza pentru a găsi media"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Returnează funcția densitate de probabilitate beta cumulativă", "ad": "este valoarea între A și B la care se evaluează funcția!este un parametru al distribuției și trebuie să fie mai mare decât 0!este un parametru al distribuției și trebuie să fie mai mare decât 0!este limita inferioară opțională a intervalului x. Dacă se omite, A = 0!este limita superioară opțională a intervalului x. Dacă se omite, B = 1"}, "BETAINV": {"a": "(probabilitate; alfa; beta; [A]; [B])", "d": "Returnează inversa funcției de densitate de probabilitate beta cumulativă (BETADIST)", "ad": "este o probabilitate asociată cu distribuția beta!este un parametru al distribuției și trebuie să fie mai mare decât 0!este un parametru al distribuției și trebuie să fie mai mare decât 0!este limita inferioară opțională a intervalului x. <PERSON><PERSON> se omite, A = 0!este limita superioară opțională a intervalului x. <PERSON><PERSON><PERSON> se omite, B = 1"}, "BETA.DIST": {"a": "(x; alfa; beta; cumulativ; [A]; [B])", "d": "Returnează funcția de distribuție a probabilității beta", "ad": "este valoarea între A și B în care se evaluează funcția!este un parametru al distribuției și trebuie să fie mai mare decât 0!este un parametru al distribuției și trebuie să fie mai mare decât 0!este opțional limita inferioară a intervalului x. Dacă este omisă, A = 0!este o valoare logică: pentru funcția de distribuție cumulativă, se utilizează TRUE; pentru funcția de probabilitate a densității, se utilizează FALSE!este opțional limita superioară a intervalului x. Da<PERSON><PERSON> este omisă, B = 1"}, "BETA.INV": {"a": "(probabilitate; alfa; beta; [A]; [B])", "d": "Returnează inversa funcției de densitate de probabilitate beta cumulativă (BETA.DIST)", "ad": "este o probabilitate asociată cu distribuția beta!este un parametru al distribuției și trebuie să fie mai mare decât 0!este un parametru al distribuției și trebuie să fie mai mare decât 0!este opțional limita inferioară a intervalului x. <PERSON><PERSON><PERSON> este omisă, A = 0!este opțional limita superioară a intervalului x. <PERSON><PERSON><PERSON> este omisă, B = 1"}, "BINOMDIST": {"a": "(număr_s; înce<PERSON><PERSON><PERSON>; probabilitate_s; cumulativ)", "d": "Returnează probabilitatea distribuției binomiale pentru o variabilă discretă", "ad": "este numărul de succese din încercări!este numărul de încercări independente!este probabilitatea de succes pentru fiecare încercare!este o valoare logică: pentru funcția distribuție cumulativă, se utilizează TRUE; pentru funcția de probabilitate de masă, se utilizează FALSE"}, "BINOM.DIST": {"a": "(număr_s; înce<PERSON><PERSON><PERSON>; probabilitate_s; cumulativ)", "d": "Returnează probabilitatea distribuției binomiale pentru o variabilă discretă", "ad": "este numărul de succese din încercări!este numărul de încercări independente!este probabilitatea de succes pentru fiecare încercare!este o valoare logică: pentru funcția distribuție cumulativă, se utilizează TRUE; pentru funcția masă de probabilitate, se utilizează FALSE"}, "BINOM.DIST.RANGE": {"a": "(înce<PERSON><PERSON><PERSON>; probabilitate_s; număr_s; [număr_s2])", "d": "Returnează probabilitatea ca un rezultat de încercare să utilizeze o distribuție binomială", "ad": "este numărul de încercări independente!este probabilitatea de succes pentru fiecare încercare!este numărul de reușite ale încercărilor!dacă este furnizată, această funcție returnează probabilitatea ca numărul de încercări reușite să fie între număr_s și număr_s2"}, "BINOM.INV": {"a": "(încerc<PERSON>ri; probabilitate_s; alfa)", "d": "Returnează valoarea cea mai mică pentru care distribuția binomială cumulativă este mai mare sau egală cu o valoare criteriu", "ad": "este numărul de încerc<PERSON><PERSON>!este probabilitatea de succes în fiecare încercare, un număr între 0 și 1 inclusiv!este valoarea criteriu, un număr între 0 și 1 inclusiv"}, "CHIDIST": {"a": "(x; grade_libertate)", "d": "Returnează probabilitatea distribuției hi-pătrat unilaterale dreapta", "ad": "este valoarea la care se evaluează distribuția, un număr nenegativ!este numărul de grade de libertate, un număr între 1 și 10^10, excluzând 10^10"}, "CHIINV": {"a": "(probabilitate; grade_libertate)", "d": "Returnează inversa probabilității distribuției hi-pătrat unilaterale dreapta", "ad": "este o probabilitate asociată cu distribuția hi-pătrat, o valoare între 0 și 1 inclusiv!este numărul de grade de libertate, un număr între 1 și 10^10, excluzând 10^10"}, "CHITEST": {"a": "(zonă_actuale; zonă_așteptate)", "d": "Returnează testul de independență: valoarea din distribuția hi-pătrat pentru statistică și gradele de libertate corespunzătoare", "ad": "este zona de date ce conține observațiile la test față de valorile așteptate!este zona de date ce conține raportul dintre produsul totalurilor pe rând și totalurilor pe coloane, și totalul general"}, "CHISQ.DIST": {"a": "(x; grade_libertate; cumulativ)", "d": "Returnează probabilitatea distribuției Hi-pătrat a cozii din stânga", "ad": "este valoarea în care se evaluează distribuția, un număr nenegativ!este numărul de grade de libertate, un număr între 1 și 10^10, excluzând 10^10!este o valoare logică pentru ca funcția să se întoarcă: funcția de distribuție cumulativă = TRUE; funcția de densitate a probabilității = FALSE"}, "CHISQ.DIST.RT": {"a": "(x; grade_libertate)", "d": "Returnează probabilitatea distribuției Hi-pătrat a cozii din dreapta", "ad": "este valoarea în care se evaluează distribuția, un număr nenegativ!este numărul de grade de libertate, un număr între 1 și 10^10, excluzând 10^10"}, "CHISQ.INV": {"a": "(probabilitate; grade_libertate)", "d": "Returnează inversa probabilității distribuției Hi-pătrat la stânga", "ad": "este o probabilitate asociată cu distribuția Hi-pătrat, o valoare între 0 și 1 inclusiv!este numărul de grade de libertate, un număr între 1 și 10^10, excluzând 10^10"}, "CHISQ.INV.RT": {"a": "(probabilitate; grade_libertate)", "d": "Returnează inversa probabilității distribuției Hi-pătrat la dreapta", "ad": "este o probabilitate asociată cu distribuția Hi-pătrat, o valoare între 0 și 1 inclusiv!este numărul de grade de libertate, un număr între 1 și 10^10, excluzând 10^10"}, "CHISQ.TEST": {"a": "(zonă_actuale; zonă_așteptate)", "d": "Returnează testul de independență: valoarea din distribuția Hi-pătrat pentru statistică și gradele de libertate corespunzătoare", "ad": "este zona de date ce conține observațiile la test față de valorile așteptate!este zona de date ce conține raportul dintre produsul totalurilor pe rând și totalurilor pe coloane, și totalul general"}, "CONFIDENCE": {"a": "(alfa; dev_standard; dimens)", "d": "Returnează intervalul de încredere pentru o medie a populației, utilizând o distribuție normală", "ad": "este nivelul de semnificație utilizat pentru a calcula nivelul de încredere, un număr mai mare decât 0 și mai mic decât 1!este deviația standard a populației pentru zona de date și se presupune cunoscută. dev_standard trebuie să fie mai mare decât 0!este dimensiunea eșantionului"}, "CONFIDENCE.NORM": {"a": "(alfa; dev_standard; dimensiune)", "d": "Returnează intervalul de încredere pentru o medie a populației, utilizând o distribuție normală", "ad": "este nivelul de semnificație utilizat pentru a calcula nivelul de încredere, un număr mai mare decât 0 și mai mic decât 1!este deviația standard a populației pentru zona de date și se presupune cunoscută. Deviația standard trebuie să fie mai mare decât 0!este dimensiunea eșantionului"}, "CONFIDENCE.T": {"a": "(alfa; dev_standard; dimensiune)", "d": "Returnează intervalul de încredere pentru o medie a populației, utilizând o distribuție t-student", "ad": "este nivelul de semnificație utilizat pentru a calcula nivelul de încredere, un număr mai mare decât 0 și mai mic decât 1!este deviația standard a populației pentru zona de date și se presupune cunoscută. dev_standard trebuie să fie mai mare decât 0!este dimensiunea eșantionului"}, "CORREL": {"a": "(matrice1; matrice2)", "d": "Returnează coeficientul de corelație dintre două seturi de date", "ad": "este o zonă de celule de valori. Valorile sunt numere, nume, matrice, sau referințe ce conțin numere!este a doua zonă de celule de valori. Valorile sunt numere, nume, matrice, sau referințe ce conțin numere"}, "COUNT": {"a": "(valoare1; [valoare2]; ...)", "d": "Numără câte celule dintr-un interval conțin numere", "ad": "sunt de la 1 la 255 de argumente care conțin sau care se referă la o varietate de tipuri diferite de date, dar numai numerele sunt luate în considerare"}, "COUNTA": {"a": "(valoare1; [valoare2]; ...)", "d": "Num<PERSON><PERSON><PERSON> celulele dintr-un interval care nu sunt goale", "ad": "sunt de la 1 la 255 de argumente care reprezintă valorile și celulele de numărat. Valorile pot fi orice tip de informație"}, "COUNTBLANK": {"a": "(zonă)", "d": "Numără celulele goale dintr-o zonă precizată de celule", "ad": "este zona din care se numără celulele goale"}, "COUNTIF": {"a": "(zonă; criterii)", "d": "Numără celulele dintr-o zonă care îndeplinesc condiția dată", "ad": "este zona de celule din care se numără celulele completate!este condiția de forma unui număr, expresie sau text care definește celulele de numărat"}, "COUNTIFS": {"a": "(zonă_criterii; criterii; ...)", "d": "Contorizează numărul de celule specificat într-un set dat de condiții sau criterii", "ad": "este intervalul de celule de evaluat pentru o anumită stare!este condiția sub forma unui număr, a unei expresii sau a unui text care definește care celule se vor contoriza"}, "COVAR": {"a": "(matrice1; matrice2)", "d": "<PERSON><PERSON><PERSON><PERSON>, media produselor deviațiilor pentru fiecare pereche de puncte de date din două seturi de date", "ad": "este prima zonă de celule de întregi și trebuie să fie numere, matrice sau referințe ce conțin numere!este a doua zonă de celule de întregi și trebuie să fie numere, matrice sau referințe ce conțin numere"}, "COVARIANCE.P": {"a": "(matrice1; matrice2)", "d": "Returnează covarianța populației, media produselor deviațiilor pentru fiecare pereche de puncte din două seturi de date", "ad": "este prima zonă de celule de întregi și trebuie să fie numere, matrice, sau referințe ce conțin numere!este a doua zonă de celule de întregi și trebuie să fie numere, matrice, sau referințe ce conțin numere"}, "COVARIANCE.S": {"a": "(matrice1; matrice2)", "d": "Returnează covarianța eșantion, media produselor deviațiilor pentru fiecare pereche de puncte din două seturi de date", "ad": "este prima zonă de celule de întregi și trebuie să fie numere, matrice, sau referințe ce conțin numere!este a doua zonă de celule de întregi și trebuie să fie numere, matrice, sau referințe ce conțin numere"}, "CRITBINOM": {"a": "(încerc<PERSON>ri; probabilitate_s; alfa)", "d": "Returnează valoarea cea mai mică pentru care distribuția binomială cumulativă este mai mare sau egală cu o valoare criteriu", "ad": "este numărul de încerc<PERSON><PERSON>!este probabilitatea de succes în fiecare încercare, un număr între 0 și 1 inclusiv!este valoarea criteriu, un număr între 0 și 1 inclusiv"}, "DEVSQ": {"a": "(număr1; [număr2]; ...)", "d": "Returnează suma pătratelor deviațiilor punctelor de date față de media eșantionului", "ad": "sunt de la 1 la 255 de argumente sau o matrice sau o referință la o matrice pentru care se calculează DEVSQ"}, "EXPONDIST": {"a": "(x; lambda; cumulativ)", "d": "Returnează distribuția exponențială", "ad": "este valoarea funcției, un număr nenegativ!este valoarea parametru, un număr pozitiv!este o valoare logică de returnat de către funcție: funcția distribuție cumulativă = TRUE; funcția densitatea probabilității = FALSE"}, "EXPON.DIST": {"a": "(x; lambda; cumulativ)", "d": "Returnează distribuția exponențială", "ad": "este valoarea funcției, un număr nenegativ!este valoarea parametru, un număr pozitiv!este o valoare logică pentru ceea ce returnează funcția: funcția distribuție cumulativă = TRUE; funcția densitatea probabilității = FALSE"}, "FDIST": {"a": "(x; grade_libertate1; grade_libertate2)", "d": "Returnează distribuția de probabilitate F (unilaterale dreapta) (grad de diversitate) pentru două seturi de date", "ad": "este valoarea la care se evaluează funcția, un număr nenegativ!este numărătorul gradelor de libertate, un număr între 1 și 10^10, exclusiv 10^10!este numitorul gradelor de libertate, un număr între 1 și 10^10, exclusiv 10^10"}, "FINV": {"a": "(probabilitate; grade_libertate1; grade_libertate2)", "d": "Returnează inversa distribuției de probabilitate F (unilaterale dreapta): dacă p = FDIST(x;...), atunci FINV(p;....) = x", "ad": "este o probabilitate asociată cu distribuția cumulativă F, un număr între 0 și 1 inclusiv!este numărătorul gradelor de libertate, un număr între 1 și 10^10, exclusiv 10^10!este numitorul gradelor de libertate, un număr între 1 și 10^10, exclusiv 10^10"}, "FTEST": {"a": "(matrice1; matrice2)", "d": "Returnează rezultatul unui test F, probabilitatea bilaterală ca varianțele din Matrice1 și Matrice2 să nu fie semnificativ diferite", "ad": "este prima matrice sau zonă de date și pot fi numere sau nume, matrice sau referințe care conțin numere (celulele necompletate se ignoră)!este a doua matrice sau zonă de date și pot fi numere sau nume, matrice sau referințe care conțin numere (celulele necompletate se ignoră)"}, "F.DIST": {"a": "(x; grade_libertate1; grade_libertate2; cumulativ)", "d": "Returnează distribuția de probabilitate F (coada din stânga) (grad de diversitate) pentru două seturi de date", "ad": "este valoarea în care se evaluează  funcția, un număr nenegativ!este numărătorul gradelor de libertate, un număr între 1 și 10^10, exclusiv 10^10!este numitorul gradelor de libertate, un număr între 1 și 10^10, exclusiv 10^10!este o valoare logică pentru ca funcția să returneze: funcția de distribuție cumulativă = TRUE; funcția de densitate a probabilității = FALSE"}, "F.DIST.RT": {"a": "(x; grade_libertate1; grade_libertate2)", "d": "Returnează distribuția de probabilitate F (coada din dreapta) (grad de diversitate) pentru două seturi de date", "ad": "este valoarea în care se evaluează  funcția, un număr nenegativ!este numărătorul gradelor de libertate, un număr între 1 și 10^10, exclusiv 10^10!este numitorul gradelor de libertate, un număr între 1 și 10^10, exclusiv 10^10"}, "F.INV": {"a": "(probabilitate; grade_libertate1; grade_libertate2)", "d": "Returnează inversa distribuției de probabilitate F (coada din stânga): dacă p = F.DIST(x;...), atunci F.INV(p;....) = x", "ad": "este o probabilitate asociată cu distribuția cumulativă F, un număr între 0 și 1 inclusiv!este numărătorul gradelor de libertate, un număr între 1 și 10^10, exclusiv 10^10!este numitorul gradelor de libertate, un număr între 1 și 10^10, exclusiv 10^10"}, "F.INV.RT": {"a": "(probabilitate; grade_libertate1; grade_libertate2)", "d": "Returnează inversa distribuției de probabilitate F (coada din dreapta): dacă p = F.DIST.RT(x;...), atunci F.INV.RT(p;....) = x", "ad": "este o probabilitate asociată cu distribuția cumulativă F, un număr între 0 și 1 inclusiv!este numărătorul gradelor de libertate, un număr între 1 și 10^10, exclusiv 10^10!este numitorul gradelor de libertate, un număr între 1 și 10^10, exclusiv 10^10"}, "F.TEST": {"a": "(matrice1; matrice2)", "d": "Returnează rezultatul unui test F, probabilitatea bilaterală ca varianțele din Matrice1 și Matrice2 să nu fie semnificativ diferite", "ad": "este prima matrice sau zonă de date și pot fi numere sau nume, matrice sau referințe care conțin numere (celulele necompletate se ignoră)!este a doua matrice sau zonă de date și pot fi numere sau nume, matrice sau referințe care conțin numere (celulele necompletate se ignoră)"}, "FISHER": {"a": "(x)", "d": "Returnează transformata Fisher.", "ad": "este valoarea pentru care se efectuează transformarea, un număr între -1 și 1, exclusiv -1 și 1"}, "FISHERINV": {"a": "(y)", "d": "Returnează inversa transformatei Fisher: dacă y = FISHER(x), atunci FISHERINV(y) = x", "ad": "este valoarea pentru care se efectuează inversa transformatei"}, "FORECAST": {"a": "(x; y_cunoscute; x_cunoscute)", "d": "Calculează sau prognozează o valoare viitoare de-a lungul unei tendințe liniare, utilizând valorile existente", "ad": "este punctul de date pentru care se prognozează o valoare și trebuie să fie o valoare numerică!este matricea sau zona de date numerice dependente!este matricea sau zona de date numerice independentă. Varianța pentru x_cunoscute nu trebuie să fie zero"}, "FORECAST.ETS": {"a": "(data_țintă; valori; cronologie; [sezonalitate]; [date_complete]; [agregare])", "d": "Returnează valoarea prognozată pentru o dată țintă viitoare specificată utilizând metoda ponderată exponențială.", "ad": " este punctul de date pentru care Spreadsheet Editor prognozează o valoare. Acesta ar trebui să continue modelul de valori din cronologie.! este matricea sau zona de date numerice prognozată.! este matricea sau zona de date numerice independentă. Datele din cronologie trebuie să aibă un decalaj uniform între ele și nu pot fi zero.! este o valoare numerică opțională care indică lungimea modelului sezonier. Valoarea implicită 1 indică faptul că sezonalitatea este detectată automat.! este o valoare opțională pentru tratarea valorilor lipsă. Valoarea implicită 1 înlocuiește valorile lipsă prin interpolare, iar 0 le înlocuiește cu zerouri.! este o valoare numerică opțională pentru agregarea mai multor valori cu aceeași marcă de timp. Dacă este necompletată, Spreadsheet Editor calculează media valorilor."}, "FORECAST.ETS.CONFINT": {"a": "(data_țintă; valori; cronologie; [nivel_încredere]; [sezonalitate]; [date_complete]; [agregare])", "d": "Returnează un interval de încredere pentru valoarea prognozată, la data țintă specificată.", "ad": " este punctul de date pentru care Spreadsheet Editor prognozează o valoare. Acesta ar trebui să continue modelul de valori din cronologie.! este matricea sau zona de date numerice prognozată.! este matricea sau zona de date numerice independentă. Datele din cronologie trebuie să aibă un decalaj uniform între ele și nu pot fi zero.! este un număr între 0 și 1, indicând nivelul de încredere pentru intervalul de încredere calculat. Valoarea implicită este 0,95.! este o valoare numerică opțională care indică lungimea modelului sezonier. Valoarea implicită 1 indică faptul că sezonalitatea este detectată automat.! este o valoare opțională pentru tratarea valorilor lipsă. Valoarea implicită 1 înlocuiește valorile lipsă prin interpolare, iar 0 le înlocuiește cu zerouri.! este o valoare numerică opțională pentru agregarea mai multor valori cu aceeași marcă de timp. <PERSON><PERSON><PERSON> este necompletată, Spreadsheet Editor calculează media valorilor."}, "FORECAST.ETS.SEASONALITY": {"a": "(valori; cronologie; [date_complete]; [agregare])", "d": "Returnează lungimea modelului repetitiv pe care aplicația îl detectează pentru seria de timp specificată.", "ad": "este matricea sau zona de date numerice prognozată.!este matricea sau zona de date numerice independentă. Datele din cronologie trebuie să aibă un decalaj uniform între ele și nu pot fi zero.!este o valoare opțională pentru tratarea valorilor lipsă. Valoarea implicită 1 înlocuiește valorile lipsă prin interpolare, iar 0 le înlocuiește cu zerouri.!este o valoare numerică opțională pentru agregarea mai multor valori cu aceeași marcă de timp. Dacă este necompletată, Spreadsheet Editor calculează media valorilor."}, "FORECAST.ETS.STAT": {"a": "(valori; cronologie; tip_statistic; [sezonalitate]; [date_complete]; [agregare])", "d": "Returnează datele statistice solicitate pentru prognoză.", "ad": "este matricea sau zona de date numerice prognozată.!este matricea sau zona de date numerice independentă. Datele din cronologie trebuie să aibă un decalaj uniform între ele și nu pot fi zero.!este un număr între 1 și 8, care indică ce date statistice va returna Spreadsheet Editor pentru prognoza calculată.!este o valoare numerică opțională care indică lungimea modelului sezonier. Valoarea implicită 1 indică faptul că sezonalitatea este detectată automat.!este o valoare opțională pentru tratarea valorilor lipsă. Valoarea implicită 1 înlocuiește valorile lipsă prin interpolare, iar 0 le înlocuiește cu zerouri.!este o valoare numerică opțională pentru agregarea mai multor valori cu aceeași marcă de timp. Dacă este necompletată, Spreadsheet Editor calculează media valorilor."}, "FORECAST.LINEAR": {"a": "(x; y_cunoscute; x_cunoscute)", "d": "Calculează sau prognozează o valoare viitoare de-a lungul unei tendințe liniare, utilizând valori existente", "ad": "este punctul de date pentru care se prognozează o valoare și trebuie să fie o valoare numerică!este matricea sau zona de date numerice dependentă!este matricea sau zona de date numerice independentă. Varianța pentru x_cunoscute nu trebuie să fie zero"}, "FREQUENCY": {"a": "(matrice_date; matrice_clasă)", "d": "Calculează cât de des apar valorile într-o zonă de valori, apoi returnează o matrice verticală de numere având un element mai mult decât matrice_clasă", "ad": "este o matrice de, sau o referință la un set de valori pentru care se numără frecvențele apariției (celulele necompletate și text sunt ignorate)!este o matrice de, sau o referință la intervalele în care se grupează valorile din matrice_date"}, "GAMMA": {"a": "(x)", "d": "Returnează valoarea funcției Gamma", "ad": "este valoarea pentru care doriți să calculați Gamma"}, "GAMMADIST": {"a": "(x; alfa; beta; cumulativ)", "d": "Returnează distribuția gamma", "ad": "este valoarea la care se evaluează distribuția, un număr nenegativ!este un parametru pentru distribuție, un număr pozitiv!este un parametru pentru distribuție, un număr pozitiv. Dacă beta = 1, GAMMADIST returnează distribuția gamma standard!este o valoare logică: returnează funcția distribuție cumulativă = TRUE; returnează funcția masă de probabilitate = FALSE sau se omite"}, "GAMMA.DIST": {"a": "(x; alfa; beta; cumulativ)", "d": "Returnează distribuția gamma", "ad": "este valoarea în care se evaluează distribuția, un număr nenegativ!este un parametru pentru distribuție, un număr pozitiv!este un parametru pentru distribuție, un număr pozitiv. Dacă beta = 1, GAMMA.DIST returnează distribuția gamma standard!este o valoare logică: returnează funcția distribuție cumulativă = TRUE; returnează funcția masă de probabilitate = FALSE sau se omite"}, "GAMMAINV": {"a": "(probabilitate; alfa; beta)", "d": "Returnează inversa distribuției cumulativ gamma: dacă p = GAMMADIST(x,...), atunci GAMMAINV(p,...) = x", "ad": "este probabilitatea asociată cu distribuția gamma, un număr între 0 și 1, inclusiv!este un parametru al distribuției, un număr pozitiv!este un parametru al distribuției, un număr pozitiv. Dacă beta = 1, GAMMAINV returnează inversa distribuției gamma standard"}, "GAMMA.INV": {"a": "(probabilitate; alfa; beta)", "d": "Returnează inversa distribuției cumulativ gamma: dacă p = GAMMA.DIST(x,...), atunci GAMMA.INV(p,...) = x", "ad": "este probabilitatea asociată cu distribuția gamma, un număr între 0 și 1, inclusiv!este un  parametru al distribuției, un număr pozitiv!este un parametru al distribuției, un număr pozitiv. Dacă beta = 1, GAMMA.INV returnează inversa distribuției gamma standard"}, "GAMMALN": {"a": "(x)", "d": "Returnează logaritmul natural al funcției gamma", "ad": "este valoarea pentru care se calculează GAMMALN, un număr pozitiv"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Returnează logaritmul natural al funcției gamma", "ad": "este valoarea pentru care se calculează GAMMALN.PRECISE, un număr pozitiv"}, "GAUSS": {"a": "(x)", "d": "Returnează cu 0,5 mai puțin decât distribuția cumulativă normală standard", "ad": "este valoarea pentru care doriți distribuția"}, "GEOMEAN": {"a": "(număr1; [număr2]; ...)", "d": "Returnează media geometrică a unei matrice sau zone de date numerice pozitive", "ad": "sunt de la 1 la 255 de numere sau nume, matrice sau referințe care conțin numere pentru care se calculează media"}, "GROWTH": {"a": "(y_cunoscute; [x_cunoscute]; [x_noi]; [const])", "d": "Returnează numere cu o tendință de creștere exponențială corespunzător punctelor de date cunoscute", "ad": "este setul de valori y deja cunoscute în relația y = b*m^x, o matrice sau o zonă de numere pozitive!este un set opțional de valori x deja cunoscute în relația y = b*m^x, o matrice sau zonă de aceeași dimensiune cu y_cunoscute!sunt valori x noi pentru care GROWTH returnează valorile y corespunzătoare!este o valoare logică: constanta b este calculată normal dacă Const = TRUE; b este egal cu 1 dacă Const = FALSE sau se omite"}, "HARMEAN": {"a": "(număr1; [număr2]; ...)", "d": "Returnează media armonică a unui set de date de numere pozitive: reciproca mediei aritmetice a reciprocelor", "ad": "sunt de la 1 la 255 de numere sau nume, matrice sau referințe care conțin numere pentru care se calculează media armonică"}, "HYPGEOM.DIST": {"a": "(eșantion_s; număr_eșantion; populație_s; număr_pop; cumulativ)", "d": "Returnează distribuția hipergeometrică", "ad": "este numărul de  succese din eșantion!este dimensiunea eșantionului!este numărul de succese din populație!este dimensiunea populației!este valoarea logică: pentru funcția de distribuție cumulativă, se utilizează TRUE; pentru funcția de densitate a probabilității, se utilizează FALSE"}, "HYPGEOMDIST": {"a": "(eșantion_s; număr_eșantion; populație_s; număr_pop)", "d": "Returnează distribuția hipergeometrică", "ad": "este numărul de succese din eșantion!este dimensiunea eșantionului!este numărul de succese din populație!este dimensiunea populației"}, "INTERCEPT": {"a": "(cunoscute_y; cunoscute_x)", "d": "Calculează punctul în care o linie va intersecta axa y utilizând linia de regresie care se potrivește cel mai bine. Linia este trasată printre valorile x și y cunoscute", "ad": "este setul dependent de observații sau de date și pot fi numere sau nume, matrice sau referințe care conțin numere!este setul independent de observații sau de date și pot fi numere sau nume, matrice sau referințe care conțin numere"}, "KURT": {"a": "(număr1; [număr2]; ...)", "d": "Returnează coeficientul kurtosis pentru un set de date", "ad": "sunt de la 1 la 255 de numere sau de nume, matrice sau referințe care conțin numere pentru care se calculează coeficientul kurtosis"}, "LARGE": {"a": "(matrice; k)", "d": "Returnează a k-a din cele mai mari valori dintr-un set de date. De exemplu, al cincilea din cele mai mari numere", "ad": "este matricea sau zona de date pentru care se determină a k-a valoare ca mărime!este poziția valorii de întors (față de cea mai mare) din matrice sau din zona de celule"}, "LINEST": {"a": "(y_cunoscute; [x_cunoscute]; [const]; [statistică])", "d": "Returnează statistica ce descrie o tendință liniară care se potrivește cel mai bine punctelor de date, prin potrivirea unei linii drepte utilizând metoda celor mai mici pătrate", "ad": "este setul de valori y deja cunoscute în relația y = mx + b!este un set opțional de valori x deja cunoscute în relația y = mx + b!este o valoare logică: constanta b este calculată normal dacă Const = TRUE sau se omite; b este egal cu 0 dacă Const = FALSE!este o valoare logică: returnează statistica de regresie suplimentară = TRUE; returnează coeficienții m și constanta b = FALSE sau se omite"}, "LOGEST": {"a": "(y_cunoscute; [x_cunoscute]; [const]; [statistică])", "d": "Returnează o statistică ce descrie o curbă exponențială care corespunde punctelor de date cunoscute", "ad": "este setul de valori y deja cunoscute în relația y = b*m^x!este un set opțional de valori x deja cunoscute în relația y = b*m^x!este o valoare logică: constanta b este calculată normal dacă Const = TRUE sau se omite; b este egal cu 1 dacă Const = FALSE!este o valoare logică: returnează statistica de regresie suplimentară = TRUE; returnează coeficienții m și constanta b = FALSE sau se omite"}, "LOGINV": {"a": "(probabilitate; media; dev_standard)", "d": "Returnează inversa funcției distribuție cumulativă lognormală a lui x, unde ln(x) este distribuit normal cu parametrii media și dev_standard", "ad": "este o probabilitate asociată cu distribuția lognormală, un număr între 0 și 1, inclusiv!este media lui ln(x)!este deviația standard a lui ln(x), un număr pozitiv"}, "LOGNORM.DIST": {"a": "(x; media; dev_standard; cumulativ)", "d": "Returnează distribuția lognormală a lui x, în care ln(x) este distribuit normal cu parametrii media și dev_standard", "ad": "este valoarea în care se evaluează funcția, un număr pozitiv!este media lui ln(x)!este deviația standard a ln(x), un număr pozitiv!este o valoare logică: pentru funcția de distribuție cumulativă, se utilizează TRUE; pentru funcția de densitate a probabilității, se utilizează FALSE"}, "LOGNORM.INV": {"a": "(probabilitate; media; dev_standard)", "d": "Returnează inversa funcției distribuție cumulativă lognormală a lui x, unde ln(x) este distribuit normal cu parametrii media și dev_standard", "ad": "este o probabilitate asociată cu distribuția lognormală, un număr între 0 și 1, inclusiv!este media lui ln(x)!este deviația standard a lui ln(x), un număr pozitiv"}, "LOGNORMDIST": {"a": "(x; media; dev_standard)", "d": "Returnează distribuția lognormală cumulativă a lui x, în care ln(x) este distribuit normal cu parametrii media și dev_standard", "ad": "este valoarea în care se evaluează funcția, un număr pozitiv!este media lui ln(x)!este deviația standard a ln(x), un număr pozitiv"}, "MAX": {"a": "(număr1; [număr2]; ...)", "d": "Returnează cea mai mare valoare dintr-un set de valori. Ignoră valorile logice și textul", "ad": "sunt de la 1 la 255 de numere, celule goale, valori logice sau numere text pentru care se calculează maximul"}, "MAXA": {"a": "(valoare1; [valoare2]; ...)", "d": "Returnează cea mai mare valoare dintr-un set de valori. Nu ignoră valorile logice și text", "ad": "sunt de la 1 la 255 de numere, celule goale, valori logice sau numere text pentru care se calculează maximul"}, "MAXIFS": {"a": "(zonă_max; zonă_criterii; criterii; ...)", "d": "Returnează valoarea maximă dintre celulele specificate după un set dat de condiții sau criterii", "ad": "celulele în care se determină valoarea maximă!este zona de celule pe care doriți să o evaluați pentru o anumită condiție!este condiția sau criteriul, sub forma unui număr, unei expresii sau unui text care definește celulele care vor fi incluse când se determină valoarea maximă"}, "MEDIAN": {"a": "(număr1; [număr2]; ...)", "d": "Returnează mediana sau numărul din mijlocul unui set de numere date", "ad": "sunt de la 1 la 255 de numere sau de nume, matrice sau referințe care conțin numere, pentru care se află mediana"}, "MIN": {"a": "(număr1; [număr2]; ...)", "d": "Returnează cel mai mic număr dintr-un set de valori. Ignoră valorile logice și textul", "ad": "sunt de la 1 la 255 de numere, celule goale, valori logice sau numere text pentru care se calculează minimul"}, "MINA": {"a": "(valoare1; [valoare2]; ...)", "d": "Returnează cea mai mică valoare dintr-un set de valori. Nu ignoră valori logice și text", "ad": "sunt de la 1 la 255 de numere, celule goale, valori logice sau numere text pentru care se calculează minimul"}, "MINIFS": {"a": "(zonă_min; zonă_criterii; criterii; ...)", "d": "Returnează valoarea minimă dintre celulele specificate după un set dat de condiții sau criterii", "ad": "celulele în care se determină valoarea minimă!este zona de celule pe care doriți să o evaluați pentru o anumită condiție!este condiția sau criteriul, sub forma unui număr, unei expresii sau unui text care definește celulele care vor fi incluse când se determină valoarea minimă"}, "MODE": {"a": "(număr1; [număr2]; ...)", "d": "Returnează valoarea cea mai frecventă sau repetitivă dintr-o matrice sau zonă de date", "ad": "sunt de la 1 la 255 de numere, nume, matrice sau referințe care conțin numere pentru care se determină modul"}, "MODE.MULT": {"a": "(număr1; [număr2]; ...)", "d": "Returnează o matrice verticală cu valorile cele mai frecvente sau repetitive dintr-o matrice sau zonă de date. Pentru o matrice orizontală, utilizați =TRANSPOSE(MODE.MULT(număr1,număr2,...))", "ad": "sunt numere, nume, matrice sau referințe de la 1 la 255 care conțin numere pentru care se determină modul"}, "MODE.SNGL": {"a": "(număr1; [număr2]; ...)", "d": "Returnează valoarea cea mai frecventă sau repetitivă dintr-o matrice sau zonă de date", "ad": "sunt de la 1 la 255 de numere sau de nume, matrice sau referințe care conțin numere pentru care se determină modul"}, "NEGBINOM.DIST": {"a": "(număr_f; număr_s; probabilitate_s; cumulativ)", "d": "Returnează distribuția binomială negativă, probabilitatea că vor exista un număr (număr_f) eșecuri înaintea succesului cu numărul număr_s, cu probabilitatea probabilitate_s a unui succes", "ad": "este numărul de erori!este numărul de succese din punctul de început!este probabilitatea unui succes; un număr între 0 și 1!este o valoare logică: pentru funcția de distribuție cumulativă, se utilizează TRUE; pentru funcția de probabilitate a masei, se utilizează FALSE"}, "NEGBINOMDIST": {"a": "(număr_f; număr_s; probabilitate_s)", "d": "Returnează distribuția binomială negativă, probabilitatea că vor exista un număr (număr_f) eșecuri înaintea succesului cu numărul număr_s, cu probabilitatea probabilitate_s a unui succes", "ad": "este numărul de erori!este numărul limită de succese!este probabilitatea unui succes; un număr între 0 și 1"}, "NORM.DIST": {"a": "(x; media; dev_standard; cumulativ)", "d": "Returnează distribuția normală pentru media specificată și deviația standard", "ad": "este valoarea pentru care se face distribuția!este media aritmetică a distribuției!este deviația standard a densității, un număr pozitiv!este o valoare logică: pentru funcția distribuție cumulativă, se utilizează TRUE; pentru funcția masă de probabilitate, se utilizează FALSE"}, "NORMDIST": {"a": "(x; media; dev_standard; cumulativ)", "d": "Returnează distribuția cumulativă normală pentru media și deviația standard specificate", "ad": "este valoarea pentru care se face distribuția!este media aritmetică a distribuției!este deviația standard a distribuției, un număr pozitiv!este o valoare logică: pentru funcția distribuție cumulativă, se utilizează TRUE; pentru funcția densitatea probabilității, se utilizează FALSE"}, "NORM.INV": {"a": "(probabilitate; media; dev_standard)", "d": "Returnează inversa distribuției cumulativ normale pentru media specificată și deviația standard", "ad": "este o probabilitate corespunzătoare distribuției normale, un număr între 0 și 1 inclusiv!este media aritmetică a distribuției!este deviația standard a distribuției, un număr pozitiv"}, "NORMINV": {"a": "(probabilitate; media; dev_standard)", "d": "Returnează inversa distribuției cumulativ normale pentru media și deviația standard specificate", "ad": "este o probabilitate corespunzătoare distribuției normale, un număr între 0 și 1 inclusiv!este media aritmetică a distribuției!este deviația standard a distribuției, un număr pozitiv"}, "NORM.S.DIST": {"a": "(z; cumulativ)", "d": "Returnează distribuția normală standard (are o medie de zero și o deviație standard de unu)", "ad": "este valoarea pentru care se calculează distribuția!este o valoare logică pentru ca funcția să returneze: funcția de distribuție cumulativă = TRUE; funcția de densitate a probabilității = FALSE"}, "NORMSDIST": {"a": "(z)", "d": "Returnează distribuția cumulativă normală standard (are o medie de zero și o deviație standard de unu)", "ad": "este valoarea pentru care se calculează distribuția"}, "NORM.S.INV": {"a": "(probabilitate)", "d": "Returnează inversa distribuției cumulativ normale standard (are o medie de zero și o deviație standard de unu)", "ad": "este o probabilitate corespunzătoare distribuției normale, un număr între 0 și 1 inclusiv"}, "NORMSINV": {"a": "(probabilitate)", "d": "Returnează inversa distribuției cumulativ normale standard (are o medie de zero și o deviație standard de unu)", "ad": "este o probabilitate corespunzătoare distribuției normale, un număr între 0 și 1 inclusiv"}, "PEARSON": {"a": "(matrice1; matrice2)", "d": "Returnează coeficientul Pearson de corelație a momentelor pro<PERSON>, r", "ad": "este un set de valori independente!este un set de valori dependente"}, "PERCENTILE": {"a": "(matrice; k)", "d": "Returnează a k-a procentilă de valori dintr-o zonă", "ad": "este matricea sau zona de date care definește poziția relativă!este valoarea procentilei cuprinsă între 0 și 1, inclusiv"}, "PERCENTILE.EXC": {"a": "(matrice; k)", "d": "Returnează a k-a procentilă de valori dintr-o zonă, unde k este intervalul 0..1, exclusiv", "ad": "este matricea sau zona de date care definește poziția relativă!este valoarea procentilei cuprinsă între 0 și 1, inclusiv"}, "PERCENTILE.INC": {"a": "(matrice; k)", "d": "Returnează a k-a procentilă de valori dintr-o zonă, unde k este intervalul 0..1, inclusiv", "ad": "este matricea sau zona de date care definește poziția relativă!este valoarea procentilei cuprinsă între 0 și 1, inclusiv"}, "PERCENTRANK": {"a": "(matrice; x; [semnifica<PERSON>ie])", "d": "Returnează rangul unei valori dintr-un set de date ca procentaj din setul de date", "ad": "este matricea sau zona de date cu valori numerice care definesc poziția relativă!este valoarea pentru care se determină rangul!este o valoare opțională care identifică numărul de cifre semnificative pentru procentajul întors, trei cifre dacă este omisă (0.xxx%)"}, "PERCENTRANK.EXC": {"a": "(matrice; x; [semnifica<PERSON>ie])", "d": "Returnează rangul unei valori dintr-un set de date ca procentaj din setul de date (0..1, exclusiv)", "ad": "este vectorul sau zona de date cu valori numerice care definesc poziția relativă!este valoarea pentru care doriți să aflați rangul!este o valoare opțională care identifică numărul de cifre semnificative pentru procentajul întors, trei cifre dacă este omisă (0.xxx%)"}, "PERCENTRANK.INC": {"a": "(matrice; x; [semnifica<PERSON>ie])", "d": "Returnează rangul unei valori dintr-un set de date ca procentaj din setul de date (0..1, inclusiv)", "ad": "este vectorul sau zona de date cu valori numerice care definesc poziția relativă!este valoarea pentru care doriți să aflați rangul!este o valoare opțională care identifică numărul de cifre semnificative pentru procentajul întors, trei cifre dacă este omisă (0.xxx%)"}, "PERMUT": {"a": "(număr; număr_ales)", "d": "Returnează numărul de permutări pentru un număr dat de obiecte care pot fi selectate din totalul de obiecte", "ad": "este numărul total de obiecte!este numărul de obiecte din fiecare permutare"}, "PERMUTATIONA": {"a": "(număr; număr_ales)", "d": "Returnează numărul de permutări pentru un număr dat de obiecte (cu repetiții) care pot fi selectate din totalul de obiecte", "ad": "este numărul total de obiecte!este numărul de obiecte din fiecare permutare"}, "PHI": {"a": "(x)", "d": "Returnează valoarea funcției de densitate pentru o distribuție normală standard", "ad": "este numărul pentru care doriți densitatea distribuției normale standard"}, "POISSON": {"a": "(x; media; cumulativ)", "d": "Returnează distribuția Poisson", "ad": "este numărul de evenimente!este valoarea numerică așteptată, un număr pozitiv!este o valoare logică: pentru probabilitatea Poisson cumulativă, se utilizează TRUE; pentru funcția probabilitate de masă Poisson, se utilizează FALSE"}, "POISSON.DIST": {"a": "(x; media; cumulativ)", "d": "Returnează distribuția Poisson", "ad": "este numărul de evenimente!este valoarea numerică așteptată, un număr pozitiv!este o valoare logică: pentru probabilitatea Poisson cumulativă, se utilizează TRUE; pentru funcția masă de probabilitate Poisson, se utilizează FALSE"}, "PROB": {"a": "(zonă_x; zonă_prob; limită_inf; [limită_sup])", "d": "Returnează probabilitatea ca valorile dintr-o zonă să fie între două limite sau egale cu limita inferioară", "ad": "este zona de valori numerice x cu care sunt asociate probabilitățile!este setul de probabilități asociate cu valori din zonă_x, valori între 0 și 1 și excluzând 0!este limita inferioară a valorii pentru care se calculează probabilitatea!este opțional limita superioară a valorii. <PERSON><PERSON><PERSON> este omisă, PROB returnează probabilitatea ca valorile din zonă_inf să fie egale cu limită_inf"}, "QUARTILE": {"a": "(matrice; quart)", "d": "Returnează cuartila unui set de date", "ad": "este matricea sau zona de celule de valori numerice pentru care se determină valoarea cuartilei!este un număr: valoarea minimă = 0; 1-a cuartilă = 1; valoarea mediană = 2; a 3-a cuartilă = 3; valoarea maximă = 4"}, "QUARTILE.INC": {"a": "(matrice; quart)", "d": "Returnează cuartila unui set de date în baza valorile procentilei din 0..1, inclusiv", "ad": "este matricea sau zona de celule de valori numerice pentru care se determină valoarea cuartilei!este un număr: valoarea minimă = 0; 1-a cuartilă = 1; valoarea mediană = 2; a 3-a cuartilă = 3; valoarea maximă = 4"}, "QUARTILE.EXC": {"a": "(matrice; quart)", "d": "Returnează cuartila unui set de date în baza valorile procentilei din 0..1, exclusiv", "ad": "este matricea sau zona de celule de valori numerice pentru care se determină valoarea cuartilei!este un număr: valoarea minimă = 0; 1-a cuartilă = 1; valoarea mediană = 2; a 3-a cuartilă = 3; valoarea maximă = 4"}, "RANK": {"a": "(număr; ref; [ordine])", "d": "Returnează rangul unui număr dintr-o listă de numere: este dimensiunea relativ la alte valori din listă", "ad": "este numărul pentru care se găsește rangul!este o matrice, o referință, o listă de numere. Valorile nenumerice sunt ignorate!este un număr: rang în lista sortată descendent = 0 sau omis; rang în lista sortată ascendent = orice valoare diferită de zero"}, "RANK.AVG": {"a": "(număr; ref; [ordine])", "d": "Returnează rangul unui număr dintr-o listă de numere: este dimensiunea relativ la alte valori din listă; dacă mai multe valori au același rang, se returnează rangul mediu", "ad": "este numărul pentru care se găsește rangul!este o matrice de sau referință la o listă de numere. Valorile nenumerice sunt ignorate!este un număr: rang în lista sortată descendent = 0 sau omis; rang în lista sortată ascendent = orice valoare diferită de zero"}, "RANK.EQ": {"a": "(număr; ref; [ordine])", "d": "Returnează rangul unui număr dintr-o listă de numere: este dimensiunea relativ la alte valori din listă; dacă mai multe valori au același rang, se returnează rangul maxim al acelui set de valori", "ad": "este numărul pentru care se găsește rangul!este o matrice de sau referință la o listă de numere. Valorile nenumerice sunt ignorate!este un număr: rang în lista sortată descendent = 0 sau omis; rang în lista sortată ascendent = orice valoare diferită de zero"}, "RSQ": {"a": "(cunoscute_y; cunoscute_x)", "d": "Returnează pătratul coeficientului Pearson de corelație moment-produs printre punctele de date cunoscute", "ad": "este o matrice sau o zonă de puncte de date și pot fi numere sau nume, matrice sau referința care conțin numere!este o matrice sau zonă de puncte de date și pot fi numere sau nume, matrice sau referințe care conțin numere"}, "SKEW": {"a": "(număr1; [număr2]; ...)", "d": "Returnează panta unei distribuții: o caracterizare a gradului de asimetrie a unei distribuții în jurul mediei sale", "ad": "sunt de la 1 la 255 de numere sau de nume, matrice sau referințe care conțin numere pentru care se calculează panta"}, "SKEW.P": {"a": "(număr1; [număr2]; ...)", "d": "Returnează panta unei distribuții pe baza unei populații: o caracterizare a gradului de asimetrie a unei distribuții în jurul mediei sale", "ad": "sunt de la 1 la 254 de numere sau de nume, matrice sau referințe care conțin numere pentru care se calculează panta de populație"}, "SLOPE": {"a": "(cunoscute_y; cunoscute_x)", "d": "Returnează panta unei linii de regresie liniară printre punctele de date cunoscute", "ad": "este o matrice sau zonă de celule de puncte de date numerice dependente și pot fi numere sau nume, matrice sau referințe care conțin numere!este setul de puncte de date independente și pot fi numere sau nume, matrice sau referințe care conțin numere"}, "SMALL": {"a": "(matrice; k)", "d": "Returnează a k-a din cele mai mici valori dintr-un set de date. De exemplu, al cincilea dintre cele mai mici numere", "ad": "este o matrice sau zonă de date numerice pentru care se determină cea mai mică a k-a valoare!este poziția valorii de întors (față de cea mai mică) din matrice sau din zonă"}, "STANDARDIZE": {"a": "(x; media; dev_standard)", "d": "Returnează o valoare normalizată dintr-o distribuție caracterizată de o medie și o deviație standard", "ad": "este valoarea de normalizat!este media aritmetică a distribuției!este deviația standard a distribuției, un număr pozitiv"}, "STDEV": {"a": "(număr1; [număr2]; ...)", "d": "Estimează deviația standard pe baza unui eșantion (ignoră valorile logice și textul din eșantion)", "ad": "sunt de la 1 la 255 de numere corespunzătoare unui eșantion de populație și pot fi numere sau referințe care conțin numere"}, "STDEV.P": {"a": "(număr1; [număr2]; ...)", "d": "Calculează deviația standard bazată pe întreaga populație dată ca argumente (ignoră valorile logice și text)", "ad": "sunt de la 1 la 255 de numere care corespund unei populații și pot fi numere sau referințe care conțin numere"}, "STDEV.S": {"a": "(număr1; [număr2]; ...)", "d": "Estimează deviația standard bazată pe un eșantion (ignoră valorile logice și text din  eșantion)", "ad": "sunt de la 1 la 255 de numere corespunzătoare unui eșantion de populație și pot fi numere sau referințe care conțin numere"}, "STDEVA": {"a": "(valoare1; [valoare2]; ...)", "d": "Estimează deviația standard bazat pe un eșantion, incluzând valori logice și text. Textul și valoarea logică FALSE au valoarea 0; valoarea logică TRUE are valoarea 1", "ad": "sunt de la 1 la 255 de valori care corespund unui eșantion de populație și pot fi valori sau nume sau referințe la valori"}, "STDEVP": {"a": "(număr1; [număr2]; ...)", "d": "Calculează deviația standard pe baza populației totale dată ca argumente (ignoră valorile logice și textul)", "ad": "sunt de la 1 la 255 de numere care corespund unei populații și pot fi numere sau referințe care conțin numere"}, "STDEVPA": {"a": "(valoare1; [valoare2]; ...)", "d": "Calculează deviația standard pe baza întregii populații, incluzând valori logice și text. Textul și valoarea logică FALSE au valoarea 0; valoarea logică TRUE are valoarea 1", "ad": "sunt de la 1 la 255 de valori care corespund unei populații și pot fi valori, nume, matrice sau referințe ce conțin valori"}, "STEYX": {"a": "(cunoscute_y; cunoscute_x)", "d": "Returnează eroarea standard a valorii y prezise pentru fiecare x dintr-o regresie", "ad": "este o matrice sau o zonă de puncte de date dependente și pot fi numere sau nume, matrice sau referințe care conțin numere!este o matrice sau zonă de puncte de date independente și pot fi numere sau nume, matrice sau referințe care conțin numere"}, "TDIST": {"a": "(x; grade_libertate; cozi)", "d": "Returnează distribuția t Student", "ad": "este valoarea numerică la care se evaluează distribuția!este un întreg indicând numărul de grade de libertate ce caracterizează distribuția!specifică numărul de cozi ale distribuției de returnat: distribuție unilaterală = 1; distribuție bilaterală = 2"}, "TINV": {"a": "(probabilitate; grade_libertate)", "d": "Returnează inversa distribuției t Student", "ad": "este probabilitatea asociată cu distribuția t Student bilaterală, un număr între 0 și 1 inclusiv!este un întreg pozitiv indicând numărul de grade de libertate care caracterizează distribuția"}, "T.DIST": {"a": "(x; grade_libertate; cumulativ)", "d": "Returnează distribuția din coada stângă t-student", "ad": "este valoarea numerică la care se evaluează distribuția!este un număr întreg care indică numărul de grade de libertate care caracterizează distribuția!este o valoare logică: pentru funcția de distribuție cumulativă, se utilizează TRUE; pentru funcția de densitate de probabilitate, se utilizează FALSE"}, "T.DIST.2T": {"a": "(x; grade_libertate)", "d": "Returnează distribuția t-student cu două cozi", "ad": "este valoarea numerică la care să evaluați distribuția!este un număr întreg care indică numărul de grade de libertate care caracterizează distribuția"}, "T.DIST.RT": {"a": "(x; grade_libertate)", "d": "Returnează distribuția t-student a cozii din stânga", "ad": "este valoarea numerică la care să evaluați distribuția!este un număr întreg care indică numărul de grade de libertate care caracterizează distribuția"}, "T.INV": {"a": "(probabilitate; grade_libertate)", "d": "Returnează inversa distribuției t Student a cozii din dreapta", "ad": "este probabilitatea asociată cu distribuția t Student bilaterală, un număr între 0 și 1 inclusiv!este un întreg pozitiv indicând numărul de grade de libertate care caracterizează distribuția"}, "T.INV.2T": {"a": "(probabilitate; grade_libertate)", "d": "Returnează inversa cu două cozi a distribuției t Student", "ad": "este probabilitatea asociată cu distribuția t Student bilaterală, un număr între 0 și 1 inclusiv!este un întreg pozitiv indicând numărul de grade de libertate care caracterizează distribuția"}, "T.TEST": {"a": "(matrice1; matrice2; cozi; tip)", "d": "Returnează probabilitatea asociată cu un test t Student", "ad": "este primul set de date!este al doilea set de date!specifică tipul distribuției întoarse: distribuție unilaterală = 1; distribuție bilaterală = 2!este felul de test t: pereche = 1, două eșantioane varianță egală (homoscedastică) = 2, două eșantioane varianță inegală = 3"}, "TREND": {"a": "(y_cunoscute; [x_cunoscute]; [x_noi]; [const])", "d": "Returnează numere într-o tendință liniară care se potrivește punctelor de date cunoscute, utilizând metoda celor mai mici pătrate", "ad": "este o zonă sau o matrice de valori y deja cunoscute în relația y = mx + b!este o zonă sau o matrice opțională de valori x deja cunoscute din relația y = mx + b, o matrice de aceeași dimensiune cu y_cunoscute!este o zonă sau o matrice de valori x noi pentru care TREND returnează valorile y corespunzătoare!este o valoare logică: constanta b este calculată normal dacă Const = TRUE sau se omite; b este egal cu 0 dacă Const = FALSE"}, "TRIMMEAN": {"a": "(matrice; procent)", "d": "Returnează media porțiuni interioare a unui set de valori de date", "ad": "este zona sau matricea de valori de trunchiat și de mediat!este numărul fracționar de puncte de date de exclus din partea de sus și din partea de jos a setului de date"}, "TTEST": {"a": "(matrice1; matrice2; cozi; tip)", "d": "Returnează probabilitatea asociată cu un test t Student", "ad": "este primul set de date!este al doilea set de date!specifică tipul distribuției de returnat: distribuție unilaterală = 1; distribuție bilaterală = 2!este tipul de test t: pereche = 1, două eșantioane varianță egală (homoscedastică) = 2, două eșantioane varianță inegală = 3"}, "VAR": {"a": "(număr1; [număr2]; ...)", "d": "Estimează varianța pe baza unui eșantion (ignoră valorile logice și textul din eșantion)", "ad": "sunt de la 1 la 255 de argumente numerice corespunzătoare unui eșantion de populație"}, "VAR.P": {"a": "(număr1; [număr2]; ...)", "d": "Calculează varianța bazată pe întreaga populație (ignoră  valorile logice și text din populație)", "ad": "sunt de la 1 la 255 de argumente numerice care corespund unei populații"}, "VAR.S": {"a": "(număr1; [număr2]; ...)", "d": "Estimează varianța bazată pe un eșantion (ignoră valorile logice și text din eșantion)", "ad": "sunt de la 1 la 255 de argumente numerice corespunzătoare unui eșantion de populație"}, "VARA": {"a": "(valoare1; [valoare2]; ...)", "d": "Estimează varianța pe baza unui eșantion, incluzând valorile logice și text. Textul și valoarea logică FALSE au valoarea 0; valoarea logică TRUE are valoarea 1", "ad": "sunt de la 1 la 255 de argumente valoare care corespund unui eșantion de populație"}, "VARP": {"a": "(număr1; [număr2]; ...)", "d": "Calculează varianța pe baza populației totale (ignoră valorile logice și textul din populație)", "ad": "sunt de la 1 la 255 de argumente numerice care corespund unei populații"}, "VARPA": {"a": "(valoare1; [valoare2]; ...)", "d": "Calculează varianța pe baza întregii populații, incluzând valori logice și text. Textul și valoarea logică FALSE au valoarea 0; valoarea logică TRUE are valoarea 1", "ad": "sunt de la 1 la 255 de argumente valoare care corespund unei populații"}, "WEIBULL": {"a": "(x; alfa; beta; cumulativ)", "d": "Returnează distribuț<PERSON>bull", "ad": "este valoarea la care se evaluează funcția, un număr nenegativ!este un parametru pentru distribuție, un număr pozitiv!este un parametru pentru distribuție, un număr pozitiv!este o valoare logică: pentru funcția distribuție cumulativă, se utilizează TRUE; pentru funcția masă de probabilitate, se utilizează FALSE"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; cumulativ)", "d": "Returnează distribuț<PERSON>bull", "ad": "este valoarea la care se evaluează funcția, un număr nenegativ!este un parametru pentru distribuție, un număr pozitiv!este un parametru pentru distribuție, un număr pozitiv!este o valoare logică: pentru funcția distribuție cumulativă, se utilizează TRUE; pentru funcția probabilitate de masă, se utilizează FALSE"}, "Z.TEST": {"a": "(matrice; x; [sigma])", "d": "Returnează valoarea P unilaterală a unui test z", "ad": "este matricea sau zona de date față de care se face testul X!este valoarea de testat!este deviația standard a populației (cunoscută). Dacă este omisă, este utilizată deviația standard a eșantionului"}, "ZTEST": {"a": "(matrice; x; [sigma])", "d": "Returnează valoarea P unilaterală a unui test z", "ad": "este matricea sau zona de date față de care se face testul X!este valoarea de testat!este deviația standard a populației (cunoscută). Dacă este omisă, este utilizată deviația standard a eșantionului"}, "ACCRINT": {"a": "(emis; prim_cupon; tranzacție; rată; par; frecvență; [bază]; [calc_metodă])", "d": "Returnează dobânda acumulată pentru o asigurare care plătește periodic dobândă.", "ad": "este data de emitere a asigur<PERSON>rii, exprimată ca număr serial!este data primei dobânzi, exprimată ca număr serial!este data de stabilire a asigurării, exprimată ca număr serial!este rata anuală a cuponului de asigurare!este valoarea par a asigurării!este numărul de plăți pe cupon dintr-un an!este tipul de bază de numărare a zilelor care trebuie utilizat!este o valoare logică: la dobânda cumulată de la data emiterii = TRUE sau omis; pentru a calcula de la data ultimei plăți pe cupon = FALSE"}, "ACCRINTM": {"a": "(emis; tranzacție; rată; par; [bază])", "d": "Returnează dobânda acumulată pentru o asigurare care plătește dobândă la maturitate", "ad": "este data de emitere a asigur<PERSON>rii, exprimată ca număr serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este rata anuală a cuponului de asigurare!este valoarea pară a asigurării!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "AMORDEGRC": {"a": "(cost; data_cump; prima_per; recuperat; perioadă; rată; [bază])", "d": "Returnează deprecierea lineară proporțională pentru un bun pentru fiecare perioadă de contabilizare.", "ad": "este costul activului!este data de cumpărare a activului!este data de sfârșit a primei perioade!este valoarea de recuperat la sfârșitul vieții activului.!este perioada!este rata de depreciere!într-o perioadă de un an: 0 pentru un an de 360 de zile, 1 pentru anul actual, 3 pentru un an de 365 de zile."}, "AMORLINC": {"a": "(cost; data_cump; prima_per; recuperat; perioadă; rată; [bază])", "d": "Returnează deprecierea lineară proporțională pentru un bun pentru fiecare perioadă de contabilizare.", "ad": "este costul activului!este data de cumpărare a activului!este data de sfârșit a primei perioade!este valoarea de recuperat la sfârșitul vieții activului.!este perioada!este rata de depreciere!într-o perioadă de un an: 0 pentru un an de 360 de zile, 1 pentru anul actual, 3 pentru un an de 365 de zile."}, "COUPDAYBS": {"a": "(tranzacție; maturitate; frecvență; [bază])", "d": "Returnează numărul de zile de la începutul perioadei de cupon la data de lichidare", "ad": "este data de lichidare a asigur<PERSON><PERSON>i, exprimată ca număr serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este numărul de plăți pe cupon pe an!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "COUPDAYS": {"a": "(tranzacție; maturitate; frecvență; [bază])", "d": "Returnează numărul de zile în perioada de cupon care conține și data de lichidare", "ad": "este data de lichidare a asigur<PERSON><PERSON>i, exprimată ca număr serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este numărul de plăți pe cupon pe an!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "COUPDAYSNC": {"a": "(tranzacție; maturitate; frecvență; [bază])", "d": "Returnează numărul de zile de la data de stabilire a asigurării la data următorului cupon", "ad": "este data de stabilire a asigur<PERSON><PERSON>i, exprimată ca număr serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este numărul de plăți pe cupon dintr-un an!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "COUPNCD": {"a": "(tranzacție; maturitate; frecvență; [bază])", "d": "Returnează următoarea dată de pe cupon după data de stabilire a asigurării", "ad": "este data de stabilire a asigurării exprimată ca număr serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este numărul de plăți pe cupon dintr-un an!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "COUPNUM": {"a": "(tranzacție; maturitate; frecvență; [bază])", "d": "Returnează numărul de cupoane de plată între data de stabilire a asigurării și data de maturitate", "ad": "este data de stabilire a asigur<PERSON>rii, exprimată ca număr de dată serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este numărul de plăți pe cupon dintr-un an!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "COUPPCD": {"a": "(tranzacție; maturitate; frecvență; [bază])", "d": "Returnează data de pe cuponul precedent înainte de data de stabilire a asigurării", "ad": "este data de stabilire a asigur<PERSON><PERSON>i, exprimată ca număr serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este numărul de plăți pe cupon dintr-un an!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "CUMIPMT": {"a": "(rată; nper; pv; per_start; per_ultima; tip)", "d": "Returnează dobânda cumulativă plătită între două perioade", "ad": "este rata dobânzii!este numărul total de perioade de plată!este valoarea prezentă!este prima perioadă din calcul!este ultima perioadă din calcul!este temporizarea plății"}, "CUMPRINC": {"a": "(rată; nper; pv; per_start; per_ultima; tip)", "d": "Returnează suma cumulativă plătită dintr-un împrumut între două perioade", "ad": "este rata dobânzii!este numărul total de perioade de plată!este valoarea prezentă!este prima perioadă din calcul!este ultima perioadă din calcul!este temporizarea plății"}, "DB": {"a": "(cost; recuperat; viață; perioadă; [lună])", "d": "Returnează amortizarea unui mijloc fix pentru o perioadă specificată utilizând metoda balanței cu amortizare fixă", "ad": "este costul inițial al mijlocului fix!este valoarea rămasă la sfârșitul duratei de viață a mijlocului fix!este numărul de perioade în care se amortizează mijlocul fix (uneori este numită durata de utilizare a mijlocului fix)!este perioada pentru care se calculează amortizarea. Perioada se măsoară în aceleași unități ca viață!este numărul de luni din primul an. Dacă luna este omisă, se presupune că este 12"}, "DDB": {"a": "(cost; val_reziduală; viață; perioadă; [factor])", "d": "Returnează amortizarea unui mijloc fix pentru o perioadă specificată utilizând metoda balanței amortizare dublă sau altă metodă specificată", "ad": "este costul inițial al mijlocului fix!este valoarea rămasă la sfârșitul duratei de viață a mijlocului fix!este numărul de perioade în care este amortizat mijlocul fix (uneori numită durata de utilizare)!este perioada pentru care se calculează amortizarea. Perioada trebuie să fie măsurată în aceleași unități de măsură ca viață!este rata cu care scade balanța. Dacă Factor este omis, se presupune că este 2 (metoda balanței cu dublă amortizare)"}, "DISC": {"a": "(tranzac<PERSON>ie; maturitate; pr; rambursare; [bază])", "d": "Returnează rata de reducere pentru o asigurare", "ad": "este data de tranzacție a asigur<PERSON>rii, exprimată ca număr serial!este data maturizării asigurării, exprimată ca număr serial!este prețul asigurării la 100 lei valoare reală!este valoarea de recuperare a asigurării la 100 lei valoare reală!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "DOLLARDE": {"a": "(preț_fracție; fracție)", "d": "Se efectuează conversia unui preț în dolari, exprimat ca fracție, într-un preț în dolari, exprimat ca număr zec<PERSON>l", "ad": "este un număr exprimat ca fracție!este numărul întreg de utilizat ca numitor al fracției"}, "DOLLARFR": {"a": "(preț_zecimal; fracție)", "d": "Se efectuează conversia unui preț în dolari, exprimat ca număr z<PERSON>, într-un preț în dolari, exprimat ca fracție", "ad": "este un număr zecimal!este numărul întreg de utilizat ca numitor al fracției"}, "DURATION": {"a": "(tranzacție; maturitate; cupon; produs; frecvență; [bază])", "d": "Returnează durata anuală a unei asigurări cu plăți periodice ale dobânzii", "ad": "este data de stabilire a asigur<PERSON><PERSON><PERSON>, exprimată ca număr serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este rata anuală a dobânzii de cupon!este produsul anual al dobânzii!este numărul de plăți pe cupon dintr-un an!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "EFFECT": {"a": "(rată_nominală; n_per)", "d": "Returnează rata anuală efectivă a dobânzii", "ad": "este rata dobânzii nominală!este numărul de perioade compuse per an"}, "FV": {"a": "(rată; nper; pmt; [pv]; [tip])", "d": "Returnează valoarea viitoare a unei investiții bazate pe plăți constante periodice și o rată constantă a dobânzii", "ad": "este rata dobânzii pe perioadă. De exemplu, utilizați 6%/4 pentru plăți trimestriale la o dobândă anuală de 6%!este numărul total de perioade de plată dintr-o investiție!este plata făcută pentru fiecare perioadă; nu se modifică pe toată durata investiției!este valoarea prezentă sau valoarea actuală a unui vărsământ unic corespunzător unei serii de plăți viitoare. Dacă se omite, Pv = 0!este o valoare care reprezintă scadența plății: plata la începutul perioadei = 1; plata la sfârșitul perioadei = 0 sau se omite"}, "FVSCHEDULE": {"a": "(principal; plan)", "d": "Returnează valoarea viitoare a unui principal inițial, după aplicarea unei serii de rate ale dobânzii compuse", "ad": "este valoarea prezentă!este un index de rate de dobândă de aplicat"}, "INTRATE": {"a": "(tranzacție; maturitate; investiție; rambursare; [bază])", "d": "Returnează rata dobânzii pentru o asigurare în care s-a investit integral", "ad": "este data de tranzacție a asigur<PERSON>rii, exprimată ca număr serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este suma investită în asigurare!este suma care va fi primită la maturitate!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "IPMT": {"a": "(rată; per; nper; pv; [fv]; [tip])", "d": "Returnează pentru o perioadă dată plata dobânzii unei investiții, bazată pe plăți constante periodice și pe o rată constantă a dobânzii", "ad": "este rata dobânzii pe o perioadă. De exemplu, utilizați 6%/4 pentru plăți trimestriale la o dobândă anuală de 6%!este perioada pentru care se determină dobânda și este în intervalul de la 1 la Nper!este numărul total de perioade de plată dintr-o investiție!este valoarea prezentă sau valoarea actuală a unui vărsământ unic corespunzător unei serii de plăți viitoare!este valoarea viitoare sau balanța în numerar la care se ajunge după ce ultima plată este făcută. Dacă se omite, Fv = 0!este o valoare logică reprezentând scadența plății: la sfârșitul perioadei = 0 sau se omite, la începutul perioadei = 1"}, "IRR": {"a": "(valori; [estim])", "d": "Returnează rata de rentabilitate internă pentru o serie de fluxuri de numerar", "ad": "este o matrice sau o  referință la celule care conțin numere pentru care calculați rata de rentabilitate internă!este un număr care reprezintă o aproximare a rezultatului funcției IRR; 0.1 (10 procente) dacă este omis"}, "ISPMT": {"a": "(rată; per; nper; pv)", "d": "Returnează dobânda plătită într-o anumită perioadă pentru o investiție", "ad": "rata dobânzii pe o perioadă. De exemplu, utilizați 6%/4 pentru plăți trimestriale la o dobândă anuală de 6%!perioada pentru care se determină dobânda!numărul de perioade de plată dintr-o investiție!valoarea actuală a unui vărsământ unic corespunzător unei serii de plăți viitoare"}, "MDURATION": {"a": "(tranzacție; maturitate; cupon; produs; frecvență; [bază])", "d": "Returnează durata Macauley modificată pentru o asigurare cu o valoare nominală de 100 lei", "ad": "este data de stabilire a asigur<PERSON>rii, exprimată ca număr serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este rata anuală a cuponului!este produsul anual al asigurării!este numărul de plăți pe cupon dintr-un an!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "MIRR": {"a": "(valori; rată_finan; rată_reinvest)", "d": "Returnează rata de rentabilitate internă pentru o serie de fluxuri periodice de numerar, luând în considerare costul investiției și dobânda din reinvestirea numerarului", "ad": "este o matrice sau o referință la celule care conțin numere care reprezintă o serie de plăți (negative) și venituri (pozitive) la perioade regulate!este rata dobânzii plătită în bani utilizați în fluxul de numerar!este rata dobânzii care intră în fluxul de numerar la reinvestirea banilor"}, "NOMINAL": {"a": "(rată_efectivă; n_per)", "d": "Returnează rata anuală a dobânzii nominale", "ad": "este rata dobânzii efective!este numărul de perioade compuse dintr-un an"}, "NPER": {"a": "(rată; pmt; pv; [fv]; [tip])", "d": "Returnează numărul de perioade pentru o investiție bazată pe plăți constante periodice și o rată constantă a dobânzii", "ad": "este rata dobânzii pe perioadă. De exemplu, utilizați 6%/4 pentru plăți trimestriale la o dobândă anuală de 6%!este plata făcută pentru fiecare perioadă; nu se modifică pe toată durata investiției!este valoarea prezentă sau valoarea actuală a unui vărsământ unic corespunzător unei serii de plăți viitoare!este valoarea viitoare sau balanța în numerar la care se ajunge după ce este făcută ultima plată. Dacă se omite, se utilizează zero!este o valoare logică: plata la începutul perioadei = 1; plata la sfârșitul perioadei = 0 sau se omite"}, "NPV": {"a": "(rată; valoare1; [valoare2]; ...)", "d": "Returnează valoarea netă prezentă a unei investiții bazată pe o rată de actualizare și o serie de plăți viitoare (valori negative) și venituri  (valori pozitive)", "ad": "este rata de actualizare pentru o perioadă!sunt de la 1 la 254 de plăți și venituri, distanțate egal în timp și care au loc la sfârșitul fiecărei perioade"}, "ODDFPRICE": {"a": "(tranzac<PERSON>ie; maturitate; emis; prim_cupon; rată; produs; rambursare; frecvenț<PERSON>; [bază])", "d": "Returnează prețul pentru 100 lei valoare reală pentru o asigurare cu o primă perioadă impară", "ad": "este data de tranzacție a asigur<PERSON>rii, exprimată ca număr de dată serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este data de eliberare a asigurării, exprimată ca număr serial!este prima dată de pe cuponul de asigurare, exprimată ca număr serial!este rata dobânzii asigurării!este produsul anual al asigurării!este valoarea de rambursare a asigurării la 100 lei valoare reală! este numărul de plăți pe cupon dintr-un an!este tipul de bază pentru numărarea zilelor de utilizat"}, "ODDFYIELD": {"a": "(tranzac<PERSON>ie; maturitate; emis; prim_cupon; rată; pr; rambursare; frecvenț<PERSON>; [bază])", "d": "Returnează produsul unei asigurări cu o primă perioadă impară", "ad": "este perioada de tranzacție a asig<PERSON><PERSON><PERSON><PERSON>, exprimată ca număr serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este data de emitere a asigur<PERSON>rii, exprimată ca număr serial!este prima dată de pe cupon, exprimată ca număr serial!este rata dobânzii asigurării!este prețul asigurării!este valoarea de rambursare a asigurării la 100 lei valoare reală! este numărul de plăți pe cupon dintr-un an!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "ODDLPRICE": {"a": "(tranzac<PERSON>ie; maturitate; ultim_cupon; rată; produs; rambursare; frecvență; [bază])", "d": "Returnează prețul pentru 100 lei valoare reală a unei asigurări cu o ultimă perioadă impară", "ad": "este data de tranzacție a asigur<PERSON>rii, exprimată ca număr serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este ultima dată de pe cupon a asigur<PERSON>rii, exprimată ca număr serial!este rata dobânzii asigurării!este produsul anual al asigurării!este valoarea de rambursare a asigurării la 100 lei valoare reală! este numărul de plăți pe cupon dintr-un an!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "ODDLYIELD": {"a": "(tranzac<PERSON>ie; maturitate; ultim_cupon; rată; pr; rambursare; frecvenț<PERSON>; [bază])", "d": "Returnează produsul unei asigurări cu o perioadă ultimă impară", "ad": "este data de tranzacție a asigur<PERSON>rii, exprimată ca număr serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este ultima dată de pe cupon a asigur<PERSON>rii, exprimată ca număr serial!este rata dobânzii asigurării!este prețul asigurării!este valoarea de rambursare a asigurării la 100 lei valoare reală! este numărul de plăți pe cupon dintr-un an!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "PDURATION": {"a": "(rată; pv; fv)", "d": "Returnează numărul de perioade necesare pentru ca o investiție să atingă o valoare specificată", "ad": "este rata dobânzii pentru fiecare perioadă.!este valoarea prezentă a investiției!este valoarea viitoare dorită a investiției"}, "PMT": {"a": "(rată; nper; pv; [fv]; [tip])", "d": "Calculează plata pentru un împrumut bazat pe plăți constante și o rată constantă a dobânzii", "ad": "este rata dobânzii pentru împrumut pe o perioadă. De exemplu, utilizați 6%/4 pentru plăți trimestriale la o dobândă anuală de 6%!este numărul total de plăți pentru împrumut!este valoarea prezentă: valoarea totală actuală a unei serii de plăți viitoare!este valoarea viitoare sau balanța în numerar la care se ajunge după ce este făcută ultima plată, 0 (zero) dacă se omite!este o valoare logică: plata la începutul perioadei = 1; plata la sfârșitul perioadei = 0 sau se omite"}, "PPMT": {"a": "(rată; per; nper; pv; [fv]; [tip])", "d": "Returnează plata efectivă pentru o investiție dată bazată pe plăți constante periodice și o rată constantă a dobânzii", "ad": "este rata dobânzii pe o perioadă. De exemplu, utilizați 6%/4 pentru plăți trimestriale la o dobândă anuală de 6%!specifică perioada și este în intervalul de la 1 la Nper!este numărul total de perioade de plată dintr-o investiție!este valoarea prezentă: valoarea totală a unui vărsământ unic corespunzător unei serii de plăți viitoare!este valoarea viitoare sau balanța în numerar la care se ajunge după ce este făcută ultima plată!este o valoare logică: plata la începutul perioadei = 1; plata la sfârșitul perioadei = 0 sau se omite"}, "PRICE": {"a": "(tranzacție; maturitate; rată; produs; rambursare; frecvenț<PERSON>; [bază])", "d": "Returnează prețul la 100 lei valoare reală al unei asigurări care plătește dobândă periodic", "ad": "este data de tranzacție a asigur<PERSON>rii, exprimată ca număr serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este rata anuală a cuponului de asigurare!este produsul anual al asigurării!este valoarea de rambursare la 100 lei valoare reală!este numărul de plăți prin cupon într-un an!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "PRICEDISC": {"a": "(tranzac<PERSON>ie; maturitate; reducere; rambursare; [bază])", "d": "Returnează prețul la 100 lei valoare reală dintr-o asigurare cu reducere", "ad": "este data de tranzacție a asigur<PERSON>rii, exprimată ca număr serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este rata de reducere a asigurării!este rata de rambursare a asigurării la 100 lei valoare reală!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "PRICEMAT": {"a": "(tranzac<PERSON>ie; maturitate; emis; rată; produs; [bază])", "d": "Returnează prețul la 100 lei valoare reală a unei asigurări care plătește dobânda la maturitate", "ad": "este data de stabilire a asigur<PERSON><PERSON>i, exprimată ca număr de dată serial!este data de lichidare a asigur<PERSON>rii, exprimată ca număr de dată serial!este data de emitere a asigur<PERSON>rii, exprimată ca număr de dată serial!este rata dobânzii asigurării la data emiterii!este produsul anual al asigurării!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "PV": {"a": "(rată; nper; pmt; [fv]; [tip])", "d": "Returnează valoarea prezentă a unei investiții: valoarea totală actuală a unei serii de plăți viitoare", "ad": "este rata dobânzii pe o perioadă. De exemplu, utilizați 6%/4 pentru plăți trimestriale la o dobândă anuală de 6%!este numărul total de perioade de plată dintr-o investiție!este plata făcută în fiecare perioadă și nu se modifică pe durata investiției!este valoarea viitoare sau balanța în numerar la care se ajunge după ce este făcută ultima plată!este o valoare logică: plata la începutul perioadei = 1; plata la sfârșitul perioadei = 0 sa omisă"}, "RATE": {"a": "(nper; pmt; pv; [fv]; [tip]; [estim])", "d": "Returnează rata dobânzii pe perioadă pentru un împrumut sau o investiție. De exemplu, utilizați 6%/4 pentru plăți trimestriale la o dobândă anuală de 6%", "ad": "este numărul total de perioade de plată pentru împrumut sau investiție!este plata făcută în fiecare perioadă și nu se modifică pe toată durata împrumutului sau investiției!este valoarea prezentă: valoarea totală actuală a unei serii de plăți viitoare!este valoarea viitoare sau balanța în numerar la care se ajunge după ce este făcută ultima plată. Dacă se omite, Fv = 0!este o valoare logică: plata la începutul perioadei = 1; plata la sfârșitul perioadei = 0 sau se omite!este propria aproximare a ratei; dacă se omite, estim = 0,1 (10 procente)"}, "RECEIVED": {"a": "(tranzac<PERSON>ie; maturitate; investiție; reducere; [bază])", "d": "Returnează suma primită la maturitate pentru o asigurare plătită integral", "ad": "este data de lichidare a asig<PERSON><PERSON><PERSON><PERSON>, exprimată ca număr serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este suma investită în asigurare!este rata de reducere!este  tipul de bază de numărare a zilelor care trebuie utilizat"}, "RRI": {"a": "(nper; pv; fv)", "d": "Returnează o rată a dobânzii echivalentă pentru creșterea unei investiții", "ad": "este numărul perioadelor pentru investiție!este valoarea prezentă a investiției!este valoarea viitoare a investiției"}, "SLN": {"a": "(cost; recuperat; viață)", "d": "Returnează amortizarea liniară a unui mijloc fix pe o perioadă", "ad": "este costul inițial al mijlocului fix!este valoarea rămasă la sfârșitul duratei de viață a mijlocului fix!este numărul de perioade în care se amortizează mijlocul fix (uneori numită durata de utilizare)"}, "SYD": {"a": "(cost; recuperat; viață; per)", "d": "Returnează amortizarea în regresie aritmetică a unui mijloc fix pentru o perioadă specificată", "ad": "este costul inițial al mijlocului fix!este valoarea rămasă la sfârșitul duratei de viață a mijlocului fix!este numărul de perioade în care se amortizează mijlocul fix (uneori numită durata de utilizare)!este perioada și trebuie să fie măsurată în aceleași unități ca viață"}, "TBILLEQ": {"a": "(tranzac<PERSON>ie; maturitate; reducere)", "d": "Returnează produsul echivalent cu un bond pentru un certificat de trezorerie", "ad": "este data de lichidare a certificatului de trezorerie, exprimată ca număr serial!este data de maturitate a certificatului de trezorerie, exprimată ca număr serial!este rata de reducere a certificatului de trezorerie"}, "TBILLPRICE": {"a": "(tranzac<PERSON>ie; maturitate; reducere)", "d": "Returnează prețul pentru 100 lei valoare reală a unui certificat de trezorerie", "ad": "este data de lichidare a certificatului de trezorerie, exprimată ca număr serial!este data de maturitate a certificatului de trezorerie, exprimată ca număr serial!este rata de reducere a certificatului de trezorerie"}, "TBILLYIELD": {"a": "(tranzacție; maturitate; pr)", "d": "Returnează produsul pentru un certificat de trezorerie", "ad": "este data de lichidare a certificatului de trezorerie, exprimată ca număr serial!este data de maturitate a certificatului de trezorerie, exprimată ca număr serial!este prețul certificatului de trezorerie la 100 lei valoare reală"}, "VDB": {"a": "(cost; recuperat; viață; per_start; per_ultima; [factor]; [nr_comutare])", "d": "Returnează amortizarea unui mijloc fix pentru orice perioadă specificată, incluzând perioade parțiale, utilizând metoda balanței amortizare dublă sau altă metodă specificată", "ad": "este costul inițial al mijlocului fix!este valoarea rămasă la sfârșitul duratei de viață a mijlocului fix!este numărul de perioade în care se amortizează mijlocul fix (uneori este numită durata de utilizare a mijlocului fix)!este perioada de început de la care se calculează amortizarea, măsurată în aceleași unități ca viață!este perioada de sfârșit pentru care se calculează amortizarea, măsurată în aceleași unități ca viață!este rata la care se deplasează balanța, 2 (balanță cu amortizare dublă) dacă este omisă!comută în amortizare liniară când amortizarea este mai mare decât deplasarea balanței = FALSE sau omisă; nu comută = TRUE"}, "XIRR": {"a": "(valori; date; [estim])", "d": "Returnează rata internă de întoarcere pentru un calendar de fluxuri monetare", "ad": "o serie de fluxuri monetare care corespunde unui calendar de plăți în funcție de date!este un calendar de date de plată care corespunde plăților fluxurilor monetare!este un număr pe care îl ghiciți că se apropie de rezultatul XIRR"}, "XNPV": {"a": "(rată; valori; date)", "d": "Returnează valoarea netă prezentă a fluxurilor monetare", "ad": "este rata de reducere care se aplică fluxurilor monetare!este o serie de fluxuri monetare care corespunde unui calendar de plăți în funcție de date!este un calendar de date de plată care corespunde cu plățile fluxului monetar"}, "YIELD": {"a": "(tranzac<PERSON>ie; maturitate; rată; pr; rambursare; frecven<PERSON><PERSON>; [bază])", "d": "Returnează produsul unei asigurări care plătește dobândă periodic", "ad": "este data de tranzacție a asigur<PERSON>rii, exprimată ca număr serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este rata anuală a cuponului de asigurare!este prețul asigurării la 100 lei valoare reală!este valoarea de rambursarea asigurării la 100 lei valoare reală!este numărul de plăți cu cupon într-un an!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "YIELDDISC": {"a": "(tranzac<PERSON>ie; maturitate; pr; rambursare; [bază])", "d": "Returnează produsul anual pentru o asigurare cu reducere. De exemplu, certificatele de trezorerie", "ad": "este data de tranzacție a asigur<PERSON>rii, exprimată ca număr de dată serial!este data de maturitate a asigurării, exprimată ca număr serial!este prețul asigurării la 100 lei valoare reală!este valoarea de rambursare a asigurării la 100 lei valoare reală!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "YIELDMAT": {"a": "(tranzac<PERSON>ie; maturitate; emis; rată; pr; [bază])", "d": "Returnează produsul anual al unei asigurări care plătește dobândă la maturizare", "ad": "este data de stabilire a asigur<PERSON>rii, exprimată ca număr de dată serial!este data de maturitate a asigur<PERSON>rii, exprimată ca număr serial!este data de emitere a asigur<PERSON>rii, exprimată ca număr serial!este rata dobânzii asigurării la data emiterii!este prețul asigurării la 100 lei valoare reală!este tipul de bază de numărare a zilelor care trebuie utilizat"}, "ABS": {"a": "(număr)", "d": "Returnează valoarea absolută a unui număr, un număr fără semnul său", "ad": "este numărul real pentru care se calculează valoarea absolută"}, "ACOS": {"a": "(număr)", "d": "Returnează arccosinusul unui număr, în radiani în intervalul 0 Pi. Arccosinusul este unghiul al cărui cosinus este număr", "ad": "este cosinusul unghiului și este între -1 și 1"}, "ACOSH": {"a": "(număr)", "d": "Returnează inversa cosinusului hiperbolic al unui număr", "ad": "este orice număr real egal sau mai mare decât 1"}, "ACOT": {"a": "(număr)", "d": "Returnează arccotangenta unui număr, exprimată în radiani, din zona de la 0 la Pi.", "ad": "este cotangenta unghiului pe care îl doriți"}, "ACOTH": {"a": "(număr)", "d": "Returnează cotangenta hiperbolică inversată a unui număr", "ad": "este cotangenta hiperbolică a unghiului pe care îl doriți"}, "AGGREGATE": {"a": "(num_func<PERSON>ie; opțiuni; ref1; ...)", "d": "Returnează o interogare agregată dintr-o listă sau bază de date", "ad": "este numărul de la 1 la 19 care specifică funcția de rezumare pentru interogarea agregată.!este opțiunea (0=nu se ignoră nimic,1=se ignoră rândurile filtrate,2=se ignoră valorile de eroare,3=se ignoră valorile de eroare și rândurile filtrate)!este matricea sau intervalul de date numerice la care se calculează interogarea agregată!indică poziția din matrice; este poziția numărul k de la mare la mic, k de la mic la mare, procentila k sau cuartila k.!este numărul de la 1 la 19 care specifică funcția de rezumare pentru interogarea agregată.!este opțiunea (0=nu se ignoră nimic,1=se ignoră rândurile filtrate,2=se ignoră valorile de eroare,3=se ignoră valorile de eroare și rândurile filtrate)!sunt intervale de la 1 la 253 sau referințe pentru care doriți interogarea agregată"}, "ARABIC": {"a": "(text)", "d": "Efectuează conversia unui număr roman în număr arab", "ad": "este numărul roman căruia îi efectuați conversia"}, "ASC": {"a": "(text)", "d": "Pentru limbile cu set de caractere pe doi octeți (DBCS), funcția modifică caracterele cu lățime întreagă (doi octeți) în caractere cu lățime pe jumătate (un octet)", "ad": "textul a cărui modificare se dorește"}, "ASIN": {"a": "(număr)", "d": "Returnează arcsinusul unui număr în radiani, în intervalul -Pi/2 Pi/2", "ad": "este sinusul unghiului și este de la -1 la 1"}, "ASINH": {"a": "(număr)", "d": "Returnează inversa sinusului hiperbolic al unui număr", "ad": "este orice număr real egal sau mai mare decât 1"}, "ATAN": {"a": "(număr)", "d": "Returnează arctangenta unui număr în radiani, în intervalul -Pi/2  Pi/2", "ad": "este tangenta unghiului care se calculează"}, "ATAN2": {"a": "(num_x; num_y)", "d": "Returnează arctangenta coordonatelor x și y specificate, în radiani între -Pi și Pi, excluzând -Pi", "ad": "este coordonata x a punctului!este coordonata y a punctului"}, "ATANH": {"a": "(număr)", "d": "Returnează inversa tangentei hiperbolice a unui număr", "ad": "este orice număr real între -1 și 1 excluzând -1 și 1"}, "BASE": {"a": "(număr; bază; [lungime_min])", "d": "Efectuează conversia unui număr într-o reprezentare text cu baza dată", "ad": "este numărul căruia îi efectuați conversia!este baza în care doriți să efectuați conversia numărului!este lungimea minimă a șirului returnat. Dacă sunt omise, nu se adaugă zerourile inițiale"}, "CEILING": {"a": "(număr; semnificație)", "d": "Rotunjește prin adaos un număr, la cel mai apropiat multiplu de argument de precizie", "ad": "este valoarea de rotunjit!este multiplul la care se face rotunjirea"}, "CEILING.MATH": {"a": "(număr; [semnificație]; [mod])", "d": "Rotunjește prin adaos un număr, la cel mai apropiat întreg sau la cel mai apropiat multiplu de semnificație", "ad": "este valoarea de rotunjit!este multiplul la care se face rotunjirea!atunci când este dat și non-zero, această funcție se va rotunji de la zero"}, "CEILING.PRECISE": {"a": "(număr; [semnificație])", "d": "Returnează un număr care este rotunjit la cel mai apropiat întreg sau la cel mai apropiat multiplu semnificativ", "ad": "este valoarea de rotunjit!este multiplul la care se face rotunjirea"}, "COMBIN": {"a": "(număr; număr_ales)", "d": "Returnează numărul de combinări pentru un număr dat de elemente", "ad": "este numărul total de elemente!este numărul de elemente din fiecare combinare"}, "COMBINA": {"a": "(număr; număr_ales)", "d": "Returnează numărul de combinări cu repetiții pentru un număr dat de elemente", "ad": "este numărul total de elemente!este numărul de elemente din fiecare combinare"}, "COS": {"a": "(număr)", "d": "Returnează cosinusul unui unghi", "ad": "este unghiul în radiani pentru care se calculează cosinusul"}, "COSH": {"a": "(număr)", "d": "Returnează cosinusul hiperbolic al unui număr", "ad": "este orice număr real"}, "COT": {"a": "(număr)", "d": "Returnează cotangenta unui unghi", "ad": "este unghiul în radiani pentru care se calculează cotangenta"}, "COTH": {"a": "(număr)", "d": "Returnează cotangenta hiperbolică a unui număr", "ad": "este unghiul în radiani pentru care se calculează cotangenta hiperbolică"}, "CSC": {"a": "(număr)", "d": "Returnează cosecanta unui unghi", "ad": "este unghiul în radiani pentru care se calculează cosecanta"}, "CSCH": {"a": "(număr)", "d": "Returnează cosecanta hiperbolică a unui unghi", "ad": "este unghiul în radiani pentru care se calculează cosecanta hiperbolică"}, "DECIMAL": {"a": "(număr; bază)", "d": "Efectuează conversia unei reprezentări text a unui număr într-o bază dată în număr cu zecimale", "ad": "este numărul căruia îi efectuați conversia!este baza numărului căruia îi efectuați conversia"}, "DEGREES": {"a": "(unghi)", "d": "Transformă radiani în grade", "ad": "este unghiul de transformat exprimat în radiani"}, "ECMA.CEILING": {"a": "(număr; semnificație)", "d": "Rotunjește prin adaos un număr, la cel mai apropiat multiplu de argument de precizie", "ad": "este valoarea de rotunjit!este multiplul la care se face rotunjirea"}, "EVEN": {"a": "(număr)", "d": "Rotunjește prin adaos un număr pozitiv și prin lipsă un număr negativ până la cel mai apropiat întreg par", "ad": "este valoarea de rotunjit"}, "EXP": {"a": "(număr)", "d": "Returnează e ridicat la o putere dată", "ad": "este exponentul aplicat bazei e. Constanta e este egală cu 2,71828182845904, baza logaritmului natural"}, "FACT": {"a": "(număr)", "d": "Returnează produsul factorial al unui număr, egal cu 1*2*3*...* număr", "ad": "este numărul nenegativ pentru care se calculează produsul factorial"}, "FACTDOUBLE": {"a": "(număr)", "d": "Returnează factorialul dublu al unui număr", "ad": "este valoarea pentru care se returnează factorialul dublu"}, "FLOOR": {"a": "(număr; semnificație)", "d": "Rotunjește prin lipsă un număr, la cel mai apropiat multiplu de semnificație", "ad": "este valoarea numerică de rotunjit!este multiplul la care se rotunjește. număr și semnif sunt ambele fie pozitive fie negative"}, "FLOOR.PRECISE": {"a": "(număr; [semnificație])", "d": "Returnează un număr care este rotunjit prin lipsă la cel mai apropiat întreg sau la cel mai apropiat multiplu al semnificației", "ad": "este valoarea de rotunjit!este multiplul la care se face rotunjirea"}, "FLOOR.MATH": {"a": "(număr; [semnificație]; [mod])", "d": "Rotunjește prin scădere un număr, la cel mai apropiat întreg sau la cel mai apropiat multiplu de semnificație", "ad": "este valoarea de rotunjit!este multiplul la care se face rotunjirea!atunci când este dat și non-zero, această funcție se va rotunji spre zero"}, "GCD": {"a": "(număr1; [număr2]; ...)", "d": "Returnează cel mai mare divizor comun", "ad": "sunt de la 1 la 255 valori"}, "INT": {"a": "(număr)", "d": "Rotunjește un număr prin lipsă la cel mai apropiat întreg", "ad": "este numărul de rotunjit prin lipsă la un întreg"}, "ISO.CEILING": {"a": "(număr; [semnif])", "d": "Returnează un număr care este rotunjit la cel mai apropiat întreg sau la cel mai apropiat multiplu semnificativ. Indiferent de semnul numărului, numărul este rotunjit. Însă, dacă numărul sau semnificația este zero, se returnează zero.", "ad": "este valoarea de rotunjit!este multiplul la care se face rotunjirea"}, "LCM": {"a": "(număr1; [număr2]; ...)", "d": "Returnează ultimul multiplu comun", "ad": "sunt de la 1 la 255 valori pentru care doriți ultimul multiplu comun"}, "LN": {"a": "(număr)", "d": "Returnează logaritmul natural al unui număr", "ad": "este numărul real pozitiv pentru care se calculează logaritmul natural"}, "LOG": {"a": "(număr; [baza])", "d": "Returnează logaritmul unui număr în baza specificată", "ad": "este numărul real pozitiv pentru care se calculează logaritmul!este baza logaritmului; 10 dacă este omisă"}, "LOG10": {"a": "(număr)", "d": "Returnează logaritmul în baza 10 a unui număr", "ad": "este numărul real pozitiv pentru care se calculează logaritmul în baza 10"}, "MDETERM": {"a": "(matrice)", "d": "Returnează determinantul matriceal al matricei", "ad": "este o matrice numerică cu un număr egal de rânduri și colo<PERSON>, fie o zonă de celule sau o constantă matrice"}, "MINVERSE": {"a": "(matrice)", "d": "Returnează inversa matricei pentru matricea stocată într-o zonă", "ad": "este o matrice numerică cu un număr egal de rânduri și de coloane, fie o zonă de celule sau o constantă matrice"}, "MMULT": {"a": "(matrice1; matrice2)", "d": "Returnează matricea produs a două matrice, o matrice cu același număr de rânduri ca matrice1 și același număr de coloane ca matrice2", "ad": "este prima matrice de numere de înmulțit și numărul său de coloane trebuie să fie egal cu numărul de rânduri al Matrice2"}, "MOD": {"a": "(num<PERSON>r; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "d": "Returnează restul după ce un număr este împărțit la un împărțitor", "ad": "este numărul pentru care se calculează restul împărțirii!este împărțitorul argumentului număr"}, "MROUND": {"a": "(număr; multiplu)", "d": "Returnează un număr care este rotunjit la valoarea dorită", "ad": "este valoarea de rotunjit!este multiplul la care se rotunjește numărul"}, "MULTINOMIAL": {"a": "(număr1; [număr2]; ...)", "d": "Returnează setul multinomial de numere", "ad": "sunt valori de la 1 la 255 pentru multinomial"}, "MUNIT": {"a": "(dimensiune)", "d": "Returnează matricea de unitate pentru dimensiunea specificată", "ad": "este un număr întreg ce specifică dimensiunea matricei de unitate pe care doriți să o returnați"}, "ODD": {"a": "(număr)", "d": "Rotunjește prin adaos un număr pozitiv și prin lipsă un număr negativ la cel mai apropiat întreg impar", "ad": "este valoarea de rotunjit"}, "PI": {"a": "()", "d": "Return<PERSON><PERSON><PERSON> valoarea lui Pi, 3,14159265358979 cu precizie de 15 cifre", "ad": ""}, "POWER": {"a": "(număr; exponent)", "d": "Returnează rezultatul unui număr ridicat la o putere", "ad": "este numărul bază, orice număr real!este exponentul, la care este ridicat numărul bază"}, "PRODUCT": {"a": "(număr1; [număr2]; ...)", "d": "Înmulț<PERSON>ște toate numerele date ca argumente", "ad": "sunt de la 1 până la 255 de numere, valori logice sau reprezentări text ale numerelor de înmulțit"}, "QUOTIENT": {"a": "(num<PERSON><PERSON><PERSON>tor; numitor)", "d": "Returnează porțiunea întreagă a unei împărțiri", "ad": "este împărțitorul!este deîmpărțitul"}, "RADIANS": {"a": "(unghi)", "d": "Transformă gradele în radiani", "ad": "este unghiul de transformat exprimat în grade"}, "RAND": {"a": "()", "d": "Returnează un număr aleator mai mare sau egal cu 0 și mai mic decât 1, distribuit uniform (se modifică la recalculare)", "ad": ""}, "RANDARRAY": {"a": "([rân<PERSON><PERSON>]; [coloane]; [min]; [max]; [întreg])", "d": "Returnează o matrice de numere aleatorii", "ad": "numărul de rânduri din matricea returnată!numărul de coloane din matricea returnată!numărul minim care doriți să fie returnat!numărul maxim care doriți să fie returnat!returnează un număr întreg sau o valoare zecimală. TRUE pentru un număr întreg, FALSE pentru un număr zecimal"}, "RANDBETWEEN": {"a": "(inf; sup)", "d": "Returnează un număr aleatoriu dintre numerele specificate", "ad": "este cel mai mic număr întreg care va fi returnat de RANDBETWEEN!este cel mai mare număr întreg care va fi returnat de RANDBETWEEN"}, "ROMAN": {"a": "(număr; [formă])", "d": "Transformă un numeral arab în roman, ca text", "ad": "este numeralul arab de transformat!este numărul care precizează tipul de numeral roman dorit."}, "ROUND": {"a": "(număr; num_cifre)", "d": "Rotunjește un număr la un număr specificat de cifre", "ad": "este numărul de rotunjit!este numărul de cifre la care se face rotunjirea. num_cifre negativ rotunjește la stânga separatorului zecimal; zero la cel mai apropiat întreg"}, "ROUNDDOWN": {"a": "(număr; num_cifre)", "d": "Rotunjește un număr p<PERSON>, spre zero", "ad": "este orice număr real care se rotunjește prin lipsă!este numărul de cifre la care se face rotunjirea. num_cifre negativ rotunjește la stânga separatorului zecimal; zero sau omis, rotunjește la cel mai apropiat întreg"}, "ROUNDUP": {"a": "(număr; num_cifre)", "d": "Rotunjește un număr prin adao<PERSON>, dinspre zero", "ad": "este orice număr real care se rotunjește prin adaos!este numărul de cifre la care se face rotunjirea. num_cifre negativ rotunjește la stânga separatorului zecimal; zero sau omis, rotunjește la cel mai apropiat întreg"}, "SEC": {"a": "(număr)", "d": "Returnează secanta unui unghi", "ad": "este unghiul în radiani pentru care se calculează secanta"}, "SECH": {"a": "(număr)", "d": "Returnează secanta hiper<PERSON>ă a unui unghi", "ad": "este unghiul în radiani pentru care se calculează secanta hiperbolică"}, "SERIESSUM": {"a": "(x; n; m; coeficien<PERSON>i)", "d": "Returnează suma unor serii de puteri bazate pe formulă", "ad": "este valoarea de intrare a seriilor de puteri!este puterea inițială la care se ridică x!este pasul cu care se mărește n pentru fiecare termen din serie!este un set de coeficienți prin care fiecare putere succesivă a lui x este multiplicată"}, "SIGN": {"a": "(număr)", "d": "Returnează semnul unui număr: 1 dacă numărul este pozitiv, zero dacă numărul este zero, sau -1 dacă numărul este negativ", "ad": "este orice număr real"}, "SIN": {"a": "(număr)", "d": "Returnează sinusul unui unghi", "ad": "este unghiul în radiani pentru care se calculează sinusul. Grade * PI()/180 = radiani"}, "SINH": {"a": "(număr)", "d": "Returnează sinusul hiperbolic al unui număr", "ad": "este orice număr real"}, "SQRT": {"a": "(număr)", "d": "Returnează rădăcina pătrată a unui număr", "ad": "este numărul pentru care se calculează rădăcina pătrată"}, "SQRTPI": {"a": "(număr)", "d": "Returnează rădăcina pătrată a (numărului * Pi)", "ad": "este numărul cu care p este multiplicat"}, "SUBTOTAL": {"a": "(num_funcție; ref1; ...)", "d": "Returnează un subtotal într-o listă sau bază de date", "ad": "este numărul de la 1 la 11 care specifică funcția rezumativă pentru subtotal.!sunt de la 1 la 254 de zone sau referințe pentru care se calculează subtotalul"}, "SUM": {"a": "(număr1; [număr2]; ...)", "d": "<PERSON><PERSON><PERSON> toate numerele dintr-o zonă de celule", "ad": "sunt de la 1 la 255 de numere de însumat. Valorile logice și textul sunt ignorate în celule, inclusiv dacă sunt tastate ca argumente"}, "SUMIF": {"a": "(zonă; criterii; [zonă_sumă])", "d": "Adună celulele specificate de o condiție sau criteriu dat", "ad": "este zona de celule care se evaluează!este condiția sau criteriul de forma unui număr, expresie, sau text care definește celulele care se adună!sunt celulele actuale de însumat. Dacă este omisă, sunt utilizate celulele din zonă"}, "SUMIFS": {"a": "(zonă_sumă; zonă_criterii; criterii; ...)", "d": "Adaugă celulele specificate dintr-un set de condiții sau criterii", "ad": "sunt celulele care trebuie adunate.!este intervalul de celule de evaluat pentru o anumită stare!este condiția sau criteriul sub forma unui număr, a unei expresii sau a unui text care definește care celule se vor adăuga"}, "SUMPRODUCT": {"a": "(matrice1; [matrice2]; [matrice3]; ...)", "d": "Returnează suma produselor zonelor sau matricelor corespondente", "ad": "sunt de la 2 la 255 de matrice pentru care se înmulțesc și se adună componente. Toate matricele trebuie să aibă aceleași dimensiuni"}, "SUMSQ": {"a": "(număr1; [număr2]; ...)", "d": "Returnează suma pătratelor argumentelor. Argumentele pot fi numere, matrice, nume sau referințe la celule care conțin numere", "ad": "sunt de la 1 la 255 de numere, matrice, nume sau referințe la matrice pentru care se calculează suma pătratelor"}, "SUMX2MY2": {"a": "(matrice_x; matrice_y)", "d": "Însumează diferențele pătratelor a două zone sau matrice corespondente", "ad": "este prima zonă sau matrice de numere și este un număr sau un nume, o matrice sau o referință ce conține numere!este a doua zonă sau matrice de numere și este un număr sau un nume, o matrice sau o referință ce conține numere"}, "SUMX2PY2": {"a": "(matrice_x; matrice_y)", "d": "Returnează suma totală a sumelor pătratelor numerelor din două zone sau matrice corespondente", "ad": "este prima zonă sau matrice de numere și este un număr sau un nume, o matrice sau o referință ce conține numere!este a doua zonă sau matrice de numere și este un număr sau un nume, o matrice sau o referință ce conține numere"}, "SUMXMY2": {"a": "(matrice_x; matrice_y)", "d": "Însumează pătratele diferențelor dintre două zone sau matrice corespondente", "ad": "este prima zonă sau matrice de valori și este un număr sau un nume, o matrice sau o referință ce conține numere!este a doua zonă sau matrice de valori și este un număr sau un nume, o matrice sau o referință ce conține numere"}, "TAN": {"a": "(număr)", "d": "Returnează tangenta unui unghi", "ad": "este unghiul în radiani pentru care se calculează tangenta. Grade * PI()/180 = radiani"}, "TANH": {"a": "(număr)", "d": "Returnează tangenta hiperbolică a unui număr", "ad": "este orice număr real"}, "TRUNC": {"a": "(număr; [num_cifre])", "d": "Trunchiază un număr la un întreg eliminând partea zecimală, sau fracționară a numărului", "ad": "este numărul de trunchiat!este un număr care specifică precizia trunchierii, 0 (zero) dacă este omis"}, "ADDRESS": {"a": "(num_rând; num_coloană; [num_abs]; [a1]; [text_foaie])", "d": "Creează o referință la celulă ca text, având date numerele de rând și coloană", "ad": "este numărul rândului de utilizat în referința la celulă: Row_număr = 1 pentru rândul 1!este numărul de coloană de utilizat în referința la celulă. De exemplu, Column_număr = 4 pentru coloana D!specifică tipul de referință: absolută = 1; rând absolut/coloană relativă = 2; rând relativ/coloană absolută = 3; relativă = 4!este o valoare logică și specifică stilul de referință: stil A1 = 1 sau TRUE; stil R1C1 = 0 sau FALSE!este textul care  specifică numele foii de lucru utilizată ca referință externă"}, "CHOOSE": {"a": "(index_num; valoare1; [valoare2]; ...)", "d": "Alege o valoare sau acțiune de efectuat dintr-o listă de valori, bazată pe un număr index", "ad": "specifică argumentul valoare care se selectează. Index_num trebuie să fie între 1 și 254 sau o formulă sau o referință la un număr între 1 și 254!sunt de la 1 la 254 numere, referințe de celule, nume definite, formule, funcții sau argumente text din care selectează funcția CHOOSE"}, "COLUMN": {"a": "([referin<PERSON><PERSON>])", "d": "Returnează numărul coloanei pentru o referință", "ad": "este celula sau zona de celule contigue pentru care se calculează numărul coloanei. Dacă este omisă, se utilizează celula care conține funcția COLOANĂ"}, "COLUMNS": {"a": "(matrice)", "d": "Returnează numărul de coloane dintr-o matrice sau referință", "ad": "este o matrice sau o formulă matrice, sau o referință la o zonă de celule pentru care se calculează numărul de coloane"}, "FORMULATEXT": {"a": "(referință)", "d": "Returnează o formulă ca șir", "ad": "este o referință la o formulă"}, "HLOOKUP": {"a": "(căutare_valoare; matrice_tabel; num_index_rând; [zon<PERSON>_căutare])", "d": "Caută o valoare în rândul de sus al unei tabele sau matrice de valori și returnează valoarea în aceeași coloană dintr-un rând specificat", "ad": "este valoarea de găsit în primul rând al tabelei și este o valoare, o referință, sau un șir text!este o tabelă de text, numere, sau valori logice în care sunt căutate datele. Table_matrice este o referință la o zonă sau un nume de zonă!este numărul rândului din Table_matrice din care este întoarsă valoarea care se potrivește. Primul rând de valori din tabelă este rândul 1!este o valoare logică: pentru a găsi cea mai apropiată potrivire în rândul de sus (sortat în ordine ascendentă) = TRUE sau omis; găsirea unei potriviri exacte = FALSE"}, "HYPERLINK": {"a": "(locație_link; [nume_prietenos])", "d": "Creează o comandă rapidă sau un salt care deschide un document stocat pe hard disc, un server de rețea, sau pe Internet", "ad": "este textul care dă calea și numele de fișier pentru documentul care se deschide, o locație pe hard disc, o adresă UNC, sau o cale URL!este textul sau numărul afișat în celulă. Dacă este omis, celula afișează textul Link_location"}, "INDEX": {"a": "(matrice; num_rând; [num_coloană]!referinț<PERSON>; num_rând; [num_coloană]; [num_suprafață])", "d": "Returnează o valoare sau referința la o celulă de la intersecția unui rând și unei coloane particulare, dintr-o zonă dată", "ad": "este o zonă de celule sau o constantă matrice.!selectează rândul din Matrice sau din Referință din care returnează o valoare. Dac<PERSON> se omite, Num_coloană este obligatoriu!selectează coloana din Matrice sau din Referință din care returnează o valoare. Dac<PERSON> se omite, Num_rând este obligatoriu!este o referință la una sau mai multe zone de celule!selectează rândul din Matrice sau din Referință din care returnează o valoare. Dac<PERSON> se omite, Num_coloană este obligatoriu!selectează coloana din Matrice sau din Referință din care returnează o valoare. Dac<PERSON> se omite, Num_rând este obligatoriu!selectează o zonă din Referință din care returnează o valoare. Prima suprafață selectată sau introdusă este suprafața 1, a doua este suprafața 2 și așa mai departe"}, "INDIRECT": {"a": "(text_ref; [a1])", "d": "Returnează referința specificată de un șir text", "ad": "este o referință la o celulă care conține un stil de referință A1 sau R1C1, un nume definit ca o referință, sau o referință la o celulă ca un șir text!este o valoare logică și specifică tipul de referință din Ref_text: stil R1C1= FALSE; stil A1 = TRUE sau omis"}, "LOOKUP": {"a": "(căutare_valoare; căutare_vector; [rezultat_vector]!căutare_valoare; matrice)", "d": "Caută o valoare fie într-o zonă de un rând sau de o coloană, fie într-o matrice. Este furnizată pentru compatibilitate cu versiunile anterioare", "ad": "este o valoare pe care funcția LOOKUP o caută în Lookup_vector și este un număr, text, o valoare logică, un nume sau o referință la o valoare!este o zonă care conține numai un rând sau o coloană de text, numere sau valori logice, plasate în ordine ascendentă!este o zonă care conține numai un rând sau o coloană, de aceeași dimensiune cu Lookup_vector!este o valoare pe care funcția LOOKUP o caută în matrice și este un număr, text, valoare logică, un nume sau o referință la o valoare!este o zonă de celule care conțin text, numere sau valori logice care se compară cu Lookup_valoare"}, "MATCH": {"a": "(căutare_valoare; căutare_matrice; [tip_ret])", "d": "Returnează poziția relativă a unui element dintr-o matrice care corespunde unei valori precizate într-o ordine precizată", "ad": "este valoarea utilizată pentru a găsi valoarea din matrice, un număr, text, valoare logică sau o referință la una din acestea!este o zonă contiguă de celule care conțin posibile valori de căutat, o matrice de valori sau o referință la o matrice!este un număr 1, 0 sau -1 indicând care valoare se returnează."}, "OFFSET": {"a": "(refer<PERSON><PERSON><PERSON>; rânduri; col; [înalt]; [lat])", "d": "Returnează o referință la o zonă care este un număr precizat de rânduri și coloane dintr-o referință dată", "ad": "este referința pe care se bazează decalajul, o referință la o celulă sau zonă de celule adiacente!este numărul de rânduri, în jos sau în sus, la care va face referire celula din stânga sus a rezultatului!este numărul de coloane, la stânga sau la dreapta, la care va face referire celula din stânga sus a rezultatului!este înălțimea, în număr de rânduri, a rezultatului, aceeași înălțime cu referință dacă se omite!este lățimea, în număr de coloane, a rezultatului, aceeași lățime cu referință dacă se omite"}, "ROW": {"a": "([referin<PERSON><PERSON>])", "d": "Returnează numărul rândului pentru o referință", "ad": "este celula sau o singură zonă de celule pentru care se calculează numărul rândului; dacă este omisă, returnează celula care conține funcția ROW"}, "ROWS": {"a": "(matrice)", "d": "Returnează numărul de rânduri dintr-o referință sau matrice", "ad": "este o matrice, o formulă matrice, sau o referință la o zonă de celule pentru care se calculează numărul de rânduri"}, "TRANSPOSE": {"a": "(matrice)", "d": "Transformă o zonă verticală de celule în zonă orizontală sau viceversa", "ad": "este o zonă de celule dintr-o foaie de lucru sau o matrice de valori, care se vor transpune"}, "UNIQUE": {"a": "(matrice; [dup<PERSON>_coloană]; [exact_o_dată])", "d": "Returnează valorile unice dintr-o zonă sau dintr-o matrice.", "ad": "intervalul sau matricea din care se returnează rânduri sau coloane unice!este o valoare logică: compară rândurile unul cu celălalt și returnează rândurile unice = FALSE sau omise; compară coloanele una cu cealaltă și returnează coloanele unice = TRUE! este o valoare logică: returnează rândurile sau coloanele care apar exact o singură dată din matrice = TRUE; returnează toate rândurile sau coloanele distincte din matrice = FALSE sau omise"}, "VLOOKUP": {"a": "(căutare_valoare; matrice_tabel; num_index_col; [zon<PERSON>_căutare])", "d": "Caută o valoare în coloana cea mai din stânga a unui tabel, apoi returnează o valoare în același rând dintr-o coloană precizată. Implicit, tabelul trebuie sortat în ordine ascendentă", "ad": "este valoarea de găsit în prima coloană a tabelului și este o valoare, o referință sau un șir text!este un tabel de text, numere sau valori logice, din care se preiau date. Table_matrice este o referință la o zonă sau la nume de zonă!este numărul de coloană din matrice_tabel din care se returnează valoarea corespondentă. Prima coloană de valori din tabel este coloana 1!este o valoare logică: pentru a găsi cea mai apropiată valoare în prima coloană (sortată în ordine ascendentă) = TRUE sau se omite; găsirea unei potriviri exacte = FALSE"}, "XLOOKUP": {"a": "(valoare_căutare; matrice_căutare; matrice­_returnată; [dacă_nu_se_g<PERSON><PERSON><PERSON><PERSON>]; [mod_potrivire]; [mod_căutare])", "d": "Caută într-o zonă sau într-o matrice pentru o potrivire și returnează elementul corespunzător dintr-o a doua zonă sau matrice. În mod implicit, se utilizează o potrivire exactă", "ad": "este valoarea după care să căutați!este matricea sau zona de căutat!este matricea sau zona de returnat!returnat dacă nu s-a găsit nicio potrivire!specificați cum se potrivește valoare_căutare cu valorile din matrice_căutare!specificați modul de căutare de utilizat. În mod implicit, se va utiliza o căutare de tip de la primele la ultimele"}, "CELL": {"a": "(tip_info; [referin<PERSON><PERSON>])", "d": "Returnează informații despre formatare, locație și conținutul unei celule", "ad": "este o valoare text care specifică tipul de informații de celulă de returnat!celula pentru care doriți informații"}, "ERROR.TYPE": {"a": "(val_er)", "d": "Returnează un număr care corespunde unei valori de eroare.", "ad": "este valoarea de eroare pentru care se cere numărul de identificare și este o valoare de eroare sau o referință la o celulă care conține o valoare de eroare"}, "ISBLANK": {"a": "(valoare)", "d": "Verifică dacă o referință este către o celulă goală și returnează TRUE sau FALSE", "ad": "este celula sau numele care fac referire la celula de testat"}, "ISERR": {"a": "(valoare)", "d": "Verifică dacă o valoare este o altă eroare decât #N/A și returnează TRUE sau FALSE", "ad": "este valoarea de testat. Valoarea poate face referire la o celulă, o formulă sau un nume care face referire la o celulă, o formulă sau o valoare"}, "ISERROR": {"a": "(valoare)", "d": "Verifică dacă o valoare este o eroare și returnează TRUE sau FALSE", "ad": "este valoarea de testat. Valoarea poate face referire la o celulă, o formulă sau un nume care face referire la o celulă, o formulă sau o valoare"}, "ISEVEN": {"a": "(număr)", "d": "Returnează TRUE dacă numărul este par", "ad": "este valoarea de testat"}, "ISFORMULA": {"a": "(referință)", "d": "Verifică dacă o referință este la o celulă ce conține o formulă și returnează TRUE sau FALSE", "ad": "este o referință la celula pe care doriți să o testați. Referința poate fi o referință la celulă, o formulă sau un nume care se referă la o celulă"}, "ISLOGICAL": {"a": "(valoare)", "d": "Verifică dacă o valoare este logică (TRUE sau FALSE) și returnează TRUE sau FALSE", "ad": "este valoarea de testat. Valoare face referire la o celulă, o formulă sau un nume care fac referire la o celulă, formulă sau valoare"}, "ISNA": {"a": "(valoare)", "d": "Verifică dacă o valoare este #N/A și returnează TRUE sau FALSE", "ad": "este valoarea de testat. Valoarea poate face referire la o celulă, formulă sau nume care fac referire la o celulă, formulă sau valoare"}, "ISNONTEXT": {"a": "(valoare)", "d": "Verifică dacă o valoare este text (celulele necompletate nu conțin text) și returnează TRUE sau FALSE", "ad": "este valoarea de testat: o celulă, o formulă sau un nume care fac referire la o celulă, formulă sau valoare"}, "ISNUMBER": {"a": "(valoare)", "d": "Verifică dacă o valoare este număr și returnează TRUE sau FALSE", "ad": "este valoarea de testat. Valoare poate face referire la o celulă, o formulă sau un nume care fac referire la o celulă, formulă sau valoare"}, "ISODD": {"a": "(număr)", "d": "Returnează TRUE dacă numărul este impar", "ad": "este valoarea de testat"}, "ISREF": {"a": "(valoare)", "d": "Verifică dacă o valoare este o referință și returnează TRUE sau FALSE", "ad": "este valoarea de testat. Valoare poate face referire la o celulă, o formulă sau un nume care fac referire la o celulă, formulă sau valoare"}, "ISTEXT": {"a": "(valoare)", "d": "Verifică dacă o valoare este text și returnează TRUE sau FALSE", "ad": "este valoarea de testat. Valoare poate face referire la o celulă, o formulă sau un nume care fac referire la o celulă, formulă sau valoare"}, "N": {"a": "(valoare)", "d": "Transformă o valoare nenumerică într-un număr, date sau numere seriale, TRUE în 1, orice altceva în 0 (zero)", "ad": "este valoarea de transformat"}, "NA": {"a": "()", "d": "Returnează valoarea de eroare #N/A (valoare indisponibilă)", "ad": ""}, "SHEET": {"a": "([valoare])", "d": "Returnează numărul de foaie al foii menționate", "ad": "este numele unei foi sau al unei referințe al cărei număr de foaie îl doriți. Dacă este omis, se returnează numărul de foaie ce conține funcția"}, "SHEETS": {"a": "([referin<PERSON><PERSON>])", "d": "Returnează numărul de foi dintr-o referință", "ad": "este o referință pentru care doriți să aflați numărul de foi. Dacă este omis, se returnează numărul de foi din registrul de lucru"}, "TYPE": {"a": "(valoare)", "d": "Returnează un întreg care reprezintă tipul de dată al unei valori: număr = 1; text = 2; valoare logică = 4; valoare de eroare = 16; matrice = 64; date compuse = 128", "ad": "poate să ia orice valoare"}, "AND": {"a": "(logic1; [logic2]; ...)", "d": "Verifică dacă toate argumentele sunt TRUE și întoarce TRUE dacă toate argumentele sunt TRUE", "ad": "sunt de la 1 la 255 de condiții de testat care pot fi TRUE sau FALSE și pot fi valori logice, matrice sau referințe"}, "FALSE": {"a": "()", "d": "Returnează valoarea logică FALSE", "ad": ""}, "IF": {"a": "(test_logic; [valoare_dac<PERSON>_adevărat]; [valoare_dacă_fals])", "d": "Verifică dacă este îndeplinită o condiție și returnează o valoare dacă aceasta este TRUE și altă valoare dacă este FALSE", "ad": "este orice valoare sau expresie care se poate evalua ca TRUE sau FALSE!este valoarea returnată dacă test_logic este TRUE. Dacă este omisă, se returnează TRUE. Se pot imbrica până la șapte funcții IF!este valoarea returnată dacă test_logic este FALSE. Dacă este omisă, se returnează FALSE"}, "IFS": {"a": "(test_logic; valoare_dacă_adevărat; ...)", "d": "Verifică dacă sunt îndeplinite una sau mai multe condiții și returnează o valoare care corespunde primei condiții TRUE", "ad": "este orice valoare sau expresie care se poate evalua ca TRUE sau FALSE!este valoarea returnată dacă test_logic este TRUE"}, "IFERROR": {"a": "(valoare; valoare_dacă_eroare)", "d": "Return<PERSON>z<PERSON> valoare_dacă_eroare dacă expresia este o eroare și valoarea expresiei în sine nu este o eroare", "ad": "este orice valoare sau expresie sau referință!este orice valoare sau expresie sau referință"}, "IFNA": {"a": "(valoare; valoare_dacă_na)", "d": "Returnează valoarea pe care o specificați dacă expresia se rezolvă la #N/A, altfel, returnează rezultatul expresiei", "ad": "este orice valoare sau expresie sau referință!este orice valoare sau expresie sau referință"}, "NOT": {"a": "(logic)", "d": "Modifică FALSE în TRUE sau TRUE în FALSE", "ad": "este o valoare sau o expresie care se evaluează la TRUE sau FALSE"}, "OR": {"a": "(logic1; [logic2]; ...)", "d": "Verifică dacă există argumente TRUE și întoarce TRUE sau FALSE. Returnează FALSE numai dacă toate argumentele sunt FALSE", "ad": "sunt de la 1 la 255 de condiții de testat care pot fi TRUE sau FALSE"}, "SWITCH": {"a": "(expresie; valoare1; rezultat1; [implicit_sau_valoare2]; [rezultat2]; ...)", "d": "Evaluează o expresie în raport cu o listă de valori și returnează rezultatul corespunzător valorii primei potriviri. Dacă nu există nicio potrivire, returnează o valoare opțională implicită", "ad": "este o expresie de evaluat!este o valoare de comparat cu expresia!este un rezultat de returnat dacă valoarea corespunzătoare se potrivește cu expresia"}, "TRUE": {"a": "()", "d": "Returnează valoarea logică TRUE", "ad": ""}, "XOR": {"a": "(logic1; [logic2]; ...)", "d": "Returnează un „Sau exclusiv” logic al tuturor argumentelor", "ad": "reprezintă 1 - 254 condiții pe care doriți să le testați care pot fi evaluate cu ADEVĂRAT sau FALS și pot fi valori logice, matrice sau referințe"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " Returnează textul care este înainte de caracterele de delimitare.", "ad": "Textul pe care doriți să-l căutați pentru delimitator.!Caracterul sau șirul de utilizat ca delimitator.!Apariția dorită a delimitatorului. Valoarea implicită este 1. Un număr negativ caută de la sfârșit.!Caută textul pentru o potrivire de delimitator. În mod implicit, se efectuează o potrivire sensibilă la litere mari și mici.!Dacă să fie potrivit delimitatorul cu sfârșitul textului. În mod implicit, acestea nu sunt potrivite.!Se returnează dacă nu se găsește nicio potrivire. În mod implicit, se returnează #N/A."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": " Returnează textul care este după caracterele de delimitare.", "ad": "Textul pe care doriți să îl căutați pentru delimitator.!Caracterul sau șirul de utilizat ca delimitator.!Apariția dorită a delimitatorului. Valoarea implicită este 1. Un număr negativ caută de la sfârșit.!Caută în text o potrivire de delimitator. În mod implicit, se efectuează o potrivire sensibilă la litere mari și mici.!Dacă se potrivește delimitatorul cu sfârșitul textului. În mod implicit, acestea nu sunt potrivite.!Se returnează dacă nu se găsește nicio potrivire. În mod implicit, se returnează #N/A."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Scindează textul în rânduri sau coloane utilizând delimitatori.", "ad": " Textul de scindat!Caracter sau șir după care se scindează coloanele.!Caracter sau șir după care se scindează rândurile.! Dacă să se ignore celulele goale. Valoarea implicită este FALSE.!Caută în text o potrivire de delimitator. În mod implicit, se efectuează o potrivire sensibilă la litere mari și mici.! Valoarea de utilizat pentru spațiere. În mod implicit, se utilizează #N/A."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": " Încadrează un vector de rând sau de coloană după un număr specificat de valori.", "ad": " Vectorul sau referința de încadrat.! Numărul maxim de valori pe rând.! Valoarea cu care se face maparea. Valoarea implicită este #N/A."}, "VSTACK": {"a": "(matrice1, [matrice2], ...)", "d": " Stivuiește pe verticală matricele într-o singură matrice.", "ad": " O matrice sau o referință de stivuit."}, "HSTACK": {"a": "(matrice1, [matrice2], ...)", "d": " Stivuiește pe orizontală matricele într-o singură matrice.", "ad": " O matrice sau o referință de stivuit."}, "CHOOSEROWS": {"a": "(matrice, row_num1, [row_num2], ...)", "d": " Returnează rânduri dintr-o matrice sau referință.", "ad": " Matricea sau referința care conține rândurile de returnat.! Numărul rândului de returnat."}, "CHOOSECOLS": {"a": "(matrice, col_num1, [col_num2], ...)", "d": " Returnează coloane dintr-o matrice sau referință.", "ad": " Matricea sau referința care conține coloanele de returnat.! Numărul coloanei de returnat."}, "TOCOL": {"a": "(matrice, [ignorare], [scan_by_column])", "d": " Returnează matricea ca o singură coloană.", "ad": " Matricea sau referința de returnat ca o coloană.! Dacă să se ignore anumite tipuri de valori. În mod implicit, nicio valoare nu este ignorată.! Scanați matricea după coloană. În mod implicit, matricea este scanată după rând."}, "TOROW": {"a": "(matrice, [ignorare], [scanare_dup<PERSON>_coloană])", "d": " Returnează matricea ca un singur rând.", "ad": "Matricea sau referința de returnat ca rând.!Dacă să se ignore anumite tipuri de valori. În mod implicit, nicio valoare nu este ignorată.!Scanează matricea după coloană. În mod implicit, matricea este scanată după rând."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": " Încadrează un vector de rând sau de coloană după un număr specificat de valori.", "ad": " Vectorul sau referința de încadrat.! Numărul maxim de valori pe coloană.! Valoarea cu care se face maparea. Valoarea implicită este #N/A."}, "TAKE": {"a": "(matrice, r<PERSON><PERSON><PERSON>, [coloane])", "d": " Returnează rânduri sau coloane de la începutul sau sfârșitul matricei.", "ad": " Matricea din care se preiau rânduri sau coloane.! Numărul de rânduri de preluat. O valoare negativă preia de la sfârșitul matricei.! Numărul de coloane de preluat. O valoare negativă preia de la sfârșitul matricei."}, "DROP": {"a": "(matrice, r<PERSON><PERSON><PERSON>, [coloane])", "d": " Elimină rânduri sau coloane de la începutul sau sfârșitul matricei.", "ad": " Matricea din care se elimină rânduri sau coloane.! Numărul de rânduri de eliminat. O valoare negativă se elimină de la sfârșitul matricei.! Numărul de coloane de eliminat. O valoare negativă se elimină de la sfârșitul matricei."}, "SEQUENCE": {"a": "(r<PERSON><PERSON><PERSON>, [coloane], [start], [pas])", "d": "Returnează o secvență de numere", "ad": "numărul de rânduri de returnat!numărul de coloane de returnat!primul număr din secvență!suma pentru a incrementa fiecare valoare ulterioară din secvență"}, "EXPAND": {"a": "(matrice, r<PERSON><PERSON><PERSON>, [coloane], [pad_with])", "d": "Extinde o matrice la dimensiunile specificate.", "ad": " Matricea de extins.! Numă<PERSON>l de rânduri din matricea extinsă. <PERSON><PERSON><PERSON> lips<PERSON>, rândurile nu vor fi extinse.! Numărul de coloane din matricea extinsă. <PERSON><PERSON><PERSON> lipses<PERSON>, coloanele nu vor fi extinse.! Valoarea cu care se face completarea. Valoarea implicită este #N/A."}, "XMATCH": {"a": "(valoare_căutare, matrice_căutare, [mod_potrivire], [mod_căutare])", "d": "Returnează poziția relativă a unui element dintr-o matrice. În mod implicit, este necesară o potrivire exactă", "ad": "este valoarea de căutat!este matricea sau zona de căutat!specifică cum se potrivește valoare_căutare cu valorile din matrice_căutare!specifică modul de căutare de utilizat. În mod implicit, se va utiliza o căutare de tip de la primele la ultimele"}, "FILTER": {"a": "(matrice, include, [dac<PERSON>_gol])", "d": "Filtrați o zonă sau o matrice", "ad": "zona sau matricea de filtrat!o matrice de valori booleene unde TRUE reprezintă un rând sau o coloană de păstrat!returnat dacă nu este reținut niciun element"}, "ARRAYTOTEXT": {"a": "(matrice, [format])", "d": "Returnează o reprezentare text a unei matrice", "ad": " matricea care să reprezinte ca text! formatul textului"}, "SORT": {"a": "(matrice, [index_sortare], [ordine_sortare], [dup<PERSON>_coloană])", "d": "Sortează o zonă sau o matrice", "ad": "zona sau matricea de sortat!un număr care indică rândul sau coloana după care să se sorteze!un număr care indică ordinea de sortare dorită; 1 pentru ordinea crescătoare (implicit), -1 pentru ordinea descrescătoare!o valoare logică ce indică direcția de sortare dorită: FALSE pentru a sorta după rând (implicit), TRUE pentru a sorta după coloană"}, "SORTBY": {"a": "(matrice, după_matrice, [ordine_sortare], ...)", "d": "Sortează o zonă sau o matrice pe baza valorilor dintr-o zonă sau dintr-o matrice corespunzătoare", "ad": "zona sau matricea de sortat!zona sau matricea după care să se efectueze sortarea!un număr care indică ordinea de sortare dorită; 1 pentru ordine crescătoare (implicit), -1 pentru ordine descrescătoare"}, "GETPIVOTDATA": {"a": "(câmp_date; tabel_pivot; [câmp]; [element]; ...)", "d": "Extrage date stocate într-un PivotTable", "ad": "este numele câmpului de date din care se extrag date!este o referință la o celulă sau la o zonă de celule din PivotTable care conține datele de regăsit!câmpul la care se face referire!elementul de câmp la care se face referire"}, "IMPORTRANGE": {"a": "(adres<PERSON> url_foaie de calcul)", "d": "Importă o zonă de celule dintr-o foaie de calcul specificată."}}