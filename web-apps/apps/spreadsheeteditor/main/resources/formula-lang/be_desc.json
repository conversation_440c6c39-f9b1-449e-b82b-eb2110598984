{"DATE": {"a": "(год;месяц;день)", "d": "Функция даты и времени, используется для добавления дат в стандартном формате дд.ММ.гггг", "ad": "число от 1900 или 1904 (в зависимости от системы дат книги) до 9999!число от 1 до 12, соответствующее месяцу года!число от 1 до 31, соответствующее дню месяца"}, "DATEDIF": {"a": "(нач_дата;кон_дата;единица)", "d": "Функция даты и времени, возвращает разницу между двумя датами (начальной и конечной) согласно заданному интервалу (единице)", "ad": "дата, представляющая первую или начальную дату заданного периода!дата окончания периода!тип возвращаемых сведений"}, "DATEVALUE": {"a": "(дата_как_текст)", "d": "Функция даты и времени, возвращает порядковый номер заданной даты", "ad": "строка, содержащая дату в формате даты Редактора таблиц в диапазоне от 01.01.1900 или 01.01.1904 (в зависимости от системы дат книги) до 31.12.9999"}, "DAY": {"a": "(дата_в_числовом_формате)", "d": "Функция даты и времени, возвращает день (число от 1 до 31), соответствующий дате, заданной в числовом формате (дд.ММ.гггг по умолчанию)", "ad": "число в коде даты-времени, используемом в Редакторе таблиц"}, "DAYS": {"a": "(кон_дата;нач_дата)", "d": "Функция даты и времени, возвращает количество дней между двумя датами", "ad": "нач_дата и кон_дата — две даты, количество дней между которыми необходимо определить!нач_дата и кон_дата — две даты, количество дней между которыми необходимо определить"}, "DAYS360": {"a": "(нач_дата;кон_дата;[метод])", "d": "Функция даты и времени, возвращает количество дней между двумя датами (начальной и конечной) на основе 360-дневного года с использованием одного из методов вычислений (американского или европейского)", "ad": "'нач_дата' и 'кон_дата' - это даты, количество дней между которыми требуется определить!'нач_дата' и 'кон_дата' - это даты, количество дней между которыми требуется определить!логическое значение, определяющее используемый в вычислениях метод: европейский (ИСТИНА) или американский (ЛОЖЬ или отсутствие значения)."}, "EDATE": {"a": "(нач_дата;число_месяцев)", "d": "Функция даты и времени, возвращает порядковый номер даты, которая идет на заданное число месяцев (число_месяцев) до или после заданной даты (нач_дата)", "ad": "порядковый номер начальной даты!количество месяцев до или после начальной даты"}, "EOMONTH": {"a": "(нач_дата;число_месяцев)", "d": "Функция даты и времени, возвращает порядковый номер последнего дня месяца, который идет на заданное число месяцев до или после заданной начальной даты", "ad": "порядковый номер начальной даты!количество месяцев до или после начальной даты"}, "HOUR": {"a": "(время_в_числовом_формате)", "d": "Функция даты и времени, возвращает количество часов (число от 0 до 23), соответствующее заданному значению времени", "ad": "число в коде даты-времени, используемом в Редакторе таблиц, или текст в формате времени, например 16:48:00"}, "ISOWEEKNUM": {"a": "(дата)", "d": "Функция даты и времени, возвращает номер недели в году для определенной даты в соответствии со стандартами ISO", "ad": "код даты-времени, используемый в Редакторе таблиц для вычислений со значениями этого типа"}, "MINUTE": {"a": "(время_в_числовом_формате)", "d": "Функция даты и времени, возвращает количество минут (число от 0 до 59), соответствующее заданному значению времени", "ad": "число в коде даты-времени, используемом в Редакторе таблиц, или текст в формате времени, например 16:48:00"}, "MONTH": {"a": "(дата_в_числовом_формате)", "d": "Функция даты и времени, возвращает месяц (число от 1 до 12), соответствующий дате, заданной в числовом формате (дд.ММ.гггг по умолчанию)", "ad": "число в коде даты-времени, используемом в Редакторе таблиц"}, "NETWORKDAYS": {"a": "(нач_дата;кон_дата;[праздники])", "d": "Функция даты и времени, возвращает количество рабочих дней между двумя датами (начальной и конечной). Выходные и праздничные дни в это число не включаются", "ad": "порядковый номер начальной даты!порядковый номер конечной даты!необязательный список из одной или нескольких заданных порядковыми номерами дат для исключения из рабочего календаря, таких как государственные праздники"}, "NETWORKDAYS.INTL": {"a": "(нач_дата;кон_дата;[выходной];[праздники])", "d": "Функция даты и времени, возвращает количество рабочих дней между двумя датами с использованием параметров, определяющих, сколько в неделе выходных и какие дни являются выходными", "ad": "порядковый номер начальной даты!порядковый номер конечной даты!число или строка, указывающая на выходные дни!необязательный список из одной или нескольких заданных порядковыми номерами дат для исключения из рабочего календаря, таких как государственные праздники"}, "NOW": {"a": "()", "d": "Функция даты и времени, возвращает текущую дату и время в числовом формате; если до ввода этой функции для ячейки был задан формат Общий, он будет изменен на формат даты и времени, соответствующий региональным параметрам", "ad": ""}, "SECOND": {"a": "(время_в_числовом_формате)", "d": "Функция даты и времени, возвращает количество секунд (число от 0 до 59), соответствующее заданному значению времени", "ad": "число в коде даты-времени, используемом в Редакторе таблиц, или текст в формате времени, например 16:48:23"}, "TIME": {"a": "(часы;минуты;секунды)", "d": "Функция даты и времени, используется для добавления определенного времени в выбранном формате (по умолчанию чч:мм tt (указатель половины дня a.m./p.m.))", "ad": "число от 0 до 23, представляющее час!число от 0 до 59, представляющее минуту!число от 0 до 59, представляющее секунду"}, "TIMEVALUE": {"a": "(время_как_текст)", "d": "Функция даты и времени, возвращает порядковый номер, соответствующий заданному времени", "ad": "строка текста, содержащая время в любом из форматов времени Редактора таблиц (сведения о дате игнорируются)"}, "TODAY": {"a": "()", "d": "Функция даты и времени, используется для добавления текущей даты в следующем формате: дд.ММ.гггг. Данная функция не требует аргумента", "ad": ""}, "WEEKDAY": {"a": "(дата_в_числовом_формате;[тип])", "d": "Функция даты и времени, определяет, какой день недели соответствует заданной дате", "ad": "число, представляющее дату!числ<PERSON> (1,2 или 3), определяющее тип отсчета недели (с Вс=1 до Сб=7; с Пн=1 до Вс=7 или с Пн=0 до Вс=6 соответственно)"}, "WEEKNUM": {"a": "(дата_в_числовом_формате;[тип])", "d": "Функция даты и времени, возвращает порядковый номер той недели в течение года, на которую приходится заданная дата", "ad": "код, используемый Редактором таблиц для расчета даты и времени!число (1 или 2), определяющее тип возвращаемого значения"}, "WORKDAY": {"a": "(нач_дата;количество_дней;[праздники])", "d": "Функция даты и времени, возвращает дату, которая идет на заданное число дней (количество_дней) до или после заданной начальной даты, без учета выходных и праздничных дней", "ad": "начальная дата, заданная порядковым номером!количество не выходных и не праздничных дней до или после начальной даты!необязательный список из одной или нескольких заданных порядковыми номерами дат для исключения из рабочего календаря, таких как государственные праздники"}, "WORKDAY.INTL": {"a": "(нач_дата;количество_дней;[выходной];[праздники])", "d": "Функция даты и времени, возвращает порядковый номер даты, отстоящей вперед или назад на заданное количество рабочих дней, с указанием настраиваемых параметров выходных, определяющих, сколько в неделе выходных дней и какие дни являются выходными", "ad": "начальная дата, заданная порядковым номером!количество не выходных и не праздничных дней до или после начальной даты!число или строка, указывающая на выходные дни!необязательный список из одной или нескольких заданных порядковыми номерами дат для исключения из рабочего календаря, таких как государственные праздники"}, "YEAR": {"a": "(дата_в_числовом_формате)", "d": "Функция даты и времени, возвращает год (число от 1900 до 9999), соответствующий дате, заданной в числовом формате (дд.ММ.гггг по умолчанию)", "ad": "число в коде даты-времени, используемом в Редакторе таблиц"}, "YEARFRAC": {"a": "(нач_дата;кон_дата;[базис])", "d": "Функция даты и времени, возвращает долю года, представленную числом целых дней между начальной и конечной датами, вычисляемую заданным способом", "ad": "порядковый номер начальной даты!порядковый номер конечной даты!используемый способ вычисления дня"}, "BESSELI": {"a": "(X;N)", "d": "Инженерная функция, возвращает модифицированную функцию Бесселя, что эквивалентно вычислению функции Бесселя для чисто мнимого аргумента", "ad": "значение, для которого вычисляется функция!порядок функции Бесселя"}, "BESSELJ": {"a": "(X;N)", "d": "Инженерная функция, возвращает функцию Бесселя", "ad": "значение, для которого вычисляется функция!порядок функции Бесселя"}, "BESSELK": {"a": "(X;N)", "d": "Инженерная функция, возвращает модифицированную функцию Бесселя, что эквивалентно вычислению функции Бесселя для чисто мнимого аргумента", "ad": "значение, для которого вычисляется функция!порядок функции"}, "BESSELY": {"a": "(X;N)", "d": "Инженерная функция, возвращает функцию Бесселя, также называемую функцией Вебера или функцией Неймана", "ad": "значение, для которого вычисляется функция!порядок функции"}, "BIN2DEC": {"a": "(число)", "d": "Инженерная функция, преобразует двоичное число в десятичное", "ad": "двоичное число, которое требуется преобразовать"}, "BIN2HEX": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует двоичное число в шестнадцатеричное", "ad": "двоичное число, которое требуется преобразовать!количество знаков для использования"}, "BIN2OCT": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует двоичное число в восьмеричное", "ad": "двоичное число, которое требуется преобразовать!количество знаков для использования"}, "BITAND": {"a": "(число1;число2)", "d": "Инженерная функция, возвращает результат операции поразрядного И для двух чисел", "ad": "десятичное представление двоичного числа, для которого требуется выполнить вычисление!десятичное представление двоичного числа, для которого требуется выполнить вычисление"}, "BITLSHIFT": {"a": "(число;число_бит)", "d": "Инженерная функция, возвращает число со сдвигом влево на указанное число бит", "ad": "десятичное представление двоичного числа, для которого требуется выполнить вычисление!количество битов, на которое требуется сдвинуть число влево"}, "BITOR": {"a": "(число1;число2)", "d": "Инженерная функция, возвращает результат операции поразрядного ИЛИ для двух чисел", "ad": "десятичное представление двоичного числа, для которого требуется выполнить вычисление!десятичное представление двоичного числа, для которого требуется выполнить вычисление"}, "BITRSHIFT": {"a": "(число;число_бит)", "d": "Инженерная функция, возвращает число со сдвигом вправо на указанное число бит", "ad": "десятичное представление двоичного числа, для которого требуется выполнить вычисление!количество битов, на которое требуется сдвинуть число вправо"}, "BITXOR": {"a": "(число1;число2)", "d": "Инженерная функция, возвращает результат операции поразрядного исключающего ИЛИ для двух чисел", "ad": "десятичное представление двоичного числа, для которого требуется выполнить вычисление!десятичное представление двоичного числа, для которого требуется выполнить вычисление"}, "COMPLEX": {"a": "(действительная_часть;мнимая_часть;[мнимая_единица])", "d": "Инженерная функция, используется для преобразования действительной и мнимой части в комплексное число, выраженное в формате a + bi или a + bj", "ad": "действительная часть комплексного числа!мнимая часть комплексного числа!обозначение мнимой единицы в комплексном числе"}, "CONVERT": {"a": "(число;исх_ед_изм;кон_ед_изм)", "d": "Инженерная функция, преобразует число из одной системы мер в другую; например, с помощью функции ПРЕОБР можно перевести таблицу расстояний в милях в таблицу расстояний в километрах", "ad": "преобразуемое значение в исходных единицах измерения!единицы измерения для аргумента \"число\"!единицы измерения для результата"}, "DEC2BIN": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует десятичное число в двоичное", "ad": "десятичное целое число, которое требуется преобразовать!количество знаков для использования"}, "DEC2HEX": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует десятичное число в шестнадцатеричное", "ad": "десятичное целое число, которое требуется преобразовать!количество знаков для использования"}, "DEC2OCT": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует десятичное число в восьмеричное", "ad": "десятичное целое число, которое требуется преобразовать!количество знаков для использования"}, "DELTA": {"a": "(число1;[число2])", "d": "Инженерная функция, используется для проверки равенства двух чисел. Функция возвращает 1, если числа равны, в противном случае возвращает 0", "ad": "первое число!второе число"}, "ERF": {"a": "(нижний_предел;[верхний_предел])", "d": "Инженерная функция, используется для расчета значения функции ошибки, проинтегрированного в интервале от заданного нижнего до заданного верхнего предела", "ad": "нижний предел интегрирования ФОШ!верхний предел интегрирования ФОШ"}, "ERF.PRECISE": {"a": "(x)", "d": "Инженерная функция, возвращает функцию ошибки", "ad": "нижний предел интегрирования ФОШ.ТОЧН"}, "ERFC": {"a": "(нижний_предел)", "d": "Инженерная функция, используется для расчета значения дополнительной функции ошибки, проинтегрированного в интервале от заданного нижнего предела до бесконечности", "ad": "нижний предел интегрирования ФОШ"}, "ERFC.PRECISE": {"a": "(x)", "d": "Инженерная функция, возвращает дополнительную функцию ошибки, проинтегрированную в пределах от x до бесконечности", "ad": "нижний предел интегрирования ДФОШ.ТОЧН"}, "GESTEP": {"a": "(число;[порог])", "d": "Инженерная функция, используется для проверки того, превышает ли какое-то число пороговое значение. Функция возвращает 1, если число больше или равно пороговому значению, в противном случае возвращает 0", "ad": "проверяемое значение!пороговое значение"}, "HEX2BIN": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует шестнадцатеричное число в двоичное", "ad": "шестнадцатеричное число, которое требуется преобразовать!количество знаков для использования"}, "HEX2DEC": {"a": "(число)", "d": "Инженерная функция, преобразует шестнадцатеричное число в десятичное", "ad": "шестнадцатеричное число, которое требуется преобразовать"}, "HEX2OCT": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует шестнадцатеричное число в восьмеричное", "ad": "шестнадцатеричное число, которое требуется преобразовать!количество знаков для использования"}, "IMABS": {"a": "(компл_число)", "d": "Инженерная функция, возвращает абсолютное значение комплексного числа", "ad": "комплексное число, абсолютную величину которого требуется получить"}, "IMAGINARY": {"a": "(компл_число)", "d": "Инженерная функция, возвращает мнимую часть заданного комплексного числа", "ad": "комплексное число, для которого определяется коэффициент при мнимой части"}, "IMARGUMENT": {"a": "(компл_число)", "d": "Инженерная функция, возвращает значение аргумента Тета, то есть угол в радианах", "ad": "комплексное число, для которого определяется аргумент"}, "IMCONJUGATE": {"a": "(компл_число)", "d": "Инженерная функция, возвращает комплексно-сопряженное значение комплексного числа", "ad": "комплексное число, для которого определяется сопряженное комплексное число"}, "IMCOS": {"a": "(компл_число)", "d": "Инженерная функция, возвращает косинус комплексного числа, представленного в текстовом формате a + bi или a + bj", "ad": "комплексное число, для которого вычисляется косинус"}, "IMCOSH": {"a": "(компл_число)", "d": "Инженерная функция, возвращает гиперболический косинус комплексного числа в текстовом формате a + bi или a + bj", "ad": "комплексное число, для которого требуется найти гиперболический косинус"}, "IMCOT": {"a": "(компл_число)", "d": "Инженерная функция, возвращает котангенс комплексного числа в текстовом формате a + bi или a + bj", "ad": "комплексное число, для которого требуется найти котангенс"}, "IMCSC": {"a": "(компл_число)", "d": "Инженерная функция, возвращает косеканс комплексного числа в текстовом формате a + bi или a + bj", "ad": "комплексное число, для которого требуется найти косеканс"}, "IMCSCH": {"a": "(компл_число)", "d": "Инженерная функция, возвращает гиперболический косеканс комплексного числа в текстовом формате a + bi или a + bj", "ad": "комплексное число, для которого требуется найти гиперболический косеканс"}, "IMDIV": {"a": "(компл_число1;компл_число2)", "d": "Инженерная функция, возвращает частное от деления двух комплексных чисел, представленных в формате a + bi или a + bj", "ad": "комплексный числитель или делимое!комплексный знаменатель или делитель"}, "IMEXP": {"a": "(компл_число)", "d": "Инженерная функция, возвращает экспоненту комплексного числа (значение константы e, возведенной в степень, заданную комплексным числом). Константа e равна 2,71828182845904", "ad": "комплексное число, для которого вычисляется экспонента"}, "IMLN": {"a": "(компл_число)", "d": "Инженерная функция, возвращает натуральный логарифм комплексного числа", "ad": "комплексное число, для которого вычисляется натуральный логарифм"}, "IMLOG10": {"a": "(компл_число)", "d": "Инженерная функция, возвращает двоичный логарифм комплексного числа", "ad": "комплексное число, для которого вычисляется десятичный логарифм"}, "IMLOG2": {"a": "(компл_число)", "d": "Инженерная функция, возвращает десятичный логарифм комплексного числа", "ad": "комплексное число, для которого вычисляется двоичный логарифм"}, "IMPOWER": {"a": "(компл_число;число)", "d": "Инженерная функция, возвращает комплексное число, возведенное в заданную степень", "ad": "комплексное число, возводимое в степень!степень, в которую возводится комплексное число"}, "IMPRODUCT": {"a": "(список_аргументов)", "d": "Инженерная функция, возвращает произведение указанных комплексных чисел", "ad": "компл_число1, компл_число2,... — от 1 до 255 перемножаемых комплексных чисел."}, "IMREAL": {"a": "(компл_число)", "d": "Инженерная функция, возвращает действительную часть комплексного числа", "ad": "комплексное число, для которого определяется коэффициент при вещественной (действительной) части"}, "IMSEC": {"a": "(компл_число)", "d": "Инженерная функция, возвращает секанс комплексного числа в текстовом формате a + bi или a + bj", "ad": "комплексное число, для которого требуется найти секанс"}, "IMSECH": {"a": "(компл_число)", "d": "Инженерная функция, возвращает гиперболический секанс комплексного числа в текстовом формате a + bi или a + bj", "ad": "комплексное число, для которого требуется найти гиперболический секанс"}, "IMSIN": {"a": "(компл_число)", "d": "Инженерная функция, возвращает синус комплексного числа a + bi или a + bj", "ad": "комплексное число, для которого вычисляется синус"}, "IMSINH": {"a": "(компл_число)", "d": "Инженерная функция, возвращает гиперболический синус комплексного числа в текстовом формате a + bi или a + bj", "ad": "комплексное число, для которого требуется найти гиперболический синус"}, "IMSQRT": {"a": "(компл_число)", "d": "Инженерная функция, возвращает значение квадратного корня из комплексного числа", "ad": "комплексное число, для которого вычисляется квадратный корень"}, "IMSUB": {"a": "(компл_число1;компл_число2)", "d": "Инженерная функция, возвращает разность двух комплексных чисел, представленных в формате a + bi или a + bj", "ad": "уменьшаемое комплексное число!вычитаемое комплексное число"}, "IMSUM": {"a": "(список_аргументов)", "d": "Инженерная функция, возвращает сумму двух комплексных чисел, представленных в формате a + bi или a + bj", "ad": "от 1 до 255 суммируемых комплексных чисел"}, "IMTAN": {"a": "(компл_число)", "d": "Инженерная функция, тангенс комплексного числа в текстовом формате a + bi или a + bj", "ad": "комплексное число, для которого требуется найти тангенс"}, "OCT2BIN": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует восьмеричное число в двоичное", "ad": "восьмеричное число, которое требуется преобразовать!количество знаков для использования"}, "OCT2DEC": {"a": "(число)", "d": "Инженерная функция, преобразует восьмеричное число в десятичное", "ad": "восьмеричное число, которое требуется преобразовать"}, "OCT2HEX": {"a": "(число;[разрядность])", "d": "Инженерная функция, преобразует восьмеричное число в шестнадцатеричное", "ad": "восьмеричное число, которое требуется преобразовать!количество знаков для использования"}, "DAVERAGE": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, усредняет значения в поле (столбце) записей списка или базы данных, удовлетворяющие заданным условиям", "ad": "диапаз<PERSON><PERSON> ячеек, формирующих список или базу данных. База данных представляет собой список связанных данных!заголовок столбца в двойных кавычках или число, представляющее номер столбца в списке!диапазон, содержащий задаваемые условия. Диапазон включает заголовок столбца и одну ячейку с условием под заголовком"}, "DCOUNT": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, подсчитывает количество ячеек в поле (столбце) записей списка или базы данных, которые содержат числа, удовлетворяющие заданным условиям", "ad": "диапазон, содержащий базу данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовки столбцов и одну ячейку с условием."}, "DCOUNTA": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, подсчитывает непустые ячейки в поле (столбце) записей списка или базы данных, которые удовлетворяют заданным условиям", "ad": "диапазон, составляющий базу данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку  условием."}, "DGET": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, извлекает из столбца списка или базы данных одно значение, удовлетворяющее заданным условиям", "ad": "диапазон базы данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку с условием"}, "DMAX": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, возвращает наибольшее число в поле (столбце) записей списка или базы данных, которое удовлетворяет заданным условиям", "ad": "диапазон ячеек, составляющих список или базу данных. База данных представляет собой список связанных данных!заголовок столбца в двойных кавычках или число, представляющее номер столбца в списке!диапазон, содержащий задаваемые условия. Диапазон включает заголовок столбца и одну ячейку с условием под заголовком"}, "DMIN": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, возвращает наименьшее число в поле (столбце) записей списка или базы данных, которое удовлетворяет заданным условиям", "ad": "диапазон базы данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку с условием"}, "DPRODUCT": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, перемножает значения в поле (столбце) записей списка или базы данных, которые удовлетворяют заданным условиям", "ad": "диапазон базы данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку с условием"}, "DSTDEV": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, оценивает стандартное отклонение на основе выборки из генеральной совокупности, используя числа в поле (столбце) записей списка или базы данных, которые удовлетворяют заданным условиям", "ad": "диапазон базы данных . База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку с условием"}, "DSTDEVP": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, вычисляет стандартное отклонение генеральной совокупности, используя числа в поле (столбце) записей списка или базы данных, которые удовлетворяют заданным условиям", "ad": "диапазон базы данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку с условием"}, "DSUM": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, cуммирует числа в поле (столбце) записей списка или базы данных, которые удовлетворяют заданным условиям", "ad": "диапазон базы данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку с условием"}, "DVAR": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, оценивает дисперсию генеральной совокупности по выборке, используя отвечающие соответствующие заданным условиям числа в поле (столбце) записей списка или базы данных", "ad": "диапазон базы данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку с условием"}, "DVARP": {"a": "(база_данных;поле;условия)", "d": "Функция базы данных, вычисляет дисперсию генеральной совокупности, используя числа в поле (столбце) записей списка или базы данных, которые удовлетворяют заданным условиям", "ad": "диапазон базы данных. База данных представляет собой набор связанных данных!заголовок столбца в кавычках или номер столбца!диапазон, содержащий условие базы данных. Диапазон включает заголовок столбца и одну ячейку с условием"}, "CHAR": {"a": "(число)", "d": "Функция для работы с текстом и данными, возвращает символ ASCII, соответствующий заданному числовому коду", "ad": "число в интервале от 1 до 255, указывающее нужный символ"}, "CLEAN": {"a": "(текст)", "d": "Функция для работы с текстом и данными, используется для удаления всех непечатаемых символов из выбранной строки", "ad": "любая информация на рабочем листе, из которой удаляются непечатаемые знаки"}, "CODE": {"a": "(текст)", "d": "Функция для работы с текстом и данными, возвращает числовой код ASCII, соответствующий заданному символу или первому символу в ячейке", "ad": "текст, в котором требуется узнать код первого символа"}, "CONCATENATE": {"a": "(текст1;текст2; ...)", "d": "Функция для работы с текстом и данными, используется для объединения данных из двух или более ячеек в одну", "ad": "от 1 до 255 текстовых строк, которые следует объединить в одну строку; могут быть строками, числами или ссылками на отдельные ячейки"}, "CONCAT": {"a": "(текст1;текст2; ...)", "d": "Функция для работы с текстом и данными, используется для объединения данных из двух или более ячеек в одну. Эта функция заменяет функцию СЦЕПИТЬ", "ad": "От 1 до 254 текстовых строк или диапазонов можно объединить в одну строку"}, "DOLLAR": {"a": "(число;[число_знаков])", "d": "Функция для работы с текстом и данными, преобразует число в текст, используя денежный формат $#.##", "ad": "число либо ссылка на ячейку, содержащую число, либо формула, вычисление которой дает число!число цифр справа от десятичной запятой. При необходимости округляется; если опущено, число знаков после запятой равно 2"}, "EXACT": {"a": "(текст1;текст2)", "d": "Функция для работы с текстом и данными, используется для сравнения данных в двух ячейках. Функция возвращает значение ИСТИНА, если данные совпадают, и ЛОЖЬ, если нет", "ad": "первая текстовая строка!вторая текстовая строка"}, "FIND": {"a": "(искомый_текст;просматриваемый_текст;[нач_позиция])", "d": "Функция для работы с текстом и данными, используется для поиска заданной подстроки (искомый_текст) внутри строки (просматриваемый_текст), предназначена для языков, использующих однобайтовую кодировку (SBCS)", "ad": "строка, которую требуется найти. Для поиска первого знака укажите пустую строку (две двойных кавычки); использование знаков подстановки не допускается!строка, содержащая искомый текст!позиция, с которой нужно начать поиск. Первый знак в параметре 'просматриваемый_текст' имеет позицию номер 1. Если значение не указано, начальная позиция принимается равной 1"}, "FINDB": {"a": "(искомый_текст;просматриваемый_текст;[нач_позиция])", "d": "Функция для работы с текстом и данными, используется для поиска заданной подстроки (искомый_текст) внутри строки (просматриваемый_текст), предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.", "ad": "строка, которую требуется найти. Для поиска первого знака укажите пустую строку (две двойных кавычки); использование знаков подстановки не допускается!строка, содержащая искомый текст!позиция, с которой нужно начать поиск. Первый знак в параметре 'просматриваемый_текст' имеет позицию номер 1. Если значение не указано, начальная позиция принимается равной 1"}, "FIXED": {"a": "(число;[чис<PERSON><PERSON>_знаков];[без_разделителей])", "d": "Функция для работы с текстом и данными, возвращает текстовое представление числа, округленного до заданного количества десятичных знаков", "ad": "число, которое округляется и преобразуется в текст!число цифр справа от десятичной запятой. По умолчанию принимается равным 2!логическое значение, определяющее, должны (ИСТИНА) или не должны (ЛОЖЬ) разделители разрядов присутствовать в результате"}, "LEFT": {"a": "(текст;[число_знаков])", "d": "Функция для работы с текстом и данными, извлекает подстроку из заданной строки, начиная с левого символа, предназначена для языков, использующих однобайтовую кодировку (SBCS)", "ad": "строка текста, содержащая знаки, которые нужно извлечь!количество знаков, которое нужно извлечь; если не указано, принимается равным 1"}, "LEFTB": {"a": "(текст;[число_знаков])", "d": "Функция для работы с текстом и данными, извлекает подстроку из заданной строки, начиная с левого символа, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.", "ad": "строка текста, содержащая знаки, которые нужно извлечь!количество знаков, которое нужно извлечь; если не указано, принимается равным 1"}, "LEN": {"a": "(текст)", "d": "Функция для работы с текстом и данными, анализирует заданную строку и возвращает количество символов, которые она содержит, предназначена для языков, использующих однобайтовую кодировку (SBCS)", "ad": "строка, длину которой следует определить. Пробелы считаются знаками"}, "LENB": {"a": "(текст)", "d": "Функция для работы с текстом и данными, анализирует заданную строку и возвращает количество символов, которые она содержит, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.", "ad": "строка, длину которой следует определить. Пробелы считаются знаками"}, "LOWER": {"a": "(текст)", "d": "Функция для работы с текстом и данными, используется для преобразования букв в выбранной ячейке из верхнего регистра в нижний", "ad": "строка, буквы которой требуется преобразовать в строчные. Знаки, не являющиеся буквами, не изменяются"}, "MID": {"a": "(текст;начальная_позиция;число_знаков)", "d": "Функция для работы с текстом и данными, извлекает символы из заданной строки, начиная с любого места, предназначена для языков, использующих однобайтовую кодировку (SBCS)", "ad": "текстовая строка, из которой следует извлечь знаки!позиция, начиная с которой следует извлечь знаки. Первый знак в тексте имеет позицию 1!количество знаков, которое следует извлечь из текста"}, "MIDB": {"a": "(текст;начальная_позиция;число_знаков)", "d": "Функция для работы с текстом и данными, извлекает символы из заданной строки, начиная с любого места, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.", "ad": "текстовая строка, из которой следует извлечь знаки!позиция, начиная с которой следует извлечь знаки. Первый знак в тексте имеет позицию 1!количество знаков, которое следует извлечь из текста"}, "NUMBERVALUE": {"a": "(текст;[десятичный_разделитель];[разделитель_групп])", "d": "Функция для работы с текстом и данными, преобразует текст в числовое значение независимым от локали способом", "ad": "строка, представляющая число, которое требуется преобразовать!знак, используемый в строке в качестве десятичного разделителя!знак, используемый в строке в качестве разделителя групп разрядов"}, "PROPER": {"a": "(текст)", "d": "Функция для работы с текстом и данными, преобразует первую букву каждого слова в прописную (верхний регистр), а все остальные буквы - в строчные (нижний регистр)", "ad": "текстовая строка, заключенная в кавычки, формула, возвращающая текст, либо ссылка на ячейку, содержащую текст, в котором некоторые буквы заменяются на прописные"}, "REPLACE": {"a": "(стар_текст;начальная_позиция;число_знаков;нов_текст)", "d": "Функция для работы с текстом и данными, заменяет ряд символов на новый, с учетом заданного количества символов и начальной позиции, предназначена для языков, использующих однобайтовую кодировку (SBCS)", "ad": "строка, в которой нужно заменить некоторые знаки!позиция знака в строке 'старый_текст', начиная с которого нужно заменить часть этой строки на 'новый_текст'!число знаков в строке 'старый_текст', которое нужно заменить на знаки строки 'новый_текст'!строка, которая заменит соответствующую подстроку строки 'старый_текст'"}, "REPLACEB": {"a": "(стар_текст;начальная_позиция;число_знаков;нов_текст)", "d": "Функция для работы с текстом и данными, заменяет ряд символов на новый, с учетом заданного количества символов и начальной позиции, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.", "ad": "строка, в которой нужно заменить некоторые знаки!позиция знака в строке 'стар_текст', начиная с которого нужно заменить часть этой строки на 'нов_текст'!число знаков в строке 'стар_текст', которое нужно заменить на знаки строки 'нов_текст'!строка, которая заменит соответствующую подстроку строки 'стар_текст'"}, "REPT": {"a": "(текст;число_повторений)", "d": "Функция для работы с текстом и данными, используется для повторения данных в выбранной ячейке заданное количество раз", "ad": "повторяемый текст!положительное число, задающее количество требуемых повторений текста"}, "RIGHT": {"a": "(текст;[число_знаков])", "d": "Функция для работы с текстом и данными, извлекает подстроку из заданной строки, начиная с крайнего правого символа, согласно заданному количеству символов, предназначена для языков, использующих однобайтовую кодировку (SBCS)", "ad": "строка текста, содержащая знаки, которые нужно извлечь!число знаков, которое нужно извлечь; если не указано, принимается равным 1"}, "RIGHTB": {"a": "(текст;[число_знаков])", "d": "Функция для работы с текстом и данными, извлекает подстроку из заданной строки, начиная с крайнего правого символа, согласно заданному количеству символов, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д.", "ad": "строка текста, содержащая знаки, которые нужно извлечь!число знаков, которое нужно извлечь; если не указано, принимается равным 1"}, "SEARCH": {"a": "(искомый_текст;просматриваемый_текст;[начальная_позиция])", "d": "Функция для работы с текстом и данными, возвращает местоположение заданной подстроки в строке, предназначена для языков, использующих однобайтовую кодировку (SBCS)", "ad": "строка, которую требуется найти. Допускается использование знаков  ? и *; для поиска самих знаков * и ? используйте синтаксис ~? и ~*!строка, в которой нужно найти искомый текст!позиция в тексте для поиска (считая слева), с которой следует начать поиск. Если не указана, используется значение 1"}, "SEARCHB": {"a": "(искомый_текст;просматриваемый_текст;[начальная_позиция])", "d": "Функция для работы с текстом и данными, возвращает местоположение заданной подстроки в строке, предназначена для языков, использующих двухбайтовую кодировку (DBCS), таких как японский, китайский, корейский и т.д."}, "SUBSTITUTE": {"a": "(текст;стар_текст;нов_текст;[номер_вхождения])", "d": "Функция для работы с текстом и данными, заменяет ряд символов на новый", "ad": "либо текст, либо ссылка на ячейку, содержащую текст, в котором подставляются знаки!заменяемый текст, с учетом регистра знаков!строка, которой заменяется старый_текст!номер вхождения стар_текст, который следует заменить на нов_текст. Если опущено, заменяется каждое вхождение стар_текст"}, "T": {"a": "(значение)", "d": "Функция для работы с текстом и данными, используется для проверки, является ли значение в ячейке (или используемое как аргумент) текстом или нет. Если это не текст, функция возвращает пустой результат. Если значение/аргумент является текстом, функция возвращает это же текстовое значение", "ad": "проверяемое значение"}, "TEXT": {"a": "(значение;формат)", "d": "Функция для работы с текстом и данными, преобразует числовое значение в текст в заданном формате", "ad": "число, формула, определяющая числовое значение, или ссылка на ячейку, содержащая числовое значение!числовой формат в текстовом формате из поля \"Категория\" на вкладке \"число\" в диалоговом окне \"формат ячеек\""}, "TEXTJOIN": {"a": "(разделитель;игнорировать_пустые;текст1;[текст2];… )", "d": "Функция для работы с текстом и данными, объединяет текст из нескольких диапазонов и (или) строк, вставляя между текстовыми значениями указанный разделитель; если в качестве разделителя используется пустая текстовая строка, функция эффективно объединит диапазоны", "ad": "Символ или строку необходимо вставлять между всеми текстовыми элементами!При выборе значения ИСТИНА (по умолчанию) пустые ячейки пропускаются!Объединить можно от 1 до 252 текстовых строк или диапазонов"}, "TRIM": {"a": "(текст)", "d": "Функция для работы с текстом и данными, удаляет пробелы из начала и конца строки", "ad": "текст, из которого удаляются пробелы"}, "UNICHAR": {"a": "(число)", "d": "Функция для работы с текстом и данными, возвращает число (кодовую страницу), которая соответствует первому символу текста", "ad": "число, соответствующее знаку Юникода"}, "UNICODE": {"a": "(текст)", "d": "Функция для работы с текстом и данными, возвращает число (кодовую страницу), которая соответствует первому символу текста", "ad": "знак, для которого требуется получить значение Юникода"}, "UPPER": {"a": "(текст)", "d": "Функция для работы с текстом и данными, используется для преобразования букв в выбранной ячейке из нижнего регистра в верхний", "ad": "строка, буквы которой требуется преобразовать в прописные; может быть ссылкой или строкой текста"}, "VALUE": {"a": "(текст)", "d": "Функция для работы с текстом и данными, преобразует текстовое значение, представляющее число, в числовое значение. Если преобразуемый текст не является числом, функция возвращает ошибку #ЗНАЧ!", "ad": "текст в кавычках или ссылка на ячейку, содержащую текст который нужно преобразовать"}, "AVEDEV": {"a": "(список_аргументов)", "d": "Статистическая функция, используется для анализа диапазона данных и возвращает среднее абсолютных значений отклонений чисел от их среднего значения", "ad": "от 1 до 255 аргументов, для которых определяется среднее абсолютных отклонений"}, "AVERAGE": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и вычисляет среднее значение", "ad": "от 1 до 255 числовых аргументов, для которых вычисляется среднее"}, "AVERAGEA": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных, включая текстовые и логические значения, и вычисляет среднее значение. Функция СРЗНАЧА интерпретирует текст и логическое значение ЛОЖЬ как числовое значение 0, а логическое значение ИСТИНА как числовое значение 1", "ad": "от 1 до 255 аргументов, для которых требуется определить среднее"}, "AVERAGEIF": {"a": "(диапазон;условия;[диапазон_усреднения])", "d": "Статистическая функция, анализирует диапазон данных и вычисляет среднее значение всех чисел в диапазоне ячеек, которые соответствуют заданному условию", "ad": "диапазон обрабатываемых ячеек!условие в форме числа, выражения или текста, определяющее суммируемые ячейки!фактические ячейки для расчета среднего значения. Если этот аргумент опущен, будут использоваться ячейки, заданные аргументом \"диапазон\""}, "AVERAGEIFS": {"a": "(диапазон_усреднения;диапазон_условий1;условие1;[диапазон_условий2;условие2]; ... )", "d": "Статистическая функция, анализирует диапазон данных и вычисляет среднее значение всех чисел в диапазоне ячеек, которые соответствуют нескольким заданным условиям", "ad": "фактические ячейки, используемые для расчета среднего значения!диапазон ячеек, проверяемых на соответствие определенному условию!условие в форме числа, выражения или текста, определяющее ячейки, по которым будет выполняться расчет среднего значения"}, "BETADIST": {"a": " (x;альфа;бета;[A];[B]) ", "d": "Статистическая функция, возвращает интегральную функцию плотности бета-вероятности", "ad": "значение в интервале между A и B, для которого вычисляется функция!параметр распределения; должен быть положительной величиной!параметр распределения; должен быть положительной величиной!необязательная нижняя граница интервала изменения x. Если опущено, A = 0!необязательная верхняя граница интервала изменения x. Если опущено, В = 1"}, "BETAINV": {"a": " (вероятность;альфа;бета;[A];[B]) ", "d": "Статистическая функция, возвращает интегральную функцию плотности бета-вероятности", "ad": "вероятность, связанная с бета распределением!параметр распределения; должен быть положительной величиной!параметр распределения; должен быть положительной величиной!необязательная нижняя граница интервала изменения x. Если опущено, A = 0!необязательная верхняя граница интервала изменения x. Если опущено, В = 1"}, "BETA.DIST": {"a": " (x;альфа;бета;интегральная;[A];[B]) ", "d": "Статистическая функция, возвращает функцию бета-распределения", "ad": "значение в интервале между A и B, для которого вычисляется функция!параметр распределения; должен быть положительной величиной!параметр распределения; должен быть положительной величиной!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)!необязательная нижняя граница интервала изменения x. Если опущено, A = 0!необязательная верхняя граница интервала изменения x. Если опущено, В = 1"}, "BETA.INV": {"a": " (вероятность;альфа;бета;[A];[B]) ", "d": "Статистическая функция, возвращает обратную функцию к интегральной функции плотности бета-распределения вероятности ", "ad": "вероятность, связанная с бета распределением!параметр распределения; должен быть положительной величиной!параметр распределения; должен быть положительной величиной!необязательная нижняя граница интервала изменения x. Если опущено, A = 0!необязательная верхняя граница интервала изменения x. Если опущено, В = 1"}, "BINOMDIST": {"a": "(число_успехов;число_испытаний;вероятность_успеха;интегральная)", "d": "Статистическая функция, возвращает отдельное значение вероятности биномиального распределения", "ad": "число успешных испытаний!число независимых испытаний!вероятность успеха каждого испытания!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ)"}, "BINOM.DIST": {"a": "(число_успехов;число_испытаний;вероятность_успеха;интегральная)", "d": "Статистическая функция, возвращает отдельное значение биномиального распределения", "ad": "число успешных испытаний!число независимых испытаний!вероятность успеха каждого испытания!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ)"}, "BINOM.DIST.RANGE": {"a": "(число_испытаний;вероятность_успеха;число_успехов;[число_успехов2])", "d": "Статистическая функция, возвращает вероятность результата испытаний при помощи биномиального распределения", "ad": "число независимых испытаний!вероятность успеха в каждом испытании!количество успешных испытаний!если этот параметр указан, функция возвращает вероятность того, что число успешных испытаний будет находиться в диапазоне между значениями число_успехов и число_успехов2"}, "BINOM.INV": {"a": "(число_испытаний;вероятность_успеха;альфа)", "d": "Статистическая функция, возвращает наименьшее значение, для которого интегральное биномиальное распределение больше заданного значения критерия или равно ему", "ad": "число испытаний Бернулли!вероятность успеха в каждом испытании!значение критерия, число в диапазоне от 0 до 1"}, "CHIDIST": {"a": "(x;степени_свободы)", "d": "Статистическая функция, возвращает правостороннюю вероятность распределения хи-квадрат", "ad": "значение, для которого требуется вычислить распределение, неотрицательное число!число степеней свободы - число от 1 до 10^10, исключая 10^10"}, "CHIINV": {"a": "(вероятность;степени_свободы)", "d": "Статистическая функция, возвращает значение, обратное правосторонней вероятности распределения хи-квадрат.", "ad": "вероятность, связанная с распределением хи-квадрат, значение в диапазоне от 0 до 1!число степеней свободы - число от 1 до 10^10, исключая 10^10"}, "CHITEST": {"a": "(фактический_интервал;ожидаемый_интервал)", "d": "Статистическая функция, возвращает критерий независимости - значение статистики для распределения хи-квадрат (χ2) и соответствующее число степеней свободы", "ad": "диапазон, содержащий наблюдения, подлежащие сравнению с ожидаемыми значениями!диапазон, содержащий отношение произведений итогов по строкам и столбцам к общему итогу"}, "CHISQ.DIST": {"a": "(x;степени_свободы;интегральная)", "d": "Статистическая функция, возвращает распределение хи-квадрат", "ad": "значение, для которого требуется вычислить распределение, неотрицательное число!число степеней свободы - число от 1 до 10^10, исключая 10^10!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)"}, "CHISQ.DIST.RT": {"a": "(x;степени_свободы)", "d": "Статистическая функция, возвращает правостороннюю вероятность распределения хи-квадрат", "ad": "значение, для которого требуется вычислить распределение, неотрицательное число!число степеней свободы - число от 1 до 10^10, исключая 10^10"}, "CHISQ.INV": {"a": "(вероятность;степени_свободы)", "d": "Статистическая функция, возвращает значение, обратное левосторонней вероятности распределения хи-квадрат", "ad": "вероятность, связанная с распределением хи-квадрат, значение в диапазоне от 0 до 1!число степеней свободы - число от 1 до 10^10, исключая 10^10"}, "CHISQ.INV.RT": {"a": "(вероятность;степени_свободы)", "d": "Статистическая функция, возвращает значение, обратное левосторонней вероятности распределения хи-квадрат", "ad": "вероятность, связанная с распределением хи-квадрат, значение в диапазоне от 0 до 1!число степеней свободы - число от 1 до 10^10, исключая 10^10"}, "CHISQ.TEST": {"a": "(фактический_интервал;ожидаемый_интервал)", "d": "Статистическая функция, возвращает критерий независимости - значение статистики для распределения хи-квадрат (χ2) и соответствующее число степеней свободы", "ad": "диапазон, содержащий наблюдения, подлежащие сравнению с ожидаемыми значениями!диапазон, содержащий отношение произведений итогов по строкам и столбцам к общему итогу"}, "CONFIDENCE": {"a": "(альфа;стандартное_откл;размер)", "d": "Статистическая функция, возвращает доверительный интервал", "ad": "уровень значимости, используемый для вычисления доверительного уровня - число, большее 0 и меньшее 1!стандартное отклонение генеральной совокупности для интервала данных (предполагается известным). Должно быть больше нуля!размер выборки"}, "CONFIDENCE.NORM": {"a": "(альфа;стандартное_откл;размер)", "d": "Статистическая функция, возвращает доверительный интервал для среднего генеральной совокупности с нормальным распределением.", "ad": "уровень значимости, используемый для вычисления доверительного уровня - число, большее 0 и меньшее 1!стандартное отклонение генеральной совокупности для интервала данных (предполагается известным). Должно быть больше нуля!размер выборки"}, "CONFIDENCE.T": {"a": "(альфа;стандартное_откл;размер)", "d": "Статистическая функция, возвращает доверительный интервал для среднего генеральной совокупности, используя распределение Стьюдента", "ad": "уровень значимости, используемый для вычисления доверительного уровня - число, большее 0 и меньшее 1!стандартное отклонение генеральной совокупности для интервала данных (предполагается известным). Должно быть больше нуля!размер выборки"}, "CORREL": {"a": "(массив1;массив2)", "d": "Статистическая функция, используется для анализа диапазона данных и возвращает коэффициент корреляции между двумя диапазонами ячеек", "ad": "первый диапазон значений. Значениями могут быть числа, имена, массивы или ссылки с именами!второй диапазон значений. Значениями могут быть числа, имена, массивы или ссылки с именами"}, "COUNT": {"a": "(список_аргументов)", "d": "Статистическая функция, используется для подсчета количества ячеек в выбранном диапазоне, содержащих числа, без учета пустых или содержащих текст ячеек", "ad": "от 1 до 255 аргументов, которые могут содержать или ссылаться на данные различных типов, но учитываются только числовые значения"}, "COUNTA": {"a": "(список_аргументов)", "d": "Статистическая функция, используется для анализа диапазона ячеек и подсчета количества непустых ячеек", "ad": "от 1 до 255 аргументов любого типа, количество которых требуется определить"}, "COUNTBLANK": {"a": "(список_аргументов)", "d": "Статистическая функция, используется для анализа диапазона ячеек и возвращает количество пустых ячеек", "ad": "диапазон, в котором требуется определить количество пустых ячеек"}, "COUNTIFS": {"a": "(диапазон_условия1;условие1;[диапазон_условия2;условие2]; ... )", "d": "Статистическая функция, используется для подсчета количества ячеек выделенного диапазона, соответствующих нескольким заданным условиям", "ad": "диапазон ячеек, проверяемый на соответствие определенному условию!условие в форме числа, выражения или текста, определяющее подсчитываемые ячейки"}, "COUNTIF": {"a": "(диапазон_ячеек;условие)", "d": "Статистическая функция, используется для подсчета количества ячеек выделенного диапазона, соответствующих заданному условию", "ad": "диапазон, в котором подсчитывается количество непустых ячеек!условие в форме числа, выражения или текста, который определяет, какие ячейки надо подсчитывать"}, "COVAR": {"a": "(массив1;массив2)", "d": "Статистическая функция, возвращает ковариацию в двух диапазонах данных", "ad": "первый диапазон целых чисел - числа, массивы или ссылки на ячейки, содержащие числа!второй диапазон целых чисел - числа, массивы или ссылки на ячейки, содержащие числа"}, "COVARIANCE.P": {"a": "(массив1;массив2)", "d": "Статистическая функция, возвращает ковариацию совокупности, т. е. среднее произведений отклонений для каждой пары точек в двух наборах данных; ковариация используется для определения связи между двумя наборами данных", "ad": "первый диапазон целых чисел - числа, массивы или ссылки на ячейки, содержащие числа!второй диапазон целых чисел - числа, массивы или ссылки на ячейки, содержащие числа"}, "COVARIANCE.S": {"a": "(массив1;массив2)", "d": "Статистическая функция, возвращает ковариацию выборки, т. е. среднее произведений отклонений для каждой пары точек в двух наборах данных", "ad": "первый диапазон целых чисел - числа, массивы или ссылки на ячейки, содержащие числа!второй диапазон целых чисел - числа, массивы или ссылки на ячейки, содержащие числа"}, "CRITBINOM": {"a": "(число_испытаний;вероятность_успеха;альфа)", "d": "Статистическая функция, возвращает наименьшее значение, для которого интегральное биномиальное распределение больше или равно заданному условию", "ad": "число испытаний Бернулли!вероятность успеха в каждом испытании!значение критерия, число в диапазоне от 0 до 1"}, "DEVSQ": {"a": "(список_аргументов)", "d": "Статистическая функция, используется для анализа диапазона ячеек и возвращает сумму квадратов отклонений чисел от их среднего значения", "ad": "от 1 до 255 аргум<PERSON>нтов, массивов или ссылок на массивы, для которых вычисляется сумма квадратов отклонений"}, "EXPONDIST": {"a": "(x;лямбда;интегральная)", "d": "Статистическая функция, возвращает экспоненциальное распределение", "ad": "значение функции, неотрицательное число!значение параметра, положительное число!логическое значение, определяющее возвращаемую функцию: интегральную функцию распределения, если ИСТИНА; функцию плотности распределения вероятности, если ЛОЖЬ"}, "EXPON.DIST": {"a": "(x;лямбда;интегральная)", "d": "Статистическая функция, возвращает экспоненциальное распределение", "ad": "значение функции, неотрицательное число!значение параметра, положительное число!логическое значение, определяющее возвращаемую функцию: интегральную функцию распределения, если ИСТИНА; функцию плотности распределения вероятности, если ЛОЖЬ"}, "FDIST": {"a": "(x;степени_свободы1;степени_свободы2)", "d": "Статистическая функция, возвращает правый хвост F-распределения вероятности для двух наборов данных. Эта функция позволяет определить, имеют ли два множества данных различные степени разброса результатов", "ad": "значение, для которого вычисляется функция, неотрицательное число!число степеней свободы - число от 1 до 10^10, исключая 10^10!знаменатель степеней свободы - число от 1 до 10^10, исключая 10^10"}, "FINV": {"a": "(вероятность;степени_свободы1;степени_свободы2)", "d": "Статистическая функция, возвращает значение, обратное (правостороннему) F-распределению вероятностей; F-распределение может использоваться в F-тесте, который сравнивает степени разброса двух множеств данных", "ad": "вероятность, связанная с F-интегральным распределением, число в диапазоне от 0 до 1 включительно!числитель степеней свободы - число от 1 до 10^10, исключая 10^10!знаменатель степеней свободы - число от 1 до 10^10, исключая 10^10"}, "FTEST": {"a": "(массив1;массив2)", "d": "Статистическая функция, возвращает результат F-теста; F-тест возвращает двустороннюю вероятность того, что разница между дисперсиями аргументов массив1 и массив2 несущественна; эта функция позволяет определить, имеют ли две выборки различные дисперсии", "ad": "первый массив или диапазон - числа, массивы или ссылки на ячейки, содержащие числа (пробелы игнорируются)!второй массив или диапазон - числа, массивы или ссылки на ячейки, содержащие числа (пробелы игнорируются)"}, "F.DIST": {"a": "(x;степени_свободы1;степени_свободы2;интегральная)", "d": "Статистическая функция, возвращает F-распределение вероятности; эта функция позволяет определить, имеют ли два множества данных различные степени разброса результатов", "ad": "значение, для которого вычисляется функция, неотрицательное число!число степеней свободы - число от 1 до 10^10, исключая 10^10!знаменатель степеней свободы - число от 1 до 10^10, исключая 10^10!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)"}, "F.DIST.RT": {"a": "(x;степени_свободы1;степени_свободы2)", "d": "Статистическая функция, возвращает правый хвост F-распределения вероятности для двух наборов данных; эта функция позволяет определить, имеют ли два множества данных различные степени разброса результатов", "ad": "значение, для которого вычисляется функция, неотрицательное число!число степеней свободы - число от 1 до 10^10, исключая 10^10!знаменатель степеней свободы - число от 1 до 10^10, исключая 10^10"}, "F.INV": {"a": "(вероятность;степени_свободы1;степени_свободы2)", "d": "Статистическая функция, возвращает значение, обратное F-распределению вероятности; F-распределение может использоваться в F-тесте, который сравнивает степени разброса двух наборов данных", "ad": "вероятность, связанная с F-интегральным распределением, число в диапазоне от 0 до 1 включительно!числитель степеней свободы - число от 1 до 10^10, исключая 10^10!знаменатель степеней свободы - число от 1 до 10^10, исключая 10^10"}, "F.INV.RT": {"a": "(вероятность;степени_свободы1;степени_свободы2)", "d": "Статистическая функция, возвращает значение, обратное F-распределению вероятности; F-распределение может использоваться в F-тесте, который сравнивает степени разброса двух наборов данных", "ad": "вероятность, связанная с F-интегральным распределением, число в диапазоне от 0 до 1 включительно!числитель степеней свободы - число от 1 до 10^10, исключая 10^10!знаменатель степеней свободы - число от 1 до 10^10, исключая 10^10"}, "F.TEST": {"a": "(массив1;массив2)", "d": "Статистическая функция, возвращает результат F-теста, двустороннюю вероятность того, что разница между дисперсиями аргументов массив1 и массив2 несущественна; эта функция позволяет определить, имеют ли две выборки различные дисперсии", "ad": "первый массив или диапазон - числа, массивы или ссылки на ячейки, содержащие числа (пробелы игнорируются)!второй массив или диапазон - числа, массивы или ссылки на ячейки, содержащие числа (пробелы игнорируются)"}, "FISHER": {"a": "(число)", "d": "Статистическая функция, возвращает преобразование Фишера для числа", "ad": "числовое значение, которое требуется преобразовать, целое число в интервале от -1 до 1, исключая концы"}, "FISHERINV": {"a": "(число)", "d": "Статистическая функция, выполняет обратное преобразование Фишера", "ad": "значение, для которого производится обратное преобразование"}, "FORECAST": {"a": "(x;массив-1;массив-2)", "d": "Статистическая функция, предсказывает будущее значение на основе существующих значений", "ad": "точка данных, для которой необходимо получить прогнозное значение. Она должна быть числовым значением!зависимый массив или диапазон числовых данных!независимый массив или диапазон числовых данных. Дисперсия значений \"Известное_значение_x\" не должна быть равна нулю"}, "FORECAST.ETS": {"a": "(целевая_дата;значения;временная_шкала;[сезонность];[заполнение_данных];[агрегирование])", "d": "Статистическая функция, рассчитывает или прогнозирует будущее значение на основе существующих (ретроспективных) данных с использованием версии AAA алгоритма экспоненциального сглаживания (ETS)", "ad": "точка данных, для которой Редактор таблиц прогнозирует значение. Должна соответствовать значениям на временной шкале.!массив или диапазон прогнозируемых числовых данных.!независимый массив или диапазон числовых данных. Интервалы между датами (которые не могут быть нулевыми) на временной шкале должны быть равными.!является необязательным числовым значением, которое указывает на сезонный шаблон. Значение по умолчанию, которое равно 1, указывает на сезонность и определяется автоматически.!необязательное значение для обработки недостающих значений. Значение по умолчанию, равное 1, заменяет недостающие значения соседними, а равное 0 — нулями.!необязательное значение для агрегации нескольких значений с одинаковой меткой времени. Если значение не указано, Редактор таблиц использует средние значения."}, "FORECAST.ETS.CONFINT": {"a": "(целевая_дата;значения;временная_шкала;[вероятность];[сезонность];[заполнение_данных];[агрегирование])", "d": "Статистическая функция, возвращает доверительный интервал для прогнозной величины на указанную дату", "ad": "точка данных, для которой Редактор таблиц прогнозирует значение. Должна соответствовать значениям на временной шкале.!массив или диапазон прогнозируемых числовых данных.!независимый массив или диапазон числовых данных. Интервалы между датами (которые не могут быть нулевыми) на временной шкале должны быть равными.!число от 0 до 1, соответствующее вероятности для вычисленного доверительного интервала. Значение по умолчанию .95.!является необязательным числовым значением, которое указывает на сезонный шаблон. Значение по умолчанию, которое равно 1, указывает на сезонность и определяется автоматически.!необязательное значение для обработки недостающих значений. Значение по умолчанию, равное 1, заменяет недостающие значения соседними, а равное 0 — нулями.!необязательное значение для агрегации нескольких значений с одинаковой меткой времени. Если значение не указано, Редактор таблиц использует средние значения."}, "FORECAST.ETS.SEASONALITY": {"a": "(значения;временная_шкала;[заполнение_данных];[агрегирование])", "d": "Статистическая функция, возвращает длину повторяющегося фрагмента, обнаруженного приложением в заданном временном ряду", "ad": "массив или диапазон прогнозируемых числовых данных!независимый массив или диапазон числовых данных. Интервалы между датами (которые не могут быть нулевыми) на временной шкале должны быть равными.!необязательное значение для обработки недостающих значений. Значение по умолчанию, равное 1, заменяет недостающие значения соседними, а равное 0 — нулями.!необязательное числовое значение для агрегирования значений с одинаковой меткой времени. Если оно не указано, Редактор таблиц использует среднее значение."}, "FORECAST.ETS.STAT": {"a": "(значения;временная_шкала;тип_статистики;[сезонность];[заполнение_данных];[агрегирование])", "d": "Статистическая функция, возвращает статистическое значение, являющееся результатом прогнозирования временного ряда; тип статистики определяет, какая именно статистика используется этой функцией", "ad": "массив или диапазон прогнозируемых числовых данных.!независимый массив или диапазон числовых данных. Интервалы между датами (которые не могут быть нулевыми) на временной шкале должны быть равными.!число от 1 до 8, указывающее на статистику, которую Редактор таблиц вернет для вычисляемого прогноза. !необязательное числовое значение, указывающее на сезонную продолжительность. Значение по умолчанию, равное 1, указывает на сезонность и определяется автоматически.!необязательное числовое значение для обработки недостающих значений. Значение по умолчанию, равное 1, заменяет недостающие значения на соседние. Значение, равное 0, заменяет значения нулями.!необязательное числовое значение для агрегирования нескольких значений с одинаковой меткой времени. Если значение не указано, Редактор таблиц использует средние значения."}, "FORECAST.LINEAR": {"a": "(x;известные_значения_y;известные_значения_x)", "d": "Статистическая функция, вычисляет или предсказывает будущее значение по существующим значениям; предсказываемое значение — это значение y, соответствующее заданному значению x; значения x и y известны; новое значение предсказывается с использованием линейной регрессии", "ad": "элемент данных, для которого прогнозируется значение (должен быть числовым значением)!зависимый массив или диапазон числовых данных!независимый массив или диапазон числовых данных. Дисперсия известных значений x не должна быть нулевой"}, "FREQUENCY": {"a": "(массив_данных;массив_интервалов)", "d": "Статистическая функция, вычисляет частоту появления значений в выбранном диапазоне ячеек и отображает первое значение возвращаемого вертикального массива чисел", "ad": "массив или ссылка на множество данных, для которых вычисляются частоты (пробелы и текст не учитываются)!массив интервалов или ссылка на интервалы, в которых группируются значения из массива данных"}, "GAMMA": {"a": "(число)", "d": "Статистическая функция, возвращает значение гамма-функции", "ad": "значение, для которого требуется вычислить гамма-функцию"}, "GAMMADIST": {"a": "(x;альфа;бета;интегральная)", "d": "Статистическая функция, возвращает гамма-распределение", "ad": "значение, для которого требуется вычислить распределение, неотрицательное число!параметр распределения, положительное число!параметр распределения, положительное число. Если бета=1, то ГАММАРАСП возвращает стандартное гамма распределение!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ или отсутствие значения)"}, "GAMMA.DIST": {"a": "(x;альфа;бета;интегральная)", "d": "Статистическая функция, возвращает гамма-распределение", "ad": "значение, для которого требуется вычислить распределение, неотрицательное число!параметр распределения, положительное число!параметр распределения, положительное число. Если бета=1, функция ГАММА.РАСП возвращает стандартное гамма распределение!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ или отсутствие значения)"}, "GAMMAINV": {"a": "(вероятность;альфа;бета)", "d": "Статистическая функция, возвращает значение, обратное гамма-распределению", "ad": "вероятность, связанная с гамма-распределением, число в диапазоне от 0 до 1, включительно!параметр распределения, положительное число!параметр распределения, положительное число. Если бета=1, то ГАММАОБР возвращает стандартное гамма-распределение"}, "GAMMA.INV": {"a": "(вероятность;альфа;бета)", "d": "Статистическая функция, возвращает значение, обратное гамма-распределению", "ad": "вероятность, связанная с гамма-распределением, число в диапазоне от 0 до 1 включительно!параметр распределения, положительное число!параметр распределения, положительное число. Если бета=1, то ГАММА.ОБР возвращает обратное стандартное гамма-распределение"}, "GAMMALN": {"a": "(число)", "d": "Статистическая функция, возвращает натуральный логарифм гамма-функции", "ad": "значение, для которого вычисляется ГАММАНЛОГ, положительное число"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Статистическая функция, возвращает натуральный логарифм гамма-функции", "ad": "значение, для которого вычисляется ГАММАНЛОГ.ТОЧН, положительное число"}, "GAUSS": {"a": "(z)", "d": "Статистическая функция, рассчитывает вероятность, с которой элемент стандартной нормальной совокупности находится в интервале между средним и стандартным отклонением z от среднего", "ad": "значение, для которого строится распределение"}, "GEOMEAN": {"a": "(список_аргументов)", "d": "Статистическая функция, вычисляет среднее геометрическое для списка значений", "ad": "от 1 до 255 чисел, и<PERSON><PERSON><PERSON>, массивов или ссылок на числовые значения, для которых вычисляется среднее"}, "GROWTH": {"a": "(известные_значения_y; [известные_значения_x]; [новые_значения_x]; [конст])", "d": "Статистическая функция, рассчитывает прогнозируемый экспоненциальный рост на основе имеющихся данных; возвращает значения y для последовательности новых значений x, задаваемых с помощью существующих значений x и y", "ad": "множество значений y, которые уже известны для соотношения y=b*m^x, массив или диапазон положительных чисел!необязательное множество значений x, для которых, возможно, уже известно соотношение y = b*m^x, массив или диапазон того же размера, что и для y!новые значения x, для которых РОСТ возвращает соответствующие значения y!логическое значение: константа b вычисляется обычным образом при значении ИСТИНА и равна 1 при значении ЛОЖЬ или отсутствии значения"}, "HARMEAN": {"a": "(список_аргументов)", "d": "Статистическая функция, вычисляет среднее гармоническое для списка значений", "ad": "от 1 до 255 чисел, и<PERSON><PERSON><PERSON>, массивов или ссылок на числовые значения, для которых вычисляется среднее гармоническое"}, "HYPGEOM.DIST": {"a": "(число_успехов_в_выборке;размер_выборки;число_успехов_в_совокупности;размер_совокупности;интегральная)", "d": "Статистическая функция, возвращает гипергеометрическое распределение, вероятность заданного количества успехов в выборке, если заданы размер выборки, количество успехов в генеральной совокупности и размер генеральной совокупности", "ad": "число успехов в выборке!размер выборки!число успехов в совокупности!размер совокупности!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)"}, "HYPGEOMDIST": {"a": "(число_успехов_в_выборке;размер_выборки;число_успехов_в_совокупности;размер_совокупности)", "d": "Статистическая функция, возвращает гипергеометрическое распределение, вероятность заданного количества успехов в выборке, если заданы размер выборки, количество успехов в генеральной совокупности и размер генеральной совокупности", "ad": "число успешных испытаний в выборке!размер выборки!число успешных испытаний в генеральной совокупности!размер генеральной совокупности"}, "INTERCEPT": {"a": "(массив1;массив2)", "d": "Статистическая функция, анализирует значения первого и второго массивов для вычисления точки пересечения", "ad": "зависимое множество наблюдений или данных — числа, массивы или ссылки на ячейки, содержащие числа!независимое множество наблюдений или данных — числа, массивы или ссылки на ячейки, содержащие числа"}, "KURT": {"a": "(список_аргументов)", "d": "Статистическая функция, возвращает эксцесс списка значений", "ad": "от 1 до 255 чисел, и<PERSON><PERSON><PERSON>, массивов или ссылок на числовые значения, для которых вычисляется эксцесс"}, "LARGE": {"a": "(массив;k)", "d": "Статистическая функция, анализирует диапазон ячеек и возвращает k-ое по величине значение", "ad": "массив или диапазон, для которых определяется k-ое наибольшее значение!позиция (начиная с наибольшей) в массиве или диапазоне"}, "LINEST": {"a": "(известные_значения_y; [известные_значения_x]; [конст]; [статистика])", "d": "Статистическая функция, рассчитывает статистику для ряда с применением метода наименьших квадратов, чтобы вычислить прямую линию, которая наилучшим образом аппроксимирует имеющиеся данные и затем возвращает массив, который описывает полученную прямую; поскольку возвращается массив значений, функция должна задаваться в виде формулы массива", "ad": "множество значений y, для которых уже известно соотношение y = mx + b!необязательное множество значений x, для которых, возможно, уже известно соотношение y = mx + b!логическое значение: константа b вычисляется обычным образом при значении ИСТИНА или отсутствии значения и равна 0 при значении ЛОЖЬ!логическое значение, которое указывает, требуется ли вернуть дополнительную статистику по регрессии (ИСТИНА) или только коэффициенты m и константу b (ЛОЖЬ или отсутствие значения)"}, "LOGEST": {"a": "(известные_значения_y; [известные_значения_x]; [конст]; [статистика])", "d": "Статистическая функция, регрессионном анализе вычисляет экспоненциальную кривую, подходящую для данных и возвращает массив значений, описывающих кривую; поскольку данная функция возвращает массив значений, она должна вводиться как формула массива", "ad": "множество значений y, которые уже известны для соотношения y=b*m^x!необязательное множество значений x, для которых, возможно, уже известно соотношение y = b*m^x!логическое значение: константа b вычисляется обычным образом при значении ИСТИНА или отсутствии значения и равна 1 при значении ЛОЖЬ!логическое значение, которое указывает, требуется ли вернуть дополнительную статистику по регрессии (ИСТИНА) или только коэффициенты m и константу b (ЛОЖЬ или отсутствие значения)"}, "LOGINV": {"a": "(x;среднее;стандартное_отклонение)", "d": "Статистическая функция, возвращает обратное логарифмическое нормальное распределение для заданного значения x с указанными параметрами", "ad": "вероятность, связанная с нормальным логарифмическим распределением, число в диапазоне от 0 до 1 включительно!среднее ln(x)!стандартное отклонение ln(x), положительное число"}, "LOGNORM.DIST": {"a": "(x;среднее;стандартное_откл;интегральная)", "d": "Статистическая функция, возвращает логнормальное распределение для x, где ln(x) является нормально распределенным с параметрами Среднее и Стандартное отклонение; эта функция используется для анализа данных, которые были логарифмически преобразованы", "ad": "значение, для которого вычисляется функция, положительное число!среднее ln(x)!стандартное отклонение ln(x), положительное число!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)"}, "LOGNORM.INV": {"a": "(вероятность;среднее;станд_откл)", "d": "Статистическая функция, возвращает обратную функцию интегрального логнормального распределения x, где ln(x) имеет нормальное распределение с параметрами Среднее и Стандартное отклонение; логнормальное распределение применяется для анализа логарифмически преобразованных данных", "ad": "вероятность, связанная с нормальным логарифмическим распределением, число в диапазоне от 0 до 1 включительно!среднее ln(x)!стандартное отклонение ln(x), положительное число"}, "LOGNORMDIST": {"a": "(x;среднее;стандартное_откл)", "d": "Статистическая функция, анализирует логарифмически преобразованные данные и возвращает логарифмическое нормальное распределение для заданного значения x с указанными параметрами", "ad": "значение, для которого вычисляется функция, положительное число!среднее ln(x)!стандартное отклонение ln(x), положительное число"}, "MAX": {"a": "(число1;число2; ...)", "d": "Статистическая функция, используется для анализа диапазона данных и поиска наибольшего числа", "ad": "от 1 до 255 чисел, пустых ячеек, логических или текстовых значений, среди которых ищется наибольшее значение"}, "MAXA": {"a": "(число1;число2; ...)", "d": "Статистическая функция, используется для анализа диапазона данных и поиска наибольшего значения", "ad": "от 1 до 255 чисел, пустых ячеек, логических значений или чисел в текстовом виде, среди которых требуется определить наибольшее"}, "MAXIFS": {"a": "(макс_диапазон;диапазон_условия1;условие1;[диапазон_условия2;условие2]; ...)", "d": "Статистическая функция, возвращает максимальное значение из заданных определенными условиями или критериями ячеек.", "ad": "Яч<PERSON>йки, для которых нужно определить максимальное значение!Диапазон ячеек, которые нужно проверить на соответствие определенному условию!Условие или критерий, связанные с форматом числа, выражения или текста, или текст, который определяет, какие ячейки будут включены в диапазон при определении максимального значения"}, "MEDIAN": {"a": "(список_аргументов)", "d": "Статистическая функция, вычисляет медиану для списка значений", "ad": "от 1 до 255 чисел, и<PERSON><PERSON><PERSON>, массивов или ссылок на числовые значения, для которых определяется медиана"}, "MIN": {"a": "(число1;число2; ...)", "d": "Статистическая функция, используется для анализа диапазона данных и поиска наименьшего числа", "ad": "от 1 до 255 чисел, пустых ячеек, логических или текстовых значений, среди которых ищется наименьшее значение"}, "MINA": {"a": "(число1;число2; ...)", "d": "Статистическая функция, используется для анализа диапазона данных и поиска наименьшего значения", "ad": "от 1 до 255 чисел, пустых ячеек, логических значений или чисел в текстовом виде, среди которых требуется определить наименьшее"}, "MINIFS": {"a": "(мин_диапазон;диапазон_условия1;условие1;[диапазон_условия2;условие2]; ...)", "d": "Статистическая функция, возвращает минимальное значение из заданных определенными условиями или критериями ячеек", "ad": "Яч<PERSON><PERSON>к<PERSON>, для которых нужно определить минимальное значение!Диапазон ячеек, которые нужно проверить на соответствие определенному условию!Условие или критерий, связанные с форматом числа, выражения или текста, или текст, который определяет, какие ячейки будут включены в диапазон при определении минимального значения"}, "MODE": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и возвращает наиболее часто встречающееся значение", "ad": "от 1 до 255 чисел, и<PERSON><PERSON><PERSON>, массивов или ссылок на числовые значения, для которых вычисляется мода"}, "MODE.MULT": {"a": "(число1;[число2]; ... )", "d": "Статистическая функция, возвращает вертикальный массив из наиболее часто встречающихся (повторяющихся) значений в массиве или диапазоне данных", "ad": "от 1 до 255 чисел, и<PERSON><PERSON><PERSON>, массивов или ссылок на числовые значения, для которых вычисляется мода"}, "MODE.SNGL": {"a": "(число1;[число2]; ... )", "d": "Статистическая функция, возвращает наиболее часто встречающееся или повторяющееся значение в массиве или интервале данных", "ad": "от 1 до 255 чисел, и<PERSON><PERSON><PERSON>, массивов или ссылок на числовые значения, для которых вычисляется мода"}, "NEGBINOM.DIST": {"a": "(число_неудач;число_успехов;вероятность_успеха;интегральная)", "d": "Статистическая функция, возвращает отрицательное биномиальное распределение — вероятность возникновения определенного числа неудач до указанного количества успехов при заданной вероятности успеха", "ad": "число неудачных испытаний!пороговое значение числа успешных испытаний!вероятность успеха - число в интервале от 0 до 1!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)"}, "NEGBINOMDIST": {"a": "(число_неудач;число_успехов;вероятность_успеха)", "d": "Статистическая функция, возвращает отрицательное биномиальное распределение", "ad": "число неудачных испытаний!пороговое значение числа успешных испытаний!вероятность успеха - число в интервале от 0 до 1"}, "NORM.DIST": {"a": "(x;среднее;стандартное_откл;интегральная)", "d": "Статистическая функция, возвращает нормальную функцию распределения для указанного среднего и стандартного отклонения", "ad": "значение, для которого строится распределение!арифметическое среднее распределения!стандартное отклонение распределения, положительное число!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)"}, "NORMDIST": {"a": "(x;среднее;стандартное_откл;интегральная)", "d": "Статистическая функция, возвращает нормальную функцию распределения для указанного среднего значения и стандартного отклонения", "ad": "значение, для которого строится распределение!арифметическое среднее распределения!стандартное отклонение распределения, положительное число!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ)"}, "NORM.INV": {"a": "(вероятность;среднее;стандартное_откл;)", "d": "Статистическая функция, возвращает обратное нормальное распределение для указанного среднего и стандартного отклонения", "ad": "вероятность, соответствующая нормальному распределению, число в диапазоне от 0 до 1 включительно!арифметическое среднее распределения!стандартное отклонение распределения, положительное число"}, "NORMINV": {"a": "(x;среднее;стандартное_откл)", "d": "Статистическая функция, возвращает обратное нормальное распределение для указанного среднего значения и стандартного отклонения", "ad": "вероятность, соответствующая нормальному распределению, число в диапазоне от 0 до 1 включительно!арифметическое среднее распределения!стандартное отклонение распределения, положительное число"}, "NORM.S.DIST": {"a": "(z;интегральная)", "d": "Статистическая функция, возвращает стандартное нормальное интегральное распределение; это распределение имеет среднее, равное нулю, и стандартное отклонение, равное единице.", "ad": "значение, для которого строится распределение!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)"}, "NORMSDIST": {"a": "(число)", "d": "Статистическая функция, возвращает стандартное нормальное интегральное распределение", "ad": "значение, для которого строится распределение"}, "NORM.S.INV": {"a": "(вероятность)", "d": "Статистическая функция, возвращает обратное значение стандартного нормального распределения; это распределение имеет среднее, равное нулю, и стандартное отклонение, равное единице", "ad": "вероятность, соответствующая нормальному распределению"}, "NORMSINV": {"a": "(вероятность)", "d": "Статистическая функция, возвращает обратное значение стандартного нормального распределения", "ad": "вероятность, соответствующая нормальному распределению"}, "PEARSON": {"a": "(массив1;массив2)", "d": "Статистическая функция, возвращает коэффициент корреляции Пирсона", "ad": "множество независимых значений!множество зависимых значений"}, "PERCENTILE": {"a": "(массив;k)", "d": "Статистическая функция, анализирует диапазон данных и возвращает k-ю персентиль", "ad": "массив или диапазон с численными значениями, который определяет относительное положение!значение процентиля в интервале от 0 до 1 включительно"}, "PERCENTILE.EXC": {"a": "(массив;k)", "d": "Статистическая функция, возвращает k-ю процентиль для значений диапазона, где k — число от 0 и 1 (не включая эти числа)", "ad": "массив или диапазон с численными значениями, который определяет относительное положение!значение процентиля в интервале от 0 до 1 включительно"}, "PERCENTILE.INC": {"a": "(массив;k)", "d": "Статистическая функция, возвращает k-ю процентиль для значений диапазона, где k — число от 0 и 1 (включая эти числа)", "ad": "массив или диапазон с численными значениями, который определяет относительное положение!значение процентиля в интервале от 0 до 1 включительно"}, "PERCENTRANK": {"a": "(массив;x;[разрядность])", "d": "Статистическая функция, возвращает категорию значения в наборе данных как процентное содержание в наборе данных", "ad": "массив или диапазон с численными значениями, который определяет относительное положение!значение, для которого определяется ранг!необязательное значение, определяющее количество значащих цифр в возвращаемом значении процентного содержания; по умолчанию принимается равным 3 (0,xxx%)"}, "PERCENTRANK.EXC": {"a": "(массив;x;[разрядность])", "d": "Статистическая функция, возвращает ранг значения в наборе данных как процентное содержание в наборе данных (от 0 до 1, не включая эти числа)", "ad": "массив или диапазон с числовыми значениями, определяющий относительное положение!значение, для которого определяется ранг!необязательное значение, определяющее количество значимых цифр в возвращаемом процентном значении; если не указано, возвращается значение с тремя цифрами (0,xxx%)"}, "PERCENTRANK.INC": {"a": "(массив;x;[разрядность])", "d": "Статистическая функция, возвращает ранг значения в наборе данных как процентное содержание в наборе данных (от 0 до 1, включая эти числа)", "ad": "массив или диапазон с числовыми значениями, определяющий относительное положение!значение, для которого определяется ранг!необязательное значение, определяющее количество значащих цифр в возвращаемом процентном значении; если не указано, возвращается значение с тремя цифрами (0,xxx%)"}, "PERMUT": {"a": "(число;число_выбранных)", "d": "Статистическая функция, возвращает количество перестановок для заданного числа элементов", "ad": "целое число, задающее общее количество объектов!целое число, задающее количество объектов в каждой перестановке"}, "PERMUTATIONA": {"a": "(число;число_выбранных)", "d": "Статистическая функция, возвращает количество перестановок для заданного числа объектов (с повторами), которые можно выбрать из общего числа объектов", "ad": "общее число объектов!число объектов в каждой перестановке"}, "PHI": {"a": "(x)", "d": "Статистическая функция, возвращает значение функции плотности для стандартного нормального распределения", "ad": "число, для которого требуется найти плотность стандартного нормального распределения"}, "POISSON": {"a": "(x;среднее;интегральная)", "d": "Статистическая функция, возвращает распределение Пуассона", "ad": "количество событий!ожидаемое числовое значение, положительное число!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ)"}, "POISSON.DIST": {"a": "(x;среднее;интегральная)", "d": "Статистическая функция, возвращает распределение Пуассона; обычное применение распределения Пуассона состоит в предсказании количества событий, происходящих за определенное время, например количества машин, появляющихся на площади за одну минуту", "ad": "количество событий!ожидаемое числовое значение, положительное число!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ)"}, "PROB": {"a": "(x_интервал;интервал_вероятностей;[нижний_предел];[верхний_предел])", "d": "Статистическая функция, возвращает вероятность того, что значения из интервала находятся внутри нижнего и верхнего пределов", "ad": "интервал числовых значений x, с которыми связаны вероятности!множество вероятностей, соответствующих значениям в аргументе 'x_интервал'; значения лежат в интервале между 0 и 1, исключая 0!нижняя граница значения, для которого вычисляется вероятность!необязательная верхняя граница значения, для которого вычисляется вероятность. Если опущена, возвращается вероятность того, что значения в аргументе 'x_интервал' равны нижнему пределу"}, "QUARTILE": {"a": "(массив;часть)", "d": "Статистическая функция, анализирует диапазон данных и возвращает квартиль", "ad": "массив или диапазон ячеек с числовыми значениями, для которых определяется значение квартиля!значение: минимальное = 0; первый квартиль = 1; медиана = 2; третий квартиль = 3; максимальное значение = 4"}, "QUARTILE.INC": {"a": "(массив;часть)", "d": "Статистическая функция, возвращает квартиль набора данных на основе значений процентили от 0 до 1 (включительно)", "ad": "массив или диапазон ячеек с числовыми значениями, для которых определяется значение квартиля!значение: минимальное = 0; первый квартиль = 1; медиана = 2; третий квартиль = 3; максимальное значение = 4"}, "QUARTILE.EXC": {"a": "(массив;часть)", "d": "Статистическая функция, возвращает квартиль набора данных на основе значений процентили от 0 до 1, исключая эти числа", "ad": "массив или диапазон ячеек с числовыми значениями, для которых определяется значение квартиля!значение: минимальное = 0; первый квартиль = 1; медиана = 2; третий квартиль = 3; максимальное значение = 4"}, "RANK": {"a": "(число;ссылка;[порядок])", "d": "Статистическая функция, возвращает ранг числа в списке чисел; ранг числа — это его величина относительно других значений в списке; если отсортировать список, то ранг числа будет его позицией.)", "ad": "число, для которого определяется ранг!массив или ссылка на список чисел. Нечисловые значения игнорируются!число, определяющее способ округления"}, "RANK.AVG": {"a": "(число;ссылка;[порядок])", "d": "Статистическая функция, возвращает ранг числа в списке чисел, то есть его величину относительно других значений в списке; если несколько значений имеют одинаковый ранг, возвращается среднее.", "ad": "число, для которого определяется ранг!массив или ссылка на список чисел. Нечисловые значения в ссылке игнорируются!число: опущено или 0 - сортировка рангов в списке по убыванию; любое ненулевое значение - сортировка рангов в списке по возрастанию"}, "RANK.EQ": {"a": "(число;ссылка;[порядок])", "d": "Статистическая функция, возвращает ранг числа в списке чисел, то есть его величину относительно других значений в списке", "ad": "число, для которого определяется ранг!массив или ссылка на список чисел. Нечисловые значения в ссылке игнорируются!число: опущено или 0 - сортировка рангов в списке по убыванию; любое ненулевое значение - сортировка рангов в списке по возрастанию"}, "RSQ": {"a": "(массив1;массив2)", "d": "Статистическая функция, возвращает квадрат коэффициента корреляции Пирсона", "ad": "массив или диапазон, который может включать числа или имена, массивы или ссылки на ячейки с числами!массив или диапазон, который может включать числа или имена, массивы или ссылки на ячейки с числами"}, "SKEW": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и возвращает асимметрию распределения для списка значений", "ad": "от 1 до 255 числовых значений, массивов чисел или ссылок на числовые значения, для которых вычисляется асимметричность"}, "SKEW.P": {"a": "(число 1;[число 2]; … )", "d": "Статистическая функция, возвращает асимметрию распределения на основе заполнения: характеристика степени асимметрии распределения относительно его среднего", "ad": "от 1 до 254 числовых значений или имен, массивов чисел или ссылок на числовые значения, для которых вычисляется асимметричность"}, "SLOPE": {"a": "(массив-1;массив-2)", "d": "Статистическая функция, возвращает наклон линии линейной регрессии для данных в двух массивах", "ad": "массив или диапазон, содержащий числовые зависимые элементы данных!множество независимых элементов данных — имена, массивы или ссылки на ячейки, содержащие числа"}, "SMALL": {"a": "(массив;k)", "d": "Статистическая функция, анализирует диапазон данных и находит k-ое наименьшее значение", "ad": "массив или диапазон числовых данных, для которых определяется k-ое наименьшее значение!позиция (начиная с наименьшей) в массиве или диапазоне"}, "STANDARDIZE": {"a": "(x;среднее;стандартное_откл)", "d": "Статистическая функция, возвращает нормализованное значение для распределения, характеризуемого заданными параметрами", "ad": "нормализуемое значение!арифметическое среднее распределения!стандартное отклонение распределения, положительное число"}, "STDEV": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и возвращает стандартное отклонение по выборке, содержащей числа", "ad": "от 1 до 255 значений, составляющих выборку из генеральной совокупности; допускаются числовые значения и ссылки на числовые значения"}, "STDEV.P": {"a": "(число1;[число2]; ... )", "d": "Статистическая функция, вычисляет стандартное отклонение по генеральной совокупности, заданной аргументами. При этом логические значения и текст игнорируются", "ad": "от 1 до 255 значений, составляющих генеральную совокупность; допускаются числовые значения и ссылки на числовые значения"}, "STDEV.S": {"a": "(число1;[число2]; ... )", "d": "Статистическая функция, оценивает стандартное отклонение по выборке, логические значения и текст игнорируются", "ad": "от 1 до 255 значений, составляющих выборку из генеральной совокупности; допускаются числовые значения и ссылки на числовые значения"}, "STDEVA": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и возвращает стандартное отклонение по выборке, содержащей числа, текст и логические значения (ИСТИНА или ЛОЖЬ). Текст и логические значения ЛОЖЬ интерпретируются как 0, а логические значения ИСТИНА - как 1", "ad": "от 1 до 255 значений, составляющих выборку из генеральной совокупности; допускаются числовые значения, имена и ссылки на числовые значения"}, "STDEVP": {"a": "(список_аргументов)", "d": "Статистическая функция, используется для анализа диапазона данных и возвращает стандартное отклонение по всей совокупности значений", "ad": "от 1 до 255 значений, составляющих генеральную совокупность; допускаются числовые значения и ссылки на числовые значения"}, "STDEVPA": {"a": "(список_аргументов)", "d": "Статистическая функция, используется для анализа диапазона данных и возвращает стандартное отклонение по всей совокупности значений", "ad": "от 1 до 255 значений, составляющих генеральную совокупность; допускаются числовые значения, имена, массивы и ссылки на числовые значения"}, "STEYX": {"a": "(известные_значения_y;известные_значения_x)", "d": "Статистическая функция, возвращает стандартную ошибку предсказанных значений Y для каждого значения X по регрессивной шкале", "ad": "массив или диапазон зависимых точек данных — числа, массивы или ссылки на ячейки, содержащие числа!массив или диапазон независимых точек данных — числа, массивы или ссылки на ячейки, содержащие числа"}, "TDIST": {"a": "(x;степени_свободы;хвосты)", "d": "Статистическая функция, возвращает процентные точки (вероятность) для t-распределения Стьюдента, где числовое значение (x) — вычисляемое значение t, для которого должны быть вычислены вероятности; T-распределение используется для проверки гипотез при малом объеме выборки", "ad": "числовое значение, для которого требуется вычислить распределение!целое, указывающее количество степеней свободы!число возвращаемых хвостов распределения (1 или 2)"}, "TINV": {"a": "(вероятность;степени_свободы)", "d": "Статистическая функция, возвращает двустороннее обратное t-распределения Стьюдента", "ad": "вероятность, связанная с двусторонним t-распределением Стьюдента!положительное целое число степеней свободы, характеризующее распределение"}, "T.DIST": {"a": "(x;степени_свободы;интегральная)", "d": "Статистическая функция, возвращает левостороннее t-распределение Стьюдента. T-распределение используется для проверки гипотез при малом объеме выборки. Данную функцию можно использовать вместо таблицы критических значений t-распределения", "ad": "числовое значение, для которого требуется вычислить распределение!целое, указывающее количество степеней свободы!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или функция плотности вероятности (ЛОЖЬ)"}, "T.DIST.2T": {"a": "(x;степени_свободы)", "d": "Статистическая функция, возвращает двустороннее t-распределение Стьюдента.T-распределение Стьюдента используется для проверки гипотез при малом объеме выборки. Данную функцию можно использовать вместо таблицы критических значений t-распределения", "ad": "числовое значение, для которого требуется вычислить распределение!целое, указывающее количество степеней свободы"}, "T.DIST.RT": {"a": "(x;степени_свободы)", "d": "Статистическая функция, возвращает правостороннее t-распределение Стьюдента. T-распределение используется для проверки гипотез при малом объеме выборки. Данную функцию можно применять вместо таблицы критических значений t-распределения", "ad": "числовое значение, для которого требуется вычислить распределение!целое, указывающее количество степеней свободы"}, "T.INV": {"a": "(вероятность;степени_свободы)", "d": "Статистическая функция, возвращает левостороннее обратное t-распределение Стьюдента.", "ad": "вероятность, связанная с двусторонним t-распределением Стьюдента, число от 0 до 1 включительно!положительное целое число степеней свободы, характеризующее распределение"}, "T.INV.2T": {"a": "(вероятность;степени_свободы)", "d": "Статистическая функция, возвращает двустороннее обратное t-распределение Стьюдента", "ad": "вероятность, связанная с двусторонним t-распределением Стьюдента, число от 0 до 1 включительно!положительное целое число степеней свободы, характеризующее распределение"}, "T.TEST": {"a": "(массив1;массив2;хвосты;тип)", "d": "Статистическая функция, возвращает вероятность, соответствующую t-тесту Стьюдента; функция СТЬЮДЕНТ.ТЕСТ позволяет определить вероятность того, что две выборки взяты из генеральных совокупностей, которые имеют одно и то же среднее", "ad": "первый набор данных!второй набор данных!число хвостов распределения (1 или 2)!вид t-test: парный = 1, двухпарный = 2, двухпарный с неравным отклонением = 3"}, "TREND": {"a": "(известные_значения_y; [известные_значения_x]; [новые_значения_x]; [конст])", "d": "Статистическая функция, возвращает значения вдоль линейного тренда; он подмещается к прямой линии (с использованием метода наименьших квадратов) в known_y массива и known_x", "ad": "множество значений y, для которых уже известно соотношение y = mx + b!необязательное множество значений x, для которых, возможно, уже известно соотношение y = mx + b или массив имеет тот же размер, что и известные значения y!новые значения x, для которых ТРЕНД возвращает соответствующие значения y!логическое значение: константа b вычисляется обычным образом при значении ИСТИНА или отсутствии значения и равна 0 при значении ЛОЖЬ"}, "TRIMMEAN": {"a": "(массив;доля)", "d": "Статистическая функция, возвращает среднее внутренности множества данных. УРЕЗСРЕДНЕЕ вычисляет среднее, отбрасывания заданный процент данных с экстремальными значениями; можно использовать эту функцию, чтобы исключить из анализа выбросы", "ad": "массив или диапазон усредняемых значений!дробное число точек данных, исключаемых из вычислений"}, "TTEST": {"a": "(массив1;массив2;хвосты;тип)", "d": "Статистическая функция, возвращает вероятность, соответствующую критерию Стьюдента; функция ТТЕСТ позволяет определить, вероятность того, что две выборки взяты из генеральных совокупностей, которые имеют одно и то же среднее", "ad": "первый набор данных!второй набор данных!число хвостов распределения (1 или 2)!вид t-test: парный = 1, двухпарный = 2, двухпарный с неравным отклонением = 3"}, "VAR": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и возвращает дисперсию по выборке, содержащей числа", "ad": "от 1 до 255 значений, составляющих выборку из генеральной совокупности"}, "VAR.P": {"a": "(число1;[число2]; ... )", "d": "Статистическая функция, вычисляет дисперсию для генеральной совокупности. Логические значения и текст игнорируются", "ad": "от 1 до 255 числовых аргументов, составляющих генеральную совокупность"}, "VAR.S": {"a": "(число1;[число2]; ... )", "d": "Статистическая функция, оценивает дисперсию по выборке; логические значения и текст игнорируются", "ad": "от 1 до 255 значений, составляющих выборку из генеральной совокупности"}, "VARA": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и возвращает дисперсию по выборке", "ad": "от 1 до 255 числовых аргументов, составляющих выборку из генеральной совокупности"}, "VARP": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и возвращает дисперсию по всей совокупности значений", "ad": "от 1 до 255 числовых аргументов, составляющих генеральную совокупность"}, "VARPA": {"a": "(список_аргументов)", "d": "Статистическая функция, анализирует диапазон данных и возвращает дисперсию по всей совокупности значений", "ad": "от 1 до 255 числовых аргументов, составляющих генеральную совокупность"}, "WEIBULL": {"a": "(x;альфа;бета;интегральная)", "d": "Статистическая функция, возвращает распределение Вейбулла; это распределение используется при анализе надежности, например для вычисления среднего времени наработки на отказ какого-либо устройства", "ad": "значение, для которого вычисляется функция, неотрицательное число!параметр распределения, положительное число!параметр распределения, положительное число!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ)"}, "WEIBULL.DIST": {"a": "(x;альфа;бета;интегральная)", "d": "Статистическая функция, возвращает распределение Вейбулла; это распределение используется при анализе надежности, например для вычисления среднего времени наработки на отказ какого-либо устройства", "ad": "значение, для которого вычисляется функция, неотрицательное число!параметр распределения, положительное число!параметр распределения, положительное число!логическое значение, определяющее вид функции: интегральная функция распределения (ИСТИНА) или весовая функция распределения (ЛОЖЬ)"}, "Z.TEST": {"a": "(массив;x;[сигма])", "d": "Статистическая функция, возвращает одностороннее P-значение z-теста; для заданного гипотетического среднего генеральной совокупности функция Z.TEСT возвращает вероятность того, что среднее по выборке будет больше среднего значения набора рассмотренных данных (массива), то есть среднего значения наблюдаемой выборки", "ad": "массив или диапазон, с которыми сравнивается x!проверяемое значение!известное стандартное отклонение генеральной совокупности"}, "ZTEST": {"a": "(массив;x;[сигма])", "d": "Статистическая функция, возвращает одностороннее значение вероятности z-теста; для заданного гипотетического среднего генеральной совокупности (μ0) возвращает вероятность того, что выборочное среднее будет больше среднего значения множества рассмотренных данных (массива), называемого также средним значением наблюдаемой выборки", "ad": "массив или диапазон, с которыми сравнивается x!проверяемое значение!известное стандартное отклонение генеральной совокупности"}, "ACCRINT": {"a": "(дата_выпуска;первый_доход;дата_согл;ставка;[номинал];частота;[базис])", "d": "Финансовая функция, используется для вычисления дохода по ценным бумагам с периодической выплатой процентов", "ad": "дата выпуска ценных бумаг, заданная порядковым номером!дата первой выплаты процентов по ценным бумагам, заданная порядковым номером!дата расчета за ценные бумаги, заданная порядковым номером!годовая процентная ставка для купонов по ценным бумагам!номинальная стоимость ценных бумаг!количество купонных выплат за год!используемый способ вычисления дня!логическое значение: для расчета с даты выпуска = ИСТИНА или опущено; для расчета с даты последнего купона = ЛОЖЬ"}, "ACCRINTM": {"a": "(дата_выпуска;дата_согл;ставка;[номинал];[базис])", "d": "Финансовая функция, используется для вычисления дохода по ценным бумагам, процент по которым уплачивается при наступлении срока погашения", "ad": "дата выпуска ценных бумаг, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!годовая процентная ставка для купонов по ценным бумагам!номинальная стоимость ценных бумаг!используемый способ вычисления дня"}, "AMORDEGRC": {"a": "(стоимость;дата_приобр;первый_период;остаточная_стоимость;период;ставка;[базис])", "d": "Финансовая функция, используется для вычисления величины амортизации имущества по каждому отчетному периоду методом дегрессивной амортизации", "ad": "затраты на приобретение актива!дата приобретения актива!дата окончания первого периода!остаточная стоимость в конце времени эксплуатации актива.!период!ставка амортизации!годовой базис: 0 — 360 дней, 1 — фактический, 3 — 365 дней."}, "AMORLINC": {"a": "(стоимость;дата_приобр;первый_период;остаточная_стоимость;период;ставка;[базис])", "d": "Финансовая функция, используется для вычисления величины амортизации имущества по каждому отчетному периоду методом линейной амортизации", "ad": "затраты на приобретение актива!дата приобретения актива!дата окончания первого периода!остаточная стоимость в конце времени эксплуатации актива.!период!ставка амортизации!годовой базис: 0 — 360 дней, 1 — фактический, 3 — 365 дней."}, "COUPDAYBS": {"a": "(дата_согл;дата_вступл_в_силу;частота;[базис])", "d": "Финансовая функция, используется для вычисления количества дней от начала действия купона до даты покупки ценной бумаги", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!количество купонных выплат за год!используемый способ вычисления дня"}, "COUPDAYS": {"a": "(дата_согл;дата_вступл_в_силу;частота;[базис])", "d": "Финансовая функция, используется для вычисления количества дней в периоде купона, содержащем дату покупки ценной бумаги", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!количество купонных выплат за год!используемый способ вычисления дня"}, "COUPDAYSNC": {"a": "(дата_согл;дата_вступл_в_силу;частота;[базис])", "d": "Финансовая функция, используется для вычисления количества дней от даты покупки ценной бумаги до следующей выплаты по купону", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!количество купонных выплат за год!используемый способ вычисления дня"}, "COUPNCD": {"a": "(дата_согл;дата_вступл_в_силу;частота;[базис])", "d": "Финансовая функция, используется для вычисления даты следующей выплаты по купону после даты покупки ценной бумаги", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!количество купонных выплат за год!используемый способ вычисления дня"}, "COUPNUM": {"a": "(дата_согл;дата_вступл_в_силу;частота;[базис])", "d": "Финансовая функция, используется для вычисления количества выплат процентов между датой покупки ценной бумаги и датой погашения", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!количество купонных выплат за год!используемый способ вычисления дня"}, "COUPPCD": {"a": "(дата_согл;дата_вступл_в_силу;частота;[базис])", "d": "Финансовая функция, используется для вычисления даты выплаты процентов, предшествующей дате покупки ценной бумаги", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!количество купонных выплат за год!используемый способ вычисления дня"}, "CUMIPMT": {"a": "(ставка;кол_пер;нз;нач_период;кон_период;тип)", "d": "Финансовая функция, используется для вычисления общего размера процентых выплат по инвестиции между двумя периодами времени исходя из указанной процентной ставки и постоянной периодичности платежей", "ad": "процентная ставка!общее число периодов выплат!текущая стоимость инвестиции!номер первого периода, включенного в вычисления!номер последнего периода, включенного в вычисления!выбор времени платежа"}, "CUMPRINC": {"a": "(ставка;кол_пер;нз;нач_период;кон_период;тип)", "d": "Финансовая функция, используется для вычисления общей суммы, выплачиваемой в погашение основного долга по инвестиции между двумя периодами времени исходя из указанной процентной ставки и постоянной периодичности платежей", "ad": "процентная ставка!общее число периодов выплат!текущая стоимость инвестиции!номер первого периода, включенного в вычисления!номер последнего периода, включенного в вычисления!выбор времени платежа"}, "DB": {"a": "(нач_стоимость;ост_стоимость;время_эксплуатации;период;[месяцы])", "d": "Финансовая функция, используется для вычисления величины амортизации имущества за указанный отчетный период методом фиксированного убывающего остатка", "ad": "начальная стоимость актива!остаточная стоимость актива в конце времени эксплуатации!число периодов амортизации актива (иногда называется временем нормальной эксплуатации актива)!период, для которого нужно вычислить амортизацию, в тех же единицах, что и время_эксплуатации!число месяцев в первом году. Если не указано, принимается равным 12"}, "DDB": {"a": "(нач_стоимость;ост_стоимость;время_эксплуатации;период;[коэффициент])", "d": "Финансовая функция, используется для вычисления величины амортизации имущества за указанный отчетный период методом двойного убывающего остатка", "ad": "начальная стоимость актива!остаточная стоимость в конце времени эксплуатации актива!число периодов амортизации актива (иногда называется временем нормальной эксплуатации актива)!период, для которого нужно вычислить амортизацию; должен указываться в тех же единицах, что и время_эксплуатации!коэффициент уменьшения остатка. Если коэффициент не указан, он принимается равным 2 (метод двукратного уменьшения остатка)"}, "DISC": {"a": "(дата_согл;дата_вступл_в_силу;цена;погашение;[базис])", "d": "Финансовая функция, используется для вычисления ставки дисконтирования по ценной бумаге", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!цена за 100 рублей номинальной стоимости ценных бумаг!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!используемый способ вычисления дня"}, "DOLLARDE": {"a": "(дроб_руб;дроб)", "d": "Финансовая функция, преобразует цену в рублях, представленную в виде дроби, в цену в рублях, выраженную десятичным числом", "ad": "число, выраженное в виде дроби!целое число для использования в знаменателе дроби"}, "DOLLARFR": {"a": "(дес_руб;дроб)", "d": "Финансовая функция, преобразует цену в рублях, представленную десятичным числом, в цену в рублях, выраженную в виде дроби", "ad": "десятичное число!целое число для использования в знаменателе дроби"}, "DURATION": {"a": "(дата_согл;дата_вступл_в_силу;купон;доход;частота;[базис])", "d": "Финансовая функция, используется для вычисления продолжительности Маколея (взвешенного среднего срока погашения) для ценной бумаги с предполагаемой номинальной стоимостью 100 рублей", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!годовая купонная ставка ценных бумаг!годовая доходность ценных бумаг!количество купонных выплат за год!используемый способ вычисления дня"}, "EFFECT": {"a": "(номинальная_ставка;кол_пер)", "d": "Финансовая функция, используется для вычисления эффективной (фактической) годовой процентной ставки по ценной бумаге исходя из указанной номинальной годовой процентной ставки и количества периодов в году, за которые начисляются сложные проценты", "ad": "номинальная процентная ставка!количество периодов в году, за которые начисляются сложные проценты"}, "FV": {"a": "(ставка;кпер;плт;[пс];[тип])", "d": "Финансовая функция, вычисляет будущую стоимость инвестиции исходя из заданной процентной ставки и постоянной периодичности платежей", "ad": "процентная ставка за период. Например, при годовой процентной ставке в 6% для квартальной ставки используйте значение 6%/4!общее число периодов выплат инвестиции!выплата, производимая в каждый период и не меняющаяся за все время выплаты!приведенная (нынешняя) стоимость, то есть общая сумма, которая на настоящий момент равноценна ряду будущих выплат. Если значение не указано, оно принимается равным 0!значение 0 или 1, обозначающее, должна ли выплата производиться в начале периода (1) или же в его конце (0 или отсутствие значения)"}, "FVSCHEDULE": {"a": "(первичное;план)", "d": "Финансовая функция, используется для вычисления будущей стоимости инвестиций на основании ряда непостоянных процентных ставок", "ad": "стоимость инвестиции на текущий момент!массив применяемых процентных ставок"}, "INTRATE": {"a": "(дата_согл;дата_вступл_в_силу;инвестиция;погашение;[базис])", "d": "Финансовая функция, используется для вычисления ставки доходности по полностью обеспеченной ценной бумаге, проценты по которой уплачиваются только при наступлении срока погашения", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!сумма, инвестированная в ценные бумаги!сумма, которая должна быть получена при погашении ценных бумаг!используемый способ вычисления дня"}, "IPMT": {"a": "(ставка;период;кпер;пс;[бс];[тип])", "d": "Финансовая функция, используется для вычисления суммы платежей по процентам для инвестиции исходя из указанной процентной ставки и постоянной периодичности платежей", "ad": "процентная ставка за период. Например, при годовой процентной ставке в 6% используйте для квартальной процентной ставки значение 6%/4!период, для которого нужно определить сумму выплаты; должен быть в диапазоне от 1 до кпер!общее число периодов выплат инвестиции!приведенная (нынешняя) стоимость, то есть общая сумма, равноценная на данный момент ряду будущих выплат!будущая стоимость, то есть баланс, которого нужно достичь после последней выплаты. Если значение не указано, оно принимается равным 0!логическое значение, указывающее, должен ли платеж выполняться в конце периода (0 или отсутствие значения) или же в его начале (1)"}, "IRR": {"a": "(значения;[предположения])", "d": "Финансовая функция, используется для вычисления внутренней ставки доходности по ряду периодических потоков денежных средств", "ad": "массив или ссылка на ячейки, содержащие числа, по которым нужно вычислить внутреннюю ставку доходности!предполагаемая величина, близкая к результату ВСД; если не указана, принимается равной 0,1 (10 процентов)"}, "ISPMT": {"a": "(ставка;период;кпер;пс)", "d": "Финансовая функция, используется для вычисления процентов, выплачиваемых за определенный инвестиционный период, исходя из постоянной периодичности платежей", "ad": "процентная ставка за период. Например, при годовой процентной ставке в 6% используйте для квартальной процентной ставки значение 6%/4!период, для которого нужно определить процент!общее число периодов выплат инвестиции!приведенная (нынешняя) стоимость, то есть общая сумма, равноценная на данный момент ряду будущих выплат"}, "MDURATION": {"a": "(дата_согл;дата_вступл_в_силу;купон;доход;частота;[базис])", "d": "Финансовая функция, используется для вычисления модифицированной продолжительности Маколея (взвешенного среднего срока погашения) для ценной бумаги с предполагаемой номинальной стоимостью 100 рублей", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!годовая купонная ставка ценных бумаг!годовая доходность ценных бумаг!количество купонных выплат за год!используемый способ вычисления дня"}, "MIRR": {"a": "(значения;ставка_финанс;ставка_реинвест)", "d": "Финансовая функция, используется для вычисления модифицированной внутренней ставки доходности по ряду периодических денежных потоков", "ad": "массив или ссылка на ячейки, содержащие числа, которые представляют серии платежей (отрицательные числа) и доходов (положительные числа), приходящихся на одинаковые по продолжительности периоды!процентная ставка, выплачиваемая за средства, которые находятся в обороте!процентная ставка, получаемая за средства, которые находятся в обороте, при реинвестировании"}, "NOMINAL": {"a": "(эффект_ставка;кол_пер)", "d": "Финансовая функция, используется для вычисления номинальной годовой процентной ставки по ценной бумаге исходя из указанной эффективной (фактической) годовой процентной ставки и количества периодов в году, за которые начисляются сложные проценты", "ad": "фактическая процентная ставка!количество периодов в году, за которые начисляются сложные проценты"}, "NPER": {"a": "(ставка;плт;пс;[бс];[тип])", "d": "Финансовая функция, вычисляет количество периодов выплаты для инвестиции исходя из заданной процентной ставки и постоянной периодичности платежей", "ad": "процентная ставка за период. Например, при годовой процентной ставке в 6% для квартальной ставки используйте значение 6%/4!выплата, производимая в каждый период; не может изменяться в течение времени выплаты инвестиции!приведенная (нынешняя) стоимость, то есть общая сумма, равноценная на данный момент ряду будущих выплат!будущая стоимость, то есть баланс, которого нужно достичь после последней выплаты. Если значение не указано, оно принимается равным 0!логическое значение (0 или 1), указывающее, должна ли выплата производиться в конце периода (0 или отсутствие значения) или же в его начале периода (1)"}, "NPV": {"a": "(ставка;список аргументов)", "d": "Финансовая функция, вычисляет величину чистой приведенной стоимости инвестиции на основе заданной ставки дисконтирования", "ad": "ставка дисконтирования на один период!от 1 до 254 выплат и поступлений, равноотстоящих друг от друга по времени и происходящих в конце каждого периода"}, "ODDFPRICE": {"a": "(дата_согл;дата_вступл_в_силу;дата_выпуска;первый_купон;ставка;доход;погашение,частота;[базис])", "d": "Финансовая функция, используется для вычисления цены за 100 рублей номинальной стоимости ценной бумаги с периодической выплатой процентов в случае нерегулярной продолжительности первого периода выплаты процентов (больше или меньше остальных периодов)", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!дата выпуска ценных бумаг, заданная порядковым номером!дата первого купона для ценных бумаг, заданная порядковым номером!процентная ставка для ценных бумаг!годовая доходность ценных бумаг!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!количество купонных выплат за год!используемый способ вычисления дня"}, "ODDFYIELD": {"a": "(дата_согл;дата_вступл_в_силу;дата_выпуска;первый_купон;ставка;цена;погашение;частота;[базис])", "d": "Финансовая функция, используется для вычисления дохода по ценной бумаге с периодической выплатой процентов в случае нерегулярной продолжительности первого периода выплаты процентов (больше или меньше остальных периодов)", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!дата выпуска ценных бумаг, заданная порядковым номером!дата первого купона для ценных бумаг, заданная порядковым номером!процентная ставка для ценных бумаг!стоимость ценных бумаг!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!количество купонных выплат за год!используемый способ вычисления дня"}, "ODDLPRICE": {"a": "(дата_согл;дата_вступл_в_силу;последняя_выплата;ставка;доход;погашение;частота;[базис])", "d": "Финансовая функция, используется для вычисления цены за 100 рублей номинальной стоимости ценной бумаги с периодической выплатой процентов в случае нерегулярной продолжительности последнего периода выплаты процентов (больше или меньше остальных периодов)", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!дата последнего купона для ценных бумаг, заданная порядковым номером!процентная ставка для ценных бумаг!годовая доходность ценных бумаг!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!количество купонных выплат за год!используемый способ вычисления дня"}, "ODDLYIELD": {"a": "(дата_согл;дата_вступл_в_силу;последняя_выплата;ставка;цена;погашение;частота;[базис])", "d": "Финансовая функция, используется для вычисления дохода по ценной бумаге с периодической выплатой процентов в случае нерегулярной продолжительности последнего периода выплаты процентов (больше или меньше остальных периодов)", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!дата последнего купона для ценных бумаг, заданная порядковым номером!процентная ставка для ценных бумаг!стоимость ценных бумаг!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!количество купонных выплат за год!используемый способ вычисления дня"}, "PDURATION": {"a": "(ставка;пс;бс)", "d": "Финансовая функция, возвращает количество периодов, которые необходимы инвестиции для достижения заданного значения", "ad": "процентная ставка за период!текущая стоимость инвестиции!требуемая будущая стоимость инвестиции"}, "PMT": {"a": "(ставка;кпер;пс;[бс];[тип])", "d": "Финансовая функция, вычисляет размер периодического платежа по ссуде исходя из заданной процентной ставки и постоянной периодичности платежей", "ad": "процентная ставка за период займа. Например, при годовой процентной ставке в 6% для квартальной ставки используйте значение 6%/4!общее число периодов выплат по займу!приведенная (нынешняя) стоимость, то есть общая сумма, на настоящий момент равноценная ряду будущих выплат!будущая стоимость, то есть баланс, которого нужно достичь после последней выплаты. Если значение не указано, оно принимается равным 0!логическое значение (0 или 1), обозначающее, должна ли выплата производиться в конце периода (0 или отсутствие значения) или же в его начале (1)"}, "PPMT": {"a": "(ставка;период;кпер;пс;[бс];[тип])", "d": "Финансовая функция, используется для вычисления размера платежа в счет погашения основного долга по инвестиции исходя из заданной процентной ставки и постоянной периодичности платежей", "ad": "процентная ставка за период. Например, при годовой процентной ставке в 6% используйте для квартальной процентной ставки значение 6%/4!период; должен быть в диапазоне от 1 до кпер!общее число периодов выплат инвестиции!приведенная (нынешняя) стоимость, то есть общая сумма, равноценная на данный момент ряду будущих выплат!будущая стоимость, то есть баланс, которого нужно достичь после последней выплаты!логическое значение, указывающее, должен ли платеж выполняться в конце периода (0 или отсутствие значения) или же в его начале (1)"}, "PRICE": {"a": "(дата_согл;дата_вступл_в_силу;ставка;доход;погашение;частота;[базис])", "d": "Финансовая функция, используется для вычисления цены за 100 рублей номинальной стоимости ценной бумаги с периодической выплатой процентов", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!годовая купонная ставка ценных бумаг!годовая доходность ценных бумаг!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!количество купонных выплат за год!используемый способ вычисления дня"}, "PRICEDISC": {"a": "(дата_согл;дата_вступл_в_силу;скидка;погашение;[базис])", "d": "Финансовая функция, используется для вычисления цены за 100 рублей номинальной стоимости ценной бумаги, на которую сделана скидка", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!ставка дисконтирования ценных бумаг!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!используемый способ вычисления дня"}, "PRICEMAT": {"a": "(дата_согл;дата_вступл_в_силу;дата_выпуска;ставка;доход;[базис])", "d": "Финансовая функция, используется для вычисления цены за 100 рублей номинальной стоимости ценной бумаги, процент по которой уплачивается при наступлении срока погашения", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!дата выпуска ценных бумаг, заданная порядковым номером!процентная ставка дохода по ценным бумагам на дату выпуска!годовая доходность ценных бумаг!используемый способ вычисления дня"}, "PV": {"a": "(ставка;кпер;плт;[бс];[тип])", "d": "Финансовая функция, вычисляет текущую стоимость инвестиции исходя из заданной процентной ставки и постоянной периодичности платежей", "ad": "процентная ставка за период. Например, при годовой процентной ставке в 6% для квартальной ставки используйте значение 6%/4!общее число периодов выплат инвестиции!выплата, производимая в каждый период и не меняющаяся за все время выплаты инвестиции!будущая стоимость, то есть баланс, которого нужно достичь после последней выплаты!логическое значение (0 или 1), обозначающее, должна ли выплата производиться в конце периода (0 или отсутствие значения) или же в его начале (1)"}, "RATE": {"a": "(кпер;плт;пс;[бс];[тип];[прогноз])", "d": "Финансовая функция, используется для вычисления размера процентной ставки по инвестиции исходя из постоянной периодичности платежей", "ad": "общее число периодов выплат займа или инвестиции!выплата, производимая в каждый период и не меняющаяся за все время выплаты займа или инвестиции!приведенная (нынешняя) стоимость, то есть общая сумма, равноценная на настоящий момент ряду будущих платежей!будущая стоимость, то есть баланс, которого нужно достичь после последней выплаты (если значение не задано, оно принимается равным 0)!логическое значение (0 или 1), обозначающее, должна ли выплата производиться в конце периода (0 или отсутствие значения) или же в его начале (1)!предполагаемая величина ставки; если значение не указано, то оно принимается равным 0,1 (10%)"}, "RECEIVED": {"a": "(дата_согл;дата_вступл_в_силу;инвестиция;скидка;[базис])", "d": "Финансовая функция, используется для вычисления суммы, полученной за полностью обеспеченную ценную бумагу при наступлении срока погашения", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!сумма, инвестированная в ценные бумаги!ставка дисконтирования ценных бумаг!используемый способ вычисления дня"}, "RRI": {"a": "(кпер;пс;бс)", "d": "Финансовая функция, возвращает эквивалентную процентную ставку для роста инвестиции", "ad": "число периодов для инвестиции!текущая стоимость инвестиции!будущая стоимость инвестиции"}, "SLN": {"a": "(нач_стоимость;ост_стоимость;время_эксплуатации)", "d": "Финансовая функция, используется для вычисления величины амортизации имущества за один отчетный период линейным методом амортизационных отчислений", "ad": "начальная стоимость актива!остаточная стоимость в конце времени эксплуатации актива!число периодов амортизации актива (иногда называется временем полезной службы актива)"}, "SYD": {"a": "(нач_стоимость;ост_стоимость;время_эксплуатации;период)", "d": "Финансовая функция, используется для вычисления величины амортизации имущества за указанный отчетный период методом \"суммы годовых цифр\"", "ad": "начальная стоимость актива!остаточная стоимость в конце времени эксплуатации актива!число периодов амортизации актива (иногда называется временем полезной службы актива)!период; должен указываться в тех же единицах, что и время_эксплуатации"}, "TBILLEQ": {"a": "(дата_согл;дата_вступл_в_силу;скидка)", "d": "Финансовая функция, используется для вычисления эквивалентной доходности по казначейскому векселю", "ad": "дата расчета за казначейский вексель, заданная порядковым номером!дата погашения казначейского векселя, заданная порядковым номером!ставка дисконтирования казначейского векселя"}, "TBILLPRICE": {"a": "(дата_согл;дата_вступл_в_силу;скидка)", "d": "Финансовая функция, используется для вычисления цены на 100 рублей номинальной стоимости для казначейского векселя", "ad": "дата расчета за казначейский вексель, заданная порядковым номером!дата погашения казначейского векселя, заданная порядковым номером!ставка дисконтирования казначейского векселя"}, "TBILLYIELD": {"a": "(дата_согл;дата_вступл_в_силу;цена)", "d": "Финансовая функция, используется для вычисления доходности по казначейскому векселю", "ad": "дата расчета за казначейский вексель, заданная порядковым номером!дата погашения казначейского векселя, заданная порядковым номером!цена за 100 рублей номинальной стоимости казначейского векселя"}, "VDB": {"a": "(нач_стоимость;ост_стоимость;время_эксплуатации;нач_период;кон_период;[коэффициент];[без_переключения])", "d": "Финансовая функция, используется для вычисления величины амортизации имущества за указанный отчетный период или его часть методом двойного уменьшения остатка или иным указанным методом", "ad": "начальная стоимость актива!остаточная стоимость актива в конце времени эксплуатации!число периодов амортизации актива (иногда называется временем нормальной эксплуатации актива)!начальный период, для которого нужно вычислить амортизацию, в тех же единицах, что и время_эксплуатации!конечный период, для которого нужно вычислить амортизацию, в тех же единицах, что и время_эксплуатации!коэффициент уменьшения остатка; если не указан, принимается равным 2 (двукратное уменьшение остатка)!значение, определяющее, следует ли переключаться на использование прямолинейной амортизации, когда амортизация превышает уменьшающийся остаток: переключаться, если ЛОЖЬ или значение не указано; не переключаться, если ИСТИНА"}, "XIRR": {"a": "(значения;даты[;предположение])", "d": "Финансовая функция, используется для вычисления внутренней ставки доходности по ряду нерегулярных денежных потоков", "ad": "ряд денежных потоков, которой соответствует графику платежей, приведенному в аргументе \"даты\"!расписание дат платежей, соответствующее ряду денежных потоков!предполагаемое значение результата функции ЧИСТВНДОХ"}, "XNPV": {"a": "(ставка;значения;даты)", "d": "Финансовая функция, используется для вычисления чистой приведенной стоимости инвестиции исходя из указанной процентной ставки и нерегулярных выплат", "ad": "ставка дисконтирования, применяемая к денежным потокам!ряд денежных потоков, который соответствует графику платежей, приведенному в аргументе \"даты\"!расписание дат платежей, соответствующее ряду денежных потоков"}, "YIELD": {"a": "(дата_согл;дата_вступл_в_силу;ставка;цена;погашение;частота;[базис])", "d": "Финансовая функция, используется для вычисления доходности по ценной бумаге с периодической выплатой процентов", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!годовая купонная ставка ценных бумаг!цена ценных бумаг за 100 рублей номинальной стоимости!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!количество купонных выплат за год!используемый способ вычисления дня"}, "YIELDDISC": {"a": "(дата_согл;дата_вступл_в_силу;цена;погашение;[базис])", "d": "Финансовая функция, используется для вычисления годовой доходности по ценной бумаге, на которую дается скидка", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!цена за 100 рублей номинальной стоимости ценных бумаг!выкупная стоимость ценных бумаг за 100 рублей номинальной стоимости!используемый способ вычисления дня"}, "YIELDMAT": {"a": "(дата_согл;дата_вступл_в_силу;дата_выпуска;ставка;цена;[базис])", "d": "Финансовая функция, используется для вычисления годовой доходности по ценным бумагам, процент по которым уплачивается при наступлении срока погашения", "ad": "дата расчета за ценные бумаги, заданная порядковым номером!дата погашения ценных бумаг, заданная порядковым номером!дата выпуска ценных бумаг, заданная порядковым номером!процентная ставка дохода по ценным бумагам на дату выпуска!цена за 100 рублей номинальной стоимости ценных бумаг!используемый способ вычисления дня"}, "ABS": {"a": "(x)", "d": "Математическая и тригонометрическая функция, используется для нахождения модуля (абсолютной величины) числа", "ad": "действительное число, абсолютную величину которого требуется найти"}, "ACOS": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает арккосинус числа", "ad": "косинус искомого угла (значение в интервале от  -1 до 1)"}, "ACOSH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический арккосинус числа", "ad": "Любое действительное число, большее или равное 1"}, "ACOT": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает главное значение арккотангенса, или обратного котангенса, числа", "ad": "котангенс искомого угла"}, "ACOTH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический арккотангенс числа", "ad": "гиперболический котангенс искомого угла"}, "AGGREGATE": {"a": "(номер_функции;параметры;ссылка1;[ссылка2];… )", "d": "Математическая и тригонометрическая функция, возвращает агрегатный результат вычислений по списку или базе данных; с помощью этой функции можно применять различные агрегатные функции к списку или базе данных с возможностью пропускать скрытые строки и значения ошибок", "ad": "число от 1 до 19, которое указывает используемую функцию сведения.!число от 0 до 7, которое указывает значения, пропускаемые при сведении!массив или диапазон числовых данных для вычисления сводного значения!указывает позицию в массиве; k-е наибольшее, k-е наименьшее, k-й процентиль или k-й квартиль.!число от 1 до 19, которое указывает используемую функцию сведения.!число от 0 до 7, которое указывает значения, пропускаемые при сведении!от 1 до 253 диапазонов или ссылок, для которых требуется вычислить сводное значение"}, "ARABIC": {"a": "(x)", "d": "Математическая и тригонометрическая функция, преобразует римское число в арабское", "ad": "римское число, которое требуется преобразовать"}, "ASC": {"a": "(текст)", "d": "Текстовая функция, для языков с двухбайтовой кодировкой (DBCS) преобразует полноширинные (двухбайтовые) знаки в полуширинные (однобайтовые)", "ad": "текст, который необходимо изменить"}, "ASIN": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает арксинус числа", "ad": "синус искомого угла (значение в диапазоне от -1 до 1)"}, "ASINH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический арксинус числа", "ad": "любое действительное число, большее или равное 1"}, "ATAN": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает арктангенс числа", "ad": "Тангенс искомого угла"}, "ATAN2": {"a": "(x;y)", "d": "Математическая и тригонометрическая функция, возвращает арктангенс координат x и y", "ad": "координата X точки!координата Y точки"}, "ATANH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический арктангенс числа", "ad": "любое действительное число в диапазоне от -1 до 1, исключая -1 и 1"}, "BASE": {"a": "(число;основание;[минимальная_длина])", "d": "Преобразует число в текстовое представление с указанным основанием системы счисления", "ad": "число, которое требуется преобразовать!основание системы счисления, в которую требуется преобразовать число!минимальная длина возвращаемой строки (если этот параметр опущен, нули в начале не добавляются)"}, "CEILING": {"a": "(x;точность)", "d": "Математическая и тригонометрическая функция, используется, чтобы округлить число в большую сторону до ближайшего числа, кратного заданной значимости", "ad": "округляемое значение!кратное, до которого требуется округлить"}, "CEILING.MATH": {"a": "(x;[точность];[мода])", "d": "Математическая и тригонометрическая функция, округляет число до ближайшего целого или до ближайшего кратного заданной значимости", "ad": "округляемое значение!кратное, до которого требуется округлить!если этот параметр задан и отличен от нуля, функция округляет число в направлении от нуля"}, "CEILING.PRECISE": {"a": "(x;[точность])", "d": "Математическая и тригонометрическая функция, округляет число вверх до ближайшего целого или до ближайшего кратного указанному значению", "ad": "округляемое значение!кратное, до которого требуется округлить"}, "COMBIN": {"a": "(число;число_выбранных)", "d": "Математическая и тригонометрическая функция, возвращает количество комбинаций для заданного числа элементов", "ad": "число элементов!число элементов в каждой комбинации"}, "COMBINA": {"a": "(число;число_выбранных)", "d": "Математическая и тригонометрическая функция, возвращает количество комбинаций (с повторениями) для заданного числа элементов", "ad": "общее число элементов!число элементов в каждой комбинации"}, "COS": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает косинус угла", "ad": "угол в радианах, косинус которого требуется определить"}, "COSH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический косинус числа", "ad": "любое действительное число"}, "COT": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает значение котангенса заданного угла в радианах", "ad": "угол в радианах, для которого требуется найти котангенс"}, "COTH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический котангенс числа", "ad": "угол в радианах, для которого требуется найти гиперболический котангенс"}, "CSC": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает косеканс угла.", "ad": "угол в радианах, для которого требуется найти косеканс"}, "CSCH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический косеканс угла", "ad": "угол в радианах, для которого требуется найти гиперболический косеканс"}, "DECIMAL": {"a": "(текст;основание)", "d": "Преобразует текстовое представление числа с указанным основанием в десятичное число", "ad": "число, которое требуется преобразовать!основание системы счисления преобразуемого числа"}, "DEGREES": {"a": "(угол)", "d": "Математическая и тригонометрическая функция, преобразует радианы в градусы", "ad": "угол в радианах, преобразуемый в градусы"}, "ECMA.CEILING": {"a": "(x;точность)", "d": "Математическая и тригонометрическая функция, используется, чтобы округлить число в большую сторону до ближайшего числа, кратного заданной значимости", "ad": "округляемое значение!кратное, до которого требуется округлить"}, "EVEN": {"a": "(x)", "d": "Математическая и тригонометрическая функция, используется, чтобы округлить число до ближайшего четного целого числа", "ad": "округляемое значение"}, "EXP": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает значение константы e, возведенной в заданную степень. Константа e равна 2,71828182845904", "ad": "степень, в которую возводится основание e. Величина e, основание натурального логарифма, приблизительно равна 2.71828182845904"}, "FACT": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает факториал числа", "ad": "неотрицательное число, факториал которого вычисляется"}, "FACTDOUBLE": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает двойной факториал числа", "ad": "число, для которого требуется вычислить двойной факториал"}, "FLOOR": {"a": "(x;точность)", "d": "Математическая и тригонометрическая функция, используется, чтобы округлить число в меньшую сторону до ближайшего числа, кратного заданной значимости", "ad": "округляемое числовое значение!кратное, до которого требуется округлить. Оба значения должны иметь одинаковый знак"}, "FLOOR.PRECISE": {"a": "(x;[точность])", "d": "Математическая и тригонометрическая функция, возвращает число, округленное с недостатком до ближайшего целого или до ближайшего кратного разрядности", "ad": "округляемое значение!кратное, до которого требуется округлить"}, "FLOOR.MATH": {"a": "(x;[точность];[мода])", "d": "Математическая и тригонометрическая функция, округляет число в меньшую сторону до ближайшего целого или до ближайшего кратного указанному значению", "ad": "округляемое значение!кратное, до которого требуется округлить!если этот параметр задан и отличен от нуля, функция округляет в направлении к нулю"}, "GCD": {"a": "(список_аргументов)", "d": "Математическая и тригонометрическая функция, возвращает наибольший общий делитель для двух и более чисел", "ad": "от 1 до 255 значений"}, "INT": {"a": "(x)", "d": "Математическая и тригонометрическая функция, анализирует и возвращает целую часть заданного числа", "ad": "действительное число, округляемое до ближайшего меньшего целого"}, "ISO.CEILING": {"a": "(число;[точность])", "d": "Округляет число вверх до ближайшего целого или до ближайшего кратного указанному значению вне зависимости от его знака; если в качестве точности указан нуль, возвращается нуль", "ad": "округляемое значение!кратное, до которого требуется округлить"}, "LCM": {"a": "(список_аргументов)", "d": "Математическая и тригонометрическая функция, возвращает наименьшее общее кратное для одного или более чисел", "ad": "от 1 до 255 значений, для которых определяется наименьшее общее кратное"}, "LN": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает натуральный логарифм числа", "ad": "положительное действительное число, для которого вычисляется натуральный логарифм"}, "LOG": {"a": "(x;[основание])", "d": "Математическая и тригонометрическая функция, возвращает логарифм числа по заданному основанию", "ad": "положительное действительное число, для которого вычисляется логарифм!основание логарифма; 10 если опущено"}, "LOG10": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает логарифм числа по основанию 10", "ad": "положительное действительное число, для которого вычисляется десятичный логарифм"}, "MDETERM": {"a": "(массив)", "d": "Математическая и тригонометрическая функция, возвращает определитель матрицы (матрица хранится в массиве)", "ad": "числовой массив с равным количеством строк и столбцов, диапазон ячеек или массив"}, "MINVERSE": {"a": "(массив)", "d": "Математическая и тригонометрическая функция, возвращает обратную матрицу для заданной матрицы и отображает первое значение возвращаемого массива чисел", "ad": "числовой массив с равным количеством строк и столбцов, либо диапазон или массив"}, "MMULT": {"a": "(массив1;массив2)", "d": "Математическая и тригонометрическая функция, возвращает матричное произведение двух массивов и отображает первое значение из возвращаемого массива чисел", "ad": "первый из перемножаемых массивов, число столбцов в нем должно равняться числу строк во втором массиве"}, "MOD": {"a": "(x;y)", "d": "Математическая и тригонометрическая функция, возвращает остаток от деления числа на заданный делитель", "ad": "число, остаток от деления которого определяется!число, на которое нужно разделить (делитель)"}, "MROUND": {"a": "(x;точность)", "d": "Математическая и тригонометрическая функция, используется, чтобы округлить число до кратного заданной значимости", "ad": "округляемое значение!точность, с которой требуется округлить число"}, "MULTINOMIAL": {"a": "(список_аргументов)", "d": "Математическая и тригонометрическая функция, возвращает отношение факториала суммы значений к произведению факториалов", "ad": "от 1 до 255 значений"}, "MUNIT": {"a": "(размерность)", "d": "Математическая и тригонометрическая функция, возвращает матрицу единиц для указанного измерения", "ad": "целое число, задающее размер единичной матрицы, которую требуется получить"}, "ODD": {"a": "(x)", "d": "Математическая и тригонометрическая функция, используется, чтобы округлить число до ближайшего нечетного целого числа", "ad": "округляемое значение"}, "PI": {"a": "()", "d": "Математическая и тригонометрическая функция, возвращает математическую константу пи, равную 3,14159265358979. Функция не требует аргумента", "ad": ""}, "POWER": {"a": "(x;y)", "d": "Математическая и тригонометрическая функция, возвращает результат возведения числа в заданную степень", "ad": "номер основания - любое действительное число!показатель степени, в которую возводится основание"}, "PRODUCT": {"a": "(список_аргументов)", "d": "Математическая и тригонометрическая функция, перемножает все числа в заданном диапазоне ячеек и возвращает произведение", "ad": "от 1 до 255 перемножаемых чисел, логических значений или чисел, представленных в текстовом виде"}, "QUOTIENT": {"a": "(числитель;знаменатель)", "d": "Математическая и тригонометрическая функция, возвращает целую часть результата деления с остатком", "ad": "делимое!делитель"}, "RADIANS": {"a": "(угол)", "d": "Математическая и тригонометрическая функция, преобразует градусы в радианы", "ad": "угол в градусах, который нужно преобразовать"}, "RAND": {"a": "()", "d": "Математическая и тригонометрическая функция, возвращает случайное число, которое больше или равно 0 и меньше 1. Функция не требует аргумента", "ad": ""}, "RANDARRAY": {"a": "([строки];[столбцы];[минимум];[максимум];[целое_число])", "d": "Математическая и тригонометрическая функция, возвращает массив случайных чисел", "ad": "количество строк в возвращаемом массиве!количество столбцов в возвращенном массиве!минимальное число_ которое должно возвратиться!максимальное число, которое должно возвратиться!возвращают целое или десятичное число. TRUE — для целого, FALSE — для десятичного числа"}, "RANDBETWEEN": {"a": "(нижн_граница;верхн_граница)", "d": "Математическая и тригонометрическая функция, возвращает случайное число, большее или равное значению аргумента нижн_граница и меньшее или равное значению аргумента верхн_граница", "ad": "наименьшее целое число, которое возвращает функция СЛУЧМЕЖДУ!наибольшее целое число, которое возвращает функция СЛУЧМЕЖДУ"}, "ROMAN": {"a": "(число;[форма])", "d": "Математическая и тригонометрическая функция, преобразует число в римское", "ad": "число в арабской записи, которое требуется преобразовать!число, указывающее желаемый тип числа в римской записи."}, "ROUND": {"a": "(x;число_разрядов)", "d": "Математическая и тригонометрическая функция, округляет число до заданного количества десятичных разрядов", "ad": "округляемое число!количество десятичных разрядов, до которого нужно округлить число. Отрицательные значения вызывают округление целой части, ноль - округление до ближайшего целого числа"}, "ROUNDDOWN": {"a": "(x;число_разрядов)", "d": "Математическая и тригонометрическая функция, округляет число в меньшую сторону до заданного количества десятичных разрядов", "ad": "любое действительное число, которое нужно округлить!количество разрядов, до которого округляется число. Отрицательные значения вызывают округление целой части, ноль или отсутствие значения - до ближайшего целого числа"}, "ROUNDUP": {"a": "(x;число_разрядов)", "d": "Математическая и тригонометрическая функция, округляет число в большую сторону до заданного количества десятичных разрядов", "ad": "любое действительное число, которое нужно округлить!количество разрядов, до которого округляется число. Отрицательные значения вызывают округление целой части, ноль или отсутствие значения - до ближайшего целого числа"}, "SEC": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает секанс угла", "ad": "угол в радианах, для которого требуется найти секанс"}, "SECH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический секанс угла", "ad": "угол в радианах, для которого требуется найти гиперболический секанс"}, "SERIESSUM": {"a": "(переменная;показатель_степени;шаг;коэффициенты)", "d": "Математическая и тригонометрическая функция, возвращает сумму степенного ряда", "ad": "переменная степенного ряда!показатель степени x для первого элемента степенного ряда!шаг увеличения n для каждого следующего элемента степенного ряда!набор коэффициентов при соответствующих степенях x"}, "SIGN": {"a": "(x)", "d": "Математическая и тригонометрическая функция, определяет знак числа. Если число положительное, функция возвращает значение 1. Если число отрицательное, функция возвращает значение -1. Если число равно 0, функция возвращает значение 0", "ad": "любое действительное число"}, "SIN": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает синус угла", "ad": "угол в радианах, синус которого требуется определить. Градусы*ПИ()/180=радианы"}, "SINH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический синус числа", "ad": "любое действительное число"}, "SQRT": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает квадратный корень числа", "ad": "число, для которого вычисляется квадратный корень"}, "SQRTPI": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает квадратный корень от результата умножения константы пи (3,14159265358979) на заданное число", "ad": "число, которое умножается на число пи"}, "SUBTOTAL": {"a": "(номер_функции;список_аргументов)", "d": "Возвращает промежуточный итог в список или базу данных", "ad": "число от 1 до 11, которое указывает, какую функцию следует использовать при вычислении промежуточных итогов!от 1 до 254 диапазонов или ссылок, для которых требуется вычислить промежуточные итоги"}, "SUM": {"a": "(список_аргументов)", "d": "Математическая и тригонометрическая функция, возвращает результат сложения всех чисел в выбранном диапазоне ячеек", "ad": "от 1 до 255 аргументов, которые суммируются. Логические и текстовые значения игнорируются"}, "SUMIF": {"a": "(диапазон;условие;[диапазон_суммирования])", "d": "Математическая и тригонометрическая функция, суммирует все числа в выбранном диапазоне ячеек в соответствии с заданным условием и возвращает результат", "ad": "диапазон проверяемых ячеек!условие в форме числа, выражения или текста, определяющее суммируемые ячейки!фактические ячейки для суммирования. Если диапазон суммирования не указан, будут использоваться ячейки, задаваемые параметром 'диапазон'"}, "SUMIFS": {"a": "(диапазон_суммирования;диапазон_условия1;условие1;[диапазон_условия2;условие2]; ... )", "d": "Математическая и тригонометрическая функция, суммирует все числа в выбранном диапазоне ячеек в соответствии с несколькими условиями и возвращает результат", "ad": "фактически суммируемые ячейки!диапазон ячеек, проверяемых на соответствие определенному условию!условие в форме числа, выражения или текста, определяющее суммируемые ячейки"}, "SUMPRODUCT": {"a": "(список_аргументов)", "d": "Математическая и тригонометрическая функция, перемножает соответствующие элементы заданных диапазонов ячеек или массивов и возвращает сумму произведений", "ad": "от 2 до 255 массивов, соответствующие компоненты которых нужно сначала перемножить, а затем сложить полученные произведения. Все массивы должны иметь одинаковую размерность"}, "SUMSQ": {"a": "(список_аргументов)", "d": "Математическая и тригонометрическая функция, вычисляет сумму квадратов чисел и возвращает результат", "ad": "от 1 до 255 чисел, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, имен или ссылок на массивы, для которых вычисляется сумма квадратов"}, "SUMX2MY2": {"a": "(массив-1;массив-2)", "d": "Математическая и тригонометрическая функция, вычисляет сумму разностей квадратов соответствующих элементов в двух массивах", "ad": "первый диапазон или массив - число, имя, массив или ссылка на диапазон с числами!второй диапазон или массив - число, имя, массив или ссылка на диапазон с числами"}, "SUMX2PY2": {"a": "(массив-1;массив-2)", "d": "Математическая и тригонометрическая функция, вычисляет суммы квадратов соответствующих элементов в двух массивах и возвращает сумму полученных результатов", "ad": "первый массив или диапазон - число, имя, массив или ссылка на диапазон с числами!второй массив или диапазон - число, имя, массив или ссылка на диапазон с числами"}, "SUMXMY2": {"a": "(массив-1;массив-2)", "d": "Математическая и тригонометрическая функция, возвращает сумму квадратов разностей соответствующих элементов в двух массивах", "ad": "первый диапазон или массив - число, имя, массив или ссылка на диапазон с числами!второй диапазон или массив - число, имя, массив или ссылка на диапазон с числами"}, "TAN": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает тангенс угла", "ad": "угол в радианах, тангенс которого требуется определить. Градусы * ПИ()/180 = радианы"}, "TANH": {"a": "(x)", "d": "Математическая и тригонометрическая функция, возвращает гиперболический тангенс числа", "ad": "любое действительное число"}, "TRUNC": {"a": "(x;[число_разрядов])", "d": "Математическая и тригонометрическая функция, возвращает число, усеченное до заданного количества десятичных разрядов", "ad": "усекаемое число!число, определяющее точность усечения. По умолчанию используется значение 0"}, "ADDRESS": {"a": "(номер_строки;номер_столбца;[тип_ссылки];[a1];[имя_листа])", "d": "Поисковая функция, возвращает адрес ячейки, представленный в виде текста", "ad": "Номер строки, используемый в ссылке ячейки; для строки 1 номер строки равен 1!Номер столбца, используемый в ссылке ячейки; для столбца D номер столбца равен 4!Задание типа возвращаемой ссылки (типы ссылки пронумерованы от 1 до 4)!логическое значение, определяющее стиль ссылок: A1 (1 или ИСТИНА) или R1C1 (0 или ЛОЖЬ)!строка, задающая имя листа, который используется как внешняя ссылка"}, "CHOOSE": {"a": "(номер_индекса;список_аргументов)", "d": "Поисковая функция, возвращает значение из списка значений по заданному индексу (позиции)", "ad": "указывает, какой аргумент должен быть выбран. Допустимые значения: число от 1 до 254, ссылка на число от 1 до 254 или формула, результатом которой является число от 1 до 254!от 1 до 254 чисел, ссылок на ячейки, определенных имен, формул, функций или текстовых аргументов, из которых производится выбор"}, "COLUMN": {"a": "([ссылка])", "d": "Поисковая функция, возвращает номер столбца ячейки", "ad": "ячейка или диапазон, для которых определяется номер столбца. Если опущено, ячейка содержит функцию столбца"}, "COLUMNS": {"a": "(массив)", "d": "Поисковая функция, возвращает количество столбцов в ссылке на ячейки", "ad": "массив либо формула, возвращающая массив, либо ссылка на диапазон, в котором определяется количество столбцов"}, "FORMULATEXT": {"a": "(ссылка)", "d": "Поисковая функция, возвращает формулу в виде строки", "ad": "ссылка на формулу"}, "HLOOKUP": {"a": "(искомое_значение;таблица;номер_строки;[интервальный_просмотр])", "d": "Поисковая функция, используется для выполнения горизонтального поиска значения в верхней строке таблицы или массива и возвращает значение, которое находится в том же самом столбце в строке с заданным номером", "ad": "значение, которое требуется найти в первой строке таблицы. Искомое_значение может быть значением, ссылкой или текстовой строкой!таблица с текстом, числами или логическими значениями, в которой производится поиск данных; может быть ссылкой или именем диапазона!номер строки в таблице, из которой должно быть возвращено сопоставляемое значение. Первая строка значений таблицы имеет номер 1!логическое значение, определяющее, точно (ЛОЖЬ) или приближенно (ИСТИНА или отсутствие значения) должен производиться поиск в верхней строке (отсортированной по возрастанию)"}, "HYPERLINK": {"a": "(адрес;[имя])", "d": "Поисковая функция, создает ярлык, который позволяет перейти к другому месту в текущей книге или открыть документ, расположенный на сетевом сервере, в интрасети или в Интернете", "ad": "путь и имя файла документа (полный путь, адрес UNC или URL).!текст или число, отображаемое в ячейке. Если этот параметр не задан, в ячейке отображается значение параметра 'адрес'"}, "INDEX": {"a": "(массив;номер_строки;[номер_столбца]) ИНДЕКС(ссылка;номер_строки;[номер_столбца];[номер_области])", "d": "Поисковая функция, возвращает значение в диапазоне ячеек на основании заданных номера строки и номера столбца. Существуют две формы функции ИНДЕКС", "ad": "диапазон ячеек или константа массива.!строка в массиве, из которой нужно возвращать значение; если опущена, требуется указание номера столбца!столбец в массиве, из которого нужно возвращать значение; если опущен - требуется указание номера строки!ссылка на один или несколько диапазонов!строка в массиве, из которой нужно возвращать значение; если опущена, требуется указание номера столбца!столбец в массиве, из которого нужно возвращать значение; если опущен - требуется указание номера строки!диапазон ссылки, из которого надо возвращать значения. Первая выделенная или введенная область - это область 1, вторая - область 2 и т.д."}, "INDIRECT": {"a": "(ссылка_на_текст;[a1])", "d": "Поисковая функция, возвращает ссылку на ячейку, указанную с помощью текстовой строки", "ad": "ссылка на ячейку, которая содержит либо ссылку в стиле А1, либо ссылку в стиле R1C1, либо имя, определенное как ссылка, либо ссылку на ячейку в виде текстовой строки!логическое значение, указывающее, какого типа ссылка содержится в ячейке, задаваемой аргументом 'ссылка_на_ячейку': R1C1 = ЛОЖЬ; A1 = ИСТИНА или опущено"}, "LOOKUP": {"a": "(искомое_значение;просматриваемый_вектор;[вектор_результатов])", "d": "Поисковая функция, возвращает значение из выбранного диапазона (строки или столбца с данными, отсортированными в порядке возрастания)", "ad": "Значение, которое ПРОСМОТР ищет в векторе просмотра; значение может быть числом, текстом, логическим значением, именем или ссылкой на значение!диапазон, содержащий только одну строку или один столбец с текстом, числами или логическими значениями, расположенными в порядке возрастания!диапазон, содержащий только одну строку или один столбец того же размера, что и просматриваемый вектор!значение, которое ПРОСМОТР ищет в массиве; оно может быть числом, строкой, именем или ссылкой на значение!диапазон ячеек, содержащий текст, числа или логические значения, которые нужно сравнивать с искомым значением"}, "MATCH": {"a": "(искомое_значение;просматриваемый_массив;[тип_сопоставления])", "d": "Поисковая функция, возвращает относительное положение заданного элемента в диапазоне ячеек", "ad": "значение, используемое при поиске нужного значения в массиве - может быть числом, текстом или логическим значением, либо ссылкой на один из этих типов!непрерывный диапазон ячеек, просматриваемый в поиске искомого значения - может быть диапазоном значений или ссылкой на диапазон!число (1, 0 или -1), определяющее возвращаемое значение."}, "OFFSET": {"a": "(ссылка;смещ_по_строкам;смещ_по_столбцам;[высота];[ширина])", "d": "Поисковая функция, возвращает ссылку на ячейку, отстоящую от заданной ячейки (или верхней левой ячейки в диапазоне ячеек) на определенное число строк и столбцов", "ad": "ссылка, от которой отсчитывается смещение - ссылка на ячейку или диапазон смежных ячеек!количество строк вниз или вверх, на которое диапазон результирующей ссылки смещен относительно диапазона исходной ссылки!количество столбцов вправо или влево, на которое диапазон результирующей ссылки смещен относительно диапазона исходной ссылки!высота, в строках, диапазона результирующей ссылки; если не указана, то равна высоте диапазона исходной ссылки!ширина, в столбцах, диапазона результирующей ссылки; если не указана, то равна ширине диапазона исходной ссылки"}, "ROW": {"a": "([ссылка])", "d": "Поисковая функция, возвращает номер строки для ссылки на ячейку", "ad": "ячейка или диапазон, для которых определяется номер строки; если опущено, возвращает ячейку с функцией СТРОКА"}, "ROWS": {"a": "(массив)", "d": "Поисковая функция, возвращает количество строк в ссылке на ячейки", "ad": "массив или формула, выдающая массив, либо ссылка на диапазон, для которых определяется количество строк"}, "TRANSPOSE": {"a": "(массив)", "d": "Поисковая функция, возвращает первый элемент массива", "ad": "диа<PERSON>азон ячеек на листе или массив значений, который нужно транспонировать"}, "UNIQUE": {"a": "(массив; [by_col]; [exactly_once])", "d": "Поисковая функция, возвращает список уникальных значений в списке или диапазоне", "ad": "диапазон или массив, из которого возвращаются уникальные строки или столбцы!логическое значение: сравнение строк с другими и возврат уникальных строк (ЛОЖЬ или отсутствие значения); сравнение столбцов с другими и возврат уникальных столбцов (ИСТИНА)!логическое значение: возврат строк или столбцов, которые встречаются в массиве только один раз (ИСТИНА); возврат всех отдельных строк или столбцов в массиве (ЛОЖЬ или отсутствие значения)"}, "VLOOKUP": {"a": "(искомое_значение;таблица;номер_столбца;[интервальный_просмотр])", "d": "Поисковая функция, используется для выполнения вертикального поиска значения в крайнем левом столбце таблицы или массива и возвращает значение, которое находится в той же самой строке в столбце с заданным номером", "ad": "значение, которое должно быть найдено в первом столбце массива (значение, ссылка или строка текста)!таблица с текстом, числами или логическими значениями, в которой производится поиск данных; может быть ссылкой или именем диапазона!номер столбца в таблице, из которого нужно вернуть значение. Первый столбец значений таблицы имеет номер 1!логическое значение, определяющее, точно (ЛОЖЬ) или приближенно (ИСТИНА или отсутствие значения) должен производиться поиск в первом столбце (отсортированном по возрастанию)"}, "XLOOKUP": {"a": "(искомое_значение; просматриваемый_массив; возращаемый_массив; [если_ничего_не_найдено]; [режим_сопоставления]; [режим_поиска])", "d": "Поисковая функция, выполняет поиск в диапазоне или массиве и возвращает элемент, соответствующий первому совпадению. Если совпадения не существуют, то она может вернуть наиболее близкое (приблизительное) совпадение", "ad": "значение, которое требуется найти!массив или диапазон, в котором выполняется поиск!массив или диапазон, из которого возвращается значение!значение, которое возвращается, если совпадение не найдено!укажите, каким образом искомое_значение сопоставляется со значениями в массиве просматриваемый_массив!укажите используемый режим поиска. По умолчанию выполняется поиск с первого до последнего элемента"}, "CELL": {"a": "(info_type, [reference])", "d": "Информационная функция, возвращает сведения о форматировании, расположении или содержимом ячейки", "ad": "текстовое значение, задающее тип сведений о ячейке при возвращении!ячейка, сведения о которой требуется получить"}, "ERROR.TYPE": {"a": "(значение)", "d": "Информационная функция, возвращает числовое представление одной из существующих ошибок", "ad": "значение ошибки, для которой нужно найти код. Может быть фактическим значением ошибки или ссылкой на ячейку, содержащую ошибку"}, "ISBLANK": {"a": "(значение)", "d": "Информационная функция, проверяет, является ли ячейка пустой. Если ячейка пуста, функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ", "ad": "проверяемая ячейка или ссылающееся на ее имя"}, "ISERR": {"a": "(значение)", "d": "Информационная функция, используется для проверки на наличие значения ошибки. Если ячейка содержит значение ошибки (кроме #Н/Д), функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ", "ad": "проверяемое значение. Оно может содержать ссылку на ячейку, формулу или имя ячейки, формулы или значения"}, "ISERROR": {"a": "(значение)", "d": "Информационная функция, используется для проверки на наличие значения ошибки. Если ячейка содержит одно из следующих значений ошибки: #Н/Д, #ЗНАЧ!, #ССЫЛКА!, #ДЕЛ/0!, #ЧИСЛО!, #ИМЯ? или #ПУСТО!, функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ", "ad": "проверяемое значение. Оно может содержать ссылку на ячейку, формулу или имя ячейки, формулы или значения"}, "ISEVEN": {"a": "(число)", "d": "Информационная функция, используется для проверки на наличие четного числа. Если ячейка содержит четное число, функция возвращает значение ИСТИНА. Если число является нечетным, она возвращает значение ЛОЖЬ", "ad": "проверяемое значение"}, "ISFORMULA": {"a": "(значение)", "d": "Информационная функция, проверяет, имеется ли ссылка на ячейку с формулой, и возвращает значение ИСТИНА или ЛОЖЬ", "ad": "ссылка на ячейку, которую требуется проверить (может быть ссылкой на ячейку, формулой или именем, указывающим на ячейку)"}, "ISLOGICAL": {"a": "(значение)", "d": "Информационная функция, используется для проверки на наличие логического значения (ИСТИНА или ЛОЖЬ). Если ячейка содержит логическое значение, функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ", "ad": "проверяемое значение - ячейка, формула или имя ячейки, формулы или значения"}, "ISNA": {"a": "(значение)", "d": "Информационная функция, используется для проверки на наличие ошибки #Н/Д. Если ячейка содержит значение ошибки #Н/Д, функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ", "ad": "проверяемое значение. Значение может содержать ссылку на ячейку, формулу или имя ячейки, формулы или значения"}, "ISNONTEXT": {"a": "(значение)", "d": "Информационная функция, используется для проверки на наличие значения, которое не является текстом. Если ячейка не содержит текстового значения, функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ", "ad": "проверяемое значение: ячейка, формула или имя ячейки, формулы или значения"}, "ISNUMBER": {"a": "(значение)", "d": "Информационная функция, используется для проверки на наличие числового значения. Если ячейка содержит числовое значение, функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ", "ad": "проверяемое значение. Значение может содержать ссылку на ячейку, формулу или имя ячейки, формулы или значения"}, "ISODD": {"a": "(число)", "d": "Информационная функция, используется для проверки на наличие нечетного числа. Если ячейка содержит нечетное число, функция возвращает значение ИСТИНА. Если число является четным, она возвращает значение ЛОЖЬ", "ad": "проверяемое значение"}, "ISREF": {"a": "(значение)", "d": "Информационная функция, используется для проверки, является ли значение допустимой ссылкой на другую ячейку", "ad": "проверяемое значение. Значение может содержать ссылку на ячейку, формулу или имя ячейки, формулы или значения"}, "ISTEXT": {"a": "(значение)", "d": "Информационная функция, используется для проверки на наличие текстового значения. Если ячейка содержит текстовое значение, функция возвращает значение ИСТИНА, в противном случае функция возвращает значение ЛОЖЬ", "ad": "проверяемое значение. Значение может содержать ссылку на ячейку, формулу или имя ячейки, формулы или значения"}, "N": {"a": "(значение)", "d": "Информационная функция, преобразует значение в число", "ad": "преобразуемое значение"}, "NA": {"a": "()", "d": "Информационная функция, возвращает значение ошибки #Н/Д. Эта функция не требует аргумента", "ad": ""}, "SHEET": {"a": "(значение)", "d": "Информационная функция, возвращает номер листа, на который имеется ссылка", "ad": "имя листа или ссылка на лист, номер которого требуется получить (если этот параметр опущен, возвращается номер листа, содержащего функцию)"}, "SHEETS": {"a": "(ссылка)", "d": "Информационная функция, возвращает количество листов в ссылке", "ad": "ссылка, для которой требуется определить число содержащихся в ней листов (если этот параметр опущен, возвращается число листов в книге, содержащей функцию)"}, "TYPE": {"a": "(значение)", "d": "Информационная функция, используется для определения типа результирующего или отображаемого значения", "ad": "любое допустимое значение"}, "AND": {"a": "(логическое_значение1;[логическое_значение2]; ...)", "d": "Логическая функция, используется для проверки, является ли введенное логическое значение истинным или ложным. Функция возвращает значение ИСТИНА, если все аргументы имеют значение ИСТИНА", "ad": "от 1 до 255 проверяемых условий, которые могут принимать значение ИСТИНА либо ЛОЖЬ; они могут быть логическими значениями, массивами или ссылками"}, "FALSE": {"a": "()", "d": "Логическая функция, возвращает значение ЛОЖЬ и не требует аргумента", "ad": ""}, "IF": {"a": "(лог_выражение;значение_если_истина;[значение_если_ложь])", "d": "Логическая функция, используется для проверки логического выражения и возвращает одно значение, если проверяемое условие имеет значение ИСТИНА, и другое, если оно имеет значение ЛОЖЬ", "ad": "любое значение или выражение, которое при вычислении дает значение ИСТИНА или ЛОЖЬ!значение, которое возвращается, если 'лог_выражение' имеет значение ИСТИНА. Если не указано, возвращается значение ИСТИНА. Допустимая глубина вложенности - семь!значение, которое возвращается, если 'лог_выражение' имеет значение ЛОЖЬ. Если не указано, возвращается значение ЛОЖЬ"}, "IFS": {"a": "(условие1;значение1;[условие2;значение2]; … )", "d": "Логическая функция, проверяет соответствие одному или нескольким условиям и возвращает значение для первого условия, принимающего значение ИСТИНА", "ad": "Должно быть значением или выражением, которое можно оценить как ИСТИНА или ЛОЖЬ!Значение возвращается, если в результате логической проверки будет получено значение ИСТИНА"}, "IFERROR": {"a": "(значение;значение_если_ошибка)", "d": "Логическая функция, используется для проверки формулы на наличие ошибок в первом аргументе. Функция возвращает результат формулы, если ошибки нет, или определенное значение, если она есть", "ad": "любое значение, выражение или ссылка!любое значение, выражение или ссылка"}, "IFNA": {"a": "(значение;значение_при_ошибке)", "d": "Логическая функция, возвращает указанное вами значение, если формула возвращает значение ошибки #Н/Д; в ином случае возвращает результат формулы.", "ad": "любое значение, выражение или ссылка!любое значение, выражение или ссылка"}, "NOT": {"a": "(логическое_значение)", "d": "Логическая функция, используется для проверки, является ли введенное логическое значение истинным или ложным. Функция возвращает значение ИСТИНА, если аргумент имеет значение ЛОЖЬ, и ЛОЖЬ, если аргумент имеет значение ИСТИНА", "ad": "значение или выражение, которое может принимать значение либо ИСТИНА, либо ЛОЖЬ"}, "OR": {"a": "(логическое_значение1;[логическое значение2]; ... )", "d": "Логическая функция, используется для проверки, является ли введенное логическое значение истинным или ложным. Функция возвращает значение ЛОЖЬ, если все аргументы имеют значение ЛОЖЬ", "ad": "от 1 до 255 проверяемых условий, которые могут принимать значение ИСТИНА либо ЛОЖЬ"}, "SWITCH": {"a": "(выражение;значение1;результат1;[по_умолчанию или значение2;результат2];…[по_умолчанию или значение3;результат3])", "d": "Логическая функция, вычисляет значение (которое называют выражением) на основе списка значений и возвращает результат, соответствующий первому совпадающему значению; если совпадения не обнаружены, может быть возвращено необязательное стандартное значение", "ad": "выражение для оценки!значение для сравнения с выражением! результат возвращается, если соответствующее значение соответствует выражению"}, "TRUE": {"a": "()", "d": "Логическая функция, возвращает значение ИСТИНА и не требует аргумента", "ad": ""}, "XOR": {"a": "(логическое_значение1;[логическое значение2]; ... )", "d": "Логическая функция, возвращает логическое исключающее ИЛИ всех аргументов", "ad": "от 1 до 254 проверяемых условий, которые могут принимать значения ИСТИНА или ЛОЖЬ и могут быть логическими значениями, массивами или ссылками"}, "TEXTBEFORE": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Возвращает текст перед символами-разделителями.", "ad": "Текст, который вы хотите найти по разделителю.!Символ или строка для использования в качестве разделителя.!Желаемая встречаемость разделителя. По умолчанию 1. Отрицательное число ищет с конца.!Ищет в тексте сопоставление разделителя. По умолчанию выполняется сопоставление с учетом регистра.!Сопоставлять ли разделитель с концом текста. По умолчанию они не сопоставляются.!Возвращается, если сопоставлений не найдено. По умолчанию возвращается #Н/Д."}, "TEXTAFTER": {"a": "(text, delimiter, [instance_num], [match_mode], [match_end], [if_not_found])", "d": "Возвращает текст после символов-разделителей.", "ad": "Текст, который вы хотите найти по разделителю.!Символ или строка для использования в качестве разделителя.!Желаемая встречаемость разделителя. По умолчанию 1. Отрицательное число ищет с конца!Ищет в тексте сопоставление разделителя. По умолчанию выполняется совпадение с учетом регистра.!Сопоставлять ли разделитель с концом текста. По умолчанию они не сопоставляются.!Возвращается, если совпадений не найдено.По умолчанию возвращается #Н/Д."}, "TEXTSPLIT": {"a": "(text, col_delimiter, [row_delimiter], [ignore_empty], [match_mode], [pad_with])", "d": "Разбивает текст на строки или столбцы с помощью разделителей.", "ad": "Текст для разделения!Символ или строка для разделения столбцов.!Символ или строка для разделения строк.!Игнорировать ли пустые ячейки. По умолчанию FALSE.!Ищет в тексте совпадение разделителя. По умолчанию учитывается совпадение с учетом регистра.!Значение, используемое для заполнения. По умолчанию используется #N/A."}, "WRAPROWS": {"a": "(vector, wrap_count, [pad_with])", "d": "Переносит вектор строки или столбца после указанного числа значений.", "ad": "Вектор или ссылка для переноса.!Максимальное число значений в строке.!Значение для заполнения. По умолчанию — #N/A."}, "VSTACK": {"a": "(array1, [array2], ...)", "d": "Вертикально собирает массивы в один массив.", "ad": "Массив или ссылка для расположения стопкой."}, "HSTACK": {"a": "(array1, [array2], ...)", "d": "Горизонтально собирает массивы в один массив.", "ad": "Массив или ссылка для расположения стопкой."}, "CHOOSEROWS": {"a": "(array, row_num1, [row_num2], ...)", "d": "Возвращает строки из массива или ссылки.", "ad": "Массив или ссылка, содержащие возвращаемые строки.!Номер возвращаемой строки."}, "CHOOSECOLS": {"a": "(array, col_num1, [col_num2], ...)", "d": "Возвращает столбцы из массива или ссылки.", "ad": "Массив или ссылка, содержащие возвращаемые столбцы.!Номер возвращаемого столбца."}, "TOCOL": {"a": "(array, [ignore], [scan_by_column])", "d": "Возвращает массив в виде одного столбца.", "ad": "Массив или ссылка, возвращаемые как столбец.!Следует ли игнорировать определенные типы значений. По умолчанию значения не игнорируются.!Сканирование массива по столбцам. По умолчанию массив сканируется по строкам."}, "TOROW": {"a": "(мас<PERSON>ив, [игнорировать], [сканировать_по_столбцам])", "d": "Возвращает массив в виде одной строки.", "ad": "Массив или ссылка, возвращаемые как строка.!Следует ли игнорировать определенные типы значений. По умолчанию значения не игнорируются.!Сканирование массива по столбцам. По умолчанию массив сканируется по строкам."}, "WRAPCOLS": {"a": "(vector, wrap_count, [pad_with])", "d": "Переносит вектор строки или столбца после указанного числа значений.", "ad": "Вектор или ссылка для переноса.!Максимальное число значений в столбце.!Значение для заполнения. По умолчанию — #N/A."}, "TAKE": {"a": "(array, rows, [columns])", "d": "Возвращает строки или столбцы из начала или конца массива.", "ad": "Мас<PERSON>и<PERSON>, из которого будут приниматься строки или столбцы.!Количество принимаемых строк. Отрицательное значение принимается с конца массива.!Количество принимаемых столбцов. Отрицательное значение принимается с конца массива."}, "DROP": {"a": "(array, rows, [columns])", "d": "Удаляет строки или столбцы из начала или конца массива.", "ad": "Массив для удаления строк или столбцов.!Количество удаляемых строк. Отрицательное значение удаляется с конца массива.!Количество удаляемых столбцов. Отрицательное значение удаляется с конца массива."}, "SEQUENCE": {"a": "(строки, [столбцы], [начало], [шаг])", "d": "Возвращает последовательность чисел.", "ad": "количество возвращаемых строк!количество возвращаемых столбцов!первое число в последовательности!величина приращения для получения каждого последующего значения в последовательности"}, "EXPAND": {"a": "(мас<PERSON>ив, строки, [столбцы], [заполняющее_значение])", "d": "Развертывает массив до указанных размеров.", "ad": "Массив для развертывания.!Количество строк в развернутом массиве. Если значение отсутствует, строки не будут развернуты.!Количество столбцов в развернутом массиве. Если значение отсутствует, столбцы не будут развернуты.!Значение, используемое для заполнения. Значение по умолчанию: #Н/Д."}, "XMATCH": {"a": "(искомое_значение, просматриваемый_массив, [режим_сопоставления], [режим_поиска])", "d": "Возвращает относительное положение элемента в массиве. По умолчанию требуется точное совпадение", "ad": " — значение, которое требуется найти! — массив или диапазон, в котором выполняется поиск!укажите, каким образом искомое_значение сопоставляется со значениями в массиве просматриваемый_массив!укажите используемый режим поиска. По умолчанию выполняется поиск с первого до последнего элемента"}, "FILTER": {"a": "(массив, включить, [если_пусто])", "d": "Фильтрация диапазона или массива", "ad": "диапазон или массив для фильтрации!массив логических значений, где значение ИСТИНА представляет сохраняемые строку или столбец!возвращается в случае, если ничего не сохранено"}, "ARRAYTOTEXT": {"a": "(массив, [формат])", "d": "Возвращает текстовое представление массива", "ad": "массив для представления в виде текста!формат текста"}, "SORT": {"a": "(массив, [индекс_сортировки], [порядок_сортировки], [по_столбцу])", "d": "Сортирует диапазон или массив", "ad": "диапазон или массив для сортировки!число, указывающее номер строки или столбца, по которым должна быть выполнена сортировка!число, указывающее порядок сортировки: 1 — по возрастанию (по умолчанию), -1 — по убыванию!логическое значение, указывающее направление сортировки: FALSE — по строке (по умолчанию), TRUE — по столбцу"}, "SORTBY": {"a": "(ма<PERSON><PERSON>и<PERSON>, ключевой_массив, [порядок_сортировки], ...)", "d": "Сортирует диапазон или массив на основе значений в сопоставленном с ним диапазоне или массиве", "ad": "сортируемый диапазон или массив!диапазон или массив, по которому выполняется сортировка!число, указывающее требуемый порядок сортировки: 1 — по возрастанию (по умолчанию), -1 — по убыванию"}, "GETPIVOTDATA": {"a": "(поле_данных; сводная_таблица; [поле]; [элемент]; ...)", "d": "Извлекает данные, хранящиеся в сводной таблице.", "ad": "имя поля данных, из которого следует извлечь данные!ссылка на ячейку или диапазон ячеек сводной таблицы, где содержатся данные, которые нужно извлечь!поле!элемент поля"}, "IMPORTRANGE": {"a": "(url_таблицы, диапазон)", "d": "Импортирует диапазон ячеек из одной электронной таблицы в другую.", "ad": "URL таблицы, из которой импортируются данные!диапазон, который нужно импортировать"}}