{"DATE": {"a": "(ano; mês; dia)", "d": "Devolve o número que representa a data no código de data e hora", "ad": "é um número entre 1900 ou 1904 (dependendo do sistema de data do livro) e 9999!é um número de 1 a 12 que representa o mês do ano!é um número de 1 a 31 que representa o dia do mês"}, "DATEDIF": {"a": "(data_inicial; data_final; unidade)", "d": "Calcula o número de dias, meses ou anos entre duas datas", "ad": "Uma data que representa a primeira ou a data de início de um determinado período!Uma data que representa a última data ou a data final do período de tempo!O tipo de informação que pretende que sejam devolvidas"}, "DATEVALUE": {"a": "(texto_data)", "d": "Converte uma data em forma de texto para um número que representa a data no código de data e hora", "ad": "é o texto que representa uma data num formato de datas do Spreadsheet Editor, entre 1/1/1900 ou 1/1/1904 (dependendo do sistema de data do livro) e 12/31/9999"}, "DAY": {"a": "(núm_série)", "d": "Devolve o dia do mês, um número entre 1 e 31", "ad": "é um número no código de data e hora utilizado pelo Spreadsheet Editor"}, "DAYS": {"a": "(data_de_fim; data_de_in<PERSON>cio)", "d": "Devolve o número de dias entre duas datas.", "ad": "data_de_início e data_de_final são as duas datas entre as quais quer saber o número de dias!data_de_início e data_de_fim são as duas datas entre as quais quer saber o número de dias"}, "DAYS360": {"a": "(data_inicial; data_final; [método])", "d": "Devolve o número de dias decorridos entre duas datas, com base num ano de 360 dias (doze meses de 30 dias)", "ad": "data_início e data_fim são as duas datas cujo intervalo em dias deseja saber!Data_início e data_fim são as duas datas cujo intervalo em dias deseja saber!é um valor lógico que especifica o método de cálculo: E.U.A. (NASD) = FALSE ou omisso; europeu = TRUE."}, "EDATE": {"a": "(data_inicial; meses)", "d": "Devolve o número de série da data que é o número indicador dos meses antes ou depois da data inicial", "ad": "é o número de série de data que representa a data inicial!é o número de meses antes ou depois de data_inicial"}, "EOMONTH": {"a": "(data_inicial; meses)", "d": "Devolve o número de série do último dia do mês antes ou depois de um dado número de meses", "ad": "é o número de série de data que representa a data inicial!é o número de meses antes ou depois de data_inicial"}, "HOUR": {"a": "(núm_série)", "d": "Devolve a hora como um número de 0 (00:00) a 23 (23:00).", "ad": "é um número no código de data e hora utilizado pelo Spreadsheet Editor ou texto em formato de hora, tal como 16:48:00 ou 4:48:00 PM"}, "ISOWEEKNUM": {"a": "(data)", "d": "Devolve o número de semana ISO do ano de uma determinada data", "ad": "é o código data-hora utilizado pelo Spreadsheet Editor para calcular a data e hora"}, "MINUTE": {"a": "(núm_série)", "d": "Devol<PERSON> os minutos, um número de 0 a 59.", "ad": "é um número no código de data e hora utilizado pelo Spreadsheet Editor ou texto em formato de hora, tal como 16:48:00 ou 4:48:00 PM"}, "MONTH": {"a": "(núm_série)", "d": "<PERSON><PERSON><PERSON> o mês, um número de 1 (janeiro) a 12 (de<PERSON><PERSON><PERSON>).", "ad": "é um número no código de data e hora utilizado pelo Spreadsheet Editor"}, "NETWORKDAYS": {"a": "(data_inicial; data_final; [feriados])", "d": "Devolve o número de dias úteis entre duas datas", "ad": "é o número de série de data que representa a data inicial!é o número de série de data que representa a data final!é uma matriz opcional de um ou mais números de série de datas a eliminar do calendário de trabalho, como feriados municipais, nacionais e móveis"}, "NETWORKDAYS.INTL": {"a": "(data_inicial; data_final; [fim_de_semana]; [feriados])", "d": "Devolve o número de dias úteis completos entre duas datas com parâmetros de fim de semana personalizados", "ad": "é um número de série de data que representa a data inicial!é um número de série de data que representa a data final!é um número ou cadeia que especifica quando ocorrem os fins de semana!é um conjunto opcional de um ou mais números de série de data a excluir do calendário de trabalho, como feriados municipais, nacionais e móveis"}, "NOW": {"a": "()", "d": "Devolve a data e hora atuais com o formato de data e hora.", "ad": ""}, "SECOND": {"a": "(núm_série)", "d": "Devolve os segundos, um número de 0 a 59.", "ad": "é um número no código de data e hora utilizado pelo Spreadsheet Editor ou texto em formato de hora, tal como 16:48:23 ou 4:48:47 PM"}, "TIME": {"a": "(horas; minutos; segundos)", "d": "Converte horas, minutos e segundos, correspondentes a números, num número de série, com um formato de hora", "ad": "é um número de 0 a 23, que representa as horas!é um número de 0 a 59, que representa os minutos!é um número de 0 a 59, que representa os segundos"}, "TIMEVALUE": {"a": "(texto_hora)", "d": "Converte uma hora de texto num número de série para uma hora, um número de 0 (00:00:00) a 0,999988426 (23:59:59). Formate o número com um formato de hora depois de introduzir a fórmula", "ad": "é uma cadeia de texto que dá as horas em qualquer dos formatos de hora do Spreadsheet Editor (as informações de data na cadeia é ignorada)"}, "TODAY": {"a": "()", "d": "Devolve a data atual formatada como uma data.", "ad": ""}, "WEEKDAY": {"a": "(núm_série; [tipo_devolvido])", "d": "Devolve um número de 1 a 7 identificando o dia da semana.", "ad": "é um número que representa uma data!é um número: para Domingo=1 a Sábado=7, utilize 1; para Segunda=1 a Domingo=7, utilize 2; para Segunda=0 a Domingo=6, utilize 3"}, "WEEKNUM": {"a": "(núm_série; [tipo_retorno])", "d": "Devolve o número da semana no ano", "ad": "é o código de data e hora utilizado pelo Spreadsheet Editor para cálculo de data e hora!é um número (1 ou 2) que determina o tipo do valor devolvido"}, "WORKDAY": {"a": "(data_inicial; dias; [feriados])", "d": "Devolve o número de série da data antes ou depois de um dado número de dias úteis", "ad": "é o número de série de data que representa a data inicial!é o número de dias úteis antes ou depois de data_inicial!é uma matriz opcional de um ou mais números de série de datas a eliminar do calendário de trabalho, como feriados municipais, nacionais e móveis"}, "WORKDAY.INTL": {"a": "(data_inicial; dias; [fim_de_semana]; [feriados])", "d": "Devolve o número de série da data anterior ou posterior a um número específico de dias úteis com parâmetros de fim de semana personalizados", "ad": "é um número de série de data que representa a data inicial!é o número de dias que não correspondem a fins de semana ou feriados antes ou depois da data_inicial!é um número ou cadeia que especifica quando ocorrem os fins de semana!é uma matriz opcional de um ou mais números de série de data a excluir do calendário de trabalho, como feriados municipais, nacionais e móveis"}, "YEAR": {"a": "(núm_série)", "d": "Devolve o ano de uma data, um número inteiro do intervalo 1900-9999.", "ad": "é um número no código de data e hora utilizado pelo Spreadsheet Editor"}, "YEARFRAC": {"a": "(data_inicial; data_final; [base])", "d": "Devolve a fração do ano que representa o número de dias entre data_inicial e data_final", "ad": "é o número de série de data que representa a data inicial!é o número de série de data que representa a data final!é o tipo de base de contagem diária a utilizar"}, "BESSELI": {"a": "(x; n)", "d": "Devolve a função de Bessel modificada ln(x)", "ad": "é o valor em que a função será avaliada!é a ordem da função de Bessel"}, "BESSELJ": {"a": "(x; n)", "d": "Devolve a função de Bessel Jn(x)", "ad": "é o valor em que a função será avaliada!é a ordem da função de Bessel"}, "BESSELK": {"a": "(x; n)", "d": "Devolve a função de Bessel modificada Kn(x)", "ad": "é o valor em que a função será avaliada!é a ordem da função"}, "BESSELY": {"a": "(x; n)", "d": "Devolve a função de Bessel Yn(x)", "ad": "é o valor em que a função será avaliada!é a ordem da função"}, "BIN2DEC": {"a": "(núm)", "d": "Converte um número binário em decimal", "ad": "é o número binário a converter"}, "BIN2HEX": {"a": "(núm; [casas])", "d": "Converte um número binário em hexadecimal", "ad": "é o número binário a converter!é o número de carateres a utilizar"}, "BIN2OCT": {"a": "(núm; [casas])", "d": "Converte um número binário em octal", "ad": "é o número binário a converter!é o número de carateres a utilizar"}, "BITAND": {"a": "(número1; número2)", "d": "Devolve um \"E\" bit-a-bit de dois números", "ad": "é a representação decimal do número binário que quer calcular!é a representação decimal do número binário que quer calcular"}, "BITLSHIFT": {"a": "(númer<PERSON>; valor_da_deslocação)", "d": "Devolve um número deslocado à esquerda por valor_da_deslocação bits", "ad": "é a representação decimal do número binário que deseja calcular!é o número de bits pelos quais quer deslocar o Número à esquerda"}, "BITOR": {"a": "(número1; número2)", "d": "Devolve um \"Ou\" bit-a-bit de dois números", "ad": "é a representação decimal do número binário que quer calcular!é a representação decimal do número binário que quer calcular"}, "BITRSHIFT": {"a": "(númer<PERSON>; valor_da_deslocação)", "d": "Devolve um número deslocado à direita por valor_da_deslocação bits", "ad": "é a representação decimal do número binário que deseja calcular!é o número de bits pelos quais quer deslocar o Número à direita"}, "BITXOR": {"a": "(número1; número2)", "d": "Devolve um \"Ou Exclusivo\" bit-a-bit de dois números", "ad": "é a representação decimal do número binário que quer calcular!é a representação decimal do número binário que quer calcular"}, "COMPLEX": {"a": "(núm_real; i_núm; [sufixo])", "d": "Converte coeficientes reais e imaginários num número complexo", "ad": "é o coeficiente real do número complexo!é o coeficiente imaginário do número complexo!é o sufixo para o componente imaginário do número complexo"}, "CONVERT": {"a": "(núm; de_unidade; para_unidade)", "d": "Converte um número de um sistema de medida para outro", "ad": "é o valor de de_unidades a converter!é a unidade do número!é a unidade do resultado"}, "DEC2BIN": {"a": "(núm; [casas])", "d": "Converte um número decimal em binário", "ad": "é o decimal inteiro a converter!é o número de carateres a utilizar"}, "DEC2HEX": {"a": "(núm; [casas])", "d": "Converte um número decimal em hexadecimal", "ad": "é o decimal inteiro a converter!é o número de carateres a utilizar"}, "DEC2OCT": {"a": "(núm; [casas])", "d": "Converte um número decimal em octal", "ad": "é o decimal inteiro a converter!é o número de carateres a utilizar"}, "DELTA": {"a": "(núm1; [núm2])", "d": "Testa se dois números são iguais", "ad": "é o primeiro número!é o segundo número"}, "ERF": {"a": "(limite_inferior; [limite_superior])", "d": "Devolve a função de erro", "ad": "é o limite inferior da integração de FUNCERRO!é o limite superior na integração de FUNERRO"}, "ERF.PRECISE": {"a": "(X)", "d": "Devolve a função de erro", "ad": "é o limite inferior da integração de FUNCERRO.PRECISO"}, "ERFC": {"a": "(x)", "d": "Devolve a função complementar de erro", "ad": "é o limite inferior da integração de FUNCERRO"}, "ERFC.PRECISE": {"a": "(X)", "d": "Devolve a função complementar de erro", "ad": "é o limite inferior da integração de FUNCERROCOMPL.PRECISO"}, "GESTEP": {"a": "(núm; [passo])", "d": "Testa se um número é maior do que um valor limite", "ad": "é o valor a testar passo a passo!é o valor limite"}, "HEX2BIN": {"a": "(núm; [casas])", "d": "Converte um número hexadecimal em binário", "ad": "é o número hexadecimal a converter!é o número de carateres a utilizar"}, "HEX2DEC": {"a": "(núm)", "d": "Converte um número hexadecimal em decimal", "ad": "é o número hexadecimal a converter"}, "HEX2OCT": {"a": "(núm; [casas])", "d": "Converte um número hexadecimal em octal", "ad": "é o número hexadecimal a converter!é o número de carateres a utilizar"}, "IMABS": {"a": "(inúm)", "d": "Devolve o valor absoluto (módulo) de um número complexo", "ad": "é o número complexo cujo valor absoluto se deseja"}, "IMAGINARY": {"a": "(inúm)", "d": "Devolve o coeficiente imaginário de um número complexo", "ad": "é o número complexo cujo coeficiente imaginário se deseja"}, "IMARGUMENT": {"a": "(inúm)", "d": "Devolve o argumento q, um ângulo expresso em radianos", "ad": "é o número complexo cujo argumento se deseja"}, "IMCONJUGATE": {"a": "(inúm)", "d": "Devolve o conjugado complexo de um número complexo", "ad": "é o número complexo cujo conjugado se deseja"}, "IMCOS": {"a": "(inúm)", "d": "Devolve o cosseno de um número complexo", "ad": "é o número complexo cujo cosseno se deseja"}, "IMCOSH": {"a": "(número)", "d": "Devolve o cosseno de um número complexo", "ad": "é o número complexo para o qual quer obter o cosseno"}, "IMCOT": {"a": "(número)", "d": "Devolve a cotangente de um número complexo", "ad": "é um número complexo para o qual quer obter a cotangente"}, "IMCSC": {"a": "(número)", "d": "Devolve a cossecante de um número complexo", "ad": "é um número complexo para o qual quer obter a cossecante"}, "IMCSCH": {"a": "(número)", "d": "Devolve a cossecante hiperbólica de um número complexo", "ad": "é um número complexo para o qual quer obter a cossecante hiperbólica"}, "IMDIV": {"a": "(inúm1; inúm2)", "d": "Devolve o quociente de dois números complexos", "ad": "é o numerador ou dividendo complexo!é o denominador ou divisor complexo"}, "IMEXP": {"a": "(inúm)", "d": "Devolve o exponencial de um número complexo", "ad": "é o número complexo cujo exponencial se deseja"}, "IMLN": {"a": "(inúm)", "d": "Devolve o logaritmo natural de um número complexo", "ad": "é o número complexo cujo logaritmo natural se deseja"}, "IMLOG10": {"a": "(inúm)", "d": "Devolve o logaritmo de base 10 de um número complexo", "ad": "é o número complexo cujo logaritmo de base 10 se deseja"}, "IMLOG2": {"a": "(inúm)", "d": "Devolve o logaritmo de base 2 de um número complexo", "ad": "é o número complexo cujo logaritmo de base 2 se deseja"}, "IMPOWER": {"a": "(inúm; núm)", "d": "Devolve um número complexo elevado a uma potência inteira", "ad": "é o número complexo que se deseja elevar a uma potência!é a potência para a qual se deseja elevar o número complexo"}, "IMPRODUCT": {"a": "(inúm1; [inúm2]; ...)", "d": "Devolve o produto de 1 a 255 números complexos", "ad": "Inúm1, Inúm2,... de 1 a 255 números complexos para multiplicar."}, "IMREAL": {"a": "(inúm)", "d": "Devolve o coeficiente real de um número complexo", "ad": "é o número complexo cujo coeficiente real se deseja"}, "IMSEC": {"a": "(número)", "d": "Devolve a secante de um número complexo", "ad": "é um número complexo para o qual quer obter a secante"}, "IMSECH": {"a": "(número)", "d": "Devolve a secante hiperbólica de um número complexo", "ad": "é um número complexo para o qual quer obter a secante hiperbólica"}, "IMSIN": {"a": "(inúm)", "d": "Devolve o seno de um número complexo", "ad": "é o número complexo cujo seno se deseja"}, "IMSINH": {"a": "(número)", "d": "Devolve o seno hiperbólico de um número complexo", "ad": "é o número complexo para o qual quer obter o seno hiperbólico"}, "IMSQRT": {"a": "(inúm)", "d": "Devolve a raiz quadrada de um número complexo", "ad": "é o número complexo cuja raiz quadrada se deseja"}, "IMSUB": {"a": "(inúm1; inúm2)", "d": "Devolve a diferença de dois números complexos", "ad": "é o número complexo do qual inúm2 será subtraído!é o número complexo a ser subtraído de inúm1"}, "IMSUM": {"a": "(inúm1; [inúm2]; ...)", "d": "Devolve a soma de números complexos", "ad": "de 1 a 255 números complexos para adicionar"}, "IMTAN": {"a": "(número)", "d": "Devolve a tangente de um número complexo", "ad": "é um número complexo para o qual quer obter a tangente"}, "OCT2BIN": {"a": "(núm; [casas])", "d": "Converte um número octal em binário", "ad": "é o número octal a converter!é o número de carateres a utilizar"}, "OCT2DEC": {"a": "(núm)", "d": "Converte um número octal em decimal", "ad": "é o número octal a converter"}, "OCT2HEX": {"a": "(núm; [casas])", "d": "Converte um número octal em hexadecimal", "ad": "é o número octal a converter!é o número de carateres a utilizar"}, "DAVERAGE": {"a": "(base_dados; campo; critérios)", "d": "Calcula a média dos valores de uma coluna numa lista ou base de dados que cumprem as condições especificadas", "ad": "é o intervalo de células que constitui a lista ou a base de dados. Uma base de dados é uma lista de dados relacionados entre si!ou é o rótulo da coluna, entre plicas, ou um número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para uma condição"}, "DCOUNT": {"a": "(base_dados; campo; critérios)", "d": "<PERSON><PERSON> as c<PERSON><PERSON><PERSON> que contêm números no campo (coluna) de dados da base de dados que cumpre as condições especificadas", "ad": "é o intervalo de células que constitui a lista ou a base de dados. Uma base de dados é uma lista de dados relacionados entre si!ou é o rótulo da coluna, entre aspas duplas, ou um número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para uma condição"}, "DCOUNTA": {"a": "(base_dados; campo; critérios)", "d": "<PERSON><PERSON> as células não em branco de um campo (coluna) de dados da base de dados que cumpre as condições especificadas", "ad": "é o intervalo de células que constitui a lista ou a base de dados. Uma base de dados é uma lista de dados relacionados entre si!ou é o rótulo da coluna, entre aspas duplas, ou um número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para uma condição"}, "DGET": {"a": "(base_dados; campo; critérios)", "d": "Extrai de uma base de dados um único registo que cumpre as condições especificadas", "ad": "é o intervalo de células que constitui a lista ou a base de dados. Uma base de dados é uma lista de dados relacionados entre si!ou é o rótulo da coluna, entre aspas duplas, ou um número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para uma condição"}, "DMAX": {"a": "(base_dados; campo; critérios)", "d": "Devolve o maior número de um campo (coluna) de registos da base de dados que cumpre as condições especificadas.", "ad": "é o intervalo de células que constitui a lista ou a base de dados. Uma base de dados é uma lista de dados relacionados entre si!é ou um rótulo da coluna, entre aspas duplas, ou um número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para uma condição"}, "DMIN": {"a": "(base_dados; campo; critérios)", "d": "Devolve o número mais pequeno de um campo (coluna) de registos da base de dados que cumpre as condições especificadas", "ad": "é o intervalo de células que constitui a lista ou a base de dados. Uma base de dados é uma lista de dados relacionados entre si!ou é o rótulo da coluna, entre aspas duplas, ou um número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para uma condição"}, "DPRODUCT": {"a": "(base_dados; campo; critérios)", "d": "Multiplica os valores de um campo (colunas) de registos da base de dados que cumpre as condições especificadas", "ad": "é o intervalo de células que constitui a lista ou a base de dados. Uma base de dados é uma lista de dados relacionados entre si!ou é o rótulo da coluna, entre aspas duplas, ou um número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para uma condição"}, "DSTDEV": {"a": "(base_dados; campo; critérios)", "d": "Calcula o desvio-padrão a partir de uma amostra de entradas selecionadas da base de dados", "ad": "é o intervalo de células que constitui a lista ou a base de dados. Uma base de dados é uma lista de dados relacionados entre si!ou é o rótulo da coluna, entre aspas duplas, ou um número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para uma condição"}, "DSTDEVP": {"a": "(base_dados; campo; critérios)", "d": "Calcula o desvio-padrão com base na população total das entradas selecionadas da base de dados", "ad": "é o intervalo de células que constitui a lista ou a base de dados. Uma base de dados é uma lista de dados relacionados entre si!ou é o rótulo da coluna, entre aspas duplas, ou um número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para uma condição"}, "DSUM": {"a": "(base_dados; campo; critérios)", "d": "Adiciona os números de um campo (coluna) de registos da base de dados que cumpre as condições especificadas", "ad": "é o intervalo de células que constitui a lista ou a base de dados. Uma base de dados é uma lista de dados relacionados entre si!ou é o rótulo da coluna, entre aspas duplas, ou um número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para uma condição"}, "DVAR": {"a": "(base_dados; campo; critérios)", "d": "Calcula a variância a partir de uma amostra de entradas selecionadas da base de dados", "ad": "é o intervalo de células que constitui a lista ou a base de dados. Uma base de dados é uma lista de dados relacionados entre si!ou é o rótulo da coluna, entre aspas duplas, ou um número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para uma condição"}, "DVARP": {"a": "(base_dados; campo; critérios)", "d": "Calcula a variância com base na população total de entradas selecionadas da base de dados", "ad": "é o intervalo de células que constitui a lista ou a base de dados. Uma base de dados é uma lista de dados relacionados entre si!ou é o rótulo da coluna, entre aspas duplas, ou um número que representa a posição da coluna na lista!é o intervalo de células que contém as condições especificadas. O intervalo inclui um rótulo de coluna e uma célula abaixo do rótulo para uma condição"}, "CHAR": {"a": "(núm)", "d": "Devolve o caráter especificado pelo número de código, a partir do conjunto de carateres do computador", "ad": "é um número entre 1 e 255 que especifica o caráter desejado"}, "CLEAN": {"a": "(texto)", "d": "Remove todos os carateres do texto que não é possível imprimir", "ad": "é qualquer informação da folha de cálculo de onde deseja remover os carateres que não é possível imprimir"}, "CODE": {"a": "(texto)", "d": "Devolve um código numérico para o primeiro caráter de uma cadeia de texto, utilizando o conjunto de carateres utilizado pelo computador", "ad": "é o texto para o qual deseja obter o código do primeiro caráter"}, "CONCATENATE": {"a": "(texto1; [texto2]; ...)", "d": "Junta várias cadeias de texto numa só", "ad": "são de 1 a 255 cadeias de texto a serem agrupadas numa única cadeia de texto e podem ser cadeias de texto, números ou referências de uma só célula"}, "CONCAT": {"a": "(texto1; ...)", "d": "Concatena uma lista ou intervalo de cadeias de texto", "ad": "são 1 a 254 cadeias de texto ou intervalos a juntar numa única cadeia de texto"}, "DOLLAR": {"a": "(núm; [decimais])", "d": "Converte um número em texto, utilizando o formato monetário", "ad": "é um número, uma referência a uma célula que contém um número, ou uma fórmula que gera um número!é o número de algarismos à direita da vírgula decimal. O número é arredondado de acordo com as necessidades; se omisso, Decimais = 2"}, "EXACT": {"a": "(texto1; texto2)", "d": "Compara se duas cadeias de texto são iguais e devolve VERDADEIRO ou FALSO. EXATO se for sensível às maiúsculas e minúsculas", "ad": "é a primeira cadeia de texto!é a segunda cadeia de texto"}, "FIND": {"a": "(texto_a_localizar; no_texto; [núm_inicial])", "d": "Devolve a posição de partida de uma cadeia de texto dentro de outra. LOCALIZAR é sensível a maiúsculas e minúsculas", "ad": "é o texto que deseja localizar. Utilize aspas (sem texto) para fazer a comparação com o primeiro caráter de No_texto; não são permitidos carateres universais!é o texto que contém o que deseja localizar!especifica o caráter a partir do qual será iniciada a pesquisa. O primeiro caráter em No_texto é o número 1. Se for omisso, Núm_inicial = 1"}, "FINDB": {"a": "(texto_a_localizar; no_texto; [núm_inicial])", "d": "Localiza uma cadeia de texto dentro de uma segunda cadeia de texto e devolve o número da posição inicial da primeira cadeia de texto a partir do primeiro caráter da segunda cadeia de texto, destina-se a ser utilizada com idiomas que utilizem o conjunto de carateres de byte duplo (DBCS, double-byte character set) - japonês, chinês e coreano", "ad": "é o texto que deseja localizar. Utilize aspas (sem texto) para fazer a comparação com o primeiro caráter de No_texto; não são permitidos carateres universais!é o texto que contém o que deseja localizar!especifica o caráter a partir do qual será iniciada a pesquisa. O primeiro caráter em No_texto é o número 1. Se for omisso, Núm_inicial = 1"}, "FIXED": {"a": "(núm; [decimais]; [sem_sep_milhar])", "d": "Arredonda um número para as casas decimais especificadas e devolve o resultado como texto, com ou sem separadores de milhares", "ad": "é o número que deseja arredondar e converter para texto!é o número de algarismos à direita da vírgula decimal. Se omisso, Decimais = 2!é um valor lógico: não mostrar separadores de milhares no texto devolvido = VERDADEIRO; mostrar separadores de milhares no texto devolvido = FALSO ou omisso"}, "LEFT": {"a": "(texto; [núm_caract])", "d": "Devolve o número especificado de carateres do início de uma cadeia de texto", "ad": "é a cadeia de texto que contém os carateres que deseja extrair!especifica quantos carateres ESQUERDA deve extrair; 1, se omisso"}, "LEFTB": {"a": "(texto; [núm_caract])", "d": "Devolve o primeiro caráter ou carateres numa cadeia de texto, com base no número de bytes que especificar, destina-se a ser utilizada com idiomas que utilizem o conjunto de carateres de byte duplo (DBCS, double-byte character set) - japon<PERSON><PERSON>, chinês e coreano", "ad": "é a cadeia de texto que contém os carateres que deseja extrair!especifica quantos carateres LEFTB deve extrair; 1, se omisso"}, "LEN": {"a": "(texto)", "d": "Devolve o número de carateres de uma cadeia de texto", "ad": "é o texto cujo comprimento deseja determinar. Os espaços contam como carateres"}, "LENB": {"a": "(texto)", "d": "Devolve o número de bytes utilizado para representar os carateres numa cadeia de texto, destina-se a ser utilizada com idiomas que utilizem o conjunto de carateres de byte duplo (DBCS, double-byte character set) - japonês, chinês e coreano", "ad": "é o texto cujo comprimento deseja determinar. Os espaços contam como carateres"}, "LOWER": {"a": "(texto)", "d": "Converte to<PERSON> as letras de uma cadeia de texto em minúsculas", "ad": "é o texto que deseja converter em minúsculas Os carateres em Texto que não forem letras não são alterados"}, "MID": {"a": "(texto; núm_inicial; núm_caract)", "d": "Devolve um número específico de carateres de uma cadeia de texto, com início na posição especificada", "ad": "é a cadeia de texto que contém os carateres que deseja extrair!é a posição do primeiro caráter que deseja extrair. O primeiro caráter em Texto é 1!especifica quantos carateres devem ser devolvidos de Texto"}, "MIDB": {"a": "(texto; núm_inicial; núm_caract)", "d": "Devolve um número específico de carateres de uma cadeia de texto, começando na posição que especificar, com base no número de bytes especificado, destina-se a ser utilizada com idiomas que utilizem o conjunto de carateres de byte duplo (DBCS, double-byte character set) - japon<PERSON><PERSON>, chinês e coreano", "ad": "é a cadeia de texto que contém os carateres que deseja extrair!é a posição do primeiro caráter que deseja extrair. O primeiro caráter em Texto é 1!especifica quantos carateres devem ser devolvidos de Texto"}, "NUMBERVALUE": {"a": "(texto; [separador_decimal]; [separador_grupo])", "d": "Converte o texto em números independentemente da região", "ad": "é a cadeia representante do número que quer converter!é o caráter usado como separador decimal na cadeia!é o caráter usado como separador de grupo na cadeia"}, "PROPER": {"a": "(texto)", "d": "Converte uma cadeia de texto em maiúsculas/minúsculas adequadas; a primeira letra de cada palavra para maiúsculas e todas as outras letras para minúsculas", "ad": "é o texto entre aspas, uma fórmula que devolve texto ou uma referência a uma célula que contém o texto que deseja colocar parcialmente em maiúsculas"}, "REPLACE": {"a": "(texto_antigo; núm_inicial; núm_caract; texto_novo)", "d": "Substitui parte de uma cadeia de texto por outra diferente", "ad": "é o texto em que deseja substituir alguns carateres!é a posição do caráter em Texto_antigo que deseja substituir por Texto_novo!é o número de carateres em Texto_antigo que deseja substituir!é o texto que substituirá carateres em Texto_antigo"}, "REPLACEB": {"a": "(texto_antigo; núm_inicial; núm_caract; texto_novo)", "d": "Substitui parte de uma cadeia de texto, com base no número de bytes que especificar, por uma cadeia de texto diferente, destina-se a ser utilizada com idiomas que utilizem o conjunto de carateres de byte duplo (DBCS, double-byte character set) - japon<PERSON>s, chinês e coreano", "ad": "é o texto em que deseja substituir alguns carateres!é a posição do caráter em Texto_antigo que deseja substituir por Texto_novo!é o número de carateres em Texto_antigo que deseja substituir!é o texto que substituirá carateres em Texto_antigo"}, "REPT": {"a": "(texto; núm_vezes)", "d": "Repete o texto um dado número de vezes. Utilize REPETIR para preencher uma célula com um número de ocorrências da cadeia de texto", "ad": "é o texto que deseja repetir!é um número positivo que especifica o número de vezes que texto deve ser repetido"}, "RIGHT": {"a": "(texto; [núm_caract])", "d": "Devolve o número especificado de carateres do fim de uma cadeia de texto", "ad": "é a cadeia de texto que contém os carateres que deseja extrair!especifica o número de carateres que deseja extrair, utilizando 1 se for omisso"}, "RIGHTB": {"a": "(texto; [núm_caract])", "d": "Devolve o último caráter ou carateres numa cadeia de texto, baseado no número de bytes especificados, destina-se a ser utilizada com idiomas que utilizem o conjunto de carateres de byte duplo (DBCS, double-byte character set) - japon<PERSON><PERSON>, chinês e coreano", "ad": "é a cadeia de texto que contém os carateres que deseja extrair!especifica o número de carateres que deseja extrair, utilizando 1 se for omisso"}, "SEARCH": {"a": "(texto_a_localizar; no_texto; [núm_inicial])", "d": "Devolve o número do caráter no qual é localizado pela primeira vez uma cadeia de texto específica, lida da esquerda para a direita (não distingue maiúsculas e minúsculas)", "ad": "é o texto que deseja localizar. Pode utilizar os carateres universais ? e *; utilize ~? e ~* para localizar os carateres ? e *!é o texto em que deseja procurar o Texto_proc!é o número do caráter em No_texto, a contar da esquerda, onde deseja iniciar a pesquisa. Se omisso, será utilizado 1"}, "SEARCHB": {"a": "(texto_a_localizar; no_texto; [núm_inicial])", "d": "Localiza uma cadeia de texto dentro de uma segunda cadeia de texto e devolve o número da posição de início da primeira cadeia de texto do primeiro caráter da segunda cadeia de texto, destina-se a ser utilizada com idiomas que utilizem o conjunto de carateres de byte duplo (DBCS, double-byte character set) - japonês, chinês e coreano", "ad": "é o texto que deseja localizar. Pode utilizar os carateres universais ? e *; utilize ~? e ~* para localizar os carateres ? e *!é o texto em que deseja procurar o Texto_proc!é o número do caráter em No_texto, a contar da esquerda, onde deseja iniciar a pesquisa. Se omisso, será utilizado 1"}, "SUBSTITUTE": {"a": "(texto; texto_antigo; texto_novo; [núm_ocorrência])", "d": "Substitui um texto existente por um novo numa cadeia de texto", "ad": "é o texto ou a referência a uma célula que contém o texto cujos carateres deseja substituir!é o texto existente que deseja substituir. Se as maiúsculas e minúsculas de Texto_antigo não correspondem às do texto, SUBSTITUIR não substituirá o texto!é o texto pelo qual deseja substituir Texto_antigo!especifica qual a ocorrência de Texto_antigo que deseja substituir. Se omissa, substitui todas as ocorrências de Texto_antigo"}, "T": {"a": "(valor)", "d": "Verifica se o valor é texto e devolve o texto se referir a texto ou devolve aspas (texto em branco) se não for", "ad": "é o valor a testar"}, "TEXT": {"a": "(valor; formato_texto)", "d": "Converte um valor para texto num determinado formato numérico", "ad": "é um número, uma fórmula que representa um valor numérico ou uma referência a uma célula que contém um valor numérico!é um formato numérico em forma de texto da caixa 'Categoria', no separador 'Número' da caixa de diálogo 'Formatar células'"}, "TEXTJOIN": {"a": "(delimitador; ignorar_vazio; texto1; ...)", "d": "Concatena uma lista ou intervalo de cadeias de texto através de um delimitador", "ad": "<PERSON><PERSON><PERSON> ou cadeia de carateres a introduzir entre cada item de texto!se VERDADEIRO (predefinição), ignora células vazias!são 1 a 252 cadeias de texto ou intervalos a juntar"}, "TRIM": {"a": "(texto)", "d": "Remove todos os espaços de uma cadeia de texto, à exceção de espaços simples entre palavras", "ad": "é o texto cujos espaços deseja que sejam removidos"}, "UNICHAR": {"a": "(número)", "d": "Devolve o caráter Unicode referenciado pelo valor numérico dado", "ad": "é o número Unicode que representa um caráter"}, "UNICODE": {"a": "(texto)", "d": "Devolve o número (ponto de código) correspondente ao primeiro caráter do texto", "ad": "é o caráter para o qual quer obter o valor Unicode"}, "UPPER": {"a": "(texto)", "d": "Converte uma cadeia de texto em maiúsculas", "ad": "é o texto que deseja converter para maiúsculas, uma referência ou uma cadeia de texto"}, "VALUE": {"a": "(texto)", "d": "Converte num número uma cadeia de texto que representa um número", "ad": "é o texto entre aspas ou a referência a uma célula que contém o texto que deseja converter"}, "AVEDEV": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a média aritmética dos desvios absolutos à média dos pontos de dados. Os argumentos são números ou nomes, matrizes ou referências que contêm números", "ad": "são de 1 a 255 argumentos de cujos desvios absolutos deseja obter a média"}, "AVERAGE": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a média aritmética dos argumentos, que podem ser números ou nomes, matrizes ou referências que contêm números", "ad": "são de 1 a 255 argumentos numéricos para os quais pretende obter a média"}, "AVERAGEA": {"a": "(valor1; [valor2]; ...)", "d": "Devolve a média aritmética dos argumentos, que podem ser números, nomes, matrizes ou referências, fazendo texto e FALSO = 0; VERDADEIRO = 1", "ad": "são de 1 a 255 argumentos cuja média pretende obter"}, "AVERAGEIF": {"a": "(intervalo; critérios; [intervalo_médio])", "d": "Calcula a média (média aritmética) das células especificadas por uma determinada condição ou critério", "ad": "é o intervalo de células que pretende avaliar!é a condição ou critério, sob a forma de um número, expressão ou texto, que define as células que serão utilizadas para calcular a média!são as células a utilizar para calcular a média. Se omissas, utilizam-se as células do intervalo"}, "AVERAGEIFS": {"a": "(intervalo_médio; intervalo_critérios; critérios; ...)", "d": "Calcula a média (média aritmética) das células especificadas por um determinado conjunto de condições ou critérios", "ad": "são as células a utilizar para calcular a média.!é o intervalo de células que pretende avaliar para a condição específica!é a condição ou critério, sob a forma de um número, expressão ou texto, que define as células que serão utilizadas para calcular a média"}, "BETADIST": {"a": "(x; alfa; beta; [A]; [B])", "d": "Devolve a função de densidade de probabilidade beta cumulativa", "ad": "é o valor entre A e B, no qual se avalia a função!é um parâmetro da distribuição e tem de ser maior que 0.!é um parâmetro da distribuição e tem de ser maior que 0!é um limite inferior opcional para o intervalo de x. Se omisso, A = 0!é um limite superior opcional para o intervalo de x. Se omisso, B = 1"}, "BETAINV": {"a": "(probabilidade; alfa; beta; [A]; [B])", "d": "Devolve o inverso da função de densidade de probabilidade beta cumulativa (DISTBETA)", "ad": "é a probabilidade associada à distribuição beta!é um parâmetro da distribuição e tem de ser maior que 0!é um parâmetro da distribuição e tem de ser maior que 0!é um limite inferior opcional para o intervalo de x. Se omisso, A = 0!é um limite superior opcional para o intervalo de x. Se omisso, B = 1"}, "BETA.DIST": {"a": "(x; alfa; beta; cumulativo; [A]; [B])", "d": "Devolve a função de distribuição de probabilidade beta", "ad": "é o valor entre A e B sobre o qual a função deve ser avaliada!é um parâmetro da distribuição e tem de ser maior que 0!é um parâmetro da distribuição e tem de ser maior que 0!é um valor lógico: para a função de distribuição cumulativa, utilize VERDADEIRO; para a função de densidade de probabilidade, utilize FALSO!é um limite inferior opcional para o intervalo de x. Se omisso, A = 0!é um limite superior opcional para o intervalo de x. Se omisso, B = 1"}, "BETA.INV": {"a": "(probabilidade; alfa; beta; [A]; [B])", "d": "Devolve o inverso da função de densidade de probabilidade beta cumulativa (DIST.BETA)", "ad": "é uma probabilidade associada à distribuição beta!é um parâmetro da distribuição e tem de ser maior que 0!é um parâmetro da distribuição e tem de ser maior que 0!é um limite inferior opcional para o intervalo de x. Se omisso, A = 0!é um limite superior opcional para o intervalo de x. Se omisso, B = 1"}, "BINOMDIST": {"a": "(núm_s; tentativas; probabilidade_s; cumulativo)", "d": "Devolve a probabilidade da distribuição binomial do termo individual", "ad": "é o número de tentativas bem sucedidas!é o número de tentativas independentes!é a probabilidade de sucesso em cada tentativa!é um valor lógico: para a função de distribuição cumulativa, utilize VERDADEIRO; para a função de densidade de probabilidade, utilize FALSO"}, "BINOM.DIST": {"a": "(núm_s; tentativas; probabilidade_s; cumulativo)", "d": "Devolve a probabilidade da distribuição binomial do termo individual", "ad": "é o número de tentativas bem sucedidas!é o número de tentativas independentes!é a probabilidade de sucesso em cada tentativa!é um valor lógico: para a função de distribuição cumulativa, utilize VERDADEIRO; para a função de densidade de probabilidade, utilize FALSO"}, "BINOM.DIST.RANGE": {"a": "(tentativas; probabilidade_s; número_s; [número_s2])", "d": "Devolve a probabilidade de um resultado tentado utilizando uma distribuição binomial", "ad": "é o número de tentativas independentes!é a probabilidade de sucesso de cada tentativa!é o número de sucessos nas tentativas!se fornecido, esta função devolve a probabilidade de o número de tentativas com sucesso estar entre número_s e número_s2"}, "BINOM.INV": {"a": "(tentativas; probabilidade_s; alfa)", "d": "Devolve o menor valor para o qual a distribuição binomial cumulativa é maior ou igual a um valor de critério", "ad": "é o número de tentativas de Bernoulli!é a probabilidade de sucesso em cada tentativa, um número entre 0 e 1 inclusive!é o valor do critério, um número entre 0 e 1 inclusive"}, "CHIDIST": {"a": "(x; graus_liberdade)", "d": "Devolve a probabilidade unilateral à direita da distribuição chi-quadrado", "ad": "é o valor no qual pretende avaliar a distribuição, um número não negativo!é o número de graus de liberdade, um número entre 1 e 10^10, excluindo 10^10"}, "CHIINV": {"a": "(probabilidade; graus_liberdade)", "d": "Devolve o inverso da probabilidade unilateral à direita da distribuição chi-quadrado", "ad": "é uma probabilidade associada à distribuição chi-quadrado, um valor entre 0 e 1 inclusive!é o número de graus de liberdade, um número entre 1 e 10^10, excluindo 10^10"}, "CHITEST": {"a": "(intervalo_real; intervalo_esperado)", "d": "Devolve o teste de independência: o valor da distribuição chi-quadrado para a estatística, com os graus de liberdade adequados", "ad": "é o intervalo de dados que contém observações a testar perante os valores esperados!é o intervalo de dados que contém a proporção entre o produto do total de linhas e do total de colunas e o total geral"}, "CHISQ.DIST": {"a": "(x; graus_liberdade; cumulativo)", "d": "Devolve a probabilidade unilateral à esquerda da distribuição chi-quadrado", "ad": "é o valor sobre o qual pretende avaliar a distribuição, um número não negativo!é o número de graus de liberdade, um número entre 1 e 10^10, excluindo 10^10!é um valor lógico que a função deve devolver: a função de distribuição cumulativa = VERDADEIRO; a função de densidade de probabilidade = FALSO"}, "CHISQ.DIST.RT": {"a": "(x; graus_liberdade)", "d": "Devolve a probabilidade unilateral à direita da distribuição chi-quadrado", "ad": "é o valor sobre o qual pretende avaliar a distribuição, um número não negativo!é o número de graus de liberdade, um número entre 1 e 10^10, excluindo 10^10"}, "CHISQ.INV": {"a": "(probabilidade; graus_liberdade)", "d": "Devolve o inverso da probabilidade unilateral à esquerda da distribuição chi-quadrado", "ad": "é uma probabilidade associada à distribuição chi-quadrado, um valor entre 0 e 1 inclusive!é o número de graus de liberdade, um número entre 1 e 10^10, excluindo 10^10"}, "CHISQ.INV.RT": {"a": "(probabilidade; graus_liberdade)", "d": "Devolve o inverso da probabilidade unilateral à direita da distribuição chi-quadrado", "ad": "é uma probabilidade associada à distribuição chi-quadrado, um valor entre 0 e 1 inclusive!é o número de graus de liberdade, um número entre 1 e 10^10, excluindo 10^10"}, "CHISQ.TEST": {"a": "(intervalo_real; intervalo_esperado)", "d": "Devolve o teste de independência: o valor da distribuição chi-quadrado para a estatística, com os graus de liberdade adequados", "ad": "é o intervalo de dados que contém observações a testar face aos valores esperados!é o intervalo de dados que contém a proporção entre o produto do total de linhas e do total de colunas e o total geral"}, "CONFIDENCE": {"a": "(alfa; desv_padrão; tamanho)", "d": "Devolve o intervalo de confiança para uma média da população utilizando uma distribuição normal", "ad": "é o nível de significância utilizado para calcular o nível de confiança, um número maior que 0 e menor que 1!é o desvio-padrão da população para o intervalo de dados e assume-se que é conhecido. O Desv_padrão tem de ser maior que 0!é o tamanho da amostra"}, "CONFIDENCE.NORM": {"a": "(alfa; desv_padrão; tamanho)", "d": "Devolve o intervalo de confiança para uma média da população", "ad": "é o nível de significância utilizado para calcular o nível de confiança, um número maior que 0 e menor que 1!é o desvio-padrão da população para o intervalo de dados e presume-se que é conhecido. Desv_padrão tem de ser maior que 0!é o tamanho da amostra"}, "CONFIDENCE.T": {"a": "(alfa; desv_padrão; tamanho)", "d": "Devolve o intervalo de confiança para uma média da população, utilizando uma distribuição T de Student", "ad": "é o nível de significância utilizado para calcular o nível de confiança, um número maior que 0 e menor que 1!é o desvio-padrão da população para o intervalo de dados e presume-se que é conhecido. Desv_padrão tem de ser maior que 0!é o tamanho da amostra"}, "CORREL": {"a": "(matriz1; matriz2)", "d": "Devolve o coeficiente de correlação entre dois conjuntos de dados", "ad": "é um intervalo de células de valores. Os valores podem ser números, nomes, matrizes ou referências que contenham números!é o segundo intervalo de células de valores. Os valores devem ser números, nomes, matrizes ou referências que contenham números"}, "COUNT": {"a": "(valor1; [valor2]; ...)", "d": "Conta o número de células num intervalo que contém números", "ad": "são de 1 a 255 argumentos que podem conter ou referir-se a diversos tipos de dados, mas em que só são contados os números"}, "COUNTA": {"a": "(valor1; [valor2]; ...)", "d": "Conta o número de células não em branco num intervalo", "ad": "são de 1 a 255 argumentos que representam os valores e células que pretende contar. Os valores podem conter qualquer tipo de informação"}, "COUNTBLANK": {"a": "(intervalo)", "d": "<PERSON><PERSON> as células em branco dentro de um intervalo de células especificado", "ad": "é o intervalo no qual deseja contar as c<PERSON><PERSON><PERSON> em branco"}, "COUNTIF": {"a": "(intervalo; critérios)", "d": "Conta o número de células de um intervalo que respeitam uma dada condição", "ad": "é o intervalo de células no qual deseja contar células não em branco!é a condição, sob a forma de um número, expressão ou texto, que define quais as células que serão contadas"}, "COUNTIFS": {"a": "(intervalo_critérios; critérios; ...)", "d": "Conta o número de células especificado por um determinado conjunto de condições ou critérios", "ad": "é o intervalo de células que pretende avaliar para a condição específica!é a condição, sob a forma de um número, expressão ou texto, que define quais as células que serão contadas"}, "COVAR": {"a": "(matriz1; matriz2)", "d": "Devolve a covariância, a média dos produtos de desvios para cada par de ponto de dados em dois conjuntos de dados", "ad": "é o primeiro intervalo de células de números inteiros e têm de ser números, matrizes ou referências que contenham números!é o segundo intervalo de célula de números inteiros e têm de ser números, matrizes ou referências que contenham números"}, "COVARIANCE.P": {"a": "(matriz1; matriz2)", "d": "Devolve a covariância da população, a média dos produtos dos desvios para cada par de pontos de dados em dois conjuntos de dados", "ad": "é o primeiro intervalo de células de números inteiros e têm de ser números, matrizes ou referências que contenham números!é o segundo intervalo de células de números inteiros e têm de ser números, matrizes ou referências que contenham números"}, "COVARIANCE.S": {"a": "(matriz1; matriz2)", "d": "Devolve a covariância da amostra, a média dos produtos dos desvios para cada par de pontos de dados em dois conjuntos de dados", "ad": "é o primeiro intervalo de células de números inteiros e têm de ser números, matrizes ou referências que contenham números!é o segundo intervalo de células de números inteiros e têm de ser números, matrizes ou referências que contenham números"}, "CRITBINOM": {"a": "(tentativas; probabilidade_s; alfa)", "d": "Devolve o menor valor para o qual a distribuição binomial cumulativa é maior ou igual a um valor de critério", "ad": "é o número de tentativas de Bernoulli!é a probabilidade de sucesso em cada tentativa, um número entre 0 e 1 inclusive!é o valor do critério, um número entre 0 e 1 inclusive"}, "DEVSQ": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a soma dos quadrados dos desvios de pontos de dados em relação à média da sua amostra", "ad": "são de 1 a 255 argumentos ou uma matriz ou uma referência de matriz, para os quais pretende calcular o DESVQ"}, "EXPONDIST": {"a": "(x; lambda; cumulativo)", "d": "Devolve a distribuição exponencial", "ad": "é o valor da função, um número não negativo!é o valor do parâmetro, um número positivo!é um valor lógico que a função deve devolver: a função de distribuição cumulativa = VERDADEIRO; a função de densidade da probabilidade = FALSO"}, "EXPON.DIST": {"a": "(x; lambda; cumulativo)", "d": "Devolve a distribuição exponencial", "ad": "é o valor da função, um número não negativo!é o valor do parâmetro, um número positivo!é um valor lógico que a função deve devolver: a função de distribuição cumulativa = VERDADEIRO; a função de densidade da probabilidade = FALSO"}, "FDIST": {"a": "(x; graus_liberdade1; graus_liberdade2)", "d": "Devolve a distribuição (unilateral à direita) de probabilidade F (grau de diversidade) para dois conjuntos de dados", "ad": "é o valor no qual se avalia a função, um número não negativo!é o grau de liberdade do numerador, um número entre 1 e 10^10, excluindo 10^10!é o grau de liberdade do denominador, um número entre 1 e 10^10, excluindo 10^10"}, "FINV": {"a": "(probabilidade; graus_liberdade1; graus_liberdade2)", "d": "Devolve o inverso da distribuição (unilateral à direita) de probabilidade F: se p = DISTF(x,...), então INVF(p,....) = x", "ad": "é a probabilidade associada à distribuição cumulativa F, um número entre 0 e 1 inclusive!é o grau de liberdade do numerador, um número entre 1 e 10^10, excluindo 10^10!é o grau de liberdade do denominador, um número entre 1 e 10^10, excluindo 10^10"}, "FTEST": {"a": "(matriz1; matriz2)", "d": "Devolve o resultado de um teste F, a probabilidade bicaudal de as variações de Matriz1 e Matriz2 não serem significativamente diferentes", "ad": "é a primeira matriz ou intervalo de dados e podem ser números ou nomes, matrizes ou referências que contenham números (valores em branco são ignorados)!é a segunda matriz ou intervalo de dados e podem ser números ou nomes, matrizes ou referências que contenham números (valores em branco são ignorados)"}, "F.DIST": {"a": "(x; grau_liberdade1; grau_liberdade2; cumulativo)", "d": "Devolve a distribuição (unilateral à esquerda) de probabilidade F (grau de diversidade) de dois conjuntos de dados", "ad": "é o valor sobre o qual pretende avaliar a função, um número não negativo!é o grau de liberdade do numerador, um número entre 1 e 10^10, excluindo 10^10!é o grau de liberdade do denominador, um número entre 1 e 10^10, excluindo 10^10!é um valor lógico que a função deve devolver: a função de distribuição cumulativa = VERDADEIRO; a função de densidade de probabilidade = FALSO"}, "F.DIST.RT": {"a": "(x; grau_liberdade1; grau_liberdade2)", "d": "Devolve a distribuição (unilateral à direita) de probabilidade F (grau de diversidade) de dois conjuntos de dados", "ad": "é o valor sobre o qual pretende avaliar a função, um número não negativo!é o grau de liberdade do numerador, um número entre 1 e 10^10, excluindo 10^10!é o grau de liberdade do denominador, um número entre 1 e 10^10, excluindo 10^10"}, "F.INV": {"a": "(probabilidade; grau_liberdade1; grau_liberdade2)", "d": "Devolve o inverso da distribuição (unilateral à esquerda) de probabilidade F: se p = DIST.F(x,...), então INV.F(p,...) = x", "ad": "é uma probabilidade associada à distribuição cumulativa F, um número entre 0 e 1 inclusive!é o grau de liberdade do numerador, um número entre 1 e 10^10, excluindo 10^10!é o grau de liberdade do denominador, um número entre 1 e 10^10, excluindo 10^10"}, "F.INV.RT": {"a": "(probabilidade; grau_liberdade1; grau_liberdade2)", "d": "Devolve o inverso da distribuição (unilateral à direita) de probabilidade F: se p = DIST.F.DIR(x,...), então INV.F.DIR(p,...) = x", "ad": "é uma probabilidade associada à distribuição cumulativa F, um número entre 0 e 1 inclusive!é o grau de liberdade do numerador, um número entre 1 e 10^10, excluindo 10^10!é o grau de liberdade do denominador, um número entre 1 e 10^10, excluindo 10^10"}, "F.TEST": {"a": "(matriz1; matriz2)", "d": "Devolve o resultado de um teste F, a probabilidade bicaudal de as variações de Matriz1 e Matriz2 não serem significativamente diferentes", "ad": "é a primeira matriz ou intervalo de dados e podem ser números ou nomes, matrizes ou referências que contenham números (valores em branco são ignorados)!é a segunda matriz ou intervalo de dados e podem ser números ou nomes, matrizes ou referências que contenham números (valores em branco são ignorados)"}, "FISHER": {"a": "(x)", "d": "Devolve a transformação Fisher", "ad": "é um valor numérico para o qual deseja a transformação, um número entre -1 e 1, excluindo -1 e 1"}, "FISHERINV": {"a": "(y)", "d": "Devolve o inverso da transformação Fisher: se y = FISHER(x), então FISHERINV(y) = x", "ad": "é o valor para o qual deseja executar o inverso da transformação"}, "FORECAST": {"a": "(x; val_conhecidos_y; val_conhecidos_x)", "d": "Calcula ou prevê um valor futuro ao longo de uma tendência linear ao utilizar valores existentes", "ad": "é o ponto de dados cujo valor pretende prever e tem de ser um valor numérico!é a matriz ou intervalo de dados numéricos dependente!é a matriz ou intervalo de dados numéricos independente. A variância de Val_conhecidos_x não pode ser zero"}, "FORECAST.ETS": {"a": "(data_alvo; valores; linha_cronológica; [sazonalidade]; [conclusão_dos_dados]; [agregação])", "d": "Devolve o valor previsto de uma data alvo futura específica utilizando o método de nivelamento exponencial.", "ad": "é o ponto de dados para o qual Spreadsheet Editor prevê um valor. Deve continuar o padrão de valores na linha temporal.!é a matriz ou intervalo de dados numéricos que está a prever.!é a matriz ou intervalo independente de dados numéricos. As datas na linha de tempo devem ter um passo consistente entre elas e não podem ser zero.!é um valor numérico opcional que indica o comprimento do padrão sazonal. O valor por defeito de 1 indica que a sazonalidade é detectada automaticamente.!é um valor opcional para lidar com valores em falta. O valor por defeito de 1 substitui os valores em falta por interpolação, e 0 substitui-os por zeros.!é um valor numérico opcional para agregar vários valores com o mesmo carimbo de tempo. Se estiver em branco, o Spreadsheet Editor calcula a média dos valores."}, "FORECAST.ETS.CONFINT": {"a": "(data_alvo; valores; linha_cronológica; [nível_de_confiança]; [sazonalidade]; [conclusão_dos_dados]; [agregação])", "d": "Devolve um intervalo de confiança para o valor de previsão na data alvo especificada.", "ad": "é o ponto de dados para o qual Spreadsheet Editor prevê um valor. Deve continuar o padrão de valores na linha temporal.!é a matriz ou intervalo de dados numéricos que está a prever.!é a matriz ou intervalo independente de dados numéricos. As datas na linha de tempo devem ter um passo consistente entre elas e não podem ser zero.!é um número entre 0 e 1 que mostra o nível de confiança para o intervalo de confiança calculado. O valor por defeito é .95.!é um valor numérico opcional que indica o comprimento do padrão sazonal. O valor por defeito de 1 indica que a sazonalidade é detectada automaticamente.!é um valor opcional para lidar com valores em falta. O valor por defeito de 1 substitui os valores em falta por interpolação, e 0 substitui-os por zeros.!é um valor numérico opcional para agregar vários valores com o mesmo carimbo de tempo. Se estiver em branco, o Spreadsheet Editor calcula a média dos valores."}, "FORECAST.ETS.SEASONALITY": {"a": "(valores; linha_cronológica; [conclusão_dos_dados]; [agregação])", "d": "Devolve o comprimento do padrão repetitivo que um aplicativo detecta para a série de tempo especificada.", "ad": "é a matriz ou intervalo de dados numéricos que está a prever.!é a matriz ou intervalo independente de dados numéricos. As datas na linha temporal devem ter um passo consistente entre elas e não podem ser zero.!é um valor opcional para lidar com valores em falta. O valor por defeito de 1 substitui os valores em falta por interpolação, e 0 substitui-os por zeros.!é um valor numérico opcional para agregar vários valores com o mesmo carimbo de tempo. Se estiver em branco, Spreadsheet Editor calcula a média dos valores."}, "FORECAST.ETS.STAT": {"a": "(valores; linha_cronológica; tipo_estatístico; [sazonabilidade]; [conclusão_dos_dados]; [agregação])", "d": "Devolve a estatística solicitada para a previsão.", "ad": "é a matriz ou intervalo de dados numéricos que está a prever.!é a matriz ou intervalo independente de dados numéricos. As datas na linha cronológica têm de ter um passo consistente entre elas e não podem ser zero.!é um valor entre 1 e 8 que indica que estatística Spreadsheet Editor irá devolver para a previsão calculada. !é um valor numérico opcional que indica o comprimento do padrão sazonal. O valor predefinido de 1 indica que a sazonalidade é detetada automaticamente.!é um valor opcional para processar valores em falta. O valor predefinido de 1 substitui os valores em falta por interpolação e o 0 substitui os valores por zeros.!é um valor numérico opcional para agregar múltiplos valores com o mesmo carimbo de data/hora. Se estiver em branco, o Spreadsheet Editor calcula a média dos valores."}, "FORECAST.LINEAR": {"a": "(x; val_conhecidos_y; val_conhecidos_x)", "d": "Calcula ou prevê um valor futuro ao longo de uma tendência linear ao utilizar valores existentes", "ad": "é o ponto de dados cujo valor pretende prever e tem de ser um valor numérico!é o intervalo de dados numéricos ou matriz dependente!é o intervalo de dados numéricos ou matriz independente. A variância de Val_conhecidos_x não pode ser zero"}, "FREQUENCY": {"a": "(matriz_dados; matriz_classe)", "d": "Calcula a frequência com que ocorrem valores num intervalo e devolve a matriz vertical de números com mais um elemento que Matriz_classe", "ad": "é uma matriz ou uma referência a um conjunto de valores cuja frequência deseja contar (valores em branco e texto são ignorados)!é uma matriz ou referência a intervalos nos quais deseja agrupar os valores contidos em Matriz_dados"}, "GAMMA": {"a": "(x)", "d": "Devolve o valor da função Gama", "ad": "é o valor para o qual quer calcular Gama"}, "GAMMADIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Devolve a distribuição gama.", "ad": "é o valor no qual pretende avaliar a distribuição, um número não negativo!é um parâmetro da distribuição, um número positivo!é um parâmetro da distribuição, um número positivo. Se beta = 1, DISTGAMA devolve a distribuição gama padrão!é um valor lógico: devolver a função de distribuição cumulativa = VERDADEIRO; devolver a função de densidade de probabilidade = FALSO ou omisso"}, "GAMMA.DIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Devolve a distribuição gama", "ad": "é o valor no qual pretende avaliar a distribuição, um número não negativo!é um parâmetro da distribuição, um número positivo!é um parâmetro da distribuição, um número positivo. Se beta = 1; DIST.GAMA devolve a distribuição gama padrão!é um valor lógico: devolver a função de distribuição cumulativa = VERDADEIRO; devolver a função de densidade de probabilidade = FALSO ou omisso"}, "GAMMAINV": {"a": "(probabilidade; alfa; beta)", "d": "Devolve o inverso da distribuição cumulativa gama: se p = DISTGAMA(x,...), então INVGAMA(p,...) = x", "ad": "é a probabilidade associada à distribuição gama, um número entre 0 e 1, inclusive!é um parâmetro da distribuição, um número positivo!é um parâmetro para a distribuição, um número positivo. Se Beta = 1, INVGAMA devolve o inverso da distribuição gama padrão"}, "GAMMA.INV": {"a": "(probabilidade; alfa; beta)", "d": "Devolve o inverso da distribuição cumulativa gama: se p = DIST.GAMA(x,...), então INV.GAMA(p,...) = x", "ad": "é a probabilidade associada à distribuição gama, um número entre 0 e 1, inclusive!é um parâmetro da distribuição, um número positivo!é um parâmetro da distribuição, um número positivo. Se beta = 1; INV.GAMA devolve o inverso da distribuição gama padrão"}, "GAMMALN": {"a": "(x)", "d": "Devolve o logaritmo natural da função gama", "ad": "é o valor para o qual deseja calcular LNGAMA, um número positivo"}, "GAMMALN.PRECISE": {"a": "(x)", "d": "Devolve o logaritmo natural da função gama", "ad": "é o valor para o qual deseja calcular LNGAMA.PRECISO, um número positivo"}, "GAUSS": {"a": "(x)", "d": "Devolve menos 0,5 do que a distribuição cumulativa normal padrão", "ad": "é o valor para o qual quer a distribuição"}, "GEOMEAN": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a média geométrica de uma matriz ou intervalo de dados numéricos positivos", "ad": "são de 1 a 255 números ou nomes, matrizes ou referências contendo números, cujas médias pretende calcular"}, "GROWTH": {"a": "(val_conhecidos_y; [val_conhecidos_x]; [novos_valores_x]; [constante])", "d": "Devolve números com base na tendência de crescimento exponencial correspondente a pontos de dados conhecidos", "ad": "é o conjunto de valores de y já conhecidos na relação y = b*m^x, uma matriz ou intervalo de números positivos!é um conjunto opcional de valores de x já conhecidos na relação y = b*m^x, uma matriz ou intervalo do mesmo tamanho de val_conhecidos_y!são novos valores de x para os quais pretende que CRESCIMENTO devolva os valores de y correspondentes!é um valor lógico: a constante b é calculada normalmente se Constante = VERDADEIRO; b é definido como igual a 1, se Constante = FALSO ou omitido"}, "HARMEAN": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a média harmónica de um conjunto de dados de números positivos: o recíproco da média aritmética dos recíprocos", "ad": "são de 1 a 255 números ou nomes, matrizes ou referências que contêm números para os quais deseja determinar a média harmónica"}, "HYPGEOM.DIST": {"a": "(tam_amostra; número_amostra; tam_população; número_pop; cumulativo)", "d": "Devolve a distribuição hipergeométrica", "ad": "é o número de sucessos na amostra!é o tamanho da amostra!é o número de sucessos na população!é o tamanho da população!é um valor lógico: para a função de distribuição cumulativa, utilize VERDADEIRO; para a função de densidade de probabilidade, utilize FALSO"}, "HYPGEOMDIST": {"a": "(exemplo_s; exemplo_núm; população_s; núm_população)", "d": "Devolve a distribuição hipergeométrica", "ad": "é o número de sucessos na amostra!é o tamanho da amostra!é o número de sucessos na população!é o tamanho da população"}, "INTERCEPT": {"a": "(val_conhecidos_y; val_conhecidos_x)", "d": "Calcula o ponto onde uma linha intercetará o eixo dos YY através da melhor linha de regressão desenhada com os valores conhecidos de X e Y", "ad": "é o conjunto dependente de observações ou dados e podem ser números ou nomes, matrizes ou referências que contenham números!é o conjunto independente de observações ou dados e podem ser números ou nomes, matrizes ou referências que contenham números"}, "KURT": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a curtose de um conjunto de dados", "ad": "são de 1 a 255 números ou nomes, matrizes ou referências contendo números cuja curtose deseja obter"}, "LARGE": {"a": "(matriz; k)", "d": "Devolve o n-ésimo maior valor de um conjunto de dados. Por exemplo, o quinto número maior", "ad": "é a matriz ou intervalo de dados para que deseja determinar o n-ésimo maior valor!é a posição (a partir do maior) na matriz ou intervalo de células do valor a devolver"}, "LINEST": {"a": "(val_conhecidos_y; [val_conhecidos_x]; [constante]; [estatística])", "d": "Devolve estatísticas que descrevem uma tendência linear que coincide com os dados conhecidos, baseada numa reta obtida por aplicação do método dos quadrados mínimos aos valores conhecidos", "ad": "é o conjunto de valores de y já conhecidos na relação y = mx + b!é um conjunto opcional de valores de x que poderá já conhecer da relação y = mx + b!é um valor lógico: a constante b é calculada normalmente se Constante = VERDADEIRO ou omisso; b é definido como igual a 0, se Constante = FALSO!é um valor lógico: devolve estatísticas adicionais de regressão = VERDADEIRO; devolve coeficientes de m e a constante b = FALSO ou omitido"}, "LOGEST": {"a": "(val_conhecidos_y; [val_conhecidos_x]; [constante]; [estatística])", "d": "Devolve estatísticas que descrevem uma curva exponencial que coincide com os dados conhecidos", "ad": "é o conjunto de valores de y já conhecidos na relação y = b*m^x!é um conjunto opcional de valores de x já conhecidos na relação y = b*m^x!é um valor lógico: a constante b é calculada normalmente se Constante = VERDADEIRO ou omisso; b é definido como igual a 1, se Constante = FALSO!é um valor lógico: devolve a estatística de regressão adicional = VERDADEIRO; devolve os coeficientes de m e a constante b = FALSO ou omitido"}, "LOGINV": {"a": "(probabilidade; média; desv_padrão)", "d": "Devolve o inverso da função de distribuição normal logarítmica cumulativa de x, onde ln(x) tem uma distribuição normal com parâmetros Média e Desv_padrão", "ad": "é uma probabilidade associada à distribuição normal logarítmica, um número entre 0 e 1, inclusive!é a média de ln(x)!é o desvio-padrão de ln(x), um número positivo"}, "LOGNORM.DIST": {"a": "(x; média; desv_padrão; cumulativo)", "d": "Devolve a distribuição normal logarítmica de x, em que ln(x) tem uma distribuição normal com os parâmetros Média e Desv_Padrão", "ad": "é o valor sobre o qual a função deve ser avaliada, um número positivo!é a média de ln(x)!é o desvio padrão de ln(x), um número positivo!é um valor lógico: para a função de distribuição cumulativa, utilize VERDADEIRO; para a função de densidade de probabilidade, utilize FALSO"}, "LOGNORM.INV": {"a": "(probabilidade; média; desv_padrão)", "d": "Devolve o inverso da função de distribuição normal logarítmica cumulativa de x, onde ln(x) tem uma distribuição normal com Média e Desv_padrão", "ad": "é a probabilidade associada à distribuição normal logarítmica, um número entre 0 e 1, inclusive!é a média de ln(x)!é o desvio-padrão de ln(x), um número positivo"}, "LOGNORMDIST": {"a": "(x; média; desv_padrão)", "d": "Devolve a distribuição normal logarítmica cumulativa de x, em que ln(x) tem uma distribuição normal com os parâmetros Média e Desv_padrão", "ad": "é o valor no qual se avalia a função, um número positivo!é a média de ln(x)!é o desvio-padrão de ln(x), um número positivo"}, "MAX": {"a": "(núm1; [núm2]; ...)", "d": "Devolve o valor máximo de uma lista de argumentos. Ignora os valores lógicos e texto", "ad": "são de 1 a 255 números, c<PERSON><PERSON><PERSON> vazias, valores lógicos ou números de texto para os quais deseja encontrar o valor máximo"}, "MAXA": {"a": "(valor1; [valor2]; ...)", "d": "Devolve o valor máximo de um conjunto de valores. Não ignora valores lógicos e texto", "ad": "são de 1 a 255 números, c<PERSON><PERSON><PERSON> vazias, valores lógicos ou números de texto cujo máximo deseja determinar"}, "MAXIFS": {"a": "(intervalo_máximo; intervalo_critérios; critérios; ...)", "d": "Devolve o valor máximo entre células especificadas por um determinado conjunto de condições ou critérios", "ad": "as células no qual pretende determinar o valor máximo!é o intervalo de células que pretende avaliar para a condição específica!é a condição ou critério na forma de um número, expressão ou texto que define que células serão incluídas quando determinar o valor máximo"}, "MEDIAN": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a mediana ou o número no meio de um conjunto de números indicados", "ad": "são de 1 a 255 números ou nomes, matrizes ou referências que contêm números para os quais deseja obter a mediana"}, "MIN": {"a": "(núm1; [núm2]; ...)", "d": "Devolve o valor mais pequeno de um conjunto de valores. Ignora valores lógicos e texto", "ad": "são de 1 a 255 números, c<PERSON><PERSON><PERSON> vazias, valores lógicos ou números de texto para os quais deseja encontrar o valor mínimo"}, "MINA": {"a": "(valor1; [valor2]; ...)", "d": "Devolve o valor mais pequeno contido num conjunto de valores. Não ignora valores lógicos e texto", "ad": "são de 1 a 255 números, c<PERSON><PERSON><PERSON> vazias, valores lógicos ou números de texto cujo mínimo pretende determinar"}, "MINIFS": {"a": "(intervalo_mínimo; intervalo_critérios; critérios; ...)", "d": "Devolve o valor mínimo entre células especificadas por um determinado conjunto de condições ou critérios", "ad": "as células no qual pretende determinar o valor mínimo!é o intervalo de células que pretende avaliar para a condição específica!é a condição ou critério na forma de um número, expressão ou texto que define que células serão incluídas quando determinar o valor mínimo"}, "MODE": {"a": "(núm1; [núm2]; ...)", "d": "Devolve o valor mais frequente ou repetitivo numa matriz ou intervalo de dados", "ad": "são de 1 a 255 números ou nomes, matrizes ou referências contendo números para os quais pretende calcular a moda"}, "MODE.MULT": {"a": "(número1; [número2]; ...)", "d": "Devolve uma matriz vertical dos valores que ocorrem mais frequentemente, ou repetitivos, numa matriz ou intervalo de dados. Para uma matriz horizontal, utilize =TRANSPOR(MODO.MÚLT(número1,número2,...))", "ad": "são 1 a 255 números, ou nomes, matrizes ou referências, que contêm determinados números cujo modo pretende obter"}, "MODE.SNGL": {"a": "(núm1; [núm2]; ...)", "d": "Devolve o valor mais frequente numa matriz ou intervalo de dados", "ad": "são de 1 a 255 números ou nomes, matrizes ou referências contendo números para os quais deseja calcular a moda"}, "NEGBINOM.DIST": {"a": "(núm_i; núm_s; probabilidade_s; cumulativo)", "d": "Devolve a distribuição binomial negativa, a probabilidade de Núm_i insucessos antes do Núm_s.º sucesso, com Probabilidade_s de sucesso", "ad": "é o número de falhas!é o número limite de sucessos!é a probabilidade de sucesso; um número entre 0 e 1!é um valor lógico: para a função de distribuição cumulativa, utilize VERDADEIRO; para a função de densidade de probabilidade, utilize FALSO"}, "NEGBINOMDIST": {"a": "(núm_i; núm_s; probabilidade_s)", "d": "Devolve a distribuição binomial negativa, a probabilidade de Núm_i insucessos antes de Núm_s sucessos, com Probabilidade_s probabilidades de um sucesso", "ad": "é o número de insucessos!é o número a partir do qual se considera haver sucesso!é a probabilidade de um sucesso; um número entre 0 e 1"}, "NORM.DIST": {"a": "(x; média; desv_padrão; cumulativo)", "d": "Devolve a distribuição cumulativa normal para a média e o desvio-padrão especificados", "ad": "é o valor para o qual pretende obter a distribuição!é a média aritmética da distribuição!é o desvio-padrão da distribuição, um número positivo!é um valor lógico: para a função de distribuição cumulativa, utilize VERDADEIRO; para a função de densidade de probabilidade, utilize FALSO"}, "NORMDIST": {"a": "(x; média; desv_padrão; cumulativo)", "d": "Devolve a distribuição cumulativa normal para a média e o desvio-padrão especificados", "ad": "é o valor para o qual pretende obter a distribuição!é a média aritmética da distribuição!é o desvio-padrão da distribuição, um número positivo!é um valor lógico: para a função de distribuição cumulativa, utilize VERDADEIRO; para a função de densidade de probabilidade, utilize FALSO"}, "NORM.INV": {"a": "(probabilidade; média; desv_padrão)", "d": "Devolve o inverso da distribuição cumulativa normal para a média e o desvio-padrão especificados", "ad": "é uma probabilidade correspondente à distribuição normal, um número entre 0 e 1 inclusive!é a média aritmética da distribuição!é o desvio-padrão da distribuição, um número positivo"}, "NORMINV": {"a": "(probabilidade; média; desv_padrão)", "d": "Devolve o inverso da distribuição cumulativa normal para a média e o desvio-padrão especificados", "ad": "é uma probabilidade correspondente à distribuição normal, um número entre 0 e 1 inclusive!é a média aritmética da distribuição!é o desvio-padrão da distribuição, um número positivo"}, "NORM.S.DIST": {"a": "(z; cumulativo)", "d": "Devolve a distribuição normal padrão (tem uma média de zero e um desvio padrão de um)", "ad": "é o valor para o qual pretende obter a distribuição!é um valor lógico que a função deve devolver: a função de distribuição cumulativa = VERDADEIRO; a função de densidade de probabilidade = FALSO"}, "NORMSDIST": {"a": "(z)", "d": "Devolve a distribuição cumulativa normal padrão (tem uma média de 0 e um desvio-padrão de 1)", "ad": "é o valor para o qual pretende obter a distribuição"}, "NORM.S.INV": {"a": "(probabilidade)", "d": "Devolve o inverso da distribuição cumulativa normal padrão (tem uma média de 0 e um desvio-padrão de 1)", "ad": "é uma probabilidade correspondente à distribuição normal, um número entre 0 e 1 inclusive"}, "NORMSINV": {"a": "(probabilidade)", "d": "Devolve o inverso da distribuição cumulativa normal padrão (tem uma média de 0 e um desvio-padrão de 1)", "ad": "é uma probabilidade correspondente à distribuição normal, um número entre 0 e 1 inclusive"}, "PEARSON": {"a": "(matriz1; matriz2)", "d": "Devolve o coeficiente de correlação do momento do produto Pearson, r", "ad": "é um conjunto de valores independentes!é um conjunto de valores dependentes"}, "PERCENTILE": {"a": "(matriz; k)", "d": "Devolve o enésimo percentil de valores num intervalo", "ad": "é a matriz ou intervalo de dados que define a posição relativa!é o valor do percentil no intervalo de 0 a 1, inclusive"}, "PERCENTILE.EXC": {"a": "(matriz; k)", "d": "Devolve o enésimo percentil de valores num intervalo, em que k está no intervalo 0..1, exclusive", "ad": "é a matriz ou intervalo de dados que define a posição relativa!é o valor do percentil no intervalo de 0 a 1, inclusive"}, "PERCENTILE.INC": {"a": "(matriz; k)", "d": "Devolve o enésimo percentil de valores num intervalo, em que k está no intervalo 0..1, inclusive", "ad": "é a matriz ou intervalo de dados que define a posição relativa!é o valor do percentil no intervalo de 0 a 1, inclusive"}, "PERCENTRANK": {"a": "(matriz; x; [significância])", "d": "Devolve a classificação de um valor num conjunto de dados como percentagem desse conjunto", "ad": "é a matriz ou intervalo de dados com valores numéricos que define a posição relativa!é o valor para o qual pretende saber a classificação!é um valor opcional que identifica o número de algarismos significativos para a percentagem devolvida, três algarismos se omisso (0,xxx%)"}, "PERCENTRANK.EXC": {"a": "(matriz; x; [significância])", "d": "Devolve a classificação de um valor num conjunto de dados como percentagem (0..1, exclusive) desse conjunto", "ad": "é a matriz ou intervalo de dados com valores numéricos que define uma classificação relativa!é o valor para o qual pretende saber a classificação!é um valor opcional que identifica o número de dígitos significativos para a percentagem devolvida, três dígitos se omisso (0,xxx%)"}, "PERCENTRANK.INC": {"a": "(matriz; x; [significância])", "d": "Devolve a classificação de um valor num conjunto de dados como percentagem (0..1, inclusive) desse conjunto", "ad": "é a matriz ou intervalo de dados com valores numéricos que define uma classificação relativa!é o valor para o qual deseja saber a ordem!é um valor opcional que identifica o número de dígitos significativos para a percentagem devolvida, três dígitos se omisso (0,xxx%)"}, "PERMUT": {"a": "(núm; núm_escolhido)", "d": "Devolve o número de permutações para um dado número de objetos que pode ser selecionado de entre a totalidade dos objetos", "ad": "é o número total de objetos!é o número de objetos em cada permutação"}, "PERMUTATIONA": {"a": "(número; número_escolhido)", "d": "Devolve o número de permutações para um determinado número de objetos (com repetições) que pode ser selecionado do total de objetos", "ad": "é o número total de objetos!é o número de objetos em cada permutação"}, "PHI": {"a": "(x)", "d": "Devolve o valor da função de densidade para uma distribuição normal padrão", "ad": "é o número para o qual quer obter a densidade da distribuição normal padrão"}, "POISSON": {"a": "(x; média; cumulativo)", "d": "Devolve a distribuição de Poisson", "ad": "é o número de eventos!é o valor numérico esperado, um número positivo!é um valor lógico: para a probabilidade de Poisson cumulativa, utilize VERDADEIRO; para a função de densidade de probabilidade de Poisson, utilize FALSO"}, "POISSON.DIST": {"a": "(x; média; cumulativo)", "d": "Devolve a distribuição de Poisson", "ad": "é o número de eventos!é o valor numérico esperado, um número positivo!é um valor lógico: para a probabilidade de Poisson cumulativa, utilize VERDADEIRO; para a função de densidade de probabilidade de Poisson, utilize FALSO"}, "PROB": {"a": "(intervalo_x; intervalo_prob; limite_inferior; [limite_superior])", "d": "Devolve a probabilidade de valores de um intervalo estarem entre dois limites ou serem iguais ao limite inferior", "ad": "é o intervalo de valores numéricos de x com os quais estão associadas probabilidades!é um conjunto de probabilidades associadas com valores no Intervalo_x, valores entre 0 e 1 à exceção do 0!é o limite inferior do valor cuja probabilidade deseja obter!é o limite superior opcional do valor. Se omisso, PROB devolve a probabilidade de os valores de Intervalo_x serem iguais a Limite_inferior"}, "QUARTILE": {"a": "(matriz; quarto)", "d": "Devolve o quartil de um conjunto de dados", "ad": "é a matriz ou o intervalo de células de valores numéricos cujo valor quartil pretende obter!é um número: valor mínimo = 0; primeiro quartil = 1; valor da mediana = 2; terceiro quartil = 3; valor máximo = 4"}, "QUARTILE.INC": {"a": "(matriz; quarto)", "d": "Devolve o quartil de um conjunto de dados, baseado em valores de percentil de 0..1, inclusive", "ad": "é a matriz ou o intervalo de células de valores numéricos cujo valor quartil pretende obter!é um número: valor mínimo = 0; primeiro quartil = 1; valor da mediana = 2; terceiro quartil = 3; valor máximo = 4"}, "QUARTILE.EXC": {"a": "(matriz; quarto)", "d": "Devolve o quartil de um conjunto de dados, baseado em valores de percentil de 0..1, exclusive", "ad": "é a matriz ou o intervalo de células de valores numéricos cujo valor quartil pretende obter!é um número: valor mínimo = 0; primeiro quartil = 1; valor da mediana = 2; terceiro quartil = 3; valor máximo = 4"}, "RANK": {"a": "(núm; ref; [ordem])", "d": "Devolve a classificação de um número numa lista de números: o tamanho do mesmo em relação a outros valores na lista", "ad": "é o número cuja classificação pretende encontrar!é uma matriz (ou uma referência) de uma lista de números. Valores não numéricos são ignorados!é um número: classificação na lista por ordem descendente = 0 ou omisso; classificação na lista por ordem ascendente = qualquer valor diferente de zero"}, "RANK.AVG": {"a": "(núm; ref; [ordem])", "d": "Devolve a classificação de um número numa lista de números: o seu tamanho em relação a outros valores na lista; se mais de um valor tiver a mesma classificação, é devolvida a classificação média", "ad": "é o número cuja classificação pretende encontrar!é uma matriz (ou uma referência) de uma lista de números. Os valores não numéricos são ignorados!é um número: classificação na lista por ordem descendente = 0 ou omisso; classificação na lista por ordem ascendente = qualquer valor diferente de zero"}, "RANK.EQ": {"a": "(núm; ref; [ordem])", "d": "Devolve a classificação de um número numa lista de números: o seu tamanho em relação a outros valores na lista; se mais de um valor tiver a mesma classificação, é devolvida a classificação superior desse conjunto de valores", "ad": "é o número cuja classificação pretende encontrar!é uma matriz (ou uma referência) de uma lista de números. Os valores não numéricos são ignorados!é um número: classificação na lista por ordem descendente = 0 ou omisso; classificação na lista por ordem ascendente = qualquer valor diferente de zero"}, "RSQ": {"a": "(val_conhecidos_y; val_conhecidos_x)", "d": "Devolve o quadrado do coeficiente de correlação do momento do produto de Pearson através dos pontos dados", "ad": "é uma matriz ou intervalo de pontos de dados e podem ser números ou nomes, matrizes ou referências que contenham números!é uma matriz ou intervalo de pontos de dados e podem ser números ou nomes, matrizes ou referências que contenham números"}, "SKEW": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a distorção de uma distribuição: uma caracterização do grau de assimetria da distribuição em torno da média", "ad": "são de 1 a 255 números ou nomes, matrizes ou referências contendo números cuja distorção deseja obter"}, "SKEW.P": {"a": "(número1; [número2]; ...)", "d": "Devolve a distorção de uma distribuição baseada numa população: uma caracterização do grau de assimetria de uma distribuição em torno da média", "ad": "são de 1 a 254 números ou nomes, matrizes ou referências contendo números cuja distorção quer obter"}, "SLOPE": {"a": "(val_conhecidos_y; val_conhecidos_x)", "d": "Devolve o declive da reta de regressão linear através dos pontos dados", "ad": "é uma matriz ou intervalo de célula de pontos de dados dependentes e numéricos e podem ser números ou nomes, matrizes ou referências que contenham números!é o conjunto de pontos de dados independentes e podem ser números ou nomes, matrizes ou referências que contenham números"}, "SMALL": {"a": "(matriz; k)", "d": "Devolve o n-ésimo menor valor de um conjunto de dados. Por exemplo, o quinto número menor", "ad": "é uma matriz ou intervalo de dados numéricos em que deseja determinar o n-ésimo menor valor!é a posição (a partir da menor), na matriz ou intervalo de dados, do valor a ser fornecido"}, "STANDARDIZE": {"a": "(x; média; desv_padrão)", "d": "Devolve um valor normalizado de uma distribuição caracterizada por uma média e um desvio-padrão", "ad": "é o valor que deseja normalizar!é a média aritmética da distribuição!é o desvio-padrão da distribuição, um número positivo"}, "STDEV": {"a": "(núm1; [núm2]; ...)", "d": "Calcula o desvio-padrão a partir de uma amostra (ignora valores lógicos e texto na amostra)", "ad": "são de 1 a 255 números correspondentes a uma amostra da população e podem ser números ou referências que contêm números"}, "STDEV.P": {"a": "(núm1; [núm2]; ...)", "d": "Calcula o desvio-padrão com base na população total fornecida como argumento (ignora valores lógicos e texto)", "ad": "são de 1 a 255 números correspondentes a uma população e podem ser números ou referências que contêm números"}, "STDEV.S": {"a": "(núm1; [núm2]; ...)", "d": "Calcula o desvio-padrão a partir de uma amostra (ignora valores lógicos e texto na amostra)", "ad": "são de 1 a 255 números correspondentes a uma amostra da população e podem ser números ou referências que contêm números"}, "STDEVA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula o desvio-padrão a partir de uma amostra, incluindo valores lógicos e texto. Texto e FALSO têm valor 0; VERDADEIRO tem o valor 1", "ad": "são de 1 a 255 argumentos de valores correspondentes a uma amostra de uma população e podem ser valores ou nomes ou referências a valores"}, "STDEVP": {"a": "(núm1; [núm2]; ...)", "d": "Calcula o desvio-padrão com base na população total fornecida como argumento (ignora valores lógicos e texto)", "ad": "são de 1 a 255 números correspondentes a uma população e podem ser números ou referências que contêm números"}, "STDEVPA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula o desvio-padrão com base na população total, incluindo valores lógicos e texto. Texto e FALSO têm valor 0; VERDADEIRO tem o valor 1", "ad": "são de 1 a 255 valores correspondentes a uma população e podem ser valores, nomes, matrizes ou referências que contêm valores"}, "STEYX": {"a": "(val_conhecidos_y; val_conhecidos_x)", "d": "Devolve o erro padrão do valor de y previsto para cada x da regressão", "ad": "é uma matriz ou intervalo de pontos de dados dependentes e podem ser números ou nomes, matrizes ou referências que contenham números!é uma matriz ou intervalo de pontos de dados independentes e podem ser números ou nomes, matrizes ou referências que contenham números"}, "TDIST": {"a": "(x; graus_liberdade; caudas)", "d": "Devolve a distribuição t de Student", "ad": "é o valor numérico em que se avalia a distribuição!é um número inteiro que indica o número de graus de liberdade que caracteriza a distribuição!especifica o número de caudas de distribuição a ser devolvido: distribuição unicaudal = 1; distribuição bicaudal = 2"}, "TINV": {"a": "(probabilidade; graus_liberdade)", "d": "Devolve o inverso bicaudal da distribuição t de Student", "ad": "é a probabilidade associada à distribuição t de Student bicaudal, um número entre 0 e 1, inclusive!é um número inteiro positivo que indica o número de graus de liberdade para caracterizar a distribuição"}, "T.DIST": {"a": "(x; graus_liberdade; cumulativo)", "d": "Devolve a distribuição t de Student unilateral à esquerda", "ad": "é o valor numérico sobre o qual a distribuição deve ser avaliada!é um número inteiro que indica o número de graus de liberdade que caracteriza a distribuição!é um valor lógico: para a função de distribuição cumulativa, utilize VERDADEIRO; para a função de densidade de probabilidade, utilize FALSO"}, "T.DIST.2T": {"a": "(x; graus_liberdade)", "d": "Devolve a distribuição t de Student bicaudal", "ad": "é o valor numérico sobre o qual a distribuição deve ser avaliada!é um número inteiro que indica o número de graus de liberdade que caracteriza a distribuição"}, "T.DIST.RT": {"a": "(x; graus_liberdade)", "d": "Devolve a distribuição t de Student unilateral à direita", "ad": "é o valor numérico sobre o qual a distribuição deve ser avaliada!é um número inteiro que indica o número de graus de liberdade que caracteriza a distribuição"}, "T.INV": {"a": "(probabilidade; graus_liberdade)", "d": "Devolve o inverso unilateral à esquerda da distribuição t de Student", "ad": "é a probabilidade associada à distribuição t de Student bicaudal, um número entre 0 e 1, inclusive!é um número inteiro positivo que indica o número de graus de liberdade que caracteriza a distribuição"}, "T.INV.2T": {"a": "(probabilidade; graus_liberdade)", "d": "Devolve o inverso bicaudal da distribuição t de Student", "ad": "é a probabilidade associada à distribuição t de Student bicaudal, um número entre 0 e 1, inclusive!é um número inteiro positivo que indica o número de graus de liberdade que caracteriza a distribuição"}, "T.TEST": {"a": "(matriz1; matriz2; caudas; tipo)", "d": "Devolve a probabilidade associada ao teste t de Student", "ad": "é o primeiro conjunto de dados!é o segundo conjunto de dados!especifica o número de caudas da distribuição a devolver: distribuição unicaudal = 1; distribuição bicaudal = 2!é o tipo de teste t: emparelhada = 1, duas amostras de variância igual (homocedásticas) = 2, duas amostras de variância desigual = 3"}, "TREND": {"a": "(val_conhecidos_y; [val_conhecidos_x]; [novos_valores_x]; [constante])", "d": "Devolve valores numa tendência linear correspondente a pontos de dados conhecidos por aplicação do método dos quadrados mínimos", "ad": "é um intervalo ou matriz de valores de y já conhecidos na relação y = mx + b!é um intervalo opcional ou matriz de valores de x já conhecidos na relação y = mx + b, uma matriz com o mesmo tamanho de val_conhecidos_y!é um intervalo ou matriz de novos valores de x para os quais pretende que TENDÊNCIA devolva valores de y correspondentes!é um valor lógico: a constante b é calculada normalmente se Constante = VERDADEIRO ou omisso; b é definido igual a 0 se Constante = FALSO"}, "TRIMMEAN": {"a": "(matriz; percentagem)", "d": "Devolve a média da porção interior de um conjunto de valores de dados", "ad": "é a matriz ou intervalo de valores que deseja compactar e calcular a média!é o número fracionário de ponto de dados a ser excluído do topo e da base do conjunto de dados"}, "TTEST": {"a": "(matriz1; matriz2; caudas; tipo)", "d": "Devolve a probabilidade associada ao teste t de Student", "ad": "é o primeiro conjunto de dados!é o segundo conjunto de dados!especifica o número de caudas de distribuição a devolver: distribuição unicaudal = 1; distribuição bicaudal = 2!é o tipo de teste t: emparelhada = 1, duas amostras de variância igual (homocedásticas) = 2, duas amostras de variância desigual = 3"}, "VAR": {"a": "(núm1; [núm2]; ...)", "d": "Calcula a variância a partir de uma amostra (ignora valores lógicos e texto da amostra)", "ad": "são de 1 a 255 argumentos numéricos correspondentes a uma amostra de uma população"}, "VAR.P": {"a": "(núm1; [núm2]; ...)", "d": "Calcula a variância a partir da população total (ignora valores lógicos e texto da população)", "ad": "são de 1 a 255 argumentos numéricos correspondentes a uma população"}, "VAR.S": {"a": "(núm1; [núm2]; ...)", "d": "Calcula a variância a partir de uma amostra (ignora valores lógicos e texto da amostra)", "ad": "são de 1 a 255 argumentos numéricos correspondentes a uma amostra de uma população"}, "VARA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula a variância a partir de uma amostra, incluindo valores lógicos e texto. Texto e o valor lógico FALSO = 0; o valor lógico VERDADEIRO = 1", "ad": "são de 1 a 255 argumentos de valores correspondentes a uma amostra de uma população"}, "VARP": {"a": "(núm1; [núm2]; ...)", "d": "Calcula a variância a partir da população total (ignora valores lógicos e texto da população)", "ad": "são de 1 a 255 argumentos numéricos correspondentes a uma população"}, "VARPA": {"a": "(valor1; [valor2]; ...)", "d": "Calcula a variância com base na população total, incluindo valores lógicos e texto. Texto e o valor lógico FALSO = 0; o valor lógico VERDADEIRO = 1", "ad": "são de 1 a 255 argumentos correspondentes a uma população"}, "WEIBULL": {"a": "(x; alfa; beta; cumulativo)", "d": "Devolve a distribuição Weibull", "ad": "é o valor no qual se avalia a função, um número não negativo!é um parâmetro da distribuição, um número positivo!é um parâmetro da distribuição, um número positivo!é um valor lógico: para a função de distribuição cumulativa, utilize VERDADEIRO; para a função de densidade de probabilidade, utilize FALSO"}, "WEIBULL.DIST": {"a": "(x; alfa; beta; cumulativo)", "d": "Devolve a distribuição Weibull", "ad": "é o valor no qual se avalia a função, um número não negativo!é um parâmetro da distribuição, um número positivo!é um parâmetro da distribuição, um número positivo!é um valor lógico: para a função de distribuição cumulativa, utilize VERDADEIRO; para a função de densidade de probabilidade, utilize FALSO"}, "Z.TEST": {"a": "(matriz; x; [sigma])", "d": "Devolve o valor P unicaudal de um teste z", "ad": "é a matriz ou intervalo de dados com que X será testado!é o valor a testar!é o desvio-padrão (conhecido) da população. Se omisso, é utilizado o desvio-padrão da amostra"}, "ZTEST": {"a": "(matriz; x; [sigma])", "d": "Devolve o valor P unicaudal de um teste z", "ad": "é a matriz ou intervalo de dados com que X será testado!é o valor a testar!é o desvio-padrão (conhecido) da população. Se omisso, é utilizado o desvio-padrão da amostra"}, "ACCRINT": {"a": "(emissão; primeiro_juro; liquidação; taxa; paridade; frequência; [base]; [método_calc])", "d": "Devolve os juros decorridos de um título que paga juros periódicos", "ad": "é a data de emissão do título, expressa como um número de série de data!é a data do primeiro juro do título, expressa como um número de série de data!é a data de liquidação do título, expressa como um número de série de data!é a taxa de cupão anual do título!é o valor de paridade do título!é o número de pagamentos de cupão por ano!é o tipo de base de contagem diária a utilizar!é um valor lógico: para juros decorridos a partir da data de emissão = VERDADEIRO ou omisso; para calcular a partir da última data de pagamento de cupão = FALSO"}, "ACCRINTM": {"a": "(emissão; liquidação; taxa; valor_nominal; [base])", "d": "Devolve os juros decorridos de um título que paga juros no vencimento", "ad": "é a data de emissão do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é a taxa de cupão anual do título!é o valor de paridade do título!é o tipo de base de contagem diária a utilizar"}, "AMORDEGRC": {"a": "(custo; data_aquisição; prim_período; recuperação; período; taxa; [base])", "d": "Devolve a depreciação linear pro rata de um bem para cada período contabilístico", "ad": "é o custo do bem!é a data em que o bem foi comprado!é a data do final do primeiro período!é o valor de recuperação no final da vida do bem!é o período!é a taxa de depreciação!base_ano: 0 para anos de 360 dias, 1 para o atual, 3 para anos de 365 dias."}, "AMORLINC": {"a": "(custo; data_aquisição; prim_período; recuperação; período; taxa; [base])", "d": "Devolve a depreciação linear pro rata de um bem para cada período contabilístico", "ad": "é o custo do bem!é a data em que o bem foi comprado!é a data do final do primeiro período!é o valor de recuperação no final da vida do bem!é o período!é a taxa de depreciação!base_ano: 0 para anos de 360 dias, 1 para o atual, 3 para anos de 365 dias."}, "COUPDAYBS": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Devolve o número de dias entre o início do período do cupão e a data de liquidação", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é o número de pagamentos de cupão por ano!é o tipo de base de contagem diária a utilizar"}, "COUPDAYS": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Devolve o número de dias no período do cupão que contém a data de liquidação", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é o número de pagamentos de cupão por ano!é o tipo de base de contagem diária a utilizar"}, "COUPDAYSNC": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Devolve o número de dias entre a data de liquidação e a data seguinte do cupão", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é o número de pagamentos de cupão por ano!é o tipo de base de contagem diária a utilizar"}, "COUPNCD": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Devolve a data seguinte do cupão depois da data de liquidação", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é o número de pagamentos de cupão por ano!é o tipo de base de contagem diária a utilizar"}, "COUPNUM": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Devolve o número de cupões a pagar entre a data de liquidação e a data do vencimento", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é o número de pagamentos de cupão por ano!é o tipo de base de contagem diária a utilizar"}, "COUPPCD": {"a": "(liquidação; vencimento; frequência; [base])", "d": "Devolve a última data do cupão antes da data de liquidação", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é o número de pagamentos de cupão por ano!é o tipo de base de contagem diária a utilizar"}, "CUMIPMT": {"a": "(taxa; nper; va; início_período; final_período; tipo_pgto)", "d": "Devolve os juros acumulados pagos entre dois períodos", "ad": "é a taxa de juros!é o número total de períodos de pagamento!é o valor atual!é o primeiro período do cálculo!é o último período do cálculo!indica quando o pagamento deve ser efetuado"}, "CUMPRINC": {"a": "(taxa; nper; va; início_período; final_período; tipo_pgto)", "d": "Devolve o capital acumulado pago por um empréstimo entre dois períodos", "ad": "é a taxa de juros!é o número total de períodos de pagamento!é o valor atual!é o primeiro período do cálculo!é o último período do cálculo!indica quando o pagamento deve ser efetuado"}, "DB": {"a": "(custo; val_residual; vida_útil; período; [mês])", "d": "Devolve a depreciação de um bem num determinado período, utilizando o método de redução fixa do saldo", "ad": "é o custo inicial do bem!é o valor residual no final da vida útil do bem!é o número de períodos durante os quais o bem é depreciado (por vezes chamado vida útil do bem)!é o período para o qual deseja calcular a depreciação. A unidade do período tem de ser a mesma de Vida_útil!é o número de meses do primeiro ano. Se mês for omisso, o valor assumido é 12"}, "DDB": {"a": "(custo; val_residual; vida_útil; período; [fator])", "d": "Devolve a depreciação de um bem para um determinado período utilizando o método de redução dupla do saldo ou qualquer outro método especificado", "ad": "é o custo inicial do bem!é o valor residual no final da vida útil do bem!é o número de períodos durante os quais o bem é depreciado (por vezes chamado vida útil do bem)!é o período para o qual deseja calcular a depreciação. A unidade do período tem de ser a mesma de Vida_útil!é o índice de redução do saldo. Se Factor for omisso, assume-se que é 2 (método de redução dupla do saldo)"}, "DISC": {"a": "(liquidação; vencimento; pr; resgate; [base])", "d": "Devolve a taxa de desconto de um título", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é o preço do título por cada 100 € do valor nominal!é o valor de resgate do título por cada 100 € do valor nominal!é o tipo de base de contagem diária a utilizar"}, "DOLLARDE": {"a": "(moeda_fraccionária; fração)", "d": "Converte a expressão de um preço em moeda de uma fração para um número decimal", "ad": "é um número expresso como uma fração!é o inteiro a utilizar no denominador da fração"}, "DOLLARFR": {"a": "(moeda_decimal; fração)", "d": "Converte a expressão de um preço em moeda de um número decimal para uma fração", "ad": "é um número decimal!é o inteiro a utilizar no denominador da fração"}, "DURATION": {"a": "(liquidação; vencimento; cupão; lcr; frequência; [base])", "d": "Devolve a duração anual de um título com pagamentos de juros periódicos", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é a taxa de cupão anual do título!é o lucro anual do título!é o número de pagamentos de cupão por ano!é o tipo de base de contagem diária a utilizar"}, "EFFECT": {"a": "(taxa_nominal; núm_por_ano)", "d": "Devolve a taxa de juros anual efetiva", "ad": "é a taxa de juros nominal!é o número de períodos compostos por ano"}, "FV": {"a": "(taxa; nper; pgto; [va]; [tipo])", "d": "Devolve o valor futuro de um investimento a partir de pagamentos periódicos constantes e de uma taxa de juros constante.", "ad": "é a taxa de juro por período. Por exemplo, utilize 6%/4 para pagamentos trimestrais a 6% APR!é o número total de períodos de pagamento de um investimento!é o pagamento efetuado em cada período; não é possível alterá-lo enquanto durar o investimento!é o valor atual ou a quantia global correspondente a uma série de pagamentos futuros. Se omisso, Va = 0!é um valor que representa o escalonamento de pagamentos: pagamento no início do período = 1; pagamento no final do período = 0 ou omisso"}, "FVSCHEDULE": {"a": "(capital; plano)", "d": "Devolve o valor futuro de um capital inicial depois de ter sido aplicada uma série de taxas de juros compostos", "ad": "é o valor presente!é uma matriz de taxas de juros a aplicar"}, "INTRATE": {"a": "(liquidação; vencimento; investimento; resgate; [base])", "d": "Devolve a taxa de juros de um título totalmente investido", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é a quantia investida no título!é a quantia a receber na data de vencimento!é o tipo de base de contagem diária a utilizar"}, "IPMT": {"a": "(taxa; período; nper; va; [vf]; [tipo])", "d": "Devolve o pagamento dos juros de um investimento durante um dado período, a partir de pagamentos periódicos e uma taxa de juro constantes", "ad": "é a taxa de juro por período. Por exemplo, utilize 6%/4 para pagamentos trimestrais a 6% APR!é o período cujos juros deseja obter e tem de estar situado no intervalo entre 1 e Nper!é o número total de períodos de pagamento de um investimento!é o valor atual da quantia correspondente aos pagamentos futuros!é o valor futuro ou o saldo em dinheiro que deseja atingir após ter sido efetuado o último pagamento. Se omisso, Fv = 0!é o valor lógico que representa o escalonamento do pagamento: no final do período = 0 ou omisso; no início do período = 1"}, "IRR": {"a": "(valores; [estimativa])", "d": "Devolve a taxa interna de rentabilidade de uma série de fluxos monetários", "ad": "é uma matriz ou uma referência a células que contêm números cuja taxa interna de rentabilidade deseja calcular!é um número que se estima ser próximo do resultado de TIR; 0,1 (10 por cento), se omisso"}, "ISPMT": {"a": "(taxa; período; nper; va)", "d": "Devolve os juros dos pagamentos de um empréstimo simples durante um período específico", "ad": "taxa de juro por período. Por exemplo, utilize 6%/4 para pagamentos trimestrais a 6% APR!período cujos juros deseja obter!número de períodos de pagamento de um investimento!valor atual da soma global de uma série de pagamentos"}, "MDURATION": {"a": "(liquidação; vencimento; cupão; lcr; frequência; [base])", "d": "Devolve a duração modificada de Macauley de um título com um valor par atribuído de 100 €", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é a taxa de cupão anual do título!é o lucro anual do título!é o número de pagamentos de cupão por ano!é o tipo de base de contagem diária a utilizar"}, "MIRR": {"a": "(valores; taxa_financ; taxa_reinvest)", "d": "Devolve a taxa interna de rentabilidade de fluxos monetários periódicos, avaliando custos de investimento e juros de reinvestimento dos valores líquidos", "ad": "é uma matriz ou uma referência a células que contêm números que representam uma série de pagamentos (negativos) e recebimentos (positivos) em períodos regulares!é a taxa de juro paga sobre o dinheiro utilizado em fluxos monetários!é a taxa de juros recebida sobre os fluxos monetários à medida que estes foram sendo reinvestidos"}, "NOMINAL": {"a": "(taxa_efetiva; núm_por_ano)", "d": "Devolve a taxa de juros nominal anual", "ad": "é a taxa de juros efetiva!é o número de períodos compostos por ano"}, "NPER": {"a": "(taxa; pgto; va; [vf]; [tipo])", "d": "Devolve o número de períodos de um investimento, com base em pagamentos periódicos constantes e uma taxa de juros constante", "ad": "é a taxa de juro por período. Por exemplo, utilize 6%/4 para pagamentos trimestrais a 6% APR!é o pagamento efetuado em cada período; não é possível alterá-lo enquanto durar o investimento!é o valor atual da quantia correspondente aos pagamentos futuros!é o valor futuro ou o saldo em dinheiro que deseja atingir após o último pagamento ter sido efetuado. Se omisso, é utilizado zero!é um valor lógico: pagamento no início do período = 1; no final do período = 0 ou omisso"}, "NPV": {"a": "(taxa; valor1; [valor2]; ...)", "d": "Devolve o valor atual líquido de um investimento, com uma taxa de desconto e uma série de pagamentos futuros (valor negativo) e rendimentos (valor positivo)", "ad": "é a taxa de desconto ao longo de um período!são de 1 a 254 pagamentos e rendimentos, igualmente espaçados no tempo e ocorrendo no final de cada período"}, "ODDFPRICE": {"a": "(liquidação; vencimento; emissão; prim_cupão; taxa; lcr; resgate; frequência; [base])", "d": "Devolve o preço por cada 100 € do valor nominal de um título com um período inicial incompleto", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é a data de emissão do título, expressa como um número de série de data!é a primeira data do cupão do título, expressa como um número de série de data!é a taxa de juros do título!é o lucro anual do título!é valor de resgate por cada 100 € do valor nominal!é o número de pagamentos de cupão por ano!é o tipo de base de contagem diária a utilizar"}, "ODDFYIELD": {"a": "(liquidação; vencimento; emissão; prim_cupão; taxa; pr; resgate; frequência; [base])", "d": "Devolve o rendimento de um título com um período inicial incompleto", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é a data de emissão do título, expressa como um número de série de data!é a primeira data do cupão do título, expressa como um número de série de data!é a taxa de juros do título!é o preço do título!é valor de resgate por cada 100 € do valor nominal!é o número de pagamentos de cupão por ano!é o tipo de base de contagem diária a utilizar"}, "ODDLPRICE": {"a": "(liquidação; vencimento; último_juros; taxa; lcr; resgate; frequência; [base])", "d": "Devolve o preço por cada 100 € do valor nominal de um título com um período final incompleto", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é a última data do cupão do título, expressa como um número de série de data!é a taxa de juros do título!é o lucro anual do título!é valor de resgate por cada 100 € do valor nominal!é o número de pagamentos de cupão por ano!é o tipo de base de contagem diária a utilizar"}, "ODDLYIELD": {"a": "(liquidação; vencimento; último_juros; taxa; pr; resgate; frequência; [base])", "d": "Devolve o rendimento de um título com um período final incompleto", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é a última data do cupão do título, expressa como um número de série de data!é a taxa de juros do título!é o preço do título!é valor de resgate por cada 100 € do valor nominal!é o número de pagamentos de cupão por ano!é o tipo de base de contagem diária a utilizar"}, "PDURATION": {"a": "(taxa; va; vf)", "d": "Devolve o número de períodos necessários a um investimento para atingir um valor específico", "ad": "é a taxa de juro por período!é o valor atual do investimento!é o valor futuro pretendido do investimento"}, "PMT": {"a": "(taxa; nper; va; [vf]; [tipo])", "d": "Calcula o pagamento de um empréstimo, a partir de pagamentos constantes e uma taxa de juros constante", "ad": "é a taxa de juro por período para o empréstimo. Por exemplo, utilize 6%/4 para pagamentos trimestrais a 6% APR!é o número total de pagamentos do empréstimo!é o valor atual: o montante total que representa agora uma série de pagamentos futuros!é o valor futuro ou o saldo em dinheiro que deseja obter após o último pagamento ter sido efetuado, 0 (zero), se omisso!é um valor lógico: pagamento no início do período = 1; pagamento no final do período = 0 ou omisso"}, "PPMT": {"a": "(taxa; período; nper; va; [vf]; [tipo])", "d": "Devolve o pagamento sobre o montante de um investimento, a partir de pagamentos constantes e periódicos e uma taxa de juros constante", "ad": "é a taxa de juro por período. Por exemplo, utilize 6%/4 para pagamentos trimestrais a 6% APR!especifica o período e tem de estar no intervalo entre 1 e nper!é o número total de períodos de pagamento de um investimento!é o valor atual: o montante total atual de uma série de pagamentos futuros!é o valor futuro ou o saldo em dinheiro que deseja atingir após o último pagamento ter sido efetuado!é um valor lógico: pagamento no início do período = 1; pagamento no final do período = 0 ou omisso"}, "PRICE": {"a": "(liquidação; vencimento; taxa; lcr; resgate; frequência; [base])", "d": "Devolve o preço por cada 100 € do valor nominal de um título que paga juros periódicos", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é a taxa de cupão anual do título!é o lucro anual do título!é o valor de resgate do título por cada 100 € do valor nominal!é o número de pagamentos de cupão por ano!é o tipo de base de contagem diária a utilizar"}, "PRICEDISC": {"a": "(liquidação; vencimento; desconto; resgate; [base])", "d": "Devolve o preço por cada 100 € do valor nominal de um título descontável", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é a taxa de desconto do título!é o valor de resgate do título por cada 100 € do valor nominal!é o tipo de base de contagem diária a utilizar"}, "PRICEMAT": {"a": "(liquidação; vencimento; emissão; taxa; lcr; [base])", "d": "Devolve o preço por cada 100 € do valor nominal de um título que paga juros no vencimento", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é a data de emissão do título, expressa como um número de série de data!é a taxa de juros do título na data de emissão!é o lucro anual do título!é o tipo de base de contagem diária a utilizar"}, "PV": {"a": "(taxa; nper; pgto; [vf]; [tipo])", "d": "Devolve o valor atual de um investimento: o montante total que vale agora uma série de pagamentos futuros", "ad": "é a taxa de juro por período. Por exemplo, utilize 6%/4 para pagamentos trimestrais a 6% APR!é o número total de períodos de pagamento de um investimento!é o pagamento efetuado em cada período; não é possível alterá-lo enquanto durar o investimento!é o valor futuro ou o saldo em dinheiro que deseja atingir após o último pagamento ter sido efetuado!é um valor lógico: pagamento no início do período = 1; pagamento no final do período = 0 ou omisso"}, "RATE": {"a": "(nper; pgto; va; [vf]; [tipo]; [estimativa])", "d": "Devolve a taxa de juros por período de um empréstimo ou um investimento. Por exemplo, utilize 6%/4 para pagamentos trimestrais a 6% APR", "ad": "é o número total de períodos de pagamento do empréstimo ou investimento!é o pagamento efetuado em cada período; não é possível alterá-lo enquanto durar o empréstimo ou investimento!é o valor atual: o que o montante total de uma série de pagamentos futuros vale no presente!é o valor futuro ou um saldo líquido que deseja atingir após o último pagamento ter sido efetuado. Se omisso, utiliza Fv = 0!é um valor lógico: pagamento no início do período = 1; pagamento no final do período = 0 ou omisso !é a estimativa do valor da taxa; se omisso, Estimativa = 0,1 (10 por cento)"}, "RECEIVED": {"a": "(liquidação; vencimento; investimento; desconto; [base])", "d": "Devolve a quantia recebida no vencimento de um título totalmente investido", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é a quantia investida no título!é a taxa de desconto do título!é o tipo de base de contagem diária a utilizar"}, "RRI": {"a": "(nper; va; vf)", "d": "Devolve uma taxa de juro equivalente para o crescimento de um investimento", "ad": "é o número de períodos para o investimento!é o valor atual do investimento!é o valor futuro do investimento"}, "SLN": {"a": "(custo; val_residual; vida_útil)", "d": "Devolve a depreciação linear de um bem durante um período", "ad": "é o custo inicial do bem!é o valor residual no final da vida útil do bem!é o número de períodos em que o bem é depreciado (por vezes chamado vida útil do bem)"}, "SYD": {"a": "(custo; val_residual; vida_útil; período)", "d": "Devolve a depreciação por algarismos da soma dos anos de um bem para um período especificado", "ad": "é o custo inicial do bem!é o valor residual no final da vida útil do bem!é o número de períodos em que o bem é depreciado (por vezes designado vida útil do bem)!é o período e tem de utilizar a mesma unidade de Vida_útil"}, "TBILLEQ": {"a": "(liquidação; vencimento; desconto)", "d": "Devolve o rendimento de um título do Tesouro equivalente a um novo título", "ad": "é a data de liquidação do título do Tesouro, expressa como um número de série de data!é a data de vencimento do título do Tesouro, expressa como um número de série de data!é a taxa de desconto do título do Tesouro"}, "TBILLPRICE": {"a": "(liquidação; vencimento; desconto)", "d": "Devolve o preço por cada 100 € do valor nominal de um título do Tesouro", "ad": "é a data de liquidação do título do Tesouro, expressa como um número de série de data!é a data de vencimento do título do Tesouro, expressa como um número de série de data!é a taxa de desconto do título do Tesouro"}, "TBILLYIELD": {"a": "(liquidação; vencimento; pr)", "d": "Devolve o rendimento de um título do Tesouro", "ad": "é a data de liquidação do título do Tesouro, expressa como um número de série de data!é a data de vencimento do título do Tesouro, expressa como um número de série de data!é o preço por cada 100 € do valor nominal do título do Tesouro"}, "VDB": {"a": "(custo; val_residual; vida_útil; início_período; final_período; [fator]; [sem_mudança])", "d": "Devolve a depreciação de um bem em qualquer período dado, incluindo parciais, utilizando o método de redução dupla do saldo ou outro especificado", "ad": "é o custo inicial do bem!é o valor residual no final da vida útil do bem!é o número de períodos ao longo dos quais o bem é depreciado (por vezes chamado vida útil do bem)!é o período inicial em que deseja calcular a depreciação, na mesma unidade de Vida_útil!é o período final em que deseja calcular a depreciação, nas mesmas unidades de Vida_útil!é a taxa de redução do saldo, 2 (redução dupla do saldo), se omisso!mudar para depreciação linear quando a depreciação for maior que o cálculo da redução do saldo = FALSO ou omisso; não mudar = VERDADEIRO"}, "XIRR": {"a": "(valores; datas; [estimativa])", "d": "Devolve a taxa de rentabilidade interna de uma sucessão de fluxos monetários", "ad": "é uma série de fluxos monetários que corresponde a um programa de pagamentos em datas!é a sucessão de datas que corresponde aos pagamentos de fluxos monetários!é um número que se estima próximo do resultado de XTIR"}, "XNPV": {"a": "(taxa; valores; datas)", "d": "Devolve o valor atual líquido de uma sucessão de fluxos monetários", "ad": "é a taxa de desconto a aplicar nos fluxos monetários!é uma série de fluxos monetários que corresponde a um programa de pagamentos em datas!é o programa de datas de pagamento que corresponde aos pagamentos de fluxos monetários"}, "YIELD": {"a": "(liquidação; vencimento; taxa; pr; resgate; frequência; [base])", "d": "Devolve o rendimento de um título que paga juros periódicos", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é a taxa de cupão anual do título!é o preço do título por cada 100 € do valor nominal!é o valor de resgate do título por cada 100 € do valor nominal!é o número de pagamentos de cupão por ano!é o tipo de base de contagem diária a utilizar"}, "YIELDDISC": {"a": "(liquidação; vencimento; pr; resgate; [base])", "d": "Devolve o rendimento anual de um título descontável, como, por exemplo, os títulos do Tesouro", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é o preço do título por cada 100 € do valor nominal!é o valor de resgate do título por cada 100 € do valor nominal!é o tipo de base de contagem diária a utilizar"}, "YIELDMAT": {"a": "(liquidação; vencimento; emissão; taxa; pr; [base])", "d": "Devolve o rendimento anual de um título que paga juros no vencimento", "ad": "é a data de liquidação do título, expressa como um número de série de data!é a data de vencimento do título, expressa como um número de série de data!é a data de emissão do título, expressa como um número de série de data!é a taxa de juros do título na data de emissão!é o preço do título por cada 100 € do valor nominal!é o tipo de base de contagem diária a utilizar"}, "ABS": {"a": "(núm)", "d": "Devolve o valor absoluto de um número, um número sem o respetivo sinal", "ad": "é o número real para o qual deseja obter o valor absoluto"}, "ACOS": {"a": "(núm)", "d": "Devolve o arco de cosseno de um número, em radianos, no intervalo de 0 a Pi. O arco de cosseno é o ângulo cujo cosseno é Núm", "ad": "é o cosseno do ângulo desejado e deve estar entre -1 e 1"}, "ACOSH": {"a": "(núm)", "d": "Devolve o cosseno hiperbólico inverso de um número", "ad": "é qualquer número real igual ou maior que 1"}, "ACOT": {"a": "(número)", "d": "Devolve o arco tangente de um número em radianos no intervalo 0 a Pi.", "ad": "é a cotangente do ângulo que quer obter"}, "ACOTH": {"a": "(número)", "d": "Devolve a cotangente hiperbólica inverso de um número", "ad": "é a cotangente hiperbólica do ângulo que quer obter"}, "AGGREGATE": {"a": "(núm_função; opções; ref1; ...)", "d": "Devolve uma agregação numa lista ou base de dados", "ad": "é o número 1 a 19 que especifica a função de resumo para a agregação.!é o número 0 a 7 que especifica os valores a ignorar para a agregação!é a matriz ou intervalo de dados numéricos sobre os quais calcular a agregação!indica a posição na matriz; é o k.º maior, k.º menor, k.º percentil ou k.º quartil.!é o número 1 a 19 que especifica a função de resumo para a agregação.! é o número 0 a 7 que especifica os valores a ignorar para a agregação!são 1 a 253 intervalos ou referências que pretende agregar"}, "ARABIC": {"a": "(texto)", "d": "Converte um número Romano em Arábico", "ad": "é o número Romano que deseja converter"}, "ASC": {"a": "(texto)", "d": "Em tipos de linguagem de conjuntos de carateres de bytes duplos (DBCS), a função altera os carateres de largura total (bytes duplos) para carateres de largura média (byte único)", "ad": "é o texto que pretende alterar"}, "ASIN": {"a": "(núm)", "d": "Devolve o arco de seno de um número em radianos, no intervalo de -Pi/2 a Pi/2", "ad": "é o seno do ângulo desejado e tem de situar-se entre -1 e 1"}, "ASINH": {"a": "(núm)", "d": "Devolve o seno hiperbólico inverso de um número", "ad": "é qualquer número real igual ou maior que 1"}, "ATAN": {"a": "(núm)", "d": "Devolve o arco de tangente de um número em radianos, num intervalo de -Pi/2 a Pi/2", "ad": "é a tangente do ângulo desejado"}, "ATAN2": {"a": "(núm_x; núm_y)", "d": "Devolve o arco de tangente das coordenadas x e y especificadas, em radianos, de -Pi a Pi, excluindo -Pi", "ad": "é a coordenada-x do ponto!é a coordenada-y do ponto"}, "ATANH": {"a": "(núm)", "d": "Devolve a tangente hiperbólica inversa de um número", "ad": "é qualquer número real entre -1 e 1, à exceção dos próprios"}, "BASE": {"a": "(número; base; [comprimento_mín])", "d": "Converte um número numa representação textual com a base dada", "ad": "é o número que quer converter!é a base para a qual quer converter o número!é o comprimento mínimo da cadeia devolvida. Se omitido, não serão adicionados zeros à esquerda"}, "CEILING": {"a": "(núm; significância)", "d": "Arredonda um número por excesso para o múltiplo significativo mais próximo", "ad": "é o valor que deseja arredondar!é o múltiplo para o qual deseja fazer o arredondamento"}, "CEILING.MATH": {"a": "(número; [significância]; [modo])", "d": "Arredonda um número por excesso para o número inteiro ou para o múltiplo significativo mais próximo", "ad": "é o valor que quer arredondar!é o múltiplo para o qual quer arredondar!quando dado e diferente de zero, esta função irá arredondar acima de zero"}, "CEILING.PRECISE": {"a": "(número; [significância])", "d": "Devolve um número arredondado para o número inteiro mais próximo ou para o múltiplo de significância mais próximo", "ad": "é o valor que deseja arredondar!é o múltiplo para o qual deseja fazer o arredondamento"}, "COMBIN": {"a": "(núm; núm_escolhido)", "d": "Devolve o número de combinações para um determinado conjunto de itens", "ad": "é o número total de itens!é o número de itens em cada combinação"}, "COMBINA": {"a": "(número; número_escolhido)", "d": "Devolve o número de combinações com repetições para um determinado número de itens", "ad": "é o número total de itens!é o número total de itens em cada combinação"}, "COS": {"a": "(núm)", "d": "Devolve o cosseno de um ângulo", "ad": "é o ângulo em radianos para o qual deseja obter o cosseno"}, "COSH": {"a": "(núm)", "d": "Devolve o cosseno hiperbólico de um número", "ad": "é qualquer número real"}, "COT": {"a": "(número)", "d": "Devolve a cotangente de um ângulo", "ad": "é o ângulo em radianos para o qual quer obter a cotangente"}, "COTH": {"a": "(número)", "d": "Devolve a cotangente hiperbólica de um número", "ad": "é o ângulo em radianos para o qual quer obter a cotangente hiperbólica"}, "CSC": {"a": "(número)", "d": "Devolve a cossecante de um ângulo", "ad": "é o ângulo em radianos para o qual quer obter a cossecante"}, "CSCH": {"a": "(número)", "d": "Devolve a cossecante hiperbólica de um ângulo", "ad": "é o ângulo em radianos para o qual quer obter a cossecante hiperbólica"}, "DECIMAL": {"a": "(número; base)", "d": "Converte uma representação textual de um número numa determinada base em um número decimal", "ad": "é o número que quer converter!é a base do número que vai a converter"}, "DEGREES": {"a": "(â<PERSON><PERSON>)", "d": "Converte radianos em graus", "ad": "é o ângulo em radianos que deseja converter"}, "ECMA.CEILING": {"a": "(núm; significância)", "d": "Arredonda um número por excesso para o múltiplo significativo mais próximo", "ad": "é o valor que deseja arredondar!é o múltiplo para o qual deseja fazer o arredondamento"}, "EVEN": {"a": "(núm)", "d": "Arredonda um número positivo para cima e um número negativo para baixo para o valor inteiro mais próximo", "ad": "é o valor a arredondar"}, "EXP": {"a": "(núm)", "d": "Devolve 'e' elevado à potência de um número dado", "ad": "é o expoente aplicado à base 'e'. A constante 'e' é igual a 2,71828182845904, a base do logaritmo natural"}, "FACT": {"a": "(núm)", "d": "Devolve o fatorial de um número, igual a 1*2*3*...*núm", "ad": "é um número não negativo cujo fatorial deseja obter"}, "FACTDOUBLE": {"a": "(núm)", "d": "Devolve o fatorial duplo de um número", "ad": "é o valor cujo fatorial duplo se pretende"}, "FLOOR": {"a": "(núm; significância)", "d": "Arredonda um número por defeito para o múltiplo significativo mais próximo", "ad": "é o valor numérico que deseja arredondar!é o múltiplo para o qual deseja arredondar. Número e Significância têm de ser ambos positivos ou ambos negativos"}, "FLOOR.PRECISE": {"a": "(número; [significância])", "d": "Devolve um número arredondado por defeito para o número inteiro mais próximo ou para o múltiplo de significância mais próximo", "ad": "é o valor que deseja arredondar!é o múltiplo para o qual deseja fazer o arredondamento"}, "FLOOR.MATH": {"a": "(número; [significância]; [modo])", "d": "Arredonda um número por defeito para o número inteiro ou para o múltiplo significativo mais próximo", "ad": "é o valor que quer arredondar!é o múltiplo para o qual quer arrendondar!quando dado e diferente de zero, esta função irá arredondar para zero"}, "GCD": {"a": "(núm1; [núm2]; ...)", "d": "devolve o maior divisor comum", "ad": "de 1 a 255 valores"}, "INT": {"a": "(núm)", "d": "Arredonda um número por defeito até ao número inteiro mais próximo", "ad": "é o número real que deseja arredondar por defeito até um inteiro"}, "ISO.CEILING": {"a": "(número; [significância])", "d": "Devolve um número arredondado para o número inteiro mais próximo ou para o múltiplo de significância mais próximo. Independentemente do sinal do número, o número é arredondado. Contudo, se o número ou a significância for zero, é devolvido zero.", "ad": "é o valor que deseja arredondar!é o múltiplo para o qual deseja fazer o arredondamento"}, "LCM": {"a": "(núm1; [núm2]; ...)", "d": "Devolve o mínimo múltiplo comum", "ad": "de 1 a 255 valores para os quais pretende o mínimo múl<PERSON>lo comum"}, "LN": {"a": "(núm)", "d": "Devolve o logaritmo natural de um número", "ad": "é o número real positivo cujo logaritmo natural deseja obter"}, "LOG": {"a": "(núm; [base])", "d": "Devolve o logaritmo de um número na base especificada", "ad": "é o número real positivo cujo logaritmo deseja obter!é a base do logaritmo; 10, se omisso"}, "LOG10": {"a": "(núm)", "d": "Devolve o logaritmo de base 10 de um número", "ad": "é o número real positivo cujo logaritmo de base 10 deseja obter"}, "MDETERM": {"a": "(matriz)", "d": "Devolve o determinante de uma matriz", "ad": "é uma matriz numérica com um número igual de linhas e colunas, seja um intervalo de células ou uma constante de matriz"}, "MINVERSE": {"a": "(matriz)", "d": "Devolve a inversa de uma matriz armazenada numa matriz", "ad": "é uma matriz numérica com o mesmo número de linhas e colunas, seja um intervalo de células seja uma constante de matriz"}, "MMULT": {"a": "(matriz1; matriz2)", "d": "Devolve a matriz produto de duas matrizes, uma matriz com o mesmo número de linhas que matriz1 e de colunas que matriz2", "ad": "é a primeira matriz de números a multiplicar e tem de ter o mesmo número de colunas que Matriz2 tem de linhas"}, "MOD": {"a": "(núm; divisor)", "d": "Devolve o resto depois de um número ser dividido por Divisor", "ad": "é o número para o qual deseja determinar o resto depois de executar a divisão!é o número pelo qual deseja dividir Núm"}, "MROUND": {"a": "(núm; múltiplo)", "d": "Devolve um número arredondado para o múltiplo desejado", "ad": "é o valor a arredondar!é o múltiplo para o qual se deseja arredondar núm"}, "MULTINOMIAL": {"a": "(núm1; [núm2]; ...)", "d": "Devolve o polinomial de um conjunto de números", "ad": "de 1 a 255 valores para os quais pretende o polinomial"}, "MUNIT": {"a": "(dimensão)", "d": "Devolve a matriz identidade para a dimensão especificada", "ad": "é um número inteiro que especifica a dimensão da matriz identidade que quer devolver"}, "ODD": {"a": "(núm)", "d": "Arredonda um número positivo para cima e um número negativo para baixo até ao número ímpar inteiro mais próximo", "ad": "é o valor a arredondar"}, "PI": {"a": "()", "d": "<PERSON><PERSON><PERSON> o valor de Pi, 3,14159265358979, com uma precisão de 13 casas decimais", "ad": ""}, "POWER": {"a": "(núm; potência)", "d": "Devolve o resultado de um número elevado a uma potência", "ad": "é o número da base, qualquer número real!é o expoente pelo qual a base do número é elevada"}, "PRODUCT": {"a": "(núm1; [núm2]; ...)", "d": "Multiplica todos os números apresentados como argumentos", "ad": "são de 1 a 255 números, valores lógicos ou representações de números em texto que pretende multiplicar"}, "QUOTIENT": {"a": "(numerador; denominador)", "d": "Devolve a parte inteira de uma divisão", "ad": "é o dividendo!é o divisor"}, "RADIANS": {"a": "(â<PERSON><PERSON>)", "d": "Converte graus em radianos", "ad": "é um ângulo em graus que deseja converter"}, "RAND": {"a": "()", "d": "Devolve um número aleatório maior ou igual a 0 e menor que 1, segundo uma distribuição uniforme (altera ao voltar a calcular)", "ad": ""}, "RANDARRAY": {"a": "([linhas]; [colunas]; [mín]; [máx]; [nú<PERSON><PERSON>_inteiro])", "d": "Devolve uma matriz de números aleatórios", "ad": "o número de linhas na matriz devolvida!o número de colunas na matriz devolvida!o número mínimo que pretende que seja devolvido! o número máximo que pretende que seja devolvido!devolver um número inteiro ou um valor decimal. VERDADEIRO se for um número inteiro, FALSO se for um número decimal"}, "RANDBETWEEN": {"a": "(inferior; superior)", "d": "Devolve um número aleatório de entre os números especificados", "ad": "é o menor inteiro a devolver por ALEATÓRIOENTRE!é o maior inteiro que ALEATÓRIOENTRE devolverá"}, "ROMAN": {"a": "(núm; [forma])", "d": "Converte um número árabe em romano, como texto", "ad": "é o número árabe que pretende converter!é o número que especifica o tipo de número romano pretendido."}, "ROUND": {"a": "(núm; núm_dígitos)", "d": "Arredonda um valor para um número de algarismos especificado", "ad": "é o número que deseja arredondar!é o número de algarismos que deseja obter no arredondamento. Os números negativos arredondam para a esquerda da vírgula decimal; zero arredonda para o número inteiro mais próximo"}, "ROUNDDOWN": {"a": "(núm; núm_dígitos)", "d": "Arredonda um número por defeito, em valor absoluto", "ad": "é qualquer número real que deseja arredondar por defeito!é o número de algarismos para o qual deseja fazer o arredondamento. Os valores negativos arredondam para a esquerda da vírgula decimal; zero ou omisso para o número inteiro mais próximo"}, "ROUNDUP": {"a": "(núm; núm_dígitos)", "d": "Arredonda um número por excesso, em valor absoluto", "ad": "é qualquer número real que deseja arredondar por excesso!é o número de algarismos que deseja para o arredondamento. Os valores negativos arredondam para a esquerda da vírgula decimal; zero ou omisso para o número inteiro mais próximo"}, "SEC": {"a": "(número)", "d": "Devolve a secante de um ângulo", "ad": "é o ângulo em radianos para o qual quer obter a secante"}, "SECH": {"a": "(número)", "d": "Devolve a secante hiperbólica de um ângulo", "ad": "é o ângulo em radianos para o qual quer obter a secante hiperbólica"}, "SERIESSUM": {"a": "(x; n; m; coeficientes)", "d": "Devolve a soma de uma série de potência baseada na fórmula", "ad": "é o valor de entrada para a série de potência!é a potência inicial para a qual se deseja elevar x!é o passo em que se acrescenta n a cada termo da série!é um conjunto de coeficientes pelo qual cada potência sucessiva de x será multiplicada"}, "SIGN": {"a": "(núm)", "d": "Devolve o sinal de um número: 1, se o número for positivo, zero se o número for zero ou -1 se o número for negativo", "ad": "é qualquer número real"}, "SIN": {"a": "(núm)", "d": "Devolve o seno de um ângulo", "ad": "é o ângulo em radianos para o qual deseja obter o seno. Graus * PI()/180 = radianos"}, "SINH": {"a": "(núm)", "d": "Devolve o seno hiperbólico de um número", "ad": "é qualquer número real"}, "SQRT": {"a": "(núm)", "d": "Devolve a raiz quadrada de um número", "ad": "é o número para o qual deseja calcular a raiz quadrada"}, "SQRTPI": {"a": "(núm)", "d": "Devolve a raiz quadrada de um número a multiplicar por Pi", "ad": "é o número pelo qual p é multiplicado"}, "SUBTOTAL": {"a": "(núm_função; ref1; ...)", "d": "Devolve um subtotal numa lista ou base de dados", "ad": "é o número de 1 a 11 que especifica a funcionalidade de resumo para o subtotal.!são 1 a 254 intervalos ou referências para os quais pretende obter o subtotal"}, "SUM": {"a": "(núm1; [núm2]; ...)", "d": "Adiciona todos os números de um intervalo de células", "ad": "são de 1 a 255 números a somar. Valores lógicos e texto são ignorados nas células, incluindo os introduzidos como argumentos"}, "SUMIF": {"a": "(intervalo; critérios; [intervalo_soma])", "d": "Adiciona as células especificadas por uma determinada condição ou critério", "ad": "é o intervalo de células que deseja avaliar!é a condição ou critério na forma de um número, expressão ou texto, que define quais as células a serem adicionadas!são as células a somar. Se omissas, utilizam-se as células do intervalo"}, "SUMIFS": {"a": "(intervalo_soma; intervalo_critérios; critérios; ...)", "d": "Adiciona as células especificadas por um determinado conjunto de condições ou critérios", "ad": "são as células a somar.!é o intervalo de células que pretende avaliar para a condição específica!é a condição ou critério, sob a forma de um número, expressão ou texto, que define quais as células que serão adicionadas"}, "SUMPRODUCT": {"a": "(matriz1; [matriz2]; [matriz3]; ...)", "d": "Devolve a soma dos produtos dos intervalos ou matrizes correspondentes", "ad": "são de 2 a 255 matrizes, cujos componentes deseja multiplicar e em seguida adicionar. As matrizes têm de ter todas as mesmas dimensões"}, "SUMSQ": {"a": "(núm1; [núm2]; ...)", "d": "Devolve a soma dos quadrados dos argumentos. Os argumentos podem ser números, nomes, matrizes ou referências a células que contenham números", "ad": "são de 1 a 255 números, matrizes, nomes ou referências cuja soma dos quadrados deseja obter"}, "SUMX2MY2": {"a": "(matriz_x; matriz_y)", "d": "<PERSON><PERSON> as diferen<PERSON>s dos quadrados de números correspondentes em dois intervalos ou matrizes", "ad": "é o primeiro intervalo ou matriz de números e pode ser um número ou nome, matriz ou referência que contém números!é o segundo intervalo ou matriz de números e pode ser um número ou nome, matriz ou referência que contém números"}, "SUMX2PY2": {"a": "(matriz_x; matriz_y)", "d": "Devolve o total das somas dos quadrados de números correspondentes em dois intervalos ou matrizes", "ad": "é o primeiro intervalo ou matriz de números e pode ser um número ou um nome, matriz ou referência que contém números!é o segundo intervalo ou matriz de números e pode ser um número ou nome, matriz ou referência que contenha números"}, "SUMXMY2": {"a": "(matriz_x; matriz_y)", "d": "Soma os quadrados das diferenças de valores correspondentes em dois intervalos ou matrizes", "ad": "é o primeiro intervalo ou matriz de valores e pode ser um número ou nome, matriz ou referência que contenha números!é o segundo intervalo ou matriz de valores e pode ser um número ou nome, matriz ou referência que contenha números"}, "TAN": {"a": "(núm)", "d": "Devolve a tangente de um ângulo", "ad": "é o ângulo em radianos cuja tangente deseja obter. Graus * PI()/180 = radianos"}, "TANH": {"a": "(núm)", "d": "Devolve a tangente hiperbólica de um número", "ad": "é qualquer número real"}, "TRUNC": {"a": "(núm; [núm_dígitos])", "d": "Trunca um número, tornando-o um número inteiro, ao remover a sua parte decimal ou fraccional", "ad": "é o número que deseja truncar!é um número que especifica a precisão da truncagem; se omisso, é 0 (zero)"}, "ADDRESS": {"a": "(núm_linha; núm_coluna; [núm_abs]; [a1]; [texto_folha])", "d": "Cria uma referência de célula como texto, a partir de números de linhas e colunas especificados", "ad": "é o número de linha a ser utilizado na referência da célula: núm_linha = 1 para a linha 1!é o número da coluna a ser utilizado na referência da célula. Por exemplo, núm_coluna = 4 para a coluna D!especifica o tipo de referência: absoluta = 1; linha absoluta/coluna relativa = 2; linha relativa/coluna absoluta = 3; relativa = 4!é um valor lógico que especifica o estilo de referência: estilo A1 = 1 ou VERDADEIRO; estilo L1C1 = 0 ou FALSO!é o texto que especifica o nome da folha de cálculo a ser utilizada como referência externa"}, "CHOOSE": {"a": "(núm_índice; valor1; [valor2]; ...)", "d": "Seleciona um valor ou ação a executar a partir de uma lista de valores, baseada num número de índice", "ad": "especifica qual o argumento de valor selecionado; Núm_índice deve estar compreendido entre 1 e 254, ser uma fórmula ou uma referência a um número compreendido entre 1 e 254!são de 1 a 254 números, referências de células, nomes definidos, fó<PERSON><PERSON><PERSON>, funções ou argumentos de texto a partir dos quais SELECIONAR faz a seleção"}, "COLUMN": {"a": "([referência])", "d": "Devolve o número da coluna de uma referência", "ad": "é a célula ou intervalo de células contíguas para o qual deseja obter o número da coluna. Se omisso, é utilizada a célula que contém a função COL"}, "COLUMNS": {"a": "(matriz)", "d": "Devolve o número de colunas de uma matriz ou referência", "ad": "é uma matriz, fórmula matricial ou referência a um intervalo de células de que deseja saber o número de colunas"}, "FORMULATEXT": {"a": "(referência)", "d": "Devolve uma fórmula como uma cadeia", "ad": "é uma referência a uma fórmula"}, "HLOOKUP": {"a": "(valor_proc; matriz_tabela; núm_índice_linha; [procurar_intervalo])", "d": "Procura um valor na linha superior de uma tabela ou matriz de valores e devolve o valor na mesma coluna de uma linha especificada", "ad": "é o valor a ser encontrado na primeira linha da tabela; pode ser um valor, uma referência ou uma cadeia de texto!é uma tabela de texto, números ou valores lógicos onde os dados devem ser procurados. Matriz_tabela pode ser uma referência a um intervalo ou um nome de intervalo!é o número da linha na matriz_tabela de onde o valor correspondente deve ser retirado. A primeira linha de valores na tabela é a linha 1!é um valor lógico: localizar a correspondência mais próxima na linha superior (ordenada por ordem ascendente) = VERDADEIRO ou omisso; localizar uma correspondência exata = FALSO"}, "HYPERLINK": {"a": "(local_vínculo; [nome_abrev])", "d": "Cria um atalho ou salto que abre um documento armazenado no disco rígido, num servidor da rede ou na Internet", "ad": "é o texto que dá o caminho e o nome do ficheiro a ser aberto, uma localização de unidade de disco rígido, endereço de UNC ou caminho de URL!é texto ou um número que aparece na célula. Se omisso, a célula mostra o texto de Local_vínculo"}, "INDEX": {"a": "(matriz; núm_linha; [núm_coluna]!referência; núm_linha; [núm_coluna]; [núm_area])", "d": "Devolve um valor ou a referência da célula na interseção de uma linha e coluna em particular, num determinado intervalo", "ad": "é um intervalo de células ou uma constante de matriz!seleciona a linha na matriz ou referência de onde será devolvido um valor. Se for omitido, é necessário Núm_coluna!seleciona a coluna na matriz ou referência de onde será devolvido um valor. Se for omisso, é necessário Núm_linha!é uma referência para um ou mais intervalos de células!seleciona a linha na matriz ou referência de onde será devolvido um valor. Se for omisso, é necessário Núm_coluna!seleciona a coluna na matriz ou referência de onde será devolvido um valor. Se for omisso, é necessário Núm_linha!seleciona um intervalo em referência a partir do qual deve ser devolvido um valor. A primeira área selecionada ou introduzida é a Área 1, a segunda a Área 2 e assim por diante"}, "INDIRECT": {"a": "(ref_texto; [a1])", "d": "Devolve uma referência especificada por um valor de texto", "ad": "é uma referência a uma célula que contém uma referência A1- ou do estilo L1C1, um nome definido como uma referência ou uma referência a uma célula como cadeia de texto!é um valor lógico que especifica o tipo de referência em ref_texto: estilo-L1C1 = FALSO; estilo-A1 = VERDADEIRO ou omisso"}, "LOOKUP": {"a": "(valor_proc; vetor_proc; [vetor_result]!valor_proc; matriz)", "d": "Devolve um valor, quer de um intervalo de uma linha ou de uma coluna, quer de uma matriz. Fornecido para compatibilidade com conversões anteriores", "ad": "é um valor que PROC procura em Vector _proc e pode ser um número, texto, um valor lógico ou um nome ou referência a um valor!é um intervalo que contém apenas uma linha ou uma coluna de texto, números ou valores lógicos, colocados por ordem ascendente!é um intervalo que contém apenas uma linha ou coluna, do mesmo tamanho de Vector_proc!é um valor que PROC procura numa matriz e pode ser um número, texto, um valor lógico ou um nome ou referência a um valor!é um intervalo de células que contém texto, números ou valores lógicos que deseja comparar com o Valor_proc"}, "MATCH": {"a": "(valor_proc; matriz_proc; [tipo_corresp])", "d": "Devolve a posição relativa de um item numa matriz que corresponde a um valor especificado por uma ordem especificada", "ad": "é o valor utilizado para encontrar o valor que pretende numa matriz, um número, texto ou valor lógico ou uma referência a um destes!é um intervalo contíguo de células contendo valores possíveis de procura, uma matriz de valores ou uma referência a uma matriz!é um número 1, 0 ou -1 que indica o valor a devolver."}, "OFFSET": {"a": "(referência; linhas; colunas; [altura]; [largura])", "d": "Devolve, a partir de uma célula ou intervalo de células, uma referência a um intervalo", "ad": "é a referência em que deseja basear o deslocamento, uma referência a uma célula ou intervalo de células adjacentes!é o número de linhas, para cima ou para baixo, a que deseja que a célula superior esquerda do resultado se refira!é o número de colunas, à esquerda ou à direita, a que deseja que a célula superior esquerda do resultado se refira!é a altura, em número de linhas, que deseja que a referência fornecida apresente; se omissa, é a mesma altura de Referência!é a largura, em número de colunas, que deseja como resultado; se omissa, é a mesma largura de Referência"}, "ROW": {"a": "([referência])", "d": "Devolve o número da linha de uma referência", "ad": "é a célula ou um único intervalo de células cujo número da linha deseja obter; se omisso, devolve a célula contendo a função LIN"}, "ROWS": {"a": "(matriz)", "d": "Devolve o número de linhas numa referência ou matriz", "ad": "é uma matriz, uma fórmula matricial ou uma referência a um intervalo de células cujo número de linhas deseja obter"}, "TRANSPOSE": {"a": "(matriz)", "d": "Converte um intervalo vertical de células para um intervalo horizontal, ou vice-versa", "ad": "é um intervalo de células na folha de cálculo ou uma matriz de valores que deseja transpor"}, "UNIQUE": {"a": "(matriz; [por_col]; [exatamente_uma_vez])", "d": "Devolve os valores exclusivos de um intervalo ou matriz.", "ad": "o intervalo ou matriz da qual devolver linhas ou colunas exclusivas!é um valor lógico: comparar as linhas entre si e devolver linhas exclusivas = FALSO ou omitido; comparar colunas entre si e devolver as colunas exclusivas = VERDADEIRO!é um valor lógico: devolver linhas ou colunas que ocorrem exatamente uma vez da matriz = VERDADEIRO; devolver todas as linhas ou colunas diferentes da matriz = FALSO ou omitido"}, "VLOOKUP": {"a": "(valor_proc; matriz_tabela; núm_índice_coluna; [procurar_intervalo])", "d": "Procura um valor na coluna mais à esquerda de uma tabela e devolve um valor na mesma linha de uma dada coluna. Por predefinição, a tabela tem de ser ordenada por ordem ascendente", "ad": "é o valor a ser encontrado na primeira coluna da tabela, e pode ser um valor, uma referência ou uma cadeia de texto!é uma tabela de texto, números ou valores lógicos, de onde os dados são obtidos. Matriz_tabela pode ser uma referência a um intervalo ou um nome de intervalo!é o número da coluna em matriz_tabela a partir do qual o valor correspondente deve ser devolvido. A primeira coluna de valores da tabela é a coluna 1!é um valor lógico: localizar o correspondente mais próximo na primeira coluna (ordenada por ordem ascendente) = VERDADEIRO ou omisso; localizar um correspondente exato = FALSO"}, "XLOOKUP": {"a": "(valor_pesquisa; matriz_pesquisa; matriz_devolver; [se_não_for_encontrado]; [modo_corresp]; [modo_pesquisa])", "d": "Procura uma correspondência num intervalo ou matriz e devolve o item correspondente de um segundo intervalo ou matriz. Por predefinição, é utilizada uma correspondência exata", "ad": "é o valor a procurar!é a matriz ou intervalo a procurar!é a matriz ou intervalo a devolver!devolvido se não forem encontradas correspondências!especifique como deve ser feita a correspondência do valor em valor_pesquisa com os valores em matriz_pesquisa!especifique o modo de pesquisa a utilizar. Por predefinição, será utilizada uma pesquisa do primeiro ao último item"}, "CELL": {"a": "(tipo_info; [referência])", "d": "Devolve informações sobre a formatação, localização ou conteúdo de uma célula", "ad": "valor de texto que especifica o tipo de informação da célula que pretende devolver!a célula da qual pretende obter informações"}, "ERROR.TYPE": {"a": "(val_erro)", "d": "Devolve um número que corresponde a um valor de erro.", "ad": "é o valor de erro cujo número de identificação pretende e pode ser um valor de erro real ou uma referência a uma célula que contém um valor de erro"}, "ISBLANK": {"a": "(valor)", "d": "Devolve VERDADEIRO ou FALSO se valor se referir a uma célula vazia", "ad": "é a célula ou um nome que se refere à célula que deseja testar"}, "ISERR": {"a": "(valor)", "d": "Verifica se um valor é um erro diferente de #N/D (valor não disponível) e devolve VERDADEIRO ou FALSO", "ad": "é o valor que pretende testar. <PERSON>or pode referir-se a uma célula, uma fórmula ou um nome que se refere a uma célula, fórmula ou valor"}, "ISERROR": {"a": "(valor)", "d": "Verifica se um valor é um erro e devolve VERDADEIRO ou FALSO", "ad": "é o valor que pretende testar. <PERSON>or pode referir-se a uma célula, uma fórmula ou um nome que se refere a uma célula, fórmula ou valor"}, "ISEVEN": {"a": "(núm)", "d": "Devolve VERDADEIRO se o número for par", "ad": "é o valor a testar"}, "ISFORMULA": {"a": "(referência)", "d": "Verifica se uma referência se relaciona com uma célula que contém uma fórmula e devolve VERDADEIRO ou FALSO", "ad": "é uma referência à célula que deseja testar. A referência pode ser uma referência de célula, uma fórmula ou um nome que faça referência a uma célula"}, "ISLOGICAL": {"a": "(valor)", "d": "Verifica se Valor é um valor lógico (VERDADEIRO ou FALSO) e devolve VERDADEIRO ou FALSO", "ad": "é o valor que deseja testar. O valor pode referir-se a uma célula, uma fórmula ou um nome referente a uma célula, fórmula ou valor"}, "ISNA": {"a": "(valor)", "d": "Devolve VERDADEIRO ou FALSO se for um valor de erro #N/D (valor não disponível)", "ad": "é o valor que deseja testar. Valor pode referir-se a uma célula, uma fórmula ou um nome que se refere a uma célula, fórmula ou valor"}, "ISNONTEXT": {"a": "(valor)", "d": "Devolve VERDADEIRO ou FALSO se um valor não for texto (células em branco não são texto)", "ad": "é o valor que deseja testar: uma célula; uma fórmula; um nome com referência a uma célula, fórmula ou valor"}, "ISNUMBER": {"a": "(valor)", "d": "Devolve VERDADEIRO ou FALSO se valor for um número", "ad": "é o valor que deseja testar. Valor pode referir-se a uma célula, uma fórmula ou um nome que se refere a uma célula, fórmula ou valor"}, "ISODD": {"a": "(núm)", "d": "Devolve VERDADEIRO se o número for ímpar", "ad": "é o valor a testar"}, "ISREF": {"a": "(valor)", "d": "Devolve VERDADEIRO ou FALSO se um valor for uma referência", "ad": "é o valor que deseja testar. Valor pode referir-se a uma célula, uma fórmula ou um nome que se refere a uma célula, fórmula ou valor"}, "ISTEXT": {"a": "(valor)", "d": "Devolve VERDADEIRO ou FALSO se valor for texto", "ad": "é o valor que deseja testar. O valor pode referir-se a uma célula, uma fórmula ou um nome que se refere a uma célula, fórmula ou valor"}, "N": {"a": "(valor)", "d": "Converte um valor não numérico para um número, datas para números de série, VERDADEIRO para 1, o resto para 0 (zero)", "ad": "é o valor que deseja converter"}, "NA": {"a": "()", "d": "Devolve o valor de erro #N/D (valor não disponível)", "ad": ""}, "SHEET": {"a": "([valor])", "d": "Devolve o número de folha da folha referenciada", "ad": "é o nome de uma folha ou referência para a qual quer o número de folha. Se omitido, será devolvido o número da folha que contém a função"}, "SHEETS": {"a": "([referência])", "d": "Devolve o número de folhas numa referência", "ad": "é uma referência para a qual quer saber o número de folhas que contém. Se omitido, é devolvido o número de folhas no livro que tenham a função"}, "TYPE": {"a": "(valor)", "d": "Devolve um número inteiro que representa o tipo de dados de um valor: número = 1; texto = 2; valor lógico = 4; valor de erro = 16; matriz = 64; dados compostos = 128", "ad": "pode ser qualquer valor"}, "AND": {"a": "(valor_lógico1; [valor_lógico2]; ...)", "d": "Devolve VERDADEIRO, se todos os argumentos forem VERDADEIRO;", "ad": "são de 1 a 255 condições a ser testadas, que podem ser VERDADEIRO ou FALSO e que podem ser valores lógicos, matrizes ou referências"}, "FALSE": {"a": "()", "d": "Devolve o valor lógico FALSO", "ad": ""}, "IF": {"a": "(teste_lógico; [valor_se_verdadeiro]; [valor_se_falso])", "d": "Devolve um valor se a condição especificada equivaler a VERDADEIRO e outro valor se equivaler a FALSO", "ad": "é qualquer valor ou expressão que pode ser avaliada como VERDADEIRO ou FALSO!é o valor devolvido se Teste_lógico for VERDADEIRO. Se for omisso, devolve VERDADEIRO. É possível aninhar até sete funções SE!é o valor devolvido se Teste_lógico for FALSO. Se omisso, é devolvido FALSO"}, "IFS": {"a": "(teste_lógico; valor_se_verdadeiro; ...)", "d": "Verifica se uma ou mais condições são cumpridas e devolve um valor correspondente à primeira condição VERDADEIRA", "ad": "é qualquer valor ou expressão que pode ser avaliado como VERDADEIRO ou FALSO!é o valor devolvido se o Teste_lógico for VERDADEIRO"}, "IFERROR": {"a": "(valor; valor_se_erro)", "d": "Devolve valor_se_erro se a expressão for um erro e o valor da própria expressão não o for", "ad": "é qualquer valor, expressão ou referência!é qualquer valor, expressão ou referência"}, "IFNA": {"a": "(valor; valor_se_nd)", "d": "Devolve o valor especificado se a expressão devolver #N/D, caso contrário devolve o resultado da expressão", "ad": "é um valor, expressão ou referência!é valor, expressão ou referência"}, "NOT": {"a": "(valor_lógico)", "d": "Altera FALSO para VERDADEIRO e VERDADEIRO para FALSO", "ad": "é o valor ou expressão que pode ser avaliado como VERDADEIRO ou FALSO"}, "OR": {"a": "(valor_lógico1; [valor_lógico2]; ...)", "d": "Verifica se algum dos argumentos é VERDADEIRO e devolve VERDADEIRO ou FALSO. Devolve FALSO se todos os argumentos forem FALSO", "ad": "são de 1 a 255 condições a serem testadas, que podem ser VERDADEIRO ou FALSO"}, "SWITCH": {"a": "(expressão; valor1; resultado1; [predefinição_ou_valor2]; [resultado2]; ...)", "d": "Avalia uma expressão em relação a uma lista de valores e devolve o resultado do primeiro valor correspondente. Se não existir correspondência, é devolvido um valor predefinido opcional", "ad": "é uma expressão para avaliação!é um valor a comparar com a expressão!é um resultado a ser devolvido caso o valor corresponda à expressão"}, "TRUE": {"a": "()", "d": "Devolve o valor lógico VERDADEIRO", "ad": ""}, "XOR": {"a": "(lógica1; [lógica2]; ...)", "d": "Devolve um \"Ou Exclusivo\" lógico de todos os argumentos", "ad": "são condições de 1 a 254 que quer testar que podem ser VERDADEIRO ou FALSO e que podem ser matrizes, referências ou valores lógicos"}, "TEXTBEFORE": {"a": "(texto, delimitador, [núm_instância], [corresp_mod], [corresp_final], [se_não_for_encontrado])", "d": "Devolve texto que está antes dos carateres delimitadores.", "ad": "O texto que pretende procurar para o delimitador.!O caráter ou cadeia a utilizar como delimitador.!A ocorrência pretendida do delimitador. A predefinição é 1. Um número negativo procura a partir do fim.!Pesquisa no texto uma correspondência do delimitador. Por predefinição, uma correspondência de maiúsculas e minúsculas foi concluída.!Indica se o delimitador está em relação ao fim do texto. Por predefinição, não são correspondentes.! Devolvido se não for encontrada qualquer correspondência. Por predefinição, #N/D é devolvido."}, "TEXTAFTER": {"a": "(texto, delimitador, [núm_instância], [corresp_mod], [corresp_final], [se_não_for_encontrado])", "d": "Devolve texto que está depois dos carateres delimitadores.", "ad": "O texto que pretende procurar para o delimitador.!O caráter ou cadeia a utilizar como delimitador.!A ocorrência pretendida do delimitador. A predefinição é 1. Um número negativo procura a partir do fim.!Pesquisa no texto uma correspondência do delimitador. Por predefinição, uma correspondência de maiúsculas e minúsculas foi concluída.!Indica se o delimitador está em relação ao fim do texto. Por predefinição, não são correspondentes.!Devolvido se não for encontrada qualquer correspondência. Por predefinição, #N/D é devolvido."}, "TEXTSPLIT": {"a": "(texto, delimitador_de_coluna, [delimitador_de_linha], [corresp], [modo], [preencher_com])", "d": "Divide o texto em linhas ou colunas usando delimitadores.", "ad": "O texto a dividir!<PERSON><PERSON><PERSON> ou cadeia para dividir em colunas.!<PERSON><PERSON><PERSON> ou cadeia para dividir em linhas.!Indica se as células vazias devem ser ignoradas. A predefinição é FALSO.!Pesquisa no texto uma correspondência do delimitador. Por predefinição, uma correspondência de maiúsculas e minúsculas foi concluída.!O valor a utilizar para preenchimento. Por predefinição, #N/A é utilizado."}, "WRAPROWS": {"a": "(vetor, contagem_de_moldagens, [preencher_com])", "d": "Molda um vetor de linha ou coluna após um número especificado de valores.", "ad": "O vetor ou referência para moldar.!O número máximo de valores por linha.!O valor com o qual preencher. A predefinição é #N/D."}, "VSTACK": {"a": "(matriz1, [matriz2], ...)", "d": "Empilha verticalmente várias matrizes numa única matriz.", "ad": "Uma matriz ou referência a empilhar."}, "HSTACK": {"a": "(matriz1, [matriz2], ...)", "d": "Empilha horizontalmente várias matrizes numa única matriz.", "ad": "Uma matriz ou referência a empilhar."}, "CHOOSEROWS": {"a": "(matriz, núm_linha1, [núm_linha2], ...)", "d": "Devolve linhas a partir de uma matriz ou referência.", "ad": "A matriz ou referência que contém as linhas a devolver.!O número da linha a devolver"}, "CHOOSECOLS": {"a": "(matriz, núm_coluna1, [núm_coluna2], ...)", "d": "Devolve colunas a partir de uma matriz ou referência.", "ad": "A matriz ou referência que contém as colunas a devolver.!O número da coluna a devolver"}, "TOCOL": {"a": "(matriz, [ignorar], [analisar_por_coluna])", "d": "Devolve a matriz como uma coluna.", "ad": "A matriz ou referência a devolver como coluna.!Indica se certos tipos de valores devem ser ignorados. Por predefinição, não são ignorados valores.!Analisar a matriz por coluna. Por predefinição, a matriz é analisada por linha."}, "TOROW": {"a": "(array, [ignore], [scan_by_column])", "d": "Devolve a matriz como uma linha.", "ad": "A matriz ou referência a devolver como linha.!Indica se certos tipos de valores devem ser ignorados. Por predefinição, não são ignorados valores.!Analisar a matriz por coluna. Por predefinição, a matriz é analisada por linha."}, "WRAPCOLS": {"a": "(vetor, contagem_de_moldagens, [preencher_com])", "d": "Molda um vetor de linha ou coluna após um número especificado de valores.", "ad": "O vetor ou referência para moldar.!O número máximo de valores por coluna.!O valor com o qual preencher. A predefinição é #N/D."}, "TAKE": {"a": "(matriz, lin<PERSON>, [colunas])", "d": "Devol<PERSON> linhas ou colunas a partir do início ou fim da matriz.", "ad": "A matriz a partir da qual obter linhas ou colunas.!O número de linhas a obter. Um valor negativo obtém a partir do fim da matriz.!O número de colunas a obter. Um valor negativo obtém a partir do fim da matriz."}, "DROP": {"a": "(matriz, lin<PERSON>, [colunas])", "d": "Remove linhas ou colunas a partir do início ou fim da matriz.", "ad": "A matriz a partir da qual remover linhas ou colunas.!O número de linhas a remover. Um valor negativo remove a partir do fim da matriz.!O número de colunas a remover. Um valor negativo remove a partir do fim da matriz."}, "SEQUENCE": {"a": "(linhas, [colunas], [in<PERSON>cio], [passo])", "d": "Devolve uma sequência de números", "ad": "o número de linhas a devolver!o número de colunas a devolver!o primeiro número da sequência!a quantidade a incrementar em cada valor subsequente na sequência"}, "EXPAND": {"a": "(matriz, lin<PERSON>, [colunas], [preencher_com])", "d": "Expande uma matriz para as dimensões especificadas", "ad": "A matriz a expandir!O número de linhas na matriz expandida. Se estiver em falta, as linhas não serão expandidas.!O número de colunas na matriz expandida. Se estiver em falta, as colunas não serão expandidas.!O valor com o qual preencher. A predefinição é #N/D."}, "XMATCH": {"a": "(lookup_value, lookup_array, [match_mode], [search_mode])", "d": "Devolve a posição relativa de um item numa matriz. Por predefinição, é necessária uma correspondência exata", "ad": "é o valor a procurar!é a matriz ou intervalo a procurar!especifique como fazer corresponder o valor_procurado com os valores em matriz_procurada!especifique o modo de pesquisa a utilizar. Por predefinição, será utilizada uma pesquisa do primeiro ao último"}, "FILTER": {"a": "(matriz, incluir, [se_vazia])", "d": "Filtrar um intervalo ou matriz", "ad": "o intervalo ou matriz a filtrar!uma matriz de valores booleanos em que VERDADEIRO representa uma linha ou coluna a reter!devolvido se nenhum item for retido"}, "ARRAYTOTEXT": {"a": "(matriz, [formato])", "d": "Devolve uma representação de texto de uma matriz", "ad": "a matriz a apresentar como texto!o formato do texto"}, "SORT": {"a": "(matriz, [ordenar_índice], [ordenar_ordem], [por_col])", "d": "Ordena um intervalo ou matriz", "ad": "o intervalo ou matriz a ordenar!um número que indica a linha ou coluna pela qual ordenar!um número que indica a sequência de ordenação pretendida; 1 para ordem ascendente (predefinição), -1 para ordem descendente!um valor lógico que indica a direção de ordenação pretendida: FALSO para ordenar por linha (predefinição), VERDADEIRO para ordenar por coluna"}, "SORTBY": {"a": "(matriz, por_matriz, [sequência_ordena<PERSON>], ...)", "d": "Ordena um intervalo ou matriz com base nos valores num intervalo ou matriz correspondente", "ad": "o intervalo ou matriz a ordenar!o intervalo ou matriz para efetuar a ordenação!um número que indica a sequência de ordenação pretendida; 1 para ordem ascendente (predefinição), -1 para ordem descendente"}, "GETPIVOTDATA": {"a": "(campo_dados; tabela_dinâmica; [campo]; [item]; ...)", "d": "Extrai os dados guardados numa Tabela Dinâmica.", "ad": "é o nome do campo de dados a partir do qual os dados serão extraídos!é uma referência a uma célula ou intervalo de células na Tabela Dinâmica que contém os dados que pretende obter!campo para referir!item do campo para referir"}, "IMPORTRANGE": {"a": "(url_folha_de_c<PERSON>lo, string_intervalo)", "d": "Importa um intervalo de células a partir de uma folha de cálculo especificada.", "ad": "o URL da folha de cálculo a partir da qual os dados serão importados!o intervalo a importar"}}