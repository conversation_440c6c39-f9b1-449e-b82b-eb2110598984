{"DATE": "PÄIVÄYS", "DATEDIF": "DATEDIF", "DATEVALUE": "PÄIVÄYSARVO", "DAY": "PÄIVÄ", "DAYS": "PÄIVÄT", "DAYS360": "PÄIVÄT360", "EDATE": "PÄIVÄ.KUUKAUSI", "EOMONTH": "KUUKAUSI.LOPPU", "HOUR": "TUNNIT", "ISOWEEKNUM": "VIIKKO.ISO.NRO", "MINUTE": "MINUUTIT", "MONTH": "KUUKAUSI", "NETWORKDAYS": "TYÖPÄIVÄT", "NETWORKDAYS.INTL": "TYÖPÄIVÄT.KANSVÄL", "NOW": "NYT", "SECOND": "SEKUNNIT", "TIME": "AIKA", "TIMEVALUE": "AIKA_ARVO", "TODAY": "TÄMÄ.PÄIVÄ", "WEEKDAY": "VIIKONPÄIVÄ", "WEEKNUM": "VIIKKO.NRO", "WORKDAY": "TYÖPÄIVÄ", "WORKDAY.INTL": "TYÖPÄIVÄ.KANSVÄL", "YEAR": "VUOSI", "YEARFRAC": "VUOSI.OSA", "BESSELI": "BESSELI", "BESSELJ": "BESSELJ", "BESSELK": "BESSELK", "BESSELY": "BESSELY", "BIN2DEC": "BINDES", "BIN2HEX": "BINHEKSA", "BIN2OCT": "BINOKT", "BITAND": "BITTI.JA", "BITLSHIFT": "BITTI.SIIRTO.V", "BITOR": "BITTI.TAI", "BITRSHIFT": "BITTI.SIIRTO.O", "BITXOR": "BITTI.EHDOTON.TAI", "COMPLEX": "KOMPLEKSI", "CONVERT": "MUUNNA", "DEC2BIN": "DESBIN", "DEC2HEX": "DESHEKSA", "DEC2OCT": "DESOKT", "DELTA": "SAMA.ARVO", "ERF": "VIRHEFUNKTIO", "ERF.PRECISE": "VIRHEFUNKTIO.TARKKA", "ERFC": "VIRHEFUNKTIO.KOMPLEMENTTI", "ERFC.PRECISE": "VIRHEFUNKTIO.KOMPLEMENTTI.TARKKA", "GESTEP": "RAJA", "HEX2BIN": "HEKSABIN", "HEX2DEC": "HEKSADES", "HEX2OCT": "HEKSAOKT", "IMABS": "KOMPLEKSI.ABS", "IMAGINARY": "KOMPLEKSI.IMAG", "IMARGUMENT": "KOMPLEKSI.ARG", "IMCONJUGATE": "KOMPLEKSI.KONJ", "IMCOS": "KOMPLEKSI.COS", "IMCOSH": "IMCOSH", "IMCOT": "KOMPLEKSI.COT", "IMCSC": "KOMPLEKSI.KOSEK", "IMCSCH": "KOMPLEKSI.KOSEKH", "IMDIV": "KOMPLEKSI.OSAM", "IMEXP": "KOMPLEKSI.EKSP", "IMLN": "KOMPLEKSI.LN", "IMLOG10": "KOMPLEKSI.LOG10", "IMLOG2": "KOMPLEKSI.LOG2", "IMPOWER": "KOMPLEKSI.POT", "IMPRODUCT": "KOMPLEKSI.TULO", "IMREAL": "KOMPLEKSI.REAALI", "IMSEC": "KOMPLEKSI.SEK", "IMSECH": "KOMPLEKSI.SEKH", "IMSIN": "KOMPLEKSI.SIN", "IMSINH": "KOMPLEKSI.SINH", "IMSQRT": "KOMPLEKSI.NELIÖJ", "IMSUB": "KOMPLEKSI.EROTUS", "IMSUM": "KOMPLEKSI.SUM", "IMTAN": "KOMPLEKSI.TAN", "OCT2BIN": "OKTBIN", "OCT2DEC": "OKTDES", "OCT2HEX": "OKTHEKSA", "DAVERAGE": "TKESKIARVO", "DCOUNT": "TLASKE", "DCOUNTA": "TLASKEA", "DGET": "TNOUDA", "DMAX": "TMAKS", "DMIN": "TMIN", "DPRODUCT": "TTULO", "DSTDEV": "TKESKIHAJONTA", "DSTDEVP": "TKESKIHAJONTAP", "DSUM": "TSUMMA", "DVAR": "TVARIANSSI", "DVARP": "TVARIANSSIP", "CHAR": "MERKKI", "CLEAN": "SIIVOA", "CODE": "KOODI", "CONCATENATE": "KETJUTA", "CONCAT": "YHDISTÄ", "DOLLAR": "VALUUTTA", "EXACT": "VERTAA", "FIND": "ETSI", "FINDB": "FINDB", "FIXED": "KIINTEÄ", "LEFT": "VASEN", "LEFTB": "LEFTB", "LEN": "PITUUS", "LENB": "LENB", "LOWER": "PIENET", "MID": "POIMI.TEKSTI", "MIDB": "MIDB", "NUMBERVALUE": "NROARVO", "PROPER": "ERISNIMI", "REPLACE": "KORVAA", "REPLACEB": "REPLACEB", "REPT": "TOISTA", "RIGHT": "OIKEA", "RIGHTB": "RIGHTB", "SEARCH": "KÄY.LÄPI", "SEARCHB": "SEARCHB", "SUBSTITUTE": "VAIHDA", "T": "T", "T.TEST": "T.TESTI", "TEXT": "TEKSTI", "TEXTJOIN": "TEKSTI.YHDISTÄ", "TREND": "SUUNTAUS", "TRIM": "POISTA.VÄLIT", "TRIMMEAN": "KESKIARVO.TASATTU", "TTEST": "TTESTI", "UNICHAR": "UNICODEMERKKI", "UNICODE": "UNICODE", "UPPER": "ISOT", "VALUE": "ARVO", "AVEDEV": "KESKIPOIKKEAMA", "AVERAGE": "KESKIARVO", "AVERAGEA": "KESKIARVOA", "AVERAGEIF": "KESKIARVO.JOS", "AVERAGEIFS": "KESKIARVO.JOS.JOUKKO", "BETADIST": "BEETAJAKAUMA", "BETAINV": "BEETAJAKAUMA.KÄÄNT", "BETA.DIST": "BEETA.JAKAUMA", "BETA.INV": "BEETA.KÄÄNT", "BINOMDIST": "BINOMIJAKAUMA", "BINOM.DIST": "BINOMI.JAKAUMA", "BINOM.DIST.RANGE": "BINOMI.JAKAUMA.ALUE", "BINOM.INV": "BINOMIJAKAUMA.KÄÄNT", "CHIDIST": "CHIJAKAUMA", "CHIINV": "CHIJAKAUMA.KÄÄNT", "CHITEST": "CHITESTI", "CHISQ.DIST": "CHINELIÖ.JAKAUMA", "CHISQ.DIST.RT": "CHINELIÖ.JAKAUMA.OH", "CHISQ.INV": "CHINELIÖ.KÄÄNT", "CHISQ.INV.RT": "CHINELIÖ.KÄÄNT.OH", "CHISQ.TEST": "CHINELIÖ.TESTI", "CONFIDENCE": "LUOTTAMUSVÄLI", "CONFIDENCE.NORM": "LUOTTAMUSVÄLI.NORM", "CONFIDENCE.T": "LUOTTAMUSVÄLI.T", "CORREL": "KORRELAATIO", "COUNT": "LASKE", "COUNTA": "LASKE.A", "COUNTBLANK": "LASKE.TYHJÄT", "COUNTIF": "LASKE.JOS", "COUNTIFS": "LASKE.JOS.JOUKKO", "COVAR": "KOVARIANSSI", "COVARIANCE.P": "KOVARIANSSI.P", "COVARIANCE.S": "KOVARIANSSI.S", "CRITBINOM": "BINOMIJAKAUMA.KRIT", "DEVSQ": "OIKAISTU.NELIÖSUMMA", "EXPON.DIST": "EKSPONENTIAALI.JAKAUMA", "EXPONDIST": "EKSPONENTIAALIJAKAUMA", "FDIST": "FJAKAUMA", "FINV": "FJAKAUMA.KÄÄNT", "FTEST": "FTESTI", "F.DIST": "F.JAKAUMA", "F.DIST.RT": "F.JAKAUMA.OH", "F.INV": "F.K<PERSON>", "F.INV.RT": "F.KÄÄNT.OH", "F.TEST": "F.TESTI", "FISHER": "FISHER", "FISHERINV": "FISHER.KÄÄNT", "FORECAST": "ENNUSTE", "FORECAST.ETS": "ENNUSTE.ETS", "FORECAST.ETS.CONFINT": "ENNUSTE.ETS.CONFINT", "FORECAST.ETS.SEASONALITY": "ENNUSTE.ETS.KAUSIVAIHTELU", "FORECAST.ETS.STAT": "ENNUSTE.ETS.STAT", "FORECAST.LINEAR": "ENNUSTE.LINEAARINEN", "FREQUENCY": "TAAJUUS", "GAMMA": "GAMMA", "GAMMADIST": "GAMMAJAKAUMA", "GAMMA.DIST": "GAMMA.JAKAUMA", "GAMMAINV": "GAMMAJAKAUMA.KÄÄNT", "GAMMA.INV": "GAMMA.JAKAUMA.KÄÄNT", "GAMMALN": "GAMMALN", "GAMMALN.PRECISE": "GAMMALN.TARKKA", "GAUSS": "GAUSS", "GEOMEAN": "KESKIARVO.GEOM", "GROWTH": "KASVU", "HARMEAN": "KESKIARVO.HARM", "HYPGEOM.DIST": "HYPERGEOM_JAKAUMA", "HYPGEOMDIST": "HYPERGEOM.JAKAUMA", "INTERCEPT": "LEIKKAUSPISTE", "KURT": "KURT", "LARGE": "SUURI", "LINEST": "LINREGR", "LOGEST": "LOGREGR", "LOGINV": "LOGNORM.JAKAUMA.KÄÄNT", "LOGNORM.DIST": "LOGNORM_JAKAUMA", "LOGNORM.INV": "LOGNORM.KÄÄNT", "LOGNORMDIST": "LOGNORM.JAKAUMA", "MAX": "MAKS", "MAXA": "MAKSA", "MAXIFS": "MAKS.JOS.JOUKKO", "MEDIAN": "MEDIAANI", "MIN": "MIN", "MINA": "MINA", "MINIFS": "MIN.JOS.JOUKKO", "MODE": "MOODI", "MODE.MULT": "MOODI.USEA", "MODE.SNGL": "MOODI.YKSI", "NEGBINOM.DIST": "BINOMI.JAKAUMA.NEG", "NEGBINOMDIST": "BINOMIJAKAUMA.NEG", "NORM.DIST": "NORMAALI.JAKAUMA", "NORM.INV": "NORMAALI.JAKAUMA.KÄÄNT", "NORM.S.DIST": "NORM_JAKAUMA.NORMIT", "NORM.S.INV": "NORM_JAKAUMA.KÄÄNT", "NORMDIST": "NORM.JAKAUMA", "NORMINV": "NORM.JAKAUMA.KÄÄNT", "NORMSDIST": "NORM.JAKAUMA.NORMIT", "NORMSINV": "NORM.JAKAUMA.NORMIT.KÄÄNT", "PEARSON": "PEARSON", "PERCENTILE": "PROSENTTIPISTE", "PERCENTILE.EXC": "PROSENTTIPISTE.ULK", "PERCENTILE.INC": "PROSENTTIPISTE.SIS", "PERCENTRANK": "PROSENTTIJÄRJESTYS", "PERCENTRANK.EXC": "PROSENTTIJÄRJESTYS.ULK", "PERCENTRANK.INC": "PROSENTTIJÄRJESTYS.SIS", "PERMUT": "PERMUTAATIO", "PERMUTATIONA": "PERMUTAATIOA", "PHI": "FII", "POISSON": "POISSON", "POISSON.DIST": "POISSON.JAKAUMA", "PROB": "TODENNÄKÖISYYS", "QUARTILE": "NELJÄNNES", "QUARTILE.INC": "NELJÄNNES.SIS", "QUARTILE.EXC": "NELJÄNNES.ULK", "RANK.AVG": "ARVON.MUKAAN.KESKIARVO", "RANK.EQ": "ARVON.MUKAAN.TASAN", "RANK": "ARVON.MUKAAN", "RSQ": "PEARSON.NELIÖ", "SKEW": "JAKAUMAN.VINOUS", "SKEW.P": "JAKAUMAN.VINOUS.POP", "SLOPE": "KULMAKERROIN", "SMALL": "PIENI", "STANDARDIZE": "NORMITA", "STDEV": "KESKIHAJONTA", "STDEV.P": "KESKIHAJONTA.P", "STDEV.S": "KESKIHAJONTA.S", "STDEVA": "KESKIHAJONTAA", "STDEVP": "KESKIHAJONTAP", "STDEVPA": "KESKIHAJONTAPA", "STEYX": "KESKIVIRHE", "TDIST": "TJAKAUMA", "TINV": "TJAKAUMA.KÄÄNT", "T.DIST": "T.JAKAUMA", "T.DIST.2T": "T.JAKAUMA.2S", "T.DIST.RT": "T.JAKAUMA.OH", "T.INV": "T.<PERSON>", "T.INV.2T": "T.KÄÄNT.2S", "VAR": "VAR", "VAR.P": "VAR.P", "VAR.S": "VAR.S", "VARA": "VARA", "VARP": "VARP", "VARPA": "VARPA", "WEIBULL": "WEIBULL", "WEIBULL.DIST": "WEIBULL.JAKAUMA", "Z.TEST": "Z.TESTI", "ZTEST": "ZTESTI", "ACCRINT": "KERTYNYT.KORKO", "ACCRINTM": "KERTYNYT.KORKO.LOPUSSA", "AMORDEGRC": "AMORDEGRC", "AMORLINC": "AMORLINC", "COUPDAYBS": "KORKOPÄIVÄT.ALUSTA", "COUPDAYS": "KORKOPÄIVÄT", "COUPDAYSNC": "KORKOPÄIVÄT.SEURAAVA", "COUPNCD": "KORKOPÄIVÄ.SEURAAVA", "COUPNUM": "KORKOPÄIVÄ.JAKSOT", "COUPPCD": "KORKOPÄIVÄ.EDELLINEN", "CUMIPMT": "MAKSETTU.KORKO", "CUMPRINC": "MAKSETTU.LYHENNYS", "DB": "DB", "DDB": "DDB", "DISC": "DISKONTTOKORKO", "DOLLARDE": "VALUUTTA.DES", "DOLLARFR": "VALUUTTA.MURTO", "DURATION": "KESTO", "EFFECT": "KORKO.EFEKT", "FV": "TULEVA.ARVO", "FVSCHEDULE": "TULEVA.ARVO.ERIKORKO", "INTRATE": "KORKO.ARVOPAPERI", "IPMT": "IPMT", "IRR": "SISÄINEN.KORKO", "ISPMT": "ISPMT", "MDURATION": "KESTO.MUUNN", "MIRR": "MSISÄINEN", "NOMINAL": "KORKO.VUOSI", "NPER": "NJAKSO", "NPV": "NNA", "ODDFPRICE": "PARITON.ENS.NIMELLISARVO", "ODDFYIELD": "PARITON.ENS.TUOTTO", "ODDLPRICE": "PARITON.VIIM.NIMELLISARVO", "ODDLYIELD": "PARITON.VIIM.TUOTTO", "PDURATION": "KESTO.JAKSO", "PMT": "MAKSU", "PPMT": "PPMT", "PRICE": "HINTA", "PRICEDISC": "HINTA.DISK", "PRICEMAT": "HINTA.LUNASTUS", "PV": "NA", "RATE": "KORKO", "RECEIVED": "SAATU.HINTA", "RRI": "TOT.ROI", "SLN": "STP", "SYD": "VUOSIPOISTO", "TBILLEQ": "OBLIG.TUOTTOPROS", "TBILLPRICE": "OBLIG.HINTA", "TBILLYIELD": "OBLIG.TUOTTO", "VDB": "VDB", "XIRR": "SISÄINEN.KORKO.JAKSOTON", "XNPV": "NNA.JAKSOTON", "YIELD": "TUOTTO", "YIELDDISC": "TUOTTO.DISK", "YIELDMAT": "TUOTTO.ERÄP", "ABS": "ITSEISARVO", "ACOS": "ACOS", "ACOSH": "ACOSH", "ACOT": "ACOT", "ACOTH": "ACOTH", "AGGREGATE": "KOOSTE", "ARABIC": "ARABIA", "ASC": "ASC", "ASIN": "ASIN", "ASINH": "ASINH", "ATAN": "ATAN", "ATAN2": "ATAN2", "ATANH": "ATANH", "BASE": "PERUS", "CEILING": "PYÖRISTÄ.KERR.YLÖS", "CEILING.MATH": "PYÖRISTÄ.KERR.YLÖS.MATEMAATTINEN", "CEILING.PRECISE": "CEILING.PRESIZE", "COMBIN": "KOMBINAATIO", "COMBINA": "KOMBINAATIOA", "COS": "COS", "COSH": "COSH", "COT": "COT", "COTH": "COTH", "CSC": "KOSEK", "CSCH": "KOSEKH", "DECIMAL": "DESIMAALI", "DEGREES": "ASTEET", "ECMA.CEILING": "ECMA.CEILING", "EVEN": "PARILLINEN", "EXP": "EKSPONENTTI", "FACT": "KERTOMA", "FACTDOUBLE": "KERTOMA.OSA", "FLOOR": "PYÖRISTÄ.KERR.ALAS", "FLOOR.PRECISE": "FLOOR.PRECISE", "FLOOR.MATH": "PYÖRISTÄ.KERR.ALAS.MATEMAATTINEN", "GCD": "SUURIN.YHT.TEKIJÄ", "INT": "KOKONAISLUKU", "ISO.CEILING": "ISO.CEILING", "LCM": "PIENIN.YHT.JAETTAVA", "LN": "LUONNLOG", "LOG": "LOG", "LOG10": "LOG10", "MDETERM": "MDETERM", "MINVERSE": "MKÄÄNTEINEN", "MMULT": "MKERRO", "MOD": "JAKOJ", "MROUND": "PYÖRISTÄ.KERR", "MULTINOMIAL": "MULTINOMI", "MUNIT": "YKSIKKÖM", "ODD": "PARITON", "PI": "PII", "POWER": "POTENSSI", "PRODUCT": "TULO", "QUOTIENT": "OSAMÄÄRÄ", "RADIANS": "RADIAANIT", "RAND": "SATUNNAISLUKU", "RANDARRAY": "SATUNN.MATRIISI", "RANDBETWEEN": "SATUNNAISLUKU.VÄLILTÄ", "ROMAN": "ROMAN", "ROUND": "PYÖRISTÄ", "ROUNDDOWN": "PYÖRISTÄ.DES.ALAS", "ROUNDUP": "PYÖRISTÄ.DES.YLÖS", "SEC": "SEK", "SECH": "SEKH", "SERIESSUM": "SARJA.SUMMA", "SIGN": "ETUMERKKI", "SIN": "SIN", "SINH": "SINH", "SQRT": "NELIÖJUURI", "SQRTPI": "NELIÖJUURI.PII", "SUBTOTAL": "VÄLISUMMA", "SUM": "SUMMA", "SUMIF": "SUMMA.JOS", "SUMIFS": "SUMMA.JOS.JOUKKO", "SUMPRODUCT": "TULOJEN.SUMMA", "SUMSQ": "NELIÖSUMMA", "SUMX2MY2": "NELIÖSUMMIEN.EROTUS", "SUMX2PY2": "NELIÖSUMMIEN.SUMMA", "SUMXMY2": "EROTUSTEN.NELIÖSUMMA", "TAN": "TAN", "TANH": "TANH", "TRUNC": "KATKAISE", "ADDRESS": "OSOITE", "CHOOSE": "VALITSE.INDEKSI", "COLUMN": "SARAKE", "COLUMNS": "SARAKKEET", "FORMULATEXT": "KAAVA.TEKSTI", "HLOOKUP": "VHAKU", "HYPERLINK": "HYPERLINKKI", "INDEX": "INDEKSI", "INDIRECT": "EPÄSUORA", "LOOKUP": "HAKU", "MATCH": "VASTINE", "OFFSET": "SIIRTYMÄ", "ROW": "RIVI", "ROWS": "RIVIT", "TRANSPOSE": "TRANSPONOI", "UNIQUE": "AINUTKERTAISET.ARVOT", "VLOOKUP": "PHAKU", "XLOOKUP": "XHAKU", "CELL": "CELL", "ERROR.TYPE": "VIRHEEN.LAJI", "ISBLANK": "ONTYHJÄ", "ISERR": "ONVIRH", "ISERROR": "ONVIRHE", "ISEVEN": "ONPARILLINEN", "ISFORMULA": "ONKAAVA", "ISLOGICAL": "ONTOTUUS", "ISNA": "ONPUUTTUU", "ISNONTEXT": "ONEI_TEKSTI", "ISNUMBER": "ONLUKU", "ISODD": "ONPARITON", "ISREF": "ONVIITT", "ISTEXT": "ONTEKSTI", "N": "N", "NA": "PUUTTUU", "SHEET": "TAULUKKO", "SHEETS": "TAULUKOT", "TYPE": "TYYPPI", "AND": "JA", "FALSE": "EPÄTOSI", "IF": "JOS", "IFS": "JOS.JOUKKO", "IFERROR": "JOSVIRHE", "IFNA": "JOSPUUTTUU", "NOT": "EI", "OR": "TAI", "SWITCH": "MUUTA", "TRUE": "TOSI", "XOR": "EHDOTON.TAI", "TEXTBEFORE": "TEKSTI.ENNEN", "TEXTAFTER": "TEKSTI.JÄLKEEN", "TEXTSPLIT": "TEKSTIJAKO", "WRAPROWS": "RIVITÄRIV", "VSTACK": "VPINO", "HSTACK": "HPINO", "CHOOSEROWS": "VALITSERIVIT", "CHOOSECOLS": "VALITSESARAKKEET", "TOCOL": "SARAKKEESEEN", "TOROW": "RIVIIN", "WRAPCOLS": "RIVITÄSAR", "TAKE": "OTA", "DROP": "HYLKÄÄ", "SEQUENCE": "JONO", "EXPAND": "LAAJENNA", "XMATCH": "XVASTINE", "FILTER": "SUODATA", "ARRAYTOTEXT": "MATRIISI.TEKSTIKSI", "SORT": "LAJITTELE", "SORTBY": "LAJITTELE.ARVOJEN.PERUSTEELLA", "GETPIVOTDATA": "NOUDA.PIVOT.TIEDOT", "IMPORTRANGE": "IMPORTRANGE", "LocalFormulaOperands": {"StructureTables": {"h": "Headers", "d": "Data", "a": "All", "tr": "This row", "t": "Totals"}, "CONST_TRUE_FALSE": {"t": "TRUE", "f": "FALSE"}, "CONST_ERROR": {"nil": "#NULL!", "div": "#DIV/0!", "value": "#VALUE!", "ref": "#REF!", "name": "#NAME\\?", "num": "#NUM!", "na": "#N/A", "getdata": "#GETTING_DATA", "uf": "#UNSUPPORTED_FUNCTION!", "calc": "#LASKE!"}, "CELL_FUNCTION_INFO_TYPE": {"address": "osoite", "col": "sarake", "color": "<PERSON><PERSON><PERSON>", "contents": "sisältö", "filename": "<PERSON><PERSON><PERSON><PERSON>", "format": "muoto", "parentheses": "sulkeet", "prefix": "etuliite", "protect": "su<PERSON><PERSON>", "row": "rivi", "type": "tyy<PERSON>i", "width": "leveys"}}}